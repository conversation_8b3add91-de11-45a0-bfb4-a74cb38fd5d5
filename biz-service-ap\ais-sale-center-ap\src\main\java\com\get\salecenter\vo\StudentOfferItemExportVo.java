package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 学习计划汇总导出类
 *
 * <AUTHOR>
 * @date 2021/11/25 15:41
 */
@Data
public class StudentOfferItemExportVo {

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 申请方案编号
     */
    @ApiModelProperty(value = "申请方案编号")
    private String studentOfferNum;

    /**
     * 申请方案项目编号
     */
    @ApiModelProperty(value = "学习计划")
    private String num;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String statusName;

    /**
     * 申请步骤名
     */
    @ApiModelProperty(value = "申请步骤状态")
    private String stepName;

    /**
     * 学生姓名
     */
    @ApiModelProperty(value = "学生名称(中/英)")
    private String studentName;

    /**
     * 学生生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "学生生日")
    private Date birthday;

    @ApiModelProperty(value = "国家")
    private String fkAreaCountryName;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "申请学校")
    private String institutionFullName;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "申请课程")
    private String courseFullName;

    /**
     * 开学时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开学时间")
    private Date openingTime;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;

    @ApiModelProperty("创建人")
    private String gmtCreateUser;

}
