package com.get.institutioncenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class InstitutionCourseAppInfoDataProcessDto {

    @ApiModelProperty("tableId")
    private Long id;

    @ApiModelProperty("公开对象名称")
    private String publicLevelName;

    @ApiModelProperty("学校封面url")
    private String coversUrl;

    @ApiModelProperty("目标对象信息")
    private List<CourseAppInfoResultMappingVo> courseAppInfoResultMappingVos;

    @ApiModelProperty(value = "公开对象")
    private String publicLevel;

    @ApiModelProperty(value = "生效时间")
    private String effectiveDate;


    @ApiModelProperty("权重")
    private Integer weight;


    private Date gmtPriorityTime;

    @ApiModelProperty(value = "学校名称")
    private String fkInstitutionName;

    @Data
    public static class CourseAppInfoResultMappingVo{

        @ApiModelProperty(value = "目标类型")
        private String typeKey;

        private Integer weight;

        @ApiModelProperty(value = "对象信息")
        private List<CourseAppInfoResultDto> courseAppInfoResultVos;
    }
}
