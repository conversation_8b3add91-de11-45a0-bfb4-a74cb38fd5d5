package com.get.rocketmqcenter.enums;

import lombok.Getter;

/**
 * rockMQ topic 枚举
 */
@Getter
public enum TopicEnum {


    /**
     * 发送自定义邮件
     */
    MAIL_CUSTOM_QUEUE_TOPIC("mail_custom_queue_topic", "mail_custom_queue_topic_consumer_group"),
    /**
     * 发送系统邮件
     */
    MAIL_SYSTEM_QUEUE_TOPIC("mail_system_queue_topic", "mail_system_queue_topic_consumer_group"),

    /**
     * 发送邮件任务
     */
    MAIL_TASK_QUEUE_TOPIC("mail_task_queue_topic", "mail_system_queue_topic_group")
    ;


    private String topic;

    private String consumerGroup;

    TopicEnum(String topic, String consumerGroup) {
        this.topic = topic;
        this.consumerGroup = consumerGroup;
    }

}
