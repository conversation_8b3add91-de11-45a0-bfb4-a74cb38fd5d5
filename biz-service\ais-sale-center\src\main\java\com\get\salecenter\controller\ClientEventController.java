package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.ClientEventVo;
import com.get.salecenter.service.IClientEventService;
import com.get.salecenter.dto.ClientEventAddDto;
import com.get.salecenter.dto.ClientEventRemindDto;
import com.get.salecenter.dto.ClientEventDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * author:Neil
 * Time: 14:06
 * Date: 2022/8/17
 * Description:客户事件管理
 */
@Api(tags = "客户事件管理")
@RestController
@RequestMapping("sale/clientEvent")
public class ClientEventController {

    @Resource
    private IClientEventService clientEventService;


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentEventVo>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/客户事件管理/列表数据")
    @PostMapping("datas")
    public ResponseBo<ClientEventVo> datas(@RequestBody SearchBean<ClientEventDto> page) {
        List<ClientEventVo> datas = clientEventService.getClientEvents(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [ClientEventDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/咨询客户事件管理/新增信息")
    @PostMapping("add")
    public ResponseBo add(@RequestBody ClientEventAddDto ClientEventAddDto) {
        return SaveResponseBo.ok(clientEventService.addClientEvent(ClientEventAddDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/咨询事件管理/删除接口")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        clientEventService.delete(id);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "添加回访提醒")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/咨询事件管理/添加回访提醒")
    @PostMapping("addRemindTaskEven")
    public ResponseBo addRemindTaskEven(@RequestBody ClientEventRemindDto clientEventRemindDto){
        clientEventService.addRemindTaskEven(clientEventRemindDto);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 客户事件类型下拉
     * @Param [providerId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "客户事件类型下拉", notes = "")
    @GetMapping("getClientEventTypeSelect")
    public ResponseBo<BaseSelectEntity> getClientEventTypeSelect() {
        return new ListResponseBo<>(clientEventService.getClientEventTypeSelect());
    }

}
