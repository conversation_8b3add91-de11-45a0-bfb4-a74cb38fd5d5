<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.platformconfigcenter.dao.appmso.AgentRecommendMapper">
  <resultMap id="BaseResultMap" type="com.get.platformconfigcenter.entity.AgentRecommend">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_agent_id" jdbcType="BIGINT" property="fkAgentId" />
    <result column="target_type" jdbcType="VARCHAR" property="targetType" />
    <result column="target_id" jdbcType="BIGINT" property="targetId" />
    <result column="view_order" jdbcType="INTEGER" property="viewOrder" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <sql id="Base_Column_List">
    id, fk_agent_id, target_type, target_id, view_order, gmt_create, gmt_create_user, 
    gmt_modified, gmt_modified_user
  </sql>
 <!-- <insert id="insert" parameterType="com.get.platformconfigcenter.entity.AgentRecommend">
    insert into m_agent_recommend (id, fk_agent_id, target_type, 
      target_id, target_value,view_order, gmt_create,
      gmt_create_user, gmt_modified, gmt_modified_user
      )
    values (#{id,jdbcType=BIGINT}, #{fkAgentId,jdbcType=BIGINT}, #{targetType,jdbcType=VARCHAR},
      #{targetId,jdbcType=BIGINT},#{targetValue,jdbcType=VARCHAR} ,#{viewOrder,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.get.platformconfigcenter.entity.AgentRecommend">
    insert into m_agent_recommend
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkAgentId != null">
        fk_agent_id,
      </if>
      <if test="targetType != null">
        target_type,
      </if>
      <if test="targetId != null">
        target_id,
      </if>
      <if test="targetValue != null">
        target_value,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkAgentId != null">
        #{fkAgentId,jdbcType=BIGINT},
      </if>
      <if test="targetType != null">
        #{targetType,jdbcType=VARCHAR},
      </if>
      <if test="targetId != null">
        #{targetId,jdbcType=BIGINT},
      </if>
      <if test="targetValue != null">
        #{targetValue,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.get.platformconfigcenter.entity.AgentRecommend">
    update m_agent_recommend
    <set>
      <if test="fkAgentId != null">
        fk_agent_id = #{fkAgentId,jdbcType=BIGINT},
      </if>
      <if test="targetType != null">
        target_type = #{targetType,jdbcType=VARCHAR},
      </if>
      <if test="targetId != null">
        target_id = #{targetId,jdbcType=BIGINT},
      </if>
      <if test="targetValue != null">
        target_Value = #{targetValue,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        view_order = #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.platformconfigcenter.entity.AgentRecommend">
    update m_agent_recommend
    set fk_agent_id = #{fkAgentId,jdbcType=BIGINT},
      target_type = #{targetType,jdbcType=VARCHAR},
      target_id = #{targetId,jdbcType=BIGINT},
      target_value = #{targetValue,jdbcType=VARCHAR},
      view_order = #{viewOrder,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select
      IFNULL(max(view_order)+1,0) view_order
    from
      m_agent_recommend
  </select>-->
</mapper>