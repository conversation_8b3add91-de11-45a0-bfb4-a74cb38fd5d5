package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.EventTargetAreaCountry;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/12/7 17:31
 * @verison: 1.0
 * @description:
 */
@Mapper
public interface EventTargetAreaCountryMapper extends BaseMapper<EventTargetAreaCountry> {
    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(EventTargetAreaCountry record);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :根据全部活动id 查找对应所以的活动对象国家id
     * @Param [eventIds]
     * <AUTHOR>
     */
    List<Long> getCountryIdsByEventIds(@Param("eventIds") List<Long> eventIds);
}