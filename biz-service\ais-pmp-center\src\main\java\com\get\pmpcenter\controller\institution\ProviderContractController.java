package com.get.pmpcenter.controller.institution;

import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.secure.utils.SecureUtil;
import com.get.pmpcenter.dto.common.IdListDto;
import com.get.pmpcenter.dto.institution.*;
import com.get.pmpcenter.entity.InstitutionProviderContract;
import com.get.pmpcenter.entity.InstitutionProviderContractApproval;
import com.get.pmpcenter.service.ContractPartyService;
import com.get.pmpcenter.service.InstitutionProviderContractApprovalService;
import com.get.pmpcenter.service.InstitutionProviderContractService;
import com.get.pmpcenter.vo.common.CountryVo;
import com.get.pmpcenter.vo.common.StaffVo;
import com.get.pmpcenter.vo.institution.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


/**
 * 学校提供商合同管理
 */
@Slf4j
@Api(tags = "学校提供商合同管理")
@RestController
@RequestMapping("/providerContract")
public class ProviderContractController {

    @Autowired
    private ContractPartyService contractPartyService;
    @Autowired
    private InstitutionProviderContractService providerContractService;
    @Autowired
    private InstitutionProviderContractApprovalService contractApprovalService;

    @ApiOperation(value = "学校供应商列表")
    @GetMapping("/providerList")
    public ResponseBo<List<ProviderVo>> providerList() {
        return new ResponseBo<>(contractPartyService.getProviderList());
    }

    @ApiOperation(value = "合同签订方列表", notes = "根据学校供应商id获取合同签订方列表(不传获取全部)")
    @GetMapping("/contractPartyList")
    public ResponseBo<List<ContractPartyVo>> contractPartyList(Long institutionProviderId) {
        return new ResponseBo<>(contractPartyService.getContractPartyList(institutionProviderId));
    }

    @ApiOperation(value = "合同类型列表")
    @GetMapping("/getContractTypeList")
    public ResponseBo<List<ContractTypeVo>> getContractTypeList() {
        return new ResponseBo<>(providerContractService.getContractTypeList());
    }

    @ApiOperation(value = "新增/编辑学校提供商合同")
    @PostMapping("/saveProviderContract")
    public ResponseBo<String> saveProviderContract(@RequestBody @Valid SaveProviderContractDto providerContractDto) {
        providerContractService.saveProviderContract(providerContractDto, Boolean.TRUE);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "生效/不生效学校提供商合同")
    @PostMapping("/enableProviderContract")
    public ResponseBo<String> enableProviderContract(@RequestBody @Valid EnableProviderContractDto enableProviderContractDto) {
        providerContractService.enableProviderContract(enableProviderContractDto);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "学校提供商合同列表")
    @PostMapping("/providerContractPage")
    public ResponseBo<ProviderContractVo> providerContractPage(@RequestBody SearchBean<ProviderContractPageDto> page) {
        List<ProviderContractVo> list = providerContractService.providerContractPage(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(list, p);
    }

    @ApiOperation(value = "合同详情", notes = "根据合同id获取合同详细信息")
    @GetMapping("/contractDetail/{id}")
    public ResponseBo<ProviderContractDetailVo> contractDetail(@PathVariable("id") Long id) {
        return new ResponseBo<>(providerContractService.providerContractDetail(id));
    }

    @ApiOperation(value = "合同基本信息", notes = "根据合同id获取合同基本信息")
    @GetMapping("/getProviderContractBaseInfo/{id}")
    public ResponseBo<InstitutionProviderContract> getProviderContractBaseInfo(@PathVariable("id") Long id) {
        return new ResponseBo<>(providerContractService.getProviderContractBaseInfo(id));
    }

    @ApiOperation(value = "续签合同详情", notes = "根据合同id获取合同详细信息(合同+佣金方案+佣金明细)")
    @GetMapping("/renewalContractDetail/{id}")
    public ResponseBo<ContractDetailVo> renewalContractDetail(@PathVariable("id") Long id) {
        return new ResponseBo<>(providerContractService.renewalContractDetail(id));
    }

    @ApiOperation(value = "续签合同")
    @PostMapping("/renewalContract")
    public ResponseBo<String> renewalContract(@RequestBody @Valid ContractDetailVo detail) {
        providerContractService.renewalContract(detail);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "删除合同")
    @PostMapping("/delContract")
    public ResponseBo<String> delContract(@RequestBody @Valid IdListDto idDto) {
        providerContractService.delContract(idDto);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "锁定/解锁合同")
    @PostMapping("/lockContract")
    public ResponseBo<String> lockContract(@RequestBody @Valid LockContractDto lockContractDto) {
        providerContractService.lockContract(lockContractDto);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "提交合同审批")
    @PostMapping("/submitApproval")
    public ResponseBo<String> submitApproval(@RequestBody @Valid SubmitApprovalDto approvalDto) {
        contractApprovalService.submitApproval(approvalDto);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SUBMIT_SUCCESS", "提交成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "审批合同")
    @PostMapping("/approvalContract")
    public ResponseBo<String> approvalContract(@RequestBody @Valid ApprovalContractDto contractDto) {
        contractApprovalService.approvalContract(contractDto);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_APPROVE_SUCCESS", "审批成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "合同审批人列表", notes = "合同审批人列表")
    @GetMapping("/getStaffList")
    public ResponseBo<List<StaffVo>> getStaffList() {
        return new ResponseBo<>(contractApprovalService.getStaffList());
    }

    @ApiOperation(value = "合同审批记录列表", notes = "根据合同id获取合同审批记录列表")
    @GetMapping("/getApprovalList")
    public ResponseBo<List<InstitutionProviderContractApproval>> getApprovalList(Long contractId) {
        return new ResponseBo<>(contractApprovalService.getApprovalList(contractId));
    }

    @ApiOperation(value = "国家列表", notes = "国家列表-根据权限过滤")
    @GetMapping("/getCountryList")
    public ResponseBo<List<CountryVo>> getCountryList() {
        return new ResponseBo<>(providerContractService.getCountryList());
    }
}
