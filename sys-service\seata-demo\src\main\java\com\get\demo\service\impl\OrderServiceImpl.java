package com.get.demo.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.demo.entity.Order;
import com.get.demo.mapper.OrderMapper;
import com.get.demo.service.IOrderService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-07
 */
@Service
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements IOrderService {

    @Override
    public Boolean updateByDemo() {
        Order order = this.getById(1);
        this.update(Wrappers.<Order>lambdaUpdate().set(Order::getStatus, order.getStatus() == 0 ? 1 : 0).eq(Order::getId, 1));
        return true;
    }
}
