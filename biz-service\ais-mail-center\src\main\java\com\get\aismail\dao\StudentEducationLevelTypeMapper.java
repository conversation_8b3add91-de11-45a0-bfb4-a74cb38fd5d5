package com.get.aismail.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.get.aismail.entity.StudentEducationLevelType;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@DS("saledb-doris")
@Mapper
public interface StudentEducationLevelTypeMapper extends GetMapper<StudentEducationLevelType> {
    /**
     * 获取学历下拉
     * @return
     */
    List<BaseSelectEntity> getEducationDropDown();
}
