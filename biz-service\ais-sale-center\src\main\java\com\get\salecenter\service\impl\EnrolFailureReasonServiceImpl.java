package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.EnrolFailureReasonMapper;
import com.get.salecenter.vo.EnrolFailureReasonVo;
import com.get.salecenter.entity.EnrolFailureReason;
import com.get.salecenter.service.EnrolFailureReasonService;
import com.get.salecenter.dto.EnrolFailureReasonDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2021/6/16
 * @TIME: 15:33
 * @Description:
 **/
@Service
public class EnrolFailureReasonServiceImpl implements EnrolFailureReasonService {
    @Resource
    private EnrolFailureReasonMapper enrolFailureReasonMapper;
    @Resource
    private UtilService utilService;

    @Override
    public List<EnrolFailureReasonVo> getAllEnrolFailureReasonDto() {
//        List<EnrolFailureReason> enrolFailureReasons = enrolFailureReasonMapper.selectAll();
        List<EnrolFailureReason> enrolFailureReasons = enrolFailureReasonMapper.selectList(Wrappers.<EnrolFailureReason>lambdaQuery());
        return enrolFailureReasons.stream().map(enrolFailureReason ->
                BeanCopyUtils.objClone(enrolFailureReason, EnrolFailureReasonVo::new)).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<EnrolFailureReasonVo> getEnrolFailureReasonDtos(EnrolFailureReasonDto enrolFailureReasonDto, Page page) {
        LambdaQueryWrapper<EnrolFailureReason> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(enrolFailureReasonDto)) {
            if (GeneralTool.isNotEmpty(enrolFailureReasonDto.getKeyWord())) {
                lambdaQueryWrapper.like(EnrolFailureReason::getReasonName, enrolFailureReasonDto.getKeyWord());
            }
        }
        lambdaQueryWrapper.orderByDesc(EnrolFailureReason::getViewOrder);
        IPage<EnrolFailureReason> pages = enrolFailureReasonMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<EnrolFailureReason> enrolFailureReasons = pages.getRecords();
        page.setAll((int) pages.getTotal());
        return enrolFailureReasons.stream().map(enrolFailureReason -> BeanCopyUtils.objClone(enrolFailureReason, EnrolFailureReasonVo::new)).collect(Collectors.toList());
    }

    @Override
    public void addEnrolFailureReason(List<EnrolFailureReasonDto> enrolFailureReasonDtoList) {
        if (GeneralTool.isEmpty(enrolFailureReasonDtoList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (EnrolFailureReasonDto enrolFailureReasonDto : enrolFailureReasonDtoList) {
            if (GeneralTool.isEmpty(enrolFailureReasonDto.getId())) {
                EnrolFailureReason enrolFailureReason = BeanCopyUtils.objClone(enrolFailureReasonDto, EnrolFailureReason::new);
                utilService.updateUserInfoToEntity(enrolFailureReason);
                enrolFailureReason.setViewOrder(enrolFailureReasonMapper.getMaxViewOrder());
                enrolFailureReasonMapper.insert(enrolFailureReason);
            } else {
                EnrolFailureReason enrolFailureReason = BeanCopyUtils.objClone(enrolFailureReasonDto, EnrolFailureReason::new);
                utilService.updateUserInfoToEntity(enrolFailureReason);
                enrolFailureReasonMapper.updateById(enrolFailureReason);
            }

        }
    }

    @Override
    public EnrolFailureReasonVo updateEnrolFailureReason(EnrolFailureReasonDto enrolFailureReasonDto) {
        EnrolFailureReason enrolFailureReason = BeanCopyUtils.objClone(enrolFailureReasonDto, EnrolFailureReason::new);
        utilService.updateUserInfoToEntity(enrolFailureReason);
        enrolFailureReasonMapper.updateById(enrolFailureReason);
        return findEnrolFailureReasonById(enrolFailureReason.getId());
    }

    @Override
    public void deleteEnrolFailureReason(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        enrolFailureReasonMapper.deleteById(id);
    }

    @Override
    public EnrolFailureReasonVo findEnrolFailureReasonById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        EnrolFailureReason enrolFailureReason = enrolFailureReasonMapper.selectById(id);
        return BeanCopyUtils.objClone(enrolFailureReason, EnrolFailureReasonVo::new);
    }

    @Override
    public Map<Long, EnrolFailureReasonVo> findEnrolFailureReasonByIds(Set<Long> ids) {
        if (GeneralTool.isEmpty(ids)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<EnrolFailureReason> enrolFailureReason = enrolFailureReasonMapper.selectBatchIds(ids);
        Map<Long, EnrolFailureReasonVo> result = new HashMap<>(enrolFailureReason.size());
        for (EnrolFailureReason failureReason : enrolFailureReason) {
            EnrolFailureReasonVo reasonDto = BeanCopyUtils.objClone(failureReason, EnrolFailureReasonVo::new);
            result.put(failureReason.getId(),reasonDto);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<EnrolFailureReasonDto> enrolFailureReasonDtoList) {
        if (GeneralTool.isEmpty(enrolFailureReasonDtoList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        EnrolFailureReason enrolFailureReason1 = BeanCopyUtils.objClone(enrolFailureReasonDtoList.get(0), EnrolFailureReason::new);
        EnrolFailureReason enrolFailureReason2 = BeanCopyUtils.objClone(enrolFailureReasonDtoList.get(1), EnrolFailureReason::new);

        Integer viewOrder1 = enrolFailureReason1.getViewOrder();
        enrolFailureReason1.setViewOrder(enrolFailureReason2.getViewOrder());
        enrolFailureReason2.setViewOrder(viewOrder1);
        utilService.updateUserInfoToEntity(enrolFailureReason1);
        utilService.updateUserInfoToEntity(enrolFailureReason2);

        enrolFailureReasonMapper.updateById(enrolFailureReason1);
        enrolFailureReasonMapper.updateById(enrolFailureReason2);
    }
}
