package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: Neil
 * @description: 查询学生业务代理绑定DTO
 * @date: 2022/5/9 16:04
 * @return
 */
@Data
public class StudentAgentBindingVo {

    @ApiModelProperty(value = "学生id")
    private Long studentId;
    @ApiModelProperty(value = "学生公司id")
    private Long fkCompanyId;
    @ApiModelProperty(value = "学生代理绑定信息")
    private List<AgentsBindingVo> studentAgentsBinding;
    @ApiModelProperty(value = "学生申请方案绑定信息")
    private List<AgentsBindingVo> studentOfferBinding;
    @ApiModelProperty(value = "学生保险绑定信息")
    private List<AgentsBindingVo> studentInsuranceBinding;
    @ApiModelProperty(value = "学生住宿绑定信息")
    private List<AgentsBindingVo> studentAccommodationBinding;

}
