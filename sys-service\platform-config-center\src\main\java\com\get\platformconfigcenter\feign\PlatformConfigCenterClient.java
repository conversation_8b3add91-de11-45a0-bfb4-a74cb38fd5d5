package com.get.platformconfigcenter.feign;

import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.platformconfigcenter.vo.*;
import com.get.platformconfigcenter.entity.UserInfo;
import com.get.platformconfigcenter.service.*;
import com.get.platformconfigcenter.dto.UserInfoDto;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;


import java.util.List;
import java.util.Map;
import java.util.Set;

@RestController
@AllArgsConstructor
@VerifyPermission(IsVerify = false)
public class PlatformConfigCenterClient implements IPlatformConfigCenterClient {

    private final  IPrivacyPolicyService privacyPolicyService;

    private final UserInfoService userInfoService;
    private final AplOrderService aplOrderService;
    private final DeleteService deleteService;
    private IIssueStudentService iIssueStudentService;


    @Override
    public Result<Map<Long, String>> getPrivacyPolicyTitlesByIds(Set<Long> ids) {
        return Result.data(privacyPolicyService.getPrivacyPolicyTitlesByIds(ids));
    }

    @Override
    public Result<Map<Long, String>> getUserNamesByUserIds(Set<Long> userIds) {
        return Result.data(userInfoService.getUserNamesByUserIds(userIds));
    }

    @Override
    public Result<Map<Long, String>> getUserNickNamesByUserIds(Set<Long> userIds) {
        return Result.data(userInfoService.getUserNickNamesByUserIds(userIds));
    }

    @Override
    public Result<Map<Long, String>> getMobileByUserIds(Set<Long> userIds) {
        return Result.data(userInfoService.getMobileByUserIds(userIds));
    }

    @Override
    public Result<Map<Long, UserInfoVo>> getUserInfoDtoByIds(Set<Long> userIds) {
        return Result.data(userInfoService.getUserInfoDtoByIds(userIds));
    }

    @Override
    public Result<List<UserInfo>> getUserInfoBySearch(UserInfoDto userInfoDto) {
        return Result.data(userInfoService.getUserInfoBySearch(userInfoDto));
    }

    @Override
    public Result<Set<Long>> getUserIdsByParam(String userName, Long fkAreaCityId, String bdName) {
        return Result.data(userInfoService.getUserIdsByParam(userName, fkAreaCityId, bdName));
    }

    @Override
    public Result<Map<Long, String>> getCityNamesByUserIds(Set<Long> userIds) {
        return Result.data(userInfoService.getCityNamesByUserIds(userIds));
    }

    @Override
    public Result<Set<Long>> getUserIdsByNameOrMobile(String userName, String phoneNumber) {
        return Result.data(userInfoService.getUserIdsByNameOrMobile(userName, phoneNumber));
    }



//    @Override
//    public Result<String> getRobotStatusById(Long orderId) {
//        return Result.data(aplOrderService.getRobotStatusById(orderId));
//    }
//
//    @Override
//    public Map<Long, String> getRobotStatusByIds(Set<Long> orderIds) {
//        return aplOrderService.getRobotStatusByIds(orderIds);
//    }


//    @Override
//    public Map<Long, AplOrderVo> getRobotStatusAndMsgByIds(Set<Long> orderIds) {
//        return aplOrderService.getRobotStatusAndMsgByIds(orderIds);
//    }

//    @Override
//    public Result<Boolean> deleteValidateCourse(Long courseId) {
//        return Result.data(deleteService.deleteValidateCourse(courseId));
//    }

//    @Override
//    public Result<List<UserInfoVo>> getUserByAgentId(Long fkAgentId, Long fkCompanyId) {
//        return Result.data(userInfoService.getUserByAgentId(fkAgentId,fkCompanyId));
//    }

//    @Override
//    public List<MediaAndAttachedVo> getStudentFiles(Long id) {
//        return null;
//    }

//    @Override
//    public Result<Map<Long, List<UserAgentVo>>> getUsersByAgentIds(Set<Long> fkAgentIds) {
//        return Result.data(userInfoService.getUsersByAgentIds(fkAgentIds));
//    }

    @Override
    public Map<Long, UserInfo> getUserByStudentIds(Set<Long> issueIds) {
        return userInfoService.getUserByStudentIds(issueIds);
    }

//    @Override
//    public Map<Long, UserInfo> getUserByofferItemIssueCourseIds(Set<Long> offerItemIssueCourseIds) {
//        return userInfoService.getUserByofferItemIssueCourseIds(offerItemIssueCourseIds);
//    }

//    @Override
//    public Result<Boolean> insertIssueAgentUser(Long fkAgentId, Long fkCompanyId, Long fkUserId) {
//        return Result.data(userInfoService.insertIssueAgentUser(fkAgentId,fkCompanyId,fkUserId));
//    }

//    @Override
//    public List<Long> getIssueStuIdsByAgentId(Long fkAgentId) {
//        return iIssueStudentService.getIssueStuIdsByAgentId(fkAgentId);
//    }

//    @Override
//    public Result<Boolean> removeIssueRelationByAgentId(Long fkAgentId) {
//        return Result.data(userInfoService.removeIssueRelationByAgentId(fkAgentId));
//    }

//    @Override
//    public Result<Boolean> updateIssueStudentInstitutionCourseWithNull(StudentInstitutionCourse studentInstitutionCourse) {
//        return Result.data(iIssueStudentService.updateIssueStudentInstitutionCourseWithNull(studentInstitutionCourse));
//    }

//    @Override
//    public Result<Boolean> updateIssueStudentInstitutionCourse(StudentInstitutionCourse studentInstitutionCourse) {
//        return Result.data(iIssueStudentService.updateIssueStudentInstitutionCourse(studentInstitutionCourse));
//    }

//    @Override
//    public StudentInstitutionCourseVo getIssueStudentInstitutionCourseById(Long fkIssueCourseId) {
//        return iIssueStudentService.getIssueStudentInstitutionCourseById(fkIssueCourseId);
//    }
//    @Override
//    public Boolean updateIssueUserContactPersonTypeKeyByPersonId(Long fkContactPersonId,String fkContactPersonTypeKey) {
//        return userInfoService.updateIssueUserContactPersonTypeKeyByPersonId(fkContactPersonId,fkContactPersonTypeKey);
//    }
}
