package com.get.resumecenter.service;


import com.get.resumecenter.vo.ResumeOtherVo;
import com.get.resumecenter.dto.ResumeOtherDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/11
 * @TIME: 14:09
 * @Description:
 **/
public interface IResumeOtherService {
    /**
     * 根据简历id 查询所有的技能
     *
     * @param resumeId
     * @return
     * @
     */
    List<ResumeOtherVo> getResumeOtherListDto(Long resumeId);

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    ResumeOtherVo getResumeOtherById(Long id);


    /**
     * 添加
     *
     * @param otherVo
     * @return
     */
    Long addResumeOther(ResumeOtherDto otherVo);

    /**
     * 修改
     *
     * @param otherVo
     * @return
     */
    ResumeOtherVo updateResumeOther(ResumeOtherDto otherVo);

    /**
     * 删除
     *
     * @param id
     */
    void deleteResumeOther(Long id);


}
