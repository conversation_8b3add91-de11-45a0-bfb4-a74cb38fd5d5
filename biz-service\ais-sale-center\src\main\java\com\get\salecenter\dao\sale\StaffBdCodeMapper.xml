<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.StaffBdCodeMapper">
  <resultMap id="BaseResultMap" type="com.get.salecenter.entity.StaffBdCode">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_company_id" jdbcType="BIGINT" property="fkCompanyId" />
    <result column="fk_staff_id" jdbcType="BIGINT" property="fkStaffId" />
    <result column="bd_code" jdbcType="VARCHAR" property="bdCode" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
    <result column="color_code" jdbcType="VARCHAR" property="colorCode" />
    <result column="fk_area_region_id" jdbcType="VARCHAR" property="fkAreaRegionId" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.StaffBdCode">
    insert into r_staff_bd_code
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkCompanyId != null">
        fk_company_id,
      </if>
      <if test="fkStaffId != null">
        fk_staff_id,
      </if>
      <if test="bdCode != null">
        bd_code,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
      <if test="colorCode != null">
        color_code,
      </if>
      <if test="fkAreaRegionId != null">
        fk_area_region_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkCompanyId != null">
        #{fkCompanyId,jdbcType=BIGINT},
      </if>
      <if test="fkStaffId != null">
        #{fkStaffId,jdbcType=BIGINT},
      </if>
      <if test="bdCode != null">
        #{bdCode,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
      <if test="colorCode != null">
        #{colorCode,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaRegionId != null">
        #{fkAreaRegionId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getStaffByBdName" resultType = "com.get.core.mybatis.base.BaseSelectEntity">
    select b.bd_code as num,concat_ws(',', b.bd_code, CONCAT(c.name,IF(c.name_en is null or c.name_en = '','',CONCAT("（",c.name_en,"）")))) as
    fullName,c.id,c.name as name
    from r_staff_bd_code b
           LEFT JOIN ais_permission_center.m_staff c ON b.fk_staff_id = c.id
    where 1=1
    <if test="companyIds !=null and companyIds.size()>0 ">
      AND b.fk_company_id in
      <foreach collection="companyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
        #{fkCompanyId}
      </foreach>
    </if>
    and b.bd_code not like "T%"
    AND (c.`name` like concat("%",#{bdName},"%") OR c.name_en like concat("%",#{bdName},"%") OR b.bd_code like
   concat("%",#{bdName},"%")) ORDER BY b.bd_code limit 20
  </select>
  <select id="getStaff" resultType = "com.get.core.mybatis.base.BaseSelectEntity">
    select b.bd_code as num,concat_ws(',', b.bd_code, CONCAT(c.name,IF(c.name_en is null or c.name_en = '','',CONCAT("（",c.name_en,"）")))) as
    fullName,c.id,c.name as name
    from r_staff_bd_code b
    LEFT JOIN ais_permission_center.m_staff c ON b.fk_staff_id = c.id
    where 1=1
    <if test="companyIds !=null and companyIds.size()>0 ">
      AND b.fk_company_id in
      <foreach collection="companyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
        #{fkCompanyId}
      </foreach>
    </if>
    and b.bd_code not like "T%"
    ORDER BY b.bd_code
  </select>


  <select id="getStaffIdsByFkAreaRegionId" resultType="java.lang.Long">
    SELECT fk_staff_id FROM r_staff_bd_code WHERE fk_area_region_id = #{fkAreaRegionId}
  </select>
    <select id="getAreaRegionSelectByConventionId" resultType="com.get.core.mybatis.base.BaseSelectEntity">
      SELECT
        c.id,
        b.bd_code AS num,
        concat_ws(
                ',',
                CONCAT(
                        c.NAME,
                        IF
                          (
                                  c.name_en IS NULL
                                  OR c.name_en = '',
                                  '',
                                  CONCAT( "（", c.name_en, "）" )))) AS fullName,
        c.NAME AS NAME
      FROM
        r_staff_bd_code b
          LEFT JOIN ais_permission_center.m_staff c ON b.fk_staff_id = c.id
          LEFT JOIN m_convention AS mc ON mc.fk_company_id = b.fk_company_id
      where c.is_on_duty = 1 AND c.is_active = 1 AND mc.id = #{conventionId}
      <if test="charFilterList != null and charFilterList.size() > 0">
        <foreach collection="charFilterList" item="charFilter">
          AND b.bd_code NOT LIKE #{charFilter}
        </foreach>
      </if>
      <if test="bdCodeFilterList != null and bdCodeFilterList.size() > 0">
        AND b.bd_code NOT IN
        <foreach collection="bdCodeFilterList" item="bdCodeFilter" open="(" separator="," close=")">
          #{bdCodeFilter}
        </foreach>
      </if>
      order by b.bd_code asc

    </select>
  <select id="getBDbyAgentIds" resultType="com.get.salecenter.vo.StaffBdCodeVo">
    SELECT ras.fk_agent_id,rsbc.* FROM r_agent_staff ras
    LEFT JOIN r_staff_bd_code rsbc ON ras.fk_staff_id = rsbc.fk_staff_id
    WHERE ras.is_active = 1
    <if test="fkAgentIds!=null and fkAgentIds.size()>0">
      AND ras.fk_agent_id IN
      <foreach collection="fkAgentIds" item="fkAgentId" open="(" separator="," close=")">
        #{fkAgentId}
      </foreach>
    </if>
  </select>

  <select id="getBdSelect" resultType="com.get.salecenter.vo.BdSelectVo">
      SELECT
        b.fk_staff_id,
        b.fk_company_id,
        CONCAT(
                a.`name`,
                IF(IFNULL(a.name_en, '') = '', '', CONCAT('（',a.name_en,'）'))
        ) AS fullName,
        b.bd_code,
        b.fk_area_region_id
      FROM ais_permission_center.m_staff a
      INNER JOIN ais_sale_center.r_staff_bd_code b ON a.id = b.fk_staff_id
      INNER JOIN ais_permission_center.m_company c ON b.fk_company_id = c.id
      WHERE a.is_on_duty = 1 AND a.is_active = 1
      <if test="companyId != null">
        AND b.fk_company_id = #{companyId}
      </if>
      <if test="charFilterList != null and charFilterList.size() > 0">
        <foreach collection="charFilterList" item="charFilter">
          AND b.bd_code NOT LIKE #{charFilter}
        </foreach>
      </if>
      <if test="bdCodeFilterList != null and bdCodeFilterList.size() > 0">
        AND b.bd_code NOT IN
        <foreach collection="bdCodeFilterList" item="bdCodeFilter" open="(" separator="," close=")">
          #{bdCodeFilter}
        </foreach>
      </if>
      ORDER BY c.view_order DESC, b.bd_code ASC
 </select>

  <select id="selectStaffBdCodeAll" resultMap="BaseResultMap">
    SELECT rsbc.*
    <if test="staffBdCodeDto.fkInstitutionPermissionGroupStaffIdBd != null and staffBdCodeDto.fkInstitutionPermissionGroupStaffIdBd != ''">
    ,rsb.id AS fkInstitutionPermissionGroupStaffBDId
    </if>
    FROM ais_sale_center.r_staff_bd_code rsbc
    <if test="staffBdCodeDto.fkInstitutionPermissionGroupStaffIdBd != null and staffBdCodeDto.fkInstitutionPermissionGroupStaffIdBd != ''">
      LEFT JOIN ais_permission_center.r_staff_bd rsb
      ON rsb.fk_staff_id_bd = rsbc.fk_staff_id
      AND rsb.fk_staff_id = #{staffBdCodeDto.fkInstitutionPermissionGroupStaffIdBd}
      INNER JOIN(
      SELECT DISTINCT ms.*
      FROM ais_permission_center.m_staff ms
      ORDER BY ms.is_on_duty DESC,ms.gmt_create DESC
      ) ms ON rsbc.fk_staff_id = ms.id
    </if>
    WHERE 1=1
    <choose>
      <when test="staffBdCodeDto.fkCompanyId != null and staffBdCodeDto.fkCompanyId != ''">
        AND rsbc.fk_company_id = #{staffBdCodeDto.fkCompanyId}
      </when>

      <otherwise>
        AND rsbc.fk_company_id IN
        <foreach collection="companyIdList" item="companyId" open="(" separator="," close=")">
          #{companyId}
        </foreach>
      </otherwise>
    </choose>

    <if test="staffBdCodeDto.keyWord != null and staffBdCodeDto.keyWord != ''">
      AND (rsbc.fk_staff_id IN
      <foreach collection="staffIdList" item="staffId" open="(" separator="," close=")">
        #{staffId}
      </foreach>)
      OR rsbc.bd_code LIKE CONCAT('%',#{staffBdCodeDto.keyWord},'%')
    </if>


    <if test="staffBdCodeDto.fkInstitutionPermissionGroupStaffIdBd != null and staffBdCodeDto.fkInstitutionPermissionGroupStaffIdBd != ''">
      <choose>
        <when test="staffBdCodeDto.isBind == 0">
          AND rsb.id IS NULL  <!-- 未绑定 -->
        </when>
        <when test="staffBdCodeDto.isBind == 1">
          AND rsb.id IS NOT NULL  <!-- 已绑定 -->
        </when>
      </choose>
    </if>
<!--    <if test="ew != null and ew.sqlSegment != null">-->
<!--      AND ${ew.sqlSegment}-->
<!--    </if>-->
    <choose>
      <when test="staffBdCodeDto.fkInstitutionPermissionGroupStaffIdBd != null and staffBdCodeDto.fkInstitutionPermissionGroupStaffIdBd != ''">
        ORDER BY ms.is_on_duty DESC,ms.gmt_create DESC
      </when>

      <otherwise>
        ORDER BY rsbc.bd_code ASC
      </otherwise>
    </choose>
  </select>
</mapper>