package com.get.financecenter.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.get.financecenter.entity.AccountingItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 科目下拉框Vo
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AccountingItemSelectVo extends AccountingItem {

    @ApiModelProperty(value = "科目类型名称")
    private String typeName;

    @ApiModelProperty(value = "余额方向：0借/1贷。资产、费用【借增贷减】。负债、权益、收入【贷增借减】。")
    private String directionName;

    @ApiModelProperty(value = "是否激活：0否/1是")
    private String isActiveName;

    @ApiModelProperty(value = "子科目List")
    List<AccountingItemSelectVo> childrenAccountingItemSelect;
}
