<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InformationMapper">


    <select id="getAllInformationSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">

        select n.id,n.title AS name from s_information n
        ORDER BY IFNULL(n.gmt_modified,n.gmt_create) desc
    </select>
</mapper>