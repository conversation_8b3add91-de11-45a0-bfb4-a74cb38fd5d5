package com.get.examcenter.feign;

import com.get.common.constant.AppCenterConstant;
import com.get.core.tool.api.Result;
import com.get.salecenter.dto.DataCollectionQuestionDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Feign接口类
 */
@FeignClient(
        value = AppCenterConstant.APPLICATION_EXAM_CENTER
)
public interface IExamCenterClient {

    String API_PREFIX = "/feign";

    /**
     * feign调用，根据考试ids获取名称
     */
    String GET_EXAMINATION_NAMES_BY_EXAMINATION_IDS = API_PREFIX + "/get-examination-names-by-examination-ids";
    /**
     * feign调用 根据考题类型ids获取名称
     */
    String GET_NAMES_BY_QUESTION_TYPE_IDS = API_PREFIX + "/get-names-by-question-type-ids";
    /**
     * feign调用 根据员工ids（BD）获取用户ids
     */
    String GET_USER_IDS_BY_STAFF_IDS = API_PREFIX + "/get-user-ids-by-staff-ids";
    /**
     * 学校机构资料回显 根据手机号获取问题
     * @param contactTel
     * @return
     */
    String GET_DATA_COLLECTION_QUESTIONS = API_PREFIX + "/get-data-collection-questions";
    /**
     * 保存学校机构资料问题
     */
    String SAVE_DATA_COLLECTION_QUESTION = API_PREFIX + "/save-data-collection-question";

    /**
     * feign调用，根据考试ids获取名称
     * @param examinationIds
     * @return
     */
    @PostMapping(GET_EXAMINATION_NAMES_BY_EXAMINATION_IDS)
    Result<Map<Long, String>> getExaminationNamesByExaminationIds(@RequestBody Set<Long> examinationIds);

    /**
     * feign调用 根据考题类型ids获取名称
     * @param questionTypeIds
     * @return
     */
    @PostMapping(GET_NAMES_BY_QUESTION_TYPE_IDS)
    Result<Map<Long, String>> getNamesByQuestionTypeIds(@RequestBody Set<Long> questionTypeIds);

    /**
     * feign调用 根据员工ids（BD）获取用户ids
     * @param fkStaffIds
     * @return
     */
    @PostMapping(GET_USER_IDS_BY_STAFF_IDS)
    Result<Set<Long>> getUserIdsByStaffIds(@RequestBody Set<Long> fkStaffIds);

    /**
     * 学校机构资料回显 根据手机号获取问题
     * @param contactTel
     * @return
     */
    @GetMapping(GET_DATA_COLLECTION_QUESTIONS)
    Result<List<DataCollectionQuestionDto>> getDataCollectionQuestions(@RequestParam(value = "contactTel") String contactTel);

    /**
     * 保存学校机构资料问题
     *
     * @param dataCollectionQuestionList
     * @param receiptCode
     * @param contactTel
     * @return
     */
    @PostMapping(SAVE_DATA_COLLECTION_QUESTION)
    Result<Boolean> saveDataCollectionQuestion(@RequestBody List<DataCollectionQuestionDto> dataCollectionQuestionList, @RequestParam(value = "contactTel") String receiptCode, @RequestParam("contactTel") String contactTel);

}
