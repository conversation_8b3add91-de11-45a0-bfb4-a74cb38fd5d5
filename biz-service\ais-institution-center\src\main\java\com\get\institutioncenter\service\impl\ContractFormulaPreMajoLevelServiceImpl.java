package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.ContractFormulaPreMajorLevelMapper;
import com.get.institutioncenter.entity.ContractFormulaPreMajorLevel;
import com.get.institutioncenter.service.IContractFormulaPreMajorLevelService;
import com.get.institutioncenter.dto.ContractFormulaPreMajorLevelDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/26 15:02
 * @verison: 1.0
 * @description:
 */
@Service
public class ContractFormulaPreMajoLevelServiceImpl implements IContractFormulaPreMajorLevelService {
    @Resource
    private ContractFormulaPreMajorLevelMapper contractFormulaPreMajorLevelMapper;
    @Resource
    private UtilService utilService;

    @Override
    public Long addContractFormulaPreMajorLevel(ContractFormulaPreMajorLevelDto contractFormulaPreMajorLevelDto) {
        if (GeneralTool.isEmpty(contractFormulaPreMajorLevelDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ContractFormulaPreMajorLevel contractFormulaPreInstitution = BeanCopyUtils.objClone(contractFormulaPreMajorLevelDto, ContractFormulaPreMajorLevel::new);
        utilService.updateUserInfoToEntity(contractFormulaPreInstitution);
        contractFormulaPreMajorLevelMapper.insertSelective(contractFormulaPreInstitution);
        return contractFormulaPreInstitution.getId();
    }

    @Override
    public void deleteByFkid(Long contractFormulaId) {
        LambdaQueryWrapper<ContractFormulaPreMajorLevel> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContractFormulaPreMajorLevel::getFkContractFormulaId, contractFormulaId);
        contractFormulaPreMajorLevelMapper.delete(wrapper);
    }

    @Override
    public List<Long> getMajorLevelIdListByFkid(Long contractFormulaId) {
        return contractFormulaPreMajorLevelMapper.getMajorLevelIdListByFkId(contractFormulaId);
    }
}
