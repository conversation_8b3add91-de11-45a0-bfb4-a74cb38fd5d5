package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.vo.CourseTypeVo;
import com.get.institutioncenter.entity.InstitutionCourseType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface InstitutionCourseTypeMapper extends BaseMapper<InstitutionCourseType> {
    int insert(InstitutionCourseType record);

    int insertSelective(InstitutionCourseType record);

    String getNamesByCourseId(@Param("id") Long id);

    String getFullNamesByCourseId(@Param("id") Long id);

    List<CourseTypeVo> getNamesByCourseIds(@Param("fkInstitutionCourseIds") Set<Long> fkInstitutionCourseIds);

    List<Long> getCourseIdsByTypeId(@Param("id") Long id);

    void deleteByByCourseId(@Param("id") Long id);

    List<Long> getTypeIdsByCourseId(@Param("id") Long id);

    String getTypeIdStringByCourseId(@Param("id") Long id);

    /**
     * 根据公式id查找类型id
     *
     * @param id
     * @return
     */
    List<Long> getTypeIdsByContractFormula(Long id);

    /**
     * @Description：校验该课程类型是否有课程关联
     * @Param
     * @Date 14:15 2021/5/10
     * <AUTHOR>
     */
    boolean isExistByCourseType(@Param("fkCourseTypeId") Long fkCourseTypeId);

    /**
     * @Description：校验该课程id是否有類型关联
     * @Param
     * @Date 14:15 2021/5/10
     * <AUTHOR>
     */
    boolean isExistByCourseId(@Param("courseId") Long courseId);

}