<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionCourseFacultyMapper">
  <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.InstitutionCourseFaculty">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_institution_course_id" jdbcType="BIGINT" property="fkInstitutionCourseId" />
    <result column="fk_institution_faculty_id" jdbcType="BIGINT" property="fkInstitutionFacultyId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insert" parameterType="com.get.institutioncenter.entity.InstitutionCourseFaculty">
    insert into r_institution_course_faculty (id, fk_institution_course_id, fk_institution_faculty_id, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkInstitutionCourseId,jdbcType=BIGINT}, #{fkInstitutionFacultyId,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionCourseFaculty">
    insert into r_institution_course_faculty
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkInstitutionCourseId != null">
        fk_institution_course_id,
      </if>
      <if test="fkInstitutionFacultyId != null">
        fk_institution_faculty_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionCourseId != null">
        #{fkInstitutionCourseId,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionFacultyId != null">
        #{fkInstitutionFacultyId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <!--<select id="getFacultyIdsByCourseId" resultType="java.lang.Long">
    select  itf.id from r_institution_course_faculty cf LEFT JOIN m_institution_faculty itf
     on cf.fk_institution_faculty_id = itf.id where cf.fk_institution_course_id = #{id}
  </select>-->

  <select id="getCourseIdsByFacultyId" resultType="java.lang.Long">
  select fk_institution_course_id from r_institution_course_faculty where fk_institution_faculty_id = #{id}
</select>
  <select id="getFacultyIdsByCourseId" resultType="java.lang.Long">
    select fk_institution_faculty_id from r_institution_course_faculty where fk_institution_course_id = #{id}
  </select>
  <select id="deleteByByCourseId">
    delete from r_institution_course_faculty where fk_institution_course_id = #{id}
  </select>
  <select id="getCountByFaculty" resultType="java.lang.Integer">
    select count(*) from r_institution_course_faculty where fk_institution_faculty_id =#{fkInstitutionFacultyId}
  </select>
  <select id="isExistByCourseId" resultType="java.lang.Boolean">
    SELECT IFNULL(max(id),0) id from r_institution_course_faculty where fk_institution_course_id=#{courseId}
  </select>
</mapper>