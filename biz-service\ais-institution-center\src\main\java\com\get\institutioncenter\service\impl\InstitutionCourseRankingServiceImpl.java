package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.InstitutionCourseRankingMapper;
import com.get.institutioncenter.vo.InstitutionCourseRankingVo;
import com.get.institutioncenter.entity.InstitutionCourseRanking;
import com.get.institutioncenter.service.IInstitutionCourseRankingService;
import com.get.institutioncenter.dto.InstitutionCourseRankingDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/12/18
 * @TIME: 17:59
 * @Description:
 **/
@Service
public class InstitutionCourseRankingServiceImpl extends BaseServiceImpl<InstitutionCourseRankingMapper, InstitutionCourseRanking> implements IInstitutionCourseRankingService {
    @Resource
    private InstitutionCourseRankingMapper institutionCourseRankingMapper;
    @Resource
    private UtilService utilService;

    @Override
    public InstitutionCourseRankingVo findInstitutionCourseRankingById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionCourseRanking institutionCourseRanking = institutionCourseRankingMapper.selectById(id);
        if (GeneralTool.isEmpty(institutionCourseRanking)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionCourseRankingVo institutionCourseRankingVo = BeanCopyUtils.objClone(institutionCourseRanking, InstitutionCourseRankingVo::new);
        if (GeneralTool.isNotEmpty(institutionCourseRankingVo.getRankingType())) {
            institutionCourseRankingVo.setRankingName(ProjectExtraEnum.getValue(institutionCourseRankingVo.getRankingType()));
        }
        return institutionCourseRankingVo;
    }

    @Override
    public List<InstitutionCourseRankingVo> getInstitutionCourseRankings(InstitutionCourseRankingDto institutionCourseRankingDto, Page page) {
        LambdaQueryWrapper<InstitutionCourseRanking> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(institutionCourseRankingDto)) {
            if (GeneralTool.isNotEmpty(institutionCourseRankingDto.getFkInstitutionCourseId())) {
                wrapper.eq(InstitutionCourseRanking::getFkInstitutionCourseId, institutionCourseRankingDto.getFkInstitutionCourseId());
            }
        }
        wrapper.orderByDesc(InstitutionCourseRanking::getGmtCreate);
        //获取分页数据
        IPage<InstitutionCourseRanking> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<InstitutionCourseRanking> ct = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<InstitutionCourseRankingVo> convertDatas = new ArrayList<>();
        for (InstitutionCourseRanking c : ct) {
            InstitutionCourseRankingVo institutionCourseRankingVo = BeanCopyUtils.objClone(c, InstitutionCourseRankingVo::new);
            if (GeneralTool.isNotEmpty(institutionCourseRankingVo.getRankingType())) {
                institutionCourseRankingVo.setRankingName(ProjectExtraEnum.getValueByKey(institutionCourseRankingVo.getRankingType(), ProjectExtraEnum.RANKING_TYPE));
            }
            convertDatas.add(institutionCourseRankingVo);
        }
        return convertDatas;
    }

    @Override
    public InstitutionCourseRankingVo updateInstitutionCourseRanking(InstitutionCourseRankingDto institutionCourseRankingDto) {
        if (institutionCourseRankingDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(institutionCourseRankingDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionCourseRanking ct = institutionCourseRankingMapper.selectById(institutionCourseRankingDto.getId());
        if (ct == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionCourseRanking institutionCourseRanking = BeanCopyUtils.objClone(institutionCourseRankingDto, InstitutionCourseRanking::new);
        utilService.updateUserInfoToEntity(institutionCourseRanking);
        institutionCourseRankingMapper.updateById(institutionCourseRanking);
        return findInstitutionCourseRankingById(institutionCourseRanking.getId());
    }

    @Override
    public void delete(Long id) {
        //TODO 改过
        //InstitutionCourseRanking institutionCourseRanking = findInstitutionCourseRankingById(id);
        InstitutionCourseRankingVo institutionCourseRanking = findInstitutionCourseRankingById(id);
        if (institutionCourseRanking == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        int i = institutionCourseRankingMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<InstitutionCourseRankingDto> institutionCourseRankingDtos) {
        for (InstitutionCourseRankingDto institutionCourseRankingDto : institutionCourseRankingDtos) {
            InstitutionCourseRanking appInfo = BeanCopyUtils.objClone(institutionCourseRankingDto, InstitutionCourseRanking::new);
            utilService.updateUserInfoToEntity(appInfo);
            institutionCourseRankingMapper.insert(appInfo);
        }
    }

    @Override
    public List<Map<String, Object>> findType() {
        return ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.RANKING_TYPE);
    }

    @Override
    public void deleteCourseRankingByCourseId(Long id) {
        LambdaQueryWrapper<InstitutionCourseRanking> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionCourseRanking::getFkInstitutionCourseId, id);
        int j = institutionCourseRankingMapper.delete(wrapper);
        if (j < 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

}
