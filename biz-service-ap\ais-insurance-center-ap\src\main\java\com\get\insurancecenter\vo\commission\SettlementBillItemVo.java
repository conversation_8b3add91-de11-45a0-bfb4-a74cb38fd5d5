package com.get.insurancecenter.vo.commission;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author:Oliver
 * @Date: 2025/6/13
 * @Version 1.0
 * @apiNote:结算单明细vo
 */
@Data
public class SettlementBillItemVo extends SettlementOrderVo {

    @ApiModelProperty(value = "保险公司名称")
    private String insuranceCompanyName;

    @ApiModelProperty(value = "保险产品名称")
    private String productTypeName;

    @ApiModelProperty(value = "结算金额")
    private BigDecimal settlementAmount;

    @ApiModelProperty(value = "对账单明细ID")
    private Long settlementBillItemId;

    @ApiModelProperty(value = "结算比例-带单位")
    private String commissionRateStr;
}
