package com.get.institutioncenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/10 15:28
 */
//二级学院
@Data
public class InstitutionScholarshipSubDto implements Serializable {

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

    /**
     * 学校学院Id（一级）
     */
    @ApiModelProperty(value = "学校学院Id（一级）")
    private Long fkInstitutionFacultyId;

    /**
     * 学校学院Id（二级）
     */
    @ApiModelProperty(value = "学校学院Id（二级）")
    private Long fkInstitutionFacultyIdSub;

    /**
     * 课程专业等级Id
     */
    @ApiModelProperty(value = "课程专业等级Id")
    private String fkMajorLevelIds;

    /**
     * 奖学金金额
     */
    @ApiModelProperty(value = "奖学金金额")
    private String scholarshipAmount;

    /**
     * 奖学金名额
     */
    @ApiModelProperty(value = "奖学金名额")
    private String scholarshipQuota;

    /**
     * 申请条件
     */
    @ApiModelProperty(value = "申请条件")
    private String appCondition;

    /**
     * 申请方式
     */
    @ApiModelProperty(value = "申请方式")
    private String appMethod;

    /**
     * 申请截止日期：[2022-1-5,2023-10-5][2022-1-5,2023-10-5]
     */
    @ApiModelProperty(value = "申请截止日期：[2022-1-5,2023-10-5][2022-1-5,2023-10-5]")
    private String appDeadline;

    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    private String publicLevel;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    private Date publishTime;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

    /**
     * 详细信息
     */
    @ApiModelProperty(value = "详细信息")
    private String appDetail;

    @ApiModelProperty("学院等级，1是1级，2是二级")
    private int Level;


    @ApiModelProperty(value = "学校学院Id（二级）")
    private String fkInstitutionFacultyIdSubName;


}
