package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PermissionGroupGradeResourceVo extends BaseVoEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 权限组别Id
     */
    @ApiModelProperty("权限组别Id")
    private Long fkPermissionGroupId;
    /**
     * 权限级别Id
     */
    @ApiModelProperty("权限级别Id")
    private Long fkPermissionGradeId;
    /**
     * 系统资源Key
     */
    @ApiModelProperty("系统资源Key")
    private String fkResourceKey;

    private String keyWord;

    private List<String> resourceKeys;
}
