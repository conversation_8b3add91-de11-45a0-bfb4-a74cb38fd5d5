package com.get.partnercenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

@Data
public class CommissionConfirmStudentsVo {
    @ApiModelProperty("offerItemId")
    private Long id;
    @ApiModelProperty("学生申请方案项目父Id")
    private Long fkParentStudentOfferItemId;
    @ApiModelProperty("学生申请方案Id")
    private Long fkStudentOfferId;

    @ApiModelProperty("是否系统自动确认：0否/1是/2待确认")
    private String isSystemConfirmed;

    @ApiModelProperty("学生offer步骤ID")
    private Long studentOfferItemStepId;

    @ApiModelProperty("步骤名称")
    private String stepName;
    @ApiModelProperty("学生ID")
    private Long studentId;
    @ApiModelProperty("学生中文名称")
    private String studentName;
    @ApiModelProperty("姓（英/拼音）")
    private String lastName;
    @ApiModelProperty("名（英/拼音）")
    private String firstName;
    @ApiModelProperty("生日")
    private LocalDate birthday;
    @ApiModelProperty(value = "国家名称")
    private String countryName;

    @ApiModelProperty(value = "学校名称")
    private String institutionName;
    @ApiModelProperty(value = "申请课程")
    private String courseName;
    @ApiModelProperty(value = "开学时间")
    private LocalDate openingTime;
    @ApiModelProperty(value = "操作人")
    private String gmtCreateUser;
    @ApiModelProperty(value = "创建时间")
    private LocalDate gmtCreate;

    @ApiModelProperty(value = "是否后续课程：0否/1是")
    private Boolean isFollow;

    @ApiModelProperty(value = "渠道名称")
    private String channerName;
    @ApiModelProperty(value = "提供商名称")
    private String providerName;





    public String getIsSystemConfirmed() {
        if(isSystemConfirmed==null || "".equals(isSystemConfirmed)) {
            isSystemConfirmed="2";
        }
        return isSystemConfirmed;
    }
}
