package com.get.common.eunms;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 错误码枚举
 *
 * @author: Jack
 * @create: 2020-4-15
 * @verison: 1.0
 * @description: 错误码枚举
 */
public enum ErrorCodeEnum {

    /**
     * 缺少参数 400
     */
    PARAM_REQUIRED("0001", "缺少参数"),

    /**
     * 无效参数 400
     */
    INVALID_PARAM("0002", "无效参数"),

    /**
     * 无效签名
     */
    INVALID_SIGNATURE("0003", "无效签名"),

    /**
     * 验证失败401
     */
    VERIFY_FAILED("0004", "验证失败"),

    /**
     * 服务不可用 503
     */
    SERVICE_UNABAILABLE("0005", "服务不可用"),

    /**
     * 会话已失效
     */
    SESSION_EXPIRED("0006", "会话已失效"),

    /**
     * 没权限
     */
    NO_AUTHORITY("0007", "没权限"),

    BLOCKING_ACCESS("1001", "禁止访问"),

    /**
     * 请求错误 404
     */
    BAD_REQUEST("1002", "请求错误"),

    /**
     * 请求成功 200
     */
    REQUEST_OK("1003", "请求成功"),

    /**
     * 服务器错误 500
     */
    SERVER_EXCEPTION("1004", "服务器错误"),

    /**
     * 访问外部服务错误 502
     */
    BAD_GATEWAY("1005", "访问外部服务错误"),

    /**
     * 请求超时504
     */
    GATEWAY_TIMEOUT("1006", "网关超时"),

    /**
     * feign调用失败
     */
    FEIGN_CALL_FAILED("1007", "feign调用失败"),

    /**
     * 请求成功,但需要提示message
     */
    REQUEST_OK_MESSAGE("1008", "请求成功,需要提示message");

    private String code;
    private String message;

    ErrorCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static ErrorCodeEnum valueOf(int status) {
        switch (status) {
            case 400:
                return INVALID_PARAM;
            case 401:
                return VERIFY_FAILED;
            case 403:
                return BLOCKING_ACCESS;
            case 404:
                return BAD_REQUEST;
            case 500:
                return SERVER_EXCEPTION;
            case 502:
                return BAD_GATEWAY;
            case 503:
                return SERVICE_UNABAILABLE;
            case 504:
                return GATEWAY_TIMEOUT;
            default:
                return SERVER_EXCEPTION;
        }
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @JsonValue
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getValue() {
        return this.code;
    }
}
