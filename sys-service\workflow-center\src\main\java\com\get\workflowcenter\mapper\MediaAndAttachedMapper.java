package com.get.workflowcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.workflowcenter.entity.WorkFlowMediaAndAttached;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface MediaAndAttachedMapper extends BaseMapper<WorkFlowMediaAndAttached> {

//    /**
//     * 添加
//     *
//     * @param record
//     * @return
//     */
//    int insertSelective(MediaAndAttached record);


}