package com.get.permissioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.permissioncenter.dto.MediaAndAttachedDto;
import com.get.permissioncenter.dto.StaffHrEventDto;
import com.get.permissioncenter.vo.CommentVo;
import com.get.permissioncenter.vo.MediaAndAttachedVo;
import com.get.permissioncenter.vo.StaffHrEventVo;
import com.get.permissioncenter.entity.StaffHrEvent;
import com.get.permissioncenter.dto.CommentDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/13
 * @TIME: 10:27
 * @Description: 人事记录接口
 **/
public interface IStaffHrEventService extends BaseService<StaffHrEvent> {

    /***
     * 获取单个人事记录详情
     * @param id
     * @return
     */
    StaffHrEventVo getStaffHrEventDtoById(Long id);


    /**
     * 新增
     *
     * @param staffHrEventDto
     * @return
     */
    Long addStaffHrEvent(StaffHrEventDto staffHrEventDto);

    /**
     * 修改
     *
     * @param staffHrEventDto
     * @return
     */

    StaffHrEventVo updateStaffHrEventVo(StaffHrEventDto staffHrEventDto);


    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);


    /**
     * @return java.util.List<com.get.permissioncenter.com.get.permissioncenter.vo.MediaAndAttachedDto>
     * @Description: 保存上传的文件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> addHrEventMedia(List<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * @return java.util.List<com.get.permissioncenter.com.get.permissioncenter.vo.MediaAndAttachedDto>
     * @Description: 查询附件
     * @Param [data, page]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> getHrEventMedia(MediaAndAttachedDto data, Page page);

    /**
     * 查询员工人事记录
     *
     * @param staffHrEventDto
     * @param page
     * @return
     */
    List<StaffHrEventVo> getStaffHrEventDto(StaffHrEventDto staffHrEventDto, Page page);


    /**
     * @return java.lang.Long
     * @Description: 增加或者更新评论
     * @Param [commentDto]
     * <AUTHOR>
     **/
    Long editComment(CommentDto commentDto);

    /**
     * @return java.util.List<com.get.salecenter.com.get.permissioncenter.vo.CommentVo>
     * @Description: 获取所有评论
     * @Param [commentDto, page]
     * <AUTHOR>
     */
    List<CommentVo> getComments(CommentDto commentDto, Page page);

}
