package com.get.remindercenter.component;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.dao.EmailSenderQueueMapper;
import com.get.remindercenter.dao.EmailTemplateMapper;
import com.get.remindercenter.dto.WorkLeaveReminderDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.EmailTemplate;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.service.RemindTaskQueueService;
import com.get.remindercenter.utils.ReminderTemplateUtils;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import com.get.workflowcenter.feign.IWorkflowCenterClient;
import com.get.workflowcenter.vo.ActHiTaskInstVo;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

@Component("workLeaveReminderEmailHelper")
@Slf4j
public class WorkLeaveReminderEmailHelper extends EmailAbstractHelper{

    @Resource
    private IWorkflowCenterClient workflowCenterClient;

    @Resource
    private EmailTemplateMapper emailTemplateMapper;

    @Resource
    private EmailSenderQueueMapper emailSenderQueueMapper;


    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private RemindTaskQueueService remindTaskQueueService;

    @Override
    public void sendMail(EmailSenderQueue emailSenderQueue) {
        StringJoiner failedEmails = new StringJoiner("; "); // 新增：记录失败邮箱
        try {
            WorkLeaveReminderDto workLeaveReminderDto = assembleEmailData(emailSenderQueue);
            //设置邮件模板
            String template =  setEmailTemplate(workLeaveReminderDto);
            EmailSystemMQMessageDto emailSystemMQMessageDto = new EmailSystemMQMessageDto();
            emailSystemMQMessageDto.setEmailSenderQueueId(workLeaveReminderDto.getId());
            emailSystemMQMessageDto.setTitle(workLeaveReminderDto.getEmailTitle());
            emailSystemMQMessageDto.setContent(template);
            StringJoiner emailsCombined = new StringJoiner(", ");

            for (String id : workLeaveReminderDto.getStaffEmailSet()){
                StaffVo staffVo = null;
                try {
                    Result<StaffVo> staffDtoResult = permissionCenterClient.getCompanyIdByStaffId(Long.valueOf(id));
                    if (staffDtoResult.isSuccess() && GeneralTool.isNotEmpty(staffDtoResult.getData())) {
                        staffVo = staffDtoResult.getData();
                    }
                    emailSystemMQMessageDto.setToEmail(staffVo.getEmail());
                    //rocKetMqCenterClient.getSystemSendEmail(emailSystemMQMessageDto);
                    remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
                    if (GeneralTool.isNotEmpty(staffVo.getEmail())) {
                        emailsCombined.add(staffVo.getEmail());
                    }
                }catch (Exception e){
                    // 记录发送失败的邮箱
                    String failedEmail = staffVo != null && staffVo.getEmail() != null ? staffVo.getEmail() : "staffId:" + staffVo.getId();
                    failedEmails.add(failedEmail + "(" + (e.getMessage() != null ? e.getMessage() : "发送失败") + ")");
                    log.error("邮件发送失败 - staffId: {}, email: {}, 原因: {}",  staffVo.getId(), failedEmail, e.getMessage());
                }
            }

            emailSenderQueue.setEmailTo(emailsCombined.toString());
            LambdaUpdateWrapper<EmailSenderQueue> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(EmailSenderQueue::getId, emailSenderQueue.getId())  // 更新条件（按ID匹配）
                    .set(EmailSenderQueue::getEmailTo, emailsCombined.toString());  // 只更新 emailTo 字段
            // 如果 failedEmails 不为空，则额外更新 errorMessage
            if (failedEmails.length() > 0) {
                updateWrapper.set(EmailSenderQueue::getErrorMessage, "失败邮箱: " + failedEmails.toString());
            }
            emailSenderQueueMapper.update(null, updateWrapper);  // 传入 null，由 updateWrapper 控制更新
        }catch (Exception e){
            log.error("WorkLeaveReminderEmailHelper error:{}", e);
            emailSenderQueue.setOperationStatus(-1);
            emailSenderQueue.setOperationCount(emailSenderQueue.getOperationCount() + 1);
            failedEmails.add("邮件发送异常"+e.getMessage());
            emailSenderQueue.setErrorMessage(failedEmails.toString());
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }

    }

    private String setEmailTemplate(WorkLeaveReminderDto reminderDto) {
        List<EmailTemplate> remindTemplates = new ArrayList<>();
        if (reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.WORK_LEAVE_WORKFLOW_REMINDER.getEmailTemplateKey())) {
            remindTemplates = emailTemplateMapper.selectList(Wrappers.<EmailTemplate>lambdaQuery().eq(EmailTemplate::getEmailTypeKey, EmailTemplateEnum.WORK_LEAVE_WORKFLOW_REMINDER.getEmailTemplateKey()));
        } else if (reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.APPLN_TERM_INVAL_WORKFLOW_REMINDER.getEmailTemplateKey())) {
            remindTemplates = emailTemplateMapper.selectList(Wrappers.<EmailTemplate>lambdaQuery().eq(EmailTemplate::getEmailTypeKey, EmailTemplateEnum.APPLN_TERM_INVAL_WORKFLOW_REMINDER.getEmailTemplateKey()));
        }

        if (GeneralTool.isEmpty(remindTemplates)) {
            log.error("邮箱模板不存在，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        String emailTemplate =null;
        if (!reminderDto.getLanguageCode().equals("en")) {
            emailTemplate = remindTemplates.get(0).getEmailTemplate();
        }else {
            emailTemplate = remindTemplates.get(0).getEmailTemplateEn();
        }
        emailTemplate  = ReminderTemplateUtils.getReminderTemplate(reminderDto.getMap(), emailTemplate);
        if (GeneralTool.isEmpty(emailTemplate)) {
            log.error("邮箱模板内容为空，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        emailTemplate = emailTemplate.replace("#{taskTitle}", reminderDto.getEmailTitle());
        //把emailTemplate插入到父模板里
        String parentEmailTemplate = null;
        if(GeneralTool.isNotEmpty(remindTemplates.get(0).getFkParentEmailTemplateId())&&remindTemplates.get(0).getFkParentEmailTemplateId()!=0){
            EmailTemplate parentTemplate = emailTemplateMapper.selectById(remindTemplates.get(0).getFkParentEmailTemplateId());
            Map map = new HashMap();
            map.put("subtemplate",emailTemplate);
            if (reminderDto.getLanguageCode().equals("en")) {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplateEn());
            }else {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplate());
            }

        }
        return parentEmailTemplate;
    }

    @Override
    public WorkLeaveReminderDto assembleEmailData(EmailSenderQueue emailSenderQueue) {
        WorkLeaveReminderDto reminderDto = new WorkLeaveReminderDto();
        BeanUtils.copyProperties(emailSenderQueue, reminderDto);
        String emailParameter = emailSenderQueue.getEmailParameter();
        Long companyId = 3L;
        //跳转链接
        String taskLink = "";
        if(GeneralTool.isNotEmpty(emailParameter)){
            ObjectMapper mapper = new ObjectMapper();
            Map<String, String> parsedMap = null;
            try {
                parsedMap = mapper.readValue(emailParameter, Map.class);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            String  id = parsedMap.get("staffId");

            String superiorId = parsedMap.get("superiorId");
            StaffVo staffVo = null;
            if(GeneralTool.isNotEmpty(superiorId)){
                staffVo = permissionCenterClient.getCompanyIdByStaffId(Long.valueOf(superiorId)).getData();
            }
            String  staffIdList = parsedMap.get("staffIdList");

            if(GeneralTool.isNotEmpty(staffIdList)){
                if (staffIdList != null && staffIdList.startsWith("[") && staffIdList.endsWith("]")) {
                    staffIdList = staffIdList.substring(1, staffIdList.length() - 1);
                }
                List<String> list = Arrays.asList(staffIdList.split("\\s*,\\s*"));
                reminderDto.setStaffEmailSet(list);
            }
            if (GeneralTool.isNotEmpty(id)) {
                Result<StaffVo> staffDtoResult = permissionCenterClient.getCompanyIdByStaffId(Long.valueOf(id));
                if (staffDtoResult.isSuccess() && GeneralTool.isNotEmpty(staffDtoResult.getData())) {
                    staffVo = staffDtoResult.getData();
                }
            }
            if (GeneralTool.isNotEmpty(staffVo)) {
                companyId  = staffVo.getFkCompanyId();
            }
            if (GeneralTool.isNotEmpty(parsedMap.get("approvalUrl"))) {
                taskLink = parsedMap.get("approvalUrl");
            }
        }
        //获取中英文配置
        Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
        String versionValue2 = versionConfigMap.get(companyId);
        reminderDto.setLanguageCode(versionValue2);
        Map<String, String> map = new HashMap<>();
        //任务开始时间
        Date startTime = null;
        //任务结束时间
        Date endTime =null;

        //请假理由
        String leaveReason = null;
        //请假时间
        String leaveDays = null;
        //请假类型
        String leaveType = null;
        //部门名称
        String departmentName = null;
        //办公室名称
        String officeName = null;
        String fkRemindEventTypeKey = emailSenderQueue.getFkEmailTypeKey();

        if (TableEnum.ACT_HI_TASKINST.key.equals(emailSenderQueue.getFkTableName()) &&EmailTemplateEnum.WORK_LEAVE_WORKFLOW_REMINDER.getEmailTemplateKey() .equals(fkRemindEventTypeKey)) {
            //工作流的内容
            Result<ActHiTaskInstVo> result = workflowCenterClient.getActHiTaskInstDtoAndLeaveFormMessage(emailSenderQueue.getFkTableId());
            log.error("发送邮件工作流调用返回信息：{}", result.getMessage());
            if (!result.isSuccess() || GeneralTool.isEmpty(result.getData())) {
                log.error("执行任务id={}，返回的工作流为空。", emailSenderQueue.getId());
            } else {
                log.info("执行任务id={}，返回的工作流详情：{}", emailSenderQueue.getId(), GeneralTool.toJson(result.getData()));
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            ActHiTaskInstVo actHiTaskInstVo = result.getData();
            if (TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key.equals(actHiTaskInstVo.getWorkFlowType())) {
                leaveReason = actHiTaskInstVo.getLeaveReason();
                leaveDays = actHiTaskInstVo.getLeaveDays();
                leaveType = actHiTaskInstVo.getLeaveType();
                departmentName = actHiTaskInstVo.getDepartmentName();
                if (GeneralTool.isEmpty(actHiTaskInstVo.getOfficeName())) {
                    officeName = "";
                } else {
                    officeName = actHiTaskInstVo.getOfficeName();
                }
                startTime = actHiTaskInstVo.getStartDay();
                endTime = actHiTaskInstVo.getEndDay();
                map.put("leaveReason", leaveReason);
                map.put("leaveDays", leaveDays);
                map.put("leaveType", leaveType);
                map.put("departmentName", departmentName);
                map.put("officeName", officeName);
                map.put("startTime", sdf.format(startTime));
                map.put("endTime",sdf.format(endTime) );
                if (GeneralTool.isNotEmpty(taskLink)) {
                    map.put("approvalUrl", taskLink);
                }
            }
        }else if(reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.APPLN_TERM_INVAL_WORKFLOW_REMINDER.getEmailTemplateKey())){
            Result<ActHiTaskInstVo> result = workflowCenterClient.getActHiTaskInstDtoAndStudentOffer(reminderDto.getFkTableId());
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                ActHiTaskInstVo actHiTaskInstVo = result.getData();
                if (GeneralTool.isNotEmpty(actHiTaskInstVo.getMap())) {
                  map = actHiTaskInstVo.getMap();
                }
            }
        }
        reminderDto.setMap(map);
        return reminderDto;
    }




}
