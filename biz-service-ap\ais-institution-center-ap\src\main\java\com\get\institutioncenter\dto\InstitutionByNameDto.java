package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * author:Neil
 * Time: 16:54
 * Date: 2022/6/1
 * Description:
 */
@Data
public class InstitutionByNameDto extends BaseVoEntity {
    @ApiModelProperty(value = "学校名")
    private String institutionName;

    @ApiModelProperty(value = "学校ids")
    private List<Long> institutionIds;

    @ApiModelProperty(value = "国家ids")
    private List<Long> fkAreaCountryIds;
}
