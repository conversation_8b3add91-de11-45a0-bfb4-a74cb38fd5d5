package com.get.remindercenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.RemindTaskQueue;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface EmailSenderQueueMapper extends BaseMapper<EmailSenderQueue> {

    /**
     * @Description: 获取当前时间大于执行任务时间的数据
     * @Author: Jerry
     * @Date:14:34 2021/11/15
     */
    List<EmailSenderQueue> findDueEmailTasks(@Param("nowDate") Date nowDate);

    List<EmailSenderQueue> getEmailSenderQueues();
}
