package com.get.votingcenter.dao.appvoting;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.votingcenter.entity.UserVotingAward;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("appvoting")
public interface UserVotingAwardMapper extends BaseMapper<UserVotingAward> {

    int insertSelective(UserVotingAward record);
}