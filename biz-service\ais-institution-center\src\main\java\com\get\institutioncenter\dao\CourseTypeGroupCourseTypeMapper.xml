<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.CourseTypeGroupCourseTypeMapper">
  <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.CourseTypeGroupCourseType">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_course_type_group_id" jdbcType="BIGINT" property="fkCourseTypeGroupId" />
    <result column="fk_course_type_id" jdbcType="BIGINT" property="fkCourseTypeId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <sql id="Base_Column_List">
    id, fk_course_type_group_id, fk_course_type_id, gmt_create, gmt_create_user, gmt_modified, 
    gmt_modified_user
  </sql>
<!--  <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">-->
<!--    select -->
<!--    <include refid="Base_Column_List" />-->
<!--    from r_course_type_group_course_type-->
<!--    where id = #{id,jdbcType=BIGINT}-->
<!--  </select>-->
<!--  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">-->
<!--    delete from r_course_type_group_course_type-->
<!--    where id = #{id,jdbcType=BIGINT}-->
<!--  </delete>-->
  <insert id="insert" parameterType="com.get.institutioncenter.entity.CourseTypeGroupCourseType">
    insert into r_course_type_group_course_type (id, fk_course_type_group_id, fk_course_type_id, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkCourseTypeGroupId,jdbcType=BIGINT}, #{fkCourseTypeId,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.CourseTypeGroupCourseType">
    insert into r_course_type_group_course_type
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkCourseTypeGroupId != null">
        fk_course_type_group_id,
      </if>
      <if test="fkCourseTypeId != null">
        fk_course_type_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkCourseTypeGroupId != null">
        #{fkCourseTypeGroupId,jdbcType=BIGINT},
      </if>
      <if test="fkCourseTypeId != null">
        #{fkCourseTypeId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.get.institutioncenter.entity.CourseTypeGroupCourseType">
    update r_course_type_group_course_type
    <set>
      <if test="fkCourseTypeGroupId != null">
        fk_course_type_group_id = #{fkCourseTypeGroupId,jdbcType=BIGINT},
      </if>
      <if test="fkCourseTypeId != null">
        fk_course_type_id = #{fkCourseTypeId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.institutioncenter.entity.CourseTypeGroupCourseType">
    update r_course_type_group_course_type
    set fk_course_type_group_id = #{fkCourseTypeGroupId,jdbcType=BIGINT},
      fk_course_type_id = #{fkCourseTypeId,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="isExistByCourseTypeGroup" resultType="java.lang.Boolean">
    SELECT IFNULL(max(id),0) id from r_course_type_group_course_type where fk_course_type_group_id=#{fkCourseTypeGroupId}
  </select>
  <select id="isExistByCourseTypeId" resultType="java.lang.Boolean">
    SELECT IFNULL(max(id),0) id from r_course_type_group_course_type where fk_course_type_id=#{fkCourseTypeId}
  </select>
  <select id="getNamesByCourseTypeId" resultType="java.lang.String">
    SELECT
      GROUP_CONCAT( DISTINCT tg.type_group_name )
    FROM
      r_course_type_group_course_type ctg
        LEFT JOIN u_course_type_group tg ON ctg.fk_course_type_group_id = tg.id
    WHERE
      ctg.fk_course_type_id = #{id}
  </select>
</mapper>