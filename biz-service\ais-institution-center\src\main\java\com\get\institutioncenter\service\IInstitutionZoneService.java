package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.filecenter.dto.FileDto;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.vo.InstitutionZoneVo;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.entity.InstitutionZone;
import com.get.institutioncenter.dto.InstitutionZoneDto;
import com.get.institutioncenter.dto.query.InstitutionZoneQueryDto;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/8/3
 * @TIME: 14:40
 * @Description:校区业务类
 **/
public interface IInstitutionZoneService extends BaseService<InstitutionZone> {
    /**
     * 列表数据
     *
     * @param institutionZoneVo
     * @param page
     * @return
     */
    List<InstitutionZoneVo> datas(InstitutionZoneQueryDto institutionZoneVo, Page page);

    /**
     * 保存
     *
     * @param institutionZoneDto
     * @return
     */
    Long addInstitutionZone(InstitutionZoneDto institutionZoneDto);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    InstitutionZoneVo findInstitutionZoneById(Long id);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    FileDto upload(MultipartFile[] multipartFiles);


    /**
     * 修改
     *
     * @param institutionZoneDto
     * @return
     */
    InstitutionZoneVo updateInstitutionZone(InstitutionZoneDto institutionZoneDto);


    /**
     * String tableName = "m_institution_faculty";
     */

    List<MediaAndAttachedVo> addInstitutionZoneMedia(List<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * 获取学校下面的学院下拉框
     *
     * @param id
     * @return
     */
    List<InstitutionZoneVo> getByfkInstitutionId(Long id);

    /**
     * 获取学院附件类型
     *
     * @return
     */
    List<Map<String, Object>> findMediaAndAttachedType();

    /**
     * 查询学院附件
     *
     * @param data
     * @param page
     * @return
     * @
     */
    List<MediaAndAttachedVo> getInstitutionZoneMedia(MediaAndAttachedDto data, Page page);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.InstitutionZoneVo>
     * @Description :多个学校所有的校区下拉框数据
     * @Param [institutionIdList]
     * <AUTHOR>
     */
    List<InstitutionZoneVo> getInstitutionZoneSelectByInstitutionIdList(List<Long> institutionIdList);


    /**
     * feign 根据学校校区ids获取校区名字
     *
     * @Date 18:28 2022/2/18
     * <AUTHOR>
     */
    Map<Long, String> getInstitutionZoneNamesByIds(Set<Long> institutionZoneIdSet);
}
