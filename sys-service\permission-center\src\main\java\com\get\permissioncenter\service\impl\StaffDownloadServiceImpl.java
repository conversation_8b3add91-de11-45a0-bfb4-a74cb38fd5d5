package com.get.permissioncenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.permissioncenter.dao.StaffDownloadMapper;
import com.get.permissioncenter.dto.StaffDownloadDto;
import com.get.permissioncenter.entity.StaffDownload;
import com.get.permissioncenter.service.StaffDownloadService;
import com.get.permissioncenter.vo.StaffDownloadVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class StaffDownloadServiceImpl implements StaffDownloadService {

    @Resource
    private StaffDownloadMapper staffDownloadMapper;

    @Resource
    private IFileCenterClient fileCenterClient;

    @Override
    public Long addRecord(StaffDownload staffDownload) {
        staffDownloadMapper.insert(staffDownload);
        return staffDownload.getId();
    }

    @Override
    public void update(StaffDownload staffDownload) {
        staffDownloadMapper.updateInfo(staffDownload);
    }

    @Override
    public List<StaffDownloadVo> getDownloadList(SearchBean<StaffDownloadDto> page) {
        StaffDownloadDto data = page.getData();
        Long staffId = SecureUtil.getStaffId();
        data.setStaffId(staffId);
        IPage<StaffDownloadDto> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<StaffDownloadVo> lists = staffDownloadMapper.lists(iPage, data);
        page.setAll((int) iPage.getTotal());
        for (StaffDownloadVo list : lists) {
            switch (list.getStatus()) {
                case 0:
                    list.setStatusDescription(FileTypeEnum.D_FAILURE.value);
                    break;
                case 1:
                    list.setStatusDescription(FileTypeEnum.D_PROCESSING.value);
                    break;
                case 2:
                    list.setStatusDescription(FileTypeEnum.D_FINISHED.value);
                    break;
                default:
                    list.setStatusDescription(LocaleMessageUtils.getMessage("illegal_state"));
            }
            list.setOptKey(FileTypeEnum.getValue(FileTypeEnum.D_LIST_EXPORT.key));
            String description = list.getOptDescription();
            if (StringUtils.isNotBlank(description)) {
                //拼接后缀
                String substring = "";
                if (GeneralTool.isNotEmpty(list.getFileKey())){
                    String filKey = list.getFileKey();
                    int i = filKey.lastIndexOf(".");
                    substring = filKey.substring(i, filKey.length()).toLowerCase();
                }
                list.setFileName(description.substring(description.indexOf("《") + 1, description.lastIndexOf("》")) + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd-HH:mm:ss"))+substring);
            }
        }
        return lists;
    }


    @Override
    public List<Map<String, Object>> doGetSelection(FileTypeEnum[] fileTypeEnums) {
        if (fileTypeEnums.length == 0) {
            return Collections.emptyList();
        }
        return FileTypeEnum.enumsTranslation2Arrays(fileTypeEnums);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBo deleteRecord(Long id) {
        Long staffId = SecureUtil.getStaffId();
        StaffDownload download = staffDownloadMapper.selectOne(Wrappers.<StaffDownload>lambdaQuery().eq(StaffDownload::getFkStaffId,staffId).eq(StaffDownload::getId,id));
        if (download != null) {
            fileCenterClient.delete(download.getFkFileGuid(), LoggerModulesConsts.EXPORT);
            staffDownloadMapper.deleteById(id);
        }
        return DeleteResponseBo.ok();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBo deleteAll() {
        Long staffId = SecureUtil.getStaffId();
        List<StaffDownload> staffDownloads = staffDownloadMapper.selectList(Wrappers.<StaffDownload>lambdaQuery().eq(StaffDownload::getFkStaffId, staffId));
        if (!staffDownloads.isEmpty()) {
            staffDownloadMapper.delete(Wrappers.<StaffDownload>lambdaQuery().eq(StaffDownload::getFkStaffId,staffId));
            Set<String> guids = staffDownloads.stream().map(StaffDownload::getFkFileGuid).collect(Collectors.toSet());
            fileCenterClient.deleteAll(guids);
        }
        return DeleteResponseBo.ok();
    }
}
