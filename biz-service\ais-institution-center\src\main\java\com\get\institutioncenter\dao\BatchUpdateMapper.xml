<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.BatchUpdateMapper">
  <insert id = "sqlInsert">
    ${sql}
  </insert>

  <delete id="sqlDelete">
    ${sql}
  </delete>

  <update id="sqlUpdate">
    ${sql}
  </update>

  <select id="getBatchUpdates" resultType="java.util.LinkedHashMap">
    ${sql1}
    from
    (select * from ${tableName} where id in
    <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
    ) a
    LEFT JOIN
    ${sql2}
  </select>
</mapper>