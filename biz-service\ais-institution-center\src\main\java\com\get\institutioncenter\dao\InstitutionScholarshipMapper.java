package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.institutioncenter.dto.WeScholarshipAppDto;
import com.get.institutioncenter.vo.InstitutionScholarshipVo;
import com.get.institutioncenter.entity.InstitutionScholarship;
import com.get.institutioncenter.dto.query.InstitutionScholarshipQueryDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface InstitutionScholarshipMapper extends BaseMapper<InstitutionScholarship> {

    int insertSelective(InstitutionScholarship record);


    List<InstitutionScholarshipVo> getWcInstitutionScholarshipList(IPage<InstitutionScholarshipVo> ipage,
                                                                   @Param("schoolName") String schoolName,
                                                                   @Param("fkCountryId") Long fkCountryId);

    InstitutionScholarshipVo getIsOtherModule(@Param("fkInstitutionId") Long fkInstitutionId);

    /**
     * 获取奖学金列表信息
     * @param pages
     * @param data
     * @return
     */
    List<InstitutionScholarshipVo> datas(IPage<InstitutionScholarship> pages, @Param("data") InstitutionScholarshipQueryDto data, @Param("countryIds") List<Long> countryIds);

    /**
     * 小程序奖学金学校详情
     * @param fkTypeKey
     * @param fkTableId
     * @return
     */
    List<InstitutionScholarshipVo> wechatDatas(@Param("fkTypeKey") String fkTypeKey, @Param("fkTableId") Long fkTableId);


    InstitutionScholarshipVo selectInfoById(Long id);

    List<InstitutionScholarshipVo> selectInfoByIds(List<Long> ids);

    List<InstitutionScholarshipVo> getWcInstitutionScholarshipDatas(@Param("weScholarshipAppDto") WeScholarshipAppDto weScholarshipAppDto, @Param("fkTableName")String fkTableName);

    /**
     * 奖学金优先匹配
     * @param weScholarshipAppDto
     * @param priorityTypeKey
     * @param fkTableName
     * @return
     */
    List<InstitutionScholarshipVo> priorityMatchingQuery(@Param("weScholarshipAppDto") WeScholarshipAppDto weScholarshipAppDto,
                                                         @Param("priorityTypeKey") Map<Integer,String> priorityTypeKey, @Param("fkTableName")String fkTableName);
}