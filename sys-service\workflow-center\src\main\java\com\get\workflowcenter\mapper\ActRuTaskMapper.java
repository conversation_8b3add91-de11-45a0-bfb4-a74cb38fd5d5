package com.get.workflowcenter.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.workflowcenter.dto.ApprovalRecordDto;
import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.entity.ActRuTask;
import com.get.workflowcenter.vo.ApprovalRecordListVo;
import com.get.workflowcenter.vo.AssigneeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ActRuTaskMapper extends BaseMapper<ActRuTask> {


    int insertSelective(ActRuTask record);

    List<ActRuTaskVo> getToDoSelectList(@Param("category") String category, @Param("name") String name);

    List<ActRuTaskVo> getToDoSelectListByNormalUser(IPage<ActRuTaskVo> page,
                                                    @Param("category") String category, @Param("name") String name,
                                                    @Param("assignee") String assignee, @Param("staffIdByNames") List<Long> staffIdByNames);

    List<ActRuTaskVo> getSignedList(@Param("category") String category, @Param("name") String name);

    List<ActRuTaskVo> getSignedListByNormalUser(IPage<ActRuTaskVo> page,
                                                @Param("category") String category, @Param("name") String name,
                                                @Param("userid") String userid, @Param("staffIdByNames") List<Long> staffIdByNames);

    ActRuTaskVo getTaskVersionByProcessInId(@Param("pinId") String pinId);

    ActRuTaskVo comparisonVersion(@Param("taskId") String taskId, @Param("version") Integer version);

    /**
     * @return java.lang.String
     * @Description :根据任务id查找版本
     * @Param [taskId]
     * <AUTHOR>
     */
    Integer getVersionById(@Param("taskId") String taskId);

    /**
     * 审批列表
     *
     * @param iPage
     * @param approvalRecordDto
     * @param staffId
     * @return
     */
    List<ApprovalRecordListVo> getApprovalRecordsList(IPage<ApprovalRecordListVo> iPage, @Param("approvalRecordDto") ApprovalRecordDto approvalRecordDto, @Param("staffId") Long staffId);

    /**
     * 获取当前审批人信息
     * @param tableName
     * @param formIds
     * @return
     */
    List<AssigneeVo> getAssigneeInfo(@Param("tableName") String tableName, @Param("formIds") List<Long> formIds);

}