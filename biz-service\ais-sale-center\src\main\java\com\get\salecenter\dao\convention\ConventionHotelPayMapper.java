package com.get.salecenter.dao.convention;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.entity.ConventionHotelPay;
import com.get.salecenter.vo.ConventionPersonPayTypeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS("conventiondb")
public interface ConventionHotelPayMapper extends GetMapper<ConventionHotelPay> {

    /**
     * 根据峰会人员ID集合获取酒店支付信息
     * @param conventionPersonIds
     * @return
     */
    List<ConventionPersonPayTypeVo> getPayTypeByConventionPersonIds(@Param("conventionPersonIds") List<Long> conventionPersonIds);


}