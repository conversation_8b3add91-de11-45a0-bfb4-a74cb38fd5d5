package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.salecenter.dto.StudentOfferItemPaymentStatusDto;
import com.get.salecenter.dto.StudentOfferItemPaymentStatusToAppDto;
import com.get.salecenter.entity.StudentOfferItemPaymentStatus;
import com.get.salecenter.dto.StudentOfferItemPaymentStatusRemarkDto;
import com.get.salecenter.dto.StudentOfferItemPaymentStatusRemarkDto;
import com.get.salecenter.vo.StudentOfferItemPaymentStatusVo;

import java.util.List;

public interface IStudentOfferItemPaymentStatusService extends IService<StudentOfferItemPaymentStatus> {

    /**
     * 代付费用日志列表
     *
     * @param vo   查询参数
     * @param page 分页参数
     * @return
     */
    List<StudentOfferItemPaymentStatusVo> datas(StudentOfferItemPaymentStatusDto vo, Page page);

    /**
     * 新增代付费用日志
     *
     * @param studentOfferItemPaymentStatusDto 新增代付费用日志参数
     */
    void add(StudentOfferItemPaymentStatusDto studentOfferItemPaymentStatusDto);

    /**
     * 代付费用日志详情
     *
     * @param id 代付费用日志id
     * @return
     */
    StudentOfferItemPaymentStatusVo detail(Long id);

    /**
     * 修改代付费用日志备注
     *
     * @param studentOfferItemPaymentStatusRemarkVo 修改代付费用日志备注参数
     * @return
     */
    StudentOfferItemPaymentStatusVo updateRemark(StudentOfferItemPaymentStatusRemarkDto studentOfferItemPaymentStatusRemarkVo);

    /**
     * 更新申请计划的支付状态，并插入代付日志, 并且发送邮件通知给项目成员
     *
     * @param studentOfferItemPaymentStatusToAppVo 参数
     */
    void updatePaymentStatusToApp(StudentOfferItemPaymentStatusToAppDto studentOfferItemPaymentStatusToAppVo);
}
