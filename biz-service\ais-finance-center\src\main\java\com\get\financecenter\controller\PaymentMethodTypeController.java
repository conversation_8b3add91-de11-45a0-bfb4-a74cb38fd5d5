package com.get.financecenter.controller;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.financecenter.dto.PaymentMethodTypeDto;
import com.get.financecenter.service.PaymentMethodTypeService;
import com.get.financecenter.vo.PaymentMethodTypeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 付款方式类型管理
 */
@Api(tags = "付款方式类型管理")
@RestController
@RequestMapping("finance/paymentMethodType")
public class PaymentMethodTypeController {
    /**
     * 服务对象
     */
    @Resource
    private PaymentMethodTypeService paymentMethodTypeService;

    @ApiOperation(value = "分页查询所有数据",notes = "分页查询所有数据")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/付款方式类型管理/查询")
    @PostMapping("list")
    public ResponseBo<PaymentMethodTypeVo> selectAll(@RequestBody SearchBean<PaymentMethodTypeDto> page) {
        List<PaymentMethodTypeVo> datas = paymentMethodTypeService.getPaymentMethodTypes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "付款方式类型新增", notes = "批量新增")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/付款方式类型管理/批量新增")
    @PostMapping("add")
    public ResponseBo  addPaymentMethodType(@RequestBody @Validated List<PaymentMethodTypeDto> paymentMethodTypeDtos) {
        paymentMethodTypeService.batchAddPaymentMethodType(paymentMethodTypeDtos);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "付款方式类型修改", notes = "付款方式类型id: paymentMethodTypeId")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/付款方式类型管理/修改")
    @PostMapping("update")
    public ResponseBo updatePaymentMethodType(@RequestBody PaymentMethodTypeDto paymentMethodTypeDto) {
        paymentMethodTypeService.updatePaymentMethodType(paymentMethodTypeDto);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "付款方式类型删除", notes = "付款方式类型id: paymentMethodTypeId")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/付款方式类型管理/删除")
    @GetMapping("delete")
    public ResponseBo deletePaymentMethodType(@RequestParam("id") Long id) {
        paymentMethodTypeService.deletePaymentMethodType(id);
        return ResponseBo.ok();
    }

//    @ApiOperation(value = "排序（交换）", notes = "排序接口")
//    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/付款方式类型管理/排序")
//    @PostMapping("sort")
//    public ResponseBo sort(@RequestBody List<Long> ids) {
//        paymentMethodTypeService.sort(ids);
//        return ResponseBo.ok();
//    }

    @ApiOperation(value = "排序（拖拽）", notes = "排序接口")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/付款方式类型管理/排序")
    @PostMapping("sort")
    public ResponseBo sort(@RequestParam("start") Integer start , @RequestParam("end") Integer end) {
        paymentMethodTypeService.movingOrder(start, end);
        return ResponseBo.ok();
    }

}

