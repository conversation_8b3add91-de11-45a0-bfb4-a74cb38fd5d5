package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.InstitutionCourseMapper;
import com.get.institutioncenter.dao.InstitutionCoursePathwayMapper;
import com.get.institutioncenter.dto.InstitutionCoursePathwayDto;
import com.get.institutioncenter.vo.InstitutionCourseVo;
import com.get.institutioncenter.vo.InstitutionCoursePathwayVo;
import com.get.institutioncenter.entity.InstitutionCourse;
import com.get.institutioncenter.entity.InstitutionCoursePathway;
import com.get.institutioncenter.service.IInstitutionCoursePathwayService;
import com.get.institutioncenter.service.IInstitutionCourseService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/12/18
 * @TIME: 16:22
 * @Description:
 **/
@Service
public class InstitutionCoursePathwayServiceImpl extends BaseServiceImpl<InstitutionCoursePathwayMapper, InstitutionCoursePathway> implements IInstitutionCoursePathwayService {
    @Resource
    private InstitutionCoursePathwayMapper institutionCoursePathwayMapper;
    @Resource
    private IInstitutionCourseService institutionCourseService;
    @Resource
    private UtilService utilService;
    @Resource
    private InstitutionCourseMapper institutionCourseMapper;

    @Override
    public List<InstitutionCoursePathwayVo> datas(InstitutionCoursePathwayDto institutionCoursePathwayDto, Page page) {
        //获取分页数据
        List<InstitutionCoursePathway> institutionCoursePathways = institutionCoursePathwayMapper.selectInstitutionCoursePathway(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), institutionCoursePathwayDto.getFkInstitutionCourseId(),
                institutionCoursePathwayDto.getFkInstitutionCourseIdPathway());
        List<InstitutionCoursePathwayVo> convertDatas = new ArrayList<>();
        for (InstitutionCoursePathway institutionCoursePathway : institutionCoursePathways) {
            InstitutionCourseVo institutionCourseVo = institutionCourseService.findInstitutionCourseById(
                    GeneralTool.isNotEmpty(institutionCoursePathwayDto.getFkInstitutionCourseIdPathway()) ?
                            institutionCoursePathway.getFkInstitutionCourseId() : institutionCoursePathway.getFkInstitutionCourseIdPathway());
            InstitutionCoursePathwayVo institutionCoursePathwayVo = new InstitutionCoursePathwayVo();
            institutionCoursePathwayVo.setInstitutionCourseVo(institutionCourseVo);
            institutionCoursePathwayVo.setId(institutionCoursePathway.getId());
            institutionCoursePathwayVo.setReqAcademic1(institutionCoursePathway.getReqAcademic1());
            institutionCoursePathwayVo.setReqAcademic2(institutionCoursePathway.getReqAcademic2());
            institutionCoursePathwayVo.setReqEng1(institutionCoursePathway.getReqEng1());
            institutionCoursePathwayVo.setReqEng2(institutionCoursePathway.getReqEng2());
            institutionCoursePathwayVo.setReqParticularSubject(institutionCoursePathway.getReqParticularSubject());
            convertDatas.add(institutionCoursePathwayVo);
        }
        return convertDatas;
    }

    @Override
    public InstitutionCoursePathwayVo findInstitutionCoursePathwayById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionCoursePathway institutionCoursePathway = institutionCoursePathwayMapper.selectById(id);
        if (GeneralTool.isEmpty(institutionCoursePathway)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionCoursePathwayVo institutionCoursePathwayVo = BeanCopyUtils.objClone(institutionCoursePathway, InstitutionCoursePathwayVo::new);
        InstitutionCourseVo institutionCourseVo = institutionCourseService.findInstitutionCourseById(
                GeneralTool.isNotEmpty(institutionCoursePathway.getFkInstitutionCourseIdPathway()) ?
                        institutionCoursePathway.getFkInstitutionCourseId() : institutionCoursePathway.getFkInstitutionCourseIdPathway());
        institutionCoursePathwayVo.setInstitutionCourseVo(institutionCourseVo);

        InstitutionCourse ic = institutionCourseMapper.selectById(institutionCoursePathwayVo.getFkInstitutionCourseId());
        if (GeneralTool.isNotEmpty(ic)) {
            institutionCoursePathwayVo.setFkInstitutionId(ic.getFkInstitutionId());
        }
        InstitutionCourse icp = institutionCourseMapper.selectById(institutionCoursePathway.getFkInstitutionCourseIdPathway());
        if (GeneralTool.isNotEmpty(icp)) {
            institutionCoursePathwayVo.setFkInstitutionIdPathway(icp.getFkInstitutionId());
        }
        return institutionCoursePathwayVo;
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (institutionCoursePathwayMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        institutionCoursePathwayMapper.deleteById(id);
    }

    @Override
    public void update(InstitutionCoursePathwayDto institutionCoursePathwayDto) {
//        if (GeneralTool.isEmpty(institutionCoursePathwayDto.getFkInstitutionCourseId())) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("courseId_null"));
//        }
//        if (GeneralTool.isEmpty(institutionCoursePathwayDto.getFkCourseIdPathways())) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("bridge_courseId_null"));
//        }
//        for (Long id : institutionCoursePathwayDto.getFkCourseIdPathways()) {
//            InstitutionCoursePathway institutionCoursePathway = new InstitutionCoursePathway();
//            institutionCoursePathway.setFkInstitutionCourseId(institutionCoursePathwayDto.getFkInstitutionCourseId());
//            institutionCoursePathway.setFkInstitutionCourseIdPathway(id);
//            utilService.updateUserInfoToEntity(institutionCoursePathway);
//            institutionCoursePathwayMapper.insertSelective(institutionCoursePathway);
//        }
        InstitutionCoursePathway institutionCoursePathway = new InstitutionCoursePathway();
        institutionCoursePathway.setFkInstitutionCourseId(institutionCoursePathwayDto.getFkInstitutionCourseId());
        institutionCoursePathway.setFkInstitutionCourseIdPathway(institutionCoursePathwayDto.getFkInstitutionCourseIdPathway());
        institutionCoursePathway.setReqAcademic1(institutionCoursePathwayDto.getReqAcademic1());
        institutionCoursePathway.setReqAcademic2(institutionCoursePathwayDto.getReqAcademic2());
        institutionCoursePathway.setReqEng1(institutionCoursePathwayDto.getReqEng1());
        institutionCoursePathway.setReqEng2(institutionCoursePathwayDto.getReqEng2());
        institutionCoursePathway.setReqParticularSubject(institutionCoursePathwayDto.getReqParticularSubject());
        utilService.updateUserInfoToEntity(institutionCoursePathway);
        institutionCoursePathwayMapper.insertSelective(institutionCoursePathway);
    }

    @Override
    public void updateInstitutionCoursePathway(InstitutionCoursePathwayDto institutionCoursePathwayDto) {
        InstitutionCoursePathway institutionCoursePathway = BeanCopyUtils.objClone(institutionCoursePathwayDto, InstitutionCoursePathway::new);
        utilService.updateUserInfoToEntity(institutionCoursePathway);
        institutionCoursePathwayMapper.updateById(institutionCoursePathway);
    }

    /**
     * 反向绑定非桥梁课程
     *
     * @Date 17:55 2021/7/22
     * <AUTHOR>
     */
//    @Override
//    public void pathwayUpdate(InstitutionCoursePathwayDto institutionCoursePathwayVo)  {
//        if (GeneralTool.isEmpty(institutionCoursePathwayVo.getFkCourseIds())) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("courseId_null"));
//        }
//        if (GeneralTool.isEmpty(institutionCoursePathwayVo.getFkInstitutionCourseIdPathway())) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("bridge_courseId_null"));
//        }
//        InstitutionCoursePathway institutionCoursePathway = new InstitutionCoursePathway();
//        institutionCoursePathway.setFkInstitutionCourseId(institutionCoursePathwayVo.getFkInstitutionCourseId());
//        institutionCoursePathway.setFkInstitutionCourseIdPathway(institutionCoursePathwayVo.getFkInstitutionCourseIdPathway());
//        institutionCoursePathway.setReqAcademic1(institutionCoursePathwayVo.getReqAcademic1());
//        institutionCoursePathway.setReqAcademic2(institutionCoursePathwayVo.getReqAcademic2());
//        institutionCoursePathway.setReqEng1(institutionCoursePathwayVo.getReqEng1());
//        institutionCoursePathway.setReqEng2(institutionCoursePathwayVo.getReqEng2());
//        institutionCoursePathway.setReqParticularSubject(institutionCoursePathwayVo.getReqParticularSubject());
//        utilService.updateUserInfoToEntity(institutionCoursePathway);
//        institutionCoursePathwayMapper.insertSelective(institutionCoursePathway);
//
//    }
    @Override
    public void deleteCoursePathwayByCourseId(Long id) {
        LambdaQueryWrapper<InstitutionCoursePathway> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionCoursePathway::getFkInstitutionCourseId, id);
        int j = institutionCoursePathwayMapper.delete(wrapper);
        if (j < 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Override
    public void deleteCoursePathwayByCoursePathwayId(Long id) {
        LambdaQueryWrapper<InstitutionCoursePathway> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionCoursePathway::getFkInstitutionCourseIdPathway, id);
        int j = institutionCoursePathwayMapper.delete(wrapper);
        if (j < 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }
}
