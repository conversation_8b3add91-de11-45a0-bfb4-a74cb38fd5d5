package com.get.financecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class OccVo {

    private Integer billtype;

    private String courseid;

    private BigDecimal tuitionamount;

    private BigDecimal receivablecommissionrate;

    private BigDecimal receivablefixedamount;

    private BigDecimal payablecommissionrate;

    private BigDecimal payablefixedamount;

    private BigDecimal arrangement;

    private Long id;

    private String name;

    private String agencyname;

    private Long agentid;

    private String currency;

    private String bankaccount;

    private String bankaccountnum;

    private String bankbranchname;

    private String bankaccountname;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createtime;

    private BigDecimal amountreceivable;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtreceivablecreatetime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtreceivedcreatetime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtpayablecreatetime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtpaidcreatetime;

    private BigDecimal receivedamount;

    private BigDecimal feesreceived;

    private BigDecimal payableamount;

    private BigDecimal fixedpayableamount;

    private BigDecimal paidamount;

    private BigDecimal feespaid;

    private String type;
}
