package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/7/4
 * @TIME: 10:08
 * @Description:
 **/
@Data
public class StudentApplicationStatisticsDto {
    /**
     * 公司ID
     */
    @ApiModelProperty(value = "公司ID")
    private Long fkCompanyId;

    /**
     * 公司ID列表
     */
    @ApiModelProperty(value = "公司列表")
    private List<Long> fkCompanyIdList;

//    /**
//     * 查询类型:1-周报统计;2-月报统计;3-季报统计;4-年报统计
//     */
//    @ApiModelProperty(value = "查询类型:1-周报统计;2-月报统计;3-季报统计;4-年报统计")
//    private Integer selectType;

    /**
     * 大区Id
     */
    @ApiModelProperty(value = "大区Id")
    private Long fkAreaRegionId;
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

    /**
     * bd名称
     */
    @ApiModelProperty(value = "bd名称")
    private String bdName;

    /**
     * 国家ID列表，搜索过滤条件
     */
    @ApiModelProperty(value = "国家ID列表,搜索过滤条件")
    private List<Long> fkAreaCountryIds;

    /**
     * 国家ID列表,员工（登录人）业务国家
     */
    @ApiModelProperty(value = "国家ID列表,员工（登录人）业务国家")
    private List<Long> fkAreaCountryIdList;

    /**
     * 员工以及旗下员工所创建的代理ids
     */
    @ApiModelProperty(value = "员工以及旗下员工所创建的代理ids")
    private List<Long> staffFollowerIds;

    /**
     * 所属集团Id
     */
    @ApiModelProperty(value = "所属集团Id")
    private List<Long> fkInstitutionGroupIds;
}
