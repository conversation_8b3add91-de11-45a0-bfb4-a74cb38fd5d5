package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dto.InstitutionProviderDto;
import com.get.institutioncenter.vo.EventRegistrationListResultVo;
import com.get.institutioncenter.vo.InstitutionProviderVo;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.salecenter.dao.sale.EventRegistrationMapper;
import com.get.salecenter.entity.Event;
import com.get.salecenter.entity.EventRegistration;
import com.get.salecenter.service.IEventRegistrationService;
import com.get.salecenter.service.IEventService;
import com.get.salecenter.dto.EventRegistrationDto;
import com.get.salecenter.dto.EventRegistrationStatusDto;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2022/9/13 16:28
 * @verison: 1.0
 * @description:
 */
@Service
public class EventRegistrationServiceImpl extends GetServiceImpl<EventRegistrationMapper, EventRegistration> implements IEventRegistrationService{

    @Resource
    private EventRegistrationMapper eventRegistrationMapper;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private UtilService utilService;
    @Lazy
    @Resource
    private IEventService eventService;


    @Override
    public List<InstitutionProviderVo> getEventRegistrations(EventRegistrationDto eventRegistrationDto, Page page) {

        if (GeneralTool.isEmpty(eventRegistrationDto)&&GeneralTool.isEmpty(eventRegistrationDto.getFkEventId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }

        LambdaQueryWrapper<EventRegistration> wrapper = Wrappers.<EventRegistration>lambdaQuery();
        wrapper.eq(EventRegistration::getFkEventId, eventRegistrationDto.getFkEventId());
        IPage<EventRegistration> iPage = eventRegistrationMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<EventRegistration> eventRegistrations = iPage.getRecords();
        if (GeneralTool.isEmpty(eventRegistrations)){
            return Collections.emptyList();
        }

        List<Long> providerIds = eventRegistrations.stream().map(EventRegistration::getFkInstitutionProviderId).filter(Objects::nonNull).collect(Collectors.toList());
        Result<List<InstitutionProviderVo>> result = institutionCenterClient.getEventRegistrationProviderByIds(providerIds);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())){
            HashMap<Long, InstitutionProviderVo> map = Maps.newHashMap();
            List<InstitutionProviderVo> datas = result.getData();
            for (InstitutionProviderVo data : datas) {
                map.put(data.getId(),data);
            }
            List<InstitutionProviderVo> institutionProviderVos = new ArrayList<>();
            for (EventRegistration eventRegistration : eventRegistrations) {
                if (GeneralTool.isNotEmpty(map)&&GeneralTool.isNotEmpty(map.get(eventRegistration.getFkInstitutionProviderId()))){
                    InstitutionProviderVo institutionProviderVo = BeanCopyUtils.objClone(map.get(eventRegistration.getFkInstitutionProviderId()), InstitutionProviderVo::new);
                    institutionProviderVo.setId(eventRegistration.getId());
                    institutionProviderVo.setRemark(eventRegistration.getRemark());
                    institutionProviderVo.setEventRegistrationStatus(eventRegistration.getStatus());
                    institutionProviderVo.setFkInstitutionProviderId(eventRegistration.getFkInstitutionProviderId());
                    institutionProviderVos.add(institutionProviderVo);
                }
            }
            return institutionProviderVos;
        }else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_fail"));
        }
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (eventRegistrationMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        int i = eventRegistrationMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Override
    public ResponseBo editRemark(Long id, String remark) {
        EventRegistration eventRegistration = eventRegistrationMapper.selectById(id);
        if (eventRegistration==null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_false"));
        }
        eventRegistration.setRemark(remark);
        eventRegistrationMapper.updateById(eventRegistration);
        return SaveResponseBo.ok();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editEventRegistration(ValidList<EventRegistrationDto> eventRegistrationDtos) {
        if (GeneralTool.isEmpty(eventRegistrationDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
//        Long fkEventId = eventRegistrationDtos.get(0).getFkEventId();
//        int i = eventRegistrationMapper.delete(Wrappers.<EventRegistration>lambdaQuery().eq(EventRegistration::getFkEventId, fkEventId));
//        if (i < 0) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
//        }
        for (EventRegistrationDto eventRegistrationDto : eventRegistrationDtos) {
            eventRegistrationDto.setStatus(1);
        }
        List<EventRegistration> eventRegistrations = BeanCopyUtils.copyListProperties(eventRegistrationDtos, EventRegistration::new);

        boolean b = saveBatch(eventRegistrations);
        if (!b){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Override
    public ResponseBo<InstitutionProviderVo> getEventRegistrationProviders(SearchBean<InstitutionProviderDto> page) {
        InstitutionProviderDto institutionProviderDto = page.getData();
        if (GeneralTool.isEmpty(institutionProviderDto)&&GeneralTool.isEmpty(institutionProviderDto.getFkEventId())
                &&GeneralTool.isEmpty(institutionProviderDto.getFkCompanyId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        LambdaQueryWrapper<EventRegistration> lambdaQuery = Wrappers.<EventRegistration>lambdaQuery();
        lambdaQuery.eq(EventRegistration::getFkEventId, institutionProviderDto.getFkEventId());
        List<EventRegistration> eventRegistrations = eventRegistrationMapper.selectList(lambdaQuery);
//        if (GeneralTool.isEmpty(eventRegistrations)){
//            return new ArrayList<>();
//        }
        List<Long> providerIds = eventRegistrations.stream().map(EventRegistration::getFkInstitutionProviderId).filter(Objects::nonNull).collect(Collectors.toList());
        page.getData().setProviderIds(providerIds);
//        institutionCenterClient.getEventRegistrationProviders(page);
//        return responseBo;
        Result<EventRegistrationListResultVo> result = institutionCenterClient.getEventRegistrationProviders(page);
        if (result.isSuccess()&&GeneralTool.isNotEmpty(result.getData())){
            EventRegistrationListResultVo data = result.getData();
//            ListResponseBo listResponseBo = JSONObject.parseObject(data, ListResponseBo.class);
            return new ListResponseBo<>(data.getInstitutionProviderDtos(), data.getPage());
        }else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_fail"));
        }
    }

    /**
     * @author: Neil
     * @description: 批量修改活动名册状态
     * @date: 2022/12/6 14:23
     */
    @Override
    public void editEventRegistrationStatus(EventRegistrationStatusDto eventRegistrationStatusDto) {
        if (GeneralTool.isEmpty(eventRegistrationStatusDto) || GeneralTool.isEmpty(eventRegistrationStatusDto.getFkEventIds())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        List<Long> Ids = Lists.newArrayList(eventRegistrationStatusDto.getFkEventIds());
        Long eventRegistrationId = Ids.get(0);
        if (GeneralTool.isNotEmpty(eventRegistrationId)){
            EventRegistration registration = getById(eventRegistrationId);
            Long fkEventId = registration.getFkEventId();
            Event event = eventService.getById(fkEventId);
            if (ProjectExtraEnum.EVENT_POSTPONE.key.equals(event.getStatus())
                    &&(eventRegistrationStatusDto.getStatus()==0|| eventRegistrationStatusDto.getStatus()==1)){
                throw new GetServiceException(LocaleMessageUtils.getMessage("can_not_set_participate_or_not_participate"));
            }
        }

        LambdaQueryWrapper<EventRegistration> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(EventRegistration::getId, eventRegistrationStatusDto.getFkEventIds());
        EventRegistration eventRegistration = new EventRegistration();
        eventRegistration.setStatus(eventRegistrationStatusDto.getStatus());
        utilService.setUpdateInfo(eventRegistration);
        eventRegistrationMapper.update(eventRegistration,lambdaQueryWrapper);
    }


}
