package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName(value = "u_business_provider_type")
public class BusinessProviderType extends BaseEntity implements Serializable {

    @ApiModelProperty("业务提供商类型名称")
    private String typeName;

    @ApiModelProperty("排序，倒序：数字由大到小排列")
    private Integer viewOrder;
}
