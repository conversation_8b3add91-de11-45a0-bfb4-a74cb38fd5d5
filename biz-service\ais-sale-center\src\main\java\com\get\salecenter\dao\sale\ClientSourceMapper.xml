<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ClientSourceMapper">

    <select id="getClientSources" resultType="com.get.salecenter.vo.ClientSourceListVo">
        SELECT
            a.*,
            b.num as agentNum
        FROM
            `s_client_source` a
                LEFT JOIN m_agent b on a.fk_agent_id = b.id
        where 1=1
        <if test="clientSourceListDto.fkClientId !=null">
            and a.fk_client_id = #{clientSourceListDto.fkClientId}
        </if>
        <if test="clientSourceListDto.fkTableName !=null and clientSourceListDto.fkTableName !=''">
            and a.fk_table_name = #{clientSourceListDto.fkTableName}
        </if>
        <if test="clientSourceListDto.fkTableValue !=null and clientSourceListDto.fkTableValue !=''">
            and a.fk_table_value = #{clientSourceListDto.fkTableValue}
        </if>
    </select>
</mapper>