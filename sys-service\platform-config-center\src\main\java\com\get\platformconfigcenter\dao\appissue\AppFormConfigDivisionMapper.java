package com.get.platformconfigcenter.dao.appissue;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.AppFormConfigDivision;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("issuedb")
public interface AppFormConfigDivisionMapper extends BaseMapper<AppFormConfigDivision> {

//    int insert(AppFormConfigDivision record);
//
//    int insertSelective(AppFormConfigDivision record);
//
//    int updateByPrimaryKeySelective(AppFormConfigDivision record);
//
//    int updateByPrimaryKey(AppFormConfigDivision record);
}