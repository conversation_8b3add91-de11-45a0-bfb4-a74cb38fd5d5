package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.InstitutionProviderInstitutionChannelMapper;
import com.get.institutioncenter.entity.InstitutionProviderInstitutionChannel;
import com.get.institutioncenter.service.IInstitutionProviderInstitutionChannelService;
import com.get.institutioncenter.dto.InstitutionProviderInstitutionChannelDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2021/9/18 15:27
 * @verison: 1.0
 * @description:
 */
@Service
public class InstitutionProviderInstitutionChannelServiceImpl extends BaseServiceImpl<InstitutionProviderInstitutionChannelMapper, InstitutionProviderInstitutionChannel> implements IInstitutionProviderInstitutionChannelService {

    @Resource
    private InstitutionProviderInstitutionChannelMapper institutionProviderInstitutionChannelMapper;
    @Resource
    private UtilService utilService;

    /**
     * 新增
     *
     * @param institutionProviderInstitutionChannelDto
     * @return
     */
    @Override
    public Long addInstitutionProviderInstitutionChannel(InstitutionProviderInstitutionChannelDto institutionProviderInstitutionChannelDto) {
        if (GeneralTool.isEmpty(institutionProviderInstitutionChannelDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        InstitutionProviderInstitutionChannel institutionProviderInstitutionChannel = BeanCopyUtils.objClone(institutionProviderInstitutionChannelDto, InstitutionProviderInstitutionChannel::new);
        utilService.updateUserInfoToEntity(institutionProviderInstitutionChannel);
        institutionProviderInstitutionChannelMapper.insertSelective(institutionProviderInstitutionChannel);
        return institutionProviderInstitutionChannel.getId();
    }

    /**
     * 删除关系表
     *
     * @param id
     */
    @Override
    public void deleteByProviderId(Long id) {
        LambdaQueryWrapper<InstitutionProviderInstitutionChannel> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionProviderInstitutionChannel::getFkInstitutionProviderId, id);
        institutionProviderInstitutionChannelMapper.delete(wrapper);
    }

    /**
     * 获取渠道名称
     *
     * @param id
     * @return
     */
    @Override
    public String getInstitutionChannelNamesById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        List<Long> companyIds = SecureUtil.getCompanyIds();
        return institutionProviderInstitutionChannelMapper.getInstitutionChannelNamesById(id,companyIds);
    }

    /**
     * 获取渠道ids
     *
     * @param id
     * @return
     * @
     */
    @Override
    public List<Long> getInstitutionChannelIdsById(Long id) {
        LambdaQueryWrapper<InstitutionProviderInstitutionChannel> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionProviderInstitutionChannel::getFkInstitutionProviderId, id);
        List<InstitutionProviderInstitutionChannel> institutionProviderInstitutionChannels = institutionProviderInstitutionChannelMapper.selectList(wrapper);
        return institutionProviderInstitutionChannels.stream().map(InstitutionProviderInstitutionChannel::getFkInstitutionChannelId).collect(Collectors.toList());
    }

    /**
     * 获取提供商Ids
     * @param fkInstitutionChannelId
     * @return
     */
    @Override
    public List<Long> getInstitutionProviderIdsByChannelId(Long fkInstitutionChannelId){
        if(GeneralTool.isEmpty(fkInstitutionChannelId)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        LambdaQueryWrapper<InstitutionProviderInstitutionChannel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InstitutionProviderInstitutionChannel::getFkInstitutionChannelId, fkInstitutionChannelId);
        List<InstitutionProviderInstitutionChannel> institutionProviderInstitutionChannels = institutionProviderInstitutionChannelMapper.selectList(wrapper);
        return institutionProviderInstitutionChannels.stream().map(InstitutionProviderInstitutionChannel::getFkInstitutionProviderId).collect(Collectors.toList());

    }
}
