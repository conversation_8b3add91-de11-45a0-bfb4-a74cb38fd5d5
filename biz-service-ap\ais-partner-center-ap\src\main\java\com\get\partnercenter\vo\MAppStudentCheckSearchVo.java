package com.get.partnercenter.vo;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class MAppStudentCheckSearchVo {
    @ApiModelProperty("代理名字")
    private String agentName;

    private String agentPersonalName;
    @ApiModelProperty("草稿箱子中的学生ID")
    private Long appStudentId;
    @ApiModelProperty("学生中文名称")
    private String studentName;
    @ApiModelProperty("姓（英/拼音）")
    private String lastName;
    @ApiModelProperty("名（英/拼音）")
    private String firstName;

    @ApiModelProperty("毕业院校")
    private String fkInstitutionNameEducation;
    @ApiModelProperty("毕业院校国际")
    private String fkInstitutionNameEducation2;

    @ApiModelProperty("生日")
    private LocalDate birthday;

    @ApiModelProperty("状态:-1已驳回/0未提交(草稿)/1待审核/2已通过")
    private Integer status;

    private LocalDateTime gmtCreate;
    @ApiModelProperty("创建人")
    private String gmtCreateUser;
    @ApiModelProperty("创建人历史")
    private String gmtCreateUserhis;

    private LocalDateTime gmtModified;
    @ApiModelProperty("审核人")
    private String gmtModifiedUser;



    @ApiModelProperty("审批类型:0学生审批/1 加申审批")
    private Integer isAddItemStatus=0;
    @ApiModelProperty("加申ITEM明细数量")
    private int   jiashenCheckItemNUm;

    @ApiModelProperty("申请计划加申状态:0审核中/1完成")
    private Integer offerItemStatus=1;

    @ApiModelProperty("待申审核ITEM明细数量")
    private int   checkItemNUm;


    public String getGmtCreateUser() {
        if(StringUtils.isNotBlank(gmtCreateUserhis)){
            gmtCreateUser=gmtCreateUserhis;

        }
        return gmtCreateUser;
    }

    public String getGmtModifiedUser() {
        if(status!=null && status.intValue()==2){

        }else{
            gmtModifiedUser="";
        }

        return gmtModifiedUser;
    }


    public Integer getIsAddItemStatus() {
        if(getJiashenCheckItemNUm()>0){
            isAddItemStatus=1;//加申审批
        }
        return isAddItemStatus;
    }

    public Integer getOfferItemStatus() {
        if(getIsAddItemStatus()!=null && getIsAddItemStatus().intValue()==1 && getCheckItemNUm()>0){
            offerItemStatus=0;
        }

        return offerItemStatus;
    }
}
