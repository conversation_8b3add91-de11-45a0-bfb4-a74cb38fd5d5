package com.get.financecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 财务报表异步信息返回Vo
 */
@Data
public class FinancialStatsAsyncVo {

    @ApiModelProperty(value = "类型")
    private String typeName;

    @ApiModelProperty(value = "时间")
    private String time;

    @ApiModelProperty(value = "公司名")
    private String companyName;

    @ApiModelProperty(value = "状态：0等待/1执行中/2完成/3错误")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private String staffName;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

}
