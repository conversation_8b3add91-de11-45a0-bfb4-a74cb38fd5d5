package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.type.Alias;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_staff_area_country")
@Alias("SchoolGateStaffAreaCountry")
public class StaffAreaCountry extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;

    /**
     * 国家Key(编号)
     */
    @ApiModelProperty(value = "国家Key(编号)")
    @Column(name = "fk_area_country_key")
    private String fkAreaCountryKey;


}