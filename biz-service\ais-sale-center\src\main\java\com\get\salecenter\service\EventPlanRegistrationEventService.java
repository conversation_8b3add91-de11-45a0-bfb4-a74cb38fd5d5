package com.get.salecenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.salecenter.entity.EventPlanRegistrationEvent;
import com.get.salecenter.dto.EventPlanRegistrationEventDeleteDto;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
public interface EventPlanRegistrationEventService extends BaseService<EventPlanRegistrationEvent> {

    /**
     * 取消活动项目
     * <AUTHOR>
     * @DateTime 2024/3/28 14:47
     */
    void cancel(Long id,Boolean isCancel);

    /**
     * 删除报名名册项目
     * <AUTHOR>
     * @DateTime 2024/3/29 18:04
     */
    void delete(EventPlanRegistrationEventDeleteDto vo);

}
