package com.get.aismail.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("m_institution")
public class MInstitution {
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long fkInstitutionTypeId;

    private Long fkAreaCountryId;

    private Long fkAreaStateId;

    private Long fkAreaCityId;

    private String fkCurrencyTypeNum;

    private String num;

    private String name;

    private String nameChn;

    private String nameDisplay;

    private String shortName;

    private String shortNameChn;

    private String nature;

    private String establishedDate;

    private String applyDate;

    private BigDecimal applyFeeMin;

    private BigDecimal applyFeeMax;

    private BigDecimal applyFeeRef;

    private BigDecimal applyFeeCny;

    private String website;

    private String zipCode;

    private String address;

    private String detail;

    private String mapXyGg;

    private String mapXyBd;

    private String rankingType;

    private Boolean isIncludingNonEnglish;

    private Boolean isKpi;

    private Integer kpiLevel;

    private Boolean isActive;

    private String publicLevel;

    private Integer dataLevel;

    private String idGea;

    private String idIae;

    private LocalDateTime gmtCreate;

    private String gmtCreateUser;

    private LocalDateTime gmtModified;

    private String gmtModifiedUser;
}
