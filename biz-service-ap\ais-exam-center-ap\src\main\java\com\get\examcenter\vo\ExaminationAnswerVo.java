package com.get.examcenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON>.
 * Time: 16:34
 * Date: 2021/8/25
 * Description:考题答案返回类
 */
@Data
@ApiModel("考题答案返回类")
public class ExaminationAnswerVo extends BaseEntity implements Serializable {


    /**
     * 是否正确答案名称
     */
    @ApiModelProperty(value = "公司名称")
    private String isRightAnswerName;

    //=================实体类======================

    private static final long serialVersionUID = 1L;
    /**
     * 问题Id
     */
    @ApiModelProperty(value = "问题Id")
    private Long fkExaminationQuestionId;
    /**
     * 答案内容
     */
    @ApiModelProperty(value = "答案内容")
    private String answer;
    /**
     * 是否正确答案：0否/1是
     */
    @ApiModelProperty(value = "是否正确答案：0否/1是")
    private Boolean isRightAnswer;

    /**
     * 答案得分
     */
    @ApiModelProperty(value = "得分")
    private Integer score;

    /**
     * 默认为0，为0时随机排序，排序为从大到小顺序排列
     */
    @ApiModelProperty(value = "默认为0，为0时随机排序，排序为从大到小顺序排列")
    private Integer viewOrder;



}
