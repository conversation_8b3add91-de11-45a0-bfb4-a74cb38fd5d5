package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.AreaCityMapper;
import com.get.institutioncenter.dao.AreaCountryMapper;
import com.get.institutioncenter.dao.AreaRegionStateMapper;
import com.get.institutioncenter.dao.AreaStateMapper;
import com.get.institutioncenter.dto.AreaStateDto;
import com.get.institutioncenter.vo.AreaCityVo;
import com.get.institutioncenter.vo.AreaCountryVo;
import com.get.institutioncenter.vo.AreaRegionVo;
import com.get.institutioncenter.vo.AreaStateVo;
import com.get.institutioncenter.entity.AreaCity;
import com.get.institutioncenter.entity.AreaCountry;
import com.get.institutioncenter.entity.AreaRegionState;
import com.get.institutioncenter.entity.AreaState;
import com.get.institutioncenter.service.IAreaCountryService;
import com.get.institutioncenter.service.IAreaRegionStateService;
import com.get.institutioncenter.service.IAreaStateService;
import com.get.institutioncenter.service.ITranslationMappingService;
import com.get.institutioncenter.utils.MyStringUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2020/7/27 12:22
 * @verison: 1.0
 * @description: 区域管理-州省配置实现类
 */
@Service
public class AreaStateServiceImpl extends ServiceImpl<AreaStateMapper, AreaState> implements IAreaStateService {
    @Resource
    private AreaStateMapper areaStateMapper;
    @Resource
    private AreaCountryMapper areaCountryMapper;
    @Resource
    private AreaCityMapper areaCityMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private AreaRegionStateMapper areaRegionStateMapper;

    @Resource
    private IAreaRegionStateService areaRegionStateService;
    @Resource
    private IAreaCountryService areaCountryService;
    @Resource
    private ITranslationMappingService translationMappingService;

    @Override
    public AreaStateVo findAreaStateById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AreaState areaState = areaStateMapper.selectById(id);

        //查找业务国家信息
        AreaStateVo areaStateVo = BeanCopyUtils.objClone(areaState, AreaStateVo::new);
        //州省ids
        Set<Long> fkAreaStateIds = new HashSet<>();
        fkAreaStateIds.add(areaStateVo.getId());
        //根据州省ids获取业务区域对象
        Map<Long, List<AreaRegionVo>> areaRegionDtoByAreaStateIdMap = getAreaRegionDtoByAreaStateIdMap(fkAreaStateIds);
        //业务区域
        areaStateVo.setAreaRegion(areaRegionDtoByAreaStateIdMap.get(areaStateVo.getId()));
        return areaStateVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAdd(ValidList<AreaStateDto> areaStateDtos) {
        if (GeneralTool.isEmpty(areaStateDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        List<AreaRegionState> list = new ArrayList<>();
        List<AreaState> upList = new ArrayList<>();
        for (AreaStateDto areaStateDto : areaStateDtos) {
            AreaState areaState = BeanCopyUtils.objClone(areaStateDto, AreaState::new);
            utilService.updateUserInfoToEntity(areaState);
            areaStateMapper.insert(areaState);
            //自动生成编号
            areaState.setNum(MyStringUtils.getStateNum(areaState.getId()));
            upList.add(areaState);
            List<Long> fkAreaRegionId = areaStateDto.getFkAreaRegionId();
            if (GeneralTool.isNotEmpty(fkAreaRegionId)) {
                for (Long aLong : fkAreaRegionId) {
                    AreaRegionState areaRegionState = new AreaRegionState();
                    areaRegionState.setFkAreaRegionId(aLong);
                    areaRegionState.setFkAreaStateId(areaState.getId());
                    utilService.updateUserInfoToEntity(areaRegionState);
                    list.add(areaRegionState);
                }
            }
        }
        updateBatchById(upList);
        if (GeneralTool.isNotEmpty(list)) {
            areaRegionStateService.saveBatch(list);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (findAreaStateById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        areaStateMapper.deleteById(id);

        //删除业务区域关联关系
        areaRegionStateMapper.deleteByAreaStateId(id);

        //删除翻译内容
        translationMappingService.deleteTranslations(TableEnum.INSTITUTION_AREA_STATE.key, id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AreaStateVo updateAreaState(AreaStateDto areaStateDto) {
        if (areaStateDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        AreaState result = areaStateMapper.selectById(areaStateDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        AreaState areaState = BeanCopyUtils.objClone(areaStateDto, AreaState::new);
        utilService.updateUserInfoToEntity(areaState);
        areaStateMapper.updateById(areaState);

        //修改州省与业务区域关联关系，先删除，再新增
        areaRegionStateMapper.deleteByAreaStateId(areaStateDto.getId());
        //新增州省与业务区域关联关系
        List<Long> fkAreaRegionId = areaStateDto.getFkAreaRegionId();
        if (GeneralTool.isNotEmpty(fkAreaRegionId)) {
            List<AreaRegionState> list = new ArrayList<>(fkAreaRegionId.size());
            for (Long aLong : fkAreaRegionId) {
                AreaRegionState areaRegionState = new AreaRegionState();
                areaRegionState.setFkAreaRegionId(aLong);
                areaRegionState.setFkAreaStateId(areaState.getId());
                utilService.updateUserInfoToEntity(areaRegionState);
                list.add(areaRegionState);
            }
            if (GeneralTool.isNotEmpty(list)) {
                areaRegionStateService.saveBatch(list);
            }
        }
        return findAreaStateById(areaStateDto.getId());
    }

    @Override
    public List<AreaCountryVo> getTreeList() {
        //获取国家 树的第一层
        List<AreaCountryVo> resultList = new ArrayList<>();
        List<AreaCountry> areaCountrys = areaCountryMapper.selectList(Wrappers.<AreaCountry>query().lambda().orderByDesc(AreaCountry::getViewOrder));
        for (AreaCountry areaCountry : areaCountrys) {
            //通过国家id查询对应州省 树的第二层
            List<AreaStateVo> areaStateTree = getAreaStateTree(areaCountry.getId());
            AreaCountryVo areaCountryVo = BeanCopyUtils.objClone(areaCountry, AreaCountryVo::new);
            areaCountryVo.setAreaStateDtos(areaStateTree);
            areaCountryVo.setType("country");
            resultList.add(areaCountryVo);
        }
        return resultList;
    }

    private List<AreaStateVo> getAreaStateTree(Long id) {
        List<AreaStateVo> areaStateList = new ArrayList<>();
        List<AreaState> areaStates = areaStateMapper.selectList(Wrappers.<AreaState>query().orderByDesc("IFNULL(view_order,0)").orderByAsc("CONVERT(name USING gbk)").lambda().eq(AreaState::getFkAreaCountryId, id));
        for (AreaState areaState : areaStates) {
            AreaStateVo areaStateVo = BeanCopyUtils.objClone(areaState, AreaStateVo::new);
            //通过州省id查询对应的城市 树的第三层
            List<AreaCityVo> areaCityTree = getAreaCityTree(areaState.getId());
            areaStateVo.setAreaCityDtos(areaCityTree);
            areaStateVo.setType("state");
            areaStateList.add(areaStateVo);
        }
        return areaStateList;
    }

    private List<AreaCityVo> getAreaCityTree(Long id) {
        List<AreaCityVo> areaCityList = new ArrayList<>();
        List<AreaCity> areaCitys = areaCityMapper.selectList(Wrappers.<AreaCity>query().orderByDesc("IFNULL(view_order,0)").orderByAsc("CONVERT(name USING gbk)").lambda().eq(AreaCity::getFkAreaStateId, id));
        for (AreaCity areaCity : areaCitys) {
            AreaCityVo areaCityVo = BeanCopyUtils.objClone(areaCity, AreaCityVo::new);
            areaCityVo.setType("city");
            areaCityList.add(areaCityVo);
        }
        return areaCityList;
    }


    @Override
    public List<AreaStateVo> getByFkAreaCountryId(Long id) {
        QueryWrapper<AreaState> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AreaState::getFkAreaCountryId, id);
        queryWrapper.orderByDesc("IFNULL(view_order,0)");
        queryWrapper.orderByAsc("CONVERT(name USING gbk)");
        List<AreaState> list = areaStateMapper.selectList(queryWrapper);
        List<AreaStateVo> convertDatas = new ArrayList<>();
        for (AreaState areaState : list) {
            AreaStateVo contractTypeDto = BeanCopyUtils.objClone(areaState, AreaStateVo::new);
            contractTypeDto.setFullName(areaStateMapper.getStateFullNameById(areaState.getId()));
            convertDatas.add(contractTypeDto);
        }
        return convertDatas;
    }

    @Override
    public List<AreaStateVo> getAreaStates(AreaStateDto areaStateDto, Page page) {

        IPage<AreaState> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        //要按照最新记录排序
        List<AreaState> areaStates = areaStateMapper.selectAreaStateByVo(pages, areaStateDto);
        page.setAll((int) pages.getTotal());

        List<AreaStateVo> convertDatas = new ArrayList<>();
        //州省ids
        Set<Long> fkAreaStateIds = areaStates.stream().map(AreaState::getId).collect(Collectors.toSet());
        //根据州省ids获取业务区域对象
        Map<Long, List<AreaRegionVo>> areaRegionDtoByAreaStateIdMap = getAreaRegionDtoByAreaStateIdMap(fkAreaStateIds);


        for (AreaState areaState : areaStates) {
            AreaStateVo areaStateVo = BeanCopyUtils.objClone(areaState, AreaStateVo::new);
            //业务区域
            areaStateVo.setAreaRegion(areaRegionDtoByAreaStateIdMap.get(areaStateVo.getId()));
            convertDatas.add(areaStateVo);
        }
        return convertDatas;
    }

    @Override
    public String getStateNameById(Long id) {
        return areaStateMapper.getStateNameById(id);
    }

    @Override
    public String getStateFullNameById(Long id) {
        return areaStateMapper.getStateFullNameById(id);
    }

    @Override
    public Map<Long, String> getStateNamesByIds(Set<Long> ids) {
        //up by Jerry 2021/07/16 10:37:00
        //批量查询改成一次性查询
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        List<AreaState> areaStates = areaStateMapper.selectList(Wrappers.<AreaState>query().lambda().in(AreaState::getId, ids));
        for (AreaState areaState : areaStates) {
            //map.put(areaState.getId(), areaState.getName());
            String stateName = GeneralTool.isEmpty(areaState.getName()) ? "" : areaState.getName();
            StringBuilder builder = new StringBuilder(stateName);
            if (GeneralTool.isNotEmpty(areaState.getNameChn())) {
                builder.append("（");
                builder.append(areaState.getNameChn());
                builder.append("）");
            }
            map.put(areaState.getId(), builder.toString());
        }
        return map;
    }

    @Override
    public Map<Long, String> getStateFullNamesByIds(Set<Long> ids) {
        //up by Jerry 2021/07/16 10:37:00
        //批量查询改成一次性查询
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        List<AreaState> areaStates = areaStateMapper.selectList(Wrappers.<AreaState>query().lambda().in(AreaState::getId, ids));
        for (AreaState areaState : areaStates) {
            String stateName = GeneralTool.isEmpty(areaState.getName()) ? "" : areaState.getName();
            StringBuilder builder = new StringBuilder(stateName);
            if (GeneralTool.isNotEmpty(areaState.getNameChn())) {
                builder.append("（");
                builder.append(areaState.getNameChn());
                builder.append("）");
            }
            map.put(areaState.getId(), builder.toString());
        }
        return map;
    }

    @Override
    public Map<Long, Long> getCountryIdsByStateIds(Set<Long> fkAreaStateIds) {
        List<AreaState> areaStates = areaStateMapper.getCountryIdsByStateIds(fkAreaStateIds);
        Map<Long, Long> dataMap = areaStates.stream().collect(Collectors.toMap(AreaState::getId, AreaState::getFkAreaCountryId));
        return dataMap;
    }

    private boolean validateAdd(AreaStateDto areaStateDto) {
        List<AreaState> list = areaStateMapper.selectList(Wrappers.<AreaState>query().lambda().eq(AreaState::getNum, areaStateDto.getNum()));
        return GeneralTool.isEmpty(list);
    }

    private boolean validateUpdate(AreaStateDto areaStateDto) {
        List<AreaState> list = areaStateMapper.selectList(Wrappers.<AreaState>query().lambda().eq(AreaState::getNum, areaStateDto.getNum()));
        return list.size() <= 0 || list.get(0).getId().equals(areaStateDto.getId());
    }


    /**
     * 根据州省ids获取业务区域对象
     *
     * @param fkAreaStateIds
     * @return
     */
    private Map<Long, List<AreaRegionVo>> getAreaRegionDtoByAreaStateIdMap(Set<Long> fkAreaStateIds) {
        Map<Long, List<AreaRegionVo>> map = new HashMap<>();
        if (GeneralTool.isEmpty(fkAreaStateIds)) {
            return map;
        }
        List<AreaRegionVo> areaRegionDtoByAreaStateId = areaRegionStateMapper.getAreaRegionDtoByAreaStateId(fkAreaStateIds);
        if (GeneralTool.isEmpty(areaRegionDtoByAreaStateId)) {
            return map;
        }
        //获取所有国家id
        Set<Long> fkCountryIds = areaRegionDtoByAreaStateId.stream().map(AreaRegionVo::getFkAreaCountryId).collect(Collectors.toSet());
        //根据国家ids获取名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkCountryIds)) {
            countryNamesByIds = areaCountryService.getCountryNamesByIds(fkCountryIds);
        }
        for (AreaRegionVo areaRegionVo : areaRegionDtoByAreaStateId) {
            areaRegionVo.setFkAreaCountryName(countryNamesByIds.get(areaRegionVo.getFkAreaCountryId()));
//            map.put(areaRegionVo.getFkAreaStateId(), areaRegionVo);
        }
        return areaRegionDtoByAreaStateId.stream().collect(Collectors.groupingBy(AreaRegionVo::getFkAreaStateId));
    }

    @Override
    public Map<Long, String> getSimplifyStateNamesByIds(Set<Long> ids) {
        //up by Jerry 2021/07/16 10:37:00
        //批量查询改成一次性查询
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        List<AreaState> areaStates = areaStateMapper.selectList(Wrappers.<AreaState>query().lambda().in(AreaState::getId, ids));
        for (AreaState areaState : areaStates) {
            //map.put(areaState.getId(), areaState.getName());
            String stateName = GeneralTool.isEmpty(areaState.getName()) ? "" : areaState.getName();
            map.put(areaState.getId(), stateName);
        }
        return map;
    }

    /**
     * 获取对应国家、公司下 有申请计划的代理所在的 州省下拉框数据
     *
     * @Date 18:52 2023/1/5
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> getExistsAgentOfferItemAreaStateList(Long companyId, Long countryId) {
        if (GeneralTool.isEmpty(SecureUtil.getCountryIds())) {
            return null;
        }
        return areaStateMapper.getExistsAgentOfferItemAreaStateList(companyId, countryId, SecureUtil.getCountryIds());
    }

    @Override
    public String getAreaStateNameByIds(Set<Long> ids) {
        if(GeneralTool.isEmpty(ids)){
            return null;
        }
        StringBuilder stringBuilder = new StringBuilder();
        List<AreaState> countryIdsByState = areaStateMapper.getCountryIdsByStateIds(ids);
        if (GeneralTool.isNotEmpty(countryIdsByState)){

            List<String> stateNames = countryIdsByState.stream().map(AreaState::getNameChn).collect(Collectors.toList());
            String stateName = StringUtils.join(stateNames.toArray(), ",");
            stringBuilder.append("【省份】").append(stateName);
        }
        return stringBuilder.toString();
    }

    /**
     * 获取对应国家、公司下 的代理所在的 州省下拉框数据
     *
     * @Date 11:05 2023/3/15
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> getExistsAgentAreaStateList(Long companyId, Long countryId) {
        return areaStateMapper.getExistsAgentAreaStateList(companyId, countryId);
    }

    /**
     * 获取所有州省名称
     * @return
     */
    @Override
    public Map<Long, String> getStateFullNames() {
        Map<Long, String> map = new HashMap<>();
        List<AreaState> areaStates = areaStateMapper.selectList(Wrappers.query());
        for (AreaState areaState : areaStates) {
            String stateName = GeneralTool.isEmpty(areaState.getName()) ? "" : areaState.getName();
            StringBuilder builder = new StringBuilder(stateName);
            if (GeneralTool.isNotEmpty(areaState.getNameChn())) {
                builder.append("（");
                builder.append(areaState.getNameChn());
                builder.append("）");
            }
            map.put(areaState.getId(), builder.toString());
        }
        return map;
    }
}
