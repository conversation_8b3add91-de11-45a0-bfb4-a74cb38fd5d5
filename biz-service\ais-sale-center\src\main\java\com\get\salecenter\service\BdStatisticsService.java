package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.common.result.SearchBean;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.UserInfo;
import com.get.salecenter.dto.AgentAnnualSummaryDto;
import com.get.salecenter.dto.BdStudentBonusDto;
import com.get.salecenter.dto.BdStudentStatisticalComparisonDto;
import com.get.salecenter.dto.DelayConfigDto;
import com.get.salecenter.vo.*;

import java.util.List;
import java.util.Map;

/**
 * bd统计业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2023/3/16 10:03
 */
public interface BdStatisticsService {
    /**
     * @Date 11:08 2022/12/28
     * <AUTHOR>
     */
    List<BdStudentStatisticalComparisonVo> bdStudentStatisticalComparison(BdStudentStatisticalComparisonDto data, Page page, List<Long> countryIds, Long staffId);

    /**
     * 下载BD学生统计对比表
     *
     * @param data
     * @param headerMap
     * @param countryIds
     */
    void downloadBdStudentStatisticalComparison(BdStudentStatisticalComparisonDto data, Map<String, String> headerMap, String locale, UserInfo user, List<Long> countryIds, Long staffId);


    /***
     * 下载代理统计对比表
     * @param annualSummaryVo
     * @param headerMap
     * @param locale
     * @param user
     * @param countryIds
     */
    void downloadAgentStatisticsComparison(AgentAnnualSummaryDto annualSummaryVo, Map<String, String> headerMap, String locale, UserInfo user, List<Long> countryIds, List<Long> institutionIds);

    /**
     * BD学生统计对比表 bd比对统计总计
     *
     * @Date 11:19 2023/1/6
     * <AUTHOR>
     */
    List<BdStudentStatisticalComparisonVo> bdStudentStatisticalComparisonTotal(BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonDto, List<Long> countryIds, Long staffId);

    /**
     * BD学生统计对比表 条件回显
     *
     * @Date 15:42 2023/1/6
     * <AUTHOR>
     */
    BdStudentStatisticalConditionalEchoVo bdStudentStatisticalComparisonConditionalEcho(BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonDto);

    /**
     * hti首页学生统计柱状图
     *
     * @Date 15:56 2023/2/16
     * <AUTHOR>
     */
    List<StudentStatistical> htiHomeBarChart(BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonDto);

    /**
     * 获取角色提成规则
     *
     * @Date 16:16 2023/3/8
     * <AUTHOR>
     */
    List<StaffCommissionPolicyVo> getBdStaffCommissionPolicy(Long fkCompanyId, String projectRoleKey);

    /**
     * bd角色提成结算
     *
     * @Date 17:17 2023/3/10
     * <AUTHOR>
     */
    void bdStaffCommissionPolicySettleAccounts(BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonDto);

    /**
     * 代理年度总表对比统计
     *
     * @Date 11:11 2023/3/16
     * <AUTHOR>
     */
    List<AgentAnnualSummaryStatisticsVo> agentAnnualSummaryStatistics(AgentAnnualSummaryDto data, List<Long> countryIds, Long staffId, List<Long> institutionIds);

    /**
     * 代理年度时间区间对比统计
     *
     * @Date 15:07 2023/3/22
     * <AUTHOR>
     */
    List<AgentAnnualSummaryStatisticsVo> agentAnnualIntervalComparisonStatistics(AgentAnnualSummaryDto agentAnnualSummaryDto, List<Long> countryIds, Long staffId, List<Long> institutionIds);

    /**
     * 代理统计对比表 条件回显
     * @param agentAnnualSummaryDto
     * @return
     */

    AgentStatisticalConditionalEchoVo agentComparisonConditionalEcho(AgentAnnualSummaryDto agentAnnualSummaryDto);

    DelayConfigDto getDelayConfig();

    /**
     * bd学生奖金预统计
     * @param
     * @return
     */
    List<BdStudentBonusVo> bdStudentBonusPreStatistics(SearchBean<BdStudentBonusDto> page);

    List<BaseSelectEntity> bdSubordinate();

}
