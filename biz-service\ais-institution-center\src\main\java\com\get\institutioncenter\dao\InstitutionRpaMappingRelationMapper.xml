<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionRpaMappingRelationMapper">
    <insert id="insert" parameterType="com.get.institutioncenter.entity.InstitutionRpaMappingRelation">
        insert into r_institution_rpa_mapping_relation (id, fk_institution_id, fk_institution_rpa_mapping_id,
                                                        gmt_create, gmt_create_user, gmt_modified,
                                                        gmt_modified_user)
        values (#{id,jdbcType=BIGINT}, #{fkInstitutionId,jdbcType=BIGINT}, #{fkInstitutionRpaMappingId,jdbcType=BIGINT},
                #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP},
                #{gmtModifiedUser,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionRpaMappingRelation">
        insert into r_institution_rpa_mapping_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkInstitutionId != null">
                fk_institution_id,
            </if>
            <if test="fkInstitutionRpaMappingId != null">
                fk_institution_rpa_mapping_id,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkInstitutionId != null">
                #{fkInstitutionId,jdbcType=BIGINT},
            </if>
            <if test="fkInstitutionRpaMappingId != null">
                #{fkInstitutionRpaMappingId,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.get.institutioncenter.entity.InstitutionRpaMappingRelation">
        update r_institution_rpa_mapping_relation
        <set>
            <if test="fkInstitutionId != null">
                fk_institution_id = #{fkInstitutionId,jdbcType=BIGINT},
            </if>
            <if test="fkInstitutionRpaMappingId != null">
                fk_institution_rpa_mapping_id = #{fkInstitutionRpaMappingId,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.get.institutioncenter.entity.InstitutionRpaMappingRelation">
        update r_institution_rpa_mapping_relation
        set fk_institution_id             = #{fkInstitutionId,jdbcType=BIGINT},
            fk_institution_rpa_mapping_id = #{fkInstitutionRpaMappingId,jdbcType=BIGINT},
            gmt_create                    = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_create_user               = #{gmtCreateUser,jdbcType=VARCHAR},
            gmt_modified                  = #{gmtModified,jdbcType=TIMESTAMP},
            gmt_modified_user             = #{gmtModifiedUser,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>