package com.get.platformconfigcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.utils.GeneralTool;
import com.get.platformconfigcenter.service.MediaAndAttachedMsoService;
import com.get.platformconfigcenter.service.SitemapService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/7/27 14:14
 */
@Api(tags = "MSO菜单管理")
@RestController
@RequestMapping("platform/mso/sitemap")
public class SitemapController {

    @Resource
    private SitemapService sitemapService;
    @Resource
    private MediaAndAttachedMsoService mediaAndAttachedMsoService;

    /**
     * 菜单树
     *
     * @return
     * @
     */
//    @VerifyPermission(IsVerify = false)
//    @ApiOperation(value = "菜单树", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.LIST, description = "业务平台配置中心/MSO/菜单树")
//    @PostMapping("getSitemapTree")
//    public ResponseBo<SitemapVo> getSitemapTree() {
//        List<SitemapVo> datas = sitemapService.getSitemapTree();
//        return new ListResponseBo<>(datas);
//    }


    /**
     * 新增菜单
     *
     * @param sitemapVo
     * @return
     * @
     */
//    @ApiOperation(value = "新增菜单", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.ADD, description = "业务平台配置中心/MSO/新增菜单")
//    @PostMapping("add")
//    public ResponseBo add(@RequestBody @Validated(SitemapDto.Add.class) SitemapDto sitemapVo) {
//        return SaveResponseBo.ok(sitemapService.addSitemap(sitemapVo));
//    }

    /**
     * 更新菜单
     *
     * @param sitemapVo
     * @return
     * @
     */
//    @ApiOperation(value = "更新菜单", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.EDIT, description = "业务平台配置中心/MSO/更新菜单")
//    @PostMapping("update")
//    public ResponseBo update(@RequestBody @Validated(SitemapDto.Update.class) SitemapDto sitemapVo) {
//        sitemapService.updateSitemap(sitemapVo);
//        return UpdateResponseBo.ok();
//    }

    /**
     * 菜单详情
     *
     * @param id
     * @return
     * @
     */
//    @ApiOperation(value = "菜单详情", notes = "id为此条数据id")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DETAIL, description = "业务平台配置中心/MSO/菜单详情")
//    @GetMapping("/{id}")
//    public ResponseBo<SitemapVo> detail(@PathVariable("id") Long id) {
//        return new ResponseBo<>(sitemapService.findSitemapById(id));
//    }
//
//    /**
//     * 删除菜单
//     *
//     * @param id
//     * @return
//     * @
//     */
//    @ApiOperation(value = "删除菜单", notes = "id为此条数据的id")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DELETE, description = "业务平台配置中心/MSO/删除菜单")
//    @PostMapping("delete/{id}")
//    public ResponseBo delete(@PathVariable("id") Long id) {
//        sitemapService.delete(id);
//        return DeleteResponseBo.ok();
//    }
//
//
//    /**
//     * 根据父id获取子菜单
//     *
//     * @param id
//     * @return
//     * @
//     */
//    @ApiOperation(value = "根据父id获取子菜单", notes = "keyWord为关键字")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DELETE, description = "业务平台配置中心/MSO/根据父id获取子菜单")
//    @PostMapping("getSiteMapByParentId/{id}")
//    public ResponseBo<SitemapVo> getSiteMapByParentId(@PathVariable("id") Long id, String keyWord) {
//        List<SitemapVo> datas = sitemapService.getSiteMapByParentId(id, keyWord);
//        return new ListResponseBo<>(datas);
//    }
//
//
//    /**
//     * @Description: 上移下移
//     * @Author: Jerry
//     * @Date:12:48 2021/8/9
//     */
//    @ApiOperation(value = "上移下移", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.EDIT, description = "业务平台配置中心/MSO/上移下移")
//    @PostMapping("movingOrder")
//    public ResponseBo movingOrder(@RequestBody List<SitemapDto> sitemapVo) {
//        sitemapService.movingOrder(sitemapVo);
//        return ResponseBo.ok();
//    }


    /**
     * @Description: MSO上传文件
     * @Author: Jerry
     * @Date:12:39 2021/8/10
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "MSO上传文件")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.ADD, description = "业务平台配置中心/MSO/上传文件")
    @PostMapping("/upload")
    public ResponseBo msoUpload(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files) {
        ResponseBo responseBo = new ResponseBo();
        if (GeneralTool.isNotEmpty(files)) {
            responseBo.put("data", mediaAndAttachedMsoService.upload(files, LoggerModulesConsts.PLATFORMCENTER_MSO));
        }
        return responseBo;
    }


    /**
     * @Description: 菜单链接类型下拉框
     * @Author: Jerry
     * @Date:12:39 2021/8/10
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "菜单链接类型下拉框")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.LIST, description = "业务平台配置中心/MSO/菜单链接类型下拉框")
    @PostMapping("/menuLinkTypeSelect")
    public ResponseBo menuLinkTypeSelect() {
        List<Map<String, Object>> datas = ProjectKeyEnum.enumsTranslation2Arrays(ProjectKeyEnum.MENU_LINK_TYPE);
        return new ResponseBo<>(datas);
    }

    /**
     * 页面模板下拉
     *
     * @return
     */
//    @VerifyPermission(IsVerify = false)
//    @ApiOperation(value = "页面模板下拉")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.LIST, description = "业务平台配置中心/MSO/菜单链接类型下拉框")
//    @PostMapping("/sitemapPageTemplate")
//    public ResponseBo sitemapPageTemplate() {
//        return new ResponseBo(sitemapService.sitemapPageTemplate());
//    }
}
