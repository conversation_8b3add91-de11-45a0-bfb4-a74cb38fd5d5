package com.get.permissioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.permissioncenter.dto.PermissionGroupDto;
import com.get.permissioncenter.vo.PermissionGroupVo;
import com.get.permissioncenter.entity.PermissionGroup;

import java.util.List;

/**
 * @author: jack
 * @create: 2020/7/2
 * @verison: 1.0
 * @description: 权限组别管理业务接口
 */
public interface IPermissionGroupService extends BaseService<PermissionGroup> {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    PermissionGroupVo findPermissionGroupById(Long id);

    /**
     * 列表数据
     *
     * @param permissionGroupDto
     * @param page
     * @return
     */
    List<PermissionGroupVo> getPermissionGroups(PermissionGroupDto permissionGroupDto, Page page);

    /**
     * 修改
     *
     * @param permissionGroupDto
     * @return
     */
    PermissionGroupVo updatePermissionGroup(PermissionGroupDto permissionGroupDto);

    /**
     * 保存
     *
     * @param permissionGroupDto
     * @return
     */
    Long addPermissionGroup(PermissionGroupDto permissionGroupDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 批量保存
     *
     * @param permissionGroupDtos
     * @return
     */
    void batchAdd(List<PermissionGroupDto> permissionGroupDtos);

    /**
     * 上移下移
     *
     * @param permissionGroupDtos
     * @return
     */
    void movingOrder(List<PermissionGroupDto> permissionGroupDtos);
}
