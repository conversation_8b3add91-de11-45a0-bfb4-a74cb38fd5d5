//package com.get.salecenter.config;
//
//import com.alibaba.druid.pool.DruidDataSource;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.mybatis.spring.SqlSessionFactoryBean;
//import org.mybatis.spring.SqlSessionTemplate;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//import org.springframework.jdbc.datasource.DataSourceTransactionManager;
//
//import javax.sql.DataSource;
//
///**
// * 主数据源配置
// */
//@Configuration
//@MapperScan(basePackages = "com.get.salecenter.dao.sale", sqlSessionTemplateRef = "defaultSqlSessionTemplate")
//public class DefaultDataSourcesConfig {
//    @Bean(name = "defaultDataSource")
//    @ConfigurationProperties(prefix = "spring.datasource")
//    @Primary// 表示这个数据源是默认数据源, 这个注解必须要加，因为不加的话spring将分不清楚那个为主数据源（默认数据源）
//    public DataSource testDataSource() {
//        return new DruidDataSource();
//    }
//
//    /**
//     * 使 application.properties配置生效，如果不主动配置，由于@Order配置顺序不同，将导致配置不能及时生效 多数据源配置驼峰法生效
//     *
//     * @return 数据源
//     */
//    @Bean
//    @ConfigurationProperties(prefix = "mybatis.configuration")
//    public org.apache.ibatis.session.Configuration globalConfiguration() {
//        return new org.apache.ibatis.session.Configuration();
//    }
//
//    @Bean(name = "defaultSqlSessionFactory")
//    @Primary
//    public SqlSessionFactory testSqlSessionFactory(@Qualifier("defaultDataSource") DataSource dataSource, org.apache.ibatis.session.Configuration config) throws Exception {
//        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
//        bean.setDataSource(dataSource);
//        bean.setConfiguration(config);
//        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/sale/*.xml"));
//        return bean.getObject();
//    }
//
//    @Bean(name = "defaultTransactionManager")
//    @Primary
//    public DataSourceTransactionManager testTransactionManager(@Qualifier("defaultDataSource") DataSource dataSource) {
//        return new DataSourceTransactionManager(dataSource);
//    }
//
//    @Bean(name = "defaultSqlSessionTemplate")
//    @Primary
//    public SqlSessionTemplate testSqlSessionTemplate(@Qualifier("defaultSqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
//        return new SqlSessionTemplate(sqlSessionFactory);
//    }
//}
