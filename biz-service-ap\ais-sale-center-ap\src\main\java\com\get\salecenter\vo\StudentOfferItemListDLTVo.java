package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;


@Data
public class StudentOfferItemListDLTVo extends BaseVoEntity {
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("延迟入学时间（最终开学时间）")
    private Date deferOpeningTime;

    /**
     * 延迟入学标记
     */
    @ApiModelProperty(value = "延迟入学标记")
    private Boolean isDeferEntrance;

    @ApiModelProperty(value = "提供商渠道名称")
    private String fkInstitutionChannelName;

    @ApiModelProperty(value = "学校提供商名称")
    private String fkInstitutionProviderName;

    @ApiModelProperty(value = "课程名称")
    private String courseFullName;

    @ApiModelProperty(value = "旧系统课程名称")
    @Column(name = "old_course_custom_name")
    private String oldCourseCustomName;

    @ApiModelProperty(value = "申请备注（网申信息）")
    private String appRemark;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "申请步骤名")
    private String stepName;

    @ApiModelProperty(value = "学生姓名")
    private String studentName;

    @ApiModelProperty(value = "学校名称")
    private String institutionFullName;

    @ApiModelProperty(value = "旧系统学校全称")
    @Column(name = "old_institution_full_name")
    private String oldInstitutionFullName;

    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

    @ApiModelProperty(value = "学校官网")
    private String institutionWebsite;

    @ApiModelProperty(value = "课程官网")
    private String courseWebsite;

    @ApiModelProperty(value = "课程id")
    private Long fkInstitutionCourseId;

    @ApiModelProperty(value = "学生号")
    private String studentId;

    @ApiModelProperty(value = "缺资料附件")
    private List<MediaAndAttachedVo> missingAttachedDtos;

    @ApiModelProperty(value = "附件")
    List<MediaAndAttachedVo> mediaAndAttachedDtos;

    @ApiModelProperty(value = "学生编号")
    private String studentNum;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;

    @ApiModelProperty("创建人")
    private String gmtCreateUser;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "学生生日")
    private Date birthday;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "学生生日")
    private String birthdayStr;

    @ApiModelProperty(value = "国家名")
    private String fkAreaCountryName;


}
