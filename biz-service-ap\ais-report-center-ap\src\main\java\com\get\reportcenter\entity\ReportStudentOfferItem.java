package com.get.reportcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("m_student_offer_item")
public class ReportStudentOfferItem extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 学生申请方案项目父Id
     */
    @ApiModelProperty(value = "学生申请方案项目父Id")
    private Long fkParentStudentOfferItemId;
    /**
     *
     */
    @ApiModelProperty(value = "学生Id")
    private Long fkStudentId;
    /**
     * 代理Id（业绩绑定）
     */
    @ApiModelProperty(value = "代理Id（业绩绑定）")
    private Long fkAgentId;
    /**
     * 员工Id（业绩绑定，BD）
     */
    @ApiModelProperty(value = "员工Id（业绩绑定，BD）")
    private Long fkStaffId;
    /**
     * 学生申请方案Id
     */
    @ApiModelProperty(value = "学生申请方案Id")
    private Long fkStudentOfferId;
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;
    /**
     * 学校校区Id
     */
    @ApiModelProperty(value = "学校校区Id")
    private Long fkInstitutionZoneId;
    /**
     * 课程Id
     */
    @ApiModelProperty(value = "课程Id")
    private Long fkInstitutionCourseId;
    /**
     * 自定义课程Id
     */
    @ApiModelProperty(value = "自定义课程Id")
    private Long fkInstitutionCourseCustomId;
    /**
     * 学校提供商Id
     */
    @ApiModelProperty(value = "学校提供商Id")
    private Long fkInstitutionProviderId;
    /**
     * 渠道来源Id
     */
    @ApiModelProperty(value = "渠道来源Id")
    private Long fkInstitutionChannelId;
    /**
     * 学生申请方案项目状态步骤Id
     */
    @ApiModelProperty(value = "学生申请方案项目状态步骤Id")
    private Long fkStudentOfferItemStepId;
    /**
     * rpa order id（一键申请对应的Order id）
     */
    @ApiModelProperty(value = "rpa order id（一键申请对应的Order id）")
    private Long fkIssueRpaOrderId;
    /**
     * 申请方案项目编号
     */
    @ApiModelProperty(value = "申请方案项目编号")
    private String num;
    /**
     * 学生ID
     */
    @ApiModelProperty(value = "学生ID")
    private String studentId;
    /**
     * 课程长度类型(0周、1月、2年、3学期)
     */
    @ApiModelProperty(value = "课程长度类型(0周、1月、2年、3学期)")
    private Integer durationType;
    /**
     * 课程长度
     */
    @ApiModelProperty(value = "课程长度")
    private BigDecimal duration;
    /**
     * 开学时间
     */
    @ApiModelProperty(value = "开学时间")
    private Date openingTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date closingTime;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;
    /**
     * 学费金额
     */
    @ApiModelProperty(value = "学费金额")
    private BigDecimal tuitionAmount;
    /**
     * 课程官网URL
     */
    @ApiModelProperty(value = "课程官网URL")
    private String courseWebsite;
    /**
     * 是否主课程：0否/1是（一套方案，只有一个主课程）
     */
    @ApiModelProperty(value = "是否主课程：0否/1是（一套方案，只有一个主课程）")
    private Boolean isMain;
    /**
     * 是否后续课程：0否/1是
     */
    @ApiModelProperty(value = "是否后续课程：0否/1是")
    private Boolean isFollow;
    /**
     * 是否减免学分：0否/1是
     */
    @ApiModelProperty(value = "是否减免学分：0否/1是")
    private Boolean isCreditExemption;
    /**
     * 是否加申，0否/1是
     */
    @ApiModelProperty(value = "是否加申，0否/1是")
    private Boolean isAddApp;
    /**
     * 是否步骤更随主课，0否/1是
     */
    @ApiModelProperty(value = "是否步骤更随主课，0否/1是")
    private Boolean isStepFollow;
    /**
     * 申请方式：0网申/1扫描/2原件邮递/3其他
     */
    @ApiModelProperty(value = "申请方式：0网申/1扫描/2原件邮递/3其他")
    private Integer appMethod;
    /**
     * 申请备注（网申信息）
     */
    @ApiModelProperty(value = "申请备注（网申信息）")
    private String appRemark;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 学习计划业务状态(多选)：0获得第一阶段佣金、1获得第二阶段佣金、2获得第三阶段佣金、3获得第四阶段佣金 / 4只读第一阶段、5只读第二阶段、6只读第三阶段、7只读第四阶段
     */
    @ApiModelProperty(value = "学习计划业务状态(多选)：0获得第一阶段佣金、1获得第二阶段佣金、2获得第三阶段佣金、3获得第四阶段佣金 / 4只读第一阶段、5只读第二阶段、6只读第三阶段、7只读第四阶段")
    private String conditionType;
    /**
     * 入学失败原因Id
     */
    @ApiModelProperty(value = "入学失败原因Id")
    private Long fkEnrolFailureReasonId;
    /**
     * 其他入学失败原因
     */
    @ApiModelProperty(value = "其他入学失败原因")
    private String otherFailureReason;
    /**
     * 是否延迟入学标记：0否/1是
     */
    @ApiModelProperty(value = "是否延迟入学标记：0否/1是")
    private Boolean isDeferEntrance;
    /**
     * 学习模式：枚举定义：0未定/1面授/2网课
     */
    @ApiModelProperty(value = "学习模式：枚举定义：0未定/1面授/2网课")
    private Integer learningMode;
    /**
     * 支付押金截止时间
     */
    @ApiModelProperty(value = "支付押金截止时间")
    private Date depositDeadline;
    /**
     * 接受Offer截止时间
     */
    @ApiModelProperty(value = "接受Offer截止时间")
    private Date acceptOfferDeadline;
    /**
     * 状态：0关闭/1打开
     */
    @ApiModelProperty(value = "状态：0关闭/1打开")
    private Integer status;
    /**
     * 旧系统学校名称
     */
    @ApiModelProperty(value = "旧系统学校名称")
    private String oldInstitutionName;
    /**
     * 旧系统学校全称
     */
    @ApiModelProperty(value = "旧系统学校全称")
    private String oldInstitutionFullName;
    /**
     * 旧系统课程名称
     */
    @ApiModelProperty(value = "旧系统课程名称")
    private String oldCourseCustomName;
    /**
     * 旧系统课程专业等级名称
     */
    @ApiModelProperty(value = "旧系统课程专业等级名称")
    private String oldCourseMajorLevelName;
    /**
     * 旧数据财务id(gea)
     */
    @ApiModelProperty(value = "旧数据财务id(gea)")
    private String idGeaFinance;
    /**
     * 旧数据id(issue)
     */
    @ApiModelProperty(value = "旧数据id(issue)")
    private String idIssue;
    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    private String idGea;
    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    private String idIae;
}