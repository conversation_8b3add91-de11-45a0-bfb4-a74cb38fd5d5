package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 批量下载对账单学习申请计划vo
 *
 * <AUTHOR>
 * @date 2022/3/19 12:43
 */
@Data
public class AgentSettlementBatchExportDto {

    @NotNull(message = "代理id不能为空")
    @ApiModelProperty(value = "代理id")
    private Long agentId;

    @NotNull(message = "学生代理合同账户Id不能为空")
    @ApiModelProperty(value = "学生代理合同账户Id")
    private Long fkAgentContractAccountId;

    @NotBlank(message = "结算币种不能为空")
    @ApiModelProperty(value = "结算币种")
    private String agentAccountCurrencyTypeNum;

    @NotNull(message = "应付计划Id不能为空")
    @ApiModelProperty(value = "应付计划Id")
    private Long payablePlanId;

    @NotBlank(message = "业务类型Key不能为空")
    @ApiModelProperty(value = "业务类型Key")
    private String fkTypeKey;

    @NotNull(message = "结算状态 不能为空")
    @ApiModelProperty(value = "当前结算状态：0未结算/1结算中/2代理确认/3财务确认/4财务汇总")
    private Integer statusSettlement;

}
