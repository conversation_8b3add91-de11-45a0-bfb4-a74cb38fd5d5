package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2022/10/8
 * @TIME: 16:08
 * @Description:
 **/
@Data
public class StudentOfferItemBatchProcessingDto  extends BaseVoEntity  {

    @ApiModelProperty(value = "学校课程Id")
    private Long fkInstitutionCourseId;

    @ApiModelProperty(value = "学校提供商渠道Id")
    private Long fkInstitutionChannelId;


    @ApiModelProperty(value = "学校提供商Id")
    private Long fkInstitutionProviderId;

    
}
