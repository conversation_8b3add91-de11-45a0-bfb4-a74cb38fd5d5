package com.get.institutioncenter.service.impl;

import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.BatchUpdateMapper;
import com.get.institutioncenter.dto.BatchUpdateDto;
import com.get.institutioncenter.service.IBatchUpdateService;
import com.get.permissioncenter.vo.BatchModifyConfigVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.dto.BatchModifyConfigDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;

/**
 * @author: Sea
 * @create: 2021/3/1 12:18
 * @verison: 1.0
 * @description:批量修改操作实现类
 */
@Service
public class BatchUpdateServiceImpl implements IBatchUpdateService {
    @Resource
    private BatchUpdateMapper batchUpdateMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Map<String, String>> getBatchUpdates(BatchModifyConfigDto batchModifyConfigDto) {
        if (GeneralTool.isEmpty(batchModifyConfigDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(batchModifyConfigDto.getIds())) {
            return null;
        }
        //feign调用 查找批量修改配置//111111
        Result<List<BatchModifyConfigVo>> result1 = permissionCenterClient.getBatchUpdateItems(batchModifyConfigDto);
        if (!result1.isSuccess()) {
            throw new GetServiceException(result1.getMessage());
        }

        List<BatchModifyConfigVo> batchModifyConfigVoList = result1.getData();
        //用来拼接sql要查询的字段
        StringJoiner stringJoiner1 = new StringJoiner(",");
        //用来拼接sql查询的语句
        StringJoiner stringJoiner2 = new StringJoiner(" LEFT JOIN ");
        //动态变量
        int i = 1;
        for (BatchModifyConfigVo batchModifyConfigVo : batchModifyConfigVoList) {
            //sql字段的别名：col+批量配置id 用来区分sql要查找字段相同的情况
            String code = "col" + batchModifyConfigVo.getId();
            //sql所查表的别名（例如：from xxx表 as a1）
            String alias = "a" + i;
            //sql要查询的字段
            String field = "";
            //动态拼接要联表查询的sql语句
            String result = "";
            //关系类型为空，表示是本表字段（本表别名定好是a，联表别名是动态的a+i） =0表示简单联表查询 =1表示复杂联表查询
            if (GeneralTool.isEmpty(batchModifyConfigVo.getRelationType())) {
                field = "IFNULL("+"a." + batchModifyConfigVo.getFkTargetColumnName() +", '') AS"+ " col" + batchModifyConfigVo.getId();
            } else if (batchModifyConfigVo.getRelationType() == 0) {
                field ="IFNULL("+ alias + "." + batchModifyConfigVo.getFkTargetColumnName() + ", '') AS"+" " + code;
                result = batchModifyConfigVo.getFkSubTableName() + " " + alias + " ON a." + batchModifyConfigVo.getFkKeyColumnName() + " = " + alias + "." + batchModifyConfigVo.getFkSubKeyColumnName();
            } else if (batchModifyConfigVo.getRelationType() == 1) {
                field ="IFNULL("+ alias + "." + batchModifyConfigVo.getFkTargetColumnName() + ", '') AS"+" " + code;
                result = "(SELECT " + batchModifyConfigVo.getFkSubKeyColumnName() +
                        ", cast(GROUP_CONCAT(" + batchModifyConfigVo.getFkTargetColumnName() +
                        ") as char) " + batchModifyConfigVo.getFkTargetColumnName() + " FROM " + batchModifyConfigVo.getFkSubTableName() +
                        " GROUP BY " + batchModifyConfigVo.getFkSubKeyColumnName() +
                        ") " + alias + " ON a." + batchModifyConfigVo.getFkKeyColumnName() +
                        " = " + alias + "." + batchModifyConfigVo.getFkSubKeyColumnName();
            }
            stringJoiner1.add(field);
            if (GeneralTool.isNotEmpty(result)) {
                stringJoiner2.add(result);
            }
            i++;
        }
        //mybatis要发送的sql 包含筛选条件
        String sql1 = "SELECT " + stringJoiner1;
        String sql2 = stringJoiner2.toString();
        List<Map<String, String>> batchUpdates = batchUpdateMapper.getBatchUpdates(sql1, batchModifyConfigDto.getIds(), sql2, batchModifyConfigDto.getFkTableName(), batchModifyConfigDto.getOrderBy());
        return batchUpdates;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchUpdate(List<BatchUpdateDto> batchUpdateVoList) {
        //111111
        for (BatchUpdateDto batchUpdateDto : batchUpdateVoList) {
            Result<BatchModifyConfigVo> result = permissionCenterClient.detail(batchUpdateDto.getBatchModifyConfigId());
            if (!result.isSuccess()) {
                throw new GetServiceException(result.getMessage());
            }
            BatchModifyConfigVo batchModifyConfigVo = result.getData();

            if (GeneralTool.isEmpty(batchModifyConfigVo.getRelationType())) {
                executeSql1(batchUpdateDto, batchModifyConfigVo);
            } else if (batchModifyConfigVo.getRelationType() == 0) {
                executeSql2(batchUpdateDto, batchModifyConfigVo);
            } else if (batchModifyConfigVo.getRelationType() == 1) {
                executeSql3(batchUpdateDto, batchModifyConfigVo);
            }
        }
    }

    /**
     * @return void
     * @Description :多对多联表修改
     * @Param [batchUpdateDto, batchModifyConfigVo]
     * <AUTHOR>
     */
    private void executeSql3(BatchUpdateDto batchUpdateDto, BatchModifyConfigVo batchModifyConfigVo) {
        String sql;//否则，表示联表操作，修改需要先删除后新增
        sql = "delete from " + batchModifyConfigVo.getFkSubTableName() + " where " + batchModifyConfigVo.getFkSubKeyColumnName() + " = " + batchUpdateDto.getId();
        System.out.println("sql = " + sql);
        //确保where条件是否成功
        if (!sql.contains("where")) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        //先删
        batchUpdateMapper.sqlDelete(sql);
        for (String result : batchUpdateDto.getResultList()) {
            sql = "insert into " +
                    batchModifyConfigVo.getFkSubTableName() + "(" + batchModifyConfigVo.getFkSubKeyColumnName() + "," + batchModifyConfigVo.getFkTargetColumnName() + ",gmt_create,gmt_create_user)" +
                    "VALUES(" + batchUpdateDto.getId() + "," + result + ", now()," + "\'" + GetAuthInfo.getLoginId() + "\'" + ")";
            System.out.println("sql = " + sql);
            //后增
            batchUpdateMapper.sqlInsert(sql);
        }
    }

    /**
     * @return void
     * @Description :简单联表修改
     * @Param [batchUpdateDto, batchModifyConfigVo]
     * <AUTHOR>
     */
    private void executeSql2(BatchUpdateDto batchUpdateDto, BatchModifyConfigVo batchModifyConfigVo) {
        String sql;
        String result = batchUpdateDto.getResult();
        //文本和日期类型 sql要加单引号 要修改得值长度大于1表示是个string格式的id串
        if (batchModifyConfigVo.getInputType() == 0 || batchModifyConfigVo.getInputType() == 4 || result.length() > 1) {
            sql = "update " + batchModifyConfigVo.getFkTableName() + " set " +
                    batchModifyConfigVo.getFkKeyColumnName() + " = " + "\'" + result + "\'" +
                    " where " + batchModifyConfigVo.getFkSubKeyColumnName() + " = " + batchUpdateDto.getId();
            System.out.println("sql = " + sql);
        } else {
            if (GeneralTool.isEmpty(result)) {
                result = "NULL";
            }
            sql = "update " + batchModifyConfigVo.getFkTableName() + " set " +
                    batchModifyConfigVo.getFkKeyColumnName() + " = " + result +
                    " where " + batchModifyConfigVo.getFkSubKeyColumnName() + " = " + batchUpdateDto.getId();
            System.out.println("sql = " + sql);
        }
        //确保where条件加入成功
        if (!sql.contains("where")) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        batchUpdateMapper.sqlUpdate(sql);

    }

    /**
     * @return void
     * @Description :本表字段修改
     * @Param [batchUpdateDto, batchModifyConfigVo]
     * <AUTHOR>
     */
    private void executeSql1(BatchUpdateDto batchUpdateDto, BatchModifyConfigVo batchModifyConfigVo) {
        String sql;
        String result = batchUpdateDto.getResult();
        //文本和日期类型 sql要加单引号 要修改得值长度大于1表示是个string格式的id串
        if (batchModifyConfigVo.getInputType() == 0 || batchModifyConfigVo.getInputType() == 4 || result.length() > 1) {
            sql = "update " + batchModifyConfigVo.getFkTableName() + " set " +
                    batchModifyConfigVo.getFkTargetColumnName() + " = " + "\'" + result + "\'" +
                    " where " + batchModifyConfigVo.getFkKeyColumnName() + " = " + batchUpdateDto.getId();
            System.out.println("sql = " + sql);
        } else {
            if (GeneralTool.isEmpty(result)) {
                result = "NULL";
            }
            sql = "update " + batchModifyConfigVo.getFkTableName() + " set " +
                    batchModifyConfigVo.getFkTargetColumnName() + " = " + result +
                    " where " + batchModifyConfigVo.getFkKeyColumnName() + " = " + batchUpdateDto.getId();
            System.out.println("sql = " + sql);
        }
        //确保where条件加入成功
        if (!sql.contains("where")) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        batchUpdateMapper.sqlUpdate(sql);
    }
}
