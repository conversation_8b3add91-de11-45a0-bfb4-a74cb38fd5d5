package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.vo.ReceivableAndPayablePlanVo;
import com.get.salecenter.service.IReceivableAndPayablePlanService;
import com.get.salecenter.dto.PayablePlanDto;
import com.get.salecenter.dto.ReceivableAndPayablePlanDto;
import com.get.salecenter.dto.ReceivablePlanDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: Hardy
 * @create: 2021/12/8 11:52
 * @verison: 1.0
 * @description:
 */
@Api(tags = "应收-应付计划管理")
@RestController
@RequestMapping("sale/receivableAndPayablePlan")
public class ReceivableAndPayablePlanController {

    @Resource
    private IReceivableAndPayablePlanService receivableAndPayablePlanService;

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增接口
     * @Param [receivableReasonVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增应收应付", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/应收-应付计划管理/新增应收应付")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated({PayablePlanDto.Add.class}) ReceivableAndPayablePlanDto receivableAndPayablePlanDto) {
        receivableAndPayablePlanService.addReceivableAndPayablePlan(receivableAndPayablePlanDto);
        return new ResponseBo<>();
    }

    @ApiOperation(value = "更新", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/应收-应付计划管理/更新")
    @PostMapping("update")
    public ResponseBo<ReceivableAndPayablePlanVo> update(@RequestBody ReceivableAndPayablePlanDto receivableAndPayablePlanDto){
        return receivableAndPayablePlanService.update(receivableAndPayablePlanDto);
    }

    @ApiOperation(value = "详情", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/应收-应付计划管理/详情")
    @GetMapping("details")
    public ResponseBo<ReceivableAndPayablePlanVo> details(@RequestParam("receivablePlanId") Long receivablePlanId, @RequestParam("payablePlanId")  Long payablePlanId){
        return receivableAndPayablePlanService.details(receivablePlanId,payablePlanId);
    }
}
