<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionIengineScoreMapper">
  <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.InstitutionIengineScore">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_table_name_1" jdbcType="VARCHAR" property="fkTableName1" />
    <result column="fk_table_id_1" jdbcType="BIGINT" property="fkTableId1" />
    <result column="fk_table_name_2" jdbcType="VARCHAR" property="fkTableName2" />
    <result column="fk_table_id_2" jdbcType="BIGINT" property="fkTableId2" />
    <result column="type_key" jdbcType="VARCHAR" property="typeKey" />
    <result column="score1" jdbcType="INTEGER" property="score1" />
    <result column="score2" jdbcType="INTEGER" property="score2" />
    <result column="score3" jdbcType="INTEGER" property="score3" />
    <result column="score4" jdbcType="INTEGER" property="score4" />
    <result column="score5" jdbcType="INTEGER" property="score5" />
    <result column="score6" jdbcType="INTEGER" property="score6" />
    <result column="score7" jdbcType="INTEGER" property="score7" />
    <result column="score8" jdbcType="INTEGER" property="score8" />
    <result column="score9" jdbcType="INTEGER" property="score9" />
    <result column="score10" jdbcType="INTEGER" property="score10" />
    <result column="score11" jdbcType="INTEGER" property="score11" />
    <result column="score12" jdbcType="INTEGER" property="score12" />
    <result column="score13" jdbcType="INTEGER" property="score13" />
    <result column="score14" jdbcType="INTEGER" property="score14" />
    <result column="score15" jdbcType="INTEGER" property="score15" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insert" parameterType="com.get.institutioncenter.entity.InstitutionIengineScore">
    insert into s_institution_iengine_score (id, fk_table_name_1, fk_table_id_1, 
      fk_table_name_2, fk_table_id_2, type_key, 
      score1, score2, score3, 
      score4, score5, score6, 
      score7, score8, score9, 
      score10, score11, score12, 
      score13, score14, score15, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkTableName1,jdbcType=VARCHAR}, #{fkTableId1,jdbcType=BIGINT}, 
      #{fkTableName2,jdbcType=VARCHAR}, #{fkTableId2,jdbcType=BIGINT}, #{typeKey,jdbcType=VARCHAR}, 
      #{score1,jdbcType=INTEGER}, #{score2,jdbcType=INTEGER}, #{score3,jdbcType=INTEGER}, 
      #{score4,jdbcType=INTEGER}, #{score5,jdbcType=INTEGER}, #{score6,jdbcType=INTEGER}, 
      #{score7,jdbcType=INTEGER}, #{score8,jdbcType=INTEGER}, #{score9,jdbcType=INTEGER}, 
      #{score10,jdbcType=INTEGER}, #{score11,jdbcType=INTEGER}, #{score12,jdbcType=INTEGER}, 
      #{score13,jdbcType=INTEGER}, #{score14,jdbcType=INTEGER}, #{score15,jdbcType=INTEGER}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionIengineScore">
    insert into s_institution_iengine_score
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkTableName1 != null">
        fk_table_name_1,
      </if>
      <if test="fkTableId1 != null">
        fk_table_id_1,
      </if>
      <if test="fkTableName2 != null">
        fk_table_name_2,
      </if>
      <if test="fkTableId2 != null">
        fk_table_id_2,
      </if>
      <if test="typeKey != null">
        type_key,
      </if>
      <if test="score1 != null">
        score1,
      </if>
      <if test="score2 != null">
        score2,
      </if>
      <if test="score3 != null">
        score3,
      </if>
      <if test="score4 != null">
        score4,
      </if>
      <if test="score5 != null">
        score5,
      </if>
      <if test="score6 != null">
        score6,
      </if>
      <if test="score7 != null">
        score7,
      </if>
      <if test="score8 != null">
        score8,
      </if>
      <if test="score9 != null">
        score9,
      </if>
      <if test="score10 != null">
        score10,
      </if>
      <if test="score11 != null">
        score11,
      </if>
      <if test="score12 != null">
        score12,
      </if>
      <if test="score13 != null">
        score13,
      </if>
      <if test="score14 != null">
        score14,
      </if>
      <if test="score15 != null">
        score15,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkTableName1 != null">
        #{fkTableName1,jdbcType=VARCHAR},
      </if>
      <if test="fkTableId1 != null">
        #{fkTableId1,jdbcType=BIGINT},
      </if>
      <if test="fkTableName2 != null">
        #{fkTableName2,jdbcType=VARCHAR},
      </if>
      <if test="fkTableId2 != null">
        #{fkTableId2,jdbcType=BIGINT},
      </if>
      <if test="typeKey != null">
        #{typeKey,jdbcType=VARCHAR},
      </if>
      <if test="score1 != null">
        #{score1,jdbcType=INTEGER},
      </if>
      <if test="score2 != null">
        #{score2,jdbcType=INTEGER},
      </if>
      <if test="score3 != null">
        #{score3,jdbcType=INTEGER},
      </if>
      <if test="score4 != null">
        #{score4,jdbcType=INTEGER},
      </if>
      <if test="score5 != null">
        #{score5,jdbcType=INTEGER},
      </if>
      <if test="score6 != null">
        #{score6,jdbcType=INTEGER},
      </if>
      <if test="score7 != null">
        #{score7,jdbcType=INTEGER},
      </if>
      <if test="score8 != null">
        #{score8,jdbcType=INTEGER},
      </if>
      <if test="score9 != null">
        #{score9,jdbcType=INTEGER},
      </if>
      <if test="score10 != null">
        #{score10,jdbcType=INTEGER},
      </if>
      <if test="score11 != null">
        #{score11,jdbcType=INTEGER},
      </if>
      <if test="score12 != null">
        #{score12,jdbcType=INTEGER},
      </if>
      <if test="score13 != null">
        #{score13,jdbcType=INTEGER},
      </if>
      <if test="score14 != null">
        #{score14,jdbcType=INTEGER},
      </if>
      <if test="score15 != null">
        #{score15,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>