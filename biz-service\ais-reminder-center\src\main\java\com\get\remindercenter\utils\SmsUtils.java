package com.get.remindercenter.utils;

import cn.hutool.http.HttpUtil;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　　　◆　　　　　　　　　　　　◆　　　　　　　　　　　　　　◆　　　　　　　　　　　　　◆◆　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　◆◆◆　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆
 * 　　　◆◆　　　　　◆◆　　　　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆◆◆◆
 * 　　　◆◆◆　　　◆◆◆　　　　　　　◆◆　　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆◆
 * 　　　　◆◆◆　◆◆◆　　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆
 * 　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　　◆◆
 * 　　　　　　◆◆◆　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆
 * 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆◆
 * <p>
 * Time: 9:48
 * Date: 2021/11/16
 * Description:手机短信工具类
 */
public class SmsUtils {

    public static final String HTTP = "http://v.juhe.cn/smsInternational/send.php";
    public static final String CNHTTP = "http://v.juhe.cn/sms/send";
    private static final String KEY = "4d156bb72e0b294826f10ec5531baf06";
    private static final String CNKEY = "691d5f060842548b40064b0446e460cd";

    /**
     * 发送 海外短信
     *
     * @param phoneNumber 手机号码
     * @param phoneArea   手机区号
     * @param tplId       tplId
     * @param paramMaps   map 只有idecodeval，随机验证码
     * @return
     */
    public static String sendSms(String phoneNumber, String phoneArea, String tplId, Map<String, Object> paramMaps) {
        //香港手机
        String areaNum = "852";
        if (areaNum.equals(phoneArea)) {
            Map map = new HashMap(16);
            map.put("mobile", phoneNumber);
            map.put("areaNum", phoneArea);
            map.put("tplId", tplId);
            map.put("tplValue", buildParams(paramMaps));
            map.put("key", KEY);
            return HttpUtil.createGet(HTTP + "?" + HttpUtil.toParams(map)).execute().body();
        } else {
            //内地手机
            Map map = new HashMap(16);
            map.put("mobile", phoneNumber);
            map.put("tpl_id", tplId);
            map.put("key", CNKEY);
            String code = buildParamsMsg(paramMaps);
            code = code.replaceAll("=", "%3D");
            return HttpUtil.createGet(CNHTTP + "?" + HttpUtil.toParams(map) + "&" + "tpl_value=" + code).execute().body();


        }
    }


    /**
     * 生成参数
     *
     * @param map
     * @return
     */
    private static String buildParams(Map<String, Object> map) {
        String paramVal = "";
        if (map != null && map.size() > 0) {
            Iterator<Map.Entry<String, Object>> iterator = map.entrySet().iterator();
            int i = 0;
            while (iterator.hasNext()) {
                Map.Entry<String, Object> entry = iterator.next();
                if (i == 0) {
                    paramVal = "#" + entry.getKey() + "#=" + entry.getValue();
                    i++;
                } else {
                    paramVal = "&#" + entry.getKey() + "#=" + entry.getValue();
                }
            }

        }
        return paramVal;
    }

    /**
     * 生成参数 拼接短信
     *
     * @param map
     * @return
     */
    private static String buildParamsMsg(Map<String, Object> map) {
        String paramVal = "";
        if (map != null && map.size() > 0) {
            Iterator<Map.Entry<String, Object>> iterator = map.entrySet().iterator();
            int i = 0;
            while (iterator.hasNext()) {
                Map.Entry<String, Object> entry = iterator.next();
                if (i == 0) {
                    paramVal = "#" + entry.getKey() + "#=" + entry.getValue();
                    i++;
                } else {
                    paramVal += "&#" + entry.getKey() + "#=" + entry.getValue();
                }
            }

        }
        try {
            paramVal = URLEncoder.encode(paramVal, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        return paramVal;
    }
}
