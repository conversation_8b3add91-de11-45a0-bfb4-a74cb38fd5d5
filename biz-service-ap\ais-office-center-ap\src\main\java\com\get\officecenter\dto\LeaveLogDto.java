package com.get.officecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2022/1/14
 * @TIME: 11:18
 * @Description:公休日志VO
 **/
@Data
public class LeaveLogDto extends BaseVoEntity {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;
    /**
     * 工休类型关键字，枚举，同工休单类型一致
     */
    @ApiModelProperty(value = "工休类型关键字，枚举，同工休单类型一致")
    private String leaveTypeKey;
    /**
     * 操作类型关键字，枚举
     */
    @ApiModelProperty(value = "操作类型关键字，枚举")
    private String optTypeKey;
    /**
     * 时长（小时）
     */
    @ApiModelProperty(value = "时长（小时）")
    private BigDecimal duration;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 关联工休库存Id
     */
    @ApiModelProperty(value = "关联工休库存Id")
    private Long fkLeaveStockId;
    /**
     * 工休申请单Id
     */
    @ApiModelProperty(value = "工休申请单Id")
    private Long fkLeaveApplicationFormId;
    /**
     * 搜索关键字
     */
    @ApiModelProperty(value = "搜索关键字")
    private String keyWord;

    /**
     * 创建时间(开始)
     */
    @ApiModelProperty("创建时间(开始)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;

    /**
     * 创建时间(结束)
     */
    @ApiModelProperty("创建时间(结束)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;
}
