package com.get.examcenter.service;

import com.get.examcenter.vo.MediaAndAttachedVo;
import com.get.examcenter.dto.MediaAndAttachedDto;
import com.get.filecenter.dto.FileDto;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by <PERSON>.
 * Time: 18:02
 * Date: 2021/8/27
 * Description:媒体附件业务逻辑类
 */
public interface MediaAndAttachedService {

    /**
     * 通过附件GUID获取DTO
     *
     * @param attachedVo
     * @return
     * @
     */
    List<MediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo);

    /**
     * @Description: 根据表ids获取批量的附件
     * @Author: Jerry
     * @Date:9:31 2021/9/7
     */
    Map<Long, List<MediaAndAttachedVo>> getMediaAndAttachedDtoByFkTableIds(String fkTableName, Set<Long> fkTableIds);

    /**
     * 上传文件
     *
     * @Date 11:42 2021/7/5
     * <AUTHOR>
     */
    List<FileDto> upload(MultipartFile[] multipartFiles, String type);

    /**
     * 上传附件
     *
     * @Date 11:42 2021/7/5
     * <AUTHOR>
     */
    List<FileDto> uploadAppendix(MultipartFile[] multipartFiles, String type);

    /**
     * @Description:保存文件
     * @Param
     * @Date 12:28 2021/5/12
     * <AUTHOR>
     */
    MediaAndAttachedVo addMediaAndAttached(MediaAndAttachedDto mediaAttachedVo);

    /**
     * @Description:删除媒体文件
     * @Param
     * @Date 16:40 2021/5/13
     * <AUTHOR>
     */
    void deleteMediaAndAttached(String fkTableName, Long fkTableId);
}
