<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ConventionExamPrizesMapper">

    <!--TODO 注释sql-->
    <!--<select id="getConventionExamPrizesList" resultType="com.get.salecenter.dto.ConventionExamPrizesVo">
        select
        mcep.*,
        CASE
        WHEN mcep.prize_type = 1 THEN '100分奖品'
        WHEN mcep.prize_type = 2 THEN '70分奖品'
        ELSE NULL END prizeTypeName,
        ms.name AS bdName,
        mcp.name AS fkConventionPersonName,
        me.name AS fkExaminationName
        from ais_app_convention_center.m_convention_exam_prizes mcep
        left join ais_sale_center.m_convention_person mcp on mcp.id = mcep.fk_convention_person_id
        left join ais_sale_center.r_staff_bd_code bc on bc.bd_code = mcp.bd_code
        left join ais_permission_center.m_staff AS ms ON ms.id = bc.fk_staff_id
        left join ais_exam_center.m_examination AS me ON me.id = mcep.fk_examination_id
        where 1=1 and mcep.fk_convention_person_id is not null
        <if test="conventionExamPrizesVo.fkExaminationId != null">
            and mcep.fk_examination_id = #{conventionExamPrizesVo.fkExaminationId}
        </if>
        <if test="conventionExamPrizesVo.fkConventionId != null">
            and mcep.fk_convention_id = #{conventionExamPrizesVo.fkConventionId}
        </if>
        <if test="conventionExamPrizesVo.prizeType != null">
            and mcep.prize_type = #{conventionExamPrizesVo.prizeType}
        </if>
        <if test="conventionExamPrizesVo.isExchange != null">
            and mcep.is_exchange = #{conventionExamPrizesVo.isExchange}
        </if>
        <if test="conventionExamPrizesVo.bdName != null and conventionExamPrizesVo.bdName != ''">
            and (ms.name like CONCAT('%', #{conventionExamPrizesVo.bdName,jdbcType=VARCHAR}, '%') or ms.name_en like CONCAT('%', #{conventionExamPrizesVo.bdName,jdbcType=VARCHAR}, '%'))
        </if>
        <if test="conventionExamPrizesVo.fkConventionPersonName != null and conventionExamPrizesVo.fkConventionPersonName != '' ">
            and (mcp.name like CONCAT('%', #{conventionExamPrizesVo.fkConventionPersonName,jdbcType=VARCHAR}, '%') or mcp.name_chn like CONCAT('%', #{conventionExamPrizesVo.fkConventionPersonName,jdbcType=VARCHAR}, '%'))
        </if>
    </select>-->
<!--    <select id="examinationSelectByConventionId" resultType="com.get.core.mybatis.base.BaseSelectEntity">-->
<!--        select e.*-->
<!--        from ais_exam_center.m_examination e-->
<!--        inner join ais_app_convention_center.m_convention_exam_prizes m on m.fk_examination_id = e.id-->
<!--        where-->
<!--        m.fk_convention_id = #{fkConventionId}-->
<!--        group by e.id-->
<!--        ORDER BY e.is_active desc,CONVERT(name USING gbk) desc-->
<!--    </select>-->
<!--    <select id="getConventionExamExportList" resultType="com.get.salecenter.dto.ConventionExamPrizesNewVo">
        SELECT
            p.fk_examination_id,
            max(mcp.`name`) AS userName,
            ms.`name` AS bdName,
            p.fk_examination_paper_id AS examinationPaperId,
            me.name AS examinationPaperName,
            COUNT(DISTINCT q.id) AS questionCount,
            CASE
                WHEN mcep.prize_type = 1 THEN '100分奖品'
                WHEN mcep.prize_type = 2 THEN '70分奖品'
            ELSE '无' END prizeTypeName,
        CASE
        WHEN mcep.is_exchange = 1 THEN '是'
        ELSE '否' END isExchangeName,
            IFNULL(mcep.prize_name,'无') as prize_name,
            mcep.fk_user_id AS prizesUserId,
            mcep.fk_convention_person_id prizesConventionPersonId,
            p.fk_user_id,mcp.id AS fk_convention_person_id
        FROM ais_app_exam_center.r_user_examination_paper_score p
        LEFT JOIN ais_app_exam_center.r_user_examination_question_score q ON p.fk_user_id = q.fk_user_id AND p.fk_examination_paper_id = q.fk_examination_paper_id
        LEFT JOIN ais_app_registration_center.m_user mu on p.fk_user_id=mu.id
        LEFT JOIN ais_sale_center.m_convention_person mcp on mcp.tel = mu.login_id and mcp.fk_convention_id = #{fkConventionId}
        LEFT JOIN ais_app_convention_center.m_convention_exam_prizes mcep on mcep.fk_convention_person_id = mcp.id and mcep.fk_examination_id = p.fk_examination_id AND mu.id = mcep.fk_user_id
        LEFT JOIN ais_exam_center.m_examination AS me ON me.id = p.fk_examination_id
        LEFT JOIN ais_sale_center.r_staff_bd_code bc on bc.bd_code = mcp.bd_code
        LEFT JOIN ais_permission_center.m_staff AS ms ON ms.id = bc.fk_staff_id
        where 1=1
        and p.fk_examination_id in(25,39,41,43,45)
        GROUP BY p.fk_examination_id,p.fk_user_id
        ORDER BY p.fk_user_id,p.fk_examination_id
    </select>-->
    <!--<select id="getConventionExamPrizesLog" resultType="java.lang.Long">
        select id
        from ais_app_convention_center.m_convention_exam_exchange_log
        where fk_convention_id = #{fkConventionId} and fk_examination_id =#{fkExaminationId} and fk_user_id = #{fkUserId} limit 1
    </select>-->
    <select id="getConventionExamScore" resultType="java.lang.Integer">
        select sum(score)
        from ais_app_exam_center.r_user_examination_paper_score
        where fk_user_id = #{fkUserId} and fk_examination_id =#{fkExaminationId} and fk_examination_id in(25,39,41,43,45)
    </select>
    <select id="getConventionExamScoreSum" resultType="java.lang.Integer">
        select sum(score)
        from ais_app_exam_center.r_user_examination_paper_score
        where fk_user_id = #{fkUserId} and fk_examination_id in(25,39,41,43,45)
    </select>
    <!--<select id="getConventionPrizesWinning" resultType="java.lang.Long">
        select fk_user_id
        from ais_app_convention_center.m_convention_exam_prizes
        where fk_convention_id = #{fkConventionId} and fk_examination_id =#{fkExaminationId} and fk_user_id = #{fkUserId} limit 1
    </select>-->

</mapper>