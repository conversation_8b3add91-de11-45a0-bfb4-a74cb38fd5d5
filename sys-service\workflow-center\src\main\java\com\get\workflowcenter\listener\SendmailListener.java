package com.get.workflowcenter.listener;

import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.SpringUtil;
import com.get.officecenter.entity.LeaveApplicationForm;
import com.get.officecenter.feign.IOfficeCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.workflowcenter.component.IWorkFlowHelper;
import com.get.workflowcenter.service.IWorkFlowService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.activiti.engine.runtime.ProcessInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

/**
 * Author: Smail
 * Date: 14/6/2024
 */
public class SendmailListener implements TaskListener {
    //private Logger logger = LoggerFactory.getLogger(this.getClass());
    private Logger logger = LoggerFactory.getLogger(SendmailListener.class);
    @Override
    public void notify(DelegateTask delegateTask) {
        IPermissionCenterClient feignPermissionService = SpringUtil.getBean(IPermissionCenterClient.class);
        RuntimeService runtimeService = SpringUtil.getBean(RuntimeService.class);
        IOfficeCenterClient iOfficeCenterClient = SpringUtil.getBean(IOfficeCenterClient.class);
        IWorkFlowService workFlowService = SpringUtil.getBean(IWorkFlowService.class);

        IWorkFlowHelper workFlowHelper = SpringUtil.getBean(IWorkFlowHelper.class);
        //获取创建人信息
        StaffVo staffVo = workFlowHelper.getStaffDto(delegateTask);
        Long starterId = staffVo.getId();

        //查询工休单内容
        String businessKey = "";
        String processInstanceId = delegateTask.getProcessInstanceId();
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();
        if (processInstance != null) {
            businessKey = processInstance.getBusinessKey();
        }
        LeaveApplicationForm leaveApplicationForm = iOfficeCenterClient.getLeaveApplicationForm(Long.valueOf(businessKey)).getData();
        logger.info("申请人：" + delegateTask.getAssignee() + "，" + "--------------------------------");
        //如果审批人和发起人是同一个人,则不发送邮件
        if (Objects.equals(delegateTask.getAssignee(), String.valueOf(starterId))){
            return;
        }

        logger.info(delegateTask.getName()+"---"+delegateTask.getTaskDefinitionKey());

        //获取申请人的直属上司id
        Long staffSupervisorId = null;
        Result<Long> result = feignPermissionService.getStaffSupervisorIdByStaffId(starterId);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            staffSupervisorId = result.getData();
        }
        logger.info("申请人直属上司：" + staffSupervisorId + "，" + "--------------------------------");
        Long SupervisorId = null;
        Result<Long> Supervisor = feignPermissionService.getStaffSupervisorIdByStaffId(staffSupervisorId);
        if (Supervisor.isSuccess() && GeneralTool.isNotEmpty(Supervisor.getData())) {
            SupervisorId = Supervisor.getData();
        }
        logger.info("申请人直属上司的直属上司：" + SupervisorId + "，" + "--------------------------------");

//        //直属上司的部门
//        StaffVo  staffSuper= feignPermissionService.getCompanyIdByStaffId(staffSupervisorId).getData();
//        Long  department= staffSuper.getFkDepartmentId();
//        //根据员工id查找部门（直属上司的直属上司部门）
//        StaffVo data = feignPermissionService.getCompanyIdByStaffId(SupervisorId).getData();
//        Long fkDepartmentId = data.getFkDepartmentId();

        //直属上司的直属上司!=VGM，需要通知 VGM员工id为689
        if (SupervisorId != null && !SupervisorId.equals(689L)){
            //直属上司的直属上司!=VGM
            try {
                workFlowService.doSendEmail(leaveApplicationForm, "工休单申请流程", SupervisorId);
            }catch (Exception e){
                logger.error("-------------提醒发送异常----------",e);
            }
        }

        //直属上司=当前审批人的id，则表示已审批过了
        if (staffSupervisorId.equals(Long.valueOf(delegateTask.getAssignee()))){
            return;
        }
        else {
            //VGM若曾审批，不需要再通知，没审批才通知
            try {
                workFlowService.doSendEmail(leaveApplicationForm, "工休单申请流程", Long.valueOf(delegateTask.getAssignee()));
            }catch (Exception e){
                logger.error("-------------提醒发送异常----------",e);
            }
        }



        //针对【对外发展部】，审批结束后增加通知邮件
//        if ("V.G.M of Global Sales".equals(delegateTask.getName()) && delegateTask.getAssignee() == null){
//            try {
//                workFlowService.doSendEmail(leaveApplicationForm, "工休单申请流程", SupervisorId);
//            }catch (Exception e){
//                logger.error("-------------提醒发送异常----------",e);
//            }
//        }


    }
}
