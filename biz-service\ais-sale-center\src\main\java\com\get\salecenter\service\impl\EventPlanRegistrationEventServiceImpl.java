package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.EventPlanRegistrationEventMapper;
import com.get.salecenter.entity.EventPlanRegistrationContactPerson;
import com.get.salecenter.entity.EventPlanRegistrationEvent;
import com.get.salecenter.service.EventPlanRegistrationContactPersonService;
import com.get.salecenter.service.EventPlanRegistrationEventService;
import com.get.salecenter.service.EventPlanRegistrationService;
import com.get.salecenter.dto.EventPlanRegistrationEventDeleteDto;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
@Service
public class EventPlanRegistrationEventServiceImpl extends BaseServiceImpl<EventPlanRegistrationEventMapper, EventPlanRegistrationEvent> implements EventPlanRegistrationEventService {

    @Resource
    private EventPlanRegistrationEventMapper eventPlanRegistrationEventMapper;

    @Resource
    @Lazy
    private EventPlanRegistrationService eventPlanRegistrationService;

    @Resource
    private EventPlanRegistrationContactPersonService eventPlanRegistrationContactPersonService;

    @Resource
    private UtilService utilService;
    @Override
    public void cancel(Long id, Boolean isCancel) {
        EventPlanRegistrationEvent eventPlanRegistrationEvent = eventPlanRegistrationEventMapper.selectById(id);
        if(GeneralTool.isEmpty(eventPlanRegistrationEvent)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        eventPlanRegistrationEvent.setIsCancel(isCancel);
        utilService.setUpdateInfo(eventPlanRegistrationEvent);
        eventPlanRegistrationEventMapper.updateById(eventPlanRegistrationEvent);
    }

    public void delete(EventPlanRegistrationEventDeleteDto vo) {
        if(GeneralTool.isEmpty(vo)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
       if(GeneralTool.isNotEmpty(vo.getFkEventPlanRegistrationEventIdList())){
           eventPlanRegistrationEventMapper.deleteBatchIds(vo.getFkEventPlanRegistrationEventIdList());
       }
        List<EventPlanRegistrationEvent> eventPlanRegistrationEvents = eventPlanRegistrationEventMapper.selectList(Wrappers.<EventPlanRegistrationEvent>lambdaQuery()
                .eq(EventPlanRegistrationEvent::getFkEventPlanRegistrationId, vo.getFkEventPlanRegistrationId()));
       //报名名册项目所有记录都不存在时，清除对应的名册以及联系人数据
       if(GeneralTool.isEmpty(eventPlanRegistrationEvents)){
           eventPlanRegistrationService.removeById(vo.getFkEventPlanRegistrationId());
           eventPlanRegistrationContactPersonService.remove(Wrappers.<EventPlanRegistrationContactPerson>lambdaQuery()
                   .eq(EventPlanRegistrationContactPerson::getFkEventPlanRegistrationId, vo.getFkEventPlanRegistrationId()));
       }
    }
}
