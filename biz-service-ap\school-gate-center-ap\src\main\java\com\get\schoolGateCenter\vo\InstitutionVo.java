package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/4
 * @TIME: 18:21
 * @Description:
 **/
@Data
public class InstitutionVo extends BaseVoEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 学校类型Id
     */
    @ApiModelProperty(value = "学校类型", required = true)
    @NotNull(message = "学校类型Id不能为空", groups = {Add.class, Update.class})
    private Long fkInstitutionTypeId;
    /**
     * 显示名称
     */
    @ApiModelProperty(value = "显示名称")
    private String nameDisplay;
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id", required = true)
    private Long fkAreaCountryId;
    /**
     * 州省Id
     */
    @ApiModelProperty(value = "州省Id")
    private Long fkAreaStateId;
    /**
     * 城市Id
     */
    @ApiModelProperty(value = "城市Id")
    private Long fkAreaCityId;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号", required = true)
    @NotBlank(message = "币种编号不能为空", groups = {Add.class, Update.class})
    private String fkCurrencyTypeNum;
    /**
     * 学校编号
     */
    @ApiModelProperty(value = "学校编号")
    private String num;
    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称", required = true)
    @NotBlank(message = "学校名称不能为空", groups = {Add.class, Update.class})
    private String name;
    /**
     * 学校中文名称
     */
    @ApiModelProperty(value = "学校中文名称", required = true)
    private String nameChn;
    /**
     * 学校简称
     */
    @ApiModelProperty(value = "学校简称")
    private String shortName;
    /**
     * 学校中文简称
     */
    @ApiModelProperty(value = "学校中文简称")
    private String shortNameChn;
    /**
     * 学校性质：公立/私立
     */
    @ApiModelProperty(value = "学校性质：公立/私立", required = true)
    private String nature;
    /**
     * 成立时间
     */
    @ApiModelProperty(value = "成立时间")
    private String establishedDate;
    /**
     * 入学申请时间
     */
    @ApiModelProperty(value = "入学申请时间", required = true)
    private String applyDate;
    /**
     * 学费下限
     */
    @ApiModelProperty(value = "学费下限", required = true)
    private BigDecimal applyFeeMin;
    /**
     * 学费上限
     */
    @ApiModelProperty(value = "学费上限", required = true)
    private BigDecimal applyFeeMax;
    /**
     * 官网
     */
    @ApiModelProperty(value = "官网")
    private String website;
    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    private String zipCode;
    /**
     * 地址
     */
    @ApiModelProperty(value = "地址", required = true)
    private String address;
    /**
     * 重点推荐的学校：0否/1是
     */
    @ApiModelProperty(value = "重点推荐的学校", required = true)
    private Boolean isKpi;
    /**
     * 重点推荐的学校等级：1小推荐/2中推荐/3大推荐
     */
    @ApiModelProperty(value = "重点推荐的学校等级：1小推荐/2中推荐/3大推荐")
    private Integer kpiLevel;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活", required = true)
    @NotNull(message = "是否激活不能为空", groups = {Add.class, Update.class})
    private Boolean isActive;
    /**
     * 详细描述
     */
    @ApiModelProperty(value = "详细描述")
    private String detail;
    @ApiModelProperty(value = "关键词")
    private String keyWord;
    @ApiModelProperty(value = "配置提供商id数组")
    private List<Long> ids;
    @ApiModelProperty(value = "配置提供商id")
    private Long fkProviderId;
    @ApiModelProperty(value = "国家ids")
    private List<Long> countryIds;
    @ApiModelProperty(value = "学校LOGO图片")
    private List<MediaAndAttachedVo> mediaAttachedVos;
    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @NotBlank(message = "公开对象", groups = {Add.class, Update.class})
    private String publicLevel;
    /**
     * 数据等级：0旧数据导入/1官方数据导入/2需要补充/3采集完整
     */
    @ApiModelProperty(value = "数据等级：0旧数据导入/1官方数据导入/2需要补充/3采集完整")
    @NotNull(message = "数据等级", groups = {Add.class, Update.class})
    private Integer dataLevel;
    /**
     * 地图坐标（google）
     */
    @ApiModelProperty(value = "地图坐标（google）")
    private String mapXyGg;
    /**
     * 地图坐标（baidu）
     */
    @ApiModelProperty(value = "地图坐标（baidu）")
    private String mapXyBd;
    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    private String idGea;
    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    private String idIae;
}
