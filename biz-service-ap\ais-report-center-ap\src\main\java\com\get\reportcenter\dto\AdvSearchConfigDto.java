package com.get.reportcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2021/3/4
 * @TIME: 15:06
 * @Description:
 **/
@Data
public class AdvSearchConfigDto extends BaseVoEntity {
    /**
     * 高级搜索脚本key
     */
    @ApiModelProperty(value = "高级搜索脚本key")
    private String queryKey;

    /**
     * 脚本标题
     */
    @ApiModelProperty(value = "脚本标题")
    private String queryTitle;

    /**
     * 结果表格长度
     */
    @ApiModelProperty(value = "结果表格长度")
    private Integer queryWidth;

    /**
     * Sql脚本
     */
    @ApiModelProperty(value = "Sql脚本")
    private String querySql;

    /**
     * 搜索参数
     */
    @ApiModelProperty(value = "搜索参数")
    private String queryParamter;
}
