package com.get.financecenter.excelmodel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 易思汇excel模板
 *
 * <AUTHOR>
 * @date 2022/1/6 14:54
 */
@Data
public class AgentSettlementTransferModel {

    @ApiModelProperty(value = "应付计划id")
    private Long id;

    @ApiModelProperty(value = "代理名")
    private String agentName;

    @ApiModelProperty(value = "银行账户名")
    private String bankAccount;

    @ApiModelProperty(value = "身份证号（个人）")
    private String idCardNum;

    @ApiModelProperty(value = "银行名")
    private String bankName;

    @ApiModelProperty(value = "银行账号")
    private String bankAccountNum;

    @ApiModelProperty(value = "应付币种")
    private String payablePlanCurrency;

    @ApiModelProperty(value = "应付金额")
    private BigDecimal payableAmount;

    @ApiModelProperty(value = "国家编号")
    private String countryNum;

    @ApiModelProperty(value = "国家名称")
    private String countryName;

    @ApiModelProperty(value = "学校名称")
    private String institutionName;

    @ApiModelProperty(value = "学生名字")
    private String studentName;

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "实际手续费金额")
    private BigDecimal serviceFeeActual;

    @ApiModelProperty("联系方式")
    private String contactInformation;

    @ApiModelProperty("学费金额")
    private BigDecimal tuitionAmount;

    @ApiModelProperty("返佣比例")
    private String rebateRatio;

    @ApiModelProperty("法人姓名")
    private String legalPersonName;

    @ApiModelProperty("法人身份证号")
    private String idCard;

    @ApiModelProperty("受益人性质")
    private String natureOfBeneficiary;

    @ApiModelProperty("性质")
    private Integer nature;

    @ApiModelProperty("账户卡类型")
    private Integer accountCardType;

    @ApiModelProperty("代理id")
    private Long agentId;

    @ApiModelProperty("社会信用代码")
    private String socialCreditCode;

    @ApiModelProperty("账户卡类型")
    private String cardType;

    @ApiModelProperty("银行地址")
    private String bankAddress;

}
