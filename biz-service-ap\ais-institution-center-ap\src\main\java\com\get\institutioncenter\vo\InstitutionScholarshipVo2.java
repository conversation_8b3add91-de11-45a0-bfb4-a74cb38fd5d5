package com.get.institutioncenter.vo;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/6 12:04
 */
@Data
public class InstitutionScholarshipVo2 extends BaseEntity {

    @ApiModelProperty("学校名")
    private String fkInstitutionName;


    @ApiModelProperty("学校中文名")
    private String fkInstitutionNameZh;

    @ApiModelProperty("学校英文名")
    private String fkInstitutionNameEn;

    @ApiModelProperty("学院名")
    private String institutionFacultyName;

    /**
     * 学校学院Id（二级）
     */
    @ApiModelProperty(value = "学校学院Id（二级）")
    private Long fkInstitutionFacultyIdSub;

    /**
     * 学校学院Id（二级）
     */
    @ApiModelProperty(value = "学校学院Id（二级）")
    private String fkInstitutionFacultyIdSubName;

    @ApiModelProperty("二级学院奖学金信息")
    private List<InstitutionScholarshipSubVo> fkFacultyDatas = new ArrayList<>();

    @ApiModelProperty("课程等级")
    private String majorLeveName;

    @ApiModelProperty("公开对象名称")
    private String publicLevelName;

    @ApiModelProperty("学院等级，1是1级，2是二级")
    private int Level;

    @ApiModelProperty("学校封面url")
    private String coversUrl;

    @ApiModelProperty("是否有申请费用")
    private Boolean isAppFee;

    @ApiModelProperty("是否有申请截止")
    private Boolean isDeadInfo;

    @ApiModelProperty("是否有奖学金")
    private Boolean isScholarship;

    //==============实体类InstitutionScholarship2=====================
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;

    /**
     * 学校学院Id（一级）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "学校学院Id（一级）")
    @Column(name = "fk_institution_faculty_id")
    private Long fkInstitutionFacultyId;



    /**
     * 课程专业等级Id
     */
    @ApiModelProperty(value = "课程专业等级Id")
    @Column(name = "fk_major_level_ids")
    private String fkMajorLevelIds;

    /**
     * 奖学金标题
     */
    @ApiModelProperty(value = "奖学金标题")
    @Column(name = "scholarship_title")
    private String scholarshipTitle;

    /**
     * 奖学金金额
     */
    @ApiModelProperty(value = "奖学金金额")
    @Column(name = "scholarship_amount")
    private String scholarshipAmount;

    /**
     * 奖学金名额
     */
    @ApiModelProperty(value = "奖学金名额")
    @Column(name = "scholarship_quota")
    private String scholarshipQuota;


    /**
     * 奖学金适用学生
     */
    @ApiModelProperty(value = "奖学金适用学生")
    @Column(name = "scholarship_apply_to")
    private String scholarshipApplyTo;

    /**
     * 申请条件
     */
    @ApiModelProperty(value = "申请条件")
    @Column(name = "app_condition")
    private String appCondition;

    /**
     * 申请方式
     */
    @ApiModelProperty(value = "申请方式")
    @Column(name = "app_method")
    private String appMethod;

    /**
     * 申请截止日期：[2022-1-5,2023-10-5][2022-1-5,2023-10-5]
     */
    @ApiModelProperty(value = "申请截止日期：[2022-1-5,2023-10-5][2022-1-5,2023-10-5]")
    @Column(name = "app_deadline")
    private String appDeadline;

    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @Column(name = "public_level")
    private String publicLevel;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    @Column(name = "publish_time")
    private Date publishTime;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;

    /**
     * 详细信息
     */
    @ApiModelProperty(value = "详细信息")
    @Column(name = "app_detail")
    private String appDetail;

}
