package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.vo.EventDeferTimeVo;
import com.get.salecenter.service.IEventDeferTimeService;
import com.get.salecenter.dto.EventDeferTimeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "活动时间日志")
@RestController
@RequestMapping("sale/eventDeferTime")
public class EventDeferTimeController {
    @Resource
    private IEventDeferTimeService eventDeferTimeService;

    @ApiOperation(value = "列表数据", notes = "fkEventId为该活动ID")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动/活动汇总/活动时间日志列表")
    @PostMapping("datas/{fkEventId}")
    public ResponseBo<EventDeferTimeVo> datas(@PathVariable("fkEventId") Long fkEventId) {
        List<EventDeferTimeVo> datas = eventDeferTimeService.datas(fkEventId);
        return new ListResponseBo<>(datas);
    }

    /**
     * 新增活动时间日志
     *
     * @param eventDeferTimeDto 活动时间日志VO
     * @return
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/活动/活动汇总/新增活动时间日志")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(EventDeferTimeDto.Add.class)  EventDeferTimeDto eventDeferTimeDto) {
        return SaveResponseBo.ok(eventDeferTimeService.addEventDeferTime(eventDeferTimeDto));
    }

    /**
     * 删除活动时间日志
     *
     * @param id 活动日志id
     * @return
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/活动/活动汇总/删除活动时间日志")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        eventDeferTimeService.delete(id);
        return DeleteResponseBo.ok();
    }

}
