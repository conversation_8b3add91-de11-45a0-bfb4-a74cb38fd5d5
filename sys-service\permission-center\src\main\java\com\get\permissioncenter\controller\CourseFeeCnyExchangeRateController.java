package com.get.permissioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author:GSHG
 * @date: 2022-01-14 2:41 PM
 * description:
 */
@Api(tags = "课程学费批量转换RMB")
@RestController
@RequestMapping("system/courseFeeCnyExchangeRate")
public class CourseFeeCnyExchangeRateController {

    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @ApiOperation(value = "更新课程学费feeCny")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.EDIT, description = "按当前费率，按照学校的币种，更新所有课程学费feeCny")
    @GetMapping("updateCoursefeeCny")
    public ResponseBo updateCoursefeeCny(@RequestParam(required = false) String updateType) {
        Result<Boolean> result = institutionCenterClient.updateCoursefeeCny(updateType);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            return ResponseBo.ok();
        }
        return ResponseBo.error();
    }

}
