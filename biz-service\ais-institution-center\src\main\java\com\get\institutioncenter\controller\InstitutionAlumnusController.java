package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.institutioncenter.vo.InstitutionAlumnusVo;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.service.IInstitutionAlumnusService;
import com.get.institutioncenter.service.IMediaAndAttachedService;
import com.get.institutioncenter.dto.InstitutionAlumnusDto;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/10
 * @TIME: 16:20
 * @Description:
 **/
@Api(tags = "知名校友管理")
@RestController
@RequestMapping("/institution/institutionAlumnus")
public class InstitutionAlumnusController {
    @Resource
    private IInstitutionAlumnusService institutionAlumnusService;
    @Resource
    private IMediaAndAttachedService mediaAndAttachedService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/知名校友管理/知名校友详情")
    @GetMapping("/{id}")
    public ResponseBo<InstitutionAlumnusVo> detail(@PathVariable("id") Long id) {
        InstitutionAlumnusVo data = institutionAlumnusService.findInstitutionAlumnusById(id);
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", data);
        return responseBo;
    }

    /**
     * 新增信息
     *
     * @param institutionAlumnusDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/知名校友管理/新增知名校友")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(InstitutionAlumnusDto.Add.class) InstitutionAlumnusDto institutionAlumnusDto) {
        return SaveResponseBo.ok(institutionAlumnusService.addInstitutionAlumnus(institutionAlumnusDto));
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/知名校友管理/删除知名校友")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        institutionAlumnusService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param institutionAlumnusDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/知名校友管理/更新知名校友")
    @PostMapping("update")
    public ResponseBo<InstitutionAlumnusVo> update(@RequestBody @Validated(InstitutionAlumnusDto.Update.class) InstitutionAlumnusDto institutionAlumnusDto) {
        return UpdateResponseBo.ok(institutionAlumnusService.updateInstitutionAlumnus(institutionAlumnusDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/知名校友管理/查询知名校友")
    @PostMapping("datas")
    public ResponseBo<InstitutionAlumnusVo> datas(@RequestBody SearchBean<InstitutionAlumnusDto> page) {
        List<InstitutionAlumnusVo> datas = institutionAlumnusService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 上移下移
     *
     * @param mediaAttachedVos
     * @return
     * @
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/知名校友管理/知名校友移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<MediaAndAttachedDto> mediaAttachedVos) {
        mediaAndAttachedService.movingOrder(mediaAttachedVos);
        return ResponseBo.ok();
    }

    /**
     * @param id
     * @return
     * @
     */

    @ApiOperation(value = "删除文件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/知名校友管理/删除文件")
    @PostMapping("deleteFile/{id}")
    public ResponseBo upload(@PathVariable("id") Long id) {
        mediaAndAttachedService.deleteMediaAttached(id);
        return ResponseBo.ok();
    }


    /**
     * @param mediaAttachedVo
     * @return
     * @
     */

    @ApiOperation(value = "保存文件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/知名校友管理/保存附件资料")
    @PostMapping("addMediaAndAttached")
    public ResponseBo addMediaAndAttached(@RequestBody @Validated(MediaAndAttachedDto.Add.class) List<MediaAndAttachedDto> mediaAttachedVo) {
        return UpdateResponseBo.ok(institutionAlumnusService.addInstitutionAlumnusMedia(mediaAttachedVo));
    }

    /**
     * 查询知名校友附件
     *
     * @param voSearchBean
     * @return
     * @
     */
    @ApiOperation(value = "查询知名校友附件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/知名校友管理/查询知名校友附件")
    @PostMapping("getInstitutionAlumnusMedia")
    public ResponseBo<MediaAndAttachedVo> getInstitutionAlumnusMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> institutionMedia = institutionAlumnusService.getInstitutionAlumnusMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(institutionMedia, page);
    }
}
