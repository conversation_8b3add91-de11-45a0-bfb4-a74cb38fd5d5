package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.dto.ContactPersonTypeDto;
import com.get.salecenter.entity.ContactPersonType;
import com.get.salecenter.service.IContactPersonTypeService;
import com.get.salecenter.vo.ContactPersonTypeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 联系人类型管理控制器
 */
@Api(tags = "联系人类型管理")
@RestController
@RequestMapping("sale/contactPersonType")
public class ContactPersonTypeController {

    @Resource
    private IContactPersonTypeService contactPersonTypeService;


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :批量新增信息
     * @Param [contactPersonTypeDtos]
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/联系人类型管理/新增联系人类型")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(
            @RequestBody @Validated(ContactPersonTypeDto.Add.class) ValidList<ContactPersonTypeDto> contactPersonTypeDtos) {
        contactPersonTypeService.batchAdd(contactPersonTypeDtos);
        return SaveResponseBo.ok();
    }

    /**
     * 获取代理可用联系人类型下拉框（排除已选择的类型）
     *
     * @param fkAppAgentId 代理ID
     * @return 可用的联系人类型列表
     */
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取代理可用联系人类型下拉框", notes = "返回排除代理已选择的联系人类型")
    @GetMapping("getAvailableTypesByAgentId")
    public ResponseBo<ContactPersonTypeVo> getAvailableTypesByAgentId(@RequestParam("fkAppAgentId") Long fkAppAgentId) {
        List<ContactPersonTypeVo> datas = contactPersonTypeService.getAvailableContactPersonTypes(fkAppAgentId);
        return new ListResponseBo<>(datas);
    }

    /**
     * 获取联系人表关联可用联系人类型下拉框（排除已选择的类型）
     *
     * @param fkTableId 表ID
     * @return 可用的联系人类型列表
     */
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取联系人表关联可用联系人类型下拉框", notes = "返回排除联系人表已选择的联系人类型")
    @GetMapping("getAvailableTypesByContactPersonTableId")
    public ResponseBo<ContactPersonTypeVo> getAvailableTypesByContactPersonTableId(
            @RequestParam("fkTableId") Long fkTableId) {
        List<ContactPersonTypeVo> datas = contactPersonTypeService.getAvailableTypesByContactPersonTableId(fkTableId);
        return new ListResponseBo<>(datas);
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.REMINDERCENTER, type = LoggerOptTypeConst.DELETE, description = "提醒中心/联系人类型管理/删除联系人类型")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        contactPersonTypeService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param contactPersonTypeDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.REMINDERCENTER, type = LoggerOptTypeConst.EDIT, description = "提醒中心/联系人类型管理/更新联系人类型")
    @PostMapping("update")
    public ResponseBo update(
            @RequestBody @Validated(ContactPersonTypeDto.Update.class) ContactPersonTypeDto contactPersonTypeDto) {
        return UpdateResponseBo.ok(contactPersonTypeService.updateContactPersonType(contactPersonTypeDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "typeName类型名称")
    @OperationLogger(module = LoggerModulesConsts.REMINDERCENTER, type = LoggerOptTypeConst.LIST, description = "提醒中心/联系人类型管理/查询联系人类型")
    @PostMapping("datas")
    public ResponseBo datas(@RequestBody SearchBean<ContactPersonTypeDto> page) {
        List<ContactPersonTypeVo> datas = contactPersonTypeService.getContactPersonTypes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 代理联系人类型下拉框
     *
     * @return
     * @
     */
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "代理联系人类型下拉框", notes = "typeName类型名称")
    @GetMapping("getAllType")
    public ResponseBo<ContactPersonTypeVo> getAllType() {
        List<ContactPersonTypeVo> datas = contactPersonTypeService.getContactPersonTypes();
        return new ListResponseBo<>(datas);
    }

    /**
     * 代理联系人类型下拉框
     *
     * @param isNewType
     * @return
     */
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "代理联系人类型下拉框", notes = "typeName类型名称")
    @GetMapping("getType")
    public ResponseBo<ContactPersonTypeVo> getType(@RequestParam("isNewType") boolean isNewType) {
        List<ContactPersonTypeVo> datas = contactPersonTypeService.getContactPersonTypes(isNewType);
        return new ListResponseBo<>(datas);
    }

    /**
     * 代理联系人类型下拉框
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "合同联系人类型下拉框", notes = "typeName类型名称")
    @GetMapping("getContractPersonType")
    public ResponseBo<ContactPersonType> getContractType() {
        List<ContactPersonType> datas = contactPersonTypeService.getContactContactTypes();
        return new ListResponseBo<>(datas);
    }

    /**
     * 上移下移
     *
     * @param contactPersonTypeDtos
     * @return
     * @
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.REMINDERCENTER, type = LoggerOptTypeConst.EDIT, description = "提醒中心/联系人类型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<ContactPersonTypeDto> contactPersonTypeDtos) {
        contactPersonTypeService.movingOrder(contactPersonTypeDtos);
        return ResponseBo.ok();
    }

}
