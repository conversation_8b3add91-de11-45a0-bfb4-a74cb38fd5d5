package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.StaffCommissionAction;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: neil
 * @verison: 1.0
 * @description:
 */
@Data
public class StaffCommissionRefundVo extends BaseEntity {

    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    @ApiModelProperty(value = "结算角色")
    private String roleAndStaff;

    @ApiModelProperty(value = "结算角色Id")
    private Long roleId;

    @ApiModelProperty(value ="结算角色")
    private String roleName;

    @ApiModelProperty(value = "结算步骤")
    private String staffCommissionActionStepName;

    /**
     * 绑定代理
     */
    @ApiModelProperty(value = "绑定代理")
    private String agentName;

    /**
     * 学生名称
     */
    @ApiModelProperty(value = "学生名称")
    private String studentName;

    @ApiModelProperty(value = "学生国籍所在国家Id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "国家")
    private String fkAreaCountryName;

    /**
     * 学校供应商
     */
    @ApiModelProperty(value = "学校供应商")
    private String fkInstitutionProviderName;

    /**
     * 学校供应商
     */
    @ApiModelProperty(value = "学校供应商(渠道)")
    private String fkInstitutionProviderChannelName;

    @ApiModelProperty(value = "学校供应商Id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "学校渠道Id")
    private Long fkInstitutionChannelId;

    @ApiModelProperty(value = "提供商渠道名称")
    private String fkInstitutionChannelName;

    /**
     * 学校
     */
    @ApiModelProperty(value = "学校")
    private String institutionFullName;

    @ApiModelProperty(value = "学校id")
    private Long fkInstitutionId;

    /**
     * 课程
     */
    @ApiModelProperty(value = "课程")
    private String courseFullName;

    @ApiModelProperty(value = "旧课程名称")
    private String oldCourseCustomName;

    /**
     * 课程ID
     */
    @ApiModelProperty(value = "课程ID")
    private Long fkInstitutionCourseId;

    /**
     * 开学时间
     */
    @ApiModelProperty(value = "开学时间")
    private String deferOpeningTime;

    @ApiModelProperty(value = "申请计划步骤")
    private String stepName;

    @ApiModelProperty(value = "申请计划步骤Id")
    private Long fkStudentOfferItemStepId;

    @ApiModelProperty(value = "审核人")
    private String staffName;

    @ApiModelProperty(value = "审核人/审核信息")
    private String refundReviewStaffContent;

    @ApiModelProperty(value = "申请计划状态")
    private Integer itemStatus;

    @ApiModelProperty(value = "方案id")
    private Long fkStudentOfferId;

    @ApiModelProperty("学生id")
    private Long fkStudentId;

    @ApiModelProperty("结算退款原因")
    private String reasonName;

    //============实体类StaffCommissionAction==================
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;



    /**
     * 学生申请方案项目Id
     */
    @ApiModelProperty(value = "学生申请方案项目Id")
    @Column(name = "fk_student_offer_item_id")
    private Long fkStudentOfferItemId;

    /**
     * 学生项目角色key
     */
    @ApiModelProperty(value = "学生项目角色key")
    @Column(name = "fk_student_project_role_key")
    private String fkStudentProjectRoleKey;

    /**
     * 员工提成业务步骤key
     */
    @ApiModelProperty(value = "员工提成业务步骤key")
    @Column(name = "fk_staff_commission_step_key")
    private String fkStaffCommissionStepKey;

    /**
     * 员工Id（业绩提成）
     */
    @ApiModelProperty(value = "员工Id（业绩提成）")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;

    /**
     * 结算日期
     */
    @ApiModelProperty(value = "结算日期（4位年月），格式如：202303")
    @Column(name = "settlement_date")
    private String settlementDate;

    /**
     * 提成金额
     */
    @ApiModelProperty(value = "提成金额")
    @Column(name = "commission_amount")
    private BigDecimal commissionAmount;

    /**
     * 状态：0未结算/1已结算
     */
    @ApiModelProperty(value = "状态：0未结算/1已结算")
    @Column(name = "status")
    private Integer status;

    /**
     * 退款审核人
     */
    @ApiModelProperty(value = "退款审核人")
    @Column(name = "fk_staff_id_refund_review")
    private Long fkStaffIdRefundReview;

    /**
     * 退款审核内容
     */
    @ApiModelProperty(value = "退款审核内容")
    @Column(name = "refund_review_content")
    private String refundReviewContent;

    /**
     * 退款审核时间
     */
    @ApiModelProperty(value = "退款审核时间")
    @Column(name = "refund_review_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date refundReviewTime;

    @ApiModelProperty(value = "业绩统计时间")
    @Column(name = "performance_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date performanceTime;

    /**
     * 退款审核状态：0或null未审核/1无需退款/2确认退款
     */
    @ApiModelProperty(value = "退款审核状态：0或null未审核/1无需退款/2确认退款")
    @Column(name = "refund_review_status")
    private Integer refundReviewStatus;

    /**
     * 退款结算人
     */
    @ApiModelProperty(value = "退款结算人")
    @Column(name = "fk_staff_id_refund_settlement")
    private Long fkStaffIdRefundSettlement;

    @ApiModelProperty(value = "退款结算时间")
    @Column(name = "refund_settlement_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date refundSettlementTime;

    /**
     * 退款结算状态：0或null未结算/1不结算/2已结算
     */
    @ApiModelProperty(value = "退款结算状态：0或null未结算/1不结算/2已结算")
    @Column(name = "refund_settlement_status")
    private Integer refundSettlementStatus;

    @ApiModelProperty(value = "结算退款原因Id")
    @Column(name = "fk_settlement_refund_reason_id")
    private Long fkSettlementRefundReasonId;

    private static final long serialVersionUID = 1L;

}
