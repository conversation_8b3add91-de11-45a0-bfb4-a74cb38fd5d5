<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.get.institutioncenter.dao.InstitutionProviderAccountMapper">

    <select id="getProviderAccountList"
            resultType="com.get.institutioncenter.vo.InstitutionProviderAccountVo">
        SELECT
            a.*
        FROM
            m_institution_provider_account a
        INNER JOIN m_institution_provider p ON p.id = a.fk_institution_provider_id
        WHERE
            1=1
        <if test="providerAccountVo.fkInstitutionProviderId!=null and providerAccountVo.fkInstitutionProviderId!=''">
            AND p.id = #{providerAccountVo.fkInstitutionProviderId}
        </if>
    </select>
    <select id="getContractAccountExist"
            resultType="com.get.institutioncenter.vo.InstitutionProviderAccountVo">
        SELECT
            a.*,CONCAT(p.name_chn,'（',p.name,'）') as providerName
        FROM
            m_institution_provider_account a
        INNER JOIN m_institution_provider p ON p.id = a.fk_institution_provider_id
        LEFT JOIN r_institution_provider_company c ON c.fk_institution_provider_id = p.id
        WHERE
            1=1
        <if test="providerId!=null and providerId!=''">
            AND p.id = #{providerId}
        </if>
        <if test="bankAccount!=null and bankAccount!=''">
            AND a.bank_account = #{bankAccount}
        </if>
        <if test="bankAccountNum!=null and bankAccountNum!=''">
            AND a.bank_account_num = #{bankAccountNum}
        </if>

    </select>
    <select id="getInstitutionProviderAccountList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            ba.id,
            CONCAT(
                '【',
                CASE
            WHEN IFNULL(ct.type_name, '') = '' THEN
                ct.num
            ELSE
                CONCAT(
                    ct.num,
                    '（',
                    ct.type_name,
                    '）'
                )
            END,
            '】',
            ba.bank_account,
            ',',
            ba.bank_name
            ) AS NAME
        FROM
            m_institution_provider_account ba
        LEFT JOIN ais_finance_center.u_currency_type ct ON ct.num = ba.fk_currency_type_num
        WHERE
            ba.is_active = 1
        AND ba.fk_institution_provider_id = #{fkTargetId}
    </select>
    <select id="getInstitutionProviderAccountById" resultType="java.lang.String">
        SELECT
            CONCAT(
                '【',
                CASE
            WHEN IFNULL(ct.type_name, '') = '' THEN
                ct.num
            ELSE
                CONCAT(
                    ct.num,
                    '（',
                    ct.type_name,
                    '）'
                )
            END,
            '】',
            ba.bank_account,
            ',',
            ba.bank_name
            )
        FROM
            m_institution_provider_account ba
        LEFT JOIN ais_finance_center.u_currency_type ct ON ct.num = ba.fk_currency_type_num
        WHERE
            ba.is_active = 1
        AND ba.id = #{fkBankAccountId}
    </select>
</mapper>
