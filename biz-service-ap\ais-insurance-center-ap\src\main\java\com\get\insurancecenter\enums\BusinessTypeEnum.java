package com.get.insurancecenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 信用卡流水业务类型枚举类
 */

@Getter
@AllArgsConstructor
public enum BusinessTypeEnum {

    ADJUST(0, "校正金额" ),
    PAY(1, "支出" ),
    REPAYMENT(2, "还款" ),
    REFUND(3, "退款" ),
    ;


    private Integer code;
    private String msg;


    public static BusinessTypeEnum getEnumByCode(Integer code) {
        for (BusinessTypeEnum value : BusinessTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
