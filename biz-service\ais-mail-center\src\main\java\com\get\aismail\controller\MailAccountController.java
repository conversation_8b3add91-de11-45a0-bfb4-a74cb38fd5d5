package com.get.aismail.controller;

import com.get.aismail.entity.MMailAccount;
import com.get.aismail.service.IMailAccountService;
import com.get.aismail.vo.MailAccountVo;
import com.get.common.result.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "邮箱账户配置")
@RestController
@RequestMapping("/mailAccount")
public class MailAccountController {
    @Resource
    private IMailAccountService mailAccountService;

    @ApiOperation(value = "绑定邮箱")
    @PostMapping("/addMailBox")
    public ResponseBo addMailBox(@RequestBody MailAccountVo mailAccountVo) throws Exception {
        return SaveResponseBo.ok(mailAccountService.bindMail(mailAccountVo));
    }

    @ApiOperation(value = "删除邮箱")
    @PostMapping("/deleteMailBox")
    public ResponseBo deleteMailBox(String emailAccount) throws Exception {
        mailAccountService.deleteMail(emailAccount);
        return DeleteResponseBo.ok();
    }

    @ApiOperation(value = "修改邮箱")
    @PostMapping("/updateMailBox")
    public ResponseBo updateMailBox(@RequestBody MailAccountVo mailAccountVo) throws Exception {
        mailAccountService.updateMail(mailAccountVo);
        return UpdateResponseBo.ok();
    }

    @ApiOperation(value = "获取所有邮箱")
    @PostMapping("/getAllMailBox")
    public ResponseBo<MMailAccount> getAllMailBox() throws Exception {
        return new ListResponseBo<>(mailAccountService.getAllMaillAccount());
    }

}
