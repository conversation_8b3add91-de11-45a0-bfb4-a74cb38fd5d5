package com.get.institutioncenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.List;

/**
 * Time: 14:28
 * Date: 2022/5/5
 * Description:
 */
@Data
public class InstitutionCourseMatchDto {
//    @ApiModelProperty(value = "课程全称")
//    private String fullName;
    @ApiModelProperty(value = "课程名称")
    private String courseName;
    @ApiModelProperty(value = "课程的学校id")
    private Long institutionId;
    @ApiModelProperty(value = "课程id")
    private Long courseId;
    @ApiModelProperty(value = "课程链接")
    private String courseWebsite;

    @ApiModelProperty(value = "得分")
    private BigDecimal score;

    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

    @ApiModelProperty(value = "课程等级List")
    private List<Long> courseLevelList;

}
