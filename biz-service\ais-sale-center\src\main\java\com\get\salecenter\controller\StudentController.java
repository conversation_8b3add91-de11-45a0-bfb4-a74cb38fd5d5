package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.FocExportVo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.CommonUtil;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.secure.utils.SecureUtil;
import com.get.salecenter.vo.*;
import com.get.salecenter.service.IStudentService;
import com.get.salecenter.service.StudentServiceFeeService;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.query.StudentListQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/9/29
 * @TIME: 14:35
 * @Description: 学生管理
 **/

@Api(tags = "学生管理")
@RestController
@RequestMapping("sale/student")
public class StudentController {
    @Autowired
    private IStudentService studentService;

    @Autowired
    private StudentServiceFeeService studentServiceFeeService;
    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [studentDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生管理/新增学生")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(StudentDto.Add.class) StudentDto studentDto) {
        return SaveResponseBo.ok(studentService.addStudent(studentDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentVo>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "num代理编号 name代理名称")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/查询学生")
    @PostMapping("datas")
    public ResponseBo<StudentVo> datas(@RequestBody @Validated StudentListQueryDto studentListQueryDto) {
        //[0]-o-主SQL执行时间,[1]-f-远程调用时间
        String[] times = {"0", "0"};
        List<StudentVo> datas = studentService.getStudents(studentListQueryDto,times,SecureUtil.getStaffId(),SecureUtil.getLocale(), SecureUtil.getCountryIds());
        return new ListResponseBo<>(datas, times[0], times[1]);
    }

    @ApiOperation(value = "获取学生列表分页信息", notes = "num代理编号 name代理名称")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生管理/查询学生")
    @PostMapping("getStudentsPaginationInfo")
    @VerifyPermission(IsVerify = false)
    public ResponseBo getStudentsPaginationInfo(@RequestBody SearchBean<StudentListQueryDto> page) {
        return studentService.getStudentPaginationInfo(page.getData(), page);
    }



        /**
         * @return com.get.common.result.ResponseBo
         * @Description: 删除信息
         * @Param [id]
         * <AUTHOR>
         */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生管理/删除学生")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        studentService.delete(id);
        return DeleteResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentVo>
     * @Description: 修改信息
     * @Param [studentDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生管理/更新学生")
    @PostMapping("update")
    public ResponseBo<StudentVo> update(@RequestBody @Validated(StudentDto.Update.class)  StudentDto studentDto) {
        return UpdateResponseBo.ok(studentService.updateStudent(studentDto));
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentVo>
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生管理/学生详情")
    @GetMapping("/{id}")
    public ResponseBo<StudentVo> detail(@PathVariable("id") Long id) {
        StudentVo data = studentService.findStudentById(id);
        return new ResponseBo<>(data);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentVo>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "绑定项目成员列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/查询")
    @PostMapping("getProjectRoleStaff")
    public ResponseBo<StudentProjectRoleStaffVo> getProjectRoleStaff(@RequestBody SearchBean<StudentProjectRoleStaffDto> page) {
        List<StudentProjectRoleStaffVo> datas = studentService.getProjectRoleStaff(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [studentVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "绑定项目成员新增", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生管理/新增")
    @PostMapping("addProjectRoleStaff")
    public ResponseBo addProjectRoleStaff(@RequestBody  @Validated(StudentProjectRoleStaffDto.Add.class) StudentProjectRoleStaffDto projectRoleStaffVo) {
        return SaveResponseBo.ok(studentService.addProjectRoleStaff(projectRoleStaffVo));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 查询应收计划附件
     * @Param [voSearchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "查询学生附件", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/查询附件")
    @PostMapping("getStudentMedia")
    public ResponseBo<MediaAndAttachedVo> getStudentMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> staffMedia = studentService.getMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 保存学生附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "保存应收计划附件")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生管理/附件保存接口")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> addMedia(@RequestBody @Validated(MediaAndAttachedDto.Add.class)  ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(studentService.addMedia(mediaAttachedVo));
    }

    /**
     * @param commentDto
     * @return
     * @
     */
    @ApiOperation(value = "编辑评论")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生管理/编辑评论")
    @PostMapping("editComment")
    public ResponseBo editComment(@RequestBody  @Validated(CommentDto.Add.class) CommentDto commentDto) {
        return SaveResponseBo.ok(studentService.editComment(commentDto));
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AgentContractVo>
     * @Description: 评论列表数据
     * @Param [searchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "评论列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/查询")
    @PostMapping("getComments")
    public ResponseBo<CommentVo> getComment(@RequestBody SearchBean<CommentDto> searchBean) {
        List<CommentVo> datas = studentService.getComments(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * feign调用 根据输入名称 模糊查询对应的学生id
     *
     * @param name
     * @return
     * @
     */
    @ApiIgnore
    @GetMapping("getStudentIds")
    public List<Long> getStudentIds(@RequestParam String name) {
        return studentService.getStudentIds(name);
    }


    /**
     * feign调用 根据输入学生id 模糊查询对应的名称
     *
     * @param id
     * @return
     * @
     */
    @ApiIgnore
    @GetMapping("getStudentNameById")
    public String getStudentNameById(@RequestParam Long id) {
        return studentService.getStudentNameById(id);
    }

    /**
     * feign调用 根据输入学生ids 查询对应的名称map
     *
     * @param ids
     * @return
     * @
     */
    @ApiIgnore
    @GetMapping("getStudentNameByIds")
    public Map<Long, String> getStudentNameByIds(@RequestBody Set<Long> ids) {
        return studentService.getStudentNameByIds(ids);
    }

    /**
     * 所属代理下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "學生下拉框", notes = "")
    @GetMapping("getStudentList")
    public ResponseBo<BaseSelectEntity> getStudentList(@RequestParam("companyId") Long companyId) {
        List<BaseSelectEntity> datas = studentService.getStudentList(companyId);
        return new ListResponseBo<>(datas);
    }

    /**
     * 学生业务状态下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学生业务状态下拉框数据", notes = "")
    @GetMapping("getConditionTypeSelect")
    public ResponseBo getConditionTypeSelect() {
        return new ListResponseBo<>(ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.STUDENT_BUSINESS_STATUS));
    }


    /**
     * 导出学生信息Excel
     *
     * @return
     * @
     */
    @ApiOperation(value = "导出学生信息Excel", notes = "")
    @PostMapping("/exportStudentInfoExcel")
    @ResponseBody
    public void exportStudentInfoExcel(HttpServletResponse response, @RequestBody @Validated StudentExportDto studentExportDto) {
        CommonUtil.ok(response);
        studentService.exportStudentInfoExcel(studentExportDto.getStudentVo(), studentExportDto.getFocExportVos());
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "导出学生信息Excel表头选项")
    @GetMapping("/getStudentOptions")
    public ListResponseBo<FocExportVo> getStudentOptions() throws Exception {
       return new ListResponseBo<>(studentService.getStudentOptions());
    }


    /**
     * 学生步骤状态统计
     *
     * @Date 10:07 2021/7/21
     * <AUTHOR>
     */
    @ApiOperation(value = "学生步骤状态数据列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/学生状态数据列表")
    @PostMapping("getStudentsStepState")
    public ResponseBo<StudentOfferItemStatisticalStatusVo> getStudentsStepState(@RequestBody StudentOfferItemStatisticalStatusDto studentOfferItemStatisticalStatusDto) {
        if (!SecureUtil.validateCompany(studentOfferItemStatisticalStatusDto.getCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        studentOfferItemStatisticalStatusDto.setIsAdmin(false);
        List<StudentOfferItemStatisticalStatusVo> studentOfferItemStatisticalStatusVoList = studentService.getStudentsStepStateNew(studentOfferItemStatisticalStatusDto);
        return new ListResponseBo<>(studentOfferItemStatisticalStatusVoList);
    }

    @ApiOperation(value = "学生最终申请状态统计", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/学生最终申请状态统计")
    @PostMapping("getCurrentStudentApplicationStatus")
    public ResponseBo<CurrentStudentApplicationStatusVo> getCurrentStudentApplicationStatus(@RequestBody CurrentStudentApplicationStatusListDto currentStudentApplicationStatusListDto) {
        if (!SecureUtil.validateCompany(currentStudentApplicationStatusListDto.getCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        List<CurrentStudentApplicationStatusVo> currentStudentApplicationStatusVoList = studentService.getCurrentStudentApplicationStatus(currentStudentApplicationStatusListDto);
        return new ListResponseBo<>(currentStudentApplicationStatusVoList);
    }

    @ApiOperation(value = "我的学生申请状态统计", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/我的学生申请状态统计")
    @PostMapping("getCurrentStudentApplicationStatusStatistics")
    public ResponseBo<CurrentStudentApplicationStatusStatisticsVo> getCurrentStudentApplicationStatusStatistics(@RequestBody CurrentStudentApplicationStatusStatisticsDto currentStudentApplicationStatusListVo) {
        if (!SecureUtil.validateCompany(currentStudentApplicationStatusListVo.getCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        List<CurrentStudentApplicationStatusStatisticsVo> currentStudentApplicationStatusStatistics = studentService.getCurrentStudentApplicationStatusStatistics(currentStudentApplicationStatusListVo);
        return new ListResponseBo<>(currentStudentApplicationStatusStatistics);
    }

    /**
     * 展示用学生步骤状态数据列表
     *
     * @Date 10:07 2021/7/21
     * <AUTHOR>
     */
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "展示用学生步骤状态数据列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/学生状态数据列表")
    @GetMapping("getStudentsStepStateNew")
    public ResponseBo<StudentOfferItemStatisticalStatusVo> getStudentsStepStateNew(@RequestParam("companyId") Long companyId, HttpServletResponse response) {
        StudentOfferItemStatisticalStatusDto studentOfferItemStatisticalStatusDto = new StudentOfferItemStatisticalStatusDto();
        studentOfferItemStatisticalStatusDto.setCompanyId(companyId);
        studentOfferItemStatisticalStatusDto.setIsAdmin(true);
        List<StudentOfferItemStatisticalStatusVo> studentOfferItemStatisticalStatusVoList = studentService.getStudentsStepState(studentOfferItemStatisticalStatusDto);
        return new ListResponseBo<>(studentOfferItemStatisticalStatusVoList);
    }

    /**
     * 验证学生唯一性
     *
     * @Date 10:07 2021/7/21
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "验证学生唯一性", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/验证学生唯一性")
    @PostMapping("validateStudent")
    public ResponseBo validateStudent(@RequestBody StudentDto studentDto) {
        Long data = studentService.validateStudent(studentDto);
        return new ResponseBo(data);
    }

    /**
     * 验证电话唯一性
     *
     * @Date 10:07 2021/7/21
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "验证电话唯一性", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/验证电话唯一性")
    @PostMapping("validatePhone")
    public ResponseBo validatePhone(@RequestParam(value = "studentId", required = false) Long studentId, @RequestParam("phone") String phone) {
        Boolean data = studentService.validatePhone(studentId, phone);
        return new ResponseBo(data);
    }

    /**
     * 验证邮箱唯一性
     *
     * @Date 10:07 2021/7/21
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "验证邮箱唯一性", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/验证邮箱唯一性")
    @PostMapping("validateEmail")
    public ResponseBo validateEmail(@RequestParam(value = "studentId", required = false) Long studentId, @RequestParam("email") String email) {
        Boolean data = studentService.validateEmail(studentId, email);
        return new ResponseBo(data);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.InstitutionProviderVo>
     * @Description: 学生留学住宿列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "学生留学住宿列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/学生留学住宿列表数据")
    @PostMapping("getStudentAccommodationDatas")
    public ResponseBo<StudentAccommodationVo> getStudentAccommodationDatas(@RequestBody SearchBean<StudentAccommodationDto> page) {
        List<StudentAccommodationVo> datas = studentService.getStudentAccommodationList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.InstitutionProviderVo>
     * @Description: 学生留学保险列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "学生留学保险列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/学生留学保险列表数据")
    @PostMapping("getStudentInsuranceDatas")
    public ResponseBo<StudentInsuranceVo> getStudentInsuranceDatas(@RequestBody SearchBean<StudentInsuranceDto> page) {
        List<StudentInsuranceVo> datas = studentService.getStudentInsuranceList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    @ApiOperation(value = "学生留学服务费数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/学生留学服务费数据")
    @PostMapping("getStudentServiceFeeDatas")
    public ResponseBo<StudentServiceFeeVo> getStudentServiceFeeDatas(@Validated @RequestBody SearchBean<StudentServiceFeeDto> page) {
        return studentServiceFeeService.datas(page.getData(), page);
    }



    /**
     * 学历情况备注-项目说明
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学历情况备注-项目说明下拉", notes = "")
    @GetMapping("getEducationProjectSelect")
    public ResponseBo getEducationProjectSelect() {
        return new ListResponseBo<>(ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.EDUCATION_PROJECT));
    }

    /**
     * 学历情况备注-学位情况
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学历情况备注-学位情况下拉", notes = "")
    @GetMapping("getEducationDegreeSelect")
    public ResponseBo getEducationDegreeSelect() {
        return new ListResponseBo<>(ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.EDUCATION_DEGREE));
    }

    /**
     * @ Description :
     * @ Param [studentName, value, type]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation("检测是否存在相同学生")
    @GetMapping("getIsExistStudent")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<ExistStudentVo> getIsExistStudent(@RequestParam("studentName") String studentName,
                                                        @RequestParam(value = "id", required = false) Long id,
                                                        @RequestParam(value = "email", required = false) String email,
                                                        @RequestParam(value = "mobile", required = false) String mobile,
                                                        @RequestParam(value = "passpost", required = false) String passpost,
                                                        @RequestParam(value = "birthday") String birthday,
                                                        @RequestParam(value = "companyIds", required = false) String companyIds) {
        return new ResponseBo<>(studentService.getIsExistStudent(studentName, email, mobile, passpost, birthday, id, companyIds));
    }

    @ApiOperation(value = "获取对应的业务学生数据", notes = "用于学生资源详情中，绑定业务学生")
    @GetMapping("getStudentByBoundBusinessStudent")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<ExistStudentByBoundVo> getStudentByBoundBusinessStudent(@RequestParam(value = "companyId") Long companyId,
                                                                              @RequestParam(value = "studentName") String studentName,
                                                                              @RequestParam(value = "birthday") String birthday) {
        return new ListResponseBo<>(studentService.getStudentByBoundBusinessStudent(companyId, studentName, birthday));
    }

    /**
     * @ Description :
     * @ Param [studentName]
     * @ return com.get.common.result.ResponseBo
     * @ author Hardy
     */
    @ApiOperation("学生名称转拼音")
    @GetMapping("getNameToPinyin")
    @VerifyPermission(IsVerify = false)
    public ResponseBo getNameToPinyin(@RequestParam("studentName") String studentName) {
        PinyinNameVo pinYin = studentService.getNameToPinyin(studentName);
        return new ResponseBo(pinYin);
    }

    @ApiOperation("高中成绩枚举下拉")
    @VerifyPermission(IsVerify = false)
    @GetMapping("getGrades")
    public ResponseBo getHighSchoolGrades() {
        return new ListResponseBo<>(ProjectExtraEnum.enums2Arrays(ProjectExtraEnum.HIGH_SCHOOL_GRADES));
    }

    @ApiOperation("本科成绩枚举下拉")
    @VerifyPermission(IsVerify = false)
    @GetMapping("getBachelorGrades")
    public ResponseBo getBachelorGrades() {
        return new ListResponseBo<>(ProjectExtraEnum.enums2Arrays(ProjectExtraEnum.UNDERGRADUATE_ACHIEVEMENT));
    }

    @ApiOperation(value = "一键入学失败")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生管理/一键入学失败")
    @PostMapping("batchEnrolFailure")
    public ResponseBo batchEnrolFailure(@RequestParam("fkStudentId") Long fkStudentId,
                                        @RequestParam("reasonId") Long reasonId,
                                        @RequestParam(value = "reason", required = false) String reason,
                                        @RequestParam(value = "studentOfferId", required = false) Long studentOfferId) {
        studentService.batchEnrolFailure(fkStudentId, reasonId, reason,studentOfferId);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "一键激活失败")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生管理/一键激活失败")
    @PostMapping("batchActivateEnrolFailure")
    public ResponseBo batchActivateEnrolFailure(@RequestParam("studentId") Long studentId,
                                        @RequestParam(value = "studentOfferId", required = false) Long studentOfferId) {
        studentService.batchActivateEnrolFailure(studentId,studentOfferId);
        return ResponseBo.ok();
    }

    /**
     * 学生是否完善护照信息
     *
     * @param fkStudentId
     * @return
     * @throws
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学生是否完善护照信息")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生管理/学生是否完善护照信息")
    @GetMapping("hasPassportNum")
    public ResponseBo hasPassportNum(@RequestParam("fkStudentId") Long fkStudentId) {
        return new ResponseBo(studentService.hasPassportNum(fkStudentId));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 代理附件类型
     * @Param []
     * <AUTHOR>
     */
    @ApiOperation(value = "成绩测试类型", notes = "")
    @VerifyPermission(IsVerify = false)
    @GetMapping("getTestTypeSelect")
    public ResponseBo getTestTypeSelect() {
        List<Map<String, Object>> datas = studentService.getTestTypeSelect();
        return new ListResponseBo<>(datas);
    }

    @ApiOperation(value = "获取学生绑定信息",notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/获取学生绑定信息")
    @GetMapping("getStudentAgentBindingByStudent")
    public ResponseBo<StudentAgentBindingVo> getStudentAgentBindingByStudent(@RequestParam("fkStudentNum") String fkStudentNum){
        StudentAgentBindingVo datas = studentService.getStudentAgentBindingByStudent(fkStudentNum);
        return new ResponseBo<>(datas);
    }

    @ApiOperation(value = "根据学生名称获取学生绑定信息",notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/获取学生绑定信息")
    @PostMapping("getStudentAgentBindingByStudentName")
    @VerifyLogin(IsVerify = false)
    public ResponseBo<StudentAgentBindingNewVo> getStudentAgentBindingByStudentName(@Validated @RequestBody SearchBean<StudentInfoDto> page){
        List<StudentAgentBindingNewVo> datas = studentService.getStudentAgentBindingByStudentName(page.getData(),page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas,p);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentVo>
     * @Description: 批量修改学生绑定信息
     * @Param [studentVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量修改学生绑定信息", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生管理/批量修改学生绑定信息")
    @PostMapping("updateStudentAgentBinding")
    public ResponseBo updateStudentAgentBinding(@RequestBody StudentAgentBindingDto studentAgentBindingDto) {
        studentService.updateStudentAgentBinding(studentAgentBindingDto);
        return UpdateResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取学生所有毕业国家", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/获取学生所有毕业国家")
    @GetMapping("getGraduatedCountrySelect/{companyId}")
    public ResponseBo<BaseSelectEntity> getStudentAllGraduatedCountrySelect(@PathVariable("companyId")Long companyId) {
        return new ListResponseBo<>(studentService.getStudentAllGraduatedCountrySelect(companyId));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取学生所有毕业国家对应的周省", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/获取学生所有毕业国家对应的周省")
    @GetMapping("getStudentGraduateCountryMpStateSelect/{companyId}/{id}")
    public ResponseBo<BaseSelectEntity> getStudentGraduateCountryMpStateSelect(@PathVariable("id")Long id,@PathVariable("companyId")Long companyId) {
        return new ListResponseBo<>(studentService.getStudentGraduateCountryMpStateSelect(id,companyId));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取学生毕业院校下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/获取学生毕业院校下拉")
    @GetMapping("getStudentGraduateSchool/{companyId}/{countryId}/{stateId}")
    public ResponseBo<BaseSelectEntity> getStudentGraduateSchool(@PathVariable("companyId")Long companyId,
                                                                 @PathVariable("countryId")Long countryId,@PathVariable("stateId")Long stateId) {
        return new ListResponseBo<>(studentService.getStudentGraduateSchool(companyId,countryId,stateId));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取学生申请计划国家下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/获取学生申请计划国家下拉")
    @GetMapping("getStudentStudyPlanCountrySelect/{companyId}")
    public ResponseBo<BaseSelectEntity> getStudentStudyPlanCountrySelect(@PathVariable("companyId")Long companyId) {
        return new ListResponseBo<>(studentService.getStudentStudyPlanCountrySelect(companyId));
    }
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取学生申请计划院校下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/获取学生申请计划院校下拉")
    @GetMapping("getStudentStudyPlanSchoolSelect/{companyId}/{id}")
    public ResponseBo<BaseSelectEntity> getStudentStudyPlanSchoolSelect(@PathVariable("id")Long id,@PathVariable("companyId")Long companyId) {
        return new ListResponseBo<>(studentService.getStudentStudyPlanSchoolSelect(companyId,id));
    }


    @ApiOperation(value = "获取学生课程统计信息", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/获取学生课程统计信息")
    @PostMapping("getStudentCourseStatisticsInfoList")
    public ResponseBo getStudentCourseStatisticsInfoList(@RequestBody SearchBean<StudentCourseStatisticsSearchDto> searchBean) {
        StudentCourseStatisticsListDto statisticsListVo = studentService.getStudentCourseStatisticsInfoList(searchBean.getData(), searchBean);
        Page<Object> page = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ResponseBo<>(statisticsListVo,page);
    }

    @ApiOperation(value = "获取学历下拉")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "获取学历下拉")
    @GetMapping("getEducationDropDown")
    @VerifyLogin(IsVerify = false)
    public ListResponseBo<BaseSelectEntity> getEducationDropDown(){
        return new ListResponseBo<>(studentService.getEducationDropDown());
    }

    @ApiOperation(value = "获取学生毕业背景信息", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/获取学生毕业背景信息")
    @PostMapping("getStudentGraduationBackgroundInfo")
    @VerifyLogin(IsVerify = false)
    public ResponseBo<StudentGraduationBackgroundVo> getStudentGraduationBackgroundInfo(@RequestBody StudentCourseStatisticsSearchDto statisticsSearchVo) {
        return new ResponseBo<>(studentService.getStudentGraduationBackgroundInfo(statisticsSearchVo));
    }


    @ApiOperation(value = "学生课程信息导出", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/学生课程信息导出")
    @PostMapping("exportStudentCourseStatisticsInfo")
    public void exportStudentCourseStatisticsInfo(@RequestBody @Validated StudentCourseStatisticsSearchDto vo, HttpServletResponse response) {
        studentService.exportStudentCourseStatisticsInfo(vo,response);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据学生邮箱匹配学生列表信息", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/根据学生邮箱匹配学生列表信息")
    @GetMapping("getStudentInfoByEmail")
    public ResponseBo<StudentAgentEmailVo> getStudentInfoByEmail(@RequestParam("email")String email, @RequestParam("companyId")Long companyId){
        return new ListResponseBo<>(studentService.getStudentInfoByEmail(email,companyId));
    }

    @ApiOperation(value = "修改学生护照编号", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生管理/修改学生护照编号")
    @PostMapping("updatePassportNum")
    public ResponseBo updatePassportNum(@RequestParam("id") Long id,@RequestParam("passportNum") String passportNum) {
        studentService.updatePassportNum(id,passportNum);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "学生信息合并", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生管理/学生信息合并")
    @PostMapping("mergeStudentInformation")
    public SaveResponseBo mergeStudentInformation(@RequestParam("mergedStudentId") Long mergedStudentId,@RequestParam("targetStudentId") Long targetStudentId
                                    ,@RequestParam("stuSource") String stuSource) {
        return studentService.mergeStudentInformation(mergedStudentId,targetStudentId,stuSource);
    }

    @ApiOperation(value = "通过学生编号获取学生", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生管理/通过学生编号获取学生")
    @GetMapping("getStudentByNum")
    public ResponseBo<StudentVo> getStudentByNum(@RequestParam("num") String num){
        return new ResponseBo<>(studentService.getStudentByNum(num));
    }

    @ApiOperation(value = "学生issueId互换", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生管理/学生issueId互换")
    @PostMapping("mergeStudentIssueId")
    public ResponseBo mergeStudentIssueId(@RequestParam("mergedStudentId") Long mergedStudentId,@RequestParam("targetStudentId") Long targetStudentId) {
        studentService.mergeStudentIssueId(mergedStudentId,targetStudentId);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentVo>
     * @Description: 审批列表获取学生(无权限)
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "审批列表获取学生")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/审批列表获取学生")
    @PostMapping("getClientStudent")
    public ResponseBo<StudentVo> getClientStudent(@RequestBody @Validated SearchBean<ClientStudentDto> searchBean) {
        List<StudentVo> datas = studentService.getClientStudent(searchBean.getData(),searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "验证共享链接是否必填",notes = "true必填/false非必填")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/验证共享链接是否必填")
    @PostMapping("validateShareParthRequired")
    public ResponseBo<Boolean> validateShareParthRequired() {
        Boolean data = studentService.validateShareParthRequired();
        return new ResponseBo<>(data);
    }

    //获取收到申请资料时间配置
    @ApiOperation(value = "获取收到申请资料时间配置", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/获取收到申请资料时间配置")
    @GetMapping("getReceiveApplyDataTimeConfig")
    public ResponseBo<ReceiveApplyDataTimeConfigVo> getReceiveApplyDataTimeConfig(@RequestParam("fkStudentId") Long fkStudentId) {
        return new ResponseBo<>(studentService.getReceiveApplyDataTimeConfig(fkStudentId));
    }

    //编辑收到申请资料时间
    @ApiOperation(value = "编辑收到申请资料时间", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生管理/编辑收到申请资料时间")
    @PostMapping("editReceiveApplyDataTime")
    public ResponseBo editReceiveApplyDataTime(@RequestBody @Validated ReceiveApplyDataTimeDto vo) {
        studentService.editReceiveApplyDataTimeConfig(vo);
        return ResponseBo.ok();
    }


    @ApiOperation(value = "获取学生下拉框数据（中英名称/生日/学生号）", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生管理/获取学生下拉框数据（中英名称/生日/学生号）")
    @PostMapping("getStudentSelect")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<BaseSelectEntity> getStudentSelect(@RequestParam(value = "name", required = false) String name, @RequestParam(value = "studentId", required = false) Long studentId,@RequestBody List<Long> companyIds) {
        List<BaseSelectEntity> datas = studentService.getStudentSelect(name, companyIds, studentId);
        return new ListResponseBo<>(datas);
    }


    @ApiOperation(value = "AI获取学生信息", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "AI获取学生信息")
    @PostMapping("getAiStudentInfo")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<AiStudentDto> getAiStudentInfo(@RequestBody AiStudentInfoDto aiStudentInfoDto) {
        List<AiStudentDto> apiStudentInfoVo = studentService.getAiStudentInfo(aiStudentInfoDto.getStudentName());
        return new ListResponseBo(apiStudentInfoVo);
    }

}
