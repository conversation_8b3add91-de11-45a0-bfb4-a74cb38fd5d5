package com.get.insurancecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author:Oliver
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:保险产品类型
 */
@Data
@TableName("m_insurance_order_settlement")
public class InsuranceOrderSettlement extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "保险订单Id")
    private Long fkInsuranceOrderId;

    @ApiModelProperty(value = "应付计划Id")
    private Long fkPayablePlanId;

    @ApiModelProperty(value = "付款单子项Id")
    private Long fkPaymentFormItemId;

    @ApiModelProperty(value = "代理提交结算批次编号")
    private String fkNumOptBatch;

    @ApiModelProperty(value = "0待确认/1已确认/2代理确认/3财务确认/4结算完成")
    private Integer statusSettlement;
}

