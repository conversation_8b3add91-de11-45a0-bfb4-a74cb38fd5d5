package com.get.permissioncenter.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dto.query.InstitutionQueryDto;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.dao.RInstitutionPermissionGroupInstitutionMapper;
import com.get.permissioncenter.dto.RInstitutionPermissionGroupInstitutionDto;
import com.get.permissioncenter.entity.RInstitutionPermissionGroupInstitution;
import com.get.permissioncenter.service.RInstitutionPermissionGroupInstitutionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 学校权限和学校关系表service层
 */
@Service("permissionRInstitutionPermissionGroupInstitutionService")
public class RInstitutionPermissionGroupInstitutionServiceImpl extends ServiceImpl<RInstitutionPermissionGroupInstitutionMapper, RInstitutionPermissionGroupInstitution> implements RInstitutionPermissionGroupInstitutionService {

    private final RInstitutionPermissionGroupInstitutionMapper rInstitutionPermissionGroupInstitutionMapper;
    private final UtilService utilService;
    private final IInstitutionCenterClient institutionCenterClient;

    public RInstitutionPermissionGroupInstitutionServiceImpl(RInstitutionPermissionGroupInstitutionMapper rInstitutionPermissionGroupInstitutionMapper, UtilService utilService, IInstitutionCenterClient institutionCenterClient) {
        this.rInstitutionPermissionGroupInstitutionMapper = rInstitutionPermissionGroupInstitutionMapper;
        this.utilService = utilService;
        this.institutionCenterClient = institutionCenterClient;
    }



    /**
     * 新增学校权限和学校关系
     * @param rInstitutionPermissionGroupInstitutionDto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> addMInstitutionPermissionGroupAndInstitution(RInstitutionPermissionGroupInstitutionDto rInstitutionPermissionGroupInstitutionDto) {
        if (GeneralTool.isEmpty(rInstitutionPermissionGroupInstitutionDto.getFkInstitutionPermissionGroupId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("institution_permission_group_id"));
        }
        if (GeneralTool.isEmpty(rInstitutionPermissionGroupInstitutionDto.getFkInstitutionIds())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("bridge_schoolId_empty"));
        }
        // 构建批量插入数据
        List<RInstitutionPermissionGroupInstitution> entityList = rInstitutionPermissionGroupInstitutionDto.getFkInstitutionIds().stream()
                .map(institutionId -> {
                    RInstitutionPermissionGroupInstitution institutionPermissionGroupInstitution = BeanCopyUtils.objClone(rInstitutionPermissionGroupInstitutionDto, RInstitutionPermissionGroupInstitution::new);
                    institutionPermissionGroupInstitution.setFkInstitutionId(institutionId);
                    utilService.setCreateInfo(institutionPermissionGroupInstitution);
                    return institutionPermissionGroupInstitution;
                })
                .collect(Collectors.toList());

        // 批量插入
        boolean success = saveBatch(entityList);
        if (!success) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }

        // 返回id
        return entityList.stream()
                .map(RInstitutionPermissionGroupInstitution::getId)
                .collect(Collectors.toList());
    }

    @Override
    public ResponseBo dataList(SearchBean<InstitutionQueryDto> institutionQueryDto) {
        List<Long> fkAreaCountryIdList = SecureUtil.getCountryIds();
        InstitutionQueryDto institutionQuery = institutionQueryDto.getData();
        if (GeneralTool.isEmpty(institutionQuery.getIsBind())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("is_bind_null"));
        }
        if (GeneralTool.isEmpty(institutionQuery.getFkInstitutionPermissionGroupId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("institution_permission_group_id"));
        }
        if (GeneralTool.isEmpty(institutionQuery.getCountryIds())){
            institutionQuery.setCountryIds(fkAreaCountryIdList);
        }

        Result<ResponseBo> institution = institutionCenterClient.getInstitutionByInstitutionPermissionGroup(institutionQueryDto);

        if (! (institution.isSuccess()) && GeneralTool.isEmpty(institution.getData())){
            return new ResponseBo<>(null);
        }
        ResponseBo data = institution.getData();
        return data;
    }


    /**
     * 删除
     * @param id
     */
    @Override
    public void removeById(Long id) {
        if (GeneralTool.isEmpty(id)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        RInstitutionPermissionGroupInstitution rInstitutionPermissionGroupInstitution = rInstitutionPermissionGroupInstitutionMapper.selectById(id);
        if (GeneralTool.isEmpty(rInstitutionPermissionGroupInstitution)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        rInstitutionPermissionGroupInstitutionMapper.deleteById(id);

    }




}

