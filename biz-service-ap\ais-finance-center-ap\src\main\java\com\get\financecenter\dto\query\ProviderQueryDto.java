package com.get.financecenter.dto.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ProviderQueryDto {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 供应商类型Id
     */
    @ApiModelProperty(value = "供应商类型Id")
    private Long fkProviderTypeId;

    /**
     * 供应商名称/编号搜索关键字
     */
    @ApiModelProperty(value = "供应商名称/编号搜索关键字")
    private String keyWord;

    /**
     * 创建时间(开始)
     */
    @ApiModelProperty("创建时间(开始)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;

    /**
     * 创建时间(结束)
     */
    @ApiModelProperty("创建时间(结束)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;


    /**
     * 区域所在国家Id
     */
    @ApiModelProperty(value = "区域所在国家Id")
    private Long fkAreaCountryId;

    /**
     * 区域所在州省Id
     */
    @ApiModelProperty(value = "区域所在州省Id")
    private Long fkAreaStateId;

    /**
     * 区域所在城市Id
     */
    @ApiModelProperty(value = "区域所在城市Id")
    private Long fkAreaCityId;

    //================================
    /**
     * 公司Ids
     */
    @ApiModelProperty(value = "公司Ids")
    private List<Long> fkCompanyIds;

}
