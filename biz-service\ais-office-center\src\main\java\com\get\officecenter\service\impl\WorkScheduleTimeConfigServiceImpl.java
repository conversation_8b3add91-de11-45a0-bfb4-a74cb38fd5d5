package com.get.officecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.officecenter.vo.WorkScheduleTimeConfigVo;
import com.get.officecenter.entity.WorkScheduleTimeConfig;
import com.get.officecenter.mapper.WorkScheduleTimeConfigMapper;
import com.get.officecenter.service.WorkScheduleTimeConfigService;
import com.get.officecenter.dto.WorkScheduleTimeConfigAddDto;
import com.get.officecenter.dto.WorkScheduleTimeConfigListDto;
import com.get.officecenter.dto.WorkScheduleTimeConfigUpdateDto;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2022/10/27
 * @TIME: 17:50
 * @Description:
 **/
@Service
public class WorkScheduleTimeConfigServiceImpl implements WorkScheduleTimeConfigService {
    @Resource
    private WorkScheduleTimeConfigMapper workScheduleTimeConfigMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private UtilService utilService;

    @Override
    public List<WorkScheduleTimeConfig> getWorkScheduleTimeConfigList(Long fkCompanyId){
        List<WorkScheduleTimeConfig> workScheduleTimeConfigs = workScheduleTimeConfigMapper.selectList(Wrappers.<WorkScheduleTimeConfig>lambdaQuery()
                .eq(WorkScheduleTimeConfig::getFkCompanyId, fkCompanyId));
        List<WorkScheduleTimeConfig> list  = workScheduleTimeConfigs.stream().filter(t->GeneralTool.isNotEmpty(t.getFkDepartmentId())).collect(Collectors.toList());
        //判断部门时间设置数据是否设置了多个
        Map<Long, Long> timeConfigCollect = list.stream().collect(Collectors.groupingBy(WorkScheduleTimeConfig::getFkDepartmentId, Collectors.counting()));
        Iterator<Map.Entry<Long,Long>> it = timeConfigCollect.entrySet().iterator();
        List abnormalData = new ArrayList();
        while (it.hasNext()){
            Map.Entry<Long, Long> entry = it.next();
            if(entry.getValue()>1L){
                abnormalData.add(entry.getKey());
            }
        }
        if(GeneralTool.isNotEmpty(abnormalData)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("workScheduleTimeConfig_duplication")+":"+abnormalData);
        }
        return workScheduleTimeConfigs;
    }

    /**
     * 查询工作时间设置
     *
     * @Date 12:47 2022/11/7
     * <AUTHOR>
     */
    @Override
    public List<WorkScheduleTimeConfigVo> getWorkScheduleTimeConfigs(WorkScheduleTimeConfigListDto data, SearchBean<WorkScheduleTimeConfigListDto> page) {
        LambdaQueryWrapper<WorkScheduleTimeConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(data.getFkCompanyId())) {
            lambdaQueryWrapper.eq(WorkScheduleTimeConfig::getFkCompanyId, data.getFkCompanyId());
        }
        if (GeneralTool.isNotEmpty(data.getFkDepartmentId())) {
            lambdaQueryWrapper.eq(WorkScheduleTimeConfig::getFkDepartmentId, data.getFkDepartmentId());
        }
        IPage<WorkScheduleTimeConfig> iPage = workScheduleTimeConfigMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<WorkScheduleTimeConfig> workScheduleTimeConfigList = iPage.getRecords();
        page.setAll((int) iPage.getTotal());

        List<WorkScheduleTimeConfigVo> datas = workScheduleTimeConfigList.stream().map(workScheduleTimeConfig -> BeanCopyUtils.objClone(workScheduleTimeConfig, WorkScheduleTimeConfigVo::new)).collect(Collectors.toList());
        //公司名称
        Set<Long> fkCompanyIds = datas.stream().map(WorkScheduleTimeConfigVo::getFkCompanyId).collect(Collectors.toSet());
        //部门名称
        Set<Long> fFkDepartmentId = datas.stream().map(WorkScheduleTimeConfigVo::getFkDepartmentId).collect(Collectors.toSet());
        Map<Long, String> companyNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkCompanyIds)) {
            Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(fkCompanyIds);
            if (result.isSuccess() && result.getData() != null) {
                companyNamesByIds = result.getData();
            }
        }
        Map<Long, String> departmentNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fFkDepartmentId)) {
            Result<Map<Long, String>> result = permissionCenterClient.getDepartmentNamesByIds(fFkDepartmentId);
            if (result.isSuccess() && result.getData() != null) {
                departmentNamesByIds = result.getData();
            }
        }
        for (WorkScheduleTimeConfigVo workScheduleTimeConfigVo : datas) {
            workScheduleTimeConfigVo.setFkCompanyName(companyNamesByIds.get(workScheduleTimeConfigVo.getFkCompanyId()));
            workScheduleTimeConfigVo.setFkDepartmentName(departmentNamesByIds.get(workScheduleTimeConfigVo.getFkDepartmentId()));
        }
        return datas;
    }

    /**
     * 工作时间设置详情
     *
     * @Date 12:54 2022/11/7
     * <AUTHOR>
     */
    @Override
    public WorkScheduleTimeConfigVo findWorkScheduleTimeConfigById(Long id) {
        WorkScheduleTimeConfig workScheduleTimeConfig = workScheduleTimeConfigMapper.selectById(id);
        WorkScheduleTimeConfigVo workScheduleTimeConfigVo = BeanCopyUtils.objClone(workScheduleTimeConfig, WorkScheduleTimeConfigVo::new);
        String companyName = permissionCenterClient.getCompanyNameById(workScheduleTimeConfigVo.getFkCompanyId()).getData();
        workScheduleTimeConfigVo.setFkCompanyName(companyName);
        if (GeneralTool.isNotEmpty(workScheduleTimeConfig.getFkDepartmentId())) {
            String departmentName = permissionCenterClient.getDepartmentNameById(workScheduleTimeConfig.getFkDepartmentId()).getData();
            workScheduleTimeConfigVo.setFkDepartmentName(departmentName);
        }
        return workScheduleTimeConfigVo;
    }

    /**
     * 新增工作时间设置
     *
     * @Date 12:50 2022/11/7
     * <AUTHOR>
     */
    @Override
    public void add(WorkScheduleTimeConfigAddDto workScheduleDateConfigVo) {
        WorkScheduleTimeConfig workScheduleTimeConfig = BeanCopyUtils.objClone(workScheduleDateConfigVo, WorkScheduleTimeConfig::new);
        Integer count = workScheduleTimeConfigMapper.getCount(workScheduleDateConfigVo.getFkCompanyId(),workScheduleDateConfigVo.getFkDepartmentId());
        if (count>0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("WORKING_TIME_SETTING_EXISTS"));
        }
        utilService.setCreateInfo(workScheduleTimeConfig);
        workScheduleTimeConfigMapper.insert(workScheduleTimeConfig);
    }


    /**
     * 更新工作时间设置
     *
     * @Date 12:50 2022/11/7
     * <AUTHOR>
     */
    @Override
    public void updateWorkScheduleTimeConfig(WorkScheduleTimeConfigUpdateDto workScheduleDateConfigVo) {
        WorkScheduleTimeConfig workScheduleTimeConfig = BeanCopyUtils.objClone(workScheduleDateConfigVo, WorkScheduleTimeConfig::new);
        WorkScheduleTimeConfig workScheduleTimeConfig1 = workScheduleTimeConfigMapper.selectById(workScheduleDateConfigVo.getId());
        if (workScheduleTimeConfig1 != null) {
            if (Objects.isNull(workScheduleTimeConfig1.getFkDepartmentId())) {
                workScheduleTimeConfig1.setFkDepartmentId(-1L);
            }
            if (Objects.isNull(workScheduleDateConfigVo.getFkDepartmentId())) {
                workScheduleDateConfigVo.setFkDepartmentId(-1L);
            }
            if (!workScheduleTimeConfig1.getFkCompanyId().equals(workScheduleDateConfigVo.getFkCompanyId())
                    || !workScheduleTimeConfig1.getFkDepartmentId().equals(workScheduleDateConfigVo.getFkDepartmentId())) {
                Integer count = workScheduleTimeConfigMapper.getCount(workScheduleDateConfigVo.getFkCompanyId(), workScheduleDateConfigVo.getFkDepartmentId());
                if (count > 0) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("WORKING_TIME_SETTING_EXISTS"));
                }
            }
            utilService.setUpdateInfo(workScheduleTimeConfig);
            workScheduleTimeConfigMapper.updateByIdWithNull(workScheduleTimeConfig);
        }
    }

    /**
     * 获取当前登录人部门对应的工作时间配置
     * @return
     */
    @Override
    public WorkScheduleTimeConfigVo getWorkScheduleTimeConfigDto(){
        List<WorkScheduleTimeConfig> timeConfigList = workScheduleTimeConfigMapper.selectList(Wrappers.<WorkScheduleTimeConfig>lambdaQuery()
                .eq(WorkScheduleTimeConfig::getFkCompanyId,SecureUtil.getStaffInfo().getFkCompanyId()));

        List<WorkScheduleTimeConfig> list = timeConfigList.stream().filter(t->GeneralTool.isNotEmpty(t.getFkDepartmentId())).collect(Collectors.toList());
        Map<Long, WorkScheduleTimeConfig> timeConfigMap = list.stream()
                .collect(Collectors.toMap(WorkScheduleTimeConfig::getFkDepartmentId, Function.identity()));
        //通过部门获取对应时间设定
        WorkScheduleTimeConfig workScheduleTimeConfig = timeConfigMap.get(SecureUtil.getStaffInfo().getFkDepartmentId());
        //部门未设置时间设定使用默认
        if(GeneralTool.isEmpty(workScheduleTimeConfig)){
            List<WorkScheduleTimeConfig> configs =  timeConfigList.stream()
                    .filter(c->GeneralTool.isEmpty(c.getFkDepartmentId()) && c.getFkCompanyId().equals(SecureUtil.getStaffInfo().getFkCompanyId()))
                    .collect(Collectors.toList());
            if(GeneralTool.isNotEmpty(configs)){
                workScheduleTimeConfig = configs.get(0);
            }
        }

        if(GeneralTool.isEmpty(workScheduleTimeConfig)){
            return null;
        }
        return BeanCopyUtils.objClone(workScheduleTimeConfig, WorkScheduleTimeConfigVo::new);
    }

    /**
     * 删除工作时间设定
     *
     * @Date 12:05 2022/11/21
     * <AUTHOR>
     */
    @Override
    public void delete(Long id) {
        workScheduleTimeConfigMapper.deleteById(id);
    }


}
