<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.PaymentApplicationFormMapper">


    <select id="getPayFormByData" resultType="com.get.financecenter.vo.PaymentApplicationFormVo">

        select * from `m_payment_application_form` where 1=1
        <if test='paymentApplicationFormVo.selectStatus=="2"'>
            <choose>
                <when test="businessKeys!=null and businessKeys.size()>0">
                    and id in
                    <foreach collection="businessKeys" item="businessKey" index="index" open="(" separator=","
                             close=")">
                        #{businessKey}
                    </foreach>
                </when>
                <otherwise>
                    and id in (null)
                </otherwise>
            </choose>
        </if>
        <if test="paymentApplicationFormVo.fkCompanyId!=null">
            and fk_company_id=#{paymentApplicationFormVo.fkCompanyId}
        </if>
        <if test="paymentApplicationFormVo.status!=null">
            and status=#{paymentApplicationFormVo.status}
        </if>
        <if test="paymentApplicationFormVo.gmtCreateUser!=null and paymentApplicationFormVo.gmtCreateUser!=''">
            and REPLACE(gmt_create_user,' ','') like REPLACE(concat('%',#{paymentApplicationFormVo.gmtCreateUser},'%'),' ','')
        </if>
        -- 改成公司多选
        <if test="paymentApplicationFormVo.fkCompanyIds != null and paymentApplicationFormVo.fkCompanyIds.size() > 0">
            and fk_company_id in
            <foreach collection="paymentApplicationFormVo.fkCompanyIds" item="fkCompanyId" index="index" open="(" separator="," close=")">
                #{fkCompanyId}
            </foreach>
        </if>
        <if test="paymentApplicationFormVo.fkOfficeId!=null">
            and fk_office_id=#{paymentApplicationFormVo.fkOfficeId}
        </if>
        <if test="paymentApplicationFormVo.fkDepartmentId!=null">
            and fk_department_id=#{paymentApplicationFormVo.fkDepartmentId}
        </if>
        <if test="num!=null and num!=''">
            and num like #{num}
        </if>
        <if test="paymentApplicationFormVo.startTime!=null">
            and DATE_FORMAT( gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{paymentApplicationFormVo.startTime}, '%Y-%m-%d' )
        </if>
        <if test="paymentApplicationFormVo.endTime!=null">
            and DATE_FORMAT( gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{paymentApplicationFormVo.endTime}, '%Y-%m-%d' )
        </if>
        <if test='paymentApplicationFormVo.selectStatus=="0"'>
            and gmt_create_user=#{userId}
        </if>
        order by gmt_create DESC
    </select>
    <select id="getExistParentId" resultType="java.lang.Boolean">
      select IFNULL(MAX(id),0) id from m_payment_application_form where fk_payment_application_form_id_revoke=#{id}
    </select>
    <select id="getPaymentApplicationFormTotalAmount" resultType="java.math.BigDecimal">
        select IFNULL(SUM(amount),0) amount from m_payment_application_form_item where fk_payment_application_form_id=#{id}
    </select>

</mapper>