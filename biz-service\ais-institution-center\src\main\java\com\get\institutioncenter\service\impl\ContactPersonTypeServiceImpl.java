package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.ContactPersonTypeMapper;
import com.get.institutioncenter.dto.ContactPersonTypeListDto;
import com.get.institutioncenter.dto.ContactPersonTypeUpdateDto;
import com.get.institutioncenter.entity.InisContactPersonType;
import com.get.institutioncenter.service.IContactPersonTypeService;
import com.get.institutioncenter.vo.ContactPersonTypeVo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2022/2/8 12:00
 * @verison: 1.0
 * @description:
 */
@Service
public class ContactPersonTypeServiceImpl extends BaseServiceImpl<ContactPersonTypeMapper, InisContactPersonType> implements IContactPersonTypeService {

    @Resource
    private ContactPersonTypeMapper contactPersonTypeMapper;
    @Resource
    private UtilService utilService;


    @Override
    public ContactPersonTypeVo findContactPersonTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InisContactPersonType contactPersonType = contactPersonTypeMapper.selectById(id);
        ContactPersonTypeVo contactPersonTypeVo = new ContactPersonTypeVo();
        BeanUtils.copyProperties(contactPersonType, contactPersonTypeVo);
        return contactPersonTypeVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(ValidList<ContactPersonTypeUpdateDto> contactPersonTypeVos) {
        if (GeneralTool.isEmpty(contactPersonTypeVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //获取最大排序(防止每次都要去查最大值，所以在外面查出一次，循环几次 就自增几次)
        Integer maxViewOrder = contactPersonTypeMapper.getMaxViewOrder();
        for (ContactPersonTypeUpdateDto contactPersonTypeVo : contactPersonTypeVos) {
            if (GeneralTool.isEmpty(contactPersonTypeVo.getId())) {
                if (validateAdd(contactPersonTypeVo)) {
                    InisContactPersonType contactPersonType = BeanCopyUtils.objClone(contactPersonTypeVo, InisContactPersonType::new);
                    contactPersonType.setViewOrder(maxViewOrder);
                    utilService.updateUserInfoToEntity(contactPersonType);
                    int i = contactPersonTypeMapper.insert(contactPersonType);
                    if (i < 0) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
                    }
                    maxViewOrder++;
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("type_already_exists"));
                }
            } else {
                if (validateUpdate(contactPersonTypeVo)) {
                    InisContactPersonType contactPersonType = BeanCopyUtils.objClone(contactPersonTypeVo, InisContactPersonType::new);
                    utilService.updateUserInfoToEntity(contactPersonType);
                    contactPersonTypeMapper.updateById(contactPersonType);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("type_already_exists"));
                }
            }

        }
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (findContactPersonTypeById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        contactPersonTypeMapper.deleteById(id);
    }

    @Override
    public ContactPersonTypeVo updateContactPersonType(ContactPersonTypeUpdateDto contactPersonTypeVo) {
        InisContactPersonType contactPersonType = BeanCopyUtils.objClone(contactPersonTypeVo, InisContactPersonType::new);
        if (contactPersonTypeVo == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_obj_null"));
        }
        InisContactPersonType result = contactPersonTypeMapper.selectById(contactPersonTypeVo.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_obj_null"));
        }
        if (validateUpdate(contactPersonTypeVo)) {
            utilService.updateUserInfoToEntity(contactPersonType);
            contactPersonTypeMapper.updateById(contactPersonType);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("type_already_exists"));
        }
        return findContactPersonTypeById(contactPersonType.getId());
    }

    @Override
    public List<ContactPersonTypeVo> getContactPersonTypes(ContactPersonTypeListDto contactPersonTypeVo, Page page) {
        LambdaQueryWrapper<InisContactPersonType> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(contactPersonTypeVo)) {
            if (GeneralTool.isNotEmpty(contactPersonTypeVo.getTypeName())) {
                wrapper.like(InisContactPersonType::getTypeName, contactPersonTypeVo.getTypeName());
            }
        }
        wrapper.orderByDesc(InisContactPersonType::getViewOrder);
        //获取分页数据
        IPage<InisContactPersonType> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<InisContactPersonType> contactPeopleTypes = pages.getRecords();
        page.setAll((int) pages.getTotal());

        List<ContactPersonTypeVo> convertDatas = new ArrayList<>();
        for (InisContactPersonType contactPersonType : contactPeopleTypes) {
            ContactPersonTypeVo contactPersonTypeDto = BeanCopyUtils.objClone(contactPersonType, ContactPersonTypeVo::new);
            convertDatas.add(contactPersonTypeDto);
        }
        return convertDatas;
    }

    @Override
    public List<ContactPersonTypeVo> getContactPersonTypes() {
        LambdaQueryWrapper<InisContactPersonType> wrapper = new LambdaQueryWrapper();
        wrapper.orderByDesc(InisContactPersonType::getViewOrder);
        List<InisContactPersonType> contactPersonTypes = contactPersonTypeMapper.selectList(wrapper);
        return contactPersonTypes.stream()
                .map(contactPersonType -> BeanCopyUtils.objClone(contactPersonType, ContactPersonTypeVo::new)).collect(Collectors.toList());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<ContactPersonTypeUpdateDto> contactPersonTypeVos) {
        if (GeneralTool.isEmpty(contactPersonTypeVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        InisContactPersonType ro = BeanCopyUtils.objClone(contactPersonTypeVos.get(0), InisContactPersonType::new);
        Integer oneorder = ro.getViewOrder();
        InisContactPersonType rt = BeanCopyUtils.objClone(contactPersonTypeVos.get(1), InisContactPersonType::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        contactPersonTypeMapper.updateById(ro);
        contactPersonTypeMapper.updateById(rt);
    }

    private boolean validateAdd(ContactPersonTypeUpdateDto contactPersonTypeVo) {
        LambdaQueryWrapper<InisContactPersonType> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InisContactPersonType::getTypeKey, contactPersonTypeVo.getTypeKey());
        List<InisContactPersonType> list = this.contactPersonTypeMapper.selectList(wrapper);
        return GeneralTool.isEmpty(list);
    }

    private boolean validateUpdate(ContactPersonTypeUpdateDto contactPersonTypeVo) {
        LambdaQueryWrapper<InisContactPersonType> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InisContactPersonType::getTypeKey, contactPersonTypeVo.getTypeKey());
        List<InisContactPersonType> list = this.contactPersonTypeMapper.selectList(wrapper);
        return list.size() <= 0 || list.get(0).getId().equals(contactPersonTypeVo.getId());
    }
}
