package com.get.salecenter.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.dto.StudentServiceFeeDto;
import com.get.salecenter.dto.StudentServiceFeeSummaryDto;
import com.get.salecenter.entity.StudentServiceFee;
import com.get.salecenter.vo.ServiceFeePayFormDetailVo;
import com.get.salecenter.vo.StudentServiceFeeSummaryVo;
import com.get.salecenter.vo.StudentServiceFeeVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface StudentServiceFeeMapper extends GetMapper<StudentServiceFee> {


    /**
     * 获取列表数据
     * @param iPage
     * @param studentServiceFeeDto
     * @return
     */
    List<StudentServiceFeeVo> datas(@Param("iPage") IPage<StudentServiceFeeVo> iPage, @Param("studentServiceFeeDto") StudentServiceFeeDto studentServiceFeeDto);


    /**
     * 获取目标对象信息列表
     * @param companyIds
     * @param keyWord
     * @return
     */
    List<StudentServiceFeeVo> getServiceFee(@Param("companyIds") List<Long> companyIds, @Param("keyWord") String keyWord);

    /**
     * 详情
     * @param id
     * @return
     */
    StudentServiceFeeVo findById(Long id);

    /**
     * 获取有服务费的学生id
     * @param targetName
     * @return
     */
    List<Long> getStudentIdsByTargetName(String targetName);


    /**
     * 获取公司服务费下的学生列表
     * @param companyId
     * @return
     */
    List<BaseSelectEntity> getStudentListByCompanyId(Long companyId);

    /**
     * 收款单获取学生（客户）类型的应收计划
     * @param targetId
     * @param receiptFormId
     * @param pageNumber
     * @param pageSize
     * @return
     */
    List<BaseSelectEntity> getStudentServiceFeeReceivablePlan(@Param("targetId") Long targetId,@Param("receiptFormId") Long receiptFormId,
                                                              @Param("pageNumber") Integer pageNumber,@Param("pageSize") Integer pageSize);

    /**
     * 服务费应收应付汇总
     *
     * @param iPage
     * @param studentServiceFeeSummaryDto
     * @param staffIds
     * @param isStudentAdmin
     * @param permissionGroupInstitutionIds
     * @param staffBoundBdIds
     * @return
     */
    @DS("saledb-doris")
    List<StudentServiceFeeSummaryVo> serviceFeeSummary(@Param("iPage") IPage<StudentServiceFeeSummaryVo> iPage,
                                                       @Param("studentServiceFeeSummaryDto") StudentServiceFeeSummaryDto studentServiceFeeSummaryDto,
                                                       @Param("staffFollowerIds") List<Long> staffIds,
                                                       @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                       @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                       @Param("staffBoundBdIds") List<Long> staffBoundBdIds);


    /**
     * 获取付款情况
     * @param itemIds
     * @return
     */
    List<ServiceFeePayFormDetailVo> getPaidAmountByIds(@Param("itemIds") List<Long> itemIds);

    /**
     * 获取留学服务费的学生id
     * @param ids
     * @return
     */
    List<Long> getServiceFeeStudentIdsByIds(@Param("ids") List<Long> ids);

    /**
     * 获取留学服务费的学生id
     * @param targetId
     * @return
     */
    Long getServiceFeeStudentIdsById(Long targetId);

    List<StudentServiceFeeVo> getServiceFeeByIds(List<Long> ids);

    /**
     * 部门下拉
     * @param companyId
     * @return
     */
    List<BaseSelectEntity> getDepartmentSelect(@Param("companyId")Long companyId);

    /**
     * 获取服务费对象名字
     * @param feeIds
     * @return
     */
    List<StudentServiceFeeVo> getFeeTargetName(@Param("feeIds") List<Long> feeIds);

    /**
     * 根据服务费id获取提供商id
     * @param feeIds
     * @return
     */
    List<Long> getServiceFeeProviderIdsByFeeIds(@Param("feeIds") List<Long> feeIds);

    StudentServiceFeeSummaryVo exportStudentServiceFeeInfo(@Param("studentServiceFeeSummaryDto") StudentServiceFeeSummaryDto studentServiceFeeSummaryDto,
                                                           @Param("staffFollowerIds") List<Long> staffIds,
                                                           @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                           @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                           @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    StudentServiceFeeSummaryVo getServiceFeeNumById(@Param("id") Long id);

    /**
     * 获取资源服务费学生
     * @param studentIds
     * @param countryId
     * @return
     */
    List<StudentServiceFee> getClientStudentServiceFee(@Param("studentIds") List<Long> studentIds,
                                                       @Param("countryId") Long countryId);

}
