<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.aisplatformcenter.mapper.MFeedbackOrderMapper">

    <resultMap id="BaseResultMap" type="com.get.aisplatformcenterap.entity.MFeedbackOrderEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkCompanyId" column="fk_company_id" jdbcType="BIGINT"/>
            <result property="fkPlatformId" column="fk_platform_id" jdbcType="BIGINT"/>
            <result property="fkPlatformCode" column="fk_platform_code" jdbcType="VARCHAR"/>
            <result property="fkPlatformCreateUserId" column="fk_platform_create_user_id" jdbcType="BIGINT"/>
            <result property="fkResourceKey" column="fk_resource_key" jdbcType="VARCHAR"/>
            <result property="num" column="num" jdbcType="VARCHAR"/>
            <result property="fkFeedbackOrderTypeId" column="fk_feedback_order_type_id" jdbcType="BIGINT"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="message" column="message" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="closeTime" column="close_time" jdbcType="TIMESTAMP"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="searchPage" resultType="com.get.aisplatformcenterap.vo.work.MFeedbackOrderVo">
            SELECT mFeedbackOrder.*,
                   company.num AS companyName,
                   mPlatform.name AS platformName,
                   uFeedbackOrderType.type_name,
                   (SELECT mStaff.name     FROM ais_platform_center.m_feedback_order_reply mFeedbackOrderReply
                    LEFT JOIN ais_permission_center.m_staff mStaff ON mFeedbackOrderReply.fk_staff_id=mStaff.id and  mStaff.is_active=1
                    WHERE mFeedbackOrderReply.fk_feedback_order_id=mFeedbackOrder.id AND mFeedbackOrderReply.fk_staff_id IS NOT NULL
                    ORDER BY mFeedbackOrderReply.gmt_create
                                           LIMIT 1
                                                 ) AS staffName,
                   (SELECT mFeedbackOrderReply.gmt_create     FROM ais_platform_center.m_feedback_order_reply mFeedbackOrderReply
                                           WHERE mFeedbackOrderReply.fk_feedback_order_id=mFeedbackOrder.id AND mFeedbackOrderReply.fk_staff_id IS NOT NULL
                    ORDER BY mFeedbackOrderReply.gmt_create
                    LIMIT 1
                   ) AS gmtReplyTime
            FROM ais_platform_center.m_feedback_order mFeedbackOrder
                   LEFT JOIN ais_platform_center.u_feedback_order_type uFeedbackOrderType ON mFeedbackOrder.fk_feedback_order_type_id=uFeedbackOrderType.id
                   LEFT JOIN ais_permission_center.m_company company ON mFeedbackOrder.fk_company_id=company.id
                   LEFT JOIN ais_platform_center.m_platform mPlatform ON mFeedbackOrder.fk_platform_code=mPlatform.code
                   LEFT JOIN app_partner_center.m_partner_user mPartnerUser ON mPartnerUser.id=mFeedbackOrder.fk_platform_create_user_id
                   LEFT JOIN ais_sale_center.m_agent mAgent ON mPartnerUser.fk_agent_id=mAgent.id
                   LEFT JOIN ais_sale_center.r_agent_staff rAgentStaff on rAgentStaff.fk_agent_id = mAgent.id and rAgentStaff.is_active =1
            WHERE 1=1

            <if test="query.orderType!=null  and query.orderType==1">

                <if test="query.staffFollowerIds!=null and query.staffFollowerIds.size()>0">
                    AND rAgentStaff.fk_staff_id  in
                    <foreach collection="query.staffFollowerIds" item="id" index="index" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </if>


            <if test="query.fkCompanyId!=null" >
                AND company.id = #{query.fkCompanyId }
            </if>
            <if test="query.fkPlatformId!=null" >
                AND mPlatform.id = #{query.fkPlatformId}


            </if>
            <if test="query.fkFeedbackOrderTypeId!=null" >
                AND uFeedbackOrderType.id = #{query.fkFeedbackOrderTypeId}


            </if>
            <if test="query.status!=null" >
                AND mFeedbackOrder.status = #{query.status}

            </if>
            <if test="query.title!=null and query.title != '' " >
                AND
                (
                        position(#{query.title,jdbcType=VARCHAR} IN mFeedbackOrder.title)
                            OR
                        position(#{query.title,jdbcType=VARCHAR} IN mFeedbackOrder.message)
                )
            </if>
            ORDER BY mFeedbackOrder.gmt_create DESC
    </select>
    <select id="getDetail" resultType="com.get.aisplatformcenterap.vo.work.MFeedbackOrderDetailVo">
        SELECT mFeedbackOrder.*,
               company.num AS companyName,
               mPlatform.name AS platformName,
               uFeedbackOrderType.type_name,
               mPartnerUser.fk_agent_id,
               mAgent.nature,
               systemUser.email
        FROM ais_platform_center.m_feedback_order mFeedbackOrder
                 LEFT JOIN ais_platform_center.u_feedback_order_type uFeedbackOrderType ON mFeedbackOrder.fk_feedback_order_type_id=uFeedbackOrderType.id
                 LEFT JOIN ais_permission_center.m_company company ON mFeedbackOrder.fk_company_id=company.id
                 LEFT JOIN ais_platform_center.m_platform mPlatform ON mFeedbackOrder.fk_platform_code=mPlatform.code
                 LEFT JOIN app_partner_center.m_partner_user mPartnerUser ON mPartnerUser.id=mFeedbackOrder.fk_platform_create_user_id
                 LEFT JOIN ais_sale_center.m_agent mAgent ON mPartnerUser.fk_agent_id=mAgent.id
                 LEFT JOIN app_system_center.system_user systemUser ON  mPartnerUser.fk_user_id=systemUser.id
        WHERE mFeedbackOrder.id=#{id}


    </select>
    <select id="getUFeedbackOrderType"
            resultType="com.get.aisplatformcenterap.entity.UFeedbackOrderTypeEntity">
        SELECT uFeedbackOrderType.* from ais_platform_center.u_feedback_order_type uFeedbackOrderType WHERE uFeedbackOrderType.fk_platform_code='PARTNER'
        ORDER BY uFeedbackOrderType.view_order
    </select>


</mapper>
