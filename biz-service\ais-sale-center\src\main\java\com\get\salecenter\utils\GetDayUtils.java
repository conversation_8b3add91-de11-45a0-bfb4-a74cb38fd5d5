package com.get.salecenter.utils;

import java.util.Calendar;
import java.util.Date;

/**
 * @author: Sea
 * @create: 2020/8/19 10:42
 * @verison: 1.0
 * @description: 日期加一天
 */
public class GetDayUtils {

    public static Date addDay(Date date, Long days) {
        //第一次即当前日期 直接返回
        if (days == 1) {
            return date;
        } else {
            //new一个Calendar类,把Date放进去
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            //实现日期加一操作,也就是明天
            calendar.add(Calendar.DATE, 1);
            return calendar.getTime();
        }

    }

    public static Date addDays(Date date, Long days) {
        //new一个Calendar类,把Date放进去
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        //实现日期加一操作,也就是明天
        for (int i = 0; i < days; i++) {
            calendar.add(Calendar.DATE, 1);
        }
        return calendar.getTime();

    }
}
