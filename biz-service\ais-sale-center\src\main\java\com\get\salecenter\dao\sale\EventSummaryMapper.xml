<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.EventSummaryMapper">
  <insert id="insert" parameterType="com.get.salecenter.entity.EventSummary" keyProperty="id" useGeneratedKeys="true">
    insert into u_event_summary (id, fk_company_id, event_summary, 
      view_order, gmt_create, gmt_create_user, 
      gmt_modified, gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkCompanyId,jdbcType=BIGINT}, #{eventSummary,jdbcType=VARCHAR}, 
      #{viewOrder,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.EventSummary" keyProperty="id" useGeneratedKeys="true">
    insert into u_event_summary
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkCompanyId != null">
        fk_company_id,
      </if>
      <if test="eventSummary != null">
        event_summary,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkCompanyId != null">
        #{fkCompanyId,jdbcType=BIGINT},
      </if>
      <if test="eventSummary != null">
        #{eventSummary,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select
      IFNULL(max(view_order)+1,0) view_order
    from
      u_event_summary
  </select>
  <select id="getEventSummaries" resultType="com.get.salecenter.vo.EventSummaryListVo">
    SELECT
      *
    FROM
      `u_event_summary`
    where 1=1
    <if test="eventSummaryListDto.fkCompanyId !=null">
      and fk_company_id = #{eventSummaryListDto.fkCompanyId}
    </if>
    <if test="eventSummaryListDto.eventSummary !=null and eventSummaryListDto.eventSummary !=''">
      and event_summary like concat("%",#{eventSummaryListDto.eventSummary},"%")
    </if>
    order by view_order desc
</select>
</mapper>