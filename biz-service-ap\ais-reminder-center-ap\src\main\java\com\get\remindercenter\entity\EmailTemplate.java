package com.get.remindercenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName("u_email_template")
public class EmailTemplate extends BaseEntity {

    @ApiModelProperty(value = "邮件模板Id")
    private Long id;

    @ApiModelProperty(value = "邮件模板Id（父模板引用）")
    private Long fkParentEmailTemplateId;

    @ApiModelProperty(value = "模板类型Key，必填")
    private String emailTypeKey;

    @ApiModelProperty(value = "电邮模板")
    private String emailTemplate;

    @ApiModelProperty(value = "电邮模板（英语版）")
    private String emailTemplateEn;

    @ApiModelProperty(value = "备注")
    private String remark;
}
