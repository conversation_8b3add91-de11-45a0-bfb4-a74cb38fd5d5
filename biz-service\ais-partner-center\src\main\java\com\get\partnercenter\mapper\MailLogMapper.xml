<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.partnercenter.mapper.MailLogMapper">


    <select id="selectMailLog" resultType="com.get.partnercenter.vo.MailLogVo">
        select l.*,
        ma.name as agent<PERSON>ame,
        s.name as fromUserName
        from log_mail_partner_user_account l
        INNER JOIN (
        <include refid="com.get.partnercenter.mapper.MPartnerUserMapper.agentPermissionSql"/>
        ) z ON l.fk_agent_id=z.id
        left join ais_sale_center.m_agent ma on ma.id = l.fk_agent_id
        left join ais_permission_center.m_staff s on s.login_id = l.from_user
        <where>
            <if test="param.keyword != null and param.keyword != ''">
                and (l.to_user like concat('%',#{param.keyword},'%') or ma.name like
                concat('%',#{param.keyword},'%'))
            </if>
            <if test="param.loginId != null and param.loginId != ''">
                and l.from_user = #{param.loginId}
            </if>

            <if test="param.optType != null and param.optType != ''">
                and l.opt_type = #{param.optType}
            </if>


        </where>
        order by l.id desc
    </select>

    <select id="selectDetail" resultType="com.get.partnercenter.vo.MailLogVo">
        select l.*,
        ma.name as agentName
        from log_mail_partner_user_account l
        left join ais_sale_center.m_agent ma on ma.id = l.fk_agent_id
        <where>
            l.id = #{id}
        </where>
    </select>
</mapper>