package com.get.partnercenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import java.io.Serializable;
import lombok.Data;

/**
 * 
 * @TableName s_comment
 */
@TableName(value ="s_comment")
@Data
public class SCommentEntity extends BaseEntity implements Serializable {
    /**
     * 表名
     */
    private String fkTableName;

    /**
     * 表Id
     */
    private Long fkTableId;

    /**
     * 评论
     */
    private String comment;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}