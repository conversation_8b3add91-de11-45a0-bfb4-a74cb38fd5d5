package com.get.resumecenter.service.Impl;

import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.resumecenter.dao.ResumeOtherMapper;
import com.get.resumecenter.vo.ResumeOtherVo;
import com.get.resumecenter.entity.ResumeOther;
import com.get.resumecenter.service.IResumeOtherService;
import com.get.resumecenter.dto.ResumeOtherDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/11
 * @TIME: 14:13
 * @Description:
 **/
@Service
public class ResumeOtherServiceImpl implements IResumeOtherService {
    @Resource
    private ResumeOtherMapper otherMapper;
    @Autowired
    private UtilService utilService;

    @Override
    public List<ResumeOtherVo> getResumeOtherListDto(Long resumeId) {
        if (GeneralTool.isEmpty(resumeId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("resume_id_null"));
        }
        return otherMapper.selectByResumeId(resumeId);
    }

    @Override
    public ResumeOtherVo getResumeOtherById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return otherMapper.selectByOtherId(id);
    }

    @Override
    public Long addResumeOther(ResumeOtherDto otherVo) {
//        if (!StaffContext.getStaff().getIsAdmin()) {
//            if (!StaffContext.getStaff().getIsModifiedResume()) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
//            }
//        }
        if (!GetAuthInfo.isAdmin()) {
            if (!SecureUtil.getIsModifiedResume()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
            }
        }
        if (GeneralTool.isEmpty(otherVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ResumeOther resumeOther = BeanCopyUtils.objClone(otherVo, ResumeOther::new);
        utilService.updateUserInfoToEntity(resumeOther);
        int i = otherMapper.insertSelective(resumeOther);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return resumeOther.getId();
    }

    @Override
    public ResumeOtherVo updateResumeOther(ResumeOtherDto otherVo) {
//        if (!StaffContext.getStaff().getIsAdmin()) {
//            if (!StaffContext.getStaff().getIsModifiedResume()) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
//            }
//        }
        if (!GetAuthInfo.isAdmin()) {
            if (!SecureUtil.getIsModifiedResume()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
            }
        }
        if (GeneralTool.isEmpty(otherVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ResumeOther resumeOther = BeanCopyUtils.objClone(otherVo, ResumeOther::new);
        utilService.updateUserInfoToEntity(resumeOther);
        otherMapper.updateById(resumeOther);
        return getResumeOtherById(resumeOther.getId());
    }

    @Override
    public void deleteResumeOther(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        otherMapper.deleteById(id);
    }
}
