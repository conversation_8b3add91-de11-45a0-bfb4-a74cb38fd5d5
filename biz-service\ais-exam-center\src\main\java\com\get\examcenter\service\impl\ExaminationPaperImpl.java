package com.get.examcenter.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.bean.WxMaCodeLineColor;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.aisplatformcenterap.feign.IPlatformCenterClient;
import com.get.aisplatformcenterap.vo.UserInfoVo;
import com.get.common.consts.AESConstant;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.Page;
import com.get.common.result.SearchBean;
import com.get.common.utils.AESUtils;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.examcenter.vo.*;
import com.get.examcenter.entity.*;
import com.get.examcenter.mapper.appexam.UserExaminationQuestionScoreMapper;
import com.get.examcenter.mapper.appexam.UserStaffBdMapper;
import com.get.examcenter.mapper.appexam.UserexaminationPaperScoreMapper;
import com.get.examcenter.mapper.exam.ExaminationAnswerMapper;
import com.get.examcenter.mapper.exam.ExaminationPaperMapper;
import com.get.examcenter.mapper.exam.ExaminationQuestionAssignMapper;
import com.get.examcenter.mapper.exam.ExaminationQuestionMapper;
import com.get.examcenter.service.ExaminationPaperService;
import com.get.examcenter.service.ExaminationService;
import com.get.examcenter.service.IExaminationQuestionService;
import com.get.examcenter.service.IQuestionTypeService;
import com.get.examcenter.service.ScoreTitleService;
import com.get.examcenter.utils.MyStringUtils;
import com.get.examcenter.dto.ExaminationPaperListDto;
import com.get.examcenter.dto.ExaminationPaperUpdateDto;
import com.get.examcenter.dto.ExaminationQuestionAssignDto;
import com.get.examcenter.dto.ViewLeaderboardDto;
import com.get.institutioncenter.vo.AreaRegionVo;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.codec.binary.Base64;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by Jerry.
 * Time: 11:49
 * Date: 2021/8/23
 * Description:考场管理实现类
 */
@Service
public class ExaminationPaperImpl implements ExaminationPaperService {

    @Resource
    private ExaminationPaperMapper examinationPaperMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private UserexaminationPaperScoreMapper userExaminationPaperScoreMapper;
//    @Resource
//    private IPlatformConfigCenterClient platformConfigCenterClient;
    @Resource
    private IPlatformCenterClient platformCenterClient;
    @Resource
    private ExaminationService examinationService;
    @Resource
    private ExaminationQuestionAssignMapper examinationQuestionAssignMapper;
    @Resource
    private ExaminationQuestionMapper examinationQuestionMapper;
    @Resource
    private UserExaminationQuestionScoreMapper userExaminationQuestionScoreMapper;
    @Resource
    private IExaminationQuestionService iExaminationQuestionService;
    @Resource
    private ScoreTitleService scoreTitleService;
    @Resource
    private UserStaffBdMapper userStaffBdMapper;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IQuestionTypeService iQuestionTypeService;
    @Resource
    private UserexaminationPaperScoreMapper userexaminationPaperScoreMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ExaminationAnswerMapper examinationAnswerMapper;

    /**
     * @Description: 列表
     * @Author: Jerry
     * @Date:11:50 2021/8/23
     */
    @Override
    public List<ExaminationPaperVo> getExaminationPaperList(ExaminationPaperListDto examinationPaperListDto, SearchBean<ExaminationPaperListDto> page) {
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        Set<Long> fkCompanyIds = new HashSet<>();
        //获取当前登陆人的公司id
        if (GetAuthInfo.getUser().getIsAdmin()) {
            Long id = GetAuthInfo.getStaffId();
            List<Long> companyIds = SecureUtil.getCompanyIdsByStaffId(id);
            if (GeneralTool.isNotEmpty(companyIds)) {
                fkCompanyIds = new HashSet<>(companyIds);
            }
        } else {
            Long fkCompanyId = SecureUtil.getFkCompanyId();
            fkCompanyIds.add(fkCompanyId);
        }
        IPage<ExaminationPaperVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        
        List<ExaminationPaperVo> examinationPaperList = examinationPaperMapper.getExaminationPaperList(iPage, examinationPaperListDto, fkCompanyIds);
        page.setAll((int) iPage.getTotal());
        if (GeneralTool.isEmpty(examinationPaperList)) {
            return new ArrayList<>();
        }
//        page.restPage(examinationPaperList);
        Map<Long, String> examinationNamesByExaminationIds = new HashMap<>();
        Set<Long> fkExaminationIds = examinationPaperList.stream().map(ExaminationPaperVo::getFkExaminationId).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(fkExaminationIds)) {
            examinationNamesByExaminationIds = examinationService.getExaminationNamesByExaminationIds(fkExaminationIds);
        }
        //获取所有的考卷ids
        Set<Long> fkExaminationPaperIds = examinationPaperList.stream().map(ExaminationPaperVo::getId).collect(Collectors.toSet());
        //获取所有的公司ids
        Set<Long> fkComanyIds = examinationPaperList.stream().map(ExaminationPaperVo::getFkCompanyId).collect(Collectors.toSet());
        //根据公司ids获取名称
        Map<Long, String> companyNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkComanyIds)) {
            Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(fkComanyIds);
            if (result.isSuccess() && result.getData() != null) {
                companyNamesByIds = result.getData();
            }
        }
        //根据考卷ids获取考试次数
        Map<Long, Integer> scoreCountMap = getScoreCountByFkExaminationPaperIds(fkExaminationPaperIds);
        for (ExaminationPaperVo examinationPaperVo : examinationPaperList) {
            if (GeneralTool.isNotEmpty(examinationPaperVo.getIsActive())) {
                examinationPaperVo.setIsActiveName(examinationPaperVo.getIsActive() ? "是" : "否");
            }
            if (GeneralTool.isNotEmpty(examinationPaperVo.getIsRetest())) {
                examinationPaperVo.setIsRetestName(examinationPaperVo.getIsRetest() ? "是" : "否");
            }
            examinationPaperVo.setFkExaminationName(examinationNamesByExaminationIds.get(examinationPaperVo.getFkExaminationId()));
            examinationPaperVo.setScoreCount(scoreCountMap.get(examinationPaperVo.getId()));
            examinationPaperVo.setFkCompanyName(companyNamesByIds.get(examinationPaperVo.getFkCompanyId()));
        }
        return examinationPaperList;
    }


    /**
     * @Description: 新增
     * @Author: Jerry
     * @Date:11:50 2021/8/23
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(ExaminationPaperUpdateDto examinationPaperUpdateDto) {
        if (GeneralTool.isEmpty(examinationPaperUpdateDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (GeneralTool.isNotEmpty(examinationPaperUpdateDto.getStartTime()) && GeneralTool.isNotEmpty(examinationPaperUpdateDto.getEndTime())) {
            //校验日期
            if (examinationPaperUpdateDto.getStartTime().after(examinationPaperUpdateDto.getEndTime())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("start_time_end_time_error"));
            }
        }
        if (examinationPaperUpdateDto.getQuestionCount() != null && 0 == examinationPaperUpdateDto.getQuestionCount()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("question_count_no_zero"));
        }
        ExaminationPaper examinationPaper = BeanCopyUtils.objClone(examinationPaperUpdateDto, ExaminationPaper::new);
        utilService.updateUserInfoToEntity(examinationPaper);
        examinationPaperMapper.insertSelective(examinationPaper);
        //自动生成编号
        examinationPaper.setNum(MyStringUtils.getExaminationPaperNum(examinationPaper.getId()));
        examinationPaperMapper.updateByPrimaryKey(examinationPaper);
    }

    /**
     * @Description: 编辑
     * @Author: Jerry
     * @Date:11:50 2021/8/23
     */
    @Override
    public void update(ExaminationPaperUpdateDto examinationPaperUpdateDto) {
        if (examinationPaperUpdateDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isNotEmpty(examinationPaperUpdateDto.getStartTime()) && GeneralTool.isNotEmpty(examinationPaperUpdateDto.getEndTime())) {
            //校验日期
            if (examinationPaperUpdateDto.getStartTime().after(examinationPaperUpdateDto.getEndTime())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("start_time_end_time_error"));
            }
        }
        if (examinationPaperUpdateDto.getQuestionCount() != null && 0 == examinationPaperUpdateDto.getQuestionCount()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("question_count_no_zero"));
        }
        ExaminationPaper examinationPaper = examinationPaperMapper.selectById(examinationPaperUpdateDto.getId());
        if (examinationPaper == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //查询是否有答题记录，有的话不允许修改生成考题数
        validateUpdateQuestionCount(examinationPaper, examinationPaperUpdateDto);
        examinationPaper = BeanCopyUtils.objClone(examinationPaperUpdateDto, ExaminationPaper::new);
        utilService.updateUserInfoToEntity(examinationPaper);
        examinationPaperMapper.updateByPrimaryKey(examinationPaper);
    }


    /**
     * @Description: 删除
     * @Author: Jerry
     * @Date:11:51 2021/8/23
     */
    @Override
    @DSTransactional
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //校验是否有考试记录，有的话不可以删除
        validateDelete(id);
        examinationPaperMapper.deleteById(id);
        //删除已分配的考题
//        Example example = new Example(ExaminationQuestionAssign.class);
//        example.createCriteria().andEqualTo("fkExaminationPaperId",id);
        examinationQuestionAssignMapper.delete(Wrappers.<ExaminationQuestionAssign>lambdaQuery().eq(ExaminationQuestionAssign::getFkExaminationPaperId, id));
    }


    /**
     * @Description: 详情
     * @Author: Jerry
     * @Date:11:51 2021/8/23
     */
    @Override
    public ExaminationPaperVo detail(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ExaminationPaper examinationPaper = examinationPaperMapper.selectById(id);
        if (GeneralTool.isEmpty(examinationPaper)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        Map<Long, String> examinationNamesByExaminationIds = new HashMap<>();
        //查询考试名称
        if (GeneralTool.isNotEmpty(examinationPaper.getFkExaminationId())) {
            Set<Long> fkExaminationIds = new HashSet<>();
            fkExaminationIds.add(examinationPaper.getFkExaminationId());
            examinationNamesByExaminationIds = examinationService.getExaminationNamesByExaminationIds(fkExaminationIds);
        }
        ExaminationPaperVo examinationPaperVo = BeanCopyUtils.objClone(examinationPaper, ExaminationPaperVo::new);
        Long fkComanyId = examinationPaperVo.getFkCompanyId();
        Result<String> result = permissionCenterClient.getCompanyNameById(fkComanyId);
        if (result.isSuccess() && result.getData() != null) {
            examinationPaperVo.setFkCompanyName(result.getData());
        }
        if (GeneralTool.isNotEmpty(examinationPaperVo.getIsActive())) {
            examinationPaperVo.setIsActiveName(examinationPaperVo.getIsActive() ? "是" : "否");
        }
        if (GeneralTool.isNotEmpty(examinationPaperVo.getIsRetest())) {
            examinationPaperVo.setIsRetestName(examinationPaperVo.getIsRetest() ? "是" : "否");
        }
        examinationPaperVo.setFkExaminationName(examinationNamesByExaminationIds.get(examinationPaperVo.getFkExaminationId()));
        return examinationPaperVo;
    }


    /**
     * @Description: 激活禁用（true：激活 false：禁用）
     * @Author: Jerry
     * @Date:11:51 2021/8/23
     */
    @Override
    public void updateActive(Long id, boolean isActive) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ExaminationPaper examinationPaper = new ExaminationPaper();
        examinationPaper.setId(id);
        examinationPaper.setIsActive(isActive);
        utilService.updateUserInfoToEntity(examinationPaper);
        examinationPaperMapper.updateByPrimaryKeySelective(examinationPaper);
    }

    /**
     * @Description: 查看排行榜
     * @Author: Jerry
     * @Date:15:55 2021/8/23
     */
    @Override
    public List<UserexaminationPaperScoreVo> viewLeaderboard(Long fkExaminationPaperId, ViewLeaderboardDto viewLeaderboardDto, Page page) {
        List<UserexaminationPaperScoreVo> userexaminationPaperScoreVos = new ArrayList<>();
        Set<Long> userStaffBdIds = null;
        Set<Long> userIds = null;
        if (GeneralTool.isNotEmpty(viewLeaderboardDto.getFkAreaRegionId())) {
            //根据大区查询用户
//            Example example = new Example(UserStaffBd.class);
//            example.createCriteria().andEqualTo("fkAreaRegionId",viewLeaderboardVo.getFkAreaRegionId());
//            List<UserStaffBd> userStaffBds = userStaffBdMapper.selectByExample(example);
            List<UserStaffBd> userStaffBds = userStaffBdMapper.selectList(Wrappers.<UserStaffBd>lambdaQuery().eq(UserStaffBd::getFkAreaRegionId, viewLeaderboardDto.getFkAreaRegionId()));
            //根据姓名查询不出结果,直接退出
            if (GeneralTool.isEmpty(userStaffBds)) {
                return userexaminationPaperScoreVos;
            }
            userStaffBdIds = userStaffBds.stream().map(UserStaffBd::getFkUserId).collect(Collectors.toSet());
        }
        if (GeneralTool.isNotEmpty(viewLeaderboardDto.getUserName()) || GeneralTool.isNotEmpty(viewLeaderboardDto.getFkAreaCityId())) {
            //根据名称或者城市搜索用户
//            userIds = platformConfigCenterClient.getUserIdsByParam(viewLeaderboardVo.getUserName(),
//                    viewLeaderboardVo.getFkAreaCityId(),
//                    null);//bdName暂时用不上，传空
            Result<Set<Long>> result = platformCenterClient.getUserIdsByParam(viewLeaderboardDto.getUserName(),
                    viewLeaderboardDto.getFkAreaCityId(),
                    null);//bdName暂时用不上，传空
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                userIds = result.getData();
            }
            //根据姓名查询不出结果,直接退出
            if (GeneralTool.isEmpty(userIds)) {
                return userexaminationPaperScoreVos;
            }
        }

//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        IPage<UserexaminationPaperScoreVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        userexaminationPaperScoreVos = userExaminationPaperScoreMapper.viewExaminationPaperLeaderboardByUserIds(iPage, fkExaminationPaperId, userStaffBdIds, userIds);
        page.setAll((int) iPage.getTotal());
        //无结果
        if (GeneralTool.isEmpty(userexaminationPaperScoreVos)) {
            return userexaminationPaperScoreVos;
        }
        //获取用户ids
        Set<Long> fkUserIds = userexaminationPaperScoreVos.stream().map(UserexaminationPaperScoreVo::getFkUserId).collect(Collectors.toSet());
//        Map<Long, String> userNamesByUserIds = new HashMap<>();
        Map<Long, String> cityNamesByUserIds = new HashMap<>();
        Map<Long, String> bdNamesByUserIds = new HashMap<>();
        Map<Long, UserInfoVo> userInfoDtoByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkUserIds)) {
            //根据userid获取名称
//            userNamesByUserIds = platformConfigCenterClient.getUserNickNamesByUserIds(fkUserIds);
//            Result<Map<Long, String>> result1 = platformConfigCenterClient.getUserNickNamesByUserIds(fkUserIds);
//            if (result1.isSuccess() && GeneralTool.isNotEmpty(result1.getData())) {
//                userNamesByUserIds = result1.getData();
//            }
            Result<Map<Long, UserInfoVo>> result = platformCenterClient.getUserInfoDtoByIds(fkUserIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                userInfoDtoByIds = result.getData();
            }
            //获取用户的所在城市
//            cityNamesByUserIds = platformConfigCenterClient.getCityNamesByUserIds(fkUserIds);
            Result<Map<Long, String>> result2 = platformCenterClient.getCityNamesByUserIds(fkUserIds);
            if (result2.isSuccess() && GeneralTool.isNotEmpty(result2.getData())) {
                cityNamesByUserIds = result2.getData();
            }
            //根据用户ids获取BD名称
            bdNamesByUserIds = getBdNamesByUserIds(fkUserIds);
        }
        //获取所有的称号
        List<ScoreTitleVo> scoreTitleVos = scoreTitleService.datasNoPage();
        //获取个人的考试次数
        Map<Long, Integer> examCountByFkExaminationPaperId = getExamCountByFkExaminationPaperId(fkExaminationPaperId);
        for (UserexaminationPaperScoreVo userexaminationPaperScoreVo : userexaminationPaperScoreVos) {
            UserInfoVo userInfoVo = userInfoDtoByIds.get(userexaminationPaperScoreVo.getFkUserId());
            if (GeneralTool.isNotEmpty(userInfoVo)) {
                //用户名称
                userexaminationPaperScoreVo.setFkUserName(userInfoVo.getWechatNickname());
                userexaminationPaperScoreVo.setPhoneNumber(userInfoVo.getMobile());
            }
            //城市名称
            userexaminationPaperScoreVo.setFkAreaCityName(cityNamesByUserIds.get(userexaminationPaperScoreVo.getFkUserId()));
            //匹配称号
            setScoreTitleName(scoreTitleVos, userexaminationPaperScoreVo);
            //BD名称
            userexaminationPaperScoreVo.setBdName(bdNamesByUserIds.get(userexaminationPaperScoreVo.getFkUserId()));
            //考试次数
            userexaminationPaperScoreVo.setExamCount(examCountByFkExaminationPaperId.get(userexaminationPaperScoreVo.getFkUserId()));
            //排名
            userexaminationPaperScoreVo.setRank(userexaminationPaperScoreVo.getRank().replace(".0",""));
            //考试次数
            userexaminationPaperScoreVo.setCount(userexaminationPaperScoreVo.getCount());
        }
//        page.restPage(userexaminationPaperScoreDtos);
        return userexaminationPaperScoreVos;
    }

    /**
     * @Description: 分配试题
     * @Author: Jerry
     * @Date:16:13 2021/8/23
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void assignExaminationQuestions(List<ExaminationQuestionAssignDto> examinationQuestionVos) {
        if (GeneralTool.isEmpty(examinationQuestionVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (ExaminationQuestionAssignDto examinationQuestionVo : examinationQuestionVos) {
            ExaminationQuestionAssign examinationQuestionAssign = BeanCopyUtils.objClone(examinationQuestionVo, ExaminationQuestionAssign::new);
            ExaminationQuestionAssign one = examinationQuestionAssignMapper.selectOne(new LambdaQueryWrapper<ExaminationQuestionAssign>().eq(ExaminationQuestionAssign::getFkExaminationPaperId, examinationQuestionAssign.getFkExaminationPaperId())
                    .eq(ExaminationQuestionAssign::getTargetId, examinationQuestionAssign.getTargetId()));
            if (GeneralTool.isNotEmpty(one)) {
                continue;
            }
            if (GeneralTool.isEmpty(examinationQuestionVo.getId())) {
                examinationQuestionAssign.setIsActive(true);
                utilService.updateUserInfoToEntity(examinationQuestionAssign);
                examinationQuestionAssignMapper.insertSelective(examinationQuestionAssign);
                //获取id更新排序view_order
                examinationQuestionAssign.setViewOrder(examinationQuestionAssign.getId().intValue());
                examinationQuestionAssign.setGmtModifiedUser(null);
                examinationQuestionAssign.setGmtModified(null);
                examinationQuestionAssignMapper.updateByPrimaryKeySelective(examinationQuestionAssign);
                continue;
            }
            utilService.updateUserInfoToEntity(examinationQuestionAssign);
            examinationQuestionAssign.setViewOrder(examinationQuestionAssign.getId().intValue());
            examinationQuestionAssignMapper.updateByPrimaryKeySelective(examinationQuestionAssign);
        }
    }


    /**
     * @Description: 删除已分配的试题
     * @Author: Jerry
     * @Date:10:03 2021/9/3
     */
    @Override
    public void deleteAssignExaminationQuestions(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //校验是否有答题记录，有的话不可以删除
        validateDelete(id);
        examinationQuestionAssignMapper.deleteById(id);
        //2022-10-8修改删除逻辑
        //examinationQuestionAssignMapper.updateActiveById(id);
    }

    /**
     * @Description: 已分配的试题列表
     * @Author: Jerry
     * @Date:10:20 2021/9/3
     */
    @Override
    public List<ExaminationQuestionAssignVo> assignExaminationQuestionsDatas(Long fkExaminationPaperId) {
//        Example example = new Example(ExaminationQuestionAssign.class);
//        example.createCriteria().andEqualTo("fkExaminationPaperId",fkExaminationPaperId);
//        List<ExaminationQuestionAssign> examinationQuestionAssigns = examinationQuestionAssignMapper.selectByExample(example);
        //2022-07-28添加排序
        List<ExaminationQuestionAssign> examinationQuestionAssigns = examinationQuestionAssignMapper.selectList(Wrappers.<ExaminationQuestionAssign>lambdaQuery().eq(ExaminationQuestionAssign::getFkExaminationPaperId, fkExaminationPaperId)
                .orderByDesc(ExaminationQuestionAssign::getTargetType).orderByDesc(ExaminationQuestionAssign::getViewOrder));
        if (GeneralTool.isEmpty(examinationQuestionAssigns)) {
            return new ArrayList<>();
        }
        List<ExaminationQuestionAssignVo> examinationQuestionAssignVos = new ArrayList<>();
        //获取该考卷对应的所有考题ids
        Set<Long> fkQuestionIds = examinationQuestionAssigns.stream().filter(examinationQuestionAssign -> examinationQuestionAssign.getTargetType() == 1).
                map(ExaminationQuestionAssign::getTargetId).collect(Collectors.toSet());
        //获取该考卷对应的所有考题类型ids
        Set<Long> fkQuestionTypeIds = examinationQuestionAssigns.stream().filter(examinationQuestionAssign -> examinationQuestionAssign.getTargetType() == 0).
                map(ExaminationQuestionAssign::getTargetId).collect(Collectors.toSet());
        //根据考题ids获取名称
        Map<Long, String> examinationQuestionNamesMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkQuestionIds)) {
            examinationQuestionNamesMap = iExaminationQuestionService.getExaminationQuestionNamesByQuestionIds(fkQuestionIds);
        }
        //根据考题类型ids获取名称
        Map<Long, String> namesByQuestionTypeMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkQuestionTypeIds)) {
            namesByQuestionTypeMap = iQuestionTypeService.getNamesByQuestionTypeIds(fkQuestionTypeIds);
        }
        for (ExaminationQuestionAssign examinationQuestionAssign : examinationQuestionAssigns) {
            ExaminationQuestionAssignVo examinationQuestionAssignVo = BeanCopyUtils.objClone(examinationQuestionAssign, ExaminationQuestionAssignVo::new);
            if (ProjectExtraEnum.EXAMPAPER_TARGET_TYPE_QUESTIONTYPE.key.equals(examinationQuestionAssignVo.getTargetType())) {
                //考题类型
                examinationQuestionAssignVo.setTargetTypeName(ProjectExtraEnum.EXAMPAPER_TARGET_TYPE_QUESTIONTYPE.value);
                examinationQuestionAssignVo.setTargetName(namesByQuestionTypeMap.get(examinationQuestionAssignVo.getTargetId()));
            } else if (ProjectExtraEnum.EXAMPAPER_TARGET_TYPE_QUESTION.key.equals(examinationQuestionAssignVo.getTargetType())) {
                //考题
                examinationQuestionAssignVo.setTargetTypeName(ProjectExtraEnum.EXAMPAPER_TARGET_TYPE_QUESTION.value);
                examinationQuestionAssignVo.setTargetName(examinationQuestionNamesMap.get(examinationQuestionAssignVo.getTargetId()));
            }
            examinationQuestionAssignVos.add(examinationQuestionAssignVo);
        }
        return examinationQuestionAssignVos;
    }

    /**
     * @Description: 根据考场编号获取考题
     * @Author: Jerry
     * @Date:9:34 2021/8/24
     */
    @Override
    public List<ExaminationQuestionVo> getExaminationQuestions(String examinationPaperNum, Long fkUserId) {
        if (GeneralTool.isEmpty(examinationPaperNum)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        //获取考试和考卷信息
        ExaminationActiveAndTimeVo ExaminationByexaminationPaperNum = examinationPaperMapper.getExaminationByexaminationPaperNum(examinationPaperNum);
        if (GeneralTool.isEmpty(ExaminationByexaminationPaperNum)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //校验考试和考场状态是否激活
        validateActive(ExaminationByexaminationPaperNum);
        //校验当前时间是否在该考试、考场有效时间内
        validateTime(ExaminationByexaminationPaperNum);
        //校验用户是否已在该考场完成答题，并判断该考场是否允许用户多次答题，如不允许则提示已完成答题
        validateUserCompleteAnswer(ExaminationByexaminationPaperNum, fkUserId);
        //用户是否已有答题记录，如有答题记录，需判断答题记录与题库中相关问题是否允许多次答题，
        // 如不允许，数据返回需过滤已完成的题目；如所有考题已完成答题，则提示已完成答题。注意：不能只靠数据查询结果0作为唯一判断依据，可能存在题库没有任何考题的情况，需加入考虑
        return valiedateGetExaminationQuestions(ExaminationByexaminationPaperNum, fkUserId);
    }

    /**
     * @Description: 根据考卷ids获取对象
     * @Author: Jerry
     * @Date:15:19 2021/8/27
     */
    @Override
    public Map<Long, ExaminationPaper> getExaminationPaperByExaminationPaperIds(Set<Long> examinationPaperIds) {
        Map<Long, ExaminationPaper> map = new HashMap<>();
        if (GeneralTool.isEmpty(examinationPaperIds)) {
            return map;
        }
//        Example example = new Example(ExaminationPaper.class);
//        example.createCriteria().andIn("id",examinationPaperIds);
//        List<ExaminationPaper> examinationPapers = examinationPaperMapper.selectByExample(example);
        List<ExaminationPaper> examinationPapers = examinationPaperMapper.selectBatchIds(examinationPaperIds);
        if (GeneralTool.isEmpty(examinationPapers)) {
            return map;
        }
        for (ExaminationPaper examinationPaper : examinationPapers) {
            map.put(examinationPaper.getId(), examinationPaper);
        }
        return map;
    }


    /**
     * @return
     * @Description: 考卷下拉框
     * @Author: Jerry
     * @Date:9:45 2021/9/3
     */
    @Override
    public List<BaseSelectEntity> examinationPaperSelect(String fkCompanyId) {
//        Example example = new Example(ExaminationPaper.class);
        /*LambdaQueryWrapper<ExaminationPaper> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(GeneralTool.isNotEmpty(fkCompanyId)){
            lambdaQueryWrapper.eq(ExaminationPaper::getFkCompanyId,fkCompanyId);
        }
        lambdaQueryWrapper.apply("CAST(SUBSTRING(`name` FROM locate('（',name)+1 FOR (locate('）',name) - locate('（',name)-1)) AS SIGNED)");
        List<ExaminationPaper> examinationPapers = examinationPaperMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isEmpty(examinationPapers)) {
            return new ArrayList<>();
        }*/
        List<ExaminationPaper> examinationPapers = examinationPaperMapper.getExaminationPaper(fkCompanyId);
        return BeanCopyUtils.copyListProperties(examinationPapers, BaseSelectEntity::new);
    }

    /**
     * @Description: 生成二维码
     * @Author: Jerry
     * @Date:17:41 2021/9/15
     */
    @Override
    public String createQrCode(Long examinationPaperId) {
        if (GeneralTool.isEmpty(examinationPaperId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //根据考卷id获取考试id
        ExaminationPaper examinationPaper = examinationPaperMapper.selectById(examinationPaperId);
        if (GeneralTool.isEmpty(examinationPaper)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        Long fkExaminationId = examinationPaper.getFkExaminationId();
        if (GeneralTool.isEmpty(fkExaminationId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("exam_unassigned_applet"));
        }
        ConfigVo configVo = null;
        Result<ConfigVo> configDtoResult = permissionCenterClient.getConfigValueByConfigKeyAndValue("EXAM_QRCODE_WECHAT_CONFIG", fkExaminationId);
        if (configDtoResult.isSuccess() && GeneralTool.isNotEmpty(configDtoResult.getData())) {
            configVo = configDtoResult.getData();
        }
        if (GeneralTool.isEmpty(configVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("exam_unassigned_applet"));
        }
        //调用工具包的服务
        WxMaService wxMaService = new WxMaServiceImpl();
        WxMaDefaultConfigImpl wxMaDefaultConfigImpl = new WxMaDefaultConfigImpl();
        //生成的链接
        String pathUrl = configVo.getValue2();
        pathUrl = pathUrl + examinationPaperId;

        //appId
        String appId = configVo.getValue3();
        //appSecret
        String appSecret = configVo.getValue4();
        try {
            appId = AESUtils.Decrypt(appId, AESConstant.AESKEY);
            appSecret = AESUtils.Decrypt(appSecret, AESConstant.AESKEY);
        } catch (Exception e) {
            throw new GetServiceException(e.getMessage());
        }
        wxMaDefaultConfigImpl.setAppid(appId);        //小程序appId
        wxMaDefaultConfigImpl.setSecret(appSecret);    //小程序secret
        wxMaService.setWxMaConfig(wxMaDefaultConfigImpl);

        // 设置小程序二维码线条颜色为黑色
        WxMaCodeLineColor lineColor = new WxMaCodeLineColor("0", "0", "0");
        byte[] qrCodeBytes = null;
        try {
            //其中codeType以及parameterValue为前端页面所需要接收的参数。
            qrCodeBytes = wxMaService.getQrcodeService().createWxaCodeBytes(pathUrl, 30, false, lineColor, false);
        } catch (WxErrorException e) {
            e.printStackTrace();
        }
        String qrCodeStr = Base64.encodeBase64String(qrCodeBytes);
        return qrCodeStr;
    }

    /**
     * @Description: 校验是否有答题记录，有的话不可以删除
     * @Author: Jerry
     * @Date:15:21 2021/8/23
     */
    private void validateDelete(Long fkExaminationPaperId) {
//        Example example = new Example(UserExaminationQuestionScore.class);
//        example.createCriteria().andEqualTo("fkExaminationPaperId",fkExaminationPaperId);
//        List<UserExaminationQuestionScore> userExaminationQuestionScores = userExaminationQuestionScoreMapper.selectByExample(example);
        List<UserExaminationQuestionScore> userExaminationQuestionScores = userExaminationQuestionScoreMapper.selectList(Wrappers.<UserExaminationQuestionScore>lambdaQuery().eq(UserExaminationQuestionScore::getFkExaminationPaperId, fkExaminationPaperId));
        if (GeneralTool.isNotEmpty(userExaminationQuestionScores)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("examinationPaper_has_been_associated"));
        }
    }

    /**
     * @Description: 校验是否有答题记录，有的话不允许修改生成考题数，如果是考题数一样的话代表是修改其他信息，不做校验
     * @Author: Jerry
     * @Date:12:13 2021/8/25
     */
    private void validateUpdateQuestionCount(ExaminationPaper examinationPaper, ExaminationPaperUpdateDto examinationPaperUpdateDto) {
        if (GeneralTool.isEmpty(examinationPaper.getQuestionCount()) && GeneralTool.isEmpty(examinationPaperUpdateDto.getQuestionCount())) {
            //没有修改生成考题数
            return;
        }
        if (GeneralTool.isNotEmpty(examinationPaper.getQuestionCount()) && examinationPaper.getQuestionCount().equals(examinationPaperUpdateDto.getQuestionCount())) {
            //没有修改生成考题数
            return;
        }
        //不相等，代表修改了生成考题数
//        Example example = new Example(UserExaminationQuestionScore.class);
//        example.createCriteria().andEqualTo("fkExaminationPaperId",examinationPaper.getId());
//        List<UserExaminationQuestionScore> userExaminationQuestionScores = userExaminationQuestionScoreMapper.selectByExample(example);
        List<UserExaminationQuestionScore> userExaminationQuestionScores = userExaminationQuestionScoreMapper.selectList(Wrappers.<UserExaminationQuestionScore>lambdaQuery().eq(UserExaminationQuestionScore::getFkExaminationPaperId, examinationPaper.getId()));

        if (GeneralTool.isNotEmpty(userExaminationQuestionScores)) {
            //有考题记录，不可以修改生成考题数
            throw new GetServiceException(LocaleMessageUtils.getMessage("examinationPaper_have_answer_records"));
        }
    }

    /**
     * @Description: 校验考试和考场状态是否激活
     * @Author: Jerry
     * @Date:10:08 2021/8/24
     */
    private void validateActive(ExaminationActiveAndTimeVo ExaminationByexaminationPaperNum) {
        if (!ExaminationByexaminationPaperNum.getExaminationActive()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("examination_not_active"));
        }
        if (!ExaminationByexaminationPaperNum.getExaminationPaperActive()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("examinationPaper_not_active"));
        }
    }

    /**
     * @Description: 校验当前时间是否在该考试、考场有效时间内
     * @Author: Jerry
     * @Date:10:09 2021/8/24
     */
    private void validateTime(ExaminationActiveAndTimeVo ExaminationByexaminationPaperNum) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //获取当前时间
        Date nowDate = new Date();
        //校验考卷
        Date examinationPaperStartTime = ExaminationByexaminationPaperNum.getExaminationPaperStartTime();
        Date examinationPaperEndTime = ExaminationByexaminationPaperNum.getExaminationPaperEndTime();
        if (examinationPaperStartTime != null && nowDate.before(examinationPaperStartTime)) {
            //考卷开始时间为：#{examinationPaperStartTime}，请稍后重试
            throw new GetServiceException(LocaleMessageUtils.getMessage("examinationPaper_start_time") + ":" + sdf.format(examinationPaperStartTime) + "，" + LocaleMessageUtils.getMessage(
                    "please_try_again_later"));
        }
        if (examinationPaperEndTime != null && nowDate.after(examinationPaperEndTime)) {
            //考卷结束时间为：#{examinationPaperEndTime}，考试已结束
            throw new GetServiceException(LocaleMessageUtils.getMessage("examinationPaper_end_time") + ":" + sdf.format(examinationPaperEndTime) + "，" + LocaleMessageUtils.getMessage(
                    "examination_is_over"));
        }
        //校验考试
        Date examinationStartTime = ExaminationByexaminationPaperNum.getExaminationStartTime();
        Date examinationEndTime = ExaminationByexaminationPaperNum.getExaminationEndTime();
        if (examinationStartTime != null && nowDate.before(examinationStartTime)) {
            //考试开始时间为：#{examinationStartTime}，请稍后重试
            throw new GetServiceException(LocaleMessageUtils.getMessage("examination_start_time") + ":" + sdf.format(examinationStartTime) + "，" + LocaleMessageUtils.getMessage(
                    "please_try_again_later"));
        }
        if (examinationEndTime != null && nowDate.after(examinationEndTime)) {
            //考试结束时间为：#{examinationEndTime}，考试已结束
            throw new GetServiceException(LocaleMessageUtils.getMessage("examination_end_time") + ":" + sdf.format(examinationEndTime) + "，" + LocaleMessageUtils.getMessage(
                    "examination_is_over"));
        }
    }

    /**
     * @Description: 校验用户是否已在该考场完成答题，并判断该考场是否允许用户多次答题，如不允许则提示已完成答题
     * @Author: Jerry
     * @Date:11:10 2021/8/24
     */
    private void validateUserCompleteAnswer(ExaminationActiveAndTimeVo ExaminationByexaminationPaperNum,
                                            Long fkUserId) {
        if (ExaminationByexaminationPaperNum.getIsRetest()) {
            //允许重考
            return;
        }
//        Example example = new Example(UserexaminationPaperScore.class);
//        example.createCriteria().andEqualTo("fkExaminationPaperId",ExaminationByexaminationPaperNum.getFkExaminationPaperId())
//                .andEqualTo("fkUserId",fkUserId).andIsNotNull("score");
//        List<UserexaminationPaperScore> userexaminationPaperScores = userExaminationPaperScoreMapper.selectByExample(example);
        List<UserexaminationPaperScore> userexaminationPaperScores = userExaminationPaperScoreMapper.selectList(Wrappers.<UserexaminationPaperScore>lambdaQuery()
                .eq(UserexaminationPaperScore::getFkExaminationPaperId, ExaminationByexaminationPaperNum.getFkExaminationPaperId())
                .eq(UserexaminationPaperScore::getFkUserId, fkUserId)
                .isNotNull(UserexaminationPaperScore::getScore));
        if (GeneralTool.isNotEmpty(userexaminationPaperScores)) {
            //有考试记录
            throw new GetServiceException(LocaleMessageUtils.getMessage("complete_answer_examinationPaper"));
        }
    }


    /**
     * @Description: 用户是否已有答题记录，如有答题记录，需判断答题记录与题库中相关问题是否允许多次答题，
     * 如不允许，数据返回需过滤已完成的题目；如所有考题已完成答题，则提示已完成答题。注意：不能只靠数据查询结果0作为唯一判断依据，可能存在题库没有任何考题的情况，需加入考虑
     * @Author: Jerry
     * @Date:11:32 2021/8/24
     */
    private List<ExaminationQuestionVo> valiedateGetExaminationQuestions(ExaminationActiveAndTimeVo ExaminationByexaminationPaperNum,
                                                                         Long fkUserId) {
        //查询该考卷是否有考题
//        Example example = new Example(ExaminationQuestionAssign.class);
//        example.createCriteria().andEqualTo("fkExaminationPaperId",ExaminationByexaminationPaperNum.getFkExaminationPaperId());
//        List<ExaminationQuestionAssign> examinationQuestionAssigns = examinationQuestionAssignMapper.selectByExample(example);

        List<ExaminationQuestionAssign> examinationQuestionAssigns = examinationQuestionAssignMapper.selectList(Wrappers.<ExaminationQuestionAssign>lambdaQuery()
                .eq(ExaminationQuestionAssign::getFkExaminationPaperId, ExaminationByexaminationPaperNum.getFkExaminationPaperId()));
        if (GeneralTool.isEmpty(examinationQuestionAssigns)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_examination_assigned"));
        }
        //获取该考卷对应的所有考题ids
        Set<Long> fkQuestionIds = examinationQuestionAssigns.stream().filter(examinationQuestionAssign -> examinationQuestionAssign.getTargetType() == 1).
                map(ExaminationQuestionAssign::getTargetId).collect(Collectors.toSet());
        //获取该考卷对应的所有考题类型ids
        Set<Long> fkQuestionTypeIds = examinationQuestionAssigns.stream().filter(examinationQuestionAssign -> examinationQuestionAssign.getTargetType() == 0).
                map(ExaminationQuestionAssign::getTargetId).collect(Collectors.toSet());
        //如果都为空，代表没有分配考题
        if (GeneralTool.isEmpty(fkQuestionIds) && GeneralTool.isEmpty(fkQuestionTypeIds)) {
            //该考卷没有分配考题
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_examination_assigned"));
        }
        //根据考题ids获取考题
//        example.clear();
//        example = new Example(ExaminationQuestion.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<ExaminationQuestion> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ExaminationQuestion::getIsActive, true);
        lambdaQueryWrapper.and(wrapper -> {
            if (GeneralTool.isNotEmpty(fkQuestionIds)) {
                wrapper.in(ExaminationQuestion::getId, fkQuestionIds);
            }
            if (GeneralTool.isNotEmpty(fkQuestionTypeIds)) {
                wrapper.or().in(ExaminationQuestion::getFkQuestionTypeId, fkQuestionTypeIds);
            }
        });
        List<ExaminationQuestion> examinationQuestions = examinationQuestionMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isEmpty(examinationQuestions)) {
            //该考卷没有激活的考题
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_examination_assigned"));
        }
        //解析考题集合获取考题id以及对应的是否允许重考状态
        Map<Long, Boolean> examinationQuestionIdsAndRetestsMap = getExaminationQuestionIdsAndRetests(examinationQuestions);
        //获取该用户的所有答题记录
//        example.clear();
//        example = new Example(UserExaminationQuestionScore.class);
        LambdaQueryWrapper<UserExaminationQuestionScore> userExaminationQuestionScoreLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userExaminationQuestionScoreLambdaQueryWrapper.eq(UserExaminationQuestionScore::getFkUserId, fkUserId).eq(UserExaminationQuestionScore::getFkExaminationPaperId,
                ExaminationByexaminationPaperNum.getFkExaminationPaperId());
        List<UserExaminationQuestionScore> userExaminationQuestionScores = userExaminationQuestionScoreMapper.selectList(userExaminationQuestionScoreLambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(userExaminationQuestionScores)) {
            //匹配考题和用户的答题记录，如果是不允许多次答题以及有答题记录的则需要将该题目移除
            for (UserExaminationQuestionScore userExaminationQuestionScore : userExaminationQuestionScores) {
                //有答题记录并且不允许多次答题
                if (examinationQuestionIdsAndRetestsMap.containsKey(userExaminationQuestionScore.getFkExaminationQuestionId())
                        && !examinationQuestionIdsAndRetestsMap.get(userExaminationQuestionScore.getFkExaminationQuestionId())) {
                    //移除该元素
                    examinationQuestions.removeIf(examinationQuestion -> examinationQuestion.getId().equals(userExaminationQuestionScore.getFkExaminationQuestionId()));
                    if (GeneralTool.isEmpty(examinationQuestions)) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("answer_all_examination"));
                    }
                }
            }
        }
        //查询考试记录表是否有分数为空值的记录，如果有的话代表是上一次考试未结束，继续上一次的考试；没有的话新增一条新纪录到考试记录表
        //操作id，记录本次考试的唯一标识
        String optGuid;
        LambdaQueryWrapper<UserexaminationPaperScore> userexaminationPaperScoreLambdaQueryWrapper = new LambdaQueryWrapper<>();

        //查询考试记录表是否有分数为空值的记录
        userexaminationPaperScoreLambdaQueryWrapper.eq(UserexaminationPaperScore::getFkExaminationPaperId, ExaminationByexaminationPaperNum.getFkExaminationPaperId()).
                eq(UserexaminationPaperScore::getFkUserId, fkUserId).isNull(UserexaminationPaperScore::getScore);
        List<UserexaminationPaperScore> userexaminationPaperScores = userExaminationPaperScoreMapper.selectList(userexaminationPaperScoreLambdaQueryWrapper);
        //随机获取指定条数的题目，如果为空则查询所有题目
        Integer questionCount = GeneralTool.isEmpty(ExaminationByexaminationPaperNum.getQuestionCount()) ? 0 :
                ExaminationByexaminationPaperNum.getQuestionCount();

        //当前记录数默认为1（显示当前题目条数要用）
        int nowCurrentRecord = 1;
        if (GeneralTool.isNotEmpty(userexaminationPaperScores)) {
            //有记录，代表上次的考试未结束，获取到上一次的uuid，并查询还剩下多少道题目
            UserexaminationPaperScore userexaminationPaperScore = userexaminationPaperScores.get(0);
            optGuid = userexaminationPaperScore.getOptGuid();
            //根据uuid查询答题记录
            List<ExaminationQuestionVo> questionByOptGuid = userExaminationQuestionScoreMapper.getQuestionByOptGuid(optGuid);
            if (GeneralTool.isNotEmpty(questionByOptGuid)) {
                //继续筛掉已经答过的考题
                for (ExaminationQuestionVo examinationQuestionVo : questionByOptGuid) {
                    //当前记录数+1（下次考试显示当前题目条数要用）
                    ++nowCurrentRecord;
                    examinationQuestions.removeIf(examinationQuestion -> examinationQuestion.getId().equals(examinationQuestionVo.getId()));
                    if (GeneralTool.isEmpty(examinationQuestions)) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("answer_all_examination"));
                    }
                }
                //随机生成剩下的考题（考卷的生成考题数减去上次做过的题目数量）
                if (questionCount > 0) {
                    questionCount -= questionByOptGuid.size();
                }
            }
        } else {
            //新增一条新纪录到考试记录表
            optGuid = UUID.randomUUID().toString();
            UserexaminationPaperScore userexaminationPaperScore = new UserexaminationPaperScore();
            userexaminationPaperScore.setOptGuid(optGuid);
            userexaminationPaperScore.setFkExaminationPaperId(ExaminationByexaminationPaperNum.getFkExaminationPaperId());
            userexaminationPaperScore.setFkExaminationId(ExaminationByexaminationPaperNum.getFkExaminationId());
            userexaminationPaperScore.setFkUserId(fkUserId);
            utilService.updateUserInfoToEntity(userexaminationPaperScore);
            userExaminationPaperScoreMapper.insertSelective(userexaminationPaperScore);
        }
        //考题ids
        Set<Long> questionIds = examinationQuestions.stream().map(ExaminationQuestion::getId).collect(Collectors.toSet());
        List<ExaminationQuestionVo> questionList = examinationQuestionMapper.getExaminationQuestionByIdsAndCount(questionIds, 0 >= questionCount ? null : questionCount);
        //随机生成的考题ids
        Set<Long> examinationQuestionIds = questionList.stream().map(ExaminationQuestionVo::getId).collect(Collectors.toSet());
        Map<Long, List<ExaminationAnswerVo>> examinationAnswers = iExaminationQuestionService.getExaminationAnswerByExaminationQuestionIds(examinationQuestionIds);
        //考题总数
        Integer questionSumCount = examinationQuestionMapper.getQuestionSumCount();
        //如果考题数量大于生成考题数，则考题总数取生成考题数
        if (GeneralTool.isNotEmpty(ExaminationByexaminationPaperNum.getQuestionCount()) && ExaminationByexaminationPaperNum.getQuestionCount() < questionSumCount) {
            questionSumCount = ExaminationByexaminationPaperNum.getQuestionCount();
        }

        for (ExaminationQuestionVo examinationQuestionVo : questionList) {
            examinationQuestionVo.setFkExaminationPaperId(ExaminationByexaminationPaperNum.getFkExaminationPaperId());
            examinationQuestionVo.setOptGuid(optGuid);
            //设置每道题目的选项
            examinationQuestionVo.setExaminationAnswers(examinationAnswers.get(examinationQuestionVo.getId()));
            //当前记录数
            examinationQuestionVo.setNowCurrentRecord(nowCurrentRecord);
            //考题总数
            examinationQuestionVo.setQuestionSumCount(questionSumCount);
        }
        return questionList;
    }

    /**
     * @Description: 解析考题集合获取考题id以及对应的是否允许重考状态
     * @Author: Jerry
     * @Date:12:06 2021/8/24
     */
    private Map<Long, Boolean> getExaminationQuestionIdsAndRetests(List<ExaminationQuestion> examinationQuestions) {
        Map<Long, Boolean> map = new HashMap<>();
        for (ExaminationQuestion examinationQuestion : examinationQuestions) {
            map.put(examinationQuestion.getId(), examinationQuestion.getIsRetest());
        }
        return map;
    }


    /**
     * @Description: 匹配称号
     * @Author: Jerry
     * @Date:11:04 2021/8/30
     */
    private void setScoreTitleName(List<ScoreTitleVo> scoreTitleVos, UserexaminationPaperScoreVo userexaminationPaperScoreVo) {
        if (GeneralTool.isNotEmpty(scoreTitleVos)) {
            //遍历排名
            for (ScoreTitleVo scoreTitleVo : scoreTitleVos) {
                //Integer rank = Integer.valueOf(userexaminationPaperScoreDto.getRank());
                Integer rank = new Double(Double.parseDouble(userexaminationPaperScoreVo.getRank())).intValue();
                Integer score = userexaminationPaperScoreVo.getScore();
                //匹配对应的分数,有可能称号列表中并没有设置分数，则匹配排名
                if (GeneralTool.isNotEmpty(scoreTitleVo.getScoreMin()) && GeneralTool.isNotEmpty(scoreTitleVo.getScoreMax())) {
                    //判断当前分数是否在这个区间
                    if (score >= scoreTitleVo.getScoreMin() && score <= scoreTitleVo.getScoreMax()) {
                        //匹配，设置称号名称
                        userexaminationPaperScoreVo.setScoreTitleName(scoreTitleVo.getTitle());
                        userexaminationPaperScoreVo.setScoreTitleFile(scoreTitleVo.getMediaAndAttachedVoList());
                        userexaminationPaperScoreVo.setColorCode(scoreTitleVo.getColorCode());
                        break;
                    }
                } else if (GeneralTool.isNotEmpty(scoreTitleVo.getScoreMin()) || GeneralTool.isNotEmpty(scoreTitleVo.getScoreMax())) {
                    //判断考生分数等于称号分数
                    if (score.equals(scoreTitleVo.getScoreMin()) || score.equals(scoreTitleVo.getScoreMax())) {
                        //匹配，设置称号名称
                        userexaminationPaperScoreVo.setScoreTitleName(scoreTitleVo.getTitle());
                        userexaminationPaperScoreVo.setScoreTitleFile(scoreTitleVo.getMediaAndAttachedVoList());
                        userexaminationPaperScoreVo.setColorCode(scoreTitleVo.getColorCode());
                        break;
                    }
                }
                //匹配排名
                if (GeneralTool.isNotEmpty(scoreTitleVo.getRankingMin()) && GeneralTool.isNotEmpty(scoreTitleVo.getRankingMax())) {
                    //判断当前排名是否在这个区间
                    if (rank >= scoreTitleVo.getRankingMin() && rank <= scoreTitleVo.getRankingMax()) {
                        //匹配，设置称号名称
                        userexaminationPaperScoreVo.setScoreTitleName(scoreTitleVo.getTitle());
                        userexaminationPaperScoreVo.setScoreTitleFile(scoreTitleVo.getMediaAndAttachedVoList());
                        userexaminationPaperScoreVo.setColorCode(scoreTitleVo.getColorCode());
                        break;
                    }
                } else if (GeneralTool.isNotEmpty(scoreTitleVo.getRankingMin()) || GeneralTool.isNotEmpty(scoreTitleVo.getRankingMax())) {
                    //判断考生排名等于称号排名
                    if (rank.equals(scoreTitleVo.getRankingMin()) || rank.equals(scoreTitleVo.getRankingMax())) {
                        //匹配，设置称号名称
                        userexaminationPaperScoreVo.setScoreTitleName(scoreTitleVo.getTitle());
                        userexaminationPaperScoreVo.setScoreTitleFile(scoreTitleVo.getMediaAndAttachedVoList());
                        userexaminationPaperScoreVo.setColorCode(scoreTitleVo.getColorCode());
                        break;
                    }
                }
            }
        }
    }

    /**
     * @Description: 根据用户ids获取BD名称(大区名称)
     * @Author: Jerry
     * @Date:14:22 2021/8/30
     */
    private Map<Long, String> getBdNamesByUserIds(Set<Long> userIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(userIds)) {
            return map;
        }
//        Example example = new Example(UserStaffBd.class);
//        example.createCriteria().andIn("fkUserId",userIds);
        List<UserStaffBd> userStaffBds = userStaffBdMapper.selectList(Wrappers.<UserStaffBd>lambdaQuery().in(UserStaffBd::getFkUserId, userIds));
        if (GeneralTool.isEmpty(userStaffBds)) {
            return map;
        }
        //获取所有的大区ids
        Set<Long> fkAreaRegionIds = userStaffBds.stream().map(UserStaffBd::getFkAreaRegionId).collect(Collectors.toSet());
        Map<Long, AreaRegionVo> getFkAreaRegionDtoByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkAreaRegionIds)) {
            //通过大区ids获取名称
//            getFkAreaRegionDtoByIds = institutionCenterClient.getAreaRegionDtoByIds(fkAreaRegionIds);
            Result<Map<Long, AreaRegionVo>> result = institutionCenterClient.getAreaRegionDtoByIds(fkAreaRegionIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                getFkAreaRegionDtoByIds = result.getData();
            }
        }
        for (UserStaffBd userStaffBd : userStaffBds) {
            AreaRegionVo areaRegionVo = getFkAreaRegionDtoByIds.get(userStaffBd.getFkAreaRegionId());
            if (GeneralTool.isNotEmpty(areaRegionVo)) {
                map.put(userStaffBd.getFkUserId(), areaRegionVo.getNameChn());
            }
        }
        return map;
    }


    /**
     * @Description: 根据考卷ids获取考试次数
     * @Author: Jerry
     * @Date:18:34 2021/9/14
     */
    private Map<Long, Integer> getScoreCountByFkExaminationPaperIds(Set<Long> fkExaminationPaperIds) {
        Map<Long, Integer> map = new HashMap<>();
//        Example example = new Example(UserexaminationPaperScore.class);
//        example.createCriteria().andIn("fkExaminationPaperId",fkExaminationPaperIds).andIsNotNull("score").andGreaterThan("score",5);
//        List<UserexaminationPaperScore> userexaminationPaperScores = userexaminationPaperScoreMapper.selectByExample(example);
        List<UserexaminationPaperScore> userexaminationPaperScores = userexaminationPaperScoreMapper.selectList(Wrappers.<UserexaminationPaperScore>lambdaQuery()
                .in(UserexaminationPaperScore::getFkExaminationPaperId, fkExaminationPaperIds)
                .isNotNull(UserexaminationPaperScore::getScore)
                .gt(UserexaminationPaperScore::getScore, 0));//大于5

        if (GeneralTool.isEmpty(userexaminationPaperScores)) {
            return map;
        }
        for (UserexaminationPaperScore userexaminationPaperScore : userexaminationPaperScores) {
            Long fkExaminationPaperId = userexaminationPaperScore.getFkExaminationPaperId();
            //如果集合中包含考试id，则往原记录+1
            if (map.containsKey(fkExaminationPaperId)) {
                Integer beforeCount = map.get(fkExaminationPaperId);
                map.put(fkExaminationPaperId, ++beforeCount);
                continue;
            }
            Integer count = 1;
            map.put(fkExaminationPaperId, count);
        }
        return map;
    }


    /**
     * @Description: 获取个人的考试次数
     * @Author: Jerry
     * @Date:14:53 2021/9/26
     */
    private Map<Long, Integer> getExamCountByFkExaminationPaperId(Long fkExaminationPaperId) {
        Map<Long, Integer> map = new HashMap<>();
//        Example example = new Example(UserexaminationPaperScore.class);
//        example.createCriteria().andEqualTo("fkExaminationPaperId",fkExaminationPaperId).andIsNotNull("score").andGreaterThan("score",5);
//        List<UserexaminationPaperScore> userexaminationPaperScores = userexaminationPaperScoreMapper.selectByExample(example);

        List<UserexaminationPaperScore> userexaminationPaperScores = userexaminationPaperScoreMapper.selectList(Wrappers.<UserexaminationPaperScore>lambdaQuery()
                .eq(UserexaminationPaperScore::getFkExaminationPaperId, fkExaminationPaperId)
                .isNotNull(UserexaminationPaperScore::getScore)
                .gt(UserexaminationPaperScore::getScore, 5));//大于5

        if (GeneralTool.isEmpty(userexaminationPaperScores)) {
            return map;
        }
        for (UserexaminationPaperScore userexaminationPaperScore : userexaminationPaperScores) {
            Long fkUserId = userexaminationPaperScore.getFkUserId();
            //如果集合中包含人员id，则往原记录+1
            if (map.containsKey(fkUserId)) {
                Integer beforeCount = map.get(fkUserId);
                map.put(fkUserId, ++beforeCount);
                continue;
            }
            Integer count = 1;
            map.put(fkUserId, count);
        }
        return map;
    }

    @Override
    public void exportAnswerSituation(HttpServletResponse response, Long fkExaminationPaperId) {
        HSSFWorkbook wb = new HSSFWorkbook();
        //生成.xls文件
        HSSFSheet sheet = wb.createSheet("excel");
        HSSFRow row1 = sheet.createRow(0);
        row1.createCell(0).setCellValue("名字（中）");
        row1.createCell(1).setCellValue("姓名(英)");
        row1.createCell(2).setCellValue("手机号");
        row1.createCell(3).setCellValue("邮箱");
        int councCol = 3;
        int countRow = 0;
        //试卷-》绑定的试题
        List<ExaminationQuestionAssign> examinationQuestionAssigns = examinationQuestionAssignMapper.getListByPaperIdAndExist(fkExaminationPaperId);
        ArrayList<Long> listIds = new ArrayList<>(examinationQuestionAssigns.size());
        //表头-》试卷绑定的试题
        for (int i = 0; i < examinationQuestionAssigns.size(); i++) {
            ExaminationQuestionAssign eqa = examinationQuestionAssigns.get(i);
            ExaminationQuestion examinationQuestion = examinationQuestionMapper.selectById(eqa.getTargetId());
            if(GeneralTool.isNotEmpty(examinationQuestion)){
                listIds.add(examinationQuestion.getId());
                councCol += 1 ;
                row1.createCell(councCol).setCellValue(examinationQuestion.getQuestion());
            }
        }
        //行，该试卷答题用户列表
        List<UserPaperScoreVo> UserExaminationPaperScore = userExaminationPaperScoreMapper.selectUserPaperScourceList(fkExaminationPaperId);
        if (GeneralTool.isNotEmpty(UserExaminationPaperScore)) {
            //遍历用户答卷列表r_user_examination_paper_score
            for (int j = 0; j < UserExaminationPaperScore.size(); j++) {
                UserPaperScoreVo ueps = UserExaminationPaperScore.get(j);
                //前4列用户资料
                //Map<String, Object> mapUser = userExaminationQuestionScoreMapper.getUserInfoById(ueps.getFkUserId());
                countRow += 1;
                HSSFRow row2 = sheet.createRow(countRow);
                row2.createCell(0).setCellValue(ueps.getWeChatNickName());
                row2.createCell(1).setCellValue(ueps.getNameEn());
                row2.createCell(2).setCellValue(ueps.getMobile());
                row2.createCell(3).setCellValue(ueps.getEmail());
                //答案情况
                List<UserExaminationQuestionScore> leqs = userExaminationQuestionScoreMapper.selectAnswers(ueps.getFkUserId(), fkExaminationPaperId,ueps.getOptGuid());
                for (int k = 0; k < leqs.size(); k++) {
                    UserExaminationQuestionScore e = leqs.get(k);
                    if (GeneralTool.isNotEmpty(e)) {
                        if (GeneralTool.isNotEmpty(e.getFkExaminationAnswerIds())) {
                            List<String> ansIds = Arrays.asList(e.getFkExaminationAnswerIds().split(","));
                            List<ExaminationAnswer> listAns = examinationAnswerMapper.selectBatchIds(ansIds);
                            //选择答案
                            if (GeneralTool.isNotEmpty(listAns)) {
                                String ansStr = listAns.stream().map(ExaminationAnswer::getAnswer).collect(Collectors.joining("，"));
                                row2.createCell(k + 4).setCellValue(ansStr);
                            }
                        } else {
                            row2.createCell(k + 4).setCellValue("");
                        }
                    }
                }
                if(GeneralTool.isNotEmpty(ueps.getScore())){
                    row2.createCell(leqs.size()+4).setCellValue(ueps.getScore());
                }else {
                    row2.createCell(leqs.size()+4).setCellValue("");
                }

            }
        }
        row1.createCell(councCol+1).setCellValue("成绩");
        try {
            OutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public String getExamLinkByPaperId(Long examinationPaperId) {
        Result<String> stringResult = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.EXAM_LINK.key);
        return stringResult.getData()+examinationPaperId;
    }

    @Override
    public void copyPaperByPaperId(Long examinationPaperId) {
        //原考卷信息
        ExaminationPaper examinationPaper = examinationPaperMapper.selectById(examinationPaperId);
        examinationPaper.setId(null);
        examinationPaper.setName("Copy-"+examinationPaper.getName());
        examinationPaper.setGmtModified(null);
        examinationPaper.setGmtModifiedUser(null);
        utilService.setCreateInfo(examinationPaper);
        examinationPaperMapper.insert(examinationPaper);
        Long newP_id = examinationPaper.getId();
        //新考卷信息
        ExaminationPaper examinationPaperNew = examinationPaperMapper.selectById(newP_id);
        //修改考卷编号
        examinationPaperNew.setNum("P00"+newP_id);
        examinationPaperNew.setFkCompanyId(examinationPaper.getFkCompanyId());
        examinationPaperNew.setDescription(examinationPaper.getDescription());
        examinationPaperMapper.updateById(examinationPaperNew);

        //考卷考题绑定关系
        List<ExaminationQuestionAssign> list = examinationQuestionAssignMapper.selectList(Wrappers.<ExaminationQuestionAssign>lambdaQuery().eq(ExaminationQuestionAssign::getFkExaminationPaperId,examinationPaperId));
        if(GeneralTool.isNotEmpty(list)){
            for(ExaminationQuestionAssign eqa : list){
                eqa.setId(null);
                eqa.setFkExaminationPaperId(newP_id);
                eqa.setGmtModified(null);
                eqa.setGmtModifiedUser(null);
                utilService.setCreateInfo(eqa);
                examinationQuestionAssignMapper.insert(eqa);
                Long eqaId = eqa.getId();
                eqa.setViewOrder(eqaId.intValue());
                eqa.setGmtModified(null);
                eqa.setGmtModifiedUser(null);
                examinationQuestionAssignMapper.updateById(eqa);
            }
        }
    }
}
