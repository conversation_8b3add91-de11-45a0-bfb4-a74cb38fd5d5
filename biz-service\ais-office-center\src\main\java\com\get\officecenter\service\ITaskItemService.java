package com.get.officecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.*;
import com.get.officecenter.vo.CommentStatisticsVo;
import com.get.officecenter.vo.TaskItemVo;
import com.get.officecenter.entity.Comment;
import com.get.officecenter.entity.TaskItem;
import com.get.officecenter.dto.TaskItemDto;
import com.get.officecenter.vo.TaskStatisticsVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;

/**
 * 子任务Service层
 */
public interface ITaskItemService extends IService<TaskItem> {

    /**
     * 根据主任务ID集合，获取子任务列表
     *
     * @param taskIds 主任务ids
     * @return
     */
    List<TaskItem> getTaskItemList(List<Long> taskIds);


    /**
     * 根据任务id获取子任务接收人id
     */
    List<Long> getFkStaffIdTo(List<Long> taskIds);


    /**
     * 获取未完成申请计划的接收人id
     */
    List<Long> getStaffIdByStatus(List<Long> taskIds);



    /**
     * 获取子任务列表 分页
     *
     * @param taskItemDto 搜索条件
     * @param page       分页参数
     * @return
     */
    List<TaskItemVo> getTaskItems(TaskItemDto taskItemDto, Page page);


    /**
     * 获取子任务导出数据
     */
    List<TaskItemVo> getTaskItemExportData(TaskItemDto taskItemDto,Long loginId,String locale);


    /**
     * 设置子任务完成
     *
     * @param taskItemId 子任务id
     * @return
     */
    UpdateResponseBo setupFinish(Long taskItemId);

    /**
     * 设置子任务未能完成
     *
     * @param taskItemId 子任务id
     * @return
     */
    UpdateResponseBo setupUnFinish(Long taskItemId);

    /**
     * 添加子任务评论
     *
     * @param taskItemId 子任务id
     * @param comment    评论
     * @return
     */
    SaveResponseBo addTaskItemComment(Long taskItemId, String comment,Integer status);

    /**
     * 获取子任务评论列表
     *
     * @param taskItemId 子任务Id
     * @param page       分页参数
     * @return
     */
    ListResponseBo<Comment> getTaskItemCommentList(Long taskItemId, Page page);

    /**
     * 更新子任务评论
     *
     * @param taskItemId 子任务id
     * @param commentId  评论id
     * @param comment    评论内容
     * @return
     */
    SaveResponseBo updateTaskItemComment(Long taskItemId, Long commentId, String comment);

    /**
     * 导出子任务列表
     * @param response
     * @param taskItemDto
     */
    void exportTaskItemList(HttpServletResponse response, TaskItemDto taskItemDto,  Page page);

    /**
     * 个人完成度统计表
     * @param data
     * @param page
     * @return
     */
    List<TaskStatisticsVo> getPersonalTaskStatistics(TaskItemDto data, Page page);
    //List<TaskStatisticsVo> getPersonalTaskStatistics(TaskItemDto data);


    /**
     * 导出个人完成度统计表
     * @param response
     * @param taskItemDto
     */
    void exportTaskStatistics(HttpServletResponse response, TaskItemDto taskItemDto);

    /**
     * 批量设置完成和未完成
     * @param type
     * @param taskItemIds
     * @return
     */
    ResponseBo batchSetup(Integer type, Set<Long> taskItemIds);

    /**
     * 批量新增子任务的评论
     * @param taskItemIds
     * @param comment
     * @return
     */
    SaveResponseBo batchAddTaskItemComment(Set<Long> taskItemIds, String comment,Integer status);



    /**
     * 获取任务反馈统计列表
     * @param taskId
     * @param typeKey
     * @return
     */
    List<CommentStatisticsVo> getTaskFeedbackStatistics(Long taskId,String typeKey);

    /**
     * 获取任务反馈统计列表总数
     * @param taskId
     * @param typeKey
     * @return
     */
    CommentStatisticsVo getTaskFeedbackStatisticsTotal(Long taskId, String typeKey);

    /**
     * 导出反馈统计
     * @param response
     * @param
     * @param
     */
    void exportTaskFeedbackStatistics(HttpServletResponse response,Long taskId, String typeKey );

    /**
     * 根据子任务id获取子任
     */
    TaskItem getTaskItem(Long taskItemId);
}
