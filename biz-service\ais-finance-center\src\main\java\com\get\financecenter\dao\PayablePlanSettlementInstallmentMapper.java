package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.financecenter.dto.CancelSettlementDto;
import com.get.salecenter.dto.CommissionSummaryBatchDto;
import com.get.salecenter.dto.PayablePlanSettlementBatchExchangeDto;
import com.get.financecenter.entity.PayablePlanSettlementInstallment;
import com.get.salecenter.vo.CommissionSummaryBatchItemVo;
import com.get.salecenter.vo.CommissionSummaryBatchPayablePlanVo;
import com.get.salecenter.vo.CommissionSummaryBatchVo;
import com.get.salecenter.vo.IFileInfoVo;
import com.get.salecenter.vo.PayablePlanVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Mapper
public interface PayablePlanSettlementInstallmentMapper extends BaseMapper<PayablePlanSettlementInstallment>, GetMapper<PayablePlanSettlementInstallment> {

    /**
     * 获取该应付计划分期表各状态已生成的实付总金额
     *
     * @Date 16:30 2022/4/20
     * <AUTHOR>
     */
    BigDecimal getInsertSettlementAmountActual(@Param("payablePlanId") Long payablePlanId);

    /**
     * 获取该应付计划之前已生成的预付金额
     *
     * @Date 16:30 2022/4/20
     * <AUTHOR>
     */
    BigDecimal getInsertSettlementPrepaidAmount(@Param("payablePlanId") Long payablePlanId);

    /**
     * 获取该应付计划非实付总金额
     *
     * @Date 16:30 2022/4/20
     * <AUTHOR>
     */
    BigDecimal getInsertSettlementNotPrepaidAmount(@Param("payablePlanId") Long payablePlanId);

    /**
     * 该应付计划已产生的分期表数据的非预付金额 算出被预付对冲掉的金额
     *
     * @Date 17:12 2022/4/22
     * <AUTHOR>
     */
    BigDecimal getInsertSettlementPrepaidHedgingAmount(@Param("payablePlanId") Long payablePlanId);

    /**
     * 该应付计划已产生的分期表数据的非预付手续费金额 算出被预付对冲掉的手续金额
     *
     * @Date 17:12 2022/4/22
     * <AUTHOR>
     */
    BigDecimal getInsertSettlementPrepaidHedgingServiceFeeAmount(@Param("payablePlanId") Long payablePlanId);

    List<PayablePlanSettlementInstallment> getList();

    List<PayablePlanSettlementInstallment> getPayPlanList();

    /**
     * 更新固定五块钱手续费
     * @param ids
     */
    void updateFee(@Param("ids") Set<Long> ids);

    /**
     * 根据应付计划id获取已付佣金金额
     *
     * @Date 17:58 2023/12/12
     * <AUTHOR>
     */
    BigDecimal getAmountPaidByPayablePlanId(@Param("payablePlanId") Long payablePlanId);

    /**
     * 获取取消的批量结算数据
     *
     * @Date 16:30 2024/5/7
     * <AUTHOR>
     */
    List<CancelSettlementDto> cancelFinancialBatchSettlement(@Param("numSettlementBatch") String numSettlementBatch);

    List<PayablePlanSettlementInstallment> getCancelSettlement(@Param("cancelSettlementVoList") List<CancelSettlementDto> cancelSettlementVoList);

    /**
     * 获取第一步佣金
     * @param payablePlanId
     * @return
     */
    BigDecimal getFirstStepCommission(@Param("payablePlanId") Long payablePlanId);


    void saveIsExchangeInput(@Param("numSettlementBatchs")List<PayablePlanSettlementBatchExchangeDto> numSettlementBatchs,
                             @Param("complete")int complete);
    /**
     * 根据批次号获取对应的应付计划
     *
     * @Date 16:40 2022/4/3
     * <AUTHOR>
     */
    List<CommissionSummaryBatchPayablePlanVo> getPayablePlanByNumSettlementBatch(@Param("numSettlementBatch") String numSettlementBatch);

    /**
     * 根据财务批次号获取应付计划信息以及对应代理id
     *
     * @Date 11:36 2022/1/18
     * <AUTHOR>
     */
    List<PayablePlanVo> getPayablePlanInfoAndAgentIdByNumSettlementBatch(@Param("numSettlementBatch") String numSettlementBatch);


    /**
     * 财务佣金汇总批次子项列表
     *
     * @Date 14:36 2021/12/27
     * <AUTHOR>
     */
    List<CommissionSummaryBatchItemVo> commissionSummaryBatchItemList(@Param("commissionSummaryBatchDto") CommissionSummaryBatchDto commissionSummaryBatchDto);


    /**
     * 先根据Vo条件筛选一遍数据，获取筛选后的财务批次号
     *
     * @Date 16:13 2022/1/14
     * <AUTHOR>
     */
    List<String> getNumSettlementBatchByVo(@Param("commissionSummaryBatchDto") CommissionSummaryBatchDto commissionSummaryBatchDto);

    List<String> selectisExchangeInputNo(List<String> numSettlementBatchList);


    /**
     * feign 财务佣金汇总批次列表
     *
     * @Date 15:42 2021/12/24
     * <AUTHOR>
     */
    List<CommissionSummaryBatchVo> commissionSummaryBatchList(IPage<CommissionSummaryBatchVo> iPage, @Param("commissionSummaryBatchDto") CommissionSummaryBatchDto commissionSummaryBatchDto,
                                                              @Param("numSettlementBatchList") List<String> numSettlementBatchList);

    /**
     * 获取结算币种
     * <select id="getPayablePlanByNumSettlementBatch" resultType="com.get.salecenter.entity.PayablePlan">
     *
     * @Date 19:02 2022/3/15
     * <AUTHOR>
     */
    String selectAgentAccountCurrencyTypeNum(@Param("numSettlementBatch") String numSettlementBatch);

    /**
     * 获取iFile Excel信息
     *
     * @Date 14:14 2022/3/8
     * <AUTHOR>
     */
    List<IFileInfoVo> iFileGroupByCurrencyInfo(@Param("numSettlementBatch") String numSettlementBatch);


    /**
     * 获取iFile Excel信息
     *
     * @Date 14:14 2022/3/5
     * <AUTHOR>
     */
    List<IFileInfoVo> iFileExcelInfo(@Param("numSettlementBatch") String numSettlementBatch);

    /**
     * 获取需要对冲金额
     * @param payablePlanId
     * @return
     */
    BigDecimal getHedgeAmount(Long payablePlanId);


}