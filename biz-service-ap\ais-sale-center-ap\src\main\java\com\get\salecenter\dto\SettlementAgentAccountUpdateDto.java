package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 更新佣金结算标记Vo类
 *
 * <AUTHOR>
 * @date 2022/3/14 17:35
 */
@Data
public class SettlementAgentAccountUpdateDto {
    @NotNull(message = "代理id不能为空")
    @ApiModelProperty(value = "代理id")
    private Long fkAgentId;

    @NotNull(message = "变更后 学生代理合同账户Id不能为空")
    @ApiModelProperty(value = "学生代理合同账户Id")
    private Long fkAgentContractAccountId;


    @NotNull(message = "变更后 学生代理合同账户币种编号不能为空")
    @ApiModelProperty(value = "学生代理合同账户币种编号")
    private String agentContractAccountNum;

//    @NotNull(message = "变更前 学生代理合同账户Id不能为空")
    @ApiModelProperty(value = "变更前 学生代理合同账户Id")
    private Long sourceFkAgentContractAccountId;

//    @NotNull(message = "变更前 学生代理合同账户币种编号不能为空")
    @ApiModelProperty(value = "变更前 学生代理合同账户币种编号")
    private String sourceAgentContractAccountNum;


    @NotNull(message = "应付计划Id不能为空")
    @ApiModelProperty(value = "应付计划id")
    private Long payablePlanId;

    @ApiModelProperty(value = "结算状态：0未结算/1结算中/2代理确认/3财务确认")
    private Integer statusSettlement;

}
