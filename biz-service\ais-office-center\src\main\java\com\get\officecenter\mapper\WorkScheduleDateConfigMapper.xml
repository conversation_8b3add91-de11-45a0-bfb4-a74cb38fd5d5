<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.officecenter.mapper.WorkScheduleDateConfigMapper">
    <select id="checkWorkScheduleDateConfig" resultType="java.lang.Integer">
        SELECT COUNT(0) FROM m_work_schedule_date_config WHERE fk_company_id=#{fkCompanyId}
        AND DATE_FORMAT(schedule_date,'%Y-%m-%d') = DATE_FORMAT(#{scheduleDate},'%Y-%m-%d')
        <if test="id != null">
            AND id &lt;&gt; #{id}
        </if>
    </select>

    <select id="getYearSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT DISTINCT `year` AS name FROM  m_work_schedule_date_config WHERE fk_company_id=#{fkCompanyId} AND `year` IS NOT NULL ORDER BY `year` DESC
    </select>

    <select id="getWorkScheduleDateConfigByDate" resultType="com.get.officecenter.entity.WorkScheduleDateConfig">
      SELECT * FROM m_work_schedule_date_config
       WHERE DATE_FORMAT(#{startTime},'%Y-%m-%d') &lt;= DATE_FORMAT(schedule_date,'%Y-%m-%d')
       AND DATE_FORMAT(#{endTime},'%Y-%m-%d') &gt;= DATE_FORMAT(schedule_date,'%Y-%m-%d')
       AND fk_company_id = #{fkCompanyId}
        <if test="fkDepartmentId != null">
            AND (fk_department_id = #{fkDepartmentId} OR fk_department_id IS NULL)
        </if>
    </select>

</mapper>