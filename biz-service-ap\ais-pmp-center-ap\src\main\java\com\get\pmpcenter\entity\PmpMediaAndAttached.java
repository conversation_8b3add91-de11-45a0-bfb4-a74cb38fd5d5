package com.get.pmpcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@TableName("s_media_and_attached")
public class PmpMediaAndAttached extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "文件guid(文档中心)")
    private String fkFileGuid;

    @ApiModelProperty(value = "表名")
    private String fkTableName;

    @ApiModelProperty(value = "表Id")
    private Long fkTableId;

    @ApiModelProperty(value = "类型关键字，如：institution_mov/institution_pic/alumnus_head_icon")
    private String typeKey;

    @ApiModelProperty(value = "索引值(默认从0开始，同一类型下值唯一)")
    private Integer indexKey;

    @ApiModelProperty(value = "链接")
    private String link;

    @ApiModelProperty(value = "备注")
    private String remark;
}
