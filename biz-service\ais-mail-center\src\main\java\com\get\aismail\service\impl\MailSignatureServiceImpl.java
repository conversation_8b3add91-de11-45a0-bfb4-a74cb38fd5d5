package com.get.aismail.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.get.aismail.dao.MFileMailMapper;
import com.get.aismail.dao.MMailAccountMapper;
import com.get.aismail.dao.MMailSignatureMapper;
import com.get.aismail.entity.MFileMail;
import com.get.aismail.entity.MMailAccount;
import com.get.aismail.entity.MMailSignature;
import com.get.aismail.service.IMailSignatureService;
import com.get.aismail.service.ITencentCloudService;
import com.get.aismail.utils.AppendixUtils;
import com.get.aismail.vo.AddNewSignature;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.StaffInfo;
import com.get.core.secure.utils.SecureUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.get.core.tool.api.ResultCode.BAD_REQUEST;

@Slf4j
@Service
public class MailSignatureServiceImpl implements IMailSignatureService {
    @Resource
    private MMailSignatureMapper mMailSignatureMapper;

    @Resource
    private MMailAccountMapper mMailAccountMapper;

    @Value("${file.tencentcloudimage.bucketname}")
    private String imageBucketName;

    @Resource
    private ITencentCloudService tencentCloudService;

    @Resource
    private MFileMailMapper mFileMailMapper;

    @Resource
    private Environment env;

    @Override
    public void addNewSignature(AddNewSignature addNewSignature) throws Exception {
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        Long fkPlatformUserId = staffInfo.getStaffId();
        LambdaQueryWrapper<MMailAccount> accountQueryWrapper = new LambdaQueryWrapper<>();
        accountQueryWrapper.eq(MMailAccount::getFkPlatformUserId, fkPlatformUserId);
        List<MMailAccount> accounts = mMailAccountMapper.selectList(accountQueryWrapper);
        if (accounts.isEmpty()) {
            throw new GetServiceException(BAD_REQUEST, LocaleMessageUtils.getMessage("user_does_not_exist"));
        }
        LambdaQueryWrapper<MMailSignature> signatureQueryWrapper = new LambdaQueryWrapper<>();
        signatureQueryWrapper.eq(MMailSignature::getFkPlatformUserId, fkPlatformUserId);
        signatureQueryWrapper.eq(MMailSignature::getSignatureTitle, addNewSignature.getSignatureTitle());
        List<MMailSignature> signatures = mMailSignatureMapper.selectList(signatureQueryWrapper);
        if (!signatures.isEmpty()) {
            throw new GetServiceException(BAD_REQUEST, LocaleMessageUtils.getMessage("a_signature_with_the_same_title_already_exists"));
        }
        if (addNewSignature.isDefault()) {
            LambdaQueryWrapper<MMailSignature> defaultSignatureQueryWrapper = new LambdaQueryWrapper<>();
            defaultSignatureQueryWrapper.eq(MMailSignature::getFkPlatformUserId, fkPlatformUserId);
            defaultSignatureQueryWrapper.eq(MMailSignature::getIsDefault, Boolean.TRUE);
            List<MMailSignature> defaultSignatures = mMailSignatureMapper.selectList(defaultSignatureQueryWrapper);
            for (MMailSignature defaultSignature : defaultSignatures) {
                defaultSignature.setIsDefault(false);
                defaultSignature.setGmtModified(LocalDateTime.now());
                defaultSignature.setGmtModifiedUser(staffInfo.getName());
                mMailSignatureMapper.updateById(defaultSignature);
            }
        }
        MMailSignature mMailSignature = new MMailSignature();
        mMailSignature.setFkPlatformCode("AIS");
        mMailSignature.setFkPlatformUserId(fkPlatformUserId);
        mMailSignature.setSignatureTitle(addNewSignature.getSignatureTitle());
        // 插入富文本应该解码
        mMailSignature.setSignatureContent(java.net.URLDecoder.decode(addNewSignature.getSignatureContent(), "utf-8"));
        mMailSignature.setIsDefault(addNewSignature.isDefault());
        mMailSignature.setGmtCreate(LocalDateTime.now());
        mMailSignature.setGmtCreateUser(staffInfo.getName());
        LambdaQueryWrapper<MMailSignature> checkSignatureQueryWrapper = new LambdaQueryWrapper<>();
        checkSignatureQueryWrapper.eq(MMailSignature::getFkPlatformUserId, fkPlatformUserId);
        checkSignatureQueryWrapper.orderByAsc(MMailSignature::getViewOrder);
        List<MMailSignature> checkSignatures = mMailSignatureMapper.selectList(checkSignatureQueryWrapper);
        if (!checkSignatures.isEmpty()) {
            int maxViewOrder = checkSignatures.get(checkSignatures.size() - 1).getViewOrder();
            mMailSignature.setViewOrder(maxViewOrder + 1);
        } else {
            mMailSignature.setViewOrder(0);
        }
        mMailSignatureMapper.insert(mMailSignature);
    }

    @Override
    public MFileMail uploadFileForSignature(MultipartFile file) throws Exception {
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        String annexSavePath = env.getProperty("mail.up-annex-path");
        if (file.isEmpty()) {
            throw new GetServiceException(BAD_REQUEST, "上传失败");
        }
        String fileName1 = file.getOriginalFilename();
        String savePath = annexSavePath + UUID.randomUUID() + "_" + fileName1.replaceAll(" ", "").replaceAll(" ", "");
        File dest = new File(savePath);
        // 将文件保存到指定路径
        file.transferTo(dest);
        MFileMail fileMail = new MFileMail();
        // 源文件名
        String fileNameOrc = dest.getName();
        // 获取后缀名
        String fileTypeOrc = "";
        int lastDotIndex = fileNameOrc.lastIndexOf('.');
        if (lastDotIndex != -1) {
            if (fileNameOrc.substring(lastDotIndex + 1).length() > 4) {
                fileTypeOrc = fileNameOrc.substring(fileNameOrc.length() - 4);
            } else {
                fileTypeOrc = fileNameOrc.substring(lastDotIndex + 1);
            }
        }
        // 文件guid
        String fileGuid = UUID.randomUUID().toString().replaceAll("-", "");
        String fileurl = subString(AppendixUtils.getFileSignaturePath(fileNameOrc));
        int j = fileurl.lastIndexOf("/");
        // 新文件名
        String fileName = fileurl.substring(j + 1, fileurl.length());
        fileMail.setFilePath(fileurl);
        fileMail.setFileNameOrc(fileName1);
        fileMail.setFileTypeOrc(fileTypeOrc);
        fileMail.setFileName(fileName);
        fileMail.setFileGuid(fileGuid);
        fileMail.setGmtCreate(LocalDateTime.now());
        fileMail.setGmtCreateUser(staffInfo.getName());
        String bucketName = imageBucketName;
        log.info("文件传入的桶的名字是{}", bucketName);
        tencentCloudService.uploadObject(true, bucketName, dest, fileurl);
        fileMail.setFileKey(fileurl);
        mFileMailMapper.insert(fileMail);
        // 本地删除
        Files.delete(dest.toPath());
        return fileMail;
    }

    @Override
    public void deleteSignature(MMailSignature mSignature) throws Exception {
        long id = mSignature.getId();
        LambdaQueryWrapper<MMailSignature> signatureQueryWrapper = new LambdaQueryWrapper<>();
        signatureQueryWrapper.eq(MMailSignature::getId, id);
        List<MMailSignature> signatures = mMailSignatureMapper.selectList(signatureQueryWrapper);
        if (!signatures.isEmpty()) {
            for (MMailSignature signature : signatures) {
                String signatureContent = signature.getSignatureContent();
                // 正则表达式匹配<img>标签中的src属性
                String imgRegex = "<img[^>]+src\\s*=\\s*['\"]([^'\"]+)['\"][^>]*>";
                Pattern pattern = Pattern.compile(imgRegex, Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(signatureContent);
                // 创建一个列表来保存所有的src属性值
                List<String> srcList = new ArrayList<>();
                // 查找所有匹配的src属性值并添加到列表中
                while (matcher.find()) {
                    srcList.add(matcher.group(1));
                }
                String bucket = "https://" + imageBucketName + ".cos.ap-shanghai.myqcloud.com";
                for (String src : srcList) {
                    QueryWrapper<MFileMail> mFileMailQueryWrapper = new QueryWrapper<>();
                    mFileMailQueryWrapper.eq("file_key", src.replace(bucket, ""));
                    List<MFileMail> mFileMailList = mFileMailMapper.selectList(mFileMailQueryWrapper);
                    if (!mFileMailList.isEmpty()) {
                        MFileMail mFileMail = mFileMailList.get(0);
                        // 在桶中将文件删除
                        tencentCloudService.deleteObject(imageBucketName, mFileMail.getFileKey());
                        // 在 mFile库中将数据删除
                        mFileMailMapper.deleteById(mFileMail.getId());
                    } else {
                        log.info("签名id是{}，在file表中找不到key是{}的文件", id, src.replace(bucket, ""));
                    }
                }
                mMailSignatureMapper.deleteById(signature.getId());
            }
        } else {
            throw new GetServiceException(BAD_REQUEST, "签名不存在");
        }
    }

    @Override
    public void updateSignature(List<MMailSignature> mSignatures) throws Exception {
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        for (int i = 0; i < mSignatures.size(); i++) {
            MMailSignature signature = mSignatures.get(i);
            signature.setViewOrder(i);
            signature.setGmtModified(LocalDateTime.now());
            signature.setGmtModifiedUser(staffInfo.getName());
            mMailSignatureMapper.updateById(signature);
        }
    }

    @Override
    public List<MMailSignature> selectAllSignatures() throws Exception {
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        Long fkPlatformUserId = staffInfo.getStaffId();
        LambdaQueryWrapper<MMailAccount> accountQueryWrapper = new LambdaQueryWrapper<>();
        accountQueryWrapper.eq(MMailAccount::getFkPlatformUserId, fkPlatformUserId);
        List<MMailAccount> accounts = mMailAccountMapper.selectList(accountQueryWrapper);
        if (accounts.isEmpty()) {
            throw new GetServiceException(BAD_REQUEST, LocaleMessageUtils.getMessage("user_does_not_exist"));
        }
        LambdaQueryWrapper<MMailSignature> signatureQueryWrapper = new LambdaQueryWrapper<>();
        signatureQueryWrapper.eq(MMailSignature::getFkPlatformUserId, fkPlatformUserId);
        return mMailSignatureMapper.selectList(signatureQueryWrapper);
    }

    public String subString(String url) {
        if (url.contains("target")) {
            url = url.replace("/data/project/get/file-center/target", "");
            url = url.replace("/data/project/get/app-file-center/target", "");
            return url;
        } else {
            return url;
        }

    }
}
