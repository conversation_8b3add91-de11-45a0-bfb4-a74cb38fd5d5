package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2021/9/9
 * @TIME: 12:32
 * @Description:
 **/
@Data
public class ConventionAwardPayDto extends BaseVoEntity implements Serializable {
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    private Long fkConventionId;

    /**
     * 峰会参展人员Id
     */
    @ApiModelProperty(value = "峰会参展人员Id")
    private Long fkConventionPersonId;

    /**
     * 支付数量
     */
    @ApiModelProperty(value = "支付数量")
    private Integer payCount;

    /**
     * 支付金额
     */
    @ApiModelProperty(value = "支付金额")
    private BigDecimal payAmount;

    /**
     * 系统支付单号，guid
     */
    @ApiModelProperty(value = "系统支付单号，guid")
    private String paySystemOrderNum;

    /**
     * 支付状态：0未支付/1完成支付
     */
    @ApiModelProperty(value = "支付状态：0未支付/1完成支付")
    private Integer payType;
    /**
     * 搜索关键词
     */
    @ApiModelProperty(value = "搜索关键词")
    private String keyWord;
}
