package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * AI学生申请计划Vo
 */
@Data
public class AiStudentOfferItemInfoVo {

    @ApiModelProperty(value = "学生id")
    private Long fkStudentId;

    @ApiModelProperty(value = "学校名字")
    private String institutionName;

    @ApiModelProperty(value = "国家名字")
    private String countryName;

    @ApiModelProperty(value = "课程名字")
    private String courseName;

    @ApiModelProperty(value = "代理名字")
    private String agentName;

    @ApiModelProperty(value = "开学时间")
    private Date openingTime;

    @ApiModelProperty(value = "学费")
    private BigDecimal tuitionAmount;

    @ApiModelProperty(value = "当前步骤名字")
    private String stepName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "bd名字")
    private String bdName;


}
