package com.get.helpcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.helpcenter.dao.MediaAndAttachedMapper;
import com.get.helpcenter.vo.MediaAndAttachedVo;
import com.get.helpcenter.entity.HelpMediaAndAttached;
import com.get.helpcenter.service.IMediaAndAttachedService;
import com.get.helpcenter.dto.MediaAndAttachedDto;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

;

/**
 * @author: Hardy
 * @create: 2021/5/11 15:33
 * @verison: 1.0
 * @description:
 */
@Service
public class MediaAndAttachedServceImpl implements IMediaAndAttachedService {
    @Resource
    private MediaAndAttachedMapper attachedMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IFileCenterClient fileCenterClient;

    @Override
    public List<FileDto> upload(MultipartFile[] multipartFiles) {
        if (GeneralTool.isEmpty(multipartFiles)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }
        List<FileDto> fileDtos = null;
        Result<List<FileDto>> result = fileCenterClient.upload(multipartFiles, LoggerModulesConsts.HELPCENTER);
        if (!result.isSuccess()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_failure"));
        }
        JSONArray jsonArray = JSONArray.fromObject(result.getData());
        fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
        return fileDtos;
    }

    @Override
    public List<FileDto> uploadAppendix(MultipartFile[] multipartFiles) {
        if (GeneralTool.isEmpty(multipartFiles)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }
        Result<List<FileDto>> result = fileCenterClient.uploadAppendix(multipartFiles, LoggerModulesConsts.HELPCENTER);
        if (!result.isSuccess()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_failure"));
        }
        JSONArray jsonArray = JSONArray.fromObject(result.getData());
        List<FileDto> fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
        return fileDtos;
    }

    @Override
    public void deleteMediaAttached(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        HelpMediaAndAttached mediaAndAttached = attachedMapper.selectById(id);
        if (GeneralTool.isEmpty(mediaAndAttached)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        int i = attachedMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Override
    public MediaAndAttachedVo addMediaAndAttached(MediaAndAttachedDto mediaAttachedVo) {
        if (GeneralTool.isEmpty(mediaAttachedVo.getFkTableName())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("tableName_null"));
        }
        HelpMediaAndAttached andAttached = BeanCopyUtils.objClone(mediaAttachedVo, HelpMediaAndAttached::new);
        Integer nextIndexKey = attachedMapper.getNextIndexKey(andAttached.getFkTableId(), mediaAttachedVo.getFkTableName());
        nextIndexKey = GeneralTool.isNotEmpty(nextIndexKey) ? nextIndexKey : 0;
        //实例化对象
        andAttached.setIndexKey(nextIndexKey);
        utilService.updateUserInfoToEntity(andAttached);
        attachedMapper.insertSelective(andAttached);
        MediaAndAttachedVo mediaAndAttachedVo = BeanCopyUtils.objClone(andAttached, MediaAndAttachedVo::new);
        mediaAndAttachedVo.setFilePath(mediaAttachedVo.getFilePath());
        mediaAndAttachedVo.setFileNameOrc(mediaAttachedVo.getFileNameOrc());
        mediaAndAttachedVo.setTypeValue(FileTypeEnum.getValue(mediaAttachedVo.getTypeKey()));
        mediaAndAttachedVo.setId(andAttached.getId());
        mediaAndAttachedVo.setFileKey(mediaAttachedVo.getFileKey());
        return mediaAndAttachedVo;
    }

    @Override
    public List<MediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo) {
        if (GeneralTool.isEmpty(attachedVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        LambdaQueryWrapper<HelpMediaAndAttached> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(attachedVo.getTypeKey())) {
            lambdaQueryWrapper.eq(HelpMediaAndAttached::getTypeKey, attachedVo.getTypeKey());
        }
        lambdaQueryWrapper.eq(HelpMediaAndAttached::getFkTableName, attachedVo.getFkTableName());
        lambdaQueryWrapper.eq(HelpMediaAndAttached::getFkTableId, attachedVo.getFkTableId());
        lambdaQueryWrapper.orderByDesc(HelpMediaAndAttached::getIndexKey);
        List<HelpMediaAndAttached> mediaAndAttacheds = attachedMapper.selectList(lambdaQueryWrapper);
        return getFileMedia(mediaAndAttacheds);
    }

    @Override
    public List<MediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo, Page page) {
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<MediaAndAttached> mediaAndAttacheds = getMediaAndAttacheds(attachedVo);
//        page.restPage(mediaAndAttacheds);

        if (GeneralTool.isEmpty(attachedVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        LambdaQueryWrapper<HelpMediaAndAttached> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(attachedVo.getTypeKey())) {
            lambdaQueryWrapper.eq(HelpMediaAndAttached::getTypeKey, attachedVo.getTypeKey());
        }
        lambdaQueryWrapper.eq(HelpMediaAndAttached::getFkTableName, attachedVo.getFkTableName());
        lambdaQueryWrapper.eq(HelpMediaAndAttached::getFkTableId, attachedVo.getFkTableId());
        lambdaQueryWrapper.orderByDesc(HelpMediaAndAttached::getIndexKey);
        IPage<HelpMediaAndAttached> mediaAndAttachedIPage = attachedMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<HelpMediaAndAttached> mediaAndAttacheds = mediaAndAttachedIPage.getRecords();
        page.setAll((int) mediaAndAttachedIPage.getTotal());
        return getFileMedia(mediaAndAttacheds);
    }

    @Override
    public void updateTableId(Long id, Long tableId) {
        HelpMediaAndAttached mediaAndAttached = new HelpMediaAndAttached();
        mediaAndAttached.setFkTableId(tableId);
        mediaAndAttached.setId(id);
        attachedMapper.updateById(mediaAndAttached);
    }

    @Override
    public void deleteMediaAndAttachedByTableId(Long tableId, String tableName) {
//        Example example = new Example(MediaAndAttached.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkTableId", tableId);
//        criteria.andEqualTo("fkTableName", tableName);
//        attachedMapper.deleteByExample(example);
        attachedMapper.delete(Wrappers.<HelpMediaAndAttached>lambdaQuery().eq(HelpMediaAndAttached::getFkTableId, tableId).eq(HelpMediaAndAttached::getFkTableName, tableName));
    }


    @Override
    public List<Map<String, Object>> findHelpMediaType() {
        return FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.HELP);
    }

    /**
     * @Override public List<Map<String, Object>> findContractMediaType() {
     * return FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.SALECONTRACT);
     * }
     **/

    @Override
    public void movingOrder(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        HelpMediaAndAttached ro = BeanCopyUtils.objClone(mediaAttachedVos.get(0), HelpMediaAndAttached::new);
        Integer oneorder = ro.getIndexKey();
        HelpMediaAndAttached rt = BeanCopyUtils.objClone(mediaAttachedVos.get(1), HelpMediaAndAttached::new);
        Integer twoorder = rt.getIndexKey();
        ro.setIndexKey(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setIndexKey(oneorder);
        utilService.updateUserInfoToEntity(rt);
        attachedMapper.updateById(ro);
        attachedMapper.updateById(rt);
    }


//    /**
//     * 获取媒体附件
//     *
//     * @param attachedVo
//     * @return
//     * @throws GetServiceException
//     */
//    private List<MediaAndAttached> getMediaAndAttacheds(MediaAndAttachedDto attachedVo) throws GetServiceException {
//        if (GeneralTool.isEmpty(attachedVo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
//        }
//        Example example = new Example(MediaAndAttached.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(attachedVo.getTypeKey())) {
//            criteria.andEqualTo("typeKey", attachedVo.getTypeKey());
//        }
//        criteria.andEqualTo("fkTableName", attachedVo.getFkTableName());
//        criteria.andEqualTo("fkTableId", attachedVo.getFkTableId());
//        example.orderBy("indexKey").desc();
//        return attachedMapper.selectByExample(example);
//    }
//    /**
//     * 获取媒体附件
//     *
//     * @param attachedVo
//     * @return
//     * @throws GetServiceException
//     */
//    private List<MediaAndAttached> getMediaAndAttacheds(MediaAndAttachedDto attachedVo) throws GetServiceException {
//        if (GeneralTool.isEmpty(attachedVo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
//        }
//        Example example = new Example(MediaAndAttached.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(attachedVo.getTypeKey())) {
//            criteria.andEqualTo("typeKey", attachedVo.getTypeKey());
//        }
//        criteria.andEqualTo("fkTableName", attachedVo.getFkTableName());
//        criteria.andEqualTo("fkTableId", attachedVo.getFkTableId());
//        example.orderBy("indexKey").desc();
//        return attachedMapper.selectByExample(example);
//    }

    private List<MediaAndAttachedVo> getFileMedia(List<HelpMediaAndAttached> mediaAndAttachedList) {
        if (GeneralTool.isEmpty(mediaAndAttachedList)) {
            return null;
        }
        //获取guid集合
        List<String> guidList = mediaAndAttachedList.stream().map(HelpMediaAndAttached::getFkFileGuid).collect(Collectors.toList());
        //转换成DTO
        List<MediaAndAttachedVo> mediaAndAttachedVos = mediaAndAttachedList.stream()
                .map(mediaAndAttached -> BeanCopyUtils.objClone(mediaAndAttached, MediaAndAttachedVo::new)).collect(Collectors.toList());
        //根据GUID服务调用查询
        List<FileDto> fileDtos = new ArrayList<>();
        Map<String, List<String>> guidListWithTypeMap = new HashMap<>();
        guidListWithTypeMap.put(LoggerModulesConsts.HELPCENTER, guidList);
        Result<List<FileDto>> result = fileCenterClient.findFileByGuid(guidListWithTypeMap);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            JSONArray jsonArray = JSONArray.fromObject(result.getData());
            fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
        }

        //返回结果不为空时
        List<MediaAndAttachedVo> collect = null;
        if (GeneralTool.isNotEmpty(fileDtos)) {
            //遍历查询GUID是否一致
            List<FileDto> finalFileDtos = fileDtos;
            collect = mediaAndAttachedVos.stream().map(mediaAndAttachedVo -> finalFileDtos
                    .stream()
                    .filter(fileDto -> fileDto.getFileGuid().equals(mediaAndAttachedVo.getFkFileGuid()))
                    .findFirst()
                    .map(fileDto -> {
                        mediaAndAttachedVo.setFilePath(fileDto.getFilePath());
                        mediaAndAttachedVo.setFileNameOrc(fileDto.getFileNameOrc());
                        mediaAndAttachedVo.setTypeValue(FileTypeEnum.getValue(mediaAndAttachedVo.getTypeKey()));
                        /*mediaAndAttachedVo.setFkTableName(null);*/
                        return mediaAndAttachedVo;
                    }).orElse(null)
            ).collect(Collectors.toList());
        }
        return collect;
    }
}
