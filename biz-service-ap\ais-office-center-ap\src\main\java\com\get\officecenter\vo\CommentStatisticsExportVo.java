package com.get.officecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CommentStatisticsExportVo {

    @ApiModelProperty(value = "接收人id")
    private Long staffId;

    @ApiModelProperty(value = "接收人名称")
    private String staffName;

    @ApiModelProperty(value = "评论列表")
    private List<CommentVo> comments;

    @ApiModelProperty(value = "待解决")
    private Integer pending;

    @ApiModelProperty(value = "待反馈")
    private Integer feedback;

    @ApiModelProperty(value = "总数")
    private Integer total;
}
