package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @author: Hardy
 * @create: 2023/2/6 16:23
 * @verison: 1.0
 * @description:
 */
@Data
public class StaffCommissionPolicyDto extends BaseVoEntity{
    /**
     * 公司Id
     */
    @NotNull(message = "公司Id", groups = {Add.class,Update.class})
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    /**
     * 学生项目角色key
     */
    @ApiModelProperty(value = "学生项目角色key")
    @Column(name = "fk_student_project_role_key")
    private String fkStudentProjectRoleKey;

    /**
     * 员工提成业务步骤key
     */
    @ApiModelProperty(value = "员工提成业务步骤key")
    @Column(name = "fk_staff_commission_step_key")
    private String fkStaffCommissionStepKey;

    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;

    /**
     * 专业等级Id
     */
    @ApiModelProperty(value = "专业等级Id")
    @Column(name = "fk_major_level_id")
    private Long fkMajorLevelId;

    /**
     * 定额金额
     */
    @ApiModelProperty(value = "定额金额")
    @Column(name = "fixed_amount")
    private BigDecimal fixedAmount;

    /**
     * 学费比率%
     */
    @ApiModelProperty(value = "学费比率%")
    @Column(name = "commission_rate")
    private BigDecimal commissionRate;

    /**
     * 优先级，数字越大越优先，优先匹配对应的提成金额
     */
    @ApiModelProperty(value = "优先级，数字越大越优先，优先匹配对应的提成金额")
    @Column(name = "priority")
    private Integer priority;


    @ApiModelProperty(value = "项目成员角色id")
    private Long fkStudentProjectRoleId;

    @ApiModelProperty(value = "结算步骤id")
    private Long fkStaffCommissionStepKeyId;

    @ApiModelProperty(value = "学生")
    private Long fkStudentId;

    private static final long serialVersionUID = 1L;

 
}
