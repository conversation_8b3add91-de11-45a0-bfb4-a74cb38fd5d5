package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2020/8/10
 * @TIME: 15:22
 * @Description:
 **/
@Data
public class ResumeSkillVo extends BaseVoEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 所属简历Id
     */
    @NotNull(message = "简历id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "所属简历Id", required = true)
    private Long fkResumeId;
    /**
     * 技能类型Id
     */
    @NotNull(message = "技能id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "技能类型Id", required = true)
    private Long fkSkillTypeId;
    /**
     * 掌握程度：一般/良好/熟练/精通
     */
    @ApiModelProperty(value = "掌握程度：一般/良好/熟练/精通")
    private String skillLevel;
}
