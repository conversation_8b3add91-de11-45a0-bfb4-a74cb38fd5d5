package com.get.salecenter.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.vo.CompanyTreeVo;
import com.get.salecenter.vo.ContactPersonVo;
import com.get.salecenter.vo.ContactPersonMobileSelectVo;
import com.get.salecenter.entity.SaleContactPerson;
import com.get.salecenter.dto.ContactPersonCompanyDto;
import com.get.salecenter.dto.ContactPersonDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/8/17
 * @TIME: 10:56
 * @Description: 联系人管理接口
 **/
public interface IContactPersonService extends IService<SaleContactPerson> {

    /**
     * 获取联系人
     *
     * @param contactPersonVo
     * @param page
     * @return
     * @
     */
    List<ContactPersonVo> getContactPersonDtos(ContactPersonDto contactPersonVo, Page page);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    ContactPersonVo finContactPersonById(Long id);

    /**
     * 新增
     *
     * @param contactPersonVo
     * @return
     * @
     */
    Long addContactPerson(ContactPersonDto contactPersonVo);

    /**
     * 修改
     *
     * @param contactPersonVo
     * @return
     * @
     */
    ContactPersonVo updateContactPerson(ContactPersonDto contactPersonVo);

    /**
     * @param id
     * @
     */
    void deleteContactPerson(Long id);

    /**
     * @return java.lang.Long
     * @Description: 新增代理联系人
     * @Param [contactPersonVo]
     * <AUTHOR>
     */
    Long addAgentContactPerson(ContactPersonDto contactPersonVo);

    /**
     * 新增代理联系人
     * 
     * @param contactPersonVo
     */
    void addAgentContactPerson(List<ContactPersonDto> contactPersonVo);

    /**
     * @return void
     * @Description: 安全配置
     * @Param [validList]
     * <AUTHOR>
     */
    void editContactPersonCompany(List<ContactPersonCompanyDto> validList);

    /**
     * @return java.util.List<com.get.salecenter.vo.CompanyTreeVo>
     * @Description: 联系人公司回显
     * @Param [id]
     * <AUTHOR>
     */
    List<CompanyTreeVo> getContactRelation(Long id);

    /**
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Description: 表名下拉
     * @Param []
     * <AUTHOR>
     */
    List<Map<String, Object>> findTargetType();

    /**
     * @ Description :内部调用
     * @ Param [fkTableName, fkTableId, fkTyepKey]
     * @ author LEO
     */
    List<SaleContactPerson> getContactPersonByFkTableId(String fkTableName, Long fkTableId);

    /**
     * 获取代理联系人Email
     *
     * @param ids
     * @return
     * @
     */
    Map<Long, String> getContactPersonEmail(Set<Long> ids);

    /**
     * 获取代理联系人Email,逗号拼接Email
     *
     * @param ids
     * @return
     * @
     */
    Map<Long, String> getContactPersonEmailByFkTableIdAndName(Set<Long> ids, String fkTableName);

    /**
     * 代理联系人邮箱下拉
     *
     * @param fkAgentId
     * @return
     */
    List<BaseSelectEntity> getContactPersonEmailSelect(Long fkAgentId);

    String getExistContactPersonPM(Long id, String email, String tel, String mobile, Long companyId);

    /**
     * 根据联系人ids获取 联系人emali Map
     *
     * @Date 14:58 2022/3/12
     * <AUTHOR>
     */
    Map<Long, String> getContactPersonEmailById(Set<Long> ids);

    /**
     * 联系人电邮重复提示
     *
     * @param email
     * @return
     */
    String getExistContactPersonEmailPM(String email);

    List<SaleContactPerson> getContactPersonByCondition(LambdaQueryWrapper<SaleContactPerson> lambdaQueryWrapper);

    List<ContactPersonVo> getContactPersonInfo(Set<Long> agentIds);

    List<BaseSelectEntity> getContactPersonMobileAreaCodeSelect(Long fkAgentId);

    List<ContactPersonMobileSelectVo> getContactPersonMobileSelect(Long fkAgentId);

    /**
     * 获取表关联已使用的新联系人类型
     *
     * @param fkTableId 表ID
     * @return 已使用的新联系人类型集合
     */
    Set<String> getUsedNewTypeKeysByTableId(Long fkTableId);

    /**
     * 获取表关联已使用的新联系人类型（排除指定联系人）
     *
     * @param fkTableId 表ID
     * @param excludeContactPersonId 要排除的联系人ID
     * @return 已使用的新联系人类型集合
     */
    Set<String> getUsedNewTypeKeysByTableId(Long fkTableId, Long excludeContactPersonId);
}
