package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2023/12/13
 * @TIME: 9:33
 * @Description:
 **/
@Data
public class EventPlanThemeOfflineFormVo extends BaseEntity implements Serializable {
    @ApiModelProperty(value = "活动计划主题Id")
    private Long fkEventPlanThemeId;

    @ApiModelProperty(value = "活动适用业务国家地区名称（表格展示名称）")
    private String areaCountryName;

    @ApiModelProperty(value = "活动目标对象国家Ids，多选用逗号隔开：1,2,3")
    private Long fkAreaCountryIds;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "是否激活：0否/1是，若否需要灰掉活动项目")
    private Boolean isActive;

    @ApiModelProperty(value = "线下活动类型项目子项列表")
    private List<EventPlanThemeOfflineItemFormVo> offlineItemFormDtoList;

}
