package com.get.platformconfigcenter.service;

import java.util.List;
import java.util.Map;

/**
 * 附件类型业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2021/5/20 15:33
 */
public interface AttachmentTypeService {
    /**
     * 附件类型列表数据
     *
     * @return
     * @Date 15:36 2021/5/20
     * <AUTHOR>
     */
   // List<StudentAttachmentVo> getAttachmentTypeList(StudentAttachmentDto studentAttachmentVo, Page page);

    /**
     * 新增附件类型
     *
     * @Date 16:13 2021/5/20
     * <AUTHOR>
     */
   // Long addAttachmentType(StudentAttachmentDto studentAttachmentVo);

    /**
     * 修改附件类型
     *
     * @Date 16:29 2021/5/20
     * <AUTHOR>
     */
   // StudentAttachmentVo updateAttachmentType(StudentAttachmentDto studentAttachmentVo);

    /**
     * 附件类型详情
     *
     * @Date 16:34 2021/5/20
     * <AUTHOR>
     */
   // StudentAttachmentVo findAttachmentTypeById(Long id);

    /**
     * 删除附件类型
     *
     * @Date 16:37 2021/5/20
     * <AUTHOR>
     */
    //void delete(Long id);

    /**
     * 上移下移
     *
     * @Date 17:04 2021/5/20
     * <AUTHOR>
     */
    //void movingOrder(List<StudentAttachmentDto> studentAttachmentVos);

    /**
     * 附件类型下拉框数据
     *
     * @Date 15:06 2021/5/26
     * <AUTHOR>
     */
   // List<StudentAttachmentVo> getAttachmentTypeSelect();

    /**
     * 附件文件类型下拉框数据
     *
     * @Date 17:08 2021/5/26
     * <AUTHOR>
     */
    List<Map<String, Object>> getFileExtensionSelect();


}
