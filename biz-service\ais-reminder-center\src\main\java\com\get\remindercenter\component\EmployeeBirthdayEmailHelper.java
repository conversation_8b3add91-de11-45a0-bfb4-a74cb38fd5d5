package com.get.remindercenter.component;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.dao.EmailSenderQueueMapper;
import com.get.remindercenter.dao.EmailTemplateMapper;
import com.get.remindercenter.dto.EmployeeBirthdayEmailDto;
import com.get.remindercenter.dto.StudyPlanSameSchoolCourseReminderDto;
import com.get.remindercenter.dto.SystemSendEmailDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.EmailTemplate;
import com.get.remindercenter.service.RemindTaskQueueService;
import com.get.remindercenter.utils.ReminderTemplateUtils;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component("employeeBirthdayEmailHelper")
@Slf4j
public class EmployeeBirthdayEmailHelper extends  EmailAbstractHelper{


    @Resource
    private EmailTemplateMapper emailTemplateMapper;

    @Resource
    private EmailSenderQueueMapper emailSenderQueueMapper;


    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private RemindTaskQueueService remindTaskQueueService;


    @Override
    public void sendMail(EmailSenderQueue emailSenderQueue) {
        try {
            //组装数据
            EmployeeBirthdayEmailDto employeeBirthdayEmailDto  = assembleEmailData(emailSenderQueue);

            if (GeneralTool.isNotEmpty(employeeBirthdayEmailDto.getStaffEmail())) {
                //设置邮件模板
                String template = setEmailTemplate(employeeBirthdayEmailDto);
                EmailSystemMQMessageDto emailSystemMQMessageDto = new EmailSystemMQMessageDto();
                emailSystemMQMessageDto.setEmailSenderQueueId(employeeBirthdayEmailDto.getId());
                emailSystemMQMessageDto.setTitle(employeeBirthdayEmailDto.getEmailTitle());
                emailSystemMQMessageDto.setContent(template);
                emailSystemMQMessageDto.setToEmail(employeeBirthdayEmailDto.getStaffEmail());
                remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
                emailSenderQueue.setEmailTo(employeeBirthdayEmailDto.getStaffEmail());
                LambdaUpdateWrapper<EmailSenderQueue> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(EmailSenderQueue::getId, emailSenderQueue.getId())  // 更新条件（按ID匹配）
                        .set(EmailSenderQueue::getEmailTo,employeeBirthdayEmailDto.getStaffEmail());  // 只更新 emailTo 字段
                emailSenderQueueMapper.update(null, updateWrapper);  // 传入 null，由 updateWrapper 控制更新
            }
        }catch (Exception e){
            log.error("employeeBirthdayEmailHelper error:{}", e);
            emailSenderQueue.setErrorMessage(e.getMessage());
            emailSenderQueue.setOperationCount(emailSenderQueue.getOperationCount() + 1);
            emailSenderQueue.setOperationStatus(-1);
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }
    }

    @Override
    public EmployeeBirthdayEmailDto assembleEmailData(EmailSenderQueue emailSenderQueue) {
        EmployeeBirthdayEmailDto reminderDto = new EmployeeBirthdayEmailDto();
        BeanUtils.copyProperties(emailSenderQueue, reminderDto);
        //获取接收人邮箱
        StaffVo staffVo = permissionCenterClient.getStaffById(emailSenderQueue.getFkTableId()).getData();
        //获取中英文配置
        Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
        String versionValue2 = versionConfigMap.get(staffVo.getFkCompanyId());
        if(GeneralTool.isEmpty(versionValue2)){
            versionValue2 = "zh";
        }
        Map<String,String> map = new HashMap<>();
        map.put("name", staffVo.getName());
        map.put("englishName", staffVo.getNameEn());
        String title = null;
        if (!versionValue2.equals("en")) {
            title = staffVo.getNameEn() + "生日快乐！";
        }else {
            title = staffVo.getName() + "Happy birthday！";
        }
        emailSenderQueue.setEmailTitle(title);
        reminderDto.setMap(map);
        reminderDto.setLanguageCode(versionValue2);
        reminderDto.setStaffEmail(staffVo.getEmail());
        //插入标题
        if (GeneralTool.isNotEmpty(reminderDto.getEmailTitle())) {
            emailSenderQueue.setEmailTitle(reminderDto.getEmailTitle());
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }

        return reminderDto;
    }

    private String setEmailTemplate(EmployeeBirthdayEmailDto reminderDto) {
        List<EmailTemplate> remindTemplates = new ArrayList<>();

        if (GeneralTool.isNotEmpty(reminderDto.getFkEmailTypeKey())) {
            remindTemplates = emailTemplateMapper.selectList(Wrappers.<EmailTemplate>lambdaQuery().eq(EmailTemplate::getEmailTypeKey, reminderDto.getFkEmailTypeKey()));
        }

        if (GeneralTool.isEmpty(remindTemplates)) {
            log.error("邮箱模板不存在，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        String emailTemplate =null;
        if (!reminderDto.getLanguageCode().equals("en")) {
            emailTemplate = remindTemplates.get(0).getEmailTemplate();
        }else {
            emailTemplate = remindTemplates.get(0).getEmailTemplateEn();
        }
        emailTemplate  = ReminderTemplateUtils.getReminderTemplate(reminderDto.getMap(), emailTemplate);
        if (GeneralTool.isEmpty(emailTemplate)) {
            log.error("邮箱模板内容为空，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        return emailTemplate;
    }


}
