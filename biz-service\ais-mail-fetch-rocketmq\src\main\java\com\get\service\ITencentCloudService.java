package com.get.service;
import com.get.entity.MFileMail;

import javax.servlet.http.HttpServletResponse;
import java.io.File;

public interface ITencentCloudService {

    /**
     * @describe 上传文件的方法
     * @methods uploadObject 方法名
     * @parameter fileUrl 上传文件地址
     * @parameter fileKey 文件对象名称
     * @parameter @return 对象列表
     */
    Boolean uploadObject(boolean isPub, String bucketName, File file, String fileKey);

    void downLoadObject(MFileMail mFileMail, HttpServletResponse response, Boolean isPub, Boolean isShare, File file, Boolean isParse);
    void downLoadTofile(MFileMail mFileMail, Boolean isPub, File file);

}
