package com.get.workflowcenter.listener;

import com.get.common.cache.CacheNames;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.cache.utils.CacheUtil;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.SpringUtil;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.workflowcenter.component.IWorkFlowHelper;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.HistoryService;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.TaskService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.history.HistoricTaskInstanceQuery;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.task.Comment;
import org.activiti.engine.task.Task;
import org.springframework.transaction.annotation.Transactional;

/**
 * 财务表单 调整申请节点监听
 */
@Slf4j
public class FinancialFormsChangeStatusListener implements ExecutionListener {
    private static final String PARAM = "sequenceFlowsStatus";
    private static final String cache_key = "task_count";



    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    @Override
    public void notify(DelegateExecution execution) {
        IFinanceCenterClient financeCenterClient = SpringUtil.getBean(IFinanceCenterClient.class);
        IPermissionCenterClient feignPermissionService = SpringUtil.getBean(IPermissionCenterClient.class);
        TaskService taskService = SpringUtil.getBean(TaskService.class);
        RepositoryService repositoryService = SpringUtil.getBean(RepositoryService.class);
        HistoryService historyService = SpringUtil.getBean(HistoryService.class);
        //通过流程定义对象查找出KEY_  就是表名
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(execution.getProcessDefinitionId()).singleResult();
        String tableName = processDefinition.getKey();
        String businessKey = execution.getProcessInstanceBusinessKey();
        IWorkFlowHelper workFlowHelper = SpringUtil.getBean(IWorkFlowHelper.class);
        StaffVo staffVo = workFlowHelper.getExecutionStaffDto(execution);
//        Long starterId = staffVo.getId();

        //获取流程名称
        String processInstanceId = execution.getProcessInstanceId();


        // 创建evpList用于存储申请人和第一次审批人的ID
        List<String> evpList = new ArrayList<>();

        // 步骤1：获取申请人ID（流程启动人）
        String starterId = staffVo.getId().toString();
        evpList.add(starterId);

        // 步骤2：获取第一次审批人的ID
        // 查询历史任务实例，按开始时间排序找到第一个审批任务
        HistoricTaskInstanceQuery historicTaskQuery = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .orderByHistoricTaskInstanceStartTime().asc();

        List<HistoricTaskInstance> historicTasks = historicTaskQuery.list();

        for (HistoricTaskInstance historicTask : historicTasks) {
            // 跳过非审批任务和申请人自己的任务
            if (historicTask.getAssignee() != null &&
                    !historicTask.getAssignee().equals(starterId) &&
                    historicTask.getName().contains("审批")) {

                String firstApproverId = historicTask.getAssignee();
                evpList.add(firstApproverId);
                break;
            }
        }

        // 如果历史任务中没有找到审批人，尝试从当前任务中查找
        if (evpList.size() == 1) {
            List<Task> currentTasks = taskService.createTaskQuery()
                    .processInstanceId(processInstanceId)
                    .list();

            for (Task task : currentTasks) {
                if (task.getAssignee() != null &&
                        !task.getAssignee().equals(String.valueOf(starterId)) &&
                        task.getName().contains("审批")) {

                    String approverId = task.getAssignee();
                    evpList.add(approverId);
                    break;
                }
            }
        }


//        LeaveApplicationForm leaveApplicationForm = new LeaveApplicationForm();
//        Result<LeaveApplicationForm> result = financeCenterClient.getLeaveApplicationForm(Long.valueOf(businessKey));
//        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
//            leaveApplicationForm = result.getData();
//        } else {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("form_not_found"));
//        }

        List<Long> staffIdsByResourceKey = feignPermissionService.getStaffIdsByResourceKey("officeLeaveApplicationForm.AllForms", true);

        //结束节点 start状态设置已结束
        if ("start".equals(execution.getEventName())) {
            Result<Boolean> changeStatusResult = financeCenterClient.changeStatus(1, tableName, Long.valueOf(businessKey));
            if (!changeStatusResult.isSuccess()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("form_status_update_fail"));
            }

        }
        //如果当前任务节点是调整申请，根据操作结果修改状态
        List<Task> taskList = taskService.createTaskQuery().processDefinitionKey(tableName).taskDefinitionKey(execution.getCurrentActivityId()).list();
        if (taskList != null && !taskList.isEmpty()) {
            if ("调整申请".equals(taskList.get(0).getName())) {
                if ("1".equals(execution.getVariable(PARAM))) {
                    //值是1 表示重新申请 状态改为2
                    Result<Boolean> changeStatusResult = financeCenterClient.changeStatus(2, tableName, Long.valueOf(businessKey));
                    if (!changeStatusResult.isSuccess()) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("form_status_update_fail"));
                    }
                } else if ("0".equals(execution.getVariable(PARAM))) {
                    //值是0 表示放弃申请 状态改为4
                    Result<Boolean> changeStatusResult = financeCenterClient.changeStatus(4, tableName, Long.valueOf(businessKey));
                    if (!changeStatusResult.isSuccess()) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("form_status_update_fail"));
                    }
                }
            }
        } else {

            // 1. 查找最近完成的审批任务（即拒绝流程的任务）
//            List<HistoricActivityInstance> list = historyService.createHistoricActivityInstanceQuery().processInstanceId(processInstanceId).orderByHistoricActivityInstanceStartTime().asc().list();
            List<HistoricActivityInstance> completedActivities =
                    historyService.createHistoricActivityInstanceQuery()
                            .processInstanceId(processInstanceId)
                            .activityType("userTask") // 只查询用户任务
                            .unfinished() // 只查询未已完成的任务
                            .orderByHistoricActivityInstanceEndTime().desc() // 按结束时间降序
                            .list();
            // 2. 获取驳回人和驳回信息
            String rejectUserId = "";
            String rejectUserName = "";
            String rejectComment = "";

            if (GeneralTool.isNotEmpty(completedActivities)) {
                // 获取最近完成的审批任务
                HistoricActivityInstance lastCompletedTask = completedActivities.get(0);
                rejectUserId = lastCompletedTask.getAssignee();

                // 获取驳回人姓名
                if (GeneralTool.isNotEmpty(rejectUserId)) {
                    Result<String> result = feignPermissionService.getStaffName(Long.valueOf(rejectUserId));
                    if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                        rejectUserName = result.getData();
                    }
                }

                // 获取审批意见/驳回原因
                List<Comment> taskComments = taskService.getTaskComments(lastCompletedTask.getTaskId());
                if (GeneralTool.isNotEmpty(taskComments)) {
                    for (Comment comment : taskComments) {
                        if (GeneralTool.isNotEmpty(comment.getFullMessage())) {
                            rejectComment = comment.getFullMessage();
                            break;
                        }
                    }
                }

                // 如果从评论中没找到，尝试从流程变量中获取
                if (GeneralTool.isEmpty(rejectComment)) {
                    Object commentObj = execution.getVariable("comment");
                    if (commentObj != null) {
                        rejectComment = commentObj.toString();
                    }
                }
            }
            //只要进入这里 说明是审批拒绝  修改状态为3
            Result<Boolean> changeStatusResult = financeCenterClient.changeStatus(3, tableName, Long.valueOf(businessKey));
            if (!changeStatusResult.isSuccess()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("form_status_update_fail"));
            }
            StringJoiner stringJoiner = new StringJoiner(",");
            stringJoiner.add(ProjectKeyEnum.WORKFLOW_CENTER.key).add(ProjectKeyEnum.WORKFLOW_CENTER_TO_DO.key);
            // 创建审批信息对象
            Map<String, String> approvalInfo = new HashMap<>();
            approvalInfo.put("currentApproverId", rejectUserId);
            approvalInfo.put("approver", rejectUserName);
            approvalInfo.put("approvalComments", rejectComment);
            //驳回发送邮件
            workFlowHelper.sendMessage(staffVo, evpList, processDefinition, "审批驳回", execution, stringJoiner.toString(),approvalInfo);
        }

        if (GeneralTool.isNotEmpty(staffIdsByResourceKey)) {
            for (Long aLong : staffIdsByResourceKey) {
                CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(aLong), cache_key);
            }
        }

    }



}