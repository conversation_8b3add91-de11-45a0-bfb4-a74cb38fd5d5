#!/bin/bash
source ~/.bash_profile
source /etc/profile

# 镜像名称，动态 app name
IMAGE_NAME=monitor-center
# java 文件路径
JAR_PATH=./monitor-center/target/monitor-center-0.0.1-SNAPSHOT.jar
# Dockerfile 路径
DOCKER_FILE_PATH=./docker/monitor-center/Dockerfile 




# 仓库用户名，写死
DOCKER_USERNAME=admin
# 仓库密码，写死
DOCKER_PASSWORD=nexus@root
# 仓库地址，写死
#DOCKER_REPOSITORY=http://106.52.34.56:5000
DOCKER_REPOSITORY=http://106.52.34.56:5000


#------------------------------------ 以下内容基本不用动 ------------------------------------ 



# 版本号，动态生成,yyyymmddHH
#VERSION_ID="${IMAGE_NAME}"-$(date +%Y%m%d%H)
VERSION_ID="${IMAGE_NAME}":"0.0.1-SNAPSHOT"
IMAGE_TAG="106.52.34.56:5000/""${VERSION_ID}"


cp "${JAR_PATH}" .
echo "文件复制完毕"

echo "开始执行docker image构建"

# 登陆
docker login --username="${DOCKER_USERNAME}" --password="${DOCKER_PASSWORD}" "${DOCKER_REPOSITORY}"
echo "登陆仓库成功:${DOCKER_REPOSITORY}"

# 删除旧容器
container_id=$(docker ps|grep "${IMAGE_NAME}"|grep "${VERSION_ID}")
if [ -n "${container_id}" ]; then
  echo "检测到旧容器:${container_id}"
	docker rm -f "${container_id}"
	echo "容器删除成功:${container_id}"
fi

# 删除旧镜像
old_image=$(docker images|grep "${IMAGE_NAME}"|grep "${VERSION_ID}")
if [[ -n $old_image ]]; then
   old_image_id=$(echo "${old_image}"|awk '{print $3}')
   echo "检测到旧镜像:${old_image_id}"
   docker rmi -f "${old_image_id}"
   echo "镜像删除成功:${old_image_id}"
fi

echo "开始执行Dockerfile构建"
#docker build -f "${DOCKER_FILE_PATH}" --tag "${DOCKER_REPOSITORY}":"${VERSION_ID}" .
docker build -f "${DOCKER_FILE_PATH}" --tag "${IMAGE_TAG}" .
echo "Dockerfile构建完毕 " "${VERSION_ID}" "${IMAGE_TAG}"


echo "开始推送至仓库"
docker push "${IMAGE_TAG}"
echo "推送仓库完毕"

