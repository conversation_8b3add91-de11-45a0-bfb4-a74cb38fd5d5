package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestContextUtil;
import com.get.financecenter.dao.CurrencyTypeMapper;
import com.get.financecenter.dao.InvoiceReceivablePlanMapper;
import com.get.financecenter.dao.PayablePlanSettlementInstallmentMapper;
import com.get.financecenter.dao.ReceiptFormInvoiceMapper;
import com.get.financecenter.dao.ReceiptFormItemMapper;
import com.get.financecenter.dao.ReceiptFormMapper;
import com.get.financecenter.dto.ReceiptFormItemDto;
import com.get.financecenter.dto.SettlementInstallmentBatchUpdateDto;
import com.get.financecenter.dto.SettlementInstallmentUpdateDto;
import com.get.financecenter.entity.PayablePlanSettlementInstallment;
import com.get.financecenter.service.HtiAgencyCommissionSettlementService;
import com.get.financecenter.vo.*;
import com.get.financecenter.vo.AlreadyReceiptVo;
import com.get.financecenter.entity.CurrencyType;
import com.get.financecenter.entity.InvoiceReceivablePlan;
import com.get.financecenter.entity.ReceiptForm;
import com.get.financecenter.entity.ReceiptFormInvoice;
import com.get.financecenter.entity.ReceiptFormItem;
import com.get.financecenter.service.AsyncExportService;
import com.get.financecenter.service.ICurrencyTypeService;
import com.get.financecenter.service.IExchangeRateService;
import com.get.financecenter.service.IInvoiceService;
import com.get.financecenter.service.IMediaAndAttachedService;
import com.get.financecenter.service.IPaymentFormItemService;
import com.get.financecenter.service.IReceiptFormInvoiceService;
import com.get.financecenter.service.IReceiptFormItemService;
import com.get.financecenter.service.IReceiptFormService;
import com.get.financecenter.dto.MediaAndAttachedDto;
import com.get.financecenter.dto.ReceiptReDto;
import com.get.financecenter.dto.query.ReceiptFormQueryDto;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.entity.ReceivablePlan;
import com.get.salecenter.vo.ReceivablePlanVo;
import com.get.salecenter.vo.SaleReceiptFormItemVo;
import com.get.salecenter.entity.PayablePlan;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.salecenter.dto.EventBillAccountNoticeDto;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/12/31
 * @TIME: 11:46
 * @Description:
 **/
@Slf4j
@Service
public class ReceiptFormItemServiceImpl extends BaseServiceImpl<ReceiptFormItemMapper, ReceiptFormItem> implements IReceiptFormItemService {
    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
    @Resource
    private ReceiptFormItemMapper receiptFormItemMapper;
    @Resource
    private ReceiptFormMapper receiptFormMapper;
    @Resource
    private ISaleCenterClient saleCenterClient;
    @Resource
    private UtilService utilService;
    @Resource
    @Lazy
    private IReceiptFormService receiptFormService;
    @Resource
    private IExchangeRateService exchangeRateService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private ICurrencyTypeService currencyTypeService;
    @Resource
    private CurrencyTypeMapper currencyTypeMapper;
    @Resource
    private InvoiceReceivablePlanMapper invoiceReceivablePlanMapper;
    @Resource
    private IInvoiceService invoiceService;
    @Resource
    private IReceiptFormInvoiceService receiptFormInvoiceService;
    @Resource
    private ReceiptFormInvoiceMapper receiptFormInvoiceMapper;
    @Resource
    private AsyncExportService asyncExportService;
    @Resource
    private HtiAgencyCommissionSettlementService htiAgencyCommissionSettlementService;
    @Resource
    private PayablePlanSettlementInstallmentMapper payablePlanSettlementInstallmentMapper;


    @Override
    public List<ReceiptFormItemVo> datas(ReceiptFormItemDto receiptFormItemDto, Page page) {
//        Example example = new Example(ReceiptFormItem.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(receiptFormItemVo.getFkReceiptFormId())) {
//            criteria.andEqualTo("fkReceiptFormId", receiptFormItemVo.getFkReceiptFormId());
//        }
//        if (GeneralTool.isNotEmpty(receiptFormItemVo.getFkCompanyId())) {
//            List<Long> formIds = getFormIds(receiptFormItemVo.getFkCompanyId());
//            criteria.andIn("fkReceivablePlanId", formIds);
//        }
//        if (GeneralTool.isNotEmpty(receiptFormItemVo.getFkTypeKey())) {
//            List<Long> receivablePlanId = feignSaleService.getReceivablePlanId(receiptFormItemVo.getFkTypeKey(), receiptFormItemVo.getFkTypeTargetId());
//            if (GeneralTool.isEmpty(receivablePlanId)) {
//                receivablePlanId = new ArrayList<>();
//                receivablePlanId.add(0L);
//            }
//            criteria.andIn("fkReceivablePlanId", receivablePlanId);
//        }
//        if (GeneralTool.isNotEmpty(receiptFormItemVo.getFkTypeTargetId())) {
//            List<Long> receivablePlanId = feignSaleService.getReceivablePlanId(receiptFormItemVo.getFkTypeKey(), receiptFormItemVo.getFkTypeTargetId());
//            if (GeneralTool.isEmpty(receivablePlanId)) {
//                receivablePlanId = new ArrayList<>();
//                receivablePlanId.add(0L);
//            }
//            criteria.andIn("fkReceivablePlanId", receivablePlanId);
//        }
//        if (GeneralTool.isNotEmpty(receiptFormItemVo.getBeginTime())) {
//            criteria.andGreaterThanOrEqualTo("gmtCreate", receiptFormItemVo.getBeginTime());
//        }
//        if (GeneralTool.isNotEmpty(receiptFormItemVo.getEndTime())) {
//            criteria.andLessThanOrEqualTo("gmtCreate", receiptFormItemVo.getEndTime());
//        }
//        if (GeneralTool.isNotEmpty(receiptFormItemVo.getSummary())) {
//            criteria.andLike("summary", "%" + receiptFormItemVo.getSummary() + "%");
//        }
//
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<ReceiptFormItem> receiptFormItems = receiptFormItemMapper.selectByExample(example);
//        page.restPage(receiptFormItems);
//
//        List<ReceiptFormItemDto> collect = receiptFormItems.stream().map(receiptFormItem ->
//                BeanCopyUtils.objClone(receiptFormItem, ReceiptFormItemDto.class)).collect(Collectors.toList());

        LambdaQueryWrapper<ReceiptFormItem> wrapper = new LambdaQueryWrapper<>();

        if (GeneralTool.isNotEmpty(receiptFormItemDto.getFkReceiptFormId())) {
            wrapper.eq(ReceiptFormItem::getFkReceiptFormId, receiptFormItemDto.getFkReceiptFormId());
        }
        if (GeneralTool.isNotEmpty(receiptFormItemDto.getFkCompanyId())) {
            List<Long> formIds = getFormIds(receiptFormItemDto.getFkCompanyId());
            if (GeneralTool.isNotEmpty(formIds)) {
                wrapper.in(ReceiptFormItem::getFkReceiptFormId, formIds);
            }
        }
        if (GeneralTool.isNotEmpty(receiptFormItemDto.getFkTypeKey())) {
            //111111
            Result<List<Long>> resultPlanId = saleCenterClient.getReceivablePlanId(receiptFormItemDto.getFkTypeKey(), receiptFormItemDto.getFkTypeTargetId());
            if (resultPlanId.isSuccess()) {
                List<Long> receivablePlanId = resultPlanId.getData();
                if (GeneralTool.isEmpty(receivablePlanId)) {
                    receivablePlanId = new ArrayList<>();
                    receivablePlanId.add(0L);
                }
                wrapper.in(ReceiptFormItem::getFkReceivablePlanId, receivablePlanId);
            }
        }
        if (GeneralTool.isNotEmpty(receiptFormItemDto.getFkTypeTargetId())) {
            //111111
            Result<List<Long>> resultReceivablePlanId = saleCenterClient.getReceivablePlanId(receiptFormItemDto.getFkTypeKey(), receiptFormItemDto.getFkTypeTargetId());
            if (resultReceivablePlanId.isSuccess()) {
                List<Long> receivablePlanId = resultReceivablePlanId.getData();
                if (GeneralTool.isEmpty(receivablePlanId)) {
                    receivablePlanId = new ArrayList<>();
                    receivablePlanId.add(0L);
                }
                wrapper.in(ReceiptFormItem::getFkReceivablePlanId, receivablePlanId);
            }

        }
        if (GeneralTool.isNotEmpty(receiptFormItemDto.getBeginTime())) {
            wrapper.ge(ReceiptFormItem::getGmtCreate, receiptFormItemDto.getBeginTime());
        }
        if (GeneralTool.isNotEmpty(receiptFormItemDto.getEndTime())) {
            wrapper.le(ReceiptFormItem::getGmtCreate, receiptFormItemDto.getEndTime());
        }
        if (GeneralTool.isNotEmpty(receiptFormItemDto.getSummary())) {
            wrapper.like(ReceiptFormItem::getSummary, receiptFormItemDto.getSummary());
        }
//        IPage<ReceiptFormItem> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        IPage<ReceiptFormItem> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        //获取收款单详情
        Long fkReceiptFormId = receiptFormItemDto.getFkReceiptFormId();
        ReceiptForm receiptForm = receiptFormMapper.selectById(fkReceiptFormId);
        if (Objects.isNull(receiptForm)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //设置目标对象类型
        receiptFormItemDto.setFkTypeKey(receiptForm.getFkTypeKey());
        List<ReceiptFormItem> pages = receiptFormItemMapper.getReceiptFormItemData(receiptFormItemDto, iPage);
        page.setAll((int) iPage.getTotal());
        List<ReceiptFormItem> providerTypes = pages;
//        page.setAll((int) pages.getTotal());
        List<ReceiptFormItemVo> collect = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(providerTypes)) {
            collect = BeanCopyUtils.copyListProperties(providerTypes, ReceiptFormItemVo::new);
            if (GeneralTool.isEmpty(collect)) {
                return null;
            }
            Set<Long> planIds = collect.stream().map(ReceiptFormItemVo::getFkReceivablePlanId).collect(Collectors.toSet());
            Map<Long, ReceivablePlanVo> offerItemMap = saleCenterClient.findOfferItemByReceivableIds(planIds).getData();
            Map<Long, String> companyMap = getCompanyMap();
            for (ReceiptFormItemVo receiptFormItemVo : collect) {
                setDatasName(receiptFormItemVo, companyMap, offerItemMap);
                //计算原币折算  原币手续费
                receiptFormItemVo.setAmountCurrencyConversion(receiptFormItemVo.getExchangeRateReceivable().multiply(receiptFormItemVo.getAmountReceipt()).setScale(2, BigDecimal.ROUND_HALF_UP));
                receiptFormItemVo.setAmountServiceFeeConversion(receiptFormItemVo.getExchangeRateReceivable().multiply(receiptFormItemVo.getServiceFee()).setScale(2, BigDecimal.ROUND_HALF_UP));
            }
        }
        return collect;
    }

    /**
     * 导出收款单子项列表
     *
     * @param response
     */
    @Override
    public void exportReceiptFormItemExcel(HttpServletResponse response, ReceiptFormQueryDto receiptFormVo) {
        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
        com.get.core.secure.UserInfo user = GetAuthInfo.getUser();
        String locale = SecureUtil.getLocale();
        asyncExportService.exportReceiptFormItemExcel(receiptFormVo,headerMap,user,locale);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addReceiptFormItem(List<ReceiptFormItemDto> receiptFormItemDtos) {
        if (GeneralTool.isEmpty(receiptFormItemDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
        Long formId = receiptFormItemDtos.get(0).getFkReceiptFormId();
        ReceiptForm receiptForm = receiptFormMapper.selectById(formId);
        //总手续费
        BigDecimal serviceFee = receiptForm.getServiceFee();
        if (GeneralTool.isEmpty(serviceFee)) {
            serviceFee = BigDecimal.ZERO;
        }
        //绑定的总手续费额
        BigDecimal bindingFee = receiptFormItemDtos.stream().map(r -> GeneralTool.isEmpty(r.getServiceFee()) ? BigDecimal.ZERO : r.getServiceFee()).reduce(BigDecimal.ZERO, BigDecimal::add);
        List<ReceiptFormItem> receiptFormItemList = receiptFormItemMapper.selectList(Wrappers.<ReceiptFormItem>query().lambda()
                .eq(ReceiptFormItem::getFkReceiptFormId, formId));

//        if (GeneralTool.isNotEmpty(receiptFormItemList)) {
//            for (ReceiptFormItem receiptFormItem : receiptFormItemList) {
//                if (GeneralTool.isEmpty(receiptFormItem.getServiceFee())) {
//                    receiptFormItem.setServiceFee(new BigDecimal(0));
//                }
//            }
//        }
        //已收手续费
        BigDecimal chargeReceived = receiptFormItemList.stream().map(r -> GeneralTool.isEmpty(r.getServiceFee()) ? BigDecimal.ZERO : r.getServiceFee()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //如果 绑定的总手续费额 > 总手续费-已收手续费
        if (bindingFee.compareTo(serviceFee.subtract(chargeReceived)) > 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("service_fee_insufficient_balance"));
        }

        //收款单金额
        BigDecimal totalAmount = receiptFormService.getAmountByFormId(formId);

        BigDecimal totalReceipt = new BigDecimal("0");
        //已收
        BigDecimal alreadyReceiptAmount = receiptFormItemMapper.getAmountByFormId(formId, null);
        totalReceipt = totalReceipt.add(alreadyReceiptAmount);
        for (ReceiptFormItemDto receiptFormItemDto : receiptFormItemDtos) {
            //总新增的收款
            totalReceipt = totalReceipt.add(receiptFormItemDto.getAmountReceipt());
//            //应收计划
//            Long fkReceivablePlanId = receiptFormItemVo.getFkReceivablePlanId();
//            List<ReceiptFormItem> receiptFormItems = receiptFormItemMapper.selectList(Wrappers.<ReceiptFormItem>query().lambda().eq(ReceiptFormItem::getFkReceivablePlanId, fkReceivablePlanId));
//            //应收计划应收金额 111111
//            Result<BigDecimal> resultPlanAmount = saleCenterClient.getReceivablePlanAmountById(fkReceivablePlanId);
//            BigDecimal payablePlanAmount = resultPlanAmount.getData();
//            //该计划总实收金额 = 本次该计划收款金额+以往该计划收款金额  收款金额= 收款金额（折合应收币种金额）+ 汇率调整
//            BigDecimal totalReceivable = receiptFormItemVo.getAmountReceivable().
//                    add(receiptFormItemVo.getAmountExchangeRate());
//            if (GeneralTool.isNotEmpty(receiptFormItems)) {
//                for (ReceiptFormItem receiptFormItem : receiptFormItems) {
//                    totalReceivable = totalReceivable.add(receiptFormItem.getAmountReceivable())
//                            .add(receiptFormItem.getAmountExchangeRate());
//                }
//            }
//            //该计划总共实收金额 不能大于该应收计划的应收金额
//            if (payablePlanAmount.compareTo(totalReceivable) < 0) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("totalReceipt_greater_receivablePlanAmount"));
//            }
        }
        if (totalAmount.compareTo(totalReceipt.setScale(2, BigDecimal.ROUND_HALF_UP)) < 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("totalReceipt_greater_formAmount"));
        }
        //IAE，自动做enrolled关联
        Boolean flag =false;
        Long fkCompanyId = receiptForm.getFkCompanyId();
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.AUTO_RELATION_RECEIPT_ENROLLED.key, 1).getData();
        String configValue1 = companyConfigMap.get(fkCompanyId);
        flag = configValue1.equals("1");
//        ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.AUTO_RELATION_RECEIPT_ENROLLED.key).getData();
//        JSONObject jsonObject = JSONObject.parseObject(configDto.getValue1());
//        if (fkCompanyId == 3){
//            String iae = jsonObject.getString("IAE");
//            if (StringUtils.isNotBlank(iae)) {
//                flag = Integer.parseInt(iae)==1;
//            }
//        }else{
//            String other = jsonObject.getString("OTHER");
//            if (StringUtils.isNotBlank(other)) {
//                flag = Integer.parseInt(other)==1;
//            }
//        }
        Set<Long> ids = receiptFormItemDtos.stream().map(ReceiptFormItemDto::getFkReceivablePlanId).collect(Collectors.toSet());
        if (flag){
            saleCenterClient.autoRelationReceipten(ids);
        }
        Set<Long> receiptFormItemIds = Sets.newHashSet();
        List<ReceiptFormItem> receiptFormItems = Lists.newArrayList();
        for (ReceiptFormItemDto receiptFormItemDto : receiptFormItemDtos) {
            receiptFormItemDto.setExchangeRateReceivable(receiptFormItemDto.getExchangeRate());
            ReceiptFormItem receiptFormItem = BeanCopyUtils.objClone(receiptFormItemDto, ReceiptFormItem::new);
            utilService.updateUserInfoToEntity(receiptFormItem);
            receiptFormItemMapper.insertSelective(receiptFormItem);
            receiptFormItemIds.add(receiptFormItem.getId());
            receiptFormItems.add(receiptFormItem);
            //可结算状态才会生成佣金
            if (1 == receiptForm.getSettlementStatus()) {
                //根据应收计划id获取对应的应付计划信息
                Result<PayablePlan> payablePlanResult = saleCenterClient.getPayablePlanByReceivablePlanId(receiptFormItemDto.getFkReceivablePlanId());
                if (!payablePlanResult.isSuccess()) {
                    throw new GetServiceException(payablePlanResult.getMessage());
                }

                PayablePlan payablePlan = payablePlanResult.getData();
                //该应收计划还没有对应的应付 不操作
                if (GeneralTool.isNotEmpty(payablePlan)) {
                    //应付计划为学校提供商类型或者业务提供商类型,不用生成给代理的佣金
                    if (!payablePlan.getFkTypeKey().equals(TableEnum.SALE_BUSINESS_PROVIDER.key) && !payablePlan.getFkTypeKey().equals(TableEnum.INSTITUTION_PROVIDER.key)) {
                        //HTI对冲金额
                        BigDecimal hedgeAmount = payablePlanSettlementInstallmentMapper.getHedgeAmount(payablePlan.getId());
//                        BigDecimal hedgeAmount = invoiceReceivablePlanMapper.getPrepaidAmount(receiptFormItemDto.getFkReceivablePlanId(), receiptFormItemDto.getFkReceiptFormId());

                        //根据新增/编辑的 收款单子单信息 更新对应的分期表数据
//                        SaleReceiptFormItemVo saleReceiptFormItemVo = BeanCopyUtils.objClone(receiptFormItem, SaleReceiptFormItemVo::new);
//                        saleReceiptFormItemVo.setFkCurrencyTypeNum(receiptForm.getFkCurrencyTypeNum());
//                        saleCenterClient.insertSettlementInstallmentByReceiptFormItem(saleReceiptFormItemVo);

                        SettlementInstallmentUpdateDto settlementInstallmentUpdateDto = new SettlementInstallmentUpdateDto();
                        settlementInstallmentUpdateDto.setFkCompanyId(payablePlan.getFkCompanyId());
                        settlementInstallmentUpdateDto.setHedgeAmount(hedgeAmount);
                        settlementInstallmentUpdateDto.setFkPayablePlanId(payablePlan.getId());
                        ReceivablePlanVo receivablePlanVo = saleCenterClient.getReceivablePlanById(payablePlan.getFkReceivablePlanId()).getData();
//                        ReceivablePlan receivablePlan = receivablePlanMapper.selectById(payablePlan.getFkReceivablePlanId());
                        settlementInstallmentUpdateDto.setFkReceivablePlanId(receivablePlanVo.getId());
                        settlementInstallmentUpdateDto.setReceivableAmount(receivablePlanVo.getReceivableAmount());
                        settlementInstallmentUpdateDto.setPayableAmount(payablePlan.getPayableAmount());
                        settlementInstallmentUpdateDto.setReceivableCurrencyTypeNum(receivablePlanVo.getFkCurrencyTypeNum());
                        settlementInstallmentUpdateDto.setPayableCurrencyTypeNum(payablePlan.getFkCurrencyTypeNum());
                        ReceiptFormItemVo receiptFormItemVo = BeanCopyUtils.objClone(receiptFormItem, ReceiptFormItemVo::new);
                        receiptFormItemVo.setFkCurrencyTypeNum(receiptForm.getFkCurrencyTypeNum());
                        htiAgencyCommissionSettlementService.insertSettlementInstallment(settlementInstallmentUpdateDto, receiptFormItemVo);
                    }
                }
            }

        }
        EventBillAccountNoticeDto eventBillAccountNoticeDto = new EventBillAccountNoticeDto();
        eventBillAccountNoticeDto.setHeaderMap(headerMap);
        eventBillAccountNoticeDto.setPlanIds(ids);
        eventBillAccountNoticeDto.setReceiptFormItemIds(receiptFormItemIds);
        eventBillAccountNoticeDto.setReceiptFormItems(receiptFormItems);
        //异步发送邮件 防止卡住业务
        Result<ResponseBo> result = saleCenterClient.sendEventBillAccountEmail(eventBillAccountNoticeDto);
        if (result.isSuccess()&&GeneralTool.isNotEmpty(result.getData())){
            ResponseBo responseBo = result.getData();
            if (!responseBo.getSuccess()){
                throw new GetServiceException(responseBo.getMessage());
            }
        }else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("news_emil_send_fail"));
        }
    }

    /**
     * 收款单子项
     *
     * @param receiptFormItemDto
     * @return
     */
    @Override
    public ReceiptFormItemVo updateReceiptFormItem(ReceiptFormItemDto receiptFormItemDto) {
        if (GeneralTool.isEmpty(receiptFormItemDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }

//        //检查收款单子项是否处于结算中或结算完成 true:没有处于结算中的，可以编辑 并删除批次表数据
//        Result<Boolean> flag = saleCenterClient.checkSettlementStatusByReceiptFormItemIds(Collections.singleton(receiptFormItemVo.getId()));
//        if (!flag.isSuccess() || !flag.getData()) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("sale_settlement_in_progress"));
//        }

//        Long formId = receiptFormItemVo.getFkReceiptFormId();
//        ReceiptForm receiptForm = receiptFormMapper.selectById(formId);
//        //手续费
//        BigDecimal serviceFee = receiptForm.getServiceFee();
//        if (GeneralTool.isEmpty(serviceFee)) {
//            serviceFee = BigDecimal.ZERO;
//        }

        //收款单金额
//        BigDecimal totalAmount = receiptFormService.getAmountByFormId(formId);

//        Example example1 = new Example(ReceiptFormItem.class);
//        example1.createCriteria().andEqualTo("fkReceiptFormId",formId).andNotEqualTo("id",receiptFormItemVo.getId());
//        List<ReceiptFormItem> receiptFormItemList = receiptFormItemMapper.selectByExample(example1);

        //该收款单已绑定的收款单子项 （排除自己）
//        List<ReceiptFormItem> receiptFormItemList = receiptFormItemMapper.selectList(Wrappers.<ReceiptFormItem>query().lambda()
//                .eq(ReceiptFormItem::getFkReceiptFormId, formId)
//                .ne(ReceiptFormItem::getId, receiptFormItemVo.getId()));
//
//        //已收手续费
//        BigDecimal chargeReceived = receiptFormItemList.stream().map(r -> GeneralTool.isEmpty(r.getServiceFee()) ? BigDecimal.ZERO : r.getServiceFee()).reduce(BigDecimal.ZERO, BigDecimal::add);
//        if (receiptFormItemVo.getServiceFee().compareTo(serviceFee.subtract(chargeReceived)) > 0) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("service_fee_insufficient_balance"));
//        }

//        //应收计划总金额上限判断
//        //所绑定的应收计划
//        Long fkReceivablePlanId = receiptFormItemVo.getFkReceivablePlanId();
//        //应收计划绑定的收款单子项
//        List<ReceiptFormItem> receiptFormItems = receiptFormItemMapper.selectList(Wrappers.<ReceiptFormItem>query().lambda()
//                .eq(ReceiptFormItem::getFkReceivablePlanId, fkReceivablePlanId).ne(ReceiptFormItem::getId, receiptFormItemVo.getId()));
//        //应收计划应收金额
//        BigDecimal payablePlanAmount = saleCenterClient.getReceivablePlanAmountById(fkReceivablePlanId).getData();
//        //该计划总实收金额 = 本次该计划收款金额+以往该计划收款金额  收款金额= 收款金额（折合应收币种金额）+ 汇率调整
//        BigDecimal totalReceivable = receiptFormItemVo.getAmountReceivable().
//                add(receiptFormItemVo.getAmountExchangeRate());
//        if (GeneralTool.isNotEmpty(receiptFormItems)) {
//            for (ReceiptFormItem receiptFormItem : receiptFormItems) {
//                totalReceivable = totalReceivable.add(receiptFormItem.getAmountReceivable())
//                        .add(receiptFormItem.getAmountExchangeRate());
//            }
//        }
//        //该计划总共实收金额 不能大于该应收计划的应收金额
//        if (payablePlanAmount.compareTo(totalReceivable) < 0) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("totalReceipt_greater_receivablePlanAmount"));
//        }


//        List<ReceiptFormItemVo> receiptFormItemVos = BeanCopyUtils.copyListProperties(receiptFormItemList, ReceiptFormItemVo::new);
//        receiptFormItemVos.add(receiptFormItemVo);

//        BigDecimal totalReceipt = new BigDecimal("0");
//        //已收
////        BigDecimal alreadyReceiptAmount = receiptFormItemMapper.getAmountByFormId(formId, receiptFormItemVo.getId());
////        totalReceipt = totalReceipt.add(alreadyReceiptAmount);
//        //该收款单已绑定的收款单子项 + 自己
//        for (ReceiptFormItemVo itemVo : receiptFormItemVos) {
//            //总新增的收款
//            totalReceipt = totalReceipt.add(itemVo.getAmountReceipt());
//        }
//
//        if (totalAmount.compareTo(totalReceipt) < 0) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("totalReceipt_greater_formAmount"));
//        }
        ReceiptFormItem receiptFormItem = receiptFormItemMapper.selectById(receiptFormItemDto.getId());
        BeanUtils.copyProperties(receiptFormItemDto, receiptFormItem);
        utilService.updateUserInfoToEntity(receiptFormItem);
        receiptFormItemMapper.updateById(receiptFormItem);

//        //根据应收计划id获取对应的应付计划信息
//        Result<PayablePlan> payablePlanResult = saleCenterClient.getPayablePlanByReceivablePlanId(fkReceivablePlanId);
//        if (!payablePlanResult.isSuccess()) {
//            throw new GetServiceException(payablePlanResult.getMessage());
//        }
//        PayablePlan payablePlan = payablePlanResult.getData();
//        if (GeneralTool.isNotEmpty(payablePlan)) {
//            //旧数据不进入结算
//            if (!"admin-1".equals(receiptFormItem.getGmtCreateUser())) {
//                if (payablePlan.getIsPayInAdvance()) {
//                    //有预付的情况：根据应收计划id判断收款状态 生成分期数据
//                    isPayInAdvanceInsertSettlementInstallment(payablePlan.getFkReceivablePlanId());
//                } else {
//                    //没有预付 正常生成 根据新增/编辑的 收款单子单信息 更新对应的分期表数据
//                    SaleReceiptFormItemVo saleReceiptFormItemDto = BeanCopyUtils.objClone(receiptFormItem, SaleReceiptFormItemVo::new);
//                    saleReceiptFormItemDto.setFkCurrencyTypeNum(receiptForm.getFkCurrencyTypeNum());
//                    Result<Boolean> result = saleCenterClient.insertSettlementInstallmentByReceiptFormItem(saleReceiptFormItemDto);
//                    if (!result.isSuccess()) {
//                        throw new GetServiceException(result.getMessage());
//                    }
//                }
//            }
//        }
        return findReceiptFormItemById(receiptFormItem.getId());
    }


//    /**
//     * 有预付的情况：根据应收计划id判断收款状态，收齐了才能生成分期表进行结算
//     *
//     * @Date 23:10 2022/4/21
//     * <AUTHOR>
//     */
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public boolean isPayInAdvanceInsertSettlementInstallment(Long receivablePlanId) {
//        ReceivablePlanVo receivablePlanVo = saleCenterClient.getReceivablePlanById(receivablePlanId).getData();
//        //如果已付完，不需要再付钱 不需要生成新的分期表数据  或 已生成过预付后第二次结算的分期数据了 也不需要再生成
//        Boolean checkFlag = saleCenterClient.checkPaymentStatusByReceivablePlanId(receivablePlanId).getData();
//        if (checkFlag) {
//            return true;
//        }
//        //有预付的情况 先判断是否已收齐
//        //应收金额
//        BigDecimal receivableAmount = receivablePlanVo.getReceivableAmount();
//        List<ReceiptFormItem> receiptFormItems = receiptFormItemMapper.selectList(Wrappers.<ReceiptFormItem>lambdaQuery().eq(ReceiptFormItem::getFkReceivablePlanId, receivablePlanId));
//        //已收金额
//        BigDecimal receivedAmount = new BigDecimal(0);
//        if (GeneralTool.isNotEmpty(receiptFormItems)) {
//            for (ReceiptFormItem receiptFormItem : receiptFormItems) {
//                receivedAmount = receivedAmount.add(receiptFormItem.getAmountReceivable());
//            }
//        }
//        //应收金额 = 实收金额  -->  已收齐  --> 才能生成分期表数据 不然不用生成 预付的未收完款，下次收完款了 就会进入生成
//        if (receivedAmount.compareTo(receivableAmount) >= 0) {
//            //预付专用：根据应收计划删除应付计划对应的 非预付的分期数据
//            Result<Boolean> booleanResult = saleCenterClient.deletePayInAdvanceInsertSettlementInstallment(receivablePlanId);
//            if (!booleanResult.isSuccess()) {
//                throw new GetServiceException(booleanResult.getMessage());
//            }
//
//            //只要预付了，除非已经收完钱 不然不会有非预付的分期数据，只要找出该应收计划绑定的收款单子单即可
//            List<ReceiptFormItemVo> receiptFormItemList = receiptFormItemMapper.getReceiptFormItemListFeignByPlanIds(Collections.singleton(receivablePlanId));
//            //对应的应收计划没有收款单的话，不生成分期数据
//            if (GeneralTool.isEmpty(receiptFormItemList)) {
//                return true;
//            }
//            //旧数据不进入结算
//            Set<String> gmtCreateUserSet = receiptFormItemList.stream().map(ReceiptFormItemVo::getGmtCreateUser).collect(Collectors.toSet());
//            for (String gmtCreateUser : gmtCreateUserSet) {
//                if ("admin-1".equals(gmtCreateUser)) {
//                    return true;
//                }
//            }
//            BigDecimal serviceFee = receiptFormItemList.stream().map(r -> GeneralTool.isEmpty(r.getServiceFee()) ? BigDecimal.ZERO : r.getServiceFee()).reduce(BigDecimal::add).get();
//            for (int i = 0; i < receiptFormItemList.size(); i++) {
//                ReceiptFormItemVo receiptFormItemVo = receiptFormItemList.get(i);
//                Result<Boolean> result = saleCenterClient.isPayInAdvanceInsertSettlementInstallment(receiptFormItemVo.getId(), receiptFormItemVo.getFkReceivablePlanId(),
//                        serviceFee, i == receiptFormItemList.size() - 1);
//                if (!result.isSuccess()) {
//                    throw new GetServiceException(result.getMessage());
//                }
//            }
//        }
//
//        return true;
//    }

    @Override
    public ReceiptFormItemVo findReceiptFormItemById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ReceiptFormItem receiptFormItem = receiptFormItemMapper.selectById(id);
        ReceiptFormItemVo receiptFormItemVo = BeanCopyUtils.objClone(receiptFormItem, ReceiptFormItemVo::new);
        Map<Long, String> companyMap = getCompanyMap();
        setName(receiptFormItemVo, companyMap);
        return receiptFormItemVo;
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //检查收款单子项是否处于结算中或结算完成 true:没有处于结算中的，可以编辑 并删除批次表数据
        LambdaQueryWrapper<PayablePlanSettlementInstallment> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<Integer> status = new ArrayList<>();
        status.add(ProjectExtraEnum.INSTALLMENT_PROCESSING.key);
        status.add(ProjectExtraEnum.INSTALLMENT_COMPLETE.key);
        lambdaQueryWrapper.eq(PayablePlanSettlementInstallment::getFkReceiptFormItemId, id)
                .in(PayablePlanSettlementInstallment::getStatus, status);
        List<PayablePlanSettlementInstallment> payablePlanSettlementInstallments = payablePlanSettlementInstallmentMapper.selectList(lambdaQueryWrapper);
        boolean flag = GeneralTool.isEmpty(payablePlanSettlementInstallments);
        if (flag) {
            payablePlanSettlementInstallmentMapper.delete(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery().eq(PayablePlanSettlementInstallment::getFkReceiptFormItemId, id)
                    .eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_UNTREATED.key));
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("sale_settlement_in_progress"));
        }
        receiptFormItemMapper.deleteById(id);
    }

    @Override
    public List<FMediaAndAttachedVo> addMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<FMediaAndAttachedVo> mediaAndAttachedDtos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.FINANCE_RECEIPT_FORM.key);
            mediaAndAttachedDtos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedDtos;
    }

    @Override
    public List<FMediaAndAttachedVo> getMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.FINANCE_RECEIPT_FORM.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);
    }

    @Override
    public List<ReceiptFormVo> getReceiptFormList(Long planId) {
        if (GeneralTool.isEmpty(planId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }

        List<ReceiptFormVo> receiptFormList = receiptFormService.getReceiptFormList(planId);
        String currency = saleCenterClient.getCurrencyNum(planId).getData();
        if (GeneralTool.isEmpty(receiptFormList) || GeneralTool.isNull(currency)) {
            return new ArrayList<>();
        }

        Set<Long> formIdset = new HashSet<>();
        if (GeneralTool.isNotEmpty(receiptFormList)) {
            formIdset = receiptFormList.stream().map(ReceiptFormVo::getId).collect(Collectors.toSet());
            if (GeneralTool.isEmpty(formIdset)) {
                formIdset.add(0L);
            }
        }

        List<ReceiptFormItem> formItems = receiptFormItemMapper.selectList(Wrappers.<ReceiptFormItem>lambdaQuery()
                .eq(ReceiptFormItem::getFkReceivablePlanId, planId).in(ReceiptFormItem::getFkReceiptFormId, formIdset));

        Map<Long, List<ReceiptFormItem>> receiptFormItemMap = null;
        if (GeneralTool.isNotEmpty(formItems)) {
            receiptFormItemMap = formItems.stream().collect(Collectors.groupingBy(ReceiptFormItem::getFkReceiptFormId));
        }

        //获取所有的发票编号map
        Map<Long, String> allNums = new HashMap<>();
        if (GeneralTool.isNotEmpty(receiptFormList)) {
            allNums = invoiceService.getAllNums();
        }
        //根据收款单ids获取发票map
        Map<Long, Set<Long>> invoiceByFkReceiptFormIds = new HashMap<>();
        Set<Long> receiptFormIds = receiptFormList.stream().map(ReceiptFormVo::getId).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(receiptFormIds)) {
            invoiceByFkReceiptFormIds = receiptFormInvoiceService.getInvoiceByFkReceiptFormIds(receiptFormIds);
        }

        Set<String> currencyTypeNums = receiptFormList.stream().map(ReceiptFormVo::getFkCurrencyTypeNum).filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
        Map<String, String> currencyNamesByNums = currencyTypeService.getCurrencyNamesByNums(currencyTypeNums);
//        ReceivablePlanVo data = saleCenterClient.getReceivablePlanById(planId).getData();
        for (ReceiptFormVo receiptFormVo : receiptFormList) {
            receiptFormVo.setFkCurrencyTypeName(currencyNamesByNums.get(receiptFormVo.getFkCurrencyTypeNum()));
            Set<Long> ids = invoiceByFkReceiptFormIds.get(receiptFormVo.getId());
            StringJoiner sj = new StringJoiner(",");
            if (GeneralTool.isNotEmpty(ids)) {
                for (Long id : ids) {
                    if (GeneralTool.isNotEmpty(allNums.get(id))) {
                        sj.add(allNums.get(id));
                    }
                }
                receiptFormVo.setFkInvoiceNum(sj.toString());
            }

            BigDecimal amountReceipt = BigDecimal.ZERO;
            BigDecimal resultAmountReceivable = BigDecimal.ZERO;
            BigDecimal resultAmountExchangeRate = BigDecimal.ZERO;

            if (GeneralTool.isNotEmpty(receiptFormItemMap) && GeneralTool.isNotEmpty(receiptFormItemMap.get(receiptFormVo.getId()))) {
                List<ReceiptFormItem> formItemList = receiptFormItemMap.get(receiptFormVo.getId());
                if (GeneralTool.isNotEmpty(formItemList)) {
                    for (ReceiptFormItem receiptFormItem : formItemList) {
                        BigDecimal itemAmountReceipt = GeneralTool.isNotEmpty(receiptFormItem.getAmountReceipt()) ? receiptFormItem.getAmountReceipt() : BigDecimal.ZERO;
                        amountReceipt = amountReceipt.add(itemAmountReceipt);

                        //收款金额（折合应收币种金额）
                        BigDecimal amountReceivable = GeneralTool.isNotEmpty(receiptFormItem.getAmountReceivable()) ? receiptFormItem.getAmountReceivable() : BigDecimal.ZERO;
                        BigDecimal amountExchangeRate = GeneralTool.isNotEmpty(receiptFormItem.getAmountExchangeRate()) ? receiptFormItem.getAmountExchangeRate() : BigDecimal.ZERO;
                        resultAmountReceivable = resultAmountReceivable.add(amountReceivable);
                        resultAmountExchangeRate = resultAmountExchangeRate.add(amountExchangeRate);

                    }
                    receiptFormVo.setAmountReceipt(amountReceipt);
                    receiptFormVo.setAmountExchangeRate(resultAmountExchangeRate);
                }
            }


        }
        return receiptFormList;
    }

    /**
     * feign根据应收计划ids获取所绑定的收款单子项
     *
     * @Date 16:49 2021/11/19
     * <AUTHOR>
     */
    @Override
    public List<ReceiptFormVo> getReceiptFormListFeignByPlanIds(Set<Long> planIds) {
        if (GeneralTool.isEmpty(planIds)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<ReceiptFormVo> receiptFormList = receiptFormService.getReceiptFormListFeignByPlanIds(planIds);
        Set<String> currencyTypeNums = receiptFormList.stream().map(ReceiptFormVo::getFkCurrencyTypeNum).filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
        Map<String, String> currencyNamesByNums = currencyTypeService.getCurrencyNamesByNums(currencyTypeNums);
        for (ReceiptFormVo receiptFormVo : receiptFormList) {
            receiptFormVo.setFkCurrencyTypeName(currencyNamesByNums.get(receiptFormVo.getFkCurrencyTypeNum()));
        }
        return receiptFormList;
    }

    /**
     * feign 查找对应的应收计划是否绑定 未结算过的收款单子单
     *
     * @Date 16:49 2021/11/19
     * <AUTHOR>
     */
    @Override
    public List<ReceiptFormItemVo> getSettlementReceiptFormItemListByPlanId(Long receivablePlanId) {
        if (GeneralTool.isEmpty(receivablePlanId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return receiptFormItemMapper.getSettlementReceiptFormItemListByPlanId(receivablePlanId);
    }

    @Override
    public ReceiptFormItemVo getReceiptFormItem(Long receiptPlanId, Long formId) {
        //111111
        Result<ReceivablePlanVo> responseBo = saleCenterClient.getReceivablePlanById(receiptPlanId);
        if (GeneralTool.isEmpty(responseBo.getData())) {
            return null;
        }
        ReceivablePlanVo receivablePlanVo = responseBo.getData();


        //应收计划（财务）
        ReceiptFormItemVo receiptFormItemVo = new ReceiptFormItemVo();
        receiptFormItemVo.setFkReceivablePlanId(receivablePlanVo.getId());
        //计划类型
        receiptFormItemVo.setFkTypeName(receivablePlanVo.getFkTypeName());
        //目标对象
        receiptFormItemVo.setTargetNames(receivablePlanVo.getTargetNames());
        //应收币种
        String receivablePlanCurrency = receivablePlanVo.getFkCurrencyTypeNum();
        receiptFormItemVo.setReceivablePlanCurrency(currencyTypeService.getCurrencyNameByNum(receivablePlanCurrency));
        //总应收金额
        BigDecimal receivableAmount = receivablePlanVo.getReceivableAmount();
        receiptFormItemVo.setReceivablePlanAmount(receivableAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
        //实收币种
        String receiptFormCurrency = receiptFormService.getCurrencyByFormId(formId);
        receiptFormItemVo.setReceiptFormCurrency(currencyTypeService.getCurrencyNameByNum(receiptFormCurrency));
        //折合应收汇率（前端显示汇率）
        BigDecimal showExchangeRate = getRate(receiptFormCurrency, receivablePlanCurrency);
        receiptFormItemVo.setExchangeRateReceivable(showExchangeRate);
        //折合应收汇率（计算用）
//        BigDecimal exchangeRate = new BigDecimal(1).divide(showExchangeRate).setScale(2, BigDecimal.ROUND_HALF_UP);

        //实收金额
        //需要计算还未完成收款的金额
        Long planId = receivablePlanVo.getId();
        List<AlreadyReceiptVo> alreadyReceipt = receiptFormItemMapper.getAlreadyReceiptByPlanId(planId);
        Set<String> currencyNums = alreadyReceipt.stream().map(AlreadyReceiptVo::getAlreadyReceiptCurrency).filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
        Map<String, String> currencyNamesByNums = currencyTypeService.getCurrencyNamesByNums(currencyNums);
        receiptFormItemVo.setAlreadyReceiptDtos(alreadyReceipt);

        BigDecimal totalBeforeReceipt = new BigDecimal("0");
        if (GeneralTool.isNotEmpty(alreadyReceipt)) {
            for (AlreadyReceiptVo alreadyReceiptVo : alreadyReceipt) {
//                alreadyReceiptDto.setAlreadyReceiptCurrencyName(currencyNamesByNums.get(alreadyReceiptDto.getAlreadyReceiptCurrency()));
//                alreadyReceiptDto.setTargetNames(receivablePlanVo.getTargetNames());
//
//                BigDecimal beforeReceiptAmount = alreadyReceiptDto.getAlreadyReceiptAmount();
//                String beforeFormCurrency = alreadyReceiptDto.getAlreadyReceiptCurrency();
//                //汇率
//                BigDecimal formRate = getRate(beforeFormCurrency, receiptFormCurrency);
//                //已付金额 实收折合金额
//                BigDecimal beforeReceiptAmountExchange = beforeReceiptAmount.multiply(formRate);
//                //汇率调整
//                BigDecimal alreadyExchangeRate = alreadyReceiptDto.getAlreadyExchangeRate();
//
//                totalBeforeReceipt = totalBeforeReceipt.add(beforeReceiptAmountExchange).add(alreadyExchangeRate);


                //收款金额（折合应收币种金额）
                BigDecimal beforeReceiptAmountExchange = alreadyReceiptVo.getAmountReceivable();
                //汇率调整
                BigDecimal alreadyExchangeRate = alreadyReceiptVo.getAlreadyExchangeRate();

                totalBeforeReceipt = totalBeforeReceipt.add(beforeReceiptAmountExchange).add(alreadyExchangeRate);
            }
        }
        //总应收金额 * 折合应收汇率（计算用）  - 之前已收的金额
        BigDecimal nowAmountReceipt = receivableAmount.divide(showExchangeRate, 2, BigDecimal.ROUND_HALF_UP).subtract(totalBeforeReceipt).setScale(2, BigDecimal.ROUND_HALF_UP);
        if ((receivableAmount.divide(showExchangeRate, 2, BigDecimal.ROUND_HALF_UP)).compareTo(totalBeforeReceipt) <= 0) {
            //已收齐
            throw new GetServiceException(LocaleMessageUtils.getMessage("plan_collect_amount"));
        }
        receiptFormItemVo.setAmountReceipt(nowAmountReceipt.setScale(2, BigDecimal.ROUND_HALF_UP));
        //折合金额 收款金额
        receiptFormItemVo.setAmountReceivable(nowAmountReceipt.multiply(showExchangeRate).setScale(2, RoundingMode.HALF_UP));
        //汇率调整
        receiptFormItemVo.setAmountExchangeRate(BigDecimal.valueOf(0.0000).setScale(4, BigDecimal.ROUND_HALF_UP));
        //港币汇率
        BigDecimal hkdRate = getRate(receiptFormCurrency, "HKD");
        receiptFormItemVo.setExchangeRateHkd(hkdRate);
        //折合港币
        BigDecimal amountHkd = hkdRate.multiply(nowAmountReceipt);
        receiptFormItemVo.setAmountHkd(amountHkd.setScale(2, BigDecimal.ROUND_HALF_UP));
        //人名币汇率
        BigDecimal rmbRate = getRate(receiptFormCurrency, "CNY");
        receiptFormItemVo.setExchangeRateRmb(rmbRate);
        //折合人名币
        BigDecimal amountRmb = rmbRate.multiply(nowAmountReceipt);
        receiptFormItemVo.setAmountRmb(amountRmb.setScale(2, BigDecimal.ROUND_HALF_UP));

        return receiptFormItemVo;
    }

    /**
     * Author Cream
     * Description : //获取应收计划列表数据分页信息
     * Date 2023/9/8 14:25
     * Params:
     * Return
     */
    @Override
    public ResponseBo getReceiptFormItemListPagination(Long formId, String[] times, Page page) {
        ReceiptForm receiptForm = receiptFormMapper.selectById(formId);
        if (GeneralTool.isEmpty(receiptForm)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        Long receiptFormId = receiptForm.getId();
        Long targetId = receiptForm.getFkTypeTargetId();
        String tableName = null;
        String typeKey = null;
        if (ProjectKeyEnum.INSTITUTION_PROVIDER.key.equals(receiptForm.getFkTypeKey())) {
            tableName = TableEnum.SALE_STUDENT_OFFER_ITEM.key;
            typeKey = TableEnum.INSTITUTION_PROVIDER.key;

        } else if (ProjectKeyEnum.BUSINESS_CHANNEL_INS.key.equals(receiptForm.getFkTypeKey())) {
            tableName = TableEnum.SALE_STUDENT_INSURANCE.key;
            typeKey = TableEnum.BUSINESS_CHANNEL_INS.key;

        } else if (ProjectKeyEnum.BUSINESS_CHANNEL_ACC.key.equals(receiptForm.getFkTypeKey())) {
            tableName = TableEnum.SALE_STUDENT_ACCOMMODATION.key;
            typeKey = TableEnum.BUSINESS_CHANNEL_ACC.key;

        } else if (ProjectKeyEnum.INSTITUTION_PROVIDER_CHANNEL.key.equals(receiptForm.getFkTypeKey())) {
            tableName = TableEnum.SALE_STUDENT_OFFER_ITEM.key;
            typeKey = TableEnum.INSTITUTION_PROVIDER_CHANNEL.key;
        }
        Result<Page> result = saleCenterClient.getReceiptFormReceivablePlanPaginationInfo(tableName, targetId, receiptFormId, typeKey, page);
        long var1 = System.currentTimeMillis();
        if (result.isSuccess()) {
            return new ResponseBo<>(BeanCopyUtils.objClone(result.getData(), Page::new), System.currentTimeMillis() - var1);
        }
        return null;
    }

    /**
     * 获取应收计划数据
     *
     *
     * @param receiptReDto
     * @return
     */
    @Override
    public List<ReceiptFormItemVo> getReceiptFormItemList(ReceiptReDto receiptReDto, String[] times, Integer pageNumber, Integer pageSize) {
        long startTime = System.currentTimeMillis();
        Long formId = receiptReDto.getFormId();
        log.info("==========================================getReceiptFormItemList===========");
        ReceiptForm receiptForm = receiptFormMapper.selectById(formId);
        if (GeneralTool.isEmpty(receiptForm)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }


        List<ReceiptFormInvoice> receiptFormInvoices = receiptFormInvoiceMapper.selectList(Wrappers.<ReceiptFormInvoice>lambdaQuery()
                .eq(ReceiptFormInvoice::getFkReceiptFormId, formId));

        Set<Long> invoiceIdset = receiptFormInvoices.stream().map(ReceiptFormInvoice::getFkInvoiceId).collect(Collectors.toSet());
        invoiceIdset.removeIf(Objects::isNull);


        List<ReceiptFormItemVo> receiptFormItemVos = new ArrayList<>();
        List<BaseSelectEntity> baseSelectEntities = null;
        if (ProjectKeyEnum.INSTITUTION_PROVIDER.key.equals(receiptForm.getFkTypeKey())) {
            baseSelectEntities = saleCenterClient.getReceivablePlanSelectByProvider(receiptForm.getFkTypeTargetId(), receiptForm.getId()).getData();
//            baseSelectEntities = getReceivablePlans(TableEnum.SALE_STUDENT_OFFER_ITEM.key, TableEnum.INSTITUTION_PROVIDER.key, receiptForm, pageNumber, pageSize);
        } else if (ProjectKeyEnum.BUSINESS_CHANNEL_INS.key.equals(receiptForm.getFkTypeKey())) {
            baseSelectEntities = getReceivablePlans(TableEnum.SALE_STUDENT_INSURANCE.key, ProjectKeyEnum.BUSINESS_CHANNEL_INS.key, receiptForm, pageNumber, pageSize);
        } else if (ProjectKeyEnum.BUSINESS_CHANNEL_ACC.key.equals(receiptForm.getFkTypeKey())) {
            baseSelectEntities = getReceivablePlans(TableEnum.SALE_STUDENT_ACCOMMODATION.key, ProjectKeyEnum.BUSINESS_CHANNEL_ACC.key, receiptForm, pageNumber, pageSize);
        } else if (ProjectKeyEnum.INSTITUTION_PROVIDER_CHANNEL.key.equals(receiptForm.getFkTypeKey())) {
            baseSelectEntities = getReceivablePlans(TableEnum.SALE_STUDENT_OFFER_ITEM.key, ProjectKeyEnum.INSTITUTION_PROVIDER_CHANNEL.key, receiptForm, pageNumber, pageSize);
        }else if (ProjectKeyEnum.BUSINESS_PROVIDER.key.equals(receiptForm.getFkTypeKey())){
            baseSelectEntities = getReceivablePlans(TableEnum.SALE_STUDENT_ACCOMMODATION.key, ProjectKeyEnum.BUSINESS_PROVIDER.key, receiptForm, pageNumber, pageSize);
        }else if (ProjectKeyEnum.M_STUDENT.key.equals(receiptForm.getFkTypeKey())){
            baseSelectEntities = getReceivablePlans(TableEnum.SALE_STUDENT_SERVICE_FEE.key, TableEnum.SALE_STUDENT_SERVICE_FEE.key, receiptForm, pageNumber, pageSize);
        } if (ProjectKeyEnum.BUSINESS_PROVIDER_INS.key.equals(receiptForm.getFkTypeKey())) {
            baseSelectEntities = getReceivablePlans(null, ProjectKeyEnum.BUSINESS_PROVIDER_INS.key, receiptForm, pageNumber, pageSize);
        }
        if (baseSelectEntities == null) {
            return null;
        }
        Set<Long> planIds = baseSelectEntities.stream().map(BaseSelectEntity::getId).collect(Collectors.toSet());
        Map<Long, String> nameMap = new HashMap<>(8);
        if (GeneralTool.isNotEmpty(baseSelectEntities)) {
            nameMap = baseSelectEntities.stream().collect(Collectors.toMap(BaseSelectEntity::getId, BaseSelectEntity::getName));
        }
        //根据传入的应收id 和 排序条件获取应收并排序
        List<ReceivablePlanVo> receivablePlansDetail = saleCenterClient.getReceivablePlansBySort(planIds, receiptReDto.getInvoiceAmountSort(), receiptReDto.getReceiptAmountSort(), receiptReDto.getStudentName(),formId);
        if (GeneralTool.isEmpty(receivablePlansDetail)) {
            return null;
        }
        long endTime1 = System.currentTimeMillis();

        List<AlreadyReceiptVo> alreadyReceipt = receiptFormItemMapper.getAlreadyReceiptByPlanIds(planIds);
        Map<Long, List<AlreadyReceiptVo>> alreadyReceiptDtoMap = alreadyReceipt.stream().collect(Collectors.groupingBy(AlreadyReceiptVo::getFkReceivablePlanId));

        //获取所有币种名称map
        List<CurrencyType> currencyTypes = currencyTypeMapper.selectList(Wrappers.<CurrencyType>query().lambda());
        Set<String> currencyTypeNumSet = currencyTypes.stream().map(CurrencyType::getNum).collect(Collectors.toSet());
        Map<String, String> currencyNamesByNums = currencyTypeService.getCurrencyNamesByNums(currencyTypeNumSet);

        // 查询发票计划关系表返回uploadService
        List<InvoiceVo> invoiceVos = invoiceReceivablePlanMapper.getInvoicesByPlanIds(planIds);
        Map<Long, List<InvoiceVo>> invoiceListMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(invoiceVos)) {
            invoiceListMap = invoiceVos.stream().collect(Collectors.groupingBy(InvoiceVo::getFkReceivablePlanId));
        }

        Map<Long, List<InvoiceReceivablePlan>> invoicePlanMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(invoiceIdset) && GeneralTool.isNotEmpty(planIds)) {
            List<InvoiceReceivablePlan> invoiceReceivablePlans = invoiceReceivablePlanMapper.selectList(Wrappers.<InvoiceReceivablePlan>lambdaQuery()
                    .in(InvoiceReceivablePlan::getFkInvoiceId, invoiceIdset).in(InvoiceReceivablePlan::getFkReceivablePlanId, planIds));

            if (GeneralTool.isNotEmpty(invoiceReceivablePlans)) {
                invoicePlanMap = invoiceReceivablePlans.stream().collect(Collectors.groupingBy(InvoiceReceivablePlan::getFkReceivablePlanId));
            }
        }

        long endTime2 = System.currentTimeMillis();
        String receiptFormCurrency = null;
        BigDecimal hkdRate = BigDecimal.ZERO;
        BigDecimal rmbRate = BigDecimal.ZERO;
        if (GeneralTool.isNotEmpty(planIds)) {
            receiptFormCurrency = receiptFormService.getCurrencyByFormId(formId);
            hkdRate = getRate(receiptFormCurrency, "HKD");
            rmbRate = getRate(receiptFormCurrency, "CNY");
        }
        Long planId;
        for (ReceivablePlanVo receivablePlanVo : receivablePlansDetail) {
            planId = receivablePlanVo.getId();
            List<InvoiceVo> invoiceList = new ArrayList<>();
            InvoiceVo invoiceVo = null;
            if (GeneralTool.isNotEmpty(invoiceListMap)) {
                invoiceList = invoiceListMap.get(planId);
            }
            if (GeneralTool.isNotEmpty(invoiceList)) {
                invoiceVo = invoiceList.get(0);
            }
            //应收计划（财务）
            ReceiptFormItemVo receiptFormItemVo = new ReceiptFormItemVo();
            receiptFormItemVo.setFkReceivablePlanId(receivablePlanVo.getId());
            receiptFormItemVo.setFkCompanyId(receivablePlanVo.getFkCompanyId());
            //计划类型
            if (GeneralTool.isNotEmpty(receivablePlanVo.getFkTypeKey())) {
                if (ProjectKeyEnum.INSTITUTION_PROVIDER.key.equals(receivablePlanVo.getFkTypeKey())) {
                    receiptFormItemVo.setFkTypeName(TableEnum.INSTITUTION_PROVIDER.value);
                } else if (ProjectKeyEnum.M_STUDENT_INSURANCE.key.equals(receivablePlanVo.getFkTypeKey())) {
                    receiptFormItemVo.setFkTypeName(TableEnum.SALE_STUDENT_INSURANCE.value);
                } else if (ProjectKeyEnum.M_STUDENT_ACCOMMODATION.key.equals(receivablePlanVo.getFkTypeKey())) {
                    receiptFormItemVo.setFkTypeName(TableEnum.SALE_STUDENT_ACCOMMODATION.value);
                } else if (ProjectKeyEnum.SALE_STUDENT_OFFER_ITEM.key.equals(receivablePlanVo.getFkTypeKey())) {
                    receiptFormItemVo.setFkTypeName(TableEnum.SALE_STUDENT_OFFER_ITEM.value);
                } else if (ProjectKeyEnum.INSTITUTION_PROVIDER_CHANNEL.key.equals(receivablePlanVo.getFkTypeKey())) {
                    receiptFormItemVo.setFkTypeName(TableEnum.INSTITUTION_PROVIDER_CHANNEL.value);
                }else if (ProjectKeyEnum.BUSINESS_PROVIDER.key.equals(receivablePlanVo.getFkTypeKey())){
                    receiptFormItemVo.setFkTypeName(ProjectKeyEnum.BUSINESS_PROVIDER.value);
                }else if (ProjectKeyEnum.BUSINESS_CHANNEL_INS.key.equals(receivablePlanVo.getFkTypeKey())){
                    receiptFormItemVo.setFkTypeName(TableEnum.BUSINESS_CHANNEL_INS.value);
                }else if (ProjectKeyEnum.BUSINESS_CHANNEL_ACC.key.equals(receivablePlanVo.getFkTypeKey())){
                    receiptFormItemVo.setFkTypeName(TableEnum.BUSINESS_CHANNEL_ACC.value);
                }else if(TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(receivablePlanVo.getFkTypeKey())){
                    receiptFormItemVo.setFkTypeName(TableEnum.SALE_STUDENT_SERVICE_FEE.value);
                }else if(TableEnum.INSURANCE_ORDER.key.equals(receivablePlanVo.getFkTypeKey())){
                    receiptFormItemVo.setFkTypeName(TableEnum.INSURANCE_ORDER.value);
                }
            }
            //目标对象
            receiptFormItemVo.setTargetNames(nameMap.get(planId));
            //应收币种
            String receivablePlanCurrency = receivablePlanVo.getFkCurrencyTypeNum();
            receiptFormItemVo.setReceivablePlanCurrency(currencyNamesByNums.get(receivablePlanCurrency));
            //原币种
            if (null != invoiceVo) {
                receiptFormItemVo.setFkCurrencyTypeNumOrc(currencyNamesByNums.get(invoiceVo.getFkCurrencyTypeNumOrc()));
            } else {
                receiptFormItemVo.setFkCurrencyTypeNumOrc(currencyNamesByNums.get(receivablePlanCurrency));
            }
            //总应收金额
            BigDecimal receivableAmount = receivablePlanVo.getReceivableAmount();
            receiptFormItemVo.setReceivablePlanAmount(receivableAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
            //实收币种
            receiptFormItemVo.setReceiptFormCurrency(currencyNamesByNums.get(receiptFormCurrency));
            //折合应收汇率（前端显示汇率）
            if ("RMB".equals(receiptFormCurrency)) {
                receiptFormCurrency = "CNY";
            }
            if ("RMB".equals(receivablePlanCurrency)) {
                receivablePlanCurrency = "CNY";
            }
//            BigDecimal showExchangeRate = getRate(receivablePlanCurrency,receiptFormCurrency);
            BigDecimal showExchangeRate = receiptForm.getExchangeRate();
            receiptFormItemVo.setExchangeRateReceivable(showExchangeRate);
            //折合应收汇率（计算用）

            //实收金额
            //需要计算还未完成收款的金额
            List<AlreadyReceiptVo> alreadyReceiptVos = alreadyReceiptDtoMap.get(planId);
            receiptFormItemVo.setAlreadyReceiptDtos(alreadyReceiptVos);
            BigDecimal totalBeforeReceipt = new BigDecimal("0");
            if (GeneralTool.isNotEmpty(alreadyReceiptVos)) {
                for (AlreadyReceiptVo alreadyReceiptVo : alreadyReceiptVos) {
                    //收款金额（折合应收币种金额）
                    BigDecimal beforeReceiptAmountExchange = alreadyReceiptVo.getAmountReceivable();
                    //汇率调整
                    BigDecimal alreadyExchangeRate = alreadyReceiptVo.getAlreadyExchangeRate();

                    totalBeforeReceipt = totalBeforeReceipt.add(beforeReceiptAmountExchange).add(alreadyExchangeRate);
                }
            }
            //最大收款金额 = (总应收金额  - 之前已收的金额) * 汇率
            BigDecimal nowAmountReceipt = (receivableAmount.subtract(totalBeforeReceipt)).setScale(2, BigDecimal.ROUND_HALF_UP);
//            if ((receivableAmount.divide(showExchangeRate, 2, BigDecimal.ROUND_HALF_UP)).compareTo(totalBeforeReceipt) == 0) {
//                //已收齐
//                continue;
//            }
            //发票绑定金额
            BigDecimal subtotal = BigDecimal.ZERO;
            if (GeneralTool.isNotEmpty(invoicePlanMap) && GeneralTool.isNotEmpty(invoicePlanMap.get(planId))) {
                List<InvoiceReceivablePlan> invoiceReceivablePlans = invoicePlanMap.get(planId);
                for (InvoiceReceivablePlan invoiceReceivablePlan : invoiceReceivablePlans) {
                    subtotal = subtotal.add(GeneralTool.isNotEmpty(invoiceReceivablePlan.getAmount()) ? invoiceReceivablePlan.getAmount() : BigDecimal.ZERO);
                }
                subtotal.setScale(2, RoundingMode.HALF_UP);
            }
            receiptFormItemVo.setSubtotal(subtotal);
            /**
             * 实收汇率
             */
            BigDecimal exchangeRate = receiptForm.getExchangeRate();
            /*
                如果发票绑定金额不为空 则 收款金额 == 发票绑定金额。否则 为最大收款金额*实收汇率
             */
            if (receiptFormItemVo.getSubtotal()!=null && receiptFormItemVo.getSubtotal().compareTo(BigDecimal.ZERO)!=0) {
                receiptFormItemVo.setAmountReceipt(receiptFormItemVo.getSubtotal().multiply(exchangeRate).setScale(3,RoundingMode.HALF_UP));
            }else {
                receiptFormItemVo.setAmountReceipt(nowAmountReceipt.multiply(exchangeRate).setScale(3, RoundingMode.HALF_UP));
            }
            nowAmountReceipt = receiptFormItemVo.getAmountReceipt();
            receiptFormItemVo.setAmountReceivable(nowAmountReceipt.divide(showExchangeRate,3, BigDecimal.ROUND_HALF_UP).setScale(2, RoundingMode.HALF_UP));

            //汇率调整
            receiptFormItemVo.setAmountExchangeRate(BigDecimal.valueOf(0.0000).setScale(3, BigDecimal.ROUND_HALF_UP));

            receiptFormItemVo.setExchangeRateHkd(hkdRate);
            //折合港币
            BigDecimal amountHkd = hkdRate.multiply(nowAmountReceipt);
            receiptFormItemVo.setAmountHkd(amountHkd.setScale(2, BigDecimal.ROUND_HALF_UP));

            receiptFormItemVo.setExchangeRateRmb(rmbRate);
            //折合人名币
            BigDecimal amountRmb = rmbRate.multiply(nowAmountReceipt);
            receiptFormItemVo.setAmountRmb(amountRmb.setScale(2, BigDecimal.ROUND_HALF_UP));

            if (GeneralTool.isNotEmpty(invoiceListMap) && GeneralTool.isNotEmpty(invoiceListMap.get(receivablePlanVo.getId()))) {
                List<InvoiceVo> invoices = invoiceListMap.get(receivablePlanVo.getId());
                StringJoiner sj = new StringJoiner(",");
                for (InvoiceVo invoice : invoices) {
                    if (GeneralTool.isNotEmpty(invoice.getNum())) {
                        sj.add(invoice.getNum());
                    }
                }
                receiptFormItemVo.setFkInvoiceNum(sj.toString());
            }


            //其他金额类型为活动费用时特殊处理
            if (ProjectExtraEnum.EVENT_COST.key.equals(receivablePlanVo.getBonusType())) {
                receiptFormItemVo.setTargetNames(receivablePlanVo.getSummary());
            }
            receiptFormItemVo.setExchangeRate(receiptForm.getExchangeRate());
            receiptFormItemVos.add(receiptFormItemVo);
        }

        long endTime = System.currentTimeMillis();

        if (GeneralTool.isNotEmpty(times)) {
            times[0] = String.valueOf((endTime2 - endTime1));
            times[1] = String.valueOf((endTime - startTime) - (endTime2 - endTime1));
        }


        return receiptFormItemVos;
    }

    /**
     * feign 根据应收计划id获取绑定的收款单子单列表
     *
     * @Date 17:58 2022/4/21
     * <AUTHOR>
     */
    @Override
    public List<ReceiptFormItemVo> getReceiptFormItemListFeignByPlanIds(Set<Long> planIds) {
        return receiptFormItemMapper.getReceiptFormItemListFeignByPlanIds(planIds);
    }

    /**
     * feign 根据收款单子单ids获取收款单子单信息
     *
     * @Date 17:58 2022/4/21
     * <AUTHOR>
     */
    @Override
    public List<ReceiptFormItemVo> getReceiptFormItemListFeignByFormItemIds(Set<Long> receiptFormItemIds) {
        return receiptFormItemMapper.getReceiptFormItemListFeignByFormItemIds(receiptFormItemIds);
    }

    /**
     * 激活佣金结算
     *
     * @Date 17:16 2022/5/5
     * <AUTHOR>
     */
    @Override
    public Boolean activateCommissionSettlement(Set<Long> receiptFormItemIds) {
        List<ReceiptFormItem> receiptFormItems = receiptFormItemMapper.selectBatchIds(receiptFormItemIds);
        //获取fkReceiptFormIdList并去重
        List<Long> fkReceiptFormIdList = receiptFormItems.stream().map(ReceiptFormItem::getFkReceiptFormId).distinct().collect(Collectors.toList());
        List<ReceiptForm> receiptForms = receiptFormMapper.selectBatchIds(fkReceiptFormIdList);
        for (ReceiptForm receiptForm : receiptForms) {
            if (receiptForm.getSettlementStatus() == 0 ) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("commission_settlement_status_error"));
            }
        }

        for (Long receiptFormItemId : receiptFormItemIds) {
            Set<Long> set = new HashSet<>();
            set.add(receiptFormItemId);
            //检查收款单子项是否处于结算中或结算完成 true:没有处于结算中的，可以编辑 并删除批次表数据
            LambdaQueryWrapper<PayablePlanSettlementInstallment> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            List<Integer> status = new ArrayList<>();
            status.add(ProjectExtraEnum.INSTALLMENT_PROCESSING.key);
            status.add(ProjectExtraEnum.INSTALLMENT_COMPLETE.key);
            lambdaQueryWrapper.eq(PayablePlanSettlementInstallment::getFkReceiptFormItemId, receiptFormItemId)
                    .in(PayablePlanSettlementInstallment::getStatus, status);
            List<PayablePlanSettlementInstallment> payablePlanSettlementInstallments = payablePlanSettlementInstallmentMapper.selectList(lambdaQueryWrapper);
            boolean flag = GeneralTool.isEmpty(payablePlanSettlementInstallments);
            if (flag) {
                payablePlanSettlementInstallmentMapper.delete(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery().eq(PayablePlanSettlementInstallment::getFkReceiptFormItemId, receiptFormItemId)
                        .eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_UNTREATED.key));
            } else {
                continue;
            }

            ReceiptFormItem receiptFormItem = receiptFormItemMapper.selectById(receiptFormItemId);
            ReceiptForm receiptForm = receiptFormMapper.selectById(receiptFormItem.getFkReceiptFormId());
            //根据应收计划id获取对应的应付计划信息
            Result<PayablePlan> payablePlanResult = saleCenterClient.getPayablePlanByReceivablePlanId(receiptFormItem.getFkReceivablePlanId());
            if (!payablePlanResult.isSuccess()) {
                throw new GetServiceException(payablePlanResult.getMessage());
            }
            PayablePlan payablePlan = payablePlanResult.getData();
            if (GeneralTool.isEmpty(payablePlan)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("COMMISSION_PAYABLE_PLAN_NOT_EXIST"));
            }
            //HTI对冲金额
            BigDecimal hedgeAmount = payablePlanSettlementInstallmentMapper.getHedgeAmount(payablePlan.getId());

            SettlementInstallmentUpdateDto settlementInstallmentUpdateDto = new SettlementInstallmentUpdateDto();
            settlementInstallmentUpdateDto.setFkCompanyId(payablePlan.getFkCompanyId());
            settlementInstallmentUpdateDto.setHedgeAmount(hedgeAmount);
            settlementInstallmentUpdateDto.setFkPayablePlanId(payablePlan.getId());
            ReceivablePlanVo receivablePlanVo = saleCenterClient.getReceivablePlanById(payablePlan.getFkReceivablePlanId()).getData();
//                        ReceivablePlan receivablePlan = receivablePlanMapper.selectById(payablePlan.getFkReceivablePlanId());
            settlementInstallmentUpdateDto.setFkReceivablePlanId(receivablePlanVo.getId());
            settlementInstallmentUpdateDto.setReceivableAmount(receivablePlanVo.getReceivableAmount());
            settlementInstallmentUpdateDto.setPayableAmount(payablePlan.getPayableAmount());
            settlementInstallmentUpdateDto.setReceivableCurrencyTypeNum(receivablePlanVo.getFkCurrencyTypeNum());
            settlementInstallmentUpdateDto.setPayableCurrencyTypeNum(payablePlan.getFkCurrencyTypeNum());
            ReceiptFormItemVo receiptFormItemVo = BeanCopyUtils.objClone(receiptFormItem, ReceiptFormItemVo::new);
            receiptFormItemVo.setFkCurrencyTypeNum(receiptForm.getFkCurrencyTypeNum());
            htiAgencyCommissionSettlementService.insertSettlementInstallment(settlementInstallmentUpdateDto, receiptFormItemVo);
        }
        return true;
    }




    /**
     * 根据应收计划id批量激活佣金结算
     *
     * @param receivablePlanIds 应收计划ids
     * @return
     */
    @Override
    public Boolean activateCommissionSettlementByReceivablePlanIds(Set<Long> receivablePlanIds) {
        if (GeneralTool.isNotEmpty(receivablePlanIds)) {
            // 获取收款单明细ids
            List<ReceiptFormItem> receiptFormItems = receiptFormItemMapper.selectList(Wrappers.<ReceiptFormItem>lambdaQuery()
                    .in(ReceiptFormItem::getFkReceivablePlanId, receivablePlanIds));
            if (GeneralTool.isNotEmpty(receiptFormItems)) {
                Set<Long> receiptFormItemIds = receiptFormItems.stream().map(ReceiptFormItem::getId).collect(Collectors.toSet());
                this.activateCommissionSettlement(receiptFormItemIds);
            }
        }
        return true;
    }


    @Override
    public Boolean isReceivablePlanBound(Long fkReceivablePlanId) {
        if (GeneralTool.isEmpty(fkReceivablePlanId)) {
            return false;
        }
        List<ReceiptFormItem> formItems = receiptFormItemMapper.selectList(Wrappers.<ReceiptFormItem>lambdaQuery()
                .eq(ReceiptFormItem::getFkReceivablePlanId, fkReceivablePlanId));
        if (GeneralTool.isNotEmpty(formItems)) {
            return true;
        }
        return false;
    }

    @Override
    public List<ReceiptFormItemVo> getReceiptFormItemsByFormIds(Set<Long> ids) {
        if (GeneralTool.isEmpty(ids)) {
            return null;
        }
        List<ReceiptFormItem> formItems = receiptFormItemMapper.selectList(Wrappers.<ReceiptFormItem>lambdaQuery()
                .in(ReceiptFormItem::getFkReceiptFormId, ids));
        if (GeneralTool.isEmpty(formItems)) {
            return null;
        }
        return BeanCopyUtils.copyListProperties(formItems, ReceiptFormItemVo::new);
    }

    /**
     * 根据发票id获取收款单子单
     * @param fkInvoiceId
     * @return
     */
    @Override
    public List<ReceiptFormItemVo> getReceiptFormByInvoiceId(Long fkInvoiceId) {
        if (Objects.isNull(fkInvoiceId)) {
            return Collections.emptyList();
        }
        return receiptFormItemMapper.getReceiptFormByInvoiceId(fkInvoiceId);
    }

    /**
     * 获取绑定的收款单项
     *
     * @param planIds
     * @param formId
     * @return
     */
    private List<ReceiptFormItem> getBindReceiptItemsByPlanIdsAndFormId(Set<Long> planIds, Long formId) {
        List<ReceiptFormItem> formItems = receiptFormItemMapper.selectList(Wrappers.<ReceiptFormItem>query().lambda()
                .in(ReceiptFormItem::getFkReceivablePlanId, planIds));
        return formItems;
    }

    @Override
    public List<BaseSelectEntity> getStudentOfferItemSelect(String tableName, String fkTypeKey, Long fkTypeTargetId) {
        //111111
        if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(tableName)) {
            //提供商
            if (TableEnum.INSTITUTION_PROVIDER.key.equals(fkTypeKey)) {
                Result<List<BaseSelectEntity>> result = saleCenterClient.getOfferItemSelectByProviderId(tableName, fkTypeTargetId);
                if (!result.isSuccess()) {
                    throw new GetServiceException(result.getMessage());
                }
                List<BaseSelectEntity> baseSelectEntities = result.getData();
                baseSelectEntities.removeIf(Objects::isNull);

//                if (GeneralTool.isNotEmpty(baseSelectEntities)) {
//                    Iterator<BaseSelectEntity> iterator = baseSelectEntities.iterator();
//                    while (iterator.hasNext()) {
//                        BaseSelectEntity baseSelectEntity = iterator.next();
//                        //忽略已收齐
//                        Long receiptPlanId = baseSelectEntity.getId();
//                        Result<ReceivablePlanVo> resultReceivable = saleCenterClient.getReceivablePlanById(receiptPlanId);
//                        if (!resultReceivable.isSuccess()) {
//                            throw new GetServiceException(resultReceivable.getMessage());
//                        }
//                        if (GeneralTool.isEmpty(resultReceivable.getData())) {
//                            continue;
//                        }
//                        ReceivablePlanVo receivablePlanDto = resultReceivable.getData();
//                        //应收币种
//                        String receivablePlanCurrency = receivablePlanDto.getFkCurrencyTypeNum();
//                        //总应收金额
//                        BigDecimal receivableAmount = receivablePlanDto.getReceivableAmount();
//                        //实收金额
//                        //需要计算还未完成收款的金额
//                        Long planId = receivablePlanDto.getId();
//                        List<AlreadyReceiptDto> alreadyReceipt = receiptFormItemMapper.getAlreadyReceiptByPlanId(planId);
//
//                        BigDecimal totalBeforeReceipt = new BigDecimal("0");
//                        if (GeneralTool.isNotEmpty(alreadyReceipt)) {
//                            for (AlreadyReceiptDto alreadyReceiptDto : alreadyReceipt) {
//                                alreadyReceiptDto.setTargetNames(receivablePlanDto.getTargetNames());
//                                //收款金额（折合应收币种金额）
//                                BigDecimal beforeReceiptAmount = alreadyReceiptDto.getAmountReceivable();
////                                String beforeFormCurrency = alreadyReceiptDto.getAlreadyReceiptCurrency();
//                                //汇率
////                                BigDecimal formRate = getRate(beforeFormCurrency, receivablePlanCurrency);
////                                BigDecimal beforeReceiptAmountExchange = beforeReceiptAmount.multiply(formRate);
//                                //汇率调整
//                                BigDecimal alreadyExchangeRate = alreadyReceiptDto.getAlreadyExchangeRate();
//
//                                totalBeforeReceipt = totalBeforeReceipt.add(beforeReceiptAmount).add(alreadyExchangeRate);
//                            }
//                        }
//                        if (receivableAmount.compareTo(totalBeforeReceipt) <= 0) {
//                            iterator.remove();
//                        }
//                    }
//                }
//                return baseSelectEntities;
            }
        }
        return null;
    }

    /**
     * Author Cream
     * Description : //根据对象名称对象类型获取应收下拉
     * Date 2023/9/8 14:28
     * Params:
     * Return
     */
    public List<BaseSelectEntity> getReceivablePlans(String tableName, String fkTypeKey, ReceiptForm receiptForm, Integer pageNumber, Integer pageSize) {
        Long receiptFormId = receiptForm.getId();
        Long targetId = receiptForm.getFkTypeTargetId();
        List<BaseSelectEntity> baseSelectEntities = null;
        Result<List<BaseSelectEntity>> result = null;
        if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(tableName)) {
            //提供商
            if (TableEnum.INSTITUTION_PROVIDER.key.equals(fkTypeKey)) {
                result = saleCenterClient.getOfferItemSelectByProviderIdNew(tableName, targetId, receiptFormId, pageNumber, pageSize);
            } else if (ProjectKeyEnum.INSTITUTION_PROVIDER_CHANNEL.key.equals(fkTypeKey)) {
                //学校渠道
                result = saleCenterClient.getOfferItemSelectByChannelId(tableName, targetId, receiptFormId, pageNumber, pageSize);
            }
        } else if (TableEnum.SALE_STUDENT_INSURANCE.key.equals(tableName)) {
            //保险
            if (ProjectKeyEnum.BUSINESS_CHANNEL_INS.key.equals(fkTypeKey)) {
                result = saleCenterClient.getPlanIdsByTableNameAndChannelId(tableName, targetId, receiptFormId, fkTypeKey, pageNumber, pageSize);
            }
        } else if (TableEnum.SALE_STUDENT_ACCOMMODATION.key.equals(tableName)) {
            //住宿
            if (ProjectKeyEnum.BUSINESS_CHANNEL_ACC.key.equals(fkTypeKey)) {
                result = saleCenterClient.getPlanIdsByTableNameAndChannelId(tableName, targetId, receiptFormId, fkTypeKey, pageNumber, pageSize);
            }else if (ProjectKeyEnum.BUSINESS_PROVIDER.key.equals(fkTypeKey)){
                result = saleCenterClient.getPlanAndTargetName(targetId,receiptFormId);
            }
            //留学服务费用
        }else if (TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(tableName)){
            result = saleCenterClient.getStudentServiceFeeReceivablePlan(targetId, receiptFormId, pageNumber, pageSize);
        }

        //留学保险提供商
        if (ProjectKeyEnum.BUSINESS_PROVIDER_INS.key.equals(fkTypeKey)) {
            //留学保险 + 澳小保保险
            result = saleCenterClient.getPlanIdsByBusinessProviderId(targetId, receiptFormId);
        }
        if (result != null && result.isSuccess()) {
            baseSelectEntities = result.getData();
            if (null != baseSelectEntities) {
                baseSelectEntities.removeIf(Objects::isNull);
//                filter(baseSelectEntities);
            }
        }
        return baseSelectEntities;
    }

    private void filter(List<BaseSelectEntity> baseSelectEntities) {
        Set<Long> planIds = baseSelectEntities.stream().map(BaseSelectEntity::getId).collect(Collectors.toSet());
        Result<List<com.get.salecenter.vo.ReceivablePlanVo>> listResult = saleCenterClient.getReceivablePlansDetailNew(planIds);
        Map<Long, List<ReceivablePlanVo>> listMap;
        if (listResult.isSuccess() && GeneralTool.isNotEmpty(listResult.getData())) {
            listMap = listResult.getData().stream().collect(Collectors.groupingBy(ReceivablePlanVo::getId));
        } else {
            listMap = null;
        }
        List<AlreadyReceiptVo> alreadyReceiptVos = receiptFormItemMapper.getAlreadyReceiptByPlanIds(planIds);
        planIds.clear();
        Map<Long, List<AlreadyReceiptVo>> alreadyReceiptDtoMap = alreadyReceiptVos.stream().collect(Collectors.groupingBy(AlreadyReceiptVo::getFkReceivablePlanId));
        if (GeneralTool.isNotEmpty(baseSelectEntities)) {
            Iterator<BaseSelectEntity> iterator = baseSelectEntities.iterator();
            while (iterator.hasNext()) {
                BaseSelectEntity baseSelectEntity = iterator.next();
                //忽略已收齐
                Long receiptPlanId = baseSelectEntity.getId();
                List<ReceivablePlanVo> receivablePlanVos;
                if (null == listMap) {
                    receivablePlanVos = new ArrayList<>();
                } else {
                    receivablePlanVos = listMap.get(receiptPlanId);
                }
                if (GeneralTool.isEmpty(receivablePlanVos)) {
                    continue;
                }
                ReceivablePlanVo receivablePlanVo = receivablePlanVos.get(0);
                //应收币种
                String receivablePlanCurrency = receivablePlanVo.getFkCurrencyTypeNum();
                //总应收金额
                BigDecimal receivableAmount = receivablePlanVo.getReceivableAmount();
                //实收金额
                //需要计算还未完成收款的金额
                Long planId = receivablePlanVo.getId();
                List<AlreadyReceiptVo> alreadyReceipt = alreadyReceiptDtoMap.get(planId);

                BigDecimal totalBeforeReceipt = BigDecimal.ZERO;
                if (GeneralTool.isNotEmpty(alreadyReceipt)) {
                    for (AlreadyReceiptVo alreadyReceiptVo : alreadyReceipt) {
                        alreadyReceiptVo.setTargetNames(receivablePlanVo.getTargetNames());
                        //收款金额（折合应收币种金额）
                        BigDecimal beforeReceiptAmount = alreadyReceiptVo.getAmountReceivable();
//                                String beforeFormCurrency = alreadyReceiptDto.getAlreadyReceiptCurrency();
                        //汇率
//                                BigDecimal formRate = getRate(beforeFormCurrency, receivablePlanCurrency);
//                                BigDecimal beforeReceiptAmountExchange = beforeReceiptAmount.multiply(formRate);
                        //汇率调整
                        BigDecimal alreadyExchangeRate = alreadyReceiptVo.getAlreadyExchangeRate();

                        totalBeforeReceipt = totalBeforeReceipt.add(beforeReceiptAmount).add(alreadyExchangeRate);
                    }
                }
                if (GeneralTool.isNotEmpty(receivableAmount)) {
                    if (receivableAmount.compareTo(totalBeforeReceipt) == 0) {
                        iterator.remove();
                    }
                } else {
                    iterator.remove();
                }
            }
        }
    }


//    public List<BaseSelectEntity> getStudentOfferItem(String tableName, String fkTypeKey, Long fkTypeTargetId) {
//        if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(tableName)) {
//            //提供商
//            if (TableEnum.INSTITUTION_PROVIDER.key.equals(fkTypeKey)) {
//                Long providerId = 0L;
//                ReceiptForm receiptForm = receiptFormMapper.selectById(fkTypeTargetId);
//                if (GeneralTool.isNotEmpty(receiptForm)) {
//                    providerId = receiptForm.getFkTypeTargetId();
//                }
//                Result<List<BaseSelectEntity>> result = saleCenterClient.getOfferItemSelectByProviderId(tableName, providerId);
//                if (!result.isSuccess()) {
//                    throw new GetServiceException(result.getMessage());
//                }
//                List<BaseSelectEntity> baseSelectEntities = result.getData();
//                baseSelectEntities.removeIf(Objects::isNull);
//                Set<Long> planIds = baseSelectEntities.stream().map(BaseSelectEntity::getId).collect(Collectors.toSet());
//                List<ReceivablePlanVo> receivablePlansDetail = saleCenterClient.getReceivablePlansDetail(planIds).getData();
//                Map<Long, List<ReceivablePlanVo>> listMap;
//                if (GeneralTool.isEmpty(receivablePlansDetail)) {
//                    listMap = null;
//                } else {
//                    listMap = receivablePlansDetail.stream().collect(Collectors.groupingBy(ReceivablePlanVo::getId));
//                }
//
//                List<AlreadyReceiptDto> alreadyReceiptDtos = receiptFormItemMapper.getAlreadyReceiptByPlanIds(planIds);
//                Map<Long, List<AlreadyReceiptDto>> alreadyReceiptDtoMap = alreadyReceiptDtos.stream().collect(Collectors.groupingBy(AlreadyReceiptDto::getFkReceivablePlanId));
//                if (GeneralTool.isNotEmpty(baseSelectEntities)) {
//                    Iterator<BaseSelectEntity> iterator = baseSelectEntities.iterator();
//                    while (iterator.hasNext()) {
//                        BaseSelectEntity baseSelectEntity = iterator.next();
//                        //忽略已收齐
//                        Long receiptPlanId = baseSelectEntity.getId();
//                        List<ReceivablePlanVo> receivablePlanDtos;
//                        if (GeneralTool.isEmpty(listMap)) {
//                            receivablePlanDtos = new ArrayList<>();
//                        } else {
//                            receivablePlanDtos = listMap.get(receiptPlanId);
//                        }
////                        ResponseBo<ReceivablePlanVo> receivableResponseBo = feignSaleService.getReceivablePlanDetail(receiptPlanId);
//                        if (GeneralTool.isEmpty(receivablePlanDtos)) {
//                            continue;
//                        }
//                        ReceivablePlanVo receivablePlanDto = receivablePlanDtos.get(0);
//                        //应收币种
//                        String receivablePlanCurrency = receivablePlanDto.getFkCurrencyTypeNum();
//                        //总应收金额
//                        BigDecimal receivableAmount = receivablePlanDto.getReceivableAmount();
//                        //实收金额
//                        //需要计算还未完成收款的金额
//                        Long planId = receivablePlanDto.getId();
//                        List<AlreadyReceiptDto> alreadyReceipt = alreadyReceiptDtoMap.get(planId);
//
//                        BigDecimal totalBeforeReceipt = BigDecimal.ZERO;
//                        if (GeneralTool.isNotEmpty(alreadyReceipt)) {
//                            for (AlreadyReceiptDto alreadyReceiptDto : alreadyReceipt) {
//                                alreadyReceiptDto.setTargetNames(receivablePlanDto.getTargetNames());
//                                //收款金额（折合应收币种金额）
//                                BigDecimal beforeReceiptAmount = alreadyReceiptDto.getAmountReceivable();
////                                String beforeFormCurrency = alreadyReceiptDto.getAlreadyReceiptCurrency();
//                                //汇率
////                                BigDecimal formRate = getRate(beforeFormCurrency, receivablePlanCurrency);
////                                BigDecimal beforeReceiptAmountExchange = beforeReceiptAmount.multiply(formRate);
//                                //汇率调整
//                                BigDecimal alreadyExchangeRate = alreadyReceiptDto.getAlreadyExchangeRate();
//
//                                totalBeforeReceipt = totalBeforeReceipt.add(beforeReceiptAmount).add(alreadyExchangeRate);
//                            }
//                        }
//                        if (receivableAmount.compareTo(totalBeforeReceipt) <= 0) {
//                            iterator.remove();
//                        }
//                    }
//                }
//                return baseSelectEntities;
//            }
//        }
//        return null;
//    }

    private void setName(ReceiptFormItemVo receiptFormItemVo, Map<Long, String> companyMap) {
        if (GeneralTool.isNotEmpty(receiptFormItemVo.getFkReceivablePlanId())) {
            //111111
            Result<ReceivablePlanVo> resultReceivablePlanDto = saleCenterClient.getReceivablePlanById(receiptFormItemVo.getFkReceivablePlanId());
            if (!resultReceivablePlanDto.isSuccess()) {
                throw new GetServiceException(resultReceivablePlanDto.getMessage());
            }
            ReceivablePlanVo receivablePlanVo = resultReceivablePlanDto.getData();
            if (GeneralTool.isNotEmpty(receivablePlanVo)) {
                receiptFormItemVo.setFkTypeName(receivablePlanVo.getFkTypeName());
                receiptFormItemVo.setTargetNames(receivablePlanVo.getTargetNames());

                receiptFormItemVo.setReceivablePlanAmount(receivablePlanVo.getReceivableAmount());
                receiptFormItemVo.setReceivablePlanCurrency(receivablePlanVo.getFkCurrencyTypeNum());
            }
        }
        //公司
        Long companyId = receiptFormItemMapper.getCompanyIdByItemId(receiptFormItemVo.getId());
        if (GeneralTool.isNotEmpty(companyId)) {
            receiptFormItemVo.setCompanyName(companyMap.get(companyId));
        }
        //应收货币
        String receiptFormCurrency = receiptFormService.getCurrencyByFormId(receiptFormItemVo.getFkReceiptFormId());
        receiptFormItemVo.setReceiptFormCurrency(currencyTypeService.getCurrencyNameByNum(receiptFormCurrency));

        //已收
        Long planId = receiptFormItemVo.getFkReceivablePlanId();
        List<AlreadyReceiptVo> alreadyReceipt = receiptFormItemMapper.getAlreadyReceiptByPlanId(planId);
        receiptFormItemVo.setAlreadyReceiptDtos(alreadyReceipt);
        receiptFormItemVo.setReceivablePlanCurrencyName(currencyTypeService.getCurrencyNameByNum(receiptFormItemVo.getReceivablePlanCurrency()));
    }

    private void setDatasName(ReceiptFormItemVo receiptFormItemVo, Map<Long, String> companyMap, Map<Long, ReceivablePlanVo> offerItemMap) {

        if (GeneralTool.isNotEmpty(receiptFormItemVo.getFkReceivablePlanId())) {
            Result<ReceivablePlanVo> receivablePlanDtoResult = saleCenterClient.getReceivablePlanById(receiptFormItemVo.getFkReceivablePlanId());
            ReceivablePlanVo receivablePlanVo = receivablePlanDtoResult.getData();
            if (GeneralTool.isNotEmpty(receivablePlanVo)) {
                receiptFormItemVo.setFkTypeName(receivablePlanVo.getFkTypeName());
                receiptFormItemVo.setTargetNames(receivablePlanVo.getTargetNames());

                receiptFormItemVo.setReceivablePlanAmount(receivablePlanVo.getReceivableAmount());
                receiptFormItemVo.setReceivablePlanCurrency(receivablePlanVo.getFkCurrencyTypeNum());
                ReceivablePlanVo offerItem = offerItemMap.get(receivablePlanVo.getId());
                //学生信息
                receiptFormItemVo.setStudentId(offerItem.getStudentId());
                receiptFormItemVo.setIsDeferEntrance(offerItem.getIsDeferEntrance());
                receiptFormItemVo.setStudentInformation(offerItem.getStudentInformation());
                //国家名称
//                    if (CheckUtils.isNotEmpty(offerItem.getFkAreaCountryName())) {
//                        receivablePlanVo.setFkAreaCountryName(offerItem.getFkAreaCountryName());
//                    }
                //业务信息
//                    StringBuffer businessInformation = new StringBuffer();
//                    if (CheckUtils.isNotEmpty(offerItem.getFkInstitutionName())) {
//                        businessInformation.append(offerItem.getFkInstitutionName());
//                    }
//                    if (CheckUtils.isNotEmpty(offerItem.getFkCourseName())) {
//                        businessInformation.append("/");
//                        businessInformation.append(offerItem.getFkCourseName());
//                        if (CheckUtils.isNotEmpty(offerItem.getDurationType()) && CheckUtils.isNotEmpty(offerItem.getDuration())) {
//                            businessInformation.append(" (").append(offerItem.getDuration()).append(ProjectExtraEnum.getValueByKey(offerItem.getDurationType(), ProjectExtraEnum.DURATION_TYPE)).append(")");
//                        }
//                    }
//                    if (CheckUtils.isNotEmpty(offerItem.getOpeningTime())) {
//                        businessInformation.append("/");
//                        businessInformation.append(formatter.format(offerItem.getOpeningTime()));
//                    }
                receiptFormItemVo.setBusinessInformation(offerItem.getBusinessInformation());
                //渠道信息
                receiptFormItemVo.setChannelInformation(offerItem.getChannelInformation());


            }
        }
        //公司
        Long companyId = receiptFormItemMapper.getCompanyIdByItemId(receiptFormItemVo.getId());
        if (GeneralTool.isNotEmpty(companyId)) {
            receiptFormItemVo.setCompanyName(companyMap.get(companyId));
        }
        //应收货币
        String receiptFormCurrency = receiptFormService.getCurrencyByFormId(receiptFormItemVo.getFkReceiptFormId());
        receiptFormItemVo.setReceiptFormCurrency(currencyTypeService.getCurrencyNameByNum(receiptFormCurrency));
//        BigDecimal rateByCurrency = exchangeRateService.getRateByCurrency(receiptFormCurrency, receiptFormItemDto.getReceivablePlanCurrency());
//        receiptFormItemDto.setAmountReceivable(receiptFormItemDto.getAmountReceivable().multiply(rateByCurrency).setScale(2,BigDecimal.ROUND_HALF_UP));
        //已收
        Long planId = receiptFormItemVo.getFkReceivablePlanId();
        List<AlreadyReceiptVo> alreadyReceipt = receiptFormItemMapper.getAlreadyReceiptByPlanId(planId);
        receiptFormItemVo.setAlreadyReceiptDtos(alreadyReceipt);
        receiptFormItemVo.setReceivablePlanCurrencyName(currencyTypeService.getCurrencyNameByNum(receiptFormItemVo.getReceivablePlanCurrency()));
    }

    /**
     * @return java.math.BigDecimal
     * @Description: 获取汇率
     * @Param [payPlanCurrency, toCurrency]
     * <AUTHOR>
     */
    private BigDecimal getRate(String payPlanCurrency, String toCurrency) {
        if (GeneralTool.isEmpty(payPlanCurrency) || GeneralTool.isEmpty(toCurrency)) {
            return BigDecimal.ZERO;
        }
        if (payPlanCurrency.equals(toCurrency)) {
            return BigDecimal.ONE;
        }

        return exchangeRateService.getLastExchangeRate(false, payPlanCurrency, toCurrency).getExchangeRate();
    }

    private List<Long> getFormIds(Long fkCompanyId) {
        List<Long> formIds = receiptFormService.getFormByCompanyId(fkCompanyId);
        if (GeneralTool.isEmpty(formIds)) {
            formIds = new ArrayList<>();
            formIds.add(0L);
        }
        return formIds;
    }

    private Map<Long, String> getCompanyMap() {
//        ListResponseBo responseBo = permissionCenterClient.getAllCompanyDto();
//        JsonConfig config = new JsonConfig();
//        config.setExcludes(new String[]{"departmentTree", "totalNum"});
//        JSONArray jsonArray = JSONArray.fromObject(responseBo.getDatas(), config);
//        List<CompanyTreeVo> companyTreeDtos = JSONArray.toList(jsonArray, new CompanyTreeVo(), new JsonConfig());
//        //初始为5的map
//        Map<String, String> companyMap = new HashMap<>(5);
//        if (GeneralTool.isNotEmpty(companyTreeDtos)) {
//            companyMap = companyTreeDtos.stream().collect(Collectors.toMap(CompanyTreeVo::getId, CompanyTreeVo::getShortName));
//        }

        Map<Long, String> companyMap = new HashMap<>(5);
        Result<List<com.get.permissioncenter.vo.tree.CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
            List<com.get.permissioncenter.vo.tree.CompanyTreeVo> companyTreeVos = result.getData();
            List<CompanyTreeVo> companyTreeDtoList = BeanCopyUtils.copyListProperties(companyTreeVos, com.get.financecenter.vo.CompanyTreeVo::new);
            if (GeneralTool.isNotEmpty(companyTreeDtoList)) {
                companyMap = companyTreeDtoList.stream().collect(Collectors.toMap(com.get.financecenter.vo.CompanyTreeVo::getId, com.get.financecenter.vo.CompanyTreeVo::getShortName));
            }
        }

        return companyMap;
    }

}
