package get.middlecenter.service.impl;

import com.get.aisplatformcenterap.dto.GetPermissionMenuDto;
import com.get.aisplatformcenterap.dto.ReleaseInfoAndItemDto;
import com.get.aisplatformcenterap.dto.ReleaseInfoSearchDto;
import com.get.aisplatformcenterap.dto.UserScopedDataDto;
import com.get.aisplatformcenterap.enums.PlatFormType;
import com.get.aisplatformcenterap.feign.IPlatformCenterClient;
import com.get.aisplatformcenterap.vo.MenuTreeVo;
import com.get.aisplatformcenterap.vo.ReleaseInfoAndItemVo;
import com.get.aisplatformcenterap.vo.ReleaseInfoAndItemVos;
import com.get.aisplatformcenterap.vo.ReleaseInfoItemVo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.ResourceVo;
import get.middlecenter.service.ReleaseInfoAndItemService;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 发版信息项服务实现类
 */
@Service("middleReleaseInfoAndItemService")
public class ReleaseInfoAndItemServiceImpl implements ReleaseInfoAndItemService {

    @Resource
    private IPlatformCenterClient platformCenterClient;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Override
    public ResponseBo getReleaseInfoAndItem(SearchBean<ReleaseInfoSearchDto> page) {
        // 查询数据
        Result<ReleaseInfoAndItemVos> releaseInfo = platformCenterClient.getReleaseInfoAndPage(page);
        if (!(releaseInfo.isSuccess()) && GeneralTool.isEmpty(releaseInfo.getData())) {
            return new ResponseBo<>(null);
        }
        ReleaseInfoAndItemVos data = releaseInfo.getData();
        return new ResponseBo<>(data.getReleaseInfoAndItemVos(), data.getPage());

    }

    /**
     * 添加发版信息项和子项
     *
     * @param releaseInfoAndItemDto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addReleaseInfoAndItem(ReleaseInfoAndItemDto releaseInfoAndItemDto) {
        verifyParameters(releaseInfoAndItemDto);
        Result result = platformCenterClient.insertReleaseInfo(releaseInfoAndItemDto);
        if (!result.isSuccess()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
    }

    private static void verifyParameters(ReleaseInfoAndItemDto releaseInfoAndItemDto) {
        if (GeneralTool.isEmpty(releaseInfoAndItemDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (GeneralTool.isEmpty(releaseInfoAndItemDto.getFkPlatformId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("platform_id_cannot_be_empty"));
        }
        //如何平台类型为伙伴，code则为必填
        if (releaseInfoAndItemDto.getFkPlatformId().equals(PlatFormType.PARTNER.getId())) {
            if (GeneralTool.isEmpty(releaseInfoAndItemDto.getFkPlatformCode())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("platform_code_cannot_be_empty"));
            }
        }
        if (GeneralTool.isEmpty(releaseInfoAndItemDto.getTitle())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("title_cannot_be_empty"));
        }
        if (releaseInfoAndItemDto.getTitle().length() > 100) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("character_limit_exceeded") + "100" + " (" + releaseInfoAndItemDto.getTitle() + ")");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteReleaseInfoAndItem(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Result result = platformCenterClient.deleteReleaseInfo(id);
        if (!result.isSuccess()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }

    }

    /**
     * 根据id获取详细信息
     *
     * @param id
     * @return
     */
    @Override
    public ReleaseInfoAndItemVo getDetailedInformationById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Result<ReleaseInfoAndItemVo> result = platformCenterClient.getDetailedInformationById(id);
        if (!result.isSuccess() || result.getData() == null) {
            return null;
        }
        return result.getData();
    }

    /**
     * 编辑发版信息项和子项
     *
     * @param releaseInfoAndItemDto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editReleaseInfoAndItem(ReleaseInfoAndItemDto releaseInfoAndItemDto) {
        if (GeneralTool.isEmpty(releaseInfoAndItemDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(releaseInfoAndItemDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (GeneralTool.isEmpty(releaseInfoAndItemDto.getTitle())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("title_cannot_be_empty"));
        }
        if (releaseInfoAndItemDto.getTitle().length() > 100) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("character_limit_exceeded") + "100" + " (" + releaseInfoAndItemDto.getTitle() + ")");
        }
        Result result = platformCenterClient.updateReleaseInfo(releaseInfoAndItemDto);
        if (!result.isSuccess()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }

    }

    @Override
    public List<ResourceVo> getAisPermissionMenu() {
        Result<List<ResourceVo>> resourceTree = permissionCenterClient.getResourceTree();
        if (resourceTree.isSuccess() && resourceTree.getData() != null) {
            return resourceTree.getData();
        }
        return Collections.emptyList();
    }

    @Override
    public List<MenuTreeVo> getPartnerPermissionMenu(GetPermissionMenuDto getPermissionMenuDto) {
        Result<List<MenuTreeVo>> result = platformCenterClient.getPartnerPermissionMenu(getPermissionMenuDto);
        if (result.isSuccess() && result.getData() != null) {
            return result.getData();
        }
        return Collections.emptyList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateReleaseInfoStatus(ReleaseInfoAndItemDto releaseInfoAndItemDto) {
//        status  '枚举：0待发布/1已发布/2已撤回'
//        判断id不能为空,status不能为空
        if (GeneralTool.isEmpty(releaseInfoAndItemDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (GeneralTool.isEmpty(releaseInfoAndItemDto.getStatus())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("status_null"));
        }
        Result result = platformCenterClient.updateReleaseInfoStatus(releaseInfoAndItemDto);
        if (!result.isSuccess()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
    }

    @Override
    public ResponseBo getUserListByResourceKeys(SearchBean<UserScopedDataDto> page) {
        verifyParameters(page.getData());
        List<ReleaseInfoAndItemVo> userReleaseInfoListDatas = new ArrayList<>();

        Result<ReleaseInfoAndItemVos> releaseInfo = platformCenterClient.getUserListByResourceKeysAndPage(page);
        if (!(releaseInfo.isSuccess()) && GeneralTool.isEmpty(releaseInfo.getData())) {
            return new ResponseBo<>(null);
        }
        ReleaseInfoAndItemVos data = releaseInfo.getData();
        return new ResponseBo<>(data.getReleaseInfoAndItemVos(), data.getPage());

    }

    @Override
    public ReleaseInfoAndItemVo getUserOneByResourceKeys(UserScopedDataDto userScopedDataDto) {
        Result<List<ReleaseInfoAndItemVo>> userListByResourceKeysDatas = platformCenterClient.getUserListByResourceKeys(userScopedDataDto);
        if (!userListByResourceKeysDatas.isSuccess() || GeneralTool.isEmpty(userListByResourceKeysDatas.getData())) {
            return null;
        }
        List<ReleaseInfoAndItemVo> userListByResourceKeys  = userListByResourceKeysDatas.getData();
        ReleaseInfoAndItemVo releaseInfoAndItemVo = new ReleaseInfoAndItemVo();
        if (GeneralTool.isNotEmpty(userListByResourceKeys)) {
            //返回创建时间最新的数据
            releaseInfoAndItemVo = userListByResourceKeys.get(0);
            userScopedDataDto.setReleaseInfoId(releaseInfoAndItemVo.getId());
        }
        Result<List<ReleaseInfoItemVo>> releaseInfoItemByReleaseInfoIdAndResourceKeysDatas = platformCenterClient.getReleaseInfoItemByReleaseInfoIdAndResourceKeys(userScopedDataDto);
        if (!releaseInfoItemByReleaseInfoIdAndResourceKeysDatas.isSuccess() || GeneralTool.isEmpty(releaseInfoItemByReleaseInfoIdAndResourceKeysDatas.getData())) {
            return null;
        }
        List<ReleaseInfoItemVo> releaseInfoItemByReleaseInfoIdAndResourceKeys = releaseInfoItemByReleaseInfoIdAndResourceKeysDatas.getData();
        releaseInfoAndItemVo.setReleaseInfoItemVos(releaseInfoItemByReleaseInfoIdAndResourceKeys);
        return releaseInfoAndItemVo;

    }

    private static void verifyParameters(UserScopedDataDto userScopedDataDto) {
        if (GeneralTool.isEmpty(userScopedDataDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(userScopedDataDto.getFkPlatformId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("platform_id_cannot_be_empty"));
        }
        if (GeneralTool.isEmpty(userScopedDataDto.getFkResourceKeys())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("platform_code_cannot_be_empty"));
        }
        if (GeneralTool.isEmpty(userScopedDataDto.getFkResourceKeys())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("resource_keys_cannot_be_empty"));
        }
        if (GeneralTool.isEmpty(userScopedDataDto.getFkStaffId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staffId_is_null"));
        }
    }


}
