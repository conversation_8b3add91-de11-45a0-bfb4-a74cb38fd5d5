package com.get.financecenter.vo;

import com.get.financecenter.entity.PrepayApplicationForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/4/1 11:27
 */
@Data
public class PrepayApplicationFormVo extends PrepayApplicationForm {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "所属部门")
    private String DepartmentName;
    @ApiModelProperty("公司简称")
    private String shortName;

    /**
     * 报销人名称
     */
    @ApiModelProperty(value = "报销人名称")
    private String staffName;

    @ApiModelProperty("部署id")
    private String deployId;
    @ApiModelProperty("任务版本号")
    private Integer taskVersion;
    @ApiModelProperty("任务id")
    private String taskId;
    @ApiModelProperty("待签或代表")
    private int signOrGetStatus;
    @ApiModelProperty("流程实例id")
    private String procInstId;
    @ApiModelProperty("币种中英")
    private String fkCurrencyTypeNumName;
    @ApiModelProperty("同表父id")
    private Long fkTableParentId;
    /**
     * 同意按钮状态
     */
    @ApiModelProperty(value = "同意按钮状态")
    private Boolean agreeButtonType;

    /**
     * 拒绝按钮状态
     */
    @ApiModelProperty(value = "拒绝按钮状态")
    private Boolean refuseButtonType;

}
