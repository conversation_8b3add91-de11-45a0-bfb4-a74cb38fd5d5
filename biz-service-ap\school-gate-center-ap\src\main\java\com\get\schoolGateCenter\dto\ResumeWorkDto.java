package com.get.schoolGateCenter.dto;
import com.get.schoolGateCenter.entity.ResumeWork;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2020/8/5
 * @TIME: 11:22
 * @Description: 工作经验DTO
 **/
@Data
public class ResumeWorkDto extends ResumeWork implements Serializable {

    /**
     * 行业类型Id
     */
    @ApiModelProperty(value = "行业类型名称")
    private String fkIndustryTypeName;

}
