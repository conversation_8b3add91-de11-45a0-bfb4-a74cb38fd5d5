package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("m_student_offer_item")
public class StudentOfferItem extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 学生申请方案项目Id
     */
    @ApiModelProperty(value = "学生申请方案项目Id")
    @Column(name = "id")
    private Long id;
    /**
     * 学生申请方案项目父Id
     */
    @ApiModelProperty(value = "学生申请方案项目父Id")
    @Column(name = "fk_parent_student_offer_item_id")
    private Long fkParentStudentOfferItemId;
    /**
     * 学生Id
     */
    @ApiModelProperty(value = "学生Id")
    @Column(name = "fk_student_id")
    private Long fkStudentId;
    /**
     * 代理Id（业绩绑定）
     */
    @ApiModelProperty(value = "代理Id（业绩绑定）")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 员工Id（业绩绑定，BD）
     */
    @ApiModelProperty(value = "员工Id（业绩绑定，BD）")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;
    /**
     * 学生申请方案Id
     */
    @ApiModelProperty(value = "学生申请方案Id")
    @Column(name = "fk_student_offer_id")
    private Long fkStudentOfferId;
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;
    /**
     * 学校学院Id
     */
    @ApiModelProperty(value = "学校学院Id")
    @Column(name = "fk_institution_faculty_id")
    private Long fkInstitutionFacultyId;
    /**
     * 学校校区Id
     */
    @ApiModelProperty(value = "学校校区Id")
    @Column(name = "fk_institution_zone_id")
    private Long fkInstitutionZoneId;
    /**
     * 课程Id
     */
    @ApiModelProperty(value = "课程Id")
    @Column(name = "fk_institution_course_id")
    private Long fkInstitutionCourseId;
    /**
     * 自定义课程Id
     */
    @ApiModelProperty(value = "自定义课程Id")
    @Column(name = "fk_institution_course_custom_id")
    @UpdateWithNull
    private Long fkInstitutionCourseCustomId;
    /**
     * 学校提供商Id
     */
    @ApiModelProperty(value = "学校提供商Id")
    @Column(name = "fk_institution_provider_id")
    @UpdateWithNull
    private Long fkInstitutionProviderId;
    /**
     * 渠道来源Id
     */
    @ApiModelProperty(value = "渠道来源Id")
    @Column(name = "fk_institution_channel_id")
    private Long fkInstitutionChannelId;
    /**
     * 学生申请方案项目状态步骤Id
     */
    @ApiModelProperty(value = "学生申请方案项目状态步骤Id")
    @Column(name = "fk_student_offer_item_step_id")
    private Long fkStudentOfferItemStepId;

    @ApiModelProperty(value = "申请方案项目状态步骤时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "student_offer_item_step_time")
    private Date studentOfferItemStepTime;

    /**
     * rpa order id（一键申请对应的Order id）
     */
    @ApiModelProperty(value = "rpa order id（一键申请对应的Order id）")
    @Column(name = "fk_issue_rpa_order_id")
    private Long fkIssueRpaOrderId;
    /**
     * 申请方案项目编号
     */
    @ApiModelProperty(value = "申请方案项目编号")
    @Column(name = "num")
    private String num;
    /**
     * 学生ID
     */
    @ApiModelProperty(value = "学生ID")
    @Column(name = "student_id")
    private String studentId;
    /**
     * 课程长度类型(0周、1学期、2年)
     */
    @UpdateWithNull
    @ApiModelProperty(value = "课程长度类型(0周、1学期、2年)")
    @Column(name = "duration_type")
    private Integer durationType;
    /**
     * 课程长度
     */
    @UpdateWithNull
    @ApiModelProperty(value = "课程长度")
    @Column(name = "duration")
    private BigDecimal duration;
    /**
     * 开学时间
     */
    @ApiModelProperty(value = "开学时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "opening_time")
    private Date openingTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "closing_time")
    private Date closingTime;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;
    /**
     * 学费金额
     */
    @ApiModelProperty(value = "学费金额")
    @Column(name = "tuition_amount")
    private BigDecimal tuitionAmount;
    /**
     * 课程官网URL
     */
    @ApiModelProperty(value = "课程官网URL")
    @Column(name = "course_website")
    private String courseWebsite;
    /**
     * 是否主课程：0否/1是（一套方案，只有一个主课程）
     */
    @ApiModelProperty(value = "是否主课程：0否/1是（一套方案，只有一个主课程）")
    @Column(name = "is_main")
    private Boolean isMain;
    /**
     * 是否后续课程：0否/1是
     */
    @ApiModelProperty(value = "是否后续课程：0否/1是")
    @Column(name = "is_follow")
    private Boolean isFollow;

    /**
     * 是否后续课程隐藏：0否/1是
     */
    @ApiModelProperty(value = "是否后续课程隐藏：0否/1是")
    @Column(name = "is_follow_hidden")
    private Boolean isFollowHidden = false;
    /**
     * 是否减免学分：0否/1是
     */
    @ApiModelProperty(value = "是否减免学分：0否/1是")
    @Column(name = "is_credit_exemption")
    private Boolean isCreditExemption;
    /**
     * 是否加申，0否/1是
     */
    @ApiModelProperty(value = "是否加申，0否/1是")
    @Column(name = "is_add_app")
    private Boolean isAddApp;

//    /**
//     * 是否记录跟进步骤，0否/1是
//     */
//    @ApiModelProperty(value = "是否记录跟进步骤，0否/1是")
//    @Column(name = "is_add_step")
//    private Boolean isAddStep;
    /**
     * 是否步骤更随主课，0否/1是
     */
    @ApiModelProperty(value = "是否步骤更随主课，0否/1是")
    @Column(name = "is_step_follow")
    private Boolean isStepFollow;

    /**
     * 是否无佣金：0否/1是
     */
    @ApiModelProperty(value = "是否无佣金：0否/1是")
    @Column(name = "is_no_commission")
    private Boolean isNoCommission;

    /**
     * 申请方式：0网申/1扫描/2原件邮递/3其他
     */
    @ApiModelProperty(value = "申请方式：0网申/1扫描/2原件邮递/3其他")
    @Column(name = "app_method")
    private Integer appMethod;
    /**
     * 申请备注（网申信息）
     */
    @ApiModelProperty(value = "申请备注（网申信息）")
    @UpdateWithNull
    @Column(name = "app_remark")
    private String appRemark;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 学习计划业务状态(多选)：0获得第一阶段佣金、1获得第二阶段佣金、2获得第三阶段佣金、3获得第四阶段佣金 / 4只读第一阶段、5只读第二阶段、6只读第三阶段、7只读第四阶段
     */
    @ApiModelProperty(value = "学习计划业务状态(多选)：0获得第一阶段佣金、1获得第二阶段佣金、2获得第三阶段佣金、3获得第四阶段佣金 / 4只读第一阶段、5只读第二阶段、6只读第三阶段、7只读第四阶段")
    @Column(name = "condition_type")
    private String conditionType;
    /**
     * 入学失败原因Id
     */
    @UpdateWithNull
    @ApiModelProperty(value = "入学失败原因Id")
    @Column(name = "fk_enrol_failure_reason_id")
    private Long fkEnrolFailureReasonId;
    /**
     * 其他入学失败原因
     */
    @ApiModelProperty(value = "其他入学失败原因")
    @Column(name = "other_failure_reason")
    private String otherFailureReason;
    /**
     * 是否延迟入学标记：0否/1是
     */
    @ApiModelProperty(value = "是否延迟入学标记：0否/1是")
    @Column(name = "is_defer_entrance")
    private Boolean isDeferEntrance=false;
    /**
     * 学习模式：枚举定义：0未定/1面授/2网课
     */
    @ApiModelProperty(value = "学习模式：枚举定义：0未定/1面授/2网课")
    @Column(name = "learning_mode")
    private Integer learningMode;


    @ApiModelProperty("支付押金时间")
    @Column(name = "deposit_time")
    @UpdateWithNull
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date depositTime;


    @ApiModelProperty("提交申请时间")
    @Column(name = "submit_app_time")
    @UpdateWithNull
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date submitAppTime;

    /**
     * 支付押金截止时间
     */
    @ApiModelProperty(value = "支付押金截止时间")
    @Column(name = "deposit_deadline")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @UpdateWithNull
    private Date depositDeadline;
    /**
     * 接受Offer截止时间
     */
    @ApiModelProperty(value = "接受Offer截止时间")
    @Column(name = "accept_offer_deadline")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @UpdateWithNull
    private Date acceptOfferDeadline;

    @ApiModelProperty(value = "支付学费时间")
    @Column(name = "tuition_time")
    @UpdateWithNull
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date tuitionTime;
    /**
     * 状态：0关闭/1打开
     */
    @ApiModelProperty(value = "状态：0关闭/1打开")
    @Column(name = "status")
    private Integer status;
    /**
     * 一键操作时间
     */
    @ApiModelProperty(value = "一键操作时间")
    @Column(name = "rpa_opt_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date rpaOptTime;
    /**
     * 一键完成时间
     */
    @ApiModelProperty(value = "一键完成时间")
    @Column(name = "rpa_finish_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date rpaFinishTime;
    /**
     * 一键处理状态：枚举
     */
    @ApiModelProperty(value = "一键处理状态：枚举")
    @Column(name = "status_rpa")
    private String statusRpa;
    /**
     * 来自平台类型：get_mso
     */
    @ApiModelProperty(value = "来自平台类型：get_mso")
    @Column(name = "fk_platform_type")
    private String fkPlatformType;
    /**
     * 旧系统学校名称
     */
    @ApiModelProperty(value = "旧系统学校名称")
    @Column(name = "old_institution_name")
    private String oldInstitutionName;
    /**
     * 旧系统学校全称
     */
    @ApiModelProperty(value = "旧系统学校全称")
    @Column(name = "old_institution_full_name")
    private String oldInstitutionFullName;
    /**
     * 旧系统课程名称
     */
    @ApiModelProperty(value = "旧系统课程名称")
    @Column(name = "old_course_custom_name")
    private String oldCourseCustomName;
    /**
     * 旧系统课程专业等级名称
     */
    @ApiModelProperty(value = "旧系统课程专业等级名称")
    @Column(name = "old_course_major_level_name")
    private String oldCourseMajorLevelName;
    /**
     * 旧系统课程类型名称
     */
    @ApiModelProperty(value = "旧系统课程类型名称")
    @Column(name = "old_course_type_name")
    private String oldCourseTypeName;
    /**
     * ISSUE课程输入标记：input/select
     */
    @ApiModelProperty(value = "ISSUE课程输入标记：input/select")
    @Column(name = "issue_course_input_flag")
    private String issueCourseInputFlag;
    /**
     * 旧数据财务id(gea)
     */
    @ApiModelProperty(value = "旧数据财务id(gea)")
    @Column(name = "id_gea_finance")
    private String idGeaFinance;
    /**
     * 旧数据id(issue)
     */
    @UpdateWithNull
    @ApiModelProperty(value = "旧数据id(issue)")
    @Column(name = "id_issue")
    private String idIssue;
    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    @Column(name = "id_gea")
    private String idGea;
    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    @Column(name = "id_iae")
    private String idIae;
    /**
     * 一键备注
     */
    @ApiModelProperty(value = "一键备注")
    @Column(name = "rpa_remark")
    private String rpaRemark;
    /**
     * 课程等级对应的ids，多个用逗号分隔
     */
    @ApiModelProperty(value = "课程等级对应的ids，多个用逗号分隔")
    @Column(name = "fk_institution_course_major_level_ids")
    @UpdateWithNull
    private String fkInstitutionCourseMajorLevelIds;
    /**
     * 课程类型对应的ids，多个用逗号分隔
     */
    @ApiModelProperty(value = "课程类型对应的ids，多个用逗号分隔")
    @Column(name = "fk_institution_course_type_ids")
    @UpdateWithNull
    private String fkInstitutionCourseTypeIds;

    @ApiModelProperty(value = "课程类型组别对应的ids，多个用逗号分隔")
    @Column(name = "fk_institution_course_type_group_ids")
    @UpdateWithNull
    private String fkInstitutionCourseTypeGroupIds;


    @ApiModelProperty("申请计划延时入学时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deferOpeningTime;

    @ApiModelProperty(value = "新申请状态：枚举：0缺资料/1未开放")
    @UpdateWithNull
    @Column(name = "new_app_status")
    private Integer newAppStatus;

    @ApiModelProperty(value = "课程开放时间")
    @Column(name = "course_open_time")
    @UpdateWithNull
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date courseOpenTime;

    //new_app_opt_time
    @ApiModelProperty(value = "新申请操作时间")
    @Column(name = "new_app_opt_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date newAppOptTime;

    @ApiModelProperty("保险购买方式枚举：0买学校保险/1通过Hti买保险/2通过其他机构买保险")
    @Column(name = "insurance_buy_method")
    @UpdateWithNull
    private Integer insuranceBuyMethod;

    @ApiModelProperty("押金支付方式枚举：0飞汇/1易思汇/2支付宝/3银行/4信用卡/5其他汇款平台")
    @Column(name = "deposit_payment_method")
    @UpdateWithNull
    private Integer depositPaymentMethod;

    @ApiModelProperty("学费支付方式枚举：0=飞汇/1=易思汇/2=支付宝/3=银行/4=信用卡/5=其他汇款平台")
    @Column(name = "tuition_payment_method")
    @UpdateWithNull
    private Integer tuitionPaymentMethod;

    /**
     * 申请费币种
     */
    @ApiModelProperty(value = "申请费币种")
    @Column(name = "fk_app_fee_currency_type_num")
    private String fkAppFeeCurrencyTypeNum;

    /**
     * 申请费金额
     */
    @ApiModelProperty(value = "申请费金额")
    @Column(name = "app_fee_amount")
    private BigDecimal appFeeAmount;

    /**
     * 申请费付费状态：0.付费失败 1.已发送代付邮件 2.已付费(支付渠道) 3.已付费(学校)
     */
    @ApiModelProperty(value = "申请费付费状态：0.付费失败 1.已发送代付邮件 2.已付费(支付渠道) 3.已付费(学校)")
    @Column(name = "app_fee_status")
    private Integer appFeeStatus;

    /**
     * 学费付费状态：0.付费失败 1.已发送代付邮件 2.已付费(支付渠道) 3.已付费(学校)
     */
    @ApiModelProperty(value = "学费付费状态：0.付费失败 1.已发送代付邮件 2.已付费(支付渠道) 3.已付费(学校)")
    @Column(name = "tuition_status")
    private Integer tuitionStatus;

}