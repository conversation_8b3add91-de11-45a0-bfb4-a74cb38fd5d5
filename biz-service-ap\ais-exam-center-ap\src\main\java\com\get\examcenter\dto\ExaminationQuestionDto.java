package com.get.examcenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/8/23 16:02
 */
@Data
public class ExaminationQuestionDto extends BaseVoEntity implements Serializable {


    /**
     * 题目类型Id
     */
    @ApiModelProperty(value = "题目类型Id")
    //@NotNull(message = "题目类型Id不能为空", groups = {Add.class, Update.class})
    private Long fkQuestionTypeId;

    /**
     * 考题编号
     */
    @ApiModelProperty(value = "考题编号")
    private String num;

    /**
     * 题型：枚举(单选题0/多选题1/判断题2)
     */
    @ApiModelProperty(value = "题型：枚举(单选题0/多选题1/判断题2)")
    private Integer questionType;

    /**
     * 问题内容
     */
    @ApiModelProperty(value = "问题内容")
    @NotBlank(message = "问题内容不能为空", groups = {Add.class, Update.class})
    private String question;

    /**
     * 得分
     */
    @ApiModelProperty(value = "得分")
    @NotNull(message = "得分不能为空", groups = {Add.class, Update.class})
    private Integer score;

    /**
     * 答题时间限制（分钟）
     */
    @ApiModelProperty(value = "答题时间限制（分钟）")
    private Integer timeLimit;

    /**
     * 是否复习题：0否/1是
     */
    @ApiModelProperty(value = "是否复习题：0否/1是")
    private Boolean isReview;

    /**
     * 是否允许重考：0否/1是
     */
    @ApiModelProperty(value = "是否允许重考：0否/1是")
    @NotNull(message = "是否允许重考不能为空", groups = {Add.class, Update.class})
    private Boolean isRetest;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @NotNull(message = "是否激活不能为空", groups = {Add.class, Update.class})
    private Boolean isActive;

    /**
     * 考卷Id
     */
    @ApiModelProperty(value = "考卷Id")
    private Long fkExaminationPaperId;


    /**
     * 答案
     */
    @ApiModelProperty(value = "答案")
    @NotNull(message = "答案不能为空", groups = {Add.class, Update.class})
    private List<ExaminationAnswerDto> examinationAnswerVoList;


    /**
     * 默认为0，为0时随机排序，排序为从大到小顺序排列
     */
    @ApiModelProperty(value = "默认为0，为0时随机排序，排序为从大到小顺序排列")
    @NotNull(message = "排序不能为空", groups = {Add.class, Update.class})
    private Integer viewOrder;

    /**
     * 是否必填：0否/1是
     */
    @ApiModelProperty(value = "是否必填：0否/1是")
    private Boolean isRequired;

    /**
     * 考卷Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

}
