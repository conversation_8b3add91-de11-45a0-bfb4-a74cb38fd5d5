package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.salecenter.dao.sale.ReceivablePlanMapper;
import com.get.salecenter.dao.sale.StudentOfferItemMapper;
import com.get.salecenter.dto.StudentReceivableAndPaySumDto;
import com.get.salecenter.dto.query.StudentReceivableAndPaySumQueryDto;
import com.get.salecenter.service.IAgentStaffService;
import com.get.salecenter.service.IStudentReceivableAndPaySumService;
import com.get.salecenter.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2021/11/22 10:26
 * @verison: 1.0
 * @description:
 */
@Slf4j
@Service
public class StudentReceivableAndPaySumServiceImpl implements IStudentReceivableAndPaySumService {

    @Resource
    private StudentOfferItemMapper studentOfferItemMapper;

    @Resource
    private IAgentStaffService agentStaffService;

    @Resource
    private IFinanceCenterClient financeCenterClient;

    @Resource
    private ReceivablePlanMapper receivablePlanMapper;

    /**
     * 学生应收应付汇总统计
     *
     * @param studentReceivableAndPaySumVo
     * @param page
     * @return
     */
    @Override
    public List<StudentReceivableAndPaySumVo> datas(StudentReceivableAndPaySumQueryDto studentReceivableAndPaySumVo, Page page) {
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        if (GeneralTool.isNotEmpty(studentReceivableAndPaySumVo.getFkCompanyIds())) {
            //查看的公司权限
            if (!SecureUtil.validateCompanys(studentReceivableAndPaySumVo.getFkCompanyIds())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }
        IPage<StudentReceivableAndPaySumVo> iPage = null;
        //计算总条数是否大于该页码,如果大于则将当前页码置为1
        if (page != null) {
            if (GeneralTool.isNotEmpty(page.getTotalResult())) {
                int totalPages = (int) Math.ceil((double) page.getTotalResult() / page.getShowCount());
                if (page.getCurrentPage().intValue() > totalPages) {
                    page.setCurrentPage(1);
                }
            }
        }
        if (page != null) {
            iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        }
        //修改搜索条件，需同步修改总计接口
        List<StudentReceivableAndPaySumVo> studentReceivableAndPaySumVos = getDorisStudentReceivableAndPaySumDtos(iPage, studentReceivableAndPaySumVo);
        if (page != null) {
            page.setAll((int) iPage.getTotal());
        }

        //代理bdMap
        Set<Long> agentIds = studentReceivableAndPaySumVos.stream().map(StudentReceivableAndPaySumVo::getAgentId).collect(Collectors.toSet());
        Map<Long, String> bdNameMap = agentStaffService.getBdNameByAgentIds(agentIds);
        Set<String> currencySet = new HashSet<>();
        Set<String> currencyReceivablSet = studentReceivableAndPaySumVos.stream().map(StudentReceivableAndPaySumVo::getReceivableCurrencyTypeNum).collect(Collectors.toSet());
        currencyReceivablSet = currencyReceivablSet.stream().filter(i -> i != null && !i.isEmpty()).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(currencyReceivablSet)) {
            for (String s : currencyReceivablSet) {
                String[] split = s.split(",");
                Set<String> set = Arrays.stream(split).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
                currencySet.addAll(set);
            }
        }
        Set<String> payCurrencySet =
                studentReceivableAndPaySumVos.stream().map(StudentReceivableAndPaySumVo::getPayCurrencyTypeNum).collect(Collectors.toSet());
        payCurrencySet = payCurrencySet.stream().filter(i -> i != null && !i.isEmpty()).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(payCurrencySet)) {
            for (String s : payCurrencySet) {
                String[] split = s.split(",");
                Set<String> set = Arrays.stream(split).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
                currencySet.addAll(set);
            }
        }
        currencySet = currencySet.stream().filter(i -> i != null && !i.isEmpty()).collect(Collectors.toSet());

        //添加港币金额显示
        Map<String, BigDecimal> currencyRateMap = financeCenterClient.getLastExchangeRateHkd(currencySet).getData();
        Map<String, String> currencyNamesNumsMap = financeCenterClient.getCurrencyNamesByNums(currencySet).getData();

//        page.restPage(studentReceivableAndPaySumVos);
        for (StudentReceivableAndPaySumVo studentReceivableAndPaySumDto : studentReceivableAndPaySumVos) {
//            if (GeneralTool.isNotEmpty(studentReceivableAndPaySumDto.getReceivableDiffAmount())) {
//                String[] split = studentReceivableAndPaySumDto.getReceivableDiffAmount().split(",");
//                List<String> list = Arrays.stream(split).filter(StringUtils::isNotBlank).map(s -> String.valueOf(Math.abs(Double.parseDouble(s)))).collect(Collectors.toList());
//                studentReceivableAndPaySumDto.setReceivableDiffAmountList(list.toArray(new String[]{}));
//                studentReceivableAndPaySumDto.setReceivableDiffAmount(String.join(",", list));
//                if (StringUtils.isNotBlank(studentReceivableAndPaySumDto.getReceivableDiffAmountCurrencyType())) {
//                    String[] reInfo = studentReceivableAndPaySumDto.getReceivableDiffAmountCurrencyType().split(",");
//                    StringBuilder s1 = new StringBuilder();
//                    for (int i = 0; i < reInfo.length; i++) {
//                        s1.append(Math.abs(Double.parseDouble(split[i]))).append(reInfo[i]).append(",");
//                    }
//                    String amountInfo = s1.toString();
//                    studentReceivableAndPaySumDto.setReceivableDiffAmountInfo(amountInfo.endsWith(",")?amountInfo.substring(0,amountInfo.indexOf(",")):amountInfo);
//                }
//
//            }
            //设置代理bd
            if (GeneralTool.isNotEmpty(bdNameMap)) {
                studentReceivableAndPaySumDto.setBdName(bdNameMap.get(studentReceivableAndPaySumDto.getAgentId()));
            }
//            if (GeneralTool.isNotEmpty(studentReceivableAndPaySumDto.getPayableDiffAmount())) {
//                String[] split = studentReceivableAndPaySumDto.getPayableDiffAmount().split(",");
//                List<String> list = Arrays.stream(split).filter(StringUtils::isNotBlank).map(s -> String.valueOf(Math.abs(Double.parseDouble(s)))).collect(Collectors.toList());
//                studentReceivableAndPaySumDto.setPayableDiffAmountList(list.toArray(new String[]{}));
//                studentReceivableAndPaySumDto.setPayableDiffAmount(String.join(",", list));
//                if (StringUtils.isNotBlank(studentReceivableAndPaySumDto.getPayableDiffAmountCurrencyType())) {
//                    String[] reInfo = studentReceivableAndPaySumDto.getPayableDiffAmountCurrencyType().split(",");
//                    StringBuilder s1 = new StringBuilder();
//                    for (int i = 0; i < reInfo.length; i++) {
//                        s1.append(Math.abs(Double.parseDouble(split[i]))).append(reInfo[i]).append(",");
//                    }
//                    String amountInfo = s1.toString();
//                    studentReceivableAndPaySumDto.setPayableDiffAmountInfo(amountInfo.endsWith(",")?amountInfo.substring(0,amountInfo.indexOf(",")):amountInfo);
//                }
//            }
            //设置港币回显
            setStudentReceivableAndPayHkd(studentReceivableAndPaySumDto, currencyRateMap, currencyNamesNumsMap);
        }
        return studentReceivableAndPaySumVos;
    }

    /**
     * 获取学生收付款信息(拷贝学生应收应付汇总统计列表逻辑,学生详情专用接口)
     *
     * @Date 9:48 2022/6/28
     * <AUTHOR>
     */
    @Override
    public List<StudentReceivableAndPaySumVo> getStudentReceivableAndPaySum(StudentReceivableAndPaySumQueryDto studentReceivableAndPaySumVo) {
        if (GeneralTool.isNotEmpty(studentReceivableAndPaySumVo.getFkCompanyIds())) {
            //查看的公司权限
            if (!SecureUtil.validateCompanys(studentReceivableAndPaySumVo.getFkCompanyIds())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }
        List<StudentReceivableAndPaySumVo> studentReceivableAndPaySumVos = studentOfferItemMapper.getStudentReceivableAndPaySumDatas(null, studentReceivableAndPaySumVo);
        for (StudentReceivableAndPaySumVo studentReceivableAndPaySumDto : studentReceivableAndPaySumVos) {
            if (GeneralTool.isNotEmpty(studentReceivableAndPaySumDto.getReceivableDiffAmount())) {
                String[] split = studentReceivableAndPaySumDto.getReceivableDiffAmount().split(",");
                List<String> list = Arrays.stream(split).filter(StringUtils::isNotBlank).map(s -> String.valueOf(Math.abs(Double.parseDouble(s)))).collect(Collectors.toList());
                studentReceivableAndPaySumDto.setReceivableDiffAmountList(list.toArray(new String[]{}));
                studentReceivableAndPaySumDto.setReceivableDiffAmount(String.join(",", list));
                if (StringUtils.isNotBlank(studentReceivableAndPaySumDto.getReceivableDiffAmountCurrencyType())) {
                    String[] reInfo = studentReceivableAndPaySumDto.getReceivableDiffAmountCurrencyType().split(",");
                    StringBuilder s1 = new StringBuilder();
                    for (int i = 0; i < reInfo.length; i++) {
                        s1.append(Math.abs(Double.parseDouble(split[i]))).append(reInfo[i]).append(",");
                    }
                    String amountInfo = s1.toString();
                    studentReceivableAndPaySumDto.setReceivableDiffAmountInfo(amountInfo.endsWith(",") ? amountInfo.substring(0, amountInfo.indexOf(",")) : amountInfo);
                }
            }
            if (GeneralTool.isNotEmpty(studentReceivableAndPaySumDto.getPayableDiffAmount())) {
                String[] split = studentReceivableAndPaySumDto.getPayableDiffAmount().split(",");
                List<String> list = Arrays.stream(split).filter(StringUtils::isNotBlank).map(s -> String.valueOf(Math.abs(Double.parseDouble(s)))).collect(Collectors.toList());
                studentReceivableAndPaySumDto.setPayableDiffAmountList(list.toArray(new String[]{}));
                studentReceivableAndPaySumDto.setPayableDiffAmount(String.join(",", list));
                if (StringUtils.isNotBlank(studentReceivableAndPaySumDto.getPayableDiffAmountCurrencyType())) {
                    String[] reInfo = studentReceivableAndPaySumDto.getPayableDiffAmountCurrencyType().split(",");
                    StringBuilder s1 = new StringBuilder();
                    for (int i = 0; i < reInfo.length; i++) {
                        s1.append(Math.abs(Double.parseDouble(split[i]))).append(reInfo[i]).append(",");
                    }
                    String amountInfo = s1.toString();
                    studentReceivableAndPaySumDto.setPayableDiffAmountInfo(amountInfo.endsWith(",") ? amountInfo.substring(0, amountInfo.indexOf(",")) : amountInfo);
                }
            }
            if (GeneralTool.isNotEmpty(studentReceivableAndPaySumDto.getReceivableTypeKey())) {
                studentReceivableAndPaySumDto.setReceivableTypeName(TableEnum.getValueByKey(studentReceivableAndPaySumDto.getReceivableTypeKey(), TableEnum.SALE_TARGET_TYPE));
            }
            if (GeneralTool.isNotEmpty(studentReceivableAndPaySumDto.getPayableTypeKey())) {
                studentReceivableAndPaySumDto.setPayableTypeName(TableEnum.getValueByKey(studentReceivableAndPaySumDto.getPayableTypeKey(), TableEnum.SALE_TARGET_TYPE));
            }
        }
        return studentReceivableAndPaySumVos;
    }

    @Override
    public StudentReceivableAndPaySummaryVo getReceivableAndPayPaginationInfo(StudentReceivableAndPaySumDto studentReceivableAndPaySumDto) {
        StudentReceivableAndPaySummaryVo studentReceivableAndPaySummaryVo = new StudentReceivableAndPaySummaryVo();
        if (GeneralTool.isNotEmpty(studentReceivableAndPaySumDto.getFkCompanyIds())) {
            //查看的公司权限
            if (!SecureUtil.validateCompanys(studentReceivableAndPaySumDto.getFkCompanyIds())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }

        if (StringUtils.isNotBlank(studentReceivableAndPaySumDto.getStudentName())) {
            studentReceivableAndPaySumDto.setStudentName(studentReceivableAndPaySumDto.getStudentName().replaceAll(" ", "").toLowerCase());
        }
        if (StringUtils.isNotBlank(studentReceivableAndPaySumDto.getInstitutionCourseName())) {
            studentReceivableAndPaySumDto.setInstitutionCourseName(studentReceivableAndPaySumDto.getInstitutionCourseName().replaceAll(" ", "").toLowerCase());
        }
        if (StringUtils.isNotBlank(studentReceivableAndPaySumDto.getInstitutionName())) {
            studentReceivableAndPaySumDto.setInstitutionName(studentReceivableAndPaySumDto.getInstitutionName().replaceAll(" ", "").toLowerCase());
        }

        if (StringUtils.isNotBlank(studentReceivableAndPaySumDto.getAgentNameNum())) {
            studentReceivableAndPaySumDto.setAgentNameNum(studentReceivableAndPaySumDto.getAgentNameNum().replaceAll(" ", "").toLowerCase());
        }
        if (StringUtils.isNotBlank(studentReceivableAndPaySumDto.getFkInvoiceNums())) {
            studentReceivableAndPaySumDto.setFkInvoiceNums(studentReceivableAndPaySumDto.getFkInvoiceNums().toLowerCase().replace("'", "_"));
        }
        List<StudentReceivableAndPaySumDetailVo> studentReceivableAndPaySumDatas = studentOfferItemMapper.getReceivableAndPayPaginationInfo(studentReceivableAndPaySumDto);
        if (GeneralTool.isNotEmpty(studentReceivableAndPaySumDatas)) {
            Set<String> currencySet = studentReceivableAndPaySumDatas.stream().map(StudentReceivableAndPaySumDetailVo::getReceivableCurrencyTypeNum).collect(Collectors.toSet());

            Set<String> payCurrencySet = studentReceivableAndPaySumDatas.stream().map(StudentReceivableAndPaySumDetailVo::getPayCurrencyTypeNum).collect(Collectors.toSet());
            currencySet.addAll(payCurrencySet);
            currencySet = currencySet.stream().filter(i -> i != null && !i.isEmpty()).collect(Collectors.toSet());

            //添加港币金额显示
            DecimalFormat df = new DecimalFormat("###,##0.00");
            BigDecimal receivableActualAmountInfoHkdSum = new BigDecimal(0);  //实收金额hkd
            BigDecimal receivablePlanAmountInfoHkdSum = new BigDecimal(0);      //应收金额hkd
            BigDecimal receivableDiffAmountInfoHkdSum = new BigDecimal(0);      //应收未收hkd
            BigDecimal payablePlanAmountInfoHkdSum = new BigDecimal(0);         //应付金额hkd
            BigDecimal payableActualAmountInfoHkdSum = new BigDecimal(0);       //实付金额Hkd
            BigDecimal payableDiffAmountInfoHkdSum = new BigDecimal(0);         //应付未付Hkd
            BigDecimal totalProfitInfoHkdSum = new BigDecimal(0);         //利润总计

            Map<String, BigDecimal> currencyRateMap = financeCenterClient.getLastExchangeRateHkd(currencySet).getData();
            if (GeneralTool.isNotEmpty(currencyRateMap)) {
                for (StudentReceivableAndPaySumDetailVo studentReceivableAndPaySumData : studentReceivableAndPaySumDatas) {
                    BigDecimal receivableRate = currencyRateMap.get(studentReceivableAndPaySumData.getReceivableCurrencyTypeNum());
                    BigDecimal payRate = currencyRateMap.get(studentReceivableAndPaySumData.getPayCurrencyTypeNum());
                    BigDecimal receivableAmount = studentReceivableAndPaySumData.getReceivableAmount();
                    if (GeneralTool.isNotEmpty(receivableAmount) && receivableAmount.compareTo(BigDecimal.ZERO) != 0 && GeneralTool.isNotEmpty(receivableRate)) {
                        receivablePlanAmountInfoHkdSum = receivablePlanAmountInfoHkdSum.add(receivableAmount.multiply(receivableRate).setScale(2,
                                BigDecimal.ROUND_HALF_UP));
                    }
                    //实收
                    BigDecimal actualReceivableAmount = studentReceivableAndPaySumData.getActualReceivableAmount();
                    if (GeneralTool.isNotEmpty(actualReceivableAmount) && actualReceivableAmount.compareTo(BigDecimal.ZERO) != 0 && GeneralTool.isNotEmpty(receivableRate)) {
                        receivableActualAmountInfoHkdSum = receivableActualAmountInfoHkdSum.add(actualReceivableAmount.multiply(receivableRate).setScale(2,
                                BigDecimal.ROUND_HALF_UP));
                    }
                    BigDecimal diffReceivableAmount = studentReceivableAndPaySumData.getDiffReceivableAmount();
                    if (GeneralTool.isNotEmpty(diffReceivableAmount) && diffReceivableAmount.compareTo(BigDecimal.ZERO) != 0 && GeneralTool.isNotEmpty(receivableRate)) {
                        receivableDiffAmountInfoHkdSum = receivableDiffAmountInfoHkdSum.add(diffReceivableAmount.multiply(receivableRate).setScale(2,
                                BigDecimal.ROUND_HALF_UP));
                    }


                    BigDecimal payableAmount = studentReceivableAndPaySumData.getPayableAmount();
                    if (GeneralTool.isNotEmpty(payableAmount) && payableAmount.compareTo(BigDecimal.ZERO) != 0 && GeneralTool.isNotEmpty(payRate)) {
                        payablePlanAmountInfoHkdSum = payablePlanAmountInfoHkdSum.add(payableAmount.multiply(payRate).setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                    BigDecimal actualPayableAmount = studentReceivableAndPaySumData.getActualPayableAmount();
                    if (GeneralTool.isNotEmpty(actualPayableAmount) && actualPayableAmount.compareTo(BigDecimal.ZERO) != 0 && GeneralTool.isNotEmpty(payRate)) {
                        payableActualAmountInfoHkdSum = payableActualAmountInfoHkdSum.add(actualPayableAmount.multiply(payRate).setScale(2,
                                BigDecimal.ROUND_HALF_UP));
                    }
                    BigDecimal diffPayableAmount = studentReceivableAndPaySumData.getDiffPayableAmount();
                    if (GeneralTool.isNotEmpty(diffPayableAmount) && diffPayableAmount.compareTo(BigDecimal.ZERO) != 0 && GeneralTool.isNotEmpty(payRate)) {
                        payableDiffAmountInfoHkdSum = payableDiffAmountInfoHkdSum.add(diffPayableAmount.multiply(payRate).setScale(2,
                                BigDecimal.ROUND_HALF_UP));
                    }
                }
            }
            studentReceivableAndPaySummaryVo.setReceivableActualAmountInfoHkdSum(df.format(receivableActualAmountInfoHkdSum) + "港币（HKD)");
            studentReceivableAndPaySummaryVo.setReceivablePlanAmountInfoHkdSum(df.format(receivablePlanAmountInfoHkdSum) + "港币（HKD)");
            studentReceivableAndPaySummaryVo.setReceivableDiffAmountInfoHkdSum(df.format(receivableDiffAmountInfoHkdSum.abs()) + "港币（HKD)");
            studentReceivableAndPaySummaryVo.setPayablePlanAmountInfoHkdSum(df.format(payablePlanAmountInfoHkdSum) + "港币（HKD)");
            studentReceivableAndPaySummaryVo.setPayableActualAmountInfoHkdSum(df.format(payableActualAmountInfoHkdSum) + "港币（HKD)");
            studentReceivableAndPaySummaryVo.setPayableDiffAmountInfoHkdSum(df.format(payableDiffAmountInfoHkdSum.abs()) + "港币（HKD)");
            totalProfitInfoHkdSum = receivablePlanAmountInfoHkdSum.subtract(payablePlanAmountInfoHkdSum);
            studentReceivableAndPaySummaryVo.setTotalProfitInfoHkdSum(df.format(totalProfitInfoHkdSum) + "港币（HKD)");
        }

        return studentReceivableAndPaySummaryVo;
    }

    /**
     * 查询数据库仓库 应付计划列表
     *
     * @Date 16:15 2022/6/27
     * <AUTHOR>
     */

    //TODO 数据库连接
//    @DS("saledb-doris")
    private List<StudentReceivableAndPaySumVo> getDorisStudentReceivableAndPaySumDtos(IPage<StudentReceivableAndPaySumVo> iPage, StudentReceivableAndPaySumQueryDto studentReceivableAndPaySumVo) {
        if (StringUtils.isNotBlank(studentReceivableAndPaySumVo.getStudentName())) {
            studentReceivableAndPaySumVo.setStudentName(studentReceivableAndPaySumVo.getStudentName().replaceAll(" ", "").toLowerCase());
        }
        if (StringUtils.isNotBlank(studentReceivableAndPaySumVo.getInstitutionCourseName())) {
            studentReceivableAndPaySumVo.setInstitutionCourseName(studentReceivableAndPaySumVo.getInstitutionCourseName().replaceAll(" ", "").toLowerCase());
        }
        if (StringUtils.isNotBlank(studentReceivableAndPaySumVo.getInstitutionName())) {
            studentReceivableAndPaySumVo.setInstitutionName(studentReceivableAndPaySumVo.getInstitutionName().replaceAll(" ", "").toLowerCase());
        }
        if (StringUtils.isNotBlank(studentReceivableAndPaySumVo.getAgentNameNum())) {
            studentReceivableAndPaySumVo.setAgentNameNum(studentReceivableAndPaySumVo.getAgentNameNum().replaceAll(" ", "").toLowerCase());
        }
        if (StringUtils.isNotBlank(studentReceivableAndPaySumVo.getFkInvoiceNums())) {
            studentReceivableAndPaySumVo.setFkInvoiceNums(studentReceivableAndPaySumVo.getFkInvoiceNums().toLowerCase().replace("'", "_"));
        }
        return studentOfferItemMapper.getStudentReceivableAndPaySumDatas(iPage, studentReceivableAndPaySumVo);
    }

    /**
     * 导出提学生应收应付汇总统计列表Excel
     *
     * @param response
     * @param studentReceivableAndPaySumVo
     */
    @Override
    public void exportStudentReceivableAndPaySumExcel(HttpServletResponse response, StudentReceivableAndPaySumQueryDto studentReceivableAndPaySumVo) {
        Page page = new Page();
        List<StudentReceivableAndPaySumVo> datas = datas(studentReceivableAndPaySumVo, null);

        //如果使用发票查询，显示发票金额
        if (GeneralTool.isNotEmpty(studentReceivableAndPaySumVo.getFkInvoiceNums())) {
            Set<Long> ids = datas.stream().map(StudentReceivableAndPaySumVo::getId).collect(Collectors.toSet());
            List<IninvoiceAmountVo> ininvoiceAmountVos = receivablePlanMapper.getIninvoiceAmounts(ids);
            if (GeneralTool.isNotEmpty(ininvoiceAmountVos)) {
                Map<Long, BigDecimal> ininvoiceAmountMap = ininvoiceAmountVos.stream().collect(Collectors.toMap(IninvoiceAmountVo::getId, IninvoiceAmountVo::getIninvoiceAmount));
                for (StudentReceivableAndPaySumVo data : datas) {
                    if (GeneralTool.isNotEmpty(ininvoiceAmountMap) && ininvoiceAmountMap.containsKey(data.getId())) {
                        //发票绑定金额
                        data.setInvoiceBindAmount(ininvoiceAmountMap.get(data.getId()));


                        BigDecimal receivablePlanAmount = BigDecimal.ZERO;
                        if (GeneralTool.isNotEmpty(data.getReceivablePlanAmount())) {
                            String[] amounts = data.getReceivablePlanAmount().split(",");
                            for (String amountStr : amounts) {
                                BigDecimal amount = new BigDecimal(amountStr);
                                receivablePlanAmount = receivablePlanAmount.add(amount);
                            }
                        }

                        BigDecimal payableActualAmount = BigDecimal.ZERO;
                        if (GeneralTool.isNotEmpty(data.getPayablePlanAmount())) {
                            String[] amounts = data.getPayablePlanAmount().split(",");
                            for (String amountStr : amounts) {
                                BigDecimal amount = new BigDecimal(amountStr);
                                payableActualAmount = payableActualAmount.add(amount);
                            }
                        }

                        //发票绑定应付金额
                        if (GeneralTool.isNotEmpty(data.getInvoiceBindAmount()) && data.getInvoiceBindAmount().compareTo(BigDecimal.ZERO) != 0
                                && payableActualAmount.compareTo(BigDecimal.ZERO) != 0) {
                            BigDecimal divide = data.getInvoiceBindAmount().divide(receivablePlanAmount.divide(payableActualAmount, 2), 2, BigDecimal.ROUND_HALF_UP);
                            data.setInvoicePayableActualAmount(divide);
                        }

                    }
                }
            }
        }

        if (GeneralTool.isEmpty(datas)) {
            return;
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        List<StudentReceivableAndPaySumExportVo> studentReceivableAndPaySumExportVos = BeanCopyUtils.copyListProperties(datas, StudentReceivableAndPaySumExportVo::new, (data, studentReceivableAndPaySumDto) -> {
            if (GeneralTool.isNotEmpty(data.getDeferOpeningTime())) {
                studentReceivableAndPaySumDto.setOpeningTime(dateFormat.format(data.getDeferOpeningTime()));
            }
        });
        Set<String> ignoreFields = new HashSet<>();
        if (SecureUtil.getFkCompanyId().equals(3L)) {
            //hit导出忽略字段
            ignoreFields.add("agentNum");

            if (GeneralTool.isEmpty(studentReceivableAndPaySumVo.getFkInvoiceNums())) {
                ignoreFields.add("invoiceBindAmount");
                ignoreFields.add("invoicePayableActualAmount");
            }
        } else {
            ignoreFields.add("fkBdAgentNum");
            ignoreFields.add("invoiceBindAmount");
            ignoreFields.add("invoicePayableActualAmount");
        }

        Map<String, String> fileMap = FileUtils.getFileMapIgnoreSomeField(StudentReceivableAndPaySumExportVo.class, ignoreFields);
        FileUtils.exportExcel(response, studentReceivableAndPaySumExportVos, "studentReceivableAndPaySumInfo", fileMap);
        //FileUtils.exportExcelNotWrapText(response, studentReceivableAndPaySumExportVos, "studentReceivableAndPaySumInfo", StudentReceivableAndPaySumExportVo.class);
    }

    private void setStudentReceivableAndPayHkd(StudentReceivableAndPaySumVo studentReceivableAndPaySumVo, Map<String, BigDecimal> currencyRateMap, Map<String, String> currencyNamesNumsMap) {
        //设置港币回显
        if (GeneralTool.isNotEmpty(currencyRateMap)) {
            //应收（hkd)
            if (GeneralTool.isNotEmpty(studentReceivableAndPaySumVo.getReceivablePlanAmountInfo())) {
                String[] split = studentReceivableAndPaySumVo.getReceivablePlanAmountInfo().split(",");
                List<String> list = Arrays.stream(split).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                if (GeneralTool.isNotEmpty(list)) {
                    List<StudentReceivableHkdVo> studentReceivableHkdVos = new ArrayList<>();
                    for (String s : list) {
                        String s1 = s.substring(0, s.indexOf("|")).trim();
                        String currencyType = s.substring(s.indexOf("（") + 1, s.indexOf("）"));
                        if (s1.matches("-?[0-9]+.?[0-9]*")) {
                            BigDecimal receivableDiffAmount = new BigDecimal(s1);
                            BigDecimal receivableRateNew = currencyRateMap.get(currencyType);
                            StudentReceivableHkdVo studentReceivableHkdVo = new StudentReceivableHkdVo();
                            studentReceivableHkdVo.setReceivableAmountInfo(receivableDiffAmount.setScale(2, BigDecimal.ROUND_HALF_UP) + currencyNamesNumsMap.get(currencyType));
                            studentReceivableHkdVo.setReceivableAmount(receivableDiffAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                            if (GeneralTool.isNotEmpty(receivableRateNew)) {
                                studentReceivableHkdVo.setReceivableAmountHkd(receivableDiffAmount.multiply(receivableRateNew).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                                studentReceivableHkdVo.setReceivableAmountInfoHkd(receivableDiffAmount.multiply(receivableRateNew).setScale(2, BigDecimal.ROUND_HALF_UP).toString() + "港币（HKD)");

                            }
                            studentReceivableHkdVos.add(studentReceivableHkdVo);

                        }
                    }
                    studentReceivableAndPaySumVo.setStudentReceivableAmountHkdList(studentReceivableHkdVos);
                }
            }
//            //实收（hkd）
            if (GeneralTool.isNotEmpty(studentReceivableAndPaySumVo.getReceivableActualAmountInfo())) {
                String[] split = studentReceivableAndPaySumVo.getReceivableActualAmountInfo().split(",");
                List<String> list = Arrays.stream(split).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                if (GeneralTool.isNotEmpty(list)) {
                    List<StudentReceivableHkdVo> studentReceivableHkdVos = new ArrayList<>();
                    for (String s : list) {
                        String s1 = s.substring(0, s.indexOf("|")).trim();
                        String currencyType = s.substring(s.indexOf("（") + 1, s.indexOf("）"));
                        if (s1.matches("-?[0-9]+.?[0-9]*")) {
                            BigDecimal receivableDiffAmount = new BigDecimal(s1);
                            BigDecimal receivableRateNew = currencyRateMap.get(currencyType);
                            StudentReceivableHkdVo studentReceivableHkdVo = new StudentReceivableHkdVo();
                            studentReceivableHkdVo.setReceivableAmountInfo(receivableDiffAmount.setScale(2, BigDecimal.ROUND_HALF_UP) + currencyNamesNumsMap.get(currencyType));
                            studentReceivableHkdVo.setReceivableAmount(receivableDiffAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                            if (GeneralTool.isNotEmpty(receivableRateNew)) {
                                studentReceivableHkdVo.setReceivableAmountHkd(receivableDiffAmount.multiply(receivableRateNew).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                                studentReceivableHkdVo.setReceivableAmountInfoHkd(receivableDiffAmount.multiply(receivableRateNew).setScale(2, BigDecimal.ROUND_HALF_UP).toString() + "港币（HKD)");
                            }
                            studentReceivableHkdVos.add(studentReceivableHkdVo);
                        }
                    }
                    studentReceivableAndPaySumVo.setStudentReceivableActualAmountHkdList(studentReceivableHkdVos);
                }
            }

            //应收未收（hkd）
            if (GeneralTool.isNotEmpty(studentReceivableAndPaySumVo.getReceivableDiffAmountInfo())) {
                String[] split = studentReceivableAndPaySumVo.getReceivableDiffAmountInfo().split(",");
                List<String> list = Arrays.stream(split).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                if (GeneralTool.isNotEmpty(list)) {
                    List<StudentReceivableHkdVo> studentReceivableHkdVos = new ArrayList<>();
                    for (String s : list) {
                        String s1 = s.substring(0, s.indexOf("|")).trim();
                        String currencyType = s.substring(s.indexOf("（") + 1, s.indexOf("）"));
                        if (s1.matches("-?[0-9]+.?[0-9]*")) {
                            BigDecimal receivableDiffAmount = new BigDecimal(s1).abs();
                            BigDecimal receivableRateNew = currencyRateMap.get(currencyType);
                            StudentReceivableHkdVo studentReceivableHkdVo = new StudentReceivableHkdVo();
                            studentReceivableHkdVo.setReceivableAmountInfo(receivableDiffAmount.setScale(2, BigDecimal.ROUND_HALF_UP) + currencyNamesNumsMap.get(currencyType));
                            studentReceivableHkdVo.setReceivableAmount(receivableDiffAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                            if (GeneralTool.isNotEmpty(receivableRateNew)) {
                                studentReceivableHkdVo.setReceivableAmountHkd(receivableDiffAmount.multiply(receivableRateNew).abs().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                                studentReceivableHkdVo.setReceivableAmountInfoHkd(receivableDiffAmount.multiply(receivableRateNew).setScale(2, BigDecimal.ROUND_HALF_UP).toString() + "港币（HKD)");

                            }
                            studentReceivableHkdVos.add(studentReceivableHkdVo);
                        }
                    }
                    studentReceivableAndPaySumVo.setStudentReceivableDiffHkdList(studentReceivableHkdVos);
                }
            }

            //应付金额（hkd）
            if (GeneralTool.isNotEmpty(studentReceivableAndPaySumVo.getPayablePlanAmountInfo())) {
                String[] split = studentReceivableAndPaySumVo.getPayablePlanAmountInfo().split(",");
                List<String> list = Arrays.stream(split).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                if (GeneralTool.isNotEmpty(list)) {
                    List<StudentPayHkdVo> studentPayHkdVos = new ArrayList<>();
                    for (String s : list) {
                        String s1 = s.substring(0, s.indexOf("|")).trim();
                        String currencyType = s.substring(s.indexOf("（") + 1, s.indexOf("）"));
                        if (s1.matches("-?[0-9]+.?[0-9]*")) {
                            BigDecimal receivableDiffAmount = new BigDecimal(s1);
                            BigDecimal receivableRateNew = currencyRateMap.get(currencyType);
                            StudentPayHkdVo studentPayHkdVo = new StudentPayHkdVo();
                            studentPayHkdVo.setPayAmountInfo(receivableDiffAmount.setScale(2, BigDecimal.ROUND_HALF_UP) + currencyNamesNumsMap.get(currencyType));
                            studentPayHkdVo.setPayAmount(receivableDiffAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                            if (GeneralTool.isNotEmpty(receivableRateNew)) {
                                studentPayHkdVo.setPayAmountHkd(receivableDiffAmount.multiply(receivableRateNew).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                                studentPayHkdVo.setPayInfoHkd(receivableDiffAmount.multiply(receivableRateNew).setScale(2, BigDecimal.ROUND_HALF_UP).toString() + "港币（HKD)");

                            }
                            studentPayHkdVos.add(studentPayHkdVo);
                        }
                    }
                    studentReceivableAndPaySumVo.setStudentPayAmountHkdList(studentPayHkdVos);
                }
            }


//            //实付金额（hkd)
            if (GeneralTool.isNotEmpty(studentReceivableAndPaySumVo.getPayableActualAmountInfo())) {
                String[] split = studentReceivableAndPaySumVo.getPayableActualAmountInfo().split(",");
                List<String> list = Arrays.stream(split).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                if (GeneralTool.isNotEmpty(list)) {
                    List<StudentPayHkdVo> studentPayHkdVos = new ArrayList<>();
                    for (String s : list) {
                        String s1 = s.substring(0, s.indexOf("|")).trim();
                        String currencyType = s.substring(s.indexOf("（") + 1, s.indexOf("）"));
                        if (s1.matches("-?[0-9]+.?[0-9]*")) {
                            BigDecimal receivableDiffAmount = new BigDecimal(s1);
                            BigDecimal receivableRateNew = currencyRateMap.get(currencyType);
                            StudentPayHkdVo studentPayHkdVo = new StudentPayHkdVo();
                            studentPayHkdVo.setPayAmountInfo(receivableDiffAmount.setScale(2, BigDecimal.ROUND_HALF_UP) + currencyNamesNumsMap.get(currencyType));
                            studentPayHkdVo.setPayAmount(receivableDiffAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                            if (GeneralTool.isNotEmpty(receivableRateNew)) {
                                studentPayHkdVo.setPayAmountHkd(receivableDiffAmount.multiply(receivableRateNew).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                                studentPayHkdVo.setPayInfoHkd(receivableDiffAmount.multiply(receivableRateNew).setScale(2, BigDecimal.ROUND_HALF_UP).toString() + "港币（HKD)");
                            }
                            studentPayHkdVos.add(studentPayHkdVo);
                        }
                    }
                    studentReceivableAndPaySumVo.setStudentPayActualAmountHkdList(studentPayHkdVos);
                }
            }
//            //应付未付（hkd）
            if (GeneralTool.isNotEmpty(studentReceivableAndPaySumVo.getPayableDiffAmountInfo())) {
                String[] split = studentReceivableAndPaySumVo.getPayableDiffAmountInfo().split(",");
                List<String> list = Arrays.stream(split).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                if (GeneralTool.isNotEmpty(list)) {
                    List<StudentPayHkdVo> studentPayHkdVos = new ArrayList<>();
                    for (String s : list) {
                        String s1 = s.substring(0, s.indexOf("|")).trim();
                        String currencyType = s.substring(s.indexOf("（") + 1, s.indexOf("）"));
                        if (s1.matches("-?[0-9]+.?[0-9]*")) {
                            BigDecimal receivableDiffAmount = new BigDecimal(s1).abs();
                            BigDecimal receivableRateNew = currencyRateMap.get(currencyType);
                            StudentPayHkdVo studentPayHkdVo = new StudentPayHkdVo();
                            studentPayHkdVo.setPayAmountInfo(receivableDiffAmount.setScale(2, BigDecimal.ROUND_HALF_UP) + currencyNamesNumsMap.get(currencyType));
                            studentPayHkdVo.setPayAmount(receivableDiffAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                            if (GeneralTool.isNotEmpty(receivableRateNew)) {
                                studentPayHkdVo.setPayAmountHkd(receivableDiffAmount.multiply(receivableRateNew).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                                studentPayHkdVo.setPayInfoHkd(receivableDiffAmount.multiply(receivableRateNew).setScale(2, BigDecimal.ROUND_HALF_UP).toString() + "港币（HKD)");

                            }
                            studentPayHkdVos.add(studentPayHkdVo);
                        }
                    }
                    studentReceivableAndPaySumVo.setStudentPayDiffHkdList(studentPayHkdVos);
                }
            }
        }
    }

}
