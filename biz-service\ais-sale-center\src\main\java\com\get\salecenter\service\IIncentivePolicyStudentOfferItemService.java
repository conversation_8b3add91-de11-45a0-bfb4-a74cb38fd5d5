package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.SearchBean;
import com.get.salecenter.vo.IncentivePolicyStudentOfferItemVo;
import com.get.salecenter.entity.IncentivePolicyStudentOfferItem;
import com.get.salecenter.dto.IncentivePolicyAddOfferItemDto;
import com.get.salecenter.dto.IncentivePolicyStudentOfferItemDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-13
 */
public interface IIncentivePolicyStudentOfferItemService extends IService<IncentivePolicyStudentOfferItem> {

    List<IncentivePolicyStudentOfferItemVo> getPolicyStudentDatas(IncentivePolicyStudentOfferItemDto data, SearchBean<IncentivePolicyStudentOfferItemDto> page);

    Boolean updatePolicyStudentOfferItem(Long fkIncentivePolicyId);

    Boolean updatePolicyStudentOfferItem(Long id ,Long fkReceivablePlanId,Long fkPayablePlanId);

    String addDiyPolicyStudent(IncentivePolicyAddOfferItemDto incentivePolicyAddOfferItemDto);

    void deleteDiyPolicyStudent(List<Long> ids);

    void cancelDiyPolicyStudent(Long id);

    void cancelSettlePolicyStudent(Long fkIncentivePolicyId);

    void settleSelectPolicyStudent(Long fkIncentivePolicyId);

    Integer settleSelectPolicyStudentCount(Long fkIncentivePolicyId);

    void exportDataExcel(HttpServletResponse response, IncentivePolicyStudentOfferItemDto incentivePolicyStudentOfferItemDto);
}
