package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-18
 */
@Data
@TableName("r_payable_plan_settlement_flag")
@ApiModel(value="RPayablePlanSettlementFlag对象", description="")
public class RPayablePlanSettlementFlag extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "财务结算汇总批次号")
    private String numSettlementBatch;

    @ApiModelProperty(value = "应付计划类型，枚举：m_student_offer_item留学申请计划/m_student_insurance留学保险/m_student_accommodation留学住宿")
    private String fkTypeKey;

    @ApiModelProperty(value = "学生代理Id")
    private Long fkAgentId;

    @ApiModelProperty(value = "支付币种")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "帐号币种")
    private String fkCurrencyTypeNumAccount;

    @ApiModelProperty(value = "结算状态：0未结算/1结算中/2代理确认/3财务确认/4财务汇总")
    private Integer statusSettlement;

    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "创建用户(登录账号)")
    private String gmtCreateUser;

    @ApiModelProperty(value = "修改时间")
    private Date gmtModified;

    @ApiModelProperty(value = "修改用户(登录账号)")
    private String gmtModifiedUser;


}
