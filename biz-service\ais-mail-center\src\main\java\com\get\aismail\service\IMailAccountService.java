package com.get.aismail.service;

import com.get.aismail.entity.MMailAccount;
import com.get.aismail.vo.MailAccountVo;

import java.util.List;

public interface IMailAccountService {
    Long bindMail(MailAccountVo mailAccountVo) throws Exception;

    void deleteMail(String emailAccount) throws Exception;

    void updateMail(MailAccountVo mailAccountVo) throws Exception;

    List<MMailAccount> getAllMaillAccount() throws Exception;
}
