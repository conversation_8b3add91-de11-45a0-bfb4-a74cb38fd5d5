package com.get.aismail.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.aismail.dao.*;
import com.get.aismail.dto.MailBox;
import com.get.aismail.entity.*;
import com.get.aismail.service.IMailAccountService;
import com.get.aismail.utils.AesUtil;
import com.get.aismail.utils.MailBoxUtils;
import com.get.aismail.vo.MailAccountVo;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.StaffInfo;
import com.get.core.secure.utils.SecureUtil;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.get.core.tool.api.ResultCode.BAD_REQUEST;

@Service
public class MailAccountServiceImpl implements IMailAccountService {
    @Resource
    private MMailAccountMapper mailAccountMapper;

    @Resource
    private MMailAttachedMapper attachedMapper;

    @Resource
    private MMailMapper mailMapper;

    @Resource
    private MMailDorisMapper mailDorisMapper;

    @Resource
    private MMailSyncQueueMapper mMailSyncQueueMapper;

    @Resource
    private MMailSyncQueueDorisMapper mMailSyncQueueDorisMapper;

    @Resource
    private MMailLLMAnalysisMapper mMailLLMAnalysisMapper;

    @Resource
    private MailServiceImpl mailServiceImpl;

    @Resource
    private Environment env;

    @Override
    public Long bindMail(MailAccountVo mailAccountVo) throws Exception {
        try {
            String host = env.getProperty("mail." + mailAccountVo.getEmailType());
            MailBox mailBox = MailBoxUtils.login(mailAccountVo.getEmailAccount(), mailAccountVo.getEmailPassword(), host);
            mailBox.getStore().close();
        } catch (Exception e) {
            throw new GetServiceException(BAD_REQUEST, "mail connect failed");
        }
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        Long userId = staffInfo.getStaffId();
        LambdaQueryWrapper<MMailAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MMailAccount::getEmailAccount, mailAccountVo.getEmailAccount());
        List<MMailAccount> mailAccount1 = mailAccountMapper.selectList(queryWrapper);
        if (!mailAccount1.isEmpty()) {
            throw new GetServiceException(BAD_REQUEST, "mail account is exist");
        }
        if (mailAccountVo.isDefault()) {
            LambdaQueryWrapper<MMailAccount> queryWrapper1 = new LambdaQueryWrapper<>();
            queryWrapper1.eq(MMailAccount::getIsDefault, Boolean.TRUE);
            queryWrapper1.eq(MMailAccount::getFkPlatformUserId, userId);
            List<MMailAccount> accounts = mailAccountMapper.selectList(queryWrapper1);
            for (MMailAccount account : accounts) {
                account.setIsDefault(Boolean.FALSE);
                mailAccountMapper.updateById(account);
            }
        }
        MMailAccount mailAccount = new MMailAccount();
        mailAccount.setFkPlatformCode("AIS");
        mailAccount.setFkPlatformUserId(userId);
        mailAccount.setEmailAccount(mailAccountVo.getEmailAccount());
        // 给密码进行加密储存
        byte[] Key = Objects.requireNonNull(env.getProperty("encrypt.KEY")).getBytes();
        mailAccount.setEmailPassword(AesUtil.encrypt(mailAccountVo.getEmailPassword(), Key));
        mailAccount.setEmailType(mailAccountVo.getEmailType());
        mailAccount.setIsDefault(mailAccountVo.isDefault());
        mailAccount.setGmtCreateUser(staffInfo.getName());
        mailAccount.setGmtCreate(LocalDateTime.now());
        mailAccountMapper.insert(mailAccount);
//        mailServiceImpl.fetchMail(mailAccount);
        return mailAccount.getFkPlatformUserId();
    }

    @Override
    public void deleteMail(String emailAccount) throws Exception {
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        Long userId = staffInfo.getStaffId();
        LambdaQueryWrapper<MMailAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MMailAccount::getFkPlatformUserId, userId);
        List<MMailAccount> accountlist = mailAccountMapper.selectList(queryWrapper);
        for (MMailAccount account : accountlist) {
            if (account.getEmailAccount().equals(emailAccount)) {
                mailAccountMapper.deleteById(account.getId());

                // 删除对应的邮件内容
                LambdaQueryWrapper<MMail> mailQueryWrapper = new LambdaQueryWrapper<>();
                mailQueryWrapper.eq(MMail::getFkMailAccountId, account.getId());
                List<MMail> mailList = mailMapper.selectList(mailQueryWrapper);
                List<Long> mailIds = new ArrayList<>();
                for (MMail mail : mailList) {
                    mailIds.add(mail.getId());
                    mailMapper.deleteById(mail.getId());
                    // doris中同步执行删除操作
                    mailDorisMapper.deleteById(mail.getId());
                }

                // 删除附件此邮箱的所有内容
                LambdaQueryWrapper<MMailAttached> attachedQueryWrapper = new LambdaQueryWrapper<>();
                attachedQueryWrapper.eq(MMailAttached::getFkMailAccountId, account.getId());
                List<MMailAttached> attachedList = attachedMapper.selectList(attachedQueryWrapper);
                for (MMailAttached attached : attachedList) {
                    attachedMapper.deleteById(attached.getId());
                }

                // 删除邮箱的操作内容
                LambdaQueryWrapper<MMailSyncQueue> mMailSyncQueueLambdaQueryWrapper = new LambdaQueryWrapper<>();
                mMailSyncQueueLambdaQueryWrapper.eq(MMailSyncQueue::getFkMailAccountId, account.getId());
                List<MMailSyncQueue> mMailSyncQueueList = mMailSyncQueueMapper.selectList(mMailSyncQueueLambdaQueryWrapper);
                for (MMailSyncQueue mMailSyncQueue : mMailSyncQueueList) {
                    mMailSyncQueueMapper.deleteById(mMailSyncQueue.getId());
                    mMailSyncQueueDorisMapper.deleteById(mMailSyncQueue.getId());
                }

                // 删除邮箱的分析内容
                for (Long mailId : mailIds) {
                    LambdaQueryWrapper<MMailLLMAnalysis> mMailLLMAnalysisLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    mMailLLMAnalysisLambdaQueryWrapper.eq(MMailLLMAnalysis::getFkMailId, mailId);
                    List<MMailLLMAnalysis> mMailLLMAnalyses = mMailLLMAnalysisMapper.selectList(mMailLLMAnalysisLambdaQueryWrapper);
                    for (MMailLLMAnalysis mMailLLMAnalysis : mMailLLMAnalyses) {
                        mMailLLMAnalysisMapper.deleteById(mMailLLMAnalysis.getId());
                    }
                }

            }
        }
    }

    @Override
    public void updateMail(MailAccountVo mailAccountVo) throws Exception {
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        Long userId = staffInfo.getStaffId();
        LambdaQueryWrapper<MMailAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MMailAccount::getFkPlatformUserId, userId);
        List<MMailAccount> accountlist = mailAccountMapper.selectList(queryWrapper);
        for (MMailAccount account : accountlist) {
            if (mailAccountVo.isDefault() && !Objects.equals(account.getEmailAccount(), mailAccountVo.getEmailAccount()) && account.getIsDefault()) {
                account.setIsDefault(false);
                mailAccountMapper.updateById(account);
            }

            if (account.getEmailAccount().equals(mailAccountVo.getEmailAccount())) {
                if (!account.getEmailPassword().equals(mailAccountVo.getEmailPassword())) {
                    try {
                        String host = env.getProperty("mail." + mailAccountVo.getEmailType());
                        MailBoxUtils.login(mailAccountVo.getEmailAccount(), mailAccountVo.getEmailPassword(), host);
                    } catch (Exception e) {
                        throw new GetServiceException(BAD_REQUEST, "mail connect failed");
                    }
                    byte[] Key = Objects.requireNonNull(env.getProperty("encrypt.KEY")).getBytes();
                    account.setEmailPassword(AesUtil.encrypt(mailAccountVo.getEmailPassword(), Key));
                }
                account.setIsDefault(mailAccountVo.isDefault());
                account.setGmtModifiedUser(staffInfo.getName());
                account.setGmtModified(LocalDateTime.now());
                mailAccountMapper.updateById(account);
            }
        }
    }

    @Override
    public List<MMailAccount> getAllMaillAccount() throws Exception {
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        Long userId = staffInfo.getStaffId();
        LambdaQueryWrapper<MMailAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MMailAccount::getFkPlatformUserId, userId);
        return mailAccountMapper.selectList(queryWrapper);
    }


}
