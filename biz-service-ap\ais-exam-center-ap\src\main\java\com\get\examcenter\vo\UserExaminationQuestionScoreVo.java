package com.get.examcenter.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * Created by <PERSON>.
 * Time: 12:09
 * Date: 2021/8/27
 * Description:答题记录返回类
 */
@Data
@ApiModel("答题记录返回类")
public class UserExaminationQuestionScoreVo {

    /**
     * 考题类型
     */
    @ApiModelProperty(value = "考题类型")
    private String fkQuestionTypeName;

    /**
     * 考题编号
     */
    @ApiModelProperty(value = "考题编号")
    private String num;

    /**
     * 考题标题
     */
    @ApiModelProperty(value = "考题标题")
    private String question;

    /**
     * 选择答案
     */
    @ApiModelProperty(value = "选择答案")
    private String answer;

    /**
     * 获得分数
     */
    @ApiModelProperty(value = "获得分数")
    private Integer score;

    /**
     * 用时（秒）
     */
    @ApiModelProperty(value = "用时（秒）")
    private Integer useTime;
}
