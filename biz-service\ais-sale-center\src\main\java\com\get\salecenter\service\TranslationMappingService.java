package com.get.salecenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.salecenter.vo.TranslationMappingVo;
import com.get.salecenter.entity.TranslationMapping;
import com.get.salecenter.dto.TranslationDto;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-04
 */
public interface TranslationMappingService extends BaseService<TranslationMapping> {

    List<TranslationMappingVo> getTranslationMappingDtos(TranslationDto translationDto);

    void updateTranslations(List<TranslationDto> translationDtos);

    void deleteTranslations(String fkTableName, Long fkTableId);

    Map<String, Object> getConventionProcedureTranslation(Long fkTableId);
}
