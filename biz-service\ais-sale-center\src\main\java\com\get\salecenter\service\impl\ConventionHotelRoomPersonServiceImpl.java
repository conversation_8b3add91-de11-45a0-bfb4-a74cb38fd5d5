package com.get.salecenter.service.impl;

import cn.hutool.poi.excel.BigExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.CacheKeyConstants;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.cache.utils.CacheUtil;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.redis.cache.GetRedis;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.ConventionHotelMapper;
import com.get.salecenter.dao.sale.ConventionHotelRoomMapper;
import com.get.salecenter.dao.sale.ConventionHotelRoomPersonMapper;
import com.get.salecenter.dao.sale.ConventionPersonMapper;
import com.get.salecenter.dao.sale.ConventionRegistrationMapper;
import com.get.salecenter.dao.sale.StaffBdCodeMapper;
import com.get.salecenter.vo.*;
import com.get.salecenter.vo.ConventionHotelVo;
import com.get.salecenter.entity.ConventionHotel;
import com.get.salecenter.entity.ConventionHotelRoom;
import com.get.salecenter.entity.ConventionHotelRoomPerson;
import com.get.salecenter.entity.ConventionPerson;
import com.get.salecenter.entity.StaffBdCode;
import com.get.salecenter.service.IConventionHotelRoomPersonService;
import com.get.salecenter.service.IConventionHotelRoomService;
import com.get.salecenter.service.IConventionHotelService;
import com.get.salecenter.service.IConventionPersonService;
import com.get.salecenter.utils.MyCollectionUtils;
import com.get.salecenter.utils.MyDateUtils;
import com.get.salecenter.dto.ConventionHotelRoomDto;
import com.get.salecenter.dto.ConventionHotelRoomPersonDto;
import com.get.salecenter.dto.ConventionPersonDto;
import com.google.common.collect.Lists;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.nlpcn.commons.lang.util.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;
import java.util.stream.Collectors;

import static com.get.common.cache.CacheNames.STAFF_FOLLOWER_IDS_CACHE;

/**
 * @author: Sea
 * @create: 2020/8/24 10:34
 * @verison: 1.0
 * @description:
 */
@Service
public class ConventionHotelRoomPersonServiceImpl implements IConventionHotelRoomPersonService {
    @Resource
    private ConventionHotelRoomPersonMapper conventionHotelRoomPersonMapper;
    @Resource
    private IConventionPersonService conventionPersonService;
    @Resource
    private IConventionHotelRoomService conventionHotelRoomService;
    @Resource
    private IConventionHotelService conventionHotelService;
    @Resource
    private UtilService utilService;
    @Resource
    private ConventionPersonMapper conventionPersonMapper;
    @Resource
    private ConventionHotelRoomMapper conventionHotelRoomMapper;
    @Resource
    private ConventionHotelMapper conventionHotelMapper;
    @Resource
    private ConventionRegistrationMapper conventionRegistrationMapper;
    @Resource
    private StaffBdCodeMapper staffBdCodeMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private GetRedis getRedis;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void configurationBed(ConventionHotelRoomPersonDto conventionHotelRoomPersonDto) {
        if (GeneralTool.isEmpty(conventionHotelRoomPersonDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        conventionHotelRoomPersonDto.setKey(false);
        ConventionHotelRoomPerson conventionHotelRoomPerson = BeanCopyUtils.objClone(conventionHotelRoomPersonDto, ConventionHotelRoomPerson::new);
        /*
        判断是否勾选了相同需求安排到其他日期的房间
            不是：正常添加
            是：根据传入的酒店房型id  查出一共有几间房，房间床位数
                根据中间表 查看这几间房每一间房是否有对应的数据
                    判断每一间房对应数据量是否和床位数相等
                        是：该房间已经住满 跳过
                        不是：正常添加
         */
        if (!conventionHotelRoomPersonDto.getKey()) {
            //验证
            validateConfigurationBed(conventionHotelRoomPersonDto.getFkConventionPersonId(), conventionHotelRoomPersonDto.getFkConventionHotelRoomId());
            if (GeneralTool.isNotEmpty(conventionHotelRoomPersonDto.getFkConventionPersonId())) {
                volatileInsert(conventionHotelRoomPersonDto, conventionHotelRoomPersonDto.getFkConventionHotelRoomId());
            }
            utilService.updateUserInfoToEntity(conventionHotelRoomPerson);
            conventionHotelRoomPersonMapper.insertSelective(conventionHotelRoomPerson);
        } else {
            //根据酒店房型id 查出对应房间ids
            List<Long> roomIds = conventionHotelRoomService.getRoomIdsByHotelId(conventionHotelRoomPersonDto.getConventionHotelId(), conventionHotelRoomPersonDto.getSystemRoomNum());
            //根据酒店房型id 查出对应床位数
            ConventionHotelVo conventionHotelVo = conventionHotelService.findConventionHotelById(conventionHotelRoomPersonDto.getConventionHotelId());
            Integer bedCount = conventionHotelVo.getBedCount();
            for (Long roomId : roomIds) {
                //获取中间表数据
                List<ConventionHotelRoomPerson> conventionHotelRoomPersons = getConventionHotelRoomPersonsByRoomId(roomId);
                //验证
                validateConfigurationBed(conventionHotelRoomPersonDto.getFkConventionPersonId(), roomId);
                //中间表数据量小于床位数 表示没住满
                if (conventionHotelRoomPersons.size() < bedCount) {
                    if (GeneralTool.isNotEmpty(conventionHotelRoomPersonDto.getFkConventionPersonId())) {
                        volatileInsert(conventionHotelRoomPersonDto, roomId);
                    }
                    conventionHotelRoomPerson.setFkConventionHotelRoomId(roomId);
                    utilService.updateUserInfoToEntity(conventionHotelRoomPerson);
                    conventionHotelRoomPersonMapper.insertSelective(conventionHotelRoomPerson);
                }
            }
        }
    }

    /**
     * 验证参会人只能添加同一个房间
     *
     * @param conventionHotelRoomPersonDto
     * @param fkConventionHotelRoomId
     */
    private void volatileInsert(ConventionHotelRoomPersonDto conventionHotelRoomPersonDto, Long fkConventionHotelRoomId) {
        //不同日期可以 不同房间
        ConventionHotelRoom hotelRoom = conventionHotelRoomMapper.selectById(conventionHotelRoomPersonDto.getFkConventionHotelRoomId());
        Date stayDate = hotelRoom.getStayDate();
        Long fkConventionPersonId = conventionHotelRoomPersonDto.getFkConventionPersonId();
//        Example example = new Example(ConventionHotelRoomPerson.class);
//        example.createCriteria().andEqualTo("fkConventionPersonId", fkConventionPersonId);
//        List<ConventionHotelRoomPerson> conventionHotelRoomPeople = conventionHotelRoomPersonMapper.selectByExample(example);

        List<ConventionHotelRoomPerson> conventionHotelRoomPeople = conventionHotelRoomPersonMapper.selectList(Wrappers.<ConventionHotelRoomPerson>lambdaQuery().eq(ConventionHotelRoomPerson::getFkConventionPersonId, fkConventionPersonId));
        if (GeneralTool.isNotEmpty(conventionHotelRoomPeople)) {
            List<Long> roomIds = conventionHotelRoomPeople.stream().map(ConventionHotelRoomPerson::getFkConventionHotelRoomId).collect(Collectors.toList());
            List<ConventionHotelRoom> rooms = conventionHotelRoomMapper.getRoomByIds(roomIds);
            if (GeneralTool.isEmpty(roomIds)) {
                roomIds.add(0L);
            }
//            Example example2 = new Example(ConventionHotelRoom.class);
//            example2.createCriteria().andIn("id",roomIds);
//            List<ConventionHotelRoom> hotelRooms = conventionHotelRoomMapper.selectByExample(example2);
            List<ConventionHotelRoom> hotelRooms = conventionHotelRoomMapper.selectBatchIds(roomIds);
            List<Date> dateList = new ArrayList<>();
            if (GeneralTool.isNotEmpty(hotelRooms)) {
                dateList = hotelRooms.stream().map(ConventionHotelRoom::getStayDate).collect(Collectors.toList());
                if (!dateList.contains(stayDate)) {
                    return;
                }
            }
            if (GeneralTool.isNotEmpty(rooms)) {
                List<Long> conventionHotelRoomIds = new ArrayList<>();
                for (ConventionHotelRoom room : rooms) {
//                    Example example1 = new Example(ConventionHotelRoom.class);
//                    Example.Criteria criteria1 = example1.createCriteria()
//                            .andEqualTo("fkConventionHotelId", room.getFkConventionHotelId())
//                            .andEqualTo("systemRoomNum", room.getSystemRoomNum());
//                    if (GeneralTool.isNotEmpty(dateList)){
//                        criteria1.andNotIn("stayDate", dateList);
//                    }
//                    List<ConventionHotelRoom> conventionHotelRooms = conventionHotelRoomMapper.selectByExample(example1);

                    LambdaQueryWrapper<ConventionHotelRoom> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                    lambdaQueryWrapper.eq(ConventionHotelRoom::getFkConventionHotelId, room.getFkConventionHotelId());
                    lambdaQueryWrapper.eq(ConventionHotelRoom::getSystemRoomNum, room.getSystemRoomNum());
                    if (GeneralTool.isNotEmpty(dateList)) {
                        lambdaQueryWrapper.notIn(ConventionHotelRoom::getStayDate, dateList);
                    }
                    List<ConventionHotelRoom> conventionHotelRooms = conventionHotelRoomMapper.selectList(lambdaQueryWrapper);
                    for (ConventionHotelRoom conventionHotelRoom : conventionHotelRooms) {
                        conventionHotelRoomIds.add(conventionHotelRoom.getId());
                    }
                }
                if (!conventionHotelRoomIds.contains(fkConventionHotelRoomId)) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("person_has_configured_bed"));
                }
            }
        }
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        conventionHotelRoomPersonMapper.deleteById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(ConventionHotelRoomPersonDto conventionHotelRoomPersonDto) {
        if (conventionHotelRoomPersonDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        conventionHotelRoomPersonDto.setKey(false);
        ConventionHotelRoomPerson result = conventionHotelRoomPersonMapper.selectById(conventionHotelRoomPersonDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ConventionHotelRoomPerson conventionHotelRoomPerson = BeanCopyUtils.objClone(conventionHotelRoomPersonDto, ConventionHotelRoomPerson::new);
        ConventionHotelRoomPerson conventionHotelRoomPerson1 = BeanCopyUtils.objClone(conventionHotelRoomPersonDto, ConventionHotelRoomPerson::new);
        //0表示不安排到其他日期房间，则只修改这一条数据
        if (!conventionHotelRoomPersonDto.getKey()) {
            //验证
            validateConfigurationBed(conventionHotelRoomPersonDto.getFkConventionPersonId(), conventionHotelRoomPersonDto.getFkConventionHotelRoomId());
            if (GeneralTool.isNotEmpty(conventionHotelRoomPersonDto.getFkConventionPersonId())) {

                volatileInsert(conventionHotelRoomPersonDto, conventionHotelRoomPersonDto.getFkConventionHotelRoomId());
            }
            utilService.updateUserInfoToEntity(conventionHotelRoomPerson);
            conventionHotelRoomPersonMapper.updateById(conventionHotelRoomPerson);
        } else {
            //根据酒店房型id 查出对应房间ids
            List<Long> roomIds = conventionHotelRoomService.getRoomIdsByHotelId(conventionHotelRoomPersonDto.getConventionHotelId(), conventionHotelRoomPersonDto.getSystemRoomNum());
            //根据酒店房型id 查出对应床位数
            ConventionHotelVo conventionHotelVo = conventionHotelService.findConventionHotelById(conventionHotelRoomPersonDto.getConventionHotelId());
            Integer bedCount = conventionHotelVo.getBedCount();
            for (Long roomId : roomIds) {
                //获取中间表数据
                List<ConventionHotelRoomPerson> conventionHotelRoomPersons = getConventionHotelRoomPersonsByRoomId(roomId);
                //验证
                validateConfigurationBed(conventionHotelRoomPersonDto.getFkConventionPersonId(), roomId);
                //中间表数据量小于床位数 表示没住满 && 要修改的这一条数据不需要新增
                if (conventionHotelRoomPersons.size() < bedCount && !roomId.equals(conventionHotelRoomPersonDto.getFkConventionHotelRoomId())) {
                    conventionHotelRoomPerson.setId(null);
                    conventionHotelRoomPerson.setFkConventionHotelRoomId(roomId);
                    if (GeneralTool.isNotEmpty(conventionHotelRoomPersonDto.getFkConventionPersonId())) {
                        volatileInsert(conventionHotelRoomPersonDto, conventionHotelRoomPersonDto.getFkConventionHotelRoomId());
                    }
                    utilService.updateUserInfoToEntity(conventionHotelRoomPerson);
                    conventionHotelRoomPersonMapper.insertSelective(conventionHotelRoomPerson);
                }
            }
            if (GeneralTool.isNotEmpty(conventionHotelRoomPersonDto.getFkConventionPersonId())) {
                //安排到其他日期房间，和新增不一样在于  先添加其他日期 在修改这一条数据
                volatileInsert(conventionHotelRoomPersonDto, conventionHotelRoomPersonDto.getFkConventionHotelRoomId());
            }
            utilService.updateUserInfoToEntity(conventionHotelRoomPerson);
            conventionHotelRoomPersonMapper.updateById(conventionHotelRoomPerson1);
        }
    }

    @Override
    public ConventionHotelRoomPersonVo findconventionHotelRoomPersonById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ConventionHotelRoomPerson conventionHotelRoomPerson = conventionHotelRoomPersonMapper.selectById(id);
        return BeanCopyUtils.objClone(conventionHotelRoomPerson, ConventionHotelRoomPersonVo::new);
    }

    @Override
    public List<ConventionHotelRoomListVo> getHotelRoomsAndPersons(ConventionHotelRoomDto conventionHotelRoomDto1) {
        //定义
        List<ConventionHotelRoomVo> convertDatas = new ArrayList<>();
        ConventionPersonVo conventionPersonVo;
        //查询条件roomNum不为空时，加上模糊查询需要得%
        if (GeneralTool.isNotEmpty(conventionHotelRoomDto1.getRoomNum())) {
            conventionHotelRoomDto1.setRoomNum("%" + conventionHotelRoomDto1.getRoomNum() + "%");
        }

        List<String> bdCodes = Lists.newArrayList();
        if (GeneralTool.isNotEmpty(conventionHotelRoomDto1.getBdCodeKey())&& conventionHotelRoomDto1.getBdCodeKey()){
            Long staffId = SecureUtil.getStaffId();
            List<Long> staffFollowerIds = Lists.newArrayList();
            List<Long> followerIds = CacheUtil.get(
                    STAFF_FOLLOWER_IDS_CACHE,
                    "staffId:",
                    staffId,
                    ()->permissionCenterClient.getStaffFollowerIds(staffId).getData()
            );
            if (GeneralTool.isNotEmpty(followerIds)) {
                staffFollowerIds.addAll(followerIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            }
            staffFollowerIds.add(staffId);
            bdCodes = getBdCodeByStaffIds(staffFollowerIds);
        }

        //获取峰会下所有参会人
        List<ConventionPersonVo> conventionPersonVos = conventionPersonService.findConventionPersonNameAndSexByConventionId(conventionHotelRoomDto1.getConventionId(),bdCodes);
        //获取峰会下所有房间
        List<ConventionHotelRoomPersonVo> conventionHotelRoomPersonVos = conventionHotelRoomPersonMapper.getConventionHotelRoomPersonDtos(conventionHotelRoomDto1.getConventionId());
        //根据峰会id 查出该峰会下的所有房型id
        List<Long> conventionHotelIds = conventionHotelService.getConventionHotelIds(conventionHotelRoomDto1.getConventionId());
        //根据房型ids 查出分类后所有该房型id开得房间信息(可按条件查找)
        List<ConventionHotelRoomVo> conventionHotelRoomVoList = conventionHotelRoomService.getConventionHotelRoomDtoList(conventionHotelIds, conventionHotelRoomDto1,bdCodes,SecureUtil.getFkCompanyId());
        //获取峰会下所有房间id，日期安排，系统房号
        List<ConventionHotelRoomVo> conventionHotelRoomVos = conventionHotelRoomPersonMapper.getRoomIdAndDate(conventionHotelRoomDto1.getConventionId());
        //根据房型ids 查出该房型所开房间入住日期有哪些
        List<String> dates = conventionHotelRoomService.getDates(conventionHotelIds);
//        List<ConventionRegistrationVo> conventionRegistrationDtos = conventionRegistrationMapper.getConventionRegistrationsByConventionId(conventionHotelRoomDto1.getConventionId());
//        conventionRegistrationDtos.removeIf(Objects::isNull);
        Map<Long, String> providerNameMap = conventionPersonVos.stream().collect(Collectors.toMap(
                //TODO 改过
//                conventionPersonVo -> Optional.ofNullable(conventionPersonVo).map(ConventionPersonVo::getId).orElse(0L),
//                conventionPersonVo -> Optional.ofNullable(conventionPersonVo).map(ConventionPersonVo::getCompany).orElse(""),
                conventionPerson -> Optional.ofNullable(conventionPerson).map(ConventionPersonVo::getId).orElse(0L),
                conventionPerson -> Optional.ofNullable(conventionPerson).map(ConventionPersonVo::getCompany).orElse(""),
                (key1, key2) -> key2));
        // bdcode过滤用
        Map<Long, String> providerBdCodeMap = conventionPersonVos.stream().collect(Collectors.toMap(
                conventionPersonDto1 -> Optional.ofNullable(conventionPersonDto1).map(ConventionPersonVo::getId).orElse(0L),
                conventionPersonDto1 -> Optional.ofNullable(conventionPersonDto1).map(ConventionPersonVo::getBdCode).orElse(""),
                (key1, key2) -> key2));
        for (ConventionHotelRoomVo conventionHotelRoomVo : conventionHotelRoomVoList) {
            //获取该房间床位数
            Integer bedCount = conventionHotelRoomVo.getBedCount();
            //定义
            TreeMap<String, List<ConventionPersonListVo>> map = new TreeMap<>();
//            List<ConventionHotelRoomVo> conventionHotelRoomVos = conventionHotelRoomPersonMapper.getRoomIdAndDate(conventionHotelRoomDto1.getConventionId());
            List<String> systemRoomNumList = conventionHotelRoomVos.stream().map(ConventionHotelRoomVo::getSystemRoomNum).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            //建立系统房号和房间id的关系  房间id和日期有个映射  日期不同，id不同
            List<Map<String, Map<Date, Long>>> systemRoomNumMapList = new ArrayList<>();
            int n = systemRoomNumList.size();
            for (int i = 0; i < n; i++) {
                int finalI = i;
                //TODO 改过
                //List<ConventionHotelRoomVo> dtoList = conventionHotelRoomVos.stream().filter(conventionHotelRoomVo -> conventionHotelRoomVo.getSystemRoomNum().equals(systemRoomNumList.get(finalI))).collect(Collectors.toList());
                List<ConventionHotelRoomVo> dtoList = conventionHotelRoomVos.stream().filter(conventionHotelRoomVo1 -> conventionHotelRoomVo1.getSystemRoomNum().equals(systemRoomNumList.get(finalI))).collect(Collectors.toList());
                Map<Date, Long> roomMap = dtoList.stream().collect(Collectors.toMap(ConventionHotelRoomVo::getStayDate, ConventionHotelRoomVo::getId));
                Map<String, Map<Date, Long>> systemRoomNumMap = new HashMap<>();
                systemRoomNumMap.put(systemRoomNumList.get(i), roomMap);
                systemRoomNumMapList.add(systemRoomNumMap);
            }
            //遍历日期  获取指定的房间id
            for (String date : dates) {
                //定义
                List<ConventionPersonListVo> list = new ArrayList<>();
                //系统编号和日期确定房间id
//                Long roomId = conventionHotelRoomPersonMapper.getRommId(conventionHotelRoomVo.getSystemRoomNum(), date, conventionHotelRoomDto1.getConventionId());
                //查找中间表  该房间是否被被安排过
                List<ConventionHotelRoomPersonVo> conventionHotelRoomPersonVoList = new ArrayList<>();
                Map<Date, Long> roomDateMap = new HashMap<>();
                for (Map<String, Map<Date, Long>> stringMapMap : systemRoomNumMapList) {
                    for (String s : stringMapMap.keySet()) {
                        if (s.equals(conventionHotelRoomVo.getSystemRoomNum())) {
                            //根据系统房号  找到了房间id 日期映射map
                            roomDateMap = stringMapMap.get(conventionHotelRoomVo.getSystemRoomNum());
                        }
                    }
                }
                Date date1 = new Date();
                try {
                    SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
                    date1 = sf.parse(date);
                } catch (Exception e) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("DATE_FORMAT_CONVERSION_ERROR"));
                }
                //根据日期最终确定到房间的id
                Long roomId = roomDateMap.get(date1);
                if (GeneralTool.isNotEmpty(roomId)) {
                    //过滤出房间id为当前 房间id的数据
                    conventionHotelRoomPersonVoList = conventionHotelRoomPersonVos.stream().filter(conventionHotelRoomPersonDto -> conventionHotelRoomPersonDto.getFkConventionHotelRoomId().equals(roomId)).collect(Collectors.toList());
//                            conventionHotelRoomPersonMapper.getConventionHotelRoomPersonDtoList(roomId);
                }
//                Set<Long> personIds = conventionHotelRoomPersonVoList.stream().filter(Objects::nonNull).map(ConventionHotelRoomPersonVo::getFkConventionPersonId).collect(Collectors.toSet());
                Map<Long, String> nameMap = conventionPersonVos.stream().collect(Collectors.toMap(ConventionPersonVo::getId, ConventionPersonVo::getName));
                Map<Long, String> nameChnMap = conventionPersonVos.stream().collect(Collectors.toMap(ConventionPersonVo::getId, ConventionPersonVo::getNameChn));
                Map<Long, Boolean> isAttendMap = conventionPersonVos.stream().collect(Collectors.toMap(ConventionPersonVo::getId,
                       //TODO 改过
                       //conventionPersonVo -> Optional.ofNullable(conventionPersonVo).map(ConventionPersonVo::getIsAttend).orElse(false)));
                        conventionPerson -> Optional.ofNullable(conventionPerson).map(ConventionPersonVo::getIsAttend).orElse(false)));
                Map<Long, Integer> genderMap = conventionPersonVos.stream().filter(conventionPersonDto2 -> GeneralTool.isNotEmpty(conventionPersonDto2.getGender())).collect(Collectors.toMap(ConventionPersonVo::getId, ConventionPersonVo::getGender));
                Map<Long, Integer> typeMap = conventionPersonVos.stream().collect(Collectors.toMap(ConventionPersonVo::getId, ConventionPersonVo::getType));
                //有则遍历数据
                for (ConventionHotelRoomPersonVo conventionHotelRoomPersonVo : conventionHotelRoomPersonVoList) {
                    //当为锁定状态时，人员id为空，不用查找具体信息
                    if (GeneralTool.isEmpty(conventionHotelRoomPersonVo.getFkConventionPersonId())) {
                        conventionPersonVo = new ConventionPersonVo();
                        conventionPersonVo.setName("[系统] 锁定床位");
                        conventionPersonVo.setNameChn("[系统] 锁定床位");
                        conventionPersonVo.setConventionHotelRoomPersonId(conventionHotelRoomPersonVo.getId());
                    } else {
                        //不为空  查找人员具体信息 这里在mapper里多添加了返回参会人员类型type，用于过滤查询
                        conventionPersonVo = new ConventionPersonVo();
                        conventionPersonVo.setName(nameMap.get(conventionHotelRoomPersonVo.getFkConventionPersonId()));
                        conventionPersonVo.setNameChn(nameChnMap.get(conventionHotelRoomPersonVo.getFkConventionPersonId()));
                        conventionPersonVo.setConventionHotelRoomPersonId(conventionHotelRoomPersonVo.getId());
                        conventionPersonVo.setIsAttend(isAttendMap.get(conventionHotelRoomPersonVo.getFkConventionPersonId()));
                        if (GeneralTool.isNotEmpty(genderMap)) {
                            conventionPersonVo.setGender(genderMap.get(conventionHotelRoomPersonVo.getFkConventionPersonId()));
                        }
                        conventionPersonVo.setType(typeMap.get(conventionHotelRoomPersonVo.getFkConventionPersonId()));
                        conventionPersonVo.setId(conventionHotelRoomPersonVo.getFkConventionPersonId());
//                        conventionPersonVo = conventionPersonService.findConventionPersonNameAndSex(conventionHotelRoomPersonVo.getFkConventionPersonId());
                    }
                    //设置中间表id(修改床位配置 删除床位配置得时候用到)
                    if (GeneralTool.isNotEmpty(conventionPersonVo.getId())) {
                        conventionPersonVo.setConventionHotelRoomPersonId(conventionHotelRoomPersonVo.getId());
                    }
                    //设置房间id(床位配置得时候用到)
                    conventionPersonVo.setConventionHotelRoomId(conventionHotelRoomPersonVo.getFkConventionHotelRoomId());
                    //设置机构名
                    if (GeneralTool.isNotEmpty(conventionPersonVo.getId())) {
                        if (GeneralTool.isNotEmpty(providerNameMap.get(conventionPersonVo.getId()))) {
                            conventionPersonVo.setInstitutionName(providerNameMap.get(conventionPersonVo.getId()));
                        }
                        if (GeneralTool.isNotEmpty(providerBdCodeMap.get(conventionPersonVo.getId()))) {
                            conventionPersonVo.setBdCode(providerBdCodeMap.get(conventionPersonVo.getId()));
                        }
                    }
                    list.add(BeanCopyUtils.objClone(conventionPersonVo, ConventionPersonListVo::new));
                }
                if (GeneralTool.isNotEmpty(list)){
                    list = list.stream().sorted(Comparator.comparing(ConventionPersonListVo::getName,Comparator.nullsLast(String::compareTo))
                        .thenComparing(ConventionPersonListVo::getNameChn,Comparator.nullsLast(String::compareTo)))
                        .collect(Collectors.toList());
                }

                //中间表数据长度 < 床位数  表示处于 待配置状态
                int size = conventionHotelRoomPersonVoList.size();
                //房间不为空，设置房间id
                if (GeneralTool.isNotEmpty(roomId)) {
                    if (size < bedCount) {
                        for (int i = 1; i <= bedCount - size; i++) {
                            conventionPersonVo = new ConventionPersonVo();
                            conventionPersonVo.setConventionHotelRoomId(roomId);
                            list.add(BeanCopyUtils.objClone(conventionPersonVo, ConventionPersonListVo::new));
                        }

                    }
                } else {
                    //为空 表示这一天没有这间房
                    list.add(null);
                }
                map.put(date, list);
            }
            Long isFull = conventionHotelRoomDto1.getIsFull();
            //条件筛选-已满/未满
            setConventionPersonMap(conventionHotelRoomVo, map, isFull);
            //已满未满时，只返回map中有值的
            if (GeneralTool.isNotEmpty(conventionHotelRoomVo.getConventionPersonMap())) {
                convertDatas.add(conventionHotelRoomVo);
            }
        }
        //根据人员名称过滤
        if (GeneralTool.isNotEmpty(conventionHotelRoomDto1.getPersonName())) {
            filterConvertDatasByName(conventionHotelRoomDto1, convertDatas);
        }
        //根据人员类型过滤
        if (GeneralTool.isNotEmpty(conventionHotelRoomDto1.getPersonType())) {
            //过滤参会人员类型
            filterConvertDatasByPersonType(conventionHotelRoomDto1, convertDatas);
        }
        //根据跟进bd 过滤
        if (GeneralTool.isNotEmpty(conventionHotelRoomDto1.getBdCodes())) {
            filterConvertDatasByBdCode(conventionHotelRoomDto1, convertDatas);
        }
        return convertDatas.stream().map(conventionHotelRoomDto -> BeanCopyUtils.objClone(conventionHotelRoomDto, ConventionHotelRoomListVo::new)).collect(Collectors.toList());
    }


    @Override
    public void exportConventionHotelRoomPersonExcel(HttpServletResponse response, ConventionHotelRoomDto conventionHotelRoomDto){
        List<ConventionHotelRoomListVo> dataList =  getHotelRoomsAndPersons(conventionHotelRoomDto);
        List<String> dateList = getDates(conventionHotelRoomDto.getConventionId());
        //最大列数
        Integer maxRow = dateList.size()+4;
        //大文件导出
        BigExcelWriter writer = FileUtils.setExcelStyleWrapText("ConventionHotelRoomPerson", maxRow);
        List<String> titleList = new ArrayList<>(Arrays.asList("系统房号","签到"));
        titleList.addAll(dateList);
        titleList.addAll(new ArrayList<>(Arrays.asList("酒店","房型")));
        List<List<String>> contentRowList = new ArrayList<>();
        //数据内容
        for(ConventionHotelRoomListVo dto : dataList){
            List<String> contentList = new ArrayList<>();
            contentList.add(dto.getSystemRoomNum());
            TreeMap<String, List<ConventionPersonListVo>> map = dto.getConventionPersonMap();
            Set<String> personSet = new HashSet<>();
            List<String> bedLsit = new ArrayList<>();
            for (Map.Entry<String, List<ConventionPersonListVo>> entry : map.entrySet()) {
                List<ConventionPersonListVo> conventionPersonList= entry.getValue();
                conventionPersonList.forEach(m->{
                    if(GeneralTool.isNotEmpty(m)){
                        StringBuilder psersonBuf = new StringBuilder();
                        if(GeneralTool.isNotEmpty(m.getName())){
                            psersonBuf.append(m.getName());
                        }
                        if(GeneralTool.isNotEmpty(m.getNameChn())){
                            psersonBuf.append("（")
                                    .append(m.getNameChn()).append("）");
                        }
                        personSet.add(psersonBuf.toString());
                    }

                });
                StringBuilder bedBuf = new StringBuilder("");
                List<ConventionPersonListVo> collect = conventionPersonList.stream().filter(Objects::nonNull).filter(f -> StringUtil.isNotBlank(f.getNameChn()) ||  StringUtil.isNotBlank(f.getInstitutionName()) || f.getGender() != null).collect(Collectors.toList());
                for(ConventionPersonListVo cpdto:collect) {
                   if(GeneralTool.isNotEmpty(cpdto)){
                       bedBuf.append("【");
                       if (GeneralTool.isNotEmpty(cpdto.getNameChn())){
                           bedBuf.append(cpdto.getNameChn()).append("，");
                       }
                       if (GeneralTool.isNotEmpty(cpdto.getGender())){
                           bedBuf.append(cpdto.getGender()==0 ?"女":"男").append("，");
                       }
                       if (GeneralTool.isNotEmpty(cpdto.getInstitutionName())){
                           bedBuf.append(cpdto.getInstitutionName());
                       }
                       String s = bedBuf.toString();
                       if (s.endsWith("，")) {
                           bedBuf.delete(s.length()-1,s.length());
                       }
                       bedBuf.append("】").append("\r\n");
                   }
               }
                bedLsit.add(bedBuf.toString());
            }
            StringBuilder stringBuffer = new StringBuilder();
            ArrayList<String> personList = new ArrayList<>(personSet);
            for(int i = 0 ; i < personList.size() ; i++){
                stringBuffer.append(personList.get(i));
                if(i < personList.size()-1){
                    stringBuffer.append(",");
                }
            }
            contentList.add(stringBuffer.toString());
            contentList.addAll(bedLsit);
            contentList.add(dto.getHotel());
            contentList.add(dto.getRoomType());
            contentRowList.add(contentList);
        }

        List<List<String>> rows = new ArrayList<>();
        rows.add(titleList);
        rows.addAll(contentRowList);
        writer.write(rows,true);
        for(int x = 0 ; x < maxRow ; x++){

            //列宽
            if(x > 2 && x < maxRow - 2){
                writer.setColumnWidth(x, 70);
            }
            for(int y = 0 ; y < rows.size() ; y++){
                setCellStyle(writer,x,y);
            }
            //标题样式
            setTitleStyle(writer,x,0);
        }
        FileUtils.doExportExcel(response, writer, "ConventionHotelRoomPerson");

    }
    /**
     * 标题样式
     * @param writer
     * @param x
     * @param y
     */
    public void setTitleStyle(BigExcelWriter writer, int x, int y){
        Workbook workbook = writer.getWorkbook();
        CellStyle cellStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        cellStyle .setFont(font);
        // 字体垂直居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //字体水平居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        //自动换行
        cellStyle.setWrapText(true);
        setBorder(cellStyle);
        writer.setStyle(cellStyle,x,y);
    }

    /**
     * 内容样式设置
     * @param writer
     * @param x 列数，从 0 开始
     * @param y 行数，从 0 开始
     */
    public void setCellStyle(BigExcelWriter writer, int x, int y) {
        Workbook workbook = writer.getWorkbook();
        CellStyle cellStyle = workbook.createCellStyle();
        // 字体垂直居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //字体水平居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        //自动换行
        cellStyle.setWrapText(true);
        setBorder(cellStyle);
        writer.setStyle(cellStyle, x, y);
    }
    /**
     * 边框设置
     * @param cellStyle
     */
    public void setBorder(CellStyle cellStyle){
        //边框设置
        // 顶边栏
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        // 右边栏
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
        // 底边栏
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        // 左边栏
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
    }


    @Override
    public List<String> getDates(Long conventionId) {
        //根据峰会id 查出该峰会下的所有房型id
        List<Long> conventionHotelIds = conventionHotelService.getConventionHotelIds(conventionId);
        return conventionHotelRoomService.getDates(conventionHotelIds);
    }

    @Override
    public Integer getArrangedBedCount(List<Long> roomIds,List<Integer> types) {
        if (GeneralTool.isEmpty(roomIds)) {
            roomIds.add(0L);
        }
        return conventionHotelRoomPersonMapper.getArrangedBedCount(roomIds,types);
    }

    @Override
    public Integer getUsedRoomCount(List<Long> roomIds,List<Integer> types) {
        if (GeneralTool.isEmpty(roomIds)) {
            roomIds.add(0L);
        }
        return conventionHotelRoomPersonMapper.getUsedRoomCount(roomIds,types);
    }

    /**
     * 批量移除住房人员
     *
     * @param conventionPersonId
     * @param conventionHotelId
     * @param systemRoomNum
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchRemovePerson(Long conventionPersonId, Long conventionHotelId, String systemRoomNum) {
//        Example example = new Example(ConventionHotelRoom.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionHotelId", conventionHotelId);
//        criteria.andEqualTo("systemRoomNum", systemRoomNum);
//        List<ConventionHotelRoom> conventionHotelRooms = conventionHotelRoomMapper.selectByExample(example);

        List<ConventionHotelRoom> conventionHotelRooms = conventionHotelRoomMapper.selectList(Wrappers.<ConventionHotelRoom>lambdaQuery()
                .eq(ConventionHotelRoom::getFkConventionHotelId, conventionHotelId)
                .eq(ConventionHotelRoom::getSystemRoomNum, systemRoomNum));
        if (GeneralTool.isEmpty(conventionHotelRooms)) {
            return;
        }
        List<Long> conventionHotelRoomIds = conventionHotelRooms.stream().map(ConventionHotelRoom::getId).collect(Collectors.toList());
        for (Long conventionHotelRoomId : conventionHotelRoomIds) {
//            Example example1 = new Example(ConventionHotelRoomPerson.class);
//            Example.Criteria criteria1 = example1.createCriteria();
//            criteria1.andEqualTo("fkConventionHotelRoomId", conventionHotelRoomId);
//            criteria1.andEqualTo("fkConventionPersonId", conventionPersonId);
//            conventionHotelRoomPersonMapper.deleteByExample(example1);
            conventionHotelRoomPersonMapper.delete(Wrappers.<ConventionHotelRoomPerson>lambdaQuery()
                    .eq(ConventionHotelRoomPerson::getFkConventionHotelRoomId, conventionHotelRoomId)
                    .eq(ConventionHotelRoomPerson::getFkConventionPersonId, conventionPersonId));

        }
    }

    /**
     * 参会人签到
     *
     * @param conventionHotelRoomPersonDtoList
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void getPersonSign(List<ConventionHotelRoomPersonDto> conventionHotelRoomPersonDtoList) {
        if (GeneralTool.isEmpty(conventionHotelRoomPersonDtoList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        boolean flag = true;
        for (ConventionHotelRoomPersonDto hotelRoomPersonVo : conventionHotelRoomPersonDtoList) {
            if (GeneralTool.isNotEmpty(hotelRoomPersonVo)) {
                flag = false;
                break;
            }
        }
        //可直接签到
        if (flag) {
            if (GeneralTool.isEmpty(conventionHotelRoomPersonDtoList.get(0).getFkConventionPersonId()) || GeneralTool.isEmpty(conventionHotelRoomPersonDtoList.get(0).getIsAttend())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
            }
            conventionPersonService.setSign(conventionHotelRoomPersonDtoList.get(0).getFkConventionPersonId(), conventionHotelRoomPersonDtoList.get(0).getIsAttend());
            return;
        }
        for (ConventionHotelRoomPersonDto conventionHotelRoomPersonDto : conventionHotelRoomPersonDtoList) {
            //空房
            if (conventionHotelRoomPersonDto.getHotelRoomNum() == null) {
                continue;
            }
            //判断参数
            if (GeneralTool.isEmpty(conventionHotelRoomPersonDto.getConventionHotelId()) || GeneralTool.isEmpty(conventionHotelRoomPersonDto.getFkConventionPersonId())
                    || GeneralTool.isEmpty(conventionHotelRoomPersonDto.getSystemRoomNum()) || GeneralTool.isEmpty(conventionHotelRoomPersonDto.getIsAttend())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
            }
            ConventionHotelRoomDto conventionHotelRoomDto = new ConventionHotelRoomDto();
            conventionHotelRoomDto.setFkConventionHotelId(conventionHotelRoomPersonDto.getConventionHotelId());
            conventionHotelRoomDto.setSystemRoomNum(conventionHotelRoomPersonDto.getSystemRoomNum());

            conventionHotelRoomDto.setHotelRoomNum(conventionHotelRoomPersonDto.getHotelRoomNum());
            try {
                conventionHotelRoomService.updateHotelRoomNumLimited(conventionHotelRoomDto);
            } catch (Exception e) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("hotel_room_num_has_been_updated"));
            }
        }
        conventionPersonService.setSign(conventionHotelRoomPersonDtoList.get(0).getFkConventionPersonId(), conventionHotelRoomPersonDtoList.get(0).getIsAttend());
    }

    @Override
    public List<LiveDateVo> getUsableDateList(ConventionHotelRoomDto conventionHotelRoomDto) {
        if (GeneralTool.isEmpty(conventionHotelRoomDto.getFkConventionHotelId()) && GeneralTool.isEmpty(conventionHotelRoomDto.getSystemRoomNum())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
//        Example example = new Example(ConventionHotelRoom.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionHotelId", conventionHotelRoomDto.getFkConventionHotelId());
//        criteria.andEqualTo("systemRoomNum", conventionHotelRoomDto.getSystemRoomNum());
//        example.orderBy("stayDate").asc();
//        List<ConventionHotelRoom> conventionHotelRooms = conventionHotelRoomMapper.selectByExample(example);

        List<ConventionHotelRoom> conventionHotelRooms = conventionHotelRoomMapper.selectList(Wrappers.<ConventionHotelRoom>lambdaQuery()
                .eq(ConventionHotelRoom::getFkConventionHotelId, conventionHotelRoomDto.getFkConventionHotelId())
                .eq(ConventionHotelRoom::getSystemRoomNum, conventionHotelRoomDto.getSystemRoomNum())
                .orderByAsc(ConventionHotelRoom::getStayDate));
        ConventionHotel conventionHotel = conventionHotelMapper.selectById(conventionHotelRoomDto.getFkConventionHotelId());
        Integer bedCount = conventionHotel.getBedCount();
        conventionHotelRoomDto.setBedCount(bedCount);
        Map<Long, Date> dateMap = conventionHotelRooms.stream().collect(Collectors.toMap(ConventionHotelRoom::getId, ConventionHotelRoom::getStayDate));
        List<LiveDateVo> liveDateVos = new ArrayList<>();
        for (Map.Entry<Long, Date> entry : dateMap.entrySet()) {
            LiveDateVo liveDateVo = new LiveDateVo();
            liveDateVo.setDate(entry.getValue());
            Integer personCount = conventionHotelRoomPersonMapper.getPersonCount(entry.getKey());
            liveDateVo.setFkConventionHotelRoomId(entry.getKey());
            if (personCount < bedCount) {
                liveDateVo.setUsable(true);
            } else {
                liveDateVo.setUsable(false);
            }
            liveDateVos.add(liveDateVo);
        }
//        if (GeneralTool.isNotEmpty(conventionHotelRoomDto.getFkConventionPersonId())) {
        for (LiveDateVo liveDateVo : liveDateVos) {
            if (liveDateVo.getUsable()) {
//                Example example1 = new Example(ConventionHotelRoomPerson.class);
//                Example.Criteria criteria1 = example1.createCriteria();
//                criteria1.andEqualTo("fkConventionHotelRoomId", liveDateVo.getFkConventionHotelRoomId());
//                if (GeneralTool.isNotEmpty(conventionHotelRoomDto.getFkConventionPersonId())) {
//                    criteria1.andEqualTo("fkConventionPersonId", conventionHotelRoomDto.getFkConventionPersonId());
//                }
//                List<ConventionHotelRoomPerson> conventionHotelRoomPeople = conventionHotelRoomPersonMapper.selectByExample(example1);

                LambdaQueryWrapper<ConventionHotelRoomPerson> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(ConventionHotelRoomPerson::getFkConventionHotelRoomId, liveDateVo.getFkConventionHotelRoomId());
                if (GeneralTool.isNotEmpty(conventionHotelRoomDto.getFkConventionPersonId())) {
                    lambdaQueryWrapper.eq(ConventionHotelRoomPerson::getFkConventionPersonId, conventionHotelRoomDto.getFkConventionPersonId());
                }else {
                    lambdaQueryWrapper.isNull(ConventionHotelRoomPerson::getFkConventionPersonId);
                }
                List<ConventionHotelRoomPerson> conventionHotelRoomPeople = conventionHotelRoomPersonMapper.selectList(lambdaQueryWrapper);
                if (GeneralTool.isNotEmpty(conventionHotelRoomPeople)) {
                    liveDateVo.setUsable(false);
                }
            }
        }
//        }
        return liveDateVos.stream().sorted(Comparator.comparing(LiveDateVo::getDate)).collect(Collectors.toList());

    }

    @Override
    public List<ConventionPersonVo> getSignInfo(Long conventionPersonId) {
        List<ConventionHotelRoomPersonVo> conventionHotelRoomPersonVoList = conventionHotelRoomPersonMapper.getConventionHotelRoomPersonInfo(conventionPersonId);
        if (GeneralTool.isEmpty(conventionHotelRoomPersonVoList)) {
            return Collections.emptyList();
        }
        ConventionPerson conventionPerson = conventionPersonMapper.selectById(conventionPersonId);

        List<String> systemNumList = conventionHotelRoomPersonVoList.stream().map(ConventionHotelRoomPersonVo::getSystemRoomNum).distinct().collect(Collectors.toList());
        int systemNumListSize = systemNumList.size();

        List<ConventionPersonVo> conventionPersonVoList = new ArrayList<>(systemNumListSize);

        for (int i = 0; i < systemNumListSize; i++) {
            //同一系统房号所有日期房间
            List<ConventionHotelRoomPersonVo> conventionHotelRoomPersonVos = new ArrayList<>();
            List<Date> dateList = new ArrayList<>();
            Map<Long, TreeSet<Date>> map = new HashMap<>();
            Long conventionHotelId = null;
            String systemRoomNum = "";
            String roomType = "";
            String hotelRoomNum = "";
            String hotelName = "";
            for (ConventionHotelRoomPersonVo conventionHotelRoomPersonVo : conventionHotelRoomPersonVoList) {
                if (conventionHotelRoomPersonVo.getSystemRoomNum().equals(systemNumList.get(i))) {
                    if ("".equals(systemRoomNum) && "".equals(roomType)) {
                        systemRoomNum = conventionHotelRoomPersonVo.getSystemRoomNum();
                        roomType = conventionHotelRoomPersonVo.getRoomType();
                        conventionHotelId = conventionHotelRoomPersonVo.getFkConventionHotelId();
                        hotelName = conventionHotelRoomPersonVo.getHotel();
                        if (GeneralTool.isNotEmpty(conventionHotelRoomPersonVo.getHotelRoomNum())) {
                            hotelRoomNum = conventionHotelRoomPersonVo.getHotelRoomNum();
                        }
                    }
                    //同个房间其他住客
                    List<ConventionHotelRoomPersonVo> otherConventionHotelRoomPersons = conventionHotelRoomPersonMapper.getOtherPersons(conventionHotelRoomPersonVo.getFkConventionHotelRoomId(), conventionHotelRoomPersonVo.getFkConventionPersonId());
                    for (ConventionHotelRoomPersonVo conventionHotelRoomPerson : otherConventionHotelRoomPersons) {
                        if (GeneralTool.isEmpty(map.get(conventionHotelRoomPerson.getFkConventionPersonId()))) {
                            TreeSet<Date> set = new TreeSet<>();
                            set.add(conventionHotelRoomPerson.getStayDate());
                            map.put(conventionHotelRoomPerson.getFkConventionPersonId(), set);
                        } else {
                            map.get(conventionHotelRoomPerson.getFkConventionPersonId()).add(conventionHotelRoomPerson.getStayDate());
                        }
                    }
                    dateList.add(conventionHotelRoomPersonVo.getStayDate());
                    conventionHotelRoomPersonVos.add(conventionHotelRoomPersonVo);
                }
            }
            ConventionPersonVo conventionPersonVo = BeanCopyUtils.objClone(conventionPerson, ConventionPersonVo::new);
            //设置日期列表
            conventionPersonVo.setDateList(dateList);
            List<String> dateStr = getDateStr(dateList);
            //设置住房时间段
            conventionPersonVo.setDateStr(dateStr);
            List<ConventionPersonVo> otherPersonDtos = new ArrayList<>();
            for (Map.Entry<Long, TreeSet<Date>> entry : map.entrySet()) {
                ConventionPersonVo otherPersonDto = BeanCopyUtils.objClone(conventionPersonMapper.selectById(entry.getKey()), ConventionPersonVo::new);
                otherPersonDto.setDateList(new ArrayList<>(entry.getValue()));
                //设置dateStr
                otherPersonDto.setDateStr(getDateStr(otherPersonDto.getDateList()));
                otherPersonDtos.add(otherPersonDto);
            }
            conventionPersonVo.setConventionHotelRoomId(conventionHotelId);
            conventionPersonVo.setSystemRoomNum(systemRoomNum);
            conventionPersonVo.setRoomType(roomType);
            conventionPersonVo.setRoomNum(hotelRoomNum);
            conventionPersonVo.setHotelName(hotelName);
            conventionPersonVo.setOtherConventionPersons(otherPersonDtos);
            conventionPersonVoList.add(conventionPersonVo);

        }
        return conventionPersonVoList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void configurationBeds(List<ConventionHotelRoomPersonDto> conventionHotelRoomPersonDtoList) {
        String systemRoomNum = conventionHotelRoomPersonDtoList.get(0).getSystemRoomNum();
        Long conventionHotelId = conventionHotelRoomPersonDtoList.get(0).getConventionHotelId();
        try {
            boolean flag = true;
            int spinCount = 0;
            //设置自旋
            do {
                flag = getRedis.setNx(CacheKeyConstants.CONFIGURATION_BED_CACHE_KEY + conventionHotelId + systemRoomNum, 1, 30L) && flag;
                if (!flag){
                    Thread.sleep(50);
                }
                spinCount++;
            } while(!flag&&spinCount<3);

            //获得锁了
            if (flag){
                for (ConventionHotelRoomPersonDto conventionHotelRoomPersonDto : conventionHotelRoomPersonDtoList) {
                    configurationBed(conventionHotelRoomPersonDto);
                }
            } else {
                //缓存锁未释放
                throw new GetServiceException(LocaleMessageUtils.getMessage("save_error"));
            }
        }catch (Exception e){
            throw new GetServiceException(e.getMessage());
        }finally {
            getRedis.del(CacheKeyConstants.CONFIGURATION_BED_CACHE_KEY + conventionHotelId + systemRoomNum);
        }
    }

    /**
     * 根据dateList获取dateStr
     *
     * @param dateList
     * @return
     */
    private List<String> getDateStr(List<Date> dateList) {
        String dateStr = "";
        List<String> stringList = new ArrayList<>();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if (dateList.size() == 1) {
            StringBuilder sb = new StringBuilder();
            createDateStr(dateList.get(0), dateList.get(0), dateFormat, sb);
            dateStr = sb.toString();
            stringList.add(dateStr);
        } else {
            List<Date> tmpList = new ArrayList<>();
            for (int j = 0; j < dateList.size(); j++) {
                tmpList.add(dateList.get(j));
                if (j > 0) {
                    if (MyDateUtils.differentDays(dateList.get(j - 1), dateList.get(j)) != 1) {
                        StringBuilder sb = new StringBuilder();
                        createDateStr(tmpList.get(0), tmpList.get(tmpList.size() - 2), dateFormat, sb);
                        stringList.add(sb.toString());
                        tmpList.clear();
                        if (j == dateList.size() - 1) {
                            StringBuilder s = new StringBuilder();
                            tmpList.add(dateList.get(j));
                            createDateStr(tmpList.get(0), tmpList.get(tmpList.size() - 1), dateFormat, s);
                            stringList.add(s.toString());
                            tmpList.clear();
                        } else {
                            tmpList.add(dateList.get(j));
                        }
                    } else if (j == dateList.size() - 1) {
                        //开头连续到最后
                        StringBuilder sb = new StringBuilder();
                        createDateStr(tmpList.get(0), tmpList.get(tmpList.size() - 1), dateFormat, sb);
                        stringList.add(sb.toString());
                        tmpList.clear();
                    }
                }
            }
            // dateStr = stringJoiner.toString();
        }
        return stringList;
    }

    /**
     * 生成住房时间段
     *
     * @param start
     * @param end
     * @param dateFormat
     * @param sb
     */
    private void createDateStr(Date start, Date end, DateFormat dateFormat, StringBuilder sb) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(end);
        calendar.add(Calendar.DATE, 1);
        String endDate = dateFormat.format(calendar.getTime());
        sb.append(dateFormat.format(start)).append("-").append(endDate);
    }

    /**
     * 验证有无重复房号
     *
     * @param systemRoomNumList
     * @param systemRoomNum
     * @return
     */
    private boolean isSystemRoomNumEqual(List<String> systemRoomNumList, String systemRoomNum) {
        for (String s : systemRoomNumList) {
            if (s.equals(systemRoomNum)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 验证ids是否一样
     *
     * @param orderPersonIdsList
     * @param peopleIds
     * @return
     */
    private boolean isPersnIdsEqual(List<List<Long>> orderPersonIdsList, List<Long> peopleIds) {
        for (List<Long> longs : orderPersonIdsList) {
            if (MyCollectionUtils.isEqualCollection(longs, peopleIds)) {
                return true;
            }
        }
        return false;
    }


    @Override
    public List<ConventionPersonVo> getRoomNotArrangedPersonList(ConventionPersonDto conventionPersonDto, Page page, String roomDate) {
        //根据峰会id 查找该峰会下所有房型ids
        List<Long> conventionHotelIds = conventionHotelService.getConventionHotelIds(conventionPersonDto.getFkConventionId());
        //根据房型ids（可选条件-日期） 查找所有房型所开得房间id
        List<Long> roomIds = conventionHotelRoomService.getAllRoomIds(conventionHotelIds, roomDate);
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        //筛选出未被安排的人员信息
        IPage<ConventionPersonVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<ConventionPersonVo> conventionPersonList = conventionPersonService.getRoomNotArrangedPersonList(iPage, roomIds, conventionPersonDto);
        page.setAll((int) iPage.getTotal());
//        page.restPage(conventionPersonList);
        return conventionPersonList;
    }

    /**
     * 条件筛选-已满/未满
     *
     * @param conventionHotelRoomVo
     * @param map
     * @param isFull
     */
    private void setConventionPersonMap(ConventionHotelRoomVo conventionHotelRoomVo, TreeMap<String, List<ConventionPersonListVo>> map, Long isFull) {
        boolean flag1 = true;
        boolean flag2 = true;
        boolean flag3 = true;
        //遍历map
        outer:
        for (List<ConventionPersonListVo> conventionPersonListDtoList : map.values()) {
            //遍历map value对应的list
            for (ConventionPersonListVo conventionPersonListVo : conventionPersonListDtoList) {
                if (GeneralTool.isNotEmpty(conventionPersonListVo)) {
                    //无条件时
                    if (GeneralTool.isEmpty(isFull)) {
                        //全部设置进去
                        conventionHotelRoomVo.setConventionPersonMap(map);
                        return;
                    }
                    //只要未满的
                    if (isFull == 0) {
                        //只要有一个中间表id为空，则这个map一定表示未满，直接设置 否则不能设置 改变flag2 确保下面不会设置
                        if (GeneralTool.isEmpty(conventionPersonListVo.getConventionHotelRoomPersonId())) {
                            conventionHotelRoomVo.setConventionPersonMap(map);
                            break outer;
                        } else {
                            flag2 = false;
                        }
                    }
                    //只要已满的
                    if (isFull == 1) {
                        //当循环完后，中间表id都有，说明是满的，下面去设置  否则有一个为空，改变flag1 下面不会进入设置
                        if (GeneralTool.isEmpty(conventionPersonListVo.getConventionHotelRoomPersonId())) {
                            flag1 = false;
                            break outer;
                        }
                    }
                    if (isFull == 2) {
                        //只要有一个中间表id不为空，则这个map一定表示已分配，直接设置 否则不能设置 改变flag2 确保下面不会设置
                        if (GeneralTool.isNotEmpty(conventionPersonListVo.getConventionHotelRoomPersonId())) {
                            flag3 = false;
                            break outer;
                        }
                    }
                }
            }
        }
        //防止重复设置
        if (GeneralTool.isEmpty(conventionHotelRoomVo.getConventionPersonMap())) {
            if (flag1 && flag2 && flag3) {
                //对应的是当isFull=1的时候
                conventionHotelRoomVo.setConventionPersonMap(map);
            }
        }
    }

    /**
     * @return void
     * @Description :根据人员名称条件过滤
     * @Param [conventionHotelRoomDto, convertDatas]
     * <AUTHOR>
     */
    private void filterConvertDatasByName(ConventionHotelRoomDto conventionHotelRoomDto, List<ConventionHotelRoomVo> convertDatas) {
        String personName = "%" + conventionHotelRoomDto.getPersonName() + "%";
        List<Long> personIds = conventionPersonService.getPersonIdsByName(personName);
        Iterator<ConventionHotelRoomVo> iterator = convertDatas.iterator();
        while (iterator.hasNext()) {
            ConventionHotelRoomVo conventionHotelRoomVo = iterator.next();
            TreeMap<String, List<ConventionPersonListVo>> conventionPersonMap = conventionHotelRoomVo.getConventionPersonMap();
            List<Long> ids = new ArrayList<>();
            for (List<ConventionPersonListVo> conventionPersonListVos : conventionPersonMap.values()) {
                //去除list中的null值
                conventionPersonListVos.removeAll(Collections.singleton(null));
                if (!conventionPersonListVos.isEmpty()) {
                    List<Long> list = conventionPersonListVos.stream().map(ConventionPersonListVo::getId).collect(Collectors.toList());
                    //获取所有personid
                    ids.addAll(list);
                } else {
                    conventionPersonListVos.add(null);
                }
            }
            //当所有安排好的personid不属于查询条件的id中时，移除这一条（判断两个集合中是否有相同元素，有返回false）
            if (Collections.disjoint(personIds, ids)) {
                iterator.remove();
            }
        }
    }

    /**
     * @return void
     * @Description :根据跟进bd过滤
     * @Param [conventionHotelRoomVo, convertData]
     * <AUTHOR>
     */
    private void filterConvertDatasByBdCode(ConventionHotelRoomDto conventionHotelRoomVo, List<ConventionHotelRoomVo> convertDatas) {
        // 过滤bdcodes
        Set<String> bdCodes = conventionHotelRoomVo.getBdCodes();
        Iterator<ConventionHotelRoomVo> iterator = convertDatas.iterator();
        while (iterator.hasNext()) {
            ConventionHotelRoomVo conventionHotelRoomDto = iterator.next();
            TreeMap<String, List<ConventionPersonListVo>> conventionPersonMap = conventionHotelRoomDto.getConventionPersonMap();
            List<String> presonBdCode =  new ArrayList<>();
            for (List<ConventionPersonListVo> conventionPersonListDtos : conventionPersonMap.values()) {
                //去除list中的null值
                conventionPersonListDtos.removeAll(Collections.singleton(null));
                if (!conventionPersonListDtos.isEmpty()) {
                    List<String> list = conventionPersonListDtos.stream().map(ConventionPersonListVo::getBdCode).collect(Collectors.toList());
                    //获取该行的所有bdCode
                    presonBdCode.addAll(list);
                } else {
                    conventionPersonListDtos.add(null);
                }
            }
            //当所有安排好的personid不属于查询条件的id中时，移除这一条（判断两个集合中是否有相同元素，有返回false）
            if (Collections.disjoint(presonBdCode, bdCodes)) {
                iterator.remove();
            }
        }
    }

    /**
     * @return void
     * @Description :根据参会人员类型过滤
     * @Param [conventionHotelRoomDto, convertData]
     * <AUTHOR>
     */
    private void filterConvertDatasByPersonType(ConventionHotelRoomDto conventionHotelRoomDto, List<ConventionHotelRoomVo> convertDatas) {
        //先根据参会人员的类型查出对应类型的ids
        Integer personType = conventionHotelRoomDto.getPersonType();
        List<Long> personIds = conventionPersonService.getPersonIdsByType(personType);
        //遍历convertDatas
        Iterator<ConventionHotelRoomVo> iterator = convertDatas.iterator();
        while (iterator.hasNext()) {
            ConventionHotelRoomVo conventionHotelRoomVo = iterator.next();
            TreeMap<String, List<ConventionPersonListVo>> conventionPersonMap = conventionHotelRoomVo.getConventionPersonMap();
            List<Long> ids = new ArrayList<>();
            for (List<ConventionPersonListVo> conventionPersonListVos : conventionPersonMap.values()) {
                //去除list中的null值
                conventionPersonListVos.removeAll(Collections.singleton(null));
                if (!conventionPersonListVos.isEmpty()) {
                    List<Long> list = conventionPersonListVos.stream().map(ConventionPersonListVo::getId).collect(Collectors.toList());
                    //获取所有personid
                    ids.addAll(list);
                } else {
                    conventionPersonListVos.add(null);
                }
            }
            //当所有安排好的personid不属于查询条件的id中时，移除这一条（判断两个集合中是否有相同元素，有返回false）
            if (Collections.disjoint(personIds, ids)) {
                iterator.remove();
            }
        }

    }

    /**
     * 根据房间id获取中间表数据
     *
     * @param roomId
     * @return
     * @throws GetServiceException
     */
    private List<ConventionHotelRoomPerson> getConventionHotelRoomPersonsByRoomId(Long roomId) throws GetServiceException {
        //根据房间id 查找中间表数据
//        Example example = new Example(ConventionHotelRoomPerson.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionHotelRoomId", roomId);
//        return conventionHotelRoomPersonMapper.selectByExample(example);
        return conventionHotelRoomPersonMapper.selectList(Wrappers.<ConventionHotelRoomPerson>lambdaQuery().eq(ConventionHotelRoomPerson::getFkConventionHotelRoomId, roomId));
    }

    /**
     * 验证 同一间房人员id不能相同
     *
     * @param fkConventionPersonId
     * @param roomId
     * @throws GetServiceException
     */
    private void validateConfigurationBed(Long fkConventionPersonId, Long roomId) throws GetServiceException {
        List<ConventionHotelRoomPerson> conventionHotelRoomPersons = getConventionHotelRoomPersonsByRoomId(roomId);
        for (ConventionHotelRoomPerson conventionHotelRoomPerson : conventionHotelRoomPersons) {
            //当为锁定时，id为空 无需验证
            if (GeneralTool.isNotEmpty(fkConventionPersonId)) {
                if (fkConventionPersonId.equals(conventionHotelRoomPerson.getFkConventionPersonId())) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("hotelRoom_person_data_association"));
                }
            }
        }
    }

    /**
     * 获取bdcode
     *
     * @param id
     * @return
     */
    private String getBdCodeByStaffId(Long id) {
//        Example example = new Example(StaffBdCode.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkStaffId", id);
//        List<StaffBdCode> staffBdCodes = staffBdCodeMapper.selectByExample(example);
        List<StaffBdCode> staffBdCodes = staffBdCodeMapper.selectList(Wrappers.<StaffBdCode>lambdaQuery().eq(StaffBdCode::getFkStaffId, id));
        if (GeneralTool.isNotEmpty(staffBdCodes)) {
            return staffBdCodes.get(0).getBdCode();
        }
        return null;
    }

    /**
     * 获取bdcode
     *
     * @param staffIds
     * @return
     */
    private List<String> getBdCodeByStaffIds(List<Long> staffIds) {
        List<StaffBdCode> staffBdCodes = staffBdCodeMapper.selectList(Wrappers.<StaffBdCode>lambdaQuery().in(StaffBdCode::getFkStaffId, staffIds));
        if (GeneralTool.isEmpty(staffBdCodes)) {
            return Collections.emptyList();
        }
        return staffBdCodes.stream().map(StaffBdCode::getBdCode).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
