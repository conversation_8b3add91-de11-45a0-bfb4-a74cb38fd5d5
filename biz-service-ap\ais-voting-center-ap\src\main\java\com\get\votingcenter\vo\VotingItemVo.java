package com.get.votingcenter.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseEntity;
import com.get.votingcenter.entity.VotingItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/9/24 11:08
 * @verison: 1.0
 * @description:
 */
@Data
public class VotingItemVo extends BaseEntity {
    /**
     * 状态名称
     */
    @ApiModelProperty(value = "状态名称")
    private String statusName;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

    /**
     * 主题名称
     */
    @ApiModelProperty(value = "主题名称")
    private String themeName;

    /**
     * 投票项管理图片
     */
    @ApiModelProperty(value = "投票项管理图片")
    private List<VotingMediaAndAttachedVo> mediaAttachedVos;

    /**
     * 投票主题Id
     */
    @ApiModelProperty(value = "投票主题Id")
    @Column(name = "fk_voting_id")
    private Long fkVotingId;
    /**
     * 投票项标题
     */
    @ApiModelProperty(value = "投票项标题")
    @Column(name = "title")
    private String title;
    /**
     * 投票项描述
     */
    @ApiModelProperty(value = "投票项描述")
    @Column(name = "description")
    private String description;
    /**
     * 开放时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "开放时间")
    @Column(name = "start_time")
    private Date startTime;
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "结束时间")
    @Column(name = "end_time")
    private Date endTime;
    /**
     * 状态（优于时间控制）：0没开始/1进行中/2已结束，只有设置为1=进行中，才按时间控制
     */
    @ApiModelProperty(value = "状态（优于时间控制）：0没开始/1进行中/2已结束，只有设置为1=进行中，才按时间控制")
    @Column(name = "status")
    private Integer status;
    /**
     * 默认为0，为0时随机排序，排序为从大到小顺序排列
     */
    @ApiModelProperty(value = "默认为0，为0时随机排序，排序为从大到小顺序排列")
    @Column(name = "view_order")
    private Integer viewOrder;


}
