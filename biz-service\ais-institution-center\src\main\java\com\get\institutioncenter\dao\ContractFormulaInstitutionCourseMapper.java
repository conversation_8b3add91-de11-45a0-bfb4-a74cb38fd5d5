package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.ContractFormulaInstitutionCourse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ContractFormulaInstitutionCourseMapper extends BaseMapper<ContractFormulaInstitutionCourse> {

    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(ContractFormulaInstitutionCourse record);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过合同公式id 查找对应课程ids
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<Long> getCourseIdListByFkid(Long contractFormulaId);

    /**
     * @return java.util.List<java.lang.String>
     * @Description :通过合同公式id 查找对应课程名称
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<String> getCourseNameByFkid(Long contractFormulaId);

    /**
     * 根据合同公式筛选课程
     *
     * @Date 14:41 2021/6/15
     * <AUTHOR>
     */
    List<Long> getCourseIdListByFaculty(@Param("courseIdList") List<Long> courseIdList, @Param("zoneIdList") List<Long> zoneIdList,
                                        @Param("facultyIdList") List<Long> facultyIdList, @Param("majorLevelIdList") List<Long> majorLevelIdList,
                                        @Param("courseTypeIdList") List<Long> courseTypeIdList);

    boolean isExistByCourseId(@Param("courseId") Long courseId);
}