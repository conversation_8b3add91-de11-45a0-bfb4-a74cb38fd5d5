package com.get.permissioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.permissioncenter.vo.CompanyVo;
import com.get.permissioncenter.entity.Company;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface CompanyMapper extends BaseMapper<Company> {

    int insertSelective(Company company);


    List<Long> getAllChildCompanyId(@Param("ids") List<Long> ids);

    /**
     * 获取公司子公司名称
     */
    List<CompanyVo> getChildCompany(@Param("fkParentCompanyId") Long fkParentCompanyId);


    /**
     * 获取最大排序值
     *
     * @return
     */
    Integer getMaxViewOrder();

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 递归查找所有父级公司
     * @Param [id]
     * <AUTHOR>
     **/
    List<Long> getParentCompanyIds(@Param("id") Long id);

    /**
     * 根据关键字模糊查询公司Ids
     *
     * @param keyWord
     * @return
     */
    List<Long> getCompanyIdByName(@Param("keyWord") String keyWord);


    /**
     * @return java.util.List<com.get.permissioncenter.com.get.permissioncenter.vo.tree.CompanyTreeVo>
     * @Description: 获取上级公司
     * @Param [companyId]
     * <AUTHOR>
     */
    List<Long> getParentCompany(@Param("companyId") Long companyId);

    /**
     * @return java.lang.Boolean
     * @Description: 是否存在最顶级上属公司id
     * @Param []
     * <AUTHOR>
     */
    Boolean getTopCompany();


    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [staffId]
     * <AUTHOR>
     */
    Boolean isExistByCompanyId(Long companyId);

    List<BaseSelectEntity> fuzzGetCompanyInfo(@Param("keyword")String keyword,@Param("companyId") Long companyId);

}