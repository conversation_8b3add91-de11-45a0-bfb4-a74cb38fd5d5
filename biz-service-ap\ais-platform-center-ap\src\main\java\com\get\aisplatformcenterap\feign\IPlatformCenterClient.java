package com.get.aisplatformcenterap.feign;

import com.get.aisplatformcenterap.dto.GetPermissionMenuDto;
import com.get.aisplatformcenterap.dto.ReleaseInfoAndItemDto;
import com.get.aisplatformcenterap.dto.ReleaseInfoSearchDto;
import com.get.aisplatformcenterap.dto.UserScopedDataDto;
import com.get.aisplatformcenterap.vo.LabelSearchAboutAgentVo;
import com.get.aisplatformcenterap.vo.MenuTreeVo;
import com.get.aisplatformcenterap.vo.PlatFormTypeVo;
import com.get.aisplatformcenterap.vo.ReleaseInfoAndItemVo;
import com.get.aisplatformcenterap.vo.ReleaseInfoAndItemVos;
import com.get.aisplatformcenterap.vo.ReleaseInfoItemVo;
import com.get.aisplatformcenterap.vo.UserInfoVo;
import com.get.common.constant.AppCenterConstant;
import com.get.common.result.SearchBean;
import com.get.core.tool.api.Result;
import com.get.salecenter.dto.AgentLabelDto;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        value = AppCenterConstant.APPLICATION_PLATFORM_CENTER
)
public interface IPlatformCenterClient {
    String API_PREFIX = "/feign";

    /**
     * 根据名称模糊搜索用户ids
     */
    String GET_USER_IDS_BY_PARAM = API_PREFIX + "/get-user-ids-by-param";


    /**
     * feign调用 根据userid获取名称（微信昵称）
     */
    String GET_USER_NICK_NAMES_BY_USER_IDS = API_PREFIX + "/get-user-nick-names-by-user-ids";

    /**
     * 根据ids获取人员对应的城市名称
     */
    String GET_CITY_NAMES_BY_USER_IDS = API_PREFIX + "/get-city-names-by-user-ids";

    /**
     * feign调用 根据userid获取手机号
     */
    String GET_MOBILE_BY_USER_IDS = API_PREFIX + "/get-mobile-by-user-ids";

    /**
     * 根据名称模糊或者手机号搜索用户ids
     */
    String GET_USER_IDS_BY_NAME_OR_MOBILE = API_PREFIX + "/get-user-ids-by-name-or-mobile";

    /**
     * feign调用 根据userid获取对象
     */
    String GET_USER_INFO_DTO_BY_IDS = API_PREFIX + "/get-user-info-dto-by-ids";

    String GET_LABEL_INFO_BY_ID_AND_KEYWORD = API_PREFIX + "/get-label-info-by-id-and-keyword";

    /**
     * 获取平台类型下拉
     */
    String GET_PLATFORM_TYPE_DROP_DOWN = API_PREFIX + "/get-platform-type-drop-down";
    /**
     * 获取发布信息
     */
    String GET_RELEASE_INFO = API_PREFIX + "/get-release-info";
    /**
     * 新增发布信息和子项
     */
    String INSERT_RELEASE_INFO_AND_ITEM = API_PREFIX + "/insert-release-info-and-item";

    /**
     * 删除发布信息
     */
    String DELETE_RELEASE_INFO = API_PREFIX + "/delete-release-info";

    /**
     * 获取发版详情
     */
    String GET_DETAILED_INFORMATION_BY_ID = API_PREFIX + "/get-detailed-information-by-id";

    /**
     * 更新发版信息
     */
    String UPDATE_RELEASE_INFO_AND_ITEM = API_PREFIX + "/update-release-info-and-item";

    /**
     * 更新发版状态
     */
    String UPDATE_RELEASE_INFO_STATUS = API_PREFIX + "/update-release-info-status";

    /**
     * 获取用户对应的发版信息列表
     */
    String GET_USER_LIST_BY_RESOURCE_KEYS_AND_PAGE = API_PREFIX + "/get-user-list-by-resource-keys-and-page";

    /**
     * 获取用户对应的发版信息
     */
    String GET_USER_LIST_BY_RESOURCE_KEYS = API_PREFIX + "/get-user-list-by-resource-keys";

    /**
     * 获取用户对应的的发版子项信息
     */
    String GET_RELEASE_INFO_ITEM_BY_RELEASE_INFO_ID_AND_RESOURCE_KEYS = API_PREFIX + "/get-release-info-item-by-release-info-id-and-resource-keys";

    /**
     * 获取partner菜单
     */
    String GET_PARTNER_PERMISSION_MENU = API_PREFIX + "/get-partner-permission-menu";


    /**
     * 根据名称模糊搜索用户ids
     * @param userName
     * @param fkAreaCityId
     * @param bdName
     * @return
     */
    @PostMapping(GET_USER_IDS_BY_PARAM)
    Result<Set<Long>> getUserIdsByParam(@RequestParam(value = "userName", required = false) String userName,
                                        @RequestParam(value = "fkAreaCityId", required = false) Long fkAreaCityId,
                                        @RequestParam(value = "bdName", required = false) String bdName);


    /**
     * feign调用 根据userid获取名称（微信昵称）
     * @param userIds
     * @return
     */
    @PostMapping(GET_USER_NICK_NAMES_BY_USER_IDS)
    Result<Map<Long, String>> getUserNickNamesByUserIds(@RequestBody Set<Long> userIds);

    /**
     * 根据ids获取人员对应的城市名称
     * @param userIds
     * @return
     */
    @PostMapping(GET_CITY_NAMES_BY_USER_IDS)
    Result<Map<Long, String>> getCityNamesByUserIds(@RequestBody Set<Long> userIds);

    /**
     * feign调用 根据userid获取手机号
     * @param userIds
     * @return
     */
    @PostMapping(GET_MOBILE_BY_USER_IDS)
    Result<Map<Long, String>> getMobileByUserIds(@RequestBody Set<Long> userIds);

    /**
     * 根据名称模糊或者手机号搜索用户ids
     * @param userName
     * @param phoneNumber
     * @return
     */
    @PostMapping(GET_USER_IDS_BY_NAME_OR_MOBILE)
    Result<Set<Long>> getUserIdsByNameOrMobile(@RequestParam(value = "userName", required = false) String userName,
                                               @RequestParam(value = "phoneNumber", required = false) String phoneNumber);

    /**
     * feign调用 根据userid获取对象
     * @param userIds
     * @return
     */
    @PostMapping(GET_USER_INFO_DTO_BY_IDS)
    Result<Map<Long, UserInfoVo>> getUserInfoDtoByIds(@RequestBody Set<Long> userIds);

    @PostMapping(GET_LABEL_INFO_BY_ID_AND_KEYWORD)
    Result<LabelSearchAboutAgentVo> getLabelByLabelTypeIdAndKeyWord(@RequestBody SearchBean<AgentLabelDto> page);

    @GetMapping(GET_PLATFORM_TYPE_DROP_DOWN)
    Result<List<PlatFormTypeVo>> getPlatformTypeDropDown();

    @PostMapping(GET_RELEASE_INFO)
    Result<ReleaseInfoAndItemVos> getReleaseInfoAndPage(@RequestBody SearchBean<ReleaseInfoSearchDto> page);

    @PostMapping(INSERT_RELEASE_INFO_AND_ITEM)
    Result insertReleaseInfo(@RequestBody ReleaseInfoAndItemDto releaseInfoAndItemDto);

    @GetMapping(DELETE_RELEASE_INFO)
    Result deleteReleaseInfo(@RequestParam(value = "id") Long id);

    @GetMapping(GET_DETAILED_INFORMATION_BY_ID)
    Result<ReleaseInfoAndItemVo> getDetailedInformationById(@RequestParam(value = "id") Long id);

    @PostMapping(UPDATE_RELEASE_INFO_AND_ITEM)
    Result updateReleaseInfo(@RequestBody ReleaseInfoAndItemDto releaseInfoAndItemDto);

    @PostMapping(UPDATE_RELEASE_INFO_STATUS)
    Result updateReleaseInfoStatus(@RequestBody ReleaseInfoAndItemDto releaseInfoAndItemDto);

    @PostMapping(GET_USER_LIST_BY_RESOURCE_KEYS_AND_PAGE)
    Result<ReleaseInfoAndItemVos> getUserListByResourceKeysAndPage(@RequestBody SearchBean<UserScopedDataDto> page);

    @PostMapping(GET_USER_LIST_BY_RESOURCE_KEYS)
    Result<List<ReleaseInfoAndItemVo>> getUserListByResourceKeys(@RequestBody UserScopedDataDto userScopedDataDto);

    @PostMapping(GET_RELEASE_INFO_ITEM_BY_RELEASE_INFO_ID_AND_RESOURCE_KEYS)
    Result<List<ReleaseInfoItemVo>> getReleaseInfoItemByReleaseInfoIdAndResourceKeys(@RequestBody UserScopedDataDto userScopedDataDto);

    @PostMapping(GET_PARTNER_PERMISSION_MENU)
    Result<List<MenuTreeVo>> getPartnerPermissionMenu(GetPermissionMenuDto getPermissionMenuDto);
}
