package com.get.aismail.dto;

import lombok.Data;

import java.util.Date;

@Data
public class NewApplicationInfoDto {
    // 姓名(中)
    private String name;
    // 性别
    private String gender;
    // 生日
    private Date birthday;
    // 护照(保险业务必填)
    private String passportNum;
    // 护照签发地
    private AreaCountryDto fkAreaCountryIdPassport;
    // 国籍/地区
    private AreaCountryDto fkAreaCountryIdNationality;
    // 持有永久居留许可证国家/地区/地区
    private AreaCountryDto fkAreaCountryIdGreenCard;
    // 学生出生地国家/地区
    private AreaCountryDto fkAreaCountryIdBirth;
    // 学生出生省
    private AreaStateDto fkAreaStateIdBirth;
    // 学生出生市
    private AreaCityDto fkAreaCityIdBirth;
    // 共享链接
    private String sharedPath;
    // 备注
    private String remark;
    public NewApplicationInfoDto(){};

    public NewApplicationInfoDto(String name, String gender, Date birthday, String passportNum, AreaCountryDto fkAreaCountryIdPassport, AreaCountryDto fkAreaCountryIdNationality, AreaCountryDto fkAreaCountryIdGreenCard, AreaCountryDto fkAreaCountryIdBirth, AreaStateDto fkAreaStateIdBirth, AreaCityDto fkAreaCityIdBirth, String sharedPath, String remark) {
        this.name = name;
        this.gender = gender;
        this.birthday = birthday;
        this.passportNum = passportNum;
        this.fkAreaCountryIdPassport = fkAreaCountryIdPassport;
        this.fkAreaCountryIdNationality = fkAreaCountryIdNationality;
        this.fkAreaCountryIdGreenCard = fkAreaCountryIdGreenCard;
        this.fkAreaCountryIdBirth = fkAreaCountryIdBirth;
        this.fkAreaStateIdBirth = fkAreaStateIdBirth;
        this.fkAreaCityIdBirth = fkAreaCityIdBirth;
        this.sharedPath = sharedPath;
        this.remark = remark;
    }
}
