package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.vo.InstitutionFaqVo;
import com.get.institutioncenter.entity.InstitutionFaq;
import com.get.institutioncenter.dto.InstitutionFaqDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/10
 * @TIME: 11:48
 * @Description:
 **/
public interface IInstitutionFaqService extends BaseService<InstitutionFaq> {
    /**
     * 列表数据
     *
     * @param institutionFaqDto
     * @param page
     * @return
     */
    List<InstitutionFaqVo> datas(InstitutionFaqDto institutionFaqDto, Page page);

    /**
     * 保存
     *
     * @param institutionFaqDto
     * @return
     */
    Long addInstitutionFaq(InstitutionFaqDto institutionFaqDto);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    InstitutionFaqVo findInstitutionFaqById(Long id);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);


    /**
     * 修改
     *
     * @param institutionFaqDto
     * @return
     */
    InstitutionFaqVo updateInstitutionFaq(InstitutionFaqDto institutionFaqDto);

}
