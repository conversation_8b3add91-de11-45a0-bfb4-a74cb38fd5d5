package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2020/12/8
 * @TIME: 11:27
 * @Description:
 **/
@Data
public class InstitutionCourseAcademicScoreDto extends BaseVoEntity {
    /**
     * 课程Id
     */
    @ApiModelProperty(value = "课程Id")
    private Long fkInstitutionCourseId;

    /**
     * 条件类型，枚举：GPA/GCSE/HKDSE/IB
     */
    @ApiModelProperty(value = "条件类型，枚举：GPA/GCSE/HKDSE/IB")
    private Integer conditionType;

    /**
     * 成绩
     */
    @ApiModelProperty(value = "分数")
    private BigDecimal score;

    /**
     * 成绩描述
     */
    @ApiModelProperty(value = "成绩描述")
    private String description;
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;
    /**
     * 分数类型
     */
    @ApiModelProperty(value = "分数类型")
    private String scoreType;
}
