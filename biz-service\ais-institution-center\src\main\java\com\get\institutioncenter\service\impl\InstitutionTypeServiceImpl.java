package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.redis.cache.GetRedis;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.component.TranslationHelp;
import com.get.institutioncenter.dao.InstitutionCourseTypeMapper;
import com.get.institutioncenter.dao.InstitutionTypeMapper;
import com.get.institutioncenter.dto.InstitutionTypeDto;
import com.get.institutioncenter.entity.InstitutionType;
import com.get.institutioncenter.service.IInstitutionTypeService;
import com.get.institutioncenter.service.ITranslationMappingService;
import com.get.institutioncenter.utils.InstitutionTypeUtil;
import com.get.institutioncenter.vo.InstitutionTypeVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * @author: Sea
 * @create: 2020/7/30 11:13
 * @verison: 1.0
 * @description: 学校类型管理实现类
 */
@Service
public class InstitutionTypeServiceImpl extends BaseServiceImpl<InstitutionTypeMapper, InstitutionType> implements IInstitutionTypeService {

    @Resource
    private InstitutionTypeMapper institutionTypeMapper;
    @Resource
    private InstitutionCourseTypeMapper institutionCourseTypeMapper;

    @Resource
    private UtilService utilService;
    @Resource
    private ITranslationMappingService translationMappingService;
    @Resource
    private GetRedis redisClient;
    @Resource
    private TranslationHelp translationHelp;

    @Override
    public InstitutionTypeVo findInstitutionTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionType institutionType = institutionTypeMapper.selectById(id);
        String language = SecureUtil.getLocale();
        if (GeneralTool.isNotEmpty(institutionType) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
            translationHelp.translation(Collections.singletonList(institutionType), ProjectKeyEnum.getInitialValue(language));
        }

        InstitutionTypeVo institutionTypeVo = BeanCopyUtils.objClone(institutionType, InstitutionTypeVo::new);
        institutionTypeVo.setFkTableName(TableEnum.INSTITUTION_TYPE.key);
        return institutionTypeVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(ValidList<InstitutionTypeDto> institutionTypeDtos) {
        if (GeneralTool.isEmpty(institutionTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //获取最大排序(防止每次都要去查最大值，所以在外面查出一次，循环几次 就自增几次)
        Integer maxViewOrder = institutionTypeMapper.getMaxViewOrder();
        for (InstitutionTypeDto institutionTypeDto : institutionTypeDtos) {
            if (GeneralTool.isEmpty(institutionTypeDto.getId())) {
                if (validateAdd(institutionTypeDto)) {
                    InstitutionType institutionType = BeanCopyUtils.objClone(institutionTypeDto, InstitutionType::new);
                    institutionType.setViewOrder(maxViewOrder);
                    utilService.updateUserInfoToEntity(institutionType);
                    institutionTypeMapper.insertSelective(institutionType);
                    maxViewOrder++;
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("institutionTypeName_null"));
                }
            } else {
                if (validateUpdate(institutionTypeDto)) {
                    InstitutionType institutionType = BeanCopyUtils.objClone(institutionTypeDto, InstitutionType::new);
                    utilService.updateUserInfoToEntity(institutionType);
                    institutionTypeMapper.updateById(institutionType);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("institutionTypeName_null"));
                }
            }

        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (findInstitutionTypeById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        institutionTypeMapper.deleteById(id);

        //删除翻译内容
        translationMappingService.deleteTranslations(TableEnum.INSTITUTION_TYPE.key, id);
    }

    @Override
    public InstitutionTypeVo updateInstitutionType(InstitutionTypeDto institutionTypeDto) {
        if (institutionTypeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        InstitutionType result = institutionTypeMapper.selectById(institutionTypeDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (validateUpdate(institutionTypeDto)) {
            InstitutionType institutionType = BeanCopyUtils.objClone(institutionTypeDto, InstitutionType::new);
            utilService.updateUserInfoToEntity(institutionType);
            institutionTypeMapper.updateById(institutionType);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("institutionTypeName_null"));
        }
        return findInstitutionTypeById(institutionTypeDto.getId());
    }

    @Override
    public List<InstitutionTypeVo> getInstitutionTypes(InstitutionTypeDto institutionTypeDto, Page page) {
        LambdaQueryWrapper<InstitutionType> wrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(institutionTypeDto)) {
            if (GeneralTool.isNotEmpty(institutionTypeDto.getKeyWord())) {
                wrapper.like(InstitutionType::getTypeName, institutionTypeDto.getKeyWord());
            }
        }
        wrapper.orderByDesc(InstitutionType::getViewOrder);
        IPage<InstitutionType> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<InstitutionType> institutionTypes = pages.getRecords();
        page.setAll((int) pages.getTotal());
        String language = SecureUtil.getLocale();
        if (GeneralTool.isNotEmpty(institutionTypes) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
            translationHelp.translation(institutionTypes, ProjectKeyEnum.getInitialValue(language));
        }
        List<InstitutionTypeVo> convertDatas = new ArrayList<>();
        for (InstitutionType institutionType : institutionTypes) {
            InstitutionTypeVo institutionTypeVo = BeanCopyUtils.objClone(institutionType, InstitutionTypeVo::new);
            institutionTypeVo.setFkTableName(TableEnum.INSTITUTION_TYPE.key);
            convertDatas.add(institutionTypeVo);
        }
        return convertDatas;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<InstitutionTypeDto> institutionTypeDtos) {
        if (GeneralTool.isEmpty(institutionTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        InstitutionType ro = BeanCopyUtils.objClone(institutionTypeDtos.get(0), InstitutionType::new);
        Integer oneorder = ro.getViewOrder();
        InstitutionType rt = BeanCopyUtils.objClone(institutionTypeDtos.get(1), InstitutionType::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        institutionTypeMapper.updateById(ro);
        institutionTypeMapper.updateById(rt);
    }

    @Override
    public List<BaseSelectEntity> getInstitutionTypeList() {
        String translation = SecureUtil.getLocale();
        return institutionTypeMapper.getInstitutionTypeList("en".equals(translation), null);
    }

    @Override
    public Map<Long, String> getInstitutionTypeNameByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        LambdaQueryWrapper<InstitutionType> wrapper = new LambdaQueryWrapper();
        wrapper.in(InstitutionType::getId, ids);
        List<InstitutionType> institutionTypes = institutionTypeMapper.selectList(wrapper);
        if (GeneralTool.isEmpty(institutionTypes)) {
            return map;
        }
     /*   String language = SecureUtil.getLocale();
        if (GeneralTool.isNotEmpty(institutionTypes) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
            translationHelp.translation(institutionTypes, ProjectKeyEnum.getInitialValue(language));
        }*/
        for (InstitutionType institutionType : institutionTypes) {
            map.put(institutionType.getId(), institutionTypeMapper.getInstitutionTypeNameById(institutionType.getId()));
        }
        return map;
    }

    @Override
    public Map<Long, String> getAllInstitutionTypeName() {
        Map<Long, String> map = new HashMap<>();
        List<InstitutionType> institutionTypes = institutionTypeMapper.selectList(Wrappers.<InstitutionType>lambdaQuery());
        for (InstitutionType institutionType : institutionTypes) {
            String name = institutionType.getTypeNameChn() + "（" + institutionType.getTypeName() + "）";
            map.put(institutionType.getId(), name);
        }
        return map;
    }

    @Override
    public String getTypeIdStringByCourseId(Long id) {
        return institutionCourseTypeMapper.getTypeIdStringByCourseId(id);
    }


    private boolean validateAdd(InstitutionTypeDto institutionTypeDto) {
        LambdaQueryWrapper<InstitutionType> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionType::getTypeName, institutionTypeDto.getTypeName());
        List<InstitutionType> list = this.institutionTypeMapper.selectList(wrapper);
        return GeneralTool.isEmpty(list);
    }

    private boolean validateUpdate(InstitutionTypeDto institutionTypeDto) {
        LambdaQueryWrapper<InstitutionType> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionType::getTypeName, institutionTypeDto.getTypeName());
        List<InstitutionType> list = this.institutionTypeMapper.selectList(wrapper);
        return list.size() <= 0 || list.get(0).getId().equals(institutionTypeDto.getId());
    }

    @Override
    public List<BaseSelectEntity> getInstitutionK12TypeList() {
//        多选逗号分隔：0=幼儿园/1=小学/2=初中/3=高中
       return InstitutionTypeUtil.getK12TypeList();
    }

    @Override
    public List<BaseSelectEntity> getAccommodationTypeList() {
//        多选逗号分隔：0=幼儿园/1=小学/2=初中/3=高中
        return InstitutionTypeUtil.getAccommodationTypeList();
    }
}
