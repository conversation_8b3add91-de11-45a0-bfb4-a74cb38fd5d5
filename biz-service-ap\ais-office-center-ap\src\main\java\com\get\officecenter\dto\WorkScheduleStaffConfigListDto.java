package com.get.officecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/7 16:37
 */
@Data
@ApiModel(value = "工作时间设置VO")
public class WorkScheduleStaffConfigListDto extends BaseVoEntity {

    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "员工名字")
    private String fkStaffName;

    @ApiModelProperty(value = "考勤号")
    private String attendanceNum;

}
