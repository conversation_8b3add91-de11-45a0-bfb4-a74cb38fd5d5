package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.financecenter.dto.CreateVouchDto;
import com.get.financecenter.dto.VouchDto;
import com.get.financecenter.vo.VouchTypeVo;
import com.get.financecenter.vo.VouchVo;
import java.util.List;
import javax.validation.Valid;

public interface VouchService {

    /**
     * 生成凭证
     * @param createVouchDto
     */
    void createVouch(CreateVouchDto createVouchDto);

    List<VouchTypeVo> getVouchType();

    Long add( VouchDto vouchDto);

    List<VouchVo> getVouchs(@Valid VouchDto vouchDto, Page page);

    VouchVo getVouchsById(Long id);

    Long delete(Long id);

    VouchVo updateById(Long id);

    VouchVo updateByVouchId(Long vouchId);
}
