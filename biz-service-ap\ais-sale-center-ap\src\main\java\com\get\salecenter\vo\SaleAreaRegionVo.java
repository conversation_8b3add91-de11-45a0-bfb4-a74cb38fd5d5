package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 业务区域返回类
 *
 * <AUTHOR>
 * @date 2021/7/28 14:59
 */
@Data
public class SaleAreaRegionVo extends BaseEntity implements Serializable {
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;

    /**
     * 大区编号
     */
    @ApiModelProperty(value = "大区编号")
    private String num;

    /**
     * 大区名称
     */
    @ApiModelProperty(value = "大区名称")
    private String name;

    /**
     * 大区中文名称
     */
    @ApiModelProperty(value = "大区中文名称")
    private String nameChn;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    /**
     * 州省id
     */
    @ApiModelProperty(value = "州省id")
    private Long fkAreaStateId;

    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;

    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;

    /**
     * 全称
     */
    @ApiModelProperty(value = "全称")
    private String fullName;
}
