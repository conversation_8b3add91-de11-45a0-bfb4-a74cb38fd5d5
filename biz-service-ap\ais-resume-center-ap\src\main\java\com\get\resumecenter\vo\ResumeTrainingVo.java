package com.get.resumecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import com.get.resumecenter.entity.ResumeTraining;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2020/8/11
 * @TIME: 10:57
 * @Description:
 **/
@Data
public class ResumeTrainingVo extends BaseEntity {

    /**
     * 所属简历Id
     */
    @ApiModelProperty(value = "所属简历Id")
    @Column(name = "fk_resume_id")
    private Long fkResumeId;
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开始时间")
    @Column(name = "start_date")
    private Date startDate;
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "结束时间")
    @Column(name = "end_date")
    private Date endDate;
    /**
     * 培训课程
     */
    @ApiModelProperty(value = "培训课程")
    @Column(name = "course")
    private String course;
    /**
     * 培训机构
     */
    @ApiModelProperty(value = "培训机构")
    @Column(name = "institution")
    private String institution;
    /**
     * 培训地点
     */
    @ApiModelProperty(value = "培训地点")
    @Column(name = "place")
    private String place;
    /**
     * 培训描述
     */
    @ApiModelProperty(value = "培训描述")
    @Column(name = "description")
    private String description;
}
