package com.get.institutioncenter.dto;

import com.get.core.mybatis.base.BaseSelectEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * author:Neil
 * Time: 15:09
 * Date: 2023/1/30
 * Description:
 */
@Data
public class MajorLevelSelectDto {

    @ApiModelProperty(value = "组别名称")
    private String majorGroup;

    @ApiModelProperty(value = "组别排序（子项排序的平均值）")
    private Integer order;

    @ApiModelProperty(value = "课程等级")
    private List<BaseSelectEntity> majorLevelList;
}
