package com.get.salecenter.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2024/4/16
 * @TIME: 14:16
 * @Description:
 **/
@Data
public class KpiPlanVo extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    @ApiModelProperty(value = "预设时间条件")
    private String defaultTime;

    @ApiModelProperty(value = "是否能够进入新增考核人员配置")
    private Boolean isAddKpiPlanStaff = false;

    @ApiModelProperty(value = "是否能够进入KPI方案统计")
    private Boolean isKpiPlanStatistics = false;

    @ApiModelProperty(value = "公司ID")
    private String fkCompanyIds;



    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "学生创建时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    private Date studentCreateTimeStart;

    @ApiModelProperty(value = "学生创建时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    private Date studentCreateTimeEnd;

    @ApiModelProperty(value = "入学时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    private Date intakeTimeStart;

    @ApiModelProperty(value = "入学时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    private Date intakeTimeEnd;

    @ApiModelProperty(value = "步骤登记时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    private Date stepTimeStart;

    @ApiModelProperty(value = "步骤登记时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    private Date stepTimeEnd;

    /**
     * 申请创建开始时间
     */
    @ApiModelProperty(value = "申请计划创建时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    private Date offerItemCreateTimeStart;

    /**
     * 申请创建结束时间
     */
    @ApiModelProperty(value = "申请计划结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    private Date offerItemCreateTimeEnd;


    @ApiModelProperty(value = "定时统计：0关闭/1开启")
    private Integer isEnableScheduledCount;
}
