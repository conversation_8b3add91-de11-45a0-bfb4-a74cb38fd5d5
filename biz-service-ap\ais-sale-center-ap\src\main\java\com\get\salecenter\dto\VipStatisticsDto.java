package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2022/11/11
 * @TIME: 15:02
 * @Description:VIP统计VO
 **/
@Data
public class VipStatisticsDto {

    @ApiModelProperty(value = "表名")
    private String fkTableName;

    /**
     * 国家列表
     */
    @ApiModelProperty(value = "国家ID列表")
    private Set<Long> fkAreaCountryIds;

    /**
     * 学校提供商ID列表
     */
    @ApiModelProperty(value = "学校提供商ID列表")
    private Set<Long> fkInstitutionProviderIds;

    /**
     * 公司ID
     */
    @ApiModelProperty(value = "公司ID")
    private Long fkCompanyId;

    /**
     * 学生创建开始时间
     */
    @ApiModelProperty(value = "学生创建开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    /**
     * 学生创建结束时间
     */
    @ApiModelProperty(value = "学生创建结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    /**
     * 开学时间
     */
    @ApiModelProperty(value = "开学时间" )
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startOpeningTime;
    /**
     * 开学时间
     */
    @ApiModelProperty(value = "开学时间" )
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endOpeningTime;

    /**
     * bd名称
     */
    @ApiModelProperty(value = "bd名称")
    private String bdName;
    /**
     * BD编号
     */
    @ApiModelProperty(value = "BD编号")
    private String bdCode;
    /**
     * 所属集团Id
     */
    @ApiModelProperty(value = "所属集团Id")
    private List<Long> fkInstitutionGroupIds;

    /**
     * 大区Id
     */
    @ApiModelProperty(value = "大区Id")
    private Long fkAreaRegionId;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

    /**
     * 绑定BD员工ids
     */
    @ApiModelProperty(value = "绑定BD员工ids")
    private List<Long> fkStaffIds;

    /**
     * 员工以及旗下员工所创建的代理ids
     */
    @ApiModelProperty(value = "员工以及旗下员工所创建的代理ids")
    private List<Long> staffFollowerIds;

    /**
     * 国家ID列表,员工（登录人）业务国家
     */
    @ApiModelProperty(value = "国家ID列表,员工（登录人）业务国家")
    private List<Long> fkAreaCountryIdList;

    /**
     * 业绩统计GEA定校量相关步骤列表枚举
     */
    @ApiModelProperty(value = "业绩统计GEA定校量相关步骤列表枚举")
    private List<String> geaConfirmationStatisticsStepList;

    /**
     * 业绩统计IAE定校量相关步骤列表枚举
     */
    @ApiModelProperty(value = "业绩统计IAE定校量相关步骤列表枚举")
    private List<String> iaeConfirmationStatisticsStepList;

    /**
     * 业绩统计GEA成功量相关步骤列表枚举
     */
    @ApiModelProperty(value = "业绩统计GEA成功量相关步骤列表枚举")
    private List<String> geaSuccessStatisticsStepList;

    /**
     * 业绩统计IAE成功量相关步骤列表枚举
     */
    @ApiModelProperty(value = "业绩统计IAE成功量相关步骤列表枚举")
    private List<String> iaeSuccessStatisticsStepList;
}