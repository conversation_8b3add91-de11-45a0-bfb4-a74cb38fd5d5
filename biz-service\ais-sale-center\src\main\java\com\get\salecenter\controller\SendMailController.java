package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.service.ISendMailService;
import com.get.salecenter.dto.SendMailDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/7/1 16:29
 * @verison: 1.0
 * @description:
 */
@Api(tags = "邮件管理")
@RestController
@RequestMapping("sale/sendMail")
public class SendMailController {

    private ISendMailService sendMailService;


    /**
     * @return
     * @Description :邮件发送
     * @Param
     * <AUTHOR>
     */
    @ApiOperation(value = "邮件发送", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/邮件管理/邮件发送")
    @PostMapping("send")
    public ResponseBo send(@RequestBody SendMailDto sendMailDto) {
        sendMailService.send(sendMailDto);
        return ResponseBo.ok();
    }

    /**
     * @return
     * @Description :邮件发送
     * @Param
     * <AUTHOR>
     */
    @ApiOperation(value = "邮件发送", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/邮件管理/批量邮件发送")
    @PostMapping("batchSend")
    public ResponseBo batchSend() {
//        List<String> receiptCodes = sendMailService.getReceiptCodesByConventionId(conventionId);
//        for (String receiptCode : receiptCodes) {
//            List<ConventionRegistration> conventionRegistrationList = sendMailService.getConventionRegistrationByReceiptCode(receiptCode);
//
//
//        }
        List<SendMailDto> sendMailDtos = new ArrayList<>();
        SendMailDto sendMailDto11 = new SendMailDto();
        sendMailDto11.setName("Ted");
        sendMailDto11.setEmail("<EMAIL>");
        sendMailDto11.setReceiptCode("LESXRN18");
        sendMailDtos.add(sendMailDto11);

        for (SendMailDto sendMailDto : sendMailDtos) {
            //sendMailService.send(sendMailDto);
        }
        return ResponseBo.ok();
    }
}


