package com.get.schoolGateCenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.schoolGateCenter.dto.FileDto;
import com.get.schoolGateCenter.entity.ResumeSupplmentInformation;
import com.get.schoolGateCenter.entity.ResumeTargetSchool;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON>.
 * <AUTHOR> Kevin
 * Date: 12/16/2020 12:56 PM
 * Description:
 */
@Data
public class UserVo extends BaseVoEntity {
    /**
     * 来自平台类型：get_mso
     */
    @ApiModelProperty(value = "来自平台类型：get_mso")
    private String fkPlatformType;

    /**
     * 登陆用户Id
     */

    @ApiModelProperty(value = "登录用户id")
    private String loginId;

    /**
     * 登陆用户密码
     */
    @NotBlank(message = "用户密码不能为空", groups = {Add.class})
    @ApiModelProperty(value = "登陆用户密码", required = true)
    private String loginPs;

    @ApiModelProperty(value = "确认用户密码", required = true)
    private String confirmPs;

    /**
     * 会员编号
     */
//    @ApiModelProperty(value = "会员编号")
//    private String num;

    /**
     * 姓名（中文）
     */
    @NotNull(message = "姓名不能为空", groups = {Add.class})
    @ApiModelProperty(value = "真实姓名（中文）")
    private String name;

    /**
     * 姓名（英文）
     */
    @ApiModelProperty(value = "姓名（英文）")
    private String nameEn;

//    /**
//     * 微信昵称
//     */
//    @ApiModelProperty(value = "微信昵称")
//    private String wechatNickname;
//    /**
//     * 微信头像URL
//     */
//    @ApiModelProperty(value = "微信头像URL")
//    private String wechatIconUrl;
//
//    /**
//     * 微信openid
//     */
//    @ApiModelProperty(value = "微信openid")
//    private String wechatOpenid;
    /**
     * 姓（拼音）
     */
    @ApiModelProperty(value = "姓（拼音）")
    private String familyNamePy;

    /**
     * 名（拼音）
     */
    @ApiModelProperty(value = "名（拼音）")
    private String firstNamePy;

    /**
     * 昵称
     */
    @ApiModelProperty(value = "昵称")
    private String nickname;

    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男")
    private Integer gender;

    /**
     * 生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "生日,格式：yyyy-MM-dd")
    private Date birthday;

    /**
     * 身份证号
     */
//    @ApiModelProperty(value = "身份证号")
//    private String identityCard;

    /**
     * 移动电话
     */
    @NotBlank(message = "移动电话不能为空", groups = {Add.class})
    @ApiModelProperty(value = "移动电话", required = true)
    private String mobile;

    /**
     * 移动电话
     */
    @ApiModelProperty(value = "手机区号")
    @Column(name = "mobile_area_code")
    private String mobileAreaCode;

    /**
     * Email
     */
    @NotBlank(message = "Email不能为空", groups = {Add.class})
    @ApiModelProperty(value = "Email")
    private String email;

    /**
     * 国家Id
     */
//    @ApiModelProperty(value = "国家Id")
//    private Long fkAreaCountryId;
//
//    /**
//     * 州省Id
//     */
//    @ApiModelProperty(value = "州省Id")
//    private Long fkAreaStateId;
//
//    /**
//     * 城市Id
//     */
//    @ApiModelProperty(value = "城市Id")
//    private Long fkAreaCityId;

    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    private String zipCode;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    private String address;
//
//    /**
//     * QQ号
//     */
//    @ApiModelProperty(value = "QQ号")
//    private String qq;
//
//    /**
//     * 微信号
//     */
//    @ApiModelProperty(value = "微信号")
//    private String wechat;
//
//    /**
//     * whatsapp号
//     */
//    @ApiModelProperty(value = "whatsapp号")
//    private String whatsapp;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

    /**
     * 是否记住登录状态
     */
    @ApiModelProperty(value = "记住登录")
    private boolean rememberMe;


    /**
     * 短信验证码
     */
    @NotBlank(message = "手机验证码不能为空", groups = {Add.class})
    @ApiModelProperty(value = "手机验证码")
    private String verification;

    /**
     * 新密码
     */
    @ApiModelProperty(value = "新密码")
    private String newLoginPs;


//    @ApiModelProperty(value = "供应商ID")
//    private Long agentId;

    @ApiModelProperty(value = "验证码类型：register：注册验证码，reset：修改密码")
    private String type;

//    @ApiModelProperty(value = "用户组id")
//    private Long fkGroupId;

//    @ApiModelProperty(value = "用户组列表")
//    private List<UserGroup> userGroupDtoList;

//    @ApiModelProperty(value = "搜索关键词")
//    private String keyword;

    /**
     * 公司名称
     */
//    @ApiModelProperty(value = "公司名称")
//    @Column(name = "company")
//    private String company;
//
//    /**
//     * 职位
//     */
//    @ApiModelProperty(value = "职位")
//    @Column(name = "position")
//    private String position;
//    /**
//     * 邀请码
//     */
//    @ApiModelProperty(value = "邀请码")
//    private String invitationCode;
//
//    /**
//     * 大区Id
//     */
//    @ApiModelProperty(value = "大区Id")
//    private Long fkAreaRegionId;
//
//    /**
//     * 跨系统请求的时间戳
//     */
//    @ApiModelProperty(value = "跨系统请求的时间戳")
//    private Long currentTimeMillis;

    /**
     * 是否刷新token
     */
    @ApiModelProperty(value = "是否刷新token")
    private Boolean isRefreshToken;


    @ApiModelProperty(value = "BmsAgentId")
    private Long response;

    /**
//     * 是否需要校验密码(酒店小程序手机验证登录无需校验密码)
//     */
//    @ApiModelProperty(value = "是否需要校验密码(酒店小程序手机验证登录无需校验密码)")
//    private Boolean isCheckPs;
//
//    @ApiModelProperty(value = "聯係人id")
//    private Long contactPersonId;
//
//    @ApiModelProperty(value = "考卷id")
//    private Long examinationPaperId;
//
//
//    @ApiModelProperty(value = "bms当前语言")
//    private String bmsLanguageCode;

//    @ApiModelProperty(value = "公司id")
//    private Long fkCompanyId;
//
//    @ApiModelProperty("会员过期时间（奖学金小程序）")
//    private Date memberExpirationTime;
//
//    @ApiModelProperty(value ="会员id（奖学金小程序）")
//    private Long memberId;
//
//    @ApiModelProperty("会员类型：0游客/1代理/2学生（奖学金小程序专用字段）")
//    private Integer memberType;
//
//    @ApiModelProperty("请求域名")
//    private String requestUrlType;
//
//    @ApiModelProperty("学生来源")
//    private String stuSource;

    @ApiModelProperty("所属学校")
    private String institution;

    @ApiModelProperty("所属学校2")
    private String institution2;

    @ApiModelProperty("父亲姓")
    private String lastNameFather;

    @ApiModelProperty("父亲名")
    private String firstNameFather;

    @ApiModelProperty("父亲email")
    private String emailFather;

    @ApiModelProperty("母亲姓")
    private String lastNameMother;

    @ApiModelProperty("母亲名")
    private String firstNameMother;

    @ApiModelProperty("母亲email")
    private String emailMother;

    @ApiModelProperty("监护人姓")
    private String lastNameGuardian;

    @ApiModelProperty("监护人名")
    private String firstNameGuardian;

    @ApiModelProperty("监护人email")
    private String emailGuardian;

    @ApiModelProperty("文件内容")
    private List<FileDto> fileDtos;

    @ApiModelProperty("电话号码")
    private String phoneNo;

    @ApiModelProperty("额外信息")
    private List<ResumeSupplmentInformation> resumeSupplmentInformations;

    @ApiModelProperty("目标学校")
    private List<ResumeTargetSchool> resumeTargetSchools;
}
