package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_file_office")
public class FileOffice extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 文件guid
     */
    @ApiModelProperty(value = "文件guid")
    @Column(name = "file_guid")
    private String fileGuid;
    /**
     * 源文件类型
     */
    @ApiModelProperty(value = "源文件类型")
    @Column(name = "file_type_orc")
    private String fileTypeOrc;
    /**
     * 源文件名
     */
    @ApiModelProperty(value = "源文件名")
    @Column(name = "file_name_orc")
    private String fileNameOrc;
    /**
     * 目标文件名
     */
    @ApiModelProperty(value = "目标文件名")
    @Column(name = "file_name")
    private String fileName;
    /**
     * 目标文件路径
     */
    @ApiModelProperty(value = "目标文件路径")
    @Column(name = "file_path")
    private String filePath;
    /**
     * 文件外部存储Key（如：腾讯云COS）
     */
    @ApiModelProperty(value = "文件外部存储Key（如：腾讯云COS）")
    @Column(name = "file_key")
    private String fileKey;
}