package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @DATE: 2022/5/25
 * @TIME: 11:02
 * @Description:
 **/
@Data
@TableName("r_student_issue_student")
public class RStudentIssueStudent  extends BaseEntity {
    /**
     * 学生Id
     */
    @ApiModelProperty(value = "学生Id")
    @TableField("fk_student_id")
    private Long fkStudentId;

    /**
     * 学生Id（ISSUE申请）
     */
    @ApiModelProperty(value = "学生Id（ISSUE申请）")
    @TableField("fk_student_id_issue")
    @UpdateWithNull
    private Long fkStudentIdIssue;

    /**
     * 学生Id（ISSUEv2版申请）
     */
    @ApiModelProperty(value = "学生Id（ISSUEv2版申请）")
    @TableField("fk_student_id_issue2")
    @UpdateWithNull
    private Long fkStudentIdIssue2;
}
