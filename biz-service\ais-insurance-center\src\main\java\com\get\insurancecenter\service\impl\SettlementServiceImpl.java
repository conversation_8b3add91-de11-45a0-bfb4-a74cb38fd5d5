package com.get.insurancecenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.financecenter.dto.InsurancePaymentFormDto;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.financecenter.vo.InsurancePaymentFormVo;
import com.get.institutioncenter.entity.AreaCity;
import com.get.institutioncenter.entity.AreaCountry;
import com.get.institutioncenter.entity.AreaState;
import com.get.insurancecenter.dto.commission.AgentDto;
import com.get.insurancecenter.dto.commission.BindPayFormDto;
import com.get.insurancecenter.dto.SettlementOrderQueryDto;
import com.get.insurancecenter.entity.InsuranceOrder;
import com.get.insurancecenter.entity.InsuranceOrderSettlement;
import com.get.insurancecenter.entity.SettlementBillItem;
import com.get.insurancecenter.enums.SettlementStatusEnum;
import com.get.insurancecenter.mapper.*;
import com.get.insurancecenter.service.SettlementService;
import com.get.insurancecenter.vo.agent.AgentCompanyEntity;
import com.get.insurancecenter.vo.commission.*;
import com.get.insurancecenter.vo.order.OrderDetailVo;
import com.get.insurancecenter.vo.SettlementOrderVo;
import com.get.permissioncenter.vo.CompanyVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/5/27
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class SettlementServiceImpl extends ServiceImpl<InsuranceOrderSettlementMapper, InsuranceOrderSettlement> implements SettlementService {

    @Autowired
    private InsuranceOrderMapper insuranceOrderMapper;
    @Autowired
    private SaleCenterMapper saleCenterMapper;
    @Autowired
    private InstitutionCenterMapper institutionCenterMapper;
    @Autowired
    private SettlementBillItemMapper billItemMapper;
    @Autowired
    private InsuranceOrderSettlementMapper orderSettlementMapper;
    @Autowired
    private IFinanceCenterClient financeCenterClient;



    @Override
    public List<AgentVo> agentList(AgentDto params, Page page, Integer type) {
        IPage<AgentVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<Long> agentIds = insuranceOrderMapper.getOrderBySettlementStatus(getSettlementStatusByType(type))
                .stream().map(InsuranceOrder::getFkAgentId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(agentIds)) {
            page.setTotalPage(0);
            page.setTotalResult(0);
            return Collections.EMPTY_LIST;
        }
        List<AgentVo> agentList = saleCenterMapper.selectAgentList(pages, agentIds, params, type);
        agentList = fillAgentList(agentList, params, type);
        int totalCount = (int) pages.getTotal();
        Integer showCount = page.getShowCount();
        page.setTotalPage(page.getShowCount() != null && showCount > 0 ? totalCount % showCount == 0 ? totalCount / showCount : totalCount / showCount + 1 : 0);
        page.setTotalResult(totalCount);
        return agentList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchConfirm(List<Long> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            log.error("批量确认,订单为空:{}", JSONObject.toJSONString(orderIdList));
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "parameter_missing", "参数缺失"));
        }
        this.update(new LambdaUpdateWrapper<InsuranceOrderSettlement>()
                .set(InsuranceOrderSettlement::getStatusSettlement, SettlementStatusEnum.CONFIRMED.getCode())
                .set(InsuranceOrderSettlement::getGmtModified, new Date())
                .set(InsuranceOrderSettlement::getGmtModifiedUser, SecureUtil.getLoginId())
                .in(InsuranceOrderSettlement::getFkInsuranceOrderId, orderIdList));
    }

    @Override
    public List<SettledOrderVo> getSettledOrderList(AgentDto params, Page page) {
        //2-确认结算订单列表;3-已结算订单列表-按批次分组筛选
        List<Integer> status = getSettlementStatusByType(params.getType());
        List<SettledOrderVo> settledOrderList = orderSettlementMapper.selectSettledOrderList(page.getCurrentPage() - 1, page.getShowCount(), params, status);
        Integer count = orderSettlementMapper.selectSettledOrderCount(params, status);
        if (CollectionUtils.isEmpty(settledOrderList)) {
            page.setTotalPage(0);
            page.setTotalResult(0);
            return Collections.EMPTY_LIST;
        }
        //查询代理信息
        List<Long> agentIds = settledOrderList.stream().map(SettledOrderVo::getAgentId).distinct().collect(Collectors.toList());
        List<AgentVo> agentList = saleCenterMapper.selectAgentListByIds(agentIds);
        agentList = fillAgentList(agentList, params, null);
        //填充代理基本信息Map-无订单信息
        Map<Long, AgentVo> agentMap = agentList.stream()
                .collect(Collectors.toMap(
                        AgentVo::getAgentId,
                        Function.identity(),
                        (oldVal, newVal) -> newVal.getAgentId() > oldVal.getAgentId() ? newVal : oldVal));
        //查询代理账户信息Map
        List<Long> agentAccountIdList = settledOrderList.stream().map(SettledOrderVo::getAgentContractAccountId).distinct().collect(Collectors.toList());
        Map<Long, AgentAccountVo> accountMap = saleCenterMapper.getAgentAccountList(CollectionUtils.isNotEmpty(agentAccountIdList) ? agentAccountIdList : Arrays.asList(0L))
                .stream()
                .filter(account -> Objects.nonNull(account.getAccountId()))
                .collect(Collectors.toMap(
                        AgentAccountVo::getAccountId,
                        Function.identity()));

        //应收应付计划ID
        List<Long> payablePlanIds = new ArrayList<>();
        settledOrderList.stream().forEach(settledOrder -> {
            //代理信息
            settledOrder.setAgent(agentMap.getOrDefault(settledOrder.getAgentId(), new AgentVo()));
            //订单信息
            if (Objects.nonNull(settledOrder.getAgent()) && Objects.nonNull(settledOrder.getAgent().getAgentId())) {
                AgentVo agent = settledOrder.getAgent();
                List<OrderDetailVo> orderList = insuranceOrderMapper.selectCommissionOrderByAgentId(agent.getAgentId(), params.getInsurantName(), Arrays.asList(settledOrder.getStatusSettlement()));
                List<Long> agentPayablePlanIds = orderList.stream().filter(order -> Objects.nonNull(order.getPayablePlanId()))
                        .map(OrderDetailVo::getPayablePlanId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(agentPayablePlanIds)) {
                    payablePlanIds.addAll(agentPayablePlanIds);
                }
                agent.setOrderList(orderList);
                agent.setOrderCount(orderList.size());
                agent.setSettlementBillId(settledOrder.getSettlementBillId());
            }
        });

        //填充订单的额外信息-结算比例、应付金额、实付金额
        Map<Long, PayablePlanVo> planMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(payablePlanIds)) {
            planMap = saleCenterMapper.getPayablePlanList(payablePlanIds)
                    .stream()
                    .collect(Collectors.toMap(
                            PayablePlanVo::getPayablePlanId,
                            Function.identity()));
        }
        Map<Long, PayablePlanVo> finalPlanMap = planMap;
        settledOrderList.stream().forEach(settledOrder -> {
            List<OrderDetailVo> orderList = settledOrder.getAgent().getOrderList();
            if (CollectionUtils.isNotEmpty(orderList)) {
                orderList.stream().forEach(order -> {
                    //取应付计划的比例
                    order.setCommissionRate(finalPlanMap.getOrDefault(order.getPayablePlanId(), new PayablePlanVo()).getCommissionRate());
                    BigDecimal commissionRate = order.getCommissionRate();
                    if (Objects.isNull(commissionRate)) {
                        //默认百分之15
                        order.setCommissionRate(new BigDecimal(15));
                    }
                    //计算应付金额
                    order.setPayableAmount(order.getInsuranceAmount()
                            .multiply(new BigDecimal(order.getCommissionRate().toString()).divide(new BigDecimal(100)))
                            .setScale(2, RoundingMode.HALF_UP));
                    //计算实付金额-没有手续费的话，实付金额就是应付金额
                    order.setActualAmount(order.getPayableAmount());
                    if (Objects.nonNull(order.getServiceFee()) && order.getServiceFee().compareTo(BigDecimal.ZERO) > 0) {
                        order.setActualAmount(order.getActualAmount().subtract(order.getServiceFee()));
                        if (order.getActualAmount().compareTo(BigDecimal.ZERO) < 0) {
                            order.setActualAmount(BigDecimal.ZERO);
                        }
                    }
                });
            }
        });

        //填充账户信息
        settledOrderList.stream().forEach(settledOrder -> {
            //agentAccountMap-只有代理ID,结算单ID,结算币种,结算总额,账户ID
            AgentVo agent = settledOrder.getAgent();
            AgentAccountVo agentAccount = accountMap.getOrDefault(settledOrder.getAgentContractAccountId(), new AgentAccountVo());
            AgentAccountVo showAgentAccount = new AgentAccountVo();
            if (Objects.nonNull(agentAccount.getAccountId())) {
                //填充账户信息
                BeanUtils.copyProperties(agentAccount, showAgentAccount);
            }
            //结算单ID\结算币种\结算总额
            showAgentAccount.setSettlementBillId(settledOrder.getSettlementBillId());
            showAgentAccount.setSettlementCurrencyTypeNum(settledOrder.getSettlementCurrencyTypeNum());
            showAgentAccount.setSettlementAmount(settledOrder.getSettlementAmount());
            //查询汇率
            BigDecimal exchangeRete = billItemMapper.selectList(new LambdaQueryWrapper<SettlementBillItem>()
                            .eq(SettlementBillItem::getFkSettlementBillId, showAgentAccount.getSettlementBillId()))
                    .stream()
                    .map(item -> item.getExchangeRate())
                    .filter(Objects::nonNull)
                    .findFirst()
                    .orElse(null);
            showAgentAccount.setExchangeRate(exchangeRete);
            //支付金额、支付币种
            BigDecimal totalInsuranceAmount = agent.getOrderList().stream()
                    .map(OrderDetailVo::getInsuranceAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            String orderCurrencyTypeNum = agent.getOrderList().stream()
                    .map(OrderDetailVo::getFkCurrencyTypeNum)
                    .filter(Objects::nonNull)
                    .findFirst()
                    .orElse(null);

            showAgentAccount.setOrderAmount(totalInsuranceAmount);
            showAgentAccount.setOrderCurrencyTypeNum(orderCurrencyTypeNum);
            agent.setAgentAccount(showAgentAccount);

            //付款单绑定状态
            if (settledOrder.getStatusSettlement().equals(SettlementStatusEnum.COMPLETED.getCode())) {
                settledOrder.setIsBindPayBill(Boolean.TRUE);
            }
        });

        int totalCount = count;
        Integer showCount = page.getShowCount();
        page.setTotalPage(page.getShowCount() != null && showCount > 0 ? totalCount % showCount == 0 ? totalCount / showCount : totalCount / showCount + 1 : 0);
        page.setTotalResult(totalCount);
        return settledOrderList;
    }


    @Override
    public void confirmSettlement(List<String> numOptBatchList) {
        //确认结算-settlementStatus变成3
        if (CollectionUtils.isNotEmpty(numOptBatchList)) {
            this.update(new LambdaUpdateWrapper<InsuranceOrderSettlement>()
                    .set(InsuranceOrderSettlement::getStatusSettlement, SettlementStatusEnum.FINANCE_CONFIRMED.getCode())
                    .set(InsuranceOrderSettlement::getGmtModified, new Date())
                    .set(InsuranceOrderSettlement::getGmtModifiedUser, SecureUtil.getLoginId())
                    .in(InsuranceOrderSettlement::getFkNumOptBatch, numOptBatchList));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindPayForm(BindPayFormDto bindPayFormDto) {
        InsurancePaymentFormDto insurancePaymentFormDto = new InsurancePaymentFormDto();
        BeanUtils.copyProperties(bindPayFormDto, insurancePaymentFormDto);
        //填充应付计划ID
        Set<Long> payablePlanIds = orderSettlementMapper.selectList(new LambdaQueryWrapper<InsuranceOrderSettlement>()
                        .eq(InsuranceOrderSettlement::getFkNumOptBatch, bindPayFormDto.getFkNumOptBatch())
                        .eq(InsuranceOrderSettlement::getStatusSettlement, SettlementStatusEnum.FINANCE_CONFIRMED.getCode()))
                .stream().map(item -> item.getFkPayablePlanId()).collect(Collectors.toSet());
        insurancePaymentFormDto.setFkPayablePlanIds(payablePlanIds);
        log.info("调用财务中心创建付款单,参数:{}", JSONObject.toJSONString(insurancePaymentFormDto));
        Result<List<InsurancePaymentFormVo>> result = financeCenterClient.createInsurancePaymentForm(insurancePaymentFormDto);
        if (!result.isSuccess()) {
            log.error("调用财务中心创建付款单失败,参数:{},错误信息:{}", JSONObject.toJSONString(insurancePaymentFormDto), result.getMessage());
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "feign_execution_failed", "feign执行失败"));
        }
        List<InsurancePaymentFormVo> list = result.getData();
        //批量绑定付款单-settlementStatus变成4
        if (CollectionUtils.isNotEmpty(list)) {
            orderSettlementMapper.batchUpdatePaymentFormItemId(list, SecureUtil.getLoginId());
        }
    }

    private AgentInfoMap initAgentInfoMap(List<AgentVo> agentList) {
        AgentInfoMap agentInfoMap = new AgentInfoMap();
        if (CollectionUtils.isEmpty(agentList)) {
            return agentInfoMap;
        }
        List<Long> currentAgentIds = agentList.stream().map(AgentVo::getAgentId).distinct().collect(Collectors.toList());
        List<Long> currentCountryIds = agentList.stream().map(AgentVo::getAreaCountryId).distinct().collect(Collectors.toList());
        List<Long> currentAreaStateIds = agentList.stream().map(AgentVo::getAreaStateId).distinct().collect(Collectors.toList());
        List<Long> currentAreaCityIds = agentList.stream().map(AgentVo::getAreaCityId).distinct().collect(Collectors.toList());
        List<AgentCompanyEntity> agentCompanyList = saleCenterMapper.selectAgentCompanyList(currentAgentIds);
        Map<Long, List<Long>> agentCompanyMap = agentCompanyList.stream()
                .filter(item -> item.getFkAgentId() != null && item.getFkCompanyId() != null)
                .collect(Collectors.groupingBy(
                        AgentCompanyEntity::getFkAgentId,
                        Collectors.mapping(AgentCompanyEntity::getFkCompanyId, Collectors.toList())
                ));
        agentInfoMap.setAgentCompanyMap(agentCompanyMap);
        //国家Map
        Map<Long, AreaCountry> countryMap = institutionCenterMapper.selectAreaCountryList(currentCountryIds)
                .stream().collect(Collectors.toMap(
                        AreaCountry::getId,
                        Function.identity()
                ));
        agentInfoMap.setCountryMap(countryMap);
        //州省Map
        Map<Long, AreaState> areaStateMap = institutionCenterMapper.selectAreaStateList(currentAreaStateIds)
                .stream().collect(Collectors.toMap(
                        AreaState::getId,
                        Function.identity()
                ));
        agentInfoMap.setAreaStateMap(areaStateMap);
        //城市Map
        Map<Long, AreaCity> areaCityMap = institutionCenterMapper.selectAreaCityList(currentAreaCityIds)
                .stream().collect(Collectors.toMap(
                        AreaCity::getId,
                        Function.identity()
                ));
        agentInfoMap.setAreaCityMap(areaCityMap);
        //分公司Map
        if (Objects.nonNull(agentCompanyMap)) {
            List<Long> allCompanyIds = agentCompanyMap.values().stream()
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(allCompanyIds)) {
                Map<Long, CompanyVo> companyMap = saleCenterMapper.selectCompanyList(allCompanyIds)
                        .stream().collect(Collectors.toMap(
                                CompanyVo::getId,
                                Function.identity()));
                agentInfoMap.setCompanyMap(companyMap);
                Map<Long, List<String>> agentCompanyNamesMap = agentCompanyMap.entrySet().stream()
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                entry -> entry.getValue().stream()
                                        .map(companyMap::get)
                                        .filter(Objects::nonNull)
                                        .map(CompanyVo::getName)
                                        .collect(Collectors.toList())
                        ));
                Map<Long, List<String>> agentCompanyNumMap = agentCompanyMap.entrySet().stream()
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                entry -> entry.getValue().stream()
                                        .map(companyMap::get)
                                        .filter(Objects::nonNull)
                                        .map(CompanyVo::getNum)
                                        .collect(Collectors.toList())
                        ));
                agentInfoMap.setAgentCompanyNameMap(agentCompanyNamesMap);
                agentInfoMap.setAgentCompanyNumMap(agentCompanyNumMap);
            }
        }
        return agentInfoMap;
    }

    private List<Integer> getSettlementStatusByType(Integer type) {
        //列表类型:0待确认订单列表-结算状态=0,没有展示在小程序端的结算订单那
        // 1待提交结算订单列表-结算状态=1,小程序端代理未提交的结算订单
        // 2确认结算订单列表-结算状态=2,小程序代理已经提交结算的订单
        // 3已结算订单-结算状态是3和4
        switch (type) {
            case 1:
                return Collections.singletonList(SettlementStatusEnum.CONFIRMED.getCode());
            case 2:
                return Collections.singletonList(SettlementStatusEnum.AGENT_CONFIRMED.getCode());
            case 3:
                return Arrays.asList(SettlementStatusEnum.FINANCE_CONFIRMED.getCode(), SettlementStatusEnum.COMPLETED.getCode());
            case 0:
            default:
                return Collections.singletonList(SettlementStatusEnum.TO_BE_CONFIRM.getCode());
        }
    }

    private List<AgentVo> fillAgentList(List<AgentVo> agentList, AgentDto params, Integer type) {
        AgentInfoMap agentInfoMap = initAgentInfoMap(agentList);
        List<Long> payablePlanIds = new ArrayList<>();
        agentList.stream().forEach(agent -> {
            //国家名称
            agent.setAreaCountryName(agentInfoMap.getCountryMap().getOrDefault(agent.getAreaCountryId(), new AreaCountry()).getName());
            agent.setAreaCountryNameChn(agentInfoMap.getCountryMap().getOrDefault(agent.getAreaCountryId(), new AreaCountry()).getNameChn());
            //州省名称
            agent.setAreaStateName(agentInfoMap.getAreaStateMap().getOrDefault(agent.getAreaStateId(), new AreaState()).getName());
            agent.setAreaStateNameChn(agentInfoMap.getAreaStateMap().getOrDefault(agent.getAreaStateId(), new AreaState()).getNameChn());
            //城市名称
            agent.setAreaCityName(agentInfoMap.getAreaCityMap().getOrDefault(agent.getAreaCityId(), new AreaCity()).getName());
            agent.setAreaCityNameChn(agentInfoMap.getAreaCityMap().getOrDefault(agent.getAreaCityId(), new AreaCity()).getNameChn());
            //所属分公司IDS
            agent.setCompanyIds(agentInfoMap.getAgentCompanyMap().getOrDefault(agent.getAgentId(), Collections.emptyList()));
            //所属分公司名称
            agent.setCompanyNames(agentInfoMap.getAgentCompanyNameMap().getOrDefault(agent.getAgentId(), Collections.emptyList()));
            //所属分公司NUM
            agent.setCompanyNums(agentInfoMap.getAgentCompanyNumMap().getOrDefault(agent.getAgentId(), Collections.emptyList()));
            //订单列表
            if (Objects.nonNull(type)) {
                List<OrderDetailVo> orderList = insuranceOrderMapper.selectCommissionOrderByAgentId(agent.getAgentId(), params.getInsurantName(), getSettlementStatusByType(type));
                List<Long> agentPayablePlanIds = orderList.stream().filter(order -> Objects.nonNull(order.getPayablePlanId()))
                        .map(OrderDetailVo::getPayablePlanId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(agentPayablePlanIds)) {
                    payablePlanIds.addAll(agentPayablePlanIds);
                }
                agent.setOrderList(orderList);
                agent.setOrderCount(orderList.size());
            }
        });
        //设置结算比例和应付金额
        if (Objects.nonNull(type)) {
            Map<Long, PayablePlanVo> planMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(payablePlanIds)) {
                planMap = saleCenterMapper.getPayablePlanList(payablePlanIds)
                        .stream()
                        .collect(Collectors.toMap(
                                PayablePlanVo::getPayablePlanId,
                                Function.identity()));
            }
            Map<Long, PayablePlanVo> finalPlanMap = planMap;
            agentList.stream().forEach(agent -> {
                agent.getOrderList().stream().forEach(order -> {
                    //取应付计划的比例
                    order.setCommissionRate(finalPlanMap.getOrDefault(order.getPayablePlanId(), new PayablePlanVo()).getCommissionRate());
                    BigDecimal commissionRate = order.getCommissionRate();
                    if (Objects.isNull(commissionRate)) {
                        //默认百分之15
                        order.setCommissionRate(new BigDecimal(15));
                    }
                    //计算应付金额
                    order.setPayableAmount(order.getInsuranceAmount()
                            .multiply(new BigDecimal(order.getCommissionRate().toString()).divide(new BigDecimal(100)))
                            .setScale(2, RoundingMode.HALF_UP));
                    //计算实付金额-没有手续费的话，实付金额就是应付金额
                    order.setActualAmount(order.getPayableAmount());
                    if (Objects.nonNull(order.getServiceFee()) && order.getServiceFee().compareTo(BigDecimal.ZERO) > 0) {
                        order.setActualAmount(order.getActualAmount().subtract(order.getServiceFee()));
                        if (order.getActualAmount().compareTo(BigDecimal.ZERO) < 0) {
                            order.setActualAmount(BigDecimal.ZERO);
                        }
                    }
                });
            });
        }
        return agentList;
    }

    @Override
    public List<SettlementOrderVo> getSettlementOrderList(SettlementOrderQueryDto queryDto, Page page) {
        IPage<SettlementOrderVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));

        // 查询数据（多表关联）
        List<SettlementOrderVo> list = orderSettlementMapper.selectSettlementOrderList(pages, queryDto);

        if (CollectionUtils.isNotEmpty(list)) {
            // 设置订单状态名称
            list.forEach(item -> {
                SettlementStatusEnum statusEnum = SettlementStatusEnum.getEnumByCode(item.getOrderStatus());
                if (statusEnum != null) {
                    item.setOrderStatusName(statusEnum.getMsg());
                }
            });
        }

        // 设置分页信息到传入的Page对象
        int totalCount = (int) pages.getTotal();
        Integer showCount = page.getShowCount();
        page.setTotalPage(showCount != null && showCount > 0 ?
            totalCount % showCount == 0 ? totalCount / showCount : totalCount / showCount + 1 : 0);
        page.setTotalResult(totalCount);
        page.setCurrentResult(list.size());

        return list;
    }
}
