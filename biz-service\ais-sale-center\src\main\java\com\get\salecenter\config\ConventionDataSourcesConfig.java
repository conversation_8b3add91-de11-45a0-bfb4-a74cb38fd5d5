//package com.get.salecenter.config;
//
//import com.alibaba.druid.pool.DruidDataSource;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.mybatis.spring.SqlSessionFactoryBean;
//import org.mybatis.spring.SqlSessionTemplate;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//import org.springframework.jdbc.datasource.DataSourceTransactionManager;
//
//import javax.sql.DataSource;
//
///**
// * 次数据源配置
// */
//@Configuration
//@MapperScan(basePackages = "com.get.salecenter.dao.convention", sqlSessionTemplateRef = "conventionSqlSessionTemplate")
//public class ConventionDataSourcesConfig {
//    @Bean(name = "conventionDataSource")
//    @ConfigurationProperties(prefix = "conventiondb.datasource")
//    public DataSource testDataSource() {
//        return new DruidDataSource();
//    }
//
//    @Bean(name = "conventionSqlSessionFactory")
//    public SqlSessionFactory testSqlSessionFactory(@Qualifier("conventionDataSource") DataSource dataSource) throws Exception {
//        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
//        bean.setDataSource(dataSource);
//        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/convention/*.xml"));
//        //mybatis 数据库字段与实体类属性驼峰映射配置
//        bean.getObject().getConfiguration().setMapUnderscoreToCamelCase(true);
//        return bean.getObject();
//    }
//
//    @Bean(name = "conventionTransactionManager")
//    public DataSourceTransactionManager testTransactionManager(@Qualifier("conventionDataSource") DataSource dataSource) {
//        return new DataSourceTransactionManager(dataSource);
//    }
//
//    @Bean(name = "conventionSqlSessionTemplate")
//    public SqlSessionTemplate testSqlSessionTemplate(@Qualifier("conventionSqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
//        return new SqlSessionTemplate(sqlSessionFactory);
//    }
//}
