package com.get.institutioncenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/7
 * @TIME: 11:47
 * @Description:提供商dto
 **/
@Data
@ApiModel("学校提供商返回类")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class InstitutionProviderVo extends BaseEntity {

    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    private String typeName;

    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;

    /**
     * 州省Id
     */
    @ApiModelProperty(value = "州省名称")
    private String fkAreaStateName;

    /**
     * 城市Id
     */
    @ApiModelProperty(value = "城市名称")
    private String fkAreaCityName;

    /**
     * 集团名称
     */
    @ApiModelProperty(value = "集团名称")
    private String fkInstitutionGroupName;


    @ApiModelProperty(value = "是否被选中")
    private Boolean isSelect;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 全称
     */
    @ApiModelProperty(value = "全称")
    private String fullName;

    /**
     * 业务国家名称
     */
    @ApiModelProperty(value = "业务国家名称")
    private String areaCountryNames;

    /**
     * 业务国家名称
     */
    @ApiModelProperty(value = "业务国家id")
    private List<Long> areaCountryIds;


    /**
     * 中间表id
     */
    @ApiModelProperty(value = "中间表id")
    private Long ipiId;
    /**
     * 公开对象名称
     */
    @ApiModelProperty(value = "公开对象名称")
    private String publicLevelName;


    /**
     * 所属渠道名称
     */
    @ApiModelProperty(value = "所属渠道名称")
    private String fkInstitutionChannelName;

    /**
     * 学校数量
     */
    @ApiModelProperty(value = "学校数量")
    private Integer institutionCount;

    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "渠道名称")
    private String institutionChannelNames;

    /**
     * 所属渠道Ids
     */
    @ApiModelProperty(value = "所属渠道Ids")
    private List<Long> institutionChannelIds;
    /**
     * 是否合作
     */
    @ApiModelProperty(value = "合作状态：0无/1有")
    private Boolean isBindingActive;

    /**
     * 提供商id
     */
    @ApiModelProperty(value = "提供商id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "枚举状态：0不参加/1参加/2待定")
    private Integer eventRegistrationStatus;


    @ApiModelProperty(value = "备注")
    private String remark;

    //=================实体类InstitutionProvider==========================
    private static final long serialVersionUID = 1L;
    /**
     * 国家Id（所在地）
     */
    @ApiModelProperty(value = "国家Id（所在地）")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 州省Id（所在地）
     */
    @ApiModelProperty(value = "州省Id（所在地）")
    @Column(name = "fk_area_state_id")
    private Long fkAreaStateId;
    /**
     * 城市Id（所在地）
     */
    @ApiModelProperty(value = "城市Id（所在地）")
    @Column(name = "fk_area_city_id")
    private Long fkAreaCityId;
    /**
     * 学校提供商类型Id：渠道=1，集团=2，学校=3
     */
    @ApiModelProperty(value = "学校提供商类型Id：渠道=1，集团=2，学校=3")
    @Column(name = "fk_institution_provider_type_id")
    private Long fkInstitutionProviderTypeId;
    /**
     * 所属集团Id
     */
    @ApiModelProperty(value = "所属集团Id")
    @Column(name = "fk_institution_group_id")
    @UpdateWithNull
    private Long fkInstitutionGroupId;
    /**
     * 渠道来源Id
     */
    @ApiModelProperty(value = "渠道来源Id")
    @Column(name = "fk_institution_channel_id")
    private Long fkInstitutionChannelId;
    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @Column(name = "num")
    private String num;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Column(name = "name")
    private String name;
    /**
     * 中文名称
     */
    @ApiModelProperty(value = "中文名称")
    @Column(name = "name_chn")
    private String nameChn;
    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    @Column(name = "zip_code")
    private String zipCode;
    /**
     * 电邮
     */
    @ApiModelProperty(value = "电邮")
    @Column(name = "email")
    private String email;
    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    @Column(name = "address")
    private String address;
    /**
     * 申请佣金截止时间
     */
    @ApiModelProperty(value = "申请佣金截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "app_commission_deadline")
    private Date appCommissionDeadline;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @Column(name = "public_level")
    private String publicLevel;
    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    @Column(name = "id_gea")
    private String idGea;
    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    @Column(name = "id_iae")
    private String idIae;

    /**
     * 合同状态：0未有合同/1有合同（生效中/已过期）/2续约中
     */
    @ApiModelProperty(value = "合同状态：0未有合同/1有合同（生效中/已过期）/2续约中")
    private Integer contractStatus;

    /**
     * 合同状态名称
     */
    @ApiModelProperty(value = "合同状态名称")
    private String contractStatusName;
}
