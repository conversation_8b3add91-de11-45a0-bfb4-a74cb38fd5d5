package com.get.resumecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("u_skill_type")
public class SkillType extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    @Column(name = "type_name")
    private String typeName;
    /**
     * 子类型名称
     */
    @ApiModelProperty(value = "子类型名称")
    @Column(name = "sub_type_name")
    private String subTypeName;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    @Column(name = "view_order")
    private Integer viewOrder;

}