<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.EventPlanThemeOfflineItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.EventPlanThemeOfflineItem">
        <id column="id" property="id" />
        <result column="fk_event_plan_theme_offline_id" property="fkEventPlanThemeOfflineId" />
        <result column="location" property="location" />
        <result column="date" property="date" />
        <result column="quota" property="quota" />
        <result column="fk_currency_type_num" property="fkCurrencyTypeNum" />
        <result column="amount" property="amount" />
        <result column="unit" property="unit" />
        <result column="view_order" property="viewOrder" />
        <result column="is_active" property="isActive" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_create_user" property="gmtCreateUser" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="gmt_modified_user" property="gmtModifiedUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, fk_event_plan_theme_offline_id, location, date, quota, fk_currency_type_num, amount, unit, view_order, is_active, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
    </sql>

    <select id="getMaxViewOrder" resultType="java.lang.Integer">
        SELECT
        max(view_order)+1 view_order
        FROM
        m_event_plan_theme_offline_item
        <where>
            AND fk_event_plan_theme_offline_id = #{fkEventPlanThemeOfflineId}
        </where>
    </select>

</mapper>
