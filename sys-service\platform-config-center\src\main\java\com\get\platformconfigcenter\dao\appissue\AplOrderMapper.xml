<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.platformconfigcenter.dao.appissue.AplOrderMapper">
    <!--TODO 注释sql-->
<!--    <select id="getRobotStatusById" resultType="java.lang.String">-->
<!--        SELECT robot_status FROM apl_order WHERE order_id=#{orderId}-->
<!--    </select>-->

<!--    <select id="getRobotStatusByIds" resultType="com.get.platformconfigcenter.vo.AplOrderVo">-->
<!--        SELECT order_id,robot_status FROM apl_order-->
<!--        <where>-->
<!--            and order_id in-->
<!--            <foreach collection="orderIds" item="orderId" index="index" open="(" separator="," close=")">-->
<!--                #{orderId}-->
<!--            </foreach>-->
<!--        </where>-->
<!--    </select>-->
</mapper>