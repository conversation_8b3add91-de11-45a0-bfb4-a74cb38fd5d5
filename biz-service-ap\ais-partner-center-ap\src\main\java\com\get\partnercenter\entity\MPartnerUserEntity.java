package com.get.partnercenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2024-12-20 14:10:35
 */

@Data
@TableName("m_partner_user")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MPartnerUserEntity extends BaseEntity  implements Serializable {

  private static final long serialVersionUID = 1L;


  @ApiModelProperty("租户Id")
  private Long fkTenantId;
 

  @ApiModelProperty("公司Id")
  private Long fkCompanyId;
 

  @ApiModelProperty("学生代理Id")
  private Long fkAgentId;
 

  @ApiModelProperty("用户Id")
  private Long fkUserId;
 

  @ApiModelProperty("姓名（中文）")
  private String name;
 

  @ApiModelProperty("姓名（英文）")
  private String nameEn;
 

  @ApiModelProperty("昵称")
  private String nickname;
 

//  @ApiModelProperty("性别：0女/1男")
//  private Integer gender;
 

  @ApiModelProperty("生日")
  private LocalDateTime birthday;
 

  @ApiModelProperty("手机区号")
  private String mobileAreaCode;
 

  @ApiModelProperty("移动电话")
  private String mobile;
 

  @ApiModelProperty("电话区号")
  private String telAreaCode;
 

  @ApiModelProperty("电话")
  private String tel;
 

  @ApiModelProperty("邮箱地址")
  private String email;
 

  @ApiModelProperty("公司名称")
  private String company;
 

  @ApiModelProperty("部门")
  private String department;
 

  @ApiModelProperty("职位")
  private String position;
 

  @ApiModelProperty("QQ号")
  private String qq;
 

  @ApiModelProperty("whatsapp号")
  private String whatsapp;
 

  @ApiModelProperty("微信号")
  private String wechat;
 

  @ApiModelProperty("微信昵称")
  private String wechatNickname;
 

  @ApiModelProperty("微信头像URL")
  private String wechatIconUrl;
 

  @ApiModelProperty("身份校验（手机或邮件验证），0否/1是")
  private Boolean isIdentityChecked;
 

  @ApiModelProperty("是否强制修改密码，0否/1是")
  private Boolean isModifiedPs;
 

  @ApiModelProperty("是否激活：0否/1是")
  private Boolean isActive;

  @ApiModelProperty("登陆用户Id")
  @TableField(value = "fk_user_login_id")
  private String fkUserLoginId;

  @ApiModelProperty("是否超级管理员0否1是")
  private Integer isAdmin;

}
