package com.get.institutioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("m_institution_course_rpa_mapping")
public class InstitutionCourseRpaMapping extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * RPA课程地图Id
     */
    @ApiModelProperty(value = "RPA课程地图Id")
    @Column(name = "id")
    private Long id;
    /**
     * 学校Id(旧系统ISSUE Id)
     */
    @ApiModelProperty(value = "学校Id(旧系统ISSUE Id)")
    @Column(name = "sch_id")
    private Integer schId;
    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    @Column(name = "gb_sch_name")
    private String gbSchName;
    /**
     * 获得数据源列名称
     */
    @ApiModelProperty(value = "获得数据源列名称")
    @Column(name = "name")
    private String name;
    /**
     * 获得数据源Key
     */
    @ApiModelProperty(value = "获得数据源Key")
    @Column(name = "key")
    private String key;
    /**
     * 获得数据源Value
     */
    @ApiModelProperty(value = "获得数据源Value")
    @Column(name = "value")
    private String value;
    /**
     * 父id，针对key
     */
    @ApiModelProperty(value = "父id，针对key")
    @Column(name = "parent_id")
    private Integer parentId;
    /**
     * 步骤等级
     */
    @ApiModelProperty(value = "步骤等级")
    @Column(name = "step_level")
    private Integer stepLevel;
    /**
     * 分支：0上1下
     */
    @ApiModelProperty(value = "分支：0上1下")
    @Column(name = "branch")
    private String branch;
    /**
     * 课程名
     */
    @ApiModelProperty(value = "课程名")
    @Column(name = "course_name")
    private String courseName;
    /**
     * 学位
     */
    @ApiModelProperty(value = "学位")
    @Column(name = "qualifications")
    private String qualifications;
    /**
     * 网址
     */
    @ApiModelProperty(value = "网址")
    @Column(name = "web")
    private String web;
    /**
     * 课程编号
     */
    @ApiModelProperty(value = "课程编号")
    @Column(name = "code")
    private String code;
    /**
     * 是否已经过时？ F=未过时 ，T = 已过时
     */
    @ApiModelProperty(value = "是否已经过时？ F=未过时 ，T = 已过时")
    @Column(name = "outdated")
    private String outdated;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @Column(name = "createtime")
    private Date createtime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @Column(name = "udpatetime")
    private Date udpatetime;
}