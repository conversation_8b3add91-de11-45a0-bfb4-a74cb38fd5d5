package com.get.insurancecenter.utils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Random;
import java.util.UUID;

public class OrderNoUtil {

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMddHHmmssSSS");
    private static final Random RANDOM = new Random();
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    /**
     * 生成订单号：格式为 yyyyMMddHHmmssSSS + 6位随机数
     * 示例：2025042915321278804162
     */
    public static String generateOrderNo() {
        String timestamp = DATE_FORMAT.format(new Date());

        // 生成6位随机数字，不足补0
        int randomNumber = RANDOM.nextInt(999999);
        String randomStr = String.format("%06d", randomNumber);

        return timestamp + randomStr;
    }

    /**
     * 生成结算批次号：格式为 yyyyMMddHHmmssSSS + 8位随机数
     * @return
     */
    public static String generateSettlementBatchNo() {
        String timestamp = DATE_FORMAT.format(new Date());

        // 生成6位随机数字，不足补0
        int randomNumber = RANDOM.nextInt(99999999);
        String randomStr = String.format("%06d", randomNumber);

        return timestamp + randomStr;
    }

        public static String generateInsuranceNo() {
            return UUID.randomUUID()
                    .toString()
                    .replaceAll("-", "")   // 移除短横线
                    .toUpperCase()         // 转成大写
                    .substring(0, 20);     // 截取前20位
    }

    /**
     * 生成交易号：格式为 yyyyMMddHHmmssSSS + 6位随机数 + 2位
     */
    public static String generateTradeNo() {
        // 当前日期
        String dateStr = LocalDate.now().format(DATE_FORMATTER);

        // 生成 6 位随机数字（左补0）
        int number = RANDOM.nextInt(1_000_000); // 0 ~ 999999
        String numberStr = String.format("%06d", number);

        // 生成 2 位随机大写字母
        String letters = randomUpperLetters(2);

        return dateStr + numberStr + letters;
    }

    private static String randomUpperLetters(int count) {
        StringBuilder sb = new StringBuilder(count);
        for (int i = 0; i < count; i++) {
            char c = (char) ('A' + RANDOM.nextInt(26));
            sb.append(c);
        }
        return sb.toString();
    }
}
