<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.get.salecenter.dao.sale.StudentServiceFeeMapper">

    <select id="datas" resultType="com.get.salecenter.vo.StudentServiceFeeVo">
        SELECT
            t.type_name as serviceTypeName,
            CONCAT(m.`name`,'(',m.name_en,')') as bdName,
            CASE WHEN
                a.name_note is not null AND a.name_note!=''
            THEN
                CONCAT(a.`name`,'(',a.name_note,')')
            ELSE
                a.`name`
            END as agentName,
            IF(f.`status`=1,'有效','作废') as statusName,
            CONCAT(b.type_name,'（',b.num,'）') as fkCurrencyNumName,
            g.receivable_plan_amount_info AS receivableAmountInfo,
            h.payable_plan_amount_info AS payableAmountInfo,
            f.*
        FROM
            m_student_service_fee f
        LEFT JOIN m_student s ON s.id = f.fk_student_id
        LEFT JOIN u_student_service_fee_type t ON t.id = f.fk_student_service_fee_type_id
        LEFT JOIN ais_permission_center.m_staff m ON m.id = f.fk_staff_id
        LEFT JOIN m_agent a ON a.id = f.fk_agent_id
        LEFT JOIN ais_finance_center.u_currency_type b ON b.num = f.fk_currency_type_num
        LEFT JOIN (
        SELECT
        a.fk_type_target_id,
        GROUP_CONCAT(
        concat(
        CAST(a.receivable_amount AS CHAR),
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) receivable_plan_amount_info
        FROM
        (
        SELECT
        a.fk_type_target_id,
        a.fk_currency_type_num,
        c.type_name,
        SUM(
        IFNULL(a.receivable_amount, 0)
        ) receivable_amount

        FROM
        ais_sale_center.m_receivable_plan a
        LEFT JOIN ais_finance_center.u_currency_type c ON a.fk_currency_type_num = c.num
        WHERE
        a.fk_type_key = 'm_student_service_fee'
        AND a.`status` != 0
        GROUP BY
        a.fk_type_target_id,
        a.fk_currency_type_num,
        c.type_name
        ) a
        GROUP BY
        a.fk_type_target_id
        ) g ON f.id = g.fk_type_target_id
        LEFT JOIN (
        SELECT
        a.fk_type_target_id,
        GROUP_CONCAT(
        concat(
        CAST(a.payable_amount AS CHAR),
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) payable_plan_amount_info
        FROM
        (
        SELECT
        a.fk_type_target_id,
        a.fk_currency_type_num,
        c.type_name,
        SUM(IFNULL(a.payable_amount, 0)) payable_amount
        FROM
        ais_sale_center.m_payable_plan a
        LEFT JOIN ais_finance_center.u_currency_type c ON a.fk_currency_type_num = c.num
        WHERE
        a.fk_type_key = 'm_student_service_fee'
        AND a.`status` != 0
        GROUP BY
        a.fk_type_target_id,
        a.fk_currency_type_num,
        c.type_name
        ) a
        GROUP BY
        a.fk_type_target_id
        ) h ON f.id = h.fk_type_target_id
        WHERE
            1=1
            <if test="studentServiceFeeDto.fkStudentId!=null and studentServiceFeeDto.fkStudentId!=''">
                AND f.fk_student_id = #{studentServiceFeeDto.fkStudentId}
            </if>
    </select>
    <select id="getServiceFee" resultType="com.get.salecenter.vo.StudentServiceFeeVo">
        SELECT
            msa.*,mbc.type_name as serviceTypeName
        FROM
            m_student_service_fee msa
        INNER JOIN m_student ms ON ms.id = msa.fk_student_id
        INNER JOIN u_student_service_fee_type mbc ON mbc.id = msa.fk_student_service_fee_type_id
        WHERE msa.status != 0
        <if test="keyWord!=null">
            AND ((ms.name LIKE CONCAT('%', #{keyWord}, '%') OR ms.last_name LIKE CONCAT('%', #{keyWord}, '%') OR ms.first_name LIKE CONCAT('%', #{keyWord}, '%'))
            or (mbc.type_name LIKE CONCAT('%', #{keyWord}, '%') or mbc.type_name LIKE CONCAT('%', #{keyWord}, '%')))
        </if>
        <if test="companyIds!=null and companyIds.size>0">
            and  ms.fk_company_id in
            <foreach collection="companyIds" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>

        </if>
        GROUP BY msa.id
        ORDER BY
        LENGTH(ms.name),LENGTH(mbc.type_name)
        limit 20
    </select>
    <select id="findById" resultType="com.get.salecenter.vo.StudentServiceFeeVo">
        SELECT
            f.*,t.type_name as serviceTypeName
        FROM
            m_student_service_fee f
        INNER JOIN u_student_service_fee_type t ON t.id = f.fk_student_service_fee_type_id
        WHERE f.id = #{id}
    </select>
    <select id="getStudentIdsByTargetName" resultType="java.lang.Long">
        SELECT DISTINCT
            f.fk_student_id
        FROM
            m_student_service_fee f
        INNER JOIN m_student a ON a.id = f.fk_student_id
        <if test="targetName!=null and targetName!=''">
            WHERE
            (
            REPLACE(CONCAT(LOWER(a.first_name),LOWER(a.last_name)),' ','') like concat('%',#{studentDto.name},'%')
            OR REPLACE(CONCAT(LOWER(a.last_name),LOWER(a.first_name)),' ','') like concat('%',#{studentDto.name},'%')
            OR REPLACE(LOWER(a.`name`),' ','') like concat('%',#{studentDto.name},'%')
            OR REPLACE(LOWER(a.last_name),' ','') like concat('%',#{studentDto.name},'%')
            OR REPLACE(LOWER(a.first_name),' ','') like concat('%',#{studentDto.name},'%')) -- 过滤学生中英文名字
        </if>
    </select>
    <select id="getStudentListByCompanyId" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            s.id,CONCAT(s.`name`,'（',s.last_name,s.first_name,'）') as `name`
        FROM
            ais_sale_center.m_student AS s
                INNER JOIN ais_sale_center.m_student_service_fee AS mssf ON mssf.fk_type_target_id_receivable = s.id AND mssf.fk_type_key_receivable = 'm_student'
                INNER JOIN ais_sale_center.m_receivable_plan AS mrp ON mrp.fk_type_target_id = mssf.id AND mrp.fk_type_key = 'm_student_service_fee'
        WHERE mrp.fk_company_id  = #{companyId}
        GROUP BY s.id
    </select>
    <select id="getStudentServiceFeeReceivablePlan" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT i.id,
               CONCAT(
                      CASE WHEN IFNULL(CONCAT(ms.first_name,ms.last_name), '') = '' THEN ms.name ELSE CONCAT(ms.name, '（', CONCAT(ms.first_name," ",ms.last_name), '）') END,
                        "/",
                      (SELECT GROUP_CONCAT(name) FROM ais_institution_center.u_area_country WHERE FIND_IN_SET(id,mssf.fk_area_country_ids)),
                   "/",
                      "【学生】",
                      CASE WHEN IFNULL(CONCAT(ms.first_name,ms.last_name), '') = '' THEN ms.name ELSE CONCAT(ms.name, '（', CONCAT(ms.first_name," ",ms.last_name), '）') END,
                      "/",
                      mssft.type_name)  AS name
        FROM
            ais_sale_center.m_receivable_plan i
                INNER JOIN (
                SELECT
                    a.id
                FROM
                    m_receivable_plan a
                        INNER JOIN ais_finance_center.r_invoice_receivable_plan b ON b.fk_receivable_plan_id = a.id
                        INNER JOIN ais_finance_center.r_receipt_form_invoice c ON c.fk_invoice_id = b.fk_invoice_id
                        INNER JOIN ais_finance_center.m_receipt_form d ON d.id = c.fk_receipt_form_id and d.fk_type_key = 'm_student'
                WHERE
                    d.fk_type_target_id = #{targetId}
                  AND d.id = #{receiptFormId}
                  and a.status = 1
                  and d.status = 1
            ) a ON a.id = i.id

                INNER JOIN ais_sale_center.m_student_service_fee AS mssf ON mssf.id = i.fk_type_target_id AND i.fk_type_key = 'm_student_service_fee'
                INNER JOIN ais_sale_center.m_student AS ms ON ms.id = mssf.fk_type_target_id_receivable
                LEFT JOIN ais_sale_center.u_student_service_fee_type AS mssft ON mssft.id = mssf.fk_student_service_fee_type_id

        WHERE mssf.fk_type_key_receivable = 'm_student'
        GROUP BY i.id
    </select>
    <select id="serviceFeeSummary" resultType="com.get.salecenter.vo.StudentServiceFeeSummaryVo">
        SELECT
        f.id AS serviceFeeId,
        s.id as fkStudentId,
        concat(s.name,'（',s.first_name,' ',s.last_name,'）') as studentName,
        m.id as targetCompanyNameId,
        m.short_name AS companyName,
        f.`status`,
        IF (
            f.`status` = 1,
            '有效',
            '无效'
        ) AS serviceFeeStatus,
         b.receivable_plan_amount_currency_type AS fkReceivableCurrencyNum,
         b.receivable_plan_amount AS receivableAmount,
         c.payable_plan_amount_currency_type AS fkPayableCurrencyNum,
         c.payable_plan_amount AS payableAmount,
         b.receivable_actual_amount AS ReceiptAmount,
         c.payable_actual_amount AS paidAmount,
         c.payable_diff_amount AS payableDiffAmount,
         c.commissionRate AS commissionRate,
         IFNULL(b.receiveStatus, 0) AS receiveStatus,
         IFNULL(c.payableStatus, 0) AS payableStatus,
         f.num AS serviceFeeNum,
         g.id AS fkAgentId,
         g.NAME AS fkAgentName,
         CONCAT(
            st. NAME,
            '（',
            st.name_en,
            '）'
        ) AS bdName,
         f.fk_area_country_ids AS countryIds,
         t.type_name AS serviceFeeTypeName,
         t.type_key AS serviceFeeTypeKey,
         f.fk_currency_type_num AS serviceFeeCurrencyNum,
         (f.amount + IFNULL(f.taxes, 0)) AS serviceFeeAmount,
         f.amount AS pretaxAmount,
         f.taxes AS serviceFeeTaxes,
         f.business_status AS businessStatus,
         f.approve_status AS approveStatus,
         f.approve_time AS approveTime,
         f.business_start_time AS businessStartTime,
         f.business_end_time AS businessEndTime,
         f.fk_student_service_fee_type_id AS fkStudentServiceFeeTypeId,
         f.remark AS remark,
         f.fk_type_key_receivable,
         f.fk_type_target_id_receivable,
         f.gmt_create_user AS gmtCreateUser,
         f.gmt_create AS gmtCreate,
         b.receivableFlag,
         c.payableFlag,
         ppsi.settlementFlag,
         f.approve_time AS approveTime,
         f.approve_user AS approveUser,
         f.sales_time AS salesTime,
--          CASE
--             WHEN IFNULL(b.ar_status, 0) = 2
--             AND IFNULL(c.ap_status, 0) = 2 THEN
--             2
--             ELSE
--             1
--             END settlementStatus
        f.settlement_status AS settlementStatus
        FROM
            m_student_service_fee f
        <if test="!isStudentAdmin">
            INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.studentAuthority"/>
            )mm ON mm.id = f.fk_student_id
        </if>
        <!-- 服务费在途结算 -->
        LEFT JOIN (
            SELECT
                a.fk_type_target_id,
                IF(COUNT(distinct b.id) > 0, true, false) AS settlementFlag
            FROM ais_sale_center.m_payable_plan a
            INNER JOIN ais_finance_center.r_payable_plan_settlement_installment b
            ON a.id = b.fk_payable_plan_id
            WHERE
                a.fk_type_key = 'm_student_service_fee'
                AND a.`status` != 0
                AND b.`status` != 0
            GROUP BY a.fk_type_target_id
        ) ppsi ON f.id = ppsi.fk_type_target_id

        <!--应收-->
        LEFT JOIN (
            SELECT
                IF(COUNT(distinct a.fk_type_target_id)>0,true,false) as receivableFlag,
                CASE
                WHEN SUM(a.diff_receivable_amount) = 0 THEN
                2
                ELSE
                CASE
                WHEN SUM(a.actual_receivable_amount) > 0 THEN
                1
                ELSE
                0
                END
                END ar_status,
                a.fk_type_target_id,
                min(a.fk_type_key) fk_type_key,
                GROUP_CONCAT(
                    concat(
                        cast(a.receivable_amount AS CHAR),
                        a.type_name,
                        '（',
                        a.fk_currency_type_num,
                        '）'
                    )
                ) receivable_plan_amount_info,
                GROUP_CONCAT(
                    cast(a.receivable_amount AS CHAR)
                ) receivable_plan_amount,
                GROUP_CONCAT(
                    concat(
                        a.type_name,
                        '（',
                        a.fk_currency_type_num,
                        '）'
                    )
                ) receivable_plan_amount_currency_type,
                GROUP_CONCAT(
                    concat(
                        cast(
                            a.actual_receivable_amount AS CHAR
                        ),
                        a.type_name,
                        '（',
                        a.fk_currency_type_num,
                        '）'
                    )
                ) receivable_actual_amount_info,
                GROUP_CONCAT(
                    cast(
                        a.actual_receivable_amount AS CHAR
                    )
                ) receivable_actual_amount,
                GROUP_CONCAT(
                    concat(
                        a.type_name,
                        '（',
                        a.fk_currency_type_num,
                        '）'
                    )
                ) receivable_actual_amount_currency_type,
                GROUP_CONCAT(
                    concat(
                        cast(
                            a.diff_receivable_amount AS CHAR
                        ),
                        a.type_name,
                        '（',
                        a.fk_currency_type_num,
                        '）'
                    )
                ) receivable_diff_amount_info,
                GROUP_CONCAT(
                    concat(
                        a.type_name,
                        '（',
                        a.fk_currency_type_num,
                        '）'
                    )
                ) receivable_diff_amount_currency_type,
                GROUP_CONCAT(
                    cast(
                        a.diff_receivable_amount AS CHAR
                    )
                ) receivable_diff_amount,
                GROUP_CONCAT(a.fk_currency_type_num) receivable_currency_type_Num,
                max(a.receiveStatus) receiveStatus
            FROM
                (
                    SELECT
                        a.fk_type_target_id,
                        min(a.fk_type_key) AS fk_type_key,
                        a.fk_currency_type_num,
                        c.type_name,
                        SUM(
                            IFNULL(a.receivable_amount, 0)
                        ) receivable_amount,
                        SUM(
                            IFNULL(b.sum_amount_receivable, 0)
                        ) sum_amount_receivable,
                        SUM(
                            IFNULL(
                                b.sum_amount_exchange_rate,
                                0
                            )
                        ) sum_amount_exchange_rate,
                        SUM(IFNULL(b.sum_amount_hkd, 0)) sum_amount_hkd,
                        SUM(IFNULL(b.sum_amount_rmb, 0)) sum_amount_rmb,
                        (
                            SUM(
                                IFNULL(b.sum_amount_receivable, 0)
                            ) + SUM(
                                IFNULL(
                                    b.sum_amount_exchange_rate,
                                    0
                                )
                            )
                        ) actual_receivable_amount,
                        (
                            SUM(
                                IFNULL(b.sum_amount_receivable, 0)
                            ) + SUM(
                                IFNULL(
                                    b.sum_amount_exchange_rate,
                                    0
                                )
                            ) - SUM(
                                IFNULL(a.receivable_amount, 0)
                            )
                        ) diff_receivable_amount,
                        CASE
                    WHEN SUM(
                        IFNULL(b.sum_amount_receivable, 0)
                    ) + SUM(
                        IFNULL(
                            b.sum_amount_exchange_rate,
                            0
                        )
                    ) - SUM(
                        IFNULL(a.receivable_amount, 0)
                    ) = 0 THEN
                        2
                    ELSE
                        CASE
                    WHEN SUM(
                        IFNULL(b.sum_amount_receivable, 0)
                    ) + SUM(
                        IFNULL(
                            b.sum_amount_exchange_rate,
                            0
                        )
                    ) > 0 THEN
                        1
                    ELSE
                        0
                    END
                    END AS receiveStatus
                    FROM
                        ais_sale_center.m_receivable_plan a
                    LEFT JOIN (
                        SELECT
                            a.fk_receivable_plan_id,
                            SUM(
                                IFNULL(a.amount_receivable, 0)
                            ) sum_amount_receivable,
                            SUM(
                                IFNULL(a.amount_exchange_rate, 0)
                            ) sum_amount_exchange_rate,
                            SUM(IFNULL(a.amount_hkd, 0)) sum_amount_hkd,
                            SUM(IFNULL(a.amount_rmb, 0)) sum_amount_rmb
                        FROM
                            ais_finance_center.m_receipt_form_item a
                        LEFT JOIN ais_finance_center.m_receipt_form b ON a.fk_receipt_form_id = b.id
                        WHERE
                            b.`status` != 0 AND b.settlement_status = 1 <!--  关闭的收款单不作计算 -->
                        GROUP BY
                            a.fk_receivable_plan_id
                    ) b ON a.id = b.fk_receivable_plan_id
                    LEFT JOIN ais_finance_center.u_currency_type c ON a.fk_currency_type_num = c.num
                    WHERE
                        a.fk_type_key = 'm_student_service_fee'
                    AND a.`status` != 0
                    GROUP BY
                        a.fk_type_target_id,
                        a.fk_currency_type_num,
                        c.type_name
                ) a
            GROUP BY
                a.fk_type_target_id
        ) b ON f.id = b.fk_type_target_id
        <!-- 应付 -->
        LEFT JOIN (
            SELECT
                IF(COUNT(distinct a.fk_type_target_id)>0,true,false) as payableFlag,
                CASE
                WHEN SUM(a.diff_payable_amount) = 0 THEN
                2
                ELSE
                CASE
                WHEN SUM(a.actual_payable_amount) > 0 THEN
                1
                ELSE
                0
                END
                END ap_status,
                a.fk_type_target_id,
                min(a.fk_type_key) fk_type_key,
                GROUP_CONCAT(
                    concat(
                        cast(a.payable_amount AS CHAR),
                        a.type_name,
                        '（',
                        a.fk_currency_type_num,
                        '）'
                    )
                ) payable_plan_amount_info,
                GROUP_CONCAT(
                    cast(a.payable_amount AS CHAR)
                ) payable_plan_amount,
                GROUP_CONCAT(
                    concat(
                        a.type_name,
                        '（',
                        a.fk_currency_type_num,
                        '）'
                    )
                ) payable_plan_amount_currency_type,
                GROUP_CONCAT(
                    concat(
                        cast(
                            a.actual_payable_amount AS CHAR
                        ),
                        a.type_name,
                        '（',
                        a.fk_currency_type_num,
                        '）'
                    )
                ) payable_actual_amount_info,
                GROUP_CONCAT(
                    cast(
                        a.actual_payable_amount AS CHAR
                    )
                ) payable_actual_amount,
                GROUP_CONCAT(
                    concat(
                        a.type_name,
                        '（',
                        a.fk_currency_type_num,
                        '）'
                    )
                ) payable_actual_amount_currency_type,
                GROUP_CONCAT(
                    concat(
                        cast(
                            a.diff_payable_amount AS CHAR
                        ),
                        a.type_name,
                        '（',
                        a.fk_currency_type_num,
                        '）'
                    )
                ) payable_diff_amount_info,
                GROUP_CONCAT(
                    cast(
                        a.diff_payable_amount AS CHAR
                    )
                ) payable_diff_amount,
                GROUP_CONCAT(
                    concat(
                        a.type_name,
                        '（',
                        a.fk_currency_type_num,
                        '）'
                    )
                ) payable_diff_amount_currency_type,
                GROUP_CONCAT(a.fk_currency_type_num) pay_currency_typeNum,
                GROUP_CONCAT(cast(
                    a.commission_rate AS CHAR
                )) commissionRate,
                max(a.payableStatus) payableStatus
            FROM
                (
                    SELECT
                        a.fk_type_target_id,
                        MIN(a.fk_type_key) AS fk_type_key,
                        a.fk_currency_type_num,
                        a.commission_rate,
                        c.type_name,
                        SUM(IFNULL(a.payable_amount, 0)) payable_amount,
                        SUM(
                            IFNULL(b.sum_amount_payable, 0)
                        ) sum_amount_payable,
                        SUM(
                            IFNULL(
                                b.sum_amount_exchange_rate,
                                0
                            )
                        ) sum_amount_exchange_rate,
                        SUM(IFNULL(b.sum_amount_hkd, 0)) sum_amount_hkd,
                        SUM(IFNULL(b.sum_amount_rmb, 0)) sum_amount_rmb,
                        (
                            SUM(
                                IFNULL(b.sum_amount_payable, 0)
                            ) + SUM(
                                IFNULL(
                                    b.sum_amount_exchange_rate,
                                    0
                                )
                            )
                        ) actual_payable_amount,
                        (
                            SUM(
                                IFNULL(b.sum_amount_payable, 0)
                            ) + SUM(
                                IFNULL(
                                    b.sum_amount_exchange_rate,
                                    0
                                )
                            ) - SUM(IFNULL(a.payable_amount, 0))
                        ) diff_payable_amount,
                        CASE
                    WHEN SUM(
                        IFNULL(b.sum_amount_payable, 0)
                    ) + SUM(
                        IFNULL(
                            b.sum_amount_exchange_rate,
                            0
                        )
                    ) - SUM(IFNULL(a.payable_amount, 0)) = 0 THEN
                        2
                    ELSE
                        CASE
                    WHEN SUM(
                        IFNULL(b.sum_amount_payable, 0)
                    ) + SUM(
                        IFNULL(
                            b.sum_amount_exchange_rate,
                            0
                        )
                    ) > 0 THEN
                        1
                    ELSE
                        3
                    END
                    END AS payableStatus
                    FROM
                        ais_sale_center.m_payable_plan a
                    LEFT JOIN (
                        SELECT
                            a.fk_payable_plan_id,
                            SUM(IFNULL(a.amount_payable, 0)) sum_amount_payable,
                            SUM(
                                IFNULL(a.amount_exchange_rate, 0)
                            ) sum_amount_exchange_rate,
                            SUM(IFNULL(a.amount_hkd, 0)) sum_amount_hkd,
                            SUM(IFNULL(a.amount_rmb, 0)) sum_amount_rmb
                        FROM
                            ais_finance_center.m_payment_form_item a
                        LEFT JOIN ais_finance_center.m_payment_form b ON a.fk_payment_form_id = b.id
                        WHERE
                            b.`status` != 0
                        GROUP BY
                            a.fk_payable_plan_id
                    ) b ON a.id = b.fk_payable_plan_id
                    LEFT JOIN ais_finance_center.u_currency_type c ON a.fk_currency_type_num = c.num
                    WHERE
                        a.fk_type_key = 'm_student_service_fee'
                    AND a.`status` != 0
                    GROUP BY
                        a.fk_type_target_id,
                        a.fk_currency_type_num,
                        a.commission_rate,
                        c.type_name
                ) a
            GROUP BY
                a.fk_type_target_id
        ) c ON f.id = c.fk_type_target_id
        LEFT JOIN m_student s ON s.id = f.fk_student_id
        LEFT JOIN ais_permission_center.m_company m ON m.id = s.fk_company_id
        LEFT JOIN m_agent g ON g.id = f.fk_agent_id
        LEFT JOIN ais_permission_center.m_staff st ON st.id = f.fk_staff_id
        LEFT JOIN u_student_service_fee_type t ON t.id = f.fk_student_service_fee_type_id
        <if test="(studentServiceFeeSummaryDto.projectRoleName!=null and studentServiceFeeSummaryDto.projectRoleName!='')
        or (studentServiceFeeSummaryDto.fkDepartmentId!=null)">
            INNER JOIN (
            select rs.fk_table_id from s_student_project_role_staff rs
            left join ais_permission_center.m_staff sf ON sf.id = rs.fk_staff_id
            where rs.fk_table_name = 'm_student_service_fee'
            <if test="studentServiceFeeSummaryDto.fkDepartmentId!=null">
                AND sf.fk_department_id = #{studentServiceFeeSummaryDto.fkDepartmentId}
            </if>
            <if test="studentServiceFeeSummaryDto.projectRoleName!=null and studentServiceFeeSummaryDto.projectRoleName!=''">
                AND (sf.name LIKE concat('%',#{studentServiceFeeSummaryDto.projectRoleName},'%')
                OR REPLACE(LOWER(sf.name_en),' ','') LIKE concat('%',#{studentServiceFeeSummaryDto.projectRoleName},'%'))
            </if>
            GROUP BY rs.fk_table_id
            )z on  z.fk_table_id = f.id
        </if>
        WHERE
            1 = 1
<!--            <if test="studentServiceFeeSummaryDto.fkStudentServiceFeeTypeId!=null">-->
<!--                AND f.fk_student_service_fee_type_id = #{studentServiceFeeSummaryDto.fkStudentServiceFeeTypeId}-->
<!--            </if>-->
            <if test="studentServiceFeeSummaryDto.fkStudentServiceFeeTypeIds!=null and studentServiceFeeSummaryDto.fkStudentServiceFeeTypeIds.size()>0">
                AND f.fk_student_service_fee_type_id IN
                <foreach collection="studentServiceFeeSummaryDto.fkStudentServiceFeeTypeIds" item="item" index="index"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="studentServiceFeeSummaryDto.status!=null">
                AND f.status = #{studentServiceFeeSummaryDto.status}
            </if>
            <if test="studentServiceFeeSummaryDto.approveStatus != null">
                AND f.approve_status = #{studentServiceFeeSummaryDto.approveStatus}
            </if>
<!--            <if test="studentServiceFeeSummaryDto.fkCompanyId!=null and studentServiceFeeSummaryDto.fkCompanyId!=''">-->
<!--                AND s.fk_company_id = #{studentServiceFeeSummaryDto.fkCompanyId}-->
<!--            </if>-->
            <if test="studentServiceFeeSummaryDto.fkCompanyIds!=null and studentServiceFeeSummaryDto.fkCompanyIds.size()>0">
                and s.fk_company_id IN
                <foreach collection="studentServiceFeeSummaryDto.fkCompanyIds" item="item" index="index"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="studentServiceFeeSummaryDto.studentName!=null and studentServiceFeeSummaryDto.studentName!=''">
                AND (
                REPLACE(CONCAT(LOWER(s.first_name),LOWER(s.last_name)),' ','') LIKE concat('%',#{studentServiceFeeSummaryDto.studentName},'%')
                OR REPLACE(CONCAT(LOWER(s.last_name),LOWER(s.first_name)),' ','') LIKE concat('%',#{studentServiceFeeSummaryDto.studentName},'%')
                OR REPLACE(LOWER(s.`name`),' ','') LIKE concat('%',#{studentServiceFeeSummaryDto.studentName},'%')
                OR REPLACE(LOWER(s.last_name),' ','') LIKE concat('%',#{studentServiceFeeSummaryDto.studentName},'%')
                OR REPLACE(LOWER(s.first_name),' ','') LIKE concat('%',#{studentServiceFeeSummaryDto.studentName},'%')
                OR LOWER(s.num) LIKE concat('%',#{studentServiceFeeSummaryDto.studentName},'%')
                )
            </if>
            <if test="studentServiceFeeSummaryDto.agentName!=null and studentServiceFeeSummaryDto.agentName!=''">
                AND g.name LIKE concat('%',#{studentServiceFeeSummaryDto.agentName},'%')
            </if>
            <if test="studentServiceFeeSummaryDto.bdName!=null and studentServiceFeeSummaryDto.bdName!=''">
                AND (st.name LIKE concat('%',#{studentServiceFeeSummaryDto.bdName},'%')
                OR REPLACE(LOWER(st.name_en),' ','') LIKE concat('%',#{studentServiceFeeSummaryDto.bdName},'%'))
            </if>
            <!-- 创建时间过滤 -->
            <if test="studentServiceFeeSummaryDto.createTime!=null">
                AND DATE_FORMAT(f.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{studentServiceFeeSummaryDto.createTime},'%Y-%m-%d' )
            </if>
            <!-- 创建时间过滤 -->
            <if test="studentServiceFeeSummaryDto.endTime!=null">
                AND DATE_FORMAT(f.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{studentServiceFeeSummaryDto.endTime},'%Y-%m-%d' )
            </if>
            <!--查询条件-应收状态-->
            <if test="studentServiceFeeSummaryDto.arStatus != null">
                AND IFNULL(b.receiveStatus, 0) = #{studentServiceFeeSummaryDto.arStatus}
            </if>
            <!--查询条件-应付状态-->
            <if test="studentServiceFeeSummaryDto.apStatus != null">
                AND IFNULL(c.payableStatus, 0) = #{studentServiceFeeSummaryDto.apStatus}
            </if>
            <!--查询条件-业务状态-->
            <if test="studentServiceFeeSummaryDto.businessStatus != null">
                AND f.business_status = #{studentServiceFeeSummaryDto.businessStatus}
            </if>
            <!-- 审批时间过滤 -->
            <if test="studentServiceFeeSummaryDto.approveStartTime != null">
                AND DATE_FORMAT(f.approve_time, '%Y-%m-%d' ) <![CDATA[ >= ]]> DATE_FORMAT(#{studentServiceFeeSummaryDto.approveStartTime},'%Y-%m-%d' )
            </if>
            <!-- 审批时间过滤 -->
            <if test="studentServiceFeeSummaryDto.approveEndTime != null">
                AND DATE_FORMAT(f.approve_time, '%Y-%m-%d' ) <![CDATA[ <= ]]> DATE_FORMAT(#{studentServiceFeeSummaryDto.approveEndTime},'%Y-%m-%d' )
            </if>
            <!-- 销售时间过滤 -->
            <if test="studentServiceFeeSummaryDto.salesStartTime != null">
                AND DATE_FORMAT(f.sales_time, '%Y-%m-%d' ) <![CDATA[ >= ]]> DATE_FORMAT(#{studentServiceFeeSummaryDto.salesStartTime},'%Y-%m-%d' )
            </if>
            <!-- 销售时间过滤 -->
            <if test="studentServiceFeeSummaryDto.salesEndTime != null">
                AND DATE_FORMAT(f.sales_time, '%Y-%m-%d' ) <![CDATA[ <= ]]> DATE_FORMAT(#{studentServiceFeeSummaryDto.salesEndTime},'%Y-%m-%d' )
            </if>
            <!--查询条件-结算状态-->
            <if test="studentServiceFeeSummaryDto.settlementStatus != null">
--                 AND (CASE WHEN IFNULL(b.ar_status, 0) = 2 AND IFNULL(c.ap_status, 0) = 2 THEN 2 ELSE 1 END) =
              AND f.settlement_status =  #{studentServiceFeeSummaryDto.settlementStatus}
            </if>
        ORDER BY f.gmt_create DESC
    </select>


    <select id="getPaidAmountByIds" resultType="com.get.salecenter.vo.ServiceFeePayFormDetailVo">
        SELECT
        <!--币种-->
        CONCAT( c.type_name, "(", c.num, "）" ) AS payableCurrencyTypeName,
        <!--实收（折合金额+汇率调整）-->
        IFNULL( i.amount_payable, 0 ) + IFNULL( i.amount_exchange_rate, 0 ) AS actualPayableAmount,
        <!--付款时间-->
        i.gmt_create as actualPayTime,
        s.id as serviceFeeId
        FROM
        ais_finance_center.m_payment_form_item i
        INNER JOIN m_payable_plan p ON p.id = i.fk_payable_plan_id
        INNER JOIN m_student_service_fee s ON s.id = p.fk_type_target_id and p.fk_type_key = "m_student_service_fee"
        LEFT JOIN ais_finance_center.u_currency_type c ON p.fk_currency_type_num=c.num
        <if test="itemIds!=null and itemIds.size>0">
            where s.id in
            <foreach collection="itemIds" item="itemId" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>
    </select>
    <select id="getServiceFeeStudentIdsByIds" resultType="java.lang.Long">
        SELECT
            DISTINCT fk_student_id
        FROM
            m_student_service_fee
        WHERE
            id IN
            <foreach collection="ids" item="cid" open="(" separator="," close=")">
                #{cid}
            </foreach>
    </select>
    <select id="getServiceFeeStudentIdsById" resultType="java.lang.Long">
         SELECT
            DISTINCT fk_student_id
        FROM
            m_student_service_fee
        WHERE
            id = #{targetId}
    </select>
    <select id="getServiceFeeByIds" resultType="com.get.salecenter.vo.StudentServiceFeeVo">
        SELECT
            t.type_name as serviceTypeName,
            CASE WHEN
                a.name_note is not null AND a.name_note!=''
            THEN
                CONCAT(a.`name`,'(',a.name_note,')')
            ELSE
                a.`name`
            END as agentName,
            IF(f.`status`=1,'有效','作废') as statusName,
            CONCAT(b.type_name,'（',b.num,'）') as fkCurrencyNumName,
            f.*
        FROM
            m_student_service_fee f
        LEFT JOIN m_student s ON s.id = f.fk_student_id
        LEFT JOIN u_student_service_fee_type t ON t.id = f.fk_student_service_fee_type_id
        LEFT JOIN m_agent a ON a.id = f.fk_agent_id
        LEFT JOIN ais_finance_center.u_currency_type b ON b.num = f.fk_currency_type_num
        WHERE
            f.id IN
            <foreach collection="ids" item="cid" open="(" close=")" separator=",">
                #{cid}
            </foreach>
    </select>
    <select id="getDepartmentSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            DISTINCT c.id,c.num,c.name
        FROM
            `s_student_project_role_staff` a
                INNER JOIN ais_permission_center.m_staff b ON a.fk_staff_id = b.id AND a.is_active = 1 AND a.fk_table_name = "m_student_service_fee"
                LEFT JOIN ais_permission_center.m_department c on c.id = b.fk_department_id
                LEFT JOIN m_student_service_fee d on d.id = a.fk_table_id
                LEFT JOIN m_student e on d.fk_student_id = e.id
        where e.fk_company_id = #{companyId}
        ORDER BY c.view_order desc

    </select>



    <select id="getFeeTargetName"  resultType="com.get.salecenter.vo.StudentServiceFeeVo">
        SELECT mssf.id, CONCAT("【学生】", CASE WHEN IFNULL(CONCAT(ms.first_name,ms.last_name), '') = '' THEN ms.name ELSE CONCAT(ms.name, '（', CONCAT(ms.first_name," ",ms.last_name), '）') END)  AS receivableName
        FROM ais_sale_center.m_student_service_fee AS mssf
        INNER JOIN ais_sale_center.m_student AS ms ON ms.id = mssf.fk_type_target_id_receivable
        WHERE mssf.fk_type_key_receivable = 'm_student'
        AND mssf.id IN
        <foreach collection="feeIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        UNION ALL
        SELECT mssf.id,
        CONCAT("【学校提供商】", CASE WHEN IFNULL(mip.name_chn,'') = '' THEN  mip.name ELSE CONCAT(mip.name, '（', mip.name_chn, '）' )  END) AS feeTarget
        FROM ais_sale_center.m_student_service_fee AS mssf
        INNER JOIN ais_institution_center.m_institution_provider AS mip ON mip.id = mssf.fk_type_target_id_receivable
        WHERE mssf.fk_type_key_receivable = 'm_institution_provider'
        AND mssf.id IN
        <foreach collection="feeIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="getServiceFeeProviderIdsByFeeIds" resultType="java.lang.Long">
        SELECT
            fk_type_target_id_receivable
        FROM
            `m_student_service_fee`
        WHERE
            id IN
            <foreach collection="feeIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
            AND fk_type_key_receivable = 'm_institution_provider'
        GROUP BY
            fk_type_target_id_receivable
    </select>

    <select id="exportStudentServiceFeeInfo" resultType="com.get.salecenter.vo.StudentServiceFeeSummaryVo">
        SELECT
        f.id AS serviceFeeId,
        s.id as fkStudentId,
        concat(s.first_name,' ',s.last_name) as studentName,
        s.name AS studentNameChn,
        m.id as targetCompanyNameId,
        m.short_name AS companyName,
        f.`status`,
        IF (
        f.`status` = 1,
        '有效',
        '无效'
        ) AS serviceFeeStatus,
        b.receivable_plan_amount_currency_type AS fkReceivableCurrencyNum,
        b.receivable_plan_amount AS receivableAmount,
        c.payable_plan_amount_currency_type AS fkPayableCurrencyNum,
        c.payable_plan_amount AS payableAmount,
        b.receivable_actual_amount AS ReceiptAmount,
        c.payable_actual_amount AS paidAmount,
        c.payable_diff_amount AS payableDiffAmount,
        c.commissionRate AS commissionRate,
        IFNULL(b.receiveStatus, 0) AS receiveStatus,
        IFNULL(c.payableStatus, 0) AS payableStatus,
        f.num AS serviceFeeNum,
        g.id AS fkAgentId,
        g.NAME AS fkAgentName,
        CONCAT(
        st. NAME,
        '（',
        st.name_en,
        '）'
        ) AS bdName,
        f.fk_area_country_ids AS countryIds,
        t.type_name AS serviceFeeTypeName,
        t.type_key AS serviceFeeTypeKey,
        f.fk_currency_type_num AS serviceFeeCurrencyNum,
        (f.amount + IFNULL(f.taxes, 0)) AS serviceFeeAmount,
        f.amount AS pretaxAmount,
        f.taxes AS serviceFeeTaxes,
        f.business_status AS businessStatus,
        f.approve_status AS approveStatus,
        f.approve_time AS approveTime,
        f.business_start_time AS businessStartTime,
        f.business_end_time AS businessEndTime,
        f.fk_student_service_fee_type_id AS fkStudentServiceFeeTypeId,
        f.remark AS remark,
        f.fk_type_key_receivable,
        f.fk_type_target_id_receivable,
        f.gmt_create_user AS gmtCreateUser,
        f.gmt_create AS gmtCreate,
        b.receivableFlag,
        c.payableFlag,
        ppsi.settlementFlag,
        f.approve_time AS approveTime,
        f.approve_user AS approveUser,
        f.sales_time AS salesTime,
        --          CASE
        --             WHEN IFNULL(b.ar_status, 0) = 2
        --             AND IFNULL(c.ap_status, 0) = 2 THEN
        --             2
        --             ELSE
        --             1
        --             END settlementStatus
        f.settlement_status AS settlementStatus
        FROM
        m_student_service_fee f
        <if test="!isStudentAdmin">
            INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.studentAuthority"/>
            )mm ON mm.id = f.fk_student_id
        </if>
        <!-- 服务费在途结算 -->
        LEFT JOIN (
        SELECT
        a.fk_type_target_id,
        IF(COUNT(distinct b.id) > 0, true, false) AS settlementFlag
        FROM ais_sale_center.m_payable_plan a
        INNER JOIN ais_finance_center.r_payable_plan_settlement_installment b
        ON a.id = b.fk_payable_plan_id
        WHERE
        a.fk_type_key = 'm_student_service_fee'
        AND a.`status` != 0
        AND b.`status` != 0
        GROUP BY a.fk_type_target_id
        ) ppsi ON f.id = ppsi.fk_type_target_id

        <!--应收-->
        LEFT JOIN (
        SELECT
        IF(COUNT(distinct a.fk_type_target_id)>0,true,false) as receivableFlag,
        CASE
        WHEN SUM(a.diff_receivable_amount) = 0 THEN
        2
        ELSE
        CASE
        WHEN SUM(a.actual_receivable_amount) > 0 THEN
        1
        ELSE
        0
        END
        END ar_status,
        a.fk_type_target_id,
        min(a.fk_type_key) fk_type_key,
        GROUP_CONCAT(
        concat(
        cast(a.receivable_amount AS CHAR),
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) receivable_plan_amount_info,
        GROUP_CONCAT(
        cast(a.receivable_amount AS CHAR)
        ) receivable_plan_amount,
        GROUP_CONCAT(
        concat(
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) receivable_plan_amount_currency_type,
        GROUP_CONCAT(
        concat(
        cast(
        a.actual_receivable_amount AS CHAR
        ),
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) receivable_actual_amount_info,
        GROUP_CONCAT(
        cast(
        a.actual_receivable_amount AS CHAR
        )
        ) receivable_actual_amount,
        GROUP_CONCAT(
        concat(
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) receivable_actual_amount_currency_type,
        GROUP_CONCAT(
        concat(
        cast(
        a.diff_receivable_amount AS CHAR
        ),
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) receivable_diff_amount_info,
        GROUP_CONCAT(
        concat(
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) receivable_diff_amount_currency_type,
        GROUP_CONCAT(
        cast(
        a.diff_receivable_amount AS CHAR
        )
        ) receivable_diff_amount,
        GROUP_CONCAT(a.fk_currency_type_num) receivable_currency_type_Num,
        max(a.receiveStatus) receiveStatus
        FROM
        (
        SELECT
        a.fk_type_target_id,
        min(a.fk_type_key) AS fk_type_key,
        a.fk_currency_type_num,
        c.type_name,
        SUM(
        IFNULL(a.receivable_amount, 0)
        ) receivable_amount,
        SUM(
        IFNULL(b.sum_amount_receivable, 0)
        ) sum_amount_receivable,
        SUM(
        IFNULL(
        b.sum_amount_exchange_rate,
        0
        )
        ) sum_amount_exchange_rate,
        SUM(IFNULL(b.sum_amount_hkd, 0)) sum_amount_hkd,
        SUM(IFNULL(b.sum_amount_rmb, 0)) sum_amount_rmb,
        (
        SUM(
        IFNULL(b.sum_amount_receivable, 0)
        ) + SUM(
        IFNULL(
        b.sum_amount_exchange_rate,
        0
        )
        )
        ) actual_receivable_amount,
        (
        SUM(
        IFNULL(b.sum_amount_receivable, 0)
        ) + SUM(
        IFNULL(
        b.sum_amount_exchange_rate,
        0
        )
        ) - SUM(
        IFNULL(a.receivable_amount, 0)
        )
        ) diff_receivable_amount,
        CASE
        WHEN SUM(
        IFNULL(b.sum_amount_receivable, 0)
        ) + SUM(
        IFNULL(
        b.sum_amount_exchange_rate,
        0
        )
        ) - SUM(
        IFNULL(a.receivable_amount, 0)
        ) = 0 THEN
        2
        ELSE
        CASE
        WHEN SUM(
        IFNULL(b.sum_amount_receivable, 0)
        ) + SUM(
        IFNULL(
        b.sum_amount_exchange_rate,
        0
        )
        ) > 0 THEN
        1
        ELSE
        0
        END
        END AS receiveStatus
        FROM
        ais_sale_center.m_receivable_plan a
        LEFT JOIN (
        SELECT
        a.fk_receivable_plan_id,
        SUM(
        IFNULL(a.amount_receivable, 0)
        ) sum_amount_receivable,
        SUM(
        IFNULL(a.amount_exchange_rate, 0)
        ) sum_amount_exchange_rate,
        SUM(IFNULL(a.amount_hkd, 0)) sum_amount_hkd,
        SUM(IFNULL(a.amount_rmb, 0)) sum_amount_rmb
        FROM
        ais_finance_center.m_receipt_form_item a
        LEFT JOIN ais_finance_center.m_receipt_form b ON a.fk_receipt_form_id = b.id
        WHERE
        b.`status` != 0 AND b.settlement_status = 1 <!--  关闭的收款单不作计算 -->
        GROUP BY
        a.fk_receivable_plan_id
        ) b ON a.id = b.fk_receivable_plan_id
        LEFT JOIN ais_finance_center.u_currency_type c ON a.fk_currency_type_num = c.num
        WHERE
        a.fk_type_key = 'm_student_service_fee'
        AND a.`status` != 0
        GROUP BY
        a.fk_type_target_id,
        a.fk_currency_type_num,
        c.type_name
        ) a
        GROUP BY
        a.fk_type_target_id
        ) b ON f.id = b.fk_type_target_id
        <!-- 应付 -->
        LEFT JOIN (
        SELECT
        IF(COUNT(distinct a.fk_type_target_id)>0,true,false) as payableFlag,
        CASE
        WHEN SUM(a.diff_payable_amount) = 0 THEN
        2
        ELSE
        CASE
        WHEN SUM(a.actual_payable_amount) > 0 THEN
        1
        ELSE
        0
        END
        END ap_status,
        a.fk_type_target_id,
        min(a.fk_type_key) fk_type_key,
        GROUP_CONCAT(
        concat(
        cast(a.payable_amount AS CHAR),
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) payable_plan_amount_info,
        GROUP_CONCAT(
        cast(a.payable_amount AS CHAR)
        ) payable_plan_amount,
        GROUP_CONCAT(
        concat(
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) payable_plan_amount_currency_type,
        GROUP_CONCAT(
        concat(
        cast(
        a.actual_payable_amount AS CHAR
        ),
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) payable_actual_amount_info,
        GROUP_CONCAT(
        cast(
        a.actual_payable_amount AS CHAR
        )
        ) payable_actual_amount,
        GROUP_CONCAT(
        concat(
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) payable_actual_amount_currency_type,
        GROUP_CONCAT(
        concat(
        cast(
        a.diff_payable_amount AS CHAR
        ),
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) payable_diff_amount_info,
        GROUP_CONCAT(
        cast(
        a.diff_payable_amount AS CHAR
        )
        ) payable_diff_amount,
        GROUP_CONCAT(
        concat(
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) payable_diff_amount_currency_type,
        GROUP_CONCAT(a.fk_currency_type_num) pay_currency_typeNum,
        GROUP_CONCAT(cast(
        a.commission_rate AS CHAR
        )) commissionRate,
        max(a.payableStatus) payableStatus
        FROM
        (
        SELECT
        a.fk_type_target_id,
        MIN(a.fk_type_key) AS fk_type_key,
        a.fk_currency_type_num,
        a.commission_rate,
        c.type_name,
        SUM(IFNULL(a.payable_amount, 0)) payable_amount,
        SUM(
        IFNULL(b.sum_amount_payable, 0)
        ) sum_amount_payable,
        SUM(
        IFNULL(
        b.sum_amount_exchange_rate,
        0
        )
        ) sum_amount_exchange_rate,
        SUM(IFNULL(b.sum_amount_hkd, 0)) sum_amount_hkd,
        SUM(IFNULL(b.sum_amount_rmb, 0)) sum_amount_rmb,
        (
        SUM(
        IFNULL(b.sum_amount_payable, 0)
        ) + SUM(
        IFNULL(
        b.sum_amount_exchange_rate,
        0
        )
        )
        ) actual_payable_amount,
        (
        SUM(
        IFNULL(b.sum_amount_payable, 0)
        ) + SUM(
        IFNULL(
        b.sum_amount_exchange_rate,
        0
        )
        ) - SUM(IFNULL(a.payable_amount, 0))
        ) diff_payable_amount,
        CASE
        WHEN SUM(
        IFNULL(b.sum_amount_payable, 0)
        ) + SUM(
        IFNULL(
        b.sum_amount_exchange_rate,
        0
        )
        ) - SUM(IFNULL(a.payable_amount, 0)) = 0 THEN
        2
        ELSE
        CASE
        WHEN SUM(
        IFNULL(b.sum_amount_payable, 0)
        ) + SUM(
        IFNULL(
        b.sum_amount_exchange_rate,
        0
        )
        ) > 0 THEN
        1
        ELSE
        3
        END
        END AS payableStatus
        FROM
        ais_sale_center.m_payable_plan a
        LEFT JOIN (
        SELECT
        a.fk_payable_plan_id,
        SUM(IFNULL(a.amount_payable, 0)) sum_amount_payable,
        SUM(
        IFNULL(a.amount_exchange_rate, 0)
        ) sum_amount_exchange_rate,
        SUM(IFNULL(a.amount_hkd, 0)) sum_amount_hkd,
        SUM(IFNULL(a.amount_rmb, 0)) sum_amount_rmb
        FROM
        ais_finance_center.m_payment_form_item a
        LEFT JOIN ais_finance_center.m_payment_form b ON a.fk_payment_form_id = b.id
        WHERE
        b.`status` != 0
        GROUP BY
        a.fk_payable_plan_id
        ) b ON a.id = b.fk_payable_plan_id
        LEFT JOIN ais_finance_center.u_currency_type c ON a.fk_currency_type_num = c.num
        WHERE
        a.fk_type_key = 'm_student_service_fee'
        AND a.`status` != 0
        GROUP BY
        a.fk_type_target_id,
        a.fk_currency_type_num,
        a.commission_rate,
        c.type_name
        ) a
        GROUP BY
        a.fk_type_target_id
        ) c ON f.id = c.fk_type_target_id
        LEFT JOIN m_student s ON s.id = f.fk_student_id
        LEFT JOIN ais_permission_center.m_company m ON m.id = s.fk_company_id
        LEFT JOIN m_agent g ON g.id = f.fk_agent_id
        LEFT JOIN ais_permission_center.m_staff st ON st.id = f.fk_staff_id
        LEFT JOIN u_student_service_fee_type t ON t.id = f.fk_student_service_fee_type_id
        WHERE
        1 = 1
        <if test="studentServiceFeeSummaryDto.fkStudentServiceFeeId !=null">
            AND f.id = #{studentServiceFeeSummaryDto.fkStudentServiceFeeId}
        </if>
        ORDER BY f.gmt_create DESC
    </select>

    <select id="getServiceFeeNumById" resultType="com.get.salecenter.vo.StudentServiceFeeSummaryVo">
        SELECT
        f.id AS serviceFeeId,
        s.id as fkStudentId,
        concat(s.first_name,' ',s.last_name) as studentName,
        s.name AS studentNameChn,
        m.id as targetCompanyNameId,
        m.short_name AS companyName,
        f.`status`,
        IF (
        f.`status` = 1,
        '有效',
        '无效'
        ) AS serviceFeeStatus,
        b.receivable_plan_amount_currency_type AS fkReceivableCurrencyNum,
        b.receivable_plan_amount AS receivableAmount,
        c.payable_plan_amount_currency_type AS fkPayableCurrencyNum,
        c.payable_plan_amount AS payableAmount,
        b.receivable_actual_amount AS ReceiptAmount,
        c.payable_actual_amount AS paidAmount,
        c.payable_diff_amount AS payableDiffAmount,
        c.commissionRate AS commissionRate,
        IFNULL(b.receiveStatus, 0) AS receiveStatus,
        IFNULL(c.payableStatus, 0) AS payableStatus,
        f.num AS serviceFeeNum,
        g.id AS fkAgentId,
        g.NAME AS fkAgentName,
        CONCAT(
        st. NAME,
        '（',
        st.name_en,
        '）'
        ) AS bdName,
        f.fk_area_country_ids AS countryIds,
        t.type_name AS serviceFeeTypeName,
        t.type_key AS serviceFeeTypeKey,
        f.fk_currency_type_num AS serviceFeeCurrencyNum,
        (f.amount + IFNULL(f.taxes, 0)) AS serviceFeeAmount,
        f.amount AS pretaxAmount,
        f.taxes AS serviceFeeTaxes,
        f.business_status AS businessStatus,
        f.approve_status AS approveStatus,
        f.approve_time AS approveTime,
        f.business_start_time AS businessStartTime,
        f.business_end_time AS businessEndTime,
        f.fk_student_service_fee_type_id AS fkStudentServiceFeeTypeId,
        f.remark AS remark,
        f.fk_type_key_receivable,
        f.fk_type_target_id_receivable,
        f.gmt_create_user AS gmtCreateUser,
        f.gmt_create AS gmtCreate,
        b.receivableFlag,
        c.payableFlag,
        ppsi.settlementFlag,
        f.approve_time AS approveTime,
        f.approve_user AS approveUser,
        f.sales_time AS salesTime,
        f.settlement_status AS settlementStatus
        FROM
        m_student_service_fee f

        <!-- 服务费在途结算 -->
        LEFT JOIN (
        SELECT
        a.fk_type_target_id,
        IF(COUNT(distinct b.id) > 0, true, false) AS settlementFlag
        FROM ais_sale_center.m_payable_plan a
        INNER JOIN ais_finance_center.r_payable_plan_settlement_installment b
        ON a.id = b.fk_payable_plan_id
        WHERE
        a.fk_type_key = 'm_student_service_fee'
        AND a.`status` != 0
        AND b.`status` != 0
        GROUP BY a.fk_type_target_id
        ) ppsi ON f.id = ppsi.fk_type_target_id

        <!--应收-->
        LEFT JOIN (
        SELECT
        IF(COUNT(distinct a.fk_type_target_id)>0,true,false) as receivableFlag,
        CASE
        WHEN SUM(a.diff_receivable_amount) = 0 THEN
        2
        ELSE
        CASE
        WHEN SUM(a.actual_receivable_amount) > 0 THEN
        1
        ELSE
        0
        END
        END ar_status,
        a.fk_type_target_id,
        min(a.fk_type_key) fk_type_key,
        GROUP_CONCAT(
        concat(
        cast(a.receivable_amount AS CHAR),
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) receivable_plan_amount_info,
        GROUP_CONCAT(
        cast(a.receivable_amount AS CHAR)
        ) receivable_plan_amount,
        GROUP_CONCAT(
        concat(
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) receivable_plan_amount_currency_type,
        GROUP_CONCAT(
        concat(
        cast(
        a.actual_receivable_amount AS CHAR
        ),
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) receivable_actual_amount_info,
        GROUP_CONCAT(
        cast(
        a.actual_receivable_amount AS CHAR
        )
        ) receivable_actual_amount,
        GROUP_CONCAT(
        concat(
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) receivable_actual_amount_currency_type,
        GROUP_CONCAT(
        concat(
        cast(
        a.diff_receivable_amount AS CHAR
        ),
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) receivable_diff_amount_info,
        GROUP_CONCAT(
        concat(
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) receivable_diff_amount_currency_type,
        GROUP_CONCAT(
        cast(
        a.diff_receivable_amount AS CHAR
        )
        ) receivable_diff_amount,
        GROUP_CONCAT(a.fk_currency_type_num) receivable_currency_type_Num,
        max(a.receiveStatus) receiveStatus
        FROM
        (
        SELECT
        a.fk_type_target_id,
        min(a.fk_type_key) AS fk_type_key,
        a.fk_currency_type_num,
        c.type_name,
        SUM(
        IFNULL(a.receivable_amount, 0)
        ) receivable_amount,
        SUM(
        IFNULL(b.sum_amount_receivable, 0)
        ) sum_amount_receivable,
        SUM(
        IFNULL(
        b.sum_amount_exchange_rate,
        0
        )
        ) sum_amount_exchange_rate,
        SUM(IFNULL(b.sum_amount_hkd, 0)) sum_amount_hkd,
        SUM(IFNULL(b.sum_amount_rmb, 0)) sum_amount_rmb,
        (
        SUM(
        IFNULL(b.sum_amount_receivable, 0)
        ) + SUM(
        IFNULL(
        b.sum_amount_exchange_rate,
        0
        )
        )
        ) actual_receivable_amount,
        (
        SUM(
        IFNULL(b.sum_amount_receivable, 0)
        ) + SUM(
        IFNULL(
        b.sum_amount_exchange_rate,
        0
        )
        ) - SUM(
        IFNULL(a.receivable_amount, 0)
        )
        ) diff_receivable_amount,
        CASE
        WHEN SUM(
        IFNULL(b.sum_amount_receivable, 0)
        ) + SUM(
        IFNULL(
        b.sum_amount_exchange_rate,
        0
        )
        ) - SUM(
        IFNULL(a.receivable_amount, 0)
        ) = 0 THEN
        2
        ELSE
        CASE
        WHEN SUM(
        IFNULL(b.sum_amount_receivable, 0)
        ) + SUM(
        IFNULL(
        b.sum_amount_exchange_rate,
        0
        )
        ) > 0 THEN
        1
        ELSE
        0
        END
        END AS receiveStatus
        FROM
        ais_sale_center.m_receivable_plan a
        LEFT JOIN (
        SELECT
        a.fk_receivable_plan_id,
        SUM(
        IFNULL(a.amount_receivable, 0)
        ) sum_amount_receivable,
        SUM(
        IFNULL(a.amount_exchange_rate, 0)
        ) sum_amount_exchange_rate,
        SUM(IFNULL(a.amount_hkd, 0)) sum_amount_hkd,
        SUM(IFNULL(a.amount_rmb, 0)) sum_amount_rmb
        FROM
        ais_finance_center.m_receipt_form_item a
        LEFT JOIN ais_finance_center.m_receipt_form b ON a.fk_receipt_form_id = b.id
        WHERE
        b.`status` != 0 AND b.settlement_status = 1 <!--  关闭的收款单不作计算 -->
        GROUP BY
        a.fk_receivable_plan_id
        ) b ON a.id = b.fk_receivable_plan_id
        LEFT JOIN ais_finance_center.u_currency_type c ON a.fk_currency_type_num = c.num
        WHERE
        a.fk_type_key = 'm_student_service_fee'
        AND a.`status` != 0
        GROUP BY
        a.fk_type_target_id,
        a.fk_currency_type_num,
        c.type_name
        ) a
        GROUP BY
        a.fk_type_target_id
        ) b ON f.id = b.fk_type_target_id
        <!-- 应付 -->
        LEFT JOIN (
        SELECT
        IF(COUNT(distinct a.fk_type_target_id)>0,true,false) as payableFlag,
        CASE
        WHEN SUM(a.diff_payable_amount) = 0 THEN
        2
        ELSE
        CASE
        WHEN SUM(a.actual_payable_amount) > 0 THEN
        1
        ELSE
        0
        END
        END ap_status,
        a.fk_type_target_id,
        min(a.fk_type_key) fk_type_key,
        GROUP_CONCAT(
        concat(
        cast(a.payable_amount AS CHAR),
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) payable_plan_amount_info,
        GROUP_CONCAT(
        cast(a.payable_amount AS CHAR)
        ) payable_plan_amount,
        GROUP_CONCAT(
        concat(
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) payable_plan_amount_currency_type,
        GROUP_CONCAT(
        concat(
        cast(
        a.actual_payable_amount AS CHAR
        ),
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) payable_actual_amount_info,
        GROUP_CONCAT(
        cast(
        a.actual_payable_amount AS CHAR
        )
        ) payable_actual_amount,
        GROUP_CONCAT(
        concat(
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) payable_actual_amount_currency_type,
        GROUP_CONCAT(
        concat(
        cast(
        a.diff_payable_amount AS CHAR
        ),
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) payable_diff_amount_info,
        GROUP_CONCAT(
        cast(
        a.diff_payable_amount AS CHAR
        )
        ) payable_diff_amount,
        GROUP_CONCAT(
        concat(
        a.type_name,
        '（',
        a.fk_currency_type_num,
        '）'
        )
        ) payable_diff_amount_currency_type,
        GROUP_CONCAT(a.fk_currency_type_num) pay_currency_typeNum,
        GROUP_CONCAT(cast(
        a.commission_rate AS CHAR
        )) commissionRate,
        max(a.payableStatus) payableStatus
        FROM
        (
        SELECT
        a.fk_type_target_id,
        MIN(a.fk_type_key) AS fk_type_key,
        a.fk_currency_type_num,
        a.commission_rate,
        c.type_name,
        SUM(IFNULL(a.payable_amount, 0)) payable_amount,
        SUM(
        IFNULL(b.sum_amount_payable, 0)
        ) sum_amount_payable,
        SUM(
        IFNULL(
        b.sum_amount_exchange_rate,
        0
        )
        ) sum_amount_exchange_rate,
        SUM(IFNULL(b.sum_amount_hkd, 0)) sum_amount_hkd,
        SUM(IFNULL(b.sum_amount_rmb, 0)) sum_amount_rmb,
        (
        SUM(
        IFNULL(b.sum_amount_payable, 0)
        ) + SUM(
        IFNULL(
        b.sum_amount_exchange_rate,
        0
        )
        )
        ) actual_payable_amount,
        (
        SUM(
        IFNULL(b.sum_amount_payable, 0)
        ) + SUM(
        IFNULL(
        b.sum_amount_exchange_rate,
        0
        )
        ) - SUM(IFNULL(a.payable_amount, 0))
        ) diff_payable_amount,
        CASE
        WHEN SUM(
        IFNULL(b.sum_amount_payable, 0)
        ) + SUM(
        IFNULL(
        b.sum_amount_exchange_rate,
        0
        )
        ) - SUM(IFNULL(a.payable_amount, 0)) = 0 THEN
        2
        ELSE
        CASE
        WHEN SUM(
        IFNULL(b.sum_amount_payable, 0)
        ) + SUM(
        IFNULL(
        b.sum_amount_exchange_rate,
        0
        )
        ) > 0 THEN
        1
        ELSE
        3
        END
        END AS payableStatus
        FROM
        ais_sale_center.m_payable_plan a
        LEFT JOIN (
        SELECT
        a.fk_payable_plan_id,
        SUM(IFNULL(a.amount_payable, 0)) sum_amount_payable,
        SUM(
        IFNULL(a.amount_exchange_rate, 0)
        ) sum_amount_exchange_rate,
        SUM(IFNULL(a.amount_hkd, 0)) sum_amount_hkd,
        SUM(IFNULL(a.amount_rmb, 0)) sum_amount_rmb
        FROM
        ais_finance_center.m_payment_form_item a
        LEFT JOIN ais_finance_center.m_payment_form b ON a.fk_payment_form_id = b.id
        WHERE
        b.`status` != 0
        GROUP BY
        a.fk_payable_plan_id
        ) b ON a.id = b.fk_payable_plan_id
        LEFT JOIN ais_finance_center.u_currency_type c ON a.fk_currency_type_num = c.num
        WHERE
        a.fk_type_key = 'm_student_service_fee'
        AND a.`status` != 0
        GROUP BY
        a.fk_type_target_id,
        a.fk_currency_type_num,
        a.commission_rate,
        c.type_name
        ) a
        GROUP BY
        a.fk_type_target_id
        ) c ON f.id = c.fk_type_target_id
        LEFT JOIN m_student s ON s.id = f.fk_student_id
        LEFT JOIN ais_permission_center.m_company m ON m.id = s.fk_company_id
        LEFT JOIN m_agent g ON g.id = f.fk_agent_id
        LEFT JOIN ais_permission_center.m_staff st ON st.id = f.fk_staff_id
        LEFT JOIN u_student_service_fee_type t ON t.id = f.fk_student_service_fee_type_id
        WHERE f.id = #{id}
    </select>
    <select id="getClientStudentServiceFee" resultType="com.get.salecenter.entity.StudentServiceFee">
        SELECT * FROM m_student_service_fee WHERE fk_student_id IN
        <foreach item="item" collection="studentIds" separator="," close=")" open="(" index="index">
            #{item}
        </foreach>
        AND FIND_IN_SET(#{countryId},fk_area_country_ids) > 0
    </select>
</mapper>
