package com.get.institutioncenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/10/18 17:44
 */
@Data
@ApiModel("新闻邮件发送统计返回类")
@NoArgsConstructor
@AllArgsConstructor
public class NewsSendStatisticsVo {

    /**
     * 新闻标题
     */
    @ApiModelProperty(value = "新闻标题")
    private String title;

    /**
     * 发送总数量
     */
    @ApiModelProperty(value = "发送总数量")
    private Integer requestCount;
    /**
     * 成功发送量
     */
    @ApiModelProperty(value = "成功发送量")
    private Integer successCount;
    /**
     * 失败发送量
     */
    @ApiModelProperty(value = "失败发送量")
    private Integer faildCount;
    /**
     * 无效发送量
     */
    @ApiModelProperty(value = "无效发送量")
    private Integer unavailableCount;

}
