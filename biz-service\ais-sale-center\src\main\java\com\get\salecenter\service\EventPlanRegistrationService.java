package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.salecenter.vo.*;
import com.get.salecenter.vo.EventPlanRegistrationVo;
import com.get.salecenter.entity.EventPlanRegistration;
import com.get.salecenter.dto.EventPlanRegistrationFormDto;
import com.get.salecenter.dto.EventPlanRegistrationSearchDto;
import com.get.salecenter.dto.EventPlanRegistrationDto;
import com.get.salecenter.dto.OfflineLocationSelectDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
public interface EventPlanRegistrationService extends BaseService<EventPlanRegistration> {
    /**
     * 注册
     * <AUTHOR>
     * @DateTime 2023/12/19 12:41
     */
    void eventPlanRegistration(EventPlanRegistrationFormDto registrationFormVo);
    /**
     *列表数据
     * <AUTHOR>
     * @DateTime 2023/12/19 15:00
     */
    List<EventPlanRegistrationVo> getEventPlanRegistrations(EventPlanRegistrationSearchDto eventPlanRegistrationSearchDto, Page page);

    /**
     * 列表收费总计
     * <AUTHOR>
     * @DateTime 2023/12/22 11:29
     */
    String getEventPlanRegistrationTotalFee(EventPlanRegistrationSearchDto vo);

    /**
     * 导出列表数据
     * <AUTHOR>
     * @DateTime 2023/12/22 14:54
     */
    void exportEventPlanRegistrationsExcel(EventPlanRegistrationSearchDto vo, HttpServletResponse response);


    /**
     * 根据报名名册id获取修改详情信息
     * <AUTHOR>
     * @DateTime 2024/1/16 14:28
     */
    EventPlanRegistrationUpdateVo findEventPlanRegistrationById(Long id);

    /**
     * 修改报名名册发票建议币种和联系人信息
     * <AUTHOR>
     * @DateTime 2024/1/16 14:38
     */
    void updateEventPlanRegistration(EventPlanRegistrationDto eventPlanRegistrationDto);

    /**
     * 删除报名名册
     * <AUTHOR>
     * @DateTime 2024/1/16 11:45
     */
    void delete(Long id);

    /**
     * 活动年度计划下拉
     * <AUTHOR>
     * @DateTime 2023/12/20 17:27
     */
    List<EventPlanSelectVo> getEventPlanListByYear(Integer year);

    /**
     * 活动年度计划主题下拉
     * <AUTHOR>
     * @DateTime 2023/12/20 17:33
     */
    List<EventPlanThemeSelectVo> getThemeListByPlanId(Long fkEventPlanId, Integer displayType);

    /**
     * 线上活动项目下拉
     * <AUTHOR>
     * @DateTime 2023/12/21 10:12
     */
    List<EventPlanThemeOnlineSelectVo> getOnlineSelect(List<Long> fkEventPlanThemeIds);

    /**
     * 线下活动项目业务国家下拉
     * <AUTHOR>
     * @DateTime 2023/12/20 17:51
     */
    List<String> getOfflineAreaCountryNameList(List<Long> fkEventPlanThemeIds);

    /**
     * 线下活动项目地区下拉
     * <AUTHOR>
     * @DateTime 2023/12/21 9:46
     */
    List<String> getOfflineLocationList(OfflineLocationSelectDto offlineLocationSelectDto);

    /**
     * 线下专访地点下拉
     * <AUTHOR>
     * @DateTime 2023/12/21 9:59
     */
    List<String> getWorkshopLocationSelect(List<Long> fkEventPlanThemeIds);

    /**
     * 学校提供商下拉
     * <AUTHOR>
     * @DateTime 2023/12/28 17:19
     */
    List<BaseSelectEntity> getInstitutionProviderSelect();
}
