package com.get.institutioncenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/*
 * author:Neil
 * Time: 12:34
 * Date: 2022/5/31
 * Description:
 */
@Data
public class InstitutionCourseNameSearchDto {
    @ApiModelProperty(value = "课程名称")
    private String courseName;
    @ApiModelProperty(value = "课程ids")
    private List<Long> courseIds;
}
