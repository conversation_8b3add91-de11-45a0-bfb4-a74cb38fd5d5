package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.dto.InstitutionGroupDto;
import com.get.institutioncenter.vo.InstitutionGroupVo;
import com.get.institutioncenter.service.IInstitutionGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/10/20
 * @TIME: 12:07
 * @Description:学校集团控制器
 **/
@Api(tags = "学校中心-学校集团")
@RestController
@RequestMapping("/institution/institutionGroup")
public class InstitutionGroupController {
    @Resource
    private IInstitutionGroupService institutionGroupService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.InstitutionGroupVo>
     * @Description：详情
     * @Param [id]
     * <AUTHOR>
     **/
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/学校集团管理/集团详情")
    @GetMapping("/{id}")
    public ResponseBo<InstitutionGroupVo> detail(@PathVariable("id") Long id) {
        InstitutionGroupVo data = institutionGroupService.findInstitutionGroupById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description：批量新增信息
     * @Param [institutionGroupDtos]
     * <AUTHOR>
     **/
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/学校集团管理/新增学校集团")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(InstitutionGroupDto.Add.class) ValidList<InstitutionGroupDto> institutionGroupDtos) {
        institutionGroupService.batchAdd(institutionGroupDtos);
        return SaveResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description：删除信息
     * @Param [id]
     * <AUTHOR>
     **/
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/学校集团管理/删除学校集团")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        institutionGroupService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.InstitutionGroupVo>
     * @Description：修改信息
     * @Param [institutionGroupDto]
     * <AUTHOR>
     **/
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/学校集团管理/更新学校集团")
    @PostMapping("update")
    public ResponseBo<InstitutionGroupVo> update(@RequestBody @Validated(InstitutionGroupDto.Update.class) InstitutionGroupDto institutionGroupDto) {
        return UpdateResponseBo.ok(institutionGroupService.updateInstitutionGroup(institutionGroupDto));
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.InstitutionGroupVo>
     * @Description：列表数据
     * @Param [page]
     * <AUTHOR>
     **/
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校集团管理/查询学校集团")
    @PostMapping("datas")
    public ResponseBo<InstitutionGroupVo> datas(@RequestBody SearchBean<InstitutionGroupDto> page) {
        List<InstitutionGroupVo> datas = institutionGroupService.getInstitutionGroups(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.InstitutionGroupVo>
     * @Description：下拉框数据
     * @Param []
     * <AUTHOR>
     **/
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "下拉框数据", notes = "")
    @GetMapping("getInstitutionGroupSelect")
    public ResponseBo<BaseSelectEntity> getInstitutionGroupSelect() {
        List<BaseSelectEntity> datas = institutionGroupService.getInstitutionGroupSelect();
        return new ListResponseBo<>(datas);
    }

    /**
     * feign调用 通过集团ids 查找对应的集团名称map
     *
     * @param
     * @return
     */
    /*@ApiIgnore
    @PostMapping(value = "getInstitutionGroupNamesByIds")
    public Map<Long, String> getInstitutionGroupNamesByIds(@RequestBody Set<Long> institutionGroupIdSet)   {
        return institutionGroupService.getInstitutionGroupNamesByIds(institutionGroupIdSet);
    }*/

}
