package com.get.insurancecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author:Oliver
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:保险公司
 */
@Data
@TableName("u_insurance_company")
public class InsuranceCompany extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "公司名称")
    private String name;

    @ApiModelProperty(value = "公司简称")
    private String shortName;

    @ApiModelProperty(value = "公司介绍")
    private String description;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
}
