package com.get.permissioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.permissioncenter.dto.CommentDto;
import com.get.permissioncenter.dto.MediaAndAttachedDto;
import com.get.permissioncenter.dto.StaffContractDto;
import com.get.permissioncenter.vo.CommentVo;
import com.get.permissioncenter.vo.MediaAndAttachedVo;
import com.get.permissioncenter.vo.StaffContractVo;
import com.get.permissioncenter.entity.StaffContract;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/7/7
 * @TIME: 15:20
 * @Description: 合同管理接口
 **/
public interface IStaffContractService extends BaseService<StaffContract> {


    /**
     * 获取员工的合同详细
     *
     * @param id
     * @return
     */
    StaffContractVo getStaffContractById(Long id);

    /**
     * 新增
     *
     * @param staffContractDto
     * @return
     */
    Long addStaffContract(StaffContractDto staffContractDto);


    /**
     * 修改
     *
     * @param staffContractDto
     * @return
     */

    StaffContractVo updateStaffContractVo(StaffContractDto staffContractDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);


    /**
     * 保存上传的文件
     *
     * @param mediaAndAttachedDto
     * @return
     */
    List<MediaAndAttachedVo> addContractMedia(List<MediaAndAttachedDto> mediaAndAttachedDto);

    /**
     * @return java.util.List<com.get.permissioncenter.com.get.permissioncenter.vo.MediaAndAttachedDto>
     * @Description: 合同附件列表
     * @Param [data, page]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> getContractMedia(MediaAndAttachedDto data, Page page);

    /**
     * 获取员工所有合同
     *
     * @param contractVo
     * @return
     */
    List<StaffContractVo> getStaffContract(StaffContractDto contractVo, Page page);


    /**
     * @return java.lang.Long
     * @Description: 增加或者更新评论
     * @Param [commentDto]
     * <AUTHOR>
     **/
    Long editComment(CommentDto commentDto);

    List<StaffContract> getStaffContract(Set<Long> ids);

    /**
     * @return java.util.List<com.get.salecenter.com.get.permissioncenter.vo.CommentVo>
     * @Description: 获取所有评论
     * @Param [commentDto, page]
     * <AUTHOR>
     */
    List<CommentVo> getComments(CommentDto commentDto, Page page);

    /**
     * 根据员工ID获取最新并激活的劳动合同
     *
     * @param staffId
     * @return
     * @
     */
    StaffContract getStaffContractByStaffId(Long staffId);

}
