package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("m_receipt_form")
public class ReceiptForm extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
    /**
     * 目标类型关键字，枚举：m_institution_provider学校供应商/m_student学生
     */
    @ApiModelProperty(value = "目标类型关键字，枚举：m_institution_provider学校供应商/m_student学生")
    private String fkTypeKey;
    /**
     * 对应记录Id
     */
    @ApiModelProperty(value = "对应记录Id")
    private Long fkTypeTargetId;
    /**
     * 银行帐号Id（公司）
     */
    @ApiModelProperty(value = "银行帐号Id（公司）")
    private Long fkBankAccountId;

    @ApiModelProperty(value = "汇率调整金额,(保留两位)")
    @UpdateWithNull
    private BigDecimal amountExchangeRate;
    /**
     * 发票编号
     */
    @ApiModelProperty(value = "发票编号")
    private String fkInvoiceNum;
    /**
     * 收款单编号（系统生成）
     */
    @ApiModelProperty(value = "收款单编号（系统生成）")
    private String numSystem;
    /**
     * 收款单编号（凭证号）
     */
    @ApiModelProperty(value = "收款单编号（凭证号）")
    private String numBank;
    /**
     * 收款日期
     */
    @ApiModelProperty(value = "收款日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date receiptDate;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "银行实际到账金额")
    private BigDecimal amountBank;

    @ApiModelProperty(value = "银行实际手续费")
    private BigDecimal serviceFeeBank;
    /**
     * 收款总金额（到账金额）
     */
    @ApiModelProperty(value = "收款总金额（到账金额）")
    private BigDecimal amount;
    /**
     * 汇率（港币）
     */
    @ApiModelProperty(value = "汇率（港币）")
    private BigDecimal exchangeRateHkd;
    /**
     * 收款总金额（到账金额，港币）
     */
    @ApiModelProperty(value = "收款总金额（到账金额，港币）")
    private BigDecimal amountHkd;
    /**
     * 汇率（人民币）
     */
    @ApiModelProperty(value = "汇率（人民币）")
    private BigDecimal exchangeRateRmb;
    /**
     * 收款总金额（到账金额，人民币）
     */
    @ApiModelProperty(value = "收款总金额（到账金额，人民币）")
    private BigDecimal amountRmb;
    /**
     * 手续费
     */
    @ApiModelProperty(value = "手续费")
    private BigDecimal serviceFee;
    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;
    /**
     * 代理佣金结算状态：0待结算/1可结算，默认为0
     */
    @ApiModelProperty(value = "代理佣金结算状态：0待结算/1可结算，默认为0")
    private Integer settlementStatus;
    /**
     * 状态：0作废/1打开
     */
    @ApiModelProperty(value = "状态：0作废/1打开")
    private Integer status;
    /**
     * 旧数据财务id(gea)
     */
    @ApiModelProperty(value = "旧数据财务id(gea)")
    private String idGeaFinance;

    @ApiModelProperty(value = "实收汇率")
    private BigDecimal exchangeRate;
}