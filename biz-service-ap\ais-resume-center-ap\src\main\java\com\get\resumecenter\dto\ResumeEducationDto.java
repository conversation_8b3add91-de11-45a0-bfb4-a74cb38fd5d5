package com.get.resumecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2020/8/5
 * @TIME: 14:11
 * @Description: 教育经历
 **/
@Data
public class ResumeEducationDto  extends BaseVoEntity implements Serializable {
        private static final long serialVersionUID = 1L;


    /**
     * 所属简历Id
     */
    @NotNull(message = "简历id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "所属简历Id", required = true)
    private Long fkResumeId;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "开始时间不能为空", groups = {Add.class, Update.class})
    private Date startDate;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;
    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String institution;
    /**
     * 学历
     */
    @ApiModelProperty(value = "学历")
    private String graduationLevel;
    /**
     * 专业
     */
    @ApiModelProperty(value = "专业")
    private String major;
    /**
     * 专业描述
     */
    @ApiModelProperty(value = "专业描述")
    private String majorDescription;
    /**
     * 是否全日制：0否/1是
     */
    @ApiModelProperty(value = "是否全日制：0否/1是")
    private Boolean isFullTime;
    /**
     * 是否有留学经历：0否/1是
     */
    @ApiModelProperty(value = "是否有留学经历：0否/1是")
    private Boolean isAbroadExperience;


}
