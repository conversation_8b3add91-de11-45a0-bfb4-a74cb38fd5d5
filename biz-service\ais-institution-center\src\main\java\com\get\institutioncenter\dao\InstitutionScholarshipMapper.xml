<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionScholarshipMapper">
    <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.InstitutionScholarship">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_institution_id" jdbcType="BIGINT" property="fkInstitutionId"/>
        <result column="fk_institution_faculty_id" jdbcType="BIGINT" property="fkInstitutionFacultyId"/>
        <result column="fk_institution_faculty_id_sub" jdbcType="BIGINT" property="fkInstitutionFacultyIdSub"/>
        <result column="fk_major_level_ids" jdbcType="VARCHAR" property="fkMajorLevelIds"/>
        <result column="scholarship_title" jdbcType="VARCHAR" property="scholarshipTitle"/>
        <result column="scholarship_amount" jdbcType="VARCHAR" property="scholarshipAmount"/>
        <result column="scholarship_quota" jdbcType="VARCHAR" property="scholarshipQuota"/>
        <result column="scholarship_apply_to" jdbcType="VARCHAR" property="scholarshipApplyTo"/>
        <result column="app_condition" jdbcType="VARCHAR" property="appCondition"/>
        <result column="app_method" jdbcType="VARCHAR" property="appMethod"/>
        <result column="app_deadline" jdbcType="VARCHAR" property="appDeadline"/>
        <result column="public_level" jdbcType="VARCHAR" property="publicLevel"/>
        <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime"/>
        <result column="is_active" jdbcType="BIT" property="isActive"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs"
               type="com.get.institutioncenter.entity.InstitutionScholarship">
        <result column="app_detail" jdbcType="LONGVARCHAR" property="appDetail"/>
    </resultMap>
    <sql id="Base_Column_List">
  id, fk_institution_id, fk_institution_faculty_id, fk_institution_faculty_id_sub,
    fk_major_level_ids, scholarship_amount, scholarship_quota, app_condition, app_method,
    app_deadline, public_level, publish_time, is_active, gmt_create, gmt_create_user,
    gmt_modified, gmt_modified_user
  </sql>
    <sql id="Blob_Column_List">
    app_detail
  </sql>

    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.get.institutioncenter.entity.InstitutionScholarship">
        insert into m_institution_scholarship
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkInstitutionId != null">
                fk_institution_id,
            </if>
            <if test="fkInstitutionFacultyId != null">
                fk_institution_faculty_id,
            </if>
            <if test="fkInstitutionFacultyIdSub != null">
                fk_institution_faculty_id_sub,
            </if>
            <if test="fkMajorLevelIds != null">
                fk_major_level_ids,
            </if>
            <if test="scholarshipTitle != null">
                scholarship_title,
            </if>
            <if test="scholarshipAmount != null">
                scholarship_amount,
            </if>
            <if test="scholarshipQuota != null">
                scholarship_quota,
            </if>
            <if test="scholarshipApplyTo != null">
                scholarship_apply_to,
            </if>
            <if test="appCondition != null">
                app_condition,
            </if>
            <if test="appMethod != null">
                app_method,
            </if>
            <if test="appDeadline != null">
                app_deadline,
            </if>
            <if test="publicLevel != null">
                public_level,
            </if>
            <if test="publishTime != null">
                publish_time,
            </if>
            <if test="isActive != null">
                is_active,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
            <if test="appDetail != null">
                app_detail,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkInstitutionId != null">
                #{fkInstitutionId,jdbcType=BIGINT},
            </if>
            <if test="fkInstitutionFacultyId != null">
                #{fkInstitutionFacultyId,jdbcType=BIGINT},
            </if>
            <if test="fkInstitutionFacultyIdSub != null">
                #{fkInstitutionFacultyIdSub,jdbcType=BIGINT},
            </if>
            <if test="fkMajorLevelIds != null">
                #{fkMajorLevelIds,jdbcType=VARCHAR},
            </if>
            <if test="scholarshipTitle != null">
                #{scholarshipTitle,jdbcType=VARCHAR},
            </if>
            <if test="scholarshipAmount != null">
                #{scholarshipAmount,jdbcType=VARCHAR},
            </if>
            <if test="scholarshipQuota != null">
                #{scholarshipQuota,jdbcType=VARCHAR},
            </if>
            <if test="scholarshipApplyTo != null">
                #{scholarshipApplyTo,jdbcType=VARCHAR},
            </if>
            <if test="appCondition != null">
                #{appCondition,jdbcType=VARCHAR},
            </if>
            <if test="appMethod != null">
                #{appMethod,jdbcType=VARCHAR},
            </if>
            <if test="appDeadline != null">
                #{appDeadline,jdbcType=VARCHAR},
            </if>
            <if test="publicLevel != null">
                #{publicLevel,jdbcType=VARCHAR},
            </if>
            <if test="publishTime != null">
                #{publishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isActive != null">
                #{isActive,jdbcType=BIT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
            <if test="appDetail != null">
                #{appDetail,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <select id="getWcInstitutionScholarshipList"
            resultType="com.get.institutioncenter.vo.InstitutionScholarshipVo">
        select mis.id,mis.fk_institution_id,GROUP_CONCAT(DISTINCT fk_major_level_ids)as
        fk_major_level_ids,GROUP_CONCAT(mis.public_level)as public_level from
        m_institution_scholarship mis
        left join m_institution mi on mi.id = mis.fk_institution_id
        left join r_institution_view_order vo on vo.fk_institution_id=mis.fk_institution_id
        where mis.is_active=1 and mi.fk_area_country_id=#{fkCountryId} and vo.type=0
        <if test="schoolName !='' and schoolName !=null">
            and(
            mi.name like concat('%',#{schoolName},'%')
            or mi.name_chn like concat('%',#{schoolName},'%')
            or mi.short_name like concat('%',#{schoolName},'%')
            or mi.short_name_chn like concat('%',#{schoolName},'%')
            or CONCAT(mi.name,'（',mi.name_chn,'）')like concat('%',#{schoolName},'%'))
        </if>
        group by mis.fk_institution_id order by vo.view_order asc
    </select>
    <select id="getIsOtherModule" resultType="com.get.institutioncenter.vo.InstitutionScholarshipVo">
   select case when appFee.ys is not null then 1 else 0 end as isAppFee,
  case when deadInfo.ys is not null then 1 else 0 end as isDeadInfo,
  case when scholarship.ys is not null then 1 else 0 end as isScholarship
  from (select max(id)as ys from m_institution_app_fee2 where fk_institution_id=#{fkInstitutionId})appFee,
  (select max(id)as ys from m_institution_deadline_info2 where fk_institution_id=#{fkInstitutionId})deadInfo,
  (select max(id)as ys from m_institution_scholarship2 where fk_institution_id=#{fkInstitutionId})scholarship
    </select>
    <select id="datas" resultType="com.get.institutioncenter.vo.InstitutionScholarshipVo">
        SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
        FROM
            m_institution_scholarship s
        INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
        <if test="data.keyword!=null and data.keyword!=''">
            <if test="data.fkTableName=='u_area_country'">
                INNER u_area_country f ON f.id = a.fk_table_id_type AND (f.name LIKE CONCAT('%',#{data.keyword},'%') OR f.name_chn LIKE CONCAT('%',#{data.keyword},'%'))
            </if>
            <if test="data.fkTableName=='m_institution'">
                INNER m_institution f ON f.id = a.fk_table_id_type AND (f.name LIKE CONCAT('%',#{data.keyword},'%') OR f.name_chn LIKE CONCAT('%',#{data.keyword},'%'))
                <if test="data.fkCountryId!=null and data.fkCountryId!=''">
                   AND f.fk_area_country_id = #{data.fkCountryId}
                </if>
            </if>
            <if test="data.fkTableName=='m_institution_faculty'">
                INNER m_institution_faculty f ON f.id = a.fk_table_id_type AND (f.name LIKE CONCAT('%',#{data.keyword},'%') OR f.name_chn LIKE CONCAT('%',#{data.keyword},'%'))
            </if>
            <if test="data.fkTableName=='u_course_type_group'">
                INNER u_course_type_group f ON f.id = a.fk_table_id_type AND (f.type_group_name LIKE CONCAT('%',#{data.keyword},'%') OR f.type_group_name_chn LIKE CONCAT('%',#{data.keyword},'%'))
            </if>
            <if test="data.fkTableName=='u_course_type'">
                INNER u_course_type f ON f.id = a.fk_table_id_type AND (f.type_name LIKE CONCAT('%',#{data.keyword},'%') OR f.type_name_chn LIKE CONCAT('%',#{data.keyword},'%'))
            </if>
            <if test="data.fkTableName=='u_major_level'">
                INNER u_major_level f ON f.id = a.fk_table_id_type AND (f.level_name LIKE CONCAT('%',#{data.keyword},'%') OR f.level_name_chn LIKE CONCAT('%',#{data.keyword},'%'))
            </if>
            <if test="data.fkTableName=='m_institution_course'">
                INNER m_institution_course f ON f.id = a.fk_table_id_type AND (f.name LIKE CONCAT('%',#{data.keyword},'%') OR f.name_chn LIKE CONCAT('%',#{data.keyword},'%'))
            </if>
        </if>
        WHERE
            a.fk_table_name = 'm_institution_scholarship'
            <choose>
                <when test="data.fkTableName!=null and data.fkTableName!=''">
                    AND a.fk_table_name_type = #{data.fkTableName}
                </when>
                <otherwise>
                    AND a.fk_table_name_type = 'u_area_country'
                </otherwise>
            </choose>
            <choose>
                <when test="data.fkTableId!=null and data.fkTableId!=''">
                    AND a.fk_table_id_type = #{data.fkTableId}
                </when>
                <otherwise>
                    AND a.fk_table_id_type IN
                    <foreach collection="countryIds" open="(" separator="," close=")" item="cid">
                        #{cid}
                    </foreach>
                </otherwise>
            </choose>
            <if test="data.isActive!=null">
                AND s.is_active = #{data.isActive}
            </if>
            GROUP BY s.id
            ORDER BY s.is_active,s.gmt_create DESC
    </select>
    <select id="wechatDatas" resultType="com.get.institutioncenter.vo.WeInstitutionScholarshipVo">
        SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
        FROM
            m_institution_scholarship s
        INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
        WHERE
            a.fk_table_name = 'm_institution_scholarship'
            <if test="fkTypeKey!=null and fkTypeKey!=''">
                AND a.fk_table_name_type = #{fkTypeKey}
            </if>
            <if test="fkTableId!=null and fkTableId!=''">
                AND a.fk_table_id_type = #{fkTableId}
            </if>
            ORDER BY s.id
    </select>
    <select id="selectInfoById" resultType="com.get.institutioncenter.vo.InstitutionScholarshipVo">
        SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId,a.group_key as effectiveDate
        FROM
            m_institution_scholarship s
        INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
        WHERE
            a.fk_table_name = 'm_institution_scholarship'
           AND s.id = #{id}
        GROUP BY s.id
    </select>
    <select id="getWcInstitutionScholarshipDatas"
            resultType="com.get.institutioncenter.vo.InstitutionScholarshipVo">
        SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
        FROM
            m_institution_scholarship s
        INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
        WHERE
            a.fk_table_name =  #{fkTableName}
            AND a.fk_table_name_type = 'm_institution'
            AND a.fk_table_id_type =#{weScholarshipAppDto.institutionId}
        <if test="weScholarshipAppDto.countryId!=null and weScholarshipAppDto.countryId!=''">
            UNION ALL
            SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
            FROM
            m_institution_scholarship s
            INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
            WHERE
            a.fk_table_name =  #{fkTableName}
            AND a.fk_table_name_type = 'u_area_country'
            AND a.fk_table_id_type =#{weScholarshipAppDto.countryId}
        </if>
        <if test="weScholarshipAppDto.facultyId!=null and weScholarshipAppDto.facultyId!=''">
            UNION ALL
            SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
            FROM
            m_institution_scholarship s
            INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
            WHERE
            a.fk_table_name =  #{fkTableName}
            AND a.fk_table_name_type = 'm_institution_faculty'
            AND a.fk_table_id_type =#{weScholarshipAppDto.facultyId}
        </if>
        <if test="weScholarshipAppDto.courseGroupTypeId!=null and weScholarshipAppDto.courseGroupTypeId!=''">
            UNION ALL
            SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
            FROM
            m_institution_scholarship s
            INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
            WHERE
            a.fk_table_name =  #{fkTableName}
            AND a.fk_table_name_type = 'u_course_type_group'
            AND a.fk_table_id_type =#{weScholarshipAppDto.courseGroupTypeId}
        </if>
        <if test="weScholarshipAppDto.courseTypeId!=null and weScholarshipAppDto.courseTypeId!=''">
            UNION ALL
            SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
            FROM
            m_institution_scholarship s
            INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
            WHERE
            a.fk_table_name =  #{fkTableName}
            AND a.fk_table_name_type = 'u_course_type'
            AND a.fk_table_id_type =#{weScholarshipAppDto.courseTypeId}
        </if>
        <if test="weScholarshipAppDto.courseLevelId!=null and weScholarshipAppDto.courseLevelId!=''">
            UNION ALL
            SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
            FROM
            m_institution_scholarship s
            INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
            WHERE
            a.fk_table_name =  #{fkTableName}
            AND a.fk_table_name_type = 'u_major_level'
            AND a.fk_table_id_type =#{weScholarshipAppDto.courseLevelId}
        </if>
        <if test="weScholarshipAppDto.courseId!=null and weScholarshipAppDto.courseId!=''">
            UNION ALL
            SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
            FROM
            m_institution_scholarship s
            INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
            WHERE
            a.fk_table_name = #{fkTableName}
            AND a.fk_table_name_type = 'm_institution_course'
            AND a.fk_table_id_type =#{weScholarshipAppDto.courseId}
        </if>
    </select>
    <select id="priorityMatchingQuery" resultType="com.get.institutioncenter.vo.InstitutionScholarshipVo">

        SELECT * FROM
        (
        SELECT
        x.*,
        GROUP_CONCAT(
        DISTINCT x.fkTableName
        ORDER BY
        x.fkTableName
        ) AS fk,
        GROUP_CONCAT(
        DISTINCT a.fk_table_name_type
        ORDER BY
        a.fk_table_name_type
        ) AS fc,
        COUNT(DISTINCT x.fkTableName) as priority
        FROM
        (
            <!-- 匹配国家-->
            <if test="weScholarshipAppDto.countryId!=null and weScholarshipAppDto.countryId!=''">

                SELECT
                s.*, a.fk_table_name_type AS fkTableName,
                a.fk_table_id_type AS fkTableId
                FROM
                m_institution_scholarship s
                INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
                WHERE
                a.fk_table_name =  #{fkTableName}
                AND a.fk_table_name_type = 'u_area_country'
                AND a.fk_table_id_type =#{weScholarshipAppDto.countryId}
            </if>
        <!-- 匹配学校-->
            <if test="weScholarshipAppDto.institutionId!=null and weScholarshipAppDto.institutionId!=''">
                UNION ALL
                SELECT
                s.*, a.
                fk_table_name_type AS fkTableName,
                a.fk_table_id_type AS fkTableId
                FROM
                m_institution_scholarship s
                INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
                WHERE
                a.fk_table_name = #{fkTableName}
                AND a.fk_table_name_type = 'm_institution'
                AND a.fk_table_id_type =#{weScholarshipAppDto.institutionId}
            </if>
            <if test="weScholarshipAppDto.facultyId!=null and weScholarshipAppDto.facultyId!=''">
                UNION ALL
                SELECT
                s.*, a.fk_table_name_type AS fkTableName,
                a.fk_table_id_type AS fkTableId
                FROM
                m_institution_scholarship s
                INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
                WHERE
                a.fk_table_name =  #{fkTableName}
                AND a.fk_table_name_type = 'm_institution_faculty'
                AND a.fk_table_id_type =#{weScholarshipAppDto.facultyId}
            </if>
            <if test="weScholarshipAppDto.courseGroupTypeId!=null and weScholarshipAppDto.courseGroupTypeId!=''">
                UNION ALL
                SELECT
                s.*, a.fk_table_name_type AS fkTableName,
                a.fk_table_id_type AS fkTableId
                FROM
                m_institution_scholarship s
                INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
                WHERE
                a.fk_table_name =  #{fkTableName}
                AND a.fk_table_name_type = 'u_course_type_group'
                AND a.fk_table_id_type =#{weScholarshipAppDto.courseGroupTypeId}
            </if>
            <if test="weScholarshipAppDto.courseTypeId!=null and weScholarshipAppDto.courseTypeId!=''">
                UNION ALL
                SELECT
                s.*, a.fk_table_name_type AS fkTableName,
                a.fk_table_id_type AS fkTableId
                FROM
                m_institution_scholarship s
                INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
                WHERE
                a.fk_table_name =  #{fkTableName}
                AND a.fk_table_name_type = 'u_course_type'
                AND a.fk_table_id_type =#{weScholarshipAppDto.courseTypeId}
            </if>
            <if test="weScholarshipAppDto.courseLevelId!=null and weScholarshipAppDto.courseLevelId!=''">
                UNION ALL
                SELECT
                s.*, a.fk_table_name_type AS fkTableName,
                a.fk_table_id_type AS fkTableId
                FROM
                m_institution_scholarship s
                INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
                WHERE
                a.fk_table_name =  #{fkTableName}
                AND a.fk_table_name_type = 'u_major_level'
                AND a.fk_table_id_type =#{weScholarshipAppDto.courseLevelId}
            </if>
        <!-- 匹配课程-->
            <if test="weScholarshipAppDto.courseId!=null and weScholarshipAppDto.courseId!=''">
                UNION ALL
                SELECT
                s.*, a.fk_table_name_type AS fkTableName,
                a.fk_table_id_type AS fkTableId
                FROM
                m_institution_scholarship s
                INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
                WHERE
                a.fk_table_name = #{fkTableName}
                AND a.fk_table_name_type = 'm_institution_course'
                AND a.fk_table_id_type =#{weScholarshipAppDto.courseId}
            </if>
        ) x
        INNER JOIN r_institution_course_app_info a ON x.id = a.fk_table_id
        GROUP BY
        x.id
        ) f
        WHERE
        <foreach collection="priorityTypeKey.entrySet()" separator="OR" open="(" close=")" item="val">
            f.fk = #{val}
        </foreach>
        ORDER BY f.priority desc
    </select>
    <select id="selectInfoByIds" resultType="com.get.institutioncenter.vo.InstitutionScholarshipVo">
        SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
        FROM
            m_institution_scholarship s
        INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
        WHERE
            a.fk_table_name = 'm_institution_scholarship'
           AND s.id IN
           <foreach collection="ids" item="cid" open="(" separator="," close=")">
               #{cid}
           </foreach>
        GROUP BY s.id
    </select>
</mapper>