package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

@Data
public class KpiPlanStaffLabelVo extends BaseEntity {


    //===============实体类KpiPlanStaffLabel===============
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "KPI方案考核人员Id")
    @Column(name = "fk_kpi_plan_staff_id")
    private Long fkKpiPlanStaffId;

    @ApiModelProperty(value = "文字标签")
    @Column(name = "word_label")
    private String wordLabel;

}
