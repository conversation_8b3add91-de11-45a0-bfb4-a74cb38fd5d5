<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.AppAgentContractAccountMapper">

  <select id="findAppAccountInfoById" resultType="com.get.salecenter.vo.AppAgentContractAccountVo">
    SELECT
      CONCAT(uc.name_chn,'(',uc.name,')') as fk_area_country_name,
      CONCAT(ac.name_chn,'(',ac.name,')') as fk_area_city_name,
      CONCAT(s.name_chn,'(',s.name,')') as fk_area_state_name,
      d.name as fk_area_city_division_name,
      CASE WHEN IFNULL(t.type_name,'')='' THEN t.num ELSE CONCAT(t.type_name,'（',t.num,'）') END currency_type_name,
      c.* from m_app_agent_contract_account c
                 LEFT JOIN ais_institution_center.u_area_country uc on uc.id = c.fk_area_country_id
                 LEFT JOIN ais_institution_center.u_area_city ac on ac.id = c.fk_area_city_id
                 LEFT JOIN ais_institution_center.u_area_state s on  s.id = c.fk_area_state_id
                 LEFT JOIN ais_institution_center.u_area_city_division d on d.id = c.fk_area_city_division_id
                 LEFT JOIN ais_finance_center.u_currency_type t on t.num = c.fk_currency_type_num
    where
      c.id = #{id}
  </select>
</mapper>