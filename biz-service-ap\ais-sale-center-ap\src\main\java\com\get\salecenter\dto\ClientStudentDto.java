package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class ClientStudentDto extends BaseEntity{

    @ApiModelProperty(value = "公司Id", required = true)
    private List<Long> fkCompanyIds;

    @ApiModelProperty(value = "生日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;

    /**
     * 学生编号
     */
    @ApiModelProperty(value = "学生编号")
    @Column(name = "num")
    private String num;

    @ApiModelProperty(value = "学生姓名")
    private String name;

    @ApiModelProperty(value = "绑定代理名称")
    private String agentName;

    @ApiModelProperty(value = "业务国家ids")
    private List<Long> fkBusinessCountryIds;

    @ApiModelProperty(value = "申请国家")
    @NotNull(message = "申请国家不能为空")
    private Long countryId;
}
