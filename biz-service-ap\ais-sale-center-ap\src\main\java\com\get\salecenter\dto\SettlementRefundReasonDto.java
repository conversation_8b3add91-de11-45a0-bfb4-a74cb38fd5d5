package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class SettlementRefundReasonDto extends BaseVoEntity{

    @NotBlank(message = "类型名称不能为空",groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    @ApiModelProperty("原因")
    private String reasonName;

    @ApiModelProperty("排序")
    private Integer viewOrder;

 
}
