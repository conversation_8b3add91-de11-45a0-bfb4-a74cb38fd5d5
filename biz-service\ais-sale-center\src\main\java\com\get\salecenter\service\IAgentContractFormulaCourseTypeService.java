package com.get.salecenter.service;


import com.get.salecenter.dto.AgentContractFormulaCourseTypeDto;

import java.util.List;
import java.util.Map;

/**
 * @author: Sea
 * @create: 2021/1/6 15:08
 * @verison: 1.0
 * @description:
 */
public interface IAgentContractFormulaCourseTypeService {
    /**
     * @return void
     * @Description :新增
     * @Param [agentContractFormulaCourseTypeDto]
     * <AUTHOR>
     */
    Long addAgentContractFormulaCourseType(AgentContractFormulaCourseTypeDto agentContractFormulaCourseTypeDto);

    /**
     * @return void
     * @Description :删除
     * @Param [agentContractFormulaId]
     * <AUTHOR>
     */
    void deleteByFkid(Long agentContractFormulaId);

    /**
     * @Description :通过学生代理合同公式id 查找对应课程类型名称
     * @Param [agentContractFormulaIds]
     * <AUTHOR>
     */
    Map<Long, String> getCourseTypeNameMapByFkids(List<Long> agentContractFormulaIds);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过学生代理合同公式id 查找对应课程类型id
     * @Param [agentContractFormulaId]
     * <AUTHOR>
     */
    List<Long> getCourseTypeIdListByFkid(Long agentContractFormulaId);

}
