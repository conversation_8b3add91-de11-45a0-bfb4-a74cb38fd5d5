package com.get.votingcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.utils.GeneralTool;
import com.get.votingcenter.vo.VotingItemVo;
import com.get.votingcenter.service.IMediaAndAttachedService;
import com.get.votingcenter.service.IVotingItemService;
import com.get.votingcenter.dto.VotingItemListDto;
import com.get.votingcenter.dto.VotingItemUpdateDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

;

/**
 * @author: Hardy
 * @create: 2021/9/24 11:24
 * @verison: 1.0
 * @description:
 */
@Api(tags = "投票项管理")
@RestController
@RequestMapping("/voting/votingItem")
@Slf4j
public class VotingItemController {

    @Resource
    private IVotingItemService votingItemService;
    @Resource
    private IMediaAndAttachedService mediaAndAttachedService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.votingcenter.vo.VotingItemVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.VOTINGCENTER, type = LoggerOptTypeConst.LIST, description = "投票中心/投票项管理/查询投票规则")
    @PostMapping("datas")
    public ResponseBo<VotingItemVo> datas(@RequestBody SearchBean<VotingItemListDto> page) {
        List<VotingItemVo> datas = votingItemService.getVotingItems(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :
     * @Param [VotingItemUpdateDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.VOTINGCENTER, type = LoggerOptTypeConst.ADD, description = "投票中心/投票项管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(VotingItemUpdateDto.Add.class) VotingItemUpdateDto votingItemUpdateDto) {
        return SaveResponseBo.ok(votingItemService.addVotingItem(votingItemUpdateDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :
     * @Param [VotingItemUpdateDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.VOTINGCENTER, type = LoggerOptTypeConst.EDIT, description = "投票中心/投票项管理/修改")
    @PostMapping("update")
    public ResponseBo<VotingItemVo> update(@RequestBody @Validated(VotingItemUpdateDto.Update.class) VotingItemUpdateDto votingItemUpdateDto) {
        return UpdateResponseBo.ok(votingItemService.updateVotingItem(votingItemUpdateDto));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.VOTINGCENTER, type = LoggerOptTypeConst.DELETE, description = "投票中心/投票项管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        votingItemService.deleteVotingItem(id);
        return DeleteResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.votingcenter.vo.VotingItemVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "投票主题详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.VOTINGCENTER, type = LoggerOptTypeConst.DETAIL, description = "投票中心/投票项管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<VotingItemVo> detail(@PathVariable("id") Long id) {
        VotingItemVo data = votingItemService.findVotingItemById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 上移下移
     * @Param [VotingItemListVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.VOTINGCENTER, type = LoggerOptTypeConst.EDIT, description = "投票中心/投票项管理/排序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<VotingItemListDto> votingItemListDtos) {
        votingItemService.movingOrder(votingItemListDtos);
        return ResponseBo.ok();
    }

    /**
     * 投票项状态下拉框数据
     *
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "投票项状态下拉框数据", notes = "")
    @GetMapping("getVotingItemSelect")
    public ResponseBo getVotingItemSelect() {
        return new ListResponseBo<>(ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.VOTING_ITEM_STATUS));
    }

        /**
        * @Description: 投票项管理图片上传
        * @Param:
        * @return:
        * @Author: Walker
        * @Date: 2022/8/22
        */
        @VerifyPermission(IsVerify = false)
        @ApiOperation(value = "投票项管理图片上传")
        @OperationLogger(module = LoggerModulesConsts.VOTINGCENTER, type = LoggerOptTypeConst.ADD, description = "投票中心/投票项管理/投票项管理图片上传")
        @PostMapping("/upload")
        public ResponseBo msoUpload(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files) {
            ResponseBo responseBo = new ResponseBo();
            if (GeneralTool.isNotEmpty(files)) {
                responseBo.put("data", mediaAndAttachedService.upload(files, LoggerModulesConsts.VOTINGCENTER));
            }
            return responseBo;
        }
}
