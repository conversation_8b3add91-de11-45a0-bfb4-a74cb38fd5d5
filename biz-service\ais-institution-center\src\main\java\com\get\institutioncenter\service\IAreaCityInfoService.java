package com.get.institutioncenter.service;


import com.get.common.result.Page;
import com.get.core.mybatis.utils.ValidList;
import com.get.institutioncenter.dto.AreaCityInfoDto;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.vo.AreaCityInfoVo;
import com.get.institutioncenter.vo.MediaAndAttachedVo;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/3/2 11:20
 * @verison: 1.0
 * @description:
 */
public interface IAreaCityInfoService {
    /**
     * @return com.get.salecenter.vo.AreaCityInfoVo
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    AreaCityInfoVo findAreaCityInfoById(Long id);

    /**
     * @return java.lang.Long
     * @Description :新增
     * @Param [areaCityInfoDto]
     * <AUTHOR>
     */
    Long add(AreaCityInfoDto areaCityInfoDto);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.salecenter.vo.AreaCityInfoVo
     * @Description :修改
     * @Param [areaCityInfoDto]
     * <AUTHOR>
     */
    AreaCityInfoVo updateAreaCityInfo(AreaCityInfoDto areaCityInfoDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.AreaCityInfoVo>
     * @Description :列表
     * @Param [areaCityInfoDto, page]
     * <AUTHOR>
     */
    List<AreaCityInfoVo> getAreaCityInfos(AreaCityInfoDto areaCityInfoDto, Page page);

    /**
     * @return java.util.List<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 附件列表
     * @Param [data, page]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> getItemMedia(MediaAndAttachedDto data, Page page);

    /**
     * @return java.lang.Long
     * @Description: 新增附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVo);
}
