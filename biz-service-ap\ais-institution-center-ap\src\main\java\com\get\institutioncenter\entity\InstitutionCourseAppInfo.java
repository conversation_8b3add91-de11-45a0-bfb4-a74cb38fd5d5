package com.get.institutioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@TableName("r_institution_course_app_info")
public class InstitutionCourseAppInfo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "表名")
    @Column(name = "fk_table_name")
    private String fkTableName;

    @ApiModelProperty(value = "表id")
    @Column(name = "fk_table_id")
    private Long fkTableId;

    @ApiModelProperty(value = "类型表名")
    @Column(name = "fk_table_name_type")
    private String fkTableNameType;

    @ApiModelProperty(value = "类型id")
    @Column(name = "fk_table_id_type")
    private Long fkTableIdType;

    @ApiModelProperty(value = "分组key")
    @Column(name = "group_key")
    private String groupKey;
}
