package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * 绑定业务学生VO
 */
@Data
public class BoundBusinessStudentDto {

    @ApiModelProperty(value = "学生Id集合")
    @NotEmpty(message = "学生Id集合不能为空")
    private Set<Long> fkStudentIds;

    @ApiModelProperty(value = "学生资源Id")
    @NotNull(message = "学生资源Id不能为空")
    private Long fkClientId;
}
