package com.get.institutioncenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.entity.ContractFormulaPreInstitutionGroup;
import com.get.institutioncenter.dto.ContractFormulaInstitutionGroupDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/26 15:01
 * @verison: 1.0
 * @description:
 */
public interface IContractFormulaPreInstitutionGroupService extends BaseService<ContractFormulaPreInstitutionGroup> {
    /**
     * @return java.lang.Long
     * @Description :新增
     * @Param [contractFormulaInstitutionGroupDto]
     * <AUTHOR>
     */
    Long addContractFormulaInstitutionGroup(ContractFormulaInstitutionGroupDto contractFormulaInstitutionGroupDto);

    /**
     * @return void
     * @Description :
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    void deleteByFkid(Long contractFormulaId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过合同公式id 查找对应集团ids
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<Long> getGroupIdListByFkid(Long contractFormulaId);
}
