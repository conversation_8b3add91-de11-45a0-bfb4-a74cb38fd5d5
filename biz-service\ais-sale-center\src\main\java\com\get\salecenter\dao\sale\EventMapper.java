package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.dto.EventRegistrationStatisticsDto;
import com.get.salecenter.vo.EventRegistrationStatisticsVo;
import com.get.salecenter.vo.EventVo;
import com.get.salecenter.entity.Event;
import com.get.salecenter.dto.EventCostDto;
import com.get.salecenter.dto.EventDto;
import com.get.salecenter.dto.query.EventQueryDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/12/7 15:12
 * @verison: 1.0
 * @description:
 */
@Mapper
public interface EventMapper extends BaseMapper<Event>, GetMapper<Event> {

    /**
     * @return java.util.List<java.lang.String>
     * @Description :获取院校/集团/项目下拉框数据
     * @Param [companyId]
     * <AUTHOR>
     */
    List<String> getEventTargetList(@Param("companyId") Long companyId);

    /**
     * @return java.util.List<java.lang.String>
     * @Description :获取活动主题 下拉框数据
     * @Param [companyId]
     * <AUTHOR>
     */
    List<String> getEventThemeList(@Param("companyId") Long companyId);

    /**
     * @return java.util.List<com.get.salecenter.entity.Event>
     * @Description :列表
     * @Param [eventVo]
     * <AUTHOR>
     */
    List<Event> getEventList(IPage<EventVo> iPage, @Param("eventDto") EventQueryDto eventQueryVo,@Param("fkDepartIds") Set<Long> fkDepartIds);

    /**
     * @return java.util.List<com.get.salecenter.entity.EventVo>
     * @Description :活动数据总览列表-不选择搜索维度
     * @Param [eventDto]
     * <AUTHOR>
     */
    List<EventVo> getEventDatasGroupByCountry(IPage<EventVo> iPage, @Param("eventDto") EventDto eventDto);

    /**
     * @return java.util.List<com.get.salecenter.entity.EventVo>
     * @Description :活动数据总览列表-活动对象国家为搜索维度
     * @Param [eventVo]
     * <AUTHOR>
     */
    List<EventVo> getEventDatasGroupByAreaState(IPage<EventVo> iPage, @Param("eventDto") EventQueryDto eventQueryDto);

    /**
     * @return java.util.List<com.get.salecenter.entity.EventVo>
     * @Description :活动数据总览列表-举办区域为搜索维度
     * @Param [eventVo]
     * <AUTHOR>
     */
    List<EventVo> getEventDatasGroupByAreaCity(IPage<EventVo> iPage, @Param("eventDto") EventQueryDto eventQueryDto);

    /**
     * @return java.util.List<com.get.salecenter.entity.EventVo>
     * @Description :活动数据总览列表-负责人为搜索维度
     * @Param [eventVo]
     * <AUTHOR>
     */
    List<EventVo> getEventDatasGroupByStaffIdLeader(IPage<EventVo> iPage, @Param("eventDto") EventQueryDto eventQueryDto);

    /**
     * @return java.util.List<com.get.salecenter.entity.EventVo>
     * @Description :活动数据总览列表-负责人为搜索维度
     * @Param [eventVo]
     * <AUTHOR>
     */
    List<EventVo> getEventDatasGroupByStaffIdLeader2(@Param("eventDto") EventQueryDto eventQueryDto);

    /**
     * @return java.util.List<com.get.salecenter.entity.EventVo>
     * @Description :活动数据总览列表-活动类型为搜索维度
     * @Param [eventVo]
     * <AUTHOR>
     */
    List<EventVo> getEventDatasGroupByEventTypeId(@Param("eventDto") EventQueryDto eventQueryDto);

    /**
     * @return java.util.List<com.get.salecenter.entity.EventVo>
     * @Description :活动数据总览显示框数据
     * @Param [eventDto]
     * <AUTHOR>
     */
    List<EventVo> getEventDatas(@Param("eventDto") EventDto eventDto);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :活动数据地区下拉框
     * @Param [companyId]
     * <AUTHOR>
     */
    List<Long> getEventStateList(@Param("companyId") Long companyId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :活动数据城市下拉框
     * @Param [companyId, eventCityIds]
     * <AUTHOR>
     */
    List<Long> getEventCityList(@Param("companyId") Long companyId, @Param("eventStateIds") List<Long> eventStateIds);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :获取国家下拉框
     * @Param [companyId]
     * <AUTHOR>
     */
    List<Long> getEventTargetCountryList(@Param("companyId") Long companyId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :获取负责人下拉框
     * @Param [companyId]
     * <AUTHOR>
     */
    List<Long> getEventStaffList(@Param("companyId") Long companyId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :获取第二负责人下拉框
     * @Param [companyId]
     * <AUTHOR>
     */
    List<Long> getEventStaff2List(@Param("companyId") Long companyId);

    /**
     * @return List<EventVo>
     * @Description : 按活动对象国家获取
     * @Param [eventVo]
     * <AUTHOR>
     */
    List<EventVo> getEventDatasGroupByTargetCountry(@Param("eventDto") EventQueryDto eventQueryDto);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :获取活动类型下拉框
     * @Param [companyId]
     * <AUTHOR>
     */
    List<Long> getEventTypeList(@Param("companyId") Long companyId,@Param("fkDepartIds") Set<Long> fkDepartIds);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :获取活动城市下拉框
     * @Param [companyId]
     * <AUTHOR>
     */
    List<Long> getEventCitySelectList(@Param("companyId") Long companyId);

    /**
     * @return List<EventVo>
     * @Description : 按活动对象国家获取统计
     * @Param [eventVo]
     * <AUTHOR>
     */
    List<EventVo> getEventDataStatisticsByTargetCountry(IPage<EventVo> iPage, @Param("eventDto") EventQueryDto eventQueryDto);

    /**
     * 活动数据统计多选
     *
     * @param eventQueryDto
     * @return
     */
    List<EventVo> getEventDataStatistics(IPage<EventVo> iPage, @Param("eventDto") EventQueryDto eventQueryDto);

    /**
     * 活动费用列表
     *
     * @return
     */
    List<EventVo> getCostList(IPage<EventVo> iPage, @Param("eventCostDto") EventCostDto eventCostDto);

    /**
     * 模糊搜索活动
     *
     * @param companyId
     * @param eventName
     * @return
     */
    List<EventVo> getEventsByName(@Param("companyId") Long companyId, @Param("eventName") String eventName);

    /**
     * 活动下拉
     * @param companyId
     * @param staffFollowerIds
     * @return
     */
    List<EventVo> getEventSelect(@Param("companyId")Long companyId, @Param("staffFollowerIds")List<Long> staffFollowerIds);

    List<EventRegistrationStatisticsVo> getEventRegistrationStatistics(IPage<EventRegistrationStatisticsVo> iPage, @Param("eventRegistrationStatisticsVo") EventRegistrationStatisticsDto eventRegistrationStatisticsVo);
}