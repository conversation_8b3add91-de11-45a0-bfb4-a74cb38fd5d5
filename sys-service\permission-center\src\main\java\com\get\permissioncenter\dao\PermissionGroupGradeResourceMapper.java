package com.get.permissioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.permissioncenter.dto.ResourceDto;
import com.get.permissioncenter.entity.PermissionGroupGradeResource;
import com.get.permissioncenter.dto.PermissionGroupGradeResourceDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface PermissionGroupGradeResourceMapper extends BaseMapper<PermissionGroupGradeResource> {
    @Override
    int insert(PermissionGroupGradeResource record);

    List<String> findResourcesByStaffId(@Param("StaffId") Long StaffId);

    Integer CountByGroupAndGrade(@Param("groupId") Long groupId, @Param("gradeId") Long gradeId);

    void deleteByGroupAndGrade(@Param("groupId") Long groupId, @Param("gradeId") Long gradeId);

    int deleteByMap(Map<String, Object> map);

    List<PermissionGroupGradeResource> findByMap(Map<String, Object> map);

    void updateByResourceKey(ResourceDto resourceDto);

    List<PermissionGroupGradeResource> selectListByCompanyId(@Param("permissionGroupGradeResourceDto") PermissionGroupGradeResourceDto permissionGroupGradeResourceDto, @Param("fkCompanyId") Long fkCompanyId);
}