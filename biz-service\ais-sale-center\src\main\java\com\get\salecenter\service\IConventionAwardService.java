package com.get.salecenter.service;


import com.get.salecenter.vo.ConventionAwardVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.vo.RecordAwardVo;
import com.get.salecenter.dto.ConventionAwardDto;
import com.get.salecenter.dto.MediaAndAttachedDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2021/9/10
 * @TIME: 11:48
 * @Description:
 **/
public interface IConventionAwardService {
    /**
     * @return com.get.salecenter.vo.ConventionAwardVo
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    ConventionAwardVo findConventionAwardById(Long id);

    /**
     * @return void
     * @Description :批量新增
     * @Param [conventionAwardDtos]
     * <AUTHOR>
     */
    void batchAdd(List<ConventionAwardDto> conventionAwardDtos);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.salecenter.vo.ConventionAwardVo
     * @Description :修改
     * @Param [conventionAwardDto]
     * <AUTHOR>
     */
    ConventionAwardVo updateConventionAward(ConventionAwardDto conventionAwardDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.ConventionAwardDto>
     * @Description :列表
     * @Param [conventionAwardDto, page]
     * <AUTHOR>
     */
    List<ConventionAwardVo> getConventionAwards(ConventionAwardDto conventionAwardDto);

    /**
     * @return void
     * @Description :上移下移
     * @Param [conventionAwardDtos]
     * <AUTHOR>
     */
    void movingOrder(List<ConventionAwardDto> conventionAwardDtos);

    /**
     * 保存上传的文件
     *
     * @param mediaAttachedVo
     * @return
     */
    List<MediaAndAttachedVo> addConventionAwardMedia(List<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * 获取角色下拉
     *
     * @return
     */
    List<Map<String, Object>> findRolesType();

    /**
     * 分享二维码
     *
     * @return
     */
    void getShareCode(HttpServletResponse response) throws Exception;

    /**
     * @Description: 根据名称模糊搜索奖品ids
     * @Author: Jerry
     * @Date:16:11 2021/9/15
     */
    Set<Long> getConventionAwardsIdsByConventionAwardsName(String conventionAwardsName);

    /**
     * @Description: 根据奖品ids获取名称
     * @Author: Jerry
     * @Date:16:51 2021/9/15
     */
    Map<Long, String> getConventionAwardsNameByConventionAwardsIds(Set<Long> conventionAwardsIds);

    /**
     * @Description: 奖品列表，大屏幕显示
     * @Author: jack
     * @Date:16:51 2021/9/15
     */
    List<RecordAwardVo> listAwardsAndTickets(Long id);


    /**
     * 排序（拖拽）
     *
     * @Date 14:23 2024/3/12
     * <AUTHOR>
     */
    void dragMovingOrder(Integer end, Integer start);

}
