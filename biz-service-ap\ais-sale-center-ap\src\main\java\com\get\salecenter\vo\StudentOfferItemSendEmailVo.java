package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class StudentOfferItemSendEmailVo {

    @ApiModelProperty("代理联系人邮箱")
    private List<ContactPersonEmailDto> contactPersonEmailDtos;

    @ApiModelProperty("发送人邮箱")
    private StudentOfferItemStaffEmailDto studentOfferItemStaffEmailDto;

    @ApiModelProperty("是否拒收系统邮件：0否/1是")
    private Boolean isRejectEmail = false;

    @ApiModelProperty("代理名称")
    private String fkAgentName;

    @ApiModelProperty("发送错误信息收集邮箱")
    private String errorEmail;

    @ApiModelProperty("是否财务专用：0否/1是")
    private Boolean isFollowHidden = false;

    @ApiModelProperty("学习计划id")
    private Long fkStudentOfferItemStepId;

    @ApiModelProperty("是否已接受学习计划：0否/1是")
    private Boolean isStepOfferAccepted = false;

    //是否已有学生到达过os后：0否/1是
    private Boolean isStudentArrivedOs = false;

    @Data
    public static class StudentOfferItemStaffEmailDto{

        @ApiModelProperty("发送邮箱")
        private String userEmail;

        @ApiModelProperty("密码")
        private String password;

        @ApiModelProperty("员工名字")
        private String staffName;
    }

    @Data
    public static class ContactPersonEmailDto{

        @ApiModelProperty("代理联系人邮箱")
        private String agentEmail;

//        @ApiModelProperty("代理联系人名称")
//        private String agentName;

    }

}
