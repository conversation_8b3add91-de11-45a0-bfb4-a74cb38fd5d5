package com.get.salecenter.dto;


import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.remindercenter.enums.EmailTemplateEnum;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * 邮件发送上下文
 * 
 * <AUTHOR>
 * @date 2025/07/18
 */
@Data
@Builder
public class EmailSendContext {

    /**
     * 项目关键字（业务数据库）
     */
    private ProjectKeyEnum projectKey;

    /**
     * 关联业务表主键ID
     */
    private Long tableId;

    /**
     * 关联业务表名
     */
    private TableEnum tableName;

    /**
     * 收件人邮箱地址
     */
    private String recipient;

    /**
     * 邮件标题
     */
    private String title;

    /**
     * 邮件模板类型
     */
    private EmailTemplateEnum emailTemplate;

    /**
     * 邮件参数Map
     */
    private Map<String, String> parameters;

    /**
     * 邮件执行时间（可选，默认当前时间）
     */
    private Date operationTime;

    /**
     * 员工ID（用于获取语言配置）
     */
    private Long staffId;

}