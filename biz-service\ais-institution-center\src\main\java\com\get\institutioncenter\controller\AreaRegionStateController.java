package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.vo.AreaCountryVo;
import com.get.institutioncenter.vo.AreaRegionVo;
import com.get.institutioncenter.service.IAreaRegionStateService;
import com.get.institutioncenter.dto.AreaRegionDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/28 14:42
 */
@Api(tags = "业务区域管理")
@RestController
@RequestMapping("/institution/areaRegionState")
public class AreaRegionStateController {

    @Resource
    private IAreaRegionStateService areaRegionStateService;

    /**
     * 树状图
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "树状图", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/业务区域管理/树状图")
    @PostMapping("getTreeList")
    public ResponseBo<AreaCountryVo> getTreeList() {
        List<AreaCountryVo> treeList = areaRegionStateService.getTreeList();
        return new ListResponseBo<>(treeList);
    }


    /**
     * 根据关键字搜索
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为搜索关键字")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/业务区域管理/列表数据")
    @PostMapping("datas")
    public ResponseBo<AreaRegionVo> datas(@RequestBody SearchBean<AreaRegionDto> page) {
        List<AreaRegionVo> datas = areaRegionStateService.getAreaRegionStates(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * 批量新增
     *
     * @param areaRegionDtos
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/业务区域管理/批量新增")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(AreaRegionDto.Add.class) ValidList<AreaRegionDto> areaRegionDtos) {
        areaRegionStateService.batchAdd(areaRegionDtos);
        return SaveResponseBo.ok();
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/业务区域管理/删除州省")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        areaRegionStateService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param areaRegionDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/区域管理-州省配置/更新州省")
    @PostMapping("update")
    public ResponseBo update(@RequestBody  @Validated(AreaRegionDto.Update.class)  AreaRegionDto areaRegionDto) {
        areaRegionStateService.updateAreaState(areaRegionDto);
        return UpdateResponseBo.ok();
    }


    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/业务区域管理/州省详情")
    @GetMapping("/{id}")
    public ResponseBo<AreaRegionVo> detail(@PathVariable("id") Long id) {
        AreaRegionVo data = areaRegionStateService.findAreaRegionById(id);
        return new ResponseBo<>(data);
    }


    /**
     * 获取当前国家的业务区域
     *
     * @param fkCountryId
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "业务区域下拉框", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/业务区域管理/业务区域下拉框")
    @GetMapping("/areaRegionSelect/{fkCountryId}")
    public ResponseBo<BaseSelectEntity> areaRegionSelect(@PathVariable("fkCountryId") Long fkCountryId) {
        return new ListResponseBo<>(areaRegionStateService.areaRegionSelectByCountryId(fkCountryId));
    }

    /**
     * feign调用,根据ids获取对象集合
     * @param ids
     * @return
     * @
     */
 /*   @ApiIgnore
    @GetMapping("/getAreaRegionDtoByIds")
    public Map<Long,AreaRegionVo> getAreaRegionDtoByIds(@RequestParam("ids") Set<Long> ids)  {
        Map<Long,AreaRegionVo> data = areaRegionStateService.getAreaRegionDtoByIds(ids);
        return data;
    }*/


    /**
     * @Description: 上移下移
     * @Author: Jerry
     * @Date:9:54 2021/10/9
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/业务区域管理/上移下移")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<AreaRegionDto> areaRegionDtos) {
        areaRegionStateService.movingOrder(areaRegionDtos);
        return ResponseBo.ok();
    }
}
