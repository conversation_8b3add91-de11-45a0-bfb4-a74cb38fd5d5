package com.get.insurancecenter.vo.commission;

import com.get.insurancecenter.entity.InsuranceOrderSettlement;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author:Oliver
 * @Date: 2025/6/23
 * @Version 1.0
 * @apiNote:确认结算订单VO
 */
@Data
public class SettledOrderVo extends InsuranceOrderSettlement {

    @ApiModelProperty(value = "结算单ID")
    private Long settlementBillId;

    @ApiModelProperty(value = "是否绑定付款单-针对已结算订单列表")
    private Boolean isBindPayBill = Boolean.FALSE;

    @ApiModelProperty(value = "代理ID")
    private Long agentId;

    @ApiModelProperty(value = "代理信息")
    private AgentVo agent;

    @ApiModelProperty(value = "结算总额")
    private BigDecimal settlementAmount;

    @ApiModelProperty(value = "代理合同结算账户Id")
    private Long agentContractAccountId;

    @ApiModelProperty(value = "结算账户币种")
    private String settlementCurrencyTypeNum;
}
