package com.get.aisplatformcenterap.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class ReleaseInfoAndItemVo extends BaseEntity {
    @ApiModelProperty(value = "平台应用Id")
    private Long fkPlatformId;
    //平台应用CODE
    @ApiModelProperty(value = "平台应用CODE")
    private String fkPlatformCode;
    //标题
    @ApiModelProperty(value = "标题")
    private String title;
    //版本号
    @ApiModelProperty(value = "版本号")
    private String versionNum;
    //枚举：0待发布/1已发布/2已撤回
    @ApiModelProperty(value = "枚举：0待发布/1已发布/2已撤回")
    private Integer status;
    //发布时间
    @ApiModelProperty(value = "发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date releaseTime;
    //发布人
    @ApiModelProperty(value = "发布人")
    private String releaseUser;
    //撤回时间
    @ApiModelProperty(value = "撤回时间")
    private Date withdrawTime;
    //撤回人
    @ApiModelProperty(value = "撤回人")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String withdrawUser;

    @ApiModelProperty(value = "发版信息子项")
    private List<ReleaseInfoItemVo> releaseInfoItemVos;
}
