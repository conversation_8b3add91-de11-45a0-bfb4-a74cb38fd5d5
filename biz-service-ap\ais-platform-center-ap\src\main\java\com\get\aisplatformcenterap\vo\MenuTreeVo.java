package com.get.aisplatformcenterap.vo;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
public class MenuTreeVo {
    @ApiModelProperty(value = "菜单ID")
    private Long id;

    @ApiModelProperty(value = "菜单ID")
    private Long fkPlatformId;

    @ApiModelProperty(value = "菜单ID")
    private String fkPlatformCode;

    @ApiModelProperty(value = "父菜单Id")
    private Long fkParentMenuId;

    @ApiModelProperty(value = "菜单类型：0目录，1菜单，2按钮，3自定义")
    private Integer menuType;

    @ApiModelProperty(value = "权限标识")
    private String permissionKey;

    @ApiModelProperty(value = "菜单图标")
    private String icon;

    @ApiModelProperty(value = "菜单名称")
    private String name;

    @ApiModelProperty(value = "菜单名称（英文）")
    private String nameEn;

    @ApiModelProperty(value = "路由路径")
    private String path;

    @ApiModelProperty(value = "是否可见，0隐藏，1显示")
    private Integer isVisible;

    @ApiModelProperty(value = "是否缓存，0否，1是")
    private Integer isKeepAlive;

    @ApiModelProperty(value = "是否内嵌，0否，1是")
    private Integer isEmbedded;

    @ApiModelProperty(value = "排序值，越小越靠前")
    private Integer viewOrder;

    @ApiModelProperty(value = "子菜单")
    private List<MenuTreeVo> childTree;

}