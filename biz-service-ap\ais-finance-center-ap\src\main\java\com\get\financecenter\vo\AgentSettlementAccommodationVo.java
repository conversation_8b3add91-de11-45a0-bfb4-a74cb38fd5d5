package com.get.financecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 留学住宿Dto
 *
 * <AUTHOR>
 * @date 2022/1/10 17:24
 */
@Data
public class AgentSettlementAccommodationVo extends BaseVoEntity implements Serializable {

    @ApiModelProperty(value = "学生名字")
    private String studentName;

    @ApiModelProperty(value = "住宿id")
    private Long accommodationId;

    @ApiModelProperty(value = "国家id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "国家名")
    private String countryName;

    @ApiModelProperty(value = "公寓名称")
    private String apartmentName;

    @ApiModelProperty(value = "入住日期")
    private Date checkInDate;

    @ApiModelProperty(value = "退房日期")
    private Date checkOutDate;

    @ApiModelProperty(value = "住宿天数")
    private Integer duration;

    @ApiModelProperty(value = "应付币种")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "应付金额")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal payableAmount;

    @ApiModelProperty(value = "已付金额")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal paidAmount;

    @ApiModelProperty(value = "应付差额")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal differenceAmount;

    private Integer status;

    @ApiModelProperty(value = "收款状态  0:未收 1：部分已收 2：已收齐")
    private String statusName;

    @ApiModelProperty(value = "业务类型Key")
    private String fkTypeKey;

    @ApiModelProperty(value = "结算标记")
    private List<PayablePlanSettlementAgentAccountVo> payablePlanSettlementAgentAccountDtoList;

    @ApiModelProperty(value = "实际支付金额")
    private BigDecimal amountActual;

    @ApiModelProperty(value = "实际手续费金额")
    private BigDecimal serviceFeeActual;

    @ApiModelProperty(value = "预付金额")
    private BigDecimal prepaymentAmount;

    @ApiModelProperty(value = "帐号导出时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date accountExportTime;

    @ApiModelProperty(value = "是否回滚，0否/1是 ")
    private Boolean rollBack;

    @ApiModelProperty("结算通知信息")
    private String commissionNotice;

    @ApiModelProperty("结算通知批次号")
    private String numSettlementBatch;

    @ApiModelProperty("结算通知备注")
    private String remark;

    @ApiModelProperty(value = "结算ids")
    private String settlementIds;

    @ApiModelProperty(value = "学生代理合同账户Id")
    private Long fkAgentContractAccountId;

    @ApiModelProperty(value = "币种编号（代理账户）")
    private String accountCurrencyTypeNum;

    @ApiModelProperty(value = "币种名（代理账户）")
    private String accountCurrencyName;

    @ApiModelProperty(value = "预付标记")
    private Boolean prepaidMark;

    @ApiModelProperty("代理id")
    private Long agentId;

    @ApiModelProperty(value = "预付/新增结算设置时间")
    private Date settlementCreateTime;

    @ApiModelProperty(value = "佣金标记")
    private String commissionMark;

}
