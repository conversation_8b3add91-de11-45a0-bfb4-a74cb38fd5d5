package com.get.aisplatformcenterap.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Partner (SystemMenu)表实体类
 */
@Data
@TableName("system_menu")
public class SystemMenu extends BaseEntity {
    //平台应用Id
    @ApiModelProperty(value = "平台应用Id")
    private Long fkPlatformId;
    //平台应用CODE
    @ApiModelProperty(value = "平台应用CODE")
    private String fkPlatformCode;
    //父菜单Id
    @ApiModelProperty(value = "父菜单Id")
    private Long fkParentMenuId;
    //菜单类型：0目录，1菜单，2按钮，3自定义
    @ApiModelProperty(value = "菜单类型：0目录，1菜单，2按钮，3自定义")
    private Integer menuType;
    //权限标识
    @ApiModelProperty(value = "权限标识")
    private String permissionKey;
    //菜单图标
    @ApiModelProperty(value = "菜单图标")
    private String icon;
    //菜单名称
    @ApiModelProperty(value = "菜单名称")
    private String name;
    //菜单名称（英文）
    @ApiModelProperty(value = "菜单名称（英文）")
    private String nameEn;
    //路由路径
    @ApiModelProperty(value = "路由路径")
    private String path;
    //是否可见，0隐藏，1显示
    @ApiModelProperty(value = "是否可见，0隐藏，1显示")
    private Boolean isVisible;
    //是否缓存，0否，1是
    @ApiModelProperty(value = "是否缓存，0否，1是")
    private Boolean isKeepAlive;
    //是否内嵌，0否，1是
    @ApiModelProperty(value = "是否内嵌，0否，1是")
    private Boolean isEmbedded;
    //删除标志，0未删除，1已删除
    @ApiModelProperty(value = "删除标志，0未删除，1已删除")
    private Boolean isDelFlag;
    //排序值，越小越靠前
    @ApiModelProperty(value = "排序值，越小越靠前")
    private Integer viewOrder;
}

