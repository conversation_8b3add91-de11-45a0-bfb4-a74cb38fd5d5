package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.ContractFormulaCommissionMapper;
import com.get.institutioncenter.vo.ContractFormulaCommissionVo;
import com.get.institutioncenter.entity.ContractFormulaCommission;
import com.get.institutioncenter.service.IContractFormulaCommissionService;
import com.get.institutioncenter.dto.ContractFormulaCommissionDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2021/4/23 17:12
 * @verison: 1.0
 * @description:
 */
@Service
public class ContractFormulaCommissionServiceImpl extends BaseServiceImpl<ContractFormulaCommissionMapper, ContractFormulaCommission> implements IContractFormulaCommissionService {
    @Resource
    private ContractFormulaCommissionMapper contractFormulaCommissionMapper;
    @Resource
    private UtilService utilService;

    @Override
    public Long addContractFormulaCommission(ContractFormulaCommissionDto contractFormulaCommissionDto) {
        if (GeneralTool.isEmpty(contractFormulaCommissionDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (GeneralTool.isEmpty(contractFormulaCommissionDto.getCommissionRate()) && GeneralTool.isEmpty(contractFormulaCommissionDto.getFixedAmount())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("commission_or_amount_fill_least_one"));
        }
        if (GeneralTool.isEmpty(contractFormulaCommissionDto.getCommissionRateAg()) && GeneralTool.isEmpty(contractFormulaCommissionDto.getFixedAmountAg())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("commission_or_refund_fill_least_one"));
        }
        Integer maxStep = contractFormulaCommissionMapper.getMaxStep(contractFormulaCommissionDto.getFkContractFormulaId());
        ContractFormulaCommission contractFormulaCommission = BeanCopyUtils.objClone(contractFormulaCommissionDto, ContractFormulaCommission::new);
        contractFormulaCommission.setStep(maxStep);
        utilService.updateUserInfoToEntity(contractFormulaCommission);
        contractFormulaCommissionMapper.insertSelective(contractFormulaCommission);
        return contractFormulaCommission.getId();
    }

    @Override
    public void deleteByFkid(Long contractFormulaId) {
        LambdaQueryWrapper<ContractFormulaCommission> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContractFormulaCommission::getFkContractFormulaId, contractFormulaId);
        contractFormulaCommissionMapper.delete(wrapper);
    }

    @Override
    public List<ContractFormulaCommissionVo> getContractFormulaCommissionDtoByFkid(Long id) {
        LambdaQueryWrapper<ContractFormulaCommission> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContractFormulaCommission::getFkContractFormulaId, id);
        wrapper.orderByAsc(ContractFormulaCommission::getStep);
        List<ContractFormulaCommission> contractFormulaCommissions = contractFormulaCommissionMapper.selectList(wrapper);
        return contractFormulaCommissions.stream().map(contractFormulaCommission -> BeanCopyUtils.objClone(contractFormulaCommission, ContractFormulaCommissionVo::new)).collect(Collectors.toList());
    }
}
