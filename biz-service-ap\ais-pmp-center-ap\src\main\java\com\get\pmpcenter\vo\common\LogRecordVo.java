package com.get.pmpcenter.vo.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/2/28  16:46
 * @Version 1.0
 * 操作记录
 */
@Data
public class LogRecordVo {

    @ApiModelProperty(value = "操作记录Id")
    private Long id;

    @ApiModelProperty(value = "操作类型-以PLAN结尾表示操作的是方案,否则操作的是明细")
    private String operationName;

    @ApiModelProperty("创建人")
    private String gmtCreateUser;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "操作描述")
    private String operationDescription;

    @ApiModelProperty(value = "表名:m_institution_provider_commission_plan-学校提供商方案;m_agent_commission_plan-代理方案")
    private String fkTableName;

    @ApiModelProperty(value = "表Id")
    private Long fkTableId;

    @ApiModelProperty(value = "方案名称")
    private String planName;


}
