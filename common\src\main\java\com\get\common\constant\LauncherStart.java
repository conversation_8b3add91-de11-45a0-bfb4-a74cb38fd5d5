package com.get.common.constant;

import com.get.core.start.constant.AppConstant;

/**
 * 通用启动常量配置
 */
public interface LauncherStart {

    /**
     * nacos dev 地址
     */
    String NACOS_DEV_ADDR = "192.168.2.31:8850";

   // String NACOS_DEV_ADDR = "192.168.2.25:8850";

    /**
     * nacos gray 地址
     */
    String NACOS_GRAY_ADDR = "192.168.1.6:8848";

    /**
     * nacos prod 地址
     */
    String NACOS_PROD_ADDR = "118.25.119.45:8848";

    /**
     * nacos iae 地址
     */
    String NACOS_IAE_ADDR = "**********:18848";

    /**
     * nacos TW 地址 作为HTI测试配置
     */
    String NACOS_TW_ADDR = "192.168.1.4:18848";

    /**
     * nacos test 地址
     */
    String NACOS_TEST_ADDR = "192.168.2.31:8850";

    /**
     * nacos local 地址
     */
    String NACOS_LOCAL_ADDR = "192.168.2.31:8850";

    /**
     * sentinel dev 地址
     */
    String SENTINEL_DEV_ADDR = "127.0.0.1:8182";

    /**
     * sentinel prod 地址
     */
    String SENTINEL_PROD_ADDR = "**********:8182";
    /**
     * sentinel iae 地址
     */
    String SENTINEL_IAE_ADDR = "**********:8182";
    /**
     * sentinel TW 地址
     */
    String SENTINEL_TW_ADDR = "**********:8182";

    /**
     * sentinel test 地址
     */
    String SENTINEL_TEST_ADDR = "127.0.0.1:8182";

    /**
     * seata dev 地址
     */
    String SEATA_DEV_ADDR = "127.0.0.1:8091";

    /**
     * seata GRAY 地址
     */
    String SEATA_GRAY_ADDR = "**********:8091";

    /**
     * seata prod 地址
     */
    String SEATA_PROD_ADDR = "**********:8091";

    /**
     * seata prod 地址
     */
    String SEATA_IAE_ADDR = "**********:8091";

    /**
     * seata TW 地址
     */
    String SEATA_TW_ADDR = "**********:8091";

    /**
     * seata test 地址
     */
    String SEATA_TEST_ADDR = "127.0.0.1:8091";

    /**
     * zipkin dev 地址
     */
    String ZIPKIN_DEV_ADDR = "http://127.0.0.1:9411";

    /**
     * zipkin GRAY 地址
     */
    String ZIPKIN_GRAY_ADDR = "http://***********:9411";

    /**
     * zipkin prod 地址
     */
    String ZIPKIN_PROD_ADDR = "http://***********:9411";

    /**
     * zipkin test 地址
     */
    String ZIPKIN_TEST_ADDR = "http://***********:9411";

    /**
     * elk dev 地址
     */
    String ELK_DEV_ADDR = "127.0.0.1:9000";

    /**
     * elk GRAY 地址
     */
    String ELK_GRAY_ADDR = "**********:9000";

    /**
     * elk prod 地址
     */
    String ELK_PROD_ADDR = "**********:9000";

    /**
     * elk iae 地址
     */
    String ELK_IAE_ADDR = "**********:9000";

    /**
     * elk TW 地址
     */
    String ELK_TW_ADDR = "**********:9000";

    /**
     * elk test 地址
     */
    String ELK_TEST_ADDR = "127.0.0.1:9000";

    /**
     * seata file模式
     */
    String FILE_MODE = "db";

    /**
     * seata nacos模式
     */
    String NACOS_MODE = "nacos";

    /**
     * seata default模式
     */
    String DEFAULT_MODE = "default";

    /**
     * seata group后缀
     */
    String GROUP_NAME = "-group";

    /**
     * seata 服务组格式
     *
     * @param appName 服务名
     * @return group
     */
    static String seataServiceGroup(String appName) {
        return appName.concat(GROUP_NAME);
    }

    /**
     * 动态获取nacos地址
     *
     * @param profile 环境变量
     * @return addr
     */
    static String nacosAddr(String profile) {
        switch (profile) {
            case (AppConstant.PROD_CODE):
                return NACOS_PROD_ADDR;
            case (AppConstant.IAE_CODE):
                return NACOS_IAE_ADDR;
            case (AppConstant.TW_CODE):
                return NACOS_TW_ADDR;
            case (AppConstant.GRAY_CODE):
                return NACOS_GRAY_ADDR;
            case (AppConstant.TEST_CODE):
                return NACOS_TEST_ADDR;
            case (AppConstant.LOCAL_CODE):
                return NACOS_LOCAL_ADDR;
            default:
                return NACOS_DEV_ADDR;
        }
    }

    /**
     * 动态获取sentinel地址
     *
     * @param profile 环境变量
     * @return addr
     */
    static String sentinelAddr(String profile) {
        switch (profile) {
            case (AppConstant.PROD_CODE):
                return SENTINEL_PROD_ADDR;
            case (AppConstant.IAE_CODE):
                return SENTINEL_IAE_ADDR;
            case (AppConstant.TW_CODE):
                return SENTINEL_TW_ADDR;
            case (AppConstant.TEST_CODE):
                return SENTINEL_TEST_ADDR;
            default:
                return SENTINEL_DEV_ADDR;
        }
    }

    /**
     * 动态获取seata地址
     *
     * @param profile 环境变量
     * @return addr
     */
    static String seataAddr(String profile) {
        switch (profile) {
            case (AppConstant.PROD_CODE):
                return SEATA_PROD_ADDR;
            case (AppConstant.IAE_CODE):
                return SEATA_IAE_ADDR;
            case (AppConstant.TW_CODE):
                return SEATA_TW_ADDR;
            case (AppConstant.GRAY_CODE):
                return SEATA_GRAY_ADDR;
            case (AppConstant.TEST_CODE):
                return SEATA_TEST_ADDR;
            default:
                return SEATA_DEV_ADDR;
        }
    }

    /**
     * 动态获取zipkin地址
     *
     * @param profile 环境变量
     * @return addr
     */
    static String zipkinAddr(String profile) {
        switch (profile) {
            case (AppConstant.PROD_CODE):
                return ZIPKIN_PROD_ADDR;
            case (AppConstant.TEST_CODE):
                return ZIPKIN_TEST_ADDR;
            default:
                return ZIPKIN_DEV_ADDR;
        }
    }

    /**
     * 动态获取elk地址
     *
     * @param profile 环境变量
     * @return addr
     */
    static String elkAddr(String profile) {
        switch (profile) {
            case (AppConstant.PROD_CODE):
                return ELK_PROD_ADDR;
            case (AppConstant.IAE_CODE):
                return ELK_IAE_ADDR;
            case (AppConstant.TW_CODE):
                return ELK_TW_ADDR;
            case (AppConstant.GRAY_CODE):
                return ELK_GRAY_ADDR;
            case (AppConstant.TEST_CODE):
                return ELK_TEST_ADDR;
            default:
                return ELK_DEV_ADDR;
        }
    }

}
