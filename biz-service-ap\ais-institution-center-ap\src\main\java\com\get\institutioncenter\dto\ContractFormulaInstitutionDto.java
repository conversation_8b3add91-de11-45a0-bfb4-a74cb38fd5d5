package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: Sea
 * @create: 2021/4/22 11:09
 * @verison: 1.0
 * @description:
 */
@Data
public class ContractFormulaInstitutionDto extends BaseVoEntity {

    /**
     * 合同公式Id
     */
    @ApiModelProperty(value = "合同公式Id")
    private Long fkContractFormulaId;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

}
