package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/11/19
 * @TIME: 15:54
 * @Description:
 **/
@Data
public class ReceivablePlanDto extends BaseVoEntity  {

    @ApiModelProperty(value = "是否同步学费到申请计划")
    private Boolean flag=false;

    /**
     * 应收类型关键字，枚举，如：m_student_offer_item
     */
    @NotNull(message = "应收类型不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "应收类型关键字，枚举，如：student_offer_item", required = true)
    private String fkTypeKey;

    /**
     * 应收类型对应记录Id，如：m_student_offer_item.id
     */
    @NotNull(message = "应收类型对应记录Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "应收类型对应记录Id，如：m_student_offer_item.id", required = true)
    private Long fkTypeTargetId;

    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    /**
     * 学费金额
     */
    @ApiModelProperty(value = "学费金额")
    private BigDecimal tuitionAmount;

    /**
     * 费率%
     */
    @ApiModelProperty(value = "费率%")
    private BigDecimal commissionRate;

    /**
     * 渠道费率%
     */
    @ApiModelProperty(value = "渠道费率%")
    private BigDecimal netRate;

    /**
     * 佣金金额
     */
    @ApiModelProperty(value = "佣金金额")
    private BigDecimal commissionAmount;

    /**
     * 定额金额
     */
    @ApiModelProperty(value = "定额金额")
    private BigDecimal fixedAmount;

    /**
     * 其他金额类型：0=奖励金额/1=Incentive奖励/2=保险金额/3=活动费用/4=其他收入
     */
    @ApiModelProperty(value = "其他金额类型：0=奖励金额/1=Incentive奖励/2=保险金额/3=活动费用/4=其他收入")
    private Integer bonusType;

    /**
     * 其他金额
     */
    @ApiModelProperty(value = "其他金额")
    private BigDecimal bonusAmount;

    /**
     * 应收金额
     */
    @ApiModelProperty(value = "应收金额")
    private BigDecimal receivableAmount;

    /**
     * 计划收款时间
     */
    @ApiModelProperty(value = "计划收款时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date receivablePlanDate;

    /**
     * 应收计划额外原因Id
     */
    @ApiModelProperty(value = "应收计划额外原因Id")
    private Integer fkReceivableReasonId;

    /**
     * 状态：0关闭/1打开/2完成
     */
    @ApiModelProperty(value = "状态：0关闭/1打开/2完成")
    private Integer status;

    /**
     * 学生id
     */
    @ApiModelProperty(value = "学生id")
    private Long fkStudentId;

    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

    /**
     * 公司ids
     */
    @ApiModelProperty(value = "公司ids")
    private List<Long> fkCompanyIds;

    /**
     * num
     */
    @ApiModelProperty(value = "num")
    private String offerItemNum;

    /**
     * 学生名称
     */
    @ApiModelProperty(value = "学生名称")
    private String studentName;

    /**
     * 提供商名称
     */
    @ApiModelProperty(value = "提供商名称")
    private String institutionProviderName;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String institutionName;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称")
    private String courseName;


    @ApiModelProperty("创建时间(开始)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty("创建时间(结束)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    /**
     * 收齐状态：0未收/1部分已收/2已收齐
     */
    @ApiModelProperty(value = "收齐状态：0未收/1部分已收/2已收齐")
    private Integer receiveStatus;

    /**
     * 实收金额
     */
    @ApiModelProperty(value = "实收金额")
    private BigDecimal actualReceivableAmount;

    /**
     * 差额
     */
    @ApiModelProperty(value = "差额")
    private BigDecimal diffReceivableAmount;

    /**
     * 查询用ids
     */
    @ApiModelProperty(value = "查询用ids")
    private List<Long> ids;


    /**
     * 学校
     */
    @ApiModelProperty(value = "学校")
    private Long fkinstitutionId;

    @ApiModelProperty("开学时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date openingTime;

    /**
     * 入学日期开始
     */
    @ApiModelProperty(value = "入学日期开始")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date enrollmentDateStart;

    @ApiModelProperty("申请计划延时入学时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deferOpeningTime;
    /**
     * 入学日期结束
     */
    @ApiModelProperty(value = "入学日期结束")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date enrollmentDateEnd;

    @ApiModelProperty(value = "是否同步到应付计划")
    private Boolean syn = false;


    //费率%
    @ApiModelProperty(value = "奖金比例")
    private BigDecimal bonusCommissionRate;

    //定额金额
    @ApiModelProperty(value = "奖金固定金额")
    private BigDecimal bonusFixedAmount;


}
