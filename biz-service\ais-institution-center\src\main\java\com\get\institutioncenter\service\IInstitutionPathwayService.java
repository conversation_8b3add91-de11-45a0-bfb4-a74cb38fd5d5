package com.get.institutioncenter.service;


import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.vo.InstitutionPathwayVo;
import com.get.institutioncenter.entity.InstitutionPathway;
import com.get.institutioncenter.dto.InstitutionPathwayDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/12/17
 * @TIME: 17:52
 * @Description:
 **/
public interface IInstitutionPathwayService extends BaseService<InstitutionPathway> {
    /**
     * @return java.util.List<CommentVo>
     * @Description: 获取所有评论
     * @Param [commentVo, page]
     * <AUTHOR>
     **/
    List<InstitutionPathwayVo> datas(InstitutionPathwayDto institutionPathwayDto, Page page);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * @return void
     * @Description: 绑定
     * @Param [institutionPathwayVos]
     * <AUTHOR>
     **/
    void update(InstitutionPathwayDto institutionPathwayDto);

    /**
     * 桥梁绑定非桥梁学校
     *
     * @Date 16:44 2021/7/22
     * <AUTHOR>
     */
    void pathwayUpdate(InstitutionPathwayDto institutionPathwayDto);

    /**
     * 课程桥梁学校下拉框数据
     *
     * @return
     */
    List<BaseSelectEntity> getBridgeInstitutionSelect(Long id);
}
