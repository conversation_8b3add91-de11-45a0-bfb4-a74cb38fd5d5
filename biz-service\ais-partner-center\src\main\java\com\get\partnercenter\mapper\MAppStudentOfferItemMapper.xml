<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.partnercenter.mapper.MAppStudentOfferItemMapper">

    <resultMap id="BaseResultMap" type="com.get.partnercenter.entity.MAppStudentOfferItemEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkAppStudentId" column="fk_app_student_id" jdbcType="BIGINT"/>
            <result property="fkAreaCountryId" column="fk_area_country_id" jdbcType="BIGINT"/>
            <result property="fkInstitutionId" column="fk_institution_id" jdbcType="BIGINT"/>
            <result property="fkInstitutionCourseId" column="fk_institution_course_id" jdbcType="BIGINT"/>
            <result property="openingTime" column="opening_time" jdbcType="DATE"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="selectItemList" resultType="com.get.partnercenter.vo.MAppStudentOfferItemVo">
        SELECT mAppStudentOfferItem.*,
               mInstitution.name AS institutionName,
               mInstitution.name_chn AS institutionNameChn,
               uAreaCountry.name AS fkAreaCountryName,
               uAreaCountry.name_chn AS fkAreaCountryNameChn,
               institutionCourse.name AS fkInstitutionCourseName,
               institutionCourse.name_chn AS fkInstitutionCourseNameChn
        FROM ais_sale_center.m_app_student_offer_item mAppStudentOfferItem
            LEFT JOIN  ais_institution_center.m_institution mInstitution ON mInstitution.id=mAppStudentOfferItem.fk_institution_id
            LEFT  JOIN  ais_institution_center.u_area_country uAreaCountry ON mAppStudentOfferItem.fk_area_country_id = uAreaCountry.id
            LEFT JOIN ais_institution_center.m_institution_course institutionCourse ON institutionCourse.id=mAppStudentOfferItem.fk_institution_course_id

            WHERE mAppStudentOfferItem.fk_app_student_id=#{id}
        <if test="type!=null and type==1">
            AND mAppStudentOfferItem.is_additional=1    AND   mAppStudentOfferItem.status_additional IN (1)
        </if>

    </select>


</mapper>
