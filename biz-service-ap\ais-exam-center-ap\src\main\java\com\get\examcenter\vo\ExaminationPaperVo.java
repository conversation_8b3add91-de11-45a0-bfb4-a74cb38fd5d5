package com.get.examcenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by <PERSON>.
 * Time: 9:45
 * Date: 2021/8/23
 * Description:考场管理返回类
 */
@Data
@ApiModel("考场管理返回类")
public class ExaminationPaperVo extends BaseEntity implements Serializable {


    /**
     * 考试名称
     */
    @ApiModelProperty(value = "考试名称")
    private String fkExaminationName;

    /**
     * 是否激活（中文值）
     */
    @ApiModelProperty(value = "是否激活（中文值）")
    private String isActiveName;

    /**
     * 是否允许多次答题（中文值）
     */
    @ApiModelProperty(value = "是否允许多次答题（中文值）")
    private String isRetestName;

    /**
     * 考试次数
     */
    @ApiModelProperty(value = "考试次数")
    private Integer scoreCount;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    //==========实体类==============
    private static final long serialVersionUID = 1L;
    /**
     * 考试Id
     */
    @ApiModelProperty(value = "考试Id")
    private Long fkExaminationId;
    /**
     * 考卷编号
     */
    @ApiModelProperty(value = "考卷编号")
    private String num;
    /**
     * 考卷名称
     */
    @ApiModelProperty(value = "考卷名称")
    private String name;
    /**
     * 开放时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "开放时间")
    private Date startTime;
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    /**
     * 题目数量
     */
    @ApiModelProperty(value = "题目数量")
    private Integer questionCount;
    /**
     * 管理员认证，如：手机号。多个用逗号分隔
     */
    @ApiModelProperty(value = "管理员认证，如：手机号。多个用逗号分隔")
    private String adminAuthentication;
    /**
     * 是否允许重考：0否/1是
     */
    @ApiModelProperty(value = "是否允许重考：0否/1是")
    private Boolean isRetest;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;
    /**
     * 参数配置
     */
    @ApiModelProperty(value = "参数配置")
    private String paramJson;

    /**
     * 所属公司Id
     */
    @ApiModelProperty(value = "所属公司Id")
    private Long fkCompanyId;



}
