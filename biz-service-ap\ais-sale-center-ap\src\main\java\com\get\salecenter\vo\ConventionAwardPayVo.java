package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.ConventionAwardPay;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2021/9/9
 * @TIME: 12:09
 * @Description:
 **/
@Data
@ApiModel(value = "支付流水单返回类")
public class ConventionAwardPayVo extends BaseEntity {
    /**
     * 参展人员类型（枚举定义）：0校方/1代理/2嘉宾/3员工/4工作人员
     */
    @ApiModelProperty(value = "参展人员类型（枚举定义）：0校方/1代理/2嘉宾/3员工/4工作人员")
    private Integer type;


    /**
     * 参加人姓名
     */
    @ApiModelProperty(value = "参加人姓名")
    private String name;

    /**
     * 购买者（角色）
     */
    @ApiModelProperty(value = "购买者（角色）")
    private String purchaser;
    /**
     * 奖券集合
     */
    @ApiModelProperty(value = "奖券集合")
    private String awardCodes;

    //==========实体类ConventionAwardPay============
    private static final long serialVersionUID = 1L;
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    @Column(name = "fk_convention_id")
    private Long fkConventionId;
    /**
     * 峰会参展人员Id
     */
    @ApiModelProperty(value = "峰会参展人员Id")
    @Column(name = "fk_convention_person_id")
    private Long fkConventionPersonId;
    /**
     * 支付数量
     */
    @ApiModelProperty(value = "支付数量")
    @Column(name = "pay_count")
    private Integer payCount;
    /**
     * 支付金额
     */
    @ApiModelProperty(value = "支付金额")
    @Column(name = "pay_amount")
    private BigDecimal payAmount;
    /**
     * 系统支付单号，guid
     */
    @ApiModelProperty(value = "系统支付单号，guid")
    @Column(name = "pay_system_order_num")
    private String paySystemOrderNum;
    /**
     * 支付状态：0未支付/1完成支付
     */
    @ApiModelProperty(value = "支付状态：0未支付/1完成支付")
    @Column(name = "pay_type")
    private Integer payType;
}
