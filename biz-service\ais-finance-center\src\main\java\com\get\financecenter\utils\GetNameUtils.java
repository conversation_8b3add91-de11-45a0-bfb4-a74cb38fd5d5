package com.get.financecenter.utils;

import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Component
public class GetNameUtils {
    @Resource
    private IPermissionCenterClient permissionCenterClient;


    public Map<Long, String> getCompanyNameMap(Set<Long> companyIds) {
        Map<Long, String> companyNamesByIds = new HashMap<>();
        Result<Map<Long, String>> result1 = permissionCenterClient.getCompanyNamesByIds(companyIds);
        if (result1.isSuccess() && GeneralTool.isNotEmpty(result1.getData())) {
            companyNamesByIds = result1.getData();
        }
        return companyNamesByIds;
    }

    public String getCompanyNameById(Long companyId) {
        String companyName = "";
        Set<Long> companyIds = new HashSet<>();
        companyIds.add(companyId);
        Result<Map<Long, String>> result1 = permissionCenterClient.getCompanyNamesByIds(companyIds);
        if (result1.isSuccess() && GeneralTool.isNotEmpty(result1.getData())) {
            companyName = result1.getData().get(companyId);
        }
        return companyName;
    }




}
