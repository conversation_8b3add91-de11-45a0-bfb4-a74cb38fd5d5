package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.CyclingRegistration;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/10/14 11:22
 */
@Data
@ApiModel(value = "骑行活动返回类")
public class CyclingRegistrationVo extends BaseEntity {

    /**
     * 参展人员类型（枚举定义）：0校方/1代理/2嘉宾/3员工/4工作人员
     */
    @ApiModelProperty(value = "参展人员类型（枚举定义）：0校方/1代理/2嘉宾/3员工/4工作人员", required = true)
    private Integer type;

    /**
     * BD名称
     */
    @ApiModelProperty(value = "BD名称")
    private String bdName;

    /**
     * 峰会手机号
     */
    @ApiModelProperty(value = "峰会手机号")
    private String tel;

    //===========实体类CyclingRegistration==============
    private static final long serialVersionUID = 1L;
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    @Column(name = "fk_convention_id")
    private Long fkConventionId;

    /**
     * 峰会参展人员Id
     */
    @ApiModelProperty(value = "峰会参展人员Id")
    @Column(name = "fk_convention_person_id")
    private Long fkConventionPersonId;

    /**
     * 骑行路线
     */
    @ApiModelProperty(value = "骑行路线")
    @Column(name = "cycling_route")
    private String cyclingRoute;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @Column(name = "name")
    private String name;
    /**
     * 英文名
     */
    @ApiModelProperty(value = "英文名")
    @Column(name = "name_en")
    private String nameEn;
    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男")
    @Column(name = "gender")
    private Integer gender;

    /**
     * 生日
     */
    @ApiModelProperty(value = "生日")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "birthday")
    private Date birthday;

    /**
     * 移动电话
     */
    @ApiModelProperty(value = "骑行移动电话")
    @Column(name = "mobile")
    private String mobile;
    /**
     * 身份类型：0身份证/1护照
     */
    @ApiModelProperty(value = "身份类型：0身份证/1护照/2港澳回乡证/3台胞证")
    @Column(name = "id_type")
    private Integer idType;
    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
    @Column(name = "id_num")
    private String idNum;
    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    @Column(name = "company")
    private String company;

    /**
     * 第一套骑行服尺码
     */
    @ApiModelProperty(value = "第一套骑行服尺码")
    private String suitSize1;

    /**
     * 第二套骑行服尺码
     */
    @ApiModelProperty(value = "第二套骑行服尺码")
    private String suitSize2;

    /**
     * 支付金额
     */
    @ApiModelProperty(value = "支付金额")
    @Column(name = "pay_amount")
    private BigDecimal payAmount;
    /**
     * 支付流水号
     */
    @ApiModelProperty(value = "支付流水号")
    @Column(name = "pay_order_num")
    private String payOrderNum;
    /**
     * 支付状态：0失败/1成功
     */
    @ApiModelProperty(value = "支付状态：0失败/1成功")
    @Column(name = "pay_status")
    private Integer payStatus;

    /**
     * 备注
     */
    @UpdateWithNull
    @ApiModelProperty(value = "备注")
    private String remark;
}
