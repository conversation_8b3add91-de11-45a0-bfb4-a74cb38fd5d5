package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.service.GetService;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.vo.OfferItemFileConfigVo;
import com.get.salecenter.vo.OfferItemLimitConfigVo;
import com.get.salecenter.vo.StudentOfferItemStepVo;
import com.get.salecenter.entity.StudentOfferItemStep;
import com.get.salecenter.dto.StudentOfferItemStepDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/11/3 11:33
 * @verison: 1.0
 * @description:
 */
public interface IStudentOfferItemStepService extends GetService<StudentOfferItemStep> {
    /**
     * @return com.get.salecenter.vo.StudentOfferItemStepVo
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    StudentOfferItemStepVo findStudentOfferItemStepById(Long id);


    /**
     * 获取步骤文件类型配置
     * @param fkStudentOfferItemStepId
     * @return
     */
    OfferItemFileConfigVo getFileConfig(Long fkStudentOfferItemStepId);
    /**
     * @return java.lang.Long
     * @Description :批量新增
     * @Param [studentOfferItemStepDtos]
     * <AUTHOR>
     */
    void batchAdd(List<StudentOfferItemStepDto> studentOfferItemStepDtos);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.salecenter.vo.StudentOfferItemStepVo
     * @Description :修改
     * @Param [studentOfferItemStepDto]
     * <AUTHOR>
     */
    StudentOfferItemStepVo updateStudentOfferItemStep(StudentOfferItemStepDto studentOfferItemStepDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.StudentOfferItemStepVo>
     * @Description :列表
     * @Param [studentOfferItemStepDto, page]
     * <AUTHOR>
     */
    List<StudentOfferItemStepVo> getStudentOfferItemSteps(StudentOfferItemStepDto studentOfferItemStepDto, Page page);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description:步骤前置下拉
     * @Param [itemStepId]
     * <AUTHOR>
     */
    List<BaseSelectEntity> getItemStepPrecondition(Long itemStepId);

    /**
     * @return void
     * @Description :上移下移
     * @Param [studentOfferItemStepDtos]
     * <AUTHOR>
     */
    void movingOrder(List<StudentOfferItemStepDto> studentOfferItemStepDtos);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 下拉
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getItemStepSelect();

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description:学生申请方案项目未执行步骤下拉
     * @Param [fkStudentOfferItemId]
     * <AUTHOR>
     */
    List<BaseSelectEntity> getUnexecutedItemStep(Long fkStudentOfferItemId, Long fkStudentOfferId);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description:学生申请方案项目未执行步骤下拉 （无权限）
     * @Param [fkStudentOfferItemId]
     * <AUTHOR>
     */
    List<BaseSelectEntity> getUnexecutedItemStepWhitoutPermission(Long fkStudentOfferItemId);

    /**
     * 判断学习计划是否存在提供商或者渠道
     * @param fkStudentOfferItemId
     * @return
     */
    Boolean hasChannelOrProvider(Long fkStudentOfferItemId);
//    /**
//     * @Description:学生申请方案项目未执行步骤下拉
//     * @Param [fkStudentOfferItemId]
//     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
//     * <AUTHOR>
//     */
//    List<BaseSelectEntity> getUnexecutedItemStep(Long fkStudentOfferItemId);

//
//    String getStepNameByItemId(Long itemId);


    /**
     * 根据学习计划申请状态查询学生id
     *
     * @param state 1,2,3,4
     * @return
     */
    List<Long> getStudentIdByState(String state);

    /**
     * enrolled步骤入学条件限制(凭证上传是否必传)
     * @return
     */
    ResponseBo<Map> stepEnrolledLimit();

    Set<String> getItemStepSelectByStepKey(List<String> stepString);

    List<BaseSelectEntity> getSuccessfulCustomerStepSelect(Long fkCompanyId);

    List<BaseSelectEntity> getFailureCustomerStepSelect(Long fkCompanyId);

    List<StudentOfferItemStep> getItemStepPostpositionByStepId(Long itemStepId);

    List<StudentOfferItemStep> getItemStepPostpositionCOE();

    OfferItemLimitConfigVo getItemStepLimtit(Long fkStudentOfferItemStepId, Long fkStudentOfferItemId);


}
