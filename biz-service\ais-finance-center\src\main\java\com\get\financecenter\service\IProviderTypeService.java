package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.financecenter.vo.ProviderTypeVo;
import com.get.financecenter.entity.ProviderType;
import com.get.financecenter.dto.ProviderTypeDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/12/18 10:25
 * @verison: 1.0
 * @description:
 */
public interface IProviderTypeService extends BaseService<ProviderType> {
    /**
     * @return com.get.financecenter.vo.ProviderTypeDto
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    ProviderTypeVo findProviderTypeById(Long id);

    /**
     * @return void
     * @Description :批量新增
     * @Param [providerTypeVos]
     * <AUTHOR>
     */
    void batchAdd(List<ProviderTypeDto> providerTypeDtos);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.financecenter.vo.ProviderTypeDto
     * @Description :修改
     * @Param [providerTypeVo]
     * <AUTHOR>
     */
    ProviderTypeVo updateProviderType(ProviderTypeDto providerTypeDto);

    /**
     * @return java.util.List<com.get.financecenter.vo.ProviderTypeDto>
     * @Description :列表
     * @Param [providerTypeVo, page]
     * <AUTHOR>
     */
    List<ProviderTypeVo> getProviderTypes(ProviderTypeDto providerTypeDto, Page page);

    /**
     * @return void
     * @Description :上移下移
     * @Param [providerTypeVos]
     * <AUTHOR>
     */
    void movingOrder(List<ProviderTypeDto> providerTypeDtos);

    /**
     * @return java.util.List<com.get.financecenter.vo.ProviderTypeDto>
     * @Description :供应商类型下拉框数据
     * @Param []
     * <AUTHOR>
     */
    List<ProviderTypeVo> getProviderTypeList();

    /**
     * @return java.lang.String
     * @Description :根据id查找对应供应商类型名称
     * @Param [providerTypeId]
     * <AUTHOR>
     */
    String getProviderTypeNameById(Long providerTypeId);


    /**
     * 根据ids查找对应供应商类型名称
     *
     * @param ids
     * @return
     */
    Map<Long, String> getProviderTypeNameByIds(Set<Long> ids);
}
