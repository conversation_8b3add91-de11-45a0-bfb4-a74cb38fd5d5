package com.get.insurancecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 * 结算账单签名
 */
@Data
@TableName("r_settlement_bill_signature")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SettlementBillSignature extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "结算账单Id")
    private Long fkSettlementBillId;

    @ApiModelProperty(value = "签名文件")
    private String signature;
}
