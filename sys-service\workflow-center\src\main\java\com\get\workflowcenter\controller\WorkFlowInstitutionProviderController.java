package com.get.workflowcenter.controller;

import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.tool.utils.GeneralTool;
import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.vo.ContractVo;
import com.get.workflowcenter.service.IWorkFlowInstitutionProviderService;
import com.get.workflowcenter.service.IWorkFlowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jdk.nashorn.internal.ir.annotations.Ignore;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.HistoryService;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.impl.identity.Authentication;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/4/20 12:22
 */
@RestController
@RequestMapping("workflow/institutionProviderFlow")
@Api(tags = "学校提供商合同流程")
@Slf4j
public class WorkFlowInstitutionProviderController {
    //    @Autowired
//    private FeginInstitutionService feginInstitutionService;
    @Autowired
    private RepositoryService repositoryService;
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private IWorkFlowInstitutionProviderService iInstitutionProviderService;
    @Autowired
    private IWorkFlowService iWorkFlowService;

    /**
     * @ Description :学校提供商流程开始
     * @ Param [businessKey, procdefKey, companyId]
     * @ return java.lang.Boolean
     * @ author LEO
     */
    @ApiIgnore
    @ApiOperation("学校提供商流程开始")
    @PostMapping("/startInstitutionContractFlow")
    public Boolean startInstitutionContractFlow(@RequestParam("businessKey") String businessKey,
                                                @RequestParam("procdefKey") String procdefKey,
                                                @RequestParam("companyId") String companyId) {
        return (this.iInstitutionProviderService.startInstitutionContractFlow(businessKey, procdefKey, companyId));
//        if (GeneralTool.isEmpty(businessKey) || GeneralTool.isEmpty(procdefKey) || GeneralTool.isEmpty(companyId)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
//        }
////        StaffVo staff = StaffContext.getStaff();
//        String username = String.valueOf(GetAuthInfo.getStaffId());
//        Authentication.setAuthenticatedUserId(username);
//
////        ContractVo institutionProviderById = feginInstitutionService.getInstitutionContractById(Long.valueOf(businessKey));
////        if (null == institutionProviderById || institutionProviderById.getStatus() != 0 || institutionProviderById.getStatus() == 5) {
////            return false;
////        }
//
////        ContractVo institutionProvider = Tools.objClone(institutionProviderById, ContractVo.class);
////        Map<String, Object> map = new HashMap<>();
////        map.put("userid", username);
////        map.put("companyId", companyId);
////        map.put("tableName", "m_institution_provider");
////        map.put("businessKey", institutionProvider.getId());
////        map.put("InstitutionProvider", institutionProvider);
////        map.put("contractApprovalMode", institutionProvider.getContractApprovalMode());
//
//
//        try {
//
//            //表名 +公司id
//            List<ProcessDefinition> processDefinition =
//                    repositoryService.createProcessDefinitionQuery().processDefinitionKey(procdefKey).processDefinitionTenantId(companyId).orderByProcessDefinitionVersion().desc().list();
//            String id = "";
//            for (ProcessDefinition pdid : processDefinition) {
//                id = pdid.getId();
//                break;
//            }
//
////            ProcessInstance processInstance = runtimeService.startProcessInstanceById(id, String.valueOf(institutionProvider.getId()), map);
////            ContractVo ifstatus = feginInstitutionService.getInstitutionContractById(Long.valueOf(businessKey));
////
////            if (ifstatus.getStatus() == 0) {
////                institutionProvider.setStatus(2);
////                feginInstitutionService.updateChangeStatus(institutionProvider);
////            }
//            return true;
//        } catch (Exception e) {
//            e.printStackTrace();
//            return false;
//        }
    }

    /**
     * @ Description :审批
     * @ Param [status, taskId, msg]
     * @ return void
     * @ author LEO
     */
    @ApiOperation("审批")
    @GetMapping("/getExamineAndApprove")
    public ResponseBo getExamineAndApprove(@RequestParam("status") String status, @RequestParam("taskId") String taskId,
                                           @RequestParam("procInstId") String procInstId, @RequestParam("msg") String msg) {

        if (GeneralTool.isEmpty(status) || GeneralTool.isEmpty(taskId) || GeneralTool.isEmpty(msg) || GeneralTool.isEmpty(procInstId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
//        StaffVo staff = StaffContext.getStaff();
        String username = String.valueOf(GetAuthInfo.getStaffId());
        Authentication.setAuthenticatedUserId(username);
        taskService.addComment(taskId, procInstId, msg);
        Map<String, Object> map = new HashMap<>();
        map.put("sequenceFlowsStatus", status);
        if ("0".equals(status)) {
            taskService.setVariableLocal(taskId, "approvalAction", 0);
        } else {
            taskService.setVariableLocal(taskId, "approvalAction", 1);
        }
        taskService.complete(taskId, map);
        iWorkFlowService.getStatusToDoSingle(status, procInstId);


        return ResponseBo.ok();
    }

    /**
     * @ Description :查询表单所有任务task版本
     * @ Param [businessKey]
     * @ return com.get.workflowcenter.vo.ActRuTaskVo
     * @ author LEO
     */
    @Ignore
    @ApiOperation("查询表单所有任务task版本")
    @GetMapping("getContractTaskDataByBusinessKey")
    @VerifyPermission(IsVerify = false)
    public ActRuTaskVo getContractTaskDataByBusinessKey(@RequestParam("businessKey") String businessKey, @RequestParam("procdefKey") String key) {
        return iInstitutionProviderService.getTaskDataByBusinessKey(businessKey, key);
    }

    /**
     * @ Description :显示合同流程流转信息
     * @ Param [businessKey]
     * @ return com.get.common.result.ResponseBo<com.get.workflowcenter.vo.PaymentApplicationFormVo>
     * @ author LEO
     */
    @ApiOperation("显示合同流程流转信息")
    @GetMapping("/getInstitutionProviderData")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<ContractVo> getInstitutionProviderData(@RequestParam("businessKey") String businessKey,
                                                             @RequestParam("procdefKey") String key) throws GetServiceException {
        List<ContractVo> mpayDtos = iInstitutionProviderService.getInstitutionProviderData(businessKey, key);
        ListResponseBo<ContractVo> mpayDtoListResponseBo = new ListResponseBo<>(mpayDtos);
        return mpayDtoListResponseBo;
    }

    /**
     * @ Description :getSignOrGet
     * @ Param [taskId, version]
     * @ return int
     * @ author LEO
     */
    @ApiIgnore
    @ApiOperation("判断去待签还是代表页面，0待签，1待办")
    @GetMapping("getSignOrGet")
    public int getSignOrGet(@RequestParam(required = false, value = "taskId") String taskId,
                            @RequestParam(required = false, value = "version") Integer version) {
        int signOrGet = iInstitutionProviderService.getSignOrGet(taskId, version);
        return signOrGet;
    }

    /**
     * @ Description : 学校供应商审批
     * @ Param [taskId, status]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiIgnore
    @GetMapping("getContractUserSubmit")
    public ResponseBo getContractUserSubmit(@RequestParam("taskId") String taskId, @RequestParam("status") String status) {
//        HashMap<String, Object> sequenceFlowsStatus = new HashMap<>();
//        //0放弃
//        if ("0".equals(status)) {
//            sequenceFlowsStatus.put("sequenceFlowsStatus", 0);
//            taskService.complete(taskId, sequenceFlowsStatus);
//            return ResponseBo.ok();
//        }
//        taskService.setVariable(taskId, "statusToDo", 1);
//        String id = taskService.getVariable(taskId, "businessKey").toString();
//        ContractVo institutionContractById = feginInstitutionService.getInstitutionContractById(Long.valueOf(id));
//
//        //不能修改 签订状态的话就解开注释，注释掉上面
//       /* Object contractApprovalMode = taskService.getVariable(taskId, "contractApprovalMode");
//        String contractApprovalModeStatus = String.valueOf(contractApprovalMode);*/
//        if (institutionContractById.getContractApprovalMode() == 1) {
//            sequenceFlowsStatus.put("sequenceFlowsStatus", 1);
//            taskService.complete(taskId, sequenceFlowsStatus);
//            return ResponseBo.ok();
//        } else if (institutionContractById.getContractApprovalMode() == 0) {
//            sequenceFlowsStatus.put("sequenceFlowsStatus", 2);
//            taskService.complete(taskId, sequenceFlowsStatus);
//            return ResponseBo.ok();
//        }
        boolean result = iInstitutionProviderService.getContractUserSubmit(taskId, status);
        if (result) {
            return ResponseBo.ok();
        }
        return ResponseBo.error();
    }


}
