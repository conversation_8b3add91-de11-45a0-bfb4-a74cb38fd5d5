package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.StudentOfferItemStep;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/11/3 11:35
 * @verison: 1.0
 * @description:
 */
@Data
public class StudentOfferItemStepVo extends BaseEntity {
    /**
     * 前置条件名称
     */
    @ApiModelProperty(value = "前置条件名称")
    private String preconditionName;

    /**
     * 申请步骤排序，由0开始按顺序排列
     */
    @ApiModelProperty(value = "申请步骤排序，由0开始按顺序排列")
    private Integer stepOrder;

    /**
     * 申请步骤名
     */
    @ApiModelProperty(value = "申请步骤名")
    private String stepName;

    @ApiModelProperty(value = "角色")
    private List<StudentProjectRoleVo> StudentProjectRoleDtos;

    /**
     * 数量
     */
    @ApiModelProperty(value = "学习计划数量")
    private Long itemNum;

    @ApiModelProperty(value = "是否加申，0否/1是")
    private Boolean isAddApp;

    //============实体类StudentOfferItemStep===============
    private static final long serialVersionUID = 1L;
    /**
     * 前置条件id，需要完成步骤条件（可多选），逗号分隔：1,2,3
     */
    @Column(name = "fk_student_offer_item_step_id_precondition")
    @ApiModelProperty(value = "前置条件id，需要完成步骤条件（可多选），逗号分隔：1,2,3")
    private String fkStudentOfferItemStepIdPrecondition;

    /**
     * 申请步骤key
     */
    @ApiModelProperty(value = "申请步骤key")
    @Column(name = "step_key")
    private String stepKey;

    /**
     * 角色Key，多个用逗号分隔
     */
    @ApiModelProperty(value = "角色Key，多个用逗号分隔")
    @Column(name = "role_key")
    private String roleKey;

    @ApiModelProperty(value = "文件类型配置信息")
    @Column(name = "config_json")
    private String configJson;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
}
