package com.get.institutioncenter.service;

import com.get.institutioncenter.entity.RInstitutionChannelCompany;
import com.get.salecenter.vo.CompanyTreeVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IInstitutionChannelCompanyService {


    void saveBatch(List<RInstitutionChannelCompany> list);
    /**
     * 获取渠道和公司的关系
     * @param channelId
     * @return
     */
    List<CompanyTreeVo> getChannelCompanyRelation(Long channelId);
}
