package com.get.salecenter.utils;

import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.vo.SelItem;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ConvertUtils<T> {

    public static Map<Long, Object> convert(List<SelItem> data) {
        if (GeneralTool.isEmpty(data)){
            return Collections.emptyMap();
        }
        Map<Long, Object> convertMap = new HashMap<>(data.size());
        for (SelItem selItem : data) {
            convertMap.put(selItem.getKeyId(), selItem.getVal());
        }
        return convertMap;
    }
}
