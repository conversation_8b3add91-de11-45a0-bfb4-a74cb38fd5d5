package com.get.officecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2022/1/17
 * @TIME: 12:24
 * @Description:考勤统计Dto
 **/
@Data
public class AttendanceStatisticsExcelVo extends BaseVoEntity {
    /**
     * 员工姓名
     */
    @ApiModelProperty(value = "姓名（英）")
    private String staffName;
    /**
     * 员工姓名
     */
    @ApiModelProperty(value = "姓名（中）")
    private String staffNameChn;
    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String departmentName;
    /**
     * 考勤号
     */
    @ApiModelProperty(value = "考勤号")
    private String attendanceNum;
    /**
     * 出勤天数
     */
    @ApiModelProperty(value = "出勤天数")
    private BigDecimal attendanceDays;
    /**
     * 免责迟到（次）
     */
    @ApiModelProperty(value = "免责迟到（次）")
    private Integer exemptionBeingLate;
    /**
     * 有责迟到（次）
     */
    @ApiModelProperty(value = "有责迟到（次）")
    private Integer responsibleBeingLate;
    /**
     * 事假（时）
     */
    @ApiModelProperty(value = "事假（时）")
    private BigDecimal leaveVacation;
    /**
     * 补休（时）
     */
    @ApiModelProperty(value = "补休（时）")
    private BigDecimal takeDeferredHolidays;
    /**
     * 除当前月外加班累计结余时间加上补休时间
     */
    @ApiModelProperty(value = "加班累计时间")
    private BigDecimal overtime;
    /**
     * 本月加班（时）
     */
    @ApiModelProperty(value = "本月加班（时）")
    private BigDecimal overtimeMonth;

    /**
     * 加班累计剩余时间（加班累计结余时间-补休-事假）
     */
    @ApiModelProperty(value = "加班累计剩余时间")
    private BigDecimal remainingOvertime;

    /**
     * 加班累计结余时间（当前全部工休库存）
     */
    @ApiModelProperty(value = "加班累计结余时间")
    private BigDecimal balanceOvertime;

    /**
     * 入职时间
     */
    @ApiModelProperty(value = "入职时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String entryDate;
    /**
     * 上一年累计年假（时）
     */
    @ApiModelProperty(value = "上一年累计年假（时）")
    private BigDecimal annualLeaveLastYear;
    /**
     * 上一年累计剩余年假（时）
     */
    @ApiModelProperty(value = "上一年累计剩余年假（时）")
    private BigDecimal remainingAnnualLeaveLastYear;
    /**
     * 本月休上一年年假（时）
     */
    @ApiModelProperty(value = "本月休上一年年假（时）")
    private BigDecimal annualLeaveLastYearMonth;
    /**
     * 今年累计年假（时）
     */
    @ApiModelProperty(value = "今年累计年假（时）")
    private BigDecimal annualLeaveThisYear;
    /**
     * 今年累计剩余年假（时）
     */
    @ApiModelProperty(value = "今年累计剩余年假（时）")
    private BigDecimal remainingAnnualLeaveThisYear;
    /**
     * 本月休今年年假（时）
     */
    @ApiModelProperty(value = "本月休今年年假（时）")
    private BigDecimal annualLeaveThisYearMonth;
    /**
     * 本季度病假剩余天数（天）
     */
    @ApiModelProperty(value = "本季度病假剩余天数（天）")
    private BigDecimal diseaseVacationQuarter;
    /**
     * 是否全勤
     */
    @ApiModelProperty(value = "是否全勤")
    private String isFullAttendance;
    /**
     * 考勤情况
     */
    @ApiModelProperty(value = "考勤情况")
    private String attendanceDetails;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


}
