package com.get.financecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.financecenter.entity.BankAccount;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Sea
 * @create: 2020/12/21 9:57
 * @verison: 1.0
 * @description:
 */
@Data
public class BankAccountVo extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String currencyTypeName;

    //===========实体类BankAccount===============


    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    /**
     * 银行账户名称
     */
    @ApiModelProperty(value = "银行账户名称")
    private String bankAccount;

    /**
     * 银行账号
     */
    @ApiModelProperty(value = "银行账号")
    private String bankAccountNum;

    /**
     * 银行名称
     */
    @ApiModelProperty(value = "银行名称")
    private String bankName;

    /**
     * 银行支行名称
     */
    @ApiModelProperty(value = "银行支行名称")
    private String bankBranchName;

    /**
     * 银行地址国家Id
     */
    @ApiModelProperty(value = "银行地址国家Id")
    private Long fkAreaCountryId;

    /**
     * 银行地址州省Id
     */
    @ApiModelProperty(value = "银行地址州省Id")
    private Long fkAreaStateId;

    /**
     * 银行地址城市Id
     */
    @ApiModelProperty(value = "银行地址城市Id")
    private Long fkAreaCityId;

    /**
     * 银行地址城市区域Id
     */
    @ApiModelProperty(value = "银行地址城市区域Id")
    private Long fkAreaCityDivisionId;

    /**
     * 银行地址
     */
    @ApiModelProperty(value = "银行地址")
    private String bankAddress;

    /**
     * Swift Code
     */
    @ApiModelProperty(value = "Swift Code")
    private String swiftCode;

    /**
     * 其他转账编码
     */
    @ApiModelProperty(value = "其他转账编码")
    private String otherCode;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

}
