package com.get.remindercenter.utils;

import cn.hutool.json.JSONUtil;
import com.aliyun.dm20151123.models.*;
import com.aliyun.tea.TeaException;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.AESConstant;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.AESUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dto.NewsSendStatisticsDto;
import com.get.remindercenter.dao.BatchSendingEmailMapper;
import com.get.remindercenter.dao.InvalidEmailMapper;
import com.get.remindercenter.dao.LogSendEmailMapper;
import com.get.remindercenter.dao.UnsubscribeEmailMapper;
import com.get.remindercenter.dto.MailDto;
import com.get.remindercenter.entity.BatchSendingEmail;
import com.get.remindercenter.entity.InvalidEmail;
import com.get.remindercenter.entity.LogSendEmail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　　　◆　　　　　　　　　　　　◆　　　　　　　　　　　　　　◆　　　　　　　　　　　　　◆◆　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　◆◆◆　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆
 * 　　　◆◆　　　　　◆◆　　　　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆◆◆◆
 * 　　　◆◆◆　　　◆◆◆　　　　　　　◆◆　　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆◆
 * 　　　　◆◆◆　◆◆◆　　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆
 * 　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　　◆◆
 * 　　　　　　◆◆◆　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆
 * 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆◆
 * <p>
 * Time: 14:45
 * Date: 2021/11/25
 * Description:发送消息工具类
 */
@Slf4j
@Component
@RefreshScope
public class SendMessageUtils {

    @Resource
    private JavaMailSender javaMailSender;

    @Resource
    private LogSendEmailMapper logSendEmailMapper;

    @Resource
    private BatchSendingEmailMapper batchSendingEmailMapper;
    @Resource
    private UnsubscribeEmailMapper unsubscribeEmailMapper;
    @Resource
    private InvalidEmailMapper invalidEmailMapper;

    @Resource
    private UtilService utilService;

    @Value("${spring.mail.username}")
    private String from;

    //是否禁止发送邮件 true 发送  false禁止发送
    @Value("${emailSwitch}")
    private boolean emailSwitch;

    @Value("${accessKeyId}")
    public String accessKeyId;
    @Value("${accessKeySecret}")
    public String accessKeySecret;

    private static com.aliyun.teaopenapi.models.Config config = null;


    /**
     * 异步发送代理邮箱
     *
     * @param mailDto
     * @param toEmails
     * @param key
     */
    public void sendMailCustom(MailDto mailDto, List<String> toEmails, String key) {
        if (!emailSwitch) {
            return;
        }

        for (String toEmail : toEmails) {
            try {
                Thread.sleep(2000); // 等待2000ms后再次尝试
            } catch (InterruptedException e) {
            }
            JavaMailSenderImpl javaMailSenderImpl = new JavaMailSenderImpl();
            javaMailSenderImpl.setDefaultEncoding(mailDto.getDefaultEncoding());
            javaMailSenderImpl.setHost(mailDto.getHost());
            javaMailSenderImpl.setPort(mailDto.getPort());
            javaMailSenderImpl.setProtocol(mailDto.getProtocol());
            javaMailSenderImpl.setUsername(mailDto.getUserName());
            javaMailSenderImpl.setPassword(mailDto.getPassword());
            MimeMessage mail = javaMailSenderImpl.createMimeMessage();
            try {
                MimeMessageHelper helper = new MimeMessageHelper(mail);
                helper.setTo(toEmail);
                helper.setSubject(mailDto.getTitle());
                helper.setFrom(mailDto.getUserName());
                helper.setText(mailDto.getTemplate(), true);
                javaMailSenderImpl.send(mail);
                log.info("新闻邮件发送代理成功=======》邮件：{}", toEmail);
                addLongSendEmail(mailDto.getUserName(), mailDto.getTitle(), key, toEmail, null, true);
            } catch (Exception e) {
                //使用系统邮箱发送
                sendMailAgent(mailDto.getTitle(), mailDto.getTemplate(), toEmail, key);
            }
        }
    }

    private void addLongSendEmail(String userName, String title, String key, String toEmail, String message, Boolean flag) {
        LogSendEmail logSendEmail = new LogSendEmail();
        logSendEmail.setOptType(key);
        logSendEmail.setFromEmail(userName);
        logSendEmail.setToEmail(toEmail);
        logSendEmail.setSubject(title);
        logSendEmail.setMessage(message);
        if (flag) {
            logSendEmail.setStatus(1);
        } else {
            logSendEmail.setStatus(0);
        }
        utilService.setCreateInfo(logSendEmail);
        logSendEmailMapper.insert(logSendEmail);
    }

    /**
     * 发送代理，记录日志
     *
     * @param subject
     * @param content
     * @param receiver
     * @param key
     */
    public void sendMailAgent(String subject, String content, String receiver, String key) {
        if (!emailSwitch) {
            return;
        }
        MimeMessage mail = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(mail);
        try {
            helper.setTo(receiver);
            helper.setSubject(subject);
            helper.setFrom(from);
            helper.setText(content, true);
            javaMailSender.send(mail);
            addLongSendEmail("<EMAIL>", subject, key, receiver, null, true);
            log.info("新闻邮件发送代理成功=======》邮件：{}", receiver);
        } catch (Exception e) {
            log.error("邮箱:{},新闻邮件发送出现异常：{}", receiver, e.getMessage());
            addLongSendEmail("<EMAIL>", subject, key, receiver, e.getMessage(), false);
        }

    }


    public void sendMail(String subject, String content, String toEmail) {
        if (!emailSwitch) {
            return;
        }
        MimeMessage mail = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(mail);
        try {
            helper.setTo(toEmail);
            helper.setSubject(subject);
            helper.setFrom(from);
            helper.setText(content, true);
            javaMailSender.send(mail);
            log.info("邮件发送成功===》邮件：{},内容：{}", toEmail, content);
        } catch (MessagingException e) {
            e.printStackTrace();
            log.error("邮件发送出现异常：" + e.getMessage());
            throw new RuntimeException(e.getMessage());
        }

    }

    /**
     * 发送邮件并且抄送
     *
     * @return
     * @Date 10:31 2022/8/22
     * <AUTHOR>
     */
    public boolean sendMail(String title, String content, String toEmail, String[] ccEmail) {
        String message = content; // 默认使用原始内容
        try {
            // 尝试进行Base64解码
            message = new String(Base64.getDecoder().decode(content), StandardCharsets.UTF_8);
        } catch (IllegalArgumentException e) {
            // 如果不是有效的Base64编码内容，则保留原始内容
            log.info("原始内容====" + message);
        }
        if (!emailSwitch) {
            return true;
        }
        MimeMessage mail = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(mail);
        try {
            helper.setTo(toEmail);
            if (GeneralTool.isNotEmpty(ccEmail)) {
                helper.setCc(Arrays.stream(ccEmail)
                        .filter(GeneralTool::isNotEmpty)
                        .toArray(String[]::new));
            }
            helper.setSubject(title);
            helper.setFrom(from);
            helper.setText(message, true);
            //helper.setText(message, "UTF-8", true);
            javaMailSender.send(mail);
            log.info("邮件发送成功===》{}", message);
            return true;
        } catch (MessagingException e) {
            e.printStackTrace();
            log.error("邮件发送出现异常：" + e.getMessage());
//            throw new RuntimeException(e.getMessage());
            return true;
        }

    }

    /**
     * @Description: 发送测试邮件
     */
    public void sendMailTest() {
        if (!emailSwitch) {
            return;
        }
        MimeMessage mailMessage = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(mailMessage);
        String context = "<b>尊敬的用户：</b><br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您好，管理员:" + System.currentTimeMillis() + "，已为你申请了新的账号，" +
                "请您尽快通过<a href=\"http://www.163.com/\">链接</a>登录系统。"
                + "<br>修改密码并完善你的个人信息。<br><br><br><b>员工管理系统<br>Bowen</b>";
        try {
            helper.setFrom(from);
            helper.setTo("<EMAIL>");
            helper.setSubject("主题:请重置后台的账号");
            helper.setSentDate(new Date());//发送时间
            helper.setText(context, true);
            //第一个参数要发送的内容，第二个参数是不是Html格式。
            javaMailSender.send(mailMessage);
            log.info("邮件发送成功===》{}", context);
        } catch (MessagingException e) {
            log.error("邮件发送失败===》{}", e.getMessage());
            e.printStackTrace();
        }

    }

    public Boolean sendMessageCustom(String email, String subjectText, String context) {
        if (!emailSwitch) {
            return false;
        }
        MimeMessage mailMessage = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(mailMessage);
//        String context="<b>尊敬的用户：</b><br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您好，管理员:"+System.currentTimeMillis()+"，已为你申请了新的账号，"+
//                "请您尽快通过<a href=\"http://www.163.com/\">链接</a>登录系统。"
//                +"<br>修改密码并完善你的个人信息。<br><br><br><b>员工管理系统<br>Bowen</b>";
        try {
            helper.setFrom(from);
            helper.setTo(email);
//            helper.setSubject("主题:请重置后台的账号");
            helper.setSubject(subjectText);
            helper.setSentDate(new Date());//发送时间
            helper.setText(context, true);
            //第一个参数要发送的内容，第二个参数是不是Html格式。
            javaMailSender.send(mailMessage);
            log.info("邮件地址{},发送成功===》{}", email, context);
            return true;
        } catch (Exception e) {
            log.error("邮件地址{},发送失败===》{}", email, e.getMessage());
            e.printStackTrace();
            return false;
        }

    }

    /**
     * 自定义发送邮件
     *
     * @Date 11:16 2022/8/25
     * <AUTHOR>
     */
    public boolean customSendMail(String defaultEncoding, String host, int port, String protocol, String userName, String password, String title, String toEmail, String ccEmail, String template, boolean flag) {
        try {
            if (!emailSwitch) {
                return true;
            }
            JavaMailSenderImpl javaMailSenderImpl = new JavaMailSenderImpl();
            javaMailSenderImpl.setDefaultEncoding(defaultEncoding);
            javaMailSenderImpl.setHost(host);
            javaMailSenderImpl.setPort(port);
            javaMailSenderImpl.setProtocol(protocol);
            javaMailSenderImpl.setUsername(userName);
            javaMailSenderImpl.setPassword(password);
            MimeMessage mail = javaMailSenderImpl.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mail);
            helper.setTo(toEmail);
            if (GeneralTool.isNotEmpty(ccEmail)) {
                helper.setCc(ccEmail);
            }
            helper.setSubject(title);
            helper.setFrom(userName);
            helper.setText(template, flag);
            javaMailSenderImpl.send(mail);
            return true;
        } catch (MessagingException e) {
            e.printStackTrace();
            log.error("邮件发送出现异常：" + e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 自定义发送邮件
     *
     * @Date 11:16 2022/8/25
     * <AUTHOR>
     */
    public boolean customSendMail(String defaultEncoding, String host, int port, String protocol, String userName, String password, String title, String[] toEmail, String[] ccEmail, String template, boolean flag) {
        try {
            if (!emailSwitch) {
                return true;
            }
            JavaMailSenderImpl javaMailSenderImpl = new JavaMailSenderImpl();
            javaMailSenderImpl.setDefaultEncoding(defaultEncoding);
            javaMailSenderImpl.setHost(host);
            javaMailSenderImpl.setPort(port);
            javaMailSenderImpl.setProtocol(protocol);
            javaMailSenderImpl.setUsername(userName);
            javaMailSenderImpl.setPassword(password);
            MimeMessage mail = javaMailSenderImpl.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mail);
            helper.setTo(toEmail);
            helper.setCc(ccEmail);
            helper.setSubject(title);
            helper.setFrom(userName);
            helper.setText(template, flag);
            javaMailSenderImpl.send(mail);
            return true;
        } catch (MessagingException e) {
            e.printStackTrace();
            log.error("邮件发送出现异常：" + e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }


    /**
     * 阿里云邮件推送
     *
     * @param toAddress
     * @param subject
     * @param htmlBody
     * @param newsId
     * @return
     */
    public boolean aliyunSendMail(List<String> toAddress, String subject, String htmlBody, Long newsId) {
        if (!emailSwitch) {
            return true;
        }
        //无效地址
        List<InvalidEmail> invalidEmails = invalidEmailMapper.selectList(Wrappers.lambdaQuery());
        if (GeneralTool.isNotEmpty(invalidEmails)) {
            toAddress.removeAll(invalidEmails.stream().map(InvalidEmail::getEmail).collect(Collectors.toList()));
        }
        //去重
        toAddress = toAddress.stream().distinct().collect(Collectors.toList());
        //无效地址

        for (String email : toAddress) {
            try {
                //退订
                String emailContent = htmlBody;
                if (htmlBody.contains("#{email}")) {
                    emailContent = emailContent.replace("#{email}", URLEncoder.encode(AESUtils.Encrypt(email, AESConstant.AESKEY)));
                }
                com.aliyun.dm20151123.Client client = createClient();
                com.aliyun.dm20151123.models.SingleSendMailRequest singleSendMailRequest = new com.aliyun.dm20151123.models.SingleSendMailRequest()
                        .setAccountName("<EMAIL>")
                        .setAddressType(0)
                        .setReplyToAddress(true)
                        .setToAddress(email)
                        .setSubject(subject)
                        .setHtmlBody(emailContent);
                com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();

                SingleSendMailResponse singleSendMailResponse = client.singleSendMailWithOptions(singleSendMailRequest, runtime);
                if (singleSendMailResponse.statusCode != 200) {
                    log.error("阿里云邮件推送异常:" + JSONUtil.toJsonStr(singleSendMailResponse));
                } else if (GeneralTool.isNotEmpty(newsId)) {
                    //新闻发送成功，记录
                    BatchSendingEmail batchSendingEmail = new BatchSendingEmail();
                    batchSendingEmail.setEmail(email);
                    batchSendingEmail.setFkTableName(TableEnum.NEWS.key);
                    batchSendingEmail.setFkTableId(newsId);
                    utilService.setCreateInfo(batchSendingEmail);
                    batchSendingEmailMapper.insert(batchSendingEmail);
                }
            } catch (TeaException error) {
                error.printStackTrace();
                // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
                // 错误 message
                System.out.println(error.getMessage());
                com.aliyun.teautil.Common.assertAsString(error.message);
                log.error("阿里云邮件推送异常:" + error.getMessage(), error);
//            throw new GetServiceException(error.getMessage());
            } catch (Exception _error) {
                _error.printStackTrace();
                TeaException error = new TeaException(_error.getMessage(), _error);
                // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
                // 错误 message
                System.out.println(error.getMessage());
                com.aliyun.teautil.Common.assertAsString(error.message);
                log.error("阿里云邮件推送异常:" + error.getMessage(), error);
//            throw new GetServiceException(error.getMessage());
            }
        }
        return true;
    }

    /**
     * 阿里云邮件推送新闻邮件
     *
     * @param
     * @param subject
     * @param htmlBody
     * @param newsId
     * @return
     */
    public boolean sendNewsEmail(String targetAddress, String subject, String htmlBody, Long newsId, String senderEmail) {
        if (!emailSwitch) {
            return true;
        }

        try {
            //退订
            String emailContent = htmlBody;
            if (htmlBody.contains("#{email}")) {
                emailContent = emailContent.replace("#{email}", URLEncoder.encode(AESUtils.Encrypt(targetAddress, AESConstant.AESKEY)));
            }
            com.aliyun.dm20151123.Client client = createClient();
            com.aliyun.dm20151123.models.SingleSendMailRequest singleSendMailRequest = new com.aliyun.dm20151123.models.SingleSendMailRequest()
                    .setAccountName(senderEmail)
                    .setAddressType(0)
                    .setReplyToAddress(true)
                    .setToAddress(targetAddress)
                    .setSubject(subject)
                    .setHtmlBody(emailContent);
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();

            SingleSendMailResponse singleSendMailResponse = client.singleSendMailWithOptions(singleSendMailRequest, runtime);
            if (singleSendMailResponse.statusCode != 200) {
                log.error("阿里云邮件推送异常:" + JSONUtil.toJsonStr(singleSendMailResponse));
            } else if (GeneralTool.isNotEmpty(newsId)) {
                //新闻发送成功，记录
                BatchSendingEmail batchSendingEmail = new BatchSendingEmail();
                batchSendingEmail.setEmail(targetAddress);
                batchSendingEmail.setFkTableName(TableEnum.NEWS.key);
                batchSendingEmail.setFkTableId(newsId);
                utilService.setCreateInfo(batchSendingEmail);
                batchSendingEmailMapper.insert(batchSendingEmail);
            }
        } catch (TeaException error) {
            error.printStackTrace();
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            com.aliyun.teautil.Common.assertAsString(error.message);
            log.error("阿里云邮件推送异常:" + error.getMessage(), error);
            return false;
        } catch (Exception _error) {
            _error.printStackTrace();
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            com.aliyun.teautil.Common.assertAsString(error.message);
            log.error("阿里云邮件推送异常:" + error.getMessage(), error);
            //            throw new GetServiceException(error.getMessage());
            return false;
        }

        return true;
    }

    /**
     * <b>description</b> :
     * <p>使用AK&amp;SK初始化账号Client</p>
     *
     * @return Client
     * @throws Exception
     */
    public com.aliyun.dm20151123.Client createClient() throws Exception {
        // 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考。
        // 建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378657.html。
        if (config == null) {
            config = new com.aliyun.teaopenapi.models.Config()
                    // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
                    .setAccessKeyId(accessKeyId)
                    // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
                    .setAccessKeySecret(accessKeySecret);
            config.endpoint = "dm.aliyuncs.com";
        }
        return new com.aliyun.dm20151123.Client(config);
    }


    /**
     * 阿里云添加邮件标签(参考示例https://next.api.aliyun.com/api/Dm/2015-11-23/CreateTag?RegionId=cn-hangzhou&tab=DEMO&lang=JAVA)
     *
     * @param tagName
     * @return
     */
    public Long aliyunAddTag(String tagName) {
        try {
            com.aliyun.dm20151123.Client client = createClient();
            com.aliyun.dm20151123.models.CreateTagRequest createTagRequest = new com.aliyun.dm20151123.models.CreateTagRequest();
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            createTagRequest.setTagName(tagName);
            CreateTagResponse tagWithOptions = client.createTagWithOptions(createTagRequest, runtime);
            if (tagWithOptions.statusCode == 200) {
                return Long.parseLong(tagWithOptions.body.tagId);
            }
        } catch (TeaException error) {
            error.printStackTrace();
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            com.aliyun.teautil.Common.assertAsString(error.message);
            log.error("阿里云邮件创建Tag标签异常:" + error.getMessage(), error);
            //            throw new GetServiceException(error.getMessage());
        } catch (Exception _error) {
            _error.printStackTrace();
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            com.aliyun.teautil.Common.assertAsString(error.message);
            log.error("阿里云邮件创建Tag标签异常:" + error.getMessage(), error);
            //            throw new GetServiceException(error.getMessage());
        }
        return null;
    }

    /**
     * 阿里云添加邮件标签(参考示例spm=api-workbench.API%20Document.0.0.7fcc693aPrYdAR&RegionId=cn-hangzhou&tab=DEMO&lang=JAVA)
     *
     * @param tagId
     * @return
     */
    public Boolean aliyunDelTag(Long tagId) {
        try {
            com.aliyun.dm20151123.Client client = createClient();
            com.aliyun.dm20151123.models.DeleteTagRequest deleteTagRequest = new com.aliyun.dm20151123.models.DeleteTagRequest();
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            deleteTagRequest.setTagId(Integer.parseInt(tagId.toString()));
            DeleteTagResponse deleteTagResponse = client.deleteTagWithOptions(deleteTagRequest, runtime);
            if (deleteTagResponse.statusCode == 200) {
                return true;
            }
        } catch (TeaException error) {
            error.printStackTrace();
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            com.aliyun.teautil.Common.assertAsString(error.message);
            log.error("阿里云邮件创建Tag标签异常:" + error.getMessage(), error);
            //            throw new GetServiceException(error.getMessage());
        } catch (Exception _error) {
            _error.printStackTrace();
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            com.aliyun.teautil.Common.assertAsString(error.message);
            log.error("阿里云邮件创建Tag标签异常:" + error.getMessage(), error);
            //            throw new GetServiceException(error.getMessage());
        }
        return false;
    }


    /**
     * 根据标签名查询，查询标签下的邮件发送情况
     *
     * @param tagName
     * @return
     */
    public NewsSendStatisticsDto aliyunQueryEmailStatisticByTagName(String tagName) {
        if (GeneralTool.isEmpty(tagName))
            return null;
        try {
            com.aliyun.dm20151123.Client client = createClient();
            com.aliyun.dm20151123.models.SenderStatisticsByTagNameAndBatchIDRequest tagNameRequest = new com.aliyun.dm20151123.models.SenderStatisticsByTagNameAndBatchIDRequest();
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            tagNameRequest.setTagName(tagName);
            SenderStatisticsByTagNameAndBatchIDResponse response = client.senderStatisticsByTagNameAndBatchIDWithOptions(tagNameRequest, runtime);
            if (response.statusCode == 200) {
                List<SenderStatisticsByTagNameAndBatchIDResponseBody.SenderStatisticsByTagNameAndBatchIDResponseBodyDataStat> stat = response.getBody().getData().getStat();
                if (GeneralTool.isEmpty(stat))
                    throw new GetServiceException(LocaleMessageUtils.getMessage("EMAIL_STATISTICS_IS_NULL"));
                // 获取返回的信息封装到对象中
                SenderStatisticsByTagNameAndBatchIDResponseBody.SenderStatisticsByTagNameAndBatchIDResponseBodyDataStat statistics = stat.get(0);
                return new NewsSendStatisticsDto(null, Integer.parseInt(statistics.getRequestCount())
                        , Integer.parseInt(statistics.getSuccessCount()), Integer.parseInt(statistics.getFaildCount())
                        , Integer.parseInt(statistics.getUnavailableCount()));
            }
        } catch (TeaException error) {
            error.printStackTrace();
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            com.aliyun.teautil.Common.assertAsString(error.message);
            log.error("阿里云邮件创建Tag标签异常:" + error.getMessage(), error);
            //            throw new GetServiceException(error.getMessage());
        } catch (Exception _error) {
            _error.printStackTrace();
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            com.aliyun.teautil.Common.assertAsString(error.message);
            log.error("阿里云邮件创建Tag标签异常:" + error.getMessage(), error);
            //            throw new GetServiceException(error.getMessage());
        }
        return null;
    }
}
