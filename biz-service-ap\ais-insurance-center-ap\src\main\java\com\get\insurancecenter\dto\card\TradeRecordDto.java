package com.get.insurancecenter.dto.card;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/8/1
 * @Version 1.0
 * @apiNote:交易记录dto
 */
@Data
public class TradeRecordDto {

    @ApiModelProperty(value = "列表类型-1:交易记录(只包含支出和还款);2-详细列表(信用卡详情的流水记录,包含完整的4种交易类型)")
    @NotNull(message = "列表类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "信用卡ID")
    @NotNull(message = "信用卡ID不能为空")
    private Long creditCardId;

    @ApiModelProperty(value = "交易类型：0调整(校正金额)/1支出/2收取(还款)/3退款")
    private Integer businessType;

    @ApiModelProperty(value = "交易状态-0失败;1成功")
    private Integer status;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "交易开始时间")
    private Date startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "交易结束时间")
    private Date endTime;

    @ApiModelProperty(value = "学生姓名/受保人姓名")
    private String insurantName;

    @ApiModelProperty(value = "交易对象ID(保险产品类型ID)")
    private String productTypeId;


}
