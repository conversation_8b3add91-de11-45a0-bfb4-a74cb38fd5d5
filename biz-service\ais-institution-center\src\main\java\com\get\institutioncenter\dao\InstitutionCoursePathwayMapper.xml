<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionCoursePathwayMapper">
    <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.InstitutionCoursePathway">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_institution_course_id" jdbcType="BIGINT" property="fkInstitutionCourseId"/>
        <result column="fk_institution_course_id_pathway" jdbcType="BIGINT" property="fkInstitutionCourseIdPathway"/>

        <result column="req_academic_1" jdbcType="VARCHAR" property="reqAcademic1"/>
        <result column="req_eng_1" jdbcType="VARCHAR" property="reqEng1"/>
        <result column="req_particular_subject" jdbcType="VARCHAR" property="reqParticularSubject"/>
        <result column="req_academic_2" jdbcType="VARCHAR" property="reqAcademic2"/>
        <result column="req_eng_2" jdbcType="VARCHAR" property="reqEng2"/>

        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <insert id="insert" parameterType="com.get.institutioncenter.entity.InstitutionCoursePathway">
        insert into r_institution_course_pathway (id, fk_institution_course_id, fk_institution_course_id_pathway,req_academic_1,
                                                  req_eng_1,req_particular_subject,req_academic_2,req_eng_2,
                                                  gmt_create, gmt_create_user, gmt_modified,
                                                  gmt_modified_user)
        values (#{id,jdbcType=BIGINT}, #{fkInstitutionCourseId,jdbcType=BIGINT},
                #{fkInstitutionCourseIdPathway,jdbcType=BIGINT}, #{reqAcademic1,jdbcType=VARCHAR}, #{reqEng1,jdbcType=VARCHAR}, #{reqParticularSubject,jdbcType=VARCHAR}, #{reqAcademic2,jdbcType=VARCHAR}, #{reqEng2,jdbcType=VARCHAR},
                #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP},
                #{gmtModifiedUser,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionCoursePathway">
        insert into r_institution_course_pathway
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkInstitutionCourseId != null">
                fk_institution_course_id,
            </if>
            <if test="fkInstitutionCourseIdPathway != null">
                fk_institution_course_id_pathway,
            </if>
            <if test="reqAcademic1 != null">
                req_academic_1,
            </if>
            <if test="reqEng1 != null">
                req_eng_1,
            </if>
            <if test="reqParticularSubject != null">
                req_particular_subject,
            </if>
            <if test="reqAcademic2 != null">
                req_academic_2,
            </if>
            <if test="reqEng2 != null">
                req_eng_2,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkInstitutionCourseId != null">
                #{fkInstitutionCourseId,jdbcType=BIGINT},
            </if>
            <if test="fkInstitutionCourseIdPathway != null">
                #{fkInstitutionCourseIdPathway,jdbcType=BIGINT},
            </if>
            <if test="reqAcademic1 != null">
                #{reqAcademic1,jdbcType=VARCHAR},
            </if>
            <if test="reqEng1 != null">
                #{reqEng1,jdbcType=VARCHAR},
            </if>
            <if test="reqParticularSubject != null">
                #{reqParticularSubject,jdbcType=VARCHAR},
            </if>
            <if test="reqAcademic2 != null">
                #{reqAcademic2,jdbcType=VARCHAR},
            </if>
            <if test="reqEng2 != null">
                #{reqEng2,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="getBridgeInstitutionCourseIds" resultType="java.lang.Long">
        SELECT fk_institution_course_id_pathway FROM r_institution_course_pathway
        <where>
            <if test="id!=null">
                and fk_institution_course_id =#{id}
            </if>
            <if test="updateCourseId != null and updateCourseId != ''">
                and fk_institution_course_id_pathway !=#{updateCourseId}
            </if>
        </where>
    </select>
    <select id="getNonBridgeInstitutionCoursePathwayByInstitution" resultType="java.lang.Long">
        SELECT fk_institution_course_id FROM r_institution_course_pathway
        <where>
            <if test="id!=null">
                and fk_institution_course_id_pathway =#{id}
            </if>
            <if test="updateCourseId != null and updateCourseId != ''">
                and fk_institution_course_id !=#{updateCourseId}
            </if>
        </where>
    </select>
    <select id="selectInstitutionCoursePathway"
            resultType="com.get.institutioncenter.entity.InstitutionCoursePathway">
        SELECT icp.* FROM r_institution_course_pathway AS icp
        <if test="fkInstitutionCourseIdPathway != null">
            INNER JOIN m_institution_course AS ic ON ic.id = icp.fk_institution_course_id
        </if>
        <if test="fkInstitutionCourseId != null">
            INNER JOIN m_institution_course AS ic ON ic.id = icp.fk_institution_course_id_pathway
        </if>
        <where>
            <if test="fkInstitutionCourseId != null">
                AND fk_institution_course_id = #{fkInstitutionCourseId}
            </if>
            <if test="fkInstitutionCourseIdPathway != null">
                AND fk_institution_course_id_pathway = #{fkInstitutionCourseIdPathway}
            </if>
        </where>
        ORDER BY
        ic.view_order DESC,
        CONVERT ( ic.name USING gbk ) ASC
    </select>

</mapper>