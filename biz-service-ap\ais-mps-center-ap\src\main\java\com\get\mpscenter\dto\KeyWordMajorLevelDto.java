package com.get.mpscenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Set;

@Data
public class KeyWordMajorLevelDto extends BaseVoEntity {

    @ApiModelProperty("类型：INPUT")
    private String typeKey;

    @ApiModelProperty("关键字")
    @NotNull(message = "关键字", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private String keyWord;

    @ApiModelProperty("排序，倒序：数字由大到小排列")
    private Integer viewOrder;


    @ApiModelProperty("专业等级Id")
    @NotNull(message = "专业等级Id", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private Set<Long> fkMajorLevelIds;
}
