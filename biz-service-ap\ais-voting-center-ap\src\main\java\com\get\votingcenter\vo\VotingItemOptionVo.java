package com.get.votingcenter.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseEntity;
import com.get.votingcenter.entity.VotingItemOption;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/9/26 16:42
 * @verison: 1.0
 * @description:
 */
@Data
public class VotingItemOptionVo  extends BaseEntity {

    /**
     * 媒体附件-投票选项图片
     */
    @ApiModelProperty(value = "媒体附件-投票选项图片")
    List<VotingMediaAndAttachedVo> mediaAndAttachedDtoList;

    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;

    /**
     * 投票数量
     */
    @ApiModelProperty(value = "投票数量")
    private Long votingCount;

    /**
     * 投票占比
     */
    @ApiModelProperty(value = "投票占比")
    private Double votingRate;

    /**
     * 投票项Id
     */
    @ApiModelProperty(value = "投票项Id")
    @Column(name = "fk_voting_item_id")
    private Long fkVotingItemId;
    /**
     * 选项名称
     */
    @ApiModelProperty(value = "选项名称")
    @Column(name = "name")
    private String name;
    /**
     * 选项副名称
     */
    @ApiModelProperty(value = "选项副名称")
    @Column(name = "name_sub")
    private String nameSub;
    /**
     * 默认为0，为0时随机排序，排序为从大到小顺序排列
     */
    @ApiModelProperty(value = "默认为0，为0时随机排序，排序为从大到小顺序排列")
    @Column(name = "view_order")
    private Integer viewOrder;



}
