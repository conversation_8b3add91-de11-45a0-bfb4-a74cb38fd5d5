package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.financecenter.vo.ProviderContactPersonVo;
import com.get.financecenter.entity.ProviderContactPerson;
import com.get.financecenter.dto.ProviderContactPersonDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/12/28 11:45
 * @verison: 1.0
 * @description:
 */
public interface IProviderContactPersonService extends BaseService<ProviderContactPerson> {
    /**
     * @return com.get.salecenter.vo.ProviderContactPersonDto
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    ProviderContactPersonVo findProviderContactPersonById(Long id);

    /**
     * @return void
     * @Description :新增
     * @Param [providerContactPersonVo]
     * <AUTHOR>
     */
    Long addProviderContactPerson(ProviderContactPersonDto providerContactPersonDto);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.salecenter.vo.ProviderContactPersonDto
     * @Description :修改
     * @Param [providerContactPersonVo]
     * <AUTHOR>
     */
    ProviderContactPersonVo updateProviderContactPerson(ProviderContactPersonDto providerContactPersonDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.ProviderContactPersonDto>
     * @Description :列表
     * @Param [providerContactPersonVo, page]
     * <AUTHOR>
     */
    List<ProviderContactPersonVo> getProviderContactPersons(ProviderContactPersonDto providerContactPersonDto, Page page);
}
