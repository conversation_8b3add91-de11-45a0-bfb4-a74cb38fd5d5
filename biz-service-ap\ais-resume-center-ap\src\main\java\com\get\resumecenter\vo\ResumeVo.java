package com.get.resumecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import com.get.resumecenter.entity.Resume;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/31
 * @TIME: 16:18
 * @Description:
 **/
@Data
public class ResumeVo extends BaseEntity {
    /**
     * 头像链接
     */
    @ApiModelProperty(value = "头像链接")
    private MediaAndAttachedVo headImageDto;

    /**
     * 公司图标链接
     */
    @ApiModelProperty(value = "公司Logo链接")
    private MediaAndAttachedVo companyIconDto;

    /**
     * 职业意向
     */
    @ApiModelProperty(value = "职业意向")
    private ResumeIntentionVo resumeIntentionDto;


    /**
     * 工作经验
     */
    @ApiModelProperty(value = "工作经验")
    private List<ResumeWorkVo> resumeWorkDtos;

    /**
     * 工作经验
     */
    @ApiModelProperty(value = "教育经历")
    private List<ResumeEducationVo> resumeEducationDtos;

    /**
     * 技能
     */
    @ApiModelProperty(value = "技能")
    private List<ResumeSkillVo> resumeSkillDtos;

    /**
     * 证书
     */
    @ApiModelProperty(value = "证书")
    private List<ResumeCertificateVo> resumeCertificateDtos;


    /**
     * 培训经历
     */
    @ApiModelProperty(value = "培训经历")
    private List<ResumeTrainingVo> resumeTrainingDtos;


    /**
     * 附加信息
     */
    @ApiModelProperty(value = "附加信息")
    private List<ResumeOtherVo> resumeOtherDtos;

    /**
     * 简历附件
     */
    @ApiModelProperty(value = "简历附件")
    private List<ResumeAttachmentVo> resumeAttachmentDtos;

    /**
     * 简历类型
     */
    @ApiModelProperty(value = "简历类型")
    private String fkResumeTypeName;


    /**
     * 工作经历（学历）
     */
    @ApiModelProperty(value = "工作经历（公司）")
    private String resumeWorks;

    /**
     * 教育经历（学历）
     */
    @ApiModelProperty(value = "教育经历（学历）")
    private String resumeEducations;


    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;
    /**
     * 简历类型Id
     */
    @ApiModelProperty(value = "简历类型Id")
    @Column(name = "fk_resume_type_id")
    private Long fkResumeTypeId;
    /**
     * 简历guid(人才中心)
     */
    @ApiModelProperty(value = "简历guid(人才中心)")
    @Column(name = "resume_guid")
    private String resumeGuid;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @Column(name = "name")
    private String name;
    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男")
    @Column(name = "gender")
    private Integer gender;
    /**
     * 生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "生日")
    @Column(name = "birthday")
    private Date birthday;
    /**
     * 身高（cm）
     */
    @ApiModelProperty(value = "身高（cm）")
    @Column(name = "height")
    private Integer height;
    /**
     * 开始工作年份
     */
    @ApiModelProperty(value = "开始工作年份")
    @Column(name = "start_working_year")
    private Integer startWorkingYear;
    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    @Column(name = "identity_card")
    private String identityCard;
    /**
     * 国籍
     */
    @ApiModelProperty(value = "国籍")
    @Column(name = "nationality")
    private String nationality;
    /**
     * 户口所在地
     */
    @ApiModelProperty(value = "户口所在地")
    @Column(name = "residence")
    private String residence;
    /**
     * 婚姻状态：未婚/已婚/保密
     */
    @ApiModelProperty(value = "婚姻状态：未婚/已婚/保密")
    @Column(name = "marriage")
    private String marriage;
    /**
     * 政治面貌：党员/预备党员/团员/民主党派人士/无党派民主人士/普通公民
     */
    @ApiModelProperty(value = "政治面貌：党员/预备党员/团员/民主党派人士/无党派民主人士/普通公民")
    @Column(name = "political")
    private String political;
    /**
     * 住址电话
     */
    @ApiModelProperty(value = "住址电话")
    @Column(name = "home_tel")
    private String homeTel;
    /**
     * 手机区号
     */
    @ApiModelProperty(value = "手机区号")
    @Column(name = "mobile_area_code")
    private String mobileAreaCode;
    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    @Column(name = "mobile")
    private String mobile;
    /**
     * Email
     */
    @ApiModelProperty(value = "Email")
    @Column(name = "email")
    private String email;
    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    @Column(name = "zip_code")
    private String zipCode;
    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    @Column(name = "address")
    private String address;
    /**
     * QQ号
     */
    @ApiModelProperty(value = "QQ号")
    @Column(name = "qq")
    private String qq;
    /**
     * 微信号
     */
    @ApiModelProperty(value = "微信号")
    @Column(name = "wechat")
    private String wechat;
    /**
     * whatsapp号
     */
    @ApiModelProperty(value = "whatsapp号")
    @Column(name = "whatsapp")
    private String whatsapp;


}
