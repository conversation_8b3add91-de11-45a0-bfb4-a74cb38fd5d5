package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @author: Sea
 * @create: 2021/5/8 11:06
 * @verison: 1.0
 * @description:
 */
@Data
public class ConventionSponsorFeeDto extends BaseVoEntity{
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    private Long fkConventionId;

    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    private String typeName;

    /**
     * 费用名称
     */
    @ApiModelProperty(value = "费用名称")
    private String title;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    /**
     * 费用金额
     */
    @ApiModelProperty(value = "费用金额")
    private BigDecimal fee;

    /**
     * 日期描述
     */
    @ApiModelProperty(value = "日期描述")
    private String dateNote;

    /**
     * 时间描述
     */
    @ApiModelProperty(value = "时间描述")
    private String timeNote;

    /**
     * 图片路径
     */
    @ApiModelProperty(value = "图片路径")
    private String imgUrl;

    /**
     * 费用摘要
     */
    @ApiModelProperty(value = "费用摘要")
    private String summary;

    /**
     * 数量限制，不填或<=0为不限制
     */
    @ApiModelProperty(value = "数量限制，不填或<=0为不限制")
    private Integer countLimit;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    /**
     * 费用金额（折合人民币）
     */
    @ApiModelProperty(value = "费用金额（折合人民币）")
    private BigDecimal feeCny;


}
