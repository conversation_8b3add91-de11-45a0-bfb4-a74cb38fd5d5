package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/12/12
 * @TIME: 12:28
 * @Description:
 **/
@Data
public class InstitutionApplicationStatisticsExportVo {
    /**
     * 学校ID
     */
    @ApiModelProperty(value = "学校ID")
    private Long fkInstitutionId;
    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String fkInstitutionName;

    /**
     * 学校提供商名称【状态】
     */
    @ApiModelProperty(value = "学校提供商名称【状态】")
    private List<String> providerList;
//
//    /**
//     * 申请数
//     */
//    @ApiModelProperty(value = "申请数")
//    private Integer applicationCount;
//    /**
//     * 学生数
//     */
//    @ApiModelProperty(value = "学生数")
//    private Integer studentCount;

    /**
     * 提交数
     */
    @ApiModelProperty(value = "提交数")
    private Integer mainCourseCount;

    /**
     * 成功入学数（学生数）
     */
    @ApiModelProperty(value = "成功入学数（学生数）")
    private Integer successsStudentCount;

//
//    /**
//     * 转化率
//     */
//    @ApiModelProperty(value = "定校量转化率）")
//    private String conversionRate;



}
