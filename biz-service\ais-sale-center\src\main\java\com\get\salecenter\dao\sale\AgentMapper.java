package com.get.salecenter.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.financecenter.dto.query.AgentSettlementQueryDto;
import com.get.pmpcenter.dto.agent.AgentCommissionTypeAgentDto;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.query.AgentQueryDto;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/7/7 11:52
 * @verison: 1.0
 * @description: 代理安排管理mapper
 */
@Mapper
public interface AgentMapper extends BaseMapper<Agent>, GetMapper<Agent> {

    /**
     * 根据id获取代理名称
     *
     * @param id
     * @return
     */
    String getAgentNameById(Long id);


    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [agentId]
     * <AUTHOR>
     */
    Boolean isExistByAgentId(Long agentId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 查询代理下级ids
     * @Param [agentId]
     * <AUTHOR>
     */
    List<Long> getAgentFollowerIds(Long agentId);

    /**
     * @Description: 根据ids一次性查询代理下级ids
     * @Author: Jerry
     * @Date:10:10 2021/8/18
     */
    List<Long> getAgentFollowerIdsByIds(String agentIds);

    /**
     * 查询下一级非结算口代理ids
     *
     * @Date 12:26 2021/8/12
     * <AUTHOR>
     */
    List<Long> getSubordinateNotPortAgentIds(@Param("agentIds") List<Long> agentIds);


    /**
     * @return java.lang.Boolean
     * @Description: 是否激活
     * @Param [agentId]
     * <AUTHOR>
     */
    Boolean isActiveByAgentId(@Param("agentId") Long agentId);

    /**
     * feign财务佣金汇总列表
     *
     * @return
     * @Date 16:08 2021/12/24
     * <AUTHOR>
     */
    List<CommissionSummaryVo> commissionSummary(IPage<CommissionSummaryVo> iPage,
                                                @Param("commissionSummaryDto") CommissionSummaryDto commissionSummaryDto,
                                                @Param("commissionSummarySecondaryScreeningDtoList") List<CommissionSummarySecondaryScreeningDto> commissionSummarySecondaryScreeningDtoList);

    /**
     * feign 根据财务结算批次号获取代理应付计划
     *
     * @return
     * @Date 14:30 2021/12/28
     * <AUTHOR>
     */
    List<PayablePlanVo> getAgentPayablePlanByNumSettlementBatch(@Param("numSettlementBatch") String numSettlementBatch);

    /**
     * 代理佣金结算列表 未结算步骤
     *
     * @return
     * @Date 11:32 2022/1/10
     * <AUTHOR>
     */
    List<Agent> agentSettlementList(IPage<Agent> iPage,
                                    @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                    @Param("agentSettlementDto") AgentSettlementQueryDto agentSettlementDto,
                                    @Param("enrolledKey") String enrolledKey,
                                    @Param("stepEnrolledTbc") String stepEnrolledTbc,
                                    @Param("payInAdvanceFlag") boolean payInAdvanceFlag,
                                    @Param("exportFlag") boolean exportFlag);

    /**
     * 代理佣金结算列表 结算中 OR 代理确认步骤(废弃)
     *
     * @return
     * @Date 11:32 2022/1/10
     * <AUTHOR>
     */
    List<Agent> agentConfirmationList(IPage<Agent> iPage, @Param("subordinateAgentIds") List<Long> subordinateAgentIds,
                                      @Param("agentSettlementDto") AgentSettlementDto agentSettlementDto,
                                      @Param("statusSettlementList") List<Integer> statusSettlementList);

    /**
     * 获取代理符合条件的应付计划
     *
     * @Date 15:47 2022/1/10
     * <AUTHOR>
     */
    int getAgentPayablePlanCountByAgentIds(@Param("subordinateAgentId") Long subordinateAgentId,
                                           @Param("agentSettlementDto") AgentSettlementQueryDto agentSettlementDto,
                                           @Param("enrolledKey") String enrolledKey,
                                           @Param("stepEnrolledTbc") String stepEnrolledTbc,
                                           @Param("payInAdvanceFlag") boolean payInAdvanceFlag);


    /**
     * 获取代理以及其旗下代理符合条件的应付计划 结算中 OR 代理确认步骤
     *
     * @Date 15:47 2022/1/10
     * <AUTHOR>
     */
    int getAgentConfirmationPayablePlanCountByAgentIds(@Param("subordinateAgentIds") List<Long> subordinateAgentIds,
                                                       @Param("agentSettlementDto") AgentSettlementDto agentSettlementDto,
                                                       @Param("statusSettlementList") List<Integer> statusSettlementList);

    /**
     * 获取代理以及其旗下代理符合条件的应付计划 结算中 OR 代理确认步骤
     *
     * @Date 15:47 2022/1/10
     * <AUTHOR>
     */
    int getAgentConfirmationPayablePlanCountByAgentIds(@Param("subordinateAgentIds") List<Long> subordinateAgentIds, @Param("agentSettlementDto") AgentSettlementDto agentSettlementDto);


    /**
     * 先根据Vo条件筛选一遍数据，获取筛选后的代理对象
     *
     * @Date 16:13 2022/1/14
     * <AUTHOR>
     */
    List<CommissionSummarySecondaryScreeningDto> getAgentBycommissionSummaryVo(@Param("commissionSummaryDto") CommissionSummaryDto commissionSummaryDto);

    /**
     * 设置关键代理过期
     */
    Boolean agentIsKeyExpired();

    AgentVo getIsExistAgent(@Param("id") Long id, @Param("companyId") Long companyId, @Param("name") String name,
                            @Param("taxCode") String taxCode,
                            @Param("nature") String nature,
                            @Param("legalPerson") String legalPerson, @Param("idCard") String idCard);

    /**
     * @Description:获取积分
     * @Param: page
     * @return:
     * @Author: Walker
     * @Date: 2022/3/15
     */
    List<AgentSourceVo> getSource(IPage<CommissionSummaryVo> ipage, @Param("agentName") String agentName, @Param("staffFollowerIds") List<Long> staffFollowerIds, @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                  @Param("beginTime") String beginTime,
                                  @Param("endTime") String endTime,
                                  @Param("opening_start_time") String opening_start_time,
                                  @Param("opening_end_time") String opening_end_time, @Param("institutionIdsExcludingString") String institutionIdsExcludingString,
                                  @Param("cpp_id") String cpp1_id, @Param("bms_id") String bms_id, @Param("state") String state);

    List<AgentSourceVo> getSourceBackUp(IPage<CommissionSummaryVo> ipage, @Param("agentName") String agentName, @Param("splitSetString") String splitSetString, @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                        @Param("beginTime") String beginTime,
                                        @Param("endTime") String endTime,
                                        @Param("opening_start_time") String opening_start_time,
                                        @Param("opening_end_time") String opening_end_time);

    List<AgentSourceVo> getSourceListAll(@Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                         @Param("beginTime") String beginTime,
                                         @Param("endTime") String endTime,
                                         @Param("opening_start_time") String opening_start_time,
                                         @Param("opening_end_time") String opening_end_time);

    /**
     * @Description:获取积分
     * @Param: page
     * @return:
     * @Author: Walker
     * @Date: 2022/3/15
     */
    List<AgentSourceVo> getSource(@Param("agentName") String agentName, @Param("splitSetString") String splitSetString, @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                  @Param("beginTime") String beginTime,
                                  @Param("endTime") String endTime,
                                  @Param("opening_start_time") String opening_start_time,
                                  @Param("opening_end_time") String opening_end_time);

    /**
     * @Description:根据cpp_id或者bms_id获取代理的总积分
     * @Param:cpp_id，bms_id
     * @return:
     * @Author: Walker
     * @Date: 2022/3/21
     */
    @DS("saledb-doris")
    AgentSourceVo getSumSourceByCppIdOrBmsId(@Param("cpp_id") String cpp_id, @Param("bms_id") String bms_id, @Param("state") String state, @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                             @Param("beginTime") String beginTime,
                                             @Param("endTime") String endTime,
                                             @Param("opening_start_time") String opening_start_time,
                                             @Param("opening_end_time") String opening_end_time);


    /**
     * 获取联系人代理列表下拉
     *
     * @param companyId
     */
    List<Agent> getContactPersonAgentList(@Param("staffFollowerIds") List<Long> staffFollowerIds, @Param("loginCompanyId") Long loginCompanyId, @Param("companyId") Long companyId);

    /**
     * 获取代理列表
     *
     * @param iPage
     * @param agentVo
     * @param staffFollowerIds
     * @param agentAnnualSummaryDto
     * @param countryIds
     * @return
     */
    List<AgentVo> getAgents(IPage<Agent> iPage, @Param("agentDto") AgentQueryDto agentVo,
                            @Param("staffFollowerIds") List<Long> staffFollowerIds,
                            @Param("companyIds") List<Long> companyIds,
                            @Param("agentAnnualSummaryDto") AgentAnnualSummaryDto agentAnnualSummaryDto,
                            @Param("countryIds") List<Long> countryIds,
                            @Param("institutionIds") List<Long> institutionIds);

    Long verifyAgentPermissions(@Param("agentId") Long agentId, @Param("staffFollowerIds") List<Long> staffFollowerIds);

    /**
     * 获取代理列表(无权限)
     *
     * @param iPage
     * @param agentVo
     * @return
     */
    List<AgentVo> getAgentList(IPage<AgentVo> iPage, @Param("agentDto") AgentQueryDto agentVo);

    /**
     * 根据ids获取代理
     *
     * @param ids
     * @return
     */
    List<Agent> getAgentByIds(@Param("ids") Set<Long> ids);

    /**
     * Author Cream
     * Description : 根据应付计划id 获取代理id
     * Date 2022/5/7 14:25
     * Params:
     * Return
     */
    Long getAgentIdByPayablePlanId(@Param("id") Long id);

    List<AgentSubVo> getAgentListByName(@Param("companyIds") List<Long> companyIds, @Param("agentName") String agentName, @Param("staffFollowerIds") List<Long> staffFollowerIds, @Param("value") Integer value);

    Agent getIaeAgentById(Long agentId);

    List<BaseSelectEntity> getAgentByTargetName(@Param("targetName") String targetName);

    List<Agent> getAgentListNew(@Param("agentIdList") List<Long> agentIdList, @Param("staffFollowerIds") List<Long> staffFollowerIds, @Param("value") Integer value);

    /**
     * 根据bdids 获取 合同未返回数
     *
     * @Date 15:42 2023/7/3
     * <AUTHOR>
     */
    List<UnsignedVo> geUnsignedNumByBdIds(@Param("bdIds") List<Long> bdIds,
                                          @Param("agentAnnualSummaryDto") AgentAnnualSummaryDto agentAnnualSummaryDto,
                                          @Param("countryIds") List<Long> countryIds,
                                          @Param("staffFollowerIds") List<Long> staffFollowerIds);

//    List<Long> getIssueAgenInfo(@Param("id") Long id);

    List<AgentVo> getAgentListAll(@Param("companyIds") List<Long> companyIds, @Param("staffFollowerIds") List<Long> staffFollowerIds);

    List<Long> getAgentIdByEmail(@Param("email") String email);

    List<AgentSourceVo> getSource2025(IPage<CommissionSummaryDto> ipage, @Param("agentName") String agentName, @Param("staffFollowerIds") List<Long> staffFollowerIds, @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                      @Param("beginTime") String beginTime,
                                      @Param("endTime") String endTime,
                                      @Param("opening_start_time") String opening_start_time,
                                      @Param("opening_end_time") String opening_end_time, @Param("institutionIdsExcludingString") String institutionIdsExcludingString,
                                      @Param("cpp_id") String cpp1_id, @Param("bms_id") String bms_id, @Param("state") String state);

//    @DS("saledb-doris")
    List<AgentVo> getAgentCommissionTypeAndAgentIsBind(IPage<Agent> iPage, @Param("agentCommissionTypeAgentDto") AgentCommissionTypeAgentDto agentCommissionTypeAgentDto,
                                                       @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                       @Param("companyIds") List<Long> companyIds,
                                                       @Param("countryIds") List<Long> countryIds);

}