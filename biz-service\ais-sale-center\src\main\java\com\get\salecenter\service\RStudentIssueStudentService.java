package com.get.salecenter.service;

/**
 * @Author:cream
 * @Date: 2023/5/11  12:45
 */
public interface RStudentIssueStudentService {

    /**
     * 删除issue关联表数据
     * @param mergedStudentId
     */
    void deleteByStudentId(Long mergedStudentId);


    /**
     * 处理合并issue学生
     * @param mergedStudentId
     * @param targetStudentId
     * @param stuSource
     */
    void processIssueStudent(Long mergedStudentId, Long targetStudentId,String stuSource);
}
