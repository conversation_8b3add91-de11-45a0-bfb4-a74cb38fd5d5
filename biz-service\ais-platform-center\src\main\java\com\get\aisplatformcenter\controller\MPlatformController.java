package com.get.aisplatformcenter.controller;


import com.get.aisplatformcenter.service.MPlatformService;
import com.get.aisplatformcenterap.dto.MPlatformParamsDto;
import com.get.aisplatformcenterap.entity.MPlatformEntity;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "后台管理-所属产品")
@RestController
@RequestMapping("platform/mplatform")
public class MPlatformController {

    @Autowired
    MPlatformService mPlatformService;

    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.LIST, description = "华通伙伴/所属产品/列表查询")
    @PostMapping("searchList")
    public ResponseBo<MPlatformEntity> searchList(@RequestBody MPlatformParamsDto dto){
        List<MPlatformEntity> datas=mPlatformService.searchList(dto);

        return new ResponseBo(datas);
    }
    @ApiOperation(value = "删除接口", notes = "")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        mPlatformService.delete(id);
        return DeleteResponseBo.ok();
    }
    @ApiOperation(value = "新增接口", notes = "")
    @PostMapping("insert")
    public ResponseBo<MPlatformEntity> insert(MPlatformEntity entity) {
        if(entity!=null){
            mPlatformService.save(entity);
        }
        return DeleteResponseBo.ok();
    }





}
