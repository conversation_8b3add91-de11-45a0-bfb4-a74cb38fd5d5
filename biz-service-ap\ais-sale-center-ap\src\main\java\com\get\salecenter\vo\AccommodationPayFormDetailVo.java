package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: Hardy
 * @create: 2022/7/21 15:18
 * @verison: 1.0
 * @description:
 */
@Data
public class AccommodationPayFormDetailVo {
    @ApiModelProperty("币种名称")
    private String payableCurrencyTypeName;
    @ApiModelProperty("实收金额")
    private BigDecimal actualPayableAmount;
    @ApiModelProperty("付款时间")
    private Date actualPayTime;
    @ApiModelProperty("留学住宿id")
    private Long accommodationId;

}
