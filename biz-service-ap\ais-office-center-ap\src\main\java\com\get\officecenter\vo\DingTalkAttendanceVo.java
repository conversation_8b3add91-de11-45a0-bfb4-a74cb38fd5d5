package com.get.officecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2022/4/29
 * @TIME: 18:41
 * @Description:
 **/
@Data
public class DingTalkAttendanceVo extends BaseEntity {
    /**
     * 员工ID
     */
    @ApiModelProperty(value = "员工ID")
    private Long staffId;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String staffName;
    /**
     * 英文名
     */
    @ApiModelProperty(value = "英文名")
    private String nameEn;
    /**
     * 中文名
     */
    @ApiModelProperty(value = "中文名")
    private String nameChn;
    /**
     * 考勤组
     */
    @ApiModelProperty(value = "考勤组")
    private String attendanceGroup;
    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    private String departmentName;
    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String num;
    /**
     * 职位
     */
    @ApiModelProperty(value = "职位")
    private String position;
    /**
     * 考勤日期
     */
    @ApiModelProperty(value = "考勤日期")
    private String attendanceDate;
    /**
     * 考勤时间
     */
    @ApiModelProperty(value = "考勤时间")
    private String attendanceTime;
    /**
     * 打卡时间
     */
    @ApiModelProperty(value = "打卡时间")
    private String punchTime;
    /**
     * 打卡结果
     */
    @ApiModelProperty(value = "打卡结果")
    private String punchResult;
    /**
     * 打卡地址
     */
    @ApiModelProperty(value = "打卡地址")
    private String punchAddress;
    /**
     * 打卡备注
     */
    @ApiModelProperty(value = "打卡备注")
    private String remark;
    /**
     * 异常打卡原因
     */
    @ApiModelProperty(value = "异常打卡原因")
    private String abnormalReason;
    /**
     * 打卡图片
     */
    @ApiModelProperty(value = "打卡图片")
    private String punchPhoto;
    /**
     * 打卡设备
     */
    @ApiModelProperty(value = "打卡设备")
    private String punchCardEquipment;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;
}
