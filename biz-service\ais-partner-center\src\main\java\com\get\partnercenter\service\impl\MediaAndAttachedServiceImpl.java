package com.get.partnercenter.service.impl;

import com.get.common.eunms.FileTypeEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.partnercenter.dto.MediaAndAttachedDto;
import com.get.partnercenter.entity.SMediaAndAttachedEntity;
import com.get.partnercenter.mapper.SMediaAndAttachedMapper;
import com.get.partnercenter.service.MediaAndAttachedService;
import com.get.partnercenter.vo.MediaAndAttachedVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class MediaAndAttachedServiceImpl implements MediaAndAttachedService {

    @Resource
    private SMediaAndAttachedMapper attachedMapper;

    @Override
    public List<MediaAndAttachedVo> addMediaAndAttachedList(List<MediaAndAttachedDto> mediaAttachedVos) {
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAttachedVo : mediaAttachedVos) {
            SMediaAndAttachedEntity andAttached = BeanCopyUtils.objClone(mediaAttachedVo, SMediaAndAttachedEntity::new);
            Integer nextIndexKey = attachedMapper.getNextIndexKey(mediaAttachedVo.getFkTableId(), mediaAttachedVo.getFkTableName());
            nextIndexKey = GeneralTool.isNotEmpty(nextIndexKey) ? nextIndexKey : 0;
            //实例化对象
            andAttached.setIndexKey(nextIndexKey);
            if (GeneralTool.isEmpty(andAttached.getGmtCreateUser())) {
                andAttached.setGmtCreateUser(SecureUtil.getLoginId());
                andAttached.setGmtCreate(new Date());
            }
            attachedMapper.insert(andAttached);
            MediaAndAttachedVo mediaAndAttachedVo = BeanCopyUtils.objClone(andAttached, MediaAndAttachedVo::new);
            mediaAndAttachedVo.setFilePath(mediaAttachedVo.getFilePath());
            mediaAndAttachedVo.setFileNameOrc(mediaAttachedVo.getFileNameOrc());
            mediaAndAttachedVo.setTypeValue(FileTypeEnum.getValue(mediaAttachedVo.getTypeKey()));
            mediaAndAttachedVo.setId(andAttached.getId());
            mediaAndAttachedVo.setFileKey(mediaAttachedVo.getFileKey());
            mediaAndAttachedVos.add(mediaAndAttachedVo);
        }
        return mediaAndAttachedVos;
    }
}
