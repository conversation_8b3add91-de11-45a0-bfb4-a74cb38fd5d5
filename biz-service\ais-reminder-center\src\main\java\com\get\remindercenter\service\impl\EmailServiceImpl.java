package com.get.remindercenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.AESConstant;
import com.get.common.utils.AESUtils;
import com.get.core.tool.utils.GeneralTool;
import com.get.remindercenter.dao.UnsubscribeEmailMapper;
import com.get.remindercenter.entity.UnsubscribeEmail;
import com.get.remindercenter.service.EmailService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class EmailServiceImpl implements EmailService {

    @Resource
    private UnsubscribeEmailMapper unsubscribeEmailMapper;

    /**
     * 推广邮件退订
     *
     * @param email
     * @param type type发送类型：1Hubs/2市场
     */
    @Override
    public void promotionUnsubscribe(String email, Integer type) throws Exception {
        String emailDecrypt = AESUtils.Decrypt(email, AESConstant.AESKEY);
        List<UnsubscribeEmail> unsubscribeEmails = unsubscribeEmailMapper.selectList(Wrappers.<UnsubscribeEmail>lambdaQuery().eq(UnsubscribeEmail::getEmail, emailDecrypt).eq(UnsubscribeEmail::getType, type));
        if (GeneralTool.isEmpty(unsubscribeEmails)) {
            UnsubscribeEmail unsubscribeEmail = new UnsubscribeEmail();
            unsubscribeEmail.setEmail(emailDecrypt);
            unsubscribeEmail.setType(type);
            unsubscribeEmail.setGmtCreate(new Date());
            unsubscribeEmailMapper.insert(unsubscribeEmail);
        }
    }

}
