package com.get.aisplatformcenterap.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import java.io.Serializable;
import lombok.Data;

/**
 * 
 * @TableName u_label_type
 */
@TableName(value ="u_label_type")
@Data
public class ULabelTypeEntity extends BaseEntity implements Serializable {
    /**
     * 类型名称
     */
    private String typeName;

    /**
     * 类型Key
     */
    private String typeKey;

    /**
     * 类型描述
     */
    private String remark;

    /**
     * 排序，倒序：数字由大到小排列
     */
    private Integer viewOrder;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}