package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: Hardy
 * @create: 2021/11/22 18:19
 * @verison: 1.0
 * @description:
 */
@Data
public class StudentReceivableAndPaySumExportVo {
    @ApiModelProperty(value = "公司名称")
    private String companyShortName;

    @ApiModelProperty(value = "代理编号")
    private String agentNum;

    @ApiModelProperty(value = "BD+代理编号")
    private String fkBdAgentNum;

    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    private String fkAgentName;

    @ApiModelProperty(value = "代理绑定bd名称")
    private String bdName;

    /**
     * 学生名称
     */
    @ApiModelProperty(value = "学生名称")
    private String studentName;

    /**
     * 国家
     */
    @ApiModelProperty(value = "国家")
    private String countryName;

    /**
     * 国家中文名
     */
    @ApiModelProperty(value = "国家中文名")
    private String countryNameChn;

    @ApiModelProperty(value = "渠道/合同方")
    private String channelName;

    /**
     * 学校名
     */
    @ApiModelProperty(value = "学校名")
    private String institutionName;


    /**
     * 学校中文名
     */
    @ApiModelProperty(value = "学校中文名")
    private String institutionNameChn;

    /**
     * 课程名
     */
    @ApiModelProperty(value = "课程名")
    private String institutionCourseName;

    /**
     * 课程中文名
     */
    @ApiModelProperty(value = "课程中文名")
    private String institutionCourseNameChn;

//    /**
//     * 应收金额
//     */
//    @ApiModelProperty(value = "应收金额")
//    private String receivablePlanAmountInfo;
//
//    /**
//     * 实收金额
//     */
//    @ApiModelProperty(value = "实收金额")
//    private String receivableActualAmountInfo;
//
//    /**
//     * 实收差额
//     */
//    @ApiModelProperty(value = "实收差额")
//    private String receivableDiffAmountInfo;
//    /**
//     * 应付金额
//     */
//    @ApiModelProperty(value = "应付金额")
//    private String payablePlanAmountInfo;
//    /**
//     * 实付金额
//     */
//    @ApiModelProperty(value = "实付金额")
//    private String payableActualAmountInfo;
//    /**
//     * 实付差额
//     */
//    @ApiModelProperty(value = "实付差额")
//    private String payableDiffAmountInfo;

    @ApiModelProperty("开学时间")
    private String openingTime;

    @ApiModelProperty("应收计划数量")
    private Integer receivablePlanNum;

    @ApiModelProperty(value = "学费金额")
    private BigDecimal tuitionAmount;

    @ApiModelProperty(value = "佣金费率")
    private String commissionRate;

    /**
     * 应收金额
     */
    @ApiModelProperty("应收金额")
    private String receivablePlanAmount;

    @ApiModelProperty("发票绑定金额")
    private BigDecimal invoiceBindAmount;

    /**
     * 应收金额币种
     */
    @ApiModelProperty("应收金额币种")
    private String receivablePlanAmountCurrencyType;

    /**
     * 实收金额
     */
    @ApiModelProperty("实收金额")
    private String receivableActualAmount;



    /**
     * 实收金额币种
     */
    @ApiModelProperty("实收金额币种")
    private String receivableActualAmountCurrencyType;

    /**
     * 应收差额
     */
    @ApiModelProperty(value = "应收未收")
    private String receivableDiffAmount;

    /**
     * 应收未收币种
     */
//    @ApiModelProperty(value = "应收未收币种")
//    private String receivableDiffAmountCurrencyType;

    @ApiModelProperty(value = "代理佣金费率")
    private String agentCommissionRate;

    /**
     * 应付金额
     */
    @ApiModelProperty(value = "应付金额")
    private String payablePlanAmount;

    @ApiModelProperty("发票绑定应付金额")
    private BigDecimal invoicePayableActualAmount;

    /**
     * 应付金额币种
     */
    @ApiModelProperty(value = "应付金额币种")
    private String payablePlanAmountCurrencyType;

    /**
     * 实付金额
     */
    @ApiModelProperty(value = "实付金额")
    private String payableActualAmount;

    /**
     * 实付金额币种
     */
    @ApiModelProperty(value = "实付金额币种")
    private String payableActualAmountCurrencyType;

    /**
     * 应付差额
     */
    @ApiModelProperty(value = "应付未付")
    private String payableDiffAmount;

    /**
     * 应付差额币种
     */
//    @ApiModelProperty(value = "应付未付币种")
//    private String payableDiffAmountCurrencyType;

}
