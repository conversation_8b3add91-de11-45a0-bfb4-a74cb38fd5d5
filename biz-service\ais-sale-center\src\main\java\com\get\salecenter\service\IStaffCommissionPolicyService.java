package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.service.GetService;
import com.get.salecenter.vo.StaffCommissionPolicyVo;
import com.get.salecenter.entity.StaffCommissionPolicy;
import com.get.salecenter.dto.StaffCommissionPolicyDto;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/2/6 14:58
 * @verison: 1.0
 * @description:
 */
public interface IStaffCommissionPolicyService extends GetService<StaffCommissionPolicy> {

    /**
     *
     * @param staffCommissionPolicyDto
     * @return
     */
    Long add(StaffCommissionPolicyDto staffCommissionPolicyDto);

    /**
     * 删除
     * @param id
     */
    void delete(Long id);

    /**
     * 修改
     * @param staffCommissionPolicyDto
     * @return
     */
    StaffCommissionPolicyVo updateStaffCommissionPolicy(StaffCommissionPolicyDto staffCommissionPolicyDto);

    /**
     * 列表
     * @param staffCommissionPolicyDto
     * @param page
     * @return
     */
    List<StaffCommissionPolicyVo> getStaffCommissionPolicyDtos(StaffCommissionPolicyDto staffCommissionPolicyDto, Page page);

    /**
     * 排序
     */
    void movingOrder(Integer end,Integer start);

    /**
     * 学校下拉
     * @return
     */
    List<BaseSelectEntity> getInstitutionsSelect(Long fkCompanyId);

    /**
     * 项目成员角色下拉
     * @param fkCompanyId
     * @return
     */
    List<BaseSelectEntity> getProjectRoleSelect(Long fkCompanyId);

    /**
     * 详情
     * @param id
     * @return
     */
    StaffCommissionPolicyVo findStaffCommissionPolicyById(Long id);
}
