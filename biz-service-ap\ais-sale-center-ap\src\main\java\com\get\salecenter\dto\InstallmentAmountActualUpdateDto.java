package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 修改实际支付金额Vo类
 *
 * <AUTHOR>
 * @date 2022/4/2 17:04
 */
@Data
public class InstallmentAmountActualUpdateDto {

    @ApiModelProperty(value = "应付计划id")
    @NotNull(message = "应付计划id不能为空")
    private Long payablePlanId;

    @ApiModelProperty(value = "实际支付金额")
    @NotNull(message = "实际支付金额不能为空")
    private BigDecimal amountActual;

    @NotNull(message = "结算状态 不能为空")
    @ApiModelProperty(value = "结算状态：0未结算/1结算中/2代理确认/3财务确认")
    private Integer statusSettlement;

    @ApiModelProperty(value = "实际手续费金额")
    @NotNull(message = "实际手续费金额不能为空")
    private BigDecimal serviceFeeActual;

    @ApiModelProperty(value = "学生代理合同账户Id")
    private Long fkAgentContractAccountId;

}
