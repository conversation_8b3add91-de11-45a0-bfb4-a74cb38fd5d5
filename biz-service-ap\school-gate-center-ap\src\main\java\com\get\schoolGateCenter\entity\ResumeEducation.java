package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("m_resume_education")
public class ResumeEducation extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 所属简历Id
     */
    @ApiModelProperty(value = "所属简历Id")
    @Column(name = "fk_resume_id")
    private Long fkResumeId;
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开始时间")
    @Column(name = "start_date")
    private Date startDate;
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "结束时间")
    @Column(name = "end_date")
    private Date endDate;
    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    @Column(name = "institution")
    private String institution;
    /**
     * 学历
     */
    @ApiModelProperty(value = "学历")
    @Column(name = "graduation_level")
    private String graduationLevel;
    /**
     * 专业
     */
    @ApiModelProperty(value = "专业")
    @Column(name = "major")
    private String major;
    /**
     * 专业描述
     */
    @ApiModelProperty(value = "专业描述")
    @Column(name = "major_description")
    private String majorDescription;
    /**
     * 是否全日制：0否/1是
     */
    @ApiModelProperty(value = "是否全日制：0否/1是")
    @Column(name = "is_full_time")
    private Boolean isFullTime;
    /**
     * 是否有留学经历：0否/1是
     */
    @ApiModelProperty(value = "是否有留学经历：0否/1是")
    @Column(name = "is_abroad_experience")
    private Boolean isAbroadExperience;
}