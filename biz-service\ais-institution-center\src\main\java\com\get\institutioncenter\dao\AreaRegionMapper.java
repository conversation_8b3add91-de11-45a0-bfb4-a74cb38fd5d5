package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.institutioncenter.vo.AreaRegionVo;
import com.get.institutioncenter.entity.AreaRegion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface AreaRegionMapper extends BaseMapper<AreaRegion> {

    int updateByPrimaryKeySelective(AreaRegion record);

    int updateByPrimaryKey(AreaRegion record);


    List<BaseSelectEntity> getByCountryId(@Param("countryId") Long countryId, @Param("fkCompanyIds") List<Long> fkCompanyIds);

    List<AreaRegionVo> getAreaRegionDtoByIds(@Param("ids") Set<Long> ids);

    List<BaseSelectEntity> getAreaRegionSelect(@Param("fkCompanyId")Long fkCompanyId, @Param("companyIds") List<Long> companyIds);

    List<BaseSelectEntity> getAreaRegionSelectByCompanyIds(@Param("fkCompanyIds") List<Long> fkCompanyIds);

    /**
     * 根据州省ids获取对应的大区对象Map
     *
     * @Date 17:26 2023/12/19
     * <AUTHOR>
     */
    List<AreaRegionVo> getRegionMapByStateIds(@Param("stateIds") List<Long> stateIds);

    List<AreaRegionVo> getGeographicalRegions();


    /**
     * 获取最大排序值
     *
     * @return
     */
    Integer getMaxViewOrder();
}