package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.entity.IncentivePolicy;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-03-13
 */
@Mapper
public interface MIncentivePolicyMapper extends BaseMapper<IncentivePolicy>, GetMapper<IncentivePolicy> {

    List<BaseSelectEntity> getInstitutionProviderSelect(Long companyId);

    List<BaseSelectEntity> getInstitutionListByProviderId(Long providerId);
}
