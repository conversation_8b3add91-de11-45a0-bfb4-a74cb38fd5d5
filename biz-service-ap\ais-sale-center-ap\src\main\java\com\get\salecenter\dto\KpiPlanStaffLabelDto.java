package com.get.salecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 考核人员标签参数实体类
 */
@Data
public class KpiPlanStaffLabelDto extends BaseVoEntity {

    @ApiModelProperty(value = "KPI方案考核人员Id集合")
    @NotEmpty(message = "KPI方案考核人员Id集合不能为空", groups = {Add.class})
    private List<Long> fkKpiPlanStaffIdList;

    @ApiModelProperty(value = "文字标签")
    @NotBlank(message = "文字标签不能为空", groups = {Add.class})
    private String wordLabel;
}
