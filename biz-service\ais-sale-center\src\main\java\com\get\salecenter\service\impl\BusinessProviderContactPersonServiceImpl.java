package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.BusinessProviderContactPersonMapper;
import com.get.salecenter.vo.BusinessProviderContactPersonVo;
import com.get.salecenter.entity.BusinessProviderContactPerson;
import com.get.salecenter.service.BusinessProviderContactPersonService;
import com.get.salecenter.dto.BusinessProviderContactPersonListDto;
import com.get.salecenter.dto.BusinessProviderContactPersonDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class BusinessProviderContactPersonServiceImpl implements BusinessProviderContactPersonService {

    @Resource
    private BusinessProviderContactPersonMapper businessProviderContactPersonMapper;
    @Resource
    private UtilService utilService;

    @Override
    public List<BusinessProviderContactPersonVo> getBusinessProviderContactPersonDtos(BusinessProviderContactPersonListDto businessProviderContactPersonListDto, SearchBean<BusinessProviderContactPersonListDto> page) {
        LambdaQueryWrapper<BusinessProviderContactPerson> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(businessProviderContactPersonListDto)) {
            if (GeneralTool.isNotEmpty(businessProviderContactPersonListDto.getKeyWord())) {
                lambdaQueryWrapper.like(BusinessProviderContactPerson::getName, businessProviderContactPersonListDto.getKeyWord());
            }
        }
        lambdaQueryWrapper.orderByDesc(BusinessProviderContactPerson::getGmtCreate);
        IPage<BusinessProviderContactPerson> pages = businessProviderContactPersonMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<BusinessProviderContactPerson> businessProviderContactPersons = pages.getRecords();
        page.setAll((int) pages.getTotal());
        return businessProviderContactPersons.stream().map(businessProviderContactPerson -> BeanCopyUtils.objClone(businessProviderContactPerson, BusinessProviderContactPersonVo::new)).collect(Collectors.toList());
    }

    @Override
    public void addBusinessProviderContactPerson(BusinessProviderContactPersonDto businessProviderContactPersonDto) {
        if (GeneralTool.isEmpty(businessProviderContactPersonDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        BusinessProviderContactPerson businessProviderContactPerson = BeanCopyUtils.objClone(businessProviderContactPersonDto, BusinessProviderContactPerson::new);
        utilService.setCreateInfo(businessProviderContactPerson);
        businessProviderContactPersonMapper.insert(businessProviderContactPerson);
    }

    @Override
    public BusinessProviderContactPersonVo updateBusinessProviderContactPerson(BusinessProviderContactPersonDto businessProviderContactPersonDto) {
        BusinessProviderContactPerson businessProviderContactPerson = BeanCopyUtils.objClone(businessProviderContactPersonDto, BusinessProviderContactPerson::new);
        utilService.updateUserInfoToEntity(businessProviderContactPerson);
        businessProviderContactPersonMapper.updateById(businessProviderContactPerson);
        return findBusinessProviderContactPersonById(businessProviderContactPerson.getId());
    }

    private BusinessProviderContactPersonVo findBusinessProviderContactPersonById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        BusinessProviderContactPerson businessProviderContactPerson = businessProviderContactPersonMapper.selectById(id);
        return BeanCopyUtils.objClone(businessProviderContactPerson, BusinessProviderContactPersonVo::new);
    }

    @Override
    public void deleteBusinessProviderContactPerson(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        businessProviderContactPersonMapper.deleteById(id);
    }
}
