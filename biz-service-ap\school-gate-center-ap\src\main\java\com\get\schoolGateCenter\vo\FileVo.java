package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class FileVo extends BaseVoEntity implements Serializable {
    private String type;
    @ApiModelProperty(value = "文件路径")
    private String filePath;
    @ApiModelProperty(value = "文件源名称")
    private String fileNameOrc;

    @ApiModelProperty(value = "文件key")
    private String fileKey;
    @ApiModelProperty(value = "类型")
    private String typeValue;
    @ApiModelProperty(value = "guid")
    private String guid;

}
