package com.get.salecenter.dto.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

@Data
public class InsuranceSummaryQueryDto {

    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    private String agentName;


    @ApiModelProperty(value = "公司Ids")
    private List<Long> fkCompanyIds;

    /**
     * 保险购买时间
     */
    @ApiModelProperty(value = "保险购买时间-结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceBuyTimeEnd;

    /**
     * 保险购买时间
     */
    @ApiModelProperty(value = "保险购买时间-开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceBuyTimeStart;

    /**
     * 保单结束时间
     */
    @ApiModelProperty(value = "保单结束时间-结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceEndTimeEnd;


    /**
     * 保单结束时间
     */
    @ApiModelProperty(value = "保单结束时间-开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceEndTimeStart;

    /**
     * 保险单号
     */
    @ApiModelProperty(value = "保险单号")
    private String insuranceNum;

    /**
     * 保单开始时间
     */
    @ApiModelProperty(value = "保单开始时间-结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceStartTimeEnd;

    /**
     * 保单开始时间
     */
    @ApiModelProperty(value = "保单开始时间-开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceStartTimeStart;

    /**
     * 项目成员名称
     */
    @ApiModelProperty(value = "项目成员名称")
    private String memberName;

    /**
     * bd名称
     */
    @ApiModelProperty(value = "bd名称")
    private String staffName;

    /**
     * 状态：0关闭/1打开
     */
    @ApiModelProperty(value = "状态：0关闭/1打开")
    private Integer status;

    /**
     * 学生名称编号
     */
    @ApiModelProperty(value = "学生名称编号")
    private String studentName;

    @ApiModelProperty(value = "保险创建时间范围起")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date gmtCreateBeginTime;

    @ApiModelProperty(value = "保险创建时间范围末")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date gmtCreateEndTime;

    /**
     * 业务渠道Id
     */
    @ApiModelProperty(value = "业务渠道Id")
    private Long fkBusinessChannelId;

    /**
     * 应收状态
     */
    @ApiModelProperty(value = "应收状态：0/未收 1/部分已收 2/已收齐")
    private Integer arStatus;

    /**
     * 应付状态
     */
    @ApiModelProperty(value = "应付状态：0/未付 1/部分已付 2/已付清")
    private Integer apStatus;

    //==================path=====================
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "是否财务专用：0否/1是")
    @Column(name = "is_hidden")
    private Boolean isHidden;

    private List<Long> staffFollowerIds;

    /**
     * 服务提供商/产品
     */
    @ApiModelProperty(value = "服务提供商/产品")
    private String businessProviderAndProductName;


}
