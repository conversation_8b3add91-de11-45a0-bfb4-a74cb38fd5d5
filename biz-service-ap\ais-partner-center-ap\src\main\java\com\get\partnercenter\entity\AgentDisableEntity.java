package com.get.partnercenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2024-12-20 14:10:35
 */

@Data
@TableName("u_agent_disabled")
public class AgentDisableEntity extends BaseEntity {

  @ApiModelProperty("伙伴用户Id")
  private Long id;
 
  @ApiModelProperty("租户Id")
  private Long fkAgentId;

}
