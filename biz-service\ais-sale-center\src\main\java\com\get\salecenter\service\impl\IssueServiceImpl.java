package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.platformconfigcenter.feign.IPlatformConfigCenterClient;
import com.get.salecenter.dao.newissue.StudentInstitutionCourseMapper;
import com.get.salecenter.dao.newissue.newIssueUserSuperiorMapper;
import com.get.salecenter.entity.NewIssueUserSuperior;
import com.get.salecenter.service.IIssueService;
import com.get.salecenter.dto.NewIssueUserSuperiorDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/10/25 14:48
 */
@Service
public class IssueServiceImpl extends BaseServiceImpl<newIssueUserSuperiorMapper, NewIssueUserSuperior> implements IIssueService {

    //TODO 注释ISSUE相关功能 lucky  2024/12/23

//    @Resource
//    private UtilService utilService;
//    @Resource
//    private newIssueUserSuperiorMapper newIssueUserSuperiorMapper;
//    @Resource
//    private IPlatformConfigCenterClient iPlatformConfigCenterClient;
//
//    @Resource
//    private StudentInstitutionCourseMapper studentInstitutionCourseMapper;

//    @Override
//    public void getSetIssueUserSuperior(Long fkUserId, List<NewIssueUserSuperiorDto> userSuperiorVos, Long fkCompanyId) {
//
//        userSuperiorVos.stream().forEach(d -> {
//            NewIssueUserSuperior newIssueUserSuperior = new NewIssueUserSuperior();
//            newIssueUserSuperior.setFkUserId(fkUserId);
//            newIssueUserSuperior.setFkUserSuperiorId(d.getFkUserSuperiorId());
//            newIssueUserSuperior.setFkCompanyId(fkCompanyId);
//            utilService.setCreateInfo(newIssueUserSuperior);
//            newIssueUserSuperiorMapper.insert(newIssueUserSuperior);
//        });
//    }

//    @Override
//    public NewIssueUserSuperiorVo getIssueUserSuperior(Long userId, Long fkAgentId, Long fkCompanyId) {
//
//        Result<List<UserInfoDto>> userByAgentId = iPlatformConfigCenterClient.getUserByAgentId(fkAgentId, fkCompanyId);
//        List<UserInfoDto> data = userByAgentId.getData();
//        if (GeneralTool.isEmpty(data)) return null;
//        List<UserInfoDto> oldDatas = data.stream().filter(d -> !d.getId().equals(userId)).collect(Collectors.toList());
//        Map<Long, UserInfoDto> userInfoDtoMap = oldDatas.stream().collect(Collectors.toMap(UserInfoDto::getId, Function.identity()));
//        NewIssueUserSuperiorVo returnData = new NewIssueUserSuperiorVo();
//
//
//        //查看已经绑定的
//        List<NewIssueUserSuperior> newIssueUserSuperiors = newIssueUserSuperiorMapper.selectExistDatas(userId);
//        if (GeneralTool.isNotEmpty(newIssueUserSuperiors)) {
//            List<UserInfoDto> superiors = new ArrayList<>();
//            //查找属于自己的上级
//            newIssueUserSuperiors.forEach(d -> {
//                if (!userId.equals(d.getFkUserSuperiorId())) {
//                    if(GeneralTool.isNotEmpty(userInfoDtoMap.get(d.getFkUserSuperiorId())))
//                    {
//                        superiors.add(userInfoDtoMap.get(d.getFkUserSuperiorId()));
//                    }
//                    userInfoDtoMap.remove(d.getFkUserSuperiorId());
//                } else {
//                    userInfoDtoMap.remove(d.getFkUserSuperiorId());
//                }
//            });
//            returnData.setIsExistDatas(superiors);
//            List<Long> collect = newIssueUserSuperiors.stream().filter(d -> !d.getFkUserId().equals(userId)).map(NewIssueUserSuperior::getFkUserId).collect(Collectors.toList());
//            //当前设置上级的人下级
//            if (GeneralTool.isNotEmpty(collect)) {
//                ArrayList<Long> longs = new ArrayList<>();
//                longs.addAll(collect);
//                List<Long> lowerUser = IssueTree(collect, longs,fkCompanyId);
//                lowerUser.stream().filter(Objects::nonNull).forEach(d -> {
//                    if (userInfoDtoMap.containsKey(d)) userInfoDtoMap.remove(d);
//                });
//            }
//            List<UserInfoDto> noExist = new ArrayList<>();
//            userInfoDtoMap.entrySet().stream().filter(Objects::nonNull).forEach(d -> noExist.add(d.getValue()));
//            returnData.setNotExistDatas(noExist);
//        } else {
//            //未绑定
//            returnData.setNotExistDatas(oldDatas);
//        }
//        return returnData;
//    }

//    @Override
//    public void getRemoveIssueUserSuperior(Long fkUserId, List<NewIssueUserSuperiorDto> userSuperiorVos, Long fkCompanyId) {
//        LambdaQueryWrapper<NewIssueUserSuperior> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(NewIssueUserSuperior::getFkUserId, fkUserId);
//        lambdaQueryWrapper.eq(NewIssueUserSuperior::getFkCompanyId, fkCompanyId);
//        List<NewIssueUserSuperior> newIssueUserSuperiors = newIssueUserSuperiorMapper.selectList(lambdaQueryWrapper);
//        if (GeneralTool.isEmpty(newIssueUserSuperiors))
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        Map<Long, NewIssueUserSuperiorDto> deleteDataMap = userSuperiorVos.stream().collect(Collectors.toMap(NewIssueUserSuperiorDto::getFkUserSuperiorId, Function.identity()));
//
//        newIssueUserSuperiors.stream().forEach(d -> {
//            NewIssueUserSuperiorDto newIssueUserSuperiorDto = deleteDataMap.get(d.getFkUserSuperiorId());
//            if (GeneralTool.isNotEmpty(newIssueUserSuperiorDto)) newIssueUserSuperiorMapper.deleteById(d.getId());
//        });
//
//    }

//    @Override
//    public List<UserInfoDto> getIssueUserSubordinate(long fkUserId, long fkAgentId, Long fkCompanyId) {
//        LambdaQueryWrapper<NewIssueUserSuperior> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(NewIssueUserSuperior::getFkUserSuperiorId, fkUserId);
//        lambdaQueryWrapper.eq(NewIssueUserSuperior::getFkCompanyId, fkCompanyId);
//        List<NewIssueUserSuperior> newIssueUserSuperiors = newIssueUserSuperiorMapper.selectList(lambdaQueryWrapper);
//        if (GeneralTool.isEmpty(newIssueUserSuperiors))
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//
//        Map<Long, NewIssueUserSuperior> newIssueUserSuperiorMap = newIssueUserSuperiors.stream().collect(Collectors.toMap(NewIssueUserSuperior::getFkUserId, Function.identity()));
//
//        List<UserInfoDto> returnData = iPlatformConfigCenterClient.getUserByAgentId(fkAgentId, fkCompanyId).getData();
//
//        List<UserInfoDto> userInfoDtos = new ArrayList<>();
//
//        returnData.stream().forEach(d -> {
//            if (newIssueUserSuperiorMap.containsKey(d.getId())) userInfoDtos.add(d);
//        });
//
//        return userInfoDtos;
//    }

//    @Override
//    public void updateIssueStudentInstitutionCourse(EventOfferPlanDto offerItemVo, StudentOfferItem offerItem,Long issueCourseId,Boolean isUpdateCourse) {
//        //更新ISSUE相关的课程信息
//        //如来源为新issue，则需要同步作废issue的课程申请,如申请计划恢复则同步恢复
//        StudentInstitutionCourseDto studentInstitutionCourseDto = iPlatformConfigCenterClient.getIssueStudentInstitutionCourseById(issueCourseId);
//        if(GeneralTool.isNotEmpty(studentInstitutionCourseDto))
//        {
//            updateIssueStudentInstitutionCourse(studentInstitutionCourseDto, offerItem,isUpdateCourse,offerItemVo.getDeferOpeningTime());
//        }
//
//    }

//    @Override
//    public void updateIssueStudentInstitutionCourse(EventOfferPlanDto offerItemVo, StudentOfferItem offerItem,Long issueCourseId,Boolean isUpdateCourse) {
//        //更新ISSUE相关的课程信息
//        //如来源为新issue，则需要同步作废issue的课程申请,如申请计划恢复则同步恢复
//        StudentInstitutionCourseDto studentInstitutionCourseDto = iPlatformConfigCenterClient.getIssueStudentInstitutionCourseById(issueCourseId);
//        if(GeneralTool.isNotEmpty(studentInstitutionCourseDto))
//        {
//            updateIssueStudentInstitutionCourse(studentInstitutionCourseDto, offerItem,isUpdateCourse,offerItemVo.getDeferOpeningTime());
//        }
//
//    }

//    @Override
//    public void updateIssueStudentInstitutionCourseStatus(Long issueCourseId,Long status) {
//        StudentInstitutionCourseDto studentInstitutionCourseDto = iPlatformConfigCenterClient.getIssueStudentInstitutionCourseById(issueCourseId);
//        if(GeneralTool.isNotEmpty(studentInstitutionCourseDto))
//        {
//            StudentInstitutionCourse studentInstitutionCourse = BeanCopyUtils.objClone(studentInstitutionCourseDto, StudentInstitutionCourse::new);
//            studentInstitutionCourse.setStatus(status.equals(0L)?0:1);  //状态：0禁用（删除）/1激活
//            iPlatformConfigCenterClient.updateIssueStudentInstitutionCourse(studentInstitutionCourse);
//        }
//    }


//    private void updateIssueStudentInstitutionCourse(StudentInstitutionCourseDto studentInstitutionCourseDto, StudentOfferItem offerItem,Boolean isUpdateCourse,Date oldDeferOpeningTime) {
//        StudentInstitutionCourse studentInstitutionCourse = BeanCopyUtils.objClone(studentInstitutionCourseDto, StudentInstitutionCourse::new);
//        if(GeneralTool.isNotEmpty(studentInstitutionCourse))
//        {
//            String modifyUserName = GetAuthInfo.getLoginId();
//            StringBuilder modifyRemark = new StringBuilder();
//            modifyRemark.append("BMS用户[").append(modifyUserName).append("],于[").append(DateUtil.formatDateTime(new Date())).append("]修改了申请课程信息.");
//            //是否需要修改课程信息
//            if(isUpdateCourse && offerItem.getDeferOpeningTime().compareTo(oldDeferOpeningTime) != 0){
//                studentInstitutionCourse.setFkInstitutionId(offerItem.getFkInstitutionId());
//                studentInstitutionCourse.setFkInstitutionCourseId(offerItem.getFkInstitutionCourseId());
//                if(GeneralTool.isNotEmpty(offerItem.getFkInstitutionCourseCustomId()) && !offerItem.getFkInstitutionCourseCustomId().equals(-1L))
//                {
//                    studentInstitutionCourse.setFkInstitutionCourseId(offerItem.getFkInstitutionCourseCustomId());
//                }
//                studentInstitutionCourse.setInstitutionCourseName("");//置空
//                studentInstitutionCourse.setInstitutionCourseWebsite("https://");//置为默认值
//                studentInstitutionCourse.setFkInstitutionFacultyId(null);//置空
//                studentInstitutionCourse.setFkInstitutionZoneId(null);//置空
//                studentInstitutionCourse.setInfoJson("");//置空
//                studentInstitutionCourse.setFkMajorLevelId(null);//置空
//                if(GeneralTool.isNotEmpty(offerItem.getFkInstitutionCourseMajorLevelIds()))
//                {
//                    //ISSUE只有一个课程等级
//                    String leveId = offerItem.getFkInstitutionCourseMajorLevelIds().split(",")[0];
//                    if(GeneralTool.isNotEmpty(leveId))
//                    {
//                        studentInstitutionCourse.setFkMajorLevelId(Long.valueOf(leveId));//根据课程id动态获取
//                    }
//                }
//                if(GeneralTool.isNotEmpty(offerItem.getCourseWebsite()))
//                {
//                    studentInstitutionCourse.setInstitutionCourseWebsite(offerItem.getCourseWebsite());
//                }
//                //如为手输入，则写入课程名称
//                if((GeneralTool.isEmpty(offerItem.getFkInstitutionCourseCustomId()) || offerItem.getFkInstitutionCourseCustomId().equals(-1L)) && GeneralTool.isNotEmpty(offerItem.getOldCourseCustomName()))
//                {
//                    studentInstitutionCourse.setInstitutionCourseName(offerItem.getOldCourseCustomName());
//                }
//                studentInstitutionCourse.setOpeningTime(offerItem.getDeferOpeningTime());
//                studentInstitutionCourse.setModifyRemark(modifyRemark.toString());
//                iPlatformConfigCenterClient.updateIssueStudentInstitutionCourseWithNull(studentInstitutionCourse);
//            }
//            //是否仅需要修改申请课程信息
//            else if(isUpdateCourse)
//            {
//                studentInstitutionCourse.setFkInstitutionId(offerItem.getFkInstitutionId());
//                studentInstitutionCourse.setFkInstitutionCourseId(offerItem.getFkInstitutionCourseId());
//                if(GeneralTool.isNotEmpty(offerItem.getFkInstitutionCourseCustomId())  && !offerItem.getFkInstitutionCourseCustomId().equals(-1L))
//                {
//                    studentInstitutionCourse.setFkInstitutionCourseId(offerItem.getFkInstitutionCourseCustomId());
//                }
//                studentInstitutionCourse.setInstitutionCourseName("");//置空
//                studentInstitutionCourse.setInstitutionCourseWebsite("https://");//置空
//                studentInstitutionCourse.setFkInstitutionFacultyId(null);//置空
//                studentInstitutionCourse.setFkInstitutionZoneId(null);//置空
//                studentInstitutionCourse.setInfoJson("");//置空
//                studentInstitutionCourse.setFkMajorLevelId(null);//置空
//                if(GeneralTool.isNotEmpty(offerItem.getFkInstitutionCourseMajorLevelIds()))
//                {
//                    //ISSUE只有一个课程等级
//                    String leveId = offerItem.getFkInstitutionCourseMajorLevelIds().split(",")[0];
//                    if(GeneralTool.isNotEmpty(leveId))
//                    {
//                        studentInstitutionCourse.setFkMajorLevelId(Long.valueOf(leveId));//根据课程id动态获取
//                    }
//                }
//                if(GeneralTool.isNotEmpty(offerItem.getCourseWebsite()))
//                {
//                    studentInstitutionCourse.setInstitutionCourseWebsite(offerItem.getCourseWebsite());
//                }
//                //如为手输入，则写入课程名称
//                if((GeneralTool.isEmpty(offerItem.getFkInstitutionCourseCustomId()) || offerItem.getFkInstitutionCourseCustomId().equals(-1L)) && GeneralTool.isNotEmpty(offerItem.getOldCourseCustomName()))
//                {
//                    studentInstitutionCourse.setInstitutionCourseName(offerItem.getOldCourseCustomName());
//                }
//                studentInstitutionCourse.setModifyRemark(modifyRemark.toString());
//                iPlatformConfigCenterClient.updateIssueStudentInstitutionCourseWithNull(studentInstitutionCourse);
//            }
//            //是否仅需要修改入学时间
//            else if(GeneralTool.isNotEmpty(offerItem.getDeferOpeningTime()) && offerItem.getDeferOpeningTime().compareTo(oldDeferOpeningTime) != 0)
//            {
//                studentInstitutionCourse.setOpeningTime(offerItem.getDeferOpeningTime());
//                iPlatformConfigCenterClient.updateIssueStudentInstitutionCourse(studentInstitutionCourse);
//            }
//        }
//    }
//
//    public List<Long> IssueTree(List<Long> fkUserId, List<Long> allDatas, Long fkCompanyId) {
//        LambdaQueryWrapper<NewIssueUserSuperior> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(NewIssueUserSuperior::getFkCompanyId, fkCompanyId);
//        lambdaQueryWrapper.in(NewIssueUserSuperior::getFkUserSuperiorId, fkUserId);
//        List<NewIssueUserSuperior> newIssueUserSuperiors = newIssueUserSuperiorMapper.selectList(lambdaQueryWrapper);
//
//        if (GeneralTool.isNotEmpty(newIssueUserSuperiors)) {
//            List<Long> collect = newIssueUserSuperiors.stream().map(NewIssueUserSuperior::getFkUserId).collect(Collectors.toList());
//            allDatas.addAll(collect);
//            this.IssueTree(collect, allDatas, fkCompanyId);
//        }
//
//        return allDatas;
//    }



}
