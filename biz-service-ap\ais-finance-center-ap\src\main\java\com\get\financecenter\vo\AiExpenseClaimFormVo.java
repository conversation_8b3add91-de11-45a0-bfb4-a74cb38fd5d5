package com.get.financecenter.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @author: Sea
 * @create: 2021/4/7 10:49
 * @verison: 1.0
 * @description:
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AiExpenseClaimFormVo {
    @ApiModelProperty(value = "id")
    @JsonIgnore
    private Long id;
    //    /**
//     * 公司名称
//     */
//    @ApiModelProperty(value = "公司名称")
//    private String companyName;
//
//    /**
//     * 部门名称
//     */
//    @ApiModelProperty(value = "部门名称")
//    private String departmentName;
    @ApiModelProperty(value = "申请人Id")
    @JsonIgnore
    private Long fkStaffId;

    /**
     * 报销人名称
     */
    @ApiModelProperty(value = "报销人名称")
    private String staffName;

    @ApiModelProperty(value = "币种编号")
    @JsonIgnore
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "票据张数")
    private Integer billCount;

    @ApiModelProperty(value = "摘要")
    private String summary;

    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String currencyTypeName;

    /**
     * 报销金额总和
     */
    @ApiModelProperty(value = "报销金额总和")
    @JsonIgnore
    private BigDecimal amountSum;

    @ApiModelProperty(value = "状态：0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销")
    private String status;

    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    /**
     * 表单明细对象集合
     */
    @ApiModelProperty(value = "表单明细对象集合")
    private List<AiExpenseClaimFormItemVo> expenseClaimFormItemDtoList;


}
