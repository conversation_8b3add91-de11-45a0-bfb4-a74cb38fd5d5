package com.get.pmpcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.pmpcenter.dto.agent.AgentCommissionTypeDto;
import com.get.pmpcenter.entity.AgentCommissionType;
import com.get.pmpcenter.vo.agent.AgentCommissionTypeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface AgentCommissionTypeMapper extends BaseMapper<AgentCommissionType>, GetMapper<AgentCommissionType> {
    List<AgentCommissionTypeVo> getAgentCommissionTypeList(@Param("agentCommissionTypeDto") AgentCommissionTypeDto agentCommissionTypeDto, IPage<AgentCommissionTypeVo> iPage);

    /**
     * 查询最大排序
     * @return
     */
    Integer selectMaxViewOrderNumByAll();
}
