<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.platformconfigcenter.dao.appissue.StudentMapper">
    <resultMap id="BaseResultMap" type="com.get.platformconfigcenter.entity.PlatFormStudent">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_agent_id" jdbcType="BIGINT" property="fkAgentId"/>
        <result column="name_zh" jdbcType="VARCHAR" property="nameZh"/>
        <result column="name_en" jdbcType="VARCHAR" property="nameEn"/>
        <result column="last_name" jdbcType="VARCHAR" property="lastName"/>
        <result column="first_name" jdbcType="VARCHAR" property="firstName"/>
        <result column="phone_region" jdbcType="VARCHAR" property="phoneRegion"/>
        <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="email_password" jdbcType="VARCHAR" property="emailPassword"/>
        <result column="fk_student_app_country_id_from" jdbcType="VARCHAR" property="fkStudentAppCountryIdFrom"/>
        <result column="fk_area_state_id_from" jdbcType="VARCHAR" property="fkAreaStateIdFrom"/>
        <result column="fk_area_city_id_from" jdbcType="VARCHAR" property="fkAreaCityIdFrom"/>
        <result column="fk_area_city_division_id_from" jdbcType="VARCHAR" property="fkAreaCityDivisionIdFrom"/>
        <result column="contact_address_1" jdbcType="VARCHAR" property="contactAddress1"/>
        <result column="contact_address_2" jdbcType="VARCHAR" property="contactAddress2"/>
        <result column="contact_postcode" jdbcType="VARCHAR" property="contactPostcode"/>
        <result column="birthday" jdbcType="DATE" property="birthday"/>
        <result column="gender" jdbcType="INTEGER" property="gender"/>
        <result column="title" jdbcType="INTEGER" property="title"/>
        <result column="fk_student_app_country_id" jdbcType="BIGINT" property="fkStudentAppCountryId"/>
        <result column="fk_student_app_ancestry_id" jdbcType="BIGINT" property="fkStudentAppAncestryId"/>
        <result column="fk_student_app_mother_language_id" jdbcType="BIGINT" property="fkStudentAppMotherLanguageId"/>
        <result column="fk_student_app_country_id_birth" jdbcType="BIGINT" property="fkStudentAppCountryIdBirth"/>
        <result column="city_birth" jdbcType="VARCHAR" property="cityBirth"/>
        <result column="is_lived_since_birth" jdbcType="BIT" property="isLivedSinceBirth"/>
        <result column="fk_student_app_country_id_live" jdbcType="BIT" property="fkStudentAppCountryIdLive"/>
        <result column="migration_time" jdbcType="DATE" property="migrationTime"/>
        <result column="staying_right_time" jdbcType="DATE" property="stayingRightTime"/>
        <result column="fk_student_app_country_id_passport" jdbcType="BIGINT" property="fkStudentAppCountryIdPassport"/>
        <result column="is_have_passport" jdbcType="BIT" property="isHavePassport"/>
        <result column="passport_issue_date" jdbcType="DATE" property="passportIssueDate"/>
        <result column="passport_issue_expried" jdbcType="DATE" property="passportIssueExpried"/>
        <result column="passport_number" jdbcType="VARCHAR" property="passportNumber"/>
        <result column="follow_up_phone_code_1" jdbcType="VARCHAR" property="followUpPhoneCode1"/>
        <result column="follow_up_phone_code_2" jdbcType="VARCHAR" property="followUpPhoneCode2"/>
        <result column="follow_up_phone_code_3" jdbcType="VARCHAR" property="followUpPhoneCode3"/>
        <result column="follow_up_phone_number_1" jdbcType="VARCHAR" property="followUpPhoneNumber1"/>
        <result column="follow_up_phone_number_2" jdbcType="VARCHAR" property="followUpPhoneNumber2"/>
        <result column="follow_up_phone_number_3" jdbcType="VARCHAR" property="followUpPhoneNumber3"/>
        <result column="follow_up_email_1" jdbcType="VARCHAR" property="followUpEmail1"/>
        <result column="follow_up_email_2" jdbcType="VARCHAR" property="followUpEmail2"/>
        <result column="follow_up_email_3" jdbcType="VARCHAR" property="followUpEmail3"/>
        <result column="father_last_name" jdbcType="VARCHAR" property="fatherLastName"/>
        <result column="father_first_name" jdbcType="VARCHAR" property="fatherFirstName"/>
        <result column="father_email" jdbcType="VARCHAR" property="fatherEmail"/>
        <result column="father_phone_number" jdbcType="VARCHAR" property="fatherPhoneNumber"/>
        <result column="monter_last_name" jdbcType="VARCHAR" property="monterLastName"/>
        <result column="monter_first_name" jdbcType="VARCHAR" property="monterFirstName"/>
        <result column="monter_email" jdbcType="VARCHAR" property="monterEmail"/>
        <result column="monter_phone_number" jdbcType="VARCHAR" property="monterPhoneNumber"/>
        <result column="parent_contact_postcode" jdbcType="VARCHAR" property="parentContactPostcode"/>
        <result column="parent_contact_address1" jdbcType="VARCHAR" property="parentContactAddress1"/>
        <result column="parent_contact_address2" jdbcType="VARCHAR" property="parentContactAddress2"/>
        <result column="fk_student_app_country_id_parent" jdbcType="VARCHAR" property="fkStudentAppCountryIdParent"/>
        <result column="fk_area_state_id_parent" jdbcType="VARCHAR" property="fkAreaStateIdParent"/>
        <result column="fk_area_city_id_parent" jdbcType="VARCHAR" property="fkAreaCityIdParent"/>
        <result column="is_need_accommodation" jdbcType="BIT" property="isNeedAccommodation"/>
        <result column="accommodation_type" jdbcType="INTEGER" property="accommodationType"/>
        <result column="is_accommodation_smoke" jdbcType="BIT" property="isAccommodationSmoke"/>
        <result column="is_accommodation_pet" jdbcType="BIT" property="isAccommodationPet"/>
        <result column="is_accommodation_children" jdbcType="BIT" property="isAccommodationChildren"/>
        <result column="is_accommodation_food" jdbcType="BIT" property="isAccommodationFood"/>
        <result column="accommodation_food_note" jdbcType="VARCHAR" property="accommodationFoodNote"/>
        <result column="is_accommodation_other" jdbcType="BIT" property="isAccommodationOther"/>
        <result column="accommodation_other_note" jdbcType="VARCHAR" property="accommodationOtherNote"/>
        <result column="is_need_pick_up" jdbcType="BIT" property="isNeedPickUp"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, fk_agent_id, name_zh, name_en, last_name, first_name, phone_region, phone_number, 
    email, email_password, fk_student_app_country_id_from, fk_area_state_id_from, fk_area_city_id_from, 
    fk_area_city_division_id_from,contact_address_1, contact_address_2, contact_postcode, birthday, gender, title,
    fk_student_app_country_id, fk_student_app_ancestry_id, fk_student_app_mother_language_id, 
    fk_student_app_country_id_birth, city_birth, is_lived_since_birth,fk_student_app_country_id_live,migration_time,
    staying_right_time, fk_student_app_country_id_passport, is_have_passport, passport_issue_date, 
    passport_issue_expried, passport_number,follow_up_phone_code_1,follow_up_phone_code_2,follow_up_phone_code_3,
    follow_up_phone_number_1,
    follow_up_phone_number_2,
    follow_up_phone_number_3, follow_up_email_1, follow_up_email_2, follow_up_email_3, 
    father_last_name, father_first_name, father_email, father_phone_number, monter_last_name, 
    monter_first_name, monter_email, monter_phone_number, parent_contact_postcode, parent_contact_address1, 
    parent_contact_address2, fk_student_app_country_id_parent, fk_area_state_id_parent, 
    fk_area_city_id_parent, is_need_accommodation, accommodation_type, is_accommodation_smoke, 
    is_accommodation_pet, is_accommodation_children, is_accommodation_food, accommodation_food_note, 
    is_accommodation_other, accommodation_other_note, is_need_pick_up, gmt_create, gmt_create_user, 
    gmt_modified, gmt_modified_user
  </sql>

    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true"  parameterType="com.get.platformconfigcenter.entity.PlatFormStudent">
        insert into m_student
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkAgentId != null">
                fk_agent_id,
            </if>
            <if test="nameZh != null">
                name_zh,
            </if>
            <if test="nameEn != null">
                name_en,
            </if>
            <if test="lastName != null">
                last_name,
            </if>
            <if test="firstName != null">
                first_name,
            </if>
            <if test="phoneRegion != null">
                phone_region,
            </if>
            <if test="phoneNumber != null">
                phone_number,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="emailPassword != null">
                email_password,
            </if>
            <if test="fkStudentAppCountryIdFrom != null">
                fk_student_app_country_id_from,
            </if>
            <if test="fkAreaStateIdFrom != null">
                fk_area_state_id_from,
            </if>
            <if test="fkAreaCityIdFrom != null">
                fk_area_city_id_from,
            </if>
            <if test="fkAreaCityDivisionIdFrom != null">
                fk_area_city_division_id_from,
            </if>
            <if test="contactAddress1 != null">
                contact_address_1,
            </if>
            <if test="contactAddress2 != null">
                contact_address_2,
            </if>
            <if test="contactPostcode != null">
                contact_postcode,
            </if>
            <if test="birthday != null">
                birthday,
            </if>
            <if test="gender != null">
                gender,
            </if>
            <if test="title != null">
                title,
            </if>
            <if test="fkStudentAppCountryId != null">
                fk_student_app_country_id,
            </if>
            <if test="fkStudentAppAncestryId != null">
                fk_student_app_ancestry_id,
            </if>
            <if test="fkStudentAppMotherLanguageId != null">
                fk_student_app_mother_language_id,
            </if>
            <if test="fkStudentAppCountryIdBirth != null">
                fk_student_app_country_id_birth,
            </if>
            <if test="cityBirth != null">
                city_birth,
            </if>
            <if test="isLivedSinceBirth != null">
                is_lived_since_birth,
            </if>
            <if test="fkStudentAppCountryIdLive != null">
                fk_student_app_country_id_live,
            </if>
            <if test="migrationTime != null">
                migration_time,
            </if>
            <if test="stayingRightTime != null">
                staying_right_time,
            </if>
            <if test="fkStudentAppCountryIdPassport != null">
                fk_student_app_country_id_passport,
            </if>
            <if test="isHavePassport != null">
                is_have_passport,
            </if>
            <if test="passportIssueDate != null">
                passport_issue_date,
            </if>
            <if test="passportIssueExpried != null">
                passport_issue_expried,
            </if>
            <if test="passportNumber != null">
                passport_number,
            </if>
            <if test="followUpPhoneCode1 != null">
                follow_up_phone_code_1,
            </if>
            <if test="followUpPhoneCode2 != null">
                follow_up_phone_code_2,
            </if>
            <if test="followUpPhoneCode3 != null">
                follow_up_phone_code_3,
            </if>
            <if test="followUpPhoneNumber1 != null">
                follow_up_phone_number_1,
            </if>
            <if test="followUpPhoneNumber2 != null">
                follow_up_phone_number_2,
            </if>
            <if test="followUpPhoneNumber3 != null">
                follow_up_phone_number_3,
            </if>
            <if test="followUpEmail1 != null">
                follow_up_email_1,
            </if>
            <if test="followUpEmail2 != null">
                follow_up_email_2,
            </if>
            <if test="followUpEmail3 != null">
                follow_up_email_3,
            </if>
            <if test="fatherLastName != null">
                father_last_name,
            </if>
            <if test="fatherFirstName != null">
                father_first_name,
            </if>
            <if test="fatherEmail != null">
                father_email,
            </if>
            <if test="fatherPhoneNumber != null">
                father_phone_number,
            </if>
            <if test="monterLastName != null">
                monter_last_name,
            </if>
            <if test="monterFirstName != null">
                monter_first_name,
            </if>
            <if test="monterEmail != null">
                monter_email,
            </if>
            <if test="monterPhoneNumber != null">
                monter_phone_number,
            </if>
            <if test="parentContactPostcode != null">
                parent_contact_postcode,
            </if>
            <if test="parentContactAddress1 != null">
                parent_contact_address1,
            </if>
            <if test="parentContactAddress2 != null">
                parent_contact_address2,
            </if>
            <if test="fkStudentAppCountryIdParent != null">
                fk_student_app_country_id_parent,
            </if>
            <if test="fkAreaStateIdParent != null">
                fk_area_state_id_parent,
            </if>
            <if test="fkAreaCityIdParent != null">
                fk_area_city_id_parent,
            </if>
            <if test="isNeedAccommodation != null">
                is_need_accommodation,
            </if>
            <if test="accommodationType != null">
                accommodation_type,
            </if>
            <if test="isAccommodationSmoke != null">
                is_accommodation_smoke,
            </if>
            <if test="isAccommodationPet != null">
                is_accommodation_pet,
            </if>
            <if test="isAccommodationChildren != null">
                is_accommodation_children,
            </if>
            <if test="isAccommodationFood != null">
                is_accommodation_food,
            </if>
            <if test="accommodationFoodNote != null">
                accommodation_food_note,
            </if>
            <if test="isAccommodationOther != null">
                is_accommodation_other,
            </if>
            <if test="accommodationOtherNote != null">
                accommodation_other_note,
            </if>
            <if test="isNeedPickUp != null">
                is_need_pick_up,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkAgentId != null">
                #{fkAgentId,jdbcType=BIGINT},
            </if>
            <if test="nameZh != null">
                #{nameZh,jdbcType=VARCHAR},
            </if>
            <if test="nameEn != null">
                #{nameEn,jdbcType=VARCHAR},
            </if>
            <if test="lastName != null">
                #{lastName,jdbcType=VARCHAR},
            </if>
            <if test="firstName != null">
                #{firstName,jdbcType=VARCHAR},
            </if>
            <if test="phoneRegion != null">
                #{phoneRegion,jdbcType=VARCHAR},
            </if>
            <if test="phoneNumber != null">
                #{phoneNumber,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="emailPassword != null">
                #{emailPassword,jdbcType=VARCHAR},
            </if>
            <if test="fkStudentAppCountryIdFrom != null">
                #{fkStudentAppCountryIdFrom,jdbcType=VARCHAR},
            </if>
            <if test="fkAreaStateIdFrom != null">
                #{fkAreaStateIdFrom,jdbcType=VARCHAR},
            </if>
            <if test="fkAreaCityIdFrom != null">
                #{fkAreaCityIdFrom,jdbcType=VARCHAR},
            </if>
            <if test="fkAreaCityDivisionIdFrom != null">
                #{fkAreaCityDivisionIdFrom,jdbcType=VARCHAR},
            </if>
            <if test="contactAddress1 != null">
                #{contactAddress1,jdbcType=VARCHAR},
            </if>
            <if test="contactAddress2 != null">
                #{contactAddress2,jdbcType=VARCHAR},
            </if>
            <if test="contactPostcode != null">
                #{contactPostcode,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                #{birthday,jdbcType=DATE},
            </if>
            <if test="gender != null">
                #{gender,jdbcType=INTEGER},
            </if>
            <if test="title != null">
                #{title,jdbcType=INTEGER},
            </if>
            <if test="fkStudentAppCountryId != null">
                #{fkStudentAppCountryId,jdbcType=BIGINT},
            </if>
            <if test="fkStudentAppAncestryId != null">
                #{fkStudentAppAncestryId,jdbcType=BIGINT},
            </if>
            <if test="fkStudentAppMotherLanguageId != null">
                #{fkStudentAppMotherLanguageId,jdbcType=BIGINT},
            </if>
            <if test="fkStudentAppCountryIdBirth != null">
                #{fkStudentAppCountryIdBirth,jdbcType=BIGINT},
            </if>
            <if test="cityBirth != null">
                #{cityBirth,jdbcType=VARCHAR},
            </if>
            <if test="isLivedSinceBirth != null">
                #{isLivedSinceBirth,jdbcType=BIT},
            </if>
            <if test="fkStudentAppCountryIdLive != null">
                #{fkStudentAppCountryIdLive,jdbcType=BIT},
            </if>
            <if test="migrationTime != null">
                #{migrationTime,jdbcType=DATE},
            </if>
            <if test="stayingRightTime != null">
                #{stayingRightTime,jdbcType=DATE},
            </if>
            <if test="fkStudentAppCountryIdPassport != null">
                #{fkStudentAppCountryIdPassport,jdbcType=BIGINT},
            </if>
            <if test="isHavePassport != null">
                #{isHavePassport,jdbcType=BIT},
            </if>
            <if test="passportIssueDate != null">
                #{passportIssueDate,jdbcType=DATE},
            </if>
            <if test="passportIssueExpried != null">
                #{passportIssueExpried,jdbcType=DATE},
            </if>
            <if test="passportNumber != null">
                #{passportNumber,jdbcType=VARCHAR},
            </if>
            <if test="followUpPhoneCode1 != null">
                #{followUpPhoneCode1,jdbcType=VARCHAR},
            </if>
            <if test="followUpPhoneCode2 != null">
                #{followUpPhoneCode2,jdbcType=VARCHAR},
            </if>
            <if test="followUpPhoneCode3 != null">
                #{followUpPhoneCode3,jdbcType=VARCHAR},
            </if>
            <if test="followUpPhoneNumber1 != null">
                #{followUpPhoneNumber1,jdbcType=VARCHAR},
            </if>
            <if test="followUpPhoneNumber2 != null">
                #{followUpPhoneNumber2,jdbcType=VARCHAR},
            </if>
            <if test="followUpPhoneNumber3 != null">
                #{followUpPhoneNumber3,jdbcType=VARCHAR},
            </if>
            <if test="followUpEmail1 != null">
                #{followUpEmail1,jdbcType=VARCHAR},
            </if>
            <if test="followUpEmail2 != null">
                #{followUpEmail2,jdbcType=VARCHAR},
            </if>
            <if test="followUpEmail3 != null">
                #{followUpEmail3,jdbcType=VARCHAR},
            </if>
            <if test="fatherLastName != null">
                #{fatherLastName,jdbcType=VARCHAR},
            </if>
            <if test="fatherFirstName != null">
                #{fatherFirstName,jdbcType=VARCHAR},
            </if>
            <if test="fatherEmail != null">
                #{fatherEmail,jdbcType=VARCHAR},
            </if>
            <if test="fatherPhoneNumber != null">
                #{fatherPhoneNumber,jdbcType=VARCHAR},
            </if>
            <if test="monterLastName != null">
                #{monterLastName,jdbcType=VARCHAR},
            </if>
            <if test="monterFirstName != null">
                #{monterFirstName,jdbcType=VARCHAR},
            </if>
            <if test="monterEmail != null">
                #{monterEmail,jdbcType=VARCHAR},
            </if>
            <if test="monterPhoneNumber != null">
                #{monterPhoneNumber,jdbcType=VARCHAR},
            </if>
            <if test="parentContactPostcode != null">
                #{parentContactPostcode,jdbcType=VARCHAR},
            </if>
            <if test="parentContactAddress1 != null">
                #{parentContactAddress1,jdbcType=VARCHAR},
            </if>
            <if test="parentContactAddress2 != null">
                #{parentContactAddress2,jdbcType=VARCHAR},
            </if>
            <if test="fkStudentAppCountryIdParent != null">
                #{fkStudentAppCountryIdParent,jdbcType=VARCHAR},
            </if>
            <if test="fkAreaStateIdParent != null">
                #{fkAreaStateIdParent,jdbcType=VARCHAR},
            </if>
            <if test="fkAreaCityIdParent != null">
                #{fkAreaCityIdParent,jdbcType=VARCHAR},
            </if>
            <if test="isNeedAccommodation != null">
                #{isNeedAccommodation,jdbcType=BIT},
            </if>
            <if test="accommodationType != null">
                #{accommodationType,jdbcType=INTEGER},
            </if>
            <if test="isAccommodationSmoke != null">
                #{isAccommodationSmoke,jdbcType=BIT},
            </if>
            <if test="isAccommodationPet != null">
                #{isAccommodationPet,jdbcType=BIT},
            </if>
            <if test="isAccommodationChildren != null">
                #{isAccommodationChildren,jdbcType=BIT},
            </if>
            <if test="isAccommodationFood != null">
                #{isAccommodationFood,jdbcType=BIT},
            </if>
            <if test="accommodationFoodNote != null">
                #{accommodationFoodNote,jdbcType=VARCHAR},
            </if>
            <if test="isAccommodationOther != null">
                #{isAccommodationOther,jdbcType=BIT},
            </if>
            <if test="accommodationOtherNote != null">
                #{accommodationOtherNote,jdbcType=VARCHAR},
            </if>
            <if test="isNeedPickUp != null">
                #{isNeedPickUp,jdbcType=BIT},
            </if>
            <if test="status !=null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>


</mapper>