package com.get.gateway.filter;

import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 接口鉴权认证
 * Created by <PERSON><PERSON>xie.
 * Time: 9:39
 * Date: 2025/7/5
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RefreshScope
public class SignatureVerificationFilter implements GlobalFilter, Ordered {

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse resp = exchange.getResponse();

        if (exchange.getRequest().getHeaders().get("X-Sign") != null && !exchange.getRequest().getHeaders().get("X-Sign").isEmpty()) {
            return verifySignature(exchange, resp)
                    .flatMap(valid -> {
                        if (valid) {
                            return chain.filter(exchange);
                        } else {
                            return unAuth(resp, "无效的签名");
                        }
                    });
        } else {
            return chain.filter(exchange);
        }
    }

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    //签名秘钥
    @Value(value = "${defautauth.secretKey}")
    private String secretKey;

    /**
     * 校验签名
     *
     * @param exchange
     * @return
     */
    public Mono<Boolean> verifySignature(ServerWebExchange exchange, ServerHttpResponse resp) {
        //签名
        String sign = exchange.getRequest().getHeaders().get("X-Sign").get(0);
        //随机数
        String nonce = exchange.getRequest().getHeaders().get("X-Nonce").get(0);
        //时间戳
        String timestampStr = exchange.getRequest().getHeaders().get("X-Timestamp").get(0);
        if (StringUtils.isBlank(sign) || StringUtils.isBlank(nonce) || StringUtils.isBlank(timestampStr)) {
            log.error("sign:" + "参数错误");
            return Mono.just(false);
        }

        //timestamp 10分钟内有效
        long timestamp = Long.parseLong(timestampStr);
        long currentTimestamp = System.currentTimeMillis() / 1000;
        if (Math.abs(currentTimestamp - timestamp) > 600) {
            log.error("sign:" + "请求已过期");
            return Mono.just(false);
        }

        //防止请求重放，有效期 20分钟
        String nonceKey = "SignatureVerificationFilter:nonce:" + nonce;
        if (!this.redisTemplate.opsForValue().setIfAbsent(nonceKey, "1", 20, TimeUnit.MINUTES)) {
            log.error("sign:" + "nonce无效");
            return Mono.just(false);
        }

        return exchange.getRequest().getBody()
                .collectList()
                .flatMap(list -> {
                    String body = list.stream()
                            .map(buffer -> StandardCharsets.UTF_8.decode(buffer.asByteBuffer()).toString())
                            .collect(Collectors.joining());

                    String data = String.format("%s%s%s%s", this.secretKey, nonce, timestampStr, body);
                    if (!DigestUtil.md5Hex(data).equals(sign)) {
                        log.error("sign:" + "签名有误");
                        return Mono.just(false);
                    }

                    return Mono.just(true);
                });
    }

    private Mono<Void> unAuth(ServerHttpResponse resp, String message) {
        resp.setStatusCode(HttpStatus.UNAUTHORIZED);
        resp.getHeaders().add("Content-Type", "application/json");
        return resp.writeWith(Mono.just(resp.bufferFactory()
                .wrap(("{\"error\":\"" + message + "\"}").getBytes())));
    }

    @Override
    public int getOrder() {
        return -2100;
    }
}
