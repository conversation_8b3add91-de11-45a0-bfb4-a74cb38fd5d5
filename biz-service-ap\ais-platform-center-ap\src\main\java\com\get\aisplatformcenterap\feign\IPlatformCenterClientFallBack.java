package com.get.aisplatformcenterap.feign;

import com.get.aisplatformcenterap.dto.GetPermissionMenuDto;
import com.get.aisplatformcenterap.dto.ReleaseInfoAndItemDto;
import com.get.aisplatformcenterap.dto.ReleaseInfoSearchDto;
import com.get.aisplatformcenterap.dto.UserScopedDataDto;
import com.get.aisplatformcenterap.vo.LabelSearchAboutAgentVo;
import com.get.aisplatformcenterap.vo.MenuTreeVo;
import com.get.aisplatformcenterap.vo.PlatFormTypeVo;
import com.get.aisplatformcenterap.vo.ReleaseInfoAndItemVo;
import com.get.aisplatformcenterap.vo.ReleaseInfoAndItemVos;
import com.get.aisplatformcenterap.vo.ReleaseInfoItemVo;
import com.get.aisplatformcenterap.vo.UserInfoVo;
import com.get.common.result.SearchBean;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.salecenter.dto.AgentLabelDto;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.stereotype.Component;

/**
 * Feign失败配置
 */
@Component
@VerifyPermission(IsVerify = false)
public class IPlatformCenterClientFallBack implements IPlatformCenterClient{

    @Override
    public Result<Set<Long>> getUserIdsByParam(String userName, Long fkAreaCityId, String bdName) {
        return Result.fail("数据获取失败");
    }

    @Override
    public Result<Map<Long, String>> getUserNickNamesByUserIds(Set<Long> userIds) {
        return Result.fail("数据获取失败");
    }

    @Override
    public Result<Map<Long, String>> getCityNamesByUserIds(Set<Long> userIds) {
        return Result.fail("数据获取失败");
    }

    @Override
    public Result<Map<Long, String>> getMobileByUserIds(Set<Long> userIds) {
        return Result.fail("数据获取失败");
    }

    @Override
    public Result<Set<Long>> getUserIdsByNameOrMobile(String userName, String phoneNumber) {
        return Result.fail("数据获取失败");
    }

    @Override
    public Result<Map<Long, UserInfoVo>> getUserInfoDtoByIds(Set<Long> userIds) {
        return Result.fail("数据获取失败");
    }

    @Override
    public Result<LabelSearchAboutAgentVo> getLabelByLabelTypeIdAndKeyWord(SearchBean<AgentLabelDto> page) {
        return null;
    }

    @Override
    public Result<List<PlatFormTypeVo>> getPlatformTypeDropDown() {
        return null;
    }

    @Override
    public Result getReleaseInfoAndPage(SearchBean<ReleaseInfoSearchDto> page) {
        return null;
    }

    @Override
    public Result insertReleaseInfo(ReleaseInfoAndItemDto releaseInfoAndItemDto) {
        return Result.fail("添加失败");
    }

    @Override
    public Result deleteReleaseInfo(Long id) {
        return Result.fail("删除失败");
    }

    @Override
    public Result<ReleaseInfoAndItemVo> getDetailedInformationById(Long id) {
        return null;
    }

    @Override
    public Result updateReleaseInfo(ReleaseInfoAndItemDto releaseInfoAndItemDto) {
        return Result.fail("编辑失败");
    }

    @Override
    public Result updateReleaseInfoStatus(ReleaseInfoAndItemDto releaseInfoAndItemDto) {
        return Result.fail("状态修改失败");
    }

    @Override
    public Result<ReleaseInfoAndItemVos> getUserListByResourceKeysAndPage(SearchBean<UserScopedDataDto> page) {
        return null;
    }

    @Override
    public Result<List<ReleaseInfoAndItemVo>> getUserListByResourceKeys(UserScopedDataDto userScopedDataDto) {
        return null;
    }

    @Override
    public Result<List<ReleaseInfoItemVo>> getReleaseInfoItemByReleaseInfoIdAndResourceKeys(UserScopedDataDto userScopedDataDto) {
        return null;
    }

    @Override
    public Result<List<MenuTreeVo>> getPartnerPermissionMenu(GetPermissionMenuDto getPermissionMenuDto) {
        return null;
    }
}
