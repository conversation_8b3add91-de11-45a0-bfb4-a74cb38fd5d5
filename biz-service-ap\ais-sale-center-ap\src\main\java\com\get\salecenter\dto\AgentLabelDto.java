package com.get.salecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AgentLabelDto extends BaseVoEntity {

    @ApiModelProperty(value = "代理Id", required = true)
    private Long fkAgentId;

    @ApiModelProperty(value = "代理Ids")
    private List<Long> fkAgentIds;

    @ApiModelProperty(value = "代理电邮地址")
    private String labelEmail;

    @ApiModelProperty(value = "代理电邮地址")
    private List<String> labelEmails;

    @ApiModelProperty(value = "标签Ids", required = true)
    private List<Long> fkLabelIds;

    @ApiModelProperty(value = "标签Id")
    private Long fkLabelId;

    @ApiModelProperty(value = "标签类型")
    private Long labelTypeId;

    @ApiModelProperty(value = "标签描述")
    private String labelRemark;

    @ApiModelProperty(value = "标签关键字")
    private String labelKeyWord;

}
