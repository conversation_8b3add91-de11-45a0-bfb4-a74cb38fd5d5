package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.ContactPersonCompanyMapper;
import com.get.institutioncenter.vo.CompanyTreeVo;
import com.get.institutioncenter.entity.ContactPersonCompany;
import com.get.institutioncenter.service.IContactPersonCompanyService;
import com.get.institutioncenter.dto.ContactPersonCompanyDto;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/11/6
 * @TIME: 17:55
 * @Description:
 **/

@Service
public class ContactPersonCompanyServiceImpl implements IContactPersonCompanyService {
    @Resource
    private ContactPersonCompanyMapper contactPersonCompanyMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private UtilService utilService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editContactPersonCompany(List<ContactPersonCompanyDto> contactPersonCompanyDtos) {
        if (GeneralTool.isEmpty(contactPersonCompanyDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        List<Long> companyIds = SecureUtil.getCompanyIds();
        LambdaQueryWrapper<ContactPersonCompany> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContactPersonCompany::getFkContactPersonId, contactPersonCompanyDtos.get(0).getFkContactPersonId())
                .in(ContactPersonCompany::getFkCompanyId,companyIds);
        contactPersonCompanyMapper.delete(wrapper);

        List<ContactPersonCompany> collect = contactPersonCompanyDtos.stream().map(contactPersonCompanyVo ->
                BeanCopyUtils.objClone(contactPersonCompanyVo, ContactPersonCompany::new)).collect(Collectors.toList());
        collect.forEach(contactPersonCompany -> contactPersonCompanyMapper.insertSelective(contactPersonCompany));

    }

    @Override
    public List<CompanyTreeVo> getContactCompanyRelation(Long contactId) {
        if (GeneralTool.isEmpty(contactId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //获取公司
        List<CompanyTreeVo> companyTreeVo = getCompanyTreeDto();
        if (GeneralTool.isEmpty(companyTreeVo)) {
            return null;
        }

        List<ContactPersonCompany> relation = getRelationByContactId(contactId);
        setContactFlag(companyTreeVo, relation);
        return getTreeList(companyTreeVo);

    }

    @Override
    public Long addRelation(ContactPersonCompanyDto relation) {
        ContactPersonCompany personCompany = BeanCopyUtils.objClone(relation, ContactPersonCompany::new);
        utilService.updateUserInfoToEntity(personCompany);
        contactPersonCompanyMapper.insertSelective(personCompany);
        return relation.getId();
    }


    @Override
    public List<Long> getRelationByCompanyId(List<Long> companyId) {
        if (GeneralTool.isEmpty(companyId)) {
            return null;
        }
        LambdaQueryWrapper<ContactPersonCompany> wrapper = new LambdaQueryWrapper();
        wrapper.in(ContactPersonCompany::getFkCompanyId, companyId);
        List<ContactPersonCompany> personCompanies = contactPersonCompanyMapper.selectList(wrapper);
        return personCompanies.stream().map(ContactPersonCompany::getFkContactPersonId).collect(Collectors.toList());
    }

    @Override
    public List<Long> getRelationsByContactId(Long contactPersonId) {
        if (GeneralTool.isEmpty(contactPersonId)) {
            return null;
        }
        List<ContactPersonCompany> relation = getRelationByContactId(contactPersonId);
        return relation.stream().map(ContactPersonCompany::getFkCompanyId).collect(Collectors.toList());
    }

    @Override
    public Map<Long, Set<Long>> getCompanyIdsByContactIds(Set<Long> contactIds) {
        Map<Long, Set<Long>> map = new HashMap<>();
        if (GeneralTool.isEmpty(contactIds)) {
            return map;
        }
        List<Long> companyIds = SecureUtil.getCompanyIds();
        LambdaQueryWrapper<ContactPersonCompany> wrapper = new LambdaQueryWrapper();
        wrapper.in(ContactPersonCompany::getFkContactPersonId, contactIds).in(ContactPersonCompany::getFkCompanyId,companyIds);
        List<ContactPersonCompany> contactPersonCompanies = contactPersonCompanyMapper.selectList(wrapper);
        if (GeneralTool.isEmpty(contactPersonCompanies)) {
            return map;
        }
        for (ContactPersonCompany contactPersonCompany : contactPersonCompanies) {
            //如果集合包含这个合同联系人id,则往原来的数据添加公司id
            if (map.containsKey(contactPersonCompany.getFkContactPersonId())) {
                Set<Long> beforeCompanyIds = map.get(contactPersonCompany.getFkContactPersonId());
                beforeCompanyIds.add(contactPersonCompany.getFkCompanyId());
                map.put(contactPersonCompany.getFkContactPersonId(), beforeCompanyIds);
                continue;
            }
            //如果没有包含,则往里面添加新数据
            Set<Long> companyIdsSet = new HashSet<>();
            companyIdsSet.add(contactPersonCompany.getFkCompanyId());
            map.put(contactPersonCompany.getFkContactPersonId(), companyIdsSet);
        }
        return map;
    }

    private List<CompanyTreeVo> getCompanyTreeDto() {
        Result<List<com.get.permissioncenter.vo.tree.CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        return BeanCopyUtils.copyListProperties(result.getData(), CompanyTreeVo::new, (sourceDO, targetVO) ->{
            // 这里可以定义特定的转换规则
            if(GeneralTool.isNotEmpty(sourceDO.getId()))
            {
                targetVO.setId(sourceDO.getId());
            }
        });
    }

    private List<ContactPersonCompany> getRelationByContactId(Long contactId) {
        LambdaQueryWrapper<ContactPersonCompany> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContactPersonCompany::getFkContactPersonId, contactId);
        List<ContactPersonCompany> contactPersonCompanies = contactPersonCompanyMapper.selectList(wrapper);
        return contactPersonCompanies;
    }

    private void setContactFlag(List<CompanyTreeVo> companyTreeVo, List<ContactPersonCompany> relation) {
        for (CompanyTreeVo treeDto : companyTreeVo) {
            for (ContactPersonCompany contactCompany : relation) {
                if (treeDto.getId().equals(String.valueOf(contactCompany.getFkCompanyId()))) {
                    treeDto.setFlag(true);
                }
            }
        }
    }

    private List<CompanyTreeVo> getTreeList(List<CompanyTreeVo> entityList) {
        List<CompanyTreeVo> resultList = new ArrayList<>();
        // 获取顶层元素集合
        String parentId;
        for (CompanyTreeVo entity : entityList) {
            parentId = String.valueOf(entity.getFkParentCompanyId());
            if (parentId == null || "0".equals(parentId)) {
                //获取父节点的部门信息
                resultList.add(entity);
            }
        }
        //假如没有父节点
        if (GeneralTool.isEmpty(resultList)) {
            //获取最小节点
            CompanyTreeVo minTreeNode = entityList.stream().min(Comparator.comparing(CompanyTreeVo::getFkParentCompanyId)).get();
            resultList.add(minTreeNode);
            if (GeneralTool.isNotEmpty(minTreeNode)) {
                //获取相同的最小节点
                List<CompanyTreeVo> minTreeNodes = entityList.stream().filter(treeDto ->
                        treeDto.getFkParentCompanyId().equals(minTreeNode.getFkParentCompanyId()) &&
                                !treeDto.getId().equals(minTreeNode.getId())).distinct().collect(Collectors.toList());
                resultList.addAll(minTreeNodes);
            }
        }
        // 获取每个顶层元素的子数据集合
        for (CompanyTreeVo entity : resultList) {
            entity.setChildList(getSubList(entity.getId(), entityList));
        }
        return resultList;
    }

    private List<CompanyTreeVo> getSubList(Long id, List<CompanyTreeVo> entityList) {
        List<CompanyTreeVo> childList = new ArrayList<>();
        Long parentId;
        // 子集的直接子对象
        for (CompanyTreeVo entity : entityList) {
            parentId = entity.getFkParentCompanyId();
            if (id.equals(parentId)) {
                //获取子节点的部门信息
                childList.add(entity);
            }
        }
        // 子集的间接子对象
        for (CompanyTreeVo entity : childList) {
            //递归调用
            entity.setChildList(getSubList(entity.getId(), entityList));
        }
        // 递归退出条件
        if (childList.size() == 0) {
            return null;
        }
        return childList;
    }
}
