package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2020/11/24
 * @TIME: 14:38
 * @Description:
 **/
@Data
public class StudentRoleAndStaffVo {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 角色名称
     */
    @ApiModelProperty(value = "角色名称")
    private String roleName;

    /**
     * 员工名称
     */
    @ApiModelProperty(value = "员工名称")
    private String staffName;

    /**
     * 角色viewOrder
     */
    @ApiModelProperty(value = "角色viewOrder")
    private Integer studentProjectRoleViewOrder;

    /**
     * fkTableId
     */
    @ApiModelProperty(value = "fkTableId")
    private Long fkTableId;

    /**
     * 学生项目角色Id
     */
    @ApiModelProperty(value = "学生项目角色Id")
    private Long fkStudentProjectRoleId;

    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;
}
