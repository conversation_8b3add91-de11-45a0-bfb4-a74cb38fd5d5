package com.get.pmpcenter.vo.institution;

import com.get.pmpcenter.vo.common.MediaVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/12  16:47
 * @Version 1.0
 * @Description:合同佣金方案列表
 */
@Data
public class ProviderCommissionPlanListVo {

    @ApiModelProperty(value = "佣金方案列表")
    private List<ProviderCommissionPlanVo> commissionPlanList;

    @ApiModelProperty(value = "佣金方案附件列表")
    private List<MediaVo> commissionPlanMediaList;

    public ProviderCommissionPlanListVo() {
        this.commissionPlanList = new ArrayList<>();
        this.commissionPlanMediaList = new ArrayList<>();
    }
}
