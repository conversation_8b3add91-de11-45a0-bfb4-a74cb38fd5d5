package com.get.salecenter.service.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.ConventionTableRegistrationMapper;
import com.get.salecenter.vo.ConventionRegistrationVo;
import com.get.salecenter.vo.ConventionTableRegistrationVo;
import com.get.salecenter.entity.ConventionTableRegistration;
import com.get.salecenter.service.IConventionRegistrationService;
import com.get.salecenter.service.IConventionTableRegistrationService;
import com.get.salecenter.dto.ConventionTableRegistrationDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/31 11:40
 * @verison: 1.0
 * @description:
 */
@Service
public class ConventionTableRegistrationServiceImpl implements IConventionTableRegistrationService {

    @Resource
    private ConventionTableRegistrationMapper conventionTableRegistrationMapper;

    @Resource
    private IConventionRegistrationService conventionRegistrationService;

    @Resource
    private UtilService utilService;

    @Override
    public void configurationTable(ConventionTableRegistrationDto conventionTableRegistrationDto) {
        if (GeneralTool.isEmpty(conventionTableRegistrationDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ConventionTableRegistration conventionTableRegistration = BeanCopyUtils.objClone(conventionTableRegistrationDto, ConventionTableRegistration::new);
        //当id存在时，表示修改 否则表示新增
        if (GeneralTool.isNotEmpty(conventionTableRegistrationDto.getId())) {
            //当报名id为空时，表示删除这一安排
            if (GeneralTool.isEmpty(conventionTableRegistrationDto.getFkConventionRegistrationId())) {
                delete(conventionTableRegistrationDto.getId());
            } else {
                update(conventionTableRegistrationDto);
            }
        } else {
            //验证是否已被安排
//            Example example = new Example(ConventionTableRegistration.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andEqualTo("fkConventionTableId", conventionTableRegistration.getFkConventionTableId());
//            List<ConventionTableRegistration> conventionTableRegistrations = conventionTableRegistrationMapper.selectByExample(example);
            List<ConventionTableRegistration> conventionTableRegistrations = conventionTableRegistrationMapper.selectList(Wrappers.<ConventionTableRegistration>lambdaQuery().eq(ConventionTableRegistration::getFkConventionTableId, conventionTableRegistration.getFkConventionTableId()));
            if (GeneralTool.isNotEmpty(conventionTableRegistrations)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("table_arrange_refresh"));
            }
            utilService.updateUserInfoToEntity(conventionTableRegistration);
            conventionTableRegistrationMapper.insertSelective(conventionTableRegistration);
        }
    }

    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (conventionTableRegistrationMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        conventionTableRegistrationMapper.deleteById(id);
    }

    public void update(ConventionTableRegistrationDto conventionTableRegistrationDto) {
        if (conventionTableRegistrationDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ConventionTableRegistration result = conventionTableRegistrationMapper.selectById(conventionTableRegistrationDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ConventionTableRegistration conventionTableRegistration = BeanCopyUtils.objClone(conventionTableRegistrationDto, ConventionTableRegistration::new);
        utilService.updateUserInfoToEntity(conventionTableRegistration);
        conventionTableRegistrationMapper.updateById(conventionTableRegistration);
    }

    @Override
    public ConventionTableRegistrationVo getRegistrationByTableId(Long id) {
//        Example example = new Example(ConventionTableRegistration.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionTableId", id);
//        ConventionTableRegistrationVo conventionTableRegistrationVo = null;
//        List<ConventionTableRegistration> conventionTableRegistrations = conventionTableRegistrationMapper.selectByExample(example);
        ConventionTableRegistrationVo conventionTableRegistrationVo = null;
        List<ConventionTableRegistration> conventionTableRegistrations = conventionTableRegistrationMapper.selectList(Wrappers.<ConventionTableRegistration>lambdaQuery().eq(ConventionTableRegistration::getFkConventionTableId, id));

        if (GeneralTool.isNotEmpty(conventionTableRegistrations)) {
            ConventionTableRegistration conventionTableRegistration = conventionTableRegistrations.get(0);
            conventionTableRegistrationVo = BeanCopyUtils.objClone(conventionTableRegistration, ConventionTableRegistrationVo::new);
            //通过峰会报名id 查找峰会报名详情
            ConventionRegistrationVo registrationDto = conventionRegistrationService.findConventionRegistrationById(conventionTableRegistration.getFkConventionRegistrationId());
            conventionTableRegistrationVo.setConventionRegistrationDto(registrationDto);
        }
        return conventionTableRegistrationVo;
    }
}
