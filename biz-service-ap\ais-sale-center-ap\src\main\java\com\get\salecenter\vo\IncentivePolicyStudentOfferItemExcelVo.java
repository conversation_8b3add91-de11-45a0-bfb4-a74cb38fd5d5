package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-03-13
 */
@Data
public class IncentivePolicyStudentOfferItemExcelVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "奖励政策编号")
    private String incentivePolicyNum;


    @ApiModelProperty(value = "统计类型：0人工剔除/1人工计入/2系统计入")
    private Integer countType;

    @ApiModelProperty(value = "财务状态：0未结算/1已结算")
    private Integer financeStatus;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 申请步骤状态
     */
    @ApiModelProperty("申请步骤状态")
    private String stepName;

    /**
     * 一个学生申请方案绑定多个代理？
     */
    @ApiModelProperty(value = "绑定代理名称")
    private String agentName;

    @ApiModelProperty(value = "学生姓名（中/英）")
    private String studentName;

    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String countryName;

    /**
     * 学校提供商名称
     */
    @ApiModelProperty(value = "学校提供商名称")
    private String institutionProviderName;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String institutionName;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称")
    private String institutionCourseName;

    @ApiModelProperty("最终开学时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deferOpeningTime;

    @ApiModelProperty("创建时间（方案创建时间）")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date offerItemGmtCreate;
}
