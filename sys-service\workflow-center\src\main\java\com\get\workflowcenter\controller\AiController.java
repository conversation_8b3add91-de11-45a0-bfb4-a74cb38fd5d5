package com.get.workflowcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.workflowcenter.service.IAiService;
import com.get.workflowcenter.vo.AiApprovalRecordListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "Ai管理")
@RestController
@RequestMapping("workflow/ai")
public class AiController {
    @Resource
    private IAiService aiService;

    // 查询所有自己的审批信息
    @ApiOperation(value = "查询所有自己的审批信息", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/Ai管理/查询所有自己的审批信息")
    @VerifyPermission(IsVerify = false)
    @PostMapping("getMyApproval")
    //           @RequestBody AiApprovalQueryParam aiApprovalQueryParam,
    public ResponseBo<List<AiApprovalRecordListVo>> getMyApproval(@RequestHeader("at") String token) {
        return new ResponseBo<>(aiService.getMyApproval());
    }

}
