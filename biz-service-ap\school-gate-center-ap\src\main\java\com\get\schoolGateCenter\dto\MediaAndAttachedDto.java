package com.get.schoolGateCenter.dto;

import com.get.schoolGateCenter.entity.MediaAndAttached;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Sea
 * @create: 2020/8/14 10:10
 * @verison: 1.0
 * @description:
 */
@Data
public class MediaAndAttachedDto extends MediaAndAttached {

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    @ApiModelProperty(value = "文件临时路径，超过6小时国企")
    private String filePathUrl;

    /**
     * 源文件名
     */
    @ApiModelProperty(value = "源文件名")
    private String fileNameOrc;

    private String typeValue;
    /**
     * 文件key
     */
    @ApiModelProperty(value = "文件key")
    private String fileKey;


    @ApiModelProperty(value = "是否App项目的文件")
    private Boolean isAppFile;
}
