package com.get.aisplatformcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.aisplatformcenterap.dto.DeletePatchParamsDto;
import com.get.aisplatformcenterap.vo.MBannerVo;
import com.get.aisplatformcenterap.entity.MBannerEntity;
import com.get.aisplatformcenterap.dto.BannerPutAwayParamsDto;
import com.get.aisplatformcenterap.dto.MBannerParamsDto;
import com.get.common.result.Page;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_banner(banner后台管理)】的数据库操作Service
* @createDate 2024-12-16 18:03:59
*/
public interface MBannerService extends IService<MBannerEntity> {
    /**
     * banner列表查询
     * @param params
     * @param page
     * @return
     */
    List<MBannerVo> searchBannerPage(MBannerParamsDto params, Page page);

    /**
     * 消息查询
     * @param entity
     * @return
     */
    Long saveOrUpdateMessage(MBannerParamsDto entity);

    /**
     * banner 上架下架
     * @param vo
     * @return
     */
    Long putAwayBanner(BannerPutAwayParamsDto vo);
    Long deleteOne(Long id);

    void deleteBatch(DeletePatchParamsDto patchVo);

    /**
     * banner明细
     * @param id
     * @return
     */
    MBannerVo  getDetail(Long id);
}
