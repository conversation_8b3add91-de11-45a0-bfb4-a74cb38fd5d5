package com.get.aisplatformcenter.controller;

import com.get.aisplatformcenter.service.ULabelService;
import com.get.aisplatformcenterap.dto.ULabelDto;
import com.get.aisplatformcenterap.vo.LabelAboutAgentVo;
import com.get.aisplatformcenterap.vo.LabelSearchAboutAgentVo;
import com.get.aisplatformcenterap.vo.ULabelVo;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.dto.AgentLabelDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


@Api(tags = "标签管理")
@RestController
@RequestMapping("platform/label")
public class ULabelController {

    @Resource
    private ULabelService uLabelService;

    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.LIST, description = "业务平台配置中心/标签管理/列表查询")
    @PostMapping("datas")
    public ResponseBo<ULabelVo> searchPage(@RequestBody SearchBean<ULabelDto> page){
        List<ULabelVo> datas=uLabelService.searchPage(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    @ApiOperation(value = "新增标签管理", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.ADD, description = "业务平台配置中心/标签管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody  ULabelDto uLabelDto) {
        return SaveResponseBo.ok(uLabelService.addULabelType(uLabelDto));
    }


    @ApiOperation(value = "更新标签管理", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.EDIT, description = "业务平台配置中心/标签管理/更新")
    @PostMapping("update")
    public ResponseBo<ULabelVo> update(@RequestBody  ULabelDto uLabelDto) {
        return UpdateResponseBo.ok(uLabelService.updateULabel(uLabelDto));
    }

    /**
     * 删除用户信息管理
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除标签管理", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DELETE, description = "业务平台配置中心/标签管理/更新")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        uLabelService.delete(id);
        return DeleteResponseBo.ok();
    }

//
//    @ApiOperation(value = "批量删除标签管理", notes = "id为此条数据的id")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DELETE, description = "业务平台配置中心/标签类型管理/批量删除标签管理")
//    @PostMapping("batchDelete")
//    public ResponseBo batchDelete(@RequestBody Set<Long> uLabelTypes) {
//        uLabelService.batchDelete(uLabelTypes);
//        return DeleteResponseBo.ok();
//    }

    @ApiOperation(value = "排序接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.EDIT, description = "业务平台配置中心/标签管理/排序")
    @PostMapping("sortULabel")
    public ResponseBo sortULabel(@RequestBody List<ULabelDto> uLabelDtoList) {
        uLabelService.sortULabel(uLabelDtoList);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "标签管理详情", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DETAIL, description = "业务平台配置中心/标签管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<ULabelVo> detail(@PathVariable("id") Long id) {
        return new ResponseBo<>(uLabelService.findULabelById(id));
    }



    @ApiOperation(value = "标签列表", notes = "代理详情添加列表")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.LIST, description = "业务平台配置中心/标签类型管理/标签列表")
    @PostMapping("getLabelByLabelTypeIdAndKeyWord")
    public ListResponseBo<LabelAboutAgentVo> getLabelByLabelTypeIdAndKeyWord(@RequestBody SearchBean<AgentLabelDto> page) {
        LabelSearchAboutAgentVo labelByLabelTypeIdAndKeyWord = uLabelService.getLabelByLabelTypeIdAndKeyWord(page.getData(), page);
        return new ListResponseBo<>(labelByLabelTypeIdAndKeyWord.getLabelAboutAgentVoList(), labelByLabelTypeIdAndKeyWord.getPage());
    }


}
