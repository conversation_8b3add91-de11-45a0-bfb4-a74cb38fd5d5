package com.get.pmpcenter.vo.agent;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/6/3
 * @Version 1.0
 * @apiNote:学校下的代理佣金方案
 */
@Data
public class InstitutionAgentPlanVo {

    @ApiModelProperty(value = "学校提供商Id")
    private Long institutionProviderId;

    @ApiModelProperty(value = "学校提供商名称")
    private String institutionProviderName;

    @ApiModelProperty(value = "代理方案ID")
    private Long planId;

    @ApiModelProperty(value = "代理方案名称")
    private String planName;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "是否激活：0否/1是")
    private Integer isActive;

    @ApiModelProperty(value = "方案有效时间（开始）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "方案有效时间（结束）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "有效时间是否无时间限制：0否/1是")
    private Integer isTimeless;

    @ApiModelProperty(value = "是否锁定：0否/1是")
    private Integer isLocked;

    @ApiModelProperty(value = "代理佣金分类Id")
    private Long fkAgentCommissionTypeId;

    @ApiModelProperty(value = "代理佣金分类名称")
    private String agentCommissionTypeName;
}
