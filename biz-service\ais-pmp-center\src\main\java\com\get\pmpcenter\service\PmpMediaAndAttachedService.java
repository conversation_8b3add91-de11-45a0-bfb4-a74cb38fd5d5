package com.get.pmpcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.filecenter.dto.FileDto;
import com.get.pmpcenter.dto.common.MediaDto;
import com.get.pmpcenter.dto.common.UpdateMediaFileNameDto;
import com.get.pmpcenter.entity.PmpMediaAndAttached;
import com.get.pmpcenter.vo.common.MediaVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:17
 * @Version 1.0
 */
public interface PmpMediaAndAttachedService extends IService<PmpMediaAndAttached> {

    /**
     * 根据表名和表id获取媒体列表
     *
     * @param tableName
     * @param tableIds
     * @return
     */
    List<MediaVo> getMediaList(String tableName, List<Long> tableIds);

    /**
     * 删除附件
     *
     * @param id
     */
    void delMedia(Long id);

    /**
     * 保存媒体附件信息
     *
     * @param tableName
     * @param tableId
     * @param mediaDtoList
     */
    void saveMedia(String tableName, Long tableId, List<MediaDto> mediaDtoList);

    /**
     * 上传附件
     * @param multipartFiles
     * @return
     */
    List<FileDto> uploadAttached(MultipartFile[] multipartFiles);

    /**
     * 上传合同附件
     * @param multipartFiles
     * @param tableId
     * @return
     */
    List<MediaVo> uploadContractAttached(MultipartFile[] multipartFiles, Long tableId, Date gmtCreate,String remark);

    /**
     * 下载附件
     * @param id
     */
    void downloadFile(HttpServletResponse response,Long id);


    /**
     * 修改文件名
     * @param updateMediaFileNameDto
     */
    void updateMediaFileName(UpdateMediaFileNameDto updateMediaFileNameDto);
}
