package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.service.IConventionExamPrizesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Api(tags = "峰会培训考试奖品")
@RestController
@RequestMapping("sale/conventionExamPrizes")
public class ConventionExamPrizesController {


    @Resource
    private IConventionExamPrizesService conventionExamPrizesService;

    /**
     * 列表数据
     *
     * @param
     * @return
     * @
     */
/*    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/兑奖管理/查询兑奖列表")
    @PostMapping("datas")
    public ResponseBo datas(@RequestBody SearchBean<ConventionExamPrizesDto> page) {
        List<ConventionExamPrizesVo> datas = conventionExamPrizesService.getDatas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }*/


    @ApiOperation(value = "设置兑奖或取消兑奖", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/兑奖管理/设置兑奖或取消兑奖")
    @PostMapping("updateExamPrizesExchange")
    public ResponseBo updateExamPrizesExchange(@RequestParam("id") Long id,@RequestParam("isExchange") Boolean isExchange) {
        conventionExamPrizesService.updateExamPrizesExchange(id,isExchange);
        return UpdateResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "奖品类型下拉框数据", notes = "")
    @PostMapping("findPrizesType")
    public ResponseBo findPrizesType() {
        List<Map<String, Object>> datas = conventionExamPrizesService.findPrizesType();
        return new ListResponseBo<>(datas);
    }

 /*   @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "考试下拉框数据", notes = "")
    @PostMapping("examinationSelectByConventionId")
    public ResponseBo<BaseSelectEntity> examinationSelectByConventionId(@RequestParam("fkConventionId") Long fkConventionId) {
        return new ListResponseBo<>(conventionExamPrizesService.examinationSelectByConventionId(fkConventionId));
    }*/


/*    @ApiOperation(value = "导出列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/留学住宿管理/导出留学住宿列表")
    @PostMapping("/exportExcel")
    @ResponseBody
    public void exportExcel(HttpServletResponse response, @RequestParam("fkConventionId") Long fkConventionId) {
        conventionExamPrizesService.exportExcel(response, fkConventionId);
    }*/




}
