package com.get.permissioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.permissioncenter.dto.AssignBusinessSchoolDto;
import com.get.permissioncenter.dto.BusinessSchoolDto;
import com.get.permissioncenter.vo.AssignBusinessSchoolVo;
import com.get.permissioncenter.vo.BusinessSchoolVo;
import com.get.permissioncenter.entity.StaffInstitution;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
@Mapper
public interface StaffInstitutionMapper extends BaseMapper<StaffInstitution> {

    List<BusinessSchoolVo> getBusinessSchoolList(IPage<BusinessSchoolVo> iPage, @Param("businessSchoolDto") BusinessSchoolDto businessSchoolDto);

    List<AssignBusinessSchoolVo> getAssignBusinessSchoolList(IPage<AssignBusinessSchoolVo> iPage, @Param("assignBusinessSchoolDto") AssignBusinessSchoolDto assignBusinessSchoolDto);
}
