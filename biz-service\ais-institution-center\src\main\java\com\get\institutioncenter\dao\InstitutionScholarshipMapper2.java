package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.institutioncenter.vo.InstitutionScholarshipVo2;
import com.get.institutioncenter.vo.WeInstitutionScholarshipVo;
import com.get.institutioncenter.entity.InstitutionScholarship2;
import com.get.institutioncenter.dto.InstitutionScholarshipDto;
import com.get.institutioncenter.dto.WeScholarshipAppDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface InstitutionScholarshipMapper2 extends BaseMapper<InstitutionScholarship2> {



    List<InstitutionScholarshipVo2> getWcInstitutionScholarshipList(IPage<InstitutionScholarshipVo2> ipage,
                                                                    @Param("schoolName") String schoolName,
                                                                    @Param("fkCountryId") Long fkCountryId);

    InstitutionScholarshipVo2 getIsOtherModule(@Param("fkInstitutionId") Long fkInstitutionId);

    /**
     * 获取奖学金列表信息
     * @param pages
     * @param data
     * @return
     */
    List<InstitutionScholarshipVo2> datas(IPage<InstitutionScholarship2> pages, @Param("data") InstitutionScholarshipDto data);

    /**
     * 小程序奖学金学校详情
     * @param fkTypeKey
     * @param fkTableId
     * @return
     */
    List<WeInstitutionScholarshipVo> wechatDatas(@Param("fkTypeKey") String fkTypeKey, @Param("fkTableId") Long fkTableId);


    InstitutionScholarshipVo2 selectInfoById(Long id);

    List<InstitutionScholarshipVo2> getWcInstitutionScholarshipDatas(@Param("weScholarshipAppDto") WeScholarshipAppDto weScholarshipAppDto, @Param("fkTableName")String fkTableName);

    /**
     * 奖学金优先匹配
     * @param weScholarshipAppDto
     * @param priorityTypeKey
     * @param fkTableName
     * @return
     */
    List<InstitutionScholarshipVo2> priorityMatchingQuery(@Param("weScholarshipAppDto") WeScholarshipAppDto weScholarshipAppDto,
                                                          @Param("priorityTypeKey") Map<Integer,String> priorityTypeKey, @Param("fkTableName")String fkTableName);
}