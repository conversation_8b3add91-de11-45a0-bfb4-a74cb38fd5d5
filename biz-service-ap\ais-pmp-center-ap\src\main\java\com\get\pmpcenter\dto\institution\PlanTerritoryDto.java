package com.get.pmpcenter.dto.institution;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/3/12
 * @Version 1.0
 * @apiNote:适合国家DTO
 */
@Data
public class PlanTerritoryDto {

    @ApiModelProperty(value = "适用国家/区域Id集合")
    private List<Long> countryIds = new ArrayList<>();

    @ApiModelProperty(value = "大区集合")
    private List<Long> regionIds = new ArrayList<>();

    @ApiModelProperty(value = "适用国家/区域规则:1-包括;2-casebycase;3-AU onshore;-1-除外;4-UK_SHORE")
    private Integer isInclude = 1;
}
