package com.get.officecenter.config;

import com.get.officecenter.utils.JsonUtils;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * <AUTHOR>
 * 企业微信（第三方应用）
 */
@Getter
@Setter
@ConfigurationProperties(prefix = "wx.cptp")
public class WxCpTpProperties {

  /**
   * 设置企业微信的corpId
   */
  private String corpId;

  private List<AppConfig>  appConfigs;

  @Getter
  @Setter
  public static class AppConfig {
    /**
     * 设置企业微信应用的suiteId
     */
    private String suiteId;

    /**
     * 设置企业微信应用的Secret
     */
    private String secret;

    /**
     * 设置企业微信应用的token
     */
    private String token;

    /**
     * 设置企业微信应用的EncodingAESKey
     */
    private String aesKey;

  }

  @Override
  public String toString() {
    return JsonUtils.toJson(this);
  }
}
