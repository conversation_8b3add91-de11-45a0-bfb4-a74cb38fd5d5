package com.get.insurancecenter.service;

import org.springframework.web.multipart.MultipartFile;

/**
 * @Author:<PERSON>
 * @Date: 2025/6/19
 * @Version 1.0
 * @apiNote:
 */
public interface TencentCloudService {

    /**
     * @describe 上传文件-私密桶
     * @methods uploadObject 方法名
     * @parameter fileUrl 上传文件地址
     * @parameter fileKey 文件对象名称
     * @parameter @return 对象列表
     */
    Boolean uploadObject(String bucketName, MultipartFile file, String fileKey);

    /**
     * 删除对象
     */
    void deleteObjectRequest(String key, String bucketName);
}
