package com.get.partnercenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.get.partnercenter.vo.BdVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * ais_institution_center
 */
@Mapper
@DS("permissiondb")
public interface PermissionCenterMapper {

    List<BdVo> getStaffList(@Param("staffFollowerIds") List<Long> staffFollowerIds);
}
