package com.get.institutioncenter.utils;

import com.get.common.eunms.TableEnum;
import com.get.common.utils.CommonUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dto.WeScholarshipAppDto;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
public class WeUtils {

    /**
     * 取完整优先级key
     * @param scholarshipAppVo
     * @return
     */
    public static String getPriorityTypeKey(WeScholarshipAppDto scholarshipAppVo){
        List<String> list = new ArrayList<>();
        if (GeneralTool.isNotEmpty(scholarshipAppVo.getCourseId())) {
            list.add(TableEnum.INSTITUTION_COURSE.key);
        }
        if (GeneralTool.isNotEmpty(scholarshipAppVo.getInstitutionId())) {
            list.add(TableEnum.INSTITUTION.key);
        }
        if (GeneralTool.isNotEmpty(scholarshipAppVo.getCountryId())) {
            list.add(TableEnum.INSTITUTION_COUNTRY.key);
        }
        if (GeneralTool.isNotEmpty(list)) {
            list.sort(String::compareTo);
            return String.join(",",list);
        }
        return null;
    }


    public final static String[] PRIORITY_KEY ={TableEnum.INSTITUTION_COURSE.key,TableEnum.INSTITUTION_MAJOR_LEVEL.key,TableEnum.INSTITUTION_COURSE_TYPE.key,TableEnum.INSTITUTION_COURSE_TYPE_GROUP.key,
            TableEnum.INSTITUTION_FACULTY.key,TableEnum.INSTITUTION.key,TableEnum.INSTITUTION_COUNTRY.key};

    /**
     * 获取排序后的优先级key和value
     * @param map
     * @return
     */
    public static String [] getPriorityTypeKey(Map<String,Long> map){
        Map<String,String> mp = new HashMap<>();
        //数据封装
        for (Map.Entry<String, Long> entry : map.entrySet()) {
            String key = entry.getKey();
            Long value = entry.getValue();
            if (TableEnum.INSTITUTION_COURSE.key.equals(key) && GeneralTool.isNotEmpty(value)) {
                mp.put(TableEnum.INSTITUTION_COURSE.key, String.valueOf(value));
            }
            if (TableEnum.INSTITUTION_MAJOR_LEVEL.key.equals(key) && GeneralTool.isNotEmpty(value)) {
                mp.put(TableEnum.INSTITUTION_MAJOR_LEVEL.key, String.valueOf(value));
            }
            if (TableEnum.INSTITUTION_COURSE_TYPE.key.equals(key) && GeneralTool.isNotEmpty(value)) {
                mp.put(TableEnum.INSTITUTION_COURSE_TYPE.key, String.valueOf(value));
            }
            if (TableEnum.INSTITUTION_COURSE_TYPE_GROUP.key.equals(key) && GeneralTool.isNotEmpty(value)) {
                mp.put(TableEnum.INSTITUTION_COURSE_TYPE_GROUP.key, String.valueOf(value));
            }
            if (TableEnum.INSTITUTION_FACULTY.key.equals(key) && GeneralTool.isNotEmpty(value)) {
                mp.put(TableEnum.INSTITUTION_FACULTY.key, String.valueOf(value));
            }
            if (TableEnum.INSTITUTION.key.equals(key) && GeneralTool.isNotEmpty(value)) {
                mp.put(TableEnum.INSTITUTION.key, String.valueOf(value));
            }
            if (TableEnum.INSTITUTION_COUNTRY.key.equals(key) && GeneralTool.isNotEmpty(value)) {
                mp.put(TableEnum.INSTITUTION_COUNTRY.key, String.valueOf(value));
            }
        }
        //对结果进行排序拼接返回数组 m_institution,u_area_country  /  155,7
        if (GeneralTool.isNotEmpty(mp)) {
            Map<String, String> result = CommonUtil.sortMapByKey(mp);
            StringBuilder key = new StringBuilder();
            StringBuilder val = new StringBuilder();
            String [] rs = new String[2];
            for (Map.Entry<String, String> entry : result.entrySet()) {
                key.append(entry.getKey()).append(",");
                val.append(entry.getValue()).append(",");
            }
            String k = key.toString();
            rs[0] = k.endsWith(",")?k.substring(0,k.length()-1):k;
            String v = val.toString();
            rs[1] = v.endsWith(",")?v.substring(0,v.length()-1):v;
            return rs;
        }
        return null;
    }

    /**
     * 按优先级取阶段性key 国家 -》 国家&学校
     * @param scholarshipAppVo
     * @return
     */
    public static Map<Integer,String> getPriorityLevelTypeKey(WeScholarshipAppDto scholarshipAppVo){
        Map<Integer,String> result = new HashMap<>();
        List<String> list = new ArrayList<>();
        if (GeneralTool.isNotEmpty(scholarshipAppVo.getCountryId())) {
            list.add(TableEnum.INSTITUTION_COUNTRY.key);
            result.put(1,String.join(",",list));
        }
        if (GeneralTool.isNotEmpty(scholarshipAppVo.getInstitutionId())) {
            list.add(TableEnum.INSTITUTION.key);
            list.sort(String::compareTo);
            result.put(2,String.join(",",list));
        }
        if (GeneralTool.isEmpty(scholarshipAppVo.getFacultyId())) {
            list.add(TableEnum.INSTITUTION_FACULTY.key);
            list.sort(String::compareTo);
            result.put(4,String.join(",",list));
        }
        if (GeneralTool.isEmpty(scholarshipAppVo.getCourseGroupId())) {
            list.add(TableEnum.INSTITUTION_COURSE_TYPE_GROUP.key);
            list.sort(String::compareTo);
            result.put(5,String.join(",",list));
        }
        if (GeneralTool.isEmpty(scholarshipAppVo.getCourseTypeId())) {
            list.add(TableEnum.INSTITUTION_COURSE_TYPE.key);
            list.sort(String::compareTo);
            result.put(6,String.join(",",list));
        }
        if (GeneralTool.isEmpty(scholarshipAppVo.getCourseLevelId())) {
            list.add(TableEnum.INSTITUTION_MAJOR_LEVEL.key);
            list.sort(String::compareTo);
            result.put(7,String.join(",",list));
        }
        if (GeneralTool.isNotEmpty(scholarshipAppVo.getCourseId())) {
            list.add(TableEnum.INSTITUTION_COURSE.key);
            list.sort(String::compareTo);
            result.put(3,String.join(",",list));
        }
        return result;
    }
}
