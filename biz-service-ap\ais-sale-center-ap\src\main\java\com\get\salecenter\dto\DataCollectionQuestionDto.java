package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 学校机构资料收集表单 考题Dto
 */
@Data
public class DataCollectionQuestionDto {

    @ApiModelProperty(value = "考题Id")
    private Long id;

    @ApiModelProperty(value = "题型：枚举(单选题0/多选题1/判断题2)")
    @NotNull(message = "题型不能为空")
    private Integer questionType;

    @ApiModelProperty(value = "问题内容")
    @NotBlank(message = "问题内容不能为空")
    private String question;

    @ApiModelProperty(value = "答案")
    @NotNull(message = "答案不能为空")
    private List<DataCollectionAnswerDto> dataCollectionAnswerList;

}
