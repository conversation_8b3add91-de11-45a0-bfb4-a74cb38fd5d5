package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.mybatis.base.UtilService;
import com.get.salecenter.dao.sale.AnnualConferenceRegistrationSponsorshipMapper;
import com.get.salecenter.vo.AnnualConferenceRegistrationSponsorshipVo;
import com.get.salecenter.entity.AnnualConferenceRegistrationSponsorship;
import com.get.salecenter.service.IAnnualConferenceRegistrationSponsorshipService;
import com.get.salecenter.service.ISponsorshipConfigService;
import com.get.salecenter.dto.AnnualConferenceRegistrationSponsorshipDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/29 11:20
 * @verison: 1.0
 * @description:
 */
@Service
public class AnnualConferenceRegistrationSponsorshipServiceImpl implements IAnnualConferenceRegistrationSponsorshipService {
    @Resource
    private AnnualConferenceRegistrationSponsorshipMapper annualConferenceRegistrationSponsorshipMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private ISponsorshipConfigService sponsorshipConfigService;

    @Override
    public void addAnnualConferenceRegistrationSponsorship(AnnualConferenceRegistrationSponsorshipDto annualConferenceRegistrationSponsorshipDto) {
        AnnualConferenceRegistrationSponsorship annualConferenceRegistrationSponsorship = BeanCopyUtils.objClone(annualConferenceRegistrationSponsorshipDto, AnnualConferenceRegistrationSponsorship::new);
        utilService.updateUserInfoToEntity(annualConferenceRegistrationSponsorship);
        annualConferenceRegistrationSponsorshipMapper.insertSelective(annualConferenceRegistrationSponsorship);
    }

    @Override
    public void deleteByFkid(Long annualConferenceRegistrationId) {
//        Example example = new Example(AnnualConferenceRegistrationSponsorship.class);
//        example.createCriteria().andEqualTo("fkAnnualConferenceRegistrationId", annualConferenceRegistrationId);
//        annualConferenceRegistrationSponsorshipMapper.deleteByExample(example);
        annualConferenceRegistrationSponsorshipMapper.delete(Wrappers.<AnnualConferenceRegistrationSponsorship>lambdaQuery().eq(AnnualConferenceRegistrationSponsorship::getFkAnnualConferenceRegistrationId, annualConferenceRegistrationId));
    }

    @Override
    public List<AnnualConferenceRegistrationSponsorshipVo> getSponsorshipDto(Long annualConferenceRegistrationId) {
//        Example example = new Example(AnnualConferenceRegistrationSponsorship.class);
//        example.createCriteria().andEqualTo("fkAnnualConferenceRegistrationId", annualConferenceRegistrationId);
//        List<AnnualConferenceRegistrationSponsorship> sponsorships = annualConferenceRegistrationSponsorshipMapper.selectByExample(example);
        List<AnnualConferenceRegistrationSponsorship> sponsorships = annualConferenceRegistrationSponsorshipMapper.selectList(Wrappers.<AnnualConferenceRegistrationSponsorship>lambdaQuery().eq(AnnualConferenceRegistrationSponsorship::getFkAnnualConferenceRegistrationId, annualConferenceRegistrationId));

        List<AnnualConferenceRegistrationSponsorshipVo> convertDatas = new ArrayList<>();
        for (AnnualConferenceRegistrationSponsorship sponsorship : sponsorships) {
            AnnualConferenceRegistrationSponsorshipVo sponsorshipConfigDto = BeanCopyUtils.objClone(sponsorship, AnnualConferenceRegistrationSponsorshipVo::new);
            String type = sponsorshipConfigService.getSponsorshipConfigType(sponsorship.getFkSponsorshipConfigId());
            sponsorshipConfigDto.setType(type);
            convertDatas.add(sponsorshipConfigDto);
        }
        return convertDatas;
    }

    @Override
    public Boolean soldOut(Long sponsorshipConfigId, Integer initNum) {
//        Example example = new Example(AnnualConferenceRegistrationSponsorship.class);
//        example.createCriteria().andEqualTo("fkSponsorshipConfigId", sponsorshipConfigId);
//        List<AnnualConferenceRegistrationSponsorship> sponsorships = annualConferenceRegistrationSponsorshipMapper.selectByExample(example);
        List<AnnualConferenceRegistrationSponsorship> sponsorships = annualConferenceRegistrationSponsorshipMapper.selectList(Wrappers.<AnnualConferenceRegistrationSponsorship>lambdaQuery().eq(AnnualConferenceRegistrationSponsorship::getFkSponsorshipConfigId, sponsorshipConfigId));

        if (null != sponsorships) {
            if (initNum > sponsorships.size()) {
                return false;
            }
        }
        return true;
    }


}
