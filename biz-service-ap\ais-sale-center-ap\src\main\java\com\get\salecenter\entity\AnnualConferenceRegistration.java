package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("m_annual_conference_registration")
public class AnnualConferenceRegistration extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    @Column(name = "institution_name")
    private String institutionName;
    /**
     * 联系人名称
     */
    @ApiModelProperty(value = "联系人名称")
    @Column(name = "name")
    private String name;
    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话")
    @Column(name = "tel")
    private String tel;
    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    @Column(name = "email")
    private String email;
    /**
     * 总价
     */
    @ApiModelProperty(value = "总价")
    @Column(name = "total_price")
    private BigDecimal totalPrice;
    /**
     * 回执码，8位数字随机数
     */
    @ApiModelProperty(value = "回执码，8位数字随机数")
    @Column(name = "receipt_code")
    private String receiptCode;
}