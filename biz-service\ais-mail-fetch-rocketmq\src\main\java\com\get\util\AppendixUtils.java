package com.get.util;

import org.springframework.web.multipart.MultipartFile;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

public class AppendixUtils {
    private static String FILE_ROOT_PATH = "/appendix";

    private static String FILE_HTI_ROOT_PATH = "/ais-email";

    public static String getFilePath(MultipartFile file) {
        String fileFileName = file.getOriginalFilename();
        String dateString = getDateString();
        // 生成一个随机的文件名，例如：XXX.jpg
        String fileName = UUID.randomUUID() + fileFileName.substring(fileFileName.lastIndexOf("."));
        String fileurl = FILE_ROOT_PATH + dateString + fileName;
        return fileurl;
    }


    public static String getFileHtiPath(String fileFileName) {
        // 首先检查文件名是否为null或空字符串
        if (fileFileName == null || fileFileName.isEmpty()) {
            throw new IllegalArgumentException("File name cannot be null or empty.");
        }

        // 获取当前日期字符串作为文件存储路径的一部分
        String dateString = getDateString();

        // 查找最后一个"."的位置
        int dotIndex = fileFileName.lastIndexOf('.');

        // 确定文件扩展名部分
        String fileExtension;
        if(dotIndex > 0) { // 如果存在"."并且不在第一位
            fileExtension = fileFileName.substring(dotIndex); // 包括"."
        } else {
            fileExtension = ""; // 默认情况下没有扩展名
        }

        // 生成一个随机的文件名，例如：UUID.jpg
        String fileName = UUID.randomUUID() + fileExtension;

        // 构建完整的文件存储路径
        String fileurl = FILE_HTI_ROOT_PATH + dateString + fileName;

        return fileurl;


       /* String dateString = getDateString();
        // 生成一个随机的文件名，例如：XXX.jpg
        String fileName = UUID.randomUUID() + fileFileName.substring(fileFileName.lastIndexOf("."));
        String fileurl = FILE_HTI_ROOT_PATH + dateString + fileName;
        return fileurl;*/
    }


    private static String getDateString() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("/yyyy/MM/dd/");
        Date date = new Date();
        String dateString = simpleDateFormat.format(date);
        return dateString;
    }
}
