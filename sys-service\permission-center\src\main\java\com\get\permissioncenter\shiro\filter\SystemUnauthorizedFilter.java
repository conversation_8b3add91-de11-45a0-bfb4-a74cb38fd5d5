//package com.get.permissioncenter.shiro.filter;
//
//import com.get.common.eunms.ErrorCodeEnum;
//import com.get.common.result.ResponseBo;
//import com.get.common.utils.WebUtilsPro;
//import org.apache.shiro.web.filter.authc.FormAuthenticationFilter;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import javax.servlet.ServletRequest;
//import javax.servlet.ServletResponse;
//
///**
// * shiro无权限过滤器
// *
// * @package: com.yinghu.platform.filter
// * @author: jack
// * @create: 2020-06-20
// * @verison: 1.0
// * @description: shiro未登录过滤器
// */
//public class SystemUnauthorizedFilter extends FormAuthenticationFilter {
//
//    private static final Logger log = LoggerFactory.getLogger(SystemUnauthorizedFilter.class);
//    @Override
//    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws Exception {
//        if (isLoginRequest(request, response)) {
//            if (isLoginSubmission(request, response)) {
//                if (log.isTraceEnabled()) {
//                    log.trace("Login submission detected.  Attempting to execute login.");
//                }
//                return executeLogin(request, response);
//            } else {
//                if (log.isTraceEnabled()) {
//                    log.trace("Login page view.");
//                }
//                WebUtilsPro.writer(ResponseBo.error(ErrorCodeEnum.VERIFY_FAILED.getCode(),"用户没登录"),request,response);
//                return false;
//            }
//        } else {
//            WebUtilsPro.writer(ResponseBo.error(ErrorCodeEnum.VERIFY_FAILED.getCode(),"用户没登录"),request,response);
//            return false;
//        }
//    }
//}
