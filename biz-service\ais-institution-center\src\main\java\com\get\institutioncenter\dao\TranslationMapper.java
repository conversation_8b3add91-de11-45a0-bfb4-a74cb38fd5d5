package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.dto.TranslationDto;
import com.get.institutioncenter.entity.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface TranslationMapper extends BaseMapper<InstitutionTranslation> {
    int insert(InstitutionTranslation record);

    int insertSelective(InstitutionTranslation record);

    String getTranslation(TranslationDto translationDto);

    List<Long> getTranslationIdsByTableNameAndColumnAndLanguageCode(@Param("fkTableName") String fkTableName, @Param("fkColumnName") String fkColumnName, @Param("languageCode") String languageCode);

    List<Long> getTranslationIdListByTableNameAndColumnAndLanguageCode(@Param("fkTableName") String fkTableName, @Param("fkColumnName") String fkColumnName, @Param("languageCode") String languageCode);

    InstitutionTranslation getTranslationById(@Param("id") Long translationId);

    List<Map<String, Object>> selectByListMaps(@Param("mapList") List<Map<String, Object>> mapList);

    List<InstitutionFaculty> getInstitutionFacultyForTranslation(@Param("institutionIds")List<Long> institutionIds);

    List<InstitutionCourseSubject> getInstitutionCourseSubjectForTranslation(@Param("institutionIds")List<Long> institutionIds);

    List<Institution> getInstitutionForTranslationDetail(@Param("institutionIds")List<Long> institutionIds);

    List<InstitutionCourse> getInstitutionCourseForTranslation(@Param("institutionIds")List<Long> institutionIds);

    List<InstitutionCourseEngScore> getInstitutionCourEngScoreForTranslation(@Param("institutionIds")List<Long> institutionIds);

    List<InstitutionCourseAcademicScore> getInstitutionCourAcademicScoreForTranslation(@Param("institutionIds")List<Long> institutionIds);

    List<InstitutionFaculty> getInstitutionFacultyForTranslations();

    List<InstitutionCourseSubject> getInstitutionCourseSubjectForTranslations();

    List<Institution> getInstitutionForTranslationDetails();

    List<InstitutionCourse> getInstitutionCourseForTranslations();

    List<InstitutionCourseEngScore> getInstitutionCourEngScoreForTranslations();

    List<InstitutionCourseAcademicScore> getInstitutionCourAcademicScoreForTranslations();
}