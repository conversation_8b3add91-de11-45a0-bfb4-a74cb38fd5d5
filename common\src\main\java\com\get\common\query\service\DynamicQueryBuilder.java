package com.get.common.query.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.get.common.query.annotation.QueryField;
import com.get.common.query.cache.QueryCacheManager;
import com.get.common.query.enums.LogicType;
import com.get.common.query.enums.QueryType;
import com.get.common.query.exception.*;
import com.get.common.query.model.FieldInfo;
import com.get.common.query.monitor.QueryMetricsCollector;
import com.get.common.query.security.SecurityValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 基于注解自动构建MyBatis Plus查询条件，支持复杂查询逻辑
 * 
 * 核心特性：
 * - 支持完整的AND/OR逻辑组合
 * - 支持复杂的分组查询：(A AND B) OR (C AND D)
 * - 智能参数验证和类型安全
 * - 高性能缓存机制
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Slf4j
@Component
@ConditionalOnBean({QueryCacheManager.class, QueryMetricsCollector.class, SecurityValidator.class})
@RequiredArgsConstructor
public class DynamicQueryBuilder {
    
    private final QueryCacheManager cacheManager;
    private final QueryMetricsCollector metricsCollector;
    private final SecurityValidator securityValidator;
    
    /**
     * 根据查询对象动态构建 QueryWrapper
     *
     * @param queryDto 查询DTO对象
     * @param entityClass 实体类Class
     * @param <T> 实体类型
     * @return 构建好的QueryWrapper
     * @throws QueryBuildException 构建查询失败时抛出
     */
    public <T> QueryWrapper<T> buildQueryWrapper(Object queryDto, Class<T> entityClass) {
        if (queryDto == null) {
            log.debug("查询DTO为null，返回空查询条件");
            return new QueryWrapper<>();
        }
        
        // 开始监控
        QueryMetricsCollector.QueryExecutionContext context = 
            metricsCollector.startQuery(queryDto.getClass(), entityClass);
        
        try {
            log.debug("开始构建动态查询 - DTO: {}, Entity: {}", 
                queryDto.getClass().getSimpleName(), entityClass.getSimpleName());
            
            QueryWrapper<T> wrapper = new QueryWrapper<>();
            
            // 获取并验证字段信息
            List<FieldInfo> fieldInfos = getFieldInfos(queryDto.getClass());
            if (fieldInfos.isEmpty()) {
                log.debug("未找到任何@QueryField注解字段，返回空查询条件");
                return wrapper;
            }
            
            // 按优先级排序
            fieldInfos = sortFieldsByPriority(fieldInfos);
            
            // 构建分组查询条件
            buildGroupedConditions(wrapper, fieldInfos, queryDto, entityClass);
            
            log.debug("查询条件构建完成 - 字段数: {}", fieldInfos.size());
            
            // 结束监控
            metricsCollector.endQuery(context, true, null);
            
            return wrapper;
            
        } catch (Exception e) {
            // 结束监控（失败）
            metricsCollector.endQuery(context, false, e.getMessage());
            throw QueryBuildException.buildFailed(queryDto.getClass(), entityClass, e);
        }
    }
    
    /**
     * 按优先级排序字段信息
     */
    private List<FieldInfo> sortFieldsByPriority(List<FieldInfo> fieldInfos) {
        return fieldInfos.stream()
                .sorted(Comparator.comparingInt(f -> f.getQueryField().priority()))
                .collect(Collectors.toList());
    }
    
    /**
     * 按组构建查询条件，支持复杂的分组逻辑
     * 分组内使用AND连接，分组间使用OR连接
     */
    private <T> void buildGroupedConditions(QueryWrapper<T> wrapper, List<FieldInfo> fieldInfos,
                                           Object queryDto, Class<T> entityClass) {
        
        // 按组分类字段
        Map<String, List<FieldInfo>> groupedFields = fieldInfos.stream()
                .collect(Collectors.groupingBy(f -> f.getQueryField().group()));
        
        log.debug("分组查询条件构建 - 分组数: {}, 分组: {}", 
            groupedFields.size(), groupedFields.keySet());
        
        boolean isFirstGroup = true;
        
        for (Map.Entry<String, List<FieldInfo>> entry : groupedFields.entrySet()) {
            String groupName = entry.getKey();
            List<FieldInfo> groupFields = entry.getValue();
            
            try {
                // 过滤有效字段（非空值字段）
                List<FieldInfo> validFields = getValidFields(groupFields, queryDto);
                
                if (validFields.isEmpty()) {
                    log.debug("分组 {} 中没有有效字段，跳过", groupName);
                    continue;
                }
                
                log.debug("处理分组 {} - 有效字段数: {}", groupName, validFields.size());
                
                if ("default".equals(groupName)) {
                    // 默认组，不分组，按原逻辑处理
                    buildDefaultGroupConditions(wrapper, validFields, queryDto, entityClass);
                } else {
                    // 分组处理
                    buildNamedGroupConditions(wrapper, validFields, queryDto, entityClass, groupName, isFirstGroup);
                    isFirstGroup = false;
                }
                
            } catch (Exception e) {
                throw QueryBuildException.groupBuildFailed(groupName, e);
            }
        }
    }
    
    /**
     * 获取有效字段（非空值字段）
     * 过滤掉值为空或应被忽略的字段
     * 
     * @param groupFields 待过滤的字段信息列表
     * @param queryDto 查询DTO对象，用于获取字段值
     * @return 有效的字段信息列表
     * @throws FieldAccessException 当字段访问失败时抛出
     */
    private List<FieldInfo> getValidFields(List<FieldInfo> groupFields, Object queryDto) {
        return groupFields.stream()
                .filter(fieldInfo -> {
                    try {
                        Object value = fieldInfo.getField().get(queryDto);
                        return !shouldIgnoreValue(value, fieldInfo.getQueryField());
                    } catch (IllegalAccessException e) {
                        throw FieldAccessException.groupAccessFailed(fieldInfo.getField(), 
                            fieldInfo.getQueryField().group(), e);
                    }
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 构建默认组查询条件
     * 默认组中，字段按照各自的logic属性进行连接
     * 
     * @param wrapper QueryWrapper实例
     * @param validFields 有效字段列表
     * @param queryDto 查询DTO对象
     * @param entityClass 实体类
     */
    private <T> void buildDefaultGroupConditions(QueryWrapper<T> wrapper, List<FieldInfo> validFields,
                                                Object queryDto, Class<T> entityClass) {
        for (FieldInfo fieldInfo : validFields) {
            buildSingleCondition(wrapper, fieldInfo, queryDto);
        }
    }
    
    /**
     * 构建命名分组查询条件
     */
    private <T> void buildNamedGroupConditions(QueryWrapper<T> wrapper, List<FieldInfo> validFields,
                                              Object queryDto, Class<T> entityClass, String groupName, boolean isFirstGroup) {
        if (!isFirstGroup) {
            // 非第一组，使用OR连接
            wrapper.or(nestedWrapper -> {
                buildGroupConditions(nestedWrapper, validFields, queryDto);
            });
        } else {
            // 第一组，直接添加条件
            buildGroupConditions(wrapper, validFields, queryDto);
        }
    }
    
    /**
     * 构建组内查询条件（组内强制使用AND连接）
     * 在分组查询中，忽略字段的logic属性，组内所有条件都使用AND连接
     * 
     * @param wrapper QueryWrapper实例
     * @param fieldInfos 字段信息列表
     * @param queryDto 查询DTO对象
     */
    private <T> void buildGroupConditions(QueryWrapper<T> wrapper, List<FieldInfo> fieldInfos, Object queryDto) {
        for (FieldInfo fieldInfo : fieldInfos) {
            // 在分组查询中，强制使用AND逻辑，忽略字段的logic属性
            buildSingleConditionWithFixedLogic(wrapper, fieldInfo, queryDto, LogicType.AND);
        }
    }
    
    /**
     * 构建单个字段的查询条件（使用字段自身的logic属性）
     */
    private <T> void buildSingleCondition(QueryWrapper<T> wrapper, FieldInfo fieldInfo, Object queryDto) {
        LogicType logicType = fieldInfo.getQueryField().logic();
        buildSingleConditionWithFixedLogic(wrapper, fieldInfo, queryDto, logicType);
    }
    
    /**
     * 构建单个字段的查询条件（使用指定的逻辑类型）
     * 包含参数安全验证和转义处理
     * 
     * @param wrapper QueryWrapper实例
     * @param fieldInfo 字段信息
     * @param queryDto 查询DTO对象
     * @param forceLogicType 强制使用的逻辑类型
     */
    private <T> void buildSingleConditionWithFixedLogic(QueryWrapper<T> wrapper, FieldInfo fieldInfo, 
                                                        Object queryDto, LogicType forceLogicType) {
        try {
            Object value = fieldInfo.getField().get(queryDto);
            
            if (shouldIgnoreValue(value, fieldInfo.getQueryField())) {
                return;
            }
            
            // 参数安全验证
            securityValidator.validateParameterValue(value);
            
            QueryType queryType = fieldInfo.getQueryField().type();
            String columnName = fieldInfo.getColumnName();
            
            // 验证查询类型与参数值的兼容性
            securityValidator.validateQueryTypeCompatibility(queryType, value);
            
            // 根据字段类型进行特定验证
            performTypeSpecificValidation(value, fieldInfo);
            
            // 对LIKE查询进行特殊的转义处理
            Object processedValue = value;
            if (isLikeQuery(queryType) && value instanceof String) {
                processedValue = securityValidator.escapeLikeValue((String) value);
                log.debug("LIKE查询值已转义 - 原值: {}, 转义后: {}", value, processedValue);
            }
            
            log.debug("构建查询条件 - 字段: {}, 类型: {}, 逻辑: {}, 值: {}", 
                fieldInfo.getField().getName(), queryType, forceLogicType, 
                processedValue.toString().length() > 100 ? 
                processedValue.toString().substring(0, 100) + "..." : processedValue);
            
            buildConditionByType(wrapper, queryType, columnName, processedValue, fieldInfo, forceLogicType);
            
        } catch (IllegalAccessException e) {
            throw FieldAccessException.accessFailed(fieldInfo.getField(), "构建查询条件", e);
        }
    }
    
    /**
     * 判断是否为LIKE类型的查询
     */
    private boolean isLikeQuery(QueryType queryType) {
        return queryType == QueryType.LIKE || 
               queryType == QueryType.LIKE_LEFT || 
               queryType == QueryType.LIKE_RIGHT;
    }
    
    /**
     * 根据查询类型和逻辑类型构建具体的查询条件
     */
    private <T> void buildConditionByType(QueryWrapper<T> wrapper, QueryType queryType,
                                         String columnName, Object value, FieldInfo fieldInfo, LogicType logicType) {
        
        boolean useOr = (logicType == LogicType.OR);
        
        switch (queryType) {
            case EQ:
                applyCondition(wrapper, useOr, w -> w.eq(columnName, value));
                break;
            case NE:
                applyCondition(wrapper, useOr, w -> w.ne(columnName, value));
                break;
            case LIKE:
                applyCondition(wrapper, useOr, w -> w.like(columnName, value));
                break;
            case LIKE_LEFT:
                applyCondition(wrapper, useOr, w -> w.likeLeft(columnName, value));
                break;
            case LIKE_RIGHT:
                applyCondition(wrapper, useOr, w -> w.likeRight(columnName, value));
                break;
            case GT:
                applyCondition(wrapper, useOr, w -> w.gt(columnName, value));
                break;
            case GE:
                applyCondition(wrapper, useOr, w -> w.ge(columnName, value));
                break;
            case LT:
                applyCondition(wrapper, useOr, w -> w.lt(columnName, value));
                break;
            case LE:
                applyCondition(wrapper, useOr, w -> w.le(columnName, value));
                break;
            case IN:
                handleCollectionQuery(wrapper, columnName, value, useOr, true, fieldInfo);
                break;
            case NOT_IN:
                handleCollectionQuery(wrapper, columnName, value, useOr, false, fieldInfo);
                break;
            case IS_NULL:
                applyCondition(wrapper, useOr, w -> w.isNull(columnName));
                break;
            case IS_NOT_NULL:
                applyCondition(wrapper, useOr, w -> w.isNotNull(columnName));
                break;
            case BETWEEN:
                handleRangeQuery(wrapper, columnName, value, useOr, true, fieldInfo);
                break;
            case NOT_BETWEEN:
                handleRangeQuery(wrapper, columnName, value, useOr, false, fieldInfo);
                break;
            case ORDER_BY:
                handleOrderBy(wrapper, columnName, fieldInfo);
                break;
            case IGNORE:
                // 忽略该字段，不做任何处理
                log.debug("忽略字段: {}", fieldInfo.getField().getName());
                break;
            default:
                throw QueryTypeNotSupportedException.unsupported(queryType, 
                    fieldInfo.getField().getName(), columnName);
        }
    }
    
    /**
     * 应用查询条件（处理AND/OR逻辑）
     * 修复AND逻辑处理，确保逻辑连接符的正确应用
     * 
     * @param wrapper QueryWrapper实例
     * @param useOr 是否使用OR逻辑，false表示使用AND逻辑
     * @param conditionApplier 条件应用函数
     */
    private <T> void applyCondition(QueryWrapper<T> wrapper, boolean useOr, 
                                   java.util.function.Consumer<QueryWrapper<T>> conditionApplier) {
        // 检查是否已有查询条件
        boolean hasExistingConditions = !wrapper.getExpression().getNormal().isEmpty();
        
        if (hasExistingConditions && useOr) {
            // 只有在需要OR逻辑且已有条件时才调用or()
            // MyBatis Plus默认使用AND连接，所以不需要显式调用and()
            wrapper.or();
        }
        // 对于AND逻辑或者没有现有条件的情况，直接添加新条件
        // MyBatis Plus会自动使用AND连接多个条件
        
        conditionApplier.accept(wrapper);
    }
    
    /**
     * 处理集合类型查询（IN/NOT_IN）
     * 包含集合参数的安全验证
     */
    private <T> void handleCollectionQuery(QueryWrapper<T> wrapper, String columnName, Object value, 
                                          boolean useOr, boolean isIn, FieldInfo fieldInfo) {
        if (!(value instanceof Collection)) {
            throw ParameterValidationException.invalidCollectionType(
                isIn ? QueryType.IN : QueryType.NOT_IN, value);
        }
        
        Collection<?> collection = (Collection<?>) value;
        if (collection.isEmpty()) {
            log.debug("{}查询的集合为空，忽略该条件 - 字段: {}", 
                isIn ? "IN" : "NOT_IN", fieldInfo.getField().getName());
            return;
        }
        
        // 集合参数安全验证（限制集合大小为1000）
        securityValidator.validateCollectionParameter(collection, 1000);
        
        log.debug("集合查询参数验证通过 - 类型: {}, 大小: {}, 字段: {}", 
            isIn ? "IN" : "NOT_IN", collection.size(), fieldInfo.getField().getName());
        
        if (isIn) {
            applyCondition(wrapper, useOr, w -> w.in(columnName, collection));
        } else {
            applyCondition(wrapper, useOr, w -> w.notIn(columnName, collection));
        }
    }
    
    /**
     * 处理范围类型查询（BETWEEN/NOT_BETWEEN）
     */
    private <T> void handleRangeQuery(QueryWrapper<T> wrapper, String columnName, Object value, 
                                     boolean useOr, boolean isBetween, FieldInfo fieldInfo) {
        if (!(value instanceof List)) {
            throw ParameterValidationException.invalidRangeType(
                isBetween ? QueryType.BETWEEN : QueryType.NOT_BETWEEN, value);
        }
        
        List<?> range = (List<?>) value;
        if (range.size() != 2) {
            throw ParameterValidationException.invalidRangeSize(
                isBetween ? QueryType.BETWEEN : QueryType.NOT_BETWEEN, range.size());
        }
        
        Object start = range.get(0);
        Object end = range.get(1);
        
        if (start == null || end == null) {
            throw ParameterValidationException.rangeContainsNull(
                isBetween ? QueryType.BETWEEN : QueryType.NOT_BETWEEN, start, end);
        }
        
        // 验证和修正范围值顺序
        RangeValues rangeValues = validateAndFixRange(start, end);
        
        if (isBetween) {
            applyCondition(wrapper, useOr, w -> w.between(columnName, rangeValues.getStart(), rangeValues.getEnd()));
        } else {
            applyCondition(wrapper, useOr, w -> w.notBetween(columnName, rangeValues.getStart(), rangeValues.getEnd()));
        }
    }
    
    /**
     * 处理排序条件
     * 根据字段注解中的asc属性添加升序或降序排序
     * 
     * @param wrapper QueryWrapper实例
     * @param columnName 数据库字段名
     * @param fieldInfo 字段信息，包含排序方向配置
     */
    private <T> void handleOrderBy(QueryWrapper<T> wrapper, String columnName, FieldInfo fieldInfo) {
        if (fieldInfo.getQueryField().asc()) {
            wrapper.orderByAsc(columnName);
            log.debug("添加升序排序 - 字段: {}", columnName);
        } else {
            wrapper.orderByDesc(columnName);
            log.debug("添加降序排序 - 字段: {}", columnName);
        }
    }
    
    /**
     * 验证并修正范围值的顺序
     * 确保范围查询的起始值小于等于结束值，如果顺序错误则自动交换
     * 
     * @param start 范围开始值
     * @param end 范围结束值
     * @return 修正后的范围值对象
     * @throws ParameterValidationException 当范围值类型不兼容时抛出
     */
    private RangeValues validateAndFixRange(Object start, Object end) {
        try {
            if (start instanceof Comparable && end instanceof Comparable && 
                start.getClass().equals(end.getClass())) {
                
                @SuppressWarnings("unchecked")
                Comparable<Object> startComp = (Comparable<Object>) start;
                
                if (startComp.compareTo(end) <= 0) {
                    return new RangeValues(start, end);
                } else {
                    log.warn("范围查询值顺序错误: [{}, {}]，已自动交换", start, end);
                    return new RangeValues(end, start);
                }
            }
        } catch (ClassCastException e) {
            throw ParameterValidationException.incompatibleRangeTypes(start, end);
        }
        
        // 无法比较时，保持原顺序
        return new RangeValues(start, end);
    }
    
    /**
     * 判断是否应该忽略该值
     * 根据QueryField注解的ignoreEmpty属性和值的实际情况判断是否应该忽略该字段
     * 
     * @param value 字段值
     * @param queryField 查询字段注解
     * @return true表示应该忽略该值，false表示不应忽略
     */
    private boolean shouldIgnoreValue(Object value, QueryField queryField) {
        if (!queryField.ignoreEmpty()) {
            return false;
        }
        
        if (value == null) {
            return true;
        }
        
        if (value instanceof String && ((String) value).trim().isEmpty()) {
            return true;
        }
        
        if (value instanceof Collection && ((Collection<?>) value).isEmpty()) {
            return true;
        }
        
        if (value.getClass().isArray()) {
            return java.lang.reflect.Array.getLength(value) == 0;
        }
        
        return false;
    }
    
    /**
     * 获取字段信息（带缓存）
     */
    private List<FieldInfo> getFieldInfos(Class<?> clazz) {
        List<FieldInfo> fieldInfos = cacheManager.getFieldInfos(clazz);
        if (fieldInfos == null) {
            // 缓存未命中
            metricsCollector.recordCacheMiss(clazz);
            fieldInfos = parseFieldInfos(clazz);
            cacheManager.cacheFieldInfos(clazz, fieldInfos);
        } else {
            // 缓存命中
            metricsCollector.recordCacheHit(clazz);
        }
        return fieldInfos;
    }
    
    /**
     * 解析字段信息
     * 包含安全验证，确保字段名符合安全要求
     */
    private List<FieldInfo> parseFieldInfos(Class<?> clazz) {
        List<FieldInfo> fieldInfos = new ArrayList<>();
        List<Field> allFields = getAllFields(clazz);
        
        for (Field field : allFields) {
            QueryField queryField = field.getAnnotation(QueryField.class);
            if (queryField != null) {
                field.setAccessible(true);
                
                // 创建字段信息对象
                FieldInfo fieldInfo = new FieldInfo(field, queryField);
                
                // 验证字段名的安全性
                try {
                    securityValidator.validateColumnName(fieldInfo.getColumnName());
                    fieldInfos.add(fieldInfo);
                    log.debug("字段安全验证通过 - 字段: {}, 数据库字段: {}", 
                        field.getName(), fieldInfo.getColumnName());
                } catch (Exception e) {
                    log.error("字段安全验证失败 - 字段: {}, 数据库字段: {}, 错误: {}", 
                        field.getName(), fieldInfo.getColumnName(), e.getMessage());
                    throw new QueryBuildException(
                        String.format("字段安全验证失败 - %s.%s: %s", 
                            clazz.getSimpleName(), field.getName(), e.getMessage()), e);
                }
            }
        }
        
        log.debug("解析字段信息完成 - 类: {}, 总字段数: {}, 注解字段数: {}", 
            clazz.getSimpleName(), allFields.size(), fieldInfos.size());
        
        return fieldInfos;
    }
    
    /**
     * 获取类的所有字段，包括父类字段
     */
    private List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        
        while (clazz != null && clazz != Object.class) {
            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }
        
        return fields;
    }
    
    
    /**
     * 清空缓存
     */
    public void clearCache() {
        cacheManager.clearAll();
    }
    
    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        return cacheManager.getCacheStats();
    }
    
    /**
     * 获取详细的诊断信息
     */
    public Map<String, Object> getDiagnosticInfo(Class<?> queryDtoClass) {
        Map<String, Object> diagnostic = new HashMap<>();
        
        try {
            List<FieldInfo> fieldInfos = getFieldInfos(queryDtoClass);
            
            diagnostic.put("className", queryDtoClass.getSimpleName());
            diagnostic.put("totalFields", fieldInfos.size());
            
            // 按查询类型统计
            Map<QueryType, Long> typeStats = fieldInfos.stream()
                    .collect(Collectors.groupingBy(f -> f.getQueryField().type(), Collectors.counting()));
            diagnostic.put("queryTypeStats", typeStats);
            
            // 按逻辑类型统计
            Map<LogicType, Long> logicStats = fieldInfos.stream()
                    .collect(Collectors.groupingBy(f -> f.getQueryField().logic(), Collectors.counting()));
            diagnostic.put("logicTypeStats", logicStats);
            
            // 按分组统计
            Map<String, Long> groupStats = fieldInfos.stream()
                    .collect(Collectors.groupingBy(f -> f.getQueryField().group(), Collectors.counting()));
            diagnostic.put("groupStats", groupStats);
            
            // 字段详情
            List<Map<String, Object>> fieldDetails = fieldInfos.stream().map(fieldInfo -> {
                Map<String, Object> detail = new HashMap<>();
                detail.put("fieldName", fieldInfo.getField().getName());
                detail.put("fieldType", fieldInfo.getFieldType().getSimpleName());
                detail.put("columnName", fieldInfo.getColumnName());
                detail.put("queryType", fieldInfo.getQueryField().type());
                detail.put("logicType", fieldInfo.getQueryField().logic());
                detail.put("group", fieldInfo.getQueryField().group());
                detail.put("priority", fieldInfo.getQueryField().priority());
                detail.put("ignoreEmpty", fieldInfo.getQueryField().ignoreEmpty());
                detail.put("isCollection", fieldInfo.isCollection());
                return detail;
            }).collect(Collectors.toList());
            diagnostic.put("fieldDetails", fieldDetails);
            
            // 添加缓存信息
            diagnostic.put("cacheInfo", cacheManager.getCacheStats());
            diagnostic.put("cacheHealth", cacheManager.getHealthStatus());
            
        } catch (Exception e) {
            diagnostic.put("error", e.getMessage());
            log.error("获取诊断信息失败: {}", queryDtoClass.getSimpleName(), e);
        }
        
        return diagnostic;
    }
    
    /**
     * 范围值封装类
     * 用于封装BETWEEN查询的开始值和结束值
     * 
     * <AUTHOR>
     * @since 2025-01-30
     */
    private static class RangeValues {
        private final Object start;
        private final Object end;
        
        /**
         * 构造函数
         * 
         * @param start 范围开始值
         * @param end 范围结束值
         */
        public RangeValues(Object start, Object end) {
            this.start = start;
            this.end = end;
        }
        
        /**
         * 获取范围开始值
         * 
         * @return 开始值
         */
        public Object getStart() {
            return start;
        }
        
        /**
         * 获取范围结束值
         * 
         * @return 结束值
         */
        public Object getEnd() {
            return end;
        }
    }
    
    /**
     * 根据字段类型执行特定的参数验证
     * 
     * @param value 参数值
     * @param fieldInfo 字段信息
     */
    private void performTypeSpecificValidation(Object value, FieldInfo fieldInfo) {
        if (value == null) {
            return;
        }
        
        Class<?> fieldType = fieldInfo.getFieldType();
        QueryType queryType = fieldInfo.getQueryField().type();
        
        try {
            // 数字类型验证
            if (Number.class.isAssignableFrom(fieldType) || fieldType.isPrimitive()) {
                if (fieldType == int.class || fieldType == Integer.class) {
                    securityValidator.validateNumericParameter(value, Integer.MIN_VALUE, Integer.MAX_VALUE);
                } else if (fieldType == long.class || fieldType == Long.class) {
                    securityValidator.validateNumericParameter(value, Long.MIN_VALUE, Long.MAX_VALUE);
                } else if (fieldType == double.class || fieldType == Double.class ||
                          fieldType == float.class || fieldType == Float.class) {
                    securityValidator.validateNumericParameter(value, null, null); // 不限制范围，只检查NaN和无穷大
                } else if (fieldType == BigDecimal.class) {
                    securityValidator.validateNumericParameter(value, null, null);
                }
            }
            // 日期类型验证
            else if (LocalDate.class.isAssignableFrom(fieldType) || 
                     LocalDateTime.class.isAssignableFrom(fieldType) ||
                     java.util.Date.class.isAssignableFrom(fieldType)) {
                // 默认允许过去和将来的日期，具体业务可通过自定义验证器进一步限制
                securityValidator.validateDateParameter(value, true, true);
            }
            // 字符串类型验证
            else if (String.class == fieldType) {
                String stringValue = (String) value;
                
                // 如果是LIKE查询，允许通配符
                if (isLikeQuery(queryType)) {
                    // LIKE查询允许 % 和 _ 通配符
                    Set<Character> allowedChars = new HashSet<>();
                    allowedChars.add('%');
                    allowedChars.add('_');
                    allowedChars.add('-'); // 连字符
                    allowedChars.add('.');
                    allowedChars.add('@'); // 邮箱
                    allowedChars.add('(');
                    allowedChars.add(')');
                    allowedChars.add('+');
                    
                    securityValidator.validateStringSpecialChars(stringValue, allowedChars);
                } else {
                    // 非LIKE查询，限制特殊字符
                    Set<Character> allowedChars = new HashSet<>();
                    allowedChars.add('-'); // 连字符
                    allowedChars.add('.');
                    allowedChars.add('@'); // 邮箱
                    allowedChars.add('(');
                    allowedChars.add(')');
                    allowedChars.add('+');
                    allowedChars.add(',');
                    allowedChars.add(':');
                    
                    securityValidator.validateStringSpecialChars(stringValue, allowedChars);
                }
            }
            // 集合类型验证（IN/NOT_IN查询已在其他地方验证）
            else if (java.util.Collection.class.isAssignableFrom(fieldType)) {
                if (queryType == QueryType.IN || queryType == QueryType.NOT_IN) {
                    Collection<?> collection = (Collection<?>) value;
                    // 递归验证集合中的每个元素
                    for (Object item : collection) {
                        if (item != null) {
                            securityValidator.validateParameterValue(item);
                        }
                    }
                }
            }
            
            log.debug("字段类型特定验证通过 - 字段: {}, 类型: {}, 查询类型: {}", 
                fieldInfo.getField().getName(), fieldType.getSimpleName(), queryType);
                
        } catch (Exception e) {
            log.error("字段类型特定验证失败 - 字段: {}, 类型: {}, 查询类型: {}, 错误: {}", 
                fieldInfo.getField().getName(), fieldType.getSimpleName(), queryType, e.getMessage());
            throw new QueryBuildException(
                String.format("字段 %s 的类型特定验证失败: %s", 
                    fieldInfo.getField().getName(), e.getMessage()), e);
        }
    }
}