package com.get.financecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Size;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2021/12/6
 * @TIME: 15:46
 * @Description:
 **/
@Data
public class ReceiptFeeTypeDto  extends BaseVoEntity {

    @ApiModelProperty(value = "类型组别Key")
    private String typeGroupKey;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    private String typeName;

    @ApiModelProperty(value = "科目Id")
    private Long fkAccountingItemId;

    @ApiModelProperty(value = "关联类型Key（目标类型表名）")
    private String relationTargetKey;

    @ApiModelProperty(value = "凭证摘要设定")
    @Size(max = 1000, message = "凭证摘要设定不能超过200字符")
    private String vouchSummary;

    @ApiModelProperty(value = "关键字")
    private String keyWord;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
}
