package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.Character;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface CharacterMapper extends BaseMapper<Character> {
    int insert(Character record);

    int insertSelective(Character record);

    /**
     * @return boolean
     * @Description :判断特性是否存在
     * @Param character
     * <AUTHOR>
     */
    boolean isExistByMap(Character character);
}