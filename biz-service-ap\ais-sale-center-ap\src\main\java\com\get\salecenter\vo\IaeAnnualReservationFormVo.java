package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/7/8 17:49
 * @verison: 1.0
 * @description:
 */
@Data
public class IaeAnnualReservationFormVo {

    /**
     * 回执码
     */
    @ApiModelProperty(value = "回执码")
    private String receiptCode;

    /**
     * 展位dtos
     */
    @ApiModelProperty(value = "展位dtos")
    private List<AnnualReservationBoothVo> annualReservationBoothDtoList;

    /**
     * 参会人dtos
     */
    @ApiModelProperty(value = "参会人dtos")
    private List<AnnualReservationFormVo> annualReservationFormDtoList;
}
