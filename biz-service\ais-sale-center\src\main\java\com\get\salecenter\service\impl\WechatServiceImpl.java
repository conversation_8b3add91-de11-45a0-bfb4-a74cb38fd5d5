package com.get.salecenter.service.impl;

import cn.hutool.core.io.resource.ClassPathResource;
import com.get.core.log.exception.GetServiceException;
import com.get.salecenter.dto.WechatOrderParamDto;
import com.get.salecenter.service.WechatService;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderV3Request;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderV3Result;
import com.github.binarywang.wxpay.bean.result.enums.TradeTypeEnum;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.v3.util.SignUtils;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.service.WxOAuth2Service;
import me.chanjar.weixin.mp.api.WxMpService;
import org.bouncycastle.asn1.pkcs.PrivateKeyInfo;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.FileReader;
import java.io.Reader;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Service
@Slf4j
public class WechatServiceImpl implements WechatService {

    @Resource(name = "wxMpOfficialService")
    private WxMpService wxMpOfficialService;
    
    @Resource(name = "officialWxPayService")
    private WxPayService officialWxPayService;

    // 授权回调地址（已在公众号后台配置）
    @Value("${wx.pay.official.redirect-uri}")
    private String redirectUri;

    // 私钥文件路径（resources下的wxPayCertIae文件夹）
    private static final String PRIVATE_KEY_FILE_PATH = "wxPayCertIae/apiclient_key.pem";

    /**
     * 微信公众号生成授权链接
     *
     * @return
     */
    @Override
    public String getAuthUrl() {
        try {
            // 1. 获取 OAuth2 服务实例（关键修复点）
            WxOAuth2Service oAuth2Service = wxMpOfficialService.getOAuth2Service();

            // 2. 构建授权链接（使用 oauth2Service 而非直接调用 wxMpOfficialService）
            String authUrl = oAuth2Service.buildAuthorizationUrl(
                    redirectUri,                  // 回调地址
                    "snsapi_base",                // 授权类型（静默授权）
                    URLEncoder.encode("STATE", StandardCharsets.UTF_8.name())  // 状态参数
            );
            return authUrl;
        } catch (Exception e) {
            log.error("微信授权<|>生成授权链接失败：{}", e.getMessage());
            throw new GetServiceException("无法获取微信授权，请联系工作人员");
        }
    }

    /**
     * 微信公众号获取用户openId
     *
     * @param code
     * @param state
     * @return
     */
    @Override
    public String getOpenId(String code, String state) {
        try {
            // 1. 获取OAuth2服务实例（关键步骤）
            WxOAuth2Service oauth2Service = wxMpOfficialService.getOAuth2Service();

            // 2. 调用getAccessToken方法获取access_token和openid（修复方法调用）
            WxOAuth2AccessToken accessToken = oauth2Service.getAccessToken(code);

            // 从返回结果中提取 openid（这就是你需要的用户唯一标识）
            return accessToken.getOpenId();
        } catch (Exception e) {
            log.error("微信授权<|>通过 code 兑换 openid 失败：{}", e.getMessage());
            throw new GetServiceException("无法获取微信授权，请联系工作人员");
        }
    }

    /**
     * JSAPI下单
     * @param wechatOrderParamDto
     * @return
     */
    @Override
    public Map<String, Object> createOrder(WechatOrderParamDto wechatOrderParamDto) {
        try {
            // 1. 构建统一下单请求参数
            WxPayUnifiedOrderV3Request request = new WxPayUnifiedOrderV3Request()
                    .setAppid(officialWxPayService.getConfig().getAppId()) // 公众号AppID
                    .setMchid(officialWxPayService.getConfig().getMchId()) // 商户号
                    .setDescription(wechatOrderParamDto.getDescription()) // 订单描述
                    .setOutTradeNo(wechatOrderParamDto.getOrderNo()) // 商户订单号
                    .setAttach(wechatOrderParamDto.getAttach()) // 附加数据（可选）
                    .setNotifyUrl("https://ais.ht-international.online/sale/annualReservationForm/notifyWeiXinPay")
                    // 设置支付金额（单位：分）
                    .setAmount(new WxPayUnifiedOrderV3Request.Amount()
                            // 总金额（分）
                            .setTotal(wechatOrderParamDto.getTotalAmount()))
                    // 关键：设置支付者信息（必须包含OpenID）
                    .setPayer(new WxPayUnifiedOrderV3Request.Payer()
                            // 用户OpenID
                            .setOpenid(wechatOrderParamDto.getOpenid()));

            // 2. 调用微信支付V3接口创建订单（JSAPI支付类型）
            WxPayUnifiedOrderV3Result result = officialWxPayService.createOrderV3(
                    TradeTypeEnum.JSAPI,
                    request
            );

            String prepayId = result.getPrepayId();

            // 3. 生成调起支付的参数
            String appId = officialWxPayService.getConfig().getAppId();
            long timestamp = System.currentTimeMillis() / 1000; // 秒级时间戳
            String nonceStr = UUID.randomUUID().toString().replaceAll("-", "");

            // 4. 构建签名串
            String message = String.format(
                    "appid=%s\nnoncestr=%s\npackage=prepay_id=%s\ntimestamp=%d\n",
                    appId, nonceStr, prepayId, timestamp
            );

            // 5. 私钥转换
            PrivateKey privateKey = getPrivateKeyFromFile();

            // 6. 生成签名
            // 调用签名方法，传入 String 类型的 message 和 PrivateKey
            String paySign = SignUtils.sign(message, privateKey);

            // 7. 组装返回参数
            Map<String, String> payParams = new HashMap<>();
            payParams.put("appId", appId);
            payParams.put("timeStamp", String.valueOf(timestamp));
            payParams.put("nonceStr", nonceStr);
            payParams.put("package", "prepay_id=" + prepayId);
            payParams.put("signType", "RSA");
            payParams.put("paySign", paySign);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("payParams", payParams);
            response.put("orderNo", wechatOrderParamDto.getOrderNo());
            return response;

        } catch (Exception e) {
            log.error("微信授权<|>JSAPI下单失败：{}", e.getMessage());
            throw new GetServiceException("创建微信订单失败，请联系工作人员");
        }
    }

    /**
     * 从文件读取私钥并转换为PrivateKey对象
     */
    private PrivateKey getPrivateKeyFromFile() throws Exception {
        // 读取resources目录下的私钥文件
        ClassPathResource resource = new ClassPathResource(PRIVATE_KEY_FILE_PATH);

        // 使用BouncyCastle解析PEM格式私钥
        try (Reader reader = new FileReader(resource.getFile());
             PEMParser pemParser = new PEMParser(reader)) {

            JcaPEMKeyConverter converter = new JcaPEMKeyConverter().setProvider("BC");
            PrivateKeyInfo privateKeyInfo = (PrivateKeyInfo) pemParser.readObject();
            return converter.getPrivateKey(privateKeyInfo);
        }
    }

}
