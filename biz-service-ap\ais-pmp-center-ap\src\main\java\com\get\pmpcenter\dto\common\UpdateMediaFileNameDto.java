package com.get.pmpcenter.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author:Oliver
 * @Date: 2025/6/30
 * @Version 1.0
 * @apiNote:修改文件名
 */
@Data
public class UpdateMediaFileNameDto {

    @ApiModelProperty(value = "文件guid")
    @NotBlank(message = "文件guid不能为空")
    private String fileGuid;

    @ApiModelProperty(value = "文件名")
    @NotBlank(message = "文件名不能为空")
    private String fileName;
}
