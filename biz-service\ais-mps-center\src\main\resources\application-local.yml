#服务器端口
server:
  port: 8103
spring:
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    dynamic:
      #设置默认的数据源master
      primary: geampsdb
      datasource:
        geampsdb:
          url: ${get.datasource.test.mps.url}
          username: ${get.datasource.test.mps.username}
          password: ${get.datasource.test.mps.password}

