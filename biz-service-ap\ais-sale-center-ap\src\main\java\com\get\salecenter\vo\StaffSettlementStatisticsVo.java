package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @author: Hardy
 * @create: 2023/3/8 10:06
 * @verison: 1.0
 * @description:
 */
@Data
public class StaffSettlementStatisticsVo {

    @ApiModelProperty("单条学生数据")
    private List<StaffSettlementStatisticsItemVo> staffSettlementStatisticsItemDtos;

    @ApiModelProperty("结算日期")
    private String sumSettlementDate;

    @ApiModelProperty("员工名称")
    private String staffName;

    /**
     * 总计
     * eg:
     * {
     *   "key":"",
     *   "value":0.00,
     *   "viewOrder":1
     *  }
     */
    private List<Map<String,Object>> totalAmount;

    /**
     * 结算日期条件的总计
     * eg:
     * {
     *   "key":"",
     *   "value":0.00,
     *   "viewOrder":1
     *  }
     */
    private List<Map<String,Object>> sumSettlementAmount;
}
