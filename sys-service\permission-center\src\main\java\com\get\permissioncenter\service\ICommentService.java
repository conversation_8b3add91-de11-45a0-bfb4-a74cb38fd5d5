package com.get.permissioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.permissioncenter.dto.CommentDto;
import com.get.permissioncenter.vo.CommentVo;
import com.get.permissioncenter.entity.PermissionComment;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/11/5
 * @TIME: 15:01
 * @Description:
 **/
public interface ICommentService extends BaseService<PermissionComment> {

    /**
     * @return java.util.List<com.get.salecenter>
     * @Description: 获取所有评论
     * @Param [commentDto, page]
     * <AUTHOR>
     */
    List<CommentVo> datas(CommentDto commentDto, Page page);

    /**
     * 保存
     *
     * @param comment
     * @return
     */
    void addComment(CommentDto comment);

    /**
     * 更新
     *
     * @param comment
     * @return
     */
    void updateComment(CommentDto comment);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);
}
