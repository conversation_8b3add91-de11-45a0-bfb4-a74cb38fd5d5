package com.get.salecenter.dao.sale;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.entity.VipConfig;
import com.get.salecenter.dto.VipConfigDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/11/9
 * @TIME: 16:54
 * @Description:
 **/
@Mapper
public interface VipConfigMapper  extends GetMapper<VipConfig> {

    /**
     * 获取VIP配置列表
     * <AUTHOR>
     * @DateTime 2022/11/24 15:05
     */
    List<VipConfig> getVipConfigs(IPage<VipConfig> iPage,
                                  @Param("vipConfigDto") VipConfigDto vipConfigDto,
                                  @Param("countryIds") List<Long> countryIds,
                                  @Param("providerIds") List<Long> providerIds);

    Integer getMaxViewOrder();
}
