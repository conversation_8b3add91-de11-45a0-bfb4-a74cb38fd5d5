package com.get.institutioncenter.service;


import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.dto.InstitutionProviderTypeDto;
import com.get.institutioncenter.vo.InstitutionProviderTypeVo;
import com.get.institutioncenter.entity.InstitutionProviderType;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/12 11:22
 * @verison: 1.0
 * @description: 学校提供商类型管理接口
 */
public interface IInstitutionProviderTypeService extends BaseService<InstitutionProviderType> {

    /**
     * 详情
     *
     * @param id
     * @return
     */
    InstitutionProviderTypeVo findInstitutionProviderTypeById(Long id);

    /**
     * 批量新增
     *
     * @param institutionProviderTypeDtos
     * @return
     */
    void batchAdd(List<InstitutionProviderTypeDto> institutionProviderTypeDtos);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    void delete(Long id);

    /**
     * 修改
     *
     * @param institutionProviderTypeDto
     * @return
     */
    InstitutionProviderTypeVo updateInstitutionProviderType(InstitutionProviderTypeDto institutionProviderTypeDto);


    /**
     * 列表
     *
     * @param institutionProviderTypeDto
     * @param page
     * @return
     */
    List<InstitutionProviderTypeVo> getInstitutionProviderTypes(InstitutionProviderTypeDto institutionProviderTypeDto, Page page);

    /**
     * 上移下移
     *
     * @param institutionProviderTypeDtos
     * @return
     */
    void movingOrder(List<InstitutionProviderTypeDto> institutionProviderTypeDtos);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.InstitutionProviderTypeVo>
     * @Description :学校提供商类型下拉框数据
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getinstitutionProviderTypeList();
}
