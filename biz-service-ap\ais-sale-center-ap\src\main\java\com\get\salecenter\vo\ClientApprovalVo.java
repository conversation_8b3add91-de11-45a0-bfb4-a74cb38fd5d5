package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.RStudentToClientApproval;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

@Data
public class ClientApprovalVo extends BaseEntity {

    @ApiModelProperty("学生编号")
    private String fkStudentNum;

    @ApiModelProperty("学生名称")
    private String fkStudentName;

    @ApiModelProperty("学生生日")
    private String birthday;

    @ApiModelProperty("代理名称")
    private String fkAgentName;

    @ApiModelProperty("审批人")
    private String approvalName;

    @ApiModelProperty("申请人")
    private String applyName;

    @ApiModelProperty("审批意见")
    private String approvalOpinion;

    @ApiModelProperty("公司名")
    private String fkCompanyName;

    @ApiModelProperty("部门名")
    private String departmentName;

    //========实体类RStudentToClientApproval=========
    @ApiModelProperty("公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    @ApiModelProperty("学生Id")
    @Column(name = "fk_student_id")
    private Long fkStudentId;

    @ApiModelProperty("客户Id")
    @Column(name = "fk_client_id")
    private Long fkClientId;

    @ApiModelProperty("审批人Id")
    @Column(name = "fk_staff_id_approval")
    private Long fkStaffIdApproval;

    @ApiModelProperty("申请Id")
    @Column(name = "fk_staff_id_apply")
    private Long fkStaffIdApply;


    @ApiModelProperty("审批状态：0新申请/1通过/2拒绝")
    @Column(name = "approval_status")
    private Integer approvalStatus;

}
