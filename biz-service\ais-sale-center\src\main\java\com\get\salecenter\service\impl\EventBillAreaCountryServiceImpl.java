package com.get.salecenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.salecenter.dao.sale.EventBillAreaCountryMapper;
import com.get.salecenter.entity.EventBillAreaCountry;
import com.get.salecenter.service.IEventBillAreaCountryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/5/9 16:46
 * @verison: 1.0
 * @description:
 */
@Service
public class EventBillAreaCountryServiceImpl extends ServiceImpl<EventBillAreaCountryMapper, EventBillAreaCountry> implements IEventBillAreaCountryService {


    @Resource
    private EventBillAreaCountryMapper eventBillAreaCountryMapper;

    @Override
    public Boolean batchAddByIds(List<EventBillAreaCountry> eventBillAreaCountries) {
        return saveBatch(eventBillAreaCountries);
    }

    @Override
    public List<EventBillAreaCountry> getEventBillAreaCountriesByCondition(LambdaQueryWrapper<EventBillAreaCountry> countryLambdaQueryWrapper) {
        return eventBillAreaCountryMapper.selectList(countryLambdaQueryWrapper);
    }
}
