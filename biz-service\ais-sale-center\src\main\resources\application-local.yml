#服务器端口
server:
  port: 8083

spring:
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    dynamic:
      #设置默认的数据源master
      primary: saledb
      datasource:
        saledb:
          url: ${get.datasource.test.saledb.url}
          username: ${get.datasource.test.saledb.username}
          password: ${get.datasource.test.saledb.password}
        conventiondb:
          url: ${get.datasource.test.conventiondb.url}
          username: ${get.datasource.test.conventiondb.username}
          password: ${get.datasource.test.conventiondb.password}
        oldissuedb:
          url: ${get.datasource.test.oldissuedb.url}
          username: ${get.datasource.test.oldissuedb.username}
          password: ${get.datasource.test.oldissuedb.password}
        newissuedb:
          url: ${get.datasource.test.newissuedb.url}
          username: ${get.datasource.test.newissuedb.username}
          password: ${get.datasource.test.newissuedb.password}
        saledb-doris:
          url: ${get.datasource.test.saledb-doris.url}
          username: ${get.datasource.test.saledb-doris.username}
          password: ${get.datasource.test.saledb-doris.password}
        occdb:
          driver-class-name: ${get.datasource.test.occdb.driver-class-name}
          url: ${get.datasource.test.occdb.url}
          username: ${get.datasource.test.occdb.username}
          password: ${get.datasource.test.occdb.password}
        saledb-doris-export:
          url: ${get.datasource.test.saledb-doris-export.url}
          username: ${get.datasource.test.saledb-doris-export.username}
          password: ${get.datasource.test.saledb-doris-export.password}
        appInsurance:
          url: ${get.datasource.test.appInsurance.url}
          username: ${get.datasource.test.appInsurance.username}
          password: ${get.datasource.test.appInsurance.password}
  #        occdb:
  #          driver-class-name: oracle.jdbc.driver.OracleDriver
  #          url: *******************************************
  #          username: road
  #          password: GETCPP_ROAD_2F82KF7V
  mail:
    port: 25


# 汇率第三方接口id key
exchangeRate:
  secretId: AKID7F0rz8PQrCtFiR7BLq3IwDqQzGBl3il14I0G
  secretKey: c14m3z425z55cw4trAUo9gjcCMspyp75rc5oVOpc

analyzeOfferAddress: http://*************:15051/offer_parser/

#营业执照解析接口
bl-url: http://************:5024/business_license/

#解析身份证接口
idCard-url: http://************:5024/idcard/?idcard_side=

#邮箱匹配接口
emailMate-url: http://************:15016/get_similar_email

issue:
  url: http://***********:8080

newissue:
  url: http://************:5080


gear:
  url: http://************:5079

# 域名
domainName: http://************:8081/

# app端域名
appDomainName: http://************:9084/

# 系统之间交互认证id和密钥
avatarKey:
  fkPlatformTypes: get_app
  appId: get_app_2024#@!
  appSecret: 15091f11-de26-4c9b-93bc-6c3dee1d5af7

# 微信支付配置
wx:
  pay:
    # 微信支付商户号
    miniprogram:
      appId: ${get.wx.pay.miniprogram.appid}
      mchId: ${get.wx.pay.miniprogram.mchId}
      keyPath: ${get.wx.pay.miniprogram.keyPath}
      privateKeyPath: ${get.wx.pay.miniprogram.privateKeyPath}
      privateCertPath: ${get.wx.pay.miniprogram.privateCertPath}
      apiV3Key: ${get.wx.pay.miniprogram.apiV3Key}
      certSerialNo: ${get.wx.pay.miniprogram.certSerialNo}
      payNotifyUrl: ${get.wx.pay.miniprogram.payNotifyUrl}
    official:
      appId: ${get.wx.pay.official.appid}
      mchId: ${get.wx.pay.official.mchId}
      keyPath: ${get.wx.pay.official.keyPath}
      privateKeyPath: ${get.wx.pay.official.privateKeyPath}
      privateCertPath: ${get.wx.pay.official.privateCertPath}
      apiV3Key: ${get.wx.pay.official.apiV3Key}
      certSerialNo: ${get.wx.pay.official.certSerialNo}
      payNotifyUrl: ${get.wx.pay.official.payNotifyUrl}
      secret: ${get.wx.pay.official.secret}
      redirect-uri: ${get.wx.pay.official.redirect-uri}


#峰会报名发送邮件
conference:
  email: ${email}
  emailPassword: ${emailPassword}

