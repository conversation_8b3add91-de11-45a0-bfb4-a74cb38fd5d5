package com.get.financecenter.dto;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class QuickReceiptFormDto extends BaseEntity {

    @NotNull(message = "应收计划Id不能为空")
    @ApiModelProperty(value = "应收计划Id")
    private Long receivablePlanId;

    @NotBlank(message = "应收类型关键字不能为空")
    @ApiModelProperty(value = "应收类型关键字，枚举，如：student_offer_item")
    private String fkTypeKey;
    /**
     * 应收类型对应记录Id，如：m_student_offer_item.id
     */
    @NotNull(message = "应收类型Id不能为空")
    @ApiModelProperty(value = "应收类型对应记录Id，如：m_student_offer_item.id")
    private Long fkTypeTargetId;

    @NotBlank(message = "请选择币种")
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "实收汇率")
    @NotNull(message = "实收汇率不能为空")
    private BigDecimal exchangeRate;

    @NotNull(message = "请填写收款金额")
    @ApiModelProperty(value = "收款金额")
    private BigDecimal receivedAmount=BigDecimal.ZERO;

    @NotNull(message = "请填写手续费")
    @ApiModelProperty(value = "手续费")
    private BigDecimal receivedFee=BigDecimal.ZERO;

    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;

}
