<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ConventionPersonAgentMapper">
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.ConventionPersonAgent">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_convention_person_id" jdbcType="BIGINT" property="fkConventionPersonId"/>
        <result column="fk_agent_id" jdbcType="BIGINT" property="fkAgentId"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <insert id="insert" parameterType="com.get.salecenter.entity.ConventionPersonAgent">
    insert into r_convention_person_agent (id, fk_convention_person_id, fk_agent_id, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkConventionPersonId,jdbcType=BIGINT}, #{fkAgentId,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.get.salecenter.entity.ConventionPersonAgent">
        insert into r_convention_person_agent
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkConventionPersonId != null">
                fk_convention_person_id,
            </if>
            <if test="fkAgentId != null">
                fk_agent_id,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkConventionPersonId != null">
                #{fkConventionPersonId,jdbcType=BIGINT},
            </if>
            <if test="fkAgentId != null">
                #{fkAgentId,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="getConventionPersonIds" parameterType="java.util.List" resultType="long">
        select
        fk_convention_person_id
        from
        r_convention_person_agent
        where
        fk_agent_id
        in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getAgentId" parameterType="java.lang.Long" resultType="long">
    select
    fk_agent_id
    from
    r_convention_person_agent
    where
    fk_convention_person_id = #{id}

  </select>

    <select id="isExistByAgentId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id from r_convention_person_agent where fk_agent_id=#{agentId}
    </select>
    <select id="getConventionPersonAgentNameMapByIds" resultType="com.get.salecenter.vo.ConventionPersonVo">
        SELECT
            b.id,
            CONCAT(c.`name`,IF(c.name_note is null or c.name_note = "","",CONCAT("（",c.name_note,"）"))) as company
        FROM
            r_convention_person_agent a
                LEFT JOIN m_convention_person b on a.fk_convention_person_id = b.id
                LEFT JOIN m_agent c on a.fk_agent_id = c.id
        where b.id in
        <foreach collection="personAgentIds" item="personAgentId" index="index" open="(" separator="," close=")">
            #{personAgentId}
        </foreach>
    </select>
</mapper>