package com.get.resumecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("m_resume_intention")
public class ResumeIntention extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 所属简历Id
     */
    @ApiModelProperty(value = "所属简历Id")
    @Column(name = "fk_resume_id")
    private Long fkResumeId;
    /**
     * 期望职位
     */
    @ApiModelProperty(value = "期望职位")
    @Column(name = "position")
    private String position;
    /**
     * 期望薪金
     */
    @ApiModelProperty(value = "期望薪金")
    @Column(name = "salary")
    private BigDecimal salary;
    /**
     * 期望工作地
     */
    @ApiModelProperty(value = "期望工作地")
    @Column(name = "work_place")
    private String workPlace;
    /**
     * 期望入职时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "期望入职时间")
    @Column(name = "take_office")
    private Date takeOffice;
    /**
     * 自我评价
     */
    @ApiModelProperty(value = "自我评价")
    @Column(name = "self_evaluation")
    private String selfEvaluation;
}