package com.get.aisplatformcenterap.vo;

import com.get.aisplatformcenterap.entity.MMessageEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MMessageVo extends MMessageEntity {
    @ApiModelProperty("平台名称")
    private String platFormName;

    @ApiModelProperty("公司名字")
    private String companyName;


    /*@ApiModelProperty(" 跳转方式名称 1自定义页面/2小程序页面")
    private String jumpModeName;

    public String getJumpModeName() {
        if(getJumpMode()!=null && getJumpMode().intValue()==1){
            jumpModeName="自定义页面";
        }else  if(getJumpMode()!=null && getJumpMode().intValue()==2){
            jumpModeName="小程序页面";
        }
        return jumpModeName;
    }*/
}
