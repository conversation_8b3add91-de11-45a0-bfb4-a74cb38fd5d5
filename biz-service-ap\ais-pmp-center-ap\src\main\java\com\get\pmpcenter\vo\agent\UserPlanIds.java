package com.get.pmpcenter.vo.agent;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/6/9
 * @Version 1.0
 * @apiNote:当前用户的代理方案和合同方案IDS
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserPlanIds {

    @ApiModelProperty(value = "代理方案IDS")
    List<Long> agentPlanIds;

    @ApiModelProperty(value = "合同方案IDS")
    List<Long> contractPlanIds;
}
