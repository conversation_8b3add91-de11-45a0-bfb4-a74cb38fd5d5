<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ClientSourceTypeMapper">

    <select id="getMaxViewOrder" resultType="java.lang.Integer">
        SELECT
            IFNULL(MAX(view_order) + 1, 0) view_order
        FROM u_client_source_type
    </select>
</mapper>