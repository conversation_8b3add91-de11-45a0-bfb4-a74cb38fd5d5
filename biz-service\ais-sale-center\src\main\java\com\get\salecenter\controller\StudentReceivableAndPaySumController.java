package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.vo.StudentReceivableAndPaySumVo;
import com.get.salecenter.vo.StudentReceivableAndPaySummaryVo;
import com.get.salecenter.service.IStudentReceivableAndPaySumService;
import com.get.salecenter.dto.StudentReceivableAndPaySumDto;
import com.get.salecenter.dto.query.StudentReceivableAndPaySumQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/11/22 10:22
 * @verison: 1.0
 * @description:
 */
@Api(tags = "学生应收应付汇总统计管理")
@RestController
@RequestMapping("sale/studentReceivableAndPaySum")
public class StudentReceivableAndPaySumController {


    @Resource
    private IStudentReceivableAndPaySumService studentReceivableAndPaySumService;


    /**
     * @Description: 学生应收应付汇总统计列表
     * @Author: Jerry
     * @Date:17:11 2021/11/19
     */
    @ApiOperation(value = "学生应收应付汇总统计列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理应付汇总统计管理/学生应收应付汇总统计列表")
    @PostMapping("/datas")
    public ResponseBo<StudentReceivableAndPaySumVo> datas(@RequestBody SearchBean<StudentReceivableAndPaySumQueryDto> page) {
        List<StudentReceivableAndPaySumVo> datas = studentReceivableAndPaySumService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "学生应收应付汇总统计列表（总计）", notes = "")
    @PostMapping("/getReceivableAndPayPaginationInfo")
    public ResponseBo<StudentReceivableAndPaySummaryVo> getReceivableAndPayPaginationInfo(@RequestBody SearchBean<StudentReceivableAndPaySumDto> page){
        StudentReceivableAndPaySumDto studentReceivableAndPaySumDto = page.getData();
        StudentReceivableAndPaySummaryVo data = studentReceivableAndPaySumService.getReceivableAndPayPaginationInfo(studentReceivableAndPaySumDto);
        return new ResponseBo<>(data);
    }

    @ApiOperation(value = "获取学生收付款信息(拷贝学生应收应付汇总统计列表逻辑,学生详情专用接口)", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理应付汇总统计管理/获取学生收付款信息(拷贝学生应收应付汇总统计列表逻辑,学生详情专用接口)")
    @PostMapping("/getStudentReceivableAndPaySum")
    public ResponseBo<StudentReceivableAndPaySumVo> getStudentReceivableAndPaySum(@RequestBody StudentReceivableAndPaySumQueryDto studentReceivableAndPaySumVo) {
        List<StudentReceivableAndPaySumVo> datas = studentReceivableAndPaySumService.getStudentReceivableAndPaySum(studentReceivableAndPaySumVo);
        return new ListResponseBo<>(datas);
    }

    /**
     * 导出提供商应收汇总统计列表Excel
     *
     * @return
     * @
     */
    @ApiOperation(value = "导出学生应收应付汇总统计列表Excel", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理应付汇总统计管理/导出学生应收应付汇总统计列表Excel")
    @PostMapping("/exportStudentReceivableAndPaySumExcel")
    @ResponseBody
    public void exportStudentReceivableAndPaySumExcel(HttpServletResponse response, @RequestBody StudentReceivableAndPaySumQueryDto studentReceivableAndPaySumVo) {
        studentReceivableAndPaySumService.exportStudentReceivableAndPaySumExcel(response, studentReceivableAndPaySumVo);
    }

//    /**
//     * 导出提供商应收汇总统计列表Excel
//     *
//     * @return
//     * @
//     */
//    @ApiOperation(value = "导出学生应收应付汇总统计列表Excel", notes = "")
//    @GetMapping("/exportStudentReceivableAndPaySumExcel")
//    @ResponseBody
//    public void exportStudentReceivableAndPaySumExcel(HttpServletResponse response)  {
//        StudentReceivableAndPaySumDto studentReceivableAndPaySumVo = new StudentReceivableAndPaySumDto();
//        studentReceivableAndPaySumVo.setFkCompanyId(1L);
//        studentReceivableAndPaySumService.exportStudentReceivableAndPaySumExcel(response,studentReceivableAndPaySumVo);
//    }

}
