package com.get.institutioncenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author:cream
 * @Date: 2023/7/13  14:41
 */
@Data
public class CourseOtherInfoDto {

    @ApiModelProperty("课程编号")
    private String courseNum;

    @ApiModelProperty("课程名称")
    private String courseName;

    @ApiModelProperty("学校名称")
    private String institutionName;

    @ApiModelProperty("学校中文名称")
    private String institutionChnName;

    @ApiModelProperty("学院名称")
    private String facultyName;

    @ApiModelProperty("学院中文名称")
    private String facultyChnName;

    @ApiModelProperty("课程类型名称")
    private String courseTypeName;

    @ApiModelProperty("课程类型中文名称")
    private String courseTypeChnName;

    @ApiModelProperty("课程级别名称")
    private String courseLevelName;

    @ApiModelProperty("课程级别中文名称")
    private String courseLevelChnName;

}
