package com.get.resumecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.resumecenter.entity.ResumeOther;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @DATE: 2020/8/11
 * @TIME: 14:11
 * @Description:
 **/
@Data
public class ResumeOtherVo extends BaseEntity {

    @ApiModelProperty(value = "主题类型")
    private String typeName;

    /**
     * 所属简历Id
     */
    @ApiModelProperty(value = "所属简历Id")
    @Column(name = "fk_resume_id")
    private Long fkResumeId;
    /**
     * 简历其他类型Id
     */
    @ApiModelProperty(value = "简历其他类型Id")
    @Column(name = "fk_other_type_id")
    private Long fkOtherTypeId;
    /**
     * 主题
     */
    @ApiModelProperty(value = "主题")
    @Column(name = "subject")
    private String subject;
    /**
     * 主题描述
     */
    @ApiModelProperty(value = "主题描述")
    @Column(name = "description")
    private String description;
}
