package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/3/5 14:17
 */
@Data
public class IFileInfoVo extends BaseEntity {

    @ApiModelProperty(value = "代理名")
    private String agentName;

    @ApiModelProperty(value = "代理Id")
    private String agentId;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "代理国家Id")
    private Long agentAreaCountryId;

    @ApiModelProperty(value = "代理国家编号")
    private String agentAreaCountryNum;

    @ApiModelProperty(value = "代理城市Id")
    private Long agentAreaCityId;

    @ApiModelProperty(value = "代理城市名")
    private String agentAreaCityName;

    @ApiModelProperty("员工Id")
    private Long fkStaffId;

    @ApiModelProperty(value = "银行账户名称")
    private String bankAccount;

    @ApiModelProperty(value = "人民币银行账户名称")
    private String cBankAccount;

    @ApiModelProperty(value = "银行账号")
    private String bankAccountNum;

    @ApiModelProperty(value = "人民币银行账号")
    private String cBankAccountNum;

    @ApiModelProperty(value = "银行名称")
    private String bankName;

    @ApiModelProperty(value = "人民币银行名称")
    private String cBankName;

    @ApiModelProperty(value = "银行支行名称")
    private String bankBranchName;

    @ApiModelProperty(value = "人民币银行支行名称")
    private String cBankBranchName;

    @ApiModelProperty(value = "银行币种编号")
    private String accountCurrencyTypeNum;

    @ApiModelProperty(value = "银行地址")
    private String bankAddress;

    @ApiModelProperty(value = "人民币银行地址")
    private String cBankAddress;

    @ApiModelProperty(value = "Swift Code")
    private String swiftCode;

    @ApiModelProperty(value = "其他转账编码")
    private String otherCode;

    @ApiModelProperty(value = "应付金额")
    private BigDecimal payableAmount;

    @ApiModelProperty(value = "应付币种")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "学生名")
    private String studentName;

    @ApiModelProperty(value = "应付金额拼接")
    private String payAmount;

    @ApiModelProperty(value = "兑换币种")
    private String exchangeCurrencyNum;

    @ApiModelProperty(value = "兑换汇率")
    private BigDecimal exchangeRate;

    @ApiModelProperty(value = "兑换金额")
    private BigDecimal exchangeAmount;

    @ApiModelProperty(value = "key")
    private String ftk;

    @ApiModelProperty(value = "性质：公司/个人/工作室/国际学校/其他")
    private String nature;

    @ApiModelProperty(value = "国家编码")
    private String areaCountryCode;


}
