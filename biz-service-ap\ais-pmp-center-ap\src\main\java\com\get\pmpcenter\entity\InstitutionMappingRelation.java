package com.get.pmpcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  11:30
 * @Version 1.0
 */
@Data
@TableName("m_institution_mapping_relation")
public class InstitutionMappingRelation extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "BMS学校提供商Id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "BMS学校Id")
    private Long fkInstitutionId;

    @ApiModelProperty(value = "对应旧系统学校名称")
    private String oldCollegeName;

    @ApiModelProperty(value = "对应旧系统学校id，文本记录")
    private String oldCollegeId;
}