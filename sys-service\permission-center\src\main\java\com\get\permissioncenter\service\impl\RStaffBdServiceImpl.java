package com.get.permissioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.dao.RStaffBdMapper;
import com.get.permissioncenter.dto.RStaffBdDto;
import com.get.permissioncenter.entity.RStaffBd;
import com.get.permissioncenter.service.IStaffService;
import com.get.permissioncenter.service.RStaffBdService;
import com.get.salecenter.dto.StaffBdCodeDto;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.salecenter.vo.StaffBdCodeVo;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 员工绑定bd 服务实现类
 */
@Service("permissionRStaffBdService")
public class RStaffBdServiceImpl extends ServiceImpl<RStaffBdMapper, RStaffBd> implements RStaffBdService {
    @Resource
    private IStaffService staffService;

    @Resource
    private UtilService utilService;

    @Resource
    private RStaffBdMapper rStaffBdMapper;

    @Resource
    private ISaleCenterClient saleCenterClient;

    @Override
    @Transactional
    public List<Long> addStaffBd(RStaffBdDto rStaffBdDto) {
        if (GeneralTool.isEmpty(rStaffBdDto.getFkStaffId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staffId_is_null"));
        }
        if (GeneralTool.isEmpty(rStaffBdDto.getFkStaffIdBds())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staffBdId_is_null"));
        }
        // 构建批量插入数据
        List<RStaffBd> entityList = rStaffBdDto.getFkStaffIdBds().stream()
                .map(staffIdBd -> {
                    RStaffBd rStaffBd = BeanCopyUtils.objClone(rStaffBdDto, RStaffBd::new);
                    rStaffBd.setFkStaffIdBd(staffIdBd);
                    utilService.setCreateInfo(rStaffBd);
                    return rStaffBd;
                })
                .collect(Collectors.toList());

        // 批量插入
        boolean success = saveBatch(entityList);
        if (!success) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }

        // 返回id
        return entityList.stream()
                .map(RStaffBd::getId)
                .collect(Collectors.toList());
    }



    /**
     * 根据id删除
     * @param id
     */
    @Override
    public void removeById(Long id) {
        if (GeneralTool.isEmpty(id)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        RStaffBd rStaffBd = rStaffBdMapper.selectById(id);
        if (GeneralTool.isEmpty(rStaffBd)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        rStaffBdMapper.deleteById(id);

    }

    @Override
    @Transactional
    public void batchDelete(RStaffBdDto rStaffBdDto) {
        if (GeneralTool.isEmpty(rStaffBdDto.getFkStaffId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staffId_is_null"));
        }
        if (GeneralTool.isEmpty(rStaffBdDto.getFkStaffIdBds())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staffBdId_is_null"));
        }

        List<RStaffBd> rStaffBds = rStaffBdMapper.selectList(new LambdaQueryWrapper<RStaffBd>().eq(RStaffBd::getFkStaffId, rStaffBdDto.getFkStaffId()).in(RStaffBd::getFkStaffIdBd, rStaffBdDto.getFkStaffIdBds()));
        if (GeneralTool.isEmpty(rStaffBds)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        Set<Long> ids = rStaffBds.stream().map(item -> item.getId()).collect(Collectors.toSet());
        rStaffBdMapper.deleteBatchIds(ids);
    }

    @Override
    public List<StaffBdCodeVo> dataList(SearchBean<StaffBdCodeDto> page) {
        StaffBdCodeDto staffBdCodeDto = page.getData();
        if (GeneralTool.isEmpty(staffBdCodeDto.getFkInstitutionPermissionGroupStaffIdBd())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staffId_is_null"));
        }
        List<StaffBdCodeVo> staffBdVos = saleCenterClient.getBdInformation(page).getData();
        if (GeneralTool.isEmpty(staffBdVos)) {
            return null;
        }
        return staffBdVos;
    }


}

