package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.financecenter.vo.ProviderTypeVo;
import com.get.financecenter.service.IProviderTypeService;
import com.get.financecenter.dto.ProviderTypeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/12/18 10:26
 * @verison: 1.0
 * @description:
 */
@Api(tags = "供应商类型管理")
@RestController
@RequestMapping("finance/providerType")
public class ProviderTypeController {
    @Resource
    private IProviderTypeService providerTypeService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.ProviderTypeDto>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/供应商类型管理/供应商类型详情")
    @GetMapping("/{id}")
    public ResponseBo<ProviderTypeVo> detail(@PathVariable("id") Long id) {
        ProviderTypeVo data = providerTypeService.findProviderTypeById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :批量新增信息
     * @Param [providerTypeVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/供应商类型管理/新增供应商类型")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(ProviderTypeDto.Add.class)  ValidList<ProviderTypeDto> providerTypeDtos) {
        providerTypeService.batchAdd(providerTypeDtos);
        return SaveResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/供应商类型管理/删除供应商类型")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        providerTypeService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.ProviderTypeDto>
     * @Description :修改信息
     * @Param [providerTypeVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/供应商类型管理/更新供应商类型")
    @PostMapping("update")
    public ResponseBo<ProviderTypeVo> update(@RequestBody @Validated(ProviderTypeDto.Update.class)  ProviderTypeDto providerTypeDto) {
        return UpdateResponseBo.ok(providerTypeService.updateProviderType(providerTypeDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.ProviderTypeDto>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/供应商类型管理/查询供应商类型")
    @PostMapping("datas")
    public ResponseBo<ProviderTypeVo> datas(@RequestBody SearchBean<ProviderTypeDto> page) {
        List<ProviderTypeVo> datas = providerTypeService.getProviderTypes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :上移下移
     * @Param [providerTypeVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/供应商类型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<ProviderTypeDto> providerTypeDtos) {
        providerTypeService.movingOrder(providerTypeDtos);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.ProviderTypeDto>
     * @Description :供应商类型下拉框数据
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "供应商类型下拉框数据", notes = "")
    @GetMapping("getProviderTypeList")
    public ResponseBo<ProviderTypeVo> getProviderTypeList() {
        List<ProviderTypeVo> providerTypeList = providerTypeService.getProviderTypeList();
        return new ListResponseBo<>(providerTypeList);
    }
}
