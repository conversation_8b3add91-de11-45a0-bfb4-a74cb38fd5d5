package com.get.common.query.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.get.common.query.model.QueryResult;
import com.get.common.query.monitor.QueryMetricsCollector;
import com.get.common.query.support.QueryCustomizer;
import com.get.common.result.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Map;

/**
 * 查询工具服务类
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Slf4j
@Service
@ConditionalOnBean(DynamicQueryBuilder.class)
@RequiredArgsConstructor
public class QueryUtils {
    
    private final DynamicQueryBuilder queryBuilder;
    private final QueryMetricsCollector metricsCollector;
    
    /**
     * 构建查询条件
     *
     * @param queryDto 查询DTO对象
     * @param entityClass 实体类Class
     * @param <T> 实体类型
     * @return 构建好的QueryWrapper
     */
    public <T> QueryWrapper<T> buildQuery(Object queryDto, Class<T> entityClass) {
        log.debug("开始构建查询条件 - DTO: {}, Entity: {}", 
            queryDto.getClass().getSimpleName(), entityClass.getSimpleName());
        
        QueryWrapper<T> wrapper = queryBuilder.buildQueryWrapper(queryDto, entityClass);
        
        log.debug("查询条件构建完成 - 生成的SQL片段数量: {}", wrapper.getExpression().getNormal().size());
        return wrapper;
    }

    /**
     * 构建查询条件并添加自定义条件
     *
     * @param queryDto 查询DTO对象
     * @param entityClass 实体类Class
     * @param customizer 自定义查询条件
     * @param <T> 实体类型
     * @return 构建好的QueryWrapper
     */
    public <T> QueryWrapper<T> buildQuery(Object queryDto, Class<T> entityClass, QueryCustomizer<T> customizer) {
        QueryWrapper<T> wrapper = buildQuery(queryDto, entityClass);
        
        if (customizer != null) {
            log.debug("应用自定义查询条件");
            customizer.customize(wrapper);
        }
        
        return wrapper;
    }
    
    /**
     * 构建分页查询条件
     *
     * @param queryDto 查询DTO对象
     * @param entityClass 实体类Class
     * @param page 分页参数
     * @param <T> 实体类型
     * @return 包含分页信息的查询结果
     */
    public <T> QueryResult<T> buildPageQuery(Object queryDto, Class<T> entityClass, Page page) {
        log.debug("开始构建分页查询 - 页码: {}, 页大小: {}", page.getCurrentPage(), page.getShowCount());
        
        QueryWrapper<T> wrapper = buildQuery(queryDto, entityClass);
        return new QueryResult<>(wrapper, page);
    }

    /**
     * 构建分页查询条件并添加自定义条件
     *
     * @param queryDto 查询DTO对象
     * @param entityClass 实体类Class
     * @param page 分页参数
     * @param customizer 自定义查询条件
     * @param <T> 实体类型
     * @return 包含分页信息的查询结果
     */
    public <T> QueryResult<T> buildPageQuery(Object queryDto, Class<T> entityClass, 
                                           Page page, QueryCustomizer<T> customizer) {
        QueryWrapper<T> wrapper = buildQuery(queryDto, entityClass, customizer);
        return new QueryResult<>(wrapper, page);
    }
    
    /**
     * 快速构建简单的等值查询
     *
     * @param entityClass 实体类Class
     * @param column 数据库字段名
     * @param value 查询值
     * @param <T> 实体类型
     * @return 构建好的QueryWrapper
     */
    public <T> QueryWrapper<T> buildSimpleQuery(Class<T> entityClass, String column, Object value) {
        log.debug("构建简单等值查询 - 字段: {}, 值: {}", column, value);
        
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        if (value != null) {
            wrapper.eq(column, value);
        }
        return wrapper;
    }

    /**
     * 快速构建模糊查询
     *
     * @param entityClass 实体类Class
     * @param column 数据库字段名
     * @param value 查询值
     * @param <T> 实体类型
     * @return 构建好的QueryWrapper
     */
    public <T> QueryWrapper<T> buildLikeQuery(Class<T> entityClass, String column, String value) {
        log.debug("构建模糊查询 - 字段: {}, 值: {}", column, value);
        
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        if (value != null && !value.trim().isEmpty()) {
            wrapper.like(column, value.trim());
        }
        return wrapper;
    }

    /**
     * 快速构建IN查询
     *
     * @param entityClass 实体类Class
     * @param column 数据库字段名
     * @param values 查询值集合
     * @param <T> 实体类型
     * @return 构建好的QueryWrapper
     */
    public <T> QueryWrapper<T> buildInQuery(Class<T> entityClass, String column, Collection<?> values) {
        log.debug("构建IN查询 - 字段: {}, 值数量: {}", column, values != null ? values.size() : 0);
        
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        if (values != null && !values.isEmpty()) {
            wrapper.in(column, values);
        }
        return wrapper;
    }
    
    /**
     * 获取查询构建器的缓存统计信息
     *
     * @return 缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        return queryBuilder.getCacheStats();
    }
    
    /**
     * 获取指定DTO类的详细诊断信息
     *
     * @param queryDtoClass 查询DTO类
     * @return 诊断信息
     */
    public Map<String, Object> getDiagnosticInfo(Class<?> queryDtoClass) {
        return queryBuilder.getDiagnosticInfo(queryDtoClass);
    }
    
    /**
     * 获取性能指标
     *
     * @return 性能指标信息
     */
    public Map<String, Object> getPerformanceMetrics() {
        return metricsCollector.getAllMetrics();
    }
    
    /**
     * 获取性能报告
     *
     * @return 性能报告
     */
    public Map<String, Object> getPerformanceReport() {
        return metricsCollector.getPerformanceReport();
    }
    
    /**
     * 清空查询构建器缓存
     */
    public void clearCache() {
        log.info("清空动态查询缓存");
        queryBuilder.clearCache();
    }
    
    /**
     * 验证查询DTO类的配置
     *
     * @param queryDtoClass 查询DTO类
     * @return 验证结果信息
     */
    public Map<String, Object> validateQueryDto(Class<?> queryDtoClass) {
        log.debug("验证查询DTO配置 - 类: {}", queryDtoClass.getSimpleName());
        return queryBuilder.getDiagnosticInfo(queryDtoClass);
    }
    
}