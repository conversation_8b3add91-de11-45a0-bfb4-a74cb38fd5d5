<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.RStudentToClientApprovalMapper">

    <select id="getClientApprovalList" resultType="com.get.salecenter.vo.ClientApprovalVo">
        select rstca.*,
        s.num as fkStudentNum,
        DATE_FORMAT(s.birthday, '%Y-%m-%d') as birthday,
        CONCAT(s.`name`, '（', IF(s.last_name IS NULL, '', s.last_name), ' ', s.first_name, '）' ) AS fkStudentName,
        GROUP_CONCAT(a.name) as fkAgentName,
        dep.name as departmentName
        from r_student_to_client_approval rstca
        left join m_student s on s.id = rstca.fk_student_id
        left join r_student_agent sa on s.id = sa.fk_student_id and sa.is_active = 1
        left join m_agent a on a.id = sa.fk_agent_id and a.is_active = 1
        left join ais_permission_center.m_staff sta on sta.id = rstca.fk_staff_id_apply
        left join ais_permission_center.m_department dep on sta.fk_department_id = dep.id
        where 1=1
        <if test="clientApprovalDto.fkDepartmentId != null">
            and dep.id = #{clientApprovalDto.fkDepartmentId}
        </if>
        <if test="clientApprovalDto.fkCompanyId != null and clientApprovalDto.fkCompanyId != ''">
            and rstca.fk_company_id = #{clientApprovalDto.fkCompanyId}
        </if>
        <if test="clientApprovalDto.approvalStatus != null">
            and rstca.approval_status = #{clientApprovalDto.approvalStatus}
        </if>
        <if test="clientApprovalDto.fkStudentNum != null and clientApprovalDto.fkStudentNum != ''">
            and s.num LIKE CONCAT('%', #{clientApprovalDto.fkStudentNum}, '%')
        </if>
        <if test="clientApprovalDto.fkStudentName !=null and clientApprovalDto.fkStudentName != ''">
            and (
            REPLACE(CONCAT(s.first_name,s.last_name),' ','') like concat('%',#{clientApprovalDto.fkStudentName},'%')
            OR REPLACE(CONCAT(IF(s.last_name IS NULL, '', s.last_name),s.first_name),' ','') like concat('%',#{clientApprovalDto.fkStudentName},'%')
            OR REPLACE(s.`name`,' ','') like concat('%',#{clientApprovalDto.fkStudentName},'%')
            OR REPLACE(s.last_name,' ','') like concat('%',#{clientApprovalDto.fkStudentName},'%')
            OR REPLACE(s.first_name,' ','') like concat('%',#{clientApprovalDto.fkStudentName},'%'))
        </if>
        <if test="clientApprovalDto.fkAgentName != null and clientApprovalDto.fkAgentName != ''">
            AND a.name LIKE CONCAT('%', #{clientApprovalDto.fkAgentName}, '%')
        </if>
        <choose>
            <when test="isStaffIdApproval == true">
                and ( 1=1
                <if test="loginIds !=null and loginIds.size()>0">
                    and rstca.gmt_create_user in
                    <foreach collection="loginIds" item="loginId" open="(" separator="," close=")">
                        #{loginId}
                    </foreach>
                </if>
                <if test="staffFollowerIds !=null and staffFollowerIds.size()>0">
                    or rstca.fk_staff_id_approval in
                    <foreach collection="staffFollowerIds" item="staffFollowerId" open="(" separator="," close=")">
                        #{staffFollowerId}
                    </foreach>
                </if>
                )
            </when>
            <otherwise>
                <if test="loginIds !=null and loginIds.size()>0">
                    and rstca.gmt_create_user in
                    <foreach collection="loginIds" item="loginId" open="(" separator="," close=")">
                        #{loginId}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        GROUP BY rstca.id
        ORDER BY rstca.gmt_create desc,rstca.id
    </select>
</mapper>