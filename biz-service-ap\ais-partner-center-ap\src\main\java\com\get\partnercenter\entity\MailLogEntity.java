package com.get.partnercenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date: 2024-12-20 14:10:35
 */

@Data
@TableName("log_mail_partner_user_account")
@Builder
public class MailLogEntity extends BaseEntity {


    @ApiModelProperty("操作类型：1新建/2重置密码")
    private Integer optType;

    @ApiModelProperty("发件人登录账号")
    private String fromUser;

    @ApiModelProperty("发件人电邮地址")
    private String fromEmail;

    @ApiModelProperty("收件人名称（代理联系人名称/伙伴账号名称）")
    private String toUser;

    @ApiModelProperty("收件人电邮地址")
    private String toEmail;

    @ApiModelProperty("邮件标题")
    private String subject;

    @ApiModelProperty("邮件内容")
    private String body;

    @ApiModelProperty("邮件发送时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendDate;

    @ApiModelProperty("发送状态：0失败/1成功'")
    private Integer sendStatus;

    @ApiModelProperty("发送信息，可以记录失败原因")
    private String sendMessage;

    @ApiModelProperty("学生代理Id")
    private Long fkAgentId ;

}
