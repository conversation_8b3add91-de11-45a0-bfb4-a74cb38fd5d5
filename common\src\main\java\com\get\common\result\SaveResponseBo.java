package com.get.common.result;

/**
 * 添加数据-响应类
 *
 * @author: jack
 * @create: 2020-4-15
 * @verison: 1.0
 * @description: 添加数据-响应类
 */
public class SaveResponseBo extends ResponseBo {
    private final static String ID = "id";
    private static final long serialVersionUID = -6240413555157179742L;

    public SaveResponseBo(Long id) {
        super();
        this.put(ID, id);
    }

    /**
     * 新增成功，返回id
     *
     * @param id 新增数据的id
     * @return 返回SaveResponBo对象
     */
    public static SaveResponseBo ok(Long id) {
        return new SaveResponseBo(id);
    }

}
