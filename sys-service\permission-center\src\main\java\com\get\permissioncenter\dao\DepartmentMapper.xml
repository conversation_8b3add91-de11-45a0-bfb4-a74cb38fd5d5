<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.permissioncenter.dao.DepartmentMapper">

    <insert id="insertSelective" parameterType="com.get.permissioncenter.entity.Department" useGeneratedKeys="true"
            keyProperty="id">
        insert into m_department
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkCompanyId != null">
                fk_company_id,
            </if>
            <if test="num != null">
                num,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="viewOrder != null">
                view_order,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkCompanyId != null">
                #{fkCompanyId,jdbcType=BIGINT},
            </if>
            <if test="num != null">
                #{num,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="viewOrder != null">
                #{viewOrder,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="getMaxViewOrder" resultType="java.lang.Integer">
      select
        max(view_order)+1 view_order
      from
      m_department
    </select>

    <select id="getDepartmentSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
      select
       id,num,name
      from
       m_department
      where
       fk_company_id = #{id}
      ORDER BY
       view_order asc
    </select>

    <select id="getDepartmentNameList" resultType="java.lang.String">
        select
        name
        from
        m_department
        where
        num
        in
        <foreach item="item" index="index" collection="departmentNumList" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>

    <select id="isExistByCompanyId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id FROM m_department where fk_company_id =#{companyId}
    </select>

    <select id="getDepartmentNameById" resultType="java.lang.String">
        select
         name
        from
         m_department
        where
         id = #{departmentId}

    </select>
</mapper>