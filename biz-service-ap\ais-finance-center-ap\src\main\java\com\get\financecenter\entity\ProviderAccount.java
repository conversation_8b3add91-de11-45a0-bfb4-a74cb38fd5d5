package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("m_provider_account")
public class ProviderAccount extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 供应商Id
     */
    @ApiModelProperty(value = "供应商Id")
    private Long fkProviderId;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;
    /**
     * 银行账户名称
     */
    @ApiModelProperty(value = "银行账户名称")
    private String bankAccount;
    /**
     * 银行账号
     */
    @ApiModelProperty(value = "银行账号")
    private String bankAccountNum;
    /**
     * 银行名称
     */
    @ApiModelProperty(value = "银行名称")
    private String bankName;
    /**
     * 银行支行名称
     */
    @ApiModelProperty(value = "银行支行名称")
    private String bankBranchName;
    /**
     * 银行地址国家Id
     */
    @ApiModelProperty(value = "银行地址国家Id")
    private Long fkAreaCountryId;
    /**
     * 银行地址州省Id
     */
    @ApiModelProperty(value = "银行地址州省Id")
    private Long fkAreaStateId;
    /**
     * 银行地址城市Id
     */
    @ApiModelProperty(value = "银行地址城市Id")
    private Long fkAreaCityId;
    /**
     * 银行地址城市区域Id
     */
    @ApiModelProperty(value = "银行地址城市区域Id")
    private Long fkAreaCityDivisionId;
    /**
     * 银行地址
     */
    @ApiModelProperty(value = "银行地址")
    private String bankAddress;
    /**
     * Swift Code
     */
    @ApiModelProperty(value = "Swift Code")
    private String swiftCode;
    /**
     * 其他转账编码
     */
    @ApiModelProperty(value = "其他转账编码")
    private String otherCode;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @UpdateWithNull
    private String remark;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;
}