package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_student_offer_item_settlement_status")
public class StudentOfferItemSettlementStatus extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 学生申请方案项目Id
     */
    @ApiModelProperty(value = "学生申请方案项目Id")
    @Column(name = "fk_student_offer_item_id")
    private Long fkStudentOfferItemId;
    /**
     * 结算状态：0未结算/1结算中/2代理确认/3财务确认
     */
    @ApiModelProperty(value = "结算状态：0未结算/1结算中/2代理确认/3财务确认")
    @Column(name = "status_settlement")
    private Integer statusSettlement;
}