package com.get.financecenter.dto.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
public class ReceiptFormQueryDto {

    
            
    @ApiModelProperty(value = "公司ids")
    private List<Long> fkCompanyIds;

    
            
    @ApiModelProperty(value = "目标类型关键字，枚举：m_institution_provider学校供应商/m_student学生")
    @NotBlank(message = "目标类型不能为空")
    private String fkTypeKey;

    
            
    @ApiModelProperty(value = "对应记录Id")
    @NotNull(message = "对应记录Id不能为空")
    private Long fkTypeTargetId;

            
    @ApiModelProperty(value = "关键词")
    private String keyWord;

    
            
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String startTime;

    
            
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String endTime;

    @ApiModelProperty(value = "到账开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String startReceiptTime;

    @ApiModelProperty(value = "到账结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String endReceiptTime;

    @ApiModelProperty(value = "银行帐号Id（公司）")
    private Long fkBankAccountId;

    @ApiModelProperty(value = "代理佣金结算状态：0待结算/1可结算，默认为0")
    @NotNull(message = "代理佣金结算状态：0待结算/1可结算不能为空")
    private Integer settlementStatus;

            
    @ApiModelProperty(value = "绑定状态（0：未绑定 1：绑定部分 2：绑定完成）")
    private Integer bindingStatus;

            
    @ApiModelProperty(value = "收费金额（含手续费）开始范围")
    private BigDecimal amountStart;

            
    @ApiModelProperty(value = "收费金额（含手续费）结束范围")
    private BigDecimal amountEnd;


    @ApiModelProperty(value = "状态：0作废/1打开")
    private Integer status;

    
            
    @ApiModelProperty(value = "是否隐藏补单数据 0否/1是")
    private Boolean isHideSupplementary;

    @ApiModelProperty(value = "公司Id", required = true)
    private Long fkCompanyId;

    @ApiModelProperty(value = "发票号")
    private String fkInvoiceNum;


    
    
}
