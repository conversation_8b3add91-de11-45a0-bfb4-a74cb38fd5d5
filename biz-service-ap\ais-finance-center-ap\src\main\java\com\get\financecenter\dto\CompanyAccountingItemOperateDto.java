package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * 公司科目
 */
@Data
public class CompanyAccountingItemOperateDto {

    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "新增科目Ids")
    private List<Long> addFkAccountingItemIds;

    @ApiModelProperty(value = "删除科目Ids")
    private List<Long> deleteFkAccountingItemIds;

}

