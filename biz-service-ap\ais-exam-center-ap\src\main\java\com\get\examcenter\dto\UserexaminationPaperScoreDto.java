package com.get.examcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


///**
// * Created by <PERSON>.
// * Time: 10:54
// * Date: 2021/8/25
// * Description:考试记录Vo
// */
@Data
public class UserexaminationPaperScoreDto extends BaseVoEntity implements Serializable {
    /**
     * 注册中心的user id
     */
    @ApiModelProperty(value = "注册中心的user id")
    private Long fkUserId;


    /**
     * 操作id
     */
    @ApiModelProperty(value = "操作id")
    private String optGuid;
}
