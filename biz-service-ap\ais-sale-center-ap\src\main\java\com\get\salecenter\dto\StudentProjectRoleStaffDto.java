package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/11/16
 * @TIME: 10:30
 * @Description:
 **/
@Data
public class StudentProjectRoleStaffDto extends BaseVoEntity {
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    @NotNull(message = "表名不能为空", groups = {Add.class})
    private String fkTableName;

    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id", required = true)
    private Long fkTableId;

    /**
     * 学生项目角色Id
     */
    @NotNull(message = "学生项目角色Id不能为空", groups = {Add.class})
    @ApiModelProperty(value = "学生项目角色Id", required = true)
    private Long fkStudentProjectRoleId;

    /**
     * 员工Id
     */
    @NotNull(message = "员工Id不能为空", groups = {Add.class})
    @ApiModelProperty(value = "员工Id", required = true)
    private Long fkStaffId;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

    /**
     * 绑定时间
     */
    @ApiModelProperty(value = "绑定时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date activeDate;

    /**
     * 取消绑定时间（下次绑定时，需要重新建立记录）
     */
    @ApiModelProperty(value = "取消绑定时间（下次绑定时，需要重新建立记录）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date unactiveDate;


    /**
     * 学生Id()
     */
    @NotNull(message = "学生Id不能为空")
    @ApiModelProperty(value = "学生Id")
    private Long fkStudentId;

    @ApiModelProperty(value = "offerIds")
    private List<Long> offerIds;
    @ApiModelProperty(value = "accommodationIds")
    private List<Long> accommodationIds;
    @ApiModelProperty(value = "insuranceIds")
    private List<Long> insuranceIds;
    @ApiModelProperty(value = "clientOfferIds")
    private List<Long> clientOfferIds;

    
}
