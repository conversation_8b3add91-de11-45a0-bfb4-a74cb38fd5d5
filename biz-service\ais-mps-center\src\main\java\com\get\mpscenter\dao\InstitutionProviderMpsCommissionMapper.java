package com.get.mpscenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.mpscenter.dto.MpsCommissionDto;
import com.get.mpscenter.vo.InstitutionProviderCommissionVo;
import com.get.mpscenter.vo.InstitutionProviderMpsCommissionVo;
import com.get.mpscenter.entity.InstitutionProviderMpsCommission;
import com.get.mpscenter.dto.InstitutionProviderMpsCommissionDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstitutionProviderMpsCommissionMapper extends BaseMapper<InstitutionProviderMpsCommission>, GetMapper<InstitutionProviderMpsCommission> {

    List<InstitutionProviderMpsCommissionVo> getCommissionList(IPage<InstitutionProviderMpsCommissionVo> iPage, @Param("institutionProviderMpsCommissionDto") InstitutionProviderMpsCommissionDto institutionProviderMpsCommissionDto);

    List<InstitutionProviderCommissionVo> getProviderInstitutionList(@Param("fkInstitutionId")Long fkInstitutionId, @Param("fkInstitutionProviderId")Long fkInstitutionProviderId);

    List<BaseSelectEntity> getYearList();

    List<BaseSelectEntity> getInstitutionProviderList(@Param("fkInstitutionId")Long fkInstitutionId);

    List<BaseSelectEntity> getInstitutionList(@Param("fkInstitutionProviderId")Long fkInstitutionProviderId);

    List<InstitutionProviderMpsCommissionVo> unActiveCommissionList(IPage<InstitutionProviderMpsCommission> iPage, @Param("mpsCommissionVo") MpsCommissionDto mpsCommissionVo);
}
