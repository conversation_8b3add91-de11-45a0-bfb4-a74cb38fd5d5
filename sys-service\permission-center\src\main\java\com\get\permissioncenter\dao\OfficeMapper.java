package com.get.permissioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.permissioncenter.entity.Office;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface OfficeMapper extends BaseMapper<Office> {

    /**
     * 查询员工的业务办公室
     *
     * @param staffId
     * @return
     */
    List<Office> getStaffOfficeById(@Param("staffId") Long staffId);

    /**
     * 查找排序最大值
     *
     * @return
     */
    Integer getMaxViewOrder();


    /**
     * @return java.util.List<com.get.common.com.get.permissioncenter.vo.entity.BaseSelectEntity>
     * @Description: 下拉
     * @Param [companyId]
     * <AUTHOR>
     */
    List<BaseSelectEntity> getOfficeSelect(@Param("companyId") Long companyId);


    int insertSelective(Office office);

    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [staffId]
     * <AUTHOR>
     */
    Boolean isExistByCompanyId(Long companyId);

    /**
     * @return java.lang.String
     * @Description :根据办公室id查找办公室名称
     * @Param [officeId]
     * <AUTHOR>
     */
    String getOfficeNameById(@Param("officeId") Long officeId);
}