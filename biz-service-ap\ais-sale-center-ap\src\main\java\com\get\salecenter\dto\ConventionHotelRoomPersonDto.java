package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: Sea
 * @create: 2020/8/21 16:31
 * @verison: 1.0
 * @description:
 */
@Data
public class ConventionHotelRoomPersonDto extends BaseVoEntity{
    /**
     * 酒店房间Id
     */
    @NotNull(message = "酒店房间Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "酒店房间Id", required = true)
    private Long fkConventionHotelRoomId;

    /**
     * 峰会参展人员Id
     */
    @ApiModelProperty(value = "峰会参展人员Id")
    private Long fkConventionPersonId;

    //自定义内容
    /**
     * 相同需求是否安排到其他日期房间
     */
    @ApiModelProperty(value = "相同需求是否安排到其他日期房间")
    private Boolean key;

    /**
     * 酒店房型id
     */
    @ApiModelProperty(value = "酒店房型id")
    private Long conventionHotelId;

    /**
     * 系统房间编号
     */
    @ApiModelProperty(value = "系统房间编号")
    private String systemRoomNum;

    /**
     * 是否签到
     */
    @ApiModelProperty(value = "是否签到")
    private Boolean isAttend;

    /**
     * 酒店房号
     */
    @ApiModelProperty(value = "酒店房号")
    private String hotelRoomNum;

   
}
