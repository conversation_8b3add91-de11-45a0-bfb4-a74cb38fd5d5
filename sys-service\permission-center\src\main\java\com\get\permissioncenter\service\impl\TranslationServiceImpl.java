package com.get.permissioncenter.service.impl;

import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.translation.baidu.result.TransVo;
import com.get.core.translation.baidu.utils.HtmlUtils;
import com.get.permissioncenter.service.ITranslationService;
import org.springframework.stereotype.Service;

/**
 * @author: Hardy
 * @create: 2021/6/7 17:25
 * @verison: 1.0
 * @description:
 */
@Service
public class TranslationServiceImpl implements ITranslationService {

    @Override
    public String getTranslation(TransVo transVo) {
        transVo.setFrom(transformFromOrTo(transVo.getFrom()));
        transVo.setTo(transformFromOrTo(transVo.getTo()));
        try {
            return HtmlUtils.htmlTranslate(transVo.getQuery(), transVo.getFrom(), transVo.getTo());
            //return TransApi.translate(transVo.getQuery(),transVo.getFrom(),transVo.getTo());
        } catch (Exception e) {
            e.printStackTrace();
            throw new GetServiceException(LocaleMessageUtils.getMessage("translate_fail"));
        }
    }

    private String transformFromOrTo(String language) {
        if ("zh-cn".equals(language)) {
            language = "zh";
        }
        if ("en-us".equals(language)) {
            language = "en";
        }
        if ("zh-hk".equals(language) || "zh-tw".equals(language)) {
            language = "cht";
        }
        return language;
    }
}
