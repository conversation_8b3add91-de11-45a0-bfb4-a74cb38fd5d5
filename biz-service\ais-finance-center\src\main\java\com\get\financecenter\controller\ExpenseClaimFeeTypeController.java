package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.financecenter.dto.ExpenseClaimFeeTypeDto;
import com.get.financecenter.service.IExpenseClaimFeeTypeService;
import com.get.financecenter.vo.ExpenseClaimFeeTypeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/6 16:54
 * @verison: 1.0
 * @description:
 */
@Api(tags = "报销费用类型管理")
@RestController
@RequestMapping("finance/expenseClaimFeeType")
public class ExpenseClaimFeeTypeController {
    @Resource
    private IExpenseClaimFeeTypeService expenseClaimFeeTypeService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.ExpenseClaimFeeTypeDto>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/费用报销单类型管理/费用报销单类型详情")
    @GetMapping("/{id}")
    public ResponseBo<ExpenseClaimFeeTypeVo> detail(@PathVariable("id") Long id) {
        ExpenseClaimFeeTypeVo data = expenseClaimFeeTypeService.findExpenseClaimFeeTypeById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :批量新增信息
     * @Param [expenseClaimFeeTypeVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/费用报销单类型管理/新增费用报销单类型")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(ExpenseClaimFeeTypeDto.Add.class)  ValidList<ExpenseClaimFeeTypeDto> expenseClaimFeeTypeDtos) {
        expenseClaimFeeTypeService.batchAdd(expenseClaimFeeTypeDtos);
        return SaveResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/费用报销单类型管理/删除费用报销单类型")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        expenseClaimFeeTypeService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.ExpenseClaimFeeTypeDto>
     * @Description :修改信息
     * @Param [expenseClaimFeeTypeVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/费用报销单类型管理/更新费用报销单类型")
    @PostMapping("update")
    public ResponseBo<ExpenseClaimFeeTypeVo> update(@RequestBody  @Validated(ExpenseClaimFeeTypeDto.Update.class) ExpenseClaimFeeTypeDto expenseClaimFeeTypeDto) {
        return UpdateResponseBo.ok(expenseClaimFeeTypeService.updateExpenseClaimFeeType(expenseClaimFeeTypeDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.ExpenseClaimFeeTypeDto>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/费用报销单类型管理/查询费用报销单类型")
    @PostMapping("datas")
    public ResponseBo<ExpenseClaimFeeTypeVo> datas(@RequestBody SearchBean<ExpenseClaimFeeTypeDto> page) {
        List<ExpenseClaimFeeTypeVo> datas = expenseClaimFeeTypeService.getExpenseClaimFeeTypes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :上移下移
     * @Param [expenseClaimFeeTypeVos]
     * <AUTHOR>
     */
//    @ApiOperation(value = "上移下移（交换）", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/费用报销单类型管理/移动顺序")
//    @PostMapping("movingOrder")
//    public ResponseBo movingOrder(@RequestBody List<ExpenseClaimFeeTypeDto> expenseClaimFeeTypeDtos) {
//        expenseClaimFeeTypeService.sort(expenseClaimFeeTypeDtos);
//        return ResponseBo.ok();
//    }
    @ApiOperation(value = "排序（拖拽）", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/费用报销单类型管理/排序（拖拽）")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestParam("start") Integer start , @RequestParam("end") Integer end) {
        expenseClaimFeeTypeService.movingOrder(start, end);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.ExpenseClaimFeeTypeDto>
     * @Description :费用报销单类型下拉框数据
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "费用报销单类型下拉框数据", notes = "")
    @GetMapping("getExpenseClaimFeeTypeList")
    public ResponseBo<ExpenseClaimFeeTypeVo> getExpenseClaimFeeTypeList() {
        List<ExpenseClaimFeeTypeVo> expenseClaimFeeTypeList = expenseClaimFeeTypeService.getExpenseClaimFeeTypeSelect();
        return new ListResponseBo<>(expenseClaimFeeTypeList);
    }

}
