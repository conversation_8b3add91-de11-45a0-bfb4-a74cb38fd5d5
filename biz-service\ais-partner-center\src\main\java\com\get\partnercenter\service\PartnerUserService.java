package com.get.partnercenter.service;

import com.get.common.result.Page;
import com.get.partnercenter.dto.*;
import com.get.partnercenter.vo.*;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/20  11:03
 * @Version 1.0
 */
public interface PartnerUserService {

    /**
     * 获取大区列表
     *
     * @return
     */
    List<ReginVo> getRegionList();

    /**
     * 获取代理账号列表
     *
     * @param params
     * @param page
     * @return
     */
    List<AgentAccountVo> agentAccountPage(AgentPageDto params, Page page);


    /**
     * 获取邮件发送日志
     *
     * @param page
     * @return
     */
    List<MailLogVo> mailLogPage(MailLogDto mailLogDto, Page page);

    /**
     * 启用/停用代理
     *
     * @param statusDto
     */
    void saveAgentStatus(StatusDto statusDto);

    /**
     * 代理下的子账号列表
     *
     * @param idDto
     * @param page
     * @return
     */
    List<PartnerUserVo> partnerUserPage(IdDto idDto, Page page);

    /**
     * 代理子账号启用禁用
     *
     * @param statusDto
     */
    void saveAccountStatus(StatusDto statusDto);

    /**
     * 代理子账号重置密码
     *
     * @param idDto
     */
    String resetPassword(IdDto idDto);


    /**
     * 新建代理子账号
     *
     * @param savePartnerUserDto
     */
    Long savePartnerUser(Long agentId, Long companyId, SavePartnerUserDto savePartnerUserDto, String password,String loginId, List<String> existEmail);

    /**
     * 代理联系人列表
     *
     * @param agentId
     * @return
     */
    List<AgentContactVo> getAgentContactList(Long agentId);

    /**
     * 获取BD列表
     *
     * @return
     */
    List<BdVo> getBdPage(BdPageDto bdPageDto, Page page);

    /**
     * 发送邮件
     *
     * @param sendEmailDto
     */
    Boolean sendMail(SendEmailDto sendEmailDto);

    /**
     * 获取邮件发送日志详情
     *
     * @param id
     * @return
     */
    MailLogVo getMailLogById(Long id);

    /**
     * 获取代理列表
     *
     * @return
     */
    List<BdVo> getAgentList();

    /**
     * 获取员工列表
     *
     * @return
     */
    List<BdVo> getStaffList();

    /**
     * 用户批量下线
     *
     * @param userIds
     */
    void userOffline(List<String> userEmails);

    /**
     * 清理用户缓存
     *
     * @param partnerUserIds 伙伴用户ID列表
     */
    void clearUserCache(List<Long> partnerUserIds);

    /**
     * 注册伙伴用户-不发送邮件
     * @param list
     * @return
     */
    List<RegisterPartnerUserVo> registerPartnerUser(List<RegisterPartnerUserDto> list);

}
