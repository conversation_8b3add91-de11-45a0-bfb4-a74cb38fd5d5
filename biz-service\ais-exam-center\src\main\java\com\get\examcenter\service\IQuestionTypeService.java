package com.get.examcenter.service;

import com.get.common.result.SearchBean;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.examcenter.vo.QuestionTypeVo;
import com.get.examcenter.dto.QuestionTypeDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/8/24 17:05
 */
public interface IQuestionTypeService {


    Long saveOrUpdataQuestionType(QuestionTypeDto questionTypeDto);

    List<QuestionTypeVo> getQuestionTypeList(QuestionTypeDto data, SearchBean<QuestionTypeDto> page);

    void deleteQuestionType(Long id);

    /**
     * @Description: 详情
     * @Author: Jerry
     * @Date:10:28 2021/8/27
     */
    QuestionTypeVo detail(Long id);

    /**
     * @Description: feign调用 根据考题类型ids获取名称
     * @Author: Jerry
     * @Date:10:18 2021/8/27
     */
    Map<Long, String> getNamesByQuestionTypeIds(Set<Long> questionTypeIds);


    /**
     * @Description: 考题类型下拉框
     * @Author: Jerry
     * @Date:9:48 2021/9/3
     */
    List<BaseSelectEntity> questionTypeSelect(String fkCompanyId);


    /**
     * @Description: 上移下移
     * @Author: Jerry
     * @Date:9:53 2021/9/13
     */
    void movingOrder(List<QuestionTypeDto> questionTypeDtos);
}
