package com.get.authentication.granter;

import com.alibaba.fastjson.JSONObject;
import com.get.authentication.service.GetUserDetails;
import com.get.authentication.utils.TokenUtil;
import com.get.common.utils.CommonUtil;
import com.get.core.redis.cache.GetRedis;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.vo.StaffInfoVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.oauth2.common.exceptions.InvalidGrantException;
import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.OAuth2RequestFactory;
import org.springframework.security.oauth2.provider.TokenRequest;
import org.springframework.security.oauth2.provider.token.AbstractTokenGranter;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @author: Hardy
 * @create: 2022/8/8 15:44
 * @verison: 1.0
 * @description:
 */
/**
 * 企业微信认证类
 */
@Slf4j
public class WxCpTokenGranter extends AbstractTokenGranter {

    private final static String GRANT_TYPE = "wxcptp";

    private final AuthenticationManager authenticationManager;

    private final IPermissionCenterClient permissionCenterClient;

    private GetRedis getRedis;

    protected WxCpTokenGranter(AuthorizationServerTokenServices tokenServices,
                               ClientDetailsService clientDetailsService,
                               OAuth2RequestFactory requestFactory,
                               AuthenticationManager authenticationManager,
                               IPermissionCenterClient permissionCenterClient,
                               GetRedis getRedis) {
        super(tokenServices, clientDetailsService, requestFactory, GRANT_TYPE);
        this.authenticationManager = authenticationManager;
        this.permissionCenterClient = permissionCenterClient;
        this.getRedis = getRedis;
    }


    @SneakyThrows
    @Override
    protected OAuth2Authentication getOAuth2Authentication(ClientDetails client, TokenRequest tokenRequest) {
        Map<String, String> parameters = new LinkedHashMap<>(tokenRequest.getRequestParameters());
        String code = parameters.get("code");
        String platformType = parameters.get("platformType");

        if(GeneralTool.isEmpty(code))
        {
            throw new InvalidGrantException("微信code不存在");
        }
        if(GeneralTool.isEmpty(platformType))
        {
            throw new InvalidGrantException("缺少必备的请求参数，请联系管理员");
        }

        //远程调用，通过注册中心获取用户登录信息
        Result<StaffInfoVo> result  = permissionCenterClient.wxCpLogin(code,platformType);
        log.info("wxCpLogin result:{}",JSONObject.toJSONString(result));
        GetUserDetails getUserDetails = null;
        if (result.isSuccess()) {
            StaffInfoVo staffInfoVo = result.getData();
            if (staffInfoVo == null) {
                throw new UsernameNotFoundException(TokenUtil.USER_NOT_FOUND);
            }
            //返回基本登录信息，用户完整信息通过getUser接口独立获取，这样避免修改用户信息后每次都要重新登录
            String oauthId = "";//扩展第三方登录时使用
            getUserDetails = new GetUserDetails(
                    staffInfoVo.getId(),
                    CommonUtil.getTokenSessionId(),
                    staffInfoVo.getLoginId(),
                    staffInfoVo.getLoginPs(),
                    true,
                    true,
                    true,
                    true,
                    AuthorityUtils.commaSeparatedStringToAuthorityList(String.valueOf(staffInfoVo.getId())),
                    staffInfoVo.getFkCompanyId(),
                    staffInfoVo.getLoginId(),
                    staffInfoVo.getNum(),
                    staffInfoVo.getName(),
                    staffInfoVo.getNameEn(),
                    staffInfoVo.getGender(),
                    staffInfoVo.getIsActive(),
                    staffInfoVo.getIsAdmin(),
                    staffInfoVo.getSessionId(),
                    staffInfoVo.getIsModifiedResume(),
                    oauthId,
                    new Date(),
                    "wxcptp");
        } else {
            throw new InvalidGrantException("social grant failure, feign client return error");
        }

        // 组装认证数据，关闭密码校验
        AbstractAuthenticationToken userAuth = new UsernamePasswordAuthenticationToken(getUserDetails, null, getUserDetails.getAuthorities());
        userAuth.setDetails(parameters);

        // 返回 OAuth2Authentication
        return new OAuth2Authentication(getRequestFactory().createOAuth2Request(client, tokenRequest), userAuth);
    }

}
