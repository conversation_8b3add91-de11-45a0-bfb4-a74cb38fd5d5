package com.get.resumecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.resumecenter.vo.SkillTypeVo;
import com.get.resumecenter.entity.SkillType;
import com.get.resumecenter.service.ISkillTypeService;
import com.get.resumecenter.dto.SkillTypeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/31
 * @TIME: 11:35
 * @Description:
 **/
@Api(tags = "技能类型管理")
@RestController
@RequestMapping("resume/skillType")
public class SkillTypeController {
    @Resource
    ISkillTypeService skillTypeService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.resumecenter.vo.SkillTypeVo>
     * @Description: 列表数据
     * @Param [skillTypeDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.LIST, description = "人才中心/技能类型管理/查询技能类型")
    @PostMapping("datas")
    public ResponseBo<SkillTypeVo> datas(@RequestBody SkillTypeDto skillTypeDto) {
        List<SkillTypeVo> datas = skillTypeService.datas(skillTypeDto);
        return new ListResponseBo<>(datas);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.DETAIL, description = "人才中心/技能类型管理/技能类型详情")
    @GetMapping("/{id}")
    public ResponseBo<SkillTypeVo> detail(@PathVariable("id") Long id) {
        SkillTypeVo data = skillTypeService.findSkillTypeById(id);
        SkillTypeVo skillTypeVo = BeanCopyUtils.objClone(data, SkillTypeVo::new);
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", skillTypeVo);
        return responseBo;
    }


    /**
     * 新增信息
     *
     * @param skillTypeDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.ADD, description = "人才中心/技能类型管理/新增技能类型")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(SkillTypeDto.Add.class) SkillTypeDto skillTypeDto) {
        return SaveResponseBo.ok(this.skillTypeService.addSkillType(skillTypeDto));
    }

    /**
     * 删除
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.DELETE, description = "人才中心/技能类型管理/删除技能类型")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        this.skillTypeService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param skillTypeDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.EDIT, description = "人才中心/技能类型管理/更新技能类型")
    @PostMapping("update")
    public ResponseBo<SkillTypeVo> update(@RequestBody @Validated(SkillTypeDto.Update.class) SkillTypeDto skillTypeDto) {
        return UpdateResponseBo.ok(skillTypeService.updateSkillType(skillTypeDto));
    }


    /**
     * 批量新增信息
     *
     * @param skillTypeDtos
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.ADD, description = "人才中心/技能类型管理/批量保存技能类型")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(SkillTypeDto.Add.class)  ValidList<SkillTypeDto> skillTypeDtos) {
        skillTypeService.batchAdd(skillTypeDtos);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 下拉
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "技能类型下拉", notes = "")
    @GetMapping("getSkillTypeSelect")
    public ResponseBo<BaseSelectEntity> getSkillTypeSelect() {
        return new ListResponseBo<>(skillTypeService.getSkillTypeSelect());
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :上移下移
     * @Param [studentOfferItemStepVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.EDIT, description = "人才中心/技能类型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<SkillTypeDto> skillTypeDtos) {
        skillTypeService.movingOrder(skillTypeDtos);
        return ResponseBo.ok();
    }

}
