package com.get.officecenter.service;

import com.get.common.result.SearchBean;
import com.get.officecenter.vo.WorkScheduleStaffConfigVo;
import com.get.officecenter.entity.WorkScheduleStaffConfig;
import com.get.officecenter.dto.TimeConfigDto;
import com.get.officecenter.dto.WorkScheduleStaffConfigAddDto;
import com.get.officecenter.dto.WorkScheduleStaffConfigListDto;
import com.get.officecenter.dto.WorkScheduleStaffConfigUpdateDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/11/7 16:34
 */
public interface WorkScheduleStaffConfigService {

    /**
     * 查询工作时间设置
     *
     * @Date 12:53 2022/11/7
     * <AUTHOR>
     */
    List<WorkScheduleStaffConfigVo> getWorkScheduleStaffConfigs(WorkScheduleStaffConfigListDto data, SearchBean<WorkScheduleStaffConfigListDto> page);

    /**
     * 工作时间设置详情
     *
     * @Date 12:54 2022/11/7
     * <AUTHOR>
     */
    WorkScheduleStaffConfigVo findWorkScheduleStaffConfigById(Long id);

    /**
     * 新增工作时间设置
     *
     * @Date 12:50 2022/11/7
     * <AUTHOR>
     */
    void add(WorkScheduleStaffConfigAddDto workScheduleDateConfigVo);

    /**
     * 更新工作时间设置
     *
     * @Date 12:50 2022/11/7
     * <AUTHOR>
     */
    void updateWorkScheduleStaffConfig(WorkScheduleStaffConfigUpdateDto workScheduleDateConfigVo);

    /**
     * 删除特殊人员工作时间
     *
     * @Date 15:23 2022/11/16
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * 根据公司获取特殊人员时间配置
     * <AUTHOR>
     * @DateTime 2023/1/12 11:39
     */
    Map<Long,List<WorkScheduleStaffConfig>> getWorkScheduleStaffConfigList(Long fkCompanyId);
    /**
     * 根据特定时间获取特殊人员时间配置
     * <AUTHOR>
     * @DateTime 2023/2/28 15:49
     */
    WorkScheduleStaffConfig getWorkScheduleStaffConfig(TimeConfigDto timeConfigDto);
}
