package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.AgentMapper;
import com.get.salecenter.dao.sale.StudentAgentMapper;
import com.get.salecenter.dto.StudentAgentDto;
import com.get.salecenter.entity.StudentAgent;
import com.get.salecenter.service.IAgentService;
import com.get.salecenter.service.IAgentStaffService;
import com.get.salecenter.service.IStudentAgentService;
import com.get.salecenter.utils.sale.GetAgentLabelDataUtils;
import com.get.salecenter.vo.AgentLabelVo;
import com.get.salecenter.vo.StudentAgentVo;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/11/9
 * @TIME: 16:22
 * @Description:
 **/
@Service
public class StudentAgentServiceImpl extends ServiceImpl<StudentAgentMapper, StudentAgent> implements IStudentAgentService {
    @Resource
    private StudentAgentMapper studentAgentMapper;
    @Lazy
    @Resource
    private IAgentService agentService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IAgentStaffService agentStaffService;
    @Resource
    private UtilService utilService;
    @Resource
    private AgentMapper agentMapper;
    @Resource
    private GetAgentLabelDataUtils getAgentLabelDataUtils;

    @Override
    public List<StudentAgentVo> getStudentAgent(StudentAgentDto studentAgentDto, Page page) {
        if (GeneralTool.isEmpty(studentAgentDto.getFkStudentId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("student_id_null"));
        }

//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<StudentAgentVo> studentAgents = studentAgentMapper.datas(studentAgentDto);
//        page.restPage(studentAgents);
//        List<StudentAgent> studentAgents = studentAgentMapper.selectList(Wrappers.<StudentAgent>lambdaQuery().eq(StudentAgent::getFkStudentId,studentAgentDto.getFkStudentId()));
//        List<StudentAgentVo> list = BeanCopyUtils.copyListProperties(studentAgents,StudentAgentVo::new);
        IPage<StudentAgentVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<StudentAgentVo> studentAgents = studentAgentMapper.datas(iPage, studentAgentDto);
        Set<Long> agentIds = studentAgents.stream().map(StudentAgentVo::getFkAgentId).collect(Collectors.toSet());
        Map<Long, List<AgentLabelVo>> agentLabelMap = getAgentLabelDataUtils.getAgentLabelMap(agentIds).getAgentLabelMap();
        studentAgents.forEach(studentAgentVo -> studentAgentVo.setAgentLabelVos(agentLabelMap.get(studentAgentVo.getFkAgentId())));
        page.setAll((int) iPage.getTotal());
        return studentAgents;
    }

    @Override
    public Long addStudentAgent(StudentAgentDto studentAgentDto) {
        if (GeneralTool.isEmpty(studentAgentDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        StudentAgent studentAgent = BeanCopyUtils.objClone(studentAgentDto, StudentAgent::new);
        Integer isExist = studentAgentMapper.validateAdd(studentAgentDto.getFkStudentId(), studentAgentDto.getFkAgentId());
        if (isExist > 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_bound_exits"));
        }
        Boolean isActive = agentMapper.isActiveByAgentId(studentAgentDto.getFkAgentId());
        if (!isActive) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_no_active"));
        }
        studentAgent.setActiveDate(new Date());
        utilService.updateUserInfoToEntity(studentAgent);
        studentAgentMapper.insertSelective(studentAgent);
        return studentAgent.getId();
    }

    @Override
    public StudentAgentVo updateStudentAgent(StudentAgentDto studentAgentDto) {
        if (GeneralTool.isEmpty(studentAgentDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        StudentAgent studentAgent = BeanCopyUtils.objClone(studentAgentDto, StudentAgent::new);
        if (studentAgent.getIsActive()) {
            Integer isExist = studentAgentMapper.validateAdd(studentAgentDto.getFkStudentId(), studentAgentDto.getFkAgentId());
            if (isExist > 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("agent_bound_exits"));
            }
        } else {
            Integer isExist = studentAgentMapper.validateUpdate(studentAgentDto.getFkStudentId());
            if (isExist == 1) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("required_agentId"));
            }
        }

        if (studentAgentDto.getIsActive()) {
            studentAgent.setActiveDate(new Date());
            studentAgent.setUnactiveDate(null);
        } else {
            studentAgent.setUnactiveDate(new Date());
        }
        utilService.updateUserInfoToEntity(studentAgent);
        studentAgentMapper.updateById(studentAgent);

        StudentAgent agent = studentAgentMapper.selectById(studentAgent.getId());
        return BeanCopyUtils.objClone(agent, StudentAgentVo::new);
    }

    @Override
    public List<Long> getRelationByAgentId(Long agentId) {
        if (GeneralTool.isEmpty(agentId)) {
            return null;
        }
//        Example example = new Example(StudentAgent.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkAgentId", agentId);
//        List<StudentAgent> studentAgents = studentAgentMapper.selectByExample(example);
        List<StudentAgent> studentAgents = studentAgentMapper.selectList(Wrappers.<StudentAgent>lambdaQuery().eq(StudentAgent::getFkAgentId, agentId));

        if (GeneralTool.isEmpty(studentAgents)) {
            return null;
        }
        return studentAgents.stream().map(StudentAgent::getFkStudentId).collect(Collectors.toList());
    }

    @Override
    public List<Long> getRelationByAgentName(String agentName) {
        if (GeneralTool.isEmpty(agentName)) {
            return null;
        }
        List<Long> studentIds = studentAgentMapper.getRelationByAgentName("%" + agentName + "%");
        return studentIds;
    }

    @Override
    public List<String> getAgentNameByStudentId(Long studentId) {
        if (GeneralTool.isEmpty(studentId)) {
            return null;
        }
        return studentAgentMapper.getAgentNameByStudentId(studentId);
    }

    @Override
    public Map<Long, String> getAgentNameByStudentIds(Set<Long> studentIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(studentIds)) {
            return map;
        }
        List<StudentAgentVo> agentNameByStudentIds = studentAgentMapper.getAgentNameByStudentIds(studentIds);
        for (StudentAgentVo agentNameByStudentId : agentNameByStudentIds) {
            map.put(agentNameByStudentId.getFkStudentId(), agentNameByStudentId.getAgentName());
        }
        return map;
    }

    @Override
    public Map<Long, Set<String>> getAgentNameListByStudentIds(Set<Long> studentIds) {

        Map<Long, Set<String>> map = new HashMap<>();
        if (GeneralTool.isEmpty(studentIds)) {
            return map;
        }

        List<StudentAgentVo> agentNameByStudentIds = studentAgentMapper.getAgentNameByStudentIds(studentIds);
        if (GeneralTool.isEmpty(agentNameByStudentIds)) {
            return map;
        }
        for (StudentAgentVo studentAgentVo : agentNameByStudentIds) {
            //如果集合包含这个收款单id,则往原来的数据发票编号ids
            if (map.containsKey(studentAgentVo.getFkStudentId())) {
                Set<String> agentNameList = map.get(studentAgentVo.getFkStudentId());
                agentNameList.add(studentAgentVo.getAgentName());
                map.put(studentAgentVo.getFkStudentId(), agentNameList);
                continue;
            }
            //如果没有包含,则往里面添加新数据
            Set<String> agentList = new HashSet<>();
            agentList.add(studentAgentVo.getAgentName());
            map.put(studentAgentVo.getFkStudentId(), agentList);
        }
        return map;
    }

    @Override
    public List<String> getBdCodeByStudentId(Long studentId) {
        if (GeneralTool.isEmpty(studentId)) {
            return null;
        }
        return studentAgentMapper.getBdCodeByStudentId(studentId);
    }

    @Override
    public Map<Long, String> getBdCodeByStudentIds(Set<Long> studentIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(studentIds)) {
            return map;
        }
        List<StudentAgentVo> bdCodeByStudentIds = studentAgentMapper.getBdCodeByStudentIds(studentIds);
        for (StudentAgentVo bdCodeByStudentId : bdCodeByStudentIds) {
            map.put(bdCodeByStudentId.getFkStudentId(), bdCodeByStudentId.getBdCode());
        }
        return map;
    }

    /**
     * 获取代理以及旗下代理所能看到的学生ids
     *
     * @Date 11:31 2021/6/24
     * <AUTHOR>
     */
    @Override
    public List<Long> getStudentIds() {
        //获取代理以及旗下代理的dis
        List<Long> allFollowerIds = getAgentIdsIsActive();
        if (GeneralTool.isEmpty(allFollowerIds)) {
            return null;
        }
//        Example example = new Example(StudentAgent.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andIn("fkAgentId", allFollowerIds);
//        criteria.andEqualTo("isActive", true);
//        List<StudentAgent> studentAgents = studentAgentMapper.selectByExample(example);
        List<StudentAgent> studentAgents = studentAgentMapper.selectList(Wrappers.<StudentAgent>lambdaQuery()
                .in(StudentAgent::getFkAgentId, allFollowerIds)
                .eq(StudentAgent::getIsActive, true));
        if (GeneralTool.isEmpty(studentAgents)) {
            return null;
        }
        List<Long> collect = studentAgents.stream().map(StudentAgent::getFkStudentId).collect(Collectors.toList());
        return collect.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 获取代理以及旗下代理的dis
     *
     * @Date 11:21 2021/6/24
     * <AUTHOR>
     */
    @Override
    public List<Long> getAgentIds() {
        //获取当前登录人id  及下属员工ids
        Long staffId = GetAuthInfo.getStaffId();
        List<Long> staffFollowerIds = new ArrayList<>();
        Result<List<Long>> result = permissionCenterClient.getStaffFollowerIds(staffId);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            staffFollowerIds.addAll(result.getData());
        }
        staffFollowerIds.add(staffId);

        //获取代理
        List<Long> agentIds = agentStaffService.getAgentIdByStaffIds(staffFollowerIds);
        if (GeneralTool.isEmpty(agentIds)) {
            return null;
        }
        agentIds = agentIds.stream().distinct().collect(Collectors.toList());

//        List<Long> subordinateAgentIds = getSubordinateAgentIds(agentIds);
//        subordinateAgentIds.addAll(agentIds);
//        return subordinateAgentIds.stream().distinct().collect(Collectors.toList());

        //获取所有的代理id，一次性查询SQL获取所有代理下级ids
        StringJoiner sj = new StringJoiner(",");
        for (Long agentId : agentIds) {
            sj.add(agentId.toString());
        }
        //遍历获取所有代理下级ids(批量获取)
        List<Long> agentFollowerIds = agentService.getAgentFollowerIdsByIds(sj.toString());
        if (GeneralTool.isEmpty(agentFollowerIds)) {
            //如果子代理为空，则直接返回父代理
            return agentIds;
        }
        //放入子代理
        agentIds.addAll(agentFollowerIds);
        return agentIds.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 获取下一层代理
     *
     * @Date 12:32 2021/8/12
     * <AUTHOR>
     */
    private List<Long> getSubordinateAgentIds(List<Long> agentIds) {
        List<Long> subordinateAgentIds = agentService.getSubordinateAgentIds(agentIds);
        if (GeneralTool.isNotEmpty(subordinateAgentIds)) {
            subordinateAgentIds.addAll(getSubordinateAgentIds(subordinateAgentIds));
        }
        return subordinateAgentIds;
    }

    /**
     * 获取代理以及旗下代理的dis
     *
     * @Date 11:21 2021/6/24
     * <AUTHOR>
     */
    @Override
    public List<Long> getAgentIdsIsActive() {
        //获取当前登录人id  及业务下属员工ids
        Long staffId = SecureUtil.getStaffId();
        List<Long> staffFollowerIds = new ArrayList<>();
        Result<List<Long>> result = permissionCenterClient.getStaffFollowerIds(staffId);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            staffFollowerIds.addAll(result.getData());
        }
        staffFollowerIds.add(staffId);

        //获取代理
        List<Long> agentIds = agentStaffService.getAgentIdByStaffIdsIsActive(staffFollowerIds);
        if (GeneralTool.isEmpty(agentIds)) {
            return null;
        }
        agentIds = agentIds.stream().distinct().collect(Collectors.toList());

//        List<Long> subordinateAgentIds = getSubordinateAgentIds(agentIds);
//        subordinateAgentIds.addAll(agentIds);
//        return subordinateAgentIds.stream().distinct().collect(Collectors.toList());

        //获取所有的代理id，一次性查询SQL获取所有代理下级ids
        StringJoiner sj = new StringJoiner(",");
        for (Long agentId : agentIds) {
            sj.add(agentId.toString());
        }
        //遍历获取所有代理下级ids(批量获取)
        List<Long> agentFollowerIds = agentService.getAgentFollowerIdsByIds(sj.toString());
        if (GeneralTool.isEmpty(agentFollowerIds)) {
            //如果子代理为空，则直接返回父代理
            return agentIds;
        }
        //放入子代理
        agentIds.addAll(agentFollowerIds);
        return agentIds.stream().distinct().collect(Collectors.toList());
    }

    /**
     * Author Cream
     * Description : //合并学生代理数据
     * Date 2023/5/11 12:00
     * Params:
     * Return
     */
    @Override
    public void mergeData(Long mergedStudentId, Long targetStudentId) {
        List<StudentAgent> mergedAgents = studentAgentMapper.selectList(Wrappers.<StudentAgent>lambdaQuery().eq(StudentAgent::getFkStudentId, mergedStudentId));
        List<Long> targetAgents = studentAgentMapper.selectList(Wrappers.<StudentAgent>lambdaQuery().eq(StudentAgent::getFkStudentId, targetStudentId))
                .stream().map(StudentAgent::getFkAgentId).collect(Collectors.toList());
        List<StudentAgent> collect = mergedAgents.stream().filter(f-> !targetAgents.contains(f.getFkAgentId())).collect(Collectors.toList());
        if (GeneralTool.isNotEmpty(collect)) {
            collect.forEach(s->s.setFkStudentId(targetStudentId));
            updateBatchById(collect);
        }
    }

    /**
     * Author Cream
     * Description : //检查学生是否存在不同代理
     * Date 2023/5/17 9:37
     * Params:
     * Return
     */
    @Override
    public void checkDifferentAgent(Long mergedStudentId, Long targetStudentId) {
        List<StudentAgent> mergedList = studentAgentMapper.selectList(Wrappers.<StudentAgent>lambdaQuery().eq(StudentAgent::getFkStudentId, mergedStudentId).eq(StudentAgent::getIsActive,1));
        List<StudentAgent> targetList = studentAgentMapper.selectList(Wrappers.<StudentAgent>lambdaQuery().eq(StudentAgent::getFkStudentId, targetStudentId).eq(StudentAgent::getIsActive,1));
        List<Long> l1 = mergedList.stream().map(StudentAgent::getFkAgentId).collect(Collectors.toList());
        List<Long> l2 = targetList.stream().map(StudentAgent::getFkAgentId).collect(Collectors.toList());
        if (!l1.equals(l2)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("the_agents_bound_to_the_students_are_inconsistent"));
        }
    }

    @Override
    public Map<Long, Integer> getAgentStudentNum(Set<Long> fkAgentIds) {
        List<StudentAgent> studentAgents = studentAgentMapper.getAgentStudentNum(fkAgentIds);
        Map<Long, Integer> map = new HashMap<>();
        for (Long fkAgentId : fkAgentIds) {
            map.put(fkAgentId, (int) studentAgents.stream().filter(e -> e.getFkAgentId().equals(fkAgentId)).collect(Collectors.summingInt(e -> 1)));
        }
        return map;
    }

}
