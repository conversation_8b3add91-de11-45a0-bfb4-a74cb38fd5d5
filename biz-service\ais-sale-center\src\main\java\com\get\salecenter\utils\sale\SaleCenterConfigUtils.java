package com.get.salecenter.utils.sale;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * @author: Hardy
 * @create: 2024/3/20 9:49
 * @verison: 1.0
 * @description:
 */
@Component
public class SaleCenterConfigUtils {

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    public Optional<Object> getValue1Config(String key,String company) {
        ConfigVo configVo = permissionCenterClient.getConfigByKey(key).getData();
        if (GeneralTool.isEmpty(configVo)||GeneralTool.isEmpty(configVo.getValue1())){
            return Optional.empty();
        }
        return getObject(company, configVo.getValue1());
    }

    public Optional<Object> getValue2Config(String key, String company) {
        ConfigVo configVo = permissionCenterClient.getConfigByKey(key).getData();
        if (GeneralTool.isEmpty(configVo)||GeneralTool.isEmpty(configVo.getValue2())){
            return Optional.empty();
        }
        return getObject(company, configVo.getValue2());
    }


    public Optional<Object> getValue3Config(String key, String company) {
        ConfigVo configVo = permissionCenterClient.getConfigByKey(key).getData();
        if (GeneralTool.isEmpty(configVo)||GeneralTool.isEmpty(configVo.getValue3())){
            return Optional.empty();
        }
        return getObject(company, configVo.getValue3());
    }

    public Optional<Object> getValue4Config(String key, String company) {
        ConfigVo configVo = permissionCenterClient.getConfigByKey(key).getData();
        if (GeneralTool.isEmpty(configVo)||GeneralTool.isEmpty(configVo.getValue4())){
            return Optional.empty();
        }
        return getObject(company, configVo.getValue4());
    }

    /**
     * 解析配置
     * @param company
     * @param value
     * @return
     */
    private Optional<Object> getObject(String company, String value) {
        try {
            JSONObject jsonObject = JSON.parseObject(value);
            Object obj = jsonObject.get(company);
            return Optional.of(obj);
        } catch (Exception e) {
            return Optional.empty();
        }
    }
}
