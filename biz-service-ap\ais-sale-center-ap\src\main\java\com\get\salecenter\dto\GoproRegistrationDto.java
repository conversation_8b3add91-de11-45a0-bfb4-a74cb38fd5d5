package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @author: Walker
 * @create:
 * @verison: 1.0
 * @description:
 */
@Data
public class GoproRegistrationDto extends BaseVoEntity {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "公司名")
    @Column(name = "fk_company_name")
    private String fkCompanyName;

    @ApiModelProperty(value = "年份")
    @NotNull(message = "请传递年份")
    private Integer year;
    /**
     * 活动场地：敦煌/西双版纳/呼伦贝尔
     */
    @ApiModelProperty(value = "活动场地：敦煌/西双版纳/呼伦贝尔")
    @Column(name = "retreat_type_name")
    private String retreatTypeName;

    /**
     * 活动场地：敦煌/西双版纳/呼伦贝尔
     */
    @ApiModelProperty(value = "活动场地：敦煌/西双版纳/呼伦贝尔")
    @Column(name = "retreat_type_name_str")
    private String retreatTypeNameStr;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @Column(name = "name")
    private String name;

    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男")
    @Column(name = "gender")
    private Integer gender;

    @ApiModelProperty(value = "国家id")
    @Column(name = "fk_area_country_id")
    private Integer fkAreaCountryId;

    @ApiModelProperty(value = "地区id")
    @Column(name = "fk_area_state_id")
    private Integer fkAreaStateId;

    @ApiModelProperty(value = "城市id")
    @Column(name = "fk_area_city_id")
    private Integer fkAreaCityId;
    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别")
    @Column(name = "gender_name")
    private String genderName;

    /**
     * 所在机构
     */
    @ApiModelProperty(value = "所在机构")
    @Column(name = "company")
    private String company;

    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    @Column(name = "mobile")
    private String mobile;

    /**
     * 电邮地址
     */
    @ApiModelProperty(value = "电邮地址")
    @Column(name = "email")
    private String email;

    /**
     * 常居国家/地区
     */
    @ApiModelProperty(value = "常居国家/地区")
    @Column(name = "area_country_name")
    private String areaCountryName;

    /**
     * 常居州省
     */
    @ApiModelProperty(value = "常居州省")
    @Column(name = "area_state_name")
    private String areaStateName;

//    /**
//     * 常居城市
//     */
//    @ApiModelProperty(value = "常居城市")
//    @Column(name = "area_city_name")
//    private String areaCityName;

    /**
     * bd区域
     */
    @ApiModelProperty(value = "bd区域")
    @Column(name = "bd_region")
    private String bdRegion;

    /**
     * bd姓名
     */
    @ApiModelProperty(value = "bd姓名")
    @Column(name = "bd_name")
    private String bdName;

//    /**
//     * 疫苗接种情况
//     */
//    @ApiModelProperty(value = "疫苗接种情况")
//    @Column(name = "v_status")
//    private String vStatus;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    private static final long serialVersionUID = 1L;
}
