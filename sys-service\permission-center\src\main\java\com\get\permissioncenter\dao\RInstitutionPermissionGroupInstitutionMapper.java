package com.get.permissioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.permissioncenter.entity.RInstitutionPermissionGroupInstitution;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Set;

/**
 * 学校权限组别与学校关系表(RInstitutionPermissionGroupInstitution)表 Mapper接口
 */
@Mapper
public interface RInstitutionPermissionGroupInstitutionMapper extends BaseMapper<RInstitutionPermissionGroupInstitution> {

    Set<Long> selectByFkInstitutionPermissionGroupId(@Param("fkInstitutionPermissionGroupId") Long fkInstitutionPermissionGroupId);
}

