package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.ConventionPersonStaff;
import org.apache.ibatis.annotations.Mapper;

/**
 * @author: Sea
 * @create: 2020/7/27 12:21
 * @verison: 1.0
 * @description: 参展人员和员工关联中间表mapper
 */
@Mapper
public interface ConventionPersonStaffMapper extends BaseMapper<ConventionPersonStaff> {
    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insert(ConventionPersonStaff record);

    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(ConventionPersonStaff record);

    /**
     * 通过参展人员id 查找对应的员工id
     *
     * @param id
     * @return
     */
    Long getStaffId(Long id);
}