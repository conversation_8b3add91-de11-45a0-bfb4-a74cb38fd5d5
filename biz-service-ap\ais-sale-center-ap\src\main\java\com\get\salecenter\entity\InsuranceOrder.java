package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("m_insurance_order")
public class InsuranceOrder extends BaseEntity implements Serializable {
    /**
     * 租户Id
     */
    @ApiModelProperty(value = "租户Id")
    private Long fkTenantId;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 学生代理Id
     */
    @ApiModelProperty(value = "学生代理Id")
    private Long fkAgentId;

    /**
     * 伙伴用户Id
     */
    @ApiModelProperty(value = "伙伴用户Id")
    private Long fkPartnerUserId;

    /**
     * 保险公司Id
     */
    @ApiModelProperty(value = "保险公司Id")
    private Long fkInsuranceCompanyId;

    /**
     * 保险产品类型Id
     */
    @ApiModelProperty(value = "保险产品类型Id")
    private Long fkProductTypeId;

    /**
     * 系统订单编号
     */
    @ApiModelProperty(value = "系统订单编号")
    private String orderNum;

    /**
     * 保险单号
     */
    @ApiModelProperty(value = "保险单号")
    private String insuranceNum;

    /**
     * 保险单类型：Single/Couple/Family
     */
    @ApiModelProperty(value = "保险单类型：Single/Couple/Family")
    private String insuranceType;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    /**
     * 保单金额
     */
    @ApiModelProperty(value = "保单金额")
    private BigDecimal insuranceAmount;

    /**
     * 受保人姓名
     */
    @ApiModelProperty(value = "受保人姓名")
    private String insurantName;

    /**
     * 受保人姓（英/拼音）
     */
    @ApiModelProperty(value = "受保人姓（英/拼音）")
    private String insurantLastName;

    /**
     * 受保人名（英/拼音）
     */
    @ApiModelProperty(value = "受保人名（英/拼音）")
    private String insurantFirstName;

    /**
     * 受保人性别
     */
    @ApiModelProperty(value = "受保人性别")
    private String insurantGender;

    /**
     * 受保人生日
     */
    @ApiModelProperty(value = "受保人生日")
    private Date insurantBirthday;

    /**
     * 受保人国籍
     */
    @ApiModelProperty(value = "受保人国籍")
    private String insurantNationality;

    /**
     * 受保人护照号
     */
    @ApiModelProperty(value = "受保人护照号")
    private String insurantPassportNum;

    /**
     * 受保人Email
     */
    @ApiModelProperty(value = "受保人Email")
    private String insurantEmail;

    /**
     * 受保人手机区号
     */
    @ApiModelProperty(value = "受保人手机区号")
    private String insurantMobileAreaCode;

    /**
     * 受保人移动电话
     */
    @ApiModelProperty(value = "受保人移动电话")
    private String insurantMobile;

    /**
     * 入学时间
     */
    @ApiModelProperty(value = "入学时间")
    private Date enrollmentTime;

    /**
     * 毕业时间
     */
    @ApiModelProperty(value = "毕业时间")
    private Date graduationTime;

    /**
     * 前往国家Id
     */
    @ApiModelProperty(value = "前往国家Id")
    private Long fkAreaCountryIdTo;

    /**
     * 保单开始时间
     */
    @ApiModelProperty(value = "保单开始时间")
    private Date insuranceStartTime;

    /**
     * 保单结束时间
     */
    @ApiModelProperty(value = "保单结束时间")
    private Date insuranceEndTime;

    /**
     * 保单备注（用户下单时填写）
     */
    @ApiModelProperty(value = "保单备注（用户下单时填写）")
    private String orderRemark;

    /**
     * 订单明细json
     */
    @ApiModelProperty(value = "订单明细json")
    private String orderJson;

    /**
     * 成交时间（保险公司系统成功下单时间）
     */
    @ApiModelProperty(value = "成交时间（保险公司系统成功下单时间）")
    private Date orderTime;

    /**
     * 备注（后台用户备注信息）
     */
    @ApiModelProperty(value = "备注（后台用户备注信息）")
    private String remark;

    /**
     * 订单状态：等待下单0/下单中1/下单成功2/下单失败-2
     */
    @ApiModelProperty(value = "订单状态：等待下单0/下单中1/下单成功2/下单失败-2")
    private Integer orderStatus;

    /**
     * 订单信息（系统反馈记录）
     */
    @ApiModelProperty(value = "订单信息（系统反馈记录）")
    private String orderMessage;

}
