<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.platformconfigcenter.dao.appmso.EventRegistrationMapper">

    <select id="getEventRegistrationList" resultType="com.get.platformconfigcenter.entity.MsoEventRegistration">

        SELECT
            *
        FROM
            m_event_registration
        <where>
            <if test="data.fkTableName !=null and data.fkTableName !='' ">
               and fk_table_name = #{data.fkTableName}
            </if>
            <if test="data.fkTableId != null and data.fkTableId !='' ">
                and fk_table_id = #{data.fkTableId }
            </if>
            <if test="data.registrationCode != null and data.registrationCode != '' ">
                and registration_code LIKE CONCAT("%",CONCAT(#{data.registrationCode},"%"))
            </if>
            <if test="data.name != null and data.name != '' ">
                and name LIKE CONCAT("%",CONCAT(#{data.name},"%"))
            </if>
            <if test="data.isMember !=null ">
                and is_member = #{data.isMember,jdbcType=BIT}
            </if>
            <if test="data.isAttend !=null  ">
                and is_attend = #{data.isAttend,jdbcType=BIT}
            </if>

        </where>


    </select>

    <update id="updateisAttend">
        UPDATE m_event_registration set is_attend = 1 WHERE registration_code = #{registrationCode}
    </update>

    <update id="batchUpdateIsAttend" parameterType="com.get.platformconfigcenter.dto.IsAttendDto">

        UPDATE m_event_registration set is_attend = #{isAttend} WHERE id in
        <foreach collection="ids" item="id" index="index" separator="," open="(" close=")">
            #{id}
        </foreach>

    </update>




</mapper>