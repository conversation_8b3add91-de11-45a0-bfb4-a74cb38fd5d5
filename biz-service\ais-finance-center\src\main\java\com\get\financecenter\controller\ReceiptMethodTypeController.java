package com.get.financecenter.controller;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.financecenter.dto.ReceiptMethodTypeDto;
import com.get.financecenter.service.ReceiptMethodTypeService;
import com.get.financecenter.vo.BaseSelectVo;
import com.get.financecenter.vo.ReceiptMethodTypeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 收款方式类型管理
 */
@Api(tags = "收款方式类型管理")
@RestController
@RequestMapping("finance/receiptMethodType")
public class ReceiptMethodTypeController {

    @Resource
    private ReceiptMethodTypeService  receiptMethodTypeService;

    @ApiOperation(value = "分页查询所有数据",notes = "分页查询所有数据")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/收款方式类型管理/查询")
    @PostMapping("list")
    public ResponseBo<ReceiptMethodTypeVo> selectAll(@RequestBody SearchBean<ReceiptMethodTypeDto> page) {
        List<ReceiptMethodTypeVo> datas = receiptMethodTypeService.getReceiptMethodTypes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "收款方式类型新增", notes = "批量新增")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/收款方式类型管理/批量新增")
    @PostMapping("batchAdd")
    public ResponseBo addReceiptMethodType(@RequestBody List<ReceiptMethodTypeDto> receiptMethodTypeDtos) {
        receiptMethodTypeService.batchAdd(receiptMethodTypeDtos);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "收款方式类型修改", notes = "付款方式类型id: paymentMethodTypeId")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/收款方式类型管理/修改")
    @PostMapping("update")
    public ResponseBo updatePaymentMethodType(@RequestBody ReceiptMethodTypeDto receiptMethodTypeDto) {
        receiptMethodTypeService.updateReceiptMethodTypes(receiptMethodTypeDto);
        return ResponseBo.ok();
    }


    @ApiOperation(value = "收款方式类型删除", notes = "收款方式类型id: receiptMethodTypeId")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/收款方式类型管理/删除")
    @GetMapping("delete")
    public ResponseBo deleteReceiptMethodType(@RequestParam("id") Long id) {
         receiptMethodTypeService.deleteReceiptMethodType(id);
         return ResponseBo.ok();
    }

//    @ApiOperation(value = "排序(对换顺序)", notes = "排序接口（对换顺序）")
//    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/收款方式类型管理/排序")
//    @PostMapping("sort")
//    public ResponseBo sort(@RequestBody List<Long> ids) {
//        receiptMethodTypeService.sort(ids);
//        return ResponseBo.ok();
//    }

    @ApiOperation(value = "排序（拖拽）", notes = "排序（拖拽）")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/收款方式类型管理/排序（拖拽）")
    @PostMapping("sort")
    public ResponseBo sort(@RequestParam("start") Integer start , @RequestParam("end") Integer end) {
        receiptMethodTypeService.movingOrder(start, end);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "收款方式类型下拉", notes = "收款方式类型下拉")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/收款方式类型管理/收款方式类型下拉框")
    @GetMapping("getReceiptMethodTypeDropDown")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<BaseSelectVo> select() {
         return new ListResponseBo<>(receiptMethodTypeService.getReceiptMethodTypeDropDown());
    }

}

