package com.get.institutioncenter.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @author: Sea
 * @create: 2021/3/1 12:21
 * @verison: 1.0
 * @description:
 */
@Mapper
public interface BatchUpdateMapper {
    /**
     * @Description :加载课程批量修改列表接口
     * @Param [sql1, ids, sql2, tableName,orderBy]
     * <AUTHOR>
     */
    List<Map<String, String>> getBatchUpdates(@Param("sql1") String sql1, @Param("ids") List<Long> ids, @Param("sql2") String sql2, @Param("tableName") String tableName, @Param("orderBy") String orderBy);

    /**
     * @return void
     * @Description :删除的sql
     * @Param [sql]
     * <AUTHOR>
     */
    void sqlDelete(@Param("sql") String sql);

    /**
     * @return void
     * @Description :修改的sql
     * @Param [sql]
     * <AUTHOR>
     */
    void sqlUpdate(@Param("sql") String sql);

    /**
     * @return void
     * @Description :新增的sql
     * @Param [sql]
     * <AUTHOR>
     */
    void sqlInsert(@Param("sql") String sql);
}
