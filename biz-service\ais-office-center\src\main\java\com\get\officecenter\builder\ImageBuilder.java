package com.get.officecenter.builder;

import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;
import me.chanjar.weixin.cp.bean.message.WxCpXmlOutImageMessage;
import me.chanjar.weixin.cp.bean.message.WxCpXmlOutMessage;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;


/**
 *  <AUTHOR>
 */
public class ImageBuilder extends AbstractBuilder {

  @Override
  public WxCpXmlOutMessage build(String content, WxCpTpXmlMessage wxMessage, WxCpTpService wxCpTpservice) {
    WxCpXmlOutImageMessage m = WxCpXmlOutMessage.IMAGE().mediaId(content)
            .fromUser(wxMessage.getToUserName()).toUser(wxMessage.getFromUserName())
            .build();
    return m;
  }
}
