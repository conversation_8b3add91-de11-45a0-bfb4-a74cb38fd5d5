<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.AgentContractMapper">

    <select id="getContractByCountryIds" resultType="java.lang.Long">
        SELECT m.id id from m_agent_contract m left join m_agent a on m.fk_agent_id=a.id
        where a.fk_area_country_id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="isExistByAgentId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id from m_agent_contract where fk_agent_id=#{agentId}
    </select>

    <select id="getStaffByAgentId" resultType="java.lang.Long">
        select DISTINCT st.fk_staff_id from r_agent_staff st
 left join m_agent_contract ac on ac.fk_agent_id=st.fk_agent_id
 where ac.fk_agent_id=#{fkAgentId} and st.is_active=1
    </select>

    <select id="getExistParentId" resultType="java.lang.Boolean">
    select IFNULL(MAX(id),0)id from m_agent_contract where fk_agent_contract_id_revoke=#{id}
    </select>


    <select id="getAgentContracts" resultType="com.get.salecenter.vo.AgentContractVo">
        SELECT a.*,z1.isHighLight
<!--        ,b3.email AS labelEmail,b1.remark AS labelRemark,b2.id AS labelTypeId-->
        FROM
        ais_sale_center.m_agent_contract a
        INNER JOIN(
        SELECT DISTINCT a.id FROM (
        -- #创建人的权限
        SELECT a.id FROM ais_sale_center.m_agent_contract a
        INNER JOIN ais_permission_center.m_staff b ON a.gmt_create_user=b.login_id AND b.id IN
        <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
            #{staffFollowerId}
        </foreach>
        -- #BD的权限
        UNION ALL
        SELECT a.id FROM ais_sale_center.m_agent_contract a
        INNER JOIN ais_sale_center.m_agent b ON a.fk_agent_id = b.id
        INNER JOIN ais_sale_center.r_agent_staff c ON b.id=c.fk_agent_id AND c.is_active = 1 AND c.fk_staff_id IN
        <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
            #{staffFollowerId}
        </foreach>
        ) a
        -- 权限过滤结束
        ) z on z.id = a.id
        INNER JOIN ais_sale_center.m_agent g ON a.fk_agent_id = g.id
        <if test="contractDto.agentIsActive!=null">
            AND g.is_active = #{contractDto.agentIsActive}
        </if>
        <if test="contractDto.bdId!=null">
            INNER JOIN ais_sale_center.r_agent_staff c2 ON g.id=c2.fk_agent_id AND c2.is_active = 1 AND c2.fk_staff_id = #{contractDto.bdId}
        </if>
        LEFT JOIN r_agent_contract_company b on b.fk_agent_contract_id = a.id
        LEFT JOIN (
        SELECT
        a.id,
        CASE
        WHEN DATE_FORMAT(DATE_SUB(a.end_time, INTERVAL #{advanceDays} ${advanceType}),'%Y-%m-%d') <![CDATA[<= ]]> CURRENT_DATE THEN 1 ELSE 0 END isHighLight
        FROM
        ais_sale_center.m_agent_contract a
        ) z1 on a.id = z1.id
<!--        <if test="contractDto.labelTypeId != null or contractDto.labelRemark != null or contractDto.labelEmail != null">-->
<!--            INNER JOIN ais_sale_center.r_agent_label b3 ON b3.fk_agent_id = a.fk_agent_id-->
<!--            INNER JOIN ais_platform_center.u_label b1 ON b1.id = b3.fk_label_id-->
<!--            INNER JOIN ais_platform_center.u_label_type b2 ON b2.id = b1.fk_label_type_id-->
<!--        </if>-->
        where 1=1
        <if test="companyIds.size() >1">
            and b.fk_company_id in
            <foreach collection="companyIds" item="companyId" index="index" open="(" separator="," close=")">
                #{companyId}
            </foreach>
        </if>
        <if test="contractDto.fkCompanyId != null and contractDto.fkCompanyId !=''">
            AND  b.fk_company_id = #{contractDto.fkCompanyId}
        </if>
        <if test="contractDto.fkAgentId != null and contractDto.fkAgentId !=''">
            AND  a.fk_agent_id = #{contractDto.fkAgentId} -- 查询代理
        </if>
        <if test="contractDto.fkAgentContractTypeId != null and contractDto.fkAgentContractTypeId !=''">
            AND  a.fk_agent_contract_type_id = #{contractDto.fkAgentContractTypeId} -- 查询合同类型
        </if>
        <if test="contractDto.isActive != null">
            AND  a.is_active = #{contractDto.isActive} -- 查询是否激活
        </if>
        <if test="contractDto.createBeginTime != null">
            AND  DATE_FORMAT(a.gmt_create,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{contractDto.createBeginTime},'%Y-%m-%d')
        </if>
        <if test="contractDto.createEndTime != null">
            AND DATE_FORMAT(a.gmt_create,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{contractDto.createEndTime},'%Y-%m-%d')
        </if>
        <if test="contractDto.keyWord != null and contractDto.keyWord !=''">
            AND  a.contract_num = #{contractDto.keyWord} -- 查询编号
        </if>
        <if test="contractDto.getSelectStatus == '0' and contractDto.gmtCreateUser !=null and contractDto.gmtCreateUser !=''">
            AND  a.gmt_create_user = #{contractDto.gmtCreateUser} -- 查询创建人
        </if>
        <if test="contractDto.formIds!=null and contractDto.formIds.size()>0">
            AND  a.id in
            <foreach collection="contractDto.formIds" item="formId" index="index" open="(" separator="," close=")">
                #{formId}  -- 查询我的审批
            </foreach>
        </if>
<!--        <if test="contractDto.labelTypeId != null">-->
<!--            AND b2.id = #{contractDto.labelTypeId}-->
<!--        </if>-->

<!--        <if test="contractDto.labelRemark != null">-->
<!--            AND b1.remark LIKE CONCAT('%', #{contractDto.labelRemark}, '%')-->
<!--        </if>-->

<!--        <if test="contractDto.labelEmail != null">-->
<!--            AND b3.email LIKE CONCAT('%', #{contractDto.labelEmail}, '%')-->
<!--        </if>-->

        <!-- 合同审批状态过滤 -->
        <if test="contractDto.contractApprovalStatus != null">
            <choose>
                <!-- 直接查询数据库状态：0,1,2,3,4,-4,5 -->
                <when test="contractDto.contractApprovalStatus == 0 or contractDto.contractApprovalStatus == 1 or
                           contractDto.contractApprovalStatus == 2 or contractDto.contractApprovalStatus == 3 or
                           contractDto.contractApprovalStatus == 4 or contractDto.contractApprovalStatus == -4 or
                           contractDto.contractApprovalStatus == 5">
                    AND a.contract_approval_status = #{contractDto.contractApprovalStatus}
                </when>
                <!-- 生效中状态：审核通过且在有效期内 -->
                <when test="contractDto.contractApprovalStatus == 6">
                    AND a.contract_approval_status = 4
                    AND a.start_time IS NOT NULL
                    AND a.end_time IS NOT NULL
                    AND NOW() <![CDATA[>= ]]> a.start_time
                    AND NOW() <![CDATA[<= ]]> a.end_time
                </when>
                <!-- 已过期状态：审核通过但已超过有效期 -->
                <when test="contractDto.contractApprovalStatus == 7">
                    AND a.contract_approval_status = 4
                    AND a.end_time IS NOT NULL
                    AND NOW() <![CDATA[> ]]> a.end_time
                </when>
            </choose>
        </if>

        group by a.id
        ORDER BY a.is_active desc,z1.isHighLight desc,a.gmt_create desc
    </select>

    <update id="changeStatus">
        update ${tableName}
        set status = #{status}
        where id = #{businessKey}
    </update>
    <select id="getFkAgentidByCppAgId" resultType="java.lang.Long">
        select id from m_agent where id_gea = #{agentid}
    </select>
    <select id="getInvalidContractAgentName" resultType="java.lang.String">
        SELECT
        CASE
        WHEN (name_note IS NOT NULL AND name_note!='')
        THEN
        CONCAT(
        name,
        '（',
        IFNULL(name_note, ''),
        '）'
        )
        ELSE
        NAME
        END
        FROM m_agent where id in
        <foreach collection="agentIdSet" item="agentId" index="index" open="(" separator="," close=")">
            #{agentId}
        </foreach>
        AND id NOT IN ( SELECT fk_agent_id FROM `m_agent_contract`
        WHERE  DATE_FORMAT(start_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(NOW(), '%Y-%m-%d')
        AND    DATE_FORMAT(end_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(NOW(), '%Y-%m-%d')
        AND    is_active = 1
        AND    fk_agent_id IN
        <foreach collection="agentIdSet" item="agentId" index="index" open="(" separator="," close=")">
            #{agentId}
        </foreach>
        GROUP BY fk_agent_id  )
    </select>
    <select id="getAttByAgencyidAndEndtm" resultType="com.get.salecenter.entity.AgentContract" >
    select mac.*
    from m_agent_contract mac
    left join ais_sale_center.s_media_and_attached smaa ON
    mac.id=smaa.fk_table_id left join m_agent mag on mac.fk_agent_id=mag.id where 1=1
    and mac.gmt_create &gt;= '2022-04-29 20:21:06'
    and mac.gmt_create &lt;= '2022-04-30 05:21:06'
    and smaa.type_key='sale_contract_file'
    and smaa.gmt_create_user='admin'
    and mac.fk_agent_id=#{fkAgentid}
    and DATE_FORMAT(mac.end_time, '%Y-%m-%d')=#{endTm} limit 1
    </select>
    <select id="checkAgentContractExpirationTime" resultType="java.lang.Long">
        SELECT
        id
        FROM
        m_agent_contract
        WHERE
        fk_agent_id = #{fkAgentId}
        AND is_active = 1
        AND NOW() <![CDATA[>= ]]> start_time
        AND NOW() <![CDATA[<= ]]> end_time
    </select>

    <select id="getFkAgentContractNum" resultType="java.lang.String">
        SELECT COALESCE(CAST(
                                SUBSTRING(
                                        MAX(contract_num),
                                        LENGTH(#{contractPrefix}) + 1,
                                        LENGTH(MAX(contract_num)) - LENGTH(#{contractPrefix})
                                ) AS UNSIGNED
                        ), 0) AS maxSequence
        FROM m_agent_contract
        WHERE contract_num LIKE CONCAT(#{contractPrefix}, '%')
    </select>

    <update id="updateByAgencyidAndEndtm">

    </update>

    <select id="countContractNum" resultType="int">
        SELECT
            COUNT(a.id) AS contract_count
        FROM ais_sale_center.m_agent_contract a
                 LEFT JOIN ais_sale_center.m_agent b
                           ON a.fk_agent_id = b.id
                 LEFT JOIN ais_sale_center.r_agent_company c
                           ON b.id = c.fk_agent_id
        WHERE c.fk_company_id = #{companyId}
    </select>
</mapper>