package com.get.workflowcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.workflowcenter.vo.ActReModelVo;
import com.get.workflowcenter.service.IModelService;
import com.get.workflowcenter.dto.ActReModelDto;
import com.get.workflowcenter.dto.ActReProcdefDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.activiti.engine.RepositoryService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/11/20 16:24
 * @verison: 1.0
 * @description: 模型管理
 */
@Api(tags = "模型管理")
@RestController
@RequestMapping("workflow/modeler")
public class ModelController {

    @Resource
    private IModelService modelService;
    @Resource
    private RepositoryService repositoryService;

    /**
     * @return void
     * @Description :创建模型
     * @Param [name, key, response]
     * <AUTHOR>
     */
    @ApiOperation(value = "创建模型", notes = "name为模型名称，key为模型标识")
    @OperationLogger(module = LoggerModulesConsts.WORKFLOWCENTER, type = LoggerOptTypeConst.ADD, description = "工作流中心/模型管理/创建模型")
    @GetMapping("createModel")
    public ResponseBo createModel(@RequestParam String name, @RequestParam String key) {
        String modelId = modelService.create(name, key);
        return SaveResponseBo.ok(Long.valueOf(modelId));
    }

    /**
     * @return void
     * @Description :上传模型
     * @Param [modelFile, response]
     * <AUTHOR>
     */
    @ApiOperation(value = "上传模型", notes = "")
    @OperationLogger(module = LoggerModulesConsts.WORKFLOWCENTER, type = LoggerOptTypeConst.ADD, description = "工作流中心/模型管理/上传模型")
    @PostMapping("uploadModel")
    public ResponseBo uploadModel(@RequestParam MultipartFile modelFile) {
        String modelId = modelService.uploadModel(modelFile);
        return SaveResponseBo.ok(Long.valueOf(modelId));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除模型
     * @Param [modelId]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除模型", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.WORKFLOWCENTER, type = LoggerOptTypeConst.DELETE, description = "工作流中心/模型管理/删除模型")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") String modelId) {
        this.modelService.delete(modelId);
        return DeleteResponseBo.ok();
    }

//    /**
//     * @return void
//     * @Description :跳转到编辑模型流程图页
//     * @Param [modelId, response]
//     * <AUTHOR>
//     */
//    @ApiOperation(value = "跳转到编辑模型流程图页", notes = "modelId为模型id")
//    @GetMapping("toUpdate")
//    @VerifyLogin(IsVerify = false)
//    @VerifyPermission(IsVerify = false)
//    public void toUpdate(@RequestParam String modelId, HttpServletResponse response)  {
//        try {
//            response.sendRedirect("/workflow/editor?modelId=" + modelId);
//        } catch (IOException e) {
//            e.printStackTrace();
//            throw new GetServiceException(LocaleMessageUtils.getMessage("redirect_fail"));
//        }
//    }
//    /**
//     * @return void
//     * @Description :跳转到编辑模型流程图页
//     * @Param [modelId, response]
//     * <AUTHOR>
//     */
//    @ApiOperation(value = "跳转到编辑模型流程图页", notes = "modelId为模型id")
//    @GetMapping("toUpdate/{modelId}")
//    @VerifyLogin(IsVerify = false)
//    @VerifyPermission(IsVerify = false)
//    public void toUpdate(@PathVariable("modelId") String modelId, HttpServletResponse response)  {
//        try {
//            response.sendRedirect("/workflow/editor?modelId=" + modelId);
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw new GetServiceException(LocaleMessageUtils.getMessage("redirect_fail"));
//        }
//    }

    /**
     * @return void
     * @Description :跳转到编辑模型流程图页
     * @Param [modelId, response]
     * <AUTHOR>
     */
    @ApiOperation(value = "跳转到编辑模型流程图页", notes = "modelId为模型id")
    @GetMapping("toUpdate/{modelId}")
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    public void toUpdate(@PathVariable("modelId") String modelId, HttpServletRequest request, HttpServletResponse response) {
        try {
//            response.sendRedirect("/workflow/editor?modelId=" + modelId);
            String scheme = request.getScheme();
            String serverName = request.getServerName();
            int port = request.getServerPort();
            String path = request.getContextPath();
            String basePath = "";
            basePath = scheme + "://" + serverName + ":" + port + path;
            System.out.println("====================>>>" + basePath);
            response.sendRedirect(basePath + "/workflow/editor?modelId=" + modelId);
        } catch (Exception e) {
            e.printStackTrace();
            throw new GetServiceException(LocaleMessageUtils.getMessage("redirect_fail"));
        }
    }

    /**
     * 跳转编辑器页面
     *
     * @return
     */
    @GetMapping("workflow/editor1")
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    public String editor() {
        return "modeler";
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.workflowcenter.vo.ReModelDto>
     * @Description :模型列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "模型列表数据", notes = "name为模型名称，key为模型标识")
    @OperationLogger(module = LoggerModulesConsts.WORKFLOWCENTER, type = LoggerOptTypeConst.LIST, description = "工作流中心/模型管理/查询模型")
    @PostMapping("datas")
    public ResponseBo<ActReModelVo> datas(@RequestBody SearchBean<ActReModelDto> page) {
        List<ActReModelVo> datas = modelService.getModels(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :下载模型
     * @Param [modelId, response]
     * <AUTHOR>
     */
    @ApiOperation(value = "下载模型", notes = "modelId为模型id")
    @OperationLogger(module = LoggerModulesConsts.WORKFLOWCENTER, type = LoggerOptTypeConst.DETAIL, description = "工作流中心/模型管理/下载模型")
    @GetMapping("export/{id}")
    public ResponseBo export(@PathVariable("id") String modelId, HttpServletResponse response) {
        this.modelService.export(modelId, response);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :部署模型
     * @Param [modelVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "部署模型", notes = "")
    @OperationLogger(module = LoggerModulesConsts.WORKFLOWCENTER, type = LoggerOptTypeConst.ADD, description = "工作流中心/模型管理/部署模型")
    @PostMapping("deployModel")
    public ResponseBo deployModel(@RequestBody ActReProcdefDto actReProcdefDto) {
        modelService.deployModel(actReProcdefDto);
        return SaveResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :部署流程
     * @Param [file, actReProcdefVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "部署流程", notes = "")
    @OperationLogger(module = LoggerModulesConsts.WORKFLOWCENTER, type = LoggerOptTypeConst.ADD, description = "工作流中心/模型管理/部署流程")
    @PostMapping("deployProcess")
    //正式接口
    public ResponseBo deployProcess(@RequestParam MultipartFile file, @RequestParam("tenantId") String tenantId, @RequestParam("category") String category) {
        modelService.deployProcess(file, tenantId, category);
        return SaveResponseBo.ok();
    }
}
