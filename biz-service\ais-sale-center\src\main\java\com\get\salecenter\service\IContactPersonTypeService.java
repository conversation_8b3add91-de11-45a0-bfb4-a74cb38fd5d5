package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.service.GetService;
import com.get.core.mybatis.utils.ValidList;
import com.get.salecenter.vo.ContactPersonTypeVo;
import com.get.salecenter.entity.ContactPersonType;
import com.get.salecenter.dto.ContactPersonTypeDto;

import java.util.List;

/**
 * 联系人类型管理接口
 */
public interface IContactPersonTypeService extends GetService<ContactPersonType> {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    ContactPersonTypeVo findContactPersonTypeById(Long id);

    /**
     * 批量新增
     *
     * @param contactPersonTypeDtos
     * @
     */
    void batchAdd(ValidList<ContactPersonTypeDto> contactPersonTypeDtos);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    void delete(Long id);

    /**
     * 修改
     *
     * @param contactPersonTypeDto
     * @return
     */
    ContactPersonTypeVo updateContactPersonType(ContactPersonTypeDto contactPersonTypeDto);

    /**
     * 列表
     *
     * @param contactPersonTypeDto
     * @param page
     * @return
     */
    List<ContactPersonTypeVo> getContactPersonTypes(ContactPersonTypeDto contactPersonTypeDto, Page page);

    /**
     * 列表不分页
     *
     * @return
     * @
     */
    List<ContactPersonTypeVo> getContactPersonTypes();

    /**
     * 获取合同联系人枚举
     * 
     * @return
     */
    List<ContactPersonType> getContactContactTypes();

    /**
     * 上移下移
     *
     * @param contactPersonTypeDtos
     * @return
     */
    void movingOrder(List<ContactPersonTypeDto> contactPersonTypeDtos);

    /**
     * 获取联系人类型
     * 
     * @param isNewType 是否是新增类型
     * @return
     */
    List<ContactPersonTypeVo> getContactPersonTypes(boolean isNewType);

    /**
     * 获取代理可用联系人类型下拉框（排除已选择的类型）
     *
     * @param fkAppAgentId 代理ID
     * @return 可用的联系人类型列表
     */
    List<ContactPersonTypeVo> getAvailableContactPersonTypes(Long fkAppAgentId);

    /**
     * 获取联系人表关联可用联系人类型下拉框（排除已选择的类型）
     *
     * @param fkTableId 表ID
     * @return 可用的联系人类型列表
     */
    List<ContactPersonTypeVo> getAvailableTypesByContactPersonTableId(Long fkTableId);

}
