package com.get.permissioncenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.dao.BatchModifyConfigMapper;
import com.get.permissioncenter.vo.BatchModifyConfigVo;
import com.get.permissioncenter.entity.BatchModifyConfig;
import com.get.permissioncenter.service.IBatchModifyConfigService;
import com.get.permissioncenter.dto.BatchModifyConfigDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/2/22 17:12
 * @verison: 1.0
 * @description:
 */
@Service
public class BatchModifyConfigServiceImpl extends BaseServiceImpl<BatchModifyConfigMapper, BatchModifyConfig> implements IBatchModifyConfigService {
    @Resource
    private BatchModifyConfigMapper batchModifyConfigMapper;
    @Resource
    private UtilService utilService;

    @Override
    public List<BatchModifyConfigVo> getBatchUpdateItems(BatchModifyConfigDto batchModifyConfigDto) {
//        Example example = new Example(BatchModifyConfig.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkDbName", batchModifyConfigDto.getFkDbName());
//        criteria.andEqualTo("fkTableName", batchModifyConfigDto.getFkTableName());
//        example.orderBy("viewOrder").asc();
//        List<BatchModifyConfig> batchModifyConfigs = batchModifyConfigMapper.selectByExample(example);
        List<BatchModifyConfig> batchModifyConfigs = batchModifyConfigMapper.selectList(Wrappers.<BatchModifyConfig>query().lambda()
                .eq(BatchModifyConfig::getFkDbName, batchModifyConfigDto.getFkDbName())
                .eq(BatchModifyConfig::getFkTableName, batchModifyConfigDto.getFkTableName()).orderByAsc(BatchModifyConfig::getViewOrder));
        List<BatchModifyConfigVo> batchModifyConfigVoList = new ArrayList<>();
        for (BatchModifyConfig batchModifyConfig : batchModifyConfigs) {
            BatchModifyConfigVo batchModifyConfigVo = BeanCopyUtils.objClone(batchModifyConfig, BatchModifyConfigVo::new);
            //特殊处理 课程列表要返回排序flag给前端使用
            if ("get_institution_center".equals(batchModifyConfig.getFkDbName()) &&
                    "m_institution_course".equals(batchModifyConfig.getFkTableName()) && "课程名称".equals(batchModifyConfig.getColumnTitle())) {
                batchModifyConfigVo.setSortFlag(true);
                batchModifyConfigVo.setDesc("CONVERT(ic.name USING gbk) DESC");
                batchModifyConfigVo.setAsc("CONVERT(ic.name USING gbk) ASC");
            } else {
                batchModifyConfigVo.setSortFlag(false);
            }
            batchModifyConfigVoList.add(batchModifyConfigVo);
        }
        return batchModifyConfigVoList;
    }

    @Override
    public BatchModifyConfigVo findbatchModifyConfigById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        BatchModifyConfig batchModifyConfig = batchModifyConfigMapper.selectById(id);
        if (GeneralTool.isEmpty(batchModifyConfig)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        return BeanCopyUtils.objClone(batchModifyConfig, BatchModifyConfigVo::new);
    }
}
