package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.institutioncenter.dto.InstitutionDeadlineInfoDto2;
import com.get.institutioncenter.vo.InstitutionDeadlineInfoVo2;
import com.get.institutioncenter.entity.InstitutionDeadlineInfo2;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface InstitutionDeadlineInfoMapper2 extends BaseMapper<InstitutionDeadlineInfo2> {


    List<InstitutionDeadlineInfoVo2> getWcInstitutionDeadlineInfoList(IPage<InstitutionDeadlineInfoDto2> ipage,
                                                                      @Param("schoolName") String schoolName,
                                                                      @Param("fkCountryId") Long fkCountryId);

    Date getNewCreateTime();
}