package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.salecenter.vo.EventPlanRegistrationVo;
import com.get.salecenter.entity.EventPlanRegistration;
import com.get.salecenter.dto.EventPlanRegistrationSearchDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
@Mapper
public interface EventPlanRegistrationMapper extends BaseMapper<EventPlanRegistration> {
    /**
     * 列表数据
     * <AUTHOR>
     * isPage：是否分页
     * @DateTime 2023/12/19 16:53
     */
    List<EventPlanRegistrationVo> getEventPlanRegistrations(IPage<EventPlanRegistrationVo> iPage,
                                                            @Param("registrationDto") EventPlanRegistrationSearchDto eventPlanRegistrationSearchDto,
                                                            @Param("isPage") Boolean isPage);
}