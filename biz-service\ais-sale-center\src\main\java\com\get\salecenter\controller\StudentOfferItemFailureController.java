package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.StudentOfferItemFailureVo;
import com.get.salecenter.service.StudentOfferItemFailureService;
import com.get.salecenter.dto.StudentOfferItemFailureDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/8 16:52
 */
@Api(tags = "学习计划生成应收、应付计划失败管理")
@RestController
@RequestMapping("sale/studentOfferItemFailure")
public class StudentOfferItemFailureController {

    @Resource
    private StudentOfferItemFailureService studentOfferItemFailureService;

    /**
     * 应收应付生成失败列表
     *
     * @Date 10:57 2021/7/9
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/生成应收应付失败管理/查询失败列表")
    @PostMapping("datas")
    public ResponseBo<StudentOfferItemFailureVo> datas(@RequestBody SearchBean<StudentOfferItemFailureDto> page) {
        List<StudentOfferItemFailureVo> datas = studentOfferItemFailureService.getStudentOfferItemFailureList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 失败记录公司代理下拉框
     *
     * @Date 11:18 2021/7/14
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "公司代理下拉框", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/生成应收应付失败管理/失败记录公司代理下拉框")
    @GetMapping("agentSelect/{companyId}")
    public ResponseBo<BaseSelectEntity> agentSelect(@PathVariable("companyId") Long companyId) {
        List<BaseSelectEntity> datas = studentOfferItemFailureService.agentSelect(companyId);
        return new ListResponseBo<>(datas);
    }

    /**
     * 重新生成
     *
     * @Date 11:01 2021/7/9
     * <AUTHOR>
     */
    @ApiOperation(value = "重新生成", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/生成应收应付失败管/重新生成")
    @GetMapping("/{id}")
    public ResponseBo regenerate(@PathVariable("id") Long id) {
        return studentOfferItemFailureService.regenerate(id);
    }


}
