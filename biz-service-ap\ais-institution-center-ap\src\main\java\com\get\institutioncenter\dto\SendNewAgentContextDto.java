package com.get.institutioncenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseSelectEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/7/4 15:40
 * @desciption:
 */
@Data
public class SendNewAgentContextDto extends BaseSelectEntity {

    @ApiModelProperty(value = "步骤ids")
    private Set<Long> stepIds;


    @ApiModelProperty(value = "入学时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date intakeTimeStart;

    @ApiModelProperty(value = "入学时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date intakeTimeEnd;

    //代理邮件
    @ApiModelProperty(value = "代理邮件")
    private String[] agentEmails;


    @ApiModelProperty(value = "国家id")
    private Long fkCountryId;

    @ApiModelProperty(value = "学校id")
    private Long fkInstitutionId;


    @ApiModelProperty(value = "标题")
    private String title;
    /**
     * 描述内容
     */
    @ApiModelProperty(value = "描述内容")
    private String description;

    @ApiModelProperty(value = "创建人邮箱")
    private String createStaffEmail;

}
