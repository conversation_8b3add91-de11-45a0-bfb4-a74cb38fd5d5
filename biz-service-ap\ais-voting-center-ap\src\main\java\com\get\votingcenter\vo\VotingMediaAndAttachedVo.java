package com.get.votingcenter.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseEntity;
import com.get.votingcenter.entity.VotingMediaAndAttached;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.util.Date;

/**
 * @Description:媒体附件
 * @Param
 * @Date 11:38 2021/5/12
 * <AUTHOR>
 */
@Data
public class VotingMediaAndAttachedVo  extends BaseEntity {

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    /**
     * 源文件名
     */
    @ApiModelProperty(value = "源文件名")
    private String fileNameOrc;


    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
    private String typeValue;
    /**
     * 文件key
     */
    @ApiModelProperty(value = "文件key")
    private String fileKey;

    /**
     * 文件guid(文档中心)
     */
    @ApiModelProperty(value = "文件guid(文档中心)")
    @Column(name = "fk_file_guid")
    private String fkFileGuid;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    @Column(name = "fk_table_name")
    private String fkTableName;

    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    @Column(name = "fk_table_id")
    private Long fkTableId;
    /**
     * 类型关键字，如：institution_mov/institution_pic/alumnus_head_icon
     */
    @ApiModelProperty(value = "类型关键字，如：institution_mov/institution_pic/alumnus_head_icon")
    @Column(name = "type_key")
    private String typeKey;
    /**
     * 索引值(默认从0开始，同一类型下值唯一)
     */
    @ApiModelProperty(value = "索引值(默认从0开始，同一类型下值唯一)")
    @Column(name = "index_key")
    private Integer indexKey;
    /**
     * 链接
     */
    @ApiModelProperty(value = "链接")
    @Column(name = "link")
    private String link;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;


}
