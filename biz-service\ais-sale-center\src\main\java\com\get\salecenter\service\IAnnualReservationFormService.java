package com.get.salecenter.service;


import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.filecenter.dto.FileDto;
import com.get.salecenter.entity.ConventionPerson;
import com.get.salecenter.vo.*;
import com.get.salecenter.vo.AnnualBoothListVo;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.AnnualReservationFormDto;
import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import com.github.binarywang.wxpay.exception.WxPayException;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/7/7 15:29
 * @verison: 1.0
 * @description:
 */
public interface IAnnualReservationFormService {

    /**
     * @return
     * @Description :获取流程信息
     * @Param [receiptCode]
     * <AUTHOR>
     */
    List<ConventionProcedureVo> getConventionProcedures(String receiptCode);

    /**
     * @return void
     * @Description :新增表单内容
     * @Param [iaeAnnualReservationFormDto]
     * <AUTHOR>
     */
    void addAnnualReservationForm(IaeAnnualReservationFormDto iaeAnnualReservationFormDto);

    /**
     * @return IaeAnnualReservationFormVo
     * @Description :iae报名表单回显接口
     * @Param [receiptCode, roomType]
     * <AUTHOR>
     */
    IaeAnnualReservationFormVo getIaeAnnualReservationFormDto(String receiptCode);

    /**
     * 房间类型下拉框
     *
     * @param receiptCode
     * @return
     */
    List<ConventionHotelVo> getRoomTypeSelect(String receiptCode);

    /**
     * @return ConventionRegistrationVo
     * @Description :机构名下拉框
     * @Param [receiptCode]
     * <AUTHOR>
     */
    List<ConventionRegistrationVo> getInstitutionNameSelect(String receiptCode, Long fkConventionId);

    /**
     * @return void
     * @Description :删除参会人接口，id为参会人
     * @Param [id]
     * <AUTHOR>
     */
    void deleteConventionPerson(Long id, String receiptCode);

    /**
     * @return void
     * @Description :删除快递信息接口，id为conventionRegistrationId
     * @Param [id]
     * <AUTHOR>
     */
    void deleteExpressInfo(Long id, List<ExpressInfoDto> expressInfoDtoList, Integer provideGifts);

    /**
     * @return Boolean
     * @Description : 验证手机号
     * @Param [conventionId, phone]
     * <AUTHOR>
     */
    Boolean validatedTel(Long conventionId, String phone, Long personId);

    /**
     * @return void
     * @Description :保存展位信息
     * @Param [annualReservationBoothDtos]
     * <AUTHOR>
     */
    void saveBoothVos(String receiptCode, List<AnnualReservationBoothDto> annualReservationBoothDtos);

    /**
     * 新增/修改年会参会人员
     *
     * @param annualReservationFormDtos
     */
    void addAnnualPersons(String receiptCode, List<AnnualReservationFormDto> annualReservationFormDtos);

    /**
     * 展位回显
     *
     * @param receiptCode
     * @return
     */
    AnnualBoothListVo getAnnualBoothListDto(String receiptCode);

    /**
     * 参会人回显
     *
     * @param receiptCode
     * @return
     */
    List<AnnualReservationFormVo> getAnnualPerson(String receiptCode);

    /**
     * 获取参会人数量
     *
     * @param receiptCode
     * @return
     */
    Integer getPersonCount(String receiptCode);

    /**
     * 2022年度iae年会代理报名表单
     *
     * @return
     * @Date 11:01 2022/8/9
     * <AUTHOR>
     */
    List<ConventionPerson> addAgentAnnualPersons(List<AnnualReservationFormAgentDto> annualReservationFormAgentDtos, Long conventionId);

    /**
     * 峰会BD下拉框
     *
     * @Date 14:50 2022/8/9
     * <AUTHOR>
     */
    List<BaseSelectEntity> getAreaRegionSelectByConventionId(Long conventionId);

    /**
     * 获取峰会名称
     *
     * @param conventionId
     * @return
     */
    String getConventionName(Long conventionId);


    /**
     * 加拿大冬季Retreat报名
     *
     * @Date 16:01 2023/7/7
     * <AUTHOR>
     */
    void canadaWinterRetreat(CanadaWinterRetreatDto canadaWinterRetreatDto, Long conventionId);

    /**
     * 加拿大冬季Retreat报名下拉框
     *
     * @Date 17:07 2023/7/7
     * <AUTHOR>
     */
    List<EventItemConfigVo> getCanadaWinterRetreatSelect(Long conventionId);

    /**
     * 生成订单
     * @param conventionHotelPayDto
     */
    void generateOrders(ConventionHotelPayDto conventionHotelPayDto, HttpServletResponse response) throws IOException;

    /**
     * 回调
     * @param signatureHeaderByHttpHeaders
     * @param notifyData
     */
    void notifyWeiXinPay(SignatureHeader signatureHeaderByHttpHeaders, String notifyData) throws WxPayException, Exception;

    /**
     * 提示信息
     * @param amountPayNoticeDto
     * @return
     */
    AmountPayNoticeVo getAmountPayNotice(AmountPayNoticeDto amountPayNoticeDto);

    /**
     * 获取支付状态
     * @param fkConventionPersonId
     * @return
     */
    Integer getPayType(Long fkConventionPersonId);

    /**
     * 同住人下拉框
     * @param fkConventionPersonId
     * @return
     */
    List<BaseSelectEntity> getResidentSelect(Long fkConventionId,Long fkConventionPersonId,Long fkConventionRegistrationId);

    /**
     * 常用币种下拉
     * @return
     */
    List<BaseSelectEntity> getCommonCurrencySelect();

    /**
     * 新增发票信息
     * @param invoiceInfoSaveDto
     */
    void saveInvoiceInfo(InvoiceInfoSaveDto invoiceInfoSaveDto);

    /**
     * 回显发票信息
     * @param fkConventionPersonId
     * @return
     */
    InvoiceInfoVo getInvoiceInfo(Long fkConventionPersonId);

    /**
     * 代理年会微信支付
     * @param conventionHotelPayAgentDto
     * @param response
     */
    void agentGenerateOrders(ConventionHotelPayAgentDto conventionHotelPayAgentDto, HttpServletResponse response) throws IOException;

    /**
     * 代理微信支付回调
     * @param signatureHeaderByHttpHeaders
     * @param notifyData
     */
    void notifyWeiXinAgentPay(SignatureHeader signatureHeaderByHttpHeaders, String notifyData) throws WxPayException;

    /**
     * 获取升级房费
     * @return
     **/
    BigDecimal getUpgradeRoomFee();

    /**
     * 学校机构资料收集
     * @param saveDataCollectionFormDto
     */
    void saveDataCollectionForm(SaveDataCollectionFormDto saveDataCollectionFormDto);

    /**
     * 学校机构资料回显
     * @param receiptCode
     * @return
     */
    DataCollectionFormInfoVo dataCollectionFormInfo(String receiptCode);

    /**
     * 上传附件
     *
     * @param files
     * @param receiptCode
     * @return
     */
    List<FileDto> uploadAppendix(MultipartFile[] files, String receiptCode);

}
