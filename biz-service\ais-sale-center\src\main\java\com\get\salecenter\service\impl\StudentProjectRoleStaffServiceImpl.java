package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.DateUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.entity.Staff;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.ClientOfferMapper;
import com.get.salecenter.dao.sale.RStudentOfferItemStepMapper;
import com.get.salecenter.dao.sale.StudentAccommodationMapper;
import com.get.salecenter.dao.sale.StudentInsuranceMapper;
import com.get.salecenter.dao.sale.StudentMapper;
import com.get.salecenter.dao.sale.StudentOfferItemMapper;
import com.get.salecenter.dao.sale.StudentProjectRoleMapper;
import com.get.salecenter.dao.sale.StudentProjectRoleStaffMapper;
import com.get.salecenter.vo.SelItem;
import com.get.salecenter.vo.StudentOfferRoleAndStaffVo;
import com.get.salecenter.vo.StudentProjectRoleStaffVo;
import com.get.salecenter.vo.StudentRoleAndStaffVo;
import com.get.salecenter.entity.ClientOffer;
import com.get.salecenter.entity.RStudentOfferItemStep;
import com.get.salecenter.entity.Student;
import com.get.salecenter.entity.StudentAccommodation;
import com.get.salecenter.entity.StudentInsurance;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.entity.StudentProjectRole;
import com.get.salecenter.entity.StudentProjectRoleStaff;
import com.get.salecenter.service.IClientOfferService;
import com.get.salecenter.service.IStudentAccommodationService;
import com.get.salecenter.service.IStudentInsuranceService;
import com.get.salecenter.service.IStudentOfferService;
import com.get.salecenter.service.IStudentProjectRoleService;
import com.get.salecenter.service.IStudentProjectRoleStaffService;
import com.get.salecenter.utils.ConvertUtils;
import com.get.salecenter.dto.ProjectRoleStaffDto;
import com.get.salecenter.dto.StudentProjectRoleStaffDto;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/11/16
 * @TIME: 10:37
 * @Description:
 **/
@Service
public class StudentProjectRoleStaffServiceImpl extends ServiceImpl<StudentProjectRoleStaffMapper, StudentProjectRoleStaff> implements IStudentProjectRoleStaffService {
    @Resource
    private StudentProjectRoleStaffMapper projectRoleStaffMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    @Lazy
    private IStudentProjectRoleService studentProjectRoleService;
    @Resource
    @Lazy
    private IStudentOfferService studentOfferService;
    @Resource
    private StudentProjectRoleStaffMapper studentProjectRoleStaffMapper;
    @Resource
    private StudentAccommodationMapper studentAccommodationMapper;
    @Resource
    private StudentInsuranceMapper studentInsuranceMapper;
    @Lazy
    @Resource
    private IStudentAccommodationService studentAccommodationService;
    @Lazy
    @Resource
    private IStudentInsuranceService studentInsuranceService;
    @Resource
    private ClientOfferMapper clientOfferMapper;
    @Resource
    private IClientOfferService clientOfferService;

    @Resource
    private StudentMapper studentMapper;

    @Resource
    private StudentProjectRoleMapper studentProjectRoleMapper;

    @Resource
    private StudentOfferItemMapper studentOfferItemMapper;

    @Resource
    private RStudentOfferItemStepMapper rStudentOfferItemStepMapper;

    @Override
    public Long  addProjectRoleStaff(StudentProjectRoleStaffDto projectRoleStaffVo) {
        if (GeneralTool.isEmpty(projectRoleStaffVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (GeneralTool.isEmpty(projectRoleStaffVo.getFkTableId())) {
            List<BaseSelectEntity> studentOfferSelect = studentOfferService.getStudentOfferSelect(projectRoleStaffVo.getFkStudentId(),
                    projectRoleStaffVo.getFkTableName(),null);
            if (GeneralTool.isNotEmpty(studentOfferSelect)){
                List<Long> ids = studentOfferSelect.stream().map(BaseSelectEntity::getId).collect(Collectors.toList());
                List<StudentProjectRoleStaff> projectRoleStaffs = new ArrayList<>();
                for (Long id : ids) {
                    StudentProjectRoleStaff projectRoleStaff = BeanCopyUtils.objClone(projectRoleStaffVo, StudentProjectRoleStaff::new);
                    projectRoleStaff.setFkTableId(id);
                    //校验是否已经绑定
                    Integer record = projectRoleStaffMapper.validateEdit(projectRoleStaff);
                    if (record > 0) {
                        continue;      //已绑定则不需要再次绑定
//                        throw new GetServiceException(LocaleMessageUtils.getMessage("role_bound_exits"));
                    }
                    //校验国家
                    List<StudentProjectRoleStaff> studentProjectRoleStaffs = projectRoleStaffMapper.selectList(Wrappers.<StudentProjectRoleStaff>lambdaQuery().eq(StudentProjectRoleStaff::getFkTableName, projectRoleStaff.getFkTableName())
                            .eq(StudentProjectRoleStaff::getFkTableId, id).eq(StudentProjectRoleStaff::getFkStudentProjectRoleId, projectRoleStaff.getFkStudentProjectRoleId())
                            .eq(StudentProjectRoleStaff::getIsActive, true));
                    if (GeneralTool.isNotEmpty(studentProjectRoleStaffs)){
                        continue;
                    }
                    utilService.updateUserInfoToEntity(projectRoleStaff);
                    projectRoleStaff.setActiveDate(new Date());
                    projectRoleStaffs.add(projectRoleStaff);
                    //projectRoleStaffMapper.insertSelective(projectRoleStaff);
                }
                projectRoleStaffMapper.batchInsertSelective(projectRoleStaffs);
            }
            return null;
            //throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        StudentProjectRoleStaff projectRoleStaff = BeanCopyUtils.objClone(projectRoleStaffVo, StudentProjectRoleStaff::new);
        //校验是否已经绑定
        Integer record = projectRoleStaffMapper.validateEdit(projectRoleStaff);
        if (record > 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("role_bound_exits"));
        }
        List<StudentProjectRoleStaff> studentProjectRoleStaffs = projectRoleStaffMapper.selectList(Wrappers.<StudentProjectRoleStaff>lambdaQuery().eq(StudentProjectRoleStaff::getFkTableName, projectRoleStaff.getFkTableName())
                .eq(StudentProjectRoleStaff::getFkTableId, projectRoleStaff.getFkTableId()).eq(StudentProjectRoleStaff::getFkStudentProjectRoleId, projectRoleStaff.getFkStudentProjectRoleId())
                .eq(StudentProjectRoleStaff::getIsActive, true));
        if (GeneralTool.isNotEmpty(studentProjectRoleStaffs)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("only_one_employee_can_be_bound_to_the_same_role_in_the_same_country"));
        }
        utilService.updateUserInfoToEntity(projectRoleStaff);
        projectRoleStaff.setActiveDate(new Date());
        projectRoleStaffMapper.insertSelective(projectRoleStaff);
        return projectRoleStaff.getId();
    }

    @Override
    public Long batchAddProjectRoleStaff(List<StudentProjectRoleStaffDto> projectRoleStaffVos) {

        return null;
    }

    @Override
    public void batchUpdate(List<StudentProjectRoleStaff> studentProjectRoleStaffs) {
        for (StudentProjectRoleStaff studentProjectRoleStaff : studentProjectRoleStaffs) {
            StudentProjectRole studentProjectRole = studentProjectRoleMapper.selectById(studentProjectRoleStaff.getFkStudentProjectRoleId());
            if (GeneralTool.isNotEmpty(studentProjectRole) && (studentProjectRole.getRoleKey().equals("HTI_COORDINATOR") || studentProjectRole.getRoleKey().equals("HTI_COUNSELLING_SUPPORT"))) {
                List<StudentOfferItem> studentOfferItems = studentOfferItemMapper.selectList(Wrappers.<StudentOfferItem>lambdaQuery().eq(StudentOfferItem::getFkStudentOfferId, studentProjectRoleStaff.getFkTableId()));
                for (StudentOfferItem studentOfferItem : studentOfferItems) {
                    ArrayList<RStudentOfferItemStep> rStudentOfferItemSteps = new ArrayList<>();
                    if (studentOfferItem.getFkStudentOfferItemStepId().equals(1L)) {
                        RStudentOfferItemStep rStudentOfferItemStep = rStudentOfferItemStepMapper.selectOne(Wrappers.<RStudentOfferItemStep>lambdaQuery().eq(RStudentOfferItemStep::getFkStudentOfferItemId, studentOfferItem.getId()).orderByDesc(RStudentOfferItemStep::getGmtCreate).last("limit 1"));
                        rStudentOfferItemSteps.add(rStudentOfferItemStep);
                    }
                    if (studentOfferItem.getFkStudentOfferItemStepId().equals(2L)) {
                        RStudentOfferItemStep rStudentOfferItemStep = rStudentOfferItemStepMapper.selectOne(Wrappers.<RStudentOfferItemStep>lambdaQuery()
                                .eq(RStudentOfferItemStep::getFkStudentOfferItemId, studentOfferItem.getId())
                                .eq(RStudentOfferItemStep::getFkStudentOfferItemStepId,studentOfferItem.getFkStudentOfferItemStepId())
                                .orderByDesc(RStudentOfferItemStep::getGmtCreate).last("limit 1"));
                        rStudentOfferItemSteps.add(rStudentOfferItemStep);
                        List<RStudentOfferItemStep> offerItemSteps = rStudentOfferItemStepMapper.selectList(Wrappers.<RStudentOfferItemStep>lambdaQuery().eq(RStudentOfferItemStep::getFkStudentOfferItemId, studentOfferItem.getId()).eq(RStudentOfferItemStep::getFkStudentOfferItemStepId, 1L));
                        rStudentOfferItemSteps.addAll(offerItemSteps);
                    }
                    for (RStudentOfferItemStep rStudentOfferItemStep : rStudentOfferItemSteps) {
                        if (GeneralTool.isEmpty(rStudentOfferItemStep.getRemark())) {
                            rStudentOfferItemStep.setRemark("【重新指派，原时间：" + DateUtil.formatDateTime(rStudentOfferItemStep.getGmtCreate()) + "】");
                        } else {
                            rStudentOfferItemStep.setRemark(rStudentOfferItemStep.getRemark() + "【重新指派，原时间：" + DateUtil.formatDateTime(rStudentOfferItemStep.getGmtCreate()) + "】");
                        }
                        rStudentOfferItemStep.setReassignTime(new Date());
//                            utilService.setUpdateInfo(rStudentOfferItemStep);
                        rStudentOfferItemStepMapper.updateById(rStudentOfferItemStep);
                    }
                }
            }
        }

        if (GeneralTool.isNotEmpty(studentProjectRoleStaffs)) {
            updateBatchById(studentProjectRoleStaffs);
        }
    }

    @Override
    public void batchAdd(List<StudentProjectRoleStaff> studentProjectRoleStaffs) {
        if (GeneralTool.isNotEmpty(studentProjectRoleStaffs)) {
//            List<StudentProjectRoleStaff> collect = studentProjectRoleStaffs.stream().filter(f -> projectRoleStaffMapper.validateEdit(f) == 0).collect(Collectors.toList());
            saveBatch(studentProjectRoleStaffs,studentProjectRoleStaffs.size());
        }
    }

    /**
     * 批量处理项目成员
     * @param projectRoleStaffDtos
     * @param id
     * @param typeKey
     */
    @Override
    public void batchProcessorPrs(List<ProjectRoleStaffDto> projectRoleStaffDtos, Long id, String typeKey) {
            List<StudentProjectRoleStaff> projectRoleStaffs = studentProjectRoleStaffMapper.selectList(Wrappers.<StudentProjectRoleStaff>lambdaQuery().eq(StudentProjectRoleStaff::getFkTableName, typeKey)
                    .eq(StudentProjectRoleStaff::getFkTableId, id).eq(StudentProjectRoleStaff::getIsActive,true));
            List<StudentProjectRoleStaff> upList = new ArrayList<>();
            //新增的项目成员
            List<StudentProjectRoleStaff> addList = new ArrayList<>();
            for (ProjectRoleStaffDto staffVo : projectRoleStaffDtos) {
                Long roleId = staffVo.getFkRoleId();
                Long staffId = staffVo.getFkStaffId();
                boolean b = projectRoleStaffs.stream().anyMatch(p -> p.getFkStudentProjectRoleId().equals(roleId) && p.getFkStaffId().equals(staffId));
                if (!b) {
                    StudentProjectRoleStaff roleStaff = new StudentProjectRoleStaff();
                    roleStaff.setFkTableId(id);
                    roleStaff.setFkTableName(typeKey);
                    roleStaff.setFkStudentProjectRoleId(staffVo.getFkRoleId());
                    roleStaff.setFkStaffId(staffVo.getFkStaffId());
                    roleStaff.setIsActive(true);
                    roleStaff.setActiveDate(new Date());
                    utilService.setCreateInfo(roleStaff);
                    addList.add(roleStaff);
                }
            }
            //要解绑的项目成员集合
            List<StudentProjectRoleStaff> collect = new ArrayList<>();
            //以前有绑定的项目成员和Vo的项目成员的交集集合
            Map<Long,StudentProjectRoleStaff> temp = new HashMap<>();
            Map<Long, List<StudentProjectRoleStaff>> listMap = projectRoleStaffs.stream().collect(Collectors.groupingBy(StudentProjectRoleStaff::getId));
            for (StudentProjectRoleStaff roleStaff : projectRoleStaffs) {
                Long projectRoleId = roleStaff.getFkStudentProjectRoleId();
                Long fkStaffId = roleStaff.getFkStaffId();
                for (ProjectRoleStaffDto staffVo : projectRoleStaffDtos) {
                    if (projectRoleId.equals(staffVo.getFkRoleId()) && fkStaffId.equals(staffVo.getFkStaffId())) {
                        temp.put(roleStaff.getId(),roleStaff);
                    }
                }
            }
            listMap.forEach((k,v)->{
                if (!temp.containsKey(k)) {
                    collect.addAll(v);
                }
            });
            collect.forEach(c->{
                if (c.getUnactiveDate()==null) {
                    c.setUnactiveDate(new Date());
                }
                c.setIsActive(false);
                utilService.setUpdateInfo(c);
                upList.add(c);
            });
            batchAdd(addList);
            batchUpdate(upList);
    }

    @Override
    public List<StudentProjectRoleStaffVo> getProjectRoleStaff(StudentProjectRoleStaffDto projectRoleStaffVo, Page page) {
//        Example example = new Example(StudentProjectRoleStaff.class);
//        Example.Criteria criteria = example.createCriteria();
//        LambdaQueryWrapper<StudentProjectRoleStaff> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(projectRoleStaffVo)) {
//            if (GeneralTool.isNotEmpty(projectRoleStaffVo.getFkStudentId())) {
//                List<Long> offerIds = getOfferIds(projectRoleStaffVo);
//                lambdaQueryWrapper.in(StudentProjectRoleStaff::getFkTableId, offerIds);
//            }
//            if (GeneralTool.isNotEmpty(projectRoleStaffVo.getFkTableName())) {
////                criteria.andEqualTo("fkTableName", projectRoleStaffVo.getFkTableName());
//                lambdaQueryWrapper.eq(StudentProjectRoleStaff::getFkTableName,projectRoleStaffVo.getFkTableName());
//            }
//            if (GeneralTool.isNotEmpty(projectRoleStaffVo.getFkStaffId())) {
////                criteria.andEqualTo("fkStaffId", projectRoleStaffVo.getFkStaffId());
//                lambdaQueryWrapper.eq(StudentProjectRoleStaff::getFkStaffId,projectRoleStaffVo.getFkStaffId());
//            }
//            if (GeneralTool.isNotEmpty(projectRoleStaffVo.getFkStudentProjectRoleId())) {
////                criteria.andEqualTo("fkStudentProjectRoleId", projectRoleStaffVo.getFkStudentProjectRoleId());
//                lambdaQueryWrapper.eq(StudentProjectRoleStaff::getFkStudentProjectRoleId, projectRoleStaffVo.getFkStudentProjectRoleId());
//            }
            if (GeneralTool.isNotEmpty(projectRoleStaffVo.getFkStudentId())) {
                List<Long> offerIds = getOfferIds(projectRoleStaffVo);
                projectRoleStaffVo.setOfferIds(offerIds);
                List<Long> accommodationIds = getAccommodationIds(projectRoleStaffVo);
                projectRoleStaffVo.setAccommodationIds(accommodationIds);
                List<Long> insuranceIds = getInsuranceIds(projectRoleStaffVo);
                projectRoleStaffVo.setInsuranceIds(insuranceIds);
            }
        }

        List<Long> staffFollowerIds = new ArrayList<>();
        //员工id + 业务下属员工ids
        List<Long> staffFollowerData = permissionCenterClient.getStaffFollowerIds(SecureUtil.getStaffId()).getData();
        if (GeneralTool.isNotEmpty(staffFollowerData)) {
            staffFollowerIds = staffFollowerData.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
        staffFollowerIds.add(SecureUtil.getStaffId());

//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        IPage<StudentProjectRoleStaff> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<StudentProjectRoleStaff> studentProjectRoleStaffs = projectRoleStaffMapper.getProjectRoleStaff(pages, projectRoleStaffVo,staffFollowerIds, SecureUtil.getStaffInfo().getIsStudentAdmin(),
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds());
        page.setAll((int) pages.getTotal());

        List<StudentProjectRoleStaffVo> collect = studentProjectRoleStaffs.stream().map(studentProjectRoleStaff ->
                BeanCopyUtils.objClone(studentProjectRoleStaff, StudentProjectRoleStaffVo::new)).collect(Collectors.toList());

        setName(collect,projectRoleStaffVo);
        return collect;
    }

    @Override
    public List<StudentProjectRoleStaffVo> getClientProjectRoleStaff(StudentProjectRoleStaffDto projectRoleStaffVo, Page page) {
        if (GeneralTool.isNotEmpty(projectRoleStaffVo)) {
            if (GeneralTool.isNotEmpty(projectRoleStaffVo.getFkStudentId())) {
                List<Long> clientOfferIds = getClientOfferIds(projectRoleStaffVo);
                projectRoleStaffVo.setClientOfferIds(clientOfferIds);
            }
        }

        List<Long> staffFollowerIds = new ArrayList<>();
        //员工id + 业务下属员工ids
        List<Long> staffFollowerData = permissionCenterClient.getStaffFollowerIds(SecureUtil.getStaffId()).getData();
        if (GeneralTool.isNotEmpty(staffFollowerData)) {
            staffFollowerIds = staffFollowerData.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
        staffFollowerIds.add(SecureUtil.getStaffId());
        List<StudentProjectRoleStaff> studentProjectRoleStaffs = new ArrayList<>();

        if (page == null){
            studentProjectRoleStaffs = projectRoleStaffMapper.getClientProjectRoleStaff(null, projectRoleStaffVo,staffFollowerIds);
        }else {
            IPage<StudentProjectRoleStaff> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            studentProjectRoleStaffs = projectRoleStaffMapper.getClientProjectRoleStaff(pages, projectRoleStaffVo,staffFollowerIds);
            page.setAll((int) pages.getTotal());
        }


        List<StudentProjectRoleStaffVo> collect = studentProjectRoleStaffs.stream().map(studentProjectRoleStaff ->
                BeanCopyUtils.objClone(studentProjectRoleStaff, StudentProjectRoleStaffVo::new)).collect(Collectors.toList());

        setName(collect,projectRoleStaffVo);
        return collect;
    }

    private List<Long> getClientOfferIds(StudentProjectRoleStaffDto projectRoleStaffVo) {
        List<ClientOffer> clientOffer = clientOfferMapper.selectList(Wrappers.<ClientOffer>lambdaQuery().eq(ClientOffer::getFkClientId,
                projectRoleStaffVo.getFkStudentId()));
        if (GeneralTool.isNotEmpty(clientOffer)) {
            return clientOffer.stream().map(ClientOffer::getId).collect(Collectors.toList());
        } else {
            List<Long> list = new ArrayList<>();
            list.add(0L);
            return list;
        }
    }

//    @Override
//    public List<StudentProjectRoleStaffVo> getProjectRoleStaffNoPage(StudentProjectRoleStaffDto projectRoleStaffVo)  {
//        LambdaQueryWrapper<StudentProjectRoleStaff> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        if (GeneralTool.isNotEmpty(projectRoleStaffVo)) {
//            if (GeneralTool.isNotEmpty(projectRoleStaffVo.getFkStudentId())) {
//                List<Long> offerIds = getOfferIds(projectRoleStaffVo);
//                lambdaQueryWrapper.in(StudentProjectRoleStaff::getFkTableId, offerIds);
//            }
//            if (GeneralTool.isNotEmpty(projectRoleStaffVo.getFkTableName())) {
//                lambdaQueryWrapper.eq(StudentProjectRoleStaff::getFkTableName, projectRoleStaffVo.getFkTableName());
//            }
//            if (GeneralTool.isNotEmpty(projectRoleStaffVo.getFkStaffId())) {
//                lambdaQueryWrapper.eq(StudentProjectRoleStaff::getFkTableId,projectRoleStaffVo.getFkStaffId());
//            }
//            if (GeneralTool.isNotEmpty(projectRoleStaffVo.getFkStudentProjectRoleId())) {
//                lambdaQueryWrapper.eq(StudentProjectRoleStaff::getFkStudentProjectRoleId,projectRoleStaffVo.getFkStudentProjectRoleId());
//            }
//        }
//        lambdaQueryWrapper.eq(StudentProjectRoleStaff::getIsActive, 1);
//        List<StudentProjectRoleStaff> studentProjectRoleStaffs = projectRoleStaffMapper.selectList(lambdaQueryWrapper);
//
//        List<StudentProjectRoleStaffVo> collect = studentProjectRoleStaffs.stream().map(studentProjectRoleStaff ->
//                BeanCopyUtils.objClone(studentProjectRoleStaff, StudentProjectRoleStaffVo::new)).collect(Collectors.toList());
//
//        setName(collect);
//        return collect;
//    }

    private List<Long> getInsuranceIds(StudentProjectRoleStaffDto projectRoleStaffVo) {
        List<StudentInsurance> studentInsurances = studentInsuranceMapper.selectList(Wrappers.<StudentInsurance>lambdaQuery().eq(StudentInsurance::getFkStudentId, projectRoleStaffVo.getFkStudentId()));
        if (GeneralTool.isNotEmpty(studentInsurances)) {
            return studentInsurances.stream().map(StudentInsurance::getId).collect(Collectors.toList());
        } else {
            List<Long> list = new ArrayList<>();
            list.add(0L);
            return list;
        }
    }

    private List<Long> getAccommodationIds(StudentProjectRoleStaffDto projectRoleStaffVo) {
        List<StudentAccommodation> studentAccommodations = studentAccommodationMapper.selectList(Wrappers.<StudentAccommodation>lambdaQuery().eq(StudentAccommodation::getFkStudentId, projectRoleStaffVo.getFkStudentId()));
        if (GeneralTool.isNotEmpty(studentAccommodations)) {
            return studentAccommodations.stream().map(StudentAccommodation::getId).collect(Collectors.toList());
        } else {
            List<Long> list = new ArrayList<>();
            list.add(0L);
            return list;
        }
    }

    @Override
    public List<Map<Long, String>> getProjectRoleStaffNoPage(String tableName, List<Long> studentIds) {

        List<Map<Long, String>> studentProjectRoleStaffs = projectRoleStaffMapper.getProjectRoleStaffNoPage(tableName, studentIds);

        return studentProjectRoleStaffs;
    }

    @Override
    public Map<Long, Object> getAFollowUpConsultant(String tableName, Set<Long> clientIds, String key) {
        List<SelItem> follow = projectRoleStaffMapper.getAFollowUpConsultant(tableName, clientIds, key);
        return ConvertUtils.convert(follow);
    }

    @Override
    public StudentProjectRoleStaffVo findProjectRoleStaffById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StudentProjectRoleStaff projectRoleStaff = projectRoleStaffMapper.selectById(id);
        return BeanCopyUtils.objClone(projectRoleStaff, StudentProjectRoleStaffVo::new);
    }


    @Override
    public StudentProjectRoleStaffVo updateProjectRoleStaff(StudentProjectRoleStaffDto projectRoleStaffVo) {
        if (GeneralTool.isEmpty(projectRoleStaffVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        StudentProjectRoleStaff projectRoleStaff = BeanCopyUtils.objClone(projectRoleStaffVo, StudentProjectRoleStaff::new);
        if (!projectRoleStaffVo.getIsActive()) {
            projectRoleStaff.setUnactiveDate(new Date());
        } else {
            Integer record = projectRoleStaffMapper.validateEdit(projectRoleStaff);
            if (record == 1) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("role_bound_exits"));
            }
        }
        utilService.updateUserInfoToEntity(projectRoleStaff);
        projectRoleStaffMapper.updateById(projectRoleStaff);
        return findProjectRoleStaffById(projectRoleStaff.getId());
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        projectRoleStaffMapper.deleteById(id);
    }

    @Override
    public void unableProjectRoleStaff(Long tableId, Long status) {
//        Example example = new Example(StudentProjectRoleStaff.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkTableName", TableEnum.SALE_STUDENT_OFFER.key);
//        criteria.andEqualTo("fkTableId", tableId);
        LambdaQueryWrapper<StudentProjectRoleStaff> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StudentProjectRoleStaff::getFkTableName, TableEnum.SALE_STUDENT_OFFER.key);
        lambdaQueryWrapper.eq(StudentProjectRoleStaff::getFkTableId, tableId);
        StudentProjectRoleStaff roleStaff = new StudentProjectRoleStaff();
        if (status == 0) {
            //需求更改，作废不更改学生项目成员角色
//            roleStaff.setIsActive(false);
//            roleStaff.setUnactiveDate(new Date());
        } else {
            roleStaff.setIsActive(true);
            roleStaff.setActiveDate(new Date());
            projectRoleStaffMapper.update(roleStaff, lambdaQueryWrapper);
        }
        //projectRoleStaffMapper.update(roleStaff, lambdaQueryWrapper);
    }

    @Override
    public List<StudentRoleAndStaffVo> getRoleAndStaffByTableId(Long tableId, String typeKey) {
        if (GeneralTool.isEmpty(tableId)) {
            return null;
        }
//        Example example = new Example(StudentProjectRoleStaff.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkTableName", TableEnum.SALE_STUDENT_OFFER.key);
//        criteria.andEqualTo("fkTableId", tableId);
//        criteria.andEqualTo("isActive", 1);
//        List<StudentProjectRoleStaff> roleStaffs = projectRoleStaffMapper.selectByExample(example);
        List<StudentProjectRoleStaff> roleStaffs = projectRoleStaffMapper.selectList(Wrappers.<StudentProjectRoleStaff>lambdaQuery()
                .eq(StudentProjectRoleStaff::getFkTableName, typeKey)
                .eq(StudentProjectRoleStaff::getFkTableId, tableId)
                .eq(StudentProjectRoleStaff::getIsActive, 1));

        List<StudentRoleAndStaffVo> roleAndStaffDtos = new ArrayList<>();
        Set<Long> projectRoleIdSet = roleStaffs.stream().map(StudentProjectRoleStaff::getFkStudentProjectRoleId).collect(Collectors.toSet());
        projectRoleIdSet.removeIf(Objects::isNull);
        List<StudentProjectRole> studentProjectRoles;
        Map<Long, Integer> viewOrderMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(projectRoleIdSet)) {
            studentProjectRoles = studentProjectRoleStaffMapper.getStudentProjectRoleByIdSet(projectRoleIdSet);
            viewOrderMap = studentProjectRoles.stream().filter(studentProjectRole -> GeneralTool.isNotEmpty(studentProjectRole.getViewOrder())).collect(Collectors.toMap(StudentProjectRole::getId, StudentProjectRole::getViewOrder));
        }
        for (StudentProjectRoleStaff roleStaff : roleStaffs) {
            StudentRoleAndStaffVo roleAndStaffDto = new StudentRoleAndStaffVo();
            //设置员工名称
            Result<String> result = permissionCenterClient.getStaffName(roleStaff.getFkStaffId());
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                roleAndStaffDto.setStaffName(result.getData());
            }
            //设置角色名称
            String roleName = studentProjectRoleService.getRoleNameById(roleStaff.getFkStudentProjectRoleId());
            roleAndStaffDto.setRoleName(roleName);
            roleAndStaffDto.setFkStaffId(roleStaff.getFkStaffId());
            roleAndStaffDto.setFkStudentProjectRoleId(roleStaff.getFkStudentProjectRoleId());

            roleAndStaffDto.setId(roleStaff.getId());
            if (GeneralTool.isNotEmpty(viewOrderMap)) {
                roleAndStaffDto.setStudentProjectRoleViewOrder(viewOrderMap.get(roleStaff.getFkStudentProjectRoleId()));
            }
            roleAndStaffDtos.add(roleAndStaffDto);
        }
        if (GeneralTool.isNotEmpty(viewOrderMap)) {
            return roleAndStaffDtos.stream().sorted(Comparator.comparing(StudentRoleAndStaffVo::getStudentProjectRoleViewOrder).reversed()).collect(Collectors.toList());
        } else {
            return roleAndStaffDtos;
        }
    }

    @Override
    public Map<Long, List<StudentRoleAndStaffVo>> getRoleAndStaffByTableIds(Set<Long> dtoIds) {
        if (GeneralTool.isEmpty(dtoIds)) {
            return new HashMap<>();
        }
        Map<Long, List<StudentRoleAndStaffVo>> map = new HashMap<>();
        List<StudentProjectRoleStaff> roleStaffs = projectRoleStaffMapper.selectList(Wrappers.<StudentProjectRoleStaff>lambdaQuery()
                .eq(StudentProjectRoleStaff::getFkTableName, TableEnum.SALE_STUDENT_OFFER.key).in(StudentProjectRoleStaff::getFkTableId, dtoIds)
                .eq(StudentProjectRoleStaff::getIsActive, 1));

        List<StudentRoleAndStaffVo> roleAndStaffDtos = new ArrayList<>();
        Set<Long> projectRoleIdSet = roleStaffs.stream().map(StudentProjectRoleStaff::getFkStudentProjectRoleId).collect(Collectors.toSet());
        projectRoleIdSet.removeIf(Objects::isNull);
        List<StudentProjectRole> studentProjectRoles;
        Map<Long, Integer> viewOrderMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(projectRoleIdSet)) {
            studentProjectRoles = studentProjectRoleStaffMapper.getStudentProjectRoleByIdSet(projectRoleIdSet);
            viewOrderMap = studentProjectRoles.stream().filter(studentProjectRole -> GeneralTool.isNotEmpty(studentProjectRole.getViewOrder())).collect(Collectors.toMap(StudentProjectRole::getId, StudentProjectRole::getViewOrder));
        }

        Map<Long, String> staffNamesByIds = new HashMap<>();
        Set<Long> staffIds = roleStaffs.stream().map(StudentProjectRoleStaff::getFkStaffId).collect(Collectors.toSet());
        roleStaffs.removeIf(Objects::isNull);
        if (GeneralTool.isEmpty(staffIds)) {
            staffIds.add(0L);
        }
        Result<Map<Long, String>> result = permissionCenterClient.getStaffNameMap(staffIds);
        if (result.isSuccess() && result.getData() != null) {
            staffNamesByIds = result.getData();
        }


        Map<Long, String> roleNamesByIds = new HashMap<>();
        Set<Long> roleIds = roleStaffs.stream().map(StudentProjectRoleStaff::getFkStudentProjectRoleId).collect(Collectors.toSet());
        roleIds.removeIf(Objects::isNull);
        if (GeneralTool.isEmpty(roleIds)) {
            roleIds.add(0L);
        }
        roleNamesByIds = studentProjectRoleService.getRoleNameByIds(roleIds);

        for (StudentProjectRoleStaff roleStaff : roleStaffs) {
            StudentRoleAndStaffVo roleAndStaffDto = new StudentRoleAndStaffVo();
            //设置员工名称
            String staffName = staffNamesByIds.get(roleStaff.getFkStaffId());
            roleAndStaffDto.setStaffName(staffName);
            //设置角色名称
            String roleName = roleNamesByIds.get(roleStaff.getFkStudentProjectRoleId());
            roleAndStaffDto.setRoleName(roleName);

            roleAndStaffDto.setId(roleStaff.getId());
            roleAndStaffDto.setFkStaffId(roleStaff.getFkStaffId());
            roleAndStaffDto.setFkStudentProjectRoleId(roleStaff.getFkStudentProjectRoleId());
            if (GeneralTool.isNotEmpty(viewOrderMap)) {
                roleAndStaffDto.setStudentProjectRoleViewOrder(viewOrderMap.get(roleStaff.getFkStudentProjectRoleId()));
            }
            roleAndStaffDto.setFkTableId(roleStaff.getFkTableId());
            roleAndStaffDtos.add(roleAndStaffDto);
        }

        if (GeneralTool.isNotEmpty(viewOrderMap)) {
            roleAndStaffDtos = roleAndStaffDtos.stream().sorted(Comparator.comparing(StudentRoleAndStaffVo::getStudentProjectRoleViewOrder).reversed()).collect(Collectors.toList());
            return roleAndStaffDtos.stream().collect(Collectors.groupingBy(StudentRoleAndStaffVo::getFkTableId));
        } else {
            return roleAndStaffDtos.stream().collect(Collectors.groupingBy(StudentRoleAndStaffVo::getFkTableId));
        }
    }

    @Override
    public Map<Long, List<StudentOfferRoleAndStaffVo>> getRoleAndStaffByOfferIds(Set<Long> offerIds) {
        if (GeneralTool.isEmpty(offerIds)) {
            return new HashMap<>();
        }

        List<StudentOfferRoleAndStaffVo> roleStaffs = studentProjectRoleStaffMapper.selectListRoleByIds(offerIds);
        List<StudentOfferRoleAndStaffVo> roleAndStaffDtos = new ArrayList<>();
        //TODO 改过
       // Set<Long> projectRoleIdSet = roleStaffs.stream().map(StudentProjectRoleStaff::getFkStudentProjectRoleId).collect(Collectors.toSet());
        Set<Long> projectRoleIdSet = roleStaffs.stream().map(StudentOfferRoleAndStaffVo::getFkStudentProjectRoleId).collect(Collectors.toSet());
        projectRoleIdSet.removeIf(Objects::isNull);

        Map<Long, Staff> staffByIds = new HashMap<>();
        //TODO 改过
       // Set<Long> staffIds = roleStaffs.stream().map(StudentProjectRoleStaff::getFkStaffId).collect(Collectors.toSet());
        Set<Long> staffIds = roleStaffs.stream().map(StudentOfferRoleAndStaffVo::getFkStaffId).collect(Collectors.toSet());
        roleStaffs.removeIf(Objects::isNull);
        if (GeneralTool.isEmpty(staffIds)) {
            staffIds.add(0L);
        }
        Result<Map<Long, Staff>> result = permissionCenterClient.getStaffMapByStaffIds(staffIds);
        if (result.isSuccess() && result.getData() != null) {
            staffByIds = result.getData();
        }


        for (StudentOfferRoleAndStaffVo roleStaff : roleStaffs) {
            StudentOfferRoleAndStaffVo roleAndStaffDto = new StudentOfferRoleAndStaffVo();
            //设置员工名称
            Staff staff = staffByIds.get(roleStaff.getFkStaffId());
            if (GeneralTool.isNotEmpty(staff)){
                roleAndStaffDto.setStaffName(staff.getName());
                roleAndStaffDto.setStaffNameEn(staff.getNameEn());
                roleAndStaffDto.setEmail(staff.getEmail());
            }

//            //设置角色名称
            roleAndStaffDto.setRoleName(roleStaff.getRoleName());
            roleAndStaffDto.setRoleKey(roleStaff.getRoleKey());
            roleAndStaffDto.setId(roleStaff.getId());
            roleAndStaffDto.setFkStaffId(roleStaff.getFkStaffId());
            roleAndStaffDto.setFkTableId(roleStaff.getFkTableId());
            roleAndStaffDtos.add(roleAndStaffDto);
        }

        return roleAndStaffDtos.stream().collect(Collectors.groupingBy(StudentOfferRoleAndStaffVo::getFkTableId));
    }

    @Override
    public Map<Long, List<StudentOfferRoleAndStaffVo>> getRoleAndStaffByClientOfferIds(Set<Long> offerIds) {
        if (GeneralTool.isEmpty(offerIds)) {
            return new HashMap<>();
        }

        List<StudentOfferRoleAndStaffVo> roleStaffs = studentProjectRoleStaffMapper.selectListRoleByClientIds(offerIds);
        List<StudentOfferRoleAndStaffVo> roleAndStaffDtos = new ArrayList<>();
        //TODO 改过
       // Set<Long> projectRoleIdSet = roleStaffs.stream().map(StudentProjectRoleStaff::getFkStudentProjectRoleId).collect(Collectors.toSet());
        Set<Long> projectRoleIdSet = roleStaffs.stream().map(StudentOfferRoleAndStaffVo::getFkStudentProjectRoleId).collect(Collectors.toSet());
        projectRoleIdSet.removeIf(Objects::isNull);

        Map<Long, Staff> staffByIds = new HashMap<>();
        //TODO 改过
        //Set<Long> staffIds = roleStaffs.stream().map(StudentProjectRoleStaff::getFkStaffId).collect(Collectors.toSet());
        Set<Long> staffIds = roleStaffs.stream().map(StudentOfferRoleAndStaffVo::getFkStaffId).collect(Collectors.toSet());
        roleStaffs.removeIf(Objects::isNull);
        if (GeneralTool.isEmpty(staffIds)) {
            staffIds.add(0L);
        }
        Result<Map<Long, Staff>> result = permissionCenterClient.getStaffMapByStaffIds(staffIds);
        if (result.isSuccess() && result.getData() != null) {
            staffByIds = result.getData();
        }


        for (StudentOfferRoleAndStaffVo roleStaff : roleStaffs) {
            StudentOfferRoleAndStaffVo roleAndStaffDto = new StudentOfferRoleAndStaffVo();
            //设置员工名称
            Staff staff = staffByIds.get(roleStaff.getFkStaffId());
            roleAndStaffDto.setStaffName(staff.getName());
            roleAndStaffDto.setStaffNameEn(staff.getNameEn());
            roleAndStaffDto.setEmail(staff.getEmail());
//            //设置角色名称
            roleAndStaffDto.setRoleName(roleStaff.getRoleName());
            roleAndStaffDto.setRoleKey(roleStaff.getRoleKey());
            roleAndStaffDto.setId(roleStaff.getId());
            roleAndStaffDto.setFkStaffId(roleStaff.getFkStaffId());
            roleAndStaffDto.setFkTableId(roleStaff.getFkTableId());
            roleAndStaffDtos.add(roleAndStaffDto);
        }

        return roleAndStaffDtos.stream().collect(Collectors.groupingBy(StudentOfferRoleAndStaffVo::getFkTableId));
    }

    private List<Long> getOfferIds(StudentProjectRoleStaffDto projectRoleStaffVo) {
        List<Long> offerIds = studentOfferService.getOfferIdByStudentId(projectRoleStaffVo.getFkStudentId());
        if (GeneralTool.isEmpty(offerIds)) {
            offerIds = new ArrayList<>();
            offerIds.add(0L);
        }
        return offerIds;
    }

    private void setName(List<StudentProjectRoleStaffVo> collect, StudentProjectRoleStaffDto studentProjectRoleStaffDto) {
        //查询员工名称Map
        Set<Long> fkStaffIds = collect.stream().map(StudentProjectRoleStaffVo::getFkStaffId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> staffNamesByIds = permissionCenterClient.getStaffNamesByIds(fkStaffIds);
        //查询角色名称
        Set<Long> fkStudentProjectRoleIds = collect.stream().map(StudentProjectRoleStaffVo::getFkStudentProjectRoleId).collect(Collectors.toSet());
        Map<Long, String> roleNameByIds = studentProjectRoleService.getRoleNameByIds(fkStudentProjectRoleIds);

        //查询项目目标名称,留学申请方案
        Set<Long> offerIds =
                collect.stream().filter(x -> x.getFkTableName().equals(TableEnum.SALE_STUDENT_OFFER.key)).map(StudentProjectRoleStaffVo::getFkTableId).collect(Collectors.toSet());
        Map<Long, String> offerNums = studentOfferService.getOfferNumByIds(offerIds);
        Set<Long> limitOfferIds = new HashSet<>();
        if (GeneralTool.isNotEmpty(offerIds)&&GeneralTool.isNotEmpty(studentProjectRoleStaffDto)){
            Student student = studentMapper.selectById(studentProjectRoleStaffDto.getFkStudentId());
            if (GeneralTool.isNotEmpty(student)){
                limitOfferIds = studentOfferService.getProjectLimitConfigKey(offerIds, student.getFkCompanyId());
            }
        }

        //查询项目目标名称,留学住宿
        Set<Long> accommodationIds = collect.stream().filter(x -> x.getFkTableName().equals(TableEnum.SALE_STUDENT_ACCOMMODATION.key)).map(StudentProjectRoleStaffVo::getFkTableId).collect(Collectors.toSet());
        Map<Long, String> accommodationNums = studentAccommodationService.getNumByIds(accommodationIds);

        //查询项目目标名称,留学保险
        Set<Long> insuranceIds = collect.stream().filter(x -> x.getFkTableName().equals(TableEnum.SALE_STUDENT_INSURANCE.key)).map(StudentProjectRoleStaffVo::getFkTableId).collect(Collectors.toSet());
        Map<Long, String> insuranceNums = studentInsuranceService.getNumByIds(insuranceIds);

        //查询项目目标名称,留学保险
        Set<Long> clientOfferIds =
                collect.stream().filter(x -> x.getFkTableName().equals(TableEnum.CLIENT_OFFER.key)).map(StudentProjectRoleStaffVo::getFkTableId).collect(Collectors.toSet());
        Map<Long, String> clientOfferNums = clientOfferService.getNumByIds(clientOfferIds);

        for (StudentProjectRoleStaffVo roleStaffDto : collect) {
            if (GeneralTool.isNotEmpty(roleStaffDto)) {
                //设置员工名称
//                Result<String> result = permissionCenterClient.getStaffName(roleStaffDto.getFkStaffId());
//                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
//                    roleStaffDto.setStaffName(result.getData());
//                }
                if (GeneralTool.isNotEmpty(staffNamesByIds)) {
                    roleStaffDto.setStaffName(staffNamesByIds.get(roleStaffDto.getFkStaffId()));
                }

                //设置角色名称
//                String roleName = studentProjectRoleService.getRoleNameById(roleStaffDto.getFkStudentProjectRoleId());
//                roleStaffDto.setRoleName(roleName);
                if (GeneralTool.isNotEmpty(roleNameByIds)){
                    roleStaffDto.setRoleName(roleNameByIds.get(roleStaffDto.getFkStudentProjectRoleId()));
                }
                //设置项目名称（表名）
                Map<Object, Object> typeKeyNamesByTypeKey = new HashMap<>();
                List<Map<String, Object>> maps = TableEnum.enumsTranslation2Arrays(TableEnum.BUSINESS_TYPE);
                for (Map<String, Object> map : maps) {
                    typeKeyNamesByTypeKey.put(map.get("key"), map.get("value"));
                }
                roleStaffDto.setProjectName((String) typeKeyNamesByTypeKey.get(roleStaffDto.getFkTableName()));
//                roleStaffDto.setProjectName(TableEnum.getValue(roleStaffDto.getFkTableName()));

                if (TableEnum.SALE_STUDENT_OFFER.key.equals(roleStaffDto.getFkTableName())) {
//                    String offerNum = studentOfferService.getOfferNumById(roleStaffDto.getFkTableId());
//                    roleStaffDto.setProjectTargetName(offerNum);
                    if (GeneralTool.isNotEmpty(offerNums)){
                        roleStaffDto.setProjectTargetName(offerNums.get(roleStaffDto.getFkTableId()));
                    }
                    if(GeneralTool.isNotEmpty(limitOfferIds) && limitOfferIds.contains(roleStaffDto.getFkTableId())){
                        roleStaffDto.setIsProjectMembersLimit(true);
                    }
                } else if (TableEnum.SALE_STUDENT_ACCOMMODATION.key.equals(roleStaffDto.getFkTableName())) {
//                    String offerNum = studentAccommodationMapper.getNumById(roleStaffDto.getFkTableId());
//                    roleStaffDto.setProjectTargetName(offerNum);
                    if (GeneralTool.isNotEmpty(accommodationNums)){
                        roleStaffDto.setProjectTargetName(accommodationNums.get(roleStaffDto.getFkTableId()));
                    }
                } else if (TableEnum.SALE_STUDENT_INSURANCE.key.equals(roleStaffDto.getFkTableName())) {
//                    String offerNum = studentInsuranceMapper.getNumById(roleStaffDto.getFkTableId());
//                    roleStaffDto.setProjectTargetName(offerNum);
                    if (GeneralTool.isNotEmpty(insuranceNums)){
                        roleStaffDto.setProjectTargetName(insuranceNums.get(roleStaffDto.getFkTableId()));
                    }
                }else if (TableEnum.CLIENT_OFFER.key.equals(roleStaffDto.getFkTableName())) {
                    if (GeneralTool.isNotEmpty(clientOfferNums)){
                        roleStaffDto.setProjectTargetName(clientOfferNums.get(roleStaffDto.getFkTableId()));
                    }
                }
            }
        }
    }

    @Override
    public List<StudentProjectRoleStaffVo> getProjectRole(Long fkStudentOfferId, List<Long> staffIds) {
        return studentProjectRoleStaffMapper.getProjectRole(fkStudentOfferId, staffIds);
    }

    @Override
    public Boolean verifyProjectRole(List<ProjectRoleStaffDto> projectRoleStaffDtos, Set<Long> tableIds) {
        //获取最近一个小时的数据
        List<StudentProjectRoleStaff> roleStaffs = studentProjectRoleStaffMapper.selectList(Wrappers.<StudentProjectRoleStaff>lambdaQuery()
                .eq(StudentProjectRoleStaff::getFkTableName,TableEnum.SALE_STUDENT_OFFER.key)
                .in(StudentProjectRoleStaff::getFkTableId,tableIds)
                .eq(StudentProjectRoleStaff::getIsActive,true)
                .gt(StudentProjectRoleStaff::getGmtCreate, LocalDateTime.now().minusHours(1)));
        if (GeneralTool.isNotEmpty(roleStaffs)) {
            projectRoleStaffDtos.sort(Comparator.comparing(ProjectRoleStaffDto::getFkRoleId));
            Map<Long, List<StudentProjectRoleStaff>> collect = roleStaffs.stream().collect(Collectors.groupingBy(StudentProjectRoleStaff::getFkTableId));
            //校验数据库的项目成员是否和新增的一样
            for (Map.Entry<Long, List<StudentProjectRoleStaff>> entry : collect.entrySet()) {
                List<StudentProjectRoleStaff> value = entry.getValue();
                if (value.size()== projectRoleStaffDtos.size()) {
                    value.sort(Comparator.comparing(StudentProjectRoleStaff::getFkStudentProjectRoleId));
                    String roles = value.stream().map(s -> String.valueOf(s.getFkStudentProjectRoleId())).collect(Collectors.joining(","));
                    String staffs = value.stream().map(s -> String.valueOf(s.getFkStaffId())).collect(Collectors.joining(","));
                    String roles2 = projectRoleStaffDtos.stream().map(s -> String.valueOf(s.getFkRoleId())).collect(Collectors.joining(","));
                    String staffs2 = projectRoleStaffDtos.stream().map(s -> String.valueOf(s.getFkStaffId())).collect(Collectors.joining(","));
                    if (roles.equals(roles2) && staffs.equals(staffs2)){
                        return false;
                    }
                }
            }
        }
        return true;
    }

    @Override
    public List<StudentProjectRoleStaffVo> getAllProjectRoleStaff(Long fkStudentOfferId) {
        return studentProjectRoleStaffMapper.getAllProjectRoleStaff(fkStudentOfferId);
    }

    @Override
    public Boolean batchAddStudentProjectRoleStaff(List<StudentProjectRoleStaff> studentProjectRoleStaffs) {
        return saveBatch(studentProjectRoleStaffs);
    }

    @Override
    public Set<Long> getRoleAndStaffByTableIdAndRoles(Long fkStudentOfferId, String typeKey, List<String> roleList) {
        if (GeneralTool.isEmpty(fkStudentOfferId)) {
            return null;
        }

        //获取角色
        List<StudentProjectRole> studentProjectRoles = studentProjectRoleMapper.getRoleByKeysLike(roleList);
        List<Long> roleIds = studentProjectRoles.stream().map(StudentProjectRole::getId).collect(Collectors.toList());

        List<StudentProjectRoleStaff> roleStaffs = projectRoleStaffMapper.selectList(Wrappers.<StudentProjectRoleStaff>lambdaQuery()
                .eq(StudentProjectRoleStaff::getFkTableName, typeKey)
                .eq(StudentProjectRoleStaff::getFkTableId, fkStudentOfferId)
                .in(StudentProjectRoleStaff::getFkStudentProjectRoleId,roleIds)
                .eq(StudentProjectRoleStaff::getIsActive, 1));

        return roleStaffs.stream().map(StudentProjectRoleStaff::getFkStaffId).collect(Collectors.toSet());
    }

    @Override
    public List<StudentProjectRoleStaffVo> getStudentProjectRoleStaffList(Long fkStudentOfferId) {
        List<StudentProjectRoleStaff> studentProjectRoleStaffs = studentProjectRoleStaffMapper.selectList(new LambdaQueryWrapper<StudentProjectRoleStaff>()
                .eq(StudentProjectRoleStaff::getFkTableId, fkStudentOfferId)
                .eq(StudentProjectRoleStaff::getFkTableName, TableEnum.SALE_STUDENT_OFFER.key)
                .eq(StudentProjectRoleStaff::getIsActive, true)
                .orderByAsc(StudentProjectRoleStaff::getGmtCreate));
        if (GeneralTool.isEmpty(studentProjectRoleStaffs)){
            return Collections.emptyList();
        }
        List<StudentProjectRoleStaffVo> studentProjectRoleStaffVos = BeanCopyUtils.copyListProperties(studentProjectRoleStaffs, StudentProjectRoleStaffVo::new);
        Set<Long> fkStaffIds = studentProjectRoleStaffVos.stream().map(StudentProjectRoleStaffVo::getFkStaffId).collect(Collectors.toSet());
        Map<Long, String> staffMap = permissionCenterClient.getStaffNameMap(fkStaffIds).getData();
        List<StaffVo> staffVoByIds = permissionCenterClient.getStaffByIds(fkStaffIds);
        //TODO 改过
       // Set<Long> roleIds = studentProjectRoleStaffVos.stream().map(StudentProjectRoleStaff::getFkStudentProjectRoleId).collect(Collectors.toSet());
        Set<Long> roleIds = studentProjectRoleStaffVos.stream().map(StudentProjectRoleStaffVo::getFkStudentProjectRoleId).collect(Collectors.toSet());
        roleIds.removeIf(Objects::isNull);
        if (GeneralTool.isEmpty(roleIds)) {
            roleIds.add(0L);
        }
        Map<Long, String> roleNamesMap = studentProjectRoleService.getRoleNameByIds(roleIds);
        for (StudentProjectRoleStaffVo studentProjectRoleStaffVo : studentProjectRoleStaffVos) {
            if (GeneralTool.isNotEmpty(staffMap) && GeneralTool.isNotEmpty(staffMap.get(studentProjectRoleStaffVo.getFkStaffId()))){
                studentProjectRoleStaffVo.setStaffName(staffMap.get(studentProjectRoleStaffVo.getFkStaffId()));
            }
            Long fkCompanyId = staffVoByIds.stream().filter(s -> s.getId().equals(studentProjectRoleStaffVo.getFkStaffId())).findFirst().get().getFkCompanyId();
            // TODO: 待优化
            String companyName = permissionCenterClient.getCompanyNameById(fkCompanyId).getData();
            studentProjectRoleStaffVo.setCompanyName(companyName);

            if (GeneralTool.isNotEmpty(roleNamesMap) && GeneralTool.isNotEmpty(roleNamesMap.get(studentProjectRoleStaffVo.getFkStudentProjectRoleId()))){
                studentProjectRoleStaffVo.setRoleName(roleNamesMap.get(studentProjectRoleStaffVo.getFkStudentProjectRoleId()));
            }
        }
        return studentProjectRoleStaffVos;
    }

    @Override
    public List<StudentProjectRoleStaff> getStaffByTableIdsAndLikeRoleKey(String tableName, Set<Long> tableIds, String roleKey, String likeType) {
        if (GeneralTool.isEmpty(tableIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<StudentProjectRole> wrapper = Wrappers.lambdaQuery();
        switch (likeType){
            case "like":
                wrapper.like(StudentProjectRole::getRoleKey, roleKey);
                break;
            case "likeLeft":
                wrapper.likeLeft(StudentProjectRole::getRoleKey, roleKey);
                break;
            case "likeRight":
                wrapper.likeRight(StudentProjectRole::getRoleKey, roleKey);
                break;
            default:
                break;
        }
        List<StudentProjectRole> studentProjectRoles = studentProjectRoleMapper.selectList(wrapper);
        if (GeneralTool.isEmpty(studentProjectRoles)) {
            return Collections.emptyList();
        }
        List<Long> roleIds = studentProjectRoles.stream().map(StudentProjectRole::getId).collect(Collectors.toList());
        List<StudentProjectRoleStaff> roleStaffs = projectRoleStaffMapper.selectList(Wrappers.<StudentProjectRoleStaff>lambdaQuery()
                .eq(StudentProjectRoleStaff::getFkTableName, tableName)
                .in(StudentProjectRoleStaff::getFkTableId, tableIds)
                .in(StudentProjectRoleStaff::getFkStudentProjectRoleId, roleIds)
                .eq(StudentProjectRoleStaff::getIsActive, 1));

        return roleStaffs;
    }
}
