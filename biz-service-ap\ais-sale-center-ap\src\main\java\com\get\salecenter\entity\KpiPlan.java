package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2024/4/16
 * @TIME: 12:20
 * @Description:KPI方案
 **/
@TableName("m_kpi_plan")
@ApiModel(value="KPI方案", description="")
@Data
public class KpiPlan extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "公司ID集合")
    private String fkCompanyIds;



    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "学生创建时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    private Date studentCreateTimeStart;

    @ApiModelProperty(value = "学生创建时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    private Date studentCreateTimeEnd;

    @ApiModelProperty(value = "入学时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    private Date intakeTimeStart;

    /**
     * 申请创建开始时间
     */
    @ApiModelProperty(value = "申请计划创建时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    private Date offerItemCreateTimeStart;

    /**
     * 申请创建结束时间
     */
    @ApiModelProperty(value = "申请计划创建结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    private Date offerItemCreateTimeEnd;

    @ApiModelProperty(value = "入学时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    private Date intakeTimeEnd;

    @ApiModelProperty(value = "步骤登记时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    private Date stepTimeStart;

    @ApiModelProperty(value = "步骤登记时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    private Date stepTimeEnd;

    @ApiModelProperty(value = "定时统计：0关闭/1开启")
    private Integer isEnableScheduledCount;

}
