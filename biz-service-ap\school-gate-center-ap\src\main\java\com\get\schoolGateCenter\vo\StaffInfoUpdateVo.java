package com.get.schoolGateCenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
public class StaffInfoUpdateVo extends BaseVoEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名", required = true)
    @NotBlank(message = "员工姓名不能为空", groups = {Add.class, Update.class})
    private String name;
    /**
     * 英文名
     */
    @ApiModelProperty("英文名")
    private String nameEn;
    /**
     * 性别：0女/1男
     */
    @ApiModelProperty("性别：0女/1男")
    private Integer gender;
    /**
     * 生日
     */
    @ApiModelProperty("生日")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;

    /**
     * 户口：农业户口/城镇户口
     */
    @ApiModelProperty(value = "户口：农业户口/城镇户口")
    private Integer hukou;

    /**
     * 证件类型枚举：0身份证/1护照/2通行证/3回乡证
     */
    @ApiModelProperty(value = "证件类型枚举：0身份证/1护照/2通行证/3回乡证")
    private Integer identityType;
    /**
     * 身份证号
     */
    @ApiModelProperty("身份证号")
    private String identityNum;
    /**
     * 身份证地址
     */
    @ApiModelProperty(value = "身份证地址")
    private String identityCardAddress;

    /**
     * 住址电话区号
     */
    @ApiModelProperty(value = "住址电话区号")
    private String homeTelAreaCode;
    /**
     * 住址电话
     */
    @ApiModelProperty("住址电话")
    private String homeTel;
    /**
     * 工作电话
     */
    @ApiModelProperty("工作电话")
    private String workTel;
    /**
     * 手机区号
     */
    @ApiModelProperty("手机区号")
    private String mobileAreaCode;
    /**
     * 移动电话
     */
    @ApiModelProperty("移动电话")
    private String mobile;
    /**
     * Email
     */
    @ApiModelProperty("Email")
    @NotBlank(message = "Email不能为空", groups = {Add.class, Update.class})
    private String email;
    /**
     * 邮编
     */
    @ApiModelProperty("邮编")
    private String zipCode;


    /**
     * 联系地址国家Id
     */
    @ApiModelProperty(value = "联系地址国家Id")
    private Long fkAreaCountryId;

    /**
     * 联系地址州省Id
     */
    @ApiModelProperty(value = "联系地址州省Id")
    private Long fkAreaStateId;

    /**
     * 联系地址城市Id
     */
    @ApiModelProperty(value = "联系地址城市Id")
    private Long fkAreaCityId;

    /**
     * 联系地址城市区域Id
     */
    @ApiModelProperty(value = "联系地址城市区域Id")
    private Long fkAreaCityDivisionId;

    /**
     * 地址
     */
    @ApiModelProperty("地址")
    private String address;
    /**
     * QQ号
     */
    @ApiModelProperty("QQ号")
    private String qq;
    /**
     * 微信号
     */
    @ApiModelProperty("微信号")
    private String wechat;
    /**
     * whatsapp号
     */
    @ApiModelProperty("whatsapp号")
    private String whatsapp;
    /**
     * 紧急联系方式
     */
    @ApiModelProperty("紧急联系方式")
    private String emergencyContact;


    /**
     * 紧急联系人关系：父母/妻子/儿女/亲戚/朋友
     */
    @ApiModelProperty(value = "紧急联系人关系：父母/妻子/儿女/亲戚/朋友")
    private String emergencyRelationship;

    /**
     * 紧急联系人电话
     */
    @ApiModelProperty(value = "紧急联系人电话")
    private String emergencyTel;

    /**
     * 头像媒体附件id
     */
    @ApiModelProperty(value = "头像媒体附件id")
    private Long fkMediaId;

    @ApiModelProperty(value = "ip白名单")
    private String ipWhiteList;

    @NotNull(message = "是否需要验证码登陆不能为null")
    @ApiModelProperty(value = "是否需要验证码登陆")
    private Boolean isVcodeRequired;
}
