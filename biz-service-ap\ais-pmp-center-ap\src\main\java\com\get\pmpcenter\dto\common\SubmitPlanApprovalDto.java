package com.get.pmpcenter.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/3/24
 * @Version 1.0
 * @apiNote:提交审核请求参数
 */
@Data
public class SubmitPlanApprovalDto {

    @ApiModelProperty(value = "审核人ID")
    @NotNull(message = "审核人ID不能为空")
    private Long staffId;

    @ApiModelProperty(value = "提交说明")
    private String submitNote;

    @ApiModelProperty(value = "方案ID")
    @NotNull(message = "方案ID不能为空")
    private Long planId;

    @ApiModelProperty(value = "附件文件集合")
    private List<MediaDto> mediaList;
}
