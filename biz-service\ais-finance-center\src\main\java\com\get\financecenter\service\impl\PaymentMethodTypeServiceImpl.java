package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.AccountingItemMapper;
import com.get.financecenter.dao.PaymentMethodTypeMapper;
import com.get.financecenter.dto.PaymentMethodTypeDto;
import com.get.financecenter.entity.PaymentMethodType;
import com.get.financecenter.service.PaymentMethodTypeService;
import com.get.financecenter.utils.GetAccountingCodeNameUtils;
import com.get.financecenter.vo.PaymentMethodTypeVo;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 付款方式类型管理
 */
@Service("financeCenterPaymentMethodTypeService")
public class PaymentMethodTypeServiceImpl extends ServiceImpl<PaymentMethodTypeMapper, PaymentMethodType> implements PaymentMethodTypeService {

    @Resource
    private PaymentMethodTypeMapper paymentMethodTypeMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private AccountingItemMapper accountingItemMapper;
    @Resource
    private GetAccountingCodeNameUtils getAccountingCodeNameUtils;


    @Override
    public List<PaymentMethodTypeVo> getPaymentMethodTypes(PaymentMethodTypeDto paymentMethodTypeDto, Page page) {
        if (GeneralTool.isEmpty(paymentMethodTypeDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isNotEmpty(paymentMethodTypeDto.getKeyWord())){
            paymentMethodTypeDto.setKeyWord(paymentMethodTypeDto.getKeyWord().replace(" ", "").trim());
        }

        LambdaQueryWrapper<PaymentMethodType> wrapper = new LambdaQueryWrapper();
        IPage<PaymentMethodType> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);

        List<PaymentMethodTypeVo> paymentMethodTypeVos = paymentMethodTypeMapper.getPaymentMethodTypes(pages, paymentMethodTypeDto);
        page.setAll((int) pages.getTotal());

        if (GeneralTool.isNotEmpty(paymentMethodTypeVos)){
            for (PaymentMethodTypeVo paymentMethodTypeVo : paymentMethodTypeVos) {
                //处理科目类型参数
                if (GeneralTool.isNotEmpty(paymentMethodTypeVo.getFkAccountingItemId())){
                    String accountingName = getAccountingCodeNameUtils.setAccountingCodeName(paymentMethodTypeVo.getFkAccountingItemId());
                    paymentMethodTypeVo.setAccountingItemName(accountingName);
                }

            }

        }
        return paymentMethodTypeVos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAddPaymentMethodType(List<PaymentMethodTypeDto> paymentMethodTypeDtos) {
        if (GeneralTool.isEmpty(paymentMethodTypeDtos)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (PaymentMethodTypeDto paymentMethodTypeDto : paymentMethodTypeDtos) {
            PaymentMethodType paymentMethodType = BeanCopyUtils.objClone(paymentMethodTypeDto, PaymentMethodType::new);
            checkParam(paymentMethodType);
            paymentMethodType.setTypeName(paymentMethodType.getTypeName().replace(" ", "").trim());
            if (paymentMethodTypeMapper.checkName(paymentMethodType.getTypeName()) > 0){
                throw new GetServiceException(LocaleMessageUtils.getMessage("duplicate_name") +paymentMethodType.getTypeName());
            }
            utilService.setCreateInfo(paymentMethodType);
            paymentMethodType.setViewOrder(paymentMethodTypeMapper.getMaxViewOrder());
            int insert = paymentMethodTypeMapper.insert(paymentMethodType);
            if (insert <= 0){
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
        }


    }

    @Override
    public void updatePaymentMethodType(PaymentMethodTypeDto paymentMethodTypeDto) {
        if (GeneralTool.isEmpty(paymentMethodTypeDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(paymentMethodTypeDto.getId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        PaymentMethodType searchPaymentMethodType = paymentMethodTypeMapper.selectById(paymentMethodTypeDto.getId());
        if (GeneralTool.isEmpty(searchPaymentMethodType)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_obj_null"));
        }
        PaymentMethodType paymentMethodType = BeanCopyUtils.objClone(paymentMethodTypeDto,PaymentMethodType::new);
        if (!searchPaymentMethodType.getTypeName().equals(paymentMethodType.getTypeName())){
            paymentMethodType.setTypeName(paymentMethodType.getTypeName().replace(" ", "").trim());
            if (paymentMethodTypeMapper.checkName(paymentMethodType.getTypeName()) > 0){
                throw new GetServiceException(LocaleMessageUtils.getMessage("duplicate_name" )+ "(" + paymentMethodType.getTypeName() + ")");
            }
        }
        utilService.setUpdateInfo(paymentMethodType);
        int update = paymentMethodTypeMapper.updateById(paymentMethodType);
        if (update <= 0){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }

    }




    @Override
    public void deletePaymentMethodType(Long id) {
        if (GeneralTool.isEmpty(id)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        PaymentMethodType paymentMethodType = paymentMethodTypeMapper.selectById(id);
        if (GeneralTool.isEmpty(paymentMethodType)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        paymentMethodTypeMapper.deleteById(id);
    }

    @Override
    public void sort(List<Long> ids) {
        if (GeneralTool.isEmpty(ids)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }

        if (ids.size() != 2){
            throw new GetServiceException(LocaleMessageUtils.getMessage("sort_param_error"));
        }

        List<PaymentMethodType> paymentMethodTypes = paymentMethodTypeMapper.selectBatchIds(ids);
        if (paymentMethodTypes.size() != ids.size()){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }

        Map<Long, Integer> orderMap = paymentMethodTypes.stream().collect(Collectors.toMap(PaymentMethodType::getId, PaymentMethodType::getViewOrder));
        List<PaymentMethodType> updateList = new ArrayList<>();

        for (int i = 0; i < ids.size(); i++) {
            Long currentId = ids.get(i);
            Long swapId = ids.get((i + 1) % ids.size());
            if (orderMap.containsKey(swapId)) {
                PaymentMethodType entity = paymentMethodTypeMapper.selectById(currentId);
                if (entity.getViewOrder() == orderMap.get(swapId)){
                    throw new GetServiceException(LocaleMessageUtils.getMessage("inconsistent_data"));
                }
                entity.setViewOrder(orderMap.get(swapId));
                utilService.updateUserInfoToEntity(entity);
                updateList.add(entity);
            }
        }

        if (!updateList.isEmpty()) {
            paymentMethodTypeMapper.updateBatchById(updateList);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(Integer start, Integer end) {
        if (GeneralTool.isEmpty(start) || GeneralTool.isEmpty( end)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("sort_param_error"));
        }
        LambdaQueryWrapper<PaymentMethodType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (end > start){
            lambdaQueryWrapper.between(PaymentMethodType::getViewOrder,start,end).orderByDesc(PaymentMethodType::getViewOrder);
        }else {
            lambdaQueryWrapper.between(PaymentMethodType::getViewOrder,end,start).orderByDesc(PaymentMethodType::getViewOrder);

        }
        List<PaymentMethodType> paymentMethodTypes = list(lambdaQueryWrapper);
        List<PaymentMethodType> updateList = new ArrayList<>();
        if (end > start){
            int finalEnd = end;
            List<PaymentMethodType> sortedList = Lists.newArrayList();
            PaymentMethodType paymentMethodTypeLast = paymentMethodTypes.get(paymentMethodTypes.size() - 1);
            sortedList.add(paymentMethodTypeLast);
            paymentMethodTypes.remove(paymentMethodTypes.size()-1);
            sortedList.addAll(paymentMethodTypes);
            for (PaymentMethodType paymentMethodType : sortedList) {
                paymentMethodType.setViewOrder(finalEnd);
                finalEnd--;
            }
            updateList.addAll(sortedList);
        }else {
            int finalStart = start;
            List<PaymentMethodType> sortedList = Lists.newArrayList();
            PaymentMethodType paymentMethodTypeFirst = paymentMethodTypes.get(0);
            paymentMethodTypes.remove(0);
            sortedList.addAll(paymentMethodTypes);
            sortedList.add(paymentMethodTypeFirst);
            for (PaymentMethodType paymentMethodType : sortedList) {
                paymentMethodType.setViewOrder(finalStart);
                finalStart--;
            }
            updateList.addAll(sortedList);
        }

        if (GeneralTool.isNotEmpty(updateList)){
            boolean batch = updateBatchById(updateList);
            if (!batch){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
    }


    private static void checkParam(PaymentMethodType paymentMethodType) {
        if (GeneralTool.isEmpty(paymentMethodType.getTypeName())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("type_name_null"));
        }
        if (GeneralTool.isEmpty(paymentMethodType.getFkAccountingItemId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("accounting_item_id_null"));
        }
        if (GeneralTool.isEmpty(paymentMethodType.getVouchType())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("vouch_type_null"));
        }
    }
}

