package com.get.votingcenter.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseEntity;
import com.get.votingcenter.entity.UserVotingAward;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.util.Date;

/**
 * Created by <PERSON>.
 * Time: 10:57
 * Date: 2021/10/20
 * Description:抽奖管理返回类
 */
@Data
public class UserAwardVo extends BaseEntity {
    /**
     * 注册中心的user id
     */
    @ApiModelProperty(value = "注册中心的user id")
    @Column(name = "fk_user_id")
    private Long fkUserId;
    /**
     * 投票主题Id
     */
    @ApiModelProperty(value = "投票主题Id")
    @Column(name = "fk_voting_id")
    private Long fkVotingId;
    /**
     * 操作批次key，主要用来区分每次随机操作的批次
     */
    @ApiModelProperty(value = "操作批次key，主要用来区分每次随机操作的批次")
    @Column(name = "opt_key")
    private String optKey;

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码")
    private String mobile;

    /**
     * 峰会用户名称
     */
    @ApiModelProperty(value = "峰会用户名称")
    private String conventionPersonName;


    /**
     * 注册中心的用户名称
     */
    @ApiModelProperty(value = "注册中心的用户名称")
    private String fkUserName;


}
