package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName: UserResume
 * @Author: Eric
 * @Date: 2023/7/12 10:52
 * @Version: 1.0
 */
@Data
public class UserResumeVo extends BaseVoEntity {

    @ApiModelProperty("人才简历Id")
    private Long fkResumeId;

    @ApiModelProperty("用户Id")
    private Long fkUserId;

    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    @ApiModelProperty("创建用户(登录账号)")
    private String gmtCreateUser;

    @ApiModelProperty("修改时间")
    private Date gmtModified;

    @ApiModelProperty("修改用户(登录账号)")
    private String gmtModifiedUser;

}
