package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.ContractType;
import org.apache.ibatis.annotations.Mapper;

/**
 * @author: Sea
 * @create: 2020/7/29 10:27
 * @verison: 1.0
 * @description: 合同类型管理mapper
 */
@Mapper
public interface ContractTypeMapper extends BaseMapper<ContractType> {
    /**
     * 添加
     *
     * @param record
     * @return
     */
    @Override
    int insert(ContractType record);

    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(ContractType record);

    /**
     * @return java.lang.Integer
     * @Description :获取最大排序值
     * @Param []
     * <AUTHOR>
     */
    Integer getMaxViewOrder();
}