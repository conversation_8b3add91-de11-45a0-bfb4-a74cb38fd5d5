package com.get.partnercenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author:<PERSON>
 * @Date: 2025/2/12  10:46
 * @Version 1.0
 * 小程序用户代理Dto
 */
@Data
public class AgentPageDto {

    @ApiModelProperty(value = "代理状态1启用0禁用")
    private Integer status;

    @ApiModelProperty(value = "大区ID")
    private Long regionId;

    @ApiModelProperty(value = "代理名称/编号")
    private String agentKeyWord;

}
