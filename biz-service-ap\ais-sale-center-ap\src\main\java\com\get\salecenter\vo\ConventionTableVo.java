package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.ConventionTable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/27 11:35
 * @verison: 1.0
 * @description:
 */
@Data
@ApiModel(value = "峰会桌台返回类")
public class ConventionTableVo extends BaseEntity {

    /**
     * 培训桌-峰会报名 中间表信息
     */
    @ApiModelProperty(value = "培训桌-峰会报名")
    private ConventionTableRegistrationVo conventionTableRegistrationDto;

    /**
     * 晚宴桌-参展人员 中间表信息
     */
    @ApiModelProperty(value = "晚宴桌-参展人员")
    private List<ConventionTablePersonVo> conventionTablePersonDtoList;

    /**
     * 剩余座位数
     */
    @ApiModelProperty(value = "剩余座位数")
    private Integer remainingSeatCount;

    //==========实体类ConventionTable==============
    private static final long serialVersionUID = 1L;
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    @Column(name = "fk_convention_id")
    private Long fkConventionId;
    /**
     * 桌子类型key
     */
    @ApiModelProperty(value = "桌子类型key")
    @Column(name = "fk_table_type_key")
    private String fkTableTypeKey;
    /**
     * 前序号
     */
    @ApiModelProperty(value = "前序号")
    @Column(name = "pre_num")
    private String preNum;
    /**
     * 桌子编号
     */
    @ApiModelProperty(value = "桌子编号")
    @Column(name = "table_num")
    private String tableNum;
    /**
     * 座位数
     */
    @ApiModelProperty(value = "座位数")
    @Column(name = "seat_count")
    private Integer seatCount;
    /**
     * 是否主席台：0否/1是，默认否
     */
    @ApiModelProperty(value = "是否主席台：0否/1是，默认否")
    @Column(name = "is_vip")
    private Integer isVip;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}
