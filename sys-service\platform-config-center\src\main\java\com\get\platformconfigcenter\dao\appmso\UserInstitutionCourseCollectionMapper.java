package com.get.platformconfigcenter.dao.appmso;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.UserInstitutionCourseCollection;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
@DS("appmsodb")
public interface UserInstitutionCourseCollectionMapper extends BaseMapper<UserInstitutionCourseCollection> {
//    int insert(UserInstitutionCourseCollection record);
//
//    int insertSelective(UserInstitutionCourseCollection record);
//
//    /**
//     * 根据课程id验证是否存在
//     *
//     * @return
//     */
//    boolean isExistByCourseId(@Param("courseId") Long courseId);
}