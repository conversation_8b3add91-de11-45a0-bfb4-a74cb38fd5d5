package com.get.insurancecenter.vo.card;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

/**
 * @Author:Oliver
 * @Date: 2025/7/25
 * @Version 1.0
 * @apiNote: 信用卡列表数据
 */
@Data
public class CreateCardPageVo {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "信用卡类型：Visa/Master/UnionPay")
    private String cardTypeKey;

    @ApiModelProperty(value = "信用卡号-加密")
    private String cardNum;

    @ApiModelProperty(value = "持卡人姓名")
    private String holderName;

    @ApiModelProperty(value = "持卡人电话")
    private String holderTel;

    @ApiModelProperty(value = "持卡人电邮")
    private String holderEmail;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "有效期（年月）")
    private Date expirationDate;

    @ApiModelProperty(value = "发卡银行Id")
    private Long fkBankId;

    @ApiModelProperty(value = "额度币种")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "额度金额")
    private BigDecimal quotaAmount;

    @ApiModelProperty(value = "当前金额/额度")
    private BigDecimal currentAmount;

    @ApiModelProperty(value = "账单日")
    private Integer statementDate;

    @ApiModelProperty(value = "还款日")
    private Integer paymentDate;

    @ApiModelProperty(value = "是否激活：0否/1是")
    private Integer isActive;

    @ApiModelProperty(value = "排序-越大扣款顺序越靠前")
    private Integer viewOrder;

    @ApiModelProperty(value = "信用卡号-明文")
    private String cardNumPlaintext;

    @ApiModelProperty(value = "信用卡号-明文脱敏后的")
    private String desensitizationCardNumPlaintext;

    @ApiModelProperty(value = "发卡银行名称")
    private String bankName;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "创建人")
    private String gmtCreateUser;

    @ApiModelProperty(value = "通知人")
    private String notifyPerson;

    @ApiModelProperty(value = "有效期（MM/yyyy）")
    private String effectiveDate;

    @ApiModelProperty(value = "当前登录人")
    private String currentLoginUser;

    public String getEffectiveDate() {
        if (Objects.isNull(expirationDate)) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("MM/yyyy");
        return sdf.format(expirationDate);
    }

}
