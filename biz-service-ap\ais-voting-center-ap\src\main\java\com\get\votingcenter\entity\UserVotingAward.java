package com.get.votingcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_user_voting_award")
public class UserVotingAward extends BaseEntity{
    private static final long serialVersionUID = 1L;
    /**
     * 注册中心的user id
     */
    @ApiModelProperty(value = "注册中心的user id")
    @Column(name = "fk_user_id")
    private Long fkUserId;
    /**
     * 投票主题Id
     */
    @ApiModelProperty(value = "投票主题Id")
    @Column(name = "fk_voting_id")
    private Long fkVotingId;
    /**
     * 操作批次key，主要用来区分每次随机操作的批次
     */
    @ApiModelProperty(value = "操作批次key，主要用来区分每次随机操作的批次")
    @Column(name = "opt_key")
    private String optKey;
}