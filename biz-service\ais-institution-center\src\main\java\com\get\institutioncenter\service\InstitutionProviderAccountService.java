package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.institutioncenter.vo.InstitutionProviderAccountVo;
import com.get.institutioncenter.dto.InstitutionProviderAccountDto;

import java.util.List;

public interface InstitutionProviderAccountService {


    /**
     * 获取学校提供商账户列表
     * @param providerAccountVo
     * @param page
     * @return
     */
    ResponseBo<InstitutionProviderAccountVo> getProviderAccountList(InstitutionProviderAccountDto providerAccountVo, Page page);

    /**
     * 获取详情
     * @param id
     * @return
     */
    InstitutionProviderAccountVo findInfoById(Long id);


    /**
     * 快捷设置首选合同
     * @param providerId
     * @param accountId
     * @return
     */
    SaveResponseBo quickFirstContractAccount(Long providerId,Long accountId);


    /**
     * 快速激活或屏蔽合同账户
     * @param providerId
     * @param accountId
     * @param status
     * @return
     */
    SaveResponseBo quickActivationOrMask(Long providerId,Long accountId,Boolean status);


    /**
     * 更新
     * @param providerAccountVo
     * @return
     */
    InstitutionProviderAccountVo update(InstitutionProviderAccountDto providerAccountVo);

    /**
     * 新增
     * @param providerAccountVo
     * @return
     */
    SaveResponseBo add(InstitutionProviderAccountDto providerAccountVo);


    /**
     * 删除
     * @param id
     * @return
     */
    SaveResponseBo delete(Long id);

    /**
     * 代理合同账户列表账户重复提示
     * @param id
     * @param providerId
     * @param bankAccount
     * @param bankAccountNum
     * @return
     */
    ResponseBo<String> getContractAccountExist(Long id, Long providerId, String bankAccount, String bankAccountNum);


    /**
     * 提供商银行账户下拉框
     * @param fkTargetId
     * @return
     */
    List<BaseSelectEntity> getInstitutionProviderAccountList(Long fkTargetId);

    /**
     * 提供商银行账户名称
     * @param fkBankAccountId
     * @return
     */
    String getInstitutionProviderAccountById(Long fkBankAccountId);
}
