package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.institutioncenter.dao.InstitutionCourseAppInfoMapper;
import com.get.institutioncenter.dao.InstitutionScholarshipMapper;
import com.get.institutioncenter.dto.*;
import com.get.institutioncenter.entity.InstitutionCourseAppInfo;
import com.get.institutioncenter.service.*;
import com.get.institutioncenter.utils.WeUtils;
import com.get.institutioncenter.vo.AppCourseMappingInfoVo;
import com.get.institutioncenter.vo.CourseAppInfoPriorityVo;
import com.get.institutioncenter.vo.CourseOtherInfoVo;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class IInstitutionCourseAppInfoServiceImpl extends BaseServiceImpl<InstitutionCourseAppInfoMapper, InstitutionCourseAppInfo> implements IInstitutionCourseAppInfoService {

    @Resource
    private UtilService<Object> utilService;

    @Resource
    private InstitutionCourseAppInfoMapper institutionCourseAppInfoMapper;

    @Resource
    private InstitutionScholarshipMapper institutionScholarshipMapper;

    @Resource
    private IInstitutionViewOrderService iInstitutionViewOrderService;
    
    @Resource
    private IInstitutionService institutionService;
    @Resource
    private IInstitutionFacultyService iInstitutionFacultyService;

    @Resource
    private ICourseTypeGroupService courseTypeGroupService;
    @Resource
    private ICourseTypeService courseTypeService;
    @Resource
    private IInstitutionMajorService iInstitutionMajorService;
    @Resource
    private IInstitutionCourseService iInstitutionCourseService;
    @Resource
    private IAreaCountryService areaCountryService;
    @Resource
    private IFileCenterClient iFileCenterClient;
    @Resource
    private MediaAndAttachedServiceImpl mediaAndAttachedService;

    /**
     * Author Cream
     * Description : //批量新增中间表信息
     * Date 2022/11/30 12:48
     * Params:
     * Return
     */
    @Override
    public void batchAdd(List<InstitutionCourseAppInfoDataProcessDto.CourseAppInfoResultMappingVo> appInfos, Long fkTableId, String fkTableName, String effectiveDate) {
        if (GeneralTool.isEmpty(appInfos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("at_least_select_a_country"));
        }
        //校验
        verifyTypeKey(appInfos,fkTableName,fkTableId,effectiveDate);
        List<InstitutionViewOrderDto> list = new ArrayList<>();
        String loginId = SecureUtil.getLoginId();
        Date date = new Date();
        List<InstitutionCourseAppInfo> saveList = new ArrayList<>();
        for (InstitutionCourseAppInfoDataProcessDto.CourseAppInfoResultMappingVo appInfo : appInfos) {
            List<CourseAppInfoResultDto> courseAppInfoResultDtos = appInfo.getCourseAppInfoResultVos();
            String typeKey = appInfo.getTypeKey();
            for (CourseAppInfoResultDto appInfoResultVo : courseAppInfoResultDtos) {
                InstitutionCourseAppInfo courseAppInfo = new InstitutionCourseAppInfo();
                courseAppInfo.setFkTableName(fkTableName);
                courseAppInfo.setFkTableId(fkTableId);
                courseAppInfo.setFkTableNameType(typeKey);
                courseAppInfo.setFkTableIdType(appInfoResultVo.getTargetId());
                courseAppInfo.setGmtCreate(date);
                courseAppInfo.setGroupKey(effectiveDate);
                courseAppInfo.setGmtCreateUser(loginId);
                if (TableEnum.INSTITUTION.key.equals(appInfoResultVo.getTypeKey())) {
                    InstitutionViewOrderDto institutionViewOrderDto = new InstitutionViewOrderDto();
                    institutionViewOrderDto.setFkInstitutionId(appInfoResultVo.getTargetId());
                    institutionViewOrderDto.setType(0);
                    list.add(institutionViewOrderDto);
                }
                saveList.add(courseAppInfo);
            }
        }
        if (GeneralTool.isNotEmpty(saveList)) {
            saveBatch(saveList);
        }
        if (GeneralTool.isNotEmpty(list)) {
            iInstitutionViewOrderService.adds(list);
        }
    }

    /**
     * 校验新增的typekey合法性
     * @param appInfos
     * @param fkTableName
     */
    private void verifyTypeKey(List<InstitutionCourseAppInfoDataProcessDto.CourseAppInfoResultMappingVo> appInfos, String fkTableName, Long fkTableId, String effectiveDate){
        if (StringUtils.isBlank(effectiveDate)) {
            return;
        }
        String[] priorityTypeKey = getPriorityTypeKey(appInfos);
        if (priorityTypeKey!=null) {
            String key = priorityTypeKey[0];
            String val = priorityTypeKey[1];
            Integer count = institutionCourseAppInfoMapper.selectByKeyAndVal(fkTableName,key, val,fkTableId,effectiveDate);
            if (count>0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("same_target_type_information_already_exists"));
            }
        }
    }
    /**
     * 获取排序后的优先级key和value
     * @param appInfos
     * @return
     */
    public static String [] getPriorityTypeKey(List<InstitutionCourseAppInfoDataProcessDto.CourseAppInfoResultMappingVo> appInfos){
        List<String> keys = new ArrayList<>();
        List<Long> val = new ArrayList<>();
        for (InstitutionCourseAppInfoDataProcessDto.CourseAppInfoResultMappingVo appInfo : appInfos) {
            String typeKey = appInfo.getTypeKey();
            List<CourseAppInfoResultDto> appInfoResultVos = appInfo.getCourseAppInfoResultVos();
            if (TableEnum.INSTITUTION_COURSE.key.equals(typeKey) && GeneralTool.isNotEmpty(appInfoResultVos)) {
                val.addAll(appInfoResultVos.stream().map(CourseAppInfoResultDto::getTargetId).distinct().collect(Collectors.toList()));
                keys.add(TableEnum.INSTITUTION_COURSE.key);
            }
            if (TableEnum.INSTITUTION_MAJOR_LEVEL.key.equals(typeKey) && GeneralTool.isNotEmpty(appInfoResultVos)) {
                val.addAll(appInfoResultVos.stream().map(CourseAppInfoResultDto::getTargetId).distinct().collect(Collectors.toList()));
                keys.add(TableEnum.INSTITUTION_MAJOR_LEVEL.key);
            }
            if (TableEnum.INSTITUTION_COURSE_TYPE.key.equals(typeKey) && GeneralTool.isNotEmpty(appInfoResultVos)) {
                val.addAll(appInfoResultVos.stream().map(CourseAppInfoResultDto::getTargetId).distinct().collect(Collectors.toList()));
                keys.add(TableEnum.INSTITUTION_COURSE_TYPE.key);
            }
            if (TableEnum.INSTITUTION_COURSE_TYPE_GROUP.key.equals(typeKey) && GeneralTool.isNotEmpty(appInfoResultVos)) {
                val.addAll(appInfoResultVos.stream().map(CourseAppInfoResultDto::getTargetId).distinct().collect(Collectors.toList()));
                keys.add(TableEnum.INSTITUTION_COURSE_TYPE_GROUP.key);
            }
            if (TableEnum.INSTITUTION_FACULTY.key.equals(typeKey) && GeneralTool.isNotEmpty(appInfoResultVos)) {
                val.addAll(appInfoResultVos.stream().map(CourseAppInfoResultDto::getTargetId).distinct().collect(Collectors.toList()));
                keys.add(TableEnum.INSTITUTION_FACULTY.key);
            }
            if (TableEnum.INSTITUTION.key.equals(typeKey) && GeneralTool.isNotEmpty(appInfoResultVos)) {
                val.add(appInfoResultVos.get(0).getTargetId());
                keys.add(TableEnum.INSTITUTION.key);
            }
            if (TableEnum.INSTITUTION_COUNTRY.key.equals(typeKey) && GeneralTool.isNotEmpty(appInfoResultVos)) {
                val.add(appInfoResultVos.get(0).getTargetId());
                keys.add(TableEnum.INSTITUTION_COUNTRY.key);
            }
        }
        //对结果进行排序拼接返回数组 m_institution,u_area_country  /  155,7
        if (GeneralTool.isNotEmpty(keys)) {
            String [] rs = new String[2];
            rs[0] = keys.stream().sorted().collect(Collectors.joining(","));
            rs[1] = val.stream().sorted().map(e->String.valueOf(e.longValue())).collect(Collectors.joining(","));
            return rs;
        }
        return null;
    }
    /**
     * Author Cream
     * Description : //数据（例奖学金）数据处理
     * Date 2022/11/23 15:14
     * Params:
     * Return
     */
    @Override
    public void packageInfo(List<InstitutionCourseAppInfoDataProcessDto> infoDataProcessVo, Boolean flag, String fkTableName){
        if (GeneralTool.isEmpty(infoDataProcessVo)) {
            return;
        }
        Set<Long> ids = infoDataProcessVo.stream().map(InstitutionCourseAppInfoDataProcessDto::getId).collect(Collectors.toSet());
        List<InstitutionCourseAppInfo> courseAppInfos = institutionCourseAppInfoMapper.selectList(Wrappers.<InstitutionCourseAppInfo>lambdaQuery()
        .eq(InstitutionCourseAppInfo::getFkTableName,fkTableName).in(InstitutionCourseAppInfo::getFkTableId,ids));
        /**
         * 获取各个类型的参数
         */
        Map<Long, List<InstitutionCourseAppInfo>> cAppMap = courseAppInfos.stream().collect(Collectors.groupingBy(InstitutionCourseAppInfo::getFkTableId));
        Set<Long> institutionIds = courseAppInfos.stream().filter(f->TableEnum.INSTITUTION.key.equals(f.getFkTableNameType())).map(InstitutionCourseAppInfo::getFkTableIdType).collect(Collectors.toSet());
        Set<Long> countryIds = courseAppInfos.stream().filter(f->TableEnum.INSTITUTION_COUNTRY.key.equals(f.getFkTableNameType())).map(InstitutionCourseAppInfo::getFkTableIdType).collect(Collectors.toSet());
        Set<Long> facultyIds = courseAppInfos.stream().filter(f->TableEnum.INSTITUTION_FACULTY.key.equals(f.getFkTableNameType())).map(InstitutionCourseAppInfo::getFkTableIdType).collect(Collectors.toSet());
        Set<Long> courseTypeGroupIds = courseAppInfos.stream().filter(f->TableEnum.INSTITUTION_COURSE_TYPE_GROUP.key.equals(f.getFkTableNameType())).map(InstitutionCourseAppInfo::getFkTableIdType).collect(Collectors.toSet());
        Set<Long> courseTypeIds = courseAppInfos.stream().filter(f->TableEnum.INSTITUTION_COURSE_TYPE.key.equals(f.getFkTableNameType())).map(InstitutionCourseAppInfo::getFkTableIdType).collect(Collectors.toSet());
        Set<Long> majorIds = courseAppInfos.stream().filter(f->TableEnum.INSTITUTION_MAJOR_LEVEL.key.equals(f.getFkTableNameType())).map(InstitutionCourseAppInfo::getFkTableIdType).collect(Collectors.toSet());
        Set<Long> courseIds = courseAppInfos.stream().filter(f->TableEnum.INSTITUTION_COURSE.key.equals(f.getFkTableNameType())).map(InstitutionCourseAppInfo::getFkTableIdType).collect(Collectors.toSet());
        Map<Long, String> institutionNamesByIds = institutionService.getInstitutionNamesByIds(institutionIds);
        Map<Long, String> countryNamesByIds = areaCountryService.getCountryFullNamesByIds(countryIds);
        Map<Long, String> facultyNameByIds = iInstitutionFacultyService.getInstitutionFacultyNameByIds(facultyIds);
        Map<Long, String> courseGroupByIds = courseTypeGroupService.getCourseGroupByIds(courseTypeGroupIds);
        Map<Long, String> typeNamesByIds = courseTypeService.getCourseTypeNamesByIds(courseTypeIds);
        Map<Long, String> majorNamesByIds = iInstitutionMajorService.getInstitutionMajorNamesByIds(majorIds);
        Map<Long, String> courseNamesByIds = iInstitutionCourseService.getInstitutionCourseNamesByIds(courseIds);
        Map<Long, String> url = new HashMap<>(8);
        if (flag) {
            //获取文件guid
            List<MediaAndAttachedVo> guIdByIds = mediaAndAttachedService.getGuIdByIds(institutionIds, "m_institution", "institution_cover");
            if (GeneralTool.isNotEmpty(guIdByIds)) {
                //获取文件具体url
                List<String> collect = guIdByIds.stream().map(MediaAndAttachedVo::getFkFileGuid).collect(Collectors.toList());
                Result<Map<String, String>> filePathByGuids = iFileCenterClient.getFilePathByGuids(collect);
                if (filePathByGuids.isSuccess() && GeneralTool.isNotEmpty(filePathByGuids.getData())) {
                    guIdByIds.forEach(d -> url.put(d.getFkTableId(), filePathByGuids.getData().get(d.getFkFileGuid())));
                }
            }
        }
        /**
         * 属性封装
         *  weight 用来排序
         */
        StringBuffer stringBuffer = new StringBuffer();
        for (InstitutionCourseAppInfoDataProcessDto processVo : infoDataProcessVo) {
            List<InstitutionCourseAppInfo> infos = cAppMap.get(processVo.getId());
            List<CourseAppInfoResultDto> courseAppInfoResultDtos = new ArrayList<>();
            for (InstitutionCourseAppInfo appInfo : infos) {
                CourseAppInfoResultDto infoResultVo = new CourseAppInfoResultDto();
                String typeKey = appInfo.getFkTableNameType();
                Long fkTableIdType = appInfo.getFkTableIdType();
                infoResultVo.setTypeKey(typeKey);
                infoResultVo.setTargetId(fkTableIdType);
                String targetName = null;
                int weight = 0;
                if (TableEnum.INSTITUTION_COUNTRY.key.equals(typeKey)) {
                    targetName = countryNamesByIds.get(fkTableIdType);
                    weight = 1;
                } else if (TableEnum.INSTITUTION.key.equals(typeKey)) {
                    targetName = institutionNamesByIds.get(fkTableIdType);
                    if (flag) {
                        processVo.setCoversUrl(url.get(fkTableIdType));
                    }
                    processVo.setFkInstitutionName(institutionNamesByIds.get(fkTableIdType));
                    weight = 2;
                } else if (TableEnum.INSTITUTION_FACULTY.key.equals(typeKey)) {
                    targetName = facultyNameByIds.get(fkTableIdType);
                    weight = 3;
                } else if (TableEnum.INSTITUTION_COURSE_TYPE_GROUP.key.equals(typeKey)) {
                    targetName = courseGroupByIds.get(fkTableIdType);
                    weight = 4;
                } else if (TableEnum.INSTITUTION_COURSE_TYPE.key.equals(typeKey)) {
                    targetName = typeNamesByIds.get(fkTableIdType);
                    weight = 5;
                } else if (TableEnum.INSTITUTION_MAJOR_LEVEL.key.equals(typeKey)) {
                    targetName = majorNamesByIds.get(fkTableIdType);
                    weight = 6;
                } else if (TableEnum.INSTITUTION_COURSE.key.equals(typeKey)) {
                    targetName = courseNamesByIds.get(fkTableIdType);
                    weight = 7;
                }
                infoResultVo.setTypeKeyName(TableEnum.getInitValue(typeKey,TableEnum.S_P));
                infoResultVo.setTargetName(targetName);
                infoResultVo.setWeight(weight);
                courseAppInfoResultDtos.add(infoResultVo);
            }
            Map<String, List<CourseAppInfoResultDto>> collect = courseAppInfoResultDtos.stream().collect(Collectors.groupingBy(CourseAppInfoResultDto::getTypeKey));
            List<InstitutionCourseAppInfoDataProcessDto.CourseAppInfoResultMappingVo> courseAppInfoResultMappingVos = new ArrayList<>();
            for (Map.Entry<String, List<CourseAppInfoResultDto>> entry : collect.entrySet()) {
                InstitutionCourseAppInfoDataProcessDto.CourseAppInfoResultMappingVo courseAppInfoResultMappingVo = new InstitutionCourseAppInfoDataProcessDto.CourseAppInfoResultMappingVo();
                courseAppInfoResultMappingVo.setTypeKey(entry.getKey());
                List<CourseAppInfoResultDto> value = entry.getValue();
                if (GeneralTool.isNotEmpty(value)) {
                    courseAppInfoResultMappingVo.setWeight(value.get(0).getWeight());
                }
                courseAppInfoResultMappingVo.setCourseAppInfoResultVos(value);
                courseAppInfoResultMappingVos.add(courseAppInfoResultMappingVo);
            }
            courseAppInfoResultMappingVos.sort(Comparator.comparingInt(InstitutionCourseAppInfoDataProcessDto.CourseAppInfoResultMappingVo::getWeight));
            processVo.setCourseAppInfoResultMappingVos(courseAppInfoResultMappingVos);
            if (StringUtils.isNotBlank(processVo.getPublicLevel())) {
                Arrays.stream(processVo.getPublicLevel().split(",")).forEach(d -> stringBuffer.append(ProjectExtraEnum.getValueByKey(Integer.valueOf(d), ProjectExtraEnum.PUBLIC_OBJECTS)).append(","));
                String substring = stringBuffer.toString().substring(0, stringBuffer.length() - 1);
                processVo.setPublicLevelName(substring);
                stringBuffer.delete(0, stringBuffer.length());
            }
        }
    }

    @Override
    public void batchUpdate(List<InstitutionCourseAppInfoDataProcessDto.CourseAppInfoResultMappingVo> appInfos, Long fkTableId, String fkTableName, String effectiveDate) {
        if (GeneralTool.isEmpty(appInfos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("at_least_select_a_country"));
        }
        //校验
        verifyTypeKey(appInfos,fkTableName,fkTableId,effectiveDate);
        institutionCourseAppInfoMapper.delete(Wrappers.<InstitutionCourseAppInfo>lambdaQuery()
                .eq(InstitutionCourseAppInfo::getFkTableName, fkTableName)
                .eq(InstitutionCourseAppInfo::getFkTableId, fkTableId));
        batchAdd(appInfos,fkTableId,fkTableName,effectiveDate);
    }


    private List<InstitutionCourseAppInfoDataProcessDto>  filter(List<WeScholarshipAppDto> weScholarshipAppDtos, List<CourseAppInfoPriorityVo> priorityMatchingQuery, String fkTableName){
        Map<Long, List<CourseAppInfoPriorityVo>> collect = priorityMatchingQuery.stream().collect(Collectors.groupingBy(CourseAppInfoPriorityVo::getId));
        int weight;
        boolean flag;
        Map<Long,Integer> result = new HashMap<>(8);
        List<InstitutionCourseAppInfoDataProcessDto> processVoList = new ArrayList<>(result.size());
        for (WeScholarshipAppDto weScholarshipAppDto : weScholarshipAppDtos) {
            for (Map.Entry<Long, List<CourseAppInfoPriorityVo>> entry : collect.entrySet()) {
                List<CourseAppInfoPriorityVo> value = entry.getValue();
                weight = 0;
                flag = true;
                for (CourseAppInfoPriorityVo priorityDto : value) {
                    if (TableEnum.INSTITUTION_COURSE.key.equals(priorityDto.getFkTableType())) {
                        if (weScholarshipAppDto.getCourseId().equals(priorityDto.getFkTypeId())) {
                            weight = 520;
                            break;
                        }else {
                            weight = -520;
                        }
                    }
                    if (TableEnum.INSTITUTION_COUNTRY.key.equals(priorityDto.getFkTableType())) {
                        if (!weScholarshipAppDto.getCountryId().equals(priorityDto.getFkTypeId())) {
                            flag = false;
                            break;
                        }
                    }
                    if (TableEnum.INSTITUTION.key.equals(priorityDto.getFkTableType())) {
                        if (!weScholarshipAppDto.getInstitutionId().equals(priorityDto.getFkTypeId())) {
                            flag = false;
                            break;
                        }
                    }
                    if (TableEnum.INSTITUTION_FACULTY.key.equals(priorityDto.getFkTableType())) {
                        if (priorityDto.getFkTypeId().equals(weScholarshipAppDto.getFacultyId())) {
                            weight+=10;
                        }
                    }
                    if (TableEnum.INSTITUTION_COURSE_TYPE_GROUP.key.equals(priorityDto.getFkTableType())) {
                        if (weScholarshipAppDto.getCourseGroupId().equals(priorityDto.getFkTypeId())) {
                            weight+=20;
                        }
                    }
                    if (TableEnum.INSTITUTION_COURSE_TYPE.key.equals(priorityDto.getFkTableType())) {
                        if (weScholarshipAppDto.getCourseTypeId().equals(priorityDto.getFkTypeId())) {
                            weight+=40;
                        }
                    }
                    if (TableEnum.INSTITUTION_MAJOR_LEVEL.key.equals(priorityDto.getFkTableType())) {
                        if (weScholarshipAppDto.getCourseLevelId().equals(priorityDto.getFkTypeId())) {
                            weight+=70;
                        }else {
                            flag = false;
                        }
                    }
                }
                if (!result.containsKey(entry.getKey()) && flag) {
                    InstitutionCourseAppInfoDataProcessDto processVo = new InstitutionCourseAppInfoDataProcessDto();
                    processVo.setId(entry.getKey());
                    processVo.setWeight(weight);
                    processVo.setGmtPriorityTime(value.get(0).getGmtPriorityTime());
                    if (GeneralTool.isNotEmpty(value.get(0).getEffectiveDate())) {
                        processVo.setEffectiveDate(value.get(0).getEffectiveDate());
                    }
                    processVoList.add(processVo);
                }
                result.put(entry.getKey(),weight);
            }
        }
        return processVoList;

    }

    /**
     * 优先处理
     * @param weScholarshipAppDto
     * @param fkTableName
     * @return
     */
    @Override
    public InstitutionCourseAppInfoDataProcessDto priorityProcess(WeScholarshipAppDto weScholarshipAppDto, String fkTableName) {
        Map<Integer,String> levelTypeKey = WeUtils.getPriorityLevelTypeKey(weScholarshipAppDto);
        List<WeScholarshipAppDto> weScholarshipAppDto11 = institutionCourseAppInfoMapper.getOtherInfo(weScholarshipAppDto.getCourseId());
        weScholarshipAppDto.setCountryId(weScholarshipAppDto.getCountryId());
        List<CourseAppInfoPriorityVo> priorityMatchingQuery = institutionCourseAppInfoMapper.priorityMatchingQuery(weScholarshipAppDto, levelTypeKey,fkTableName);
        if (priorityMatchingQuery.isEmpty()) {
            return null;
        }
        // fk代指匹配上的目标类型， fc代指该奖学金拥有的目标类型
        CourseAppInfoPriorityVo priorityDto = null;
        for (CourseAppInfoPriorityVo dto : priorityMatchingQuery) {
            if (Arrays.stream(dto.getFk().split(",")).anyMatch(d->d.equals(TableEnum.INSTITUTION_COURSE.key))) {
                priorityDto = dto;
                break;
            }
            if (dto.getFk().equals(dto.getFc())) {
                priorityDto = dto;
                break;
            }
            dto.setPriority(dto.getFc().split(",").length);
        }
        if (null == priorityDto) {
            priorityMatchingQuery.sort(Comparator.comparing(CourseAppInfoPriorityVo::getPriority));
            priorityDto = priorityMatchingQuery.get(0);
        }
        List<InstitutionCourseAppInfoDataProcessDto> processVoList = new ArrayList<>(1);
        InstitutionCourseAppInfoDataProcessDto processVo = new InstitutionCourseAppInfoDataProcessDto();
        processVo.setId(priorityDto.getId());
        if (Objects.isNull(processVo.getId())) {
            return null;
        }
        processVoList.add(processVo);
        packageInfo(processVoList,false,fkTableName);
        return processVo;
    }

    @Override
    public List<InstitutionCourseAppInfoDataProcessDto> testPriority(WeScholarshipAppDto weScholarshipAppDto, String fkTableName) {
        List<WeScholarshipAppDto> weScholarshipAppDto11 = institutionCourseAppInfoMapper.getOtherInfo(weScholarshipAppDto.getCourseId());
        weScholarshipAppDto.setCountryId(weScholarshipAppDto.getCountryId());
        List<CourseAppInfoPriorityVo> priorityMatchingQuery = institutionCourseAppInfoMapper.priorityMatchingQueryTwo(weScholarshipAppDto,fkTableName);
        if (priorityMatchingQuery.isEmpty()) {
            return null;
        }
        List<InstitutionCourseAppInfoDataProcessDto> processVoList = filter(weScholarshipAppDto11, priorityMatchingQuery, fkTableName);
        packageInfo(processVoList,false,fkTableName);
        List<InstitutionCourseAppInfoDataProcessDto> special = processVoList.stream().filter(f -> Objects.isNull(f.getEffectiveDate())).collect(Collectors.toList());
        processVoList = processVoList.stream().filter(f -> !Objects.isNull(f.getEffectiveDate())).collect(Collectors.toList());
        Map<String, List<InstitutionCourseAppInfoDataProcessDto>> resultMap = processVoList.stream().collect(Collectors.groupingBy(InstitutionCourseAppInfoDataProcessDto::getEffectiveDate));
//        Map<LocalDate, List<InstitutionCourseAppInfoDataProcessDto>> liquidLinkedMap = new LinkedHashMap<>();
//        resultMap.entrySet().stream()
//                .sorted(Map.Entry.<LocalDate, List<InstitutionCourseAppInfoDataProcessDto>>comparingByKey().reversed())
//                .forEachOrdered(e -> liquidLinkedMap.put(e.getKey(), e.getValue()));
        List<InstitutionCourseAppInfoDataProcessDto> result = new ArrayList<>();
        for (Map.Entry<String, List<InstitutionCourseAppInfoDataProcessDto>> entry : resultMap.entrySet()) {
            entry.getValue().sort(Comparator.comparing(InstitutionCourseAppInfoDataProcessDto::getWeight).reversed());
            result.add(entry.getValue().get(0));
        }
        result.addAll(special);
        return result;
    }


    @Override
    public ListResponseBo<CourseOtherInfoVo> getOtherCourseInfo(QueryOtherCourseDto queryOtherCourseDto, Page page) {
        if (Objects.isNull(queryOtherCourseDto)) {
            return new ListResponseBo<>(Collections.emptyList());
        }


        String fkTypeName = queryOtherCourseDto.getFkTypeName();
        List<AppCourseMappingInfoVo> appCourseMappingInfoDtoList = institutionCourseAppInfoMapper.getAppInfoMappingInfo(queryOtherCourseDto.getFkTableId(),fkTypeName);
        if (appCourseMappingInfoDtoList.isEmpty()) {
            return new ListResponseBo<>(Collections.emptyList());
        }
        Map<String, String> collect = appCourseMappingInfoDtoList.stream().collect(Collectors.toMap(AppCourseMappingInfoVo::getTypeKey, AppCourseMappingInfoVo::getTypeKeyIdStr));
        String groupKeyStr = appCourseMappingInfoDtoList.get(0).getGroupKeyStr();

        List<Long> notInCourseIds = Collections.emptyList();
        if (GeneralTool.isNotEmpty(groupKeyStr)) {
            List<InstitutionCourseAppInfo> institutionCourseAppInfos = institutionCourseAppInfoMapper.selectList(Wrappers.<InstitutionCourseAppInfo>lambdaQuery().ne(InstitutionCourseAppInfo::getFkTableId, queryOtherCourseDto.getFkTableId()).eq(InstitutionCourseAppInfo::getFkTableName, fkTypeName).eq(InstitutionCourseAppInfo::getGroupKey, groupKeyStr).eq(InstitutionCourseAppInfo::getFkTableNameType, TableEnum.INSTITUTION_COURSE.key));
            if (GeneralTool.isNotEmpty(institutionCourseAppInfos)) {
                notInCourseIds = institutionCourseAppInfos.stream().map(InstitutionCourseAppInfo::getFkTableIdType).collect(Collectors.toList());
            }
        }

        List<Long> institutionFacultyIds = Collections.emptyList();
        if (collect.containsKey(TableEnum.INSTITUTION_FACULTY.key)) {
            institutionFacultyIds = Arrays.stream(collect.get(TableEnum.INSTITUTION_FACULTY.key).split(",")).map(Long::valueOf).collect(Collectors.toList());
        }
        List<Long> courseTypeGroupIds = Collections.emptyList();
        if (collect.containsKey(TableEnum.INSTITUTION_COURSE_TYPE_GROUP.key)) {
            courseTypeGroupIds = Arrays.stream(collect.get(TableEnum.INSTITUTION_COURSE_TYPE_GROUP.key).split(",")).map(Long::valueOf).collect(Collectors.toList());
        }
        List<Long> courseTypeIds = Collections.emptyList();
        if (collect.containsKey(TableEnum.INSTITUTION_COURSE_TYPE.key)) {
            courseTypeIds = Arrays.stream(collect.get(TableEnum.INSTITUTION_COURSE_TYPE.key).split(",")).map(Long::valueOf).collect(Collectors.toList());
        }

        List<Long> institutionIds = Arrays.stream(collect.get(TableEnum.INSTITUTION.key).split(",")).map(Long::valueOf).collect(Collectors.toList());
        List<Long> majorLevelIds = Collections.emptyList();
        if (collect.containsKey(TableEnum.INSTITUTION_MAJOR_LEVEL.key)) {
            majorLevelIds = Arrays.stream(collect.get(TableEnum.INSTITUTION_MAJOR_LEVEL.key).split(",")).map(Long::valueOf).collect(Collectors.toList());
        }
        List<Long> courseIds = Collections.emptyList();
        if (collect.containsKey(TableEnum.INSTITUTION_COURSE.key)) {
            courseIds = Arrays.stream(collect.get(TableEnum.INSTITUTION_COURSE.key).split(",")).map(Long::valueOf).collect(Collectors.toList());
        }
        IPage<CourseOtherInfoVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<CourseOtherInfoVo> courseOtherInfo = institutionCourseAppInfoMapper.getCourseOtherInfo(iPage, institutionIds, majorLevelIds, courseIds, notInCourseIds, institutionFacultyIds, courseTypeGroupIds, courseTypeIds);
        page.setAll((int) iPage.getTotal());
        return new ListResponseBo<>(courseOtherInfo, BeanCopyUtils.objClone(page, Page::new));
    }
}
