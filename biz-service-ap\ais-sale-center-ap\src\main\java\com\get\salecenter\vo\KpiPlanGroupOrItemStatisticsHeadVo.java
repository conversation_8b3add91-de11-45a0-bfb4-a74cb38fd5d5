package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2024/4/26
 * @TIME: 10:38
 * @Description:
 **/
@Data
public class KpiPlanGroupOrItemStatisticsHeadVo {
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;

    @ApiModelProperty(value = "员工名称")
    private String staffName;
    @ApiModelProperty(value = "统计角色，枚举：BD=1/项目成员=2/PM=3")
    private Integer countRole;

    @ApiModelProperty(value = "统计方式，枚举：个人=1/团队(含业务下属)=2")
    private Integer countMode;
    @ApiModelProperty(value = "KPI方案考核人员Id")
    private Long fkKpiPlanStaffId;
}
