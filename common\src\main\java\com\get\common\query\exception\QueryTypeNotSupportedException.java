package com.get.common.query.exception;

import com.get.common.query.enums.QueryType;

/**
 * 查询类型不支持异常
 * 当使用了不支持的查询类型时抛出
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
public class QueryTypeNotSupportedException extends DynamicQueryException {

    public QueryTypeNotSupportedException(String message) {
        super("QUERY_TYPE_NOT_SUPPORTED", message);
    }

    public QueryTypeNotSupportedException(String message, Object... params) {
        super("QUERY_TYPE_NOT_SUPPORTED", message, params);
    }

    /**
     * 创建不支持的查询类型异常
     */
    public static QueryTypeNotSupportedException unsupported(QueryType queryType, String fieldName, String columnName) {
        String message = String.format("不支持的查询类型 - 类型: %s, 字段: %s, 数据库字段: %s", 
            queryType, fieldName, columnName);
        return new QueryTypeNotSupportedException(message, queryType, fieldName, columnName);
    }

    /**
     * 创建查询类型与字段类型不匹配异常
     */
    public static QueryTypeNotSupportedException typeMismatch(QueryType queryType, String fieldName, 
                                                             Class<?> fieldType, Object value) {
        String message = String.format("查询类型与字段类型不匹配 - 查询类型: %s, 字段: %s, 字段类型: %s, 值类型: %s", 
            queryType, fieldName, fieldType.getSimpleName(), 
            value != null ? value.getClass().getSimpleName() : "null");
        return new QueryTypeNotSupportedException(message, queryType, fieldName, 
            fieldType.getSimpleName(), value != null ? value.getClass().getSimpleName() : "null");
    }
}