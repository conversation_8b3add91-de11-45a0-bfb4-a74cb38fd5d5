package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.CompanyTreeVo;
import com.get.salecenter.vo.ContactPersonVo;
import com.get.salecenter.service.IContactPersonService;
import com.get.salecenter.dto.ContactPersonCompanyDto;
import com.get.salecenter.dto.ContactPersonDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/9/7
 * @TIME: 11:46
 * @Description: 联系人管理
 **/

@Api(tags = "联系人管理")
@RestController
@RequestMapping("sale/contactPerson")
public class ContactPersonController {
    @Autowired
    private IContactPersonService personService;


    /**
     * 新增代理联系人
     *
     * @param contactPersonVo
     * @return
     * @
     */
    @ApiOperation(value = "新增代理联系人", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/联系人管理/新增代理联系人")
    @PostMapping("addContactPerson")
    public ResponseBo addContactPerson(@RequestBody @Validated(ContactPersonDto.Add.class) ContactPersonDto contactPersonVo) {
        return SaveResponseBo.ok(personService.addContactPerson(contactPersonVo));
    }


    /**
     * 代理联系人列表
     *
     * @param searchBean
     * @return
     * @
     */
    @ApiOperation(value = "联系人列表")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/联系人管理/联系人列表")
    @PostMapping("getAgentContactPerson")
    public ResponseBo<ContactPersonVo> getAgentContactPerson(@RequestBody SearchBean<ContactPersonDto> searchBean) {
        List<ContactPersonVo> contactPersonVos = personService.getContactPersonDtos(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(contactPersonVos, p);
    }


    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/联系人管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<ContactPersonVo> detail(@PathVariable("id") Long id) {
        ContactPersonVo contactPersonVo = personService.finContactPersonById(id);
        return new ResponseBo<>(contactPersonVo);
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/联系人管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        personService.deleteContactPerson(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param contactPersonVo
     * @return
     * @
     */
    @ApiOperation(value = "修改代理联系人", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/联系人管理/修改代理联系人")
    @PostMapping("updateContactPerson")
    public ResponseBo<ContactPersonVo> updateContactPerson(@RequestBody @Validated(ContactPersonDto.Update.class) ContactPersonDto contactPersonVo) {
        return UpdateResponseBo.ok(personService.updateContactPerson(contactPersonVo));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 联系人安全配置
     * @Param [validList]
     * <AUTHOR>
     */
    @ApiOperation(value = "联系人-公司安全配置")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/联系人管理/安全配置")
    @PostMapping("editCompanyRelation")
    public ResponseBo editCompanyRelation(@RequestBody @Validated(ContactPersonCompanyDto.Add.class) ValidList<ContactPersonCompanyDto> validList) {
        personService.editContactPersonCompany(validList);
        return UpdateResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.CompanyTreeVo>
     * @Description: 回显联系人和公司的关系
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "回显联系人和公司的关系", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/联系人管理/联系人和公司的关系（数据回显）")
    @GetMapping("getContactRelation/{contactId}")
    public ResponseBo<CompanyTreeVo> getContractCompanyRelation(@PathVariable("contactId") Long id) {
        List<CompanyTreeVo> contractCompanyRelation = personService.getContactRelation(id);
        return new ListResponseBo<>(contractCompanyRelation);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 目标类型下拉
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "目标类型下拉", notes = "")
    @GetMapping("findTargetType")
    public ResponseBo findContractMediaType() {
        List<Map<String, Object>> datas = personService.findTargetType();
        return new ListResponseBo<>(datas);
    }

    @ApiOperation("联系人手机电话重复提示")
    @GetMapping("getExistContactPersonPM")
    @VerifyPermission(IsVerify = false)
    public ResponseBo getExistContactPersonPM(@RequestParam(value = "id", required = false) Long id,
                                              @RequestParam(value = "email", required = false) String email,
                                              @RequestParam(value = "tel", required = false) String tel,
                                              @RequestParam(value = "mobile", required = false) String mobile, @RequestParam("companyId") Long companyId) {
        ResponseBo responseBo = new ResponseBo();
        responseBo.setMessage(personService.getExistContactPersonPM(id, email, tel, mobile, companyId));
        return responseBo;
    }

    @ApiOperation("联系人电邮重复提示")
    @GetMapping("getExistContactPersonEmailPM")
    @VerifyPermission(IsVerify = false)
    public ResponseBo getExistContactPersonEmailPM(@RequestParam("email")String email){
        ResponseBo responseBo = new ResponseBo();
        responseBo.setMessage(personService.getExistContactPersonEmailPM(email));
        return responseBo;
    }
}
