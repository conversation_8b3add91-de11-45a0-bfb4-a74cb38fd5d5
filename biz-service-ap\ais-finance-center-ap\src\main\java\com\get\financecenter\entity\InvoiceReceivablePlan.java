package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("r_invoice_receivable_plan")
public class InvoiceReceivablePlan extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 发票Id
     */
    @ApiModelProperty(value = "发票Id")
    private Long fkInvoiceId;
    /**
     * 应收计划Id
     */
    @ApiModelProperty(value = "应收计划Id")
    private Long fkReceivablePlanId;
    /**
     * 发票绑定金额
     */
    @ApiModelProperty(value = "发票绑定金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "佣金通知")
    private String commissionNotice;

    @ApiModelProperty(value = "是否预付，0否/1是")
    private Boolean isPayInAdvance;

    @ApiModelProperty(value = "预付百分比：50, 100")
    private BigDecimal payInAdvancePercent;

    @ApiModelProperty(value = "预付金额")
    private BigDecimal payInAdvanceAmount ;

}