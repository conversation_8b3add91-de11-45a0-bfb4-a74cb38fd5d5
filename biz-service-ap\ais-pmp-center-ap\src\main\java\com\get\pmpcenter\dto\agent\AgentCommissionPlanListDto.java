package com.get.pmpcenter.dto.agent;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/4/21
 * @Version 1.0
 * @apiNote:代理佣金方案列表Dto
 */
@Data
public class AgentCommissionPlanListDto {

    @ApiModelProperty(value = "分公司ID")
    @NotNull(message = "分公司ID不能为空")
    private Long companyId;

    @ApiModelProperty(value = "学校提供商ID")
    private Long institutionProviderId;

    @ApiModelProperty(value = "国家ID")
    private Long countryId;

    @ApiModelProperty(value = "代理等级ID")
    private Long agentCommissionTypeId;

    @ApiModelProperty(value = "上架状态:1上架0下架")
    private Integer isActive;

    @ApiModelProperty(value = "学校名称")
    private String institutionName;

    @ApiModelProperty(value = "未设置:-1;未审核:0;审核中:1;已通过:2;已驳回:3;合同方案调整:4")
    private Integer status;

    @ApiModelProperty(value = "方案开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "方案结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "方案名称")
    private String planName;

    @ApiModelProperty(value = "方案名称/学校名称/供应商名称")
    private String keyword;

    @ApiModelProperty(value = "审核人ID")
    private Long staffId;

    @ApiModelProperty(value = "学校类型ID")
    private Long institutionTypeId;

    @ApiModelProperty(value = "适用国家/地区IDs")
    private List<Long> territoryIds;


}
