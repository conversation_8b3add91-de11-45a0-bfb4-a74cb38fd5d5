package com.get.pmpcenter.vo.agent;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/2/25  17:44
 * @Version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentCommissionPlanVo {

    @ApiModelProperty(value = "代理佣金方案ID-若是学校提供商方案的话没有id返回")
    private Long id;

    @ApiModelProperty(value = "学校提供商Id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "代理佣金分类Id")
    private Long fkAgentCommissionTypeId;

    @ApiModelProperty(value = "学校提供商佣金方案Id")
    private Long fkInstitutionProviderCommissionPlanId;

    @ApiModelProperty(value = "方案名称")
    private String name;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "方案有效时间（开始）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "方案有效时间（结束）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "是否全局模板：0否/1是")
    private Integer isGobal;

    @ApiModelProperty(value = "有效时间是否无时间限制：0否/1是")
    private Integer isTimeless;

    @ApiModelProperty(value = "是否主模板：0否/1是")
    private Integer isMain;

    @ApiModelProperty(value = "是否激活：0否/1是")
    private Integer isActive;

    @ApiModelProperty(value = "代理佣金分类名称")
    private String agentCommissionTypeName;

    @ApiModelProperty(value = "创建用户(登录账号)")
    private String gmtCreateUser;

    @ApiModelProperty(value = "继承模板名称")
    private String extendTemplateName;

    @ApiModelProperty(value = "是否锁定：0否/1是")
    private Integer isLocked;

    @ApiModelProperty(value = "锁定权限:0-方案未锁定,没有锁定权限;1-方案未锁定,有锁定权限;2-方案已锁定,没有解锁权限;3-方案已锁定,有解锁权限")
    private Integer lockPermission;

    @ApiModelProperty(value = "审批状态：0未提交/1待审批/2通过/3拒绝")
    private Integer approvalStatus;

    @ApiModelProperty(value = "审核权限:0-方案未提交审核,没有权限提交审核;1-方案未提交审核,有权限提交审核;2-方案已提交审核但没有审核权限;3-方案已提交审核且有审核权限;")
    private Integer approvalPermission;
}
