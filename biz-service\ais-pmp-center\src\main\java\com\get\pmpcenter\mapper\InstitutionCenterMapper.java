package com.get.pmpcenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.get.pmpcenter.vo.agent.TimeOverlapPlanVo;
import com.get.pmpcenter.vo.common.*;
import com.get.pmpcenter.vo.institution.ProviderVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * ais_institution_center
 */
@Mapper
@DS("institution")
public interface InstitutionCenterMapper {

    /**
     * 学校供应商列表
     *
     * @return
     */
    List<ProviderVo> institutionProviderList(@Param("companyIds") List<Long> companyIds, @Param("countryIds") List<Long> countryIds);

    /**
     * 学校列表
     *
     * @return
     */
    List<InstitutionVo> institutionList(@Param("institutionProviderId") Long institutionProviderId, @Param("companyIds") List<Long> companyIds);

    /**
     * 国家列表
     *
     * @return
     */
    List<CountryVo> countryList();

    /**
     * 根据国家ID查询国家名称
     *
     * @param countryIds
     * @return
     */
    List<CountryVo> queryCountryListByIds(@Param("countryIds") List<Long> countryIds);

    /**
     * 根据国家ID查询学校ID
     *
     * @param countryIds
     * @return
     */
    List<InstitutionVo> getInstitutionIdsCountryIds(@Param("countryIds") List<Long> countryIds);


    /**
     * 根据供应商ID查询学校ID列表
     *
     * @param providerIds
     * @param companyIds
     * @return
     */
    List<ProviderInstitutionVo> getInstitutionListByProviderIds(@Param("providerIds") List<Long> providerIds, @Param("companyIds") List<Long> companyIds);


    /**
     * 根据学校提供商ID+分公司+业务国家查询学校列表
     *
     * @param institutionProviderId
     * @param companyIds
     * @param countryIds
     * @return
     */
    List<InstitutionVo> getInstitutionListBycompanyIdsAndCountryIds(@Param("institutionProviderId") Long institutionProviderId,
                                                                    @Param("companyIds") List<Long> companyIds,
                                                                    @Param("countryIds") List<Long> countryIds);


    /**
     * 根据学校ID列表查询学校列表
     *
     * @param institutionIds
     * @return
     */
    List<InstitutionVo> getInstitutionListByIds(@Param("institutionIds") List<Long> institutionIds);


    /**
     * 根据学校ID列表查询国家列表
     *
     * @param institutionIds
     * @return
     */
    List<CountryVo> getCountryListByInstitutionIds(@Param("institutionIds") List<Long> institutionIds);

    /**
     * 根据方案ID查询学校提供商名称和分公司
     *
     * @param providerPlanId
     * @param companyId
     * @return
     */
    TimeOverlapPlanVo getProviderNameAndCompanyNum(@Param("providerId") Long providerId,
                                                   @Param("companyId") Long companyId);


    /**
     * 根据供应商ID列表查询学校id列表-根据分公司过滤
     *
     * @param providerIds
     * @param companyIds
     * @return
     */
    List<ProviderInstitutionVo> selectInstitutionListByProviderIdsAndCompanyIds(@Param("providerIds") List<Long> providerIds,
                                                                                @Param("companyIds") List<Long> companyIds);


    /**
     * 根据分公司ID查询提供商和学校的关联关系-根据安全组别过滤-代理端
     *
     * @param companyIds
     * @return
     */
    List<ProviderInstitutionVo> selectProviderInstitutionByCompanyIds(@Param("companyId") Long companyId,
                                                                      @Param("companyIds") List<Long> companyIds,
                                                                      @Param("institutionIds") List<Long> institutionIds);

    /**
     * 根据分公司ID查询合同提供商和学校的关联关系-根据安全组别过滤-合同端
     *
     * @param companyIds
     * @return
     */
    List<ProviderInstitutionVo> selectContractProviderInstitutionByCompanyIds(@Param("companyIds") List<Long> companyIds,
                                                                              @Param("institutionIds") List<Long> institutionIds);


    /**
     * 提供商列表-根据安全组别过滤-代理端
     *
     * @param companyIds
     * @param countryIds
     * @param providerIds
     * @return
     */
    List<ProviderVo> selectInstitutionProviderListByIds(@Param("companyIds") List<Long> companyIds,
                                                        @Param("countryIds") List<Long> countryIds,
                                                        @Param("providerIds") List<Long> providerIds);


    /**
     * 根据学校名称查询学校ID
     *
     * @param institutionName
     * @return
     */
    List<InstitutionVo> getInstitutionIdByName(@Param("countryIds") List<Long> countryIds, @Param("institutionName") String institutionName);


    /**
     * 询提供商和学校的关联关系-根据安全组别过滤-代理端
     *
     * @param companyIds
     * @param institutionIds
     * @return
     */
    List<ProviderInstitutionVo> selectPassProviderInstitution(@Param("companyIds") List<Long> companyIds,
                                                              @Param("institutionIds") List<Long> institutionIds);

    /**
     * 根据供应商ID列表查询供应商列表
     *
     * @param providerIds
     * @return
     */
    List<ProviderVo> selectProviderByIds(@Param("providerIds") List<Long> providerIds);

    /**
     * 学校类型列表
     *
     * @return
     */
    List<InstitutionTypeVo> institutionTypeList();

    /**
     * 大区列表
     *
     * @return
     */
    List<RegionVo> regionList();

    /**
     * 根据大区ID查询大区名称
     * *
     */
    List<RegionVo> queryRegionListByIds(@Param("regionIds") List<Long> regionIds);

    /**
     * 根据国家ID查询大区ID
     *
     * @param countryId
     * @return
     */
    String selectCountryAreaRegionIds(@Param("countryId") Long countryId);

    /**
     * 根据国家ID列表查询大区ID列表
     */
    List<String> selectAreaRegionIdsByCountryIds(@Param("countryIds") List<Long> countryIds);
}
