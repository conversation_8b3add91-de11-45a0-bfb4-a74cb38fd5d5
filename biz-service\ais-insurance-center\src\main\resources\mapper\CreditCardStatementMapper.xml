<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.insurancecenter.mapper.CreditCardStatementMapper">

    <!--    type-1:交易记录-只包含支出和还款-->
    <!--    type-2:详细列表-信用卡详情的流水记录,包含完整的4种交易类型-->
    <select id="selectTradeRecordPage" resultType="com.get.insurancecenter.vo.card.TradeRecordVo">
        select cs.*,
        o.insurance_num as insuranceNum,
        o.order_num as orderNum,
        o.insurant_name as insurantName,
        o.id as orderId,
        t.type_name as productTypeName
        from m_credit_card_statement cs
        left join m_insurance_order o
        on cs.relation_target_key = 'm_insurance_order' and cs.relation_target_id = o.id
        left join u_product_type t on o.fk_product_type_id = o.id
        <where>
            cs.fk_credit_card_id = #{param.creditCardId}
            <if test="param.type == 1">
                and cs.business_type in (1,2)
            </if>
            <!--交易类型-->
            <if test="param.businessType != null">
                and cs.business_type =
                #{param.businessType}
            </if>
            <!--交易状态-->
            <if test="param.status != null">
                and cs.status =
                #{param.status}
            </if>
            <!--交易时间-->
            <if test="param.startTime != null">
                and cs.gmt_create &gt;=
                #{param.startTime}
            </if>
            <if test="param.endTime != null">
                and cs.gmt_create &lt;=
                #{param.endTime}
            </if>
            <!--学生姓名/保险人姓名(仅当是支出或者是退款的时候才会关联订单)-->
            <if test="param.insurantName != null and param.insurantName != ''">
                and cs.business_type in (1, 3) and o.insurant_name like concat('%',
                #{param.insurantName},
                '%'
                )
            </if>
            <!--交易对象(仅当是支出或者是退款的时候才会关联订单)-->
            <if test="param.productTypeId != null and param.productTypeId > 0 ">
                and cs.business_type in (1, 3) and o.fk_product_type_id =
                #{param.productTypeId}
            </if>
        </where>
        order by cs.gmt_create desc
    </select>
</mapper>