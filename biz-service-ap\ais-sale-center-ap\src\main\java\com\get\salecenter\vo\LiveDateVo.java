package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: Hardy
 * @create: 2021/9/2 12:22
 * @verison: 1.0
 * @description:
 */
@Data
public class LiveDateVo {

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "住房日期")
    private Date date;

    @ApiModelProperty(value = "是否可住")
    private Boolean usable;

    @ApiModelProperty(value = "房间id")
    private Long fkConventionHotelRoomId;
}
