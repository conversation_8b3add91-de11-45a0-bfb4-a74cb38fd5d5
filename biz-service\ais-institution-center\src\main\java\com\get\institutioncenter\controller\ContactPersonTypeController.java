package com.get.institutioncenter.controller;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.vo.ContactPersonTypeVo;
import com.get.institutioncenter.service.IContactPersonTypeService;
import com.get.institutioncenter.dto.ContactPersonTypeListDto;
import com.get.institutioncenter.dto.ContactPersonTypeUpdateDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/2/8 11:57
 * @verison: 1.0
 * @description:
 */
@Api(tags = "联系人类型管理")
@RestController
@RequestMapping("institution/contactPersonType")
public class ContactPersonTypeController {

    @Resource
    private IContactPersonTypeService contactPersonTypeService;


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :批量新增信息
     * @Param [contactPersonTypeVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/联系人类型管理/新增联系人类型")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(ContactPersonTypeUpdateDto.Add.class) ValidList<ContactPersonTypeUpdateDto> contactPersonTypeVos) {
        contactPersonTypeService.batchAdd(contactPersonTypeVos);
        return SaveResponseBo.ok();
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.REMINDERCENTER, type = LoggerOptTypeConst.DELETE, description = "提醒中心/联系人类型管理/删除联系人类型")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        contactPersonTypeService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param contactPersonTypeVo
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.REMINDERCENTER, type = LoggerOptTypeConst.EDIT, description = "提醒中心/联系人类型管理/更新联系人类型")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated(ContactPersonTypeUpdateDto.Update.class) ContactPersonTypeUpdateDto contactPersonTypeVo) {
        return UpdateResponseBo.ok(contactPersonTypeService.updateContactPersonType(contactPersonTypeVo));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "typeName类型名称")
    @OperationLogger(module = LoggerModulesConsts.REMINDERCENTER, type = LoggerOptTypeConst.LIST, description = "提醒中心/联系人类型管理/查询联系人类型")
    @PostMapping("datas")
    public ResponseBo datas(@RequestBody SearchBean<ContactPersonTypeListDto> page) {
        List<ContactPersonTypeVo> datas = contactPersonTypeService.getContactPersonTypes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 联系人类型下拉框
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "联系人类型下拉框", notes = "typeName类型名称")
    @GetMapping("getAllType")
    public ResponseBo getAllType() {
        List<ContactPersonTypeVo> datas = contactPersonTypeService.getContactPersonTypes();
        return new ListResponseBo<>(datas);
    }


    /**
     * 上移下移
     *
     * @param contactPersonTypeVos
     * @return
     * @
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.REMINDERCENTER, type = LoggerOptTypeConst.EDIT, description = "提醒中心/联系人类型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<ContactPersonTypeUpdateDto> contactPersonTypeVos) {
        contactPersonTypeService.movingOrder(contactPersonTypeVos);
        return ResponseBo.ok();
    }


}
