package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 凭证明细
 */
@Data
@TableName("m_vouch_item")
public class VouchItem extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "凭证Id")
    private Long fkVouchId;

    @ApiModelProperty(value = "摘要")
    private String summary;

    @ApiModelProperty(value = "科目Id")
    private Long fkAccountingItemId;

    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "借方发生额")
    private BigDecimal amountDr;

    @ApiModelProperty(value = "贷方发生额")
    private BigDecimal amountCr;

    @ApiModelProperty(value = "关联类型Key（目标类型表名）")
    private String relationTargetKey;

    @ApiModelProperty(value = "关联类型Id（目标类型表对应记录项Id）")
    private Long relationTargetId;

}