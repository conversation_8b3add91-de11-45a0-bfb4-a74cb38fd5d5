<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.permissioncenter.dao.StaffResourceMapper">
  <resultMap id="BaseResultMap" type="com.get.permissioncenter.entity.StaffResource">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_staff_id" jdbcType="BIGINT" property="fkStaffId" />
    <result column="fk_resource_key" jdbcType="VARCHAR" property="fkResourceKey" />
    <result column="permission" jdbcType="INTEGER" property="permission" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insert" parameterType="com.get.permissioncenter.entity.StaffResource" keyProperty="id" useGeneratedKeys="true">
    insert into r_staff_resource (id, fk_staff_id, fk_resource_key, 
      permission, gmt_create, gmt_create_user, 
      gmt_modified, gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkStaffId,jdbcType=BIGINT}, #{fkResourceKey,jdbcType=VARCHAR}, 
      #{permission,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <select id="findStaffResourcesByStaffId" resultMap="BaseResultMap">
  SELECT * FROM r_staff_resource sr WHERE sr.fk_staff_id =#{StaffId}
  </select>

  <select id="findStaffResourcesByVo" resultMap="BaseResultMap" >
    SELECT * FROM r_staff_resource sr
    <where>
      <if test="fkStaffId!=null" >
        and  sr.fk_staff_id = #{fkStaffId}
      </if>
      <if test="permission!=null" >
        and  sr.permission = #{permission,jdbcType=INTEGER}
      </if>
    </where>
  </select>
  <select id="deleteByStaffId">
    delete from r_staff_resource where 1=1
    <if test="fkStaffId!=null and fkStaffId !=''" >
       and  fk_staff_id = #{fkStaffId}
    </if>
  </select>

  <select id="isExistByStaffId" resultType="java.lang.Boolean">
    SELECT IFNULL(max(id),0) id FROM  m_staff_config where fk_staff_id =#{staffId}
  </select>
    <select id="getStaffIdByKey" resultType="java.lang.Long">
      SELECT fk_staff_id FROM r_staff_resource WHERE fk_resource_key = #{key} AND fk_staff_id IN
      <foreach collection="staffIds" open="(" close=")" item="item" separator=",">
        #{item}
      </foreach>
    </select>
</mapper>