package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Hardy
 * @create: 2024/10/9 11:15
 * @verison: 1.0
 * @description:
 */
@Data
public class LikeCollectionActivitySearchDto {

    @ApiModelProperty(value = "集赞类型")
    private Integer type;

    @ApiModelProperty(value = "峰会id")
    private Long fkConventionId;

    @ApiModelProperty(value = "状态：0未审批/1已审批")
    private Integer status;

    @ApiModelProperty(value = "参会人员名称/电话")
    private String nameOrTel;

    @ApiModelProperty(value = "昵称")
    private String nickname;

    @ApiModelProperty(value = "社交平台id")
    private String platformAccount;

}
