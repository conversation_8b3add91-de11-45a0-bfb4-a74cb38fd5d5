package com.get.officecenter.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.officecenter.vo.WorkScheduleStaffConfigVo;
import com.get.officecenter.entity.WorkScheduleStaffConfig;
import com.get.officecenter.dto.TimeConfigDto;
import com.get.officecenter.dto.WorkScheduleStaffConfigListDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface WorkScheduleStaffConfigMapper extends GetMapper<WorkScheduleStaffConfig> {

    /**
     *  查询时间点特殊人员工作时间设置
     * <AUTHOR>
     * @DateTime 2023/2/28 15:48
     */
    WorkScheduleStaffConfig getWorkScheduleStaffConfig(@Param("timeConfigDto") TimeConfigDto timeConfigDto);

    /**
     * 查询特殊人员工作时间设置
     *
     * @Date 12:47 2022/11/7
     * <AUTHOR>
     */
    List<WorkScheduleStaffConfigVo> selectWorkScheduleStaffConfigs(IPage<WorkScheduleStaffConfig> pages, @Param("data") WorkScheduleStaffConfigListDto data);
    /**
     * 根据员工ID获取特殊人员事假设置
     * <AUTHOR>
     * @DateTime 2023/2/10 11:07
     */
    List<WorkScheduleStaffConfigVo> getWorkScheduleStaffConfigByStaffId(@Param("fkCompanyId") Long fkCompanyId, @Param("fkStaffId") Long fkStaffId);
}