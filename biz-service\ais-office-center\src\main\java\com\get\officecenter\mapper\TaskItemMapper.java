package com.get.officecenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.officecenter.vo.CommentVo;
import com.get.officecenter.vo.TaskItemVo;
import com.get.officecenter.entity.TaskItem;
import com.get.officecenter.dto.TaskItemDto;
import com.get.officecenter.vo.TaskStatisticsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

@Mapper
public interface TaskItemMapper extends GetMapper<TaskItem> {


    @DS("officedb-doris")
    List<TaskItem>  getTaskItemList(@Param("taskIds") List<Long> taskIds);

    @DS("officedb-doris")
    List<Long>  getFkStaffIdTo(@Param("taskIds") List<Long> taskIds);


    @DS("officedb-doris")
    List<Long>  getStaffIdByStatus(@Param("taskIds") List<Long> taskIds);

    Long batchInsert(@Param("taskItems") List<TaskItem> taskItems);



    /**
     * 获取子任务列表 分页
     *
     * @param iPage
     * @param taskItemDto
     * @return
     */
    @DS("officedb-doris")
    List<TaskItemVo> getTaskItems(IPage<TaskItemVo> iPage,
                                  @Param("taskItemDto") TaskItemDto taskItemDto,
                                  @Param("staffIds") Set<Long> staffIds,
                                  @Param("fkAreaCountryIds")Set<Long> fkAreaCountryIds,
                                  @Param("isFlag")Boolean isFlag);

    List<TaskStatisticsVo>getPersonalTaskStatistics(IPage<TaskStatisticsVo> iPage,
                                                    @Param("taskItemDto") TaskItemDto taskItemDto,
                                                    @Param("staffIds") Set<Long> staffIds,
                                                    @Param("fkAreaCountryIds")Set<Long> fkAreaCountryIds);

    List<TaskItem> getTaskItemListByTaskItemIds(@Param("taskItemIds") Set<Long> taskItemIds);

    Integer updateBatchById(Set<Long> taskItemBatch, Integer status,String gmtModifiedUser,Long taskId);

    List<CommentVo> getTaskAllStaff(Long taskId, Set<Long> staffIds, Set<Long> fkAreaCountryIds, String typeKey);

    List<CommentVo> getTaskFeedbackCountByStatus(Long taskId, Set<Long> staffIds, Set<Long> fkAreaCountryIds, String typeKey,Boolean isFlag);

    List<CommentVo> getTasksPendingFeedback(Long taskId, Set<Long> staffIds, Set<Long> fkAreaCountryIds, String typeKey);

    List<CommentVo> getCountUnfinishedTasks(Long taskId, Set<Long> staffIds, Set<Long> fkAreaCountryIds, String typeKey);

    List<CommentVo> getTotalByItem(Long taskId, Set<Long> staffIds, Set<Long> fkAreaCountryIds);

    Integer getTasksFeedback(Long taskId, Set<Long> staffIds, Set<Long> fkAreaCountryIds);


    Integer getCountUnfinish(Long taskId, Set<Long> staffIds, Set<Long> fkAreaCountryIds);
}
