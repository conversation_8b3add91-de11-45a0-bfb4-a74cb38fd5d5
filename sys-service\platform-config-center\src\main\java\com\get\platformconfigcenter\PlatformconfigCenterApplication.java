package com.get.platformconfigcenter;

import com.get.common.constant.AppCenterConstant;
import com.get.core.cloud.feign.EnableGetFeign;
import com.get.core.start.GetApplication;
import org.springframework.cloud.client.SpringCloudApplication;

@EnableGetFeign
@SpringCloudApplication
//@SeataCloudApplication
public class PlatformconfigCenterApplication {
    public static void main(String[] args) {
        GetApplication.run(AppCenterConstant.APPLICATION_PLATFORM_CONFIG_CENTER, com.get.platformconfigcenter.PlatformconfigCenterApplication.class, args);
    }
}

