package com.get.platformconfigcenter.controller;

import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @DATE: 2021/12/3
 * @TIME: 16:51
 * @Description:
 **/
@Api(tags = "MSO模板页面")
@RestController
@RequestMapping("platform/mso/sitemapPageTemplate")
public class SitemapPageTemplateController {
//    @Resource
//    private SitemapPageTemplateService sitemapPageTemplateService;
//
//    @ApiOperation(value = "模板页面列表数据", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.LIST, description = "业务平台配置中心/MSO/页面模板配置/查询")
//    @PostMapping("datas")
//    @VerifyLogin(IsVerify = false)
//    public ResponseBo<SitemapPageTemplateVo> datas(@RequestBody SearchBean<SitemapPageTemplateDto> page) {
//        List<SitemapPageTemplateVo> sitemapPageTemplates = sitemapPageTemplateService.getSitemapPageTemplateList(page.getData(), page);
//        Page p = BeanCopyUtils.objClone(page, Page::new);
//        return new ListResponseBo<>(sitemapPageTemplates, p);
//    }
//
//    /**
//     * 新增模板页面
//     *
//     * @param sitemapPageTemplateVo
//     * @return
//     * @
//     */
//    @ApiOperation(value = "新增模板页面接口", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.ADD, description = "业务平台配置中心/MSO/页面模板配置/新增模板页面")
//    @PostMapping("add")
//    @VerifyLogin(IsVerify = false)
//    public ResponseBo add(@RequestBody @Validated(SitemapPageTemplateDto.Add.class) SitemapPageTemplateDto sitemapPageTemplateVo) {
//        return SaveResponseBo.ok(sitemapPageTemplateService.addSitemapPageTemplate(sitemapPageTemplateVo));
//    }
//
//    /**
//     * 更新模板页面
//     *
//     * @param sitemapPageTemplateVo
//     * @return
//     * @
//     */
//    @ApiOperation(value = "修改模板页面接口", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.EDIT, description = "业务平台配置中心/MSO/页面模板配置/更新模板页面")
//    @PostMapping("update")
//    @VerifyLogin(IsVerify = false)
//    public ResponseBo<SitemapPageTemplateVo> update(@RequestBody @Validated(SitemapPageTemplateDto.Update.class) SitemapPageTemplateDto sitemapPageTemplateVo) {
//        return UpdateResponseBo.ok(sitemapPageTemplateService.updateSitemapPageTemplate(sitemapPageTemplateVo));
//    }
//
//    /**
//     * 页面模板详情
//     *
//     * @param id
//     * @return
//     * @
//     */
//    @ApiOperation(value = "页面模板详情接口", notes = "id为此条数据id")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DETAIL, description = "业务平台配置中心/MSO/页面模板配置/页面模板详情")
//    @GetMapping("/{id}")
//    @VerifyLogin(IsVerify = false)
//    public ResponseBo<SitemapPageTemplateVo> detail(@PathVariable("id") Long id) {
//        SitemapPageTemplateVo data = sitemapPageTemplateService.findSitemapPageTemplateById(id);
//        return new ResponseBo<>(data);
//    }
//
//    /**
//     * @Description:删除代理配置
//     * @Param
//     * @Date 16:56 2021/5/13
//     * <AUTHOR>
//     */
//    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DELETE, description = "业务平台配置中心/MSO/页面模板配置/删除代理配置")
//    @PostMapping("delete/{id}")
//    @VerifyLogin(IsVerify = false)
//    public ResponseBo delete(@PathVariable("id") Long id) {
//        sitemapPageTemplateService.delete(id);
//        return DeleteResponseBo.ok();
//    }
}
