package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import com.get.schoolGateCenter.entity.StaffResource;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class StaffResourceVo extends BaseVoEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 员工Id
     */
    @ApiModelProperty("员工Id")
    @NotNull(message = "员工Id不能为空", groups = {Add.class, Update.class})
    private Long fkStaffId;
    /**
     * 系统资源Key
     */
    @ApiModelProperty("系统资源Key")
    /*@NotNull(message = "系统资源Key", groups = {Add.class, Update.class})*/
    private String fkResourceKey;
    /**
     * 权限：0禁止/1允许
     */
    @ApiModelProperty("权限：0禁止/1允许")
    private Integer permission;


    private List<StaffResource> staffResources;
    private String keyword;


}
