package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.salecenter.vo.EnrolFailureReasonVo;
import com.get.salecenter.dto.EnrolFailureReasonDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2021/6/16
 * @TIME: 15:06
 * @Description:
 **/
public interface EnrolFailureReasonService {
    /**
     * @return java.util.List<com.get.salecenter.vo.EnrolFailureReasonVo>
     * @Description:查询所有原因（下拉）
     * @Param []
     * <AUTHOR>
     */
    List<EnrolFailureReasonVo> getAllEnrolFailureReasonDto();

    /**
     * @return java.util.List<com.get.salecenter.vo.EnrolFailureReasonVo>
     * @Description:查询所有原因（列表）
     * @Param [enrolFailureReasonDto, page]
     * <AUTHOR>
     */
    List<EnrolFailureReasonVo> getEnrolFailureReasonDtos(EnrolFailureReasonDto enrolFailureReasonDto, Page page);

    /**
     * @return void
     * @Description:新增事件
     * @Param [enrolFailureReasonDto]
     * <AUTHOR>
     */
    void addEnrolFailureReason(List<EnrolFailureReasonDto> enrolFailureReasonDto);

    /**
     * @return com.get.salecenter.vo.EnrolFailureReasonVo
     * @Description:修改
     * @Param [enrolFailureReasonDto]
     * <AUTHOR>
     */
    EnrolFailureReasonVo updateEnrolFailureReason(EnrolFailureReasonDto enrolFailureReasonDto);

    /**
     * @return void
     * @Description:删除
     * @Param [id]
     * <AUTHOR>
     */
    void deleteEnrolFailureReason(Long id);

    /**
     * @return com.get.salecenter.vo.EnrolFailureReasonVo
     * @Description:查询byID
     * @Param [id]
     * <AUTHOR>
     */
    EnrolFailureReasonVo findEnrolFailureReasonById(Long id);


    Map<Long, EnrolFailureReasonVo> findEnrolFailureReasonByIds(Set<Long> ids);
    /**
     * @return void
     * @Description:上移下移
     * @Param [enrolFailureReasonDtoList]
     * <AUTHOR>
     */
    void movingOrder(List<EnrolFailureReasonDto> enrolFailureReasonDtoList);
}
