package com.get.officecenter.service.impl;

import cn.hutool.poi.excel.BigExcelWriter;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.secure.UserInfo;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestHeaderHandler;
import com.get.file.utils.FileUtils;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.officecenter.dto.TaskItemDto;
import com.get.officecenter.entity.Task;
import com.get.officecenter.service.AsyncExportService;
import com.get.officecenter.service.ITaskItemService;
import com.get.officecenter.vo.TaskItemExportVo;
import com.get.officecenter.vo.TaskItemVo;
import com.get.permissioncenter.entity.StaffDownload;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class AsyncExportServiceImpl implements AsyncExportService {


    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private ITaskItemService taskItemService;

    @Resource
    private IFileCenterClient fileCenterClient;

    //@Async
    @Override
    @Async
    public void exportTaskItemList(TaskItemDto taskItemDto, UserInfo user, String locale, Task task,Map<String, String> headerMap) {
        RequestHeaderHandler.setHeaderMap(headerMap);
        log.info("ExportTaskItemList userInfo{}", user);
        // 设置下载记录对象
        StaffDownload download = new StaffDownload();
        try {
            // 记录开始时间
            long startTime = System.currentTimeMillis();

            // 设置下载记录信息
            download.setFkStaffId(user.getStaffId())
                    .setOptKey(FileTypeEnum.OFFICE_TASK_ITEM_EXPORT.key)
                    .setOptDescription("【任务管理】任务内容："+task.getTaskDescription()+"《子任务列表》导出")
                    .setStatus(1) // 1表示开始
                    .setGmtCreate(new Date());
            download.setGmtCreateUser(user.getLoginId());

            // 初始化下载记录
            download.setId(permissionCenterClient.addDownloadRecord(download));


            List<TaskItemVo> taskItemVos = taskItemService.getTaskItemExportData(taskItemDto, user.getStaffId(),locale);

            // 创建导出数据列表
            List<TaskItemExportVo> exportList = new ArrayList<>();

            // 转换数据为导出格式
            for (TaskItemVo data : taskItemVos) {
                    String value = ProjectExtraEnum.getInitialValueByKey(Integer.valueOf(data.getStatus()), ProjectExtraEnum.TASK_STATUS);
                    TaskItemExportVo exportVo= BeanCopyUtils.objClone(data, TaskItemExportVo::new);
                    exportVo.setStatus(value);
                // 添加到导出列表
                exportList.add(exportVo);
            }

            // 生成Excel文件
            BigExcelWriter writer = FileUtils.getExcelWhithHeaderStyle(
                    exportList,
                    "TaskItemList",
                    TaskItemExportVo.class,
                    locale
            );

            // 将Excel转为MultipartFile
            MultipartFile excelFile = FileUtils.getFile(writer, "TaskItemList.xlsx");

            // 上传文件到腾讯云
            Result<List<FileDto>> uploadResult = fileCenterClient.uploadAppendix(
                    new MultipartFile[]{excelFile},
                    LoggerModulesConsts.EXPORT
            );

            // 处理上传结果
            if (uploadResult.isSuccess() &&
                    GeneralTool.isNotEmpty(uploadResult.getData())) {

                FileDto fileDto = uploadResult.getData().get(0);
                if (fileDto.getFileGuid() != null) {
                    // 上传成功，更新下载记录
                    download.setStatus(2) // 2表示成功
                            .setFkFileGuid(fileDto.getFileGuid());
                } else {
                    download.setStatus(0); // 0表示失败
                }
            } else {
                download.setStatus(0); // 0表示失败
            }

            // 记录导出耗时
            long endTime = System.currentTimeMillis();
            log.info("子任务列表导出耗时: {}ms", (endTime - startTime));
            log.info("----------导出结束:导出条数："+taskItemVos.size());
        } catch (Exception e) {
            log.error("子任务列表导出异常", e);
            download.setStatus(0) // 0表示失败
                    .setRemark("导出失败: " + e.getMessage());
        } finally {
            // 更新下载记录
            download.setGmtModified(new Date());
            download.setGmtModifiedUser(user.getLoginId());
            permissionCenterClient.updateDownload(download);
        }
    }
}
