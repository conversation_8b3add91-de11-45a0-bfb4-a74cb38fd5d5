package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2022/2/14
 * @TIME: 14:44
 * @Description:
 **/
@Data
public class BankDto {
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    /**
     * 银行名称
     */
    @ApiModelProperty(value = "银行名称")
    private String bankName;

    /**
     * 银行国际化名称，语言按需要进行翻译
     */
    @ApiModelProperty(value = "银行国际化名称，语言按需要进行翻译")
    private String bankNameInternational;
}
