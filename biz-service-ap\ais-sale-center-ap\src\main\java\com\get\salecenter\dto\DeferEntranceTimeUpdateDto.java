package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 设置延迟入学时间编辑类
 *
 * <AUTHOR>
 * @date 2023/3/3 11:12
 */
@Data
public class DeferEntranceTimeUpdateDto {

    @ApiModelProperty(value = "学习计划id")
    @NotNull(message = "学习计划id不能为空")
    private Long fkStudentOfferItemId;

    @ApiModelProperty(value = "是否延迟入学标记：0否/1是 ")
    private Boolean isDeferEntrance;

    @ApiModelProperty(value = "延迟入学记录id (有数据则表示要删除这条记录)")
    private Long deferEntranceTimeId;

    @ApiModelProperty(value = "设置延迟入学时间 (有数据则表示要新增延迟时间)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deferEntranceTime;

}
