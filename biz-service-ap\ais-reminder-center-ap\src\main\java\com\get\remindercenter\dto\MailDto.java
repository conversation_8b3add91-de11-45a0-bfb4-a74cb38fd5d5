package com.get.remindercenter.dto;

import lombok.Data;

@Data
public class MailDto {
    private String defaultEncoding;

    private String host;

    private Integer port;

    private String protocol;

    private String userName;

    private String password;

    private String title;

    /**
     * 主邮件接收人
     */
    private String toEmail;

    /**
     * 抄送人邮件地址，不可为空
     */
    private String ccEmail;

    /**
     * 主邮件接收人
     */
    private String[] toEmails;

    /**
     * 抄送人邮件地址，不可为空
     */
    private String[] ccEmails;

    /**
     * 邮件主要内容，可以使用模板
     */
    private String template;

    private boolean flag;


    /**
     * 新闻专用参数
     */
    private Long newsId;

}
