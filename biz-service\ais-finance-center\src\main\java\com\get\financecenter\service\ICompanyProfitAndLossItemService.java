package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.financecenter.dto.CompanyProfitAndLossItemDto;
import com.get.financecenter.vo.CompanyProfitAndLossItemVo;
import java.util.List;
import javax.validation.Valid;

/**
 * 公司利润表项目服务接口
 */
public interface ICompanyProfitAndLossItemService {

    void delete(Long id);

    List<CompanyProfitAndLossItemVo> getCompanyProfitAndLossItem(@Valid CompanyProfitAndLossItemDto companyProfitAndLossItemDto, Page page);

    List<Object> getShowMode();

    Integer save(CompanyProfitAndLossItemDto companyProfitAndLossItemDto);

    Integer updateById(CompanyProfitAndLossItemDto companyProfitAndLossItemDto);

    void sort(List<Long> ids);

    void copyTemplateToProject(Long fkCompanyId);

    void movingOrder(Integer start, Integer end);
}

