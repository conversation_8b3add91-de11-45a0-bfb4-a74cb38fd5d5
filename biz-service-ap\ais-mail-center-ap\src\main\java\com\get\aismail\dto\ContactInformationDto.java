package com.get.aismail.dto;

import com.get.aismail.entity.AreaCountry;
import lombok.Data;

@Data
public class ContactInformationDto {
    // 移动电话区号
    private AreaCountry mobileAreaCode;
    // 移动电话
    private String mobile;
    // 固定电话区号
    private AreaCountry areaCodeValue;
    // 固定电话
    private String tel;
    // 电子邮箱
    private String email;
    // 现居住地址邮编
    private String zipcode;
    // 现居住地址国家
    private AreaCountryDto fkAreaCountryId;
    // 现居住地址省份
    private AreaStateDto fkAreaStateId;
    // 现居住地址市
    private AreaCityDto fkAreaCityId;
    // 现居住联系地址
    private String contactAddress;

    public ContactInformationDto() {
    }

    public ContactInformationDto(AreaCountry mobileAreaCode, String mobile, AreaCountry areaCodeValue, String tel, String email, String zipcode, AreaCountryDto fkAreaCountryId, AreaStateDto fkAreaStateId, AreaCityDto fkAreaCityId, String contactAddress) {
        this.mobileAreaCode = mobileAreaCode;
        this.mobile = mobile;
        this.areaCodeValue = areaCodeValue;
        this.tel = tel;
        this.email = email;
        this.zipcode = zipcode;
        this.fkAreaCountryId = fkAreaCountryId;
        this.fkAreaStateId = fkAreaStateId;
        this.fkAreaCityId = fkAreaCityId;
        this.contactAddress = contactAddress;
    }
}
