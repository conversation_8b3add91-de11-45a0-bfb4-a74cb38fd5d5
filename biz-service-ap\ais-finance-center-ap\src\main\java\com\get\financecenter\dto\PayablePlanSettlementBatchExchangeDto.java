package com.get.financecenter.dto;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class PayablePlanSettlementBatchExchangeDto  extends BaseEntity implements Serializable {
    /**
     * 财务结算汇总批次号
     */
    @ApiModelProperty(value = "财务结算汇总批次号")
    private String numSettlementBatch;

    /**
     * 目标类型关键字，枚举：m_student_offer留学申请方案/m_student_insurance留学保险/m_student_accommodation留学住宿
     */
    @ApiModelProperty(value = "目标类型关键字，枚举：m_student_offer留学申请方案/m_student_insurance留学保险/m_student_accommodation留学住宿")
    private String fkTypeKey;

    /**
     * 学生代理Id
     */
    @ApiModelProperty(value = "学生代理Id")
    private Long fkAgentId;

    /**
     * 币种编号（原币种）
     */
    @ApiModelProperty(value = "币种编号（原币种）")
    private String fkCurrencyTypeNum;

    /**
     * 币种编号（兑换币种）
     */
    @ApiModelProperty(value = "币种编号（兑换币种）")
    private String fkCurrencyTypeNumExchange;

    /**
     * 兑换汇率
     */
    @ApiModelProperty(value = "兑换汇率")
    private BigDecimal exchangeRate;

    /**
     * 手续费
     */
    @ApiModelProperty(value = "手续费")
    private BigDecimal serviceFee;


    @ApiModelProperty(value = "状态： 1：第一弹框汇率修改(汇率全删全增) 2：第二弹框汇率修改（只会删除港元兑人民币汇率） ")
    private int state;

}