package com.get.partnercenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author:<PERSON>
 * @Date: 2025/2/24  16:47
 * @Version 1.0
 */
@Data
public class MailLogDto {

    @ApiModelProperty(value = "发件人登录账号")
    private String loginId;

    @ApiModelProperty(value = "代理名称/收件人名称")
    private String keyword;

    @ApiModelProperty("操作类型：1新建/2重置密码/3学生确认提醒")
    private Integer optType;

}
