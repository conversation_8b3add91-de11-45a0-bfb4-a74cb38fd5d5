package com.get.platformconfigcenter.dao.appmso;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.AgentRecommend;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("appmsodb")
public interface AgentRecommendMapper extends BaseMapper<AgentRecommend> {
//    int insert(AgentRecommend record);
//
//    int insertSelective(AgentRecommend record);
//
//    int updateByPrimaryKeySelective(AgentRecommend record);
//
//    int updateByPrimaryKey(AgentRecommend record);
//
//    /**
//     * 获取最大排序值
//     */
//    Integer getMaxViewOrder();
}