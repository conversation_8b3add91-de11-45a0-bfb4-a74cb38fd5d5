<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.ContractFormulaMapper">
  <insert id="insert" parameterType="com.get.institutioncenter.entity.ContractFormula" keyProperty="id" useGeneratedKeys="true">
    insert into m_contract_formula (id, fk_institution_provider_id, formula_type,
                                    title, count_type, count_vale_min,
                                    count_vale_max, condition_type, start_time,
                                    end_time, fk_currency_type_num, fk_currency_type_num_ag,
                                    limit_amount, limit_amount_ag, remark,
                                    view_order, is_active, id_gea,
                                    id_iae, gmt_create, gmt_create_user,
                                    gmt_modified, gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkInstitutionProviderId,jdbcType=BIGINT}, #{formulaType,jdbcType=INTEGER},
            #{title,jdbcType=VARCHAR}, #{countType,jdbcType=INTEGER}, #{countValeMin,jdbcType=DECIMAL},
            #{countValeMax,jdbcType=DECIMAL}, #{conditionType,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP},
            #{endTime,jdbcType=TIMESTAMP}, #{fkCurrencyTypeNum,jdbcType=VARCHAR}, #{fkCurrencyTypeNumAg,jdbcType=VARCHAR},
            #{limitAmount,jdbcType=DECIMAL}, #{limitAmountAg,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR},
            #{viewOrder,jdbcType=INTEGER}, #{isActive,jdbcType=BIT}, #{idGea,jdbcType=VARCHAR},
            #{idIae,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR},
            #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.ContractFormula" keyProperty="id" useGeneratedKeys="true">
    insert into m_contract_formula
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkInstitutionProviderId != null">
        fk_institution_provider_id,
      </if>
      <if test="formulaType != null">
        formula_type,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="countType != null">
        count_type,
      </if>
      <if test="countValeMin != null">
        count_vale_min,
      </if>
      <if test="countValeMax != null">
        count_vale_max,
      </if>
      <if test="conditionType != null">
        condition_type,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="fkCurrencyTypeNum != null">
        fk_currency_type_num,
      </if>
      <if test="fkCurrencyTypeNumAg != null">
        fk_currency_type_num_ag,
      </if>
      <if test="limitAmount != null">
        limit_amount,
      </if>
      <if test="limitAmountAg != null">
        limit_amount_ag,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="isActive != null">
        is_active,
      </if>
      <if test="idGea != null">
        id_gea,
      </if>
      <if test="idIae != null">
        id_iae,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionProviderId != null">
        #{fkInstitutionProviderId,jdbcType=BIGINT},
      </if>
      <if test="formulaType != null">
        #{formulaType,jdbcType=INTEGER},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="countType != null">
        #{countType,jdbcType=INTEGER},
      </if>
      <if test="countValeMin != null">
        #{countValeMin,jdbcType=DECIMAL},
      </if>
      <if test="countValeMax != null">
        #{countValeMax,jdbcType=DECIMAL},
      </if>
      <if test="conditionType != null">
        #{conditionType,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="fkCurrencyTypeNum != null">
        #{fkCurrencyTypeNum,jdbcType=VARCHAR},
      </if>
      <if test="fkCurrencyTypeNumAg != null">
        #{fkCurrencyTypeNumAg,jdbcType=VARCHAR},
      </if>
      <if test="limitAmount != null">
        #{limitAmount,jdbcType=DECIMAL},
      </if>
      <if test="limitAmountAg != null">
        #{limitAmountAg,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="isActive != null">
        #{isActive,jdbcType=BIT},
      </if>
      <if test="idGea != null">
        #{idGea,jdbcType=VARCHAR},
      </if>
      <if test="idIae != null">
        #{idIae,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select
      IFNULL(max(view_order)+1,0) view_order
    from
      m_contract_formula
    where
      fk_institution_provider_id = #{institutionProviderId}
  </select>
  <select id="getContractFormulasByOfferItem" resultType="com.get.institutioncenter.entity.ContractFormula">
    select cf.* from m_contract_formula cf
    -- 学生公司-合同公式公司 fk_company_id 学生所在公司Id
    <if test="companyId != null">
      inner join (select * from r_contract_formula_company where fk_company_id=#{companyId})  cfc on cf.id = cfc.fk_contract_formula_id
    </if>
    -- 课程
    <if test="studentOfferItem.fkInstitutionCourseId != null">
      INNER JOIN (SELECT cf11.* FROM m_contract_formula AS cf11 LEFT JOIN  r_contract_formula_institution_course AS cfic ON cf11.id = cfic.fk_contract_formula_id
      WHERE cfic.fk_institution_course_id=#{studentOfferItem.fkInstitutionCourseId} OR cfic.fk_institution_course_id IS NULL)cf12 ON cf12.id=cf.id
    </if>
    -- 合同公式 学生来源国家/区域  areaCountryId 学生所在国家Id
    <if test="areaCountryId != null">
      INNER JOIN (SELECT cf1.* FROM m_contract_formula AS cf1 LEFT JOIN  r_contract_formula_area_country_student AS cfacs ON cf1.id = cfacs.fk_contract_formula_id
      WHERE cfacs.fk_area_country_id=#{areaCountryId} OR cfacs.fk_area_country_id IS NULL)cf2 ON cf2.id=cf.id
    </if>
    -- 合同公式 国家/区域 fk_area_country_id 学生计划国家id
    <if test="studentOfferItem.fkAreaCountryId != null">
      INNER JOIN (SELECT cf3.* FROM `m_contract_formula` AS cf3 LEFT JOIN  r_contract_formula_area_country AS cfac ON cf3.id = cfac.fk_contract_formula_id
      WHERE cfac.fk_area_country_id=#{studentOfferItem.fkAreaCountryId} OR cfac.fk_area_country_id IS NULL)cf4 ON cf4.id=cf.id
    </if>
    -- 课程类型
    <if test="studentOfferItem.fkInstitutionCourseId != null">
      INNER JOIN (SELECT cf5.* FROM m_contract_formula AS cf5 LEFT JOIN  r_contract_formula_course_type AS cfct ON cf5.id = cfct.fk_contract_formula_id
      WHERE cfct.fk_course_type_id IN
      -- 根据课程id 获取对应的课程类型 ic.id 课程id
      (SELECT fk_course_type_id FROM r_institution_course_type
      WHERE fk_institution_course_id = #{studentOfferItem.fkInstitutionCourseId}) OR cfct.fk_course_type_id IS NULL GROUP BY cf5.id )cf6 ON cf.id = cf6.id
    </if>
    -- 课程等级 fk_institution_course_id 课程id
    <if test="studentOfferItem.fkInstitutionCourseId != null">
      INNER JOIN (SELECT cf7.* FROM m_contract_formula AS cf7 LEFT JOIN r_contract_formula_major_level AS cfml ON cf7.id=cfml.fk_contract_formula_id
      WHERE cfml.fk_major_level_id IN (SELECT fk_major_level_id
      FROM r_institution_course_major_level
      WHERE fk_institution_course_id = #{studentOfferItem.fkInstitutionCourseId}) OR fk_major_level_id IS NULL)cf8 ON cf.id=cf8.id
    </if>
    -- 合同公式学校
    <if test="studentOfferItem.fkInstitutionId != null">
      INNER JOIN (SELECT cf9.* FROM `m_contract_formula` AS cf9 LEFT JOIN  r_contract_formula_institution AS cfi ON cf9.id = cfi.fk_contract_formula_id
      WHERE cfi.fk_institution_id = #{studentOfferItem.fkInstitutionId} OR cfi.fk_institution_id IS NULL)cf10 ON cf10.id=cf.id
    </if>
    -- 学院  fk_institution_course_id 课程id
    <if test="studentOfferItem.fkInstitutionCourseId != null">
      INNER JOIN (SELECT cf13.* FROM m_contract_formula AS cf13 LEFT JOIN r_contract_formula_institution_faculty AS cfif ON cf13.id=cfif.fk_contract_formula_id
      WHERE cfif.fk_institution_faculty_id IN (SELECT fk_institution_faculty_id
      FROM r_institution_course_faculty
      WHERE fk_institution_course_id = #{studentOfferItem.fkInstitutionCourseId}) OR cfif.fk_institution_faculty_id IS NULL)cf14 ON cf.id=cf14.id
    </if>
    -- 校区
    <if test="studentOfferItem.fkInstitutionZoneId != null">
      INNER JOIN (SELECT cf15.* FROM m_contract_formula AS cf15 LEFT JOIN r_contract_formula_institution_zone AS cfiz ON cf15.id=cfiz.fk_contract_formula_id
      WHERE cfiz.fk_institution_zone_id = #{studentOfferItem.fkInstitutionZoneId} OR cfiz.fk_institution_zone_id IS NULL)cf16 ON cf.id=cf16.id
    </if>
    <if test="studentOfferItem.fkInstitutionChannelId != null">
      INNER JOIN r_contract_formula_institution_channel as cfic ON cfic.fk_contract_formula_id = cf.id AND cfic.fk_institution_channel_id = #{studentOfferItem.fkInstitutionChannelId}
    </if>

    where 1=1
    <if test="studentOfferItem.fkInstitutionProviderId!=null">
      and cf.fk_institution_provider_id=#{studentOfferItem.fkInstitutionProviderId}
    </if>
    and  cf.is_active = 1 and (cf.start_time <![CDATA[ <= ]]> now() or cf.start_time is null)
    and (cf.end_time <![CDATA[ >= ]]> now() or cf.end_time is null)
    group by cf.id

  </select>

  <select id="checkProviderInfoIsEmptyByProviderId"  resultType="java.lang.Boolean">
    select IFNULL(max(id),0) id from m_contract_formula where fk_institution_provider_id = #{providerId}  LIMIT 1
  </select>


  <select id="getContractFormulas" resultType="com.get.institutioncenter.entity.ContractFormula">
    SELECT
    mcf.*
    FROM
      m_contract_formula AS mcf
    WHERE mcf.fk_institution_provider_id = #{contractFormulaDto.fkInstitutionProviderId}
    AND EXISTS (
    SELECT 1 FROM r_contract_formula_company AS rcfc WHERE rcfc.fk_contract_formula_id = mcf.id
    and rcfc.fk_company_id IN
    <foreach collection="companyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
      #{fkCompanyId}
    </foreach>

    )
      <if test="contractFormulaDto.keyWord != null and contractFormulaDto.keyWord != '' ">
        AND (mcf.title LIKE CONCAT('%', #{contractFormulaDto.keyWord}, '%') OR mcf.remark LIKE CONCAT('%', #{contractFormulaDto.keyWord}, '%'))
      </if>
    ORDER BY
      mcf.view_order
  </select>
</mapper>