package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.vo.*;
import com.get.salecenter.entity.ReceivablePlan;
import com.get.salecenter.entity.Student;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.dto.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/11/19
 * @TIME: 15:54
 * @Description:
 **/
public interface IReceivablePlanService extends IService<ReceivablePlan> {

    /**
     * @return java.util.List<com.get.salecenter.vo.ReceivablePlanVo>
     * @Description: 列表数据
     * @Param [receivablePlanDto, page]
     * <AUTHOR>
     */
    List<ReceivablePlanVo> getReceivablePlan(ReceivablePlanDto receivablePlanDto, Page page, String[] times);


    void saveBatch(List<ReceivablePlan> receivablePlanList);

    /**
     * @Description: 提供商应收汇总统计明细
     * @Author: Jerry
     * @Date:11:38 2021/11/22
     */
    List<ReceivablePlanVo> institutionProviderReceivableSumDetail(InstitutionProviderReceivableSumDetailDto institutionProviderReceivableSumDetailDto,
                                                                  Page page);

    /**
     * 学生应收明细
     *
     * @param studentReceivableSumDetailDto
     * @param page
     * @return
     */
    List<ReceivablePlanVo> studentReceivableSumDetail(StudentReceivableSumDetailDto studentReceivableSumDetailDto, Page page);

    /**
     * @return java.lang.Long
     * @Description:
     * @Param [receivablePlanDto]
     * <AUTHOR>
     */
    Long addReceivablePlan(ReceivablePlanDto receivablePlanDto);


    /**
     * @return com.get.salecenter.vo.ReceivablePlanVo
     * @Description: 修改
     * @Param [receivableReasonVo]
     * <AUTHOR>
     */
    ReceivablePlanVo updateReceivablePlan(ReceivablePlanDto receivableReasonVo);


    /**
     * 获取应收计划收款时间列表
     * @param receivablePlanId
     * @return
     */
    List<ReceivablePlanDateVo> getReceivablePlanDateList(Long receivablePlanId);

    /**
     * 添加应收计划收款时间
     * @param receivablePlanId
     * @param receivablePlanDate
     * @return
     */
    SaveResponseBo addReceivablePlanDate(Long receivablePlanId, String receivablePlanDate);

    /**
     * 批量添加应收计划收款时间
     *
     * @Date 16:53 2023/11/13
     * <AUTHOR>
     */
    void batchAddReceivablePlanDate(List<Long> receivablePlanIdList, String receivablePlanDate);

    /**
     * 更新应收计划收款时间
     * @param fkReceivablePlanDateId
     * @param receivablePlanDate
     * @return
     */
    SaveResponseBo updateReceivablePlanDate(Long fkReceivablePlanDateId, String receivablePlanDate);

    /**
     * 删除应收计划收款时间
     * @param fkReceivablePlanDateId
     * @return
     */
    ResponseBo deleteReceivablePlanDate(Long fkReceivablePlanDateId);

    /**
     * @return com.get.salecenter.vo.ReceivablePlanVo
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    ReceivablePlanVo findReceivablePlan(Long id);


    ARAPDto getARAPInformation(Long itemId, String typeKey);


    /**
     * 获取折合币种编号
     * @param id
     * @return
     */
    String getCurrencyNum(Long id);

    /**
     * feign调用ids详情
     *
     * @param ids
     * @return
     */
    List<ReceivablePlanVo> getReceivablePlansDetail(Set<Long> ids);

    /**
     * @return void
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);


    /**
     * @return java.util.List<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 添加附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> addMedia(List<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * @return java.util.List<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 获取附件
     * @Param [andAttachedVo, page]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> getMedia(MediaAndAttachedDto andAttachedVo, Page page);

    /**
     * @return void
     * @Description: 关闭应收计划
     * @Param [id, status,syncPay]
     * <AUTHOR>
     */
    void unableReceivable(Long id, Long status,Boolean syncPay);


    /**
     * 批量作废应收计划
     * @param receivableIds
     * @return
     */
    ResponseBo<StudentOfferItemVo> batchUnableReceivablePlan(Set<Long> receivableIds);


    /**
     * @return java.util.List<java.lang.Long>
     * @Description: feign
     * @Param [typeKey, targetId]
     * <AUTHOR>
     */
    List<Long> getReceivablePlanId(String typeKey, Long targetId);

    Map<Long,List<Long>> getReceivablePlanIds(String typeKey, Set<Long> targetIds);
    /**
     * @return void
     * @Description: 生成应收计划
     * @Param [studentOfferItem]
     * <AUTHOR>
     **/
    String generateReceivablePlan(StudentOfferItem studentOfferItem, boolean failureFlag);

    /**
     * @return
     * @Description：feign 根据应收计划id查询应收计划应收金额
     * @Param
     * @Date 14:10 2021/4/23
     * <AUTHOR>
     */
    BigDecimal getReceivablePlanAmountById(Long id);

    /**
     * 根据学习计划获取学生信息
     *
     * @Date 16:05 2021/7/16
     * <AUTHOR>
     */
    Student getStudent(StudentOfferItem studentOfferItem);

    /**
     * 根据ids获取学习计划
     *
     * @param fkReceivablePlanIds
     * @return
     */
    List<ReceivablePlanVo> getReceivablePlanDtosByIds(List<Long> fkReceivablePlanIds);


    /**
     * 根据fkProviderId获取应收计划列表
     *
     * @param fkProviderId
     * @return
     */
    List<ReceivablePlanVo> getReceivablePlanDtosByProviderId(Long fkProviderId);

    /**
     * 根据ids获取应收计划列表
     *
     * @param ids
     * @return
     * @
     */
    List<ReceivablePlanVo> getReceivablePlanByIds(Set<Long> ids);

    /**
     * 删除记录
     *
     * @return
     */
    void deleteReceivablePlanByItemId(Long id);

    /**
     * 导出应收计划Excel
     *
     * @param response
     * @param receivablePlanNewDto
     */
    void exportReceivablePlanExcel(HttpServletResponse response, ReceivablePlanNewDto receivablePlanNewDto);

    /**
     * 根据应付计划信息获取对应应收计划下拉框数据
     *
     * @param fkTypeKey
     * @param fkTypeTargetId
     * @Date 11:03 2022/4/6
     * <AUTHOR>
     */
    List<BaseSelectEntity> getReceivablePlanByPayablePlanInfo(String fkTypeKey, Long fkTypeTargetId);

    List<ReceivablePlanNewVo> getReceivablePlanNew(ReceivablePlanNewDto data, SearchBean<ReceivablePlanNewDto> page, String[] times);


    /**
     *  根据提供商 && 发展业务对象查询应收计划下拉数据
     */
    List<ReceivablePlanVo> doGetInvoiceNewReceivablePlan(ReceivablePlanBatchDto receivablePlanBatchDto, String[] times);

    /**
     * 根据提供商 && 发展业务对象查询应收计划列表分页信息
     * @param receivablePlanBatchDto
     * @param page
     * @return
     */
    ResponseBo doGetInvoiceRPPaginationInfo(ReceivablePlanBatchDto receivablePlanBatchDto, Page page);
    /**
     *
     * @param ids
     * @return
     */
    List<ReceivablePlanVo> getReceivablePlansDetailNew(Set<Long> ids);


    Boolean batchUpdateByIds(List<ReceivablePlan> receivablePlans);

    List<ReceivablePlanVo> packResult(List<ReceivablePlanVo> receivablePlanVos);

    /**
     * 获取应收计划id 和公司名称
     * @param typeKey
     * @param targetId
     * @return
     */
    List<BaseSelectEntity> getPlanIdsAndCompanyName(String typeKey,Long targetId,Long receiptFormId);

    Page getReceiptFormReceivablePlanPaginationInfo(String tableName, Long channelId, Long receiptFormId, String fkTypeKey,Page page);


    /**
     * 获取最新的三条学费
     * @return
     * @param fkCompanyId
     */
    List<String> getRePlanTheLatestThreeTuitionFees(Long fkCompanyId);

    /**
     * 留学申请业务，学费佣金到账后自动关绑入学Enrolled步骤
     * @param receivablePlanIds
     */
    Boolean autoRelationReceipten(Set<Long> receivablePlanIds);


    /**
     * 应收应付对冲
     * @param arApHedgingDto
     * @return
     */
    SaveResponseBo arApHedging(ArApHedgingDto arApHedgingDto);

    /**
     * 获取应收金额信息
     * @param receivablePlanIds
     * @return
     */
    List<ReceivablePlanVo> getReceivableAmountInfo(Set<Long> receivablePlanIds);

    /**
     * 获取传入固定排序的应收计划
     * @param planIds
     * @param invoiceAmountSort
     * @param receiptAmountSort
     * @param studentName
     * @param receiptFormId
     * @return
     */
    List<ReceivablePlanVo> getReceivablePlansBySort(Set<Long> planIds, Boolean invoiceAmountSort, Boolean receiptAmountSort, String studentName, Long receiptFormId);


    /**
     * 提供商收款单获取应收计划下拉列表
     *
     * @param providerId
     * @param receiptFormId
     * @return
     */
    List<BaseSelectEntity> getReceivablePlanSelectByProvider(Long providerId, Long receiptFormId);

}
