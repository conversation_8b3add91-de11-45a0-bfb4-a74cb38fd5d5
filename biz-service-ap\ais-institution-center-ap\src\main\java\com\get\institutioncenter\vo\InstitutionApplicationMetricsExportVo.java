package com.get.institutioncenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class InstitutionApplicationMetricsExportVo {
    @ApiModelProperty(value = "国家名称")
    private String countryName;

    //学校名称
    @ApiModelProperty(value = "学校名称")
    private String institutionName;


    //学校类型
    @ApiModelProperty(value = "学校类型")
    private String institutionTypeName;
    //QS排名
    @ApiModelProperty(value = "QS排名")
    private Integer qs;
    //提交完成数

    @ApiModelProperty(value = "提交完成数")
    private Integer submitted;
    //录取数

    @ApiModelProperty(value = "录取数")
    private Integer admitted;
    //已付押金数

    @ApiModelProperty(value = "已付押金数")
    private Integer deposited;
    //收到签证函数

    @ApiModelProperty(value = "收到签证函数")
    private  Integer cas;
    //成功入学数

    @ApiModelProperty(value = "获得签证数")
    private Integer receivedVisa;

    @ApiModelProperty(value = "成功入学数")
    private Integer enrolled;

    @ApiModelProperty(value = "准入学数")
    private Integer pendingEnrollmentCount;

    @ApiModelProperty(value = "后续课程数")
    private Integer followUpCourseCount;


}
