package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_agent_role_staff")
public class AgentRoleStaff extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;
    /**
     * 目标类型关键字，枚举：m_student_offer留学申请/m_student_insurance留学保险/m_student_accommodation留学住宿
     */
    @ApiModelProperty(value = "目标类型关键字，枚举：m_student_offer留学申请/m_student_insurance留学保险/m_student_accommodation留学住宿")
    @Column(name = "fk_type_key")
    private String fkTypeKey;
    /**
     * 代理Id
     */
    @ApiModelProperty(value = "代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 代理所在国家Id
     */
    @ApiModelProperty(value = "代理所在国家Id")
    @Column(name = "fk_area_country_id_agent")
    private Long fkAreaCountryIdAgent;
    /**
     * 代理所在州省Id
     */
    @ApiModelProperty(value = "代理所在州省Id")
    @Column(name = "fk_area_state_id_agent")
    private Long fkAreaStateIdAgent;
    /**
     * 学生项目角色Id
     */
    @ApiModelProperty(value = "学生项目角色Id")
    @Column(name = "fk_student_project_role_id")
    private Long fkStudentProjectRoleId;
    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;
    /**
     * 国家Id，可不选，不选为适合所有国家线
     */
    @ApiModelProperty(value = "国家Id，可不选，不选为适合所有国家线")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
}