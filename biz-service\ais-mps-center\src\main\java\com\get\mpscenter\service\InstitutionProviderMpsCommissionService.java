package com.get.mpscenter.service;


import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.service.GetService;
import com.get.mpscenter.vo.InstitutionProviderBingVo;
import com.get.mpscenter.vo.InstitutionProviderMpsCommissionVo;
import com.get.mpscenter.vo.KeyWordVo;
import com.get.mpscenter.entity.InstitutionProviderMpsCommission;
import com.get.mpscenter.dto.InstitutionProviderMpsCommissionDto;
import com.get.mpscenter.dto.KeyWordMajorLevelDto;
import com.get.mpscenter.dto.MpsCommissionDto;

import java.util.List;

public interface InstitutionProviderMpsCommissionService extends GetService<InstitutionProviderMpsCommission> {
    /**
     * 列表
     * @param data
     * @param page
     * @return
     */
    List<InstitutionProviderMpsCommissionVo> getCommissionList(InstitutionProviderMpsCommissionDto data, Page page);

    /**
     * 新增
     * @param mpsCommissionDto
     */
    Long add(MpsCommissionDto mpsCommissionDto);

    /**
     *  添加关键字
     * @param keyWordMajorLevelDto
     */
    void addKeyWord(KeyWordMajorLevelDto keyWordMajorLevelDto);

    /**
     * 获取提供商佣金列表
     * @param fkInstitutionId
     * @param fkInstitutionProviderId
     * @return
     */
    InstitutionProviderBingVo getProviderInstitutionList(Long fkInstitutionId, Long fkInstitutionProviderId);

    /**
     * 佣金详情
     * @param id
     * @return
     */
    InstitutionProviderMpsCommissionVo findCommissionById(Long id);

    /**
     * 修改
     * @param mpsCommissionDto
     * @return
     */
    InstitutionProviderMpsCommissionVo update(MpsCommissionDto mpsCommissionDto);

    /**
     * 获取关键字列表
     * @return
     */
    List<KeyWordVo> getKeyWordList();

    /**
     * 获取年份列表
     * @return
     */
    List<BaseSelectEntity> getYearList();

    /**
     * 获取学校提供商下拉
     * @param fkInstitutionId
     * @return
     */
    List<BaseSelectEntity> getInstitutionProviderList(Long fkInstitutionId);

    /**
     * 获取学校下拉
     * @param fkInstitutionProviderId
     * @return
     */
    List<BaseSelectEntity> getInstitutionList(Long fkInstitutionProviderId);

    /**
     * 拖拽
     * @param fkProviderMpsCommissionId
     * @param start
     * @param end
     */
    void movingOrder(Long fkProviderMpsCommissionId,Integer start,Integer end);

    /**
     * 复制
     * @param id
     * @return
     */
    InstitutionProviderMpsCommissionVo copyMpsCommission(Long id);

    /**
     * 修改激活状态
     * @param id
     * @param status
     */
    void updateActive(Long id, Boolean status);

    void delete(Long id);

    List<InstitutionProviderMpsCommissionVo> getUnActiveCommissionList(MpsCommissionDto mpsCommissionDto, Page page);
}
