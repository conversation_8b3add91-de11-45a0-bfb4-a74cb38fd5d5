package com.get.officecenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.officecenter.vo.AttendanceMachineDataVo;
import com.get.officecenter.vo.DingTalkAttendanceVo;
import com.get.officecenter.entity.ClockInData;
import com.get.officecenter.dto.ClockInDataListDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/5/10
 * @TIME: 10:59
 * @Description:
 **/
@Mapper
public interface ClockInDataMapper extends BaseMapper<ClockInData> {
    /**
     * 获取所有打卡记录
     * @return
     */
    List<ClockInData> getAllClockInData(@Param("clockInDataDto") ClockInDataListDto clockInDataVo);

    /**
     * 指定年月删除对应类型打卡数据
     * @param clockInTime
     * @param dataSource
     */
    void deleteClockInData(@Param("clockInTime") String clockInTime,
                           @Param("dataSource") Integer dataSource,
                           @Param("fkCompanyId") Long fkCompanyId);

    /**
     * 录入考勤机打卡数据
     */
    void importAttendanceMachineData(@Param("attendanceMachineDataVos") List<AttendanceMachineDataVo> attendanceMachineDataVos,
                                     @Param("fkCompanyId") Long fkCompanyId);

    /**
     * 录入钉钉打卡数据
     * @param dingTalkAttendanceVos
     */
    void importDingTalkAttendanceData(@Param("dingTalkAttendanceVos") List<DingTalkAttendanceVo> dingTalkAttendanceVos,
                                      @Param("fkCompanyId") Long fkCompanyId);

    /**
     * 获取打卡数据
     * @param startTime
     * @param endTime
     * @return
     */
    List<ClockInData> getClockInData(@Param("startTime") Date startTime,
                                     @Param("endTime") Date endTime,
                                     @Param("fkCompanyId") Long fkCompanyId);

}
