package com.get.schoolGateCenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2020/8/4
 * @TIME: 18:29
 * @Description: 职业意向DTO
 **/
@Data
public class ResumeIntentionVo extends BaseVoEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 所属简历Id
     */
    @NotNull(message = "简历id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "所属简历Id", required = true)
    private Long fkResumeId;
    /**
     * 期望职位
     */
    @ApiModelProperty(value = "期望职位")
    private String position;
    /**
     * 期望薪金
     */
    @ApiModelProperty(value = "期望薪金")
    private BigDecimal salary;
    /**
     * 期望工作地
     */
    @ApiModelProperty(value = "期望工作地")
    private String workPlace;
    /**
     * 期望入职时间
     */
    @ApiModelProperty(value = "期望入职时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date takeOffice;
    /**
     * 自我评价
     */
    @ApiModelProperty(value = "自我评价")
    private String selfEvaluation;
}
