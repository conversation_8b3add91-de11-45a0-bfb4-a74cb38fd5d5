package com.get.schoolGateCenter.dto;

import com.get.schoolGateCenter.entity.Template;
import com.get.schoolGateCenter.vo.MediaAndAttachedVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName: TemplateDto
 * @Author: Eric
 * @Date: 2023/3/2 18:44
 * @Version: 1.0
 */
@Data
public class TemplateDto extends Template {

    @ApiModelProperty(value = "图片文件")
    List<MediaAndAttachedDto> mediaAndAttachedDtos;

}
