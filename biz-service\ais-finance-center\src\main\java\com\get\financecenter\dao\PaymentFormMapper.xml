<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.PaymentFormMapper">
    <update id="updateFee">
        UPDATE m_payment_form set service_fee=service_fee + #{fee} where id=#{id}
    </update>

    <select id="getPayFormList" resultType="com.get.financecenter.vo.PaymentFormVo">
        SELECT
            p.service_fee AS payment_slip_fee,
            i.service_fee AS binding_fee,
            i.amount_exchange_rate as childAmountExchangeRate,
            p.*, i.*
        FROM
            m_payment_form p
        LEFT JOIN m_payment_form_item i ON p.id = i.fk_payment_form_id
        WHERE
            i.fk_payable_plan_id = #{planId}
    </select>

    <select id="getPayFormListByBindingStatus" resultType="com.get.financecenter.vo.PaymentFormVo">
        select a.* from (SELECT
        mpf.id,
        mpf.fk_company_id,
        mpf.fk_type_key,
        mpf.fk_type_target_id,
        mpf.fk_bank_account_id,
        mpf.fk_payment_fee_type_id,
        mpf.num_system,
        mpf.num_bank,
        mpf.payment_date,
        mpf.fk_currency_type_num,
        mpf.exchange_rate_hkd,
        mpf.amount_hkd,
        mpf.exchange_rate_rmb,
        mpf.amount_rmb,
        mpf.service_fee,
        mpf.summary,
        mpf.`status`,
        mpf.gmt_create,
        mpf.gmt_create_user,
        mpf.gmt_modified,
        mpf.gmt_modified_user,
        ( IFNULL(mpf.amount,0) + IFNULL(mpf.service_fee,0) ) AS amount,
        CAST(( IFNULL(sum(mpfi.amount_payment),0) + IFNULL(sum(mpfi.service_fee),0) ) AS DECIMAL(18,2)) AS amountPayment,
        CAST(( IFNULL(mpf.amount,0) + IFNULL(mpf.service_fee,0) + IFNULL(mpf.amount_exchange_rate,0)) AS DECIMAL (18, 2)) - CAST(( IFNULL(sum(mpfi.amount_payment),0) + IFNULL(sum(mpfi.service_fee),0) + IFNULL(sum(mpfi.amount_exchange_rate),0) ) AS DECIMAL (18, 2)) AS diffAmount
        FROM
        m_payment_form mpf
        left join m_payment_form_item mpfi on mpf.id = mpfi.fk_payment_form_id
        group by mpf.id) a
        <where>
            <if test="bindingStatus != null and bindingStatus == 0">
                and a.amount = abs(a.diffAmount)  #未绑定：差额等于付款金额;abs函数将负数转为正数
            </if>
            <if test="bindingStatus != null and bindingStatus == 1">
                and a.amount != abs(a.diffAmount)  and a.diffAmount <![CDATA[< ]]> 0        #绑定部分：差额小于0
            </if>
            <if test="bindingStatus != null and bindingStatus == 2">
                and a.diffAmount = 0        #绑定完成：差额等于0
            </if>
            <if test="fkPayFormIds != null and fkPayFormIds.size()>0">
                and a.id in
                <foreach collection="fkPayFormIds" item="fkPayFormId" index="index" open="(" separator="," close=")">
                    #{fkPayFormId}
                </foreach>
            </if>
        </where>
        order by a.gmt_create desc
    </select>
    <select id="getPayFormListFeignByPlanIds" resultType="com.get.financecenter.vo.PaymentFormVo">
        SELECT *,i.amount_exchange_rate as childAmountExchangeRate from m_payment_form p
        left join m_payment_form_item i on p.id =i.fk_payment_form_id
        where i.fk_payable_plan_id IN
        <foreach collection="planIds" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getPayMentFormCountByTypeId" resultType="java.lang.Integer">
        SELECT COUNT(0) FROM m_payment_form WHERE fk_payment_fee_type_id=#{fkPaymentFeeTypeId}
    </select>
    <select id="getPayFormListByNumSettlementBatch" resultType="com.get.financecenter.entity.PaymentForm">
        SELECT
            mpf.*
        FROM
            ais_finance_center.r_payable_plan_settlement_installment AS rppsi
                INNER JOIN ais_finance_center.m_payment_form_item AS mpfi ON mpfi.id = rppsi.fk_payment_form_item_id
                INNER JOIN ais_finance_center.m_payment_form AS mpf ON mpf.id = mpfi.fk_payment_form_id
                INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.id = mpfi.fk_payable_plan_id
        WHERE rppsi.num_settlement_batch = #{numSettlementBatch}
    </select>
    <select id="getCurrencyByFormIds" resultType="com.get.salecenter.vo.SelItem">
        SELECT
            p.id as key_id,
            CONCAT(u.type_name,'（',u.num,'）') as val
        FROM
            m_payment_form p
        LEFT JOIN u_currency_type u ON u.num = p.fk_currency_type_num
        WHERE
            p.id in
            <foreach collection="formIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
    </select>
</mapper>