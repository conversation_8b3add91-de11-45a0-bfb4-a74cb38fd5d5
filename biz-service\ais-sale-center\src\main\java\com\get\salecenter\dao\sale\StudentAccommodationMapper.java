package com.get.salecenter.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.AccommodationPayFormDetailVo;
import com.get.salecenter.vo.AgentsBindingVo;
import com.get.salecenter.vo.StudentAccommodationVo;
import com.get.salecenter.entity.StudentAccommodation;
import com.get.salecenter.dto.StudentAccommodationDto;
import com.get.salecenter.dto.query.AccommodationSummaryQueryDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface StudentAccommodationMapper extends GetMapper<StudentAccommodation> {
    int insert(StudentAccommodation record);

    int insertSelective(StudentAccommodation record);

    List<StudentAccommodationVo> getStudentAccommodationList(IPage<StudentAccommodationVo> iPage, StudentAccommodationDto studentAccommodation);


    @DS("saledb-doris")
    List<StudentAccommodationVo> getStudentAccommodationSummary(IPage<StudentAccommodationVo> iPage,
                                                                @Param("studentAccommodation") AccommodationSummaryQueryDto studentAccommodation,
                                                                @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                                @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                                @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                                @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    Long verifyAccPermissions(@Param("accId") Long accId,@Param("staffFollowerIds") List<Long> staffFollowerIds);

    String getNumById(@Param("fkTableId") Long fkTableId);

    /**
     * 下拉框
     *
     * @param studentId
     * @return
     */
    List<BaseSelectEntity> getStudentAccommodationSelect(@Param("studentId") Long studentId);

    List<StudentAccommodation> getStudentAccommodations(@Param("studentId") Long studentId, @Param("companyIds") List<Long> companyIds, @Param("keyWord") String keyWord);

    List<StudentAccommodationVo> getCompanyIdsByAccommodationIds(@Param("accommodationIds") List<Long> accommodationIds);

    List<Long> getAccommodationIdsByStudentId(@Param("studentId") Long studentId);

    List<AgentsBindingVo> getStudentAccommodationAgent(@Param("fkStudentNum")String fkStudentNum);

    Long queryAccommodationAgentId(@Param("targetId") Long targetId);


    Long getIdByTargetId(@Param("targetId") Long targetId);

    List<AccommodationPayFormDetailVo> getPaidAmountByIds(@Param("itemIds") Set<Long> itemIds);
}