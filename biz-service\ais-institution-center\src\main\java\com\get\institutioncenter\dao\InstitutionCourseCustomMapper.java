package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.InstitutionCourseCustom;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface InstitutionCourseCustomMapper extends BaseMapper<InstitutionCourseCustom> {
    int insert(InstitutionCourseCustom record);

    int insertSelective(InstitutionCourseCustom record);

    int updateByPrimaryKeySelective(InstitutionCourseCustom record);

    int updateByPrimaryKey(InstitutionCourseCustom record);
}