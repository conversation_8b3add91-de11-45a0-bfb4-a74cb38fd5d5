package com.get.permissioncenter.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.StyleSet;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFSheet;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class FileUtils {
    public static <T> Map<String, String> getFileMapIgnoreSomeField(Class<T> beanType, Collection collection) {
        Map<String, String> fileMap = new LinkedHashMap<>(7);
        Field[] fields = ReflectUtil.getFields(beanType);
        for (Field field : fields) {
            ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
            if (annotation != null&&!collection.contains(field.getName())) {
                fileMap.put(annotation.value(), field.getName());
            }
        }
        return fileMap;
    }
    public static void setSizeColumn(Sheet sheet, int size) {
        for (int columnNum = 0; columnNum < size; columnNum++) {
            int columnWidth = sheet.getColumnWidth(columnNum) / 256;
            for (int rowNum = 0; rowNum < sheet.getLastRowNum(); rowNum++) {
                Row currentRow;
                //当前行未被使用过
                if (sheet.getRow(rowNum) == null) {
                    currentRow = sheet.createRow(rowNum);
                } else {
                    currentRow = sheet.getRow(rowNum);
                }
                if (currentRow.getCell(columnNum) != null) {
                    Cell currentCell = currentRow.getCell(columnNum);
                    if (currentCell.getCellType() == CellType.STRING) {
                        int length = currentCell.getStringCellValue().getBytes().length;
                        if (columnWidth < length) {
                            columnWidth = length;
                        }
                    }
                }
            }
            sheet.setColumnWidth(columnNum, columnWidth * 600);
        }
    }
    public static BigExcelWriter setExcelStyleNotWrapText(String fileName, int size) {
        //大文件导出
        BigExcelWriter writer = (BigExcelWriter) ExcelUtil.getBigWriter();
        writer.renameSheet(fileName);

        SXSSFSheet sheet = (SXSSFSheet) writer.getSheet();
        setSizeColumn(sheet, size);
//        sheet.trackAllColumnsForAutoSizing();
        //样式
        StyleSet styleSet = writer.getStyleSet();
        CellStyle headCellStyle = styleSet.getHeadCellStyle();
        //字体样式
        Font font = writer.createFont();
        font.setBold(true);
        font.setFontName("微软雅黑");
        headCellStyle.setFont(font);
        headCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        CellStyle cellStyle = styleSet.getCellStyle();
        //不自动换行
        cellStyle.setWrapText(false);
        return writer;
    }
    public static <T> void exportExcel(HttpServletResponse response, List<T> exportData, String fileName, Map<String, String> fileMap) {
        if (CollectionUtil.isEmpty(exportData) || StrUtil.isEmpty(fileName)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
            log.info("filename is null!!!");
        }
//        Map<String, String> fileMap = getFileMap(beanType);
        BigExcelWriter writer = setExcelStyleNotWrapText(fileName, fileMap.size());

        //设置字段
        for (Map.Entry<String, String> field : fileMap.entrySet()) {
            writer.addHeaderAlias(field.getValue(), field.getKey());
        }
        writer.setOnlyAlias(true);
        writer.write(exportData, true);
        //大文件导出
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
        ServletOutputStream out = null;
        try {
            out = response.getOutputStream();
            writer.flush(out, true);
        } catch (IOException e) {
//            throw new YServerException(LocaleMessageUtils.getMessage("file_export_fail"));
            e.printStackTrace();
        } finally {
            IoUtil.close(writer);
            IoUtil.close(out);
        }
    }

}
