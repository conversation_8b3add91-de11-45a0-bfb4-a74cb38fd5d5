package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.salecenter.dto.ConventionRegistrationFastGenerationDto;
import com.get.salecenter.dto.ConventionRegistrationRosterDto;
import com.get.salecenter.vo.BoothVo;
import com.get.salecenter.vo.ConventionRegistrationVo;
import com.get.salecenter.vo.RegistrationSponsorVo;
import com.get.salecenter.entity.ConventionRegistration;
import com.get.salecenter.dto.ConventionRegistrationDto;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/3 10:57
 * @verison: 1.0
 * @description: 峰会报名管理业务接口
 */
public interface IConventionRegistrationService {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    ConventionRegistrationVo findConventionRegistrationById(Long id);

    /**
     * 批量新增
     *
     * @param conventionRegistrationDtos
     */
    void batchAdd(List<ConventionRegistrationDto> conventionRegistrationDtos);

    /**
     * 新增
     *
     * @param
     */
    void add(ConventionRegistrationRosterDto conventionRegistrationRosterDto);



    /**
     * @return void
     * @Description :年度会议注册的时候用
     * @Param [conventionRegistrationDtos]
     * <AUTHOR>
     */
    void batchAdd2(List<ConventionRegistrationDto> conventionRegistrationDtos);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 修改
     *
     * @param conventionRegistrationDto
     * @return
     */
    ConventionRegistrationVo updateConventionRegistration(ConventionRegistrationDto conventionRegistrationDto);

    /**
     * 列表
     *
     * @param conventionRegistrationDto
     * @param page
     * @return
     */
    List<ConventionRegistrationVo> getConventionRegistrations(ConventionRegistrationDto conventionRegistrationDto, Page page);

    /**
     * 所属报名名册下拉框数据
     *
     * @param conventionId
     * @return
     */
    List<ConventionRegistrationVo> getConventionRegistrationList(Long conventionId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :根据展位名称 模糊查询对应的报名ids
     * @Param [boothName]
     * <AUTHOR>
     */
    List<Long> getRegistrationIdsByName(String boothName);

    /**
     * @return void
     * @Description: 状态修改
     * @Param [id, status]
     * <AUTHOR>
     */
    void updateStatus(Long id, Integer status);

    /**
     * @return java.util.List<com.get.salecenter.vo.ConventionRegistrationVo>
     * @Description :根据回执码查找报名对象list
     * @Param [conventionId, receiptCode]
     * <AUTHOR>
     */
    List<ConventionRegistrationVo> getConventionRegistrationDto(Long conventionId, String receiptCode);

    /**
     * @return void
     * @Description :通过赞助商回执码删除
     * @Param [conventionId, receiptCode]
     * <AUTHOR>
     */
    void deleteByReceiptCode(Long conventionId, String receiptCode);

    /**
     * @return java.util.List<java.lang.String>
     * @Description :获取该峰会下已选全部展位
     * @Param [conventionId]
     * <AUTHOR>
     */
    List<String> getBoothIndex(Long conventionId);

    /**
     * @return java.lang.Boolean
     * @Description :展位是否已被安排
     * @Param [conventionId, boothNum]
     * <AUTHOR>
     */
    Boolean haveSit(Long conventionId, String boothNum);

    /**
     * @return java.lang.Boolean
     * @Description :验证回执码是否重复
     * @Param [receiptCode]
     * <AUTHOR>
     */
    Boolean validateReceiptCode(String receiptCode);


    /**
     * @return
     * @Description : 分组后报名名册数据
     * @Param
     * <AUTHOR>
     */
    List<RegistrationSponsorVo> getConventionRegistrationGroup(ConventionRegistrationDto conventionRegistrationDto, Page page);


    /**
     * @return java.lang.Boolean
     * @Description : 分组后报名名册数据
     * @Param [conventionId, providerName]
     * <AUTHOR>
     */
    Boolean providerNameVerify(Long conventionId, String providerName);

    /**
     * @return java.util.List<com.get.salecenter.vo.BoothVo>
     * @Description :根据回执码查找总费用
     * @Param [receiptCode]
     * <AUTHOR>
     */
    List<BoothVo> getSumRegistrationFreeByReceiptCode(String receiptCode);

    /**
     * @return void
     * @Description :导出报名名册Excel
     * @Param [response, conventionRegistrationDto]
     * <AUTHOR>
     */
    void exportConventionRegistrationExcel(HttpServletResponse response, ConventionRegistrationDto conventionRegistrationDto);

    /**
     * @return BigDecimal
     * @Description :导出报名名册Excel
     * @Param [response, conventionRegistrationDto]
     * <AUTHOR>
     */
    BigDecimal registrationFeeCny(ConventionRegistrationDto conventionRegistrationDto);

    /**
     * 根据条件搜索符合的数据list
     *
     * @param conventionRegistrationDto
     * @return
     */
    List<ConventionRegistration> getConventionRegistrationsByVo(ConventionRegistrationDto conventionRegistrationDto);

    /**
     * 根据id获取报名名册
     *
     * @param conventionRegistrationId
     * @return
     */
    ConventionRegistration getConventionRegistrationById(Long conventionRegistrationId);

    /**
     * 根据id更新
     *
     * @param conventionRegistration
     */
    void updateConventionRegistrationById(ConventionRegistration conventionRegistration);

    /**
     * 置空的更新
     *
     * @param conventionRegistration
     */
    void updateWithNullConventionRegistrationById(ConventionRegistration conventionRegistration);

    /**
     * 一键生成活动费用汇总、应收计划、发票
     *
     * @param conventionRegistrationFastGenerationVo 参数
     */
    void fastGeneration(ConventionRegistrationFastGenerationDto conventionRegistrationFastGenerationVo);

    Integer confirm(String code);

    List<ConventionRegistrationVo> getConventionRegistrationsByReceiptCode(List<String> receiptCode);

    Integer isRegistered(Long fkInstitutionProviderId,  String providerName);
}