package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;


/**
 * @author: Hardy
 * @create: 2023/8/8 17:34
 * @verison: 1.0
 * @description:
 */
@Data
public class ConventionHotelPayDto extends BaseVoEntity{
    /**
     * 峰会Id
     */
    @NotNull(message = "峰会Id")
    @ApiModelProperty(value = "峰会Id",required = true)
    private Long fkConventionId;

    /**
     * 峰会报名Id
     */
    @NotNull(message = "峰会Id")
    @ApiModelProperty(value = "峰会报名Id",required = true)
    private Long fkConventionRegistrationId;

    /**
     * 峰会报名Id
     */
    @NotNull(message = "峰会参展人员Id")
    @ApiModelProperty(value = "峰会参展人员Id",required = true)
    private Long fkConventionPersonId;


    /**
     * 系统订单号
     */
    @ApiModelProperty(value = "系统订单号")
    @Column(name = "system_order_num")
    private String systemOrderNum;

    /**
     * 微信订单号
     */
    @ApiModelProperty(value = "微信订单号")
    @Column(name = "pay_order_num")
    private String payOrderNum;

    /**
     * 机构名称
     */
    @NotBlank(message = "机构名称")
    @ApiModelProperty(value = "机构名称")
    private String institutionName;

    /**
     * 住客名称，逗号分隔
     */
    @ApiModelProperty(value = "住客名称，逗号分隔")
    private String residents;

    /**
     * 住客名称，逗号分隔
     */
    @NotBlank(message = "住客名称")
    @ApiModelProperty(value = "住客名称",required = true)
    private String name;

    /**
     * 住客名称，逗号分隔
     */
    @ApiModelProperty(value = "住客名称中文名")
    private String nameChn;

    @NotBlank(message = "电话")
    @ApiModelProperty(value = "参加人电话",required = true)
    private String tel;

    /**
     * 房型
     */
    @NotBlank(message = "房型")
    @ApiModelProperty(value = "房型",required = true)
    private String roomType;

    /**
     * 到店日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "到店日期")
    @ApiModelProperty(value = "到店日期",required = true)
    private Date checkInTime;

    /**
     * 离店日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "离店日期")
    @ApiModelProperty(value = "离店日期",required = true)
    private Date checkOutTime;

    /**
     * 支付数量
     */
    @ApiModelProperty(value = "支付数量")
    private Integer payCount;

    /**
     * 支付金额
     */
    @ApiModelProperty(value = "支付金额")
    private BigDecimal payAmount;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

}
