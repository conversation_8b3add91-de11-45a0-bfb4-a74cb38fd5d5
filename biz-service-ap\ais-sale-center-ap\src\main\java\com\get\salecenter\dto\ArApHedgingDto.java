package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class ArApHedgingDto {

    @ApiModelProperty("应收id")
    @NotNull(message = "应收id不能为空")
    private Long receivablePlanId;

    @ApiModelProperty("发票id")
    @NotNull(message = "发票id不能为空")
    private Long invoiceId;

    @ApiModelProperty("应收币种")
    private String fkReceivableCurrencyNum;

    @ApiModelProperty("额外原因id")
    private Integer fkReceivableResonId;

    @ApiModelProperty("应付币种")
    private String fkPayableCurrencyNum;

    @ApiModelProperty("公司id")
    private Long fkCompanyId;

    @ApiModelProperty("对冲金额")
    @NotNull(message = "对冲金额不能为空")
    private BigDecimal receivableAmount;

    @ApiModelProperty("应收对冲摘要")
    private String receivableRemark;

    @ApiModelProperty("是否要对冲应付")
    @NotNull(message = "是否要对冲应付不能为空")
    private Boolean payableFlag;

    @ApiModelProperty("应付对冲摘要")
    private String payableRemark;

    @ApiModelProperty("应付对冲金额")
    private BigDecimal payableAmount;
}
