package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.secure.utils.SecureUtil;
import com.get.salecenter.dto.AgentStaffDto;
import com.get.salecenter.service.IAgentStaffService;
import com.get.salecenter.vo.AgentAndAgentLabelVo;
import com.get.salecenter.vo.AgentStaffVo;
import com.get.salecenter.vo.StaffBdCodeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/10/16 15:13
 * @verison: 1.0
 * @description: BD绑定管理
 */
@Api(tags = "BD绑定管理")
@RestController
@RequestMapping("sale/agentStaff")
public class AgentStaffController {
    @Resource
    private IAgentStaffService agentStaffService;

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :绑定BD
     * @Param [conventionVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "绑定BD", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/BD绑定管理/绑定BD")
    @PostMapping("setBd")
    public ResponseBo setBd(@RequestBody @Validated(AgentStaffDto.Add.class) AgentStaffDto agentStaffDto) {
        this.agentStaffService.setBd(agentStaffDto);
        return SaveResponseBo.ok();
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/BD绑定管理/查询BD绑定")
    @PostMapping("datas")
    public ResponseBo<AgentStaffVo> datas(@RequestBody SearchBean<AgentStaffDto> page) {
        List<AgentStaffVo> datas = agentStaffService.getAgentStaffs(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AgentStaffVo>
     * @Description: 修改BD绑定管理
     * @Param [agentStaffDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改BD绑定管理", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/BD绑定管理/更新")
    @PostMapping("update")
    public ResponseBo<AgentStaffVo> update(@RequestBody @Validated(AgentStaffDto.Update.class)  AgentStaffDto agentStaffDto) {
        return UpdateResponseBo.ok(agentStaffService.updateAgentStaff(agentStaffDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AgentStaffVo>
     * @Description: 修改BD绑定管理
     * @Param [agentStaffVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量修改BD绑定", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/BD绑定管理/批量修改BD绑定")
    @PostMapping("/updateAgentStaffBinding")
    public ResponseBo updateAgentStaffBinding(@RequestBody AgentStaffDto agentStaffVo) {
        agentStaffService.updateAgentStaffBinding(agentStaffVo);
        return UpdateResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StaffBdCodeVo>
     * @Description :绑定BD下拉框数据
     * @Param [companyId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "绑定BD下拉框数据", notes = "testBdFLag true:返回公司下所有bd,  false：排除掉T开头的 测试bD" +
            "subordinateFLag true:查询所有bd   false:查询有查看权限bd")
    @GetMapping("getStaffBdCodeList")
    public ResponseBo<StaffBdCodeVo> getStaffBdCodeList(@RequestParam("companyId") Long companyId,
                                                        @RequestParam(value = "testBdFLag", required = false,defaultValue = "true") Boolean testBdFlag,
                                                        @RequestParam(value = "subordinateFlag",required = false, defaultValue = "true") Boolean subordinateFlag) {
        List<StaffBdCodeVo> datas = agentStaffService.getStaffBdCodeList(companyId, testBdFlag, subordinateFlag);
        return new ListResponseBo<>(datas);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "BD下拉框数据 有权限bd")
    @GetMapping("getStaffBdCodeListByPermission")
    public ResponseBo<StaffBdCodeVo> getStaffBdCodeListByPermission() {
        List<StaffBdCodeVo> datas = agentStaffService.getStaffBdCodeListByPermission(null);
        return new ListResponseBo<>(datas);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据公司ids查询BD下拉框数据 有权限bd")
    @PostMapping("getStaffBdCodeListPermissionByCompany")
    public ResponseBo<StaffBdCodeVo> getStaffBdCodeListByPermission(@RequestBody List<Long> companyIds) {
        List<StaffBdCodeVo> datas = agentStaffService.getStaffBdCodeListByPermission(companyIds);
        return new ListResponseBo<>(datas);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "有绑定代理并且为业务下属的 BD下拉框数据", notes = "testBdFLag true:返回公司下所有bd,  false：排除掉T开头的 测试bD")
    @GetMapping("getExistAgentStaffBdCodeList")
    public ResponseBo<StaffBdCodeVo> getExistAgentStaffBdCodeList(@RequestParam("companyId") Long companyId) {
        if (!SecureUtil.validateCompany(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        List<StaffBdCodeVo> datas = agentStaffService.getExistAgentStaffBdCodeList(companyId);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 代理下拉(BD)
     * @Param [companyId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "代理下拉(BD联动)", notes = "")
    @GetMapping("getAgentList")
    public ResponseBo<BaseSelectEntity> getAgentList() {
        List<BaseSelectEntity> datas = agentStaffService.getAgentSelect();
        return new ListResponseBo<>(datas);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 代理下拉(BD)
     * @Param [companyId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "BD下拉", notes = "")
    @GetMapping("getBDList")
    public ResponseBo<StaffBdCodeVo> getBdList(@RequestParam(value = "agentId") Long agentId) {
        List<StaffBdCodeVo> datas = agentStaffService.getBdSelect(agentId);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 代理下拉(BD)
     * @Param [companyId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "代理下拉(根据学生id)", notes = "")
    @GetMapping("getAgentSelect")
    public ResponseBo<BaseSelectEntity> getAgentSelect(Long studentId) {
        List<BaseSelectEntity> datas = agentStaffService.getAgentByStudentId(studentId);
        return new ListResponseBo<>(datas);
    }

    /**
     * 获取学生已绑定申请方案的代理，业务国家过滤
     *
     * @param studentId
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取学生已绑定申请方案的代理，业务国家过滤", notes = "")
    @GetMapping("getAgents")
    public ResponseBo<AgentAndAgentLabelVo> getAgentList(Long studentId) {
        List<AgentAndAgentLabelVo> datas = agentStaffService.getAgentList(studentId);
        return new ListResponseBo<>(datas);
    }

    /**
     * @ Description :员工跟进的全部代理
     * @ Param [fkStaffId]
     * @ return java.util.List<java.lang.Long>
     * @ author LEO
     */
    @ApiIgnore
    @VerifyPermission(IsVerify = false)
    @ApiOperation("员工跟进的全部代理")
    @GetMapping("getAgentsByStaffId")
    public List<Long> getAgentsByStaffId(@RequestParam("fkStaffId") Long fkStaffId) {
        List<Long> agentsByStaffId = agentStaffService.getAgentsByStaffId(fkStaffId);
        return agentsByStaffId;
    }
}
