package com.get.resumecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.resumecenter.vo.ResumeIntentionVo;
import com.get.resumecenter.service.IResumeIntentionService;
import com.get.resumecenter.dto.ResumeIntentionDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @DATE: 2020/8/5
 * @TIME: 10:42
 * @Description: 职位意向管理
 **/

@Api(tags = "简历职位意向管理")
@RestController
@RequestMapping("resume/intention")
public class ResumeIntentionController {

    @Autowired
    private IResumeIntentionService intentionService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.DETAIL, description = "人才中心/职位意向管理/参数详情")
    @GetMapping("/{id}")
    public ResponseBo detail(@PathVariable("id") Long id) {
        ResumeIntentionVo resumeIntentionVo = intentionService.getResumeIntentionById(id);
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", resumeIntentionVo);
        return responseBo;
    }

    /**
     * 修改信息
     *
     * @param resumeIntentionDto
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "新增修改通用接口")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.EDIT, description = "人才中心/职位意向管理/修改简历")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated(ResumeIntentionDto.Add.class) ResumeIntentionDto resumeIntentionDto) {
        return UpdateResponseBo.ok(intentionService.updateResumeIntention(resumeIntentionDto));
    }

}
