package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.ContactPersonCompanyMapper;
import com.get.institutioncenter.dao.ContactPersonMapper;
import com.get.institutioncenter.dto.ContactPersonCompanyDto;
import com.get.institutioncenter.dto.ContactPersonDto;
import com.get.institutioncenter.entity.ContactPerson;
import com.get.institutioncenter.entity.ContactPersonCompany;
import com.get.institutioncenter.service.*;
import com.get.institutioncenter.vo.ContactPersonTypeVo;
import com.get.institutioncenter.vo.ContactPersonVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.platformconfigcenter.feign.IPlatformConfigCenterClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @DATE: 2020/8/17
 * @TIME: 11:11
 * @Description: 联系人管理实现类
 **/
@Service
public class ContactPersonServiceImpl extends BaseServiceImpl<ContactPersonMapper, ContactPerson> implements IContactPersonService {
    @Resource
    private ContactPersonMapper personMapper;
    //    @Resource
//    private IReminderCenterClient reminderCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private IPlatformConfigCenterClient platformConfigCenterClient;
    @Resource
    private IContactPersonCompanyService companyService;
    @Resource
    private IInstitutionProviderService providerService;
    @Resource
    private IInstitutionService institutionService;
    @Resource
    private IInstitutionProviderCompanyService providerCompanyService;
    @Resource
    private IContactPersonTypeService contactPersonTypeService;
    @Resource
    private ContactPersonCompanyMapper contactPersonCompanyMapper;


    @Override
    public List<ContactPersonVo> getContactPersonDtos(ContactPersonDto contactPersonDto, Page page) {
        if (GeneralTool.isEmpty(contactPersonDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        LambdaQueryWrapper<ContactPerson> wrapper = new LambdaQueryWrapper();
        //查询登陆人公司ids，非本公司或者下属公司的联系人不给看
        if (GeneralTool.isEmpty(contactPersonDto.getFkCompanyId())) {
            List<Long> companyIds = SecureUtil.getCompanyIds();
            companyIds.removeIf(Objects::isNull);
            List<Long> companyId = getCompanyId(companyIds);
            wrapper.in(ContactPerson::getId, companyId);
        }
        if (GeneralTool.isNotEmpty(contactPersonDto.getFkCompanyId())) {
            if (!SecureUtil.validateCompany(contactPersonDto.getFkCompanyId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
            List<Long> queryCompanyId = new ArrayList<>();
            queryCompanyId.add(contactPersonDto.getFkCompanyId());
            List<Long> contactIds = getCompanyId(queryCompanyId);
            wrapper.in(ContactPerson::getId, contactIds);
        }
        if (GeneralTool.isNotEmpty(contactPersonDto.getFkTableName())) {
            wrapper.eq(ContactPerson::getFkTableName, contactPersonDto.getFkTableName());
        }
        if (GeneralTool.isNotEmpty(contactPersonDto.getFkTableId())) {
            wrapper.eq(ContactPerson::getFkTableId, contactPersonDto.getFkTableId());
        }
        //根据公司查询
        if (GeneralTool.isNotEmpty(contactPersonDto.getCompany())) {
            wrapper.like(ContactPerson::getCompany, contactPersonDto.getCompany());
        }
        //联系人类型查询
        if (GeneralTool.isNotEmpty(contactPersonDto.getFkContactPersonTypeKey())) {
            wrapper.like(ContactPerson::getFkContactPersonTypeKey, contactPersonDto.getFkContactPersonTypeKey());
        }


        //名称查询/公司名称查询
        if (GeneralTool.isNotEmpty(contactPersonDto.getKeyWord())) {
            wrapper.and(wrapper_ ->
                    wrapper_.like(ContactPerson::getName, contactPersonDto.getKeyWord()).or().like(ContactPerson::getCompany, contactPersonDto.getKeyWord()));
        }
        wrapper.orderByDesc(ContactPerson::getId);
        //获取分页数据
        IPage<ContactPerson> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<ContactPerson> contactPeoples = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<ContactPersonVo> contactPersonVos = contactPeoples.stream()
                .map(contactPerson -> BeanCopyUtils.objClone(contactPerson, ContactPersonVo::new)).collect(Collectors.toList());

        //设置联系人属性
        setContactPersonList(contactPersonVos);
        Map<Long, String> companyMap = getCompanyMap();

        //表ids
        Set<Long> fkTableIds = contactPersonVos.stream().map(ContactPersonVo::getFkTableId).collect(Collectors.toSet());
        //根据提供商ids获取名称map
        Map<Long, String> nameByProviderIds = new HashMap<>();
        //根据学校ids获取名称map
        Map<Long, String> institutionNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkTableIds)) {
            nameByProviderIds = providerService.getNameByProviderIds(fkTableIds);
            institutionNamesByIds = institutionService.getInstitutionNamesByIds(fkTableIds);
        }

        //合同联系人ids
        Set<Long> contactIds = contactPersonVos.stream().map(ContactPersonVo::getId).collect(Collectors.toSet());
        //根据合同联系人ids获取公司ids
        Map<Long, Set<Long>> companyIdsByContactIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(contactIds)) {
            companyIdsByContactIds = companyService.getCompanyIdsByContactIds(contactIds);
        }
        List<ContactPersonVo> contactPersons = new ArrayList<>();
        for (ContactPersonVo contactPersonVo : contactPersonVos) {
            //设置目标名称
            setTargetName(contactPersonVo, companyMap, nameByProviderIds, institutionNamesByIds, companyIdsByContactIds);
            if (GeneralTool.isNotEmpty(contactPersonDto.getTargetName())) {
                if (contactPersonVo.getTargetName().equalsIgnoreCase(contactPersonDto.getTargetName())) {
                    contactPersons.add(contactPersonVo);
                }
            } else {
                contactPersons.add(contactPersonVo);
            }
        }
        if (GeneralTool.isNotEmpty(contactPersonDto.getTargetName())) {
            page.setAll(contactPersons.size());
        }
        return contactPersons;
    }

    @Override
    public ContactPersonVo finContactPersonById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ContactPerson contactPerson = personMapper.selectById(id);
        ContactPersonVo contactPersonVo = BeanCopyUtils.objClone(contactPerson, ContactPersonVo::new);

        //提供商ids
        Set<Long> fkTableIds = new HashSet<>();
        fkTableIds.add(contactPersonVo.getFkTableId());
        //根据提供商ids获取名称map
        Map<Long, String> nameByProviderIds = new HashMap<>();
        //根据学校ids获取名称map
        Map<Long, String> institutionNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkTableIds)) {
            nameByProviderIds = providerService.getNameByProviderIds(fkTableIds);
            institutionNamesByIds = institutionService.getInstitutionNamesByIds(fkTableIds);
        }

        //合同联系人ids
        Set<Long> contactIds = new HashSet<>();
        contactIds.add(contactPersonVo.getId());
        //根据合同联系人ids获取公司ids
        Map<Long, Set<Long>> companyIdsByContactIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(contactIds)) {
            companyIdsByContactIds = companyService.getCompanyIdsByContactIds(contactIds);
        }

        if (GeneralTool.isNotEmpty(contactPersonVo)) {
            Map<Long, String> companyMap = getCompanyMap();
            setTargetName(contactPersonVo, companyMap, nameByProviderIds, institutionNamesByIds, companyIdsByContactIds);
            List<ContactPersonVo> contactPersonDtoList = new ArrayList<>();
            contactPersonDtoList.add(contactPersonVo);
            setContactPersonList(contactPersonDtoList);
        }

        return contactPersonVo;
    }

    @Override
    public Long addContactPerson(ContactPersonDto contactPersonDto) {
        ContactPerson contactPerson = createContactPerson(contactPersonDto);

        Long companyId;
        if (GeneralTool.isNotEmpty(contactPersonDto.getFkCompanyId())) {
            companyId = contactPersonDto.getFkCompanyId();
        } else {
            companyId = SecureUtil.getFkCompanyId();
        }
        //添加到中间表
        ContactPersonCompanyDto relation = new ContactPersonCompanyDto();
        relation.setFkCompanyId(companyId);
        relation.setFkContactPersonId(contactPerson.getId());
        companyService.addRelation(relation);

        return contactPerson.getId();
    }

    @Override
    public ContactPersonVo updateContactPerson(ContactPersonDto contactPersonDto) {
        if (GeneralTool.isEmpty(contactPersonDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ContactPerson contactPerson = BeanCopyUtils.objClone(contactPersonDto, ContactPerson::new);
        personMapper.updateById(contactPerson);
        return finContactPersonById(contactPerson.getId());
    }

    @Override
    public void deleteContactPerson(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<ContactPersonCompany> contactPersonCompanies = contactPersonCompanyMapper.selectList(
                new LambdaQueryWrapper<ContactPersonCompany>()
                        .eq(ContactPersonCompany::getFkContactPersonId, id)
        );
        if (GeneralTool.isNotEmpty(contactPersonCompanies)) {
            contactPersonCompanyMapper.deleteBatchIds(contactPersonCompanies.stream().map(ContactPersonCompany::getId).collect(Collectors.toList()));
        }
        personMapper.deleteById(id);
    }

    @Override
    public void editContactPersonCompany(List<ContactPersonCompanyDto> validList) {
        companyService.editContactPersonCompany(validList);
    }

    @Override
    public List<com.get.institutioncenter.vo.CompanyTreeVo> getContactRelation(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return companyService.getContactCompanyRelation(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addProviderContactPerson(ContactPersonDto contactPersonDto) {
        ContactPerson contactPerson = createContactPerson(contactPersonDto);
        //获取公司ids
        List<Long> companyIds = providerCompanyService.getRelationByProviderId(contactPerson.getFkTableId());
        //跟随提供商的公司关系
        if (GeneralTool.isNotEmpty(companyIds)) {
            //添加中间表
            for (Long companytId : companyIds) {
                ContactPersonCompanyDto relation = new ContactPersonCompanyDto();
                relation.setFkCompanyId(companytId);
                relation.setFkContactPersonId(contactPerson.getId());
                companyService.addRelation(relation);
            }
        }
        return contactPerson.getId();
    }

    @Override
    public List<Map<String, Object>> findTargetType() {
        return TableEnum.enumsTranslation2Arrays(TableEnum.INSTITUTION_TYPES);
    }

    private List<ContactPersonTypeVo> getContactPersonType() {
        return contactPersonTypeService.getContactPersonTypes();
    }


    private ContactPerson createContactPerson(ContactPersonDto contactPersonDto) {
        if (GeneralTool.isEmpty(contactPersonDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ContactPerson contactPerson = BeanCopyUtils.objClone(contactPersonDto, ContactPerson::new);
        int i = personMapper.insert(contactPerson);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return contactPerson;
    }

    /**
     * 设置属性
     *
     * @param contactPersonVos
     * @
     */
    private void setContactPersonList(List<ContactPersonVo> contactPersonVos) {
        List<ContactPersonTypeVo> contactPersonType = getContactPersonType();
        if (GeneralTool.isNotEmpty(contactPersonVos)) {
            //当联系人类型不为空
            if (GeneralTool.isNotEmpty(contactPersonType)) {
                //遍历设置属性
                contactPersonVos.forEach(contactPersonDto ->
                        contactPersonDto.setContactPersonTypeName(getContactType(contactPersonDto.getFkContactPersonTypeKey(), contactPersonType)));
            }
        }
    }

    /**
     * 获取联系人类型
     *
     * @param contactPersonType
     * @param typeList
     * @return
     */
    private List<String> getContactType(String contactPersonType, List<ContactPersonTypeVo> typeList) {
        if (GeneralTool.isEmpty(contactPersonType)) {
            return null;
        }
        String[] typeSplit = contactPersonType.split(",");
        //转成流对象
        Stream<String> stream = Arrays.stream(typeSplit);
        //获取类型
        List<String> collect = stream.map(s -> typeList.stream()
                        .filter(contactPersonTypeVo -> contactPersonTypeVo.getTypeKey().equals(s))
                        .findFirst()
                        .map(ContactPersonTypeVo::getTypeName).orElse(null))
                .collect(Collectors.toList());
        collect.removeIf(Objects::isNull);
        return collect;
    }


    private List<Long> getCompanyId(List<Long> companyId) {
        List<Long> relation = companyService.getRelationByCompanyId(companyId);
        if (GeneralTool.isEmpty(relation)) {
            relation = new ArrayList<>();
            relation.add(0L);
        }
        return relation;
    }

    private Map<Long, String> getCompanyMap() {
        Result<List<CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        List<CompanyTreeVo> companyTreeVos = result.getData();
        //初始为5的map
        Map<Long, String> companyMap = new HashMap<>(5);
        if (GeneralTool.isNotEmpty(companyTreeVos)) {
            companyMap = companyTreeVos.stream().collect(Collectors.toMap(CompanyTreeVo::getId, CompanyTreeVo::getShortName));
        }
        return companyMap;
    }

    private void setTargetName(ContactPersonVo contactPersonVo, Map<Long, String> companyMap,
                               Map<Long, String> nameByProviderIds, Map<Long, String> institutionNamesByIds,
                               Map<Long, Set<Long>> companyIdsByContactIds) {
        if (GeneralTool.isNotEmpty(contactPersonVo)) {
            if (TableEnum.INSTITUTION_PROVIDER.key.equals(contactPersonVo.getFkTableName())) {
                contactPersonVo.setTargetName(nameByProviderIds.get(contactPersonVo.getFkTableId()));
            }
            if (TableEnum.INSTITUTION.key.equals(contactPersonVo.getFkTableName())) {
                contactPersonVo.setTargetName(institutionNamesByIds.get(contactPersonVo.getFkTableId()));
            }
            //设置目标类型（表名）
            contactPersonVo.setTargetTypeName(TableEnum.getValue(contactPersonVo.getFkTableName()));
            setCompanyName(contactPersonVo, companyMap, companyIdsByContactIds);
        }
    }

    private void setCompanyName(ContactPersonVo contactPersonVo, Map<Long, String> companyMap,
                                Map<Long, Set<Long>> companyIdsByContactIds) {
        if (GeneralTool.isNotEmpty(contactPersonVo)) {
            StringBuilder builder = new StringBuilder();
            Set<Long> companyIds = companyIdsByContactIds.get(contactPersonVo.getId());
            if (GeneralTool.isNotEmpty(companyIds)) {
                for (Long companyId : companyIds) {
                    String name = companyMap.get(companyId);
                    builder.append(name).append("，");
                }
                contactPersonVo.setCompanyName(sub(builder));
            }
        }
    }

    private String sub(StringBuilder sb) {
        if (GeneralTool.isEmpty(sb)) {
            return null;
        }
        String substring = null;
        int i = sb.lastIndexOf("，");
        if (i != -1) {
            substring = sb.substring(0, i);
        }
        return substring;
    }

}
