package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.salecenter.vo.ConventionAwardPayVo;
import com.get.salecenter.dto.ConventionAwardPayDto;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/9/13 11:05
 * @verison: 1.0
 * @description:
 */
public interface IConventionAwardPayService {
    /**
     * @return java.util.List<com.get.salecenter.vo.ConventionAwardPayVo>
     * @Description :列表
     * @Param [conventionAwardPayDto, page]
     * <AUTHOR>
     */
    List<ConventionAwardPayVo> datas(ConventionAwardPayDto conventionAwardPayDto, Page page);
}
