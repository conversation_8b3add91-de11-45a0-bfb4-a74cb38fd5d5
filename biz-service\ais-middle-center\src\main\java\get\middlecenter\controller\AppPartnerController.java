package get.middlecenter.controller;

import com.alibaba.fastjson.JSONObject;
import get.middlecenter.dto.appPartner.CreateContractPdfDto;
import get.middlecenter.service.AppPartnerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;


@RestController
@RequestMapping("/api/appPartner")
@Slf4j
public class AppPartnerController {

    @Resource
    private AppPartnerService appPartnerService;

    @PostMapping("/createContractPdf")
    public void createContractPdf(@RequestHeader("X-Nonce") String nonce, @RequestBody CreateContractPdfDto dto, HttpServletResponse response) {
        log.info("createAgentContractPdf===============>");
        log.info("dto:{}", JSONObject.toJSONString(dto));
        log.info("nonce:{}", nonce);
        appPartnerService.createContractPdf(dto, nonce, response);
    }
}