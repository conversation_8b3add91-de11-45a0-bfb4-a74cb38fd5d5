package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Sea
 * @create: 2021/3/29 10:14
 * @verison: 1.0
 * @description:
 */
@Data
public class InstitutionProviderAreaCountryVo extends BaseEntity {
    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String areaCountryName;

    /**
     * 业务国家名称
     */
    @ApiModelProperty(value = "业务国家名称")
    private String areaCountryNames;

    //===============实体类InstitutionProviderAreaCountry=====================
    private static final long serialVersionUID = 1L;
    /**
     * 学校代理Id
     */
    @ApiModelProperty(value = "学校代理Id")
    @Column(name = "fk_institution_provider_id")
    private Long fkInstitutionProviderId;
    /**
     * 业务国家Id
     */
    @ApiModelProperty(value = "业务国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;

}
