<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.ReceiptFormItemMapper">
    <insert id="insertSelective" parameterType="com.get.financecenter.entity.ReceiptFormItem" keyProperty="id"
            useGeneratedKeys="true">
        insert into m_receipt_form_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkReceiptFormId != null">
                fk_receipt_form_id,
            </if>
            <if test="fkReceivablePlanId != null">
                fk_receivable_plan_id,
            </if>
            <if test="amountReceipt != null">
                amount_receipt,
            </if>
            <if test="exchangeRateReceivable != null">
                exchange_rate_receivable,
            </if>
            <if test="amountReceivable != null">
                amount_receivable,
            </if>
            <if test="amountExchangeRate != null">
                amount_exchange_rate,
            </if>
            <if test="exchangeRateHkd != null">
                exchange_rate_hkd,
            </if>
            <if test="amountHkd != null">
                amount_hkd,
            </if>
            <if test="exchangeRateRmb != null">
                exchange_rate_rmb,
            </if>
            <if test="amountRmb != null">
                amount_rmb,
            </if>
            <if test="serviceFee != null">
                service_fee,
            </if>
            <if test="summary != null">
                summary,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkReceiptFormId != null">
                #{fkReceiptFormId,jdbcType=BIGINT},
            </if>
            <if test="fkReceivablePlanId != null">
                #{fkReceivablePlanId,jdbcType=BIGINT},
            </if>
            <if test="amountReceipt != null">
                #{amountReceipt,jdbcType=DECIMAL},
            </if>
            <if test="exchangeRateReceivable != null">
                #{exchangeRateReceivable,jdbcType=DECIMAL},
            </if>
            <if test="amountReceivable != null">
                #{amountReceivable,jdbcType=DECIMAL},
            </if>
            <if test="amountExchangeRate != null">
                #{amountExchangeRate,jdbcType=DECIMAL},
            </if>
            <if test="exchangeRateHkd != null">
                #{exchangeRateHkd,jdbcType=DECIMAL},
            </if>
            <if test="amountHkd != null">
                #{amountHkd,jdbcType=DECIMAL},
            </if>
            <if test="exchangeRateRmb != null">
                #{exchangeRateRmb,jdbcType=DECIMAL},
            </if>
            <if test="amountRmb != null">
                #{amountRmb,jdbcType=DECIMAL},
            </if>
            <if test="serviceFee != null">
                #{serviceFee,jdbcType=DECIMAL},
            </if>
            <if test="summary != null">
                #{summary,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="getAlreadyReceiptByPlanId" resultType="com.get.financecenter.vo.AlreadyReceiptVo">
        SELECT i.id,mrp.fk_currency_type_num alreadyReceiptCurrency,i.amount_receipt alreadyReceiptAmount,
               IFNULL(i.amount_exchange_rate,0) alreadyExchangeRate, i.amount_receivable amountReceivable
        from m_receipt_form_item i
                 LEFT join m_receipt_form f on i.fk_receipt_form_id=f.id
        LEFT JOIN ais_sale_center.m_receivable_plan AS mrp ON mrp.id = i.fk_receivable_plan_id
        where i.fk_receivable_plan_id= #{planId}
          and f.status=1
    </select>
    <select id="getAlreadyReceiptByPlanIds" resultType="com.get.financecenter.vo.AlreadyReceiptVo">
        SELECT i.id,f.fk_currency_type_num alreadyReceiptCurrency,i.amount_receipt alreadyReceiptAmount,
        IFNULL(i.amount_exchange_rate,0) alreadyExchangeRate, i.amount_receivable amountReceivable,i.fk_receivable_plan_id fkReceivablePlanId,i.fk_receipt_form_id fkReceiptFormId
        from m_receipt_form_item i
        LEFT join m_receipt_form f on i.fk_receipt_form_id=f.id
        where 1=1
        <if test="planIds != null and planIds.size()>0">
            AND i.fk_receivable_plan_id IN
            <foreach collection="planIds" item="planId" index="index" open="(" separator="," close=")">
                #{planId}
            </foreach>
        </if>
        and f.status=1
        order by i.gmt_create desc
    </select>

    <select id="getCompanyIdByItemId" resultType="java.lang.Long">
        SELECT f.fk_company_id from m_receipt_form_item r
                                        left join m_receipt_form f on r.fk_receipt_form_id=f.id
        where r.id=#{itemId} limit 1
    </select>

    <select id="getAmountByFormId" resultType="java.math.BigDecimal">
        SELECT IFNULL(sum(i.amount_receipt),0) alreadyReceiptAmount from  m_receipt_form_item i
        LEFT join m_receipt_form f on i.fk_receipt_form_id=f.id
        where i.fk_receipt_form_id= #{formId}
        <if test="formItemId != null">
            AND i.id != #{formItemId}
        </if>
        and f.status=1
    </select>
    <select id="getPlanIdByInvoiceNum" resultType="java.lang.Long">
        SELECT
            rirp.fk_receivable_plan_id
        FROM
            m_receipt_form AS mrf
                LEFT JOIN
            m_invoice AS mi
            ON
                mrf.fk_invoice_num = mi.num
                LEFT JOIN
            r_invoice_receivable_plan AS rirp
            ON
                rirp.fk_invoice_id = mi.id
        where
            mrf.fk_invoice_num = #{fkInvoiceNum}
    </select>
    <select id="getSettlementReceiptFormItemListByPlanId" resultType="com.get.financecenter.vo.ReceiptFormItemVo">
      SELECT
        mrfi.*, mrf.fk_currency_type_num
      FROM
        ais_finance_center.m_receipt_form AS mrf
          INNER JOIN ais_finance_center.m_receipt_form_item AS mrfi ON mrfi.fk_receipt_form_id = mrf.id
            AND mrfi.fk_receivable_plan_id = #{receivablePlanId}
          LEFT JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi ON rppsi.fk_receipt_form_item_id = mrfi.id
      WHERE  (rppsi.status IS NULL OR  rppsi.status NOT IN (1,2)) AND mrf.status = 1 AND mrf.settlement_status = 1

    </select>
    <select id="getReceiptFormItemListFeignByPlanIds" resultType="com.get.financecenter.vo.ReceiptFormItemVo">
        SELECT
        mrfi.*, mrf.fk_currency_type_num
        FROM
        ais_finance_center.m_receipt_form AS mrf
        INNER JOIN ais_finance_center.m_receipt_form_item AS mrfi ON mrfi.fk_receipt_form_id = mrf.id
        WHERE mrf.status = 1 AND mrfi.fk_receivable_plan_id IN
        <foreach collection="receivablePlanIds" item="planId" index="index" open="(" separator="," close=")">
            #{planId}
        </foreach>
    </select>
    <select id="getUnsettledReceiptFormItem" resultType="com.get.salecenter.vo.SaleReceiptFormItemVo">
        SELECT
            mrfi.*, mrf.fk_currency_type_num
        FROM
            ais_finance_center.m_receipt_form_item AS mrfi
        INNER JOIN ais_finance_center.m_receipt_form AS mrf ON mrf.id = mrfi.fk_receipt_form_id
        WHERE
            NOT EXISTS ( SELECT * FROM ais_finance_center.r_payable_plan_settlement_installment WHERE fk_receipt_form_item_id = mrfi.id )
          AND mrfi.id = #{receivablePlanId}
    </select>
    <select id="getReceiptFormItemListFeignByFormItemIds"
            resultType="com.get.financecenter.vo.ReceiptFormItemVo">
        SELECT
        mrfi.*, mrf.fk_currency_type_num
        FROM
        ais_finance_center.m_receipt_form AS mrf
        INNER JOIN ais_finance_center.m_receipt_form_item AS mrfi ON mrfi.fk_receipt_form_id = mrf.id
        AND mrfi.id IN
        <foreach collection="receiptFormItemIds" item="receiptFormItemId" index="index" open="(" separator="," close=")">
            #{receiptFormItemId}
        </foreach>
    </select>

    <select id="getReceiptFormItemData" resultType="com.get.financecenter.entity.ReceiptFormItem">
        SELECT
        r.*
        FROM
        m_receipt_form_item r
        left join ais_sale_center.m_receivable_plan p on r.fk_receivable_plan_id = p.id
        <if test="receiptFormItemDto.fkTypeKey == 'm_institution_provider' or receiptFormItemDto.fkTypeKey == 'm_institution_channel'">
            left join ais_sale_center.m_student_offer_item o on o.id = p.fk_type_target_id
        </if>
        <if test="receiptFormItemDto.fkTypeKey == 'm_business_channel_ins' or receiptFormItemDto.fkTypeKey == 'm_business_provider_ins'">
            left join ais_sale_center.m_student_insurance o on o.id = p.fk_type_target_id
        </if>
        <if test="receiptFormItemDto.fkTypeKey == 'm_student'">
            left join ais_sale_center.m_student_service_fee o on o.id = p.fk_type_target_id
        </if>
        <if test="receiptFormItemDto.fkTypeKey == 'm_business_channel_acc' or receiptFormItemDto.fkTypeKey == 'm_business_provider_acc'">
            left join ais_sale_center.m_student_accommodation o on o.id = p.fk_type_target_id
        </if>
        left join ais_sale_center.m_student m on o.fk_student_id = m.id
        WHERE
        1=1
        <if test="receiptFormItemDto.fkReceiptFormId != null">
            and r.fk_receipt_form_id = #{receiptFormItemDto.fkReceiptFormId}
        </if>
        <if test="receiptFormItemDto.studentName != null and receiptFormItemDto.studentName != ''">
            and (m.name like CONCAT('%',#{receiptFormItemDto.studentName},'%') or m.first_name like CONCAT('%',#{receiptFormItemDto.studentName},'%')
            or m.last_name like CONCAT('%',#{receiptFormItemDto.studentName},'%') or REPLACE(CONCAT(m.first_name,m.last_name),' ','') like concat('%',#{receiptFormItemDto.studentName},'%')
            OR REPLACE(CONCAT(m.last_name,m.first_name),' ','') like concat('%',#{receiptFormItemDto.studentName},'%') )
        </if>
    </select>
    <select id="getReceiptFormByInvoiceId" resultType="com.get.financecenter.vo.ReceiptFormItemVo">
        SELECT
            mri.*,mr.receipt_date
        FROM
            m_receipt_form_item mri
        INNER JOIN r_receipt_form_invoice ri ON ri.fk_receipt_form_id = mri.fk_receipt_form_id
        INNER JOIN  m_receipt_form mr ON mr.id = mri.fk_receipt_form_id
        WHERE ri.fk_invoice_id = #{fkInvoiceId}
    </select>
</mapper>