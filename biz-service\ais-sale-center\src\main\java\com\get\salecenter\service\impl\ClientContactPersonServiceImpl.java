package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.ClientContactPersonMapper;
import com.get.salecenter.vo.ClientContactPersonVo;
import com.get.salecenter.entity.ClientContactPerson;
import com.get.salecenter.service.IClientContactPersonService;
import com.get.salecenter.dto.ClientContactPersonDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * author:Neil
 * Time: 11:27
 * Date: 2022/8/19
 * Description:
 */
@Service
public class ClientContactPersonServiceImpl implements IClientContactPersonService {

    @Resource
    private ClientContactPersonMapper clientContactPersonMapper;

    @Resource
    private UtilService utilService;

    @Override
    public Long addContactPerson(ClientContactPersonDto contactPersonVo) {
        if (GeneralTool.isEmpty(contactPersonVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ClientContactPerson contactPerson = BeanCopyUtils.objClone(contactPersonVo, ClientContactPerson::new);
        utilService.updateUserInfoToEntity(contactPerson);
        clientContactPersonMapper.insert(contactPerson);
        return contactPerson.getId();
    }

    @Override
    public List<ClientContactPersonVo> getContactPersons(ClientContactPersonDto contactPersonVo, SearchBean<ClientContactPersonDto> page) {
        if (GeneralTool.isEmpty(contactPersonVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(contactPersonVo.getFkClientId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("student_id_null"));
        }
        LambdaQueryWrapper<ClientContactPerson> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ClientContactPerson::getFkClientId, contactPersonVo.getFkClientId());
        if (GeneralTool.isNotEmpty(contactPersonVo.getName())) {
            lambdaQueryWrapper.like(ClientContactPerson::getName, contactPersonVo.getName());
        }
        if (GeneralTool.isNotEmpty(contactPersonVo.getRelationship())) {
            lambdaQueryWrapper.eq(ClientContactPerson::getRelationship, contactPersonVo.getRelationship());
        }
        lambdaQueryWrapper.orderByDesc(ClientContactPerson::getGmtCreate);
        List<ClientContactPerson> studentContactPersonList = clientContactPersonMapper.selectList(lambdaQueryWrapper);

        return studentContactPersonList.stream().map(ClientContactPerson ->
                BeanCopyUtils.objClone(ClientContactPerson, ClientContactPersonVo::new)).collect(Collectors.toList());
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        clientContactPersonMapper.deleteById(id);
    }

    @Override
    public ClientContactPersonVo updateContactPerson(ClientContactPersonDto clientContactPersonDto) {
        if (GeneralTool.isEmpty(clientContactPersonDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ClientContactPerson contactPerson = BeanCopyUtils.objClone(clientContactPersonDto, ClientContactPerson::new);
        utilService.updateUserInfoToEntity(contactPerson);
        clientContactPersonMapper.updateById(contactPerson);
        return findContactPersonById(contactPerson.getId());
    }

    @Override
    public ClientContactPersonVo findContactPersonById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ClientContactPerson clientContactPerson = clientContactPersonMapper.selectById(id);
        return BeanCopyUtils.objClone(clientContactPerson, ClientContactPersonVo::new);
    }
}
