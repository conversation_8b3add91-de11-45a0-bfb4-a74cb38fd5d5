package com.get.officecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.officecenter.vo.LeaveLogVo;
import com.get.officecenter.vo.StaffOfLeaveLogVo;
import com.get.officecenter.entity.LeaveLog;
import com.get.officecenter.mapper.LeaveLogMapper;
import com.get.officecenter.service.ILeaveApplicationFormTypeService;
import com.get.officecenter.service.LeaveLogService;
import com.get.officecenter.dto.LeaveLogDto;
import com.get.officecenter.dto.StaffOfLeaveLogDto;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2022/1/14
 * @TIME: 11:24
 * @Description:
 **/
@Service
public class LeaveLogServiceImpl extends GetServiceImpl<LeaveLogMapper,LeaveLog> implements LeaveLogService {
    @Resource
    private LeaveLogMapper leaveLogMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ILeaveApplicationFormTypeService leaveApplicationFormTypeService;

    @Override
    public LeaveLogVo findLeaveLogById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        LeaveLog leaveLog = leaveLogMapper.selectById(id);
        if (GeneralTool.isEmpty(leaveLog)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        return BeanCopyUtils.objClone(leaveLog, LeaveLogVo::new);

    }

    @Override
    public List<LeaveLogVo> getLeaveLogs(LeaveLogDto leaveLogDto, Page page) {
        LambdaQueryWrapper<LeaveLog> lambdaQueryWrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(leaveLogDto)) {
            if (GeneralTool.isNotEmpty(leaveLogDto.getFkCompanyId())) {
                lambdaQueryWrapper.eq(LeaveLog::getFkCompanyId, leaveLogDto.getFkCompanyId());
            }
            if (GeneralTool.isNotEmpty(leaveLogDto.getLeaveTypeKey())) {
                lambdaQueryWrapper.eq(LeaveLog::getLeaveTypeKey, leaveLogDto.getLeaveTypeKey());
            }
            if (GeneralTool.isNotEmpty(leaveLogDto.getOptTypeKey())) {
                lambdaQueryWrapper.eq(LeaveLog::getOptTypeKey, leaveLogDto.getOptTypeKey());
            }
            if (GeneralTool.isNotEmpty(leaveLogDto.getKeyWord())) {
                //模糊查询对应员工Ids
                Result<List<Long>> result = permissionCenterClient.getStaffIdsByNameKeyOrEnNameKey(leaveLogDto.getKeyWord());
                if (GeneralTool.isNotEmpty(result.getData())) {
                    lambdaQueryWrapper.in(LeaveLog::getFkStaffId, result.getData());
                }
            }
            if (GeneralTool.isNotEmpty(leaveLogDto.getBeginTime())) {
//                criteria.andGreaterThanOrEqualTo("gmtCreate", leaveLogDto.getBeginTime());
                lambdaQueryWrapper.ge(LeaveLog::getGmtCreate, leaveLogDto.getBeginTime());
            }
            if (GeneralTool.isNotEmpty(leaveLogDto.getEndTime())) {
                lambdaQueryWrapper.le(LeaveLog::getGmtCreate, leaveLogDto.getEndTime());
            }
        }
        lambdaQueryWrapper.orderByDesc(LeaveLog::getGmtCreate);
        IPage<LeaveLog> iPage = leaveLogMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        page.setAll((int) iPage.getTotal());

        List<LeaveLog> leaveLogs = iPage.getRecords();
        List<LeaveLogVo> datas = leaveLogs.stream().map(leaveStock -> BeanCopyUtils.objClone(leaveStock, LeaveLogVo::new)).collect(Collectors.toList());
        //公司名称
        Set<Long> fkCompanyIds = datas.stream().map(LeaveLogVo::getFkCompanyId).collect(Collectors.toSet());
        Map<Long, String> companyNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkCompanyIds)) {
            Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(fkCompanyIds);
            if (result.isSuccess() && result.getData() != null) {
                companyNamesByIds = result.getData();
            }
        }
        //员工名称
        Set<Long> fkStaffIds = datas.stream().map(LeaveLogVo::getFkStaffId).collect(Collectors.toSet());
        Map<Long, String> staffNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkStaffIds)) {
            Result<Map<Long, String>> result = permissionCenterClient.getStaffNameMap(fkStaffIds);
            if (result.isSuccess() && result.getData() != null) {
                staffNamesByIds = result.getData();
            }
        }
        for (LeaveLogVo leaveLogVo : datas) {
            //公司名
            leaveLogVo.setFkCompanyName(companyNamesByIds.get(leaveLogVo.getFkCompanyId()));
            //员工名
            leaveLogVo.setFkStaffName(staffNamesByIds.get(leaveLogVo.getFkStaffId()));
            //公休类型名称
            String leaveTypeName = leaveApplicationFormTypeService.getLeaveApplicationFormTypeNameByKey(leaveLogVo.getLeaveTypeKey());
            if (GeneralTool.isNotEmpty(leaveTypeName)) {
                leaveLogVo.setLeaveTypeName(leaveTypeName);
            }
            //操作类型名
            if (GeneralTool.isNotEmpty(leaveLogVo.getOptTypeKey())) {
                leaveLogVo.setOptTypeName(ProjectKeyEnum.getValue(leaveLogVo.getOptTypeKey()));
            }
        }
        return datas;
    }

    @Override
    public void addLeaveLog(LeaveLogDto leaveLogDto) {
        if (GeneralTool.isEmpty(leaveLogDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        LeaveLog leaveLog = BeanCopyUtils.objClone(leaveLogDto, LeaveLog::new);

        utilService.updateUserInfoToEntity(leaveLog);
        leaveLogMapper.insert(leaveLog);
    }

    @Override
    public List<LeaveLogVo> getLeaveLogByfkStaffIds(Set<Long> fkStaffIds, Long fkCompanyId, Date startTime, Date endTime){
        if (GeneralTool.isEmpty(fkStaffIds)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //获取公休日志记录(不包含已撤销记录)
        List<LeaveLogVo> dataList = leaveLogMapper.getLeaveLog(fkStaffIds,fkCompanyId,startTime,endTime,ProjectExtraEnum.APPROVAL_FINISHED.key);
        return dataList;
    }


    @Override
    public Map<Long, BigDecimal> getLeaveLogsAboutTakeDeferredHoliday(Set<Long> fkStaffIds,Long fkCompanyId, Date endTime) {
        if (GeneralTool.isEmpty(fkStaffIds)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<LeaveLogVo> sumDurationMapList = leaveLogMapper.getLeaveLogsAboutTakeDeferredHoliday(fkStaffIds,fkCompanyId, endTime, ProjectExtraEnum.APPROVAL_FINISHED.key);
        Map<Long, BigDecimal> data = new HashMap<>();
        if (GeneralTool.isEmpty(sumDurationMapList)) {
            return data;
        }
        for (LeaveLogVo dto : sumDurationMapList) {
            data.put(dto.getFkStaffId(), dto.getDurationSum());
        }
        return data;
    }


    @Override
    public Map<Long, BigDecimal> getLeaveLogsAboutAnnualVacationLastYear(Set<Long> fkStaffIds,Long fkCompanyId,Date endTime, Date date) {
        if (GeneralTool.isEmpty(fkStaffIds)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<LeaveLogVo> sumDurationMapList = leaveLogMapper.getLeaveLogsAboutAnnualVacationLastYear(fkStaffIds,fkCompanyId,endTime,date,ProjectExtraEnum.APPROVAL_FINISHED.key);
        Map<Long, BigDecimal> data = new HashMap<>();
        if (GeneralTool.isEmpty(sumDurationMapList)) {
            return data;
        }
        for (LeaveLogVo dto : sumDurationMapList) {
            data.put(dto.getFkStaffId(), dto.getDurationSum());
        }
        return data;
    }

    @Override
    public Map<Long, BigDecimal> getLeaveLogsAboutAnnualVacationThisYear(Set<Long> fkStaffIds,Long fkCompanyId,Date endTime, Date date) {
        if (GeneralTool.isEmpty(fkStaffIds)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<LeaveLogVo> sumDurationMapList = leaveLogMapper.getLeaveLogsAboutAnnualVacationThisYear(fkStaffIds,fkCompanyId,endTime,date,ProjectExtraEnum.APPROVAL_FINISHED.key);
        Map<Long, BigDecimal> data = new HashMap<>();
        if (GeneralTool.isEmpty(sumDurationMapList)) {
            return data;
        }
        for (LeaveLogVo dto : sumDurationMapList) {
            data.put(dto.getFkStaffId(), dto.getDurationSum());
        }
        return data;
    }

    @Override
    public Map<Long, BigDecimal> getLeaveLogsAboutDiseaseVacationQuarter(Set<Long> fkStaffIds,Set<Long> fkLeaveStockIds, Long fkCompanyId, Date endTime) {
        if (GeneralTool.isEmpty(fkStaffIds)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<LeaveLogVo> sumDurationMapList = leaveLogMapper.getLeaveLogsAboutDiseaseVacationQuarter(fkStaffIds,fkLeaveStockIds, fkCompanyId, endTime, ProjectExtraEnum.APPROVAL_FINISHED.key);
        Map<Long, BigDecimal> data = new HashMap<>();
        if (GeneralTool.isEmpty(sumDurationMapList)) {
            return data;
        }
        for (LeaveLogVo dto : sumDurationMapList) {
            data.put(dto.getFkStaffId(), dto.getDurationSum());
        }
        return data;
    }


    @Override
    public List<LeaveLogVo> getAnnualVacationLastYearLog(Set<Long> fkStaffIds, Long fkCompanyId, Date startTime, Date endTime, Date date) {
        if (GeneralTool.isEmpty(fkStaffIds)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //获取考勤时间范围已休上一年年假记录
        List<LeaveLogVo> dataList = leaveLogMapper.getAnnualVacationLastYearLog(fkStaffIds,fkCompanyId,startTime,endTime,date,"annualVacation",
                ProjectKeyEnum.ANNUAL_LEAVE_DEDUCTION.key,ProjectExtraEnum.APPROVAL_FINISHED.key);
        return dataList;
    }

    @Override
    public List<LeaveLogVo> getAnnualVacationThisYearLog(Set<Long> fkStaffIds, Long fkCompanyId, Date startTime, Date endTime, Date date) {
        if (GeneralTool.isEmpty(fkStaffIds)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //获取考勤时间范围已休今年年假记录
        List<LeaveLogVo> dataList = leaveLogMapper.getAnnualVacationThisYearLog(fkStaffIds,fkCompanyId,startTime,endTime,date,"annualVacation",
                ProjectKeyEnum.ANNUAL_LEAVE_DEDUCTION.key,ProjectExtraEnum.APPROVAL_FINISHED.key);
        return dataList;
    }

    @Override
    public List<LeaveLogVo> getSystemLeaveLog(LeaveLogDto leaveLogDto) {
        LambdaQueryWrapper<LeaveLog> lambdaQueryWrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(leaveLogDto)) {
            if (GeneralTool.isNotEmpty(leaveLogDto.getFkCompanyId())) {
                lambdaQueryWrapper.eq(LeaveLog::getFkCompanyId, leaveLogDto.getFkCompanyId());
            }
            if (GeneralTool.isNotEmpty(leaveLogDto.getLeaveTypeKey())) {
                lambdaQueryWrapper.eq(LeaveLog::getLeaveTypeKey, leaveLogDto.getLeaveTypeKey());
            }
            if (GeneralTool.isNotEmpty(leaveLogDto.getOptTypeKey())) {
                lambdaQueryWrapper.eq(LeaveLog::getOptTypeKey, leaveLogDto.getOptTypeKey());
            }
            if (GeneralTool.isNotEmpty(leaveLogDto.getFkLeaveStockId())) {
                lambdaQueryWrapper.eq(LeaveLog::getFkLeaveStockId, leaveLogDto.getFkLeaveStockId());
            }
            if (GeneralTool.isNotEmpty(leaveLogDto.getFkLeaveApplicationFormId())) {
                lambdaQueryWrapper.eq(LeaveLog::getFkLeaveApplicationFormId, leaveLogDto.getFkLeaveApplicationFormId());
            }
            if (GeneralTool.isNotEmpty(leaveLogDto.getKeyWord())) {
                //模糊查询对应员工Ids
//                List<Long> fkStaffIds = permissionCenterClient.getStaffIdsByNameKeyOrEnNameKey(leaveLogDto.getKeyWord());
                Result<List<Long>> result = permissionCenterClient.getStaffIdsByNameKeyOrEnNameKey(leaveLogDto.getKeyWord());
                if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
                    lambdaQueryWrapper.in(LeaveLog::getFkStaffId, result.getData());
                }
            }
        }
        List<LeaveLog> leaveLogs = leaveLogMapper.selectList(lambdaQueryWrapper);
        return BeanCopyUtils.copyListProperties(leaveLogs, LeaveLogVo::new);
    }

    /**
     * 移动端休假记录员工列表
     *
     * @param staffOfLeaveLogDto
     * @param page
     * @return
     */
    @Override
    public List<StaffOfLeaveLogVo> getStaffOfLeaveLogList(StaffOfLeaveLogDto staffOfLeaveLogDto, Page page) {
        if (GeneralTool.isEmpty(staffOfLeaveLogDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(staffOfLeaveLogDto.getFkCompanyId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }

        IPage<StaffOfLeaveLogVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<StaffOfLeaveLogVo> staffOfLeaveStockDtos = leaveLogMapper.getStaffOfLeaveLogList(iPage, staffOfLeaveLogDto);
        page.setAll((int) iPage.getTotal());

        return staffOfLeaveStockDtos;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean addSystemLeaveLog(LeaveLogDto leaveLogDto) {
        if (GeneralTool.isEmpty(leaveLogDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        LeaveLog leaveLog = new LeaveLog();
        BeanUtils.copyProperties(leaveLogDto, leaveLog);
        leaveLogMapper.insert(leaveLog);
        return true;
    }


}
