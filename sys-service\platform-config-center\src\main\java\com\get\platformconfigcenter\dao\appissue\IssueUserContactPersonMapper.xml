<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.platformconfigcenter.dao.appissue.IssueUserContactPersonMapper">
    <resultMap id="BaseResultMap" type="com.get.platformconfigcenter.entity.IssueUserContactPerson">
        <result column="fk_user_id" jdbcType="BIGINT" property="fkUserId"/>
        <result column="fk_contact_person_id" jdbcType="BIGINT" property="fkContactPersonId"/>

    </resultMap>
    <sql id="Base_Column_List">
    id, fk_user_id, fk_contact_person_id, gmt_create, gmt_create_user, gmt_modified, 
    gmt_modified_user
  </sql>
</mapper>