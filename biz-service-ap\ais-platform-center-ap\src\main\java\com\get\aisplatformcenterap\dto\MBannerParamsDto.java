package com.get.aisplatformcenterap.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.aisplatformcenterap.entity.MBannerEntity;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class MBannerParamsDto  extends MBannerEntity {

    @JsonSerialize(
            using = ToStringSerializer.class
    )
    @ApiModelProperty("主键id")
    private @NotNull(
            message = "主键不能为空！",
            groups = {BaseVoEntity.Update.class}
    ) Long id;


    /**
     * 文件guid(文档中心)
     */
    private String fileGuid;


    private String mMageAddress;

}
