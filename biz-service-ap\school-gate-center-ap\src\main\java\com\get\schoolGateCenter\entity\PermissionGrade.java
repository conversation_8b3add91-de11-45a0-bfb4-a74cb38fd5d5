package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.type.Alias;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_permission_grade")
@Alias("SchoolGatePermissionGrade")
public class PermissionGrade extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;
    /**
     * 权限级别编号
     */
    @ApiModelProperty(value = "权限级别编号")
    @Column(name = "grade_num")
    private String gradeNum;
    /**
     * 权限级别名称
     */
    @ApiModelProperty(value = "权限级别名称")
    @Column(name = "grade_name")
    private String gradeName;
    /**
     * 级别等级
     */
    @ApiModelProperty(value = "级别等级")
    @Column(name = "grade_level")
    private Integer gradeLevel;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}