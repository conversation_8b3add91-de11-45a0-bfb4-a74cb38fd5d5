package com.get.institutioncenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.entity.ContractFormulaAreaCountryStudent;
import com.get.institutioncenter.dto.ContractFormulaAreaCountryStudentDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/3/29 11:34
 * @verison: 1.0
 * @description:
 */
public interface IContractFormulaAreaCountryStudentService extends BaseService<ContractFormulaAreaCountryStudent> {
    /**
     * @return java.lang.Long
     * @Description :新增
     * @Param [contractFormulaAreaCountryVo]
     * <AUTHOR>
     */
    Long addContractFormulaAreaCountryStudent(ContractFormulaAreaCountryStudentDto contractFormulaAreaCountryVo);

    /**
     * @return void
     * @Description :根据contractFormulaId删除
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    void deleteByFkid(Long contractFormulaId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过合同公式id 查找对应学生来源国家ids
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<Long> getStudentCountryIdListByFkid(Long contractFormulaId);

    /**
     * @return java.lang.String
     * @Description :通过合同公式id 查找对应学生来源国家名称
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    String getStudentCountryNameByFkid(Long contractFormulaId);
}
