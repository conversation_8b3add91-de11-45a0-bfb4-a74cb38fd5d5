package com.get.platformconfigcenter.dao.appissue;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.platformconfigcenter.entity.StudentInstitutionCourse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
@DS("issuedb")
public interface StudentInstitutionCourseMapper extends GetMapper<StudentInstitutionCourse> {

//    int insertSelective(StudentInstitutionCourse record);
//
//    /**
//     * 根据课程id验证是否存在
//     *
//     * @return
//     */
//    boolean isExistByCourseId(@Param("courseId") Long courseId);
}