package com.get.pmpcenter.vo.institution;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author:<PERSON>
 * @Date: 2025/2/12  10:05
 * @Version 1.0
 * 学校供应商
 */
@Data
public class ProviderVo {

    @ApiModelProperty(value = "学校提供商Id")
    private Long institutionProviderId;

    @ApiModelProperty(value = "学校提供商名称")
    private String name;

    @ApiModelProperty(value = "学校提供商中文名称")
    private String nameChn;

    @ApiModelProperty(value = "代理端已保存的代理方案数量")
    private Integer allPlanCount;

    @ApiModelProperty(value = "合同端的数量")
    private Integer activePlanCount;
}
