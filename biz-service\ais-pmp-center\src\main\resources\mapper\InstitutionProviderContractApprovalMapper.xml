<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.pmpcenter.mapper.InstitutionProviderContractApprovalMapper">

    <select id="getApprovalList" resultType="com.get.pmpcenter.entity.InstitutionProviderContractApproval">
        select a.*,
               s.name as staffName
        from m_institution_provider_contract_approval a
                 left join ais_permission_center.m_staff s on a.fk_staff_id = s.id
        where a.fk_institution_provider_contract_id = #{contractId}
        order by a.gmt_create desc
    </select>
</mapper>