package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 学生最高最低状态dto
 *
 * <AUTHOR>
 * @date 2023/5/16 14:33
 */
@Data
public class StudentItemStatusVo {

    @ApiModelProperty(value = "学生id")
    private Long id;

    @ApiModelProperty(value = "学生最高状态")
    private Integer maxStepOrder;

    @ApiModelProperty(value = "学生最高状态名")
    private String maxStepOrderName;

    @ApiModelProperty(value = "学生最低状态")
    private Integer minStepOrder;

    @ApiModelProperty(value = "学生最低状态名")
    private String minStepOrderName;

}
