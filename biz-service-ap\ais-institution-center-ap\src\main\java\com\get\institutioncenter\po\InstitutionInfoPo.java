package com.get.institutioncenter.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.common.annotion.TableDto;
import com.get.institutioncenter.entity.InstitutionInfo;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("学校资讯返回类")
public class InstitutionInfoPo extends InstitutionInfo {
    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String institutionName;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    @TableDto(tableName = "u_info_type", columnDto = "type_name", entityColumnDto = "infoTypeName", columnDtoMainId = "fk_info_type_id")
    private String infoTypeName;
    /**
     * 文件下载链接
     */
    @ApiModelProperty(value = "资讯附件")
    private List<MediaAndAttachedVo> mediaAndAttachedDtos;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;
    /**
     * 公开对象名称
     */
    @ApiModelProperty(value = "公开对象名称")
    private String publicLevelName;

    //=============实体类InstitutionInfo========================
    private static final long serialVersionUID = 1L;
    /**
     * 资讯类型Id
     */
    @ApiModelProperty(value = "资讯类型Id")
    @Column(name = "fk_info_type_id")
    private Long fkInfoTypeId;
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @Column(name = "title")
    private String title;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @Column(name = "public_level")
    private String publicLevel;
    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    @Column(name = "publish_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date publishTime;
    /**
     * 内容描述
     */
    @ApiModelProperty(value = "内容描述")
    @Column(name = "description")
    private String description;
    /**
     * 简介
     */
    @ApiModelProperty(value = "简介")
    @Column(name = "profile")
    private String profile;
    /**
     * 网页标题
     */
    @ApiModelProperty(value = "网页标题")
    @Column(name = "web_title")
    private String webTitle;
    /**
     * 网页Meta描述
     */
    @ApiModelProperty(value = "网页Meta描述")
    @Column(name = "web_meta_description")
    private String webMetaDescription;
    /**
     * 网页Meta关键字
     */
    @ApiModelProperty(value = "网页Meta关键字")
    @Column(name = "web_meta_keywords")
    private String webMetaKeywords;
}
