package com.get.salecenter.controller;

import com.get.salecenter.service.MAppAgentSignatureService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  app代理签名 控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/mAppAgentSignature")
@Api(tags = "app代理签名接口", value = "app代理签名接口")
public class MAppAgentSignatureController {

    private final MAppAgentSignatureService mAppAgentSignatureService;

} 