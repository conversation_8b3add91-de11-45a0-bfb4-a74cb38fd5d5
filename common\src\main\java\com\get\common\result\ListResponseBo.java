package com.get.common.result;

import com.get.common.utils.BeanCopyUtils;
import lombok.Data;

import java.util.Collection;
import java.util.List;

/**
 * 查询列表数据-响应类
 *
 * <AUTHOR>
 * create: 2020-4-15
 * verison: 1.0
 * description: 查询列表数据-响应类
 */
@Data
public class ListResponseBo<T> extends ResponseBo<T> {

    private static final String DATA = "datas";

    private static final String PAGE = "page";
    private static final long serialVersionUID = -7825443374905082532L;

    //列表主sql执行时间
    private String oTime;
    //远程调用时间
    private String fTime;


    private T datas;
    private Page page;

    public ListResponseBo() {
    }

    public ListResponseBo(Collection<T> list) {
        super();
        setData(list);
    }

    public ListResponseBo(Collection<T> list, String oTime, String fTime) {
        super();
        setData(list);
        this.oTime = oTime;
        this.fTime = fTime;
    }

    public ListResponseBo(Collection<T> list, Page page, String oTime, String fTime) {
//        put(DATA, (T) list);
        this.datas = (T) list;
        Page p = null;
        if (page != null) {
            this.page = page;
            p = BeanCopyUtils.objClone(page, Page::new);
        }
        this.page = p;
        this.oTime = oTime;
        this.fTime = fTime;
    }

    public ListResponseBo(Collection<T> list, Page page) {
//        put(DATA, (T) list);
        this.datas = (T) list;
        Page p = null;
        if (page != null) {
            this.page = page;
            p = BeanCopyUtils.objClone(page, Page::new);
        }
        this.page = p;
    }

    /**
     * 查询成功，返回列表数据及分页数据
     *
     * @param list 要返回的列表数据
     * @param page 要返回的分页信息数据
     * @param <T>  返回列表的实体
     * @return 返回列表数据和分页信息数据
     */
    public static <T> ListResponseBo ok(Collection<T> list, Page page) {
        return new ListResponseBo(list, page);
    }

    public static <T> ListResponseBo ok(Collection<T> list, Page page, String oTime, String fTime) {
        return new ListResponseBo(list, page, oTime, fTime);
    }

    public void setData(Collection<T> data) {
//        put(DATA, (T) data);
        this.datas = (T) data;
    }

    public void setData(Collection<T> data, String oTime, String fTime) {
//        put(DATA, (T) data);
        this.datas = (T) data;
        this.oTime = oTime;
        this.fTime = fTime;
    }


    public List<T> getDatas() {
        return (List<T>) datas;
    }
}
