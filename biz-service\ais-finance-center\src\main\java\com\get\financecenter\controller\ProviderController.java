package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.financecenter.dto.MediaAndAttachedDto;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import com.get.financecenter.vo.ProviderVo;
import com.get.financecenter.service.IProviderService;
import com.get.financecenter.dto.ProviderDto;
import com.get.financecenter.dto.query.ProviderQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/12/18 10:12
 * @verison: 1.0
 * @description: 供应商管理控制器
 */
@Api(tags = "供应商管理")
@RestController
@RequestMapping("finance/provider")
public class ProviderController {
    @Resource
    private IProviderService providerService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ProviderDto>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/供应商管理/供应商详情")
    @GetMapping("/{id}")
    public ResponseBo<ProviderVo> detail(@PathVariable("id") Long id) {
        ProviderVo data = providerService.findProviderById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增信息
     * @Param [providerVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/供应商管理/新增供应商")
    @PostMapping("add")
    public ResponseBo add(@RequestBody  ProviderDto providerDto) {
        return SaveResponseBo.ok(providerService.addProvider(providerDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/供应商管理/删除供应商")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        providerService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ProviderDto>
     * @Description :修改信息
     * @Param [providerVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/供应商管理/更新供应商")
    @PostMapping("update")
    public ResponseBo<ProviderVo> update(@RequestBody @Validated(ProviderDto.Update.class)  ProviderDto providerDto) {
        return UpdateResponseBo.ok(providerService.updateProvider(providerDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ProviderDto>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/供应商管理/查询供应商")
    @PostMapping("datas")
    public ResponseBo<ProviderVo> datas(@RequestBody SearchBean<ProviderQueryDto> page) {
        List<ProviderVo> datas = providerService.getProviders(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description : 查询供应商附件
     * @Param [voSearchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "查询供应商附件", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/供应商管理/查询附件")
    @PostMapping("getItemMedia")
    public ResponseBo<FMediaAndAttachedVo> getItemMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<FMediaAndAttachedVo> staffMedia = providerService.getItemMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description : 保存供应商附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "保存供应商附件")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/供应商管理/保存附件")
    @PostMapping("upload")
    public ResponseBo<FMediaAndAttachedVo> addAgentMedia(@RequestBody @Validated(MediaAndAttachedDto.Add.class)  ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(providerService.addItemMedia(mediaAttachedVo));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description :供应商下拉
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "供应商下拉", notes = "")
    @GetMapping("getOtherTypeSelect")
    public ResponseBo<BaseSelectEntity> getProviderSelect() {
        return new ListResponseBo<>(providerService.getProviderSelect());
    }

}
