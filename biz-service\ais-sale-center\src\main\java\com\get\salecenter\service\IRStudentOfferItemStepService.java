package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.salecenter.vo.MainCourseConditionVo;
import com.get.salecenter.vo.RStudentOfferItemStepVo;
import com.get.salecenter.vo.RStudentOfferItemStepSaveVo;
import com.get.salecenter.entity.RStudentOfferItemStep;
import com.get.salecenter.dto.RStudentOfferItemStepUpRemarkDto;
import com.get.salecenter.dto.RStudentOfferItemStepDto;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/11/18
 * @TIME: 16:31
 * @Description:
 **/
public interface IRStudentOfferItemStepService extends IService<RStudentOfferItemStep> {

    /**
     * @return java.util.List<com.get.salecenter.vo.StudentOfferItemStepVo>
     * @Description :列表
     * @Param [studentOfferItemStepVo, page]
     * <AUTHOR>
     */
    List<RStudentOfferItemStepVo> getReStudentStepLog(RStudentOfferItemStepDto studentOfferItemStepVo, Page page);


    /**
     * @return java.lang.Long
     * @Description :新增
     * @Param [studentOfferItemStepVos]
     * <AUTHOR>
     */
    Long addReStudentOfferItemStep(RStudentOfferItemStepDto studentOfferItemStepVo);

    /**
     * @return java.lang.Long
     * @Description:保存记录申请步骤
     * @Param [studentOfferItemStepVo]
     * <AUTHOR>
     */
    RStudentOfferItemStepSaveVo saveReStudentOfferItemStep(RStudentOfferItemStepDto studentOfferItemStepVo);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.salecenter.vo.StudentOfferItemStepVo
     * @Description :修改
     * @Param [studentOfferItemStepVo]
     * <AUTHOR>
     */
    RStudentOfferItemStepVo updateReStudentOfferItemStep(RStudentOfferItemStepDto studentOfferItemStepVo);


    /**
     * @Description: 修改申请步骤日志备注
     * @Author: Jerry
     * @Date:11:43 2021/8/17
     */
    RStudentOfferItemStepVo updateApplyStepLogRemark(RStudentOfferItemStepUpRemarkDto rStudentOfferItemStepUpRemarkDto);

    /**
     * @Description: 查询申请计划id
     * @Author: jack
     * @Date:11:43 2021/8/17
     */
    List<Long> selectItemIdByStepIdAndGmtCreate(RStudentOfferItemStepDto RStudentOfferItemStepDto);

    Boolean insertRSteps(List<RStudentOfferItemStep> childRStudentOfferItemSteps);

    void batchOneClickSubmission(Set<Long> itemIds);

    Map<Long, Date> failureIds(Set<Long> itemIds);

    /**
     * 获取最新备注
     * @param ids
     * @return
     */
    Map<Long, String> getLastRemarkByIds(Set<Long> ids);

    void deleteStepLog(Long id);

    MainCourseConditionVo isUkMainCourse(Long id);
}
