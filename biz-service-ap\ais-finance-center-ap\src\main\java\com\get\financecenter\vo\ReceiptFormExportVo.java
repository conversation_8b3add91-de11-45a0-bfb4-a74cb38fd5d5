package com.get.financecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　　　◆　　　　　　　　　　　　◆　　　　　　　　　　　　　　◆　　　　　　　　　　　　　◆◆　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　◆◆◆　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆
 * 　　　◆◆　　　　　◆◆　　　　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆◆◆◆
 * 　　　◆◆◆　　　◆◆◆　　　　　　　◆◆　　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆◆
 * 　　　　◆◆◆　◆◆◆　　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆
 * 　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　　◆◆
 * 　　　　　　◆◆◆　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆
 * 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆◆
 * <p>
 * Time: 16:17
 * Date: 2021/12/20
 * Description:收款单导出类
 */
@Data
public class ReceiptFormExportVo {

    /**
     * 所属公司
     */
    @ApiModelProperty(value = "所属公司")
    private String fkCompanyName;
    /**
     * 目标类型
     */
    @ApiModelProperty(value = "目标类型")
    private String fkTypeKeyName;
    /**
     * 目标名称
     */
    @ApiModelProperty(value = "目标名称")
    private String targetName;
    /**
     * 系统编号
     */
    @ApiModelProperty(value = "系统编号")
    private String numSystem;
    /**
     * 银行编号
     */
    @ApiModelProperty(value = "银行编号")
    private String numBank;
    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String fkCurrencyTypeName;
    /**
     * 收款金额
     */
    @ApiModelProperty(value = "收款金额")
    private String amountStr;

    /**
     * 发票
     */
    @ApiModelProperty(value = "发票")
    private String fkInvoiceNum;

    /**
     * 发票
     */
    @ApiModelProperty(value = "发票")
    private String invoiceName;
    /**
     * 绑定应收金额
     */
    @ApiModelProperty(value = "绑定应收金额")
    private String amountReceiptStr;
    /**
     * 差额
     */
    @ApiModelProperty(value = "差额")
    private String diffAmountStr;
    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String statusName;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String gmtCreateUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private String gmtCreateDate;
}
