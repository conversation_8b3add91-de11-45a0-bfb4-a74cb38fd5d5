package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 奖励奖品管理
 * <AUTHOR>
 * @since 2023-03-13
 */
@Data
@TableName("u_incentive_reward")
@ApiModel(value="UIncentiveReward对象", description="")
public class IncentiveReward extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "奖品名称")
    private String rewardName;

    @ApiModelProperty(value = "奖品关键字")
    private String rewardKey;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
}
