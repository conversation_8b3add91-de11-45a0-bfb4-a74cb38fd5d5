package com.get.permissioncenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.dao.PositionMapper;
import com.get.permissioncenter.dto.PositionDto;
import com.get.permissioncenter.vo.*;
import com.get.permissioncenter.vo.PositionVo;
import com.get.permissioncenter.vo.tree.DepartmentTreeVo;
import com.get.permissioncenter.vo.tree.PositionTreeVo;
import com.get.permissioncenter.vo.tree.StaffTreeVo;
import com.get.permissioncenter.entity.Position;
import com.get.permissioncenter.service.ICompanyService;
import com.get.permissioncenter.service.IDeleteService;
import com.get.permissioncenter.service.IDepartmentService;
import com.get.permissioncenter.service.IPositionService;
import com.get.permissioncenter.service.IStaffService;
import com.get.permissioncenter.utils.MyStringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/6/29
 * @TIME: 16:32
 **/
@Service
public class PositionServiceImpl extends BaseServiceImpl<PositionMapper, Position> implements IPositionService {
    @Resource
    private PositionMapper positionMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private ICompanyService companyService;
    @Resource
    private IDepartmentService departmentService;
    @Resource
    @Lazy
    private IStaffService staffService;
    @Resource
    private IDeleteService deleteService;

    @Override
    public List<CompanyParallelVo> getCompanyParallelDto(Boolean showStaff, Long companyId) {
        if (GeneralTool.isEmpty(showStaff)) {
            showStaff = false;
        }
        //查询所有公司
        List<CompanyVo> companys = companyService.getAllCompanys(companyId);
        List<CompanyParallelVo> companyParallelVos =
                companys.stream().map(companyDto -> BeanCopyUtils.objClone(companyDto, CompanyParallelVo::new)).collect(Collectors.toList());
        if (GeneralTool.isNotEmpty(companyParallelVos)) {
            for (CompanyParallelVo entity : companyParallelVos) {
                //获取公司的部门信息
                List<DepartmentTreeVo> departmentTreeList = getDepartmentTreeList(entity.getId(), showStaff);
                entity.setDepartmentTree(departmentTreeList);
                entity.setTotalNum(departmentTreeList.size());
            }
        }
        return companyParallelVos;
    }

    @Override
    public PositionVo findPositionById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("position_id_null"));
        }
//        Position position = positionMapper.selectByPrimaryKey(id);
        Position position = positionMapper.selectById(id);
//        if (GeneralTool.isNotEmpty(position)) {
////            SecureUtil.validateCompany(position.getFkCompanyId());
//            if(!SecureUtil.validateCompany(position.getFkCompanyId()))
//            {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
//            }
//        }
        return BeanCopyUtils.objClone(position, PositionVo::new);
    }

    @Override
    public Map<Long, String> getPositionNamesByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        List<Position> positions = this.list(Wrappers.<Position>query().lambda().in(Position::getId, ids));
        if (GeneralTool.isEmpty(positions)) {
            return map;
        }
        for (Position position : positions) {
            map.put(position.getId(), position.getName());
        }
        return map;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAddPosition(List<PositionDto> positionDtoList) {
        if (GeneralTool.isEmpty(positionDtoList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (PositionDto positionDto : positionDtoList) {
            if (validateChange(positionDto)) {
                Position position = BeanCopyUtils.objClone(positionDto, Position::new);
                utilService.updateUserInfoToEntity(position);
                Integer maxViewOrder = positionMapper.getMaxViewOrder();
                position.setViewOrder(maxViewOrder);
                positionMapper.insertSelective(position);
            } else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("num_exist"));
            }
        }
    }

    @Override
    public PositionVo updatePositionVo(PositionDto positionDto) {
        if (GeneralTool.isEmpty(positionDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Long aLong = validateUpdate(positionDto);
        if (!positionDto.getId().equals(aLong)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
//        Position position = Tools.objClone(positionDto, Position.class);
//        utilService.setUpdateInfo(position);
//        positionMapper.updateByPrimaryKeySelective(position);
        Position position = BeanCopyUtils.objClone(positionDto, Position::new);
        utilService.updateUserInfoToEntity(position);
        positionMapper.updateById(position);
        return findPositionById(position.getId());
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        deleteService.deleteValidatePosition(id);
//        int i = positionMapper.deleteByPrimaryKey(id);
        int i = positionMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<PositionDto> positionDtos) {
        if (GeneralTool.isEmpty(positionDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
//        Position position1 = Tools.objClone(positionDtos.get(0), Position.class);
//        Position position2 = Tools.objClone(positionDtos.get(1), Position.class);
        Position position1 = BeanCopyUtils.objClone(positionDtos.get(0), Position::new);
        Position position2 = BeanCopyUtils.objClone(positionDtos.get(1), Position::new);
        Integer viewOrder = position1.getViewOrder();
        position1.setViewOrder(position2.getViewOrder());
        position2.setViewOrder(viewOrder);
//        utilService.setUpdateInfo(position1);
//        utilService.setUpdateInfo(position2);
//        positionMapper.updateByPrimaryKeySelective(position1);
//        positionMapper.updateByPrimaryKeySelective(position2);
        utilService.updateUserInfoToEntity(position1);
        utilService.updateUserInfoToEntity(position2);
        positionMapper.updateById(position1);
        positionMapper.updateById(position2);

    }

    @Override
    public List<PositionVo> getPositions(PositionDto positionDto, Page page) {
        if (GeneralTool.isEmpty(positionDto) || GeneralTool.isEmpty(positionDto.getFkCompanyId())) {
            List<Long> companyIds = getCompanyIds();
            positionDto.setFkCompanyIds(companyIds);
        }
        if (GeneralTool.isNotEmpty(positionDto)) {
            if (GeneralTool.isNotEmpty(positionDto.getFkCompanyId())) {
//                SecureUtil.validateCompany(positionDto.getFkCompanyId());
                if (!SecureUtil.validateCompany(positionDto.getFkCompanyId())) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
                }
            }
        }
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<Position> positions = positionMapper.getPositions(positionDto);
//        page.restPage(positions);
//        LambdaQueryWrapper<Position> wrapper = new LambdaQueryWrapper();
        IPage<Position> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<Position> positions = positionMapper.getPositions(iPage, positionDto);
//       IPage<Position> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(),page.getShowCount())), wrapper);
//        List<Position> positions = pages.getRecords();
        page.setAll((int) iPage.getTotal());
//        return positions.stream().map(position -> Tools.objClone(position, PositionVo.class)).collect(Collectors.toList());
        return BeanCopyUtils.copyListProperties(positions, PositionVo::new);
    }

    @Override
    public List<BaseSelectEntity> getPositionSelect(Long companyId, Long departmentId) {
        if (!SecureUtil.validateCompany(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        if (GeneralTool.isEmpty(departmentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("department_id_null"));
        }
        return positionMapper.getPositionSelect(companyId, departmentId);
    }

    /**
     * 通过员工ids 查找对应的职位编号
     *
     * @param ids
     * @return
     */
    @Override
    public Map<Long, String> getPositionNumByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        List<PositionVo> positionNumByIds = positionMapper.getPositionNumByIds(MyStringUtils.getSqlString(ids));
        if (GeneralTool.isEmpty(positionNumByIds)) {
            return map;
        }
        for (PositionVo positionNumById : positionNumByIds) {
            map.put(positionNumById.getFkStaffId(), positionNumById.getNum());
        }
        return map;
    }

    @Override
    public Map<String, List<Long>> getPositionNumAndStaffIdMap(Set<Long> ids) {
        List<StaffVo> staffVos = positionMapper.getPositionNumAndStaffIdMap(ids);
        Map<String, List<Long>> result = new HashMap<>();
        Map<String, List<StaffVo>> map = staffVos.stream().collect(Collectors.groupingBy(StaffVo::getPositionNum));
        for (String s : map.keySet()) {
            result.put(s, map.get(s).stream().map(StaffVo::getId).collect(Collectors.toList()));
        }
        return result;
    }

    @Override
    public List<PositionVo> getAllPosition(Long companyId, Long departId) {
        if (GeneralTool.isEmpty(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_id_null"));
        }
        if (GeneralTool.isEmpty(departId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("department_id_null"));
        }
        //权限校验
//        SecureUtil.validateCompany(companyId);
        if (!SecureUtil.validateCompany(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }

//        Example example = new Example(Position.class);
//        Example.Criteria criteria = example.createCriteria();
//
//        criteria.andEqualTo("fkCompanyId", companyId);
//        criteria.andEqualTo("fkDepartmentId", departId);
//        example.setOrderByClause("fk_department_id,view_order");
//        List<Position> positions = positionMapper.selectByExample(example);
//        return positions.stream().map(position -> Tools.objClone(position, PositionVo.class)).collect(Collectors.toList());

        List<Position> positions = positionMapper.selectList(Wrappers.<Position>query().lambda()
                .eq(Position::getFkCompanyId, companyId)
                .eq(Position::getFkDepartmentId, departId)
                .orderByAsc(Position::getViewOrder));
//                .orderBy(true, true, Position::getFkDepartmentId, Position::getViewOrder));

        return BeanCopyUtils.copyListProperties(positions, PositionVo::new);

    }

    //新增校验是否存在
    private boolean validateChange(PositionDto positionDto) {
//        Example example = new Example(Position.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("num", positionDto.getNum());
//        criteria.andEqualTo("fkCompanyId", positionDto.getFkCompanyId());
//        List<Position> positions = positionMapper.selectByExample(example);
        List<Position> positions = positionMapper.selectList(Wrappers.<Position>query().lambda()
                .eq(Position::getNum, positionDto.getNum())
                .eq(Position::getFkCompanyId, positionDto.getFkCompanyId()));

        return GeneralTool.isEmpty(positions);
    }

    //编辑校验是否存在
    private Long validateUpdate(PositionDto positionDto) {
//        Example example = new Example(Position.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("num", positionDto.getNum());
//        criteria.andEqualTo("fkCompanyId", positionDto.getFkCompanyId());
//        List<Position> positions = positionMapper.selectByExample(example);
        List<Position> positions = positionMapper.selectList(Wrappers.<Position>query().lambda()
                .eq(Position::getNum, positionDto.getNum())
                .eq(Position::getFkCompanyId, positionDto.getFkCompanyId()));
        return GeneralTool.isNotEmpty(positions) ? positions.get(0).getId() : positionDto.getId();
    }


    //获取同一个公司下的所有部门
    private List<DepartmentTreeVo> getDepartmentTreeList(Long companyId, boolean showStaff) {
        List<DepartmentVo> departMents = departmentService.getAllDepartments(companyId);
        List<DepartmentTreeVo> departmentTreeVos
                = departMents.stream().map(department -> BeanCopyUtils.objClone(department, DepartmentTreeVo::new)).collect(Collectors.toList());
        //当部门不为空时
        if (GeneralTool.isNotEmpty(departmentTreeVos)) {
            for (DepartmentTreeVo departmentTreeVo : departmentTreeVos) {
                //查询当前的部门的职位
                List<PositionTreeVo> positionTreeVos = getPositionTreeList(departmentTreeVo.getFkCompanyId(), departmentTreeVo.getId(), showStaff);
                departmentTreeVo.setPositionTreeList(positionTreeVos);
                departmentTreeVo.setTotalNum(positionTreeVos.size());
            }
        }
        return departmentTreeVos;
    }

    //获取同一个部门下的所有的职位
    private List<PositionTreeVo> getPositionTreeList(Long companyId, Long departId, boolean showStaff) {
        List<PositionVo> positionVos = getAllPosition(companyId, departId);
        List<PositionTreeVo> positionTreeVos
                = positionVos.stream().map(positionDto -> BeanCopyUtils.objClone(positionDto, PositionTreeVo::new)).collect(Collectors.toList());
        //当职位不为空时
        if (GeneralTool.isNotEmpty(positionTreeVos) && showStaff) {
            for (PositionTreeVo positionTreeVo : positionTreeVos) {
                //查询当前职位的所有员工
                List<StaffTreeVo> staffTreeVos = getStaffTreeList(positionTreeVo.getFkCompanyId(), positionTreeVo.getFkDepartmentId(), positionTreeVo.getId());
                positionTreeVo.setStaffTreeList(staffTreeVos);
                positionTreeVo.setTotalNum(staffTreeVos.size());
            }
        }
        return positionTreeVos;
    }

    //获取同一个职位下的所有的员工
    private List<StaffTreeVo> getStaffTreeList(Long companyId, Long departId, Long positionId) {
        List<StaffVo> staffVos = staffService.getAllStaffDto(companyId, departId, positionId);
        return staffVos.stream().map(staffDto -> BeanCopyUtils.objClone(staffDto, StaffTreeVo::new)).collect(Collectors.toList());
    }

    private List<Long> getCompanyIds() {
        List<Long> companyIds = SecureUtil.getCompanyIds();
        if (GeneralTool.isEmpty(companyIds)) {
            companyIds.add(0L);
        }
        return companyIds;
    }

}
