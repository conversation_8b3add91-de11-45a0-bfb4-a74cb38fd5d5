package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.ClientOfferStep;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Hardy
 * @create: 2023/12/13 12:13
 * @verison: 1.0
 * @description:
 */
@Data
public class ClientOfferStepVo extends BaseEntity {

    //=========实体类ClientOfferStep==============
    /**
     * 状态步骤名
     */
    @ApiModelProperty(value = "状态步骤名")
    @Column(name = "step_name")
    private String stepName;

    /**
     * 状态步骤key
     */
    @ApiModelProperty(value = "状态步骤key")
    @Column(name = "step_key")
    private String stepKey;

    /**
     * 状态步骤排序，由0开始按顺序排列
     */
    @ApiModelProperty(value = "状态步骤排序，由0开始按顺序排列")
    @Column(name = "step_order")
    private Integer stepOrder;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    private static final long serialVersionUID = 1L;
}
