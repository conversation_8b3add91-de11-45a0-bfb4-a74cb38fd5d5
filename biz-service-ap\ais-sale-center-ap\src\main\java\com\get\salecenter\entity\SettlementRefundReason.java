package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName(value = "u_settlement_refund_reason")
public class SettlementRefundReason extends BaseEntity implements Serializable {

    @ApiModelProperty("原因名称")
    private String reasonName;

    @ApiModelProperty("排序")
    private Integer viewOrder;

}
