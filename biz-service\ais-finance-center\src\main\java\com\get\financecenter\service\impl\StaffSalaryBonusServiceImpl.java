package com.get.financecenter.service.impl;


import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.ErrorCodeEnum;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.TemplateExcelUtils;
import com.get.financecenter.dao.StaffSalaryBonusMapper;
import com.get.financecenter.dto.query.StaffSalaryBonusQueryDto;
import com.get.financecenter.entity.BankAccount;
import com.get.financecenter.entity.ReceiptMethodType;
import com.get.financecenter.entity.StaffSalaryBonus;
import com.get.financecenter.service.StaffSalaryBonusService;
import com.get.financecenter.vo.ImportSalaryBonusVo;
import com.get.financecenter.vo.StaffSalaryBonusVo;
import com.get.permissioncenter.entity.Staff;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.salecenter.entity.KpiPlanGroup;
import com.get.salecenter.entity.KpiPlanGroupItem;
import com.get.salecenter.vo.KpiPlanGroupItemImportVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.UUID;
import java.util.stream.Collectors;

import static cn.hutool.poi.excel.cell.CellUtil.getCellValue;

@Service
@Slf4j
public class StaffSalaryBonusServiceImpl extends ServiceImpl<StaffSalaryBonusMapper, StaffSalaryBonus> implements StaffSalaryBonusService {


    @Autowired
    private StaffSalaryBonusMapper staffSalaryBonusMapper;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private UtilService utilService;



    @Override
    public void downloadTemplateFile(HttpServletResponse response, String type) {
        String fileName = null;
        if (type == null) {
            fileName = "SalaryTemplate.xls";
        }

        if (type.equals("salary")) { //工资
            fileName = "SalaryTemplate.xls";
        } else if (type.equals("bonus")) { //奖金
            fileName = "BonusTemplate.xls";
        }
        //设置文件路径
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setCharacterEncoding("UTF-8");

        // 输出流
        OutputStream outputStream = null;
        InputStream inputStream = null;
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            outputStream = response.getOutputStream();
            inputStream = TemplateExcelUtils.class.getClassLoader().getResourceAsStream("template/" + fileName);
            byte[] bytes = IOUtils.toByteArray(inputStream);
            outputStream.write(bytes);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (outputStream != null) {
                    outputStream.flush();
                    outputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public ImportSalaryBonusVo importSalaryBonus(MultipartFile file, String type) {
        InputStream inputStream = null;
        ImportSalaryBonusVo importSalaryBonusVo = new ImportSalaryBonusVo();
        try {
            inputStream = file.getInputStream();
            Workbook workbook = WorkbookFactory.create(inputStream);
            Sheet sheet = workbook.getSheetAt(0);
            String batchId = UUID.randomUUID().toString();
            List<Staff> staffList = permissionCenterClient.getAllStaff().getData();
            Map<String, Staff> staffMap = staffList.stream().collect(Collectors.toMap(Staff::getNum, staff -> staff));

            // 错误记录
            List<String> invalidNum = new ArrayList<>();
            List<String> invalidName = new ArrayList<>();
            List<String> invalidOther = new ArrayList<>();
            List<String> invalidYearMonth = new ArrayList<>();

            // 统计信息
            int totalCount = 0;
            int successCount = 0;
            int failCount = 0;

            // 获取表头行（第0行）
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                throw new RuntimeException("Excel 文件没有表头行！");
            }

            // 3. 遍历数据行（从第1行开始）
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;
                totalCount++;

                // 4. 解析基础字段
                String staffCode = (String) getCellValue(row.getCell(0)); // 工号
                String staffName = (String) getCellValue(row.getCell(2)); // 姓名
                String yearMonth = null;

                // 5. 动态解析表头并构建 JSON
                JSONObject detailJson = new JSONObject(true);
                BigDecimal amount = null;

                if (type.equals("salary")) {
                    // 获取年月（第3列）
                    Object yearMonthObj = getCellValue(row.getCell(3));
                    yearMonth = yearMonthObj != null ? yearMonthObj.toString() : "";

                    // 动态获取表头（从第4列开始）
                    for (int j = 4; j < headerRow.getLastCellNum(); j++) {
                        String headerName = getCellValue(headerRow.getCell(j)).toString();
                        Object cellValueObj = getCellValue(row.getCell(j));
                        String value = parseCellValue(cellValueObj);
                        detailJson.put(headerName, value);
                    }
                    amount = new BigDecimal(detailJson.getString("工资卡发放")); // 关键字段
                } else if (type.equals("bonus")) {
                    // 获取年月（第5列）
                    Object yearMonthObj = getCellValue(row.getCell(5));
                    yearMonth = yearMonthObj != null ? yearMonthObj.toString() : "";

                    // 动态获取表头（从第6列开始）
                    for (int j = 6; j < headerRow.getLastCellNum(); j++) {
                        String headerName = getCellValue(headerRow.getCell(j)).toString();
                        Object cellValueObj = getCellValue(row.getCell(j));
                        String value = parseCellValue(cellValueObj);
                        detailJson.put(headerName, value);
                    }
                    amount = new BigDecimal(detailJson.getString("总计")); // 关键字段
                }

                String remark = detailJson.getString("备注");

                // 6. 校验员工信息  工号
                Staff staff = staffMap.get(staffCode.trim());
                if (GeneralTool.isEmpty(staff)) {
                    invalidNum.add(staffCode + "," + staffName);
                    failCount++;
                    continue;
                }
                //员工姓名
                if (GeneralTool.isNotEmpty(staff) && !staffName.trim().equals(staff.getName())) {
                    invalidName.add(staffCode + "," + staffName);
                    failCount++;
                    continue;
                }
                //日期格式
                // 验证 yearMonth 格式
                if (!isValidYearMonthFormat(yearMonth)) {
                    invalidYearMonth.add(staffCode + "," + staffName);
                    failCount++;
                    continue;
                }


                // 7. 检查是否已存在记录，存在则删除
                LambdaQueryWrapper<StaffSalaryBonus> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(StaffSalaryBonus::getFkStaffId, staff.getId())
                        .eq(StaffSalaryBonus::getTypeKey, type)
                        .eq(StaffSalaryBonus::getYearMonth, yearMonth);
                StaffSalaryBonus existingRecord = staffSalaryBonusMapper.selectOne(wrapper);
                if (GeneralTool.isNotEmpty(existingRecord)) {
                    staffSalaryBonusMapper.deleteById(existingRecord.getId());
                }

                // 8. 保存新记录
                StaffSalaryBonus entity = new StaffSalaryBonus();
                entity.setTypeKey(type);
                entity.setFkStaffId(staff.getId());
                entity.setFkCompanyId(staff.getFkCompanyId());
                entity.setYearMonth(yearMonth);
                entity.setFkCurrencyTypeNum("CNY");
                entity.setAmount(amount);
                entity.setRemark(remark);
                entity.setJsonDetail(detailJson.toJSONString());
                entity.setImportFileName(batchId);
                utilService.setCreateInfo(entity);

                int insertCount = staffSalaryBonusMapper.insert(entity);
                if (insertCount > 0) {
                    successCount++;
                } else {
                    invalidOther.add(staffCode + "," + staffName);
                    failCount++;
                }
            }

            // 9. 返回导入结果
            importSalaryBonusVo.setTotalNum(totalCount);
            importSalaryBonusVo.setSuccessNum(successCount);
            importSalaryBonusVo.setFailNum(failCount);
            importSalaryBonusVo.setInvalidNum(invalidNum);
            importSalaryBonusVo.setInvalidName(invalidName);
            importSalaryBonusVo.setInvalidOther(invalidOther);
            importSalaryBonusVo.setInvalidYearMonth(invalidYearMonth);
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_type_error"));
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return importSalaryBonusVo;
    }

    /**
     * 解析单元格值，统一返回 String 类型
     */
    private String parseCellValue(Object cellValueObj) {
        if (cellValueObj == null) {
            return "";
        } else if (cellValueObj instanceof String) {
            return (String) cellValueObj;
        } else if (cellValueObj instanceof Double) {
            Double doubleValue = (Double) cellValueObj;
            return (doubleValue == doubleValue.intValue()) ?
                    String.valueOf(doubleValue.intValue()) :
                    String.valueOf(doubleValue);
        } else {
            return cellValueObj.toString();
        }
    }

    /**
     * 验证年月格式是否为 yyyyMM 格式
     * @param yearMonth 年月字符串
     * @return 是否有效
     */
    private boolean isValidYearMonthFormat(String yearMonth) {
        if (GeneralTool.isEmpty(yearMonth)) {
            return false;
        }

        // 去除空格
        yearMonth = yearMonth.trim();

        // 检查长度是否为6位
        if (yearMonth.length() != 6) {
            return false;
        }

        // 检查是否全为数字
        if (!yearMonth.matches("\\d{6}")) {
            return false;
        }

        try {
            int year = Integer.parseInt(yearMonth.substring(0, 4));
            int month = Integer.parseInt(yearMonth.substring(4, 6));

            // 检查年份范围(1900-2100)
            if (year < 1900 || year > 2100) {
                return false;
            }

            // 检查月份范围(01-12)
            if (month < 1 || month > 12) {
                return false;
            }

            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }



    @Override
    public List<StaffSalaryBonusVo> datas(StaffSalaryBonusQueryDto data, Page page) {
        if (GeneralTool.isEmpty(data)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        IPage<StaffSalaryBonusVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<StaffSalaryBonusVo> datas = staffSalaryBonusMapper.datas(pages,data);

        datas.forEach(item -> {
            if (item.getTypeKey().equals("salary")) {
                item.setTypeKeyName("工资");
            }else if (item.getTypeKey().equals("bonus")) {
                item.setTypeKeyName("奖金");
            }
            if (GeneralTool.isNotEmpty(item.getFkCurrencyTypeNum())&& item.getFkCurrencyTypeNum().equals("CNY")) {
                item.setFkCurrencyTypeNum(item.getFkCurrencyTypeNum()+" (人民币)");
            }
        });
        page.setAll((int) pages.getTotal());
        return datas;
    }

    @Override
    public StaffSalaryBonusVo findStaffSalaryBonusById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StaffSalaryBonusVo staffSalaryBonusVo = staffSalaryBonusMapper.findStaffSalaryBonusById(id);
        staffSalaryBonusVo.setTypeKeyName(staffSalaryBonusVo.getTypeKey().equals("salary") ? "工资" : "奖金");
        staffSalaryBonusVo.setFkCurrencyTypeNum(staffSalaryBonusVo.getFkCurrencyTypeNum().equals("CNY") ? "CNY (人民币)" : staffSalaryBonusVo.getFkCurrencyTypeNum());
        if (GeneralTool.isEmpty(staffSalaryBonusVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        return staffSalaryBonusVo;
    }

    @Override
    public List<StaffSalaryBonusVo> parseExcelTemplate(MultipartFile file,String type) {
        InputStream inputStream = null;
        List<StaffSalaryBonusVo> staffSalaryBonusVos = new ArrayList<>();
        try {
            inputStream = file.getInputStream();
            //  读取Excel文件
            Workbook workbook = WorkbookFactory.create(inputStream);
            Sheet sheet = workbook.getSheetAt(0);

            // 获取表头行（第0行）
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                throw new RuntimeException("Excel 文件没有表头行！");
            }


            // 遍历每一行数据
            for (int i = 1; i <= sheet.getLastRowNum(); i++) { // 假设第一行是标题

                Row row = sheet.getRow(i);
                if (row == null) continue;

                // 4. 解析基础字段
                String staffCode = (String) getCellValue(row.getCell(0)); // 工号
                String companyName = (String) getCellValue(row.getCell(1)); // 所属公司
                String staffName = (String) getCellValue(row.getCell(2)); // 姓名
                String yearMonth = null; // 年月
                String departmentName = null; // 部门名称
                String postName = null; // 岗位名称
                // 构建JSON明细
                JSONObject detailJson = new JSONObject(true);
                //  获取amount(工资卡发放) h=或者奖金
                BigDecimal amount = null;
                if (type.equals("salary")) {
                    // 获取年月（第3列）
                    Object yearMonthObj = getCellValue(row.getCell(3));
                    yearMonth = yearMonthObj != null ? yearMonthObj.toString() : "";

                    // 动态获取表头（从第4列开始）
                    for (int j = 4; j < headerRow.getLastCellNum(); j++) {
                        String headerName = getCellValue(headerRow.getCell(j)).toString();
                        Object cellValueObj = getCellValue(row.getCell(j));
                        String value = parseCellValue(cellValueObj);
                        detailJson.put(headerName, value);
                    }
                    amount = new BigDecimal(detailJson.getString("工资卡发放")); // 关键字段
                }else if(type.equals("bonus")){
                    // 获取年月（第5列）
                    Object yearMonthObj = getCellValue(row.getCell(5));
                    yearMonth = yearMonthObj != null ? yearMonthObj.toString() : "";

                    // 动态获取表头（从第6列开始）
                    for (int j = 6; j < headerRow.getLastCellNum(); j++) {
                        String headerName = getCellValue(headerRow.getCell(j)).toString();
                        Object cellValueObj = getCellValue(row.getCell(j));
                        String value = parseCellValue(cellValueObj);
                        detailJson.put(headerName, value);
                    }
                    amount = new BigDecimal(detailJson.getString("总计")); // 关键字段
                }

                String remark = detailJson.getString("备注");

                // 构建实体并保存
                StaffSalaryBonusVo entity = new StaffSalaryBonusVo();
                entity.setYearMonth(yearMonth);
                entity.setAmount(amount);
                entity.setStaffNum(staffCode);
                entity.setStaffName(staffName);
                entity.setDepartmentName(departmentName);
                entity.setPostName(postName);
                entity.setRemark(remark);
                entity.setCompanyName(companyName);
                entity.setJsonDetail(detailJson.toJSONString());
                staffSalaryBonusVos.add(entity);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_type_error"));
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
       return  staffSalaryBonusVos;
    }
}
