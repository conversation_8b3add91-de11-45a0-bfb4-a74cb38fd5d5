package com.get.partnercenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CommissionAgentInfoVo {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("公司ID")
    private String companyId;


    @ApiModelProperty("公司num")
    private String companyNum;
    @ApiModelProperty("公司Name")
    private String companyName;

    @ApiModelProperty("代理编号")
    private Long agentId;

    @ApiModelProperty("代理编号")
    private String agentNum;

    @ApiModelProperty("代理名称")
    private String agentName;


    @ApiModelProperty("bdCode")
    private String bdCode;
    @ApiModelProperty("BD名称")
    private String bdName;

    @ApiModelProperty("BD英文名称")
    private String bdNameEn;

    @ApiModelProperty(value = "国家名称")
    private String countryName;


    @ApiModelProperty(value = "州省名称")
    private String stateName;

    @ApiModelProperty(value = "学生数")
    private int studentNum;
    @ApiModelProperty(value = "状态 是否激活：0否/1是")
    private int status;







}
