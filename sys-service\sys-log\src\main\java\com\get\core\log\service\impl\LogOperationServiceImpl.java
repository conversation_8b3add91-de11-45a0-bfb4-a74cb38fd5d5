package com.get.core.log.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.dto.LogOperationDto;
import com.get.core.log.exception.GetServiceException;
import com.get.core.log.mapper.LogOperationMapper;
import com.get.core.log.model.LogOperation;
import com.get.core.log.service.ILogOperationService;
import com.get.core.log.vo.LogOperationVo;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: jack
 * @create: 2020/6/8
 * @verison: 1.0
 * @description: 操作日志业务实现类
 */
@Service
public class LogOperationServiceImpl extends ServiceImpl<LogOperationMapper, LogOperation> implements ILogOperationService {
    @Resource
    private UtilService utilService;
    @Resource
    private LogOperationMapper logOperationMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Override
    public LogOperationDto findLogOperationById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        LogOperation logOperation = logOperationMapper.selectById(id);
        LogOperationDto logOperationDto = BeanCopyUtils.objClone(logOperation, LogOperationDto::new);
        String companyName = "";
        Result<String> result = permissionCenterClient.getCompanyNameById(logOperation.getFkCompanyId());
        if (result.isSuccess()) {
            companyName = result.getData();
        }
        logOperationDto.setCompanyName(companyName);
        return logOperationDto;
    }

    @Override
    public List<LogOperationDto> getLogOperations(LogOperationVo logOperationVo, Page page) {
        //设置查询条件
        if (GeneralTool.isNotEmpty(logOperationVo)) {
            if (GeneralTool.isNotEmpty(logOperationVo.getGmtCreateUser())) {
                logOperationVo.setGmtCreateUser("%" + logOperationVo.getGmtCreateUser() + "%");
            }
            if (GeneralTool.isNotEmpty(logOperationVo.getStaffName())) {
                logOperationVo.setStaffName("%" + logOperationVo.getStaffName() + "%");
            }
            if (GeneralTool.isNotEmpty(logOperationVo.getOptCode())) {
                logOperationVo.setOptCode("%" + logOperationVo.getOptCode() + "%");
            }
            if (GeneralTool.isNotEmpty(logOperationVo.getRemark())) {
                logOperationVo.setRemark("%" + logOperationVo.getRemark() + "%");
            }
        }
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<LogOperation> logOperations = logOperationMapper.getLogOperations(logOperationVo);
//        page.restPage(logOperations);
        //分页功能待测试
        IPage<LogOperation> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<LogOperation> logOperations = logOperationMapper.getLogOperations(iPage, logOperationVo);
        page.setAll((int) iPage.getTotal());
        List<LogOperationDto> convertDatas = new ArrayList<>();
        for (LogOperation logOperation : logOperations) {
            LogOperationDto logOperationDto = BeanCopyUtils.objClone(logOperation, LogOperationDto::new);
            String companyName = "";
            Result<String> result = permissionCenterClient.getCompanyNameById(logOperation.getFkCompanyId());
            if (result.isSuccess()) {
                companyName = result.getData();
            }
            logOperationDto.setCompanyName(companyName);
            convertDatas.add(logOperationDto);
        }
        return convertDatas;
    }

    @Override
    public Long addLogOperation(LogOperation logOperation) {
        utilService.updateUserInfoToEntity(logOperation);
        logOperationMapper.insert(logOperation);
        return logOperation.getId();
    }
}
