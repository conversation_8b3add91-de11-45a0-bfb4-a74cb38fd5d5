package com.get.officecenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import com.get.officecenter.entity.LeaveLog;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2022/1/14
 * @TIME: 11:17
 * @Description:
 **/
@Data
public class LeaveLogVo extends BaseVoEntity {

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;
    /**
     * 工休类型关键字，枚举，同工休单类型一致
     */
    @ApiModelProperty(value = "工休类型关键字，枚举，同工休单类型一致")
    private String leaveTypeKey;
    /**
     * 操作类型关键字，枚举
     */
    @ApiModelProperty(value = "操作类型关键字，枚举")
    private String optTypeKey;
    /**
     * 时长（小时）
     */
    @ApiModelProperty(value = "时长（小时）")
    private BigDecimal duration;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 关联工休库存Id
     */
    @ApiModelProperty(value = "关联工休库存Id")
    private Long fkLeaveStockId;
    /**
     * 工休申请单Id
     */
    @ApiModelProperty(value = "工休申请单Id")
    private Long fkLeaveApplicationFormId;
    /**
     * 公司名称
     */
    @ApiModelProperty("公司名称")
    private String fkCompanyName;
    /**
     * 员工名称
     */
    @ApiModelProperty("员工名称")
    private String fkStaffName;
    /**
     * 工休类型名称
     */
    @ApiModelProperty("工休类型名称")
    private String leaveTypeName;
    /**
     * 操作类型名
     */
    @ApiModelProperty("操作类型名")
    private String optTypeName;
    /**
     * 时长合计
     */
    @ApiModelProperty("时长合计")
    private BigDecimal durationSum;
    /**
     * 请假开始时间
     */
    @ApiModelProperty("请假开始时间")
    private Date startTime;
    /**
     * 请假结束时间
     */
    @ApiModelProperty("请假结束时间")
    private Date endTime;
}
