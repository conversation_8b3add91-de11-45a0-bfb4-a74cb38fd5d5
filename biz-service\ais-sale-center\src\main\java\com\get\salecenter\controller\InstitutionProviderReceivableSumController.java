package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.vo.InstitutionProviderReceivableSumVo;
import com.get.salecenter.service.InstitutionProviderReceivableSumService;
import com.get.salecenter.dto.InstitutionProviderReceivableSumDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　　　◆　　　　　　　　　　　　◆　　　　　　　　　　　　　　◆　　　　　　　　　　　　　◆◆　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　◆◆◆　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆
 * 　　　◆◆　　　　　◆◆　　　　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆◆◆◆
 * 　　　◆◆◆　　　◆◆◆　　　　　　　◆◆　　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆◆
 * 　　　　◆◆◆　◆◆◆　　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆
 * 　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　　◆◆
 * 　　　　　　◆◆◆　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆
 * 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆◆
 * <p>
 * Time: 16:16
 * Date: 2021/11/19
 * Description:提供商应收汇总统计控制器
 */
@Api(tags = "提供商应收汇总统计管理")
@RestController
@RequestMapping("sale/institutionProviderReceivableSum")
public class InstitutionProviderReceivableSumController {

    @Resource
    private InstitutionProviderReceivableSumService institutionProviderReceivableSumService;

    /**
     * @Description: 提供商应收汇总统计列表
     * @Author: Jerry
     * @Date:17:11 2021/11/19
     */
    @ApiOperation(value = "提供商应收汇总统计列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理应付汇总统计管理/提供商应收汇总统计列表")
    @PostMapping("/datas")
    public ResponseBo<InstitutionProviderReceivableSumVo> datas(@RequestBody SearchBean<InstitutionProviderReceivableSumDto> page) {
        List<InstitutionProviderReceivableSumVo> datas = institutionProviderReceivableSumService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * 导出提供商应收汇总统计列表Excel
     *
     * @return
     * @
     */
    @ApiOperation(value = "导出提供商应收汇总统计列表Excel", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理应付汇总统计管理/导出提供商应收汇总统计列表Excel")
    @PostMapping("/exportInstitutionProviderReceivableSumExcel")
    @ResponseBody
    public void exportInstitutionProviderReceivableSumExcel(HttpServletResponse response, @RequestBody InstitutionProviderReceivableSumDto institutionProviderReceivableSumDto) {
        institutionProviderReceivableSumService.exportAgentPaySumExcel(response, institutionProviderReceivableSumDto);
    }
}
