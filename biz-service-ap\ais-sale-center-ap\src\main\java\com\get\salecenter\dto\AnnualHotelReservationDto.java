package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: Hardy
 * @create: 2021/6/22 10:21
 * @verison: 1.0
 * @description: 酒店预订表单Vo
 */
@Data
public class AnnualHotelReservationDto extends BaseVoEntity {
    /**
     * 峰会Id
     */
    //@NotNull(message = "峰会Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "峰会Id", required = true)
    private Long fkConventionId;

    /**
     * 参展人员类型（枚举定义）：0校方/1代理/2嘉宾/3员工/4工作人员
     */
    //@NotNull(message = "参展人员类型不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "参展人员类型（枚举定义）：0校方/1代理/2嘉宾/3员工/4工作人员", required = true)
    private Integer type;

    /**
     * 参会编号
     */
    //@NotBlank(message = "参会编号不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "参会编号", required = true)
    private String num;

    /**
     * 参加人姓名
     */
    //@NotBlank(message = "参加人姓名不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "参加人姓名", required = true)
    private String name;

    /**
     * 参加人姓名（中文）
     */
    //@NotBlank(message = "参加人姓名（中文）不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "参加人姓名（中文）", required = true)
    private String nameChn;

    /**
     * 性别：0女/1男
     */
//    @NotNull(message = "性别不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "性别：0女/1男", required = true)
    private Integer gender;

    /**
     * 参加人职位
     */
    @ApiModelProperty(value = "参加人职位")
    private String title;

    /**
     * 公司
     */
    @ApiModelProperty(value = "公司")
    private String company;

    /**
     * 参加人电邮
     */
    @ApiModelProperty(value = "参加人电邮")
    private String email;

    /**
     * 参加人电话
     */
    @NotBlank(message = "参加人电话不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "参加人电话", required = true)
    private String tel;

    @ApiModelProperty(value = "酒店房型Id")
    private Long fkConventionHotelId;

    /**
     * 护照号
     */
    @ApiModelProperty(value = "护照号")
    private String passportNum;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    private String idCardNum;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "其他自定义信息记录")
    private String remarkJson;

    /**
     * 出发地
     */
    @ApiModelProperty(value = "出发地")
    private String departurePlace;

    /**
     * 出发时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "出发时间")
    private Date departureTime;

    /**
     * 起程交通类型（枚举定义选择）：0飞机/1高铁/2汽车
     */
    @ApiModelProperty(value = "起程交通类型（枚举定义选择）：0飞机/1高铁/2汽车")
    private String toTransportation;

    /**
     * 起程交通编号：航班号/高铁班次/汽车班次
     */
    @ApiModelProperty(value = "起程交通编号：航班号/高铁班次/汽车班次")
    private String toTransportationCode;

    /**
     * 目的地
     */
    @ApiModelProperty(value = "目的地")
    private String destinationPlace;

    /**
     * 到达时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "到达时间")
    private Date destinationTime;

    /**
     * 回程交通类型（枚举定义选择）：0飞机/1高铁/2汽车
     */
    @ApiModelProperty(value = "回程交通类型（枚举定义选择）：0飞机/1高铁/2汽车")
    private String backTransportation;

    /**
     * 回程交通编号：航班号/高铁班次/汽车班次
     */
    @ApiModelProperty(value = "回程交通编号：航班号/高铁班次/汽车班次")
    private String backTransportationCode;

    /**
     * 酒店住宿费用类型：0公费/1自费
     */
    @ApiModelProperty(value = "酒店住宿费用类型：0公费/1自费")
    private Integer hotelFeeType;

    /**
     * 价格币种编号
     */
    @ApiModelProperty(value = "价格币种编号")
    private String fkCurrencyTypeNumHotelExpense;

    /**
     * 住宿费用单价（每晚）
     */
    @ApiModelProperty(value = "住宿费用单价（每晚）")
    private BigDecimal hotelExpense;

    /**
     * BD编号（4位）
     */
    @ApiModelProperty(value = "BD编号（4位）")
    private String bdCode;

    @ApiModelProperty(value = "是否参加晚宴：0否/1是")
    @Column(name = "is_attend_dinner")
    private Boolean isAttendDinner;

    @ApiModelProperty(value = "是否代订酒店：0否/1是")
    @Column(name = "is_book_hotel")
    private Boolean isBookHotel;

    /**
     * 是否出席：0否/1是
     */
    @ApiModelProperty(value = "是否出席：0否/1是")
    private Boolean isAttend;


    //----------自定义----------

    /**
     * 回执码
     */
    @ApiModelProperty(value = "回执码")
    private String receiptCode;

//    /**
//     * 中文姓
//     */
//    @ApiModelProperty(value = "中文姓")
//    private String lastNameChn;
//
//    /**
//     * 中文名
//     */
//    @ApiModelProperty(value = "中文名")
//    private String firstNameChn;

//    /**
//     * 英文姓/拼音姓
//     */
//    @ApiModelProperty(value = "英文姓/拼音姓")
//    private String lastName;
//
//    /**
//     * 英文名/拼音名
//     */
//    @ApiModelProperty(value = "英文名/拼音名")
//    private String firstName;

    /**
     * 入住时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "入住时间")
    private Date checkInDate;

    /**
     * 退房时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "退房时间")
    private Date checkOutDate;

    /**
     * 房间类型名称
     */
    @ApiModelProperty(value = "房间类型名称")
    private String roomTypeName;

    /**
     * 机构名
     */
    @ApiModelProperty(value = "机构名")
    private String institutionName;

    /**
     * 展位名
     */
    @ApiModelProperty(value = "展位名")
    private String boothName;

    /**
     * 展位报名Id
     */
    @ApiModelProperty(value = "展位报名Id")
    private Long conventionRegistrationId;

    /**
     * 州省名
     */
    @ApiModelProperty(value = "州省名")
    private String fkAreaStateName;

    /**
     * 城市名
     */
    @ApiModelProperty(value = "城市名")
    private String fkAreaCityName;
}
