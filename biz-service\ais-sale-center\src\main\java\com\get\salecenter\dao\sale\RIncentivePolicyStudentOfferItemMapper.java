package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.salecenter.vo.IncentivePolicyConditionVo;
import com.get.salecenter.vo.IncentivePolicyStudentOfferItemVo;
import com.get.salecenter.entity.IncentivePolicy;
import com.get.salecenter.entity.IncentivePolicyStudentOfferItem;
import com.get.salecenter.dto.IncentivePolicyStudentOfferItemDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-03-13
 */
@Mapper
public interface RIncentivePolicyStudentOfferItemMapper extends BaseMapper<IncentivePolicyStudentOfferItem> {

    List<IncentivePolicyStudentOfferItemVo> datas(IPage<IncentivePolicyStudentOfferItem> pages, IncentivePolicyStudentOfferItemDto incentivePolicyStudentOfferItemDto);

    List<Long> getStudentOfferItemList(IncentivePolicyConditionVo conditionJson, IncentivePolicy incentivePolicy);
}
