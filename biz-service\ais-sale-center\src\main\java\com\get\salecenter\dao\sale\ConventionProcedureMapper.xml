<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ConventionProcedureMapper">
  <resultMap id="BaseResultMap" type="com.get.salecenter.entity.ConventionProcedure">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_convention_id" jdbcType="BIGINT" property="fkConventionId" />
    <result column="fk_table_type_key" jdbcType="VARCHAR" property="fkTableTypeKey" />
    <result column="subject" jdbcType="VARCHAR" property="subject" />
    <result column="venue" jdbcType="VARCHAR" property="venue" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="step_index" jdbcType="INTEGER" property="stepIndex" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>

  <insert id="insertSelective" parameterType="com.get.salecenter.entity.ConventionProcedure" keyProperty="id" useGeneratedKeys="true">
    insert into m_convention_procedure
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkConventionId != null">
        fk_convention_id,
      </if>
      <if test="fkTableTypeKey != null">
        fk_table_type_key,
      </if>
      <if test="subject != null">
        subject,
      </if>
      <if test="venue != null">
        venue,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="stepIndex != null">
        step_index,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkConventionId != null">
        #{fkConventionId,jdbcType=BIGINT},
      </if>
      <if test="fkTableTypeKey != null">
        #{fkTableTypeKey,jdbcType=VARCHAR},
      </if>
      <if test="subject != null">
        #{subject,jdbcType=VARCHAR},
      </if>
      <if test="venue != null">
        #{venue,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="stepIndex != null">
        #{stepIndex,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getPersonProcedureCount" parameterType="java.lang.Long" resultType="long">
    select
     count(pp.id) personProcedureCount
    from
     r_convention_person_procedure pp
    where
     pp.fk_convention_procedure_id = #{id}
  </select>

  <select id="getMaxStepIndex" parameterType="java.lang.Long" resultType="int">
    select
     max(step_index)
    from
     m_convention_procedure
    where
    fk_convention_id = #{id}
  </select>

  <select id="conventionProcedureIsEmpty" parameterType="java.lang.Long" resultType="java.lang.Boolean">
    select IFNULL(max(id),0) id from m_convention_procedure where fk_convention_id = #{id} LIMIT 1

  </select>
</mapper>