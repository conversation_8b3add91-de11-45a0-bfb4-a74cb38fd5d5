package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.dto.AreaCityDto;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.vo.AreaCityVo;
import com.get.institutioncenter.service.IAreaCityService;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/28 10:52
 * @verison: 1.0
 * @description: 区域管理-城市配置控制器
 */
@Api(tags = "区域管理-城市配置")
@RestController
@RequestMapping("/institution/areaCity")
public class AreaCityController {

    @Resource
    private IAreaCityService areaCityService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/区域管理-城市配置/城市详情")
    @GetMapping("/{id}")
    public ResponseBo<AreaCityVo> detail(@PathVariable("id") Long id) {
        AreaCityVo data = areaCityService.findAreaCityById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 批量新增信息
     *
     * @param areaCityDtos
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/区域管理-城市配置/新增城市")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(AreaCityDto.Add.class)  ValidList<AreaCityDto> areaCityDtos) {
        areaCityService.batchAdd(areaCityDtos);
        return SaveResponseBo.ok();
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/区域管理-城市配置/删除城市")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        areaCityService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param areaCityDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/区域管理-城市配置/更新城市")
    @PostMapping("update")
    public ResponseBo<AreaCityVo> update(@RequestBody @Validated(AreaCityDto.Update.class) AreaCityDto areaCityDto) {
        return UpdateResponseBo.ok(areaCityService.updateAreaCity(areaCityDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为搜索关键字(num城市编号 name城市名称 nameChn城市中午名称)")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/区域管理-城市配置/查询城市")
    @PostMapping("datas")
    public ResponseBo<AreaCityVo> datas(@RequestBody SearchBean<AreaCityDto> page) {
        List<AreaCityVo> datas = areaCityService.getAreaCitys(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 查询州省下面城市
     *
     * @param id
     * @return
     * @
     */
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "查询州省下面城市", notes = "")
    @GetMapping("getByFkAreaStateId/{id}")
    public ResponseBo getByFkAreaStateId(@PathVariable("id") Long id) {
        List<AreaCityVo> datas = areaCityService.getByFkAreaStateId(id);
        return new ListResponseBo<>(datas);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取对应国家、公司下 有申请计划的代理所在的 城市下拉框数据", notes = "")
    @GetMapping("getExistsAgentOfferItemAreaStateCityList/{companyId}")
    public ResponseBo getExistsAgentOfferItemAreaStateCityList(@PathVariable("companyId") Long companyId,
                                                                  @RequestParam("stateIds") @NotNull(message = "城市id不能为空")String stateIds) {
        List<BaseSelectEntity> datas = areaCityService.getExistsAgentOfferItemAreaStateCityList(companyId, stateIds);
        return new ListResponseBo<>(datas);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取对应国家、公司下的代理所在的 城市下拉框数据", notes = "")
    @GetMapping("getExistsAgentAreaStateCityList/{companyId}")
    public ResponseBo getExistsAgentAreaStateCityList(@PathVariable("companyId") Long companyId,
                                                               @RequestParam("stateIds") @NotNull(message = "城市id不能为空")String stateIds) {
        List<BaseSelectEntity> datas = areaCityService.getExistsAgentAreaStateCityList(companyId, stateIds);
        return new ListResponseBo<>(datas);
    }

    /**
     * @Description: 查询国家下面城市
     * @Author: Jerry
     * @Date:12:37 2021/9/10
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "查询国家下面城市", notes = "")
    @GetMapping("getByFkAreaCountryId")
    public ResponseBo getByFkAreaCountryId(@RequestParam(value = "fkAreaCountryId", required = false) Long fkAreaCountryId) {
        List<AreaCityVo> datas = areaCityService.getByFkAreaCountryId(fkAreaCountryId);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return java.lang.String
     * @Description :feign调用 通过城市id 查找对应的城市名称
     * @Param [id]
     * <AUTHOR>
     */
/*    @ApiIgnore
    @GetMapping(value = "getCityNameById")
    public String getCityNameById(@RequestParam(required = false) Long id) {
        return areaCityService.getCityNameById(id);
    }*/

    /**
     * @Description :feign调用 通过城市ids 查找对应的城市名称map
     * @Param [ids]
     * <AUTHOR>
     */
/*    @ApiIgnore
    @VerifyLogin(IsVerify = false)
    @PostMapping(value = "getCityNamesByIds")
    public Map<Long, String> getCityNamesByIds(@RequestBody Set<Long> ids) {
        return areaCityService.getCityNamesByIds(ids);
    }*/

    /**
     * @Description :feign调用 通过城市ids 查找对应的城市中文名称map
     * @Param [ids]
     * <AUTHOR>
     */
/*    @ApiIgnore
    @PostMapping(value = "getCityNameChnsByIds")
    public Map<Long, String> getCityNameChnsByIds(@RequestBody Set<Long> ids) {
        return areaCityService.getCityNameChnsByIds(ids);
    }*/

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.MediaAndAttachedDto>
     * @Description :查询城市附件
     * @Param [voSearchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "查询城市附件", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/区域管理-城市配置/查询附件")
    @PostMapping("getItemMedia")
    public ResponseBo<MediaAndAttachedVo> getItemMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> staffMedia = areaCityService.getItemMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.MediaAndAttachedDto>
     * @Description :保存城市附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "保存城市附件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/区域管理-城市配置/保存附件")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> addAgentMedia(@RequestBody @Validated(MediaAndAttachedDto.Add.class) ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(areaCityService.addItemMedia(mediaAttachedVo));
    }


    /**
     * @Description :feign调用 通过城市ids 查找对应的城市中文名称map
     * @Param [ids]
     * <AUTHOR>
     */
/*    @ApiIgnore
    @PostMapping(value = "getCityFullNamesByIds")
    public Map<Long, String> getCityFullNamesByIds(@RequestBody Set<Long> ids) {
        return areaCityService.getCityFullNamesByIds(ids);
    }*/
}
