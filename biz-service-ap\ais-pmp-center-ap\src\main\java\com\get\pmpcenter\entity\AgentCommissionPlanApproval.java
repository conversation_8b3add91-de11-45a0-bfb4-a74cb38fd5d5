package com.get.pmpcenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import com.get.pmpcenter.vo.common.MediaVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  11:29
 * @Version 1.0
 * 学校佣金方案审核记录表
 */
@Data
@TableName("m_agent_commission_plan_approval")
public class AgentCommissionPlanApproval extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "审批意见")
    private String approvalComment;

    @ApiModelProperty(value = "审批人Id")
    private Long fkStaffId;

    @ApiModelProperty(value = "代理佣金方案Id")
    private Long fkAgentCommissionPlanId;

    @ApiModelProperty(value = "审批状态：2通过/3拒绝")
    private Integer approvalStatus;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("审批时间")
    private Date approvalTime;

    @ApiModelProperty(value = "方案名称")
    private String commissionPlanName;

    @ApiModelProperty(value = "审批人名称")
    @TableField(exist = false)
    private String staffName;

    @ApiModelProperty(value = "提审说明")
    private String submitNote;

    @TableField(exist = false)
    @ApiModelProperty(value = "附件列表")
    private List<MediaVo> mediaList;
}