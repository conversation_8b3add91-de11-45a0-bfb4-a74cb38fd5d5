package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.permissioncenter.vo.StaffVo;
import com.get.salecenter.vo.KpiPlanStaffTreeVo;
import com.get.salecenter.vo.KpiPlanStaffVo;
import com.get.salecenter.entity.KpiPlanStaff;
import com.get.salecenter.dto.KpiPlanStaffDto;

import java.util.List;
import java.util.Set;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface KpiPlanStaffService extends IService<KpiPlanStaff> {
    /**
     * KPI方案考核人员列表
     *
     * @param kpiPlanStaffVo 参数（用到fkKpiPlanId、rootFkStaffId）
     * @return
     */
    List<KpiPlanStaffVo> datas(KpiPlanStaffDto kpiPlanStaffVo);

     /**
      * 新增KPI方案考核人员
      * <AUTHOR>
      * @DateTime 2024/4/18 16:23
      */
    Long addKpiPlanStaff(KpiPlanStaffDto kpiPlanStaffDto);

     /**
      *  删除KPI方案考核人员
      * <AUTHOR>
      * @DateTime 2024/4/18 16:21
      */
     void delete(Long id);

    /**
     * 拖拽
     * <AUTHOR>
     * @DateTime 2024/4/18 14:53
     */
    void movingOrder(Long fkKpiPlanId, Integer start, Integer end);

    /**
     * 获取KPI方案考核人员下拉
     *
     * @param kpiPlanStaffVo 参数（用到fkKpiPlanId、rootFkStaffId、countRole）
     * @return
     */
    List<StaffVo> getKpiStaffSelect(KpiPlanStaffDto kpiPlanStaffVo);

    /**
     * 获取该KPI方案下考核人员树（登录人员自己及业务下属设置的整个考核人员树）
     *
     * @param fkKpiPlanId KPI方案Id
     * @return
     */
    List<KpiPlanStaffTreeVo> getKpiPlanStaffTree(Long fkKpiPlanId);

    /**
     * 构建KPI方案考核人员树结构
     *
     * @param kpiPlanStaffs KPI方案考核人员所有节点
     * @param rootStaffIds  根节点的考核人员id
     * @return 树结构
     */
    List<KpiPlanStaffTreeVo> buildTree(List<KpiPlanStaffVo> kpiPlanStaffs, Set<Long> rootStaffIds,Boolean isRoot);

    /**
     * 构建KPI方案考核人员树结构
     *
     * @param kpiPlanStaffs KPI方案考核人员所有节点
     * @param rootStaffIds  根节点的考核人员id
     * @return 树结构
     */
    List<KpiPlanStaffTreeVo> buildTree(List<KpiPlanStaffVo> kpiPlanStaffs, Set<Long> rootStaffIds);

    /**
     * 获取指定人员下属添加的考核人员列表，然后返回该列表的最小层级
     *
     * @param kpiPlanStaffTreeDtoList 考核人员树
     * @param staffFollowerIds        指定人员的下属Id集合
     * @return
     */
    Integer getMinLevel(List<KpiPlanStaffTreeVo> kpiPlanStaffTreeDtoList, Set<Long> staffFollowerIds);

    /**
     * 根据指定层级获取对应的列表
     *
     * @param kpiPlanStaffTree 考核人员树
     * @param level            树层级
     * @return
     */
    List<KpiPlanStaff> getStaffTreeDtoByLevel(List<KpiPlanStaffTreeVo> kpiPlanStaffTree, Integer level);

}
