package com.get.salecenter.vo;

import com.get.salecenter.entity.StaffCommissionPolicy;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * BD学生统计对比表DTO
 *
 * <AUTHOR>
 * @date 2022/12/27 17:09
 */
@Data
public class BdStudentStatisticalComparisonVo {

    @ApiModelProperty(value = "目标名称")
    private String targetName;

    @ApiModelProperty(value = "目标id")
    private Long targetId;

    @ApiModelProperty(value = "员工id")
    private Long fkStaffId;

    @ApiModelProperty(value = "业务国家名称")
    private String areaCountryName;

    @ApiModelProperty(value = "业务国家id")
    private String fkAreaCountryId;


    @ApiModelProperty(value = "年份 - 学生数统计")
    private Map<String, StudentStatistical> studentStatisticalMap;

    @ApiModelProperty(value = "年份 - 学生数统计比率")
    private Map<String, StudentStatisticalRatio> studentStatisticalRatioMap;

    @ApiModelProperty(value = "bd编号 + bd名")
    private String bdName;

    @ApiModelProperty(value = "代理编号")
    private String agentNum;

    @ApiModelProperty(value = "代理昵称")
    private String nickName;

    @ApiModelProperty(value = "所在区域")
    private String region;

    @ApiModelProperty(value = "学生项目角色Id")
    private Long fkStudentProjectRoleId;

    @ApiModelProperty(value = "角色员工名")
    private String roleStaffName;

    @ApiModelProperty(value = "角色提成规则")
    List<StaffCommissionPolicy> staffCommissionPolicies;

    /**
     * 代理比对排序专用字段
     */
    private Integer agentSort;

    @ApiModelProperty(value ="代理标签")
    private List<AgentLabelVo> agentLabelVos;

    @ApiModelProperty(value = "代理标签")
    private String agentLabelNames;

}

