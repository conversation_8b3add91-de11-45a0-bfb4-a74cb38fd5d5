package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.StudentOfferItemIssueInstitutionCourseMapper;
import com.get.salecenter.entity.StudentOfferItemIssueInstitutionCourse;
import com.get.salecenter.service.IStudentOfferItemIssueInstitutionCourseService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Author:cream
 * @Date: 2023/5/11  12:50
 */
@Service
public class IStudentOfferItemIssueInstitutionCourseServiceImpl implements IStudentOfferItemIssueInstitutionCourseService {

    @Resource
    private StudentOfferItemIssueInstitutionCourseMapper studentOfferItemIssueInstitutionCourseMapper;


    /**
     * Author Cream
     * Description : //删除ISSUE课程关联数据
     * Date 2023/5/11 12:52
     * Params:
     * Return
     */
    @Override
    public void deleteByStudentId(Long mergedStudentId) {
        if (GeneralTool.isNotEmpty(mergedStudentId)) {
            if (studentOfferItemIssueInstitutionCourseMapper.selectCount(Wrappers.<StudentOfferItemIssueInstitutionCourse>lambdaQuery().eq(StudentOfferItemIssueInstitutionCourse::getFkStudentId,mergedStudentId))>0) {
                studentOfferItemIssueInstitutionCourseMapper.delete(Wrappers.<StudentOfferItemIssueInstitutionCourse>lambdaQuery().eq(StudentOfferItemIssueInstitutionCourse::getFkStudentId,mergedStudentId));
            }
        }
    }
}
