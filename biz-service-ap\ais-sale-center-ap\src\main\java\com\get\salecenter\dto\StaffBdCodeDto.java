package com.get.salecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Sea
 * @create: 2021/1/20 17:52
 * @verison: 1.0
 * @description:
 */
@Data
public class StaffBdCodeDto extends BaseVoEntity {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;

    /**
     * BD编号（4位）
     */
    @ApiModelProperty(value = "BD编号（4位）")
    private String bdCode;

    //自定义内容
    /**
     * 搜索关键字
     */
    @ApiModelProperty(value = "搜索关键字")
    private String keyWord;


    /**
     * 颜色编码
     */
    @ApiModelProperty(value = "颜色编码")
    private String colorCode;

    /**
     * 业务区域ids（多个id用英文逗号分隔）
     */
    @ApiModelProperty(value = "业务区域ids（多个id用英文逗号分隔）")
    private String fkAreaRegionId;

    @ApiModelProperty("学校权限组员工BD id")
    private Long fkInstitutionPermissionGroupStaffIdBd;

    @ApiModelProperty("是否绑定")
    private Integer isBind;
  
}
