package com.get.financecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
@Data
public class VouchItemVo {
    @ApiModelProperty(value = "凭证Id")
    private Long fkVouchId;

    @ApiModelProperty(value = "摘要")
    private String summary;

    @ApiModelProperty(value = "科目Id")
    private Long fkAccountingItemId;

    @ApiModelProperty(value = "科目名称")
    private String accountingItemName;

    @ApiModelProperty(value = "凭证摘要设定")
    private String vouchSummary;

    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "币种名称")
    private String currencyTypeName;

    @ApiModelProperty(value = "借方发生额")
    private BigDecimal amountDr;

    @ApiModelProperty(value = "关联项借方公司Id")
    private Long relationTargetDrCompanyId;

    @ApiModelProperty(value = "贷方发生额")
    private BigDecimal amountCr;

    @ApiModelProperty(value = "关联项贷方公司Id")
    private Long relationTargetCrCompanyId;

    @ApiModelProperty(value = "关联类型Key（目标类型表名）")
    private String relationTargetKey;
    @ApiModelProperty(value = "关联类型名称")
    private String relationTargetName;

    @ApiModelProperty(value = "关联类型Id（目标类型表对应记录项Id）")
    private Long relationTargetId;

    @ApiModelProperty(value = "名称")
    private String name;
}
