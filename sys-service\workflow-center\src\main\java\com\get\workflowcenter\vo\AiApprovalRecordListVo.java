package com.get.workflowcenter.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

@Data
public class AiApprovalRecordListVo {

    @ApiModelProperty(value = "id")
    @JsonIgnore
    private Long id;

    @ApiModelProperty(value = "公司id")
    @JsonIgnore
    private Long fkCompanyId;

    @ApiModelProperty(value = "业务类型")
    private String businessKeyName;

    @ApiModelProperty(value = "编号")
    private String num;

    @ApiModelProperty(value = "审批标题")
    private String title;

    @ApiModelProperty("申请人")
    private String applyName;

    @ApiModelProperty("部门名")
    private String departmentName;

    @ApiModelProperty("公司编号")
    private String companyNum;

    @ApiModelProperty("申请原因")
    private String reason;

    @ApiModelProperty("申请状态")
    private String status;

    @ApiModelProperty("当前审批人")
    private String currentApproverName;

//    @ApiModelProperty("部署id")
//    private String deployId;
//    @ApiModelProperty("任务版本号")
//    private Integer taskVersion;
//    @ApiModelProperty("任务id")
//    private String taskId;
//    @ApiModelProperty("待签或代表")
//    private int signOrGetStatus;
//    @ApiModelProperty("流程实例id")
//    private String procInstId;

    @ApiModelProperty("申请时间")
    private Date gmtCreate;

}
