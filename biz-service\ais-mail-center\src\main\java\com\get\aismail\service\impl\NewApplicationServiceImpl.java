package com.get.aismail.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.get.aismail.dao.*;
import com.get.aismail.dto.*;
import com.get.aismail.entity.*;
import com.get.aismail.service.INewApplicationService;
import com.get.aismail.service.ITencentCloudService;
import com.get.aismail.utils.AesUtil;
import com.get.aismail.utils.AppendixUtils;
import com.get.aismail.utils.MailBoxUtils;
import com.get.aismail.utils.ProcessNewApplicationUtils;
import com.get.aismail.vo.AddNewStudentVo;
import com.get.aismail.vo.DownloadAttachedVo;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.StaffInfo;
import com.get.core.secure.utils.SecureUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static com.get.core.tool.api.ResultCode.BAD_REQUEST;
import static java.lang.System.out;

@Slf4j
@Service
public class NewApplicationServiceImpl implements INewApplicationService {
    @Resource
    private MMailAccountMapper accountMapper;
    @Resource
    private ProcessNewApplicationServiceImpl processNewApplicationServiceImpl;
    @Resource
    private MMailMapper mMailMapper;
    @Resource
    private MMailAttachedMapper attachedMapper;

    @Resource
    private MFileMailMapper mFileMailMapper;
    @Resource
    private SMediaAndAttachedMapper sMediaAndAttachedMapper;
    @Resource
    private ITencentCloudService tencentCloudService;

    @Value("${file.tencentcloudfile.bucketname}")
    private String fileBucketName;
    @Resource
    private Environment env;

    @Resource
    private MFileSaleMapper mFileSaleMapper;


    @Override
    public AddNewStudentVo addNewStudent(DownloadAttachedVo downloadAttachedVo) throws Exception {
        File needDeleteFile = null;
        try {
            StaffInfo staffInfo = SecureUtil.getStaffInfo();
            MFileSale mFileSale = new MFileSale();
            boolean checkFindFile = false;
            String attachedPath = downloadAttachedVo.getAttachedFilePath();
            if (downloadAttachedVo.getMailId() != null && !downloadAttachedVo.getMailId().isEmpty()) {
                Long userId = staffInfo.getStaffId();
                LambdaQueryWrapper<MMailAccount> accountQueryWrapper = new LambdaQueryWrapper<>();
                accountQueryWrapper.eq(MMailAccount::getFkPlatformUserId, userId).eq(MMailAccount::getEmailAccount, downloadAttachedVo.getEmailAccount());
                List<MMailAccount> accounts = accountMapper.selectList(accountQueryWrapper);
                if (accounts.isEmpty()) {
                    throw new GetServiceException(BAD_REQUEST, "mail account is not exit");
                }
                MMailAccount account = accounts.get(0);
                // 首先找到mail的位置找到id
                QueryWrapper<MMail> mailQueryWrapper1 = new QueryWrapper<>();
                mailQueryWrapper1.eq("fk_mail_account_id", account.getId());
                mailQueryWrapper1.eq("fold_box", downloadAttachedVo.getFoldName());
                mailQueryWrapper1.eq("mail_id", downloadAttachedVo.getMailId());
                List<MMail> mails1 = mMailMapper.selectList(mailQueryWrapper1);
                if (mails1.isEmpty()) {
                    throw new GetServiceException(BAD_REQUEST, "没有找到对应邮件");
                }
                MMail mail1 = mails1.get(0);
                QueryWrapper<MMailAttached> attachedQueryWrapper = new QueryWrapper<>();
                attachedQueryWrapper.eq("fk_mail_id", mail1.getId())
                        .eq("file_id", downloadAttachedVo.getAnnexId())
                        .and(wrapper -> wrapper.eq("file_name", downloadAttachedVo.getFileName())
                                .or()
                                .eq("file_name", downloadAttachedVo.getFileName().replaceAll(" ", " ")))
                        .eq("fk_mail_account_id", account.getId());
                List<MMailAttached> attacheds = attachedMapper.selectList(attachedQueryWrapper);
                if (attacheds.isEmpty()) {
                    throw new GetServiceException(BAD_REQUEST, "mail attached is not exit");
                }
                MMailAttached attached = attacheds.get(0);
                QueryWrapper<SMediaAndAttachedEntity> mediaAndAttachedEntityQueryWrapper = new QueryWrapper<>();
                mediaAndAttachedEntityQueryWrapper.eq("fk_table_name", "m_mail_attached");
                mediaAndAttachedEntityQueryWrapper.eq("fk_table_id", attached.getId());
                List<SMediaAndAttachedEntity> sMediaAndAttachedEntityList = sMediaAndAttachedMapper.selectList(mediaAndAttachedEntityQueryWrapper);
                if (sMediaAndAttachedEntityList.isEmpty()) {
                    String host = env.getProperty("mail." + account.getEmailType());
                    byte[] key = Objects.requireNonNull(env.getProperty("encrypt.KEY")).getBytes();
                    MailBox mailBox = MailBoxUtils.login(account.getEmailAccount(), AesUtil.decrypt(account.getEmailPassword(), key), host);
                    LambdaQueryWrapper<MMail> mailQueryWrapper = new LambdaQueryWrapper<>();
                    mailQueryWrapper.eq(MMail::getMailId, downloadAttachedVo.getMailId());
                    mailQueryWrapper.eq(MMail::getFkMailAccountId, account.getId());
                    List<MMail> mails = mMailMapper.selectList(mailQueryWrapper);
                    if (mails.isEmpty()) {
                        throw new GetServiceException(BAD_REQUEST, "not find mail");
                    }
                    MMail mail = mails.get(0);
                    String foldName = env.getProperty("mail." + account.getEmailType() + "-" + mail.getFoldBox());
                    String annexSavePath = env.getProperty("mail.annex-save-path");
                    downloadAttachedVo.setDate(mail.getDate());
                    attachedPath = MailBoxUtils.downAnnex(mailBox, foldName, downloadAttachedVo, annexSavePath);
                    mailBox.getStore().close();
                } else {
                    SMediaAndAttachedEntity sMediaAndAttachedEntity = sMediaAndAttachedEntityList.get(0);
                    QueryWrapper<MFileMail> fileMailQueryWrapper = new QueryWrapper<>();
                    fileMailQueryWrapper.eq("file_guid", sMediaAndAttachedEntity.getFkFileGuid());
                    List<MFileMail> mFileMailList = mFileMailMapper.selectList(fileMailQueryWrapper);
                    if (mFileMailList.isEmpty()) {
                        log.info("在桶里没有找到文件");
                    } else {
                        MFileMail mFileMail = mFileMailList.get(0);
                        // 将文件保存在mFileSale中
                        log.info("将邮件已经在桶中的文件保存到sale中");
                        QueryWrapper<MFileSale> fileSaleQueryWrapper = new QueryWrapper<>();
                        fileSaleQueryWrapper.eq("file_guid", mFileMail.getFileGuid());
                        List<MFileSale> mFileSaleList = mFileSaleMapper.selectList(fileSaleQueryWrapper);
                        if (!mFileSaleList.isEmpty()) {
                            log.info("邮件中的附件，文件已经保存在sale中");
                            mFileSale = mFileSaleList.get(0);
                            checkFindFile = true;
                        } else {
                            log.info("邮件中的附件，文件没有保存在sale中，上传到sale中");
                            mFileSale.setFileGuid(mFileMail.getFileGuid());
                            mFileSale.setFileTypeOrc(mFileMail.getFileTypeOrc());
                            mFileSale.setFileNameOrc(mFileMail.getFileNameOrc());
                            mFileSale.setFileName(mFileMail.getFileName());
                            mFileSale.setFilePath(mFileMail.getFilePath());
                            mFileSale.setFileKey(mFileMail.getFileKey());
                            mFileSale.setGmtModifiedUser(staffInfo.getName());
                            mFileSale.setGmtCreate(LocalDateTime.now());
                            mFileSaleMapper.insert(mFileSale);
                            checkFindFile = true;
                        }
                        attachedPath = env.getProperty("mail.annex-save-path") + mFileMail.getFileGuid() + mFileMail.getFileName();
                        File attFile = new File(attachedPath);
                        log.info("邮件中的附件，文件的key是：{}", mFileMail.getFileKey());
                        tencentCloudService.downLoadTofile(mFileMail, false, attFile);
                    }
                }
            }
            // 解析桶里的文件
            if (downloadAttachedVo.getGuid() != null && !downloadAttachedVo.getGuid().isEmpty()) {
                log.info("将用户文件上传在桶里用于解析");
                QueryWrapper<MFileMail> fileMailQueryWrapper = new QueryWrapper<>();
                fileMailQueryWrapper.eq("file_guid", downloadAttachedVo.getGuid());
                List<MFileMail> mFileMailList = mFileMailMapper.selectList(fileMailQueryWrapper);
                if (mFileMailList.isEmpty()) {
                    log.info("在桶里没有找到文件");
                } else {
                    MFileMail mFileMail = mFileMailList.get(0);
                    QueryWrapper<MFileSale> fileSaleQueryWrapper = new QueryWrapper<>();
                    fileSaleQueryWrapper.eq("file_guid", mFileMail.getFileGuid());
                    List<MFileSale> mFileSaleList = mFileSaleMapper.selectList(fileSaleQueryWrapper);
                    if (!mFileSaleList.isEmpty()) {
                        log.info("用户上传的文件，已经在sale中");
                        mFileSale = mFileSaleList.get(0);
                        checkFindFile = true;
                    } else {
                        // 将文件保存在mFileSale中
                        log.info("用户上传的文件，保存到sale中");
                        mFileSale.setFileGuid(mFileMail.getFileGuid());
                        mFileSale.setFileTypeOrc(mFileMail.getFileTypeOrc());
                        mFileSale.setFileNameOrc(mFileMail.getFileNameOrc());
                        mFileSale.setFileName(mFileMail.getFileName());
                        mFileSale.setFilePath(mFileMail.getFilePath());
                        mFileSale.setFileKey(mFileMail.getFileKey());
                        mFileSale.setGmtModifiedUser(staffInfo.getName());
                        mFileSale.setGmtCreate(LocalDateTime.now());
                        mFileSaleMapper.insert(mFileSale);
                        checkFindFile = true;
                    }
                    attachedPath = env.getProperty("mail.annex-save-path") + mFileMail.getFileGuid() + mFileMail.getFileName();
                    File attFile = new File(attachedPath);
                    tencentCloudService.downLoadTofile(mFileMail, false, attFile);
                }
            }
            assert attachedPath != null;
            File file = new File(attachedPath);
            needDeleteFile = file;
            // 不在桶里面的文件要上传到桶中
            if (!checkFindFile) {
                log.info("将不存在的文件上传到桶里");
                // 源文件名
                String fileNameOrc = file.getName();
                // 获取后缀名
                String fileTypeOrc = "";
                int lastDotIndex = fileNameOrc.lastIndexOf('.');
                if (lastDotIndex != -1) {
                    if (fileNameOrc.substring(lastDotIndex + 1).length() > 4) {
                        fileTypeOrc = fileNameOrc.substring(fileNameOrc.length() - 4);
                    } else {
                        fileTypeOrc = fileNameOrc.substring(lastDotIndex + 1);
                    }
                }
                // 文件guid
                String fileGuid = UUID.randomUUID().toString().replaceAll("-", "");
                String fileurl = subString(AppendixUtils.getFileHtiPath(fileNameOrc));
                int j = fileurl.lastIndexOf("/");
                // 新文件名
                String fileName = fileurl.substring(j + 1, fileurl.length());
                mFileSale.setFilePath(fileurl);
                mFileSale.setFileNameOrc(fileNameOrc);
                mFileSale.setFileTypeOrc(fileTypeOrc);
                mFileSale.setFileName(fileName);
                mFileSale.setFileGuid(fileGuid);
                mFileSale.setGmtModifiedUser(staffInfo.getName());
                mFileSale.setGmtCreate(LocalDateTime.now());
                tencentCloudService.uploadObject(true, fileBucketName, file, fileurl);
                mFileSale.setFileKey(fileurl);
                mFileSaleMapper.insert(mFileSale);

                // 同时将文件上传到mail file
                MFileMail fileMail = new MFileMail();
                fileMail.setFilePath(fileurl);
                fileMail.setFileNameOrc(fileNameOrc);
                fileMail.setFileTypeOrc(fileTypeOrc);
                fileMail.setFileName(fileName);
                fileMail.setFileGuid(fileGuid);
                fileMail.setGmtCreate(LocalDateTime.now());
                fileMail.setGmtCreateUser(staffInfo.getName());
                fileMail.setFileKey(fileurl);
                mFileMailMapper.insert(fileMail);
            }
            // 获取文件名并转换为小写
            String fileName = file.getName().toLowerCase();
            FileInputStream fis = new FileInputStream(file);
            if (!fileName.endsWith(".docx")) {
                // issue表单是html先尝试解析
                try {
                    log.info("识别issue表单");
                    Document htmlDoc = Jsoup.parse(fis, "UTF-8", "");
                    Elements tables = htmlDoc.select("table");
                    if (!tables.isEmpty()) {
                        Elements trs = tables.get(0).select("tr");
                        AddNewStudentVo addNewStudentVoIssue = processNewApplicationServiceImpl.addNewStudentIssue(trs);
                        addNewStudentVoIssue.setMFileSale(mFileSale);
                        return addNewStudentVoIssue;
                    }
                } catch (Exception e) {
                    log.info("不是issue表单");
                    throw new GetServiceException(BAD_REQUEST, "文件类型错误，请重新传入文件");
                } finally {
                    // 关闭输入流,并且删除文件
                    fis.close();
                }
            }
            // 加载文档
            XWPFDocument document = new XWPFDocument(fis);
            // 获取所有表格
            List<XWPFTable> tables = document.getTables();
            if (tables.isEmpty()) {
                throw new GetServiceException(BAD_REQUEST, "表格是空");
            }
            // 学生基本信息表格
            XWPFTable newApplicationInfoTable = ProcessNewApplicationUtils.findBestMatchTable("个人基本信息英文", tables);
            // 学生联系信息表格
            XWPFTable contactInfoTable = ProcessNewApplicationUtils.findBestMatchTable("学生联系信息", tables);
            // 国内学校情况（最高学历）表格
            XWPFTable chinaHighestDegreeInfoTable = ProcessNewApplicationUtils.findBestMatchTable("国内学习情况（最高学历）", tables);
            // 国际学校情况（最高学历）表格
            XWPFTable intinationalHighestDegreeInfoTable = ProcessNewApplicationUtils.findBestMatchTable("海外学历背景国际学习情况（最高学历）", tables);
            // 学历情况备注表格
            XWPFTable degreeInfoRemarkTable = ProcessNewApplicationUtils.findBestMatchTable("学历情况备注", tables);
            // 成绩情况表格
            XWPFTable gradesInfoTable = ProcessNewApplicationUtils.findBestMatchTable("成绩情况表格", tables);
            // 申请学校信息表格
            XWPFTable aSchoolInfoTable = ProcessNewApplicationUtils.findBestMatchTable("申请学校信息表格申请信息根据学生的意向排序", tables);
            // 学生基本信息
            NewApplicationInfoDto newApplicationInfo = new NewApplicationInfoDto();
            try {
                newApplicationInfo = processNewApplicationServiceImpl.getStudentInfo(newApplicationInfoTable);
            } catch (Exception e) {
                log.info(e.getMessage());
            }
            // 学生联系信息
            ContactInformationDto contactInfo = new ContactInformationDto();
            try {
                contactInfo = processNewApplicationServiceImpl.getContactInfo(contactInfoTable);
            } catch (Exception e) {
                log.info(e.getMessage());
            }
            // 国内最高学历
            HighestDegreeInfoDto chinaHighestDegreeInfo = new HighestDegreeInfoDto();
            try {
                chinaHighestDegreeInfo = processNewApplicationServiceImpl.getHighestDegreeInfo(chinaHighestDegreeInfoTable);
            } catch (Exception e) {
                log.info(e.getMessage());
            }
            // 国际最高学历
            HighestDegreeInfoDto intinationalHighestDegreeInfo = new HighestDegreeInfoDto();
            try {
                intinationalHighestDegreeInfo = processNewApplicationServiceImpl.getHighestDegreeInfo(intinationalHighestDegreeInfoTable);
            } catch (Exception e) {
                log.info(e.getMessage());
            }
            // 学历情况备注
            DegreeInfoRemarkDto degreeInfoRemark = new DegreeInfoRemarkDto();
            try {
                degreeInfoRemark = processNewApplicationServiceImpl.getDegreeInfoRemark(degreeInfoRemarkTable);
            } catch (Exception e) {
                log.info(e.getMessage());
            }
            // 成绩情况
            GradesInfoDto gradesInfo = new GradesInfoDto();
            try {
                gradesInfo = processNewApplicationServiceImpl.getGradesInfo(gradesInfoTable);
            } catch (Exception e) {
                log.info(e.getMessage());
            }
            // 申请学校信息
            ApplySchoolInfoDto aSchoolInfo = new ApplySchoolInfoDto();
            try {
                aSchoolInfo = processNewApplicationServiceImpl.getApplySchoolInfo(aSchoolInfoTable);
            } catch (Exception e) {
                log.info(e.getMessage());
            }
            // 关闭输入流
            fis.close();
            AddNewStudentVo addNewStudentVo = new AddNewStudentVo();
            addNewStudentVo.setNewApplication(newApplicationInfo);
            addNewStudentVo.setContact(contactInfo);
            addNewStudentVo.setHighestDegree(chinaHighestDegreeInfo);
            addNewStudentVo.setIntinationalHighestDegreeInfo(intinationalHighestDegreeInfo);
            addNewStudentVo.setDegree(degreeInfoRemark);
            addNewStudentVo.setGrades(gradesInfo);
            addNewStudentVo.setApplication(aSchoolInfo);
            addNewStudentVo.setMFileSale(mFileSale);
            return addNewStudentVo;
        } catch (Exception e) {
            e.printStackTrace();
            throw new GetServiceException(BAD_REQUEST, "识别失败");
        } finally {
            if (needDeleteFile != null) {
                log.info("删除临时文件");
                Files.delete(needDeleteFile.toPath());
            }
        }
    }

    static String subString(String url) {
        if (url.contains("target")) {
            url = url.replace("/data/project/get/file-center/target", "");
            url = url.replace("/data/project/get/app-file-center/target", "");
            return url;
        } else {
            return url;
        }

    }

}
