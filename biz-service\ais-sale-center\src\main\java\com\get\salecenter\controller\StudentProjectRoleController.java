package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.dto.RoleStaffDepartmentDto;
import com.get.salecenter.vo.StudentProjectRoleVo;
import com.get.salecenter.service.IStudentProjectRoleService;
import com.get.salecenter.dto.StudentProjectRoleDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/11/2 16:15
 * @verison: 1.0
 * @description: 项目成员角色管理
 */
@Api(tags = "项目成员角色管理")
@RestController
@RequestMapping("sale/studentProjectRole")
public class StudentProjectRoleController {
    @Resource
    private IStudentProjectRoleService studentProjectRoleService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/项目成员角色管理/项目成员角色详情")
    @GetMapping("/{id}")
    public ResponseBo<StudentProjectRoleVo> detail(@PathVariable("id") Long id) {
        StudentProjectRoleVo data = studentProjectRoleService.findStudentProjectRoleById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增信息
     * @Param [studentProjectRoleDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/项目成员角色管理/新增项目成员角色")
    @PostMapping("add")
    public ResponseBo add(@RequestBody  @Validated(StudentProjectRoleDto.Add.class)  StudentProjectRoleDto studentProjectRoleDto) {
        return SaveResponseBo.ok(this.studentProjectRoleService.addStudentProjectRole(studentProjectRoleDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/项目成员角色管理/删除项目成员角色")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        studentProjectRoleService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentProjectRoleVo>
     * @Description :修改信息
     * @Param [studentProjectRoleDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/项目成员角色管理/更新项目成员角色")
    @PostMapping("update")
    public ResponseBo<StudentProjectRoleVo> update(@RequestBody @Validated(StudentProjectRoleDto.Update.class) StudentProjectRoleDto studentProjectRoleDto) {
        return UpdateResponseBo.ok(studentProjectRoleService.updateStudentProjectRole(studentProjectRoleDto));
    }

    @ApiOperation(value = "获取存在学习方案角色下的所属部门", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/项目成员角色管理/获取学习方案角色下的员工")
    @PostMapping("getRoleStaffDepartmentByRoleId")
    public ListResponseBo<BaseSelectEntity> getRoleStaffDepartmentByRoleId(@RequestBody RoleStaffDepartmentDto roleStaffDepartmentVo){
        return studentProjectRoleService.getRoleStaffDepartmentByRoleId(roleStaffDepartmentVo);
    }

    @ApiOperation(value = "获取存在学习方案角色下的员工", notes = "subordinateFLag true:查询所有bd   false:查询有查看权限bd")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/项目成员角色管理/获取学习方案角色下的员工")
    @GetMapping("getRoleStaffByRoleId")
    public ListResponseBo<BaseSelectEntity> getRoleStaff(@RequestParam("projectRoleKey") String projectRoleKey,
                                                         @RequestParam(value = "departmentIdStr", required = false) String departmentIdStr,
                                                         @RequestParam(value = "subordinateFlag", required = false,defaultValue = "true") Boolean subordinateFlag,
                                                         @RequestParam(value = "companyIdStr", required = false) String companyIdStr){
        return studentProjectRoleService.getRoleStaff(projectRoleKey, departmentIdStr, subordinateFlag, companyIdStr);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentProjectRoleVo>
     * @Description :列表
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "fkCompanyId所属公司id ， roleName角色名称")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/项目成员角色管理/查询项目成员角色")
    @PostMapping("datas")
    public ResponseBo<StudentProjectRoleVo> datas(@RequestBody SearchBean<StudentProjectRoleDto> page) {
        List<StudentProjectRoleVo> datas = studentProjectRoleService.getStudentProjectRoles(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :上移下移
     * @Param [studentProjectRoleDtos]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/酒店房型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<StudentProjectRoleDto> studentProjectRoleDtos) {
        studentProjectRoleService.movingOrder(studentProjectRoleDtos);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 角色下拉
     * @Param [companyId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "角色下拉", notes = "")
    @GetMapping("getRoleList")
    public ResponseBo<BaseSelectEntity> getAgentList(@RequestParam(value = "companyId", required = false) Long companyId) {
        List<BaseSelectEntity> datas = studentProjectRoleService.getRoleSelect(companyId);
        return new ListResponseBo<>(datas);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取对应公司下有申请计划的 角色下拉", notes = "")
    @GetMapping("getExistsOfferItemAgentList")
    public ResponseBo<BaseSelectEntity> getExistsOfferItemAgentList(@RequestParam(value = "companyId") Long companyId) {
        List<BaseSelectEntity> datas = studentProjectRoleService.getExistsOfferItemAgentList(companyId);
        return new ListResponseBo<>(datas);
    }

    /**
     * 角色下拉（首页）
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "角色下拉（首页）", notes = "")
    @GetMapping("getRoleSelect")
    public ResponseBo<BaseSelectEntity> getRoleCollection() {
        List<BaseSelectEntity> datas = studentProjectRoleService.getRoleCollection();
        return new ListResponseBo<>(datas);
    }

    /**
     * 角色下拉,根据登录人部门与角色绑定部门过滤数据
     *
     * @return
     * @
     */
    @ApiOperation(value = "角色下拉,根据登录人部门与角色绑定部门过滤数据", notes = "")
    @VerifyPermission(IsVerify = false)
    @GetMapping("getDepartmentRole")
    public ResponseBo<BaseSelectEntity> getDepartmentRole(@RequestParam(value = "companyId") Long companyId) {
        List<BaseSelectEntity> datas = studentProjectRoleService.getDepartmentRole(companyId);
        return new ListResponseBo<>(datas);
    }
}