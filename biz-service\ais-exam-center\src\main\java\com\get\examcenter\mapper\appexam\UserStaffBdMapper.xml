<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.examcenter.mapper.appexam.UserStaffBdMapper">
  <insert id="insert" parameterType="com.get.examcenter.entity.UserStaffBd">
    insert into r_user_staff_bd (id, fk_user_id, fk_staff_id, fk_area_region_id,
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkUserId,jdbcType=BIGINT}, #{fkStaffId,jdbcType=BIGINT}, #{fkAreaRegionId,jdbcType=BIGINT},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.examcenter.entity.UserStaffBd">
    insert into r_user_staff_bd
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkUserId != null">
        fk_user_id,
      </if>
      <if test="fkStaffId != null">
        fk_staff_id,
      </if>
      <if test="fkAreaRegionId != null">
        fk_area_region_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkUserId != null">
        #{fkUserId,jdbcType=BIGINT},
      </if>
      <if test="fkStaffId != null">
        #{fkStaffId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaRegionId != null">
        #{fkAreaRegionId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.get.examcenter.entity.UserStaffBd">
    update r_user_staff_bd
    <set>
      <if test="fkUserId != null">
        fk_user_id = #{fkUserId,jdbcType=BIGINT},
      </if>
      <if test="fkStaffId != null">
        fk_staff_id = #{fkStaffId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaRegionId != null">
        fk_area_region_id = #{fkAreaRegionId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.examcenter.entity.UserStaffBd">
    update r_user_staff_bd
    set fk_user_id = #{fkUserId,jdbcType=BIGINT},
      fk_staff_id = #{fkStaffId,jdbcType=BIGINT},
      fk_area_region_id = #{fkAreaRegionId,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>