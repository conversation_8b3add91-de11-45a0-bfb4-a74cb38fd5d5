package com.get.salecenter.service;

import com.get.core.secure.StaffInfo;
import com.get.core.secure.UserInfo;
import com.get.remindercenter.dto.MailDto;
import com.get.salecenter.dto.StudentOfferItemDto;
import com.get.salecenter.dto.StudentProjectRoleStaffDto;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.EventBillReminderDto;
import com.get.salecenter.vo.StudentOfferItemVo;
import com.get.salecenter.vo.StudentProjectRoleStaffVo;

import java.util.List;
import java.util.Map;

/**
 * @author: Hardy
 * @create: 2022/7/1 10:45
 * @verison: 1.0
 * @description:
 */
public interface AsyncReminderService {

    /**
     * 发送活动费用汇总提醒
     *
     * @param headerMap
     * @param eventBillReminderDto
     * @param title
     */
    void doAddReminders(Map<String, String> headerMap, EventBillReminderDto eventBillReminderDto, String title);

    /**
     * 发送奖励推广活动提醒
     *
     * @param headerMap
     * @param user
     * @param eventIncentiveReminderDto
     * @param title
     */
    void doAddEventIncentiveReminders(Map<String, String> headerMap, UserInfo user, EventIncentiveReminderDto eventIncentiveReminderDto, String title);

    /**
     * 异步推送邮件
     * @param offerPlanVo
     * @param offerItemDto
     * @param headerMap
     * @param staffId
     * @param fkCompanyId
     */
//    void sendOfferItemEmail(EventOfferPlanDto offerPlanVo, StudentOfferItemVo offerItemDto, Map<String, String> headerMap, Long staffId,Long fkCompanyId);


    /**
     * 申请计划信息变更通知佣金部门
     * @param offerPlanVo
     * @param offerItemDto
     * @param headerMap
     * @param staffId
     * @param isDefer
     */
    void sendOfferItemCommissionNotice(EventOfferPlanDto offerPlanVo, StudentOfferItemVo offerItemDto, Map<String, String> headerMap, Long staffId, String locale, boolean isDefer);
    /**
     * 发送活动费用汇总提醒
     *
     * @param headerMap
     * @param eventBillReminderDto
     * @param title
     */
    void sendEventBillEmail(Map<String, String> headerMap, EventBillReminderDto eventBillReminderDto, String title, StaffInfo staffInfo);

    /**
     * 发送活动费用汇总提醒
     *
     * @param headerMap
     * @param eventBillReminderDto
     * @param title
     */
    void sendEventBillEmailAsync(Map<String, String> headerMap, EventBillReminderDto eventBillReminderDto, String title, StaffInfo staffInfo);


    /**
     * 发送offer新增邮件
     * @param offerVo
     * @param headerMap
     * @param staffId
     */
    void sendOfferEmail(StudentOfferDto offerVo, Map<String, String> headerMap, Long staffId);


    /**
     * 发送offer步骤变更邮件
     * @param studentOfferItemStepVo
     * @param projectRoleStaffs
     * @param studentName
     * @param agentName
     * @param institutionName
     * @param courseName
     * @param offerItem
     * @param loginId
     * @param headerMap
     * @param staffId
     */
    void sendOfferItemStepUpdateEmail(RStudentOfferItemStepDto studentOfferItemStepVo
            , List<StudentProjectRoleStaffVo> projectRoleStaffs, String studentName, String agentName
            , String institutionName, String courseName, StudentOfferItem offerItem, String loginId, Map<String, String> headerMap, Long staffId);


    /**
     * 发送佣金结算通知邮件
     * @param fkTypeTargetId
     * @param commissionNotice
     * @param headerMap
     * @param fkCompanyId
     */
    void sendSettlementCommissionEmail(Long fkTypeTargetId,String commissionNotice, Map<String, String> headerMap,Long fkCompanyId);

    /**
     * 发送入学失败邮件
     * @param headerMap
     * @param user
     * @param itemId
     */
    void sendEnrolFailureEmail(Map<String, String> headerMap, UserInfo user, Long itemId,Long otherStaffId,String enrolFailure);


    /**
     * @param rejectEmailAgentIds
     */
    void customSendStudentOfferNotice(Map<Long, MailDto> mailVoMap, StaffInfo staffInfo, List<Long> rejectEmailAgentIds);
}
