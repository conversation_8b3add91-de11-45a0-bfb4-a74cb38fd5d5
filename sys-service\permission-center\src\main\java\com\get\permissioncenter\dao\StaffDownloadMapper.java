package com.get.permissioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.permissioncenter.dto.StaffDownloadDto;
import com.get.permissioncenter.vo.StaffDownloadVo;
import com.get.permissioncenter.entity.StaffDownload;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StaffDownloadMapper extends BaseMapper<StaffDownload> {

    void insertSelective(StaffDownload staffDownload);
    /**
     * 获取列表
     * @param staffDownloadDto
     */
    List<StaffDownloadVo> lists(@Param("page")IPage<StaffDownloadDto> page
            , @Param("staffDownloadDto") StaffDownloadDto staffDownloadDto);


    void updateInfo(StaffDownload staffDownload);

    void deleteByGuid(String guid);
}
