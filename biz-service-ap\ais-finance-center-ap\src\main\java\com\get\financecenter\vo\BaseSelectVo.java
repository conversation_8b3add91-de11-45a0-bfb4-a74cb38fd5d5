package com.get.financecenter.vo;

import com.get.core.tool.utils.GeneralTool;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BaseSelectVo {
    @ApiModelProperty("id，主键")
    private Long id;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("中文名称")
    private String nameChn;
    @ApiModelProperty("全称")
    private String fullName;

    @ApiModelProperty(value = "关联类型Key（目标类型表名）")
    private String relationTargetKey;
    @ApiModelProperty(value = "关联类型名称")
    private String relationTargetKeyName;

    @ApiModelProperty(value = "科目Id")
    private Long fkAccountingItemId;

    @ApiModelProperty(value = "科目名称")
    private String accountingItemName;

    public String getFullName() {
        if (GeneralTool.isNotEmpty(this.fullName)) {
            return this.fullName;
        } else if (GeneralTool.isNotEmpty(this.name)) {
            return GeneralTool.isNotEmpty(this.nameChn) ? this.name + "（" + this.nameChn + "）" : this.name;
        } else {
            return this.nameChn;
        }
    }



}
