package com.get.officecenter.service;

import com.get.common.result.Page;
import com.get.common.result.SaveResponseBo;
import com.get.officecenter.entity.Comment;
import com.get.officecenter.dto.CommentDto;

import java.util.List;
import java.util.Set;

/**
 * @Author:cream
 * @Date: 2023/5/30  12:31
 */
public interface CommentService {


    /**
     * 增加评论
     * @param commentDto
     * @return
     */
    SaveResponseBo addComment(CommentDto commentDto);

    /**
     * 编辑评论
     * @param commentDto
     * @return
     */
    SaveResponseBo updateComment(CommentDto commentDto);

    /**
     * 获取评论列表
     * @param fkTableName
     * @param fkTableIds
     * @return
     */
    List<Comment> getCommentList(String fkTableName,List<Long> fkTableIds);


    /**
     * 获取任务的评论
     * @param fkTableName
     * @param fkTableId
     * @param page
     * @return
     */
    List<Comment> getCommentList(String fkTableName,Long fkTableId, Page page);


    /**
     * 删除评论
     *
     * @param fkTableName 表名
     * @param fkTableIds  表id集合
     */
    void deleteComment(String fkTableName, Set<Long> fkTableIds);


    /**
     * 删除单个评论
     * @param commentId
     */
    void removeComment(Long commentId);

    void addCommentBatch(List<CommentDto> commentBatch);
}
