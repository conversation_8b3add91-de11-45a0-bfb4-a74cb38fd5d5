package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2020/12/7
 * @TIME: 16:09
 * @Description:
 **/
@Data
public class InstitutionCourseEngScoreDto extends BaseVoEntity {
    /**
     * 课程Id
     */
    @ApiModelProperty(value = "课程Id")
    private Long fkInstitutionCourseId;

    /**
     * 条件类型，枚举：IELTS/TOEFL-IBT/TOEFL-PBT/PTE
     */
    @ApiModelProperty(value = "条件类型，枚举：IELTS/TOEFL-IBT/TOEFL-PBT/PTE")
    private Integer conditionType;

    /**
     * 总分
     */
    @ApiModelProperty(value = "总分")
    private BigDecimal overall;

    /**
     * 听
     */
    @ApiModelProperty(value = "听")
    private BigDecimal listening;

    /**
     * 讲
     */
    @ApiModelProperty(value = "讲")
    private BigDecimal speaking;

    /**
     * 读
     */
    @ApiModelProperty(value = "读")
    private BigDecimal reading;

    /**
     * 写
     */
    @ApiModelProperty(value = "写")
    private BigDecimal writing;

    /**
     * 成绩描述
     */
    @ApiModelProperty(value = "成绩描述")
    private String description;
}
