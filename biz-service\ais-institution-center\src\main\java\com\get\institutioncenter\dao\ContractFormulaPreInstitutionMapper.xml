<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.ContractFormulaPreInstitutionMapper">
  <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.ContractFormulaPreInstitution">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_contract_formula_id" jdbcType="BIGINT" property="fkContractFormulaId" />
    <result column="fk_institution_id" jdbcType="BIGINT" property="fkInstitutionId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, fk_contract_formula_id, fk_institution_id, gmt_create, gmt_create_user, gmt_modified, 
    gmt_modified_user
  </sql>

  <insert id="insert" parameterType="com.get.institutioncenter.entity.ContractFormulaPreInstitution">
    insert into r_contract_formula_pre_institution (id, fk_contract_formula_id, fk_institution_id, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkContractFormulaId,jdbcType=BIGINT}, #{fkInstitutionId,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.ContractFormulaPreInstitution">
    insert into r_contract_formula_pre_institution
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkContractFormulaId != null">
        fk_contract_formula_id,
      </if>
      <if test="fkInstitutionId != null">
        fk_institution_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkContractFormulaId != null">
        #{fkContractFormulaId,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionId != null">
        #{fkInstitutionId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.get.institutioncenter.entity.ContractFormulaPreInstitution">
    update r_contract_formula_pre_institution
    <set>
      <if test="fkContractFormulaId != null">
        fk_contract_formula_id = #{fkContractFormulaId,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionId != null">
        fk_institution_id = #{fkInstitutionId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.institutioncenter.entity.ContractFormulaPreInstitution">
    update r_contract_formula_pre_institution
    set fk_contract_formula_id = #{fkContractFormulaId,jdbcType=BIGINT},
      fk_institution_id = #{fkInstitutionId,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="getInstiutionIdListByFkId" resultType="java.lang.Long">
    select
      fk_institution_id
    from
      r_contract_formula_pre_institution
    where
      fk_contract_formula_id = #{contractFormulaId}
  </select>
  <select id="isExistByInstitutionId" resultType="java.lang.Boolean">
    SELECT IFNULL(max(rcfpi.id),0) id from r_contract_formula_pre_institution AS rcfpi
                                INNER JOIN m_contract_formula AS mcf ON mcf.id = rcfpi.fk_contract_formula_id
                                INNER JOIN m_institution_provider AS mip ON mip.id = mcf.fk_institution_provider_id

                                where rcfpi.fk_institution_id=#{fkInstitutionId} AND mip.id = #{fkInstitutionProviderId}
  </select>
</mapper>