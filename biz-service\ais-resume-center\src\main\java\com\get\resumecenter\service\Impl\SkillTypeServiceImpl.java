package com.get.resumecenter.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.resumecenter.dao.SkillTypeMapper;
import com.get.resumecenter.vo.SkillTypeVo;
import com.get.resumecenter.entity.SkillType;
import com.get.resumecenter.service.IDeleteService;
import com.get.resumecenter.service.ISkillTypeService;
import com.get.resumecenter.dto.SkillTypeDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/30
 * @TIME: 15:50
 * @Description:
 **/
@Service
public class SkillTypeServiceImpl implements ISkillTypeService {
    @Resource
    private SkillTypeMapper skillTypeMapper;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private UtilService utilService;

    @Override
    public List<SkillTypeVo> datas(SkillTypeDto skillTypeDto) {
        LambdaQueryWrapper<SkillType> lambdaQueryWrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(skillTypeDto)) {
            if (GeneralTool.isNotEmpty(skillTypeDto.getTypeName())) {
                lambdaQueryWrapper.like(SkillType::getTypeName, skillTypeDto.getTypeName());
                lambdaQueryWrapper.or(wrapper -> wrapper.like(SkillType::getSubTypeName, skillTypeDto.getTypeName()));
            }
            lambdaQueryWrapper.orderByDesc(SkillType::getViewOrder);
        }
        List<SkillType> skillTypes = skillTypeMapper.selectList(lambdaQueryWrapper);
        List<SkillTypeVo> convertDatas = new ArrayList<>();
        for (SkillType skillType : skillTypes) {
            SkillTypeVo skillTypeVo = BeanCopyUtils.objClone(skillType, SkillTypeVo::new);
            convertDatas.add(skillTypeVo);
        }
        return convertDatas;
    }

    @Override
    public SkillTypeVo findSkillTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        SkillType skillType = skillTypeMapper.selectById(id);
        SkillTypeVo skillTypeVo = BeanCopyUtils.objClone(skillType, SkillTypeVo::new);
        return skillTypeVo;
    }

    @Override
    public SkillTypeVo updateSkillType(SkillTypeDto skillTypeDto) {
        if (skillTypeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(skillTypeDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        SkillType rs = this.skillTypeMapper.selectById(skillTypeDto.getId());
        if (rs == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        SkillType skillType = BeanCopyUtils.objClone(skillTypeDto, SkillType::new);
        if (validateUpdate(skillTypeDto)) {
            utilService.updateUserInfoToEntity(skillType);
            skillTypeMapper.updateById(skillType);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        return findSkillTypeById(skillType.getId());
    }

    @Override
    public Long addSkillType(SkillTypeDto skillTypeDto) {
        SkillType skillType = BeanCopyUtils.objClone(skillTypeDto, SkillType::new);
        if (validateAdd(skillTypeDto)) {
            utilService.updateUserInfoToEntity(skillType);
            skillType.setViewOrder(skillTypeMapper.getMaxViewOrder());
            skillTypeMapper.insert(skillType);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        return skillType.getId();
    }

    @Override
    public void delete(Long id) {
        SkillTypeVo skillType = findSkillTypeById(id);
        if (skillType == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        deleteService.deleteValidateSkillType(id);
        int i = skillTypeMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<SkillTypeDto> skillTypeDtos) {
        for (SkillTypeDto skillTypeDto : skillTypeDtos) {
            if (GeneralTool.isEmpty(skillTypeDto.getId())) {
                if (validateAdd(skillTypeDto)) {
                    SkillType skillType = BeanCopyUtils.objClone(skillTypeDto, SkillType::new);
                    utilService.updateUserInfoToEntity(skillType);
                    skillType.setViewOrder(skillTypeMapper.getMaxViewOrder());
                    skillTypeMapper.insert(skillType);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
                }
            } else {
                if (validateUpdate(skillTypeDto)) {
                    SkillType skillType = BeanCopyUtils.objClone(skillTypeDto, SkillType::new);
                    utilService.updateUserInfoToEntity(skillType);
                    skillTypeMapper.updateById(skillType);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
                }
            }


        }

    }

    @Override
    public List<BaseSelectEntity> getSkillTypeSelect() {
        return skillTypeMapper.getSkillTypeSelect();
    }

    @Override
    public String getNameByTypeId(Long industryTypeId) {
        if (GeneralTool.isEmpty(industryTypeId)) {
            return null;
        }
        return skillTypeMapper.getTypeNameByTypeId(industryTypeId);
    }

    @Override
    public void movingOrder(List<SkillTypeDto> skillTypeDtos) {
        if (GeneralTool.isEmpty(skillTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        SkillType skillType = BeanCopyUtils.objClone(skillTypeDtos.get(0), SkillType::new);
        SkillType skillType2 = BeanCopyUtils.objClone(skillTypeDtos.get(1), SkillType::new);

        Integer viewOrder = skillType.getViewOrder();
        skillType.setViewOrder(skillType2.getViewOrder());
        skillType2.setViewOrder(viewOrder);

        utilService.updateUserInfoToEntity(skillType);
        utilService.updateUserInfoToEntity(skillType2);

        skillTypeMapper.updateById(skillType);
        skillTypeMapper.updateById(skillType2);

    }

    private boolean validateAdd(SkillTypeDto skillTypeDto) {
//        Example example = new Example(SkillType.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("typeName", skillTypeDto.getTypeName());
        List<SkillType> list = this.skillTypeMapper.selectList(Wrappers.<SkillType>lambdaQuery().eq(SkillType::getTypeName, skillTypeDto.getTypeName()));
        return GeneralTool.isEmpty(list);
    }

    private boolean validateAdd(List<SkillTypeDto> skillTypeDtos) {
        boolean success = true;
        for (SkillTypeDto skillTypeDto : skillTypeDtos) {
//            Example example = new Example(SkillType.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andEqualTo("typeName", skillTypeDto.getTypeName());
//            criteria.andEqualTo("subTypeName", skillTypeDto.getSubTypeName());
//            List<SkillType> list = this.skillTypeMapper.selectByExample(example);
            List<SkillType> list = this.skillTypeMapper.selectList(Wrappers.<SkillType>lambdaQuery()
                    .eq(SkillType::getTypeName, skillTypeDto.getTypeName()).eq(SkillType::getSubTypeName, skillTypeDto.getSubTypeName()));
            if (!GeneralTool.isEmpty(list)) {
                success = false;
            }
        }
        return success;
    }

    private boolean validateUpdate(SkillTypeDto skillTypeDto) {
//        Example example = new Example(SkillType.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("typeName", skillTypeDto.getTypeName());
//        criteria.andEqualTo("subTypeName", skillTypeDto.getSubTypeName());
//        List<SkillType> list = this.skillTypeMapper.selectByExample(example);
        List<SkillType> list = this.skillTypeMapper.selectList(Wrappers.<SkillType>lambdaQuery()
                .eq(SkillType::getTypeName, skillTypeDto.getTypeName()).eq(SkillType::getSubTypeName, skillTypeDto.getSubTypeName()));
        return list.size() <= 0 || list.get(0).getId().equals(skillTypeDto.getId());
    }
}
