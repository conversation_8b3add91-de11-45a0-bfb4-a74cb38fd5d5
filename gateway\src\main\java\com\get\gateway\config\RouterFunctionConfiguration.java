package com.get.gateway.config;

import com.get.gateway.props.AuthProperties;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.cors.reactive.CorsUtils;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

/**
 * 路由配置信息
 */
@Slf4j
@Configuration
@AllArgsConstructor
@EnableConfigurationProperties({AuthProperties.class})
public class RouterFunctionConfiguration {

    /**
     * 这里为支持的请求头，如果有自定义的header字段请自己添加
     */
//	private static final String ALLOWED_HEADERS = "X-Requested-With, Tenant-Id, One-Auth, Content-Type, Authorization, credential, X-XSRF-TOKEN, token, username, client, knfie4j-gateway-request, request-origion";
    //at表示认证token，这里需要测试
    private static final String ALLOWED_HEADERS = "Origin,X-Requested-With, at, Content-Type, Authorization,credential, X-XSRF-TOKEN, token, username, client, knfie4j-gateway-request, request-origion";
    private static final String ALLOWED_METHODS = "GET,POST,PUT,DELETE,OPTIONS,HEAD";
    private static final String ALLOWED_ORIGIN = "*";
    private static final String ALLOWED_EXPOSE = "*";
    private static final String MAX_AGE = "18000L";

    /**
     * 跨域配置
     */
    @Bean
    public WebFilter corsFilter() {
        return (ServerWebExchange ctx, WebFilterChain chain) -> {
            ServerHttpRequest request = ctx.getRequest();
            if (CorsUtils.isCorsRequest(request)) {
                ServerHttpResponse response = ctx.getResponse();
                HttpHeaders headers = response.getHeaders();
                log.info(" origin : {}", request.getHeaders().get(HttpHeaders.ORIGIN).get(0));

                headers.add("Access-Control-Allow-Headers", ALLOWED_HEADERS);
                headers.add("Access-Control-Allow-Methods", ALLOWED_METHODS);
                headers.add("Access-Control-Allow-Origin", ALLOWED_ORIGIN);
                headers.add("Access-Control-Expose-Headers", ALLOWED_EXPOSE);
                headers.add("Access-Control-Max-Age", MAX_AGE);
                headers.add("Access-Control-Allow-Credentials", "true");
                if (request.getMethod() == HttpMethod.OPTIONS) {
                    response.setStatusCode(HttpStatus.OK);
                    return Mono.empty();
                }
            }

//			ServerHttpRequest request = ctx.getRequest();
//			if (CorsUtils.isCorsRequest(request)) {
//				HttpHeaders requestHeaders = request.getHeaders();
//				ServerHttpResponse response = ctx.getResponse();
//				HttpMethod requestMethod = requestHeaders.getAccessControlRequestMethod();
//				HttpHeaders headers = response.getHeaders();
//				headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*");
//				headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "PUT,POST,GET,DELETE,OPTIONS");
//
//				headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS,"Origin, No-Cache, X-Requested-With, If-Modified-Since, Pragma, Last-Modified, Cache-Control, Expires, Content-Type, X-E4M-With,userId,token");
//				headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_CREDENTIALS, "true");
//				headers.add(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "*");
//				headers.add(HttpHeaders.ACCESS_CONTROL_MAX_AGE, MAX_AGE);
//				if (request.getMethod() == HttpMethod.OPTIONS) {
//					response.setStatusCode(HttpStatus.OK);
//					return Mono.empty();
//				}
//			}
            return chain.filter(ctx);
        };
    }

}
