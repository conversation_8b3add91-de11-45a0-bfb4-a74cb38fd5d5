package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @author: Hardy
 * @create: 2022/7/19 16:55
 * @verison: 1.0
 * @description:
 */
@Data
public class EventIncentiveCostUpdateDto extends BaseVoEntity {
    /**
     * 奖励推广活动Id
     */
    @ApiModelProperty(value = "奖励推广活动Id")
    private Long fkEventIncentiveId;

    /**
     * 学校提供商Id
     */
    @NotNull(message = "学校提供商Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "学校提供商Id")
    private Long fkInstitutionProviderId;

    /**
     * 活动费用账单Id
     */
    @NotNull(message = "活动费用账单Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "活动费用账单Id")
    private Long fkEventBillId;

    /**
     * 收款单Id（暂时无用）
     */
    @ApiModelProperty(value = "收款单Id（暂时无用）")
    private Long fkReceiptFormId;

    /**
     * 活动费用币种
     */
    @NotBlank(message = "活动费用币种不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "活动费用币种")
    private String fkCurrencyTypeNum;

    /**
     * 活动费用
     */
    @NotNull(message = "活动费用不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "活动费用")
    private BigDecimal amount;

    /**
     * 折合收款汇率
     */
    @NotNull(message = "折合收款汇率不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "折合收款汇率")
    private BigDecimal exchangeRateReceivable;

    /**
     * 折合收款金额
     */
    @ApiModelProperty(value = "折合收款金额")
    private BigDecimal amountReceivable;

    /**
     * 汇率（折合港币）
     */
    @ApiModelProperty(value = "汇率（折合港币）")
    private BigDecimal exchangeRateHkd;

    /**
     * 收款金额（折合港币）
     */
    @ApiModelProperty(value = "收款金额（折合港币）")
    private BigDecimal amountHkd;

    /**
     * 汇率（折合人民币）
     */
    @ApiModelProperty(value = "汇率（折合人民币）")
    private BigDecimal exchangeRateRmb;

    /**
     * 收款金额（折合人民币）
     */
    @ApiModelProperty(value = "收款金额（折合人民币）")
    private BigDecimal amountRmb;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
