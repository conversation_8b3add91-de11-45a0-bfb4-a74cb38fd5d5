package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2020/8/3
 * @TIME: 15:50
 * @Description:
 **/
@Data
public class InstitutionFacultyDto extends BaseVoEntity {
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id", required = true)
    @NotNull(message = "学校Id不能为空", groups = {Add.class, Update.class})
    private Long fkInstitutionId;

    /**
     * 学院名称
     */
    @ApiModelProperty(value = "学院名称", required = true)
    @NotBlank(message = "学院名称不能为空", groups = {Add.class, Update.class})
    private String name;

    /**
     * 学院中文名称
     */
    @ApiModelProperty(value = "学院中文名称", required = true)
    private String nameChn;

    /**
     * 简介
     */
    @ApiModelProperty(value = "简介", required = true)
    private String description;

    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序", required = true)
    private Integer viewOrder;

}
