package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@TableName(value = "r_payable_plan_settlement_message")
@Data
public class SettlementCommissionNotice extends BaseEntity {


    @ApiModelProperty("批次号")
    private String numSettlementBatch;

    @ApiModelProperty("应付id")
    private Long fkPayablePlanId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("佣金通知")
    private String commissionNotice;
}
