package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.vo.ConventionPersonVo;
import com.get.salecenter.entity.ConventionPersonRegistration;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/7/27 12:21
 * @verison: 1.0
 * @description: 参展人员和峰会报名关联中间表mapper
 */
@Mapper
public interface ConventionPersonRegistrationMapper extends BaseMapper<ConventionPersonRegistration> {
    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insert(ConventionPersonRegistration record);

    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(ConventionPersonRegistration record);

    /**
     * 根据参展人员id获取峰会报名id
     *
     * @param conventionPersonId
     * @return
     */
    Long getConventionRegistrationId(Long conventionPersonId);


    /**
     * 获取报名名册ids
     *
     * @param fkConventionId
     * @return
     */
    List<Long> getConventionRegistrationIds(@Param("fkConventionId") Long fkConventionId);

    /**
     * 获取参会人
     *
     * @param ids
     * @return
     */
    List<ConventionPersonVo> getConventionPersonByRegistrationIds(@Param("ids") Set<Long> ids);

    /**
     *
     * @param institutionAgentIds
     * @return
     */
    List<ConventionPersonVo> getConventionPersonProvideNameMapByIds(@Param("institutionAgentIds") List<Long> institutionAgentIds);

    /**
     * 检查数据
     * @param conventionPersonId
     * @param receiptCode
     * @return
     */
    Boolean checkConventionPersonRegistration(@Param("conventionPersonId") Long conventionPersonId, @Param("receiptCode") String receiptCode);
}
