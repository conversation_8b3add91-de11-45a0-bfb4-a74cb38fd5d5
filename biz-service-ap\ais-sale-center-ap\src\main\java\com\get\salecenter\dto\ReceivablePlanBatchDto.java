package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ReceivablePlanBatchDto {

    @NotNull(message = "typeKey不能为空")
    private String typeKey;

    @NotNull(message = "typeTargetId不能为空")
    private List<Long> typeTargetIds;

    @NotNull(message = "invoiceId不能为空")
    private Long invoiceId;

    @ApiModelProperty(value = "学生名称")
    private String studentName;

    /**
     * 其他金额类型：0=奖励金额/1=Incentive奖励/2=保险金额/3=活动费用/4=其他收入
     */
    @ApiModelProperty(value = "其他金额类型：0=奖励金额/1=Incentive奖励/2=保险金额/3=活动费用/4=其他收入")
    private Integer bonusType;

    @ApiModelProperty(value = "开学时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date openingTime;

    @ApiModelProperty(value = "结束入学时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date closingTime;

    @NotNull(message = "页码不能为空")
    private Integer pageNumber;

    @NotNull(message = "页面大小为空")
    private Integer pageSize;

    private Integer offset=0;

    @ApiModelProperty(value = "应收计划类型")
    private String fkTypeKey;

    @ApiModelProperty("应收金额（最大）")
    private BigDecimal maxReceivableAmount;

    @ApiModelProperty("应收金额（最小）")
    private BigDecimal minReceivableAmount;
}
