package com.get.platformconfigcenter.service;

import com.get.filecenter.dto.FileDto;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 媒体附件业务逻辑类
 *
 * <AUTHOR>
 * @date 2021/5/12 11:42
 */
public interface MediaAndAttachedMsoService {

    /**
     * 通过附件GUID获取DTO
     *
     * @param attachedVo
     * @return
     * @
     */
//    List<MediaAndAttachedDto> getMediaAndAttachedVo(MediaAndAttachedVo attachedVo);

    /**
     * 上传文件  111111
     *
     * @Date 11:42 2021/7/5
     * <AUTHOR>
     */
    List<FileDto> upload(MultipartFile[] multipartFiles, String type);

    /**
     * 上传附件  111111
     *
     * @Date 11:42 2021/7/5
     * <AUTHOR>
     */
    List<FileDto> uploadAppendix(MultipartFile[] multipartFiles, String type);

    /**
     * @Description:保存文件
     * @Param
     * @Date 12:28 2021/5/12
     * <AUTHOR>
     */
//    MediaAndAttachedDto addMediaAndAttached(MediaAndAttachedVo mediaAttachedVo);

    /**
     * @Description:删除媒体文件
     * @Param
     * @Date 16:40 2021/5/13
     * <AUTHOR>
     */
//    void deleteMediaAndAttached(String fkTableName, Long fkTableId);
}
