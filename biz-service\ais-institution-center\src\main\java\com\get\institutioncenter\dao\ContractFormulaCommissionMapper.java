package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.ContractFormulaCommission;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ContractFormulaCommissionMapper extends BaseMapper<ContractFormulaCommission> {

    @Override
    int insert(ContractFormulaCommission record);

    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(ContractFormulaCommission record);

    /**
     * @return java.lang.Integer
     * @Description :
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    Integer getMaxStep(Long contractFormulaId);
}