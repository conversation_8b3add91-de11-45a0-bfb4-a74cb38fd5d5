<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.platformconfigcenter.mapper.NoticeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="noticeResultMap" type="com.get.platformconfigcenter.entity.Notice">
        <result column="id" property="id"/>
        <result column="release_time" property="releaseTime"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        select id,
        title, content
    </sql>

    <select id="topList" resultMap="noticeResultMap">
        select * from get_notice limit #{number}
    </select>

    <select id="selectNoticePage" resultMap="noticeResultMap">
        select * from get_notice where title like concat('%', #{notice.title}, '%')
    </select>

</mapper>
