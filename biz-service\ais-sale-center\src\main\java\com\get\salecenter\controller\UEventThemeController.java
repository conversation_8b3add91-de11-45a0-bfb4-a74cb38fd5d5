package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.dto.query.UEventThemeQueryDto;
import com.get.salecenter.service.UEventThemeService;
import com.get.salecenter.vo.UEventThemeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "活动主题名称管理")
@RestController
@RequestMapping("sale/UEventTheme")
public class UEventThemeController {

    @Resource
    private UEventThemeService uEventThemeService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/活动主题名称管理/主题名称详情")
    @GetMapping("/{id}")
    public ResponseBo<UEventThemeVo> detail(@PathVariable("id") Long id) {
        UEventThemeVo data = uEventThemeService.findUEventThemeById(id);
        return new ResponseBo<>(data);
    }


    /**
     * 新增信息
     *
     * @param uEventThemeQueryDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/活动主题名称管理/新增主题名称")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(UEventThemeQueryDto.Add.class)UEventThemeQueryDto uEventThemeQueryDto) {
        return SaveResponseBo.ok(uEventThemeService.addUEventTheme(uEventThemeQueryDto));
    }


    /**
     * 修改信息
     *
     * @param
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动主题名称管理/更新主题名称")
    @PostMapping("update")
    public ResponseBo<UEventThemeVo> update(@RequestBody @Validated(UEventThemeQueryDto.Add.class) UEventThemeQueryDto UEventThemeQueryDto) {
        return UpdateResponseBo.ok(uEventThemeService.updateUEventTheme(UEventThemeQueryDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动主题名称管理/查询主题名称")
    @PostMapping("datas")
    public ResponseBo<UEventThemeVo> datas(@RequestBody SearchBean<UEventThemeQueryDto> page) {
        List<UEventThemeVo> datas = uEventThemeService.getUEventThemes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/活动主题名称管理/删除主题名称")
    @GetMapping ("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        uEventThemeService.deleteUEventTheme(id);
        return DeleteResponseBo.ok();
    }

//    @ApiOperation(value = "检查不同分公司下活动名称是否存在", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/活动主题名称管理/检查主题名称是否存在")
//    @GetMapping("/check")
//    public ResponseBo check(@RequestBody  UEventThemeQueryDto uEventThemeQueryDto) {
//        return new ResponseBo<>(uEventThemeService.check(uEventThemeQueryDto));
//    }


}
