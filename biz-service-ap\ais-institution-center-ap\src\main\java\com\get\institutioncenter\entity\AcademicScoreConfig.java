package com.get.institutioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("s_academic_score_config")
public class AcademicScoreConfig extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 条件类型，枚举：GPA/GCSE/HKDSE/IB
     */
    @ApiModelProperty(value = "条件类型，枚举：GPA/GCSE/HKDSE/IB")
    @Column(name = "condition_type")
    private Integer conditionType;
    /**
     * 分数类型
     */
    @ApiModelProperty(value = "分数类型")
    @Column(name = "score_type")
    private String scoreType;
    /**
     * 分数
     */
    @ApiModelProperty(value = "分数")
    @Column(name = "score")
    private BigDecimal score;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", conditionType=").append(conditionType);
        sb.append(", scoreType=").append(scoreType);
        sb.append(", score=").append(score);
        sb.append(", remark=").append(remark);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}