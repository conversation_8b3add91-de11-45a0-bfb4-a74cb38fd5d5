package com.get.partnercenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import java.io.Serializable;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 
 * @TableName m_app_student_check
 */
@TableName(value ="m_app_student_check")
@Data
public class MAppStudentCheckEntity extends BaseEntity implements Serializable {
    /**
     *
     */
    @NotNull(message = "申请学生AppId不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    @ApiModelProperty( "申请学生Id")
    private Long fkAppStudentId;

    /**
     * 检查意见
     */
    private String checkComment;

    /**
     *
     */
    @NotNull(message = "是否通过，0否/1是", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    @ApiModelProperty( "是否通过，0否/1是")
    private Boolean isPassed;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}