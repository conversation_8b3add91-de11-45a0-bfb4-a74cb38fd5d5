package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.ContractFormulaInstitutionFaculty;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ContractFormulaInstitutionFacultyMapper extends BaseMapper<ContractFormulaInstitutionFaculty> {

    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(ContractFormulaInstitutionFaculty record);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过合同公式id 查找对应学院ids
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<Long> getFacultyIdListByFkid(Long contractFormulaId);
}