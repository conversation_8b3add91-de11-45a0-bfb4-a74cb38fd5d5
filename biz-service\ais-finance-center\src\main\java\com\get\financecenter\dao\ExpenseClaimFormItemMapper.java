package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.financecenter.dto.SearchActivityDataDto;
import com.get.financecenter.entity.ExpenseClaimFormItem;
import com.get.financecenter.vo.ExpenseClaimFormAndItemVo;
import java.math.BigDecimal;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ExpenseClaimFormItemMapper extends BaseMapper<ExpenseClaimFormItem> {

    /**
     * @return List<ExpenseClaimFormItem>
     * @Description :获取对应表单id的子项
     * @Param [id]
     * <AUTHOR>
     */
    List<ExpenseClaimFormItem> getFormItemByExpenseClaimFormId(@Param("id") Long id);

    /**
     * @return BigDecimal
     * @Description :获取对应表单id的子项总金额
     * @Param [id]
     * <AUTHOR>
     */
    BigDecimal getExpenseClaimFormTotalAmount(@Param("id") Long id);

    List<ExpenseClaimFormAndItemVo> getAllExpenseClaimFormItemByActivityData(@Param("searchActivityDataDto") SearchActivityDataDto searchActivityDataDto, @Param("statusList") List<Integer> statusList);

    /**
     * 根据活动信息查询费用报销
     * @param searchActivityDataDto
     * @param pages
     * @return
     */
    List<ExpenseClaimFormAndItemVo> getExpenseClaimFormByActivityData(@Param("searchActivityDataDto") SearchActivityDataDto searchActivityDataDto, IPage<ExpenseClaimFormAndItemVo> pages);

    /**
     * 根据活动信息查询费用报销总金额
     * @param searchActivityDataDto
     * @param statusList
     * @return
     */
    BigDecimal getClaimedEventCostByActivityData(@Param("searchActivityDataDto") SearchActivityDataDto searchActivityDataDto, @Param("statusList") List<Integer> statusList, @Param("isAvailableClaimAmount") boolean isAvailableClaimAmount);

    String getCurrencyTypeByActivityData(@Param("searchActivityDataDto") SearchActivityDataDto searchActivityDataDto, @Param("statusList") List<Integer> statusList);
}