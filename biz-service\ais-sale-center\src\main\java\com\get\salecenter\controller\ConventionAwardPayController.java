package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.vo.ConventionAwardPayVo;
import com.get.salecenter.service.IConventionAwardPayService;
import com.get.salecenter.dto.ConventionAwardPayDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2021/9/16
 * @TIME: 17:08
 * @Description:
 **/
@Api(tags = "支付流水管理")
@RestController
@RequestMapping("sale/conventionAwardPay")
public class ConventionAwardPayController {
    @Resource
    private IConventionAwardPayService conventionAwardPayService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ConventionAwardVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/支付流水管理/查询支付流水")
    @PostMapping("datas")
    public ResponseBo<ConventionAwardPayVo> datas(@RequestBody SearchBean<ConventionAwardPayDto> page) {
        List<ConventionAwardPayVo> datas = conventionAwardPayService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }
}
