<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.platformconfigcenter.dao.appissue.UserAgentMapper">
  <!--  <select id="getUserIdsByAgent" resultType="com.get.platformconfigcenter.entity.UserAgent">
        SELECT * FROM r_user_agent WHERE fk_agent_id = #{fkAgentId} and fk_company_id=#{fkCompanyId}
    </select>
    <select id="getUsersAgentList" resultType="com.get.platformconfigcenter.entity.UserAgent">
        select
        u.id,u.fk_user_id,u.role_type,u.fk_agent_id,
        u.fk_company_id,u.is_subagent_permission,u.is_bms_permission,u.is_show_app_status_permission,
        m.gmt_create as gmt_create,u.gmt_create_user,u.gmt_modified,u.gmt_modified_user
        from r_user_agent u
        left join ais_app_registration_center.m_user m on m.id = u.fk_user_id
        where u.fk_agent_id in
        <foreach collection="fkAgentIds" item="fkAgentId" index="index" open="(" separator="," close=")">
            #{fkAgentId}
        </foreach>
        order by m.gmt_create DESC
    </select>-->
</mapper>