package com.get.reportcenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

@Data
public class ReportSaleDto extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 报表关联的用户id
     */
    @ApiModelProperty(value = "关联的用户id")
    private Long fkUserId;

    /**
     * 报表名称（参考枚举）
     */
    @ApiModelProperty(value = "报表名称（参考枚举）")
    private String reportName;

    /**
     * 报表查询条件（json）
     */
    @ApiModelProperty(value = "报表查询（json）")
    private String reportQuery;

    /**
     * 报表统计结果（json）
     */
    @ApiModelProperty(value = "报表统计结果（json）")
    private String reportResult;

    /**
     * 报表统计时间
     */
    @ApiModelProperty(value = "报表统计时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportTime;
    /**
     * 状态，0-已完成，1-统计中，2-失败
     */
    @ApiModelProperty(value = "状态，0-已完成，1-统计中，2-失败")
    private Integer reportStatus;
}