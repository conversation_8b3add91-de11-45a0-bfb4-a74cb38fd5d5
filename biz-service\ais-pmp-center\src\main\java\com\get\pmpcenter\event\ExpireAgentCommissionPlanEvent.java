package com.get.pmpcenter.event;

import io.swagger.annotations.ApiModelProperty;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/27  17:12
 * @Version 1.0
 */
public class ExpireAgentCommissionPlanEvent extends ApplicationEvent {

    @ApiModelProperty("合同端过期方案Ids")
    private List<Long> expirePlanIds;

    public ExpireAgentCommissionPlanEvent(Object source, List<Long> expirePlanIds) {
        super(source);
        this.expirePlanIds = expirePlanIds;
    }

    public List<Long> getExpirePlanIds() {
        return expirePlanIds;
    }

    public void setExpirePlanIds(List<Long> expirePlanIds) {
        this.expirePlanIds = expirePlanIds;
    }
}
