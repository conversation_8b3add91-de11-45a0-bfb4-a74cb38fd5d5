package com.get.permissioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.dto.CompanyConfigInfoDto;
import com.get.permissioncenter.dto.ConfigDto;
import com.get.permissioncenter.service.IConfigService;
import com.get.permissioncenter.utils.MD5Utils;
import com.get.permissioncenter.vo.AvatarInfoVo;
import com.get.permissioncenter.vo.ConfigVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @author: jack
 * @create: 2020/6/8
 * @verison: 1.0
 * @description: 系统配置控制器
 */
@Api(tags = "系统配置管理")
@RestController
@RequestMapping("system/config")
public class ConfigController {
    @Resource
    private IConfigService configService;

    /**
     * 列表数据
     *
     * @param page
     * @return
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为关键词，configGroup为类型")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.LIST, description = "系统中心/系统参数管理/查询参数")
    @PostMapping("datas")
    public ResponseBo<ConfigVo> datas(@RequestBody SearchBean<ConfigDto> page) {
        List<ConfigVo> datas = configService.getConfigs(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.DETAIL, description = "系统中心/系统参数管理/参数详情")
    @GetMapping("/{id}")
    public ResponseBo<ConfigVo> detail(@PathVariable("id") Long id) {
        ConfigVo data = configService.findConfigById(id);
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", data);
        return responseBo;
    }


    /**
     * 新增信息
     *
     * @param configDto
     * @return
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.ADD, description = "系统中心/系统参数管理/新增参数")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(ConfigDto.Add.class) ConfigDto configDto) {
        return SaveResponseBo.ok(this.configService.addConfig(configDto));
    }

    /**
     * 修改信息
     *
     * @param configDto
     * @return
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.EDIT, description = "系统中心/系统资源管理/更新参数")
    @PostMapping("update")
    public ResponseBo<ConfigVo> update(@RequestBody @Validated(ConfigDto.Update.class) ConfigDto configDto) {
        return UpdateResponseBo.ok(configService.updateConfig(configDto));
    }

    /**
     * @return java.lang.String
     * @Description: 配置key查询value
     * @Param [key]
     * <AUTHOR>
     **/
    @VerifyPermission(IsVerify = false)
    @ApiIgnore
    @ApiOperation(value = "配置key查询value", notes = "")
    @PostMapping("getConfigValueByConfigKey")
    public String getConfigValueByConfigKey(@RequestParam(name = "key") String key) {
        return configService.getConfigValueByConfigKey(key);
    }


    /**
     * @Description: 配置key和value1查询对象
     * @Author: Jerry
     * @Date:17:47 2021/9/15
     */
    @VerifyPermission(IsVerify = false)
    @ApiIgnore
    @PostMapping("getConfigValueByConfigKeyAndValue")
    public ConfigVo getConfigValueByConfigKeyAndValue(@RequestParam(name = "key") String key,
                                                      @RequestParam(name = "value1") Long value1) {
        return configService.getConfigValueByConfigKeyAndValue(key, value1);
    }

    /**
     * @Description: 配置key查询value（AES加密key，需解密）
     * @Author: Jerry
     * @Date:12:08 2021/9/15
     */
    @VerifyPermission(IsVerify = false)
    @ApiIgnore
    @PostMapping("getConfigValueByConfigKeyAndAESKey")
    public String getConfigValueByConfigKeyAndAESKey(@RequestParam(name = "key") String key, @RequestParam(name = "AESKey") String AESKey) {
        return configService.getConfigValueByConfigKeyAndAESKey(key, AESKey);
    }

    /**
     * @return java.lang.String
     * @Description: 查询类型下拉框
     * @Param [key]
     * <AUTHOR>
     **/
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "查询类型下拉框", notes = "")
    @GetMapping("getGroupSelect")
    public ResponseBo getGroupSelect() {
        return new ResponseBo<>(configService.getGroupSelect());
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.DELETE, description = "系统中心/系统配置管理/删除配置")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        this.configService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * 获取系统配置
     *
     * @Date 14:37 2021/8/16
     * <AUTHOR>
     */
    @ApiOperation(value = "获取系统配置", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.DETAIL, description = "系统中心/系统配置管理/获取系统配置")
    @VerifyPermission(IsVerify = false)
    @GetMapping("getConfigByKey/{key}")
    public ResponseBo getConfigByKey(@PathVariable("key") String key) {
        return new ResponseBo(configService.getConfigByKey(key));
    }


    @Value("${avatarKey.fkPlatformTypes}")
    private String fkPlatformTypes;
    @Value("${avatarKey.appId}")
    private String appId;
    @Value("${avatarKey.appSecret}")
    private String appSecret;

    /**
     * @return java.lang.String
     * @Description: 查询内部系统交互密钥
     * @Param [key]
     * <AUTHOR>
     **/
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "查询内部系统临时交互密钥", notes = "")
    @GetMapping("getAvatarSecret")
    public ResponseBo getAvatarSecret(@RequestParam("userId") String userId,
                                      @RequestParam("fkPlatformType") String fkPlatformType)
    {
        if(GeneralTool.isEmpty(fkPlatformTypes))
        {
            throw new GetServiceException(LocaleMessageUtils.getMessage("platform_type_not_yet_configured_for_login"));
        }
        if(GeneralTool.isEmpty(appId) || GeneralTool.isEmpty(appSecret))
        {
            throw new GetServiceException(LocaleMessageUtils.getMessage("platform_authorization_code_has_not_been_configured_yet"));
        }
        //判断平台是否支持跨系统登录
        if(!fkPlatformTypes.contains(fkPlatformType))
        {
            throw new GetServiceException(LocaleMessageUtils.getMessage("this_platform_does_not_yet_support_cross_system_login"));
        }
        long currentTimeMillis = System.currentTimeMillis ();
        String password = fkPlatformType+userId+currentTimeMillis+appId+appSecret;//固定顺序加密

        AvatarInfoVo avatarInfoVo = new AvatarInfoVo();
        avatarInfoVo.setCurrentTimeMillis(String.valueOf(currentTimeMillis));
        avatarInfoVo.setUserId(userId);
        avatarInfoVo.setBmsLoginId(SecureUtil.getLoginId());
        avatarInfoVo.setUserPwd(MD5Utils.encrypt(password));
        return new ResponseBo<>(avatarInfoVo);
    }

    @ApiOperation(value = "获得项目成员限制配置")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "系统中心/系统配置管理/获得项目成员限制配置")
    @VerifyPermission(IsVerify = false)
    @GetMapping("getProjectLimitConfigKey")
    public ResponseBo getProjectLimitConfigKey(){
        return new ResponseBo<>(configService.getProjectLimitConfigKey(ProjectKeyEnum.ASSIGN_PROJECT_MEMBERS_LIMIT.key));
    }

    @ApiOperation(value = "获取公司配置key")
//    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "系统中心/系统配置管理/获得公司相关配置")
    @VerifyPermission(IsVerify = false)
    @GetMapping("getCompanySettlementConfigInfo")
    public ListResponseBo<CompanyConfigInfoDto> getCompanySettlementConfigInfo(@RequestParam("configKey") String configKey){
        return configService.getCompanySettlementConfigInfo(configKey);
    }

    @ApiOperation(value = "获取枚举下拉")
    @VerifyPermission(IsVerify = false)
    @GetMapping("getEnumSelect")
    public ResponseBo<Map<String, Object>> getEnumSelect(@RequestParam("enumKey") String enumKey) {
        return new ListResponseBo<>(ProjectKeyEnum.enums2Arrays(ProjectKeyEnum.getDynamicArray(enumKey)));
    }

}
