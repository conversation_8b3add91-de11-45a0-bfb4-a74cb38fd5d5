package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.AgentContractCompany;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2020/9/15
 * @TIME: 14:26
 * @Description: 代理合同-公司安全配置DTO
 **/
@Data
public class AgentContractCompanyVo extends BaseEntity implements Serializable {

    //==========实体类AgentContractCompany==============
    private static final long serialVersionUID = 1L;
    /**
     * 学生代理合同Id
     */
    @ApiModelProperty(value = "学生代理合同Id")
    @Column(name = "fk_agent_contract_id")
    private Long fkAgentContractId;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

}
