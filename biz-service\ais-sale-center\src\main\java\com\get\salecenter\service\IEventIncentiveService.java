package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.core.mybatis.utils.ValidList;
import com.get.salecenter.vo.CommentVo;
import com.get.salecenter.vo.EventIncentiveListVo;
import com.get.salecenter.vo.EventIncentiveVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.entity.EventIncentive;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.CommentDto;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/7/19 14:47
 * @verison: 1.0
 * @description:
 */
public interface IEventIncentiveService extends IService<EventIncentive> {

    /**
     * 新增奖励推广活动
     *
     * @param eventIncentiveUpdateDto
     * @return
     */
    Long addEventIncentive(EventIncentiveUpdateDto eventIncentiveUpdateDto);

    /**
     * 配置详情接口
     *
     * @param id
     * @return
     */
    EventIncentiveVo findEventIncentiveById(Long id);

    /**
     * 列表数据
     *
     * @param data
     * @param page
     * @return
     */
    List<EventIncentiveListVo> getEventIncentives(EventIncentiveListDto data, Page page);

    /**
     * 编辑
     *
     * @param eventBillUpdateVo
     * @return
     */
    EventIncentiveVo updateEventIncentive(EventIncentiveUpdateDto eventBillUpdateVo);

    /**
     * 附件列表
     *
     * @param data
     * @param page
     * @return
     */
    List<MediaAndAttachedVo> getItemMedia(MediaAndAttachedDto data, Page page);

    /**
     * 保存附件
     *
     * @param mediaAttachedVo
     * @return
     */
    List<MediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * 编辑评论
     *
     * @param commentDto
     * @return
     */
    Long editComment(CommentDto commentDto);

    /**
     * 评论列表
     *
     * @param commentDto
     * @param page
     * @return
     */
    List<CommentVo> getComments(CommentDto commentDto, Page page);

    /**
     * 分配奖励活动列表
     *
     * @param eventIncentiveDistributeDto
     * @param page
     * @return
     */
    List<EventIncentiveListVo> getEventIncentiveList(EventIncentiveDistributeDto eventIncentiveDistributeDto, Page page);

    void end(Long eventIncentiveId);

    void plan(Long eventIncentiveId);

    void postpone(Long eventIncentiveId);

    void cancel(Long eventIncentiveId);

    /**
     * 删除接口
     *
     * @param id
     */
    void delete(Long id);
}
