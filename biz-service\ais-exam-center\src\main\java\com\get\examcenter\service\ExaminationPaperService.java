package com.get.examcenter.service;

import com.get.common.result.Page;
import com.get.common.result.SearchBean;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.examcenter.vo.ExaminationPaperVo;
import com.get.examcenter.vo.ExaminationQuestionAssignVo;
import com.get.examcenter.vo.ExaminationQuestionVo;
import com.get.examcenter.vo.UserexaminationPaperScoreVo;
import com.get.examcenter.entity.ExaminationPaper;
import com.get.examcenter.dto.ExaminationPaperListDto;
import com.get.examcenter.dto.ExaminationPaperUpdateDto;
import com.get.examcenter.dto.ExaminationQuestionAssignDto;
import com.get.examcenter.dto.ViewLeaderboardDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by <PERSON>.
 * Time: 9:48
 * Date: 2021/8/23
 * Description:考试管理逻辑处理类
 */
public interface ExaminationPaperService {

    /**
     * @Description: 列表数据
     * @Author: Jerry
     * @Date:10:02 2021/8/23
     */
    List<ExaminationPaperVo> getExaminationPaperList(ExaminationPaperListDto examinationPaperListDto, SearchBean<ExaminationPaperListDto> page);

    /**
     * @Description: 新增接口
     * @Author: Jerry
     * @Date:10:50 2021/8/23
     */
    void add(ExaminationPaperUpdateDto examinationPaperUpdateDto);

    /**
     * @Description: 编辑接口
     * @Author: Jerry
     * @Date:10:50 2021/8/23
     */
    void update(ExaminationPaperUpdateDto examinationPaperUpdateDto);


    /**
     * @Description: 删除接口
     * @Author: Jerry
     * @Date:10:51 2021/8/23
     */
    void delete(Long id);


    /**
     * @Description: 详情接口
     * @Author: Jerry
     * @Date:10:51 2021/8/23
     */
    ExaminationPaperVo detail(Long id);


    /**
     * @Description: 激活禁用（true：激活 false：禁用）
     * @Author: Jerry
     * @Date:11:10 2021/8/23
     */
    void updateActive(Long id, boolean isActive);

    /**
     * @Description: 查看排行榜
     * @Author: Jerry
     * @Date:15:43 2021/8/23
     */
    List<UserexaminationPaperScoreVo> viewLeaderboard(Long fkExaminationPaperId, ViewLeaderboardDto viewLeaderboardDto, Page page);

    /**
     * @Description: 分配试题
     * @Author: Jerry
     * @Date:16:13 2021/8/23
     */
    void assignExaminationQuestions(List<ExaminationQuestionAssignDto> examinationQuestionVos);

    /**
     * @Description: 删除已分配的试题
     * @Author: Jerry
     * @Date:10:02 2021/9/3
     */
    void deleteAssignExaminationQuestions(Long id);


    /**
     * @Description: 已分配的试题列表
     * @Author: Jerry
     * @Date:10:20 2021/9/3
     */
    List<ExaminationQuestionAssignVo> assignExaminationQuestionsDatas(Long fkExaminationPaperId);

    /**
     * @Description: 根据考场编号获取考题
     * @Author: Jerry
     * @Date:9:34 2021/8/24
     */
    List<ExaminationQuestionVo> getExaminationQuestions(String examinationPaperNum, Long fkUserId);


    /**
     * @Description: 根据考卷ids获取对象
     * @Author: Jerry
     * @Date:15:18 2021/8/27
     */
    Map<Long, ExaminationPaper> getExaminationPaperByExaminationPaperIds(Set<Long> examinationPaperIds);


    /**
     * @Description: 考卷下拉框
     * @Author: Jerry
     * @Date:9:45 2021/9/3
     */
    List<BaseSelectEntity> examinationPaperSelect(String fkCompanyId);

    /**
     * 生成二维码
     *
     * @param examinationPaperId
     * @return
     */
    String createQrCode(Long examinationPaperId);

    void exportAnswerSituation(HttpServletResponse response, Long fkExaminationPaperId);

    String getExamLinkByPaperId(Long examinationPaperId);

    void copyPaperByPaperId(Long examinationPaperId);
}
