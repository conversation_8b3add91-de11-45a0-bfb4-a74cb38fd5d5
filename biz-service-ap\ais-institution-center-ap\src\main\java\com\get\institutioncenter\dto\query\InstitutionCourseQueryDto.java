package com.get.institutioncenter.dto.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Set;

@Data
public class InstitutionCourseQueryDto {
    /**
     * 数据等级：0旧数据导入/1官方数据导入/2需要补充/3采集完整
     */
    @ApiModelProperty(value = "数据等级：0旧数据导入/1官方数据导入/2需要补充/3采集完整")
    @NotNull(message = "数据等级")
    private Integer dataLevel;

    /**
     * 国家ID
     */
    @ApiModelProperty(value = "国家ID")
    private Long fkAreaCountryId;

    /**
     * 课程类型id
     */
    @ApiModelProperty(value = "课程类型id")
    private Long fkCourseTypeId;

    /**
     * 学校学院ID
     */
    @ApiModelProperty(value = "学校学院ID")
    private Long fkInstitutionFacultyId;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @NotNull(message = "fkInstitutionId")
    private Long fkInstitutionId;

    /**
     * 学校类型Id
     */
    @ApiModelProperty(value = "学校类型Id", required = true)
    private Long fkInstitutionTypeId;

    /**
     * 校区id
     */
    @ApiModelProperty(value = "校区id")
    private Long fkInstitutionZoneId;

    /**
     * 专业等级id
     */
    @ApiModelProperty(value = "专业等级id")
    private Long fkMajorLevelId;

    /**
     * 专业等级数组
     */
    @ApiModelProperty(value = "专业等级数组")
    @NotNull(message = "fkMajorLevelIds")
    private Set<Long> fkMajorLevelIds;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称")
    @NotBlank(message = "name")
    private String name;

    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段")
    private String orderBy;

    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @NotBlank(message = "publicLevel")
    private String publicLevel;

}
