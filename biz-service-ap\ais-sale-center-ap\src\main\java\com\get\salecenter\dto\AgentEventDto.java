package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2020/9/11
 * @TIME: 17:07
 * @Description:
 **/
@Data
public class AgentEventDto extends BaseVoEntity {

    /**
     * 学生代理Id
     */
    @ApiModelProperty(value = "学生代理Id", required = true)
    @NotNull(message = "学生代理Id", groups = {Add.class, Update.class})
    private Long fkAgentId;

    /**
     * 学生代理事件类型Id
     */
    @ApiModelProperty(value = "学生代理事件类型Id", required = true)
    @NotNull(message = "学生代理事件类型Id", groups = {Add.class, Update.class})
    private Long fkAgentEventTypeId;

    /**
     * 事件内容
     */
    @ApiModelProperty(value = "事件内容")
    private String description;

    /**
     * 事件关键字
     */
    @ApiModelProperty(value = "事件关键字")
    private String keyWord;



}
