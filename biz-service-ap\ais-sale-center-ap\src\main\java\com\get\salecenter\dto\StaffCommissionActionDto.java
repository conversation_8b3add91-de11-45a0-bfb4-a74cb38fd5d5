package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @author: Hardy
 * @create: 2023/2/8 11:23
 * @verison: 1.0
 * @description:
 */
@Data
public class StaffCommissionActionDto  extends BaseVoEntity{
    /**
     * 公司Id
     */
    @NotNull(message = "公司Id", groups = {Add.class,Update.class})
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 学生Id
     */
    @NotNull(message = "学生Id", groups = {Add.class,Update.class})
    @ApiModelProperty(value = "学生Id")
    private Long fkStudentId;

    /**
     * 学生申请方案项目Id
     */
    @NotNull(message = "学生申请方案项目Id", groups = {Add.class,Update.class})
    @ApiModelProperty(value = "学生申请方案项目Id")
    private Long fkStudentOfferItemId;

    /**
     * 学生项目角色key
     */
    @NotBlank(message = "学生项目角色key", groups = {Add.class,Update.class})
    @ApiModelProperty(value = "学生项目角色key")
    private String fkStudentProjectRoleKey;

    /**
     * 员工提成业务步骤key
     */
    @NotBlank(message = "员工提成业务步骤key", groups = {Add.class,Update.class})
    @ApiModelProperty(value = "员工提成业务步骤key")
    private String fkStaffCommissionStepKey;

    /**
     * 员工Id（业绩提成）
     */

    @ApiModelProperty(value = "员工Id（业绩提成）")
    private Long fkStaffId;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    /**
     * 结算日期（4位年月），格式如：202303
     */
    @NotNull(message = "结算日期", groups = {Add.class,Update.class})
    @ApiModelProperty(value = "结算日期（4位年月），格式如：202303")
    private String settlementDate;

    /**
     * 提成金额
     */
    @NotNull(message = "提成金额", groups = {Add.class,Update.class})
    @ApiModelProperty(value = "提成金额")
    private BigDecimal commissionAmount;

    /**
     * 状态：0未结算/1已结算
     */
    @ApiModelProperty(value = "状态：0未结算/1已结算")
    private Integer status=0;

    private static final long serialVersionUID = 1L;

  
}
