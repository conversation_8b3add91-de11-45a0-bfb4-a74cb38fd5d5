package com.get.resumecenter.dto.query;

import com.get.core.mybatis.base.BaseEntity;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class ResumeQueryDto extends BaseEntity {

    /**
     * 公司Id
     */
    @NotNull(message = "公司Id不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    @Min(value = 1, message = "缺少公司id参数", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    @ApiModelProperty(value = "公司Id", required = true)
    private Long fkCompanyId;

    /**
     * 简历类型Id
     */
    @NotNull(message = "简历类型Id不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    @ApiModelProperty(value = "简历类型Id", required = true)
    private Long fkResumeTypeId;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    @ApiModelProperty(value = "姓名", required = true)
    private String name;

    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话", required = true)
    @NotBlank(message = "移动电话不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private String mobile;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String institution;

    /**
     * 简历guid(人才中心)
     */
    @ApiModelProperty(value = "简历guid(人才中心)")
    private String resumeGuid;

}
