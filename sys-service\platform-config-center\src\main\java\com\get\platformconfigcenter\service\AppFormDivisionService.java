package com.get.platformconfigcenter.service;

/**
 * 申请表单板块业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2021/5/20 15:33
 */
public interface AppFormDivisionService {
    /**
     * 申请表单板块列表数据
     *
     * @return
     * @Date 15:36 2021/5/20
     * <AUTHOR>
     */
   // List<AppFormDivisionVo> getAppFormDivisionList(AppFormDivisionDto appFormDivisionVo, Page page);

    /**
     * 新增申请表单板块
     *
     * @Date 16:13 2021/5/20
     * <AUTHOR>
     */
   // Long addAppFormDivision(AppFormDivisionDto appFormDivisionVo);

    /**
     * 修改申请表单板块
     *
     * @Date 16:29 2021/5/20
     * <AUTHOR>
     */
//    AppFormDivisionVo updateAppFormDivision(AppFormDivisionDto appFormDivisionVo);

    /**
     * 申请表单板块详情
     *
     * @Date 16:34 2021/5/20
     * <AUTHOR>
     */
    //AppFormDivisionVo findAppFormDivisionById(Long id);

    /**
     * 删除申请表单板块
     *
     * @Date 16:37 2021/5/20
     * <AUTHOR>
     */
    //void delete(Long id);

    /**
     * 上移下移
     *
     * @Date 17:04 2021/5/20
     * <AUTHOR>
     */
    //void movingOrder(List<AppFormDivisionDto> appFormDivisionVos);

    /**
     * 资料板块下拉框数据
     *
     * @Date 15:13 2021/5/26
     * <AUTHOR>
     */
    //List<AppFormDivisionVo> getAppFormDivisionSelect();


}
