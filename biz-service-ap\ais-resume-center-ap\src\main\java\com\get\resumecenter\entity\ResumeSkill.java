package com.get.resumecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_resume_skill")
public class ResumeSkill extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 所属简历Id
     */
    @ApiModelProperty(value = "所属简历Id")
    @Column(name = "fk_resume_id")
    private Long fkResumeId;
    /**
     * 技能类型Id
     */
    @ApiModelProperty(value = "技能类型Id")
    @Column(name = "fk_skill_type_id")
    private Long fkSkillTypeId;
    /**
     * 掌握程度：一般/良好/熟练/精通
     */
    @ApiModelProperty(value = "掌握程度：一般/良好/熟练/精通")
    @Column(name = "skill_level")
    private String skillLevel;
}