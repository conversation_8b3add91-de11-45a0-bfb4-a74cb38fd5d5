package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.TableEnum;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.ClientOfferMapper;
import com.get.salecenter.dao.sale.ClientSourceMapper;
import com.get.salecenter.dao.sale.StudentProjectRoleMapper;
import com.get.salecenter.dao.sale.StudentProjectRoleStaffMapper;
import com.get.salecenter.vo.ClientOfferVo;
import com.get.salecenter.vo.StudentProjectRoleStaffVo;
import com.get.salecenter.vo.StudentRoleAndStaffVo;
import com.get.salecenter.entity.ClientOffer;
import com.get.salecenter.entity.ClientSource;
import com.get.salecenter.entity.StudentProjectRoleStaff;
import com.get.salecenter.service.IAgentService;
import com.get.salecenter.service.IClientOfferService;
import com.get.salecenter.service.IContactPersonService;
import com.get.salecenter.service.IStudentProjectRoleStaffService;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.ClientOfferInfoDto;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * author:Neil
 * Time: 14:10
 * Date: 2022/8/17
 * Description:
 */
@Service
public class ClientOfferServiceImpl implements IClientOfferService {

    @Resource
    private ClientOfferMapper clientOfferMapper;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Resource
    private StudentProjectRoleStaffMapper studentProjectRoleStaffMapper;

    @Resource
    private UtilService<Object> utilService;

    @Resource
    private IContactPersonService contactPersonService;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    @Lazy
    private IAgentService agentService;

    @Resource
    private IStudentProjectRoleStaffService projectRoleStaffService;
    @Resource
    private StudentProjectRoleMapper studentProjectRoleMapper;

    @Resource
    private ClientSourceMapper clientSourceMapper;


    @Override
    public List<ClientOfferVo> getClientOfferList(ClientOfferDto clientOfferDto, Page page) {
        if (GeneralTool.isEmpty(clientOfferDto.getFkClientId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        //获取业务下属
        List<Long> staffFollowerIds = new ArrayList<>();
        Result<List<Long>> longResult = permissionCenterClient.getStaffFollowerIds(SecureUtil.getStaffId());
        if (longResult.isSuccess() && GeneralTool.isNotEmpty(longResult.getData())) {
            staffFollowerIds.addAll(longResult.getData());
        }
        if (GeneralTool.isEmpty(staffFollowerIds)) {
            staffFollowerIds = new ArrayList<>();
        }
        staffFollowerIds.add(SecureUtil.getStaffId());
        IPage<ClientOfferVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<ClientOfferVo> clientOfferList = clientOfferMapper.getClientOfferList(clientOfferDto,staffFollowerIds);
        page.setAll((int) iPage.getTotal());
        //获取学生集合的所有国家ids
        if (GeneralTool.isEmpty(clientOfferList)){
            return Collections.emptyList();
        }
        Set<Long> agentIds = clientOfferList.stream().map(ClientOfferVo::getFkAgentId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> agentNamesMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(agentIds)){
            agentNamesMap = agentService.getAgentNamesByIds(agentIds);
        }

        Set<Long> countryIds = clientOfferList.stream().map(ClientOfferVo::getFkAreaCountryId).collect(Collectors.toSet());
        //根据国家ids获取国家名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getCountryNamesByIds(countryIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                countryNamesByIds = result.getData();
            }
        }

        Set<Long> ids = clientOfferList.stream().map(ClientOfferVo::getId).collect(Collectors.toSet());
        //查询项目成员
        List<StudentProjectRoleStaffVo> studentProjectRoleStaffVos = studentProjectRoleStaffMapper.selectProjectStaff(TableEnum.CLIENT_OFFER.key, new ArrayList<>(ids));
        Map<Long, List<StudentProjectRoleStaffVo>> studentProjectRoleStaffMap =
                studentProjectRoleStaffVos.stream().collect(Collectors.groupingBy(StudentProjectRoleStaffVo::getFkTableId));

        for (ClientOfferVo clientOfferVo : clientOfferList) {
            clientOfferVo.setFkAreaCountryName(countryNamesByIds.get(clientOfferVo.getFkAreaCountryId()));
            clientOfferVo.setProjectRoleStaffDtos(studentProjectRoleStaffMap.get(clientOfferVo.getId()));
            if (GeneralTool.isNotEmpty(clientOfferVo.getFkAgentId())){
                clientOfferVo.setAgentName(agentNamesMap.get(clientOfferVo.getFkAgentId()));
            }
        }

        return clientOfferList;
    }

    @Override
    public Long addProjectRoleStaff(StudentProjectRoleStaff projectRoleStaff) {
        utilService.setCreateInfo(projectRoleStaff);
        projectRoleStaff.setActiveDate(new Date());
        studentProjectRoleStaffMapper.insert(projectRoleStaff);
        return projectRoleStaff.getId();
    }

    @Override
    @Transactional
    public void updateStatus(ClientOfferUpdateDto clientOfferVo) {
        if (GeneralTool.isEmpty(clientOfferVo.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ClientOffer clientOffer = clientOfferMapper.selectById(clientOfferVo.getId());
        if (GeneralTool.isNotEmpty(clientOffer)){
            if (GeneralTool.isNotEmpty(clientOfferVo.getStatus())){
                clientOffer.setStatus(clientOfferVo.getStatus());
            }
            if (GeneralTool.isNotEmpty(clientOfferVo.getFkClientOfferStepId())){
                clientOffer.setFkClientOfferStepId(clientOfferVo.getFkClientOfferStepId());
            }
            utilService.updateUserInfoToEntity(clientOffer);
            clientOfferMapper.updateById(clientOffer);
        }
        //  解绑 项目成员
        if (GeneralTool.isNotEmpty(clientOfferVo.getStatus()) && clientOfferVo.getStatus() == 0){
            //解绑项目成员
            projectRoleStaffService.update(Wrappers.<StudentProjectRoleStaff>lambdaUpdate().set(StudentProjectRoleStaff::getIsActive,false)
                    .set(StudentProjectRoleStaff::getUnactiveDate,new Date())
                    .set(StudentProjectRoleStaff::getGmtModified, clientOffer.getGmtModified())
                    .set(StudentProjectRoleStaff::getGmtModifiedUser, clientOffer.getGmtModifiedUser())
                    .eq(StudentProjectRoleStaff::getIsActive,true)
                    .eq(StudentProjectRoleStaff::getFkTableId,clientOfferVo.getId())
                    .eq(StudentProjectRoleStaff::getFkTableName,TableEnum.CLIENT_OFFER.key));
        }
    }


    /**
     * Author Cream
     * Description : // 获取客户方案详情
     * Date 2022/8/18 9:52
     * Params: id
     * Return ClientOfferInfoDto
     */
    @Override
    public ResponseBo<ClientOfferInfoDto> getClientInfoById(Long id) {
        if (GeneralTool.isNull(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ClientOfferVo clientOffer = clientOfferMapper.getClientOfferInfoById(id);
        if (clientOffer != null) {
            ClientOfferInfoDto offerInfoVo = BeanCopyUtils.objClone(clientOffer, ClientOfferInfoDto::new);
//            ClientOfferInfoDto offerInfoVo = new ClientOfferInfoDto();
//            offerInfoVo.setId(clientOffer.getId());
            Long countryId = clientOffer.getFkAreaCountryId();
            Result<String> countryNameById = institutionCenterClient.getCountryNameById(countryId);
            if (countryNameById.isSuccess() && GeneralTool.isNotEmpty(countryNameById.getData())) {
                offerInfoVo.setFkAreaCountryName(countryNameById.getData());
            }
            //代理需求变动
//            Long fkAgentId = clientOffer.getFkAgentId();
//            offerInfoVo.setFkAgentId(fkAgentId);
//            Agent agent = agentService.getAgentById(fkAgentId);
//            if (agent!=null) {
//                offerInfoVo.setFkAgentName(agent.getName());
//            }
//            offerInfoVo.setRemark(clientOffer.getRemark());
//            offerInfoVo.setNum(clientOffer.getNum());
//            offerInfoVo.setAgentName(clientOffer.getAgentName());
//            offerInfoVo.setAgentContactTel(clientOffer.getAgentContactTel());
//            offerInfoVo.setAgentContactEmail(clientOffer.getAgentContactEmail());
//            offerInfoVo.setFkAreaCountryId(clientOffer.getFkAreaCountryId());
//            offerInfoVo.setFkAgentId(clientOffer.getFkAgentId());
//            if (GeneralTool.isNotEmpty(clientOffer.getFkContactPersonId())) {
//                ContactPersonDto contactPersonDto = contactPersonService.finContactPersonById(clientOffer.getFkContactPersonId());
//                if (GeneralTool.isNotEmpty(contactPersonDto) && GeneralTool.isNotEmpty(contactPersonDto.getEmail())) {
//                    offerInfoVo.setEmail(contactPersonDto.getEmail());
//                }
//            }
            offerInfoVo.setClientName(clientOffer.getName());
            if (StringUtils.isNotBlank(clientOffer.getLastName()) && StringUtils.isNotBlank(clientOffer.getFirstName())) {
                offerInfoVo.setClientName(clientOffer.getName() + "(" + clientOffer.getLastName() +" " + clientOffer.getFirstName()+")");
            }
            List<Long> ids = new ArrayList<Long>(1) {{
                add(clientOffer.getId());
            }};
            //需求变动，去掉BD
//            Result<String> staffName = permissionCenterClient.getStaffName(clientOffer.getFkStaffId());
//            if (staffName.isSuccess() && staffName.getData()!=null) {
//                offerInfoVo.setBdName(staffName.getData());
//            }
            List<StudentProjectRoleStaffVo> roleStaffDtos = studentProjectRoleStaffMapper.selectProjectStaff(TableEnum.CLIENT_OFFER.key, ids);
            String str = roleStaffDtos.stream().map(StudentProjectRoleStaffVo::getStaffIdStr).collect(Collectors.joining(","));
            if (StringUtils.isNotBlank(str)) {
                String[] split = str.split(",");
                Set<Long> collect = Arrays.stream(split).map(Long::valueOf).collect(Collectors.toSet());
                Result<Map<Long, String>> staffNameMap = permissionCenterClient.getStaffNameMap(collect);
                if (staffNameMap.isSuccess() && GeneralTool.isNotEmpty(staffNameMap.getData())) {
                    Map<Long, String> data = staffNameMap.getData();
                    for (StudentProjectRoleStaffVo staffDto : roleStaffDtos) {
                        String staffIdStr = staffDto.getStaffIdStr();
                        if (StringUtils.isNotBlank(staffIdStr)) {
                            String[] strings = staffIdStr.split(",");
                            StringBuilder builder = new StringBuilder();
                            Set<Long> longs = Arrays.stream(strings).map(Long::valueOf).collect(Collectors.toSet());
                            for (Long aLong : longs) {
                                if (StringUtils.isNotBlank(data.get(aLong))) {
                                    builder.append(data.get(aLong)).append(",");
                                }
                            }
                            String s = builder.toString();
                            if (s.endsWith(",")) {
                                s = s.substring(0, s.length() - 1);
                            }
                            staffDto.setStaffName(s);
                        }
                    }
                    offerInfoVo.setStudentProjectRoleStaffDtos(roleStaffDtos);
                }
            }
            List<StudentRoleAndStaffVo> roleAndStaff = projectRoleStaffService.getRoleAndStaffByTableId(clientOffer.getId(),TableEnum.CLIENT_OFFER.key);
            offerInfoVo.setStudentRoleAndStaffList(roleAndStaff);
            return new ResponseBo<>(offerInfoVo);
        }
        return new ResponseBo<>(null);
    }

    /**
     * Author Cream
     * Description : // 新增方案
     * Date 2022/8/18 12:04
     * Params: clientOfferAddDto
     * Return Long
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBo<Long> addClientOffer(ClientOfferAddDto clientOfferAddDto) {
        if (GeneralTool.isEmpty(clientOfferAddDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ClientOffer offer = new ClientOffer();
        BeanUtils.copyProperties(clientOfferAddDto, offer);
//        offer.setFkContactPersonId(getContactPersonId(clientOfferAddDto));
        offer.setStatus(1);
        //新增默认1
        offer.setFkClientOfferStepId(1L);
        utilService.setCreateInfo(offer);
        clientOfferMapper.insert(offer);
        String offerNum = MyStringUtils.getClientOfferNum(offer.getId());
        offer.setNum(offerNum);
        clientOfferMapper.updateById(offer);
        List<ProjectRoleStaffDto> roleStaffVo = clientOfferAddDto.getRoleStaffVo();
        if (GeneralTool.isNotEmpty(roleStaffVo)) {
            List<StudentProjectRoleStaff> addList = new ArrayList<>(roleStaffVo.size());
            String loginId = SecureUtil.getLoginId();
            for (ProjectRoleStaffDto staffVo : roleStaffVo) {
                StudentProjectRoleStaff projectRoleStaff = new StudentProjectRoleStaff();
                projectRoleStaff.setFkTableId(offer.getId());
                projectRoleStaff.setFkTableName(TableEnum.CLIENT_OFFER.key);
                projectRoleStaff.setFkStaffId(staffVo.getFkStaffId());
                projectRoleStaff.setFkStudentProjectRoleId(staffVo.getFkRoleId());
                projectRoleStaff.setIsActive(true);
                projectRoleStaff.setGmtCreate(new Date());
                projectRoleStaff.setGmtCreateUser(loginId);
                addList.add(projectRoleStaff);
            }
            projectRoleStaffService.batchAdd(addList);
        }
        return new ResponseBo<>(offer.getId());
    }




//    private Long getContactPersonId(ClientOfferAddDto clientOfferAddVo) {
//        Long fkContactPersonId = null;
//        if (StringUtils.isNotBlank(clientOfferAddVo.getEmail())) {
//            List<SaleContactPerson> person = contactPersonService.getContactPersonByFkTableId(TableEnum.SALE_AGENT.key, clientOfferAddVo.getFkAgentId());
//            String emailJoin = person.stream().map(SaleContactPerson::getEmail).collect(Collectors.joining("; "));
//            List<String> list = Arrays.asList(emailJoin.split("; "));
//            String email = clientOfferAddVo.getEmail();
//            String[] splitEmail = email.split("; ");
//            boolean anyMatch = Arrays.stream(splitEmail).anyMatch(list::contains);
//            if (!anyMatch) {
//                String name = email.substring(0, email.indexOf("@"));
//                ContactPersonVo contactPersonVo = new ContactPersonVo();
//                contactPersonVo.setEmail(email);
//                contactPersonVo.setName(name);
//                contactPersonVo.setFkTableName(TableEnum.SALE_AGENT.key);
//                contactPersonVo.setFkTableId(clientOfferAddVo.getFkAgentId());
//                contactPersonVo.setFkContactPersonTypeKey("CONTACT_AGENT_SALES");
//                fkContactPersonId = contactPersonService.addContactPerson(contactPersonVo);
//            } else {
//                List<SaleContactPerson> contactPersonList = person.stream().filter(p -> {
//                    if (StringUtils.isNotBlank(p.getEmail())) {
//                        return Arrays.stream(splitEmail).anyMatch(Arrays.asList(p.getEmail().split("; "))::contains);
//                    }
//                    return false;
//                }).collect(Collectors.toList());
//                if (!contactPersonList.isEmpty()) {
//                    fkContactPersonId = contactPersonList.get(0).getId();
//                }
//            }
//        }
//        return fkContactPersonId;
//    }

    /**
     * Author Cream
     * Description : //更新客户资源方案
     * Date 2022/8/18 12:04
     * Params: clientOfferAddDto
     * Return ClientOfferInfoDto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBo<ClientOfferInfoDto> updateClientOffer(ClientOfferAddDto clientOfferAddDto) {
        if (GeneralTool.isEmpty(clientOfferAddDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Long id = clientOfferAddDto.getId();
        ClientOffer clientOffer = clientOfferMapper.selectById(id);
        if (clientOffer!=null) {
            ClientOffer offer = new ClientOffer();
            BeanUtils.copyProperties(clientOfferAddDto,offer);
//            offer.setFkContactPersonId(getContactPersonId(clientOfferAddDto));
            List<ProjectRoleStaffDto> roleStaffVo = clientOfferAddDto.getRoleStaffVo();
            offer.setGmtModifiedUser(SecureUtil.getLoginId());
            offer.setGmtModified(new Date());
            if (GeneralTool.isEmpty(clientOfferAddDto.getAgentName())){
                offer.setAgentName(null);
            }
            if (GeneralTool.isEmpty(clientOfferAddDto.getFkAgentId())){
                offer.setFkAgentId(null);
            }
            clientOfferMapper.updateByIdWithNull(offer);
            projectRoleStaffService.batchProcessorPrs(roleStaffVo,offer.getId(),TableEnum.CLIENT_OFFER.key);
            return getClientInfoById(offer.getId());
        }
        throw new GetServiceException(LocaleMessageUtils.getMessage("client_offer_not_exists"));
    }


    @Override
    public List<BaseSelectEntity> getClientOfferSelect(Long clientId) {
        return clientOfferMapper.getClientOfferSelect(clientId);
    }

    @Override
    public StudentProjectRoleStaffVo updateProjectRoleStaff(StudentProjectRoleStaffUpdateDto projectRoleStaffVo) {
        StudentProjectRoleStaffDto studentProjectRoleStaffDto = new StudentProjectRoleStaffDto();
        BeanUtils.copyProperties(projectRoleStaffVo, studentProjectRoleStaffDto);
        return projectRoleStaffService.updateProjectRoleStaff(studentProjectRoleStaffDto);
    }

    @Override
    public List<StudentProjectRoleStaffVo> getProjectRoleStaff(ClientProjectRoleStaffDto clientProjectRoleStaffDto, Page page) {
        if (GeneralTool.isEmpty(clientProjectRoleStaffDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(clientProjectRoleStaffDto.getFkClientId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("student_id_null"));
        }
        StudentProjectRoleStaffDto studentProjectRoleStaffDto = new StudentProjectRoleStaffDto();
        studentProjectRoleStaffDto.setFkStudentId(clientProjectRoleStaffDto.getFkClientId());
        studentProjectRoleStaffDto.setFkTableName(clientProjectRoleStaffDto.getFkTableName());
        return projectRoleStaffService.getClientProjectRoleStaff(studentProjectRoleStaffDto,page);
    }

    @Override
    public Map<Long, String> getNumByIds(Set<Long> insuranceIds) {
        if (GeneralTool.isEmpty(insuranceIds)) {
            return new HashMap<>();
        }
        List<ClientOffer> clientOffers = clientOfferMapper.selectBatchIds(insuranceIds);
        if (GeneralTool.isEmpty(clientOffers)) {
            return null;
        }
        Map<Long, String> map = new HashMap<>();
        for (ClientOffer clientOffer : clientOffers) {
            map.put(clientOffer.getId(), clientOffer.getNum());
        }
        return map;
    }

    @Override
    public List<BaseSelectEntity> getRoleStaffByRoleSelect() {
        //分公司,获取员工角色
        StudentProjectRoleStaffDto studentProjectRoleStaffDto = new StudentProjectRoleStaffDto();
//        studentProjectRoleStaffDto.setFkStudentId(clientProjectRoleStaffVo.getFkClientId());
        studentProjectRoleStaffDto.setFkTableName(TableEnum.CLIENT_OFFER.key);
        List<StudentProjectRoleStaffVo> clientProjectRoleStaff = projectRoleStaffService.getClientProjectRoleStaff(studentProjectRoleStaffDto, null);
        if (GeneralTool.isEmpty(clientProjectRoleStaff)){
            return Collections.emptyList();
        }
        List<BaseSelectEntity> baseSelectEntities = new ArrayList<>();
        Set<Long> ids = clientProjectRoleStaff.stream().map(StudentProjectRoleStaffVo::getFkStaffId).collect(Collectors.toSet());
        List<StaffVo> staffVos = permissionCenterClient.getStaffDtoByIds(ids);
        for (StaffVo staffVo : staffVos) {
            BaseSelectEntity baseSelect = new BaseSelectEntity();
            baseSelect.setId(staffVo.getId());
            baseSelect.setName(staffVo.getName());
            baseSelect.setFullName(staffVo.getFullName());
            baseSelectEntities.add(baseSelect);
        }

//        LambdaQueryWrapper<StudentProjectRole> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(StudentProjectRole::getRoleKey,"GEA_RESOURCE_ARC");
//        List<StudentProjectRole> studentProjectRoles = studentProjectRoleMapper.selectList(lambdaQueryWrapper);
//        if (GeneralTool.isEmpty(studentProjectRoles)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
//        List<String> departmentIds = studentProjectRoles.stream().map(StudentProjectRole::getDepartmentNum).collect(Collectors.toList());
////        String[] departmentIds = studentProjectRole.getDepartmentNum().split(",");
//        List<Long> departmentIdsList = new ArrayList<>();
//        for (String departmentIdStr : departmentIds) {
//
//            String[] departmentIdsArr = departmentIdStr.split(",");
//            for (String departmentid: departmentIdsArr) {
//                Long departmentId = Long.valueOf(departmentid);
//                departmentIdsList.add(departmentId);
//            }
//        }
////        return permissionCenterClient.getStaffByDepartmentIds(departmentIdsList);
//        Result<List<BaseSelectEntity>> result = permissionCenterClient.getStaffByDepartmentIds(departmentIdsList);
//        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
//            return result.getData();
//        }
        return baseSelectEntities;
    }

    @Override
    public void updateStep(ClientOfferUpdateDto clientOfferVo) {
        if (GeneralTool.isEmpty(clientOfferVo.getFkClientOfferStepId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        ClientOffer clientOffer = BeanCopyUtils.objClone(clientOfferVo, ClientOffer::new);
        utilService.setUpdateInfo(clientOffer);
        clientOfferMapper.updateById(clientOffer);
    }

    @Override
    public List<BaseSelectEntity> getAgentSelect(Long clientId) {
        List<ClientSource> clientSources = clientSourceMapper.selectList(Wrappers.lambdaQuery(ClientSource.class)
                .eq(ClientSource::getFkClientId, clientId));
        if (GeneralTool.isEmpty(clientSources)){
            return Collections.emptyList();
        }
        Set<Long> agentIds = clientSources.stream().map(ClientSource::getFkAgentId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (GeneralTool.isEmpty(agentIds)){
            return Collections.emptyList();
        }
        List<BaseSelectEntity> baseSelectEntities = Lists.newArrayList();
        Map<Long, String> agentNamesMap = agentService.getAgentNamesByIds(agentIds);
        for (Long agentId : agentIds) {
            BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
            baseSelectEntity.setId(agentId);
            baseSelectEntity.setFullName(agentNamesMap.get(agentId));
            baseSelectEntity.setName(agentNamesMap.get(agentId));
            baseSelectEntities.add(baseSelectEntity);
        }
        return baseSelectEntities;
    }

    @Override
    public ResponseBo<BaseSelectEntity> getClientAgentSelection(Long fkClientId) {
        return new ListResponseBo<>(clientOfferMapper.getClientAgentSelect(fkClientId));
    }
}
