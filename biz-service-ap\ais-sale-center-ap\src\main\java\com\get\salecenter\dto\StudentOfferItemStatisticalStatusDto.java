package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 学习计划统计状态栏
 *
 * <AUTHOR>
 * @date 2021/7/26 12:49
 */
@Data
public class StudentOfferItemStatisticalStatusDto extends BaseVoEntity {
    @NotNull(message = "公司id不能为空")
    @Min(value = 1, message = "缺少公司id参数")
    @ApiModelProperty(value = "公司id")
    private Long companyId;

    @ApiModelProperty(value = "项目成员")
    private List<Long> staffIds;

    @ApiModelProperty(value = "项目成员名称")
    private String fkProjectMemberName;

    @ApiModelProperty(value = "业务国家Key")
    private List<Long> areaCountryIds;

    @ApiModelProperty("修改时间(开始)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty("修改时间(结束)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    /**
     * 是否管理员
     */
    private Boolean isAdmin;
    /**
     * BD编号
     */
    @ApiModelProperty("BD编号/BD名称")
    private String bdCode;

    /**
     *  时间搜索类型：1-操作步骤时间；2-申请创建时间；3-学生开学时间
     */
    @ApiModelProperty("时间搜索类型：1-操作步骤时间；2-申请创建时间；3-学生开学时间")
    private Integer selectType;

   
}
