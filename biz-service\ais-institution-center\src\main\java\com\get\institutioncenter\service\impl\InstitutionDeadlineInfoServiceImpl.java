package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.TableEnum;
import com.get.common.result.SearchBean;
import com.get.common.utils.Assert;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.institutioncenter.dao.InstitutionCourseAppInfoMapper;
import com.get.institutioncenter.dao.InstitutionDeadlineInfoMapper;
import com.get.institutioncenter.dao.InstitutionDeadlineInfoMapper2;
import com.get.institutioncenter.dto.InstitutionCourseAppInfoDataProcessDto;
import com.get.institutioncenter.vo.InstitutionDeadlineInfoVo;
import com.get.institutioncenter.vo.InstitutionDeadlineInfoVo2;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.entity.Institution;
import com.get.institutioncenter.entity.InstitutionCourseAppInfo;
import com.get.institutioncenter.entity.InstitutionDeadlineInfo;
import com.get.institutioncenter.entity.InstitutionDeadlineInfo2;
import com.get.institutioncenter.service.*;
import com.get.institutioncenter.dto.InstitutionDeadlineInfoDto;
import com.get.institutioncenter.dto.InstitutionDeadlineInfoDto2;
import com.get.institutioncenter.dto.WeScholarshipAppDto;
import com.get.institutioncenter.dto.query.InstitutionDeadlineInfoQueryDto;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/6 15:46
 */
@Service
public class InstitutionDeadlineInfoServiceImpl extends BaseServiceImpl<InstitutionDeadlineInfoMapper, InstitutionDeadlineInfo> implements IInstitutionDeadlineInfoService {
    @Resource
    private UtilService<Object> utilService;
    @Resource
    private IInstitutionFacultyService iInstitutionFacultyService;
    @Resource
    private ICourseTypeGroupService courseTypeGroupService;
    @Resource
    private ICourseTypeService courseTypeService;
    @Resource
    private IInstitutionMajorService iInstitutionMajorService;
    @Resource
    private IInstitutionCourseService iInstitutionCourseService;
    @Resource
    private IAreaCountryService areaCountryService;
    @Resource
    private InstitutionDeadlineInfoMapper institutionDeadlineInfoMapper;
    @Resource
    private InstitutionDeadlineInfoMapper2 institutionDeadlineInfoMapper2;
    @Resource
    @Lazy
    private IInstitutionService institutionService;
    @Resource
    private MediaAndAttachedServiceImpl mediaAndAttachedService;
    @Resource
    private IFileCenterClient iFileCenterClient;
    @Resource
    private IInstitutionViewOrderService iInstitutionViewOrderService;
    @Resource
    private InstitutionCourseAppInfoMapper institutionCourseAppInfoMapper;
    @Resource
    @Lazy
    private IInstitutionCourseAppInfoService iInstitutionCourseAppInfoService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addInstitutionDeadlineInfo(InstitutionDeadlineInfoDto institutionDeadlineInfoDto) {
        InstitutionDeadlineInfo idnstitutionDeadlineInfo = BeanCopyUtils.objClone(institutionDeadlineInfoDto, InstitutionDeadlineInfo::new);
        utilService.setCreateInfo(idnstitutionDeadlineInfo);
        int i = institutionDeadlineInfoMapper.insert(idnstitutionDeadlineInfo);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        iInstitutionCourseAppInfoService.batchAdd(institutionDeadlineInfoDto.getCourseAppInfos(),idnstitutionDeadlineInfo.getId(),TableEnum.INSTITUTION_DEAD_LINE_INFO.key, institutionDeadlineInfoDto.getEffectiveDate());
        return idnstitutionDeadlineInfo.getId();
    }

    @Override
    public InstitutionDeadlineInfoVo findInstitutionDeadlineInfoById(Long id) {
        InstitutionDeadlineInfoVo institutionDeadlineInfoVo = institutionDeadlineInfoMapper.selectInfoById(id);
        if (GeneralTool.isEmpty(institutionDeadlineInfoVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionCourseAppInfoDataProcessDto processVo = BeanCopyUtils.objClone(institutionDeadlineInfoVo, InstitutionCourseAppInfoDataProcessDto::new);
        List<InstitutionCourseAppInfoDataProcessDto> list = new ArrayList<>(1);
        list.add(processVo);
        iInstitutionCourseAppInfoService.packageInfo(list,false,TableEnum.INSTITUTION_DEAD_LINE_INFO.key);
        Assert.notNull(processVo,"processVo is null");
        BeanUtils.copyProperties(processVo, institutionDeadlineInfoVo);
        return institutionDeadlineInfoVo;
    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        InstitutionDeadlineInfo idnstitutionDeadlineInfo = institutionDeadlineInfoMapper.selectById(id);
        if (GeneralTool.isEmpty(idnstitutionDeadlineInfo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        int i = institutionDeadlineInfoMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        institutionCourseAppInfoMapper.delete(Wrappers.<InstitutionCourseAppInfo>lambdaQuery()
                .eq(InstitutionCourseAppInfo::getFkTableName,TableEnum.INSTITUTION_DEAD_LINE_INFO.key)
                .eq(InstitutionCourseAppInfo::getFkTableId,id));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public InstitutionDeadlineInfoVo updateInstitutionDeadlineInfo(InstitutionDeadlineInfoDto institutionDeadlineInfoDto) {
        if (GeneralTool.isEmpty(institutionDeadlineInfoDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        InstitutionDeadlineInfo institutionDeadlineInfo = institutionDeadlineInfoMapper.selectById(institutionDeadlineInfoDto.getId());
        if (GeneralTool.isEmpty(institutionDeadlineInfo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionDeadlineInfo institutionDeadlineInfo1 = BeanCopyUtils.objClone(institutionDeadlineInfoDto, InstitutionDeadlineInfo::new);
        utilService.updateUserInfoToEntity(institutionDeadlineInfo1);
        institutionDeadlineInfoMapper.updateById(institutionDeadlineInfo1);
        iInstitutionCourseAppInfoService.batchUpdate(institutionDeadlineInfoDto.getCourseAppInfos(),institutionDeadlineInfo1.getId(),TableEnum.INSTITUTION_DEAD_LINE_INFO.key, institutionDeadlineInfoDto.getEffectiveDate());
        return findInstitutionDeadlineInfoById(institutionDeadlineInfo1.getId());
    }

    @Override
    public List<InstitutionDeadlineInfoVo> datas(InstitutionDeadlineInfoQueryDto data, SearchBean<InstitutionDeadlineInfoQueryDto> page) {
        IPage<InstitutionDeadlineInfo> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())));
        List<Long> countryIds = SecureUtil.getCountryIds();
        List<InstitutionDeadlineInfoVo> datas = institutionDeadlineInfoMapper.datas(pages, data,countryIds);
        page.setAll((int) pages.getTotal());
        packProcess(datas,false);
        return datas;
    }

    /**
     * 结果处理
     * @param institutionDeadlineInfoVos
     * @param flag 是否要获取图片url
     */
    private void packProcess(List<InstitutionDeadlineInfoVo> institutionDeadlineInfoVos, boolean flag){
        List<InstitutionCourseAppInfoDataProcessDto> processVos = BeanCopyUtils.copyListProperties(institutionDeadlineInfoVos, InstitutionCourseAppInfoDataProcessDto::new);
        iInstitutionCourseAppInfoService.packageInfo(processVos,flag,TableEnum.INSTITUTION_DEAD_LINE_INFO.key);
        Map<Long, InstitutionCourseAppInfoDataProcessDto> voMap = processVos.stream().collect(Collectors.toMap(InstitutionCourseAppInfoDataProcessDto::getId, Function.identity()));
        for (InstitutionDeadlineInfoVo deadlineInfoDto : institutionDeadlineInfoVos) {
            InstitutionCourseAppInfoDataProcessDto processVo = voMap.get(deadlineInfoDto.getId());
            BeanUtils.copyProperties(processVo,deadlineInfoDto);
        }
    }

    /**
     * Author Cream
     * Description : //优先匹配查询
     * Date 2022/11/22 10:11
     * Params:
     * Return
     */
    @Override
    public List<InstitutionDeadlineInfoVo> priorityMatchingQuery(WeScholarshipAppDto weScholarshipAppDto) {
        List<InstitutionCourseAppInfoDataProcessDto> processVo = iInstitutionCourseAppInfoService.testPriority(weScholarshipAppDto, TableEnum.INSTITUTION_DEAD_LINE_INFO.key);
        if (GeneralTool.isEmpty(processVo)) {
            return null;
        }
        List<Long> ids = processVo.stream().map(InstitutionCourseAppInfoDataProcessDto::getId).collect(Collectors.toList());
        List<InstitutionDeadlineInfoVo> deadlineInfoDtos = institutionDeadlineInfoMapper.selectInfoByIds(ids);
        Map<Long, InstitutionCourseAppInfoDataProcessDto> collect = processVo.stream().collect(Collectors.toMap(InstitutionCourseAppInfoDataProcessDto::getId, Function.identity()));
        for (InstitutionDeadlineInfoVo appFeeDto : deadlineInfoDtos) {
            BeanUtils.copyProperties(collect.get(appFeeDto.getId()),appFeeDto);
        }
        deadlineInfoDtos.sort(Comparator.comparing(InstitutionDeadlineInfoVo::getGmtPriorityTime).reversed());
        return deadlineInfoDtos;
    }

    /**
     * 获取最优先匹配信息
     * @param list
     * @return
     */
    private InstitutionDeadlineInfoVo priorityGet(List<InstitutionDeadlineInfoVo> list){
        list.sort(((o1, o2) -> o2.getPriority()-o1.getPriority()));
        Integer maxPriority = list.get(0).getPriority();
        Map<Integer, List<InstitutionDeadlineInfoVo>> collect = list.stream().collect(Collectors.groupingBy(InstitutionDeadlineInfoVo::getPriority));
        //匹配数的最高优先级集合
        List<InstitutionDeadlineInfoVo> maxPriorityList = collect.get(maxPriority);
        for (InstitutionDeadlineInfoVo dto : maxPriorityList) {
            if (dto.getFk().equals(dto.getFc())) {
                return dto;
            }
            dto.setPriority(dto.getFc().split(",").length);
        }
        maxPriorityList.sort(Comparator.comparing(InstitutionDeadlineInfoVo::getPriority));
        return maxPriorityList.get(0);
    }

    @Override
    public List<InstitutionDeadlineInfoVo2> getWcInstitutionDeadlineInfoDtoDatas(Long fkInstitutionId) {
        LambdaQueryWrapper<InstitutionDeadlineInfo2> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InstitutionDeadlineInfo2::getFkInstitutionId, fkInstitutionId).orderByDesc(InstitutionDeadlineInfo2::getId);
        List<InstitutionDeadlineInfo2> institutionDeadlineInfos = institutionDeadlineInfoMapper2.selectList(lambdaQueryWrapper);
        List<InstitutionDeadlineInfoVo2> institutionDeadlineInfos1 = BeanCopyUtils.copyListProperties(institutionDeadlineInfos, InstitutionDeadlineInfoVo2::new);
        if (GeneralTool.isEmpty(institutionDeadlineInfos1)) {
            return institutionDeadlineInfos1;
        }


        Iterator<InstitutionDeadlineInfoVo2> iterator = institutionDeadlineInfos1.iterator();
        while (iterator.hasNext()) {
            InstitutionDeadlineInfoVo2 next = iterator.next();
            boolean b = !next.getPublicLevel().contains("0");
            if (!b) {
                iterator.remove();
            }
        }
        if (GeneralTool.isEmpty(institutionDeadlineInfos1)) {
            return institutionDeadlineInfos1;
        }
        //TODO 改过
        //Set<Long> collect = institutionDeadlineInfos1.stream().map(InstitutionDeadlineInfo2::getFkInstitutionId).collect(Collectors.toSet());
        Set<Long> collect = institutionDeadlineInfos1.stream().map(InstitutionDeadlineInfoVo2::getFkInstitutionId).collect(Collectors.toSet());
        Map<Long, Institution> map = institutionService.getInstitutionByIds(collect);


        institutionDeadlineInfos1.stream().forEach(d -> {
            d.setFkInstitutionNameEn(map.get(d.getFkInstitutionId()).getName());
            Optional.ofNullable(map.get(d.getFkInstitutionId()).getNameChn()).ifPresent(dd -> d.setFkInstitutionNameZh(dd));
        });
        return institutionDeadlineInfos1;
    }

    @Override
    public List<InstitutionDeadlineInfoVo2> getWcInstitutionDeadlineInfoList(InstitutionDeadlineInfoDto2 data, SearchBean<InstitutionDeadlineInfoDto2> page) {
        IPage<InstitutionDeadlineInfoDto2> ipage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<InstitutionDeadlineInfoVo2> wcInstitutionDeadlineInfoList = institutionDeadlineInfoMapper2.getWcInstitutionDeadlineInfoList(ipage, data.getSchoolName(), data.getFkCountryId());
        page.setAll((int) ipage.getTotal());
        page.setCurrentResult((int) ipage.getSize());

        if (GeneralTool.isEmpty(wcInstitutionDeadlineInfoList)) {
            return wcInstitutionDeadlineInfoList;
        }
        Iterator<InstitutionDeadlineInfoVo2> iterator = wcInstitutionDeadlineInfoList.iterator();
        while (iterator.hasNext()) {
            InstitutionDeadlineInfoVo2 next = iterator.next();
            boolean b = !next.getPublicLevel().contains("0");
            if (!b) {
                iterator.remove();
            }
        }
        if (GeneralTool.isEmpty(wcInstitutionDeadlineInfoList)) {
            return wcInstitutionDeadlineInfoList;
        }
        //TODO 改过
        //Set<Long> institutionIds = wcInstitutionDeadlineInfoList.stream().map(InstitutionDeadlineInfo2::getFkInstitutionId).collect(Collectors.toSet());
        Set<Long> institutionIds = wcInstitutionDeadlineInfoList.stream().map(InstitutionDeadlineInfoVo2::getFkInstitutionId).collect(Collectors.toSet());
        Map<Long, Institution> map = institutionService.getInstitutionByIds(institutionIds);

        //获取文件guid
        List<MediaAndAttachedVo> guIdByIds = mediaAndAttachedService.getGuIdByIds(institutionIds, "m_institution", "institution_cover");
        Map<Long, String> url = new HashMap<>();
        if (GeneralTool.isNotEmpty(guIdByIds)) {
            //获取文件具体url
            List<String> collect = guIdByIds.stream().map(MediaAndAttachedVo::getFkFileGuid).collect(Collectors.toList());
            Result<Map<String, String>> filePathByGuids = iFileCenterClient.getFilePathByGuids(collect);
            if (filePathByGuids.isSuccess() && GeneralTool.isNotEmpty(filePathByGuids.getData())) {
                guIdByIds.stream().forEach(d -> url.put(d.getFkTableId(), filePathByGuids.getData().get(d.getFkFileGuid())));
            }
        }

        wcInstitutionDeadlineInfoList.get(0).setNewGmtCreate(institutionDeadlineInfoMapper2.getNewCreateTime());
        for (InstitutionDeadlineInfoVo2 institutionDeadlineInfoDto : wcInstitutionDeadlineInfoList) {
            Optional.ofNullable(url.get(institutionDeadlineInfoDto.getFkInstitutionId())).ifPresent(dd -> institutionDeadlineInfoDto.setCoversUrl(dd));
            Institution institution = map.get(institutionDeadlineInfoDto.getFkInstitutionId());
            if (GeneralTool.isBlank(institution.getNameDisplay())) {
                institutionDeadlineInfoDto.setFkInstitutionNameEn(institution.getName());
                if (GeneralTool.isNotBlank(institution.getNameChn())) {
                    institutionDeadlineInfoDto.setFkInstitutionNameZh(institution.getNameChn());
                }
            } else {
                institutionDeadlineInfoDto.setFkInstitutionNameEn(institution.getNameDisplay());
            }
        }

        return wcInstitutionDeadlineInfoList;
    }

}
