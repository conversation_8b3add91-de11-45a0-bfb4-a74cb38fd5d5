package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/11/21 16:54
 */
@Data
public class StaffEmailVo extends BaseVoEntity {
    /**
     * 员工Id
     */
    @NotNull(message = "员工id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;

    /**
     * Email
     */
    @NotBlank(message = "员工id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "Email")
    private String email;


}
