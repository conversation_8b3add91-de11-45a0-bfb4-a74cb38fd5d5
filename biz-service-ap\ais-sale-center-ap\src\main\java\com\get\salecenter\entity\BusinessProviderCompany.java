package com.get.salecenter.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import lombok.Data;

/**
 * r_business_provider_company
 * <AUTHOR>
@Data
@TableName("r_business_provider_company")
public class BusinessProviderCompany extends BaseEntity implements Serializable {
    /**
     * 业务提供商及公司关系Id
     */
    private Long id;

    /**
     * 业务提供商Id
     */
    private Long fkBusinessProviderId;

    /**
     * 公司Id
     */
    private Long fkCompanyId;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 创建用户(登录账号)
     */
    private String gmtCreateUser;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 修改用户(登录账号)
     */
    private String gmtModifiedUser;

    private static final long serialVersionUID = 1L;
}