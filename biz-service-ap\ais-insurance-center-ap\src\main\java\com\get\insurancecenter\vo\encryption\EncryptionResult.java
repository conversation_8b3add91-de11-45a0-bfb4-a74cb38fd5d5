package com.get.insurancecenter.vo.encryption;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author:<PERSON>
 * @Date: 2025/7/31
 * @Version 1.0
 * @apiNote:
 */
@Data
public class EncryptionResult {

    @ApiModelProperty(value = "状态码")
    private Integer code;

    @ApiModelProperty(value = "请求结果")
    private Boolean success;

    @ApiModelProperty(value = "信息")
    private String message;

    @ApiModelProperty(value = "加密密钥")
    private String key;
}
