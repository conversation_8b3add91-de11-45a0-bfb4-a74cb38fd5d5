package com.get.platformconfigcenter.dao.appmso;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.AgentInfo;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("appmsodb")
public interface AgentInfoMapper extends BaseMapper<AgentInfo> {

//    int insert(AgentInfo record);
//
//    int insertSelective(AgentInfo record);
//
//    int updateByPrimaryKeySelective(AgentInfo record);
//
//    int updateByPrimaryKeyWithBLOBs(AgentInfo record);
//
//    int updateByPrimaryKey(AgentInfo record);
}