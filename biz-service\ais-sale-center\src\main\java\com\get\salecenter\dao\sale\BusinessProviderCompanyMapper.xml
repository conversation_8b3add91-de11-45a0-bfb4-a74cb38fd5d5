<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.BusinessProviderCompanyMapper">
  <resultMap id="BaseResultMap" type="com.get.salecenter.entity.BusinessProviderCompany">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_business_provider_id" jdbcType="BIGINT" property="fkBusinessProviderId" />
    <result column="fk_company_id" jdbcType="BIGINT" property="fkCompanyId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>

</mapper>