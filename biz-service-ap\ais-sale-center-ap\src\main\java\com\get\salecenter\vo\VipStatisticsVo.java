package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2022/11/11
 * @TIME: 15:01
 * @Description:VIP统计DTO
 **/
@Data
public class VipStatisticsVo {
    @ApiModelProperty(value = "vip配置ID")
    private Long vipConfigId;

    @ApiModelProperty(value = "统计类型")
    private String statisticsType;

    @ApiModelProperty(value = "统计项名称")
    private String statisticsName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "新建学生")
    private BigDecimal createCount;

    @ApiModelProperty(value = "处理申请（含加申计划）")
    private BigDecimal applicationCount;

    @ApiModelProperty(value = "定校量（按申请学校）")
    private BigDecimal confirmationCount;

    @ApiModelProperty(value = "成功入学量（按申请学校）")
    private BigDecimal successCount;

    @ApiModelProperty(value = "定校量（按学生）")
    private BigDecimal confirmationCountByStudent;

    @ApiModelProperty(value = "成功入学量（按学生）")
    private BigDecimal successCountByStudent;
}
