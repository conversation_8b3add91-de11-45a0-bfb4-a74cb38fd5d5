package com.get.platformconfigcenter.service;

import com.get.common.result.Page;
import com.get.platformconfigcenter.vo.StudentInfoVo;
import com.get.platformconfigcenter.dto.StudentInfoDto;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON>.
 * Time: 17:20
 * Date: 2021/7/9
 * Description:学生信息业务类
 */
public interface StudentInfoService {

    /**
     * 学生信息管理列表数据
     *
     * @param studentInfoDto
     * @param page
     * @return
     */
    List<StudentInfoVo> getStudentInfoList(StudentInfoDto studentInfoDto, Page page);


    /**
     * 更新学生信息状态
     *
     * @param studentInfoDto
     * @return
     */
    void updateStudentInfoStatus(StudentInfoDto studentInfoDto);

    /**
     * 学生信息管理详情
     *
     * @param id
     * @return
     * @
     */
    StudentInfoVo findStudentInfoById(Long id);


    /**
     * 代理下拉框数据
     *
     * @return
     */
    List<Map<String, Object>> agentSelectDatas();
}
