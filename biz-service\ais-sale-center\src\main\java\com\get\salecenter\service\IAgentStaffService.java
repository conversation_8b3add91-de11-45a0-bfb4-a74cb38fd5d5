package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.dto.AgentStaffDto;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.entity.AgentStaff;
import com.get.salecenter.vo.AgentAndAgentLabelVo;
import com.get.salecenter.vo.AgentStaffVo;
import com.get.salecenter.vo.AreaRegionVo;
import com.get.salecenter.vo.StaffBdCodeVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/10/16 15:15
 * @verison: 1.0
 * @description:
 */
public interface IAgentStaffService extends IService<AgentStaff> {

    /**
     * @return java.lang.Long
     * @Description: 新增记录
     * @Param [agentStaffVo]
     * <AUTHOR>
     */
    Long addAgentStaff(AgentStaffVo agentStaffVo);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过BD编号 模糊查询对应代理ids
     * @Param [bdNum]
     * <AUTHOR>
     */
    List<Long> getAgentIdsByBdNum(String bdNum);

    /**
     * 通过BD绑定大区ID查询对应代理
     *
     * @param fkAreaRegionId
     * @return
     * @
     */
    List<Long> getAgentIdsByBdAreaRegionId(Long fkAreaRegionId);

    /**
     * @return com.get.salecenter.entity.AgentStaff
     * @Description :通过代理id 获取激活的代理员工BD编号对象
     * @Param [id]
     * <AUTHOR>
     */
    AgentStaff getAgentStaffByAgentId(Long id);


    /**
     * 通过代理ids 获取激活的代理员工BD编号对象
     *
     * @param ids
     * @return
     */
    Map<Long, AgentStaff> getAgentStaffByAgentIds(Set<Long> ids);

    /**
     * @return void
     * @Description :绑定BD
     * @Param [agentStaffDto]
     * <AUTHOR>
     */
    void setBd(AgentStaffDto agentStaffDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.AgentStaffVo>
     * @Description :列表
     * @Param [agentStaffDto, page]
     * <AUTHOR>
     */
    List<AgentStaffVo> getAgentStaffs(AgentStaffDto agentStaffDto, Page page);

    /**
     * @return com.get.salecenter.vo.AgentStaffVo
     * @Description: 修改
     * @Param [agentStaffDto]
     * <AUTHOR>
     */
    AgentStaffVo updateAgentStaff(AgentStaffDto agentStaffDto);

    /**
     * @return java.util.List<com.get.salecenter.entity.StaffBdCode>
     * @Description :绑定BD下拉框数据
     * @Param [companyId]
     * <AUTHOR>
     */
    List<StaffBdCodeVo> getStaffBdCodeList(Long companyId, Boolean testBdFlag, Boolean subordinateFlag);


    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 获取代理下拉
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getAgentSelect();


    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 获取BD下拉
     * @Param []
     * <AUTHOR>
     */
    List<StaffBdCodeVo> getBdSelect(Long agentId);


    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 获取对应的代理ids
     * @Param [staffIds]
     * <AUTHOR>
     */
    List<Long> getAgentIdByStaffIds(List<Long> staffIds);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 获取对应的代理ids(生效的)
     * @Param [staffIds]
     * <AUTHOR>
     */
    List<Long> getAgentIdByStaffIdsIsActive(List<Long> staffIds);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 根据学生id下拉
     * @Param [studentId]
     * <AUTHOR>
     */
    List<BaseSelectEntity> getAgentByStudentId(Long studentId);

    /**
     * 获取学生已绑定申请方案的代理，业务国家过滤
     *
     * @param studentId
     * @return
     */
    List<AgentAndAgentLabelVo> getAgentList(Long studentId);

    /**
     * @return java.lang.Integer
     * @Description :查看该bd绑定的代理数
     * @Param [staffId]
     * <AUTHOR>
     */
    List<Agent> getBdCount(Long staffId);

    /**
     * @ Description :员工跟进的全部代理
     * @ Param [fkStaffId]
     * @ return java.util.List<java.lang.Long>
     * @ author LEO
     */
    List<Long> getAgentsByStaffId(Long fkStaffId);

    /**
     * 批量修改BD绑定
     *
     * @param agentStaffDto
     */
    void updateAgentStaffBinding(AgentStaffDto agentStaffDto);

    /**
     * 根据代理ids获取bd名称
     *
     * @param ids
     * @return
     */
    Map<Long, String> getBdNameByAgentIds(Set<Long> ids);

    /**
     * 有绑定代理并且为业务下属的 BD下拉框数据
     *
     * @Date 12:14 2023/3/16
     * <AUTHOR>
     */
    List<StaffBdCodeVo> getExistAgentStaffBdCodeList(Long companyId);

    /**
     * 根据代理id获取bd的id
     *
     * @param agentIds
     * @return
     */
    Map<Long, List<Long>> getBdIdByAgentIds(Set<Long> agentIds);

    /**
     * 根据代理ids获取bd的区域dto集合
     *
     * @param agentIds 代理ids
     * @return
     */
    Map<Long, List<AreaRegionVo>> getAreaRegionDtosByAgentIds(Set<Long> agentIds);

    /**
     * BD下拉框数据 有权限bd
     *
     * @Date 14:53 2024/7/18
     * <AUTHOR>
     */
    List<StaffBdCodeVo> getStaffBdCodeListByPermission(List<Long> companyIds);

    /**
     * 根据代理id获取激活的BD
     */
    AgentStaff getActiveAgentStaffByAgentId(Long agentId);


}
