package com.get.resumecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import com.get.resumecenter.entity.ResumeCertificate;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2020/8/10
 * @TIME: 17:22
 * @Description:
 **/
@Data
public class ResumeCertificateVo  extends BaseEntity {
    /**
     * 所属简历Id
     */
    @ApiModelProperty(value = "所属简历Id")
    @Column(name = "fk_resume_id")
    private Long fkResumeId;
    /**
     * 获得时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "获得时间")
    @Column(name = "get_date")
    private Date getDate;
    /**
     * 证书名称
     */
    @ApiModelProperty(value = "证书名称")
    @Column(name = "certificate")
    private String certificate;
    /**
     * 成绩
     */
    @ApiModelProperty(value = "成绩")
    @Column(name = "score")
    private String score;
}
