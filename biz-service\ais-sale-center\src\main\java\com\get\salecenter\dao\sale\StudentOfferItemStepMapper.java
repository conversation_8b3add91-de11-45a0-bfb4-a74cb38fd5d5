package com.get.salecenter.dao.sale;

import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.entity.StudentOfferItemStep;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface StudentOfferItemStepMapper extends GetMapper<StudentOfferItemStep> {

    /**
     * @return java.lang.Integer
     * @Description :获取步骤排序最新值,用于步骤管理批量新增
     * @Param []
     * <AUTHOR>
     */
    Integer getMaxStepOrder();

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description:步骤前置下拉，用于步骤管理前置步骤状态下拉
     * @Param [itemStepId]
     * <AUTHOR>
     */
    List<BaseSelectEntity> getItemStepPrecondition(@Param("itemStepId") Long itemStepId);


    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 下拉
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getItemStepSelect();

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description:学生申请方案项目未执行步骤
     * @Param [stepOrder]
     * <AUTHOR>
     */
    List<StudentOfferItemStep> getUnexecutedItemStep(@Param("itemStepId") Long itemStepId);

    /**
     * 学生申请方案项目已执行步骤
     *
     * @param itemStepId
     * @return
     */
    List<StudentOfferItemStep> getExecutedItemStep(@Param("itemStepId") Long itemStepId);


    /**
     * @Description:根据学习计划申请状态查询学生id
     * @Param
     * @Date 11:12 2021/5/17
     * <AUTHOR>
     */
    List<Long> getStudentIdByState(@Param("states") List<Long> states);

    /**
     * 获取步骤id和步骤名
     *
     * @Date 11:59 2021/7/8
     * <AUTHOR>
     */
    List<Map<String, Object>> getItemStepMap();

    /**
     * 获取步骤id和步骤名
     *
     * @Date 11:59 2021/7/8
     * <AUTHOR>
     */
    List<Map<String, Object>> getItemStepMapAndId();

    /**
     * 根据步骤order列表获取对应id列表
     *
     * @param orders
     * @return
     */
    List<Long> getStepIdByOrder(@Param("orders") List<Long> orders);

    List<BaseSelectEntity> getSuccessfulCustomerStepSelect(@Param("list") List<String> list);

    List<StudentOfferItemStep> getUnexecutedItemStepWithCountryConfig(@Param("itemStepId") Long itemStepId, @Param("fkStudentOfferItemId")Long fkStudentOfferItemId);

    List<StudentOfferItemStep> getItemStepPostpositionByStepId(@Param("itemStepId")Long itemStepId);

}