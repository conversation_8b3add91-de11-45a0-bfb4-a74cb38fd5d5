package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.EventPlanThemeOnlineVo;
import com.get.salecenter.service.EventPlanThemeOnlineService;
import com.get.salecenter.dto.EventPlanThemeOnlineDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 *  前端接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */

@Api(tags = "活动年度计划线上活动类型项目管理")
@RestController
@RequestMapping("sale/eventPlanThemeOnline")
@VerifyPermission(IsVerify = false)
public class EventPlanThemeOnlineController {

    @Resource
    private EventPlanThemeOnlineService eventPlanThemeOnlineService;

    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动年度计划线上活动类型项目管理/列表数据")
    @PostMapping("datas")
    public ResponseBo<EventPlanThemeOnlineVo> datas(@RequestParam("fkEventPlanId") Long fkEventPlanId){
        return new ListResponseBo(eventPlanThemeOnlineService.getEventPlanThemeOnlines(fkEventPlanId));
    }

    @ApiOperation(value = "根据主题获取线上活动列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动年度计划线上活动类型项目管理/根据主题获取线上活动列表数据")
    @PostMapping("getOnlinesByThemeId")
    public ResponseBo<EventPlanThemeOnlineVo> getOnlinesByThemeId(@RequestParam("fkEventPlanThemeId") Long fkEventPlanThemeId){
        return new ListResponseBo(eventPlanThemeOnlineService.getOnlinesByThemeId(fkEventPlanThemeId));
    }

    @ApiOperation(value = "激活", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/活动年度计划线上活动类型项目管理/激活")
    @PostMapping("activate")
    public ResponseBo activate(@RequestBody @Validated(EventPlanThemeOnlineDto.Update.class)  EventPlanThemeOnlineDto onlineVo) {
        eventPlanThemeOnlineService.activate(onlineVo);
        return SaveResponseBo.ok();
    }

    @ApiOperation(value = "批量新增/批量修改", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/活动年度计划线上活动类型项目管理/批量新增或修改")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(EventPlanThemeOnlineDto.Add.class) ValidList<EventPlanThemeOnlineDto> onlineVos) {
        eventPlanThemeOnlineService.batchAdd(onlineVos);
        return SaveResponseBo.ok();
    }

    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/活动年度计划线上活动类型项目管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        eventPlanThemeOnlineService.delete(id);
        return DeleteResponseBo.ok();
    }

    @ApiOperation(value = "拖拽", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动年度计划线上活动类型项目管理/拖拽")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestParam("fkEventPlanThemeId")Long fkEventPlanThemeId,@RequestParam("start")Integer start,@RequestParam("end")Integer end) {
        eventPlanThemeOnlineService.movingOrder(fkEventPlanThemeId,start,end);
        return ResponseBo.ok();
    }

}
