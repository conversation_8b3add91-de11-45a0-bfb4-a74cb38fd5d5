package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.vo.NewsTypeVo;
import com.get.institutioncenter.service.INewsTypeService;
import com.get.institutioncenter.dto.NewsTypeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/6 11:45
 * @verison: 1.0
 * @description:
 */
@Api(tags = "新闻类型管理")
@RestController
@RequestMapping("/institution/newsType")
public class NewsTypeController {

    @Resource
    private INewsTypeService newsTypeService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/新闻类型管理/新闻类型详情")
    @GetMapping("/{id}")
    public ResponseBo<NewsTypeVo> detail(@PathVariable("id") Long id) {
        NewsTypeVo data = newsTypeService.findNewsTypeById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 批量新增信息
     *
     * @param newsTypeDtos
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/新闻类型管理/新增新闻类型")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(NewsTypeDto.Add.class) ValidList<NewsTypeDto> newsTypeDtos) {
        newsTypeService.batchAdd(newsTypeDtos);
        return SaveResponseBo.ok();
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/新闻类型管理/删除新闻类型")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        newsTypeService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param newsTypeDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/新闻类型管理/更新新闻类型")
    @PostMapping("update")
    public ResponseBo<NewsTypeVo> update(@RequestBody @Validated(NewsTypeDto.Update.class) NewsTypeDto newsTypeDto) {
        return UpdateResponseBo.ok(newsTypeService.updateNewsType(newsTypeDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为搜索关键字(typeName类型名称)")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/新闻类型管理/查询新闻类型")
    @PostMapping("datas")
    public ResponseBo<NewsTypeVo> datas(@RequestBody SearchBean<NewsTypeDto> page) {
        List<NewsTypeVo> datas = newsTypeService.getNewsTypes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 上移下移
     *
     * @param newsTypeDtos
     * @return
     * @
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/新闻类型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<NewsTypeDto> newsTypeDtos) {
        newsTypeService.movingOrder(newsTypeDtos);
        return ResponseBo.ok();
    }

    /**
     * 新闻类型下拉框
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "新闻类型下拉框", notes = "")
    @GetMapping("getNewsTypeList")
    public ResponseBo<BaseSelectEntity> getNewsTypeList() {
        return new ListResponseBo<>(newsTypeService.getNewsTypeList());
    }
}
