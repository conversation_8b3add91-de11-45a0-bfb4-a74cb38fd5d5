package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.InstitutionMapper;
import com.get.institutioncenter.dao.InstitutionViewOrderMapper;
import com.get.institutioncenter.vo.InstitutionVo;
import com.get.institutioncenter.vo.InstitutionViewOrderVo;
import com.get.institutioncenter.entity.InstitutionViewOrder;
import com.get.institutioncenter.service.IInstitutionService;
import com.get.institutioncenter.service.IInstitutionViewOrderService;
import com.get.institutioncenter.dto.InstitutionViewOrderDto;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/6/8 17:21
 */
@Service
public class InstitutionViewOrderServiceImpl extends BaseServiceImpl<InstitutionViewOrderMapper, InstitutionViewOrder> implements IInstitutionViewOrderService {
    @Resource
    private InstitutionViewOrderMapper institutionViewOrderMapper;
    @Resource
    private InstitutionMapper institutionMapper;
    @Resource
    @Lazy
    private IInstitutionService institutionService;
    @Resource
    private UtilService utilService;

    @Override
    public void movingOrder(List<InstitutionViewOrderDto> institutionViewOrderDtos) {
        if (GeneralTool.isEmpty(institutionViewOrderDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        InstitutionViewOrder otherType = BeanCopyUtils.objClone(institutionViewOrderDtos.get(0), InstitutionViewOrder::new);
        InstitutionViewOrder otherType2 = BeanCopyUtils.objClone(institutionViewOrderDtos.get(1), InstitutionViewOrder::new);

        Integer viewOrder = otherType.getViewOrder();
        otherType.setViewOrder(otherType2.getViewOrder());
        otherType2.setViewOrder(viewOrder);

        institutionViewOrderMapper.updateById(otherType);
        institutionViewOrderMapper.updateById(otherType2);

    }

    @Override
    public void delete(Long id) {
        Optional.ofNullable(id).orElseThrow(() -> new GetServiceException(LocaleMessageUtils.getMessage("search_result_null")));
        institutionViewOrderMapper.deleteById(id);

        LambdaQueryWrapper<InstitutionViewOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InstitutionViewOrder::getType, 0).orderByAsc(InstitutionViewOrder::getViewOrder);
        List<InstitutionViewOrder> institutionViewOrders = institutionViewOrderMapper.selectList(lambdaQueryWrapper);

        //重新排序
        for (int i = 0; i < institutionViewOrders.size(); i++) {
            InstitutionViewOrder institutionViewOrder = institutionViewOrders.get(i);
            institutionViewOrder.setViewOrder(i);
        }
        this.updateBatchById(institutionViewOrders);



    }

    @Override
    public List<InstitutionViewOrderVo> datas(InstitutionViewOrderDto data, SearchBean<InstitutionViewOrderDto> page) {

        List<Long> institutions = null;
        if (GeneralTool.isNotBlank(data.getInstitutionName()) || GeneralTool.isNotEmpty(data.getFkCountryId())) {
            institutions = institutionMapper.getInstitutionsByNameEnOrNameZh2(data.getInstitutionName(),
                    data.getFkCountryId());
            //学校检索条件空就直接返回空当做找不到。
            if (GeneralTool.isEmpty(institutions)) {
                return null;
            }
        }
        LambdaQueryWrapper<InstitutionViewOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(GeneralTool.isNotEmpty(institutions), InstitutionViewOrder::getFkInstitutionId, institutions);
        lambdaQueryWrapper.eq(InstitutionViewOrder::getType, data.getType());
        lambdaQueryWrapper.orderByAsc(InstitutionViewOrder::getViewOrder);
        IPage<InstitutionViewOrder> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<InstitutionViewOrder> pagesRecords = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<InstitutionViewOrderVo> institutionViewOrderVos = BeanCopyUtils.copyListProperties(pagesRecords, InstitutionViewOrderVo::new);
        //TODO 改过
        //Set<Long> institutionIds = institutionViewOrderVos.stream().map(InstitutionViewOrder::getFkInstitutionId).collect(Collectors.toSet());
        Set<Long> institutionIds = institutionViewOrderVos.stream().map(InstitutionViewOrderVo::getFkInstitutionId).collect(Collectors.toSet());


        Map<Long, String> institutionNamesByIds = Optional.ofNullable(institutionService.getInstitutionNamesByIds(institutionIds)).orElse(new HashMap<>());

        institutionViewOrderVos.stream().forEach(d -> {
            d.setInstitutionName(institutionNamesByIds.get(d.getFkInstitutionId()));
            d.setTypeName(ProjectExtraEnum.getValueByKey(d.getType(), ProjectExtraEnum.InstitutionType));
        });
        return institutionViewOrderVos;
    }

    @Override
    public void adds(List<InstitutionViewOrderDto> data) {

        LambdaQueryWrapper<InstitutionViewOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InstitutionViewOrder::getType, data.get(0).getType());
        lambdaQueryWrapper.select(InstitutionViewOrder::getViewOrder).orderByDesc(InstitutionViewOrder::getViewOrder).last("limit 1");
        InstitutionViewOrder institutionViewOrder = institutionViewOrderMapper.selectOne(lambdaQueryWrapper);
        int temp = 0;
        if (GeneralTool.isNotEmpty(institutionViewOrder)) {
            temp = institutionViewOrder.getViewOrder();
        }
        LambdaQueryWrapper<InstitutionViewOrder> lists = new LambdaQueryWrapper<>();
        lists.eq(InstitutionViewOrder::getType, data.get(0).getType());
        List<InstitutionViewOrder> institutionViewOrders1 = institutionViewOrderMapper.selectList(lists);
        Set<Long> collect = institutionViewOrders1.stream().map(InstitutionViewOrder::getFkInstitutionId).collect(Collectors.toSet());
        Iterator<InstitutionViewOrderDto> iterator = data.iterator();
        while (iterator.hasNext()) {
            InstitutionViewOrderDto next = iterator.next();
            for (Long aLong : collect) {
                if (aLong.equals(next.getFkInstitutionId())) {
                    iterator.remove();
                    break;
                }
            }
        }

        List<InstitutionViewOrder> institutionViewOrders = BeanCopyUtils.copyListProperties(data, InstitutionViewOrder::new);
        for (InstitutionViewOrder viewOrder : institutionViewOrders) {
            utilService.setCreateInfo(viewOrder);
            viewOrder.setViewOrder(temp + 1);
            temp = viewOrder.getViewOrder();
        }

        this.saveBatch(institutionViewOrders);
    }

    @Override
    public List<InstitutionVo> searchInstitution(String fkInstitutionName) {
        List<InstitutionVo> institutionsObjByNameEnOrNameZh = institutionMapper.getInstitutionsObjByNameEnOrNameZh(fkInstitutionName);
        List<Long> ids = institutionViewOrderMapper.selectList(null).stream().filter(Objects::nonNull).map(InstitutionViewOrder::getFkInstitutionId).collect(Collectors.toList());
        List<InstitutionVo> collect = institutionsObjByNameEnOrNameZh.stream().filter(Objects::nonNull).filter(d -> !ids.contains(d.getId())).collect(Collectors.toList());
        return collect;
    }

    @Override
    public void movingOrderSelect(int end, int start) {

        LambdaQueryWrapper<InstitutionViewOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InstitutionViewOrder::getType, 0).orderByAsc(InstitutionViewOrder::getViewOrder);
        List<InstitutionViewOrder> institutionViewOrders = institutionViewOrderMapper.selectList(lambdaQueryWrapper);
        //将数据放到目标数据下面。
        if (start < end) {
            end++;
        }

        institutionViewOrders.add(end, institutionViewOrders.get(start));
        if (start > end) {
            institutionViewOrders.remove(start + 1);
        } else {
            institutionViewOrders.remove(start);
        }
        for (int i = 0; i < institutionViewOrders.size(); i++) {
            InstitutionViewOrder institutionViewOrder = institutionViewOrders.get(i);
            institutionViewOrder.setViewOrder(i);
        }
        this.updateBatchById(institutionViewOrders);
    }

}
