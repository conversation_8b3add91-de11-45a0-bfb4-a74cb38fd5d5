package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.dto.InstitutionCoursePathwayDto;
import com.get.institutioncenter.vo.InstitutionCoursePathwayVo;
import com.get.institutioncenter.entity.InstitutionCoursePathway;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/12/18
 * @TIME: 16:15
 * @Description:
 **/
public interface IInstitutionCoursePathwayService extends BaseService<InstitutionCoursePathway> {
    /**
     * @return java.util.List<CommentVo>
     * @Description: 获取所有评论
     * @Param [commentVo, page]
     * <AUTHOR>
     **/
    List<InstitutionCoursePathwayVo> datas(InstitutionCoursePathwayDto institutionCoursePathwayDto, Page page);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    InstitutionCoursePathwayVo findInstitutionCoursePathwayById(Long id);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * @return void
     * @Description: 绑定
     * @Param [institutionPathwayVos]
     * <AUTHOR>
     **/
    void update(InstitutionCoursePathwayDto institutionCoursePathwayDto);

    /**
     * @return void
     * @Description: 编辑绑定的信息
     * @Param [institutionPathwayVos]
     * <AUTHOR>
     **/
    void updateInstitutionCoursePathway(InstitutionCoursePathwayDto institutionCoursePathwayDto);
    /**
     * 反向绑定非桥梁课程
     *
     * @Date 17:55 2021/7/22
     * <AUTHOR>
     */
//    void pathwayUpdate(InstitutionCoursePathwayDto institutionCoursePathwayVo) ;

    /**
     * 删除记录
     *
     * @return
     */
    void deleteCoursePathwayByCourseId(Long id);

    /**
     * 删除记录
     *
     * @return
     */
    void deleteCoursePathwayByCoursePathwayId(Long id);
}
