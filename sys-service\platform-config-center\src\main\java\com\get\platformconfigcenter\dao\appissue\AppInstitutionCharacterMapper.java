package com.get.platformconfigcenter.dao.appissue;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.AppInstitutionCharacter;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
@DS("issuedb")
public interface AppInstitutionCharacterMapper extends BaseMapper<AppInstitutionCharacter> {

//    int insert(AppInstitutionCharacter record);
//
//    int insertSelective(AppInstitutionCharacter record);
//
//    int updateByPrimaryKeySelective(AppInstitutionCharacter record);
//
//    int updateByPrimaryKey(AppInstitutionCharacter record);
//
//    /**
//     * 根据课程id是否存在
//     *
//     * @return
//     */
//    boolean isExistByCourseId(@Param("courseId") Long courseId);
}