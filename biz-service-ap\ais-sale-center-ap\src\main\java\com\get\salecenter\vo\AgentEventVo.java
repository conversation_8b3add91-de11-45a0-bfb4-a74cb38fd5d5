package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.AgentEvent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2020/9/11
 * @TIME: 17:06
 * @Description:
 **/
@Data
public class AgentEventVo extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "类型名称")
    private String typeName;

    //===========实体类AgentEvent==============
    private static final long serialVersionUID = 1L;
    /**
     * 学生代理Id
     */
    @ApiModelProperty(value = "学生代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 学生代理事件类型Id
     */
    @ApiModelProperty(value = "学生代理事件类型Id")
    @Column(name = "fk_agent_event_type_id")
    private Long fkAgentEventTypeId;
    /**
     * 事件内容
     */
    @ApiModelProperty(value = "事件内容")
    @Column(name = "description")
    private String description;
}
