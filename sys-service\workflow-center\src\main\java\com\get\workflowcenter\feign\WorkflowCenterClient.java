package com.get.workflowcenter.feign;

import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.officecenter.entity.LeaveApplicationForm;
import com.get.workflowcenter.vo.ActHiTaskInstVo;
import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.vo.HiCommentFeignVo;
import com.get.workflowcenter.service.IAgentContractService;
import com.get.workflowcenter.service.IStudentOfferWorkFlowService;
import com.get.workflowcenter.service.IWorkFlowInstitutionProviderService;
import com.get.workflowcenter.service.IWorkFlowService;
import com.get.workflowcenter.service.ImBorrowMoneyServiec;
import com.get.workflowcenter.service.MpayService;
import com.get.workflowcenter.dto.ActReProcdefDto;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Set;

@RestController
@AllArgsConstructor
@VerifyPermission(IsVerify = false)
public class WorkflowCenterClient implements IWorkflowCenterClient {
    private final MpayService mpayService;
    private final IWorkFlowInstitutionProviderService institutionProviderService;
    private final IWorkFlowService workFlowService;
    private final IWorkFlowInstitutionProviderService iInstitutionProviderService;
    private final IStudentOfferWorkFlowService studentOfferWorkFlowService;
    private final IAgentContractService agentContractService;
    private final ImBorrowMoneyServiec imBorrowMoneyServiec;

    @Override
    public Result<List<Long>> getPersonalHistoryTasks(String procdkey) {
        return Result.data(mpayService.getPersonalHistoryTasks(procdkey));
    }

    @Override
    public Result<ActRuTaskVo> getContractTaskDataByBusinessKey(String businessKey, String procdefKey) {
        return Result.data(institutionProviderService.getTaskDataByBusinessKey(businessKey, procdefKey));
    }

    @Override
    public Result<Integer> getSignOrGet(String taskId, Integer version) {
        return Result.data(institutionProviderService.getSignOrGet(taskId, version));
    }

    @Override
    public Result<Boolean> startContractFlow(String businessKey, String procdefKey, String companyId) {
        return Result.data(agentContractService.startContractFlow(businessKey, procdefKey, companyId));
    }

    @Override
    public Result<Boolean> startPayFlow(String businessKey, String procdefKey, String companyId) {
        return Result.data(mpayService.startPayFlow(businessKey, procdefKey, companyId));
    }

    @Override
    public Result<Boolean> startBorrowFlow(String businessKey, String procdefKey, String companyId) {
        return Result.data(imBorrowMoneyServiec.startBorrowFlow(businessKey, procdefKey, companyId));
    }

    @Override
    public Result<Map<String, String>> getCurrencyNamesByNums(Set<String> fkCurrencyTypeNums) {
        return null;
    }

    @Override
    public Result<Map<Long, Integer>> getFromIdsByStaffId(Long staffId, String key) {
        return Result.data(workFlowService.getFromIdsByStaffId(staffId, key));
    }

    @Override
    public Result<Boolean> getContractUserSubmit(String taskId, String status) {
        return Result.data(iInstitutionProviderService.getContractUserSubmit(taskId, status));
    }

    @Override
//    public Result<Map<Long, ActRuTaskVo>> getActRuTaskDtosByBusinessKey(List<Long> businessIds, String procdefKey) {
    public Result<Map<Long, ActRuTaskVo>> getActRuTaskDtosByBusinessKey(Map<String, List<Long>> businessIdsWithProcdefKey) {
        String procdefKey = businessIdsWithProcdefKey.entrySet().iterator().next().getKey();
        List<Long> businessIds = businessIdsWithProcdefKey.get(procdefKey);
        return Result.data(workFlowService.getActRuTaskDtosByBusinessKey(businessIds, procdefKey));
    }

    @Override
    public Result<Boolean> startProcess(String businessKey, String procdefKey, String companyId, Map<String, Object> map) {
        return Result.data(workFlowService.startProcess(businessKey, procdefKey, companyId, map));
    }

    @Override
    public Result<Boolean> startInstitutionContractFlow(String businessKey, String procdefKey, String companyId) {
        return Result.data(iInstitutionProviderService.startInstitutionContractFlow(businessKey, procdefKey, companyId));
    }

    @Override
    public Result<Boolean> startStudentOfferFlow(String businessKey, String procdefKey, String companyId, String buttonType, String submitReason, Long fkCancelOfferReasonId) {
        return Result.data(studentOfferWorkFlowService.startStudentOfferFlow(businessKey, procdefKey, companyId, buttonType,submitReason,fkCancelOfferReasonId));
    }

    @Override
    public Result<Object> getVariableByHisInstanceAndName(String processInstanceId, String name) {
        return Result.data(workFlowService.getVariableByHisInstanceAndName(processInstanceId, name));
    }

    @Override
    public Result<Boolean> getUserSubmit(String taskId, String status) {
        return Result.data(workFlowService.getUserSubmit(taskId, status));
    }

    @Override
    public Result<ActRuTaskVo> getTaskDataByBusinessKey(String businessKey, String key) {
        return Result.data(mpayService.getTaskDataByBusinessKey(businessKey, key));
    }

    @Override
    public Result<ActRuTaskVo> getPrepayTaskDataByBusinessKey(String businessKey, String key) {
        return Result.data(imBorrowMoneyServiec.getTaskDataByBusinessKey(businessKey, key));
    }

    @Override
    public Result<HiCommentFeignVo> getHiComment(Long businessKey, String procdefKey) {
        return Result.data(workFlowService.getHiComment(businessKey, procdefKey));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<ActHiTaskInstVo> getActHiTaskInstDtoAndLeaveFormMessage(Long fkTableId) {
        return Result.data(workFlowService.getActHiTaskInstDtoAndLeaveFormMessage(fkTableId));
    }

    @Override
    public Result<List<ActHiTaskInstVo>> getActHiTaskInstDtosByBusinessKey(String businessKey, String procdefKey) {
        return Result.data(workFlowService.getActHiTaskInstDtosByBusinessKey(businessKey,procdefKey));
    }

    @Override
    public Result<Set<String>> getToDoByStaffIdAndTableName(Long staffId, String key) {
        return Result.data(workFlowService.getToDoByStaffIdAndTableName(staffId,key));
    }

    @Override
    public Result<Set<String>> getToSignByStaffIdAndTableName(Long staffId, String key) {
        return Result.data(workFlowService.getToSignByStaffIdAndTableName(staffId,key));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result doSendRemind(LeaveApplicationForm leaveApplicationForm, String title) {
        workFlowService.doSendRemind(leaveApplicationForm,title);
        return Result.success("请求成功！");
    }

    @Override
    public Result<ResponseBo<ActRuTaskVo>> getToDoMatter(SearchBean<ActReProcdefDto> page) {
        List<ActRuTaskVo> toSign = workFlowService.getToDoSelect(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        ListResponseBo<ActRuTaskVo> actRuTaskDtoListResponseBo = new ListResponseBo<>(toSign, p);
        return Result.data(actRuTaskDtoListResponseBo);
    }

    @Override
    public Result<ResponseBo<ActRuTaskVo>> getToSignedMatter(SearchBean<ActReProcdefDto> page) {
        List<ActRuTaskVo> toSign = workFlowService.getSignedSelect(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        ListResponseBo<ActRuTaskVo> actRuTaskDtoListResponseBo = new ListResponseBo<>(toSign, p);
        return Result.data(actRuTaskDtoListResponseBo);
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<ActHiTaskInstVo> getActHiTaskInstDtoAndStudentOffer(Long fkTableId) {
        return Result.data(workFlowService.getActHiTaskInstDtoAndStudentOffer(fkTableId));
    }

    @Override
    public Result<Boolean> stopStudentOfferWorkFlow(Long id) {
        return Result.data(workFlowService.stopStudentOfferWorkFlow(id));
    }

    @Override
    public Result<Long> getStartUserIdByIdAndProcdefKey(Long id, String procdefKey) {
        return Result.data(workFlowService.getStartUserIdByIdAndProcdefKey(id,procdefKey));
    }

    @Override
    public Result<Boolean> stopExecution(String processInstId, String msg, String procdefKey, String businessKey) {
        return Result.data(workFlowService.StopExecution(processInstId,msg,procdefKey,businessKey));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<Long> getAssigneeStaffId(String businessKey) {
        return Result.data(workFlowService.getAssigneeStaffId(businessKey));
    }

//    @Autowired
//    private final ICurrencyTypeService currencyTypeService;

//    @Override
//    public Result<String> getCurrencyNameByNum(String fkCurrencyTypeNum) {
//        return Result.data(currencyTypeService.getCurrencyNameByNum(fkCurrencyTypeNum));
//    }
//
//    @Override
//    public Result<Map<String, String>> getCurrencyNamesByNums(Set<String> fkCurrencyTypeNums) {
//        return Result.data(currencyTypeService.getCurrencyNamesByNums(fkCurrencyTypeNums));
//    }
}
