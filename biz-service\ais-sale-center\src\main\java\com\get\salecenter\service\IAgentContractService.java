package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.core.log.exception.GetServiceException;
import com.get.salecenter.dto.AgentContractCompanyDto;
import com.get.salecenter.dto.AgentContractDto;
import com.get.salecenter.dto.CommentDto;
import com.get.salecenter.dto.ContactPersonDto;
import com.get.salecenter.dto.CreateAgentContractDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.query.AgentContractQueryDto;
import com.get.salecenter.entity.AgentContract;
import com.get.salecenter.vo.AgentContractSelect;
import com.get.salecenter.vo.AgentContractTypeVo;
import com.get.salecenter.vo.AgentContractVo;
import com.get.salecenter.vo.CommentVo;
import com.get.salecenter.vo.CompanyTreeVo;
import com.get.salecenter.vo.ContactPersonVo;
import com.get.salecenter.vo.MediaAndAttachedVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/9/14
 * @TIME: 12:49
 * @Description:
 **/
public interface IAgentContractService extends IService<AgentContract> {

    /**
     * 查询所有的代理合同
     *
     * @param contractVo
     * @param page
     * @return
     */
    List<AgentContractVo> getAgentContractDtos(AgentContractQueryDto contractVo, Page page);


    /**
     * 代理合同续约
     * @param contractIds
     * @return
     */
    void renewal(List<Long> contractIds);

    /**
     * 新增合同
     *
     * @param contractVo
     * @return
     */
    Long addContract(AgentContractDto contractVo);


    /***
     * 修改
     * @param agentContractDto
     * @return
     */
    AgentContractVo updateAgentContract(AgentContractDto agentContractDto);

    /**
     * 查询详细
     *
     * @param id
     * @return
     */
    AgentContractVo findAgentContractById(Long id);


    /**
     * 生成代理合同文档
     * @param contractId
     * @param agentId
     * @param response
     */
    void createAgentContractDocx(Long contractId, Long agentId, Long contractVsion, Integer contractTemplateMode,HttpServletResponse response);

    /**
     * 根据代理合同ids查询ContractNum
     *
     * @param ids
     * @return
     */
    Map<Long, String> findContractNumByAgentContractIds(Set<Long> ids);

    /**
     * 删除
     *
     * @param id
     */
    void deleteAgentContract(Long id);

    /**
     * @return java.util.List<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 查询代理合同文件
     * @Param [attachedVo, page]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> getAgentContractMedia(MediaAndAttachedDto attachedVo, Page page);

    /**
     * @return com.get.salecenter.vo.MediaAndAttachedDto
     * @Description: 添加合同媒体附件
     * @Param
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> addAgentContractMedia(List<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * 安全配置(合同和公司)
     *
     * @param contractCompanyVo
     * @
     */
    void editAgentContractCompany(List<AgentContractCompanyDto> contractCompanyVo);


    /**
     * @return java.util.List<com.get.salecenter.vo.CompanyTreeVo>
     * @Description: 代理合同和公司的关系（数据回显）
     * @Param [contractId]
     * <AUTHOR>
     */
    List<CompanyTreeVo> getContractCompanyRelation(Long contractId);


    /**
     * @return java.util.List<com.get.salecenter.vo.AgentContractTypeVo>
     * @Description: 合同类型
     * @Param []
     * <AUTHOR>
     */
    List<AgentContractTypeVo> getContractType();


    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 合同下拉
     * @Param []
     * <AUTHOR>
     */
    List<AgentContractSelect> getAllAgentContract();

    Long addApprovedAgentContract(AgentContractDto contractVo);

    /**
     * 新增合同
     *
     * @param contractVo
     * @return
     */
    Long addAgentContract(AgentContractDto contractVo);

    /**
     * @return java.lang.Long
     * @Description: 增加或者更新评论
     * @Param [commentDto]
     * <AUTHOR>
     **/
    Long editComment(CommentDto commentDto);


    /**
     * @return java.util.List<com.get.salecenter.vo.CommentVo>
     * @Description: 获取所有评论
     * @Param [commentDto, page]
     * <AUTHOR>
     */
    List<CommentVo> getComments(CommentDto commentDto, Page page);

    /**
     * 获取代理合同联系人列表
     *
     * @param contactPersonVo
     * @param page
     * @return
     */
    List<ContactPersonVo> getContactPerson(ContactPersonDto contactPersonVo, Page page);

    /**
     * 新增代理合同联系人
     *
     * @param contactPersonVo
     * @return
     * @
     */
    Long addContactPerson(ContactPersonDto contactPersonVo);

    /**
     * @ Description :
     * @ Param [id]
     * @ return com.get.salecenter.vo.AgentContractVo
     * @ author LEO
     */
    AgentContractVo getAgentContractById(Long id);

    Boolean updateChangeStatus(AgentContract agentContract);


    Boolean startContractFlow(String businessKey, String procdefKey, String companyId) throws GetServiceException;

    void updateCancellationBusiness(Long id);

    void getUserSubmit(String taskId, String status);

    Long getStaffByAgentId(Long id);

    Boolean changeStatus(Integer status, String tableName, Long businessKey);

    void getRevokeAgentContract(Long id, String summary);

    void synchronizeCppAttactment();

    /**
     * feign 查询代理有合同信息且当前时间在有效期内，否则返回无效合同的代理名
     *
     * @Date 12:49 2022/7/13
     * <AUTHOR>
     */
    List<String> checkAgentContractByAgentIds(Set<Long> agentIdSet);

    void synchronizeCppAttactmentInfo();

    /**
     * 获取指定时间到期的代理合同
     *
     * @param date
     * @return
     */
    List<AgentContractVo> getAgentContractsByEndTime(String date);

    /**
     *
     * @param id
     * @return
     */
    void renewalAgentContract(Long id);


    /**
     * 获取代理合同
     * @param agentIds
     * @return
     */
    Map<Long, List<AgentContract>> getAgentContractByAgentIds(List<Long> agentIds);

    /**
     * 生成代理合同PDF
     * @param contractId
     * @param agentId
     * @param contractVsion
     * @param response
     */
    void createAgentContractPdf(Long contractId, Long agentId,  Long contractVsion, Integer contractTemplateMode,HttpServletResponse response);

    AgentContract latestActiveAgentContract(Long fkAgentId);

    /**
     * 审批同意
     *
     * @param id
     */
    void agree(Long id);

    /**
     * 发送代理合同未签署提醒邮件
     * 基于公司配置的提醒间隔，给符合条件的未签署合同发送提醒邮件
     */
    void sendAgentContractUnsignedReminders();

    void createAgentContractPdfForOthers(CreateAgentContractDto createAgentContractDto, HttpServletResponse response);
}