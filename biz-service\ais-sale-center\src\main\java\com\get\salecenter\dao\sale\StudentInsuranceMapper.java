package com.get.salecenter.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.AgentsBindingVo;
import com.get.salecenter.vo.InsurancePayFormDetailVo;
import com.get.salecenter.vo.StudentInsuranceVo;
import com.get.salecenter.entity.StudentInsurance;
import com.get.salecenter.dto.StudentInsuranceDto;
import com.get.salecenter.dto.query.InsuranceSummaryQueryDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface StudentInsuranceMapper extends GetMapper<StudentInsurance> {

    List<StudentInsuranceVo> getStudentInsuranceList(IPage<StudentInsuranceVo> iPage, StudentInsuranceDto studentInsurance);

    @DS("saledb-doris")
    List<StudentInsuranceVo> getStudentInsuranceSummary(IPage<StudentInsuranceVo> iPage,
                                                        @Param("studentInsurance") InsuranceSummaryQueryDto studentInsurance,
                                                        @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                        @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                        @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                        @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    List<InsurancePayFormDetailVo> getPaidAmountByIds(@Param("itemIds") Set<Long> itemIds);

    Long verifyInsPermissions(@Param("insId") Long insId,@Param("staffFollowerIds") List<Long> staffFollowerIds);

    String getNumById(Long fkTableId);

    Long getAgentIdByTargetId(@Param("targetId") Long targetId);

    Long getIdByTargetId(@Param("targetId") Long targetId);


    Long getStudentIdById(Long fkTableId);

    /**
     * 编号下拉框
     *
     * @param studentId
     * @return
     */
    List<BaseSelectEntity> getStudentInsuranceSelect(@Param("studentId") Long studentId);

    List<StudentInsurance> getStudentInsurances(@Param("studentId") Long studentId, @Param("companyIds") List<Long> companyIds, @Param("keyWord") String keyWord);

    List<StudentInsuranceVo> getCompanyIdsByInsuranceIds(@Param("insuranceIds") List<Long> insuranceIds);

    List<Long> getInsuranceIdsByStudentId(@Param("studentId") Long studentId);

    List<AgentsBindingVo> getStudentInsuranceAgent(@Param("fkStudentNum")String fkStudentNum);
}