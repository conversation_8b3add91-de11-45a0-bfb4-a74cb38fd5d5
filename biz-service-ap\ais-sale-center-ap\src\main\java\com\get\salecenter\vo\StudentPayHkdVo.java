package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * author:<PERSON>
 * Time: 17:55
 * Date: 2023/1/17
 * Description:
 */
@Data
public class StudentPayHkdVo {

    @ApiModelProperty(value = "付款币种（不带币种的）")
    private String payAmount;

    @ApiModelProperty(value = "付合港币（不带币种的）")
    private String payAmountHkd;

    @ApiModelProperty(value = "付款币种")
    private String payAmountInfo;

    @ApiModelProperty(value = "折合港币")
    private String payInfoHkd;
}
