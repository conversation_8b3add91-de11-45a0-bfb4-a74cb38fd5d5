package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2024/4/19
 * @TIME: 10:51
 * @Description:
 **/
@Data
public class KpiPlanStatisticsDto {
    @ApiModelProperty(value = "KPI方案Id")
    @NotNull(message = "KPI方案Id不能为空")
    private Long fkKpiPlanId;

    @ApiModelProperty(value = "根节点员工Id（以其为根节点维度）")
    @NotNull(message = "根节点员工Id不能为空")
    private Long rootFkStaffId;
}
