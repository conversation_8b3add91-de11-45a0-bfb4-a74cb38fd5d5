package com.get.insurancecenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.insurancecenter.dto.order.CreditCardOrderListDto;
import com.get.insurancecenter.dto.order.OrderListDto;
import com.get.insurancecenter.entity.InsuranceOrder;
import com.get.insurancecenter.vo.order.CreditCardOrderVo;
import com.get.insurancecenter.vo.order.OrderDetailVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:
 */
@Mapper
public interface InsuranceOrderMapper extends BaseMapper<InsuranceOrder> {

    /**
     * 根据订单号查询保单信息
     *
     * @param orderNum
     * @return
     */
    InsuranceOrder selectByOrderNum(@Param("orderNum") String orderNum);

    /**
     * 根据订单号查询订单信息
     *
     * @param limit
     * @return
     */
    List<InsuranceOrder> selectPendingOrders(@Param("limit") int limit);

    /**
     * 根据订单号查询订单详情
     *
     * @param id
     * @return
     */
    OrderDetailVo selectOrderDetailById(@Param("id") Long id);

    /**
     * 根据伙伴用户Id、订单状态、日期查询订单列表
     *
     * @param partnerUserId
     * @param orderStatus
     * @param date
     * @return
     */
    List<InsuranceOrder> selectOrderList(@Param("partnerUserId") Long partnerUserId,
                                         @Param("orderStatus") Integer orderStatus,
                                         @Param("date") String date);


    /**
     * 订单列表
     *
     * @param page
     * @param orderListDto
     * @return
     */
    List<OrderDetailVo> selectOrderPage(IPage<OrderDetailVo> page, @Param("param") OrderListDto orderListDto);


    /**
     * 根据代理Id查询佣金订单
     *
     * @param agentId
     * @param insurantName
     * @return
     */
    List<OrderDetailVo> selectCommissionOrderByAgentId(@Param("agentId") Long agentId,
                                                       @Param("insurantName") String insurantName,
                                                       @Param("settlementStatusList") List<Integer> settlementStatusList);


    /**
     * 根据结算状态查询订单
     *
     * @param settlementStatusList
     * @return
     */
    List<InsuranceOrder> getOrderBySettlementStatus(@Param("settlementStatusList") List<Integer> settlementStatusList);

    /**
     * 信用卡订单列表
     *
     * @param page
     * @param orderListDto
     * @return
     */
    List<CreditCardOrderVo> selectCreditCardOrderPage(IPage<CreditCardOrderVo> page, @Param("param") CreditCardOrderListDto orderListDto);
}
