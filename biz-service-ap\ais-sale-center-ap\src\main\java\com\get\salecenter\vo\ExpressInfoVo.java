package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Hardy
 * @create: 2021/7/8 17:53
 * @verison: 1.0
 * @description:
 */
@Data
public class ExpressInfoVo {

//    /**
//     * 报名名册id
//     */
//    @ApiModelProperty(value = "报名名册id")
//    private Long conventionRegistrationId;

    /**
     * 快递公司
     */
    @ApiModelProperty(value = "快递公司")
    private String courierCompany;

    /**
     * 快递编号
     */
    @ApiModelProperty(value = "快递编号")
    private String courierNumber;

    /**
     * 快递件数
     */
    @ApiModelProperty(value = "快递件数")
    private Integer pieces;

    /**
     * 内容备注
     */
    @ApiModelProperty(value = "内容备注")
    private String remark;
}
