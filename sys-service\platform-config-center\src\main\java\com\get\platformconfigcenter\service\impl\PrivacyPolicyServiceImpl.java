package com.get.platformconfigcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.TableEnum;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.platformconfigcenter.dao.registration.PrivacyPolicyMapper;
import com.get.platformconfigcenter.dao.registration.PrivacyPolicyTranslationMapper;
import com.get.platformconfigcenter.vo.PrivacyPolicyVo;
import com.get.platformconfigcenter.entity.PrivacyPolicy;
import com.get.platformconfigcenter.service.IPrivacyPolicyService;
import com.get.platformconfigcenter.dto.PrivacyPolicyDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2021/5/13 15:54
 * @verison: 1.0
 * @description:
 */
@Service
public class PrivacyPolicyServiceImpl implements IPrivacyPolicyService {
    @Resource
    private PrivacyPolicyMapper privacyPolicyMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private PrivacyPolicyTranslationMapper privacyPolicyTranslationMapper;

    @Override
    public List<PrivacyPolicyVo> getPrivacyPolicys(PrivacyPolicyDto privacyPolicyDto, SearchBean<PrivacyPolicyDto> page) {
//        Example example = new Example(PrivacyPolicy.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(privacyPolicyDto)) {
//            //查询条件
//            if (GeneralTool.isNotEmpty(privacyPolicyDto.getTitle())) {
//                criteria.andLike("title", "%" + privacyPolicyDto.getTitle() + "%");
//            }
//        }
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<PrivacyPolicy> privacyPolicies = privacyPolicyMapper.selectByExample(example);
//        page.restPage(privacyPolicies);

        LambdaQueryWrapper<PrivacyPolicy> lambdaQueryWrapper = Wrappers.<PrivacyPolicy>lambdaQuery();
        if (GeneralTool.isNotEmpty(privacyPolicyDto)) {
            //查询条件
            if (GeneralTool.isNotEmpty(privacyPolicyDto.getTitle())) {
                lambdaQueryWrapper.like(PrivacyPolicy::getTitle, privacyPolicyDto.getTitle());
            }
        }
        IPage<PrivacyPolicy> pages = privacyPolicyMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<PrivacyPolicy> privacyPolicies = pages.getRecords();
        page.setAll((int) pages.getTotal());

        List<PrivacyPolicyVo> privacyPolicyVoList = privacyPolicies.stream().map(privacyPolicy -> BeanCopyUtils.objClone(privacyPolicy, PrivacyPolicyVo::new)).collect(Collectors.toList());
        privacyPolicyVoList.forEach(privacyPolicyVo -> privacyPolicyVo.setFkTableName(TableEnum.PRIVATE_POLICY.key));
        return privacyPolicyVoList;
    }

    @Override
    public Long addPrivacyPolicy(PrivacyPolicyDto privacyPolicyDto) {
        if (GeneralTool.isEmpty(privacyPolicyDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        PrivacyPolicy privacyPolicy = BeanCopyUtils.objClone(privacyPolicyDto, PrivacyPolicy::new);
        utilService.updateUserInfoToEntity(privacyPolicy);
        int i = privacyPolicyMapper.insertSelective(privacyPolicy);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return privacyPolicy.getId();
    }

    @Override
    public PrivacyPolicyVo updatePrivacyPolicyVo(PrivacyPolicyDto privacyPolicyDto) {
        if (GeneralTool.isEmpty(privacyPolicyDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(privacyPolicyDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        PrivacyPolicy privacyPolicy = BeanCopyUtils.objClone(privacyPolicyDto, PrivacyPolicy::new);
        utilService.updateUserInfoToEntity(privacyPolicy);
        privacyPolicyMapper.updateByPrimaryKeySelective(privacyPolicy);
        return findPrivacyPolicyById(privacyPolicyDto.getId());
    }

    @Override
    public PrivacyPolicyVo findPrivacyPolicyById(Long id) throws GetServiceException {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        PrivacyPolicy privacyPolicy = privacyPolicyMapper.selectById(id);
        if (GeneralTool.isEmpty(privacyPolicy)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        PrivacyPolicyVo privacyPolicyVo = BeanCopyUtils.objClone(privacyPolicy, PrivacyPolicyVo::new);
        privacyPolicyVo.setFkTableName(TableEnum.PRIVATE_POLICY.key);
        return privacyPolicyVo;
    }

    @Override
    public void deletePrivacyPolicy(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (privacyPolicyMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        privacyPolicyMapper.deleteById(id);

        //删除翻译内容
        privacyPolicyTranslationMapper.deleteTranslations(TableEnum.PRIVATE_POLICY.key, id);
    }

    @Override
    public List<PrivacyPolicyVo> getPrivacyPolicyList() {
//        Example example = new Example(PrivacyPolicy.class);
        //111111待测试
        List<PrivacyPolicy> privacyPolicies = privacyPolicyMapper.selectList(Wrappers.<PrivacyPolicy>lambdaQuery());
        return privacyPolicies.stream().map(privacyPolicy -> BeanCopyUtils.objClone(privacyPolicy, PrivacyPolicyVo::new)).collect(Collectors.toList());
    }

    @Override
    public Map<Long, String> getPrivacyPolicyTitlesByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
//        Example example = new Example(PrivacyPolicy.class);
//        example.createCriteria().andIn("id",ids);
//        List<PrivacyPolicy> privacyPolicies = privacyPolicyMapper.selectByExample(example);

        List<PrivacyPolicy> privacyPolicies = privacyPolicyMapper.selectList(Wrappers.<PrivacyPolicy>lambdaQuery().in(PrivacyPolicy::getId, ids));
        if (GeneralTool.isEmpty(privacyPolicies)) {
            return map;
        }
        for (PrivacyPolicy privacyPolicy : privacyPolicies) {
            map.put(privacyPolicy.getId(), privacyPolicy.getTitle());
        }
        return map;
    }

}
