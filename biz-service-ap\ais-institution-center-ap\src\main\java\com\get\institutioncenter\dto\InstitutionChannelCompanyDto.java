package com.get.institutioncenter.dto;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class InstitutionChannelCompanyDto extends BaseVoEntity {

    @ApiModelProperty(value = "学校渠道id")
    @NotNull(message = "学校渠道id不能为空",groups = {BaseVoEntity.Add.class})
    private Long fkInstitutionChannelId;

    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
}
