package com.get.institutioncenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.dto.ContractFormulaInstitutionZoneDto;
import com.get.institutioncenter.entity.ContractFormulaInstitutionZone;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/26 15:03
 * @verison: 1.0
 * @description:
 */
public interface IContractFormulaInstitutionZoneService extends BaseService<ContractFormulaInstitutionZone> {
    /**
     * @return java.lang.Long
     * @Description :新增
     * @Param [contractFormulaInstitutionZoneDto]
     * <AUTHOR>
     */
    Long addContractFormulaInstitutionZone(ContractFormulaInstitutionZoneDto contractFormulaInstitutionZoneDto);

    /**
     * @return void
     * @Description :
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    void deleteByFkid(Long contractFormulaId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过合同公式id 查找对应校区ids
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<Long> getZoneIdListByFkid(Long contractFormulaId);

}
