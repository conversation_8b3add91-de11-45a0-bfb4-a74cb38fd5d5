package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.InstitutionCourseMajorLevelMapper;
import com.get.institutioncenter.dao.MajorLevelMapper;
import com.get.institutioncenter.vo.MajorLevelVo;
import com.get.institutioncenter.vo.MajorLevelSelectVo;
import com.get.institutioncenter.entity.InstitutionCourseMajorLevel;
import com.get.institutioncenter.entity.MajorLevel;
import com.get.institutioncenter.service.IMajorLevelService;
import com.get.institutioncenter.service.ITranslationMappingService;
import com.get.institutioncenter.dto.MajorLevelDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2020/7/31 12:13
 * @verison: 1.0
 * @description: 专业等级管理实现类
 */
@Service
public class MajorLevelServiceImpl extends BaseServiceImpl<MajorLevelMapper, MajorLevel> implements IMajorLevelService {

    @Resource
    private MajorLevelMapper majorLevelMapper;

    @Resource
    private UtilService utilService;
    @Resource
    private ITranslationMappingService translationMappingService;
    @Resource
    private InstitutionCourseMajorLevelMapper institutionCourseMajorLevelMapper;

    @Override
    public MajorLevelVo findMajorLevelById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        MajorLevel majorLevel = majorLevelMapper.selectById(id);
        MajorLevelVo majorLevelVo = BeanCopyUtils.objClone(majorLevel, MajorLevelVo::new);
        majorLevelVo.setFkTableName(TableEnum.INSTITUTION_MAJOR_LEVEL.key);
        return majorLevelVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<MajorLevelDto> majorLevelDtos) {
        if (GeneralTool.isEmpty(majorLevelDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //获取最大排序(防止每次都要去查最大值，所以在外面查出一次，循环几次 就自增几次)
        Integer maxViewOrder = majorLevelMapper.getMaxViewOrder();
        for (MajorLevelDto majorLevelDto : majorLevelDtos) {
            if (GeneralTool.isEmpty(majorLevelDto.getId())) {
                if (validateAdd(majorLevelDto)) {
                    MajorLevel majorLevel = BeanCopyUtils.objClone(majorLevelDto, MajorLevel::new);
                    majorLevel.setViewOrder(maxViewOrder);
                    utilService.updateUserInfoToEntity(majorLevel);
                    majorLevelMapper.insertSelective(majorLevel);
                    maxViewOrder++;
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("majorLevelName_exists"));
                }
            } else {
                if (validateUpdate(majorLevelDto)) {
                    MajorLevel majorLevel = BeanCopyUtils.objClone(majorLevelDto, MajorLevel::new);
                    utilService.updateUserInfoToEntity(majorLevel);
                    majorLevelMapper.updateById(majorLevel);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("majorLevelName_exists"));
                }
            }

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (findMajorLevelById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        majorLevelMapper.deleteById(id);

        //删除翻译内容
        translationMappingService.deleteTranslations(TableEnum.INSTITUTION_MAJOR_LEVEL.key, id);
    }

    @Override
    public MajorLevelVo updateMajorLevel(MajorLevelDto majorLevelDto) {
        if (majorLevelDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        MajorLevel result = majorLevelMapper.selectById(majorLevelDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (validateUpdate(majorLevelDto)) {
            MajorLevel majorLevel = BeanCopyUtils.objClone(majorLevelDto, MajorLevel::new);
            utilService.updateUserInfoToEntity(majorLevel);
            majorLevelMapper.updateById(majorLevel);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("majorLevelName_exists"));
        }
        return findMajorLevelById(majorLevelDto.getId());
    }

    @Override
    public List<MajorLevelVo> getMajorLevels(MajorLevelDto majorLevelDto, Page page) {
        LambdaQueryWrapper<MajorLevel> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(majorLevelDto)) {
            if (GeneralTool.isNotEmpty(majorLevelDto.getKeyWord())) {
                wrapper.like(MajorLevel::getLevelName, majorLevelDto.getKeyWord());
            }
        }
        wrapper.orderByDesc(MajorLevel::getViewOrder);
        //获取分页数据
        IPage<MajorLevel> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<MajorLevel> majorLevels = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<MajorLevelVo> convertDatas = new ArrayList<>();
        for (MajorLevel majorLevel : majorLevels) {
            MajorLevelVo majorLevelVo = BeanCopyUtils.objClone(majorLevel, MajorLevelVo::new);
            majorLevelVo.setFkTableName(TableEnum.INSTITUTION_MAJOR_LEVEL.key);
            convertDatas.add(majorLevelVo);
        }
        return convertDatas;
    }

    @Override
    public List<BaseSelectEntity> getMajorLevelSelect() {
        return majorLevelMapper.getMajorLevelSelect();
    }

    @Override
    public Map<Long, String> getMajorLevelNamesByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        List<MajorLevel> majorLevels = this.majorLevelMapper.selectList(Wrappers.<MajorLevel>query().lambda().in(MajorLevel::getId, ids));
        if (GeneralTool.isEmpty(majorLevels)) {
            return map;
        }
        for (MajorLevel majorLevel : majorLevels) {
            map.put(majorLevel.getId(), majorLevel.getLevelName()+" "+majorLevel.getLevelNameChn());
        }
        return map;
    }

    @Override
    public Map<Long, MajorLevel> getMajorLevelByIds(Set<Long> ids) {
        Map<Long, MajorLevel> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        List<MajorLevel> majorLevels = this.majorLevelMapper.selectList(Wrappers.<MajorLevel>query().lambda().in(MajorLevel::getId, ids));
        if (GeneralTool.isEmpty(majorLevels)) {
            return map;
        }
        for (MajorLevel majorLevel : majorLevels) {
            map.put(majorLevel.getId(), majorLevel);
        }
        return map;
    }

    /**
     * feign调用 通过课程等级id 查找对应的课程等级名称
     *
     * @Date 16:24 2021/5/24
     * <AUTHOR>
     */
    @Override
    public String getMajorLevelNamesById(Long id) {
        return majorLevelMapper.getMajorLevelNameById(id);
    }

    @Override
    public Map<Long, String> getMajorLevelNamesByCourIds(Set<Long> ids) {
        Map<Long, String> majorLevelNameMap = new HashMap<>();
        List<MajorLevel> majorLevels = majorLevelMapper.selectList(Wrappers.<MajorLevel>lambdaQuery());
        for (MajorLevel majorLevel : majorLevels) {
            String name = majorLevel.getLevelNameChn() + (majorLevel.getLevelName() == null ? "" : "（" +majorLevel.getLevelName() + "）");
            majorLevelNameMap.put(majorLevel.getId(), name);
        }

        Map<Long, String> map = new HashMap<>();
        List<InstitutionCourseMajorLevel> institutionCourseMajorLevels = institutionCourseMajorLevelMapper.selectList(Wrappers.<InstitutionCourseMajorLevel>lambdaQuery().in(InstitutionCourseMajorLevel::getFkInstitutionCourseId, ids));
        if (GeneralTool.isEmpty(institutionCourseMajorLevels)) {
            return map;
        }
        Map<Long, List<InstitutionCourseMajorLevel>> listMap = institutionCourseMajorLevels.stream().collect(Collectors.groupingBy(InstitutionCourseMajorLevel::getFkInstitutionCourseId));
        for (Long aLong : listMap.keySet()) {
            List<InstitutionCourseMajorLevel> institutionCourseMajorLevels1 = listMap.get(aLong);
            if (GeneralTool.isNotEmpty(institutionCourseMajorLevels1)) {
                StringJoiner sj = new StringJoiner(",");
                for (InstitutionCourseMajorLevel institutionCourseMajorLevel : institutionCourseMajorLevels1) {
                    sj.add(majorLevelNameMap.get(institutionCourseMajorLevel.getFkMajorLevelId()));
                }
                map.put(aLong, sj.toString());
            } else {
                map.put(aLong, null);
            }
        }
        return map;
    }

    @Override
    public String getMajorLevelIdStringByCourseId(Long id) {
        return institutionCourseMajorLevelMapper.getMajorLevelIdStringByCourseId(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<MajorLevelDto> majorLevelDtos) {
        if (GeneralTool.isEmpty(majorLevelDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        MajorLevel ro = BeanCopyUtils.objClone(majorLevelDtos.get(0), MajorLevel::new);
        Integer oneorder = ro.getViewOrder();
        MajorLevel rt = BeanCopyUtils.objClone(majorLevelDtos.get(1), MajorLevel::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        majorLevelMapper.updateById(ro);
        majorLevelMapper.updateById(rt);
    }

    private boolean validateAdd(MajorLevelDto majorLevelDto) {
        List<MajorLevel> list = this.majorLevelMapper.selectList(Wrappers.<MajorLevel>query().lambda().eq(MajorLevel::getLevelName, majorLevelDto.getLevelName()));
        return GeneralTool.isEmpty(list);
    }

    private boolean validateUpdate(MajorLevelDto majorLevelDto) {
        List<MajorLevel> list = this.majorLevelMapper.selectList(Wrappers.<MajorLevel>query().lambda().eq(MajorLevel::getLevelName, majorLevelDto.getLevelName()));
        return list.size() <= 0 || list.get(0).getId().equals(majorLevelDto.getId());
    }

    @Override
    public Map<Long, String> getMajorLevelNameByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        List<MajorLevel> majorLevels = this.majorLevelMapper.selectList(Wrappers.<MajorLevel>query().lambda().in(MajorLevel::getId, ids));
        if (GeneralTool.isEmpty(majorLevels)) {
            return map;
        }
        for (MajorLevel majorLevel : majorLevels) {
            map.put(majorLevel.getId(), majorLevel.getLevelName());
        }
        return map;
    }

    @Override
    public List<MajorLevelSelectVo> getMajorLevelAndGroupSelect() {
        List<MajorLevel> majorLevels = majorLevelMapper.getMajorLevelAndGroupSelect();
        Map<String, List<MajorLevel>> listMap =
                majorLevels.stream().filter(e ->GeneralTool.isNotBlank(e.getGroupName())).collect(Collectors.groupingBy(MajorLevel::getGroupName));
        List<MajorLevelSelectVo> majorLevelSelectVos = new ArrayList<>();
        if (GeneralTool.isNotEmpty(listMap)){
            for (String s : listMap.keySet()) {
                MajorLevelSelectVo majorLevelSelectVo = new MajorLevelSelectVo();
                List<MajorLevel> levels = listMap.get(s);
                List<BaseSelectEntity> entityList = levels.stream().map(e -> {
                    BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
                    baseSelectEntity.setId(e.getId());
                    baseSelectEntity.setName(e.getLevelName());
                    baseSelectEntity.setNameChn(e.getLevelNameChn());
                    baseSelectEntity.setOrder(e.getViewOrder());
                    String fullName = e.getLevelName();
                    if (GeneralTool.isNotEmpty(e.getLevelNameChn())) {
                        fullName = fullName + "（" + e.getLevelNameChn() + "）";
                    }
                    baseSelectEntity.setFullName(fullName);
                    return baseSelectEntity;
                }).collect(Collectors.toList());

                entityList = entityList.stream().sorted(Comparator.comparing(BaseSelectEntity::getOrder).reversed()).collect(Collectors.toList());
                if (GeneralTool.isNotEmpty(listMap.get(s).get(0).getViewOrder())){
                    majorLevelSelectVo.setOrder(listMap.get(s).get(0).getViewOrder());
                }
                if (GeneralTool.isNotEmpty(listMap.get(s).get(0).getGroupNameChn())){
                    s = s +"（"+listMap.get(s).get(0).getGroupNameChn()+"）";
                }
                majorLevelSelectVo.setMajorGroup(s);
                majorLevelSelectVo.setMajorLevelList(entityList);
                majorLevelSelectVos.add(majorLevelSelectVo);
            }
            majorLevelSelectVos = majorLevelSelectVos.stream().sorted(Comparator.comparing(MajorLevelSelectVo::getOrder).reversed()).collect(Collectors.toList());
        }
        return majorLevelSelectVos;
    }


    @Override
    public Map<Long, String> getMajorLevelNameChnsByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        List<MajorLevel> majorLevels = this.majorLevelMapper.selectList(Wrappers.<MajorLevel>query().lambda().in(MajorLevel::getId, ids));
        if (GeneralTool.isEmpty(majorLevels)) {
            return map;
        }
        for (MajorLevel majorLevel : majorLevels) {
            map.put(majorLevel.getId(), majorLevel.getLevelNameChn());
        }
        return map;
    }

    @Override
    public Map<Long, Set<Long>> getMajorLevelIdsByIds(Set<Long> ids) {
        Map<Long, Set<Long>> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        List<InstitutionCourseMajorLevel> institutionCourseMajorLevels = institutionCourseMajorLevelMapper.selectList(new LambdaQueryWrapper<InstitutionCourseMajorLevel>().in(InstitutionCourseMajorLevel::getFkInstitutionCourseId, ids));
        if (GeneralTool.isEmpty(institutionCourseMajorLevels)) {
            return map;
        }
        for (InstitutionCourseMajorLevel institutionCourseMajorLevel : institutionCourseMajorLevels) {
            //如果集合包含这个提供商id,则往原来的数据添加公司id
            if (map.containsKey(institutionCourseMajorLevel.getFkInstitutionCourseId())) {
                Set<Long> majorlevels = map.get(institutionCourseMajorLevel.getFkInstitutionCourseId());
                majorlevels.add(institutionCourseMajorLevel.getFkMajorLevelId());
                map.put(institutionCourseMajorLevel.getFkInstitutionCourseId(), majorlevels);
                continue;
            }
            //如果没有包含,则往里面添加新数据
            Set<Long> majorlevels = new HashSet<>();
            majorlevels.add(institutionCourseMajorLevel.getFkMajorLevelId());
            map.put(institutionCourseMajorLevel.getFkInstitutionCourseId(), majorlevels);
        }
        return map;
    }

}
