package com.get.officecenter.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.officecenter.vo.LeaveApplicationFormVo;
import com.get.officecenter.vo.LeaveApplicationFormTypeVo;
import com.get.officecenter.entity.LeaveApplicationFormType;
import com.get.officecenter.dto.LeaveApplicationFormTypeDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface LeaveApplicationFormTypeMapper extends GetMapper<LeaveApplicationFormType> {

    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(LeaveApplicationFormType record);

    /**
     * @return java.lang.Integer
     * @Description :获取最大排序值
     * @Param []
     * <AUTHOR>
     */
    Integer getMaxViewOrder();

    /**
     * 根据类型key获取请假类型
     *
     * @param key
     * @return
     */
    LeaveApplicationFormVo getLeaveApplicationFormTypeByKey(@Param("key") String key);

    /**
     * 根据key获取类型名称
     *
     * @param leaveTypeKey
     * @return
     */
    String getLeaveApplicationFormTypeNameByKey(@Param("leaveTypeKey") String leaveTypeKey);

    /**
     * 列表
     * @param iPage
     * @param leaveApplicationFormTypeDto
     * @return
     */
    List<LeaveApplicationFormTypeVo> getLeaveApplicationFormTypes(IPage<LeaveApplicationFormType> iPage, @Param("leaveApplicationFormTypeDto") LeaveApplicationFormTypeDto leaveApplicationFormTypeDto, @Param("companyIds") List<Long> companyIds);
}