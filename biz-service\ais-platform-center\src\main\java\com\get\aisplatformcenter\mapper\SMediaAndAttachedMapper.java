package com.get.aisplatformcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.aisplatformcenterap.dto.MLiveDto;
import com.get.aisplatformcenterap.dto.SMediaAndAttachedPublicDto;
import com.get.aisplatformcenterap.entity.SMediaAndAttachedEntity;
import com.get.aisplatformcenterap.vo.FileArray;
import com.get.aisplatformcenterap.vo.FilePubArray;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【s_media_and_attached】的数据库操作Mapper
* @createDate 2025-01-08 17:33:34
* @Entity com.partner.entity.SMediaAndAttached
*/
@Mapper
public interface SMediaAndAttachedMapper extends BaseMapper<SMediaAndAttachedEntity> {

    List<FileArray>  selectFileArrays(MLiveDto params);


    List<FilePubArray> selectPublicPlatformFileArrays(SMediaAndAttachedPublicDto params);

}




