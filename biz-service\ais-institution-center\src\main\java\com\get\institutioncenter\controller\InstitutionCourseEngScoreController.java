package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.vo.InstitutionCourseEngScoreVo;
import com.get.institutioncenter.service.IInstitutionCourseEngScoreService;
import com.get.institutioncenter.dto.InstitutionCourseEngScoreDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/12/7
 * @TIME: 17:54
 * @Description:
 **/
@Api(tags = "英语成绩管理")
@RestController
@RequestMapping("/institution/institutionCourseEngScore")
public class InstitutionCourseEngScoreController {
    @Resource
    private IInstitutionCourseEngScoreService institutionCourseEngScoreService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/英语成绩管理/英语成绩详情")
    @GetMapping("/{id}")
    public ResponseBo<InstitutionCourseEngScoreVo> detail(@PathVariable("id") Long id) {
        InstitutionCourseEngScoreVo data = institutionCourseEngScoreService.findInstitutionCourseEngScoreById(id);
        return new ResponseBo<>(data);
    }


    /**
     * 删除
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/英语成绩管理/删除英语成绩")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        this.institutionCourseEngScoreService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param institutionCourseEngScoreDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/英语成绩管理/更新英语成绩")
    @PostMapping("update")
    public ResponseBo<InstitutionCourseEngScoreVo> update(@RequestBody @Validated(InstitutionCourseEngScoreDto.Update.class) InstitutionCourseEngScoreDto institutionCourseEngScoreDto) {
        return UpdateResponseBo.ok(institutionCourseEngScoreService.updateInstitutionCourseEngScore(institutionCourseEngScoreDto));
    }

    /**
     * 批量新增信息
     *
     * @param resources
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/英语成绩管理/批量保存")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(InstitutionCourseEngScoreDto.Add.class) ValidList<InstitutionCourseEngScoreDto> resources) {
        institutionCourseEngScoreService.batchAdd(resources);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 代理附件类型
     * @Param []
     * <AUTHOR>
     */
    @ApiOperation(value = "英语测试类型", notes = "")
    @VerifyPermission(IsVerify = false)
    @GetMapping("getEnglishTestType")
    public ResponseBo getEnglishTestType() {
        List<Map<String, Object>> datas = institutionCourseEngScoreService.getEnglishTestType();
        return new ListResponseBo<>(datas);
    }

    @ApiOperation("英语成绩枚举下拉")
    @VerifyPermission(IsVerify = false)
    @GetMapping("getEnglishGrades")
    public ResponseBo getEnglishGrades() {
        return new ListResponseBo<>(ProjectExtraEnum.enums2Arrays(ProjectExtraEnum.English_subtype));
    }

}
