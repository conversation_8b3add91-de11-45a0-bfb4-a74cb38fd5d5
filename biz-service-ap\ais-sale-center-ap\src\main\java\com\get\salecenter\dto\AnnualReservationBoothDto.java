package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/7/7 18:32
 * @verison: 1.0
 * @description:
 */
@Data
public class AnnualReservationBoothDto {

    /**
     * 报名名册id
     */
    @ApiModelProperty(value = "报名名册id")
    private Long conventionRegistrationId;

    /**
     * 修改名字
     */
    @ApiModelProperty(value = "修改名字")
    private String modifyName;

    /**
     * 快递信息
     */
    @ApiModelProperty(value = "快递信息")
    private List<ExpressInfoDto> expressInfoVoList;

    /**
     * 提供晚宴抽奖礼品：0否/1是
     */
    @ApiModelProperty("提供晚宴抽奖礼品：0否/1是")
    private Integer provideGifts;
}
