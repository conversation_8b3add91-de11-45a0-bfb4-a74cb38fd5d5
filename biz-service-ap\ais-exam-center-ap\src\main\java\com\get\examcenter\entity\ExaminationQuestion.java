package com.get.examcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("m_examination_question")
public class ExaminationQuestion extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 题目类型Id
     */
    @ApiModelProperty(value = "题目类型Id")
    private Long fkQuestionTypeId;
    /**
     * 考题编号
     */
    @ApiModelProperty(value = "考题编号")
    private String num;
    /**
     * 题型：枚举(单选题0/多选题1/判断题2)
     */
    @ApiModelProperty(value = "题型：枚举(单选题0/多选题1/判断题2)")
    private Integer questionType;
    /**
     * 问题内容
     */
    @ApiModelProperty(value = "问题内容")
    private String question;
    /**
     * 得分
     */
    @ApiModelProperty(value = "得分")
    private Integer score;
    /**
     * 答题时间限制（分钟）
     */
    @ApiModelProperty(value = "答题时间限制（分钟）")
    private Integer timeLimit;
    /**
     * 是否复习题：0否/1是
     */
    @ApiModelProperty(value = "是否复习题：0否/1是")
    private Boolean isReview;
    /**
     * 是否允许重考：0否/1是
     */
    @ApiModelProperty(value = "是否允许重考：0否/1是")
    private Boolean isRetest;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;
    /**
     * 默认为0，为0时随机排序，排序为从大到小顺序排列
     */
    @ApiModelProperty(value = "默认为0，为0时随机排序，排序为从大到小顺序排列")
    private Integer viewOrder;

    /**
     * 是否必填：0否/1是
     */
    @ApiModelProperty(value = "是否必填：0否/1是")
    private Boolean isRequired;

    /**
     * 考卷Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
}