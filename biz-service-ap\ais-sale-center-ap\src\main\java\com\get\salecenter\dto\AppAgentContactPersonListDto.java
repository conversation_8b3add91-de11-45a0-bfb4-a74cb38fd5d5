package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Hardy
 * @create: 2022/11/22 12:06
 * @verison: 1.0
 * @description:
 */
@Data
public class AppAgentContactPersonListDto {

    @ApiModelProperty("联系人类型")
    private String fkContactPersonTypeKey;

    @ApiModelProperty("名称/邮箱")
    private String keyWord;

//    @NotNull(message = "代理申请Id", groups = {AppAgentContactPersonListDto.List.class})
    @ApiModelProperty("代理申请Id")
    private Long fkAppAgentId;

}
