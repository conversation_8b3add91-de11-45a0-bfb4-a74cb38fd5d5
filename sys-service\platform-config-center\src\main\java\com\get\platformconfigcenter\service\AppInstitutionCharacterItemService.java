package com.get.platformconfigcenter.service;


/**
 * 课程动态表单详情配置业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2021/5/25 10:12
 */
public interface AppInstitutionCharacterItemService {
    /**
     * 课程动态表单配置Item详情列表数据
     *
     * @Date 10:19 2021/5/24
     * <AUTHOR>
     */
    //List<AppInstitutionCharacterItemVo> getInstitutionCharacterItemList(Long characterId);

    /**
     * 删除课程动态表单详情配置
     *
     * @Date 11:48 2021/5/24
     * <AUTHOR>
     */
    //void delete(List<Long> ids);

    /**
     * 新增课程动态表单Item详情配置
     *
     * @Date 16:13 2021/5/20
     * <AUTHOR>
     */
   // void addInstitutionCharacterItem(AppInstitutionCharacterItemDto appInstitutionCharacterItemVo);

    /**
     * 修改课程动态表单Item详情配置
     *
     * @Date 16:29 2021/5/20
     * <AUTHOR>
     */
    //void updateInstitutionCharacterItem(AppInstitutionCharacterItemDto appInstitutionCharacterItemVo);

    /**
     * 课程动态表单Item详情
     *
     * @Date 16:34 2021/5/20
     * <AUTHOR>
     */
    //AppInstitutionCharacterItemVo findInstitutionCharacterItemById(Long id);

    /**
     * @Description:上移下移
     * @Param
     * @Date 12:49 2021/5/12
     * <AUTHOR>
     */
    //void movingOrder(List<AppInstitutionCharacterItemDto> appInstitutionCharacterItemVos);
}
