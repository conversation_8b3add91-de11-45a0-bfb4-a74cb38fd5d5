<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ReceivableReasonMapper">
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.ReceivableReason">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="reason_name" jdbcType="VARCHAR" property="reasonName"/>
        <result column="view_order" jdbcType="INTEGER" property="viewOrder"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <insert id="insert" parameterType="com.get.salecenter.entity.ReceivableReason">
    insert into u_receivable_reason (id, reason_name, view_order, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{reasonName,jdbcType=VARCHAR}, #{viewOrder,jdbcType=INTEGER}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.get.salecenter.entity.ReceivableReason">
        insert into u_receivable_reason
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="reasonName != null">
                reason_name,
            </if>
            <if test="viewOrder != null">
                view_order,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="reasonName != null">
                #{reasonName,jdbcType=VARCHAR},
            </if>
            <if test="viewOrder != null">
                #{viewOrder,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select ifnull(max(view_order)+1,1) from u_receivable_reason
    </select>

    <select id="getReasonSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        select id,reason_name name
        from u_receivable_reason
    </select>

    <select id="getReasonNameById" resultType="java.lang.String">
        SELECT reason_name FROM u_receivable_reason where id=#{id}
    </select>

</mapper>