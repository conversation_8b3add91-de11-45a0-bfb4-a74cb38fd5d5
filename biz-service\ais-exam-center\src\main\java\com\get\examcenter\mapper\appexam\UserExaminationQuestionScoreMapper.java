package com.get.examcenter.mapper.appexam;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.examcenter.vo.ExaminationQuestionVo;
import com.get.examcenter.vo.UserexaminationPaperScoreVo;
import com.get.examcenter.entity.UserExaminationQuestionScore;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@DS("appexamdb")
public interface UserExaminationQuestionScoreMapper extends BaseMapper<UserExaminationQuestionScore> {
//    int insert(UserExaminationQuestionScore record);

    int insertSelective(UserExaminationQuestionScore record);

    int updateByPrimaryKeySelective(UserExaminationQuestionScore record);

    int updateByPrimaryKey(UserExaminationQuestionScore record);

    /**
     * @Description: 根据操作id获取问题ids
     * @Author: Jerry
     * @Date:10:00 2021/8/25
     */
    List<ExaminationQuestionVo> getQuestionByOptGuid(@Param("optGuid") String optGuid);


    /**
     * @Description: 根据操作id获取总分
     * @Author: Jerry
     * @Date:11:44 2021/8/25
     */
    List<UserexaminationPaperScoreVo> getScoreAndUseTimeByOptGuid(@Param("optGuid") String optGuid);

    Boolean isExist(@Param("id") String id);

    Map<String,Object> getUserInfoById(@Param("userId") Long userId);

    List<UserExaminationQuestionScore> selectAnswers(@Param("fkUserId") Long fkUserId,@Param("fkExaminationPaperId") Long fkExaminationPaperId,@Param("optGuid") String optGuid);
}