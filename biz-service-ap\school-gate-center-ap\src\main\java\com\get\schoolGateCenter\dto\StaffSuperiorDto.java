package com.get.schoolGateCenter.dto;
import com.get.schoolGateCenter.entity.StaffSuperior;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2020/7/27
 * @TIME: 15:23
 * @Description:
 **/
@Data
public class StaffSuperiorDto extends StaffSuperior implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 公司
     */
    @ApiModelProperty("公司")
    private String companyName;
    /**
     * 部门
     */
    @ApiModelProperty("部门")
    private String departmentName;
    /**
     * 职位
     */
    @ApiModelProperty("职位")
    private String positionName;

    /**
     * 员工编号
     */
    @ApiModelProperty("员工编号")
    private String num;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String name;


}
