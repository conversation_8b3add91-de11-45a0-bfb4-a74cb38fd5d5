package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 更新佣金结算标记Vo类
 *
 * <AUTHOR>
 * @date 2022/3/14 17:35
 */
@Data
public class SettlementAgentAccountUpdateDto {
    @NotNull(message = "代理id不能为空")
    @ApiModelProperty(value = "代理id")
    private Long fkAgentId;

    @NotNull(message = "学生代理合同账户Id不能为空")
    @ApiModelProperty(value = "学生代理合同账户Id")
    private Long fkAgentContractAccountId;


    @NotNull(message = "学生代理合同账户币种编号不能为空")
    @ApiModelProperty(value = "学生代理合同账户币种编号")
    private String agentContractAccountNum;


    @NotNull(message = "应付计划Id不能为空")
    @ApiModelProperty(value = "应付计划Ids")
    private List<Long> payablePlanIdList;

}
