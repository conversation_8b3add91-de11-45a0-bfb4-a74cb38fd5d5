package com.get.salecenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信小程序页面路径枚举
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Getter
@AllArgsConstructor
public enum MiniProgramPageEnum {

    /**
     * 代理加入页面
     */
    NEW_APPAGENT_ADD("pagesA/joinus/joinus", "代理加入页面"),

    /**
     * 登录页面
     */
    LOGIN("pages/login/login", "登录页面"),

    /**
     * 续约申请页面
     */
    RENEWAL_APPLY("pagesA/review/review", "续约申请页面");

    private final String path;

    private final String description;

    private static final Map<String, MiniProgramPageEnum> MINI_PROGRAM_PAGE_MAP = new HashMap<>();

    static {
        for (MiniProgramPageEnum enumItem : MiniProgramPageEnum.values()) {
            MINI_PROGRAM_PAGE_MAP.put(enumItem.getPath(), enumItem);
        }
    }

    /**
     * 根据路径获取枚举实例
     *
     * @param path 页面路径
     * @return 枚举实例
     */
    public static MiniProgramPageEnum getMiniProgramPageByPath(String path) {
        return MINI_PROGRAM_PAGE_MAP.get(path);
    }

    /**
     * 构建带参数的完整页面路径
     *
     * @param params 参数字符串，
     * @return 完整的页面路径
     */
    public String buildFullPath(String params) {
        if (params == null || params.trim().isEmpty()) {
            return this.path;
        }
        return new StringBuilder(this.path).append("?id=").append(params).toString();
    }

    /**
     * 构建代理加入页面路径（专用方法）
     *
     * @param appAgentId 代理ID
     * @return 完整的代理加入页面路径
     */
    public static String buildAgentJoinPath(Long appAgentId) {
        return NEW_APPAGENT_ADD.buildFullPath(appAgentId.toString());
    }

}