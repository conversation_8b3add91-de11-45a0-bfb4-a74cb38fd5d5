package com.get.examcenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON>.
 * Time: 17:14
 * Date: 2021/8/27
 * Description:称号Vo
 */
@Data
public class ScoreTitleDto extends BaseVoEntity implements Serializable {
    /**
     * 称号名称
     */
    @ApiModelProperty(value = "称号名称")
    @NotBlank(message = "称号名称不能为空", groups = {Add.class, Update.class})
    private String title;

    /**
     * 分值范围（小）
     */
    @ApiModelProperty(value = "分值范围（小）")
    private Integer scoreMin;

    /**
     * 分值范围（大）
     */
    @ApiModelProperty(value = "分值范围（大）")
    private Integer scoreMax;

    /**
     * 排名范围（小）
     */
    @ApiModelProperty(value = "排名范围（小）")
    private Integer rankingMin;

    /**
     * 排名范围（大）
     */
    @ApiModelProperty(value = "排名范围（大）")
    private Integer rankingMax;

    /**
     * 颜色编码，用于突显，可不填
     */
    @ApiModelProperty(value = "颜色编码，用于突显，可不填")
    private String colorCode;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;


    /**
     * 排名称号图片
     */
    @ApiModelProperty(value = "排名称号图片")
    private List<MediaAndAttachedDto> mediaAttachedVos;

    /**
     * 考试Id
     */
    @ApiModelProperty(value = "考试Id")
    private Long fkExaminationId;

    /**
     * 考试Id
     */
    @ApiModelProperty(value = "所属公司Id")
    private Long fkCompanyId;

    /**
     * 参数配置
     */
    @ApiModelProperty(value = "参数配置")
    private String paramJson;
}
