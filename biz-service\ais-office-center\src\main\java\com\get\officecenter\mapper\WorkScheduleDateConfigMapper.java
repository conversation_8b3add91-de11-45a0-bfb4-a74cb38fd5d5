package com.get.officecenter.mapper;

import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.officecenter.entity.WorkScheduleDateConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/1/13
 * @TIME: 11:58
 * @Description:
 **/
@Mapper
public interface WorkScheduleDateConfigMapper extends GetMapper<WorkScheduleDateConfig> {


    /**
     * 检查当前公司的当前日期是否已存在排班
     *
     * @param fkCompanyId
     * @param scheduleDate
     * @return
     */
    Integer checkWorkScheduleDateConfig(@Param("id") Long id, @Param("fkCompanyId") Long fkCompanyId, @Param("scheduleDate") Date scheduleDate);


    /**
     * 年份下拉
     *
     * @return
     */
    List<BaseSelectEntity> getYearSelect(Long fkCompanyId);

    /**
     * 根据时间范围的所有排班
     *
     * @return
     */
    List<WorkScheduleDateConfig> getWorkScheduleDateConfigByDate(@Param("startTime") Date startTime,
                                                                 @Param("endTime")Date endTime,
                                                                 @Param("fkCompanyId") Long fkCompanyId,
                                                                 @Param("fkDepartmentId") Long fkDepartmentId);
}
