package com.get.rocketmqcenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 系统邮件发送 MQ队列
 */
@Data
public class EmailSystemMQMessageDto {

    @ApiModelProperty(value = "邮件发送器队列Id")
    private Long emailSenderQueueId;

    @ApiModelProperty(value = "邮件标题")
    private String title;

    @ApiModelProperty(value = "邮件正文")
    private String content;

    @ApiModelProperty(value = "邮件接收人")
    private String toEmail;

    @ApiModelProperty(value = "邮件抄送人")
    private String[] ccEmail;

}
