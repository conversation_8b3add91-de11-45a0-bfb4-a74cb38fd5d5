package com.get.permissioncenter.service;

import com.get.common.result.Page;
import com.get.permissioncenter.dto.MInstitutionPermissionGroupDto;
import com.get.permissioncenter.dto.MInstitutionPermissionGroupSearchDto;
import com.get.permissioncenter.vo.MInstitutionPermissionGroupVo;

import java.util.List;

/**
 * 学校权限组service
 */
public interface MInstitutionPermissionGroupService {

    /**
     * 新增
     * @param mInstitutionPermissionGroupDto
     * @return
     */
    Long addMInstitutionPermissionGroup(MInstitutionPermissionGroupDto mInstitutionPermissionGroupDto);


    /**
     * 根据id删除
     * @param id
     */
    void removeById(Long id);


    /**
     * 根据id修改
     * @param mInstitutionPermissionGroupDto
     * @return
     */
    MInstitutionPermissionGroupVo updateById(MInstitutionPermissionGroupDto mInstitutionPermissionGroupDto);

    /**
     * 查询
     * @param data
     * @param page
     * @return
     */
    List<MInstitutionPermissionGroupVo> getInstitutionPermissionGroups(MInstitutionPermissionGroupSearchDto data, Page page);

    void sortInstitutionPermissionGroup(List<MInstitutionPermissionGroupDto> mInstitutionPermissionGroupDtos);

    /**
     * 权限中心/学校权限管理/拖拽
     * @param start
     * @param end
     */
    void movingOrder(Integer start, Integer end);
}

