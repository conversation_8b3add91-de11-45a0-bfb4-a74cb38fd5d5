package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.TableEnum;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.VipConfigVo;
import com.get.salecenter.service.VipConfigService;
import com.get.salecenter.dto.VipConfigDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/11/9
 * @TIME: 15:11
 * @Description:
 **/
@Api(tags = "VIP统计配置")
@RestController
@RequestMapping("sale/vipConfig")
public class VipConfigController {
    @Resource
    private VipConfigService vipConfigService;

    /**
     * @Description:列表数据
     * <AUTHOR>
     * @Date 16:38 2022/11/9
     **/
    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/VIP统计配置/查询列表")
    @PostMapping("datas")
    public ResponseBo<VipConfigVo> datas(@RequestBody SearchBean<VipConfigDto> page){
        List<VipConfigVo> datas = vipConfigService.getVipConfigs(page.getData(),page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo(datas,p);
    }

    /**
     * @Description:新增接口
     * <AUTHOR>
     * @Date 16:39 2022/11/9
     **/
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/VIP统计配置/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody VipConfigDto vipConfigDto){
        vipConfigService.add(vipConfigDto);
        return SaveResponseBo.ok();
    }

   /**
    * @Description:删除接口
    * <AUTHOR>
    * @Date 16:38 2022/11/9
    **/
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/VIP统计配置/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        vipConfigService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @Description:详情接口
     * <AUTHOR>
     * @Date 16:38 2022/11/9
     **/
    @ApiOperation(value = "详情接口",notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER,type = LoggerOptTypeConst.DETAIL,description = "销售中心/VIP统计配置/详情")
    @GetMapping("/{id}")
    public ResponseBo<VipConfigVo> detail(@PathVariable("id") Long id){
        VipConfigVo vipConfigVo = vipConfigService.findVipConfigById(id);
        return new ResponseBo(vipConfigVo);
    }

    /**
     * @Description:修改接口
     * <AUTHOR>
     * @Date 16:38 2022/11/9
     **/
    @ApiOperation(value = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/VIP统计配置/修改")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated(VipConfigDto.Update.class)  VipConfigDto vipConfigDto){
        vipConfigService.updateVipConfig(vipConfigDto);
        return UpdateResponseBo.ok();
    }

    /**
     * @Description:上移下移
     * <AUTHOR>
     * @Date 16:41 2022/11/9
     **/
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/VIP统计配置/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<VipConfigDto> vipConfigDtos) {
        vipConfigService.movingOrder(vipConfigDtos);
        return ResponseBo.ok();
    }

    @ApiOperation("统计项类型下拉")
    @VerifyPermission(IsVerify = false)
    @GetMapping("getVipConfigType")
    public ResponseBo getVipConfigType() {
        return new ListResponseBo<>(TableEnum.enums2Arrays(TableEnum.VIP_CONFIG_TYPE));
    }

}
