package com.get.workflowcenter.controller;

import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * @author: Sea
 * @create: 2020/12/16 16:00
 * @verison: 1.0
 * @description: 跳转编辑页面
 */
@Controller
public class ViewController {

//    /**
//     * 跳转编辑器页面
//     *
//     * @return
//     */
//    @GetMapping("workflow/editor")
//    @VerifyLogin(IsVerify = false)
//    @VerifyPermission(IsVerify = false)
//    public String editor() {
//        return "modeler";
//    }

    /**
     * 跳转编辑器页面
     *
     * @return
     */
//    @GetMapping("workflow/editor")
    @GetMapping("editor/{modelId}")
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    public String editor(@PathVariable("modelId") String modelId) {
        System.out.println("==请求的数据==>" + modelId);
        return "modeler";
    }
}
