package com.get.institutioncenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * ai学校返回类
 */
@Data
public class AiInstitutionInfoVo {
    @ApiModelProperty(value = "学校id")
    private Long id;

    @ApiModelProperty(value = "学校名称")
    private String institutionName;

    @ApiModelProperty(value = "学校中文名")
    private String institutionNameChn;

    @ApiModelProperty(value = "学校类型名")
    private String institutionTypeName;

    @ApiModelProperty(value = "国家")
    private String countryName;

    @ApiModelProperty(value = "州省")
    private String stateName;

}
