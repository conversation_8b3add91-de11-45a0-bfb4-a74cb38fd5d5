package com.get.platformconfigcenter.dao.appissue;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.get.platformconfigcenter.vo.AplOrderVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2021/11/24
 * @TIME: 14:43
 * @Description:
 **/
@Mapper
@DS("issuedb")
public interface AplOrderMapper {
    String getRobotStatusById(@Param("orderId") Long ordreId);
//
    List<AplOrderVo> getRobotStatusByIds(@Param("orderIds") Set<Long> orderIds);
}
