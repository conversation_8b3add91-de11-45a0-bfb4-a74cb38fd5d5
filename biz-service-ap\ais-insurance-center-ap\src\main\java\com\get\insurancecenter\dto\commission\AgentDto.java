package com.get.insurancecenter.dto.commission;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author:<PERSON>
 * @Date: 2025/5/23
 * @Version 1.0
 * @apiNote:
 */
@Data
public class AgentDto {

    @ApiModelProperty(value = "大区ID")
    private Long areaRegionId;

    @ApiModelProperty(value = "国家ID")
    private Long countryId;

    @ApiModelProperty(value = "州省ID")
    private Long areaStateId;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "客户名称")
    private String insurantName;

    @ApiModelProperty(value = "列表类型:0待确认订单列表;1待提交结算订单列表;2-确认结算订单列表;3-已结算订单列表")
    private Integer type;
}
