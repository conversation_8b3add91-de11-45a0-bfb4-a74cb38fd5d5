<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.PaymentFeeTypeMapper">
    <select id="getMaxViewOrder" resultType="java.lang.Integer">
        select IFNULL(max(view_order) + 1, 0) view_order
        from u_payment_fee_type
    </select>

    <select id="selectByFkAccountingItemId" resultType="com.get.financecenter.entity.PaymentFeeType">
        select *
        from u_payment_fee_type
        where fk_accounting_item_id = #{fkAccountingItemId}
    </select>
</mapper>