package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.ProviderMapper;
import com.get.financecenter.dao.ReceiptFeeTypeMapper;
import com.get.financecenter.dao.VouchReceiptRegisterMapper;
import com.get.financecenter.dto.VouchDto;
import com.get.financecenter.dto.VouchItemDto;
import com.get.financecenter.dto.VouchReceiptRegisterDto;
import com.get.financecenter.entity.ReceiptFeeType;
import com.get.financecenter.entity.VouchReceiptRegister;
import com.get.financecenter.enums.ReceiptFeeTypeGroupEnum;
import com.get.financecenter.enums.RelationTargetKeyEnum;
import com.get.financecenter.enums.VouchTypeEnum;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.financecenter.service.IVouchReceiptRegisterService;
import com.get.financecenter.service.VouchService;
import com.get.financecenter.utils.GetAccountingCodeNameUtils;
import com.get.financecenter.utils.RelationTargetProcessorUtils;
import com.get.financecenter.vo.VouchReceiptRegisterVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.feign.ISaleCenterClient;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 *收款方式管理
 */
@Service("financeVouchReceiptRegisterService")
public class VouchReceiptRegisterServiceImpl extends ServiceImpl<VouchReceiptRegisterMapper, VouchReceiptRegister> implements IVouchReceiptRegisterService {

    @Resource
    private VouchReceiptRegisterMapper vouchReceiptRegisterMapper;

    @Resource
    private UtilService utilService;

    @Resource
    private GetAccountingCodeNameUtils  getAccountingCodeNameUtils;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private IFinanceCenterClient financeCenterClient;

    @Resource
    private ISaleCenterClient saleCenterClient;
    @Resource
    private VouchService vouchService;

    @Resource
    private ReceiptFeeTypeMapper receiptFeeTypeMapper;

    @Resource
    private ProviderMapper providerMapper;

    @Resource
    private RelationTargetProcessorUtils relationTargetProcessorUtils;



    @Override
    public List<VouchReceiptRegisterVo> getVouchReceiptRegister(VouchReceiptRegisterDto vouchReceiptRegisterDto, Page page) {
        LambdaQueryWrapper<VouchReceiptRegister> wrapper = new LambdaQueryWrapper();
        IPage<VouchReceiptRegister> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);

        List<VouchReceiptRegisterVo> vouchReceiptRegisters = vouchReceiptRegisterMapper.getVouchReceiptRegister(pages,vouchReceiptRegisterDto);
        page.setAll((int) pages.getTotal());
        //公司名数据
        Map<Long, String> companyNamesByIds = new HashMap<>();
        //部门名称数据
        Map<Long, String> departmentByIds = new HashMap<>();
        //币种名数据
        Map<String, String> currencyNamesByNums = new HashMap<>();

        if (GeneralTool.isNotEmpty(vouchReceiptRegisters)){
            //公司ids
            Set<Long> companyIds = vouchReceiptRegisters.stream().map(VouchReceiptRegisterVo::getFkCompanyId).filter(Objects::nonNull).collect(Collectors.toSet());
            //部门ids
            Set<Long> departmentIds = vouchReceiptRegisters.stream().map(VouchReceiptRegisterVo::getFkDepartmentId).filter(Objects::nonNull).collect(Collectors.toSet());
            //币种nums
            Set<String> currencyTypeNamesByNums = vouchReceiptRegisters.stream().map(VouchReceiptRegisterVo::getFkCurrencyTypeNum).filter(Objects::nonNull).collect(Collectors.toSet());

            Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNamesByNums);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                currencyNamesByNums = result.getData();
            }

            Result<Map<Long, String>> result1 = permissionCenterClient.getCompanyNamesByIds(companyIds);
            if (result1.isSuccess() && GeneralTool.isNotEmpty(result1.getData())) {
                companyNamesByIds = result1.getData();
            }


            Result<Map<Long, String>> result2 = permissionCenterClient.getDepartmentNamesByIds(departmentIds);
            if (result2.isSuccess() && GeneralTool.isNotEmpty(result2.getData())) {
                departmentByIds = result2.getData();
            }

        }

      for (VouchReceiptRegisterVo vouchReceiptRegisterVo : vouchReceiptRegisters) {
          vouchReceiptRegisterVo.setAccountingItemName(getAccountingCodeNameUtils.setAccountingCodeName(vouchReceiptRegisterVo.getFkAccountingItemIdReceiptMethod()));

          if (GeneralTool.isNotEmpty(vouchReceiptRegisterVo.getFkCompanyId())){
              vouchReceiptRegisterVo.setCompanyName(companyNamesByIds.get(vouchReceiptRegisterVo.getFkCompanyId()));
          }
          if (GeneralTool.isNotEmpty(vouchReceiptRegisterVo.getFkDepartmentId())){
              vouchReceiptRegisterVo.setDepartmentName(departmentByIds.get(vouchReceiptRegisterVo.getFkDepartmentId()));
          }
          if(GeneralTool.isNotEmpty(vouchReceiptRegisterVo.getFkCurrencyTypeNum())){
              vouchReceiptRegisterVo.setCurrencyTypeName(currencyNamesByNums.get(vouchReceiptRegisterVo.getFkCurrencyTypeNum()));
          }

          if (GeneralTool.isNotEmpty(vouchReceiptRegisterVo.getReceiptAmount())){
              vouchReceiptRegisterVo.setReceiptAmount(vouchReceiptRegisterVo.getReceiptAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
          }
          if (GeneralTool.isNotEmpty(vouchReceiptRegisterVo.getRelationTargetKeyReceiptMethod())){
              vouchReceiptRegisterVo.setRelationTargetKeyReceiptMethodName(RelationTargetKeyEnum.getNameByRelationTargetKey(vouchReceiptRegisterVo.getRelationTargetKeyReceiptMethod()));
//              if (vouchReceiptRegisterVo.getRelationTargetKeyReceiptMethod().equals(RelationTargetKeyEnum.STAFF.relationTargetKey)) {
//                  vouchReceiptRegisterVo.setRelationTargetIdReceiptMethodName(permissionCenterClient.getStaffName(vouchReceiptRegisterVo.getRelationTargetIdReceiptMethod()).getData());
//              } else if (vouchReceiptRegisterVo.getRelationTargetKeyReceiptMethod().equals(RelationTargetKeyEnum.PROVIDER.relationTargetKey)) {
//                  Provider provider = providerMapper.selectById(vouchReceiptRegisterVo.getRelationTargetIdReceiptMethod());
//                  vouchReceiptRegisterVo.setRelationTargetIdReceiptMethodName(provider.getName());
//              } else if (vouchReceiptRegisterVo.getRelationTargetKeyReceiptMethod().equals(RelationTargetKeyEnum.STUDENT.relationTargetKey)) {
//                  Student student = saleCenterClient.getStudentById(vouchReceiptRegisterVo.getRelationTargetIdReceiptMethod()).getData();
//                  vouchReceiptRegisterVo.setRelationTargetIdReceiptMethodName(student.getName());
//              }else if (vouchReceiptRegisterVo.getRelationTargetKeyReceiptMethod().equals(RelationTargetKeyEnum.STUDENT_SERVICE_FEE.relationTargetKey)) {
//                  vouchReceiptRegisterVo.setRelationTargetIdReceiptMethodName(saleCenterClient.getServiceFeeNumById(vouchReceiptRegisterVo.getRelationTargetIdReceiptMethod()).getData());
//              }else {
//                  vouchReceiptRegisterVo.setRelationTargetIdReceiptMethodName(null);
//              }
              relationTargetProcessorUtils.processRelationTarget(
                      vouchReceiptRegisterVo.getRelationTargetKeyReceiptMethod(),
                      vouchReceiptRegisterVo.getRelationTargetIdReceiptMethod(),
                      name -> vouchReceiptRegisterVo.setRelationTargetIdReceiptMethodName(name),
                      companyId -> {
                          vouchReceiptRegisterVo.setRelationTargetDrCompanyId(companyId);
                      },null);
          }
          if (GeneralTool.isNotEmpty(vouchReceiptRegisterVo.getRelationTargetKeyReceiptFee())){
              vouchReceiptRegisterVo.setRelationTargetKeyReceiptFeeName(RelationTargetKeyEnum.getNameByRelationTargetKey(vouchReceiptRegisterVo.getRelationTargetKeyReceiptFee()));

//              if (vouchReceiptRegisterVo.getRelationTargetKeyReceiptFee().equals(RelationTargetKeyEnum.STAFF.relationTargetKey)) {
//                  vouchReceiptRegisterVo.setRelationTargetIdReceiptFeeName(permissionCenterClient.getStaffName(vouchReceiptRegisterVo.getRelationTargetIdReceiptFee()).getData());
//              } else if (vouchReceiptRegisterVo.getRelationTargetKeyReceiptFee().equals(RelationTargetKeyEnum.PROVIDER.relationTargetKey)) {
//                  Provider provider = providerMapper.selectById(vouchReceiptRegisterVo.getRelationTargetIdReceiptFee());
//                  vouchReceiptRegisterVo.setRelationTargetIdReceiptFeeName(provider.getName());
//              } else if (vouchReceiptRegisterVo.getRelationTargetKeyReceiptFee().equals(RelationTargetKeyEnum.STUDENT.relationTargetKey)) {
//                  Student student = saleCenterClient.getStudentById(vouchReceiptRegisterVo.getRelationTargetIdReceiptFee()).getData();
//                  vouchReceiptRegisterVo.setRelationTargetIdReceiptFeeName(student.getName());
//              }else if (vouchReceiptRegisterVo.getRelationTargetKeyReceiptFee().equals(RelationTargetKeyEnum.STUDENT_SERVICE_FEE.relationTargetKey)) {
//                  vouchReceiptRegisterVo.setRelationTargetIdReceiptFeeName(saleCenterClient.getServiceFeeNumById(vouchReceiptRegisterVo.getRelationTargetIdReceiptFee()).getData());
//              }else {
//                  vouchReceiptRegisterVo.setRelationTargetIdReceiptFeeName(null);
//              }
              relationTargetProcessorUtils.processRelationTarget(
                      vouchReceiptRegisterVo.getRelationTargetKeyReceiptFee(),
                      vouchReceiptRegisterVo.getRelationTargetIdReceiptFee(),
                      name -> vouchReceiptRegisterVo.setRelationTargetIdReceiptFeeName(name),
                      companyId -> {
                          vouchReceiptRegisterVo.setRelationTargetCrCompanyId(companyId);
                      },null);
          }


      }
      return vouchReceiptRegisters;

    }

    //收款记录是否激活
    private static final int ACTIVE_STATE = 1;
    private static final int FAILURE_STATE = 0;
    @Override
    public void add(VouchReceiptRegisterDto vouchReceiptRegisterDto) {
        verifyParameters(vouchReceiptRegisterDto);
        // 添加凭证信息
//        1	现金 现收
//        2	银行转帐 银收
//        3	已付定金 转
        VouchDto vouchDto = new VouchDto();
        if (vouchReceiptRegisterDto.getFkReceiptMethodTypeId() == 1) {
            vouchDto.setVouchType(VouchTypeEnum.CASH_RECEIPT.getCodeName());
        }else if (vouchReceiptRegisterDto.getFkReceiptMethodTypeId() == 2) {
            vouchDto.setVouchType(VouchTypeEnum.BANK_RECEIPT.getCodeName());
        }else if (vouchReceiptRegisterDto.getFkReceiptMethodTypeId() == 3) {
            vouchDto.setVouchType(VouchTypeEnum.TRANSFER.getCodeName());
        }else {
            vouchDto.setVouchType(VouchTypeEnum.TRANSFER.getCodeName());
        }
        //凭证参数处理
        ReceiptFeeType receiptFeeType = receiptFeeTypeMapper.selectById(vouchReceiptRegisterDto.getFkReceiptFeeTypeId());
        String summary = "";
        if (GeneralTool.isNotEmpty(receiptFeeType.getTypeGroupKey())){
            HashMap<String, String> summaryMap = new HashMap<>();
            //员工类型拼接
            if (receiptFeeType.getTypeGroupKey().equals(ReceiptFeeTypeGroupEnum.STAFF.getTypeGroupKey())){
                //创建核销借款收款凭证，员工（{staff_name}）{receipt_summary}
                summaryMap.put("staff_name", permissionCenterClient.getStaffName(vouchReceiptRegisterDto.getRelationTargetIdReceiptFee()).getData());

            }

//            //提供商类型拼接
//            else if (receiptFeeType.getTypeGroupKey().equals(ReceiptFeeTypeGroupEnum.PROVIDER.getTypeGroupKey())){}
//            //学生类型拼接
//            else if (receiptFeeType.getTypeGroupKey().equals(ReceiptFeeTypeGroupEnum.STUDENT.getTypeGroupKey())){}


            //学生服务费类型拼接
            else if (receiptFeeType.getTypeGroupKey().equals(ReceiptFeeTypeGroupEnum.STUDENT_SERVICE_FEE.getTypeGroupKey())){
//                创建代收-签证服务费-收款凭证，服务费编号（{student_service_fee_num}）{receipt_summary}
                summaryMap.put("student_service_fee_num", saleCenterClient.getServiceFeeInfoById(vouchReceiptRegisterDto.getRelationTargetIdReceiptFee()).getData().getServiceFeeNum());

            }
            //共同规则
            summaryMap.put("receipt_summary", vouchReceiptRegisterDto.getSummary());
            summary = replaceNamedPlaceholders(receiptFeeType.getVouchSummary(), summaryMap);
        }
        vouchDto.setBusinessDate(vouchReceiptRegisterDto.getReceiptDate());
        vouchDto.setRemark(summary +"; "+vouchReceiptRegisterDto.getSummary());
        vouchDto.setFkCompanyId(vouchReceiptRegisterDto.getFkCompanyId());
        List<VouchItemDto> vouchItemDtos = new ArrayList<>();
        //收款凭证明细  借方
        VouchItemDto vouchItemDtoDr = new VouchItemDto();
        vouchItemDtoDr.setFkAccountingItemId(vouchReceiptRegisterDto.getFkAccountingItemIdReceiptMethod());
        vouchItemDtoDr.setRelationTargetKey(vouchReceiptRegisterDto.getRelationTargetKeyReceiptMethod());
        vouchItemDtoDr.setRelationTargetId(vouchReceiptRegisterDto.getRelationTargetIdReceiptMethod());
        vouchItemDtoDr.setFkCurrencyTypeNum(vouchReceiptRegisterDto.getFkCurrencyTypeNum());
        vouchItemDtoDr.setAmountDr(vouchReceiptRegisterDto.getReceiptAmount());


        vouchItemDtos.add(vouchItemDtoDr);

//        贷
        VouchItemDto vouchItemDtoCr = new VouchItemDto();
        if (GeneralTool.isNotEmpty(receiptFeeType)){
            vouchItemDtoCr.setFkAccountingItemId(receiptFeeType.getFkAccountingItemId());
        }

        vouchItemDtoCr.setAmountCr(vouchReceiptRegisterDto.getReceiptAmount());
        vouchItemDtoCr.setRelationTargetKey(vouchReceiptRegisterDto.getRelationTargetKeyReceiptFee());
        vouchItemDtoCr.setRelationTargetId(vouchReceiptRegisterDto.getRelationTargetIdReceiptFee());
        vouchItemDtos.add(vouchItemDtoCr);
        vouchDto.setVouchItemList(vouchItemDtos);
        Long vouchId = vouchService.add(vouchDto);


        //添加
        VouchReceiptRegister vouchReceiptRegister = BeanCopyUtils.objClone(vouchReceiptRegisterDto, VouchReceiptRegister::new);
        utilService.setCreateInfo(vouchReceiptRegister);
        //创建时，默认为激活
        vouchReceiptRegister.setStatus(ACTIVE_STATE);
        //添加凭证id
        vouchReceiptRegister.setFkVouchId(vouchId);
        int insert = vouchReceiptRegisterMapper.insert(vouchReceiptRegister);
        if (insert <= 0){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }




    }

    /**
     * 替换字符串中的 {key} 占位符（无 $ 符号）
     * @param template 模板字符串（含 {key} 占位符）
     * @param params 键值对参数（key 对应占位符名称，value 为替换值）
     * @return 替换后的字符串
     */
    public static String replaceNamedPlaceholders(String template, Map<String, String> params) {
        // 正则匹配 {key} 格式的占位符（key 由字母、数字、下划线组成）
        Pattern pattern = Pattern.compile("\\{([a-zA-Z0-9_]+)\\}");
        Matcher matcher = pattern.matcher(template);
        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            String placeholderKey = matcher.group(1);
            String replacement = params.getOrDefault(placeholderKey, "");
            // 转义替换值中的特殊字符（如 $、\），避免正则替换出错
            matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(result);
        return result.toString();
    }


    private static void verifyParameters(VouchReceiptRegisterDto vouchReceiptRegisterDto) {
        if (GeneralTool.isEmpty(vouchReceiptRegisterDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (GeneralTool.isEmpty(vouchReceiptRegisterDto.getFkAccountingItemIdReceiptMethod())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("accounting_item_id_null"));
        }
        if (GeneralTool.isEmpty(vouchReceiptRegisterDto.getFkReceiptMethodTypeId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("receipt_method_type_id_null"));
        }
        if (GeneralTool.isEmpty(vouchReceiptRegisterDto.getReceiptAmount())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("receipt_amount_null"));
        }
        if (vouchReceiptRegisterDto.getReceiptAmount().compareTo(BigDecimal.ZERO) <= 0){
            throw new GetServiceException(LocaleMessageUtils.getMessage("receipt_amount_must_be_greater_than_zero"));
        }
        if (GeneralTool.isEmpty(vouchReceiptRegisterDto.getReceiptDate())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("receipt_date_null"));
        }
    }

    @Override
    public void updateById(VouchReceiptRegisterDto vouchReceiptRegisterDto) {
        if (GeneralTool.isEmpty(vouchReceiptRegisterDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(vouchReceiptRegisterDto.getId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (GeneralTool.isEmpty(vouchReceiptRegisterMapper.selectById(vouchReceiptRegisterDto.getId()))){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        VouchReceiptRegister vouchReceiptRegister = BeanCopyUtils.objClone(vouchReceiptRegisterDto, VouchReceiptRegister::new);
        //作废
        vouchReceiptRegister.setStatus(FAILURE_STATE);
        utilService.setUpdateInfo(vouchReceiptRegister);

        LambdaUpdateWrapper<VouchReceiptRegister> vouchReceiptRegisterLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        vouchReceiptRegisterLambdaUpdateWrapper.eq(VouchReceiptRegister::getId,vouchReceiptRegisterDto.getId());
        vouchReceiptRegisterLambdaUpdateWrapper.set(VouchReceiptRegister::getStatus,FAILURE_STATE);
        vouchReceiptRegisterLambdaUpdateWrapper.set(VouchReceiptRegister::getGmtModified,vouchReceiptRegister.getGmtModified());
        vouchReceiptRegisterLambdaUpdateWrapper.set(VouchReceiptRegister::getGmtModifiedUser,vouchReceiptRegister.getGmtModifiedUser());

        int update = vouchReceiptRegisterMapper.update(vouchReceiptRegister, vouchReceiptRegisterLambdaUpdateWrapper);
        if (update <= 0){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        //修改凭证为作废状态
        vouchService.updateByVouchId(vouchReceiptRegisterDto.getFkVouchId());
    }


}


