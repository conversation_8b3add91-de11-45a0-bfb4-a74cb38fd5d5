package com.get.institutioncenter.dto.query;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ContractQueryDto  extends BaseVoEntity implements Serializable {

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "查询条件公司Id")
    private Long fkCompanyId;

    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型", required = true)
    @NotNull(message = "合同类型不能为空", groups = {Add.class, Update.class})
    private Long fkContractTypeId;

    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    private Long fkCountryId;

    /**
     * 学校课程Id
     */
    @ApiModelProperty(value = "学校课程Id")
    private Long fkInstitutionCourseId;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

    /**
     * 学校提供商Id
     */
    @ApiModelProperty(value = "学校提供商Id", required = true)
    @NotNull(message = "学校提供商Id", groups = {Add.class, Update.class})
    private Long fkInstitutionProviderId;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是", required = true)
    @NotNull(message = "是否激活不能为空", groups = {Add.class, Update.class})
    private Boolean isActive;

    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyWord;

    /**
     * 流程key
     */
    @ApiModelProperty("流程key")
    private String procdkey;

    /**
     * 1查询全部，0查询个人，2我的审批
     */
    @ApiModelProperty(value = "1查询全部，0查询个人，2我的审批")
    @NotNull(message = "查询状态不能为空")
    private Integer selectStatus;


    //============新增属性=================
    @ApiModelProperty("申请周期，如：1-2周")
    private String applicationCycle;

    @ApiModelProperty("结算周期，如：8-12周")
    private String settlementCycle;

    @ApiModelProperty(value = "合同编号", required = true)
//    @NotBlank(message = "合同编号不能为空", groups = {Update.class})
    private String contractNum;


}
