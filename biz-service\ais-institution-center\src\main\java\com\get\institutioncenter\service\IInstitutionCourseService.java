package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.vo.*;
import com.get.institutioncenter.entity.InstitutionCourse;
import com.get.institutioncenter.dto.*;
import com.get.institutioncenter.dto.query.InstitutionCourseQueryDto;
import com.get.institutioncenter.dto.query.NewsQueryDto;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/8/10
 * @TIME: 10:02
 * @Description:课程业务类
 **/
public interface IInstitutionCourseService extends BaseService<InstitutionCourse> {
    /**
     * 列表数据
     *
     * @param institutionCourseVo
     * @param page
     * @return
     */
    List<InstitutionCourseVo> datas(InstitutionCourseQueryDto institutionCourseVo, Page page);

    /**
     * 保存
     *
     * @param institutionCourseDto
     * @return
     */
    Long addInstitutionCourse(InstitutionCourseDto institutionCourseDto);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    InstitutionCourseVo findInstitutionCourseById(Long id);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 修改
     *
     * @param institutionCourseDto
     * @return
     */
    InstitutionCourseVo updateInstitutionCourse(InstitutionCourseDto institutionCourseDto);


    /**
     * @return java.lang.Long
     * @Description: 根据学校课程查询课程数
     * @Param [institutionId]
     * <AUTHOR>
     */
    Integer getCourseCountByInstitutionId(Long institutionId);


    /**
     * 保存附件
     *
     * @param mediaAttachedVo
     * @return
     */
    List<MediaAndAttachedVo> addInstitutionCourseMedia(List<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * 获取附件类型
     *
     * @return
     */
    List<Map<String, Object>> findMediaAndAttachedType();

    /**
     * @return java.util.List<com.get.institutioncenter.vo.InstitutionCourseVo>
     * @Description:获取课程下拉框
     * @Param [institutionId]
     * <AUTHOR>
     **/
    List<BaseSelectEntity> getInstitutionCourseByInstitution(Long institutionId);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description :多个学校所有的课程下拉框数据
     * @Param [institutionIdList]
     * <AUTHOR>
     */
    List<BaseSelectEntity> getInstitutionCourseByInstitutionIdList(List<Long> institutionIdList);

    /**
     * 根据id获取课程
     *
     * @param id
     * @return
     */
    InstitutionCourseVo getCourseById(Long id);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 模糊查询
     * @Param [name]
     * <AUTHOR>
     */
    List<Long> getCourseIds(String name);


    /**
     * 根据id获取课程名称
     *
     * @param id
     * @return
     */
    String getCourseNameById(Long id);

    /**
     * 根据ids获取课程名称
     *
     * @param ids
     * @return
     */
    Map<Long, String> getCourseNameByIds(Set<Long> ids);

    /**
     * 根据id获取课程名称
     *
     * @param id
     * @return
     */
    String getCourseNameChnById(Long id);

    /**
     * 根据ids获取课程名称
     *
     * @param ids
     * @return
     */
    Map<Long, String> getCourseNameChnByIds(Set<Long> ids);

    /**
     * @return java.lang.Long
     * @Description: 添加新闻
     * @Param [newsDto]
     * <AUTHOR>
     */
    Long addNews(NewsDto newsDto);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.NewsVo>
     * @Description: 查询新闻
     * @Param [data, page]
     * <AUTHOR>
     */
    List<NewsVo> getNewsData(NewsQueryDto data, Page page);

    /**
     * 查询课程附件
     *
     * @param data
     * @param page
     * @return
     * @throws
     */
    List<MediaAndAttachedVo> getCourseMedia(MediaAndAttachedDto data, Page page);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.InstitutionCourseEngScoreVo>
     * @Description: 英语成绩列表数据
     * @Param [data, page]
     * <AUTHOR>
     **/
    List<InstitutionCourseEngScoreVo> getEngScoreDatas(InstitutionCourseEngScoreDto data, Page page);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.InstitutionCourseAcademicScoreVo>
     * @Description: 学术成绩列表数据
     * @Param [data, page]
     * <AUTHOR>
     **/
    List<InstitutionCourseAcademicScoreVo> getAcademicScoreDatas(InstitutionCourseAcademicScoreDto data, Page page);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.AppInfoVo>
     * @Description: 申请资料列表数据
     * @Param [data, page]
     * <AUTHOR>
     **/
    List<AppInfoVo> getAppInfoDatas(AppInfoDto data, Page page);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.AppInfoVo>
     * @Description: 排名列表数据
     * @Param [data, page]
     * <AUTHOR>
     **/
    List<InstitutionCourseRankingVo> getInstitutionCourseRankingDatas(InstitutionCourseRankingDto data, Page page);

    /**
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Description: 表名下拉
     * @Param []
     * <AUTHOR>
     */
    List<Map<String, Object>> findTargetType();

    /**
     * 目标类型获取目标
     *
     * @return
     */
    List<BaseSelectEntity> findTarget(String tableName);

    /**
     * 保存
     *
     * @param appInfoDtos
     * @return
     */
    void batchAdd(List<AppInfoDto> appInfoDtos);

    /**
     * 保存
     *
     * @param istitutionCourseRankingVos
     * @return
     */
    void batchAddInstitutionCourseRanking(List<InstitutionCourseRankingDto> istitutionCourseRankingVos);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 桥梁课程下拉框数据
     * @Param [institutionId]
     * <AUTHOR>
     **/
    List<BaseSelectEntity> getInstitutionCoursePathwayByInstitution(InstitutionCourseDto institutionCourseDto);

    /**
     * 非桥梁课程下拉框数据
     *
     * @Date 17:36 2021/7/22
     * <AUTHOR>
     */
    List<BaseSelectEntity> getNonBridgeInstitutionCoursePathwayByInstitution(InstitutionCourseDto institutionCourseDto);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.AppInfoVo>
     * @Description: 学校特性列表数据
     * @Param [data, page]
     * <AUTHOR>
     **/
    List<CharacterVo> getCharacterDatas(CharacterDto data, Page page);

    /**
     * 保存
     *
     * @param characterDtos
     * @return
     */
    void batchAddCharacter(List<CharacterDto> characterDtos);

    /**
     * @Description :根据课程id查詢學費
     * @Param [institutionCourseIdSet]
     * <AUTHOR>
     */
    BigDecimal getFeeById(Long id);

    /**
     * @Description :根据课程ids查詢學費
     * @Param [institutionCourseIdSet]
     * <AUTHOR>
     */
    BigDecimal getSumFeeByIds(List<Long> ids);

    /**
     * @Description :根据课程ids 查找对应课程名称map
     * @Param [institutionCourseIdSet]
     * <AUTHOR>
     */
    Map<Long, String> getInstitutionCourseNamesByIds(Set<Long> institutionCourseIdSet);

    /**
     * feign调用 根据课程ids查找对应课程名称
     *
     * @Date 2:21 2021/6/19
     * <AUTHOR>
     */
    String getInstitutionCourseNameById(Long institutionCourseId);

    /**
     * feign调用 通过自定义课程ids 查找对应的自定义课程名称
     *
     * @param ids
     * @return
     */
    Map<Long, String> getCourseNameByCustomIds(Set<Long> ids);

    /**
     * feign调用  更新所有课程的fee_cny费率
     *
     * @param updateType
     */
    Boolean updateCoursefeeCny(String updateType);

    /**
     * 检查获取课程链接
     *
     * @Date 18:50 2022/2/28
     * <AUTHOR>
     */
    Long checkCourseWebsite(Long fkInstitutionId, String courseWebsite);

    /**
     * 根据输入关键字模糊查询课程列表
     *
     * @Date 2:20 2022/03/11
     * <AUTHOR>
     */
    List<Long> getInstitutionCourseByName(String courseName,Long institutionId);

    /**
     * 根据级别名称模糊查询课程列表
     *
     * @Date 2:20 2022/03/11
     * <AUTHOR>
     */
    List<Long> getInstitutionCourseIdsByLevelAndName(String name, Long levelId);

    /**
     * 获取课程下拉
     * @param keyword
     * @param institutionId
     * @return
     */
    List<BaseSelectEntity> getCourseSelected(String keyword,Long institutionId);

    List<InstitutionCourseMatchVo> getInstitutionCourseByNameMatch(String courseName, Long institutionId);

    List<BaseSelectEntity> InstitutionCourseListByName(String courseName, List<Long> institutionId, List<Long> courseIds);

    public List<CaseStudyResultsDto.Statistics> getCourseStatic(Set<Long> courseIds);

    List<BaseSelectEntity> getInstitutionCourseList(InstitutionCourseNameSearchDto institutionCourseNameSearchDto);

    List<BaseSelectEntity> getInstitutionCourseListByIds(InstitutionCourseNameSearchDto institutionCourseNameSearchDto);

    /**
     * 科目列表
     * @param institutionCourseSubjectDto
     * @param page
     * @return
     */
    List<InstitutionCourseSubjectVo> getInstitutionCourseSubjectDatas(InstitutionCourseSubjectDto institutionCourseSubjectDto, Page page);
    /**
     * 根据课程ids获取英文课程名map
     */
    Map<Long, String> getCourseEnNameByIds(Set<Long> courseIds);
}
