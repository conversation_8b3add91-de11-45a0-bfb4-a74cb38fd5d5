package com.get.salecenter.service.impl;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.EventPlanThemeWorkshopMapper;
import com.get.salecenter.vo.EventPlanThemeWorkshopVo;
import com.get.salecenter.entity.EventPlanRegistration;
import com.get.salecenter.entity.EventPlanRegistrationEvent;
import com.get.salecenter.entity.EventPlanTheme;
import com.get.salecenter.entity.EventPlanThemeWorkshop;
import com.get.salecenter.service.EventPlanRegistrationEventService;
import com.get.salecenter.service.EventPlanRegistrationService;
import com.get.salecenter.service.EventPlanThemeService;
import com.get.salecenter.service.EventPlanThemeWorkshopService;
import com.get.salecenter.dto.EventPlanThemeWorkshopDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
@Service
public class EventPlanThemeWorkshopServiceImpl extends BaseServiceImpl<EventPlanThemeWorkshopMapper, EventPlanThemeWorkshop> implements EventPlanThemeWorkshopService {

    @Resource
    private EventPlanThemeWorkshopMapper eventPlanThemeWorkshopMapper;

    @Resource
    private EventPlanThemeService eventPlanThemeService;

    @Resource
    private EventPlanRegistrationService eventPlanRegistrationService;

    @Resource
    private EventPlanRegistrationEventService eventService;

    @Resource
    private UtilService utilService;

    @Override
    public List<EventPlanThemeWorkshopVo> getEventPlanThemeWorkshops(Long fkEventPlanId) {
        if(GeneralTool.isEmpty(fkEventPlanId)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }

        List<EventPlanThemeWorkshopVo> eventPlanThemeWorkshopVos = eventPlanThemeWorkshopMapper.getEventPlanThemeWorkshops(fkEventPlanId, ProjectExtraEnum.EVENT_PLAN_THEME_WORKSHOP.key);

        if(GeneralTool.isEmpty(eventPlanThemeWorkshopVos)){
            return Collections.emptyList();
        }

        //报名名册
        List<EventPlanRegistration> registrations = eventPlanRegistrationService.list(Wrappers.<EventPlanRegistration>lambdaQuery()
                .eq(EventPlanRegistration::getFkEventPlanId, fkEventPlanId));
        List<EventPlanRegistrationEvent> registrationlist = new ArrayList<>();
        if(GeneralTool.isNotEmpty(registrations)){
            Set<Long> fkRegistrationIds = registrations.stream().map(EventPlanRegistration::getId).collect(Collectors.toSet());
            List<Long> workshopIds = eventPlanThemeWorkshopVos.stream().map(EventPlanThemeWorkshopVo::getId).collect(Collectors.toList());
            registrationlist = eventService.list(Wrappers.<EventPlanRegistrationEvent>lambdaQuery()
                    .in(EventPlanRegistrationEvent::getFkEventPlanRegistrationId,fkRegistrationIds)
                    .eq(EventPlanRegistrationEvent::getFkTableName, TableEnum.EVENT_PLAN_THEME_WORKSHOP.key)
                    .in(EventPlanRegistrationEvent::getFkTableId,workshopIds));
        }
        //报名名册合计
        for(EventPlanThemeWorkshopVo dto : eventPlanThemeWorkshopVos){
            List<EventPlanRegistrationEvent> regList = registrationlist.stream().filter(r -> dto.getId().equals(r.getFkTableId())).collect(Collectors.toList());
            dto.setRegistrationCount(regList.size());
        }

        return eventPlanThemeWorkshopVos;
    }

    @Override
    public List<EventPlanThemeWorkshopVo> getWorkshopsByThemeId(Long fkEventPlanThemeId) {
        if(GeneralTool.isEmpty(fkEventPlanThemeId)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        EventPlanTheme theme = eventPlanThemeService.getById(fkEventPlanThemeId);

        List<EventPlanThemeWorkshop> eventPlanThemeWorkshops = eventPlanThemeWorkshopMapper.selectList(Wrappers.<EventPlanThemeWorkshop>lambdaQuery()
                .eq(EventPlanThemeWorkshop::getFkEventPlanThemeId, fkEventPlanThemeId)
                .orderByDesc(EventPlanThemeWorkshop::getViewOrder));
        if(GeneralTool.isEmpty(eventPlanThemeWorkshops)){
            return Collections.emptyList();
        }
        List<EventPlanThemeWorkshopVo> eventPlanThemeWorkshopVos = BeanCopyUtils.copyListProperties(eventPlanThemeWorkshops, EventPlanThemeWorkshopVo::new);
        eventPlanThemeWorkshopVos.forEach(e->e.setMainTitle(theme.getMainTitle()));
        return eventPlanThemeWorkshopVos;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void activate(EventPlanThemeWorkshopDto workshopVo){
        if (GeneralTool.isEmpty(workshopVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        EventPlanThemeWorkshop workshop = BeanCopyUtils.objClone(workshopVo, EventPlanThemeWorkshop::new);
        utilService.updateUserInfoToEntity(workshop);
        eventPlanThemeWorkshopMapper.updateById(workshop);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(ValidList<EventPlanThemeWorkshopDto> workshopVos) {
        if (GeneralTool.isEmpty(workshopVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (EventPlanThemeWorkshopDto workshopVo : workshopVos) {

            if (GeneralTool.isEmpty(workshopVo.getId())) {
                EventPlanThemeWorkshop workshop = BeanCopyUtils.objClone(workshopVo, EventPlanThemeWorkshop::new);
                Integer maxViewOrder = eventPlanThemeWorkshopMapper.getMaxViewOrder(workshopVo.getFkEventPlanThemeId());
                maxViewOrder = GeneralTool.isEmpty(maxViewOrder) ? 0 : maxViewOrder;
                workshop.setViewOrder(maxViewOrder);
                utilService.updateUserInfoToEntity(workshop);
                eventPlanThemeWorkshopMapper.insert(workshop);
            } else {
                EventPlanThemeWorkshop workshop = BeanCopyUtils.objClone(workshopVo, EventPlanThemeWorkshop::new);
                utilService.updateUserInfoToEntity(workshop);
                eventPlanThemeWorkshopMapper.updateById(workshop);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        EventPlanThemeWorkshop workshop = eventPlanThemeWorkshopMapper.selectById(id);
        if (GeneralTool.isEmpty(workshop)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //存在注册不能删除
        List<EventPlanRegistrationEvent> registrations = eventService.list(Wrappers.<EventPlanRegistrationEvent>lambdaQuery()
                .eq(EventPlanRegistrationEvent::getFkTableName, TableEnum.EVENT_PLAN_THEME_WORKSHOP.key)
                .in(EventPlanRegistrationEvent::getFkTableId, id));

        if(GeneralTool.isNotEmpty(registrations)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_workshop_fail"));
        }

        int delete = eventPlanThemeWorkshopMapper.deleteById(id);
        if (delete <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void movingOrder( Long fkEventPlanThemeId , Integer start,Integer end) {
        LambdaQueryWrapper<EventPlanThemeWorkshop> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (end > start){
            lambdaQueryWrapper.between(EventPlanThemeWorkshop::getViewOrder,start,end).orderByDesc(EventPlanThemeWorkshop::getViewOrder);
        }else {
            lambdaQueryWrapper.between(EventPlanThemeWorkshop::getViewOrder,end,start).orderByDesc(EventPlanThemeWorkshop::getViewOrder);

        }
        lambdaQueryWrapper.eq(EventPlanThemeWorkshop::getFkEventPlanThemeId,fkEventPlanThemeId);
        List<EventPlanThemeWorkshop> eventPlanThemeWorkshops = list(lambdaQueryWrapper);

        //从下上移 列表倒序排序
        List<EventPlanThemeWorkshop> updateList = Lists.newArrayList();
        if (end>start){
            int finalEnd = end;
            List<EventPlanThemeWorkshop> sortedList = Lists.newArrayList();
            EventPlanThemeWorkshop workshop = eventPlanThemeWorkshops.get(eventPlanThemeWorkshops.size() - 1);
            sortedList.add(workshop);
            eventPlanThemeWorkshops.remove(eventPlanThemeWorkshops.size() - 1);
            sortedList.addAll(eventPlanThemeWorkshops);
            for (EventPlanThemeWorkshop themeWorkshop : sortedList) {
                themeWorkshop.setViewOrder(finalEnd);
                finalEnd--;
            }
            updateList.addAll(sortedList);
        }else {
            int finalStart = start;
            List<EventPlanThemeWorkshop> sortedList = Lists.newArrayList();
            EventPlanThemeWorkshop online = eventPlanThemeWorkshops.get(0);
            eventPlanThemeWorkshops.remove(0);
            sortedList.addAll(eventPlanThemeWorkshops);
            sortedList.add(online);
            for (EventPlanThemeWorkshop themeWorkshop : sortedList) {
                themeWorkshop.setViewOrder(finalStart);
                finalStart--;
            }
            updateList.addAll(sortedList);
        }

        if (GeneralTool.isNotEmpty(updateList)){
            boolean batch = updateBatchById(updateList);
            if (!batch){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
    }
}
