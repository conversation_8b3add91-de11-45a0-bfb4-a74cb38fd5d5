<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.examcenter.mapper.exam.ScoreTitleMapper">
  <insert id="insert" parameterType="com.get.examcenter.entity.ScoreTitle"  keyProperty="id" useGeneratedKeys="true">
    insert into u_score_title (id, title, score_min, 
      score_max, ranking_min, ranking_max, color_code, remark, view_order,
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{title,jdbcType=VARCHAR}, #{scoreMin,jdbcType=INTEGER}, 
      #{scoreMax,jdbcType=INTEGER}, #{rankingMin,jdbcType=INTEGER}, #{rankingMax,jdbcType=INTEGER},
      #{colorCode,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{viewOrder,jdbcType=VARCHAR},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP},
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.examcenter.entity.ScoreTitle"  keyProperty="id" useGeneratedKeys="true">
    insert into u_score_title
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="scoreMin != null">
        score_min,
      </if>
      <if test="scoreMax != null">
        score_max,
      </if>
      <if test="rankingMin != null">
        ranking_min,
      </if>
      <if test="rankingMax != null">
        ranking_max,
      </if>
      <if test="colorCode != null">
        color_code,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
      <if test="fkExaminationId != null">
        fk_examination_id,
      </if>
        <if test="fkCompanyId != null">
            fk_company_id,
        </if>
      <if test="paramJson != null">
        param_json,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="scoreMin != null">
        #{scoreMin,jdbcType=INTEGER},
      </if>
      <if test="scoreMax != null">
        #{scoreMax,jdbcType=INTEGER},
      </if>
      <if test="rankingMin != null">
        #{rankingMin,jdbcType=INTEGER},
      </if>
      <if test="rankingMax != null">
        #{rankingMax,jdbcType=INTEGER},
      </if>
      <if test="colorCode != null">
        #{colorCode,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
      <if test="fkExaminationId != null">
        #{fkExaminationId,jdbcType=BIGINT},
      </if>
        <if test="fkCompanyId != null">
            #{fkCompanyId,jdbcType=BIGINT},
        </if>
      <if test="paramJson != null">
        #{paramJson,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.get.examcenter.entity.ScoreTitle">
    update u_score_title
    <set>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="scoreMin != null">
        score_min = #{scoreMin,jdbcType=INTEGER},
      </if>
      <if test="scoreMax != null">
        score_max = #{scoreMax,jdbcType=INTEGER},
      </if>
      <if test="rankingMin != null">
        ranking_min = #{rankingMin,jdbcType=INTEGER},
      </if>
      <if test="rankingMax != null">
        ranking_max = #{rankingMax,jdbcType=INTEGER},
      </if>
      <if test="colorCode != null">
        color_code = #{colorCode,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        view_order = #{viewOrder,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.examcenter.entity.ScoreTitle">
    update u_score_title
    set title = #{title,jdbcType=VARCHAR},
      score_min = #{scoreMin,jdbcType=INTEGER},
      score_max = #{scoreMax,jdbcType=INTEGER},
      ranking_min = #{rankingMin,jdbcType=INTEGER},
      ranking_max = #{rankingMax,jdbcType=INTEGER},
      color_code = #{colorCode,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      view_order = #{viewOrder,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      fk_examination_id = #{fkExaminationId,jdbcType=BIGINT},
      fk_company_id = #{fkCompanyId,jdbcType=BIGINT},
      param_json = #{paramJson,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select
      IFNULL(max(view_order)+1,0) view_order
    from
      u_score_title

  </select>
</mapper>