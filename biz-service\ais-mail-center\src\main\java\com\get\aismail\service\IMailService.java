package com.get.aismail.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.aismail.dto.MailBox;
import com.get.aismail.dto.MailDto;
import com.get.aismail.entity.MFileMail;
import com.get.aismail.entity.MMail;
import com.get.aismail.entity.MMailAccount;
import com.get.aismail.vo.*;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.mail.Message;
import javax.mail.Session;
import javax.mail.UIDFolder;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface IMailService {
    IPage<MailDto> getAllMail(SearchMailVo searchMailVo) throws Exception;

    Long changeMailStatus(ChangeMailStatusVo changeMailStatusVo) throws Exception;

    ResponseEntity<Resource> downloadAttached(HttpServletResponse response, DownloadAttachedVo downloadAttachedVo) throws Exception;

    List<String> uploadFile(MultipartFile[] files) throws Exception;

    void sendMail(SendMailVo sendMailVo) throws Exception;

    void fetchMail(MMailAccount mMailAccount) throws Exception;

    IPage<MailDto> getAllMailManually(SearchMailVo searchMailVo) throws Exception;

    void saveMail(SendMailVo sendMailVo) throws Exception;

//    void fetchAllMail(MMailAccount mMailAccount, MMail mMail, MailBox mailBox, String foldName, String saveFoldName) throws Exception;

    MFileMail uploadFileToParse(MultipartFile file, boolean isBody) throws Exception;

    CountNotReadNumVo countNotReadNum(SearchMailVo searchMailVo) throws Exception;

    Message sendMail(Session session, MailBox mailBox, String foldName, SendMailVo sendMailVo, MMailAccount account) throws Exception;

    Message saveMail(MailBox mailBox, String foldName, SendMailVo sendMailVo, MMailAccount account) throws Exception;

    void processMessage(Message message, String mailId, MMailAccount mMailAccount, String saveFoldName) throws Exception;

    MailDto reGetMessage(MailDto mailDto) throws Exception;
}
