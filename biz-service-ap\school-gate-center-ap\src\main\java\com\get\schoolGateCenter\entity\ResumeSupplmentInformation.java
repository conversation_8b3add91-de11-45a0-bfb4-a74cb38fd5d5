package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * Author: Smail
 * Date: 21/9/2023
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("m_resume_supplment_information")
@ApiModel(value="MResumeSupplmentInformation对象", description="")
public class ResumeSupplmentInformation extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "所属简历Id")
    private Long fkResumeId;

    @ApiModelProperty(value = "内容描述")
    private String content;

    @ApiModelProperty(value = "额外信息枚举")
    private String contentType;


}
