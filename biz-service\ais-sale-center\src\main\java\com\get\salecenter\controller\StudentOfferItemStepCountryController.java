package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.salecenter.vo.StudentOfferItemStepCountryVo;
import com.get.salecenter.service.IStudentOfferItemStepCountryService;
import com.get.salecenter.dto.StudentOfferItemStepCountryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.get.core.log.annotation.OperationLogger;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  前端接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-22
 */

@Api(tags = "申请计划状态步骤国家前置配置 ")
@RestController
@RequestMapping("sale/studentOfferItemStepCountry")
public class StudentOfferItemStepCountryController {


    @Resource
    private IStudentOfferItemStepCountryService studentOfferItemStepCountryService;


    @ApiOperation(value = "新增", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/申请计划状态步骤国家前置配置/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(BaseVoEntity.Add.class) StudentOfferItemStepCountryDto studentOfferItemStepCountryDto) {
        return SaveResponseBo.ok(studentOfferItemStepCountryService.add(studentOfferItemStepCountryDto));
    }


    @ApiOperation(value = "删除", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/申请计划状态步骤国家前置配置/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        studentOfferItemStepCountryService.delete(id);
        return DeleteResponseBo.ok();
    }

    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/申请计划状态步骤国家前置配置/列表数据")
    @PostMapping("datas")
    public ListResponseBo<StudentOfferItemStepCountryVo> datas(@RequestBody @Validated(StudentOfferItemStepCountryDto.Datas.class) SearchBean<StudentOfferItemStepCountryDto> page) {
        List<StudentOfferItemStepCountryVo> datas = studentOfferItemStepCountryService.getStudentOfferItemStepCountryDtos(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

}
