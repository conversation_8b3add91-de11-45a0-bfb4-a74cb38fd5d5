package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.ContractFormulaPreInstitutionGroup;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ContractFormulaPreInstitutionGroupMapper extends BaseMapper<ContractFormulaPreInstitutionGroup> {

    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(ContractFormulaPreInstitutionGroup record);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过合同公式id 查找对应集团ids
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<Long> getGroupIdListByFkid(Long contractFormulaId);

}