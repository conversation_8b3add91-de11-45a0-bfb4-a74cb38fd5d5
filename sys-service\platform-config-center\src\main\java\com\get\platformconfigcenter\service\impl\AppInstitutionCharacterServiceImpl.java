package com.get.platformconfigcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.api.ResultCode;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.platformconfigcenter.dao.appissue.AppInstitutionCharacterMapper;
import com.get.platformconfigcenter.vo.AppInstitutionCharacteVo;
import com.get.platformconfigcenter.entity.AppInstitutionCharacter;
import com.get.platformconfigcenter.service.AppInstitutionCharacterService;
import com.get.platformconfigcenter.service.DeleteService;
import com.get.platformconfigcenter.dto.AppInstitutionCharacterDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 课程动态表单配置业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2021/5/20 15:33
 */
@Service
public class AppInstitutionCharacterServiceImpl implements AppInstitutionCharacterService {
    @Resource
    private AppInstitutionCharacterMapper appInstitutionCharacterMapper;
    @Resource
    private IInstitutionCenterClient feignInstitutionService;
    @Resource
    private UtilService utilService;
    @Resource
    private DeleteService deleteService;


    /**
     * 课程动态表单配置列表数据
     *
     * @return
     * @Date 15:36 2021/5/20
     * <AUTHOR>
     */
    @Override
    public List<AppInstitutionCharacteVo> getInstitutionCharacterList(AppInstitutionCharacterDto appInstitutionCharacterDto, Page page) {
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        Example example = new Example(AppInstitutionCharacter.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(appInstitutionCharacterDto.getFkAreaCountryId())) {
//            criteria.andEqualTo("fkAreaCountryId", appInstitutionCharacterDto.getFkAreaCountryId());
//        }
//        if (GeneralTool.isNotEmpty(appInstitutionCharacterDto.getFkInstitutionId())) {
//            criteria.andEqualTo("fkInstitutionId", appInstitutionCharacterDto.getFkInstitutionId());
//        }
//        if (GeneralTool.isNotEmpty(appInstitutionCharacterDto.getFkInstitutionCourseId())) {
//            criteria.andEqualTo("fkInstitutionCourseId", appInstitutionCharacterDto.getFkInstitutionCourseId());
//        }
//        if (GeneralTool.isNotEmpty(appInstitutionCharacterDto.getIsActive())) {
//            criteria.andEqualTo("isActive", appInstitutionCharacterDto.getIsActive());
//        }
//        List<AppInstitutionCharacter> appInstitutionCharacters = appInstitutionCharacterMapper.selectByExample(example);
//        page.restPage(appInstitutionCharacters);
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        Example example = new Example(AppInstitutionCharacter.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<AppInstitutionCharacter> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(appInstitutionCharacterDto.getFkAreaCountryId())) {
            lambdaQueryWrapper.eq(AppInstitutionCharacter::getFkAreaCountryId, appInstitutionCharacterDto.getFkAreaCountryId());
        }
        if (GeneralTool.isNotEmpty(appInstitutionCharacterDto.getFkInstitutionId())) {
            lambdaQueryWrapper.eq(AppInstitutionCharacter::getFkInstitutionId, appInstitutionCharacterDto.getFkInstitutionId());
        }
        if (GeneralTool.isNotEmpty(appInstitutionCharacterDto.getFkInstitutionCourseId())) {
            lambdaQueryWrapper.eq(AppInstitutionCharacter::getFkInstitutionCourseId, appInstitutionCharacterDto.getFkInstitutionCourseId());
        }
        if (GeneralTool.isNotEmpty(appInstitutionCharacterDto.getIsActive())) {
            lambdaQueryWrapper.eq(AppInstitutionCharacter::getIsActive, appInstitutionCharacterDto.getIsActive());
        }
        IPage<AppInstitutionCharacter> pages = appInstitutionCharacterMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<AppInstitutionCharacter> appInstitutionCharacters = pages.getRecords();
        page.setAll((int) pages.getTotal());

        List<AppInstitutionCharacteVo> appInstitutionCharacterDto1List = new ArrayList<>();
        //111111
        Result<Map<Long, String>> resultcountryNameMap = feignInstitutionService.getCountryNamesByIds(appInstitutionCharacters.stream().map(AppInstitutionCharacter::getFkAreaCountryId).collect(Collectors.toSet()));
        Result<Map<Long, String>> resultinstitutionNameMap = feignInstitutionService.getInstitutionNamesByIds(appInstitutionCharacters.stream().map(AppInstitutionCharacter::getFkInstitutionId).filter(Objects::nonNull).collect(Collectors.toSet()));
        Result<Map<Long, String>> resultinstitutionCourseNamesMap = feignInstitutionService.getInstitutionCourseNamesByIds(appInstitutionCharacters.stream().map(AppInstitutionCharacter::getFkInstitutionCourseId).filter(Objects::nonNull).collect(Collectors.toSet()));
        Map<Long, String> countryNameMap = new HashMap<>();
        Map<Long, String> institutionCourseNamesMap = new HashMap<>();
        Map<Long, String> institutionNameMap = new HashMap<>();
        if (resultcountryNameMap.isSuccess()) {
            countryNameMap = resultcountryNameMap.getData();
        }
        if (resultinstitutionNameMap.isSuccess()) {
            institutionNameMap = resultinstitutionNameMap.getData();
        }
        if (resultinstitutionCourseNamesMap.isSuccess()) {
            institutionCourseNamesMap = resultinstitutionCourseNamesMap.getData();
        }

        for (AppInstitutionCharacter appInstitutionCharacter : appInstitutionCharacters) {
            AppInstitutionCharacteVo appInstitutionCharacterDto1 = BeanCopyUtils.objClone(appInstitutionCharacter, AppInstitutionCharacteVo::new);
            if (GeneralTool.isNotEmpty(appInstitutionCharacter.getFkInstitutionId())) {
                appInstitutionCharacterDto1.setFkInstitutionName(institutionNameMap.get(appInstitutionCharacter.getFkInstitutionId()));
            }
            appInstitutionCharacterDto1.setFkAreaCountryName(countryNameMap.get(appInstitutionCharacter.getFkAreaCountryId()));
            if (GeneralTool.isNotEmpty(appInstitutionCharacter.getFkInstitutionCourseId())) {
                appInstitutionCharacterDto1.setFkInstitutionCourseName(institutionCourseNamesMap.get(appInstitutionCharacter.getFkInstitutionCourseId()));
            }
            appInstitutionCharacterDto1List.add(appInstitutionCharacterDto1);
        }
        return appInstitutionCharacterDto1List;
    }

    /**
     * 新增程动态表单配置
     *
     * @Date 16:13 2021/5/20
     * <AUTHOR>
     */
    @Override
    public Long addInstitutionCharacter(AppInstitutionCharacterDto appInstitutionCharacterDto) {
        if (GeneralTool.isEmpty(appInstitutionCharacterDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
//        Example example = new Example(AppInstitutionCharacter.class);
//        Example.Criteria criteria = example.createCriteria().andEqualTo("fkAreaCountryId", appInstitutionCharacterDto.getFkAreaCountryId());
//        if (GeneralTool.isNotEmpty(appInstitutionCharacterDto.getFkInstitutionId())) {
//            criteria.andEqualTo("fkInstitutionId", appInstitutionCharacterDto.getFkInstitutionId());
//        } else {
//            criteria.andIsNull("fkInstitutionId");
//        }
//        if (GeneralTool.isNotEmpty(appInstitutionCharacterDto.getFkInstitutionCourseId())) {
//            criteria.andEqualTo("fkInstitutionCourseId", appInstitutionCharacterDto.getFkInstitutionCourseId());
//        } else {
//            criteria.andIsNull("fkInstitutionCourseId");
//        }
//        List<AppInstitutionCharacter> appInstitutionCharacters = appInstitutionCharacterMapper.selectByExample(example);
        LambdaQueryWrapper<AppInstitutionCharacter> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(AppInstitutionCharacter::getFkAreaCountryId, appInstitutionCharacterDto.getFkAreaCountryId());
        if (GeneralTool.isNotEmpty(appInstitutionCharacterDto.getFkInstitutionId())) {
            lambdaQueryWrapper.eq(AppInstitutionCharacter::getFkInstitutionId, appInstitutionCharacterDto.getFkInstitutionId());
        } else {
            lambdaQueryWrapper.isNull(AppInstitutionCharacter::getFkInstitutionId);
        }
        if (GeneralTool.isNotEmpty(appInstitutionCharacterDto.getFkInstitutionCourseId())) {
            lambdaQueryWrapper.eq(AppInstitutionCharacter::getFkInstitutionCourseId, appInstitutionCharacterDto.getFkInstitutionCourseId());
        } else {
            lambdaQueryWrapper.isNull(AppInstitutionCharacter::getFkInstitutionCourseId);
        }
        List<AppInstitutionCharacter> appInstitutionCharacters = appInstitutionCharacterMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(appInstitutionCharacters)) {
            throw new GetServiceException(ResultCode.INVALID_PARAM, LocaleMessageUtils.getMessage("type_name_exists"));
        }
        AppInstitutionCharacter appInstitutionCharacter = BeanCopyUtils.objClone(appInstitutionCharacterDto, AppInstitutionCharacter::new);
        utilService.updateUserInfoToEntity(appInstitutionCharacter);
        appInstitutionCharacterMapper.insert(appInstitutionCharacter);
        return appInstitutionCharacter.getId();
    }

    /**
     * 修改程动态表单配置
     *
     * @Date 16:29 2021/5/20
     * <AUTHOR>
     */
//    @Override
//    public AppInstitutionCharacteVo updateInstitutionCharacter(AppInstitutionCharacterDto appInstitutionCharacterVo) {
//        if (GeneralTool.isEmpty(appInstitutionCharacterVo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
//        }
//        AppInstitutionCharacter appInstitutionCharacter = appInstitutionCharacterMapper.selectById(appInstitutionCharacterVo.getId());
//        if (GeneralTool.isEmpty(appInstitutionCharacter)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        }
////        Example example = new Example(AppInstitutionCharacter.class);
////        Example.Criteria criteria = example.createCriteria().andEqualTo("fkAreaCountryId", appInstitutionCharacterVo.getFkAreaCountryId())
////                .andNotEqualTo("id", appInstitutionCharacterVo.getId());
////        if (GeneralTool.isNotEmpty(appInstitutionCharacterVo.getFkInstitutionId())) {
////            criteria.andEqualTo("fkInstitutionId", appInstitutionCharacterVo.getFkInstitutionId());
////        } else {
////            criteria.andIsNull("fkInstitutionId");
////        }
////        if (GeneralTool.isNotEmpty(appInstitutionCharacterVo.getFkInstitutionCourseId())) {
////            criteria.andEqualTo("fkInstitutionCourseId", appInstitutionCharacterVo.getFkInstitutionCourseId());
////        } else {
////            criteria.andIsNull("fkInstitutionCourseId");
////        }
////        List<AppInstitutionCharacter> appInstitutionCharacters = appInstitutionCharacterMapper.selectByExample(example);
//
//        LambdaQueryWrapper<AppInstitutionCharacter> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.ne(AppInstitutionCharacter::getId,appInstitutionCharacterVo.getId());
//        lambdaQueryWrapper.eq(AppInstitutionCharacter::getFkAreaCountryId, appInstitutionCharacterVo.getFkAreaCountryId());
//        if (GeneralTool.isNotEmpty(appInstitutionCharacterVo.getFkInstitutionId())) {
//            lambdaQueryWrapper.eq(AppInstitutionCharacter::getFkInstitutionId, appInstitutionCharacterVo.getFkInstitutionId());
//        } else {
//            lambdaQueryWrapper.isNull(AppInstitutionCharacter::getFkInstitutionId);
//        }
//        if (GeneralTool.isNotEmpty(appInstitutionCharacterVo.getFkInstitutionCourseId())) {
//            lambdaQueryWrapper.eq(AppInstitutionCharacter::getFkInstitutionCourseId, appInstitutionCharacterVo.getFkInstitutionCourseId());
//        } else {
//            lambdaQueryWrapper.isNull(AppInstitutionCharacter::getFkInstitutionCourseId);
//        }
//        List<AppInstitutionCharacter> appInstitutionCharacters = appInstitutionCharacterMapper.selectList(lambdaQueryWrapper);
//
//        if (GeneralTool.isNotEmpty(appInstitutionCharacters)) {
//            throw new GetServiceException(ResultCode.INVALID_PARAM, LocaleMessageUtils.getMessage("type_name_exists"));
//        }
//        appInstitutionCharacter = BeanCopyUtils.objClone(appInstitutionCharacterVo, AppInstitutionCharacter::new);
//        utilService.updateUserInfoToEntity(appInstitutionCharacter);
//        appInstitutionCharacterMapper.updateByPrimaryKeySelective(appInstitutionCharacter);
//        return findInstitutionCharacterById(appInstitutionCharacter.getId());
//    }

    /**
     * 程动态表单配置详情
     *
     * @Date 16:34 2021/5/20
     * <AUTHOR>
     */
    @Override
    public AppInstitutionCharacteVo findInstitutionCharacterById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AppInstitutionCharacter appInstitutionCharacter = appInstitutionCharacterMapper.selectById(id);
        if (GeneralTool.isEmpty(appInstitutionCharacter)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        AppInstitutionCharacteVo appInstitutionCharacterDto1 = BeanCopyUtils.objClone(appInstitutionCharacter, AppInstitutionCharacteVo::new);
        //111111
        if (GeneralTool.isNotEmpty(appInstitutionCharacterDto1.getFkInstitutionId())) {
            Result<String> institutionNames = feignInstitutionService.getInstitutionNamesById(appInstitutionCharacterDto1.getFkInstitutionId());
            if (institutionNames.isSuccess()) {
                appInstitutionCharacterDto1.setFkInstitutionName(institutionNames.getData());
            }

        }
        if (GeneralTool.isNotEmpty(appInstitutionCharacter.getFkAreaCountryId())) {
            Result<String> countryName = feignInstitutionService.getCountryNameById(appInstitutionCharacter.getFkAreaCountryId());
            if (countryName.isSuccess()) {
                appInstitutionCharacterDto1.setFkAreaCountryName(countryName.getData());
            }

        }
        if (GeneralTool.isNotEmpty(appInstitutionCharacter.getFkInstitutionCourseId())) {
            Result<String> institutionCourseName = feignInstitutionService.getInstitutionCourseNameById(appInstitutionCharacter.getFkInstitutionCourseId());
            if (institutionCourseName.isSuccess()) {
                appInstitutionCharacterDto1.setFkInstitutionCourseName(institutionCourseName.getData());
            }
        }
        return appInstitutionCharacterDto1;
    }

    /**
     * 删除程动态表单配置
     *
     * @Date 16:38 2021/5/20
     * <AUTHOR>
     */
//    @Override
//    public void delete(Long id) {
//        if (GeneralTool.isEmpty(id)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
//        deleteService.deleteValidateAppInstitutionCharacter(id);
//        appInstitutionCharacterMapper.deleteById(id);
//    }


}
