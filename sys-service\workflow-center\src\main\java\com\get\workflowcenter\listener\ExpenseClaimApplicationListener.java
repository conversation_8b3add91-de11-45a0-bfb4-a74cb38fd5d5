package com.get.workflowcenter.listener;

import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.SpringUtil;
import com.get.financecenter.entity.ExpenseClaimForm;
import com.get.financecenter.entity.PrepayApplicationForm;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.financecenter.vo.PrepayApplicationFormVo;
import com.get.permissioncenter.vo.StaffVo;
import com.get.workflowcenter.component.IWorkFlowHelper;
import com.get.workflowcenter.entity.WorkFlowPrepayApplicationForm;
import java.io.Serializable;
import java.util.StringJoiner;
import org.activiti.engine.HistoryService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.repository.ProcessDefinition;

/**
 * 费用报销单监听器
 */
public class ExpenseClaimApplicationListener implements Serializable, ExecutionListener {
    @Override
    public void notify(DelegateExecution delegateExecution) {
        IWorkFlowHelper workFlowHelper = SpringUtil.getBean(IWorkFlowHelper.class);
        //获取流程名称
        ProcessEngine processEngine = SpringUtil.getBean(ProcessEngine.class);
        ProcessDefinition processDefinition = processEngine.getRepositoryService()
                .createProcessDefinitionQuery()
                .processDefinitionId(delegateExecution.getProcessDefinitionId())
                .singleResult();
        StaffVo staffVo = workFlowHelper.getExecutionStaffDto(delegateExecution);

        String processInstanceBusinessKey = delegateExecution.getProcessInstanceBusinessKey();
        String processInstanceId = delegateExecution.getProcessInstanceId();
        HistoryService historyService = SpringUtil.getBean(HistoryService.class);
        IFinanceCenterClient financeCenterClient = SpringUtil.getBean(IFinanceCenterClient.class);
        UtilService utilService = SpringUtil.getBean(UtilService.class);
        if ("end".equals(delegateExecution.getEventName())) {
            Result<ExpenseClaimForm> result = financeCenterClient.getExpenseClaimFormById(Long.valueOf(processInstanceBusinessKey));
            //数据库名称
            StringJoiner stringJoiner = new StringJoiner(",");
            stringJoiner.add(ProjectKeyEnum.WORKFLOW_CENTER.key).add(ProjectKeyEnum.WORKFLOW_CENTER_TO_DO.key);
            if (!result.isSuccess()) {
                return;
            }
            ExpenseClaimForm expenseClaimForm = result.getData();
            expenseClaimForm.setStatus(1);
            Result<Boolean> result1 = financeCenterClient.updateExpenseClaimFormStatus(expenseClaimForm);
            workFlowHelper.sendMessage(staffVo, null, processDefinition, "审批通过", delegateExecution, stringJoiner.toString(),null);
            if (!result1.isSuccess()) {
                return;
            }
        } else {
            Object sequenceFlowsStatus = delegateExecution.getVariable("sequenceFlowsStatus");
            Result<PrepayApplicationFormVo> prepayApplicationFormVoResult = financeCenterClient.getBorrowMoneyById(Long.valueOf(processInstanceBusinessKey));
            if (prepayApplicationFormVoResult.isSuccess() && GeneralTool.isNotEmpty(prepayApplicationFormVoResult.getData())) {
                PrepayApplicationFormVo prepayApplicationFormVo = prepayApplicationFormVoResult.getData();
                WorkFlowPrepayApplicationForm prepayApplicationForm = BeanCopyUtils.objClone(prepayApplicationFormVo, WorkFlowPrepayApplicationForm::new);
                if ("0".equals(sequenceFlowsStatus)) {
                    utilService.updateUserInfoToEntity(prepayApplicationForm);
                    prepayApplicationForm.setStatus(3);
                } else {
                    utilService.updateUserInfoToEntity(prepayApplicationForm);
                    prepayApplicationForm.setStatus(2);
                }
                PrepayApplicationForm prepayApplicationForm_ = BeanCopyUtils.objClone(prepayApplicationForm, PrepayApplicationForm::new);
                financeCenterClient.updateBorrowMoneyStatus(prepayApplicationForm_);
            }
        }
    }
}
