package get.middlecenter.service.impl;

import get.middlecenter.consts.MiddleConstant;
import get.middlecenter.dto.TransferRequest;
import get.middlecenter.dto.appPartner.CreateContractPdfDto;
import get.middlecenter.service.AppPartnerService;
import get.middlecenter.utils.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;

/**
 * @Author:Oliver
 * @Date: 2025/7/23
 * @Version 1.0
 * @apiNote:伙伴
 */
@Service
@Slf4j
public class AppPartnerControllerImpl implements AppPartnerService {

    @Autowired
    private HttpUtils httpUtils;

    @Override
    public void createContractPdf(CreateContractPdfDto dto, String nonce, HttpServletResponse response) {
        String baseUrl = MiddleConstant.SALE_CENTER_CREATE_CONTRACT_PDF;

        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(baseUrl)
                .queryParam("contractId", dto.getContractId())
                .queryParam("agentId", dto.getAgentId())
                .queryParam("contractVsion", dto.getContractVsion())
                .queryParam("contractTemplateMode", dto.getContractTemplateMode());

        String url = builder.toUriString();
        log.info("调用api:{}",url);
        httpUtils.sendPostRequest(dto, url, nonce);
        log.info("调用成功=============>");
    }
}
