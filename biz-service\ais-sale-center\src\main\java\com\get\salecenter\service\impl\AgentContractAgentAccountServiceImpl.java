package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.salecenter.dao.sale.AgentContractAccountMapper;
import com.get.salecenter.dao.sale.AgentContractAgentAccountMapper;
import com.get.salecenter.vo.AgentContractAccountVo;
import com.get.salecenter.entity.AgentContractAccount;
import com.get.salecenter.entity.AgentContractAgentAccount;
import com.get.salecenter.service.IAgentContractAgentAccountService;
import com.get.salecenter.utils.VerifyDataPermissionsUtils;
import com.get.salecenter.dto.AgentContractAgentAccountDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 合同合同账户管理逻辑处理类
 *
 * @Date 16:05 2021/6/28
 * <AUTHOR>
 */
@Service
public class AgentContractAgentAccountServiceImpl extends ServiceImpl<AgentContractAgentAccountMapper, AgentContractAgentAccount> implements IAgentContractAgentAccountService {
    @Resource
    private AgentContractAgentAccountMapper agentContractAgentAccountMapper;
    @Resource
    private AgentContractAccountMapper agentContractAccountMapper;
    @Autowired
    private UtilService utilService;
    @Resource
    private IFinanceCenterClient financeCenterClient;

    @Resource
    private VerifyDataPermissionsUtils verifyDataPermissionsUtils;

    /**
     * 合同账户列表数据
     *
     * @Date 15:08 2021/6/28
     * <AUTHOR>
     */
    @Override
    public List<AgentContractAccountVo> getAgentContractAgentAccount(AgentContractAgentAccountDto agentContractAgentAccountDto, Page page) {
        if (GeneralTool.isEmpty(agentContractAgentAccountDto) && GeneralTool.isEmpty(agentContractAgentAccountDto.getFkAgentContractId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        verifyDataPermissionsUtils.verifyByBusinessId(agentContractAgentAccountDto.getFkAgentContractId(),VerifyDataPermissionsUtils.AGENT_CONTRACT_O);
        //获取分页数据
        IPage<AgentContractAccountVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<AgentContractAccountVo> agentContractAccountVos = agentContractAgentAccountMapper.selectAgentContractAgentAccount(iPage, agentContractAgentAccountDto.getFkAgentContractId());
        page.setAll((int) iPage.getTotal());

        agentContractAccountVos = agentContractAccountVos.stream().filter(Objects::nonNull).collect(Collectors.toList());
        for (AgentContractAccountVo agentContractAccountVo : agentContractAccountVos) {
            Result<String> result = financeCenterClient.getCurrencyNameByNum(agentContractAccountVo.getFkCurrencyTypeNum());
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                agentContractAccountVo.setCurrencyTypeName(result.getData());
            }
        }
        return agentContractAccountVos;
    }


    /**
     * 代理合同-账户绑定
     *
     * @Date 16:50 2021/6/28
     * <AUTHOR>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindingContractAgentAccount(List<AgentContractAgentAccountDto> contractAgentAccountVos, Long contractId) {
        if (GeneralTool.isNotEmpty(contractAgentAccountVos)) {
            Set<String> currencyTypeNum = contractAgentAccountVos.stream().map(AgentContractAgentAccountDto::getFkCurrencyTypeNum).collect(Collectors.toSet());
            //币种唯一
            if (currencyTypeNum.size() != contractAgentAccountVos.size()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("agent_account_contract_currency_exist"));
            }
        }
//        Example example = new Example(AgentContractAgentAccount.class);
//        example.createCriteria().andEqualTo("fkAgentContractId", contractId);
//        agentContractAgentAccountMapper.deleteByExample(example);

        agentContractAgentAccountMapper.delete(Wrappers.<AgentContractAgentAccount>lambdaQuery().eq(AgentContractAgentAccount::getFkAgentContractId, contractId));
        for (AgentContractAgentAccountDto contractAgentAccountVo : contractAgentAccountVos) {
            AgentContractAgentAccount agentContractAgentAccount = BeanCopyUtils.objClone(contractAgentAccountVo, AgentContractAgentAccount::new);
            utilService.updateUserInfoToEntity(agentContractAgentAccount);
            agentContractAgentAccountMapper.insert(agentContractAgentAccount);
            if (contractAgentAccountVo.getAllActivate()) {
                AgentContractAccount agentContractAccount = agentContractAccountMapper.selectById(contractAgentAccountVo.getFkAgentContractAccountId());
                if (GeneralTool.isEmpty(agentContractAccount)) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
                }
                agentContractAccount.setIsActive(true);
                utilService.updateUserInfoToEntity(agentContractAccount);
                agentContractAccountMapper.updateById(agentContractAccount);
            }
        }
    }


    /**
     * 合同绑定账户详情列表数据
     *
     * @Date 12:26 2021/6/29
     * <AUTHOR>
     */
    @Override
    public List<AgentContractAccountVo> getAgentContractAgentAccountDetail(Long contractId) {
        //获取分页数据
        List<AgentContractAccountVo> agentContractAccountVos = agentContractAgentAccountMapper.selectAgentContractAgentAccount(null, contractId);
        agentContractAccountVos = agentContractAccountVos.stream().filter(Objects::nonNull).collect(Collectors.toList());
        for (AgentContractAccountVo agentContractAccountVo : agentContractAccountVos) {
            Result<String> result = financeCenterClient.getCurrencyNameByNum(agentContractAccountVo.getFkCurrencyTypeNum());
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                agentContractAccountVo.setCurrencyTypeName(result.getData());
            }
        }
        return agentContractAccountVos;
    }

}
