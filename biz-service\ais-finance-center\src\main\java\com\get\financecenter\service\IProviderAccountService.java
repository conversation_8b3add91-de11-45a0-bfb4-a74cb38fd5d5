package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.financecenter.dto.ProviderAccountDto;
import com.get.financecenter.vo.ProviderAccountVo;
import com.get.financecenter.entity.ProviderAccount;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/12/28 10:53
 * @verison: 1.0
 * @description:
 */
public interface IProviderAccountService extends BaseService<ProviderAccount> {
    /**
     * @return com.get.salecenter.vo.ProviderAccountDto
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    ProviderAccountVo findProviderAccountById(Long id);

    /**
     * @return void
     * @Description :新增
     * @Param [providerAccountVo]
     * <AUTHOR>
     */
    Long addProviderAccount(ProviderAccountDto providerAccountDto);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.salecenter.vo.ProviderAccountDto
     * @Description :修改
     * @Param [providerAccountVo]
     * <AUTHOR>
     */
    ProviderAccountVo updateProviderAccount(ProviderAccountDto providerAccountDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.ProviderAccountDto>
     * @Description :列表
     * @Param [providerAccountVo, page]
     * <AUTHOR>
     */
    List<ProviderAccountVo> getProviderAccounts(ProviderAccountDto providerAccountDto, Page page);

    /**
     * @return void
     * @Description :是否激活
     * @Param [providerAccountId, isActive]
     * <AUTHOR>
     */
    void isActive(Long providerAccountId, Boolean isActive);
}
