package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CreateAgentContractDto {

    @ApiModelProperty(value = "密钥")
    private String secret;
    @ApiModelProperty(value = "合同Id")
    private String encryptContractId;
    @ApiModelProperty(value = "学生代理Id")
    private String encryptAgentId;
    @ApiModelProperty(value = "合同版本")
    private Long contractVsion;
    @ApiModelProperty(value = "合同模板：0=MPS主合同/1=PMP主合同/2=PMP附加合同")
    private Integer contractTemplateMode;
}
