package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.CyclingRegistrationVo;
import com.get.salecenter.service.ConventionCyclingRegistrationService;
import com.get.salecenter.dto.CyclingRegistrationDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 峰会骑行活动管理类
 *
 * <AUTHOR>
 * @date 2021/10/14 10:46
 */
@Api(tags = "峰会骑行管理")
@RestController
@RequestMapping("sale/conventionCyclingRegistration")
public class ConventionCyclingRegistrationController {
    @Resource
    private ConventionCyclingRegistrationService conventionCyclingRegistrationService;

    /**
     * 列表数据
     *
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/公益骑行/公益骑行列表")
    @PostMapping("datas")
    public ResponseBo<CyclingRegistrationVo> datas(@RequestBody @Valid CyclingRegistrationDto cyclingRegistrationDto) {
        List<CyclingRegistrationVo> datas = conventionCyclingRegistrationService.getCyclingRegistrationList(cyclingRegistrationDto);
        return new ListResponseBo<>(datas);
    }

    /**
     * 导出骑行活动报名Excel
     * <AUTHOR>
     * @DateTime 2023/4/13 15:03
     */
    @ApiOperation(value = "导出骑行活动报名Excel", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/公益骑行/导出骑行活动报名Excel")
    @PostMapping("/exportCyclingRegistrationExcel")
    public void exportCyclingRegistrationExcel(HttpServletResponse response, @RequestBody CyclingRegistrationDto cyclingRegistrationDto) {
        conventionCyclingRegistrationService.exportCyclingRegistrationExcel(cyclingRegistrationDto,response);
    }

    /**
     * 身份类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "身份类型下拉框数据", notes = "")
    @GetMapping("getIdTypeSelect")
    public ResponseBo getIdTypeSelect() {
        return new ListResponseBo<>(ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.CYCLING_ID_TYPE));
    }


    /**
     * 支付状态下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "支付状态下拉框数据", notes = "")
    @GetMapping("getPayStatusSelect")
    public ResponseBo getPayStatusSelect() {
        return new ListResponseBo<>(ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.CYCLING_PAY_STATUS));
    }


    @ApiOperation(value = "编辑新增备注", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/公益骑行/编辑新增备注")
    @PostMapping("/editRemark")
    public SaveResponseBo editRemark(@RequestParam("id") Long id,@RequestParam("remark") String remark){
        return conventionCyclingRegistrationService.editRemark(id,remark);
    }


    @ApiOperation(value = "删除备注", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/公益骑行/删除备注")
    @PostMapping("/delete/{id}")
    public DeleteResponseBo deleteRemark(@PathVariable("id") Long id) {
        return conventionCyclingRegistrationService.delete(id);
    }

    @ApiOperation(value = "更新支付状态", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/公益骑行/更新支付状态")
    @PostMapping("/updatePayStatus")
    public SaveResponseBo updatePayStatus(@RequestParam("id")Long id,@RequestParam("status") Integer status) {
        return conventionCyclingRegistrationService.updatePayStatus(id,status);
    }
}
