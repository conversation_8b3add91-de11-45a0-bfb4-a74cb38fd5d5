<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.CurrencyTypeMapper">
  <insert id="insertSelective" parameterType="com.get.financecenter.entity.CurrencyType" keyProperty="id" useGeneratedKeys="true">
    insert into u_currency_type
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="typeName != null">
        type_name,
      </if>
      <if test="publicLevel != null">
        public_level,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="num != null">
        #{num,jdbcType=VARCHAR},
      </if>
      <if test="typeName != null">
        #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="publicLevel != null">
        #{publicLevel,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getCurrencyNameByNum" parameterType="java.lang.String" resultType="java.lang.String">
    select
      CASE WHEN IFNULL(type_name,'')='' THEN `num` ELSE CONCAT(`type_name`,'（',num,'）') END fullName
    from
     u_currency_type
    where
     num = #{num}

  </select>

  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select
     IFNULL(max(view_order)+1,0) view_order
    from
     u_currency_type

  </select>
    <select id="getCurrencyByPublicLevel" resultType="com.get.financecenter.vo.CurrencyTypeVo">
      select *
      from u_currency_type
      WHERE FIND_IN_SET(#{key},public_level)
      order by view_order desc
    </select>
</mapper>