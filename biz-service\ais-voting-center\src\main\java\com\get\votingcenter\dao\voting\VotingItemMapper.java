package com.get.votingcenter.dao.voting;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.votingcenter.vo.VotingItemVo;
import com.get.votingcenter.entity.VotingItem;
import com.get.votingcenter.dto.VotingItemListDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface VotingItemMapper extends BaseMapper<VotingItem> {

    int insertSelective(VotingItem record);

    /**
     * 获取最大排序值
     *
     * @return
     */
    Integer getMaxViewOrder();

    /**
     * 获取投票项
     *
     * @param votingItemListDto
     * @return
     */
    List<VotingItemVo> getVotingItems(IPage<VotingItemVo> pages, @Param("votingItemListDto") VotingItemListDto votingItemListDto);
}