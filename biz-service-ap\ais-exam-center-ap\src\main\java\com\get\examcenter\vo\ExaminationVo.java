package com.get.examcenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by <PERSON>.
 * Time: 9:45
 * Date: 2021/8/23
 * Description:考试管理返回类
 */
@Data
@ApiModel("考试管理返回类")
public class  ExaminationVo extends BaseEntity implements Serializable {

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    /**
     * 是否激活（中文值）
     */
    @ApiModelProperty(value = "是否激活（中文值）")
    private String isActiveName;


    /**
     * 考试次数
     */
    @ApiModelProperty(value = "考试次数")
    private Integer scoreCount;

    //============实体类=================
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
    /**
     * 考试编号
     */
    @ApiModelProperty(value = "考试编号")
    private String num;
    /**
     * 考试名称
     */
    @ApiModelProperty(value = "考试名称")
    private String name;
    /**
     * 开放时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "开放时间")
    private Date startTime;
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;




}
