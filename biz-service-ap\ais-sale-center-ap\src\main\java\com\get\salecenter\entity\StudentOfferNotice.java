package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.get.core.mybatis.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Data
@TableName("m_student_offer_notice")
@ApiModel(value="StudentOfferNotice对象", description="")
public class StudentOfferNotice extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "异步执行记录Id")
    private Long fkReportSaleId;

    @ApiModelProperty(value = "学生Id")
    private Long fkStudentId;

//    @TableField("fk_student_offer_notice_template_id")
//    @ApiModelProperty(value = "学生申请方案项目状态步骤Id（最高状态）")
//    private Long fkStudentOfferItemStepIdHighest;

    @ApiModelProperty(value = "学生代理Id")
    private Long fkAgentId;

    @ApiModelProperty(value = "入读意向通知模板Id")
    private Long fkStudentOfferNoticeTemplateId;

    @ApiModelProperty(value = "邮件标题")
    private String emailSubject;

    @ApiModelProperty(value = "邮件内容")
    private String emailContent;

    @ApiModelProperty(value = "发件人员工Id")
    private Long fkStaffIdFrom;

    @ApiModelProperty(value = "发件人地址")
    private String fromEmail;

    @ApiModelProperty(value = "抄送地址")
    private String ccEmail;

    @ApiModelProperty(value = "收件人地址")
    private String toEmail;

    @ApiModelProperty(value = "备注（失败Msg）")
    private String remark;

    @ApiModelProperty(value = "状态：未发=0/已发=1/拒收=2/失败=-1/删除=-2（删除不显示，但保留数据）")
    private Integer status;

}
