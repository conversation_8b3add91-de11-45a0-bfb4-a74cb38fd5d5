<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>biz-service</artifactId>
        <groupId>com.get</groupId>
        <version>1.0.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ais-sale-center</artifactId>
    <name>${project.artifactId}</name>
    <version>${get.project.version}</version>
    <packaging>jar</packaging>

    <dependencies>

        <!-- Spring Boot Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- Thymeleaf 模板 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>

        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-boot</artifactId>
            <version>${get.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-swagger</artifactId>
            <version>${get.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.13.2</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
            <version>5.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf.tool</groupId>
            <artifactId>xmlworker</artifactId>
            <version>5.5.13.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-transcoder</artifactId>
            <version>1.16</version>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-awt-util</artifactId>
            <version>1.16</version>
        </dependency>

        <!-- XML Graphics 公共库（Batik 依赖） -->
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>xmlgraphics-commons</artifactId>
            <version>2.7</version>
            <exclusions>
                <!-- 排除其他模块引入的旧版本 -->
                <exclusion>
                    <groupId>org.apache.xmlgraphics</groupId>
                    <artifactId>xmlgraphics-commons</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-codec</artifactId>
            <version>1.16</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
            <version>2.3.12.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>ais-sale-center-ap</artifactId>
            <version>${get.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>permission-center-ap</artifactId>
            <version>${get.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>ais-finance-center-ap</artifactId>
            <version>${get.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-auto</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-cloud</artifactId>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>file-option-ap</artifactId>
            <version>${get.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>workflow-center-ap</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>file-center-ap</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.get</groupId>-->
        <!--            <artifactId>institution-center-ap</artifactId>-->
        <!--            <version>${get.project.version}</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>ais-reminder-center-ap</artifactId>
            <version>${get.project.version}</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.get</groupId>-->
        <!--            <artifactId>platform-config-center-ap</artifactId>-->
        <!--            <version>1.0.RELEASE</version>-->
        <!--            <scope>compile</scope>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>ais-platform-center-ap</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>i18n-center-ap</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>ais-institution-center-ap</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>4.1.33.Final</version>
        </dependency>
        <dependency>
            <groupId>org.nlpcn</groupId>
            <artifactId>nlp-lang</artifactId>
            <version>1.7.9</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>ais-report-center-ap</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>

        <!--hutool mail-->
        <dependency>
            <groupId>javax.mail</groupId>
            <artifactId>mail</artifactId>
            <version>1.4.7</version>
        </dependency>
        <!-- springboot 邮件mail -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>

        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.1</version>
        </dependency>

        <dependency>
            <groupId>org.nlpcn</groupId>
            <artifactId>nlp-lang</artifactId>
            <version>1.7.9</version>
        </dependency>

        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <version>21.5.0.0</version>
        </dependency>
        <dependency>
            <groupId>cn.easyproject</groupId>
            <artifactId>orai18n</artifactId>
            <version>12.1.0.2.0</version>
        </dependency>
        <dependency>
            <groupId>io.seata</groupId>
            <artifactId>seata-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>ais-registration-center-ap</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-pay</artifactId>
            <version>4.1.5.B</version>
        </dependency>
        <!--二维码-->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <dependency>
            <groupId>me.xdrop</groupId>
            <artifactId>fuzzywuzzy</artifactId>
            <version>1.4.0</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>ais-pmp-center-ap</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>ais-partner-center-ap</artifactId>
            <version>${get.project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>ais-exam-center-ap</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot.version}</version>
                    <configuration>
                        <fork>true</fork>
                        <finalName>${project.build.finalName}</finalName>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>com.spotify</groupId>
                    <artifactId>docker-maven-plugin</artifactId>
                    <version>1.2.2</version>
                    <executions>
                        <execution>
                            <id>build-image</id>
                            <phase>package</phase>
                            <goals>
                                <goal>build</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <!--打包docker镜像的docker服务器-->
                        <dockerHost>${docker-url}</dockerHost>
                        <!--镜像名，这里用工程名 -->
                        <imageName>${registry-url}/${nexus.project_name}/${project.artifactId}:${nexus.version}
                        </imageName>
                        <!--nexus3 hosted 仓库地址-->
                        <registryUrl>${registry-url}</registryUrl>
                        <!-- ca认证正书-->
                        <!--                        <dockerCertPath>./docker/cert-new</dockerCertPath>-->
                        <!--TAG,这里用工程版本号-->
                        <imageTags>
                            <!-- 指定镜像标签,可以排至多个标签 -->
                            <imageTag>${nexus.version}</imageTag>
                            <imageTag>latest</imageTag>
                        </imageTags>
                        <!--是否强制覆盖已有镜像-->
                        <forceTags>true</forceTags>
                        <!--方式一：1、指定Dockerfile文件所在目录，通过文件执行打包上传nexus私服-->
                        <dockerDirectory>biz-service/${project.artifactId}/src/main/docker</dockerDirectory>
                        <!-- 指定docker镜像打包参数，即dockerfile中使用的参数，通过${参数名}取值 -->
                        <buildArgs>
                            <JAR_FILE>${project.build.finalName}.jar</JAR_FILE>
                            <JAR_FILE_NAME>${project.artifactId}.jar</JAR_FILE_NAME>
                        </buildArgs>
                        <resources>
                            <resource>
                                <targetPath>/</targetPath>
                                <directory>${project.build.directory}</directory>
                                <include>${project.build.finalName}.jar</include>
                            </resource>
                        </resources>
                        <serverId>nexus-docker-prod</serverId>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <executions>
                        <execution>
                            <phase>package</phase>
                            <goals>
                                <goal>run</goal>
                            </goals>
                            <configuration>
                                <tasks>
                                    <!--suppress UnresolvedMavenProperty -->
                                    <copy overwrite="true"
                                          tofile="${session.executionRootDirectory}/target/${project.artifactId}.jar"
                                          file="${project.build.directory}/${project.artifactId}.jar"/>
                                </tasks>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>