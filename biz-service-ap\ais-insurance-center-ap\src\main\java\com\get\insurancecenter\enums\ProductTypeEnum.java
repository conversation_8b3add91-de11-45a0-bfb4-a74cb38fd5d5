package com.get.insurancecenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 产品类型枚举类
 */

@Getter
@AllArgsConstructor
public enum ProductTypeEnum {

    NIB("NIB", "NIB免登录" ),
    ALLIAN("ALLIAN", "安联" ),
    NIB_LOGIN("NIB_LOGIN", "NIB登录" ),
    ;

    private String code;

    private String msg;


    public static ProductTypeEnum getEnumByCode(String code) {
        for (ProductTypeEnum value : ProductTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
