<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.StudentContactPersonMapper">
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.StudentContactPerson">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_student_id" jdbcType="BIGINT" property="fkStudentId"/>
        <result column="relationship" jdbcType="VARCHAR" property="relationship"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="gender" jdbcType="VARCHAR" property="gender"/>
        <result column="job" jdbcType="VARCHAR" property="job"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="tel" jdbcType="VARCHAR" property="tel"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="qq" jdbcType="VARCHAR" property="qq"/>
        <result column="wechat" jdbcType="VARCHAR" property="wechat"/>
        <result column="whatsapp" jdbcType="VARCHAR" property="whatsapp"/>
        <result column="contact_address" jdbcType="VARCHAR" property="contactAddress"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, fk_student_id, relationship, name, gender, job, mobile, tel, email, qq, wechat, 
    whatsapp, contact_address, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
  </sql>

    <insert id="insertSelective" parameterType="com.get.salecenter.entity.StudentContactPerson" keyProperty="id"
            useGeneratedKeys="true">
        insert into m_student_contact_person
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkStudentId != null">
                fk_student_id,
            </if>
            <if test="relationship != null">
                relationship,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="gender != null">
                gender,
            </if>
            <if test="job != null">
                job,
            </if>
            <if test="mobile != null">
                mobile,
            </if>
            <if test="tel != null">
                tel,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="qq != null">
                qq,
            </if>
            <if test="wechat != null">
                wechat,
            </if>
            <if test="whatsapp != null">
                whatsapp,
            </if>
            <if test="contactAddress != null">
                contact_address,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkStudentId != null">
                #{fkStudentId,jdbcType=BIGINT},
            </if>
            <if test="relationship != null">
                #{relationship,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="gender != null">
                #{gender,jdbcType=VARCHAR},
            </if>
            <if test="job != null">
                #{job,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="tel != null">
                #{tel,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="qq != null">
                #{qq,jdbcType=VARCHAR},
            </if>
            <if test="wechat != null">
                #{wechat,jdbcType=VARCHAR},
            </if>
            <if test="whatsapp != null">
                #{whatsapp,jdbcType=VARCHAR},
            </if>
            <if test="contactAddress != null">
                #{contactAddress,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="isExistByStudentId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id from m_student_contact_person where fk_student_id=#{studentId}
    </select>

    <select id="getStudentContactPersonByStudentId" resultType="com.get.salecenter.vo.StudentContactPersonVo">
        SELECT * FROM m_student_contact_person WHERE fk_student_id = #{studentId}
    </select>
</mapper>