package com.get.salecenter.dto;

import com.get.salecenter.entity.InsuranceOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class InsuranceOrderDto extends InsuranceOrder {

    @ApiModelProperty(value = "学生信息")
    private String studentName;

    @ApiModelProperty(value = "业务信息")
    private String businessInformation;

    @ApiModelProperty(value = "代理名字")
    private String agentName;

    @ApiModelProperty(value = "保单开始时间")
    private Date insuranceStartTime;

    @ApiModelProperty(value = "保单结束时间")
    private Date insuranceEndTime;

    @ApiModelProperty(value = "保险单号")
    private String insuranceNum;

    @ApiModelProperty(value = "保险产品类型名称")
    private String productTypeName;

    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;

}
