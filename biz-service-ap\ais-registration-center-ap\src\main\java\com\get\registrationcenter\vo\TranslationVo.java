package com.get.registrationcenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import com.get.registrationcenter.entity.RegistrationTranslation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Hardy
 * @create: 2021/5/14 17:24
 * @verison: 1.0
 * @description:
 */
@Data
public class TranslationVo extends BaseVoEntity {
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;
    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    private Long fkTableId;
    /**
     * 翻译配置Id
     */
    @ApiModelProperty(value = "翻译配置Id")
    private Long fkTranslationMappingId;
    /**
     * 语言枚举code：zh-hk/en-us
     */
    @ApiModelProperty(value = "语言枚举code：zh-hk/en-us")
    private String languageCode;
    /**
     * 翻译内容
     */
    @ApiModelProperty(value = "翻译内容")
    private String translation;
}
