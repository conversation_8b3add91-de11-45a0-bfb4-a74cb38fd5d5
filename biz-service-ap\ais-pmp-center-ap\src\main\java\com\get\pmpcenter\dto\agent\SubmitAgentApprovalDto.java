package com.get.pmpcenter.dto.agent;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/3/24
 * @Version 1.0
 * @apiNote:提交审核请求参数
 */
@Data
public class SubmitAgentApprovalDto {

    @ApiModelProperty(value = "提供商ID")
    @NotNull(message = "提供商ID不能为空")
    private Long institutionProviderId;

    @ApiModelProperty(value = "分公司ID")
    @NotNull(message = "分公司ID不能为空")
    private Long companyId;

    @ApiModelProperty(value = "审核人ID")
    @NotNull(message = "审核人ID不能为空")
    private Long staffId;

    @ApiModelProperty(value = "审核人邮箱")
    @NotBlank(message = "审核人邮箱不能为空")
    private String email;

}
