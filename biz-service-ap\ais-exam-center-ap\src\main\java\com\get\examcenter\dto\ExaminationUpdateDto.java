package com.get.examcenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by <PERSON>.
 * Time: 9:43
 * Date: 2021/8/23
 * Description:考试管理列表Vo
 */
@Data
public class ExaminationUpdateDto extends BaseVoEntity implements Serializable {
    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    @Min(value = 1, message = "缺少公司id参数")
    @NotNull(message = "公司Id不能为空", groups = {Add.class, Update.class})
    @Min(value = 1, message = "缺少公司id参数", groups = {Add.class, Update.class})
    private Long fkCompanyId;

    /**
     * 考试编号
     */
    @ApiModelProperty(value = "考试编号")
    private String num;

    /**
     * 考试名称
     */
    @ApiModelProperty(value = "考试名称")
    @NotBlank(message = "考试名称不能为空", groups = {Add.class, Update.class})
    private String name;

    /**
     * 开放时间
     */
    @ApiModelProperty(value = "开放时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @NotNull(message = "是否激活不能为空", groups = {Add.class, Update.class})
    private Boolean isActive;
}
