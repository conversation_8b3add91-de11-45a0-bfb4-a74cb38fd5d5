package com.get.salecenter.utils;

import com.get.common.consts.AESConstant;
import com.get.common.utils.AESUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;

/**
 * 代理在线申请表单工具类
 */
public class ApplyAgentOnlineFormUtils {

    /**
     * 获取代理在线申请表单回显链接
     *
     * @param fkAppAgentId 学生代理申请Id
     * @param fkCompanyId  公司id
     * @return
     */
    public static String getAppAgentFormDetailLink(Long fkAppAgentId, Long fkCompanyId) {
        String encrypt = null;
        try {
            encrypt = AESUtils.Encrypt(String.valueOf(fkAppAgentId), AESConstant.AESKEY);
        } catch (Exception e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("sign_encryption_failed"));
        }
        String link = "";
        if (fkCompanyId.equals(2L)) {
            link = "https://app.geteducation.online/apply-agent-online-form/?sign=" + encrypt;
        } else {
            link = "https://app.ht-international.online/apply-agent-online-form/?sign=" + encrypt;
        }
        return link;
    }

}
