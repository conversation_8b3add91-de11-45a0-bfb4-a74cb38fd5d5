package com.get.permissioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.permissioncenter.vo.OfficeVo;
import com.get.permissioncenter.service.IOfficeService;
import com.get.permissioncenter.dto.OfficeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jdk.nashorn.internal.ir.annotations.Ignore;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/6/24
 * @TIME: 18:22
 **/


@Api(tags = "办公室管理")
@RestController
@RequestMapping("permission/office")
public class OfficeController {
    @Resource
    IOfficeService officeService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.permissioncenter.vo.OfficeVo>
     * @Description: 列表数据
     * @Param [officeVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/办公室管理/查询办公室")
    @PostMapping("datas")
    public ResponseBo<OfficeVo> datas(@RequestBody OfficeDto searchBean) {
        List<OfficeVo> officeVos = officeService.getOfficeDtos(searchBean);
        return new ListResponseBo<>(officeVos);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.permissioncenter.vo.OfficeVo>
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "权限中心/办公室管理/参数详情")
    @GetMapping("/{id}")
    public ResponseBo<OfficeVo> detail(@PathVariable("id") Long id) {
        OfficeVo officeVo = officeService.findOfficeById(id);
        return new ResponseBo<>(officeVo);
    }

    /**
     * 新增信息
     *
     * @param officeDto
     * @return
     */
    @ApiOperation(value = "新增接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/办公室管理/新增参数")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(OfficeDto.Add.class) OfficeDto officeDto) {
        return SaveResponseBo.ok(officeService.addOffice(officeDto));
    }

    /**
     * 批量新增信息
     *
     * @param officeDtos
     * @return
     */
    @ApiOperation(value = "批量新增接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/办公室管理/批量新增")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(OfficeDto.Add.class) ValidList<OfficeDto> officeDtos) {
        officeService.batchAddOffice(officeDtos);
        return SaveResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param officeDto
     * @return
     */
    @ApiOperation(value = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/办公室管理/更新参数")
    @PostMapping("update")
    public ResponseBo<OfficeVo> update(@RequestBody @Validated(OfficeDto.Update.class) OfficeDto officeDto) {
        return UpdateResponseBo.ok(officeService.updateOfficeVo(officeDto));
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.DELETE, description = "权限中心/办公室管理/删除资源")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        officeService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * 上移下移
     *
     * @param officeDtos
     * @return
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/办公室管理/排序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<OfficeDto> officeDtos) {
        officeService.movingOrder(officeDtos);
        return ResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 办公室下拉框数据
     * @Param [id]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "办公室下拉框数据", notes = "id公司id")
    @PostMapping("getOfficeSelect")
    public ResponseBo<BaseSelectEntity> getOfficeList(@RequestParam("id") Long companyId) {
        List<BaseSelectEntity> datas = officeService.getOfficeSelect(companyId);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return java.lang.String
     * @Description :feign调用 根据办公室id查找办公室名称
     * @Param [officeId]
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getOfficeNameById")
    public String getOfficeNameById(@RequestParam(required = false, value = "officeId") Long officeId) {
        return officeService.getOfficeNameById(officeId);
    }


    @Ignore
    @ApiOperation("根据id获取办公室")
    @GetMapping("getOfficeById")
    public OfficeVo getOfficeById(@RequestParam("id") Long officeId) {
        OfficeVo officeById = officeService.getOfficeById(officeId);
        return officeById;
    }

    @ApiIgnore
    @PostMapping("/getofficeNamesByIds")
    public Map<Long, String> getofficeNamesByIds(@RequestBody Set<Long> officeIds) {
        return officeService.getofficeNamesByIds(officeIds);
    }


}
