package com.get.rocketmq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.api.Result;
import com.get.rocketmqcenter.dto.InsurancePlanMessageDto;
import com.get.salecenter.feign.ISaleCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 澳小保应收应付队列监听
 */
@Component
@Slf4j
@RocketMQMessageListener(
        consumeThreadMax = 10,
        topic = "insurance_receivable_and_payable_topic", // topic
        consumerGroup = "insurance_receivable_and_payable_topic_consumer_group", // 消费组
        maxReconsumeTimes = 3, //最大重试次数
        consumeMode = ConsumeMode.ORDERLY
)
public class InsurancePlanListener implements RocketMQListener<InsurancePlanMessageDto> {

    @Resource
    private ISaleCenterClient saleCenterClient;


    /**
     * 澳小保创建应收应付
     * @param insurancePlanMessageDto
     */
    @Override
    public void onMessage(InsurancePlanMessageDto insurancePlanMessageDto) {
        try {
            Result result = saleCenterClient.createInsurancePlan(insurancePlanMessageDto);
            if (!result.isSuccess()) {
                throw new GetServiceException(result.getMessage());
            }
        } catch (Exception e) {
            log.error("澳小保应收应付创建失败<|>insurancePlanMessageDto={}", JSONObject.toJSONString(insurancePlanMessageDto));
            e.printStackTrace();
            throw new GetServiceException(e.getMessage());
        }
    }

}
