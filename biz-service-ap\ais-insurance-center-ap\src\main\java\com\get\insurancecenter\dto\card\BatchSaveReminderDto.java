package com.get.insurancecenter.dto.card;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/7/25
 * @Version 1.0
 * @apiNote:批量保存信用卡通知配置参数
 */
@Data
public class BatchSaveReminderDto {

    @ApiModelProperty(value = "醒配置信息")
    @NotNull(message = "提醒配置信息不能为空")
    private SaveCreditCardReminderDto saveCreditCardReminderDto;

    @ApiModelProperty(value = "信用卡IDS")
    @NotNull(message = "信用卡ID不能为空")
    private List<Long> creditCardIds;
}
