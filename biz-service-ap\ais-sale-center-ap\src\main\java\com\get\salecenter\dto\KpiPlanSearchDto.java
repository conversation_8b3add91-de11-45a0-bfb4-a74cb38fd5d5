package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2024/4/16
 * @TIME: 14:20
 * @Description:
 **/
@Data
public class KpiPlanSearchDto {

    @ApiModelProperty(value = "公司ID集合")
    private String fkCompanyIds;


    @ApiModelProperty(value = "标题/描述关键字")
    private String keyword;

    @ApiModelProperty(value = "定时统计：0关闭/1开启")
    private Integer isEnableScheduledCount;
}
