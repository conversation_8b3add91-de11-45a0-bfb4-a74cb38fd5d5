package com.get.insurancecenter.vo.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.get.insurancecenter.config.BigDecimalTwoScaleSerializer;
import com.get.insurancecenter.entity.InsuranceOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @Author:Oliver
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:订单详情
 */
@Data
public class OrderDetailVo extends InsuranceOrder {

    @ApiModelProperty(value = "保险公司名称")
    private String insuranceCompanyName;

    @ApiModelProperty(value = "保险产品类型名称")
    private String productTypeName;

    @ApiModelProperty(value = "保险产品类型key")
    private String productTypeKey;

    @ApiModelProperty(value = "所属公司编号")
    private String companyNum;

    @ApiModelProperty(value = "所属公司名称")
    private String companyName;

    @ApiModelProperty(value = "代理编号")
    private String agentNum;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "创建人名称")
    private String gmtCreateName;

    @ApiModelProperty(value = "订单状态(展示以此字段为准):-2:下单失败;1:下单中;2:已完成待生效;3:生效中;4:已失效;5:已退款(退保);6:已取消;")
    private Integer showStatus;

    @ApiModelProperty(value = "费率%(代理)-应付计划")
    private BigDecimal commissionRate;

    @ApiModelProperty(value = "佣金金额(代理)-应付计划")
    private BigDecimal commissionAmount;

    @ApiModelProperty(value = "结算状态：0待确认/1已确认/2代理确认/3财务确认/4结算完成")
    private Integer statusSettlement;

    @ApiModelProperty(value = "应付计划Id")
    private Long payablePlanId;

    @ApiModelProperty(value = "代理提交结算批次编号")
    private String numOptBatch;

    @ApiModelProperty(value = "应付金额")
    @JsonSerialize(using = BigDecimalTwoScaleSerializer.class)
    private BigDecimal payableAmount;

    @ApiModelProperty(value = "实付金额")
    @JsonSerialize(using = BigDecimalTwoScaleSerializer.class)
    private BigDecimal actualAmount;

    @ApiModelProperty(value = "实付金额币种")
    private String actualCurrencyTypeNum;

    @ApiModelProperty(value = "手续费")
    private BigDecimal serviceFee;


    public BigDecimal getServiceFee() {
        return serviceFee == null ? BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP)
                : serviceFee.setScale(2, RoundingMode.HALF_UP);
    }
}
