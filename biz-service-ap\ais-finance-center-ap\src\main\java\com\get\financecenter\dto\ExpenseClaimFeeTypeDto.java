package com.get.financecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Sea
 * @create: 2021/4/6 16:55
 * @verison: 1.0
 * @description:
 */
@Data
public class ExpenseClaimFeeTypeDto  extends BaseVoEntity {
    /**
     * 费用报销单类型名称
     */
    @ApiModelProperty(value = "费用报销单类型名称")
    private String typeName;

    @ApiModelProperty(value = "科目Id")
    private Long fkAccountingItemId;

    @ApiModelProperty(value = "关联类型Key（目标类型表名）")
    private String relationTargetKey;

    @ApiModelProperty(value = "关键字")
    private String keyWord;
}
