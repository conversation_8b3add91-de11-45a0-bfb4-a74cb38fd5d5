package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/11/9
 * @TIME: 16:17
 * @Description:
 **/
@Data
public class StudentAgentVo extends BaseEntity {

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty("代理编号")
    private String agentNum;

    @ApiModelProperty(value = "bd编号")
    private String bdCode;

    @ApiModelProperty(value = "bd名称")
    private String bdName;

    @ApiModelProperty(value = "员工id")
    private Long staffId;

    @ApiModelProperty(value = "邮箱")
    private Set<String> emails;

    @ApiModelProperty("代理标签")
    private List<AgentLabelVo> agentLabelVos;

    @ApiModelProperty(value = "代理电邮标签")
    private List<AgentLabelVo> agentEmailLabelVos;

    //==========实体类StudentAgent==============
    private static final long serialVersionUID = 1L;
    /**
     * 学生Id
     */
    @ApiModelProperty(value = "学生Id")
    @Column(name = "fk_student_id")
    private Long fkStudentId;
    /**
     * 代理Id
     */
    @ApiModelProperty(value = "代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
    /**
     * 绑定时间
     */
    @ApiModelProperty(value = "绑定时间")
    @Column(name = "active_date")
    private Date activeDate;
    /**
     * 取消绑定时间（下次绑定时，需要重新建立记录）
     */
    @ApiModelProperty(value = "取消绑定时间（下次绑定时，需要重新建立记录）")
    @Column(name = "unactive_date")
    private Date unactiveDate;
}
