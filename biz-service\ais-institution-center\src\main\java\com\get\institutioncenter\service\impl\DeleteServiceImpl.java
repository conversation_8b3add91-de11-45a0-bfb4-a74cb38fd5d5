package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.*;
import com.get.institutioncenter.entity.Character;
import com.get.institutioncenter.entity.*;
import com.get.institutioncenter.service.*;
import com.get.platformconfigcenter.feign.IPlatformConfigCenterClient;
import com.get.salecenter.feign.ISaleCenterClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/11/25
 * @TIME: 14:41
 * @Description:
 **/
@Service
public class DeleteServiceImpl implements IDeleteService {
    @Resource
    private InstitutionFacultyMapper institutionFacultyMapper;
    @Resource
    private InstitutionProviderInstitutionMapper institutionProviderInstitutionMapper;
    @Resource
    private ContactPersonMapper contactPersonMapper;
    @Resource
    private NewsMapper newsMapper;
    @Lazy
    @Resource
    private IMediaAndAttachedService attachedService;
    @Lazy
    @Resource
    private INewsService newsService;
    @Resource
    private AreaCountryInfoMapper areaCountryInfoMapper;
    @Resource
    private CharacterMapper characterMapper;
    @Resource
    private AreaCityInfoMapper areaCityInfoMapper;
    @Resource
    private AreaCityDivisionMapper areaCityDivisionMapper;
    @Resource
    private ContractMapper contractMapper;
    @Resource
    private ContractFormulaMapper contractFormulaMapper;
    @Resource
    private InstitutionProviderMapper institutionProviderMapper;
    @Resource
    private IAppInfoService appInfoService;
    @Resource
    private IInstitutionCourseAcademicScoreService institutionCourseAcademicScoreService;
    @Resource
    private IInstitutionCourseEngScoreService institutionCourseEngScoreService;
    @Resource
    private IInstitutionCoursePathwayService institutionCoursePathwayService;
    @Resource
    private IInstitutionCourseRankingService institutionCourseRankingService;

    @Resource
    private InstitutionCourseMajorLevelMapper institutionCourseMajorLevelMapper;
    @Resource
    private InstitutionCourseTypeMapper institutionCourseTypeMapper;
    @Resource
    private InstitutionCourseZoneMapper institutionCourseZoneMapper;
    @Resource
    private InstitutionCourseFacultyMapper institutionCourseFacultyMapper;
    @Resource
    private ContractFormulaInstitutionCourseMapper contractFormulaInstitutionCourseMapper;
    @Resource
    private InstitutionCourseMapper institutionCourseMapper;
    @Resource
    private InstitutionCoursePathwayMapper institutionCoursePathwayMapper;
    @Resource
    private InstitutionProviderAreaCountryMapper institutionProviderAreaCountryMapper;
    @Resource
    private InstitutionProviderCompanyMapper institutionProviderCompanyMapper;
    @Resource
    private IInstitutionCourseStudyModeService institutionCourseStudyModeService;
    @Resource
    private IPlatformConfigCenterClient platformConfigCenterClient;
    @Resource
    private ISaleCenterClient saleCenterClient;

    /**
     * 删除学校 关系校验
     *
     * @Date 10:30 2021/4/16
     * <AUTHOR>
     */
    @Override
    public boolean deleteInstitutionRelation(Long institutionId) {
        boolean success = true;
        Integer facultyCount = institutionFacultyMapper.getFacultyCountByInstitutionId(institutionId);
        if (facultyCount > 0) {
            success = false;
            throw new GetServiceException(LocaleMessageUtils.getMessage("institution_faculty_data_association"));
        }
        Integer providerCount = institutionProviderInstitutionMapper.getCountByInstitutionId(institutionId);
        if (providerCount > 0) {
            success = false;
            throw new GetServiceException(LocaleMessageUtils.getMessage("institution_provider_data_association"));
        }
        Integer contactPersonCount = contactPersonMapper.getCountByInstitutionId(institutionId);
        if (contactPersonCount > 0) {
            success = false;
            throw new GetServiceException(LocaleMessageUtils.getMessage("institution_contactPerson_data_association"));
        }
        Integer newsCount = newsMapper.getCountByInstitutionId(institutionId);
        if (newsCount > 0) {
            success = false;
            throw new GetServiceException(LocaleMessageUtils.getMessage("institution_news_data_association"));
        }
        Character character = new Character();
        character.setFkTableName(TableEnum.INSTITUTION.key);
        character.setFkTableId(institutionId);
        if (characterMapper.isExistByMap(character)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("institution_news_data_association"));
        }
        //删除关联数据
        attachedService.deleteMediaAndAttachedByTableId(institutionId, TableEnum.INSTITUTION.key);
        return success;
    }

    //TODO 等下搞
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCourseRelation(Long courseId) {
        if (contractMapper.isExistByCourseId(courseId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("course_contract_data_association"));
        }
        if (institutionCourseMajorLevelMapper.isExistByCourseId(courseId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("course_coursemajorlevel_data_association"));
        }
        if (institutionCourseTypeMapper.isExistByCourseId(courseId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("course_coursetype_data_association"));
        }
        if (institutionCourseZoneMapper.isExistByCourseId(courseId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("course_coursezone_data_association"));
        }
        if (institutionCourseFacultyMapper.isExistByCourseId(courseId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("course_faculty_data_association"));
        }
        if (contractFormulaInstitutionCourseMapper.isExistByCourseId(courseId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("course_formulains_data_association"));
        }
        if (newsMapper.isExistByCourseId(courseId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("course_news_data_association"));
        }
//        Result<Boolean> result = platformConfigCenterClient.deleteValidateCourse(courseId);
//        if (!result.isSuccess()) {
//            throw new GetServiceException(result.getMessage());
//        }
//        if (!result.getData()) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("course_student_character_collection_data_association"));
//        }
        Result<Boolean> result1 = saleCenterClient.deleteValidateCourse(courseId);
        if (!result1.isSuccess()) {
            throw new GetServiceException(result1.getMessage());
        }
        if (!result1.getData()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("course_item_data_association"));
        }
        //同时删除该表id下的所有媒体附件
        List<MediaAndAttached> mediaAndAttachedByTableIds = attachedService.findMediaAndAttachedByTableId(courseId, TableEnum.INSTITUTION_COURSE.key, null);
        for (MediaAndAttached mediaAndAttached : mediaAndAttachedByTableIds) {
            attachedService.deleteMediaAttached(mediaAndAttached.getId());
        }
        institutionCourseStudyModeService.deleteModeByCourseId(courseId);
        appInfoService.deleteAppInfoByCourseId(courseId);
        institutionCourseAcademicScoreService.deleteCourseAcademicScoreByCourseId(courseId);
        institutionCourseEngScoreService.deleteInstitutionCourseEngScoreByCourseId(courseId);
        institutionCoursePathwayService.deleteCoursePathwayByCourseId(courseId);
        institutionCoursePathwayService.deleteCoursePathwayByCoursePathwayId(courseId);
        institutionCourseRankingService.deleteCourseRankingByCourseId(courseId);
    }

    @Override
    public boolean deleteValidateAreaCountryInfoType(Long areaCountryInfoTypeId) {
        LambdaQueryWrapper<AreaCountryInfo> wrapper = new LambdaQueryWrapper();
        wrapper.in(AreaCountryInfo::getFkAreaCountryInfoTypeId, areaCountryInfoTypeId);
        List<AreaCountryInfo> areaCountryInfos = areaCountryInfoMapper.selectList(wrapper);
        if (GeneralTool.isNotEmpty(areaCountryInfos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("countryInfoType_countryInfo_data_association"));
        }
        return true;
    }

    @Override
    public boolean deleteValidateAreaCountry(Long areaCountryId) {
        if (areaCountryInfoMapper.areaCountryInfoIsEmpty(areaCountryId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("country_countryInfo_data_association"));
        }
        if (newsMapper.newsIsEmpty(areaCountryId, TableEnum.INSTITUTION_COUNTRY.key)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("country_news_data_association"));
        }
        //同时删除该表id下的所有媒体附件
        attachedService.deleteMediaAndAttachedByTableId(areaCountryId, TableEnum.INSTITUTION_COUNTRY.key);
        return true;
    }

    @Override
    public boolean deleteValidateAreaCityInfoType(Long areaCityInfoTypeId) {
        LambdaQueryWrapper<AreaCityInfo> wrapper = new LambdaQueryWrapper();
        wrapper.in(AreaCityInfo::getFkAreaCityInfoTypeId, areaCityInfoTypeId);
        List<AreaCityInfo> areaCityInfos = areaCityInfoMapper.selectList(wrapper);
        if (GeneralTool.isNotEmpty(areaCityInfos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("cityInfoType_cityInfo_data_association"));
        }
        return true;
    }

    @Override
    public boolean deleteValidateAreaCity(Long areaCityId) {
        if (areaCityInfoMapper.areaCityInfoIsEmpty(areaCityId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("city_cityInfo_data_association"));
        }
        if (areaCityDivisionMapper.areaCityDivisionIsEmpty(areaCityId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("city_cityDivision_data_association"));
        }
        //同时删除该表id下的所有媒体附件
        attachedService.deleteMediaAndAttachedByTableId(areaCityId, TableEnum.INSTITUTION_AREA_CITY.key);
        return true;
    }


    /**
     * 删除学校提供商校验
     *
     * @Date 10:20 2021/4/16
     * <AUTHOR>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteValidateProviderByProviderId(Long providerId) {
        if (institutionProviderInstitutionMapper.providerInfoIsEmpty(providerId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("provider_institution_data_association"));
        }
        if (contactPersonMapper.checkProviderInfoIsEmptyByProviderId(providerId, TableEnum.INSTITUTION_PROVIDER.key)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("provider_contact_person_data_association"));
        }
        if (contractMapper.checkProviderInfoIsEmptyByProviderId(providerId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("provider_contract_data_association"));
        }
        if (contractFormulaMapper.checkProviderInfoIsEmptyByProviderId(providerId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("provider_contract_formula_data_association"));
        }
        if (newsMapper.newsIsEmpty(providerId, TableEnum.INSTITUTION_PROVIDER.key)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("provider_news_data_association"));
        }
        institutionProviderAreaCountryMapper.deleteByProviderId(providerId);
        institutionProviderCompanyMapper.deleteByProviderId(providerId);
        return true;
    }

    /**
     * @Description：删除提供商类型校验
     * @Param
     * @Date 11:46 2021/4/22
     * <AUTHOR>
     */
    @Override
    public boolean deleteValidateProviderType(Long providerTypeId) {
        LambdaQueryWrapper<InstitutionProvider> wrapper = new LambdaQueryWrapper();
        wrapper.in(InstitutionProvider::getFkInstitutionProviderTypeId, providerTypeId);
        List<InstitutionProvider> institutionProviders = institutionProviderMapper.selectList(wrapper);
        if (GeneralTool.isNotEmpty(institutionProviders)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("provider_type_provider_data_association"));
        }
        return true;
    }

    /**
     * 删除桥梁学校校验
     *
     * @Date 16:09 2021/7/27
     * <AUTHOR>
     */
    @Override
    public boolean deleteValidatePathwayCourse(InstitutionPathway institutionPathway) {
        LambdaQueryWrapper<InstitutionCourse> wrapper = new LambdaQueryWrapper();
        wrapper.in(InstitutionCourse::getFkInstitutionId, institutionPathway.getFkInstitutionId());
        List<InstitutionCourse> institutionCourses = institutionCourseMapper.selectList(wrapper);
        List<Long> courseId = institutionCourses.stream().map(InstitutionCourse::getId).collect(Collectors.toList());

        LambdaQueryWrapper<InstitutionCourse> wrapper1 = new LambdaQueryWrapper();
        wrapper.eq(InstitutionCourse::getFkInstitutionId, institutionPathway.getFkInstitutionIdPathway());
        List<InstitutionCourse> pathwayInstitutionCourses = institutionCourseMapper.selectList(wrapper1);
        List<Long> pathwayCoursesId = pathwayInstitutionCourses.stream().map(InstitutionCourse::getId).collect(Collectors.toList());

        LambdaQueryWrapper<InstitutionCoursePathway> wrapper2 = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(courseId) && GeneralTool.isNotEmpty(pathwayCoursesId)) {
            wrapper2.in(InstitutionCoursePathway::getFkInstitutionCourseId, courseId);
            wrapper2.in(InstitutionCoursePathway::getFkInstitutionCourseIdPathway, pathwayCoursesId);
            List<InstitutionCoursePathway> institutionCoursePathways = institutionCoursePathwayMapper.selectList(wrapper2);
            if (GeneralTool.isNotEmpty(institutionCoursePathways)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("course_pathway_course_data_association"));
            }
        }
        return true;
    }

}
