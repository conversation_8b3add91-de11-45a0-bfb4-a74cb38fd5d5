package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("m_stage_value_task_queue")
public class StageValueTaskQueue extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "表名，操作类型：m_stage_profit_and_loss_value损益表/m_stage_balance_sheet_value资产负债表/m_stage_accounting_item_value科目")
    private String fkTableName;

    @ApiModelProperty(value = "参数：开始年月")
    private String startYearMonth;

    @ApiModelProperty(value = "参数：分公司Id串")
    private String fkCompanyIds;

    @ApiModelProperty(value = "开始执行时间")
    private Date startTime;

    @ApiModelProperty(value = "结束执行时间")
    private Date endTime;

    @ApiModelProperty(value = "状态：0等待/1执行中/2完成/3错误")
    private Integer status;

    @ApiModelProperty(value = "执行信息，错误信息等")
    private String message;

}
