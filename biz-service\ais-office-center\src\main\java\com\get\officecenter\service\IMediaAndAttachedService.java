package com.get.officecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.filecenter.dto.FileDto;
import com.get.officecenter.vo.OfficeMediaAndAttachedVo;
import com.get.officecenter.entity.OfficeMediaAndAttached;
import com.get.officecenter.dto.MediaAndAttachedDto;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * @author: Sea
 * @create: 2020/8/14 9:57
 * @verison: 1.0
 * @description:
 */
public interface IMediaAndAttachedService extends IService<OfficeMediaAndAttached> {

    /**
     * 上传文件
     *
     * @param multipartFiles
     * @return
     */
    List<FileDto> upload(MultipartFile[] multipartFiles);

    /**
     * 上传附件
     *
     * @param multipartFiles
     * @return
     * @
     */
    List<FileDto> uploadAppendix(MultipartFile[] multipartFiles);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    void deleteMediaAttached(Long id);

    /**
     * 保存附件
     *
     * @param mediaAttachedVo
     * @return
     */
    OfficeMediaAndAttachedVo addMediaAndAttached(MediaAndAttachedDto mediaAttachedVo);

    /**
     * 通过附件GUID获取DTO
     *
     * @param attachedVo
     * @return
     * @
     */
    List<OfficeMediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo);

    /**
     * 通过附件GUID分页获取DTO
     *
     * @param attachedVo
     * @return
     * @
     */
    List<OfficeMediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo, Page page);


    /**
     * 根据媒体附件id修改表id
     *
     * @param id
     * @param tableId
     */
    void updateTableId(Long id, Long tableId);

    /**
     * 根据表id删除对应媒体附件
     *
     * @param tableId
     * @return
     */
    void deleteMediaAndAttachedByTableId(Long tableId, String tableName);

    /**
     * 获取代理附件类型
     *
     * @return
     */
    List<Map<String, Object>> findAgentMediaType();


    /**
     * 获取代理合同附件类型
     *
     * @return
     */
    List<Map<String, Object>> findContractMediaType();


    /**
     * @return void
     * @Description :上移下移
     * @Param [mediaAttachedVos]
     * <AUTHOR>
     */
    void movingOrder(List<MediaAndAttachedDto> mediaAttachedVos);

    /**
     * 批量保存附件
     * @param mediaAndAttachedDtos
     * @return
     */
    Boolean saveBatchMediaAndAttached(List<MediaAndAttachedDto> mediaAndAttachedDtos);
}
