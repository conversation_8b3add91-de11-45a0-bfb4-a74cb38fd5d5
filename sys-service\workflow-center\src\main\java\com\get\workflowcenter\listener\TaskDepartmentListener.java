package com.get.workflowcenter.listener;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.SpringUtil;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.workflowcenter.component.IWorkFlowHelper;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.TaskService;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.activiti.engine.repository.ProcessDefinition;

import java.util.List;
import java.util.StringJoiner;

/**
 * @author: Sea
 * @create: 2021/3/24 10:23
 * @verison: 1.0
 * @description: 流程节点用来获取任务部门设置候选人的监听器
 */
public class TaskDepartmentListener implements TaskListener {
    @Override
    public void notify(DelegateTask task) {
        System.out.println("进入任务部门监听--------------------------------");
        //获取feign调用service
        IPermissionCenterClient feignPermissionService = SpringUtil.getBean(IPermissionCenterClient.class);
        IWorkFlowHelper workFlowHelper = SpringUtil.getBean(IWorkFlowHelper.class);
        TaskService taskService = SpringUtil.getBean(TaskService.class);

        //获取流程名称
        ProcessEngine processEngine = SpringUtil.getBean(ProcessEngine.class);

        ProcessDefinition processDefinition = processEngine.getRepositoryService()
                .createProcessDefinitionQuery()
                .processDefinitionId(task.getProcessDefinitionId())
                .singleResult();

        StaffVo staffVo = workFlowHelper.getStaffDto(task);

        //通过登录人公司id和描述中设置的部门id feign调用查找该部门全部员工id
        String departmentId = task.getDescription();
        Result<List<Long>> result = feignPermissionService.getAllDepartmentStaffIds(SecureUtil.getFkCompanyId(), Long.valueOf(departmentId));
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            JSONArray objects = JSONUtil.parseArray(result.getData());
            List<String> staffIdList = JSONUtil.toList(objects, String.class);
            //有就全部设置为候选人
            task.addCandidateUsers(staffIdList);
            StringJoiner stringJoiner = new StringJoiner(",");
            stringJoiner.add(ProjectKeyEnum.WORKFLOW_CENTER.key).add(ProjectKeyEnum.WORKFLOW_CENTER_TO_SIGN.key);
            if (TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key.equals(processDefinition.getKey())) {
                stringJoiner.add(ProjectKeyEnum.OFFICE_CENTER.key);
            }
            workFlowHelper.sendMessage(staffVo, staffIdList, processDefinition, "待签取", task, stringJoiner.toString(),null);
        }


    }
}
