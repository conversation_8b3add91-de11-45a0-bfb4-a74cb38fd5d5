package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.institutioncenter.dto.InstitutionDeadlineInfoDto;
import com.get.institutioncenter.vo.InstitutionDeadlineInfoVo;
import com.get.institutioncenter.entity.InstitutionDeadlineInfo;
import com.get.institutioncenter.dto.WeScholarshipAppDto;
import com.get.institutioncenter.dto.query.InstitutionDeadlineInfoQueryDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface InstitutionDeadlineInfoMapper extends BaseMapper<InstitutionDeadlineInfo> {

    List<InstitutionDeadlineInfoVo> getWcInstitutionDeadlineInfoList(IPage<InstitutionDeadlineInfoDto> ipage,
                                                                     @Param("schoolName") String schoolName,
                                                                     @Param("fkCountryId") Long fkCountryId);

    Date getNewCreateTime();

    InstitutionDeadlineInfoVo selectInfoById(Long id);

    List<InstitutionDeadlineInfoVo> datas(IPage<InstitutionDeadlineInfo> pages, @Param("data") InstitutionDeadlineInfoQueryDto data, @Param("countryIds") List<Long> countryIds);

    List<InstitutionDeadlineInfoVo> getWcInstitutionDeadlineInfoDtoDatas(@Param("weScholarshipAppDto") WeScholarshipAppDto weScholarshipAppDto, @Param("fkTableName")String fkTableName);

    /**
     * 申请截止信息优先匹配
     * @param weScholarshipAppDto
     * @param priorityTypeKey
     * @param fkTableName
     * @return
     */
    List<InstitutionDeadlineInfoVo> priorityMatchingQuery(@Param("weScholarshipAppDto") WeScholarshipAppDto weScholarshipAppDto,
                                                          @Param("priorityTypeKey") Map<Integer,String> priorityTypeKey, @Param("fkTableName")String fkTableName);

    List<InstitutionDeadlineInfoVo> selectInfoByIds(List<Long> ids);
}