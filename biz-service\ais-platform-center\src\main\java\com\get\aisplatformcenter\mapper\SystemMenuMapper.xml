<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.get.aisplatformcenter.mapper.SystemMenuMapper">

    <select id="selectPartnerMenuList" resultType="com.get.aisplatformcenterap.entity.SystemMenu">
        select sm.* from app_system_center.system_menu sm
        where 1=1
        <if test="getPermissionMenuDto.fkPlatformCode != null and getPermissionMenuDto.fkPlatformCode != ''">
            AND sm.fk_platform_code = #{getPermissionMenuDto.fkPlatformCode}
        </if>
    </select>
</mapper>
