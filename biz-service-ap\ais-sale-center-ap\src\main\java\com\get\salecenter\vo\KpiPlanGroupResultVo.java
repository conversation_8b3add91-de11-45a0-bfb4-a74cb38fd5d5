package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2024/4/16
 * @TIME: 16:27
 * @Description:
 **/
@Data
public class KpiPlanGroupResultVo extends BaseEntity {

    @ApiModelProperty(value = "公司ID")
    private String fkCompanyIds;

    @ApiModelProperty(value = "考核人员列表")
    private List<KpiPlanStaffVo> kpiPlanStaffDtoList;

    @ApiModelProperty(value = "考核方案组别或子项列表")
    private List<KpiPlanGroupOrItemVo> kpiPlanGrouporItemDtoList;

    @ApiModelProperty(value = "目标设置人Id")
    private Long fkStaffIdAdd;
}
