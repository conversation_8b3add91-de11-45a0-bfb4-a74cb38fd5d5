package com.get.permissioncenter.utils;

import com.get.core.tool.utils.GeneralTool;

import java.util.Collection;

/**
 * <AUTHOR>
 * @DATE: 2020/8/14
 * @TIME: 11:28
 * @Description: 权限中心编码规则工具类
 **/
public class MyStringUtils {


    /**
     * 权限组别编号
     *
     * @param num
     * @return
     */
    public static String getGroupNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 5) {
            code = String.format("%04d", num);
        }
        return "PG" + code;
    }

    /**
     * 权限级别编号
     *
     * @param num
     * @return
     */
    public static String getGradeNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 5) {
            code = String.format("%04d", num);
        }
        return "PL" + code;
    }


    /**
     * 员工编号
     *
     * @param num
     * @return
     */
    public static String getStaffNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 6) {
            code = String.format("%05d", num);
        }
        return "SF" + code;
    }

    /**
     * 将集合拼接成一个string（in语句）
     *
     * @param collection
     * @return
     */
    public static <E> String getSqlString(Collection<E> collection) {
        if (GeneralTool.isEmpty(collection)) {
            return "";
        }
        StringBuffer sb = new StringBuffer();
        sb.append("(");
        for (E e : collection) {
            if (GeneralTool.isEmpty(e)) {
                continue;
            }
            if (!Number.class.equals(e.getClass().getSuperclass())) {
                sb.append("\"").append(String.valueOf(e)).append("\"").append(",");
            } else {
                sb.append(String.valueOf(e)).append(",");
            }
        }
        sb.deleteCharAt(sb.toString().length() - 1);
        sb.append(")");
        return sb.toString();
    }

}
