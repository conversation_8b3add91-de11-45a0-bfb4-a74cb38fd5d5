package com.get.officecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class TaskStatisticsVo {

    @ApiModelProperty("接收人名称")
    private String recipientStaffName;

    @ApiModelProperty("子任务完成状态数量")
    private Integer taskItemFinishedCount;

    @ApiModelProperty("子任务未能完成状态数量")
    private Integer taskItemUnfinishedCount;

    @ApiModelProperty("子任务总数")
    private Integer taskItemTotal;

    @ApiModelProperty("子任务待处理状态数量")
    private Integer taskItemTodoCount;

    @ApiModelProperty("第一次完成时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date firstTime;

    @ApiModelProperty("最后一次完成时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastTime;

}
