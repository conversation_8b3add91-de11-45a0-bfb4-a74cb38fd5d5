package com.get.financecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import com.get.financecenter.entity.ReceiptFormItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/12/31
 * @TIME: 11:35
 * @Description:
 **/
@Data
public class ReceiptFormItemVo extends BaseEntity {

    /**
     * 已收
     */
    @ApiModelProperty(value = "已收")
    List<AlreadyReceiptVo> alreadyReceiptDtos;
    @ApiModelProperty(value = "应付类型名称")
    private String fkTypeName;
    /**
     * 记录总名称
     */
    @ApiModelProperty(value = "记录总名称")
    private String targetNames;
    /**
     * 应付金额
     */
    @ApiModelProperty(value = "应收金额")
    private BigDecimal receivablePlanAmount;
    /**
     * 应付币种
     */
    @ApiModelProperty(value = "应收币种")
    private String receivablePlanCurrency;
    @ApiModelProperty(value = "应收币种名")
    private String receivablePlanCurrencyName;
    /**
     * 实付币种
     */
    @ApiModelProperty(value = "实收币种")
    private String receiptFormCurrency;
    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司")
    private Long fkCompanyId;

    /**
     * 手续费
     */
    @ApiModelProperty(value = "手续费")
    private BigDecimal serviceFee;

    /**
     * 延迟入学标记
     */
    @ApiModelProperty(value = "延迟入学标记")
    private Boolean isDeferEntrance;

    @ApiModelProperty(value = "学生号")
    private String studentId;

    /**
     * 延迟入学时间
     */
    @ApiModelProperty(value = "延迟入学时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date maxDeferEntranceTimes;

    /**
     * 是否勾选
     */
    @ApiModelProperty(value = "是否勾选")
    private Boolean isSelected;

    /**
     * 发票编号
     */
    @ApiModelProperty(value = "发票编号")
    private String fkInvoiceNum;

    /**
     * 原币种
     */
    @ApiModelProperty(value = "原币种")
    private String fkCurrencyTypeNumOrc;

    /**
     * 实收金额
     */
    @ApiModelProperty(value = "实收金额")
    private String fkCurrencyTypeNumReceipt;

    @ApiModelProperty(value = "学生信息")
    private String studentInformation;

    @ApiModelProperty(value = "业务信息")
    private String businessInformation;

    @ApiModelProperty(value = "渠道信息")
    private String channelInformation;

    /**
     * 学习计划id
     */
    private Long fkStudentOfferItemId;

    @ApiModelProperty(value = "币种")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "银行名字")
    private String bankName;

    @ApiModelProperty("发票绑定金额")
    private BigDecimal subtotal;


    @ApiModelProperty(value = "原币折算")
    private BigDecimal amountCurrencyConversion;

    @ApiModelProperty(value = "原币手续费")
    private BigDecimal amountServiceFeeConversion;

    @ApiModelProperty(value = "实收汇率")
    private BigDecimal exchangeRate;

    @ApiModelProperty(value = "到账时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date receiptDate;


    //==============实体类ReceiptFormItem===============
    private static final long serialVersionUID = 1L;
    /**
     * 收款单Id
     */
    @ApiModelProperty(value = "收款单Id")
    private Long fkReceiptFormId;
    /**
     * 应收计划Id
     */
    @ApiModelProperty(value = "应收计划Id")
    private Long fkReceivablePlanId;
    /**
     * 收款金额（拆分/总）
     */
    @ApiModelProperty(value = "收款金额（拆分/总）")
    private BigDecimal amountReceipt;
    /**
     * 实收汇率
     */
    @ApiModelProperty(value = "实收汇率")
    private BigDecimal exchangeRateReceivable;
    /**
     * 收款金额（折合应收币种金额）
     */
    @ApiModelProperty(value = "收款金额（折合应收币种金额）")
    private BigDecimal amountReceivable;
    /**
     * 汇率调整（可正可负，为平衡计算应收金额）
     */
    @ApiModelProperty(value = "汇率调整（可正可负，为平衡计算应收金额）")
    private BigDecimal amountExchangeRate;
    /**
     * 汇率（折合港币）
     */
    @ApiModelProperty(value = "汇率（折合港币）")
    private BigDecimal exchangeRateHkd;
    /**
     * 收款金额（折合港币）
     */
    @ApiModelProperty(value = "收款金额（折合港币）")
    private BigDecimal amountHkd;
    /**
     * 汇率（折合人民币）
     */
    @ApiModelProperty(value = "汇率（折合人民币）")
    private BigDecimal exchangeRateRmb;
    /**
     * 收款金额（折合人民币）
     */
    @ApiModelProperty(value = "收款金额（折合人民币）")
    private BigDecimal amountRmb;

    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;

}
