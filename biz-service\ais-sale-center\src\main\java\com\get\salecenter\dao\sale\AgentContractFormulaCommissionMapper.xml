<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.AgentContractFormulaCommissionMapper">

  <select id="getMaxStep" resultType="java.lang.Integer">
    select
     IFNULL(max(step)+1,0) step
    from
     m_agent_contract_formula_commission
    where
     fk_agent_contract_formula_id = #{agentContractFormulaId}

  </select>
</mapper>