package com.get.officecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.officecenter.vo.WorkScheduleDateConfigVo;
import com.get.officecenter.entity.WorkScheduleDateConfig;
import com.get.officecenter.dto.WorkScheduleDateConfigDto;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/1/13
 * @TIME: 11:27
 * @Description:
 **/
public interface WorkScheduleDateConfigService {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    WorkScheduleDateConfigVo findWorkScheduleDateConfigById(Long id);

    /**
     * 新增
     *
     * @param workScheduleDateConfigDto
     * @
     */
    void add(WorkScheduleDateConfigDto workScheduleDateConfigDto);

    /**
     * 删除
     *
     * @param id
     * @
     */
    void delete(Long id);

    /**
     * 修改
     *
     * @param workScheduleDateConfigDto
     * @return
     * @
     */
    WorkScheduleDateConfigVo updateWorkScheduleDateConfig(WorkScheduleDateConfigDto workScheduleDateConfigDto);

    /**
     * 列表
     *
     * @param workScheduleDateConfigDto
     * @param page
     * @return
     * @
     */
    List<WorkScheduleDateConfigVo> getWorkScheduleDateConfigs(WorkScheduleDateConfigDto workScheduleDateConfigDto, Page page);

    /**
     * 年份下拉
     *
     * @return
     */
    List<BaseSelectEntity> getYearSelect(Long fkCompanyId);

    /**
     * 全部排班数据
     *
     * @param workScheduleDateConfigDto
     * @return
     * @
     */
    List<WorkScheduleDateConfigVo> getAllWorkScheduleDateConfigs(WorkScheduleDateConfigDto workScheduleDateConfigDto);


    /**
     * 获取考勤范围时间所有排班
     * @param startTime
     * @param endTime
     * @param fkCompanyId
     * @return
     */
    List<WorkScheduleDateConfig> getWorkScheduleDateConfigByDate(Date startTime, Date endTime, Long fkCompanyId,Long fkDepartmentId);


}
