package com.get.permissioncenter.controller;

import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.permissioncenter.dto.workbench.WorkbenchApprovalDto;
import com.get.permissioncenter.enums.WorkbenchApprovalTypeEnum;
import com.get.permissioncenter.service.WorkbenchService;
import com.get.permissioncenter.vo.workbench.WorkbenchApprovalVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/11/6
 * @TIME: 14:30
 * @Description: 工作台控制器
 **/
@Api(tags = "工作台管理")
@RestController
@RequestMapping("permission/workbench")
public class WorkbenchController {
    @Resource
    private WorkbenchService workbenchService;


    @ApiOperation(value = "获取工作台审核列表")
    @PostMapping("/getWorkbenchApprovalList")
    public ResponseBo<WorkbenchApprovalVo> getWorkbenchApprovalList(@RequestBody SearchBean<WorkbenchApprovalDto> page) {
        List<WorkbenchApprovalVo> list = workbenchService.getWorkbenchApprovalList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(list, p);
    }

    @ApiOperation(value = "工作台审核类型列表")
    @GetMapping("/getWorkbenchApprovalTypeList")
    public ResponseBo<Map<String, String>> getWorkbenchApprovalTypeList() {
        return new ResponseBo<>(Arrays.stream(WorkbenchApprovalTypeEnum.values())
                .filter(enums -> !enums.equals(WorkbenchApprovalTypeEnum.WORK_LEAVE_FORM))
                .collect(Collectors.toMap(WorkbenchApprovalTypeEnum::getCode, WorkbenchApprovalTypeEnum::getMsg)));
    }

}
