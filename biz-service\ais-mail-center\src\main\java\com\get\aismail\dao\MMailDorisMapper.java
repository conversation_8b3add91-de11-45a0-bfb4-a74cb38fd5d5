package com.get.aismail.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.get.aismail.entity.MMailDoris;
import com.get.aismail.vo.SearchMailVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@DS("mail-doris")
@Mapper
public interface MMailDorisMapper extends BaseMapper<MMailDoris> {
    IPage<Long> selectMailDoris(Page<Long> page, @Param("searchMailVo") SearchMailVo searchMailVo);
/*    void deleteMailDoris(@Param("id") Long id);
    void insertDoris(MMailDoris mailDoris);*/
}
