package com.get.pmpcenter.feign;

import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.permissioncenter.vo.workbench.WorkbenchApprovalVo;
import com.get.pmpcenter.dto.institution.UnbindProviderInstitutionDto;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/3/24
 * @Version 1.0
 * @apiNote:
 */
@Component
@VerifyPermission(IsVerify = false)
public class PmpCenterClientFallBack implements IPmpCenterClient {
    @Override
    public Result<Boolean> unbindProviderInstitution(UnbindProviderInstitutionDto unbindProviderInstitutionDto) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<List<WorkbenchApprovalVo>> getWorkbenchApprovalList(Long staffId, String approvalType,Integer approvalStatus, String loginId) {
        return Result.fail("操作失败");
    }
}
