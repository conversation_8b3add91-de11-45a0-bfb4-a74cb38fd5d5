package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 支付申请单子项
 */
@Data
@TableName("m_payment_application_form_item")
public class PaymentApplicationFormItem extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "支付申请单Id")
    private Long fkPaymentApplicationFormId;

    @ApiModelProperty(value = "付款费用类型Id")
    private Long fkPaymentFeeTypeId;

    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "摘要")
    private String summary;

    @ApiModelProperty(value = "关联类型Key（目标类型表名）")
    private String relationTargetKey;

    @ApiModelProperty(value = "关联类型Id（目标类型表对应记录项Id）")
    private Long relationTargetId;

}