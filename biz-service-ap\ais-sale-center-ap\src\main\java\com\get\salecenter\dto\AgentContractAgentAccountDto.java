package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2021/6/28 16:07
 */
@Data
public class AgentContractAgentAccountDto extends BaseVoEntity{
    /**
     * 学生代理合同Id
     */
    @ApiModelProperty(value = "学生代理合同Id")
    @NotNull(message = "学生代理合同Id不能为空", groups = {Add.class, Update.class})
    private Long fkAgentContractId;

    /**
     * 学生代理合同账户Id
     */
    @ApiModelProperty(value = "学生代理合同账户Id")
    @NotNull(message = "学生代理合同Id不能为空", groups = {Add.class, Update.class})
    private Long fkAgentContractAccountId;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @NotNull(message = "币种编号不能为空", groups = {Add.class, Update.class})
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "是否同时激活账户")
    private Boolean allActivate;
}
