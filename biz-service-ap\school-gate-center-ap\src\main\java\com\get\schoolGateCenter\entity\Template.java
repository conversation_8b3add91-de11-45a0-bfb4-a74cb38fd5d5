package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
@Data
@TableName("m_template")
@ApiModel(value="MTemplate对象", description="")
public class Template extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模板名称")
    @Column(name = "name")
    private String name;

    @ApiModelProperty(value = "内容(html)")
    @Column(name = "content_html")
    private String contentHtml;

    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;


}
