package get.middlecenter.service;

import get.middlecenter.dto.appPartner.CreateContractPdfDto;

import javax.servlet.http.HttpServletResponse;

/**
 * @Author:Oliver
 * @Date: 2025/7/23
 * @Version 1.0
 * @apiNote:app-伙伴中心接口
 */
public interface AppPartnerService {

    /**
     * 生成代理合同pdf
     *
     * @param dto
     * @param response
     */
    void createContractPdf(CreateContractPdfDto dto,
                           String nonce,
                           HttpServletResponse response);
}
