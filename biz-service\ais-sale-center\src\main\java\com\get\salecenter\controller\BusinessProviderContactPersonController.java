package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.vo.BusinessProviderContactPersonVo;
import com.get.salecenter.service.BusinessProviderContactPersonService;
import com.get.salecenter.dto.BusinessProviderContactPersonListDto;
import com.get.salecenter.dto.BusinessProviderContactPersonDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "业务提供商联系人管理")
@RestController
@RequestMapping("sale/businessProviderContactPerson")
public class BusinessProviderContactPersonController {


    @Resource
    private BusinessProviderContactPersonService businessProviderContactPersonService;

    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/业务提供商联系人管理/查询")
    @PostMapping("datas")
    public ListResponseBo<BusinessProviderContactPersonVo> datas(@RequestBody SearchBean<BusinessProviderContactPersonListDto> page) {
        List<BusinessProviderContactPersonVo> datas = businessProviderContactPersonService.getBusinessProviderContactPersonDtos(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/业务提供商联系人管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(BusinessProviderContactPersonDto.Add.class)  BusinessProviderContactPersonDto businessProviderContactPersonDto) {
        businessProviderContactPersonService.addBusinessProviderContactPerson(businessProviderContactPersonDto);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/业务提供商联系人管理/更新")
    @PostMapping("update")
    public ResponseBo<BusinessProviderContactPersonVo> update(@RequestBody @Validated(BusinessProviderContactPersonDto.Update.class)  BusinessProviderContactPersonDto businessProviderContactPersonDto) {
        return UpdateResponseBo.ok(businessProviderContactPersonService.updateBusinessProviderContactPerson(businessProviderContactPersonDto));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description:删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/业务提供商联系人管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        businessProviderContactPersonService.deleteBusinessProviderContactPerson(id);
        return DeleteResponseBo.ok();
    }

}
