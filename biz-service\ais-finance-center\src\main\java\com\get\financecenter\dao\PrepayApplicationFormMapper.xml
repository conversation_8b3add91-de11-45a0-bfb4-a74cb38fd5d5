<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.PrepayApplicationFormMapper">
    <select id="getBorrowFormData" resultType="com.get.financecenter.vo.PrepayApplicationFormVo">
        select * from `m_prepay_application_form` where 1=1
        <if test='prepayApplicationFormQueryDto.selectStatus=="2"'>
            <choose>
                <when test="businessKeys!=null and businessKeys.size()>0">
                    and id in
                    <foreach collection="businessKeys" item="businessKey" index="index" open="(" separator=","
                             close=")">
                        #{businessKey}
                    </foreach>
                </when>
                <otherwise>
                    and id in (null)
                </otherwise>
            </choose>
        </if>
        <if test="prepayApplicationFormQueryDto.fkCompanyId!=null">
            and fk_company_id=#{prepayApplicationFormQueryDto.fkCompanyId}
        </if>
        -- 改成公司多选
        <if test="prepayApplicationFormQueryDto.fkCompanyIds != null and prepayApplicationFormQueryDto.fkCompanyIds.size() > 0">
            and fk_company_id in
            <foreach collection="prepayApplicationFormQueryDto.fkCompanyIds" item="fkCompanyId" index="index" open="(" separator="," close=")">
                #{fkCompanyId}
            </foreach>
        </if>
        <if test="prepayApplicationFormQueryDto.fkDepartmentId!=null">
            and fk_department_id=#{prepayApplicationFormQueryDto.fkDepartmentId}
        </if>
        <if test="num!=null and num!=''">
            and num like #{num}
        </if>
        <if test="prepayApplicationFormQueryDto.startTime!=null">
            and DATE_FORMAT( gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{prepayApplicationFormQueryDto.startTime}, '%Y-%m-%d' )
        </if>
        <if test="prepayApplicationFormQueryDto.endTime!=null">
            and DATE_FORMAT( gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{prepayApplicationFormQueryDto.endTime}, '%Y-%m-%d' )
        </if>
        <if test='prepayApplicationFormQueryDto.selectStatus=="0"'>
            and gmt_create_user=#{userId}
        </if>
        <if test="prepayApplicationFormQueryDto.gmtCreateUser!=null and prepayApplicationFormQueryDto.gmtCreateUser!=''">
            and REPLACE(gmt_create_user,' ','') like REPLACE(concat('%',#{prepayApplicationFormQueryDto.gmtCreateUser},'%'),' ','')
        </if>
        <if test="prepayApplicationFormQueryDto.status!=null">
            and status=#{prepayApplicationFormQueryDto.status}
        </if>
        order by gmt_create DESC
    </select>
    <select id="getExistParentId" resultType="java.lang.Boolean">
    select IFNULL(MAX(id),0) id from m_prepay_application_form where fk_prepay_application_form_id_revoke=#{id}
    </select>


</mapper>