package com.get.salecenter.dao.sale;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.*;
import com.get.salecenter.entity.ReceivablePlan;
import com.get.salecenter.dto.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface ReceivablePlanMapper extends GetMapper<ReceivablePlan> {

    int insertSelective(ReceivablePlan record);


    /**
     * @return java.lang.Long
     * @Description: 根据计划id 查询公司id
     * @Param [planId]
     * <AUTHOR>
     */
    Long getCompanyIdByPlanId(Long planId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据公司id查询计划
     * @Param [companyId]
     * <AUTHOR>
     */
    List<Long> getPlanIdByCompanyId(@Param("companyId") Long companyId, @Param("studentId") Long studentId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据公司ids查询计划
     * @Param [companyId]
     * <AUTHOR>
     */
    List<Long> getPlanIdByCompanyIds(@Param("fkCompanyIds") List<Long> fkCompanyIds, @Param("studentId") Long studentId);


    /**
     * 按照公司过滤
     *
     * @param fkCompanyId
     * @return
     */
    List<Long> getPlanIdByCompanyIdAndAccommodation(@Param("fkCompanyId") Long fkCompanyId, @Param("studentId") Long studentId);

    /**
     * 按照公司过滤
     *
     * @param fkCompanyIds
     * @return
     */
    List<Long> getPlanIdByCompanyIdsAndAccommodation(@Param("fkCompanyIds") List<Long> fkCompanyIds, @Param("studentId") Long studentId);


    /**
     * 按照公司过滤
     *
     * @param fkCompanyId
     * @return
     */
    List<Long> getPlanIdByCompanyIdAndInsurance(@Param("fkCompanyId") Long fkCompanyId, @Param("studentId") Long studentId);


    /**
     * 按照公司s过滤
     *
     * @param fkCompanyIds
     * @return
     */
    List<Long> getPlanIdByCompanyIdsAndInsurance(@Param("fkCompanyIds") List<Long> fkCompanyIds, @Param("studentId") Long studentId);


    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据学生名称模糊查询
     * @Param [studentName]
     * <AUTHOR>
     */
    List<Long> getPlanIdByStudentName(String studentName);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据学生名称模糊查询
     * @Param [studentName]
     * <AUTHOR>
     */
    List<Long> getPlanIdByProviderId(@Param(value = "providerIds") List<Long> providerIds);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据学生名称模糊查询
     * @Param [studentName]
     * <AUTHOR>
     */
    List<Long> getPlanIdByInstitutionId(@Param(value = "institutionIds") List<Long> institutionIds);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据学生名称模糊查询
     * @Param [studentName]
     * <AUTHOR>
     */
    List<Long> getPlanIdByCourseId(@Param(value = "courseIds") List<Long> courseIds);


    /**
     * @Description: 提供商应收汇总统计列表
     * @Author: Jerry
     * @Date:16:21 2021/11/20
     */
    List<InstitutionProviderReceivableSumVo> getInstitutionProviderReceivableSumDatas(IPage<InstitutionProviderReceivableSumVo> iPage, @Param("institutionProviderReceivableSumDto") InstitutionProviderReceivableSumDto institutionProviderReceivableSumDto);


    /**
     * @Description: 提供商应收汇总统计明细
     * @Author: Jerry
     * @Date:11:42 2021/11/22
     */
    List<ReceivablePlanVo> institutionProviderReceivableSumDetail(IPage<ReceivablePlanVo> iPage, @Param("institutionProviderReceivableSumDetailDto") InstitutionProviderReceivableSumDetailDto institutionProviderReceivableSumDetailDto);

    /**
     * 学生应收明细
     *
     * @param studentReceivableSumDetailDto
     * @return
     */
    List<ReceivablePlanVo> studentReceivableSumDetail(IPage<ReceivablePlanVo> iPage, @Param("studentReceivableSumDetailDto") StudentReceivableSumDetailDto studentReceivableSumDetailDto);

    /**
     * 获取应收的差额、实收、状态
     *
     * @param receivablePlanDto
     * @return
     */
    List<ReceivablePlanVo> getReceivablePlansInfo(@Param("receivablePlanDto") ReceivablePlanDto receivablePlanDto);

    List<ReceivablePlanVo> getReceivablePlansInfoByType(@Param("itemId") Long itemId, @Param("typeKey") String typeKey);

    /**
     * 获取计划id
     *
     * @param studentOfferItemIds
     * @return
     */
    List<Long> getPlanIdsByStudentOfferItemIds(@Param("studentOfferItemIds") String studentOfferItemIds);

    /**
     * 根据fkProviderId获取应收计划列表
     *
     * @param fkProviderId
     * @return
     */
    List<ReceivablePlanVo> getReceivablePlanDtosByProviderId(@Param("fkProviderId") Long fkProviderId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据计划IDS查询目标IDS
     * @Param [studentName]
     * <AUTHOR>
     */
    List<Long> getFkTypeTargetIdByIds(@Param(value = "ids") Set<Long> ids);

    Boolean isExistByItemId(@Param("id") Long id);

    /**
     * 获取所有id
     *
     * @param fkCompanyId
     * @return
     */
    List<Long> getAllPlanIdByCompanyId(@Param("fkCompanyId") Long fkCompanyId, @Param("studentId") Long studentId, @Param("institutionProviderIds") List<Long> institutionProviderIds);

    /**
     * @param fkCompanyIds
     * @param studentId
     * @param institutionProviderIds
     * @return
     */
    List<Long> getAllPlanIdByCompanyIds(@Param("fkCompanyIds") List<Long> fkCompanyIds, @Param("studentId") Long studentId, @Param("institutionProviderIds") List<Long> institutionProviderIds);

    /**
     * 根据目标ids和指定类型获取应收计划
     *
     * @param fkTypeKey
     * @param fkTypeTargetIds
     * @return
     */
    List<ReceivablePlanVo> getReceivablePlanByTargetsAndType(@Param("fkTypeKey") String fkTypeKey, @Param("fkTypeTargetIds") List<Long> fkTypeTargetIds);

    /**
     * @param fkTypeKey
     * @param institutionProviderIds
     * @return
     */
    List<Long> getPlanIdByInstitutionProviderIds(@Param("fkTypeKey") String fkTypeKey, @Param("institutionProviderIds") List<Long> institutionProviderIds);

    /**
     * 根据应付计划信息获取对应应收计划下拉框数据
     * @param fkTypeKey
     * @param fkTypeTargetId
     * @return
     */
    List<ReceivablePlan> getReceivablePlanByPayablePlanInfo(@Param("fkTypeKey") String fkTypeKey, @Param("fkTypeTargetId") Long fkTypeTargetId);



    List<ReceivablePlanNewVo> getReceivablePlanNew(IPage<ReceivablePlanNewVo> iPage, @Param("receivablePlanNewDto") ReceivablePlanNewDto receivablePlanNewDto);

    /**
     *  根据提供商 && 发展业务对象查询应收计划下拉数据
     */
    List<ReceivablePlanNewVo> getReceivablePlanInfoByKeyATargetId(@Param("receivablePlanBatchDto") ReceivablePlanBatchDto receivablePlanBatchDto);


    List<BaseSelectEntity> getReIdsAndCompanyName(@Param("typeKey")String typeKey,@Param("targetId") Long targetId,@Param("receiptFormId")Long receiptFormId);

    Integer getReceiptFormReCount(@Param("typeKey")String typeKey,@Param("targetId") Long targetId,@Param("receiptFormId")Long receiptFormId);

    Integer getReceiptFormReCountByChannel(@Param("typeKey")String typeKey,@Param("targetId") Long targetId,@Param("receiptFormId")Long receiptFormId);

    /**
     * 获取备注信息
     *
     * @param planIds
     * @return
     */
    List<PlanRemarkDetailResultVo> getReceivablePlanRemarkDetails(@Param("planIds") Set<Long> planIds);

    List<String> getRePlanTheLatestThreeTuitionFees(Long fkCompanyId);

    List<PublicReceiptFormDetailVo> getReceiptAmountByIds(@Param("ids") List<Long> ids);

    List<ReceivablePlanVo> getReceivableAmountInfo(@Param("receivablePlanIds") Set<Long> receivablePlanIds);

    List<IninvoiceAmountVo> getIninvoiceAmounts(@Param("ids")Set<Long> ids);

    /**
     * 提供商收款单获取应收计划下拉列表
     * @param providerId
     * @param receiptFormId
     * @return
     */
    List<BaseSelectEntity> getReceivablePlanSelectByProvider(@Param("providerId") Long providerId, @Param("receiptFormId") Long receiptFormId);

}