package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.ConventionTablePerson;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Sea
 * @create: 2020/8/28 16:21
 * @verison: 1.0
 * @description:
 */
@Data
@ApiModel(value = "峰会桌台配置-晚宴桌返回类")
public class ConventionTablePersonVo extends BaseEntity {

    /**
     * 峰会参展人员详细信息
     */
    @ApiModelProperty(value = "峰会参展人员详细信息")
    private ConventionPersonVo conventionPersonDto;

    //===============实体类ConventionTablePerson==================
    private static final long serialVersionUID = 1L;
    /**
     * 峰会桌子Id
     */
    @ApiModelProperty(value = "峰会桌子Id")
    @Column(name = "fk_convention_table_id")
    private Long fkConventionTableId;
    /**
     * 峰会参展人员Id
     */
    @ApiModelProperty(value = "峰会参展人员Id")
    @Column(name = "fk_convention_person_id")
    private Long fkConventionPersonId;
}
