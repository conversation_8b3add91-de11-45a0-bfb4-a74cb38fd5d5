package com.get.financecenter.controller;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.financecenter.dto.VouchReceiptRegisterDto;
import com.get.financecenter.service.IVouchReceiptRegisterService;
import com.get.financecenter.vo.VouchReceiptRegisterVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 收款记录和凭证管理
 */
@Api(tags = "收款记录管理")
@RestController
@RequestMapping("finance/vouchReceiptRegister")
public class VouchReceiptRegisterController{

    @Resource
    private IVouchReceiptRegisterService mVouchReceiptRegisterService;

    @ApiOperation(value = "获取列表数据",  notes = "收款记录和凭证列表数据")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/收款记录管理/获取列表数据")
    @PostMapping("datas")
    public ResponseBo<VouchReceiptRegisterVo> selectAll(@RequestBody SearchBean<VouchReceiptRegisterDto> page) {
        List<VouchReceiptRegisterVo> datas = mVouchReceiptRegisterService.getVouchReceiptRegister(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "新增接口", notes = "新增接口")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/收款记录管理/新增接口")
    @PostMapping("add")
    public ResponseBo<VouchReceiptRegisterVo> add(@RequestBody VouchReceiptRegisterDto vouchReceiptRegisterDto) {
        mVouchReceiptRegisterService.add(vouchReceiptRegisterDto);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "修改接口", notes = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/收款记录管理/修改接口")
    @PostMapping("update")
    public ResponseBo<VouchReceiptRegisterVo> update(@RequestBody VouchReceiptRegisterDto vouchReceiptRegisterDto) {
        mVouchReceiptRegisterService.updateById(vouchReceiptRegisterDto);
        return ResponseBo.ok();
    }

}

