package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.salecenter.vo.StudentContactPersonVo;
import com.get.salecenter.dto.StudentContactPersonDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/11/11
 * @TIME: 11:51
 * @Description:
 **/
public interface IStudentContactPersonService {


    /**
     * @return com.get.salecenter.vo.StudentContactPersonVo
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    StudentContactPersonVo findContactPersonById(Long id);

    /**
     * @return java.util.List<com.get.salecenter.vo.StudentContactPersonVo>
     * @Description: 列表数据
     * @Param [contactPersonVo, page]
     * <AUTHOR>
     */
    List<StudentContactPersonVo> getContactPersons(StudentContactPersonDto contactPersonVo, Page page);

    /**
     * @return com.get.salecenter.vo.StudentContactPersonVo
     * @Description: 修改
     * @Param [contactPersonVo]
     * <AUTHOR>
     */
    StudentContactPersonVo updateContactPerson(StudentContactPersonDto contactPersonVo);

    /**
     * @return java.lang.Long
     * @Description: 保存
     * @Param [contactPersonVo]
     * <AUTHOR>
     */
    Long addContactPerson(StudentContactPersonDto contactPersonVo);

    /**
     * @return void
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * 根据学生Id获取联系人列表
     *
     * @param studentId
     * @return
     * @
     */
    List<StudentContactPersonVo> getStudentContactPersonByStudentId(Long studentId);

    /**
     * 删除被合并学生联系人信息
     * @param mergedStudentId
     */
    void deleteByStudentId(Long mergedStudentId);
}
