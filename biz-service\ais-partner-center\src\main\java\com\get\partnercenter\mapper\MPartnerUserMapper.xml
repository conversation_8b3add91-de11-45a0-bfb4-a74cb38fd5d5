<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.partnercenter.mapper.MPartnerUserMapper">

    <update id="batchUpdateStatus">
        UPDATE m_partner_user set is_active = #{status},gmt_modified = now(),gmt_modified_user = #{loginId}
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <select id="selectAgentAccount" resultType="com.get.partnercenter.vo.AgentAccountVo">
        SELECT DISTINCT mpu.fk_agent_id AS agent_id,
        ma.num AS agent_num,
        ma.name AS agent_name,
        ma.gmt_create_user,
        ma.gmt_create,
        IF(ud.id IS NOT NULL, 0, 1) AS status,
        rsbd.fk_area_region_id as area_region_id,
        rsbd.bd_code,
        uas.`name` AS area_name,
        uas.name_chn AS area_name_chn,
        mst.name AS bd_name,
        mst.name_en AS bd_name_en
        FROM m_partner_user mpu
        INNER JOIN (
        <include refid="agentPermissionSql"/>
        ) z ON mpu.fk_agent_id=z.id
        INNER JOIN ais_sale_center.m_agent ma ON mpu.fk_agent_id = ma.id
        INNER JOIN ais_sale_center.r_agent_staff ras ON ras.fk_agent_id = ma.id
        AND ras.is_active = 1
        LEFT JOIN ais_sale_center.r_staff_bd_code rsbd ON rsbd.fk_staff_id = ras.fk_staff_id
        LEFT JOIN u_agent_disabled ud ON ud.fk_agent_id = ma.id
        LEFT JOIN ais_institution_center.u_area_state uas ON ma.fk_area_state_id = uas.id
        LEFT JOIN ais_permission_center.m_staff mst ON ras.fk_staff_id = mst.id
        <where>
            <if test="param.regionId != null">
                AND FIND_IN_SET(#{param.regionId},rsbd.fk_area_region_id)
            </if>
            <if test="param.agentKeyWord != null and param.agentKeyWord != ''">
                AND (ma.num LIKE CONCAT('%',#{param.agentKeyWord},'%') OR ma.name LIKE
                CONCAT('%',#{param.agentKeyWord},'%'))
            </if>
            <if test="param.status != null and param.status == 0">
                AND exists(SELECT 1 FROM u_agent_disabled ud WHERE ud.fk_agent_id = ma.id )
            </if>
            <if test="param.status != null and param.status == 1">
                AND not exists(SELECT 1 FROM u_agent_disabled ud WHERE ud.fk_agent_id = ma.id )
            </if>
        </where>
        HAVING agent_id IS NOT NULL
    </select>


    <select id="selectPartnerUser" resultType="com.get.partnercenter.vo.PartnerUserVo">
        SELECT u.name as userName,
        u.id as partnerUserId,
        u.email,
        u.gmt_create,
        u.gmt_create_user,
        u.fk_user_id as userId,
        u.is_active as status
        FROM m_partner_user u
        <where>
            u.fk_agent_id = #{agentId}
        </where>
    </select>

    <select id="selectPartnerUserCountryIds" resultType="java.lang.Long">
        SELECT DISTINCT fk_area_country_id from r_partner_user_area_country
        <where>
            fk_partner_user_id = #{partnerUserId}
        </where>
    </select>

    <select id="selectIdAndNameByIds" resultType="com.get.partnercenter.vo.PartnerUserSimpleVo">
        select id,name
        from m_partner_user
        where id in
        <foreach collection="list" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectByPartnerUserId" resultType="com.get.partnercenter.entity.MPartnerUserEntity">
        SELECT *
        FROM m_partner_user
        WHERE id = #{partnerUserId}
    </select>


    <!-- 代理权限 -->
    <sql id="agentPermissionSql">
        SELECT DISTINCT a.id FROM (
        <!-- 父子代理的BD权限，儿子下面的学生，父亲的BD，第一层的权限 -->
        SELECT c.id FROM ais_sale_center.m_agent c
        INNER JOIN ais_sale_center.r_agent_staff d ON c.fk_parent_agent_id=d.fk_agent_id AND d.is_active=1 AND d.fk_staff_id IN
        <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
            #{staffFollowerId}
        </foreach>
        UNION ALL
        <!-- 父子代理的BD权限，儿子下面的学生，父亲的BD，第二层的权限 -->
        SELECT c.id FROM ais_sale_center.m_agent c
        INNER JOIN ais_sale_center.m_agent d ON c.fk_parent_agent_id=d.id AND d.fk_parent_agent_id IS NOT NULL
        INNER JOIN ais_sale_center.r_agent_staff e ON d.fk_parent_agent_id=e.fk_agent_id AND e.is_active=1 AND e.fk_staff_id IN
        <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
            #{staffFollowerId}
        </foreach>
        UNION ALL
        <!-- BD的权限 -->
        SELECT c.fk_agent_id FROM ais_sale_center.r_agent_staff c WHERE c.is_active = 1 AND c.fk_staff_id IN
        <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
            #{staffFollowerId}
        </foreach>
        UNION ALL
        <!-- #创建人的权限 -->
        SELECT c.id FROM ais_sale_center.m_agent c
        INNER JOIN ais_permission_center.m_staff b ON c.gmt_create_user=b.login_id AND b.id IN
        <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
            #{staffFollowerId}
        </foreach>
        ) a
    </sql>
</mapper>
