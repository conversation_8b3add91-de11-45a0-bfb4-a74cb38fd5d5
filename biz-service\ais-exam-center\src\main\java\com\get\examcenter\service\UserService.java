package com.get.examcenter.service;

import com.get.common.result.Page;
import com.get.common.result.SearchBean;
import com.get.examcenter.vo.UserExaminationQuestionScoreVo;
import com.get.examcenter.vo.UserexaminationPaperScoreVo;
import com.get.examcenter.dto.UserExaminationQuestionScoreDto;
import com.get.examcenter.dto.UserDto;
import com.get.examcenter.dto.UserexaminationPaperScoreDto;
import com.get.salecenter.dto.DataCollectionQuestionDto;

import java.util.List;
import java.util.Set;

/**
 * Created by <PERSON>.
 * Time: 11:12
 * Date: 2021/8/25
 * Description:考生管理逻辑处理类
 */
public interface UserService {

    /**
     * @Description: 生成考试记录
     * @Author: Jerry
     * @Date:11:14 2021/8/25
     */
    UserexaminationPaperScoreVo generateExaminationRecords(String optGuid);


    /**
     * @Description: 生成答题记录
     * @Author: Jerry
     * @Date:11:14 2021/8/25
     */
    String generateAnswerRecords(UserExaminationQuestionScoreDto userExaminationQuestionScoreDto);

    /**
     * @Description: 考生列表
     * @Author: Jerry
     * @Date:11:27 2021/8/27
     */
    List<UserexaminationPaperScoreVo> getUserExaminationPaperScore(UserDto userDto, Page page);


    /**
     * @Description: 答题记录列表
     * @Author: Jerry
     * @Date:12:14 2021/8/27
     */
    List<UserExaminationQuestionScoreVo> answerRecordsList(UserexaminationPaperScoreDto data, SearchBean<UserexaminationPaperScoreDto> page);

    List<UserexaminationPaperScoreVo> getUserPaperData(UserexaminationPaperScoreDto data, SearchBean<UserexaminationPaperScoreDto> page);


    /**
     * @Description: feign调用 根据员工ids（BD）获取用户ids
     * @Author: Jerry
     * @Date:14:43 2021/8/30
     */
    Set<Long> getUserIdsByStaffIds(Set<Long> fkStaffIds);

    /**
     * 学校机构资料回显 根据手机号获取问题
     * @param contactTel
     * @return
     */
    List<DataCollectionQuestionDto> getDataCollectionQuestions(String contactTel);
}
