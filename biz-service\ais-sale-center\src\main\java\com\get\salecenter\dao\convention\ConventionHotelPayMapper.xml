<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.convention.ConventionHotelPayMapper">

    <select id="getPayTypeByConventionPersonIds" resultType="com.get.salecenter.vo.ConventionPersonPayTypeVo">
        SELECT
            fk_convention_person_id,
            MAX(pay_type) AS pay_type
        FROM
            `m_convention_hotel_pay`
        WHERE
            pay_type = 1
        GROUP BY
            fk_convention_person_id
    </select>
</mapper>