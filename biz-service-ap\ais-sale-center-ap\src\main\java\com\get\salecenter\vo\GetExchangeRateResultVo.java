package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: Hardy
 * @create: 2021/6/29 16:46
 * @verison: 1.0
 * @description:
 */
@Data
public class GetExchangeRateResultVo {

    @ApiModelProperty(value = "from")
    private String from;

    @ApiModelProperty(value = "to")
    private String to;

    @ApiModelProperty(value = "fromname")
    private String fromname;

    @ApiModelProperty(value = "toname")
    private String toname;

    @ApiModelProperty(value = "updatetime")
    private Date updatetime;

    @ApiModelProperty(value = "rate")
    private BigDecimal rate;

    @ApiModelProperty(value = "camount")
    private BigDecimal camount;


}
