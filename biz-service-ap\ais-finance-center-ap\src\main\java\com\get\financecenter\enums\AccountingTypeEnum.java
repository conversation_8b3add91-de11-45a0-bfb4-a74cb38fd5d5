package com.get.financecenter.enums;

import com.get.core.mybatis.base.BaseSelectEntity;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors; /**
 * 会计项目类型枚举
 */
public enum AccountingTypeEnum {
    ASSET(1, "资产"),
    LIABILITY(2, "负债"),
    EQUITY(3, "权益"),
    COST(4, "成本"),
    PROFIT_LOSS(5, "损益"),
    COMMON(6, "共同");

    private final Integer id;
    private final String name;

    AccountingTypeEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public static String getNameById(Integer id) {
        for (AccountingTypeEnum itemType : AccountingTypeEnum.values()) {
            if (itemType.getId().equals(id)) {
                return itemType.getName();
            }
        }
        return null;
    }

    /**
     * 获取所有枚举值作为选择列表
     */
    public static List<BaseSelectEntity> asSelectList() {
        return Arrays.stream(values())
                .map(type ->{
                    BaseSelectEntity selectEntity = new BaseSelectEntity();
                    selectEntity.setId(Long.valueOf(type.getId()));
                    selectEntity.setName(type.getName());
                    selectEntity.setNameChn(type.getName());
                    selectEntity.setFullName(type.getName());
                    return selectEntity;
                })
                .collect(Collectors.toList());
    }


}
