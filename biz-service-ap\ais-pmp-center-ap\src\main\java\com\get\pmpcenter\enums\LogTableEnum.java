package com.get.pmpcenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 记录日志的表枚举
 */

@Getter
@AllArgsConstructor
public enum LogTableEnum {

    AGENT_PLAN("m_agent_commission_plan", "代理佣金方案"),
    PROVIDER_PLAN("m_institution_provider_commission_plan", "学校提供商佣金方案"),
    PROVIDER_CONTRACT("m_institution_provider_contract", "学校提供商合同"),
    ;

    private String code;

    private String msg;

    public static LogTableEnum getEnumByCode(String code) {
        for (LogTableEnum value : LogTableEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
