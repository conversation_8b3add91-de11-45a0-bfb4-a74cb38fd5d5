package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2020/8/10
 * @TIME: 14:43
 * @Description:知名校友VO
 **/
@Data
public class InstitutionAlumnusDto extends BaseVoEntity {
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id", required = true)
    @NotNull(message = "学校Id不能为空", groups = {Add.class, Update.class})
    private Long fkInstitutionId;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名", required = true)
    @NotBlank(message = "姓名不能为空", groups = {Add.class, Update.class})
    private String name;

    /**
     * 简历
     */
    @ApiModelProperty(value = "简历", required = true)
    @NotBlank(message = "简历不能为空", groups = {Add.class, Update.class})
    private String resume;

    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列", required = true)
    @NotNull(message = "学校排序不能为空", groups = {Add.class, Update.class})
    private Integer viewOrder;

    private String keyWord;

}
