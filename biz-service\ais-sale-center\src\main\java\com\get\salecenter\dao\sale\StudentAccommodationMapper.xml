<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.StudentAccommodationMapper">
  <insert id="insert" parameterType="com.get.salecenter.entity.StudentAccommodation" keyProperty="id"
          useGeneratedKeys="true">
    insert into m_student_accommodation (id, fk_student_id, fk_agent_id,
                                         fk_staff_id, num, fk_area_country_id,
                                         fk_area_state_id, fk_area_city_id, apartment_name,
                                         check_in_date, check_out_date, duration,
                                         fk_business_channel_id, deposit_date, fk_currency_type_num_accommodation,
                                         accommodation_amount_per, accommodation_amount_per_unit,
                                         accommodation_amount, accommodation_amount_note,
                                         fk_currency_type_num_commission, commission_rate_receivable,
                                         commission_rate_payable, fixed_amount_receivable,
                                         fixed_amount_payable, remark, status,
                                         gmt_create, gmt_create_user, gmt_modified,
                                         gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkStudentId,jdbcType=BIGINT}, #{fkAgentId,jdbcType=BIGINT},
            #{fkStaffId,jdbcType=BIGINT}, #{num,jdbcType=VARCHAR}, #{fkAreaCountryId,jdbcType=BIGINT},
            #{fkAreaStateId,jdbcType=BIGINT}, #{fkAreaCityId,jdbcType=BIGINT}, #{apartmentName,jdbcType=VARCHAR},
            #{checkInDate,jdbcType=DATE}, #{checkOutDate,jdbcType=DATE}, #{duration,jdbcType=INTEGER},
            #{fkBusinessChannelId,jdbcType=BIGINT}, #{depositDate,jdbcType=DATE}, #{fkCurrencyTypeNumAccommodation,jdbcType=VARCHAR},
            #{accommodationAmountPer,jdbcType=DECIMAL}, #{accommodationAmountPerUnit,jdbcType=INTEGER},
            #{accommodationAmount,jdbcType=DECIMAL}, #{accommodationAmountNote,jdbcType=VARCHAR},
            #{fkCurrencyTypeNumCommission,jdbcType=VARCHAR}, #{commissionRateReceivable,jdbcType=DECIMAL},
            #{commissionRatePayable,jdbcType=DECIMAL}, #{fixedAmountReceivable,jdbcType=DECIMAL},
            #{fixedAmountPayable,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
            #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP},
            #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.StudentAccommodation" keyProperty="id"
          useGeneratedKeys="true">
    insert into m_student_accommodation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkStudentId != null">
        fk_student_id,
      </if>
      <if test="fkAgentId != null">
        fk_agent_id,
      </if>
      <if test="fkStaffId != null">
        fk_staff_id,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="fkAreaCountryId != null">
        fk_area_country_id,
      </if>
      <if test="fkAreaStateId != null">
        fk_area_state_id,
      </if>
      <if test="fkAreaCityId != null">
        fk_area_city_id,
      </if>
      <if test="apartmentName != null">
        apartment_name,
      </if>
      <if test="checkInDate != null">
        check_in_date,
      </if>
      <if test="checkOutDate != null">
        check_out_date,
      </if>
      <if test="duration != null">
        duration,
      </if>
      <if test="fkBusinessChannelId != null">
        fk_business_channel_id,
      </if>
      <if test="depositDate != null">
        deposit_date,
      </if>
      <if test="fkCurrencyTypeNumAccommodation != null">
        fk_currency_type_num_accommodation,
      </if>
      <if test="accommodationAmountPer != null">
        accommodation_amount_per,
      </if>
      <if test="accommodationAmountPerUnit != null">
        accommodation_amount_per_unit,
      </if>
      <if test="accommodationAmount != null">
        accommodation_amount,
      </if>
      <if test="accommodationAmountNote != null">
        accommodation_amount_note,
      </if>
      <if test="fkCurrencyTypeNumCommission != null">
        fk_currency_type_num_commission,
      </if>
      <if test="commissionRateReceivable != null">
        commission_rate_receivable,
      </if>
      <if test="commissionRatePayable != null">
        commission_rate_payable,
      </if>
      <if test="fixedAmountReceivable != null">
        fixed_amount_receivable,
      </if>
      <if test="fixedAmountPayable != null">
        fixed_amount_payable,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkStudentId != null">
        #{fkStudentId,jdbcType=BIGINT},
      </if>
      <if test="fkAgentId != null">
        #{fkAgentId,jdbcType=BIGINT},
      </if>
      <if test="fkStaffId != null">
        #{fkStaffId,jdbcType=BIGINT},
      </if>
      <if test="num != null">
        #{num,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCountryId != null">
        #{fkAreaCountryId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaStateId != null">
        #{fkAreaStateId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCityId != null">
        #{fkAreaCityId,jdbcType=BIGINT},
      </if>
      <if test="apartmentName != null">
        #{apartmentName,jdbcType=VARCHAR},
      </if>
      <if test="checkInDate != null">
        #{checkInDate,jdbcType=DATE},
      </if>
      <if test="checkOutDate != null">
        #{checkOutDate,jdbcType=DATE},
      </if>
      <if test="duration != null">
        #{duration,jdbcType=INTEGER},
      </if>
      <if test="fkBusinessChannelId != null">
        #{fkBusinessChannelId,jdbcType=BIGINT},
      </if>
      <if test="depositDate != null">
        #{depositDate,jdbcType=DATE},
      </if>
      <if test="fkCurrencyTypeNumAccommodation != null">
        #{fkCurrencyTypeNumAccommodation,jdbcType=VARCHAR},
      </if>
      <if test="accommodationAmountPer != null">
        #{accommodationAmountPer,jdbcType=DECIMAL},
      </if>
      <if test="accommodationAmountPerUnit != null">
        #{accommodationAmountPerUnit,jdbcType=INTEGER},
      </if>
      <if test="accommodationAmount != null">
        #{accommodationAmount,jdbcType=DECIMAL},
      </if>
      <if test="accommodationAmountNote != null">
        #{accommodationAmountNote,jdbcType=VARCHAR},
      </if>
      <if test="fkCurrencyTypeNumCommission != null">
        #{fkCurrencyTypeNumCommission,jdbcType=VARCHAR},
      </if>
      <if test="commissionRateReceivable != null">
        #{commissionRateReceivable,jdbcType=DECIMAL},
      </if>
      <if test="commissionRatePayable != null">
        #{commissionRatePayable,jdbcType=DECIMAL},
      </if>
      <if test="fixedAmountReceivable != null">
        #{fixedAmountReceivable,jdbcType=DECIMAL},
      </if>
      <if test="fixedAmountPayable != null">
        #{fixedAmountPayable,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getStudentAccommodationList" resultType="com.get.salecenter.vo.StudentAccommodationVo">

    SELECT *
    FROM m_student_accommodation sa
    <where>
      <if test="studentAccommodation.fkStudentId!=null and studentAccommodation.fkStudentId !=''">
        and sa.fk_student_id=#{studentAccommodation.fkStudentId}
      </if>
    </where>
  </select>

  <select id="verifyAccPermissions" resultType="java.lang.Long">
    select distinct msi.id from m_student_accommodation msi
    LEFT JOIN m_student ms on ms.id = msi.fk_student_id
    LEFT JOIN m_agent ma on ma.id = msi.fk_agent_id and ma.is_active = 1
    left join ais_permission_center.m_staff gpcms on gpcms.id = msi.fk_staff_id and gpcms.is_active = 1
    left join  (SELECT ms.name name,sprs.fk_table_id fk_table_id,sprs.fk_staff_id fk_staff_id
    FROM s_student_project_role_staff sprs,ais_permission_center.m_staff ms
    WHERE ms.id = sprs.fk_staff_id and sprs.is_active = 1 and sprs.fk_table_name = 'm_student_accommodation') sprsms on sprsms.fk_table_id = msi.id
    <if test="staffFollowerIds!=null and staffFollowerIds.size()>0">
      left join r_agent_staff ras on ras.fk_agent_id = msi.fk_agent_id
      and ras.is_active =1
    </if>
    where 1=1
      and msi.id=#{accId}
    <if test="staffFollowerIds!=null and staffFollowerIds.size()>0">
      and (ras.fk_staff_id  in
      <foreach collection="staffFollowerIds" item="id" index="index" open="(" separator="," close=")">
        #{id}
      </foreach>

    </if>
    <if test="staffFollowerIds!=null and staffFollowerIds.size()>0">
      or msi.fk_staff_id  in
      <foreach collection="staffFollowerIds" item="id" index="index" open="(" separator="," close=")">
        #{id}
      </foreach>
      OR (msi.fk_staff_id IS NULL AND msi.fk_agent_id IS NULL)
      or sprsms.fk_staff_id in
      <foreach collection="staffFollowerIds" item="id" index="index" open="(" separator="," close=")">
        #{id}
      </foreach>
      )
    </if>
  </select>
  <select id="getStudentAccommodationSummary" resultType="com.get.salecenter.vo.StudentAccommodationVo">
    select
    IFNULL(r.ar_status, 0) arStatus,
    IFNULL(p.ap_status, 0) apStatus,
    CASE
    WHEN IFNULL(r.ar_status, 0) = 2
    AND IFNULL(p.ap_status, 0) = 2 THEN
    2
    ELSE
    1
    END settlementStatus,
    -- 结算状态：1=处理中、2=完成
    r.currency_type_name AS receivableCurrencyTypeName,
    r.receivable_amount AS amountReceivable,
    r.actual_receivable_amount AS actualReceivableAmount,
    r.diff_receivable_amount AS diffReceivableAmount,
    p.currency_type_name AS payableCurrencyTypeName,
    p.payable_amount AS payableAmount,
    p.actual_payable_amount AS actualPayableAmount,
    p.diff_payable_amount AS diffPayableAmount,
    a.*,
    ms.fk_company_id fkCompanyId,
    ms.name studentName,
    ms.last_name lastName,
    ms.first_name firstName,
    ms.birthday birthday,
    ms.gender gender,
    ms.passport_num passportNum,
    ma.name fkAgentName,LENGTH(CONCAT(ms.first_name,ms.last_name)) as weights from m_student_accommodation a
    <!-- 权限sql -->
    <if test="!isStudentAdmin">
      INNER JOIN (
      <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.studentAuthority"/>
      )c ON c.id = a.fk_student_id
    </if>
    <!-- 权限sql结束 -->
    -- 获取申请计划的总应付状态：0=未付、1=部分已付、2=已付清
    LEFT JOIN (
    SELECT a.fk_type_target_id,
    MIN(a.payable_amount) as payable_amount,
    MIN(a.actual_payable_amount) as actual_payable_amount,
    MIN(a.diff_payable_amount) as diff_payable_amount,
    MIN(a.currency_type_name) as currency_type_name,
    CASE WHEN SUM(a.diff_payable_amount)=0 THEN 2 ELSE
    CASE WHEN SUM(a.actual_payable_amount)>0 THEN 1 ELSE 0 END
    END ap_status
    FROM (
    SELECT a.fk_type_target_id,
    CONCAT( c.type_name ,"(",c.num, "）") as currency_type_name,
    IFNULL(a.payable_amount,0) payable_amount, -- 应付金额
    IFNULL(b.sum_amount_payable,0) sum_amount_payable, -- 实付折合金额（这个金额币种和应付一致，因为已经是折合了）
    IFNULL(b.sum_amount_exchange_rate,0) sum_amount_exchange_rate, -- 汇率调整金额
    (IFNULL(b.sum_amount_payable,0) + IFNULL(b.sum_amount_exchange_rate,0)) actual_payable_amount, -- 实付（折合金额+汇率调整金额）
    (IFNULL(b.sum_amount_payable,0) + IFNULL(b.sum_amount_exchange_rate,0) - IFNULL(a.payable_amount,0)) diff_payable_amount -- 差额
    FROM ais_sale_center.m_payable_plan a
    LEFT JOIN (
    -- 计算每条应付计划里累计的实付金额
    SELECT a.fk_payable_plan_id,
    SUM(IFNULL(a.amount_payable,0)) sum_amount_payable,
    SUM(IFNULL(a.amount_exchange_rate,0)) sum_amount_exchange_rate
    FROM ais_finance_center.m_payment_form_item a
    LEFT JOIN ais_finance_center.m_payment_form b ON a.fk_payment_form_id=b.id
    WHERE b.`status`!=0 -- 关闭的付款单不作计算
    GROUP BY a.fk_payable_plan_id
    ) b ON a.id=b.fk_payable_plan_id
    LEFT JOIN ais_finance_center.u_currency_type c ON a.fk_currency_type_num=c.num
    WHERE a.fk_type_key = 'm_student_accommodation' AND a.status!=0
    ) a
    GROUP BY a.fk_type_target_id
    ) p ON a.id=p.fk_type_target_id

    -- 获取申请计划的总应收状态：0=未收、1=部分已收、2=已收齐
    LEFT JOIN (
    SELECT a.fk_type_target_id,
    MIN(a.receivable_amount) as receivable_amount,
    MIN(a.actual_receivable_amount) as actual_receivable_amount,
    MIN(a.diff_receivable_amount) as diff_receivable_amount,
    MIN(a.currency_type_name) as currency_type_name,
    CASE WHEN SUM(a.diff_receivable_amount)=0 THEN 2 ELSE
    CASE WHEN SUM(a.actual_receivable_amount)>0 THEN 1 ELSE 0 END
    END ar_status
    FROM (
    SELECT a.fk_type_target_id,
    CONCAT( c.type_name ,"(",c.num, "）") as currency_type_name,
<!--    <c.type_name currency_type_name,>-->
    IFNULL(a.receivable_amount,0) receivable_amount, -- 应收金额
    IFNULL(b.sum_amount_receivable,0) sum_amount_receivable, -- 实收折合金额（这个金额币种和应收一致，因为已经是折合了）
    IFNULL(b.sum_amount_exchange_rate,0) sum_amount_exchange_rate, -- 汇率调整金额
    (IFNULL(b.sum_amount_receivable,0) + IFNULL(b.sum_amount_exchange_rate,0)) actual_receivable_amount, -- 实收（折合金额（含手续费）+汇率调整金额）
    (IFNULL(b.sum_amount_receivable,0) + IFNULL(b.sum_amount_exchange_rate,0) - IFNULL(a.receivable_amount,0)) diff_receivable_amount -- 差额
    FROM ais_sale_center.m_receivable_plan a
    LEFT JOIN (
    -- 计算每条应收计划里累计的实收金额
    SELECT a.fk_receivable_plan_id,
    SUM(IFNULL(a.amount_receivable,0)) sum_amount_receivable,
    SUM(IFNULL(a.amount_exchange_rate,0)) sum_amount_exchange_rate
    FROM ais_finance_center.m_receipt_form_item a
    LEFT JOIN ais_finance_center.m_receipt_form b ON a.fk_receipt_form_id=b.id
    WHERE b.`status`!=0 AND b.settlement_status = 1 -- 关闭的收款单不作计算
    GROUP BY a.fk_receivable_plan_id
    ) b ON a.id=b.fk_receivable_plan_id
    LEFT JOIN ais_finance_center.u_currency_type c ON a.fk_currency_type_num=c.num
    WHERE a.fk_type_key = 'm_student_accommodation' AND a.status!=0
    ) a
    GROUP BY a.fk_type_target_id
    ) r ON  a.id=r.fk_type_target_id

    LEFT JOIN m_student ms on ms.id = a.fk_student_id
    LEFT JOIN m_agent ma on ma.id = a.fk_agent_id
    left join ais_permission_center.m_staff gpcms on gpcms.id = a.fk_staff_id
    left join  (SELECT ms.name name,sprs.fk_table_id fk_table_id,sprs.fk_staff_id fk_staff_id FROM s_student_project_role_staff sprs,ais_permission_center.m_staff ms

    WHERE ms.id = sprs.fk_staff_id and sprs.is_active = 1 and sprs.fk_table_name = 'm_student_accommodation') sprsms on sprsms.fk_table_id = a.id
    <if test="studentAccommodation.staffFollowerIds!=null and studentAccommodation.staffFollowerIds.size()>0">
      left join r_agent_staff ras on ras.fk_agent_id = a.fk_agent_id
      and ras.is_active =1
    </if>
    where 1=1
    <if test="studentAccommodation.staffFollowerIds!=null and studentAccommodation.staffFollowerIds.size()>0">
      and (ras.fk_staff_id  in
      <foreach collection="studentAccommodation.staffFollowerIds" item="id" index="index" open="(" separator="," close=")">
        #{id}
      </foreach>

    </if>
    <if test="studentAccommodation.staffFollowerIds!=null and studentAccommodation.staffFollowerIds.size()>0">
      or a.fk_staff_id  in
      <foreach collection="studentAccommodation.staffFollowerIds" item="id" index="index" open="(" separator="," close=")">
        #{id}
      </foreach>
      OR (a.fk_staff_id IS NULL AND a.fk_agent_id IS NULL)
      or sprsms.fk_staff_id in
      <foreach collection="studentAccommodation.staffFollowerIds" item="id" index="index" open="(" separator="," close=")">
        #{id}
      </foreach>
          )
    </if>
    <if test="studentAccommodation.fkCompanyId!=null">
      and ms.fk_company_id = #{studentAccommodation.fkCompanyId}
    </if>
    <if test="studentAccommodation.studentName!=null and studentAccommodation.studentName!=''">
      and (
      REPLACE(CONCAT(LOWER(ms.first_name),LOWER(ms.last_name)),' ','') like concat('%',#{studentAccommodation.studentName},'%')
        OR REPLACE(CONCAT(LOWER(ms.last_name),LOWER(ms.first_name)),' ','') like concat('%',#{studentAccommodation.studentName},'%')
        OR REPLACE(LOWER(ms.`name`),' ','')  like concat('%',#{studentAccommodation.studentName},'%')
        OR REPLACE(LOWER(ms.last_name),' ','')  like concat('%',#{studentAccommodation.studentName},'%')
        OR REPLACE(LOWER(ms.first_name),' ','')  like concat('%',#{studentAccommodation.studentName},'%')
      )
    </if>
    <if test="studentAccommodation.agentName!=null and studentAccommodation.agentName!=''">
      and LOWER(ma.name) like concat("%",#{studentAccommodation.agentName},"%")
    </if>
    <if test="studentAccommodation.staffName!=null and studentAccommodation.staffName!=''">
      and LOWER(gpcms.name) like concat("%",#{studentAccommodation.staffName},"%")
    </if>
    <if test="studentAccommodation.memberName!=null and studentAccommodation.memberName!=''">
      and LOWER(sprsms.name) like concat("%",#{studentAccommodation.memberName},"%")
    </if>
    <if test="studentAccommodation.fkBusinessChannelId!=null">
      and a.fk_business_channel_id = #{studentAccommodation.fkBusinessChannelId}
    </if>
    <if test="studentAccommodation.fkBusinessProviderId != null">
      and a.fk_business_provider_id = #{studentAccommodation.fkBusinessProviderId}
    </if>
    <if test="studentAccommodation.status!=null">
      and a.status = #{studentAccommodation.status}
    </if>

    <if test="studentAccommodation.fkAreaCountryId!=null">
      and a.fk_area_country_id = #{studentAccommodation.fkAreaCountryId}
    </if>
    <if test="studentAccommodation.fkAreaStateId!=null">
      and a.fk_area_state_id = #{studentAccommodation.fkAreaStateId}
    </if>
    <if test="studentAccommodation.fkAreaCityId!=null">
      and a.fk_area_city_id = #{studentAccommodation.fkAreaCityId}
    </if>
    <if test="studentAccommodation.apartmentName!=null and studentAccommodation.apartmentName!=''">
      and LOWER(a.apartment_name) like concat("%",#{studentAccommodation.apartmentName},"%")
    </if>

    <!--查询条件-开始日期-->
    <if test="studentAccommodation.checkInDateStart != null and studentAccommodation.checkInDateStart.toString() !=''">
      and DATE_FORMAT(a.check_in_date,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{studentAccommodation.checkInDateStart},'%Y-%m-%d')
    </if>
    <!--查询条件-结束日期-->
    <if test="studentAccommodation.checkInDateEnd!= null and studentAccommodation.checkInDateEnd.toString() !=''">
      and DATE_FORMAT(a.check_in_date,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{studentAccommodation.checkInDateEnd},'%Y-%m-%d')
    </if>

    <!--查询条件-开始日期-->
    <if test="studentAccommodation.checkOutDateStart != null and studentAccommodation.checkOutDateStart.toString() !=''">
      and DATE_FORMAT(a.check_out_date,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{studentAccommodation.checkOutDateStart},'%Y-%m-%d')
    </if>
    <!--查询条件-结束日期-->
    <if test="studentAccommodation.checkOutDateEnd!= null and studentAccommodation.checkOutDateEnd.toString() !=''">
      and DATE_FORMAT(a.check_out_date,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{studentAccommodation.checkOutDateEnd},'%Y-%m-%d')
    </if>

    <!--查询条件-应收状态-->
    <if test="studentAccommodation.arStatus != null">
      and IFNULL(r.ar_status, 0) = #{studentAccommodation.arStatus}
    </if>
    <!--查询条件-应付状态-->
    <if test="studentAccommodation.apStatus != null">
      and IFNULL(p.ap_status, 0) = #{studentAccommodation.apStatus}
    </if>
    <!--GROUP BY a.id-->
    <choose>
      <when test="studentAccommodation.studentName!=null and studentAccommodation.studentName!=''">
        ORDER BY weights ASC
      </when>
      <otherwise>
        ORDER BY a.gmt_create desc
      </otherwise>
    </choose>
  </select>
  <select id="getNumById" resultType="java.lang.String">
    SELECT num FROM `m_student_accommodation` where id = #{fkTableId}
  </select>
  <select id="getStudentAccommodationSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    SELECT
      id,
      num NAME
    FROM
      `m_student_accommodation`
    WHERE
      fk_student_id = #{studentId}
  </select>
  <select id="getStudentAccommodations" resultType="com.get.salecenter.entity.StudentAccommodation">
    SELECT
    *
    FROM
    `m_student_accommodation` msa
    left join  m_student ms on ms.id = msa.fk_student_id
    <if test="keyWord!=null">
      left join m_business_channel mbc on mbc.id = msa.fk_business_channel_id

    </if>
    WHERE msa.status != 0
    <if test="keyWord!=null">
      AND ((ms.name LIKE CONCAT('%', #{keyWord}, '%') OR ms.last_name LIKE CONCAT('%', #{keyWord}, '%') OR ms.first_name LIKE CONCAT('%', #{keyWord}, '%'))
      or (mbc.name LIKE CONCAT('%', #{keyWord}, '%') or mbc.name_chn LIKE CONCAT('%', #{keyWord}, '%')))
    </if>
    <if test="studentId!=null">
      and  msa.fk_student_id = #{studentId}
    </if>
    <if test="companyIds!=null and companyIds.size>0">
      and  ms.fk_company_id in
      <foreach collection="companyIds" separator="," open="(" close=")" item="item">
        #{item}
      </foreach>

    </if>
    ORDER BY
    LENGTH(ms.name),LENGTH(mbc.name)
    limit 20
  </select>
  <select id="getCompanyIdsByAccommodationIds" resultType="com.get.salecenter.vo.StudentAccommodationVo">
    SELECT
    msa.id,msa.fk_student_id,ms.fk_company_id
    FROM
    m_student_accommodation AS msa
    INNER JOIN
    m_student AS ms
    ON
    msa.fk_student_id = ms.id
    <where>
      <if test="accommodationIds != null and accommodationIds.size()>0">
        AND msa.id in
        <foreach collection="accommodationIds" item="accommodationId" index="index" open="(" separator="," close=")">
          #{accommodationId}
        </foreach>
      </if>
    </where>

  </select>

  <select id="getAccommodationIdsByStudentId" resultType="java.lang.Long">
       select id from m_student_accommodation where fk_student_id = #{studentId}
    </select>

  <select id="getStudentAccommodationAgent" resultType="com.get.salecenter.vo.AgentsBindingVo">
    SELECT a.NAME as agentName,
           d.name as bdName,
           m.num
    FROM m_student_accommodation m
           LEFT JOIN m_agent a ON a.id = m.fk_agent_id
           LEFT JOIN ais_permission_center.m_staff d ON d.id = m.fk_staff_id
           LEFT JOIN m_student t on m.fk_student_id = t.id
    WHERE t.num = #{fkStudentNum}
      AND d.is_active = 1
    AND a.is_active = 1
  </select>
    <select id="queryAccommodationAgentId" resultType="java.lang.Long" parameterType="Long">
        SELECT
            m.id
        FROM
            m_agent m
        INNER JOIN m_student_accommodation a on a.fk_agent_id = m.id
        WHERE
            a.id = #{targetId}
    </select>
  <select id="getIdByTargetId" resultType="java.lang.Long" parameterType="Long">
        SELECT
            fk_business_channel_id
        FROM
            m_student_accommodation
        WHERE
            id = #{targetId}
  </select>
    <select id="getPaidAmountByIds" resultType="com.get.salecenter.vo.AccommodationPayFormDetailVo">
      SELECT
      <!--币种-->
      CONCAT( c.type_name, "(", c.num, "）" ) AS payableCurrencyTypeName,
      <!--实收（折合金额+汇率调整）-->
      IFNULL( i.amount_payable, 0 ) + IFNULL( i.amount_exchange_rate, 0 ) AS actualPayableAmount,
      <!--付款时间-->
      i.gmt_create as actualPayTime,
      s.id as accommodationId
      FROM
      ais_finance_center.m_payment_form_item i
      INNER JOIN m_payable_plan p ON p.id = i.fk_payable_plan_id
      INNER JOIN m_student_accommodation s ON s.id = p.fk_type_target_id and p.fk_type_key = "m_student_accommodation"
      LEFT JOIN ais_finance_center.u_currency_type c ON p.fk_currency_type_num=c.num
      where s.id in
      <foreach collection="itemIds" item="itemId" open="(" separator="," close=")">
        #{itemId}
      </foreach>
    </select>
</mapper>