package com.get.gateway.props;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.ArrayList;
import java.util.List;

/**
 * 权限过滤
 */
@Data
@RefreshScope
@ConfigurationProperties("get.secure")
public class AuthProperties {

    /**
     * 可以放行的API集合
     */
    private final List<String> skipUrl = new ArrayList<>();

}
