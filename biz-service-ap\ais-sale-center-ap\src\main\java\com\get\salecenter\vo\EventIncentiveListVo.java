package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.EventIncentive;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/7/19 16:25
 * @verison: 1.0
 * @description:
 */
@Data
public class EventIncentiveListVo extends BaseEntity {

    @ApiModelProperty("公司名称")
    private String fkCompanyName;

    @ApiModelProperty("提供商名称")
    private String fkInstitutionProviderName;

    @ApiModelProperty("国家名称")
    private String fkAreaCountryName;

    @ApiModelProperty(value = "币种名称")
    private String fkCurrencyTypeNumName;

    @ApiModelProperty(value = "状态名称")
    private String statusName;

    @ApiModelProperty(value = "实际支付奖励金额(带币种)")
    private String actualPayAmountCurrency;

    @ApiModelProperty(value = "奖励市场费实际收入")
    private BigDecimal actualReceivableAmount;

    @ApiModelProperty(value = "奖励市场费实际收入(带币种)")
    private String actualReceivableAmountCurrency;

    @ApiModelProperty(value = "余额")
    private BigDecimal balanceAmount;

    @ApiModelProperty(value = "余额(带币种)")
    private String balanceAmountCurrency;

    @ApiModelProperty(value = "活动费用")
    private BigDecimal amount;

    @ApiModelProperty(value = "活动费用(带币种)")
    private String amountCurrency;

    @ApiModelProperty(value = "折合收款金额")
    private BigDecimal amountReceivable;

    @ApiModelProperty(value = "折合收款金额(带币种)")
    private String amountReceivableCurrency;

    @ApiModelProperty(value = "折合收款金额币种")
    private String amountReceivableCurrencyNum;

    @ApiModelProperty(value = "归口id")
    private Long fkEventIncentiveCostId;

    @ApiModelProperty("附件")
    private List<MediaAndAttachedVo> mediaAndAttachedDtos;

    @ApiModelProperty(value = "活动对象")
    private String publicLevelName;

    @ApiModelProperty("活动标题")
    private String eventTitle;

    @ApiModelProperty("是否下发奖励：0否/1是")
    private Boolean isDistributed;

    /**
     * 查看状态
     */
    @ApiModelProperty(value = "查看权限")
    private Boolean visitStatus;

    //========实体类EventIncentive=================
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    /**
     * 学校提供商Id
     */
    @ApiModelProperty(value = "学校提供商Id")
    @Column(name = "fk_institution_provider_id")
    private Long fkInstitutionProviderId;

    /**
     * 活动编号
     */
    @ApiModelProperty(value = "活动编号")
    @Column(name = "num")
    private String num;

    /**
     * 收到奖励时间
     */
    @UpdateWithNull
    @ApiModelProperty(value = "收到奖励时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "receiving_reward_time")
    private Date receivingRewardTime;

    /**
     * 实际宣传时间
     */
    @UpdateWithNull
    @ApiModelProperty(value = "实际宣传时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "actual_publicity_time")
    private Date actualPublicityTime;

    /**
     * 活动开始时间
     */
    @UpdateWithNull
    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "event_start_time")
    private Date eventStartTime;

    /**
     * 活动结束时间
     */
    @UpdateWithNull
    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "event_end_time")
    private Date eventEndTime;

    /**
     * 符合奖励intake
     */
    @ApiModelProperty(value = "符合奖励intake")
    @Column(name = "accord_with_incentive_intake")
    private String accordWithIncentiveIntake;

    /**
     * 激励政策
     */
    @ApiModelProperty(value = "激励政策")
    @Column(name = "incentive_policy")
    private String incentivePolicy;

    /**
     * 建议核对时间
     */
    @UpdateWithNull
    @ApiModelProperty(value = "建议核对时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "suggest_check_time")
    private Date suggestCheckTime;

    /**
     * 预计完成学生数
     */
    @ApiModelProperty(value = "预计完成学生数")
    @Column(name = "expect_target_count")
    private Integer expectTargetCount;

    /**
     * 实际完成学生数
     */
    @UpdateWithNull
    @ApiModelProperty(value = "实际完成学生数")
    @Column(name = "actual_target_count")
    private Integer actualTargetCount;

    /**
     * 币种编号
     */
    @UpdateWithNull
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;

    /**
     * 实际支付奖励金额
     */
    @UpdateWithNull
    @ApiModelProperty(value = "实际支付奖励金额")
    @Column(name = "actual_pay_amount")
    private BigDecimal actualPayAmount;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    /**
     * 状态：0计划/1结束/2取消/3延期
     */
    @ApiModelProperty(value = "状态：0计划/1结束/2取消/3延期")
    @Column(name = "status")
    private Integer status;



    @ApiModelProperty("公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @Column(name = "public_level")
    private String publicLevel;

    private static final long serialVersionUID = 1L;
}
