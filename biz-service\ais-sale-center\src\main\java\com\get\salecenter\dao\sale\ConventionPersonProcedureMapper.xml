<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ConventionPersonProcedureMapper">
  <resultMap id="BaseResultMap" type="com.get.salecenter.entity.ConventionPersonProcedure">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_convention_person_id" jdbcType="BIGINT" property="fkConventionPersonId" />
    <result column="fk_convention_procedure_id" jdbcType="BIGINT" property="fkConventionProcedureId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>

  <insert id="insert" parameterType="com.get.salecenter.entity.ConventionPersonProcedure">
    insert into r_convention_person_procedure (id, fk_convention_person_id, fk_convention_procedure_id,
      gmt_create, gmt_create_user, gmt_modified,
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkConventionPersonId,jdbcType=BIGINT}, #{fkConventionProcedureId,jdbcType=BIGINT},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP},
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.ConventionPersonProcedure">
    insert into r_convention_person_procedure
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkConventionPersonId != null">
        fk_convention_person_id,
      </if>
      <if test="fkConventionProcedureId != null">
        fk_convention_procedure_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkConventionPersonId != null">
        #{fkConventionPersonId,jdbcType=BIGINT},
      </if>
      <if test="fkConventionProcedureId != null">
        #{fkConventionProcedureId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>


  <select id="getConventionPersonProcedureId" resultType="java.lang.Long">
    select
     id
    from
     r_convention_person_procedure
    where
     fk_convention_person_id = #{personId}
    and
     fk_convention_procedure_id = #{procedureId}

  </select>

  <select id="getConventionPerson" resultType="com.get.salecenter.vo.ConventionPersonProcedureVo">
    select
     a.id,a.fk_convention_person_id,b.name,b.name_chn,b.gender,b.company,b.type,b.bd_code bdCode
    from
    (select id,fk_convention_person_id from r_convention_person_procedure where fk_convention_procedure_id = #{procedureId}) a
    left join m_convention_person b on a.fk_convention_person_id = b.id
    where 1=1
    <if test=" conventionPersonProcedureDto.nameKey != null and conventionPersonProcedureDto.nameKey != ''">
      and (position(#{conventionPersonProcedureDto.nameKey} in b.name) or  position(#{conventionPersonProcedureDto.nameKey} in b.name_chn))
    </if>
    <if test="conventionPersonProcedureDto.companyKey != null and conventionPersonProcedureDto.companyKey != ''">
      and (position(#{conventionPersonProcedureDto.companyKey} in b.company))
    </if>
    <if test="conventionPersonProcedureDto.type != null and conventionPersonProcedureDto.type != ''">
      and (b.type = #{conventionPersonProcedureDto.type})
    </if>
    <if test="conventionPersonProcedureDto.type == 0">
      and (b.type = #{conventionPersonProcedureDto.type})
    </if>
    <if test="conventionPersonProcedureDto.bdNameKey != null and conventionPersonProcedureDto.bdNameKey != ''">
      <if test="bdCodeList != null and bdCodeList.size()>0">
        and
        bd_code
        in
        <foreach item="item" index="index" collection="bdCodeList" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="bdCodeList == null or (bdCodeList != null and bdCodeList.size()==0)">
        and 1=0
      </if>
    </if>

  </select>

  <select id="getNotJoinConventionPerson" resultType="com.get.salecenter.vo.ConventionPersonProcedureVo">
    select
     id as fkConventionPersonId,name,name_chn,gender,company,type,bd_code bdCode
    from
     m_convention_person
    where
     fk_convention_id = #{conventionId}
    and
     id
    not in
     <foreach item="item" index="index" collection="personIds" open="(" separator="," close=")">
       #{item}
     </foreach>
     <if test=" conventionPersonProcedureDto.nameKey != null and conventionPersonProcedureDto.nameKey != ''">
       and (position(#{conventionPersonProcedureDto.nameKey} in name) or  position(#{conventionPersonProcedureDto.nameKey} in name_chn))
     </if>
     <if test="conventionPersonProcedureDto.companyKey != null and conventionPersonProcedureDto.companyKey != ''">
       and (position(#{conventionPersonProcedureDto.companyKey} in company))
     </if>
     <if test="conventionPersonProcedureDto.type != null and conventionPersonProcedureDto.type != ''">
      and (type = #{conventionPersonProcedureDto.type})
     </if>
    <if test="conventionPersonProcedureDto.type == 0">
      and (type = #{conventionPersonProcedureDto.type})
    </if>
    <if test="conventionPersonProcedureDto.bdNameKey != null and conventionPersonProcedureDto.bdNameKey != ''">
      and
       bd_code
      in
      <foreach item="item" index="index" collection="bdCodeList" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>

  </select>
  <select id="conventionPersonProcedureIsEmpty" resultType="java.lang.Boolean">
    select IFNULL(max(id),0) id from r_convention_person_procedure where ${fieldName} = #{id} LIMIT 1

  </select>
    <select id="getConventionPersonProceduresMap" resultType="com.get.salecenter.vo.ConventionPersonVo">
      SELECT
        fk_convention_person_id as id,
        GROUP_CONCAT( fk_convention_procedure_id ) as procedureIds
      FROM
        `r_convention_person_procedure`
      WHERE
        fk_convention_person_id IN
      <foreach item="item" index="index" collection="personIds" open="(" separator="," close=")">
        #{item}
      </foreach>
      GROUP BY
        fk_convention_person_id
    </select>
</mapper>