//package com.get.permissioncenter.utils;
//
//import com.get.permissioncenter.vo.StaffVo;
//import org.apache.shiro.SecurityUtils;
//import org.apache.shiro.authc.AuthenticationToken;
//import org.apache.shiro.authc.UsernamePasswordToken;
//import org.apache.shiro.subject.Subject;
//
///**
// * 登录相关工具类
// *
// * @author: jack
// * @create: 2020/6/22
// * @verison: 1.0
// * @description: 登录相关工具类
// */
//public class LoginUtil {
//    /**
//     * 获取用户登录的shiro信息
//     *
//     * @return
//     */
//    public static Subject getSubject() {
//        return SecurityUtils.getSubject();
//    }
//
//    public static void login(AuthenticationToken token) {
//        getSubject().login(token);
//    }
//
//
//    /**     * 切换身份，登录后，动态更改subject的用户属性
//     * @param staffDto
//     */
//    public static void setStaff(StaffVo staffDto) {
//        Subject subject = SecurityUtils.getSubject();
//      /*  String realmName = subject.getPrincipals().getRealmNames().iterator().next();*/
//        subject.logout();
//       /* SimpleAuthenticationInfo simpleAuthenticationInfo = new SimpleAuthenticationInfo(staffDto, staffDto.getLoginPs(),realmName);*/
//        UsernamePasswordToken token = new UsernamePasswordToken(staffDto.getLoginId(), staffDto.getLoginPs());
//        login(token);
//    }
//
//
//
//
//}
