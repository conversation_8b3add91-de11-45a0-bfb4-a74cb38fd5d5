package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 合同公式-前置专业等级
 *
 * @Date 16:21 2021/6/2
 * <AUTHOR>
 */
@Data
public class ContractFormulaPreMajorLevelDto extends BaseVoEntity {
    /**
     * 合同公式Id
     */
    @ApiModelProperty(value = "合同公式Id")
    private Long fkContractFormulaId;

    /**
     * 专业等级Id(前置等级)
     */
    @ApiModelProperty(value = "专业等级Id(前置等级)")
    private Long fkMajorLevelId;

}
