package com.get.permissioncenter.service;

import com.get.permissioncenter.vo.StaffCourseLevelTypeConfigVo;
import com.get.permissioncenter.entity.StaffCourseLevelTypeConfig;
import com.get.permissioncenter.dto.StaffCourseLevelTypeConfigDto;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-18
 */
public interface StaffCourseLevelTypeConfigService {

    /**
     * 列表数据
     * <AUTHOR>
     * @DateTime 2024/1/22 14:31
     */
    List<StaffCourseLevelTypeConfigVo> getConfigs(Integer type);

    /**
     * 新增
     * <AUTHOR>
     * @DateTime 2024/1/22 14:56
     */
    Long add(StaffCourseLevelTypeConfigDto staffCourseLevelTypeConfigDto);

    /**
     * 修改
     * <AUTHOR>
     * @DateTime 2024/1/22 15:01
     */
    void delete(Long id);

    /**
     * 重置为默认值配置
     * <AUTHOR>
     * @DateTime 2024/1/22 15:10
     */
    void resetDefaultConfig(Integer type);


   /**
    * 拖拽
    * <AUTHOR>
    * @DateTime 2024/3/26 14:56
    */
    void movingOrder(Integer type,Integer start,Integer end);

    /**
     * 是否应用
     * <AUTHOR>
     * @DateTime 2024/3/15 17:51
     */
    void update(StaffCourseLevelTypeConfigDto staffCourseLevelTypeConfigDto);

    /**
     * 获取用户课程等级类型设定配置
     * <AUTHOR>
     * @DateTime 2024/1/18 18:24
     */
    List<StaffCourseLevelTypeConfig> getStaffCourseLevelTypeConfig(Long fkStaffId,Integer type);

}
