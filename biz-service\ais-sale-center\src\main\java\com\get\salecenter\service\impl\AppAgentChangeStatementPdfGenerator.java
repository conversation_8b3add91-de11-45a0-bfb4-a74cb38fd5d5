package com.get.salecenter.service.impl;

import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dto.AppAgentDto;
import com.itextpdf.text.Chunk;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Element;
import com.itextpdf.text.Font;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfWriter;
import feign.template.UriUtils;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import javax.servlet.http.HttpServletResponse;


public class AppAgentChangeStatementPdfGenerator {
    private static final float REQUIRED_SPACE = 200f;
    // 中文字体路径（需自行添加字体文件）
    private static BaseFont baseFont;
    private static Font titleFont, sectionFont, bodyFont, statementFont;

    static {
        try {
            // 初始化字体
            baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.EMBEDDED);
            titleFont = new Font(baseFont, 18, Font.BOLD);
            sectionFont = new Font(baseFont, 14, Font.BOLD);
            bodyFont = new Font(baseFont, 12);
            statementFont = new Font(baseFont, 12);
        } catch (DocumentException | IOException e) {
            e.printStackTrace();
        }
    }

    public static void generatePdf(AppAgentDto appAgentDto, HttpServletResponse response) throws Exception {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition",
                "attachment; filename=\"" + UriUtils.encode("out", StandardCharsets.UTF_8) + ".pdf\"");
        Document document = new Document(PageSize.A4, 50, 50, 50, 50);
        PdfWriter writer = PdfWriter.getInstance(document, response.getOutputStream());
        document.open();

        // 添加标题
        addTitle(document);

        String natureNote = (GeneralTool.isNotEmpty(appAgentDto.getNatureNote()) ? appAgentDto.getNatureNote() : " ");
        String contractNumber = (GeneralTool.isNotEmpty(appAgentDto.getContractNumber()) ? appAgentDto.getContractNumber() : " ");
        // 添加引言部分
        addIntroduction(document, natureNote, contractNumber);


        // 添加声明 section
        addSectionTitle(document, "变更信息 (不变更信息无需填写)");

        // 处理动态数据
        processDynamicData(document, appAgentDto.getChangeStatement());

        // 计算剩余空间并判断是否需要分页
        float currentY = writer.getVerticalPosition(false);
        float remainingSpace = currentY - document.bottomMargin();
        if (remainingSpace < REQUIRED_SPACE) {
            document.newPage();
        }

        // 添加重要声明
        addStatement(document);

        // 添加签名区域
        addSignatureArea(document);

        document.close();
    }

    private static void addTitle(Document document) throws DocumentException {
        Paragraph title = new Paragraph("代理信息变更申请", titleFont);
        title.setAlignment(Element.ALIGN_CENTER);
        title.setSpacingAfter(30);
        document.add(title);
    }


    private static void addIntroduction(Document document, String natureNote, String contractNumber) throws DocumentException {
        String introText = String.format(
                "本人 %s (公司)与华通信诺国际文化交流中心有限公司（香港）签署了代理协议（协议编号：%s），现因实际情况需要，特申请变更协议内填写的相关信息。具体变更内容如下：",
                natureNote, contractNumber
        );

        Paragraph intro = new Paragraph(introText, bodyFont);
        intro.setSpacingAfter(15);
        intro.setLeading(20);
        float fontSize = statementFont.getSize();
        intro.setFirstLineIndent(fontSize * 2);
        document.add(intro);
    }

    private static void addSectionTitle(Document document, String titleText) throws DocumentException {
        Paragraph section = new Paragraph();

        // 拆分标题文本为主要标题和注释部分
        if (titleText.contains(" (不变更信息无需填写)")) {
            String mainTitle = titleText.replace(" (不变更信息无需填写)", "");
            String noteText = " (不变更信息无需填写)";

            // 添加主标题（14号加粗）
            section.add(new Chunk(mainTitle, sectionFont));
            // 添加注释文本（12号常规，使用bodyFont）
            section.add(new Chunk(noteText, bodyFont));
        } else {
            // 普通标题直接使用sectionFont
            section.add(new Chunk(titleText, sectionFont));
        }
//        Paragraph section = new Paragraph(titleText, sectionFont);
        section.setSpacingBefore(15);
        section.setSpacingAfter(10);
        document.add(section);
    }

    private static void processDynamicData(Document document, String changeStatement) throws DocumentException {

        String[] lines = changeStatement.split("\n");
        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) continue;

            Paragraph content = new Paragraph(line, bodyFont);
            content.setLeading(20);
            document.add(content);
        }
    }

    private static void addStatement(Document document) throws DocumentException {
        Paragraph statement = new Paragraph(
                "一旦本次信息更新完成，之前的邮箱信息将予以注销，此后相关业务往来均以此次更新的邮箱为准。",
                statementFont
        );
        statement.setSpacingBefore(30);    // 段前间距
        statement.setSpacingAfter(15);     // 段后间距
        statement.setLeading(20);          // 行高
        float fontSize = statementFont.getSize();
        statement.setFirstLineIndent(fontSize * 2);// 首行缩进2字符（24pt）
        document.add(statement);
    }

    private static void addSignatureArea(Document document) throws DocumentException {
        Paragraph applicant = new Paragraph("申请人：________________________", bodyFont);
        applicant.setSpacingBefore(5);
        applicant.setAlignment(Element.ALIGN_RIGHT);  //右对齐
        document.add(applicant);

        Paragraph note = new Paragraph("（个人代理请手写签名 / 公司协议请加盖公司公章）", bodyFont);
        note.setSpacingBefore(5);
        note.setAlignment(Element.ALIGN_RIGHT);
        document.add(note);

        Paragraph date = new Paragraph("20_______年_____月_____日", bodyFont);
        date.setSpacingBefore(10);
        date.setAlignment(Element.ALIGN_RIGHT);
        document.add(date);
    }

}
