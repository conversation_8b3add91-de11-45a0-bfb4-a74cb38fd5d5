package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2021/6/16
 * @TIME: 14:54
 * @Description:
 **/
@Data
public class EnrolFailureReasonDto extends BaseVoEntity {
    /**
     * 入学失败原因名称
     */
    @ApiModelProperty(value = "入学失败原因名称")
    @Column(name = "reason_name")
    private String reasonName;
    /**
     * 原因Key
     */
    @ApiModelProperty(value = "原因Key")
    @Column(name = "reason_key")
    private String reasonKey;

    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyWord;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;

  
}
