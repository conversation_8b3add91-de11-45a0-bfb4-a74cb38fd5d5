package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2022/6/30
 * @TIME: 16:24
 * @Description:
 **/
@Data
public class AgentApplicationRankingExportVo {
    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    private String agentName;

    /**
     * 性质：公司/个人/工作室/国际学校/其他
     */
    @ApiModelProperty(value = "性质")
    private String nature;

    /**
     * 代理区域名
     */
    @ApiModelProperty(value = "代理区域")
    private String countryAndStateName;
    /**
     * BD名称
     */
    @ApiModelProperty(value = "绑定BD")
    private String bdName;
    /**
     * 新建学生
     */
    @ApiModelProperty(value = "新建学生")
    private Integer createCount;

    /**
     * 定校量（按学生）
     */
    @ApiModelProperty(value = "定校量（按学生）")
    private Integer confirmationCountByStudent;

    /**
     * 成功入学量（按学生）
     */
    @ApiModelProperty(value = "成功入学量（按学生）")
    private Integer successCountByStudent;

    /**
     * 定校量转化率
     */
    @ApiModelProperty(value = "定校量转化率）")
    private String confirmationConversionRate;
    /**
     * 成功入学量转化率
     */
    @ApiModelProperty(value = "成功入学量转化率")
    private String successConversionRate;



}
