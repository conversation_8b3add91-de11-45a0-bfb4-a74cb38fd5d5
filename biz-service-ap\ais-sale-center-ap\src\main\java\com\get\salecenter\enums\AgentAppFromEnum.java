package com.get.salecenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 代理申请来源 枚举
 *
 * <AUTHOR>
 * @Date 2025/6/27 下午12:09
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum AgentAppFromEnum {

    WEB_APPLY_1(0, "网页申请1.0"),

    WEB_APPLY_2(1, "网页申请2.0"),

    PARTNER_APPLY_2(2, "小程序申请2.0")
    ;

    private final Integer code;

    private final String msg;

    private static final Map<Integer, AgentAppFromEnum> AGENT_APP_FROM_ENUM_MAP = new HashMap<>();

    static {
        for (AgentAppFromEnum agentAppFromEnum : AgentAppFromEnum.values()) {
            AGENT_APP_FROM_ENUM_MAP.put(agentAppFromEnum.getCode(), agentAppFromEnum);
        }
    }

    public static AgentAppFromEnum getAgentAppFromEnum(Integer code) {
        return AGENT_APP_FROM_ENUM_MAP.get(code);
    }

    /**
     * 判断是否新表单,
     *
     * @param code
     * @return
     */
    public static Boolean isNewType(Integer code) {
        return Optional.ofNullable(code)
                .map(AgentAppFromEnum::getAgentAppFromEnum)
                .map(agentAppFromEnum -> agentAppFromEnum == AgentAppFromEnum.WEB_APPLY_2
                        || agentAppFromEnum == AgentAppFromEnum.PARTNER_APPLY_2)
                .orElse(false);
    }

}
