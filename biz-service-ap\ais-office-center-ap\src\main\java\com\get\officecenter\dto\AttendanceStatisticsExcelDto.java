package com.get.officecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2022/1/19
 * @TIME: 17:07
 * @Description:
 **/
@Data
public class AttendanceStatisticsExcelDto {
    /**
     * 公司ID
     */
    @ApiModelProperty(value = "公司ID")
    @NotNull(message = "公司ID", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private Long fkCompanyId;
    /**
     * 日期条件
     */
    @ApiModelProperty(value = "日期条件")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date date;
}
