package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2021/6/16
 * @TIME: 12:23
 * @Description:
 **/
@Data
@TableName("m_student_offer_item_defer_entrance_time")
public class StudentOfferItemDeferEntranceTime extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "学生申请方案Id")
    @Column(name = "fk_student_offer_item_id")
    private Long fkStudentOfferItemId;
    @ApiModelProperty(value = "延迟入学时间")
    @Column(name = "defer_entrance_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deferEntranceTime;
}
