package com.get.pmpcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.pmpcenter.entity.AgentCommissionMajorLevelCustom;
import com.get.pmpcenter.vo.common.CommissionMajorLevelVo;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:17
 * @Version 1.0
 */
public interface AgentCommissionMajorLevelCustomService extends IService<AgentCommissionMajorLevelCustom> {

    /**
     * 根据佣金id获取佣金专业等级列表
     * @param commissionIds
     * @return
     */
    List<CommissionMajorLevelVo> getCommissionMajorLevelList(List<Long> commissionIds);
}
