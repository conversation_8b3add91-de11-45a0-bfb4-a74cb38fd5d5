package com.get.financecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/3/31 12:05
 */
@Data
public class PaymentApplicationFormItemVo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("附件")
    private List<FMediaAndAttachedVo> mediaAndAttachedDtos;
    @ApiModelProperty("类型对象名称")
    private String typeName;
    @ApiModelProperty("供应商类型")
    private String targetType;
    @ApiModelProperty("类型对象")
    private PaymentFeeTypeVo paymentFeeTypeDto;
    @ApiModelProperty("币种名称")
    private String currencyTypeName;
    @ApiModelProperty("关联项名称")
    private String relationTargetName;

    //==================实体类PaymentApplicationFormItem===================
    @ApiModelProperty(value = "支付申请单Id")
    private Long fkPaymentApplicationFormId;

    @ApiModelProperty(value = "付款费用类型Id")
    private Long fkPaymentFeeTypeId;

    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "摘要")
    private String summary;

    @ApiModelProperty(value = "关联类型Key（目标类型表名）")
    private String relationTargetKey;

    @ApiModelProperty(value = "关联类型Id（目标类型表对应记录项Id）")
    private Long relationTargetId;

    @ApiModelProperty(value = "关联项公司id")
    private Long relationTargetCompanyId;
}
