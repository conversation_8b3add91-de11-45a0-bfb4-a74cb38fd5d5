package com.get.salecenter.service.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.salecenter.dao.sale.AgentContractFormulaInstitutionCourseMapper;
import com.get.salecenter.entity.AgentContractFormulaInstitutionCourse;
import com.get.salecenter.service.IAgentContractFormulaInstitutionCourseService;
import com.get.salecenter.dto.AgentContractFormulaInstitutionCourseDto;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2021/4/22 18:02
 * @verison: 1.0
 * @description:
 */
@Service
public class AgentContractFormulaInstitutionCourseServiceImpl implements IAgentContractFormulaInstitutionCourseService {
    @Resource
    private AgentContractFormulaInstitutionCourseMapper agentContractFormulaInstitutionCourseMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Override
    public Long addAgentContractFormulaInstitutionCourse(AgentContractFormulaInstitutionCourseDto agentContractFormulaInstitutionCourseDto) {
        AgentContractFormulaInstitutionCourse agentContractFormulaInstitutionCourse = BeanCopyUtils.objClone(agentContractFormulaInstitutionCourseDto, AgentContractFormulaInstitutionCourse::new);
        utilService.updateUserInfoToEntity(agentContractFormulaInstitutionCourse);
        agentContractFormulaInstitutionCourseMapper.insertSelective(agentContractFormulaInstitutionCourse);
        return agentContractFormulaInstitutionCourse.getId();
    }

    @Override
    public void deleteByFkid(Long agentContractFormulaId) {
//        Example example = new Example(AgentContractFormulaInstitutionCourse.class);
//        example.createCriteria().andEqualTo("fkAgentContractFormulaId", agentContractFormulaId);
//        agentContractFormulaInstitutionCourseMapper.deleteByExample(example);

        agentContractFormulaInstitutionCourseMapper.delete(Wrappers.<AgentContractFormulaInstitutionCourse>lambdaQuery().eq(AgentContractFormulaInstitutionCourse::getFkAgentContractFormulaId, agentContractFormulaId));
    }

    @Override
    public Map<Long, String> getInstitutionCourseNameMapByFkids(List<Long> agentContractFormulaIds) {
        //关系map
        Map<Long, List<Long>> idMap = new HashMap<>();
        Map<Long, String> nameMap = new HashMap<>();
        //全部institutionCourseId集合
        Set<Long> institutionCourseIdSet = new HashSet<>();
        for (Long agentContractFormulaId : agentContractFormulaIds) {
            //通过agentContractFormulaId获取对应所有课程id
            List<Long> institutionCourseIds = getInstitutionCourseIdListByFkid(agentContractFormulaId);
            institutionCourseIdSet.addAll(institutionCourseIds);
            //agentContractFormulaId和institutionCourseIds一一对应关系map
            idMap.put(agentContractFormulaId, institutionCourseIds);
        }
        institutionCourseIdSet.removeIf(Objects::isNull);
        //feign调用一次查出 institutionCourseId和institutionCourseName对应关系map
        Map<Long, String> institutionCourseNameMap = new HashMap<>();
        Result<Map<Long, String>> result = institutionCenterClient.getInstitutionCourseNamesByIds(institutionCourseIdSet);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            institutionCourseNameMap = result.getData();
            //map由agentContractFormulaId 对应 institutionCourseIds 转成 agentContractFormulaId 对应 institutionCourseNames
            for (Map.Entry<Long, List<Long>> agentContractFormula : idMap.entrySet()) {
                List<String> institutionCourseNames = new ArrayList<>();
                List<Long> institutionCourseIds = agentContractFormula.getValue();
                for (Long institutionCourseId : institutionCourseIds) {
                    institutionCourseNames.add(institutionCourseNameMap.get(institutionCourseId));
                }
                nameMap.put(agentContractFormula.getKey(), StringUtils.join(institutionCourseNames, "，"));
            }
        }
        return nameMap;
    }

    @Override
    public List<Long> getInstitutionCourseIdListByFkid(Long agentContractFormulaId) {
        return agentContractFormulaInstitutionCourseMapper.getInstitutionCourseIdsByFkid(agentContractFormulaId);
    }
}
