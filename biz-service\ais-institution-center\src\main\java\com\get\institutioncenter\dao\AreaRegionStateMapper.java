package com.get.institutioncenter.dao;

import com.get.core.mybatis.mapper.GetMapper;
import com.get.institutioncenter.vo.AreaRegionVo;
import com.get.institutioncenter.entity.AreaRegionState;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface AreaRegionStateMapper extends GetMapper<AreaRegionState> {
    int insert(AreaRegionState record);

    int insertSelective(AreaRegionState record);

    int updateByPrimaryKeySelective(AreaRegionState record);

    int updateByPrimaryKey(AreaRegionState record);

    int updateFkAreaRegionIdByAreaStateId(@Param("areaRegionState") AreaRegionState areaRegionState);

    List<AreaRegionVo> getAreaRegionDtoByAreaStateId(@Param("fkAreaStateIds") Set<Long> fkAreaStateIds);

    int deleteByAreaStateId(@Param("fkAreaStateId") Long fkAreaStateId);
}