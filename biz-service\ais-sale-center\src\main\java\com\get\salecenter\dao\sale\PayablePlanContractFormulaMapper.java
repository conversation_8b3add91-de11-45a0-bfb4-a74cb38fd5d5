package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.PayablePlanContractFormula;
import org.apache.ibatis.annotations.Mapper;


@Mapper
public interface PayablePlanContractFormulaMapper extends BaseMapper<PayablePlanContractFormula> {
    int insert(PayablePlanContractFormula record);

    int insertSelective(PayablePlanContractFormula record);

    boolean isExistByFormulaId(Long id);
}