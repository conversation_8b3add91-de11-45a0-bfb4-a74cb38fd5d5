package com.get.platformconfigcenter.dao.appissue;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.AppFormDivision;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("issuedb")
public interface AppFormDivisionMapper extends BaseMapper<AppFormDivision> {


//    int insert(AppFormDivision record);
//
//    int insertSelective(AppFormDivision record);
//
//    int updateByPrimaryKeySelective(AppFormDivision record);
//
//    int updateByPrimaryKey(AppFormDivision record);
//
//    /**
//     * 获取最大排序值
//     *
//     * @Date 16:23 2021/5/20
//     * <AUTHOR>
//     */
//    Integer getMaxViewOrder();
}