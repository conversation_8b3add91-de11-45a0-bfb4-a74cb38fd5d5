<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.platformconfigcenter.dao.appissue.StudentInstitutionCourseMapper">
    <resultMap id="BaseResultMap" type="com.get.platformconfigcenter.entity.StudentInstitutionCourse">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_student_id" jdbcType="BIGINT" property="fkStudentId"/>
        <result column="fk_area_country_id" jdbcType="BIGINT" property="fkAreaCountryId"/>
        <result column="fk_institution_id" jdbcType="BIGINT" property="fkInstitutionId"/>
        <result column="fk_major_level_id" jdbcType="BIGINT" property="fkMajorLevelId"/>
        <result column="fk_institution_course_id" jdbcType="BIGINT" property="fkInstitutionCourseId"/>
        <result column="opening_time" jdbcType="DATE" property="openingTime"/>
        <result column="institution_course_name" jdbcType="VARCHAR" property="institutionCourseName"/>
        <result column="info_json" jdbcType="LONGVARCHAR" property="infoJson"/>
        <result column="view_order" jdbcType="INTEGER" property="viewOrder"/>
        <result column="status_step" jdbcType="INTEGER" property="statusStep"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, fk_student_id, fk_area_country_id, fk_institution_id, fk_major_level_id, fk_institution_course_id, 
    opening_time,institution_course_name, info_json, view_order, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
  </sql>
    <!--TODO 注释sql-->
<!--    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id"-->
<!--            parameterType="com.get.platformconfigcenter.entity.StudentInstitutionCourse">-->
<!--        insert into m_student_institution_course-->
<!--        <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--            <if test="id != null">-->
<!--                id,-->
<!--            </if>-->
<!--            <if test="fkStudentId != null">-->
<!--                fk_student_id,-->
<!--            </if>-->
<!--            <if test="fkAreaCountryId != null">-->
<!--                fk_area_country_id,-->
<!--            </if>-->
<!--            <if test="fkInstitutionId != null">-->
<!--                fk_institution_id,-->
<!--            </if>-->
<!--            <if test="fkMajorLevelId != null">-->
<!--                fk_major_level_id,-->
<!--            </if>-->
<!--            <if test="fkInstitutionCourseId != null">-->
<!--                fk_institution_course_id,-->
<!--            </if>-->
<!--            <if test="openingTime != null">-->
<!--                opening_time,-->
<!--            </if>-->
<!--            <if test="institutionCourseName != null">-->
<!--                institution_course_name,-->
<!--            </if>-->
<!--            <if test="infoJson != null">-->
<!--                info_json,-->
<!--            </if>-->
<!--            <if test="viewOrder != null">-->
<!--                view_order,-->
<!--            </if>-->
<!--            <if test="statusStep != null">-->
<!--                status_step,-->
<!--            </if>-->
<!--            <if test="gmtCreate != null">-->
<!--                gmt_create,-->
<!--            </if>-->
<!--            <if test="gmtCreateUser != null">-->
<!--                gmt_create_user,-->
<!--            </if>-->
<!--            <if test="gmtModified != null">-->
<!--                gmt_modified,-->
<!--            </if>-->
<!--            <if test="gmtModifiedUser != null">-->
<!--                gmt_modified_user,-->
<!--            </if>-->
<!--        </trim>-->
<!--        <trim prefix="values (" suffix=")" suffixOverrides=",">-->
<!--            <if test="id != null">-->
<!--                #{id,jdbcType=BIGINT},-->
<!--            </if>-->
<!--            <if test="fkStudentId != null">-->
<!--                #{fkStudentId,jdbcType=BIGINT},-->
<!--            </if>-->
<!--            <if test="fkAreaCountryId != null">-->
<!--                #{fkAreaCountryId,jdbcType=BIGINT},-->
<!--            </if>-->
<!--            <if test="fkInstitutionId != null">-->
<!--                #{fkInstitutionId,jdbcType=BIGINT},-->
<!--            </if>-->
<!--            <if test="fkMajorLevelId != null">-->
<!--                #{fkMajorLevelId,jdbcType=BIGINT},-->
<!--            </if>-->
<!--            <if test="fkInstitutionCourseId != null">-->
<!--                #{fkInstitutionCourseId,jdbcType=BIGINT},-->
<!--            </if>-->
<!--            <if test="openingTime != null">-->
<!--                #{openingTime,jdbcType=DATE},-->
<!--            </if>-->
<!--            <if test="institutionCourseName !=null">-->
<!--                #{institutionCourseName,jdbcType=VARCHAR},-->
<!--            </if>-->
<!--            <if test="infoJson != null">-->
<!--                #{infoJson,jdbcType=LONGVARCHAR},-->
<!--            </if>-->
<!--            <if test="viewOrder != null">-->
<!--                #{viewOrder,jdbcType=INTEGER},-->
<!--            </if>-->
<!--            <if test="statusStep != null">-->
<!--                #{statusStep,jdbcType=INTEGER},-->
<!--            </if>-->
<!--            <if test="gmtCreate != null">-->
<!--                #{gmtCreate,jdbcType=TIMESTAMP},-->
<!--            </if>-->
<!--            <if test="gmtCreateUser != null">-->
<!--                #{gmtCreateUser,jdbcType=VARCHAR},-->
<!--            </if>-->
<!--            <if test="gmtModified != null">-->
<!--                #{gmtModified,jdbcType=TIMESTAMP},-->
<!--            </if>-->
<!--            <if test="gmtModifiedUser != null">-->
<!--                #{gmtModifiedUser,jdbcType=VARCHAR},-->
<!--            </if>-->
<!--        </trim>-->
<!--    </insert>-->
    <!--TODO 注释sql-->
<!--    <select id="isExistByCourseId" resultType="java.lang.Boolean">-->
<!--        SELECT IFNULL(max(id),0) id from m_student_institution_course where fk_institution_course_id=#{courseId}-->
<!--    </select>-->

</mapper>