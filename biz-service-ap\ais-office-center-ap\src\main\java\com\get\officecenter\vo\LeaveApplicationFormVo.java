package com.get.officecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.officecenter.entity.LeaveApplicationForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/12 19:15
 * @verison: 1.0
 * @description:
 */
@Data
public class LeaveApplicationFormVo  extends BaseVoEntity {

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 部门Id
     */
    @ApiModelProperty(value = "部门Id")
    private Long fkDepartmentId;

    /**
     * 办公室Id
     */
    @ApiModelProperty(value = "办公室Id")
    private Long fkOfficeId;

    /**
     * 申请人Id
     */
    @ApiModelProperty(value = "申请人Id")
    private Long fkStaffId;

    /**
     * 工休申请单类型Id
     */
    @ApiModelProperty(value = "工休申请单类型Id")
    private Long fkLeaveApplicationFormTypeId;

    /**
     * 工休申请单编号（系统生成）
     */
    @ApiModelProperty(value = "工休申请单编号（系统生成）")
    private String num;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    /**
     * 天数，2小时0.25, 4小时0.5, 8小时1天
     */
    @ApiModelProperty(value = "天数，2小时0.25, 4小时0.5, 8小时1天")
    private BigDecimal days;

    /**
     * 申请原因
     */
    @ApiModelProperty(value = "申请原因")
    private String reason;

    /**
     * 是否直属上司口头同意，0否/1是
     */
    @ApiModelProperty(value = "是否直属上司口头同意，0否/1是")
    private Boolean isAgree;

    /**
     * 状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废
     */
    @ApiModelProperty(value = "状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废")
    private Integer status;

    /**
     * 关联撤销表单Id
     */
    @ApiModelProperty(value = "关联撤销表单Id")
    private Long fkLeaveApplicationFormIdRevoke;

    /**
     * 组别guid
     */
    @ApiModelProperty(value = "组别guid")
    private String groupGuid;
    /**
     * 工休单类型名称
     */
    @ApiModelProperty(value = "工休单类型名称")
    private String leaveApplicationFormTypeName;

    /**
     * 工休单类型key
     */
    @ApiModelProperty(value = "工休单类型key")
    private String leaveApplicationFormTypeKey;

    /**
     * 入职时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("入职时间")
    private Date entryDate;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 办公室名称
     */
    @ApiModelProperty(value = "办公室名称")
    private String officeName;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    /**
     * 报销人名称
     */
    @ApiModelProperty(value = "报销人名称")
    private String staffName;

    /**
     * 状态 0待签1代办2无
     */
    @ApiModelProperty(value = "报销单状态 0待签1代办2无")
    private Integer expenseClaimFormStatus;

    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    private Long taskId;
    /**
     * 实例id
     */
    @ApiModelProperty(value = "实例id")
    private Long procInstId;

    /**
     * 任务版本号
     */
    @ApiModelProperty(value = "任务版本号")
    private Integer taskVersion;

    /**
     * 年假剩余天数
     */
    @ApiModelProperty(value = "年假剩余天数")
    private BigDecimal annualLeaveDays;

    /**
     * 补休剩余天数
     */
    @ApiModelProperty(value = "补休剩余天数")
    private BigDecimal compensatoryLeaveDays;

    /**
     * 关联撤销表单状态
     */
    @ApiModelProperty(value = "关联撤销表单状态")
    private Integer fkLeaveApplicationFormIdRevokeStatus;

    /**
     * 同意按钮状态
     */
    @ApiModelProperty(value = "同意按钮状态")
    private Boolean agreeButtonType;

    /**
     * 拒绝按钮状态
     */
    @ApiModelProperty(value = "拒绝按钮状态")
    private Boolean refuseButtonType;

    /**
     * 审批人名称
     */
    @ApiModelProperty(value = "审批人名称")
    private String assigneeName;

    @ApiModelProperty(value = "是否存在附件；true为有附件")
    private Boolean existMediaAndAttacheds;


    @ApiModelProperty(value = "申请中库存提醒")
    private String applyingStockRemind;

    @ApiModelProperty(value = "是否在职，0否/1是")
    private Boolean isOnDuty;

    @ApiModelProperty(value = "附件列表")
    private List<OfficeMediaAndAttachedVo> mediaAndAttachedDtos;

}
