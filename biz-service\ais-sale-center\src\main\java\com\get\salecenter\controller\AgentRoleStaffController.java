package com.get.salecenter.controller;

import com.alibaba.fastjson.JSONObject;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.TableEnum;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.vo.AgentRoleStaffVo;
import com.get.salecenter.vo.AgentVo;
import com.get.salecenter.service.AgentRoleStaffService;
import com.get.salecenter.dto.AgentRoleStaffDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.util.List;

/**
 * 代理项目成员配置管理
 *
 * <AUTHOR>
 * @date 2021/8/2 11:06
 */
@Api(tags = "代理项目成员配置管理")
@RestController
@RequestMapping("sale/agentRoleStaff")
public class AgentRoleStaffController {
    @Resource
    private AgentRoleStaffService agentRoleStaffService;

    /**
     * 代理项目成员配置管理列表
     *
     * @Date 11:34 2021/8/2
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理项目成员配置管理/代理项目成员配置管理列表")
    @PostMapping("datas")
    public ResponseBo<AgentVo> datas(@RequestBody SearchBean<AgentRoleStaffDto> page) {
        List<AgentVo> datas = agentRoleStaffService.getAgentRoleStaffs(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 批量分配项目成员
     *
     * @Date 16:39 2021/8/2
     * <AUTHOR>
     */
    @ApiOperation(value = "批量分配项目成员", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理项目成员配置管理/批量分配项目成员")
    @PostMapping("batchUpdate")
    public ResponseBo batchUpdate(@RequestBody AgentRoleStaffDto agentRoleStaffDto) {
        agentRoleStaffService.batchUpdate(agentRoleStaffDto);
        return ResponseBo.ok();
    }

    /**
     * 批量移除项目成员
     *
     * @Date 16:39 2021/8/2
     * <AUTHOR>
     */
    @ApiOperation(value = "批量移除项目成员", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理项目成员配置管理/批量移除项目成员")
    @PostMapping("batchRemoveUpdate")
    public ResponseBo batchRemoveUpdate(@RequestBody AgentRoleStaffDto agentRoleStaffDto) {
        agentRoleStaffService.batchRemoveUpdate(agentRoleStaffDto);
        return ResponseBo.ok();
    }

    /**
     * 代理项目成员配置详情
     *
     * @param id
     * @param countryId 【申请方案管理】选择代理，自动带出角色设定 使用的参数
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为代理id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/代理项目成员配置管理/代理项目成员配置详情")
    @GetMapping("/{id}")
    public ResponseBo<AgentRoleStaffVo> detail(@PathVariable("id") Long id, @RequestParam("companyId") Long companyId, @RequestParam(required = false, value = "countryId") Long countryId,
                                               @RequestParam(required = false, value = "fkTypeKey") String fkTypeKey) {
        List<AgentRoleStaffVo> data = agentRoleStaffService.detail(id, countryId, companyId, fkTypeKey);
        return new ListResponseBo<>(data);
    }

    @ApiOperation(value = "新增获取代理项目成员配置", notes = "id为代理id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/代理项目成员配置管理/新增获取代理项目成员配置")
    @GetMapping("getAgentRoleStaff")
    public ResponseBo<AgentRoleStaffVo> getAgentRoleStaff(@RequestParam(required = false, value = "agentId") Long agentId,
                                                          @RequestParam("companyId") Long companyId,
                                                          @RequestParam("countryId") Long countryId,
                                                          @RequestParam("fkTypeKey") String fkTypeKey) {
        List<AgentRoleStaffVo> data = agentRoleStaffService.getAgentRoleStaff(agentId, countryId, companyId, fkTypeKey);
        return new ListResponseBo<>(data);
    }

    @ApiOperation(value = "新增获取代理项目成员配置", notes = "id为代理id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/代理项目成员配置管理/新增获取代理项目成员配置")
    @PostMapping("getAgentRoleStaffToIssue")
    @VerifyLogin(IsVerify = false)
    public ResponseBo<AgentRoleStaffVo> getAgentRoleStaffToIssue(HttpServletRequest request) throws Exception {
        BufferedReader reader = request.getReader();
        String str, wholeStr = "";
        while ((str = reader.readLine()) != null) {
            wholeStr += str;
        }
        JSONObject jsonObject = JSONObject.parseObject(wholeStr);

        if (GeneralTool.isEmpty(jsonObject.get("agentId"))||GeneralTool.isEmpty(jsonObject.get("companyId"))||GeneralTool.isEmpty(jsonObject.get("countryId"))||GeneralTool.isEmpty(jsonObject.get("fkTypeKey"))){
            return null;
        }

        Long agentId = Long.valueOf(jsonObject.get("agentId").toString());
        Long companyId=Long.valueOf(jsonObject.get("companyId").toString());
        Long countryId=Long.valueOf(jsonObject.get("countryId").toString());
        String fkTypeKey=jsonObject.get("fkTypeKey").toString();
        List<AgentRoleStaffVo> data = agentRoleStaffService.getAgentRoleStaff(agentId, countryId, companyId, fkTypeKey);
        return new ListResponseBo<>(data);
    }





    /**
     * 更新代理项目成员配置
     *
     * @return
     * @Date 16:39 2021/8/2
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/代理项目成员配置管理/更新代理项目成员配置")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated(AgentRoleStaffDto.Update.class) ValidList<AgentRoleStaffDto> agentRoleStaffDtos) {
        agentRoleStaffService.update(agentRoleStaffDtos);
        return UpdateResponseBo.ok();
    }

    /**
     * 删除绑定关系
     *
     * @Date 16:16 2021/8/3
     * <AUTHOR>
     */
    @ApiOperation(value = "删除绑定关系", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/代理项目成员配置管理/删除代理项目成员配置")
    @PostMapping("delete")
    public ResponseBo delete(@RequestBody AgentRoleStaffDto agentRoleStaffDto) {
        agentRoleStaffService.delete(agentRoleStaffDto);
        return DeleteResponseBo.ok();
    }

    /**
     * 业务类型下拉框
     *
     * @param
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "业务类型下拉框", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理项目成员配置管理/业务类型下拉框")
    @PostMapping("getBusinessTypeSelect")
    public ResponseBo getBusinessTypeSelect() {
        return new ListResponseBo<>(TableEnum.enumsTranslation2Arrays(TableEnum.BUSINESS_TYPE));
    }

    /**
     * 通用项目成员配置列表
     *
     * @Date 11:34 2021/8/2
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理项目成员配置管理/代理项目成员配置管理列表")
    @PostMapping("getCommonAgentRoleStaffs")
    public ResponseBo<AgentRoleStaffVo> getCommonAgentRoleStaffs(@RequestBody SearchBean<AgentRoleStaffDto> page) {
        List<AgentRoleStaffVo> datas = agentRoleStaffService.getCommonAgentRoleStaffs(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * 新增通用项目成员配置
     *
     * @return
     * @Date 16:39 2021/8/2
     * <AUTHOR>
     */
    @ApiOperation(value = "新增通用项目成员配置", notes = "新增通用项目成员配置")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理项目成员配置管理/新增通用项目成员配置")
    @PostMapping("addCommonAgentRoleStaff")
    public ResponseBo addCommonAgentRoleStaff(@RequestBody @Validated(BaseVoEntity.Add.class) AgentRoleStaffDto agentRoleStaffDto) {
        agentRoleStaffService.addCommonAgentRoleStaff(agentRoleStaffDto);
        return SaveResponseBo.ok();
    }

    /**
     * 修改通用项目成员配置
     *
     * @return
     * @Date 16:39 2021/8/2
     * <AUTHOR>
     */
    @ApiOperation(value = "修改通用项目成员配置", notes = "修改通用项目成员配置")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/代理项目成员配置管理/修改通用项目成员配置")
    @PostMapping("updateCommonAgentRoleStaff")
    public ResponseBo updateCommonAgentRoleStaff(@RequestBody @Validated(BaseVoEntity.Update.class) AgentRoleStaffDto agentRoleStaffDto) {
        return UpdateResponseBo.ok(agentRoleStaffService.updateCommonAgentRoleStaff(agentRoleStaffDto));
    }

    /**
     * 通用项目成员配置详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "通用项目成员配置详情", notes = "id为代理id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/代理项目成员配置管理/通用项目成员配置详情")
    @GetMapping("getCommonAgentRoleStaff/{id}")
    public ResponseBo<AgentRoleStaffVo> getCommonAgentRoleStaff(@PathVariable("id") Long id) {
        AgentRoleStaffVo data = agentRoleStaffService.findCommonAgentRoleStaffById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @Description: 项目成员名称下拉框
     * @Author: Jerry
     * @Date:12:14 2021/11/11
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation("项目成员名称下拉框")
    @GetMapping("getStaffNameSelect")
    public ResponseBo<BaseSelectEntity> getStaffRoleNameSelect(@RequestParam("fkCompanyId") Long fkCompanyId) {
        return new ListResponseBo<>(agentRoleStaffService.getStaffNameSelect(fkCompanyId));
    }


    /**
     * 删除
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/代理项目成员配置管理/通用项目成员配置删除")
    @PostMapping("deleteCommonAgentRoleStaff/{id}")
    public ResponseBo deleteCommonAgentRoleStaff(@PathVariable("id") Long id) {
        agentRoleStaffService.deleteCommonAgentRoleStaff(id);
        return DeleteResponseBo.ok();
    }

}
