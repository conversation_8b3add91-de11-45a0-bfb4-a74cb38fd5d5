package com.get.votingcenter.service;

import com.get.votingcenter.vo.VotingItemOptionVo;
import com.get.votingcenter.dto.VotingItemOptionListDto;
import com.get.votingcenter.dto.VotingItemOptionUpdateDto;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/9/26 16:45
 * @verison: 1.0
 * @description:
 */
public interface IVotingItemOptionService {
    /**
     * @return Long
     * @Description :新增
     * @Param [votingItemOptionUpdateDto]
     * <AUTHOR>
     */
    Long addVotingItemOption(VotingItemOptionUpdateDto votingItemOptionUpdateDto);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void deleteVotingItemOption(Long id);

    /**
     * @return VotingItemOptionVo
     * @Description :修改
     * @Param [votingItemOptionUpdateDto]
     * <AUTHOR>
     */
    VotingItemOptionVo updateVotingItemOption(VotingItemOptionUpdateDto votingItemOptionUpdateDto);

    /**
     * @return VotingItemOptionVo
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    VotingItemOptionVo findVotingItemOptionById(Long id);

    /**
     * @return List<VotingItemOptionVo>
     * @Description :列表
     * @Param [votingItemOptionListDto, page]
     * <AUTHOR>
     */
    List<VotingItemOptionVo> getVotingItemOptions(VotingItemOptionListDto votingItemOptionListDto);

    /**
     * @return void
     * @Description :上移下移
     * @Param [votingItemOptionListDtos]
     * <AUTHOR>
     */
    void movingOrder(List<VotingItemOptionListDto> votingItemOptionListDtos);

}
