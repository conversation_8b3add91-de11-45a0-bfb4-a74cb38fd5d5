package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/11/21 19:17
 * @verison: 1.0
 * @description:
 */
@Data
public class BatchStudentOfferNoticeDto {

    @ApiModelProperty("邮件ids")
    private List<Long> studentOfferNoticeIds;


    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开学时间开始")
    private Date openingTimeStart;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开学时间结束")
    private Date openingTimeEnd;

}
