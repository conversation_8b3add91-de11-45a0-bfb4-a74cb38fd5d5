package com.get.permissioncenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.redis.cache.GetRedis;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.dao.PermissionGradeMapper;
import com.get.permissioncenter.dao.PermissionGroupGradeNameMapper;
import com.get.permissioncenter.dao.PermissionGroupGradeResourceMapper;
import com.get.permissioncenter.dao.PermissionGroupGradeStaffMapper;
import com.get.permissioncenter.dao.PermissionGroupMapper;
import com.get.permissioncenter.dao.ResourceMapper;
import com.get.permissioncenter.dao.StaffMapper;
import com.get.permissioncenter.dao.StaffResourceMapper;
import com.get.permissioncenter.dto.PermissionGroupGradeResourceDto;
import com.get.permissioncenter.vo.GroupGradeResourceVo;
import com.get.permissioncenter.vo.PermissionGroupGradeForMovingContext;
import com.get.permissioncenter.vo.PermissionGroupGradeResourceVo;
import com.get.permissioncenter.vo.ResourceVo;
import com.get.permissioncenter.entity.PermissionGrade;
import com.get.permissioncenter.entity.PermissionGroup;
import com.get.permissioncenter.entity.PermissionGroupGradeName;
import com.get.permissioncenter.entity.PermissionGroupGradeResource;
import com.get.permissioncenter.entity.Staff;
import com.get.permissioncenter.entity.StaffResource;
import com.get.permissioncenter.service.IGroupGradeResourceService;
import com.get.permissioncenter.service.IPermissionGroupGradeNameService;
import com.get.permissioncenter.service.IResourceService;
import com.get.permissioncenter.dto.PermissionGroupGradeForMovingAndCopyingDto;
import com.get.permissioncenter.dto.ResourceDto;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: jack
 * @create: 2020/7/13
 * @verison: 1.0
 * @description: 权限系统资源配置业务实现类
 */
@Service
public class GroupGradeResourceServiceImpl extends BaseServiceImpl<PermissionGroupGradeResourceMapper, PermissionGroupGradeResource> implements IGroupGradeResourceService {
    @Resource
    private PermissionGradeMapper permissionGradeMapper;
    @Resource
    private PermissionGroupMapper permissionGroupMapper;
    @Resource
    private PermissionGroupGradeResourceMapper permissionGroupGradeResourceMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private StaffResourceMapper staffResourceMapper;
    @Resource
    private ResourceMapper resourceMapper;
    @Resource
    private PermissionGroupGradeStaffMapper permissionGroupGradeStaffMapper;
    @Resource
    private StaffMapper staffMapper;
    @Resource
    private GetRedis getRedis;
    @Resource
    private IResourceService resourceService;
    @Resource
    private PermissionGroupGradeNameMapper permissionGroupGradeNameMapper;
    @Lazy
    @Resource
    private IPermissionGroupGradeNameService permissionGroupGradeNameService;

    @Override
    public PermissionGroupGradeResourceVo getGroupGradeResource(Long companyId) {
        if (GeneralTool.isEmpty(companyId)) {
//            companyId = StaffContext.getStaff().getFkCompanyId();
            companyId = SecureUtil.getFkCompanyId();
        }
        PermissionGroupGradeResourceVo permissionGroupGradeResourceVo = new PermissionGroupGradeResourceVo();
        List<GroupGradeResourceVo> resourcenums = new ArrayList<GroupGradeResourceVo>();
//        Example examplegrade = new Example(PermissionGrade.class);
//        Example.Criteria criteria = examplegrade.createCriteria();
//        Example examplegroup = new Example(PermissionGroup.class);
//        Example.Criteria criteria1 = examplegroup.createCriteria();
//        criteria.andEqualTo("fkCompanyId", companyId);
//        examplegrade.orderBy("viewOrder").desc();
//        criteria1.andEqualTo("fkCompanyId", companyId);
//        examplegroup.orderBy("viewOrder").desc();
//        List<PermissionGrade> permissionGrades = permissionGradeMapper.selectByExample(examplegrade);
//        List<PermissionGroup> permissionGroups = permissionGroupMapper.selectByExample(examplegroup);

        List<PermissionGrade> permissionGrades = this.permissionGradeMapper.selectList(Wrappers.<PermissionGrade>query().lambda().eq(PermissionGrade::getFkCompanyId, companyId).orderByDesc(PermissionGrade::getViewOrder));
        List<PermissionGroup> permissionGroups = this.permissionGroupMapper.selectList(Wrappers.<PermissionGroup>query().lambda().eq(PermissionGroup::getFkCompanyId, companyId).orderByDesc(PermissionGroup::getViewOrder));

        if (GeneralTool.isNotEmpty(permissionGrades) && GeneralTool.isNotEmpty(permissionGroups)) {
            for (PermissionGroup permissionGroup : permissionGroups) {
                for (PermissionGrade permissionGrade : permissionGrades) {
                    Integer resourcenum = permissionGroupGradeResourceMapper.CountByGroupAndGrade(permissionGroup.getId(), permissionGrade.getId());
                    String name = permissionGroupGradeNameMapper.selectNameByGroupAndGrade(permissionGroup.getId(), permissionGrade.getId());
                    GroupGradeResourceVo dto = new GroupGradeResourceVo();
                    dto.setGroupGradeResourceCount(resourcenum);
                    dto.setGroupGradeResourceName(name);
                    resourcenums.add(dto);
                }
            }
        }
        permissionGroupGradeResourceVo.setGroupGradeResourceDto(resourcenums);
        permissionGroupGradeResourceVo.setPermissionGrades(permissionGrades);
        permissionGroupGradeResourceVo.setPermissionGroups(permissionGroups);
        return permissionGroupGradeResourceVo;
    }

    @Override
    public List<String> getGroupGradeResources(PermissionGroupGradeResourceDto permissionGroupGradeResourceDto) {
        Long fkCompanyId = SecureUtil.getFkCompanyId();
        List<PermissionGroupGradeResource> permissionGroupGradeResources = permissionGroupGradeResourceMapper.selectListByCompanyId(permissionGroupGradeResourceDto,fkCompanyId);
//        LambdaQueryWrapper<PermissionGroupGradeResource> wrapper = new LambdaQueryWrapper();
//        if (GeneralTool.isNotEmpty(permissionGroupGradeResourceDto)) {
//            if (GeneralTool.isNotEmpty(permissionGroupGradeResourceDto.getFkPermissionGroupId())) {
//                wrapper.eq(PermissionGroupGradeResource::getFkPermissionGroupId, permissionGroupGradeResourceDto.getFkPermissionGroupId());
//            }
//            if (GeneralTool.isNotEmpty(permissionGroupGradeResourceDto.getFkPermissionGradeId())) {
//                wrapper.eq(PermissionGroupGradeResource::getFkPermissionGradeId, permissionGroupGradeResourceDto.getFkPermissionGradeId());
//            }
//        }
//        List<PermissionGroupGradeResource> permissionGroupGradeResources = this.permissionGroupGradeResourceMapper.selectList(wrapper);
        List<String> convertDatas = new ArrayList<>();
        if (GeneralTool.isNotEmpty(permissionGroupGradeResources)) {
            for (PermissionGroupGradeResource permissionGroupGradeResource : permissionGroupGradeResources) {
                convertDatas.add(permissionGroupGradeResource.getFkResourceKey());
            }
        }
        return convertDatas;
    }

    //    @Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = YException.class)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateGroupGradeResources(PermissionGroupGradeResourceDto permissionGroupGradeResourceDto) {
        if (GeneralTool.isEmpty(permissionGroupGradeResourceDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(permissionGroupGradeResourceDto.getFkPermissionGradeId()) || GeneralTool.isEmpty(permissionGroupGradeResourceDto.getFkPermissionGroupId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        permissionGroupGradeResourceMapper.deleteByGroupAndGrade(permissionGroupGradeResourceDto.getFkPermissionGroupId(), permissionGroupGradeResourceDto.getFkPermissionGradeId());
        if (GeneralTool.isNotEmpty(permissionGroupGradeResourceDto.getResourceKeys())) {
            for (String keys : permissionGroupGradeResourceDto.getResourceKeys()) {
                PermissionGroupGradeResource permissionGroupGradeResource = new PermissionGroupGradeResource();
                permissionGroupGradeResource.setFkPermissionGroupId(permissionGroupGradeResourceDto.getFkPermissionGroupId());
                permissionGroupGradeResource.setFkPermissionGradeId(permissionGroupGradeResourceDto.getFkPermissionGradeId());
                permissionGroupGradeResource.setFkResourceKey(keys);
//                utilService.setCreateInfo(permissionGroupGradeResource);
                utilService.updateUserInfoToEntity(permissionGroupGradeResource);
                permissionGroupGradeResourceMapper.insert(permissionGroupGradeResource);
            }
        }
        List<Long> staffIds = permissionGroupGradeStaffMapper.selectStaffByGroupAndGrade(permissionGroupGradeResourceDto.getFkPermissionGroupId(), permissionGroupGradeResourceDto.getFkPermissionGradeId());
        updateStaffSession(staffIds);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addGroupGradeResources(PermissionGroupGradeResourceDto permissionGroupGradeResourceDto) {
        if (GeneralTool.isEmpty(permissionGroupGradeResourceDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (GeneralTool.isEmpty(permissionGroupGradeResourceDto.getFkPermissionGradeId()) || GeneralTool.isEmpty(permissionGroupGradeResourceDto.getFkPermissionGroupId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        if (GeneralTool.isNotEmpty(permissionGroupGradeResourceDto.getResourceKeys())) {
            for (String keys : permissionGroupGradeResourceDto.getResourceKeys()) {
                PermissionGroupGradeResource permissionGroupGradeResource = new PermissionGroupGradeResource();
                permissionGroupGradeResource.setFkPermissionGradeId(permissionGroupGradeResourceDto.getFkPermissionGroupId());
                permissionGroupGradeResource.setFkPermissionGradeId(permissionGroupGradeResourceDto.getFkPermissionGradeId());
                permissionGroupGradeResource.setFkResourceKey(keys);
//                utilService.setCreateInfo(permissionGroupGradeResource);
                utilService.updateUserInfoToEntity(permissionGroupGradeResource);
                permissionGroupGradeResourceMapper.insert(permissionGroupGradeResource);
            }
        }
    }

    @Override
    public void deleteGroupGradeResources(PermissionGroupGradeResourceDto permissionGroupGradeResourceDto) {
        if (GeneralTool.isEmpty(permissionGroupGradeResourceDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(permissionGroupGradeResourceDto.getFkPermissionGradeId()) || GeneralTool.isEmpty(permissionGroupGradeResourceDto.getFkPermissionGroupId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        Map<String, Object> data = new HashMap<>();
        data.put("groupId", permissionGroupGradeResourceDto.getFkPermissionGroupId());
        data.put("gradeId", permissionGroupGradeResourceDto.getFkPermissionGradeId());
        data.put("resourceKeys", permissionGroupGradeResourceDto.getResourceKeys());
        permissionGroupGradeResourceMapper.deleteByMap(data);

    }

    @Override
    public Boolean getGroupGradeResourcesByResource(String resourceKey) {
        Map<String, Object> data = new HashMap<>();
        data.put("resourceKey", resourceKey);
        List<PermissionGroupGradeResource> permissionGroupGradeResources = permissionGroupGradeResourceMapper.findByMap(data);
        Boolean success = true;
    /*    for(PermissionGroupGradeResource permissionGroupGradeResource:permissionGroupGradeResources){
            List<Long> staffIds = permissionGroupGradeStaffMapper.selectStaffByGroupAndGrade(permissionGroupGradeResource.getFkPermissionGroupId(),permissionGroupGradeResource.getFkPermissionGradeId());
            if(GeneralTool.isNotEmpty(staffIds)){
                success = false;
            }
        }*/
        if (GeneralTool.isNotEmpty(permissionGroupGradeResources)) {
            success = false;
        }
        if (success) {
//            Map<String, Object> datas = new HashMap<>();
//            datas.put("resourceKey", resourceKey);
//            permissionGroupGradeResourceMapper.deleteByMap(datas);
            permissionGroupGradeResourceMapper.delete(Wrappers.<PermissionGroupGradeResource>lambdaQuery().eq(PermissionGroupGradeResource::getFkResourceKey, resourceKey));
        }
        return success;
    }

    @Override
    public ResourceVo updateGroupGradeResourcesByResource(ResourceDto resourceKey) {
//        Map<String, Object> data = new HashMap<>();
//        data.put("resourceKey", resourceKey.getResourceKey());
//        permissionGroupGradeResourceMapper.updateByResourceKey(resourceKey);
//        List<PermissionGroupGradeResource> permissionGroupGradeResources = permissionGroupGradeResourceMapper.findByMap(data);
        this.update(Wrappers.<PermissionGroupGradeResource>query().lambda().eq(PermissionGroupGradeResource::getFkResourceKey, resourceKey.getResourceKey()));
        List<PermissionGroupGradeResource> permissionGroupGradeResources = permissionGroupGradeResourceMapper.selectList(Wrappers.<PermissionGroupGradeResource>query().lambda().eq(PermissionGroupGradeResource::getFkResourceKey, resourceKey.getResourceKey()));
        for (PermissionGroupGradeResource permissionGroupGradeResource : permissionGroupGradeResources) {
            List<Long> staffIds = permissionGroupGradeStaffMapper.selectStaffByGroupAndGrade(permissionGroupGradeResource.getFkPermissionGroupId(), permissionGroupGradeResource.getFkPermissionGradeId());
            updateStaffSession(staffIds);
        }
        ResourceVo resourceVo = resourceService.updateResource(resourceKey);
        return resourceVo;
    }

    @Override
    public Set<String> getStaffApiKeys(Long staffId) {
        List<String> apiKeys = new ArrayList<String>();
        List<String> resourcekeys = permissionGroupGradeResourceMapper.findResourcesByStaffId(staffId);
        List<StaffResource> staffResources = staffResourceMapper.findStaffResourcesByStaffId(staffId);
        if (GeneralTool.isNotEmpty(staffResources)) {
            for (StaffResource staffResource : staffResources) {
                //权限允许访问及尚未包含则添加，否则不允许访问就移除
                if (staffResource.getPermission() == 1 && !resourcekeys.contains(staffResource.getFkResourceKey())) {
                    resourcekeys.add(staffResource.getFkResourceKey());
                } else if (staffResource.getPermission() == 0 && resourcekeys.contains(staffResource.getFkResourceKey())) {
                    resourcekeys.remove(staffResource.getFkResourceKey());
                }
            }
        }

        if (GeneralTool.isNotEmpty(resourcekeys)) {
            System.out.println("==resourcekeys==>" + GeneralTool.toJson(resourcekeys));
            apiKeys = resourceMapper.getApiKeysByResourceKeys(resourcekeys);
            System.out.println("==apiKeys==>" + GeneralTool.toJson(apiKeys));
        }
        return new HashSet<>(apiKeys);
    }

    @Override
    public List<String> getStaffResourceKeys(Long staffId) {
        List<String> resourcekeys = permissionGroupGradeResourceMapper.findResourcesByStaffId(staffId);
        List<StaffResource> staffResources = staffResourceMapper.findStaffResourcesByStaffId(staffId);
        if (GeneralTool.isNotEmpty(staffResources)) {
            for (StaffResource staffResource : staffResources) {
                if (staffResource.getPermission() == 1 && !resourcekeys.contains(staffResource.getFkResourceKey())) {
                    resourcekeys.add(staffResource.getFkResourceKey());
                } else if (staffResource.getPermission() == 0 && resourcekeys.contains(staffResource.getFkResourceKey())) {
                    resourcekeys.remove(staffResource.getFkResourceKey());
                }
            }
        }
        if (GeneralTool.isEmpty(resourcekeys)) {
            resourcekeys.add(null);
        }
        return resourcekeys;
    }

    @Override
    public void updateStaffSession(List<Long> staffIds) {
        for (Long staffId : staffIds) {
            Staff staff = staffMapper.selectById(staffId);
            if (GeneralTool.isNotEmpty(staff)) {
                //获取员工apikey
                Set<String> apikeys = this.getStaffApiKeys(staff.getId());
                if (GeneralTool.isNotEmpty(apikeys)) {
                    SecureUtil.updateApiKeysByStaffId(staff.getId(), apikeys);
                }
                //获取员工资源权限
                List<String> resourcekeys = this.getStaffResourceKeys(staff.getId());
                if (GeneralTool.isNotEmpty(resourcekeys)) {
                    SecureUtil.updateResourceKeysByStaffId(staff.getId(), resourcekeys);
                }
            }
        }
    }

    /**
     * 移动网点资源
     *
     * @param permissionGroupGradeForMovingAndCopyingDto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void movePermissionGroupGrade(PermissionGroupGradeForMovingAndCopyingDto permissionGroupGradeForMovingAndCopyingDto) {
        //验证原网点和目标网点资源是否为空
        PermissionGroupGradeForMovingContext permissionGroupGradeForMovingContext = doValidatePermissionGroupGraderResource(permissionGroupGradeForMovingAndCopyingDto);

        //移动网点信息
        doMovePermissionGroupGradeResource(permissionGroupGradeForMovingContext);
    }

    /**
     * 移动网点资源
     *
     * @param permissionGroupGradeForCopyingVo
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void copyPermissionGroupGrade(PermissionGroupGradeForMovingAndCopyingDto permissionGroupGradeForCopyingVo) {
        //验证原网点和目标网点资源是否为空
        PermissionGroupGradeForMovingContext permissionGroupGradeForMovingContext = doValidatePermissionGroupGraderResource(permissionGroupGradeForCopyingVo);

        //复制网点信息
        doCopyPermissionGroupGradeResource(permissionGroupGradeForMovingContext);

    }

    /**
     * 复制网点信息
     *
     * @param permissionGroupGradeForMovingContext
     */
    private void doCopyPermissionGroupGradeResource(PermissionGroupGradeForMovingContext permissionGroupGradeForMovingContext) {
        List<PermissionGroupGradeResource> sourcePermissionGroupGradeResources = permissionGroupGradeForMovingContext.getSourcePermissionGroupGradeResources();
        Long sourceGroupId = sourcePermissionGroupGradeResources.get(0).getFkPermissionGroupId();
        Long sourceGradeId = sourcePermissionGroupGradeResources.get(0).getFkPermissionGradeId();
        Long targetGroupId = permissionGroupGradeForMovingContext.getTargetGroupId();
        Long targetGradeId = permissionGroupGradeForMovingContext.getTargetGradeId();
        int insertCount = sourcePermissionGroupGradeResources.size();
        for (PermissionGroupGradeResource sourcePermissionGroupGradeResource : sourcePermissionGroupGradeResources) {
            sourcePermissionGroupGradeResource.setId(null);
            sourcePermissionGroupGradeResource.setFkPermissionGroupId(targetGroupId);
            sourcePermissionGroupGradeResource.setFkPermissionGradeId(targetGradeId);
            sourcePermissionGroupGradeResource.setGmtModified(null);
            sourcePermissionGroupGradeResource.setGmtModifiedUser(null);
            utilService.setCreateInfo(sourcePermissionGroupGradeResource);
        }

        while (insertCount>0){
            //批量插入 每次插入DEFAULT_BATCH_SIZE = 1000
            boolean b = saveBatch(sourcePermissionGroupGradeResources, DEFAULT_BATCH_SIZE);
            if (!b){
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
            insertCount = insertCount - DEFAULT_BATCH_SIZE;
        }

        //复制名称
        PermissionGroupGradeName permissionGroupGradeName = permissionGroupGradeNameService.getOne(Wrappers.<PermissionGroupGradeName>lambdaQuery()
                .eq(PermissionGroupGradeName::getFkPermissionGroupId, sourceGroupId)
                .eq(PermissionGroupGradeName::getFkPermissionGradeId, sourceGradeId).last("limit 1"));

        if (GeneralTool.isEmpty(permissionGroupGradeName)
                ||(GeneralTool.isNotEmpty(permissionGroupGradeName)&&GeneralTool.isEmpty(permissionGroupGradeName.getPermissionName()))){
            return ;
        }
        boolean b = permissionGroupGradeNameService.remove((Wrappers.<PermissionGroupGradeName>lambdaQuery()
                .eq(PermissionGroupGradeName::getFkPermissionGroupId, targetGroupId)
                .eq(PermissionGroupGradeName::getFkPermissionGradeId, targetGradeId)));

        PermissionGroupGradeName newPermissionGroupGradeName = new PermissionGroupGradeName();
        newPermissionGroupGradeName.setFkPermissionGroupId(targetGroupId);
        newPermissionGroupGradeName.setFkPermissionGradeId(targetGradeId);
        newPermissionGroupGradeName.setPermissionName(permissionGroupGradeName.getPermissionName());
        utilService.setCreateInfo(newPermissionGroupGradeName);
        boolean save = permissionGroupGradeNameService.save(newPermissionGroupGradeName);
        if (!save) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
    }


    /**
     * 验证原网点和目标网点资源是否为空
     * @param permissionGroupGradeForMovingContext
     */
    private void doMovePermissionGroupGradeResource(PermissionGroupGradeForMovingContext permissionGroupGradeForMovingContext) {
        List<PermissionGroupGradeResource> sourcePermissionGroupGradeResources = permissionGroupGradeForMovingContext.getSourcePermissionGroupGradeResources();
        Long sourceGroupId = sourcePermissionGroupGradeResources.get(0).getFkPermissionGroupId();
        Long sourceGradeId = sourcePermissionGroupGradeResources.get(0).getFkPermissionGradeId();
        Long targetGroupId = permissionGroupGradeForMovingContext.getTargetGroupId();
        Long targetGradeId = permissionGroupGradeForMovingContext.getTargetGradeId();
        int updateCount = sourcePermissionGroupGradeResources.size();
        for (PermissionGroupGradeResource sourcePermissionGroupGradeResource : sourcePermissionGroupGradeResources) {
            sourcePermissionGroupGradeResource.setFkPermissionGroupId(targetGroupId);
            sourcePermissionGroupGradeResource.setFkPermissionGradeId(targetGradeId);
            utilService.setUpdateInfo(sourcePermissionGroupGradeResource);
        }

        while (updateCount>0){
            //批量更新 每次更新DEFAULT_BATCH_SIZE = 1000
            boolean b = updateBatchById(sourcePermissionGroupGradeResources, DEFAULT_BATCH_SIZE);
            if (!b){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
            updateCount = updateCount - DEFAULT_BATCH_SIZE;
        }


        //复制名称
        PermissionGroupGradeName permissionGroupGradeName = permissionGroupGradeNameService.getOne(Wrappers.<PermissionGroupGradeName>lambdaQuery()
                .eq(PermissionGroupGradeName::getFkPermissionGroupId, sourceGroupId)
                .eq(PermissionGroupGradeName::getFkPermissionGradeId, sourceGradeId).last("limit 1"));

        if (GeneralTool.isEmpty(permissionGroupGradeName)
                ||(GeneralTool.isNotEmpty(permissionGroupGradeName)&&GeneralTool.isEmpty(permissionGroupGradeName.getPermissionName()))){
            return ;
        }
        boolean b = permissionGroupGradeNameService.remove((Wrappers.<PermissionGroupGradeName>lambdaQuery()
                .eq(PermissionGroupGradeName::getFkPermissionGroupId, targetGroupId)
                .eq(PermissionGroupGradeName::getFkPermissionGradeId, targetGradeId)));

        boolean b1 = permissionGroupGradeNameService.remove((Wrappers.<PermissionGroupGradeName>lambdaQuery()
                .eq(PermissionGroupGradeName::getFkPermissionGroupId, sourceGroupId)
                .eq(PermissionGroupGradeName::getFkPermissionGradeId, sourceGradeId)));

        PermissionGroupGradeName newPermissionGroupGradeName = new PermissionGroupGradeName();
        newPermissionGroupGradeName.setFkPermissionGroupId(targetGroupId);
        newPermissionGroupGradeName.setFkPermissionGradeId(targetGradeId);
        newPermissionGroupGradeName.setPermissionName(permissionGroupGradeName.getPermissionName());
        utilService.setCreateInfo(newPermissionGroupGradeName);
        boolean save = permissionGroupGradeNameService.save(newPermissionGroupGradeName);
        if (!save) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
    }


    /**
     * 移动网点信息
     *
      * @param permissionGroupGradeForMovingAndCopyingDto
     * @return
     */
    private PermissionGroupGradeForMovingContext doValidatePermissionGroupGraderResource(PermissionGroupGradeForMovingAndCopyingDto permissionGroupGradeForMovingAndCopyingDto) {
        //原网点
        List<PermissionGroupGradeResource> sourcePermissionGroupGradeResources = permissionGroupGradeResourceMapper.selectList(Wrappers.<PermissionGroupGradeResource>lambdaQuery()
                .eq(PermissionGroupGradeResource::getFkPermissionGroupId, permissionGroupGradeForMovingAndCopyingDto.getSourceGroupId())
                .eq(PermissionGroupGradeResource::getFkPermissionGradeId, permissionGroupGradeForMovingAndCopyingDto.getSourceGradeId()));

        if(GeneralTool.isEmpty(sourcePermissionGroupGradeResources)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("source_permission_group_grade_null"));

        }

        //目标网点
        List<PermissionGroupGradeResource> targetPermissionGroupGradeResources = permissionGroupGradeResourceMapper.selectList(Wrappers.<PermissionGroupGradeResource>lambdaQuery()
                .eq(PermissionGroupGradeResource::getFkPermissionGroupId, permissionGroupGradeForMovingAndCopyingDto.getTargetGroupId())
                .eq(PermissionGroupGradeResource::getFkPermissionGradeId, permissionGroupGradeForMovingAndCopyingDto.getTargetGradeId()).last("limit 1"));

        if(GeneralTool.isNotEmpty(targetPermissionGroupGradeResources)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("target_permission_group_grade_null"));
        }

        return new PermissionGroupGradeForMovingContext(permissionGroupGradeForMovingAndCopyingDto.getTargetGroupId(), permissionGroupGradeForMovingAndCopyingDto.getTargetGradeId(), sourcePermissionGroupGradeResources);
    }


    private void setIsSelected(List<ResourceVo> resourceVos, List<String> convertDatas) {
        for (ResourceVo resourceVo : resourceVos) {
            if (convertDatas.contains(resourceVo.getResourceKey())) {
                resourceVo.setIsSelected(true);
            }
            if (GeneralTool.isNotEmpty(resourceVo.getResourceDtos())) {
                setIsSelected(resourceVo.getResourceDtos(), convertDatas);
            }
        }
    }

}
