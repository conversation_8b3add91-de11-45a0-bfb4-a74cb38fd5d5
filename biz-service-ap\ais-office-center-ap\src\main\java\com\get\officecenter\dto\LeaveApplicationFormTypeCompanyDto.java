package com.get.officecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: Hardy
 * @create: 2023/3/29 14:58
 * @verison: 1.0
 * @description:
 */
@Data
public class LeaveApplicationFormTypeCompanyDto extends BaseVoEntity {

    /**
     * 工休申请单类型Id
     */
    @NotNull(message = "工休申请单类型Id", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "工休申请单类型Id")
    private Long fkLeaveApplicationFormTypeId;

    /**
     * 公司Id
     */
    @NotNull(message = "公司Id", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

}
