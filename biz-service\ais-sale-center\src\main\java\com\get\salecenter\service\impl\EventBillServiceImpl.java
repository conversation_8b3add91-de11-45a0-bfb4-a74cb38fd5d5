package com.get.salecenter.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.ErrorCodeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.StaffInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestContextUtil;
import com.get.file.utils.FileUtils;
import com.get.financecenter.dto.InvoiceDto;
import com.get.financecenter.dto.InvoiceReceivablePlanDto;
import com.get.financecenter.entity.Invoice;
import com.get.financecenter.entity.ReceiptFormItem;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.financecenter.vo.InvoiceVo;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.DepartmentAndStaffVo;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.salecenter.dao.sale.*;
import com.get.salecenter.dto.*;
import com.get.salecenter.entity.*;
import com.get.salecenter.service.*;
import com.get.salecenter.strategy.config.CompanyConfigStrategyConfig;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.utils.sale.SaleCenterConfigUtils;
import com.get.salecenter.vo.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2022/5/9 11:50
 * @verison: 1.0
 * @description:
 */
@Service
public class EventBillServiceImpl extends ServiceImpl<EventBillMapper, EventBill> implements IEventBillService {

    @Resource
    private EventBillMapper eventBillMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private UtilService utilService;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private ReceivablePlanMapper receivablePlanMapper;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private IEventBillAreaCountryService eventBillAreaCountryService;
    @Resource
    private IEventBillStaffNoticeService eventBillStaffNoticeService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private EventBillAreaCountryMapper eventBillAreaCountryMapper;
    @Resource
    private EventBillStaffNoticeMapper eventBillStaffNoticeMapper;
    @Resource
    private ICommentService commentService;
    @Resource
    private IReminderCenterClient reminderCenterClient;
    @Resource
    private IEventBillEventSummaryService eventBillEventSummaryService;
    @Resource
    private EventSummaryMapper eventSummaryMapper;
    @Resource
    private EventBillEventSummaryMapper eventBillEventSummaryMapper;
    @Resource
    private EventCostMapper eventCostMapper;
    @Resource
    private AsyncReminderService asyncReminderService;
    @Lazy
    @Resource
    private IEventIncentiveCostService eventIncentiveCostService;
    @Resource
    private CompanyConfigStrategyConfig companyConfigStrategyConfig;
    @Resource
    private SaleCenterConfigUtils saleCenterConfigUtils;
    @Resource
    private IEventCostService eventCostService;
    @Resource
    private EventBillReceivablePlanMapper eventBillReceivablePlanMapper;
    @Resource
    private IReceivablePlanService receivablePlanService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addEventBill(EventBillUpdateDto eventBillVo) {
        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        if (GeneralTool.isEmpty(eventBillVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        EventBill eventBill = new EventBill();
        BeanCopyUtils.copyProperties(eventBillVo, eventBill);
        utilService.setCreateInfo(eventBill);
        eventBillMapper.insert(eventBill);

        if (GeneralTool.isNotEmpty(eventBillVo.getEventCostVos())) {
            for (EventCostDto eventCostDto : eventBillVo.getEventCostVos()) {
                eventCostDto.setFkEventBillId(eventBill.getId());
            }
            eventCostService.batchAdd(eventBillVo.getEventCostVos());
        }

        //绑定业务国家
        if (GeneralTool.isNotEmpty(eventBillVo.getFkAreaCountryIdList())) {
            List<EventBillAreaCountry> eventBillAreaCountries = new ArrayList<>();
            for (Long fkAreaCountryId : eventBillVo.getFkAreaCountryIdList()) {
                EventBillAreaCountry eventBillAreaCountry = new EventBillAreaCountry();
                eventBillAreaCountry.setFkEventBillId(eventBill.getId());
                eventBillAreaCountry.setFkAreaCountryId(fkAreaCountryId);
                utilService.updateUserInfoToEntity(eventBillAreaCountry);
                eventBillAreaCountries.add(eventBillAreaCountry);
            }
            eventBillAreaCountryService.batchAddByIds(eventBillAreaCountries);
        }

        //绑定摘要关系表
        if (GeneralTool.isNotEmpty(eventBillVo.getFkEventSummaryIdList())) {
            List<EventBillEventSummary> eventBillEventSummaries = new ArrayList<>();
            for (Long fkEventSummaryId : eventBillVo.getFkEventSummaryIdList()) {
                EventBillEventSummary eventBillEventSummary = new EventBillEventSummary();
                eventBillEventSummary.setFkEventBillId(eventBill.getId());
                eventBillEventSummary.setFkEventSummaryId(fkEventSummaryId);
                utilService.setCreateInfo(eventBillEventSummary);
                eventBillEventSummaries.add(eventBillEventSummary);
            }
            eventBillEventSummaryService.batchAddByIds(eventBillEventSummaries);
        }


//        String summary =  eventBillVo.getInvoiceSummary();

        EventBillReminderDto eventBillReminderDto = new EventBillReminderDto();
        BeanCopyUtils.copyProperties(eventBillVo, eventBillReminderDto);
        eventBillReminderDto.setFkEventBillId(eventBill.getId());

        //绑定通知人
        if (GeneralTool.isNotEmpty(eventBillVo.getDepartmentAndStaffDtos())) {
            Set<Long> staffIds = eventBillVo.getDepartmentAndStaffDtos().stream().filter(d -> GeneralTool.isNotEmpty(d.getFkStaffId()))
                    .map(DepartmentAndStaffVo::getFkStaffId).collect(Collectors.toSet());

            if (GeneralTool.isNotEmpty(staffIds)) {
                List<EventBillStaffNotice> eventBillStaffNotices = new ArrayList<>();
                for (Long fkStaffIdNotice : staffIds) {
                    EventBillStaffNotice eventBillStaffNotice = new EventBillStaffNotice();
                    eventBillStaffNotice.setFkEventBillId(eventBill.getId());
                    eventBillStaffNotice.setFkStaffIdNotice(fkStaffIdNotice);
                    utilService.updateUserInfoToEntity(eventBillStaffNotice);
                    eventBillStaffNotices.add(eventBillStaffNotice);
                }
                eventBillStaffNoticeService.batchAddByIds(eventBillStaffNotices);
                eventBillReminderDto.setFkStaffIds(staffIds);
            }

            asyncReminderService.sendEventBillEmail(headerMap, eventBillReminderDto, "【已发起】", staffInfo);

            //异步发送提醒
//            asyncReminderService.doAddReminders(headerMap,eventBillReminderDto,"【已发起】");


        }
        return eventBill.getId();
    }


    @Override
    public EventBillVo findEventBillById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        EventBill eventBill = eventBillMapper.selectById(id);
        EventBillVo eventBillVo = new EventBillVo();
        BeanCopyUtils.copyProperties(eventBill, eventBillVo);

        List<EventBillReceivablePlan> eventBillReceivablePlans = eventBillReceivablePlanMapper.selectList(Wrappers.lambdaQuery(EventBillReceivablePlan.class)
                .eq(EventBillReceivablePlan::getFkEventBillId, id)
        );

//        if (GeneralTool.isEmpty(eventBill.getFkReceivablePlanId())){
//            eventBillVo.setEnableUpdate(false);
//        }else{
//            eventBillVo.setEnableUpdate(true);
//        }
        if (GeneralTool.isEmpty(eventBillReceivablePlans)) {
            eventBillVo.setEnableUpdate(false);
        } else {
            eventBillVo.setEnableUpdate(true);
        }

        //业务国家
        List<EventBillAreaCountry> eventBillAreaCountries = eventBillAreaCountryMapper.selectList(Wrappers.<EventBillAreaCountry>lambdaQuery().eq(EventBillAreaCountry::getFkEventBillId, id));
        if (GeneralTool.isNotEmpty(eventBillAreaCountries)) {
            List<Long> countryList = eventBillAreaCountries.stream().filter(e -> GeneralTool.isNotEmpty(e.getFkAreaCountryId())).distinct().map(EventBillAreaCountry::getFkAreaCountryId).collect(Collectors.toList());
            eventBillVo.setFkAreaCountryIdList(countryList);
            if (GeneralTool.isNotEmpty(countryList)) {
                Result<Map<Long, String>> countryNamesByIdsResult = institutionCenterClient.getCountryFullNamesByIds(new HashSet<>(countryList));
                if (countryNamesByIdsResult.isSuccess() && GeneralTool.isNotEmpty(countryNamesByIdsResult.getData())) {
                    Map<Long, String> data = countryNamesByIdsResult.getData();
                    StringJoiner sj = new StringJoiner(",");
                    for (Long aLong : countryList) {
                        if (GeneralTool.isNotEmpty(data.get(aLong))) {
                            sj.add(data.get(aLong));
                        }
                    }
                    eventBillVo.setFkAreaCountryName(sj.toString());
                }
            }
        }

        //通知人
        List<EventBillStaffNotice> eventBillStaffNotices = eventBillStaffNoticeMapper.selectList(Wrappers.<EventBillStaffNotice>lambdaQuery().eq(EventBillStaffNotice::getFkEventBillId, id));
        if (GeneralTool.isNotEmpty(eventBillStaffNotices)) {
            Set<Long> staffIds = eventBillStaffNotices.stream().filter(e -> GeneralTool.isNotEmpty(e.getFkStaffIdNotice())).map(EventBillStaffNotice::getFkStaffIdNotice).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(staffIds)) {
                Result<List<DepartmentAndStaffVo>> result = permissionCenterClient.getDepartmentAndStaffDtoByStaffIds(staffIds);
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    eventBillVo.setDepartmentAndStaffDtos(result.getData());
                }
            }
        }

        //摘要
        List<EventBillEventSummary> eventBillEventSummaries = eventBillEventSummaryMapper.selectList(Wrappers.<EventBillEventSummary>lambdaQuery().eq(EventBillEventSummary::getFkEventBillId, id));
        if (GeneralTool.isNotEmpty(eventBillEventSummaries)) {
            List<Long> eventBillEventSummaryIds = eventBillEventSummaries.stream().filter(e -> GeneralTool.isNotEmpty(e.getFkEventSummaryId())).map(EventBillEventSummary::getFkEventSummaryId).collect(Collectors.toList());
            if (GeneralTool.isNotEmpty(eventBillEventSummaryIds)) {
                List<EventSummary> eventSummaries = eventSummaryMapper.selectList(Wrappers.<EventSummary>lambdaQuery().
                        in(EventSummary::getId, eventBillEventSummaryIds));

                if (GeneralTool.isNotEmpty(eventSummaries)) {
                    StringJoiner sj = new StringJoiner(",");
                    eventSummaries.forEach(eventSummary -> sj.add(eventSummary.getEventSummary()));
                    List<Long> eventSummaryIds = eventSummaries.stream().map(EventSummary::getId).collect(Collectors.toList());
                    eventBillVo.setFkEventSummaryName(sj.toString());
                    eventBillVo.setFkEventSummaryIdList(eventSummaryIds);
                }
            }
        }


        if (GeneralTool.isNotEmpty(eventBill.getFkCurrencyTypeNumInvoice())) {
            Result<String> currencyNameByNum = financeCenterClient.getCurrencyNameByNum(eventBill.getFkCurrencyTypeNumInvoice());
            String data = currencyNameByNum.getData();
            eventBillVo.setFkCurrencyTypeNumInvoiceName(data);
            if (GeneralTool.isNotEmpty(eventBill.getInvoiceAmount())) {
                String invoiceAmountCurrency = eventBill.getInvoiceAmount().toString();
                if (currencyNameByNum.isSuccess() && GeneralTool.isNotEmpty(currencyNameByNum.getData())) {
                    invoiceAmountCurrency = invoiceAmountCurrency + currencyNameByNum.getData();
//                    eventBillVo.setFkCurrencyTypeNumInvoiceName(data);
                    eventBillVo.setInvoiceAmountCurrency(invoiceAmountCurrency);
                }
            }
        }


        if (GeneralTool.isNotEmpty(eventBill.getEventAmount()) && GeneralTool.isNotEmpty(eventBill.getFkCurrencyTypeNumEvent())) {
            String eventAmountCurrency = eventBill.getEventAmount().toString();
            Result<String> eventCurrencyNameByNum = financeCenterClient.getCurrencyNameByNum(eventBill.getFkCurrencyTypeNumEvent());
            if (eventCurrencyNameByNum.isSuccess() && GeneralTool.isNotEmpty(eventCurrencyNameByNum.getData())) {
                String data = eventCurrencyNameByNum.getData();
                eventAmountCurrency = eventAmountCurrency + eventCurrencyNameByNum.getData();
                eventBillVo.setFkCurrencyTypeNumEventName(data);
                eventBillVo.setEventAmountCurrency(eventAmountCurrency);
            }
        }


        String institutionProviderName = "";
        Result<String> institutionProviderNameResult = institutionCenterClient.getInstitutionProviderName(eventBill.getFkInstitutionProviderId());
        if (institutionProviderNameResult.isSuccess() && GeneralTool.isNotEmpty(institutionProviderNameResult.getData())) {
            institutionProviderName = institutionProviderName + institutionProviderNameResult.getData();
            eventBillVo.setFkInstitutionProviderName(institutionProviderName);
        }

//        Result<String> currencyNameByNumResult = financeCenterClient.getCurrencyNameByNum(eventBill.getFkCurrencyTypeNumInvoice());
//        if (currencyNameByNumResult.isSuccess() && GeneralTool.isNotEmpty(currencyNameByNumResult.getData())) {
//            String data = currencyNameByNumResult.getData();
//            eventBillVo.setFkCurrencyTypeNumInvoiceName(data);
//        }

        List<EventCost> eventCosts = eventCostMapper.selectList(Wrappers.<EventCost>lambdaQuery().eq(EventCost::getFkEventBillId, id));
        List<EventIncentiveCost> eventIncentiveCosts = eventIncentiveCostService.list(Wrappers.<EventIncentiveCost>lambdaQuery().eq(EventIncentiveCost::getFkEventBillId, id));

        BigDecimal amountReceivableEvent = BigDecimal.ZERO;
        if (GeneralTool.isNotEmpty(eventCosts)) {
            for (EventCost eventCost : eventCosts) {
                if (GeneralTool.isEmpty(eventCost.getAmountReceivable())) {
                    eventCost.setAmountReceivable(BigDecimal.ZERO);
                }
                amountReceivableEvent = amountReceivableEvent.add(eventCost.getAmountReceivable());
            }

        }
        if (GeneralTool.isNotEmpty(eventIncentiveCosts)) {
            for (EventIncentiveCost eventIncentiveCost : eventIncentiveCosts) {
                if (GeneralTool.isEmpty(eventIncentiveCost.getAmountReceivable())) {
                    eventIncentiveCost.setAmountReceivable(BigDecimal.ZERO);
                }
                amountReceivableEvent = amountReceivableEvent.add(eventIncentiveCost.getAmountReceivable());
            }
        }
        eventBillVo.setAmountReceiptEvent(amountReceivableEvent.setScale(2, BigDecimal.ROUND_HALF_UP));
        eventBillVo.setDiffAmountEvent(eventBillVo.getEventAmount().subtract(amountReceivableEvent).setScale(4, BigDecimal.ROUND_HALF_UP));

        String companyName = permissionCenterClient.getCompanyNameById(eventBill.getFkCompanyId()).getData();
        eventBillVo.setFkCompanyName(companyName);

        return eventBillVo;
    }


    @Override
    public List<MediaAndAttachedVo> getItemMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.SALE_EVENT_BILL.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);
    }

    @Override
    public List<MediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.SALE_EVENT_BILL.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }


    @Override
    public List<EventBillListVo> getEventBills(EventBillListDto eventBillListDto, Page page) {
        if (GeneralTool.isEmpty(eventBillListDto)) {
            if (GeneralTool.isEmpty(eventBillListDto.getFkCompanyId()) && GeneralTool.isEmpty(eventBillListDto.getFkCompanyIdList())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
            }
        }

        List<Long> countryIds = SecureUtil.getCountryIds();
        if (GeneralTool.isEmpty(countryIds)) {
            countryIds.add(0L);
        }
        //获取业务下属
        Long staffId = SecureUtil.getStaffId();
        //员工id + 业务下属员工ids
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData();
        staffFollowerIds.add(staffId);

        List<EventBillListVo> eventBillListVos = new ArrayList<>();
        if (page == null){
            eventBillListVos = eventBillMapper.getEventBills(null, eventBillListDto, countryIds, SecureUtil.getStaffId(), SecureUtil.getStaffInfo().getLoginId(),staffFollowerIds);
        } else {
            long startTime1 = System.nanoTime();
            //TODO: 2024/4/11  活动费用小计(增加登录用户是否是创建用户)
            //计算总条数是否大于该页码,如果大于则将当前页码置为1
            if(GeneralTool.isNotEmpty(page.getTotalResult())){
                int totalPages = (int) Math.ceil((double) page.getTotalResult() / page.getShowCount());
                if (page.getCurrentPage().intValue() > totalPages) {
                    page.setCurrentPage(1);
                }
            }

            IPage<EventBillListVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            eventBillListVos = eventBillMapper.getEventBills(iPage, eventBillListDto, countryIds, SecureUtil.getStaffId(), SecureUtil.getStaffInfo().getLoginId(),staffFollowerIds);
            long endTime1 = System.nanoTime();
            double durationMs1 = (endTime1 - startTime1) / 1_000_000.0;
            System.out.printf("getEventBills 运行时间: %.3f 毫秒", durationMs1);
            page.setAll((int) iPage.getTotal());
        }



        if (GeneralTool.isEmpty(eventBillListVos)) {
            return Collections.emptyList();
        }
        Set<String> numSet = eventBillListVos.stream().map(EventBillListVo::getFkInvoiceNum)
                .filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
        long startTime2 = System.nanoTime();
        List<InvoiceVo> invoiceVos = financeCenterClient.getInvoiceByNumList(numSet).getData();

        long endTime2 = System.nanoTime();
        double durationMs2 = (endTime2 - startTime2) / 1_000_000.0;
        System.out.printf("getInvoiceByNumList 运行时间: %.3f 毫秒", durationMs2);

        Map<String, Date> createTimeMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(invoiceVos)) {
            createTimeMap = invoiceVos.stream().filter(i -> GeneralTool.isNotEmpty(i.getNum()))
                    .collect(HashMap::new, (m, v) -> m.put(v.getNum(), v.getGmtCreate()), HashMap::putAll);
        }


        long startTime3 = System.nanoTime();
        List<Long> eventBillIds = eventBillListVos.stream().map(EventBillListVo::getId).collect(Collectors.toList());
        Map<Long, List<EventBillListVo>> receivableInfoMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(eventBillIds)) {
            List<EventBillListVo> receivableInfos = eventBillMapper.getReceivableInfo(eventBillIds);
            receivableInfoMap = receivableInfos.stream().collect(Collectors.groupingBy(EventBillListVo::getId));
        }
        long endTime3 = System.nanoTime();
        double durationMs3 = (endTime3 - startTime3) / 1_000_000.0;
        System.out.printf("getReceivableInfo 运行时间: %.3f 毫秒", durationMs3);

//        //是否实收map
//        Map<Long, BigDecimal> actualReceivableAmountMap = Maps.newHashMap();
//        List<Long> planIds = eventBillListVos.stream().map(EventBillListVo::getFkReceivablePlanId).filter(Objects::nonNull).collect(Collectors.toList());
//        if (GeneralTool.isNotEmpty(planIds)){
//            List<ReceivablePlanVo> receivablePlans = receivablePlanMapper.getReceivablePlanByTargetsAndType(TableEnum.INSTITUTION_PROVIDER.key, planIds);
//            if (GeneralTool.isNotEmpty(receivablePlans)){
//                actualReceivableAmountMap = receivablePlans.stream().collect(Collectors.toMap(ReceivablePlanVo::getId, ReceivablePlanVo::getActualReceivableAmount));
//            }
//        }
        long startTime4 = System.nanoTime();
        //实收金额map
        for (EventBillListVo eventBillListVo : eventBillListVos) {
            String statusName = Objects.equals(eventBillListVo.getStatus(), 1) ? LocaleMessageUtils.getMessage("status_effective") : LocaleMessageUtils.getMessage("status_invalid");
            eventBillListVo.setStatusName(statusName);

            if (GeneralTool.isNotEmpty(eventBillListVo.getFkEventSummaryIds())) {
                List<Long> fkEventSummaryIds = Arrays.stream(eventBillListVo.getFkEventSummaryIds().replace(" ","").split(",")).filter(Objects::nonNull).map(Long::valueOf).filter(Objects::nonNull).collect(Collectors.toList());
                if (GeneralTool.isNotEmpty(fkEventSummaryIds)) {
                    eventBillListVo.setFkEventSummaryIdList(fkEventSummaryIds);
                }
            }

            if (GeneralTool.isNotEmpty(createTimeMap) && GeneralTool.isNotEmpty(eventBillListVo.getFkInvoiceNum())
                    && GeneralTool.isNotEmpty(createTimeMap.get(eventBillListVo.getFkInvoiceNum()))) {
                eventBillListVo.setInvoiceCreateTime(createTimeMap.get(eventBillListVo.getFkInvoiceNum()));
            }

            eventBillListVo.setAllocationStatusName(ProjectExtraEnum.getValueByKey(eventBillListVo.getAllocationStatus(), ProjectExtraEnum.EB_STATUS));

//            if (GeneralTool.isNotEmpty(eventBillListVo.getFkReceivablePlanId())){
//                BigDecimal actualReceivableAmount = actualReceivableAmountMap.get(eventBillListVo.getFkReceivablePlanId());
//                //无实收
//                if ((GeneralTool.isNotEmpty(actualReceivableAmount)&&actualReceivableAmount.compareTo(BigDecimal.ZERO) == 0)
//                        ||(GeneralTool.isEmpty(actualReceivableAmount))){
//                    eventBillListVo.setReinitiateStatus(true);
//                }
//            }
            //获取实收金额
            List<EventBillListVo> receivableInfos = receivableInfoMap.get(eventBillListVo.getId());
            if (GeneralTool.isNotEmpty(receivableInfos)) {
                eventBillListVo.setReceivableAmountCurrency(receivableInfos.stream().map(EventBillListVo::getReceivableAmountCurrency).filter(GeneralTool::isNotEmpty).map(amount ->new BigDecimal(amount).setScale(2, RoundingMode.HALF_UP).toString()).collect(Collectors.joining(",")));
                eventBillListVo.setActualReceivableAmountCurrency(receivableInfos.stream().map(EventBillListVo::getActualReceivableAmountCurrency).filter(GeneralTool::isNotEmpty).map(amount ->new BigDecimal(amount).setScale(2, RoundingMode.HALF_UP).toString()).collect(Collectors.joining(",")));
                eventBillListVo.setDifferenceAmountCurrency(receivableInfos.stream().map(EventBillListVo::getDifferenceAmountCurrency).filter(GeneralTool::isNotEmpty).map(amount ->new BigDecimal(amount).setScale(2, RoundingMode.HALF_UP).toString()).collect(Collectors.joining(",")));
                eventBillListVo.setActualReceivableAmount(receivableInfos.stream().map(EventBillListVo::getActualReceivableAmount).filter(GeneralTool::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP) );
                eventBillListVo.setReceivableCurrencyTypeNum(eventBillListVo.getEventCurrencyTypeNum());
            } else {
                eventBillListVo.setActualReceivableAmount(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                eventBillListVo.setReceivableCurrencyTypeNum("");
            }

            if (GeneralTool.isNotEmpty(eventBillListVo.getDifferenceEventAmountCurrency())){
                eventBillListVo.setDifferenceEventAmountCurrency(new BigDecimal(eventBillListVo.getDifferenceEventAmountCurrency()).setScale(2, RoundingMode.HALF_UP).toString());
            }

            eventBillListVo.setReinitiateStatus(eventBillListVo.getStatus() == 1 && eventBillListVo.getActualReceivableAmount().compareTo(BigDecimal.ZERO) == 0 && GeneralTool.isNotEmpty(eventBillListVo.getActualReceivableAmountCurrency()));
        }

        long endTime4 = System.nanoTime();
        double durationMs4 = (endTime4 - startTime4) / 1_000_000.0;
        System.out.printf("for循环 运行时间: %.3f 毫秒", durationMs4);

        return eventBillListVos;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public EventBillVo updateEventBill(EventBillUpdateDto eventBillUpdateDto) {
        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        if (GeneralTool.isEmpty(eventBillUpdateDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }

        EventBill eventBill = eventBillMapper.selectById(eventBillUpdateDto.getId());
        if (GeneralTool.isEmpty(eventBill)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }

        Boolean flag = false;
        List<EventBillReceivablePlan> eventBillReceivablePlans = eventBillReceivablePlanMapper.selectList(Wrappers.lambdaQuery(EventBillReceivablePlan.class)
                .eq(EventBillReceivablePlan::getFkEventBillId, eventBillUpdateDto.getId())
        );
//        if (GeneralTool.isNotEmpty(eventBill.getFkReceivablePlanId())){
//            //不为空说明绑定了
//            flag = true;
//        }

        if (GeneralTool.isNotEmpty(eventBillReceivablePlans)) {
            //不为空说明绑定了
            flag = true;
        }

        if (GeneralTool.isNotEmpty(eventBill.getFkInvoiceNum())) {
            //不为空说明绑定了
            flag = true;
        }

        List<Long> areaCountryIds = Lists.newArrayList();
        List<EventBillAreaCountry> areaCountries = eventBillAreaCountryMapper.selectList(Wrappers.<EventBillAreaCountry>lambdaQuery().eq(EventBillAreaCountry::getFkEventBillId, eventBillUpdateDto.getId()));
        if (GeneralTool.isNotEmpty(areaCountries)) {
            areaCountryIds = areaCountries.stream().map(EventBillAreaCountry::getFkAreaCountryId).collect(Collectors.toList());
        }

        List<Long> summaryIds = Lists.newArrayList();
        List<EventBillEventSummary> summaries = eventBillEventSummaryMapper.selectList(Wrappers.<EventBillEventSummary>lambdaQuery().eq(EventBillEventSummary::getFkEventBillId, eventBillUpdateDto.getId()));
        if (GeneralTool.isNotEmpty(summaries)) {
            summaryIds = summaries.stream().map(EventBillEventSummary::getFkEventSummaryId).collect(Collectors.toList());
        }

        if (flag) {
            //已经绑定  无法编辑
            eventBillUpdateDto.setFkInstitutionProviderId(eventBill.getFkInstitutionProviderId());
            eventBillUpdateDto.setInvoiceAmount(eventBill.getInvoiceAmount());
            eventBillUpdateDto.setFkCurrencyTypeNumInvoice(eventBill.getFkCurrencyTypeNumInvoice());
            eventBillUpdateDto.setInvoiceSummary(eventBill.getInvoiceSummary());

            eventBillAreaCountryMapper.delete(Wrappers.<EventBillAreaCountry>lambdaQuery().eq(EventBillAreaCountry::getFkEventBillId, eventBillUpdateDto.getId()));

            eventBillStaffNoticeMapper.delete(Wrappers.<EventBillStaffNotice>lambdaQuery().eq(EventBillStaffNotice::getFkEventBillId, eventBillUpdateDto.getId()));

            eventBillEventSummaryMapper.delete(Wrappers.<EventBillEventSummary>lambdaQuery().eq(EventBillEventSummary::getFkEventBillId, eventBillUpdateDto.getId()));

            //绑定业务国家
            if (GeneralTool.isNotEmpty(eventBillUpdateDto.getFkAreaCountryIdList())) {
                List<EventBillAreaCountry> eventBillAreaCountries = new ArrayList<>();
                for (Long fkAreaCountryId : eventBillUpdateDto.getFkAreaCountryIdList()) {
                    EventBillAreaCountry eventBillAreaCountry = new EventBillAreaCountry();
                    eventBillAreaCountry.setFkEventBillId(eventBill.getId());
                    eventBillAreaCountry.setFkAreaCountryId(fkAreaCountryId);
                    utilService.updateUserInfoToEntity(eventBillAreaCountry);
                    eventBillAreaCountries.add(eventBillAreaCountry);
                }
                eventBillAreaCountryService.batchAddByIds(eventBillAreaCountries);
            }

            //绑定摘要
            if (GeneralTool.isNotEmpty(eventBillUpdateDto.getFkEventSummaryIdList())) {
                List<EventBillEventSummary> eventBillEventSummaries = new ArrayList<>();
                for (Long fkEventSummaryId : eventBillUpdateDto.getFkEventSummaryIdList()) {
                    EventBillEventSummary eventBillEventSummary = new EventBillEventSummary();
                    eventBillEventSummary.setFkEventBillId(eventBill.getId());
                    eventBillEventSummary.setFkEventSummaryId(fkEventSummaryId);
                    utilService.setCreateInfo(eventBillEventSummary);
                    eventBillEventSummaries.add(eventBillEventSummary);
                }
                eventBillEventSummaryService.batchAddByIds(eventBillEventSummaries);
            }

            EventBillReminderDto eventBillReminderDto = new EventBillReminderDto();
            BeanCopyUtils.copyProperties(eventBillUpdateDto, eventBillReminderDto);
            eventBillReminderDto.setFkEventBillId(eventBill.getId());

            //设置修改过的高亮属性
            setHighLineProperties(eventBillUpdateDto, eventBill, areaCountryIds, summaryIds, eventBillReminderDto);

            BeanCopyUtils.copyProperties(eventBillUpdateDto, eventBill);
            utilService.setUpdateInfo(eventBill);
            eventBillMapper.updateByIdWithNull(eventBill);

            //绑定通知人
            if (GeneralTool.isNotEmpty(eventBillUpdateDto.getDepartmentAndStaffDtos())) {
                Set<Long> staffIds = eventBillUpdateDto.getDepartmentAndStaffDtos().stream().filter(d -> GeneralTool.isNotEmpty(d.getFkStaffId()))
                        .map(DepartmentAndStaffVo::getFkStaffId).collect(Collectors.toSet());

                if (GeneralTool.isNotEmpty(staffIds)) {
                    List<EventBillStaffNotice> eventBillStaffNotices = new ArrayList<>();
                    for (Long fkStaffIdNotice : staffIds) {
                        EventBillStaffNotice eventBillStaffNotice = new EventBillStaffNotice();
                        eventBillStaffNotice.setFkEventBillId(eventBill.getId());
                        eventBillStaffNotice.setFkStaffIdNotice(fkStaffIdNotice);
                        utilService.updateUserInfoToEntity(eventBillStaffNotice);
                        eventBillStaffNotices.add(eventBillStaffNotice);
                    }
                    eventBillStaffNoticeService.batchAddByIds(eventBillStaffNotices);
                    eventBillReminderDto.setFkStaffIds(staffIds);
                }
                //异步发送提醒
//                    asyncReminderService.doAddReminders(headerMap,eventBillReminderDto,"【已更新】");
                //TODO 邮件模板
                asyncReminderService.sendEventBillEmail(headerMap, eventBillReminderDto, "【已更新】", staffInfo);

            }

        } else {


            //更新业务国家和通知人-》先删除关系表  摘要-》删除关系表
            eventBillAreaCountryMapper.delete(Wrappers.<EventBillAreaCountry>lambdaQuery().eq(EventBillAreaCountry::getFkEventBillId, eventBillUpdateDto.getId()));

            eventBillStaffNoticeMapper.delete(Wrappers.<EventBillStaffNotice>lambdaQuery().eq(EventBillStaffNotice::getFkEventBillId, eventBillUpdateDto.getId()));

            eventBillEventSummaryMapper.delete(Wrappers.<EventBillEventSummary>lambdaQuery().eq(EventBillEventSummary::getFkEventBillId, eventBillUpdateDto.getId()));

            //绑定业务国家
            if (GeneralTool.isNotEmpty(eventBillUpdateDto.getFkAreaCountryIdList())) {
                List<EventBillAreaCountry> eventBillAreaCountries = new ArrayList<>();
                for (Long fkAreaCountryId : eventBillUpdateDto.getFkAreaCountryIdList()) {
                    EventBillAreaCountry eventBillAreaCountry = new EventBillAreaCountry();
                    eventBillAreaCountry.setFkEventBillId(eventBill.getId());
                    eventBillAreaCountry.setFkAreaCountryId(fkAreaCountryId);
                    utilService.updateUserInfoToEntity(eventBillAreaCountry);
                    eventBillAreaCountries.add(eventBillAreaCountry);
                }
                eventBillAreaCountryService.batchAddByIds(eventBillAreaCountries);
            }

            //绑定摘要
            if (GeneralTool.isNotEmpty(eventBillUpdateDto.getFkEventSummaryIdList())) {
                List<EventBillEventSummary> eventBillEventSummaries = new ArrayList<>();
                for (Long fkEventSummaryId : eventBillUpdateDto.getFkEventSummaryIdList()) {
                    EventBillEventSummary eventBillEventSummary = new EventBillEventSummary();
                    eventBillEventSummary.setFkEventBillId(eventBill.getId());
                    eventBillEventSummary.setFkEventSummaryId(fkEventSummaryId);
                    utilService.setCreateInfo(eventBillEventSummary);
                    eventBillEventSummaries.add(eventBillEventSummary);
                }
                eventBillEventSummaryService.batchAddByIds(eventBillEventSummaries);
            }

            EventBillReminderDto eventBillReminderDto = new EventBillReminderDto();
            BeanCopyUtils.copyProperties(eventBillUpdateDto, eventBillReminderDto);
            eventBillReminderDto.setFkEventBillId(eventBill.getId());
            //设置修改过的高亮属性
            setHighLineProperties(eventBillUpdateDto, eventBill, areaCountryIds, summaryIds, eventBillReminderDto);

            BeanCopyUtils.copyProperties(eventBillUpdateDto, eventBill);
            utilService.setUpdateInfo(eventBill);
            eventBillMapper.updateByIdWithNull(eventBill);

            //绑定通知人
            if (GeneralTool.isNotEmpty(eventBillUpdateDto.getDepartmentAndStaffDtos())) {
                Set<Long> staffIds = eventBillUpdateDto.getDepartmentAndStaffDtos().stream().filter(d -> GeneralTool.isNotEmpty(d.getFkStaffId()))
                        .map(DepartmentAndStaffVo::getFkStaffId).collect(Collectors.toSet());

                if (GeneralTool.isNotEmpty(staffIds)) {
                    List<EventBillStaffNotice> eventBillStaffNotices = new ArrayList<>();
                    for (Long fkStaffIdNotice : staffIds) {
                        EventBillStaffNotice eventBillStaffNotice = new EventBillStaffNotice();
                        eventBillStaffNotice.setFkEventBillId(eventBill.getId());
                        eventBillStaffNotice.setFkStaffIdNotice(fkStaffIdNotice);
                        utilService.updateUserInfoToEntity(eventBillStaffNotice);
                        eventBillStaffNotices.add(eventBillStaffNotice);
                    }

                    eventBillStaffNoticeService.batchAddByIds(eventBillStaffNotices);
                    eventBillReminderDto.setFkStaffIds(staffIds);
                }
                //异步发送提醒
//                    asyncReminderService.doAddReminders(headerMap,eventBillReminderDto,"【已更新】");
                asyncReminderService.sendEventBillEmail(headerMap, eventBillReminderDto, "【已更新】", staffInfo);
            }

        }

        return findEventBillById(eventBillUpdateDto.getId());
    }

    private void setHighLineProperties(EventBillUpdateDto eventBillUpdateDto, EventBill eventBill, List<Long> areaCountryIds, List<Long> summaryIds, EventBillReminderDto eventBillReminderDto) {
        String fkAreaCountryNameHighline = "";
        if (!eventBillUpdateDto.getFkAreaCountryIdList().containsAll(areaCountryIds) || !areaCountryIds.containsAll(eventBillUpdateDto.getFkAreaCountryIdList())) {
            fkAreaCountryNameHighline = "highline";
        }
        eventBillReminderDto.setFkAreaCountryNameHighline(fkAreaCountryNameHighline);

        String fkEventSummaryHighline = "";
        if (!eventBillUpdateDto.getFkEventSummaryIdList().containsAll(summaryIds) || !summaryIds.containsAll(eventBillUpdateDto.getFkEventSummaryIdList())) {
            fkEventSummaryHighline = "highline";
        }
        eventBillReminderDto.setFkEventSummaryHighline(fkEventSummaryHighline);

        if (!Objects.equals(eventBillUpdateDto.getFkInstitutionProviderId(), eventBill.getFkInstitutionProviderId())) {
            eventBillReminderDto.setFkInstitutionProviderNameHighline("highline");
        } else {
            eventBillReminderDto.setFkInstitutionProviderNameHighline("");
        }

        if (!Objects.equals(eventBillUpdateDto.getInvoiceAmount(), eventBill.getInvoiceAmount())) {
            eventBillReminderDto.setInvoiceAmountHighline("highline");
        } else {
            eventBillReminderDto.setInvoiceAmountHighline("");
        }

        if (!Objects.equals(eventBillUpdateDto.getFkCurrencyTypeNumInvoice(), eventBill.getFkCurrencyTypeNumInvoice())) {
            eventBillReminderDto.setFkCurrencyTypeNumInvoiceHighline("highline");
        } else {
            eventBillReminderDto.setFkCurrencyTypeNumInvoiceHighline("");
        }

        if (!Objects.equals(eventBillUpdateDto.getEventAmount(), eventBill.getEventAmount())) {
            eventBillReminderDto.setEventAmountHighline("highline");
        } else {
            eventBillReminderDto.setEventAmountHighline("");
        }

        if (!Objects.equals(eventBillUpdateDto.getFkCurrencyTypeNumEvent(), eventBill.getFkCurrencyTypeNumEvent())) {
            eventBillReminderDto.setFkCurrencyTypeNumEventHighline("highline");
        } else {
            eventBillReminderDto.setFkCurrencyTypeNumEventHighline("");
        }

        if (!Objects.equals(eventBillUpdateDto.getInvoiceSummary(), eventBill.getInvoiceSummary())) {
            eventBillReminderDto.setInvoiceSummaryHighline("highline");
        } else {
            eventBillReminderDto.setInvoiceSummaryHighline("");
        }

        if (!Objects.equals(eventBillUpdateDto.getInvoiceContactPerson(), eventBill.getInvoiceContactPerson())) {
            eventBillReminderDto.setInvoiceContactPersonHighline("highline");
        } else {
            eventBillReminderDto.setInvoiceContactPersonHighline("");
        }

        if (!Objects.equals(eventBillUpdateDto.getInvoiceContactEmail(), eventBill.getInvoiceContactEmail())) {
            eventBillReminderDto.setInvoiceContactEmailHighline("highline");
        } else {
            eventBillReminderDto.setInvoiceContactEmailHighline("");
        }

        if (!Objects.equals(eventBillUpdateDto.getEventYear(), eventBill.getEventYear())) {
            eventBillReminderDto.setEventYearHighline("highline");
        } else {
            eventBillReminderDto.setEventYearHighline("");
        }

        if (!Objects.equals(eventBillUpdateDto.getRemark(), eventBill.getRemark())) {
            eventBillReminderDto.setRemarkHighline("highline");
        } else {
            eventBillReminderDto.setRemarkHighline("");
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateInvalidStatus(Long id) {
//        作废时检查归口是否有绑定活动，如果有就提示：作废活动费用发起计划失败，有活动归口已经绑定，请先删除绑定的活动。
//        作废时检查是否已经创建了应收计划和发票，如果有就提示：作废活动费用发起计划失败，已经创建了应收计划和发票。
        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        EventBill eventBill = eventBillMapper.selectById(id);
        if (GeneralTool.isEmpty(eventBill)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }

        List<EventCost> eventCosts = eventCostMapper.selectList(Wrappers.<EventCost>lambdaQuery().eq(EventCost::getFkEventBillId, id)
                .isNotNull(EventCost::getFkEventId));


        if (GeneralTool.isNotEmpty(eventCosts)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("event_bill_has_bound"));
        }

        List<EventIncentiveCost> eventIncentiveCosts = eventIncentiveCostService.list(Wrappers.<EventIncentiveCost>lambdaQuery().eq(EventIncentiveCost::getFkEventBillId, id)
                .isNotNull(EventIncentiveCost::getFkEventIncentiveId));

        if (GeneralTool.isNotEmpty(eventIncentiveCosts)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("event_bill_has_bound_event_incentive"));
        }


//        if (GeneralTool.isNotEmpty(eventBill.getFkReceivablePlanId())){
//            throw new GetServiceException(LocaleMessageUtils.getMessage("event_bill_has_bound_plan_and_invoice"));
//        }
        //检查创建的应收和发票
        List<EventBillReceivablePlan> eventBillReceivablePlans = eventBillReceivablePlanMapper.selectList(Wrappers.lambdaQuery(EventBillReceivablePlan.class)
                .eq(EventBillReceivablePlan::getFkEventBillId, id)
        );
        if (GeneralTool.isNotEmpty(eventBillReceivablePlans)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("event_bill_has_bound_plan_and_invoice"));
        }

        if (GeneralTool.isNotEmpty(eventBill.getFkInvoiceNum())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("event_bill_has_bound_plan_and_invoice"));
        }


//        String summary = eventBill.getInvoiceSummary();

        EventBillReminderDto eventBillReminderDto = new EventBillReminderDto();
        BeanCopyUtils.copyProperties(eventBill, eventBillReminderDto);
        eventBillReminderDto.setFkEventBillId(eventBill.getId());


        List<EventBillStaffNotice> eventBillStaffNotices = eventBillStaffNoticeMapper.selectList(Wrappers.<EventBillStaffNotice>lambdaQuery()
                .eq(EventBillStaffNotice::getFkEventBillId, eventBill.getId()));

        if (GeneralTool.isNotEmpty(eventBillStaffNotices)) {
            Set<Long> staffIds = eventBillStaffNotices.stream().filter(e -> GeneralTool.isNotEmpty(e.getFkStaffIdNotice()))
                    .map(EventBillStaffNotice::getFkStaffIdNotice).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(staffIds)) {
                LambdaQueryWrapper<EventBillEventSummary> lambdaQueryWrapper = Wrappers.lambdaQuery();
                lambdaQueryWrapper.eq(EventBillEventSummary::getFkEventBillId, id);
                List<EventBillEventSummary> eventSummaries = eventBillEventSummaryService.getEventBillEventSummariesByCondition(lambdaQueryWrapper);

                if (GeneralTool.isNotEmpty(eventSummaries)) {
                    List<Long> summaryIds = eventSummaries.stream().filter(e -> GeneralTool.isNotEmpty(e.getFkEventSummaryId())).map(EventBillEventSummary::getFkEventSummaryId).collect(Collectors.toList());
                    eventBillReminderDto.setFkEventSummaryIdList(summaryIds);
                }

                LambdaQueryWrapper<EventBillAreaCountry> countryLambdaQueryWrapper = Wrappers.lambdaQuery();
                countryLambdaQueryWrapper.eq(EventBillAreaCountry::getFkEventBillId, id);
                List<EventBillAreaCountry> eventBillAreaCountries = eventBillAreaCountryService.getEventBillAreaCountriesByCondition(countryLambdaQueryWrapper);

                if (GeneralTool.isNotEmpty(eventBillAreaCountries)) {
                    List<Long> countryIds = eventBillAreaCountries.stream().filter(e -> GeneralTool.isNotEmpty(e.getFkAreaCountryId())).map(EventBillAreaCountry::getFkAreaCountryId).collect(Collectors.toList());
                    eventBillReminderDto.setFkAreaCountryIdList(countryIds);
                }


                eventBillReminderDto.setFkStaffIds(staffIds);
            }

            //异步发送提醒
//            asyncReminderService.doAddReminders(headerMap,eventBillReminderDto,"【已作废】");
            //TODO 邮件模板
            asyncReminderService.sendEventBillEmail(headerMap, eventBillReminderDto, "【已作废】", staffInfo);

        }

        eventBill.setStatus(0);
        utilService.setUpdateInfo(eventBill);
        eventBillMapper.updateById(eventBill);
    }


    @Override
    public Long editComment(CommentDto commentDto) {
        SaleComment comment = BeanCopyUtils.objClone(commentDto, SaleComment::new);
        if (GeneralTool.isNotEmpty(commentDto)) {
            if (GeneralTool.isNotEmpty(commentDto.getId())) {
                comment.setFkTableName(TableEnum.SALE_EVENT_BILL.key);
                commentService.updateComment(comment);
            } else {
                comment.setFkTableName(TableEnum.SALE_EVENT_BILL.key);
                commentService.addComment(comment);
            }
        }
        return comment.getId();
    }

    @Override
    public List<CommentVo> getComments(CommentDto commentDto, Page page) {
        commentDto.setFkTableName(TableEnum.SALE_EVENT_BILL.key);
        return commentService.datas(commentDto, page);
    }

    @Override
    public List<BaseSelectEntity> getSummarySelect(Long fkCompanyId) {

        List<Long> countryIds = SecureUtil.getCountryIds();
        if (GeneralTool.isEmpty(countryIds)) {
            countryIds.add(0L);
        }
        return eventBillMapper.getSummarySelect(fkCompanyId, countryIds, SecureUtil.getStaffId());
    }

    @Override
    public List<BaseSelectEntity> getSummarySelectList(List<Long> fkCompanyIdList) {

        List<Long> countryIds = SecureUtil.getCountryIds();
        if (GeneralTool.isEmpty(countryIds)) {
            countryIds.add(0L);
        }
        return eventBillMapper.getSummarySelectList(fkCompanyIdList, countryIds, SecureUtil.getStaffId());
    }

    @Override
    public void generateBill(EventBillUpdateDto eventBillVo) {
        if (GeneralTool.isEmpty(eventBillVo.getId()) || GeneralTool.isEmpty(eventBillVo.getInvoiceAmount()) || GeneralTool.isEmpty(eventBillVo.getFkCurrencyTypeNumInvoice())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }

        EventBill eventBill = eventBillMapper.selectById(eventBillVo);
        if (GeneralTool.isEmpty(eventBill)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }

//        if (GeneralTool.isNotEmpty(eventBill.getFkReceivablePlanId())){
//            throw new GetServiceException(LocaleMessageUtils.getMessage("plan_and_invoice_has_generated"));
//        }


        String companyName = "";
        Result<String> result = permissionCenterClient.getCompanyNameById(eventBill.getFkCompanyId());
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            companyName = result.getData();
        }

        String invoiceNum;
        Integer poNum = null;
        if (GeneralTool.isEmpty(eventBillVo.getFkInvoiceNum())) {
            Date date = new Date();
            SimpleDateFormat sf = new SimpleDateFormat("yyyyMM");
            String num = companyName + "-MKT" + sf.format(date);
            poNum = financeCenterClient.getMaxPoNum(num).getData();
            invoiceNum = MyStringUtils.getGenerateBillInvoiceNum(poNum, companyName);

            Invoice in = financeCenterClient.isExistNum(poNum, invoiceNum).getData();
            if (GeneralTool.isNotEmpty(in)) {
                invoiceNum = in.getNum();
                poNum = Integer.valueOf(in.getPoNum());
            }
        } else {
            invoiceNum = eventBillVo.getFkInvoiceNum();
        }

        List<Long> receivablePlanIds = eventBillMapper.getReceivablePlanByInvoiceNum(invoiceNum);
//        if (receivablePlanIds.size()>1){
//            throw new GetServiceException(LocaleMessageUtils.getMessage("invoice_bind_fail"));
//        }
        boolean flag = GeneralTool.isNotEmpty(receivablePlanIds) && receivablePlanIds.size() >= 1;

        String summary = eventBill.getInvoiceSummary();

        Result<Long> longResult = null;
        if (!flag) {
            //3.生成发票
            InvoiceDto invoiceDto = new InvoiceDto();
            invoiceDto.setFkCompanyId(eventBill.getFkCompanyId());
            invoiceDto.setFkTypeKey(TableEnum.INSTITUTION_PROVIDER.key);
            invoiceDto.setFkTypeTargetId(eventBill.getFkInstitutionProviderId());
            invoiceDto.setNum(invoiceNum);
            invoiceDto.setInvoiceDate(new Date());
            invoiceDto.setFkCurrencyTypeNum(eventBillVo.getFkCurrencyTypeNumInvoice());
            invoiceDto.setInvoiceAmount(eventBillVo.getInvoiceAmount());
            invoiceDto.setStatus(1);
            invoiceDto.setSummary(summary);
            if (GeneralTool.isNotEmpty(poNum)) {
                invoiceDto.setPoNum(String.valueOf(poNum));
            }
            longResult = financeCenterClient.generateInvoice(invoiceDto);
            if (!longResult.isSuccess() || GeneralTool.isEmpty(longResult.getData())) {
                //            throw new GetServiceException(LocaleMessageUtils.getMessage("invoice_num_is_exist"));
                invoiceNum = null;
            }
        }


        //发票编号自动生成
        if (GeneralTool.isEmpty(invoiceNum)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }

        eventBill.setFkInvoiceNum(invoiceNum);
        eventBill.setInvoiceAmount(eventBillVo.getInvoiceAmount());
        eventBill.setFkCurrencyTypeNumInvoice(eventBillVo.getFkCurrencyTypeNumInvoice());

        if (!flag) {
            //1、生成应收计划
            ReceivablePlan receivablePlan = new ReceivablePlan();
            receivablePlan.setFkCompanyId(eventBill.getFkCompanyId());
            receivablePlan.setFkTypeKey(TableEnum.INSTITUTION_PROVIDER.key);
            receivablePlan.setFkTypeTargetId(eventBill.getFkInstitutionProviderId());
            receivablePlan.setFkCurrencyTypeNum(eventBill.getFkCurrencyTypeNumInvoice());
            //活动费用
            receivablePlan.setBonusType(ProjectExtraEnum.EVENT_COST.key);
            receivablePlan.setBonusAmount(eventBill.getInvoiceAmount());
            receivablePlan.setReceivableAmount(eventBill.getInvoiceAmount());
            receivablePlan.setStatus(1);
            receivablePlan.setSummary(summary);
            utilService.updateUserInfoToEntity(receivablePlan);
            receivablePlanMapper.insertSelective(receivablePlan);
            //2.更新应收计划id和发票编号
            EventBillReceivablePlan eventBillReceivablePlan = new EventBillReceivablePlan();
            eventBillReceivablePlan.setFkEventBillId(eventBill.getId());
            eventBillReceivablePlan.setFkReceivablePlanId(receivablePlan.getId());
            utilService.updateUserInfoToEntity(eventBillReceivablePlan);
            eventBillReceivablePlanMapper.insert(eventBillReceivablePlan);


            //4、生成发票应收绑定关系
            InvoiceReceivablePlanDto invoiceReceivablePlanDto = new InvoiceReceivablePlanDto();
            invoiceReceivablePlanDto.setFkInvoiceId(longResult.getData());
            invoiceReceivablePlanDto.setFkReceivablePlanId(receivablePlan.getId());
            invoiceReceivablePlanDto.setAmount(eventBill.getInvoiceAmount());
            financeCenterClient.generateInvoiceReceivablePlan(invoiceReceivablePlanDto);
            if (!longResult.isSuccess()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
        } else {
//            eventBill.setFkReceivablePlanId(receivablePlanIds.get(0));
            if (GeneralTool.isEmpty(eventBillVo.getFkReceivablePlanIds())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("select_at_least_one_accounts_receivable_plan"));
            }
            for (Long fkReceivablePlanId : eventBillVo.getFkReceivablePlanIds()) {
                EventBillReceivablePlan eventBillReceivablePlan = new EventBillReceivablePlan();
                eventBillReceivablePlan.setFkEventBillId(eventBill.getId());
                eventBillReceivablePlan.setFkReceivablePlanId(fkReceivablePlanId);
                utilService.updateUserInfoToEntity(eventBillReceivablePlan);
                eventBillReceivablePlanMapper.insert(eventBillReceivablePlan);
            }
        }
        utilService.setUpdateInfo(eventBill);
        eventBillMapper.updateById(eventBill);

    }

    @Override
    public List<DepartmentAndStaffVo> getDefaultStafffNotices(Long fkCompanyId) {
        List<Long> staffIds = Lists.newArrayList();
        if (GeneralTool.isEmpty(fkCompanyId)) {
            fkCompanyId = SecureUtil.getFkCompanyId();
        }

        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.EVENT_BILL_DEFAULT_NOTICE.key, 1).getData();
        String configValue1 = companyConfigMap.get(fkCompanyId);
        if (GeneralTool.isEmpty(configValue1)) {
            return null;
        }
        JSONArray jsonArray = JSON.parseArray(configValue1);
        staffIds.addAll(jsonArray.toJavaList(Long.class));
        if (!staffIds.contains(SecureUtil.getStaffId())) {
            staffIds.add(SecureUtil.getStaffId());
        }
        return permissionCenterClient.getDepartmentAndStaffDtoByStaffIds(new HashSet<>(staffIds)).getData();
    }

    /**
     * 财务一键作废
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateInvalidStatusFinance(Long id) {
        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        //作废时检查是否存在已经绑定对应应收计划或发票的收款单，如果有提示：财务一键作废失败，已经存在收款业务记录。
        EventBill eventBill = eventBillMapper.selectById(id);
        if (GeneralTool.isEmpty(eventBill)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }

        List<EventBillReceivablePlan> eventBillReceivablePlans = eventBillReceivablePlanMapper.selectList(Wrappers.lambdaQuery(EventBillReceivablePlan.class)
                .eq(EventBillReceivablePlan::getFkEventBillId, id)
        );
        Boolean flag = false;
        if (GeneralTool.isNotEmpty(eventBillReceivablePlans)) {
            for (EventBillReceivablePlan eventBillReceivablePlan : eventBillReceivablePlans) {
                //判断是否产生收款绑定
                Result<Boolean> flagResult = financeCenterClient.isReceivablePlanBound(eventBillReceivablePlan.getFkReceivablePlanId());
                if (flagResult.isSuccess() && GeneralTool.isNotEmpty(flagResult.getData())) {
                    flag = flagResult.getData();
                    if (flag) {
                        break;
                    }
                }
            }
        }

        if (flag) {
            //已经绑定收款，不给作废
            throw new GetServiceException(LocaleMessageUtils.getMessage("event_bill_has_bound_receipt_form"));
        } else {
            //A. 作废对应的应收计划
            //B. 作废对应的发票
            //C. 删除归口活动绑定
            //D. 作废活动费用发起计划

            //作废eventBill
            eventBill.setStatus(0);
            utilService.setUpdateInfo(eventBill);
            eventBillMapper.updateById(eventBill);

            // TODO: 2024/4/11 改成绑定多个应收 作废逻辑需考虑
            //同时作废应收计划
            List<EventBillReceivablePlan> eventBillReceivablePlanList = eventBillReceivablePlanMapper.selectList(Wrappers.lambdaQuery(EventBillReceivablePlan.class)
                    .eq(EventBillReceivablePlan::getFkEventBillId, id)
            );
            if (GeneralTool.isNotEmpty(eventBillReceivablePlanList)){
                ReceivablePlan receivablePlan = receivablePlanMapper.selectById(eventBillReceivablePlanList.get(0).getFkReceivablePlanId());
                receivablePlan.setStatus(0);
                utilService.setUpdateInfo(receivablePlan);
                receivablePlanMapper.updateById(receivablePlan);
            }

            //作废发票
            if (GeneralTool.isNotEmpty(eventBill.getFkInvoiceNum())) {
                InvoiceDto invoiceDto = new InvoiceDto();
                invoiceDto.setStatus(0);
                Result<List<Invoice>> invoiceResult = financeCenterClient.getInvoiceByNum(eventBill.getFkInvoiceNum());
                if (invoiceResult.isSuccess()) {
                    if (GeneralTool.isNotEmpty(invoiceResult.getData())) {
                        List<Invoice> data = invoiceResult.getData();
                        if (data.size() != 1) {
                            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
                        } else {
                            BeanCopyUtils.copyProperties(data.get(0), invoiceDto);
                            //                    invoiceVo.setId(data.get(0).getId());
                            Set<Long> invoices = new HashSet<>(1);
                            invoices.add(eventBill.getFkInstitutionProviderId());
                            invoiceDto.setFkTypeTargetIds(invoices);
                            invoiceDto.setStatus(0);
                            Result result = financeCenterClient.updateInvoice(invoiceDto);
                            if (!result.isSuccess()) {
                                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
                            }
                        }
                    }
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
                }
            }


            //去掉和费用归口的绑定关系
            eventCostMapper.delete(Wrappers.<EventCost>lambdaQuery().eq(EventCost::getFkEventBillId, id));
            //去掉和奖励推广活动费用归口的绑定关系
            eventIncentiveCostService.remove(Wrappers.<EventIncentiveCost>lambdaQuery().eq(EventIncentiveCost::getFkEventBillId, id));

//            String summary = eventBill.getInvoiceSummary();

            EventBillReminderDto eventBillReminderDto = new EventBillReminderDto();
            BeanCopyUtils.copyProperties(eventBill, eventBillReminderDto);
            eventBillReminderDto.setFkEventBillId(eventBill.getId());


            List<EventBillStaffNotice> eventBillStaffNotices = eventBillStaffNoticeMapper.selectList(Wrappers.<EventBillStaffNotice>lambdaQuery()
                    .eq(EventBillStaffNotice::getFkEventBillId, eventBill.getId()));

            if (GeneralTool.isNotEmpty(eventBillStaffNotices)) {
                Set<Long> staffIds = eventBillStaffNotices.stream().filter(e -> GeneralTool.isNotEmpty(e.getFkStaffIdNotice()))
                        .map(EventBillStaffNotice::getFkStaffIdNotice).collect(Collectors.toSet());
                if (GeneralTool.isNotEmpty(staffIds)) {

                    LambdaQueryWrapper<EventBillEventSummary> lambdaQueryWrapper = Wrappers.lambdaQuery();
                    lambdaQueryWrapper.eq(EventBillEventSummary::getFkEventBillId, id);
                    List<EventBillEventSummary> eventSummaries = eventBillEventSummaryService.getEventBillEventSummariesByCondition(lambdaQueryWrapper);

                    if (GeneralTool.isNotEmpty(eventSummaries)) {
                        List<Long> summaryIds = eventSummaries.stream().filter(e -> GeneralTool.isNotEmpty(e.getFkEventSummaryId())).map(EventBillEventSummary::getFkEventSummaryId).collect(Collectors.toList());
                        eventBillReminderDto.setFkEventSummaryIdList(summaryIds);
                    }

                    LambdaQueryWrapper<EventBillAreaCountry> countryLambdaQueryWrapper = Wrappers.lambdaQuery();
                    countryLambdaQueryWrapper.eq(EventBillAreaCountry::getFkEventBillId, id);
                    List<EventBillAreaCountry> eventBillAreaCountries = eventBillAreaCountryService.getEventBillAreaCountriesByCondition(countryLambdaQueryWrapper);

                    if (GeneralTool.isNotEmpty(eventBillAreaCountries)) {
                        List<Long> countryIds = eventBillAreaCountries.stream().filter(e -> GeneralTool.isNotEmpty(e.getFkAreaCountryId())).map(EventBillAreaCountry::getFkAreaCountryId).collect(Collectors.toList());
                        eventBillReminderDto.setFkAreaCountryIdList(countryIds);
                    }


                    eventBillReminderDto.setFkStaffIds(staffIds);
                }

                //异步发送提醒
//                asyncReminderService.doAddReminders(headerMap,eventBillReminderDto,"【已作废】");
                asyncReminderService.sendEventBillEmail(headerMap, eventBillReminderDto, "【已作废】", staffInfo);
            }
        }

    }

    @Override
    public List<Map<String, Object>> getAllocationStatusSelect() {
        return ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.EB_STATUS);
    }

    @Override
    public List<EventBillVo> getEventBillByProviderIdAndEventIncentiveCostId(Long institutionProviderId, Long companyId, Long eventIncentiveCostId) {
        return eventBillMapper.getEventBillByProviderIdAndEventIncentiveCostId(institutionProviderId, companyId, eventIncentiveCostId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateInvalidStatusFinancialDocuments(Long id) {
        /*
            作废时检查是否存在已经绑定对应应收计划或发票的收款单，如果有提示：作废财务单据失败，已经存在收款业务记录。
            若可以作废时，需要做如下逻辑操作：
            A. 作废对应的应收计划
            B. 作废对应的发票
        */
        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        EventBill eventBill = eventBillMapper.selectById(id);
        if (GeneralTool.isEmpty(eventBill)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }

        List<EventBillReceivablePlan> eventBillReceivablePlans = eventBillReceivablePlanMapper.selectList(Wrappers.lambdaQuery(EventBillReceivablePlan.class)
                .eq(EventBillReceivablePlan::getFkEventBillId, id)
        );
        Boolean flag = false;
        if (GeneralTool.isNotEmpty(eventBillReceivablePlans)) {
            //判断是否产生收款绑定
            for (EventBillReceivablePlan eventBillReceivablePlan : eventBillReceivablePlans) {
                //判断是否产生收款绑定
                Result<Boolean> flagResult = financeCenterClient.isReceivablePlanBound(eventBillReceivablePlan.getFkReceivablePlanId());
                if (flagResult.isSuccess() && GeneralTool.isNotEmpty(flagResult.getData())) {
                    flag = flagResult.getData();
                    if (flag) {
                        break;
                    }
                }
            }
        }

        if (flag) {
            //已经绑定收款，不给作废
            throw new GetServiceException(LocaleMessageUtils.getMessage("financial_documents_update_status_fail"));
        } else {
            /*
                A. 作废对应的应收计划
                B. 作废对应的发票
            */

            if (GeneralTool.isEmpty(eventBillReceivablePlans) && GeneralTool.isEmpty(eventBill.getFkInvoiceNum())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
            }
            // TODO: 2024/4/11 需考虑是否作废应收 因为也可能绑定了其他地方
//            //同时作废应收计划
            if (GeneralTool.isNotEmpty(eventBillReceivablePlans)){
                ReceivablePlan receivablePlan = receivablePlanMapper.selectById(eventBillReceivablePlans.get(0).getFkReceivablePlanId());
                receivablePlan.setStatus(0);
                utilService.setUpdateInfo(receivablePlan);
                receivablePlanMapper.updateById(receivablePlan);
            }

            //作废发票
            if (GeneralTool.isNotEmpty(eventBill.getFkInvoiceNum())) {
                InvoiceDto invoiceDto = new InvoiceDto();
                invoiceDto.setStatus(0);
                Result<List<Invoice>> invoiceResult = financeCenterClient.getInvoiceByNum(eventBill.getFkInvoiceNum());
                if (invoiceResult.isSuccess() && GeneralTool.isNotEmpty(invoiceResult.getData())) {
                    List<Invoice> data = invoiceResult.getData();
                    if (data.size() != 1) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
                    } else {
                        BeanCopyUtils.copyProperties(data.get(0), invoiceDto);
                        Set<Long> invoices = new HashSet<>(1);
                        invoices.add(eventBill.getFkInstitutionProviderId());
                        //检查发票是否已绑定收款单
                        Boolean exist = financeCenterClient.checkReceiptInvoiceMappingExist(invoiceDto.getId());
                        if (exist) {
                            throw new GetServiceException(LocaleMessageUtils.getMessage("the_invoice_has_been_bound_to_the_payment_receipt"));
                        }
                        invoiceDto.setFkTypeTargetIds(invoices);
                        invoiceDto.setStatus(0);
                        Result result = financeCenterClient.updateInvoice(invoiceDto);
                        if (!result.isSuccess()) {
                            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
                        }
                    }
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
                }
            }

            eventBillReceivablePlanMapper.delete(Wrappers.lambdaQuery(EventBillReceivablePlan.class)
                    .eq(EventBillReceivablePlan::getFkEventBillId, id)
            );
//            eventBill.setFkReceivablePlanId(null);
            eventBill.setFkInvoiceNum(null);
            utilService.setUpdateInfo(eventBill);
            eventBillMapper.updateByIdWithNull(eventBill);

            EventBillReminderDto eventBillReminderDto = new EventBillReminderDto();
            BeanCopyUtils.copyProperties(eventBill, eventBillReminderDto);
            eventBillReminderDto.setFkEventBillId(eventBill.getId());

            List<EventBillStaffNotice> eventBillStaffNotices = eventBillStaffNoticeMapper.selectList(Wrappers.<EventBillStaffNotice>lambdaQuery()
                    .eq(EventBillStaffNotice::getFkEventBillId, eventBill.getId()));

            if (GeneralTool.isNotEmpty(eventBillStaffNotices)) {
                Set<Long> staffIds = eventBillStaffNotices.stream().filter(e -> GeneralTool.isNotEmpty(e.getFkStaffIdNotice()))
                        .map(EventBillStaffNotice::getFkStaffIdNotice).collect(Collectors.toSet());
                if (GeneralTool.isNotEmpty(staffIds)) {

                    LambdaQueryWrapper<EventBillEventSummary> lambdaQueryWrapper = Wrappers.lambdaQuery();
                    lambdaQueryWrapper.eq(EventBillEventSummary::getFkEventBillId, id);
                    List<EventBillEventSummary> eventSummaries = eventBillEventSummaryService.getEventBillEventSummariesByCondition(lambdaQueryWrapper);

                    if (GeneralTool.isNotEmpty(eventSummaries)) {
                        List<Long> summaryIds = eventSummaries.stream().filter(e -> GeneralTool.isNotEmpty(e.getFkEventSummaryId())).map(EventBillEventSummary::getFkEventSummaryId).collect(Collectors.toList());
                        eventBillReminderDto.setFkEventSummaryIdList(summaryIds);
                    }

                    LambdaQueryWrapper<EventBillAreaCountry> countryLambdaQueryWrapper = Wrappers.lambdaQuery();
                    countryLambdaQueryWrapper.eq(EventBillAreaCountry::getFkEventBillId, id);
                    List<EventBillAreaCountry> eventBillAreaCountries = eventBillAreaCountryService.getEventBillAreaCountriesByCondition(countryLambdaQueryWrapper);

                    if (GeneralTool.isNotEmpty(eventBillAreaCountries)) {
                        List<Long> countryIds = eventBillAreaCountries.stream().filter(e -> GeneralTool.isNotEmpty(e.getFkAreaCountryId())).map(EventBillAreaCountry::getFkAreaCountryId).collect(Collectors.toList());
                        eventBillReminderDto.setFkAreaCountryIdList(countryIds);
                    }


                    eventBillReminderDto.setFkStaffIds(staffIds);
                }

                //异步发送提醒
//                asyncReminderService.doAddReminders(headerMap,eventBillReminderDto,"【财务单据已作废】");
                //TODO 邮件模板
                asyncReminderService.sendEventBillEmail(headerMap, eventBillReminderDto, "【财务单据已作废】", staffInfo);
            }

        }
    }

    /**
     * 发送到账邮件提醒
     *
     * @param eventBillAccountNoticeDto
     */
    @Override
    public ResponseBo sendAccountEmail(EventBillAccountNoticeDto eventBillAccountNoticeDto) {
        try {
            Set<Long> planIds = eventBillAccountNoticeDto.getPlanIds();
            Set<Long> receiptFormItemIds = eventBillAccountNoticeDto.getReceiptFormItemIds();
            List<ReceiptFormItem> receiptFormItems = eventBillAccountNoticeDto.getReceiptFormItems();
            Map<String, String> headerMap = eventBillAccountNoticeDto.getHeaderMap();
            if (GeneralTool.isEmpty(planIds) || GeneralTool.isEmpty(receiptFormItemIds)) {
                return ResponseBo.ok();
            }
            StaffInfo staffInfo = SecureUtil.getStaffInfo();
            List<ReceivablePlan> receivablePlans = receivablePlanMapper.selectList(Wrappers.<ReceivablePlan>lambdaQuery().in(ReceivablePlan::getId, planIds));
            Map<Long, String> currencyMap = receivablePlans.stream().collect(Collectors.toMap(ReceivablePlan::getId, ReceivablePlan::getFkCurrencyTypeNum));


            List<Long> eventBillIdList = Lists.newArrayList();
            Map<Long, Long> receivablePlanIdMap = Maps.newHashMap();
            List<EventBillReceivablePlan> eventBillReceivablePlans = eventBillReceivablePlanMapper.selectList(Wrappers.lambdaQuery(EventBillReceivablePlan.class)
                    .in(EventBillReceivablePlan::getFkReceivablePlanId, planIds)
            );
            if (GeneralTool.isNotEmpty(eventBillReceivablePlans)) {
                eventBillIdList = eventBillReceivablePlans.stream().map(EventBillReceivablePlan::getFkEventBillId).collect(Collectors.toList());
                receivablePlanIdMap = eventBillReceivablePlans.stream().collect(Collectors.toMap(EventBillReceivablePlan::getFkEventBillId, EventBillReceivablePlan::getFkReceivablePlanId));
            }
            if (GeneralTool.isEmpty(eventBillIdList)) {
                return ResponseBo.ok();
            }
            List<EventBill> eventBills = list(Wrappers.<EventBill>lambdaQuery().in(EventBill::getId, eventBillIdList).eq(EventBill::getStatus, 1));
            if (GeneralTool.isNotEmpty(eventBills)) {
                //到账金额
                List<EventBillAccountVo> eventBillAccountVos = this.baseMapper.getEventBillAccount(planIds);
                Map<Long, List<EventBillAccountVo>> eventBillAccountDtoMap = Maps.newHashMap();
                if (GeneralTool.isNotEmpty(eventBillAccountVos)) {
                    eventBillAccountDtoMap = eventBillAccountVos.stream().collect(Collectors.groupingBy(EventBillAccountVo::getFkEventBillId));
                }

                Set<Long> eventBillIds = eventBills.stream().map(EventBill::getId).collect(Collectors.toSet());
                //通知人
                List<EventBillStaffNotice> eventBillStaffNotices = eventBillStaffNoticeService.list(Wrappers.<EventBillStaffNotice>lambdaQuery().in(EventBillStaffNotice::getFkEventBillId, eventBillIds));
                if (GeneralTool.isEmpty(eventBillStaffNotices)) {
                    return ResponseBo.ok();
                }
                Map<Long, List<EventBillStaffNotice>> eventBillStaffNoticeMap = eventBillStaffNotices.stream().collect(Collectors.groupingBy(EventBillStaffNotice::getFkEventBillId));

                //摘要
                Map<Long, List<EventBillEventSummary>> eventBillEventSummaryMap = Maps.newHashMap();
                List<EventBillEventSummary> eventBillEventSummaries = eventBillEventSummaryService.list(Wrappers.<EventBillEventSummary>lambdaQuery().in(EventBillEventSummary::getFkEventBillId, eventBillIds));
                if (GeneralTool.isNotEmpty(eventBillStaffNotices)) {
                    eventBillEventSummaryMap = eventBillEventSummaries.stream().collect(Collectors.groupingBy(EventBillEventSummary::getFkEventBillId));
                }

                //国家
                Map<Long, List<EventBillAreaCountry>> eventBillAreaCountryMap = Maps.newHashMap();
                List<EventBillAreaCountry> eventBillAreaCountries = eventBillAreaCountryService.list(Wrappers.<EventBillAreaCountry>lambdaQuery().in(EventBillAreaCountry::getFkEventBillId, eventBillIds));
                if (GeneralTool.isNotEmpty(eventBillAreaCountries)) {
                    eventBillAreaCountryMap = eventBillAreaCountries.stream().collect(Collectors.groupingBy(EventBillAreaCountry::getFkEventBillId));
                }


                for (EventBill eventBill : eventBills) {
                    EventBillReminderDto eventBillReminderDto = new EventBillReminderDto();
                    BeanCopyUtils.copyProperties(eventBill, eventBillReminderDto);
                    eventBillReminderDto.setFkEventBillId(eventBill.getId());
                    if (GeneralTool.isNotEmpty(eventBillStaffNoticeMap)) {
                        List<EventBillStaffNotice> notices = eventBillStaffNoticeMap.getOrDefault(eventBill.getId(), Lists.newArrayList());
                        Set<Long> staffIds = notices.stream().map(EventBillStaffNotice::getFkStaffIdNotice).collect(Collectors.toSet());
                        if (GeneralTool.isNotEmpty(staffIds)) {
                            eventBillReminderDto.setFkStaffIds(staffIds);
                        }
                    }
                    if (GeneralTool.isNotEmpty(eventBillEventSummaryMap)) {
                        List<EventBillEventSummary> eventSummaries = eventBillEventSummaryMap.get(eventBill.getId());
                        List<Long> summaryIds = eventSummaries.stream().map(EventBillEventSummary::getFkEventSummaryId).collect(Collectors.toList());
                        if (GeneralTool.isNotEmpty(summaryIds)) {
                            eventBillReminderDto.setFkEventSummaryIdList(summaryIds);
                        }
                    }
                    if (GeneralTool.isNotEmpty(eventBillAreaCountryMap)) {
                        List<EventBillAreaCountry> eventBillAreaCountryList = eventBillAreaCountryMap.get(eventBill.getId());
                        List<Long> countryIds = eventBillAreaCountryList.stream().map(EventBillAreaCountry::getFkAreaCountryId).collect(Collectors.toList());
                        if (GeneralTool.isNotEmpty(countryIds)) {
                            eventBillReminderDto.setFkAreaCountryIdList(countryIds);
                        }
                    }
                    List<EventBillAccountVo> accountDtos = eventBillAccountDtoMap.get(eventBill.getId());
                    Long fkReceivablePlanId = receivablePlanIdMap.get(eventBill.getId());
                    if (GeneralTool.isEmpty(fkReceivablePlanId)) {
                        continue;
                    }
                    List<ReceiptFormItem> receiptFormItemList;
                    List<EventBillAccountVo> eventBillAccountVoList;
                    String currencyNum = currencyMap.get(fkReceivablePlanId);
                    receiptFormItemList = receiptFormItems.stream().filter(receiptFormItem -> receiptFormItem.getFkReceivablePlanId().equals(fkReceivablePlanId)).sorted(Comparator.comparing(ReceiptFormItem::getGmtCreate)).collect(Collectors.toList());

                    if (GeneralTool.isNotEmpty(accountDtos)) {
                        eventBillAccountVoList = receiptFormItemList.stream().map(r -> {
                            EventBillAccountVo eventBillAccountVo = new EventBillAccountVo();
                            eventBillAccountVo.setFkEventBillId(eventBill.getId());
                            eventBillAccountVo.setFkCurrencyTypeNum(currencyNum);
                            eventBillAccountVo.setAmountReceivable(r.getAmountReceivable());
                            eventBillAccountVo.setFkReceiptFormItemId(r.getId());
                            eventBillAccountVo.setFkReceivablePlanId(fkReceivablePlanId);
                            return eventBillAccountVo;
                        }).collect(Collectors.toList());
                        accountDtos.addAll(eventBillAccountVoList);

                    } else {
                        accountDtos = receiptFormItemList.stream().map(r -> {
                            EventBillAccountVo eventBillAccountVo = new EventBillAccountVo();
                            eventBillAccountVo.setFkEventBillId(eventBill.getId());
                            eventBillAccountVo.setFkCurrencyTypeNum(currencyNum);
                            eventBillAccountVo.setAmountReceivable(r.getAmountReceivable());
                            eventBillAccountVo.setFkReceiptFormItemId(r.getId());
                            eventBillAccountVo.setFkReceivablePlanId(fkReceivablePlanId);
                            return eventBillAccountVo;
                        }).collect(Collectors.toList());
                    }

                    //排序先后
                    final Integer[] order = {0};
                    accountDtos = accountDtos.stream().map(a -> {
                        a.setOrder(order[0]);
                        order[0]++;
                        return a;
                    }).collect(Collectors.toList());
                    for (EventBillAccountVo accountDto : accountDtos) {
                        if (receiptFormItemIds.contains(accountDto.getFkReceiptFormItemId())) {
                            Integer currentOrder = accountDto.getOrder();
                            BigDecimal totalActualReceivableAmount = accountDtos.stream().filter(a -> a.getOrder().compareTo(currentOrder) <= 0).map(EventBillAccountVo::getAmountReceivable).reduce(BigDecimal.ZERO, BigDecimal::add);
                            EventBillReminderDto reminderVo = BeanCopyUtils.objClone(eventBillReminderDto, EventBillReminderDto::new);
                            assert reminderVo != null;
                            reminderVo.setTotalActualReceivableAmount(totalActualReceivableAmount);
                            reminderVo.setActualReceivableAmount(accountDto.getAmountReceivable());
                            reminderVo.setActualReceivableAmountCurrencyNum(accountDto.getFkCurrencyTypeNum());
                            //异步发送邮件
                            //TODO 邮件模板
                            asyncReminderService.sendEventBillEmailAsync(headerMap, reminderVo, "【已到账】", staffInfo);
                        }
                    }

                }
            }
        } catch (GetServiceException e) {
            return ResponseBo.error(ErrorCodeEnum.BAD_REQUEST.getCode(), e.getMessage());
        }

        return ResponseBo.ok();
    }

    /**
     * 重新发起
     *
     * @param eventBillUpdateAmountDto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBo updateAmount(EventBillUpdateAmountDto eventBillUpdateAmountDto) {

        EventBill eventBillOld = baseMapper.selectById(eventBillUpdateAmountDto.getId());
        String eventAmountHighLine = "";
        if (!Objects.equals(eventBillUpdateAmountDto.getEventAmount(), eventBillOld.getEventAmount())) {
            eventAmountHighLine = "highline";
        }
        String fkCurrencyTypeNumEventHighLine = "";
        if (!Objects.equals(eventBillUpdateAmountDto.getFkCurrencyTypeNumEvent(), eventBillOld.getFkCurrencyTypeNumEvent())) {
            fkCurrencyTypeNumEventHighLine = "highline";
        }
        String invoiceAmountHighLine = "";
        if (!Objects.equals(eventBillUpdateAmountDto.getInvoiceAmount(), eventBillOld.getInvoiceAmount())) {
            invoiceAmountHighLine = "highline";
        }
        String fkCurrencyTypeNumInvoiceHighLine = "";
        if (!Objects.equals(eventBillUpdateAmountDto.getFkCurrencyTypeNumInvoice(), eventBillOld.getFkCurrencyTypeNumInvoice())) {
            fkCurrencyTypeNumInvoiceHighLine = "highline";
        }

        EventBill eventBill = BeanCopyUtils.objClone(eventBillUpdateAmountDto, EventBill::new);
        utilService.setUpdateInfo(eventBill);
        boolean update = updateById(eventBill);
        if (!update) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        //获取详情
        assert eventBill != null;
        EventBillVo eventBillVo = findEventBillById(eventBill.getId());

        EventBillReminderDto eventBillReminderDto = new EventBillReminderDto();
        BeanCopyUtils.copyProperties(eventBillVo, eventBillReminderDto);
        eventBillReminderDto.setFkEventBillId(eventBill.getId());
        eventBillReminderDto.setEventAmountHighline(eventAmountHighLine);
        eventBillReminderDto.setInvoiceAmountHighline(invoiceAmountHighLine);
        eventBillReminderDto.setFkCurrencyTypeNumEventHighline(fkCurrencyTypeNumEventHighLine);
        eventBillReminderDto.setFkCurrencyTypeNumInvoiceHighline(fkCurrencyTypeNumInvoiceHighLine);

        List<EventBillReceivablePlan> eventBillReceivablePlans = eventBillReceivablePlanMapper.selectList(Wrappers.lambdaQuery(EventBillReceivablePlan.class)
                .eq(EventBillReceivablePlan::getFkEventBillId, eventBill.getId())
        );

        // TODO: 2024/4/11  改成绑定多个应收计划 应收金额修改也得考虑
        Long fkReceivablePlanId = null;
        boolean flag = false;
        if (GeneralTool.isNotEmpty(eventBillReceivablePlans)) {
            flag = eventBillReceivablePlans.size() == 1;
            if (flag) {
                fkReceivablePlanId = eventBillReceivablePlans.get(0).getFkReceivablePlanId();
            }
        }
        if (GeneralTool.isNotEmpty(fkReceivablePlanId)) {
            ReceivablePlan receivablePlan = receivablePlanMapper.selectById(fkReceivablePlanId);
            if (GeneralTool.isEmpty(receivablePlan)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
            receivablePlan.setReceivableAmount(eventBillUpdateAmountDto.getInvoiceAmount());
            receivablePlan.setBonusAmount(eventBillUpdateAmountDto.getInvoiceAmount());
            receivablePlan.setFkCurrencyTypeNum(eventBillUpdateAmountDto.getFkCurrencyTypeNumInvoice());
            utilService.setUpdateInfo(receivablePlan);
            receivablePlanMapper.updateById(receivablePlan);
        }

        InvoiceDto invoiceDto = new InvoiceDto();
        Result<List<Invoice>> invoiceResult = financeCenterClient.getInvoiceByNum(eventBillVo.getFkInvoiceNum());
        if (invoiceResult.isSuccess() && GeneralTool.isNotEmpty(invoiceResult.getData())) {
            List<Invoice> data = invoiceResult.getData();
            if (data.size() != 1) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            } else {
                BeanCopyUtils.copyProperties(data.get(0), invoiceDto);
                Set<Long> invoices = new HashSet<>(1);
                invoices.add(eventBillVo.getFkInstitutionProviderId());
                invoiceDto.setInvoiceAmount(eventBillUpdateAmountDto.getInvoiceAmount());
                invoiceDto.setFkCurrencyTypeNum(eventBillUpdateAmountDto.getFkCurrencyTypeNumInvoice());
//                invoiceVo.setReceiptAmount(eventBillUpdateAmountDto.getInvoiceAmount());
                invoiceDto.setFkTypeTargetIds(invoices);
                Result result = financeCenterClient.updateInvoice(invoiceDto);
                if (!result.isSuccess()) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
                }
            }

            //
//            Result result = financeCenterClient.updateInvoiceReceivablePlan(data.get(0).getId(), fkReceivablePlanId, eventBillUpdateAmountDto.getInvoiceAmount());
//            if (!result.isSuccess()){
//                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
//            }
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }

        //发送通知
        if (GeneralTool.isNotEmpty(eventBillVo.getDepartmentAndStaffDtos())) {
            Set<Long> staffIds = eventBillVo.getDepartmentAndStaffDtos().stream().filter(d -> GeneralTool.isNotEmpty(d.getFkStaffId()))
                    .map(DepartmentAndStaffVo::getFkStaffId).collect(Collectors.toSet());
            eventBillReminderDto.setFkStaffIds(staffIds);
            //异步发送提醒
            //TODO EVENT_BILL_NOTICE 邮件模板
            asyncReminderService.sendEventBillEmail(headerMap, eventBillReminderDto, "【活动费用修改】", staffInfo);
        }
        if (flag) {
            ResponseBo responseBo = new ResponseBo(true);
            responseBo.setCode(ErrorCodeEnum.REQUEST_OK.getCode());
            responseBo.setMessage("发起成功");
            return responseBo;
        } else {
            ResponseBo responseBo = new ResponseBo(true);
            responseBo.setCode(ErrorCodeEnum.REQUEST_OK.getCode());
            responseBo.setMessage("有多条应收计划无法做数据联动，需要手动修改!");
            return responseBo;
        }
    }

    /**
     * 统计小计
     *
     * @param eventBillListDto
     * @return
     */
    @Override
    public List<EventBillSubtotalVo> getEventBillSubtotals(EventBillListDto eventBillListDto) {
        List<EventBillSubtotalVo> result = Lists.newArrayList();
        if (GeneralTool.isEmpty(eventBillListDto)) {
            if (GeneralTool.isEmpty(eventBillListDto.getFkCompanyId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
            }
        }
        List<Long> countryIds = SecureUtil.getCountryIds();
        if (GeneralTool.isEmpty(countryIds)) {
            countryIds.add(0L);
        }
        //获取业务下属
        Long staffId = SecureUtil.getStaffId();
        //员工id + 业务下属员工ids
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData();
        staffFollowerIds.add(staffId);
        //TODO: 2024/4/11  活动费用小计(增加登录用户是否是创建用户)
        List<EventBillListVo> eventBillListVos = eventBillMapper.getEventBills(null, eventBillListDto, countryIds, SecureUtil.getStaffId(), SecureUtil.getStaffInfo().getLoginId(),staffFollowerIds);
        if (GeneralTool.isEmpty(eventBillListVos)) {
            return Collections.emptyList();
        }

        Map<String, List<EventBillListVo>> eventBillListDtosMap = eventBillListVos.stream().collect(Collectors.groupingBy(EventBillListVo::getFkCurrencyTypeNumEvent));
        for (Map.Entry<String, List<EventBillListVo>> entry : eventBillListDtosMap.entrySet()) {
            EventBillSubtotalVo eventBillSubtotalVo = new EventBillSubtotalVo();
            eventBillSubtotalVo.setFkCurrencyTypeNumEvent(entry.getKey());
            List<EventBillListVo> billListDtos = entry.getValue();
            if (GeneralTool.isEmpty(billListDtos)) {
                continue;
            }
            BigDecimal sumEventAmount = billListDtos.stream().map(EventBillListVo::getEventAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
            eventBillSubtotalVo.setSumEventAmount(sumEventAmount);
            BigDecimal sumDifferenceEventAmount = billListDtos.stream().map(EventBillListVo::getDifferenceEventAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
            eventBillSubtotalVo.setSumDifferenceEventAmount(sumDifferenceEventAmount);
            BigDecimal sumAssignedAmount = billListDtos.stream().map(EventBillListVo::getAssignedAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
            eventBillSubtotalVo.setSumAssignedAmount(sumAssignedAmount);
            result.add(eventBillSubtotalVo);
        }

        if (GeneralTool.isNotEmpty(result)) {
            BigDecimal sumAllAssignedAmount = BigDecimal.ZERO;
            BigDecimal sumAllDifferenceEventAmount = BigDecimal.ZERO;
            BigDecimal sumAllEventAmount = BigDecimal.ZERO;
            String fkCurrencyTypeNumEvent = "CNY（总额）";
            for (EventBillSubtotalVo eventBillSubtotalVo : result) {
                Result<BigDecimal> rateResult = financeCenterClient.getLastExchangeRate(true, eventBillSubtotalVo.getFkCurrencyTypeNumEvent(), "CNY");
                if (rateResult.isSuccess() && GeneralTool.isNotEmpty(rateResult.getData())) {
                    sumAllAssignedAmount = sumAllAssignedAmount.add(eventBillSubtotalVo.getSumAssignedAmount().multiply(rateResult.getData())).setScale(2, BigDecimal.ROUND_HALF_UP);
                    sumAllDifferenceEventAmount = sumAllDifferenceEventAmount.add(eventBillSubtotalVo.getSumDifferenceEventAmount().multiply(rateResult.getData())).setScale(2, BigDecimal.ROUND_HALF_UP);
                    sumAllEventAmount = sumAllEventAmount.add(eventBillSubtotalVo.getSumEventAmount().multiply(rateResult.getData())).setScale(2, BigDecimal.ROUND_HALF_UP);
                }
            }
            EventBillSubtotalVo eventBillSubtotalVo = new EventBillSubtotalVo();
            eventBillSubtotalVo.setSumAssignedAmount(sumAllAssignedAmount);
            eventBillSubtotalVo.setSumDifferenceEventAmount(sumAllDifferenceEventAmount);
            eventBillSubtotalVo.setSumEventAmount(sumAllEventAmount);
            eventBillSubtotalVo.setFkCurrencyTypeNumEvent(fkCurrencyTypeNumEvent);
            result.add(eventBillSubtotalVo);
        }
        return result;
    }

    /**
     * 导出活动费用列表
     * @param eventBillListDto
     * @param page
     * @param response
     */
    @Override
    public void exportEventBillList(EventBillListDto eventBillListDto, Page page, HttpServletResponse response) {
        List<EventBillListVo> eventBillListVos = getEventBills(eventBillListDto, null);

        if (GeneralTool.isEmpty(eventBillListVos)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        List<ExportEventBillListVo> exportEventBillListVos =eventBillListVos.stream().map(eventBillListVo-> {
            ExportEventBillListVo exportEventBillListVo = BeanCopyUtils.objClone(eventBillListVo, ExportEventBillListVo::new);
//            if (GeneralTool.isNotEmpty(institutionVo.getIsBindingActive())){
//                exportInstitutionVo.setIsBindingActive(institutionVo.getIsBindingActive() ? "绑定" : "解绑");
//            }
            return exportEventBillListVo;
        }).collect(Collectors.toList());
        FileUtils.exportExcelNotWrapText(response, exportEventBillListVos, "eventBillList", ExportEventBillListVo.class);

    }


    @Override
    public List<ReceivablePlanNewVo> getEventBillReceivablePlanList(String invoiceNum) {
        ReceivablePlanNewDto receivablePlanNewDto = new ReceivablePlanNewDto();
        receivablePlanNewDto.setFkInvoiceNums(invoiceNum);
        String[] times = {"0", "0"};
        List<ReceivablePlanNewVo> receivablePlanNews = receivablePlanService.getReceivablePlanNew(receivablePlanNewDto, new SearchBean<>(), times);
        if (GeneralTool.isEmpty(receivablePlanNews)) {
            return Collections.emptyList();
        }
        return receivablePlanNews;
    }

}
