package com.get.institutioncenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/25 11:40
 */
@Data
public class InstitutionRankingDto {

    @ApiModelProperty(value = "学校名称")
    private String fkInstitutionName;

    @ApiModelProperty(value = "国家id")
    private Long fkCountryId;

    /**
     * 排名类型：QS=0/TIMES=1/THE=2/USNews=3/Macleans=4/ARWU=5/QS专业排名=6
     */
    @ApiModelProperty(value = "排名类型：QS=0/TIMES=1/THE=2/USNews=3/Macleans=4/ARWU=5/QS专业排名=6")
    private Integer rankingType;

    @ApiModelProperty(value = "专业大类名称（中文）")
    private String courseTypeGroupNameChn;

    @ApiModelProperty(value = "专业小类名称")
    private String courseTypeName;




}
