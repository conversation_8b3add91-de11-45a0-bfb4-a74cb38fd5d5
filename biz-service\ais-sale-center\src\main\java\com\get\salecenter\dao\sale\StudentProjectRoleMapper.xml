<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.StudentProjectRoleMapper">
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.StudentProjectRole">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_company_id" jdbcType="BIGINT" property="fkCompanyId"/>
        <result column="role_name" jdbcType="VARCHAR" property="roleName"/>
        <result column="role_key" jdbcType="VARCHAR" property="roleKey"/>
        <result column="department_num" jdbcType="VARCHAR" property="departmentNum"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="view_order" jdbcType="INTEGER" property="viewOrder"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <insert id="insert" parameterType="com.get.salecenter.entity.StudentProjectRole" keyProperty="id"
            useGeneratedKeys="true">
    insert into u_student_project_role (id, fk_company_id, role_name, 
      role_key, department_num, remark, 
      view_order, gmt_create, gmt_create_user, 
      gmt_modified, gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkCompanyId,jdbcType=BIGINT}, #{roleName,jdbcType=VARCHAR}, 
      #{roleKey,jdbcType=VARCHAR}, #{departmentNum,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{viewOrder,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.get.salecenter.entity.StudentProjectRole" keyProperty="id"
            useGeneratedKeys="true">
        insert into u_student_project_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkCompanyId != null">
                fk_company_id,
            </if>
            <if test="roleName != null">
                role_name,
            </if>
            <if test="roleKey != null">
                role_key,
            </if>
            <if test="departmentNum != null">
                department_num,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="viewOrder != null">
                view_order,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkCompanyId != null">
                #{fkCompanyId,jdbcType=BIGINT},
            </if>
            <if test="roleName != null">
                #{roleName,jdbcType=VARCHAR},
            </if>
            <if test="roleKey != null">
                #{roleKey,jdbcType=VARCHAR},
            </if>
            <if test="departmentNum != null">
                #{departmentNum,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="viewOrder != null">
                #{viewOrder,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select
     IFNULL(max(view_order)+1,0) view_order
    from
     u_student_project_role
    where
     fk_company_id = #{fkCompanyId}
    </select>


    <select id="getRoleSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        select id,role_name name from u_student_project_role
        <where>
            <if test="fkCompanyId != null">
                fk_company_id= #{fkCompanyId,jdbcType=BIGINT}
            </if>
        </where>
        order by view_order desc
    </select>
    <select id="getExistsOfferItemAgentList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
        uspr.id,
        uspr.role_name NAME,
        uspr.role_key AS num
        FROM
        m_student_offer AS mso
        INNER JOIN s_student_project_role_staff AS ssprs ON ssprs.fk_table_id = mso.id
        AND ssprs.fk_table_name = 'm_student_offer'
        INNER JOIN u_student_project_role AS uspr ON uspr.id = ssprs.fk_student_project_role_id
        INNER JOIN m_student_offer_item AS msoi ON msoi.fk_student_offer_id = mso.id
        LEFT JOIN ais_sale_center.r_staff_bd_code AS rsbc ON rsbc.fk_staff_id = msoi.fk_staff_id
        where msoi.status = 1
          AND uspr.fk_company_id= #{companyId}
          AND rsbc.bd_code NOT LIKE 'T%'
        AND msoi.fk_area_country_id IN
        <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">
            #{countryId}
        </foreach>
        GROUP BY
        uspr.id
        order by view_order desc
    </select>
    <select id="getRoleCollection" resultType="com.get.salecenter.entity.StudentProjectRole">
        SELECT id,fk_company_id,role_name,role_key,view_order FROM u_student_project_role
        ORDER BY fk_company_id,view_order DESC
    </select>

    <select id="getDepartmentRole" resultType="com.get.salecenter.entity.StudentProjectRole">
        SELECT id,fk_company_id,role_name,role_key,view_order FROM u_student_project_role
        WHERE FIND_IN_SET(#{fkDepartmentId},department_num) and fk_company_id =#{fkCompanyId}
         ORDER BY view_order DESC
    </select>

    <select id="getRoleByKey" resultType="com.get.salecenter.entity.StudentProjectRole">
        SELECT * FROM u_student_project_role WHERE role_key=#{roleKey}
    </select>
    <select id="getRoleByKeys" resultType="com.get.salecenter.vo.StudentProjectRoleVo">
         SELECT * FROM u_student_project_role WHERE role_key IN
         <foreach collection="roleKeys" item="rk" open="(" separator="," close=")">
             #{rk}
         </foreach>
    </select>
    <select id="getRoleStaffByRoleId" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            s.id,
            s.`name` as nameChn,
            CONCAT("【", md.name, "】", if(s.is_on_duty=1,'','【离职】'), s.name_en) as name,
            mc.num
        FROM
            s_student_project_role_staff p
        INNER JOIN m_student_offer f ON p.fk_table_id = f.id
        INNER JOIN u_student_project_role r ON r.id = p.fk_student_project_role_id
        INNER JOIN ais_permission_center.m_staff s ON s.id = p.fk_staff_id
        INNER JOIN ais_permission_center.m_department AS md ON md.id = s.fk_department_id
        INNER JOIN m_student_offer_item AS msoi ON msoi.fk_student_offer_id = f.id
        LEFT JOIN ais_sale_center.r_staff_bd_code AS rsbc ON rsbc.fk_staff_id = msoi.fk_staff_id
        LEFT JOIN ais_permission_center.m_company AS mc ON mc.id = s.fk_company_id
        WHERE
            p.fk_table_name = 'm_student_offer'
          AND rsbc.bd_code NOT LIKE 'T%'
          AND r.role_key like #{projectRoleKey}
        <if test="departmentIdStr != null and departmentIdStr != ''">
            AND s.fk_department_id IN (${departmentIdStr})
        </if>
        <if test="companyIdStr != null and companyIdStr != ''">
            AND mc.id IN (${companyIdStr})
        </if>
        <if test="staffFollowerIds != null and staffFollowerIds.size() > 0">
            AND s.id IN
            <foreach collection="staffFollowerIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY
            s.id
        ORDER BY
            mc.view_order DESC,
            md.view_order DESC,
            s.is_on_duty desc,
            s.is_active DESC,
            s.name
    </select>
    <select id="getRoleStaffDepartmentByRoleId" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            md.id,
            md.name,
            mc.num
        FROM
            s_student_project_role_staff p
        INNER JOIN m_student_offer f ON p.fk_table_id = f.id
        INNER JOIN u_student_project_role r ON r.id = p.fk_student_project_role_id
        INNER JOIN ais_permission_center.m_staff s ON s.id = p.fk_staff_id
        LEFT JOIN ais_permission_center.m_company AS mc ON mc.id = s.fk_company_id
        LEFT JOIN ais_sale_center.r_staff_bd_code AS rsbc ON rsbc.fk_staff_id = f.fk_staff_id
        INNER JOIN ais_permission_center.m_department AS md ON md.id = s.fk_department_id
        INNER JOIN ais_sale_center.m_student_offer_item AS msoi ON msoi.fk_student_offer_id = f.id
        WHERE
            p.fk_table_name = 'm_student_offer'
            AND rsbc.bd_code NOT LIKE 'T%'
            AND r.role_key like #{projectRoleKey}
            AND s.id IN
            <foreach collection="staffFollowerIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
            AND mc.id IN
            <foreach collection="companyIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        GROUP BY
            md.id
        ORDER BY
        mc.view_order desc, md.view_order desc
    </select>
    <select id="getRoleByKeysLike" resultType="com.get.salecenter.entity.StudentProjectRole">
        SELECT * FROM u_student_project_role WHERE 1=1 and
        <foreach collection="roleList" item="rk" open="(" separator="OR" close=")">
            role_key LIKE CONCAT('%', #{rk}, '%')
        </foreach>
    </select>
</mapper>