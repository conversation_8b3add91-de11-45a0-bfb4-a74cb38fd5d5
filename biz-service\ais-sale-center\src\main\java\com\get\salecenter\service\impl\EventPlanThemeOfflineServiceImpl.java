package com.get.salecenter.service.impl;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.EventPlanThemeOfflineMapper;
import com.get.salecenter.vo.EventPlanThemeOfflineVo;
import com.get.salecenter.vo.EventPlanThemeOfflineItemVo;
import com.get.salecenter.entity.EventPlanRegistration;
import com.get.salecenter.entity.EventPlanRegistrationEvent;
import com.get.salecenter.entity.EventPlanTheme;
import com.get.salecenter.entity.EventPlanThemeOffline;
import com.get.salecenter.entity.EventPlanThemeOfflineItem;
import com.get.salecenter.service.EventPlanRegistrationEventService;
import com.get.salecenter.service.EventPlanRegistrationService;
import com.get.salecenter.service.EventPlanThemeOfflineItemService;
import com.get.salecenter.service.EventPlanThemeOfflineService;
import com.get.salecenter.service.EventPlanThemeService;
import com.get.salecenter.dto.EventPlanThemeOfflineDto;
import com.get.salecenter.dto.EventPlanThemeOfflineItemDto;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
@Service
public class EventPlanThemeOfflineServiceImpl extends BaseServiceImpl<EventPlanThemeOfflineMapper, EventPlanThemeOffline> implements EventPlanThemeOfflineService {

    @Resource
    private EventPlanThemeService eventPlanThemeService;

    @Resource
    private EventPlanThemeOfflineMapper eventPlanThemeOfflineMapper;

    @Resource
    private EventPlanThemeOfflineItemService eventPlanThemeOfflineItemService;

    @Resource
    private EventPlanRegistrationEventService eventService;

    @Resource
    private EventPlanRegistrationService eventPlanRegistrationService;

    @Resource
    private UtilService utilService;


    @Override
    public List<EventPlanThemeOfflineVo> getEventPlanThemeOfflines(Long fkEventPlanId) {
        if(GeneralTool.isEmpty(fkEventPlanId)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }

        List<EventPlanThemeOfflineVo> eventPlanThemeOfflineVos = eventPlanThemeOfflineMapper.getEventPlanThemeOfflines(fkEventPlanId, ProjectExtraEnum.EVENT_PLAN_THEME_OFFLINE.key);
        if(GeneralTool.isEmpty(eventPlanThemeOfflineVos)){
            return Collections.emptyList();
        }
        List<Long> offlineIds = eventPlanThemeOfflineVos.stream().map(EventPlanThemeOfflineVo::getId).collect(Collectors.toList());
        //子项目
        List<EventPlanThemeOfflineItem> itemlist = eventPlanThemeOfflineItemService.list(Wrappers.<EventPlanThemeOfflineItem>lambdaQuery()
                .in(EventPlanThemeOfflineItem::getFkEventPlanThemeOfflineId, offlineIds));

        List<EventPlanRegistrationEvent> registrationlist = new ArrayList<>();
        if(GeneralTool.isNotEmpty(itemlist)){
            List<Long> itemIds = itemlist.stream().map(EventPlanThemeOfflineItem::getId).collect(Collectors.toList());
            //报名名册
            List<EventPlanRegistration> registrations = eventPlanRegistrationService.list(Wrappers.<EventPlanRegistration>lambdaQuery()
                    .eq(EventPlanRegistration::getFkEventPlanId, fkEventPlanId));
            if(GeneralTool.isNotEmpty(registrations)){
                Set<Long> fkRegistrationIds = registrations.stream().map(EventPlanRegistration::getId).collect(Collectors.toSet());
                registrationlist = eventService.list(Wrappers.<EventPlanRegistrationEvent>lambdaQuery()
                        .in(EventPlanRegistrationEvent::getFkEventPlanRegistrationId,fkRegistrationIds)
                        .eq(EventPlanRegistrationEvent::getFkTableName, TableEnum.EVENT_PLAN_THEME_OFFLINE_ITEM.key)
                        .in(EventPlanRegistrationEvent::getFkTableId,itemIds));
            }
        }

        for(EventPlanThemeOfflineVo dto : eventPlanThemeOfflineVos){
            if(GeneralTool.isNotEmpty(itemlist)){
                //项目地区合计
                List<EventPlanThemeOfflineItem> offlineItemList = itemlist.stream()
                        .filter(i -> dto.getId().equals(i.getFkEventPlanThemeOfflineId()))
                        .collect(Collectors.toList());
                dto.setItemCount(offlineItemList.size());

                //报名名册合计
                if(GeneralTool.isNotEmpty(registrationlist)){
                    List<EventPlanRegistrationEvent> regList = registrationlist.stream()
                            .filter(r -> offlineItemList.stream().anyMatch(o -> r.getFkTableId().equals(o.getId()))).collect(Collectors.toList());
                    dto.setRegistrationCount(regList.size());
                }
            }
        }


        return eventPlanThemeOfflineVos;
    }

    @Override
    public List<EventPlanThemeOfflineVo> getOfflinesByThemeId(Long fkEventPlanThemeId){
        if(GeneralTool.isEmpty(fkEventPlanThemeId)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }

        EventPlanTheme theme = eventPlanThemeService.getById(fkEventPlanThemeId);

        List<EventPlanThemeOffline> eventPlanThemeOfflines = eventPlanThemeOfflineMapper.selectList(Wrappers.<EventPlanThemeOffline>lambdaQuery()
                .eq(EventPlanThemeOffline::getIsActive, true)
                .eq(EventPlanThemeOffline::getFkEventPlanThemeId, fkEventPlanThemeId)
                .orderByDesc(EventPlanThemeOffline::getViewOrder));
        if(GeneralTool.isEmpty(eventPlanThemeOfflines)){
            return Collections.emptyList();
        }
        List<EventPlanThemeOfflineVo> eventPlanThemeOfflineVos = BeanCopyUtils.copyListProperties(eventPlanThemeOfflines, EventPlanThemeOfflineVo::new);
        eventPlanThemeOfflineVos.forEach(e->e.setMainTitle(theme.getMainTitle()));
        return eventPlanThemeOfflineVos;
    }

    @Override
    public EventPlanThemeOfflineVo findOfflineById(Long id){
        if(GeneralTool.isEmpty(id)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        EventPlanThemeOffline offline = eventPlanThemeOfflineMapper.selectById(id);
        if(GeneralTool.isEmpty(offline)){
            return null;
        }
        EventPlanThemeOfflineVo dto = BeanCopyUtils.objClone(offline, EventPlanThemeOfflineVo::new);
        //子项目
        List<EventPlanThemeOfflineItem> list = eventPlanThemeOfflineItemService.list(Wrappers.<EventPlanThemeOfflineItem>lambdaQuery()
                .eq(EventPlanThemeOfflineItem::getFkEventPlanThemeOfflineId, id)
                .orderByDesc(EventPlanThemeOfflineItem::getViewOrder));
        if(GeneralTool.isNotEmpty(list)){
            List<EventPlanThemeOfflineItemVo> offlineItemDtos = BeanCopyUtils.copyListProperties(list, EventPlanThemeOfflineItemVo::new);
            dto.setOfflineItemList(offlineItemDtos);
        }
        return dto;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAdd(ValidList<EventPlanThemeOfflineDto> offlineVos) {
        if (GeneralTool.isEmpty(offlineVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (EventPlanThemeOfflineDto offlineVo : offlineVos) {
            if (GeneralTool.isEmpty(offlineVo.getId())) {
                EventPlanThemeOffline offline = BeanCopyUtils.objClone(offlineVo, EventPlanThemeOffline::new);
                Integer maxViewOrder = eventPlanThemeOfflineMapper.getMaxViewOrder(offlineVo.getFkEventPlanThemeId());
                maxViewOrder = GeneralTool.isEmpty(maxViewOrder) ? 0 : maxViewOrder;
                offline.setViewOrder(maxViewOrder);
                offline.setAreaCountryName(StringEscapeUtils.unescapeHtml(offline.getAreaCountryName()));
                offline.setDescription(StringEscapeUtils.unescapeHtml(offline.getDescription()));
                utilService.updateUserInfoToEntity(offline);
                eventPlanThemeOfflineMapper.insert(offline);
            } else {
                EventPlanThemeOffline offline = BeanCopyUtils.objClone(offlineVo, EventPlanThemeOffline::new);
                utilService.updateUserInfoToEntity(offline);
                offline.setAreaCountryName(StringEscapeUtils.unescapeHtml(offline.getAreaCountryName()));
                offline.setDescription(StringEscapeUtils.unescapeHtml(offline.getDescription()));
                eventPlanThemeOfflineMapper.updateById(offline);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void activate(EventPlanThemeOfflineDto offlineVo){
        if (GeneralTool.isEmpty(offlineVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        EventPlanThemeOffline offline = BeanCopyUtils.objClone(offlineVo, EventPlanThemeOffline::new);
        utilService.updateUserInfoToEntity(offline);
        eventPlanThemeOfflineMapper.updateById(offline);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addEventPlanThemeOffline(EventPlanThemeOfflineDto offlineVo){
        if (GeneralTool.isEmpty(offlineVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        EventPlanThemeOffline offline = BeanCopyUtils.objClone(offlineVo, EventPlanThemeOffline::new);
        if(GeneralTool.isNotEmpty(offlineVo.getFkAreaCountryIds())){
            offline.setFkAreaCountryIds(StringUtils.join(offlineVo.getFkAreaCountryIds(),","));
        }
        Integer maxViewOrder = eventPlanThemeOfflineMapper.getMaxViewOrder(offlineVo.getFkEventPlanThemeId());
        maxViewOrder = GeneralTool.isEmpty(maxViewOrder) ? 0 : maxViewOrder;
        offline.setViewOrder(maxViewOrder);
        utilService.setCreateInfo(offline);
        offline.setAreaCountryName(StringEscapeUtils.unescapeHtml(offline.getAreaCountryName()));
        offline.setDescription(StringEscapeUtils.unescapeHtml(offline.getDescription()));
        int insert = eventPlanThemeOfflineMapper.insert(offline);
        if(insert <= 0){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        //插入地区
        if(GeneralTool.isNotEmpty(offlineVo.getOfflineItemList())){
            List<EventPlanThemeOfflineItemDto> itemVoList = offlineVo.getOfflineItemList();
            for(EventPlanThemeOfflineItemDto vo:itemVoList){
                EventPlanThemeOfflineItem eventPlanThemeOfflineItem = BeanCopyUtils.objClone(vo, EventPlanThemeOfflineItem::new);
                Integer viewOrder = eventPlanThemeOfflineItemService.getMaxViewOrder(offline.getId());
                viewOrder = GeneralTool.isEmpty(viewOrder) ? 0 : viewOrder;
                eventPlanThemeOfflineItem.setViewOrder(viewOrder);
                eventPlanThemeOfflineItem.setFkEventPlanThemeOfflineId(offline.getId());
                utilService.setCreateInfo(eventPlanThemeOfflineItem);
                eventPlanThemeOfflineItemService.save(eventPlanThemeOfflineItem);
            }

        }
        return offline.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public EventPlanThemeOfflineVo updateOffline(EventPlanThemeOfflineDto offlineVo){
        if (GeneralTool.isEmpty(offlineVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        EventPlanThemeOffline themeoffline = eventPlanThemeOfflineMapper.selectById(offlineVo.getId());
        if (GeneralTool.isEmpty(themeoffline)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }

        EventPlanThemeOffline offline = BeanCopyUtils.objClone(offlineVo, EventPlanThemeOffline::new);
        if(GeneralTool.isNotEmpty(offlineVo.getFkAreaCountryIds())){
            offline.setFkAreaCountryIds(StringUtils.join(offlineVo.getFkAreaCountryIds(),","));
        }
        offline.setAreaCountryName(StringEscapeUtils.unescapeHtml(offline.getAreaCountryName()));
        offline.setDescription(StringEscapeUtils.unescapeHtml(offline.getDescription()));
        utilService.updateUserInfoToEntity(offline);
        int update = eventPlanThemeOfflineMapper.updateById(offline);
        if (update <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }

        //查询原有子项目
        List<EventPlanThemeOfflineItem> itemList = eventPlanThemeOfflineItemService.list(Wrappers.<EventPlanThemeOfflineItem>lambdaQuery()
                .eq(EventPlanThemeOfflineItem::getFkEventPlanThemeOfflineId, offlineVo.getId()));
        if(GeneralTool.isNotEmpty(itemList)){
            List<Long> itemIds = itemList.stream().map(EventPlanThemeOfflineItem::getId).collect(Collectors.toList());

            if(GeneralTool.isNotEmpty(offlineVo.getOfflineItemList())){
                List<Long> itemIds1 = offlineVo.getOfflineItemList().stream().map(EventPlanThemeOfflineItemDto::getId).collect(Collectors.toList());
                itemIds = itemIds.stream().filter(i -> !itemIds1.contains(i)).collect(Collectors.toList());
            }

            if(GeneralTool.isNotEmpty(itemIds)){
                List<EventPlanRegistrationEvent> registrationList = eventService.list(Wrappers.<EventPlanRegistrationEvent>lambdaQuery()
                        .eq(EventPlanRegistrationEvent::getFkTableName, TableEnum.EVENT_PLAN_THEME_OFFLINE_ITEM.key)
                        .in(EventPlanRegistrationEvent::getFkTableId, itemIds));
                if(GeneralTool.isNotEmpty(registrationList)){
                    throw new GetServiceException(LocaleMessageUtils.getMessage("delete_offline_item_fail"));
                }
                //删除
                boolean remove =  eventPlanThemeOfflineItemService.remove(Wrappers.<EventPlanThemeOfflineItem>lambdaQuery()
                        .in(EventPlanThemeOfflineItem::getId, itemIds));
                if (!remove) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
                }
            }
        }


        if(GeneralTool.isNotEmpty(offlineVo.getOfflineItemList())){
            List<EventPlanThemeOfflineItemDto> itemVoList = offlineVo.getOfflineItemList();
            for(EventPlanThemeOfflineItemDto vo:itemVoList){
                if(GeneralTool.isEmpty(vo.getId())){
                    EventPlanThemeOfflineItem eventPlanThemeOfflineItem = BeanCopyUtils.objClone(vo, EventPlanThemeOfflineItem::new);
                    Integer viewOrder = eventPlanThemeOfflineItemService.getMaxViewOrder(offline.getId());
                    viewOrder = GeneralTool.isEmpty(viewOrder) ? 0 : viewOrder;
                    eventPlanThemeOfflineItem.setViewOrder(viewOrder);
                    eventPlanThemeOfflineItem.setFkEventPlanThemeOfflineId(offline.getId());
                    utilService.setCreateInfo(eventPlanThemeOfflineItem);
                    eventPlanThemeOfflineItemService.save(eventPlanThemeOfflineItem);
                }else{
                    EventPlanThemeOfflineItem eventPlanThemeOfflineItem = BeanCopyUtils.objClone(vo, EventPlanThemeOfflineItem::new);
                    utilService.setUpdateInfo(eventPlanThemeOfflineItem);
                    eventPlanThemeOfflineItemService.updateById(eventPlanThemeOfflineItem);
                }

            }
        }
        return findOfflineById(offlineVo.getId());
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        EventPlanThemeOffline offline = eventPlanThemeOfflineMapper.selectById(id);
        if (GeneralTool.isEmpty(offline)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //存在子项不能删除
        List<EventPlanThemeOfflineItem> itemList = eventPlanThemeOfflineItemService.list(Wrappers.<EventPlanThemeOfflineItem>lambdaQuery()
                .eq(EventPlanThemeOfflineItem::getFkEventPlanThemeOfflineId, id)
                .eq(EventPlanThemeOfflineItem::getIsActive, true));

        if(GeneralTool.isNotEmpty(itemList)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("has_associated"));
        }

        int delete = eventPlanThemeOfflineMapper.deleteById(id);
        if (delete <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void movingOrder(Long fkEventPlanThemeId,Integer start,Integer end) {
        LambdaQueryWrapper<EventPlanThemeOffline> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (end > start){
            lambdaQueryWrapper.between(EventPlanThemeOffline::getViewOrder,start,end).orderByDesc(EventPlanThemeOffline::getViewOrder);
        }else {
            lambdaQueryWrapper.between(EventPlanThemeOffline::getViewOrder,end,start).orderByDesc(EventPlanThemeOffline::getViewOrder);

        }
        lambdaQueryWrapper.eq(EventPlanThemeOffline::getFkEventPlanThemeId,fkEventPlanThemeId);
        List<EventPlanThemeOffline> eventPlanThemeOfflines = list(lambdaQueryWrapper);

        //从下上移 列表倒序排序
        List<EventPlanThemeOffline> updateList = Lists.newArrayList();
        if (end>start){
            int finalEnd = end;
            List<EventPlanThemeOffline> sortedList = Lists.newArrayList();
            EventPlanThemeOffline offline = eventPlanThemeOfflines.get(eventPlanThemeOfflines.size() - 1);
            sortedList.add(offline);
            eventPlanThemeOfflines.remove(eventPlanThemeOfflines.size() - 1);
            sortedList.addAll(eventPlanThemeOfflines);
            for (EventPlanThemeOffline themeOffline : sortedList) {
                themeOffline.setViewOrder(finalEnd);
                finalEnd--;
            }
            updateList.addAll(sortedList);
        }else {
            int finalStart = start;
            List<EventPlanThemeOffline> sortedList = Lists.newArrayList();
            EventPlanThemeOffline online = eventPlanThemeOfflines.get(0);
            eventPlanThemeOfflines.remove(0);
            sortedList.addAll(eventPlanThemeOfflines);
            sortedList.add(online);
            for (EventPlanThemeOffline themeOffline : sortedList) {
                themeOffline.setViewOrder(finalStart);
                finalStart--;
            }
            updateList.addAll(sortedList);
        }

        if (GeneralTool.isNotEmpty(updateList)){
            boolean batch = updateBatchById(updateList);
            if (!batch){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
    }
}
