package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.vo.ExchangeRateVo;
import com.get.financecenter.entity.ExchangeRate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Mapper
public interface ExchangeRateMapper extends BaseMapper<ExchangeRate> {
    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(ExchangeRate record);


    /**
     * @return java.math.BigDecimal
     * @Description: 获取汇率
     * @Param [fromCurrency, toCurrency]
     * <AUTHOR>
     */
    BigDecimal getRateByCurrency(@Param("fromCurrency") String fromCurrency, @Param("toCurrency") String toCurrency);


    /**
     * 获取当天汇率
     *
     * @param date
     * @return
     */
    ExchangeRate getRateByCurrencyByGetDate(@Param("fromCurrency") String fromCurrency, @Param("toCurrency") String toCurrency, @Param("date") Date date);

    List<ExchangeRateVo> getLastExchangeRateHkd(@Param("toCurrencys") Set<String> toCurrencys, @Param("currencyNum") String currencyNum);
}