package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.BdSelectVo;
import com.get.salecenter.vo.StaffBdCodeVo;
import com.get.salecenter.service.IStaffBdCodeService;
import com.get.salecenter.dto.StaffBdCodeDto;
import com.get.salecenter.dto.StaffBdCompanyDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2021/1/20 17:48
 * @verison: 1.0
 * @description:
 */
@Api(tags = "BD团队配置管理")
@RestController
@RequestMapping("sale/staffBdCode")
public class StaffBdCodeController {
    @Resource
    private IStaffBdCodeService staffBdCodeService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/BD团队配置管理/BD团队配置详情")
    @GetMapping("/{id}")
    public ResponseBo<StaffBdCodeVo> detail(@PathVariable("id") Long id) {
        StaffBdCodeVo data = staffBdCodeService.findStaffBdCodeById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 批量新增信息
     *
     * @param staffBdCodeDtos
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/BD团队配置管理/新增BD团队配置")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody  @Validated(StaffBdCodeDto.Add.class) ValidList<StaffBdCodeDto> staffBdCodeDtos) {
        staffBdCodeService.batchAdd(staffBdCodeDtos);
        return SaveResponseBo.ok();
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/BD团队配置管理/删除BD团队配置")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        staffBdCodeService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param staffBdCodeDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/BD团队配置管理/更新BD团队配置")
    @PostMapping("update")
    public ResponseBo<StaffBdCodeVo> update(@RequestBody  @Validated(StaffBdCodeDto.Update.class) StaffBdCodeDto staffBdCodeDto) {
        return UpdateResponseBo.ok(staffBdCodeService.updateStaffBdCode(staffBdCodeDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/BD团队配置管理/查询BD团队配置")
    @PostMapping("datas")
    public ResponseBo<StaffBdCodeVo> datas(@RequestBody SearchBean<StaffBdCodeDto> page) {
        List<StaffBdCodeVo> datas = staffBdCodeService.getStaffBdCodes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 根据公司id获取bd员工下拉框数据
     *
     * @Date 16:27 2021/7/6
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据公司id获取bd员工下拉框数据", notes = "")
    @GetMapping("getStaffByBdCompanyIds/{companyId}")
    public ResponseBo<BaseSelectEntity> getStaffByBdCompanyIds(@PathVariable("companyId") Long companyId) {
        List<BaseSelectEntity> datas = staffBdCodeService.getStaffByBdCompanyIds(companyId);
        return new ListResponseBo<>(datas);
    }

    /**
     * 根据公司id获取bd员工下拉框数据
     *
     * @Date 16:27 2021/7/6
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "根据公司id获取bd员工下拉框数据", notes = "")
    @GetMapping("getBdSelectByCompanyId/{companyId}")
    public ResponseBo<BdSelectVo> getBdSelectByCompanyId(@PathVariable("companyId") Long companyId) {
        List<BdSelectVo> datas = staffBdCodeService.getBdSelectByCompanyId(companyId);
        return new ListResponseBo<>(datas);
    }

    /**
     * 根据公司id获取bd员工下拉框数据（用于代理在线申请表单）
     * @param companyId
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "根据公司id获取bd员工下拉框数据（用于代理在线申请表单）", notes = "")
    @GetMapping("getBdSelectAgentOnlineForm/{companyId}")
    public ResponseBo<BdSelectVo> getBdSelectAgentOnlineForm(@PathVariable("companyId") Long companyId) {
        List<BdSelectVo> datas = staffBdCodeService.getBdSelectAgentOnlineForm(companyId);
        return new ListResponseBo<>(datas);
    }

    /**
     * 根据公司ids,bd名称，编号获取bd员工下拉框数据(百度式搜索)
     *
     * @Date 16:27 2021/7/6
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "bd员工下拉框数据(百度式搜索)", notes = "")
    @PostMapping("getStaffByBdCompanyIds")
    public ResponseBo<BaseSelectEntity> getStaffByBdName(@RequestBody StaffBdCompanyDto staffBdCompanyVo) {
        List<BaseSelectEntity> datas = staffBdCodeService.getStaffByBdName(staffBdCompanyVo.getCompanyIds(),staffBdCompanyVo.getBdName());
        return new ListResponseBo<>(datas);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "bd员工下拉框数据", notes = "")
    @PostMapping("getStaffByCompanyIds")
    public ResponseBo<BaseSelectEntity> getStaffByCompanyIds(@RequestBody StaffBdCompanyDto staffBdCompanyVo) {
        List<BaseSelectEntity> datas = staffBdCodeService.getStaff(staffBdCompanyVo.getCompanyIds());
        return new ListResponseBo<>(datas);
    }


    /**
     * @Description: 获取BD团队配置IAE下的所有大区ids
     * @Author: Jerry
     * @Date:12:13 2021/9/1
     */
    @ApiIgnore
    @GetMapping("getAreaRegionIdsByCompanyId")
    public Set<Long> getAreaRegionIdsByCompanyId() {
        return staffBdCodeService.getAreaRegionIdsByCompanyId();
    }
}
