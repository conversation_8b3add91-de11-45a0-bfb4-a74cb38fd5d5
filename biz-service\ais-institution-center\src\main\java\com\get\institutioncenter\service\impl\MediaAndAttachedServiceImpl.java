package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.institutioncenter.dao.MediaAndAttachedMapper;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.entity.MediaAndAttached;
import com.get.institutioncenter.service.IMediaAndAttachedService;
import com.get.institutioncenter.dto.MediaAndAttachedQueryDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/8/6
 * @TIME: 9:53
 * @Description:
 **/
@Service
public class MediaAndAttachedServiceImpl extends BaseServiceImpl<MediaAndAttachedMapper, MediaAndAttached> implements IMediaAndAttachedService {
    @Resource
    private UtilService utilService;
    @Resource
    private MediaAndAttachedMapper attachedMapper;
    @Resource
    private IFileCenterClient fileCenterClient;


    @Override
    public List<FileDto> upload(MultipartFile[] multipartFiles) {
        if (GeneralTool.isEmpty(multipartFiles)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }

        List<FileDto> fileDtos = null;
        Result<List<FileDto>> result = fileCenterClient.upload(multipartFiles, LoggerModulesConsts.INSTITUTIONCENTER);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        fileDtos = result.getData();
        return fileDtos;
    }

    /**
     * 上传附件
     * @param multipartFiles
     * @return
     */
    @Override
    public List<FileDto> uploadAttached(MultipartFile[] multipartFiles) {
        if (GeneralTool.isEmpty(multipartFiles)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }
        List<FileDto> fileDtos = null;
        Result<List<FileDto>> result = fileCenterClient.uploadAppendix(multipartFiles, LoggerModulesConsts.INSTITUTIONCENTER);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }

        fileDtos = result.getData();
        return fileDtos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMediaAttached(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        MediaAndAttached mediaAndAttached = attachedMapper.selectById(id);
        if (GeneralTool.isEmpty(mediaAndAttached)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        int i = attachedMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }else
        {
            Result<Boolean> result = fileCenterClient.delete(mediaAndAttached.getFkFileGuid(), LoggerModulesConsts.INSTITUTIONCENTER);
            if (!result.isSuccess()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        MediaAndAttached ro = BeanCopyUtils.objClone(mediaAttachedVos.get(0), MediaAndAttached::new);
        Integer oneorder = ro.getIndexKey();
        MediaAndAttached rt = BeanCopyUtils.objClone(mediaAttachedVos.get(1), MediaAndAttached::new);
        Integer twoorder = rt.getIndexKey();
        ro.setIndexKey(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setIndexKey(oneorder);
        utilService.updateUserInfoToEntity(rt);
        attachedMapper.updateById(ro);
        attachedMapper.updateById(rt);
    }

    @Override
    public MediaAndAttachedVo addMediaAndAttached(MediaAndAttachedDto mediaAttachedVo) {
        if (GeneralTool.isEmpty(mediaAttachedVo.getFkTableName())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("tableName_null"));
        }
        MediaAndAttached andAttached = BeanCopyUtils.objClone(mediaAttachedVo, MediaAndAttached::new);
        Integer nextIndexKey = attachedMapper.getNextIndexKey(andAttached.getFkTableId(), mediaAttachedVo.getFkTableName());
        nextIndexKey = GeneralTool.isNotEmpty(nextIndexKey) ? nextIndexKey : 0;
        //实例化对象
        andAttached.setIndexKey(nextIndexKey);
        utilService.updateUserInfoToEntity(andAttached);
        attachedMapper.insert(andAttached);
        MediaAndAttachedVo mediaAndAttachedVo = BeanCopyUtils.objClone(andAttached, MediaAndAttachedVo::new);
        mediaAndAttachedVo.setFilePath(mediaAttachedVo.getFilePath());
        mediaAndAttachedVo.setFileNameOrc(mediaAttachedVo.getFileNameOrc());
        mediaAndAttachedVo.setTypeValue(FileTypeEnum.getValue(mediaAttachedVo.getTypeKey()));
        mediaAndAttachedVo.setId(andAttached.getId());
        mediaAndAttachedVo.setFileKey(mediaAttachedVo.getFileKey());
        return mediaAndAttachedVo;
    }

    @Override
    public List<MediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo) {
        if (GeneralTool.isEmpty(attachedVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        LambdaQueryWrapper<MediaAndAttached> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(attachedVo.getTypeKey())) {
            wrapper.eq(MediaAndAttached::getTypeKey, attachedVo.getTypeKey());
        }
        wrapper.eq(MediaAndAttached::getFkTableName, attachedVo.getFkTableName());
        wrapper.eq(MediaAndAttached::getFkTableId, attachedVo.getFkTableId());
        List<MediaAndAttached> mediaAndAttacheds = attachedMapper.selectList(wrapper);
        List<MediaAndAttachedVo> fileMedia = getFileMedia(mediaAndAttacheds);
        return fileMedia;
    }

    @Override
    public Map<Long, MediaAndAttachedVo> getMediaAndAttachedDtos(MediaAndAttachedQueryDto queryVo) {
        if (GeneralTool.isEmpty(queryVo)) {
            return Collections.emptyMap();
        }
        if (queryVo.getIds().isEmpty()) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<MediaAndAttached> wrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(queryVo.getTypeKey())) {
            wrapper.eq(MediaAndAttached::getTypeKey, queryVo.getTypeKey());
        }
        wrapper.eq(MediaAndAttached::getFkTableName, queryVo.getTableName());
        wrapper.in(MediaAndAttached::getFkTableId, queryVo.getIds());
        List<MediaAndAttached> mediaAndAttacheds = attachedMapper.selectList(wrapper);
        List<MediaAndAttachedVo> fileMedia = getFileMedia(mediaAndAttacheds);
        if (GeneralTool.isEmpty(fileMedia)) {
            return Collections.emptyMap();
        }
        return fileMedia.stream().filter(Objects::nonNull).collect(Collectors.toMap(MediaAndAttachedVo::getFkTableId, Function.identity()));
    }

    @Override
    public List<MediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo, Page page) {
        List<MediaAndAttached> mediaAndAttacheds = getMediaAndAttacheds(page, attachedVo);
        return getFileMedia(mediaAndAttacheds);
    }

    /**
     * 获取媒体附件
     *
     * @param attachedVo
     * @return
     */
    private List<MediaAndAttached> getMediaAndAttacheds(Page page, MediaAndAttachedDto attachedVo) {
        if (GeneralTool.isEmpty(attachedVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        LambdaQueryWrapper<MediaAndAttached> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(attachedVo.getTypeKey())) {
            wrapper.eq(MediaAndAttached::getTypeKey, attachedVo.getTypeKey());
        }
        wrapper.eq(MediaAndAttached::getFkTableName, attachedVo.getFkTableName());
        wrapper.eq(MediaAndAttached::getFkTableId, attachedVo.getFkTableId());
        wrapper.orderByDesc(MediaAndAttached::getIndexKey);
        IPage<MediaAndAttached> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<MediaAndAttached> list = pages.getRecords();
        page.setAll((int) pages.getTotal());
        return list;
    }

    @Override
    public void updateTableId(Long id, Long tableId) {
        MediaAndAttached mediaAndAttached = new MediaAndAttached();
        mediaAndAttached.setFkTableId(tableId);
        mediaAndAttached.setId(id);
        attachedMapper.updateById(mediaAndAttached);
    }

    @Override
    public List<MediaAndAttached> findMediaAndAttachedByTableId(Long tableId, String tableName, String key) {
        LambdaQueryWrapper<MediaAndAttached> wrapper = new LambdaQueryWrapper();
        wrapper.eq(MediaAndAttached::getFkTableName, tableName);
        wrapper.eq(MediaAndAttached::getFkTableId, tableId);
        if (GeneralTool.isNotEmpty(key)) {
            wrapper.eq(MediaAndAttached::getTypeKey, key);
        }
        List<MediaAndAttached> mediaAndAttacheds = attachedMapper.selectList(wrapper);

        return mediaAndAttacheds;
    }

    @Override
    public void deleteMediaAndAttachedByTableId(Long tableId, String tableName) {
        LambdaQueryWrapper<MediaAndAttached> wrapper = new LambdaQueryWrapper();
        wrapper.eq(MediaAndAttached::getFkTableName, tableName);
        wrapper.eq(MediaAndAttached::getFkTableId, tableId);
        attachedMapper.delete(wrapper);
    }

    @Override
    public List<Map<String, Object>> findMediaAndAttachedType() {
        return FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.INSTITUTION);
    }

    @Override
    public List<Map<String, Object>> getProviderMediaType() {
        return FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.INSTITUTIONPROVIDER);
    }

    @Override
    public List<Map<String, Object>> getContractMediaType() {
        return FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.INSTITUTIONCONTRACT);
    }

    @Override
    public Map<Long, List<MediaAndAttachedVo>> getMediaAndAttachedDtoByTableIds(Set<Long> tableIds, String tableName) {
        Map<Long, List<MediaAndAttachedVo>> map = new HashMap<>();

        LambdaQueryWrapper<MediaAndAttached> wrapper = new LambdaQueryWrapper();
        wrapper.eq(MediaAndAttached::getFkTableName, tableName);
        wrapper.in(MediaAndAttached::getFkTableId, tableIds);
        List<MediaAndAttached> mediaAndAttacheds = attachedMapper.selectList(wrapper);
        List<MediaAndAttachedVo> fileMedia = getFileMedia(mediaAndAttacheds);
        if (GeneralTool.isNotEmpty(fileMedia)){
            map = fileMedia.stream().collect(Collectors.groupingBy(MediaAndAttachedVo::getFkTableId));
        }
        return map;
    }

    private List<MediaAndAttachedVo> getFileMedia(List<MediaAndAttached> mediaAndAttachedList) {
        if (GeneralTool.isEmpty(mediaAndAttachedList)) {
            return Collections.emptyList();
        }
        //获取guid集合
        List<String> guidList = mediaAndAttachedList.stream().map(MediaAndAttached::getFkFileGuid).collect(Collectors.toList());
        //转换成DTO
        List<MediaAndAttachedVo> mediaAndAttachedVos = mediaAndAttachedList.stream()
                .map(mediaAndAttached -> BeanCopyUtils.objClone(mediaAndAttached, MediaAndAttachedVo::new)).collect(Collectors.toList());
        //根据GUID服务调用查询
        //111111
        Map<String, List<String>> guidListWithTypeMap = new HashMap<>();
        guidListWithTypeMap.put(LoggerModulesConsts.INSTITUTIONCENTER, guidList);
        Result<List<FileDto>> result = fileCenterClient.findFileByGuid(guidListWithTypeMap);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        List<FileDto> fileDtos = result.getData();
        if (fileDtos.isEmpty() || mediaAndAttachedVos.isEmpty()) {
            return Collections.emptyList();
        }
        //返回结果不为空时
        List<MediaAndAttachedVo> collect;
        //遍历查询GUID是否一致
        collect = mediaAndAttachedVos.stream().map(mediaAndAttachedDto -> fileDtos
                .stream()
                .filter(fileDto -> fileDto.getFileGuid().equals(mediaAndAttachedDto.getFkFileGuid()))
                .findFirst()
                .map(fileDto -> {
                    mediaAndAttachedDto.setFilePath(fileDto.getFilePath());
                    mediaAndAttachedDto.setFileNameOrc(fileDto.getFileNameOrc());
                    mediaAndAttachedDto.setFileKey(fileDto.getFileKey());
                    mediaAndAttachedDto.setTypeValue(FileTypeEnum.getValue(mediaAndAttachedDto.getTypeKey()));
                    //*mediaAndAttachedDto.setFkTableName(null);*//*
                    return mediaAndAttachedDto;
                }).orElse(null)
        ).collect(Collectors.toList());

        return collect;
    }

    /**
     * @ Description : 根据表明和ids获取guid
     * @ Param
     * @ return
     * @ author LEO
     */
    public List<MediaAndAttachedVo>getGuIdByIds(Set<Long> ids, String tableName, String typeKey){
        LambdaQueryWrapper<MediaAndAttached>lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MediaAndAttached::getFkTableName, tableName).eq(MediaAndAttached::getTypeKey,typeKey).in(MediaAndAttached::getFkTableId,
                ids);
        List<MediaAndAttached> mediaAndAttacheds = attachedMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(mediaAndAttacheds)){
            return BeanCopyUtils.copyListProperties(mediaAndAttacheds, MediaAndAttachedVo::new);
        }
        return null;
    }

}
