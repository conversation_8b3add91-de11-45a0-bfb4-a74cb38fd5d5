package com.get.common.eunms;

import com.get.common.utils.LocaleMessageUtils;
import com.get.core.tool.utils.GeneralTool;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/12/2
 * @TIME: 16:39
 * @Description:
 **/
public enum ProjectExtraEnum {

    /**
     * 学校中心
     */
    //条件类型 多个枚举复用 不可随意修改key值
    TRANSFER_AGENT_STUDENT(0, "转代理学生"),
//    PERCENTAGE_OF_SCHOLARSHIP_FEES(1, "学生获得奖学金占学费的60%以上"),

    //公式类型中文
    ROUTINE(0, "常规"),
    AWARD(1, "奖励"),
    ONE_AWARD(2, "一次性奖励"),
    SPECIAL(3, "特殊（优先匹配）"),
    //类型组别分类模式
    INTERLLIGENT_CALSSIFCATION(1,"MSO智选分类"),
    PROFESSIONAL_CLASSIFCATION(2,"MSO国内专业大类"),
    OFFER_ITEM_CLASSIFCATION(3,"BMS申请计划专业分类"),
    MSO_SIMPLE_CLASSIFCATION(4,"MSO简易分类"),
    MSO_MODULE_CLASSIFCATION(5,"MSO模块分类"),
    //统计类型中文
    NUMBER_OF_STUDENTS(0, "累计学生数"),
    FEE_OF_STUDENTS(1, "学费"),
    COURSE_DURATION_WEEK(2, "课程长度(周)"),
    COURSE_DURATION_MONTH(3, "课程长度(月)"),
    COURSE_DURATION_YEAR(4, "课程长度(年)"),
    ACCUMULATED_TUITION(5, "累计学费"),
    COURSE_DURATION_SEMESTER(6, "课程长度(学期)"),
    //学习申请计划 课程长度下拉框枚举
    STUDENT_OFFER_ITEM_COURSE_DURATION_WEEK(0, "课程长度(周)"),
    STUDENT_OFFER_ITEM_COURSE_DURATION_MONTH(1, "课程长度(月)"),
    STUDENT_OFFER_ITEM_COURSE_DURATION_YEAR(2, "课程长度(年)"),
    STUDENT_OFFER_ITEM_COURSE_DURATION_SEMESTER(3, "课程长度(学期)"),
    //学生计划新申请状态
    LACK_OF_INFORMATION(0,"缺资料"),
    NOT_OPEN(1,"未开放"),
    NOT_MARKED(-1,"无标记(空值)"),
    //公开对象中文
    NOT_PUBLIC(0, "不公开"),
    PUBLIC(1, "公开"),
    PUBLIC_STUDENTS(2, "学生"),
    PUBLIC_AGENT(3, "代理"),
    //PUBLIC_MSO_CN(4, "MSO_中国内地"),
    //PUBLIC_MSO_HK(5, "MSO_香港"),
    //PUBLIC_MSO_TW(6, "MSO_台湾"),
    //PUBLIC_HKISO(13, "HKISO"),
    //PUBLIC_IBS(8, "IBS"),
    PUBLIC_CURRENCY_COMMON(7, "常用币种"),
    //PUBLIC_ISSUE(9, "ISSUE"),
    PUBLIC_COUNTRY_HOME(10, "国家_首页"),
    PUBLIC_COUNTRY_COMMON(11, "国家_常用"),
    //PUBLIC_CURRENCY_ACC(12, "留学小屋"),
    PUBLIC_PARTNER(13, "华通伙伴"),
    PUBLIC_PMP(14, "PMP"),

    //英语成绩类型学术
    IELTS(1, "IELTS"),
    TOEFL_IBT(2, "TOEFL-IBT"),
    TOEFL_PBT(3, "TOEFL-PBT"),
    PTE(4, "PTE"),
    HKDSE_ENG(5, "HKDSE-ENG"),
    CET(6, "CET"),
    DUOLINGO(7, "DUOLINGO"),
    BEC(8, "BEC"),
    LANGUAGE_CERT(9,"LanguageCert"),
    SCHOOL_INTERNAL_TEST(10, "UniversityTest"),
    //高中成绩枚举部分
    NCEE(1, "National College Entrance Exam (USA)"),
    HKDSE(2, "HKDSE (Hong Kong)"),
    ALEVEL(3, "GCE A Level"),
    IB(4, "IB Diploma"),
    MSS(5, "Middle School Score"),
    UG(6, "UG"),
    OLEVEL(101, "GCE O Level"),
    OSSD(102, "Ontario Secondary School Diploma"),
    NCUK(103, "NCUK"),
    ATAR(104, "Australian Tertiary Amission Rank"),
    GAOKAO(105, "GaoKao (China)"),
    SAT1(106, "SAT1 (USA)"),
    SAT2(118, "SAT2 (USA)"),
    ACT(119, "ACT (USA)"),
    AD(107, "Advanced Diploma (Sinfgapore)"),
    STPM(108, "STPM (Malaysia)"),
    MUFY(109, "Monash University Foundation year (Malaysia)"),
    PM(110, "Program Matrikulasi (Malaysia)"),
    UEC(111, "UEC (Malaysia)"),
    ISCE(112, "Indian School Certificate Examination (Malaysia)"),
    DS(113, "Diploma Studies (Mapaysia)"),
    SPM(120,"SPM(Mapaysia)"),
    SMA3(114, "SMA3 (Indonesia)"),
    SMA2(115, "SMA2 (Indonesia)"),
    MATAYOM6(116, "Matayom 6 (Thailand)"),
    NE(117, "National Exam (Vietnam)"),
    AEAS(121, "Australian Education Assessment Services"),
    QCE(122, "Queensland Certificate of Education"),
    QTAC(123, "Queensland Tertiary Admissions Centre"),
    SACE(124, "South Australian Certificate of Education"),
    GCE(125, "GCE"),
    GCSE(126, "GCSE"),
    IGCSE(127, "IGCSE"),
    HKAL(128,"HK A LEVEL (Hong Kong)"),
    HKAS(129,"HK AS LEVEL (Hong Kong)"),
    HKCEE(130,"HKCEE (Hong Kong)"),
    NCAE(131,"NCAE (China)"),
    AP(132,"Advanced Placement"),

    //标准成绩类型本科
    CLASS(11, "Class"),
    GPA(21, "GPA"),
    GPA4(12, "GPA(out of 4 points)"),
    GPA4_2(13, "GPA(out of 4.2)"),
    GPA4_3(23, "GPA(out of 4.3)"),
    GPA4_5(14, "GPA(out of 4.5)"),
    GPA5(15, "GPA(out of 5 points)"),
    GPA7(16, "GPA(out of 7 points)"),
    GPA9(17, "GPA(out of 9 points)"),
    GPA10(22, "GPA(out of 10 points)"),
    GPA12(26, "GPA(out of 12 points)"),
    PERCENTAGE(18, "Percentage"),
    GRADING(19, "Grading"),
    GU(27, "GU"),
    GRE(24, "GRE"),
    GMAT(25, "GMAT"),
    OTHER_REQUIREMENTS(20, "other requirements"),

    //身份证正面反面
    FRONT(0,"front"),
    BACK(1,"back"),

    //代理申请状态
    APP_STATUS_NEW(0,"待审核"),
    APP_STATUS_REVIEW(1,"审核中"),
    APP_STATUS_AGREE(2,"已通过"),
    APP_STATUS_REJECT(3,"已拒绝"),


    //其他要求
    AGE(51, "Age"),
    WORK_EXP(52, "Work Experience"),
    ACCEPT_MAJOR_TRANSFER(53, "Accept major transfer, recommended major background"),
    //系统配置类型中文
    SYSTEM_CONFIGURATION(1, "系统配置"),
    BUSINESS_CONFIGURATION(2, "业务配置"),
    //学校特性类型中文
    WORLD_RANKINGS_QS(1, "QS世界排名"),
    WORLD_RANKINGS_TIMES(11, "TIMES世界排名"),
    ASIA_RANKINGS_QS(12, "QS亚洲排名"),
    ASIA_RANKINGS_TIMES(13, "TIMES亚洲排名"),
    INTERNATIONAL_RANKINGS_QS(14, "QS最国际化排名"),
    INTERNATIONAL_RANKINGS_TIMES(15, "TIMES最国际化排名"),
    COUNTRY_RANKINGS(2, "国家排名"),
    CUG_RANKINGS(3, "CUG排名"),
    REF_RANKINGS(4, "REF排名"),
    FACULTY_STUDENT_RATIO(5, "师生比例"),
    SEX_RATIO(6, "男女比例"),
    PERCENTAGE_OF_INTERNATIONAL_STUDENTS(7, "国际学生百分比"),
    PERCENTAGE_OF_CHINESE_STUDENTS(8, "中国学生百分比"),
    STUDENTS_NUM(9, "学生人数"),
    VIDEO_WEBSITE_01(10, "视频网址"),
    ACCEPTANCE_RATE(16, "录取率"),
    ENROLLMENT(17, "入学人数"),
    RELIGION(18, "宗教"),
    TEL(19, "电话"),
    FAX(20, "传真"),

    CLOTHING_EXPENSES(21, "服装费用"),
    CATERING_EXPENSES(22, "餐饮费用"),
    ACCOMMODATION_EXPENSES(23, "住宿费用"),
    TRANSPORTATION_EXPENSES(24, "交通费用"),
    TEACHING_LANGUAGE(25, "教学语言"),
    SET_UP_NATURE(26, "成立性质"),
    EMAIL(27, "学校邮箱"),
    SPECIFIC(28, "学校特色"),
    LIVING_EXPENSES(29, "生活费用"),
    INFO_SCHOOL_ADVANTAGE(100, "学校优势"),
    INFO_IB_AP_OSSD_AL(101, "分制"),
    INFO_CURRICULUM_JUNIOR(102, "学制_初级"),
    INFO_CURRICULUM_SENIOR(103, "学制_高级"),
    INFO_CHINESE_FONT(104, "中文使用字体"),
    INFO_TUITION_FEE_HIGHEST(105, "最高学费"),
    INFO_CLASS_STRUCTURE(106, "班级结构"),
    INFO_GENDER(107, "男校女校"),
    INFO_IB_AVERAGE(108, "平均IB"),
    INFO_IB_40(109, "IB 40+%"),
    INFO_AP_AVERAGE(110, "平均AP"),
    INFO_AP_4(111, "AP 4+%"),
    INFO_A_A(112, "A*/A%"),
    INFO_A_B(113, "A*/B%"),
    INFO_TOP_20_RATE(114, "前20U录取率"),
    INFO_APPLICATION_DEADLINE(115, "报名截止日期"),
    INFO_ONLINE_APPLICATION(116, "在线申请"),
    INFO_APPLICATION_FEE(117, "报名费"),
    INFO_APPLICATION_RENEWAL_FEE(118, "申请更新费"),
    INFO_ASSESSMENT_FEE(119, "评估费"),
    INFO_CAPITAL_LEVY(120, "资本税"),
    INFO_DEPRECIATING_DEBENTURES(121, "折旧债券"),
    INFO_DEPRECIATED_BY(122, "折旧"),


    //信息枚举
    CAREER_PATH(1, "职业规划"),
    LIVING_EXPENSES_STUDYING_ABROAD(2, "留学生活费"),
    TOTLE_EXPENSES_STUDYING_ABROAD(3, "留学总费用"),
    LIFE_SAFETY(4, "生活安全指数"),

    //课程排名中文
    COUNTRY_RANKING(0, "国家排名"),
    WORLD_RANKING(1, "世界排名"),
    //续签，新签
    RENEW(1, "续签"),
    NEWSIGNING(0, "新签"),
    //课程条件升读要求
    ACADEMIC_REQ(1, "升读学术要求"),
    ENGLISH_REQ(2, "升读英语要求"),
    SOPHOMORE_ACADEMIC_REQ(3, "升读大二学术要求"),
    SOPHOMORE_ENGLISH_REQ(4, "升读大二英语要求"),
    SPECIFIC_SUBJECT_REQ(5, "升读特定科目要求"),
    //我的审批和我的申请
    MYAPPROVAL(2, "我的审批"),
    MYAPPLICATION(0, "我的申请"),

    //帮助，显示类型
    DIRECT_DISPLAY(0, "直接显示"),
    SMALL_LAYER_PLAIN_TEXT_DISPLAY(1, "小图层纯文本显示"),
    LARGE_LAYER_RICH_TEXT_DISPLAY(2, "大图层富文本显示"),

    //学校排名枚举
    RANKINGS_QS(0,"QS排名"),
    RANKINGS_TIMES(1,"TIMESS排名"),
    RANKINGS_USNEWS(3,"USNEWS排名"),
    RANKINGS_MACLEANS(4,"Macleans排名"),
    RANKINGS_ARWU(5,"ARWU排名"),
    RANKINGS_GUARDIAN(6,"GUARDIAN英国卫报排名"),
    RANKINGS_PROQS(10,"QS专业排名"),

    //KPI推荐等级
    SMALL_RECOMMENDATION(1, "小推荐"),
    MEDIUM_RECOMMENDATION(2, "中推荐"),
    BIG_RECOMMENDATION(3, "大推荐"),

    //学习计划业务状态 多个枚举复用 不可随意修改key值
    GET_THE_FIRST_STAGE_COMMISSION(0, "获得第一阶段佣金"),
    GET_THE_SECOND_STAGE_COMMISSION(1, "获得第二阶段佣金"),
    GET_THE_THIRD_STAGE_COMMISSION(2, "获得第三阶段佣金"),
    GET_THE_FOURTH_STAGE_COMMISSION(3, "获得第四阶段佣金"),
    READ_ONLY_PHASE_ONE(4, "只读第一阶段"),
    READ_ONLY_PHASE_TWO(5, "只读第二阶段"),
    READ_ONLY_PHASE_THREE(6, "只读第三阶段"),
    READ_ONLY_PHASE_FOUR(7, "只读第四阶段"),

    //数据等级
    IMPORT_FROM_OLD_DATA(0, "旧数据导入"),
    MANUAL_INPUT(6, "手动录入"),
    IMPORT_FROM_BUSINESS_DATA(4, "业务数据导入"),
    IMPORT_FROM_OFFICIAL_DATA(1, "官方数据导入"),
    IMPORT_FROM_OFFICIAL_DATA2(5, "官方数据导入2"),
    NEED_TO_ADD(2, "需要补充"),
    COMPLETE_COLLECTION(3, "采集完整"),

    //参会人员类型
    //0校方/1代理/2嘉宾/3员工/4工作人员
    INSTITUTION_AGENT(0, "校代"),
    AGENT(1, "代理"),
    GUEST(2, "嘉宾"),
    STAFF(3, "员工"),
    WORKING_PERSON(4, "工作人员")   ,

    //学生信息状态：0新建/1已提交/2需补件/3加申院校
    NEW_CONSTRUCTION(0, "新建"),
    ALREADY_SUBMITTED(1, "已提交"),
    NEED_PATCH_BOLT(2, "需补件"),
    ADD_APPLY_ACADEMY(3, "加申院校"),

    //交通
    //0飞机/1高铁/2汽车
    AIRPLANE(0, "飞机"),
    HIGH_WAY(1, "高铁"),
    COACH(2, "汽车"),
    TRANSPORTATION_OTHERS(3, "其他"),
    //学习模式
    UN_DECIDED(0, "未定"),
    FACE_TO_FACE(1, "面授"),
    ONLINE_CLASS(2, "网课"),
    //保险购买方式枚举：0买学校保险/1通过Hti买保险/2通过其他机构买保险
    BUY_SCHOOL_INSURANCE(0, "买学校保险"),
    BUY_HTI_INSURANCE(1, "通过Hti买保险"),
    BUY_OTHER_INSURANCE(2, "通过其他机构买保险"),
    BUY_OTHER_INSURANCE_OTHER(3, "其他"),
    //支付方式 0=飞汇/1=易思汇/2=支付宝/3=银行/4=信用卡/5=其他汇款平台
    PAYMENT_BY_FEI_HUI(0, "飞汇"),
    PAYMENT_BY_YI_SI_HUI(1, "易思汇"),
    PAYMENT_BY_ALIPAY(2, "支付宝"),
    PAYMENT_BY_BANK(3, "银行"),
    PAYMENT_BY_CREDIT_CARD(4, "信用卡"),
    PAYMENT_BY_OTHER_REMITTANCE_PLATFORM(5, "其他汇款平台"),
    PAYMENT_BY_WECHAT(6, "微信"),
    PAYMENT_BY_INTERAC(7, "Interac e-Transfer"),
    //住宿费用类型
    WEEKLY(1, "每周"),
    MONTHLY(2, "每月"),
    //审批状态
    //0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销
    TO_BE_INITIATED(0, "待发起"),
    APPROVAL_FINISHED(1, "审批结束"),
    APPROVAL_IN_PROGRESS(2, "审批中"),
    APPROVAL_REJECT(3, "审批拒绝"),
    APPROVAL_ABANDONED(4, "申请放弃"),
    CANCELLATION(5, "作废"),
    REVOKED(6, "撤销"),

    //活动数据总览搜索维度
    //0按活动对象国家搜索/1按举办区域搜索/2按负责人搜索/3按活动类型搜索
    SEARCH_BY_TARGET_COUNTRY(0, "活动对象国家"),
    SEARCH_BY_STATE(1, "举办区域"),
    SEARCH_BY_CITY(2, "举办城市"),
    SEARCH_BY_STAFF(3, "负责人"),
    SEARCH_BY_STAFF2(4, "第二负责人"),
    SEARCH_BY_EVENT_TYPE(5, "活动类型"),

    //活动状态枚举
    //0计划/1结束/2取消/3延期
    EVENT_PLAN(0, "计划"),
    EVENT_END(1, "结束"),
    EVENT_CANCEL(2, "取消"),
    EVENT_POSTPONE(3, "延期"),
    //奖券购买状态
    LOCKING(0, "锁定"),
    PURCHASED_NOT_USED(1, "购买未使用"),
    PURCHASED_USED(2, "购买已使用"),
    /**
     * 考试中心题目类型
     */
    EXAMPAPER_TARGET_TYPE_QUESTION(1, "考题"),
    EXAMPAPER_TARGET_TYPE_QUESTIONTYPE(0, "考题类型"),
    /**
     * 考试中心题型(单选题、多选题、判断题)
     */
    QUESTION_TYPE_SINGLE(0, "单选题"),
    QUESTION_TYPE_MULTIPLE(1, "多选题"),
    QUESTION_TYPE_JUDGMENT(2, "判断题"),

    /**
     * 投票中心
     */
    //状态（优于时间控制）：0没开始/1进行中/2已结束
    VOTING_ITEM_NOT_STARTED(0, "未开始"),
    VOTING_ITEM_HAVE_IN_HAND(1, "进行中"),
    VOTING_ITEM_HAS_END(2, "已结束"),

    /**
     * 骑行报名身份类型：0身份证/1护照
     */
    CYCLING_REGISTRATION_ID_TYPE(0, "身份证"),
    CYCLING_REGISTRATION_PASSPORT_TYPE(1, "护照"),
    CYCLING_REGISTRATION_PERMIT_TYPE(2, "港澳回乡证"),
    CYCLING_REGISTRATION_TAIWANESE_TYPE(3, "台胞证"),

    /**
     * 骑行报名支付状态：0失败/1成功
     */
    CYCLING_REGISTRATION_FAILURE_PAY_STATUS(0, "失败"),
    CYCLING_REGISTRATION_SUCCESS_PAY_STATUS(1, "成功"),

    //区号
    CHINA(86, "中国大陆 +86"),
    HONG_KONG(852, "中国香港 +852"),
    TAI_WAN(886, "中国台湾 +886"),
    VIETNAM(84, "越南 +84"),
    INDONESIA(62, "印尼 +62"),
    MALAYSIA(60, "马来西亚 +60"),

    //佣金结算收款状态
    UNCOLLECTED(0, "未收"),
    PARTIALLY_RECEIVED(1, "部分已收"),
    COLLECTION_COMPLETED(2, "收款完成"),

    //结算状态
    UNSETTLED(0, "未结算"),
    SETTLEMENT_IN_PROGRESS(1, "结算中"),
    AGENT_CONFIRMATION(2, "代理确认"),
    FINANCIAL_RECOGNITION(3, "财务确认"),
    FINANCIAL_SUMMARY(4, "财务汇总"),


    /**
     * 申请方案状态
     */
    STUDENT_OFFER_CLOSE_STATUS(0, "关闭"),
    STUDENT_OFFER_OPEN_STATUS(1, "打开"),
    STUDENT_OFFER_END_STATUS(2, "终止"),
    STUDENT_OFFER_FINISH_STATUS(3, "成功结案"),

    /**
     * 分期表状态
     */
    INSTALLMENT_UNTREATED(0, "未处理"),
    INSTALLMENT_PROCESSING(1, "处理中"),
    INSTALLMENT_COMPLETE(2, "完成"),

    /**
     * 提醒方式：0系统内/1邮件/2短信
     */
    REMIND_METHOD_NONE(0, "不提醒"),
    REMIND_METHOD_EMAIL(1, "邮件"),
    REMIND_METHOD_MESSAGE(2, "短信"),
    REMIND_METHOD_SYSTEM(3, "系统内"),
    /**
     * 户籍类型
     */
    LOCAL_NON_AGRICULTURAL_ACCOUNT(0, "本地非农业户口"),
    LOCAL_AGRICULTURAL_ACCOUNT(1, "本地农业户口"),
    NON_AGRICULTURAL_ACCOUNT_IN_OTHER_PLACES(2, "外地非农业户口"),
    REMOTE_AGRICULTURAL_ACCOUNT(3, "外地农业户口"),
    HONGKONG_MACAO_TAIWAN(4, "港澳台"),
    FOREIGN(5, "外籍"),
    /**
     * 证件类型
     */
    IDENTITY_CARD(0, "身份证"),
    PASSPORT(1, "护照"),
    PASS(2, "通行证"),
    RETURN_PERMIT(3, "回乡证"),
    /**
     * 学历情况备注-项目说明
     */
    THREE_ONE(0, "3+2"),
    TWO_TWO(1, "2+2"),
    FOUR_ZERO(2, "4+0"),
    EXCHANGE_STUDENTS(3, "交换生"),
    /**
     * 学历情况备注-学位情况
     */
    DOUBLE_DEGREE(0, "获得双学位"),
    INTERNATIONAL_DEGREE(1, "获得国际学位"),
    DOMESTIC_DEGREE(2, "获得国内学位"),

    /**
     * 测试类型
     */
    HIGH_SCHOOL_TEST(1, "高中成绩类型"),
    STANDARD_TEST(2, "本科成绩类型"),
    ENGLISH_TEST(3, "英语测试类型"),
//    /**
//     * 其他金额类型
//     */
//    REWARD_MONEY(0,"奖励金额"),
//    INCENTIVE_REWARD(1,"Incentive奖励"),
//    INSURANCE_REWARD(2,"保险金额"),
//    EVENT_COST(3,"活动费用"),
//    OTHER_INCOME(4,"其他收入"),

    /**
     * 结算状态 settlement
     */
    SETTLEMENT_NOT_DONE(1, "处理中"),
    SETTLEMENT_FINISHED(2, "已完成"),

    /**
     * 财务确认状态
     */
    NOT_STARTED(0, "未开始"),
    SUBMITTED_TO_FINANCE(1, "已提交到财务"),
    COMPLETED(2, "已完成"),

    /**
     * 代理性质枚举
     */
    AGENT_NATURE_COMPANY(1,"公司"),
    AGENT_NATURE_PERSON(2,"个人"),
    AGENT_NATURE_STUDIO(3,"工作室"),
    AGENT_NATURE_INTERNATIONAL_SCHOOL(4,"国际学校"),
    AGENT_NATURE_OTHER(5,"其他"),
    AGENT_NATURE_PERSONAL_ACCOUNT_COMPANY(6,"个人账户公司"),

    /**
     * 代理性质全称
     */
    AGENT_NATURE_COMPANY_NAME(1,"（合资、股份制、民营）"),
    AGENT_NATURE_PERSON_NAME(2,"（个人）"),
    AGENT_NATURE_STUDIO_NAME(3,"（个人）"),
    AGENT_NATURE_INTERNATIONAL_SCHOOL_NAME(4,"（合资、股份制、民营）"),
    AGENT_NATURE_OTHER_NAME(5,"（个人）"),
    AGENT_NATURE_PERSONAL_ACCOUNT_COMPANY_NAME(6,"（个人）"),
    /**
     * apStatus应付状态
     */
    //0=未付、1=部分已付、2=已付清、3=未付【已创建应付计划】
    AP_STATUS_UNPAID(0, "未付"),
    AP_STATUS_PARTIALLY_PAID(1, "已付部分"),
    AP_STATUS_PAID_UP(2, "已付齐"),
    AP_STATUS_UNPAID_CREATED_PAYABLE_PLAN(3, "未付【已创建应付计划】"),

    /**
     * arStatus应收状态
     */
    //0=未收、1=部分已收、2=已收齐
    AR_STATUS_NOT_RECEIVED(0, "未收"),
    AR_STATUS_PARTIALLY_RECEIVED(1, "部分已收"),
    AR_STATUS_RECEIVED(2, "已收齐"),

    /**
     * 其他金额类型
     */
    REWARD_MONEY(0, "奖励金额"),
    INCENTIVE_REWARD(1, "Incentive奖励"),
    INSURANCE_REWARD(2, "保险金额"),
    EVENT_COST(3, "申请活动费用"),
    OTHER_INCOME(4, "其他收入"),
    COMPANY_COLLECTION(5, "公司代收"),
    FAM_TRIP(6, "Fam Trip"),

    GOPRO_HEADQUARTERS(0,"总部"),
    GOPRO_SOUTH_CHINA(1,"华南区"),
    GOPRO_EAST_CHINA(2,"华东区"),
    GOPRO_CENTRAL_CHINA(3,"华中区"),
    GOPRO_SOUTHWEST_CHINA(4,"西南区"),
    GOPRO_WEST_CHINA(5,"西区"),
    GOPRO_NORTHWEST_CHINA(6,"西北区"),
    GOPRO_NORTH_CHINA(7,"华北区"),
    //申请费用等级类型
    APP_FEE_BACHELOR(0,"本科"),
    APP_FEE_MASTER_DEGREE(1,"硕士"),
    APP_FEE_COMMUNITY(2,"社区"),


    /**
     * EventBillStatus应付状态
     */
    //0=未付、1=部分已付、2=已付清
    EB_STATUS_UNASSIGNED(0, "未分配"),
    EB_STATUS_ASSIGNED(1, "已经分配"),
    EB_STATUS_COMPLETE_ASSIGNMENT(2, "完成分配"),
    /**
     * 合同账户类型
     */
    DEBIT_CARD(1,"DEBIT_CARD"),

    PASSBOOK(2,"PASSBOOK"),

    CREDIT_CARD(3,"CREDIT_CARD"),

    SEMI_CREDIT_CARD(4,"SEMI_CREDIT_CARD"),

    PREPAID_CARD_FEE(5,"PREPAID_CARD_FEE"),

    OVERSEAS_CARD(6,"OVERSEAS_CARD"),

    /**
     * 住宿状态枚举
     */
    ACC_INACTIVE(0, "作废"),
    ACC_ACTIVE(1, "有效"),
    ACC_SUCCESS(2, "成功"),
    ACC_POSTPONE(3, "延期"),
    APPROVAL_FAILURE(4, "失败"),


    APP_AGENT_COMPANY(0,"公司"),
    APP_AGENT_PERSONAL(1,"个人"),
    APP_AGENT_PERSONAL_COMPANY(3,"个人账户公司"),

    APP_AGENT_BY_COMPANY(0,"以公司名义"),
    APP_AGENT_BY_PERSONAL(1,"以个人名义"),

    //0禁止进入/1允许进入
    CONVENTION_STAFF_PROHIBIT(0,"禁止进入"),
    CONVENTION_STAFF_ALLOW(1,"允许进入"),

    //SG联系人类型(枚举，父-1，母-2，监护人-3)',
    APP_SCHOOLGATE_FATHER(1,"父亲"),
    APP_SCHOOLGATE_MOTHER(2,"母亲"),
    APP_SCHOOLGATE_GUARDIAN(3,"监护人"),
    //SG活动单类型
    APP_SCHOOLGATE_TRANSCRIPT(1,"成绩单"),
    APP_SCHOOLGATE_ACADEMIC(2,"学术"),
    APP_SCHOOLGATE_MUSIC(3,"音乐"),
    APP_SCHOOLGATE_ARTS(4,"艺术"),
    APP_SCHOOLGATE_SPORTS(5,"运动"),
    APP_SCHOOLGATE_LANGUAGE(6,"语言"),
    APP_SCHOOLGATE_SERVICE(7,"服务"),
    APP_SCHOOLGATE_SPEECH(8,"演讲/辩论"),
    APP_SCHOOLGATE_HOBBIES(9,"组织/爱好"),
    APP_SCHOOLGATE_OTHERS(99,"其他"),
    //SG额外信息类型
    APP_SCHOOLGATE_BACKGROUND(1,"背景/介绍"),
    APP_SCHOOLGATE_GOALS(2,"目标和社会贡献"),
    APP_SCHOOLGATE_REASON(3,"选择学校的原因"),
    APP_SCHOOLGATE_LETTER(4,"推荐信"),

    //小程序类型
    WC_INSTITUTION_CONSULT(0,"学校资讯小程序"),

    MAIL_STATUS_NOT_SEND(0,"未发"),
    MAIL_STATUS_SUCCESS(1,"已发"),
    MAIL_STATUS_FAIL(-1,"失败"),
    MAIL_STATUS_REJECTION(2, "拒收"),

    //`status_review` int(10) DEFAULT NULL COMMENT '预审状态：提差异-1/未审核0/已审核1',
    REVIEW_STATUS_NOT_REVIEW(0,"未审核"),
    REVIEW_STATUS_REVIEW(1,"已审核"),
    REVIEW_STATUS_DIFFERENTIAL(-1,"提差异"),

    //代理申请排行排序类型
    CREATE_COUNT_SORT_TYPE(1,"按新建学生数"),
    CONFIRMATION_COUNT_BY_STUDENT_SORT_TYPE(2,"按定校量"),
    SUCCESS_COUNT_BY_STUDENT_SORT_TYPE (3,"按成功入学量"),

    //活动年度计划主题类型
    EVENT_PLAN_THEME_ONLINE(1,"线上活动"),
    EVENT_PLAN_THEME_OFFLINE(2,"线下活动"),
    EVENT_PLAN_THEME_WORKSHOP(3,"线下专访"),

    //mps 课程模式：0=Push/1=Direct
    PUSH(0,"Push"),
    DIRECT(1,"Direct"),

    //KPI方案统计角色
    BD(1,"BD"),
    STUDENT_OFFER_STAFF(2,"项目成员"),
    CPM(3,"PM"),
    // KPI方案统计方式
    PERSONAGE(1, "个人"),
    TEAM(2, "团队"),

    // 支付类型（1属于申请费代付 2、3、4属于学费代付）
    PAYMENT_TYPE_APPLICATION_FEE(1, "申请费（Application）"),
    PAYMENT_TYPE_TUITION_FULL(2, "全额学费（Full）"),
    PAYMENT_TYPE_TUITION_DEPOSIT(3, "学费首付（Deposit）"),
    PAYMENT_TYPE_TUITION_REMAINING(4, "学费尾款（Remaining）"),

    // 支付状态（申请费、学费）
    PAYMENT_STATUS_FAILED(0,"付费失败"),
    PAYMENT_STATUS_SENT_EMAIL(1,"已发送代付邮件"),
    PAYMENT_STATUS_PAID_CHANNEL(2, "已付费（支付渠道）"),
    PAYMENT_STATUS_PAID_SCHOOL(3, "已付费（学校）"),

    // 报名名册/赞助商名册状态
    CONVENTION_STATUS_UNSENT_INVOICE(0,"待发Invoice"),
    CONVENTION_STATUS_SENT_INVOICE(1,"已发Invoice"),
    CONVENTION_STATUS_PAID(2,"已收款"),
    CONVENTION_STATUS_PAID_MARKETING_FEE(3,"已从mkt fee扣"),
    CONVENTION_STATUS_FREE(4,"免费"),
    CONVENTION_STATUS_UNCONFIRMED(5,"待确定"),

    //hubs支付状态
    //hubs支付状态枚举
    PAY_STATUS_UNPAID(0,"待支付1"),
    PAY_STATUS_PAID(1,"支付成功1"),
    PAY_STATUS_PAID_FAIL(2,"支付失败11"),
    PAY_STATUS_PAID_CANCEL(3,"用户取消1"),
    PAY_STATUS_PAID_CANCEL_SYSTEM(4,"系统取消1"),

    /**
     * 合同状态
     */
    NONE(0, "无合同"),
    EFFECTIVE(11, "生效中"),
    EXPIRED(12, "已过期"),
    RENEWAL(13, "续约中"),
    NORENEWAL(14, "不续约"),


    /**
     * 任务状态
     */
    TASK_UNFINISHED(0, "待解决"),
    TASK_FINISHED(1, "已完成"),
    TASK_NOT_FINISHED(2, "未能完成")

    /**
     * 任务类型
     */

    ;

    /**
     * 任务状态
     */
    public static final ProjectExtraEnum[] TASK_STATUS = new ProjectExtraEnum[]{
            TASK_UNFINISHED,TASK_FINISHED,TASK_NOT_FINISHED
    };


    /**
     * 报名名册状态
     */
    public static final ProjectExtraEnum[] CONVENTION_STATUS = new ProjectExtraEnum[]{
            CONVENTION_STATUS_UNSENT_INVOICE,
            CONVENTION_STATUS_SENT_INVOICE,
            CONVENTION_STATUS_PAID,
            CONVENTION_STATUS_PAID_MARKETING_FEE,
            CONVENTION_STATUS_FREE,
            CONVENTION_STATUS_UNCONFIRMED
    };

    /**
     * 支付类型（1属于申请费代付 2、3、4属于学费代付）
     */
    public static final ProjectExtraEnum[] PAYMENT_TYPE = new ProjectExtraEnum[]{
            PAYMENT_TYPE_APPLICATION_FEE,
            PAYMENT_TYPE_TUITION_FULL,
            PAYMENT_TYPE_TUITION_DEPOSIT,
            PAYMENT_TYPE_TUITION_REMAINING
    };
    /**
     * 申请费代付类型
     */
    public static final ProjectExtraEnum[] APPLICATION_FEE_PAYMENT_TYPE = new ProjectExtraEnum[]{PAYMENT_TYPE_APPLICATION_FEE};
    /**
     * 学费代付类型
     */
    public static final ProjectExtraEnum[] TUITION_PAYMENT_TYPE = new ProjectExtraEnum[]{
            PAYMENT_TYPE_TUITION_FULL,
            PAYMENT_TYPE_TUITION_DEPOSIT,
            PAYMENT_TYPE_TUITION_REMAINING
    };

    /**
     * 支付状态（申请费、学费）
     */
    public static final ProjectExtraEnum[] PAYMENT_STATUS = new ProjectExtraEnum[]{PAYMENT_STATUS_FAILED, PAYMENT_STATUS_SENT_EMAIL, PAYMENT_STATUS_PAID_CHANNEL, PAYMENT_STATUS_PAID_SCHOOL};

    public static  final  ProjectExtraEnum[] APP_HUBS_PAY_STATUS = new ProjectExtraEnum[]{PAY_STATUS_UNPAID,PAY_STATUS_PAID,PAY_STATUS_PAID_FAIL,PAY_STATUS_PAID_CANCEL,PAY_STATUS_PAID_CANCEL_SYSTEM};

    public static final ProjectExtraEnum[] EXTRANEOUS_INFORMATION = new ProjectExtraEnum[]{APP_SCHOOLGATE_BACKGROUND,APP_SCHOOLGATE_GOALS,APP_SCHOOLGATE_REASON,APP_SCHOOLGATE_LETTER};

    public static final ProjectExtraEnum[] InstitutionType = new ProjectExtraEnum[]{WC_INSTITUTION_CONSULT};

    public static final ProjectExtraEnum[] ContactType = new ProjectExtraEnum[]{APP_SCHOOLGATE_FATHER,APP_SCHOOLGATE_MOTHER,APP_SCHOOLGATE_GUARDIAN};

    public static final ProjectExtraEnum[] ACTIVITY_TYPE = new ProjectExtraEnum[]{  APP_SCHOOLGATE_TRANSCRIPT, APP_SCHOOLGATE_ACADEMIC, APP_SCHOOLGATE_MUSIC,APP_SCHOOLGATE_ARTS,APP_SCHOOLGATE_SPORTS,
            APP_SCHOOLGATE_LANGUAGE,APP_SCHOOLGATE_SERVICE, APP_SCHOOLGATE_SPEECH, APP_SCHOOLGATE_HOBBIES,APP_SCHOOLGATE_OTHERS};

    /**
     * 合同账户类型
     */
    public static final ProjectExtraEnum[] cardType = {DEBIT_CARD,PASSBOOK,CREDIT_CARD,SEMI_CREDIT_CARD,PREPAID_CARD_FEE,OVERSEAS_CARD};

    /**
     * MPS programmes_mode
     */
    public static final ProjectExtraEnum[] PROGRAMES_MODE = {PUSH,DIRECT};

    /**
     * 申请费用等级类型
     */
    public static final ProjectExtraEnum[] InstitutionAppFeeLevelType = new ProjectExtraEnum[]{APP_FEE_BACHELOR,
            APP_FEE_MASTER_DEGREE,APP_FEE_COMMUNITY};


    /**
     * 测试类型
     */
    public static final ProjectExtraEnum[] TEST_TYPE = new ProjectExtraEnum[]{HIGH_SCHOOL_TEST, STANDARD_TEST, ENGLISH_TEST};
    /**
     * 标准测试类型
     */
    public static final ProjectExtraEnum[] STANDARD_TEST_TYPE = new ProjectExtraEnum[]{NCEE, SAT2, HKDSE, ALEVEL, IB, MSS,
            UG, CLASS, GPA, GPA4, GPA4_2, GPA4_5, GPA5, GPA7, GPA9, PERCENTAGE, GRADING, AGE, WORK_EXP,
            ACCEPT_MAJOR_TRANSFER, OLEVEL, OSSD, NCUK, ATAR, GAOKAO, SAT1, AD, STPM, MUFY, PM, UEC, ISCE, DS, SPM, SMA3, SMA2, MATAYOM6,
            NE, GPA4_3, GPA10,GPA12,ACT, GRE, GMAT};
    /**
     * 代理佣金结算收款状态
     */
    public static final ProjectExtraEnum[] AGENT_SETTLEMENT_STATUS = new ProjectExtraEnum[]{UNCOLLECTED, PARTIALLY_RECEIVED, COLLECTION_COMPLETED};
    /**
     * 考试中心题型(单选题、多选题、判断题)
     */
    public static final ProjectExtraEnum[] QUESTION_TYPE_LIST_NAME = new ProjectExtraEnum[]{QUESTION_TYPE_SINGLE, QUESTION_TYPE_MULTIPLE, QUESTION_TYPE_JUDGMENT};
//    /**
//     * 英语测试类型 标准测试类型
//     */
//    public static final ProjectExtraEnum[] ENGLISH_TEST_TYPE = new ProjectExtraEnum[]{IELTS, TOEFL_IBT, TOEFL_PBT, PTE, HKDSE_ENG, CET, DUOLINGO, BEC};

    /**
     * 英语成绩专用(教育背景和考试模块专用)
     */
    public static final ProjectExtraEnum[] ENGLISH_TEST_TYPE = new ProjectExtraEnum[]{IELTS,TOEFL_IBT,PTE,CET,DUOLINGO,BEC, LANGUAGE_CERT, SCHOOL_INTERNAL_TEST};

    /**
     * 住宿费用类型
     */
    public static final ProjectExtraEnum[] ACCOMMODATION_EXPENSE_TYPE = new ProjectExtraEnum[]{WEEKLY, MONTHLY};
    /**
     * 公开对象中文
     */
    public static final ProjectExtraEnum[]  PUBLIC_OBJECTS = new ProjectExtraEnum[]{NOT_PUBLIC,PUBLIC,PUBLIC_STUDENTS,PUBLIC_AGENT,PUBLIC_CURRENCY_COMMON,PUBLIC_COUNTRY_HOME,PUBLIC_COUNTRY_COMMON,PUBLIC_PARTNER,PUBLIC_PMP};


    /**
     * 合同状态
     */
    public static final ProjectExtraEnum[] CONTRACT_STATUS = new ProjectExtraEnum[]{NONE,EFFECTIVE,EXPIRED,RENEWAL};

    /**
     * 系统配置中文
     */
    public static final ProjectExtraEnum[] SYSTEM_CONFIGURATIONS = new ProjectExtraEnum[]{SYSTEM_CONFIGURATION, BUSINESS_CONFIGURATION};
    /**
     * 课程排名中文
     */
    public static final ProjectExtraEnum[] RANKING_TYPE = new ProjectExtraEnum[]{COUNTRY_RANKING, WORLD_RANKING};
    /**
     * 特性类型中文
     */
    public static final ProjectExtraEnum[] FEATURE_TYPE = new ProjectExtraEnum[]{WORLD_RANKINGS_QS, WORLD_RANKINGS_TIMES, ASIA_RANKINGS_QS,
            ASIA_RANKINGS_TIMES, INTERNATIONAL_RANKINGS_QS, INTERNATIONAL_RANKINGS_TIMES, COUNTRY_RANKINGS, CUG_RANKINGS, REF_RANKINGS, FACULTY_STUDENT_RATIO, SEX_RATIO, PERCENTAGE_OF_INTERNATIONAL_STUDENTS,
            PERCENTAGE_OF_CHINESE_STUDENTS, STUDENTS_NUM, VIDEO_WEBSITE_01, ACCEPTANCE_RATE, ENROLLMENT, RELIGION, TEL, FAX, CLOTHING_EXPENSES,CATERING_EXPENSES,ACCOMMODATION_EXPENSES,TRANSPORTATION_EXPENSES,TEACHING_LANGUAGE,SET_UP_NATURE,EMAIL,SPECIFIC,LIVING_EXPENSES,
            INFO_SCHOOL_ADVANTAGE, INFO_IB_AP_OSSD_AL, INFO_CURRICULUM_JUNIOR, INFO_CURRICULUM_SENIOR, INFO_CHINESE_FONT, INFO_TUITION_FEE_HIGHEST, INFO_CLASS_STRUCTURE, INFO_GENDER,
            INFO_IB_AVERAGE, INFO_IB_40, INFO_AP_AVERAGE, INFO_AP_4, INFO_A_A, INFO_A_B, INFO_TOP_20_RATE, INFO_APPLICATION_DEADLINE, INFO_ONLINE_APPLICATION,
            INFO_APPLICATION_FEE,INFO_APPLICATION_RENEWAL_FEE,INFO_ASSESSMENT_FEE,INFO_CAPITAL_LEVY,INFO_DEPRECIATING_DEBENTURES,INFO_DEPRECIATED_BY};
    /**
     * 信息类型
     */
    public static final ProjectExtraEnum[] INFORMATION_TYPE = new ProjectExtraEnum[]{CAREER_PATH,LIVING_EXPENSES_STUDYING_ABROAD,TOTLE_EXPENSES_STUDYING_ABROAD,LIFE_SAFETY};
    /**
     * 公式类型英文
     */
    public static final ProjectExtraEnum[] FORMULA_TYPE = new ProjectExtraEnum[]{SPECIAL, ROUTINE, AWARD, ONE_AWARD};
    /**
     * 合同公式条件类型
     */
    public static final ProjectExtraEnum[] CONDITION_TYPE = new ProjectExtraEnum[]{TRANSFER_AGENT_STUDENT,
            //PERCENTAGE_OF_SCHOLARSHIP_FEES,
            READ_ONLY_PHASE_ONE, READ_ONLY_PHASE_TWO, READ_ONLY_PHASE_THREE, READ_ONLY_PHASE_FOUR};
    /**
     * 合同公式条件类型只保留转代理学生
     */
    public static final ProjectExtraEnum[] CONDITION_TYPE_ONLY = new ProjectExtraEnum[]{TRANSFER_AGENT_STUDENT};
    /**
     * 统计类型中文
     */
    public static final ProjectExtraEnum[] COUNT_TYPE = new ProjectExtraEnum[]{NUMBER_OF_STUDENTS, FEE_OF_STUDENTS, ACCUMULATED_TUITION,
            COURSE_DURATION_WEEK, COURSE_DURATION_MONTH, COURSE_DURATION_YEAR, COURSE_DURATION_SEMESTER};
    /**
     * 学习申请计划 课程长度下拉框枚举
     */
    public static final ProjectExtraEnum[] DURATION_TYPE = new ProjectExtraEnum[]{STUDENT_OFFER_ITEM_COURSE_DURATION_WEEK, STUDENT_OFFER_ITEM_COURSE_DURATION_MONTH,
            STUDENT_OFFER_ITEM_COURSE_DURATION_YEAR, STUDENT_OFFER_ITEM_COURSE_DURATION_SEMESTER};
    /**
     * 签类型
     */
    public static final ProjectExtraEnum[] INSTITUTIONSIGNINGTYPE = new ProjectExtraEnum[]{RENEW, NEWSIGNING};
    public static final ProjectExtraEnum[] SELECTSTATUS = new ProjectExtraEnum[]{MYAPPLICATION, MYAPPROVAL};
    /**
     * 显示类型中文
     */
    public static final ProjectExtraEnum[] SHOW_TYPES = new ProjectExtraEnum[]{DIRECT_DISPLAY, SMALL_LAYER_PLAIN_TEXT_DISPLAY, LARGE_LAYER_RICH_TEXT_DISPLAY};
    /**
     * kpi推荐等级
     */
    public static final ProjectExtraEnum[] KEI_LEVEL = new ProjectExtraEnum[]{BIG_RECOMMENDATION, MEDIUM_RECOMMENDATION, SMALL_RECOMMENDATION};
    /**
     * 数据等级
     */
    public static final ProjectExtraEnum[] DATA_LEVEL = new ProjectExtraEnum[]{IMPORT_FROM_OLD_DATA, MANUAL_INPUT,IMPORT_FROM_BUSINESS_DATA, IMPORT_FROM_OFFICIAL_DATA, IMPORT_FROM_OFFICIAL_DATA2, NEED_TO_ADD, COMPLETE_COLLECTION,};
    /**
     * 学习计划业务状态
     */
    public static final ProjectExtraEnum[] LEARNING_PLAN_BUSINESS_STATUS = new ProjectExtraEnum[]{GET_THE_FIRST_STAGE_COMMISSION,
            GET_THE_SECOND_STAGE_COMMISSION, GET_THE_THIRD_STAGE_COMMISSION, GET_THE_FOURTH_STAGE_COMMISSION, READ_ONLY_PHASE_ONE,
            READ_ONLY_PHASE_TWO, READ_ONLY_PHASE_THREE, READ_ONLY_PHASE_FOUR};
    /**
     * 学生业务状态
     */
    public static final ProjectExtraEnum[] STUDENT_BUSINESS_STATUS = new ProjectExtraEnum[]{TRANSFER_AGENT_STUDENT
//            , PERCENTAGE_OF_SCHOLARSHIP_FEES
    };
    /**
     * 参会人员类型
     */
    public static final ProjectExtraEnum[] CONVENTION_PERSON_TYPE = new ProjectExtraEnum[]{INSTITUTION_AGENT, AGENT, GUEST, STAFF, WORKING_PERSON};
    /**
     * 交通类型
     */
    public static final ProjectExtraEnum[] TRANSPORTATION = new ProjectExtraEnum[]{AIRPLANE, HIGH_WAY, COACH, TRANSPORTATION_OTHERS};
    /**
     * 学生信息状态
     */
    public static final ProjectExtraEnum[] STUDENT_INFO_STATUS = new ProjectExtraEnum[]{NEW_CONSTRUCTION, ALREADY_SUBMITTED, NEED_PATCH_BOLT, ADD_APPLY_ACADEMY};
    /**
     * 课程课程条件升读要求条件类型
     */
    public static final ProjectExtraEnum[] COURSE_FURTHER_SCORE_CONDITION_TYPE = new ProjectExtraEnum[]{ACADEMIC_REQ, ENGLISH_REQ, SOPHOMORE_ACADEMIC_REQ, SOPHOMORE_ENGLISH_REQ, SPECIFIC_SUBJECT_REQ};
    /**
     * 学习模式
     */
    public static final ProjectExtraEnum[] LEARNING_MODEL = new ProjectExtraEnum[]{UN_DECIDED, FACE_TO_FACE, ONLINE_CLASS};
    //'保险购买方式枚举：0买学校保险/1通过Hti买保险/2通过其他机构买保险
    public static final ProjectExtraEnum[] INSURANCE_PURCHASE_METHOD = new ProjectExtraEnum[]{BUY_SCHOOL_INSURANCE, BUY_HTI_INSURANCE, BUY_OTHER_INSURANCE,BUY_OTHER_INSURANCE_OTHER};
    /**
     * 留学保险支付方式
     */
    public static final ProjectExtraEnum[] INSURANCE_PAYMENT_METHOD = new ProjectExtraEnum[]{PAYMENT_BY_FEI_HUI, PAYMENT_BY_YI_SI_HUI, PAYMENT_BY_ALIPAY, PAYMENT_BY_CREDIT_CARD, PAYMENT_BY_WECHAT, PAYMENT_BY_INTERAC};

    //支付方式
    public static final ProjectExtraEnum[] PAYMENT_METHOD = new ProjectExtraEnum[]{PAYMENT_BY_FEI_HUI, PAYMENT_BY_YI_SI_HUI, PAYMENT_BY_ALIPAY,PAYMENT_BY_BANK,PAYMENT_BY_CREDIT_CARD,PAYMENT_BY_OTHER_REMITTANCE_PLATFORM};
    /**
     * 活动数据总览搜索维度
     */
    public static final ProjectExtraEnum[] EVENT_SEARCH_BY = new ProjectExtraEnum[]{SEARCH_BY_TARGET_COUNTRY, SEARCH_BY_STATE, SEARCH_BY_CITY, SEARCH_BY_STAFF, SEARCH_BY_STAFF2, SEARCH_BY_EVENT_TYPE};
    /**
     * 活动状态
     */
    public static final ProjectExtraEnum[] EVENT_STATUS = new ProjectExtraEnum[]{EVENT_PLAN, EVENT_END, EVENT_CANCEL, EVENT_POSTPONE};
    /**
     * 活动对象
     */
    public static final ProjectExtraEnum[] EVENT_PUBLIC_LEVEL = new ProjectExtraEnum[]{NOT_PUBLIC, PUBLIC, PUBLIC_STUDENTS, PUBLIC_AGENT};
    /**
     * 提醒类型
     */
    public static final ProjectExtraEnum[] REMINDER_TYPE = new ProjectExtraEnum[]{REMIND_METHOD_NONE, REMIND_METHOD_EMAIL, REMIND_METHOD_MESSAGE, REMIND_METHOD_SYSTEM};

    /**
     * 奖券购买状态
     */
    public static final ProjectExtraEnum[] LOTTERY_TICKET_PURCHASE_STATUS = new ProjectExtraEnum[]{LOCKING, PURCHASED_NOT_USED, PURCHASED_USED};
    /**
     * 投票项状态
     */
    public static final ProjectExtraEnum[] VOTING_ITEM_STATUS = new ProjectExtraEnum[]{VOTING_ITEM_NOT_STARTED, VOTING_ITEM_HAVE_IN_HAND, VOTING_ITEM_HAS_END};
    /**
     * 骑行身份类型：0身份证/1护照
     */
    public static final ProjectExtraEnum[] CYCLING_ID_TYPE = new ProjectExtraEnum[]{CYCLING_REGISTRATION_PASSPORT_TYPE, CYCLING_REGISTRATION_ID_TYPE};
    /**
     * 骑行支付状态：0失败/1成功
     */
    public static final ProjectExtraEnum[] CYCLING_PAY_STATUS = new ProjectExtraEnum[]{CYCLING_REGISTRATION_SUCCESS_PAY_STATUS, CYCLING_REGISTRATION_FAILURE_PAY_STATUS};
    /**
     * 区号类型
     */
    public static final ProjectExtraEnum[] AREA_CODE_TYPE = new ProjectExtraEnum[]{CHINA, HONG_KONG, TAI_WAN, VIETNAM, INDONESIA, MALAYSIA};
    /**
     * 户籍类型
     */
    public static final ProjectExtraEnum[] REGISTERED_RESIDENCE_TYPE = new ProjectExtraEnum[]{LOCAL_NON_AGRICULTURAL_ACCOUNT, LOCAL_AGRICULTURAL_ACCOUNT, NON_AGRICULTURAL_ACCOUNT_IN_OTHER_PLACES, REMOTE_AGRICULTURAL_ACCOUNT, HONGKONG_MACAO_TAIWAN, FOREIGN};
    /**
     * 证件类型
     */
    public static final ProjectExtraEnum[] DOCUMENT_TYPE = new ProjectExtraEnum[]{IDENTITY_CARD, PASSPORT, PASS, RETURN_PERMIT};
    /**
     * 学历情况备注-项目说明
     */
    public static final ProjectExtraEnum[] EDUCATION_PROJECT = new ProjectExtraEnum[]{THREE_ONE, TWO_TWO, FOUR_ZERO, EXCHANGE_STUDENTS};
    /**
     * 学历情况备注-学位情况
     */
    public static final ProjectExtraEnum[] EDUCATION_DEGREE = new ProjectExtraEnum[]{DOUBLE_DEGREE, INTERNATIONAL_DEGREE, DOMESTIC_DEGREE};
    /**
     * 分类模式
     */
    public static final ProjectExtraEnum[] PATTERN_TYPE = new ProjectExtraEnum[]{INTERLLIGENT_CALSSIFCATION, PROFESSIONAL_CLASSIFCATION,OFFER_ITEM_CLASSIFCATION,MSO_SIMPLE_CLASSIFCATION,MSO_MODULE_CLASSIFCATION};
    /**
     * 审批状态
     */
    public static final ProjectExtraEnum[] STATUS_OF_APPROVE = new ProjectExtraEnum[]{
            TO_BE_INITIATED,
            APPROVAL_FINISHED,
            APPROVAL_IN_PROGRESS,
            APPROVAL_REJECT,
            APPROVAL_ABANDONED,
            CANCELLATION,
            REVOKED};
    //高中成绩枚举,学历背景下拉
    public static final ProjectExtraEnum[] HIGH_SCHOOL_GRADES = new ProjectExtraEnum[]{
            MSS,IB,AP,ALEVEL,OLEVEL,GCE,GCSE,IGCSE,NCUK,UG,ATAR,AEAS,QCE,QTAC,SACE,GAOKAO,
            NCAE,HKDSE,HKAL,HKAS,HKCEE,OSSD,NCEE,SAT1,SAT2,ACT,AD,STPM,MUFY,PM,UEC,ISCE,DS,SPM,SMA3,SMA2,MATAYOM6,NE
    };
    //本科成绩枚举,学历背景下拉
    public static final ProjectExtraEnum[] UNDERGRADUATE_ACHIEVEMENT = new ProjectExtraEnum[]{
            CLASS, GPA, GPA4, GPA4_2, GPA4_3, GPA4_5, GPA5, GPA7, GPA9, GPA10,GPA12,GRE,
            GMAT, PERCENTAGE, GRADING, GU, OTHER_REQUIREMENTS
    };
    //学术子表条件类型枚举,课程子表下拉
    public static final ProjectExtraEnum[] ACADEMIC_SUB_TABLE_CONDITION_TYPE = new ProjectExtraEnum[]{
            MSS,IB,AP,ALEVEL,OLEVEL,GCE,GCSE,IGCSE,NCUK,UG,ATAR,AEAS,QCE,QTAC,SACE,GAOKAO,
            NCAE,HKDSE,HKAL,HKAS,HKCEE,OSSD,NCEE, SAT1,SAT2,ACT,AD,STPM,MUFY,PM,UEC,ISCE,DS,SPM,SMA3,SMA2,MATAYOM6,NE,
            CLASS,GPA,GPA4,GPA4_2,GPA4_3,GPA4_5,GPA5,GPA7,GPA9,GPA10,GPA12,GRE,GMAT,PERCENTAGE,GRADING,GU,AGE,WORK_EXP,ACCEPT_MAJOR_TRANSFER,OTHER_REQUIREMENTS
    };
    //英语子表成绩类型枚举,课程子表下拉
    public static final ProjectExtraEnum[] English_subtype = new ProjectExtraEnum[]{
            IELTS, TOEFL_IBT, TOEFL_PBT, PTE, HKDSE_ENG, CET, DUOLINGO, BEC , LANGUAGE_CERT

    };
    //其他金额类型bonusType
    public static final ProjectExtraEnum[] BONUS_TYPE = new ProjectExtraEnum[]{
            REWARD_MONEY,
            INCENTIVE_REWARD,
            INSURANCE_REWARD,
            EVENT_COST,
            COMPANY_COLLECTION,
            OTHER_INCOME,
            FAM_TRIP
    };
    //结算状态
    public static final ProjectExtraEnum[] SETTLEMENT_TYPE = new ProjectExtraEnum[]{
            SETTLEMENT_NOT_DONE,
            SETTLEMENT_FINISHED

    };

    //留学服务费结算状态
    public static final ProjectExtraEnum[] STUDY_ABROAD_FEE_STATUS = new ProjectExtraEnum[]{
            NOT_STARTED,
            SUBMITTED_TO_FINANCE,
            COMPLETED

    };


    //apStatus应付状态
    public static final ProjectExtraEnum[] AP_STATUS = new ProjectExtraEnum[]{
            AP_STATUS_UNPAID,
            AP_STATUS_PARTIALLY_PAID,
            AP_STATUS_PAID_UP,
            AP_STATUS_UNPAID_CREATED_PAYABLE_PLAN

    };
    //身份证正反面
    public static final ProjectExtraEnum[] idCartFB={FRONT,BACK};
    //arStatus应收状态
    public static final ProjectExtraEnum[] AR_STATUS = new ProjectExtraEnum[]{
            AR_STATUS_NOT_RECEIVED,
            AR_STATUS_PARTIALLY_RECEIVED,
            AR_STATUS_RECEIVED
    };
    //GOPRO大区
    public static final ProjectExtraEnum[] GOPRO_REGION = new ProjectExtraEnum[]{
            GOPRO_HEADQUARTERS,
            GOPRO_SOUTH_CHINA,
            GOPRO_EAST_CHINA,
            GOPRO_CENTRAL_CHINA,
            GOPRO_SOUTHWEST_CHINA,
            GOPRO_WEST_CHINA,
            GOPRO_NORTHWEST_CHINA,
            GOPRO_NORTH_CHINA
};
    /**
     * 活动汇总余额分配状态
     */
    public static final ProjectExtraEnum[] EB_STATUS = new ProjectExtraEnum[]{
            EB_STATUS_UNASSIGNED,
            EB_STATUS_ASSIGNED,
            EB_STATUS_COMPLETE_ASSIGNMENT
};
    /**
     * 住宿状态枚举
     */
    public static final ProjectExtraEnum[] ACC_STATUS = new ProjectExtraEnum[]{
//            ACC_INACTIVE,
//            ACC_ACTIVE,
            ACC_SUCCESS,
            ACC_POSTPONE,
            APPROVAL_FAILURE
};
    public static final ProjectExtraEnum[] APP_STATUS_TYPE = new ProjectExtraEnum[]{
            APP_STATUS_NEW,
            APP_STATUS_REVIEW,
            APP_STATUS_AGREE,
            APP_STATUS_REJECT
};


    public static final ProjectExtraEnum[] AGENT_NATURE_TYPE = new ProjectExtraEnum[]{
            AGENT_NATURE_COMPANY,
            AGENT_NATURE_PERSON,
            AGENT_NATURE_STUDIO,
            AGENT_NATURE_INTERNATIONAL_SCHOOL,
            AGENT_NATURE_OTHER,
            AGENT_NATURE_PERSONAL_ACCOUNT_COMPANY
};
    public static final ProjectExtraEnum[] AGENT_NATURE_TYPE_NAME = new ProjectExtraEnum[]{
            AGENT_NATURE_COMPANY_NAME,
            AGENT_NATURE_PERSON_NAME,
            AGENT_NATURE_STUDIO_NAME,
            AGENT_NATURE_INTERNATIONAL_SCHOOL_NAME,
            AGENT_NATURE_OTHER_NAME,
            AGENT_NATURE_PERSONAL_ACCOUNT_COMPANY_NAME
    };

    /**
     * 峰会人员权限模式
     */
    public static final ProjectExtraEnum[] CONVENTION_STAFF_MODE_TYPE = new ProjectExtraEnum[]{CONVENTION_STAFF_PROHIBIT, CONVENTION_STAFF_ALLOW};




    /**
     * 学习计划新申请状态下拉,搜索枚举和下拉枚举已分开，新增需要同步ITEM_NEW_APP_STATUS
     */
    public static final ProjectExtraEnum[] NEW_APP_STATUS = {LACK_OF_INFORMATION,NOT_OPEN};

    public static final ProjectExtraEnum[] ITEM_NEW_APP_STATUS = {LACK_OF_INFORMATION,NOT_OPEN,NOT_MARKED};

    /**
     * 投票项状态
     */
    public static final ProjectExtraEnum[] AGENT_APPLICATION_RANKING_SORT_TYPE  = new ProjectExtraEnum[]{CREATE_COUNT_SORT_TYPE, CONFIRMATION_COUNT_BY_STUDENT_SORT_TYPE, SUCCESS_COUNT_BY_STUDENT_SORT_TYPE};
    /**
     * 投票项状态
     */
    public static final ProjectExtraEnum[] MAIL_STATUS  = new ProjectExtraEnum[]{
            MAIL_STATUS_NOT_SEND,
            MAIL_STATUS_SUCCESS,
            MAIL_STATUS_FAIL,
            MAIL_STATUS_REJECTION
    };

    public static final ProjectExtraEnum[] DISPLAY_TYPE = new ProjectExtraEnum[]{ EVENT_PLAN_THEME_ONLINE, EVENT_PLAN_THEME_OFFLINE, EVENT_PLAN_THEME_WORKSHOP};

    public static final ProjectExtraEnum[] COUNT_ROLE = new ProjectExtraEnum[]{BD,STUDENT_OFFER_STAFF,CPM};
    public static final ProjectExtraEnum[] COUNT_MODE = new ProjectExtraEnum[]{PERSONAGE, TEAM};
    public static final ProjectExtraEnum[] AUDIT_STATUS =new ProjectExtraEnum[]{REVIEW_STATUS_NOT_REVIEW,REVIEW_STATUS_REVIEW,REVIEW_STATUS_DIFFERENTIAL} ;

    public Integer key;
    public String value;

    ProjectExtraEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getValue(Integer key) {
        ProjectExtraEnum[] projectKeyEnums = values();
        for (ProjectExtraEnum projectKeyEnum : projectKeyEnums) {
            if (projectKeyEnum.key.equals(key)) {
                return LocaleMessageUtils.getMessage(projectKeyEnum.name());
            }
        }
        return null;
    }

    /**
     * 根据key获取value
     *
     * @param key
     * @return
     */
    public static String getValueByKey(Integer key, ProjectExtraEnum[] enums) {
        List<ProjectExtraEnum> list = Arrays.asList(enums).stream().filter(e -> e.key().equals(key)).collect(Collectors.toList());
        String value = "";
        if (GeneralTool.isNotEmpty(list)) {
            value = list.get(0).value();
            String i18nString = LocaleMessageUtils.getMessage(list.get(0).name());
            if (GeneralTool.isNotEmpty(i18nString)) {
                value = i18nString;
            }
        }
//        return GeneralTool.isNotEmpty(list) ? LocaleMessageUtils.getMessage(list.get(0).name()) : "";
        return value;
    }

    /**
     * 直接取出原始value,不执行国际化
     *
     * @Date 14:04 2022/8/12
     * <AUTHOR>
     */
    public static String getInitialValueByKey(Integer key, ProjectExtraEnum[] enums) {
        List<ProjectExtraEnum> list = Arrays.stream(enums).filter(e -> e.key().equals(key)).collect(Collectors.toList());
        String value = "";
        if (GeneralTool.isNotEmpty(list)) {
            value = list.get(0).value();
        }
//        return GeneralTool.isNotEmpty(list) ? LocaleMessageUtils.getMessage(list.get(0).name()) : "";
        return value;
    }


    public static String getValueByKey(Integer key, ProjectExtraEnum[] enums,String local) {
        List<ProjectExtraEnum> list = Arrays.asList(enums).stream().filter(e -> e.key().equals(key)).collect(Collectors.toList());
        String value = "";
        if (GeneralTool.isNotEmpty(list)) {
            value = list.get(0).name();
            String i18nString = LocaleMessageUtils.getMessage(local,list.get(0).name());
            if (GeneralTool.isNotEmpty(i18nString)) {
                value = i18nString;
            }
        }
//        return GeneralTool.isNotEmpty(list) ? LocaleMessageUtils.getMessage(list.get(0).name()) : "";
        return value;
    }

    public static Integer getKey(String value) {
        ProjectExtraEnum[] projectKeyEnums = values();
        for (ProjectExtraEnum projectKeyEnum : projectKeyEnums) {
            if (projectKeyEnum.value().equals(value)) {
                return projectKeyEnum.key();
            }
        }
        return null;
    }

    /**
     * 将枚举转换为list(不用国际化)
     *
     * @param enums
     * @return
     */
    public static List<Map<String, Object>> enums2Arrays(ProjectExtraEnum[] enums) {
        if (GeneralTool.isEmpty(enums)) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> list = new ArrayList<>();
        Arrays.stream(enums).forEach(item -> {
            Map<String, Object> map = new HashMap<>();
            map.put("key", item.key);
            map.put("value", item.value());
            list.add(map);
        });
        return list;
    }

    /**
     * 将枚举转换为list(不用国际化)
     *
     * @param enums
     * @return
     */
    public static List<Map<String, Object>> mapListResponseBo(ProjectExtraEnum[] enums) {
        if (GeneralTool.isEmpty(enums)) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> list = new ArrayList<>();
        Arrays.stream(enums).forEach(item -> {
            Map<String, Object> map = new HashMap<>();
            map.put("key", item.key);
            map.put("value", item.value());
            list.add(map);
        });
        return list;
    }

    /**
     * 将枚举转换为list（国际化）
     *
     * @param enums
     * @return
     */
    public static List<Map<String, Object>> enumsTranslation2Arrays(ProjectExtraEnum[] enums) {
        if (GeneralTool.isEmpty(enums)) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> list = new ArrayList<>();
        Arrays.stream(enums).forEach(item -> {
            Map<String, Object> map = new HashMap<>();
            map.put("key", item.key);
            map.put("value", LocaleMessageUtils.getMessage(item.name()));
            list.add(map);
        });
        return list;
    }

    /**
     * 判断是否在枚举里
     *
     * @Date 11:27 2021/6/9
     * <AUTHOR>
     */
    public static boolean existsEnums(ProjectExtraEnum[] enums, ProjectExtraEnum projectExtraEnum) {
        if (GeneralTool.isEmpty(enums)) {
            return false;
        }
        boolean flag = false;
        for (ProjectExtraEnum emum : enums) {
            if (emum.equals(projectExtraEnum)) {
                flag = true;
            }
        }
        return flag;
    }

    public static ProjectExtraEnum getEnum(Integer key, ProjectExtraEnum[] values) {
        for (ProjectExtraEnum value : values) {
            if (key.equals(value.key())) {
                return value;
            }
        }
        return null;
    }

    private Integer key() {
        return this.key;
    }

    private String value() {
        return this.value;
    }
}
