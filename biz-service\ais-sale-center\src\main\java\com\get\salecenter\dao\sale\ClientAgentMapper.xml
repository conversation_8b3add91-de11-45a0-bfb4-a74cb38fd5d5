<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ClientAgentMapper">

    <select id="getAgentStaffNameByClientId" resultType="com.get.salecenter.vo.ClientAgentVo">
        SELECT
        sa.*,
        CONCAT(a.`name`,IF(a.name_note is null or a.name_note = "","",CONCAT("（",a.name_note,"）"))) as agentName,
        s.fk_staff_id AS staffId
        FROM
        r_client_agent AS sa
        INNER JOIN m_agent AS a ON a.id = sa.fk_agent_id
        LEFT JOIN r_agent_staff AS s ON s.fk_agent_id = a.id AND s.is_active = 1
        where sa.is_active = 1
        <if test="id != null">
            AND  sa.fk_client_id = #{id}
        </if>
    </select>

    <select id="getAgentNameByClientIds" resultType="com.get.salecenter.vo.ClientAgentVo">
        SELECT s.fk_client_id,group_concat(distinct CONCAT(a.`name`,IF(a.name_note is null or a.name_note = "","",CONCAT("（",a.name_note,"）")))) AS agentName
        FROM  r_client_agent s
        left join m_agent a on a.id=s.fk_agent_id
        where s.is_active=1 and s.fk_client_id in
        <foreach collection="clientIds" item="clientId" index="index" open="(" separator="," close=")">
            #{clientId}
        </foreach>
        group by s.fk_client_id
    </select>

    <select id="getBdCodeByClientIds" resultType="com.get.salecenter.vo.ClientAgentVo">
        SELECT rsa.fk_client_id,group_concat(distinct d.bd_code) AS bdCode from r_staff_bd_code d
        left join r_agent_staff ras on ras.fk_staff_id = d.fk_staff_id
        left join r_client_agent rsa on ras.fk_agent_id=rsa.fk_agent_id
        where rsa.is_active=1 and ras.is_active=1 and rsa.fk_client_id in
        <foreach collection="clientIds" item="clientId" index="index" open="(" separator="," close=")">
            #{clientId}
        </foreach>
        group by rsa.fk_client_id
    </select>
    <select id="exitsClientAgent" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM r_client_agent WHERE fk_agent_id = #{fkAgentId} AND fk_client_id = #{fkClientId} AND is_active = #{isActive} LIMIT 1
    </select>
    <select id="datas" resultType="com.get.salecenter.vo.ClientAgentVo">
        SELECT
            CONCAT(
                a.`name`,

            IF (
                a.name_note IS NULL
                OR a.name_note = "",
                "",
                CONCAT("（", a.name_note, "）")
            )
            ) AS agentName,
            s.*
        FROM
            m_agent a
        LEFT JOIN r_client_agent s ON s.fk_agent_id = a.id
        WHERE
            s.fk_client_id = #{agentDto.fkClientId}
        ORDER BY
            a.is_active desc
    </select>

</mapper>