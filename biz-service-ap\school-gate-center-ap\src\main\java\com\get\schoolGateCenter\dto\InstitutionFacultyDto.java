package com.get.schoolGateCenter.dto;
import com.get.schoolGateCenter.entity.InstitutionFaculty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/3
 * @TIME: 15:30
 * @Description:学院dto
 **/
@Data
@ApiModel("学校学院返回类")
public class InstitutionFacultyDto extends InstitutionFaculty {
    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String institutionName;
    /**
     * 文件下载链接
     */
    @ApiModelProperty(value = "学院附件")
    private List<MediaAndAttachedDto> mediaAndAttachedDtos;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;
    /**
     * 全称
     */
    @ApiModelProperty(value = "全称")
    private String fullName;

    /**
     * 课程id
     */
    @ApiModelProperty(value = "课程id")
    private Long fkInstitutionCourseId;
}
