package com.get.partnercenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/12  10:46
 * @Version 1.0
 * partnerUser账户
 */
@Data
public class PartnerUserVo {

    @ApiModelProperty(value = "状态1启用0禁用")
    private Integer status;

    @ApiModelProperty(value = "伙伴用户ID")
    private Long partnerUserId;

    @ApiModelProperty(value = "系统用户ID")
    private Long userId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "角色")
    private String roleName;

    @ApiModelProperty(value = "角色IDs")
    private List<Long> roleIds;

    @ApiModelProperty(value = "角色名称")
    private List<String> roleNames;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "上司名称")
    private String superiorName;

    @ApiModelProperty(value = "上司名称-多个")
    private List<String> superiorNames;


    @ApiModelProperty(value = "国家线")
    private String country;

    @ApiModelProperty(value = "创建人")
    private String gmtCreateUser;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("创建时间")
    private Date gmtCreate;

}
