package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.ClientSourceTypeVo;
import com.get.salecenter.service.IClientSourceTypeService;
import com.get.salecenter.dto.ClientSourceTypeListDto;
import com.get.salecenter.dto.ClientSourceTypeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 学生资源推荐来源类型控制器
 */
@Api(tags = "学生资源推荐来源类型管理")
@RestController
@RequestMapping("sale/clientSourceType")
public class ClientSourceTypeController {

    @Resource
    private IClientSourceTypeService clientSourceTypeService;

    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生资源推荐来源类型管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(BaseVoEntity.Add.class) ClientSourceTypeDto clientSourceTypeDto) {
        return SaveResponseBo.ok(clientSourceTypeService.addClientSourceType(clientSourceTypeDto));
    }

    @ApiOperation(value = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生资源推荐来源类型管理/更新")
    @PostMapping("update")
    public ResponseBo<ClientSourceTypeVo> update(@RequestBody @Validated(BaseVoEntity.Update.class) ClientSourceTypeDto clientSourceTypeDto) {
        return UpdateResponseBo.ok(clientSourceTypeService.updateClientSourceType(clientSourceTypeDto));
    }

    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生资源推荐来源类型管理/删除")
    @PostMapping("delete")
    public ResponseBo delete(@RequestParam("id") Long id) {
        clientSourceTypeService.delete(id);
        return DeleteResponseBo.ok();
    }

    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生资源推荐来源类型管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<ClientSourceTypeVo> detail(@PathVariable("id") Long id) {
        ClientSourceTypeVo data = clientSourceTypeService.findClientSourceTypeById(id);
        return new ResponseBo<>(data);
    }

    @ApiOperation(value = "获取列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生资源推荐来源类型管理/列表")
    @PostMapping("datas")
    public ResponseBo<ClientSourceTypeVo> datas(@RequestBody SearchBean<ClientSourceTypeListDto> page) {
        List<ClientSourceTypeVo> datas = clientSourceTypeService.getClientSourceTypeList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生资源推荐来源类型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<ClientSourceTypeDto> clientSourceTypeDtos) {
        clientSourceTypeService.movingOrder(clientSourceTypeDtos);
        return ResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学生资源推荐来源类型下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生资源推荐来源类型管理/学生资源推荐来源类型下拉")
    @GetMapping("getClientSourceTypeSelect")
    public ResponseBo<ClientSourceTypeVo> getClientSourceTypeSelect() {
        return new ListResponseBo<>(clientSourceTypeService.getClientSourceTypeSelect());
    }
}
