package com.get.officecenter.dto.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class LeaveApplicationFormQueryDto {

    private String gmtCreateUser;


    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 部门Id
     */
    @ApiModelProperty(value = "部门Id")
    private Long fkDepartmentId;

    /**
     * 工休申请单类型Id
     */
    @ApiModelProperty(value = "工休申请单类型Id")
    private Long fkLeaveApplicationFormTypeId;

    /**
     * 状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废
     */
    @ApiModelProperty(value = "状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废")
    private Integer status;

    /**
     * 申请原因
     */
    @ApiModelProperty(value = "申请原因")
    private String reason;

    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空！")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空！")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    /**
     * 表单创建日期-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "表单创建日期-开始")
    private String createStartTime;

    /**
     * 表单创建日期-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "表单创建日期-结束")
    private String createEndTime;

    //自定义内容
    /**
     * 1查询全部，0查询个人,2 查询我的审批
     */
    @ApiModelProperty(value = "1查询全部，0查询个人,2 查询我的审批")
    private String selectStatus;

    //=============================
    /**
     * bms移动版 审批中，已完成 bigType: 'examining' | 'completed'
     */
    @ApiModelProperty(value = "bms移动版审批类型：审批中/已完成")
    private String bigType;


    /**
     * 工休申请单编号（系统生成）
     */
    @ApiModelProperty(value = "工休申请单编号（系统生成）")
    private String num;

}
