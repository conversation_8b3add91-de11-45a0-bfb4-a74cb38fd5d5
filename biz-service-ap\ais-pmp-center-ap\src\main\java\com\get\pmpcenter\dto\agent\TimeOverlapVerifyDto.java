package com.get.pmpcenter.dto.agent;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/6/4
 * @Version 1.0
 * @apiNote:方案时间重叠校验Dto
 */
@Data
public class TimeOverlapVerifyDto {

    @ApiModelProperty(value = "学校提供商Id")
    @NotNull(message = "学校提供商Id不能为空")
    private Long institutionProviderId;

    @ApiModelProperty(value = "分公司Id")
    @NotNull(message = "分公司Id不能为空")
    private Long companyId;

    @ApiModelProperty(value = "学校Id集合")
    @NotNull(message = "学校Id不能为空")
    private List<Long> institutionIds;

    @ApiModelProperty(value = "方案有效时间（开始）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message = "方案开始时间不能为空")
    private Date startTime;

    @ApiModelProperty(value = "方案有效时间（结束）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "有效时间是否无时间限制：0否/1是")
    @NotNull(message = "有效时间是否无时间限制不能为空")
    private Integer isTimeless;

    @ApiModelProperty(value = "代理商等级Id")
    private Long fkAgentCommissionTypeId;
}
