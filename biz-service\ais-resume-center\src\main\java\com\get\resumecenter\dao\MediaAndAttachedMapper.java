package com.get.resumecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.resumecenter.entity.MediaAndAttached;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

;

@Mapper
public interface MediaAndAttachedMapper extends BaseMapper<MediaAndAttached> {

    Integer getNextIndexKey(@Param("tableId") Long tableId, @Param("tableName") String tableName);

    int insertSelective(MediaAndAttached record);

}