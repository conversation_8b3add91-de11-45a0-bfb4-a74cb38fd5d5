package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.InstitutionCourseFaculty;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstitutionCourseFacultyMapper extends BaseMapper<InstitutionCourseFaculty> {
    int insert(InstitutionCourseFaculty record);

    int insertSelective(InstitutionCourseFaculty record);

    /**
     * @Description：获取课程关联的学院
     * @Param
     * @Date 14:32 2021/4/27
     * <AUTHOR>
     */
    List<Long> getFacultyIdsByCourseId(@Param("id") Long id);

    /**
     * @Description：获取学院关联的课程
     * @Param
     * @Date 14:32 2021/4/27
     * <AUTHOR>
     */
    List<Long> getCourseIdsByFacultyId(@Param("id") Long id);

    /**
     * @Description：根据课程删除信息
     * @Param
     * @Date 14:32 2021/4/27
     * <AUTHOR>
     */
    void deleteByByCourseId(@Param("id") Long id);

    /**
     * @Description：根据学院统计
     * @Param
     * @Date 14:32 2021/4/27
     * <AUTHOR>
     */
    Integer getCountByFaculty(@Param("fkInstitutionFacultyId") Long fkInstitutionFacultyId);

    /**
     * @Description：根据课程判断是否存在
     * @Param
     * @Date 14:32 2021/4/27
     * <AUTHOR>
     */
    boolean isExistByCourseId(@Param("courseId") Long courseId);
}