package com.get.aisplatformcenter.mapper;

import com.get.aisplatformcenterap.dto.ULabelTypeDto;
import com.get.aisplatformcenterap.entity.ULabelTypeEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.aisplatformcenterap.vo.ULabelTypeExportVo;
import com.get.aisplatformcenterap.vo.ULabelTypeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【u_label_type】的数据库操作Mapper
* @createDate 2024-12-19 11:15:30
* @Entity com.get.aisplatformcenter.entity.ULabelTypeEntity
*/
@Mapper
public interface ULabelTypeMapper extends BaseMapper<ULabelTypeEntity> {
    Integer getMaxOrder();

    List<ULabelTypeExportVo> getExportLabelTypeInfo(@Param("uLabelTypeDto") ULabelTypeDto uLabelTypeDto);


}




