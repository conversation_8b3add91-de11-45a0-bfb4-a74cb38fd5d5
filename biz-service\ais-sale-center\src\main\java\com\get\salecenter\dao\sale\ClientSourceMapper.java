package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.salecenter.vo.ClientSourceListVo;
import com.get.salecenter.entity.ClientSource;
import com.get.salecenter.dto.ClientSourceListDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ClientSourceMapper extends BaseMapper<ClientSource> {

    /**
     * 列表
     * @param iPage
     * @param clientSourceListDto
     * @return
     */
    List<ClientSourceListVo> getClientSources(IPage<ClientSourceListVo> iPage, @Param("clientSourceListDto") ClientSourceListDto clientSourceListDto);
}