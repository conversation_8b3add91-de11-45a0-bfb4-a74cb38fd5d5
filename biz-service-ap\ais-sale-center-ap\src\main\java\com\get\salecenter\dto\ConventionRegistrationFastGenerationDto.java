package com.get.salecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 报名名册一键生成活动费用汇总、应收计划、发票 参数
 */
@Data
public class ConventionRegistrationFastGenerationDto extends BaseVoEntity {

    @ApiModelProperty(value = "公司Id")
    @NotNull(message = "公司Id不能为空")
    private Long fkCompanyId;

    @ApiModelProperty(value = "学校供应商（收款方）Id")
    @NotNull(message = "学校供应商（收款方）Id不能为空")
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "业务国家/地区")
    @NotEmpty(message = "业务国家/地区不能为空")
    private List<Long> fkAreaCountryIdList;

    @ApiModelProperty(value = "发票/应收/活动费用币种")
    @NotBlank(message = "发票/应收/活动费用币种不能为空")
    private String fkCurrencyTypeNumToAll;

    @ApiModelProperty(value = "发票/应收/活动费用金额")
    @NotNull(message = "发票/应收/活动费用金额不能为空")
    private BigDecimal amountToAll;

    @ApiModelProperty(value = "invoice名目")
    @NotBlank(message = "invoice名目不能为空")
    private String invoiceSummary;

    @ApiModelProperty(value = "invoice收件人")
    @NotBlank(message = "invoice收件人不能为空")
    private String invoiceContactPerson;

    @ApiModelProperty(value = "活动年份")
    @NotNull(message = "活动年份不能为空")
    private Integer eventYear;

    @ApiModelProperty(value = "发票编号")
    @NotBlank(message = "发票编号不能为空")
    private String fkInvoiceNum;

}
