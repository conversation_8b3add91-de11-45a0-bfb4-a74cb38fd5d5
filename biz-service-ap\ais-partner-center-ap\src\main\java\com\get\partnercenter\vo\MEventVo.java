package com.get.partnercenter.vo;

import com.get.partnercenter.entity.MEventEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

@Data
public class MEventVo extends MEventEntity {

    /**
     * 文件guid(文档中心)
     */
    private String fileGuid;
    @ApiModelProperty("文件路径")
    private String fileKey;
    @ApiModelProperty("活动举办国家名称")
    private String areaCountryName;
    @ApiModelProperty("活动举办州省名称")
    private String areaStateName;
    @ApiModelProperty("活动举办城市名称")
    private String areaCityName;
    @ApiModelProperty("活动类型名称")
    private String typeName;

    @ApiModelProperty("公司名称")
    private String companyName;

    private String comment;

    @ApiModelProperty("描述（富文本）")
    private String description;

    @ApiModelProperty("活动负责人名称")
    private String tartgerName;



    @ApiModelProperty("活动-桶地址")
    private List<FileArray> eventFile;

    public String getDescription() {
        if(comment!=null && comment.length()>0){
            description=comment;
        }
        return description;
    }
}
