package com.get.permissioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.permissioncenter.dto.ResourceDto;
import com.get.permissioncenter.vo.ResourceVo;
import com.get.permissioncenter.service.IResourceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * @author: jack
 * @create: 2020/6/8
 * @verison: 1.0
 * @description: 系统资源控制器
 */
@Api(tags = "系统资源管理")
@RestController
@RequestMapping("system/resource")
public class ResourceController {

    @javax.annotation.Resource
    private IResourceService resourceService;

    /**
     * 列表数据
     *
     * @return
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.LIST, description = "系统中心/系统资源管理/查询资源")
    @PostMapping("datas")
    public ResponseBo<ResourceVo> datas(@RequestBody ResourceDto resourceDto) {
        List<ResourceVo> datas = resourceService.getResources(resourceDto);
        return new ListResponseBo<>(datas);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.DETAIL, description = "系统中心/系统资源管理/资源详情")
    @GetMapping("/{id}")
    public ResponseBo<ResourceVo> detail(@PathVariable("id") Long id) {
        ResourceVo data = resourceService.findResourceById(id);
        return new ResponseBo<>(data);
    }


    /**
     * 新增信息
     *
     * @param resourceDto
     * @return
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.ADD, description = "系统中心/系统资源管理/新增资源")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(ResourceDto.Add.class) ResourceDto resourceDto) {
        return SaveResponseBo.ok(this.resourceService.addResource(resourceDto));
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.DELETE, description = "系统中心/系统资源管理/删除资源")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        this.resourceService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param resourceDto
     * @return
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.EDIT, description = "系统中心/系统资源管理/更新资源")
    @PostMapping("update")
    public ResponseBo<ResourceVo> update(@RequestBody @Validated(ResourceDto.Update.class) ResourceDto resourceDto) {
        return UpdateResponseBo.ok(resourceService.updateResource(resourceDto));
    }


    /**
     * 批量新增信息
     *
     * @param resources
     * @return
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.ADD, description = "系统中心/系统资源管理/批量保存")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(ResourceDto.Add.class) ValidList<ResourceDto> resources) {
        resourceService.batchAdd(resources);
        return ResponseBo.ok();
    }

    /**
     * 上移下移
     *
     * @param resources
     * @return
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.EDIT, description = "系统中心/系统资源管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<ResourceDto> resources) {
        resourceService.movingOrder(resources);
        return ResponseBo.ok();
    }


    /**
     * 通过关键词获取系统资源
     *
     * @param keyword
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiIgnore
    @PostMapping("getResources")
    public ListResponseBo getResources(@RequestParam(required = false) String keyword) {
        return new ListResponseBo<>(resourceService.getResources(keyword));
    }

    /**
     * 通过资源key获取后端权限
     *
     * @param resourcekeys
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiIgnore
    @PostMapping("getApiKeysByResourceKeys")
    public ResponseBo getApiKeysByResourceKeys(@RequestParam(required = false) List<String> resourcekeys) {
        List<String> apikeys = resourceService.getApiKeysByResourceKeys(resourcekeys);
        return new ListResponseBo<>(apikeys);
    }

    /**
     * 资源树
     *
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "资源树", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.LIST, description = "系统中心/系统资源管理/获取资源树")
    @PostMapping("getResourceTree")
    public ResponseBo<ResourceVo> getResourceTree() {
        List<ResourceVo> datas = resourceService.getResourceTree();
        return new ListResponseBo<>(datas);
    }

    /**
     * 修改信息
     *
     * @param type
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "修改翻译设置接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.EDIT, description = "系统中心/修改翻译设置接口")
    @PostMapping("updateTranslationConfig")
    public ResponseBo updateTranslationConfig(@RequestParam String type) {
        resourceService.updateTranslationConfig(type);
        return UpdateResponseBo.ok();
    }
}
