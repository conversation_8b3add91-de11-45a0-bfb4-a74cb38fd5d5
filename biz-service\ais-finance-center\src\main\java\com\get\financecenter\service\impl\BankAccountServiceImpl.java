package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.BankAccountMapper;
import com.get.financecenter.vo.BankAccountVo;
import com.get.financecenter.entity.BankAccount;
import com.get.financecenter.service.IBankAccountService;
import com.get.financecenter.service.ICurrencyTypeService;
import com.get.financecenter.dto.BankAccountDto;
import com.get.financecenter.dto.query.BankAccountQueryDto;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;

/**
 * @author: Sea
 * @create: 2020/12/21 9:56
 * @verison: 1.0
 * @description:
 */
@Service
public class BankAccountServiceImpl extends BaseServiceImpl<BankAccountMapper, BankAccount> implements IBankAccountService {
    @Resource
    private BankAccountMapper bankAccountMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ICurrencyTypeService currencyTypeService;

    @Override
    public BankAccountVo findBankAccountById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        BankAccount bankAccount = bankAccountMapper.selectById(id);
        if (GeneralTool.isEmpty(bankAccount)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }

        BankAccountVo bankAccountVo = BeanCopyUtils.objClone(bankAccount, BankAccountVo::new);
        Result<String> result = permissionCenterClient.getCompanyNameById(bankAccountVo.getFkCompanyId());
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            //设置公司名称
            bankAccountVo.setCompanyName(result.getData());
        }
        //设置币种名称
        bankAccountVo.setCurrencyTypeName(currencyTypeService.getCurrencyNameByNum(bankAccountVo.getFkCurrencyTypeNum()));
        return bankAccountVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<BankAccountDto> bankAccountDtos) {
        if (GeneralTool.isEmpty(bankAccountDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //获取最大排序(防止每次都要去查最大值，所以在外面查出一次，循环几次 就自增几次)
        Integer maxViewOrder;
        for (BankAccountDto bankAccountDto : bankAccountDtos) {
            String msg = validateAdd(bankAccountDto);
            if (GeneralTool.isEmpty(msg)) {
                BankAccount bankAccount = BeanCopyUtils.objClone(bankAccountDto, BankAccount::new);
                maxViewOrder = bankAccountMapper.getMaxViewOrder(bankAccount.getFkCompanyId());
                bankAccount.setViewOrder(maxViewOrder);
                utilService.updateUserInfoToEntity(bankAccount);
                bankAccountMapper.insertSelective(bankAccount);
            } else {
                throw new GetServiceException(msg);
            }
        }
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (bankAccountMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        bankAccountMapper.deleteById(id);
    }

    @Override
    public BankAccountVo updateBankAccount(BankAccountDto bankAccountDto) {
        if (bankAccountDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        BankAccount result = bankAccountMapper.selectById(bankAccountDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        String msg = validateUpdate(bankAccountDto);
        if (GeneralTool.isEmpty(msg)) {
            BankAccount bankAccount = BeanCopyUtils.objClone(bankAccountDto, BankAccount::new);
            utilService.updateUserInfoToEntity(bankAccount);
            bankAccountMapper.updateById(bankAccount);
        } else {
            throw new GetServiceException(msg);
        }
        return findBankAccountById(bankAccountDto.getId());
    }

    @Override
    public List<BankAccountVo> getBankAccounts(BankAccountQueryDto bankAccountVo, Page page) {
//        Example example = new Example(BankAccount.class);
//        Example.Criteria criteria = example.createCriteria();
//        Example.Criteria criteria1 = example.createCriteria();
//        //不选所属公司时
//        if (GeneralTool.isEmpty(bankAccountVo) || GeneralTool.isEmpty(bankAccountVo.getFkCompanyId())) {
//            List<Long> companyIds = getCompanyIds();
//            criteria.andIn("fkCompanyId", companyIds);
//        }
//        if (GeneralTool.isNotEmpty(bankAccountVo)) {
//            //查询条件-所属公司
//            if (GeneralTool.isNotEmpty(bankAccountVo.getFkCompanyId())) {
//                PermissionUtils.validateCompany(bankAccountVo.getFkCompanyId());
//                criteria.andEqualTo("fkCompanyId", bankAccountVo.getFkCompanyId());
//            }
//            //查询条件-银行名称/账号名称/银行账号
//            if (GeneralTool.isNotEmpty(bankAccountVo.getKeyWord())) {
//                criteria1.andLike("bankName", "%" + bankAccountVo.getKeyWord() + "%");
//                criteria1.orLike("bankAccount", "%" + bankAccountVo.getKeyWord() + "%");
//                criteria1.orLike("bankAccountNum", "%" + bankAccountVo.getKeyWord() + "%");
//            }
//        }
//        example.and(criteria1);
//        example.orderBy("viewOrder").desc();
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<BankAccount> bankAccounts = bankAccountMapper.selectByExample(example);
//        page.restPage(bankAccounts);

        LambdaQueryWrapper<BankAccount> wrapper = new LambdaQueryWrapper();
        //不选所属公司时
        if (GeneralTool.isEmpty(bankAccountVo) || GeneralTool.isEmpty(bankAccountVo.getFkCompanyId())) {
            List<Long> companyIds = SecureUtil.getCompanyIdsByStaffId(GetAuthInfo.getStaffId());
            if (CollectionUtil.isNotEmpty(companyIds)) {
                wrapper.in(BankAccount::getFkCompanyId, companyIds);
            }
        }
        if (GeneralTool.isNotEmpty(bankAccountVo)) {
            //查询条件-公司ids
            if (GeneralTool.isNotEmpty(bankAccountVo.getFkCompanyIds())) {
                if (!SecureUtil.validateCompanys(bankAccountVo.getFkCompanyIds())) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
                }
                wrapper.in(BankAccount::getFkCompanyId, bankAccountVo.getFkCompanyIds());
            }
            //查询条件-所属公司
            if (GeneralTool.isNotEmpty(bankAccountVo.getFkCompanyId())) {
                if (!SecureUtil.validateCompany(bankAccountVo.getFkCompanyId())) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
                }
                wrapper.eq(BankAccount::getFkCompanyId, bankAccountVo.getFkCompanyId());
            }
            //查询条件-银行名称/账号名称/银行账号
            if (GeneralTool.isNotEmpty(bankAccountVo.getKeyWord())) {
                wrapper.and(wrapper_ ->
                        wrapper_.like(BankAccount::getBankName, bankAccountVo.getKeyWord()).or()
                                .like(BankAccount::getBankAccount, bankAccountVo.getKeyWord()).or()
                                .like(BankAccount::getBankAccountNum, bankAccountVo.getKeyWord()));
            }
        }
        wrapper.orderByDesc(BankAccount::getViewOrder);
        IPage<BankAccount> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<BankAccount> bankAccounts = pages.getRecords();

        List<BankAccountVo> convertDatas = new ArrayList<>();
        //公司id集合
        Set<Long> companyIds = new HashSet<>();
        //币种编号集合
        Set<String> currencyTypeNums = new HashSet<>();
        //获取各自的值
        for (BankAccount bankAccount : bankAccounts) {
            companyIds.add(bankAccount.getFkCompanyId());
            currencyTypeNums.add(bankAccount.getFkCurrencyTypeNum());
        }
        Map<String, String> currencyTypeNameMap = getCurrencyNameMap(currencyTypeNums);
        Map<Long, String> companyNameMap = getCompanyNameMap(companyIds);
        for (BankAccount bankAccount : bankAccounts) {
            BankAccountVo bankAccountDto = BeanCopyUtils.objClone(bankAccount, BankAccountVo::new);
            //feign调用返回的map 根据key-id获取对应value-名称 设置返回给前端
            bankAccountDto.setCurrencyTypeName(currencyTypeNameMap.get(bankAccountDto.getFkCurrencyTypeNum()));
            bankAccountDto.setCompanyName(companyNameMap.get(bankAccountDto.getFkCompanyId()));
            convertDatas.add(bankAccountDto);
        }
        return convertDatas;
    }

    @Override
    public void movingOrder(List<BankAccountDto> bankAccountDtos) {
        if (GeneralTool.isEmpty(bankAccountDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        BankAccount ro = BeanCopyUtils.objClone(bankAccountDtos.get(0), BankAccount::new);
        Integer oneorder = ro.getViewOrder();
        BankAccount rt = BeanCopyUtils.objClone(bankAccountDtos.get(1), BankAccount::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        bankAccountMapper.updateById(ro);
        bankAccountMapper.updateById(rt);

    }

    @Override
    public void isActive(Long bankAccountId, Boolean isActive) {
        if (GeneralTool.isEmpty(bankAccountId) || GeneralTool.isEmpty(bankAccountMapper.selectById(bankAccountId))) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        BankAccount bankAccount = new BankAccount();
        bankAccount.setId(bankAccountId);
        bankAccount.setIsActive(isActive);
        bankAccountMapper.updateById(bankAccount);
    }

    @Override
    public List<BankAccountVo> getBankAccountSelect(Long fkCompanyId) {
        return bankAccountMapper.getBankAccountSelect(fkCompanyId);
    }

    @Override
    public String getBankAccountNameById(Long id) {
        return null;
    }

    @Override
    public Map<Long, String> getBankAccountNameByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        List<BankAccountVo> bankAccountDtoList = bankAccountMapper.getBankNameByIds(ids);
        if (GeneralTool.isEmpty(bankAccountDtoList)) {
            return map;
        }
        for (BankAccountVo bankAccountVo : bankAccountDtoList) {
            map.put(bankAccountVo.getId(), bankAccountVo.getBankName());
        }
        return map;
    }

    private String validateAdd(BankAccountDto bankAccountDto) {
//        Example example = new Example(BankAccount.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("bankAccount", bankAccountVo.getBankAccount());
//        criteria.orEqualTo("bankAccountNum", bankAccountVo.getBankAccountNum());
//        List<BankAccount> list = this.bankAccountMapper.selectByExample(example);
        LambdaQueryWrapper<BankAccount> queryWrapper = Wrappers.<BankAccount>query()
                .lambda()
                .eq(BankAccount::getFkCompanyId, bankAccountDto.getFkCompanyId())
//                .and(wrapper -> wrapper
//                        .eq(BankAccount::getBankAccount, bankAccountVo.getBankAccount())
//                        .or()
//                        .eq(BankAccount::getBankAccountNum, bankAccountVo.getBankAccountNum())
//                );
                .eq(BankAccount::getBankAccountNum, bankAccountDto.getBankAccountNum());

        List<BankAccount> list = this.bankAccountMapper.selectList(queryWrapper);
        StringJoiner stringJoiner = new StringJoiner("，");
        for (BankAccount bankAccount : list) {
            if (bankAccount.getBankAccount().equals(bankAccountDto.getBankAccount())) {
                stringJoiner.add("银行账户名称已存在");
            }
            if (bankAccount.getBankAccountNum().equals(bankAccountDto.getBankAccountNum())) {
                stringJoiner.add("银行账号已存在");
            }
        }
        return stringJoiner.toString();
    }

    private String validateUpdate(BankAccountDto bankAccountDto) {
//        Example example = new Example(BankAccount.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("bankAccount", bankAccountVo.getBankAccount());
//        criteria.orEqualTo("bankAccountNum", bankAccountVo.getBankAccountNum());
//        List<BankAccount> list = this.bankAccountMapper.selectByExample(example);
        LambdaQueryWrapper<BankAccount> queryWrapper = Wrappers.<BankAccount>query()
                .lambda()
                .eq(BankAccount::getFkCompanyId, bankAccountDto.getFkCompanyId())
                .and(wrapper -> wrapper
                        .eq(BankAccount::getBankAccount, bankAccountDto.getBankAccount())
                        .or()
                        .eq(BankAccount::getBankAccountNum, bankAccountDto.getBankAccountNum())
                );

        List<BankAccount> list = this.bankAccountMapper.selectList(queryWrapper);
        StringJoiner stringJoiner = new StringJoiner("，");
        for (BankAccount bankAccount : list) {
            if (!bankAccountDto.getId().equals(bankAccount.getId())) {
                if (bankAccount.getBankAccount().equals(bankAccountDto.getBankAccount())) {
                    stringJoiner.add("银行账户名称已存在");
                }
                if (bankAccount.getBankAccountNum().equals(bankAccountDto.getBankAccountNum())) {
                    stringJoiner.add("银行账号已存在");
                }
            }
        }
        return stringJoiner.toString();
    }


    /**
     * @return java.util.Map<java.lang.String, java.lang.String>
     * @Description :feign调用一次查出全部对应币种名称
     * @Param [events]
     * <AUTHOR>
     */
    private Map<String, String> getCurrencyNameMap(Set<String> currencyTypeNums) {
        currencyTypeNums.removeIf(Objects::isNull);
        //调用一次查出全部对应币种名称
        return currencyTypeService.getCurrencyNamesByNums(currencyTypeNums);
    }

    /**
     * @return java.util.Map<java.lang.Long, java.lang.String>
     * @Description :feign调用一次 查出全部对应公司名称
     * @Param [companyIds]
     * <AUTHOR>
     */
    private Map<Long, String> getCompanyNameMap(Set<Long> companyIds) {
        companyIds.removeIf(Objects::isNull);
        Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(companyIds);
        if (result.isSuccess() && result.getData() != null) {
            return result.getData();
        }
        return new HashMap<>();//返回一个空的map
    }
}
