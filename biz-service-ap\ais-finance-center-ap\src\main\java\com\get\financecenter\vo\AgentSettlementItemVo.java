package com.get.financecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 代理佣金结算二级对象Dto
 *
 * <AUTHOR>
 * @date 2021/12/21 15:58
 */
@Data
public class AgentSettlementItemVo {
    @ApiModelProperty(value = "留学申请Partner对账单")
    private OfferSettlementVo offerSettlement;

    @ApiModelProperty(value = "留学申请List")
    private List<AgentSettlementOfferItemVo> agentSettlementOfferItemDtoList;

    @ApiModelProperty(value = "留学保险List")
    private List<AgentSettlementInsuranceVo> agentSettlementInsuranceDtoList;

    @ApiModelProperty(value = "留学住宿List")
    private List<AgentSettlementAccommodationVo> agentSettlementAccommodationDtoList;

    @ApiModelProperty(value = "留学服务费List")
    private List<AgentSettlementServiceFeeVo> agentSettlementServiceFeeDtoList;

}
