package com.get.salecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: Hardy
 * @create: 2022/11/22 16:54
 * @verison: 1.0
 * @description:
 */
@Data
public class AppAgentContactPersonUpdateDto extends BaseVoEntity{

    /**
     * 学生代理申请Id
     */
    @NotNull(message = "学生代理申请Id", groups = {Save.class})
    @ApiModelProperty(value = "学生代理申请Id")
    private Long fkAppAgentId;

    /**
     * 联系人类型Key，多值逗号隔开
     */
    @NotBlank(message = "联系人类型Key", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "联系人类型Key，多值逗号隔开")
    private String fkContactPersonTypeKey;

    /**
     * 名称
     */
    @NotBlank(message = "名称", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 性别：0女/1男
     */
    @NotNull(message = "性别", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "性别：0女/1男")
    private Integer gender;

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    private String department;

    /**
     * 职位
     */
    @ApiModelProperty(value = "职位")
    private String title;

    @NotBlank(message = "手机区号", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "手机区号")
    private String mobileAreaCode;

    /**
     * 移动电话
     */
    @NotBlank(message = "移动电话", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "移动电话")
    private String mobile;

    /**
     * Email
     */
    @NotBlank(message = "Email", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "Email")
    private String email;

    /**
     * 是否佣金邮件地址
     */
    @ApiModelProperty(value = "是否佣金邮件地址")
    @Column(name = "is_commission_email")
    private Boolean isCommissionEmail;

    /**
     * 是否新闻邮件地址
     */
    @ApiModelProperty(value = "是否新闻邮件地址")
    @Column(name = "is_news_email")
    private Boolean isNewsEmail;

    /**
     * 新闻邮件关系国家Ids串，英文逗号隔开：1,2,3
     */
    @ApiModelProperty(value = "新闻邮件关系国家Ids串，英文逗号隔开：1,2,3")
    @Column(name = "fk_area_country_ids_news")
    private String fkAreaCountryIdsNews;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    public interface Save {
    }

}
