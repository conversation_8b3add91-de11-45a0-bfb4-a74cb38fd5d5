package com.get.salecenter.service.impl;

import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.convention.ConventionMediaAndAttachedMapper;
import com.get.salecenter.entity.convention.ConventionMediaAndAttached;
import com.get.salecenter.service.ConventionMediaAndAttachedService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 峰会附件服务实现类
 */
@Service
public class ConventionMediaAndAttachedServiceImpl implements ConventionMediaAndAttachedService {
    @Resource
    private ConventionMediaAndAttachedMapper attachedMapper;

    @Override
    public void addMediaAndAttached(ConventionMediaAndAttached mediaAttachedVo) {
        if (GeneralTool.isEmpty(mediaAttachedVo.getFkTableName())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("tableName_null"));
        }
        ConventionMediaAndAttached andAttached = BeanCopyUtils.objClone(mediaAttachedVo, ConventionMediaAndAttached::new);
        Integer nextIndexKey = attachedMapper.getNextIndexKey(mediaAttachedVo.getFkTableId(), mediaAttachedVo.getFkTableName());
        nextIndexKey = GeneralTool.isNotEmpty(nextIndexKey) ? nextIndexKey : 0;
        //实例化对象
        andAttached.setIndexKey(nextIndexKey);
        andAttached.setGmtCreate(new Date());
        attachedMapper.insert(andAttached);
    }
}
