package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
public class BatchUpdateBindingAmountDto {


    @ApiModelProperty("发票id")
    @NotNull(message = "发票id不能为空")
    private Long invoiceId;

    @ApiModelProperty("应收ids")
    @NotNull(message = "请选择要修改的应收计划")
    private List<Long> receivableIds;

    @ApiModelProperty("汇率")
    @NotNull(message = "请填写汇率")
    private BigDecimal exchangeRate;
}
