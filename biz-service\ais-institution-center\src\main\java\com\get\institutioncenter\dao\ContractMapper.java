package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.institutioncenter.vo.ContractVo;
import com.get.institutioncenter.entity.Contract;
import com.get.institutioncenter.dto.query.ContractQueryDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ContractMapper extends BaseMapper<Contract> {

    int insertSelective(Contract record);


//    ContractVo selectById(Long id);

    /**
     * 提供商获取合同
     *
     * @Date 11:08 2021/4/16
     * <AUTHOR>
     */
    List<ContractVo> selectByProviderId(Long providerId);


    List<ContractVo> selectByContractVo(IPage<Contract> pages, @Param("contractDto") ContractQueryDto contractDto,
                                        @Param(value = "contractIds") List<Long> contractIds,
                                        @Param(value = "providerIds") List<Long> providerIds,
                                        @Param(value = "ids") List<Long> ids,
                                        @Param(value = "myApplication") Integer myApplication,
                                        @Param(value = "status") Integer status,
                                        @Param(value = "companyIds") List<Long> companyIds);

    void changeStatus(@Param("status") Integer status, @Param("tableName") String tableName, @Param("businessKey") Long businessKey);


    /**
     * 校验合同中是否有对应的学校提供商绑定
     *
     * @Date 11:08 2021/4/16
     * <AUTHOR>
     */
    boolean checkProviderInfoIsEmptyByProviderId(Long providerId);

    /**
     * 校验合同是否存在
     *
     * @Date 11:08 2021/4/16
     * <AUTHOR>
     */
    boolean isExistByCourseId(@Param("courseId") Long courseId);

    boolean getExistParentId(Long id);


    /**
     * 校验合同是否存在
     *
     * @Date 11:08 2021/4/16
     * <AUTHOR>
     */
    boolean isExistByInstitutionId(@Param("id") Long id);
}