package com.get.authentication.config;

import cloud.tianai.captcha.generator.ImageCaptchaGenerator;
import cloud.tianai.captcha.generator.impl.MultiImageCaptchaGenerator;
import cloud.tianai.captcha.generator.impl.transform.Base64ImageTransform;
import cloud.tianai.captcha.resource.ImageCaptchaResourceManager;
import cloud.tianai.captcha.resource.impl.DefaultImageCaptchaResourceManager;
import cloud.tianai.captcha.spring.application.DefaultImageCaptchaApplication;
import cloud.tianai.captcha.spring.application.ImageCaptchaApplication;
import cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaProperties;
import cloud.tianai.captcha.spring.plugins.secondary.SecondaryVerificationApplication;
import cloud.tianai.captcha.spring.store.CacheStore;
import cloud.tianai.captcha.validator.ImageCaptchaValidator;
import com.get.authentication.handle.ExtCaptchaApplicationService;
import com.get.authentication.handle.ExtImageCaptchaApplication;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class CaptchaManageConfiguration {

    @Autowired
    private CaptchaResourceConfig captchaResourceConfig;


    @Bean(name = "imageCaptchaGenerator")
    @Primary
    public ImageCaptchaGenerator imageCaptchaGenerator(){
        ImageCaptchaResourceManager imageCaptchaResourceManager = new DefaultImageCaptchaResourceManager();
        imageCaptchaResourceManager.setResourceStore(captchaResourceConfig);
        Base64ImageTransform imageTransform = new Base64ImageTransform();
        return new MultiImageCaptchaGenerator(imageCaptchaResourceManager,imageTransform).init(true);
    }

    @Bean(name = "extCaptchaApplicationService")
    public ExtCaptchaApplicationService extCaptchaApplicationService(ImageCaptchaGenerator captchaGenerator,
                                                                         ImageCaptchaValidator imageCaptchaValidator,
                                                                         CacheStore cacheStore,
                                                                         ImageCaptchaProperties prop){
        return new ExtImageCaptchaApplication(captchaGenerator, imageCaptchaValidator, cacheStore, prop);
    }
}
