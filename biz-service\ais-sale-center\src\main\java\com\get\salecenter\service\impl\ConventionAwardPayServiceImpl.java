package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.Page;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.ConventionAwardCodeMapper;
import com.get.salecenter.dao.sale.ConventionAwardPayMapper;
import com.get.salecenter.vo.ConventionAwardPayVo;
import com.get.salecenter.entity.ConventionAwardCode;
import com.get.salecenter.service.IConventionAwardPayService;
import com.get.salecenter.dto.ConventionAwardPayDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/9/13 11:06
 * @verison: 1.0
 * @description:
 */
@Service
public class ConventionAwardPayServiceImpl implements IConventionAwardPayService {
    @Resource
    private ConventionAwardPayMapper conventionAwardPayMapper;
    @Resource
    private ConventionAwardCodeMapper conventionAwardCodeMapper;

    @Override
    public List<ConventionAwardPayVo> datas(ConventionAwardPayDto conventionAwardPayDto, Page page) {
        IPage<ConventionAwardPayVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<ConventionAwardPayVo> conventionAwardPayVos = conventionAwardPayMapper.datas(iPage, conventionAwardPayDto);
        page.setAll((int) iPage.getTotal());
        for (ConventionAwardPayVo conventionAwardPayVo : conventionAwardPayVos) {
            String role = null;
            if (GeneralTool.isNotEmpty(conventionAwardPayVo.getType())) {
                role = ProjectExtraEnum.getValueByKey(Integer.valueOf(conventionAwardPayVo.getType()), ProjectExtraEnum.CONVENTION_PERSON_TYPE);
            }
            conventionAwardPayVo.setPurchaser(conventionAwardPayVo.getName() + "(" + role + ")");
            List<ConventionAwardCode> conventionAwardCodes = conventionAwardCodeMapper.getBySystemCode(conventionAwardPayVo.getPaySystemOrderNum());
            StringBuffer sb = new StringBuffer();
            if (GeneralTool.isNotEmpty(conventionAwardCodes)) {
                for (ConventionAwardCode conventionAwardCode : conventionAwardCodes) {
                    sb.append(conventionAwardCode.getAwardCode()).append(" ");
                }
            }
            conventionAwardPayVo.setAwardCodes(sb.toString());
        }
//        PageInfo<ConventionAwardPayVo> pageInfo = new PageInfo<ConventionAwardPayVo>(conventionAwardPayVos);
//        page.setTotalResult(new Long(pageInfo.getTotal()).intValue());
        return conventionAwardPayVos;
    }
}
