<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.permissioncenter.dao.OfficeMapper">
    <resultMap id="BaseResultMap" type="com.get.permissioncenter.entity.Office">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_company_id" jdbcType="BIGINT" property="fkCompanyId"/>
        <result column="num" jdbcType="VARCHAR" property="num"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="hotLine" jdbcType="VARCHAR" property="hotline"/>
        <result column="tel" jdbcType="VARCHAR" property="tel"/>
        <result column="fax" jdbcType="VARCHAR" property="fax"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="zip_code" jdbcType="VARCHAR" property="zipCode"/>
        <result column="view_order" jdbcType="INTEGER" property="viewOrder"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, fk_company_id, num, name, hotLine, tel, fax, address, zip_code, view_order, gmt_create,
    gmt_create_user, gmt_modified, gmt_modified_user
    </sql>


    <insert id="insertSelective" parameterType="com.get.permissioncenter.entity.Office" useGeneratedKeys="true"
            keyProperty="id">
        insert into m_office
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkCompanyId != null">
                fk_company_id,
            </if>
            <if test="num != null">
                num,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="hotline != null">
                hotline,
            </if>
            <if test="tel != null">
                tel,
            </if>
            <if test="fax != null">
                fax,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="zipCode != null">
                zip_code,
            </if>
            <if test="viewOrder != null">
                view_order,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkCompanyId != null">
                #{fkCompanyId,jdbcType=BIGINT},
            </if>
            <if test="num != null">
                #{num,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="hotline != null">
                #{hotline,jdbcType=VARCHAR},
            </if>
            <if test="tel != null">
                #{tel,jdbcType=VARCHAR},
            </if>
            <if test="fax != null">
                #{fax,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="viewOrder != null">
                #{viewOrder,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="getStaffOfficeById" resultMap="BaseResultMap">
        SELECT * FROM  m_office o left join r_staff_office r
          on r.fk_office_id=o.id where r.fk_staff_id=#{staffId}
    </select>

    <select id="getMaxViewOrder" resultType="java.lang.Integer">
      select
        max(view_order)+1 view_order
      from
      m_office
    </select>

    <select id="getOfficeSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
         select
       id,num,name
      from
       m_office
      where
       fk_company_id = #{companyId}
      ORDER BY
       view_order asc
    </select>

    <select id="isExistByCompanyId" resultType="java.lang.Boolean">
      SELECT IFNULL(max(id),0) id FROM  m_office where fk_company_id =#{companyId}
    </select>

    <select id="getOfficeNameById" resultType="java.lang.String">
        select
         name
        from
         m_office
        where
         id = #{officeId}

    </select>
</mapper>