package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.salecenter.vo.ConventionHotelRoomListVo;
import com.get.salecenter.vo.ConventionHotelRoomPersonVo;
import com.get.salecenter.vo.ConventionPersonVo;
import com.get.salecenter.vo.LiveDateVo;
import com.get.salecenter.dto.ConventionHotelRoomDto;
import com.get.salecenter.dto.ConventionHotelRoomPersonDto;
import com.get.salecenter.dto.ConventionPersonDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/24 10:34
 * @verison: 1.0
 * @description:
 */
public interface IConventionHotelRoomPersonService {
    /**
     * 配置床位
     *
     * @param conventionHotelRoomPersonDto
     */
    void configurationBed(ConventionHotelRoomPersonDto conventionHotelRoomPersonDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 修改
     *
     * @param conventionHotelRoomPersonDto
     */
    void update(ConventionHotelRoomPersonDto conventionHotelRoomPersonDto);

    /**
     * 床位配置详情
     *
     * @param id
     * @return
     */
    ConventionHotelRoomPersonVo findconventionHotelRoomPersonById(Long id);

    /**
     * 获取房间数据集合
     *
     * @param conventionHotelRoomDto
     * @return
     * @
     */
    List<ConventionHotelRoomListVo> getHotelRoomsAndPersons(ConventionHotelRoomDto conventionHotelRoomDto);

    /**
     * 导出酒店住房安排
     * @param response
     * @param conventionHotelRoomDto
     */
    void exportConventionHotelRoomPersonExcel(HttpServletResponse response, ConventionHotelRoomDto conventionHotelRoomDto);

    /**
     * @return java.util.List<java.lang.String>
     * @Description :获取入住时间集合
     * @Param [conventionId]
     * <AUTHOR>
     */
    List<String> getDates(Long conventionId);

    /**
     * @return java.lang.Integer
     * @Description :查找该日期和房型下，所有房间已经被安排得床位数
     * @Param [roomIds]
     * <AUTHOR>
     */
    Integer getArrangedBedCount(List<Long> roomIds,List<Integer> types);

    /**
     * @return java.util.List<com.get.salecenter.vo.ConventionPersonVo>
     * @Description :房间未安排人员详细
     * @Param [data, page, roomDate]
     * <AUTHOR>
     */
    List<ConventionPersonVo> getRoomNotArrangedPersonList(ConventionPersonDto conventionPersonDto, Page page, String roomDate);

    /**
     * @return java.lang.Integer
     * @Description :查找该日期和房型下，被安排的房间数
     * @Param [roomIds]
     * <AUTHOR>
     */
    Integer getUsedRoomCount(List<Long> roomIds,List<Integer> types);

    /**
     * @return void
     * @Description :批量移除住房人员
     * @Param [conventionPersonId]
     * <AUTHOR>
     */
    void batchRemovePerson(Long conventionPersonId, Long conventionHotelId, String systemRoomNum);

    /**
     * 参会人签到
     *
     * @param conventionHotelRoomPersonDtoList
     */
    void getPersonSign(List<ConventionHotelRoomPersonDto> conventionHotelRoomPersonDtoList);

    /**
     * 获取日期
     *
     * @param conventionHotelRoomDto
     * @return
     */
    List<LiveDateVo> getUsableDateList(ConventionHotelRoomDto conventionHotelRoomDto);

    /**
     * 参会人签到信息
     *
     * @param conventionPersonId
     * @return
     * @
     */
    List<ConventionPersonVo> getSignInfo(Long conventionPersonId);

    void configurationBeds(List<ConventionHotelRoomPersonDto> conventionHotelRoomPersonDtoList);
}
