package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("m_event_cost")
public class EventCost extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 活动Id
     */
    @ApiModelProperty(value = "活动Id")
    @Column(name = "fk_event_id")
    private Long fkEventId;
    /**
     * 学校提供商Id
     */
    @ApiModelProperty(value = "学校提供商Id")
    @Column(name = "fk_institution_provider_id")
    private Long fkInstitutionProviderId;

    /**
     * 活动费用账单Id
     */
//    @UpdateWithNull
    @ApiModelProperty(value = "活动费用账单Id")
    @Column(name = "fk_event_bill_id")
    private Long fkEventBillId;

    /**
     * 收款单Id（暂时无用）
     */
    @ApiModelProperty(value = "收款单Id（暂时无用）")
    @Column(name = "fk_receipt_form_id")
    private Long fkReceiptFormId;
    /**
     * 活动费用币种
     */
    @ApiModelProperty(value = "活动费用币种")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;
    /**
     * 活动费用
     */
    @ApiModelProperty(value = "活动费用")
    @Column(name = "amount")
    private BigDecimal amount;
    /**
     * 折合收款汇率
     */
    @ApiModelProperty(value = "折合收款汇率")
    @Column(name = "exchange_rate_receivable")
    private BigDecimal exchangeRateReceivable;
    /**
     * 折合收款金额
     */
    @ApiModelProperty(value = "折合收款金额")
    @Column(name = "amount_receivable")
    private BigDecimal amountReceivable;

    /**
     * 汇率（折合港币）
     */
    @ApiModelProperty(value = "汇率（折合港币）")
    @Column(name = "exchange_rate_hkd")
    private BigDecimal exchangeRateHkd;

    /**
     * 收款金额（折合港币）
     */
    @ApiModelProperty(value = "收款金额（折合港币）")
    @Column(name = "amount_hkd")
    private BigDecimal amountHkd;

    /**
     * 汇率（折合人民币）
     */
    @ApiModelProperty(value = "汇率（折合人民币）")
    @Column(name = "exchange_rate_rmb")
    private BigDecimal exchangeRateRmb;

    /**
     * 收款金额（折合人民币）
     */
    @ApiModelProperty(value = "收款金额（折合人民币）")
    @Column(name = "amount_rmb")
    private BigDecimal amountRmb;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
}