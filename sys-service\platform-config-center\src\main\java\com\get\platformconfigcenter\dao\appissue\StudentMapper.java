package com.get.platformconfigcenter.dao.appissue;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.PlatFormStudent;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("issuedb")
public interface StudentMapper extends BaseMapper<PlatFormStudent> {

    int insertSelective(PlatFormStudent record);

}