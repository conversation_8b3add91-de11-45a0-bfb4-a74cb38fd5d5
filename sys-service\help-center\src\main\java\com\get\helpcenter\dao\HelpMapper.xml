<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.helpcenter.dao.HelpMapper">

  <insert id="insertSelective" parameterType="com.get.helpcenter.entity.Help" useGeneratedKeys="true" keyProperty="id">
    insert into m_help
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkParentHelpId != null">
        fk_parent_help_id,
      </if>
      <if test="fkHelpTypeId != null">
        fk_help_type_id,
      </if>
      <if test="keyCode != null">
        key_code,
      </if>
      <if test="showType != null">
        show_type,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="publicLevel != null">
        public_level,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="keyInfoName != null">
        key_info_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkParentHelpId != null">
        #{fkParentHelpId,jdbcType=BIGINT},
      </if>
      <if test="fkHelpTypeId != null">
        #{fkHelpTypeId,jdbcType=BIGINT},
      </if>
      <if test="keyCode != null">
        #{keyCode,jdbcType=VARCHAR},
      </if>
      <if test="showType != null">
        #{showType,jdbcType=INTEGER},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="publicLevel != null">
        #{publicLevel,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="keyInfoName != null">
        #{keyInfoName,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select ifnull(max(view_order)+1,1) from m_help
  </select>


  <select id="getAllChilHelpId" resultType="java.lang.Long">
    select id from m_help where fk_parent_help_id=#{fkParentHelpId}
  </select>


  <update id="updateByFkParentId">
    update m_help set fk_parent_help_id=#{fkParentHelpId} where id=#{fkHelpId}
  </update>

  <select id="getRelationHelpIds" resultType="java.lang.Long">
    select fk_help_id from r_parent_help where fk_parent_help_id=#{fkHelpId}
  </select>

    <select id="getHelpById" resultType="com.get.helpcenter.entity.Help">
      select * from m_help where  id = #{fkHelpId}
    </select>
    <select id="getHelpSelect" resultType="com.get.helpcenter.vo.HelpVo">
        select * from m_help
    </select>
    <select id="getHelpByHelpTypeId" resultType="com.get.helpcenter.vo.HelpVo">
      SELECT
        h.*
      FROM
        u_help_type AS ht
          INNER JOIN m_help AS h ON ht.id = h.fk_help_type_id where ht.id = #{fkHelpTypeId}
    </select>
  <select id="getRelationHelp" resultType="java.lang.Long">
    SELECT id FROM r_parent_help WHERE fk_help_id=#{fkHelpId} AND fk_parent_help_id=#{parentId} LIMIT 1
  </select>

  <insert id="insertParents" parameterType="com.get.helpcenter.entity.ParentHelp" useGeneratedKeys="true" keyProperty="id">
    insert into r_parent_help
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fkHelpId!=null">
        fk_help_id,
      </if>
      <if test="parentId!=null">
        fk_parent_help_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fkHelpId!=null">
        #{fkHelpId,jdbcType=BIGINT},
      </if>
      <if test="parentId!=null">
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <delete id="deleteParentIds">
    delete from r_parent_help where fk_parent_help_id=#{fkHelpId}
  </delete>
  <select id="getHelpByParentId" resultType="java.lang.Long">
    select id from m_help where fk_parent_help_id=#{fkHelpId}
  </select>
  <select id="getHelpInfo" resultType="com.get.helpcenter.vo.HelpInfoVo">
      SELECT key_code,description,show_type from m_help where show_type =0 or show_type =1
  </select>


</mapper>