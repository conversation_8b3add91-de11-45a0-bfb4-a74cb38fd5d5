package com.get.institutioncenter.service;


import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.dto.ContractFormulaDto;
import com.get.institutioncenter.vo.CompanyTreeVo;
import com.get.institutioncenter.vo.ContractFormulaCommissionVo;
import com.get.institutioncenter.vo.ContractFormulaVo;
import com.get.institutioncenter.vo.ContractFormulaFeignVo;
import com.get.institutioncenter.entity.ContractFormula;
import com.get.institutioncenter.dto.ContractFormulaCompanyDto;
import com.get.institutioncenter.dto.InstitutionStudentOfferItemDto;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2021/1/8 10:12
 * @verison: 1.0
 * @description:
 */
public interface IContractFormulaService extends BaseService<ContractFormula> {

    /**
     * @return com.get.institutioncenter.vo.ContractFormulaVo
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    ContractFormulaVo findContractFormulaById(Long id);

    /**
     * @return void
     * @Description :新增
     * @Param [contractFormulaDto]
     * <AUTHOR>
     */
    Long addContractFormula(ContractFormulaDto contractFormulaDto);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.institutioncenter.vo.ContractFormulaVo
     * @Description :修改
     * @Param [contractFormulaDto]
     * <AUTHOR>
     */
    ContractFormulaVo updateContractFormula(ContractFormulaDto contractFormulaDto);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.ContractFormulaVo>
     * @Description :列表
     * @Param [contractFormulaDto, page]
     * <AUTHOR>
     */
    List<ContractFormulaVo> getContractFormulas(ContractFormulaDto contractFormulaDto, Page page);

    /**
     * @return void
     * @Description :上移下移
     * @Param [contractFormulaDtos]
     * <AUTHOR>
     */
    void movingOrder(List<ContractFormulaDto> contractFormulaDtos);

    /**
     * @return void
     * @Description :合同公式-安全配置
     * @Param [contractFormulaCompanyDtos]
     * <AUTHOR>
     */
    void editContractFormulaCompany(List<ContractFormulaCompanyDto> contractFormulaCompanyDtos);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.CompanyTreeVo>
     * @Description :合同公式-安全配置详情
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<CompanyTreeVo> getContractFormulaCompany(Long contractFormulaId);

    /**
     * @Description :公式类型下拉
     * @Param []
     * <AUTHOR>
     */
    List<Map<String, Object>> findFormulaType();

    /**
     * @Description :条件类型下拉
     * @Param []
     * <AUTHOR>
     */
    List<Map<String, Object>> findConditionType();

    /**
     * @Description :统计类型下拉
     * @Param []
     * <AUTHOR>
     */
    List<Map<String, Object>> findCountType();

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description :根据学校提供商获取合同公式下拉框数据
     * @Param [institutionProviderId]
     * <AUTHOR>
     */
    List<BaseSelectEntity> getContractFormulaList(Long institutionProviderId);

    /**
     * @Description :feign调用 通过合同公式ids 查找对应的合同公式map
     * @Param [ids]
     * <AUTHOR>
     */
    Map<Long, String> getContractFormulasByIds(Set<Long> ids);

    /**
     * @return java.util.List<com.get.institutioncenter.entity.ContractFormula>
     * @Description: 获取符和学习计划的合同公式
     * @Param [companyId, studentOfferItem]
     * <AUTHOR>
     **/
    List<ContractFormula> getContractFormulasByOfferItem(Long companyId, Long areaCountryId, InstitutionStudentOfferItemDto studentOfferItem);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 通过合同公式查询课程id
     * @Param [companyId, studentOfferItem]
     * <AUTHOR>
     **/
    List<Long> getCourseIdsByContractFormulaId(Long id);

    /**
     * feign调用 通过合同公式查询合同公式佣金配置
     *
     * @Date 10:36 2021/6/9
     * <AUTHOR>
     */
    List<ContractFormulaCommissionVo> getContractFormulaCommissionByContractFormulaId(Long id);

    /**
     * feign调用 通过合同公式查询前置学校
     *
     * @Date 16:03 2021/6/10
     * <AUTHOR>
     */
    List<Long> getContractFormulaPreInstitutionByContractFormulaId(Long id);

    /**
     * feign调用 通过合同公式查询前置集团
     *
     * @Date 16:03 2021/6/10
     * <AUTHOR>
     */
    List<Long> getPreInstitutionGroupByContractFormulaId(Long id);

    /**
     * feign调用 通过合同公式查询前置课程等级
     *
     * @Date 16:03 2021/6/10
     * <AUTHOR>
     */
    List<Long> getPreMajorLevelByContractFormulaId(Long id);

    /**
     * feign调用 通过合同公式id查询合同公式  学校课程信息
     *
     * @Date 12:13 2021/6/15
     * <AUTHOR>
     */
    ContractFormulaFeignVo getContractFormulaConfigByContractFormulaId(Long formulaId);

    /**
     * 初步检查学习计划-合同公式匹配
     *
     * @Date 16:13 2021/7/16
     * <AUTHOR>
     */
    String checkContractFormula(Long studentCompanyId, Long areaCountryId, Long fkInstitutionCourseId, Long contractFormulaId);

    /**
     * feign调用 根据合同公式Id获取合同公式信息
     *
     * @Date 11:26 2021/7/19
     * <AUTHOR>
     */
    /*com.get.common.entity.fegin.ContractFormula getContractFormulaByFormulaId(@RequestParam(value = "formulaId") Long formulaId) ;*/

    /**
     * feign调用 根据合同公式Id获取合同公式信息
     *
     * @Date 11:26 2021/7/19
     * <AUTHOR>
     */
    ContractFormula getContractFormulaByFormulaId(@RequestParam(value = "formulaId") Long formulaId);

}
