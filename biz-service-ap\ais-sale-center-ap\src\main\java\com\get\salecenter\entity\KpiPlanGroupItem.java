package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2024/4/16
 * @TIME: 12:32
 * @Description:KPI方案组别子项
 **/
@Data
@TableName("m_kpi_plan_group_item")
@ApiModel(value="KPI方案组别子项", description="")
public class KpiPlanGroupItem extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "KPI方案Id")
    private Long fkKpiPlanId;

    @ApiModelProperty(value = "KPI方案组别Id")
    private Long fkKpiPlanGroupId;

    @ApiModelProperty(value = "业务国家Id")
    @UpdateWithNull
    private String fkAreaCountryIdsKpi;

    @ApiModelProperty(value = "学校提供商Id")
    @UpdateWithNull
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "专业等级Ids(多选)，格式：1,2,3")
    private String fkMajorLevelIds;

    @ApiModelProperty(value = "只统计直录（没有子计划的主课）0否/1是")
    private Boolean isDirect;

    @ApiModelProperty(value = "申请国家Ids(多选)，格式：1,2,3")
    private String fkAreaCountryIds;

    @ApiModelProperty(value = "国家包含类型：null没设置/0不包含/1包含")
    @UpdateWithNull
    private Integer countryIncludeType;

    @ApiModelProperty(value = "剔除提供商下的学校Ids(多选)，格式：1,2,3")
    private String fkInstitutionIds;

    @ApiModelProperty(value = "学生创建时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    @UpdateWithNull
    private Date studentCreateTimeStart;




    @ApiModelProperty(value = "学生创建时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    @UpdateWithNull
    private Date studentCreateTimeEnd;


    /**
     * 申请创建开始时间
     */
    @ApiModelProperty(value = "申请计划创建时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    @UpdateWithNull
    private Date offerItemCreateTimeStart;

    /**
     * 申请创建结束时间
     */
    @ApiModelProperty(value = "申请计划结束时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    @UpdateWithNull
    private Date offerItemCreateTimeEnd;



    @ApiModelProperty(value = "入学时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    @UpdateWithNull
    private Date intakeTimeStart;

    @ApiModelProperty(value = "入学时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    @UpdateWithNull
    private Date intakeTimeEnd;

    @ApiModelProperty(value = "步骤登记时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    @UpdateWithNull
    private Date stepTimeStart;

    @ApiModelProperty(value = "步骤登记时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    @UpdateWithNull
    private Date stepTimeEnd;

    @ApiModelProperty(value = "目标设置（成功入学）")
    @TableField(fill = FieldFill.UPDATE)
    private Integer targetEnrolled;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "是否统计后续课程：0否/1是，默认0不做统计")
    private Boolean  isFollow;

    @ApiModelProperty(value = "学校包含类型：null没设置/0不包含/1包含")
    private Integer institutionIncludeType;
}
