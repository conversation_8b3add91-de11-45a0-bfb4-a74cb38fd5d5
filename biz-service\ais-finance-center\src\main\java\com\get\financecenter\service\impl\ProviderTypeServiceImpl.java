package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.ProviderTypeMapper;
import com.get.financecenter.vo.ProviderTypeVo;
import com.get.financecenter.entity.ProviderType;
import com.get.financecenter.service.IProviderTypeService;
import com.get.financecenter.dto.ProviderTypeDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

;

/**
 * @author: Sea
 * @create: 2020/12/18 10:25
 * @verison: 1.0
 * @description:
 */
@Service
public class ProviderTypeServiceImpl extends BaseServiceImpl<ProviderTypeMapper, ProviderType> implements IProviderTypeService {
    @Resource
    private ProviderTypeMapper providerTypeMapper;
    @Resource
    private UtilService utilService;

    @Override
    public ProviderTypeVo findProviderTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ProviderType providerType = providerTypeMapper.selectById(id);
        if (GeneralTool.isEmpty(providerType)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        return BeanCopyUtils.objClone(providerType, ProviderTypeVo::new);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<ProviderTypeDto> providerTypeDtos) {
        if (GeneralTool.isEmpty(providerTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //获取最大排序(防止每次都要去查最大值，所以在外面查出一次，循环几次 就自增几次)
        Integer maxViewOrder = providerTypeMapper.getMaxViewOrder();
        for (ProviderTypeDto providerTypeDto : providerTypeDtos) {
            if (GeneralTool.isEmpty(providerTypeDto.getId())) {
                if (validateAdd(providerTypeDto)) {
                    ProviderType providerType = BeanCopyUtils.objClone(providerTypeDto, ProviderType::new);
                    providerType.setViewOrder(maxViewOrder);
                    utilService.updateUserInfoToEntity(providerType);
                    providerTypeMapper.insertSelective(providerType);
                    maxViewOrder++;
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("type_name_exists"));
                }
            } else {
                if (validateUpdate(providerTypeDto)) {
                    ProviderType providerType = BeanCopyUtils.objClone(providerTypeDto, ProviderType::new);
                    utilService.updateUserInfoToEntity(providerType);
                    providerTypeMapper.updateById(providerType);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("type_name_exists"));
                }
            }
        }
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (providerTypeMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        int i = providerTypeMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Override
    public ProviderTypeVo updateProviderType(ProviderTypeDto providerTypeDto) {
        if (providerTypeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ProviderType result = providerTypeMapper.selectById(providerTypeDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (validateUpdate(providerTypeDto)) {
            ProviderType providerType = BeanCopyUtils.objClone(providerTypeDto, ProviderType::new);
            utilService.updateUserInfoToEntity(providerType);
            providerTypeMapper.updateById(providerType);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("type_name_exists"));
        }
        return findProviderTypeById(providerTypeDto.getId());
    }

    @Override
    public List<ProviderTypeVo> getProviderTypes(ProviderTypeDto providerTypeDto, Page page) {
//        Example example = new Example(ProviderType::new);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(providerTypeVo)) {
//            //查询条件-类型名称
//            if (GeneralTool.isNotEmpty(providerTypeVo.getTypeName())) {
//                criteria.andLike("typeName", "%" + providerTypeVo.getTypeName() + "%");
//            }
//        }
//        example.orderBy("viewOrder").desc();
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<ProviderType> providerTypes = providerTypeMapper.selectByExample(example);
//        page.restPage(providerTypes);
        LambdaQueryWrapper<ProviderType> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(providerTypeDto)) {
            //查询条件-类型名称
            if (GeneralTool.isNotEmpty(providerTypeDto.getTypeName())) {
                wrapper.like(ProviderType::getTypeName, providerTypeDto.getTypeName());
            }
        }
        wrapper.orderByDesc(ProviderType::getViewOrder);
        IPage<ProviderType> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<ProviderType> providerTypes = pages.getRecords();
        page.setAll((int) pages.getTotal());
        return providerTypes.stream().map(providerType -> BeanCopyUtils.objClone(providerType, ProviderTypeVo::new)).collect(Collectors.toList());
    }

    @Override
    public void movingOrder(List<ProviderTypeDto> providerTypeDtos) {
        if (GeneralTool.isEmpty(providerTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ProviderType ro = BeanCopyUtils.objClone(providerTypeDtos.get(0), ProviderType::new);
        Integer oneorder = ro.getViewOrder();
        ProviderType rt = BeanCopyUtils.objClone(providerTypeDtos.get(1), ProviderType::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        providerTypeMapper.updateById(ro);
        providerTypeMapper.updateById(rt);
    }

    @Override
    public List<ProviderTypeVo> getProviderTypeList() {
//        Example example = new Example(ProviderType::new);
//        example.orderBy("viewOrder").desc();
//        List<ProviderType> providerTypes = providerTypeMapper.selectByExample(example);
        List<ProviderType> providerTypes = providerTypeMapper.selectList(Wrappers.<ProviderType>query().lambda().orderByDesc(ProviderType::getViewOrder));
        return providerTypes.stream().map(providerType -> BeanCopyUtils.objClone(providerType, ProviderTypeVo::new)).collect(Collectors.toList());
    }

    @Override
    public String getProviderTypeNameById(Long providerTypeId) {
        return providerTypeMapper.getProviderTypeNameById(providerTypeId);
    }

    @Override
    public Map<Long, String> getProviderTypeNameByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
//        Example example = new Example(ProviderType::new);
//        example.createCriteria().andIn("id",ids);
//        List<ProviderType> providerTypes = providerTypeMapper.selectByExample(example);
        List<ProviderType> providerTypes = providerTypeMapper.selectBatchIds(ids);
        if (GeneralTool.isEmpty(providerTypes)) {
            return map;
        }
        for (ProviderType providerType : providerTypes) {
            map.put(providerType.getId(), providerType.getTypeName());
        }
        return map;
    }

    private boolean validateAdd(ProviderTypeDto providerTypeDto) {
//        Example example = new Example(ProviderType::new);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("typeName", providerTypeVo.getTypeName());
//
//        List<ProviderType> list = this.providerTypeMapper.selectByExample(example);
        List<ProviderType> list = this.providerTypeMapper.selectList(Wrappers.<ProviderType>query().lambda().eq(ProviderType::getTypeName, providerTypeDto.getTypeName()));
        return GeneralTool.isEmpty(list);
    }

    private boolean validateUpdate(ProviderTypeDto providerTypeDto) {
//        Example example = new Example(ProviderType::new);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("typeName", providerTypeVo.getTypeName());
//        List<ProviderType> list = this.providerTypeMapper.selectByExample(example);
        List<ProviderType> list = this.providerTypeMapper.selectList(Wrappers.<ProviderType>query().lambda().eq(ProviderType::getTypeName, providerTypeDto.getTypeName()));
        return list.size() <= 0 || list.get(0).getId().equals(providerTypeDto.getId());
    }
}
