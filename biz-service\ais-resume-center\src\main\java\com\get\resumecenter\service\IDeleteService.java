package com.get.resumecenter.service;


/**
 * <AUTHOR>
 * @DATE: 2020/12/28
 * @TIME: 15:57
 * @Description:
 **/
public interface IDeleteService {

    /**
     * @return java.lang.Boolean
     * @Description: 删除简历数据校验
     * @Param [agentId]
     * <AUTHOR>
     */
    Boolean deleteValidateResume(Long resumeId);


    /**
     * @return java.lang.Boolean
     * @Description: 删除行业类型校验
     * @Param [industryTypeId]
     * <AUTHOR>
     */
    Boolean deleteValidateIndustryType(Long industryTypeId);


    /**
     * @return java.lang.Boolean
     * @Description: 删除简历附加信息主题类型校验
     * @Param [industryTypeId]
     * <AUTHOR>
     */
    Boolean deleteValidateOtherType(Long otherTypeId);


    /**
     * @return java.lang.Boolean
     * @Description: 删除简历类型校验
     * @Param [industryTypeId]
     * <AUTHOR>
     */
    <PERSON><PERSON><PERSON> deleteValidateResumeType(Long resumeTypeId);


    /**
     * @return java.lang.Boolean
     * @Description: 删除技能类型校验
     * @Param [industryTypeId]
     * <AUTHOR>
     */
    Boolean deleteValidateSkillType(Long skillTypeId);


}
