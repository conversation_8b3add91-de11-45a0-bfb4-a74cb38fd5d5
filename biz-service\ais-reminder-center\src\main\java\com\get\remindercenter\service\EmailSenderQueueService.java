package com.get.remindercenter.service;

import com.get.remindercenter.dto.EmailParamsDto;
import com.get.remindercenter.dto.RemindTaskDto;
import com.get.remindercenter.entity.EmailSenderQueue;

import java.util.Date;
import java.util.List;

/**
 * 邮件发送器队列业务层
 */
public interface EmailSenderQueueService {

    /**
     * 发送邮件
     * @param
     * @return
     */
    Boolean sendMqEmail(EmailSenderQueue emailSenderQueue);


    /**
     * 查询要发送的邮件任务
     * @return
     */
    List<EmailSenderQueue> findDueEmailTasks();

    /**
     * 查询指定时间要发送的邮件任务
     * @return
     */
    List<EmailSenderQueue> getEmailSenderQueues();



    /**
     * 执行要发送的邮件任务
     */
    Boolean performEmailTasks(EmailSenderQueue emailSenderQueue);


    /**
     * @Description: 批量新增任务
     */
    Boolean batchAddEmailQueue(List<EmailSenderQueue> queueList);

    Boolean updateEmailQueue(EmailSenderQueue emailSenderQueue);

    void batchDelete(List<EmailSenderQueue> emailSenderQueues, Date optTime);


    void delete(EmailSenderQueue emailSenderQueue, Date optTime);
}
