package com.get.salecenter.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.*;
import com.get.salecenter.entity.PayablePlan;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.query.PayablePlanNewQueryDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Mapper
public interface PayablePlanMapper extends GetMapper<PayablePlan> {

    Long getPayablePlanLastId();


    /**
     * @return java.lang.Long
     * @Description: 根据计划id 查询公司id
     * @Param [planId]
     * <AUTHOR>
     */
    Long getCompanyIdByPlanId(Long planId);

    /**
     * @return java.lang.Long
     * @Description: 根据计划id 查询公司id
     * @Param [planId]
     * <AUTHOR>
     */
    List<StudentOfferItemVo> getCompanyIdByPlanIds(@Param("ids") Set<Long> ids);


//    /**
//     * 根据留学保险应付计划id查询公司id
//     * @param planId
//     * @return
//     */
//    Long getCompanyIdByInsurancePlanId(Long planId);
//
//    /**
//     * 根据留学住宿应付计划id查询公司id
//     * @param palnId
//     * @return
//     */
//    Long getCompanyIdByAccommodationPlanId(Long palnId);

    /**
     * 根据留学保险id查询公司id
     *
     * @param fkTypeTargetId
     * @return
     */
    Long getCompanyIdByInsuranceTargetId(Long fkTypeTargetId);

    /**
     * 根据留学保险id查询公司id
     *
     * @param ids
     * @return
     */
    List<StudentInsuranceVo> getCompanyIdByInsuranceTargetIds(@Param("ids")Set<Long> ids);

    /**
     * 根据留学住宿id查询公司id
     *
     * @param fkTypeTargetId
     * @return
     */
    Long getCompanyIdByAccommodationTargetId(Long fkTypeTargetId);

    /**
     * 根据留学住宿id查询公司id
     *
     * @param ids
     * @return
     */
    List<StudentAccommodationVo> getCompanyIdByAccommodationTargetIds(@Param("ids")Set<Long> ids);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据公司id查询应收计划ids
     * @Param [companyId]
     * <AUTHOR>
     */
    List<Long> getPlanIdByCompanyId(@Param("companyId") Long companyId, @Param("studentId") Long studentId);

    /**
     * @param fkCompanyIds
     * @param studentId
     * @return
     */
    List<Long> getPlanIdByCompanyIds(@Param("fkCompanyIds") List<Long> fkCompanyIds, @Param("studentId") Long studentId);


    /**
     * 根据公司id查询留学保险应付计划ids
     *
     * @param companyId
     * @return
     */
    List<Long> getStudentInsurancePlanIds(@Param("companyId") Long companyId, @Param("studentId") Long studentId);

    /**
     * 根据公司id查询留学保险应付计划ids
     *
     * @param fkCompanyIds
     * @return
     */
    List<Long> getStudentInsurancePlanIdsByCompanyIds(@Param("fkCompanyIds") List<Long> fkCompanyIds, @Param("studentId") Long studentId);


    /**
     * 根据公司id查询留学住宿应付计划ids
     *
     * @param companyId
     * @return
     */
    List<Long> getStudentAccommodationIds(@Param("companyId") Long companyId, @Param("studentId") Long studentId);

    /**
     * 根据公司id查询留学住宿应付计划ids
     *
     * @param fkCompanyIds
     * @param studentId
     * @return
     */
    List<Long> getStudentAccommodationIdsByCompanyIds(@Param("fkCompanyIds") List<Long> fkCompanyIds, @Param("studentId") Long studentId);


    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据学生名称模糊查询
     * @Param [studentName]
     * <AUTHOR>
     */
    List<Long> getPlanIdByStudentName(String studentName);


    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据代理模糊查询
     * @Param [agentId]
     * <AUTHOR>
     */
    List<Long> getPlanIdByAgentId(@Param(value = "agentIds") List<Long> agentIds);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据学生名称模糊查询
     * @Param [studentName]
     * <AUTHOR>
     */
    List<Long> getPlanIdByInstitutionId(@Param(value = "institutionIds") List<Long> institutionIds);


    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据学生名称模糊查询
     * @Param [studentName]
     * <AUTHOR>
     */
    List<Long> getPlanIdByCourseId(@Param(value = "courseIds") List<Long> courseIds);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据计划IDS查询目标IDS
     * @Param [studentName]
     * <AUTHOR>
     */
    List<Long> getFkTypeTargetIdByIds(@Param(value = "ids") Set<Long> ids);

    /**
     * 根据Ids获取应付计划列表
     *
     * @param ids
     * @return
     */
    List<PayablePlanVo> getPayablePlanByIds(@Param(value = "ids") Set<Long> ids);


    /**
     * @Description: 代理应付汇总统计列表
     * @Author: Jerry
     * @Date:17:20 2021/11/19
     */
    List<AgentPaySumVo> getAgentPaySumDatas(IPage<AgentPaySumVo> iPage, @Param("agentPaySumDto") AgentPaySumDto agentPaySumDto);


    /**
     * @Description: 代理应付汇总统计明细
     * @Author: Jerry
     * @Date:12:02 2021/11/22
     */
    List<PayablePlanVo> agentPaySumDetail(IPage<PayablePlanVo> payablePlanDtoIPage, @Param("agentPaySumDetailDto") AgentPaySumDetailDto agentPaySumDetailDto);

    /**
     * 学生应付汇总统计明细
     *
     * @param studentPaySumDetailDto
     * @return
     */
    List<PayablePlanVo> studentPaySumDetail(IPage<PayablePlanVo> payablePlanDtoIPage, @Param("studentPaySumDetailDto") StudentPaySumDetailDto studentPaySumDetailDto);

    /**
     * 应付计划信息
     *
     * @param payablePlanDto
     * @return
     */
    List<PayablePlanVo> getPayablePlansInfo(@Param("payablePlanDto") PayablePlanDto payablePlanDto);



    /**
     * 获取应付计划
     * @param offerItemId
     * @return
     */
    List<PayablePlan> getPayablePlanByOfferItemId(Long offerItemId);
    /**
     * 获取iFile Excel信息
     *
     * @Date 14:14 2022/3/8
     * <AUTHOR>
     */
    List<IFileInfoVo> iFileGroupByAgidAndCurrencyInfo(@Param("numSettlementBatch") String numSettlementBatch);

    Boolean isExistByItemId(@Param("id") Long id);



//    /**
//     * 根据批次号获取对应的应付计划
//     *
//     * @Date 16:40 2022/4/3
//     * <AUTHOR>
//     */
//    List<CommissionSummaryBatchPayablePlanVo> getPayablePlanByNumSettlementBatch(@Param("numSettlementBatch") String numSettlementBatch);


    List<PayablePlanNewVo> payablePlanDatas(IPage<PayablePlanNewVo> iPage, @Param("payablePlanNewDto") PayablePlanNewQueryDto payablePlanNewVo);

    /**
     * 根据应付计划获取已支付金额
     *
     * @Date 14:25 2022/4/14
     * <AUTHOR>
     */
    BigDecimal getPaidAmountByPayablePlanId(@Param("id") Long id);

    /**
     * 获取实付
     * @param fkTypeKey
     * @param fkTypeTargetIds
     * @return
     */
    List<PayablePlanVo> getPayablePlanByTargetsAndType(@Param("fkTypeKey") String fkTypeKey, @Param("fkTypeTargetIds") List<Long> fkTypeTargetIds);

    PayablePlan getPayablePlanByCourseId(@Param("courseId") String courseId,@Param("createTime") String createTime);

    List<SaleDataTempVo> getOtherPlan();

    PayablePlan queryPayablePlan(String courseId);

    PayablePlan queryTsPayablePlan(String offerId);

    PayablePlan getPayablePlanByReId(Long rePlanId);

    /**
     * 获取要作废的佣金应付计划
     *
     * @Date 10:32 2022/8/4
     * <AUTHOR>
     */
    List<PayablePlan> getDeleteSettlementPayablePlans(@Param("deleteSettlementDeleteDto") DeleteSettlementDeleteDto deleteSettlementDeleteDto);

    /**
     * 获取备注信息
     *
     * @param planIds
     * @return
     */
    List<PlanRemarkDetailResultVo> getPayablePlanRemarkDetails(Set<Long> planIds);

    List<String> getPayPlanTheLatestThreeTuitionFees(Long fkCompanyId);

    List<PublicPayFormDetailVo> getPaidAmountInfoByIds(@Param("ids") List<Long> ids);

    List<PayablePlanVo> getPayableListByAgent(@Param("iPage")IPage<PayablePlan> iPage, @Param("targetId") Long targetId);

    List<SelItem> getAgentPayInfo(@Param("ids") Set<Long> ids);

    List<ReceivablePlanVo> getReceivablePlansBySort(@Param("planIds") Set<Long> planIds, @Param("invoiceAmountSort") Boolean invoiceAmountSort
            , @Param("receiptAmountSort") Boolean receiptAmountSort, @Param("studentName") String studentName, @Param("receiptFormId")Long receiptFormId);

}