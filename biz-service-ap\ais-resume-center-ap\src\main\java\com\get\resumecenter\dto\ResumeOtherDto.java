package com.get.resumecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2020/8/11
 * @TIME: 14:11
 * @Description:
 **/
@Data
public class ResumeOtherDto  extends BaseVoEntity implements Serializable {
        private static final long serialVersionUID = 1L;

    /**
     * 所属简历Id
     */
    @ApiModelProperty(value = "所属简历Id", required = true)
    @NotNull(message = "简历id不能为空", groups = {Add.class, Update.class})
    private Long fkResumeId;
    /**
     * 简历其他类型Id
     */
    @ApiModelProperty(value = "简历其他类型Id")
    @NotNull(message = "类型Id不能为空", groups = {Add.class, Update.class})
    private Long fkOtherTypeId;
    /**
     * 主题
     */
    @ApiModelProperty(value = "主题")
    private String subject;
    /**
     * 主题描述
     */
    @ApiModelProperty(value = "主题描述")
    private String description;
}
