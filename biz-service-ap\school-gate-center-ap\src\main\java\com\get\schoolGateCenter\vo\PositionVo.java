package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/6/30
 * @TIME: 9:38
 **/
@Data
public class PositionVo extends BaseVoEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id", required = true)
    @NotNull(message = "公司Id不能为空", groups = {Add.class, Update.class})
    @Min(value = 1, message = "缺少公司id参数", groups = {Add.class, Update.class})
    private Long fkCompanyId;

    /**
     * 部门Id
     */
    @ApiModelProperty(value = "部门Id", required = true)
    @NotNull(message = "部门Id不能为空", groups = {Add.class, Update.class})
    private Long fkDepartmentId;

    /**
     * 职位编号
     */
    @ApiModelProperty(value = "职位编号", required = true)
    @NotBlank(message = "职位编号不能为空", groups = {Add.class, Update.class})
    private String num;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称", required = true)
    @NotBlank(message = "职位名称不能为空", groups = {Add.class, Update.class})
    private String name;

    /**
     * 职位等级
     */
    @ApiModelProperty(value = "职位等级")
    private String posLevel;

    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    private Integer viewOrder;


    private List<Long> fkCompanyIds;

    @ApiModelProperty("查询关键字")
    private String keyWord;

}
