package com.get.pmpcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.pmpcenter.dto.common.ApprovalPlanDto;
import com.get.pmpcenter.dto.common.LockPlanDto;
import com.get.pmpcenter.dto.common.SubmitPlanApprovalDto;
import com.get.pmpcenter.entity.AgentCommissionPlanApproval;
import com.get.pmpcenter.vo.common.StaffVo;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/4/7
 * @Version 1.0
 * @apiNote:
 */
public interface AgentCommissionPlanApprovalService extends IService<AgentCommissionPlanApproval> {

    /**
     * 锁定/解锁代理佣金方案
     *
     * @param lockPlanDto
     */
    void lockAgentPlan(LockPlanDto lockPlanDto);


    /**
     * 提交代理佣金方案审批
     *
     * @param submitPlanApprovalDto
     */
    void submitAgentApproval(SubmitPlanApprovalDto submitPlanApprovalDto);

    /**
     * 审批代理佣金方案
     *
     * @param approvalPlanDto
     */
    void approvalAgentProviderPlan(ApprovalPlanDto approvalPlanDto);

    /**
     * 获取代理佣金方案审批列表
     *
     * @param institutionProviderId
     * @param companyId
     * @return
     */
    List<AgentCommissionPlanApproval> getAgentApprovalList(Long institutionProviderId, Long companyId,Long planId, String planName,String keyword);

    /**
     * 代理佣金方案审核人列表
     * @return
     */
    List<StaffVo> getAgentStaff();

    /**
     * 拒绝合同端佣金方案-用于驳回合同方案审批后驳回合同端佣金方案
     * @param approvalPlanDto
     */
    void rejectProviderPlan(ApprovalPlanDto approvalPlanDto);
}
