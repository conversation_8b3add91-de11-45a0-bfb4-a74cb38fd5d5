package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.secure.utils.SecureUtil;
import com.get.institutioncenter.dto.AreaStateDto;
import com.get.institutioncenter.vo.AreaCountryVo;
import com.get.institutioncenter.vo.AreaStateVo;
import com.get.institutioncenter.service.IAreaStateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/27 12:21
 * @verison: 1.0
 * @description: 区域管理-州省配置控制器
 */
@Api(tags = "区域管理-州省配置")
@RestController
@RequestMapping("/institution/areaState")
public class AreaStateController {

    @Resource
    private IAreaStateService areaStateService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/区域管理-州省配置/州省详情")
    @GetMapping("/{id}")
    public ResponseBo<AreaStateVo> detail(@PathVariable("id") Long id) {
        AreaStateVo data = areaStateService.findAreaStateById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 批量新增信息
     *
     * @param areaStateDtos
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/区域管理-州省配置/新增州省")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(AreaStateDto.Add.class) ValidList<AreaStateDto> areaStateDtos) {
        areaStateService.batchAdd(areaStateDtos);
        return SaveResponseBo.ok();
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/区域管理-州省配置/删除州省")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        areaStateService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param areaStateDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/区域管理-州省配置/更新州省")
    @PostMapping("update")
    public ResponseBo<AreaStateVo> update(@RequestBody @Validated(AreaStateDto.Update.class) AreaStateDto areaStateDto) {
        return UpdateResponseBo.ok(areaStateService.updateAreaState(areaStateDto));
    }

    /**
     * 树状图
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "树状图", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/区域管理-州省配置/树状图")
    @PostMapping("getTreeList")
    public ResponseBo<AreaCountryVo> getTreeList() {
        List<AreaCountryVo> treeList = areaStateService.getTreeList();
        return new ListResponseBo<>(treeList);
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为搜索关键字(num州省编号 name州省名称 nameChn州省中文名)")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/区域管理-州省配置/查询州省")
    @PostMapping("datas")
    public ResponseBo<AreaStateVo> datas(@RequestBody SearchBean<AreaStateDto> page) {
        List<AreaStateVo> datas = areaStateService.getAreaStates(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 查询国家下面州省
     *
     * @param id
     * @return
     * @
     */
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "查询国家下面州省", notes = "")
    @GetMapping("getByFkAreaCountryId/{id}")
    public ResponseBo getByFkAreaCountryId(@PathVariable("id") Long id) {
        List<AreaStateVo> datas = areaStateService.getByFkAreaCountryId(id);
        return new ListResponseBo<>(datas);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取对应国家、公司下 有申请计划的代理所在的 州省下拉框数据", notes = "")
    @GetMapping("getExistsAgentOfferItemAreaStateList/{companyId}")
    public ResponseBo getExistsAgentOfferItemAreaStateList(@PathVariable("companyId") Long companyId,
                                                             @RequestParam("countryId") @NotNull(message = "国家id不能为空")Long countryId) {
        if (!SecureUtil.validateCompany(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        List<BaseSelectEntity> datas = areaStateService.getExistsAgentOfferItemAreaStateList(companyId, countryId);
        return new ListResponseBo<>(datas);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取对应国家、公司下 的代理所在的 州省下拉框数据", notes = "")
    @GetMapping("getExistsAgentAreaStateList/{companyId}")
    public ResponseBo getExistsAgentAreaStateList(@PathVariable("companyId") Long companyId,
                                                           @RequestParam("countryId") @NotNull(message = "国家id不能为空")Long countryId) {
        if (!SecureUtil.validateCompany(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        List<BaseSelectEntity> datas = areaStateService.getExistsAgentAreaStateList(companyId, countryId);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return java.lang.String
     * @Description :feign调用 通过州省id 查找对应的州省名称
     * @Param [id]
     * <AUTHOR>
     */
/*    @ApiIgnore
    @GetMapping(value = "getStateNameById")
    public String getStateNameById(@RequestParam(required = false) Long id) {
        return areaStateService.getStateNameById(id);
    }*/
    /**
     * @return java.lang.String
     * @Description :feign调用 通过州省id 查找对应的州省名称
     * @Param [id]
     * <AUTHOR>
     */
/*    @ApiIgnore
    @GetMapping(value = "getStateFullNameById")
    public String getStateFullNameById(@RequestParam(required = false)Long id) {
        return areaStateService.getStateFullNameById(id);
    }*/


    /**
     * @return java.lang.String
     * @Description :feign调用 通过州省ids 查找对应的州省名称map
     * @Param [id]
     * <AUTHOR>
     */
/*    @ApiIgnore
    @VerifyLogin(IsVerify = false)
    @PostMapping(value = "getStateNamesByIds")
    public Map<Long, String> getStateNamesByIds(@RequestBody Set<Long> ids) {
        return areaStateService.getStateNamesByIds(ids);
    }*/


    /**
     * @return java.lang.String
     * @Description :feign调用 通过州省ids 查找对应的州省（全名）名称map
     * @Param [id]
     * <AUTHOR>
     */
 /*   @ApiIgnore
    @PostMapping(value = "getStateFullNamesByIds")
    public Map<Long, String> getStateFullNamesByIds(@RequestBody Set<Long> ids) {
        return areaStateService.getStateFullNamesByIds(ids);
    }*/


}
