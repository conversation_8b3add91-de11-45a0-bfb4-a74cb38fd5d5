package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.institutioncenter.entity.NewsType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/6 11:45
 * @verison: 1.0
 * @description: 新闻类型管理mapper
 */
@Mapper
public interface NewsTypeMapper extends BaseMapper<NewsType> {

    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(NewsType record);

    /**
     * 通过id获取名字
     *
     * @param id
     * @return
     */
    String getNewNameById(Long id);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description :新闻类型下拉框
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getNewsTypeList();

    /**
     * @return java.lang.Integer
     * @Description :获取最大排序值
     * @Param []
     * <AUTHOR>
     */
    Integer getMaxViewOrder();
}