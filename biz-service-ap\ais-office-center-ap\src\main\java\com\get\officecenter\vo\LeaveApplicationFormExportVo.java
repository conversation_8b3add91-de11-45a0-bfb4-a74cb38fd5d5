package com.get.officecenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Hardy
 * @create: 2024/3/26 15:23
 * @verison: 1.0
 * @description:
 */
@Data
public class LeaveApplicationFormExportVo {

    @ApiModelProperty(value = "所属公司")
    private String companyName;

    @ApiModelProperty(value = "类型")
    private String leaveApplicationFormTypeName;

    @ApiModelProperty(value = "请假时间")
    private String leaveTime;

    @ApiModelProperty(value = "小时(天)")
    private String days;

    @ApiModelProperty(value = "部门")
    private String departmentName;

    @ApiModelProperty("创建人")
    private String gmtCreateUser;

    @ApiModelProperty(value = "申请原因")
    private String reason;

    @ApiModelProperty(value = "审批状态")
    private String statusName;

    @ApiModelProperty(value = "当前审批人")
    private String assigneeName;

    @ApiModelProperty("创建时间")
    private String gmtCreate;
}
