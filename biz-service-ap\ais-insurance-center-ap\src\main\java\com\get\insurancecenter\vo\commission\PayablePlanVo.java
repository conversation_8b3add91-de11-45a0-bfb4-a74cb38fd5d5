package com.get.insurancecenter.vo.commission;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author:Oliver
 * @Date: 2025/5/27
 * @Version 1.0
 * @apiNote:应付计划Vo
 */
@Data
public class PayablePlanVo {

    @ApiModelProperty(value = "费率%(代理)")
    private BigDecimal commissionRate;

    @ApiModelProperty(value = "佣金金额(代理)")
    private BigDecimal commissionAmount;

    @ApiModelProperty(value = "应付计划Id")
    private Long payablePlanId;
}
