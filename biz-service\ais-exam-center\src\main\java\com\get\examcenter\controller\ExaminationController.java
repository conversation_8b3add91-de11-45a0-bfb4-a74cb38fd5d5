package com.get.examcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.examcenter.vo.ExaminationVo;
import com.get.examcenter.vo.UserexaminationPaperScoreVo;
import com.get.examcenter.service.ExaminationService;
import com.get.examcenter.dto.ExaminationListDto;
import com.get.examcenter.dto.ExaminationUpdateDto;
import com.get.examcenter.dto.ViewLeaderboardDto;
import com.get.institutioncenter.vo.AreaRegionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by Jerry.
 * Time: 9:39
 * Date: 2021/8/23
 * Description:考试管理控制器
 */
@Api(tags = "考试管理")
@RestController
@RequestMapping("exam/Examination")
public class ExaminationController {

    @Resource
    private ExaminationService examinationService;

    /**
     * @Description: 列表数据
     * @Author: Jerry
     * @Date:9:41 2021/8/23
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.LIST, description = "考试中心/考试管理/列表数据")
    @PostMapping("datas")
    public ResponseBo<ExaminationVo> datas(@RequestBody SearchBean<ExaminationListDto> page) {
        List<ExaminationVo> examinationVos = examinationService.getExaminationList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(examinationVos, p);
    }


    /**
     * @Description: 新增
     * @Author: Jerry
     * @Date:11:23 2021/8/23
     */
    @ApiOperation(value = "新增", notes = "")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.ADD, description = "考试中心/考试管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(ExaminationUpdateDto.Add.class) ExaminationUpdateDto examinationUpdateDto) {
        examinationService.add(examinationUpdateDto);
        return SaveResponseBo.ok();
    }

    /**
     * @Description: 更新
     * @Author: Jerry
     * @Date:11:25 2021/8/23
     */
    @ApiOperation(value = "更新", notes = "")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.EDIT, description = "考试中心/考试管理/更新")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated(ExaminationUpdateDto.Update.class) ExaminationUpdateDto examinationUpdateDto) {
        examinationService.update(examinationUpdateDto);
        return UpdateResponseBo.ok();
    }

    /**
     * @Description: 详情
     * @Author: Jerry
     * @Date:11:26 2021/8/23
     */
    @ApiOperation(value = "详情", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.DETAIL, description = "考试中心/考试管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<ExaminationVo> detail(@PathVariable("id") Long id) {
        return new ResponseBo<>(examinationService.detail(id));
    }

    /**
     * 删除
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.DELETE, description = "考试中心/考试管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        examinationService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @Description: 激活禁用
     * @Author: Jerry
     * @Date:11:25 2021/8/23
     */
    @ApiOperation(value = "激活禁用", notes = "isActive: true：激活 false：禁用")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.EDIT, description = "考试中心/考试管理/激活禁用")
    @PostMapping("updateActive/{id}/{isActive}")
    public ResponseBo updateActive(@PathVariable("id") Long id, @PathVariable("isActive") boolean isActive) {
        examinationService.updateActive(id, isActive);
        return UpdateResponseBo.ok();
    }


    /**
     * @Description: 查看排行榜
     * @Author: Jerry
     * @Date:12:32 2021/8/23
     */
    @ApiOperation(value = "查看排行榜", notes = "fkExaminationId为考试id，userName为用户姓名")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.LIST, description = "考试中心/考试管理/查看排行榜")
    @PostMapping("/viewLeaderboard/{fkExaminationId}")
    public ResponseBo<UserexaminationPaperScoreVo> viewLeaderboard(@PathVariable("fkExaminationId") Long fkExaminationId,
                                                                   @RequestBody SearchBean<ViewLeaderboardDto> page) {
        List<UserexaminationPaperScoreVo> userexaminationPaperScoreVos = examinationService.viewLeaderboard(fkExaminationId, page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(userexaminationPaperScoreVos, p);
    }

    /**
     * @Description: feign调用，根据考试ids获取名称
     * @Author: Jerry
     * @Date:18:00 2021/8/23
     */
    @ApiIgnore
    @GetMapping("getExaminationNamesByExaminationIds")
    public Map<Long, String> getExaminationNamesByExaminationIds(@RequestParam(value = "examinationIds") Set<Long> examinationIds) {
        return examinationService.getExaminationNamesByExaminationIds(examinationIds);
    }


    /**
     * @Description: BD下拉框
     * @Author: Jerry
     * @Date:10:40 2021/9/1
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "BD下拉框", notes = "")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.LIST, description = "考试中心/考试管理/BD下拉框")
    @PostMapping("/getAreaRegionSelect")
    public ResponseBo<AreaRegionVo> getAreaRegionSelect() {
        return new ListResponseBo<>(examinationService.getAreaRegionSelect());
    }


    /**
     * @Description: 考试下拉框
     * @Author: Jerry
     * @Date:10:40 2021/9/1
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "考试下拉框", notes = "")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.LIST, description = "考试中心/考试管理/考试下拉框")
    @PostMapping("/examinationSelect")
    public ResponseBo<BaseSelectEntity> examinationSelect(@RequestParam(required = false, value = "fkCompanyId") Long fkCompanyId) {
        return new ListResponseBo<>(examinationService.examinationSelect(fkCompanyId));
    }


    /**
     * @Description: 导出考题文件(word文件)
     * @Author: Jerry
     * @Date:10:56 2021/9/13
     */
    @ApiOperation(value = "导出考题文件(word文件)", notes = "")
    @PostMapping("/exportQuestionDoc")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.LIST, description = "考试中心/考试管理/导出考题文件(word文件)")
    @ResponseBody
    public void exportQuestionDoc(HttpServletResponse response, @RequestBody ExaminationUpdateDto examinationUpdateDto) {
        examinationService.exportQuestionDoc(response, examinationUpdateDto.getId());
    }
}
