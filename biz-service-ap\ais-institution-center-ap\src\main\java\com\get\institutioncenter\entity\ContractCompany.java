package com.get.institutioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_contract_company")
public class ContractCompany extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 合同Id
     */
    @ApiModelProperty(value = "合同Id")
    @Column(name = "fk_contract_id")
    private Long fkContractId;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;
}