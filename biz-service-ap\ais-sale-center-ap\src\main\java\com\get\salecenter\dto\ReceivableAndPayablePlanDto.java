package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;

/**
 * @author: Hardy
 * @create: 2021/12/8 12:15
 * @verison: 1.0
 * @description:
 */
@Data
public class ReceivableAndPayablePlanDto {

    @Valid
    @ApiModelProperty("应收计划")
    private ReceivablePlanDto receivablePlanVo;

    @Valid
    @ApiModelProperty("应付计划")
    private PayablePlanDto payablePlanVo;

    @ApiModelProperty(value = "是否同步学费到申请计划")
    private Boolean flag=false;
}
