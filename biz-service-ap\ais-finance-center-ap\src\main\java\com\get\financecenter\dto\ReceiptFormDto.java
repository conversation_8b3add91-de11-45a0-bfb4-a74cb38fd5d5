package com.get.financecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/12/22
 * @TIME: 16:09
 * @Description:
 **/
@Data
public class ReceiptFormDto  extends BaseVoEntity {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 目标类型关键字，枚举：m_institution_provider学校供应商/m_student学生
     */
    @ApiModelProperty(value = "目标类型关键字，枚举：m_institution_provider学校供应商/m_student学生")
    @NotBlank(message = "目标类型不能为空", groups = {Add.class, Update.class})
    private String fkTypeKey;

    /**
     * 对应记录Id
     */
    @ApiModelProperty(value = "对应记录Id")
    @NotNull(message = "对应记录Id不能为空", groups = {Add.class, Update.class})
    private Long fkTypeTargetId;

    /**
     * 收款费用类型名称
     */
    @ApiModelProperty(value = "收款费用类型名称")
    private String fkReceiptFeeTypeName;

    /**
     * 收款费用类型key
     */
    @ApiModelProperty(value = "收款费用类型key")
    private String fkReceiptFeeTypeKey;


    /**
     * 银行帐号Id（公司）
     */
    @ApiModelProperty(value = "银行帐号Id（公司）")
    @NotNull(message = "银行帐号Id（公司）不能为空", groups = {Add.class, Update.class})
    private Long fkBankAccountId;

    /**
     * 发票编号
     */
    @ApiModelProperty(value = "发票编号")
    private String fkInvoiceNum;

    /**
     * 收款单编号（系统生成）
     */
    @ApiModelProperty(value = "收款单编号（系统生成）")
    private String numSystem;

    @ApiModelProperty(value = "汇率调整金额,(保留两位)")
    private BigDecimal amountExchangeRate;

    /**
     * 收款单编号（凭证号）
     */
    @ApiModelProperty(value = "收款单编号（凭证号）")
    private String numBank;
    /**
     * 收款日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "收款日期")
    private Date receiptDate;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @NotBlank(message = "币种编号不能为空", groups = {Add.class, Update.class})
    private String fkCurrencyTypeNum;

    /**
     * 收款总金额（到账金额）
     */
    @ApiModelProperty(value = "收款总金额（到账金额）")
    @NotNull(message = "收款总金额（到账金额）不能为空", groups = {Add.class, Update.class})
    private BigDecimal amount;

    /**
     * 汇率（港币）
     */
    @ApiModelProperty(value = "汇率（港币）")
    @NotNull(message = "汇率（港币）不能为空", groups = {Add.class, Update.class})
    private BigDecimal exchangeRateHkd;

    /**
     * 收款总金额（到账金额，港币）
     */
    @ApiModelProperty(value = "收款总金额（到账金额，港币）")
    @NotNull(message = "收款总金额（到账金额，港币）不能为空", groups = {Add.class, Update.class})
    private BigDecimal amountHkd;

    /**
     * 汇率（人民币）
     */
    @ApiModelProperty(value = "汇率（人民币）")
    @NotNull(message = "汇率（人民币）不能为空", groups = {Add.class, Update.class})
    private BigDecimal exchangeRateRmb;

    /**
     * 收款总金额（到账金额，人民币）
     */
    @ApiModelProperty(value = "收款总金额（到账金额，人民币）")
    @NotNull(message = "收款总金额（到账金额，人民币）不能为空", groups = {Add.class, Update.class})
    private BigDecimal amountRmb;

    /**
     * 手续费
     */
    @ApiModelProperty(value = "手续费")
    private BigDecimal serviceFee;

    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;

    /**
     * 代理佣金结算状态：0待结算/1可结算，默认为0
     */
    @ApiModelProperty(value = "代理佣金结算状态：0待结算/1可结算，默认为0")
    @NotNull(message = "代理佣金结算状态：0待结算/1可结算不能为空", groups = {Add.class, Update.class})
    private Integer settlementStatus;

    /**
     * 状态：0作废/1打开
     */
    @ApiModelProperty(value = "状态：0作废/1打开")
    private Integer status;

    /**
     * 目标名称
     */
    @ApiModelProperty(value = "目标名称")
    private String targetName;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String endTime;

    /**
     * 关键词
     */
    @ApiModelProperty(value = "关键词")
    private String keyWord;

    /**
     * 应收计划id
     */
    @ApiModelProperty(value = "应收计划id")
    private Long receivablePlanId;

    /**
     * 绑定状态（0：未绑定 1：绑定部分 2：绑定完成）
     */
    @ApiModelProperty(value = "绑定状态（0：未绑定 1：绑定部分 2：绑定完成）")
    private Integer bindingStatus;

    /**
     * 旧数据财务id(gea)
     */
    @ApiModelProperty(value = "旧数据财务id(gea)")
    private String idGeaFinance;

    /**
     * 发票ID列表
     */
    @ApiModelProperty(value = "发票ID列表")
    private List<Long> fkInvoiceIdList;

    /**
     * 公司ids
     */
    @ApiModelProperty(value = "公司ids")
    private List<Long> fkCompanyIds;

    @NotNull(message = "请填写实收汇率")
    @ApiModelProperty(value = "汇率")
    private BigDecimal exchangeRate;


    @ApiModelProperty(value = "银行实际到账金额")
    private BigDecimal amountBank;

    @ApiModelProperty(value = "银行实际手续费")
    private BigDecimal serviceFeeBank;


    @ApiModelProperty(value = "收费金额（含手续费）开始范围")
    private BigDecimal amountStart;

    @ApiModelProperty(value = "收费金额（含手续费）结束范围")
    private BigDecimal amountEnd;

    @ApiModelProperty(value = "收款单状态 0否/1是")
    private Boolean isStatus;

    @ApiModelProperty(value = "是否隐藏补单数据 0否/1是")
    private Boolean isHideSupplementary ;
}
