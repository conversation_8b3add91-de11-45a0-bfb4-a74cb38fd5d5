package com.get.financecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/10 17:15
 */
@Data
public class AgentSettlementOfferItemVo extends BaseVoEntity implements Serializable {

    @ApiModelProperty(value = "学生名称")
    private String name;

    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;

    @ApiModelProperty("学校提供商id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty("应收计划id")
    private Long fkReceivablePlanId;

    @ApiModelProperty(value = "国家名称")
    private String countryName;

    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

    @ApiModelProperty(value = "学校名称")
    private String institutionName;

    @ApiModelProperty("代理id")
    private Long agentId;

    @ApiModelProperty(value = "代理编号")
    private String agentNum;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "佣金费率")
    private BigDecimal payableCommissionRate;

    @ApiModelProperty(value = "分成比例")
    private BigDecimal payableSplitRate;

    @ApiModelProperty("学生编号")
    private String studentNum;

    @ApiModelProperty(value = "课程Id")
    private Long fkInstitutionCourseId;

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    private Long studentOfferItemId;

    /**
     * 延迟入学标记
     */
    @ApiModelProperty(value = "延迟入学标记")
    private Boolean isDeferEntrance;

    /**
     * 延迟入学时间
     */
    @ApiModelProperty(value = "延迟入学时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date maxDeferEntranceTimes;

    /**
     * 课程长度类型(0周、1月、2年、3学期)
     */
    @ApiModelProperty(value = "课程长度类型(0周、1月、2年、3学期)")
    private Integer durationType;

    /**
     * 课程长度
     */
    @ApiModelProperty(value = "课程长度")
    private BigDecimal duration;

    @ApiModelProperty(value = "开学时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openingTime;

    @ApiModelProperty(value = "应付币种")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "应付金额")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal payableAmount;

    @ApiModelProperty(value = "已付金额")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal paidAmount;

    @ApiModelProperty(value = "应付差额")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal differenceAmount;

    @ApiModelProperty(value = "申请状态")
    private String stepName;

    private Integer status;

    @ApiModelProperty(value = "收款状态  0:未收 1：部分已收 2：已收齐")
    private String statusName;

    @ApiModelProperty(value = "业务类型Key")
    private String fkTypeKey;

    @ApiModelProperty(value = "结算标记")
    private List<PayablePlanSettlementAgentAccountVo> payablePlanSettlementAgentAccountDtoList;


    /**
     * 旧系统学校名称
     */
    @ApiModelProperty(value = "旧系统学校名称")
    private String oldInstitutionName;

    /**
     * 旧系统学校全称
     */
    @ApiModelProperty(value = "旧系统学校全称")
    private String oldInstitutionFullName;

    /**
     * 旧系统课程名称
     */
    @ApiModelProperty(value = "旧系统课程名称")
    private String oldCourseCustomName;

    @ApiModelProperty(value = "实际支付金额")
    private BigDecimal amountActual;

    /**
     * 实际手续费金额
     */
    @ApiModelProperty(value = "实际手续费金额")
    private BigDecimal serviceFeeActual;

    /**
     * 预付金额
     */
    @ApiModelProperty(value = "预付金额")
    private BigDecimal prepaymentAmount;

    /**
     * 帐号导出时间
     */
    @ApiModelProperty(value = "帐号导出时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date accountExportTime;

    @ApiModelProperty(value = "到账时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate installmentCreateTime;


    @ApiModelProperty(value = "是否回滚，0否/1是")
    private Boolean rollBack;

    @ApiModelProperty("结算通知Id")
    private Long commissionNoticeId;

    @ApiModelProperty("结算通知信息")
    private String commissionNotice;

    @ApiModelProperty("结算通知批次号")
    private String numSettlementBatch;

    @ApiModelProperty("结算通知备注")
    private String remark;

    @ApiModelProperty("学习方案创建时间")
    private Date studentOfferCreateTime;

    @ApiModelProperty(value = "折合收款金额")
    private BigDecimal amountReceivable;

    @ApiModelProperty(value = "是否预付，0否/1是")
    private Boolean isPayInAdvance;

    @ApiModelProperty(value = "最大收款单id")
    private Long maxFkReceiptFormItemId;

    @ApiModelProperty(value = "摘要")
    private String summary;

    @ApiModelProperty(value = "结算ids")
    private String settlementIds;

    @ApiModelProperty(value = "学生代理合同账户Id")
    private Long fkAgentContractAccountId;

    @ApiModelProperty(value = "币种编号（代理账户）")
    private String accountCurrencyTypeNum;

    @ApiModelProperty(value = "币种名（代理账户）")
    private String accountCurrencyName;

    @ApiModelProperty(value = "预付标记")
    private Boolean prepaidMark;

    @ApiModelProperty(value = "预付/新增结算设置时间")
    private Date settlementCreateTime;

    @ApiModelProperty(value = "佣金标记")
    private String commissionMark;

    @ApiModelProperty(value = "结算账单Id")
    private Long fkSettlementBillId;

    @ApiModelProperty(value = "代理公司")
    private String companyName;


    private String fileKey;
    private String fileNameOrc;


}
