package com.get.partnercenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.get.common.consts.AESConstant;
import com.get.common.utils.AESUtils;
import com.get.partnercenter.dto.SendEmailInfoDto;
import com.get.partnercenter.service.MailLogService;
import com.get.partnercenter.util.MailSenderFactory;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.encoder.org.apache.commons.lang3.StringUtils;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.mail.internet.MimeMessage;

/**
 * @Author:Oliver
 * @Date: 2025/2/25  09:52
 * @Version 1.0
 */
@Service
@Slf4j
public class MailLogServiceImpl implements MailLogService {
//    @Override
//    public String sendEmail(SendEmailInfoDto sendEmailInfoDto, String subject, String body) {
//        if (StringUtils.isAnyBlank(sendEmailInfoDto.getFromEmail(), sendEmailInfoDto.getToEmail(), sendEmailInfoDto.getEmailPassword())) {
//            log.error("邮件发送失败，参数为空,参数:{}", JSONObject.toJSONString(sendEmailInfoDto));
//            throw new RuntimeException("邮件发送失败，参数为空");
//        }
//        log.info("邮件发送====》发件人:{},收件人:{}", sendEmailInfoDto.getFromEmail(), sendEmailInfoDto.getToEmail());
//        try {
//            String password = AESUtils.Decrypt(sendEmailInfoDto.getEmailPassword(), AESConstant.AESKEY);
//            String fromEmail = sendEmailInfoDto.getFromEmail();
//            String toEmail = sendEmailInfoDto.getToEmail();
//            //qq邮箱/hti邮箱
//            if (fromEmail.contains("@qq.com") || fromEmail.contains("@ht-international.net")) {
//                // 邮件相关配置
//                String host = "smtp.exmail.qq.com";
//                int port = 465;
//                // 配置邮件服务器
//                Properties props = new Properties();
//                props.put("mail.smtp.host", host);
//                props.put("mail.smtp.port", port);
//                props.put("mail.smtp.auth", "true");
//                props.put("mail.smtp.ssl.enable", "true");
//                // 创建会话
//                Session session = Session.getInstance(props);
//                // 创建邮件
//                Message message = new MimeMessage(session);
//                message.setFrom(new InternetAddress(fromEmail));
//                message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(toEmail));
//                message.setSubject(subject);
//                message.setContent(body, "text/html; charset=utf-8");
//
//                // 发送邮件
//                Transport transport = session.getTransport("smtp");
//                transport.connect(host, fromEmail, password);
//                transport.sendMessage(message, message.getAllRecipients());
//                transport.close();
//                log.info("发送邮件成功,发送人:{},收件人:{}", fromEmail, toEmail);
//
//            } else {
//                MailDto mailVo = new MailDto();
//                mailVo.setDefaultEncoding("utf-8");
//                mailVo.setHost("smtp.qiye.aliyun.com");
//                if (fromEmail.contains("@geteducation.org")) {
//                    mailVo.setHost("smtp.exmail.qq.com");
//                }
//                mailVo.setPort(465);
//                mailVo.setProtocol("smtps");
//                mailVo.setUserName(fromEmail);
//                if (GeneralTool.isNotEmpty(password)) {
//                    mailVo.setPassword(password);
//                }
//                mailVo.setTitle(subject);
//                mailVo.setToEmail(toEmail);
//                mailVo.setTemplate(body);
//                mailVo.setFlag(true);
//
//                JavaMailSenderImpl javaMailSenderImpl = new JavaMailSenderImpl();
//                javaMailSenderImpl.setDefaultEncoding(mailVo.getDefaultEncoding());
//                javaMailSenderImpl.setHost(mailVo.getHost());
//                javaMailSenderImpl.setPort(mailVo.getPort());
//                javaMailSenderImpl.setProtocol(mailVo.getProtocol());
//                javaMailSenderImpl.setUsername(mailVo.getUserName());
//                javaMailSenderImpl.setPassword(password);
//                MimeMessage mail = javaMailSenderImpl.createMimeMessage();
//                MimeMessageHelper helper = new MimeMessageHelper(mail);
//                helper.setTo(toEmail);
//                if (GeneralTool.isNotEmpty(mailVo.getCcEmail())) {
//                    helper.setCc(mailVo.getCcEmail());
//                }
//                helper.setSubject(subject);
//                helper.setFrom(mailVo.getUserName());
//                helper.setText(mailVo.getTemplate(), mailVo.isFlag());
//                javaMailSenderImpl.send(mail);
//            }
//        } catch (Exception e) {
//            log.error("邮件发送失败,失败原因:{}", e.getMessage());
//            return "邮件发送失败,失败原因:" + e.getMessage();
//        }
//        return "";
//    }


    @Override
    public String sendEmail(SendEmailInfoDto sendEmailInfoDto, String subject, String body) {
        if (StringUtils.isAnyBlank(sendEmailInfoDto.getFromEmail(), sendEmailInfoDto.getToEmail(), sendEmailInfoDto.getEmailPassword())) {
            log.error("邮件发送失败，参数为空,参数:{}", JSONObject.toJSONString(sendEmailInfoDto));
            throw new RuntimeException("邮件发送失败，参数为空");
        }
        try {
            String fromEmail = sendEmailInfoDto.getFromEmail();
            String toEmail = sendEmailInfoDto.getToEmail();
            String password = AESUtils.Decrypt(sendEmailInfoDto.getEmailPassword(), AESConstant.AESKEY);

            log.info("邮件发送 => 发件人: {}, 收件人: {}", fromEmail, toEmail);

            JavaMailSenderImpl mailSender = MailSenderFactory.buildMailSender(fromEmail, password);
            MimeMessage mail = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mail, true, "UTF-8");

            helper.setFrom(fromEmail);
            helper.setTo(toEmail);
            helper.setSubject(subject);
            // 支持 HTML
            helper.setText(body, true);
            mailSender.send(mail);
            log.info("发送邮件成功: 发件人 {}, 收件人 {}", fromEmail, toEmail);
        } catch (Exception e) {
            log.error("邮件发送失败, 失败原因: {}", e.getMessage(), e);
            return "邮件发送失败, 原因: " + e.getMessage();
        }
        return "";
    }
}


