package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/8/18 11:49
 * @verison: 1.0
 * @description:
 */
@Data
public class ConventionHotelRoomDto  extends BaseVoEntity{
    /**
     * 酒店房型Id
     */
    @NotNull(message = "酒店房型不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "酒店房型Id", required = true)
    private Long fkConventionHotelId;

    /**
     * 系统房间编号
     */
    @ApiModelProperty(value = "系统房间编号")
    private String systemRoomNum;

    /**
     * 酒店房间编号
     */
    @ApiModelProperty(value = "酒店房间编号")
    private String hotelRoomNum;

    /**
     * 住店日期
     */
    @NotNull(message = "住店日期不能为空", groups = {Add.class, Update.class})
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "住店日期", required = true)
    private Date stayDate;

    /**
     * 房间数
     */
    @ApiModelProperty(value = "房间数")
    private long rooms;

    /**
     * 住店天数
     */
    @ApiModelProperty(value = "住店天数")
    private long days;

    //自定义内容
    /**
     * 酒店名称
     */
    @ApiModelProperty(value = "酒店名称")
    private String hotel;

    /**
     * 房型
     */
    @ApiModelProperty(value = "房型")
    private String roomType;

    /**
     * 房号
     */
    @ApiModelProperty(value = "房号")
    private String roomNum;

    /**
     * 峰会id
     */
    @ApiModelProperty(value = "峰会id")
    private Long conventionId;

    /**
     * 1:已满/0:未满
     */
    @ApiModelProperty(value = "1:已满/0:未满")
    private Long isFull;

    /**
     * 参会人员姓名
     */
    @ApiModelProperty(value = "参会人员姓名")
    private String personName;

    /**
     * 参会人员姓名
     */
    @ApiModelProperty(value = "参会人员类型")
    private Integer personType;

    /**
     * 房间床位id
     */
    @ApiModelProperty(value = "房间床位id")
    private Long fkConventionHotelRoomId;

    /**
     * 床位数
     */
    @ApiModelProperty(value = "床位数")
    private Integer bedCount;

    /**
     * 参会人id
     */
    @ApiModelProperty(value = "参会人id")
    private Long fkConventionPersonId;

    /**
     * 根据登录人bdcode查询
     */
    @ApiModelProperty(value = "根据登录人bdcode查询")
    private Boolean bdCodeKey;

    /**
     * 根据指定的bdcode查询
     */
    @ApiModelProperty(value = "根据指定的bdcode查询")
    private Set<String> bdCodes;

   
}
