package com.get.schoolGateCenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.schoolGateCenter.entity.StaffHrEvent;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2020/7/13
 * @TIME: 10:36
 * @Description:
 **/
@ApiOperation("员工人事记录返回类")
@Data
public class StaffHrEventDto extends StaffHrEvent {


    @ApiModelProperty(value = "原办公室名称")
    private String fromOfficeName;
    @ApiModelProperty(value = "原部门名称")
    private String fromDepartmentName;
    @ApiModelProperty(value = "原职位名称")
    private String fromPositionName;

    @ApiModelProperty(value = "目标办公室名称")
    private String toOfficeName;
    @ApiModelProperty(value = "目标部门名称")
    private String toDepartmentName;
    @ApiModelProperty(value = "目标职位名称")
    private String toPositionName;

    /**
     * 转正时间
     */
    @ApiModelProperty("转正时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date passProbationDate;
}
