package com.get.partnercenter.config;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Author:Oliver
 * @Date: 2025/5/7
 * @Version 1.0
 * @apiNote:
 */
@Configuration
@ConfigurationProperties(prefix = "wx")
@Data
public class WxConfig {

    @ApiModelProperty("微信小程序appId")
    private String appId = "wx13fec1a5be977e0c";

    @ApiModelProperty("微信小程序appSecret")
    private String appSecret = "ec27fb0f8d845ac0b99364fd8d64374a";
}
