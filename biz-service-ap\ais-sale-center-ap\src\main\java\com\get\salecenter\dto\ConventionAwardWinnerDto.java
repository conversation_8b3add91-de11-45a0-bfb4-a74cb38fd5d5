package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2021/9/9
 * @TIME: 12:36
 * @Description:
 **/
@Data
public class ConventionAwardWinnerDto extends BaseVoEntity implements Serializable  {
    /**
     * 搜索关键字
     */
    @ApiModelProperty(value = "搜索关键字")
    private String keyword;

    /**
     * 峰会id
     */
    @ApiModelProperty(value = "峰会id")
    @NotNull(message = "峰会id不能为空", groups = {Add.class, Update.class})
    private Long fkConventionId;


    /**
     * 峰会奖品Id
     */
    @ApiModelProperty(value = "峰会奖品Id")
    @NotNull(message = "峰会奖品Id不能为空", groups = {Add.class, Update.class})
    private Long fkConventionAwardId;

    /**
     * 峰会参展人员Id（中奖人）
     */
    @ApiModelProperty(value = "峰会参展人员Id（中奖人）")
    private Long fkConventionPersonId;

    /**
     * 峰会抽奖号码Id
     */
    @ApiModelProperty(value = "峰会抽奖号码Id")
    @NotNull(message = "峰会抽奖号码Id", groups = {Add.class, Update.class})
    private Long fkConventionAwardCodeId;

   



}
