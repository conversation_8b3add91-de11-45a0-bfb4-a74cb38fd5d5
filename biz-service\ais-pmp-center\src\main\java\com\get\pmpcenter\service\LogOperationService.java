package com.get.pmpcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.pmpcenter.dto.common.LogRecordDto;
import com.get.pmpcenter.entity.LogOperation;
import com.get.pmpcenter.vo.common.LogRecordVo;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/28  12:01
 * @Version 1.0
 */
public interface LogOperationService extends IService<LogOperation> {

    /**
     * 根据类型和id查询操作日志
     *
     * @param type
     * @param id
     * @return
     */
    List<LogRecordVo> logList(Integer type, Long id);

    /**
     * 日志列表-分页
     *
     * @param params
     * @param page
     * @return
     */
    List<LogRecordVo> commissionLogRecordPage(LogRecordDto params, Page page);

}
