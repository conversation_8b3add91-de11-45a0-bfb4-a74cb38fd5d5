package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.salecenter.vo.StudentAgentVo;
import com.get.salecenter.entity.StudentAgent;
import com.get.salecenter.dto.StudentAgentDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/11/9
 * @TIME: 16:20
 * @Description:
 **/
public interface IStudentAgentService extends IService<StudentAgent> {
    /**
     * @return java.util.List<com.get.salecenter.vo.StudentAgentVo>
     * @Description: 列表数据
     * @Param [studentAgentDto]
     * <AUTHOR>
     */
    List<StudentAgentVo> getStudentAgent(StudentAgentDto studentAgentDto, Page page);


    /**
     * @return java.lang.Long
     * @Description: 新增
     * @Param [studentAgentDto]
     * <AUTHOR>
     */
    Long addStudentAgent(StudentAgentDto studentAgentDto);


    /**
     * @return com.get.salecenter.vo.StudentAgentVo
     * @Description: 修改
     * @Param [studentAgentDto]
     * <AUTHOR>
     */
    StudentAgentVo updateStudentAgent(StudentAgentDto studentAgentDto);


    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据代理id查询学生
     * @Param [agentId]
     * <AUTHOR>
     */
    List<Long> getRelationByAgentId(Long agentId);

    /**
     * 根据代理名称查询学生
     *
     * @param agentName
     * @return
     */
    List<Long> getRelationByAgentName(String agentName);


    /**
     * @return java.util.List<java.lang.String>
     * @Description: 根据学生id查询代理名称
     * @Param [studentId]
     * <AUTHOR>
     */
    List<String> getAgentNameByStudentId(Long studentId);

    /**
     * 根据学生ids查询代理名称
     *
     * @param studentIds
     * @return
     */
    Map<Long, String> getAgentNameByStudentIds(Set<Long> studentIds);

    /**
     * 根据学生ids查询代理名称
     *
     * @param studentIds
     * @return
     */
    Map<Long, Set<String>> getAgentNameListByStudentIds(Set<Long> studentIds);


    /**
     * @return java.util.List<java.lang.String>
     * @Description: 根据学生获取bd
     * @Param [studentId]
     * <AUTHOR>
     */
    List<String> getBdCodeByStudentId(Long studentId);

    /**
     * 根据学生ids获取bd
     *
     * @param studentIds
     * @return
     */
    Map<Long, String> getBdCodeByStudentIds(Set<Long> studentIds);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 回获取可查询的学生ids
     * @Param []
     * <AUTHOR>
     */
    List<Long> getStudentIds();

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 获取可查询的代理ids
     * @Param []
     * <AUTHOR>
     */
    List<Long> getAgentIds();

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 获取可查询的代理ids(生效)
     * @Param []
     * <AUTHOR>
     */
    List<Long> getAgentIdsIsActive();

    /**
     * 合并学生代理数据
     * @param mergedStudentId
     * @param targetStudentId
     */
    void mergeData(Long mergedStudentId, Long targetStudentId);

    /**
     * 检查学生是否存在不同代理
     * @param mergedStudentId
     * @param targetStudentId
     */
    void checkDifferentAgent(Long mergedStudentId, Long targetStudentId);

    /**
     * 获取代理的学生数
     * @param fkAgentIds
     * @return
     */
    Map<Long,Integer> getAgentStudentNum(Set<Long> fkAgentIds);
}
