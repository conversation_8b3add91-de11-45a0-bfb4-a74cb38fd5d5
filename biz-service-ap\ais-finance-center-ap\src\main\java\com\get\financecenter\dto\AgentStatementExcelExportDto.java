package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 代理对账单Excel导出Vo类
 *
 * <AUTHOR>
 * @date 2022/1/5 17:21
 */
@Data
public class AgentStatementExcelExportDto {

    @NotNull(message = "代理id不能为空")
    @ApiModelProperty(value = "代理id")
    private Long fkAgentId;

    @NotNull(message = "学生代理合同账户Id不能为空")
    @ApiModelProperty(value = "学生代理合同账户Id")
    private Long fkAgentContractAccountId;


    @NotNull(message = "应付计划Id不能为空")
    @ApiModelProperty(value = "应付计划Ids")
    private List<Long> payablePlanIdList;

    @NotBlank(message = "导出类型不能为空")
    @ApiModelProperty(value = "导出类型 人民币中介结算汇出表:INTERMEDIARY_SETTLEMENT 人民币易思汇：EASY_TRANSFER 外币中介结算汇出表：FOREIGN_CURRENCY  留学保险：INSURANCE 留学住宿：ACCOMMODATION")
    private String excelType;


}
