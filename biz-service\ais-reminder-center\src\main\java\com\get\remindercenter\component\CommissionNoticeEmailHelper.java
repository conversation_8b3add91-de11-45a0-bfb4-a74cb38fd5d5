package com.get.remindercenter.component;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.dao.EmailSenderQueueMapper;
import com.get.remindercenter.dao.EmailTemplateMapper;
import com.get.remindercenter.dto.ApplicationStepAdmittedEmailDto;
import com.get.remindercenter.dto.CommissionNoticeEmailDto;
import com.get.remindercenter.dto.StudentOfferCreateEmailDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.EmailTemplate;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.service.RemindTaskQueueService;
import com.get.remindercenter.utils.ReminderTemplateUtils;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import com.get.rocketmqcenter.feign.IRocKetMqCenterClient;
import com.get.salecenter.entity.Student;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.feign.ISaleCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component("commissionNoticeEmailHelper")
public class CommissionNoticeEmailHelper extends EmailAbstractHelper{

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private ISaleCenterClient saleCenterClient;


    @Resource
    private RemindTaskQueueService remindTaskQueueService;



    @Resource
    private EmailSenderQueueMapper emailSenderQueueMapper;

    @Resource
    private EmailTemplateMapper emailTemplateMapper;


    @Override
    public void sendMail(EmailSenderQueue emailSenderQueue) {
        StringJoiner failedEmails = new StringJoiner("; "); // 新增：记录失败邮箱
        try {
            CommissionNoticeEmailDto commissionNoticeEmailDto = assembleEmailData(emailSenderQueue);
            StringJoiner emailsCombined = new StringJoiner(", ");
            //根据发送人id获取发送人邮箱
            Set<Long> staffEmailSet = commissionNoticeEmailDto.getStaffEmailSet().stream().collect(Collectors.toSet());
            List<StaffVo> staff = permissionCenterClient.getStaffByIds(staffEmailSet);
            Map<Long, StaffVo> data = staff.stream()
                    .collect(Collectors.toMap(
                            StaffVo::getId,  // Key: StaffVo 的 ID
                            staffVo -> staffVo  // Value: StaffVo 本身
                    ));
            String template = setEmailTemplate(commissionNoticeEmailDto);
            for (Long id : commissionNoticeEmailDto.getStaffEmailSet()) {
                try {
                EmailSystemMQMessageDto emailSystemMQMessageDto = new EmailSystemMQMessageDto();
                emailSystemMQMessageDto.setEmailSenderQueueId(emailSenderQueue.getId());
                emailSystemMQMessageDto.setTitle(commissionNoticeEmailDto.getEmailTitle());
                emailSystemMQMessageDto.setContent(template);
                emailSystemMQMessageDto.setToEmail(data.get(id).getEmail());
                //emailSystemMQMessageDto.setToEmail("<EMAIL>");
                remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
                if (GeneralTool.isNotEmpty(data.get(id).getEmail())) {
                    emailsCombined.add(data.get(id).getEmail());
                }
                } catch (Exception e) {
                    // 记录发送失败的邮箱
                    String failedEmail = data.get(id) != null &&data.get(id).getEmail() != null ? data.get(id).getEmail() : "staffId:" + id;
                    failedEmails.add(failedEmail + "(" + (e.getMessage() != null ? e.getMessage() : "发送失败") + ")");
                    log.error("邮件发送失败 - staffId: {}, email: {}, 原因: {}",  id, failedEmail, e.getMessage());
                }
            }
            LambdaUpdateWrapper<EmailSenderQueue> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(EmailSenderQueue::getId, emailSenderQueue.getId())  // 更新条件（按ID匹配）
                    .set(EmailSenderQueue::getEmailTo, emailsCombined.toString());  // 只更新 emailTo 字段
            // 如果 failedEmails 不为空，则额外更新 errorMessage
            if (failedEmails.length() > 0) {
                updateWrapper.set(EmailSenderQueue::getErrorMessage, "失败邮箱: " + failedEmails.toString());
            }
            emailSenderQueueMapper.update(null, updateWrapper);  // 传入 null，由 updateWrapper 控制更新
        } catch (Exception e) {
            log.error("CommissionNoticeEmailDto error:{}", e);
            emailSenderQueue.setErrorMessage(e.getMessage());
            emailSenderQueue.setOperationStatus(-1);
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }
    }

    @Override
    public CommissionNoticeEmailDto assembleEmailData(EmailSenderQueue emailSenderQueue) {
        //获取申请计划
        StudentOfferItem studentOfferItem =saleCenterClient.getStudentOfferItemById(emailSenderQueue.getFkTableId()).getData();
        //获取学生信息
        Student student = saleCenterClient.getStudentById(studentOfferItem.getFkStudentId()).getData();
        Long fkCompanyId = null;
        if (GeneralTool.isNotEmpty(student.getFkCompanyId())) {
            fkCompanyId = student.getFkCompanyId();
        }else {
            fkCompanyId = 3L;
        }
        //获取中英文配置
        Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
        String versionValue2 = versionConfigMap.get(fkCompanyId);
        String title = null;
        CommissionNoticeEmailDto reminderDto = new CommissionNoticeEmailDto();
        BeanUtils.copyProperties(emailSenderQueue, reminderDto);
        String emailParameter = emailSenderQueue.getEmailParameter();
        String studentName = null;

        if(GeneralTool.isNotEmpty(emailParameter)) {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, String> parsedMap = null;
            try {
                parsedMap = mapper.readValue(emailParameter, Map.class);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            String  staffIdList =parsedMap.get("staffIdList");

            if(GeneralTool.isNotEmpty(staffIdList)){
                if (staffIdList != null && staffIdList.startsWith("[") && staffIdList.endsWith("]")) {
                    staffIdList = staffIdList.substring(1, staffIdList.length() - 1);
                }
                List<Long> idList = Arrays.stream(staffIdList.split("\\s*,\\s*"))
                        .map(Long::valueOf) // 自动处理空格和转成 Long
                        .collect(Collectors.toList());
                reminderDto.setStaffEmailSet(idList);
            }
            reminderDto.setMap(parsedMap);

            if(!versionValue2.equals("en")){
                title = "申请计划佣金通知";
            }else {
                title = "Commission notification for application plan";
                parsedMap.put("studentName", student.getName());
            }
            reminderDto.setEmailTitle(title);
            reminderDto.setLanguageCode(versionValue2);

        }
        //插入标题
        if (GeneralTool.isNotEmpty(reminderDto.getEmailTitle())) {
            emailSenderQueue.setEmailTitle(reminderDto.getEmailTitle());
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }
        return reminderDto;
    }


    /**
     * 设置模板
     * @param reminderDto
     * @return
     */
    private String setEmailTemplate(CommissionNoticeEmailDto reminderDto) {
        List<EmailTemplate> remindTemplates = new ArrayList<>();
        remindTemplates = emailTemplateMapper.selectList(Wrappers.<EmailTemplate>lambdaQuery().eq(EmailTemplate::getEmailTypeKey, EmailTemplateEnum.REMINDER_COMMISSION_NOTICE.getEmailTemplateKey()));
        if (GeneralTool.isEmpty(remindTemplates)) {
            log.error("邮箱模板不存在，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        String emailTemplate =null;
        if (!reminderDto.getLanguageCode().equals("en")) {
            emailTemplate = remindTemplates.get(0).getEmailTemplate();
        }else {
            emailTemplate = remindTemplates.get(0).getEmailTemplateEn();
        }
        emailTemplate  = ReminderTemplateUtils.getReminderTemplate(reminderDto.getMap(), emailTemplate);
        if (GeneralTool.isEmpty(emailTemplate)) {
            log.error("邮箱模板内容为空，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        emailTemplate = emailTemplate.replace("#{taskTitle}", reminderDto.getEmailTitle());
        //把emailTemplate插入到父模板里
        String parentEmailTemplate = null;
        if(GeneralTool.isNotEmpty(remindTemplates.get(0).getFkParentEmailTemplateId())&&remindTemplates.get(0).getFkParentEmailTemplateId()!=0){
            EmailTemplate parentTemplate = emailTemplateMapper.selectById(remindTemplates.get(0).getFkParentEmailTemplateId());
            Map map = new HashMap();
            map.put("subtemplate",emailTemplate);
            if (reminderDto.getLanguageCode().equals("en")) {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplateEn());
            }else {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplate());
            }

        }
        return parentEmailTemplate;
    }

}
