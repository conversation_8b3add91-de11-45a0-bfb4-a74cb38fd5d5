package com.get.pmpcenter.vo.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/3/19
 * @Version 1.0
 * @apiNote:
 */
@Data
public class MajorLevelTreeVo {

    @ApiModelProperty(value = "课程等级Id")
    private Long levelId;

    @ApiModelProperty(value = "等级名称")
    private String levelName;

    @ApiModelProperty(value = "等级名称-中文")
    private String levelNameChn;

    @ApiModelProperty(value = "是否通用等级：0否/1是")
    private Integer isGeneral;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "子级课程等级")
    private List<MajorLevelTreeVo> children;

}
