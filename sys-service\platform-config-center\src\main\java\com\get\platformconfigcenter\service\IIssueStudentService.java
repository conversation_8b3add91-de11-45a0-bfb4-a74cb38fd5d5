package com.get.platformconfigcenter.service;


import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/7/6 17:56
 */
public interface IIssueStudentService {
    //TODO 注释ISSUE相关功能 lucky  2024/12/23
//    List<StudentInstitutionCourseVo> getIssueStudentCourseByStuId(Long fkStudentId);


//    void updateIssueCourseStatus(Integer statusStep, Long courseId);

    /**
     * @Description:获取加密密文
     * @Param
     * @Date 12:42 2021/7/23
     * <AUTHOR>
     */
    String getCipherText();

    void updateStudentAgent(Long fkSutdentId, Long fkAgentId);

  //  void getChangeIssueStudentSataus(Long fkIssueStudentsId, Integer status);

   // List<Long> getIssueStuIdsByAgentId(Long fkAgentId);

//    Boolean updateIssueStudentInstitutionCourse(StudentInstitutionCourse studentInstitutionCourse);
//    Boolean updateIssueStudentInstitutionCourseWithNull(StudentInstitutionCourse studentInstitutionCourse);

   // StudentInstitutionCourseVo getIssueStudentInstitutionCourseById(Long fkIssueCourseId);
}
