package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/5 21:54
 */
@Data
public class InstitutionAppFeeDto2 extends BaseVoEntity {

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @NotNull(message = "学校Id不能为空", groups = {Add.class, Update.class})
    private Long fkInstitutionId;

    @ApiModelProperty("等级类型：0本科/1硕士/2社区")
    private Integer levelType;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    private String publicLevel;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    private Date publishTime;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

    @ApiModelProperty(value = "学校名称")
    private String schoolName;

    @ApiModelProperty(value = "是否免申请费，0否/1是")
    private Boolean isFree;

    @ApiModelProperty(value = "缴纳时间信息")
    private String paymentTimeInfo;

    @ApiModelProperty(value = "链接信息")
    private String urlInfo;

    /**
     * 国家id
     */
    @ApiModelProperty(value = "国家id")
    private Long fkCountryId;

}
