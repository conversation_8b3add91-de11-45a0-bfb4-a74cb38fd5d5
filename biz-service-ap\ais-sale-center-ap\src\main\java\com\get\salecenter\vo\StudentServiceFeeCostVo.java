package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.StudentServiceFeeCost;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: Hardy
 * @create: 2024/2/5 10:58
 * @verison: 1.0
 * @description:
 */
@Data
public class StudentServiceFeeCostVo extends BaseEntity {

    @ApiModelProperty(value = "币种名称")
    private String fkCurrencyNumName;

    /**
     * 付款银行名称
     */
    @ApiModelProperty(value = "付款银行名称")
    private String bankName;

    /**
     * 成本支出描述
     */
    @ApiModelProperty(value = "成本支出描述")
    private String feeCostDescription;

    //========实体类StudentServiceFeeCost==============
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "学生留学服务费id")
    @Column(name = "fk_student_service_fee_id")
    private Long fkStudentServiceFeeId;

    @ApiModelProperty(value = "业务提供商Id")
    @Column(name = "fk_business_provider_id")
    private Long fkBusinessProviderId;

    @ApiModelProperty(value = "币种")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "金额")
    @Column(name = "amount")
    private BigDecimal amount;

    @ApiModelProperty("税金")
    @Column(name = "taxes")
    private BigDecimal taxes;

    @ApiModelProperty("银行帐号Id（公司付款银行）")
    @Column(name = "fk_bank_account_id_company")
    private Long fkBankAccountIdCompany;

    @ApiModelProperty(value = "付款日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "payment_time")
    private Date paymentTime;

    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    @ApiModelProperty(value = "状态：0作废/1生效")
    @Column(name = "status")
    private Integer status;
}
