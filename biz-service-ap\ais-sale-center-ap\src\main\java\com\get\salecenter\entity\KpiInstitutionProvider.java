package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("u_kpi_institution_provider")
public class KpiInstitutionProvider extends BaseEntity {
    @ApiModelProperty("学校提供商Id")
    private Long fkInstitutionProviderId;
    @ApiModelProperty("活动积分")
    private Integer score;
    @ApiModelProperty("剔除提供商下的学校Ids(多选)，格式：1,2,3")
    private String fkInstitutionIdsExcluding;
}
