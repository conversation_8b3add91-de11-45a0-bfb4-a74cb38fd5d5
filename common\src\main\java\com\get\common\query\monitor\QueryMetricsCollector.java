package com.get.common.query.monitor;

import com.get.common.query.cache.CacheKeyGenerator;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;
import java.util.stream.Collectors;

/**
 * 查询指标收集器
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Slf4j
@Component
@ConditionalOnProperty(
    name = "dynamic.query.metrics.enabled",
    havingValue = "true",
    matchIfMissing = true
)
@RequiredArgsConstructor
public class QueryMetricsCollector {
    
    private final CacheKeyGenerator keyGenerator;
    
    /**
     * 性能指标存储
     */
    private final Map<String, QueryMetrics> metricsMap = new ConcurrentHashMap<>();
    
    /**
     * 慢查询阈值（毫秒）
     */
    @Value("${dynamic.query.metrics.slow-query-threshold:1000}")
    private long slowQueryThreshold;
    
    /**
     * 是否启用详细监控
     */
    @Value("${dynamic.query.metrics.detailed-monitoring:true}")
    private boolean detailedMonitoring;
    
    /**
     * 慢查询记录（使用环形缓冲区）
     */
    private volatile SlowQueryRecord[] slowQueryBuffer;
    private final AtomicLong slowQueryIndex = new AtomicLong(0);
    
    /**
     * 最大慢查询记录数
     */
    @Value("${dynamic.query.metrics.max-slow-queries:100}")
    private int maxSlowQueries;
    
    /**
     * 监控数据清理间隔（分钟）
     */
    @Value("${dynamic.query.metrics.cleanup-interval:60}")
    private int cleanupIntervalMinutes;
    
    @PostConstruct
    public void init() {
        // 初始化慢查询环形缓冲区
        slowQueryBuffer = new SlowQueryRecord[maxSlowQueries];
        
        log.info("查询指标收集器初始化完成 - 慢查询阈值: {}ms, 详细监控: {}, 最大慢查询记录: {}, 清理间隔: {}分钟", 
            slowQueryThreshold, detailedMonitoring, maxSlowQueries, cleanupIntervalMinutes);
    }
    
    /**
     * 记录查询执行开始
     */
    public QueryExecutionContext startQuery(Class<?> dtoClass, Class<?> entityClass) {
        if (!detailedMonitoring) {
            return new QueryExecutionContext(dtoClass, entityClass, System.currentTimeMillis(), false);
        }
        
        String key = getMetricsKey(dtoClass, entityClass);
        QueryMetrics metrics = metricsMap.computeIfAbsent(key, k -> new QueryMetrics(dtoClass, entityClass));
        
        metrics.incrementTotalQueries();
        
        return new QueryExecutionContext(dtoClass, entityClass, System.currentTimeMillis(), true);
    }
    
    /**
     * 记录查询执行结束
     */
    public void endQuery(QueryExecutionContext context, boolean success, String errorMessage) {
        if (!context.isMonitored()) {
            return;
        }
        
        long executionTime = System.currentTimeMillis() - context.getStartTime();
        String key = getMetricsKey(context.getDtoClass(), context.getEntityClass());
        QueryMetrics metrics = metricsMap.get(key);
        
        if (metrics != null) {
            metrics.addExecutionTime(executionTime);
            
            if (success) {
                metrics.incrementSuccessQueries();
            } else {
                metrics.incrementFailedQueries();
                if (errorMessage != null) {
                    metrics.addErrorMessage(errorMessage);
                }
            }
            
            // 记录慢查询
            if (executionTime >= slowQueryThreshold) {
                recordSlowQuery(context, executionTime, errorMessage);
                metrics.incrementSlowQueries();
            }
        }
        
        // 记录性能日志
        if (executionTime >= slowQueryThreshold) {
            log.warn("慢查询检测 - DTO: {}, Entity: {}, 耗时: {}ms{}", 
                context.getDtoClass().getSimpleName(), 
                context.getEntityClass().getSimpleName(),
                executionTime,
                success ? "" : ", 错误: " + errorMessage);
        } else if (log.isDebugEnabled()) {
            log.debug("查询完成 - DTO: {}, Entity: {}, 耗时: {}ms, 结果: {}", 
                context.getDtoClass().getSimpleName(), 
                context.getEntityClass().getSimpleName(),
                executionTime,
                success ? "成功" : "失败");
        }
    }
    
    /**
     * 记录缓存命中
     */
    public void recordCacheHit(Class<?> dtoClass) {
        if (!detailedMonitoring) {
            return;
        }
        
        String key = getMetricsKey(dtoClass, null);
        QueryMetrics metrics = metricsMap.computeIfAbsent(key, k -> new QueryMetrics(dtoClass, null));
        metrics.incrementCacheHits();
    }
    
    /**
     * 记录缓存未命中
     */
    public void recordCacheMiss(Class<?> dtoClass) {
        if (!detailedMonitoring) {
            return;
        }
        
        String key = getMetricsKey(dtoClass, null);
        QueryMetrics metrics = metricsMap.computeIfAbsent(key, k -> new QueryMetrics(dtoClass, null));
        metrics.incrementCacheMisses();
    }
    
    /**
     * 获取所有指标
     */
    public Map<String, Object> getAllMetrics() {
        Map<String, Object> result = new HashMap<>();
        
        // 总体统计
        long totalQueries = metricsMap.values().stream().mapToLong(m -> m.getTotalQueries().sum()).sum();
        long totalSuccess = metricsMap.values().stream().mapToLong(m -> m.getSuccessQueries().sum()).sum();
        long totalFailed = metricsMap.values().stream().mapToLong(m -> m.getFailedQueries().sum()).sum();
        long totalSlowQueries = metricsMap.values().stream().mapToLong(m -> m.getSlowQueries().sum()).sum();
        
        Map<String, Object> overall = new HashMap<>();
        overall.put("totalQueries", totalQueries);
        overall.put("successQueries", totalSuccess);
        overall.put("failedQueries", totalFailed);
        overall.put("slowQueries", totalSlowQueries);
        overall.put("successRate", totalQueries > 0 ? String.format("%.2f%%", (double) totalSuccess / totalQueries * 100) : "0.00%");
        overall.put("slowQueryRate", totalQueries > 0 ? String.format("%.2f%%", (double) totalSlowQueries / totalQueries * 100) : "0.00%");
        
        result.put("overall", overall);
        
        // 详细指标
        Map<String, Object> detailed = new HashMap<>();
        metricsMap.forEach((key, metrics) -> {
            Map<String, Object> detail = new HashMap<>();
            detail.put("dtoClass", metrics.getDtoClass().getSimpleName());
            if (metrics.getEntityClass() != null) {
                detail.put("entityClass", metrics.getEntityClass().getSimpleName());
            }
            detail.put("totalQueries", metrics.getTotalQueries().sum());
            detail.put("successQueries", metrics.getSuccessQueries().sum());
            detail.put("failedQueries", metrics.getFailedQueries().sum());
            detail.put("slowQueries", metrics.getSlowQueries().sum());
            detail.put("cacheHits", metrics.getCacheHits().sum());
            detail.put("cacheMisses", metrics.getCacheMisses().sum());
            detail.put("avgExecutionTime", metrics.getAverageExecutionTime());
            detail.put("maxExecutionTime", metrics.getMaxExecutionTime());
            detail.put("minExecutionTime", metrics.getMinExecutionTime());
            detail.put("recentErrors", new ArrayList<>(metrics.getRecentErrors()));
            
            detailed.put(key, detail);
        });
        result.put("detailed", detailed);
        
        // 慢查询记录（从环形缓冲区读取）
        List<Map<String, Object>> slowQueryDetails = new ArrayList<>();
        SlowQueryRecord[] bufferSnapshot = slowQueryBuffer; // 快照，避免并发修改
        
        if (bufferSnapshot != null) {
            for (SlowQueryRecord record : bufferSnapshot) {
                if (record != null) { // 缓冲区可能还未完全填充
                    Map<String, Object> slowQuery = new HashMap<>();
                    slowQuery.put("dtoClass", record.getDtoClass().getSimpleName());
                    slowQuery.put("entityClass", record.getEntityClass().getSimpleName());
                    slowQuery.put("executionTime", record.getExecutionTime());
                    slowQuery.put("timestamp", record.getTimestamp().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    if (record.getErrorMessage() != null) {
                        slowQuery.put("errorMessage", record.getErrorMessage());
                    }
                    slowQueryDetails.add(slowQuery);
                }
            }
        }
        result.put("slowQueries", slowQueryDetails);
        
        return result;
    }
    
    /**
     * 获取性能报告
     */
    public Map<String, Object> getPerformanceReport() {
        Map<String, Object> report = new HashMap<>();
        
        // 性能等级评估
        long totalQueries = metricsMap.values().stream().mapToLong(m -> m.getTotalQueries().sum()).sum();
        long totalSlowQueries = metricsMap.values().stream().mapToLong(m -> m.getSlowQueries().sum()).sum();
        double slowQueryRate = totalQueries > 0 ? (double) totalSlowQueries / totalQueries : 0;
        
        String performanceGrade;
        if (slowQueryRate <= 0.01) {
            performanceGrade = "EXCELLENT";
        } else if (slowQueryRate <= 0.05) {
            performanceGrade = "GOOD";
        } else if (slowQueryRate <= 0.1) {
            performanceGrade = "FAIR";
        } else {
            performanceGrade = "POOR";
        }
        
        report.put("performanceGrade", performanceGrade);
        report.put("slowQueryRate", String.format("%.4f%%", slowQueryRate * 100));
        report.put("slowQueryThreshold", slowQueryThreshold + "ms");
        
        // Top 慢查询类型
        List<Map<String, Object>> topSlowQueries = metricsMap.entrySet().stream()
                .filter(entry -> entry.getValue().getSlowQueries().sum() > 0)
                .sorted((e1, e2) -> Long.compare(e2.getValue().getSlowQueries().sum(), e1.getValue().getSlowQueries().sum()))
                .limit(10)
                .map(entry -> {
                    Map<String, Object> item = new HashMap<>();
                    item.put("key", entry.getKey());
                    item.put("slowQueries", entry.getValue().getSlowQueries().sum());
                    item.put("avgExecutionTime", entry.getValue().getAverageExecutionTime());
                    return item;
                })
                .collect(Collectors.toList());
        report.put("topSlowQueries", topSlowQueries);
        
        // 建议
        List<String> recommendations = new ArrayList<>();
        if (slowQueryRate > 0.1) {
            recommendations.add("慢查询比例过高，建议优化查询条件或添加数据库索引");
        }
        if (totalQueries > 10000 && slowQueryRate > 0.05) {
            recommendations.add("高并发场景下建议启用查询结果缓存");
        }
        if (recommendations.isEmpty()) {
            recommendations.add("查询性能良好，继续保持");
        }
        report.put("recommendations", recommendations);
        
        return report;
    }
    
    /**
     * 清空所有指标
     */
    public void clearAllMetrics() {
        metricsMap.clear();
        
        // 清空环形缓冲区
        if (slowQueryBuffer != null) {
            for (int i = 0; i < slowQueryBuffer.length; i++) {
                slowQueryBuffer[i] = null;
            }
        }
        slowQueryIndex.set(0);
        
        log.info("所有查询指标已清空");
    }
    
    /**
     * 清空指定类的指标
     */
    public void clearMetrics(Class<?> dtoClass, Class<?> entityClass) {
        String key = getMetricsKey(dtoClass, entityClass);
        metricsMap.remove(key);
        log.debug("清空指标 - key: {}", key);
    }
    
    /**
     * 生成指标键（使用统一的key生成器）
     */
    private String getMetricsKey(Class<?> dtoClass, Class<?> entityClass) {
        return keyGenerator.generateMetricsKey(dtoClass, entityClass);
    }
    
    /**
     * 记录慢查询（使用无锁环形缓冲区）
     */
    private void recordSlowQuery(QueryExecutionContext context, long executionTime, String errorMessage) {
        SlowQueryRecord record = new SlowQueryRecord(
            context.getDtoClass(),
            context.getEntityClass(),
            executionTime,
            LocalDateTime.now(),
            errorMessage
        );
        
        // 使用原子操作获取下一个索引位置
        long index = slowQueryIndex.getAndIncrement();
        int bufferIndex = (int) (index % maxSlowQueries);
        
        // 无锁写入环形缓冲区
        slowQueryBuffer[bufferIndex] = record;
        
        log.debug("记录慢查询 - 索引: {}, 缓冲区位置: {}, 执行时间: {}ms", 
            index, bufferIndex, executionTime);
    }
    
    /**
     * 查询执行上下文
     */
    @Getter
    @AllArgsConstructor
    public static class QueryExecutionContext {
        private final Class<?> dtoClass;
        private final Class<?> entityClass;
        private final long startTime;
        private final boolean monitored;
    }
    
    /**
     * 慢查询记录
     */
    @Getter
    @AllArgsConstructor
    private static class SlowQueryRecord {
        private final Class<?> dtoClass;
        private final Class<?> entityClass;
        private final long executionTime;
        private final LocalDateTime timestamp;
        private final String errorMessage;
    }
    
    /**
     * 查询指标
     */
    @Getter
    private static class QueryMetrics {
        private final Class<?> dtoClass;
        private final Class<?> entityClass;
        private final LongAdder totalQueries = new LongAdder();
        private final LongAdder successQueries = new LongAdder();
        private final LongAdder failedQueries = new LongAdder();
        private final LongAdder slowQueries = new LongAdder();
        private final LongAdder cacheHits = new LongAdder();
        private final LongAdder cacheMisses = new LongAdder();
        private final LongAdder totalExecutionTime = new LongAdder();
        private final AtomicLong maxExecutionTime = new AtomicLong(0);
        private final AtomicLong minExecutionTime = new AtomicLong(Long.MAX_VALUE);
        private final String[] recentErrorsBuffer = new String[10]; // 固定大小的错误环形缓冲区
        private final AtomicLong errorIndex = new AtomicLong(0);
        
        public QueryMetrics(Class<?> dtoClass, Class<?> entityClass) {
            this.dtoClass = dtoClass;
            this.entityClass = entityClass;
        }
        
        public void incrementTotalQueries() {
            totalQueries.increment();
        }
        
        public void incrementSuccessQueries() {
            successQueries.increment();
        }
        
        public void incrementFailedQueries() {
            failedQueries.increment();
        }
        
        public void incrementSlowQueries() {
            slowQueries.increment();
        }
        
        public void incrementCacheHits() {
            cacheHits.increment();
        }
        
        public void incrementCacheMisses() {
            cacheMisses.increment();
        }
        
        public void addExecutionTime(long time) {
            totalExecutionTime.add(time);
            maxExecutionTime.updateAndGet(current -> Math.max(current, time));
            minExecutionTime.updateAndGet(current -> current == Long.MAX_VALUE ? time : Math.min(current, time));
        }
        
        public void addErrorMessage(String errorMessage) {
            if (errorMessage != null) {
                // 使用无锁环形缓冲区
                long index = errorIndex.getAndIncrement();
                int bufferIndex = (int) (index % recentErrorsBuffer.length);
                recentErrorsBuffer[bufferIndex] = errorMessage;
            }
        }
        
        public double getAverageExecutionTime() {
            long total = totalQueries.sum();
            return total > 0 ? (double) totalExecutionTime.sum() / total : 0;
        }
        
        public long getMaxExecutionTime() {
            return maxExecutionTime.get() == 0 ? 0 : maxExecutionTime.get();
        }
        
        public long getMinExecutionTime() {
            return minExecutionTime.get() == Long.MAX_VALUE ? 0 : minExecutionTime.get();
        }
        
        public List<String> getRecentErrors() {
            List<String> errors = new ArrayList<>();
            // 快照读取，避免并发修改
            String[] bufferSnapshot = recentErrorsBuffer.clone();
            
            for (String error : bufferSnapshot) {
                if (error != null) {
                    errors.add(error);
                }
            }
            return errors;
        }
    }
}