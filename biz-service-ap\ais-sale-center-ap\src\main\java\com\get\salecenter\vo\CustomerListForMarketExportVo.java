package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: Hardy
 * @create: 2022/7/12 18:10
 * @verison: 1.0
 * @description:
 */
@Data
public class CustomerListForMarketExportVo {

    @ApiModelProperty(value = "所属公司")
    private String companyName;

    @ApiModelProperty(value = "状态")
    private String statusName;

    @ApiModelProperty(value = "结算状态")
    private String settlementStatusName;

    @ApiModelProperty(value = "币种")
    private String payableCurrencyTypeName;

    @ApiModelProperty(value = "应付金额")
    private BigDecimal payableAmount;

    @ApiModelProperty(value = "实付金额")
    private BigDecimal actualPayableAmount;

    @ApiModelProperty(value = "应付未付")
    private BigDecimal diffPayableAmount;

    @ApiModelProperty(value = "收款状态")
    private String arStatusName;

    @ApiModelProperty(value = "付款状态")
    private String apStatusName;

    @ApiModelProperty(value = "是否预付")
    private String payInAdvanceStatusName;

    @ApiModelProperty(value = "申请步骤状态")
    private String stepName;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "BD名称")
    private String bdName;

    @ApiModelProperty(value = "学生姓名（中/英）")
    private String studentName;

    @ApiModelProperty(value = "学生生日")
    private String birthday;

    @ApiModelProperty(value = "国家")
    private String fkAreaCountryName;

    @ApiModelProperty(value = "申请学校")
    private String institutionFullName;

    @ApiModelProperty(value = "申请课程")
    private String courseFullName;

    @ApiModelProperty(value = "开学时间")
    private String openingTime;

    @ApiModelProperty("创建人")
    private String gmtCreateUser;

    @ApiModelProperty("创建时间")
    private String gmtCreate;

    @ApiModelProperty(value = "代理标签")
    private String agentLabelNames;
}
