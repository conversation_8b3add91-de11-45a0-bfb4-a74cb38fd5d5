package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class AssistantVo {
    private Integer studentCount;
    private String name;
    private String nameZh;
    private BigDecimal ratio;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<AssistantVo> children = new ArrayList<>();
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<AssistantVo> others = new ArrayList<>();
}
