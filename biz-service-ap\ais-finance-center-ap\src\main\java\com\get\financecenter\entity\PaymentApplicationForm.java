package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 支付申请单
 */
@Data
@TableName("m_payment_application_form")
public class PaymentApplicationForm extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "部门Id")
    private Long fkDepartmentId;

    @ApiModelProperty(value = "申请人Id")
    private Long fkStaffId;

    @ApiModelProperty("关联撤销表单Id")
    private Long fkPaymentApplicationFormIdRevoke;

    @ApiModelProperty(value = "支付申请单编号（系统生成）")
    private String num;

    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "摘要")
    private String summary;

    @ApiModelProperty(value = "凭证Id")
    private Long fkVouchId;

    @ApiModelProperty(value = "是否创建凭证：0否/1是")
    private Boolean isVouchCreated;

    @ApiModelProperty(value = "创建凭证时间")
    private Date dateVouchCreated;

    @ApiModelProperty(value = "创建凭证人")
    private Long fkStaffIdVouchCreated;

    @ApiModelProperty(value = "通知员工Ids，支持多选（英文逗号分隔：1,2,3）")
    private String fkStaffIdsNotice;

    @ApiModelProperty(value = "状态：0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销")
    private Integer status;

}