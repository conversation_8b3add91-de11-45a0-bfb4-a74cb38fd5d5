package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * @author: Neil
 * @description: 修改业务代理绑定VO
 * @date: 2022/5/10 10:32
 * @return
 */
@Data
public class StudentAgentBindingDto {
    @ApiModelProperty(value = "学生编号")
    private String fkStudentNum;

    @ApiModelProperty(value = "学生ids")
    private Set<Long> fkStudentIds;

    @ApiModelProperty(value = "代理id")
    private Long fkAgentId;

    @ApiModelProperty(value = "申请方案代理id")
    private Long studentOfferAgentId;

    @ApiModelProperty(value = "申请方案BD id")
    private Long studentOfferStaffId;

    @ApiModelProperty(value = "申请方案代理联系人email")
    private String studentOfferEmail;

    @ApiModelProperty(value = "代理联系人Id")
    private Long fkContactPersonId;

    @ApiModelProperty(value = "留学住宿代理id")
    private Long accommodationAgentId;

    @ApiModelProperty(value = "留学住宿BD id")
    private Long accommodationStaffId;

    @ApiModelProperty(value = "留学保险代理id")
    private Long insuranceAgentId;

    @ApiModelProperty(value = "留学保险BD id")
    private Long insuranceStaffId;

    @ApiModelProperty(value = "留学服务费代理id")
    private Long feeAgentId;

    @ApiModelProperty(value = "留学服务费BD id")
    private Long feeStaffId;

    @ApiModelProperty(value = "是否取消原学生绑定代理（false否/true取消）")
    private Boolean isCancelOriginalBinding = false;

    @ApiModelProperty(value = "是否取消原学生绑定代理（false否/true取消）")
    private Boolean isSetupOffer = false;

    @ApiModelProperty(value = "修改模式 0：修改学生代理/1：单独修改方案代理 ")
    private Integer modifiedMode;

    @ApiModelProperty(value = "申请方案id")
    private Set<Long> offerIds;

}
