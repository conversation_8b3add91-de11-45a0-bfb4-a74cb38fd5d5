package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("m_convention_hotel_pay")
public class ConventionHotelPay extends BaseEntity implements Serializable {
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    @Column(name = "fk_convention_id")
    private Long fkConventionId;

    /**
     * 峰会报名Id
     */
    @ApiModelProperty(value = "峰会报名Id")
    @Column(name = "fk_convention_registration_id")
    private Long fkConventionRegistrationId;

    /**
     * 峰会报名Id
     */
    @ApiModelProperty(value = "峰会参展人员Id")
    @Column(name = "fk_convention_person_id")
    private Long fkConventionPersonId;

    /**
     * 系统订单号
     */
    @ApiModelProperty(value = "系统订单号")
    @Column(name = "system_order_num")
    private String systemOrderNum;

    /**
     * 微信订单号
     */
    @ApiModelProperty(value = "微信订单号")
    @Column(name = "pay_order_num")
    private String payOrderNum;

    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    @Column(name = "institution_name")
    private String institutionName;

    /**
     * 住客名称，逗号分隔
     */
    @ApiModelProperty(value = "住客名称，逗号分隔")
    @Column(name = "residents")
    private String residents;

    /**
     * 房型
     */
    @ApiModelProperty(value = "房型")
    @Column(name = "room_type")
    private String roomType;

    /**
     * 到店日期
     */
    @ApiModelProperty(value = "到店日期")
    @Column(name = "check_in_time")
    private Date checkInTime;

    /**
     * 离店日期
     */
    @ApiModelProperty(value = "离店日期")
    @Column(name = "check_out_time")
    private Date checkOutTime;

    /**
     * 支付数量
     */
    @ApiModelProperty(value = "支付数量")
    @Column(name = "pay_count")
    private Integer payCount;

    /**
     * 支付金额
     */
    @ApiModelProperty(value = "支付金额")
    @Column(name = "pay_amount")
    private BigDecimal payAmount;

    /**
     * 支付状态：0未支付/1完成支付
     */
    @ApiModelProperty(value = "支付状态：0未支付/1完成支付")
    @Column(name = "pay_type")
    private Integer payType;

    /**
     * 发票信息
     */
    @ApiModelProperty(value = "发票信息")
    @Column(name = "invoice_info")
    private String invoiceInfo;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    private static final long serialVersionUID = 1L;
}