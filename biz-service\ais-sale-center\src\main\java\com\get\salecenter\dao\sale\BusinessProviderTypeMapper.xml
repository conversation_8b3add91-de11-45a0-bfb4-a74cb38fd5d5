<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.BusinessProviderTypeMapper">
    <select id="getMaxViewOrder" resultType="java.lang.Integer">
        select ifnull(max(view_order)+1,1) from u_business_provider_type
      </select>
    <select id="selectBusinessProviderType" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        select n.id,n.type_name AS name from u_business_provider_type n
        ORDER BY n.view_order desc
    </select>
</mapper>