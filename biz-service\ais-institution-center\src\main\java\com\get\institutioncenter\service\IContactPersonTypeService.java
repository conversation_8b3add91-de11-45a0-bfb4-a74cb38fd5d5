package com.get.institutioncenter.service;


import com.get.common.result.Page;
import com.get.core.mybatis.utils.ValidList;
import com.get.institutioncenter.vo.ContactPersonTypeVo;
import com.get.institutioncenter.dto.ContactPersonTypeListDto;
import com.get.institutioncenter.dto.ContactPersonTypeUpdateDto;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/2/8 11:59
 * @verison: 1.0
 * @description:
 */
public interface IContactPersonTypeService {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    ContactPersonTypeVo findContactPersonTypeById(Long id);

    /**
     * @return void
     * @Description :批量新增
     * @Param [contactPersonTypeVos]
     * <AUTHOR>
     */
    void batchAdd(ValidList<ContactPersonTypeUpdateDto> contactPersonTypeVos);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    void delete(Long id);

    /**
     * 修改
     *
     * @param contactPersonTypeVo
     * @return
     */
    ContactPersonTypeVo updateContactPersonType(ContactPersonTypeUpdateDto contactPersonTypeVo);

    /**
     * 列表
     *
     * @param contactPersonTypeVo
     * @param page
     * @return
     */
    List<ContactPersonTypeVo> getContactPersonTypes(ContactPersonTypeListDto contactPersonTypeVo, Page page);


    /**
     * 列表不分页
     *
     * @return
     */
    List<ContactPersonTypeVo> getContactPersonTypes();


    /**
     * 上移下移
     *
     * @param contactPersonTypeVos
     * @return
     */
    void movingOrder(List<ContactPersonTypeUpdateDto> contactPersonTypeVos);
}
