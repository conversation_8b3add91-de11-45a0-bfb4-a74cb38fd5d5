package com.get.permissioncenter.service.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.dao.StaffOfficeMapper;
import com.get.permissioncenter.vo.OfficeVo;
import com.get.permissioncenter.vo.StaffOfficeVo;
import com.get.permissioncenter.entity.StaffOffice;
import com.get.permissioncenter.service.IOfficeService;
import com.get.permissioncenter.service.IStaffOfficeService;
import com.get.permissioncenter.dto.OfficeDto;
import com.get.permissioncenter.dto.StaffOfficeBatchUpdateDto;
import com.get.permissioncenter.dto.StaffOfficeDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/7/24
 * @TIME: 15:24
 * @Description: 业务办公室实现类
 **/
@Service
public class StaffOfficeServiceImpl extends BaseServiceImpl<StaffOfficeMapper, StaffOffice> implements IStaffOfficeService {

    @Resource
    private IOfficeService officeService;
    @Resource
    private StaffOfficeMapper staffOfficeMapper;
    @Autowired
    private UtilService utilService;

    @Override
    public List<StaffOfficeVo> getStaffOfficeById(Long fkCompanyId, Long staffId, String keyWord) {
        if (GeneralTool.isEmpty(staffId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //查询单个用户中间表的业务办公室
//        Example example = new Example(StaffOffice.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkStaffId", staffId);
//        List<StaffOffice> staffOffices = staffOfficeMapper.selectByExample(example);

        List<StaffOffice> staffOffices = staffOfficeMapper.selectList(Wrappers.<StaffOffice>query().lambda().eq(StaffOffice::getFkStaffId, staffId));

        OfficeDto officeDto1 = new OfficeDto();
        //获取所有的办公室
        officeDto1.setKeyWord(keyWord);
        officeDto1.setFkCompanyId(fkCompanyId);
        List<OfficeVo> office = officeService.getOffice(officeDto1);
        //转换成输出的DTO
        List<StaffOfficeVo> officeDtos
                = office.stream().map(officeDto -> BeanCopyUtils.objClone(officeDto, StaffOfficeVo::new)).collect(Collectors.toList());
        //判断是否为当前用户的业务办公室
        if (GeneralTool.isNotEmpty(officeDtos) && GeneralTool.isNotEmpty(staffOffices)) {
            for (StaffOfficeVo officeDto : officeDtos) {
                for (StaffOffice staffOffice : staffOffices) {
                    if (staffOffice.getFkOfficeId().equals(officeDto.getId())) {
                        officeDto.setIsSelect(true);
                    }
                }
            }
        }
        return officeDtos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long updateStaffOffice(Long id, List<StaffOfficeDto> staffOfficeDtos) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }

        //删除原先记录
//        Example example = new Example(StaffOffice.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkStaffId", id);
//        staffOfficeMapper.deleteByExample(example);

        staffOfficeMapper.delete(Wrappers.<StaffOffice>query().lambda().eq(StaffOffice::getFkStaffId, id));
        List<StaffOfficeDto> collect = staffOfficeDtos.stream().filter(StaffOfficeDto::getIsSelect).collect(Collectors.toList());
        if (GeneralTool.isNotEmpty(staffOfficeDtos)) {
            for (StaffOfficeDto staffOfficeDto : collect) {
                StaffOffice staffOffice = new StaffOffice();
                staffOffice.setFkOfficeId(staffOfficeDto.getId());
                staffOffice.setFkStaffId(id);
//                utilService.setUpdateInfo(staffOffice);
//                staffOfficeMapper.insertSelective(staffOffice);
                utilService.updateUserInfoToEntity(staffOffice);
                staffOfficeMapper.insert(staffOffice);
            }
        }
        return id;
    }

    /**
     * 批量设置业务办公室
     *
     * @Date 12:41 2023/12/11
     * <AUTHOR>
     */
    @Override
    public void batchUpdateStaffOffice(StaffOfficeBatchUpdateDto staffOfficeBatchUpdateDto) {
        if (!staffOfficeBatchUpdateDto.getOfficeFlag()) {
            staffOfficeMapper.delete(Wrappers.<StaffOffice>query().lambda()
                    .in(StaffOffice::getFkStaffId, staffOfficeBatchUpdateDto.getFkStaffIds()));
        }
        List<StaffOffice> staffOfficeList = new ArrayList<>();
        for (Long fkStaffId : staffOfficeBatchUpdateDto.getFkStaffIds()) {
            List<StaffOffice> staffOffices = staffOfficeMapper.selectList(Wrappers.<StaffOffice>query().lambda().eq(StaffOffice::getFkStaffId, fkStaffId));
            for (Long fkOfficeId : staffOfficeBatchUpdateDto.getFkOfficeIds()) {
                boolean flag = false;
                for (StaffOffice staffOffice : staffOffices) {
                    if (staffOffice.getFkOfficeId().equals(fkOfficeId)) {
                        flag = true;
                    }
                }
                if (flag) {
                    continue;
                }
                StaffOffice staffOffice = new StaffOffice();
                staffOffice.setFkOfficeId(fkOfficeId);
                staffOffice.setFkStaffId(fkStaffId);
                utilService.setCreateInfo(staffOffice);
                staffOfficeList.add(staffOffice);
            }
        }
        if (GeneralTool.isNotEmpty(staffOfficeList)) {
            staffOfficeMapper.insertBatchSomeColumn(staffOfficeList);
        }
    }

}
