package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.get.common.consts.CacheKeyConstants;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.redis.cache.GetRedis;
import com.get.core.redis.lock.RedisLockClient;
import com.get.core.secure.StaffInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.start.constant.AppConstant;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.reportcenter.feign.IReportCenterClient;
import com.get.salecenter.bo.AgentApplicationRankingQueryBo;
import com.get.salecenter.dao.sale.AgentMapper;
import com.get.salecenter.dao.sale.StudentMapper;
import com.get.salecenter.dao.sale.StudentOfferItemMapper;
import com.get.salecenter.dao.sale.StudentOfferItemStepMapper;
import com.get.salecenter.dao.sale.StudentOfferNoticeMapper;
import com.get.salecenter.dao.sale.StudentOfferNoticeTemplateMapper;
import com.get.salecenter.dao.sale.StudentProjectRoleStaffMapper;
import com.get.salecenter.dto.*;
import com.get.salecenter.entity.*;
import com.get.salecenter.service.*;
import com.get.salecenter.vo.*;
import com.get.salecenter.vo.AgentApplicationRankingVo;
import com.get.salecenter.utils.sale.VerifyStudentOfferItemUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;



/**
 * <AUTHOR>
 * @DATE: 2022/3/30
 * @TIME: 17:33
 * @Description:基础统计异步处理
 **/
@Service
@Slf4j
public class AsyncStatisticsServiceImpl implements AsyncStatisticsService {
    private final static ObjectMapper objectMapper = new ObjectMapper();

    @Resource
    private StudentOfferItemMapper studentOfferItemMapper;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private VerifyStudentOfferItemUtils verifyStudentOfferItemUtils;

    @Resource
    private StudentOfferItemStepMapper studentOfferItemStepMapper;

    @Resource
    private StudentOfferNoticeTemplateMapper studentOfferNoticeTemplateMapper;

    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Resource
    private StudentMapper studentMapper;

    @Resource
    private StudentProjectRoleStaffMapper studentProjectRoleStaffMapper;

    @Resource
    private DataSourceTransactionManager dataSourceTransactionManager;

    @Resource
    private TransactionDefinition transactionDefinition;

    @Lazy
    @Resource
    private StudentOfferNoticeService studentOfferNoticeService;

    @Resource
    private StudentOfferNoticeMapper studentOfferNoticeMapper;

    @Resource
    private AgentMapper agentMapper;

    @Resource
    private RedisLockClient redisLockClient;

    @Resource
    private IReportCenterClient reportCenterClient;

    @Resource
    private GetRedis getRedis;

    @Resource
    private IStudentOfferService studentOfferService;
    @Resource
    @Lazy
    private KpiPlanService kpiPlanService;
    @Resource
    @Lazy
    private KpiPlanTaskResultService kpiPlanTaskResultService;


//    @Async
//    @Override
//    public CompletableFuture<List<StudentApplicationStatisticsVo>> getStatisticsDtos(StatisticsDto statisticsVo, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin) {
//        //针对like的字段转为小写
//        if(GeneralTool.isNotEmpty(statisticsVo.getBdName()))
//        {
//            statisticsVo.setBdName(statisticsVo.getBdName().toLowerCase());
//        }
//        statisticsVo.setGeaConfirmationStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.GEA_CONFIRMATION_STATISTICS_STEP));
//        statisticsVo.setGeaSuccessStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.GEA_SUCCESS_STATISTICS_STEP));
//        statisticsVo.setIaeConfirmationStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.IAE_CONFIRMATION_STATISTICS_STEP));
//        statisticsVo.setIaeSuccessStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.IAE_SUCCESS_STATISTICS_STEP));
//        List<StudentApplicationStatisticsVo> resout = studentOfferItemMapper.getStudentApplicationStatistics(statisticsVo, isStudentOfferItemFinancialHiding, isStudentAdmin);
//        List<StudentApplicationStatisticsVo> confirmationByStudentList = studentOfferItemMapper.getStudentApplicationStatisticsByStudent(statisticsVo, isStudentOfferItemFinancialHiding, isStudentAdmin,1);
//        List<StudentApplicationStatisticsVo> successByStudentList = studentOfferItemMapper.getStudentApplicationStatisticsByStudent(statisticsVo, isStudentOfferItemFinancialHiding, isStudentAdmin,2);
//        for(StudentApplicationStatisticsVo vo:resout){
//            //确认量（按学生）
//            if(GeneralTool.isNotEmpty(confirmationByStudentList)){
//                for(StudentApplicationStatisticsVo d:confirmationByStudentList){
//                    if(GeneralTool.isEmpty(vo.getFkAreaCountryId()) || vo.getFkAreaCountryId().equals(d.getFkAreaCountryId())){
//                        vo.setConfirmationCountByStudent(d.getConfirmationCountByStudent());
//                    }
//                }
//            }
//
//            //成功量（按学生）
//            if(GeneralTool.isNotEmpty(successByStudentList)){
//                for(StudentApplicationStatisticsVo d:successByStudentList){
//                    if(GeneralTool.isEmpty(vo.getFkAreaCountryId()) || vo.getFkAreaCountryId().equals(d.getFkAreaCountryId())){
//                        vo.setSuccessCountByStudent(d.getSuccessCountByStudent());
//                    }
//                }
//            }
//
//        }
//
//        return CompletableFuture.completedFuture(resout);
//    }



//    @Async
    @Override
    public List<StudentApplicationStatisticsVo> getCreateStatisticsDtos(StatisticsDto statisticsDto, String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin){
        //针对like的字段转为小写
        if(GeneralTool.isNotEmpty(statisticsDto.getBdName()))
        {
            statisticsDto.setBdName(statisticsDto.getBdName().toLowerCase());
        }
        List<StudentApplicationStatisticsVo> resout = studentOfferItemMapper.getStudentApplicationStatistics(statisticsDto, type , isStudentOfferItemFinancialHiding, isStudentAdmin, statisticsDto.getStaffFollowerIds(),
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds());
        return resout;
    }

//    @Async
    @Override
    public List<StudentApplicationStatisticsVo> getApplicationStatisticsDtos(StatisticsDto statisticsDto, String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin){
        //针对like的字段转为小写
        if(GeneralTool.isNotEmpty(statisticsDto.getBdName()))
        {
            statisticsDto.setBdName(statisticsDto.getBdName().toLowerCase());
        }
        List<StudentApplicationStatisticsVo> resout = studentOfferItemMapper.getStudentApplicationStatistics(statisticsDto, type , isStudentOfferItemFinancialHiding, isStudentAdmin, statisticsDto.getStaffFollowerIds(),
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds());
        return resout;
    }

//    @Async
    @Override
    public List<StudentApplicationStatisticsVo> getConfirmationStatisticsDtos(StatisticsDto statisticsDto, String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin){
        //针对like的字段转为小写
        if(GeneralTool.isNotEmpty(statisticsDto.getBdName()))
        {
            statisticsDto.setBdName(statisticsDto.getBdName().toLowerCase());
        }
        statisticsDto.setGeaConfirmationStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.GEA_CONFIRMATION_STATISTICS_STEP));
        statisticsDto.setIaeConfirmationStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.IAE_CONFIRMATION_STATISTICS_STEP));
        List<StudentApplicationStatisticsVo> resout = studentOfferItemMapper.getStudentApplicationStatistics(statisticsDto, type , isStudentOfferItemFinancialHiding, isStudentAdmin, statisticsDto.getStaffFollowerIds(),
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds());
        return resout;
    }

//    @Async
    @Override
    public List<StudentApplicationStatisticsVo> getSuccessStatisticsDtos(StatisticsDto statisticsDto, String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin){
        //针对like的字段转为小写
        if(GeneralTool.isNotEmpty(statisticsDto.getBdName()))
        {
            statisticsDto.setBdName(statisticsDto.getBdName().toLowerCase());
        }
        statisticsDto.setGeaSuccessStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.GEA_SUCCESS_STATISTICS_STEP));
        statisticsDto.setIaeSuccessStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.IAE_SUCCESS_STATISTICS_STEP));
        List<StudentApplicationStatisticsVo> resout = studentOfferItemMapper.getStudentApplicationStatistics(statisticsDto, type , isStudentOfferItemFinancialHiding, isStudentAdmin, statisticsDto.getStaffFollowerIds(),
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds());
        return resout;
    }

//    @Async
    @Override
    public List<StudentApplicationStatisticsVo> getAgentStateApplicationStatisticsByStudent(StatisticsDto statisticsDto, String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin){
        //针对like的字段转为小写
        if(GeneralTool.isNotEmpty(statisticsDto.getBdName()))
        {
            statisticsDto.setBdName(statisticsDto.getBdName().toLowerCase());
        }
        statisticsDto.setGeaConfirmationStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.GEA_CONFIRMATION_STATISTICS_STEP));
        statisticsDto.setIaeConfirmationStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.IAE_CONFIRMATION_STATISTICS_STEP));
        List<StudentApplicationStatisticsVo> resout = studentOfferItemMapper.getAgentStateApplicationStatisticsByStudent(statisticsDto,
                isStudentOfferItemFinancialHiding, isStudentAdmin,type, statisticsDto.getStaffFollowerIds(),
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds());
        return resout;
    }

//    @Async
    @Override
    public List<StudentApplicationStatisticsVo> getConfirmationByStudentStatisticsDtos(StatisticsDto statisticsDto, String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin){
        //针对like的字段转为小写
        if(GeneralTool.isNotEmpty(statisticsDto.getBdName()))
        {
            statisticsDto.setBdName(statisticsDto.getBdName().toLowerCase());
        }
        statisticsDto.setGeaConfirmationStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.GEA_CONFIRMATION_STATISTICS_STEP));
        statisticsDto.setIaeConfirmationStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.IAE_CONFIRMATION_STATISTICS_STEP));
        List<StudentApplicationStatisticsVo> resout = studentOfferItemMapper.getStudentApplicationStatisticsByStudent(statisticsDto, isStudentOfferItemFinancialHiding, isStudentAdmin,type, statisticsDto.getStaffFollowerIds(),
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds());
        return resout;
    }

//    @Async
    @Override
    public List<StudentApplicationStatisticsVo> getSuccessByStudentStatisticsDtos(StatisticsDto statisticsDto, String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin){
        //针对like的字段转为小写
        if(GeneralTool.isNotEmpty(statisticsDto.getBdName()))
        {
            statisticsDto.setBdName(statisticsDto.getBdName().toLowerCase());
        }
        statisticsDto.setGeaSuccessStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.GEA_SUCCESS_STATISTICS_STEP));
        statisticsDto.setIaeSuccessStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.IAE_SUCCESS_STATISTICS_STEP));
        List<StudentApplicationStatisticsVo> resout = studentOfferItemMapper.getStudentApplicationStatisticsByStudent(statisticsDto, isStudentOfferItemFinancialHiding, isStudentAdmin,type, statisticsDto.getStaffFollowerIds(),
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds());
        return resout;
    }


    @Async
    @Override
    public CompletableFuture<List<AgentApplicationRankingVo>> getAgentApplicationRanking(AgentApplicationRankingQueryBo rankingQueryBo, String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin){
        List<AgentApplicationRankingVo> statisticsList = studentOfferItemMapper.getAgentApplicationRanking(rankingQueryBo,type,isStudentOfferItemFinancialHiding,isStudentAdmin, rankingQueryBo.getStaffFollowerIds(),
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds());
        return CompletableFuture.completedFuture(statisticsList);
    }

    @Async
    @Override
    public CompletableFuture<List<AgentApplicationRankingVo>> getAgentApplicationRankingByStudent(AgentApplicationRankingQueryBo rankingQueryBo, String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin){
        List<AgentApplicationRankingVo> statisticsList = studentOfferItemMapper.getAgentApplicationRankingByStudent(rankingQueryBo,
                type,isStudentOfferItemFinancialHiding,isStudentAdmin, rankingQueryBo.getStaffFollowerIds(),
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds());
        return CompletableFuture.completedFuture(statisticsList);
    }



    @Async
    @Override
    public CompletableFuture<List<VipStatisticsVo>> getVipStatistics(VipStatisticsDto vipStatisticsDto, String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin){
        vipStatisticsDto.setGeaConfirmationStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.GEA_CONFIRMATION_STATISTICS_STEP));
        vipStatisticsDto.setGeaSuccessStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.GEA_SUCCESS_STATISTICS_STEP));
        vipStatisticsDto.setIaeConfirmationStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.IAE_CONFIRMATION_STATISTICS_STEP));
        vipStatisticsDto.setIaeSuccessStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.IAE_SUCCESS_STATISTICS_STEP));
        List<VipStatisticsVo> statisticsList = studentOfferItemMapper.getVipStatistics(vipStatisticsDto,type,isStudentOfferItemFinancialHiding,isStudentAdmin, vipStatisticsDto.getStaffFollowerIds(),
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds());
        return CompletableFuture.completedFuture(statisticsList);
    }

    @Async
    @Override
    public CompletableFuture<List<VipStatisticsVo>> getVipStatisticsByStudent(VipStatisticsDto vipStatisticsDto, String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin){
        vipStatisticsDto.setGeaConfirmationStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.GEA_CONFIRMATION_STATISTICS_STEP));
        vipStatisticsDto.setGeaSuccessStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.GEA_SUCCESS_STATISTICS_STEP));
        vipStatisticsDto.setIaeConfirmationStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.IAE_CONFIRMATION_STATISTICS_STEP));
        vipStatisticsDto.setIaeSuccessStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.IAE_SUCCESS_STATISTICS_STEP));
        List<VipStatisticsVo> statisticsList = studentOfferItemMapper.getVipStatisticsByStudent(vipStatisticsDto,type,isStudentOfferItemFinancialHiding,isStudentAdmin, vipStatisticsDto.getStaffFollowerIds(),
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds());
        return CompletableFuture.completedFuture(statisticsList);
    }

    @Async
    @Override
    public CompletableFuture<AgentApplicationStatisticsVo> getAgentCountryApplicationStatistics(AgentPerformanceStatisticsDto agentPerformanceStatisticsDto, StaffInfo staffInfo){
        AgentApplicationStatisticsVo statistics = studentOfferItemMapper.getAgentCountryApplicationStatistics(agentPerformanceStatisticsDto,
                staffInfo.getIsStudentOfferItemFinancialHiding(),staffInfo.getIsStudentAdmin(), agentPerformanceStatisticsDto.getStaffFollowerIds(),
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds());
        return CompletableFuture.completedFuture(statistics);
    }

    @Async
    @Override
    public CompletableFuture<AgentApplicationStatisticsVo> getAgentCountryApplicationStatisticsByStudent(AgentPerformanceStatisticsDto agentPerformanceStatisticsDto, String type, StaffInfo staffInfo){
        agentPerformanceStatisticsDto.setGeaConfirmationStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.GEA_CONFIRMATION_STATISTICS_STEP));
        agentPerformanceStatisticsDto.setGeaSuccessStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.GEA_SUCCESS_STATISTICS_STEP));
        agentPerformanceStatisticsDto.setIaeConfirmationStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.IAE_CONFIRMATION_STATISTICS_STEP));
        agentPerformanceStatisticsDto.setIaeSuccessStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.IAE_SUCCESS_STATISTICS_STEP));
        AgentApplicationStatisticsVo statistics = studentOfferItemMapper.getAgentCountryApplicationStatisticsByStudent(agentPerformanceStatisticsDto,
                staffInfo.getIsStudentOfferItemFinancialHiding(),staffInfo.getIsStudentAdmin(),type, agentPerformanceStatisticsDto.getStaffFollowerIds(),
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds());
        return CompletableFuture.completedFuture(statistics);

    }


    @Async("saleTaskExecutor")
    @Override
    public void asyncEmailStatistics(Map<String, String> headerMap, StaffInfo staffInfo, String locale, List<Long> countryIds, EmailStatisticsDto emailStatisticsDto, Long reportSaleId, String cachekey) {

        //开启手动事务
        TransactionStatus transaction = null;

        try {
            List<Long> staffFollowerIds = verifyStudentOfferItemUtils.getStaffFollowerIds(staffInfo.getStaffId());
            if (!emailStatisticsDto.getFkCompanyId().equals(2L)){
                throw new GetServiceException(LocaleMessageUtils.getMessage(locale,"email_statistics_null"));
            }
            Boolean isBd = studentOfferService.getIsBd(staffInfo.getStaffId());
            List<EmailStatisticsOfferItemVo> emailStatisticsOfferItemVos =studentOfferNoticeMapper.getEmailStatisticsOfferItem(emailStatisticsDto,staffFollowerIds,
                    countryIds,
                    staffInfo.getIsStudentOfferItemFinancialHiding(),
                    staffInfo.getIsStudentAdmin(),
                    isBd,
                    SecureUtil.getPermissionGroupInstitutionIds(),
                    SecureUtil.getStaffBoundBdIds());

            //开启手动事务
            transaction = dataSourceTransactionManager.getTransaction(transactionDefinition);
            if (GeneralTool.isEmpty(emailStatisticsOfferItemVos)){
                //改成update 更新status -2表示删除
                StudentOfferNotice studentOfferNotice = new StudentOfferNotice();
                studentOfferNotice.setStatus(-2);
                LambdaQueryWrapper<StudentOfferNotice> wrapper = Wrappers.lambdaQuery(StudentOfferNotice.class)
                        .eq(StudentOfferNotice::getGmtCreateUser, staffInfo.getLoginId())
                        .ne(StudentOfferNotice::getStatus, -2);
                if (GeneralTool.isNotEmpty(emailStatisticsDto.getStudentIds())){
                    wrapper.in(StudentOfferNotice::getFkStudentId, emailStatisticsDto.getStudentIds());
                }
                studentOfferNoticeService.update(studentOfferNotice,wrapper);
            }else {
                //拒收邮件的代理
                List<Agent> agents = agentMapper.selectList(Wrappers.<Agent>lambdaQuery().eq(Agent::getIsRejectEmail, 1));
                List<Long> rejectEmailAgentIds = new ArrayList<>();
                if (GeneralTool.isNotEmpty(agents)) {
                    rejectEmailAgentIds = agents.stream().map(Agent::getId).collect(Collectors.toList());
                }
                //查询项目成员
                List<Long> offerIds = emailStatisticsOfferItemVos.stream().map(EmailStatisticsOfferItemVo::getFkStudentOfferId).collect(Collectors.toList());
                if (GeneralTool.isEmpty(offerIds)){
                    throw new GetServiceException(LocaleMessageUtils.getMessage(locale,"search_result_null"));
                }
                List<StudentProjectRoleStaff> studentProjectRoleStaffs = studentProjectRoleStaffMapper.selectList(Wrappers.lambdaQuery(StudentProjectRoleStaff.class)
                        .eq(StudentProjectRoleStaff::getFkTableName, TableEnum.SALE_STUDENT_OFFER.key)
                        .in(StudentProjectRoleStaff::getFkTableId, offerIds)
                        .eq(StudentProjectRoleStaff::getIsActive, true)
                );

                Set<Long> staffIds = studentProjectRoleStaffs.stream().map(StudentProjectRoleStaff::getFkStaffId).filter(Objects::nonNull).collect(Collectors.toSet());
                staffIds.add(staffInfo.getStaffId());
                Map<Long, List<StudentProjectRoleStaff>> projectRoleStaffMap = studentProjectRoleStaffs.stream().collect(Collectors.groupingBy(StudentProjectRoleStaff::getFkTableId));

                //获取员工Map
                List<StaffVo> staffByIds = permissionCenterClient.getStaffByIds(staffIds);
                Map<Long, StaffVo> staffMap = staffByIds.stream().collect(Collectors.toMap(StaffVo::getId, Function.identity()));


                //所有学生ids 学生名称map
                List<Long> studentIds = emailStatisticsOfferItemVos.stream().map(EmailStatisticsOfferItemVo::getFkStudentId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                List<StudentVo> studentVos = studentMapper.getStudentZhEnNameByIds(Sets.newHashSet(studentIds));
                Map<Long, String> studentNameMap = studentVos.stream().collect(Collectors.toMap(StudentVo::getId, StudentVo::getFullName));


                //所有学校ids
                Set<Long> institutionIds = emailStatisticsOfferItemVos.stream().map(EmailStatisticsOfferItemVo::getFkInstitutionId).collect(Collectors.toSet());
                //学校名称map
                Map<Long, String> institutionNameMap = institutionCenterClient.getInstitutionNamesByIds(institutionIds).getData();

                //获取所有符合条件语言课的计划
                List<EmailStatisticsOfferItemVo> languageCourseItems = studentOfferNoticeMapper.getEmailStatisticsOfferItemIsLanguageCourse(emailStatisticsDto);
                //语言课的学生id
                List<Long> languageCourseStudentIds = languageCourseItems.stream().map(EmailStatisticsOfferItemVo::getFkStudentId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                //语言课的计划id
                List<Long> languageCourseOfferItemIds = languageCourseItems.stream().map(EmailStatisticsOfferItemVo::getId).filter(Objects::nonNull).distinct().collect(Collectors.toList());

                //没有语言的计划按学生id分组
                Map<Long, List<EmailStatisticsOfferItemVo>> mainCourseOfferItemsGroupByStuId = emailStatisticsOfferItemVos.stream().filter(e->!languageCourseStudentIds.contains(e.getFkStudentId()))
                        .collect(Collectors.groupingBy(EmailStatisticsOfferItemVo::getFkStudentId));

                //有读语言的计划按学生id分组
                Map<Long, List<EmailStatisticsOfferItemVo>> languageCourseOfferItemsGroupByStuId = emailStatisticsOfferItemVos.stream().filter(e->languageCourseStudentIds.contains(e.getFkStudentId()))
                        .collect(Collectors.groupingBy(EmailStatisticsOfferItemVo::getFkStudentId));
                //复制一个新的map
                Map<Long, List<EmailStatisticsOfferItemVo>> copyLanguageCourseOfferItemsGroupByStuIdMap  = Maps.newHashMap(languageCourseOfferItemsGroupByStuId);

                //有读语言的计划按学生id分组  计划进行过滤出全部主课程 用于比对最高申请状态
                Map<Long, List<EmailStatisticsOfferItemVo>> languageCourseOfferItemsOnlyMainMap = languageCourseOfferItemsGroupByStuId.entrySet().stream().peek(entry -> {
                    List<EmailStatisticsOfferItemVo> e = entry.getValue();
                    entry.setValue(e.stream().filter(item -> !languageCourseOfferItemIds.contains(item.getId())).collect(Collectors.toList()));
                }).filter(entry-> entry.getValue().size()>0).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

                //有读语言的计划按学生id分组  计划进行过滤出全部语言课程 用于比对最高申请状态
                Map<Long, List<EmailStatisticsOfferItemVo>> languageCourseOfferItemsOnlyLanguageMap = copyLanguageCourseOfferItemsGroupByStuIdMap.entrySet().stream().peek(entry -> {
                    List<EmailStatisticsOfferItemVo> e = entry.getValue();
                    entry.setValue(e.stream().filter(item -> languageCourseOfferItemIds.contains(item.getId())).collect(Collectors.toList()));
                }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

                //主课需要排除的步骤ids
                List<Long> mianCourseExcludeStepIds = Lists.newArrayList(1L,13L,14L,15L,9L);
                //步骤的状态高低映射
                List<StudentOfferItemStep> studentOfferItemSteps = studentOfferItemStepMapper.selectList(null);
                Map<Long, Integer> stepOrderMap = studentOfferItemSteps.stream().collect(Collectors.toMap(StudentOfferItemStep::getId, StudentOfferItemStep::getStepOrder));

                //模板映射
                List<StudentOfferNoticeTemplate> studentOfferNoticeTemplates = studentOfferNoticeTemplateMapper.selectList(null);
                if (GeneralTool.isEmpty(studentOfferNoticeTemplates)){
                    throw new GetServiceException(LocaleMessageUtils.getMessage(locale,"mailbox_template_is_empty"));
                }
                Map<String, StudentOfferNoticeTemplate> studentOfferNoticeTemplateMap = studentOfferNoticeTemplates.stream().collect(Collectors.toMap(StudentOfferNoticeTemplate::getTypeKey, Function.identity()));


                List<StudentOfferNotice> insertList = Lists.newArrayList();
                //匹配模板
                for (Long studentId : studentIds) {
                    //匹配主课
                    List<EmailStatisticsOfferItemVo> offerItemDtos = mainCourseOfferItemsGroupByStuId.get(studentId);
                    if (GeneralTool.isNotEmpty(offerItemDtos)){
                        List<EmailStatisticsOfferItemVo> afterFilteringItems = offerItemDtos.stream().filter(item -> !mianCourseExcludeStepIds.contains(item.getFkStudentOfferItemStepId())).collect(Collectors.toList());
                        if (GeneralTool.isEmpty(afterFilteringItems)){
                            continue;
                        }
                        List<EmailStatisticsOfferItemVo> itemDtos = getHightestStepOfferItemDtos(afterFilteringItems,stepOrderMap);
                        Long fkStudentOfferItemStepId = itemDtos.get(0).getFkStudentOfferItemStepId();
                        StudentOfferNotice studentOfferNotice = new StudentOfferNotice();
                        studentOfferNotice.setFkReportSaleId(reportSaleId);
                        studentOfferNotice.setFkStudentId(studentId);
                        studentOfferNotice.setFkAgentId(itemDtos.get(0).getFkAgentId());
                        if (GeneralTool.isNotEmpty(rejectEmailAgentIds) && rejectEmailAgentIds.contains(studentOfferNotice.getFkAgentId())) {
                            studentOfferNotice.setStatus(2);
                        } else {
                            studentOfferNotice.setStatus(0);
                        }
                        //学生姓名
                        String stuName = studentNameMap.get(studentId) == null ? "" : studentNameMap.get(studentId);
                        studentOfferNotice.setEmailSubject("学生"+stuName+"--英国院校入读意向确认");

                        //发件人邮箱
                        String fromEmail = getFromEmail(staffMap,projectRoleStaffMap,itemDtos.get(0),studentOfferNotice);
                        studentOfferNotice.setFromEmail(fromEmail);

                        //收件人邮箱 收件人邮箱多个 跟进顾问的邮箱
                        String toEmail = itemDtos.get(0).getAgentEmail();
        //                if (GeneralTool.isEmpty(toEmail)){
        //                    throw new GetServiceException(LocaleMessageUtils.getMessage("missing_required_configuration"));
        //                }
                        if (GeneralTool.isNotEmpty(toEmail)){
                            studentOfferNotice.setToEmail(toEmail.replace(" ",""));
                        }

                        //获取多个抄送邮箱
                        String ccEmail = getCcEmails(staffMap,projectRoleStaffMap,itemDtos.get(0));
                        studentOfferNotice.setCcEmail(ccEmail);

                        //创建人和创建时间
                        studentOfferNotice.setGmtCreate(new Date());
                        studentOfferNotice.setGmtCreateUser(staffInfo.getLoginId());


                        //步骤是已录取 Admitted/Offer  暂写死id
                        if (fkStudentOfferItemStepId.equals(4L)){
                            //如果最高状态是已录取 Admitted/Offer
                            getTemplateAndSetEmailContent(studentOfferNotice,itemDtos,studentOfferNoticeTemplateMap,ProjectKeyEnum.MAIN_COURSE_ADMITTED_NOTICE.key,institutionNameMap,stuName);
                            //添加近待插入列表
                            insertList.add(studentOfferNotice);
                        }

                        //步骤是已付押金 Deposit Paid  暂写死id
                        if (fkStudentOfferItemStepId.equals(5L)){
                            //区分本科和研究生课程
                            List<Long> fkInstitutionCourseMajorLevelIdList = Lists.newArrayList();
                            String oldCourseMajorLevelName = itemDtos.get(0).getOldCourseMajorLevelName();
                            String fkInstitutionCourseMajorLevelIds = itemDtos.get(0).getFkInstitutionCourseMajorLevelIds();
                            if (GeneralTool.isNotEmpty(fkInstitutionCourseMajorLevelIds)) {
                                fkInstitutionCourseMajorLevelIdList = Arrays.stream(fkInstitutionCourseMajorLevelIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
                            }
                            //本科课程
                            if ((GeneralTool.isNotEmpty(fkInstitutionCourseMajorLevelIdList)&&(fkInstitutionCourseMajorLevelIdList.contains(7L)||fkInstitutionCourseMajorLevelIdList.contains(8L)))
                                    ||(GeneralTool.isNotEmpty(oldCourseMajorLevelName)&&oldCourseMajorLevelName.contains("本科学位"))){
                                List<EmailStatisticsOfferItemVo> admittedAndPaidItemList = afterFilteringItems.stream().filter(item -> item.getFkStudentOfferItemStepId().equals(4L) || item.getFkStudentOfferItemStepId().equals(5L)).collect(Collectors.toList());
                                //用本科模板
                                getTemplateAndSetEmailContent(studentOfferNotice,admittedAndPaidItemList,studentOfferNoticeTemplateMap,ProjectKeyEnum.MAIN_COURSE_DEPOSIT_PAID_BACHELOR_NOTICE.key,institutionNameMap,stuName);
                                //添加近待插入列表
                                insertList.add(studentOfferNotice);
                            }
                            //研究生课程
                            else if ((GeneralTool.isNotEmpty(fkInstitutionCourseMajorLevelIdList)&&(fkInstitutionCourseMajorLevelIdList.contains(12L)||fkInstitutionCourseMajorLevelIdList.contains(13L)))
                                    ||(GeneralTool.isNotEmpty(oldCourseMajorLevelName)&&oldCourseMajorLevelName.contains("硕士学位"))){
                                //过滤出已付押金的
                                List<EmailStatisticsOfferItemVo> admittedAndPaidItemList = afterFilteringItems.stream().filter(item -> item.getFkStudentOfferItemStepId().equals(5L)).collect(Collectors.toList());
                                //用研究生课程模板
                                getTemplateAndSetEmailContent(studentOfferNotice,admittedAndPaidItemList,studentOfferNoticeTemplateMap,ProjectKeyEnum.MAIN_COURSE_DEPOSIT_PAID_PG_NOTICE.key,institutionNameMap,stuName);
                                //添加近待插入列表
                                insertList.add(studentOfferNotice);
                            }
                        }
                        //步骤是收到签证函 CAS  暂写死id
                        if (fkStudentOfferItemStepId.equals(6L)){
                            //如果最高状态是已录取 Admitted/Offer
                            getTemplateAndSetEmailContent(studentOfferNotice,itemDtos,studentOfferNoticeTemplateMap,ProjectKeyEnum.MAIN_COURSE_CAS_NOTICE.key,institutionNameMap,stuName);
                            //添加近待插入列表
                            insertList.add(studentOfferNotice);
                        }
                    } else {
                        //匹配语言课
                        List<EmailStatisticsOfferItemVo> languageOfferItemDtoList = languageCourseOfferItemsOnlyLanguageMap.get(studentId);
                        List<EmailStatisticsOfferItemVo> mainOfferItemDtoList = languageCourseOfferItemsOnlyMainMap.get(studentId);
                        List<EmailStatisticsOfferItemVo> mainOfferItemDtoForCasLanguageList = Lists.newArrayList();
                        if (GeneralTool.isNotEmpty(mainOfferItemDtoList)){
                            mainOfferItemDtoForCasLanguageList = Lists.newArrayList(mainOfferItemDtoList);
                            mainOfferItemDtoList = mainOfferItemDtoList.stream().filter(item -> !mianCourseExcludeStepIds.contains(item.getFkStudentOfferItemStepId())).collect(Collectors.toList());
                        }else {
                            mainOfferItemDtoList = Lists.newArrayList();
                        }
                        if (GeneralTool.isEmpty(languageOfferItemDtoList)){
                            continue;
                        }

                        StudentOfferNotice studentOfferNotice = new StudentOfferNotice();
                        studentOfferNotice.setFkReportSaleId(reportSaleId);
                        studentOfferNotice.setFkStudentId(studentId);
                        studentOfferNotice.setStatus(0);
                        //学生姓名
                        String stuName = studentNameMap.get(studentId) == null ? "" : studentNameMap.get(studentId);
                        studentOfferNotice.setEmailSubject("学生"+stuName+"--英国院校入读意向确认");
                        //创建人和创建时间
                        studentOfferNotice.setGmtCreate(new Date());
                        studentOfferNotice.setGmtCreateUser(staffInfo.getLoginId());


                        List<EmailStatisticsOfferItemVo> enrolledItems = mainOfferItemDtoList.stream().filter(item -> item.getFkStudentOfferItemStepId().equals(8L)).collect(Collectors.toList());
                        List<EmailStatisticsOfferItemVo> casTemplateItems = languageOfferItemDtoList.stream().filter(item -> item.getFkStudentOfferItemStepId().equals(6L) || item.getFkStudentOfferItemStepId().equals(8L)).collect(Collectors.toList());
                        if (GeneralTool.isNotEmpty(casTemplateItems)&&GeneralTool.isNotEmpty(mainOfferItemDtoList)&&GeneralTool.isEmpty(enrolledItems)){
                            //步骤是收到签证函 CAS或入学登记完成  暂写死id
                            studentOfferNotice.setFkAgentId(casTemplateItems.get(0).getFkAgentId());
                            if (GeneralTool.isNotEmpty(rejectEmailAgentIds) && rejectEmailAgentIds.contains(studentOfferNotice.getFkAgentId())) {
                                studentOfferNotice.setStatus(2);
                            }
                            //发件人邮箱
                            String fromEmail = getFromEmail(staffMap,projectRoleStaffMap,casTemplateItems.get(0),studentOfferNotice);
                            studentOfferNotice.setFromEmail(fromEmail);
                            //收件人邮箱 收件人邮箱多个 跟进顾问的邮箱
                            String toEmail = casTemplateItems.get(0).getAgentEmail();
                            if (GeneralTool.isEmpty(toEmail)){
                                throw new GetServiceException(LocaleMessageUtils.getMessage(locale,"missing_required_configuration"));
                            }
                            studentOfferNotice.setToEmail(toEmail.replace(" ",""));
                            //获取多个抄送邮箱
                            String ccEmail = getCcEmails(staffMap,projectRoleStaffMap,casTemplateItems.get(0));
                            studentOfferNotice.setCcEmail(ccEmail);
                            Date deferOpeningTime = languageOfferItemDtoList.get(0).getDeferOpeningTime();
                            if (deferOpeningTime.compareTo(new Date())<=0){
                                //用已开学模板
                                getTemplateAndSetEmailContent(studentOfferNotice,casTemplateItems,studentOfferNoticeTemplateMap,ProjectKeyEnum.LANGUAGE_COURSE_CAS_OPENED_NOTICE.key,null,stuName);
                                //添加近待插入列表
                                insertList.add(studentOfferNotice);
                                //匹配到就不需匹配下面的
                                continue;
                            }else if (deferOpeningTime.compareTo(new Date())>0){
                                //用已未开学模板
                                getTemplateAndSetEmailContent(studentOfferNotice,casTemplateItems,studentOfferNoticeTemplateMap,ProjectKeyEnum.LANGUAGE_COURSE_CAS_NOT_OPENED_NOTICE.key,institutionNameMap,stuName);
                                //添加近待插入列表
                                insertList.add(studentOfferNotice);
                                //匹配到就不需匹配下面的
                                continue;
                            }
                        }

                        mainOfferItemDtoForCasLanguageList = mainOfferItemDtoForCasLanguageList.stream().filter(item ->item.getFkStudentOfferItemStepId().equals(8L)).collect(Collectors.toList());
                        List<EmailStatisticsOfferItemVo> admittedTemplateItems = languageOfferItemDtoList.stream().filter(item -> item.getFkStudentOfferItemStepId().equals(4L)).collect(Collectors.toList());
                        if (GeneralTool.isNotEmpty(admittedTemplateItems)&&GeneralTool.isEmpty(mainOfferItemDtoForCasLanguageList)){
                            //步骤是已录取 Admitted/Offer  暂写死id
                            studentOfferNotice.setFkAgentId(admittedTemplateItems.get(0).getFkAgentId());
                            if (GeneralTool.isNotEmpty(rejectEmailAgentIds) && rejectEmailAgentIds.contains(studentOfferNotice.getFkAgentId())) {
                                studentOfferNotice.setStatus(2);
                            }
                            //发件人邮箱
                            String fromEmail = getFromEmail(staffMap,projectRoleStaffMap,admittedTemplateItems.get(0),studentOfferNotice);
                            studentOfferNotice.setFromEmail(fromEmail);
                            //收件人邮箱 收件人邮箱多个 跟进顾问的邮箱
                            String toEmail = admittedTemplateItems.get(0).getAgentEmail();
                            if (GeneralTool.isEmpty(toEmail)){
                                throw new GetServiceException(LocaleMessageUtils.getMessage(locale,"missing_required_configuration"));
                            }
                            studentOfferNotice.setToEmail(toEmail.replace(" ",""));
                            //获取多个抄送邮箱
                            String ccEmail = getCcEmails(staffMap,projectRoleStaffMap,admittedTemplateItems.get(0));
                            studentOfferNotice.setCcEmail(ccEmail);
                            Date deferOpeningTime = languageOfferItemDtoList.get(0).getDeferOpeningTime();
                            if (deferOpeningTime.compareTo(new Date())<=0){
                                //用已开学模板
                                getTemplateAndSetEmailContent(studentOfferNotice,admittedTemplateItems,studentOfferNoticeTemplateMap,ProjectKeyEnum.LANGUAGE_COURSE_ADMITTED_OPENED_NOTICE.key,null,stuName);
                                //添加近待插入列表
                                insertList.add(studentOfferNotice);
                            }else if (deferOpeningTime.compareTo(new Date())>0){
                                //用已未开学模板
                                getTemplateAndSetEmailContent(studentOfferNotice,admittedTemplateItems,studentOfferNoticeTemplateMap,ProjectKeyEnum.LANGUAGE_COURSE_ADMITTED_NOT_OPENED_NOTICE.key,institutionNameMap,stuName);
                                //添加近待插入列表
                                insertList.add(studentOfferNotice);
                            }
                        }


    //                    List<EmailStatisticsOfferItemVo> itemDtos = getHightestStepOfferItemDtos(languageOfferItemDtoList,stepOrderMap);
    //                    Long fkStudentOfferItemStepId = itemDtos.get(0).getFkStudentOfferItemStepId();
    //
    //                    StudentOfferNotice studentOfferNotice = new StudentOfferNotice();
    //                    studentOfferNotice.setFkReportSaleId(reportSaleId);
    //                    studentOfferNotice.setFkStudentId(studentId);
    //                    studentOfferNotice.setStatus(0);
    //                    studentOfferNotice.setFkAgentId(itemDtos.get(0).getFkAgentId());
    //                    //学生姓名
    //                    String stuName = studentNameMap.get(studentId) == null ? "" : studentNameMap.get(studentId);
    //                    studentOfferNotice.setEmailSubject("学生"+stuName+"--英国院校入读意向确认");
    //
    //                    //发件人邮箱
    //                    String fromEmail = getFromEmail(staffMap,projectRoleStaffMap,itemDtos.get(0),studentOfferNotice);
    //                    studentOfferNotice.setFromEmail(fromEmail);
    //
    //                    //收件人邮箱 收件人邮箱多个 跟进顾问的邮箱
    //                    String toEmail = itemDtos.get(0).getAgentEmail();
    //                    if (GeneralTool.isEmpty(toEmail)){
    //                        throw new GetServiceException(LocaleMessageUtils.getMessage(locale,"missing_required_configuration"));
    //                    }
    //                    studentOfferNotice.setToEmail(toEmail.replace(" ",""));
    //
    //                    //获取多个抄送邮箱
    //                    String ccEmail = getCcEmails(staffMap,projectRoleStaffMap,itemDtos.get(0));
    //                    studentOfferNotice.setCcEmail(ccEmail);
    //
    //                    //创建人和创建时间
    //                    studentOfferNotice.setGmtCreate(new Date());
    //                    studentOfferNotice.setGmtCreateUser(staffInfo.getLoginId());
    //
    //                    Date deferOpeningTime = languageOfferItemDtoList.get(0).getDeferOpeningTime();
    //
    //                    //步骤是收到签证函 CAS或入学登记完成  暂写死id
    //                    if (fkStudentOfferItemStepId.equals(6L)||fkStudentOfferItemStepId.equals(8L)){
    //                        if (deferOpeningTime.compareTo(new Date())<=0){
    //                            //用已开学模板
    //                            getTemplateAndSetEmailContent(studentOfferNotice,itemDtos,studentOfferNoticeTemplateMap,ProjectKeyEnum.LANGUAGE_COURSE_CAS_OPENED_NOTICE.key,null,stuName);
    //                            //添加近待插入列表
    //                            insertList.add(studentOfferNotice);
    //                        }else if (deferOpeningTime.compareTo(new Date())>0){
    //                            //用已未开学模板
    //                            getTemplateAndSetEmailContent(studentOfferNotice,itemDtos,studentOfferNoticeTemplateMap,ProjectKeyEnum.LANGUAGE_COURSE_CAS_NOT_OPENED_NOTICE.key,institutionNameMap,stuName);
    //                            //添加近待插入列表
    //                            insertList.add(studentOfferNotice);
    //                        }
    //                    }
    //
    //                    //步骤是已录取 Admitted/Offer  暂写死id
    //                    if (fkStudentOfferItemStepId.equals(4L)){
    //                        if (deferOpeningTime.compareTo(new Date())<=0){
    //                            //用已开学模板
    //                            getTemplateAndSetEmailContent(studentOfferNotice,itemDtos,studentOfferNoticeTemplateMap,ProjectKeyEnum.LANGUAGE_COURSE_ADMITTED_OPENED_NOTICE.key,null,stuName);
    //                            //添加近待插入列表
    //                            insertList.add(studentOfferNotice);
    //                        }else if (deferOpeningTime.compareTo(new Date())>0){
    //                            //用已未开学模板
    //                            getTemplateAndSetEmailContent(studentOfferNotice,itemDtos,studentOfferNoticeTemplateMap,ProjectKeyEnum.LANGUAGE_COURSE_ADMITTED_NOT_OPENED_NOTICE.key,institutionNameMap,stuName);
    //                            //添加近待插入列表
    //                            insertList.add(studentOfferNotice);
    //                        }
    //                    }

                    }
                }

    //            transaction = dataSourceTransactionManager.getTransaction(transactionDefinition);
                //改成update 更新status -2表示删除
                StudentOfferNotice studentOfferNotice = new StudentOfferNotice();
                studentOfferNotice.setStatus(-2);
                LambdaQueryWrapper<StudentOfferNotice> wrapper = Wrappers.lambdaQuery(StudentOfferNotice.class)
                        .eq(StudentOfferNotice::getGmtCreateUser, staffInfo.getLoginId())
                        .ne(StudentOfferNotice::getStatus, -2);
                if (GeneralTool.isNotEmpty(emailStatisticsDto.getStudentIds())){
                    wrapper.in(StudentOfferNotice::getFkStudentId, emailStatisticsDto.getStudentIds());
                }
                studentOfferNoticeService.update(studentOfferNotice,wrapper);
                if (GeneralTool.isNotEmpty(insertList)){
                    if (GeneralTool.isEmpty(staffInfo.getLoginId())){
                        throw new GetServiceException(LocaleMessageUtils.getMessage(locale,"insert_fail"));
                    }
                    int insertCount = insertList.size();
                    int count = 0;

                    while (insertCount>0){
                        List<StudentOfferNotice> subList;
                        if ((count + 1) * 1000>=insertList.size()){
                            subList = insertList.subList(count * 1000, insertList.size());
                        }else {
                            subList = insertList.subList(count * 1000, (count + 1) * 1000);
                        }
                        //批量插入 每次插入DEFAULT_BATCH_SIZE = 1000
                        boolean b = studentOfferNoticeService.saveBatch(subList, IService.DEFAULT_BATCH_SIZE);
                        if (!b){
                            throw new GetServiceException(LocaleMessageUtils.getMessage(locale,"insert_fail"));
                        }
                        insertCount -= IService.DEFAULT_BATCH_SIZE;
                        count++;
                    }
                }
            }
            dataSourceTransactionManager.commit(transaction);
            reportCenterClient.updateReportSaleStatus(reportSaleId,0);

        } catch (Exception e) {
            reportCenterClient.updateReportSaleStatus(reportSaleId,2);
            if (GeneralTool.isNotEmpty(transaction)){
                dataSourceTransactionManager.rollback(transaction);
            }
            e.printStackTrace();
            log.error("统计邮箱失败，emsg：{}",e.getMessage());
        }
        finally {
            //释放锁
            getRedis.del(cachekey);
        }

    }

    @Async
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAddKpiPlanTaskResult(List<KpiPlan> kpiPlanList) {
        if (GeneralTool.isEmpty(kpiPlanList)) {
            return;
        }
        Long fkKpiPlanId = kpiPlanList.get(0).getId();
        String key = CacheKeyConstants.KPI_PLAN_STATISTICS_KEY + fkKpiPlanId;
        // 获取当前KPI方案有进入统计权限key的人员列表
        List<Long> authorizedStaffIds = Lists.newArrayList();
        Result<List<Long>> result = permissionCenterClient.getAuthorizedStaffIdsByResourceKey("studentKpiPlanList.ViewReport");
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            authorizedStaffIds = result.getData();
        }

        List<KpiPlanTaskResult> kpiPlanTaskResultList = Lists.newArrayList();
        KpiPlanStatisticsDto kpiPlanStatisticsVo = new KpiPlanStatisticsDto();

        try {
            // 删除旧的统计结果
            Set<Long> kpiPlanIds = kpiPlanList.stream().map(KpiPlan::getId).collect(Collectors.toSet());
            kpiPlanTaskResultService.remove(Wrappers.<KpiPlanTaskResult>lambdaQuery().in(KpiPlanTaskResult::getFkKpiPlanId, kpiPlanIds));
            // 插入新的统计结果
            for (KpiPlan kpiPlan : kpiPlanList) {
                for (Long staffId : authorizedStaffIds) {
                    kpiPlanStatisticsVo.setFkKpiPlanId(kpiPlan.getId());
                    kpiPlanStatisticsVo.setRootFkStaffId(staffId);

                    KpiPlanTaskResult kpiPlanTaskResult = new KpiPlanTaskResult();
                    kpiPlanTaskResult.setFkKpiPlanId(kpiPlan.getId());
                    kpiPlanTaskResult.setFkStaffIdRoot(staffId);
                    kpiPlanTaskResult.setTaskStartTime(Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()));
                    KpiPlanStatisticsVo kpiPlanStatistics = kpiPlanService.getKpiPlanStatistics(kpiPlanStatisticsVo);
                    kpiPlanTaskResult.setTaskEndTime(Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()));
                    kpiPlanTaskResult.setKpiStatisticalResult(objectMapper.writeValueAsString(kpiPlanStatistics));

                    kpiPlanTaskResultList.add(kpiPlanTaskResult);
                }
            }
            kpiPlanTaskResultService.saveBatch(kpiPlanTaskResultList);
        } catch (Exception e) {
            getRedis.del(key);
            e.printStackTrace();
            log.error("插入KPI方案定时任务结果表过程中发生异常：{}", e.getMessage());
            throw new GetServiceException(LocaleMessageUtils.getMessage(null,"insert_fail"));
        } finally {
            getRedis.del(key);
        }
    }


    /**
     *
     * @param studentOfferNotice
     * @param itemDtos
     * @param studentOfferNoticeTemplateMap
     * @param typeKey
     * @param institutionNameMap
     * @param stuName
     */
    private void getTemplateAndSetEmailContent(StudentOfferNotice studentOfferNotice,
                                               List<EmailStatisticsOfferItemVo> itemDtos,
                                               Map<String, StudentOfferNoticeTemplate> studentOfferNoticeTemplateMap,
                                               String typeKey,
                                               Map<Long, String> institutionNameMap,
                                               String stuName){
        StudentOfferNoticeTemplate studentOfferNoticeTemplate = studentOfferNoticeTemplateMap.get(typeKey);
        if (GeneralTool.isEmpty(studentOfferNoticeTemplate)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("zh","mailbox_template_is_empty"));
        }

        studentOfferNotice.setFkStudentOfferNoticeTemplateId(studentOfferNoticeTemplate.getId());
        /*
         *  模板替换生成具体邮件内容
         */
        Map<String,String> paramMap = Maps.newHashMap();
        StringJoiner sj = new StringJoiner(",");
        Set<String> institutionNameSet = Sets.newHashSet();
        for (EmailStatisticsOfferItemVo itemDto : itemDtos) {
            if ((GeneralTool.isEmpty(institutionNameMap)||GeneralTool.isEmpty(institutionNameMap.get(itemDto.getFkInstitutionId())))&&GeneralTool.isEmpty(itemDto.getOldInstitutionName())){
                continue;
            }
            if (itemDto.getFkInstitutionId().equals(-1L)){
                if (GeneralTool.isNotEmpty(itemDto.getOldInstitutionName())&&institutionNameSet.contains(itemDto.getOldInstitutionName())){
                    continue;
                }
                sj.add(itemDto.getOldInstitutionName());
                institutionNameSet.add(itemDto.getOldInstitutionName());
            }else {
                if (GeneralTool.isNotEmpty(institutionNameMap)&&GeneralTool.isNotEmpty(institutionNameMap.get(itemDto.getFkInstitutionId()))){
                    if (GeneralTool.isNotEmpty(institutionNameMap.get(itemDto.getFkInstitutionId()))&&institutionNameSet.contains(institutionNameMap.get(itemDto.getFkInstitutionId()))){
                        continue;
                    }
                    sj.add(institutionNameMap.get(itemDto.getFkInstitutionId()));
                    institutionNameSet.add(institutionNameMap.get(itemDto.getFkInstitutionId()));
                }
            }
        }
        paramMap.put("studentName",stuName);
        paramMap.put("institutionName",sj.toString());
        String emailContent =  getEmailContent(paramMap,studentOfferNoticeTemplate);
        studentOfferNotice.setEmailContent(emailContent);
    }

    /**
     * 获取抄送邮箱
     * @param staffMap
     * @param projectRoleStaffMap
     * @return
     */
    private String getCcEmails(Map<Long, StaffVo> staffMap, Map<Long, List<StudentProjectRoleStaff>> projectRoleStaffMap, EmailStatisticsOfferItemVo emailStatisticsOfferItemVo) {
        //固定抄送邮箱
        String fixedCcEmail = "<EMAIL>";
        Properties props = System.getProperties();
        String profile = props.getProperty("spring.profiles.active");
        if (GeneralTool.isNotEmpty(profile)) {
            if (profile.equals(AppConstant.PROD_CODE) || profile.equals(AppConstant.IAE_CODE)|| profile.equals(AppConstant.TW_CODE)) {
                fixedCcEmail = "<EMAIL>";
            }else {
                fixedCcEmail = "<EMAIL>";
            }
        }
        StringJoiner ccEmails = new StringJoiner(";");
        Set<String> ccEmailSet = Sets.newHashSet(fixedCcEmail);
        ccEmails.add(fixedCcEmail);
        List<StudentProjectRoleStaff> studentProjectRoleStaffs = projectRoleStaffMap.get(emailStatisticsOfferItemVo.getFkStudentOfferId());
        if (GeneralTool.isEmpty(studentProjectRoleStaffs)){
            return ccEmails.toString();
        }
        //分别是为英国组文案和顾问的角色id 暂时写死
        List<StudentProjectRoleStaff> roleStaffs = studentProjectRoleStaffs.stream().filter(s -> s.getFkStudentProjectRoleId().equals(20L)||s.getFkStudentProjectRoleId().equals(24L)||s.getFkStudentProjectRoleId().equals(63L)).collect(Collectors.toList());
        if (GeneralTool.isEmpty(roleStaffs)){
            return ccEmails.toString();
        }
        List<Long> staffIds = roleStaffs.stream().map(StudentProjectRoleStaff::getFkStaffId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        for (Long staffId : staffIds) {
            StaffVo staffVo = staffMap.get(staffId);
            if (GeneralTool.isEmpty(staffVo)&&GeneralTool.isEmpty(staffVo.getEmail())){
                continue;
            }
            if (ccEmailSet.contains(staffVo.getEmail())){
                continue;
            }
            ccEmails.add(staffVo.getEmail());
            ccEmailSet.add(staffVo.getEmail());
        }
        return ccEmails.toString();
    }


    /**
     * 获取发送人邮件
     * @param staffMap
     * @param projectRoleStaffMap
     * @param emailStatisticsOfferItemVo
     * @return
     */
    private String getFromEmail(Map<Long, StaffVo> staffMap, Map<Long, List<StudentProjectRoleStaff>> projectRoleStaffMap, EmailStatisticsOfferItemVo emailStatisticsOfferItemVo, StudentOfferNotice studentOfferNotice) {
        List<StudentProjectRoleStaff> studentProjectRoleStaffs = projectRoleStaffMap.get(emailStatisticsOfferItemVo.getFkStudentOfferId());
        if (GeneralTool.isEmpty(studentProjectRoleStaffs)){
            return "";
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        //id 21为英国组跟进顾问的id 暂时写死
        List<StudentProjectRoleStaff> roleStaffs = studentProjectRoleStaffs.stream().filter(s -> s.getFkStudentProjectRoleId().equals(21L)).collect(Collectors.toList());
        if (GeneralTool.isEmpty(roleStaffs)){
            roleStaffs = studentProjectRoleStaffs.stream().filter(s -> s.getFkStudentProjectRoleId().equals(23L)).collect(Collectors.toList());
        }
        if (GeneralTool.isEmpty(roleStaffs)){
            roleStaffs = studentProjectRoleStaffs.stream().filter(s -> s.getFkStudentProjectRoleId().equals(24L)).collect(Collectors.toList());
        }
        if (GeneralTool.isEmpty(roleStaffs)){
            roleStaffs = studentProjectRoleStaffs.stream().filter(s -> s.getFkStudentProjectRoleId().equals(63L)).collect(Collectors.toList());
        }
        if (GeneralTool.isEmpty(roleStaffs)){
            roleStaffs = studentProjectRoleStaffs.stream().filter(s -> s.getFkStudentProjectRoleId().equals(22L)).collect(Collectors.toList());
        }
        if (GeneralTool.isEmpty(roleStaffs)){
            return "";
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        Long fkStaffId = roleStaffs.get(0).getFkStaffId();
        StaffVo staffVo = staffMap.get(fkStaffId);
        studentOfferNotice.setFkStaffIdFrom(fkStaffId);
        if (GeneralTool.isEmpty(staffVo.getEmail())){
            return "";
//            throw new GetServiceException(LocaleMessageUtils.getMessage("missing_required_configuration"));
        }
        return staffVo.getEmail();
    }


    /**
     * 获取完整邮件内容
     * @param paramMap
     * @param studentOfferNoticeTemplate
     * @return
     */
    private String getEmailContent(Map<String,String> paramMap, StudentOfferNoticeTemplate studentOfferNoticeTemplate) {
        String emailTemplate = studentOfferNoticeTemplate.getEmailTemplate();
        if (GeneralTool.isEmpty(emailTemplate)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("zh","mailbox_template_is_empty"));
        }
        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            emailTemplate = emailTemplate.replace("#{"+entry.getKey()+"}",entry.getValue());
        }
        return emailTemplate;
    }


    /**
     * 获取最高申请步骤的计划
     * @param offerItemDtos
     * @param stepOrderMap
     * @return
     */
    private List<EmailStatisticsOfferItemVo> getHightestStepOfferItemDtos(List<EmailStatisticsOfferItemVo> offerItemDtos, Map<Long, Integer> stepOrderMap) {
        int maxOrder = -1;
        for (EmailStatisticsOfferItemVo offerItemDto : offerItemDtos) {
            Integer order = stepOrderMap.get(offerItemDto.getFkStudentOfferItemStepId());
            offerItemDto.setStepOrder(order);
            maxOrder = Math.max(maxOrder,order);
        }
        Integer finalMaxOrder = maxOrder;
        return offerItemDtos.stream().filter(o->o.getStepOrder().equals(finalMaxOrder)).collect(Collectors.toList());
    }


}
