package com.get.insurancecenter.vo.commission;

import com.get.institutioncenter.entity.AreaCity;
import com.get.institutioncenter.entity.AreaCountry;
import com.get.institutioncenter.entity.AreaState;
import com.get.permissioncenter.vo.CompanyVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author:Oliver
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:代理详情相关MAP
 */
@Data
public class AgentInfoMap {

    @ApiModelProperty(value = "代理公司Map")
    private Map<Long, List<Long>> agentCompanyMap;

    @ApiModelProperty(value = "国家Map")
    private Map<Long, AreaCountry> countryMap;

    @ApiModelProperty(value = "州省Map")
    private Map<Long, AreaState> areaStateMap;

    @ApiModelProperty(value = "城市Map")
    private Map<Long, AreaCity> areaCityMap;

    @ApiModelProperty(value = "分公司Map")
    private Map<Long, CompanyVo> companyMap;

    @ApiModelProperty(value = "代理所属分公司名称Map")
    private Map<Long, List<String>> agentCompanyNameMap;

    @ApiModelProperty(value = "代理所属分公司numMap")
    private Map<Long, List<String>> agentCompanyNumMap;

    public AgentInfoMap() {
        agentCompanyMap = new HashMap<>();
        countryMap = new HashMap<>();
        areaStateMap = new HashMap<>();
        areaCityMap = new HashMap<>();
        agentCompanyNumMap = new HashMap<>();
        agentCompanyNameMap = new HashMap<>();
        companyMap = new HashMap<>();
    }

}
