package com.get.insurancecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:用户客户
 */
@Data
@TableName("m_partner_user_client")
public class PartnerUserClient extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "租户Id")
    private Long fkTenantId;

    @ApiModelProperty(value = "伙伴用户Id")
    private Long fkPartnerUserId;

    @ApiModelProperty(value = "客户类型（枚举：1学生）")
    private Integer clientType;

    @ApiModelProperty(value = "客户名称")
    private String clientName;

    @ApiModelProperty(value = "前往国家Id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "前往州省Id")
    private Long fkAreaStateId;

    @ApiModelProperty(value = "保单开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insuranceStartTime;

    @ApiModelProperty(value = "保单结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insuranceEndTime;

    @ApiModelProperty(value = "Email")
    private String email;

    @ApiModelProperty(value = "手机区号")
    private String mobileAreaCode;

    @ApiModelProperty(value = "移动电话")
    private String mobile;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "微信openid")
    private String wechatOpenid;
}
