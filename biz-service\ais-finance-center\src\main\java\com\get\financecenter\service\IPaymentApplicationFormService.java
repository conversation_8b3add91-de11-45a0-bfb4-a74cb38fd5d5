package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.financecenter.dto.MediaAndAttachedDto;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import com.get.financecenter.vo.PaymentApplicationFormVo;
import com.get.financecenter.entity.PaymentApplicationForm;
import com.get.financecenter.dto.PaymentApplicationFormDto;
import com.get.financecenter.dto.query.PaymentApplicationFormQueryDto;

import java.math.BigDecimal;
import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/3/30 11:47
 */
public interface IPaymentApplicationFormService extends BaseService<PaymentApplicationForm> {


    Long save(PaymentApplicationFormDto mpay);

    Boolean updateMpay(PaymentApplicationForm payForm);

    PaymentApplicationFormVo getMpayById(Long id);

    /**
     * @ Description :附件上传
     * @ Param [mediaAttachedVo]
     * @ return java.util.List<com.get.financecenter.vo.MediaAndAttachedDto>
     * @ author LEO
     */
    List<FMediaAndAttachedVo> addInstitutionMedia(List<MediaAndAttachedDto> mediaAttachedVo);


    List<PaymentApplicationFormVo> getMpayList(PaymentApplicationFormQueryDto paymentApplicationFormVo, Page page);

    PaymentApplicationFormVo getMpayDetailData(Long businessKey);


    void updataMpayData(PaymentApplicationFormDto paymentApplicationFormDto);

    List<FMediaAndAttachedVo> getPayFileData(MediaAndAttachedDto mediaAndAttachedDto, Page page);

    Boolean startPayFlow(String businessKey, String procdefKey, String companyId);


    void updateById(Long id);

    void getUserSubmit(String taskId, String status);

    void getRevokePaymentApplicationFrom(Long id, String summary);

    /**
     * 根据id获取支付申请单
     * @param targetId
     * @return
     */
    PaymentApplicationForm getPaymentApplicationFormById(Long targetId);

    /**
     * 更新支付申请单状态
     * @param paymentApplicationForm
     * @return
     */
    Boolean updatePaymentApplicationFormStatus(PaymentApplicationForm paymentApplicationForm);

    /**
     * 获取支付申请单总金额
     * @param id
     * @return
     */
    BigDecimal getPaymentApplicationFormTotalAmount(Long id);
}
