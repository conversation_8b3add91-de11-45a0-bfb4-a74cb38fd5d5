<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionScholarshipMapper2">

    <select id="getWcInstitutionScholarshipList"
            resultType="com.get.institutioncenter.vo.InstitutionScholarshipVo2">
        select mis.id,mis.fk_institution_id,GROUP_CONCAT(DISTINCT fk_major_level_ids)as
        fk_major_level_ids,GROUP_CONCAT(mis.public_level)as public_level from
        m_institution_scholarship2 mis
        left join m_institution mi on mi.id = mis.fk_institution_id
        left join r_institution_view_order vo on vo.fk_institution_id=mis.fk_institution_id
        where mis.is_active=1 and mi.fk_area_country_id=#{fkCountryId} and vo.type=0
        <if test="schoolName !='' and schoolName !=null">
            and(
            mi.name like concat('%',#{schoolName},'%')
            or mi.name_chn like concat('%',#{schoolName},'%')
            or mi.short_name like concat('%',#{schoolName},'%')
            or mi.short_name_chn like concat('%',#{schoolName},'%')
            or CONCAT(mi.name,'（',mi.name_chn,'）')like concat('%',#{schoolName},'%'))
        </if>
        group by mis.fk_institution_id order by vo.view_order asc
    </select>
    <select id="getIsOtherModule" resultType="com.get.institutioncenter.vo.InstitutionScholarshipVo2">
   select case when appFee.ys is not null then 1 else 0 end as isAppFee,
  case when deadInfo.ys is not null then 1 else 0 end as isDeadInfo,
  case when scholarship.ys is not null then 1 else 0 end as isScholarship
  from (select max(id)as ys from m_institution_app_fee where fk_institution_id=#{fkInstitutionId})appFee,
  (select max(id)as ys from m_institution_deadline_info where fk_institution_id=#{fkInstitutionId})deadInfo,
  (select max(id)as ys from m_institution_scholarship2 where fk_institution_id=#{fkInstitutionId})scholarship
    </select>
    <select id="datas" resultType="com.get.institutioncenter.vo.InstitutionScholarshipVo2">
        SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
        FROM
            m_institution_scholarship2 s
        INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
        <if test="data.keyword!=null and data.keyword!=''">
            <if test="data.fkTableName=='u_area_country'">
                INNER u_area_country f ON f.id = a.fk_table_id_type AND (f.name LIKE CONCAT('%',#{data.keyword},'%') OR f.name_chn LIKE CONCAT('%',#{data.keyword},'%'))
            </if>
            <if test="data.fkTableName=='m_institution'">
                INNER m_institution f ON f.id = a.fk_table_id_type AND (f.name LIKE CONCAT('%',#{data.keyword},'%') OR f.name_chn LIKE CONCAT('%',#{data.keyword},'%'))
                <if test="data.fkCountryId!=null and data.fkCountryId!=''">
                   AND f.fk_area_country_id = #{data.fkCountryId}
                </if>
            </if>
            <if test="data.fkTableName=='m_institution_faculty'">
                INNER m_institution_faculty f ON f.id = a.fk_table_id_type AND (f.name LIKE CONCAT('%',#{data.keyword},'%') OR f.name_chn LIKE CONCAT('%',#{data.keyword},'%'))
            </if>
            <if test="data.fkTableName=='u_course_type_group'">
                INNER u_course_type_group f ON f.id = a.fk_table_id_type AND (f.type_group_name LIKE CONCAT('%',#{data.keyword},'%') OR f.type_group_name_chn LIKE CONCAT('%',#{data.keyword},'%'))
            </if>
            <if test="data.fkTableName=='u_course_type'">
                INNER u_course_type f ON f.id = a.fk_table_id_type AND (f.type_name LIKE CONCAT('%',#{data.keyword},'%') OR f.type_name_chn LIKE CONCAT('%',#{data.keyword},'%'))
            </if>
            <if test="data.fkTableName=='u_major_level'">
                INNER u_major_level f ON f.id = a.fk_table_id_type AND (f.level_name LIKE CONCAT('%',#{data.keyword},'%') OR f.level_name_chn LIKE CONCAT('%',#{data.keyword},'%'))
            </if>
            <if test="data.fkTableName=='m_institution_course'">
                INNER m_institution_course f ON f.id = a.fk_table_id_type AND (f.name LIKE CONCAT('%',#{data.keyword},'%') OR f.name_chn LIKE CONCAT('%',#{data.keyword},'%'))
            </if>
        </if>
        WHERE
            a.fk_table_name = 'm_institution_scholarship2'
            <if test="data.fkTableName!=null and data.fkTableName!=''">
                AND a.fk_table_name_type = #{data.fkTableName}
            </if>
            <if test="data.fkTableId!=null and data.fkTableId!=''">
                AND a.fk_table_id_type = #{data.fkTableId}
            </if>
            <if test="data.isActive!=null">
                AND s.is_active = #{data.isActive}
            </if>
            GROUP BY s.id
            ORDER BY s.is_active,s.gmt_create DESC
    </select>
    <select id="wechatDatas" resultType="com.get.institutioncenter.vo.WeInstitutionScholarshipVo">
        SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
        FROM
            m_institution_scholarship2 s
        INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
        WHERE
            a.fk_table_name = 'm_institution_scholarship2'
            <if test="fkTypeKey!=null and fkTypeKey!=''">
                AND a.fk_table_name_type = #{fkTypeKey}
            </if>
            <if test="fkTableId!=null and fkTableId!=''">
                AND a.fk_table_id_type = #{fkTableId}
            </if>
            ORDER BY s.id
    </select>
    <select id="selectInfoById" resultType="com.get.institutioncenter.vo.InstitutionScholarshipVo2">
        SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
        FROM
            m_institution_scholarship2 s
        INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
        WHERE
            a.fk_table_name = 'm_institution_scholarship2'
           AND s.id = #{id}
        GROUP BY s.id
    </select>
    <select id="getWcInstitutionScholarshipDatas"
            resultType="com.get.institutioncenter.vo.InstitutionScholarshipVo2">
        SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
        FROM
            m_institution_scholarship2 s
        INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
        WHERE
            a.fk_table_name =  #{fkTableName}
            AND a.fk_table_name_type = 'm_institution'
            AND a.fk_table_id_type =#{weScholarshipAppDto.institutionId}
        <if test="weScholarshipAppDto.countryId!=null and weScholarshipAppDto.countryId!=''">
            UNION ALL
            SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
            FROM
            m_institution_scholarship2 s
            INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
            WHERE
            a.fk_table_name =  #{fkTableName}
            AND a.fk_table_name_type = 'u_area_country'
            AND a.fk_table_id_type =#{weScholarshipAppDto.countryId}
        </if>
        <if test="weScholarshipAppDto.facultyId!=null and weScholarshipAppDto.facultyId!=''">
            UNION ALL
            SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
            FROM
            m_institution_scholarship2 s
            INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
            WHERE
            a.fk_table_name =  #{fkTableName}
            AND a.fk_table_name_type = 'm_institution_faculty'
            AND a.fk_table_id_type =#{weScholarshipAppDto.facultyId}
        </if>
        <if test="weScholarshipAppDto.courseGroupTypeId!=null and weScholarshipAppDto.courseGroupTypeId!=''">
            UNION ALL
            SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
            FROM
            m_institution_scholarship2 s
            INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
            WHERE
            a.fk_table_name =  #{fkTableName}
            AND a.fk_table_name_type = 'u_course_type_group'
            AND a.fk_table_id_type =#{weScholarshipAppDto.courseGroupTypeId}
        </if>
        <if test="weScholarshipAppDto.courseTypeId!=null and weScholarshipAppDto.courseTypeId!=''">
            UNION ALL
            SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
            FROM
            m_institution_scholarship2 s
            INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
            WHERE
            a.fk_table_name =  #{fkTableName}
            AND a.fk_table_name_type = 'u_course_type'
            AND a.fk_table_id_type =#{weScholarshipAppDto.courseTypeId}
        </if>
        <if test="weScholarshipAppDto.courseLevelId!=null and weScholarshipAppDto.courseLevelId!=''">
            UNION ALL
            SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
            FROM
            m_institution_scholarship2 s
            INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
            WHERE
            a.fk_table_name =  #{fkTableName}
            AND a.fk_table_name_type = 'u_major_level'
            AND a.fk_table_id_type =#{weScholarshipAppDto.courseLevelId}
        </if>
        <if test="weScholarshipAppDto.courseId!=null and weScholarshipAppDto.courseId!=''">
            UNION ALL
            SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
            FROM
            m_institution_scholarship2 s
            INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
            WHERE
            a.fk_table_name = #{fkTableName}
            AND a.fk_table_name_type = 'm_institution_course'
            AND a.fk_table_id_type =#{weScholarshipAppDto.courseId}
        </if>
    </select>
    <select id="priorityMatchingQuery" resultType="com.get.institutioncenter.vo.InstitutionScholarshipVo2">

        SELECT * FROM
        (
        SELECT
        x.*,
        GROUP_CONCAT(
        DISTINCT x.fkTableName
        ORDER BY
        x.fkTableName
        ) AS fk,
        GROUP_CONCAT(
        DISTINCT a.fk_table_name_type
        ORDER BY
        a.fk_table_name_type
        ) AS fc,
        COUNT(DISTINCT x.fkTableName) as priority
        FROM
        (
            <!-- 匹配国家-->
            <if test="weScholarshipAppDto.countryId!=null and weScholarshipAppDto.countryId!=''">

                SELECT
                s.*, a.fk_table_name_type AS fkTableName,
                a.fk_table_id_type AS fkTableId
                FROM
                m_institution_scholarship2 s
                INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
                WHERE
                a.fk_table_name =  #{fkTableName}
                AND a.fk_table_name_type = 'u_area_country'
                AND a.fk_table_id_type =#{weScholarshipAppDto.countryId}
            </if>
        <!-- 匹配学校-->
            <if test="weScholarshipAppDto.institutionId!=null and weScholarshipAppDto.institutionId!=''">
                UNION ALL
                SELECT
                s.*, a.
                fk_table_name_type AS fkTableName,
                a.fk_table_id_type AS fkTableId
                FROM
                m_institution_scholarship2 s
                INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
                WHERE
                a.fk_table_name = #{fkTableName}
                AND a.fk_table_name_type = 'm_institution'
                AND a.fk_table_id_type =#{weScholarshipAppDto.institutionId}
            </if>
            <if test="weScholarshipAppDto.facultyId!=null and weScholarshipAppDto.facultyId!=''">
                UNION ALL
                SELECT
                s.*, a.fk_table_name_type AS fkTableName,
                a.fk_table_id_type AS fkTableId
                FROM
                m_institution_scholarship2 s
                INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
                WHERE
                a.fk_table_name =  #{fkTableName}
                AND a.fk_table_name_type = 'm_institution_faculty'
                AND a.fk_table_id_type =#{weScholarshipAppDto.facultyId}
            </if>
            <if test="weScholarshipAppDto.courseGroupTypeId!=null and weScholarshipAppDto.courseGroupTypeId!=''">
                UNION ALL
                SELECT
                s.*, a.fk_table_name_type AS fkTableName,
                a.fk_table_id_type AS fkTableId
                FROM
                m_institution_scholarship2 s
                INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
                WHERE
                a.fk_table_name =  #{fkTableName}
                AND a.fk_table_name_type = 'u_course_type_group'
                AND a.fk_table_id_type =#{weScholarshipAppDto.courseGroupTypeId}
            </if>
            <if test="weScholarshipAppDto.courseTypeId!=null and weScholarshipAppDto.courseTypeId!=''">
                UNION ALL
                SELECT
                s.*, a.fk_table_name_type AS fkTableName,
                a.fk_table_id_type AS fkTableId
                FROM
                m_institution_scholarship2 s
                INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
                WHERE
                a.fk_table_name =  #{fkTableName}
                AND a.fk_table_name_type = 'u_course_type'
                AND a.fk_table_id_type =#{weScholarshipAppDto.courseTypeId}
            </if>
            <if test="weScholarshipAppDto.courseLevelId!=null and weScholarshipAppDto.courseLevelId!=''">
                UNION ALL
                SELECT
                s.*, a.fk_table_name_type AS fkTableName,
                a.fk_table_id_type AS fkTableId
                FROM
                m_institution_scholarship2 s
                INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
                WHERE
                a.fk_table_name =  #{fkTableName}
                AND a.fk_table_name_type = 'u_major_level'
                AND a.fk_table_id_type =#{weScholarshipAppDto.courseLevelId}
            </if>
        <!-- 匹配课程-->
            <if test="weScholarshipAppDto.courseId!=null and weScholarshipAppDto.courseId!=''">
                UNION ALL
                SELECT
                s.*, a.fk_table_name_type AS fkTableName,
                a.fk_table_id_type AS fkTableId
                FROM
                m_institution_scholarship2 s
                INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
                WHERE
                a.fk_table_name = #{fkTableName}
                AND a.fk_table_name_type = 'm_institution_course'
                AND a.fk_table_id_type =#{weScholarshipAppDto.courseId}
            </if>
        ) x
        INNER JOIN r_institution_course_app_info a ON x.id = a.fk_table_id
        GROUP BY
        x.id
        ) f
        WHERE
        <foreach collection="priorityTypeKey.entrySet()" separator="OR" open="(" close=")" item="val">
            f.fk = #{val}
        </foreach>
        ORDER BY f.priority desc
    </select>
</mapper>