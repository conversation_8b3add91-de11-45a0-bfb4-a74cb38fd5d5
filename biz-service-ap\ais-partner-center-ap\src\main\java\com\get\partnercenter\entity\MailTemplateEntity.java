package com.get.partnercenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date: 2024-12-20 14:10:35
 */

@Data
@TableName("u_mail_template")
public class MailTemplateEntity extends BaseEntity {

    @ApiModelProperty("类型Key，必填")
    private String typeKey;

    @ApiModelProperty("标题，可应用到邮件标题")
    private String title;

    @ApiModelProperty("电邮模板")
    private String emailTemplate;

    @ApiModelProperty("电邮模板（英语版）")
    private String emailTemplateEn;

    @ApiModelProperty("备注")
    private String remark;

}
