package com.get.salecenter.dto.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.salecenter.dto.AgentAnnualSummaryDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Data
public class AgentQueryDto {


 /**
  * 国家Id
  */
 @ApiModelProperty(value = "国家Id")
 @NotNull(message = "国家Id")
 private Long fkAreaCountryId;
 /**
  * 州省Id
  */
 @ApiModelProperty(value = "州省Id")
 @NotNull(message = "州省Id")
 private Long fkAreaStateId;
 /**
  * 城市Id
  */
 @ApiModelProperty(value = "城市Id")
 private Long fkAreaCityId;

 /**
  * BD绑定的大区ID
  */
 @ApiModelProperty("BD绑定的大区ID")
 private Long fkAreaRegionId;

 /**
  * 公司Id
  */
 @ApiModelProperty(value = "公司Id")
 private Long fkCompanyId;


 /**
  * 是否激活：0否/1是
  */
 @ApiModelProperty(value = "是否激活：0否/1是")
 private Boolean isActive;

 /**
  * BD编号
  */
 @ApiModelProperty(value = "BD编号")
 private String bdCode;
 /**
  * 关键字
  */
 @ApiModelProperty(value = "关键字")
 private String keyWord;

 /**
  * 是否关键代理：0否/1是
  */
 @NotNull(message = "请选择是否关键代理")
 @ApiModelProperty(value = "是否关键代理：0否/1是")
 private Boolean isKeyAgent;
 /**
  * 性质：公司/个人/工作室/国际学校/其他
  */
 @ApiModelProperty(value = "性质：公司/个人/工作室/国际学校/其他")
 private String nature;

 @ApiModelProperty("代理查询开始时间")
 @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
 private Date createBeginTime;

 @ApiModelProperty("代理查询结束时间")
 @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
 private Date createEndTime;

 @ApiModelProperty("是否无合同附件代理:false否/true是")
 private Boolean isHasContractAttachment;

 @ApiModelProperty("代理是否存在备注 true：有 false:没有")
 private Boolean existsRemark;

 @ApiModelProperty(value = "是否渠道代理：false否/true是")
 private Boolean isCustomerChannel;

 @ApiModelProperty("代理id集合，用于KPI统计中代理数的跳转")
 private Set<Long> agentIds;

 //========================================================================
 @ApiModelProperty("代理统计跳转")
 private AgentAnnualSummaryDto agentAnnualSummaryVo;

 @ApiModelProperty(value = "是否导出")
 private Boolean isExport;

 /**
  * 性质列表
  */
 @ApiModelProperty(value = "性质列表")
 private List<String> natureList;

 /**
  * BD名称
  */
 @ApiModelProperty(value = "BD名称")
 private String bdName;

 /**
  * 学生代理父Id
  */
 @ApiModelProperty(value = "学生代理父Id")
 private Long fkParentAgentId;

 /**
  * 性质备注
  */
 @ApiModelProperty(value = "性质备注")
 private String natureNote;

 @ApiModelProperty(value = "标签类型id")
 private Long labelTypeId;

 @ApiModelProperty(value = "标签描述")
 private String labelRemark;

 @ApiModelProperty(value = "邮箱")
 private String labelEmail;

 /**
  * 生效合同开始时间
  */
 @ApiModelProperty("生效合同开始时间（查询在此时间段内生效的合同）")
 @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
 private Date contractEffectiveTimeBegin;

 /**
  * 生效合同结束时间
  */
 @ApiModelProperty("生效合同结束时间（查询在此时间段内生效的合同）")
 @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
 private Date contractEffectiveTimeEnd;

 /**
  * 合同状态
  */
 @ApiModelProperty("合同状态：0无合同/1有合同/2未签署/3待审核/4审核通过/-4审核驳回/5续约中/6生效中/7已过期")
 private Integer contractApprovalStatus;

}