package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.AgentContractFormulaCourseType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/1/6 12:21
 * @verison: 1.0
 * @description:
 */
@Mapper
public interface AgentContractFormulaCourseTypeMapper extends BaseMapper<AgentContractFormulaCourseType> {

    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(AgentContractFormulaCourseType record);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :查找agentContractFormulaId对应课程类型ids
     * @Param [agentContractFormulaId]
     * <AUTHOR>
     */
    List<Long> getCourseTypeIdsByFkid(@Param("agentContractFormulaId") Long agentContractFormulaId);
}