package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @author: Hardy
 * @create: 2023/10/10 11:06
 * @verison: 1.0
 * @description:
 */
@Data
public class BalancingPaymentFormDto {

    @NotNull(message = "应付计划id")
    @ApiModelProperty(value = "应付计划id",required = true)
    private Long id;

    @NotBlank(message = "币种")
    @ApiModelProperty(value = "币种",required = true)
    private String fkCurrencyTypeNum;

    @NotNull(message = "应付未付")
    @ApiModelProperty(value = "应付未付",required = true)
    private BigDecimal diffPayableAmount;

    @NotBlank(message = "应付类型关键字")
    @ApiModelProperty(value = "目标类型关键字，枚举：m_agent代理",required = true)
    private String fkTypeKey;

    @NotNull(message = "应付类型Id")
    @ApiModelProperty(value = "应付类型Id",required = true)
    private Long fkTypeTargetId;

//    @NotNull(message = "流水号")
//    @ApiModelProperty(value = "流水号",required = true)
//    private String paymentSerialNumber;
//
//    @ApiModelProperty(value = "摘要",required = true)
//    private String summary;



}
