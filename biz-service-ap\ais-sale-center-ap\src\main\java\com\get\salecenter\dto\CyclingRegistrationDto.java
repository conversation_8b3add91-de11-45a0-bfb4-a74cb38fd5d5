package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/10/14 11:23
 */
@Data
public class CyclingRegistrationDto  extends BaseVoEntity{
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    @Column(name = "fk_convention_id")
    @NotNull(message = "峰会Id不能为空")
    private Long fkConventionId;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @Column(name = "name")
    private String name;

    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男")
    @Column(name = "gender")
    private Integer gender;

    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    @Column(name = "mobile")
    private String mobile;

    /**
     * 身份类型：0身份证/1护照
     */
    @ApiModelProperty(value = "身份类型：0身份证/1护照")
    @Column(name = "id_type")
    private Integer idType;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
    @Column(name = "id_num")
    private String idNum;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    @Column(name = "company")
    private String company;

    /**
     * 支付金额
     */
    @ApiModelProperty(value = "支付金额")
    @Column(name = "pay_amount")
    private BigDecimal payAmount;

    /**
     * 支付流水号
     */
    @ApiModelProperty(value = "支付流水号")
    @Column(name = "pay_order_num")
    private String payOrderNum;

    /**
     * 支付状态：0失败/1成功
     */
    @ApiModelProperty(value = "支付状态：0失败/1成功")
    @Column(name = "pay_status")
    private Integer payStatus;

    /**
     * 参展人员类型（枚举定义）：0校方/1代理/2嘉宾/3员工/4工作人员
     */
    @ApiModelProperty(value = "参展人员类型（枚举定义）：0校方/1代理/2嘉宾/3员工/4工作人员")
    private Integer type;

    /**
     * bd名称关键字
     */
    @ApiModelProperty(value = "bd名称关键字")
    private String bdNameKey;
  
}
