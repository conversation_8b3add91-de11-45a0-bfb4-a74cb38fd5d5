package com.get.common.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Collection;

/**
 * <AUTHOR>
 * @DATE: 2023/3/20
 * @TIME: 18:05
 * @Description:
 **/
@Data
public class InstitutionStatisticsResponseBo<T> extends ListResponseBo<T>{

    @ApiModelProperty(value = "申请数小计")
    private BigDecimal applicationSubtotal;

    @ApiModelProperty(value = "提交数小计")
    private BigDecimal mainCourseSubtotal;

    @ApiModelProperty(value = "学生数小计")
    private BigDecimal studentSubtotal;

    @ApiModelProperty(value = "成功入学数(学生数)小计")
    private BigDecimal successsStudentSubtotal;

    @ApiModelProperty(value = "小计转化率")
    private String subtotalConversionRate;


    public InstitutionStatisticsResponseBo(Collection<T> list, Page page){
        super(list,page);
    }

    public InstitutionStatisticsResponseBo(Collection<T> list, Page page ,BigDecimal applicationSubtotal,BigDecimal mainCourseSubtotal,BigDecimal studentSubtotal,BigDecimal successsStudentSubtotal,String subtotalConversionRate) {
        super(list,page);
        this.applicationSubtotal = applicationSubtotal;
        this.mainCourseSubtotal = mainCourseSubtotal;
        this.studentSubtotal = studentSubtotal;
        this.successsStudentSubtotal = successsStudentSubtotal;
        this.subtotalConversionRate = subtotalConversionRate;
    }


}