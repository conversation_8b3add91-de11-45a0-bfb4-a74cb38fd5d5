package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_convention_registration_area_country")
public class ConventionRegistrationAreaCountry extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 峰会报名Id
     */
    @ApiModelProperty(value = "峰会报名Id")
    @Column(name = "fk_convention_registration_id")
    private Long fkConventionRegistrationId;
    /**
     * 国家Key(编号)
     */
    @ApiModelProperty(value = "国家Key(编号)")
    @Column(name = "fk_area_country_key")
    private String fkAreaCountryKey;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fkConventionRegistrationId=").append(fkConventionRegistrationId);
        sb.append(", fkAreaCountryKey=").append(fkAreaCountryKey);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}