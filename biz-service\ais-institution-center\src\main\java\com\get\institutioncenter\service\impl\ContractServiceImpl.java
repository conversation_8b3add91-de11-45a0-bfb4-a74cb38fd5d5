package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.*;
import com.get.institutioncenter.dto.ContractCompanyDto;
import com.get.institutioncenter.dto.ContractDto;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.entity.InstitutionProvider;
import com.get.institutioncenter.vo.*;
import com.get.institutioncenter.entity.Comment;
import com.get.institutioncenter.entity.Contract;
import com.get.institutioncenter.entity.MediaAndAttached;
import com.get.institutioncenter.service.*;
import com.get.institutioncenter.utils.MyStringUtils;
import com.get.institutioncenter.dto.CommentDto;
import com.get.institutioncenter.dto.query.ContractQueryDto;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.vo.HiCommentFeignVo;
import com.get.workflowcenter.feign.IWorkflowCenterClient;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.get.common.eunms.ProjectExtraEnum.INSTITUTIONSIGNINGTYPE;

/**
 * <AUTHOR>
 * @DATE: 2020/8/20
 * @TIME: 18:34
 * @Description: 合同实现类
 **/
@Service
public class ContractServiceImpl extends BaseServiceImpl<ContractMapper, Contract> implements IContractService {
    @Resource
    private ContractMapper contractMapper;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private IContractCompanyService companyService;
    @Resource
    @Lazy
    private IInstitutionProviderService providerService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ICommentService commentService;
    @Resource
    private IInstitutionProviderCompanyService providerCompanyService;
    @Resource
    private InstitutionMapper institutionMapper;
    @Resource
    private InstitutionCourseMapper institutionCourseMapper;
    @Resource
    private InstitutionProviderMapper institutionProviderMapper;
    @Resource
    private IWorkflowCenterClient workflowCenterClient;
    @Resource
    private UtilService utilService;
    @Resource
    @Lazy
    private IInstitutionService iInstitutionService;
    @Resource
    private IInstitutionCourseService iInstitutionCourseService;

    @Resource
    private ContractTypeMapper contractTypeMapper;




    @Override
    public List<ContractVo> getAllContract(ContractQueryDto contractVo, Page page, List<Long> companyIds) {
        if (GeneralTool.isEmpty(contractVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        List<Long> contractId;
        List<Long> providerIdList = null;
        if (GeneralTool.isEmpty(contractVo.getFkCompanyId())) {
            //获取当前登录员工可查看的合同id
            contractId = getContractList();
        } else {
            contractId = getContractId(contractVo);
        }
        //对国家过滤
        if (GeneralTool.isNotEmpty(contractVo.getFkCountryId())) {
            //获取提供商ids
            providerIdList = getProviderIdList(contractVo);
        }

        if (GeneralTool.isEmpty(contractId)) {
            contractId = new ArrayList<>();
            contractId.add(0L);
        }


        Boolean selectstatus = false;
        Integer myApplication = null;
        //去除 sql中的 当前登录员工可查看的合同id
        Integer status = null;
        //我的申请，如果传了提供商id 就是查学校提供商里面的合同管理
        if (contractVo.getFkInstitutionProviderId() == null) {
            if (contractVo.getSelectStatus().equals(ProjectExtraEnum.MYAPPLICATION.key)) {
                myApplication = 0;
                selectstatus = true;
            }
        }
        //我的审批
        List<Long> personalHistoryTasks = null;
        if (contractVo.getSelectStatus().equals(ProjectExtraEnum.MYAPPROVAL.key)) {
            Result<List<Long>> result = workflowCenterClient.getPersonalHistoryTasks(contractVo.getProcdkey());
            if (!result.isSuccess()) {
                throw new GetServiceException(result.getMessage());
            }
            if (personalHistoryTasks == null || personalHistoryTasks.size() == 0) {
                return null;
            }
            //去除 sql中的 当前登录员工可查看的合同id
            status = 0;
            selectstatus = true;
        }
        //获取分页数据
        IPage<Contract> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        //要按照最新记录排序
        List<ContractVo> contracts = contractMapper.selectByContractVo(pages, contractVo, contractId, providerIdList,
                personalHistoryTasks, myApplication, status, companyIds);
        page.setAll((int) pages.getTotal());
        if (selectstatus == false) {
            List<ContractVo> collect = contracts.stream().map(contract -> BeanCopyUtils.objClone(contract, ContractVo::new)).collect(Collectors.toList());

            //根据学校ids 查找对应名称map
            Set<Long> institutionIds = collect.stream().map(ContractVo::getFkInstitutionId).collect(Collectors.toSet());
            Map<Long, String> institutionNamesByIds = new HashMap<>();
            if (GeneralTool.isNotEmpty(institutionIds)) {
                institutionNamesByIds = iInstitutionService.getInstitutionNamesByIds(institutionIds);
            }

            //根据课程ids 查找对应课程名称map
            Set<Long> institutionCourseIds = collect.stream().map(ContractVo::getFkInstitutionCourseId).collect(Collectors.toSet());
            Map<Long, String> institutionCourseNamesByIds = new HashMap<>();
            if (GeneralTool.isNotEmpty(institutionCourseIds)) {
                institutionCourseNamesByIds = iInstitutionCourseService.getInstitutionCourseNamesByIds(institutionCourseIds);
            }

            //根据学校提供商ids 查询名称map
            Set<Long> institutionProviderIds = collect.stream().map(ContractVo::getFkInstitutionProviderId).collect(Collectors.toSet());
            Map<Long, String> institutionProviderNamesByIds = new HashMap<>();
            if (GeneralTool.isNotEmpty(institutionProviderIds)) {
                institutionProviderNamesByIds = providerService.getInstitutionProviderNamesByIds(institutionProviderIds);
            }

            //设置属性名称
            setCompanyName(collect);
            setDtoName(collect, institutionNamesByIds, institutionCourseNamesByIds, institutionProviderNamesByIds);
            return collect;

        } else {
            List<ContractVo> contractVos = BeanCopyUtils.copyListProperties(contracts, ContractVo::new);
            ArrayList<ContractVo> newContractVo = new ArrayList<>();
            for (ContractVo contractDto : contractVos) {
                ContractVo newcontractVo = contractDto;
                newcontractVo.setFkTableParentId(contractDto.getFkContractIdRevoke());
                Result<ActRuTaskVo> result =
                        workflowCenterClient.getContractTaskDataByBusinessKey(String.valueOf(newcontractVo.getId()), "m_institution_provider");
                if (!result.isSuccess()) {
                    throw new GetServiceException(result.getMessage());
                }
                ActRuTaskVo taskVersionByBusinessKey = result.getData();
                if (taskVersionByBusinessKey != null) {
                    newcontractVo.setTaskVersion(taskVersionByBusinessKey.getTaskVersion());
                    newcontractVo.setTaskId(taskVersionByBusinessKey.getId());
                    newcontractVo.setProcInstId(taskVersionByBusinessKey.getProcInstId());

                    //设置申请人id
                    if (StringUtils.isNotBlank(contractDto.getGmtCreateUser())) {
                        StaffVo staffVo = permissionCenterClient.getStaffByCreateUser(contractDto.getGmtCreateUser()).getData();
                        if (staffVo != null) {
                            newcontractVo.setFkStaffId(staffVo.getId());
                        }

                    }

                    //待修改和上面
                    Result<Integer> result1 = workflowCenterClient.getSignOrGet(taskVersionByBusinessKey.getId(), taskVersionByBusinessKey.getTaskVersion());
                    newcontractVo.setSignOrGetStatus(result1.getData());
                } else {
                    newcontractVo.setSignOrGetStatus(2);
                }
                //公司id
                newcontractVo.setFkcompanyId(SecureUtil.getFkCompanyId());
                newContractVo.add(newcontractVo);
            }

            //根据学校ids 查找对应名称map
            Set<Long> institutionIds = newContractVo.stream().map(ContractVo::getFkInstitutionId).collect(Collectors.toSet());
            Map<Long, String> institutionNamesByIds = new HashMap<>();
            if (GeneralTool.isNotEmpty(institutionIds)) {
                institutionNamesByIds = iInstitutionService.getInstitutionNamesByIds(institutionIds);
            }

            //根据课程ids 查找对应课程名称map
            Set<Long> institutionCourseIds = newContractVo.stream().map(ContractVo::getFkInstitutionCourseId).collect(Collectors.toSet());
            Map<Long, String> institutionCourseNamesByIds = new HashMap<>();
            if (GeneralTool.isNotEmpty(institutionCourseIds)) {
                institutionCourseNamesByIds = iInstitutionCourseService.getInstitutionCourseNamesByIds(institutionCourseIds);
            }

            //根据学校提供商ids 查询名称map
            Set<Long> institutionProviderIds = newContractVo.stream().map(ContractVo::getFkInstitutionProviderId).collect(Collectors.toSet());
            Map<Long, String> institutionProviderNamesByIds = new HashMap<>();
            if (GeneralTool.isNotEmpty(institutionProviderIds)) {
                institutionProviderNamesByIds = providerService.getInstitutionProviderNamesByIds(institutionProviderIds);
            }

            setCompanyName(newContractVo);
            setDtoName(newContractVo, institutionNamesByIds, institutionCourseNamesByIds, institutionProviderNamesByIds);
            return newContractVo;
        }
    }


    private List<Long> getContractList() {
        List<ContractCompanyVo> contractCompany = companyService.getContractCompany(SecureUtil.getCompanyIds());
        System.out.println("==从redis获取到的companyIds列表==>" + GeneralTool.toJson(contractCompany));
        List<Long> contractList = new ArrayList<>();
        if (GeneralTool.isNotEmpty(contractCompany)) {
            contractList = contractCompany.stream().map(ContractCompanyVo::getFkContractId).collect(Collectors.toList());
        }
        if (GeneralTool.isEmpty(contractList)) {
            contractList = new ArrayList<>();
            contractList.add(0L);
        }
        return contractList;
    }

    @Override
    public ContractVo getContractById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Contract contract = contractMapper.selectById(id);
        ContractVo contractVo = BeanCopyUtils.objClone(contract, ContractVo::new);
        // 把合同的类型补充上
        if (GeneralTool.isNotEmpty(contractVo.getFkContractTypeId())) {
            contractVo.setFkContractTypeName(contractTypeMapper.selectById(contractVo.getFkContractTypeId()).getTypeName());
        }
        Result<HiCommentFeignVo> hiComment = workflowCenterClient.getHiComment(id, TableEnum.INSTITUTION_CONTRACT.key);
        if (hiComment.isSuccess()) {
            contractVo.setAgreeButtonType(hiComment.getData().getAgreeButtonType());
            contractVo.setRefuseButtonType(hiComment.getData().getRefuseButtonType());
        }
        List<ContractVo> contractVos = new ArrayList<>();
        Map<String, List<Long>> businessIdsWithProcdefKey = new HashMap<>();
        businessIdsWithProcdefKey.put(TableEnum.INSTITUTION_CONTRACT.key, Collections.singletonList(Long.valueOf(id)));
        Result<Map<Long, ActRuTaskVo>> actRuTaskDtoMapResult = workflowCenterClient.getActRuTaskDtosByBusinessKey(businessIdsWithProcdefKey);
        if (!actRuTaskDtoMapResult.isSuccess()) {
            throw new GetServiceException(actRuTaskDtoMapResult.getMessage());
        }
        Map<Long, ActRuTaskVo> actRuTaskDtoMap = actRuTaskDtoMapResult.getData();
        ActRuTaskVo actRuTaskVo = actRuTaskDtoMap.get(id);
//        ActRuTaskVo taskVersionByBusinessKey =
//                feignWorkFlowService.getContractTaskDataByBusinessKey(String.valueOf(contractVo.getId()), "m_institution_provider");
        if (actRuTaskVo != null) {
//            contractVo.setTaskVersion(taskVersionByBusinessKey.getTaskVersion());
//            contractVo.setTaskId(taskVersionByBusinessKey.getId());
//            contractVo.setProcInstId(taskVersionByBusinessKey.getProcInstId());
            //正在进行的任务id
            if (GeneralTool.isNotEmpty(actRuTaskVo.getId())) {
                contractVo.setTaskId(actRuTaskVo.getId());
            }
            //流程实例id
            if (GeneralTool.isNotEmpty(actRuTaskVo.getProcInstId())) {
                contractVo.setProcInstId(actRuTaskVo.getProcInstId());
            }
            //任务版本
            if (GeneralTool.isNotEmpty(actRuTaskVo.getTaskVersion())) {
                contractVo.setTaskVersion(actRuTaskVo.getTaskVersion());
            }
            //待修改和上面
            int signOrGet = workflowCenterClient.getSignOrGet(actRuTaskVo.getId(), actRuTaskVo.getTaskVersion()).getData();
            contractVo.setSignOrGetStatus(signOrGet);
            //设置申请人id
            if (StringUtils.isNotBlank(contractVo.getGmtCreateUser())) {
                StaffVo staffByCreateUser = permissionCenterClient.getStaffByCreateUser(contractVo.getGmtCreateUser()).getData();
                if (staffByCreateUser != null) {
                    contractVo.setFkStaffId(staffByCreateUser.getId());
                }
            }
        } else {
            contractVo.setSignOrGetStatus(2);
        }
        UserInfo staff = GetAuthInfo.getUser();
        //公司id
        contractVo.setFkcompanyId(staff.getFkCompanyId());


        contractVos.add(contractVo);

        //根据学校ids 查找对应名称map
        Set<Long> institutionIds = contractVos.stream().map(ContractVo::getFkInstitutionId).collect(Collectors.toSet());
        Map<Long, String> institutionNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(institutionIds)) {
            institutionNamesByIds = iInstitutionService.getInstitutionNamesByIds(institutionIds);
        }

        //根据课程ids 查找对应课程名称map
        Set<Long> institutionCourseIds = contractVos.stream().map(ContractVo::getFkInstitutionCourseId).collect(Collectors.toSet());
        Map<Long, String> institutionCourseNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(institutionCourseIds)) {
            institutionCourseNamesByIds = iInstitutionCourseService.getInstitutionCourseNamesByIds(institutionCourseIds);
        }

        //根据学校提供商ids 查询名称map
        Set<Long> institutionProviderIds = contractVos.stream().map(ContractVo::getFkInstitutionProviderId).collect(Collectors.toSet());
        Map<Long, String> institutionProviderNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(institutionProviderIds)) {
            institutionProviderNamesByIds = providerService.getInstitutionProviderNamesByIds(institutionProviderIds);
        }

        //设置属性名称
        setCompanyName(contractVos);
        setDtoName(contractVos, institutionNamesByIds, institutionCourseNamesByIds, institutionProviderNamesByIds);
        return contractVos.get(0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addContract(ContractDto contractDto) {
        Contract contract = createContract(contractDto);

        //添加中间表
        ContractCompanyDto relation = new ContractCompanyDto();
        Long companyId;
        if (GeneralTool.isNotEmpty(contractDto.getFkCompanyId())) {
            companyId = contractDto.getFkCompanyId();
        } else {
            companyId = SecureUtil.getFkCompanyId();
        }
        relation.setFkCompanyId(companyId);
        relation.setFkContractId(contract.getId());
        companyService.addRelation(relation);

        return contract.getId();
    }

    @Override
    public ContractVo updateContract(ContractDto contractDto) {
        if (GeneralTool.isEmpty(contractDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Contract contract = BeanCopyUtils.objClone(contractDto, Contract::new);
        contractMapper.updateById(contract);
        return getContractById(contract.getId());
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        contractMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<MediaAndAttachedVo> addContractMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }

        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.INSTITUTION_CONTRACT.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }

    @Override
    public List<MediaAndAttachedVo> getContractMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.INSTITUTION_CONTRACT.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);

    }

    @Override
    public void editContractCompanyRelation(List<ContractCompanyDto> validList) {
        companyService.editContractCompanyRelation(validList);
    }

    @Override
    public List<CompanyTreeVo> getContractCompanyRelation(Long contractId) {
        return companyService.getContractCompanyRelation(contractId);
    }

    @Override
    public Long editComment(CommentDto commentDto) {
        Comment comment = BeanCopyUtils.objClone(commentDto, Comment::new);
        if (GeneralTool.isNotEmpty(commentDto)) {
            if (GeneralTool.isNotEmpty(commentDto.getId())) {
                comment.setFkTableName(TableEnum.INSTITUTION_CONTRACT.key);
                commentService.updateComment(comment);
            } else {
                comment.setFkTableName(TableEnum.INSTITUTION_CONTRACT.key);
                commentService.addComment(comment);
            }
        }
        return comment.getId();
    }

    @Override
    public List<CommentVo> getComments(CommentDto commentDto, Page page) {
        commentDto.setFkTableName(TableEnum.INSTITUTION_CONTRACT.key);
        return commentService.datas(commentDto, page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addProviderContract(ContractDto contractDto) {
        LambdaUpdateWrapper<InstitutionProvider> wrapper = new LambdaUpdateWrapper<>();
        if(GeneralTool.isNotEmpty(contractDto.getFkInstitutionProviderId())){
            int contractStatus = 1;
            // 设置更新条件

            InstitutionProvider provider = new InstitutionProvider();
            provider.setContractStatus(contractStatus);
            wrapper.eq(InstitutionProvider::getId, contractDto.getFkInstitutionProviderId());

            // 调用 mapper 层的 update 方法执行更新操作
          institutionProviderMapper.update(provider, wrapper);
        }
        Contract contract = createContract(contractDto);
        //获取公司ids
        List<Long> companyIds = providerCompanyService.getRelationByProviderId(contractDto.getFkInstitutionProviderId());
        //跟随提供商的公司关系
        if (GeneralTool.isNotEmpty(companyIds)) {
            //添加中间表
            for (Long companytId : companyIds) {
                ContractCompanyDto relation = new ContractCompanyDto();
                relation.setFkCompanyId(companytId);
                relation.setFkContractId(contract.getId());
                companyService.addRelation(relation);
            }
        }
        //添加附件
//        if (GeneralTool.isNotEmpty(contractVo.getMediaAndAttachedVos())){
//            for (MediaAndAttachedDto mediaAndAttachedVo : contractVo.getMediaAndAttachedVos()) {
//                mediaAndAttachedVo.setFkTableName(TableEnum.INSTITUTION_CONTRACT.key);
//                mediaAndAttachedVo.setFkTableId(contract.getId());
//                mediaAndAttachedVo.setFileKey(FileTypeEnum.INSTITUTION_CONTRACT_FILE.key);
//                MediaAndAttachedDto mediaAndAttachedDto = attachedService.addMediaAndAttached(mediaAndAttachedVo);
//                if (GeneralTool.isEmpty(mediaAndAttachedDto)){
//                    throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
//                }
//            }
//
//        }
        return contract.getId();
    }


    private Contract createContract(ContractDto contractDto) {
        if (GeneralTool.isEmpty(contractDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        Contract contract = BeanCopyUtils.objClone(contractDto, Contract::new);
        //设置合同编号
        contract.setContractNum(MyStringUtils.getContractNum());
        utilService.updateUserInfoToEntity(contract);
        contract.setStatus(0);
        int i = contractMapper.insert(contract);

        //添加附件
        if (GeneralTool.isNotEmpty(contractDto.getMediaAndAttachedVos())){
            for (MediaAndAttachedDto mediaAndAttachedVo : contractDto.getMediaAndAttachedVos()) {
                mediaAndAttachedVo.setFkTableName(TableEnum.INSTITUTION_CONTRACT.key);
                mediaAndAttachedVo.setFkTableId(contract.getId());
                mediaAndAttachedVo.setFileKey(FileTypeEnum.INSTITUTION_CONTRACT_FILE.key);
                // 防止前端传入id，导致没有创建人和创建时间
                mediaAndAttachedVo.setId(null);
                MediaAndAttachedVo mediaAndAttachedDto = attachedService.addMediaAndAttached(mediaAndAttachedVo);
                if (GeneralTool.isEmpty(mediaAndAttachedDto)){
                    throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
                }
            }

        }
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return contract;
    }

    private void setDtoName(List<ContractVo> collect, Map<Long, String> institutionNamesByIds,
                            Map<Long, String> institutionCourseNamesByIds, Map<Long, String> institutionProviderNamesByIds) {
        for (ContractVo contractVo : collect) {
            if (GeneralTool.isNotEmpty(contractVo.getFkInstitutionId())) {
                contractVo.setFkInstitutionName(institutionNamesByIds.get(contractVo.getFkInstitutionId()));
            }
            if (GeneralTool.isNotEmpty(contractVo.getFkInstitutionCourseId())) {
                contractVo.setFkInstitutionCourseName(institutionCourseNamesByIds.get(contractVo.getFkInstitutionCourseId()));
            }
            if (GeneralTool.isNotEmpty(contractVo.getFkInstitutionProviderId())) {
                contractVo.setFkInstitutionProviderName(institutionProviderNamesByIds.get(contractVo.getFkInstitutionProviderId()));
            }
        }
    }


    private void setCompanyName(List<ContractVo> collect) {
        //获取所有公司
        Map<Long, String> companyMap = getCompanyMap();
        if (GeneralTool.isNotEmpty(collect)) {
            List<ContractCompanyVo> contractCompanyVos = companyService.getContractCompany(SecureUtil.getCompanyIds());
            for (ContractVo contractVo : collect) {
                setCompanyName(companyMap, contractVo, contractCompanyVos);
            }
        }
    }

    private List<Long> getEmptyQueryList() {
        List<Long> empty = new ArrayList<>();
        empty.add(0L);
        return empty;
    }

    private void setCompanyName(Map<Long, String> companyMap, ContractVo contractVo, List<ContractCompanyVo> contractCompanyVos) {
        //获取中间表
        if (GeneralTool.isNotEmpty(contractCompanyVos)) {
            StringBuilder builder = new StringBuilder();
            for (ContractCompanyVo companyDto : contractCompanyVos) {
                if (contractVo.getId().equals(companyDto.getFkContractId())) {
                    Long fkCompanyId = companyDto.getFkCompanyId();
                    String companyName = companyMap.get(fkCompanyId);
                    builder.append(companyName).append("，");
                }
            }
            contractVo.setFkCompanyName(sub(builder));
        }
    }

    private String sub(StringBuilder sb) {
        if (GeneralTool.isEmpty(sb)) {
            return null;
        }
        String substring = null;
        int i = sb.lastIndexOf("，");
        if (i != -1) {
            substring = sb.substring(0, i);
        }
        return substring;
    }

    private List<Long> getContractId(ContractQueryDto contractVo) {
        Long fkCompanyId = contractVo.getFkCompanyId();
        //校验公司ID权限
        if (!SecureUtil.validateCompany(fkCompanyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        List<Long> companyList = new ArrayList<>();
        companyList.add(fkCompanyId);
        //查询合同id
        List<ContractCompanyVo> contractCompany = companyService.getContractCompany(companyList);
        if (GeneralTool.isEmpty(contractCompany)) {
            return null;
        }
        List<Long> collect = contractCompany.stream().map(ContractCompanyVo::getFkContractId).collect(Collectors.toList());
        if (GeneralTool.isEmpty(collect)) {
            collect = new ArrayList<>();
            collect.add(0L);
        }
        return collect;
    }

    private List<Long> getProviderIdList(ContractQueryDto contractVo) {
        List<Long> countryIds = new ArrayList<>();
        countryIds.add((long) Math.toIntExact(contractVo.getFkCountryId()));
        //查询
        List<InstitutionProviderVo> providerList = providerService.getProviderList(countryIds);
        List<Long> providerId = providerList.stream().map(InstitutionProviderVo::getId).collect(Collectors.toList());
        List<Long> emptyQueryList = getEmptyQueryList();
        return GeneralTool.isEmpty(providerId) ? emptyQueryList : providerId;
    }

    private Map<Long, String> getCompanyMap() {
        Result<List<com.get.permissioncenter.vo.tree.CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        List<com.get.permissioncenter.vo.tree.CompanyTreeVo> companyTreeVos = result.getData();
        //初始为5的map
        Map<Long, String> companyMap = new HashMap<>(5);
        if (GeneralTool.isNotEmpty(companyTreeVos)) {
            companyMap = companyTreeVos.stream().collect(Collectors.toMap(com.get.permissioncenter.vo.tree.CompanyTreeVo::getId, com.get.permissioncenter.vo.tree.CompanyTreeVo::getShortName));
        }
        return companyMap;
    }

    @Override
    public ContractVo getInstitutionContractById(Long id) {
        Contract contract = contractMapper.selectById(id);
        ContractVo contractVo = BeanCopyUtils.objClone(contract, ContractVo::new);
        return contractVo;
    }

    @Override
    public List<Map<String, Object>> getContractApprovalMode() {
        return ProjectExtraEnum.enumsTranslation2Arrays(INSTITUTIONSIGNINGTYPE);

    }

    @Override
    public void startInstitutionContractFlow(String businessKey, String procdefKey, String companyId) {
        Result<Boolean> aBoolean = workflowCenterClient.startInstitutionContractFlow(businessKey, procdefKey, companyId);
        if (aBoolean.getData() == false) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("start_error"));
        }
    }

    @Override
    public boolean updateChangeStatus(Contract contract) {
        utilService.updateUserInfoToEntity(contract);
        contractMapper.updateById(contract);
        return true;
    }


    @Override
    public void updateCancellationBusiness(Long id) {
        Contract agentContract = contractMapper.selectById(id);
        if (agentContract == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        agentContract.setStatus(5);
        utilService.updateUserInfoToEntity(agentContract);
        contractMapper.updateById(agentContract);
    }

    @Override
    public boolean changeStatus(Integer status, String tableName, Long businessKey) {
        contractMapper.changeStatus(status, tableName, businessKey);
        return true;
    }

    @Override
    public void getUserSubmit(String taskId, String status) {
        /* feignWorkFlowService.getContractUserSubmit(taskId, status);*/
    }

    @Override
    public void getRevokeContract(Long id, String summary) {
        Contract contract = contractMapper.selectById(id);
        if (GeneralTool.isEmpty(contract)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        if (contractMapper.getExistParentId(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        contract.setStatus(null);
        contract.setId(null);
        contract.setContractNum(null);
        contract.setGmtModified(null);
        contract.setGmtModifiedUser(null);
        contract.setFkContractIdRevoke(id);
        contract.setTitle(summary);
        ContractDto contractDto = BeanCopyUtils.objClone(contract, ContractDto::new);
        Long aLong = this.addContract(contractDto);
        if (GeneralTool.isNotEmpty(aLong)) {
            List<MediaAndAttached> mediaAndAttachedByTableId = attachedService.findMediaAndAttachedByTableId(id, "m_contract", "institution_contract_file");
            if (GeneralTool.isNotEmpty(mediaAndAttachedByTableId)) {
                mediaAndAttachedByTableId.stream().forEach(mediaAndAttachedDto -> {
                    mediaAndAttachedDto.setFkTableId(aLong);
                    mediaAndAttachedDto.setId(null);
                    mediaAndAttachedDto.setGmtModifiedUser(null);
                    mediaAndAttachedDto.setGmtModified(null);
                });
                this.addContractMedia(BeanCopyUtils.copyListProperties(mediaAndAttachedByTableId, com.get.institutioncenter.dto.MediaAndAttachedDto::new));
            }
            Contract contract1 = contractMapper.selectById(id);
            contract1.setStatus(ProjectExtraEnum.REVOKED.key);
            utilService.updateUserInfoToEntity(contract1);
            contractMapper.updateById(contract1);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));

        }
    }

    @Override
    public String getContractNewByProviderId(Long fkInstitutionProviderId) {
        String contractNum = "";

        LambdaQueryWrapper<Contract> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Contract::getFkInstitutionProviderId,fkInstitutionProviderId).orderByDesc(Contract::getGmtCreate);
        Contract contract = contractMapper.selectOne(lambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(contract)){
            contractNum = contract.getContractNum();
        }
        return contractNum;
    }
}
