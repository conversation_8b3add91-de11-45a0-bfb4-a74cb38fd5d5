package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/*
 * author:Neil
 * Time: 12:34
 * Date: 2022/5/31
 * Description:
 */
@Data
public class InstitutionCourseNameDto extends BaseVoEntity {
    @ApiModelProperty(value = "课程名称")
    private String courseName;
    @ApiModelProperty(value = "学校ids")
    private List<Long> institutionIds;
    @ApiModelProperty(value = "课程ids")
    private List<Long> courseIds;
}
