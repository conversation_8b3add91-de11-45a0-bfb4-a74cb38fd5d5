package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 凭证
 */
@Data
@TableName("m_vouch")
public class Vouch extends BaseEntity implements Serializable{

    @ApiModelProperty(value = "凭证号：银付-202504-000279")
    private String vouchNum;

    @ApiModelProperty(value = "凭证类型：转/现收/现付/银收/银付")
    private String vouchType;

    @ApiModelProperty(value = "凭证月份：yyyyMM")
    private String vouchMonth;

    @ApiModelProperty(value = "凭证当月数量累计（6位补0）")
    private Integer vouchQty;

    @ApiModelProperty(value = "业务时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date businessDate;

    @ApiModelProperty(value = "参考信息")
    private String remark;

    @ApiModelProperty(value = "归口公司")
    private Long fkCompanyId;

    @ApiModelProperty(value = "创建来源：system/import")
    private String createType;

    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;


}