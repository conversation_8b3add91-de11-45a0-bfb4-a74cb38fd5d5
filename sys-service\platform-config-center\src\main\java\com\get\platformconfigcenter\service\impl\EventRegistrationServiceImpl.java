package com.get.platformconfigcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.SearchBean;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.platformconfigcenter.dao.appmso.EventRegistrationMapper;
import com.get.platformconfigcenter.dto.EventRegistrationDto;
import com.get.platformconfigcenter.dto.IsAttendDto;
import com.get.platformconfigcenter.entity.MsoEventRegistration;
import com.get.platformconfigcenter.service.EventRegistrationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName: EventRegistrationService
 * @Author: Eric
 * @Date: 2023/5/24 9:58
 * @Version: 1.0
 */
@Service
public class EventRegistrationServiceImpl extends BaseServiceImpl<EventRegistrationMapper, MsoEventRegistration> implements EventRegistrationService {

    @Resource
    private EventRegistrationMapper eventRegistrationMapper;


    @Override
    public List<MsoEventRegistration> getEventRegistrationList(EventRegistrationDto data, SearchBean<EventRegistrationDto> eventRegistrationVo) {
       IPage<MsoEventRegistration> iPage = GetCondition.getPage(PageUtil.convertToQuery(eventRegistrationVo.getCurrentPage(),eventRegistrationVo.getShowCount()));

       List<MsoEventRegistration> eventRegistrations = eventRegistrationMapper.getEventRegistrationList(data,iPage);

        eventRegistrationVo.setAll((int) iPage.getTotal());


        return eventRegistrations;
    }

    @Override
    public void batchUpdateIsAttend(IsAttendDto isAttendDto) {
        if(GeneralTool.isEmpty(isAttendDto.getIds())){
           throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }

        if(GeneralTool.isEmpty(isAttendDto.getIsAttend())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("type_is_null"));
        }


        eventRegistrationMapper.batchUpdateIsAttend(isAttendDto);


    }

    @Override
    public void batchDeleteEventRegistration(List<Long> ids) {
        if(GeneralTool.isEmpty(ids)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        LambdaQueryWrapper<MsoEventRegistration> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MsoEventRegistration::getId,ids);
        eventRegistrationMapper.delete(wrapper);

    }
}
