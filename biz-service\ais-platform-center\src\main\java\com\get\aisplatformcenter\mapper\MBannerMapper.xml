<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.aisplatformcenter.mapper.MBannerMapper">

    <resultMap id="BaseResultMap" type="com.get.aisplatformcenterap.entity.MBannerEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkCompanyId" column="fk_company_id" jdbcType="BIGINT"/>
            <result property="fkPlatformId" column="fk_platform_id" jdbcType="BIGINT"/>
            <result property="fkPlatformCode" column="fk_platform_code" jdbcType="VARCHAR"/>
            <result property="fkBannerTypeId" column="fk_banner_type_id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="weight" column="weight" jdbcType="INTEGER"/>
            <result property="jumpMode" column="jump_mode" jdbcType="INTEGER"/>
            <result property="jumpUrl" column="jump_url" jdbcType="VARCHAR"/>
            <result property="webTitle" column="web_title" jdbcType="VARCHAR"/>
            <result property="webMetaDescription" column="web_meta_description" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
            <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        banner.id,
        banner.fk_company_id,
        banner.fk_platform_id,
        banner.fk_platform_code,
        banner.fk_banner_type_id,
        banner.name,
        banner.weight,
        banner.jump_mode,
        banner.jump_url,
        banner.web_title,
        banner.web_meta_description,
        banner.status,
        banner.start_time,
        banner.end_time,
        banner.remark,
        banner.gmt_create,
        banner.gmt_create_user,
        banner.gmt_modified,
        banner.gmt_modified_user
    </sql>


    <select id="searchBannerPage" resultType="com.get.aisplatformcenterap.vo.MBannerVo">
        <if test="query.status==null or (query.status!=null and query.status==2)">
            SELECT a.* FROM (
                SELECT  <include refid="Base_Column_List" /> ,
                platform.name AS platFormName,
                uBannerType.type_name,
                uBannerType.width,
                uBannerType.height,
                company.num AS companyName,
                (
                SELECT  CONCAT(#{query.mMageAddress}, platFormFile.file_key) from ais_platform_center.s_media_and_attached sAttached
                INNER JOIN ais_file_center.m_file_platform platFormFile ON platFormFile.file_guid = sAttached.fk_file_guid
                WHERE sAttached.fk_table_name='m_banner'    AND sAttached.fk_table_id=banner.id limit 1
                ) AS file_key
                FROM m_banner banner
                LEFT JOIN ais_platform_center.m_platform platform ON banner.fk_platform_id=platform.id
                LEFT JOIN ais_platform_center.u_banner_type uBannerType ON banner.fk_banner_type_id=uBannerType.id
                LEFT JOIN ais_permission_center.m_company company ON banner.fk_company_id=company.id
                WHERE banner.status=2
                <if test="query.fkCompanyId != null ">
                    AND banner.fk_company_id=#{query.fkCompanyId}
                </if>
                <if test="query.fkPlatformId != null ">
                    AND banner.fk_platform_id=#{query.fkPlatformId}
                </if>
                <if test="query.fkBannerTypeId != null ">
                    AND banner.fk_banner_type_id=#{query.fkBannerTypeId}
                </if>
                <if test="query.status!=null">
                    AND banner.status=#{query.status}
                </if>

                <if test="query.name!=null and query.name!='' ">
                    AND position(#{query.name,jdbcType=VARCHAR} IN banner.name)
                </if>
                ORDER BY banner.status,banner.weight DESC limit 1000
            ) a
        </if>

        <if test="query.status==null">
            UNION ALL
        </if>

        <if test="query.status==null or (query.status!=null and query.status==1)">

            SELECT b.* FROM (
            SELECT  <include refid="Base_Column_List" /> ,
            platform.name AS platFormName,
            uBannerType.type_name,
            uBannerType.width,
            uBannerType.height,
            company.name_chn AS companyName,
            (
            SELECT  CONCAT(#{query.mMageAddress}, platFormFile.file_key) from ais_platform_center.s_media_and_attached sAttached
            INNER JOIN ais_file_center.m_file_platform platFormFile ON platFormFile.file_guid = sAttached.fk_file_guid
            WHERE sAttached.fk_table_name='m_banner'    AND sAttached.fk_table_id=banner.id limit 1
            ) AS file_key
            FROM m_banner banner
            LEFT JOIN ais_platform_center.m_platform platform ON banner.fk_platform_id=platform.id
            LEFT JOIN ais_platform_center.u_banner_type uBannerType ON banner.fk_banner_type_id=uBannerType.id
            LEFT JOIN ais_permission_center.m_company company ON banner.fk_company_id=company.id
            WHERE banner.status=1
            <if test="query.fkCompanyId != null ">
                AND banner.fk_company_id=#{query.fkCompanyId}
            </if>
            <if test="query.fkPlatformId != null ">
                AND banner.fk_platform_id=#{query.fkPlatformId}
            </if>
            <if test="query.fkBannerTypeId != null ">
                AND banner.fk_banner_type_id=#{query.fkBannerTypeId}
            </if>

            <if test="query.name!=null and query.name!='' ">
                AND position(#{query.name,jdbcType=VARCHAR} IN banner.name)
            </if>
            ORDER BY banner.start_time DESC  limit 1000
            ) b
        </if>
        <if test="query.status==null">
            UNION ALL
        </if>

        <if test="query.status==null or  (query.status!=null and query.status==0)">
            SELECT c.* FROM (
            SELECT  <include refid="Base_Column_List" /> ,
            platform.name AS platFormName,
            uBannerType.type_name,
            uBannerType.width,
            uBannerType.height,
            company.name_chn AS companyName,
            (
            SELECT  CONCAT(#{query.mMageAddress}, platFormFile.file_key) from ais_platform_center.s_media_and_attached sAttached
            INNER JOIN ais_file_center.m_file_platform platFormFile ON platFormFile.file_guid = sAttached.fk_file_guid
            WHERE sAttached.fk_table_name='m_banner'    AND sAttached.fk_table_id=banner.id limit 1
            ) AS file_key
            FROM m_banner banner
            LEFT JOIN ais_platform_center.m_platform platform ON banner.fk_platform_id=platform.id
            LEFT JOIN ais_platform_center.u_banner_type uBannerType ON banner.fk_banner_type_id=uBannerType.id
            LEFT JOIN ais_permission_center.m_company company ON banner.fk_company_id=company.id
            WHERE banner.status=0
            <if test="query.fkCompanyId != null ">
                AND banner.fk_company_id=#{query.fkCompanyId}
            </if>
            <if test="query.fkPlatformId != null ">
                AND banner.fk_platform_id=#{query.fkPlatformId}
            </if>
            <if test="query.fkBannerTypeId != null ">
                AND banner.fk_banner_type_id=#{query.fkBannerTypeId}
            </if>

            <if test="query.name!=null and query.name!='' ">
                AND position(#{query.name,jdbcType=VARCHAR} IN banner.name)
            </if>
            ORDER BY banner.end_time DESC  limit 1000
            ) c

        </if>




    </select>
    <select id="getDetail" resultType="com.get.aisplatformcenterap.vo.MBannerVo">
        SELECT  <include refid="Base_Column_List" /> ,
        platform.name AS platFormName,
        uBannerType.type_name,
        uBannerType.width,
        uBannerType.height,
        company.name_chn AS companyName,
        (
        SELECT  CONCAT(#{mMageAddress}, platFormFile.file_key) from ais_platform_center.s_media_and_attached sAttached
        INNER JOIN ais_file_center.m_file_platform platFormFile ON platFormFile.file_guid = sAttached.fk_file_guid
        WHERE sAttached.fk_table_name='m_banner'    AND sAttached.fk_table_id=banner.id limit 1
        ) AS file_key
        FROM m_banner banner
        LEFT JOIN ais_platform_center.m_platform platform ON banner.fk_platform_id=platform.id
        LEFT JOIN ais_platform_center.u_banner_type uBannerType ON banner.fk_banner_type_id=uBannerType.id
        LEFT JOIN ais_permission_center.m_company company ON banner.fk_company_id=company.id
        WHERE banner.id=#{id}



    </select>


    <update id="updatePutAway">
        UPDATE m_banner
        <if test="type!=null and type==1">
            SET status=0
            <if test="gmtModifiedUser!=null and gmtModifiedUser!=''">
                ,gmt_modified_user=#{gmtModifiedUser}
            </if>
            <if test="gmtModified!=null ">
                ,gmt_modified=#{gmtModified}
            </if>
        </if>
        <if test="type!=null and type==2">
            SET status=2
            <if test="gmtModifiedUser!=null and gmtModifiedUser!=''">
                ,gmt_modified_user=#{gmtModifiedUser}
            </if>
            <if test="gmtModified!=null ">
                ,gmt_modified=#{gmtModified}
            </if>
        </if>


        WHERE
        id IN
        <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>

    </update>
</mapper>
