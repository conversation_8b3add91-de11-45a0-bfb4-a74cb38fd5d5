package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.salecenter.entity.KpiPlanTarget;
import com.get.salecenter.dto.KpiPlanTargetDto;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface KpiPlanTargetService extends IService<KpiPlanTarget> {

    /**
     * 新增KPI目标设置
     * <AUTHOR>
     * @DateTime 2024/4/18 17:07
     */
    Long addKpiPlanTarget(KpiPlanTargetDto kpiPlanTargetDto);

    /**
     * 批量设置KPI
     * <AUTHOR>
     * @DateTime 2024/4/18 16:58
     */
    void batchAdd(List<KpiPlanTargetDto> kpiPlanTargetDtos);

}
