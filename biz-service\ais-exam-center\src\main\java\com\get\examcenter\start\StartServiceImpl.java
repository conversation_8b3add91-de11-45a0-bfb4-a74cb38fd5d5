package com.get.examcenter.start;

import com.get.core.auto.service.AutoService;
import com.get.core.start.service.StartService;
import com.get.core.start.utils.PropsUtil;
import org.springframework.boot.builder.SpringApplicationBuilder;

import java.util.Properties;

/**
 * 如有多数据源则需要配置该参数
 */
@AutoService(StartService.class)
public class StartServiceImpl implements StartService {

    @Override
    public void launcher(SpringApplicationBuilder builder, String appName, String profile, boolean isLocalDev) {
        Properties props = System.getProperties();
        PropsUtil.setProperty(props, "spring.datasource.dynamic.enabled", "true");
    }

}
