package com.get.examcenter.feign;

import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.salecenter.dto.DataCollectionQuestionDto;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Feign失败配置
 */
@Component
@VerifyPermission(IsVerify = false)
public class IExamCenterClientFallBack implements IExamCenterClient {
    @Override
    public Result<Map<Long, String>> getExaminationNamesByExaminationIds(Set<Long> examinationIds) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getNamesByQuestionTypeIds(Set<Long> questionTypeIds) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Set<Long>> getUserIdsByStaffIds(Set<Long> fkStaffIds) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<DataCollectionQuestionDto>> getDataCollectionQuestions(String contactTel) {
        return null;
    }

    @Override
    public Result<Boolean> saveDataCollectionQuestion(List<DataCollectionQuestionDto> dataCollectionQuestionList, @NotBlank String receiptCode, String contactTel) {
        return null;
    }
}
