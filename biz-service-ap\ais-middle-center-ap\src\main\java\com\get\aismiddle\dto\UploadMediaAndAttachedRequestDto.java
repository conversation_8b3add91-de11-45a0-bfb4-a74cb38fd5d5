package com.get.aismiddle.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 文件保存DTO
 */
@Data
public class UploadMediaAndAttachedRequestDto {

    @ApiModelProperty(value = "文件对应的微服务名称, 如：销售中心,华通伙伴中心")
    @NotBlank(message = "文件对应的微服务名称不能为空")
    private String serviceName;

    @ApiModelProperty(value = "表名")
    @NotBlank(message = "表名不能为空")
    private String fkTableName;

    @ApiModelProperty(value = "表Id")
    @NotNull(message = "表Id不能为空")
    private Long fkTableId;

    @ApiModelProperty(value = "类型关键字，如：institution_mov/institution_pic/alumnus_head_icon")
    @NotBlank(message = "类型关键字不能为空")
    private String typeKey;

    @ApiModelProperty(value = "创建人")
    @NotBlank(message = "创建人不能为空")
    private String gmtCreateUser;

    @ApiModelProperty(value = "文件guid")
    @NotBlank(message = "文件guid不能为空")
    private String fileGuid;

}
