package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.dto.PaymentFormAgentUpdateDto;
import com.get.financecenter.entity.PayablePlanSettlementAgentAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface PayablePlanSettlementAgentAccountMapper extends BaseMapper<PayablePlanSettlementAgentAccount> {
    /**
     * 查询佣金结算中状态 1：不在佣金结算中，可编辑  2：佣金结算中 编辑币种需要同时更新佣金结算币种标记 3：已进行最终佣金结算 不可编辑
     * @param agentContractAccountId
     * @return
     */
    Integer getCommissionSettlementStatus(@Param("agentContractAccountId") Long agentContractAccountId);

    /**
     * 修改付款单代理账户信息  - 修改对应的佣金结算信息
     *
     * @Date 10:40 2022/12/22
     * <AUTHOR>
     */
    int updateCommissionSettlement(@Param("paymentFormAgentUpdateDto") PaymentFormAgentUpdateDto paymentFormAgentUpdateDto, @Param("fkCurrencyTypeNum") String fkCurrencyTypeNum);

    /**
     * 银行账号是否存在佣金数据
     *
     * @Date 15:58 2024/2/18
     * <AUTHOR>
     */
    Boolean getCommissionSettlementAccountInfo(@Param("agentContractAccountId") Long agentContractAccountId);

}