package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.vo.InstitutionCourseRankingVo;
import com.get.institutioncenter.service.IInstitutionCourseRankingService;
import com.get.institutioncenter.dto.AppInfoDto;
import com.get.institutioncenter.dto.InstitutionCourseRankingDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/12/10
 * @TIME: 16:47
 * @Description:
 **/
@Api(tags = "课程排名管理")
@RestController
@RequestMapping("/institution/institutionCourseRanking")
public class InstitutionCourseRankingController {
    @Resource
    private IInstitutionCourseRankingService institutionCourseRankingService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/课程管理/排名详情")
    @GetMapping("/{id}")
    public ResponseBo<InstitutionCourseRankingVo> detail(@PathVariable("id") Long id) {
        InstitutionCourseRankingVo data = institutionCourseRankingService.findInstitutionCourseRankingById(id);
        return new ResponseBo<>(data);
    }


    /**
     * 删除
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/课程管理/删除排名")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        this.institutionCourseRankingService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param institutionCourseRankingDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/课程管理/更新排名")
    @PostMapping("update")
    public ResponseBo<InstitutionCourseRankingVo> update(@RequestBody @Validated(AppInfoDto.Update.class) InstitutionCourseRankingDto institutionCourseRankingDto) {
        return UpdateResponseBo.ok(institutionCourseRankingService.updateInstitutionCourseRanking(institutionCourseRankingDto));
    }

    /**
     * 批量新增信息
     * @param resources
     * @return
     * @
     */
   /* @ApiOperation(value = "批量新增接口",notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER,type = LoggerOptTypeConst.ADD,description = "学校中心/申请资料管理/批量保存")
    @PostMapping("batchAdd" )
    public ResponseBo batchAdd(@RequestBody @Validated(AppInfoDto.Add.class) ValidList<AppInfoDto> resources)  {
        appInfoService.batchAdd(resources);
        return ResponseBo.ok();
    }*/

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 申请资料类型下拉
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "排名下拉", notes = "")
    @GetMapping("findRankingType")
    public ResponseBo findRankingType() {
        List<Map<String, Object>> datas = institutionCourseRankingService.findType();
        return new ListResponseBo<>(datas);
    }
}
