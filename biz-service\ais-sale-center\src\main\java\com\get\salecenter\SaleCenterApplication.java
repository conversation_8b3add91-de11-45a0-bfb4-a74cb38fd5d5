package com.get.salecenter;

import com.get.common.constant.AppCenterConstant;
import com.get.core.cloud.feign.EnableGetFeign;
import com.get.core.start.GetApplication;
import org.springframework.cloud.client.SpringCloudApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * Time: 15:48
 *
 *
 * Date: 2021/12/16
 * Description:
 */
@EnableGetFeign
@SpringCloudApplication
@EnableAsync
public class SaleCenterApplication {

    public static void main(String[] args) {
        GetApplication.run(AppCenterConstant.APPLICATION_SALE_CENTER, SaleCenterApplication.class, args);
    }
}
