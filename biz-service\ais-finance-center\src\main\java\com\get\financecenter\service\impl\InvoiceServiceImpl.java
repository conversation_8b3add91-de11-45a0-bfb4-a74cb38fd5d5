package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.DataConverter;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.api.ResultCode;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestHeaderHandler;
import com.get.file.utils.FileUtils;
import com.get.financecenter.dao.*;
import com.get.financecenter.dto.*;
import com.get.financecenter.dto.query.InvoiceQueryDto;
import com.get.financecenter.entity.*;
import com.get.financecenter.service.*;
import com.get.financecenter.vo.*;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dto.InvoiceReceiptFormDto;
import com.get.salecenter.dto.PrepaymentButtonHtiDto;
import com.get.salecenter.entity.PayablePlan;
import com.get.salecenter.entity.ReceivablePlan;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.salecenter.vo.PayablePlanVo;
import com.get.salecenter.vo.ReceivablePlanVo;
import com.get.salecenter.vo.SelItem;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2020/12/16 17:05
 * @verison: 1.0
 * @description:
 */
@Service
@Slf4j
public class InvoiceServiceImpl extends BaseServiceImpl<InvoiceMapper, Invoice> implements IInvoiceService {
    @Resource
    private InvoiceMapper invoiceMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ICurrencyTypeService currencyTypeService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private ISaleCenterClient saleCenterClient;
    @Resource
    private IPaymentFormItemService paymentFormItemService;
    @Resource
    private PayablePlanSettlementInstallmentMapper payablePlanSettlementInstallmentMapper;
    @Resource
    private PayablePlanSettlementStatusMapper payablePlanSettlementStatusMapper;
    @Resource
    private IInvoiceReceivablePlanService iInvoiceReceivablePlanService;
    @Resource
    private InvoiceReceivablePlanMapper invoiceReceivablePlanMapper;
    @Resource
    private InvoiceReceivablePlanServiceImpl invoiceReceivablePlanService;
    @Resource
    private ReceiptFormInvoiceMapper receiptFormInvoiceMapper;
    @Resource
    @Lazy
    private IExchangeRateService exchangeRateService;
    @Resource
    private ISaleCenterClient feignSaleService;
    @Resource
    private IInstitutionCenterClient institutionService;
    @Resource
    private InvoiceTargetMapper invoiceTargetMapper;
    @Resource
    private InvoiceTargetServiceImpl invoiceTargetService;
    @Resource
    private AsyncReminderService asyncReminderService;


    @Override
    public InvoiceVo findInvoiceById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Invoice invoice = invoiceMapper.selectById(id);
        if (GeneralTool.isEmpty(invoice)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InvoiceVo invoiceVo = BeanCopyUtils.objClone(invoice, InvoiceVo::new);
        //111111
//        invoiceDto.setInstitutionProviderName(institutionCenterClient.getInstitutionProviderName(invoiceDto.getFkInstitutionProviderId()).getData());
        invoiceVo.setCompanyName(permissionCenterClient.getCompanyNameById(invoiceVo.getFkCompanyId()).getData());
        invoiceVo.setCurrencyTypeName(currencyTypeService.getCurrencyNameByNum(invoiceVo.getFkCurrencyTypeNum()));
        invoiceVo.setFkCurrencyTypeNumOrcName(currencyTypeService.getCurrencyNameByNum(invoiceVo.getFkCurrencyTypeNumOrc()));
        invoiceVo.setFkCurrencyTypeNumReceiptName(currencyTypeService.getCurrencyNameByNum(invoiceVo.getFkCurrencyTypeNumReceipt()));
        if (GeneralTool.isNotEmpty(invoiceVo.getFkTypeKey())) {
            invoiceVo.setFkTypeKeyName(ProjectKeyEnum.getInitialValue(invoiceVo.getFkTypeKey()));
        }
        /*
          因为发票的目标对象为多个,可以拥有多个渠道 中间表为 r_invoice_type_key_target
         */
        //对应记录ids
        List<InvoiceTarget> invoiceTargets = invoiceTargetMapper.getTargetIdAndTypeKeyByInvoiceId(invoice.getId());
        Set<Long> fkTypeTargetIds = invoiceTargets.stream().map(InvoiceTarget::getFkTypeTargetId).collect(Collectors.toSet());
        //根据提供商ids获取名称map
        Map<Long, String> institutionProviderNamesByIds = new HashMap<>();
        //根据渠道ids获取名称map
        Map<Long, String> channelNamesByIds = new HashMap<>();
        Map<Long, String> institutionChannelNamesByIds = new HashMap<>();
        Map<Long, String> businessProviderByIds = new HashMap<>();
        Map<Long, String> studentByIds = new HashMap<>();
        // 留学保险提供商
        Map<Long, String> insuranceProviderNameMap = new HashMap<>();

        //fkTypeTargetIds为目标对象id，拿目标ids查询对应的例如渠道名称
        if (GeneralTool.isNotEmpty(fkTypeTargetIds)) {
            Result<Map<Long, String>> namesByIds = feignSaleService.getChannelNamesByIds(fkTypeTargetIds);
            if (namesByIds.isSuccess()) {
                channelNamesByIds = namesByIds.getData();
            }
            //111111
            Result<Map<Long, String>> resultProviderNamesByIds = institutionService.getInstitutionProviderNamesByIds(fkTypeTargetIds);
            if (resultProviderNamesByIds.isSuccess()) {
                institutionProviderNamesByIds = resultProviderNamesByIds.getData();
            }
            Result<Map<Long, String>> channelByIds = institutionService.getChannelByIds(fkTypeTargetIds);
            if (channelByIds.isSuccess()) {
                institutionChannelNamesByIds = channelByIds.getData();
            }
            Result<Map<Long, String>> accommodationProvider = feignSaleService.getAStudyAccommodationProvider(fkTypeTargetIds);
            if (accommodationProvider.isSuccess() && accommodationProvider.getData() != null) {
                businessProviderByIds = accommodationProvider.getData();
            }
            Result<Map<Long, String>> studentNameByIds = saleCenterClient.getStudentNameByIds(fkTypeTargetIds);
            if (studentNameByIds.isSuccess() && studentNameByIds.getData() != null) {
                studentByIds = studentNameByIds.getData();
            }
            Result<Map<Long, String>> result = saleCenterClient.getInsuranceProviderNameByIds(fkTypeTargetIds);
            if (result.isSuccess() && result.getData() != null) {
                insuranceProviderNameMap = result.getData();
            }
        }
        //封装详情目标类型参数
        if (GeneralTool.isNotEmpty(invoiceTargets)) {
//            String targetName = null;
            StringBuilder builder = new StringBuilder();
            String typeKey;
            Long targetId;
            invoiceVo.setFkTypeTargetIds(invoiceTargets.stream().map(InvoiceTarget::getFkTypeTargetId).collect(Collectors.toSet()));
            for (InvoiceTarget target : invoiceTargets) {
                typeKey = target.getFkTypeKey();
                targetId = target.getFkTypeTargetId();
                if (TableEnum.INSTITUTION_PROVIDER.key.equals(typeKey)) {
                    builder.append(",").append(institutionProviderNamesByIds.get(targetId));
                } else if (TableEnum.BUSINESS_CHANNEL_INS.key.equals(typeKey)
                        || TableEnum.BUSINESS_CHANNEL_ACC.key.equals(typeKey)) {
                    builder.append(",").append(channelNamesByIds.get(targetId));
                } else if (ProjectKeyEnum.INSTITUTION_PROVIDER_CHANNEL.key.equals(typeKey)) {
                    builder.append(",").append(institutionChannelNamesByIds.get(targetId));
                } else if (ProjectKeyEnum.BUSINESS_PROVIDER.key.equals(typeKey)) {
                    builder.append(",").append(businessProviderByIds.get(targetId));
                }else if (ProjectKeyEnum.M_STUDENT.key.equals(typeKey)){
                    builder.append(",").append(studentByIds.get(targetId));
                } else if (ProjectKeyEnum.BUSINESS_PROVIDER_INS.key.equals(typeKey)) {
                    builder.append(",").append(insuranceProviderNameMap.get(targetId));
                }
            }
//            if (TableEnum.INSTITUTION_PROVIDER.key.equals(typeKey)) {
//                targetName = institutionProviderNamesByIds.get(targetId);
//            } else if (TableEnum.BUSINESS_CHANNEL_INS.key.equals(typeKey)
//                    || TableEnum.BUSINESS_CHANNEL_ACC.key.equals(typeKey)) {
//                targetName = channelNamesByIds.get(targetId);
//            } else if (ProjectKeyEnum.INSTITUTION_PROVIDER_CHANNEL.key.equals(typeKey)) {
//                targetName = institutionChannelNamesByIds.get(targetId);
//            }
            invoiceVo.setTargetName(builder.toString().replaceFirst(",", ""));
        }
//        subtotal
        //查询发票与应收计划的绑定关系list
        List<InvoiceReceivablePlan> invoiceReceivablePlans = invoiceReceivablePlanMapper.getReceivablePlanByInvoiceId(invoiceVo.getId());


        BigDecimal subtotal = BigDecimal.ZERO;
        if (GeneralTool.isNotEmpty(invoiceReceivablePlans)) {
            Set<Long> planIdset = invoiceReceivablePlans.stream().map(InvoiceReceivablePlan::getFkReceivablePlanId).collect(Collectors.toSet());
            if (GeneralTool.isEmpty(planIdset)) {
                planIdset.add(0L);
            }
//            Map<Long, BigDecimal> planMap = invoiceReceivablePlans.stream().collect(Collectors.toMap(InvoiceReceivablePlan::getFkReceivablePlanId,
//                    i->{
//                if (GeneralTool.isEmpty(i.getAmount())){
//                    return BigDecimal.ZERO;
//                }else {
//                    return i.getAmount();
//                }
//                }));

            Map<Long, List<InvoiceReceivablePlan>> planMap = invoiceReceivablePlans.stream().collect(Collectors.groupingBy(InvoiceReceivablePlan::getFkReceivablePlanId));

            Result<List<ReceivablePlanVo>> result = saleCenterClient.getReceivablePlansDetailNew(planIdset);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                List<ReceivablePlanVo> data = result.getData();
                for (ReceivablePlanVo receivablePlanVo : data) {
                    ExchangeRateVo exchangeRate = exchangeRateService.getLastExchangeRate(false, receivablePlanVo.getFkCurrencyTypeNum(), invoiceVo.getFkCurrencyTypeNum());
                    if (GeneralTool.isNotEmpty(planMap) && GeneralTool.isNotEmpty(planMap.get(receivablePlanVo.getId()))) {

                        List<InvoiceReceivablePlan> invoiceReceivablePlanList = planMap.get(receivablePlanVo.getId());
                        for (InvoiceReceivablePlan invoiceReceivablePlan : invoiceReceivablePlanList) {
                            subtotal = subtotal
                                    .add(exchangeRate.getExchangeRate().multiply(invoiceReceivablePlan.getAmount() == null ? BigDecimal.ZERO : invoiceReceivablePlan.getAmount()))
                                    .setScale(2, RoundingMode.HALF_UP);
                        }
//                        subtotal = subtotal
//                                .add(exchangeRate.getExchangeRate().multiply(planMap.get(receivablePlanVo.getId())== null?BigDecimal.ZERO:planMap.get(receivablePlanVo.getId())))
//                                .setScale(2, RoundingMode.HALF_UP);
                    }
                }
            }
        }
        invoiceVo.setSubtotal(subtotal);
        //获取发票绑定的最新的一个收款单
        Long receiptFormId = receiptFormInvoiceMapper.getLastReceiptFormIdByInvoiceId(id);
        invoiceVo.setReceiptFormId(receiptFormId);
        return invoiceVo;
    }

    /**
     * 编辑申请佣金摘要并通知邮件
     * @param invoiceId
     * @param receivablePlanId
     * @param remark
     * @return
     */
    @Override
    public SaveResponseBo editCommissionNotice(Long invoiceId, Long receivablePlanId, String remark) {
        //查询发票与应收计划
        InvoiceReceivablePlan invoiceReceivablePlan = invoiceReceivablePlanMapper.selectOne(Wrappers.<InvoiceReceivablePlan>lambdaQuery().eq(InvoiceReceivablePlan::getFkInvoiceId, invoiceId).eq(InvoiceReceivablePlan::getFkReceivablePlanId, receivablePlanId));
        if (GeneralTool.isNotEmpty(invoiceReceivablePlan)) {
            //更新应收发票绑定的摘要
            invoiceReceivablePlan.setCommissionNotice(remark);
            utilService.setUpdateInfo(invoiceReceivablePlan);
            invoiceReceivablePlanMapper.updateById(invoiceReceivablePlan);
            ReceivablePlan receivablePlan = invoiceReceivablePlanMapper.getReceivablePlanById(receivablePlanId);
            if (GeneralTool.isNotEmpty(receivablePlan)) {
                //应收为学习计划类型就发送邮件
                if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(receivablePlan.getFkTypeKey())) {
                    StudentOfferItem offerItem = saleCenterClient.getStudentOfferItemById(receivablePlan.getFkTypeTargetId()).getData();
                    if (!offerItem.getIsFollowHidden()) {
                        asyncReminderService.sendInvoiceCommissionEmail(offerItem, remark, RequestHeaderHandler.getHeaderMap());
                    }
                }
            }
        }
        return SaveResponseBo.ok(invoiceId);
    }

    /**
     * Author Cream
     * Description : //批量修改绑定金额
     * Date 2023/2/20 17:27
     * Params:
     * Return
     */
    @Override
    public SaveResponseBo batchUpdateBindingAmount(BatchUpdateBindingAmountDto batchUpdateBindingAmountDto) {
        Long invoiceId = batchUpdateBindingAmountDto.getInvoiceId();
        List<Long> receivableIds = batchUpdateBindingAmountDto.getReceivableIds();
        BigDecimal exchangeRate = batchUpdateBindingAmountDto.getExchangeRate();
        //查询发票与应收的绑定关系
        List<InvoiceReceivablePlan> invoiceReceivablePlans = invoiceReceivablePlanMapper.selectList(Wrappers.<InvoiceReceivablePlan>lambdaQuery().eq(InvoiceReceivablePlan::getFkInvoiceId, invoiceId).in(InvoiceReceivablePlan::getFkReceivablePlanId, receivableIds));
        for (InvoiceReceivablePlan receivablePlan : invoiceReceivablePlans) {
            BigDecimal amount = DataConverter.bigDecimalNullConvert(receivablePlan.getAmount());
            //计算新的发票绑定金额
            receivablePlan.setAmount(amount.multiply(exchangeRate).setScale(3,RoundingMode.UP));
        }
        //更新发票绑定金额
        if (GeneralTool.isNotEmpty(invoiceReceivablePlans)) {
            invoiceReceivablePlanService.updateBatchById(invoiceReceivablePlans);
        }
        return SaveResponseBo.ok(invoiceId);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public Long addInvoice(InvoiceDto invoiceDto) {
        if (GeneralTool.isEmpty(invoiceDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //申明目标对象list
        List<InvoiceTarget> list = new ArrayList<>();
        Set<Long> targetIds = invoiceDto.getFkTypeTargetIds();
        invoiceDto.setStatus(1);
        if (GeneralTool.isNotEmpty(invoiceDto.getNum()) && invoiceDto.getNum().contains("&amp;")) {
            invoiceDto.setNum(invoiceDto.getNum().replace("&amp;", "&"));
        }
        Invoice invoice = BeanCopyUtils.objClone(invoiceDto, Invoice::new);
        validateUpdate(invoiceDto);
        utilService.updateUserInfoToEntity(invoice);
        int i = invoiceMapper.insert(invoice);
        InvoiceTarget invoiceTarget;
        //初始化发票与目标对象绑定关系的list
        for (Long targetId : targetIds) {
            invoiceTarget = new InvoiceTarget();
            invoiceTarget.setFkInvoiceId(invoice.getId())
                    .setFkTypeKey(invoiceDto.getFkTypeKey())
                    .setFkTypeTargetId(targetId);
            utilService.setCreateInfo(invoiceTarget);
            list.add(invoiceTarget);
        }
        //将发票和多个目标对象mapping保存到数据库
        if (GeneralTool.isNotEmpty(list)) {
            invoiceTargetService.saveBatch(list);
        }
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        //如果有传应收计划ids则同时绑定关系表
        List<InvoiceReceivablePlan> param = new ArrayList<>();
        if (GeneralTool.isNotEmpty(invoiceDto.getFkReceivablePlanIds())) {
            //删除原绑定关系
            //invoiceReceivablePlanMapper.deleteInvoiceReceivablePlan(invoiceVo.getFkReceivablePlanIds());
            for (Long fkReceivablePlanId : invoiceDto.getFkReceivablePlanIds()) {
                InvoiceReceivablePlan invoiceReceivablePlan = new InvoiceReceivablePlan();
                invoiceReceivablePlan.setFkInvoiceId(invoice.getId());
                invoiceReceivablePlan.setFkReceivablePlanId(fkReceivablePlanId);
                utilService.updateUserInfoToEntity(invoiceReceivablePlan);
                param.add(invoiceReceivablePlan);
            }
            if (GeneralTool.isNotEmpty(param)) {
                invoiceReceivablePlanService.saveBatch(param);
            }
        }
        return invoice.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (invoiceMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //删除关联附件
        deleteService.deleteMedia(id, TableEnum.FINANCE_INVOICES.key);
        int i = invoiceMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        invoiceTargetMapper.delete(Wrappers.<InvoiceTarget>lambdaQuery().eq(InvoiceTarget::getFkInvoiceId, id));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public InvoiceVo updateInvoice(InvoiceDto invoiceDto) {
        if (invoiceDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Invoice result = invoiceMapper.selectById(invoiceDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (GeneralTool.isNotEmpty(invoiceDto.getNum()) && invoiceDto.getNum().contains("&amp;")) {
            invoiceDto.setNum(invoiceDto.getNum().replace("&amp;", "&"));
        }
        Invoice invoice = BeanCopyUtils.objClone(invoiceDto, Invoice::new);
        validateUpdate(invoiceDto);
        utilService.updateUserInfoToEntity(invoice);
        int i = invoiceMapper.updateById(invoice);
        //删除被更新的发票与目标对象绑定关系
        invoiceTargetMapper.delete(Wrappers.<InvoiceTarget>lambdaQuery().eq(InvoiceTarget::getFkInvoiceId, invoiceDto.getId()));
        InvoiceTarget invoiceTarget;
        Set<Long> targetIds = invoiceDto.getFkTypeTargetIds();
        List<InvoiceTarget> list = new ArrayList<>();
        UserInfo user = GetAuthInfo.getUser();
        //设置发票的目标对象
        for (Long targetId : targetIds) {
            invoiceTarget = new InvoiceTarget();
            invoiceTarget.setFkInvoiceId(invoiceDto.getId())
                    .setFkTypeKey(invoiceDto.getFkTypeKey())
                    .setFkTypeTargetId(targetId);
            invoiceTarget.setGmtCreate(new Date());
            invoiceTarget.setGmtCreateUser(user.getLoginId());
            invoiceTarget.setGmtModified(new Date());
            invoiceTarget.setGmtModifiedUser(user.getLoginId());
            list.add(invoiceTarget);
        }
        //更新目标对象
        if (GeneralTool.isNotEmpty(list)) {
            invoiceTargetService.saveBatch(list);
        }
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        return findInvoiceById(invoiceDto.getId());
    }

    @Override
    public List<InvoiceVo> getInvoices(InvoiceQueryDto invoiceVo, Page page) {
        if (GeneralTool.isNotEmpty(invoiceVo)) {
            //查询条件-所属公司
            if (GeneralTool.isNotEmpty(invoiceVo.getFkCompanyId())) {
                if (!SecureUtil.validateCompany(invoiceVo.getFkCompanyId())) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
                }
            }
            //查询条件-所属公司ids
            if (GeneralTool.isNotEmpty(invoiceVo.getFkCompanyIds())) {
                if (!SecureUtil.validateCompanys(invoiceVo.getFkCompanyIds())) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
                }
            }else {
                invoiceVo.setFkCompanyIds(SecureUtil.getCompanyIdsByStaffId(GetAuthInfo.getStaffId()));
            }

            if (GeneralTool.isNotEmpty(invoiceVo.getStudentName())){
                invoiceVo.setStudentName(invoiceVo.getStudentName().replace(" ", "").trim());
            }
        }
        List<Invoice> invoices = new ArrayList<>();
        if (page == null){
            invoices = invoiceMapper.getInvoiceList(null, invoiceVo);
        }else {
            IPage<Invoice> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())));
            invoices = invoiceMapper.getInvoiceList(pages, invoiceVo);
            page.setAll(Integer.parseInt(String.valueOf(pages.getTotal())));
            page.setCurrentResult(Integer.parseInt(String.valueOf(pages.getSize())));
        }

        if (GeneralTool.isEmpty(invoices)) {
            return Collections.emptyList();
        }


        List<InvoiceVo> convertDatas = new ArrayList<>();
        //学校提供商id集合
        //币种编号集合
        Set<String> currencyTypeNums = new HashSet<>();
        //所属公司id集合
        Set<Long> companyIds = new HashSet<>();
        //获取各自集合的值
        for (Invoice invoice : invoices) {
            currencyTypeNums.add(invoice.getFkCurrencyTypeNum());
            companyIds.add(invoice.getFkCompanyId());
        }
        //对应记录ids
        Set<Long> fkProviderTypeTargetIds = new HashSet<>();
        Set<Long> fkChannelTypeTargetIds = new HashSet<>();
        Set<Long> fkBusinessProviderTargetIds = new HashSet<>();
        Set<Long> fkInstitutionChannelTypeTargetIds = new HashSet<>();
        Set<Long> studentIds = new HashSet<>();
        Set<Long> businessProviderInsIds = new HashSet<>();
        /*
            主要逻辑变更为获取发票的目标对象ids，获取目标对象值，然后设置给发票进行回显
         */
        Set<Long> ids = invoices.stream().map(Invoice::getId).collect(Collectors.toSet());
        List<ReceiptFormInvoice> receiptFormInvoices = receiptFormInvoiceMapper.selectList(Wrappers.<ReceiptFormInvoice>lambdaQuery().in(ReceiptFormInvoice::getFkInvoiceId, ids));
        List<Long> reIds = receiptFormInvoices.stream().map(ReceiptFormInvoice::getFkInvoiceId).collect(Collectors.toList());
        List<InvoiceTarget> invoiceTargets = invoiceTargetMapper.getTargetIdAndTypeKeyByInvoiceIds(ids);
        String typeKey;
        Long targetId;
        for (InvoiceTarget invoiceTarget : invoiceTargets) {
            typeKey = invoiceTarget.getFkTypeKey();
            targetId = invoiceTarget.getFkTypeTargetId();
            if (TableEnum.INSTITUTION_PROVIDER.key.equals(typeKey)) {
                fkProviderTypeTargetIds.add(targetId);
            } else if (ProjectKeyEnum.INSTITUTION_PROVIDER_CHANNEL.key.equals(typeKey)) {
                fkInstitutionChannelTypeTargetIds.add(targetId);
            } else if (ProjectKeyEnum.BUSINESS_PROVIDER.key.equals(typeKey)) {
                fkBusinessProviderTargetIds.add(targetId);
            } else if(ProjectKeyEnum.M_STUDENT.key.equals(typeKey)){
                studentIds.add(targetId);
            } else if(ProjectKeyEnum.BUSINESS_PROVIDER_INS.key.equals(typeKey)) {
                businessProviderInsIds.add(targetId);
            } else {
                fkChannelTypeTargetIds.add(targetId);
            }
        }
        Map<String, String> currencyTypeNameMap = getCurrencyNameMap(currencyTypeNums);
        Map<Long, String> companyNameMap = getCompanyNameMap(companyIds);
        //根据提供商ids获取名称map
        Map<Long, String> institutionProviderNamesByIds = new HashMap<>();
        //根据渠道ids获取名称map
        Map<Long, String> channelNamesByIds = new HashMap<>();
        //根据提供商渠道ids获取名称map
        Map<Long, String> institutionProviderChannelNamesByIds = new HashMap<>();
        //根据留学住宿提供商ids获取名称map
        Map<Long, String> BusinessProviderNamesByIds = new HashMap<>();
        // 学生map
        Map<Long, String> studentNameMap = new HashMap<>();
        // 留学保险提供商
        Map<Long, String> insuranceProviderNameMap = new HashMap<>();

        if (GeneralTool.isNotEmpty(fkProviderTypeTargetIds)) {
            institutionProviderNamesByIds = institutionCenterClient.getInstitutionProviderNamesByIds(fkProviderTypeTargetIds).getData();
        }
        if (GeneralTool.isNotEmpty(fkChannelTypeTargetIds)) {
            channelNamesByIds = saleCenterClient.getChannelNamesByIds(fkChannelTypeTargetIds).getData();
        }
        if (GeneralTool.isNotEmpty(studentIds)) {
            studentNameMap = saleCenterClient.getStudentNameByIds(studentIds).getData();
        }
        //新添加学校渠道
        if (GeneralTool.isNotEmpty(fkInstitutionChannelTypeTargetIds)) {
            institutionProviderChannelNamesByIds = institutionCenterClient.getChannelByIds(fkInstitutionChannelTypeTargetIds).getData();
        }
        if (GeneralTool.isNotEmpty(fkBusinessProviderTargetIds)) {
            BusinessProviderNamesByIds = saleCenterClient.getAStudyAccommodationProvider(fkBusinessProviderTargetIds).getData();
        }
        if (GeneralTool.isNotEmpty(businessProviderInsIds)) {
            insuranceProviderNameMap = saleCenterClient.getInsuranceProviderNameByIds(businessProviderInsIds).getData();
        }
        Map<Long, List<InvoiceTarget>> map = invoiceTargets.stream().collect(Collectors.groupingBy(InvoiceTarget::getFkInvoiceId));
        List<InvoiceTarget> targetIds;
        StringBuilder builder;

        //OTHER 为除了IAE的其他公司，发票列表，迟收款高亮提醒：value1=发票创建时间（开始提醒时间），value2=迟收款多长时间提醒（天）
//        Result<ConfigVo> inTime = permissionCenterClient.getConfigByKey(ProjectKeyEnum.INVOICE_LIST_LATE_RECEIVE.key);
//        JSONObject jsonObject = JSONObject.parseObject(inTime.getData().getValue2());
//        String gea = jsonObject.getString("OTHER");
//        String iae = jsonObject.getString("IAE");
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.INVOICE_LIST_LATE_RECEIVE.key, 2).getData();

        for (Invoice invoice : invoices) {
            InvoiceVo invoiceDto = BeanCopyUtils.objClone(invoice, InvoiceVo::new);
            if (invoiceDto == null) {
                continue;
            }
            if (GeneralTool.isNotEmpty(invoiceDto.getFkTypeKey())) {
                invoiceDto.setFkTypeKeyName(ProjectKeyEnum.getValue(invoiceDto.getFkTypeKey()));
            }
            targetIds = map.get(invoiceDto.getId());
            if (GeneralTool.isNotEmpty(targetIds)) {
                invoiceDto.setFkTypeTargetIds(targetIds.stream().map(InvoiceTarget::getFkTypeTargetId).collect(Collectors.toSet()));
                builder = new StringBuilder();
                for (InvoiceTarget target : targetIds) {
                    typeKey = target.getFkTypeKey();
                    targetId = target.getFkTypeTargetId();
                    if (TableEnum.INSTITUTION_PROVIDER.key.equals(typeKey)) {
                        builder.append(",").append(institutionProviderNamesByIds.get(targetId));
                    } else if (TableEnum.BUSINESS_CHANNEL_INS.key.equals(typeKey)
                            || TableEnum.BUSINESS_CHANNEL_ACC.key.equals(typeKey)) {
                        builder.append(",").append(channelNamesByIds.get(targetId));
                    } else if (ProjectKeyEnum.INSTITUTION_PROVIDER_CHANNEL.key.equals(typeKey)) {
                        builder.append(",").append(institutionProviderChannelNamesByIds.get(targetId));
                    } else if (ProjectKeyEnum.BUSINESS_PROVIDER.key.equals(typeKey) && BusinessProviderNamesByIds != null) {
                        builder.append(",").append(BusinessProviderNamesByIds.get(targetId));
                    } else if (ProjectKeyEnum.M_STUDENT.key.equals(typeKey)){
                        builder.append(",").append(studentNameMap.get(targetId));
                    } else if (ProjectKeyEnum.BUSINESS_PROVIDER_INS.key.equals(typeKey)){
                        builder.append(",").append(insuranceProviderNameMap.get(targetId));
                    }
                }
                invoiceDto.setTargetName(builder.toString().replaceFirst(",", ""));
            }
            //feign调用返回的map 根据key-id获取对应value-名称 设置返回给前端
            invoiceDto.setCurrencyTypeName(currencyTypeNameMap.get(invoiceDto.getFkCurrencyTypeNum()));
            invoiceDto.setCompanyName(companyNameMap.get(invoiceDto.getFkCompanyId()));
            //reIds 是收款单和应收绑定的发票id集合
            /*
                时间判断若发票加上延迟时间在创建时间小于当前时间，且发票状态为激活状态。则标记为需要高亮显示
             */
            if (reIds.contains(invoiceDto.getId())) {
                invoiceDto.setIsAccount(true);
            }else {
                Date gmtCreate = invoiceDto.getGmtCreate();
//                if (invoice.getFkCompanyId()==2) {
//                    if (gmtCreate!=null) {
//                        LocalDateTime time = gmtCreate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().plusDays(Integer.parseInt(gea));
//                        invoiceDto.setMark(LocalDateTime.now().compareTo(time)>0 && invoiceDto.getStatus() == 1);
//                    }
//                }
//                if (invoice.getFkCompanyId()==3) {
//                    if (gmtCreate!=null) {
//                        LocalDateTime time = gmtCreate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().plusDays(Integer.parseInt(iae));
//                        invoiceDto.setMark(LocalDateTime.now().compareTo(time)>0 && invoiceDto.getStatus() == 1);
//                    }
//                }
                if (gmtCreate!=null) {
                    LocalDateTime time = gmtCreate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().plusDays(Integer.parseInt(companyConfigMap.get(invoice.getFkCompanyId())));
                    invoiceDto.setMark(LocalDateTime.now().compareTo(time)>0 && invoiceDto.getStatus() == 1);
                }
            }
            convertDatas.add(invoiceDto);
        }
        /*
         * 把需要高亮显示的发票排列到列表前面
         */
        List<InvoiceVo> first = convertDatas.stream().filter(InvoiceVo::getMark).sorted(((o1, o2) -> o2.getGmtCreate().compareTo(o1.getGmtCreate()))).collect(Collectors.toList());
        List<InvoiceVo> end = convertDatas.stream().filter(f->!f.getMark()).sorted(((o1, o2) -> o2.getGmtCreate().compareTo(o1.getGmtCreate()))).collect(Collectors.toList());
        first.addAll(end);
        return first;
    }

    /**
     * Author Cream
     * Description : // 获取发票与目标对象的绑定关系
     * Date 2023/9/8 13:40
     * Params:
     * Return
     */
    private void queryResult(List<Long> feignTargetId, List<InvoiceTarget> targets,String... typeKey) {
        if (feignTargetId == null) {
            return;
        }
        feignTargetId = feignTargetId.stream().filter(f -> f != 0).collect(Collectors.toList());
        if (feignTargetId.isEmpty()) {
            return;
        }
        List<InvoiceTarget> invoiceTargets = invoiceTargetMapper.selectList(Wrappers.<InvoiceTarget>lambdaQuery().in(InvoiceTarget::getFkTypeKey, typeKey)
                .in(InvoiceTarget::getFkTypeTargetId, feignTargetId));
        if (invoiceTargets.isEmpty()) {
            return;
        }
        targets.addAll(invoiceTargets);
    }

    @Override
    public List<FMediaAndAttachedVo> getItemMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.FINANCE_INVOICES.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);

    }

    @Override
    public List<FMediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<FMediaAndAttachedVo> mediaAndAttachedDtos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.FINANCE_INVOICES.key);
            mediaAndAttachedDtos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedDtos;
    }

    /**
     * Author Cream
     * Description : //收款单和发票绑定关系作废
     * Date 2023/9/8 13:41
     * Params:  1代表绑定   0代表作废
     * Return
     */
    @Override
    public void isCancel(Long invoiceId, Integer status) {
        if (GeneralTool.isEmpty(invoiceId) || GeneralTool.isEmpty(invoiceMapper.selectById(invoiceId))) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        switch (status) {
            case 1:
                break;
            case 0:
                List<ReceiptFormInvoice> receiptFormInvoices = receiptFormInvoiceMapper.selectList(Wrappers.<ReceiptFormInvoice>lambdaQuery().eq(ReceiptFormInvoice::getFkInvoiceId, invoiceId));
                if (!receiptFormInvoices.isEmpty()) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("the_invoice_has_been_bound_to_the_payment_receipt"));
                }
                //删除发票与应收映射关联
                invoiceReceivablePlanMapper.deleteInvoiceReceivablePlanByInvoiceId(invoiceId);
                break;
            default:
                throw new GetServiceException(LocaleMessageUtils.getMessage("illegal_state"));
        }
        Invoice invoice = new Invoice();
        invoice.setId(invoiceId);
        invoice.setStatus(status);
        utilService.updateUserInfoToEntity(invoice);
        invoiceMapper.updateById(invoice);
    }

    @Override
    public List<BaseSelectEntity> getInvoiceSelect() {
//        Example example = new Example(Invoice.class);
//        List<Invoice> invoices = invoiceMapper.selectByExample(example);
        List<Invoice> invoices = this.list();
        List<BaseSelectEntity> baseSelectEntitys = new ArrayList<>();
        for (Invoice invoice : invoices) {
            BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
            String num = invoice.getNum();
//            if (GeneralTool.isNotEmpty(invoice.getFkInstitutionProviderId())) {
//                //111111
////                String providerName = institutionService.getInstitutionProviderName(invoice.getFkInstitutionProviderId());
////                num = num + "(" + providerName + ")";
//            }
            baseSelectEntity.setId(invoice.getId());
            baseSelectEntity.setName(num);
            baseSelectEntitys.add(baseSelectEntity);
        }
        return baseSelectEntitys;
    }

    @Override
    public Map<Long, String> getAllNums() {
//        Map<Long,String> map = new HashMap<>();
//        Example example = new Example(Invoice.class);
//        List<Invoice> invoices = invoiceMapper.selectByExample(example);
        Map<Long, String> map = new HashMap<>();
        List<Invoice> invoices = this.list();
        if (GeneralTool.isEmpty(invoices)) {
            return map;
        }
        for (Invoice invoice : invoices) {
            map.put(invoice.getId(), invoice.getNum());
        }
        return map;
    }

//    /**
//     * @return java.util.List<java.lang.Long>
//     * @Description :获取登录人对应所有公司id集合
//     * @Param []
//     * <AUTHOR>
//     */
//    private List<Long> getCompanyIds() {
//        List<Long> companyIds = StaffContext.getStaff().getCompanyIds();
//        if (GeneralTool.isEmpty(companyIds)) {
//            companyIds.add(0L);
//        }
//        return companyIds;
//    }

    /**
     * @return java.util.Map<java.lang.Long, java.lang.String>
     * @Description :feign调用一次 查出全部对应公司名称
     * @Param [companyIds]
     * <AUTHOR>
     */
    private Map<Long, String> getCompanyNameMap(Set<Long> companyIds) {
        companyIds.removeIf(Objects::isNull);
        Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(companyIds);
        if (result.isSuccess() && result.getData() != null) {
            return result.getData();
        }
        return new HashMap<>();
    }

    /**
     * @return java.util.Map<java.lang.String, java.lang.String>
     * @Description :feign调用一次查出全部对应币种名称
     * @Param [events]
     * <AUTHOR>
     */
    private Map<String, String> getCurrencyNameMap(Set<String> currencyTypeNums) {
        currencyTypeNums.removeIf(Objects::isNull);
        //调用一次查出全部对应币种名称
        return currencyTypeService.getCurrencyNamesByNums(currencyTypeNums);
    }

    /**
     * 根据发票ID获取应收列表
     *
     * @param data
     * @param page
     * @return
     */
    @Override
    public List<ReceivablePlanVo> getReceivablePlans(InvoiceReceivablePlanDto data, Page page) {
        if (GeneralTool.isEmpty(data.getFkInvoiceId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        long startTime = System.currentTimeMillis();
        //去掉分页
        IPage<Long> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
//        List<Long> fkReceivablePlanIds = invoiceMapper.getReceivablePlanIds(pages, data.getFkInvoiceId());
        //通过发票查询应收计划
        List<ReceivablePlanVo> plan = invoiceMapper.getReceivablePlanByInvoice(pages, data.getFkInvoiceId());
        //封装应收计划的回显值，学生信息，实收信息等
        Result<List<ReceivablePlanVo>> planResult = saleCenterClient.packReceivablePlanResult(plan);

        page.setAll((int) pages.getTotal());

        long getReceivablePlanIdsTime = System.currentTimeMillis();
        List<ReceivablePlanVo> receivablePlanVos = new ArrayList<>();
        if (planResult.isSuccess() && GeneralTool.isNotEmpty(planResult.getData())) {
            receivablePlanVos = planResult.getData();
        }
        long getReceivablePlanDtosByIdsTime = System.currentTimeMillis();
        Set<String> nums = receivablePlanVos.stream().filter(r -> GeneralTool.isEmpty(r.getFkCurrencyTypeName()) && GeneralTool.isNotEmpty(r.getFkCurrencyTypeNum()))
                .map(ReceivablePlanVo::getFkCurrencyTypeNum).collect(Collectors.toSet());

        Map<String, String> currencyNamesByNums = currencyTypeService.getCurrencyNamesByNums(nums);
        for (ReceivablePlanVo receivablePlanVo : receivablePlanVos) {
            if (GeneralTool.isEmpty(receivablePlanVo.getFkCurrencyTypeName())) {
                if (GeneralTool.isNotEmpty(currencyNamesByNums) && GeneralTool.isNotEmpty(receivablePlanVo.getFkCurrencyTypeNum())
                        && GeneralTool.isNotEmpty(currencyNamesByNums.get(receivablePlanVo.getFkCurrencyTypeNum()))) {
                    receivablePlanVo.setFkCurrencyTypeName(currencyNamesByNums.get(receivablePlanVo.getFkCurrencyTypeNum()));
                }
            }
        }
        long endTime = System.currentTimeMillis();
        log.info("====>发票应收计划列表：getReceivablePlanIds用时{}", (getReceivablePlanIdsTime - startTime));
        log.info("====>发票应收计划列表：getReceivablePlanDtosByIds用时{}", (getReceivablePlanDtosByIdsTime - getReceivablePlanIdsTime));
        log.info("====>发票应收计划列表：selectList用时{}", (endTime - getReceivablePlanDtosByIdsTime));

        return receivablePlanVos;
    }

    @Override
    public List<Long> doGetReceivablePlanIds(Long invoiceId) {
        if (GeneralTool.isEmpty(invoiceId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("invoiceId is null"));
        }
        return invoiceMapper.getReceivablePlanIds(null, invoiceId);
    }

    @Override
    public Map<Long, String> getFkInvoiceNum(Set<Long> palnIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(palnIds)) {
            return map;
        }
        List<InvoiceReceivablePlanVo> invoiceReceivablePlanVos = invoiceMapper.getFkInvoiceNum(palnIds);
        if (GeneralTool.isEmpty(invoiceReceivablePlanVos)) {
            return map;
        }

        for (InvoiceReceivablePlanVo invoiceReceivablePlanVo : invoiceReceivablePlanVos) {
            //应收计划支持多发票绑定
            if (GeneralTool.isEmpty(map.get(invoiceReceivablePlanVo.getFkReceivablePlanId()))) {
                map.put(invoiceReceivablePlanVo.getFkReceivablePlanId(), invoiceReceivablePlanVo.getFkInvoiceNum());
            } else {
                String fkReceivablePlanIdStr = map.get(invoiceReceivablePlanVo.getFkReceivablePlanId());
                fkReceivablePlanIdStr = fkReceivablePlanIdStr.concat("," + invoiceReceivablePlanVo.getFkInvoiceNum());
                map.put(invoiceReceivablePlanVo.getFkReceivablePlanId(), fkReceivablePlanIdStr);
            }
            //map.put(invoiceReceivablePlanDto.getFkReceivablePlanId(), invoiceReceivablePlanDto.getFkInvoiceNum());
        }
        return map;
    }

    @Override
    public List<InvoiceSelectVo> getInvoiceSelectByTargetId(Long fkTypeTargetId, String fkTypeKey, Long fkReceiptFormId) {
        return invoiceMapper.getInvoiceSelectByTargetId(fkTypeTargetId, fkTypeKey, fkReceiptFormId);
    }

    /**
     * Author Cream
     * Description : 获取收款单发票下拉列表
     * Date 2022/4/22 19:34
     */
    @Override
    public List<InvoiceSelectVo> doGetInvoiceSelectList(InvoiceReceiptFormDto invoiceReceiptFormDto) {

        return invoiceMapper.getInvoiceSelect(invoiceReceiptFormDto);
    }

    /*
        初始化目标对象权重,目的为了排序
        详见禅道 https://zentao.geteducation.net/zentao/bug-view-1997.html
        比如已勾选：
            GEA/AAAA学校
            GEA/BBBB学校
            IDP/CCCC学校

            发票排序结果应为
            GEA发票1
            GEA发票2
            IDP发票3
            AAA学校发票5
            BBB学校发票6
            CCC学校发票7

           学校渠道>留学保险和住宿渠道>学校提供商>业务提供商
     */
    private final Map<String, BigDecimal> insMap = new HashMap<String, BigDecimal>(3) {{
        put(TableEnum.BUSINESS_CHANNEL_INS.key, BigDecimal.valueOf(0.8));
        put(TableEnum.BUSINESS_CHANNEL_ACC.key, BigDecimal.valueOf(0.8));
        put(TableEnum.INSTITUTION_PROVIDER_CHANNEL.key, BigDecimal.valueOf(1));
    }};
    private final Map<String, BigDecimal> proMap = new HashMap<String, BigDecimal>(1) {{
        put(TableEnum.INSTITUTION_PROVIDER.key, BigDecimal.valueOf(0.5));
        put(ProjectKeyEnum.BUSINESS_PROVIDER.key, BigDecimal.valueOf(0.5));
    }};

    /**
     * 获取发票下拉应收列表用到
     * @param receivableInvoiceQueryDto
     * @return
     */
    @Override
    public List<BaseSelectEntity> doGetAllInvoice(ReceivableInvoiceQueryDto receivableInvoiceQueryDto) {
        List<Long> planIds = receivableInvoiceQueryDto.getPlanIds();
        if (GeneralTool.isEmpty(planIds)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //根据应收ids获取应收计划
        Result<List<ReceivablePlanVo>> result = saleCenterClient.getReceivablePlansDetail(new HashSet<>(planIds));
        List<ReceivablePlanVo> data = result.getData();
        if (result.isSuccess() && data != null) {
            //初始化渠道和提供商的权重为0
            BigDecimal zero = BigDecimal.ZERO;
            BigDecimal insW = zero, proW = zero;
            Map<String, List<ReceivablePlanVo>> listMap = data.stream().collect(Collectors.groupingBy(ReceivablePlanVo::getFkTypeKey));
            List<InvoiceTarget> targets = new ArrayList<>();
            //满足这些条件的将权重拉升到最大值
            if (listMap.containsKey(ProjectKeyEnum.M_STUDENT_INSURANCE.key)
                    || listMap.containsKey(ProjectKeyEnum.M_STUDENT_ACCOMMODATION.key)
                    || listMap.containsKey(ProjectKeyEnum.BUSINESS_CHANNEL_INS.key)
                    || listMap.containsKey(ProjectKeyEnum.BUSINESS_CHANNEL_ACC.key)
                    || listMap.containsKey(TableEnum.SALE_STUDENT_SERVICE_FEE.key)
                    || listMap.containsKey(TableEnum.INSTITUTION_PROVIDER_CHANNEL.key)) {
                insW = BigDecimal.valueOf(1000000);
            }
            //提供商权限设置5
            if (listMap.containsKey(TableEnum.INSTITUTION_PROVIDER.key)
                    || listMap.containsKey(ProjectKeyEnum.BUSINESS_PROVIDER.key)) {
                proW = BigDecimal.valueOf(5);
            }
            //学习计划设置为4
            if (listMap.containsKey(TableEnum.SALE_STUDENT_OFFER_ITEM.key)) {
                insW = BigDecimal.valueOf(1000000);
                proW = BigDecimal.valueOf(4);
            }
            String key;
            Map<String, Set<Long>> params = new HashMap<>();
            /*
                queryResult是获取发票与目标对象的绑定信息。
             */
            for (Map.Entry<String, List<ReceivablePlanVo>> entry : listMap.entrySet()) {
                key = entry.getKey();
                List<Long> ids = entry.getValue().stream().map(ReceivablePlanVo::getFkTypeTargetId).collect(Collectors.toList());
                if (ProjectKeyEnum.M_STUDENT_INSURANCE.key.equals(key)) {
                    params.put(ProjectKeyEnum.M_STUDENT_INSURANCE.key, new HashSet<>(ids));
                    Result<Set<Long>> idResult = saleCenterClient.getBusinessId(params);
                    if (idResult.isSuccess() && idResult.getData() != null) {
                        queryResult(new ArrayList<>(idResult.getData()), targets, TableEnum.BUSINESS_CHANNEL_INS.key);
                    }
                } else if (ProjectKeyEnum.M_STUDENT_ACCOMMODATION.key.equals(key)) {
                    params.put(ProjectKeyEnum.M_STUDENT_ACCOMMODATION.key, new HashSet<>(ids));
                    Result<Set<Long>> idResult = saleCenterClient.getBusinessId(params);
                    if (idResult.isSuccess() && idResult.getData() != null) {
                        queryResult(new ArrayList<>(idResult.getData()), targets, TableEnum.BUSINESS_CHANNEL_ACC.key);
                        Result<Set<Long>> providerIdByAccIds = saleCenterClient.getBusinessProviderIdByAccIds(idResult.getData());
                        if (providerIdByAccIds.isSuccess() && providerIdByAccIds.getData() != null) {
                            queryResult(new ArrayList<>(providerIdByAccIds.getData()), targets, ProjectKeyEnum.BUSINESS_PROVIDER.key);
                        }
                    }
                } else if (ProjectKeyEnum.BUSINESS_CHANNEL_INS.key.equals(key)) {
                    queryResult(ids, targets, TableEnum.BUSINESS_CHANNEL_INS.key);
                }else if (TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(key)) {
//                    ids = saleCenterClient.getServiceFeeStudentIdsByIds(ids);
                    ids = saleCenterClient.getServiceFeeProviderIdsByFeeIds(ids).getData();
                    queryResult(ids, targets, ProjectKeyEnum.INSTITUTION_PROVIDER.key);
                } else if (ProjectKeyEnum.BUSINESS_CHANNEL_ACC.key.equals(key)) {
                    queryResult(ids, targets, TableEnum.BUSINESS_CHANNEL_ACC.key);
                } else if (TableEnum.INSTITUTION_PROVIDER_CHANNEL.key.equals(key)) {
                    queryResult(ids, targets, TableEnum.INSTITUTION_PROVIDER_CHANNEL.key);
                } else if (TableEnum.INSTITUTION_PROVIDER.key.equals(key)) {
                    queryResult(ids, targets, TableEnum.INSTITUTION_PROVIDER.key);
                } else if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(key)) {
                    Result<Map<String,Set<Long>>> pcIds = saleCenterClient.getInChannelAndProviderIds(new HashSet<>(ids));
                    if (pcIds.isSuccess() && pcIds.getData()!=null) {
                        queryResult(new ArrayList<>(pcIds.getData().get(TableEnum.INSTITUTION_PROVIDER.key)), targets, TableEnum.INSTITUTION_PROVIDER.key);
                        queryResult(new ArrayList<>(pcIds.getData().get(TableEnum.INSTITUTION_PROVIDER_CHANNEL.key)), targets, TableEnum.INSTITUTION_PROVIDER_CHANNEL.key);
                    }
                }
            }
            Set<Long> invoiceIds = targets.stream().map(InvoiceTarget::getFkInvoiceId).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(invoiceIds)) {
                return Collections.emptyList();
            }
            List<InvoiceSelectVo> allInvoice = invoiceMapper.getAllInvoice(invoiceIds, receivableInvoiceQueryDto.getFkCompanyId());
            BigDecimal remainder = BigDecimal.valueOf(*********);
            BigDecimal divide;
            /*
                遍历发票从初始化map拿出初始化权重+后期分配权重。再进行由大到小排序
             */
            for (InvoiceSelectVo dto : allInvoice) {
                key = dto.getFkTypeKey();
                if (key == null) {
                    continue;
                }
                divide = BigDecimal.valueOf(dto.getGmtCreate().getTime()).divide(remainder, 9, RoundingMode.UP);
                if (insMap.containsKey(key)) {
                    dto.setWeight(insMap.get(key).add(insW).add(divide));
                } else if (proMap.containsKey(key)) {
                    dto.setWeight(proMap.get(key).add(proW).add(divide));
                }
            }
            List<InvoiceSelectVo> re = allInvoice.stream().sorted(Comparator.comparing(InvoiceSelectVo::getWeight).reversed()).collect(Collectors.toList());
            if (GeneralTool.isNotEmpty(re)) {
                List<BaseSelectEntity> entities = new ArrayList<>();
                BaseSelectEntity baseSelectEntity;
                for (InvoiceSelectVo dto : re) {
                    baseSelectEntity = new BaseSelectEntity();
                    baseSelectEntity.setId(dto.getId());
                    baseSelectEntity.setName(dto.getNum());
                    entities.add(baseSelectEntity);
                }
                return entities;
            }
        }
        return Collections.emptyList();
    }

    @Override
    public List<Invoice> getInvoiceByIds(List<Long> ids) {
        if (ids.isEmpty()) {
            return Collections.emptyList();
        }
        return invoiceMapper.selectBatchIds(ids);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unBoundPlans(List<Long> ids) {
        if (GeneralTool.isEmpty(ids)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
//        if (GeneralTool.isEmpty(invoiceVo) || GeneralTool.isEmpty(invoiceVo.getId()) || GeneralTool.isEmpty(invoiceVo.getFkReceivablePlanIds())) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
//        BigDecimal totalAmount = BigDecimal.ZERO;
//        for (Long planId : invoiceVo.getFkReceivablePlanIds()) {
//            BigDecimal amount = saleCenterClient.getReceivablePlanAmountById(planId).getData();
//            invoiceReceivablePlanMapper.delete(Wrappers.<InvoiceReceivablePlan>lambdaQuery()
//                    .eq(InvoiceReceivablePlan::getFkInvoiceId, invoiceVo.getId())
//                    .eq(InvoiceReceivablePlan::getFkReceivablePlanId, planId));
//            totalAmount = totalAmount.add(amount);
//        }

//        Set<Long> idset = new HashSet<>(invoiceVo.getFkReceivablePlanIds());
//        List<ReceivablePlanVo> receivablePlanByIds = saleCenterClient.findReceivablePlanByIds(idset).getData();

        /**
         * 去掉自动扣减
         */
//        Invoice invoice = invoiceMapper.selectById(invoiceVo.getId());
//        //按币种分组
//        Map<String, List<ReceivablePlanVo>> currencyTypeMap = receivablePlanByIds.stream().filter(s -> s.getFkCurrencyTypeNum() != null)
//                .collect(Collectors.groupingBy(ReceivablePlanVo::getFkCurrencyTypeNum));
//
//        //金额计算支持多币种  添加汇率转换
//        BigDecimal resultSumAmount = BigDecimal.ZERO;
//        if (GeneralTool.isNotEmpty(currencyTypeMap)){
//            for (String s : currencyTypeMap.keySet()) {
//                BigDecimal sumAmount = currencyTypeMap.get(s).stream().filter(receivablePlanDto -> receivablePlanDto.getReceivableAmount() != null)
//                        .map(ReceivablePlanVo::getReceivableAmount)
//                        .reduce(BigDecimal.ZERO, BigDecimal::add);
//                //获取汇率
//                BigDecimal exchangeRate = exchangeRateService.getLastExchangeRate(false, s, invoice.getFkCurrencyTypeNum()).getExchangeRate()
//                        .setScale(4, BigDecimal.ROUND_HALF_UP);
//
//                resultSumAmount = resultSumAmount.add(sumAmount.multiply(exchangeRate));
//
//            }
//        }

        //删除发票绑定
//        for (Long planId : invoiceVo.getFkReceivablePlanIds()) {
//            invoiceReceivablePlanMapper.delete(Wrappers.<InvoiceReceivablePlan>lambdaQuery()
//                    .eq(InvoiceReceivablePlan::getFkInvoiceId, invoiceVo.getId())
//                    .eq(InvoiceReceivablePlan::getFkReceivablePlanId, planId));
//        }
        invoiceReceivablePlanMapper.deleteBatchIds(ids);

//        //更新发票金额
//        if (resultSumAmount.compareTo(BigDecimal.ZERO)!=0){
//            invoice.setInvoiceAmount((GeneralTool.isNotEmpty(invoice.getInvoiceAmount())?invoice.getInvoiceAmount():BigDecimal.ZERO).subtract(resultSumAmount));
//            utilService.updateUserInfoToEntity(invoice);
//            invoiceMapper.updateById(invoice);
//        }
    }

    /**
     * Author Cream
     * Description : //获取发票的编号串
     * Date 2023/9/8 14:05
     * Params:
     * Return
     */
    @Override
    public String getBindInvoiceNums(InvoiceBindDto invoiceBindDto) {
        List<String> invoiceNum = invoiceTargetMapper.getInvoiceNum(invoiceBindDto);
        if (GeneralTool.isNotEmpty(invoiceNum)) {
            return String.join(",", invoiceNum);
        }
        return null;
    }

    private void validateUpdate(InvoiceDto invoiceDto) {
        LambdaQueryWrapper<Invoice> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(Invoice::getNum, invoiceDto.getNum());
        lambdaQueryWrapper.eq(Invoice::getStatus, 1);
        if (GeneralTool.isNotEmpty(invoiceDto.getId())) {
            lambdaQueryWrapper.ne(Invoice::getId, invoiceDto.getId());
        }
        List<Invoice> invoices = invoiceMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(invoices)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("invoice_num_is_exist"));
        }
    }

    /**
     * Author Cream
     * Description : 批量新增发票和应收计划映射
     * Date 2022/4/18 15:57
     * Params: InvoiceReceivablePlanBatchVo
     * Return  ResponseBo
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBo batchMappingInvoiceAndReceivablePlan(InvoiceReceivablePlanBatchDto invoiceVo) {
        if (GeneralTool.isEmpty(invoiceVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //如果有传应收计划ids则同时绑定关系表
        if (GeneralTool.isNotEmpty(invoiceVo.getFkReceivablePlanIds())) {
            List<InvoiceReceivablePlan> list = new ArrayList<>();
            List<Long> planIds = invoiceVo.getFkReceivablePlanIds().stream().distinct().collect(Collectors.toList());
            for (Long fkReceivablePlanId : planIds) {
                InvoiceReceivablePlan invoiceReceivablePlan = new InvoiceReceivablePlan();
                invoiceReceivablePlan.setFkInvoiceId(invoiceVo.getFkInvoiceId());
                invoiceReceivablePlan.setFkReceivablePlanId(fkReceivablePlanId);
                utilService.updateUserInfoToEntity(invoiceReceivablePlan);
                list.add(invoiceReceivablePlan);
            }
            //增发票与应收的绑定关系
            if (GeneralTool.isNotEmpty(list)) {
                invoiceReceivablePlanMapper.addMappingInvoiceReceivablePlan(list);
            }
        }
        return SaveResponseBo.ok();
    }


    /**
     * Author Cream
     * Description :  获取发票金额
     * Date 2022/4/25 18:53
     * Params: Set<Long> invoiceIds
     * Return ResponseBo<BigDecimal>
     */
    @Override
    public ResponseBo<BigDecimal> doGetInvoiceTotalAmountByIds(Set<Long> invoiceIds) {
        if (GeneralTool.isEmpty(invoiceIds)) {
            throw new GetServiceException(ResultCode.PARAM_REQUIRED, "缺少发票id参数");
        }
        BigDecimal amount = invoiceMapper.getInvoiceTotalAmountByIds(invoiceIds);
        if (null == amount) {
            throw new GetServiceException(ResultCode.INVALID_PARAM, "非法发票id");
        }
        return new ResponseBo<>(amount);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long generateInvoice(InvoiceDto invoiceDto) {
        //验证编号
        validateUpdate(invoiceDto);

        Invoice invoice = new Invoice();
        BeanCopyUtils.copyProperties(invoiceDto, invoice);
        utilService.updateUserInfoToEntity(invoice);
        invoiceMapper.insert(invoice);

        InvoiceTarget invoiceTarget = new InvoiceTarget();
        invoiceTarget.setFkInvoiceId(invoice.getId());
        invoiceTarget.setFkTypeTargetId(invoiceDto.getFkTypeTargetId());
        invoiceTarget.setFkTypeKey(invoiceDto.getFkTypeKey());
        utilService.updateUserInfoToEntity(invoiceTarget);
        invoiceTargetMapper.insert(invoiceTarget);

        return invoice.getId();
    }

    @Override
    public void generateInvoiceReceivablePlan(InvoiceReceivablePlanDto invoiceReceivablePlanDto) {
        InvoiceReceivablePlan invoiceReceivablePlan = new InvoiceReceivablePlan();
        BeanCopyUtils.copyProperties(invoiceReceivablePlanDto, invoiceReceivablePlan);
        utilService.updateUserInfoToEntity(invoiceReceivablePlan);
        invoiceReceivablePlanMapper.insert(invoiceReceivablePlan);
    }

    @Override
    public List<Invoice> getInvoiceByNum(String invoiceNum) {
        if (GeneralTool.isEmpty(invoiceNum)) {
            return null;
        }
        List<Invoice> invoices = invoiceMapper.selectList(Wrappers.<Invoice>lambdaQuery()
                .eq(Invoice::getNum, invoiceNum)
                .eq(Invoice::getStatus,1));
        return invoices;
    }

    @Override
    public Map<Long, String> getInvoiceNumByReceivableId(Set<Long> ids) {
        if (ids.isEmpty()) {
            return Collections.emptyMap();
        }
        List<SelItem> selItems = invoiceReceivablePlanMapper.getInvoiceNumByReceivableId(ids);
        if (selItems.isEmpty()) {
            return Collections.emptyMap();
        }
        return selItems.stream().filter(f->f.getVal()!=null).collect(HashMap<Long,String>::new,(m,v)->m.put(v.getKeyId(),String.valueOf(v.getVal())),HashMap::putAll);
    }

    /**
     * Author Cream
     * Description : //新增发票与目标对象的绑定关系
     * Date 2023/9/8 14:07
     * Params:
     * Return
     */
    @Override
    public void createInvoiceAndTargetMapping(String typeKey, Long invoiceId, Long targetId) {
        if (StringUtils.isBlank(typeKey) || invoiceId == null || targetId == null) {
            return;
        }
        InvoiceTarget invoiceTarget = new InvoiceTarget();
        invoiceTarget.setFkInvoiceId(invoiceId)
                .setFkTypeKey(typeKey)
                .setFkTypeTargetId(targetId);
        utilService.setCreateInfo(invoiceTarget);
        invoiceTargetMapper.insert(invoiceTarget);
    }

    /**
     * Author Cream
     * Description : //更新发票与目标对象的绑定关系，采用先删除后绑定
     * Date 2023/9/8 14:07
     * Params:
     * Return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateInvoiceAndTargetMapping(String typeKey, Long invoiceId, Long targetId) {
        if (StringUtils.isBlank(typeKey) || invoiceId == null || targetId == null) {
            return;
        }
        InvoiceTarget invoiceTarget = new InvoiceTarget();
        invoiceTarget.setFkInvoiceId(invoiceId)
                .setFkTypeKey(typeKey)
                .setFkTypeTargetId(targetId);
        utilService.setUpdateInfo(invoiceTarget);
        invoiceTargetMapper.delete(Wrappers.<InvoiceTarget>lambdaQuery().eq(InvoiceTarget::getFkInvoiceId, invoiceId));
        invoiceTargetMapper.insert(invoiceTarget);
    }

    @Override
    public List<InvoiceVo> getInvoiceByNumList(Set<String> numSet) {
        if (GeneralTool.isEmpty(numSet)){
            return null;
        }
        List<Invoice> invoices = invoiceMapper.selectList(Wrappers.<Invoice>lambdaQuery().in(Invoice::getNum, numSet));
        if (GeneralTool.isNotEmpty(invoices)){
            List<InvoiceVo> invoiceVos = BeanCopyUtils.copyListProperties(invoices, InvoiceVo::new);
            return invoiceVos;
        }
        return null;
    }

    @Override
    public Integer getMaxPoNum(String num) {

        return invoiceMapper.getMaxPoNum(num);
    }

    @Override
    public Invoice isExistNum(Integer poNum, String invoiceNum) {
        String newNum = invoiceNum;
        List<Invoice> invoices = invoiceMapper.selectList(Wrappers.<Invoice>lambdaQuery().eq(Invoice::getNum, invoiceNum));
        while(GeneralTool.isNotEmpty(invoices)){
            poNum++;
            String code = String.valueOf(poNum);
            if (String.valueOf(poNum).length() <= 2) {
                code = String.format("%03d", poNum);
            }
            newNum = invoiceNum.substring(0, invoiceNum.length() - 3);
            newNum = newNum + code;
            invoices = invoiceMapper.selectList(Wrappers.<Invoice>lambdaQuery().eq(Invoice::getNum, newNum));
        }
        Invoice invoice = new Invoice();
        invoice.setNum(newNum);
        invoice.setPoNum(String.valueOf(poNum));
        return invoice;
    }

    /**
     * 解除与发票的绑定
     * 如果该发票只有这条绑定记录，同时作废发票
     * @param receivablePlanIdset
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean unBindInvoiceByReceivablePlanIds(List<Long> receivablePlanIdset) {
        LambdaQueryWrapper<InvoiceReceivablePlan> wrapper = Wrappers.lambdaQuery(InvoiceReceivablePlan.class);
        wrapper.in(InvoiceReceivablePlan::getFkReceivablePlanId,receivablePlanIdset);

        List<InvoiceReceivablePlan> invoiceReceivablePlans = invoiceReceivablePlanService.list(wrapper);
        Set<Long> invoiceIds = Sets.newHashSet();
        Set<Long> updateInvoiceIds = Sets.newHashSet();
        if (GeneralTool.isNotEmpty(invoiceReceivablePlans)){
            invoiceIds = invoiceReceivablePlans.stream().map(InvoiceReceivablePlan::getFkInvoiceId).collect(Collectors.toSet());

            List<InvoiceReceivablePlan> invoiceReceivablePlanList = invoiceReceivablePlanService.list(Wrappers.lambdaQuery(InvoiceReceivablePlan.class)
                    .in(InvoiceReceivablePlan::getFkInvoiceId, invoiceIds)
            );

            if (GeneralTool.isNotEmpty(invoiceReceivablePlanList)){
                Map<Long, Long> countMap = invoiceReceivablePlanList.stream().collect(Collectors.groupingBy(InvoiceReceivablePlan::getFkInvoiceId, Collectors.counting()));
                for (Map.Entry<Long, Long> entry : countMap.entrySet()) {
                    if (entry.getValue().equals(1L)){
                        updateInvoiceIds.add(entry.getKey());
                    }
                }
            }
        }

        //解除绑定
        invoiceReceivablePlanService.remove(wrapper);

        if (GeneralTool.isNotEmpty(updateInvoiceIds)){
            List<Invoice> invoices = list(Wrappers.lambdaQuery(Invoice.class).in(Invoice::getId, updateInvoiceIds));
            if (GeneralTool.isNotEmpty(invoices)){
                invoices.forEach(invoice -> invoice.setStatus(0));
                boolean update = updateBatchById(invoices);
                return update;
            }
//            Invoice invoice = new Invoice();
//            invoice.setStatus(0);
//            boolean update = update(invoice, Wrappers.lambdaQuery(Invoice.class).in(Invoice::getId, updateInvoiceIds));
        }
        return true;
    }

    /**
     * Author Cream
     * Description : //同步实收收款日期到发票
     * Date 2023/8/2 10:53
     * Params:
     * Return
     */
    @Override
    public void syncToInvoiceReceiptDate(List<Long> fkInvoiceIdList, Date receiptDate) {
        if (Objects.isNull(receiptDate) || GeneralTool.isEmpty(fkInvoiceIdList)) {
            return;
        }
        List<Invoice> invoices = invoiceMapper.selectBatchIds(fkInvoiceIdList);
        invoices.forEach(i->{
            i.setReceiptDate(receiptDate);
            utilService.setUpdateInfo(i);
        });
        if (GeneralTool.isNotEmpty(invoices)) {
            updateBatchById(invoices);
        }
    }

    @Override
    public void exportInvoicesExcel(HttpServletResponse response, InvoiceQueryDto invoiceVo) {
        List<InvoiceVo> invoices = getInvoices(invoiceVo, null);

        if (GeneralTool.isEmpty(invoices)) {
            return;
        }
        List<InvoiceExportVo> invoiceExportVos = new ArrayList<>();
        for (InvoiceVo invoiceDto : invoices) {
            InvoiceExportVo invoiceExportVo = BeanCopyUtils.objClone(invoiceDto, InvoiceExportVo::new);
            if (GeneralTool.isNotEmpty(invoiceDto.getStatus()) && invoiceDto.getStatus() == 1){
                invoiceExportVo.setStatusStr("正常");
            }else {
                invoiceExportVo.setStatusStr("作废");
            }
            if (invoiceDto.getIsAccount()){
                invoiceExportVo.setIsAccountStr("是");
            }else {
                invoiceExportVo.setIsAccountStr("否");
            }
            invoiceExportVos.add(invoiceExportVo);
        }
        FileUtils.exportExcelNotWrapText(response, invoiceExportVos, "InvoiceInfo", InvoiceExportVo.class);
    }

    @Override
    public List<Long> getInvoiceIdsByReceivablePlanId(Long receivablePlanId) {
        return invoiceMapper.getInvoiceIdsByReceivablePlanId(receivablePlanId);
    }

    @Override
    public Boolean isExistInvoiceNum(String invoiceNum) {
        if (GeneralTool.isEmpty(invoiceNum)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        Invoice invoice = invoiceMapper.selectOne(Wrappers.<Invoice>lambdaQuery().eq(Invoice::getNum, invoiceNum).eq(Invoice::getStatus, 1));
        return GeneralTool.isNotEmpty(invoice);
    }

    /**
     * HTI预付按钮
     *
     * @return
     * @Date 17:52 2024/6/13
     * <AUTHOR>
     */
    @Override
    @Transactional
    public String prepaymentButtonHti(List<PrepaymentButtonHtiDto> prepaymentButtonHtiDto1List) {
        List<Long> fkReceivablePlanIdList = prepaymentButtonHtiDto1List.stream().map(PrepaymentButtonHtiDto::getFkReceivablePlanId).collect(Collectors.toList());
        Result<List<PayablePlan>> result = saleCenterClient.getPayablePlanByReceivablePlanIds(fkReceivablePlanIdList);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        List<PayablePlan> payablePlanList = result.getData();
        List<Long> payablePlanIds = payablePlanList.stream().map(PayablePlan::getId).collect(Collectors.toList());

        //应付计划为学校提供商类型或者业务提供商类型,不用生成给代理的佣金
        Result<List<PayablePlanVo>> result1 = saleCenterClient.getPayablePlanByIds(new HashSet<>(payablePlanIds));
        if (!result1.isSuccess()) {
            throw new GetServiceException(result1.getMessage());
        }
        List<PayablePlanVo> payablePlanVoList = result1.getData();
        for (PayablePlanVo payablePlanVo : payablePlanVoList) {
            if (payablePlanVo.getFkTypeKey().equals(TableEnum.INSTITUTION_PROVIDER.key) || payablePlanVo.getFkTypeKey().equals(TableEnum.SALE_BUSINESS_PROVIDER.key)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("PAYABLE_PLANS_CANNOT_GENERATE_COMMISSIONS"));
            }
        }

        List<PrepaymentButtonHtiVo> prepaymentButtonHtiDtoList = new ArrayList<>();
        StringBuilder str = new StringBuilder();
        for (PrepaymentButtonHtiDto prepaymentButtonHtiDto1 : prepaymentButtonHtiDto1List) {
            PayablePlan plan = saleCenterClient.getPayablePlanByReceivablePlanId(prepaymentButtonHtiDto1.getFkReceivablePlanId()).getData();
            if (GeneralTool.isEmpty(plan)) {
                continue;
            }

            ReceivablePlanVo receivablePlan = saleCenterClient.getReceivablePlanById(prepaymentButtonHtiDto1.getFkReceivablePlanId()).getData();
            BigDecimal amountPaid = paymentFormItemService.getAmountPaidByPayablePlanId(plan.getId()).add(payablePlanSettlementInstallmentMapper.getAmountPaidByPayablePlanId(plan.getId()));
            if (amountPaid.compareTo(plan.getPayableAmount()) == 0) {
                str.append(prepaymentButtonHtiDto1.getStudentName()).append(",").append(LocaleMessageUtils.getMessage("ACCOUNTS_PAYABLE_COMPLETED")).append("\n");
                continue;
            }

            Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.PAY_IN_ADVANCE_SERVICE_FEE.key, 1).getData();
            //应收金额
            BigDecimal receivableAmount = receivablePlan.getReceivableAmount();
            //应收币种
            String fkCurrencyTypeNum = receivablePlan.getFkCurrencyTypeNum();
            //应收金额折合成应付币种的应收金额
            BigDecimal receivableExchangeAmount;
            //统一将全部金额转成应付计划币种进行公式计算
            if (fkCurrencyTypeNum.equals(plan.getFkCurrencyTypeNum())) {
                receivableExchangeAmount = receivableAmount;
            } else {
                BigDecimal lastExchangeRate = exchangeRateService.getLastExchangeRate(false, fkCurrencyTypeNum, plan.getFkCurrencyTypeNum()).getExchangeRate();
                receivableExchangeAmount = receivableAmount.multiply(lastExchangeRate).setScale(2, RoundingMode.HALF_UP);
            }
            //应收应付比例
            BigDecimal proportion = plan.getPayableAmount().divide(receivableExchangeAmount, 12, RoundingMode.DOWN);
            //本次预付金额
            BigDecimal amountActual = prepaymentButtonHtiDto1.getSubtotal().multiply(proportion).multiply(prepaymentButtonHtiDto1.getPayInAdvancePercent().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)).setScale(2, RoundingMode.HALF_UP);
            String configValue1 = companyConfigMap.get(plan.getFkCompanyId());
            BigDecimal serviceFee = new BigDecimal(configValue1);
            amountActual = amountActual.subtract(serviceFee);

            PayablePlanSettlementInstallment payablePlanSettlementInstallment = new PayablePlanSettlementInstallment();
            payablePlanSettlementInstallment.setFkPayablePlanId(plan.getId());
            payablePlanSettlementInstallment.setAmountExpect(amountActual);
            payablePlanSettlementInstallment.setAmountActual(amountActual);
            payablePlanSettlementInstallment.setAmountActualInit(amountActual);
            payablePlanSettlementInstallment.setServiceFeeExpect(serviceFee);
            payablePlanSettlementInstallment.setServiceFeeActual(serviceFee);
            payablePlanSettlementInstallment.setServiceFeeActualInit(serviceFee);
            payablePlanSettlementInstallment.setFkInvoiceId(prepaymentButtonHtiDto1.getFkInvoiceId());
            payablePlanSettlementInstallment.setFkInvoiceReceivablePlanId(prepaymentButtonHtiDto1.getInvoiceReceivablePlanRelationId());
            payablePlanSettlementInstallment.setRollBack(false);
            payablePlanSettlementInstallment.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
            payablePlanSettlementInstallment.setStatus(ProjectExtraEnum.INSTALLMENT_UNTREATED.key);
            utilService.setCreateInfo(payablePlanSettlementInstallment);
            payablePlanSettlementInstallmentMapper.insert(payablePlanSettlementInstallment);

            PayablePlanSettlementStatus payablePlanSettlementStatus = new PayablePlanSettlementStatus();
            payablePlanSettlementStatus.setFkPayablePlanId(plan.getId());
            payablePlanSettlementStatus.setFkPayablePlanSettlementInstallmentId(payablePlanSettlementInstallment.getId());
            payablePlanSettlementStatus.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
            utilService.setCreateInfo(payablePlanSettlementStatus);
            payablePlanSettlementStatusMapper.insert(payablePlanSettlementStatus);

            PrepaymentButtonHtiVo prepaymentButtonHtiDto = new PrepaymentButtonHtiVo();
            prepaymentButtonHtiDto.setPercentage(prepaymentButtonHtiDto1.getPayInAdvancePercent());
            prepaymentButtonHtiDto.setPayInAdvanceAmount(amountActual);
            prepaymentButtonHtiDto.setInvoiceReceivablePlanRelationId(prepaymentButtonHtiDto1.getInvoiceReceivablePlanRelationId());
            prepaymentButtonHtiDtoList.add(prepaymentButtonHtiDto);
        }
        //HTI预付
        iInvoiceReceivablePlanService.prepaymentButtonHti(prepaymentButtonHtiDtoList);
        return str.toString();

    }

    /**
     * HTI取消预付按钮
     * @param prepaymentButtonHtiDtoList
     * @return
     */
    @Override
    public String cancelPrepaymentButtonHti(List<PrepaymentButtonHtiDto> prepaymentButtonHtiDtoList) {
        List<Long> fkReceivablePlanIdList = prepaymentButtonHtiDtoList.stream().map(PrepaymentButtonHtiDto::getFkReceivablePlanId).collect(Collectors.toList());
        Result<List<PayablePlan>> result = saleCenterClient.getPayablePlanByReceivablePlanIds(fkReceivablePlanIdList);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        List<PayablePlan> payablePlanList = result.getData();
        List<Long> payablePlanIds = payablePlanList.stream().map(PayablePlan::getId).collect(Collectors.toList());
        StringBuilder str = new StringBuilder();

        List<Long> invoiceReceivablePlanRelationIds = prepaymentButtonHtiDtoList.stream().map(PrepaymentButtonHtiDto::getInvoiceReceivablePlanRelationId).collect(Collectors.toList());
        List<PayablePlanSettlementInstallment> settlementInstallments = payablePlanSettlementInstallmentMapper.selectList(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                .ne(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_UNTREATED.key)
                .in(PayablePlanSettlementInstallment::getFkInvoiceReceivablePlanId, invoiceReceivablePlanRelationIds));

        //应付计划为学校提供商类型或者业务提供商类型,不用生成给代理的佣金
        Result<List<PayablePlanVo>> result1 = saleCenterClient.getPayablePlanByIds(new HashSet<>(payablePlanIds));
        if (!result1.isSuccess()) {
            throw new GetServiceException(result1.getMessage());
        }
        List<PayablePlanVo> payablePlanVoList = result1.getData();
        for (PayablePlanVo payablePlanVo : payablePlanVoList) {
            if (payablePlanVo.getFkTypeKey().equals(TableEnum.INSTITUTION_PROVIDER.key) || payablePlanVo.getFkTypeKey().equals(TableEnum.SALE_BUSINESS_PROVIDER.key)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("PAYABLE_PLANS_CANNOT_GENERATE_COMMISSIONS"));
            }
        }

        List<Long> invoiceReceivablePlanRelationIdList = prepaymentButtonHtiDtoList.stream().map(PrepaymentButtonHtiDto::getInvoiceReceivablePlanRelationId).collect(Collectors.toList());
        for (PrepaymentButtonHtiDto prepaymentButtonHtiDto : prepaymentButtonHtiDtoList) {
            if (GeneralTool.isNotEmpty(settlementInstallments)) {
                boolean flag = false;
                for (PayablePlanSettlementInstallment settlementInstallment : settlementInstallments) {
                    if (settlementInstallment.getFkInvoiceReceivablePlanId().equals(prepaymentButtonHtiDto.getInvoiceReceivablePlanRelationId())) {
                        str.append(prepaymentButtonHtiDto.getStudentName()).append(",").append(LocaleMessageUtils.getMessage("NOT_PAY_ADVANCE_SETTLEMENT_INSTALLMENT")).append("\n");
                        invoiceReceivablePlanRelationIdList.remove(prepaymentButtonHtiDto.getInvoiceReceivablePlanRelationId());
                        flag = true;
                    }
                }
                if (flag) {
                    continue;
                }
            }
            payablePlanSettlementInstallmentMapper.delete(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                    .eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_UNTREATED.key)
                    .eq(PayablePlanSettlementInstallment::getFkInvoiceId, prepaymentButtonHtiDto.getFkInvoiceId())
                    .eq(PayablePlanSettlementInstallment::getFkInvoiceReceivablePlanId, prepaymentButtonHtiDto.getInvoiceReceivablePlanRelationId())
                    .isNull(PayablePlanSettlementInstallment::getFkReceiptFormItemId));
        }
        if (GeneralTool.isNotEmpty(invoiceReceivablePlanRelationIdList)) {
            invoiceReceivablePlanMapper.update(null,Wrappers.<InvoiceReceivablePlan>lambdaUpdate()
                    .in(InvoiceReceivablePlan::getId, invoiceReceivablePlanRelationIdList)
                    .set(InvoiceReceivablePlan::getIsPayInAdvance,false)
                    .set(InvoiceReceivablePlan::getPayInAdvancePercent,null)
                    .set(InvoiceReceivablePlan::getPayInAdvanceAmount, null));
        }
        return str.toString();
    }

}
