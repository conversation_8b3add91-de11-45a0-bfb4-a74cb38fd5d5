package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2020/8/24
 * @TIME: 16:30
 * @Description:
 **/
@Data
public class InstitutionProviderInstitutionDto extends BaseVoEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 学校提供商Id
     */
    @ApiModelProperty(value = "学校提供商Id", required = true)
    @NotNull(message = "学校提供商Id不能为空", groups = {Add.class, Update.class})
    private Long fkInstitutionProviderId;
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;
    /**
     * 绑定时间
     */
    @ApiModelProperty(value = "绑定时间")
    private Date activeDate;
    /**
     * 取消绑定时间
     */
    @ApiModelProperty(value = "取消绑定时间")
    private Date unactiveDate;
}
