package com.get.votingcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_voting_item_option")
public class VotingItemOption extends BaseEntity{
    private static final long serialVersionUID = 1L;
    /**
     * 投票项Id
     */
    @ApiModelProperty(value = "投票项Id")
    @Column(name = "fk_voting_item_id")
    private Long fkVotingItemId;
    /**
     * 选项名称
     */
    @ApiModelProperty(value = "选项名称")
    @Column(name = "name")
    private String name;
    /**
     * 选项副名称
     */
    @ApiModelProperty(value = "选项副名称")
    @Column(name = "name_sub")
    private String nameSub;
    /**
     * 默认为0，为0时随机排序，排序为从大到小顺序排列
     */
    @ApiModelProperty(value = "默认为0，为0时随机排序，排序为从大到小顺序排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}