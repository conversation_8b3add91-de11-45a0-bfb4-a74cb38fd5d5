package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2023/12/13
 * @TIME: 15:47
 * @Description:活动年度计划Dto
 **/
@Data
public class EventPlanVo extends BaseEntity implements Serializable {
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "线上活动合计")
    private Integer onlineCount;

    @ApiModelProperty(value = "线下活动合计")
    private Integer offlineCount;

    @ApiModelProperty(value = "线下专访合计")
    private Integer workshopCount;

    @ApiModelProperty(value = "报名名册合计")
    private Integer registrationCount;

    @ApiModelProperty(value = "描述")
    private String description;
}
