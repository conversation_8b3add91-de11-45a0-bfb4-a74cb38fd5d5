<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.insurance.InsuranceOrderMapper">

    <select id="getInsuranceOrder" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
        mio.id,
        CONCAT(mio.insurant_name, " (",mio.insurant_first_name, " ", mio.insurant_last_name, ")"," / ",
        ma.name," / ",
        upt.type_name,"(",upt.type_key,")： ",
        mio.insurance_num,", ",
        mio.insurance_type,", ",
        DATE_FORMAT( mio.insurance_start_time, '%Y-%m-%d' ),"至", DATE_FORMAT( mio.insurance_end_time, '%Y-%m-%d' )) AS name
        FROM app_insurance_center.m_insurance_order AS mio
                 LEFT JOIN ais_sale_center.m_agent AS ma ON ma.id = mio.fk_agent_id
                 LEFT JOIN app_insurance_center.u_product_type AS upt ON upt.id = mio.fk_product_type_id
        <where>
            mio.fk_company_id IN
            <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                #{companyId}
            </foreach>
        <if test="keyWord != null and keyWord != ''">
            AND (
            mio.insurance_num = CONCAT('%',#{keyWord},'%')
            OR mio.insurance_type LIKE CONCAT('%',#{keyWord},'%')
            OR mio.insurant_name LIKE CONCAT('%',#{keyWord},'%')
            OR ma.name LIKE CONCAT('%',#{keyWord},'%')
            OR upt.type_name LIKE CONCAT('%',#{keyWord},'%')
            OR upt.type_key LIKE CONCAT('%',#{keyWord},'%')
            )
        </if>

        </where>
    </select>
    <select id="getPlanInfoByTargetId" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT i.id,
               CONCAT(mio.insurant_name, " (",mio.insurant_first_name, " ", mio.insurant_last_name, ")"," / ",
                      ma.name," / ",
                      upt.type_name,"(",upt.type_key,")： ",
                      mio.insurance_num,", ",
                      mio.insurance_type,", ",
                      DATE_FORMAT( mio.insurance_start_time, '%Y-%m-%d' ),"至", DATE_FORMAT( mio.insurance_end_time, '%Y-%m-%d' )) AS name
        FROM
            ais_sale_center.m_receivable_plan i
                INNER JOIN (
                SELECT
                    a.id
                FROM
                    ais_sale_center.m_receivable_plan a
                        INNER JOIN ais_finance_center.r_invoice_receivable_plan b ON b.fk_receivable_plan_id = a.id
                        INNER JOIN ais_finance_center.r_receipt_form_invoice c ON c.fk_invoice_id = b.fk_invoice_id
                        INNER JOIN ais_finance_center.m_receipt_form d ON d.id = c.fk_receipt_form_id and d.fk_type_key = 'm_business_provider_ins'
                WHERE
                    d.fk_type_target_id = #{targetId}
                  AND d.id = #{receiptFormId}
                  and a.status = 1
                  and d.status = 1
            ) a ON a.id = i.id
                INNER JOIN app_insurance_center.m_insurance_order mio ON mio.id = i.fk_type_target_id and i.fk_type_key = 'm_insurance_order'
                LEFT JOIN ais_sale_center.m_agent AS ma ON ma.id = mio.fk_agent_id
                LEFT JOIN app_insurance_center.u_product_type AS upt ON upt.id = mio.fk_product_type_id
        GROUP BY i.id
    </select>
    <select id="getInsuranceOrderInformation" resultType="com.get.salecenter.dto.InsuranceOrderDto">
        SELECT
            mio.*,
            CONCAT(mio.insurant_name, " (",mio.insurant_first_name, " ", mio.insurant_last_name, ")") AS studentName,
            CONCAT(upt.type_name,"(",upt.type_key,")： ",
                   mio.insurance_num,", ",
                   mio.insurance_type,", ",
                   DATE_FORMAT( mio.insurance_start_time, '%Y-%m-%d' ),"至", DATE_FORMAT( mio.insurance_end_time, '%Y-%m-%d' )) AS businessInformation,
                   ma.name AS agentName,
                   CONCAT(upt.type_name,"(",upt.type_key,")") AS productTypeName,
            CONCAT(
            "[",
            uac.num,
            "] ",
            uac.`name`,
            IF
            (
            uac.name_chn IS NULL
            OR uac.name_chn = '',
            '',
            CONCAT( "（", uac.name_chn, "）" ))) AS fkAreaCountryName
        FROM app_insurance_center.m_insurance_order mio
                 LEFT JOIN ais_sale_center.m_agent AS ma ON ma.id = mio.fk_agent_id
                 LEFT JOIN app_insurance_center.u_product_type AS upt ON upt.id = mio.fk_product_type_id
                 LEFT JOIN ais_institution_center.u_area_country AS uac ON uac.id = mio.fk_area_country_id_to
        where mio.id = #{insuranceOrderId}
    </select>
    <select id="getInsuranceOrderInformationList" resultType="com.get.salecenter.dto.InsuranceOrderDto">
        SELECT
            mio.*,
            CONCAT(mio.insurant_name, " (",mio.insurant_first_name, " ", mio.insurant_last_name, ")") AS studentName,
            CONCAT(upt.type_name,"(",upt.type_key,")： ",
                   mio.insurance_num,", ",
                   mio.insurance_type,", ",
                   DATE_FORMAT( mio.insurance_start_time, '%Y-%m-%d' ),"至", DATE_FORMAT( mio.insurance_end_time, '%Y-%m-%d' )) AS businessInformation,
            ma.name AS agentName,
            CONCAT(upt.type_name,"(",upt.type_key,")") AS productTypeName,
            CONCAT(
                    "[",
                    uac.num,
                    "] ",
                    uac.`name`,
                    IF
                    (
                            uac.name_chn IS NULL
                                OR uac.name_chn = '',
                            '',
                            CONCAT( "（", uac.name_chn, "）" ))) AS fkAreaCountryName
        FROM app_insurance_center.m_insurance_order mio
                 LEFT JOIN ais_sale_center.m_agent AS ma ON ma.id = mio.fk_agent_id
                 LEFT JOIN app_insurance_center.u_product_type AS upt ON upt.id = mio.fk_product_type_id
                 LEFT JOIN ais_institution_center.u_area_country AS uac ON uac.id = mio.fk_area_country_id_to
        where mio.id in
        <foreach item="item" collection="insuranceOrderIds" separator="," open="(" close=")" index="index">
            #{item}
        </foreach>
    </select>
</mapper>