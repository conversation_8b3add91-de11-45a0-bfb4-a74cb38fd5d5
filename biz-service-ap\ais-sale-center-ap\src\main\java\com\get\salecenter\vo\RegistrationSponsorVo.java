package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/5/18 10:50
 * @verison: 1.0
 * @description:
 */
@Data
public class RegistrationSponsorVo {
    /**
     * 回执码，8位数字随机数
     */
    @ApiModelProperty(value = "回执码，8位数字随机数")
    @Column(name = "receipt_code")
    private String receiptCode;

    /**
     * 提供商名称
     */
    @ApiModelProperty(value = "提供商名称")
    private String providerName;

    /**
     * 展位列表
     */
    @ApiModelProperty(value = "展位列表")
    private List<BoothVo> boothDtos;

    /**
     * 赞助项目列表（以状态区分）
     */
    @ApiModelProperty(value = "赞助项目列表（以状态区分）")
    private List<SponsorVo> sponsorDtos;

    /**
     * 赞助项目列表
     */
    @ApiModelProperty(value = "赞助项目列表")
    private List<ConventionSponsorFeeVo> conventionSponsorFeeDtoList;
}
