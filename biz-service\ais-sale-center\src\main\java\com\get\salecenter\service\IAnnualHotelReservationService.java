package com.get.salecenter.service;


import com.get.salecenter.vo.AnnualHotelReservationVo;
import com.get.salecenter.vo.ConventionHotelVo;
import com.get.salecenter.vo.ConventionRegistrationVo;
import com.get.salecenter.dto.AnnualHotelReservationDto;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/6/22 11:09
 * @verison: 1.0
 * @description:
 */
public interface IAnnualHotelReservationService {

    /**
     * @return
     * @Description :酒店预订表单新增
     * @Param [annualHotelReservationDto]
     * <AUTHOR>
     */
    void addAnnualHotelReservation(AnnualHotelReservationDto annualHotelReservationDto);

    /**
     * @return
     * @Description :批量酒店预订表单新增
     * @Param [annualHotelReservationDtos]
     * <AUTHOR>
     */
    void batchAddAnnualHotelReservation(List<AnnualHotelReservationDto> annualHotelReservationDtos);

    /**
     * @return
     * @Description :机构名称下拉框
     * @Param [receiptCode]
     * <AUTHOR>
     */
    List<ConventionRegistrationVo> getInstitutionNameSelect(String receiptCode, Long fkConventionId);


    /**
     * @return
     * @Description :回显接口
     * @Param [receiptCode]
     * <AUTHOR>
     */
    List<AnnualHotelReservationVo> getAnnualHotelReservationDto(String receiptCode);

    /**
     * @return
     * @Description :删除参会人信息
     * @Param [id]
     * <AUTHOR>
     */
    void deleteConventionPerson(Long id,String receiptCode);

    /**
     * 房型下拉框
     *
     * @Date 11:31 2024/1/3
     * <AUTHOR>
     */
    List<ConventionHotelVo> getConventionHotel();
}
