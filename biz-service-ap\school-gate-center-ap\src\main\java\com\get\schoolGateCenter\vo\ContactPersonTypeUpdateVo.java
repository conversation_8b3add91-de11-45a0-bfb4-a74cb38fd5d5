package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;

/**
 * @author: Hardy
 * @create: 2022/2/8 12:01
 * @verison: 1.0
 * @description:
 */
@Data
public class ContactPersonTypeUpdateVo extends BaseVoEntity {
    /**
     * 类型名称
     */
    @NotBlank(message = "类型名称", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "类型名称")
    @Column(name = "type_name")
    private String typeName;

    /**
     * 类型Key，枚举类型Key
     */
    @NotBlank(message = "类型Key不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "类型Key，枚举类型Key")
    @Column(name = "type_key")
    private String typeKey;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}
