package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.financecenter.dto.VouchApplicationFormPaymentAddDto;
import com.get.financecenter.dto.VouchApplicationFormPaymentQueryDto;
import com.get.financecenter.vo.PaymentMethodTypeSelectEntity;
import com.get.financecenter.vo.VouchApplicationFormPaymentVo;

import java.util.List;

public interface VouchApplicationFormPaymentService {

    /**
     * 付款列表
     *
     * @param vouchApplicationFormPaymentQueryDto
     * @param page
     * @return
     */
    List<VouchApplicationFormPaymentVo> vouchApplicationFormPaymentDatas(VouchApplicationFormPaymentQueryDto vouchApplicationFormPaymentQueryDto, Page page);

    /**
     * 创建付款记录
     * @param vouchApplicationFormPaymentAddDto
     */
    void addVouchApplicationFormPayment(VouchApplicationFormPaymentAddDto vouchApplicationFormPaymentAddDto);

    /**
     * 付款方式下拉框
     * @return
     */
    List<PaymentMethodTypeSelectEntity> paymentMethodTypeSelect();

    /**
     * 作废付款记录
     */
    void cancelVouchApplicationFormPayment(Long id);

}
