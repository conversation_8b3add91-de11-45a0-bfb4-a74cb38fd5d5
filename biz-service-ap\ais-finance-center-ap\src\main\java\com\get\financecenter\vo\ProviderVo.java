package com.get.financecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.financecenter.entity.Provider;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Sea
 * @create: 2020/12/18 10:13
 * @verison: 1.0
 * @description:
 */
@Data
public class ProviderVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 区域所在国家名称
     */
    @ApiModelProperty(value = "区域所在国家名称")
    private String countryName;

    /**
     * 区域所在州省名称
     */
    @ApiModelProperty(value = "区域所在州省名称")
    private String stateName;

    /**
     * 区域所在城市名称
     */
    @ApiModelProperty(value = "区域所在城市名称")
    private String cityName;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 供应商类型名称
     */
    @ApiModelProperty(value = "供应商类型名称")
    private String providerTypeName;

    //=============实体类Provider===================
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
    /**
     * 供应商类型Id
     */
    @ApiModelProperty(value = "供应商类型Id")
    private Long fkProviderTypeId;
    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商编号")
    private String num;
    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String name;
    /**
     * 区域所在国家Id
     */
    @ApiModelProperty(value = "区域所在国家Id")
    private Long fkAreaCountryId;
    /**
     * 区域所在州省Id
     */
    @ApiModelProperty(value = "区域所在州省Id")
    private Long fkAreaStateId;
    /**
     * 区域所在城市Id
     */
    @ApiModelProperty(value = "区域所在城市Id")
    private Long fkAreaCityId;
    /**
     * 性质：0个人/1公司
     */
    @ApiModelProperty(value = "性质：0个人/1公司")
    private Integer natureType;
    /**
     * 法定代表人
     */
    @ApiModelProperty(value = "法定代表人")
    private String legalPerson;
    /**
     * 税号/统一社会信用代码（公司）
     */
    @ApiModelProperty(value = "税号/统一社会信用代码（公司）")
    private String taxCode;
    /**
     * 身份证号（个人）
     */
    @ApiModelProperty(value = "身份证号（个人）")
    private String idCardNum;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;
}
