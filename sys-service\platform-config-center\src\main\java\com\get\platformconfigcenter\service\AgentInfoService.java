package com.get.platformconfigcenter.service;


/**
 * 代理配置逻辑处理类
 *
 * <AUTHOR>
 * @date 2021/5/13 9:52
 */
public interface AgentInfoService {

    /**
     * @Description:查询代理配置列表
     * @Param
     * @Date 18:54 2021/5/13
     * <AUTHOR>
     */
//    List<AgentInfoVo> getAgentInfoList(AgentInfoDto agentInfoVo, Page page);

    /**
     * @Description:新增代理配置
     * @Param
     * @Date 10:15 2021/5/13
     * <AUTHOR>
     */
//    Long addAgentInfo(AgentInfoDto agentInfoVo);

    /**
     * @Description:更新代理配置
     * @Param
     * @Date 16:29 2021/5/13
     * <AUTHOR>
     */
//    AgentInfoVo updateAgentInfo(AgentInfoDto agentInfoVo);


    /**
     * @Description:代理配置详情
     * @Param
     * @Date 12:52 2021/5/13
     * <AUTHOR>
     */
//    AgentInfoVo findAgentInfoById(Long id);

    /**
     * @Description:删除代理配置
     * @Param
     * @Date 16:56 2021/5/13
     * <AUTHOR>
     */
//    void delete(Long id);

    /**
     * @Description: 分配siteMap
     * @Author: Jerry
     * @Date:18:28 2021/8/13
     */
//    void allocationSitemap(Long id, List<String> sitemapVoIds);

    /**
     * @Description: 回显代理菜单功能
     * @Author: Jerry
     * @Date:10:04 2021/8/16
     */
//    List<AgentSiteMapVo> selectSitemap(Long id);

    /**
     *
     * <AUTHOR>
     * @Description: 配置代理课程大类绑定的推荐学习和新闻
     *
     * @param agentCourseGroupRecommendVo
     * @Return void
     * @date 2022/11/15 11:25
     */
//    void configCourseGroupRecommendations(AgentCourseGroupRecommendDto agentCourseGroupRecommendVo);

    /**
     *
     * <AUTHOR>
     * @Description:  查询代理课程大类绑定的详情
     *
     * @param id
     * @Return com.get.platformconfigcenter.vo.AgentCourseGroupRecommendVo
     * @date 2022/11/18 16:34
     */
//    AgentCourseGroupRecommendVo selectCourseGroupRecommendations(Long id);

    /**
     *
     * <AUTHOR>
     * @Description: 代理推荐大类绑定的学校，新闻列表
     *
     * @param data
     * @param voSearchBean
     * @Return java.util.List<com.get.platformconfigcenter.vo.AgentCourseGroupRecommendVo>
     * @date 2022/11/18 16:33
     */
//    List<AgentCourseGroupRecommendVo> getCourseGroupRecommendationsList(AgentCourseGroupRecommendListDto data, Page voSearchBean);

//    void deleteCourseGroupRecommendations(Long id);
}
