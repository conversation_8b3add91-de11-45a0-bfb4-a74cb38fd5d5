package com.get.permissioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.permissioncenter.dto.BatchModifyConfigDto;
import com.get.permissioncenter.vo.BatchModifyConfigVo;
import com.get.permissioncenter.service.IBatchModifyConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/2/22 17:09
 * @verison: 1.0
 * @description:
 */
@Api(tags = "批量修改配置管理")
@RestController
@RequestMapping("system/batchModifyConfig")
public class BatchModifyConfigController {
    @Resource
    private IBatchModifyConfigService batchModifyConfigService;

    /**
     * @return com.get.common.result.ListResponseBo<com.get.systemcenter.vo.BatchModifyConfigVo>
     * @Description :加载批量修改项接口
     * @Param [batchModifyConfigDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "加载批量修改项接口", notes = "fkDbName库名 ， fkTableName表名")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.LIST, description = "系统中心/批量修改配置管理/加载批量修改项")
    @PostMapping("getBatchUpdateItems")
    public ListResponseBo<BatchModifyConfigVo> getBatchUpdateItems(@RequestBody BatchModifyConfigDto batchModifyConfigDto) {
        List<BatchModifyConfigVo> datas = batchModifyConfigService.getBatchUpdateItems(batchModifyConfigDto);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.systemcenter.vo.BatchModifyConfigVo>
     * @Description :批量修改配置详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.DETAIL, description = "系统中心/批量修改配置管理/批量修改配置详情")
    @GetMapping("/{id}")
    public ResponseBo<BatchModifyConfigVo> detail(@PathVariable("id") Long id) {
        BatchModifyConfigVo data = batchModifyConfigService.findbatchModifyConfigById(id);
        return new ResponseBo<>(data);
    }
}
