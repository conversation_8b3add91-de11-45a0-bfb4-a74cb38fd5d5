<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.TravelClaimFormItemMapper">

    <select id="getAllTravelClaimFormItemByActivityData" resultType="com.get.financecenter.vo.TravelClaimFormAndItemVo">
        SELECT
        mtcf.num AS form_number,
        mtcfi.*
        FROM m_travel_claim_form mtcf
        LEFT JOIN m_travel_claim_form_item mtcfi ON mtcf.id = mtcfi.fk_travel_claim_form_id
        WHERE 1=1
        <if test="statusList != null and statusList.size() > 0">
            AND mtcf.status IN
            <foreach collection="statusList" item="status" index="index" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="searchActivityDataDto.fkEventTableName != null and searchActivityDataDto.fkEventTableName != ''">
            AND mtcfi.fk_event_table_name = #{searchActivityDataDto.fkEventTableName}
        </if>
        <if test="searchActivityDataDto.fkEventTableId != null">
            AND mtcfi.fk_event_table_id = #{searchActivityDataDto.fkEventTableId}
        </if>
    </select>

    <select id="getTravelClaimFormActivityData" resultType="com.get.financecenter.vo.TravelClaimFormAndItemVo">
        SELECT mtcfi.*,
        mtcf.fk_company_id,
        mtcf.fk_department_id,
        mtcf.fk_staff_id,
        mtcf.fk_travel_claim_form_id_revoke,
        mtcf.num,
        mtcf.fk_currency_type_num,
        mtcf.bill_count,
        mtcf.summary,
        mtcf.fk_vouch_id,
        mtcf.is_vouch_created,
        mtcf.date_vouch_created,
        mtcf.fk_staff_id_vouch_created,
        mtcf.fk_staff_ids_notice,
        mtcf.status
        FROM m_travel_claim_form_item mtcfi
        LEFT JOIN m_travel_claim_form mtcf ON mtcfi.fk_travel_claim_form_id = mtcf.id
        WHERE 1=1 AND mtcf.status IN (1,2)
        <if test="searchActivityDataDto.fkEventTableName != null and searchActivityDataDto.fkEventTableName != ''">
            AND mtcfi.fk_event_table_name = #{searchActivityDataDto.fkEventTableName}
        </if>
        <if test="searchActivityDataDto.fkEventTableId != null">
            AND mtcfi.fk_event_table_id = #{searchActivityDataDto.fkEventTableId}
        </if>
    </select>

    <select id="getClaimedEventCostByActivityData" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(mtcfi.amount), 0) AS totalAmount
        FROM m_travel_claim_form mtcf
        LEFT JOIN m_travel_claim_form_item mtcfi ON mtcf.id = mtcfi.fk_travel_claim_form_id
        WHERE 1=1
        <if test="searchActivityDataDto.fkEventTableName != null and searchActivityDataDto.fkEventTableName != ''">
            AND mtcfi.fk_event_table_name = #{searchActivityDataDto.fkEventTableName}
        </if>
        <if test="searchActivityDataDto.fkEventTableId != null">
            AND mtcfi.fk_event_table_id = #{searchActivityDataDto.fkEventTableId}
        </if>
        <!-- 条件1: 状态相同的记录 -->
        <if test="statusList != null and statusList.size() > 0">
            AND mtcf.status IN
            <foreach collection="statusList" item="status" index="index" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <!-- 条件2: 获取当前申请的记录 -->
        <if test="isAvailableClaimAmount">
            <if test="searchActivityDataDto.id != null and searchActivityDataDto.id != ''">
                AND mtcf.id = #{searchActivityDataDto.id}
            </if>
        </if>
    </select>

    <select id="getCurrencyTypeByActivityData" resultType="java.lang.String">
        SELECT DISTINCT mtcf.fk_currency_type_num
        FROM m_travel_claim_form mtcf
        LEFT JOIN m_travel_claim_form_item mtcfi ON mtcf.id = mtcfi.fk_travel_claim_form_id
        WHERE 1=1
        <if test="statusList != null and statusList.size() > 0">
            AND mtcf.status IN
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="searchActivityDataDto.fkEventTableName != null and searchActivityDataDto.fkEventTableName != ''">
            AND mtcfi.fk_event_table_name = #{searchActivityDataDto.fkEventTableName}
        </if>
        <if test="searchActivityDataDto.fkEventTableId != null">
            AND mtcfi.fk_event_table_id = #{searchActivityDataDto.fkEventTableId}
        </if>
    </select>
</mapper>