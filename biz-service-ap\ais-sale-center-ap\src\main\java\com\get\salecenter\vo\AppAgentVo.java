package com.get.salecenter.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.get.core.mybatis.base.BaseEntity;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @author: Hardy
 * @create: 2022/11/21 15:32
 * @verison: 1.0
 * @description:
 */
@Data
public class AppAgentVo extends BaseEntity {

    @ApiModelProperty("公司名称")
    private String fkCompanyName;

    @ApiModelProperty("国家名称")
    private String fkCountryName;

    @ApiModelProperty("州省名称")
    private String fkStateName;

    @ApiModelProperty("城市名称")
    private String fkCityName;

    @ApiModelProperty("申请状态名称")
    private String appStatusName;

    @ApiModelProperty("性质名称")
    private String natureName;

    @ApiModelProperty("bdCode")
    private String bdCode;

    @ApiModelProperty("bd名称")
    private String bdName;

    //============实体类AppAgent=================
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    /**
     * BD员工Id
     */
    @ApiModelProperty(value = "BD员工Id")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;

    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;

    /**
     * 州省Id
     */
    @ApiModelProperty(value = "州省Id")
    @Column(name = "fk_area_state_id")
    private Long fkAreaStateId;

    /**
     * 城市Id
     */
    @ApiModelProperty(value = "城市Id")
    @Column(name = "fk_area_city_id")
    private Long fkAreaCityId;

    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    @Column(name = "name")
    private String name;

    /**
     * 名称备注
     */
    @ApiModelProperty(value = "名称备注")
    @Column(name = "name_note")
    private String nameNote;

    /**
     * 个人姓名
     */
    @ApiModelProperty(value = "个人姓名")
    @Column(name = "personal_name")
    private String personalName;

    /**
     * 代理昵称
     */
    @ApiModelProperty(value = "代理昵称")
    @Column(name = "nick_name")
    private String nickName;

    /**
     * 性质：公司/个人/工作室/国际学校/其他
     */
    @ApiModelProperty(value = "性质：公司/个人/工作室/国际学校/其他")
    @Column(name = "nature")
    private String nature;

    /**
     * 性质备注
     */
    @ApiModelProperty(value = "性质备注")
    @Column(name = "nature_note")
    private String natureNote;

    /**
     * 法定代表人
     */
    @ApiModelProperty(value = "法定代表人")
    @Column(name = "legal_person")
    private String legalPerson;

    /**
     * 税号/统一社会信用代码（公司）
     */
    @ApiModelProperty(value = "税号/统一社会信用代码（公司）")
    @Column(name = "tax_code")
    private String taxCode;

    /**
     * 身份证号（个人）
     */
    @ApiModelProperty(value = "身份证号（个人）")
    @Column(name = "id_card_num")
    private String idCardNum;

    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    @Column(name = "address")
    private String address;

    /**
     * 合同开始时间
     */
    @ApiModelProperty(value = "合同开始时间")
    @Column(name = "contract_start_time")
    private Date contractStartTime;

    /**
     * 合同结束时间
     */
    @ApiModelProperty(value = "合同结束时间")
    @Column(name = "contract_end_time")
    private Date contractEndTime;

    /**
     * 合同佣金联系邮箱
     */
    @Column(name = "email_1")
    @ApiModelProperty(value = "合同佣金联系邮箱")
    @TableField("email_1")
    private String email1;

    /**
     * 业务新闻接收邮箱
     */
    @TableField("email_2")
    @ApiModelProperty(value = "业务新闻接收邮箱")
    @Column(name = "email_2")
    private String email2;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    /**
     * 申请状态：0新申请/1审核中/2通过/3拒绝
     */
    @ApiModelProperty(value = "申请状态：0新申请/1审核中/2通过/3拒绝")
    @Column(name = "app_status")
    private Integer appStatus;

    /**
     * 申请状态修改时间
     */
    @ApiModelProperty(value = "申请状态修改时间")
    @Column(name = "app_status_modify_time")
    private Date appStatusModifyTime;

    /**
     * 申请状态修改人(登录账号)
     */
    @ApiModelProperty(value = "申请状态修改人(登录账号)")
    @Column(name = "app_status_modify_user")
    private String appStatusModifyUser;

    /**
     * 转化到代理Id
     */
    @ApiModelProperty(value = "转化到代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:text &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:变更声明内容 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty(value = "变更声明内容")
    @JsonProperty("changeStatement")
    private String changeStatement;

    /**
     * <p> Java类型:Integer &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:int(10) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:申请类型：1新申请/2续签申请 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty(value = "申请类型：1新申请/2续签申请")
    @JsonProperty("appType")
    private Integer appType;

    @ApiModelProperty(value = "申请来源：0=网页申请1.0/1=网页申请2.0/2=小程序申请2.0")
    @JsonProperty("appFrom")
    private Integer appFrom;

    private static final long serialVersionUID = 1L;
}
