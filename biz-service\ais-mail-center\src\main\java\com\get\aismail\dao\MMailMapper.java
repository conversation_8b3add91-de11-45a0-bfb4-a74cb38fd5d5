package com.get.aismail.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.get.aismail.dto.MailDto;
import com.get.aismail.entity.MMail;
import com.get.aismail.vo.SearchMailVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DS("mail")
@Mapper
public interface MMailMapper extends BaseMapper<MMail> {
    IPage<MailDto> selectMail(Page<MailDto> page, @Param("searchMailVo") SearchMailVo searchMailVo);
    int selectNotRead(@Param("searchMailVo") SearchMailVo searchMailVo);
    IPage<MailDto> selectMailByIds(Page<MailDto> page, @Param("ids") List<Long> ids);
}
