package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.salecenter.dao.sale.MAppAgentSignatureMapper;
import com.get.salecenter.entity.MAppAgentSignatureEntity;
import com.get.salecenter.service.MAppAgentSignatureService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 *  app代理签名 服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Service
@RequiredArgsConstructor
public class MAppAgentSignatureServiceImpl extends ServiceImpl<MAppAgentSignatureMapper, MAppAgentSignatureEntity> implements MAppAgentSignatureService {

    private final MAppAgentSignatureMapper mAppAgentSignatureMapper;

}
 