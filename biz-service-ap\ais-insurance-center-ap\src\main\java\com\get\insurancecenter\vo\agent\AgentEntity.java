package com.get.insurancecenter.vo.agent;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class AgentEntity {

    @ApiModelProperty("学生代理Id")
    private Long id;

    @ApiModelProperty("学生代理父Id")
    private Long fkParentAgentId;

    @ApiModelProperty("国家Id")
    private Long fkAreaCountryId;

    @ApiModelProperty("州省Id")
    private Long fkAreaStateId;

    @ApiModelProperty("城市Id")
    private Long fkAreaCityId;

    @ApiModelProperty("代理编号")
    private String num;

    @ApiModelProperty("代理名称")
    private String name;

    @ApiModelProperty("名称备注")
    private String nameNote;

    @ApiModelProperty("个人姓名")
    private String personalName;

    @ApiModelProperty("代理昵称")
    private String nickName;

    @ApiModelProperty("性质：公司/个人/工作室/国际学校/其他")
    private String nature;

    @ApiModelProperty("性质备注")
    private String natureNote;

    @ApiModelProperty("法定代表人")
    private String legalPerson;

    @ApiModelProperty("税号/统一社会信用代码（公司）")
    private String taxCode;

    @ApiModelProperty("身份证号（个人）")
    private String idCardNum;

    @ApiModelProperty("联系地址")
    private String address;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("邀请码，8位数字或字母随机数")
    private String invitationCode;

    @ApiModelProperty("是否结算口，0否/1是")
    private Boolean isSettlementPort;

    @ApiModelProperty("是否关键代理：0否/1是")
    private Boolean isKeyAgent;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("关键代理失效时间")
    private Date keyAgentFailureTime;

    @ApiModelProperty("是否拒收系统邮件：0否/1是")
    private Boolean isRejectEmail;

    @ApiModelProperty("是否渠道代理：0否/1是")
    private Boolean isCustomerChannel;

    @ApiModelProperty("是否激活：0否/1是")
    private Boolean isActive;

    @ApiModelProperty("旧数据id(gea)")
    private String idGea;

    @ApiModelProperty("旧数据id(iae)")
    private String idIae;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    @ApiModelProperty("创建用户(登录账号)")
    private String gmtCreateUser;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("修改时间")
    private Date gmtModified;

    @ApiModelProperty("修改用户(登录账号)")
    private String gmtModifiedUser;
}
