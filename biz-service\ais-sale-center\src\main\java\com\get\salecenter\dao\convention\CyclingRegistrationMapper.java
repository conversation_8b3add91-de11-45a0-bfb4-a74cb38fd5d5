package com.get.salecenter.dao.convention;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.entity.CyclingRegistration;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("conventiondb")
public interface CyclingRegistrationMapper extends GetMapper<CyclingRegistration> {
    int insert(CyclingRegistration record);

    int insertSelective(CyclingRegistration record);

    int updateById(CyclingRegistration record);

    int updateByPrimaryKey(CyclingRegistration record);
}