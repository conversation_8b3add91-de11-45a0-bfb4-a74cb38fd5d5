package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2024/3/25
 * @TIME: 10:48
 * @Description:业务标记
 **/
@TableName("s_name_label")
@ApiModel(value="业务标记", description="")
@Data
public class NameLabel extends BaseEntity implements Serializable {
    @ApiModelProperty(value = "表名：m_institution学校")
    private String fkTableName;

    @ApiModelProperty(value = "表Id")
    private Long fkTableId;

    @ApiModelProperty(value = "0名字前面/1名字后面")
    private Integer positionType;

    @ApiModelProperty(value = "标签")
    private String label;

    @ApiModelProperty(value = "排序")
    private Integer viewOrder;
}
