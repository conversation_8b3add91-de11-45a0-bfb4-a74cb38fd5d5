package com.get.pmpcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  11:29
 * @Version 1.0
 */
@Data
@TableName("log_operation")
public class LogOperation extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "表名")
    private String fkTableName;

    @ApiModelProperty(value = "表Id")
    private Long fkTableId;

    @ApiModelProperty(value = "学校提供商合同Id")
    private Long fkInstitutionProviderContractId;

    @ApiModelProperty(value = "操作类型")
    private String operationName;

    @ApiModelProperty(value = "操作描述")
    private String operationDescription;
}