package get.middlecenter.component;

import com.get.aismiddle.dto.UploadMediaAndAttachedRequestDto;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.api.Result;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.salecenter.vo.MediaAndAttachedVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 销售中心上传组件类
 */
@Slf4j
@Component("saleUploadAbstractHelper")
public class SaleUploadAbstractHelper extends UploadAbstractHelper {

    @Resource
    private ISaleCenterClient saleCenterClient;

    @Override
    public void upload(UploadMediaAndAttachedRequestDto uploadMediaAndAttachedRequestDto) {
        MediaAndAttachedDto mediaAndAttachedDto = new MediaAndAttachedDto();
        try {
            BeanUtils.copyProperties(uploadMediaAndAttachedRequestDto, mediaAndAttachedDto);
        } catch (Exception e) {
            throw new GetServiceException(e.getMessage());
        }
        mediaAndAttachedDto.setGmtCreate(new Date());
        Result<List<MediaAndAttachedVo>> result = saleCenterClient.addMediaAndAttachedList(Collections.singletonList(mediaAndAttachedDto));
        if (Result.isNotSuccess(result)) {
            throw new GetServiceException(result.getMessage());
        }
    }

}
