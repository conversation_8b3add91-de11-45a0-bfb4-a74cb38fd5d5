package com.get.schoolGateCenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2020/8/24
 * @TIME: 18:01
 * @Description:
 **/
@Data
public class ProviderInstitutionRelationDto extends BaseVoEntity {

    /**
     * 学校编号
     */
    @ApiModelProperty(value = "学校编号")
    private String num;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String name;

    /**
     * 国家Name
     */
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;

    /**
     * 标记
     */
    @ApiModelProperty(value = "是否选中")
    private Boolean flag;

    @ApiModelProperty(value = "国家Id", required = true)
    private Long fkAreaCountryId;

    /**
     * 类型Name
     */
    @ApiModelProperty(value = "学校类型名称")
    private String fkInstitutionTypeName;

}
