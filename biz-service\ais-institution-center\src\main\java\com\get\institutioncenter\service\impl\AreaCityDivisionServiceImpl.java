package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.AreaCityDivisionMapper;
import com.get.institutioncenter.dto.AreaCityDivisionDto;
import com.get.institutioncenter.vo.AreaCityDivisionVo;
import com.get.institutioncenter.entity.AreaCityDivision;
import com.get.institutioncenter.service.IAreaCityDivisionService;
import com.get.institutioncenter.service.IAreaCityService;
import com.get.institutioncenter.service.ITranslationMappingService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * @author: Sea
 * @create: 2021/3/3 14:45
 * @verison: 1.0
 * @description:
 */
@Service
public class AreaCityDivisionServiceImpl extends BaseServiceImpl<AreaCityDivisionMapper, AreaCityDivision> implements IAreaCityDivisionService {
    @Resource
    private AreaCityDivisionMapper areaCityDivisionMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IAreaCityService areaCityService;
    @Resource
    private ITranslationMappingService translationMappingService;


    @Override
    public AreaCityDivisionVo findAreaCityDivisionById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AreaCityDivision areaCityDivision = areaCityDivisionMapper.selectById(id);
        if (GeneralTool.isEmpty(areaCityDivision)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        AreaCityDivisionVo areaCityDivisionVo = BeanCopyUtils.objClone(areaCityDivision, AreaCityDivisionVo::new);
        areaCityDivisionVo.setAreaCityName(areaCityService.getCityFullNameById(areaCityDivisionVo.getFkAreaCityId()));
        //语言
        areaCityDivisionVo.setFkTableName(TableEnum.INSTITUTION_AREA_CITY_DIVISION.key);
        return areaCityDivisionVo;
    }

    @Override
    public Long add(AreaCityDivisionDto areaCityDivisionDto) {
        if (GeneralTool.isEmpty(areaCityDivisionDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        AreaCityDivision areaCityDivision = BeanCopyUtils.objClone(areaCityDivisionDto, AreaCityDivision::new);
        areaCityDivision.setViewOrder(areaCityDivisionMapper.getMaxViewOrder());
        utilService.updateUserInfoToEntity(areaCityDivision);
        areaCityDivisionMapper.insertSelective(areaCityDivision);
        return areaCityDivision.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (areaCityDivisionMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        areaCityDivisionMapper.deleteById(id);

        //删除翻译内容
        translationMappingService.deleteTranslations(TableEnum.INSTITUTION_AREA_CITY_DIVISION.key, id);
    }

    /**
     * 查询城市下面的区域
     *
     * @param fkAreaCityId
     * @return
     */
    @Override
    public List<AreaCityDivisionVo> getByFkAreaCityId(Long fkAreaCityId) {
        List<AreaCityDivisionVo> areaCityDivisionVos = new ArrayList<>();
        if (GeneralTool.isEmpty(fkAreaCityId)) {
            return areaCityDivisionVos;
        }
        LambdaQueryWrapper<AreaCityDivision> wrapper = new LambdaQueryWrapper();
        wrapper.eq(AreaCityDivision::getFkAreaCityId, fkAreaCityId);
        List<AreaCityDivision> areaCityDivisions = areaCityDivisionMapper.selectList(wrapper);
        for (AreaCityDivision areaCityDivision : areaCityDivisions) {
            AreaCityDivisionVo areaCityDivisionVo = new AreaCityDivisionVo();
            BeanUtils.copyProperties(areaCityDivision, areaCityDivisionVo);
            areaCityDivisionVos.add(areaCityDivisionVo);
        }
        return areaCityDivisionVos;
    }

    @Override
    public AreaCityDivisionVo updateAreaCityDivision(AreaCityDivisionDto areaCityDivisionDto) {
        if (areaCityDivisionDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        AreaCityDivision result = areaCityDivisionMapper.selectById(areaCityDivisionDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        AreaCityDivision areaCityDivision = BeanCopyUtils.objClone(areaCityDivisionDto, AreaCityDivision::new);
        utilService.updateUserInfoToEntity(areaCityDivision);
        areaCityDivisionMapper.updateById(areaCityDivision);
        return findAreaCityDivisionById(areaCityDivision.getId());
    }

    @Override
    public Map<Long, String> getCityDivisionFullNamesByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        LambdaQueryWrapper<AreaCityDivision> wrapper = new LambdaQueryWrapper();
        wrapper.in(AreaCityDivision::getId, ids);
        List<AreaCityDivision> areaCityDivisions = areaCityDivisionMapper.selectList(wrapper);
        for (AreaCityDivision areaCityDivision : areaCityDivisions) {
            String name = GeneralTool.isEmpty(areaCityDivision.getName()) ? "" : areaCityDivision.getName();
            StringBuilder builder = new StringBuilder(name);
            if (GeneralTool.isNotEmpty(areaCityDivision.getStreetsName())) {
                builder.append("（");
                builder.append(areaCityDivision.getStreetsName());
                builder.append("）");
            }
            map.put(areaCityDivision.getId(), builder.toString());
        }
        return map;
    }

    @Override
    public List<AreaCityDivisionVo> getAreaCityDivisions(AreaCityDivisionDto areaCityDivisionDto, Page page) {
        LambdaQueryWrapper<AreaCityDivision> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(areaCityDivisionDto)) {
            wrapper.eq(AreaCityDivision::getFkAreaCityId, areaCityDivisionDto.getFkAreaCityId());
            if (GeneralTool.isNotEmpty(areaCityDivisionDto.getName())) {
                wrapper.like(AreaCityDivision::getName, areaCityDivisionDto.getName());
            }
        }
        wrapper.orderByDesc(AreaCityDivision::getViewOrder);
        //获取分页数据
        IPage<AreaCityDivision> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<AreaCityDivision> areaCityDivisions = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<AreaCityDivisionVo> convertDatas = new ArrayList<>();
        for (AreaCityDivision areaCityDivision : areaCityDivisions) {
            AreaCityDivisionVo areaCityDivisionVo = BeanCopyUtils.objClone(areaCityDivision, AreaCityDivisionVo::new);
            //语言
            areaCityDivisionVo.setFkTableName(TableEnum.INSTITUTION_AREA_CITY_DIVISION.key);
            convertDatas.add(areaCityDivisionVo);
        }
        return convertDatas;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<AreaCityDivisionDto> areaCityDivisionDtos) {
        if (GeneralTool.isEmpty(areaCityDivisionDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        AreaCityDivision ro = BeanCopyUtils.objClone(areaCityDivisionDtos.get(0), AreaCityDivision::new);
        Integer oneorder = ro.getViewOrder();
        AreaCityDivision rt = BeanCopyUtils.objClone(areaCityDivisionDtos.get(1), AreaCityDivision::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        areaCityDivisionMapper.updateById(ro);
        areaCityDivisionMapper.updateById(rt);
    }

}
