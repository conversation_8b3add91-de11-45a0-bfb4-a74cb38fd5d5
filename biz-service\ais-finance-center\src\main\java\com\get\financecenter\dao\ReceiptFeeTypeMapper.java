package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.entity.ReceiptFeeType;
import com.get.financecenter.vo.BaseSelectVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ReceiptFeeTypeMapper extends BaseMapper<ReceiptFeeType> {

    /**
     * 获取最大排序值
     *
     * @return
     */
    Integer getMaxViewOrder();

    /**
     * 收款费用类型下拉
     *
     * @return
     */
    List<BaseSelectVo> getReceiptFeeTypeSelect();


    String getNameById(@Param("id") Long id);

    ReceiptFeeType selectByFkAccountingItemId(Long id);

    int checkName(@Param("typeName") String typeName);
}