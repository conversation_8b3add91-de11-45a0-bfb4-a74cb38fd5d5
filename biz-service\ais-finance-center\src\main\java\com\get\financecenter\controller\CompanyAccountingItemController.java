package com.get.financecenter.controller;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.financecenter.dto.CompanyAccountingDto;
import com.get.financecenter.dto.CompanyAccountingItemDto;
import com.get.financecenter.dto.CompanyAccountingItemOperateDto;
import com.get.financecenter.service.ICompanyAccountingItemService;
import com.get.financecenter.vo.CompanyAccountingItemVo;
import com.get.financecenter.vo.CompanyAccountingVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 账套科目控制器
 */
@Api(tags = "账套科目管理")
@RestController
@RequestMapping("finance/companyAccountingItem")
public class CompanyAccountingItemController{
    @Resource
    private ICompanyAccountingItemService companyAccountingItemService;

    @ApiOperation(value = "查询所有公司数据", notes = "查询所有公司数据")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/账套科目管理/查询所有公司数据")
    @PostMapping("datas")
    public ResponseBo<CompanyAccountingVo> getCompanyAccountingDatas(@RequestBody SearchBean<CompanyAccountingDto> page) {
        List<CompanyAccountingVo> datas = companyAccountingItemService.getCompanyAccountingDatas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "分页查询所有数据", notes = "分页查询所有数据")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/账套科目管理/分页查询所有数据")
    @PostMapping("list")
    public ResponseBo<CompanyAccountingItemVo> getCompanyAccounting(@RequestBody SearchBean<CompanyAccountingItemDto> page) {
        List<CompanyAccountingItemVo> datas = companyAccountingItemService.getCompanyAccountingItem(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    @ApiOperation(value = "添加", notes = "添加")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/账套科目管理/添加")
    @PostMapping("add")
    public ResponseBo<String> add(@RequestBody CompanyAccountingItemDto companyAccountingItemDto) {
        companyAccountingItemService.save(companyAccountingItemDto);
        return ResponseBo.ok();
    }


    @ApiOperation(value = "批量操作科目", notes = "批量操作科目")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/账套科目管理/批量操作科目")
    @PostMapping("batchOperation")
    public ResponseBo batchOperation(@RequestBody CompanyAccountingItemOperateDto companyAccountingItemOperateDto) {
        companyAccountingItemService.batchOperation(companyAccountingItemOperateDto);
        return ResponseBo.ok();

    }

    @ApiOperation(value = "修改", notes = "修改")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/账套科目管理/修改")
    @PostMapping("update")
    public ResponseBo<String> update(@RequestBody CompanyAccountingItemDto companyAccountingItemDto) {
        companyAccountingItemService.updateById(companyAccountingItemDto);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "删除", notes = "删除")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/账套科目管理/删除")
    @PostMapping("delete")
    public ResponseBo<String> delete(@RequestParam("id") Long id) {
        companyAccountingItemService.delete(id);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "查询所有数据已绑定的科目", notes = "查询所有数据")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/账套科目管理/查询所有数据已绑定的科目")
    @GetMapping("listBind")
    public ResponseBo<Long> listBind(@RequestParam("fkCompanyId") Long fkCompanyId) {
        return new ListResponseBo<>(companyAccountingItemService.getCompanyProfitAndLossItemBind(fkCompanyId));
    }

}

