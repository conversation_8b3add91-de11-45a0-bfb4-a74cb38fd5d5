package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseSelectEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgentAndAgentLabelVo extends BaseSelectEntity {
    @ApiModelProperty(value = "代理id")
    private Long fkAgentId;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty("代理标签")
    private List<AgentLabelVo> agentLabelVos;

}
