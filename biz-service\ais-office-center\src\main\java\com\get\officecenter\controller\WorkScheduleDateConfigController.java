package com.get.officecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.officecenter.vo.WorkScheduleDateConfigVo;
import com.get.officecenter.service.WorkScheduleDateConfigService;
import com.get.officecenter.dto.WorkScheduleDateConfigDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/1/11
 * @TIME: 10:30
 * @Description:排班日期设定控制器
 **/
@Api(tags = "排班日期设定管理")
@RestController
@RequestMapping("office/workScheduleDateConfig")
public class WorkScheduleDateConfigController {
    @Resource
    private WorkScheduleDateConfigService workScheduleDateConfigService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DETAIL, description = "办公中心/排班日期设定管理/排班日期设定详情")
    @GetMapping("/{id}")
    public ResponseBo<WorkScheduleDateConfigVo> detail(@PathVariable("id") Long id) {
        WorkScheduleDateConfigVo data = workScheduleDateConfigService.findWorkScheduleDateConfigById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 新增信息
     *
     * @param workScheduleDateConfigDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "办公中心/排班日期设定管理/新增排班日期设定")
    @PostMapping("add")
    public ResponseBo add(@RequestBody WorkScheduleDateConfigDto workScheduleDateConfigDto) {
        workScheduleDateConfigService.add(workScheduleDateConfigDto);
        return SaveResponseBo.ok();
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DELETE, description = "办公中心/排班日期设定管理/删除排班日期设定")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        workScheduleDateConfigService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param workScheduleDateConfigDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.EDIT, description = "办公中心/排班日期设定管理/更新排班日期设定")
    @PostMapping("update")
    public ResponseBo<WorkScheduleDateConfigVo> update(@RequestBody @Validated(WorkScheduleDateConfigDto.Update.class) WorkScheduleDateConfigDto workScheduleDateConfigDto) {
        return UpdateResponseBo.ok(workScheduleDateConfigService.updateWorkScheduleDateConfig(workScheduleDateConfigDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/排班日期设定管理/查询排班日期设定")
    @PostMapping("datas")
    public ResponseBo<WorkScheduleDateConfigVo> datas(@RequestBody SearchBean<WorkScheduleDateConfigDto> page) {
        List<WorkScheduleDateConfigVo> datas = workScheduleDateConfigService.getWorkScheduleDateConfigs(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 年份下拉
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "年份下拉", notes = "")
    @GetMapping("getYearSelect")
    public ResponseBo<BaseSelectEntity> getYearSelect(@RequestParam("fkCompanyId") Long fkCompanyId) {
        return new ListResponseBo<BaseSelectEntity>(workScheduleDateConfigService.getYearSelect(fkCompanyId));
    }

    /**
     * 全部排班数据
     *
     * @param workScheduleDateConfigDto
     * @return
     * @
     */
    @ApiOperation(value = "全部排班数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/排班日期设定管理/查询排班日期设定")
    @PostMapping("getAllData")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<WorkScheduleDateConfigVo> getAllData(@RequestBody WorkScheduleDateConfigDto workScheduleDateConfigDto) {
        List<WorkScheduleDateConfigVo> datas = workScheduleDateConfigService.getAllWorkScheduleDateConfigs(workScheduleDateConfigDto);
        return new ListResponseBo<>(datas);
    }

}
