package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.EventTypeMapper;
import com.get.salecenter.vo.EventTypeVo;
import com.get.salecenter.entity.EventType;
import com.get.salecenter.service.IDeleteService;
import com.get.salecenter.service.IEventTypeService;
import com.get.salecenter.dto.EventTypeDto;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: Sea
 * @create: 2020/12/7 11:27
 * @verison: 1.0
 * @description:
 */
@Service
public class EventTypeServiceImpl implements IEventTypeService {
    @Resource
    private EventTypeMapper eventTypeMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Override
    public EventTypeVo findEventTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        EventType eventType = eventTypeMapper.selectById(id);
        if (GeneralTool.isEmpty(eventType)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_relevant_information_found"));
        }
        EventTypeVo eventTypeVo = BeanCopyUtils.objClone(eventType, EventTypeVo::new);
        if (GeneralTool.isNotEmpty(eventType.getFkDepartmentIds())){
            eventTypeVo.setFkDepartmentIdList(Arrays.stream(eventType.getFkDepartmentIds().split(",")).map(Long::valueOf).collect(Collectors.toList()));
        }
        return eventTypeVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<EventTypeDto> eventTypeDtos) {
        if (GeneralTool.isEmpty(eventTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (EventTypeDto eventTypeDto : eventTypeDtos) {
            if (GeneralTool.isEmpty(eventTypeDto.getId())) {
                if (validateAdd(eventTypeDto)) {
                    //获取最大排序
                    eventTypeDto.setViewOrder(eventTypeMapper.getMaxViewOrder());
                    EventType eventType = BeanCopyUtils.objClone(eventTypeDto, EventType::new);
                    if(GeneralTool.isNotEmpty(eventTypeDto.getFkDepartmentIdList())){
                        String departmentIds = eventTypeDto.getFkDepartmentIdList().stream().map(String::valueOf).collect(Collectors.joining(","));
                        eventType.setFkDepartmentIds(departmentIds);
                    }
                    utilService.updateUserInfoToEntity(eventType);
                    int i = eventTypeMapper.insert(eventType);
                    if (i <= 0) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
                    }
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
                }
            } else {
                if (validateUpdate(eventTypeDto)) {
                    EventType eventType = BeanCopyUtils.objClone(eventTypeDto, EventType::new);
                    if(GeneralTool.isNotEmpty(eventTypeDto.getFkDepartmentIdList())){
                        String departmentIds = eventTypeDto.getFkDepartmentIdList().stream().map(String::valueOf).collect(Collectors.joining(","));
                        eventType.setFkDepartmentIds(departmentIds);
                    }else {
                        eventType.setFkDepartmentIds(null);
                    }
                    utilService.updateUserInfoToEntity(eventType);
                    int i = eventTypeMapper.updateByIdWithNull(eventType);
                    if (i <= 0) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
                    }
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
                }
            }

        }
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //删除校验
        deleteService.deleteValidateEventType(id);
        eventTypeMapper.deleteById(id);
    }

    @Override
    public EventTypeVo updateEventType(EventTypeDto eventTypeDto) {
        if (eventTypeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (validateUpdate(eventTypeDto)) {
            EventType eventType = BeanCopyUtils.objClone(eventTypeDto, EventType::new);
            if(GeneralTool.isNotEmpty(eventTypeDto.getFkDepartmentIdList())){
                String departmentIds = eventTypeDto.getFkDepartmentIdList().stream().map(String::valueOf).collect(Collectors.joining(","));
                eventType.setFkDepartmentIds(departmentIds);
            }else {
                eventType.setFkDepartmentIds(null);
            }
            utilService.updateUserInfoToEntity(eventType);
            int i = eventTypeMapper.updateByIdWithNull(eventType);
            if (i <= 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        return findEventTypeById(eventTypeDto.getId());
    }

    @Override
    public List<EventTypeVo> getEventTypes(EventTypeDto eventTypeDto, Page page) {
//        Example example = new Example(EventType.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(eventTypeDto)) {
//            if (GeneralTool.isNotEmpty(eventTypeDto.getKeyWord())) {
//                criteria.andLike("typeName", "%" + eventTypeDto.getKeyWord() + "%");
//            }
//        }
//        example.orderBy("viewOrder").desc();
//        List<EventType> eventTypes = eventTypeMapper.selectByExample(example);
//        page.restPage(eventTypes);

        LambdaQueryWrapper<EventType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(eventTypeDto)) {
            if (GeneralTool.isNotEmpty(eventTypeDto.getKeyWord())) {
                lambdaQueryWrapper.like(EventType::getTypeName, eventTypeDto.getKeyWord());
            }
        }
        lambdaQueryWrapper.orderByDesc(EventType::getViewOrder);
        List<EventType> eventTypes = eventTypeMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isEmpty(eventTypes)){
            return Collections.emptyList();
        }
        Set<Long> departmentIds = eventTypes.stream().flatMap(eventType -> {
            if (GeneralTool.isNotEmpty(eventType.getFkDepartmentIds())){
                return Arrays.stream(eventType.getFkDepartmentIds().split(","));
            }
            return Stream.empty();
        }).filter(Objects::nonNull).map(Long::valueOf).collect(Collectors.toSet());
        Map<Long, String> departmentNameMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(departmentIds)){
            departmentNameMap = permissionCenterClient.getDepartmentNamesByIds(departmentIds).getData();
        }
        List<EventTypeVo> eventTypeVos = BeanCopyUtils.copyListProperties(eventTypes, EventTypeVo::new);
        for (EventTypeVo eventTypeVo : eventTypeVos) {
            if (GeneralTool.isNotEmpty(eventTypeVo.getFkDepartmentIds())){
                String departmentName = Arrays.stream(eventTypeVo.getFkDepartmentIds().split(",")).filter(Objects::nonNull).map(Long::valueOf).map(departmentNameMap::get).collect(Collectors.joining(","));
                eventTypeVo.setFkDepartmentNames(departmentName);
            }
        }
        return eventTypeVos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<EventTypeDto> eventTypeDtos) {
        if (GeneralTool.isEmpty(eventTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        EventType ro = BeanCopyUtils.objClone(eventTypeDtos.get(0), EventType::new);
        Integer oneorder = ro.getViewOrder();
        EventType rt = BeanCopyUtils.objClone(eventTypeDtos.get(1), EventType::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        eventTypeMapper.updateById(ro);
        eventTypeMapper.updateById(rt);
    }

    @Override
    public List<EventTypeVo> getEventTypeList() {
//        Example example = new Example(EventType.class);
//        example.orderBy("viewOrder").desc();
//        List<EventType> eventTypes = eventTypeMapper.selectByExample(example);
        //获取下属员工所有部门
        Set<Long> fkDepartIds = permissionCenterClient.getStaffDepartmentsById(SecureUtil.getStaffId()).getData();
        List<EventType> eventTypes = eventTypeMapper.getEventTypeList(fkDepartIds);
//        List<EventType> eventTypes = eventTypeMapper.selectList(Wrappers.<EventType>lambdaQuery()
//                .and(wrapper->wrapper
//                        .apply("FIND_IN_SET ('" +fkDepartIdString + "',fk_department_ids)")
//                        .or()
//                        .isNull(EventType::getFkDepartmentIds))
//                .orderByDesc(EventType::getViewOrder));
        return BeanCopyUtils.copyListProperties(eventTypes, EventTypeVo::new);
    }

    @Override
    public String getEventTypeNameById(Long eventTypeId) {
        return eventTypeMapper.getEventTypeNameById(eventTypeId);
    }

    private boolean validateAdd(EventTypeDto eventTypeDto) {
//        Example example = new Example(EventType.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("typeName", eventTypeDto.getTypeName());
//        List<EventType> list = this.eventTypeMapper.selectByExample(example);
        List<EventType> list = this.eventTypeMapper.selectList(Wrappers.<EventType>lambdaQuery().eq(EventType::getTypeName, eventTypeDto.getTypeName()));
        return GeneralTool.isEmpty(list);
    }

    private boolean validateUpdate(EventTypeDto eventTypeDto) {
//        Example example = new Example(EventType.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("typeName", eventTypeDto.getTypeName());
//        List<EventType> list = this.eventTypeMapper.selectByExample(example);
        List<EventType> list = this.eventTypeMapper.selectList(Wrappers.<EventType>lambdaQuery().eq(EventType::getTypeName, eventTypeDto.getTypeName()));
        return list.size() <= 0 || list.get(0).getId().equals(eventTypeDto.getId());
    }
}
