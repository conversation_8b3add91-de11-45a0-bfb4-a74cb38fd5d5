package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.salecenter.vo.BusinessChannelVo;
import com.get.salecenter.service.IBusinessChannelService;
import com.get.salecenter.dto.BusinessChannelDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2022/1/7
 * @TIME: 18:14
 * @Description:
 **/
@Api(tags = "业务渠道管理")
@RestController
@RequestMapping("sale/businessChannel")
public class BusinessChannelController {
    @Resource
    private IBusinessChannelService businessChannelService;


    @ApiOperation(value = "保存提供商-公司配置接口")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER)
    @PostMapping("editChannelCompanyRelation")
    public ResponseBo editChannelCompanyRelation(@RequestBody
                                                  ValidList<BusinessChannelDto> validList) {
        businessChannelService.editChannelCompanyRelation(validList);
        return UpdateResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "显示选择的公司", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER)
    @PostMapping("getChannelCompanyRelation/{channelId}")
    public ResponseBo<CompanyTreeVo> getChannelCompanyRelation(@PathVariable("channelId")Long channelId) {
        return new ListResponseBo<>(businessChannelService.getChannelCompanyRelation(channelId));
    }


    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "所有公司", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER)
    @PostMapping("allCompany")
    public ResponseBo<List<CompanyTreeVo>> allCompany(@RequestParam(name = "companyId", required = false) Long companyId) {

        return new ResponseBo<>(businessChannelService.allCompany(companyId));
    }

    /**
     * @return com.get.common.result.ListResponseBo<com.get.salecenter.vo.AgentEventTypeVo>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/业务渠道管理/查询列表")
    @PostMapping("datas")
    public ListResponseBo<BusinessChannelVo> datas(@RequestBody SearchBean<BusinessChannelDto> page) {
        List<BusinessChannelVo> datas = businessChannelService.getBusinessChannelDtos(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [agentEventVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/业务渠道管理/新增业务渠道")
    @PostMapping("add")
    public ResponseBo add(@RequestBody  @Validated(BusinessChannelDto.Add.class) ValidList<BusinessChannelDto> businessChannelDto) {
        businessChannelService.addBusinessChannel(businessChannelDto);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AgentEventTypeVo>
     * @Description: 修改信息
     * @Param [agentEventVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/业务渠道管理/更新")
    @PostMapping("update")
    public ResponseBo<BusinessChannelVo> update(@RequestBody  @Validated(BusinessChannelDto.Update.class)  BusinessChannelDto businessChannelDto) {
        return UpdateResponseBo.ok(businessChannelService.updateBusinessChannel(businessChannelDto));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/业务渠道管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        businessChannelService.deleteBusinessChannel(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "类型下拉框", notes = "")
    @GetMapping("getTypeKey")
    public ResponseBo getTypeKey() {
        List<Map<String, Object>> datas = businessChannelService.getModuleKey();
        return new ListResponseBo<>(datas);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.officecenter.vo.LeaveApplicationFormTypeVo>
     * @Description :渠道下拉框数据
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "渠道下拉框数据", notes = "")
    @GetMapping("channelSelect")
    public ResponseBo<BaseSelectEntity> channelSelect(@RequestParam(value = "tableName") String tableName,@RequestParam(value = "companyId") Long companyId) {
        List<BaseSelectEntity> list = businessChannelService.channelSelect(tableName,companyId);
        return new ListResponseBo<>(list);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.officecenter.vo.LeaveApplicationFormTypeVo>
     * @Description :渠道下拉框数据
     * @Param []
     * <AUTHOR>
     */
    @ApiIgnore
    @ApiOperation(value = "目标名称下拉框", notes = "")
    @GetMapping("getTargetName")
    public ResponseBo<BaseSelectEntity> getTargetName(@RequestParam(value = "companyId") Long companyId, @RequestParam(value = "tableName") String tableName) {
        List<BaseSelectEntity> list = businessChannelService.getTargetName(tableName, companyId);
        return new ListResponseBo<>(list);
    }


    /**
     * feign调用 根据输入的渠道名称 模糊查询对应的id
     *
     * @param channelName
     * @return
     */
    @ApiIgnore
    @GetMapping("getChannelIds")
    public List<Long> getChannelIds(@RequestParam(required = false, value = "tableName") String tableName, @RequestParam(value = "channelName") String channelName) {
        return businessChannelService.getChannelIds(tableName, channelName);
    }

    /**
     * @return java.util.Map<java.lang.Long
            * java.lang.String>
     * @Description :fegin调用 根据学校提供商ids 查询名称map
     * @Param [ids]
     * <AUTHOR>
     */
    @ApiIgnore
    @GetMapping("getChannelNamesByIds")
    public Map<Long, String> getChannelNamesByIds(@RequestParam(value = "ids") Set<Long> ids) {
        return businessChannelService.getChannelNamesByIds(ids);
    }

}
