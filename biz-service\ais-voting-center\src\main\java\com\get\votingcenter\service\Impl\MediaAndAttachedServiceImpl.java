package com.get.votingcenter.service.Impl;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.votingcenter.dao.voting.MediaAndAttachedMapper;
import com.get.votingcenter.vo.VotingMediaAndAttachedVo;
import com.get.votingcenter.entity.VotingMediaAndAttached;
import com.get.votingcenter.service.IMediaAndAttachedService;
import com.get.votingcenter.dto.MediaAndAttachedDto;
import com.google.common.collect.Lists;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

;

/**
 * @author: Hardy
 * @create: 2021/9/26 17:22
 * @verison: 1.0
 * @description:
 */
@Service
public class MediaAndAttachedServiceImpl implements IMediaAndAttachedService {

    @Resource
    private IFileCenterClient fileCenterClient;
    @Resource
    private MediaAndAttachedMapper mediaAndAttachedMapper;
    @Resource
    private UtilService utilService;

    @Override
    public List<FileDto> upload(MultipartFile[] multipartFiles, String type) {
        if (GeneralTool.isEmpty(multipartFiles)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }
        List<FileDto> fileDtos = null;
        Result<List<FileDto>> fileDtoResult = fileCenterClient.upload(multipartFiles, type);
        if (fileDtoResult.isSuccess() && GeneralTool.isNotEmpty(fileDtoResult.getData())) {
            JSONArray jsonArray = JSONArray.fromObject(fileDtoResult.getData());
            fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_upload_fail"));
        }
        return fileDtos;
    }

    @Override
    public List<FileDto> uploadAppendix(MultipartFile[] multipartFiles, String type) {
        if (GeneralTool.isEmpty(multipartFiles)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }
        List<FileDto> fileDtos = null;
        Result<List<FileDto>> fileDtoResult = fileCenterClient.uploadAppendix(multipartFiles, type);
        if (fileDtoResult.isSuccess() && GeneralTool.isNotEmpty(fileDtoResult.getData())) {
            JSONArray jsonArray = JSONArray.fromObject(fileDtoResult.getData());
            fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_upload_fail"));
        }
        return fileDtos;
    }

    @Override
    public List<VotingMediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto mediaAndAttachedDto) {
        List<VotingMediaAndAttached> votingMediaAndAttacheds = getMediaAndAttacheds(mediaAndAttachedDto);
        return getFileMedia(votingMediaAndAttacheds);
    }

    /**
     * @Description:保存文件
     * @Param
     * @Date 12:28 2021/5/12
     * <AUTHOR>
     */
    @Override
    public VotingMediaAndAttachedVo addMediaAndAttached(MediaAndAttachedDto mediaAttachedVo) {
        if (GeneralTool.isEmpty(mediaAttachedVo.getFkTableName())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("tableName_null"));
        }
        VotingMediaAndAttached andAttached = BeanCopyUtils.objClone(mediaAttachedVo, VotingMediaAndAttached::new);
        Integer nextIndexKey = mediaAndAttachedMapper.getNextIndexKey(andAttached.getFkTableId(), mediaAttachedVo.getFkTableName());
        nextIndexKey = GeneralTool.isNotEmpty(nextIndexKey) ? nextIndexKey : 0;
        //实例化对象
        andAttached.setIndexKey(nextIndexKey);
        utilService.updateUserInfoToEntity(andAttached);
        mediaAndAttachedMapper.insertSelective(andAttached);
        VotingMediaAndAttachedVo mediaAndAttachedDto = BeanCopyUtils.objClone(andAttached, VotingMediaAndAttachedVo::new);
        mediaAndAttachedDto.setFilePath(mediaAttachedVo.getFilePath());
        mediaAndAttachedDto.setFileNameOrc(mediaAttachedVo.getFileNameOrc());
        mediaAndAttachedDto.setTypeValue(FileTypeEnum.getValue(mediaAttachedVo.getTypeKey()));
        mediaAndAttachedDto.setId(andAttached.getId());
        mediaAndAttachedDto.setFileKey(mediaAttachedVo.getFileKey());
        return mediaAndAttachedDto;
    }

    /**
     * 获取文件
     *
     * @param votingMediaAndAttachedList
     * @return
     */
    private List<VotingMediaAndAttachedVo> getFileMedia(List<VotingMediaAndAttached> votingMediaAndAttachedList) {
        if (GeneralTool.isEmpty(votingMediaAndAttachedList)) {
            return null;
        }
        //获取guid集合
        List<String> guidList = votingMediaAndAttachedList.stream().map(VotingMediaAndAttached::getFkFileGuid).collect(Collectors.toList());
        //转换成DTO
        List<VotingMediaAndAttachedVo> mediaAndAttachedDtos = votingMediaAndAttachedList.stream()
                .map(mediaAndAttached -> BeanCopyUtils.objClone(mediaAndAttached, VotingMediaAndAttachedVo::new)).collect(Collectors.toList());
        //根据GUID服务调用查询
//        ListResponseBo responseBo = fileCenterClient.findFileByGuid(guidList, LoggerModulesConsts.VOTINGCENTER);
//        JSONArray jsonArray = JSONArray.fromObject(responseBo.getDatas());
//        List<FileDto> fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());

        List<FileDto> fileDtos = Lists.newArrayList();
        Map<String, List<String>> guidListWithTypeMap = new HashMap<>();
        guidListWithTypeMap.put(LoggerModulesConsts.VOTINGCENTER, guidList);
        Result<List<FileDto>> result = fileCenterClient.findFileByGuid(guidListWithTypeMap);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            JSONArray jsonArray = JSONArray.fromObject(result.getData());
            fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
        }
        //返回结果不为空时
        List<VotingMediaAndAttachedVo> collect = null;
        if (GeneralTool.isNotEmpty(fileDtos)) {
            //遍历查询GUID是否一致
            List<FileDto> finalFileDtos = fileDtos;
            collect = mediaAndAttachedDtos.stream().map(mediaAndAttachedDto -> finalFileDtos
                    .stream()
                    .filter(fileDto -> fileDto.getFileGuid().equals(mediaAndAttachedDto.getFkFileGuid()))
                    .findFirst()
                    .map(fileDto -> {
                        mediaAndAttachedDto.setFilePath(fileDto.getFilePath());
                        mediaAndAttachedDto.setFileNameOrc(fileDto.getFileNameOrc());
                        mediaAndAttachedDto.setFileKey(fileDto.getFileKey());
                        mediaAndAttachedDto.setTypeValue(FileTypeEnum.getValue(mediaAndAttachedDto.getTypeKey()));
                        /*mediaAndAttachedDto.setFkTableName(null);*/
                        return mediaAndAttachedDto;
                    }).orElse(null)
            ).collect(Collectors.toList());
        }
        return collect;
    }

    /**
     * 获取媒体附件
     *
     * @param mediaAndAttachedDto
     * @return
     * @throws GetServiceException
     */
    private List<VotingMediaAndAttached> getMediaAndAttacheds(MediaAndAttachedDto mediaAndAttachedDto) {

        if (GeneralTool.isEmpty(mediaAndAttachedDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        List<VotingMediaAndAttached> votingMediaAndAttachedList = mediaAndAttachedMapper.getMediaAndAttacheds(mediaAndAttachedDto);
//        Example example = new Example(MediaAndAttached.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(mediaAndAttachedDto.getTypeKey())) {
//            criteria.andEqualTo("typeKey", mediaAndAttachedDto.getTypeKey());
//        }
//        criteria.andEqualTo("fkTableName", mediaAndAttachedDto.getFkTableName());
//        criteria.andEqualTo("fkTableId", mediaAndAttachedDto.getFkTableId());
//        example.orderBy("indexKey").desc();

//        return mediaAndAttachedMapper.selectByExample(example);
        return votingMediaAndAttachedList;
    }
}
