package com.get.partnercenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.partnercenter.dto.DeletePatchParamsDto;
import com.get.partnercenter.dto.HighCommissionPutAwayParamsDto;
import com.get.partnercenter.vo.HighCommissionComboxVo;
import com.get.partnercenter.vo.HighCommissionVo;
import com.get.partnercenter.entity.MInstitutionHighCommissionEntity;
import com.get.partnercenter.dto.HighCommissionDto;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_institution_high_commission(学校高佣表)】的数据库操作Service
* @createDate 2024-12-05 11:23:44
*/
public interface MInstitutionHighCommissionService extends IService<MInstitutionHighCommissionEntity> {

    /**
     * 高佣列表查询
     * @param params
     * @param page
     * @return
     */
    List<HighCommissionVo> searchPage(HighCommissionDto params, Page page);

    /**
     * 新增或保存高佣
     * @param dto
     * @return
     */
    Long saveOrUpdateHightCommission(HighCommissionDto dto);

    /**
     * 上下架
     * @param dto
     * @return
     */
    Long putAway( HighCommissionPutAwayParamsDto dto);
    void deleteBatch(DeletePatchParamsDto patchVo);

    /**
     * 高佣详细
     * @param id
     * @return
     */
    HighCommissionVo  getDetail(Long id);


    List<HighCommissionComboxVo> searchList();
}
