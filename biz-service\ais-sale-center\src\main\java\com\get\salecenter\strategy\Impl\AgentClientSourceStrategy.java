package com.get.salecenter.strategy.Impl;

import com.get.common.eunms.ProjectKeyEnum;
import com.get.salecenter.service.IClientSourceService;
import com.get.salecenter.strategy.ClientSourceStrategy;
import com.get.salecenter.dto.ClientSourceAddDto;
import org.springframework.stereotype.Component;

/**
 * @author: Hardy
 * @create: 2024/1/31 17:09
 * @verison: 1.0
 * @description:
 */
@Component
public class AgentClientSourceStrategy implements ClientSourceStrategy {

    private static final String TYPE = ProjectKeyEnum.CLIENT_SOURCE_TYPE_AGENT.key;

    @Override
    public String getType(){
        return TYPE;
    }
    @Override
    public void addClientSource(ClientSourceAddDto clientSourceAddDto, IClientSourceService clientSourceService) {
        clientSourceService.addAgentClientSource(clientSourceAddDto);
    }
}
