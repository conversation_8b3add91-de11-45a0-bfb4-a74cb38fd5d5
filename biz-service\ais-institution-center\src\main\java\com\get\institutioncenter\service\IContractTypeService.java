package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.core.mybatis.utils.ValidList;
import com.get.institutioncenter.vo.ContractTypeVo;
import com.get.institutioncenter.entity.ContractType;
import com.get.institutioncenter.dto.ContractTypeDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/29 10:27
 * @verison: 1.0
 * @description: 合同类型管理接口
 */
public interface IContractTypeService extends BaseService<ContractType> {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    ContractTypeVo findContractTypeById(Long id);

    /**
     * 批量新增
     *
     * @param contractTypeDtos
     * @return
     */
    void batchAdd(ValidList<ContractTypeDto> contractTypeDtos);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    void delete(Long id);

    /**
     * 修改
     *
     * @param contractTypeDto
     * @return
     */
    ContractTypeVo updateContractType(ContractTypeDto contractTypeDto);


    /**
     * 列表
     *
     * @param contractTypeDto
     * @param page
     * @return
     */
    List<ContractTypeVo> getContractTypes(ContractTypeDto contractTypeDto, Page page);

    /**
     * 上移下移
     *
     * @param contractTypeDtos
     * @return
     */
    void movingOrder(List<ContractTypeDto> contractTypeDtos);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.ContractTypeVo>
     * @Description: 合同类型下拉
     * @Param []
     * <AUTHOR>
     **/
    List<ContractTypeVo> getContractTypeSelect();

}