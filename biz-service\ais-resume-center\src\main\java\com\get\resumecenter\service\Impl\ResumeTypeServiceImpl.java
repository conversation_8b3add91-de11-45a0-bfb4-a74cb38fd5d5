package com.get.resumecenter.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.resumecenter.dao.ResumeTypeMapper;
import com.get.resumecenter.vo.ResumeTypeVo;
import com.get.resumecenter.entity.ResumeType;
import com.get.resumecenter.service.IDeleteService;
import com.get.resumecenter.service.IResumeTypeService;
import com.get.resumecenter.dto.ResumeTypeDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/30
 * @TIME: 15:49
 * @Description:
 **/
@Service
public class ResumeTypeServiceImpl implements IResumeTypeService {
    @Resource
    private ResumeTypeMapper resumeTypeMapper;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private UtilService utilService;

    @Override
    public List<ResumeTypeVo> datas(ResumeTypeDto resumeTypeDto) {
//        Example example = new Example(ResumeType.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<ResumeType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(resumeTypeDto)) {
            if (GeneralTool.isNotEmpty(resumeTypeDto.getTypeName())) {
//                criteria.andEqualTo("typeName", resumeTypeDto.getTypeName());
                lambdaQueryWrapper.eq(ResumeType::getTypeName, resumeTypeDto.getTypeName());
            }
            lambdaQueryWrapper.orderByDesc(ResumeType::getViewOrder);
        }
        List<ResumeType> resumetypes = resumeTypeMapper.selectList(lambdaQueryWrapper);
        List<ResumeTypeVo> convertDatas = new ArrayList<>();
        for (ResumeType resumeType : resumetypes) {
            ResumeTypeVo resumeTypeVo = BeanCopyUtils.objClone(resumeType, ResumeTypeVo::new);
            convertDatas.add(resumeTypeVo);
        }
        return convertDatas;
    }

    @Override
    public ResumeTypeVo findResumeTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ResumeType resumeType = resumeTypeMapper.selectById(id);
        return BeanCopyUtils.objClone(resumeType, ResumeTypeVo::new);
    }

    @Override
    public ResumeTypeVo updateResumeType(ResumeTypeDto resumeTypeDto) {
        if (resumeTypeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(resumeTypeDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ResumeType rs = this.resumeTypeMapper.selectById(resumeTypeDto.getId());
        if (rs == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ResumeType resumeType = BeanCopyUtils.objClone(resumeTypeDto, ResumeType::new);
        if (validateUpdate(resumeTypeDto)) {
            utilService.updateUserInfoToEntity(resumeType);
            resumeTypeMapper.updateById(resumeType);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        return findResumeTypeById(resumeType.getId());
    }

    @Override
    public Long addResumeType(ResumeTypeDto resumeTypeDto) {
        ResumeType resumeType = BeanCopyUtils.objClone(resumeTypeDto, ResumeType::new);
        if (validateAdd(resumeTypeDto)) {
            utilService.updateUserInfoToEntity(resumeType);
            resumeType.setViewOrder(resumeTypeMapper.getMaxViewOrder());
            resumeTypeMapper.insert(resumeType);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        return resumeType.getId();
    }

    @Override
    public void delete(Long id) {
        ResumeTypeVo resumeType = findResumeTypeById(id);
        if (resumeType == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        deleteService.deleteValidateResumeType(id);
        int i = resumeTypeMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<ResumeTypeDto> resumeTypeDtos) {
        for (ResumeTypeDto resumeTypeDto : resumeTypeDtos) {
            if (GeneralTool.isEmpty(resumeTypeDto.getId())) {
                if (validateAdd(resumeTypeDto)) {
                    ResumeType resumeType = BeanCopyUtils.objClone(resumeTypeDto, ResumeType::new);
                    utilService.updateUserInfoToEntity(resumeType);
                    resumeType.setViewOrder(resumeTypeMapper.getMaxViewOrder());
                    resumeTypeMapper.insert(resumeType);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
                }
            } else {
                if (validateUpdate(resumeTypeDto)) {
                    ResumeType resumeType = BeanCopyUtils.objClone(resumeTypeDto, ResumeType::new);
                    utilService.updateUserInfoToEntity(resumeType);
                    resumeTypeMapper.updateById(resumeType);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
                }
            }

        }

    }

    @Override
    public List<BaseSelectEntity> getResumeTypeSelect() {
        return resumeTypeMapper.getResumeTypeSelect();
    }

    @Override
    public void movingOrder(List<ResumeTypeDto> resumeTypeDtos) {
        if (GeneralTool.isEmpty(resumeTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ResumeType resumeType = BeanCopyUtils.objClone(resumeTypeDtos.get(0), ResumeType::new);
        ResumeType resumeType2 = BeanCopyUtils.objClone(resumeTypeDtos.get(1), ResumeType::new);

        Integer viewOrder = resumeType.getViewOrder();
        resumeType.setViewOrder(resumeType2.getViewOrder());
        resumeType2.setViewOrder(viewOrder);

        utilService.updateUserInfoToEntity(resumeType);
        utilService.updateUserInfoToEntity(resumeType2);
        resumeTypeMapper.updateById(resumeType);
        resumeTypeMapper.updateById(resumeType2);

    }

    private boolean validateAdd(ResumeTypeDto resumeTypeDto) {
//        Example example = new Example(ResumeType.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("typeName", resumeTypeDto.getTypeName());
        List<ResumeType> list = this.resumeTypeMapper.selectList(Wrappers.<ResumeType>lambdaQuery().eq(ResumeType::getTypeName, resumeTypeDto.getTypeName()));
        return GeneralTool.isEmpty(list);
    }

    private boolean validateAdd(List<ResumeTypeDto> resumeTypeDtos) {
        boolean success = true;
        for (ResumeTypeDto resumeTypeDto : resumeTypeDtos) {
//            Example example = new Example(ResumeType.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andEqualTo("typeName", resumeTypeDto.getTypeName());
//            List<ResumeType> list = this.resumeTypeMapper.selectByExample(example);

            List<ResumeType> list = this.resumeTypeMapper.selectList(Wrappers.<ResumeType>lambdaQuery().eq(ResumeType::getTypeName, resumeTypeDto.getTypeName()));
            if (!GeneralTool.isEmpty(list)) {
                success = false;
            }
        }
        return success;
    }

    private boolean validateUpdate(ResumeTypeDto resumeTypeDto) {
//        Example example = new Example(ResumeType.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("typeName", resumeTypeDto.getTypeName());
//        List<ResumeType> list = this.resumeTypeMapper.selectByExample(example);
        List<ResumeType> list = this.resumeTypeMapper.selectList(Wrappers.<ResumeType>lambdaQuery().eq(ResumeType::getTypeName, resumeTypeDto.getTypeName()));
        return list.size() <= 0 || list.get(0).getId().equals(resumeTypeDto.getId());
    }
}
