package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.salecenter.vo.AgentEventTypeVo;
import com.get.salecenter.service.IAgentEventTypeService;
import com.get.salecenter.dto.AgentEventTypeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/10/20
 * @TIME: 12:39
 * @Description:
 **/


@Api(tags = "代理事件类型管理")
@RestController
@RequestMapping("sale/agentEventType")
public class AgentEventTypeController {
    @Autowired
    private IAgentEventTypeService typeService;

    /**
     * @return com.get.common.result.ListResponseBo<com.get.salecenter.vo.AgentEventTypeVo>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "num代理编号 name代理名称")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理事件类型管理/查询")
    @PostMapping("datas")
    public ListResponseBo<AgentEventTypeVo> datas(@RequestBody SearchBean<AgentEventTypeDto> page) {
        List<AgentEventTypeVo> datas = typeService.getAgentEventTypeDtos(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [agentEventVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理事件类型管理/新增事件")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(AgentEventTypeDto.Add.class) ValidList<AgentEventTypeDto> agentEventVo) {
        typeService.addAgentEventType(agentEventVo);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AgentEventTypeVo>
     * @Description: 修改信息
     * @Param [agentEventVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/代理事件类型管理/更新事件")
    @PostMapping("update")
    public ResponseBo<AgentEventTypeVo> update(@RequestBody @Validated(AgentEventTypeDto.Update.class) AgentEventTypeDto agentEventVo) {
        return UpdateResponseBo.ok(typeService.updateAgentEventType(agentEventVo));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/代理事件类型管理/删除代理")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        typeService.deleteAgentEventType(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 上移下移
     * @Param [agentEventTypeDtoList]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/代理事件类型管理/排序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<AgentEventTypeDto> agentEventTypeDtoList) {
        typeService.movingOrder(agentEventTypeDtoList);
        return ResponseBo.ok();
    }

}
