package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.entity.Convention;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: Sea
 * @create: 2020/6/30 17:37
 * @verison: 1.0
 * @description: 峰会管理mapper
 */
@Mapper
public interface ConventionMapper extends BaseMapper<Convention>, GetMapper<Convention> {

    /**
     * 查找该峰会的流程数
     *
     * @param id
     * @return
     */
    Long getProcedureCount(@Param("id") Long id);

    /**
     * 查找该峰会的报名数
     *
     * @param id
     * @return
     */
    Long getRegistrationCount(@Param("id") Long id);

    /**
     * 查找该峰会的参展人数
     *
     * @param id
     * @return
     */
    Long getPersonCount(@Param("id") Long id);

    /**
     * 该峰会主题名称
     *
     * @param id
     * @return
     */
    String getConventionNameById(@Param("id") Long id);
}