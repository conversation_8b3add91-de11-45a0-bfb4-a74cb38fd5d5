package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.ConventionAwardCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @DATE: 2021/9/9
 * @TIME: 12:06
 * @Description:
 **/
@Data
@ApiModel(value = "抽奖号码返回类")
public class ConventionAwardCodeVo extends BaseEntity {
    /**
     * 参展人员类型（枚举定义）：0校方/1代理/2嘉宾/3员工/4工作人员
     */
    @ApiModelProperty(value = "参展人员类型（枚举定义）：0校方/1代理/2嘉宾/3员工/4工作人员")
    private Integer type;


    /**
     * 参加人姓名
     */
    @ApiModelProperty(value = "参加人姓名")
    private String name;
    /**
     * 使用状态名称
     */
    @ApiModelProperty(value = "使用状态名称")
    private String useTypeName;

    /**
     * 购买者（角色）
     */
    @ApiModelProperty(value = "购买者（角色）")
    private String purchaser;

    //=========实体类ConventionAwardCode==============
    private static final long serialVersionUID = 1L;
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    @Column(name = "fk_convention_id")
    private Long fkConventionId;
    /**
     * 抽奖号码（预生成，可自定义前序字符）
     */
    @ApiModelProperty(value = "抽奖号码（预生成，可自定义前序字符）")
    @Column(name = "award_code")
    private String awardCode;
    /**
     * 峰会参展人员Id（抽奖码占用人）
     */
    @ApiModelProperty(value = "峰会参展人员Id（抽奖码占用人）")
    @Column(name = "fk_convention_person_id")
    private Long fkConventionPersonId;
    /**
     * 系统支付单号，guid
     */
    @ApiModelProperty(value = "系统支付单号，guid")
    @Column(name = "pay_system_order_num")
    private String paySystemOrderNum;
    /**
     * 微信支付单号
     */
    @ApiModelProperty(value = "微信支付单号")
    @Column(name = "pay_wechat_order_num")
    private String payWechatOrderNum;
    /**
     * 使用状态：0锁定/1已购买未使用/2已购买已使用（中奖后不能再参与抽奖）
     */
    @ApiModelProperty(value = "使用状态：0锁定/1已购买未使用/2已购买已使用（中奖后不能再参与抽奖）")
    @Column(name = "use_type")
    private Integer useType;
}
