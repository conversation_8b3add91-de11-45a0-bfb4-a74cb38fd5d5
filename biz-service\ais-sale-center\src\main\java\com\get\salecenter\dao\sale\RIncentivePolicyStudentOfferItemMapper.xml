<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.RIncentivePolicyStudentOfferItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.IncentivePolicyStudentOfferItem">
        <id column="id" property="id" />
        <result column="fk_incentive_policy_id" property="fkIncentivePolicyId" />
        <result column="fk_student_offer_item_id" property="fkStudentOfferItemId" />
        <result column="count_type" property="countType" />
        <result column="finance_status" property="financeStatus" />
        <result column="settlement_time" property="settlementTime" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_create_user" property="gmtCreateUser" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="gmt_modified_user" property="gmtModifiedUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, fk_incentive_policy_id, fk_student_offer_item_id, count_type, finance_status, settlement_time, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
    </sql>


    <select id="datas" resultType="com.get.salecenter.vo.IncentivePolicyStudentOfferItemVo">
        SELECT
        policy_item.id,
        policy_item.fk_student_offer_item_id,
        policy_item.fk_incentive_policy_id,
        student.fk_company_id,
        offer_item.fk_area_country_id,
        policy_item.count_type,
        policy_item.finance_status,
        item_step.step_name,
        agent.`name` AS agent_name,
        student.`name` AS student_name,
        student.id AS student_id,
        institution.`name` AS institution_name,
        institution_course.`name` AS institution_course_name,
        offer_item.fk_institution_provider_id AS institution_provider_id,
        offer_item.defer_opening_time,
        offer_item.gmt_create AS offer_item_gmt_create
        FROM
        r_incentive_policy_student_offer_item AS policy_item
        INNER JOIN m_student_offer_item AS offer_item ON policy_item.fk_student_offer_item_id = offer_item.id
        INNER JOIN m_student AS student ON offer_item.fk_student_id = student.id
        LEFT JOIN m_agent AS agent ON agent.id = offer_item.fk_agent_id
        LEFT JOIN ais_institution_center.m_institution AS institution ON institution.id = offer_item.fk_institution_id
        LEFT JOIN ais_institution_center.m_institution_course AS institution_course ON institution_course.id = offer_item.fk_institution_course_custom_id
        LEFT JOIN u_student_offer_item_step AS item_step ON item_step.id = offer_item.fk_student_offer_item_step_id
        <where>
            1 = 1
            <if test="incentivePolicyStudentOfferItemDto.fkCompanyIds !=null and incentivePolicyStudentOfferItemDto.fkCompanyIds.size>0">
                AND student.fk_company_id in
                <foreach collection="incentivePolicyStudentOfferItemDto.fkCompanyIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="incentivePolicyStudentOfferItemDto.countType !=null">
                and policy_item.count_type = #{incentivePolicyStudentOfferItemDto.countType}
            </if>
            <if test="incentivePolicyStudentOfferItemDto.financeStatus !=null">
                and policy_item.finance_status = #{incentivePolicyStudentOfferItemDto.financeStatus}
            </if>
            <if test="incentivePolicyStudentOfferItemDto.agentNameOrNum!=null and incentivePolicyStudentOfferItemDto.agentNameOrNum!=''">
                AND (
                LOWER(agent.`name`) like concat("%",#{incentivePolicyStudentOfferItemDto.agentNameOrNum},"%")
                OR LOWER(agent.name_note) like concat("%",#{incentivePolicyStudentOfferItemDto.agentNameOrNum},"%")
                OR concat(LOWER(agent.`name`),"（",agent.name_note,"）") like concat("%",#{incentivePolicyStudentOfferItemDto.agentNameOrNum},"%")
                OR LOWER(agent.num) like concat("%",#{incentivePolicyStudentOfferItemDto.agentNameOrNum},"%")
                )
            </if>
            <if test="incentivePolicyStudentOfferItemDto.studentName !=null and incentivePolicyStudentOfferItemDto.studentName!=''">
                AND (
                REPLACE(CONCAT(LOWER(student.first_name),LOWER(student.last_name)),' ','') like concat('%',#{incentivePolicyStudentOfferItemDto.studentName},'%')
                OR REPLACE(CONCAT(LOWER(student.last_name),LOWER(student.first_name)),' ','') like concat('%',#{incentivePolicyStudentOfferItemDto.studentName},'%')
                OR REPLACE(LOWER(student.`name`),' ','') like concat('%',#{incentivePolicyStudentOfferItemDto.studentName},'%')
                OR REPLACE(LOWER(student.last_name),' ','') like concat('%',#{incentivePolicyStudentOfferItemDto.studentName},'%')
                OR REPLACE(LOWER(student.first_name),' ','') like concat('%',#{incentivePolicyStudentOfferItemDto.studentName},'%')
                )
            </if>
            <if test="incentivePolicyStudentOfferItemDto.institutionName!=null and incentivePolicyStudentOfferItemDto.institutionName!=''">
                and CONCAT(LOWER(institution.`name`), '（', institution.name_chn, '）')  like concat("%",#{incentivePolicyStudentOfferItemDto.institutionName},"%")
            </if>
            <if test="incentivePolicyStudentOfferItemDto.fkIncentivePolicyId !=null">
                and policy_item.fk_incentive_policy_id = #{incentivePolicyStudentOfferItemDto.fkIncentivePolicyId}
            </if>
        </where>
    </select>
    <select id="getStudentOfferItemList_bak" resultType="java.lang.Long">
        SELECT
            distinct(offer_item.id)
        FROM
            m_student_offer_item AS offer_item
            LEFT JOIN m_student AS student ON offer_item.fk_student_id = student.id
            LEFT JOIN r_student_offer_item_step AS item_step ON item_step.fk_student_offer_item_id = offer_item.id
        WHERE
           offer_item.fk_student_offer_item_step_id != 9
        <if test="conditionJson.submitAppTimes!=null and conditionJson.submitAppTimes.size()>0">
            AND ( 1=0
            <foreach collection="conditionJson.submitAppTimes" item="submitAppTimes" index="index">
               <if test="submitAppTimes.startTime!=null and  submitAppTimes.endTime!=null">
                   OR (offer_item.submit_app_time BETWEEN #{submitAppTimes.startTime} AND  DATE_ADD(#{submitAppTimes.endTime},INTERVAL 1 DAY)) OR ( item_step.fk_student_offer_item_step_id in (2,3,11) AND item_step.gmt_create BETWEEN #{submitAppTimes.startTime} AND DATE_ADD(#{submitAppTimes.endTime},INTERVAL 1 DAY) )
               </if>
            </foreach>
            )
        </if>
        <if test="conditionJson.depositTimes!=null and conditionJson.depositTimes.size()>0">
            AND ( 1=0
            <foreach collection="conditionJson.depositTimes" item="depositTimes" index="index">
                <if test="depositTimes.startTime!=null and  depositTimes.endTime!=null">
                    OR (offer_item.deposit_time BETWEEN #{depositTimes.startTime} AND DATE_ADD(#{depositTimes.endTime},INTERVAL 1 DAY)) OR ( item_step.fk_student_offer_item_step_id = 5 AND item_step.gmt_create BETWEEN #{depositTimes.startTime} AND DATE_ADD(#{depositTimes.endTime},INTERVAL 1 DAY) )
                </if>
            </foreach>
            )
        </if>
        <if test="conditionJson.deferOpeningTimes!=null and conditionJson.deferOpeningTimes.size()>0">
            AND ( 1=0
            <foreach collection="conditionJson.deferOpeningTimes" item="deferOpeningTimes" index="index">
                <if test="deferOpeningTimes.startTime!=null and  deferOpeningTimes.endTime!=null">
                    OR offer_item.defer_opening_time BETWEEN #{deferOpeningTimes.startTime} AND DATE_ADD(#{deferOpeningTimes.endTime},INTERVAL 1 DAY)
                </if>
            </foreach>
            )
        </if>
        <if test="conditionJson.stepOrderIds!=null and conditionJson.stepOrderIds.size()>0">
            AND ( item_step.fk_student_offer_item_step_id in
            <foreach collection="conditionJson.stepOrderIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="incentivePolicy.fkCompanyId!=null">
            AND student.fk_company_id = #{incentivePolicy.fkCompanyId}
        </if>
        <if test="incentivePolicy.fkAreaCountryId!=null">
            AND offer_item.fk_area_country_id = #{incentivePolicy.fkAreaCountryId}
        </if>
        <if test="incentivePolicy.fkInstitutionProviderId!=null">
            AND offer_item.fk_institution_provider_id = #{incentivePolicy.fkInstitutionProviderId}
        </if>
        <if test="incentivePolicy.fkInstitutionIds!=null and incentivePolicy.fkInstitutionIds!=''">
            AND offer_item.fk_institution_id in
            <foreach collection="incentivePolicy.fkInstitutionIds.split(',')" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="conditionJson.mainCourseMark == true or conditionJson.subCourseMark == true or conditionJson.laterCourseMark == true ">
            AND offer_item.id in
            (
            <choose>
                <when test="conditionJson.subCourseMark == true and conditionJson.laterCourseMark == true">
                    select offer_item_parent.id from m_student_offer_item AS offer_item_parent
                    INNER JOIN m_student_offer_item AS offer_item_sub ON offer_item_sub.fk_parent_student_offer_item_id = offer_item_parent.id AND offer_item_sub.is_follow = 0
                    INNER JOIN m_student_offer_item AS offer_item_later ON offer_item_later.fk_parent_student_offer_item_id = offer_item_parent.id AND offer_item_later.is_follow = 1
                    where 1=1
                    -- mainCourse
                    <if test="conditionJson.mainMajorLevelIds!=null and conditionJson.mainMajorLevelIds.size()>0">
                        AND ( 1=0
                        <foreach collection="conditionJson.mainMajorLevelIds" item="id" index="index">
                            OR FIND_IN_SET(#{id},offer_item_parent.fk_institution_course_major_level_ids)
                        </foreach>
                        <foreach collection="conditionJson.mainMajorLevelNames" item="name" index="index">
                            OR offer_item_parent.old_course_major_level_name LIKE CONCAT(#{name},'%')
                        </foreach>
                        )
                    </if>
                    <if test="conditionJson.mainCourseTypeIds!=null and conditionJson.mainCourseTypeIds.size()>0">
                        AND ( 1=0
                        <foreach collection="conditionJson.mainCourseTypeIds" item="id" index="index">
                            OR FIND_IN_SET(#{id},offer_item_parent.fk_institution_course_type_ids)
                        </foreach>
                        <foreach collection="conditionJson.mainCourseTypeNames" item="name" index="index">
                            OR offer_item_parent.old_course_type_name LIKE CONCAT(#{name},'%')
                        </foreach>
                        )
                    </if>
                    <if test="conditionJson.mainCourseIds!=null and conditionJson.mainCourseIds.size()>0">
                        AND (offer_item_parent.fk_institution_course_custom_id IN
                        <foreach collection="conditionJson.mainCourseIds" item="id" index="index" open="(" separator=","
                                 close=")">
                            #{id,jdbcType=BIGINT}
                        </foreach>
                        <if test="conditionJson.mainCourseNames!=null and conditionJson.mainCourseNames.size()>0">
                            <foreach collection="conditionJson.mainCourseNames" item="name" index="index">
                                OR offer_item_parent.old_course_custom_name LIKE CONCAT('%',#{name},'%')
                            </foreach>
                        </if>
                        )
                    </if>

                    -- subCourse
                    <if test="conditionJson.subMajorLevelIds!=null and conditionJson.subMajorLevelIds.size()>0">
                        AND ( 1=0
                        <foreach collection="conditionJson.subMajorLevelIds" item="id" index="index">
                            OR FIND_IN_SET(#{id},offer_item_sub.fk_institution_course_major_level_ids)
                        </foreach>
                        <foreach collection="conditionJson.subMajorLevelNames" item="name" index="index">
                            OR offer_item_sub.old_course_major_level_name LIKE CONCAT(#{name},'%')
                        </foreach>
                        )
                    </if>
                    <if test="conditionJson.subCourseTypeIds!=null and conditionJson.subCourseTypeIds.size()>0">
                        AND ( 1=0
                        <foreach collection="conditionJson.subCourseTypeIds" item="id" index="index">
                            OR FIND_IN_SET(#{id},offer_item_sub.fk_institution_course_type_ids)
                        </foreach>
                        <foreach collection="conditionJson.subCourseTypeNames" item="name" index="index">
                            OR offer_item_sub.old_course_type_name LIKE CONCAT(#{name},'%')
                        </foreach>
                        )
                    </if>
                    <if test="conditionJson.subCourseIds!=null and conditionJson.subCourseIds.size()>0">
                        AND (offer_item_sub.fk_institution_course_custom_id IN
                        <foreach collection="conditionJson.subCourseIds" item="id" index="index" open="(" separator=","
                                 close=")">
                            #{id,jdbcType=BIGINT}
                        </foreach>
                        <if test="conditionJson.subCourseNames!=null and conditionJson.subCourseNames.size()>0">
                            <foreach collection="conditionJson.subCourseNames" item="name" index="index">
                                OR offer_item_sub.old_course_custom_name LIKE CONCAT('%',#{name},'%')
                            </foreach>
                        </if>
                        )
                    </if>
                    -- laterCourse
                    <if test="conditionJson.laterInstitutionProviderId!=null">
                        AND offer_item_later.fk_institution_provider_id =  #{conditionJson.laterInstitutionProviderId}
                    </if>
                    <if test="conditionJson.laterInstitutionIds!=null and conditionJson.laterInstitutionIds.size()>0">
                        AND offer_item_later.fk_institution_id IN
                        <foreach collection="conditionJson.laterInstitutionIds" item="item" index="index" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="conditionJson.laterUpMajorLevelIds!=null and conditionJson.laterUpMajorLevelIds.size()>0">
                        AND ( 1=0
                        <foreach collection="conditionJson.laterUpMajorLevelIds" item="id" index="index">
                            OR FIND_IN_SET(#{id},offer_item_later.fk_institution_course_major_level_ids)
                        </foreach>
                        <foreach collection="conditionJson.laterUpMajorLevelNames" item="name" index="index">
                            OR offer_item_later.old_course_major_level_name LIKE CONCAT(#{name},'%')
                        </foreach>
                        )
                    </if>
                    <if test="conditionJson.laterUpCourseTypeIds!=null and conditionJson.laterUpCourseTypeIds.size()>0">
                        AND ( 1=0
                        <foreach collection="conditionJson.laterUpCourseTypeIds" item="id" index="index">
                            OR FIND_IN_SET(#{id},offer_item_later.fk_institution_course_type_ids)
                        </foreach>
                        <foreach collection="conditionJson.laterUpCourseTypeNames" item="name" index="index">
                            OR offer_item_later.old_course_type_name LIKE CONCAT(#{name},'%')
                        </foreach>
                        )
                    </if>
                    <if test="conditionJson.laterUpCourseIds!=null and conditionJson.laterUpCourseIds.size()>0">
                        AND (offer_item_later.fk_institution_course_custom_id IN
                        <foreach collection="conditionJson.laterUpCourseIds" item="id" index="index" open="("
                                 separator="," close=")">
                            #{id,jdbcType=BIGINT}
                        </foreach>
                        <if test="conditionJson.laterUpCourseNames!=null and conditionJson.laterUpCourseNames.size()>0">
                            <foreach collection="conditionJson.laterUpCourseNames" item="name" index="index">
                                OR offer_item_later.old_course_custom_name LIKE CONCAT('%',#{name},'%')
                            </foreach>
                        </if>
                        )
                    </if>
                </when>
                <when test="conditionJson.subCourseMark == true">
                    select offer_item_parent.id from m_student_offer_item AS offer_item_parent
                    INNER JOIN m_student_offer_item AS offer_item_sub ON offer_item_sub.fk_parent_student_offer_item_id = offer_item_parent.id AND offer_item_sub.is_follow = 0
                    where 1=1
                    -- mainCourse
                    <if test="conditionJson.mainMajorLevelIds!=null and conditionJson.mainMajorLevelIds.size()>0">
                        AND ( 1=0
                        <foreach collection="conditionJson.mainMajorLevelIds" item="id" index="index">
                            OR FIND_IN_SET(#{id},offer_item_parent.fk_institution_course_major_level_ids)
                        </foreach>
                        <foreach collection="conditionJson.mainMajorLevelNames" item="name" index="index">
                            OR offer_item_parent.old_course_major_level_name LIKE CONCAT(#{name},'%')
                        </foreach>
                        )
                    </if>
                    <if test="conditionJson.mainCourseTypeIds!=null and conditionJson.mainCourseTypeIds.size()>0">
                        AND ( 1=0
                        <foreach collection="conditionJson.mainCourseTypeIds" item="id" index="index">
                            OR FIND_IN_SET(#{id},offer_item_parent.fk_institution_course_type_ids)
                        </foreach>
                        <foreach collection="conditionJson.mainCourseTypeNames" item="name" index="index">
                            OR offer_item_parent.old_course_type_name LIKE CONCAT(#{name},'%')
                        </foreach>
                        )
                    </if>
                    <if test="conditionJson.mainCourseIds!=null and conditionJson.mainCourseIds.size()>0">
                        AND (offer_item_parent.fk_institution_course_custom_id IN
                        <foreach collection="conditionJson.mainCourseIds" item="id" index="index" open="(" separator=","
                                 close=")">
                            #{id,jdbcType=BIGINT}
                        </foreach>
                        <if test="conditionJson.mainCourseNames!=null and conditionJson.mainCourseNames.size()>0">
                            <foreach collection="conditionJson.mainCourseNames" item="name" index="index">
                                OR offer_item_parent.old_course_custom_name LIKE CONCAT('%',#{name},'%')
                            </foreach>
                        </if>
                        )
                    </if>
                    -- subCourse
                    <if test="conditionJson.subMajorLevelIds!=null and conditionJson.subMajorLevelIds.size()>0">
                        AND ( 1=0
                        <foreach collection="conditionJson.subMajorLevelIds" item="id" index="index">
                            OR FIND_IN_SET(#{id},offer_item_sub.fk_institution_course_major_level_ids)
                        </foreach>
                        <foreach collection="conditionJson.subMajorLevelNames" item="name" index="index">
                            OR offer_item_sub.old_course_major_level_name LIKE CONCAT(#{name},'%')
                        </foreach>
                        )
                    </if>
                    <if test="conditionJson.subCourseTypeIds!=null and conditionJson.subCourseTypeIds.size()>0">
                        AND ( 1=0
                        <foreach collection="conditionJson.subCourseTypeIds" item="id" index="index">
                            OR FIND_IN_SET(#{id},offer_item_sub.fk_institution_course_type_ids)
                        </foreach>
                        <foreach collection="conditionJson.subCourseTypeNames" item="name" index="index">
                            OR offer_item_sub.old_course_type_name LIKE CONCAT(#{name},'%')
                        </foreach>
                        )
                    </if>
                    <if test="conditionJson.subCourseIds!=null and conditionJson.subCourseIds.size()>0">
                        AND (offer_item_sub.fk_institution_course_custom_id IN
                        <foreach collection="conditionJson.subCourseIds" item="id" index="index" open="(" separator=","
                                 close=")">
                            #{id,jdbcType=BIGINT}
                        </foreach>
                        <if test="conditionJson.subCourseNames!=null and conditionJson.subCourseNames.size()>0">
                            <foreach collection="conditionJson.subCourseNames" item="name" index="index">
                                OR offer_item_sub.old_course_custom_name LIKE CONCAT('%',#{name},'%')
                            </foreach>
                        </if>
                        )
                    </if>
                </when>
                <when test="conditionJson.laterCourseMark == true">
                    select offer_item_parent.id from m_student_offer_item AS offer_item_parent
                    INNER JOIN m_student_offer_item AS offer_item_later ON offer_item_later.fk_parent_student_offer_item_id = offer_item_parent.id AND offer_item_later.is_follow = 1
                    where 1=1
                    -- mainCourse
                    <if test="conditionJson.mainMajorLevelIds!=null and conditionJson.mainMajorLevelIds.size()>0">
                        AND ( 1=0
                        <foreach collection="conditionJson.mainMajorLevelIds" item="id" index="index">
                            OR FIND_IN_SET(#{id},offer_item_parent.fk_institution_course_major_level_ids)
                        </foreach>
                        <foreach collection="conditionJson.mainMajorLevelNames" item="name" index="index">
                            OR offer_item_parent.old_course_major_level_name LIKE CONCAT(#{name},'%')
                        </foreach>
                        )
                    </if>
                    <if test="conditionJson.mainCourseTypeIds!=null and conditionJson.mainCourseTypeIds.size()>0">
                        AND ( 1=0
                        <foreach collection="conditionJson.mainCourseTypeIds" item="id" index="index">
                            OR FIND_IN_SET(#{id},offer_item_parent.fk_institution_course_type_ids)
                        </foreach>
                        <foreach collection="conditionJson.mainCourseTypeNames" item="name" index="index">
                            OR offer_item_parent.old_course_type_name LIKE CONCAT(#{name},'%')
                        </foreach>
                        )
                    </if>
                    <if test="conditionJson.mainCourseIds!=null and conditionJson.mainCourseIds.size()>0">
                        AND (offer_item_parent.fk_institution_course_custom_id IN
                        <foreach collection="conditionJson.mainCourseIds" item="id" index="index" open="(" separator=","
                                 close=")">
                            #{id,jdbcType=BIGINT}
                        </foreach>
                        <if test="conditionJson.mainCourseNames!=null and conditionJson.mainCourseNames.size()>0">
                            <foreach collection="conditionJson.mainCourseNames" item="name" index="index">
                                OR offer_item_parent.old_course_custom_name LIKE CONCAT('%',#{name},'%')
                            </foreach>
                        </if>
                        )
                    </if>
                    -- laterCourse
                    <if test="conditionJson.laterInstitutionProviderId!=null">
                        AND offer_item_later.fk_institution_provider_id =  #{conditionJson.laterInstitutionProviderId}
                    </if>
                    <if test="conditionJson.laterInstitutionIds!=null and conditionJson.laterInstitutionIds.size()>0">
                        AND offer_item_later.fk_institution_id IN
                        <foreach collection="conditionJson.laterInstitutionIds" item="item" index="index" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="conditionJson.laterUpMajorLevelIds!=null and conditionJson.laterUpMajorLevelIds.size()>0">
                        AND ( 1=0
                        <foreach collection="conditionJson.laterUpMajorLevelIds" item="id" index="index">
                            OR FIND_IN_SET(#{id},offer_item_later.fk_institution_course_major_level_ids)
                        </foreach>
                        <foreach collection="conditionJson.laterUpMajorLevelNames" item="name" index="index">
                            OR offer_item_later.old_course_major_level_name LIKE CONCAT(#{name},'%')
                        </foreach>
                        )
                    </if>
                    <if test="conditionJson.laterUpCourseTypeIds!=null and conditionJson.laterUpCourseTypeIds.size()>0">
                        AND ( 1=0
                        <foreach collection="conditionJson.laterUpCourseTypeIds" item="id" index="index">
                            OR FIND_IN_SET(#{id},offer_item_later.fk_institution_course_type_ids)
                        </foreach>
                        <foreach collection="conditionJson.laterUpCourseTypeNames" item="name" index="index">
                            OR offer_item_later.old_course_type_name LIKE CONCAT(#{name},'%')
                        </foreach>
                        )
                    </if>
                    <if test="conditionJson.laterUpCourseIds!=null and conditionJson.laterUpCourseIds.size()>0">
                        AND (offer_item_later.fk_institution_course_custom_id IN
                        <foreach collection="conditionJson.laterUpCourseIds" item="id" index="index" open="("
                                 separator="," close=")">
                            #{id,jdbcType=BIGINT}
                        </foreach>
                        <if test="conditionJson.laterUpCourseNames!=null and conditionJson.laterUpCourseNames.size()>0">
                            <foreach collection="conditionJson.laterUpCourseNames" item="name" index="index">
                                OR offer_item_later.old_course_custom_name LIKE CONCAT('%',#{name},'%')
                            </foreach>
                        </if>
                        )
                    </if>
                </when>
                <when test="conditionJson.mainCourseMark == true">
                    select id from m_student_offer_item AS offer_item_parent
                    WHERE
                    1 = 1
                    -- mainCourse
                    <if test="conditionJson.mainMajorLevelIds!=null and conditionJson.mainMajorLevelIds.size()>0">
                        AND ( 1=0
                        <foreach collection="conditionJson.mainMajorLevelIds" item="id" index="index">
                            OR FIND_IN_SET(#{id},offer_item_parent.fk_institution_course_major_level_ids)
                        </foreach>
                        <foreach collection="conditionJson.mainMajorLevelNames" item="name" index="index">
                            OR offer_item_parent.old_course_major_level_name LIKE CONCAT(#{name},'%')
                        </foreach>
                        )
                    </if>
                    <if test="conditionJson.mainCourseTypeIds!=null and conditionJson.mainCourseTypeIds.size()>0">
                        AND ( 1=0
                        <foreach collection="conditionJson.mainCourseTypeIds" item="id" index="index">
                            OR FIND_IN_SET(#{id},offer_item_parent.fk_institution_course_type_ids)
                        </foreach>
                        <foreach collection="conditionJson.mainCourseTypeNames" item="name" index="index">
                            OR offer_item_parent.old_course_type_name LIKE CONCAT(#{name},'%')
                        </foreach>
                        )
                    </if>
                    <if test="conditionJson.mainCourseIds!=null and conditionJson.mainCourseIds.size()>0">
                        AND (offer_item_parent.fk_institution_course_custom_id IN
                        <foreach collection="conditionJson.mainCourseIds" item="id" index="index" open="(" separator=","
                                 close=")">
                            #{id,jdbcType=BIGINT}
                        </foreach>
                        <if test="conditionJson.mainCourseNames!=null and conditionJson.mainCourseNames.size()>0">
                            <foreach collection="conditionJson.mainCourseNames" item="name" index="index">
                                OR offer_item_parent.old_course_custom_name LIKE CONCAT('%',#{name},'%')
                            </foreach>
                        </if>
                        )
                    </if>
                </when>
            </choose>
            )
        </if>
    </select>
    <select id="getStudentOfferItemList" resultType="java.lang.Long">
        SELECT
        distinct(offer_item.id)
        FROM
        m_student_offer_item AS offer_item
        LEFT JOIN m_student AS student ON offer_item.fk_student_id = student.id
        LEFT JOIN r_student_offer_item_step AS item_step ON item_step.fk_student_offer_item_id = offer_item.id
        WHERE
        -- offer_item.fk_student_offer_item_step_id != 9
        1 = 1
        <if test="conditionJson.submitAppTimes!=null and conditionJson.submitAppTimes.size()>0">
            AND ( 1=0
            <foreach collection="conditionJson.submitAppTimes" item="submitAppTimes" index="index">
                <if test="submitAppTimes.startTime!=null and  submitAppTimes.endTime!=null">
                    OR (offer_item.submit_app_time BETWEEN #{submitAppTimes.startTime} AND  DATE_ADD(#{submitAppTimes.endTime},INTERVAL 1 DAY)) OR ( item_step.fk_student_offer_item_step_id in (2,3,11) AND item_step.gmt_create BETWEEN #{submitAppTimes.startTime} AND DATE_ADD(#{submitAppTimes.endTime},INTERVAL 1 DAY) )
                </if>
            </foreach>
            )
        </if>
        <if test="conditionJson.depositTimes!=null and conditionJson.depositTimes.size()>0">
            AND ( 1=0
            <foreach collection="conditionJson.depositTimes" item="depositTimes" index="index">
                <if test="depositTimes.startTime!=null and  depositTimes.endTime!=null">
                    OR (offer_item.deposit_time BETWEEN #{depositTimes.startTime} AND DATE_ADD(#{depositTimes.endTime},INTERVAL 1 DAY)) OR ( item_step.fk_student_offer_item_step_id = 5 AND item_step.gmt_create BETWEEN #{depositTimes.startTime} AND DATE_ADD(#{depositTimes.endTime},INTERVAL 1 DAY) )
                </if>
            </foreach>
            )
        </if>
        <if test="conditionJson.tuitionTimes!=null and conditionJson.tuitionTimes.size()>0">
            AND ( 1=0
            <foreach collection="conditionJson.tuitionTimes" item="tuitionTimes" index="index">
                <if test="tuitionTimes.startTime!=null and  tuitionTimes.endTime!=null">
                    OR (offer_item.tuition_time BETWEEN #{tuitionTimes.startTime} AND DATE_ADD(#{tuitionTimes.endTime},INTERVAL 1 DAY)) OR ( item_step.fk_student_offer_item_step_id = 17 AND item_step.gmt_create BETWEEN #{tuitionTimes.startTime} AND DATE_ADD(#{tuitionTimes.endTime},INTERVAL 1 DAY) )
                </if>
            </foreach>
            )
        </if>
        <if test="conditionJson.deferOpeningTimes!=null and conditionJson.deferOpeningTimes.size()>0">
            AND ( 1=0
            <foreach collection="conditionJson.deferOpeningTimes" item="deferOpeningTimes" index="index">
                <if test="deferOpeningTimes.startTime!=null and  deferOpeningTimes.endTime!=null">
                    OR offer_item.defer_opening_time BETWEEN #{deferOpeningTimes.startTime} AND DATE_ADD(#{deferOpeningTimes.endTime},INTERVAL 1 DAY)
                </if>
            </foreach>
            )
        </if>
        <if test="conditionJson.stepOrderIds!=null and conditionJson.stepOrderIds.size()>0">
            AND ( item_step.fk_student_offer_item_step_id in
            <foreach collection="conditionJson.stepOrderIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="incentivePolicy.fkCompanyId!=null">
            AND student.fk_company_id = #{incentivePolicy.fkCompanyId}
        </if>
        <if test="incentivePolicy.fkAreaCountryId!=null">
            AND offer_item.fk_area_country_id = #{incentivePolicy.fkAreaCountryId}
        </if>
        <if test="incentivePolicy.fkInstitutionProviderId!=null">
            AND offer_item.fk_institution_provider_id = #{incentivePolicy.fkInstitutionProviderId}
        </if>
        <if test="incentivePolicy.fkInstitutionIds!=null and incentivePolicy.fkInstitutionIds!=''">
            AND offer_item.fk_institution_id in
            <foreach collection="incentivePolicy.fkInstitutionIds.split(',')" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="conditionJson.mainCourseMark == true or conditionJson.subCourseMark == true or conditionJson.laterCourseMark == true ">
            AND offer_item.id in
            (
                select offer_item_parent.id from m_student_offer_item AS offer_item_parent
                LEFT JOIN m_student_offer_item AS offer_item_sub ON offer_item_sub.fk_parent_student_offer_item_id = offer_item_parent.id AND offer_item_sub.is_follow = 0
                LEFT JOIN m_student_offer_item AS offer_item_later ON offer_item_later.fk_parent_student_offer_item_id = offer_item_parent.id AND offer_item_later.is_follow = 1
                where 1=1
                AND
                (
                    <if test="conditionJson.mainAndSubCourseFlag == 1 and conditionJson.subAndLaterCourseFlag == 0 and conditionJson.mainCourseMark == true and conditionJson.subCourseMark == true">
                    (
                    </if>
                    <if test="conditionJson.mainCourseMark == true">
                    (
                        1=1
                        <if test="conditionJson.mainMajorLevelIds!=null and conditionJson.mainMajorLevelIds.size()>0">
                            AND ( 1=0
                            <foreach collection="conditionJson.mainMajorLevelIds" item="id" index="index">
                                OR FIND_IN_SET(#{id},offer_item_parent.fk_institution_course_major_level_ids)
                            </foreach>
                            <foreach collection="conditionJson.mainMajorLevelNames" item="name" index="index">
                                OR offer_item_parent.old_course_major_level_name LIKE CONCAT(#{name},'%')
                            </foreach>
                            )
                        </if>
                        <if test="conditionJson.mainCourseTypeIds!=null and conditionJson.mainCourseTypeIds.size()>0">
                            AND ( 1=0
                            <foreach collection="conditionJson.mainCourseTypeIds" item="id" index="index">
                                OR FIND_IN_SET(#{id},offer_item_parent.fk_institution_course_type_ids)
                            </foreach>
                            <foreach collection="conditionJson.mainCourseTypeNames" item="name" index="index">
                                OR offer_item_parent.old_course_type_name LIKE CONCAT(#{name},'%')
                            </foreach>
                            )
                        </if>
                        <if test="conditionJson.mainCourseIds!=null and conditionJson.mainCourseIds.size()>0">
                            AND (offer_item_parent.fk_institution_course_custom_id IN
                            <foreach collection="conditionJson.mainCourseIds" item="id" index="index" open="(" separator=","
                                     close=")">
                                #{id,jdbcType=BIGINT}
                            </foreach>
                            <if test="conditionJson.mainCourseNames!=null and conditionJson.mainCourseNames.size()>0">
                                <foreach collection="conditionJson.mainCourseNames" item="name" index="index">
                                    OR offer_item_parent.old_course_custom_name LIKE CONCAT('%',#{name},'%')
                                </foreach>
                            </if>
                            )
                        </if>
                    )
                    </if>
                    <if test="conditionJson.mainAndSubCourseFlag == 0 and conditionJson.mainCourseMark == true and conditionJson.subCourseMark == true">
                        AND
                    </if>
                    <if test="conditionJson.mainAndSubCourseFlag == 1 and conditionJson.mainCourseMark == true and conditionJson.subCourseMark == true">
                        OR
                    </if>
                    <if test="conditionJson.mainAndSubCourseFlag == 0 and conditionJson.subAndLaterCourseFlag == 1 and conditionJson.subCourseMark == true and conditionJson.laterCourseMark == true">
                    (
                    </if>
                    <if test="conditionJson.subCourseMark == true">
                    (
                        1=1
                        <if test="conditionJson.subMajorLevelIds!=null and conditionJson.subMajorLevelIds.size()>0">
                            AND ( 1=0
                            <foreach collection="conditionJson.subMajorLevelIds" item="id" index="index">
                                OR FIND_IN_SET(#{id},offer_item_sub.fk_institution_course_major_level_ids)
                            </foreach>
                            <foreach collection="conditionJson.subMajorLevelNames" item="name" index="index">
                                OR offer_item_sub.old_course_major_level_name LIKE CONCAT(#{name},'%')
                            </foreach>
                            )
                        </if>
                        <if test="conditionJson.subCourseTypeIds!=null and conditionJson.subCourseTypeIds.size()>0">
                            AND ( 1=0
                            <foreach collection="conditionJson.subCourseTypeIds" item="id" index="index">
                                OR FIND_IN_SET(#{id},offer_item_sub.fk_institution_course_type_ids)
                            </foreach>
                            <foreach collection="conditionJson.subCourseTypeNames" item="name" index="index">
                                OR offer_item_sub.old_course_type_name LIKE CONCAT(#{name},'%')
                            </foreach>
                            )
                        </if>
                        <if test="conditionJson.subCourseIds!=null and conditionJson.subCourseIds.size()>0">
                            AND (offer_item_sub.fk_institution_course_custom_id IN
                            <foreach collection="conditionJson.subCourseIds" item="id" index="index" open="(" separator=","
                                     close=")">
                                #{id,jdbcType=BIGINT}
                            </foreach>
                            <if test="conditionJson.subCourseNames!=null and conditionJson.subCourseNames.size()>0">
                                <foreach collection="conditionJson.subCourseNames" item="name" index="index">
                                    OR offer_item_sub.old_course_custom_name LIKE CONCAT('%',#{name},'%')
                                </foreach>
                            </if>
                            )
                        </if>
                    )
                    </if>
                    <if test="conditionJson.mainAndSubCourseFlag == 1 and conditionJson.subAndLaterCourseFlag == 0 and conditionJson.mainCourseMark == true and conditionJson.subCourseMark == true">
                        )
                    </if>
                    <if test="conditionJson.subAndLaterCourseFlag == 0 and conditionJson.laterCourseMark == true">
                        AND
                    </if>
                    <if test="conditionJson.subAndLaterCourseFlag == 1 and conditionJson.laterCourseMark == true">
                        OR
                    </if>
                    <if test="conditionJson.laterCourseMark == true">
                    (
                        1=1
                        <if test="conditionJson.laterInstitutionProviderId!=null">
                            AND offer_item_later.fk_institution_provider_id =  #{conditionJson.laterInstitutionProviderId}
                        </if>
                        <if test="conditionJson.laterInstitutionIds!=null and conditionJson.laterInstitutionIds.size()>0">
                            AND offer_item_later.fk_institution_id IN
                            <foreach collection="conditionJson.laterInstitutionIds" item="item" index="index" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                        <if test="conditionJson.laterUpMajorLevelIds!=null and conditionJson.laterUpMajorLevelIds.size()>0">
                            AND ( 1=0
                            <foreach collection="conditionJson.laterUpMajorLevelIds" item="id" index="index">
                                OR FIND_IN_SET(#{id},offer_item_later.fk_institution_course_major_level_ids)
                            </foreach>
                            <foreach collection="conditionJson.laterUpMajorLevelNames" item="name" index="index">
                                OR offer_item_later.old_course_major_level_name LIKE CONCAT(#{name},'%')
                            </foreach>
                            )
                        </if>
                        <if test="conditionJson.laterUpCourseTypeIds!=null and conditionJson.laterUpCourseTypeIds.size()>0">
                            AND ( 1=0
                            <foreach collection="conditionJson.laterUpCourseTypeIds" item="id" index="index">
                                OR FIND_IN_SET(#{id},offer_item_later.fk_institution_course_type_ids)
                            </foreach>
                            <foreach collection="conditionJson.laterUpCourseTypeNames" item="name" index="index">
                                OR offer_item_later.old_course_type_name LIKE CONCAT(#{name},'%')
                            </foreach>
                            )
                        </if>
                        <if test="conditionJson.laterUpCourseIds!=null and conditionJson.laterUpCourseIds.size()>0">
                            AND (offer_item_later.fk_institution_course_custom_id IN
                            <foreach collection="conditionJson.laterUpCourseIds" item="id" index="index" open="("
                                     separator="," close=")">
                                #{id,jdbcType=BIGINT}
                            </foreach>
                            <if test="conditionJson.laterUpCourseNames!=null and conditionJson.laterUpCourseNames.size()>0">
                                <foreach collection="conditionJson.laterUpCourseNames" item="name" index="index">
                                    OR offer_item_later.old_course_custom_name LIKE CONCAT('%',#{name},'%')
                                </foreach>
                            </if>
                            )
                        </if>
                    )
                    </if>
                    <if test="conditionJson.mainAndSubCourseFlag == 0 and conditionJson.subAndLaterCourseFlag == 1 and conditionJson.subCourseMark == true and conditionJson.laterCourseMark == true">
                        )
                    </if>
                )
            )
        </if>
    </select>

</mapper>
