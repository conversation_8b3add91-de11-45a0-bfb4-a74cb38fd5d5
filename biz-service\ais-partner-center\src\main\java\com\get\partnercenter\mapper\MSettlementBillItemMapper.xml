<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.partnercenter.mapper.MSettlementBillItemMapper">

    <resultMap id="BaseResultMap" type="com.get.partnercenter.entity.MSettlementBillItemEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkSettlementBillId" column="fk_settlement_bill_id" jdbcType="BIGINT"/>
            <result property="fkStudentId" column="fk_student_id" jdbcType="BIGINT"/>
            <result property="fkPayablePlanId" column="fk_payable_plan_id" jdbcType="BIGINT"/>
            <result property="fkCurrencyTypeNum" column="fk_currency_type_num" jdbcType="VARCHAR"/>
            <result property="amountActual" column="amount_actual" jdbcType="DECIMAL"/>
            <result property="serviceFeeActual" column="service_fee_actual" jdbcType="DECIMAL"/>
            <result property="exchangeRate" column="exchange_rate" jdbcType="DECIMAL"/>
            <result property="amountExchange" column="amount_exchange" jdbcType="DECIMAL"/>
            <result property="serviceFeeExchange" column="service_fee_exchange" jdbcType="DECIMAL"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>










</mapper>
