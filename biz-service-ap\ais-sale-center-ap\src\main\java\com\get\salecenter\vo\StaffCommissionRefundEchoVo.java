package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class StaffCommissionRefundEchoVo {

    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    @ApiModelProperty(value = "退款结算操作时间(开始)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createBeginTime;

    @ApiModelProperty(value = "退款结算操作时间(结束)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createEndTime;

    @ApiModelProperty(value = "退款审核操作时间(开始范围)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date refundReviewBeginTime;

    @ApiModelProperty(value = "退款审核操作时间(结束范围)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date refundReviewEndTime;

    @ApiModelProperty(value = "入学失败操作时间(开始范围)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stepFailureBeginTime;

    @ApiModelProperty(value = "入学失败操作时间(结束范围)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stepFailureEndTime;

    @ApiModelProperty(value = "奖金结算日期")
    private String settlementDate;

    /**
     * 退款审批状态
     */
    @ApiModelProperty(value = "退款审批状态")
    private String refundReviewStatusName;

    @ApiModelProperty(value = "退款结算状态")
    private String refundSettlementStatusName;
    /**
     * 结算角色/成员名称
     */
    @ApiModelProperty(value = "结算角色/成员名称")
    private String staffName;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    /**
     * 学生名称
     */
    @ApiModelProperty(value = "学生名称")
    private String studentName;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String institutionName;

}
