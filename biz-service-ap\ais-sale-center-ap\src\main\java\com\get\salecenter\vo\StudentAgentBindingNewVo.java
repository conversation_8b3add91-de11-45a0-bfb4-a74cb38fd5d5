package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * @author: Neil
 * @description: 查询学生业务代理绑定DTO
 * @date: 2022/5/9 16:04
 * @return
 */
@Data
public class StudentAgentBindingNewVo {

    @ApiModelProperty(value = "学生id")
    private Long studentId;

    @ApiModelProperty(value = "学生编号")
    private String studentNum;

    @ApiModelProperty(value = "学生名称")
    private String studentName;

    @ApiModelProperty(value = "学生名称（英文）")
    private String studentNameEng;

    @ApiModelProperty(value = "申请国家")
    private String areaCountryNames;

    @ApiModelProperty("申请方案绑定代理/BD")
    private Set<String> currentBdNameAndStaffNameList;

    @ApiModelProperty(value = "是否绑定多条代理")
    private Boolean isMultipleOffers = false;

//    @ApiModelProperty(value = "学生公司id")
//    private Long fkCompanyId;
    @ApiModelProperty(value = "学生代理绑定信息")
    private Set<String> currentStaffNameList;

    @ApiModelProperty(value = "注册信息 0 AIS注册/1 旧issue / 2新issue")
    private Integer fkPlatformType;

    @ApiModelProperty(value = "学生Id（ISSUE申请）")
    private Long fkStudentIdIssue;

    @ApiModelProperty(value = "学生Id（ISSUEv2版申请）")
    private Long fkStudentIdIssue2;

    @ApiModelProperty(value = "是否存在实付佣金绑定数据")
    private Boolean existPaymentForm;

//    @ApiModelProperty("申请方案绑定代理/BD")
//    private Set<String> studentOfferStaffNameList;
//    @ApiModelProperty(value = "学生申请方案绑定信息")
//    private List<AgentsBindingVo> studentOfferBinding;
//    @ApiModelProperty(value = "学生保险绑定信息")
//    private List<AgentsBindingVo> studentInsuranceBinding;
//    @ApiModelProperty(value = "学生住宿绑定信息")
//    private List<AgentsBindingVo> studentAccommodationBinding;

}
