package com.get.insurancecenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:Oliver
 * @Date: 2025/7/25
 * @Version 1.0
 * @apiNote:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("m_credit_card_reminder_notifier")
@ApiModel("信用卡提醒配置通知人")
public class CreditCardReminderNotifier extends BaseEntity {

    @ApiModelProperty("信用卡提醒配置Id")
    private Long fkCreditCardReminderId;

    @ApiModelProperty("通知人姓名")
    private String name;

    @ApiModelProperty("通知人手机")
    private String mobile;

    @ApiModelProperty("通知人邮件")
    private String email;

    @ApiModelProperty("信用卡ID")
    @TableField(exist = false)
    private Long creditCardId;
}