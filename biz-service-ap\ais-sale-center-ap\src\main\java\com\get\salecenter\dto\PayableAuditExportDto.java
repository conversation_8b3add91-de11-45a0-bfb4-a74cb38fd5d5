package com.get.salecenter.dto;

import com.get.common.result.FocExportVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PayableAuditExportDto {

    @NotNull
    @ApiModelProperty(value = "查询条件")
    private PayablePlanAuditDto payablePlanAuditVo;

//    @NotEmpty(message = "列名不能为空")
    @NotNull
    @ApiModelProperty(value = "导出参数")
    private List<FocExportVo> focExportVos;
}