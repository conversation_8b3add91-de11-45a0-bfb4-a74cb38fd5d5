package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("r_payable_plan_settlement_agent")
public class PayablePlanSettlementAgent extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 应付计划Id
     */
    @ApiModelProperty(value = "应付计划Id")
    private Long fkPayablePlanId;
    /**
     * 代理Id
     */
    @ApiModelProperty(value = "代理Id")
    private Long fkAgentId;
}