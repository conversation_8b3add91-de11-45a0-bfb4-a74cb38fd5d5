package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.vo.ConventionSponsorFeeVo;
import com.get.salecenter.entity.ConventionSponsorSponsorFee;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ConventionSponsorSponsorFeeMapper extends BaseMapper<ConventionSponsorSponsorFee> {



    /**
     * @return java.util.List<com.get.salecenter.vo.ConventionSponsorFeeVo>
     * @Description :根据赞助商id查找所选赞助类型
     * @Param [sponsorId]
     * <AUTHOR>
     */
    List<ConventionSponsorFeeVo> getSponsorFeeDtoList(Long sponsorId);

    /**
     * @return java.util.List<com.get.salecenter.vo.ConventionSponsorFeeVo>
     * @Description :根据回执码查找所选赞助类型
     * @Param [conventionId, receiptCode]
     * <AUTHOR>
     */
    List<ConventionSponsorFeeVo> getSponsorFeeDtoListByReceiptCode(Long conventionId, String receiptCode);
}