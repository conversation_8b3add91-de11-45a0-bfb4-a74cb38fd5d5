package com.get.core.log.controller;

import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.dto.LogInstitutionProviderDto;
import com.get.core.log.service.ILogInstitutionProviderService;
import com.get.core.log.vo.LogInstitutionProviderVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/6/17 11:06
 * @verison: 1.0
 * @description: 登录日志控制器
 */
@Api(tags = "提供商日志管理")
@RestController
@RequestMapping("log/institutionProvider")
public class InstitutionProviderLogController {
    @Resource
    private ILogInstitutionProviderService logInstitutionProviderService;

    /**
     * 详情
     *
     * @param id
     * @return
     */
//    @ApiOperation(value = "提供商修改日志详情接口", notes = "id为此条数据id")
//    @GetMapping("/{id}")
//    public ResponseBo<LogInstitutionProviderDto> detail(@PathVariable("id") Long id) {
//        LogInstitutionProviderDto data = logInstitutionProviderService.findLogInstitutionProviderById(id);
//        return new ResponseBo<>(data);
//    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     */
    @ApiOperation(value = "列表数据", notes = "")
    @PostMapping("datas")
    public ResponseBo<LogInstitutionProviderDto> datas(@RequestBody SearchBean<LogInstitutionProviderVo> page) {
        //数据库查询的列表数据
        List<LogInstitutionProviderDto> datas = logInstitutionProviderService.getLogInstitutionProviders(page.getData(), page);
        //简化page对象 只需分页内容
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 新增信息
     *
     * @param logInstitutionProvider
     * @return
     */
//    @ApiIgnore
//    @PostMapping("add")
//    public void add(@RequestBody LogInstitutionProviderVo logInstitutionProvider) {
//        logInstitutionProviderService.addLogInstitutionProvider(logInstitutionProvider);
//    }


}
