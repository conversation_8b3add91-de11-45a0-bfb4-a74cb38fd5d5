package com.get.pmpcenter.dto.agent;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author:<PERSON>
 * @Date: 2025/8/22
 * @Version 1.0
 * @apiNote:代理审批记录DTO
 */
@Data
public class AgentApprovalRecordDto {

    @ApiModelProperty(value = "学校提供商ID")
    private Long institutionProviderId;

    @ApiModelProperty(value = "分公司ID")
    private Long companyId;

    @ApiModelProperty(value = "方案ID")
    private Long planId;

    @ApiModelProperty(value = "方案名称")
    private String planName;

    @ApiModelProperty(value = "关键字")
    private String keyword;
}
