package com.get.platformconfigcenter.dao.appissue;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.StudentAttachment;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("issuedb")
public interface StudentAttachmentMapper extends BaseMapper<StudentAttachment> {

//    int insert(StudentAttachment record);
//
//    int insertSelective(StudentAttachment record);
//
//    int updateByPrimaryKeySelective(StudentAttachment record);
//
//    int updateByPrimaryKey(StudentAttachment record);
//
//    /**
//     * 获取最大排序值
//     *
//     * @Date 16:23 2021/5/20
//     * <AUTHOR>
//     */
//    Integer getMaxViewOrder();

}