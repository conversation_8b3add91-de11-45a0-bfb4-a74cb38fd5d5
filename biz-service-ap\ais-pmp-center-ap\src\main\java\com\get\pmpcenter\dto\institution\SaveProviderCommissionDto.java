package com.get.pmpcenter.dto.institution;

import com.get.pmpcenter.vo.institution.ProviderCommissionListVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/2/12  10:22
 * @Version 1.0
 * 保存佣金明细
 */
@Data
public class SaveProviderCommissionDto extends ProviderCommissionListVo {

    @ApiModelProperty(value = "佣金方案Id")
    @NotNull(message = "佣金方案Id不能为空")
    private Long commissionPlanId;

}
