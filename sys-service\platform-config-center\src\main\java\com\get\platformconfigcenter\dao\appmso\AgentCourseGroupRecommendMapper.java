package com.get.platformconfigcenter.dao.appmso;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.platformconfigcenter.entity.AgentCourseGroupRecommend;
import com.get.platformconfigcenter.dto.AgentCourseGroupRecommendListDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
@DS("appmsodb")
public interface AgentCourseGroupRecommendMapper extends BaseMapper<AgentCourseGroupRecommend> {
//    int deleteByPrimaryKey(Long id);

    int insert(AgentCourseGroupRecommend record);

//    int insertSelective(AgentCourseGroupRecommend record);

//    AgentCourseGroupRecommend selectByPrimaryKey(Long id);

//    int updateByPrimaryKeySelective(AgentCourseGroupRecommend record);

//    int updateByPrimaryKey(AgentCourseGroupRecommend record);

    Integer getMaxViewOrder();

    List<AgentCourseGroupRecommend> getCourseGroupRecommendationsList(IPage<AgentCourseGroupRecommend> iPage, AgentCourseGroupRecommendListDto data);
}