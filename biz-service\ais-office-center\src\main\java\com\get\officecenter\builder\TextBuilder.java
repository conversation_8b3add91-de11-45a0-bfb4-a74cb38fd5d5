package com.get.officecenter.builder;

import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;
import me.chanjar.weixin.cp.bean.message.WxCpXmlOutMessage;
import me.chanjar.weixin.cp.bean.message.WxCpXmlOutTextMessage;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;

/**
 *  <AUTHOR>
 */
public class TextBuilder extends AbstractBuilder {

  @Override
  public WxCpXmlOutMessage build(String content, WxCpTpXmlMessage wxMessage, WxCpTpService wxCpTpservice) {
    WxCpXmlOutTextMessage m = WxCpXmlOutMessage.TEXT().content(content)
            .fromUser(wxMessage.getToUserName()).toUser(wxMessage.getFromUserName())
            .build();
    return m;
  }
}
