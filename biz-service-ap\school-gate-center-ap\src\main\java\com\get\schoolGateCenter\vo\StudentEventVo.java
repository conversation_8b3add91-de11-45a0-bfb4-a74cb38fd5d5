package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2020/11/10
 * @TIME: 16:08
 * @Description:
 **/
@Data
public class StudentEventVo extends BaseVoEntity {

    /**
     * 学生Id
     */
    @NotNull(message = "学生Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "学生Id")
    private Long fkStudentId;

    /**
     * 学生事件类型Id
     */
    @ApiModelProperty(value = "学生事件类型Id")
    private Long fkStudentEventTypeId;

    /**
     * 事件内容
     */
    @ApiModelProperty(value = "事件内容")
    private String description;

    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyWord;

}
