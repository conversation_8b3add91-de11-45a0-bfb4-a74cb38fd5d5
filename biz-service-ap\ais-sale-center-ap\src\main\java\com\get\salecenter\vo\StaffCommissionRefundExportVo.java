package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: neil
 * @verison: 1.0
 * @description:
 */
@Data
public class StaffCommissionRefundExportVo {


    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    @ApiModelProperty(value = "奖金结算日期")
    private String settlementDate;

    @ApiModelProperty(value = "结算角色")
    private String roleAndStaff;

    @ApiModelProperty(value = "结算步骤")
    private String staffCommissionActionStepName;

    @ApiModelProperty(value = "已结金额")
    private BigDecimal commissionAmount;

    @ApiModelProperty(value = "退款审核状态")
    private String refundReviewStatusName;

    @ApiModelProperty(value = "审核人/审核信息")
    private String refundReviewStaffContent;

    @ApiModelProperty("结算退款原因")
    private String reasonName;

    @ApiModelProperty(value = "退款结算状态")
    private String refundSettlementStatusName;

    @ApiModelProperty(value = "绑定代理")
    private String agentName;

    @ApiModelProperty(value = "学生名称")
    private String studentName;

    @ApiModelProperty(value = "国家")
    private String fkAreaCountryName;

    @ApiModelProperty(value = "学校供应商(渠道/集团/学校)")
    private String fkInstitutionProviderChannelName;

    @ApiModelProperty(value = "学校")
    private String institutionFullName;

    @ApiModelProperty(value = "课程")
    private String courseFullName;

    @ApiModelProperty(value = "开学时间")
    private String deferOpeningTime;

    @ApiModelProperty(value = "申请计划状态")
    private String itemStatusName;

    @ApiModelProperty(value = "申请计划步骤")
    private String stepName;
}
