<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.permissioncenter.dao.StaffInstitutionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.get.permissioncenter.entity.StaffInstitution">
        <id column="id" property="id" />
        <result column="fk_staff_id" property="fkStaffId" />
        <result column="fk_institution_id" property="fkInstitutionId" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_create_user" property="gmtCreateUser" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="gmt_modified_user" property="gmtModifiedUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, fk_staff_id, fk_institution_id, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
    </sql>
    <select id="getBusinessSchoolList" resultType="com.get.permissioncenter.vo.BusinessSchoolVo">
        SELECT
            mi.* ,CASE WHEN IFNULL(mi.name_chn,'')='' THEN mi.`name` ELSE CONCAT(mi.`name`,'（',mi.name_chn,'）') END institutionName
        FROM
            ais_institution_center.m_institution AS mi
                LEFT JOIN r_staff_institution as rsi on mi.id = rsi.fk_institution_id
        WHERE rsi.fk_staff_id  = #{businessSchoolDto.fkStaffId}
    </select>
    <select id="getAssignBusinessSchoolList" resultType="com.get.permissioncenter.vo.AssignBusinessSchoolVo">
        SELECT
            mi.*,
            CASE WHEN IFNULL(mi.name_chn,'')='' THEN mi.`name` ELSE CONCAT(mi.`name`,'（',mi.name_chn,'）') END institutionName
        FROM
            ais_institution_center.m_institution mi
                LEFT JOIN r_staff_institution rsi on mi.id = rsi.fk_institution_id and rsi.fk_staff_id = #{assignBusinessSchoolDto.fkStaffId}
        where 1=1
          and rsi.fk_institution_id IS NULL
          <if test="assignBusinessSchoolDto.fkAreaCountryId != null">
              and mi.fk_area_country_id = #{assignBusinessSchoolDto.fkAreaCountryId}
          </if>
          <if test="assignBusinessSchoolDto.keyWord != null and assignBusinessSchoolDto.keyWord !=''">
              and (
                mi.num like concat('%',#{assignBusinessSchoolDto.keyWord},'%')
              or mi.name like concat('%',#{assignBusinessSchoolDto.keyWord},'%')
              or mi.name_chn like concat('%',#{assignBusinessSchoolDto.keyWord},'%')
              )
          </if>
        ORDER BY mi.is_active desc,mi.gmt_create desc
    </select>

</mapper>
