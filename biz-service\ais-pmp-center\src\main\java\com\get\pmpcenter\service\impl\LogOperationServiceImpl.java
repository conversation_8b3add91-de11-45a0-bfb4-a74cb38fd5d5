package com.get.pmpcenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.pmpcenter.dto.common.LogRecordDto;
import com.get.pmpcenter.entity.LogOperation;
import com.get.pmpcenter.mapper.LogOperationMapper;
import com.get.pmpcenter.service.LogOperationService;
import com.get.pmpcenter.vo.common.LogRecordVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @Author:Oliver
 * @Date: 2025/2/28  12:01
 * @Version 1.0
 */
@Service
@Slf4j
public class LogOperationServiceImpl extends ServiceImpl<LogOperationMapper, LogOperation> implements LogOperationService {

    @Autowired
    private LogOperationMapper logOperationMapper;

    @Override
    public List<LogRecordVo> logList(Integer type, Long id) {
        if (Objects.isNull(type) || Objects.isNull(id)) {
//            throw new GetServiceException("参数不能为空");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"PMP_PARAM_REQUIRED","参数不能为空"));
        }
        if (type.equals(1)) {
            return logOperationMapper.selectProviderLogs(id);
        }
        return logOperationMapper.selectAgentLogs(id);
    }

    @Override
    public List<LogRecordVo> commissionLogRecordPage(LogRecordDto params, Page page) {
        IPage<LogRecordVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<LogRecordVo> list = logOperationMapper.commissionLogRecordPage(pages, params);
        int totalCount = (int) pages.getTotal();
        Integer showCount = page.getShowCount();
        page.setTotalPage(page.getShowCount() != null && showCount > 0 ? totalCount % showCount == 0 ? totalCount / showCount : totalCount / showCount + 1 : 0);
        page.setTotalResult(totalCount);
        return list;
    }
}
