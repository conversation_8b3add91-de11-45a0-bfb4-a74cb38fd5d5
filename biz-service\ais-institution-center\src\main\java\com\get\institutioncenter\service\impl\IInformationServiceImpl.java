package com.get.institutioncenter.service.impl;

import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.institutioncenter.dao.InformationMapper;
import com.get.institutioncenter.service.IInformationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName: InformationServiceImpl
 * @Author: Eric
 * @Date: 2023/4/25 18:28
 * @Version: 1.0
 */
@Service
public class IInformationServiceImpl implements IInformationService {


    @Resource
    private InformationMapper informationMapper;

    @Override
    public List<BaseSelectEntity> getAllInformationSelect() {
        return informationMapper.getAllInformationSelect();
    }
}
