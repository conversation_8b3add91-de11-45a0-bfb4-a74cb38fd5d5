package com.get.salecenter.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.salecenter.vo.EmailStatisticsOfferItemVo;
import com.get.salecenter.vo.StudentOfferNoticeListVo;
import com.get.salecenter.entity.StudentOfferNotice;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.dto.EmailStatisticsDto;
import com.get.salecenter.dto.StudentOfferNoticeListDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Mapper
public interface StudentOfferNoticeMapper extends BaseMapper<StudentOfferNotice> {

    /**
     *
     * @param emailStatisticsDto
     * @param staffFollowerIds
     * @param countryIds
     * @param isStudentOfferItemFinancialHiding
     * @param isStudentAdmin
     * @return
     */
    @DS("saledb-doris")
    List<EmailStatisticsOfferItemVo> getEmailStatisticsOfferItem(@Param("emailStatisticsDto") EmailStatisticsDto emailStatisticsDto,
                                                                 @Param("staffFollowerIds")List<Long> staffFollowerIds,
                                                                 @Param("countryIds")List<Long> countryIds,
                                                                 @Param("isStudentOfferItemFinancialHiding")Boolean isStudentOfferItemFinancialHiding,
                                                                 @Param("isStudentAdmin")Boolean isStudentAdmin,
                                                                 @Param("isBd")Boolean isBd,
                                                                 @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                                 @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * 获取所有语言课程计划
     * @param emailStatisticsDto
     * @return
     */
    List<EmailStatisticsOfferItemVo> getEmailStatisticsOfferItemIsLanguageCourse(@Param("emailStatisticsDto") EmailStatisticsDto emailStatisticsDto);


    List<StudentOfferNoticeListVo> getStudentOfferNotices(IPage<StudentOfferNoticeListVo> iPage,
                                                          @Param("studentOfferNoticeListDto") StudentOfferNoticeListDto studentOfferNoticeListDto,
                                                          @Param("loginId")String loginId);
}
