package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class InsuranceSummaryReDto extends BaseVoEntity {

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
    /**
     * 保险批量生成应收应付数组
     */
    @ApiModelProperty(value = "保险批量生成应收应付数组")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private List<Long> insuranceIds;

    /**
     * 佣金币种
     */
    @ApiModelProperty(value = "佣金币种")
    private String fkCurrencyTypeNumCommission;
    /**
     * 收取佣金比例%
     */
    @ApiModelProperty(value = "收取佣金比例%")
    private BigDecimal commissionRateReceivable;


    /**
     * 固定收取佣金金额
     */
    @ApiModelProperty(value = "固定收取佣金金额")
    private BigDecimal fixedAmountReceivable;

  
}
