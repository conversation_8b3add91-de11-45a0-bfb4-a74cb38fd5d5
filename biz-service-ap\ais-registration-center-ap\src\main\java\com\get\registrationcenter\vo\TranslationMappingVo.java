package com.get.registrationcenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import com.get.registrationcenter.entity.RegistrationTranslationMapping;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Hardy
 * @create: 2021/5/14 17:24
 * @verison: 1.0
 * @description:
 */
@Data
public class TranslationMappingVo extends BaseVoEntity {
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;
    /**
     * 字段名
     */
    @ApiModelProperty(value = "字段名")
    private String fkColumnName;
    /**
     * 字段标题名
     */
    @ApiModelProperty(value = "字段标题名")
    private String inputTitle;
    /**
     * 输入类型：0单行/1多行/2富文本
     */
    @ApiModelProperty(value = "输入类型：0单行/1多行/2富文本")
    private Integer inputType;
    /**
     * 最大字符限制数
     */
    @ApiModelProperty(value = "最大字符限制数")
    private Integer maxLength;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    private Integer viewOrder;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;
    /**
     * 标准版内容
     */
    @ApiModelProperty(value = "标准版内容")
    private String standardContent;
    /**
     * 翻译内容
     */
    @ApiModelProperty(value = "翻译内容")
    private String translationContent;
}
