package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;


/**
 * <AUTHOR>
 */
@Data
@TableName("u_student_service_fee_type")
public class StudentServiceFeeType extends BaseEntity implements Serializable {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "类型名称")
    @Column(name = "type_name")
    private String typeName;


    /**
     * 公司Id
     */
    @ApiModelProperty(value = "类型key")
    @Column(name = "type_key")
    private String typeKey;


    /**
     * 公司Id
     */
    @ApiModelProperty(value = "排序")
    @Column(name = "view_order")
    private Integer viewOrder;
}
