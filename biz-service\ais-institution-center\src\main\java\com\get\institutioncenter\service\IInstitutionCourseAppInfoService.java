package com.get.institutioncenter.service;

import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.dto.InstitutionCourseAppInfoDataProcessDto;
import com.get.institutioncenter.dto.WeScholarshipAppDto;
import com.get.institutioncenter.vo.CourseOtherInfoVo;
import com.get.institutioncenter.entity.InstitutionCourseAppInfo;
import com.get.institutioncenter.dto.QueryOtherCourseDto;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface IInstitutionCourseAppInfoService extends BaseService<InstitutionCourseAppInfo> {

    /**
     * 批量增加
     * @param appInfos
     * @param fkTableId
     * @param fkTableName
     * @param effectiveDate
     */
    void batchAdd(List<InstitutionCourseAppInfoDataProcessDto.CourseAppInfoResultMappingVo> appInfos, Long fkTableId, String fkTableName, String effectiveDate);


    /**
     * 信息封装
     * @param infoDataProcessVo
     * @param flag
     * @param fkTableName
     * @return
     */
    void packageInfo(List<InstitutionCourseAppInfoDataProcessDto> infoDataProcessVo, Boolean flag, String fkTableName);

    /**
     * 批量更新
     * @param appInfos
     * @param fkTableId
     * @param fkTableName
     */
    void batchUpdate(List<InstitutionCourseAppInfoDataProcessDto.CourseAppInfoResultMappingVo> appInfos, Long fkTableId, String fkTableName, String effectiveDate);

    /**
     * 优先级处理
     * @param weScholarshipAppDto
     * @param fkTableName
     * @return
     */
    InstitutionCourseAppInfoDataProcessDto priorityProcess(WeScholarshipAppDto weScholarshipAppDto, String fkTableName);


    /**
     * 数据过滤
     * @param weScholarshipAppDto
     * @param fkTableName
     * @return
     */
    List<InstitutionCourseAppInfoDataProcessDto> testPriority(WeScholarshipAppDto weScholarshipAppDto, String fkTableName);


    /**
     * 获取其他课程信息
     * @param queryOtherCourseDto
     * @param page
     * @return
     */
    ListResponseBo<CourseOtherInfoVo> getOtherCourseInfo(QueryOtherCourseDto queryOtherCourseDto, Page page);
}
