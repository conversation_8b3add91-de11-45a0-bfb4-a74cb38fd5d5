package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * author:<PERSON>
 * Time: 17:47
 * Date: 2023/1/17
 * Description:收款币种：500美金
 * 折合港币：1500港币
 * 收款币种（不带币种的）：500
 * 折合港币（不带币种的）：1500
 */
@Data
public class StudentReceivableHkdVo {

    @ApiModelProperty(value = "收款币种（不带币种的）")
    private String receivableAmount;

    @ApiModelProperty(value = "折合港币（不带币种的）")
    private String receivableAmountHkd;

    @ApiModelProperty(value = "收款币种")
    private String receivableAmountInfo;

    @ApiModelProperty(value = "折合港币")
    private String receivableAmountInfoHkd;
}
