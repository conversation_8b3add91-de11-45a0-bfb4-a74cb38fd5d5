package com.get.core.log.controller;

import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.dto.LogLoginDto;
import com.get.core.log.model.LogLogin;
import com.get.core.log.service.ILogLoginService;
import com.get.core.log.vo.LogLoginVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/6/17 11:06
 * @verison: 1.0
 * @description: 登录日志控制器
 */
@Api(tags = "登录日志管理")
@RestController
@RequestMapping("log/login")
public class LoginLogController {
    @Resource
    private ILogLoginService logLoginService;

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "登录日志详情接口", notes = "id为此条数据id")
    @GetMapping("/{id}")
    public ResponseBo<LogLoginDto> detail(@PathVariable("id") Long id) {
        LogLoginDto data = logLoginService.findLogLoginById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     */
    @ApiOperation(value = "列表数据", notes = "staffName为姓名，staffLoginId为登录账号 ，loginStartTime为登录开始日期，loginEndTime为登录结束日期，loginOutStartTime为登出开始日期,loginOutEndTime为登出结束日期")
    @PostMapping("datas")
    public ResponseBo<LogLoginDto> datas(@RequestBody SearchBean<LogLoginVo> page) {
        //数据库查询的列表数据
        List<LogLoginDto> datas = logLoginService.getLogLogins(page.getData(), page);
        //简化page对象 只需分页内容
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 新增信息
     *
     * @param logLogin
     * @return
     */
    @ApiIgnore
    @PostMapping("add")
    public void add(@RequestBody LogLogin logLogin) {
        logLoginService.addLogLogin(logLogin);
    }

    @PostMapping("forceLogout/{id}")
    public ResponseBo forceLogout(@PathVariable("id") Long id){
        return logLoginService.forceLogout(id);
    }

    @PostMapping("getStaffLoginInfo")
    public ListResponseBo<LogLoginDto> getStaffLoginInfo(@RequestBody SearchBean<LogLoginVo> searchBean){
        return new ListResponseBo<>(logLoginService.getStaffLoginInfo(searchBean.getData(),searchBean),searchBean);
    }

    /**
     * 登出日志
     *
     * @return
     */
    @ApiOperation(value = "登出日志", notes = "登出日志")
    @GetMapping("logout")
    public ResponseBo logout() {
        logLoginService.updateLogLogin();
        return ResponseBo.ok();
    }
}
