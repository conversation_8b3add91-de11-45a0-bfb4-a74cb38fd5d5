package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.persistence.Column;
import lombok.Data;

/**
 * (RStudentIdentify)学生识别表实体类
 */
@Data
@TableName("r_student_identify")
public class RStudentIdentify extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -1L;

    @ApiModelProperty(value = "学生Id")
    @Column(name = "fk_student_id")
    private Long fkStudentId;

    @ApiModelProperty(value = "识别来源：0邮件/1学生管理")
    @Column(name = "identify_from")
    private Integer identifyFrom;

    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

}

