package com.get.institutioncenter.vo;

import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/1/8 10:14
 * @verison: 1.0
 * @description:
 */
@Data
public class ContractFormulaVo extends BaseEntity {
    /**
     * 国家区域id
     */
    @ApiModelProperty(value = "国家区域id")
    private List<Long> countryIdList;

    /**
     * 国家区域name
     */
    @ApiModelProperty(value = "国家区域name")
    private String countryName;

    /**
     * 课程类型id
     */
    @ApiModelProperty(value = "课程类型id")
    private List<Long> courseTypeIdList;

    /**
     * 课程类型name
     */
    @ApiModelProperty(value = "课程类型name")
    private String courseTypeName;

    /**
     * 课程等级id
     */
    @ApiModelProperty(value = "课程等级id")
    private List<Long> majorLevelIdList;

    /**
     * 课程等级name
     */
    @ApiModelProperty(value = "课程等级name")
    private String majorLevelName;

    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    private List<Long> companyIdList;

    /**
     * 公司name
     */
    @ApiModelProperty(value = "公司name")
    private String companyName;

    /**
     * 公式类型name
     */
    @ApiModelProperty(value = "公式类型name")
    private String formulaTypeName;

    /**
     * 统计类型name
     */
    @ApiModelProperty(value = "统计类型name")
    private String countTypeName;

    /**
     * 学生来源国家区域id
     */
    @ApiModelProperty(value = "学生来源国家区域id")
    private List<Long> studentCountryIdList;

    /**
     * 学生来源国家区域name
     */
    @ApiModelProperty(value = "学生来源国家区域name")
    private String studentCountryName;

    /**
     * 学校id
     */
    @ApiModelProperty(value = "学校id")
    private List<Long> institutionIdList;

    /**
     * 学校name
     */
    @ApiModelProperty(value = "学校name")
    private String institutionName;

    /**
     * 课程id
     */
    @ApiModelProperty(value = "课程id")
    private List<Long> courseIdList;

    /**
     * 课程name
     */
    @ApiModelProperty(value = "课程name")
    private String courseName;

    /**
     * 合同公式佣金配置
     */
    @ApiModelProperty(value = "合同公式佣金配置")
    private List<ContractFormulaCommissionVo> contractFormulaCommissionDtos;


    /**
     * 校区id
     */
    @ApiModelProperty(value = "校区id")
    private List<Long> zoneIdList;

    /**
     * 学院id
     */
    @ApiModelProperty(value = "学院id")
    private List<Long> facultyIdList;

    /**
     * 前置学校集团(多选)
     */
    @ApiModelProperty(value = "前置学校集团(多选)")
    private List<Long> preGroupIdList;

    /**
     * 前置学校(多选)
     */
    @ApiModelProperty(value = "前置学校(多选)")
    private List<Long> preInstitutionIdList;

    /**
     * 前置专业等级(多选)
     */
    @ApiModelProperty(value = "前置专业等级(多选)")
    private List<Long> preMajorLevelList;

    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "渠道名称")
    private String institutionChannelNames;

    /**
     * 所属渠道Ids
     */
    @ApiModelProperty(value = "所属渠道Ids")
    private List<Long> institutionChannelIds;

    @ApiModelProperty(value = "国籍（学生出生地国家/地区）【不包括】")
    private List<Long> studentCountryIdNotInList;

    //================实体类ContractFormula======================
    private static final long serialVersionUID = 1L;
    /**
     * 学校提供商Id
     */
    @ApiModelProperty(value = "学校提供商Id")
    @Column(name = "fk_institution_provider_id")
    private Long fkInstitutionProviderId;
    /**
     * 公式类型：0常规/1奖励/2一次性奖励
     */
    @ApiModelProperty(value = "公式类型：0常规/1奖励/2一次性奖励")
    @Column(name = "formula_type")
    private Integer formulaType;
    /**
     * 公式名称
     */
    @ApiModelProperty(value = "公式名称")
    @Column(name = "title")
    private String title;
    /**
     * 统计类型：0学生数，1学费，2课程长度(周)，3课程长度(月)，4课程长度(年)
     */
    @UpdateWithNull
    @ApiModelProperty(value = "统计类型：0学生数，1学费，2课程长度(周)，3课程长度(月)，4课程长度(年)")
    @Column(name = "count_type")
    private Integer countType;
    /**
     * 统计目标值(小)
     */
    @UpdateWithNull
    @ApiModelProperty(value = "统计目标值(小)")
    @Column(name = "count_vale_min")
    private BigDecimal countValeMin;
    /**
     * 统计目标值(大)
     */
    @UpdateWithNull
    @ApiModelProperty(value = "统计目标值(大)")
    @Column(name = "count_vale_max")
    private BigDecimal countValeMax;
    /**
     * 公式条件类型(多选)：0转代理学生 / 1学生获得奖学金占学费的60%以上 / 4只读第一阶段、5只读第二阶段、6只读第三阶段、7只读第四阶段
     */
//    @ApiModelProperty(value = "条件类型（枚举）：0转代理学生 / 1学生获得奖学金占学费的60%以上 / 4只读第一阶段、5只读第二阶段、6只读第三阶段、7只读第四阶段")
    @UpdateWithNull
    @ApiModelProperty(value = "公式条件类型(多选)：0转代理学生 / 1学生获得奖学金占学费的60%以上 / 4只读第一阶段、5只读第二阶段、6只读第三阶段、7只读第四阶段")
    @Column(name = "condition_type")
    private String conditionType;
    /**
     * 生效开始时间
     */
    @UpdateWithNull
    @ApiModelProperty(value = "生效开始时间")
    @Column(name = "start_time")
    private Date startTime;
    /**
     * 生效结束时间
     */
    @UpdateWithNull
    @ApiModelProperty(value = "生效结束时间")
    @Column(name = "end_time")
    private Date endTime;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;
    /**
     * 代理佣金币种编号（不同佣金币种时才需要填写）
     */
    @ApiModelProperty(value = "代理佣金币种编号（不同佣金币种时才需要填写）")
    @Column(name = "fk_currency_type_num_ag")
    private String fkCurrencyTypeNumAg;
    /**
     * 佣金上限(总)
     */
    @UpdateWithNull
    @ApiModelProperty(value = "佣金上限(总)")
    @Column(name = "limit_amount")
    private BigDecimal limitAmount;
    /**
     * 代理佣金上限(总)
     */
    @ApiModelProperty(value = "代理佣金上限(总)")
    @Column(name = "limit_amount_ag")
    private BigDecimal limitAmountAg;
    /**
     * 公式备注
     */
    @UpdateWithNull
    @ApiModelProperty(value = "公式备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 排序（倒序），数字由大到小排列
     */
    @ApiModelProperty(value = "排序（倒序），数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    @Column(name = "id_gea")
    private String idGea;
    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    @Column(name = "id_iae")
    private String idIae;

}
