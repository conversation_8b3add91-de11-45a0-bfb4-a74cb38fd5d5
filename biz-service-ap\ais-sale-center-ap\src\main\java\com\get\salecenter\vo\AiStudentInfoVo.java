package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * AI学生信息Vo
 */
@Data
public class AiStudentInfoVo {
    @ApiModelProperty(value = "学生id")
    private Long studentId;

    @ApiModelProperty(value = "公司名")
    private String companyName;

    @ApiModelProperty(value = "生日")
    private Date birthday;

    @ApiModelProperty(value = "学生名")
    private String studentName;

    @ApiModelProperty(value = "性别")
    private String sex;

    @ApiModelProperty(value = "代理名字")
    private String agentName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;


}
