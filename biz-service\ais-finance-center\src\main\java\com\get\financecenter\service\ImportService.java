package com.get.financecenter.service;


import com.get.core.tool.api.Result;
import com.get.financecenter.vo.OccVo;


import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

public interface ImportService {


    Result<BigDecimal> importReceivableData(List<OccVo> receivedOcList, Long receivablePlanId, Long targetId, String currency);


    void importPayableData(List<OccVo> hiPaidInfoList, Long payablePlanId, String currency, Long agentId, BigDecimal payableAmount, Double receivableFee);


    void importPaymentNewRecord(OccVo occVo, Long payablePlanId, String currency, Long agentId, BigDecimal payableAmount, Long backId);


    void updateFee(Set<Long> payItemIds);
}
