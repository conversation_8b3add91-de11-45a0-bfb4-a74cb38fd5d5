<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.ContractFormulaInstitutionMapper">

  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.ContractFormulaInstitution" keyProperty="id" useGeneratedKeys="true">
    insert into r_contract_formula_institution
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkContractFormulaId != null">
        fk_contract_formula_id,
      </if>
      <if test="fkInstitutionId != null">
        fk_institution_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkContractFormulaId != null">
        #{fkContractFormulaId,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionId != null">
        #{fkInstitutionId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getInstitutionIdListByFkid" resultType="java.lang.Long">
    select
     fk_institution_id
    from
     r_contract_formula_institution
    where
     fk_contract_formula_id = #{contractFormulaId}
  </select>

  <select id="getInstitutionNameByFkid" resultType="java.lang.String">
    SELECT
    CASE WHEN IFNULL(b.name_chn,'')='' THEN b.`name` ELSE CONCAT(b.`name`,'（',b.name_chn,'）') END fullName
    FROM
        r_contract_formula_institution a
        LEFT JOIN m_institution b ON a.fk_institution_id = b.id
    WHERE
        fk_contract_formula_id = #{contractFormulaId}
  </select>
</mapper>