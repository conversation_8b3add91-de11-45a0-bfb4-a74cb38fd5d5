package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: Hardy
 * @create: 2022/3/14 15:20
 * @verison: 1.0
 * @description:  应付计划学习导出模板
 */
@Data
public class ReceivablePlanExportVo {

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    /**
     * 发票编号
     */
    @ApiModelProperty(value = "发票编号")
    private String fkInvoiceNum;

    /**
     * 应收类型名称
     */
    @ApiModelProperty(value = "应收类型名称")
    private String fkTypeName;

    @ApiModelProperty(value = "申请计划编号")
    private String offerItemNum;

    @ApiModelProperty(value = "学生名称")
    private String studentName;

    @ApiModelProperty(value = "佣金结算标记")
    private String commissionMark;

    @ApiModelProperty(value = "学生名称(英文)")
    private String studentNameEng;

    @ApiModelProperty(value = "生日")
    private String studentBirthday;

    @ApiModelProperty(value = "性别")
    private String genderName;

    @ApiModelProperty(value = "学生")
    private String studentNameEn;

    @ApiModelProperty(value = "姓名（英/拼音）")
    private String studentFirstName;



    @ApiModelProperty(value = "学生号")
    private String studentId;

    @ApiModelProperty(value = "BD+代理编号")
    private String bdAgentNum;

    @ApiModelProperty(value = "代理编号")
    private String agentNum;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;

    @ApiModelProperty(value = "渠道信息")
    private String fkInstitutionProviderName;

    @ApiModelProperty("佣金合同方")
    private String fkInstitutionChannelName;

    @ApiModelProperty(value = "学校提供商")
    private String providerName;

    @ApiModelProperty(value = "业务信息")
    private String businessInformation;

    @ApiModelProperty(value = "学校")
    private String fkInstitutionName;

    @ApiModelProperty(value = "课程名称")
    private String fkCourseName;

    @ApiModelProperty(value = "开学时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String deferOpeningTime;

    @ApiModelProperty("申请步骤状态")
    private String stepName;
    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;

    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String fkCurrencyTypeName;

    @ApiModelProperty(value = "学费金额")
    private BigDecimal tuitionAmount;

    @ApiModelProperty(value = "佣金费率")
    private BigDecimal commissionRate;

    @ApiModelProperty(value = "渠道费率%")
    private BigDecimal netRate;

    @ApiModelProperty(value = "佣金金额")
    private BigDecimal commissionAmount;

    @ApiModelProperty(value = "定额金额")
    private BigDecimal fixedAmount;

    @ApiModelProperty(value = "其他金额")
    private BigDecimal bonusAmount;

    @ApiModelProperty(value = "其他金额类型")
    private Integer bonusType;
    /**
     * 应收金额
     */
    @ApiModelProperty(value = "应收金额")
    private BigDecimal receivableAmount;

    /**
     * 实收金额
     */
    @ApiModelProperty(value = "实收金额")
    private BigDecimal actualReceivableAmount;

    /**
     * 差额
     */
    @ApiModelProperty(value = "差额")
    private BigDecimal diffReceivableAmount;

    /**
     * 其他金额类型：0=奖励金额/1=Incentive奖励/2=保险金额/3=活动费用/4=其他收入
     */
    @ApiModelProperty(value = "其他金额类型")
    private String bonusTypeName;

    /**
     * 发票绑定金额
     */
    @ApiModelProperty(value = "发票绑定金额")
    private BigDecimal amount;


    @ApiModelProperty(value = "计划收款时间")
    private String receivablePlanDateStr;


    @ApiModelProperty(value = "到账时间")
    private String paymentDate;
    /**
     * 状态：0关闭/1打开/2完成
     */
    @ApiModelProperty(value = "状态")
    private String statusName;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;

    @ApiModelProperty("创建人")
    private String gmtCreateUser;

}
