package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2022/1/7
 * @TIME: 15:55
 * @Description:
 **/
@Data
public class BusinessChannelDto extends BaseVoEntity{
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "业务渠道Id")
    private Long fkBusinessChannelId;

    /**
     * 业务类型关键字，枚举：m_student_insurance留学保险/m_student_accommodation留学住宿
     */
    @ApiModelProperty(value = "业务类型关键字，枚举：m_student_insurance留学保险/m_student_accommodation留学住宿")
    private String fkTypeKey;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private String num;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 中文名称
     */
    @ApiModelProperty(value = "中文名称")
    private String nameChn;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

    private String keyWord;
}
