package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2021/4/12
 * @TIME: 15:14
 * @Description:
 **/
@Data
public class InstitutionChannelDto extends BaseVoEntity {
    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private String num;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 中文名称
     */
    @ApiModelProperty(value = "中文名称")
    private String nameChn;

    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;
}
