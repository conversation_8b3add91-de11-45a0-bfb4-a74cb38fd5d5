<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.AreaRegionStateMapper">
  <insert id="insert" parameterType="com.get.institutioncenter.entity.AreaRegionState">
    insert into r_area_region_state (id, fk_area_region_id, fk_area_state_id, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkAreaRegionId,jdbcType=BIGINT}, #{fkAreaStateId,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.AreaRegionState">
    insert into r_area_region_state
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkAreaRegionId != null">
        fk_area_region_id,
      </if>
      <if test="fkAreaStateId != null">
        fk_area_state_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkAreaRegionId != null">
        #{fkAreaRegionId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaStateId != null">
        #{fkAreaStateId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.get.institutioncenter.entity.AreaRegionState">
    update r_area_region_state
    <set>
      <if test="fkAreaRegionId != null">
        fk_area_region_id = #{fkAreaRegionId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaStateId != null">
        fk_area_state_id = #{fkAreaStateId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.institutioncenter.entity.AreaRegionState">
    update r_area_region_state
    set fk_area_region_id = #{fkAreaRegionId,jdbcType=BIGINT},
      fk_area_state_id = #{fkAreaStateId,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateFkAreaRegionIdByAreaStateId">
  update r_area_region_state
  set fk_area_region_id = #{areaRegionState.fkAreaRegionId,jdbcType=BIGINT},
  gmt_create = #{areaRegionState.gmtCreate,jdbcType=TIMESTAMP},
  gmt_create_user = #{areaRegionState.gmtCreateUser,jdbcType=VARCHAR},
  gmt_modified = #{areaRegionState.gmtModified,jdbcType=TIMESTAMP},
  gmt_modified_user = #{areaRegionState.gmtModifiedUser,jdbcType=VARCHAR}
  where fk_area_state_id = #{areaRegionState.fkAreaStateId,jdbcType=BIGINT}
</update>

  <select id="getAreaRegionDtoByAreaStateId" resultType="com.get.institutioncenter.vo.AreaRegionVo">
    SELECT
      rars.fk_area_state_id AS fkAreaStateId,
      uar.id AS id,
      uar.fk_area_country_id AS fkAreaCountryId,
      uar.num AS num,
      uar.`name` AS name,
      uar.name_chn AS nameChn,
      uar.remark AS remark,
      uar.view_order AS viewOrder,
      uar.gmt_create AS gmtCreate,
      uar.gmt_create_user AS gmtCreateUser,
      uar.gmt_modified AS gmtModified,
      uar.gmt_modified_user AS gmtModifiedUser
    FROM
      u_area_region uar join r_area_region_state rars on uar.id = rars.fk_area_region_id
    where rars.fk_area_state_id in
    <foreach collection="fkAreaStateIds" item="fkAreaStateId" index="index" open="(" separator="," close=")">
      #{fkAreaStateId}
    </foreach>
  </select>

  <delete id="deleteByAreaStateId">
    delete from r_area_region_state
    where fk_area_state_id = #{fkAreaStateId}
  </delete>
</mapper>