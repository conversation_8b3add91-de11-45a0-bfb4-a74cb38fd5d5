package com.get.salecenter.utils;

import com.get.common.eunms.ProjectKeyEnum;
import com.get.core.tool.utils.GeneralTool;
import com.google.common.collect.Lists;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;

import javax.servlet.http.HttpServletRequest;
import java.beans.PropertyDescriptor;
import java.io.BufferedReader;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author: Sea
 * @create: 2020/8/19 12:56
 * @verison: 1.0
 * @description:
 */
@Slf4j
public class MyStringUtils {

    /**
     * 系统房号
     *
     * @param preNum
     * @param maxSystemRoomNum
     * @return
     */
    public static String getSystemRoomNum(String preNum, Long maxSystemRoomNum) {
        String code = String.valueOf(maxSystemRoomNum);
        if (String.valueOf(maxSystemRoomNum).length() < 4) {
            code = String.format("%03d", maxSystemRoomNum);
        }
        return preNum + code;
    }

    /**
     * 桌台编号
     *
     * @param preNum
     * @param maxTableNum
     * @return
     */
    public static String getmaxTableNum(String preNum, Long maxTableNum) {
        String code = String.valueOf(maxTableNum);
        if (String.valueOf(maxTableNum).length() < 4) {
            code = String.format("%03d", maxTableNum);
        }
        return preNum + code;
    }

    /**
     * 合同编号
     *
     * @return
     */
    public static String getContractNum() {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        String format = formatter.format(calendar.getTime());
        return "AGC" + format;
    }
    /**
     * 设定合同编号生成规则
     *
     * @return
     */
    public static String getSpecialContractNum(String contractPrefix,String contractNum,int countContractNum) {
        // 如果没有记录，从 000001 开始
        //如果contractNum为000001，则加一为000002
        int newSequence;
        if (!"".equals(contractNum)) {
            // 将 contractNum 转换为整数并加 1
            newSequence = Integer.parseInt(contractNum) + 1;
        } else {
            // 如果 contractNum 为空，从 1 开始
            newSequence = countContractNum +1;
        }
        String formattedSequence = String.format("%06d", newSequence);
        // 返回合同编号
        return contractPrefix + formattedSequence;
    }



    /**
     * 学生代理编号
     *
     * @param num
     * @return
     */
    public static String getAgentNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 7) {
            code = String.format("%06d", num);
        }
        return "AG" + code;
    }

    /**
     * @Description：获取代理邀请码
     * @Param
     * @Date 16:52 2021/4/30
     * <AUTHOR>
     */
    public static String getAgentInvitationCode() {
        int length = 8;
        StringBuilder str = new StringBuilder();
        Random random = new Random();

        //参数length，表示生成几位随机数
        for (int i = 0; i < length; i++) {

            String charOrNum = random.nextInt(2) % 2 == 0 ? "char" : "num";
            //输出字母还是数字
            if ("char".equalsIgnoreCase(charOrNum)) {
                //输出是大写字母还是小写字母
                int temp = random.nextInt(2) % 2 == 0 ? 65 : 97;
                str.append((char) (random.nextInt(26) + temp));
            } else {
                str.append(random.nextInt(10));
            }
        }
        return str.toString().toUpperCase();
    }


    /**
     * 学生编号
     *
     * @param num
     * @return
     */
    public static String getStudentOfferNum(Long num) {
        String code = String.valueOf(num);
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        String year = formatter.format(calendar.getTime());

        if (String.valueOf(num).length() < 9) {
            code = String.format("%08d", num);
        }
        return "OFF" + year + code;
    }
    public static String getStudentServiceFeeNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 9) {
            code = String.format("%08d", num);
        }
        return "SVF" + code;
    }


    public static String getClientOfferNum(Long num) {
        String code = String.valueOf(num);
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        String year = formatter.format(calendar.getTime());

        if (String.valueOf(num).length() < 9) {
            code = String.format("%08d", num);
        }
        return "CS" + year + code;
    }


    /**
     * @return java.lang.String
     * @Description :活动编号
     * @Param [num]
     * <AUTHOR>
     */
    public static String getEventNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 7) {
            code = String.format("%06d", num);
        }
        return "EVT" + code;
    }

    /**
     * @return java.lang.String
     * @Description :渠道编号
     * @Param [num]
     * <AUTHOR>
     */
    public static String getBusinessChannelNum(String type, Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 7) {
            code = String.format("%06d", num);
        }
        if (type.equals(ProjectKeyEnum.M_STUDENT_INSURANCE.key)) {
            return "BCINS" + code;
        } else {
            return "BCACC" + code;
        }
    }

    /**
     * @return java.lang.String
     * @Description :住宿编号
     * @Param [num]
     * <AUTHOR>
     */
    public static String getAccommodationNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 7) {
            code = String.format("%06d", num);
        }
        return "ACC" + code;
    }

    /**
     * @return java.lang.String
     * @Description :保险编号
     * @Param [num]
     * <AUTHOR>
     */
    public static String getInsuranceNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 7) {
            code = String.format("%06d", num);
        }
        return "INS" + code;
    }

    /**
     * @return java.lang.String
     * @Description :奖励策略编号：ICP+6位ID数字，例：ICP000001
     * @Param [num]
     */
    public static String getIncentivePolicyNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 7) {
            code = String.format("%06d", num);
        }
        return "ICP" + code;
    }

    /**
     * @return java.lang.String
     * @Description :渠道编号
     * @Param [num]
     * <AUTHOR>
     */
    public static String getBusinessProviderNum(String type, Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 7) {
            code = String.format("%06d", num);
        }
        if (type.equals(ProjectKeyEnum.M_STUDENT_INSURANCE.key)) {
            return "BPINS" + code;
        } else {
            return "BPACC" + code;
        }
    }

    /**
     * @return java.lang.String
     * @Description :奖券编号
     * @Param [num]
     * <AUTHOR>
     */
    public static String getAwardCodeNum(int num, int digit, String preamble) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < digit + 1) {
            code = String.format("%0" + digit + "d", num);
        }
        if (GeneralTool.isNotEmpty(preamble)) {
            return preamble + code;
        } else {
            return code;
        }
    }

    /**
     * @return java.lang.String
     * @Description: 随机生成8位数
     * @Param []
     * <AUTHOR>
     */
    public static String getRandomCode() {
        StringBuilder str = new StringBuilder();
        Random random = new Random();
        char[] arr = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'};
        //随机生成数字，并添加到字符串
        for (int i = 0; i < 8; i++) {
            int index = random.nextInt(arr.length);
            str.append(arr[index]);
        }
        return str.toString();
    }

    /**
     * @return
     * @Description: 参会人员编号仅预订酒店生成
     * @Param
     * <AUTHOR>
     */
    public static String getConventionPersonNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 7) {
            code = String.format("%06d", num);
        }
        return "A" + code;
    }

    /**
     * @return
     * @Description: 参会人员编号仅预订酒店生成
     * @Param
     * <AUTHOR>
     */
    public static String getConventionBoothNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 9) {
            code = String.format("%08d", num);
        }
        return "R" + code;
    }


    /**
     * 提取中括号中内容，忽略中括号中的中括号
     *
     * @param msg
     * @return
     */
    public static List<String> extractMessage(String msg) {

        List<String> list = new ArrayList<String>();
        int start = 0;
        int startFlag = 0;
        int endFlag = 0;
        for (int i = 0; i < msg.length(); i++) {
            if (msg.charAt(i) == '[') {
                startFlag++;
                if (startFlag == endFlag + 1) {
                    start = i;
                }
            } else if (msg.charAt(i) == ']') {
                endFlag++;
                if (endFlag == startFlag) {
                    list.add(msg.substring(start + 1, i));
                }
            }
        }
        return list;
    }

    /**
     * 提取中括号中内容，忽略中括号中的中括号
     *
     * @param msg
     * @return
     */
    public static List<String> extractMessageByChar(String msg, char ch1, char ch2) {

        List<String> list = new ArrayList<String>();
        int start = 0;
        int startFlag = 0;
        int endFlag = 0;
        for (int i = 0; i < msg.length(); i++) {
            if (msg.charAt(i) == ch1) {
                startFlag++;
                if (startFlag == endFlag + 1) {
                    start = i;
                }
            } else if (msg.charAt(i) == ch2) {
                endFlag++;
                if (endFlag == startFlag) {
                    list.add(msg.substring(start + 1, i));
                }
            }
        }
        return list;
    }


    public static String getRequestPayload(HttpServletRequest req) {
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = req.getReader();) {
            char[] buff = new char[1024];
            int len;
            while ((len = reader.read(buff)) != -1) {
                sb.append(buff, 0, len);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return sb.toString();
    }


    public static String getReminderTemplate(Map<String, String> map, String htmlText) {
        if (GeneralTool.isNotEmpty(map)) {
            for (String s : map.keySet()) {
                if (GeneralTool.isNotEmpty(map.get(s))) {
                    htmlText = htmlText.replace("${" + s + "}", map.get(s));
                } else {
                    htmlText = htmlText.replace("${" + s + "}", "");
                }
            }
        }
        return htmlText;
    }


    /**
     * @return
     * @Description: 参会人员编号仅预订酒店生成
     * @Param
     * <AUTHOR>
     */
    public static String getEventBillInvoiceNum(Long num,String companyName) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 7) {
            code = String.format("%06d", num);
        }

        Date date = new Date();
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-M");
        String format = sf.format(date);
        String str = "";
        if (GeneralTool.isNotEmpty(companyName)){
            str = companyName+"-"+"EVT"+code+"-"+format;
        }else {
            str = "EVT"+code+"-"+format;
        }
        return str;
    }

    /**
     * @return
     * @Description: 参会人员编号仅预订酒店生成
     * @Param
     * <AUTHOR>
     */
    public static String getGenerateBillInvoiceNum(Integer order,String companyName) {
        String code = String.valueOf(order);
        if (String.valueOf(order).length() <= 2) {
            code = String.format("%03d", order);
        }

        Date date = new Date();
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMM");
        String format = sf.format(date);
        String str = "";
        if (GeneralTool.isNotEmpty(companyName)){
            str = companyName+"-"+"MKT"+format+code;
        }else {
            str = "MKT"+format+code;
        }
        return str;
    }

    /**
     * @return java.lang.String
     * @Description :活动编号
     * @Param [num]
     * <AUTHOR>
     */
    public static String getNumCommon(String prefix,Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 7) {
            code = String.format("%06d", num);
        }
        return prefix + code;
    }

    /**
     *
     * @param prefix 前缀
     * @param num
     * @param codeLength
     * @return
     */
    public static String getNumCommon(String prefix,Long num,Integer codeLength) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < codeLength+1) {
            code = String.format("%0"+codeLength+"d", num);
        }
        return prefix + code;
    }


    /**
     * 判断属性值是否全部为空
     * @param object
     * @return
     */
    public static boolean isAllFieldNull(Object object) {
        boolean flag = true;
        Class clazz = object.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if(field.getName().equals("serialVersionUID")) continue; //忽略 serialVersionUID，类中 serialVersionUID = 1L;
            if(isAllUpperCase(field.getName())) continue; //忽略类中类似 USER_ID大写定义的属性 public static final String USER_ID = "user_id";
            //设置属性是可以访问的(私有的也可以)
            field.setAccessible(true);
            Object value = null;
            try {
                value = field.get(object);
                // 只要有1个属性不为空,那么就不是所有的属性值都为空
                if (GeneralTool.isNotEmpty(value)) {
                    flag = false;
                    break;
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        //继承的父类中属性值也要判断
        Field[] superFields = clazz.getSuperclass().getDeclaredFields();
        for (Field superField : superFields) {
            if(superField.getName().equals("serialVersionUID")) continue;
            if(isAllUpperCase(superField.getName())) continue;
            //设置属性是可以访问的(私有的也可以)
            superField.setAccessible(true);
            Object value = null;
            try {
                value = superField.get(object);
                // 只要有1个属性不为空,那么就不是所有的属性值都为空
                if (GeneralTool.isNotEmpty(value)) {
                    flag = false;
                    break;
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return flag;
    }

    public static boolean isAllUpperCase(String fieldName){
        boolean isUpper = true;
        fieldName = fieldName.replaceAll("[^(A-Za-z)]", "");
        for (int i = 0; i < fieldName.length(); i++) {
            char charAt = fieldName.substring(0, fieldName.length()).charAt(i);
            if(!Character.isUpperCase(charAt)){
                isUpper = false;
                break;
            }
        }
        return isUpper;
    }


    /**
     * 把转义的字符转回正常
     * @param target
     * @param <T>
     */
    public static <T>void UnescapeHtml(T target){
        Class clazz = target.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if(field.getName().equals("serialVersionUID")) {
                continue; //忽略 serialVersionUID，类中 serialVersionUID = 1L;
            }
            //设置属性是可以访问的(私有的也可以)
            field.setAccessible(true);
            Object value = null;
            try {
                value = field.get(target);
                if (String.class.equals(field.getType())){
                    // 属性不为空
                    if (GeneralTool.isNotEmpty(value)) {
                        PropertyDescriptor propertyDescriptor = new PropertyDescriptor(field.getName(),clazz);
                        //获取写方法 setXX
                        Method writeMethod = propertyDescriptor.getWriteMethod();
                        //调用写方法 setXX
                        writeMethod.invoke(target,StringEscapeUtils.unescapeHtml(value.toString()));
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        //继承的父类中属性值也要判断
        Field[] superFields = clazz.getSuperclass().getDeclaredFields();
        for (Field superField : superFields) {
            if(superField.getName().equals("serialVersionUID")){
                continue;
            }
            //设置属性是可以访问的(私有的也可以)
            superField.setAccessible(true);
            Object value = null;
            try {
                value = superField.get(target);
                // 只要有1个属性不为空,那么就不是所有的属性值都为空
                if (String.class.equals(superField.getType())){
                    // 属性不为空
                    if (GeneralTool.isNotEmpty(value)) {
                        PropertyDescriptor propertyDescriptor = new PropertyDescriptor(superField.getName(),clazz);
                        //获取写方法 setXX
                        Method writeMethod = propertyDescriptor.getWriteMethod();
                        //调用写方法 setXX
                        writeMethod.invoke(target,StringEscapeUtils.unescapeHtml(value.toString()));
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    public static String getStringBetween(String target,String start,String end){
        if (GeneralTool.isEmpty(target)||GeneralTool.isEmpty(start)||GeneralTool.isEmpty(end)){
            return "";
        }
        int startIndex = target.indexOf(start);
        int endIndex = target.indexOf(end);
        String result = (startIndex == -1 || endIndex == -1) ? "" : target.substring(startIndex + 1, endIndex);
        return result;
    }

    /**
     * 生成二维码
     *
     * @param content 二维码的内容
     * @return BitMatrix对象
     */
    public static BitMatrix createCode(String content) {
        //二维码的宽高
        int width = 200;
        int height = 200;

        //其他参数，如字符集编码
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        //容错级别为H
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        //白边的宽度，可取0~4
        hints.put(EncodeHintType.MARGIN, 1);

        BitMatrix bitMatrix = null;
        try {
            //生成矩阵，因为我的业务场景传来的是编码之后的URL，所以先解码
            bitMatrix = new MultiFormatWriter().encode(content,
                    BarcodeFormat.QR_CODE, width, height, hints);

            //bitMatrix = deleteWhite(bitMatrix);
        } catch (WriterException e) {
            e.printStackTrace();
        }

        return bitMatrix;
    }


    /**
     * 获取 "[","]"之间的内容
     * @param input
     * @return
     */
    public static List<String> getContentBetweenMiddleBrackets(String input){
        String patternString = "\\[(.*?)\\]";
        Pattern pattern = Pattern.compile(patternString);
        Matcher matcher = pattern.matcher(input);

        List<String> list = Lists.newArrayList();
        while (matcher.find()) {
            String content = matcher.group(1);
            list.add(content);
            System.out.println(content);
        }
        return list;
    }

}
