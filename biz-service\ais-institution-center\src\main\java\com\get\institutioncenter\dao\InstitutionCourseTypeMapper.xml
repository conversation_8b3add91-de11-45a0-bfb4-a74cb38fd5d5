<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionCourseTypeMapper">
  <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.InstitutionCourseType">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_institution_course_id" jdbcType="BIGINT" property="fkInstitutionCourseId" />
    <result column="fk_course_type_id" jdbcType="BIGINT" property="fkCourseTypeId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insert" parameterType="com.get.institutioncenter.entity.InstitutionCourseType">
    insert into r_institution_course_type (id, fk_institution_course_id, fk_course_type_id, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkInstitutionCourseId,jdbcType=BIGINT}, #{fkCourseTypeId,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionCourseType">
    insert into r_institution_course_type
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkInstitutionCourseId != null">
        fk_institution_course_id,
      </if>
      <if test="fkCourseTypeId != null">
        fk_course_type_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionCourseId != null">
        #{fkInstitutionCourseId,jdbcType=BIGINT},
      </if>
      <if test="fkCourseTypeId != null">
        #{fkCourseTypeId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getNamesByCourseId" resultType="java.lang.String">
    select  GROUP_CONCAT(DISTINCT itf.type_name) from r_institution_course_type cf LEFT JOIN u_course_type itf
       on cf.fk_course_type_id = itf.id where cf.fk_institution_course_id = #{id}
  </select>
  <select id="getFullNamesByCourseId" resultType="java.lang.String">
    select GROUP_CONCAT(DISTINCT CASE WHEN IFNULL(itf.type_name_chn, '') = '' THEN itf.type_name ELSE CONCAT(itf.type_name, '（', itf.type_name_chn, '）') END) from r_institution_course_type cf LEFT JOIN u_course_type itf
                                                                                             on cf.fk_course_type_id = itf.id where cf.fk_institution_course_id = #{id}
  </select>
  <select id="getNamesByCourseIds" resultType="com.get.institutioncenter.vo.CourseTypeVo">
    select  cf.fk_institution_course_id AS fkInstitutionCourseId,GROUP_CONCAT(DISTINCT itf.type_name) AS typeName
    from r_institution_course_type cf LEFT JOIN
    u_course_type itf
    on cf.fk_course_type_id = itf.id where cf.fk_institution_course_id in
    <foreach collection="fkInstitutionCourseIds" item="fkInstitutionCourseId" index="index" open="(" separator="," close=")">
      #{fkInstitutionCourseId}
    </foreach>
    group by cf.fk_institution_course_id
  </select>
  <select id="getCourseIdsByTypeId" resultType="java.lang.Long">
    select fk_institution_course_id from r_institution_course_type where fk_course_type_id = #{id}
  </select>
  <select id="deleteByByCourseId">
    delete from r_institution_course_type where fk_institution_course_id = #{id}
  </select>
  <select id="getTypeIdsByCourseId" resultType="java.lang.Long">
    select fk_course_type_id from r_institution_course_type where fk_institution_course_id = #{id}
  </select>
  <select id="getTypeIdStringByCourseId" resultType="java.lang.String">
    select GROUP_CONCAT(fk_course_type_id) from r_institution_course_type where fk_institution_course_id = #{id}
  </select>
  <select id="getTypeIdsByContractFormula" resultType="java.lang.Long">
    select ml.id from r_institution_course_type ml left join r_contract_formula_course_type cfml
    on ml.id = cfml.fk_course_type_id where cfml.fk_contract_formula_id = #{id}
  </select>
  <select id="isExistByCourseType" resultType="java.lang.Boolean">
    SELECT IFNULL(max(id),0) id from r_institution_course_type where fk_course_type_id=#{fkCourseTypeId}
  </select>

  <select id="isExistByCourseId" resultType="java.lang.Boolean">
    SELECT IFNULL(max(id),0) id from r_institution_course_type where fk_institution_course_id=#{courseId}
  </select>
</mapper>