package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.AgentContractFormulaMajorLevel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/1/6 12:21
 * @verison: 1.0
 * @description:
 */
@Mapper
public interface AgentContractFormulaMajorLevelMapper extends BaseMapper<AgentContractFormulaMajorLevel> {
    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(AgentContractFormulaMajorLevel record);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :查找agentContractFormulaId对应课程等级ids
     * @Param [agentContractFormulaId]
     * <AUTHOR>
     */
    List<Long> getMajorLevelIdsByFkid(@Param("agentContractFormulaId") Long agentContractFormulaId);
}