package com.get.insurancecenter.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/5/23
 * @Version 1.0
 * @apiNote:
 */
@Data
public class CreditCardOrderListDto {

    @ApiModelProperty(value = "列表类型-1：支出列表2:退款列表")
    private Integer type;

    @ApiModelProperty(value = "保险公司Id")
    private Long insuranceCompanyId;

    @ApiModelProperty(value = "保险单类型")
    private String insuranceType;

    @ApiModelProperty(value = "保险单号")
    private String insuranceNum;

    @ApiModelProperty(value = "系统订单号")
    private String orderNum;

    @ApiModelProperty(value = "姓名")
    private String insurantName;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "信用卡ID")
    @NotNull(message = "信用卡ID不能为空")
    private Long creditCardId;
}
