package com.get.salecenter.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.*;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.entity.StaffCommissionAction;
import com.get.salecenter.dto.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface StaffCommissionActionMapper extends GetMapper<StaffCommissionAction> {


    /**
     * 匹配规则
     * @param staffCommissionPolicyDto
     * @return
     */
    List<StaffCommissionConfirmVo> getSuitablePolicies(@Param("staffCommissionPolicyDto") StaffCommissionPolicyDto staffCommissionPolicyDto);

    /**
     * 根据学生和提成步骤获取申请计划
     * @param fkStudentId
     * @param fkStaffCommissionStepKeyId
     * @return
     */
    List<StudentOfferItemCommissionVo> getStudentOfferItemsByCommissionStepAndStudentKey(@Param("fkStudentId") Long fkStudentId, @Param("fkStaffCommissionStepKeyId") Long fkStaffCommissionStepKeyId);

    /**
     * 查询结算员工
     * @param roleKeys
     * @param itemIds
     * @return
     */
    List<StaffCommissionActionVo> getSettlementStaffByRoleKeysAndItemIds(@Param("roleKeys") Set<String> roleKeys, @Param("itemIds") Set<Long> itemIds);

    /**
     * 汇总列表非动态部分信息
     * @param staffCommissionSummaryDto
     * @return
     */
    List<StaffCommissionSummaryVo> getStaffCommissionSummary(@Param("staffCommissionSummaryDto") StaffCommissionSummaryDto staffCommissionSummaryDto,
                                                             @Param("staffFollowerIds")List<Long> staffFollowerIds,
                                                             @Param("fkAreaCountryIds")List<Long> fkAreaCountryIds);

    /**
     * 获取课程大类和结算状态
     * @param offerItemIds
     * @return
     */
    List<StaffCommissionConfirmVo> getCourseTypeGroupAndSettlementStatus(@Param("offerItemIds")List<Long> offerItemIds);

    /**
     * 汇总列表动态部分信息
     * @param staffCommissionSummaryDto
     * @return
     */
    List<StaffCommissionSummaryVo> getStaffCommissionDynamicSummary(@Param("staffCommissionSummaryDto") StaffCommissionSummaryDto staffCommissionSummaryDto,
                                                                    @Param("commissionStepKeys")List<String> commissionStepKeys,
                                                                    @Param("staffFollowerIds")List<Long> staffFollowerIds,
                                                                    @Param("fkAreaCountryIds")List<Long> fkAreaCountryIds,
                                                                    @Param("studentIdList")List<Long> studentIdList);

    /**
     * 提成申请列表
     * @param commissionStaffDatasDto
     * @return
     */
    List<CommissionStaffDatasItemVo> getCommissionStaffDatas(@Param("commissionStaffDatasDto") CommissionStaffDatasDto commissionStaffDatasDto);

    /**
     * 代理下拉
     * @param companyId
     * @return
     */
    List<Agent> getStaffCommissionAgentList(@Param("companyId") Long companyId);

    /**
     * 查找规则匹配状态
     * @param staffCommissionSummaryDto
     * @param studentIds
     * @return
     */
    List<StaffCommissionSummaryVo> getStaffCommissionDynamicSummaryPolicyStatus(@Param("staffCommissionSummaryDto") StaffCommissionSummaryDto staffCommissionSummaryDto,
                                                                                @Param("studentIds")Set<Long> studentIds);


    /**
     * 员工统计固定的数据部分
     * @param staffSettlementStatisticsDto
     * @return
     */
    List<StaffSettlementStatisticsItemVo> getStaffSettlementStatisticsFixedDatas(@Param("staffSettlementStatisticsDto") StaffSettlementStatisticsDto staffSettlementStatisticsDto);

    /**
     * 员工统计动态的数据部分
     * @param staffSettlementStatisticsDto
     * @return
     */
    List<StaffCommissionActionVo> getStaffSettlementStatisticsTotalDynamicDatas(@Param("staffSettlementStatisticsDto") StaffSettlementStatisticsDto staffSettlementStatisticsDto);

    /**
     * 获取接收总计
     * @param staffSettlementStatisticsDto
     * @return
     */
    List<StaffCommissionActionVo> getSumSettlementAmounts(@Param("staffSettlementStatisticsDto") StaffSettlementStatisticsDto staffSettlementStatisticsDto);

    /**
     * 获取部门id
     * @param companyId
     * @return
     */
    List<String> getStaffCommissionStaffIds(@Param("companyId") Long companyId);

    @DS("saledb-doris")
    List<StaffCommissionRefundVo> getStaffSettlementRefundList(IPage<StaffCommissionRefundVo> pages,
                                                               @Param("staffCommissionRefundDto") StaffCommissionRefundDto staffCommissionRefundDto,
                                                               @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                               @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                               @Param("areaCountryIds") List<Long> areaCountryIds,
                                                               @Param("isBd") Boolean isBd,
                                                               @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                               @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    List<BaseSelectEntity> getSettlementDateSelect(@Param("companyId")Long companyId);

    @DS("saledb-doris")
    List<StaffCommissionRefundDetailVo> getStaffSettlementRefundSummaryList(@Param("staffCommissionRefundDto") StaffCommissionRefundDto staffCommissionRefundDto,
                                                                            @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                                            @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                                            @Param("areaCountryIds") List<Long> areaCountryIds,
                                                                            @Param("isBd") Boolean isBd,
                                                                            @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                                            @Param("staffBoundBdIds") List<Long> staffBoundBdIds);
}