package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.institutioncenter.entity.MajorLevel;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/31 12:12
 * @verison: 1.0
 * @description: 专业等级管理mapper
 */
@Mapper
public interface MajorLevelMapper extends BaseMapper<MajorLevel> {
    /**
     * 添加
     *
     * @param record
     * @return
     */
    @Override
    int insert(MajorLevel record);

    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(MajorLevel record);

    /**
     * 根据等级id查找等级名称
     *
     * @param id
     * @return
     */
    String getMajorLevelNameById(Long id);

    /**
     * @return java.lang.Integer
     * @Description :获取最大排序值
     * @Param []
     * <AUTHOR>
     */
    Integer getMaxViewOrder();

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description :专业等级下拉框
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getMajorLevelSelect();


    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description :专业等级下拉框带组别
     * @Param []
     * <AUTHOR>
     */
    List<MajorLevel> getMajorLevelAndGroupSelect();
    /**
     * 根据公式id查找等级id
     *
     * @param id
     * @return
     */
    List<Long> getMajorLevelByContractFormula(Long id);

}