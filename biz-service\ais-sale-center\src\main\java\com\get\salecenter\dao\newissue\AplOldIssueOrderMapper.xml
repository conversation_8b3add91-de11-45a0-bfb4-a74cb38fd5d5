<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.newissue.AplOldIssueOrderMapper">
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.AplOldIssueOrder">
        <id column="order_id" jdbcType="INTEGER" property="orderId"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="stu_id" jdbcType="INTEGER" property="stuId"/>
        <result column="cdet_id" jdbcType="INTEGER" property="cdetId"/>
        <result column="stu_source" jdbcType="VARCHAR" property="stuSource"/>
        <result column="stu_surname" jdbcType="VARCHAR" property="stuSurname"/>
        <result column="stu_first_name" jdbcType="VARCHAR" property="stuFirstName"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="order_createtime" jdbcType="TIMESTAMP" property="orderCreatetime"/>
        <result column="cdet_schl_ctry" jdbcType="VARCHAR" property="cdetSchlCtry"/>
        <result column="cdet_schl_id" jdbcType="INTEGER" property="cdetSchlId"/>
        <result column="cdet_crse_name" jdbcType="VARCHAR" property="cdetCrseName"/>
        <result column="cdet_crse_code" jdbcType="VARCHAR" property="cdetCrseCode"/>
        <result column="cdet_star_date" jdbcType="DATE" property="cdetStarDate"/>
        <result column="cdet_sch_login_name" jdbcType="VARCHAR" property="cdetSchLoginName"/>
        <result column="cdet_sch_login_pw" jdbcType="VARCHAR" property="cdetSchLoginPw"/>
        <result column="order_status" jdbcType="VARCHAR" property="orderStatus"/>
        <result column="robot_status" jdbcType="VARCHAR" property="robotStatus"/>
        <result column="robot_status_time" jdbcType="TIMESTAMP" property="robotStatusTime"/>
        <result column="error_bp_no" jdbcType="INTEGER" property="errorBpNo"/>
        <result column="creater_id" jdbcType="INTEGER" property="createrId"/>
        <result column="people_msg" jdbcType="VARCHAR" property="peopleMsg"/>
        <result column="order_create_msg" jdbcType="LONGVARCHAR" property="orderCreateMsg"/>
        <result column="robot_return_msg" jdbcType="LONGVARCHAR" property="robotReturnMsg"/>
        <result column="Run_Duration" jdbcType="LONGVARCHAR" property="runDuration"/>
    </resultMap>

    <insert id="insertSelective" keyProperty="orderId" useGeneratedKeys="true"
            parameterType="com.get.salecenter.entity.AplOldIssueOrder">
        insert into apl_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                order_id,
            </if>
            <if test="createName != null">
                create_name,
            </if>
            <if test="stuId != null">
                stu_id,
            </if>
            <if test="cdetId != null">
                cdet_id,
            </if>
            <if test="stuSource != null">
                stu_source,
            </if>
            <if test="stuSurname != null">
                stu_surname,
            </if>
            <if test="stuFirstName != null">
                stu_first_name,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="cdetSchlCtry != null">
                cdet_schl_ctry,
            </if>
            <if test="cdetSchlId != null">
                cdet_schl_id,
            </if>
            <if test="cdetCrseName != null">
                cdet_crse_name,
            </if>
            <if test="cdetCrseCode != null">
                cdet_crse_code,
            </if>
            <if test="cdetSchLoginName != null">
                cdet_sch_login_name,
            </if>
            <if test="cdetSchLoginPw != null">
                cdet_sch_login_pw,
            </if>
            <if test="orderCreatetime != null">
                order_createtime,
            </if>
            <if test="orderStatus != null">
                order_status,
            </if>
            <if test="robotStatus != null">
                robot_status,
            </if>
            <if test="robotStatusTime != null">
                robot_status_time,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="peopleMsg != null">
                people_msg,
            </if>
            <if test="orderCreateMsg != null">
                order_create_msg,
            </if>
            <if test="robotReturnMsg != null">
                robot_return_msg,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                #{orderId,jdbcType=INTEGER},
            </if>
            <if test="createName != null">
                #{createName,jdbcType=VARCHAR},
            </if>
            <if test="stuId != null">
                #{stuId,jdbcType=INTEGER},
            </if>
            <if test="cdetId != null">
                #{cdetId,jdbcType=INTEGER},
            </if>
            <if test="stuSource != null">
                #{stuSource,jdbcType=VARCHAR},
            </if>
            <if test="stuSurname != null">
                #{stuSurname,jdbcType=VARCHAR},
            </if>
            <if test="stuFirstName != null">
                #{stuFirstName,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="cdetSchlCtry != null">
                #{cdetSchlCtry,jdbcType=VARCHAR},
            </if>
            <if test="cdetCrseName != null">
                #{cdetCrseName,jdbcType=VARCHAR},
            </if>
            <if test="cdetCrseCode != null">
                #{cdetCrseCode,jdbcType=VARCHAR},
            </if>
            <if test="cdetSchLoginName != null">
                #{cdetSchLoginName,jdbcType=VARCHAR},
            </if>
            <if test="cdetSchLoginPw != null">
                #{cdetSchLoginPw,jdbcType=VARCHAR},
            </if>
            <if test="orderCreatetime != null">
                #{orderCreatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="orderStatus != null">
                #{orderStatus,jdbcType=VARCHAR},
            </if>
            <if test="robotStatus != null">
                #{robotStatus,jdbcType=VARCHAR},
            </if>
            <if test="robotStatusTime != null">
                #{robotStatusTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=INTEGER},
            </if>
            <if test="peopleMsg != null">
                #{peopleMsg,jdbcType=VARCHAR},
            </if>
            <if test="orderCreateMsg != null">
                #{orderCreateMsg,jdbcType=LONGVARCHAR},
            </if>
            <if test="robotReturnMsg != null">
                #{robotReturnMsg,jdbcType=LONGVARCHAR},
            </if>
            <if test="runDuration != null">
                #{runDuration,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>

    <select id="getListByOrderIds" resultType="com.get.salecenter.entity.AplOldIssueOrder">
        select order_id, email,case robot_status when 'T' then 'N' when 'Q' then'N'  ELSE robot_status end as robot_status,cdet_sch_login_name,cdet_sch_login_pw,robot_return_msg,people_msg from
        apl_order
        where
        order_id in
        <foreach collection="orderIds" item="id" index="index" open="("
                 separator="," close=")">
            #{id}
        </foreach>
    </select>

</mapper>