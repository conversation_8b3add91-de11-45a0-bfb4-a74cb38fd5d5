package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.ClientOfferMapper;
import com.get.salecenter.dao.sale.ClientOfferStepMapper;
import com.get.salecenter.vo.ClientOfferStepVo;
import com.get.salecenter.entity.ClientOfferStep;
import com.get.salecenter.service.IClientOfferStepService;
import com.get.salecenter.dto.ClientOfferStepAddDto;
import com.get.salecenter.dto.ClientOfferStepListDto;
import com.get.salecenter.dto.ClientOfferStepDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/12/13 10:37
 * @verison: 1.0
 * @description:
 */
@Service
public class ClientOfferStepServiceImpl implements IClientOfferStepService {

    @Resource
    private ClientOfferStepMapper clientOfferStepMapper;

    @Resource
    private UtilService utilService;

    @Resource
    private ClientOfferMapper clientOfferMapper;



    /**
     * 新增
     * @param clientOfferStepAddDto
     * @return
     */
    @Override
    public Long addClientOfferStep(ClientOfferStepAddDto clientOfferStepAddDto) {
        if (GeneralTool.isEmpty(clientOfferStepAddDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        Integer maxStepOrder = clientOfferStepMapper.getMaxStepOrder();
        ClientOfferStep clientOfferStep = BeanCopyUtils.objClone(clientOfferStepAddDto, ClientOfferStep::new);
        if (validateAdd(clientOfferStepAddDto)) {
            clientOfferStep.setStepOrder(maxStepOrder);
            utilService.updateUserInfoToEntity(clientOfferStep);
            clientOfferStepMapper.insert(clientOfferStep);
            maxStepOrder++;
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }

        return clientOfferStep.getId();
    }

    /**
     * 删除
     * @param id
     */
    @Override
    public void delete(Long id) {
        if (clientOfferMapper.isExistByStepId(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("step_offerItem_data_association"));
        }
        clientOfferStepMapper.deleteById(id);
    }

    /**
     * 详情
     * @param id
     * @return
     */
    @Override
    public ClientOfferStepVo findClientOfferStepById(Long id) {
        ClientOfferStep clientOfferStep = clientOfferStepMapper.selectById(id);
        return BeanCopyUtils.objClone(clientOfferStep, ClientOfferStepVo::new);
    }

    /**
     * 列表
     * @param clientOfferStepListDto
     * @param page
     * @return
     */
    @Override
    public List<ClientOfferStepVo> getClientOfferSteps(ClientOfferStepListDto clientOfferStepListDto, Page page) {
        LambdaQueryWrapper<ClientOfferStep> wrapper = Wrappers.lambdaQuery(ClientOfferStep.class);
        if (GeneralTool.isNotEmpty(clientOfferStepListDto.getStepName())){
            wrapper.like(ClientOfferStep::getStepName, clientOfferStepListDto.getStepName());
        }
        wrapper.orderByDesc(ClientOfferStep::getStepOrder);
        IPage<ClientOfferStep> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<ClientOfferStep> clientOfferSteps = clientOfferStepMapper.selectPage(iPage, wrapper).getRecords();
        page.setAll((int) iPage.getTotal());
        if (GeneralTool.isEmpty(clientOfferSteps)){
            return Collections.emptyList();
        }
        return BeanCopyUtils.copyListProperties(clientOfferSteps, ClientOfferStepVo::new);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void movingOrder(List<ClientOfferStepDto> clientOfferStepDtos) {
        if (GeneralTool.isEmpty(clientOfferStepDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        ClientOfferStep ro = BeanCopyUtils.objClone(clientOfferStepDtos.get(0), ClientOfferStep::new);
        Integer oneorder = ro.getStepOrder();
        ClientOfferStep rt = BeanCopyUtils.objClone(clientOfferStepDtos.get(1), ClientOfferStep::new);
        Integer twoorder = rt.getStepOrder();
        ro.setStepOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setStepOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        clientOfferStepMapper.updateById(ro);
        clientOfferStepMapper.updateById(rt);
    }

    @Override
    public ClientOfferStepVo updateClientOfferStep(ClientOfferStepAddDto clientOfferStepAddDto) {
        ClientOfferStep clientOfferStep = BeanCopyUtils.objClone(clientOfferStepAddDto, ClientOfferStep::new);
        utilService.setUpdateInfo(clientOfferStep);
        clientOfferStepMapper.updateById(clientOfferStep);
        return findClientOfferStepById(clientOfferStepAddDto.getId());
    }

    @Override
    public List<ClientOfferStepVo> getClientOfferStepSelect() {
        List<ClientOfferStep> clientOfferSteps = clientOfferStepMapper.selectList(Wrappers.lambdaQuery(ClientOfferStep.class)
                .orderByAsc(ClientOfferStep::getStepOrder));
        if (GeneralTool.isEmpty(clientOfferSteps)){
            return Collections.emptyList();
        }
        return BeanCopyUtils.copyListProperties(clientOfferSteps, ClientOfferStepVo::new);
    }


    private boolean validateAdd(ClientOfferStepAddDto clientOfferStepAddDto) {
        List<ClientOfferStep> list = this.clientOfferStepMapper.selectList(Wrappers.<ClientOfferStep>lambdaQuery().eq(ClientOfferStep::getStepKey, clientOfferStepAddDto.getStepKey()));
        return GeneralTool.isEmpty(list);
    }
}
