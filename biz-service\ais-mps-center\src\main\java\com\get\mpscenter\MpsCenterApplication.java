package com.get.mpscenter;

import com.get.common.constant.AppCenterConstant;
import com.get.core.cloud.feign.EnableGetFeign;
import com.get.core.start.GetApplication;
import org.springframework.cloud.client.SpringCloudApplication;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableGetFeign
@SpringCloudApplication
@EnableAsync
public class MpsCenterApplication {
    public static void main(String[] args) {
        GetApplication.run(AppCenterConstant.APPLICATION_MPS_CENTER, MpsCenterApplication.class, args);
    }

}