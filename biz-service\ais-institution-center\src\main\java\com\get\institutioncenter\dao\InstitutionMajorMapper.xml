<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionMajorMapper">
  <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.InstitutionMajor">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_institution_id" jdbcType="BIGINT" property="fkInstitutionId" />
    <result column="fk_institution_faculty_id" jdbcType="BIGINT" property="fkInstitutionFacultyId" />
    <result column="fk_major_level_id" jdbcType="BIGINT" property="fkMajorLevelId" />
    <result column="num" jdbcType="VARCHAR" property="num" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="name_chn" jdbcType="VARCHAR" property="nameChn" />
    <result column="ranking" jdbcType="VARCHAR" property="ranking" />
    <result column="fee" jdbcType="VARCHAR" property="fee" />
    <result column="duration" jdbcType="VARCHAR" property="duration" />
    <result column="core_course" jdbcType="VARCHAR" property="coreCourse" />
    <result column="entry_standards" jdbcType="VARCHAR" property="entryStandards" />
    <result column="occupation_development" jdbcType="VARCHAR" property="occupationDevelopment" />
    <result column="is_active" jdbcType="BIT" property="isActive" />
    <result column="view_order" jdbcType="INTEGER" property="viewOrder" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insert" parameterType="com.get.institutioncenter.entity.InstitutionMajor" keyProperty="id" useGeneratedKeys="true">
    insert into m_institution_major (id, fk_institution_id, fk_institution_faculty_id, 
      fk_major_level_id, num, name, 
      name_chn, ranking, fee, 
      duration, core_course, entry_standards, 
      occupation_development, is_active, view_order, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkInstitutionId,jdbcType=BIGINT}, #{fkInstitutionFacultyId,jdbcType=BIGINT}, 
      #{fkMajorLevelId,jdbcType=BIGINT}, #{num,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{nameChn,jdbcType=VARCHAR}, #{ranking,jdbcType=VARCHAR}, #{fee,jdbcType=VARCHAR}, 
      #{duration,jdbcType=VARCHAR}, #{coreCourse,jdbcType=VARCHAR}, #{entryStandards,jdbcType=VARCHAR}, 
      #{occupationDevelopment,jdbcType=VARCHAR}, #{isActive,jdbcType=BIT}, #{viewOrder,jdbcType=INTEGER}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionMajor" keyProperty="id" useGeneratedKeys="true">
    insert into m_institution_major
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkInstitutionId != null">
        fk_institution_id,
      </if>
      <if test="fkInstitutionFacultyId != null">
        fk_institution_faculty_id,
      </if>
      <if test="fkMajorLevelId != null">
        fk_major_level_id,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="nameChn != null">
        name_chn,
      </if>
      <if test="ranking != null">
        ranking,
      </if>
      <if test="fee != null">
        fee,
      </if>
      <if test="duration != null">
        duration,
      </if>
      <if test="coreCourse != null">
        core_course,
      </if>
      <if test="entryStandards != null">
        entry_standards,
      </if>
      <if test="occupationDevelopment != null">
        occupation_development,
      </if>
      <if test="isActive != null">
        is_active,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionId != null">
        #{fkInstitutionId,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionFacultyId != null">
        #{fkInstitutionFacultyId,jdbcType=BIGINT},
      </if>
      <if test="fkMajorLevelId != null">
        #{fkMajorLevelId,jdbcType=BIGINT},
      </if>
      <if test="num != null">
        #{num,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="nameChn != null">
        #{nameChn,jdbcType=VARCHAR},
      </if>
      <if test="ranking != null">
        #{ranking,jdbcType=VARCHAR},
      </if>
      <if test="fee != null">
        #{fee,jdbcType=VARCHAR},
      </if>
      <if test="duration != null">
        #{duration,jdbcType=VARCHAR},
      </if>
      <if test="coreCourse != null">
        #{coreCourse,jdbcType=VARCHAR},
      </if>
      <if test="entryStandards != null">
        #{entryStandards,jdbcType=VARCHAR},
      </if>
      <if test="occupationDevelopment != null">
        #{occupationDevelopment,jdbcType=VARCHAR},
      </if>
      <if test="isActive != null">
        #{isActive,jdbcType=BIT},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getInstitutionMajorNameById" parameterType="java.lang.Long" resultType="java.lang.String">
    select
      CASE
        WHEN IFNULL(level_name_chn, '') = '' THEN
            `level_name`
        ELSE
            CONCAT(
                `level_name`,
                '（',
                level_name_chn,
                '）'
            )
    END fullName
    from
      u_major_level i
    where
      i.id = #{id}
  </select>
  <select id="getMaxId" resultType="java.lang.Integer">
    select ifnull(max(id)+1,1) from m_institution_major
  </select>
    <select id="getInstitutionMajorSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
          id,
          level_name AS NAME,
          level_name_chn AS name_chn,
        CASE
        WHEN IFNULL(level_name_chn, '') = '' THEN
            `level_name`
        ELSE
            CONCAT(
                `level_name`,
                '（',
                level_name_chn,
                '）'
            )
    END fullName
    FROM
        u_major_level
      <if test="keyword!=null and keyword!=''">
        WHERE
        (`level_name` LIKE concat('%',#{keyword},'%') OR level_name_chn LIKE concat('%',#{keyword},'%'))
      </if>
    </select>
    <select id="getInstitutionMajorNamesByIds" resultType="com.get.salecenter.vo.SelItem">
      SELECT
          id AS keyId,
        CASE
        WHEN IFNULL(level_name_chn, '') = '' THEN
            `level_name`
        ELSE
            CONCAT(
                `level_name`,
                '（',
                level_name_chn,
                '）'
            )
    END val
    FROM
        u_major_level
      WHERE id IN
      <foreach collection="ids" open="(" close=")" separator="," item="uid">
        #{uid}
      </foreach>
    </select>

</mapper>