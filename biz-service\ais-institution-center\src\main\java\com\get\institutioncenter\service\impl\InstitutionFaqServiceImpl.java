package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.InstitutionFaqMapper;
import com.get.institutioncenter.dao.InstitutionMapper;
import com.get.institutioncenter.dto.InstitutionFaqDto;
import com.get.institutioncenter.entity.InstitutionFaq;
import com.get.institutioncenter.service.IInstitutionFaqService;
import com.get.institutioncenter.service.IInstitutionService;
import com.get.institutioncenter.service.ITranslationMappingService;
import com.get.institutioncenter.vo.InstitutionFaqVo;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/8/10
 * @TIME: 11:59
 * @Description:
 **/
@Service
public class InstitutionFaqServiceImpl extends BaseServiceImpl<InstitutionFaqMapper, InstitutionFaq> implements IInstitutionFaqService {
    @Resource
    private InstitutionFaqMapper institutionFaqMapper;
    @Resource
    private InstitutionMapper institutionMapper;
    @Resource
    private UtilService utilService;
    @Resource
    @Lazy
    private IInstitutionService institutionService;
    @Resource
    private ITranslationMappingService translationMappingService;

    @Override
    public List<InstitutionFaqVo> datas(InstitutionFaqDto institutionFaqDto, Page page) {
        LambdaQueryWrapper<InstitutionFaq> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(institutionFaqDto)) {
            if (GeneralTool.isNotEmpty(institutionFaqDto.getKeyWord())) {
                wrapper.like(InstitutionFaq::getQuestion, institutionFaqDto.getKeyWord());
            }
            if (GeneralTool.isNotEmpty(institutionFaqDto.getFkInstitutionId())) {
                wrapper.eq(InstitutionFaq::getFkInstitutionId, institutionFaqDto.getFkInstitutionId());
            }
        }
        wrapper.orderByDesc(InstitutionFaq::getViewOrder).orderByDesc(InstitutionFaq::getGmtCreate);
        //获取分页数据
        IPage<InstitutionFaq> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<InstitutionFaq> institutionFaqs = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<InstitutionFaqVo> convertDatas = new ArrayList<>();

        //学校ids
        Set<Long> institutionIds = institutionFaqs.stream().map(InstitutionFaq::getFkInstitutionId).collect(Collectors.toSet());
        //根据学校ids获取名称
        Map<Long, String> institutionNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(institutionIds)) {
            institutionNamesByIds = institutionService.getInstitutionNamesByIds(institutionIds);
        }

        for (InstitutionFaq institutionFaq : institutionFaqs) {
            InstitutionFaqVo institutionFaqVo = BeanCopyUtils.objClone(institutionFaq, InstitutionFaqVo::new);
            if (GeneralTool.isNotEmpty(institutionFaqVo.getFkInstitutionId())) {
                institutionFaqVo.setInstitutionName(institutionNamesByIds.get(institutionFaqVo.getFkInstitutionId()));
            }
            convertDatas.add(institutionFaqVo);
        }
        return convertDatas;
    }

    @Override
    public Long addInstitutionFaq(InstitutionFaqDto institutionFaqDto) {
        InstitutionFaq institutionFaq = BeanCopyUtils.objClone(institutionFaqDto, InstitutionFaq::new);
        utilService.updateUserInfoToEntity(institutionFaq);
        int i = institutionFaqMapper.insertSelective(institutionFaq);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return institutionFaq.getId();
    }

    @Override
    public InstitutionFaqVo findInstitutionFaqById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionFaq institutionFaq = institutionFaqMapper.selectById(id);
        if (GeneralTool.isEmpty(institutionFaq)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionFaqVo institutionFaqVo = BeanCopyUtils.objClone(institutionFaq, InstitutionFaqVo::new);
        if (GeneralTool.isNotEmpty(institutionFaqVo.getFkInstitutionId())) {
            institutionFaqVo.setInstitutionName(institutionMapper.getInstitutionNameById(institutionFaqVo.getFkInstitutionId()));
        }
        institutionFaqVo.setFkTableName(TableEnum.INSTITUTION_FAQ.key);
        return institutionFaqVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        //TODO 改过
        //InstitutionFaq institutionFaq = findInstitutionFaqById(id);
        InstitutionFaqVo institutionFaq = findInstitutionFaqById(id);
        if (institutionFaq == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        int i = institutionFaqMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        //删除翻译内容
        translationMappingService.deleteTranslations(TableEnum.INSTITUTION_FAQ.key, id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public InstitutionFaqVo updateInstitutionFaq(InstitutionFaqDto institutionFaqDto) {
        if (institutionFaqDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(institutionFaqDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionFaq rs = institutionFaqMapper.selectById(institutionFaqDto.getId());
        if (rs == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionFaq institutionFaq = BeanCopyUtils.objClone(institutionFaqDto, InstitutionFaq::new);
        utilService.updateUserInfoToEntity(institutionFaq);
        institutionFaqMapper.updateById(institutionFaq);
        return findInstitutionFaqById(institutionFaq.getId());
    }

}
