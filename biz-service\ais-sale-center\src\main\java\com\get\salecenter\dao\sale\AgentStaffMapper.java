package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.StaffBdCodeVo;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.entity.AgentStaff;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Mapper
public interface AgentStaffMapper extends BaseMapper<AgentStaff>, GetMapper<AgentStaff> {
    /**
     * @return int
     * @Description :添加
     * @Param [record]
     * <AUTHOR>
     */
    int insert(AgentStaff record);

    /**
     * @return int
     * @Description :添加
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(AgentStaff record);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过BD编号 模糊查询对应代理ids
     * @Param [bdNum]
     * <AUTHOR>
     */
    List<Long> getAgentIdsByBdNum(@Param("bdNum") String bdNum);

    /**
     * 根据BD绑定的大区ID查询对应代理Ids
     *
     * @param fkAreaRegionId
     * @return
     */
    List<Long> getAgentIdsByBdAreaRegionId(@Param("fkAreaRegionId") Long fkAreaRegionId);

    /**
     * @return java.util.List<com.get.salecenter.entity.StaffBdCode>
     * @Description :绑定BD下拉框数据
     * @Param testBdFLag true:返回公司下所有bd,  false：排除掉T开头的 测试bD
     * <AUTHOR>
     */
    List<StaffBdCodeVo> getStaffBdCodeList(@Param("staffIds") List<Long> staffIds, @Param("testBdFlag") Boolean testBdFlag);


    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 代理下拉
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getAgentSelect(@Param("agentIds") List<Long> agentIds);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: bd下拉
     * @Param []
     * <AUTHOR>
     */
    List<StaffBdCodeVo> getBDSelect(@Param("fkAgentId") Long fkAgentId);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 根据学生id下拉
     * @Param [studentId]
     * <AUTHOR>
     */
    List<BaseSelectEntity> getAgentByStudentId(Long studentId);

    /**
     * 获取学生已绑定申请方案的代理，业务国家过滤
     *
     * @param studentId
     * @param visibleCountryIds
     * @return
     */
    List<BaseSelectEntity> getAgentList(@Param("studentId") Long studentId, @Param("visibleCountryIds") List<Long> visibleCountryIds);


    /**
     * @return java.lang.Integer
     * @Description :查看该bd绑定的代理数
     * @Param [staffId]
     * <AUTHOR>
     */
    List<Agent> getBdCount(@Param("staffId") Long staffId);

    /**
     * 根据代理id获取绑定的bd员工 以及bd编号
     *
     * @Date 14:10 2021/8/3
     * <AUTHOR>
     */
    StaffBdCodeVo selectAgentStaffBdInfo(@Param("agentId") Long agentId);

    /**
     * 根据代理id获取绑定的bd员工
     *
     * @Date 14:10 2021/8/3
     * <AUTHOR>
     */
    Long getStaffByAgent(@Param("id") Long id);

    /**
     * 根据代理ids获取绑定的bd员工
     *
     * @Date 14:10 2021/8/3
     * <AUTHOR>
     */
    Set<Long> getStaffsByAgents(@Param("ids") Set<Long> ids);

    /**
     * 根据代理ids获取绑定的bd员工MAP
     *
     * @Date 14:10 2021/8/3
     * <AUTHOR>
     */
    List<Map<Long, Long>> getStaffsByAgentsMap(@Param("ids") Set<Long> ids);

    List<BaseSelectEntity> getBdNameByAgentIds(@Param("ids") Set<Long> ids);

    /**
     * 根据代理ids获取BD所属的大区id
     *
     * @param agentIds 代理ids
     * @return
     */
    List<StaffBdCodeVo> getAreaRegionIdByAgentIds(@Param("agentIds") Set<Long> agentIds);
}