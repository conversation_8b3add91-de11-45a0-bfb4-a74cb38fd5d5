<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionZoneMapper">
  <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.InstitutionZone">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_institution_id" jdbcType="BIGINT" property="fkInstitutionId" />
    <result column="fk_area_country_id" jdbcType="BIGINT" property="fkAreaCountryId" />
    <result column="fk_area_state_id" jdbcType="BIGINT" property="fkAreaStateId" />
    <result column="fk_area_city_id" jdbcType="BIGINT" property="fkAreaCityId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="name_chn" jdbcType="VARCHAR" property="nameChn" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="view_order" jdbcType="INTEGER" property="viewOrder" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insert" parameterType="com.get.institutioncenter.entity.InstitutionZone" keyProperty="id" useGeneratedKeys="true">
    insert into m_institution_zone (id, fk_institution_id, name, 
      name_chn, description, view_order, fk_area_country_id,fk_area_state_id,fk_area_city_id,
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkInstitutionId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, 
      #{nameChn,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{viewOrder,jdbcType=INTEGER},
            #{fkAreaCountryId,jdbcType=VARCHAR}, #{fkAreaStateId,jdbcType=VARCHAR}, #{fkAreaCityId,jdbcType=INTEGER},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionZone" keyProperty="id" useGeneratedKeys="true">
    insert into m_institution_zone
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkInstitutionId != null">
        fk_institution_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="nameChn != null">
        name_chn,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
      <if test="fkAreaCountryId != null">
        fk_area_country_id,
      </if>
      <if test="fkAreaStateId != null">
        fk_area_state_id,
      </if>
      <if test="fkAreaCityId != null">
        fk_area_city_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionId != null">
        #{fkInstitutionId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="nameChn != null">
        #{nameChn,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCountryId != null">
        #{fkAreaCountryId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaStateId != null">
        #{fkAreaStateId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCityId != null">
        #{fkAreaCityId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="getNameById" parameterType="java.lang.Long" resultType="string">
    select
      CASE WHEN IFNULL(name_chn,'')='' THEN `name` ELSE CONCAT(`name`,'（',name_chn,'）') END fullName
    from
      m_institution_zone i
    where
      i.id = #{id}
  </select>

  <select id="getNameByIds" resultType="com.get.institutioncenter.vo.InstitutionZoneVo">
    select
      ricz.fk_institution_course_id AS fkInstitutionCourseId,
      GROUP_CONCAT(DISTINCT CASE WHEN IFNULL(miz.name_chn,'')='' THEN `name` ELSE CONCAT(miz.`name`,'（',miz.name_chn,'）') END separator '，')  AS fullName
    from
      m_institution_zone miz
        left join
      r_institution_course_zone ricz on miz.id = ricz.fk_institution_zone_id
    where ricz.fk_institution_course_id in
    <foreach collection="fkInstitutionCourseIds" item="fkInstitutionCourseId" index="index" open="(" separator="," close=")">
      #{fkInstitutionCourseId}
    </foreach>
    group by ricz.fk_institution_course_id
  </select>
</mapper>