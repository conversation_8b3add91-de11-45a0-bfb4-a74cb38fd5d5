package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 财务佣金汇总批次子项列表
 *
 * <AUTHOR>
 * @date 2021/12/27 11:42
 */
@Data
public class CommissionSummaryBatchDto {

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @NotNull(message = "公司id不能为空")
    @Min(value = 1, message = "缺少公司id参数")
    private Long fkCompanyId;

    @ApiModelProperty(value = "支付单银行流水号 付款单编号（凭证号）")
    private String numBank;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "财务结算汇总批次号")
    private String numSettlementBatch;

    @ApiModelProperty(value = "代理名称或编号")
    private String agentNameOrNum;

    @ApiModelProperty(value = "学生名称")
    private String studentName;

    @ApiModelProperty(value = "结算账号币种")
    private String fkCurrencyTypeNum;


}
