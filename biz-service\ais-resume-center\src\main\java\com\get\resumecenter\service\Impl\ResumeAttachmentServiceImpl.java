package com.get.resumecenter.service.Impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.resumecenter.dao.ResumeAttachmentMapper;
import com.get.resumecenter.vo.MediaAndAttachedVo;
import com.get.resumecenter.vo.ResumeAttachmentVo;
import com.get.resumecenter.entity.ResumeAttachment;
import com.get.resumecenter.service.IMediaAndAttachedService;
import com.get.resumecenter.service.IResumeAttachmentService;
import com.get.resumecenter.dto.MediaAndAttachedDto;
import com.get.resumecenter.dto.ResumeAttachmentDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/12/29
 * @TIME: 16:54
 * @Description:
 **/
@Service
public class ResumeAttachmentServiceImpl implements IResumeAttachmentService {
    @Resource
    private ResumeAttachmentMapper resumeAttachmentMapper;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private UtilService utilService;

    @Override
    public List<ResumeAttachmentVo> getResumeAttachment(Long resumeId) {
        if (GeneralTool.isEmpty(resumeId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("resume_id_null"));
        }
//        Example example = new Example(ResumeAttachment.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkResumeId", resumeId);
        List<ResumeAttachment> resumeAttachments = resumeAttachmentMapper.selectList(Wrappers.<ResumeAttachment>lambdaQuery().eq(ResumeAttachment::getFkResumeId, resumeId));
        List<ResumeAttachmentVo> collect = resumeAttachments.stream().map(resumeAttachment ->
                BeanCopyUtils.objClone(resumeAttachment, ResumeAttachmentVo::new)).collect(Collectors.toList());

        for (ResumeAttachmentVo attachmentDto : collect) {
            setMedia(attachmentDto);
        }
        return collect;
    }
    @Override
    public List<MediaAndAttachedVo> getResumeMedia(MediaAndAttachedDto attachedVo) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.INSTITUTION.key);
        return attachedService.getMediaAndAttachedDto(attachedVo);
    }
    @Override
    public Long add(ResumeAttachmentDto attachmentVo) {
        if (!GetAuthInfo.isAdmin()) {
            if (!SecureUtil.getIsModifiedResume()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
            }
        }
        if (GeneralTool.isEmpty(attachmentVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ResumeAttachment resumeAttachment = BeanCopyUtils.objClone(attachmentVo, ResumeAttachment::new);
        utilService.updateUserInfoToEntity(resumeAttachment);
        resumeAttachmentMapper.insertSelective(resumeAttachment);

        //上传附件
        if (attachmentVo.getMode() == 0) {
            List<MediaAndAttachedDto> mediaAndAttachedDtos = attachmentVo.getMediaAndAttacheds();
            for (MediaAndAttachedDto mediaAndAttachedDto : mediaAndAttachedDtos) {
                mediaAndAttachedDto.setFkTableName(TableEnum.RESUME_ATTACHMENT.key);
                mediaAndAttachedDto.setFkTableId(resumeAttachment.getId());
                mediaAndAttachedDto.setTypeKey(FileTypeEnum.RESUME_FILE.key);
                attachedService.addMediaAndAttached(mediaAndAttachedDto);
            }
        }
        return resumeAttachment.getId();
    }

    @Override
    public ResumeAttachmentVo update(ResumeAttachmentDto resumeAttachmentDto) {
//        if (!StaffContext.getStaff().getIsAdmin()) {
//            if (!StaffContext.getStaff().getIsModifiedResume()) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
//            }
//        }
        if (!GetAuthInfo.isAdmin()) {
            if (!SecureUtil.getIsModifiedResume()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
            }
        }
        if (GeneralTool.isEmpty(resumeAttachmentDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ResumeAttachment resumeAttachment = BeanCopyUtils.objClone(resumeAttachmentDto, ResumeAttachment::new);
        utilService.updateUserInfoToEntity(resumeAttachment);
        resumeAttachmentMapper.updateById(resumeAttachment);

        if (resumeAttachmentDto.getMode() == 0) {
            //获取附件
            List<MediaAndAttachedDto> mediaAndAttachedDtos = resumeAttachmentDto.getMediaAndAttacheds();
            //删除原先数据
            MediaAndAttachedDto deleteMedia = new MediaAndAttachedDto();
            deleteMedia.setFkTableName(TableEnum.RESUME_ATTACHMENT.key);
            deleteMedia.setFkTableId(resumeAttachment.getId());
            deleteMedia.setTypeKey(FileTypeEnum.RESUME_FILE.key);
            attachedService.delete(deleteMedia);

            for (MediaAndAttachedDto mediaAndAttachedDto : mediaAndAttachedDtos) {
                mediaAndAttachedDto.setFkTableName(TableEnum.RESUME_ATTACHMENT.key);
                mediaAndAttachedDto.setFkTableId(resumeAttachment.getId());
                mediaAndAttachedDto.setTypeKey(FileTypeEnum.RESUME_FILE.key);
                attachedService.addMediaAndAttached(mediaAndAttachedDto);
            }
        }
        return findResumeAttachmentById(resumeAttachment.getId());
    }

    @Override
    public ResumeAttachmentVo findResumeAttachmentById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ResumeAttachment resumeAttachment = resumeAttachmentMapper.selectById(id);
        ResumeAttachmentVo attachmentDto = BeanCopyUtils.objClone(resumeAttachment, ResumeAttachmentVo::new);
        setMedia(attachmentDto);
        return attachmentDto;
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        MediaAndAttachedDto mediaAndAttachedDto = new MediaAndAttachedDto();
        mediaAndAttachedDto.setFkTableName(TableEnum.RESUME_ATTACHMENT.key);
        mediaAndAttachedDto.setFkTableId(id);
        mediaAndAttachedDto.setTypeKey(FileTypeEnum.RESUME_FILE.key);
        attachedService.delete(mediaAndAttachedDto);

        resumeAttachmentMapper.deleteById(id);
    }


    private void setMedia(ResumeAttachmentVo attachmentDto) {
        MediaAndAttachedDto attachedVo = new MediaAndAttachedDto();
        attachedVo.setFkTableId(attachmentDto.getId());
        attachedVo.setFkTableName(TableEnum.RESUME_ATTACHMENT.key);
        attachedVo.setTypeKey(FileTypeEnum.RESUME_FILE.key);
        List<MediaAndAttachedVo> mediaAndAttachedVo = attachedService.getMediaAndAttachedDto(attachedVo);
        attachmentDto.setMediaAndAttacheds(mediaAndAttachedVo);
    }

}
