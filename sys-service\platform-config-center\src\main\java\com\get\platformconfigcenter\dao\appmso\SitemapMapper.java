package com.get.platformconfigcenter.dao.appmso;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.Sitemap;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("appmsodb")
public interface SitemapMapper extends BaseMapper<Sitemap> {
//    int insert(Sitemap record);
//
//    int insertSelective(Sitemap record);
//
//    int updateByPrimaryKeySelective(Sitemap record);
//
//    int updateByPrimaryKey(Sitemap record);
//
//    /**
//     * 子菜单
//     *
//     * @Date 14:38 2021/7/27
//     * <AUTHOR>
//     */
//    List<SitemapVo> getChildSitemap(@Param("sitemapId") Long sitemapId);
//
//
//    /**
//     * @Description: 获取最大排序值
//     * @Author: Jerry
//     * @Date:11:09 2021/8/11
//     */
//    Integer getMaxViewOrder();
}