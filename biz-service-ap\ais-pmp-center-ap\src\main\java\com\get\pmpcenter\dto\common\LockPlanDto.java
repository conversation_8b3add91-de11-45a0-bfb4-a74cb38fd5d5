package com.get.pmpcenter.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/3/18
 * @Version 1.0
 * @apiNote:
 */
@Data
public class LockPlanDto {

    @ApiModelProperty(value = "是否锁定：0否/1是")
    @NotNull(message = "是否锁定状态不能为空")
    private Integer isLocked;

    @ApiModelProperty(value = "方案ID")
    @NotNull(message = "方案ID不能为空")
    private Long planId;
}
