package com.get.institutioncenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CourseAppInfoPriorityVo {

    @ApiModelProperty(value = "奖学金id")
    private Long id;

    @ApiModelProperty(value = "优先级匹配用的key")
    private String fk;

    @ApiModelProperty(value = "优先级匹配用的key")
    private String fc;

    @ApiModelProperty(value = "类型")
    private String fkTableType;

    @ApiModelProperty(value = "类型id")
    private Long fkTypeId;

    @ApiModelProperty(value = "排序字段")
    private Integer priority;

    @ApiModelProperty(value = "分组字段")
    private String effectiveDate;

    @ApiModelProperty(value = "最终结果排序字段")
    private Date gmtPriorityTime;
}
