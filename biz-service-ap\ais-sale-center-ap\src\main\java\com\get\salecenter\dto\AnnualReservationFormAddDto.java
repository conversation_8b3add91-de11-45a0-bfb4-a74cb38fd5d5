package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AnnualReservationFormAddDto extends BaseVoEntity {

    @ApiModelProperty("人员信息")
    private List<AnnualReservationFormDto> annualReservationFormVos;

    @NotBlank(message = "回执码不能为null")
    @ApiModelProperty("回执码")
    private String receiptCode;
}
