package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.ClientSourceType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * 学生资源推荐来源类型DTO
 */
@Data
public class ClientSourceTypeVo extends BaseEntity {

    //==========实体类ClientSourceType=============

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "类型名称")
    @Column(name = "type_name")
    private String typeName;

    /**
     * 类型Key：bms_student_num/crm_contract_num/m_agent/bms_student_num_not_os/m_business_provider
     */
    @ApiModelProperty(value = "类型Key：bms_student_num/crm_contract_num/m_agent/bms_student_num_not_os/m_business_provider")
    @Column(name = "type_key")
    private String typeKey;

    @ApiModelProperty(value = "类型标识：100%, 30%")
    @Column(name = "type_mark")
    private String typeMark;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}
