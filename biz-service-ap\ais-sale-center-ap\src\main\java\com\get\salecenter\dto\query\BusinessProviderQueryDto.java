package com.get.salecenter.dto.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

@Data
public class BusinessProviderQueryDto {

 /**
  * 公司Id
  */
 @ApiModelProperty(value = "公司Id")
 private Long fkCompanyId;

 private List<Long> fkCompanyIds;

 /**
  * 业务类型关键字，枚举：m_student_insurance留学保险/m_student_accommodation留学住宿
  */
 @ApiModelProperty(value = "业务类型关键字，枚举：m_student_insurance留学保险/m_student_accommodation留学住宿")
 private String fkTypeKey;

 /**
  * 是否激活：0否/1是
  */
 @ApiModelProperty(value = "是否激活：0否/1是")
 private Boolean isActive;
   
 private String keyWord;

 @ApiModelProperty(value = "提供商业务类型Id")
 private Long fkBusinessProviderTypeId;
}