package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("m_agent")
public class Agent extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 学生代理父Id
     */
    @ApiModelProperty(value = "学生代理父Id")
    @Column(name = "fk_parent_agent_id")
    @UpdateWithNull
    private Long fkParentAgentId;
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 州省Id
     */
    @ApiModelProperty(value = "州省Id")
    @Column(name = "fk_area_state_id")
    private Long fkAreaStateId;
    /**
     * 城市Id
     */
    @ApiModelProperty(value = "城市Id")
    @Column(name = "fk_area_city_id")
    private Long fkAreaCityId;
    /**
     * 代理编号
     */
    @ApiModelProperty(value = "代理编号")
    @Column(name = "num")
    private String num;
    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    @Column(name = "name")
    private String name;
    /**
     * 名称备注
     */
    @ApiModelProperty(name = "名称备注")
    @Column(name = "name_note")
    private String nameNote;
    /**
     * 代理昵称
     */
    @ApiModelProperty(value = "代理昵称")
    @Column(name = "nick_name")
    private String nickName;
    /**
     * 性质：公司/个人/工作室/国际学校/其他
     */
    @ApiModelProperty(value = "性质：公司/个人/工作室/国际学校/其他")
    @Column(name = "nature")
    private String nature;
    /**
     * 性质备注
     */
    @ApiModelProperty(value = "性质备注")
    @Column(name = "nature_note")
    private String natureNote;
    /**
     * 法定代表人
     */
    @ApiModelProperty(value = "法定代表人")
    @Column(name = "legal_person")
    private String legalPerson;
    /**
     * 税号/统一社会信用代码（公司）
     */
    @ApiModelProperty(value = "税号/统一社会信用代码（公司）")
    @Column(name = "tax_code")
    private String taxCode;
    /**
     * 身份证号（个人）
     */
    @ApiModelProperty(value = "身份证号（个人）")
    @Column(name = "id_card_num")
    private String idCardNum;
    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    @Column(name = "address")
    private String address;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 邀请码，8位数字或字母随机数
     */
    @ApiModelProperty(value = "邀请码，8位数字或字母随机数")
    @Column(name = "invitation_code")
    private String invitationCode;
    /**
     * 是否结算口，0否/1是
     */
    @ApiModelProperty(value = "是否结算口，0否/1是")
    @Column(name = "is_settlement_port")
    private Boolean isSettlementPort;
    /**
     * 是否关键代理：0否/1是
     */
    @ApiModelProperty(value = "是否关键代理：0否/1是")
    @Column(name = "is_key_agent")
    private Boolean isKeyAgent;
    /**
     * 关键代理失效时间
     */
    @ApiModelProperty(value = "关键代理失效时间")
    @Column(name = "key_agent_failure_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date keyAgentFailureTime;

    @ApiModelProperty(value = "是否渠道代理：0否/1是")
    @Column(name = "is_customer_channel")
    private Boolean isCustomerChannel;

    @ApiModelProperty(value = "是否拒收系统邮件：0否/1是")
    @Column(name = "is_reject_email")
    private Boolean isRejectEmail;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    @Column(name = "id_gea")
    private String idGea;
    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    @Column(name = "id_iae")
    private String idIae;


    @ApiModelProperty("个人姓名")
    @Column(name = "personal_name")
    private String personalName;

    /**
     * <p> Java类型:Integer &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:int(10) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:合同审批状态：0无合同/1有合同/5续约中 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Column(name = "合同审批状态：0无合同/1有合同/5续约中")
    @JsonProperty("contractApprovalStatus")
    @TableField("contract_approval_status")
    private Integer contractApprovalStatus;


}