package com.get.financecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2020/11/19
 * @TIME: 15:54
 * @Description:
 **/
@Data
public class ReceivablePlanVo {

    @ApiModelProperty("id，主键")
    private Long id;
    /**
     * 应收类型关键字，枚举，如：student_offer_item
     */
    @ApiModelProperty(value = "应收类型关键字，枚举，如：student_offer_item")
    private String fkTypeKey;

    /**
     * 应收类型对应记录Id，如：m_student_offer_item.id
     */
    @ApiModelProperty(value = "应收类型对应记录Id，如：m_student_offer_item.id")
    private Long fkTypeTargetId;

    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    /**
     * 学费金额
     */
    @ApiModelProperty(value = "学费金额")
    private BigDecimal tuitionAmount;

    /**
     * 费率%
     */
    @ApiModelProperty(value = "费率%")
    private BigDecimal commissionRate;

    /**
     * 定额金额
     */
    @ApiModelProperty(value = "定额金额")
    private BigDecimal fixedAmount;

    /**
     * 奖励金额
     */
    @ApiModelProperty(value = "奖励金额")
    private BigDecimal bonusAmount;

    /**
     * 应收金额
     */
    @ApiModelProperty(value = "应收金额")
    private BigDecimal receivableAmount;

    /**
     * 计划收款时间
     */
    @ApiModelProperty(value = "计划收款时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date receivablePlanDate;

    /**
     * 应收计划额外原因Id
     */
    @ApiModelProperty(value = "应收计划额外原因Id")
    private Integer fkReceivableReasonId;

    /**
     * 状态：0关闭/1打开/2完成
     */
    @ApiModelProperty(value = "状态：0关闭/1打开/2完成")
    private Integer status;


    @ApiModelProperty(value = "应收类型名称")
    private String fkTypeName;

    /**
     * 应收类型对应记录名称
     */
    @ApiModelProperty(value = "应收类型对应记录名称")
    private String fkTypeTargetName;

    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;

    /**
     * 提供商名称
     */
    @ApiModelProperty(value = "提供商名称")
    private String fkInstitutionProviderName;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String fkInstitutionName;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称")
    private String fkCourseName;


    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String fkCurrencyTypeName;

    /**
     * 应收计划额外原因Id
     */
    @ApiModelProperty(value = "应收计划额外原因名称")
    private String fkReceivableReasonName;

    /**
     * 记录总名称
     */
    @ApiModelProperty(value = "记录总名称")
    private String targetNames;

    @ApiModelProperty(value = "学生信息")
    private String studentInformation;

    @ApiModelProperty(value = "业务信息")
    private String businessInformation;

    @ApiModelProperty(value = "渠道信息")
    private String channelInformation;
}
