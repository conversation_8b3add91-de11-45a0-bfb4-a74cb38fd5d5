package com.get.aismail.vo;

import lombok.Data;

@Data
public class CountNotReadNumVo {
    // 未读数量
    private Integer notReadNum;

    // 新申请未读数量
    private int newApplicationNum;
    // 新申请今天未读数量
    private int todayNewApplicationNum;
    // 新申请昨天未读数量
    private int yesterdayNewApplicationNum;
    // 新申请其他未读数量
    private int otherNewApplicationNum;

    // 提交完成数量
    private int finishSubmitNum;
    // 提交完成今天未读数
    private int todayFinishSubmitNum;
    // 提交完成昨天未读数
    private int yesterdayFinishSubmitNum;
    // 提交完成其他未读数
    private int otherFinishSubmitNum;

    // 已录取数量
    private int admissionNum;
    // 已录取今天未读数
    private int todayAdmissionNum;
    // 已录取昨天未读数
    private int yesterdayAdmissionNum;
    // 已录取其他维度
    private int otherAdmissionNum;

    // 已付学费
    private int payTuitionNum;
    // 已付学费今天未读数
    private int todayPayTuitionNum;
    // 已付学费昨天未读书
    private int yesterdayPayTuitionNum;
    // 已付学费其他未读数
    private int otherPayTuitionNum;

    // 收到签证涵
    private int visaNum;
    // 收到签证涵今天未读数
    private int todayVisaNum;
    // 收到签证涵昨天未读数
    private int yesterdayVisaNum;
    // 收到签证涵其他未读
    private int otherVisaNum;

    // 获得签证
    private int getVisaNum;
    // 获得签证今天未读数
    private int todayGetVisaNum;
    // 获得签证昨天未读数
    private int yesterdayGetVisaNum;
    // 获得签证其他未读数
    private int otherGetVisaNum;

    // 系统提醒未读总数
    private int systemReminderNum;
    // 系统提醒今天未读总数
    private int todaySystemReminderNum;
    // 系统提醒昨天未读总数
    private int yesterdaySystemReminderNum;
    // 系统其他未读总数
    private int otherSystemReminderNum;

    // 今天未读数量
    private Integer todayNotReadNum;
    // 昨天未读数量
    private Integer yesterdayNotReadNum;
    // 其他未读数量
    private Integer otherNotReadNum;
}
