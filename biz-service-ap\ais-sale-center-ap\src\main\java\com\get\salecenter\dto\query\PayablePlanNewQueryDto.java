package com.get.salecenter.dto.query;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class PayablePlanNewQueryDto {

    @ApiModelProperty(value = "应收计划类型")
    private String fkTypeKey;

    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryIds;

    @ApiModelProperty(value = "学生信息（中英名称/生日）")
    private String studentName;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "业务信息（学校/课程/保险/住宿）")
    private String bziName;

    @ApiModelProperty(value = "收齐状态：0未付/1已付部分/2已付齐/3未付齐")
    private Integer payableStatus;

    @ApiModelProperty(value = "创建时间(开始)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty(value = "创建时间(结束)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "公司ids")
    private List<Long> fkCompanyIds;

    @ApiModelProperty(value = "摘要")
    private String summary;

    @ApiModelProperty(value = "是否预付，0否/1是")
    private Integer isPayInAdvance;

    @ApiModelProperty(value = "导出ids")
    private List<Long> exportIds;

    @ApiModelProperty(value = "业务信息补充（学校/课程）")
    private String bziNameSupplement;

    @ApiModelProperty(value = "发票编号")
    private String fkInvoiceNums;

    @ApiModelProperty(value = "学生佣金结算标记关键字")
    private String commissionMark;

    @ApiModelProperty(value = "收款状态  0:未收 1：部分已收 2：已收齐")
    private Integer tradeStatus;

    @ApiModelProperty(value = "是否为发票详情调用flag true:发票调用保证实时性  false：非发票调用 使用数据仓库")
    private Boolean isInvoiceFlag = false;

    @ApiModelProperty(value = "学生id")
    private Long fkStudentId;

    //=============path=====================
    @ApiModelProperty(value = "应付金额不为0开关")
    private Boolean payableAmountUnequalZeroFlag;
}
