package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("r_payable_plan_settlement_status")
public class PayablePlanSettlementStatus extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 财务结算汇总批次号
     */
    @ApiModelProperty(value = "财务结算汇总批次号")
    private String numSettlementBatch;
    /**
     * 应付计划Id
     */
    @ApiModelProperty(value = "应付计划Id")
    private Long fkPayablePlanId;

    @ApiModelProperty(value = "应付计划结算分期表Id")
    private Long fkPayablePlanSettlementInstallmentId;

    /**
     * 结算状态：0未结算/1结算中/2代理确认/3财务确认
     */
    @ApiModelProperty(value = "结算状态：0未结算/1结算中/2代理确认/3财务确认")
    private Integer statusSettlement;
}