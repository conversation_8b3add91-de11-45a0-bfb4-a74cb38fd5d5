package com.get.financecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 财务佣金汇总批次列表
 *
 * <AUTHOR>
 * @date 2021/12/27 11:29
 */
@Data
public class CommissionSummaryBatchVo {

    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    @ApiModelProperty(value = "财务结算汇总批次号")
    private String numSettlementBatch;

    @ApiModelProperty(value = "合作伙伴数")
    private Long agentNum;

    @ApiModelProperty(value = "学生数")
    private Long studentNum;

    @ApiModelProperty(value = "应付计划数")
    private Long payablePlanNum;

    @ApiModelProperty(value = "已绑定付款单")
    private Long payableFormNum;

    @ApiModelProperty(value = "支付流水号")
    private String numBank;


}
