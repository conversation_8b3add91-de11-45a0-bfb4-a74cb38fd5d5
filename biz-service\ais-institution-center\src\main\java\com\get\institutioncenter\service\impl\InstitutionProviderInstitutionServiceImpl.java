package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.*;
import com.get.institutioncenter.vo.InstitutionProviderInstitutionVo;
import com.get.institutioncenter.vo.InstitutionVo;
import com.get.institutioncenter.vo.ProviderInstitutionRelationVo;
import com.get.institutioncenter.entity.InstitutionProviderCompany;
import com.get.institutioncenter.entity.InstitutionProviderInstitution;
import com.get.institutioncenter.service.IInstitutionProviderInstitutionService;
import com.get.institutioncenter.service.IInstitutionService;
import com.get.institutioncenter.dto.InstitutionProviderInstitutionDto;
import com.get.institutioncenter.dto.ProviderInstitutionRelationDto;
import com.get.institutioncenter.dto.query.InstitutionQueryDto;
import com.get.salecenter.feign.ISaleCenterClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/8/24
 * @TIME: 16:41
 * @Description: 学校提供商管理-学校绑定配置实现类
 **/

@Service
public class InstitutionProviderInstitutionServiceImpl extends BaseServiceImpl<InstitutionProviderInstitutionMapper, InstitutionProviderInstitution> implements IInstitutionProviderInstitutionService {

    @Resource
    private InstitutionProviderInstitutionMapper providerInstitutionMapper;
    @Lazy
    @Resource
    private IInstitutionService institutionService;
    @Resource
    private UtilService utilService;
    @Resource
    private ContractMapper contractMapper;
    @Resource
    private ContractFormulaPreInstitutionMapper contractFormulaPreInstitutionMapper;
    @Resource
    private InstitutionProviderAreaCountryMapper institutionProviderAreaCountryMapper;
    @Resource
    private InstitutionProviderCompanyMapper institutionProviderCompanyMapper;

    @Resource
    private ISaleCenterClient saleCenterClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editProviderInstitutionRelation(List<InstitutionProviderInstitutionDto> providerInstitutionVos) {
        if (GeneralTool.isEmpty(providerInstitutionVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        //获取学校ID
        List<Long> collect = providerInstitutionVos.stream()
                .filter(Objects::nonNull)
                .map(InstitutionProviderInstitutionDto::getFkInstitutionId).collect(Collectors.toList());
        //移除空元素
        collect.removeIf(Objects::isNull);

        Long providerId = providerInstitutionVos.get(0).getFkInstitutionProviderId();
        LambdaQueryWrapper<InstitutionProviderInstitution> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionProviderInstitution::getFkInstitutionProviderId, providerId);
        //不能全删了-查原来有多少-对比新加的
        List<InstitutionProviderInstitution> institutionProviderInstitutions = providerInstitutionMapper.selectList(wrapper);
        List<Long> institutionIds = new ArrayList<>();
        if (GeneralTool.isNotEmpty(institutionProviderInstitutions)){
            institutionIds = institutionProviderInstitutions.stream().map(InstitutionProviderInstitution::getFkInstitutionId).collect(Collectors.toList());
        }
        if (GeneralTool.isEmpty(institutionIds)){
            institutionIds.add(0L);
        }
//        providerInstitutionMapper.delete(wrapper);

        if (GeneralTool.isNotEmpty(collect)) {
            List<InstitutionProviderCompany> institutionProviderCompanyList = institutionProviderCompanyMapper.selectList(Wrappers.<InstitutionProviderCompany>lambdaQuery().eq(InstitutionProviderCompany::getFkInstitutionProviderId, providerId));
            String companyIds = null;
            if (GeneralTool.isNotEmpty(institutionProviderCompanyList)) {
                companyIds = institutionProviderCompanyList.stream().map(InstitutionProviderCompany::getFkCompanyId).map(String::valueOf).collect(Collectors.joining(","));
            }

            List<InstitutionProviderInstitution> providerInstitutions =
                    providerInstitutionVos.stream()
                            .map(providerInstitutionVo -> BeanCopyUtils.objClone(providerInstitutionVo, InstitutionProviderInstitution::new))
                            .collect(Collectors.toList());
            //循环插入
            for (InstitutionProviderInstitution providerInstitution : providerInstitutions) {
                if (institutionIds.contains(providerInstitution.getFkInstitutionId())){
                    continue;
                }
                providerInstitution.setFkCompanyIds(companyIds);
                providerInstitution.setIsActive(true);
                providerInstitution.setActiveDate(new Date());
                utilService.setCreateInfo(providerInstitution);
                providerInstitutionMapper.insert(providerInstitution);
            }
//            providerInstitutions.forEach(institutionProviderInstitution ->
//            {
//
//                utilService.updateUserInfoToEntity(institutionProviderInstitution);
//
//                providerInstitutionMapper.insertSelective(institutionProviderInstitution);
//            });
        }
    }

    @Override
    public List<ProviderInstitutionRelationVo> getProviderInstitutionRelation(ProviderInstitutionRelationDto relationVo, Page page) {
        if (GeneralTool.isEmpty(relationVo.getFkProviderId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("providerId_null"));
        }
        return getProviderInstitutionRelationDtos(relationVo, page);
    }

    @Override
    public List<InstitutionProviderInstitutionVo> getAllRelation(Long fkInstitutionProviderId, Long fkInstitutionId) {
        LambdaQueryWrapper<InstitutionProviderInstitution> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(fkInstitutionId)) {
            wrapper.eq(InstitutionProviderInstitution::getFkInstitutionId, fkInstitutionId);
        }
        if (GeneralTool.isNotEmpty(fkInstitutionProviderId)) {
            wrapper.eq(InstitutionProviderInstitution::getFkInstitutionProviderId, fkInstitutionProviderId);
        }

        List<InstitutionProviderInstitution> institutionProviderInstitutions = providerInstitutionMapper.selectList(wrapper);
        return institutionProviderInstitutions.stream().map(providerInstitution ->
                BeanCopyUtils.objClone(providerInstitution, InstitutionProviderInstitutionVo::new)).collect(Collectors.toList());
    }

    /**
     * 查询学校
     *
     * @param institutionRelationVo
     * @return
     * @
     */
    private List<ProviderInstitutionRelationVo> getProviderInstitutionRelationDtos(ProviderInstitutionRelationDto institutionRelationVo, Page page) {
        //查询学校
        InstitutionQueryDto institutionVo = BeanCopyUtils.objClone(institutionRelationVo, InstitutionQueryDto::new);
        //根据提供商业务国家过滤
        if (GeneralTool.isEmpty(institutionVo.getCountryIds())) {
            List<Long> countryIds = institutionProviderAreaCountryMapper.getAreaCountryIdsByProviderId(institutionRelationVo.getFkProviderId());
            if (GeneralTool.isNotEmpty(countryIds)) {
                institutionVo.setCountryIds(countryIds);
            }
        }
        List<InstitutionVo> datas = institutionService.datas(institutionVo, page);
        List<ProviderInstitutionRelationVo> institutionDtos =
                datas.stream()
                        .map(institutionDto -> BeanCopyUtils.objClone(institutionDto, ProviderInstitutionRelationVo::new)).collect(Collectors.toList());
        if (GeneralTool.isEmpty(institutionDtos)) {
            return null;
        }
        //获取中间表数据
        List<InstitutionProviderInstitution> relations = getRelationByProviderId(institutionRelationVo.getFkProviderId());
        setFlag(institutionDtos, relations);
        return institutionDtos;
    }


    /**
     * 设置标志
     *
     * @param institutionDtos
     * @param relations
     */
    private void setFlag(List<ProviderInstitutionRelationVo> institutionDtos, List<InstitutionProviderInstitution> relations) {
        //是否选中
        for (ProviderInstitutionRelationVo institutionDto : institutionDtos) {
            for (InstitutionProviderInstitution relation : relations) {
                if (institutionDto.getId().equals(relation.getFkInstitutionId())) {
                    institutionDto.setFlag(true);
                }
            }
        }
        //排序
        institutionDtos.sort(Comparator.comparing(ProviderInstitutionRelationVo::getFlag, Comparator.nullsLast(Boolean::compareTo)));
    }

    /**
     * 获取中间表数据
     *
     * @param providerId
     * @return
     */
    private List<InstitutionProviderInstitution> getRelationByProviderId(Long providerId) {
        LambdaQueryWrapper<InstitutionProviderInstitution> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionProviderInstitution::getFkInstitutionProviderId, providerId);
        return providerInstitutionMapper.selectList(wrapper);
    }


    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionProviderInstitution institutionProviderInstitution = providerInstitutionMapper.selectById(id);
        if (GeneralTool.isEmpty(institutionProviderInstitution)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        checkDeleteProviderInstitution(institutionProviderInstitution);
        int i = providerInstitutionMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }

    }

    /**
     * 学校提供商：学校关系删除校验
     * @param institutionProviderInstitution
     */
    private void checkDeleteProviderInstitution(InstitutionProviderInstitution institutionProviderInstitution){


        if (saleCenterClient.getProviderInstitutionItem(institutionProviderInstitution.getFkInstitutionId(),institutionProviderInstitution.getFkInstitutionProviderId()).getData()){
            throw new GetServiceException(LocaleMessageUtils.getMessage("provider_item_data_association"));
        }

        if (contractMapper.isExistByInstitutionId(institutionProviderInstitution.getFkInstitutionId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("institution_contract_data_association"));
        }
        if (contractFormulaPreInstitutionMapper.isExistByInstitutionId(institutionProviderInstitution.getFkInstitutionId(), institutionProviderInstitution.getFkInstitutionProviderId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("institution_contractformula_data_association"));
        }

    }
}
