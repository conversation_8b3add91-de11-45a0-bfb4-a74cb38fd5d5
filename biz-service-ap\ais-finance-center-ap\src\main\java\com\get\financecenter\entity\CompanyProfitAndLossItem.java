package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 公司利润表项目
 */
@Data
@TableName("m_company_profit_and_loss_item")
public class CompanyProfitAndLossItem extends BaseEntity implements Serializable {
    @ApiModelProperty("公司id")
    private Long fkCompanyId;

    @ApiModelProperty("项目名称")
    private String title;

    @ApiModelProperty("显示模式：Code/Expand/Sum")
    private String showMode;

    @ApiModelProperty("科目Id")
    private Long fkAccountingItemId;

    @ApiModelProperty("加减方向：1/-1")
    private Integer directionValue;

    @ApiModelProperty("排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty("颜色代码RGB")
    private String colorCode;


}

