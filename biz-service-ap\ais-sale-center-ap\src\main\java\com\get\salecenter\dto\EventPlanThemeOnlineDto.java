package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2023/12/14
 * @TIME: 14:16
 * @Description:
 **/
@Data
public class EventPlanThemeOnlineDto extends BaseVoEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动计划主题Id")
    private Long fkEventPlanThemeId;

    @ApiModelProperty(value = "项目名称")
    private String name;

    @ApiModelProperty(value = "报名币种")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "报名费用")
    private BigDecimal amount;

    @ApiModelProperty(value = "费用单位")
    private String unit;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "是否激活：0否/1是，若否需要灰掉活动项目")
    private Boolean isActive;

  


}
