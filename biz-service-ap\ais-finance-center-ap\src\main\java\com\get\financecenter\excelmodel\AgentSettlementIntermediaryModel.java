package com.get.financecenter.excelmodel;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 中介计算excel模板
 *
 * <AUTHOR>
 * @date 2022/1/6 14:54
 */
@Data
public class AgentSettlementIntermediaryModel {

    @ApiModelProperty(value = "应付计划id")
    private Long id;

    @ApiModelProperty(value = "学生名字")
    private String studentName;

    @ApiModelProperty(value = "国家编号")
    private String countryNum;

    @ApiModelProperty(value = "学校名称")
    private String fkInstitutionName;

    @ApiModelProperty(value = "课程名称")
    private String fkCourseName;

    @ApiModelProperty(value = "应付币种")
    private String payablePlanCurrency;

    @ApiModelProperty("开学时间")
    private String openTime;

    @ApiModelProperty(value = "应付金额")
    private BigDecimal payableAmount;

    @ApiModelProperty(value = "学费金额")
    private BigDecimal tuitionAmount;

    @ApiModelProperty(value = "IAE倒推 学费金额")
    private BigDecimal tuitionAmountIae;


    @ApiModelProperty(value = "费率%(代理)")
    private String commissionRate;

    @ApiModelProperty(value = "代理分成比率%")
    private String splitRate;

    @ApiModelProperty(value = "折合已付金额")
    private BigDecimal paid;

    @ApiModelProperty(value = "本次结算金额")
    private BigDecimal outstanding;

    @ApiModelProperty(value = "实际手续费金额")
    private BigDecimal serviceFeeActual;

}
