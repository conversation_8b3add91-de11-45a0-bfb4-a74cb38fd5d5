package com.get.resumecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.resumecenter.entity.SkillType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

;

@Mapper
public interface SkillTypeMapper extends BaseMapper<SkillType> {
    int insert(SkillType record);

    int insertSelective(SkillType record);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 下拉
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getSkillTypeSelect();


    Integer getMaxViewOrder();

    /**
     * @return java.lang.String
     * @Description: 根据类型id查询类型名称
     * @Param [industryTypeId]
     * <AUTHOR>
     */
    String getTypeNameByTypeId(Long industryTypeId);
}