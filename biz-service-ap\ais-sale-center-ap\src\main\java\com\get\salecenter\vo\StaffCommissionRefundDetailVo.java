package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class StaffCommissionRefundDetailVo {

    @ApiModelProperty("员工id")
    private Long fkStaffId;

    @ApiModelProperty("员工名称")
    private String fkStaffName;

    @ApiModelProperty("岗位名称")
    private String position;

    @ApiModelProperty("结算金额（已结算）")
    private BigDecimal commissionAmount;

    @ApiModelProperty("结算金额(未结算)")
    private BigDecimal unCommissionAmount;

    @ApiModelProperty("OS数(已结算)")
    private Integer osSettlementQuantity;

    @ApiModelProperty("OS数(未结算)")
    private Integer osUnSettlementQuantity;

    @ApiModelProperty("签证数（已结算）")
    private Integer vgSettlementQuantity;

    @ApiModelProperty("签证数（未结算）")
    private Integer vgUnSettlementQuantity;

    @ApiModelProperty("入学数（已结算）")
    private Integer seSettlementQuantity;

    @ApiModelProperty("入学数（未结算）")
    private Integer seUnSettlementQuantity;

    @ApiModelProperty("后补签证数（已结算）")
    private Integer vgbSettlementQuantity;

    @ApiModelProperty("后补签证数（未结算）")
    private Integer vgbUnSettlementQuantity;




    @ApiModelProperty("OS金额(未结算)")
    private BigDecimal osSettlementAmount;

    @ApiModelProperty("OS金额(已结算)")
    private BigDecimal osUnSettlementAmount;

    @ApiModelProperty("签证金额（已结算）")
    private BigDecimal vgSettlementAmount;

    @ApiModelProperty("签证金额（未结算）")
    private BigDecimal vgUnSettlementAmount;

    @ApiModelProperty("入学金额（已结算）")
    private BigDecimal seSettlementAmount;

    @ApiModelProperty("入学金额（未结算）")
    private BigDecimal seUnSettlementAmount;

    @ApiModelProperty("后补签证金额（已结算）")
    private BigDecimal vgbSettlementAmount;

    @ApiModelProperty("后补签证金额（未结算）")
    private BigDecimal vgbUnSettlementAmount;
}
