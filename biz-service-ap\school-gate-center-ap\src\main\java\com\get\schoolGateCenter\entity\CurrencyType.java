package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("u_currency_type")
public class CurrencyType extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 货币类型编号
     */
    @ApiModelProperty(value = "货币类型编号")
    private String num;
    /**
     * 货币类型名称
     */
    @ApiModelProperty(value = "货币类型名称")
    private String typeName;
    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    private String publicLevel;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
}