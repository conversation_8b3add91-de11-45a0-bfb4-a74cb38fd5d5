package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/5/25 12:36
 * @verison: 1.0
 * @description:
 */
@Data
public class GoproNucleicAcidUpdateDto extends BaseVoEntity {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 活动场地：敦煌/西双版纳/呼伦贝尔
     */
    @ApiModelProperty(value = "活动场地：敦煌/西双版纳/呼伦贝尔")
    private String retreatTypeName;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男")
    private Integer gender;

    /**
     * 所在机构/院校
     */
    @ApiModelProperty(value = "所在机构/院校")
    private String institution;

    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    private String mobile;

    /**
     * 证件类型：身份证/护照
     */
    @ApiModelProperty(value = "证件类型：身份证/护照")
    private String identityType;

    /**
     * 身份证/护照号
     */
    @ApiModelProperty(value = "身份证/护照号")
    private String identityCard;

    /**
     * 出发国家/地区
     */
    @ApiModelProperty(value = "出发国家/地区")
    private String areaCountryName;

    /**
     * 出发州省
     */
    @ApiModelProperty(value = "出发州省")
    private String areaStateName;

    /**
     * 出发城市
     */
    @ApiModelProperty(value = "出发城市")
    private String areaCityName;

    /**
     * 到达日期
     */
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "到达日期")
    private String arrivalDate;

    /**
     * 交通班次编号
     */
    @ApiModelProperty(value = "交通班次编号")
    private String transportationCode;

    /**
     * 行程安排
     */
    @ApiModelProperty(value = "行程安排")
    private String scheduling;

    /**
     * bd区域
     */
    @ApiModelProperty(value = "bd区域")
    private String bdRegion;

    /**
     * bd姓名
     */
    @ApiModelProperty(value = "bd姓名")
    private String bdName;

    /**
     * 排序id
     */
    @ApiModelProperty(value = "排序id")
    private Integer orderId;

    /**
     * 保存附件对象
     */
    @ApiModelProperty("附件对象")
    private List<MediaAndAttachedDto> mediaAttachedVos;

}
