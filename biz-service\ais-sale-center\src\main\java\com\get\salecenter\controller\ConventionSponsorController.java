package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.vo.ConventionSponsorVo;
import com.get.salecenter.vo.ConventionSponsorFeeVo;
import com.get.salecenter.service.IConventionSponsorService;
import com.get.salecenter.dto.ConventionSponsorDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @author: Sea
 * @create: 2021/5/8 11:23
 * @verison: 1.0
 * @description:
 */
@Api(tags = "峰会赞助商管理")
@RestController
@RequestMapping("sale/conventionSponsor")
public class ConventionSponsorController {
    @Resource
    private IConventionSponsorService conventionSponsorService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ConventionSponsorVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/峰会赞助商管理/峰会赞助商详情")
    @GetMapping("/{id}")
    public ResponseBo<ConventionSponsorVo> detail(@PathVariable("id") Long id) {
        ConventionSponsorVo data = conventionSponsorService.findConventionSponsorById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增信息
     * @Param [conventionSponsorVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/峰会赞助商管理/新增峰会赞助商")
    @PostMapping("add")
    public ResponseBo add(@RequestBody  @Validated(ConventionSponsorDto.Add.class) ConventionSponsorDto conventionSponsorDto) {
        return SaveResponseBo.ok(conventionSponsorService.addConventionSponsor(conventionSponsorDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/峰会赞助商管理/删除峰会赞助商")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        conventionSponsorService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ConventionSponsorVo>
     * @Description :修改信息
     * @Param [conventionSponsorDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/峰会赞助商管理/更新峰会赞助商")
    @PostMapping("update")
    public ResponseBo<ConventionSponsorVo> update(@RequestBody  @Validated(ConventionSponsorDto.Update.class)  ConventionSponsorDto conventionSponsorDto) {
        return UpdateResponseBo.ok(conventionSponsorService.updateConventionSponsor(conventionSponsorDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ConventionSponsorVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/峰会赞助商管理/查询峰会赞助商")
    @PostMapping("datas")
    public ResponseBo<ConventionSponsorVo> datas(@RequestBody SearchBean<ConventionSponsorDto> page) {
        List<ConventionSponsorVo> datas = conventionSponsorService.getConventionSponsors(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :赞助列表及是否剩余
     * @Param [conventionId]
     * <AUTHOR>
     */
    @ApiOperation(value = "赞助列表及是否剩余", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/峰会赞助商管理/赞助列表及是否剩余")
    @GetMapping("getSponsorshipConfig")
    public ResponseBo getSponsorshipConfig(@RequestParam Long conventionId) {
        List<Map<String, List<ConventionSponsorFeeVo>>> datas = conventionSponsorService.getSponsorshipConfig(conventionId);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :单个赞助对象验证是否售空
     * @Param [sponsorshipConfigId, initNum]
     * <AUTHOR>
     */
    @ApiOperation(value = "单个赞助对象验证是否售空", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/峰会赞助商管理/赞助列表及是否剩余")
    @GetMapping("soldOut")
    public ResponseBo soldOut(@RequestParam("sponsorshipConfigId") Long sponsorshipConfigId, @RequestParam("initNum") Integer initNum) {
        return new ResponseBo<>(conventionSponsorService.soldOut(sponsorshipConfigId, initNum));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :修改状态
     * @Param [sponsorshipConfigId, initNum]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改状态", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/峰会赞助商管理/赞助列表及是否剩余")
    @GetMapping("updateStatus")
    public ResponseBo updateStatus(@RequestParam Long id, @RequestParam Integer status) {
        conventionSponsorService.updateStatus(id, status);
        return ResponseBo.ok();
    }


    @ApiOperation(value = "导出赞助商名册Excel")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/峰会赞助商管理/导出赞助商名册Excel")
    @PostMapping("/exportSponsorExcel")
    @ResponseBody
    public void exportSponsorExcel(HttpServletResponse response, @RequestBody ConventionSponsorDto conventionSponsorDto) {
        conventionSponsorService.exportSponsorExcel(response, conventionSponsorDto);
    }

}
