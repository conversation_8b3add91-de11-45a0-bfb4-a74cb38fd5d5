package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dto.GoproRegistrationDto;
import com.get.salecenter.service.IConventionGoproRegistrationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/10 11:24
 */
@Api(tags = "活动报名管理")
@RestController
@RequestMapping("sale/goproRegistration")
public class ConventionGoproRegistrationController {

    @Resource
    private IConventionGoproRegistrationService goproRegistrationService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @ApiOperation(value = "活动报名列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动/活动报名列表")
    @PostMapping("datas")
    public ResponseBo<GoproRegistrationDto> datas(@RequestBody SearchBean<GoproRegistrationDto> page) {
        List<GoproRegistrationDto> datas = goproRegistrationService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @Description: 获取活动场数和座位情况
     * @Param:
     * @return:
     * @Author: Walker
     * @Date: 2022/5/10
     */
    @ApiOperation("获取活动场数和座位情况")
    @GetMapping("getSeatList")
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    public ResponseBo getSeatList() {
        List<Map<String,Object>> list = new ArrayList<>();
        //获取系统配置座位数
        List<Map<String,Object>> seatCountList = goproRegistrationService.getSeatCountList();
        int[] count = new int[]{Integer.valueOf(seatCountList.get(0).get("value1").toString()),
                Integer.valueOf(seatCountList.get(0).get("value2").toString()),
                Integer.valueOf(seatCountList.get(0).get("value3").toString()),
                Integer.valueOf(seatCountList.get(0).get("value4").toString()),0};
        String[] type = new String[]{"华山", "呼伦贝尔","蓬莱","九寨沟","TBC"};
        String[] time = new String[]{"5月24日-5月26日","6月14日-6月16日","9月13日-9月15日","10月12日-10月14日",""};
        for (int i = 0; i <= type.length - 1; i++) {
            Map<String,Object> map = new HashMap<>();
            int countSeatUsed = goproRegistrationService.getCountSeatUsed(type[i]);
            int countRemaining = count[i] - countSeatUsed;
            map.put("type",type[i]);
            map.put("time",time[i]);
            map.put("countRemaining",countRemaining);
            list.add(map);
        }
        return new ListResponseBo<>(list);
    }

    @ApiOperation("检测是否存在相同手机号")
    @GetMapping("getIsExistMobile")
    @VerifyLogin(IsVerify = false)
    public ResponseBo getIsExistMobile(@RequestParam("mobile") String mobile){
        List<GoproRegistrationDto> list = goproRegistrationService.getIsExistMobile(mobile);
        Map<String,Object> map = new HashMap<>();
        if(list.size()!=0){// 已有
            map.put("hasExist",true);
        }else {
            map.put("hasExist",false);
        }
        return new ResponseBo<>(map);
    }

    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/活动报名管理/新增")
    @PostMapping("add")
    @VerifyLogin(IsVerify = false)
    public ResponseBo add(@RequestBody  @Validated(GoproRegistrationDto.Add.class) GoproRegistrationDto goproRegistrationDto) {
        if ("TBC".equals(goproRegistrationDto.getRetreatTypeName())){
            return SaveResponseBo.ok(goproRegistrationService.addGopro(goproRegistrationDto));
        }
        //判断是否还有剩余座位
        int count = 0;
        List<Map<String,Object>> seatCountList = goproRegistrationService.getSeatCountList();
        Map<String, Object> objectMap = seatCountList.get(0);
        int countUsed = goproRegistrationService.getCountSeatUsed(goproRegistrationDto.getRetreatTypeName());
        if ("华山".equals(goproRegistrationDto.getRetreatTypeName())){
            count = Integer.parseInt(objectMap.get("value1").toString());
        }
        if("呼伦贝尔".equals(goproRegistrationDto.getRetreatTypeName())){
            count = Integer.parseInt(objectMap.get("value2").toString());
        }
        if ("蓬莱".equals(goproRegistrationDto.getRetreatTypeName())){
            count = Integer.parseInt(seatCountList.get(0).get("value3").toString());
        }
        if("九寨沟".equals(goproRegistrationDto.getRetreatTypeName())){
            count = Integer.parseInt(seatCountList.get(0).get("value4").toString());
        }
        int countRemaining = count - countUsed;
        if(countRemaining<=0){
            Map<String,Object> map = new HashMap<>();
            map.put("count",0);
            return new ResponseBo<>(map);
        }else{
            return SaveResponseBo.ok(goproRegistrationService.addGopro(goproRegistrationDto));
        }
    }
    @ApiOperation(value = "获取年份", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动报名管理/获取年份")
    @PostMapping("getYears")
    @VerifyLogin(IsVerify = false)
    public ListResponseBo<BaseSelectEntity> getYears(){
        return new ListResponseBo<>(goproRegistrationService.getYears());
    }

    @ApiOperation("校验是否还有位置")
    @GetMapping("getRemainingSeat")
    @VerifyLogin(IsVerify = false)
    public ResponseBo getRemainingSeat(@RequestParam("type") String type){
        //获取系统配置座位数
        List<Map<String,Object>> seatCountList = goproRegistrationService.getSeatCountList();
        int count = 0;
        Map<String,Object> map = new HashMap<>();
        int countUsed = goproRegistrationService.getCountSeatUsed(type);
        if ("华山".equals(type)){
            count = Integer.valueOf(seatCountList.get(0).get("value1").toString());
        }
        if("呼伦贝尔".equals(type)){
            count = Integer.valueOf(seatCountList.get(0).get("value2").toString());
        }
        if ("蓬莱".equals(type)){
            count = Integer.valueOf(seatCountList.get(0).get("value3").toString());
        }
        if("九寨沟".equals(type)){
            count = Integer.valueOf(seatCountList.get(0).get("value4").toString());
        }
        int countRemaining = count - countUsed;
        map.put("countRemaining",countRemaining);
        return new ResponseBo<>(map);
    }

    @ApiOperation(value = "导出活动报名列表")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "导出活动报名列表")
    @PostMapping("/exportGoPro")
    @ResponseBody
    public void exportGoPro(HttpServletResponse response, @RequestBody GoproRegistrationDto goproRegistrationDto) {
        goproRegistrationService.exportGoPro(response, goproRegistrationDto);
    }

    @ApiOperation(value = "更新", notes = "")
    @PostMapping("/update")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "更新")
    public ResponseBo update(@RequestBody @Validated(GoproRegistrationDto.Update.class) GoproRegistrationDto goproRegistrationDto) {
        if ("TBC".equals(goproRegistrationDto.getRetreatTypeName())){
            goproRegistrationService.update(goproRegistrationDto);
            return SaveResponseBo.ok();
        }
        //判断当前场次座位剩余数是否有剩余
        int count = 0;
        List<Map<String,Object>> seatCountList = goproRegistrationService.getSeatCountList();
        if ("华山".equals(goproRegistrationDto.getRetreatTypeName())){
            count = Integer.valueOf(seatCountList.get(0).get("value1").toString());
        }
        if("呼伦贝尔".equals(goproRegistrationDto.getRetreatTypeName())){
            count = Integer.valueOf(seatCountList.get(0).get("value2").toString());
        }
        if ("蓬莱".equals(goproRegistrationDto.getRetreatTypeName())){
            count = Integer.valueOf(seatCountList.get(0).get("value3").toString());
        }
        if("九寨沟".equals(goproRegistrationDto.getRetreatTypeName())){
            count = Integer.valueOf(seatCountList.get(0).get("value4").toString());
        }
        int countUsed = goproRegistrationService.getUsedByTypeAndId(goproRegistrationDto.getRetreatTypeName(), goproRegistrationDto.getId());
        //剩余座位数
        int countRemaining = count - countUsed;
        if(countRemaining <= 0){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insufficient_seat_balance"));
        }else {
            goproRegistrationService.update(goproRegistrationDto);
            return UpdateResponseBo.ok();
        }
    }

    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "根据id删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        goproRegistrationService.delete(id);
        return DeleteResponseBo.ok();
    }
}
