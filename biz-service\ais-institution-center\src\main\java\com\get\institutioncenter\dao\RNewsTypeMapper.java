package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.vo.RNewsTypeVo;
import com.get.institutioncenter.entity.RNewsType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface RNewsTypeMapper extends BaseMapper<RNewsType> {
    int insert(RNewsType record);

    int insertSelective(RNewsType record);

    List<RNewsTypeVo> getRNewsTypeByNewIds(@Param("fkNewsId") Long fkNewsId);

    List<RNewsTypeVo> getRNewsByNewIds(@Param("fkNewsIds") List<Long> fkNewsId,
                                       @Param("fkTableName") String fkTableName, @Param("fkTableId") Long fkTableId,
                                       @Param("fkNewsTypeId") Long fkNewsTypeId);

    RNewsTypeVo getRNewName(@Param("fkTableName") String fkTableName, @Param("tableId") Long tableId);


}