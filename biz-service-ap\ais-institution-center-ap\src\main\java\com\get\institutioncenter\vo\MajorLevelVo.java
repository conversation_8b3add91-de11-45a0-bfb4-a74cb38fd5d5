package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Sea
 * @create: 2020/7/31 12:11
 * @verison: 1.0
 * @description:
 */
@Data
@ApiModel(value = "专业等级返回类")
public class MajorLevelVo extends BaseEntity {
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;


    /**
     * 学校课程id
     */
    @ApiModelProperty(value = "学校课程id")
    private Long fkInstitutionCourseId;

    //============实体类MajorLevel===================
    private static final long serialVersionUID = 1L;
    /**
     * 等级名字
     */
    @ApiModelProperty(value = "等级名字")
    @Column(name = "level_name")
    private String levelName;
    /**
     * 等级名字
     */
    @ApiModelProperty(value = "等级名字中文")
    @Column(name = "level_name_chn")
    private String levelNameChn;

    @ApiModelProperty(value = "组别名字")
    @Column(name = "group_name")
    private String groupName;


    @ApiModelProperty(value = "组别中文名字")
    @Column(name = "group_name_chn")
    private String groupNameChn;

    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    @Column(name = "view_order")
    private Integer viewOrder;

}
