package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.SearchBean;
import com.get.common.utils.Assert;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.institutioncenter.dao.InstitutionCourseAppInfoMapper;
import com.get.institutioncenter.dao.InstitutionScholarshipMapper;
import com.get.institutioncenter.dao.InstitutionScholarshipMapper2;
import com.get.institutioncenter.dto.*;
import com.get.institutioncenter.dto.query.InstitutionScholarshipQueryDto;
import com.get.institutioncenter.entity.Institution;
import com.get.institutioncenter.entity.InstitutionCourseAppInfo;
import com.get.institutioncenter.entity.InstitutionScholarship;
import com.get.institutioncenter.entity.InstitutionScholarship2;
import com.get.institutioncenter.service.*;
import com.get.institutioncenter.vo.InstitutionScholarshipVo;
import com.get.institutioncenter.vo.InstitutionScholarshipVo2;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/6 12:08
 */
@Service
public class InstitutionScholarshipServiceImpl extends BaseServiceImpl<InstitutionScholarshipMapper, InstitutionScholarship> implements IInstitutionScholarshipService {
    @Resource
    private InstitutionScholarshipMapper institutionScholarshipMapper;
    @Resource
    private InstitutionScholarshipMapper2 institutionScholarshipMapper2;
    @Resource
    private UtilService<Object> utilService;

    @Resource
    private InstitutionCourseAppInfoMapper institutionCourseAppInfoMapper;

    @Resource
    @Lazy
    private IInstitutionCourseAppInfoService iInstitutionCourseAppInfoService;

    @Resource
    private IInstitutionService institutionService;
    @Resource
    private IInstitutionFacultyService iInstitutionFacultyService;

    @Resource
    private ICourseTypeGroupService courseTypeGroupService;
    @Resource
    private ICourseTypeService courseTypeService;
    @Resource
    private IInstitutionMajorService iInstitutionMajorService;
    @Resource
    private IInstitutionCourseService iInstitutionCourseService;
    @Resource
    private IAreaCountryService areaCountryService;
    @Resource
    private MediaAndAttachedServiceImpl mediaAndAttachedService;
    @Resource
    private IFileCenterClient iFileCenterClient;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addInstitutionScholarship(InstitutionScholarshipDto institutionScholarshipDto) {
        InstitutionScholarship institutionScholarship = BeanCopyUtils.objClone(institutionScholarshipDto, InstitutionScholarship::new);
        utilService.setCreateInfo(institutionScholarship);
        int i = institutionScholarshipMapper.insert(institutionScholarship);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        List<InstitutionCourseAppInfoDataProcessDto.CourseAppInfoResultMappingVo> appInfoResultMappingVos = institutionScholarshipDto.getCourseAppInfos();
        iInstitutionCourseAppInfoService.batchAdd(appInfoResultMappingVos,institutionScholarship.getId(),TableEnum.INSTITUTION_SCHOLARSHIP.key, institutionScholarshipDto.getEffectiveDate());
        return institutionScholarship.getId();
    }



    @Override
    public InstitutionScholarshipVo findInstitutionScholarshipById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionScholarshipVo institutionScholarshipVo = institutionScholarshipMapper.selectInfoById(id);
        if (GeneralTool.isEmpty(institutionScholarshipVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionCourseAppInfoDataProcessDto processVo = BeanCopyUtils.objClone(institutionScholarshipVo, InstitutionCourseAppInfoDataProcessDto::new);
        List<InstitutionCourseAppInfoDataProcessDto> list = new ArrayList<>(1);
        list.add(processVo);
        iInstitutionCourseAppInfoService.packageInfo(list,false,TableEnum.INSTITUTION_SCHOLARSHIP.key);
        Assert.notNull(processVo,"processVo is null");
        BeanUtils.copyProperties(processVo, institutionScholarshipVo);
        return institutionScholarshipVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        InstitutionScholarship institutionScholarship = institutionScholarshipMapper.selectById(id);
        if (GeneralTool.isEmpty(institutionScholarship)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        int i = institutionScholarshipMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        institutionCourseAppInfoMapper.delete(Wrappers.<InstitutionCourseAppInfo>lambdaQuery()
                .eq(InstitutionCourseAppInfo::getFkTableName,TableEnum.INSTITUTION_SCHOLARSHIP.key)
                .eq(InstitutionCourseAppInfo::getFkTableId,id));
    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public InstitutionScholarshipVo updateInstitutionScholarship(InstitutionScholarshipDto institutionScholarshipDto) {
        if (GeneralTool.isEmpty(institutionScholarshipDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        InstitutionScholarship institutionScholarship = institutionScholarshipMapper.selectById(institutionScholarshipDto.getId());
        if (GeneralTool.isEmpty(institutionScholarship)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionScholarship institutionScholarship1 = BeanCopyUtils.objClone(institutionScholarshipDto, InstitutionScholarship::new);
        utilService.updateUserInfoToEntity(institutionScholarship1);
        institutionScholarshipMapper.updateById(institutionScholarship1);
        iInstitutionCourseAppInfoService.batchUpdate(institutionScholarshipDto.getCourseAppInfos(),institutionScholarship1.getId(),TableEnum.INSTITUTION_SCHOLARSHIP.key, institutionScholarshipDto.getEffectiveDate());
        return findInstitutionScholarshipById(institutionScholarship1.getId());

    }

    /**
     * 结果处理
     * @param institutionScholarships
     * @param flag 是否要获取图片url
     */
    private void packProcess(List<InstitutionScholarshipVo> institutionScholarships, boolean flag){
        List<InstitutionCourseAppInfoDataProcessDto> processVos = BeanCopyUtils.copyListProperties(institutionScholarships, InstitutionCourseAppInfoDataProcessDto::new);
        iInstitutionCourseAppInfoService.packageInfo(processVos,flag,TableEnum.INSTITUTION_SCHOLARSHIP.key);
        Map<Long, InstitutionCourseAppInfoDataProcessDto> voMap = processVos.stream().collect(Collectors.toMap(InstitutionCourseAppInfoDataProcessDto::getId, Function.identity()));
        for (InstitutionScholarshipVo scholarship : institutionScholarships) {
            InstitutionCourseAppInfoDataProcessDto processVo = voMap.get(scholarship.getId());
            BeanUtils.copyProperties(processVo,scholarship);
        }
    }

    @Override
    public List<InstitutionScholarshipVo> datas(InstitutionScholarshipQueryDto data, SearchBean<InstitutionScholarshipQueryDto> page) {
        IPage<InstitutionScholarship> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())));
        List<Long> countryIds = SecureUtil.getCountryIds();
        List<InstitutionScholarshipVo> institutionScholarships = institutionScholarshipMapper.datas(pages, data,countryIds);
        page.setAll((int) pages.getTotal());
        //数据封装
        packProcess(institutionScholarships,false);
        return institutionScholarships;
    }


    @Override
    public List<InstitutionScholarshipVo2> getWcInstitutionScholarshipDatas(Long fkInstitutionId, Integer fkMajorLevelId) {
        LambdaQueryWrapper<InstitutionScholarship2> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(GeneralTool.isNotEmpty(fkInstitutionId), InstitutionScholarship2::getFkInstitutionId, fkInstitutionId).orderByAsc(InstitutionScholarship2::getFkInstitutionFacultyIdSub);
        List<InstitutionScholarship2> sourceDatas = institutionScholarshipMapper2.selectList(lambdaQueryWrapper);
        List<InstitutionScholarshipVo2> newDatas = BeanCopyUtils.copyListProperties(sourceDatas, InstitutionScholarshipVo2::new);
        if (GeneralTool.isEmpty(newDatas)) {
            return newDatas;
        }
        Iterator<InstitutionScholarshipVo2> iterator = newDatas.iterator();

        //过滤等级
        if (GeneralTool.isNotEmpty(fkMajorLevelId)) {
            while (iterator.hasNext()) {
                InstitutionScholarshipVo2 next = iterator.next();
                if (GeneralTool.isNotBlank(next.getFkMajorLevelIds())) {
                    String[] split = next.getFkMajorLevelIds().split(",");
                    long count = Arrays.asList(split).stream().filter(d -> d.equals(fkMajorLevelId.toString())).count();
                    if (count == 0) {
                        iterator.remove();
                    }
                }
            }
        }

        Iterator<InstitutionScholarshipVo2> iterator1 = newDatas.iterator();
        while (iterator1.hasNext()) {
            InstitutionScholarshipVo2 next = iterator1.next();
            boolean b = !next.getPublicLevel().contains("0");
            if (!b) {
                iterator1.remove();
            }
        }


        //ArrayList<InstitutionScholarshipVo> returnData = new ArrayList<>();
        //TODO 改过
//        Set<Long> institutionIds = newDatas.stream().map(InstitutionScholarship2::getFkInstitutionId).collect(Collectors.toSet());
//        Set<Long> fkFacultyIds = newDatas.stream().map(InstitutionScholarship2::getFkInstitutionFacultyId).collect(Collectors.toSet());
//        Set<Long> fkSubId = newDatas.stream().map(InstitutionScholarship2::getFkInstitutionFacultyIdSub).collect(Collectors.toSet());
        Set<Long> institutionIds = newDatas.stream().map(InstitutionScholarshipVo2::getFkInstitutionId).collect(Collectors.toSet());
        Set<Long> fkFacultyIds = newDatas.stream().map(InstitutionScholarshipVo2::getFkInstitutionFacultyId).collect(Collectors.toSet());
        Set<Long> fkSubId = newDatas.stream().map(InstitutionScholarshipVo2::getFkInstitutionFacultyIdSub).collect(Collectors.toSet());

        fkSubId.addAll(fkFacultyIds);
        Map<Long, String> facultyNameByIds = iInstitutionFacultyService.getInstitutionFacultyNameByIds(fkSubId);
       /* //等级多选
        Set<String> collect = newDatas.stream().filter(d -> GeneralTool.isNotBlank(d.getFkMajorLevelIds())).map(InstitutionScholarship::getFkMajorLevelIds).collect(Collectors.toSet());
        Set<Long> fkMajorIds = new HashSet<>();
        for (String s : collect) {
            String[] split = s.split(",");
            fkMajorIds.addAll(Arrays.asList(split).stream().map(d -> Long.valueOf(d)).collect(Collectors.toSet()));
        }
        Map<Long, String> majorLevelNameByIds = iMajorLevelService.getMajorLevelNameByIds(fkMajorIds);*/
        Map<Long, Institution> institutionNamesByIds = institutionService.getInstitutionByIds(institutionIds);

        StringBuffer stringBuffer = new StringBuffer();
        newDatas.stream().forEach(d -> {
            Institution institution = institutionNamesByIds.get(d.getFkInstitutionId());
            Optional.ofNullable(d.getFkInstitutionFacultyId()).ofNullable(facultyNameByIds).ifPresent(dd -> d.setInstitutionFacultyName(dd.get(d.getFkInstitutionFacultyId())));
            Optional.ofNullable(d.getFkInstitutionFacultyIdSub()).ofNullable(facultyNameByIds).ifPresent(dd -> d.setFkInstitutionFacultyIdSubName(dd.get(d.getFkInstitutionFacultyIdSub())));
            d.setFkInstitutionNameEn(institution.getName());
            Optional.ofNullable(institution.getNameChn()).ifPresent(dd -> d.setFkInstitutionNameZh(dd));
            //公开对象多选
            stringBuffer.delete(0, stringBuffer.length());
            Arrays.stream(d.getPublicLevel().split(",")).forEach(dd -> stringBuffer.append(ProjectExtraEnum.getValueByKey(Integer.valueOf(dd), ProjectExtraEnum.PUBLIC_OBJECTS)).append(","));
            String substring = stringBuffer.toString().substring(0, stringBuffer.length() - 1);
            d.setPublicLevelName(substring);
            stringBuffer.delete(0, stringBuffer.length());

        });


        return newDatas;
    }

    @Override
    public List<InstitutionScholarshipVo2> getWcInstitutionScholarshipList(InstitutionScholarshipDto2 data, SearchBean<InstitutionScholarshipDto2> page) {

        IPage<InstitutionScholarshipVo2> ipage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<InstitutionScholarshipVo2> wcInstitutionScholarshipList = institutionScholarshipMapper2.getWcInstitutionScholarshipList(ipage, data.getSchoolName(), data.getFkCountryId());
        page.setAll((int) ipage.getTotal());
        page.setCurrentResult((int) ipage.getSize());

        if (GeneralTool.isEmpty(wcInstitutionScholarshipList)) {
            return wcInstitutionScholarshipList;
        }

        Iterator<InstitutionScholarshipVo2> iterator = wcInstitutionScholarshipList.iterator();
        while (iterator.hasNext()) {
            InstitutionScholarshipVo2 next = iterator.next();
            boolean b = !next.getPublicLevel().contains("0");
            if (!b) {
                iterator.remove();
            }
        }
        if (GeneralTool.isEmpty(wcInstitutionScholarshipList)) {
            return wcInstitutionScholarshipList;
        }
        //TODO 改过
        //Set<Long> schoolId = wcInstitutionScholarshipList.stream().map(InstitutionScholarship2::getFkInstitutionId).collect(Collectors.toSet());
        Set<Long> schoolId = wcInstitutionScholarshipList.stream().map(InstitutionScholarshipVo2::getFkInstitutionId).collect(Collectors.toSet());
        Map<Long, Institution> institutionNamesByIds = institutionService.getInstitutionByIds(schoolId);
        //获取文件guid
        List<MediaAndAttachedVo> guIdByIds = mediaAndAttachedService.getGuIdByIds(schoolId, "m_institution", "institution_cover");
        Map<Long, String> url = new HashMap<>();
        if (GeneralTool.isNotEmpty(guIdByIds)) {
            //获取文件具体url
            List<String> collect = guIdByIds.stream().map(MediaAndAttachedVo::getFkFileGuid).collect(Collectors.toList());
            Result<Map<String, String>> filePathByGuids = iFileCenterClient.getFilePathByGuids(collect);
            if (filePathByGuids.isSuccess() && GeneralTool.isNotEmpty(filePathByGuids.getData())) {
                guIdByIds.stream().forEach(d -> url.put(d.getFkTableId(), filePathByGuids.getData().get(d.getFkFileGuid())));
            }
        }
        List<InstitutionScholarshipVo2> institutionScholarshipDtos = BeanCopyUtils.copyListProperties(wcInstitutionScholarshipList, InstitutionScholarshipVo2::new);
        institutionScholarshipDtos.stream().forEach(d -> {
            d.setCoversUrl(url.containsKey(d.getFkInstitutionId()) ? url.get(d.getFkInstitutionId()) : null);
            Institution institution = institutionNamesByIds.get(d.getFkInstitutionId());

            if (GeneralTool.isBlank(institution.getNameDisplay())) {
                d.setFkInstitutionNameEn(institution.getName());
                if (GeneralTool.isNotBlank(institution.getNameChn())) {
                    d.setFkInstitutionNameZh(institution.getNameChn());
                }
            } else {
                d.setFkInstitutionNameEn(institution.getNameDisplay());
            }


        });


        return institutionScholarshipDtos;
    }

    @Override
    public InstitutionScholarshipVo getIsOtherModule(Long fkInstitutionId) {
        return institutionScholarshipMapper.getIsOtherModule(fkInstitutionId);
    }

    /**
     * Author Cream
     * Description : //优先匹配查询
     * Date 2022/11/22 10:11
     * Params:
     * Return
     */
    @Override
    public List<InstitutionScholarshipVo> priorityMatchingQuery(WeScholarshipAppDto weScholarshipAppDto) {
        List<InstitutionCourseAppInfoDataProcessDto> processVo = iInstitutionCourseAppInfoService.testPriority(weScholarshipAppDto, TableEnum.INSTITUTION_SCHOLARSHIP.key);
        if (GeneralTool.isEmpty(processVo)) {
            return null;
        }
        List<Long> ids = processVo.stream().map(InstitutionCourseAppInfoDataProcessDto::getId).collect(Collectors.toList());
        List<InstitutionScholarshipVo> institutionScholarshipVos = institutionScholarshipMapper.selectInfoByIds(ids);
        Map<Long, InstitutionCourseAppInfoDataProcessDto> collect = processVo.stream().collect(Collectors.toMap(InstitutionCourseAppInfoDataProcessDto::getId, Function.identity()));
        for (InstitutionScholarshipVo scholarshipDto : institutionScholarshipVos) {
            BeanUtils.copyProperties(collect.get(scholarshipDto.getId()),scholarshipDto);
        }
        institutionScholarshipVos.sort(Comparator.comparing(InstitutionScholarshipVo::getGmtPriorityTime).reversed());
        return institutionScholarshipVos;
    }

//    /**
//     * 获取最优先匹配信息
//     * @param list
//     * @return
//     */
//    private InstitutionScholarshipVo priorityGet(List<InstitutionScholarshipVo> list,WeScholarshipAppDto weScholarshipAppVo){
//        for (InstitutionScholarshipVo vo : list) {
//            if (Arrays.stream(vo.getFk().split(",")).anyMatch(d->d.equals(TableEnum.INSTITUTION_COURSE.key))) {
//                return vo;
//            }
//            if (vo.getFk().equals(vo.getFc())) {
//                return vo;
//            }
//            vo.setPriority(vo.getFc().split(",").length);
//        }
//        list.sort(Comparator.comparing(InstitutionScholarshipVo::getPriority));
//        return list.get(0);
//    }

    /**
     * 获取目标对象信息
     * @param scholarshipTargetDto
     * @return
     */
    @Override
    public List<BaseSelectEntity> getSelectedByTarget(ScholarshipTargetDto scholarshipTargetDto) {
        String targetName = scholarshipTargetDto.getTargetName();
        String keyword = scholarshipTargetDto.getKeyword();
        String val = TableEnum.getValue(targetName, TableEnum.S_P);
        if (StringUtils.isBlank(val)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("illegal_type"));
        }
        if (TableEnum.INSTITUTION_COUNTRY.key.equals(targetName)) {
            return areaCountryService.getAreaCountryListByKeyword(keyword);
        }else if (TableEnum.INSTITUTION.key.equals(targetName)){
            return institutionService.getInstitutionListByKeyword(keyword, scholarshipTargetDto.getCountryId());
        }else if (TableEnum.INSTITUTION_FACULTY.key.equals(targetName)){
            return iInstitutionFacultyService.getInstitutionFacultyList(keyword, scholarshipTargetDto.getInstitutionId());
        }else if (TableEnum.INSTITUTION_COURSE_TYPE_GROUP.key.equals(targetName)){
            return courseTypeGroupService.getCourseGroupList(keyword);
        }else if (TableEnum.INSTITUTION_COURSE_TYPE.key.equals(targetName)){
            return courseTypeService.getCourseTypeList(keyword);
        }else if (TableEnum.INSTITUTION_MAJOR_LEVEL.key.equals(targetName)){
            return iInstitutionMajorService.getInstitutionMajorSelect(keyword);
        }else if (TableEnum.INSTITUTION_COURSE.key.equals(targetName)){
            return iInstitutionCourseService.getCourseSelected(keyword, scholarshipTargetDto.getInstitutionId());
        }
        return Collections.emptyList();
    }


}
