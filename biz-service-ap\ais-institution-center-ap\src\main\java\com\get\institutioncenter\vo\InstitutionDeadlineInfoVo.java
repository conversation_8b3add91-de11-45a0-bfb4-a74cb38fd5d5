package com.get.institutioncenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import com.get.institutioncenter.dto.InstitutionCourseAppInfoDataProcessDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/6 15:35
 */
@Data
public class InstitutionDeadlineInfoVo extends BaseEntity {

    /**
     * 目标对象id
     */
    @ApiModelProperty(value = "目标对象id")
    private Long fkTableId;

    @ApiModelProperty("公开对象名称")
    private String publicLevelName;

    @ApiModelProperty("学校封面url")
    private String coversUrl;

    @ApiModelProperty("最新创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date newGmtCreate;

    @ApiModelProperty("目标对象信息")
    private List<InstitutionCourseAppInfoDataProcessDto.CourseAppInfoResultMappingVo> courseAppInfoResultMappingVos;

    @ApiModelProperty(value = "优先级匹配用的key")
    private String fk;

    @ApiModelProperty(value = "优先级匹配用的key")
    private String fc;

    private Integer priority;

    @ApiModelProperty("关系绑定时间")
    private Date gmtPriorityTime;

    @ApiModelProperty(value = "生效时间")
    private String effectiveDate;

    @ApiModelProperty(value = "学校名称")
    private String fkInstitutionName;

    //==============实体类InstitutionDeadlineInfo===================
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @Column(name = "title")
    private String title;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Column(name = "description")
    private String description;

    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @Column(name = "public_level")
    private String publicLevel;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    @Column(name = "publish_time")
    private Date publishTime;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;

    private static final long serialVersionUID = 1L;
}
