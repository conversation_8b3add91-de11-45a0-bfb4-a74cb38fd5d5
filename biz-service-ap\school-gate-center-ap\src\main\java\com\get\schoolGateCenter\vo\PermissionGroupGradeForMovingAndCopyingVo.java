package com.get.schoolGateCenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: Hardy
 * @create: 2022/11/11 15:56
 * @verison: 1.0
 * @description:
 */
@Data
public class PermissionGroupGradeForMovingAndCopyingVo {

    @NotNull(message = "原groupId不能为空", groups = {Update.class,Add.class})
    @ApiModelProperty("原groupId")
    private Long sourceGroupId;

    @NotNull(message = "原gradeId不能为空", groups = {Update.class,Add.class})
    @ApiModelProperty("原gradeId")
    private Long sourceGradeId;

    @NotNull(message = "目标groupId不能为空", groups = {Update.class,Add.class})
    @ApiModelProperty("目标groupId")
    private Long targetGroupId;

    @NotNull(message = "目标gradeId不能为空", groups = {Update.class,Add.class})
    @ApiModelProperty("目标gradeId")
    private Long targetGradeId;

    public interface Update {
    }

    public interface Add {
    }
}
