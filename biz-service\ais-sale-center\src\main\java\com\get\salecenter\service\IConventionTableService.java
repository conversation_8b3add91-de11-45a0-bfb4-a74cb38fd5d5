package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.salecenter.vo.ConventionPersonVo;
import com.get.salecenter.vo.ConventionTableVo;
import com.get.salecenter.vo.TableCharacterSubtotal;
import com.get.salecenter.vo.TableChartVo;
import com.get.salecenter.dto.ConventionPersonDto;
import com.get.salecenter.dto.ConventionTableDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/27 11:32
 * @verison: 1.0
 * @description:
 */
public interface IConventionTableService {

    /**
     * 详情
     *
     * @param id
     * @return
     */
    ConventionTableVo findConventionTableById(Long id);

    /**
     * 批量新增
     *
     * @param conventionTableDto
     */
    void batchAdd(ConventionTableDto conventionTableDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 修改
     *
     * @param conventionTableDto
     * @return
     */
    ConventionTableVo updateConventionTable(ConventionTableDto conventionTableDto);

    /**
     * 列表
     *
     * @param conventionTableDto
     * @return
     */
    List<ConventionTableVo> getConventionTables(ConventionTableDto conventionTableDto);

    /**
     * 导出晚宴桌台安排
     * @param conventionTableDto
     */
    void exportConventionTableExcel(HttpServletResponse response , ConventionTableDto conventionTableDto);

    /**
     * 设置为vip桌
     *
     * @param id
     * @param isVip
     * @return
     */
    Integer setVip(Long id, Integer isVip);

    /**
     * 新增桌台座位数
     *
     * @param id
     * @param seatCount
     */
    void seatCountUp(Long id, Integer seatCount);

    /**
     * 减少桌台座位数
     *
     * @param id
     * @param seatCount
     */
    void seatCountDown(Long id, Integer seatCount);

    /**
     * @return java.util.List<com.get.salecenter.vo.TableChartVo>
     * @Description :该峰会和类型下所有桌台信息
     * @Param [conventionId, tableType]
     * <AUTHOR>
     */
    List<TableChartVo> getTableChartList(Long conventionId, String tableType);

    /**
     * @return java.util.List<java.lang.String>
     * @Description :获取桌台编号集合
     * @Param [conventionId]
     * <AUTHOR>
     */
    List<String> getTableNums(Long conventionId, String tableType);

    /**
     * @return java.util.List<com.get.salecenter.vo.ConventionPersonVo>
     * @Description :桌台未安排人员详细
     * @Param [conventionPersonDto, page, type]
     * <AUTHOR>
     */
    List<ConventionPersonVo> getNotArrangedPersonList(ConventionPersonDto conventionPersonDto, Page page, String type);
    /**
     * 获取桌子角色小计
     * @return
     */
    List<TableCharacterSubtotal> getTableCharacterSubtotal(ConventionTableDto conventionTableVo);
}
