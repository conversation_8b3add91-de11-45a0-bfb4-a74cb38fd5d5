package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.AESConstant;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.AESUtils;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.ConventionAwardCodeMapper;
import com.get.salecenter.dao.sale.ConventionAwardMapper;
import com.get.salecenter.dao.sale.MediaAndAttachedMapper;
import com.get.salecenter.vo.ConventionAwardVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.vo.RecordAwardVo;
import com.get.salecenter.entity.ConventionAward;
import com.get.salecenter.entity.ConventionAwardCode;
import com.get.salecenter.entity.SaleMediaAndAttached;
import com.get.salecenter.service.IConventionAwardService;
import com.get.salecenter.service.IConventionService;
import com.get.salecenter.service.IMediaAndAttachedService;
import com.get.salecenter.utils.WxUtils;
import com.get.salecenter.dto.ConventionAwardDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2021/9/10
 * @TIME: 12:09
 * @Description:
 **/
@Service
public class ConventionAwardServiceImpl  extends GetServiceImpl<ConventionAwardMapper, ConventionAward>  implements IConventionAwardService {
    @Resource
    private ConventionAwardMapper conventionAwardMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private MediaAndAttachedMapper mediaAndAttachedMapper;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private IConventionService conventionService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ConventionAwardCodeMapper conventionAwardCodeMapper;

    @Override
    public ConventionAwardVo findConventionAwardById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ConventionAward conventionAward = conventionAwardMapper.selectById(id);
        if (GeneralTool.isEmpty(conventionAward)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("not_find_information"));
        }
        ConventionAwardVo conventionAwardVo = BeanCopyUtils.objClone(conventionAward, ConventionAwardVo::new);
        if (GeneralTool.isNotEmpty(conventionAwardVo.getFkConventionId())) {
            conventionAwardVo.setConventionName(conventionService.getConventionNameById(conventionAwardVo.getFkConventionId()));
        }
        //设置属性
        conventionAwardVo.setMediaAndAttachedDto(getIconImg(id));
        return conventionAwardVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAdd(List<ConventionAwardDto> conventionAwardDtos) {
        for (ConventionAwardDto conventionAwardDto : conventionAwardDtos) {
            ConventionAward conventionAward = BeanCopyUtils.objClone(conventionAwardDto, ConventionAward::new);
            conventionAward.setViewOrder(conventionAwardMapper.getMaxViewOrder());
            utilService.updateUserInfoToEntity(conventionAward);
            conventionAwardMapper.insert(conventionAward);
            //保存图片
            saveMediaAndAttached(conventionAwardDto.getMediaAttachedVos(), FileTypeEnum.AWARD_PIC.key, TableEnum.SALE_CONVENTION_AWARD.key, conventionAward.getId());
        }
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        conventionAwardMapper.deleteById(id);
    }

    @Override
    public ConventionAwardVo updateConventionAward(ConventionAwardDto conventionAwardDto) {
        if (conventionAwardDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ConventionAward conventionAward = BeanCopyUtils.objClone(conventionAwardDto, ConventionAward::new);
        utilService.updateUserInfoToEntity(conventionAward);
        int i = conventionAwardMapper.updateById(conventionAward);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        //删除旧图片
        attachedService.deleteMediaAndAttached(TableEnum.SALE_CONVENTION_AWARD.key, conventionAwardDto.getId());
        //保存图片
        saveMediaAndAttached(conventionAwardDto.getMediaAttachedVos(), FileTypeEnum.AWARD_PIC.key, TableEnum.SALE_CONVENTION_AWARD.key, conventionAwardDto.getId());
        return findConventionAwardById(conventionAwardDto.getId());
    }

    @Override
    public List<ConventionAwardVo> getConventionAwards(ConventionAwardDto conventionAwardDto) {
        List<String> roles = new ArrayList<>();
        if (GeneralTool.isNotEmpty(conventionAwardDto.getGetRole())) {
            String[] as = conventionAwardDto.getGetRole().split(",");
            for (int i = 0; i < as.length; i++) {
                roles.add(as[i]);
            }
        }
        //获取分页数据
        List<ConventionAwardVo> conventionAwardVos = conventionAwardMapper.getConventionAwards(conventionAwardDto.getAwardName(), roles, conventionAwardDto.getFkConventionId());
        for (ConventionAwardVo conventionAwardVo : conventionAwardVos) {
            if (GeneralTool.isNotEmpty(conventionAwardVo.getFkConventionId())) {
                conventionAwardVo.setConventionName(conventionService.getConventionNameById(conventionAwardVo.getFkConventionId()));
            }
            if (GeneralTool.isNotEmpty(conventionAwardVo.getGetRole())) {
                String[] roleIds = conventionAwardVo.getGetRole().split(",");
                StringBuffer sb = new StringBuffer();
                for (int i = 0; i < roleIds.length; i++) {
                    sb.append(ProjectExtraEnum.getValueByKey(Integer.valueOf(roleIds[i]), ProjectExtraEnum.CONVENTION_PERSON_TYPE)).append(" ");
                }
                conventionAwardVo.setRoleName(sb.toString());
            }
            //设置属性
            conventionAwardVo.setMediaAndAttachedDto(getIconImg(conventionAwardVo.getId()));
        }
        return conventionAwardVos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<ConventionAwardDto> conventionAwardDtos) {
        if (GeneralTool.isEmpty(conventionAwardDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ConventionAward ro = BeanCopyUtils.objClone(conventionAwardDtos.get(0), ConventionAward::new);
        Integer oneorder = ro.getViewOrder();
        ConventionAward rt = BeanCopyUtils.objClone(conventionAwardDtos.get(1), ConventionAward::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        conventionAwardMapper.updateById(ro);
        conventionAwardMapper.updateById(rt);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<MediaAndAttachedVo> addConventionAwardMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        if (mediaAttachedVos.get(0).getTypeKey().equals(FileTypeEnum.AWARD_PIC.key)) {
//            Example example = new Example(MediaAndAttached.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andEqualTo("fkTableName", mediaAttachedVos.get(0).getFkTableName());
//            criteria.andEqualTo("fkTableId", mediaAttachedVos.get(0).getFkTableId());
//            criteria.andEqualTo("typeKey", mediaAttachedVos.get(0).getTypeKey());
//            mediaAndAttachedMapper.deleteByExample(example);

            mediaAndAttachedMapper.delete(Wrappers.<SaleMediaAndAttached>lambdaQuery()
                    .eq(SaleMediaAndAttached::getFkTableName, TableEnum.SALE_CONVENTION_AWARD.key)
                    .eq(SaleMediaAndAttached::getFkTableId, mediaAttachedVos.get(0).getFkTableId())
                    .eq(SaleMediaAndAttached::getTypeKey, mediaAttachedVos.get(0).getTypeKey()));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAttachedVo : mediaAttachedVos) {
            //设置插入的表
            mediaAttachedVo.setFkTableName(TableEnum.SALE_CONVENTION_AWARD.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAttachedVo));
        }
        return mediaAndAttachedVos;
    }

    @Override
    public List<Map<String, Object>> findRolesType() {
        return ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.CONVENTION_PERSON_TYPE);
    }


    @Override
    public void getShareCode(HttpServletResponse response) throws Exception {
        ConfigVo appidconfig = new ConfigVo();
        ConfigVo appsecretconfig = new ConfigVo();
        Result<ConfigVo> appIdConfigResult = permissionCenterClient.getConfigByKey(ProjectKeyEnum.APP_ID.value);
        if (appIdConfigResult.isSuccess() && appIdConfigResult.getData() != null) {
            appidconfig = appIdConfigResult.getData();
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("not_yet_configured") +"appid");
        }
        Result<ConfigVo> appsecretConfigResult = permissionCenterClient.getConfigByKey(ProjectKeyEnum.APP_SECRET.value);
        if (appsecretConfigResult.isSuccess() && appsecretConfigResult.getData() != null) {
            appsecretconfig = appsecretConfigResult.getData();
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("not_yet_configured") + "appsecret");
        }
        if (GeneralTool.isNotEmpty(appidconfig) && GeneralTool.isNotEmpty(appsecretconfig)) {
            WxUtils.getShareCode(response, AESUtils.Decrypt(appidconfig.getValue1(), AESConstant.AESKEY), AESUtils.Decrypt(appsecretconfig.getValue1(), AESConstant.AESKEY));
        }
    }

    /**
     * @Description: 根据名称模糊搜索奖品ids
     * @Author: Jerry
     * @Date:16:12 2021/9/15
     */
    @Override
    public Set<Long> getConventionAwardsIdsByConventionAwardsName(String conventionAwardsName) {
//        Example example = new Example(ConventionAward.class);
//        example.createCriteria().andLike("awardName","%"+conventionAwardsName+"%");
//        List<ConventionAward> conventionAwards = conventionAwardMapper.selectByExample(example);

        List<ConventionAward> conventionAwards = conventionAwardMapper.selectList(Wrappers.<ConventionAward>lambdaQuery().like(ConventionAward::getAwardName, conventionAwardsName));
        if (GeneralTool.isEmpty(conventionAwards)) {
            return null;
        }
        return conventionAwards.stream().map(ConventionAward::getId).collect(Collectors.toSet());
    }

    /**
     * @Description: 根据奖品ids获取名称
     * @Author: Jerry
     * @Date:16:51 2021/9/15
     */
    @Override
    public Map<Long, String> getConventionAwardsNameByConventionAwardsIds(Set<Long> conventionAwardsIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(conventionAwardsIds)) {
            return map;
        }
//        Example example = new Example(ConventionAward.class);
//        example.createCriteria().andIn("id",conventionAwardsIds);
//        List<ConventionAward> conventionAwards = conventionAwardMapper.selectByExample(example);

        List<ConventionAward> conventionAwards = conventionAwardMapper.selectList(Wrappers.<ConventionAward>lambdaQuery().in(ConventionAward::getId, conventionAwardsIds));
        if (GeneralTool.isEmpty(conventionAwards)) {
            return map;
        }
        for (ConventionAward conventionAward : conventionAwards) {
            map.put(conventionAward.getId(), conventionAward.getAwardName());
        }
        return map;
    }

    @Override
    public List<RecordAwardVo> listAwardsAndTickets(Long id) {
        List rewardList = new ArrayList();
        String award_name = "";
        String status = "";
        //所有奖品
        List<ConventionAwardVo> listAwards = conventionAwardMapper.listAward(award_name, id);
        if (listAwards.size() > 0) {
            for (int i = 0; i <= listAwards.size() - 1; i++) {
                Long award_id = listAwards.get(i).getId();
                MediaAndAttachedVo mediaAndAttachedVo = getIconImg(award_id);
                RecordAwardVo recordAward = new RecordAwardVo();
                recordAward.setAward_id(listAwards.get(i).getId());
                if (GeneralTool.isNotEmpty(mediaAndAttachedVo)) {
                    recordAward.setPicture(mediaAndAttachedVo.getFileNameOrc());
                    recordAward.setPicture_url(mediaAndAttachedVo.getFileKey());
                }
                recordAward.setReward_name(listAwards.get(i).getAwardName());
                recordAward.setCount(listAwards.get(i).getAwardCount());
                recordAward.setDionationor(listAwards.get(i).getProviderName());
               /* if(GeneralTool.isNotEmpty(listAwards.get(i).getGetRole())){
                    String[] roleIds  = listAwards.get(i).getGetRole().split(",");
                    StringBuffer sb = new StringBuffer();
                    for(int j=0;j< roleIds.length;j++){
                        sb.append(ProjectExtraEnum.getValueByKey(Integer.valueOf(roleIds[j]), ProjectExtraEnum.CONVENTION_PERSON_TYPE)).append(" ");
                    }
                    recordAward.setGrole(sb.toString());
                }*/
                recordAward.setGrole(listAwards.get(i).getGetRole());
                recordAward.setRestcount(listAwards.get(i).getRestcount());
                recordAward.setAward_origin_zh(listAwards.get(i).getProviderNameSub());
                if (GeneralTool.isNotEmpty(listAwards.get(i).getGetFkConventionPersonId())) {
                    List<ConventionAwardCode> codes = new ArrayList<>();
                 /*   String[] split =listAwards.get(i).getGetFkConventionPersonId().split(",");
                    for(String s:split){
                        if(GeneralTool.isNotEmpty(s)){
                            codes.addAll(conventionAwardCodeMapper.getCodeByAwardAndType(Long.valueOf(s)));
                        }
                    }*/
                    recordAward.setCodes(codes);
                }
                //根据权限和奖品id获取对应的购买奖券列表
                String agrole = listAwards.get(i).getGetRole();
                //1校代,2代理,3GEA,4嘉宾 排除已中奖的奖券
                //status = "1";//未使用
                //List listTicket = lotteryService.getTicketsByGroleAndAwardId(agrole, award_id, status);
                status = "2";//已使用
                List listTichkeUsed = conventionAwardCodeMapper.getTicketsByGroleAndAwardIdUsed(agrole, award_id, status, id);
                recordAward.setTicketsused(listTichkeUsed);
                //recordAward.set("tickets", listTicket);
                rewardList.add(recordAward);
            }
        }
        return rewardList;
    }

    /**
     * 排序（拖拽）
     *
     * @Date 14:23 2024/3/12
     * <AUTHOR>
     */
    @Override
    public void dragMovingOrder(Integer end, Integer start) {
        LambdaQueryWrapper<ConventionAward> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (end>start){
            lambdaQueryWrapper.between(ConventionAward::getViewOrder,start,end).orderByDesc(ConventionAward::getViewOrder);
        }else {
            lambdaQueryWrapper.between(ConventionAward::getViewOrder,end,start).orderByDesc(ConventionAward::getViewOrder);

        }
        List<ConventionAward> staffCommissionPolicies = list(lambdaQueryWrapper);

        //从下上移 列表倒序排序
        List<ConventionAward> updateList = Lists.newArrayList();
        if (end>start){
            int finalEnd = end;
            List<ConventionAward> sortedList = Lists.newArrayList();
            ConventionAward policy = staffCommissionPolicies.get(staffCommissionPolicies.size() - 1);
            sortedList.add(policy);
            staffCommissionPolicies.remove(staffCommissionPolicies.size() - 1);
            sortedList.addAll(staffCommissionPolicies);
            for (ConventionAward staffCommissionPolicy : sortedList) {
                staffCommissionPolicy.setViewOrder(finalEnd);
                finalEnd--;
            }
            updateList.addAll(sortedList);
        }else {
            int finalStart = start;
            List<ConventionAward> sortedList = Lists.newArrayList();
            ConventionAward policy = staffCommissionPolicies.get(0);
            staffCommissionPolicies.remove(0);
            sortedList.addAll(staffCommissionPolicies);
            sortedList.add(policy);
            for (ConventionAward staffCommissionPolicy : sortedList) {
                staffCommissionPolicy.setViewOrder(finalStart);
                finalStart--;
            }
            updateList.addAll(sortedList);
        }

        if (GeneralTool.isNotEmpty(updateList)){
            boolean batch = updateBatchById(updateList);
            if (!batch){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
    }

    //获取图片
    private MediaAndAttachedVo getIconImg(Long id) {
        MediaAndAttachedDto attachedVo = new MediaAndAttachedDto();
        attachedVo.setFkTableName(TableEnum.SALE_CONVENTION_AWARD.key);
        attachedVo.setFkTableId(id);
        attachedVo.setTypeKey(FileTypeEnum.AWARD_PIC.key);

        List<MediaAndAttachedVo> headIcon = attachedService.getMediaAndAttachedDto(attachedVo);
        if (GeneralTool.isNotEmpty(headIcon)) {
            return headIcon.get(0);
        }
        return null;
    }

    /**
     * @Description:保存图片
     * @Param
     * @Date 12:18 2021/5/13
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveMediaAndAttached(List<MediaAndAttachedDto> topLogoMediaAttachedVos, String typeKey, String fkTableName, Long fkTableId) {
        if (GeneralTool.isNotEmpty(topLogoMediaAttachedVos)) {
            for (MediaAndAttachedDto mediaAndAttachedDto : topLogoMediaAttachedVos) {
                mediaAndAttachedDto.setTypeKey(typeKey);
                mediaAndAttachedDto.setFkTableId(fkTableId);
                mediaAndAttachedDto.setFkTableName(fkTableName);
                attachedService.addMediaAndAttached(mediaAndAttachedDto);
            }
        }
    }
}
