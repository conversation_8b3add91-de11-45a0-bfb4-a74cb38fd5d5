package com.get.salecenter.controller;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.dto.KpiPlanStaffLabelDto;
import com.get.salecenter.service.KpiPlanStaffLabelService;
import com.get.salecenter.vo.KpiPlanStaffLabelVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "KPI方案考核人员标签管理")
@RestController
@RequestMapping("sale/kpiPlanStaffLabel")
public class KpiPlanStaffLabelController {

    @Resource
    private KpiPlanStaffLabelService kpiPlanStaffLabelService;

    @ApiOperation(value = "批量新增", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/KPI方案考核人员标签管理/批量新增")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(KpiPlanStaffLabelDto.Add.class) KpiPlanStaffLabelDto vo) {
        kpiPlanStaffLabelService.batchAdd(vo);
        return SaveResponseBo.ok();
    }

    @ApiOperation(value = "删除", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/KPI方案考核人员标签管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        kpiPlanStaffLabelService.delete(id);
        return DeleteResponseBo.ok();
    }

}
