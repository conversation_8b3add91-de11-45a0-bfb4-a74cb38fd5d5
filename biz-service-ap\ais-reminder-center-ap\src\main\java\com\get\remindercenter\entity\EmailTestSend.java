package com.get.remindercenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@Data
@TableName("m_email_test_send")
@ApiModel(value="EmailTestSend对象", description="")
public class EmailTestSend extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    private String email;

    @ApiModelProperty(value = "0未发送1已发送2已重发")
    private Integer status;

}
