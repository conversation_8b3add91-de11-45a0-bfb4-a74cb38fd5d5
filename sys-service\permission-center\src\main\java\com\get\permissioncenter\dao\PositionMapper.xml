<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.permissioncenter.dao.PositionMapper">

    <insert id="insertSelective" parameterType="com.get.permissioncenter.entity.Position" keyProperty="id" useGeneratedKeys="true">
        insert into m_position
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>

            <if test="fkCompanyId != null">
                fk_company_id,
            </if>
            <if test="fkDepartmentId != null">
                fk_department_id,
            </if>
            <if test="num != null">
                num,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="posLevel != null">
                pos_level,
            </if>
            <if test="viewOrder != null">
                view_order,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkCompanyId != null">
                #{fkCompanyId,jdbcType=BIGINT},
            </if>
            <if test="fkDepartmentId != null">
                #{fkDepartmentId,jdbcType=BIGINT},
            </if>
            <if test="num != null">
                #{num,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="posLevel != null">
                #{posLevel,jdbcType=VARCHAR},
            </if>
            <if test="viewOrder != null">
                #{viewOrder,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="getMaxViewOrder" resultType="java.lang.Integer">
      select
        ifnull(max(view_order)+1,1) view_order
      from
      m_position
    </select>

    <select id="getPositionSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
      select
       id,num,name
      from
       m_position
      where
       fk_company_id = #{companyId} and fk_department_id=#{departmentId}
      ORDER BY
       view_order asc
    </select>

    <select id="isExistByDepartment" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id FROM  m_position where fk_department_id =#{departmentId}
    </select>

    <select id="isExistByCompanyId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id FROM  m_position where fk_company_id =#{companyId}
    </select>

    <select id="getPositions" parameterType="com.get.permissioncenter.entity.Position" resultType="com.get.permissioncenter.entity.Position">
        select p.* from m_position p LEFT JOIN m_department d on d.id = p.fk_department_id
        <where>
            <if test="positionDto.fkCompanyId != null">
             and  p.fk_company_id = #{positionDto.fkCompanyId,jdbcType=BIGINT}
            </if>
            <if test="positionDto.fkDepartmentId != null">
            and  p.fk_department_id = #{positionDto.fkDepartmentId,jdbcType=BIGINT}
            </if>
            <if test="positionDto.keyWord != null and  positionDto.keyWord !=''">
            and (position(#{positionDto.keyWord,jdbcType=VARCHAR} in p.num) or position(#{positionDto.keyWord,jdbcType=VARCHAR} in p.name))
            </if>
            <if test="positionDto.fkCompanyIds != null and positionDto.fkCompanyIds.size()>0">
                AND p.fk_company_id IN
                <foreach collection="fkCompanyIds" item="fkCompanyId" index="index" open="(" separator="," close=")">
                    #{positionDto.fkCompanyId,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
        order by d.view_order,p.view_order
    </select>

    <select id="getPositionNumByIds" resultType="com.get.permissioncenter.vo.PositionVo">
        SELECT
        ms.id as fkStaffId,
        mp.num
        FROM
        m_staff AS ms
        LEFT JOIN
        m_position AS mp
        ON
        ms.fk_position_id = mp.id
        where 1=1
        <if test="ids != null and ids != ''">
            and  ms.id in ${ids}
        </if>
    </select>

    <select id="getPositionNumAndStaffIdMap" resultType="com.get.permissioncenter.vo.StaffVo">
        SELECT
        mp.num as positionNum,
        ms.id
        FROM
        m_staff AS ms
        INNER JOIN
        m_position AS mp
        ON
        ms.fk_position_id = mp.id
        where ms.id in
        <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>