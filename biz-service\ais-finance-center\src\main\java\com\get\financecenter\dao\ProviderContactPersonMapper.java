package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.entity.ProviderContactPerson;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProviderContactPersonMapper extends BaseMapper<ProviderContactPerson> {

    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */

    int insertSelective(ProviderContactPerson record);

    /**
     * @return java.lang.Boolean
     * @Description :验证是否为空
     * @Param [id]
     * <AUTHOR>
     */
    Boolean providerContactPersonIsEmpty(@Param("id") Long id);
}