package com.get.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * IAECRM
 */
@Data
@TableName("MAttachment")
public class MAttachment implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(
            value = "AttachmentID",
            type = IdType.NONE
    )
//    @Column(name = "AttachmentID")
    private String AttachmentID;

    /**
     * 附件功能
     */
    @TableField("AttachmentFunction")
    private String AttachmentFunction;

    /**
     * 附件类型
     */
    @TableField("AttachmentType")
    private String AttachmentType;

    /**
     * 附件源文件名
     */
    @TableField("FileName")
    private String FileName;

    /**
     * 附件内容(二进制内容)
     */
    @TableField("AttachmentFile")
    private byte[] AttachmentFile;
}