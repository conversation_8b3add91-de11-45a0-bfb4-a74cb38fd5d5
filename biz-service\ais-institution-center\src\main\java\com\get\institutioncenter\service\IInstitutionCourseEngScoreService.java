package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.vo.InstitutionCourseEngScoreVo;
import com.get.institutioncenter.entity.InstitutionCourseEngScore;
import com.get.institutioncenter.dto.InstitutionCourseEngScoreDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/12/7
 * @TIME: 16:05
 * @Description:
 **/
public interface IInstitutionCourseEngScoreService extends BaseService<InstitutionCourseEngScore> {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    InstitutionCourseEngScoreVo findInstitutionCourseEngScoreById(Long id);

    /**
     * 列表数据
     *
     * @param institutionCourseEngScoreDto
     * @param page
     * @return
     */
    List<InstitutionCourseEngScoreVo> getInstitutionCourseEngScores(InstitutionCourseEngScoreDto institutionCourseEngScoreDto, Page page);

    /**
     * 修改
     *
     * @param institutionCourseEngScoreDto
     * @return
     */
    InstitutionCourseEngScoreVo updateInstitutionCourseEngScore(InstitutionCourseEngScoreDto institutionCourseEngScoreDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 批量保存
     *
     * @param institutionCourseEngScoreDtos
     * @return
     */
    void batchAdd(List<InstitutionCourseEngScoreDto> institutionCourseEngScoreDtos);

    /**
     * 测试类型下拉框
     *
     * @return
     */
    List<Map<String, Object>> getEnglishTestType();

    /**
     * 删除记录
     *
     * @return
     */
    void deleteInstitutionCourseEngScoreByCourseId(Long id);
}
