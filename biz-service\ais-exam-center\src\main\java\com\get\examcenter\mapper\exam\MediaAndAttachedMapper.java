package com.get.examcenter.mapper.exam;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.examcenter.entity.ExamMediaAndAttached;
import org.apache.ibatis.annotations.Param;

@DS("examdb")
public interface MediaAndAttachedMapper extends BaseMapper<ExamMediaAndAttached> {
    int insert(ExamMediaAndAttached record);

    int insertSelective(ExamMediaAndAttached record);

    int updateByPrimaryKeySelective(ExamMediaAndAttached record);

    int updateByPrimaryKey(ExamMediaAndAttached record);

    Integer getNextIndexKey(@Param("tableId") Long tableId, @Param("tableName") String tableName);
}