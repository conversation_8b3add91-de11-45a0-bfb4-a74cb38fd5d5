package com.get.financecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

@Data
public class FormInformationVo {
    @ApiModelProperty(value = "申请单ID")
    private Long id;
    @ApiModelProperty(value = "申请单编号")
    private String num;
    @ApiModelProperty(value = "申请人")
    private String applicant;
    @ApiModelProperty(value = "申请部门名称")
    private String departmentName;
    @ApiModelProperty(value = "申请日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createDate;
    @ApiModelProperty(value = "申请金额")
    private String amount;
    @ApiModelProperty(value = "申请摘要")
    private String summary;
    @ApiModelProperty(value = "币种")
    private String currencyTypeName;
}
