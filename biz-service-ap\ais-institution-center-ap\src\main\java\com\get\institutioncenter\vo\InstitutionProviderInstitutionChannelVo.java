package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Hardy
 * @create: 2021/9/22 11:23
 * @verison: 1.0
 * @description:
 */
@Data
public class InstitutionProviderInstitutionChannelVo extends BaseEntity {

    /**
     * 学校提供商名称
     */
    @ApiModelProperty(value = "学校提供商名称")
    private String providerName;

    @ApiModelProperty(value = "提供商名称")
    private String name;

    /**
     * 渠道名称
     */
    @ApiModelProperty("渠道名称")
    private String channelName;

    @ApiModelProperty(value = "学校提供商中文名称")
    private String providerNameChn;

    @ApiModelProperty(value = "渠道中文名称")
    private String channelNameChn;

    /**
     * 渠道和学校提供商名称
     */
    @ApiModelProperty(value = "渠道和学校提供商名称")
    private String channelProviderName;

    //==============实体类InstitutionProviderInstitutionChannel======================
    private static final long serialVersionUID = 1L;
    /**
     * 学校提供商Id
     */
    @ApiModelProperty(value = "学校提供商Id")
    @Column(name = "fk_institution_provider_id")
    private Long fkInstitutionProviderId;
    /**
     * 渠道来源Id
     */
    @ApiModelProperty(value = "渠道来源Id")
    @Column(name = "fk_institution_channel_id")
    private Long fkInstitutionChannelId;

}
