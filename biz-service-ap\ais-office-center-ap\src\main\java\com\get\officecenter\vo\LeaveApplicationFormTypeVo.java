package com.get.officecenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import com.get.officecenter.entity.LeaveApplicationFormType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Sea
 * @create: 2021/4/12 16:04
 * @verison: 1.0
 * @description:
 */
@Data
public class LeaveApplicationFormTypeVo extends BaseVoEntity {

    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("公司名称")
    private String fkCompanyIds;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    private String typeName;

    /**
     * 类型关键字
     */
    @ApiModelProperty(value = "类型关键字")
    private String typeKey;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
}
