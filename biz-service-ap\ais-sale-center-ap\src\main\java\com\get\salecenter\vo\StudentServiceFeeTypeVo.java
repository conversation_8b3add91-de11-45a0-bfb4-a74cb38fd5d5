package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.StudentServiceFeeType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 */
@Data
public class StudentServiceFeeTypeVo extends BaseEntity {


    @ApiModelProperty("公司名称")
    private String fkCompanyName;

    //========实体类StudentServiceFeeType=============
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "类型名称")
    @Column(name = "type_name")
    private String typeName;


    /**
     * 公司Id
     */
    @ApiModelProperty(value = "类型key")
    @Column(name = "type_key")
    private String typeKey;


    /**
     * 公司Id
     */
    @ApiModelProperty(value = "排序")
    @Column(name = "view_order")
    private Integer viewOrder;
}
