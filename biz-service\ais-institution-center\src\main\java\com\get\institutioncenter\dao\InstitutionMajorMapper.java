package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.institutioncenter.entity.InstitutionMajor;
import com.get.salecenter.vo.SelItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface InstitutionMajorMapper extends BaseMapper<InstitutionMajor> {
    @Override
    int insert(InstitutionMajor record);

    int insertSelective(InstitutionMajor record);

    /**
     * 根据专业id查找专业名称
     *
     * @param id
     * @return
     */
    String getInstitutionMajorNameById(Long id);

    Integer getMaxId();

    /**
     * 根据公式id查找等级id
     *
     * @param id
     * @return
     */
    List<Long> getMajorLevelByContractFormula(Long id);

    /**
     * 获取课程等级下拉
     * @param keyword
     * @return
     */
    List<BaseSelectEntity> getInstitutionMajorSelect(String keyword);

    List<SelItem> getInstitutionMajorNamesByIds(@Param("ids") Set<Long> ids);
}