package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.StudentProjectRoleStaff;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * author:Neil
 * Time: 12:00
 * Date: 2022/6/14
 * Description:
 */
@Data
public class StudentOfferRoleAndStaffVo extends BaseEntity {
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 角色名称
     */
    @ApiModelProperty(value = "角色名称")
    private String roleName;

    /**
     * 员工名称
     */
    @ApiModelProperty(value = "员工名称")
    private String staffName;

    /**
     * 员工名称(英文名)
     */
    @ApiModelProperty(value = "员工名称（英文名）")
    private String staffNameEn;

    /**
     * 角色Key
     */
    @ApiModelProperty(value = "角色Key")
    private String roleKey;

    @ApiModelProperty(value = "员工邮箱")
    private String email;

    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;

    /**
     * fkTableId
     */
    @ApiModelProperty(value = "fkTableId")
    private Long fkTableId;

    //==============实体类StudentProjectRoleStaff=============
    private static final long serialVersionUID = 1L;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    @Column(name = "fk_table_name")
    private String fkTableName;

    /**
     * 学生项目角色Id
     */
    @ApiModelProperty(value = "学生项目角色Id")
    @Column(name = "fk_student_project_role_id")
    private Long fkStudentProjectRoleId;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
    /**
     * 绑定时间
     */
    @ApiModelProperty(value = "绑定时间")
    @Column(name = "active_date")
    private Date activeDate;
    /**
     * 取消绑定时间（下次绑定时，需要重新建立记录）
     */
    @ApiModelProperty(value = "取消绑定时间（下次绑定时，需要重新建立记录）")
    @Column(name = "unactive_date")
    private Date unactiveDate;


}
