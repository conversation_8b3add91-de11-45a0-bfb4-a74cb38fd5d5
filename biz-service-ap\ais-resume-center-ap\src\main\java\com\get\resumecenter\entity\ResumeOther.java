package com.get.resumecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_resume_other")
public class ResumeOther extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 所属简历Id
     */
    @ApiModelProperty(value = "所属简历Id")
    @Column(name = "fk_resume_id")
    private Long fkResumeId;
    /**
     * 简历其他类型Id
     */
    @ApiModelProperty(value = "简历其他类型Id")
    @Column(name = "fk_other_type_id")
    private Long fkOtherTypeId;
    /**
     * 主题
     */
    @ApiModelProperty(value = "主题")
    @Column(name = "subject")
    private String subject;
    /**
     * 主题描述
     */
    @ApiModelProperty(value = "主题描述")
    @Column(name = "description")
    private String description;
}