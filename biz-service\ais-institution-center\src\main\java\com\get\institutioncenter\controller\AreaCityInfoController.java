package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.institutioncenter.dto.AreaCityInfoDto;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.vo.AreaCityInfoVo;
import com.get.institutioncenter.service.IAreaCityInfoService;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/3/2 11:21
 * @verison: 1.0
 * @description:
 */
@Api(tags = "城市资讯管理")
@RestController
@RequestMapping("institution/areaCityInfo")
public class AreaCityInfoController {
    @Resource
    private IAreaCityInfoService areaCityInfoService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AreaCityInfoVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/城市资讯管理/城市资讯详情")
    @GetMapping("/{id}")
    public ResponseBo<AreaCityInfoVo> detail(@PathVariable("id") Long id) {
        AreaCityInfoVo data = areaCityInfoService.findAreaCityInfoById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增
     * @Param [areaCityInfoDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/城市资讯管理/新增城市资讯")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(AreaCityInfoDto.Add.class) AreaCityInfoDto areaCityInfoDto) {
        return SaveResponseBo.ok(areaCityInfoService.add(areaCityInfoDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/城市资讯管理/删除城市资讯")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        areaCityInfoService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AreaCityInfoVo>
     * @Description :修改信息
     * @Param [areaCityInfoDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/城市资讯管理/更新城市资讯")
    @PostMapping("update")
    public ResponseBo<AreaCityInfoVo> update(@RequestBody  @Validated(AreaCityInfoDto.Update.class) AreaCityInfoDto areaCityInfoDto) {
        return UpdateResponseBo.ok(areaCityInfoService.updateAreaCityInfo(areaCityInfoDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AreaCityInfoVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/城市资讯管理/查询城市资讯")
    @PostMapping("datas")
    public ResponseBo<AreaCityInfoVo> datas(@RequestBody SearchBean<AreaCityInfoDto> page) {
        List<AreaCityInfoVo> datas = areaCityInfoService.getAreaCityInfos(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.MediaAndAttachedDto>
     * @Description :查询城市资讯附件
     * @Param [voSearchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "查询城市资讯附件", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/城市资讯管理/查询附件")
    @PostMapping("getItemMedia")
    public ResponseBo<MediaAndAttachedVo> getItemMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> staffMedia = areaCityInfoService.getItemMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.MediaAndAttachedDto>
     * @Description :保存城市资讯附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "保存城市资讯附件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/城市资讯管理/保存附件")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> addAgentMedia(@RequestBody @Validated(MediaAndAttachedDto.Add.class) ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(areaCityInfoService.addItemMedia(mediaAttachedVo));
    }

}
