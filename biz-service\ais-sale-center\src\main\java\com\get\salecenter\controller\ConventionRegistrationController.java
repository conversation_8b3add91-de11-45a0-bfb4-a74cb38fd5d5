package com.get.salecenter.controller;

import org.springframework.ui.Model;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.dto.ConventionRegistrationFastGenerationDto;
import com.get.salecenter.dto.ConventionRegistrationRosterDto;
import com.get.salecenter.vo.BoothVo;
import com.get.salecenter.vo.ConventionRegistrationVo;
import com.get.salecenter.vo.RegistrationSponsorVo;
import com.get.salecenter.service.IConventionRegistrationService;
import com.get.salecenter.utils.GetExchangeRateUtils;
import com.get.salecenter.dto.ConventionRegistrationDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/3 10:56
 * @verison: 1.0
 * @description: 峰会报名管理控制器
 */
@Api(tags = "峰会报名管理")
@RestController
@RequestMapping("sale/conventionRegistration")
public class ConventionRegistrationController {

    @Resource
    private IConventionRegistrationService conventionRegistrationService;

    @Resource
    private GetExchangeRateUtils getExchangeRateUtils;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/峰会报名管理/峰会报名详情")
    @GetMapping("/{id}")
    public ResponseBo<ConventionRegistrationVo> detail(@PathVariable("id") Long id) {
        ConventionRegistrationVo data = conventionRegistrationService.findConventionRegistrationById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 批量新增信息
     *
     * @param conventionRegistrationDtos
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/峰会报名管理/新增峰会报名")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(ConventionRegistrationDto.Add.class) ValidList<ConventionRegistrationDto> conventionRegistrationDtos) {
        conventionRegistrationService.batchAdd(conventionRegistrationDtos);
        return SaveResponseBo.ok();
    }

    /**
     * 新增信息
     *
     * @param
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/峰会报名管理/新增峰会报名")
    @VerifyLogin(IsVerify = false)
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(ConventionRegistrationRosterDto.Add.class)ConventionRegistrationRosterDto conventionRegistrationRosterDto) {
        conventionRegistrationService.add(conventionRegistrationRosterDto);
        return SaveResponseBo.ok();
    }



    /**
     * 是否报名
     *
     * @param
     * @return
     * @
     */
    @ApiOperation(value = "是否报名", notes = "")
    @VerifyLogin(IsVerify = false)
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/峰会报名管理/是否报名")
    @GetMapping ("isRegistered")
        public ResponseBo isRegistered(@RequestParam(value = "fkInstitutionProviderId",required = false) Long fkInstitutionProviderId,
                                 @RequestParam(value = "providerName",required = false) String providerName
                                 ) {
       int num = conventionRegistrationService.isRegistered(fkInstitutionProviderId, providerName);
    return new ResponseBo<>(num);

    }



    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/峰会报名管理/删除峰会报名")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        conventionRegistrationService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param conventionProcedureVo
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/峰会报名管理/更新峰会报名")
    @PostMapping("update")
    public ResponseBo<ConventionRegistrationVo> update(@RequestBody @Validated(ConventionRegistrationDto.Update.class) ConventionRegistrationDto conventionProcedureVo) {
        return UpdateResponseBo.ok(conventionRegistrationService.updateConventionRegistration(conventionProcedureVo));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "institutionProviderName为学校提供商名称 , contactPersonName为联系人名称,fkConventionId该峰会id(一定有)")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/峰会报名管理/查询峰会报名")
    @PostMapping("datas")
    public ResponseBo<ConventionRegistrationVo> datas(@RequestBody SearchBean<ConventionRegistrationDto> page) {
        List<ConventionRegistrationVo> datas = conventionRegistrationService.getConventionRegistrations(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

//    /**
//     * 列表数据
//     *
//     * @param page
//     * @return
//     * @
//     */
//    @ApiOperation(value = "列表数据", notes = "institutionProviderName为学校提供商名称 , contactPersonName为联系人名称,fkConventionId该峰会id(一定有)")
//    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/峰会报名管理/查询峰会报名")
//    @PostMapping("datas")
//    public ResponseBo<RegistrationSponsorVo> datas(@RequestBody SearchBean<ConventionRegistrationDto> page)  {
//        List<RegistrationSponsorVo> datas = conventionRegistrationService.getConventionRegistrationGroup(page.getData(), page);
//        Page p = BeanCopyUtils.objClone(page, Page.class);
//        return new ListResponseBo<>(datas, p);
//    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 修改状态
     * @Param [id, status]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改状态", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/峰会报名管理/修改状态")
    @PostMapping("updateStatus")
    public ResponseBo updateStatus(@RequestParam("id") Long id, @RequestParam("status") Integer status) {
        conventionRegistrationService.updateStatus(id, status);
        return UpdateResponseBo.ok();
    }


    /**
     * 所属报名名册下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "所属报名名册下拉框数据", notes = "")
    @GetMapping("getConventionRegistrationList/{id}")
    public ResponseBo getConventionRegistrationList(@PathVariable("id") Long id) {
        List<ConventionRegistrationVo> datas = conventionRegistrationService.getConventionRegistrationList(id);
        return new ListResponseBo<>(datas);
    }

    /**
     * 根据回执码返回费用合计
     *
     * @return
     * @
     */
    @ApiOperation(value = "根据回执码返回费用合计", notes = "receiptCode为回执码")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/峰会报名管理/回执码费用合计")
    @GetMapping("getSumRegistrationFree/{receiptCode}")
    public ResponseBo<RegistrationSponsorVo> getSumRegistrationFree(@PathVariable("receiptCode") String receiptCode) {
        List<BoothVo> boothVos = conventionRegistrationService.getSumRegistrationFreeByReceiptCode(receiptCode);
        RegistrationSponsorVo data = new RegistrationSponsorVo();
        data.setReceiptCode(receiptCode);
        data.setBoothDtos(boothVos);
        return new ResponseBo<>(data);
    }

    /**
     * @return void
     * @Description :导出报名名册Excel
     * @Param [response, conventionSponsorVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "导出报名名册Excel")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/峰会报名管理/导出报名名册Excel")
    @PostMapping("/exportConventionRegistrationExcel")
    @ResponseBody
    public void exportConventionRegistrationExcel(HttpServletResponse response, @RequestBody ConventionRegistrationDto conventionRegistrationDto) {
        conventionRegistrationService.exportConventionRegistrationExcel(response, conventionRegistrationDto);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取报名费折合人民币")
    @PostMapping("/registrationFeeCny")
    @ResponseBody
    public ResponseBo registrationFeeCny(@RequestBody ConventionRegistrationDto conventionRegistrationDto) {
        return new ResponseBo<>(conventionRegistrationService.registrationFeeCny(conventionRegistrationDto));
    }

    @VerifyPermission(IsVerify = false)
    @ApiModelProperty(value = "报名名册状态下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/峰会报名管理/报名名册/报名名册状态下拉")
    @PostMapping("getConventionRegistrationStatusSelect")
    public ResponseBo getConventionRegistrationStatusSelect() {
        return new ListResponseBo<>(ProjectExtraEnum.enums2Arrays(ProjectExtraEnum.CONVENTION_STATUS));
    }

    @ApiOperation(value = "创建报名费单据", notes = "一键生成活动费用汇总、应收计划、发票")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/峰会报名管理/报名名册/创建报名费单据")
    @PostMapping("/fastGeneration")
    public ResponseBo fastGeneration(@RequestBody @Validated ConventionRegistrationFastGenerationDto conventionRegistrationFastGenerationVo) {
        conventionRegistrationService.fastGeneration(conventionRegistrationFastGenerationVo);
        return SaveResponseBo.ok();
    }

//    /**
//     * @return void
//     * @Description :导出报名名册Excel
//     * @Param [response,conventionSponsorVo]
//     * <AUTHOR>
//     */
//    @ApiOperation(value = "导出报名名册Excel")
//    @GetMapping("/exportConventionRegistrationExcel")
//    @ResponseBody
//    public void exportConventionRegistrationExcel(HttpServletResponse response)  {
//        ConventionRegistrationDto conventionRegistrationVo = new ConventionRegistrationDto();
//        conventionRegistrationVo.setFkConventionId(8L);
//        conventionRegistrationVo.setFkAreaCountryId(7L);
//        conventionRegistrationService.exportConventionRegistrationExcel(response, conventionRegistrationVo);
//    }
}
