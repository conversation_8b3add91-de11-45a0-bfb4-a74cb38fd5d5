package com.get.votingcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.votingcenter.vo.VotingRuleVo;
import com.get.votingcenter.service.IVotingRuleService;
import com.get.votingcenter.dto.VotingRuleListDto;
import com.get.votingcenter.dto.VotingRuleUpdateDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/9/23 14:50
 * @verison: 1.0
 * @description:
 */
@Api(tags = "投票规则管理")
@RestController
@RequestMapping("/voting/votingRule")
@Slf4j
public class VotingRuleController {

    @Resource
    private IVotingRuleService votingRuleService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.votingcenter.vo.VotingRuleVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.VOTINGCENTER, type = LoggerOptTypeConst.LIST, description = "投票中心/投票规则管理/查询投票规则")
    @PostMapping("datas")
    public ResponseBo<VotingRuleVo> datas(@RequestBody VotingRuleListDto votingRuleListDto) {
        List<VotingRuleVo> datas = votingRuleService.getVotingRules(votingRuleListDto);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :
     * @Param [VotingRuleUpdateDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.VOTINGCENTER, type = LoggerOptTypeConst.ADD, description = "投票中心/投票规则管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(VotingRuleUpdateDto.Add.class) VotingRuleUpdateDto votingRuleUpdateDto) {
        return SaveResponseBo.ok(votingRuleService.addVotingRule(votingRuleUpdateDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :
     * @Param [VotingRuleUpdateDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.VOTINGCENTER, type = LoggerOptTypeConst.EDIT, description = "投票中心/投票规则管理/修改")
    @PostMapping("update")
    public ResponseBo<VotingRuleVo> update(@RequestBody @Validated(VotingRuleUpdateDto.Update.class) VotingRuleUpdateDto votingRuleUpdateDto) {
        return UpdateResponseBo.ok(votingRuleService.updateVotingRule(votingRuleUpdateDto));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.VOTINGCENTER, type = LoggerOptTypeConst.DELETE, description = "投票中心/投票规则管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        votingRuleService.deleteVotingRule(id);
        return DeleteResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.votingcenter.vo.VotingRuleVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "投票主题详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.VOTINGCENTER, type = LoggerOptTypeConst.DETAIL, description = "投票中心/投票规则管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<VotingRuleVo> detail(@PathVariable("id") Long id) {
        VotingRuleVo data = votingRuleService.findVotingRuleById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 上移下移
     * @Param [votingRuleListDtos]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.VOTINGCENTER, type = LoggerOptTypeConst.EDIT, description = "投票中心/投票规则管理/排序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<VotingRuleListDto> votingRuleListDtos) {
        votingRuleService.movingOrder(votingRuleListDtos);
        return ResponseBo.ok();
    }
}
