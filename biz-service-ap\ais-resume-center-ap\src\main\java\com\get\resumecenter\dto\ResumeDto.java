package com.get.resumecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2020/8/3
 * @TIME: 16:14
 * @Description:
 **/
@Data
public class ResumeDto  extends BaseVoEntity implements Serializable {

    /**
     * 公司Id
     */
    @NotNull(message = "公司Id不能为空", groups = {Add.class, Update.class})
    @Min(value = 1, message = "缺少公司id参数", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "公司Id", required = true)
    private Long fkCompanyId;

    /**
     * 简历类型Id
     */
    @NotNull(message = "简历类型Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "简历类型Id", required = true)
    private Long fkResumeTypeId;

    /**
     * 简历guid(人才中心)
     */
    @ApiModelProperty(value = "简历guid(人才中心)")
    private String resumeGuid;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "姓名", required = true)
    private String name;

    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男", required = true)
    @NotNull(message = "性别不能为空", groups = {Add.class, Update.class})
    private Integer gender;

    /**
     * 生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "生日", required = true)
    private Date birthday;

    /**
     * 身高（cm）
     */
    @ApiModelProperty(value = "身高（cm）", required = true)
    private Integer height;

    /**
     * 开始工作年份
     */
    @ApiModelProperty(value = "开始工作年份", required = true)
    private Integer startWorkingYear;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号", required = true)
    private String identityCard;

    /**
     * 国籍
     */
    @ApiModelProperty(value = "国籍", required = true)
    private String nationality;

    /**
     * 户口所在地
     */
    @ApiModelProperty(value = "户口所在地", required = true)
    private String residence;

    /**
     * 婚姻状态：未婚/已婚/保密
     */
    @ApiModelProperty(value = "婚姻状态：未婚/已婚/保密", required = true)
    private String marriage;

    /**
     * 政治面貌：党员/预备党员/团员/民主党派人士/无党派民主人士/普通公民
     */
    @ApiModelProperty(value = "政治面貌：党员/预备党员/团员/民主党派人士/无党派民主人士/普通公民", required = true)
    private String political;

    /**
     * 住址电话
     */
    @ApiModelProperty(value = "住址电话", required = true)
    private String homeTel;

    /**
     * 电话区号
     */
    @ApiModelProperty(value = "电话区号", required = true)
    @NotBlank(message = "电话区号", groups = {Add.class, Update.class})
    private String mobileAreaCode;

    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话", required = true)
    @NotBlank(message = "移动电话不能为空", groups = {Add.class, Update.class})
    private String mobile;

    /**
     * Email
     */
    @ApiModelProperty(value = "Email", required = true)
    @NotBlank(message = "Email不能为空", groups = {Add.class, Update.class})
    private String email;

    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编", required = true)
    private String zipCode;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址", required = true)
    private String address;

    /**
     * QQ号
     */
    @ApiModelProperty(value = "QQ号")
    private String qq;

    /**
     * 微信号
     */
    @ApiModelProperty(value = "微信号")
    private String wechat;

    /**
     * whatsapp号
     */
    @ApiModelProperty(value = "whatsapp号")
    private String whatsapp;

    /**
     * 头像媒体附件id
     */
    @ApiModelProperty(value = "头像媒体附件id")
    private Long fkMediaId;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String institution;


}
