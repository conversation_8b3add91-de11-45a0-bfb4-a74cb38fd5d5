package com.get.reportcenter.feign;

import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.reportcenter.dto.ReportSaleDto;
import com.get.reportcenter.entity.ReportSale;
import com.get.reportcenter.service.IReportSaleService;
//import com.get.reportcenter.vo.ReportSaleVo;
import com.get.salecenter.vo.PeriodicStatisticsVo;
import com.get.salecenter.dto.StudentApplicationStatisticsDto;
import com.get.salecenter.vo.StudentApplicationStatisticsVo;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * Feign实现
 */
@RestController
@AllArgsConstructor
@VerifyPermission(IsVerify = false)
public class ReportCenterClient implements IReportCenterClient {

    private IReportSaleService reportSaleService;
//    private IExportService exportService;
//    @Override
//    public Result<List<StudentOfferItemReportModel>> successfullyExportedCustomerList(ReportStudentOfferItemVo studentOfferItemVo) {
//        return Result.data(exportService.successfullyExportedCustomerList(studentOfferItemVo));
//    }

    @Override
    public Result<ReportSale> getLastReportSale(Long fkStaffId){
        return Result.data(reportSaleService.getLastReportSale(fkStaffId));
    }

    @Override
    public Result<ReportSale> getReportSale(StudentApplicationStatisticsDto studentApplicationStatisticsDto){
        return Result.data(reportSaleService.getReportSale(studentApplicationStatisticsDto));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public void updateReportSaleStatus(Long fkReportSaleId, Integer reportStatus){
        reportSaleService.updateReportSaleStatus(fkReportSaleId,reportStatus);
    }

    @Override
    public Result<Long> addReportSale(StudentApplicationStatisticsDto studentApplicationStatisticsDto){
        return Result.data(reportSaleService.addReportSale(studentApplicationStatisticsDto));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<Long> addReportSaleCommon(ReportSaleDto reportSaleVo) {
        return Result.data(reportSaleService.addReportSaleCommon(reportSaleVo));
    }

    @Override
    public void updateReportSale(PeriodicStatisticsVo periodicStatisticsVo, Long fkReportSaleId){
        reportSaleService.updateReportSale(periodicStatisticsVo,fkReportSaleId);
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<Integer> getReportSaleStatusById(Long fkReportSaleId) {
        return Result.data(reportSaleService.getReportSaleStatusById(fkReportSaleId));
    }

    @Override
    public Result<ReportSaleDto> getReportSaleById(Long fkReportSaleId) {
        return Result.data(reportSaleService.getReportSaleById(fkReportSaleId));
    }

    @Override
    public Result<ReportSaleDto> getLastReportSaleVoByReportNameAndUserId(String key, Long staffId) {
        return Result.data(reportSaleService.getLastReportSaleVoByReportNameAndUserId(key,staffId));
    }
}
