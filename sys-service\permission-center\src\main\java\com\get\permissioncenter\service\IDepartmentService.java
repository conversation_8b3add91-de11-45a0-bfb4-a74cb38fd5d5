package com.get.permissioncenter.service;

import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.permissioncenter.dto.CompanyIdsRequestDto;
import com.get.permissioncenter.dto.DepartmentDto;
import com.get.permissioncenter.entity.Department;
import com.get.permissioncenter.vo.CompanyVo;
import com.get.permissioncenter.vo.DepartmentVo;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/6/28
 * @TIME: 16:43
 **/
public interface IDepartmentService extends BaseService<Department> {


    /**
     * 列表数据
     *
     * @param departMentDto
     * @param
     * @return
     */
    List<DepartmentVo> getDepartments(DepartmentDto departMentDto);

    /**
     * 根据所属公司查询所有的职位
     *
     * @return
     */
    List<DepartmentVo> getAllDepartments(Long companyId);


    /**
     * 详情
     *
     * @param id
     * @return
     */
    DepartmentVo findDepartmentById(Long id);


    /**
     * 新增
     *
     * @param departMentDto
     * @return
     */
    Long addDepartment(DepartmentDto departMentDto);

    /**
     * 修改
     *
     * @param departMentDto
     * @return
     */

    DepartmentVo updateDepartment(DepartmentDto departMentDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 排序
     *
     * @param departmentDtos
     */
    void movingOrder(List<DepartmentDto> departmentDtos);

    /**
     * 批量新增
     *
     * @param departmentDtos
     */
    void batchAddDepartment(List<DepartmentDto> departmentDtos);
    /**
     * @return java.util.List<com.get.common.com.get.permissioncenter.vo.entity.BaseSelectEntity>
     * @Description :部门下拉框数据
     * @Param [id]
     * <AUTHOR>
     */
    List<BaseSelectEntity> getDepartmentSelect(Long companyId);

    /**
     * @return java.util.List<com.get.common.com.get.permissioncenter.vo.entity.BaseSelectEntity>
     * @Description :批量获取部门下拉框数据
     */
    List<BaseSelectEntity> batchObtainDepartmentList(CompanyIdsRequestDto companyIds);


    /**
     * @return java.util.List<java.lang.String>
     * @Description :销售中心feign调用 部门编号对应的部门名称
     * @Param [departmentNumList]
     * <AUTHOR>
     */
    List<String> getDepartmentNameList(String[] departmentNumList);

    /**
     * @return java.lang.String
     * @Description :feign调用 根据id查找对应部门名称
     * @Param [departmentId]
     * <AUTHOR>
     */
    String getDepartmentNameById(Long departmentId);


    Map<Long, String> getDepartmentNamesByIds(Set<Long> departmentIds);

    /**
     * 根据部门编号获取部门id
     *
     * @param num
     * @return
     */
    Long getDepartmentIdByNum(String num);

    /**
     * 根据部门id获取部门编号
     *
     * @param id
     * @return
     */
    String getDepartmentNumById(Long id);

    CompanyVo multiCompanyDepartmentList();
}
