package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/12/7 11:29
 * @verison: 1.0
 * @description:
 */
@Data
public class EventTypeDto extends BaseVoEntity {
    /**
     * 类型名称
     */
    @NotBlank(message = "类型名称不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "类型名称")
    private String typeName;

    @ApiModelProperty(value = "专属部门ids")
    private List<Long> fkDepartmentIdList;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    //自定义内容
    /**
     * 关键字搜索
     */
    @ApiModelProperty(value = "关键字搜索")
    private String keyWord;

  
}
