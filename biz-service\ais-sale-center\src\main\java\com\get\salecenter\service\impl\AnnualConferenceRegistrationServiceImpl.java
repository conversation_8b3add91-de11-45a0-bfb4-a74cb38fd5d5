package com.get.salecenter.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.CacheKeyConstants;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.redis.cache.GetRedis;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.remindercenter.dto.MailDto;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.salecenter.dao.sale.*;
import com.get.salecenter.dto.*;
import com.get.salecenter.entity.*;
import com.get.salecenter.service.*;
import com.get.salecenter.utils.MailTemplateUtils;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.vo.*;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2021/4/29 10:49
 * @verison: 1.0
 * @description:
 */
@Service
public class AnnualConferenceRegistrationServiceImpl implements IAnnualConferenceRegistrationService {
    /**
     * 桌子缓存数
     */
    private static final String seat_cache_key = "seat_count";

    @Resource
    private IConventionRegistrationService conventionRegistrationService;
    @Resource
    private IConventionSponsorService conventionSponsorService;
    @Resource
    private IConventionSponsorFeeService conventionSponsorFeeService;
    @Resource
    private IConventionSponsorSponsorFeeService conventionSponsorSponsorFeeService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ConventionMapper conventionMapper;
    @Resource
    private ConventionTableMapper conventionTableMapper;
    @Resource
    private ConventionTablePersonMapper conventionTablePersonMapper;
    @Resource
    private ConventionRegistrationMapper conventionRegistrationMapper;
    @Resource
    private IConventionPersonService conventionPersonService;
    @Resource
    private ConventionPersonStaffMapper conventionPersonStaffMapper;
    @Resource
    private ConventionPersonInstitutionProviderMapper conventionPersonInstitutionProviderMapper;
    @Resource
    private ConventionPersonInstitutionMapper conventionPersonInstitutionMapper;
    @Resource
    private ConventionPersonAgentMapper conventionPersonAgentMapper;
    @Resource
    private ConventionPersonRegistrationMapper conventionPersonRegistrationMapper;
    @Resource
    private ConventionPersonMapper conventionPersonMapper;
    @Resource
    private IFormCommonService formCommonService;
    @Resource
    private ConventionPersonProcedureMapper conventionPersonProcedureMapper;
    @Resource
    private ConventionRegistrationAreaCountryMapper conventionRegistrationAreaCountryMapper;
    @Resource
    private IReminderCenterClient reminderCenterClient;
    @Resource
    private IStaffBdCodeService staffBdCodeService;
    @Resource
    private GetRedis getRedis;
    @Resource
    private DataSourceTransactionManager transactionManager;
//    @Autowired
//    private JavaMailSender javaMailSender;
    @Value("${spring.mail.port}")
    private int port;

    @Value("${conference.email}")
    private String conferenceEmail;

    @Value("${conference.emailPassword}")
    private String conferenceEmailPassword;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addAnnualConferenceRegistration(AnnualConferenceRegistrationDto annualConferenceRegistrationDto) {
        if (GeneralTool.isEmpty(annualConferenceRegistrationDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_is_not_empty"));
        }
        if (GeneralTool.isEmpty(annualConferenceRegistrationDto.getRegistrationVos())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("booth_is_null"));
        }
        //自动生成回执码
        String randomCode = "";
        //自动获取，重复则继续获取，直到不重复
        boolean result = true;
        while (result) {
            randomCode = MyStringUtils.getRandomCode();
            if (!conventionSponsorService.validateReceiptCode(randomCode)) {
                result = false;
            }
        }
        //设置对象中的回执码
        annualConferenceRegistrationDto.getSponsorVo().setReceiptCode(randomCode);
        List<ConventionRegistrationDto> registrationVos1 = annualConferenceRegistrationDto.getRegistrationVos();
        String finalRandomCode = randomCode;
        registrationVos1.forEach(registrationVo1 -> registrationVo1.setReceiptCode(finalRandomCode));
        //新增报名
        conventionRegistrationService.batchAdd2(annualConferenceRegistrationDto.getRegistrationVos());

        if (GeneralTool.isNotEmpty(annualConferenceRegistrationDto.getSponsorVo().getConventionSponsorFeeVoList())) {
            //新增赞助商
            conventionSponsorService.addConventionSponsor(annualConferenceRegistrationDto.getSponsorVo());
        }
        //新增成功之后发邮件
        List<ConventionRegistrationDto> registrationVos = annualConferenceRegistrationDto.getRegistrationVos();
        if (GeneralTool.isNotEmpty(registrationVos)) {
            ConventionRegistrationDto conventionRegistrationDto = registrationVos.get(0);
            //标题
            String title = "Thank you for your registration and participation to the GEA 11th Annual Conference";
            //要抄送人的邮箱
//            String ccEmail = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.CONVENTION_REGISTRATION_CCEMAIL.key);
            Result<String> stringResult = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.GEA_CONVENTION_REGISTRATION.key);
            if (stringResult.isSuccess() && GeneralTool.isNotEmpty(stringResult.getData())) {
                String ccEmail = stringResult.getData();
                //获取邮箱模板
                String template = MailTemplateUtils.getMailTemplate(conventionRegistrationDto.getContactPersonName(), conventionRegistrationDto.getReceiptCode(), "autoEmail.html");
                MailDto mailVo = new MailDto();
                mailVo.setDefaultEncoding("utf-8");
                mailVo.setHost("smtp.qiye.aliyun.com");
                mailVo.setPort(port);
                mailVo.setProtocol("smtps");
                mailVo.setUserName(conferenceEmail);
                mailVo.setPassword(conferenceEmailPassword);
                mailVo.setTitle(title);
                mailVo.setToEmail(conventionRegistrationDto.getContactEmail());
                mailVo.setCcEmail(ccEmail);
                mailVo.setTemplate(template);
                mailVo.setFlag(true);
//                Result<Boolean> booleanResult = reminderCenterClient.customSendMail("utf-8", "smtp.qiye.aliyun.com", port, "smtps", conferenceEmail, conferenceEmailPassword, title, conventionRegistrationDto.getContactEmail(), ccEmail, template, true);
                Result<Boolean> booleanResult = reminderCenterClient.customSendMailByBody(mailVo);
                if (!booleanResult.isSuccess() || !booleanResult.getData()) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("save_failed"));
                }
//                JavaMailSenderImpl javaMailSenderImpl = new JavaMailSenderImpl();
//                javaMailSenderImpl.setDefaultEncoding("utf-8");
//                javaMailSenderImpl.setHost("smtp.qiye.aliyun.com");
//                javaMailSenderImpl.setPort(25);
//                javaMailSenderImpl.setProtocol("smtp");
//                javaMailSenderImpl.setUsername("<EMAIL>");
//                javaMailSenderImpl.setPassword("Alina@1231234");
//                MimeMessage mail = javaMailSenderImpl.createMimeMessage();
//                MimeMessageHelper helper = new MimeMessageHelper(mail);
//                helper.setTo(conventionRegistrationDto.getContactEmail());
//                helper.setCc(ccEmail);
//                helper.setSubject(title);
//                helper.setFrom("<EMAIL>");
//                helper.setText(template, true);
//                javaMailSenderImpl.send(mail);
//                Result<Boolean> booleanResult = reminderCenterClient.sendMail(title, template, conventionRegistrationDto.getContactEmail(), ccEmail);
//                if (!booleanResult.getData()) {
//                    throw new GetServiceException(LocaleMessageUtils.getMessage("save_failed"));
//                }
                //发送并抄送邮件
//                MailUtil.send(conventionRegistrationDto.getContactEmail(), ccEmail, null, title, template, true);
            } else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_configured_email_found"));
            }

            System.out.println("发送邮件成功");
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateAnnualConferenceRegistration(AnnualConferenceRegistrationDto annualConferenceRegistrationDto) {
        if (annualConferenceRegistrationDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        //修改赞助商和赞助类型
        if (GeneralTool.isNotEmpty(annualConferenceRegistrationDto.getSponsorVo().getConventionSponsorFeeVoList())) {
            conventionSponsorService.updateConventionSponsor2(annualConferenceRegistrationDto.getSponsorVo());
        }
        //修改展位号，因为不能取消前面的内容，所以只要继续新增就可以了
        conventionRegistrationService.batchAdd2(annualConferenceRegistrationDto.getRegistrationVos());
    }

    @Override
    public AnnualConferenceRegistrationVo getAnnualConferenceRegistrationDto(Long conventionId, String receiptCode) {
        AnnualConferenceRegistrationVo annualConferenceRegistrationVo = new AnnualConferenceRegistrationVo();
        //查找赞助商信息
        List<ConventionSponsorVo> conventionSponsorVos = conventionSponsorService.getConventionSponsorFeeDtos(conventionId, receiptCode);

        List<ConventionSponsorFeeVo> sponsorFeeDtoList = new ArrayList<>();
        for (ConventionSponsorVo conventionSponsorVo : conventionSponsorVos) {
            //赞助类型信息
            sponsorFeeDtoList.addAll(conventionSponsorVo.getConventionSponsorFeeDtoList());
        }
        //设置值
        annualConferenceRegistrationVo.setSponsorFeeDtos(sponsorFeeDtoList);

        //查找展位信息
        List<ConventionRegistrationVo> conventionRegistrationVos = conventionRegistrationService.getConventionRegistrationDto(conventionId, receiptCode);
        if (GeneralTool.isEmpty(conventionRegistrationVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (GeneralTool.isNotEmpty(conventionRegistrationVos)) {
            annualConferenceRegistrationVo.setRegistrationDtos(conventionRegistrationVos);
            //从展位对象列表取出一个，设置值（里面内容都是一样）
            ConventionRegistrationVo conventionRegistrationVo = conventionRegistrationVos.get(0);
            annualConferenceRegistrationVo.setSponsorName(conventionRegistrationVo.getProviderName());
            annualConferenceRegistrationVo.setName(conventionRegistrationVo.getContactPersonName());
            annualConferenceRegistrationVo.setEmail(conventionRegistrationVo.getContactEmail());
            annualConferenceRegistrationVo.setTel(conventionRegistrationVo.getContactTel());
        }
        return annualConferenceRegistrationVo;
    }

    @Override
    public List<Map<String, List<ConventionSponsorFeeVo>>> getSponsorshipConfig(Long conventionId) {
        return conventionSponsorFeeService.getSponsorshipFee(conventionId);
    }

    @Override
    public Boolean soldOut(Long sponsorFeeId, Integer initNum) {
        return conventionSponsorSponsorFeeService.soldOut(sponsorFeeId, initNum, null);
    }

    @Override
    public List<String> getBoothIndex(Long conventionId) {
        return conventionRegistrationService.getBoothIndex(conventionId);
    }

    @Override
    public Boolean haveSit(Long conventionId, String boothNum) {
        return conventionRegistrationService.haveSit(conventionId, boothNum);
    }

    @Override
    public Boolean providerNameVerify(Long conventionId, String providerName) {
        return conventionRegistrationService.providerNameVerify(conventionId, providerName);
    }


//    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addRegistration(AnnualRegistrationDto annualRegistrationDto) throws Exception {
        //配合缓存锁改成手动事务提交
        // 手动开启事务  start
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        definition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        TransactionStatus transaction = transactionManager.getTransaction(definition);
        // 手动开启事务  end

        List<String> placeList = Lists.newArrayList();

        try {
            if (GeneralTool.isEmpty(annualRegistrationDto.getConventionId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
            }
            Convention convention = conventionMapper.selectById(annualRegistrationDto.getConventionId());
            if (GeneralTool.isEmpty(convention)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            if (GeneralTool.isEmpty(annualRegistrationDto)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
            }

            boolean flag = true;
            if (GeneralTool.isNotEmpty(annualRegistrationDto.getRegistrationOptions())){
                String[] places = annualRegistrationDto.getRegistrationOptions().split(";");
                //自旋次数
                int spinCount = 0;
                placeList = Arrays.stream(places).collect(Collectors.toList());
                //Mount Hua;Hulunbuir;Penglai;Jiuzhai Valley 对选择的地区加缓存锁
                //设置自旋
                do {
                    if (placeList.contains("Mount Hua")){
                        flag = getRedis.setNx(CacheKeyConstants.GOPRO_PLACE_LOCK_KEY + "Mount Hua", 1, 10L) && flag;
                    }
                    if (placeList.contains("Hulunbuir")){
                        flag = getRedis.setNx(CacheKeyConstants.GOPRO_PLACE_LOCK_KEY + "Hulunbuir", 1, 10L) && flag;
                    }
                    if (placeList.contains("Penglai")){
                        flag = getRedis.setNx(CacheKeyConstants.GOPRO_PLACE_LOCK_KEY + "Penglai", 1, 10L) && flag;
                    }
                    if (placeList.contains("Jiuzhai Valley")){
                        flag = getRedis.setNx(CacheKeyConstants.GOPRO_PLACE_LOCK_KEY + "Jiuzhai Valley", 1, 10L) && flag;
                    }
                    if (!flag){
                        Thread.sleep(200);
                    }
                    spinCount++;
                } while(!flag&&spinCount<3);
            }

            if(flag){
                //拿到了锁
                if (GeneralTool.isNotEmpty(placeList)){
                    List<String> exceededPlaceList = Lists.newArrayList();
                    for (String s : placeList) {
                        int count = conventionRegistrationMapper.getPlaceCount(s, annualRegistrationDto.getConventionId());
                        if (count>=25){
                            exceededPlaceList.add(s);
                        }
                    }

                    if (GeneralTool.isNotEmpty(exceededPlaceList)){
                        StringJoiner sj = new StringJoiner(",");
                        for (String s : exceededPlaceList) {
                            if ("Mount Hua".equals(s)){
                                sj.add("华山");
                            }
                            if ("Hulunbuir".equals(s)){
                                sj.add("呼伦贝尔");
                            }
                            if ("Penglai".equals(s)){
                                sj.add("蓬莱");
                            }
                            if ("Jiuzhai Valley".equals(s)){
                                sj.add("九寨沟");
                            }
                        }
                        throw new GetServiceException(sj + LocaleMessageUtils.getMessage("the_registration_quota_is_full"));
                    }
                }

                ConventionRegistration conventionRegistration = new ConventionRegistration();
                conventionRegistration.setProviderName(annualRegistrationDto.getEducationInstitutionName());
                conventionRegistration.setContactPersonName(annualRegistrationDto.getName());
                conventionRegistration.setContactTel(annualRegistrationDto.getTel());
                conventionRegistration.setContactEmail(annualRegistrationDto.getEmail());
                conventionRegistration.setSummaryFee(annualRegistrationDto.getRegistrationOptions());
                conventionRegistration.setFkConventionId(annualRegistrationDto.getConventionId());
                if (GeneralTool.isEmpty(conventionRegistration.getReceiptCode())) {
                    //没有回执码得时候，自动获取，重复则继续获取，直到不重复
                    boolean result = true;
                    while (result) {
                        String randomCode = MyStringUtils.getRandomCode();
                        if (!conventionSponsorService.validateReceiptCode(randomCode)) {
                            result = false;
                            conventionRegistration.setReceiptCode(randomCode);
                        }
                    }
                }
                conventionRegistration.setBoothName(annualRegistrationDto.getEducationInstitutionName());
                conventionRegistration.setStatus(0);
                conventionRegistration.setGmtCreate(new Date());
                conventionRegistration.setGmtCreateUser("[form]");
                int length = annualRegistrationDto.getRegistrationOptions().split(";").length;
                if (GeneralTool.isNotEmpty(annualRegistrationDto.getRegistrationOptions())) {
                    int money = length * 12000;
                    conventionRegistration.setRegistrationFee(new BigDecimal(money));
                }
                conventionRegistration.setFkCurrencyTypeNum("CNY");

                formCommonService.updateUserInfoToEntity(conventionRegistration);
                conventionRegistrationMapper.insertSelective(conventionRegistration);

                conventionRegistration.setBoothNum(MyStringUtils.getConventionBoothNum(conventionRegistration.getId()));
                formCommonService.updateUserInfoToEntity(conventionRegistration);
                conventionRegistrationMapper.updateById(conventionRegistration);

                //中间表对象
                ConventionRegistrationAreaCountry conventionRegistrationAreaCountry = new ConventionRegistrationAreaCountry();
                //fk报名id即刚刚插入成功的报名表返回的id
                conventionRegistrationAreaCountry.setFkConventionRegistrationId(conventionRegistration.getId());
                conventionRegistrationAreaCountry.setFkAreaCountryKey("CHN");
                formCommonService.updateUserInfoToEntity(conventionRegistrationAreaCountry);
                conventionRegistrationAreaCountryMapper.insert(conventionRegistrationAreaCountry);

                List<ConventionPersonDto> conventionPersonDtos = new ArrayList<>();
                if (GeneralTool.isNotEmpty(annualRegistrationDto.getAttendeesList())) {
                    List<AttendeesDto> attendeesList = annualRegistrationDto.getAttendeesList();
                    for (AttendeesDto attendeesDto : attendeesList) {
                        ConventionPersonDto conventionPersonDto = new ConventionPersonDto();
                        conventionPersonDto.setGender(attendeesDto.getGender());
                        conventionPersonDto.setName(attendeesDto.getEnglishName());
                        conventionPersonDto.setNameChn(attendeesDto.getChineseName());
                        conventionPersonDto.setTitle(attendeesDto.getPosition());
                        conventionPersonDto.setTel(attendeesDto.getTel());
                        conventionPersonDto.setEmail(attendeesDto.getEmail());
                        conventionPersonDto.setType(0);
                        conventionPersonDto.setFkConventionId(annualRegistrationDto.getConventionId());
                        List<Long> selectProjectIds = new ArrayList<>(1);
                        selectProjectIds.add(conventionRegistration.getId());
                        conventionPersonDto.setSelectProjectIds(selectProjectIds);

                        conventionPersonDtos.add(conventionPersonDto);
                    }
                    for (ConventionPersonDto conventionPersonDto : conventionPersonDtos) {
                        addConventionPerson(conventionPersonDto);
                    }
                }
            }else {
                //缓存锁未释放
                throw new GetServiceException(LocaleMessageUtils.getMessage("save_error"));
            }

            // 手动提交事务  end
            transactionManager.commit(transaction);
            // 手动提交事务  start
        }// 异常回滚
        catch (Exception e) {
            // 手动回滚事务  start
            transactionManager.rollback(transaction);
            // 手动回滚事务  end
            throw new GetServiceException(e.getMessage());
        }finally {
            //释放锁
            if (GeneralTool.isNotEmpty(annualRegistrationDto.getRegistrationOptions())){
                if (placeList.contains("Mount Hua")){
                    getRedis.del(CacheKeyConstants.GOPRO_PLACE_LOCK_KEY + "Mount Hua");
                }
                if (placeList.contains("Hulunbuir")){
                    getRedis.del(CacheKeyConstants.GOPRO_PLACE_LOCK_KEY + "Hulunbuir");
                }
                if (placeList.contains("Penglai")){
                    getRedis.del(CacheKeyConstants.GOPRO_PLACE_LOCK_KEY + "Penglai");
                }
                if (placeList.contains("Jiuzhai Valley")){
                    getRedis.del(CacheKeyConstants.GOPRO_PLACE_LOCK_KEY + "Jiuzhai Valley");
                }
            }
        }

    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public AnnualRegistrationVo updateRegistration(AnnualRegistrationDto annualRegistrationDto) {
        if (GeneralTool.isEmpty(annualRegistrationDto.getConventionId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Convention convention = conventionMapper.selectById(annualRegistrationDto.getConventionId());
        if (GeneralTool.isEmpty(convention)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (GeneralTool.isEmpty(annualRegistrationDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        AnnualRegistrationVo annualRegistrationVo = BeanCopyUtils.objClone(annualRegistrationDto, AnnualRegistrationVo::new);

        ConventionRegistration conventionRegistration = new ConventionRegistration();
        conventionRegistration.setId(annualRegistrationDto.getId());
        conventionRegistration.setProviderName(annualRegistrationDto.getEducationInstitutionName());
        conventionRegistration.setContactPersonName(annualRegistrationDto.getName());
        conventionRegistration.setContactPersonName(annualRegistrationDto.getName());
        conventionRegistration.setContactTel(annualRegistrationDto.getTel());
        conventionRegistration.setContactEmail(annualRegistrationDto.getEmail());
        conventionRegistration.setSummaryFee(annualRegistrationDto.getRegistrationOptions());
        conventionRegistration.setBoothName(annualRegistrationDto.getEducationInstitutionName());
        formCommonService.updateUserInfoToEntity(conventionRegistration);
        conventionRegistrationMapper.updateById(conventionRegistration);

        List<ConventionPersonDto> conventionPersonDtos = new ArrayList<>();
        List<AttendeesVo> attendeesVoList = new ArrayList<>();
        if (GeneralTool.isNotEmpty(annualRegistrationDto.getAttendeesList())) {
            List<AttendeesDto> attendeesList = annualRegistrationDto.getAttendeesList();
            for (AttendeesDto attendeesDto : attendeesList) {
                ConventionPersonDto conventionPersonDto = new ConventionPersonDto();
                conventionPersonDto.setId(attendeesDto.getId());
                conventionPersonDto.setGender(attendeesDto.getGender());
                conventionPersonDto.setName(attendeesDto.getEnglishName());
                conventionPersonDto.setNameChn(attendeesDto.getChineseName());
                conventionPersonDto.setTitle(attendeesDto.getPosition());
                conventionPersonDto.setTel(attendeesDto.getTel());
                conventionPersonDto.setTel(attendeesDto.getEmail());
                conventionPersonDto.setType(0);
                conventionPersonDto.setFkConventionId(annualRegistrationDto.getConventionId());
                conventionPersonDtos.add(conventionPersonDto);
            }
            for (ConventionPersonDto conventionPersonDto : conventionPersonDtos) {
                AttendeesVo attendeesVo = updateConventionPerson(conventionPersonDto);
                attendeesVoList.add(attendeesVo);
            }
        }
        annualRegistrationVo.setAttendeesList(attendeesVoList);
        return annualRegistrationVo;
    }

    /**
     * 根据参会人员的id删除信息
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteConventionPerson(Long id) {
        //删除 参会人-报名关系表
        deletePersonTable(id);
        //校验参会人
        ConventionPerson conventionPerson = conventionPersonMapper.selectById(id);
        if (GeneralTool.isEmpty(conventionPerson)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("convention_person_not_found"));
        }
        //删除 参会人-流程关系表
        List<ConventionPersonProcedure> conventionPersonProcedures = conventionPersonProcedureMapper.selectList(Wrappers.<ConventionPersonProcedure>query().lambda().eq(ConventionPersonProcedure::getFkConventionPersonId, id));
        if (GeneralTool.isNotEmpty(conventionPersonProcedures)) {
            conventionPersonProcedureMapper.delete(Wrappers.<ConventionPersonProcedure>query().lambda().eq(ConventionPersonProcedure::getFkConventionPersonId, id));
        }
        //删除 参会人表
        conventionPersonMapper.deleteById(id);
        // 在删除参会人员时，同步删除关系表数据
        conventionPersonService.deleteAssociationTable(id, conventionPerson);
    }


    /**
     * @param annualRegistrationDto
     * @param page
     * @return
     */
    @Override
    public List<AnnualRegistrationVo> getAnnualRegistrationDtos(AnnualRegistrationDto annualRegistrationDto, SearchBean<AnnualRegistrationDto> page) {
        LambdaQueryWrapper<ConventionRegistration> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ConventionRegistration::getFkConventionId, annualRegistrationDto.getConventionId());
        if (GeneralTool.isNotEmpty(annualRegistrationDto.getEducationInstitutionName())) {
            lambdaQueryWrapper.like(ConventionRegistration::getProviderName, annualRegistrationDto.getEducationInstitutionName());
        }
        lambdaQueryWrapper.orderByDesc(ConventionRegistration::getGmtCreate);
        IPage<ConventionRegistration> iPage = conventionRegistrationMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<ConventionRegistration> conventionRegistrations = iPage.getRecords();
        page.setAll((int) iPage.getTotal());

        Map<Long, List<ConventionPersonVo>> map;
        Set<Long> ids = conventionRegistrations.stream().map(ConventionRegistration::getId).collect(Collectors.toSet());

        List<ConventionPersonVo> conventionPersonRegistrations = conventionPersonRegistrationMapper.getConventionPersonByRegistrationIds(ids);
        map = conventionPersonRegistrations.stream().collect(Collectors.groupingBy(ConventionPersonVo::getFkConventionRegistrationId));

        List<AnnualRegistrationVo> annualRegistrationVos = new ArrayList<>();
        if (GeneralTool.isNotEmpty(conventionRegistrations)) {
            for (ConventionRegistration conventionRegistration : conventionRegistrations) {
                AnnualRegistrationVo annualRegistrationVo = new AnnualRegistrationVo();
                annualRegistrationVo.setId(conventionRegistration.getId());
                annualRegistrationVo.setEducationInstitutionName(conventionRegistration.getProviderName());
                annualRegistrationVo.setName(conventionRegistration.getContactPersonName());
                annualRegistrationVo.setTel(conventionRegistration.getContactTel());
                annualRegistrationVo.setEmail(conventionRegistration.getContactEmail());
                annualRegistrationVo.setConventionId(conventionRegistration.getFkConventionId());
                annualRegistrationVo.setRegistrationOptions(conventionRegistration.getSummaryFee());

                List<AttendeesVo> attendeesList = new ArrayList<>();
                List<ConventionPersonVo> conventionPersonVoList = map.get(conventionRegistration.getId());
                if (GeneralTool.isEmpty(conventionPersonVoList)) {
                    annualRegistrationVo.setIsParticipant(0);
                    annualRegistrationVo.setAttendeesList(attendeesList);
                } else {
                    for (ConventionPersonVo conventionPersonVo : conventionPersonVoList) {
                        AttendeesVo attendeesVo = new AttendeesVo();
                        attendeesVo.setGender(conventionPersonVo.getGender());
                        attendeesVo.setTel(conventionPersonVo.getTel());
                        attendeesVo.setEmail(conventionPersonVo.getEmail());
                        attendeesVo.setId(conventionPersonVo.getId());
                        attendeesVo.setPosition(conventionPersonVo.getTitle());
                        String[] nameSplit = conventionPersonVo.getName().split(" ");
                        if (nameSplit.length > 1) {
                            attendeesVo.setFirstNameCapitals(nameSplit[0]);
                            attendeesVo.setLastNameCapitals(nameSplit[1]);
                        } else {
                            attendeesVo.setFirstNameCapitals(conventionPersonVo.getName());
                        }
                        String[] nameChnSplit = conventionPersonVo.getNameChn().split(" ");
                        if (nameChnSplit.length > 1) {
                            attendeesVo.setFirstNameChinese(nameChnSplit[0]);
                            attendeesVo.setLastNameChinese(nameChnSplit[1]);
                        } else {
                            attendeesVo.setFirstNameChinese(conventionPersonVo.getNameChn());
                        }
                        attendeesList.add(attendeesVo);
                    }
                    annualRegistrationVo.setIsParticipant(1);
                    annualRegistrationVo.setAttendeesList(attendeesList);
                }
                annualRegistrationVos.add(annualRegistrationVo);
            }
        }

        return annualRegistrationVos;
    }

    @Override
    public void exportRegistrationExcel(HttpServletResponse response, AnnualRegistrationDto annualRegistrationDto) {
        SearchBean<AnnualRegistrationDto> page = new SearchBean<AnnualRegistrationDto>();
        List<AnnualRegistrationVo> annualRegistrationVos = getAnnualRegistrationDtos(annualRegistrationDto, page);
        List<AnnualRegistrationVo> annualRegistrationVoList = new ArrayList<>(annualRegistrationVos);
        if (page.getTotalPage() >= 2) {
            for (int i = 2; i < page.getTotalPage() + 1; i++) {
                page.setCurrentPage(i);
                annualRegistrationVoList.addAll(getAnnualRegistrationDtos(annualRegistrationDto, page));
            }
        }

        List<AnnualRegistrationExportVo> annualRegistrationExportVos = BeanCopyUtils.copyListProperties(annualRegistrationVos, AnnualRegistrationExportVo::new);

        FileUtils.exportExcelNotWrapText(response, annualRegistrationExportVos, "AnnualRegistrationExportVo", AnnualRegistrationExportVo.class);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addAgentConventionPerson(AgentConventionPersonSaveDto agentConventionPersonSaveDto) {
        if(GeneralTool.isEmpty(agentConventionPersonSaveDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Convention convention = conventionMapper.selectById(agentConventionPersonSaveDto.getFkConventionId());
        if (GeneralTool.isEmpty(convention)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }

        //手机重复校验
        List<ConventionPerson> conventionPersonList = conventionPersonMapper.selectList(Wrappers.<ConventionPerson>lambdaQuery()
                .eq(ConventionPerson::getFkConventionId, agentConventionPersonSaveDto.getFkConventionId())
                .eq(ConventionPerson::getTel, agentConventionPersonSaveDto.getTel()));

        if (GeneralTool.isNotEmpty(conventionPersonList)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("this_phone_number_has_already_been_used"));
        }

        ConventionPerson conventionPerson = BeanCopyUtils.objClone(agentConventionPersonSaveDto, ConventionPerson::new);
//        String remark = "所属区域："+agentConventionPersonSaveDto.getAreaCountryName()+"，" + agentConventionPersonSaveDto.getAreaStateName() + "，"+ agentConventionPersonSaveDto.getAreaCityName()+";";
        String remark = "常驻城市："+ agentConventionPersonSaveDto.getResidentCountryName()+"，" + agentConventionPersonSaveDto.getResidentStateName() + "，"+ agentConventionPersonSaveDto.getResidentCityName()+";";
        if (GeneralTool.isNotEmpty(agentConventionPersonSaveDto.getIdType())){
            if (agentConventionPersonSaveDto.getIdType().equals(1)){
                conventionPerson.setPassportNum(agentConventionPersonSaveDto.getIdCardNum());
                conventionPerson.setIdCardNum(null);
            }else if (agentConventionPersonSaveDto.getIdType().equals(2)){
                remark = remark + "身份类型：港澳回乡证";
            }else if (agentConventionPersonSaveDto.getIdType().equals(3)){
                remark = remark + "身份类型：台胞证";
            }
        }
        if (GeneralTool.isEmpty(conventionPerson.getName())){
            conventionPerson.setName(conventionPerson.getNameChn());
        }
        conventionPerson.setType(1);
        conventionPerson.setIsVip(false);
        conventionPerson.setIsAttend(false);
        conventionPerson.setRemark(remark);
        conventionPerson.setGmtCreate(new Date());
        conventionPerson.setGmtCreateUser("[form]");
        int i = conventionPersonMapper.insert(conventionPerson);
        if (i <= 0){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }

        List<StaffBdCode> bdCodes = staffBdCodeService.list(Wrappers.<StaffBdCode>lambdaQuery()
                .eq(StaffBdCode::getFkStaffId, agentConventionPersonSaveDto.getFkStaffId()));

        if (GeneralTool.isNotEmpty(bdCodes)){
            conventionPerson.setBdCode(bdCodes.get(0).getBdCode());
        }

        //生成编号
        String num = MyStringUtils.getNumCommon("B",conventionPerson.getId(),8);
        conventionPerson.setNum(num);
        i = conventionPersonMapper.updateById(conventionPerson);
        if (i <= 0){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }

//        //添加关系表
//        ConventionPersonAgent conventionPersonAgent = new ConventionPersonAgent();
//        conventionPersonAgent.setFkConventionPersonId(conventionPerson.getId());
//        conventionPersonAgent.setFkAgentId(agentConventionPersonSaveDto.getFkStaffId());
//        conventionPersonAgent.setGmtCreate(new Date());
//        conventionPersonAgent.setGmtCreateUser("[form]");
//        i = conventionPersonAgentMapper.insert(conventionPersonAgent);
//        if (i <= 0){
//            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
//        }
        //加入峰会培训流程
        ConventionPersonProcedure conventionPersonProcedure = new ConventionPersonProcedure();
        conventionPersonProcedure.setFkConventionPersonId(conventionPerson.getId());
        conventionPersonProcedure.setFkConventionProcedureId(193L);
        conventionPersonProcedure.setGmtCreate(new Date());
        conventionPersonProcedure.setGmtCreateUser("[form]");
        conventionPersonProcedureMapper.insert(conventionPersonProcedure);
        //安排培训桌台
        //检查桌子是否还有座位
        ConventionTable conventionTable = conventionTableMapper.selectOne(Wrappers.<ConventionTable>lambdaQuery().eq(ConventionTable::getFkConventionId, 25)
                .eq(ConventionTable::getFkTableTypeKey, agentConventionPersonSaveDto.getFkTableTypeKey())
                .eq(ConventionTable::getTableNum, agentConventionPersonSaveDto.getTableNum()));
        //桌子座位数
        Integer seatCount = conventionTable.getSeatCount();
        //峰会桌子缓存锁
        String conventionTableLockKey = CacheKeyConstants.CONVENTION_TABLE_LOCK_KEY + conventionTable.getId();
        if (getRedis.setNx(conventionTableLockKey, 1,10L)) {
            //峰会桌子已入座数量缓存key
//            String conventionSeatCountKey = CacheKeyConstants.CONVENTION_SEATED_SEAT_COUNT_KEY + conventionTable.getId();
            //桌子已入座数
//            Object seatedSeats = getRedis.get(conventionSeatCountKey);
//            try {
//                if (seatedSeats == null) {
//                    while (true) {
//                        seatedSeats = getRedis.hGetAll(conventionSeatCountKey);
//                        if (seatedSeats != null) {
//                            break;
//                        }
//                        if (getRedis.setNx(conventionSeatCountLockKey, 1, 10L)) {
//                            Integer count = conventionTablePersonMapper.selectCount(Wrappers.<ConventionTablePerson>lambdaQuery().eq(ConventionTablePerson::getFkConventionTableId, conventionTable.getId()));
//                            getRedis.incrBy(conventionSeatCountKey, Long.valueOf(count));
//                            getRedis.expire(conventionSeatCountKey, 604800);
//                        }
//                    }
//                }
//                //该桌子已分配数量
//                Long allocatedQuantity = getRedis.incrBy(conventionSeatCountKey, 1L);
//                if (allocatedQuantity > seatCount) {
//                    getRedis.decrBy(conventionSeatCountKey, 1L);
//                    throw new GetServiceException(LocaleMessageUtils.getMessage("full_seats_available"));
//                }
//            } catch (Exception e) {
//                getRedis.del(conventionSeatCountKey);
//                e.printStackTrace();
//                throw new GetServiceException(LocaleMessageUtils.getMessage("save_error"));
//            }

            saveConventionTablePerson(conventionPerson, conventionTable, seatCount, conventionTableLockKey);
        } else {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            //自旋
            saveConventionTablePerson(conventionPerson, conventionTable, seatCount, conventionTableLockKey);
        }
    }


    /**
     * 保存桌子席位人
     *
     * @Date 15:07 2022/11/7
     * <AUTHOR>
     */
    private void saveConventionTablePerson(ConventionPerson conventionPerson, ConventionTable conventionTable, Integer seatCount, String conventionTableLockKey) {
        //该桌子已分配数量
        Integer allocatedQuantity = conventionTablePersonMapper.selectCount(Wrappers.<ConventionTablePerson>lambdaQuery().eq(ConventionTablePerson::getFkConventionTableId, conventionTable.getId()));
        allocatedQuantity++;
        if (allocatedQuantity > seatCount) {
//                getRedis.decrBy(conventionSeatCountKey, 1L);
            throw new GetServiceException(LocaleMessageUtils.getMessage("full_seats_available"));
        }
        ConventionTablePerson conventionTablePerson = new ConventionTablePerson();
        conventionTablePerson.setFkConventionTableId(conventionTable.getId());
        conventionTablePerson.setFkConventionPersonId(conventionPerson.getId());
        conventionTablePerson.setGmtCreate(new Date());
        conventionTablePerson.setGmtCreateUser("[form]");
        conventionTablePersonMapper.insert(conventionTablePerson);
        getRedis.del(conventionTableLockKey);
    }


    /**
     * 2022GEA峰会 代理参会人员报名培训桌下拉框
     *
     * @Date 17:58 2022/11/2
     * <AUTHOR>
     */
    @Override
    public List<ConventionTableVo> agentConventionTableList() {
        List<ConventionTable> conventionTables = conventionTableMapper.selectList(Wrappers.<ConventionTable>lambdaQuery().eq(ConventionTable::getFkConventionId, 25));
        List<ConventionTableVo> conventionTableVoList = BeanCopyUtils.copyListProperties(conventionTables, ConventionTableVo::new);
        for (ConventionTableVo conventionTableVo : conventionTableVoList) {
            //桌子数量
            Integer seatCount = conventionTableVo.getSeatCount();
            //该桌子已分配数量
            Integer allocatedQuantity = conventionTablePersonMapper.selectCount(Wrappers.<ConventionTablePerson>lambdaQuery().eq(ConventionTablePerson::getFkConventionTableId, conventionTableVo.getId()));
            conventionTableVo.setRemainingSeatCount(seatCount - allocatedQuantity);
        }
        return conventionTableVoList;
    }

    /**
     * 获取早鸟价配置
     *
     * @Date 10:00 2023/10/25
     * <AUTHOR>
     */
    @Override
    public EarlyBirdConfigVo getEarlyBirdConfig() {
        ConfigVo configVo = permissionCenterClient.getConfigByKey(ProjectKeyEnum.GEA_CONVENTION_REGISTRATION.key).getData();
        EarlyBirdConfigVo earlyBirdConfigVo = new EarlyBirdConfigVo();
        if (DateUtil.compare(DateUtil.date(), DateUtil.parse(configVo.getValue2())) < 0) {
            earlyBirdConfigVo.setEarlyBird(true);
        } else {
            earlyBirdConfigVo.setEarlyBird(false);
        }
        earlyBirdConfigVo.setIsEarlyBirdDiscount(new BigDecimal(configVo.getValue3()));
        return earlyBirdConfigVo;
    }

    /**
     * 删除参会人关联表
     *
     * @param id
     */
    private void deletePersonTable(Long id) {
        formCommonService.deleteTable(id);
    }

    /**
     * 新增参会人
     *
     * @param conventionPersonDto
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addConventionPerson(ConventionPersonDto conventionPersonDto) {
        if (GeneralTool.isEmpty(conventionPersonDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ConventionPerson conventionPerson = BeanCopyUtils.objClone(conventionPersonDto, ConventionPerson::new);
        if (!validatedTel(conventionPersonDto)) {
            throw new GetServiceException(conventionPersonDto.getName() + " " + LocaleMessageUtils.getMessage("phone_number_is_been_used") + "（" + conventionPersonDto.getName() + " " + "手机号已经被绑定！）");
        }
        formCommonService.updateUserInfoToEntity(conventionPerson);
        conventionPersonMapper.insertSelective(conventionPerson);
        //插入后返回的参展人员id
        Long id = conventionPerson.getId();
        //关联表的保存
        insertTable(id, conventionPersonDto);
        conventionPerson.setNum(MyStringUtils.getConventionPersonNum(id));
        formCommonService.updateUserInfoToEntity(conventionPerson);
        conventionPersonMapper.updateById(conventionPerson);
        return conventionPerson.getId();
    }


    /**
     * 关联表的保存
     */
    private void insertTable(Long id, ConventionPersonDto conventionPersonDto) {
        switch (conventionPersonDto.getType()) {
            case 0:
                //0校方-r_convention_person_registration 中间表对象
                ConventionPersonRegistration conventionPersonRegistration = new ConventionPersonRegistration();
                //参展人员id为刚插入成功返回的id
                conventionPersonRegistration.setFkConventionPersonId(id);
                //所属报名名册的id
                conventionPersonRegistration.setFkConventionRegistrationId(conventionPersonDto.getSelectProjectIds().get(0));
                if (GeneralTool.isNotEmpty(conventionPersonRegistration.getFkConventionRegistrationId())) {
                    formCommonService.addUserInfoToEntity(conventionPersonRegistration);
                    conventionPersonRegistrationMapper.insert(conventionPersonRegistration);
                }
                break;
            case 1:
                //1代理-r_convention_person_agent 中间表对象
                ConventionPersonAgent conventionPersonAgent = new ConventionPersonAgent();
                //参展人员id为刚插入成功返回的id
                conventionPersonAgent.setFkConventionPersonId(id);
                //所属代理的id
                conventionPersonAgent.setFkAgentId(conventionPersonDto.getSelectProjectIds().get(0));
                if (GeneralTool.isNotEmpty(conventionPersonAgent.getFkAgentId())) {
                    formCommonService.addUserInfoToEntity(conventionPersonAgent);
                    conventionPersonAgentMapper.insert(conventionPersonAgent);
                }
                break;
            case 2:
                int a = 2;
                if (conventionPersonDto.getSelectProjectIds().size() < a) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
                }
                //2嘉宾-r_convention_person_institution & r_convention_person_institution_provider 中间表对象
                ConventionPersonInstitutionProvider conventionPersonInstitutionProvider = new ConventionPersonInstitutionProvider();
                ConventionPersonInstitution conventionPersonInstitution = new ConventionPersonInstitution();
                //参展人员id为刚插入成功返回的id
                conventionPersonInstitutionProvider.setFkConventionPersonId(id);
                conventionPersonInstitution.setFkConventionPersonId(id);
                //所属学校提供商的id
                conventionPersonInstitutionProvider.setFkInstitutionProviderId(conventionPersonDto.getSelectProjectIds().get(0));
                //所属学校的id
                conventionPersonInstitution.setFkInstitutionId(conventionPersonDto.getSelectProjectIds().get(1));

                if (GeneralTool.isNotEmpty(conventionPersonInstitutionProvider.getFkInstitutionProviderId())) {
                    formCommonService.addUserInfoToEntity(conventionPersonInstitutionProvider);
                    conventionPersonInstitutionProviderMapper.insert(conventionPersonInstitutionProvider);
                }

                if (GeneralTool.isNotEmpty(conventionPersonInstitution.getFkInstitutionId())) {
                    formCommonService.addUserInfoToEntity(conventionPersonInstitution);
                    conventionPersonInstitutionMapper.insert(conventionPersonInstitution);
                }
                break;
            case 3:
                //3员工-r_convention_person_staff 中间表对象
                ConventionPersonStaff conventionPersonStaff = new ConventionPersonStaff();
                //参展人员id为刚插入成功返回的id
                conventionPersonStaff.setFkConventionPersonId(id);
                //所属员工id
                conventionPersonStaff.setFkStaffId(conventionPersonDto.getSelectProjectIds().get(0));
                if (GeneralTool.isNotEmpty(conventionPersonStaff.getFkStaffId())) {
                    formCommonService.addUserInfoToEntity(conventionPersonStaff);
                    conventionPersonStaffMapper.insert(conventionPersonStaff);
                }
                break;
            case 4:
                break;
            default:
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
    }

    /**
     * 验证手机唯一
     *
     * @param conventionPersonDto
     * @return
     */
    private Boolean validatedTel(ConventionPersonDto conventionPersonDto) {
        LambdaQueryWrapper<ConventionPerson> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ConventionPerson::getFkConventionId, conventionPersonDto.getFkConventionId());
        lambdaQueryWrapper.eq(ConventionPerson::getTel, conventionPersonDto.getTel());
        if (GeneralTool.isNotEmpty(conventionPersonDto.getId())) {
            //修改情况时
            lambdaQueryWrapper.ne(ConventionPerson::getId, conventionPersonDto.getId());
        }
        List<ConventionPerson> conventionPeople = conventionPersonMapper.selectList(lambdaQueryWrapper);
        return GeneralTool.isEmpty(conventionPeople);
    }


    public AttendeesVo updateConventionPerson(ConventionPersonDto conventionPersonDto) {
        if (conventionPersonDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ConventionPerson result = conventionPersonMapper.selectById(conventionPersonDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ConventionPerson conventionPerson = BeanCopyUtils.objClone(conventionPersonDto, ConventionPerson::new);
        if (!validatedTel(conventionPersonDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("phone_number_is_been_used"));
        }
        formCommonService.updateUserInfoToEntity(conventionPerson);
        conventionPersonMapper.updateById(conventionPerson);
        //关联表的修改
        deleteTable(conventionPersonDto.getId(), result);
        insertTable(conventionPersonDto.getId(), conventionPersonDto);

        AttendeesVo attendeesVo = new AttendeesVo();
        attendeesVo.setId(conventionPerson.getId());
        attendeesVo.setGender(conventionPerson.getGender());
        String[] nameSplit = conventionPerson.getName().split(" ");
        if (nameSplit.length > 1) {
            attendeesVo.setFirstNameCapitals(nameSplit[0]);
            attendeesVo.setLastNameCapitals(nameSplit[1]);
        } else {
            attendeesVo.setFirstNameCapitals(conventionPerson.getName());
        }
        String[] nameChnSplit = conventionPerson.getNameChn().split(" ");
        if (nameChnSplit.length > 1) {
            attendeesVo.setFirstNameChinese(nameChnSplit[0]);
            attendeesVo.setLastNameChinese(nameChnSplit[1]);
        } else {
            attendeesVo.setFirstNameChinese(conventionPerson.getNameChn());
        }
        attendeesVo.setTel(conventionPerson.getTel());
        attendeesVo.setEmail(conventionPerson.getEmail());
        return attendeesVo;
    }

    /**
     * 关联表的删除
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteTable(Long id, ConventionPerson conventionPerson) {
        Integer type = conventionPerson.getType();
        switch (type) {
            case 0:
                conventionPersonRegistrationMapper.delete(Wrappers.<ConventionPersonRegistration>lambdaQuery().eq(ConventionPersonRegistration::getFkConventionPersonId, id));
                break;
            case 1:
                conventionPersonAgentMapper.delete(Wrappers.<ConventionPersonAgent>lambdaQuery().eq(ConventionPersonAgent::getFkConventionPersonId, id));

                break;
            case 2:
                conventionPersonInstitutionMapper.delete(Wrappers.<ConventionPersonInstitution>lambdaQuery().eq(ConventionPersonInstitution::getFkConventionPersonId, id));
                conventionPersonInstitutionProviderMapper.delete(Wrappers.<ConventionPersonInstitutionProvider>lambdaQuery().eq(ConventionPersonInstitutionProvider::getFkConventionPersonId, id));
                break;
            case 3:
                conventionPersonStaffMapper.delete(Wrappers.<ConventionPersonStaff>lambdaQuery().eq(ConventionPersonStaff::getFkConventionPersonId, id));
                break;
            case 4:

                break;
            default:
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
    }
}
