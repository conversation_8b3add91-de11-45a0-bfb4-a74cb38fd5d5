package com.get.salecenter.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.institutioncenter.entity.ContractFormula;
import com.get.permissioncenter.entity.Company;
import com.get.salecenter.dto.BdStudentStatisticalComparisonDto;
import com.get.salecenter.dto.ClientStudentDto;
import com.get.salecenter.dto.CurrentStudentApplicationStatusStatisticsDto;
import com.get.salecenter.dto.StudentCourseStatisticsSearchDto;
import com.get.salecenter.dto.StudentDto;
import com.get.salecenter.dto.query.StudentListQueryDto;
import com.get.salecenter.entity.Student;
import com.get.salecenter.vo.AgentVo;
import com.get.salecenter.vo.AgentsBindingVo;
import com.get.salecenter.vo.AiStudentInfoVo;
import com.get.salecenter.vo.SelCountVo;
import com.get.salecenter.vo.SelItem;
import com.get.salecenter.vo.StudentAgentBindingNewVo;
import com.get.salecenter.vo.StudentAgentEmailVo;
import com.get.salecenter.vo.StudentGraduationBackgroundVo;
import com.get.salecenter.vo.StudentItemStatusVo;
import com.get.salecenter.vo.StudentVo;
import java.util.Date;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface StudentMapper extends GetMapper<Student> {


    Company getCompanyNameByStudentId(Long companyId);

    /**
     * feign调用 根据输入的名称 模糊查询对应的学生id
     *
     * @param name
     * @return
     */
    List<Long> getStudentIds(String name);

    /**
     * feign调用 根据输入的学生id 模糊查询对应的名称
     *
     * @param id
     * @return
     */
    String getStudentNameById(Long id);

    List<Student> getStudentNationalityByIds(@Param("ids") Set<Long> ids);

    Long getStudentNationalityById(Long id);
    /**
     * @return java.lang.Integer
     * @Description: 根据提供商id和课程id查询学生数
     * @Param [providerId, courseIds]
     * <AUTHOR>
     **/
    Integer getCountByProviderAndCourse(@Param(value = "contractFormula") ContractFormula contractFormula, @Param(value = "courseIds") List<Long> courseIds);

    /**
     * @return java.lang.Integer
     * @Description: 根据国家获取申请学习计划的学生数
     * @Param [id]
     * <AUTHOR>
     **/
//    @DS("saledb-doris")
    Integer getCountByCountry(@Param(value = "companyIds") List<Long> companyIds, @Param(value = "id") Long id, @Param(value = "year") String year, @Param(value = "isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding);

    List<SelItem> getCountByCountryTwo(@Param(value = "companyIds") List<Long> companyIds,
                                    @Param("fkAreaCountryIds") List<Long> fkAreaCountryIds,
                                    @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                    @Param(value = "year") String year,
                                    @Param("isStudentAdmin") Boolean isStudentAdmin,
                                    @Param(value = "isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                    @Param(value = "isBd") Boolean isBd,
                                    @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                    @Param("staffBoundBdIds") List<Long> staffBoundBdIds);
    /**
     * @return java.lang.Integer
     * @Description: 根据州省获取申请学习计划的学生数
     * @Param [id]
     * <AUTHOR>
     **/
    @DS("saledb-doris")
    List<SelCountVo> getCountByState(@Param(value = "companyIds") List<Long> companyIds, @Param("areaCountryIds") List<Long> areaCountryIds,
                                     @Param(value = "ids") List<Long> ids, @Param("year") String year, @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                     @Param("staffFollowerIds") List<Long> staffFollowerIds, @Param("isStudentAdmin") Boolean isStudentAdmin, @Param("isBd") Boolean isBd,
                                     @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                     @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * 筛选后的学生再根据目标国家进行第二次筛选
     *
     * @param studentIds      筛选后的学生
     * @param targetCountryId 目标国家
     * @param failureReasonId 失败原因id
     * @param isDeferEntrance 是否延迟入学
     * @return
     */
    List<StudentVo> getStudentsByTargetCountryAndStudentIds(IPage<StudentVo> iPage,
                                                            @Param(value = "studentIds") Set<Long> studentIds,
                                                            @Param(value = "targetCountryId") Long targetCountryId,
                                                            @Param(value = "failureReasonId") Long failureReasonId,
                                                            @Param(value = "isDeferEntrance") Boolean isDeferEntrance,
                                                            @Param(value = "statusBeginTime") Date statusBeginTime,
                                                            @Param(value = "statusEndTime") Date statusEndTime,
                                                            @Param(value = "beginOpeningTime") Date beginOpeningTime,
                                                            @Param(value = "endOpeningTime") Date endOpeningTime,
                                                            @Param(value = "targetCountryIdList") List<Long> targetCountryIdList,
                                                            @Param(value = "states") Set<Long> states);


    /**
     * 根据学习计划状态各步骤获取学生数
     *
     * @Date 10:37 2021/8/3
     * <AUTHOR>
     */
    List<StudentVo> getStudentNumByOfferItemStepOrder(@Param("companyId") Long companyId,
                                                      @Param("studentIds") List<Long> studentIds,
                                                      @Param("userNames") List<String> userNames,
                                                      @Param("beginTime") Date beginTime,
                                                      @Param("endTime") Date endTime,
                                                      @Param("stepOrder") Integer stepOrder,
                                                      @Param("staffId") Long staffId,
                                                      @Param("areaCountryIds") List<Long> areaCountryIds,
                                                      @Param("countryIds") List<Long> countryIds);


    /**
     * 根据失败原因获取学生数
     *
     * @Date 10:56 2021/8/3
     * <AUTHOR>
     */
    List<StudentVo> getStudentNumByFailureReasonKey(@Param("companyId") Long companyId,
                                                    @Param("studentIds") List<Long> studentIds,
                                                    @Param("userNames") List<String> userNames,
                                                    @Param("beginTime") Date beginTime,
                                                    @Param("endTime") Date endTime,
                                                    @Param("reasonId") Long reasonId,
                                                    @Param("staffId") Long staffId,
                                                    @Param("areaCountryIds") List<Long> areaCountryIds,
                                                    @Param("countryIds") List<Long> countryIds);

    /**
     * 根据延迟入学状态获取学生数
     *
     * @Date 11:15 2021/8/3
     * <AUTHOR>
     */
    List<StudentVo> getStudentNumByDeferEntranceTime(@Param("companyId") Long companyId,
                                                     @Param("studentIds") List<Long> studentIds,
                                                     @Param("userNames") List<String> userNames,
                                                     @Param("beginTime") Date beginTime,
                                                     @Param("endTime") Date endTime,
                                                     @Param("staffId") Long staffId,
                                                     @Param("areaCountryIds") List<Long> areaCountryIds);

    /**
     * 验证学生唯一性
     *
     * @param studentDto
     * @return
     */
    List<Student> getStudentByNameAndBirthday(@Param("studentDto") StudentDto studentDto);

    /**
     * @return java.lang.Integer
     * @Description: 根据城市获取申请学习计划的学生数
     * @Param [id]
     * <AUTHOR>
     **/
    @DS("saledb-doris")
    Integer getCountByCity(@Param("companyIds") List<Long> companyIds, @Param("id") Long id, @Param("year") String year,
                           @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                           @Param("staffFollowerIds") List<Long> staffFollowerIds,@Param("isStudentAdmin") Boolean isStudentAdmin,
                           @Param("isBd") Boolean isBd,
                           @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                           @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * @return java.lang.String
     * @Description: 查询学生生日
     * @Param [id]
     * <AUTHOR>
     **/

    Date getStudentBirthDay(@Param("studentId") Long studentId);

    /**
     * @return java.lang.String
     * @Description: 查询学生生日
     * @Param [id]
     * <AUTHOR>
     **/
    List<Student> getStudentBirthDayByIds(@Param("studentIds") Set<Long> studentIds);

    /**
     * @return java.lang.String
     * @Description: 查询学生护照
     * @Param [id]
     * <AUTHOR>
     **/
    String getStudentPassportMum(@Param("studentId") Long studentId);

    /**
     * @return java.lang.String
     * @Description: 查询学生护照
     * @Param [id]
     * <AUTHOR>
     **/
    List<StudentVo> getStudentPassportMumByIds(@Param("studentIds") Set<Long> studentIds);

    /**
     * @return java.lang.String
     * @Description: 查询学生中英
     * @Param [id]
     * <AUTHOR>
     **/
    String getStudentZhEnName(@Param("studentId") Long studentId);

    /**
     * @return java.lang.String
     * @Description: 查询学生中英
     * @Param [id]
     * <AUTHOR>
     **/
    List<StudentVo> getStudentZhEnNameByIds(@Param("studentIds") Set<Long> studentIds);

    /**
     * @return java.lang.String
     * @Description: 查询学生中英(批量)
     * @Param [studentIds]
     * <AUTHOR>
     **/
    List<StudentVo> getStudentZhEnNameByStudentIds(@Param("studentIds") Set<Long> studentIds);

    StudentVo getIsExitStudent(@Param("studentName") String studentName, @Param("value") String value, @Param("type") String type);

    List<AgentVo> getStudentAgents(@Param("fkStudentId") List<Long> id);


    StudentVo getIsExitStudent(@Param("studentName") String studentName, @Param("email") String email,
                               @Param("id") Long id,
                               @Param("mobile") String mobile,
                               @Param("passpost") String passpost,
                               @Param("birthday") String birthdaye, @Param("companyId") Long companyId);


    /**
     * 学生列表
     *
     * @Date 11:11 2022/2/28
     * <AUTHOR>
     */
    @DS("saledb-doris")
    List<StudentVo> getStudents(@Param("studentDto") StudentListQueryDto studentListQueryBo,
                                   @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                   @Param("userNames") List<String> userNames,
                                   @Param("fkAreaCountryIds") List<Long> fkAreaCountryIds,
                                   @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                   @Param("staffBoundBdIds") List<Long> staffBoundBdIds,
                                   @Param("stepFailure") String stepFailure,
                                   @Param("isStudentOfferItemFinancialHiding")Boolean isStudentOfferItemFinancialHiding,
                                   @Param("isStudentAdmin") Boolean isStudentAdmin,
                                   @Param("currentStudentApplicationStatusStatisticsDto") CurrentStudentApplicationStatusStatisticsDto currentStudentApplicationStatusStatisticsDto,
                                   @Param("bdStudentStatisticalComparisonDto") BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonDto,
                                   @Param("htiStartTime") Date htiStartTime,
                                   @Param("beginTime") Date beginTime,
                                   @Param("endTime") Date endTime,
                                   @Param("staffId") Long staffId,
                                   @Param("beginOpenTime") Date beginOpenTime,
                                   @Param("endOpenTime") Date endOpenTime,
                                   @Param("studentIds") List<Long> studentIds,
                                   @Param("institutionIds") List<Long> institutionIds,
                                   @Param("isBd") Boolean isBd,
                                   @Param("isExport") Boolean isExport);

    @DS("saledb-doris-export")
    List<StudentVo> getExportStudents(@Param("studentDto") StudentListQueryDto studentListQueryDto,
                                      @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                      @Param("userNames") List<String> userNames,
                                      @Param("fkAreaCountryIds") List<Long> fkAreaCountryIds,
                                      @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                      @Param("staffBoundBdIds") List<Long> staffBoundBdIds,
                                      @Param("stepFailure") String stepFailure,
                                      @Param("isStudentOfferItemFinancialHiding")Boolean isStudentOfferItemFinancialHiding,
                                      @Param("isStudentAdmin") Boolean isStudentAdmin,
                                      @Param("currentStudentApplicationStatusStatisticsDto") CurrentStudentApplicationStatusStatisticsDto currentStudentApplicationStatusStatisticsDto,
                                      @Param("bdStudentStatisticalComparisonDto") BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonDto,
                                      @Param("htiStartTime") Date htiStartTime,
                                      @Param("beginTime") Date beginTime,
                                      @Param("endTime") Date endTime,
                                      @Param("staffId") Long staffId,
                                      @Param("beginOpenTime") Date beginOpenTime,
                                      @Param("endOpenTime") Date endOpenTime,
                                      @Param("studentIds") List<Long> studentIds,
                                      @Param("institutionIds") List<Long> institutionIds,
                                      @Param("isBd") Boolean isBd,
                                      @Param("isExport") Boolean isExport);


    /**
     * 根据学生ids查询学生的最高最低状态
     *
     * @Date 14:45 2023/5/16
     * <AUTHOR>
     */
    @DS("saledb-doris")
    List<StudentItemStatusVo> getStudentItemStatus(@Param("studentIds") List<Long> studentIds,
                                                   @Param("studentDto") StudentListQueryDto studentListQueryBo,
                                                   @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                   @Param("fkAreaCountryIds") List<Long> fkAreaCountryIds,
                                                   @Param("stepFailure") String stepFailure,
                                                   @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                   @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                   @Param("staffId") Long staffId,
                                                   @Param("beginOpenTime") Date beginOpenTime,
                                                   @Param("endOpenTime") Date endOpenTime,
                                                   @Param("isBd") Boolean isBd,
                                                   @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                   @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * 判断学生是否完善护照信息
     *
     * @param fkStudentId
     * @return
     */
    Boolean hasPassportNum(@Param("fkStudentId") Long fkStudentId);

    /**
     * 根据id获取学生
     *
     * @param fkStudentId
     * @return
     */
    Student getStudentById(@Param("fkStudentId") Long fkStudentId);

    /**
     * 获取当前学生所绑定代理信息
     * @param fkStudentNum
     * @return
     */
    List<AgentsBindingVo> getStudentAgentBinding(@Param("fkStudentNum") String fkStudentNum);

    /**
     * 根据学生id获取失败原因
     *
     * @Date 1 2022/7/11
     * <AUTHOR>
     */
    List<SelItem> getReasonNameByStudentIds(List<Long> ids);

    /**
     * 根据国家获取学生数
     *
     * @Date 11:08 2022/8/2
     * <AUTHOR>
     */
    @DS("saledb-doris")
    List<SelItem> getStudentCountByCountry(@Param(value = "companyIds") List<Long> companyIds,
                                     @Param("fkAreaCountryIds") List<Long> fkAreaCountryIds,
                                     @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                     @Param(value = "year") String year,
                                     @Param("isStudentAdmin") Boolean isStudentAdmin,
                                     @Param(value = "isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                     @Param(value = "isBd") Boolean isBd,
                                     @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                     @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    List<BaseSelectEntity> getStudentAllGraduatedCountrySelect(Long companyId);

    List<BaseSelectEntity> getStudentGraduateCountryMpStateSelect(@Param("id") Long id,@Param("companyId") Long companyId);

    List<BaseSelectEntity> getStudentGraduateSchool(@Param("companyId")Long companyId,@Param("countryId") Long countryId, @Param("stateId") Long stateId);

    //查询学生成功申请国家
    List<BaseSelectEntity> getSuccessfulApplicationCountry(@Param("ids") List<Long> ids);

    Integer getTotalCount();

    //根据学生名称获取学生信息
    List<StudentAgentBindingNewVo> getStudentAgentBindingByStudentName(IPage<StudentAgentBindingNewVo> iPage, @Param("fkStudentName") String fkStudentName,
                                                                       @Param("fkCompanyId") Long fkCompanyId);

    List<StudentAgentEmailVo> getStudentInfoByEmail(@Param("ids") Set<Long> ids);

    /**
     * 获取学生毕业背景信息
     * @param statisticsSearchVo
     * @return
     */
    StudentGraduationBackgroundVo getStudentGraduationBackgroundInfo(@Param("statisticsSearchDto") StudentCourseStatisticsSearchDto statisticsSearchVo, @Param("isStudentOfferItemFinancialHiding")Boolean isStudentOfferItemFinancialHiding);

    /**
     * 通过编号查询学生
     * @param num
     * @return
     */
    StudentVo getStudentByNum(String num);

    @DS("saledb-doris")
    List<StudentVo> getClientStudent(IPage<StudentVo> iPage, @Param("clientStudentDto") ClientStudentDto clientStudentDto);


    List<Student> getStudentSelect(@Param("name") String name, @Param("companyIds") List<Long> companyIds, @Param("studentId") Long studentId);

    @DS("saledb-doris")
    Integer getStudentOfferItemNumCount(@Param("studentDto") StudentListQueryDto studentListQueryBo,
                                @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                @Param("userNames") List<String> userNames,
                                @Param("fkAreaCountryIds") List<Long> fkAreaCountryIds,
                                @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                @Param("staffBoundBdIds") List<Long> staffBoundBdIds,
                                @Param("stepFailure") String stepFailure,
                                @Param("isStudentOfferItemFinancialHiding")Boolean isStudentOfferItemFinancialHiding,
                                @Param("isStudentAdmin") Boolean isStudentAdmin,
                                @Param("currentStudentApplicationStatusStatisticsDto") CurrentStudentApplicationStatusStatisticsDto currentStudentApplicationStatusStatisticsDto,
                                @Param("bdStudentStatisticalComparisonDto") BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonDto,
                                @Param("htiStartTime") Date htiStartTime,
                                @Param("beginTime") Date beginTime,
                                @Param("endTime") Date endTime,
                                @Param("staffId") Long staffId,
                                @Param("beginOpenTime") Date beginOpenTime,
                                @Param("endOpenTime") Date endOpenTime,
                                @Param("studentIds") List<Long> studentIds,
                                @Param("institutionIds") List<Long> institutionIds,
                                @Param("isBd") Boolean isBd,
                                @Param("isExport") Boolean isExport);

    /**
     * AI获取学生信息
     *
     * @param studentName
     * @param companyIds
     * @param fkAreaCountryIds
     * @param staffFollowerIds
     * @param permissionGroupInstitutionIds
     * @param staffBoundBdIds
     * @param isStudentOfferItemFinancialHiding
     * @param isStudentAdmin
     * @param institutionIds
     * @return
     */
    @DS("saledb-doris")
    List<AiStudentInfoVo> getAiStudentInfo(@Param("studentName")String studentName,
                                           @Param("companyIds") List<Long> companyIds,
                                           @Param("fkAreaCountryIds") List<Long> fkAreaCountryIds,
                                           @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                           @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                           @Param("staffBoundBdIds") List<Long> staffBoundBdIds,
                                           @Param("isStudentOfferItemFinancialHiding")Boolean isStudentOfferItemFinancialHiding,
                                           @Param("isStudentAdmin") Boolean isStudentAdmin,
                                           @Param("institutionIds") List<Long> institutionIds);

}