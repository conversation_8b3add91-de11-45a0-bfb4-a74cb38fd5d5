package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.vo.AgentEventVo;
import com.get.salecenter.entity.AgentEvent;
import com.get.salecenter.dto.AgentEventDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface AgentEventMapper extends BaseMapper<AgentEvent> {

    int insertSelective(AgentEvent record);

    List<AgentEventVo> findAgentEventsById(@Param("agentEventDto") AgentEventDto agentEventDto);

    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [agentId]
     * <AUTHOR>
     */
    Bo<PERSON>an isExistByAgentId(Long agentId);

}