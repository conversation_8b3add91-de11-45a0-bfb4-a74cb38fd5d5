package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.ContractFormulaCourseTypeMapper;
import com.get.institutioncenter.entity.ContractFormulaCourseType;
import com.get.institutioncenter.service.IContractFormulaCourseTypeService;
import com.get.institutioncenter.service.ICourseTypeService;
import com.get.institutioncenter.dto.ContractFormulaCourseTypeDto;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/1/8 10:28
 * @verison: 1.0
 * @description:
 */
@Service
public class ContractFormulaCourseTypeServiceImpl extends BaseServiceImpl<ContractFormulaCourseTypeMapper, ContractFormulaCourseType> implements IContractFormulaCourseTypeService {
    @Resource
    private ContractFormulaCourseTypeMapper contractFormulaCourseTypeMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private ICourseTypeService courseTypeService;

    @Override
    public Long addContractFormulaCourseType(ContractFormulaCourseTypeDto contractFormulaCourseTypeDto) {
        if (GeneralTool.isEmpty(contractFormulaCourseTypeDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ContractFormulaCourseType contractFormulaCourseType = BeanCopyUtils.objClone(contractFormulaCourseTypeDto, ContractFormulaCourseType::new);
        utilService.updateUserInfoToEntity(contractFormulaCourseType);
        contractFormulaCourseTypeMapper.insertSelective(contractFormulaCourseType);
        return contractFormulaCourseType.getId();
    }

    @Override
    public void deleteByFkid(Long contractFormulaId) {
        LambdaQueryWrapper<ContractFormulaCourseType> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContractFormulaCourseType::getFkContractFormulaId, contractFormulaId);
        contractFormulaCourseTypeMapper.delete(wrapper);
    }

    @Override
    public List<Long> getCourseTypeIdListByFkid(Long contractFormulaId) {
        return contractFormulaCourseTypeMapper.getCourseTypeIdListByFkid(contractFormulaId);
    }

    @Override
    public String getCourseTypeNameByFkid(Long contractFormulaId) {
        List<String> countryNameList = contractFormulaCourseTypeMapper.getCourseTypeNameByFkid(contractFormulaId);
        String result = "";
        if (GeneralTool.isNotEmpty(countryNameList)) {
            result = StringUtils.join(countryNameList, ",");
        }
        return result;
    }
}
