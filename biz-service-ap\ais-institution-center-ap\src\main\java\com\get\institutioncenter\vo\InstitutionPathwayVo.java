package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @DATE: 2020/12/17
 * @TIME: 17:47
 * @Description:
 **/
@Data
public class InstitutionPathwayVo extends BaseEntity {
    private InstitutionVo institutionDto;

    //==============实体类InstitutionPathway=================
    private static final long serialVersionUID = 1L;
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;
    /**
     * 桥梁学校Id
     */
    @ApiModelProperty(value = "桥梁学校Id")
    @Column(name = "fk_institution_id_pathway")
    private Long fkInstitutionIdPathway;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fkInstitutionId=").append(fkInstitutionId);
        sb.append(", fkInstitutionIdPathway=").append(fkInstitutionIdPathway);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }

}
