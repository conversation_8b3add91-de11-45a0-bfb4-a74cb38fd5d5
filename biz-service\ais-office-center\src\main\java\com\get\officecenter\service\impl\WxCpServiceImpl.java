package com.get.officecenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.get.common.cache.CacheNames;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.redis.cache.GetRedis;
import com.get.core.tool.utils.GeneralTool;
import com.get.officecenter.controller.BaseController;
import com.get.officecenter.service.IWxCpService;
import com.get.permissioncenter.vo.WxCpUserVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: Hardy
 * @create: 2022/9/26 10:50
 * @verison: 1.0
 * @description:
 */
@Slf4j
@Service
public class WxCpServiceImpl implements IWxCpService {

    @Value("${wx.cptp.appConfigs[0].suiteId}")
    private String SUIT_ID;

    @Value("${wx.cptp.appConfigs[0].secret}")
    private String SUIT_SECRT;

    @Value("${wx.cptp.corpId}")
    private String CORP_ID;

    @Value("${wx.cptp.corpsecret}")
    private String CORP_SECRET;

    @Resource
    private GetRedis getRedis;

    @Override
    public WxCpUserVo getWxCpUserIdByCode(String code){
        try {
            log.info("@getWxCpUserIdByCode#请求的用户code：{}"+code);
            //企业微信返回errCode
            String errCode = "errcode";
            //企业微信返回errmsg
            String errmsg = "errmsg";
            //企业微信返回errmsg中请求成功标志
            String successFlag = "ok";
            //请求获取空值存到缓存的null字符串
            String nullStringValue = "null";

//            final WxCpTpService wxCpTpService = WxCpTpConfiguration.getCpTpService(SUIT_ID);
//            WxCpTpConfigStorage tpConfig = wxCpTpService.getWxCpTpConfigStorage();
//            String suiteAccessToken = tpConfig.getSuiteAccessToken();
//            log.info("@getWxCpUserIdByCode#获取缓存中第三方应用凭证suiteAccessToken:{}",suiteAccessToken);
//            if (GeneralTool.isEmpty(suiteAccessToken)||nullStringValue.equals(suiteAccessToken)){
//                String suiteTicket = tpConfig.getSuiteTicket();
//                log.info("@getWxCpUserIdByCode#获取缓存中suiteTicket:{}",suiteTicket);
//                suiteAccessToken = BaseController.getSuiteAccessToken(suiteTicket,SUIT_ID,SUIT_SECRT);
//                log.info("@getWxCpUserIdByCode#发送企业微信api请求suiteAccessToken:{}",suiteAccessToken);
//                if (!nullStringValue.equals(suiteAccessToken)){
//                    //有效期两个小时 更新到缓存
//                    tpConfig.updateSuiteAccessToken(suiteAccessToken, 60*60*2);
//                }
//            }
//
//            //获取用户openUserid
//            JSONObject userInfo3rd = BaseController.getUserInfo3rd(suiteAccessToken, code);
//            if (!userInfo3rd.get(errCode).equals(0)||!successFlag.equals(userInfo3rd.get(errmsg))){
//                log.error("@getWxCpUserIdByCode#获取用户openUserid失败！！:{}",userInfo3rd.toJSONString());
//                throw new GetServiceException("获取用户信息失败！");
//            }
//            log.info("@getWxCpUserIdByCode#获取用户信息成功！！:{}",userInfo3rd.toJSONString());
//            String openUserid = userInfo3rd.get("open_userid").toString();

            //获取企业accessToken
            String accessToken = getRedis.get(CacheNames.ACCESS_TOKEN_KEY);
            log.info("@getWxCpUserIdByCode#获取缓存中accessToken:{}",accessToken);
            if (GeneralTool.isEmpty(accessToken)){
                JSONObject accessTokenJson = BaseController.getAccessToken(CORP_ID, CORP_SECRET);
                if (!accessTokenJson.get(errCode).equals(0)||!successFlag.equals(accessTokenJson.get(errmsg))){
                    log.error("@getWxCpUserIdByCode#企业accessToken失败！！:{}",accessTokenJson.toJSONString());
                    throw new GetServiceException(LocaleMessageUtils.getMessage("get_accessToken_failed"));
                }
                accessToken = accessTokenJson.get("access_token").toString();
                log.info("@getWxCpUserIdByCode#调用企业微信accessToken接口:{}",accessTokenJson.toJSONString());
                Long expiresIn = Long.valueOf(accessTokenJson.get("expires_in").toString());
                getRedis.setEx(CacheNames.ACCESS_TOKEN_KEY,accessToken,expiresIn);
            }

//            JSONObject authInfoJson = BaseController.getAuthInfo(suiteAccessToken);
//            if (GeneralTool.isEmpty(authInfoJson.getJSONObject("auth_info"))){
//                log.error("@getWxCpUserIdByCode#应用信息authInfo获取失败:{}",authInfoJson.toJSONString());
//                throw new GetServiceException("应用信息authInfo获取失败！");
//            }
//            log.info("@getWxCpUserIdByCode#应用信息authInfo获取成功！！:{}",authInfoJson.toJSONString());
//            JSONObject authInfo = authInfoJson.getJSONObject("auth_info");
//            JSONArray agent = authInfo.getJSONArray("agent");
//            JSONObject agentJson = agent.getJSONObject(0);
//            Long agentId = agentJson.getLong("agentid");
//            log.info("@getWxCpUserIdByCode#获取agentid成功！！:{}",agentId);
//
//            JSONObject userIdJson = BaseController.getUserId(accessToken, openUserid, agentId);
//            if (!userIdJson.get(errCode).equals(0)||!successFlag.equals(userIdJson.get(errmsg))){
//                log.error("@getWxCpUserIdByCode#userId获取失败！:{}",userIdJson.toJSONString());
//                throw new GetServiceException("userId获取失败！");
//            }
//            log.info("@getWxCpUserIdByCode#用户信息getUser获取成功！！:{}",userIdJson.toJSONString());
//            JSONArray useridList = userIdJson.getJSONArray("userid_list");
//            JSONObject jsonObject = useridList.getJSONObject(0);
//            String userid = jsonObject.get("userid").toString();
//            log.error("@getWxCpUserIdByCode#userId获取成功！:{}",userid);
//            if (GeneralTool.isEmpty(userid)){
//                throw new GetServiceException("获取员工失败！");
//            }

            /*企业内部应用逻辑*/
            JSONObject userInfoJson = BaseController.getUserInfo(accessToken,code);
            if (!userInfoJson.get(errCode).equals(0)||!successFlag.equals(userInfoJson.get(errmsg))){
                log.error("@getWxCpUserIdByCode#userId获取失败！:{}",userInfoJson.toJSONString());
                throw new GetServiceException(LocaleMessageUtils.getMessage("USER_ID_FETCH_FAILED"));
            }
            String userid = userInfoJson.getString("userid");
            log.info("@getWxCpUserIdByCode#userId获取成功！:{}",userid);
            /*企业内部应用逻辑*/

            String userTicket = userInfoJson.getString("user_ticket");
            log.info("@getWxCpUserIdByCode#userTicket获取成功！:{}",userTicket);
            JSONObject userDetail = BaseController.getUserDetail(accessToken, userTicket);
            String avatarUrl = userDetail.getString("avatar");
            log.info("@getWxCpUserIdByCode#avatarUrl获取成功！:{}",avatarUrl);

            WxCpUserVo wxCpUserVo = new WxCpUserVo();
            wxCpUserVo.setUserId(userid);
            wxCpUserVo.setAvatarUrl(avatarUrl);

            return wxCpUserVo;
        }catch (Exception e) {
            e.printStackTrace();
            log.info("@getWxCpUserIdByCode#"+e.getMessage());
        }
        return null;
    }
}
