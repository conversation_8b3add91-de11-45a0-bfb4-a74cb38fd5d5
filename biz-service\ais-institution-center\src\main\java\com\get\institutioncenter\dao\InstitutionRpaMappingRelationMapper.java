package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.InstitutionRpaMappingRelation;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface InstitutionRpaMappingRelationMapper extends BaseMapper<InstitutionRpaMappingRelation> {
    int insert(InstitutionRpaMappingRelation record);

    int insertSelective(InstitutionRpaMappingRelation record);

    int updateByPrimaryKeySelective(InstitutionRpaMappingRelation record);

    int updateByPrimaryKey(InstitutionRpaMappingRelation record);
}