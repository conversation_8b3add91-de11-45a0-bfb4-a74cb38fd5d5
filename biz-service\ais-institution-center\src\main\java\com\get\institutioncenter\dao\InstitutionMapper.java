package com.get.institutioncenter.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.institutioncenter.bo.InstitutionApplicationStaticsQueryBo;
import com.get.institutioncenter.dto.AiInstitutionDto;
import com.get.institutioncenter.dto.CaseStudyResultsDto;
import com.get.institutioncenter.dto.InstitutionApplicationStaticsDto;
import com.get.institutioncenter.dto.InstitutionByNameDto;
import com.get.institutioncenter.dto.InstitutionDto;
import com.get.institutioncenter.dto.query.InstitutionQueryDto;
import com.get.institutioncenter.entity.Institution;
import com.get.institutioncenter.vo.AiCourseVo;
import com.get.institutioncenter.vo.AiInstitutionInfoVo;
import com.get.institutioncenter.vo.ApplicationStatisticsProviderVo;
import com.get.institutioncenter.vo.InstitutionApplicationStatisticsVo;
import com.get.institutioncenter.vo.InstitutionVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface InstitutionMapper extends GetMapper<Institution> {

    /**
     * feign调用 根据输入的学校名称 模糊查询对应的学校ids
     *
     * @param institutionName
     * @return
     */
    List<Long> getInstitutionIds(String institutionName);

    /**
     * 获取所有对应字段不为null或""的InstitutionIds
     * <AUTHOR>
     * @DateTime 2023/10/25 10:36
     */
    List<Long> getInstitutionIdsByColumnName(String columnName);

    /**
     * 根据IDS获取学校列表
     * <AUTHOR>
     * @DateTime 2023/10/25 14:10
     */
    Institution getInstitutionById(@Param("id") Long id);

    /**
     * feign调用 根据学校id查找学校名称
     *
     * @param id
     * @return
     */
    String getInstitutionNameById(Long id);

    /**
     * feign调用 根据学校id查找学校国家
     *
     * @Date 18:08 2021/7/13
     * <AUTHOR>
     */
    Long getCountryIdByInstitutionId(Long id);

    /**
     * 学校列表
     *
     * @param institutionVo
     * @return
     */
    @DS("institutiondb-doris")
    List<InstitutionVo> getByVo(IPage<Institution> pages,@Param("institutionDto") InstitutionQueryDto institutionVo);

    /**
     * 学校数量
     *
     * @param institutionDto
     * @return
     */
    Integer getCountByVo(InstitutionDto institutionDto);

    /**
     * 学校下拉框
     *
     * @return
     */
    List<BaseSelectEntity> getInstitutionList();

    /**
     * 根据国家获取学校下拉框数据
     *
     * @Date 16:30 2021/5/31
     * <AUTHOR>
     */
    List<BaseSelectEntity> getInstitutionListByCountryId(@Param("countryId") Long countryId);

    /**
     * 根据国家(多选)获取学校下拉框数据
     * @param fkCountryIdList
     * @return
     */
    List<BaseSelectEntity> getInstitutionListByCountryIdList(@Param("fkCountryIdList") List<Long> fkCountryIdList);

    /**
     * 根据国家查询学校下拉框数据
     *
     * @return
     */
    List<BaseSelectEntity> getInstitutionListByProviderId(@Param("providerId") Long providerId);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.InstitutionVo>
     * @Description:
     * @Param []
     * <AUTHOR>
     */
      List<InstitutionVo> getInstitutions(IPage<Institution> pages, @Param("institutionDto") InstitutionDto institutionDto, @Param("companyIds") List<Long> companyIds);

    List<InstitutionVo> getInstitutions( @Param("institutionDto") InstitutionDto institutionDto, @Param("companyIds") List<Long> companyIds);

    /**
     * 学校桥梁学校下拉框数据
     *
     * @return
     */
    List<BaseSelectEntity> getBridgeInstitutionSelect(@Param("ids") List<Long> ids);

    /**
     * 课程桥梁学校下拉框数据
     *
     * @Date 16:02 2021/7/27
     * <AUTHOR>
     */
    List<BaseSelectEntity> getBridgeInstitutionSelectByCourse( @Param("institutionId") Long institutionId);

    /**
     * 非桥梁学校下拉框数据
     *
     * @Date 16:17 2021/7/22
     * <AUTHOR>
     */
    List<BaseSelectEntity> getNonBridgeInstitutionSelect(@Param("ids") List<Long> ids);

    /**
     * 课程 非桥梁学校下拉框数据
     *
     * @Date 18:02 2021/7/27
     * <AUTHOR>
     */
    List<BaseSelectEntity> getNonBridgeInstitutionSelectByCourse(@Param("ids") List<Long> ids, @Param("pathwayInstitutionId") Long pathwayInstitutionId);


    /**
     * feign调用 根据学校id查找学校全称
     *
     * @param id
     * @return
     */
    String getInstitutionFullNameById(Long id);

    /**
     * feign调用 根据学校ids查找国家名字
     *
     * @Date 10:38 2021/8/26
     * <AUTHOR>
     */
    List<InstitutionVo> getInstitutionCountryInfoByInstitutionIds(@Param("institutionIds") Set<Long> institutionIds);

    /**
     * 根据学校id获取学校国家下拉框数据
     *
     * @return
     * @
     */
    List<BaseSelectEntity> getCountryByInstitutionIdSelect(@Param("id") Long id);

    /**
     * 已绑定提供商学校下拉框数据
     *
     * @param companyIds
     * @param countryId
     * @return
     */
    List<BaseSelectEntity> getProviderInstitutionList(@Param("companyIds") List<Long> companyIds, @Param("countryId") Long countryId);

    List<BaseSelectEntity> getProviderInsByOfferId(Long id);

    InstitutionVo getCountByInstitutionId(@Param("id") Long id);

    /**
     * 学校下拉框包括简称
     *
     * @return
     */
    List<BaseSelectEntity> getInstitutionSfList();

    List<Long> getInstitutionsByNameEnOrNameZh(@Param("schoolName") String schoolName);

    List<Long> getInstitutionsByNameEnOrNameZh2(@Param("schoolName") String schoolName, @Param("fkCountryId")Long fkCountryId);


    List<InstitutionVo> getInstitutionsObjByNameEnOrNameZh(@Param("schoolName") String schoolName);

    List<Long> getInstitutionsByNameEnOrNameZhAndCountry(@Param("schoolName") String schoolName,
                                                         @Param("fkCountryId")Long fkCountryId);

    @DS("institutiondb-doris")
    List<BaseSelectEntity> getInstitutionByName(@Param("institutionByNameDto") InstitutionByNameDto institutionByNameDto);

    List<CaseStudyResultsDto.Statistics> getRankingTypeCount(@Param("ids")Set<Long> institutionIds);

    /**
     * 获取匹配模糊查询学校名称或则学校名称的学校ids
     * <AUTHOR>
     * @DateTime 2022/12/9 17:30
     */
    Set<Long> getLikeInstitutionIds(@Param("institutionApplicationStaticsDto") InstitutionApplicationStaticsDto institutionApplicationStaticsDto);
    /**
     * 获取所有绑定学校的提供商
     * <AUTHOR>
     * @DateTime 2022/12/8 14:50
     */
    List<ApplicationStatisticsProviderVo> getInstitutionProviderList(@Param("institutionApplicationStaticsDto") InstitutionApplicationStaticsQueryBo staticsQueryBo);
    /**
     * 获取学校列表
     * <AUTHOR>
     * @DateTime 2022/12/8 10:18
     */
    List<InstitutionApplicationStatisticsVo> getInstitutionDtoList(@Param("institutionApplicationStaticsDto") InstitutionApplicationStaticsQueryBo staticsQueryBo);

    List<BaseSelectEntity> getInstitutionListByKeyword(@Param("keyword") String keyword,@Param("countryId") Long countryId);

    List<Long> getInstitutionIdsByKeyword(@Param("keyword") String keyword);

    /**
     * 有新闻数据的 学校下拉框数据包括简称
     *
     * @Date 11:50 2023/7/11
     * <AUTHOR>
     */
    List<BaseSelectEntity> getNewsInstitutionSfList();

    /**
     * 获取AI学校信息
     *
     * @Date 11:50 2023/7/11
     * <AUTHOR>
     */
    List<AiInstitutionInfoVo> getAiInstitutionInfo(@Param("aiInstitutionDto") AiInstitutionDto aiInstitutionDto);

    /**
     * 获取AI学校课程信息
     *
     * @Date 11:50 2023/7/11
     * <AUTHOR>
     */
    List<AiCourseVo> getAiCourseInfo(@Param("institutionIds") List<Long> institutionIds);
}