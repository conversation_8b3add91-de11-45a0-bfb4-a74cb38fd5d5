<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.StaffCommissionInstitutionMapper">

    <select id="getStaffCommissionInstitutionDtos"
            resultType="com.get.salecenter.vo.StaffCommissionInstitutionVo">
        SELECT a.* from (
            SELECT
                a.* ,
                CONCAT(b.`name`,IF(b.name_chn is null,b.name_chn,CONCAT("（",b.name_chn,"）"))) fkInstitutionName
            FROM
                `r_staff_commission_institution` a
                    LEFT JOIN ais_institution_center.m_institution b on a.fk_institution_id = b.id
            HAVING 1=1
               <if test="staffCommissionInstitutionDto.keyWord !=null and staffCommissionInstitutionDto.keyWord !=''">
                    and fkInstitutionName like concat("%",#{staffCommissionInstitutionDto.keyWord},"%")
               </if>
            ORDER BY a.gmt_create desc
        ) a
    </select>
</mapper>