package com.get.platformconfigcenter.dao.appmso;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.AgentModuleInfo;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("appmsodb")
public interface AgentModuleInfoMapper extends BaseMapper<AgentModuleInfo> {

    int insert(AgentModuleInfo record);

//    int insertSelective(AgentModuleInfo record);
//
//    int updateByPrimaryKeySelective(AgentModuleInfo record);
//
//    int updateByPrimaryKey(AgentModuleInfo record);
//
//    /**
//     * 获取最大排序值
//     */
//    Integer getMaxViewOrder();

}