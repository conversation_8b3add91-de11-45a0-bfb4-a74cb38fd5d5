package com.get.core.log.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.log.model.LogError;
import com.get.core.log.service.ILogErrorService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.support.Query;
import com.get.core.tool.api.Result;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;

/**
 * 控制器
 */
@RestController
@AllArgsConstructor
@RequestMapping("/error")
public class LogErrorController {

    private final ILogErrorService errorLogService;

    /**
     * 查询单条
     */
    @GetMapping("/detail")
    public Result<LogError> detail(LogError logError) {
        return Result.data(errorLogService.getOne(GetCondition.getQueryWrapper(logError)));
    }

    /**
     * 查询多条(分页)
     */
    @GetMapping("/list")
    public Result<IPage<LogError>> list(@ApiIgnore @RequestParam Map<String, Object> logError, Query query) {
        IPage<LogError> pages = errorLogService.page(GetCondition.getPage(query.setDescs("gmt_create")), GetCondition.getQueryWrapper(logError, LogError.class));
        return Result.data(pages);
    }

}
