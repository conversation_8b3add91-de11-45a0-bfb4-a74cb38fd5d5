package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 我的学生申请状态统计Vo类
 *
 * <AUTHOR>
 * @date 2022/7/14 11:58
 */
@Data
public class CurrentStudentApplicationStatusStatisticsDto {

    @NotNull(message = "公司id不能为空")
    @Min(value = 1, message = "缺少公司id参数")
    @ApiModelProperty(value = "公司id")
    private Long companyId;

//    @ApiModelProperty(value = "项目成员名称")
//    private String fkProjectMemberName;

    @ApiModelProperty(value = "项目成员id列表")
    private List<Long> fkProjectMemberIds;

    @ApiModelProperty(value = "报考国家id")
    private List<Long> countryIds;


    @ApiModelProperty("时间搜索类型：1-步骤状态变更时间2-申请创建时间；3-学生开学时间 4-业绩统计时间")
    private Integer selectType;

    @ApiModelProperty("修改时间(开始)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty("修改时间(结束)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "绑定BD名称或编号")
    private String bdNameOrCode;

    @ApiModelProperty(value = "员工Id（业绩绑定，BD）")
    private Long fkBdId;


    @NotNull(message = "数据结果类型不能为空")
    @ApiModelProperty(value = "数据结果类型： 1：申请数  2：当前学习计划状态学生数  3：历史学习计划状态学生数"  )
    private Long dataResultType;

    /**
     * 代理国家
     */
    @ApiModelProperty(value = "代理国家")
    private Long fkAreaCountryIdAgent;

    /**
     * 代理省份ID列表
     */
    @ApiModelProperty(value = "代理省份ID列表")
    private List<Long> fkAreaStateIdAgent;

    /**
     * 代理大区ID
     */
    @ApiModelProperty("BD绑定的大区ID")
    private Long fkAreaRegionId;

    @ApiModelProperty(value = "申请步骤ID")
    private Long stepId;

    @ApiModelProperty(value = "申请步骤ID List")
    private List<Long> stepIdList;
}
