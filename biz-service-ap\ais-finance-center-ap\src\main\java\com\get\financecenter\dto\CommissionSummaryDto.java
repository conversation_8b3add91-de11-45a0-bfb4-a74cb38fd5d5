package com.get.financecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 财务佣金汇总Vo
 *
 * <AUTHOR>
 * @date 2021/12/24 15:40
 */
@Data
@ApiModel(value = "财务佣金汇总Vo")
public class CommissionSummaryDto  extends BaseVoEntity implements Serializable{

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @NotNull(message = "公司id不能为空")
    @Min(value = 1, message = "缺少公司id参数")
    private Long fkCompanyId;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "代理名称或编号")
    private String agentNameOrNum;

    @ApiModelProperty(value = "学生名字")
    private String studentName;

    @ApiModelProperty(value = "结算币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "代理州省Id")
    private Long agentAreaStateId;

}
