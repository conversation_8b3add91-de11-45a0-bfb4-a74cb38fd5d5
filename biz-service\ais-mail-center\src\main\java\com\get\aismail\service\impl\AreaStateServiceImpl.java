package com.get.aismail.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.get.aismail.dao.AreaStateMapper;
import com.get.aismail.dto.AreaStateDto;
import com.get.aismail.entity.AreaState;
import com.get.aismail.service.IAreaStateService;
import com.get.common.utils.BeanCopyUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class AreaStateServiceImpl implements IAreaStateService {
    @Resource
    private AreaStateMapper areaStateMapper;

    @Override
    public List<AreaStateDto> getByFkAreaCountryId(Long id) {
        QueryWrapper<AreaState> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AreaState::getFkAreaCountryId, id);
        queryWrapper.orderByDesc("IFNULL(view_order,0)");
        queryWrapper.orderByAsc("CONVERT(name USING gbk)");
        List<AreaState> list = areaStateMapper.selectList(queryWrapper);
        List<AreaStateDto> convertDatas = new ArrayList<>();
        for (AreaState areaState : list) {
            AreaStateDto contractTypeDto = BeanCopyUtils.objClone(areaState, AreaStateDto::new);
            contractTypeDto.setFullName(areaStateMapper.getStateFullNameById(areaState.getId()));
            convertDatas.add(contractTypeDto);
        }
        return convertDatas;
    }
}
