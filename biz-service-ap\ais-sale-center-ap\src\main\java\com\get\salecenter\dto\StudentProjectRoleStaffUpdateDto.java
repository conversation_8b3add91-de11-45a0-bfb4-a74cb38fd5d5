package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2020/11/16
 * @TIME: 10:30
 * @Description:
 **/
@Data
public class StudentProjectRoleStaffUpdateDto extends BaseVoEntity{
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    @NotNull(message = "表名不能为空", groups = {Add.class})
    private String fkTableName;


    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id", required = true)
    private Long fkStaffId;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

   
}
