package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: Hardy
 * @create: 2023/2/7 15:56
 * @verison: 1.0
 * @description:
 */
@Data
public class StaffCommissionStudentDto extends BaseVoEntity {
    /**
     * 学生Id
     */
    @NotNull(message = "学生Id", groups = {Add.class,Update.class})
    @ApiModelProperty(value = "学生Id")
    private Long fkStudentId;

    /**
     * 状态，枚举：1激活提成/2完成结算
     */
    @ApiModelProperty(value = "状态，枚举：1激活提成/2完成结算")
    private Integer status = 1;

    private static final long serialVersionUID = 1L;

   
}
