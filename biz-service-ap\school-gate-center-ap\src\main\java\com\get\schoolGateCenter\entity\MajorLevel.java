package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("u_major_level")
public class MajorLevel extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 等级名字
     */
    @ApiModelProperty(value = "等级名字")
    @Column(name = "level_name")
    private String levelName;
    /**
     * 等级名字
     */
    @ApiModelProperty(value = "等级名字中文")
    @Column(name = "level_name_chn")
    private String levelNameChn;

    @ApiModelProperty(value = "组别名字")
    @Column(name = "group_name")
    private String groupName;


    @ApiModelProperty(value = "组别中文名字")
    @Column(name = "group_name_chn")
    private String groupNameChn;

    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}