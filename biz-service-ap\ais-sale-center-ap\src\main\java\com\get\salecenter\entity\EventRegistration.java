package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_event_registration")
public class EventRegistration extends BaseEntity implements Serializable {
    /**
     * 活动Id
     */
    @ApiModelProperty(value = "活动Id")
    @Column(name = "fk_event_id")
    private Long fkEventId;

    @ApiModelProperty(value = "枚举状态：0不参加/1参加/2待定")
    @Column(name = "status")
    private Integer status;

    /**
     * 学校提供商Id
     */
    @ApiModelProperty(value = "学校提供商Id")
    @Column(name = "fk_institution_provider_id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    private static final long serialVersionUID = 1L;
}