package com.get.platformconfigcenter.service;


/**
 * <AUTHOR>
 * @DATE: 2021/12/3
 * @TIME: 16:55
 * @Description:
 **/
public interface SitemapPageTemplateService {
    /**
     * 页面模板列表
     *
     * @param sitemapPageTemplateVo
     * @param page
     * @return
     * @
     */
//    List<SitemapPageTemplateVo> getSitemapPageTemplateList(SitemapPageTemplateDto sitemapPageTemplateVo, Page page);
//
//    /**
//     * 新增页面模板
//     *
//     * @param sitemapPageTemplateVo
//     * @return
//     * @
//     */
//    Long addSitemapPageTemplate(SitemapPageTemplateDto sitemapPageTemplateVo);
//
//    /**
//     * 修改页面模板
//     *
//     * @param sitemapPageTemplateVo
//     * @return
//     * @
//     */
//    SitemapPageTemplateVo updateSitemapPageTemplate(SitemapPageTemplateDto sitemapPageTemplateVo);
//
//    /**
//     * 页面模板详情
//     *
//     * @param id
//     * @return
//     * @
//     */
//    SitemapPageTemplateVo findSitemapPageTemplateById(Long id);
//
//    /**
//     * 删除页面模板
//     *
//     * @param id
//     * @
//     */
//    void delete(Long id);
}
