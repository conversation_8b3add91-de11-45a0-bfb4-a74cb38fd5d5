<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.TranslationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.Translation">
        <id column="id" property="id" />
        <result column="fk_table_name" property="fkTableName" />
        <result column="fk_table_id" property="fkTableId" />
        <result column="fk_translation_mapping_id" property="fkTranslationMappingId" />
        <result column="language_code" property="languageCode" />
        <result column="translation" property="translation" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_create_user" property="gmtCreateUser" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="gmt_modified_user" property="gmtModifiedUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, fk_table_name, fk_table_id, fk_translation_mapping_id, language_code, translation, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
    </sql>
    <select id="getTranslation" resultType="java.lang.String">
        select translation from s_translation
        <where>
            <if test="fkTableName != null and  fkTableName !=''">
                and fk_table_name =#{fkTableName}
            </if>
            <if test="fkTableId != null">
                and fk_table_id = #{fkTableId}
            </if>
            <if test="fkTranslationMappingId != null">
                and fk_translation_mapping_id = #{fkTranslationMappingId}
            </if>
            <if test="languageCode != null and  languageCode !=''">
                and language_code =#{languageCode}
            </if>
        </where>

    </select>

</mapper>
