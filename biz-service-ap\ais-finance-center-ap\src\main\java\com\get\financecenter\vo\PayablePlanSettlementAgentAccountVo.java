package com.get.financecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/2/22 18:13
 */
@Data
public class PayablePlanSettlementAgentAccountVo {

    @ApiModelProperty("结算信息id")
    private Long id;

    /**
     * 应付计划Id
     */
    @ApiModelProperty(value = "应付计划Id")
    private Long fkPayablePlanId;

    /**
     * 学生代理合同账户Id
     */
    @ApiModelProperty(value = "学生代理合同账户Id")
    private Long fkAgentContractAccountId;

    /**
     * 币种编号（代理账户）
     */
    @ApiModelProperty(value = "币种编号（代理账户）")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "币种名称（代理账户）")
    private String currencyTypeName;
}
