package com.get.pmpcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.permissioncenter.vo.workbench.WorkbenchApprovalVo;
import com.get.pmpcenter.dto.agent.PmpWorkbenchApprovalDto;
import com.get.pmpcenter.dto.common.*;
import com.get.pmpcenter.dto.institution.*;
import com.get.pmpcenter.entity.InstitutionProviderCommissionPlan;
import com.get.pmpcenter.entity.InstitutionProviderCommissionPlanApproval;
import com.get.pmpcenter.vo.common.CountryVo;
import com.get.pmpcenter.vo.common.InstitutionVo;
import com.get.pmpcenter.vo.institution.*;

import java.util.List;
import java.util.Map;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:17
 * @Version 1.0
 */
public interface InstitutionProviderCommissionPlanService extends IService<InstitutionProviderCommissionPlan> {

    /**
     * 获取供应商合同下的佣金计划列表
     *
     * @param providerContractId
     * @return
     */
    ProviderCommissionPlanListVo getProviderCommissionPlanList(Long providerContractId);

    /**
     * 保存供应商合同下的佣金计划
     *
     * @param providerCommissionPlanDto
     */
    Long saveProviderCommissionPlan(SaveProviderCommissionPlanDto providerCommissionPlanDto, Boolean saveLog);

    /**
     * 获取供应商合同下的佣金计划详情
     *
     * @param id
     * @return
     */
    ProviderCommissionPlanDetailVo getProviderCommissionPlanDetail(Long id);

    /**
     * 获取供应商下的学校列表
     *
     * @param institutionProviderId
     * @return
     */
    List<InstitutionVo> getInstitutionList(Long institutionProviderId);

    /**
     * 获取国家列表
     *
     * @return
     */
    List<CountryVo> getCountryList();

    /**
     * 删除佣金计划
     *
     * @param id
     */
    void deleteProviderCommissionPlan(Long id, Boolean saveLog);

    /**
     * 获取最近一个方案的适用国家/区域规则
     *
     * @param contractId
     * @return
     */
    List<PlanTerritoryDto> getLastPlanTerritory(Long contractId);


    /**
     * 获取佣金方案下的适用国家/区域规则
     *
     * @param planIds
     * @return
     */
    Map<Long, List<PlanTerritoryVo>> getPlanTerritories(List<Long> planIds);


    /**
     * 复制佣金方案
     *
     * @param saveProviderCommissionPlanDto
     * @return
     */
    Long copyProviderCommissionPlan(SaveProviderCommissionPlanDto saveProviderCommissionPlanDto);

    /**
     * 解绑供应商下的学校
     *
     * @param unbindProviderInstitutionDto
     * @return
     */
    Boolean unbindProviderInstitution(UnbindProviderInstitutionDto unbindProviderInstitutionDto);

    /**
     * 获取佣金方案详情-包含佣金计划
     *
     * @param planId
     * @return
     */
    ProviderCommissionPlanInfoVo getProviderCommissionPlanInfo(Long planId);


    /**
     * 锁定/解锁佣金方案
     *
     * @param lockPlanDto
     */
    void lockPlan(LockPlanDto lockPlanDto);

    /**
     * 提交佣金方案审批
     *
     * @param submitPlanApprovalDto
     */
    void submitProviderPlanApproval(SubmitPlanApprovalDto submitPlanApprovalDto);

    /**
     * 审核佣金方案
     *
     * @param approvalPlanDto
     */
    void approvalProviderPlan(ApprovalPlanDto approvalPlanDto);


    /**
     * 方案审核记录列表
     *
     * @param contractId
     * @return
     */
    List<InstitutionProviderCommissionPlanApproval> getApprovalList(Long contractId, Long planId, String planName);

    /**
     * 修改合同的供应商
     *
     * @param contractId
     * @param newProviderId
     */
    void changeContractProvider(Long contractId, Long newProviderId);


    /**
     * 获取用户权限下的方案ID列表
     *
     * @param contractId
     * @return
     */
    List<Long> getUserPermissionPlanIds(Long contractId);

    /**
     * 获取供应商合同下的佣金计划列表-包含国家/区域规则
     *
     * @param providerContractId
     * @return
     */
    List<ProviderCommissionPlanVo> getProviderCommissionPlanAndTerritoryList(Long providerContractId);


    /**
     * 批量审批适用地区
     *
     * @param batchApprovalDto
     */
    void submitBatchApproval(BatchApprovalDto batchApprovalDto);

    /**
     * 获取批量审批列表
     *
     * @param providerContractId
     * @return
     */
    List<BatchApprovalVo> getBatchApprovalList(Long providerContractId);


    /**
     * 审核批量提交
     *
     * @param examineBatchApprovalDto
     */
    void examineBatchApproval(ExamineBatchApprovalDto examineBatchApprovalDto);

    /**
     * 获取工作台审批列表-PMP审核
     *
     * @param workbenchApprovalDto
     * @return
     */
    List<WorkbenchApprovalVo> getWorkbenchApprovalList(PmpWorkbenchApprovalDto workbenchApprovalDto);


    /**
     * 获取工作台审批列表-PMP审核-分页
     *
     * @param workbenchApprovalDto
     * @return
     */
    List<WorkbenchApprovalVo> getWorkbenchApprovalPage(PmpWorkbenchApprovalDto workbenchApprovalDto, Page page);

    /**
     * 学校方案列表
     *
     * @param params
     * @param page
     * @return
     */
    List<InstitutionPlanVo> institutionPlanPage(IdDto params, Page page);

    /**
     * 学校方案数量
     *
     * @param institutionIds
     * @return
     */
    Map<Long, Integer> institutionPlanCount(List<Long> institutionIds);


    /**
     * 上下架佣金方案
     *
     * @param statusDto
     */
    void updatePlanStatus(UpdatePlanStatusDto statusDto);

    /**
     * 更新方案续约中状态
     *
     * @param renewalStatusDto
     */
    void updatePlanRenewalStatus(UpdatePlanRenewalStatusDto renewalStatusDto);

    /**
     * 批量续约
     *
     * @param batchRenewalDto
     */
    void batchRenewalPlan(BatchRenewalDto batchRenewalDto);
}
