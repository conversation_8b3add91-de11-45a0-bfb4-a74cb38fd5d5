package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.redis.cache.GetRedis;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.component.TranslationHelp;
import com.get.institutioncenter.dao.AreaCityInfoMapper;
import com.get.institutioncenter.dto.AreaCityInfoDto;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.po.AreaCityInfoPo;
import com.get.institutioncenter.vo.AreaCityInfoVo;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.entity.AreaCityInfo;
import com.get.institutioncenter.service.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * @author: Sea
 * @create: 2021/3/2 11:21
 * @verison: 1.0
 * @description:
 */
@Service
public class AreaCityInfoServiceImpl extends BaseServiceImpl<AreaCityInfoMapper, AreaCityInfo> implements IAreaCityInfoService {
    @Resource
    private AreaCityInfoMapper areaCityInfoMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IAreaCityInfoTypeService areaCityInfoTypeService;
    @Resource
    private IAreaCityService areaCityService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private ITranslationMappingService translationMappingService;
    @Resource
    private GetRedis getRedis;
    @Resource
    private TranslationHelp translationHelp;

    @Override
    public AreaCityInfoVo findAreaCityInfoById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AreaCityInfo areaCityInfo = areaCityInfoMapper.selectById(id);
        if (GeneralTool.isEmpty(areaCityInfo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        AreaCityInfoVo areaCityInfoVo = BeanCopyUtils.objClone(areaCityInfo, AreaCityInfoVo::new);
        areaCityInfoVo.setAreaCityInfoTypeName(areaCityInfoTypeService.getAreaCityInfoTypeNameById(areaCityInfoVo.getFkAreaCityInfoTypeId()));
        areaCityInfoVo.setAreaCityName(areaCityService.getCityFullNameById(areaCityInfoVo.getFkAreaCityId()));
        areaCityInfoVo.setFkTableName(TableEnum.INSTITUTION_AREA_CITY_INFO.key);
        StringJoiner stringJoiner = new StringJoiner(" ");
        if (GeneralTool.isNotEmpty(areaCityInfoVo.getPublicLevel())) {
            List<String> result = Arrays.asList(areaCityInfoVo.getPublicLevel().split(","));
            for (String name : result) {
                stringJoiner.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(name), ProjectExtraEnum.PUBLIC_OBJECTS));
            }
            areaCityInfoVo.setPublicLevelName(stringJoiner.toString());
        }
//        String language = getRedis.get("translation:" + GetAuthInfo.getLoginId());
        String language = SecureUtil.getLocale();
        AreaCityInfoPo areaCityInfoPo = BeanCopyUtils.objClone(areaCityInfoVo, AreaCityInfoPo::new);
        if (GeneralTool.isNotEmpty(areaCityInfoVo) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
            translationHelp.translation(Collections.singletonList(areaCityInfoPo), ProjectKeyEnum.getInitialValue(language));
        }
        if(GeneralTool.isNotEmpty(areaCityInfoPo)){
            areaCityInfoVo = BeanCopyUtils.objClone(areaCityInfoPo, AreaCityInfoVo::new);
        }
        return areaCityInfoVo;
    }

    @Override
    public Long add(AreaCityInfoDto areaCityInfoDto) {
        if (GeneralTool.isEmpty(areaCityInfoDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        AreaCityInfo areaCityInfo = BeanCopyUtils.objClone(areaCityInfoDto, AreaCityInfo::new);
        utilService.updateUserInfoToEntity(areaCityInfo);
        areaCityInfoMapper.insertSelective(areaCityInfo);
        return areaCityInfo.getId();
    }

    /**
     * 删除城市资讯
     *
     * @param id
     * @throws
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (areaCityInfoMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //同时删除该表id下的所有媒体附件
        attachedService.deleteMediaAndAttachedByTableId(id, TableEnum.INSTITUTION_AREA_CITY_INFO.key);
        areaCityInfoMapper.deleteById(id);
        //删除资讯翻译内容
        translationMappingService.deleteTranslations(TableEnum.INSTITUTION_AREA_CITY_INFO.key, id);
    }

    @Override
    public AreaCityInfoVo updateAreaCityInfo(AreaCityInfoDto areaCityInfoDto) {
        if (areaCityInfoDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        AreaCityInfo result = areaCityInfoMapper.selectById(areaCityInfoDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        AreaCityInfo areaCityInfo = BeanCopyUtils.objClone(areaCityInfoDto, AreaCityInfo::new);
        utilService.updateUserInfoToEntity(areaCityInfo);
        areaCityInfoMapper.updateById(areaCityInfo);
        return findAreaCityInfoById(areaCityInfo.getId());
    }

    @Override
    public List<AreaCityInfoVo> getAreaCityInfos(AreaCityInfoDto areaCityInfoDto, Page page) {
        LambdaQueryWrapper<AreaCityInfo> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(areaCityInfoDto)) {
            if (GeneralTool.isNotEmpty(areaCityInfoDto.getFkAreaCityId())) {
                wrapper.eq(AreaCityInfo::getFkAreaCityId, areaCityInfoDto.getFkAreaCityId());
            }
            if (GeneralTool.isNotEmpty(areaCityInfoDto)) {
                //查询条件-城市资讯类型
                if (GeneralTool.isNotEmpty(areaCityInfoDto.getFkAreaCityInfoTypeId())) {
                    wrapper.eq(AreaCityInfo::getFkAreaCityInfoTypeId, areaCityInfoDto.getFkAreaCityInfoTypeId());
                }
                //查询条件-公开对象
                if (GeneralTool.isNotEmpty(areaCityInfoDto.getPublicLevel())) {
                    wrapper.like(AreaCityInfo::getPublicLevel, areaCityInfoDto.getPublicLevel());
                }
                //查询条件-标题关键字
                if (GeneralTool.isNotEmpty(areaCityInfoDto.getTitle())) {
                    wrapper.like(AreaCityInfo::getTitle, areaCityInfoDto.getTitle());
                }
            }
        }
        wrapper.orderByDesc(AreaCityInfo::getViewOrder);
        //获取分页数据
        IPage<AreaCityInfo> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<AreaCityInfo> areaCityInfos = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<AreaCityInfoVo> convertDatas = new ArrayList<>();
        for (AreaCityInfo areaCityInfo : areaCityInfos) {
            AreaCityInfoVo areaCityInfoVo = BeanCopyUtils.objClone(areaCityInfo, AreaCityInfoVo::new);
            areaCityInfoVo.setAreaCityInfoTypeName(areaCityInfoTypeService.getAreaCityInfoTypeNameById(areaCityInfoVo.getFkAreaCityInfoTypeId()));
            areaCityInfoVo.setAreaCityName(areaCityService.getCityFullNameById(areaCityInfoVo.getFkAreaCityId()));
            StringJoiner stringJoiner = new StringJoiner(" ");
            if (GeneralTool.isNotEmpty(areaCityInfoVo.getPublicLevel())) {
                List<String> result = Arrays.asList(areaCityInfoVo.getPublicLevel().split(","));
                for (String name : result) {
                    stringJoiner.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(name), ProjectExtraEnum.PUBLIC_OBJECTS));
                }
                areaCityInfoVo.setPublicLevelName(stringJoiner.toString());
            }
            convertDatas.add(areaCityInfoVo);
        }
//        String language = getRedis.get("translation:" + GetAuthInfo.getLoginId());
        String language = SecureUtil.getLocale();
        if (GeneralTool.isNotEmpty(convertDatas) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
            translationHelp.translation(convertDatas, ProjectKeyEnum.getInitialValue(language));
        }
        return convertDatas;
    }

    @Override
    public List<MediaAndAttachedVo> getItemMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.INSTITUTION_AREA_CITY_INFO.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);

    }

    @Override
    public List<MediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.INSTITUTION_AREA_CITY_INFO.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }
}
