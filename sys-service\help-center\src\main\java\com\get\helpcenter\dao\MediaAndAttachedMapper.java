package com.get.helpcenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.helpcenter.vo.MediaAndAttachedVo;
import com.get.helpcenter.entity.HelpMediaAndAttached;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MediaAndAttachedMapper extends BaseMapper<HelpMediaAndAttached> {
    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(HelpMediaAndAttached record);

    Integer getNextIndexKey(@Param("tableId") Long tableId, @Param("tableName") String tableName);

    List<MediaAndAttachedVo> getMediaAndAttachedList(@Param("fkTableId") Long fkTableId);
}