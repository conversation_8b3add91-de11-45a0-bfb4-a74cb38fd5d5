package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.institutioncenter.vo.AppInfoVo;
import com.get.institutioncenter.vo.AppInfoFeignVo;
import com.get.institutioncenter.dto.AppInfoDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/12/10
 * @TIME: 14:54
 * @Description:
 **/
public interface IAppInfoService {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    AppInfoVo findAppInfoById(Long id);

    /**
     * 列表数据
     *
     * @param appInfoDto
     * @param page
     * @return
     */
    List<AppInfoVo> getAppInfos(AppInfoDto appInfoDto, Page page);

    /**
     * 修改
     *
     * @param appInfoDto
     * @return
     */
    AppInfoVo updateAppInfo(AppInfoDto appInfoDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 批量保存
     *
     * @param appInfoDtos
     * @return
     */
    void batchAdd(List<AppInfoDto> appInfoDtos);

    /**
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Description: 下拉
     * @Param []
     * <AUTHOR>
     */
    List<Map<String, Object>> findType();

    /**
     * 删除记录
     *
     * @return
     */
    void deleteAppInfoByCourseId(Long id);

    /**
     * 获取官网链接
     *
     * @Date 11:36 2021/6/23
     * <AUTHOR>
     */
    Map<Long, String> getWebSiteByTable(String tableName, String typeKey);

    /**
     * 获取官网链接  通过对象传递
     *
     * @Date 11:36 2021/6/23
     * <AUTHOR>
     */
    Map<Long, String> getWebSiteByAppInfoFeignDto(AppInfoFeignVo appInfoFeignVo);

}
