package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.ContractFormulaInstitution;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ContractFormulaInstitutionMapper extends BaseMapper<ContractFormulaInstitution> {
    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(ContractFormulaInstitution record);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过合同公式id 查找对应学校ids
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<Long> getInstitutionIdListByFkid(Long contractFormulaId);

    /**
     * @return java.util.List<java.lang.String>
     * @Description :通过合同公式id 查找对应学校名称(中英)
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<String> getInstitutionNameByFkid(Long contractFormulaId);
}