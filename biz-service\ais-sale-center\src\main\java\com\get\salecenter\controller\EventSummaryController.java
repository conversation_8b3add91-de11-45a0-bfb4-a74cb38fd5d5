package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.EventSummaryListVo;
import com.get.salecenter.vo.EventSummaryVo;
import com.get.salecenter.service.IEventSummaryService;
import com.get.salecenter.dto.EventSummaryListDto;
import com.get.salecenter.dto.EventSummaryUpdateDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/6/7 15:05
 * @verison: 1.0
 * @description: 活动汇总费用摘要管理
 */
@Api(tags = "活动汇总费用摘要管理")
@RestController
@RequestMapping("sale/summary")
public class EventSummaryController {

    @Resource
    private IEventSummaryService eventSummaryService;

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增信息
     * @Param []
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/活动汇总费用摘要管理/新增活动汇总费用摘要")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(EventSummaryUpdateDto.Add.class) EventSummaryUpdateDto eventSummaryVo) {
        return SaveResponseBo.ok(eventSummaryService.addEventSummary(eventSummaryVo));
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/活动汇总费用摘要管理/活动汇总费用摘要详情")
    @GetMapping("/{id}")
    public ResponseBo<EventSummaryVo> detail(@PathVariable("id") Long id) {
        EventSummaryVo data = eventSummaryService.findEventSummaryById(id);
        return new ResponseBo<>(data);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动汇总费用摘要管理/活动汇总费用摘要列表")
    @PostMapping("datas")
    public ResponseBo<EventSummaryListVo> datas(@RequestBody SearchBean<EventSummaryListDto> page) {
        List<EventSummaryListVo> datas = eventSummaryService.getEventSummaries(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentVo>
     * @Description: 修改信息
     * @Param [studentVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动汇总费用摘要管理/编辑")
    @PostMapping("update")
    public ResponseBo<EventSummaryVo> update(@RequestBody @Validated(EventSummaryUpdateDto.Update.class) EventSummaryUpdateDto eventSummaryUpdateDto) {
        return UpdateResponseBo.ok(eventSummaryService.updateeventSummary(eventSummaryUpdateDto));
    }

    /**
     * 删除
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/活动汇总费用摘要管理/删除接口")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        eventSummaryService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :上移下移
     * @Param [bankAccountVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动汇总费用摘要管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<EventSummaryUpdateDto> eventSummaryUpdateDtos) {
        eventSummaryService.movingOrder(eventSummaryUpdateDtos);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 下拉
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "活动摘要下拉框", notes = "")
    @GetMapping("getEventSummariesSelect")
    public ResponseBo<BaseSelectEntity> getEventSummariesSelect(@RequestParam("fkCompanyId") Long fkCompanyId) {
        return new ListResponseBo<>(eventSummaryService.getEventSummariesSelect(fkCompanyId));
    }
}
