package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2024/4/26
 * @TIME: 10:33
 * @Description:
 **/
@Data
public class KpiPlanGroupOrItemStatisticsResultVo {

    @ApiModelProperty(value = "KPI方案组别或子项统计表头列表")
    private List<KpiPlanGroupOrItemStatisticsHeadVo> kpiPlanGroupOrItemStatisticsHeadDtoList;

    @ApiModelProperty(value = "KPI方案组别或子项统计列表")
    private List<KpiPlanGroupOrItemStatisticsVo> kpiPlanGroupOrItemStatisticsBodyDtoList;
}
