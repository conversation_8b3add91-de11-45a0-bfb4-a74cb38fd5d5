package com.get.officecenter.service.impl;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.get.common.eunms.ErrorCodeEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.GetDateUtil;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.TemplateExcelUtils;
import com.get.officecenter.vo.*;
import com.get.officecenter.vo.AttendanceMachineDataVo;
import com.get.officecenter.entity.ClockInData;
import com.get.officecenter.entity.WorkScheduleDateConfig;
import com.get.officecenter.entity.WorkScheduleStaffConfig;
import com.get.officecenter.entity.WorkScheduleTimeConfig;
import com.get.officecenter.mapper.ClockInDataMapper;
import com.get.officecenter.service.ClockInDataService;
import com.get.officecenter.service.ILeaveApplicationFormService;
import com.get.officecenter.service.WorkScheduleDateConfigService;
import com.get.officecenter.service.WorkScheduleStaffConfigService;
import com.get.officecenter.service.WorkScheduleTimeConfigService;
import com.get.officecenter.utils.AttendanceUtils;
import com.get.officecenter.dto.ClockInDataDto;
import com.get.officecenter.dto.ClockInDataListDto;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2022/5/10
 * @TIME: 10:41
 * @Description:
 **/
@Service
@Slf4j
public class ClockInDataServiceImpl implements ClockInDataService {
    @Resource
    private UtilService utilService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ClockInDataMapper clockInDataMapper;
    @Resource
    private ILeaveApplicationFormService leaveApplicationFormService;
    @Resource
    private WorkScheduleDateConfigService workScheduleDateConfigService;
    @Resource
    private WorkScheduleTimeConfigService workScheduleTimeConfigService;
    @Resource
    private WorkScheduleStaffConfigService workScheduleStaffConfigService;

    @Override
    public List<ClockInDataVo> getAllClockInData(ClockInDataListDto clockInDataVo, Page page) {
        //获取当前公司或者部门的全部员工（不包含已经离职）
        List<StaffVo> staffVos = permissionCenterClient.getStaffDtos(clockInDataVo.getFkCompanyId(),clockInDataVo.getDepartmentId(), clockInDataVo.getKeyWord());
        if(GeneralTool.isEmpty(staffVos)){
            page.setAll(0);
            return null;
        }
        List<Long> fkStaffIdList = staffVos.stream().map(StaffVo::getId).collect(Collectors.toList());

        //获取考勤时间范围配置
        List<Integer> config= getAttachedRangeTimeConfig(clockInDataVo.getFkCompanyId());
        int year = GetDateUtil.getYear(clockInDataVo.getDate());
        int month = GetDateUtil.getMonth(clockInDataVo.getDate())+1;
        int actualMaximum= GetDateUtil.getActualMaximum(clockInDataVo.getDate());
        Date dateStart = null;
        Date dateEnd = null;
        if(GeneralTool.isEmpty(config)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("attachedRangeTimeConfig_not_set"));
        }
        if(config.size() < 2 && config.get(0).equals(0)){
            //考勤开始时间:指定月份的1号7点30分开始
            dateStart = AttendanceUtils.getOpenClockIn(year,month,1);
            clockInDataVo.setDateStart(dateStart);
            //考勤结束时间:指定月份的下一个月1号凌晨4点结束
            dateEnd = AttendanceUtils.getCloseClockIn(year,month,actualMaximum,actualMaximum);
            clockInDataVo.setDateEnd(dateEnd);
        }
        if(config.size() > 1){
            //根据配置自定义考勤时间范围
            dateStart = AttendanceUtils.getOpenClockIn(year,month-1,config.get(0));
            clockInDataVo.setDateStart(dateStart);
            dateEnd = AttendanceUtils.getCloseClockIn(year,month,config.get(1),actualMaximum);
            clockInDataVo.setDateEnd(dateEnd);
        }
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        log.info("考勤开始时间>>>>>>>>>{}",df.format(dateStart));
        log.info("考勤结束时间>>>>>>>>>{}",df.format(dateEnd));

        //查询所有打卡记录
        List<ClockInData> clockInDatas = clockInDataMapper.getAllClockInData(clockInDataVo);
        if(GeneralTool.isEmpty(clockInDatas)){
            page.setAll(0);
            return null;
        }
        List<ClockInDataVo> datas = clockInDatas.stream().map(clockInData-> BeanCopyUtils.objClone(clockInData, ClockInDataVo::new)).collect(Collectors.toList());


        //公司名称
        Map<Long, String> companyNamesByIds = staffVos.stream().collect(Collectors.toMap(StaffVo::getId, StaffVo::getCompanyName));
        //员工名称
        Map<Long, String> staffNamesByIds = staffVos.stream().collect(Collectors.toMap(StaffVo::getId, StaffVo::getName));
        for (ClockInDataVo clockInDataDto1 : datas) {
            //公司名
            clockInDataDto1.setFkCompanyName(companyNamesByIds.get(clockInDataDto1.getFkCompanyId()));
            //员工名
            clockInDataDto1.setFkStaffName(staffNamesByIds.get(clockInDataDto1.getFkStaffId()));
        }


        //获取所有员工的请假记录
        List<LeaveApplicationFormVo> leaveApplicationFormVos = leaveApplicationFormService.getLeaveApplicationFormList(new HashSet<>(fkStaffIdList),dateStart,dateEnd,clockInDataVo.getFkCompanyId());
        Map<Long, List<LeaveApplicationFormVo>> LeaveApplicationFormDtoMap = leaveApplicationFormVos.stream().collect(Collectors.groupingBy(LeaveApplicationFormVo::getFkStaffId));


        //根据时间范围的所有排班
        List<WorkScheduleDateConfig> dateConfigList = workScheduleDateConfigService.getWorkScheduleDateConfigByDate(dateStart,GetDateUtil.getYesterdayDate(dateEnd),clockInDataVo.getFkCompanyId(),null);


        //按员工id分组
        Map<Long,List<ClockInDataVo>> clockInDataByStaffIdMap = datas.stream().collect(Collectors.groupingBy(ClockInDataVo::getFkStaffId));
        if(GeneralTool.isEmpty(staffVos)){
            page.setAll(0);
            return null;
        }

        //获取排班时间设定
        List<WorkScheduleTimeConfig> timeConfigList = workScheduleTimeConfigService.getWorkScheduleTimeConfigList(clockInDataVo.getFkCompanyId());


        //特殊人员排班时间设定
        Map<Long,List<WorkScheduleStaffConfig>> staffConfigListMap = workScheduleStaffConfigService.getWorkScheduleStaffConfigList(clockInDataVo.getFkCompanyId());


        List<ClockInDataVo> resout = new ArrayList<>();
        for(StaffVo staffdto : staffVos){
            //员工对应打卡记录
            List<ClockInDataVo> dtos = clockInDataByStaffIdMap.get(staffdto.getId());
            //请假记录
            List<LeaveApplicationFormVo> leaveApplicationFormVoList =  LeaveApplicationFormDtoMap.get(staffdto.getId());
            List<ClockInDataVo> oneStaffClock = new ArrayList<>();

            Date onlyDate = dateStart;
            //判断员工对应的每一天打卡记录，是否存在漏卡，迟到情况
            while(onlyDate.compareTo(dateEnd) < 0){
                //特殊人员排班设定
                List<WorkScheduleStaffConfig> workScheduleStaffConfigs = staffConfigListMap.get(staffdto.getId());
                WorkScheduleStaffConfig staffConfig = AttendanceUtils.getWorkScheduleStaffConfig(onlyDate,null,workScheduleStaffConfigs);
                //校验是否为休假时间
                List<WorkScheduleDateConfig> configList = dateConfigList.stream().filter(c ->GeneralTool.isEmpty(c.getFkDepartmentId()) && c.getFkDepartmentId().equals(staffdto.getFkDepartmentId())).collect(Collectors.toList());
                Boolean ignoreDate= AttendanceUtils.dayOff(onlyDate,configList,staffConfig);
                if(ignoreDate){
                    //第二天
                    onlyDate = GetDateUtil.getTomorrowDate(onlyDate);
                    continue;
                }




                //有效打卡开始时间
                Date openClockIn = AttendanceUtils.getOpenClockIn(GetDateUtil.getYear(onlyDate),GetDateUtil.getMonth(onlyDate)+1,GetDateUtil.getDay(onlyDate));

                //有效打卡结束时间
                actualMaximum= GetDateUtil.getActualMaximum(onlyDate);
                Date closeClockIn = AttendanceUtils.getCloseClockIn(GetDateUtil.getYear(onlyDate),GetDateUtil.getMonth(onlyDate)+1,GetDateUtil.getDay(onlyDate),actualMaximum);


                //上班时间
                List<Integer> workingStartList = getWorkScheduleTimeConfig(timeConfigList,staffConfig,staffdto.getFkCompanyId(),staffdto.getFkDepartmentId(),1);
                Date workingStart = GetDateUtil.setTime(onlyDate,workingStartList.get(0),workingStartList.get(1),workingStartList.get(2));

                //下班时间
                List<Integer> workingEndList = getWorkScheduleTimeConfig(timeConfigList,staffConfig,staffdto.getFkCompanyId(),staffdto.getFkDepartmentId(),2);
                Date workingEnd = GetDateUtil.setTime(onlyDate,workingEndList.get(0),workingEndList.get(1),workingEndList.get(2));

                //上班后两个小时
                 Date startTime = GetDateUtil.getAdvanceDateByHour(workingStart,-2L);

                //下班前两个小时
                 Date endTime = GetDateUtil.getAdvanceDateByHour(workingEnd,2L);


                //午休开始时间
                List<Integer> noonBreakStartList = getWorkScheduleTimeConfig(timeConfigList,staffConfig,staffdto.getFkCompanyId(),staffdto.getFkDepartmentId(),3);
                Date noonBreakStart =  null;
                if(GeneralTool.isNotEmpty(noonBreakStartList)){
                    noonBreakStart = GetDateUtil.setTime(onlyDate,noonBreakStartList.get(0),noonBreakStartList.get(1),noonBreakStartList.get(2));
                }


                //午休结束时间
                List<Integer> noonBreakEndList = getWorkScheduleTimeConfig(timeConfigList,staffConfig,staffdto.getFkCompanyId(),staffdto.getFkDepartmentId(),4);
                Date noonBreakEnd = null;
                if(GeneralTool.isNotEmpty(noonBreakEndList)){
                    noonBreakEnd = GetDateUtil.setTime(onlyDate,noonBreakEndList.get(0),noonBreakEndList.get(1),noonBreakEndList.get(2));
                }


                //员工对应一天的所有有效打卡记录
                List<ClockInDataVo> activeOneDayClockIn = new ArrayList<>();
                //员工对应一天的所有无效打卡记录
                List<ClockInDataVo> lnactiveOneDayClockIn = new ArrayList<>();

                if(GeneralTool.isNotEmpty(dtos)){
                    for(ClockInDataVo dto : dtos){
                        //打卡记录设置员工对应部门名称
                        dto.setDepartmentName(staffdto.getDepartmentName());
                        //判断是否符合打卡范围时间
                        if(dto.getClockInTime().compareTo(openClockIn) > 0 && dto.getClockInTime().compareTo(closeClockIn) < 0){
                            if(dto.getIsActive()){
                                activeOneDayClockIn.add(dto);
                            }else{
                                lnactiveOneDayClockIn.add(dto);
                            }

                        }
                    }
                }


                //检查迟到、漏卡情况
                List<ClockInDataVo> onDutyClockIns = new ArrayList<>();
                List<ClockInDataVo> offDutyClockIns = new ArrayList<>();
                for(ClockInDataVo dto:activeOneDayClockIn){
                    //上班卡
                    if(openClockIn.compareTo(dto.getClockInTime())<= 0 && startTime.compareTo(dto.getClockInTime())>=0 ){
                        onDutyClockIns.add(dto);
                    }
                    //下班卡
                    if(endTime.compareTo(dto.getClockInTime())<0 && closeClockIn.compareTo(dto.getClockInTime())>=0){
                        offDutyClockIns.add(dto);
                    }
                }

               //校验上班卡是否存在漏卡
               if(onDutyClockIns.size()>0){
                   for(ClockInDataVo onDutyClockIn : onDutyClockIns){
                       onDutyClockIn.setAttendanceTime(workingStart);
                       if(onDutyClockIn.getClockInTime().compareTo(workingStart) > 0){
                           //迟到
                           onDutyClockIn.setClockInState(2);
                       }else{
                           //正常
                           onDutyClockIn.setClockInState(1);
                       }
                   }
               }else{
                   //校验是否存在工休单记录
                   Boolean isLeave = false;
                   if(GeneralTool.isNotEmpty(noonBreakStart)){
                       isLeave = AttendanceUtils.isLeave(leaveApplicationFormVoList,workingStart,noonBreakStart);
                   }else{
                       isLeave = AttendanceUtils.isLeave(leaveApplicationFormVoList,workingStart,workingEnd);
                   }

                   //无请假记录并且入职时间小于等于这一天
                   if(!isLeave && GeneralTool.isNotEmpty(staffdto.getEntryDate()) && staffdto.getEntryDate().compareTo(onlyDate)<=0){
                       ClockInDataVo clockInDataDto1 = getClockInDataDto(staffdto.getId(), staffdto.getName(),staffdto.getDepartmentName(),
                               staffdto.getFkCompanyId(),staffdto.getCompanyName(),workingStart);
                       onDutyClockIns.add(clockInDataDto1);
                   }

               }

               //校验下班卡是否漏卡
               if(offDutyClockIns.size()>0){
                   for(ClockInDataVo onDutyClockIn : offDutyClockIns){
                       onDutyClockIn.setAttendanceTime(workingEnd);
                       //正常
                       onDutyClockIn.setClockInState(1);
                   }
               }else{
                   //校验是否存在工休单记录
                   Boolean isLeave = false;
                   if(GeneralTool.isNotEmpty(noonBreakEnd)){
                       isLeave = AttendanceUtils.isLeave(leaveApplicationFormVoList,noonBreakEnd,workingEnd);
                   }else{
                       isLeave = AttendanceUtils.isLeave(leaveApplicationFormVoList,workingStart,workingEnd);
                   }

                   //无请假记录并且入职时间小于等于这一天
                   if(!isLeave && GeneralTool.isNotEmpty(staffdto.getEntryDate()) && staffdto.getEntryDate().compareTo(onlyDate)<=0){
                       //添加漏卡记录
                       ClockInDataVo clockInDataDto1 = getClockInDataDto(staffdto.getId(),staffdto.getName(),staffdto.getDepartmentName(),
                               staffdto.getFkCompanyId(),staffdto.getCompanyName(),workingEnd);
                       offDutyClockIns.add(clockInDataDto1);
                   }
               }
                oneStaffClock.addAll(onDutyClockIns);
                oneStaffClock.addAll(offDutyClockIns);


                //无效打卡记录赋值考勤时间
                for(ClockInDataVo dto:lnactiveOneDayClockIn){
                    if(workingStart.compareTo(dto.getClockInTime())<= 0 && startTime.compareTo(dto.getClockInTime())>=0 ){
                        dto.setAttendanceTime(workingStart);
                    }
                    if(endTime.compareTo(dto.getClockInTime())<0 && workingEnd.compareTo(dto.getClockInTime())>=0){
                        dto.setAttendanceTime(workingEnd);
                    }
                }
                //第二天
                onlyDate = GetDateUtil.getTomorrowDate(onlyDate);
                oneStaffClock.addAll(lnactiveOneDayClockIn);
            }

            resout.addAll(oneStaffClock);
        }

        //过滤数据来源
        if(GeneralTool.isNotEmpty(clockInDataVo.getDataSource())){
           resout = resout.stream().filter(c->GeneralTool.isNotEmpty(c.getDataSource()))
                   .filter(c->c.getDataSource().equals(clockInDataVo.getDataSource()))
                   .collect(Collectors.toList());
        }

        //过滤打卡结果
        if(GeneralTool.isNotEmpty(clockInDataVo.getClockInState())){
            resout = resout.stream().filter(c->GeneralTool.isNotEmpty(c.getClockInState()))
                    .filter(c->c.getClockInState().equals(clockInDataVo.getClockInState()))
                    .collect(Collectors.toList());
        }

        //有效无效过滤
        if(GeneralTool.isNotEmpty(clockInDataVo.getIsActive())){
            resout = resout.stream().filter(c->GeneralTool.isNotEmpty(c.getIsActive()))
                    .filter(c->c.getIsActive().equals(clockInDataVo.getIsActive()))
                    .collect(Collectors.toList());
        }

        if(GeneralTool.isNotEmpty(page)){
            //当前页
            int currentPage = page.getCurrentPage();
            //页条数
            int pageSize = page.getShowCount();
            //总条数
            int total = resout.size();
            //开始条数
            int currIdx = (currentPage - 1) * pageSize;
            //结束条数
            int toIndex = currIdx + pageSize;
            //截取返回
            resout =toIndex > total ? resout.subList(currIdx, total) : resout.subList(currIdx, toIndex);
            page.setAll(total);
        }

        return resout;
    }

    /**
     * 获取考勤时间范围系统配置
     * @param fkCompanyId
     * @return
     */
    public List<Integer> getAttachedRangeTimeConfig(Long fkCompanyId) {
        if (GeneralTool.isEmpty(fkCompanyId)){
            fkCompanyId = SecureUtil.getFkCompanyId();
        }

        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.ATTENDANCE_RANGE_TIME.key, 1).getData();
        String configValue1 = companyConfigMap.get(fkCompanyId);
        if (GeneralTool.isEmpty(configValue1)) {
            return null;
        }
        JSONArray jsonArray = JSON.parseArray(configValue1);
        return jsonArray.toJavaList(Integer.class);

//        ConfigVo data = permissionCenterClient.getConfigByKey(ProjectKeyEnum.ATTENDANCE_RANGE_TIME.key).getData();
//        if (GeneralTool.isNotEmpty(data) && GeneralTool.isNotEmpty(data.getValue1())){
//            JSONObject jsonObject = JSONObject.parseObject(data.getValue1());
//            JSONArray otherConfigs = jsonObject.getJSONArray("OTHER");
//            JSONArray iaeConfigs = jsonObject.getJSONArray("IAE");
//            List<Integer> otherConfigList = otherConfigs.toJavaList(Integer.class);
//            List<Integer> iaeConfigList = iaeConfigs.toJavaList(Integer.class);
//            if (fkCompanyId.equals(3L)){
//                return iaeConfigList;
//            }else{
//                    return otherConfigList;
//            }
//        }
//        return null;
    }

    /**
     * 获取排班时间设定
     * @param departmentId 部门ID
     * @param type  类型：1-获取上班时间，2-获取下班时间，3-获取中午休息开始时间，4-获取中午休息结束时间
     * @return 返回时分秒组成的list 例如:八点三十分[8,30,0]
     */
    public List<Integer> getWorkScheduleTimeConfig(List<WorkScheduleTimeConfig> timeConfigList,
                                                   WorkScheduleStaffConfig staffConfig,
                                                   Long fkCompanyId,
                                                   Long departmentId,
                                                   Integer type){
        List<WorkScheduleTimeConfig> list  = timeConfigList.stream().filter(t->GeneralTool.isNotEmpty(t.getFkDepartmentId())).collect(Collectors.toList());
        Map<Long, WorkScheduleTimeConfig> timeConfigMap = list.stream()
                .collect(Collectors.toMap(WorkScheduleTimeConfig::getFkDepartmentId, Function.identity()));
        //通过部门获取对应时间设定
        WorkScheduleTimeConfig workScheduleTimeConfig = timeConfigMap.get(departmentId);
        //部门未设置时间设定使用默认
        if(GeneralTool.isEmpty(workScheduleTimeConfig)){
            List<WorkScheduleTimeConfig> configs =  timeConfigList.stream().filter(c->GeneralTool.isEmpty(c.getFkDepartmentId()) && c.getFkCompanyId().equals(fkCompanyId)).collect(Collectors.toList());
            if(GeneralTool.isNotEmpty(configs)){
                workScheduleTimeConfig = configs.get(0);
            }
        }

        if(GeneralTool.isEmpty(workScheduleTimeConfig)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("workScheduleTimeConfig_not_set"));
        }

        //存在特殊人员时间设定,使用该时间设定
        if(GeneralTool.isNotEmpty(staffConfig)){
            switch (type) {
                case 1:
                    //上班时间
                    return formatWorkTime(staffConfig.getWorkingStartTime());
                case 2:
                    //下班时间
                    return formatWorkTime(staffConfig.getWorkingEndTime());
                case 3:
                    //午休开始时间
                    if(GeneralTool.isNotEmpty(staffConfig.getNoonBreakStartTime())){
                        return formatWorkTime(staffConfig.getNoonBreakStartTime());
                    }else{
                        return null;
                    }

                case 4:
                    //午休结束时间
                    if(GeneralTool.isNotEmpty(staffConfig.getNoonBreakEndTime())){
                        return formatWorkTime(staffConfig.getNoonBreakEndTime());
                    }else{
                        return null;
                    }
            }
        }else{
            switch (type) {
                case 1:
                    //上班时间
                    return formatWorkTime(workScheduleTimeConfig.getWorkingStartTime());
                case 2:
                    //下班时间
                    return formatWorkTime(workScheduleTimeConfig.getWorkingEndTime());
                case 3:
                    //午休开始时间
                    if(GeneralTool.isNotEmpty(workScheduleTimeConfig.getNoonBreakStartTime())){
                        return formatWorkTime(workScheduleTimeConfig.getNoonBreakStartTime());
                    }else{
                        return null;
                    }

                case 4:
                    //午休结束时间
                    if(GeneralTool.isNotEmpty(workScheduleTimeConfig.getNoonBreakEndTime())){
                        return formatWorkTime(workScheduleTimeConfig.getNoonBreakEndTime());
                    }else{
                        return null;
                    }

            }
        }

      return null;
    }

    public List<Integer> formatWorkTime(String workingStartTime){
        String[] workingStart = workingStartTime.split(":");
        ArrayList<String> stringList = new ArrayList<>(workingStart.length);
        Collections.addAll(stringList,workingStart);
        return stringList.stream().map(Integer::valueOf).collect(Collectors.toList());
    }

    @Override
    public void exportClockInDataExcel(HttpServletResponse response, ClockInDataListDto clockInDataVo) throws Exception {
        List<ClockInDataVo> clockInDataVoList = getAllClockInData(clockInDataVo,null);
        List<ClockInDataExcelVo> excelDtos = new ArrayList<>();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if(GeneralTool.isNotEmpty(clockInDataVoList)){
            for(ClockInDataVo dto : clockInDataVoList){
                ClockInDataExcelVo clockInDataExcelVo = new ClockInDataExcelVo();
                clockInDataExcelVo.setFkStaffName(dto.getFkStaffName());
                clockInDataExcelVo.setDepartmentName(dto.getDepartmentName());
                if(GeneralTool.isNotEmpty(dto.getAttendanceTime())){
                    clockInDataExcelVo.setAttendanceTime(dateFormat.format(dto.getAttendanceTime()));
                }
                if(GeneralTool.isNotEmpty(dto.getClockInTime())){
                    clockInDataExcelVo.setClockInTime(dateFormat.format(dto.getClockInTime()));
                }

                if(GeneralTool.isNotEmpty(dto.getClockInState())){
                    switch (dto.getClockInState()){
                        case 1:
                            clockInDataExcelVo.setClockInStateName("正常");
                            break;
                        case 2:
                            clockInDataExcelVo.setClockInStateName("迟到");
                            break;
                        case 3:
                            clockInDataExcelVo.setClockInStateName("缺卡");
                            break;
                    }
                }

                if(GeneralTool.isNotEmpty(dto.getDataSource())){
                    switch (dto.getDataSource()){
                        case 0:
                            clockInDataExcelVo.setDataSourceName("打卡机");
                            break;
                        case 1:
                            clockInDataExcelVo.setDataSourceName("钉钉");
                            break;
                        case 2:
                            clockInDataExcelVo.setDataSourceName("系统录入");
                            break;
                    }
                }
                if(GeneralTool.isNotEmpty(dto.getIsActive())){
                    clockInDataExcelVo.setIsActiveName(dto.getIsActive()?"是":"否");
                }
                clockInDataExcelVo.setGmtCreateUser(dto.getGmtCreateUser());
                if(GeneralTool.isNotEmpty(dto.getGmtCreate())){
                    clockInDataExcelVo.setGmtCreate(dateFormat.format(dto.getGmtCreate()));
                }
                excelDtos.add(clockInDataExcelVo);
            }
        }

        Map<String, Object> param = new HashMap<>();
        if(GeneralTool.isNotEmpty(excelDtos)){
            param.put("list", excelDtos);
        }
        TemplateExcelUtils.downLoadExcel("考勤时间管理", "ClockInData.xlsx", param, response);
    }


    /**
     * 漏卡回补对象
     * @return
     */
    public ClockInDataVo getClockInDataDto(Long fkStaffId, String fkStaffName, String departmentName, Long fkCompanyId, String fkCompanyName, Date attendanceTime){
        ClockInDataVo clock = new ClockInDataVo();
        clock.setFkCompanyName(fkCompanyName);
        clock.setFkCompanyId(fkCompanyId);
        clock.setDepartmentName(departmentName);
        clock.setFkStaffId(fkStaffId);
        clock.setFkStaffName(fkStaffName);
        clock.setAttendanceTime(attendanceTime);
        clock.setClockInState(3);
        clock.setIsActive(null);
        return clock;
    }

    @Override
    public void addClockInData(ClockInDataDto clockInDataDto){
        if (GeneralTool.isEmpty(clockInDataDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ClockInData clockInData = BeanCopyUtils.objClone(clockInDataDto, ClockInData::new);
        //数据来源设置为系统录入
        clockInData.setDataSource(2);
        clockInData.setIsActive(true);
        utilService.updateUserInfoToEntity(clockInData);
        clockInDataMapper.insert(clockInData);
    }

    @Override
    public void updateActive(Long id, Boolean isActive) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ClockInData clockInData = new ClockInData();
        clockInData.setId(id);
        clockInData.setIsActive(isActive);
        utilService.updateUserInfoToEntity(clockInData);
        clockInDataMapper.updateById(clockInData);
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public ResponseBo importAttendanceMachineData(Long fkCompanyId,MultipartFile file){
        if(GeneralTool.isEmpty(fkCompanyId)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            ExcelReader reader = ExcelUtil.getReader(inputStream);
//            if("HTI".equals(interfaceConfiguration)){
                reader.addHeaderAlias("EnNo","attendanceNum");
                reader.addHeaderAlias("Mode","clockInTime");
//            }else{
//                reader.addHeaderAlias("部门","departmentName");
//                reader.addHeaderAlias("员工名称","staffName");
//                reader.addHeaderAlias("考勤号码","attendanceNum");
//                reader.addHeaderAlias("日期时间","clockInTime");
//                reader.addHeaderAlias("机器号","machineNum");
//                reader.addHeaderAlias("编号","number");
//                reader.addHeaderAlias("比对方式","contrastType");
//                reader.addHeaderAlias("卡号","cardNumber");
//            }
            List<AttendanceMachineDataVo> list = reader.readAll(AttendanceMachineDataVo.class);
            Set<String> attendanceNums = list.stream().map(AttendanceMachineDataVo::getAttendanceNum).collect(Collectors.toSet());
            Map<String,Long> staffIdMap= permissionCenterClient.getStaffIdsByAttendanceNum(attendanceNums,fkCompanyId).getData();


            //如存在无法查询到对应用户的数据，返回前端进行提示处理
            Set<String> attendanceNumList = new HashSet<>(staffIdMap.keySet());
            List<String> unableToCheckList = attendanceNums.stream().filter(item -> !attendanceNumList.contains(item)).collect(Collectors.toList());
            attendanceNums = attendanceNums.stream().filter(item -> attendanceNumList.contains(item)).collect(Collectors.toSet());
            List<AttendanceMachineDataVo> dataList = new ArrayList<>();
            for(String attendanceNum : attendanceNums){
               List<AttendanceMachineDataVo> dtoList = list.stream().filter(l->attendanceNum.equals(l.getAttendanceNum())).collect(Collectors.toList());
                dataList.addAll(dtoList);
            }

            for(AttendanceMachineDataVo dto:dataList){
                dto.setStaffId(staffIdMap.get(dto.getAttendanceNum()));
                utilService.setCreateInfo(dto);
            }
            if(GeneralTool.isNotEmpty(dataList)){
                //删除与当前导入考勤数据打卡年月相同的数据
                clockInDataMapper.deleteClockInData(dataList.get(0).getClockInTime(),0,fkCompanyId);
                //插入数据库
                clockInDataMapper.importAttendanceMachineData(dataList,fkCompanyId);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseBo.error(ErrorCodeEnum.INVALID_PARAM.getCode(),"导入文件异常");
        }
        ResponseBo responseBo = new ResponseBo(true);
        responseBo.setCode(ErrorCodeEnum.REQUEST_OK.getCode());
        responseBo.setMessage("数据导入成功");
        return responseBo;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public ResponseBo importDingTalkAttendanceData(Long fkCompanyId ,MultipartFile file){
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            //第三个sheet
            ExcelReader reader = ExcelUtil.getReader(inputStream,2);
            reader.addHeaderAlias("姓名","staffName");
            reader.addHeaderAlias("考勤组","attendanceGroup");
            reader.addHeaderAlias("部门","departmentName");
            reader.addHeaderAlias("工号","num");
            reader.addHeaderAlias("职位","position");
            reader.addHeaderAlias("考勤日期","attendanceDate");
            reader.addHeaderAlias("考勤时间","attendanceTime");
            reader.addHeaderAlias("打卡时间","punchTime");
            reader.addHeaderAlias("打卡结果","punchResult");
            reader.addHeaderAlias("打卡地址","punchAddress");
            reader.addHeaderAlias("打卡备注","remark");
            reader.addHeaderAlias("异常打卡原因","abnormalReason");
            reader.addHeaderAlias("打卡图片","punchPhoto");
            reader.addHeaderAlias("打卡设备","punchCardEquipment");
            List<DingTalkAttendanceVo> dingTalkAttendanceVos = reader.read(2,3, DingTalkAttendanceVo.class);

            //状态设置，打卡无效设置无效状态
             String invalidPunch = "打卡无效";
             dingTalkAttendanceVos.stream().forEach(d-> d.setIsActive(d.getPunchResult().indexOf(invalidPunch)==-1 ? true : false));

            List<BaseSelectEntity> entities = new ArrayList<>();
            for(DingTalkAttendanceVo dto: dingTalkAttendanceVos){
                String nameEn = dto.getStaffName();
                String nameChn = dto.getStaffName();
                BaseSelectEntity entity = new BaseSelectEntity();
                entity.setNameChn(nameEn.replaceAll("[A-Za-z]", ""));
                entity.setName(nameChn.replaceAll("[^A-Za-z]", ""));
                entity.setFullName(dto.getStaffName());
                entities.add(entity);
            }
            //去重
            entities = entities.stream().distinct().collect(Collectors.toList());
            Map<String,BaseSelectEntity> staffIdMap= permissionCenterClient.getStaffIdsByNameAndEnName(entities).getData();

            //如存在无法查询到对应员工的数据，返回前端进行提示处理
            Set<String> staffNames = dingTalkAttendanceVos.stream().map(DingTalkAttendanceVo::getStaffName).collect(Collectors.toSet());
            Set<String> staffNameList = new HashSet<>(staffIdMap.keySet());
            List<String> unableToCheckList = staffNames.stream().filter(item -> !staffNameList.contains(item)).collect(Collectors.toList());
            if(GeneralTool.isNotEmpty(unableToCheckList)){
                ResponseBo responseBo = new ResponseBo(true);
                responseBo.setData(unableToCheckList);
                responseBo.setCode(ErrorCodeEnum.REQUEST_OK.getCode());
                responseBo.setMessage("查询不到相关员工信息");
            }

            for(DingTalkAttendanceVo dt: dingTalkAttendanceVos){
                BaseSelectEntity entity = staffIdMap.get(dt.getStaffName());
                if(GeneralTool.isNotEmpty(entities)){
                    dt.setStaffId(entity.getId());
                }
                utilService.setCreateInfo(dt);
            }
            //删除与当前导入考勤数据打卡年月相同的数据
            clockInDataMapper.deleteClockInData(dingTalkAttendanceVos.get(0).getPunchTime(),1,fkCompanyId);
            //插入数据库
            clockInDataMapper.importDingTalkAttendanceData(dingTalkAttendanceVos, fkCompanyId);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseBo.error(ErrorCodeEnum.INVALID_PARAM.getCode(),"录入文件异常");
        }
        ResponseBo responseBo = new ResponseBo(true);
        responseBo.setCode(ErrorCodeEnum.REQUEST_OK.getCode());
        responseBo.setMessage("数据导入成功");
        return responseBo;
    }

    @Override
    public List<ClockInData> getClockInData(Date startTime,Date endTime,Long fkCompanyId){
        return clockInDataMapper.getClockInData(startTime,endTime,fkCompanyId);
    }

}
