package com.get.remindercenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("m_remind_task_queue")
public class RemindTaskQueue extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 提醒任务id
     */
    @ApiModelProperty(value = "提醒任务id")
    private Long fkRemindTaskId;
    /**
     * 提醒方式：0系统内/1邮件/2短信
     */
    @ApiModelProperty(value = "提醒方式：0系统内/1邮件/2短信")
    private String remindMethod;
    /**
     * 任务执行时间
     */
    @ApiModelProperty(value = "任务执行时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date optTime;
    /**
     * 错误尝试次数
     */
    @ApiModelProperty(value = "错误尝试次数")
    private Integer tryTimes;
    /**
     * 获取错误信息，累加update
     */
    @ApiModelProperty(value = "获取错误信息，累加update")
    private String tryError;
}