package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("r_receipt_form_invoice")
public class ReceiptFormInvoice extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 收款单Id
     */
    @ApiModelProperty(value = "收款单Id")
    private Long fkReceiptFormId;

    /**
     * 发票Id
     */
    @ApiModelProperty(value = "发票Id")
    private Long fkInvoiceId;

}