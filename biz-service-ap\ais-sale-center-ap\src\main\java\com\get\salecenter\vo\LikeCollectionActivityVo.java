package com.get.salecenter.vo;

import com.get.salecenter.dto.MediaAndAttachedDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2024/10/9 11:27
 * @verison: 1.0
 * @description:
 */
@Data
public class LikeCollectionActivityVo {

    @ApiModelProperty(value = "新媒体推广助力赛Id")
    private Long id;

    @ApiModelProperty(value = "集赞类型")
    private Integer type;

    @ApiModelProperty(value = "峰会id")
    private Long fkConventionId;

    @ApiModelProperty(value = "参会人员英文名称")
    private String name;

    @ApiModelProperty(value = "参会人员中文名称")
    private String nameChn;

    @ApiModelProperty(value = "电话")
    private String tel;

    @ApiModelProperty(value = "昵称")
    private String nickname;

    @ApiModelProperty(value = "社交平台id")
    private String platformAccount;

    @ApiModelProperty(value = "集赞数")
    private Integer likeCount;

    @ApiModelProperty(value = "状态：0未审批/1已审批")
    private Integer status;

    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "附件")
    List<MediaAndAttachedVo> mediaAndAttachedDtos;
}
