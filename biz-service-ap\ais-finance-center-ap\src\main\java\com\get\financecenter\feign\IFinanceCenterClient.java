package com.get.financecenter.feign;

import com.get.common.constant.AppCenterConstant;
import com.get.core.tool.api.Result;
import com.get.financecenter.dto.*;
import com.get.financecenter.entity.ExpenseClaimForm;
import com.get.financecenter.entity.TravelClaimForm;
import com.get.financecenter.vo.*;
import com.get.financecenter.vo.AlreadyPayVo;
import com.get.financecenter.entity.PrepayApplicationForm;
import com.get.financecenter.entity.Invoice;
import com.get.financecenter.entity.InvoiceReceivablePlan;
import com.get.financecenter.entity.PaymentApplicationForm;
import com.get.financecenter.entity.PaymentForm;
import com.get.financecenter.dto.InvoiceDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Feign接口类
 */
@FeignClient(
        value = AppCenterConstant.APPLICATION_FINANCE_CENTER
)
public interface IFinanceCenterClient {
    String API_PREFIX = "/feign";

    /**
     * @Description :feign调用 根据币种编号 查找币种名称
     */
    String GET_CURRENCY_NAME_BY_NUM = API_PREFIX + "/get-currency-name-by-num";

    String GET_ALREADY_PAY_BY_PLAN_IDS = API_PREFIX + "/get-already-pay-by-plan-ids";
    /**
     * @Description :feign调用 根据币种编号nums 查找币种名称map
     */
    String GET_CURRENCY_NAMES_BY_NUMS = API_PREFIX + "/get-currency-names-by-nums";
    /**
     * @Description: feign调用 根据币种编号nums查找币种名称map（只返回名称）
     */
    String GET_NEW_CURRENCY_NAMES_BY_NUMS = API_PREFIX + "/get-new-currency-names-by-nums";
    /**
     * 获取付款单列表
     */
    String GET_PAY_FORM_LIST = API_PREFIX + "/get-pay-form-list";
    /**
     * 获取收款单列表
     */
    String GET_RECEIPT_FORM_LIST = API_PREFIX + "/get-receipt-form-list";
    /**
     * @Description :feign调用 根据当前币种和目标币种获取汇率
     */
    String GET_EXCHANGE_RATE = API_PREFIX + "/get-exchange-rate";
    /**
     * :汇率自动批量新增
     */
    String BATCH_ADD_AU_TO = API_PREFIX + "/batch-add-au-to";
    /**
     * 获取汇率
     */
    String GET_LAST_EXCHANGE_RATE = API_PREFIX + "/get-last-exchange-rate";
    String GET_MPAY_BY_ID = API_PREFIX + "/get-mpay-by-id";
    String UPDATE_MPAY = API_PREFIX + "/update-mpay";
    String UPDATE_BORROW_MONEY_STATUS = API_PREFIX + "/update-borrow-money-status";
    String GET_BORROW_MONEY_BY_ID = API_PREFIX + "/get-borrow-money-by-id";
    /**
     * 修改流程表单状态
     */
    String CHANGE_STATUS = API_PREFIX + "/change-status";
    /**
     * 根据应收计划ids获取所绑定的收款单信息
     */
    String GET_RECEIPT_FORM_LIST_FEIGN_BY_PLAN_IDS = API_PREFIX + "/get-receipt-form-list-feign-by-plan-ids";
    String IMPORT_RE_GEA= API_PREFIX + "/import-re-gea";
    String IMPORT_PAY_GEA=API_PREFIX + "/import-pay-gea";
    String IMPORT_PAYMENT_NEW_RECORD = API_PREFIX + "/import-payment-new-record";

    /**
     * 有预付的情况：根据应收计划id判断收款状态，收齐了才能生成分期表进行结算
     */
    String IS_PAY_IN_ADVANCE_INSERT_SETTLEMENT_INSTALLMENT = "/is-pay-in-advance-insert-settlement-installment";
    /**
     * 根据应收计划id获取收款单子单列表
     */
    String GET_RECEIPT_FORM_ITEM_LIST_FEIGN_BY_PLAN_IDS =  API_PREFIX + "/get-receipt-form-item-list-feign-by-plan-ids";
    /**
     * feign 根据收款单子单ids获取收款单子单信息
     */
    String GET_RECEIPT_FORM_ITEM_LIST_FEIGN_BY_FORM_ITEM_IDS = API_PREFIX + "/get-receipt-form-item-list-feign-by-form-item-ids";
    /**
     * 根据应付计划ids获取所绑定的付款单
     */
    String GET_PAY_FORM_LIST_FEIGN_BY_PLAN_IDS = API_PREFIX + "/get-pay-form-list-feign-by-plan-ids";
    /**
     * 获取收款单详情
     */
    String GET_RECEIPT_FORM_BY_FORM_ID = API_PREFIX + "/get-receipt-form-by-form-id";
    /**
     * 根据应收计划ids获取对应发票编号
     */
    String GET_FK_INVOICE_NUM = API_PREFIX + "/get-fk-invoice-num";

    /**
     * 根据应收计划id获取收款单子项
     */
    String GET_RECEIPT_FORM_LIST_FEIGN = API_PREFIX + "/get-receipt-form-list-feign";
    /**
     * 根据应付计划id获取应付折合金额
     *
     * @Date 15:47 2022/6/9
     * <AUTHOR>
     */
    String GET_AMOUNT_PAID_BY_PAYABLE_PLAN_ID = API_PREFIX + "/get-amount-paid-by-payable-plan-id";
    /**
     * feign 查找对应的应收计划未结算过的收款单子单
     */
    String GET_SETTLEMENT_RECEIPT_FORM_ITEM_LIST_BY_PLAN_ID = API_PREFIX + "/get-settlement-receipt-form-item-list-by-plan-id";
    /**
     * 根据发票编号获取已绑定的应收计划
     */
    String GET_RECEIVABLE_PLAN_IDS=API_PREFIX + "get-receivable-plan-ids";
    String GENERATE_INVOICE =API_PREFIX + "/generate-invoice";
    String GENERATE_INVOICE_RECEIVABLE_PLAN =API_PREFIX + "/generate-invoice-receivable-plan";
    String IS_RECEIVABLE_PLAN_BOUND =API_PREFIX + "/is-receivable-plan-bound";
    String UPDATE_INVOICE =API_PREFIX + "/update-invoice";
    String GET_INVOICE_BY_NUM =API_PREFIX + "/get-invoice-by-num";
    String CREATE_INVOICE_AND_TARGET_MAPPING = API_PREFIX + "/create-invoice-and-target-mapping";
    String UPDATE_INVOICE_AND_TARGET_MAPPING = API_PREFIX + "/update-invoice-and-target-mapping";
    String GET_RECEIPT_FORM_ITEMS_BY_FORM_IDS = API_PREFIX + "/get-receipt-form-items-by-form-ids";
    String GET_INVOICE_BY_NUM_LIST = API_PREFIX + "/get-invoice-by-num-list";
    String GET_MAX_PO_NUM = API_PREFIX + "/get-max-po-num";
    String IS_EXIST_NUM = API_PREFIX + "/is-exist-num";
    String UPDATE_FEE = API_PREFIX + "/update-fee";
    String GET_PAY_FORM_ITEM_COUNT = API_PREFIX + "/get-pay-form-item-count";
    String GET_INVOICE_NUM_BY_RECEIVABLE_ID = API_PREFIX + "/get-invoice-num-by-receivable-id";
    String GET_LAST_EXCHANGE_RATE_HKD = API_PREFIX + "get-last-exchange-rate-hkd";
    String RECEIVABLE_BINDING_INVOICE = API_PREFIX + "/receivable-binding-invoice";
    String GET_PAY_FORM_BY_ID = API_PREFIX + "/get-pay-form-id";
    String GET_INVOICE_COMMISSION_NOTICE = API_PREFIX +"/get-invoice-commission-notice";
    String GET_RECEIPT_FORM_BY_INVOICE_ID = API_PREFIX + "/get-receipt-form-by-invoice-id";
    String UPDATE_RECEIPT_AND_PAYMENT_FORM_STATUS = API_PREFIX + "/update-receipt-and-payment-form-status";
    String CHECK_RECEIPT_INVOICE_MAPPING_EXIST = API_PREFIX +"/check-receipt-invoice-mapping-exist";
    String UN_BIND_INVOICE_BY_RECEIVABLE_PLAN_IDS = API_PREFIX +"/un-bind-invoice-by-receivable-plan-ids";
    String GET_CURRENCY_BY_PUBLIC_LEVEL = API_PREFIX +"/get-currency-by-public-level";
    String UPDATE_INVOICE_RECEIVABLE_PLAN = API_PREFIX +"/update-invoice-receivable-plan";
    String GET_INVOICE_IDS_BY_RECEIVABLE_PLAN = API_PREFIX + "/get-invoice-ids-by-receivable-plan";
    String PREPAYMENT_BUTTON_HTI = API_PREFIX + "/get-prepayment-button-hti";
    String CANCEL_PREPAYMENT_BUTTON_HTI = API_PREFIX + "/cancel-prepayment-button-hti";
    String GET_INVOICE_AMOUNT_BY_IDS = API_PREFIX + "/get-invoice-amount-by-ids";

    String GET_UPDATE_INVOICE_RECEIVABLE_PLAN_AMOUNT = API_PREFIX + "/get-update-invoice-receivable-plan-amount";
    String GET_PAYMENT_FORM_ITEM_LIST = API_PREFIX + "/get-payment-form-item-list";
    String SAVE_BATCH_RECEIPT_FORMS = API_PREFIX + "/save-batch-receipt-forms";
    String SAVE_BATCH_PAYMENT_FORMS = API_PREFIX + "/save-batch-payment-forms";
    String GET_BATCH_LAST_EXCHANGE_RATE = API_PREFIX + "/get-batch-last-exchange-rate";
    String GET_BANK_NAME_BY_IDS = API_PREFIX + "/get-bank-name-by-ids";
    String GET_RECEIPT_FORMS_BY_TARGET_IDS = API_PREFIX + "/get-receipt-forms-by-target-ids";
    String ACTIVATE_COMMISSION_SETTLEMENT_BY_RECEIVABLE_PLAN_IDS = API_PREFIX + "/activate-commission-settlement-by-receivable-plan-ids";
    String ACTIVATE_COMMISSION_SETTLEMENT = API_PREFIX + "/activate-commission-settlement";
    String GET_INVOICE_RECEIVABLE_PLAN_BY_ID = API_PREFIX + "/get-invoice-receivable-plan-by-id";
    String GET_RECEIPT_FORMS_BY_FEE_IDS = API_PREFIX + "/get-receipt-forms-by-fee-ids";
    String GET_ACCOUNT_COMMISSION_SETTLEMENT_STATUS = API_PREFIX + "/get-account-commission-settlement-status";
    String UPDATE_COMMISSION_SETTLEMENT_ACCOUNT_CURRENCY_TYPE_NUM = API_PREFIX + "/update-commission-settlement-account-currency-type-num";
    String GET_COMMISSION_SETTLEMENT_ACCOUNT_INFO = API_PREFIX + "/get-commission-settlement-account-info";
    String CHECK_EXIST_ACCOUNT_COMMISSION_SETTLEMENT = API_PREFIX + "/check-exist-account-commission-settlement";
    String SETTLEMENT_INSTALLMENT_UPDATE = API_PREFIX + "/settlement-installment-update";
    String CHECK_PAYABLE_INFO = API_PREFIX + "/check-payable-info";
    String INSERT_SETTLEMENT_INSTALLMENT_BATCH = API_PREFIX + "/insert-settlement-installment-batch";
    String DELETE_SETTLEMENT_COMMISSION_BY_PAYABLE_PLAN_ID = API_PREFIX + "/delete-settlement-commission-by-payable-plan-id";
    String ONE_CLICK_SETTLEMENT = API_PREFIX + "/one-click-settlement";
    String GET_SETTLEMENT_FLAG_AGENT_IDS = API_PREFIX + "/get-settlement-flag-agent-ids";
    String DELETE_UNSETTLED_COMMISSION_BY_ADMISSION_FAILURE = API_PREFIX + "/delete-unsettled-commission-by-admission-failure";
    String GET_TRAVEL_CLAIM_FORM_BY_ID = API_PREFIX + "/get-travel-claim-form-by-id";
    String GET_EXPENSE_CLAIM_FORM_BY_ID = API_PREFIX + "/get-expense-claim-form-by-id";
    String GET_PREPAY_APPLICATION_FORM_BY_ID = API_PREFIX + "/get-prepay-application-form-by-id";
    String GET_PAYMENT_APPLICATION_FORM_BY_ID = API_PREFIX + "/get-payment-application-form-by-id";
    String UPDATE_EXPENSE_CLAIM_FORM_STATUS = API_PREFIX + "/update-expense-claim-form-status";
    String UPDATE_TRAVEL_CLAIM_FORM_STATUS = API_PREFIX + "/update-travel-claim-form-status";
    String UPDATE_PREPAY_APPLICATION_FORM_STATUS = API_PREFIX + "/update-prepay-application-form-status";
    String GET_EXPENSE_CLAIM_FORM_TOTAL_AMOUNT = API_PREFIX + "/get-expense-claim-form-total-amount";
    String GET_TRAVEL_CLAIM_FORM_TOTAL_AMOUNT = API_PREFIX + "/get-travel-claim-form-total-amount";
    String GET_PAYMENT_APPLICATION_FORM_TOTAL_AMOUNT = API_PREFIX + "/get-payment-application-form-total-amount";
    String CREATE_INSURANCE_PAYMENT_FORM = API_PREFIX + "/create-insurance-payment-form";
    String GET_FORM_INFORMATION_BY_FORM_ID = API_PREFIX + "/get-form-information-by-form-id";


    /**
     * @return java.lang.String
     * @Description :feign调用 根据币种编号 查找币种名称
     * @Param [num]
     * <AUTHOR>
     */
    @GetMapping(GET_CURRENCY_NAME_BY_NUM)
    Result<String> getCurrencyNameByNum(@RequestParam("fkCurrencyTypeNum") String fkCurrencyTypeNum);


    /**
     * 获取已付信息
     * @param planIds
     * @return
     */
    @PostMapping(GET_ALREADY_PAY_BY_PLAN_IDS)
    List<AlreadyPayVo> getAlreadyPayByPlanIds(@RequestBody Set<Long> planIds);
    /**
     * @Description :feign调用 根据币种编号nums 查找币种名称map
     * @Param [nums]
     *
     * <AUTHOR>
     */
    @PostMapping(GET_CURRENCY_NAMES_BY_NUMS)
    Result<Map<String, String>> getCurrencyNamesByNums(@RequestBody Set<String> fkCurrencyTypeNums);
    /**
     * @Description: feign调用 根据币种编号nums查找币种名称map（只返回名称）
     * @Author: Jerry
     * @Date:11:48 2021/11/20
     */
    @PostMapping(GET_NEW_CURRENCY_NAMES_BY_NUMS)
    Result<Map<String, String>> getNewCurrencyNamesByNums(@RequestBody Set<String> nums);

    /**
     * 获取付款单列表
     * @param planId
     * @return
     */
    @GetMapping(GET_PAY_FORM_LIST)
    Result<List<PaymentFormVo>> getPayFormList(@RequestParam("planId") Long planId);

    /**
     * 获取收款单列表
     * @param planId
     * @return
     */
    @GetMapping(GET_RECEIPT_FORM_LIST)
    Result<List<ReceiptFormVo>> getReceiptFormList(@RequestParam("planId") Long planId);
    /**
     * @return
     * @Description :feign调用 根据当前币种和目标币种获取汇率
     * @Param
     * <AUTHOR>
     */
    @GetMapping(GET_EXCHANGE_RATE)
    Result<BigDecimal> getExchangeRate(@RequestParam("fromCurrency") String fromCurrency, @RequestParam("toCurrency") String toCurrency);

    /**
     * :汇率自动批量新增
     * @param exchangeRateDtos
     * @return
     */
    @PostMapping(BATCH_ADD_AU_TO)
    Result<Boolean> batchAddAuTo(@RequestBody List<ExchangeRateDto> exchangeRateDtos);

    /**
     * 获取汇率
     *
     * @Date 14:18 2022/3/30
     * <AUTHOR>
     */
    @PostMapping(GET_LAST_EXCHANGE_RATE)
    Result<BigDecimal> getLastExchangeRate(@RequestParam("last") Boolean last, @RequestParam("fromCurrency") String fromCurrency, @RequestParam("toCurrency") String toCurrency);

    @GetMapping(GET_MPAY_BY_ID)
    Result<PaymentApplicationFormVo> getMpayById(@RequestParam("id") Long id);

    @PostMapping(UPDATE_MPAY)
    Result updateMpay(@RequestBody PaymentApplicationForm payForm);

    @PostMapping(UPDATE_BORROW_MONEY_STATUS)
    Result<Boolean> updateBorrowMoneyStatus(@RequestBody PrepayApplicationForm prepayApplicationForm);

    @GetMapping(GET_BORROW_MONEY_BY_ID)
    Result<PrepayApplicationFormVo> getBorrowMoneyById(@RequestParam("id") Long id);

    /**
     * 修改流程表单状态
     * @param status
     * @param tableName
     * @param businessKey
     * @return
     */
    @PostMapping(CHANGE_STATUS)
    Result<Boolean> changeStatus(@RequestParam("status") Integer status, @RequestParam("tableName") String tableName, @RequestParam("businessKey") Long businessKey);
    /**
     * 根据应收计划ids获取所绑定的收款单信息
     *
     * @Date 16:49 2021/11/19
     * <AUTHOR>
     */
    @PostMapping(GET_RECEIPT_FORM_LIST_FEIGN_BY_PLAN_IDS)
    List<ReceiptFormVo> getReceiptFormListFeignByPlanIds(@RequestBody Set<Long> planIds);

    /**
     * feign 根据应收计划id获取绑定的收款单子单列表
     *
     * @Date 17:58 2022/4/21
     * <AUTHOR>
     */
    @PostMapping(GET_RECEIPT_FORM_ITEM_LIST_FEIGN_BY_PLAN_IDS)
    List<ReceiptFormItemVo> getReceiptFormItemListFeignByPlanIds(@RequestBody Set<Long> planIds);

    /**
     * feign 根据收款单子单ids获取收款单子单信息
     *
     * @Date 17:58 2022/4/21
     * <AUTHOR>
     */
    @PostMapping(GET_RECEIPT_FORM_ITEM_LIST_FEIGN_BY_FORM_ITEM_IDS)
    List<ReceiptFormItemVo> getReceiptFormItemListFeignByFormItemIds(@RequestBody Set<Long> receiptFormItemIds);

    /**
     * 根据应付计划ids获取所绑定的付款单
     *
     * @Date 16:49 2021/11/19
     * <AUTHOR>
     */
    @PostMapping(GET_PAY_FORM_LIST_FEIGN_BY_PLAN_IDS)
    List<PaymentFormVo> getPayFormListFeignByPlanIds(@RequestBody Set<Long> planIds);
    /**
     * 获取收款单详情
     *
     * @param id
     * @return
     */
    @GetMapping(GET_RECEIPT_FORM_BY_FORM_ID)
    ReceiptFormVo getReceiptFormByFormId(@RequestParam("id") Long id);
    /**
     * 根据应收计划ids获取对应发票编号
     * @param planIds
     * @return
     */
    @PostMapping(GET_FK_INVOICE_NUM)
    Map<Long, String> getFkInvoiceNum(@RequestBody Set<Long> planIds);


    /**
     * 根据应收计划id获取收款单子项
     *
     * @Date 16:49 2021/11/19
     * <AUTHOR>
     */
    @GetMapping(GET_RECEIPT_FORM_LIST_FEIGN)
    Result<List<ReceiptFormVo>> getReceiptFormListFeign(@RequestParam("planId") Long planId);

    /**
     * 根据应付计划id获取应付折合金额
     *
     * @Date 15:47 2022/6/9
     * <AUTHOR>
     */
    @GetMapping(GET_AMOUNT_PAID_BY_PAYABLE_PLAN_ID)
    BigDecimal getAmountPaidByPayablePlanId(@RequestParam("planId") Long payablePlanId);


    /**
     * 根据发票编号获取已绑定的应收计划
     */
    @GetMapping(GET_RECEIVABLE_PLAN_IDS)
    List<Long> getReceivablePlanIdsFeign(@RequestParam("invoiceId")Long invoiceId);


    /**
     * feign 查找对应的应收计划未结算过的收款单子单
     *
     * @Date 16:49 2021/11/19
     * <AUTHOR>
     */
    @GetMapping(GET_SETTLEMENT_RECEIPT_FORM_ITEM_LIST_BY_PLAN_ID)
    List<ReceiptFormItemVo> getSettlementReceiptFormItemListByPlanId(@RequestParam("receivablePlanId") Long receivablePlanId);

    @PostMapping(IMPORT_RE_GEA)
    Result<BigDecimal> importReGea(@RequestBody List<OccVo> receivedOcList, @RequestParam("receivablePlanId")Long receivablePlanId, @RequestParam("targetId") Long targetId
            , @RequestParam("currency")String currency);

    @PostMapping(IMPORT_PAY_GEA)
    void importPayGea(@RequestBody List<OccVo> hiPaidInfoList, @RequestParam("payablePlanId") Long payablePlanId, @RequestParam("currency") String currency
            , @RequestParam("agentId") Long agentId, @RequestParam("payableAmount") Double payableAmount, @RequestParam("receivableFee") Double receivableFee);

    @PostMapping(IMPORT_PAYMENT_NEW_RECORD)
    void importPaymentNewRecord(@RequestBody OccVo occVo, @RequestParam("payablePlanId") Long payablePlanId, @RequestParam("currency") String currency
            , @RequestParam("agentId") Long agentId, @RequestParam("payableAmount") Double payableAmount, @RequestParam("backId") Long backId);
//    /**
//     * 有预付的情况：根据应收计划id判断收款状态，收齐了才能生成分期表进行结算
//     *
//     * @return
//     * @Date 23:10 2022/4/21
//     * <AUTHOR>
//     */
//    @PostMapping(IS_PAY_IN_ADVANCE_INSERT_SETTLEMENT_INSTALLMENT)
//    Result isPayInAdvanceInsertSettlementInstallment(@RequestParam("fkReceivablePlanId") Long fkReceivablePlanId);

    @PostMapping(GENERATE_INVOICE)
    Result<Long> generateInvoice(@RequestBody InvoiceDto invoiceDto);

    @PostMapping(GENERATE_INVOICE_RECEIVABLE_PLAN)
    Result generateInvoiceReceivablePlan(@RequestBody InvoiceReceivablePlanDto invoiceReceivablePlanDto);

    @PostMapping(IS_RECEIVABLE_PLAN_BOUND)
    Result<Boolean> isReceivablePlanBound(@RequestParam("fkReceivablePlanId") Long fkReceivablePlanId);

    @PostMapping(UPDATE_INVOICE)
    Result updateInvoice(@RequestBody InvoiceDto invoiceDto);

    @PostMapping(GET_INVOICE_BY_NUM)
    Result<List<Invoice>> getInvoiceByNum(@RequestParam("invoiceNum") String invoiceNum);

    /**
     * 创建单条发票与渠道目标对象映射
     * @param typeKey
     * @param invoiceId
     * @param targetId
     */
    @GetMapping(CREATE_INVOICE_AND_TARGET_MAPPING)
    void createInvoiceAndTargetMapping(@RequestParam("typeKey") String typeKey,@RequestParam("invoiceId") Long invoiceId,
                                       @RequestParam("targetId") Long targetId);

    /**
     * 更新单条发票与渠道目标对象映射
     * @param typeKey
     * @param invoiceId
     * @param targetId
     */
    @GetMapping(UPDATE_INVOICE_AND_TARGET_MAPPING)
    void updateInvoiceAndTargetMapping(@RequestParam("typeKey") String typeKey,@RequestParam("invoiceId") Long invoiceId,
                                       @RequestParam("targetId") Long targetId);

    /**
     * 根据收款单ids获取收款单子项
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_RECEIPT_FORM_ITEMS_BY_FORM_IDS)
    Result<List<ReceiptFormItemVo>> getReceiptFormItemsByFormIds(@RequestBody Set<Long> ids);

    /**
     *
     * @param numSet
     * @return
     */
    @PostMapping(GET_INVOICE_BY_NUM_LIST)
    Result<List<InvoiceVo>> getInvoiceByNumList(@RequestBody Set<String> numSet);

    /**
     * 获取发票序号
     *
     * @param num
     * @return
     */
    @PostMapping(GET_MAX_PO_NUM)
    Result<Integer> getMaxPoNum(@RequestParam("num") String num);

    @PostMapping(IS_EXIST_NUM)
    Result<Invoice> isExistNum(@RequestParam("poNum") Integer poNum,@RequestParam("invoiceNum")  String invoiceNum);

    @PostMapping(UPDATE_FEE)
    void updateFee(@RequestBody Set<Long> payItemIds);

    @PostMapping(GET_PAY_FORM_ITEM_COUNT)
    Result<Integer> getPayFormItemCount(@RequestBody Set<Long> payPlanIds);

    /**
     * 获取发票编号
     * @param ids
     * @return
     */
    @PostMapping(GET_INVOICE_NUM_BY_RECEIVABLE_ID)
    Map<Long, String> getInvoiceNumByReceivableId(@RequestBody Set<Long> ids);

    @PostMapping(GET_LAST_EXCHANGE_RATE_HKD)
    Result<Map<String,BigDecimal>> getLastExchangeRateHkd(@RequestBody Set<String> toCurrency);

    @PostMapping(RECEIVABLE_BINDING_INVOICE)
    void receivableBindingInvoice(@RequestParam("receivablePlanId")Long receivablePlanId,@RequestParam("amount")BigDecimal amount,@RequestParam("invoiceId") Long invoiceId);

    @GetMapping(GET_PAY_FORM_BY_ID)
    PaymentForm getPayFormById(@RequestParam("paymentFormId")Long paymentFormId);

    @GetMapping(GET_INVOICE_COMMISSION_NOTICE)
    Map<Long, String> getInvoiceCommissionNotice(@RequestParam("fkInvoiceId")Long fkInvoiceId);

    @GetMapping(GET_RECEIPT_FORM_BY_INVOICE_ID)
    List<ReceiptFormItemVo> getReceiptFormByInvoiceId(@RequestParam("fkInvoiceId") Long fkInvoiceId);

    @PostMapping(UPDATE_RECEIPT_AND_PAYMENT_FORM_STATUS)
    Boolean updateReceiptAndPaymentFormStatus(@RequestParam("paymentFormIds") Set<Long> paymentFormIds, @RequestParam("receiptFormIds") Set<Long> receiptFormIds);

    /**
     * 校验发票是否合收款单绑定
     * @param invoiceId
     * @return
     */
    @GetMapping(CHECK_RECEIPT_INVOICE_MAPPING_EXIST)
    Boolean checkReceiptInvoiceMappingExist(@RequestParam("invoiceId") Long invoiceId);

    /**
     * 解除与发票的绑定
     * 如果该发票只有这条绑定记录，同时作废发票
     * @param receivablePlanIdset
     * @return
     */
    @PostMapping(UN_BIND_INVOICE_BY_RECEIVABLE_PLAN_IDS)
    Boolean unBindInvoiceByReceivablePlanIds(@RequestBody List<Long> receivablePlanIdset);

    /**
     * 根据PublicLevel获取币种
     * @param key
     * @return
     */
    @PostMapping(GET_CURRENCY_BY_PUBLIC_LEVEL)
    Result<List<CurrencyTypeVo>> getCurrencyByPublicLevel(@RequestParam("key")Integer key);


    @PostMapping(UPDATE_INVOICE_RECEIVABLE_PLAN)
    Result updateInvoiceReceivablePlan(@RequestParam("invoiceId") Long invoiceId,@RequestParam("receivablePlanId") Long receivablePlanId,@RequestParam("amount") BigDecimal amount);

    /**
     * 根据receivablePlanId获取invoiceId
     * @param receivablePlanId
     * @return
     */
    @PostMapping(GET_INVOICE_IDS_BY_RECEIVABLE_PLAN)
    Result<List<Long>> getInvoiceIdsByReceivablePlanId(@RequestParam("receivablePlanId")Long receivablePlanId);

    /**
     * HTI预付按钮
     *
     * @Date 18:12 2024/6/14
     * <AUTHOR>
     */
    @PostMapping(PREPAYMENT_BUTTON_HTI)
    Result prepaymentButtonHti(@RequestBody List<PrepaymentButtonHtiVo> prepaymentButtonHtiDtoList);

    /**
     * 获取发票绑定金额
     *
     * @Date 9:59 2024/6/17
     * <AUTHOR>
     */
    @PostMapping(GET_INVOICE_AMOUNT_BY_IDS)
    Result<Map<Long, InvoiceReceivablePlan>> getInvoiceAmountByIds(@RequestBody List<Long> fkInvoiceReceivablePlanIds);

    /**
     * 更新发票绑定金额
     *
     * @Date 13:36 2024/6/21
     * <AUTHOR>
     */
    @PostMapping(GET_UPDATE_INVOICE_RECEIVABLE_PLAN_AMOUNT)
    Result updateInvoiceReceivablePlanAmount(@RequestBody Map<Long, BigDecimal> map);

    /**
     * 根据keys和学生id集合获取付款单子项
     *
     * @param keys       m_student_offer_item（留学申请计划）
     *                   m_student_insurance（留学保险）
     *                   m_student_accommodation（留学住宿）
     *                   m_student_service_fee（留学服务费）
     * @param studentIds 学生id集合
     * @return
     */
    @PostMapping(GET_PAYMENT_FORM_ITEM_LIST)
    Result<Map<Long, Integer>> getPaymentFormItemList(@RequestParam("keys") List<String> keys, @RequestParam("studentIds") Set<Long> studentIds);

    /**
     * 留学服务费，批量创建收款单，涉及表：
     * m_receipt_form、m_receipt_form_item
     *
     * @param serviceFeeReceiptFormDto
     */
    @PostMapping(SAVE_BATCH_RECEIPT_FORMS)
    Result saveBatchReceiptForms(@RequestBody ServiceFeeReceiptFormDto serviceFeeReceiptFormDto);

    /**
     * 留学服务费，批量创建付款单，涉及表：
     * m_payment_form、m_payment_form_item
     *
     * @param serviceFeePaymentFormDto
     */
    @PostMapping(SAVE_BATCH_PAYMENT_FORMS)
    Result saveBatchPaymentForms(@RequestBody ServiceFeePaymentFormDto serviceFeePaymentFormDto);

    /**
     * 批量获取实时汇率
     *
     * @param fromCurrencySet 参照币种编号集合
     * @param toCurrency      目标币种编号
     * @return
     */
    @PostMapping(GET_BATCH_LAST_EXCHANGE_RATE)
    Result<Map<String, BigDecimal>> getBatchLastExchangeRate(@RequestBody Set<String> fromCurrencySet, @RequestParam("toCurrency") String toCurrency);

    /**
     * 批量获取付款银行名称
     *
     * @param ids   付款银行id
     * @return
     */
    @PostMapping(GET_BANK_NAME_BY_IDS)
    Result<Map<Long, String>> getBankNameByIds(@RequestBody Set<Long> ids);

    /**
     * 根据目标类型关键字和目标id集合获取收款单
     *
     * @param fkTypeKey       目标类型关键字，枚举：m_institution_provider学校供应商/m_student学生
     * @param fkTypeTargetIds 目标id集合
     * @return key：目标id，value：收款单集合
     */
    @PostMapping(GET_RECEIPT_FORMS_BY_TARGET_IDS)
    Result<Map<Long, List<ReceiptFormVo>>> getReceiptFormsByTargetIds(@RequestParam("fkTypeKey") String fkTypeKey, @RequestBody Set<Long> fkTypeTargetIds);

    /**
     * 根据应收计划id批量激活佣金结算
     * 对照 finance/receiptFormItem/activateCommissionSettlement 接口
     *
     * @param receivablePlanIds 应收计划id集合
     * @return
     */
    @PostMapping(ACTIVATE_COMMISSION_SETTLEMENT_BY_RECEIVABLE_PLAN_IDS)
    Result<Boolean> activateCommissionSettlementByReceivablePlanIds(@RequestBody Set<Long> receivablePlanIds);

    /**
     * 激活佣金
     * @param receiptFormItemIds
     * @return
     */
    @PostMapping(ACTIVATE_COMMISSION_SETTLEMENT)
    Result<Boolean> activateCommissionSettlement(@RequestBody Set<Long> receiptFormItemIds);

    /**
     * 根据发票应收绑定id,发票应收绑定对象
     * @param fkInvoiceReceivablePlanId
     * @return
     */
    @PostMapping(GET_INVOICE_RECEIVABLE_PLAN_BY_ID)
    Result<InvoiceReceivablePlan> getInvoiceReceivablePlanById(@RequestParam("fkInvoiceReceivablePlanId") Long fkInvoiceReceivablePlanId);

    /**
     * 根据服务费id获取对应的收款单信息
     * @param feeIds
     * @return
     */
    @PostMapping(GET_RECEIPT_FORMS_BY_FEE_IDS)
    Result<Map<Long, List<ReceiptFormVo>>> getReceiptFormsByFeeIds(@RequestBody List<Long> feeIds);

    /**
     * 查询银行账户佣金结算状态
     * @param accountId
     * @return
     */
    @PostMapping(GET_ACCOUNT_COMMISSION_SETTLEMENT_STATUS)
    Result<Integer> getAccountCommissionSettlementStatus(@RequestParam("accountId") Long accountId);

    /**
     * 修改未结算佣金的银行币种标记
     * @param accountId
     * @param fkCurrencyTypeNum
     * @return
     */
    @PostMapping(UPDATE_COMMISSION_SETTLEMENT_ACCOUNT_CURRENCY_TYPE_NUM)
    Result updateCommissionSettlementAccountCurrencyTypeNum(@RequestParam("accountId") Long accountId, @RequestParam("fkCurrencyTypeNum") String fkCurrencyTypeNum);

    /**
     * 佣金结算标记 true:已有佣金结算 false:无佣金结算数据
     * @param accountId
     * @return
     */
    @GetMapping(GET_COMMISSION_SETTLEMENT_ACCOUNT_INFO)
    Result<Boolean> getCommissionSettlementAccountInfo(@RequestParam("accountId") Long accountId);

    /**
     * 检查银行账户是否存在佣金数据
     * @param agentAccountId
     * @return
     */
    @GetMapping(CHECK_EXIST_ACCOUNT_COMMISSION_SETTLEMENT)
    Result<Boolean> checkExistAccountCommissionSettlement(@RequestParam("agentAccountId") Long agentAccountId);

    /**
     * 佣金结算更新
     * @param settlementInstallmentUpdateDto
     * @return
     */
    @PostMapping(SETTLEMENT_INSTALLMENT_UPDATE)
    Result<Boolean> settlementInstallmentUpdate(@RequestBody SettlementInstallmentUpdateDto settlementInstallmentUpdateDto);

    /**
     * 检查应付计划结算状态，没有结算就返回true
     * @param payablePlanIds
     * @return
     */
    @PostMapping(CHECK_PAYABLE_INFO)
    Result<Boolean> checkPayableInfo(@RequestBody Set<Long> payablePlanIds);


    /**
     * 批量更新、插入 分期表数据
     * @param settlementInstallmentBatchUpdateDtos
     * @return
     */
    @PostMapping(INSERT_SETTLEMENT_INSTALLMENT_BATCH)
    Result insertSettlementInstallmentBatch(@RequestBody List<SettlementInstallmentBatchUpdateDto> settlementInstallmentBatchUpdateDtos);


    /**
     * 作废应付时，删除佣金结算数据
     * @param payablePlanId
     * @return
     */
    @PostMapping(DELETE_SETTLEMENT_COMMISSION_BY_PAYABLE_PLAN_ID)
    Result<Boolean> deleteSettlementCommissionByPayablePlanId(@RequestParam("payablePlanId") Long payablePlanId);

    /**
     * 应付计划一键结算按钮
     * @param oneClickSettlementDto
     * @return
     */
    @PostMapping(ONE_CLICK_SETTLEMENT)
    Result<Boolean> oneClickSettlement(@RequestBody OneClickSettlementDto oneClickSettlementDto);

    /**
     * 获取待结算标记代理ids
     * @return
     */
    @GetMapping(GET_SETTLEMENT_FLAG_AGENT_IDS)
    Result<List<Long>> getSettlementFlagAgentIds();

    /**
     * 入学失败，需要删除对应的未结算完的佣金数据,并返回是否有结算数据 true为有数据
     * @return
     */
    @PostMapping(DELETE_UNSETTLED_COMMISSION_BY_ADMISSION_FAILURE)
    Result<Boolean> deleteUnsettledCommissionByAdmissionFailure(@RequestBody List<Long> payablePlanIdSet);

    /**
     * 根据id获取差旅报销单
     * @param targetId
     * @return
     */
    @PostMapping(GET_TRAVEL_CLAIM_FORM_BY_ID)
    Result<TravelClaimForm> getTravelClaimFormById(@RequestParam("targetId") Long targetId);

    /**
     * 根据id获取费用报销单
     * @param targetId
     * @return
     */
    @PostMapping(GET_EXPENSE_CLAIM_FORM_BY_ID)
    Result<ExpenseClaimForm> getExpenseClaimFormById(@RequestParam("targetId") Long targetId);

    /**
     * 根据id获取借款申请单
     * @param targetId
     * @return
     */
    @PostMapping(GET_PREPAY_APPLICATION_FORM_BY_ID)
    Result<PrepayApplicationForm> getPrepayApplicationFormById(@RequestParam("targetId") Long targetId);

    /**
     * 根据id获取支付申请单
     * @param targetId
     * @return
     */
    @PostMapping(GET_PAYMENT_APPLICATION_FORM_BY_ID)
    Result<PaymentApplicationForm> getPaymentApplicationFormById(@RequestParam("targetId") Long targetId);

    /**
     * 修改费用报销单状态
     * @param expenseClaimForm
     * @return
     */
    @PostMapping(UPDATE_EXPENSE_CLAIM_FORM_STATUS)
    Result<Boolean> updateExpenseClaimFormStatus(@RequestBody ExpenseClaimForm expenseClaimForm);

    /**
     * 修改差旅报销单状态
     * @param travelClaimForm
     * @return
     */
    @PostMapping(UPDATE_TRAVEL_CLAIM_FORM_STATUS)
    Result<Boolean> updateTravelClaimFormStatus(@RequestBody TravelClaimForm travelClaimForm);

    /**
     * 修改借款申请单状态
     * @param paymentApplicationForm
     * @return
     */
    @PostMapping(UPDATE_PREPAY_APPLICATION_FORM_STATUS)
    Result<Boolean> updatePaymentApplicationFormStatus(@RequestBody PaymentApplicationForm paymentApplicationForm);

    /**
     * 获取费用报销单总金额
     * @param id
     * @return
     */
    @PostMapping(GET_EXPENSE_CLAIM_FORM_TOTAL_AMOUNT)
    Result<BigDecimal> getExpenseClaimFormTotalAmount(@RequestParam("id") Long id);

    /**
     * 获取差旅报销单总金额
     * @param id
     * @return
     */
    @PostMapping(GET_TRAVEL_CLAIM_FORM_TOTAL_AMOUNT)
    Result<BigDecimal> getTravelClaimFormTotalAmount(@RequestParam("id") Long id);

    /**
     * 获取借款申请单总金额
     * @param id
     * @return
     */
    @PostMapping(GET_PAYMENT_APPLICATION_FORM_TOTAL_AMOUNT)
    Result<BigDecimal> getPaymentApplicationFormTotalAmount(@RequestParam("id") Long id);

    /**
     * 澳小保创建实付
     *
     * @param insurancePaymentFormDto
     * @return
     */
    @PostMapping(CREATE_INSURANCE_PAYMENT_FORM)
    Result<List<InsurancePaymentFormVo>> createInsurancePaymentForm(@RequestBody InsurancePaymentFormDto insurancePaymentFormDto);

    /**
     * 根据报销单id获取报销单信息
     * @param fkTableName
     * @param fkTableId
     * @return
     */
    @GetMapping(GET_FORM_INFORMATION_BY_FORM_ID)
    Result<FormInformationVo> getFormInformationByFormId(@RequestParam("fkTableName") String fkTableName, @RequestParam("fkTableId") Long fkTableId);
}
