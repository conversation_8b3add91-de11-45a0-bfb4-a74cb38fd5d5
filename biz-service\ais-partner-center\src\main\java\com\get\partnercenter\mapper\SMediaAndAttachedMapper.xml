<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.partnercenter.mapper.SMediaAndAttachedMapper">

    <resultMap id="BaseResultMap" type="com.get.partnercenter.entity.SMediaAndAttachedEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkFileGuid" column="fk_file_guid" jdbcType="VARCHAR"/>
            <result property="fkTableName" column="fk_table_name" jdbcType="VARCHAR"/>
            <result property="fkTableId" column="fk_table_id" jdbcType="BIGINT"/>
            <result property="typeKey" column="type_key" jdbcType="VARCHAR"/>
            <result property="indexKey" column="index_key" jdbcType="INTEGER"/>
            <result property="link" column="link" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="selectPublicFileArrays" resultType="com.get.partnercenter.vo.FileArray">
        SELECT
            mFilePartner.id,
            mFilePartner.file_guid AS fileGuid,
            CONCAT(#{mMageAddress}, mFilePartner.file_key) AS fileKeyFile,
            mFilePartner.file_name_orc
        from app_partner_center.s_media_and_attached sAttached
                 INNER JOIN app_file_center.m_file_partner mFilePartner ON mFilePartner.file_guid = sAttached.fk_file_guid
        WHERE sAttached.fk_table_name=#{fkTableName}
          AND  sAttached.type_key=#{typeKey}
          AND  sAttached.fk_table_id=#{fkTableId} ORDER BY sAttached.gmt_create
        LIMIT 100



    </select>
    <select id="getNextIndexKey" resultType="java.lang.Integer">
        select max(index_key)+1 from s_media_and_attached where fk_table_id=#{tableId} and fk_table_name=#{tableName}
    </select>

    <select id="selectSaleCenterFileArrays" resultType="com.get.partnercenter.vo.FileArray">
        select fs.id            as id,
               fs.file_guid     as fileGuid,
               fs.file_key      as fileKey,
               fs.file_name_orc as fileNameOrc,
               fs.file_key as fileKeyFile
        from ais_sale_center.s_media_and_attached a
                 inner join ais_file_center.m_file_sale fs on fs.file_guid = a.fk_file_guid
        where a.fk_table_name = #{tableName}
          and a.fk_table_id = #{tableId}
          and a.type_key = #{typeKey}
    </select>


</mapper>
