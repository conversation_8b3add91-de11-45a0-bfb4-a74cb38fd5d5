package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.dto.AppAgentContactPersonAddDto;
import com.get.salecenter.dto.AppAgentContactPersonUpdateDto;
import com.get.salecenter.service.IAppAgentContactPersonService;
import com.get.salecenter.vo.AppAgentContactPersonVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @author: Hardy
 * @create: 2022/11/22 16:07
 * @verison: 1.0
 * @description:
 */
@Api(tags = "代理申请联系人管理")
@RestController
@RequestMapping("sale/appAgentContactPerson")
public class AppAgentContactPersonController {


    @Resource
    private IAppAgentContactPersonService appAgentContactPersonService;

    @ApiOperation(value = "根据代理申请id获取数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理申请联系人管理/根据代理申请id获取数据")
    @GetMapping("getByAppAgentId/{id}")
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    public ListResponseBo<AppAgentContactPersonVo> getByAppAgentId(@PathVariable Long id) {
        return new ListResponseBo<AppAgentContactPersonVo>(appAgentContactPersonService.getByAppAgentId(id));
    }


    /**
     * 新增代理申请联系人
     *
     * @param appAgentContactPersonAddDto
     * @return
     * @
     */
    @ApiOperation(value = "新增代理申请联系人", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理申请联系人管理/新增代理申请联系人")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated({AppAgentContactPersonAddDto.Add.class, AppAgentContactPersonAddDto.Save.class}) AppAgentContactPersonAddDto appAgentContactPersonAddDto) {
        return SaveResponseBo.ok(appAgentContactPersonService.addAppAgentContactPerson(appAgentContactPersonAddDto));
    }

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/代理申请联系人管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<AppAgentContactPersonVo> detail(@PathVariable("id") Long id) {
        return new ResponseBo<>(appAgentContactPersonService.finAppAgentContactPersonById(id));
    }


    /**
     * 修改信息
     *
     * @param appAgentContactPersonUpdateDto
     * @return
     * @
     */
    @ApiOperation(value = "修改代理联系人", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/联系人管理/修改代理联系人")
    @PostMapping("update")
    public ResponseBo<AppAgentContactPersonVo> update(@RequestBody @Validated({AppAgentContactPersonUpdateDto.Update.class,AppAgentContactPersonUpdateDto.Save.class}) AppAgentContactPersonUpdateDto appAgentContactPersonUpdateDto) {
        return UpdateResponseBo.ok(appAgentContactPersonService.updateAppAgentContactPerson(appAgentContactPersonUpdateDto));
    }
}
