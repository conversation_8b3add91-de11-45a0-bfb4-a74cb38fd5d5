package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.FocExportVo;
import com.get.common.result.InstitutionStatisticsResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.StaffInfo;
import com.get.institutioncenter.dto.CaseStudyResultsDto;
import com.get.institutioncenter.dto.InstitutionApplicationMetricsDto;
import com.get.institutioncenter.dto.query.InstitutionApplicationStaticsQueryDto;
import com.get.institutioncenter.vo.InstitutionApplicationMetricsVo;
import com.get.permissioncenter.vo.StaffVo;
import com.get.reportcenter.entity.ReportSale;
import com.get.salecenter.vo.*;
import com.get.salecenter.vo.AgentApplicationRankingVo;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.entity.EnrolFailureReason;
import com.get.salecenter.entity.KpiPlan;
import com.get.salecenter.entity.PayablePlan;
import com.get.salecenter.entity.ReceivablePlan;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.query.AgentApplicationRankingQueryDto;
import com.get.salecenter.dto.query.StudentOfferItemListQueryDto;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/11/18
 * @TIME: 12:37
 * @Description:
 **/
public interface IStudentOfferItemService extends IService<StudentOfferItem> {

    /**
     * @return java.util.List<com.get.salecenter.vo.StudentOfferItemVo>
     * @Description: 列表数据
     * @Param [studentOfferVo, page]
     * <AUTHOR>
     */
    List<StudentOfferItemVo> getStudentOfferItem(StudentOfferItemDto itemVo, Page page);

    /**
     * 根据申请方案id获取学习计划ids,作废方案调用
     *
     * @param fkStudentOfferId
     * @return
     * @
     */
    Set<Long> getStudentOfferItemByFkStudentOfferId(Long fkStudentOfferId);


    /**
     * 获取子计划及后续课程 课程和开学部分信息
     *
     * @param offerItemId
     * @return
     */
    List<StudentOfferItemVo> getChildOfferItemPartInfo(Long offerItemId);

    /**
     * @return java.lang.Long
     * @Description: 新增
     * @Param [offerVo]
     * <AUTHOR>
     */
    Long addOfferItem(StudentOfferItemDto offerItemVo);

    /**
     * 发送接受offer截至时间邮件/支付截至截至时间邮件
     *
     * @param studentOfferItem
     */
    void sendEmailOfferItemAdd(StudentOfferItem studentOfferItem);

    /**
     * @return java.lang.Long
     * @Description : 复制学习计划
     * @Param [id]
     * <AUTHOR>
     */
    Long copyOfferItem(Long id);


    /**
     * @return com.get.salecenter.vo.StudentOfferVo
     * @Description: 修改
     * @Param [offerVo]
     * <AUTHOR>
     */
    StudentOfferItemVo updateOfferItem(EventOfferPlanDto offerItemVo);


    StudentOfferItemVo updateOpenTime(StudentOfferItemDto studentOfferItemDto);

    void sendEmailNotice(EventOfferPlanDto offerPlanVo, StudentOfferItemVo offerItemDto, boolean isDefer);

    /**
     * 根据课程ID获取课程等级、类型
     *
     * @param fkInstitutionCourseId
     * @return
     */
    Map<String, String> getCourseMajorLevelAndTypeById(Long fkInstitutionCourseId);


    /**
     * @Description: 创建应收应付编辑接口
     * @Author: Jerry
     * @Date:9:47 2021/11/19
     */
    StudentOfferItem receivablePlanUpdate(StudentOfferItemReceivablePlanDto studentOfferItemReceivablePlanDto);

    /**
     * @return com.get.salecenter.vo.StudentOfferVo
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    StudentOfferItemVo findOfferItemById(Long id);

    /**
     * 根据应付类型对应记录ids获取对象map
     *
     * @param ids
     * @return
     * @
     */
    Map<Long, StudentOfferItemVo> findOfferItemByIds(Set<Long> ids);

    /**
     * 批量作废应收计划
     *
     * @param ids
     * @return
     */
    List<StudentOfferItemVo> getOfferItemByIds(Set<Long> ids);

    /**
     * 根据应付类型对应记录ids获取对象map
     *
     * @param ids
     * @return
     * @
     */
    Map<Long, StudentOfferItemVo> findOfferItemByIdsNew(Set<Long> ids);

    /**
     * @return com.get.salecenter.vo.StudentOfferVo
     * @Description: 根据目标对象获取学生信息
     * @Param [id]
     * <AUTHOR>
     */
    StudentOfferItemVo findStudentItemById(Long id, String type);

    /**
     * Author Cream
     * Description : // 获取历史佣金费率
     * Date 2022/8/11 15:00
     * Params:
     * Return
     */
    List<BaseSelectEntity> getHistoryCommissionRatio(Long fkInstitutionId, Long fkInstitutionCourseId, Boolean flag);

    /**
     * @return void
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);


    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据offerIds查询项目ids
     * @Param [itemIds]
     * <AUTHOR>
     */
    List<Long> getItemIdByStudentId(Long studentIds);


    /**
     * @return java.lang.Long
     * @Description: 增加或者更新评论
     * @Param [commentDto]
     * <AUTHOR>
     **/
    Long editComment(CommentDto commentDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.CommentVo>
     * @Description: 获取所有评论
     * @Param [commentDto, page]
     * <AUTHOR>
     */
    List<CommentVo> getComments(CommentDto commentDto, Page page);

    /**
     * @return int
     * @Description:查询学习计划附件数量
     * @Param [id]
     * <AUTHOR>
     */
    int getItemMediaCount(Long fkStudentOfferItemId, Long fkStudentOfferItemStepId);

    /**
     * @return java.util.List<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 附件列表
     * @Param [data, page]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> getItemMedia(MediaAndAttachedDto data, Page page);

    /**
     * @return java.util.List<com.get.salecenter.vo.MediaAndAttachedDto>
     */
    List<MediaAndAttachedVo> getItemMedias(Set<Long> ids);

    /**
     * @return java.lang.Long
     * @Description: 新增附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * @return void
     * @Description: 关闭
     * @Param [offerId]
     * <AUTHOR>
     */
    void unableOfferItem(Long offerId, Long status);

    /**
     * @return void
     * @Description: 关闭
     * @Param [offerId]
     * <AUTHOR>
     */
    void unableOfferItemByOfferId(Long offerId, Long status);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 下拉(学生id)
     * @Param [studentId]
     * <AUTHOR>
     */
    List<BaseSelectEntity> getStudentOfferItemSelect(Long companyIds, String fkTypeKey, Long studentId, String keyWord);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 下拉(代理id)
     * @Param [tableName, agentId]
     * <AUTHOR>
     */
    List<BaseSelectEntity> getOfferItemSelectByAgentId(String tableName, Long agentId);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 下拉(提供商id)
     * @Param [tableName, providerId]
     * <AUTHOR>
     */
    List<BaseSelectEntity> getOfferItemSelectByProviderId(String tableName, Long providerId);

    /**
     * @return java.lang.String
     * @Description: 根据项目id查询学生名称
     * @Param [itemId]
     * <AUTHOR>
     */
    String getStudentNameByItemId(Long itemId);


    Map<Long, String> getStudentNameByItemIds(Set<Long> itemIds);

    /**
     * @return java.lang.String
     * @Description: 根据项目id查询学生名称
     * @Param [itemId]
     * <AUTHOR>
     */
    String getAgentNameByItemId(Long itemId);

    Map<Long, String> getAgentNameByItemIds(Set<Long> itemIds);

    /**
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Description: 应收类型下拉框数据
     * @Param []
     * <AUTHOR>
     */
    List<Map<String, Object>> findReceivableTargetTypeSelect();

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据编号模糊查询
     * @Param [num]
     * <AUTHOR>
     */
    List<Long> getItemIdByNum(String num);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description:入学失败原因下拉数据
     * @Param []
     * <AUTHOR>
     */
    List<EnrolFailureReason> getEnrolFailureReason();

    /**
     * @return void
     * @Description:设置延迟入学
     * @Param [studentOfferItemVo]
     * <AUTHOR>
     */
//    StudentOfferItemVo updateDeferEntrance(StudentOfferItemDto studentOfferItemVo);

    /**
     * 学习计划学校tab
     *
     * @Date 16:21 2021/7/7
     * <AUTHOR>
     */
    List<OfferItemInstitutionTabVo> getOfferItemInstitutionTab(Long offerId);

    /**
     * @Description: 根据申请方案ids获取学习计划学校tab
     * @Author: Jerry
     * @Date:14:06 2021/8/17
     */
    Map<Long, List<InstitutionTabVo>> getOfferItemInstitutionTab(Set<Long> offerIds);

    /**
     * 学习计划、合同公式匹配排查
     *
     * @Date 15:54 2021/7/16
     * <AUTHOR>
     */
    String checkItemConditionByItemNum(String num, Long contractFormulaId);

    /**
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Description: 下拉
     * @Param []
     * <AUTHOR>
     */
    List<Map<String, Object>> findLearningModel();

    List<FocExportVo> getStudyPlanOptions(Long fkCompanyId, List<Long> fkCompanyIds);


    /**
     * 更新学校提供商
     *
     * @param offerItemId
     * @param institutionProviderId
     * @param institutionId
     * @param fkInstitutionChannelId
     * @return
     */
    SaveResponseBo modifyOfferItemInstitutionProvider(Long offerItemId, Long institutionProviderId, Long institutionId, Long fkInstitutionChannelId);

    /**
     * 激活学习计划
     *
     * @Date 12:37 2021/8/13
     * <AUTHOR>
     */
    void activationOfferItem(Long id);

    /**
     * @return java.util.List<com.get.salecenter.vo.StudentOfferItemVo>
     * @Description: 学习计划汇总表
     * @Param [studentOfferVo, page]
     * <AUTHOR>
     */
    List<StudentOfferItemListVo> getStudentOfferItemList(StudentOfferItemCollectDto data, String[] times);


    ResponseBo doGetStudentOfferPaginationInfo(StudentOfferItemCollectDto data, Page page);

    /**
     * @return java.util.List<com.get.salecenter.vo.StudentOfferItemVo>
     * @Description: 客户列表
     * @Param [studentOfferVo, page]
     * <AUTHOR>
     */
    List<StudentOfferItemListVo> getCustomersList(StudentOfferItemListQueryDto data, String[] times);

    List<StudentOfferItemListVo> AsyncGetStudentOfferItems(StudentOfferItemCollectDto offerItemVo, Integer index,
                                                           Integer count, Map<String, String> headerMap, List<Long> staffFollowerIds, String local,
                                                           Long staffId, String sort, List<Long> secureCountryIds, List<Long> secureInstitutionIds, List<Long> securePermissionGroupInstitutionIds, List<Long> secureStaffBoundBdIds);

    List<StudentOfferItemSummaryExportVo> setAttribute(Map<Long, String> countryChnNameByIds, Map<Integer, String> educationProjectMap,
                                                       List<StudentOfferItemSummaryExportVo> studentOfferItemSummaryExportVos,
                                                       List<StudentOfferItemListVo> studentOfferItemList,
                                                       Map<Long, String> staffNamesByIds, Map<Long, List<Long>> staffSuperiorMap,
                                                       Map<Long, List<StudentProjectRoleStaffVo>> rcMap,
                                                       Map<Long, List<StudentProjectRoleStaffVo>> arcMap, Map<Long, List<StudentProjectRoleStaffVo>> liMap,
                                                       Map<Long, List<StudentProjectRoleStaffVo>> adMap, Map<Long, List<StudentProjectRoleStaffVo>> cmMap,
                                                       Map<Long, List<StudentProjectRoleStaffVo>> iaeLiMap, Map<Long, List<StudentProjectRoleStaffVo>> iaeSupMap,
                                                       Map<Long, List<StudentProjectRoleStaffVo>> iaeAdMap, Map<Long, List<StudentProjectRoleStaffVo>> indexCmMap,
                                                       Map<Long, List<StudentProjectRoleStaffVo>> indexArcMap, Map<Long, List<StudentProjectRoleStaffVo>> seaArcMap,
                                                       Map<Long, List<StudentProjectRoleStaffVo>> seaCmMap, Map<Long, String> resonMap,
                                                       //List<Long> prdIds, List<Long> sddIds, List<Long> sdmIds,
                                                       Map<Long, String> allInstitutionTypeName, Map<Long, List<StudentOfferItemListVo>> studentMap,
                                                       Map<Long, Integer> orderMap, Map<Long, Agent> agentsByIds, String local, Map<Long, String> stateNamesByIds,
                                                       Map<Long, String> cityNamesByIds, Map<Long, String> institutionNamesByIds,
                                                       Map<String, StaffVo> staffDtoMapByLoginIds, Map<Long, String> followItemCourseNameMap);

    ResponseBo doGetCustomersListPaginationInfo(StudentOfferItemListQueryDto studentOfferItemListVo, Page page);

    /**
     * @return java.util.List<com.get.salecenter.vo.StudentOfferItemVo>
     * @Description: 成功客户列表
     * @Param [studentOfferVo, page]
     * <AUTHOR>
     */
    List<StudentOfferItemListVo> getSuccessfulCustomersListForMarket(StudentOfferItemListQueryDto data, String[] times, StaffInfo staffInfo);


    ResponseBo doGetSuccessfulCustomersListForMarketPaginationInfo(StudentOfferItemListQueryDto studentOfferItemListVo, Page page);

    /**
     * 成功客户列表导出
     *
     * @Date 15:21 2022/3/2
     * <AUTHOR>
     */
    void ExportedCustomerList(StudentOfferItemDto offerItemVo, HttpServletResponse httpServletResponse, String fileName);

    /**
     * 主动推送的数据
     *
     * @Date 16:21 2021/11/17
     * <AUTHOR>
     */
    StudentCountVo getStudentCountRecord(Long itemId);

    /**
     * 确认创建应收应付
     *
     * @Date 10:41 2021/11/19
     * <AUTHOR>
     */
    ResponseBo generatePlan(StudentOfferItemReceivablePlanDto studentOfferItemReceivablePlanDto);

    /**
     * 更新应收应付
     *
     * @Date 15:02 2021/12/2
     * <AUTHOR>
     */
    ResponseBo updateGeneratePlan(StudentOfferItemReceivablePlanDto studentOfferItemReceivablePlanDto);


    /**
     * 导出学习计划汇总列表Excel
     *
     * @return
     * @
     */
    void exportStudentOfferCollectExcel(HttpServletResponse response, StudentOfferItemCollectDto studentOfferItemVo);

    /**
     * 设置支付押金截止时间、接受Offer截止时间
     *
     * @param studentOfferItem
     */
    void setOfferItemDate(StudentOfferItem studentOfferItem);


    /**
     * 获取提供商ids
     *
     * @param itemIds
     * @return
     */
    Map<Long, Long> getProviderIdsByItemIds(List<Long> itemIds);

    /**
     * 公司Ids
     *
     * @param itemIds
     * @return
     */
    Map<Long, Long> getCompanyIdsByItemIds(List<Long> itemIds);

    /**
     * 应付类型下拉框数据
     *
     * @Date 10:33 2022/1/22
     * <AUTHOR>
     */
    List<Map<String, Object>> findPayableTargetTypeSelect();

    /**
     * 获取匹配的合同公式
     *
     * @return
     * @Date 10:22 2022/1/24
     * <AUTHOR>
     */
    List<ContractFormulaMatchingVo> matchingContractFormula(Long studentOfferItemId);

    /**
     * 生成应收应付
     *
     * @Date 17:24 2022/1/24
     * <AUTHOR>
     */
    void generateMatchingPlan(GenerateMatchingPlanListDto generateMatchingPlanListVo);

    /**
     * 验证课程删除逻辑验证
     *
     * @Date 18:04 2021/5/21
     * <AUTHOR>
     */
    boolean deleteValidateCourse(Long courseId);

    /**
     * @Description:创建应收应付状态
     * <AUTHOR>
     */
    List<Map<String, Object>> getAPARStatus();


    Map<Long, ReceivablePlanVo> findOfferItemByReceivableIds(Set<Long> ids);

    Map<Long, PayablePlanVo> findOfferItemByPayIds(Set<Long> ids);


    void enrolFailure(Long itemId, Long reasonId, String reason);

    /**
     * 更新Issue order Id
     *
     * @Date 11:18 2022/3/12
     * <AUTHOR>
     */
    void updateIssueRpaOrderId(IssueRpaDto issueRpaDto);

    /**
     * 批量生成应收应付计划
     *
     * @param batchGenerateMatchingPlanDto
     */
    void batchGenerateMatchingPlan(BatchGenerateMatchingPlanDto batchGenerateMatchingPlanDto);


    /**
     * @param studentOfferItemVo
     */
    void exportStudentOfferItemExcel(StudentOfferItemCollectDto studentOfferItemVo, List<FocExportVo> focExportVos);


    void updateStudentOfferItem(StudentOfferItemDto studentOfferItemDto);

    /**
     * 获取上一次申请统计报表记录
     */
    ResponseBo<ReportSale> lastStudentApplicationStatistics();

    /**
     * 申请统计
     */
    ResponseBo<ReportSale> studentApplicationStatistics(StudentApplicationStatisticsDto studentApplicationStatisticsDto);

    /**
     * 导出申请学生周、月、季度、年度统计EXCEL
     */
    void exportStudentApplicationStatisticsExcel(StudentApplicationStatisticsDto studentApplicationStatisticsDto, HttpServletResponse response);

    /**
     * 业绩统计
     *
     * @return
     */
    List<StatisticsVo> performanceStatistics(StatisticsDto statisticsDto);

    /**
     * 代理省份送生统计表
     *
     * @return
     */
    List<StudentApplicationStatisticsVo> getPushStudentStatistics(PushStudentStatisticsDto pushStudentStatisticsDto);

    /**
     * 导出代理省份送生表
     */
    void exportPushStudentStatistics(PushStudentStatisticsDto pushStudentStatisticsDto, HttpServletResponse response);

    /**
     * 代理申请排行
     *
     * @param applicationRankingQueryVo
     * @return
     */
    List<AgentApplicationRankingVo> getAgentApplicationRanking(AgentApplicationRankingQueryDto applicationRankingQueryVo, Page page);

    /**
     * 导出代理申请排行
     *
     * @param applicationRankingQueryVo
     */
    void exportAgentApplicationRanking(AgentApplicationRankingQueryDto applicationRankingQueryVo, HttpServletResponse response);

    /**
     * @Description:VIP统计
     * <AUTHOR>
     * @Date 15:13 2022/11/11
     **/
    List<VipStatisticsVo> getVipStatistics(VipStatisticsDto vipStatisticsDto, Page page);

    /**
     * @return com.get.salecenter.vo.AgentPerformanceStatisticsVo
     * <AUTHOR>
     * @Description 代理业绩统计
     * @Param [agentPerformanceStatisticsDto]
     * @Date 2022/11/21 10:30
     */
    AgentPerformanceStatisticsVo agentPerformanceStatistics(AgentPerformanceStatisticsDto agentPerformanceStatisticsDto);

    /**
     * 学校申请统计
     *
     * <AUTHOR>
     * @DateTime 2022/12/7 17:24
     */
    InstitutionStatisticsResponseBo institutionApplicationStatistics(InstitutionApplicationStaticsQueryDto institutionApplicationStaticsQueryDto, Page page);


    /**
     * 学校申请指标统计
     * @param metricsDto
     * @return
     */
    List<InstitutionApplicationMetricsVo> institutionApplicationMetrics(InstitutionApplicationMetricsDto metricsDto,Page page);






    /**
     * 导出学校申请统计
     *
     * <AUTHOR>
     * @DateTime 2022/12/12 12:24
     */
    void exportInstitutionApplicationStatistics(InstitutionApplicationStaticsQueryDto institutionApplicationStaticsQueryDto, HttpServletResponse response);


    /**
     * 学校入学年度转化率统计
     *
     * <AUTHOR>
     * @DateTime 2024/1/17 10:28
     */
    IEConversionRateResultVo<InstitutionEnrolledConversionRateVo> getInstitutionEnrolledConversionRate(InstitutionEnrolledConversionRateDto rateVo, Page page);


    /**
     * 学校入学年度转化率年度下拉
     *
     * <AUTHOR>
     * @DateTime 2024/3/15 11:24
     */
    List<Integer> getYearSelect();

    /**
     * 学校入学年度转化率学校下拉
     *
     * <AUTHOR>
     * @DateTime 2024/3/15 12:35
     */
    List<BaseSelectEntity> getInstitutionSelect(InstitutionSelectDto institutionSelectDto);

    /**
     * 导出学校入学年度转化率统计
     *
     * <AUTHOR>
     * @DateTime 2024/1/23 9:45
     */
    void exportInstitutionEnrolledConversionRate(InstitutionEnrolledConversionRateDto rateVo, HttpServletResponse response);

    /**
     * 获取KPI方案申请数统计相关的申请计划列表
     *
     * <AUTHOR>
     * @DateTime 2024/4/19 15:42
     */
    List<KpiStudentOfferItemVo> getKpiPlanApplicationStudentOfferItemList(KpiPlanStudentOfferItemListDto vo);

    /**
     * 获取KPI方案成功入学数统计相关的申请计划列表
     *
     * <AUTHOR>
     * @DateTime 2024/4/19 16:14
     */
    List<KpiStudentOfferItemVo> getKpiPlanSuccessStudentOfferItemList(KpiPlanStudentOfferItemListDto vo);

    /**
     * 获取KPI方案定校数统计相关的申请计划列表
     *
     * <AUTHOR>
     * @DateTime 2024/4/19 16:21
     */
    List<KpiStudentOfferItemVo> getKpiPlanConfirmationStudentOfferItemList(KpiPlanStudentOfferItemListDto vo);

    /**
     * 在指定学习计划列表过滤项目角色对应学习计划
     *
     * <AUTHOR>
     * @DateTime 2024/5/7 17:39
     */
    List<Long> filterProjectRoleStudentOfferItemList(KpiPlan kpiPlan, List<Long> staffFollowerIds, String fkTableName);

    /**
     * 其他金额下拉
     *
     * @return
     */
    List<Map<String, Object>> getBonusTypeSelect();

    /**
     * 结算状态下拉框
     *
     * @return
     */
    List<Map<String, Object>> getSettlementTypeSelect();

    /**
     * 获取学习计划
     *
     * @param fkTableIds
     * @return
     */
    List<StudentOfferItemVo> getStudentOfferItemByStudentOfferItemStepId(List<Long> fkTableIds);

    /**
     * 生成负数应收应付（入学失败时调用）
     *
     * @param offerItemIds
     */
    void generateNegativeReceivablePlanAndPayablePlan(List<Long> offerItemIds);

    /**
     * 通过渠道搜索计划
     *
     * @param tableName
     * @param channelId
     * @return
     */
    List<BaseSelectEntity> getOfferItemSelectByChannelId(String tableName, Long channelId, Long receiptFormId, Integer pageNumber, Integer pageSize);

    /**
     * 通过提供商Id搜索计划
     *
     * @param tableName
     * @param providerId
     * @return
     */
    List<BaseSelectEntity> getOfferItemSelectByProviderIdNew(String tableName, Long providerId, Long receiptFormId, Integer pageNumber, Integer pageSize);

    Map<Long, Object> getDeferEntranceTimeByIds(Set<Long> ids);

    /**
     * 验证学习计划是否能入学失败
     */
    void validateInvalid(Long itemId, String entryKey, boolean isFinance);


    //验证【申请退押金】
    StepApplyRefundVo validateInvalidApplyRefund(StudentOfferItem studentOfferItem, boolean isFinance);

    List<Map<String, Object>> doGetNewAppStatusSelection();


    Map<String, Set<Long>> getInChannelAndProviderIds(Set<Long> targetIds);

    StudentOfferItem getStudentOfferItemById(Long id);

    List<Long> getStudentOfferItemByPayIds(Set<Long> targetIds);

    List<SelItem> getStudentOfferItemByIds(Set<Long> targetIds);

    ResponseBo updateInstitutionProviderId(BatchUpdateReceivableDto updateReceivableVo);

    Boolean doResetReceivableAndPayablePlan(List<ReceivablePlan> receivablePlans, List<PayablePlan> payablePlans, String entryKey);

    void updateNoCommission(Long id, String remark);

    /**
     * 导出申请结算汇总
     *
     * @param studentOfferItemListVo
     */
    void exportedCustomerListForMarket(StudentOfferItemListQueryDto studentOfferItemListVo);

    void updateCommission(Long id, Boolean activePayablePlan);

    /**
     * 查看院校推荐
     *
     * @param queryVo
     * @return
     */
    ListResponseBo<RecommendedViewingResultDto> recommendedViewing(RecommendedViewingQueryDto queryVo);

    ListResponseBo<RecommendedViewingResultDto> getRecommendedViewingDetails(CaseStudyDetailsQueryDto queryVo);

    /**
     * 案例分析统计
     *
     * @param queryVo
     * @return
     */
    ResponseBo<CaseStudyResultsDto> caseStudyStatistics(RecommendedViewingQueryDto queryVo);

    void updateBatchStudentOfferItem(StudentOfferItemBatchProcessingDto studentOfferItemBatchProcessingDto);

    /**
     * 根据代理id获取佣金所需附件
     *
     * @Date 14:29 2022/11/29
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> getAgentCommissionMedias(Set<Long> ids);


    /**
     * 获取最新的三条学费
     *
     * @param fkCompanyId
     * @param institutionId
     * @param courseId
     * @param oldCourseName
     * @return
     */
    List<String> getApplicationPlanTheLatestThreeTuitionFees(Long fkCompanyId, Long institutionId, Long courseId, String oldCourseName);

    /**
     * 获得受限制的学习方案
     *
     * @param offerIds
     * @param itemStepIds
     * @return
     */
    Set<Long> getAssignProjectMembersLimit(Set<Long> offerIds, Set<Long> itemStepIds);

    Boolean checkUpdateNoCommission(Long id);

    /**
     * 佣金应付类型下拉框数据
     *
     * @Date 12:03 2023/2/13
     * <AUTHOR>
     */
    List<Map<String, Object>> findCommissionPayableTargetTypeSelect();

    /**
     * 设置延迟入学时间
     *
     * @Date 11:12 2023/3/3
     * <AUTHOR>
     */
    void updateDeferEntranceTime(DeferEntranceTimeUpdateDto deferEntranceTimeUpdateDto);

    /**
     * 匹配入学时间并返回
     *
     * @param studentOfferItem     必须
     * @param openTime             必须
     * @param isCountDeferEntrance 是否统计入学延迟标记 true or false
     * @return
     */
    Date compareAndReturnNewerOpeningTime(StudentOfferItem studentOfferItem, Date openTime, Boolean isCountDeferEntrance);

    Boolean getOsPermissionRestrictions(Long id);

    List<Map<String, Object>> getItemNewAppStatusSelection();

    /**
     * 合并申请计划数据
     *
     * @param mergedStudentId
     * @param targetStudentId
     */
    void mergeData(Long mergedStudentId, Long targetStudentId);

    /**
     * 通过申请计划ids获取学生
     *
     * @param itemIds
     * @return
     */
    List<StudentOfferItemVo> getStudentByOfferItemIds(List<Long> itemIds);

    RStudentOfferItemStepSaveVo setStepApplyRefund(Long itemId);

    Map<String, String> getContactPersonEmailMap(Long itemId);

    void dataImportItemEmail();

    StudentOfferItemSendEmailVo getContactPersonEmailStaff(Long itemId);

    void updateDepositTuitionTime(DepositTuitionTimeDto depositTuitionTimeDto);

    void updateIsStepFollow(Long id, Boolean isStepFollow);

    OfferItemLimitConfigVo getItemLimtit(Long fkStudentOfferItemId);

    EnrolFailureCheckVo enrolFailureCheck(EnrolFailureCheckDto enrolFailureCheckDto);

    /**
     * 根据申请计划汇总搜索结果创建任务
     *
     * @param studentOfferItemCollectCreateTaskDto
     */
    void createTask(StudentOfferItemCollectCreateTaskDto studentOfferItemCollectCreateTaskDto);

    Boolean getIssueApplyDataInfoResult(EventOfferPlanDto studentOfferItemVo);

    /**
     * 根据学生id获取所申请的国家列表
     *
     * @param studentId 学生Id
     * @return
     */
    List<BaseSelectEntity> getAreaCountrySelect(Long studentId);

    /**
     * 使用父申请计划凭证
     * @param offerItemVoucherVo
     */
    void useParentApplicationPlanVoucher(OfferItemVoucherDto offerItemVoucherVo);

    /**
     * 检测父申请计划凭证
     * @param parentItemId
     * @param fkStudentOfferItemStepId
     * @return
     */
    boolean checkParentApplicationPlanVoucher(Long parentItemId, Long fkStudentOfferItemStepId);

    /**
     * 发起费用代付
     *
     * @param offerItemPaymentFeeDto 费用代付参数
     */
    void initiatingFeePayment(OfferItemPaymentFeeDto offerItemPaymentFeeDto);

    void exportInstitutionApplicationMetrics(HttpServletResponse response,InstitutionApplicationMetricsDto metricsDto);

    /**
     * 判断不同申请方案不同代理是否重复申请计划
     * @param studentOfferItemDto
     * @return
     */
    ExistStudentOfferItemVo getIsExistStudentOfferItem(StudentOfferItemDto studentOfferItemDto);

    StudentOfferItemMismatchedListVo getStudentOfferItemMismatchedList(StudentOfferItemCollectDto studentOfferItemListVo, String[] times);
}
