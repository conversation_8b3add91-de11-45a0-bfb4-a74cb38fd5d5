package com.get.schoolGateCenter.dto;

import com.get.schoolGateCenter.entity.InstitutionZone;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2021/1/15
 * @TIME: 15:25
 * @Description:
 **/
@Data
@ApiModel("学校校区返回类")
public class InstitutionZoneDto extends InstitutionZone {
    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String institutionName;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;
    /**
     * 全称
     */
    @ApiModelProperty(value = "全称")
    private String fullName;
    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String areaCountryName;

    /**
     * 州省名称
     */
    @ApiModelProperty(value = "州省名称")
    private String areaStateName;

    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称")
    private String areaCityName;

    /**
     * 课程id
     */
    @ApiModelProperty(value = "课程id")
    private Long fkInstitutionCourseId;
}
