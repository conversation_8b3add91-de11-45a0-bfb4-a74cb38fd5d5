<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.StudentOfferMapper">

    <select id="getMaxId" resultType="java.lang.Long">
        select
            IFNULL(max(id)+1,0) id
        from
            m_student_offer
    </select>

    <select id="getStudentOfferSelectNew" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        select a.id,a.num as name
        from m_student_offer a

        INNER JOIN (
        <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.offerPermissionSql"/>
        ) z ON a.id=z.id
        where a.fk_student_id=#{studentId,jdbcType=BIGINT}
    </select>

    <select id="getStudentOfferSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        select id,num name
        from m_student_offer
        where fk_student_id=#{studentId,jdbcType=BIGINT} AND status = 1
    </select>

    <select id="getItemAndStepList" resultType="com.get.salecenter.vo.StudentItemAndStepVo">
        SELECT p.*,t.step_name stepName from
            (
                SELECT  k.id, max(k.fkInstitutionCourseId) fkInstitutionCourseId, max(u.step_order) step_order, k.fkInstitutionCourseCustomId
                from u_student_offer_item_step u
                         right join (
                        SELECT s.id,s.fk_institution_course_id fkInstitutionCourseId, s.fk_institution_course_custom_id AS fkInstitutionCourseCustomId,
                               r.fk_student_offer_item_id ,r.fk_student_offer_item_step_id
                        from m_student_offer_item  s
                                 LEFT join m_student_offer o on o.id=s.fk_student_offer_id
                                 LEFT join r_student_offer_item_step r on s.id=r.fk_student_offer_item_id
                        where s.fk_student_offer_id=#{offerId}
                    ) k
                                    on u.id=k.fk_student_offer_item_step_id
                GROUP BY k.id
            ) p
                left join u_student_offer_item_step t
                          on p.step_order=t.step_order
    </select>

    <select id="getAgentNameByOfferId" resultType="java.lang.String">
        SELECT CONCAT(a.`name`,IF(a.name_note is null or a.name_note = "","",CONCAT("（",a.name_note,"）"))) as agentName
        from m_student_offer s
                               left join m_agent a on s.fk_agent_id=a.id
        where s.id=#{offerId,jdbcType=BIGINT}
    </select>

    <select id="isExistByStudentId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id from m_student_offer where fk_student_id=#{studentId}
    </select>

    <select id="getStudentCountByOfferIds" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT fk_student_id) FROM m_student_offer
        where 1=1
        <if test="studentOfferIdList != null and studentOfferIdList.size()>0">
            AND id IN
            <foreach collection="studentOfferIdList" item="offerId" index="index" open="(" separator="," close=")">
                #{offerId}
            </foreach>
        </if>
    </select>
    <select id="getItemStepList" resultType="com.get.salecenter.vo.StudentOfferItemListVo">
        SELECT
        soi.id,
        GROUP_CONCAT(a.fk_student_offer_item_step_id,"-",step_order) as stepIdStr
        FROM
        m_student_offer_item AS soi
        LEFT JOIN
        r_student_offer_item_step AS a
        ON
        soi.id = a.fk_student_offer_item_id
        INNER JOIN
        u_student_offer_item_step AS b
        ON
        a.fk_student_offer_item_step_id = b.id
        <where>
            <if test="ids != null and ids.size()>0">
                and soi.id in
                <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        GROUP BY soi.id
    </select>
    <select id="getStudentOfferWorkFolw1" resultType="com.get.salecenter.vo.StudentOfferVo">
        SELECT
        so.*,
        GROUP_CONCAT(DISTINCT soi.id) studentOfferItemIdStr,
        GROUP_CONCAT(DISTINCT i.id) institutionIdStr,
        GROUP_CONCAT(DISTINCT CONCAT(i.`name`,IF(i.name_chn is null or i.name_chn = '','',CONCAT("（",i.name_chn,"）")))) institutionFullName,
        GROUP_CONCAT(DISTINCT ic.id) institutionCourseIdStr,
        GROUP_CONCAT(DISTINCT CONCAT(ic.`name`,IF(ic.name_chn is null or ic.name_chn = '','',CONCAT("（",ic.name_chn,"）")))) courseFullName,
        CONCAT(s.`name`,IF(s.first_name is null and s.last_name is null,'',CONCAT("（",s.first_name," ",s.last_name,"）"))) studentName
        FROM
        ais_sale_center.m_student_offer AS so
        LEFT JOIN
        ais_sale_center.m_student_offer_item AS soi
        ON
        so.id = soi.fk_student_offer_id
        INNER JOIN
        ais_institution_center.m_institution AS i
        ON
        soi.fk_institution_id = i.id
        INNER JOIN
        ais_institution_center.m_institution_course AS ic
        ON
        soi.fk_institution_course_id = ic.id
        LEFT JOIN
        ais_sale_center.m_student AS s
        ON
        so.fk_student_id = s.id
        where
        1=1
        <!--查询条件-代理-->
        <if test="studentOfferDto.fkAgentId != null and studentOfferDto.fkAgentId != ''">
            AND so.fk_agent_id = #{studentOfferDto.fkAgentId}
        </if>
        <!--查询条件-BD-->
        <if test="studentOfferDto.fkStaffId != null and studentOfferDto.fkStaffId != ''">
            AND so.fk_staff_id = #{studentOfferDto.fkStaffId}
        </if>
        <!--查询条件-编号-->
        <if test="studentOfferDto.num != null and studentOfferDto.num != ''">
            AND so.num like #{studentOfferDto.num}
        </if>
        <!--我的申请-->
        <if test="studentOfferDto.statusWorkflow != null and studentOfferDto.statusWorkflow != '' and  studentOfferDto.statusWorkflow==0">
            AND so.fk_staff_id_workflow = #{studentOfferDto.fkStaffIdWorkflow}
        </if>
        <!--我的审批-->
        <if test="studentOfferDto.ids != null and studentOfferDto.ids.size()>0">
            and so.id in
            <foreach collection="studentOfferDto.ids" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="studentOfferDto.statusWorkflow != null and studentOfferDto.statusWorkflow != '' and  studentOfferDto.statusWorkflow!=1">
            AND so.status_workflow != 0
        </if>
        <if test="studentOfferDto.fkStaffIdWorkflowIds != null and studentOfferDto.fkStaffIdWorkflowIds.size()>0">
            AND (
            so.fk_staff_id_workflow in
            <foreach collection="studentOfferDto.fkStaffIdWorkflowIds" item="id" index="index" open="(" separator=","
                     close=")">
                #{id}
            </foreach>
            <if test="studentOfferDto.statusWorkflow != null and studentOfferDto.statusWorkflow != '' and  studentOfferDto.statusWorkflow ==1">
                OR so.fk_student_id in
                <foreach collection="studentOfferDto.studentIds" item="id" index="index" open="(" separator=","
                         close=")">
                    #{id}
                </foreach>
            </if>
            )
        </if>
        <if test="studentOfferDto.studentName != null and studentOfferDto.studentName !=''">
            AND CONCAT(s.`name`,IF(s.first_name is null and s.last_name is null,'',CONCAT("（",s.first_name," ",s.last_name,"）"))) like concat("%",#{studentOfferDto.studentName},"%")
        </if>
        <if test="studentOfferDto.status != null and studentOfferDto.status !=''">
            AND so.status = #{studentOfferDto.status}
        </if>
        <!--状态（多选）-->
        <if test="studentOfferDto.statusList != null and studentOfferDto.statusList.size()>0">
            AND so.status in
            <foreach collection="studentOfferDto.statusList" item="status" index="index" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <!--公司-->
        <if test="studentOfferDto.fkCompanyId != null and studentOfferDto.fkCompanyId !=''">
            AND s.fk_company_id = #{studentOfferDto.fkCompanyId}
        </if>
        GROUP BY
        so.id
        <if test="studentOfferDto.fkInstitutionId != null and studentOfferDto.fkInstitutionId != ''">
            having 	FIND_IN_SET(#{studentOfferDto.fkInstitutionId},institutionIdStr)
            <if test="studentOfferDto.fkInstitutionCourseId !=null and studentOfferDto.fkInstitutionCourseId !=''">
                AND FIND_IN_SET(#{studentOfferDto.fkInstitutionCourseId},institutionCourseIdStr)
            </if>
        </if>
    </select>

    <select id="getStudentOfferWorkFolw" resultType="com.get.salecenter.vo.StudentOfferVo">
        SELECT
        so.id,
        so.fk_student_id,
        so.fk_contact_person_id,
        so.fk_agent_id,
        so.fk_staff_id,
        so.fk_area_country_id,
        so.num,
        so.remark,
        so.`status`,
        so.fk_staff_id_workflow,
        so.status_workflow,
        GROUP_CONCAT(DISTINCT soi.id) AS studentOfferItemIdStr,
        GROUP_CONCAT(DISTINCT soi.fk_institution_id) AS institutionIdStr,
        <!--GROUP_CONCAT(DISTINCT CONCAT(i.`name`,IF(i.name_chn is null or i.name_chn = '','',CONCAT("（",i.name_chn,"）")))) AS institutionFullName,-->
        GROUP_CONCAT(DISTINCT soi.fk_institution_course_id) AS institutionCourseIdStr,
        <!--GROUP_CONCAT(DISTINCT CONCAT(ic.`name`,IF(ic.name_chn is null or ic.name_chn = '','',CONCAT("（",ic.name_chn,"）")))) AS courseFullName,-->
        CONCAT(s.`name`,IF(s.first_name is null and s.last_name is null,'',CONCAT("（",s.first_name," ",s.last_name,"）"))) AS studentName,
        GROUP_CONCAT(DISTINCT soi.fk_institution_id,"-",soi.fk_institution_course_id) AS institutionAndCourseIdStr,
        GROUP_CONCAT(DISTINCT soi.fk_institution_id,"-",soi.fk_institution_course_custom_id) AS institutionAndCustomCourseIdStr
        FROM
        ais_sale_center.m_student_offer AS so
        LEFT JOIN
        ais_sale_center.m_student_offer_item AS soi
        ON
        soi.fk_student_offer_id = so.id and soi.`status` !=0
        LEFT JOIN
        ais_sale_center.m_student AS s
        ON
        so.fk_student_id = s.id
        <!--LEFT JOIN
        ais_institution_center.m_institution AS i
        ON
        soi.fk_institution_id = i.id
        LEFT JOIN
        ais_institution_center.m_institution_course AS ic
        ON
        soi.fk_institution_course_id = ic.id
        LEFT JOIN
        ais_institution_center.m_institution_course_custom AS icc
        ON
        soi.fk_institution_course_custom_id =icc.id-->
        where
        <!--只显示学习计划为打开的/或者无学习计划-->
        1=1
        <!--<and soi.`status` !=0>-->
        <!--查询条件-代理-->
        <if test="studentOfferDto.fkAgentId != null and studentOfferDto.fkAgentId != ''">
            AND so.fk_agent_id = #{studentOfferDto.fkAgentId}
        </if>
        <!--查询条件-BD-->
        <if test="studentOfferDto.fkStaffId != null and studentOfferDto.fkStaffId != ''">
            AND so.fk_staff_id = #{studentOfferDto.fkStaffId}
        </if>
        <!--查询条件-编号-->
        <if test="studentOfferDto.num != null and studentOfferDto.num != ''">
            AND so.num like #{studentOfferDto.num}
        </if>
        <!--我的申请-->
        <if test="studentOfferDto.statusWorkflow != null and studentOfferDto.statusWorkflow != '' and  studentOfferDto.statusWorkflow==0">
            AND so.fk_staff_id_workflow = #{studentOfferDto.fkStaffIdWorkflow}
        </if>
        <!--我的审批-->
        <!--        <if test="studentOfferDto.ids != null and studentOfferDto.ids.size()>0">-->
        <!--            and so.id in-->
        <!--            <foreach collection="studentOfferDto.ids" item="id" index="index" open="(" separator="," close=")">-->
        <!--                #{id}-->
        <!--            </foreach>-->
        <!--        </if>-->
        <if test="studentOfferDto.ids != null and studentOfferDto.ids != ''">
            AND so.id in ${studentOfferDto.ids}
        </if>
        <if test="studentOfferDto.statusWorkflow != null and studentOfferDto.statusWorkflow != '' and  studentOfferDto.statusWorkflow!=1">
            AND so.status_workflow != 0
        </if>
        <if test="studentOfferDto.fkStaffIdWorkflowIds != null and studentOfferDto.fkStaffIdWorkflowIds !='' and studentOfferDto.statusWorkflow !=1">
            AND (
            so.fk_staff_id_workflow in ${studentOfferDto.fkStaffIdWorkflowIds}
            <!--            <if test="studentOfferDto.statusWorkflow != null and studentOfferDto.statusWorkflow != '' and  studentOfferDto.statusWorkflow ==1">-->
            <!--                OR so.fk_student_id in ${studentOfferDto.allStudentIds}-->
            <!--            </if>-->
            )
        </if>
        <if test="studentOfferDto.studentIds != null and studentOfferDto.studentIds !=''">
            AND so.fk_student_id in ${studentOfferDto.studentIds}
        </if>
        <if test="studentOfferDto.studentName != null and studentOfferDto.studentName !=''">
            AND CONCAT(s.`name`,IF(s.first_name is null and s.last_name is null,'',CONCAT("（",s.first_name," ",s.last_name,"）"))) like concat("%",#{studentOfferDto.studentName},"%")
        </if>
        <if test="studentOfferDto.status != null and studentOfferDto.status !=''">
            AND so.status = #{studentOfferDto.status}
        </if>
        <!--状态（多选）-->
        <if test="studentOfferDto.statusList != null and studentOfferDto.statusList.size()>0">
            AND so.status in
            <foreach collection="studentOfferDto.statusList" item="status" index="index" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <!--公司-->
        <if test="studentOfferDto.fkCompanyId != null and studentOfferDto.fkCompanyId !=''">
            AND s.fk_company_id = #{studentOfferDto.fkCompanyId}
        </if>
        <!--学生国籍-->
        <if test="studentOfferDto.fkAreaCountryIdNationality != null and studentOfferDto.fkAreaCountryIdNationality != ''">
            AND s.fk_area_country_id_nationality = #{studentOfferDto.fkAreaCountryIdNationality}
        </if>
        <!--查询-国家线-->
        <if test="studentOfferDto.fkAreaCountryId != null and studentOfferDto.fkAreaCountryId !=''">
            AND so.fk_area_country_id = #{studentOfferDto.fkAreaCountryId}
        </if>
        <!--查询-方案创建时间-->
        <if test="studentOfferDto.createBeginDate!=null">
            AND so.gmt_create <![CDATA[>= ]]>#{studentOfferDto.createBeginDate}
        </if>
        <if test="studentOfferDto.createEndDate!=null">
            AND so.gmt_create <![CDATA[<= ]]>#{studentOfferDto.createEndDate}
        </if>
        <!--查询-开学时间-->
        <if test="studentOfferDto.openingTimeStart!=null">
            AND soi.defer_opening_time <![CDATA[>= ]]>#{studentOfferDto.openingTimeStart}
        </if>
        <if test="studentOfferDto.openingTimeEnd!=null">
            AND soi.defer_opening_time <![CDATA[<= ]]>#{studentOfferDto.openingTimeEnd}
        </if>
        GROUP BY
        so.id
        <if test="studentOfferDto.fkInstitutionId != null and studentOfferDto.fkInstitutionId != ''">
            having 	FIND_IN_SET(#{studentOfferDto.fkInstitutionId},institutionIdStr)
            <if test="studentOfferDto.fkInstitutionCourseId !=null and studentOfferDto.fkInstitutionCourseId !=''">
                AND FIND_IN_SET(#{studentOfferDto.fkInstitutionCourseId},institutionCourseIdStr)
            </if>
        </if>
        order by
        so.gmt_create
        desc
    </select>




    <select id="getItemAndSteps" resultType="com.get.salecenter.vo.StudentItemAndStepVo">
        SELECT
            u.step_name AS stepName,
            s.fk_institution_id AS fkInstitutionId,
            s.fk_institution_course_custom_id AS fkInstitutionCourseCustomId,
            s.fk_student_offer_item_step_id AS step_order,
            s.fk_institution_course_id AS fkInstitutionCourseId,
            s.id,
            s.old_course_custom_name AS oldCourseCustomName,
            s.opening_time AS openingTime,
            s.defer_opening_time AS deferOpeningTime,
            s.fk_parent_student_offer_item_id AS fkParentStudentOfferItemId,
            s.gmt_create,
            s.old_institution_name,
            s.old_institution_full_name,
            s.old_course_custom_name,
            s.duration_type,
            s.duration,
            s.is_follow,
            s.app_remark
        FROM
            m_student_offer_item AS s
                LEFT JOIN
                u_student_offer_item_step AS u
                ON s.fk_student_offer_item_step_id=u.id
        WHERE s.fk_student_offer_id = #{offerId}
          AND u.step_name IS NOT NULL
          AND s.status !=0
        ORDER BY s.gmt_create DESC
    </select>

    <select id="getAgentByOfferIds" resultType="com.get.salecenter.vo.AgentVo">
        SELECT s.id as offerId,
        a.`name`,
        a.name_note
        from m_student_offer s
        left join m_agent a on s.fk_agent_id=a.id
        <where>
            <if test="offerIds != null and offerIds !=''">
                AND s.id IN ${offerIds}
            </if>
        </where>
    </select>
    <select id="getAreaCountryByStudentId" resultType="java.lang.Long">
        select fk_area_country_id from m_student_offer where fk_student_id =#{studentId} and status !=0
    </select>
    <select id="getAreaCountrysByStudentIds" resultType="java.lang.Long">
        select fk_area_country_id from m_student_offer where status !=0
        AND fk_student_id in
        <foreach collection="studentIds" item="id" index="index" open="(" separator=","
                 close=")">
            #{id}
        </foreach>
    </select>
    <select id="getOfferIdByStepChangeDate" resultType="java.lang.Long">
        SELECT DISTINCT fk_student_offer_id FROM m_student_offer_item as msoi inner JOIN
        (SELECT
        sois01.fk_student_offer_item_id,
        sois01.maxDate,
        sois01.maxOrder,
        usois01.id
        FROM
        (
        SELECT
        rsois.fk_student_offer_item_id,
        max(rsois.gmt_create) AS maxDate,
        max(usois.step_order) AS maxOrder
        FROM
        r_student_offer_item_step AS rsois
        LEFT JOIN
        u_student_offer_item_step AS usois
        ON
        rsois.fk_student_offer_item_step_id = usois.id
        GROUP BY
        rsois.fk_student_offer_item_id
        ) AS sois01
        LEFT JOIN
        u_student_offer_item_step AS usois01
        ON
        sois01.maxOrder = usois01.step_order
        where 1=1
        <if test="studentOfferDto.stepChangeBeginDate!=null">
            AND maxDate<![CDATA[>= ]]>#{studentOfferDto.stepChangeBeginDate}
        </if>
        <if test="studentOfferDto.stepChangeEndDate!=null">
            AND maxDate<![CDATA[<= ]]>#{studentOfferDto.stepChangeEndDate}
        </if>
        GROUP BY
        sois01.fk_student_offer_item_id,
        usois01.id
        ORDER BY
        sois01.fk_student_offer_item_id ASC) a on msoi.id = a.fk_student_offer_item_id
    </select>
    <select id="getAreaCountryList" resultType="java.lang.Long">
        SELECT
        ac.id
        FROM
        ais_institution_center.u_area_country as ac
        INNER JOIN
        (SELECT
        DISTINCT mso.fk_area_country_id
        FROM
        ais_sale_center.m_student_offer AS mso
        LEFT JOIN
        ais_sale_center.m_student AS ms
        ON
        mso.fk_student_id = ms.id
        LEFT JOIN
        ais_institution_center.u_area_country AS uac
        ON
        mso.fk_area_country_id = uac.id
        WHERE 1=1
        <if test="companyId != null and companyId != ''">
            AND ms.fk_company_id = #{companyId}
        </if>
        ) as a on a.fk_area_country_id = ac.id
        ORDER BY
        ac.view_order desc
    </select>
    <select id="getVisibleCountryIdByStaffIds" resultType="java.lang.Long">
        SELECT
        distinct uac.id
        FROM
        ais_permission_center.r_staff_area_country AS rsac
        LEFT JOIN
        ais_institution_center.u_area_country AS uac
        ON
        rsac.fk_area_country_key = uac.num
        WHERE
        1=1
        <if test="staffIds != null and staffIds != ''">
            AND rsac.fk_staff_id in ${staffIds}
        </if>
    </select>
    <select id="getStudentIdById" resultType="java.lang.Long">
        select fk_student_id from m_student_offer where id =#{id}
    </select>

    <select id="getOfferByStudentId" resultType="com.get.salecenter.entity.StudentOffer">
        select * from m_student_offer where fk_student_id = #{id} and status!=0
    </select>

    <select id="getOfferByStudentIds" resultType="com.get.salecenter.entity.StudentOffer">
        select * from m_student_offer where status!=0 and  fk_student_id IN
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getStudentOfferListNew" resultType="com.get.salecenter.vo.StudentOfferVo">
        SELECT
        mso.*
        FROM
        m_student_offer mso
        WHERE
        EXISTS (SELECT DISTINCT ( id ) FROM view_student_offer_list AS view_list WHERE view_list.id = mso.id
        AND (
        <if test="staffFollowerIds != null and staffFollowerIds.size()>0">
            (view_list.sprs_fk_staff_id IS NULL OR view_list.sprs_fk_staff_id IN
            <foreach collection="staffFollowerIds" item="staffId" index="index" open="(" separator="," close=")">
                #{staffId}
            </foreach>
            ) OR
            (view_list.fk_staff_id IS NULL OR view_list.fk_staff_id IN
            <foreach collection="staffFollowerIds" item="staffId" index="index" open="(" separator="," close=")">
                #{staffId}
            </foreach>
            )
        </if>
        )
        <if test="studentOfferDto.fkAgentId != null and studentOfferDto.fkAgentId != ''">
            AND view_list.fk_agent_id = #{studentOfferDto.fkAgentId}
        </if>
        <!--查询条件-BD-->
        <if test="studentOfferDto.fkStaffId != null and studentOfferDto.fkStaffId != ''">
            AND view_list.fk_staff_id = #{studentOfferDto.fkStaffId}
        </if>
        <!--查询条件-编号-->
        <if test="studentOfferDto.num != null and studentOfferDto.num != ''">
            AND view_list.num like #{studentOfferDto.num}
        </if>
        <!--我的申请-->
        <if test="studentOfferDto.statusWorkflow != null and studentOfferDto.statusWorkflow != '' and  studentOfferDto.statusWorkflow==0">
            AND view_list.fk_staff_id_workflow = #{studentOfferDto.fkStaffIdWorkflow}
        </if>
        <if test="studentOfferDto.ids != null and studentOfferDto.ids != ''">
            AND view_list.id in ${studentOfferDto.ids}
        </if>
        <if test="studentOfferDto.statusWorkflow != null and studentOfferDto.statusWorkflow != '' and  studentOfferDto.statusWorkflow!=1">
            AND view_list.status_workflow != 0
        </if>
        <if test="studentOfferDto.fkStaffIdWorkflowIds != null and studentOfferDto.fkStaffIdWorkflowIds !='' and studentOfferDto.statusWorkflow !=1">
            AND (
            view_list.fk_staff_id_workflow in ${studentOfferDto.fkStaffIdWorkflowIds}
            <!--            <if test="studentOfferDto.statusWorkflow != null and studentOfferDto.statusWorkflow != '' and  studentOfferDto.statusWorkflow ==1">-->
            <!--                OR so.fk_student_id in ${studentOfferDto.allStudentIds}-->
            <!--            </if>-->
            )
        </if>
        <if test="studentOfferDto.studentIds != null and studentOfferDto.studentIds !=''">
            AND view_list.fk_student_id in ${studentOfferDto.studentIds}
        </if>
        <if test="studentOfferDto.studentName != null and studentOfferDto.studentName !=''">
            AND CONCAT(view_list.`name`,IF(view_list.first_name is null and view_list.last_name is null,'',CONCAT("（",view_list.first_name," ",view_list.last_name,"）"))) like concat("%",#{studentOfferDto.studentName},"%")
        </if>
        <if test="studentOfferDto.status != null and studentOfferDto.status !=''">
            AND view_list.status = #{studentOfferDto.status}
        </if>
        <!--状态（多选）-->
        <if test="studentOfferDto.statusList != null and studentOfferDto.statusList.size()>0">
            AND view_list.status in
            <foreach collection="studentOfferDto.statusList" item="status" index="index" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <!--公司-->
        <if test="studentOfferDto.fkCompanyId != null and studentOfferDto.fkCompanyId !=''">
            AND view_list.fk_company_id = #{studentOfferDto.fkCompanyId}
        </if>
        <!--学生国籍-->
        <if test="studentOfferDto.fkAreaCountryIdNationality != null and studentOfferDto.fkAreaCountryIdNationality != ''">
            AND view_list.fk_area_country_id_nationality = #{studentOfferDto.fkAreaCountryIdNationality}
        </if>
        <!--查询-国家线-->
        <if test="studentOfferDto.fkAreaCountryId != null and studentOfferDto.fkAreaCountryId !=''">
            AND view_list.fk_area_country_id = #{studentOfferDto.fkAreaCountryId}
        </if>
        <!--查询-方案创建时间-->
        <if test="studentOfferDto.createBeginDate!=null">
            AND view_list.gmt_create<![CDATA[>= ]]>#{studentOfferDto.createBeginDate}
        </if>
        <if test="studentOfferDto.createEndDate!=null">
            AND view_list.gmt_create<![CDATA[<= ]]>#{studentOfferDto.createEndDate}
        </if>
        <!--查询-开学时间-->
        <if test="studentOfferDto.openingTimeStart!=null">
            AND view_list.opening_time<![CDATA[>= ]]>#{studentOfferDto.openingTimeStart}
        </if>
        <if test="studentOfferDto.openingTimeEnd!=null">
            AND view_list.opening_time<![CDATA[<= ]]>#{studentOfferDto.openingTimeEnd}
        </if>

        )
    </select>
    <select id="getItemAndStepsByIds" resultType="com.get.salecenter.vo.StudentItemInfoVo">
        SELECT
        u.step_name AS stepName,
        IF (
        u.id = 9,
        CONCAT(
        e.reason_name,
        IF (s.other_failure_reason != '' AND s.other_failure_reason IS NOT NULL,CONCAT('(',s.other_failure_reason,')'),'')),''
        ) AS enrolFailureReason,
        s.fk_institution_id AS fkInstitutionId,
        s.fk_institution_course_custom_id AS fkInstitutionCourseCustomId,
        s.fk_student_offer_item_step_id AS step_order,
        s.fk_student_offer_item_step_id AS fkStudentOfferItemStepId,
        s.fk_institution_course_id AS fkInstitutionCourseId,
        s.id,
        s.old_course_custom_name AS oldCourseCustomName,
        s.opening_time AS openingTime,
        s.defer_opening_time AS deferOpeningTime,
        s.fk_parent_student_offer_item_id AS fkParentStudentOfferItemId,
        s.gmt_create,
        s.old_institution_name,
        s.old_institution_full_name,
        s.old_course_custom_name,
        s.duration_type,
        s.duration,
        s.is_follow,
        s.is_follow_hidden,
        s.is_add_app,
        s.app_remark,
        s.fk_student_offer_id,
        s.is_defer_entrance,
        s.is_follow_hidden,
        s.gmt_create,
        s.status,
        s.is_step_follow,
        s.fk_student_id,
        s.fk_area_country_id
        FROM
        m_student_offer_item AS s
        LEFT JOIN
        u_student_offer_item_step AS u
        ON s.fk_student_offer_item_step_id=u.id
        LEFT JOIN u_enrol_failure_reason e ON e.id = s.fk_enrol_failure_reason_id
        WHERE s.fk_student_offer_id in
        <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="!isStudentOfferItemFinancialHiding">
            AND IFNULL(s.is_follow_hidden, 0)!=1
        </if>
        AND u.step_name IS NOT NULL
        AND s.status = 1
        AND s.is_follow = 0
        ORDER BY s.gmt_create DESC
    </select>


    <select id="getStudentOfferListNew2" resultType="com.get.salecenter.vo.StudentOfferVo">
        SELECT
        <if test="studentOfferDto.pageNumber!=null">
            a.*,o.weights
        </if>
        <if test="studentOfferDto.pageNumber==null">
            <choose>
                <when test="studentOfferDto.isProjectUpate !=null and studentOfferDto.isProjectUpate">
                    a.id
                </when>
                <otherwise>
                    count(*) as total_count
                </otherwise>
            </choose>
        </if>

        FROM ais_sale_center.m_student_offer a

        INNER JOIN (
        SELECT DISTINCT a.id,a.weights
        FROM (

        SELECT a.id,LENGTH(CONCAT(b.first_name,b.last_name)) as weights
        <!-- #第一层#################### -->
        FROM ais_sale_center.m_student_offer a

        <if test="(studentOfferDto.agentName !=null and studentOfferDto.agentName !='')
         or (studentOfferDto.fkAreaCountryIdAgent !=null and studentOfferDto.fkAreaCountryIdAgent !='')
         or(studentOfferDto.fkAreaStateIdAgentList !=null and studentOfferDto.fkAreaStateIdAgentList.size()>0)
         or(studentOfferDto.fkAgentIds !=null and studentOfferDto.fkAgentIds.size()>0)">
            LEFT JOIN ais_sale_center.m_agent a1 ON a.fk_agent_id=a1.id
        </if>

        <if test="(studentOfferDto.bdName != null and studentOfferDto.bdName !='')
            or(studentOfferDto.fkBdIds !=null and studentOfferDto.fkBdIds.size()>0)
            or(studentOfferDto.fkAreaRegionId !=null)
">
            <!-- bdName过滤 -->
            LEFT JOIN ais_permission_center.m_staff a2 ON a.fk_staff_id=a2.id
            LEFT JOIN ais_sale_center.r_staff_bd_code a3 ON a2.id = a3.fk_staff_id
        </if>

        <!-- #项目成员的过滤条件 -->
        <if test="(studentOfferDto.fkProjectRoleIds !=null and studentOfferDto.fkProjectRoleIds.size()>0) or
        (studentOfferDto.fkRoleIds !=null and studentOfferDto.fkRoleIds.size()>0)">
            INNER JOIN (
            SELECT fk_table_id FROM ais_sale_center.s_student_project_role_staff
            WHERE fk_table_name='m_student_offer' AND is_active=1
            <if test="studentOfferDto.fkProjectRoleIds !=null and studentOfferDto.fkProjectRoleIds.size()>0">
                AND fk_staff_id IN
                <foreach collection="studentOfferDto.fkProjectRoleIds" item="fkProjectRoleId" index="index" open="(" separator=","  close=")">
                    #{fkProjectRoleId}
                </foreach>
            </if>
            <if test="studentOfferDto.fkRoleIds !=null and studentOfferDto.fkRoleIds.size()>0">
                AND fk_student_project_role_id IN
                <foreach collection="studentOfferDto.fkRoleIds" item="fkRoleId" index="index" open="(" separator=","  close=")">
                    #{fkRoleId}
                </foreach>
            </if>
            GROUP BY fk_table_id
            ) a4 ON a.id = a4.fk_table_id
        </if>


        <!-- #第二层#################### -->
        INNER JOIN ais_sale_center.m_student b ON a.fk_student_id=b.id

        <!-- #第三层#################### -->
        <if test="(studentOfferDto.stepOrderList != null and studentOfferDto.stepOrderList.size > 0)
        or (studentOfferDto.fkAreaCountryId != null and studentOfferDto.fkAreaCountryId !='')
        or (studentOfferDto.openingTimeStart!=null)
        or (studentOfferDto.openingTimeEnd !=null)
        or (studentOfferDto.itemCreateBeginDate !=null)
        or (studentOfferDto.itemCreateEndDate !=null)
        or (studentOfferDto.failureReasonId !=null and studentOfferDto.failureReasonId !='')
        or (studentOfferDto.fkEnrolFailureReasonIds !=null and studentOfferDto.fkEnrolFailureReasonIds.size()>0)
        or (studentOfferDto.schoolName !=null and studentOfferDto.schoolName !='')
        or (studentOfferDto.courseName !=null and studentOfferDto.courseName !='')
        or (studentOfferDto.majorLevelId !=null and studentOfferDto.majorLevelId !='')
        or (studentOfferDto.majorLevelIds !=null and studentOfferDto.majorLevelIds.size()>0)
        or (studentOfferDto.fkInstitutionProviderId!=null and studentOfferDto.fkInstitutionProviderId!='')
        or (studentOfferDto.newAppStatus !=null)
        or (studentOfferDto.stepChangeBeginDate!=null or studentOfferDto.stepChangeEndDate !=null) and (studentOfferDto.changeStepId !=null and studentOfferDto.changeStepId.size>0)
        or (studentOfferDto.groupName !=null and studentOfferDto.groupName !='')
        or (studentOfferDto.fkInstitutionGroupIds !=null and studentOfferDto.fkInstitutionGroupIds.size()>0)
        or (studentOfferDto.channelName !=null and studentOfferDto.channelName !='')
        or (studentOfferDto.channelNames !=null and studentOfferDto.channelNames.size()>0)
        or (studentOfferDto.isDeferEntrance !=null and studentOfferDto.isDeferEntrance !='')
        or (studentOfferDto.targetCountryIdList != null and studentOfferDto.targetCountryIdList.size()>0)
        or ((studentOfferDto.majorLevelIds !=null and studentOfferDto.majorLevelIds.size()>0) or (studentOfferDto.oldCourseMajorLevelNames !=null and studentOfferDto.oldCourseMajorLevelNames.size()>0))
        or (studentOfferDto.openingTimeStart!=null) or (studentOfferDto.courseIds!=null and studentOfferDto.courseIds.size()>0)
        or (studentOfferDto.openingTimeEnd!=null) or (studentOfferDto.courseTypeGroupIds !=null and studentOfferDto.courseTypeGroupIds.size()>0) or (studentOfferDto.oldCourseTypeGroupNames !=null and studentOfferDto.oldCourseTypeGroupNames.size()>0)
        ">
            INNER JOIN ais_sale_center.m_student_offer_item c ON a.id=c.fk_student_offer_id
        </if>


        <!-- <if test="studentOfferDto.openingTimeStart != null or studentOfferDto.openingTimeEnd != null">
            LEFT JOIN (
            SELECT
            b.fk_student_offer_item_id
            FROM
            (SELECT
            a.fk_student_offer_item_id,
            MAX( a.id ) as id
            FROM
            m_student_offer_item_defer_entrance_time a

            GROUP BY
            a.fk_student_offer_item_id) a
            INNER JOIN m_student_offer_item_defer_entrance_time b on a.id = b.id
            where 1=1
            <if test="studentOfferDto.openingTimeStart != null">
                AND  DATE_FORMAT(b.defer_entrance_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{studentOfferDto.openingTimeStart},'%Y-%m-%d')
            </if>
            <if test="studentOfferDto.openingTimeEnd != null">
                AND DATE_FORMAT(b.defer_entrance_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{studentOfferDto.openingTimeEnd},'%Y-%m-%d')
            </if>
            ) z1 on c.id = z1.fk_student_offer_item_id

        </if> -->


        <!-- #变更步骤过滤 -->
        <if test="(studentOfferDto.stepChangeBeginDate!=null or studentOfferDto.stepChangeEndDate !=null) and (studentOfferDto.changeStepId !=null and studentOfferDto.changeStepId.size>0)">
            INNER JOIN (
            SELECT fk_student_offer_item_id FROM ais_sale_center.r_student_offer_item_step WHERE fk_student_offer_item_step_id  in
            <foreach collection="studentOfferDto.changeStepId" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
            <!-- 过滤状态变更时间 -->
            <if test="studentOfferDto.stepChangeBeginDate!=null">
                AND DATE_FORMAT( gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{studentOfferDto.stepChangeBeginDate},'%Y-%m-%d' )
            </if>
            <if test="studentOfferDto.stepChangeEndDate!=null">
                AND DATE_FORMAT( gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{studentOfferDto.stepChangeEndDate},'%Y-%m-%d' )
            </if>
            GROUP BY fk_student_offer_item_id
            ) c1 ON c.id=c1.fk_student_offer_item_id
        </if>

        <!-- #集团过滤 groupName -->
        <if test="studentOfferDto.groupName !=null and studentOfferDto.groupName !=''">
            INNER JOIN (
            SELECT a.fk_institution_id
            FROM ais_institution_center.r_institution_provider_institution a
            LEFT JOIN ais_institution_center.m_institution_provider b ON a.fk_institution_provider_id=b.id
            LEFT JOIN ais_institution_center.m_institution_group c ON b.fk_institution_group_id=c.id
            WHERE LOWER(c.name) LIKE CONCAT("%",#{studentOfferDto.groupName},"%") OR LOWER(c.name_chn) LIKE CONCAT("%",#{studentOfferDto.groupName},"%")
            GROUP BY a.fk_institution_id
            ) c2 ON c.fk_institution_id=c2.fk_institution_id
        </if>

        <!-- #集团过滤 多选 -->
        <if test="studentOfferDto.fkInstitutionGroupIds !=null and studentOfferDto.fkInstitutionGroupIds.size()>0 ">
            INNER JOIN (
            SELECT i.id
            FROM ais_sale_center.m_student_offer_item i
            LEFT JOIN ais_institution_center.r_institution_provider_institution a on a.fk_institution_id = i.fk_institution_id
            LEFT JOIN ais_institution_center.m_institution_provider b ON a.fk_institution_provider_id=b.id
            <where>
                <if test="studentOfferDto.fkInstitutionGroupIds != null and studentOfferDto.fkInstitutionGroupIds.size()>0">
                    AND i.fk_institution_provider_id=-1 AND b.fk_institution_group_id IN
                    <foreach collection="studentOfferDto.fkInstitutionGroupIds" item="fkInstitutionGroupId" open="(" separator="," close=")">
                        #{fkInstitutionGroupId}
                    </foreach>
                </if>
            </where>
            UNION
            SELECT i.id
            FROM ais_sale_center.m_student_offer_item i
            LEFT JOIN ais_institution_center.m_institution_provider b ON i.fk_institution_provider_id=b.id
            <where>
                <if test="studentOfferDto.fkInstitutionGroupIds != null and studentOfferDto.fkInstitutionGroupIds.size()>0">
                    AND b.fk_institution_group_id IN
                    <foreach collection="studentOfferDto.fkInstitutionGroupIds" item="fkInstitutionGroupId" open="(" separator="," close=")">
                        #{fkInstitutionGroupId}
                    </foreach>
                </if>
            </where>
            ) c2 ON c.id=c2.id
        </if>

        <if test="studentOfferDto.channelName !=null and studentOfferDto.channelName !=''">
            <!-- #渠道过滤 -->
            INNER JOIN ais_institution_center.m_institution_channel c3 ON c.fk_institution_channel_id=c3.id AND (LOWER(c3.name) LIKE CONCAT("%",#{studentOfferDto.channelName},"%") OR LOWER(c3.name_chn) LIKE CONCAT("%",#{studentOfferDto.channelName},"%"))
        </if>

        <!-- 渠道过滤 多选 -->
        <if test="studentOfferDto.channelNames !=null and studentOfferDto.channelNames.size()>0">
            INNER JOIN ais_institution_center.m_institution_channel c3 ON c.fk_institution_channel_id=c3.id AND (
            <foreach collection="studentOfferDto.channelNames" item="channelName" index="index" separator="OR">
                LOWER(c3.name) like concat("%",#{channelName},"%") OR LOWER(c3.name_chn) like concat("%",#{channelName},"%")
            </foreach>
            )
        </if>

    <!-- #是否延迟入学过滤条件之一 -->
        <if test="studentOfferDto.isDeferEntrance !=null and studentOfferDto.isDeferEntrance !=''">
            LEFT JOIN (
            SELECT fk_student_offer_item_id, MAX(defer_entrance_time) max_defer_entrance_time
            FROM ais_sale_center.m_student_offer_item_defer_entrance_time GROUP BY fk_student_offer_item_id
            ) c4 ON c.id=c4.fk_student_offer_item_id
        </if>

        <if test="studentOfferDto.isCourseGlobalMatching == true">
            LEFT JOIN ais_institution_center.m_institution_course h ON c.fk_institution_course_id=h.id
        </if>



        WHERE 1=1

        <!-- #第一层 -->
        <if test="studentOfferDto.statusList !=null and studentOfferDto.statusList.size()>0">
            AND a.`status` IN
            <foreach collection="studentOfferDto.statusList" item="status" index="index" open="(" separator="," close=")">
                #{status}
            </foreach>
            <!-- #默认值 申请方案过滤状态 -->
        </if>

        <if test="studentOfferDto.num !=null and studentOfferDto.num != ''">
            AND a.num = #{studentOfferDto.num} -- #过滤方案编号
        </if>
        <!-- 方案创建时间过滤 -->
        <if test="studentOfferDto.createBeginDate!=null">
            AND DATE_FORMAT( a.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{studentOfferDto.createBeginDate},'%Y-%m-%d' )
        </if>
        <if test="studentOfferDto.createEndDate!=null">
            AND DATE_FORMAT( a.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{studentOfferDto.createEndDate},'%Y-%m-%d' )
        </if>
        <!-- 过滤代理名字 -->
        <if test="studentOfferDto.agentName !=null and studentOfferDto.agentName !=''">
            AND (
            <if test="studentOfferDto.fkAgentIds != null and studentOfferDto.fkAgentIds.size()>0">
                a1.id IN
                <foreach collection="studentOfferDto.fkAgentIds" item="agentId" open="(" separator="," close=")">
                    #{agentId}
                </foreach>
                OR
            </if>
            LOWER(a1.`name`) LIKE CONCAT("%",#{studentOfferDto.agentName},"%") OR LOWER(a1.name_note) LIKE CONCAT("%",#{
            studentOfferDto.agentName},"%")
            )
        </if>



        <if test="studentOfferDto.fkAreaCountryIdAgent !=null and studentOfferDto.fkAreaCountryIdAgent !=''">
            AND a1.fk_area_country_id = #{studentOfferDto.fkAreaCountryIdAgent} -- #过滤代理国家
            <!--  过滤代理省份 --></if>
        <if test="studentOfferDto.fkAreaStateIdAgentList != null and studentOfferDto.fkAreaStateIdAgentList.size()>0">
            AND a1.fk_area_state_id IN
            <foreach collection="studentOfferDto.fkAreaStateIdAgentList" item="areastateid" index="index" open="(" separator="," close=")">
                #{areastateid}
            </foreach>
        </if>
        <!--         BD中英名字及编号 -->
        <if test="studentOfferDto.bdName != null and studentOfferDto.bdName !=''">
            AND (
            (LOWER(a2.`name`) LIKE CONCAT("%",#{studentOfferDto.bdName},"%") OR LOWER(a2.name_en) LIKE CONCAT("%",#{studentOfferDto.bdName},"%") OR LOWER(a3.bd_code) LIKE CONCAT("%",#{studentOfferDto.bdName},"%"))
            <if test ="studentOfferDto.fkBdIds!=null and studentOfferDto.fkBdIds.size()>0" >
                OR a3.fk_staff_id IN
                <foreach collection="studentOfferDto.fkBdIds" item="fkBdId" open="(" separator="," close=")">
                    #{fkBdId}
                </foreach>
            </if>
            )
        </if>

        <if test="studentOfferDto.fkAreaRegionId != null">
            AND FIND_IN_SET(#{studentOfferDto.fkAreaRegionId},a3.fk_area_region_id)>0
        </if>

        <!-- 业务学校 -->
<!--        <if test="institutionIds !=null and institutionIds.size() > 0">-->
<!--            and EXISTS (SELECT 1 FROM m_student_offer_item msoi1 where msoi1.fk_student_offer_id = a.id and msoi1.fk_institution_id in-->
<!--            <foreach collection="institutionIds" item="institutionId" open="(" separator="," close=")">-->
<!--                #{institutionId}-->
<!--            </foreach>-->
<!--            )-->
<!--        </if>-->
        <!-- 业务学校 -->
        <if test="institutionIds !=null and institutionIds.size() > 0">
            AND EXISTS (SELECT 1 FROM m_student_offer_item msoi1 where msoi1.fk_student_id = b.id and msoi1.status = 1 and msoi1.fk_institution_id in
            <foreach collection="institutionIds" item="institutionId" open="(" separator="," close=")">
                #{institutionId}
            </foreach>
            )
        </if>

        <!-- 过滤我的国家权限 -->
        <if test="countryIds != null and countryIds.size()>0">
            AND a.fk_area_country_id IN
            <foreach collection="countryIds" item="countryId" index="index" open="(" separator="," close=")">
                #{countryId}
            </foreach>
        </if>

<!--        <if test="studentOfferDto.fkStaffIdWorkflow != null and studentOfferDto.fkStaffIdWorkflow != '' and  studentOfferDto.statusWorkflow==0">-->
<!--            AND a.fk_staff_id_workflow = #{studentOfferDto.fkStaffIdWorkflow} &#45;&#45; 我的申请-->
<!--        </if>-->
        <!-- 我的审批 -->
        <if test="studentOfferDto.ids != null and studentOfferDto.ids != ''">
            AND a.id in ${studentOfferDto.ids}
        </if>

        <!-- 第二层 -->
        <!-- 所属分公司 多选 -->
        <if test="studentOfferDto.fkCompanyIds != null and studentOfferDto.fkCompanyIds.size()>0">
            AND b.fk_company_id IN
            <foreach collection="studentOfferDto.fkCompanyIds" item="fkCompanyId" index="index" open="(" separator="," close=")">
                #{fkCompanyId}
            </foreach>
        </if>
        <!-- #所属分公司 兼容单个公司id -->
        <if test="studentOfferDto.fkCompanyId !=null and studentOfferDto.fkCompanyId !=''">
            AND b.fk_company_id=#{studentOfferDto.fkCompanyId}
        </if>
        <!-- 过滤学生中英文名字 -->
        <if test="studentOfferDto.studentName !=null and studentOfferDto.studentName !=''">
            AND (
            REPLACE(CONCAT(LOWER(b.first_name),LOWER(b.last_name)),' ','') like concat('%',#{studentOfferDto.studentName},'%')
            OR REPLACE(CONCAT(LOWER(b.last_name),LOWER(b.first_name)),' ','') like concat('%',#{studentOfferDto.studentName},'%')
            OR REPLACE(LOWER(b.`name`),' ','') like concat('%',#{studentOfferDto.studentName},'%')
            OR REPLACE(LOWER(b.last_name),' ','') like concat('%',#{studentOfferDto.studentName},'%')
            OR REPLACE(LOWER(b.first_name),' ','') like concat('%',#{studentOfferDto.studentName},'%')
            )

        </if>

        <if test="studentOfferDto.studentNum !=null and studentOfferDto.studentNum !=''">
            AND b.num = #{studentOfferDto.studentNum}
            -- #过滤学生编号
        </if>

        <if test="studentOfferDto.studentNationalityCountryId !=null">
            AND b.fk_area_country_id_nationality=#{studentOfferDto.studentNationalityCountryId}
        <!-- 学生国籍 -->
    </if>

        <if test="studentOfferDto.tel !=null and studentOfferDto.tel !=''">
            AND ( b.tel = #{studentOfferDto.tel} OR b.mobile = #{studentOfferDto.tel} )
        </if>
        <!-- 过滤电邮 -->
        <if test="studentOfferDto.email !=null and studentOfferDto.email !=''">
            AND LOWER(b.email) LIKE CONCAT("%",#{studentOfferDto.email},"%")
        </if>

        <!-- 第三层 -->
        <if test="(studentOfferDto.stepOrderList != null and studentOfferDto.stepOrderList.size > 0)
        or (studentOfferDto.fkAreaCountryId != null and studentOfferDto.fkAreaCountryId !='')
        or (studentOfferDto.openingTimeStart!=null)
        or (studentOfferDto.openingTimeEnd !=null)
        or (studentOfferDto.itemCreateBeginDate !=null)
        or (studentOfferDto.itemCreateEndDate !=null)
        or (studentOfferDto.failureReasonId !=null and studentOfferDto.failureReasonId !='')
        or (studentOfferDto.fkEnrolFailureReasonIds !=null and studentOfferDto.fkEnrolFailureReasonIds.size()>0)
        or (studentOfferDto.schoolName !=null and studentOfferDto.schoolName !='')
        or (studentOfferDto.courseName !=null and studentOfferDto.courseName !='')
        or (studentOfferDto.majorLevelId !=null and studentOfferDto.majorLevelId !='')
        or (studentOfferDto.majorLevelIds !=null and studentOfferDto.majorLevelIds.size()>0)
        or (studentOfferDto.newAppStatus !=null)
        or (studentOfferDto.fkInstitutionProviderId!=null and studentOfferDto.fkInstitutionProviderId!='')
        or (studentOfferDto.stepChangeBeginDate!=null or studentOfferDto.stepChangeEndDate !=null) and (studentOfferDto.changeStepId !=null and studentOfferDto.changeStepId.size>0)
        or (studentOfferDto.groupName !=null and studentOfferDto.groupName !='')
        or (studentOfferDto.fkInstitutionGroupIds !=null and studentOfferDto.fkInstitutionGroupIds.size()>0)
        or (studentOfferDto.channelName !=null and studentOfferDto.channelName !='')
        or (studentOfferDto.channelNames !=null and studentOfferDto.channelNames.size()>0)
        or (studentOfferDto.isDeferEntrance !=null and studentOfferDto.isDeferEntrance !='')
        or (studentOfferDto.targetCountryIdList != null and studentOfferDto.targetCountryIdList.size()>0)
        or ((studentOfferDto.majorLevelIds !=null and studentOfferDto.majorLevelIds.size()>0) or (studentOfferDto.oldCourseMajorLevelNames !=null and studentOfferDto.oldCourseMajorLevelNames.size()>0))
        or (studentOfferDto.openingTimeStart!=null) or (studentOfferDto.courseIds!=null and studentOfferDto.courseIds.size()>0)
        or (studentOfferDto.openingTimeEnd!=null) or (studentOfferDto.courseTypeGroupIds !=null and studentOfferDto.courseTypeGroupIds.size()>0) or (studentOfferDto.oldCourseTypeGroupNames !=null and studentOfferDto.oldCourseTypeGroupNames.size()>0)
        ">
            AND c.is_follow=0
            <!-- #申请计划，默认为有效状态 -->
            AND c.`status`=1
            <if test="!isStudentOfferItemFinancialHiding">
                AND IFNULL(c.is_follow_hidden, 0)!=1
            </if>
        </if>

        <!-- 过滤步骤状态 -->
        <if test="studentOfferDto.stepOrderList != null and studentOfferDto.stepOrderList.size > 0">
            AND c.fk_student_offer_item_step_id IN
            <foreach collection="studentOfferDto.stepOrderList" item="stepId" index="index" open="(" separator="," close=")">
                #{stepId}
            </foreach>
        </if>
        <!--申请学校提供商过滤-->
        <if test="studentOfferDto.fkInstitutionProviderId!=null and studentOfferDto.fkInstitutionProviderId!=''">
            AND c.fk_institution_provider_id = #{studentOfferDto.fkInstitutionProviderId}
        </if>
        <!-- #申请计划国家过滤 -->
        <if test="studentOfferDto.fkAreaCountryId != null and studentOfferDto.fkAreaCountryId !=''">
            AND c.fk_area_country_id=#{studentOfferDto.fkAreaCountryId}
        </if>

        <if test="studentOfferDto.targetCountryIdList != null and studentOfferDto.targetCountryIdList.size()>0">
            AND c.fk_area_country_id IN
            <foreach collection="studentOfferDto.targetCountryIdList" item="countryId" open="(" separator="," close=")">
                #{countryId}
            </foreach>
        </if>

        <!-- 开学时间 -->
        <if test="studentOfferDto.openingTimeStart != null">
            AND  (
            DATE_FORMAT(c.defer_opening_time,'%Y-%m-%d') <![CDATA[>=]]> DATE_FORMAT(#{studentOfferDto.openingTimeStart},'%Y-%m-%d')
            )
        </if>
        <if test="studentOfferDto.openingTimeEnd != null">
            AND (
            DATE_FORMAT(c.defer_opening_time,'%Y-%m-%d') <![CDATA[<=]]> DATE_FORMAT(#{studentOfferDto.openingTimeEnd},'%Y-%m-%d')
            )
        </if>

        <!-- 创建时间过滤 -->
        <if test="studentOfferDto.itemCreateBeginDate!=null">
            AND DATE_FORMAT( c.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{studentOfferDto.itemCreateBeginDate},'%Y-%m-%d' )
        </if>
        <!-- 创建时间过滤 -->
        <if test="studentOfferDto.itemCreateEndDate!=null">
            AND DATE_FORMAT( c.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{studentOfferDto.itemCreateEndDate},'%Y-%m-%d' )
        </if>
        <if test="studentOfferDto.newAppStatus!=null">
            <choose>
                <when test="studentOfferDto.newAppStatus == -1">
                    AND c.new_app_status is null
                </when>
                <otherwise>
                    AND c.new_app_status = #{studentOfferDto.newAppStatus}
                </otherwise>
            </choose>
        </if>
        <if test="studentOfferDto.isDeferEntrance !=null and studentOfferDto.isDeferEntrance !=''">
            <!-- #过滤是否延迟入学（是） -->
            <if test="studentOfferDto.isDeferEntrance">
                AND c.is_defer_entrance=1 AND c4.max_defer_entrance_time <![CDATA[> ]]> NOW()
            </if>
            <!-- 过滤是否延迟入学（否） -->
            <if test="!studentOfferDto.isDeferEntrance">
                AND ((c.is_defer_entrance=1 AND c4.max_defer_entrance_time <![CDATA[< ]]> NOW()) OR IFNULL(c.is_defer_entrance, 0)=0)
            </if>
        </if>
        <!-- 过滤失败原因 -->
        <if test="studentOfferDto.failureReasonId !=null and studentOfferDto.failureReasonId !=''">
            AND c.fk_enrol_failure_reason_id=#{studentOfferDto.failureReasonId}
        </if>

        <!-- 过滤失败原因 -->
        <if test="studentOfferDto.fkEnrolFailureReasonIds!=null and studentOfferDto.fkEnrolFailureReasonIds.size()>0">
            AND c.fk_enrol_failure_reason_id in
            <foreach collection="studentOfferDto.fkEnrolFailureReasonIds" item="failureReasonId" index="index" open="(" separator="," close=")">
                #{failureReasonId}
            </foreach>
        </if>

        <if test="studentOfferDto.institutionIds!=null and studentOfferDto.institutionIds.size()>0">
            AND c.fk_institution_id IN
            <foreach collection="studentOfferDto.institutionIds" item="institutionId" index="index" open="(" separator="," close=")">
                #{institutionId}
            </foreach>
        </if>
<!--        <if test="studentOfferDto.schoolName !=null and studentOfferDto.schoolName !=''">-->
<!--            AND (-->
<!--            <if test="studentOfferDto.institutionIds!=null and studentOfferDto.institutionIds.size()>0">-->
<!--                c.fk_institution_id IN-->
<!--                <foreach collection="studentOfferDto.institutionIds" item="institutionId" index="index" open="(" separator="," close=")">-->
<!--                    #{institutionId}-->
<!--                </foreach>-->
<!--                OR-->
<!--            </if>-->
<!--            LOWER(c.old_institution_full_name) LIKE CONCAT("%",#{studentOfferDto.schoolName},"%")-->
<!--            OR LOWER(c.old_institution_name) LIKE CONCAT("%",#{studentOfferDto.schoolName},"%")-->
<!--            )-->
<!--            &#45;&#45; #学校名称过滤，根据接口模糊搜索，先获取学校Id-->
<!--        </if>-->
        <if test="studentOfferDto.courseName !=null and studentOfferDto.courseName !=''">
            AND (
            <if test="studentOfferDto.courseIds!=null and studentOfferDto.courseIds.size()>0 and studentOfferDto.isCourseGlobalMatching == false">
                c.fk_institution_course_id IN
                <foreach collection="studentOfferDto.courseIds" item="courseId" index="index" open="(" separator="," close=")">
                    #{courseId}
                </foreach>
                OR
            </if>
            <if test="studentOfferDto.isCourseGlobalMatching == true">
                LOWER(h.name) like concat("%",#{studentOfferDto.courseName},"%") OR LOWER(h.name_chn) like concat("%",#{studentOfferDto.courseName},"%") OR
            </if>
            LOWER(c.old_course_custom_name) LIKE CONCAT("%",#{studentOfferDto.courseName},"%")
            )
            -- #课程名称过滤，根据接口模糊搜索，先获取课程Id
        </if>

        -- 课程类型过滤
        <if test="(studentOfferDto.courseTypeGroupIds !=null and studentOfferDto.courseTypeGroupIds.size()>0) or (studentOfferDto.oldCourseTypeGroupNames !=null and studentOfferDto.oldCourseTypeGroupNames.size()>0)">
            AND (
            <foreach collection="studentOfferDto.courseTypeGroupIds" item="courseTypeGroupId" index="index" separator="OR">
                FIND_IN_SET(#{courseTypeGroupId}, c.fk_institution_course_type_group_ids)>0
            </foreach>
            <if test="studentOfferDto.oldCourseTypeGroupNames !=null and studentOfferDto.oldCourseTypeGroupNames.size()>0">
                or
                <foreach collection="studentOfferDto.oldCourseTypeGroupNames" item="oldCourseTypeGroupName" index="index"  separator="OR">
                    LOWER(c.old_course_type_name) like concat("%",#{oldCourseTypeGroupName},"%")
                </foreach>
            </if>
            )
        </if>

        <!-- 课程等级过滤 -->
        <if test="(studentOfferDto.majorLevelId !=null and studentOfferDto.majorLevelId !='') or (studentOfferDto.oldCourseMajorLevelName !=null and studentOfferDto.oldCourseMajorLevelName!='')">
            AND (LOWER(c.old_course_major_level_name) LIKE concat("%",#{studentOfferDto.oldCourseMajorLevelName},"%")
            <if test="studentOfferDto.majorLevelId !=null and studentOfferDto.majorLevelId !=''">
                OR FIND_IN_SET(#{studentOfferDto.majorLevelId}, c.fk_institution_course_major_level_ids)>0
            </if>
            )
        </if>

        <!-- 课程等级过滤（多选） -->
        <if
                test="(studentOfferDto.majorLevelIds !=null and studentOfferDto.majorLevelIds.size()>0) or (studentOfferDto.oldCourseMajorLevelNames !=null and studentOfferDto.oldCourseMajorLevelNames.size()>0)">
            AND (
            <foreach collection="studentOfferDto.oldCourseMajorLevelNames" item="oldCourseMajorLevelName" index="index"  separator="OR">
                LOWER(c.old_course_major_level_name) like concat("%",#{oldCourseMajorLevelName},"%")
            </foreach>

            <if test="studentOfferDto.majorLevelIds !=null and studentOfferDto.majorLevelIds.size()>0">
                or
                <foreach collection="studentOfferDto.majorLevelIds" item="majorLevelId" index="index" separator="OR">
                    FIND_IN_SET(#{majorLevelId}, c.fk_institution_course_major_level_ids)>0
                </foreach>
            </if>
            )
        </if>

        <!--        <if test="studentOfferDto.majorLevelId !=null and studentOfferDto.majorLevelId !=''">-->
        <!--            AND FIND_IN_SET(#{studentOfferDto.majorLevelId}, c.fk_institution_course_major_level_ids) &#45;&#45; #课程等级过滤-->
        <!--        </if>-->

        ) a
        ) o ON a.id=o.id

        INNER JOIN (
        <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.offerPermissionSql"/>
        ) z ON a.id=z.id

        <if test="studentOfferDto.pageNumber!=null">
            <choose>
                <when test="studentOfferDto.studentName !=null and studentOfferDto.studentName !=''">
                    ORDER BY weights ASC
                </when>
                <otherwise>
                    ORDER BY a.gmt_create DESC
                </otherwise>
            </choose>
            <if test="studentOfferDto.pageSize!=null">
            LIMIT #{studentOfferDto.offset},#{studentOfferDto.pageSize}
            </if>
        </if>
    </select>
    <select id="getStudentOfferNew" resultType="com.get.salecenter.entity.StudentOffer">
        SELECT
        a.*,o.weights

        FROM ais_sale_center.m_student_offer a
        <!-- 显示2022年的学生
        <if test="staffId == 1247 or staffId ==  1245">
        INNER JOIN (
            SELECT
            fk_student_offer_id
            FROM
            m_student_offer_item
            WHERE
            YEAR ( defer_opening_time ) = 2022
            GROUP BY fk_student_offer_id
        )2022Student  ON 2022Student.fk_student_offer_id = a.id
        </if>
         -->
        INNER JOIN (
        SELECT DISTINCT a.id,a.weights
        FROM (

        SELECT a.id,LENGTH(CONCAT(b.first_name,b.last_name)) as weights
        -- #第一层####################
        FROM ais_sale_center.m_student_offer a


        <if test="studentOfferDto.bdName != null and studentOfferDto.bdName !=''">
            -- bdName过滤
            LEFT JOIN ais_permission_center.m_staff a2 ON a.fk_staff_id=a2.id
            LEFT JOIN ais_sale_center.r_staff_bd_code a3 ON a2.id = a3.fk_staff_id
        </if>

        -- #项目成员的过滤条件
        <if test="studentOfferDto.fkProjectRoleIds !=null and studentOfferDto.fkProjectRoleIds.size()>0">
            INNER JOIN (
            SELECT fk_table_id FROM ais_sale_center.s_student_project_role_staff
            WHERE fk_table_name='m_student_offer' AND is_active=1 AND fk_staff_id IN
            <foreach collection="studentOfferDto.fkProjectRoleIds" item="fkProjectRoleId" index="index" open="(" separator=","  close=")">
                #{fkProjectRoleId}
            </foreach>
            GROUP BY fk_table_id
            ) a4 ON a.id = a4.fk_table_id
        </if>


        -- #第二层####################
        INNER JOIN ais_sale_center.m_student b ON a.fk_student_id=b.id

        -- #第三层####################
        <if test="(studentOfferDto.stepOrderList != null and studentOfferDto.stepOrderList.size > 0)
        or (studentOfferDto.fkAreaCountryId != null and studentOfferDto.fkAreaCountryId !='')
        or (studentOfferDto.openingTimeStart!=null)
        or (studentOfferDto.openingTimeEnd !=null)
        or (studentOfferDto.itemCreateBeginDate !=null)
        or (studentOfferDto.itemCreateEndDate !=null)
        or (studentOfferDto.failureReasonId !=null and studentOfferDto.failureReasonId !='')
        or (studentOfferDto.schoolName !=null and studentOfferDto.schoolName !='')
        or (studentOfferDto.courseName !=null and studentOfferDto.courseName !='')
        or (studentOfferDto.majorLevelId !=null and studentOfferDto.majorLevelId !='')
        or (studentOfferDto.stepChangeBeginDate!=null or studentOfferDto.stepChangeEndDate !=null) and (studentOfferDto.changeStepId !=null and studentOfferDto.changeStepId.size>0)
        or (studentOfferDto.groupName !=null and studentOfferDto.groupName !='')
        or (studentOfferDto.channelName !=null and studentOfferDto.channelName !='')
        or (studentOfferDto.isDeferEntrance !=null and studentOfferDto.isDeferEntrance !='')
        ">
            INNER JOIN ais_sale_center.m_student_offer_item c ON a.id=c.fk_student_offer_id
            <if test="!isStudentOfferItemFinancialHiding">
                AND IFNULL(c.is_follow_hidden, 0)!=1
            </if>
            <!-- 业务学校 -->
<!--            <if test="institutionIds !=null and institutionIds.size() > 0">-->
<!--                AND c.fk_institution_id IN-->
<!--                <foreach collection="institutionIds" item="institutionId" open="(" separator="," close=")">-->
<!--                    #{institutionId}-->
<!--                </foreach>-->
<!--            </if>-->
        </if>

        -- #变更步骤过滤
        <if test="(studentOfferDto.stepChangeBeginDate!=null or studentOfferDto.stepChangeEndDate !=null) and (studentOfferDto.changeStepId !=null and studentOfferDto.changeStepId.size>0)">
            INNER JOIN (
            SELECT fk_student_offer_item_id FROM ais_sale_center.r_student_offer_item_step WHERE fk_student_offer_item_step_id in
            <foreach collection="studentOfferDto.changeStepId" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
            <if test="studentOfferDto.stepChangeBeginDate!=null">
                AND DATE_FORMAT( gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{studentOfferDto.stepChangeBeginDate},'%Y-%m-%d' ) -- 过滤状态变更时间
            </if>
            <if test="studentOfferDto.stepChangeEndDate!=null">
                AND DATE_FORMAT( gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{studentOfferDto.stepChangeEndDate},'%Y-%m-%d' ) --  过滤状态变更时间
            </if>
            GROUP BY fk_student_offer_item_id
            ) c1 ON c.id=c1.fk_student_offer_item_id
        </if>

        -- #集团过滤 groupName
        <if test="studentOfferDto.groupName !=null and studentOfferDto.groupName !=''">
            INNER JOIN (
            SELECT a.fk_institution_id
            FROM ais_institution_center.r_institution_provider_institution a
            LEFT JOIN ais_institution_center.m_institution_provider b ON a.fk_institution_provider_id=b.id
            LEFT JOIN ais_institution_center.m_institution_group c ON b.fk_institution_group_id=c.id
            WHERE c.name LIKE CONCAT("%",#{studentOfferDto.groupName},"%") OR c.name_chn LIKE CONCAT("%",#{studentOfferDto.groupName},"%")
            GROUP BY a.fk_institution_id
            ) c2 ON c.fk_institution_id=c2.fk_institution_id
        </if>

        <if test="studentOfferDto.channelName !=null and studentOfferDto.channelName !=''">
            -- #渠道过滤
            INNER JOIN ais_institution_center.m_institution_channel c3 ON c.fk_institution_channel_id=c3.id AND (c3.name LIKE CONCAT("%",#{studentOfferDto.channelName},"%") OR c3.name_chn LIKE CONCAT("%",#{studentOfferDto.channelName},"%"))
        </if>

        -- #是否延迟入学过滤条件之一
        <if test="studentOfferDto.isDeferEntrance !=null and studentOfferDto.isDeferEntrance !=''">
            LEFT JOIN (
            SELECT fk_student_offer_item_id, MAX(defer_entrance_time) max_defer_entrance_time
            FROM ais_sale_center.m_student_offer_item_defer_entrance_time GROUP BY fk_student_offer_item_id
            ) c4 ON c.id=c4.fk_student_offer_item_id
        </if>

        WHERE 1=1
        <if test="studentOfferDto.fkStudentId !=null and studentOfferDto.fkStudentId !=''">
            and a.fk_student_id = #{studentOfferDto.fkStudentId}
        </if>
        <if test="studentOfferDto.num !=null and studentOfferDto.num !=''">
            and a.num = #{studentOfferDto.num}
        </if>
        <if test="studentOfferDto.fkAgentId !=null and studentOfferDto.fkAgentId !=''">
            and a.fk_agent_id = #{studentOfferDto.fkAgentId}
        </if>
        <if test="studentOfferDto.fkStaffId !=null and studentOfferDto.fkStaffId !=''">
            and a.fk_staff_id = #{studentOfferDto.fkStaffId}
        </if>
        -- #第一层####################
        <if test="studentOfferDto.statusList !=null and studentOfferDto.statusList.size()>0">
            AND a.`status` IN
            <foreach collection="studentOfferDto.statusList" item="status" index="index" open="(" separator="," close=")">
                #{status}
            </foreach>
            -- #默认值 申请方案过滤状态
        </if>

        <if test="studentOfferDto.createBeginDate!=null">
            AND DATE_FORMAT( a.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{studentOfferDto.createBeginDate},'%Y-%m-%d' ) -- 方案创建时间过滤
        </if>
        <if test="studentOfferDto.createEndDate!=null">
            AND DATE_FORMAT( a.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{studentOfferDto.createEndDate},'%Y-%m-%d' ) -- 方案创建时间过滤
        </if>


        <if test="studentOfferDto.bdName != null and studentOfferDto.bdName !=''">
            AND (a2.`name` LIKE CONCAT("%",#{studentOfferDto.bdName},"%") OR a2.name_en LIKE CONCAT("%",#{studentOfferDto.bdName},"%") OR a3.bd_code LIKE CONCAT("%",#{studentOfferDto.bdName},"%")) -- #BD中英名字及编号
        </if>

        <if test="countryIds != null and countryIds.size()>0">
            AND a.fk_area_country_id IN
            <foreach collection="countryIds" item="countryId" index="index" open="(" separator="," close=")">
                #{countryId}
            </foreach> --  过滤我的国家权限
        </if>

        -- #第二层####################
        -- #所属分公司 多选
        <if test="studentOfferDto.fkCompanyIds != null and studentOfferDto.fkCompanyIds.size()>0">
            AND b.fk_company_id IN
            <foreach collection="studentOfferDto.fkCompanyIds" item="fkCompanyId" index="index" open="(" separator="," close=")">
                #{fkCompanyId} -- #所属分公司
            </foreach>
        </if>
        <if test="studentOfferDto.fkCompanyId !=null and studentOfferDto.fkCompanyId !=''">
            AND b.fk_company_id=#{studentOfferDto.fkCompanyId} -- #所属分公司 兼容单个公司id
        </if>

        <if test="studentOfferDto.studentName !=null and studentOfferDto.studentName !=''">
            AND (
            REPLACE(CONCAT(b.first_name,b.last_name),' ','') like concat('%',#{studentOfferDto.studentName},'%')
            OR REPLACE(CONCAT(b.last_name,b.first_name),' ','') like concat('%',#{studentOfferDto.studentName},'%')
            OR REPLACE(b.`name`,' ','') like concat('%',#{studentOfferDto.studentName},'%')
            OR REPLACE(b.last_name,' ','') like concat('%',#{studentOfferDto.studentName},'%')
            OR REPLACE(b.first_name,' ','') like concat('%',#{studentOfferDto.studentName},'%')
            ) -- 过滤学生中英文名字
            -- #过滤学生中英文名字
        </if>

        <if test="studentOfferDto.studentNum !=null and studentOfferDto.studentNum !=''">
            AND b.num = #{studentOfferDto.studentNum}
            -- #过滤学生编号
        </if>

        <if test="studentOfferDto.studentNationalityCountryId !=null">
            AND b.fk_area_country_id_nationality=#{studentOfferDto.studentNationalityCountryId} -- #学生国籍
            -- #学生国籍
        </if>

        <if test="studentOfferDto.tel !=null and studentOfferDto.tel !=''">
            AND ( b.tel = #{studentOfferDto.tel} OR b.mobile = #{studentOfferDto.tel} )
        </if>

        <if test="studentOfferDto.email !=null and studentOfferDto.email !=''">
            AND b.email LIKE CONCAT("%",#{studentOfferDto.email},"%") -- #过滤电邮
        </if>

        -- #第三层####################
        <if test="(studentOfferDto.stepOrderList != null and studentOfferDto.stepOrderList.size > 0)
        or (studentOfferDto.fkAreaCountryId != null and studentOfferDto.fkAreaCountryId !='')
        or (studentOfferDto.openingTimeStart!=null)
        or (studentOfferDto.openingTimeEnd !=null)
        or (studentOfferDto.itemCreateBeginDate !=null)
        or (studentOfferDto.itemCreateEndDate !=null)
        or (studentOfferDto.failureReasonId !=null and studentOfferDto.failureReasonId !='')
        or (studentOfferDto.schoolName !=null and studentOfferDto.schoolName !='')
        or (studentOfferDto.courseName !=null and studentOfferDto.courseName !='')
        or ((studentOfferDto.majorLevelId !=null and studentOfferDto.majorLevelId !='') or (studentOfferDto.oldCourseMajorLevelName !=null and studentOfferDto.oldCourseMajorLevelName!=''))
        or (studentOfferDto.stepChangeBeginDate!=null or studentOfferDto.stepChangeEndDate !=null) and (studentOfferDto.changeStepId !=null and studentOfferDto.changeStepId.size>0)
        or (studentOfferDto.groupName !=null and studentOfferDto.groupName !='')
        or (studentOfferDto.channelName !=null and studentOfferDto.channelName !='')
        or (studentOfferDto.isDeferEntrance !=null and studentOfferDto.isDeferEntrance !='')
        ">
            AND c.is_follow=0
            AND c.`status`=1 -- #申请计划，默认为有效状态
        </if>

        <if test="studentOfferDto.stepOrderList != null and studentOfferDto.stepOrderList.size > 0">
            AND c.fk_student_offer_item_step_id IN
            <foreach collection="studentOfferDto.stepOrderList" item="stepId" index="index" open="(" separator="," close=")">
                #{stepId} -- #过滤步骤状态
            </foreach>
        </if>

        <if test="studentOfferDto.fkAreaCountryId != null and studentOfferDto.fkAreaCountryId !=''">
            AND c.fk_area_country_id=#{studentOfferDto.fkAreaCountryId} -- #申请计划国家过滤
        </if>

        <if test="studentOfferDto.openingTimeStart!=null">
            AND DATE_FORMAT( c.defer_opening_time, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{studentOfferDto.openingTimeStart},'%Y-%m-%d' ) -- 开学时间过滤
        </if>
        <if test="studentOfferDto.openingTimeEnd!=null">
            AND DATE_FORMAT( c.defer_opening_time, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{studentOfferDto.openingTimeEnd},'%Y-%m-%d' ) -- 开学时间过滤
        </if>

        <if test="studentOfferDto.itemCreateBeginDate!=null">
            AND DATE_FORMAT( c.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{studentOfferDto.itemCreateBeginDate},'%Y-%m-%d' ) -- 创建时间过滤
        </if>
        <if test="studentOfferDto.itemCreateEndDate!=null">
            AND DATE_FORMAT( c.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{studentOfferDto.itemCreateEndDate},'%Y-%m-%d' ) -- 创建时间过滤
        </if>

        <if test="studentOfferDto.isDeferEntrance !=null and studentOfferDto.isDeferEntrance !=''">
            <if test="studentOfferDto.isDeferEntrance">
                AND c.is_defer_entrance=1 AND c4.max_defer_entrance_time <![CDATA[> ]]> NOW() -- #过滤是否延迟入学（是）
            </if>
            <if test="!studentOfferDto.isDeferEntrance">
                AND ((c.is_defer_entrance=1 AND c4.max_defer_entrance_time <![CDATA[< ]]> NOW()) OR IFNULL(c.is_defer_entrance, 0)=0) -- #过滤是否延迟入学（否）
            </if>
        </if>

        <if test="studentOfferDto.failureReasonId !=null and studentOfferDto.failureReasonId !=''">
            AND c.fk_enrol_failure_reason_id=#{studentOfferDto.failureReasonId} -- #过滤失败原因
        </if>


        <if test="studentOfferDto.institutionIds!=null and studentOfferDto.institutionIds.size()>0">
            AND c.fk_institution_id IN
            <foreach collection="studentOfferDto.institutionIds" item="institutionId" index="index" open="(" separator="," close=")">
                #{institutionId}
            </foreach>
        </if>
<!--        <if test="studentOfferDto.schoolName !=null and studentOfferDto.schoolName !=''">-->
<!--            AND (-->
<!--            <if test="studentOfferDto.institutionIds!=null and studentOfferDto.institutionIds.size()>0">-->
<!--                c.fk_institution_id IN-->
<!--                <foreach collection="studentOfferDto.institutionIds" item="institutionId" index="index" open="(" separator="," close=")">-->
<!--                    #{institutionId}-->
<!--                </foreach>-->
<!--                OR-->
<!--            </if>-->
<!--            c.old_institution_full_name LIKE CONCAT("%",#{studentOfferDto.schoolName},"%")-->
<!--            OR c.old_institution_name LIKE CONCAT("%",#{studentOfferDto.schoolName},"%")-->
<!--            )-->
<!--            &#45;&#45; #学校名称过滤，根据接口模糊搜索，先获取学校Id-->
<!--        </if>-->

        <if test="studentOfferDto.courseName !=null and studentOfferDto.courseName !=''">
            AND (
            <if test="studentOfferDto.courseIds!=null and studentOfferDto.courseIds.size()>0">
                c.fk_institution_course_id IN
                <foreach collection="studentOfferDto.courseIds" item="courseId" index="index" open="(" separator="," close=")">
                    #{courseId}
                </foreach>
                OR
            </if>
            c.old_course_custom_name LIKE CONCAT("%",#{studentOfferDto.courseName},"%")
            )
            -- #课程名称过滤，根据接口模糊搜索，先获取课程Id
        </if>

        -- 课程等级过滤
        <if test="(studentOfferDto.majorLevelId !=null and studentOfferDto.majorLevelId !='') or (studentOfferDto.oldCourseMajorLevelName !=null and studentOfferDto.oldCourseMajorLevelName!='')">
            AND (c.old_course_major_level_name LIKE concat("%",#{studentOfferDto.oldCourseMajorLevelName},"%")
            <if test="studentOfferDto.majorLevelId !=null and studentOfferDto.majorLevelId !=''">
                OR FIND_IN_SET(#{studentOfferDto.majorLevelId}, c.fk_institution_course_major_level_ids)
            </if>
            )
        </if>

        ) a
        ) o ON a.id=o.id


        INNER JOIN (
        <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.offerPermissionSql"/>
        ) z ON a.id=z.id
        <where>
            <if test="studentOfferDto.fkStudentId !=null and studentOfferDto.fkStudentId !=''">
                and a.fk_student_id = #{studentOfferDto.fkStudentId}
            </if>
        </where>

        ORDER BY a.gmt_create DESC
    </select>



    <select id="getAgentNameByOfferIds" resultType="com.get.salecenter.vo.StudentOfferVo">
        SELECT s.*,
        CONCAT(a.`name`,IF(a.name_note is null or a.name_note = "","",CONCAT("（",a.name_note,"）"))) as fkAgentName
        from m_student_offer s
        left join m_agent a on s.fk_agent_id = a.id
        where s.id in
        <foreach collection="offerIds" item="offerId" index="index" open="(" separator="," close=")">
            #{offerId}
        </foreach>
    </select>

    <select id="getStudentOfferAgentSelect" resultType="com.get.salecenter.vo.AgentsBindingVo">
        SELECT a.NAME as agentName,
               d.name as bdName,
               m.num,
               p.email
        FROM m_student_offer m
                 LEFT JOIN m_agent a ON a.id = m.fk_agent_id
                 LEFT JOIN ais_permission_center.m_staff d ON d.id = m.fk_staff_id
                 LEFT JOIN s_contact_person p ON p.id = m.fk_contact_person_id
                 LEFT JOIN m_student t on m.fk_student_id = t.id
        WHERE t.num = #{fkStudentNum}
          AND d.is_active = 1
        AND a.is_active = 1
    </select>
    <select id="test" resultType="com.get.salecenter.vo.COfferVo">
        SELECT
            s.id as student_id,f.id as offer_id,GROUP_CONCAT(CONCAT(u.role_key, r.fk_staff_id)) AS k
        FROM
            m_student s
        INNER JOIN m_student_offer f ON f.fk_student_id = s.id
        LEFT JOIN s_student_project_role_staff r on r.fk_table_id = f.id
        LEFT JOIN u_student_project_role u on u.id = r.fk_student_project_role_id
        WHERE s.fk_company_id = 2
        and r.fk_table_name = 'm_student_offer'
        AND f.fk_area_country_id in
        <foreach collection="ids" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        GROUP BY f.id
    </select>
    <select id="selectOfferAccepted" resultType="com.get.salecenter.entity.RStudentOfferItemStep">
        select r.*
        from r_student_offer_item_step r
        left join u_student_offer_item_step s on s.id = r.fk_student_offer_item_step_id
        where r.fk_student_offer_item_id = #{itemId}
        and s.step_key = 'STEP_OFFER_ACCEPTED'
    </select>
    <select id="getOfferBindingList" resultType="com.get.salecenter.vo.StudentOfferBindingVo">
        select
        m.*,
        a.num as studentNum,
        a.`name` as studentName,
        CONCAT(a.first_name,' ',a.last_name) as studentNameEng
        from m_student_offer m
        LEFT JOIN m_student a on m.fk_student_id = a.id
        where 1=1
        <if test="studentInfoDto.fkCompanyId !=null ">
            AND a.fk_company_id = #{studentInfoDto.fkCompanyId}
        </if>
        <if test="studentInfoDto.studentName !=null and studentInfoDto.studentName != ''">
            AND (
            REPLACE(CONCAT(a.first_name,a.last_name),' ','') like concat('%',#{studentInfoDto.studentName},'%')
            OR REPLACE(CONCAT(a.last_name,a.first_name),' ','') like concat('%',#{studentInfoDto.studentName},'%')
            OR REPLACE(a.`name`,' ','') like concat('%',#{studentInfoDto.studentName},'%')
            OR REPLACE(a.last_name,' ','') like concat('%',#{studentInfoDto.studentName},'%')
            OR REPLACE(a.first_name,' ','') like concat('%',#{studentInfoDto.studentName},'%'))
        </if>
        order by a.id,m.gmt_create desc
    </select>
    <select id="selectIssueCreatItem" resultType="java.lang.Long">
        select i.fk_student_offer_id
        from m_student_offer_item i
        inner join r_student_offer_item_issue_institution_course rc on rc.fk_student_offer_item_id = i.id
        where i.fk_student_offer_id in
        <foreach collection="ids" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getStudentOffer" resultType="com.get.salecenter.vo.StudentOfferItemUploadVo">
        SELECT x.id,
               x.fk_student_id,
               x.fk_institution_course_id,
               x2.name as course_name,
               x.old_course_custom_name,
               y.type_key,
               z.file_name_orc,
               z.file_name,
               z.file_path,
               z.file_key,
               z.file_guid,
               x3.fk_company_id
        FROM ais_sale_center.`m_student_offer_item` x
                 left join ais_institution_center.m_institution_course x2
                           on x2.id = x.fk_institution_course_id
                 left join ais_sale_center.s_media_and_attached y
                           on x.id = y.fk_table_id
                 left join ais_file_center.m_file_sale z
                           on z.file_guid = y.fk_file_guid
                 left join ais_sale_center.`m_student` x3
                           on x.fk_student_id=x3.id
                 left join ais_sale_center.log_student_offer_item_offer_file_identify l
                           on l.fk_file_guid=z.file_guid
        where y.fk_table_name = "m_student_offer_item"
          AND y.type_key in ("offer_accepted_media", "admitted_media")
          and l.id is null
        ORDER BY z.gmt_create desc
        limit 100
    </select>
    <select id="verifyStudentOffer" resultType="com.get.salecenter.entity.StudentOffer">
        SELECT * FROM `m_student_offer`
        where fk_student_id=#{offerVo.fkStudentId}
          and fk_area_country_id=#{offerVo.fkAreaCountryId}
          and fk_agent_id=#{offerVo.fkAgentId}
          and status!=0
        limit 1
    </select>

    <select id="getRoleStaffByOfferId" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT mso.id AS id, concat(if(ms.is_on_duty=1,'','【离职】'),ms.name,IFNULL(concat('(',ms.name_en,')'),'')) AS name
        FROM ais_sale_center.m_student_offer AS mso
                 INNER JOIN ais_sale_center.s_student_project_role_staff AS ssprs ON ssprs.fk_table_id = mso.id AND ssprs.fk_table_name = 'm_student_offer' AND is_active = 1
                 INNER JOIN ais_sale_center.u_student_project_role AS uspr ON uspr.id = ssprs.fk_student_project_role_id
                 INNER JOIN ais_permission_center.m_staff AS ms ON ms.id = ssprs.fk_staff_id
        WHERE uspr.role_key like concat('%',#{roleKeyStr},'%')
        AND mso.id IN
        <foreach collection="offerIds" item="offerId" separator="," open="(" close=")">
            #{offerId}
        </foreach>
        GROUP BY mso.id,ms.id
    </select>

    <select id="getStatusChangeDetails" resultType="com.get.salecenter.vo.StudentStepHistoryVo">
        SELECT
            offerItemStep.fk_student_offer_item_step_id AS stepId,
            itemStep.step_name AS stepName,
            COUNT( offerItemStep.fk_student_offer_item_step_id) AS stepCount,
            MIN(offerItemStep.gmt_create) AS earliestTime
        FROM
            `r_student_offer_item_step` AS offerItemStep
                INNER JOIN  u_student_offer_item_step AS itemStep on offerItemStep.fk_student_offer_item_step_id = itemStep.id
                INNER JOIN  m_student_offer_item AS offerItem ON (offerItemStep.fk_student_offer_item_id = offerItem.id ) AND offerItem.is_follow =0
                INNER JOIN  m_student AS student ON (offerItem.fk_student_id = student.id)
                <!-- 学习计划权限 -->
                INNER JOIN (
                <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.itemPermissionSql"/>
                ) z ON offerItem.id=z.id
        WHERE
            <if test="studentId!=null">
                student.id = #{studentId}
            </if>
        GROUP BY
            offerItemStep.fk_student_offer_item_step_id,itemStep.step_name;
    </select>

</mapper>