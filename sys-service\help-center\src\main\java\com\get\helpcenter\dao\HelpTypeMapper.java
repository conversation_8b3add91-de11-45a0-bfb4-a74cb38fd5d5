package com.get.helpcenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.helpcenter.vo.HelpTypeVo;
import com.get.helpcenter.entity.HelpType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface HelpTypeMapper extends BaseMapper<HelpType> {

    int insertSelective(HelpType record);

    Integer getMaxViewOrder();

    List<Long> getAllChildHelpTypeId(@Param("ids") List<Long> ids);

    // List<Long> getHelpTypeIds();

    List<HelpTypeVo> getHelpTypeSelect();

    HelpType getHelpTypeTitleById(@Param("id") Long id);
}