<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.StudentOfferItemSettlementAgentAccountMapper">
  <insert id="insert" parameterType="com.get.salecenter.entity.StudentOfferItemSettlementAgentAccount" useGeneratedKeys="true"
          keyProperty="id">
    insert into r_student_offer_item_settlement_agent_account (id, fk_student_offer_item_id, fk_currency_type_num_payable_plan, 
      fk_agent_contract_account_id, fk_currency_type_num, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkStudentOfferItemId,jdbcType=BIGINT}, #{fkCurrencyTypeNumPayablePlan,jdbcType=VARCHAR}, 
      #{fkAgentContractAccountId,jdbcType=BIGINT}, #{fkCurrencyTypeNum,jdbcType=VARCHAR}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.StudentOfferItemSettlementAgentAccount" useGeneratedKeys="true"
          keyProperty="id">
    insert into r_student_offer_item_settlement_agent_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkStudentOfferItemId != null">
        fk_student_offer_item_id,
      </if>
      <if test="fkCurrencyTypeNumPayablePlan != null">
        fk_currency_type_num_payable_plan,
      </if>
      <if test="fkAgentContractAccountId != null">
        fk_agent_contract_account_id,
      </if>
      <if test="fkCurrencyTypeNum != null">
        fk_currency_type_num,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkStudentOfferItemId != null">
        #{fkStudentOfferItemId,jdbcType=BIGINT},
      </if>
      <if test="fkCurrencyTypeNumPayablePlan != null">
        #{fkCurrencyTypeNumPayablePlan,jdbcType=VARCHAR},
      </if>
      <if test="fkAgentContractAccountId != null">
        #{fkAgentContractAccountId,jdbcType=BIGINT},
      </if>
      <if test="fkCurrencyTypeNum != null">
        #{fkCurrencyTypeNum,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getSettlementMarkByItemIds"
          resultType="com.get.salecenter.entity.StudentOfferItemSettlementAgentAccount">
    SELECT
      *
    FROM
      ais_sale_center.r_student_offer_item_settlement_agent_account AS soisa
        INNER JOIN ais_finance_center.u_currency_type AS uct ON uct.num = soisa.fk_currency_type_num
    ORDER BY
      uct.view_order DESC
  </select>
</mapper>