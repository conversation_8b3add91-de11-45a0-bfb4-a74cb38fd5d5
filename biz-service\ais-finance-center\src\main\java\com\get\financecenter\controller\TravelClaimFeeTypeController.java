package com.get.financecenter.controller;


import com.baomidou.mybatisplus.extension.api.ApiController;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.financecenter.dto.TravelClaimFeeTypeDto;
import com.get.financecenter.service.TravelClaimFeeTypeService;
import com.get.financecenter.vo.TravelClaimFeeTypeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 *差旅报销费用类型管理
 */
@Api(tags = "差旅报销费用类型管理")
@RestController
@RequestMapping("finance/travelClaimFeeType")
public class TravelClaimFeeTypeController extends ApiController {
    /**
     * 服务对象
     */
    @Resource
    private TravelClaimFeeTypeService travelClaimFeeTypeService;

    @ApiOperation(value = "分页查询所有数据",notes = "分页查询所有数据")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/差旅报销费用类型管理/查询")
    @PostMapping("list")
    public ResponseBo<TravelClaimFeeTypeVo> selectAll(@RequestBody SearchBean<TravelClaimFeeTypeDto> page) {
        List<TravelClaimFeeTypeVo> datas = travelClaimFeeTypeService.getTravelClaimFeeTypes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "差旅报销费用类型新增", notes = "批量新增")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/差旅报销费用类型管理/删除")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody List<TravelClaimFeeTypeDto> travelClaimFeeTypeDtos) {
         travelClaimFeeTypeService.batchAdd(travelClaimFeeTypeDtos);
         return ResponseBo.ok();
    }

    @ApiOperation(value = "差旅报销费用类型更新", notes = "更新数据")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/差旅报销费用类型管理/更新数据")
    @PostMapping("update")
    public ResponseBo<TravelClaimFeeTypeVo> update(@RequestBody TravelClaimFeeTypeDto travelClaimFeeTypeDto) {
        return new ResponseBo<>(travelClaimFeeTypeService.update(travelClaimFeeTypeDto));
    }



    @ApiOperation(value = "差旅报销费用类型删除", notes = "差旅报销费用类型id: id" )
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/差旅报销费用类型管理/删除")
    @GetMapping("delete")
    public ResponseBo delete(@RequestParam("id") Long id) {
         travelClaimFeeTypeService.deleteTravelClaimFeeType(id);
         return ResponseBo.ok();
    }

//    @ApiOperation(value = "排序（交换）", notes = "排序接口")
//    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/差旅报销费用类型管理/排序（交换）")
//    @PostMapping("sort")
//    public ResponseBo sort(@RequestBody List<Long> ids) {
//        travelClaimFeeTypeService.sort(ids);
//        return ResponseBo.ok();
//    }
@ApiOperation(value = "排序（拖拽）", notes = "排序接口")
@OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/差旅报销费用类型管理/排序（拖拽）")
@PostMapping("sort")
public ResponseBo sort(@RequestParam("start") Integer start , @RequestParam("end") Integer end) {
    travelClaimFeeTypeService.movingOrder(start, end);
    return ResponseBo.ok();
}

}

