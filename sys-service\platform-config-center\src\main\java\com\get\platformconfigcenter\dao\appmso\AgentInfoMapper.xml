<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.platformconfigcenter.dao.appmso.AgentInfoMapper">
  <resultMap id="BaseResultMap" type="com.get.platformconfigcenter.entity.AgentInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_agent_id" jdbcType="BIGINT" property="fkAgentId" />
    <result column="key_code" jdbcType="VARCHAR" property="keyCode" />
    <result column="host" jdbcType="VARCHAR" property="host" />
    <result column="website_title" jdbcType="VARCHAR" property="websiteTitle" />
    <result column="contact_tel" jdbcType="VARCHAR" property="contactTel" />
    <result column="whatsapp" jdbcType="VARCHAR" property="whatsapp" />
    <result column="facebook" jdbcType="VARCHAR" property="facebook" />
    <result column="instagram" jdbcType="VARCHAR" property="instagram" />
    <result column="fk_privacy_policy_id" jdbcType="BIGINT" property="fkPrivacyPolicyId" />
    <result column="is_active" jdbcType="BIT" property="isActive" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="language_code" jdbcType="VARCHAR" property="languageCode" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.get.platformconfigcenter.entity.AgentInfo">
    <result column="about_us" jdbcType="LONGVARCHAR" property="aboutUs" />
    <result column="contact_us" jdbcType="LONGVARCHAR" property="contactUs" />
  </resultMap>
  <sql id="Base_Column_List">
    id, fk_agent_id, key_code, host, website_title, contact_tel, whatsapp, facebook,
    instagram, fk_privacy_policy_id, is_active, gmt_create, gmt_create_user, gmt_modified,
    gmt_modified_user,language_code, public_level
  </sql>
  <sql id="Blob_Column_List">
    about_us, contact_us
  </sql>
    <!-- <insert id="insert" parameterType="com.get.platformconfigcenter.entity.AgentInfo" keyProperty="id" useGeneratedKeys="true">
       insert into m_agent_info (id, fk_agent_id, key_code,
         host, website_title, contact_tel,
         whatsapp, facebook, instagram,
         fk_privacy_policy_id, is_active, gmt_create,
         gmt_create_user, gmt_modified, gmt_modified_user,
         about_us, contact_us,language_code, public_level,email)
       values (#{id,jdbcType=BIGINT}, #{fkAgentId,jdbcType=BIGINT}, #{keyCode,jdbcType=VARCHAR},
         #{host,jdbcType=VARCHAR}, #{websiteTitle,jdbcType=VARCHAR}, #{contactTel,jdbcType=VARCHAR},
         #{whatsapp,jdbcType=VARCHAR}, #{facebook,jdbcType=VARCHAR}, #{instagram,jdbcType=VARCHAR},
         #{fkPrivacyPolicyId,jdbcType=BIGINT}, #{isActive,jdbcType=BIT}, #{gmtCreate,jdbcType=TIMESTAMP},
         #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR},
         #{aboutUs,jdbcType=LONGVARCHAR}, #{contactUs,jdbcType=LONGVARCHAR}, #{languageCode,jdbcType=VARCHAR}, #{publicLevel,jdbcType=VARCHAR},#{email,jdbcType=VARCHAR})
     </insert>
     <insert id="insertSelective" parameterType="com.get.platformconfigcenter.entity.AgentInfo">
       insert into m_agent_info
       <trim prefix="(" suffix=")" suffixOverrides=",">
         <if test="id != null">
           id,
         </if>
         <if test="fkAgentId != null">
           fk_agent_id,
         </if>
         <if test="keyCode != null">
           key_code,
         </if>
         <if test="host != null">
           host,
         </if>
         <if test="websiteTitle != null">
           website_title,
         </if>
         <if test="contactTel != null">
           contact_tel,
         </if>
         <if test="whatsapp != null">
           whatsapp,
         </if>
         <if test="facebook != null">
           facebook,
         </if>
         <if test="instagram != null">
           instagram,
         </if>
         <if test="fkPrivacyPolicyId != null">
           fk_privacy_policy_id,
         </if>
         <if test="isActive != null">
           is_active,
         </if>
         <if test="gmtCreate != null">
           gmt_create,
         </if>
         <if test="gmtCreateUser != null">
           gmt_create_user,
         </if>
         <if test="gmtModified != null">
           gmt_modified,
         </if>
         <if test="gmtModifiedUser != null">
           gmt_modified_user,
         </if>
         <if test="aboutUs != null">
           about_us,
         </if>
         <if test="contactUs != null">
           contact_us,
         </if>
         <if test="languageCode != null">
           language_code,
         </if>
         <if test="publicLevel != null">
           public_level,
         </if>
       </trim>
       <trim prefix="values (" suffix=")" suffixOverrides=",">
         <if test="id != null">
           #{id,jdbcType=BIGINT},
         </if>
         <if test="fkAgentId != null">
           #{fkAgentId,jdbcType=BIGINT},
         </if>
         <if test="keyCode != null">
           #{keyCode,jdbcType=VARCHAR},
         </if>
         <if test="host != null">
           #{host,jdbcType=VARCHAR},
         </if>
         <if test="websiteTitle != null">
           #{websiteTitle,jdbcType=VARCHAR},
         </if>
         <if test="contactTel != null">
           #{contactTel,jdbcType=VARCHAR},
         </if>
         <if test="whatsapp != null">
           #{whatsapp,jdbcType=VARCHAR},
         </if>
         <if test="facebook != null">
           #{facebook,jdbcType=VARCHAR},
         </if>
         <if test="instagram != null">
           #{instagram,jdbcType=VARCHAR},
         </if>
         <if test="fkPrivacyPolicyId != null">
           #{fkPrivacyPolicyId,jdbcType=BIGINT},
         </if>
         <if test="isActive != null">
           #{isActive,jdbcType=BIT},
         </if>
         <if test="gmtCreate != null">
           #{gmtCreate,jdbcType=TIMESTAMP},
         </if>
         <if test="gmtCreateUser != null">
           #{gmtCreateUser,jdbcType=VARCHAR},
         </if>
         <if test="gmtModified != null">
           #{gmtModified,jdbcType=TIMESTAMP},
         </if>
         <if test="gmtModifiedUser != null">
           #{gmtModifiedUser,jdbcType=VARCHAR},
         </if>
         <if test="aboutUs != null">
           #{aboutUs,jdbcType=LONGVARCHAR},
         </if>
         <if test="contactUs != null">
           #{contactUs,jdbcType=LONGVARCHAR},
         </if>
         <if test="languageCode != null">
           #{languageCode,jdbcType=VARCHAR},
         </if>
         <if test="publicLevel != null">
           #{publicLevel,jdbcType=VARCHAR},
         </if>
       </trim>
     </insert>
     <update id="updateByPrimaryKeySelective" parameterType="com.get.platformconfigcenter.entity.AgentInfo">
       update m_agent_info
       <set>
         <if test="fkAgentId != null">
           fk_agent_id = #{fkAgentId,jdbcType=BIGINT},
         </if>
         <if test="keyCode != null">
           key_code = #{keyCode,jdbcType=VARCHAR},
         </if>
         <if test="host != null">
           host = #{host,jdbcType=VARCHAR},
         </if>
         <if test="websiteTitle != null">
           website_title = #{websiteTitle,jdbcType=VARCHAR},
         </if>
         <if test="contactTel != null">
           contact_tel = #{contactTel,jdbcType=VARCHAR},
         </if>
         <if test="whatsapp != null">
           whatsapp = #{whatsapp,jdbcType=VARCHAR},
         </if>
         <if test="facebook != null">
           facebook = #{facebook,jdbcType=VARCHAR},
         </if>
         <if test="instagram != null">
           instagram = #{instagram,jdbcType=VARCHAR},
         </if>
         <if test="fkPrivacyPolicyId != null">
           fk_privacy_policy_id = #{fkPrivacyPolicyId,jdbcType=BIGINT},
         </if>
         <if test="isActive != null">
           is_active = #{isActive,jdbcType=BIT},
         </if>
         <if test="gmtCreate != null">
           gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
         </if>
         <if test="gmtCreateUser != null">
           gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
         </if>
         <if test="gmtModified != null">
           gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
         </if>
         <if test="gmtModifiedUser != null">
           gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
         </if>
         <if test="aboutUs != null">
           about_us = #{aboutUs,jdbcType=LONGVARCHAR},
         </if>
         <if test="contactUs != null">
           contact_us = #{contactUs,jdbcType=LONGVARCHAR},
         </if>
         <if test="languageCode != null">
           language_code = #{languageCode,jdbcType=LONGVARCHAR},
         </if>
         <if test="publicLevel != null">
           public_level = #{publicLevel,jdbcType=LONGVARCHAR},
         </if>
         <if test="email != null">
           email = #{email,jdbcType=LONGVARCHAR},
         </if>
       </set>
       where id = #{id,jdbcType=BIGINT}
     </update>
     <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.get.platformconfigcenter.entity.AgentInfo">
       update m_agent_info
       set fk_agent_id = #{fkAgentId,jdbcType=BIGINT},
         key_code = #{keyCode,jdbcType=VARCHAR},
         host = #{host,jdbcType=VARCHAR},
         website_title = #{websiteTitle,jdbcType=VARCHAR},
         contact_tel = #{contactTel,jdbcType=VARCHAR},
         whatsapp = #{whatsapp,jdbcType=VARCHAR},
         facebook = #{facebook,jdbcType=VARCHAR},
         instagram = #{instagram,jdbcType=VARCHAR},
         fk_privacy_policy_id = #{fkPrivacyPolicyId,jdbcType=BIGINT},
         is_active = #{isActive,jdbcType=BIT},
         gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
         gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
         gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
         gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
         about_us = #{aboutUs,jdbcType=LONGVARCHAR},
         contact_us = #{contactUs,jdbcType=LONGVARCHAR},
         language_code = #{languageCode,jdbcType=VARCHAR},
         public_level = #{publicLevel,jdbcType=VARCHAR}
       where id = #{id,jdbcType=BIGINT}
     </update>
     <update id="updateByPrimaryKey" parameterType="com.get.platformconfigcenter.entity.AgentInfo">
       update m_agent_info
       set fk_agent_id = #{fkAgentId,jdbcType=BIGINT},
         key_code = #{keyCode,jdbcType=VARCHAR},
         host = #{host,jdbcType=VARCHAR},
         website_title = #{websiteTitle,jdbcType=VARCHAR},
         contact_tel = #{contactTel,jdbcType=VARCHAR},
         whatsapp = #{whatsapp,jdbcType=VARCHAR},
         facebook = #{facebook,jdbcType=VARCHAR},
         instagram = #{instagram,jdbcType=VARCHAR},
         fk_privacy_policy_id = #{fkPrivacyPolicyId,jdbcType=BIGINT},
         is_active = #{isActive,jdbcType=BIT},
         gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
         gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
         gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
         gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
         language_code = #{languageCode,jdbcType=VARCHAR},
         public_level = #{publicLevel,jdbcType=VARCHAR}
       where id = #{id,jdbcType=BIGINT}
     </update>-->
</mapper>
