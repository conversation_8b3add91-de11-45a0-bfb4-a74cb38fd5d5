package com.get.remindercenter.utils;

import com.get.core.tool.utils.GeneralTool;
import lombok.extern.slf4j.Slf4j;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 邮件HTML标题提取工具类
 * 从HTML模板中提取class="title"的元素内容作为邮件标题
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Slf4j
public class EmailHtmlTitleExtractor {

    /**
     * 匹配class包含"title"的HTML元素的正则表达式
     * 支持各种HTML标签（h1, h2, div, span等）
     * 支持class有多个值的情况（如class="content title"）
     * 支持class前后有空格的情况（如class = "title"）
     */
    private static final Pattern TITLE_PATTERN = Pattern.compile(
            "<(\\w+)[^>]*class\\s*=\\s*\"[^\"]*title[^\"]*\"[^>]*>(.*?)</\\1>",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );

    /**
     * 匹配任何包含class="title"的元素（更宽松的匹配）
     * 支持单引号和双引号
     */
    private static final Pattern TITLE_PATTERN_LOOSE = Pattern.compile(
            "<(\\w+)[^>]*class\\s*=\\s*['\"][^'\"]*title[^'\"]*['\"][^>]*>(.*?)</\\1>",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );

    /**
     * 从HTML模板中提取标题
     *
     * @param htmlContent HTML模板内容
     * @return 提取的标题，如果未找到则返回null
     */
    public static String extractTitle(String htmlContent) {
        if (GeneralTool.isEmpty(htmlContent)) {
            log.debug("HTML内容为空，无法提取标题");
            return null;
        }

        try {
            // 首先尝试精确匹配
            String title = extractTitleWithPattern(htmlContent, TITLE_PATTERN);
            if (GeneralTool.isNotEmpty(title)) {
                log.debug("使用精确匹配提取到标题: {}", title);
                return title;
            }

            // 如果精确匹配失败，尝试宽松匹配
            title = extractTitleWithPattern(htmlContent, TITLE_PATTERN_LOOSE);
            if (GeneralTool.isNotEmpty(title)) {
                log.debug("使用宽松匹配提取到标题: {}", title);
                return title;
            }

            log.debug("HTML中未找到class='title'的元素");
            return null;

        } catch (Exception e) {
            log.error("提取HTML标题时发生异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 使用指定的正则表达式模式提取标题
     *
     * @param htmlContent HTML内容
     * @param pattern     正则表达式模式
     * @return 提取的标题
     */
    private static String extractTitleWithPattern(String htmlContent, Pattern pattern) {
        Matcher matcher = pattern.matcher(htmlContent);
        if (matcher.find()) {
            // 获取第2个捕获组（内容），第1个是标签名
            String rawTitle = matcher.group(2); 
            log.debug("提取到原始标题内容: [{}]", rawTitle);
            return cleanTitle(rawTitle);
        }
        return null;
    }

    /**
     * 清理提取的标题内容
     * 去除HTML标签、多余的空白字符等
     *
     * @param rawTitle 原始标题
     * @return 清理后的标题
     */
    private static String cleanTitle(String rawTitle) {
        if (GeneralTool.isEmpty(rawTitle)) {
            return rawTitle;
        }

        String cleanedTitle = rawTitle;

        // 去除嵌套的HTML标签
        cleanedTitle = cleanedTitle.replaceAll("<[^>]+>", "");

        // 解码常见的HTML实体
        cleanedTitle = cleanedTitle.replace("&lt;", "<")
                .replace("&gt;", ">")
                .replace("&amp;", "&")
                .replace("&quot;", "\"")
                .replace("&nbsp;", " ")
                .replace("&#39;", "'");

        // 去除多余的空白字符
        cleanedTitle = cleanedTitle.trim().replaceAll("\\s+", " ");

        return cleanedTitle;
    }

    /**
     * 获取多语言默认标题
     *
     * @param languageCode 语言代码（zh/en等）
     * @return 对应语言的默认标题
     */
    public static String getDefaultTitle(String languageCode) {
        if ("en".equalsIgnoreCase(languageCode)) {
            return "System Email Notification";
        } else {
            // 默认返回中文标题
            return "系统邮件通知";
        }
    }

    /**
     * 按优先级获取邮件标题
     *
     * @param externalTitle 外部传入的标题
     * @param htmlContent   HTML模板内容
     * @param languageCode  语言代码
     * @return 最终的邮件标题
     */
    public static String getTitleByPriority(String externalTitle, String htmlContent, String languageCode) {
        // 1. 优先使用外部传入的标题
        if (GeneralTool.isNotEmpty(externalTitle)) {
            log.debug("使用外部传入的标题: {}", externalTitle);
            return externalTitle;
        }

        // 2. 尝试从HTML模板中提取标题
        String htmlTitle = extractTitle(htmlContent);
        if (GeneralTool.isNotEmpty(htmlTitle)) {
            log.debug("从HTML模板中提取的标题: {}", htmlTitle);
            return htmlTitle;
        }

        // 3. 使用默认标题
        String defaultTitle = getDefaultTitle(languageCode);
        log.debug("使用默认标题: {}", defaultTitle);
        return defaultTitle;
    }

}