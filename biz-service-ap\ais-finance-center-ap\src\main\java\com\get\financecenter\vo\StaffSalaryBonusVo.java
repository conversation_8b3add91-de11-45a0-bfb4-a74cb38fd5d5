package com.get.financecenter.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class StaffSalaryBonusVo extends BaseEntity {

    @ApiModelProperty("类型Key：salary / bonus")
    private String typeKey;

    @ApiModelProperty("类型名称")
    private String typeKeyName;

    @ApiModelProperty("员工ID")
    private Long fkStaffId;

    @ApiModelProperty("员工编号")
    private String staffNum;

    @ApiModelProperty("员工名称")
    private String staffName;

    @ApiModelProperty("公司id")
    private Long fkCompanyId;

    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("年月，如：20250")
    @TableField("`year_month`")
    private String yearMonth;

    @ApiModelProperty("币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty("金额，工资：发卡工资 / 奖金：合计奖金")
    private BigDecimal amount;

    @ApiModelProperty("明细Json")
    private String jsonDetail;

    @ApiModelProperty("部门名称")
    private String departmentName;

    @ApiModelProperty("岗位名称")
    private String postName;


    @ApiModelProperty("备注")
    private String remark;


    @ApiModelProperty("导入文件")
    private String importFileName;

}
