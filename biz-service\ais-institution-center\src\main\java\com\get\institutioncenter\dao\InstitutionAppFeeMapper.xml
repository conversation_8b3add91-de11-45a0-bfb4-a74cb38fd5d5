<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionAppFeeMapper">
    <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.InstitutionAppFee">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_institution_id" jdbcType="BIGINT" property="fkInstitutionId"/>
        <result column="level_type" jdbcType="INTEGER" property="levelType"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="public_level" jdbcType="VARCHAR" property="publicLevel"/>
        <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime"/>
        <result column="is_free" jdbcType="BIT" property="isFree"/>
        <result column="is_active" jdbcType="BIT" property="isActive"/>
        <result column="payment_time_info" jdbcType="VARCHAR" property="paymentTimeInfo"/>
        <result column="url_info" jdbcType="VARCHAR" property="urlInfo"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, fk_institution_id, title, description, public_level, publish_time, is_active, 
    gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
  </sql>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true"
            parameterType="com.get.institutioncenter.entity.InstitutionAppFee">
        insert into m_institution_app_fee
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkInstitutionId != null">
                fk_institution_id,
            </if>
            <if test="levelType !=null">
                level_type,
            </if>
            <if test="title != null">
                title,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="publicLevel != null">
                public_level,
            </if>
            <if test="publishTime != null">
                publish_time,
            </if>
            <if test="isFree != null">
                is_free,
            </if>
            <if test="isActive != null">
                is_active,
            </if>
            <if test="paymentTimeInfo != null">
                payment_time_info,
            </if>
            <if test="urlInfo != null">
                url_info,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkInstitutionId != null">
                #{fkInstitutionId,jdbcType=BIGINT},
            </if>
            <if test="levelType !=null">
                #{levelType,jdbcType=INTEGER},
            </if>
            <if test="title != null">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="publicLevel != null">
                #{publicLevel,jdbcType=VARCHAR},
            </if>
            <if test="publishTime != null">
                #{publishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isFree != null">
                #{isFree,jdbcType=BIT},
            </if>
            <if test="isActive != null">
                #{isActive,jdbcType=BIT},
            </if>
            <if test="paymentTimeInfo != null">
                #{paymentTimeInfo,jdbcType=VARCHAR},
            </if>
            <if test="urlInfo != null">
                #{urlInfo,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="getWcInstitutionAppFeeList" resultType="com.get.institutioncenter.vo.InstitutionAppFeeVo">
        select mis.id,mis.fk_institution_id,GROUP_CONCAT(mis.public_level)as public_level,a.fk_table_name_type AS fkTableName,
        a.fk_table_id_type AS fkTableId
        from m_institution_app_fee mis
        INNER JOIN r_institution_course_app_info a ON mis.id = a.fk_table_id
        left join m_institution mi on mi.id = mis.fk_institution_id
        left join r_institution_view_order vo on vo.fk_institution_id=mis.fk_institution_id
        where mis.is_active=1 and mi.fk_area_country_id=#{fkCountryId} and vo.type=0
        <if test="schoolName !=''">
            and(
            mi.name like concat('%',#{schoolName},'%')
            or mi.name_chn like concat('%',#{schoolName},'%')
            or mi.short_name like concat('%',#{schoolName},'%')
            or mi.short_name_chn like concat('%',#{schoolName},'%')
            or CONCAT(mi.name,'（',mi.name_chn,'）')like concat('%',#{schoolName},'%'))
        </if>
        group by mis.id order by vo.view_order asc
    </select>
    <select id="getWcInstitutionAppFeeDatas" resultType="com.get.institutioncenter.vo.InstitutionAppFeeVo">
        select mis.*,vo.*,a.fk_table_name_type AS fkTableName,
        a.fk_table_id_type AS fkTableId from m_institution_app_fee mis
        INNER JOIN r_institution_course_app_info a ON mis.id = a.fk_table_id
        left join r_institution_view_order vo on vo.fk_institution_id=mis.fk_institution_id
        where
        a.fk_table_name = 'm_institution_app_fee'
        <if test="data.fkTableName!=null and data.fkTableName!=''">
            AND a.fk_table_name_type = #{data.fkTableName}
        </if>
        <if test="data.fkTableId!=null and data.fkTableId!=''">
            and mis.fk_table_id_type=#{data.fkTableId}
        </if>
        <if test="data.levelType!=null">
            and mis.level_type=#{data.levelType}
        </if>
        <if test="data.isFree!=null">
            and mis.is_free=#{data.isFree}
        </if>
        group by mis.id
        order by vo.view_order asc
    </select>
    <select id="selectInfoById" resultType="com.get.institutioncenter.vo.InstitutionAppFeeVo">
        SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId,a.group_key as effectiveDate
        FROM
            m_institution_app_fee s
        INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
        WHERE
             a.fk_table_name = 'm_institution_app_fee'
           AND s.id = #{id}
        GROUP BY s.id
    </select>
    <select id="datas" resultType="com.get.institutioncenter.vo.InstitutionAppFeeVo">
        SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
        FROM
        m_institution_app_fee s
        INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
        <if test="data.keyword!=null and data.keyword!=''">
            <if test="data.fkTableName=='u_area_country'">
                INNER u_area_country f ON f.id = a.fk_table_id_type AND (f.name LIKE CONCAT('%',#{data.keyword},'%') OR f.name_chn LIKE CONCAT('%',#{data.keyword},'%'))
            </if>
            <if test="data.fkTableName=='m_institution'">
                INNER m_institution f ON f.id = a.fk_table_id_type AND (f.name LIKE CONCAT('%',#{data.keyword},'%') OR f.name_chn LIKE CONCAT('%',#{data.keyword},'%'))
            </if>
            <if test="data.fkTableName=='m_institution_faculty'">
                INNER m_institution_faculty f ON f.id = a.fk_table_id_type AND (f.name LIKE CONCAT('%',#{data.keyword},'%') OR f.name_chn LIKE CONCAT('%',#{data.keyword},'%'))
            </if>
            <if test="data.fkTableName=='u_course_type_group'">
                INNER u_course_type_group f ON f.id = a.fk_table_id_type AND (f.type_group_name LIKE CONCAT('%',#{data.keyword},'%') OR f.type_group_name_chn LIKE CONCAT('%',#{data.keyword},'%'))
            </if>
            <if test="data.fkTableName=='u_course_type'">
                INNER u_course_type f ON f.id = a.fk_table_id_type AND (f.type_name LIKE CONCAT('%',#{data.keyword},'%') OR f.type_name_chn LIKE CONCAT('%',#{data.keyword},'%'))
            </if>
            <if test="data.fkTableName=='u_major_level'">
                INNER u_major_level f ON f.id = a.fk_table_id_type AND (f.level_name LIKE CONCAT('%',#{data.keyword},'%') OR f.level_name_chn LIKE CONCAT('%',#{data.keyword},'%'))
            </if>
            <if test="data.fkTableName=='m_institution_course'">
                INNER m_institution_course f ON f.id = a.fk_table_id_type AND (f.name LIKE CONCAT('%',#{data.keyword},'%') OR f.name_chn LIKE CONCAT('%',#{data.keyword},'%'))
            </if>
        </if>
        WHERE
        a.fk_table_name = 'm_institution_app_fee'
        <choose>
            <when test="data.fkTableName!=null and data.fkTableName!=''">
                AND a.fk_table_name_type = #{data.fkTableName}
            </when>
            <otherwise>
                AND a.fk_table_name_type = 'u_area_country'
            </otherwise>
        </choose>
        <choose>
            <when test="data.fkTableId!=null and data.fkTableId!=''">
                AND a.fk_table_id_type = #{data.fkTableId}
            </when>
            <otherwise>
                AND a.fk_table_id_type IN
                <foreach collection="countryIds" open="(" separator="," close=")" item="cid">
                    #{cid}
                </foreach>
            </otherwise>
        </choose>
        <if test="data.isActive!=null">
            AND s.is_active = #{data.isActive}
        </if>
        <if test="data.title!=null">
            AND (s.title LIKE concat('%',#{data.title},'%') OR s.description LIKE concat('%',#{data.description},'%'))
        </if>
        <if test="data.levelType!=null">
            and s.level_type=#{data.levelType}
        </if>
        <if test="data.isFree!=null">
            and s.is_free=#{data.isFree}
        </if>
        GROUP BY s.id
        ORDER BY s.is_active,s.gmt_create DESC
    </select>


    <select id="priorityMatchingQuery" resultType="com.get.institutioncenter.vo.InstitutionAppFeeVo">

        SELECT * FROM
        (
        SELECT
        x.*,
        GROUP_CONCAT(
        DISTINCT x.fkTableName
        ORDER BY
        x.fkTableName
        ) AS fk,
        GROUP_CONCAT(
        DISTINCT a.fk_table_name_type
        ORDER BY
        a.fk_table_name_type
        ) AS fc,
        COUNT(DISTINCT x.fkTableName) as priority
        FROM
        (
        <!-- 匹配国家-->
        <if test="weScholarshipAppDto.countryId!=null and weScholarshipAppDto.countryId!=''">

            SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
            FROM
            m_institution_app_fee s
            INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
            WHERE
            a.fk_table_name =  #{fkTableName}
            AND a.fk_table_name_type = 'u_area_country'
            AND a.fk_table_id_type =#{weScholarshipAppDto.countryId}
        </if>
        <!-- 匹配学校-->
        <if test="weScholarshipAppDto.institutionId!=null and weScholarshipAppDto.institutionId!=''">
            UNION ALL
            SELECT
            s.*, a.
            fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
            FROM
            m_institution_app_fee s
            INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
            WHERE
            a.fk_table_name = #{fkTableName}
            AND a.fk_table_name_type = 'm_institution'
            AND a.fk_table_id_type =#{weScholarshipAppDto.institutionId}
        </if>
        <!-- 匹配课程-->
        <if test="weScholarshipAppDto.courseId!=null and weScholarshipAppDto.courseId!=''">
            UNION ALL
            SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
            FROM
            m_institution_app_fee s
            INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
            WHERE
            a.fk_table_name = #{fkTableName}
            AND a.fk_table_name_type = 'm_institution_course'
            AND a.fk_table_id_type =#{weScholarshipAppDto.courseId}
        </if>
        ) x
        INNER JOIN r_institution_course_app_info a ON x.id = a.fk_table_id
        GROUP BY
        x.id
        ) f WHERE
        <foreach collection="priorityTypeKey.entrySet()" separator="OR" open="(" close=")" item="val">
            f.fk = #{val}
        </foreach>
        ORDER BY f.priority desc
    </select>
    <select id="selectInfoByIds" resultType="com.get.institutioncenter.vo.InstitutionAppFeeVo">
        SELECT
        s.*, a.fk_table_name_type AS fkTableName,
        a.fk_table_id_type AS fkTableId
        FROM
        m_institution_app_fee s
        INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
        WHERE
        a.fk_table_name = 'm_institution_app_fee'
        AND s.id IN
        <foreach collection="ids" item="cid" open="(" separator="," close=")">
            #{cid}
        </foreach>
        GROUP BY s.id
    </select>

    <select id="getAppFees" resultType="com.get.institutioncenter.dto.InstitutionAppFeeDto">
        SELECT
            s.*,
            a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
        FROM m_institution_app_fee s
        INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
        WHERE
            a.fk_table_name = 'm_institution_app_fee'
            AND a.fk_table_name_type = #{fkTableName}
            AND a.fk_table_id_type = #{fkTableId}
            AND s.is_free = 0
            AND s.is_active = 1
    </select>
</mapper>