package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/1/10
 * @TIME: 12:08
 * @Description:
 **/
@Data
public class StudentInsuranceDto extends BaseVoEntity {
    /**
     * 学生Id
     */
    @ApiModelProperty(value = "学生Id")
    private Long fkStudentId;

    /**
     * 业务渠道Id
     */
    @ApiModelProperty(value = "业务渠道Id")
    private Long fkBusinessChannelId;

    /**
     * 代理Id（业绩绑定）
     */
    @ApiModelProperty(value = "代理Id（业绩绑定）")
    private Long fkAgentId;

    /**
     * 员工Id（业绩绑定，BD）
     */
    @ApiModelProperty(value = "员工Id（业绩绑定，BD）")
    private Long fkStaffId;

    /**
     * 留学保险编号
     */
    @ApiModelProperty(value = "留学保险编号")
    private String num;

    @ApiModelProperty(value = "分类，枚举：0学生保险/1陪读人保险")
    private Integer type;

    @ApiModelProperty(value = "受保人姓名（中）")
    private String insurantName;

    @ApiModelProperty(value = "受保人姓（英/拼音）")
    private String insurantLastName;


    @ApiModelProperty(value = "受保人名（英/拼音）")
    private String insurantFirstName;

    @ApiModelProperty(value = "受保人性别")
    private Integer insurantGender;

    @ApiModelProperty(value = "受保人生日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insurantBirthday;

    @ApiModelProperty(value = "受保人护照编号")
    private String insurantPassportNum;

    /**
     * 前往国家Id
     */
    @ApiModelProperty(value = "前往国家Id")
    @NotNull(message = "国家Id不能为空", groups = {Add.class, Update.class})
    private Long fkAreaCountryId;

    /**
     * 保险单号
     */
    @ApiModelProperty(value = "保险单号")
    private String insuranceNum;

    /**
     * 保单开始时间
     */
    @ApiModelProperty(value = "保单开始时间")
    @NotNull(message = "保单开始时间不能为空", groups = {Add.class, Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceStartTime;

    /**
     * 保单结束时间
     */
    @ApiModelProperty(value = "保单结束时间")
    @NotNull(message = "保单结束时间不能为空", groups = {Add.class, Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceEndTime;

    /**
     * 保险购买时间
     */
    @ApiModelProperty(value = "保险购买时间")
    @NotNull(message = "保险购买时间不能为空", groups = {Add.class, Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceBuyTime;

    /**
     * 购买账户
     */
    @ApiModelProperty(value = "购买账户")
    @NotBlank(message = "购买账户不能为空", groups = {Add.class, Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String buyAccount;

    /**
     * 保险费币种
     */
    @ApiModelProperty(value = "保险费币种")
    @NotBlank(message = "保险费币种不能为空", groups = {Add.class, Update.class})
    private String fkCurrencyTypeNumInsurance;

    /**
     * 保险金额
     */
    @ApiModelProperty(value = "保险金额")
    @NotNull(message = "保险金额不能为空", groups = {Add.class, Update.class})
    private BigDecimal insuranceAmount;

    /**
     * 保险金额说明
     */
    @ApiModelProperty(value = "保险金额说明")
    private String insuranceAmountNote;

    /**
     * 佣金币种
     */
    @ApiModelProperty(value = "佣金币种")
    private String fkCurrencyTypeNumCommission;

    /**
     * 收取佣金比例%
     */
    @ApiModelProperty(value = "收取佣金比例%")
    private BigDecimal commissionRateReceivable;

    /**
     * 支付佣金比例%（代理）
     */
    @ApiModelProperty(value = "支付佣金比例%（代理）")
    private BigDecimal commissionRatePayable;

    /**
     * 固定收取佣金金额
     */
    @ApiModelProperty(value = "固定收取佣金金额")
    private BigDecimal fixedAmountReceivable;

    /**
     * 固定支付佣金金额（代理）
     */
    @ApiModelProperty(value = "固定支付佣金金额（代理）")
    private BigDecimal fixedAmountPayable;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 备注
     */
    @ApiModelProperty(value = "是否财务专用：0否/1是")
    private Boolean isHidden = false;
    /**
     * 状态：0关闭/1打开
     */
    @ApiModelProperty(value = "状态：0关闭/1打开")
    private Integer status;

    @ApiModelProperty(value = "角色员工id")
    @NotNull(message = "角色员工id不能为空", groups = {Add.class})
    private List<ProjectRoleStaffDto> roleStaffVo;
    /**
     * 服务提供商Id
     */
    @ApiModelProperty(value = "服务提供商Id")
    private Long fkBusinessProviderId;
    /**
     * 购买产品
     */
    @ApiModelProperty(value = "购买产品")
    private String businessProviderProduct;
    /**
     * 支付方式枚举：0飞汇/1易思汇/4信用卡
     */
    @ApiModelProperty(value = "支付方式枚举：0飞汇/1易思汇/4信用卡")
    private Integer paymentMethod;

   
}
