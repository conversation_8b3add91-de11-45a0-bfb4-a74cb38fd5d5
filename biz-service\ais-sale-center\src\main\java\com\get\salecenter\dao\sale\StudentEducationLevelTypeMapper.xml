<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.get.salecenter.dao.sale.StudentEducationLevelTypeMapper">

    <select id="getEducationDropDown" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT id,type_name as name,type_name_chn as nameChn FROM u_student_education_level_type ORDER BY view_order DESC
    </select>
</mapper>
