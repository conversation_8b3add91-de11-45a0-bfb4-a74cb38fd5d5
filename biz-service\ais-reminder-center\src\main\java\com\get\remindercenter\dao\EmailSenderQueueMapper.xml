<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.remindercenter.dao.EmailSenderQueueMapper">

    <select id="findDueEmailTasks" resultType="com.get.remindercenter.entity.EmailSenderQueue">
        SELECT *
        FROM m_email_sender_queue
        WHERE
        unix_timestamp(#{nowDate}) <![CDATA[>= ]]> unix_timestamp(operation_time)
        AND IFNULL(operation_count,0) <![CDATA[ < ]]> 3 AND operation_status = 0
    </select>
    <select id="getEmailSenderQueues" resultType="com.get.remindercenter.entity.EmailSenderQueue">
        SELECT *
        FROM m_email_sender_queue
        WHERE fk_email_type_key IN (
                                    'PAY_DEPOSIT_DUE_REMIND',
                                    'AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE',
                                    'OFFER_ACCEPT_DUE_REMIND',
                                    'AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE',
                                    'COURSE_OPENING_REMINDER'
            )
          AND DATE(gmt_create) BETWEEN '2025-05-28' AND '2025-05-30';
    </select>
</mapper>
