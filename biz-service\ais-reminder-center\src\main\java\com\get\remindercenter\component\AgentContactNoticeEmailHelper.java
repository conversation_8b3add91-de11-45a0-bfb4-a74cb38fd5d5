package com.get.remindercenter.component;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.utils.GeneralTool;
import com.get.remindercenter.dao.EmailSenderQueueMapper;
import com.get.remindercenter.dao.EmailTemplateMapper;
import com.get.remindercenter.dto.AgentContactNoticeDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.EmailTemplate;
import com.get.remindercenter.service.RemindTaskQueueService;
import com.get.remindercenter.utils.ReminderTemplateUtils;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;

import java.util.List;
import java.util.Map;


@Component("agentContactNoticeEmailHelper")
@Slf4j
public class AgentContactNoticeEmailHelper extends  EmailAbstractHelper{


    @Resource
    private EmailTemplateMapper emailTemplateMapper;

    @Resource
    private EmailSenderQueueMapper emailSenderQueueMapper;


    @Resource
    private RemindTaskQueueService remindTaskQueueService;


    @Override
    public void sendMail(EmailSenderQueue emailSenderQueue) {
        try {
            //组装数据
            AgentContactNoticeDto agentContactNoticeDto = assembleEmailData(emailSenderQueue);
            if (GeneralTool.isNotEmpty(agentContactNoticeDto.getStaffEmail())) {
                //设置邮件模板
                String template = setEmailTemplate(agentContactNoticeDto);
                EmailSystemMQMessageDto emailSystemMQMessageDto = new EmailSystemMQMessageDto();
                emailSystemMQMessageDto.setEmailSenderQueueId(agentContactNoticeDto.getId());
                emailSystemMQMessageDto.setTitle(agentContactNoticeDto.getEmailTitle());
                emailSystemMQMessageDto.setContent(template);
                emailSystemMQMessageDto.setToEmail(agentContactNoticeDto.getStaffEmail());
                remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
                emailSenderQueue.setEmailTo(agentContactNoticeDto.getStaffEmail());
                LambdaUpdateWrapper<EmailSenderQueue> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(EmailSenderQueue::getId, emailSenderQueue.getId())  // 更新条件（按ID匹配）
                        .set(EmailSenderQueue::getEmailTo,agentContactNoticeDto.getStaffEmail());  // 只更新 emailTo 字段
                emailSenderQueueMapper.update(null, updateWrapper);  // 传入 null，由 updateWrapper 控制更新
            }
        }catch (Exception e){
            log.error("agentContactNoticeEmailHelper error:{}", e);
            emailSenderQueue.setErrorMessage(e.getMessage());
            emailSenderQueue.setOperationCount(emailSenderQueue.getOperationCount() + 1);
            emailSenderQueue.setOperationStatus(-1);
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }
    }

    @Override
    public AgentContactNoticeDto assembleEmailData(EmailSenderQueue emailSenderQueue) {
        AgentContactNoticeDto reminderDto = new AgentContactNoticeDto();
        BeanUtils.copyProperties(emailSenderQueue, reminderDto);
        Map<String, String> parsedMap = null;
        String versionValue2 = null;
        String emailParameter = emailSenderQueue.getEmailParameter();
        if(GeneralTool.isNotEmpty(emailSenderQueue.getEmailParameter())){
            ObjectMapper mapper = new ObjectMapper();
            try {
                parsedMap = mapper.readValue(emailParameter, Map.class);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            String emailTo = parsedMap.get("email");
            reminderDto.setStaffEmail(emailTo);
            versionValue2 = parsedMap.get("versionValue");
            reminderDto.setLanguageCode(versionValue2);

        }
        if(GeneralTool.isEmpty(versionValue2)){
            reminderDto.setLanguageCode("zh");
        }
        reminderDto.setMap(parsedMap);
        return  reminderDto;

    }


    private String setEmailTemplate(AgentContactNoticeDto reminderDto) {
        List<EmailTemplate> remindTemplates = new ArrayList<>();

        if (GeneralTool.isNotEmpty(reminderDto.getFkEmailTypeKey())) {
            remindTemplates = emailTemplateMapper.selectList(Wrappers.<EmailTemplate>lambdaQuery().eq(EmailTemplate::getEmailTypeKey, reminderDto.getFkEmailTypeKey()));
        }

        if (GeneralTool.isEmpty(remindTemplates)) {
            log.error("邮箱模板不存在，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        String emailTemplate =null;
        if (!reminderDto.getLanguageCode().equals("en")) {
            emailTemplate = remindTemplates.get(0).getEmailTemplate();
        }else {
            emailTemplate = remindTemplates.get(0).getEmailTemplateEn();
        }
        emailTemplate  = ReminderTemplateUtils.getReminderTemplate(reminderDto.getMap(), emailTemplate);
        if (GeneralTool.isEmpty(emailTemplate)) {
            log.error("邮箱模板内容为空，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        emailTemplate = emailTemplate.replace("#{taskTitle}", reminderDto.getEmailTitle());
        //把emailTemplate插入到父模板里
        String parentEmailTemplate = null;
        if(GeneralTool.isNotEmpty(remindTemplates.get(0).getFkParentEmailTemplateId())&&remindTemplates.get(0).getFkParentEmailTemplateId()!=0){
            EmailTemplate parentTemplate = emailTemplateMapper.selectById(remindTemplates.get(0).getFkParentEmailTemplateId());
            Map map = new HashMap();
            map.put("subtemplate",emailTemplate);
            if (reminderDto.getLanguageCode().equals("en")) {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplateEn());
            }else {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplate());
            }

        }
        return parentEmailTemplate;
    }


}
