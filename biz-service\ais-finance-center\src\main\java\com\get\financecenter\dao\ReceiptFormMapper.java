package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.financecenter.vo.ReceiptFormVo;
import com.get.financecenter.entity.ReceiptForm;
import com.get.financecenter.dto.query.ReceiptFormQueryDto;
import com.get.salecenter.vo.ReceivablePlanNewVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface ReceiptFormMapper extends BaseMapper<ReceiptForm>,GetMapper<ReceiptForm> {

    List<ReceiptFormVo> getReceiptFormList(Long planId);


    List<ReceiptFormVo> getReceiptFormListByBindingStatus(@Param("bindingStatus") Integer bindingStatus,
                                                          @Param("fkReceiptFormIds") Set<Long> fkReceiptFormIds);

    List<ReceiptFormVo> getReceiptFormListFeignByPlanIds(@Param("planIds") Set<Long> planIds);

    /**
     * 根据付款类型获取付款单
     *
     * @param fkReceiptFeeTypeId
     * @return
     */
    Integer getReceiptFormCountByTypeId(@Param("fkReceiptFeeTypeId") Long fkReceiptFeeTypeId);

    /**
     * 检测是否绑定过付款单
     *
     * @Date 15:56 2021/12/29
     * <AUTHOR>
     */
    boolean checkForReceiptForm(@Param("numSettlementBatch") String numSettlementBatch);

    List<ReceiptForm> getReceiptForms(@Param("receiptFormDto") ReceiptFormQueryDto receiptFormVo, IPage<ReceivablePlanNewVo> iPage);

    /**
     * 根据服务费id获取对应的收款单信息
     * @param feeIds
     * @return
     */
    List<ReceiptFormVo> getReceiptFormsByTargetIds(@Param("feeIds") List<Long> feeIds);

}