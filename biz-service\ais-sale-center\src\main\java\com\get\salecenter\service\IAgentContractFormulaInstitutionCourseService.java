package com.get.salecenter.service;


import com.get.salecenter.dto.AgentContractFormulaInstitutionCourseDto;

import java.util.List;
import java.util.Map;

/**
 * @author: Sea
 * @create: 2021/4/22 18:02
 * @verison: 1.0
 * @description:
 */
public interface IAgentContractFormulaInstitutionCourseService {
    /**
     * @return void
     * @Description :新增
     * @Param [agentContractFormulaInstitutionCourseDto]
     * <AUTHOR>
     */
    Long addAgentContractFormulaInstitutionCourse(AgentContractFormulaInstitutionCourseDto agentContractFormulaInstitutionCourseDto);

    /**
     * @return void
     * @Description :删除
     * @Param [agentContractFormulaId]
     * <AUTHOR>
     */
    void deleteByFkid(Long agentContractFormulaId);

    /**
     * @Description :通过学生代理合同公式id 查找对应课程等级名称
     * @Param [agentContractFormulaIds]
     * <AUTHOR>
     */
    Map<Long, String> getInstitutionCourseNameMapByFkids(List<Long> agentContractFormulaIds);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过学生代理合同公式id 查找对应课程等级ids
     * @Param [agentContractFormulaId]
     * <AUTHOR>
     */
    List<Long> getInstitutionCourseIdListByFkid(Long agentContractFormulaId);
}
