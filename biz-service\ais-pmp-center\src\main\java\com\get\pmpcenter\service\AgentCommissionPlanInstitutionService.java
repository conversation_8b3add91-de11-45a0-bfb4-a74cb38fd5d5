package com.get.pmpcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.pmpcenter.dto.common.LogDto;
import com.get.pmpcenter.entity.AgentCommissionPlanInstitution;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:17
 * @Version 1.0
 */
public interface AgentCommissionPlanInstitutionService extends IService<AgentCommissionPlanInstitution> {

    /**
     * 保存代理佣金方案关联的学校
     *
     * @param institutionIds
     * @param agentCommissionPlanId
     */
    List<LogDto>  saveAgentCommissionPlanInstitution(List<Long> institutionIds, Long agentCommissionPlanId);


    /**
     * 更新代理佣金方案关联的学校
     * @param institutionIds
     * @param providerCommissionPlanId
     */
    void updateAgentCommissionPlanInstitution(List<Long> institutionIds, Long providerCommissionPlanId,Boolean isSave);


    /**
     * 同步代理佣金方案关联的学校
     * @param institutionIds
     * @param providerCommissionPlanId
     */
    void syncAgentCommissionPlanInstitution(List<Long> institutionIds,Long providerCommissionPlanId);
}
