package com.get.examcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.examcenter.vo.MediaAndAttachedVo;
import com.get.examcenter.entity.ExamMediaAndAttached;
import com.get.examcenter.mapper.exam.MediaAndAttachedMapper;
import com.get.examcenter.service.MediaAndAttachedService;
import com.get.examcenter.dto.MediaAndAttachedDto;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.feign.IFileCenterClient;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by Jerry.
 * Time: 18:04
 * Date: 2021/8/27
 * Description:媒体附件实现类
 */
@Service
public class MediaAndAttachedServiceImpl implements MediaAndAttachedService {

    @Resource
    private MediaAndAttachedMapper mediaAndAttachedMapper;
    @Resource
    private IFileCenterClient fileCenterClient;
    @Resource
    private UtilService utilService;

    @Override
    public List<MediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo) {
        List<ExamMediaAndAttached> mediaAndAttacheds = getMediaAndAttacheds(attachedVo);
        return getFileMedia(mediaAndAttacheds);
    }

    /**
     * @Description: 根据表ids获取批量的附件
     * @Author: Jerry
     * @Date:9:32 2021/9/7
     */
    @Override
    public Map<Long, List<MediaAndAttachedVo>> getMediaAndAttachedDtoByFkTableIds(String fkTableName, Set<Long> fkTableIds) {
        List<ExamMediaAndAttached> mediaAndAttacheds = getMediaAndAttachedsByFkTableIds(fkTableName, fkTableIds);
        return getFileMediaByFkTableIds(mediaAndAttacheds);
    }

    /**
     * @Description: 根据表ids解析媒体附件, 返回表id对应的附件对象
     * @Author: Jerry
     * @Date:9:35 2021/9/7
     */
    private Map<Long, List<MediaAndAttachedVo>> getFileMediaByFkTableIds(List<ExamMediaAndAttached> mediaAndAttachedList) {
        Map<Long, List<MediaAndAttachedVo>> map = new HashMap<>();
        if (GeneralTool.isEmpty(mediaAndAttachedList)) {
            return map;
        }
        //获取guid集合
        List<String> guidList = mediaAndAttachedList.stream().map(ExamMediaAndAttached::getFkFileGuid).collect(Collectors.toList());
        if (GeneralTool.isEmpty(guidList)) {
            return map;
        }
        //转换成DTO
        List<MediaAndAttachedVo> mediaAndAttachedVos = mediaAndAttachedList.stream()
                .map(mediaAndAttached -> BeanCopyUtils.objClone(mediaAndAttached, MediaAndAttachedVo::new)).collect(Collectors.toList());
        //根据GUID服务调用查询
//        ListResponseBo responseBo = fileCenterClient.findFileByGuid(guidList, LoggerModulesConsts.EXAMCENTER);
        List<FileDto> fileDtos = new ArrayList<>();
        Map<String, List<String>> guidListWithTypeMap = new HashMap<>();
        guidListWithTypeMap.put(LoggerModulesConsts.EXAMCENTER, guidList);
        Result<List<FileDto>> fileDtoResult = fileCenterClient.findFileByGuid(guidListWithTypeMap);
        if (fileDtoResult.isSuccess() && GeneralTool.isNotEmpty(fileDtoResult.getData())) {
            JSONArray jsonArray = JSONArray.fromObject(fileDtoResult.getData());
            fileDtos.addAll(JSONArray.toList(jsonArray, new FileDto(), new JsonConfig()));
        }
        //返回结果不为空时
        List<MediaAndAttachedVo> collect = null;
        if (GeneralTool.isNotEmpty(fileDtos)) {
            //遍历查询GUID是否一致
            collect = mediaAndAttachedVos.stream().map(mediaAndAttachedDto -> fileDtos
                    .stream()
                    .filter(fileDto -> fileDto.getFileGuid().equals(mediaAndAttachedDto.getFkFileGuid()))
                    .findFirst()
                    .map(fileDto -> {
                        mediaAndAttachedDto.setFilePath(fileDto.getFilePath());
                        mediaAndAttachedDto.setFileNameOrc(fileDto.getFileNameOrc());
                        mediaAndAttachedDto.setFileKey(fileDto.getFileKey());
                        mediaAndAttachedDto.setTypeValue(FileTypeEnum.getValue(mediaAndAttachedDto.getTypeKey()));
                        /*mediaAndAttachedDto.setFkTableName(null);*/
                        return mediaAndAttachedDto;
                    }).orElse(null)
            ).collect(Collectors.toList());
        }
        if (GeneralTool.isEmpty(collect)) {
            return map;
        }
        collect.removeIf(Objects::isNull);
        for (MediaAndAttachedVo mediaAndAttachedVo : collect) {
            //表id
            Long fkTableId = mediaAndAttachedVo.getFkTableId();
            //如果集合中包含表id，则往原记录上面添加记录
            if (map.containsKey(fkTableId)) {
                List<MediaAndAttachedVo> mediaAndAttachedVoList = map.get(fkTableId);
                mediaAndAttachedVoList.add(mediaAndAttachedVo);
                continue;
            }
            //添加新记录
            List<MediaAndAttachedVo> mediaAndAttachedVoList = new ArrayList<>();
            mediaAndAttachedVoList.add(mediaAndAttachedVo);
            map.put(fkTableId, mediaAndAttachedVoList);
        }
        return map;
    }

    /**
     * @Description: 根据表ids获取媒体附件
     * @Author: Jerry
     * @Date:9:34 2021/9/7
     */
    private List<ExamMediaAndAttached> getMediaAndAttachedsByFkTableIds(String fkTableName, Set<Long> fkTableIds) {
//        Example example = new Example(MediaAndAttached.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkTableName", fkTableName);
//        criteria.andIn("fkTableId", fkTableIds);
//        example.orderBy("indexKey").desc();
//        return mediaAndAttachedMapper.selectByExample(example);
        return mediaAndAttachedMapper.selectList(Wrappers.<ExamMediaAndAttached>lambdaQuery()
                .eq(ExamMediaAndAttached::getFkTableName, fkTableName)
                .in(ExamMediaAndAttached::getFkTableId, fkTableIds)
                .orderByDesc(ExamMediaAndAttached::getIndexKey));
    }

    /**
     * @Description: 上传文件
     * @Author: Jerry
     * @Date:18:06 2021/8/27
     */
    @Override
    public List<FileDto> upload(MultipartFile[] multipartFiles, String type) {
        if (GeneralTool.isEmpty(multipartFiles)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }
        List<FileDto> fileDtos = null;
        //            ListResponseBo responseBo = fileCenterClient.upload(multipartFiles, type);
        Result<List<FileDto>> result = fileCenterClient.upload(multipartFiles, type);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            JSONArray jsonArray = JSONArray.fromObject(result.getData());
            fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_upload_fail"));
        }
        return fileDtos;
    }

    /**
     * @Description: 上传附件
     * @Author: Jerry
     * @Date:18:06 2021/8/27
     */
    @Override
    public List<FileDto> uploadAppendix(MultipartFile[] multipartFiles, String type) {
        if (GeneralTool.isEmpty(multipartFiles)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }
        List<FileDto> fileDtos = null;
        //            ListResponseBo responseBo = fileCenterClient.uploadAppendix(multipartFiles, type);
        Result<List<FileDto>> result = fileCenterClient.uploadAppendix(multipartFiles, type);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            JSONArray jsonArray = JSONArray.fromObject(result.getData());
            fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_upload_fail"));
        }
        return fileDtos;
    }

    /**
     * 获取媒体附件
     *
     * @param attachedVo
     * @return
     * @throws GetServiceException
     */
    private List<ExamMediaAndAttached> getMediaAndAttacheds(MediaAndAttachedDto attachedVo) {
        if (GeneralTool.isEmpty(attachedVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
//        Example example = new Example(MediaAndAttached.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<ExamMediaAndAttached> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(attachedVo.getTypeKey())) {
//            criteria.andEqualTo("typeKey", attachedVo.getTypeKey());
            lambdaQueryWrapper.eq(ExamMediaAndAttached::getTypeKey, attachedVo.getTypeKey());
        }
//        criteria.andEqualTo("fkTableName", attachedVo.getFkTableName());
        lambdaQueryWrapper.eq(ExamMediaAndAttached::getFkTableName, attachedVo.getFkTableName());
//        criteria.andEqualTo("fkTableId", attachedVo.getFkTableId());
        lambdaQueryWrapper.eq(ExamMediaAndAttached::getFkTableId, attachedVo.getFkTableId());
//        example.orderBy("indexKey").desc();
        lambdaQueryWrapper.orderByDesc(ExamMediaAndAttached::getIndexKey);
        return mediaAndAttachedMapper.selectList(lambdaQueryWrapper);
    }

    private List<MediaAndAttachedVo> getFileMedia(List<ExamMediaAndAttached> mediaAndAttachedList) {
        if (GeneralTool.isEmpty(mediaAndAttachedList)) {
            return null;
        }
        //获取guid集合
        List<String> guidList = mediaAndAttachedList.stream().map(ExamMediaAndAttached::getFkFileGuid).collect(Collectors.toList());
        if (GeneralTool.isEmpty(guidList)) {
            return null;
        }
        //转换成DTO
        List<MediaAndAttachedVo> mediaAndAttachedVos = mediaAndAttachedList.stream()
                .map(mediaAndAttached -> BeanCopyUtils.objClone(mediaAndAttached, MediaAndAttachedVo::new)).collect(Collectors.toList());
        //根据GUID服务调用查询
//        ListResponseBo responseBo = fileCenterClient.getFile(guidList, LoggerModulesConsts.EXAMCENTER);
        List<FileDto> fileDtos = null;
        Map<String, List<String>> guidListWithTypeMap = new HashMap<>();
        guidListWithTypeMap.put(LoggerModulesConsts.EXAMCENTER, guidList);
        Result<List<FileDto>> result = fileCenterClient.findFileByGuid(guidListWithTypeMap);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            JSONArray jsonArray = JSONArray.fromObject(result.getData());
            fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
        }

        //返回结果不为空时
        List<MediaAndAttachedVo> collect = null;
        if (GeneralTool.isNotEmpty(fileDtos)) {
            //遍历查询GUID是否一致
            List<FileDto> finalFileDtos = fileDtos;
            collect = mediaAndAttachedVos.stream().map(mediaAndAttachedDto -> finalFileDtos
                    .stream()
                    .filter(fileDto -> fileDto.getFileGuid().equals(mediaAndAttachedDto.getFkFileGuid()))
                    .findFirst()
                    .map(fileDto -> {
                        mediaAndAttachedDto.setFilePath(fileDto.getFilePath());
                        mediaAndAttachedDto.setFileNameOrc(fileDto.getFileNameOrc());
                        mediaAndAttachedDto.setFileKey(fileDto.getFileKey());
                        mediaAndAttachedDto.setTypeValue(FileTypeEnum.getValue(mediaAndAttachedDto.getTypeKey()));
                        /*mediaAndAttachedDto.setFkTableName(null);*/
                        return mediaAndAttachedDto;
                    }).orElse(null)
            ).collect(Collectors.toList());
        }
        return collect;
    }

    /**
     * @Description: 保存文件
     * @Author: Jerry
     * @Date:18:07 2021/8/27
     */
    @Override
    public MediaAndAttachedVo addMediaAndAttached(MediaAndAttachedDto mediaAttachedVo) {
        if (GeneralTool.isEmpty(mediaAttachedVo.getFkTableName())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("tableName_null"));
        }
        ExamMediaAndAttached andAttached = BeanCopyUtils.objClone(mediaAttachedVo, ExamMediaAndAttached::new);
        Integer nextIndexKey = mediaAndAttachedMapper.getNextIndexKey(andAttached.getFkTableId(), mediaAttachedVo.getFkTableName());
        nextIndexKey = GeneralTool.isNotEmpty(nextIndexKey) ? nextIndexKey : 0;
        //实例化对象
        andAttached.setIndexKey(nextIndexKey);
        utilService.updateUserInfoToEntity(andAttached);
        mediaAndAttachedMapper.insertSelective(andAttached);
        MediaAndAttachedVo mediaAndAttachedVo = BeanCopyUtils.objClone(andAttached, MediaAndAttachedVo::new);
        mediaAndAttachedVo.setFilePath(mediaAttachedVo.getFilePath());
        mediaAndAttachedVo.setFileNameOrc(mediaAttachedVo.getFileNameOrc());
        mediaAndAttachedVo.setTypeValue(FileTypeEnum.getValue(mediaAttachedVo.getTypeKey()));
        mediaAndAttachedVo.setId(andAttached.getId());
        mediaAndAttachedVo.setFileKey(mediaAttachedVo.getFileKey());
        return mediaAndAttachedVo;
    }

    @Override
    public void deleteMediaAndAttached(String fkTableName, Long fkTableId) {
//        Example example = new Example(MediaAndAttached.class);
//        example.createCriteria().andEqualTo("fkTableName", fkTableName).andEqualTo("fkTableId", fkTableId);
//        mediaAndAttachedMapper.deleteByExample(example);
        mediaAndAttachedMapper.delete(Wrappers.<ExamMediaAndAttached>lambdaQuery()
                .eq(ExamMediaAndAttached::getFkTableName, fkTableName)
                .eq(ExamMediaAndAttached::getFkTableId, fkTableId));
    }
}
