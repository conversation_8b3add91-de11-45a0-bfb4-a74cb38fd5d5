package com.get.permissioncenter.controller;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.core.log.annotation.OperationLogger;
import com.get.institutioncenter.dto.query.InstitutionQueryDto;
import com.get.permissioncenter.dto.RInstitutionPermissionGroupInstitutionDto;
import com.get.permissioncenter.service.RInstitutionPermissionGroupInstitutionService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 学校权限组别与学校关联表(RInstitutionPermissionGroupInstitution)表控制层
 */
@RestController
@RequestMapping("permission/getInstitutionPermissionGroupInstitution")
@ApiOperation(value = "学校权限组别与学校关联表")
public class RInstitutionPermissionGroupInstitutionController{

    @Resource
    private RInstitutionPermissionGroupInstitutionService rInstitutionPermissionGroupInstitutionService;

@ApiOperation(value = "查询接口", notes = "查询学校权限和学校")
@OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/学校权限管理/查询学校权限组别与学校关系")
@PostMapping("selectInstitutionPermissionGroupInstitution")
public ResponseBo getInstitutionPermissionGroupInstitution(@RequestBody SearchBean<InstitutionQueryDto> page) {
    return rInstitutionPermissionGroupInstitutionService.dataList(page);
}

    /**
     * 新增数据
     *
     * @param rInstitutionPermissionGroupInstitutionDto 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "新增数据", notes = "新增数据")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/学校权限管理/新增学校权限组别与学校关系")
    @PostMapping("add")
    public ResponseBo insert(@RequestBody RInstitutionPermissionGroupInstitutionDto rInstitutionPermissionGroupInstitutionDto) {
        return new ResponseBo<>(rInstitutionPermissionGroupInstitutionService.addMInstitutionPermissionGroupAndInstitution(rInstitutionPermissionGroupInstitutionDto));
    }

    /**
     * 删除数据
     *
     * @param id 主键
     * @return 删除结果
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DELETE, description = "权限中心/学校权限管理/删除学校权限组别和学校关联")
    @GetMapping("delete")
    public ResponseBo delete(@RequestParam("id") Long id) {
        rInstitutionPermissionGroupInstitutionService.removeById(id);
        return ResponseBo.ok();
    }
}

