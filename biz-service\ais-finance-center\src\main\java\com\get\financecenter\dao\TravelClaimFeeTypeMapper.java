package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.financecenter.dto.TravelClaimFeeTypeDto;
import com.get.financecenter.entity.TravelClaimFeeType;
import com.get.financecenter.vo.TravelClaimFeeTypeVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TravelClaimFeeTypeMapper extends BaseMapper<TravelClaimFeeType> {


    Integer selectByTypeName(@Param("typeName") String typeName);

    List<TravelClaimFeeTypeVo> getTravelClaimFeeTypes(IPage<TravelClaimFeeType> pages, @Param("travelClaimFeeTypeDto") TravelClaimFeeTypeDto travelClaimFeeTypeDto);

    Integer getMaxViewOrder();

    int checkName(@Param("typeName") String typeName);

    void updateBatchById(@Param("updateList") List<TravelClaimFeeType> updateList);

    TravelClaimFeeType selectByFkAccountingItemId(Long id);
}