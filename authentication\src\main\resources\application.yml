# 在使用Spring默认数据源Hikari的情况下配置以下配置项
spring:
  datasource:
    hikari:
      # 自动提交从池中返回的连接
      auto-commit: true
      # 连接池中维护的最小空闲连接数
      minimum-idle: 10
      # 连接池中允许的最大连接数。缺省值：10；推荐的公式：((core_count * 2) + effective_spindle_count)
      maximum-pool-size: 60
      # 空闲连接超时时间，默认值600000（10分钟），大于等于max-lifetime且max-lifetime>0，会被重置为0；不等于0且小于10秒，会被重置为10秒。
      # 只有空闲连接数大于最大连接数且空闲时间超过该值，才会被释放
      idle-timeout: 30000
      # 连接最大存活时间.不等于0且小于30秒，会被重置为默认值30分钟.设置应该比mysql设置的超时时间短
      max-lifetime: 1800000
      # 等待连接池分配连接的最大时长（毫秒），超过这个时长还没可用的连接则发生SQLException， 缺省:30秒
      connection-timeout: 30000
      # 连接测试查询
      connection-test-query: select 1
      #connection-test-query: select 1 from dual
captcha:
  # 如果项目中使用到了redis，滑块验证码会自动把验证码数据存到redis中， 这里配置redis的key的前缀,默认是captcha:slider
  prefix: captcha
  # 验证码过期时间，默认是2分钟,单位毫秒， 可以根据自身业务进行调整
  expire:
    # 默认缓存时间 30s
    default: 30000
    # 针对 点选验证码 过期时间设置为 2分钟， 因为点选验证码验证比较慢，把过期时间调整大一些
    WORD_IMAGE_CLICK: 20000
  # 使用加载系统自带的资源， 默认是 false
  init-default-resource: false
  cache:
    # 缓存控制， 默认为false不开启
    enabled: true
    # 验证码会提前缓存一些生成好的验证数据， 默认是20
    cacheSize: 20
    # 缓存拉取失败后等待时间 默认是 5秒钟
    wait-time: 5000
    # 缓存检查间隔 默认是2秒钟
    period: 2000
    secondary:
      # 二次验证， 默认false 不开启
      enabled: false
      # 二次验证过期时间， 默认 2分钟
      expire: 120000
      # 二次验证缓存key前缀，默认是 captcha:secondary
      keyPrefix: "captcha:secondary"
#swagger文档
swagger:
  base-packages:
    - com.get
    - org.springframework.security.oauth2.provider.endpoint
