<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.workflowcenter.mapper.ActReProcdefMapper">
  <resultMap id="BaseResultMap" type="com.get.workflowcenter.entity.ActReProcdef">
    <id column="ID_" jdbcType="VARCHAR" property="id" />
    <result column="REV_" jdbcType="INTEGER" property="rev" />
    <result column="CATEGORY_" jdbcType="VARCHAR" property="category" />
    <result column="NAME_" jdbcType="VARCHAR" property="name" />
    <result column="KEY_" jdbcType="VARCHAR" property="key" />
    <result column="VERSION_" jdbcType="INTEGER" property="version" />
    <result column="DEPLOYMENT_ID_" jdbcType="VARCHAR" property="deploymentId" />
    <result column="RESOURCE_NAME_" jdbcType="VARCHAR" property="resourceName" />
    <result column="DGRM_RESOURCE_NAME_" jdbcType="VARCHAR" property="dgrmResourceName" />
    <result column="DESCRIPTION_" jdbcType="VARCHAR" property="description" />
    <result column="HAS_START_FORM_KEY_" jdbcType="TINYINT" property="hasStartFormKey" />
    <result column="HAS_GRAPHICAL_NOTATION_" jdbcType="TINYINT" property="hasGraphicalNotation" />
    <result column="SUSPENSION_STATE_" jdbcType="INTEGER" property="suspensionState" />
    <result column="TENANT_ID_" jdbcType="VARCHAR" property="tenantId" />
    <result column="ENGINE_VERSION_" jdbcType="VARCHAR" property="engineVersion" />
  </resultMap>
  <sql id="Base_Column_List">
    ID_, REV_, CATEGORY_, NAME_, KEY_, VERSION_, DEPLOYMENT_ID_, RESOURCE_NAME_, DGRM_RESOURCE_NAME_, 
    DESCRIPTION_, HAS_START_FORM_KEY_, HAS_GRAPHICAL_NOTATION_, SUSPENSION_STATE_, TENANT_ID_, 
    ENGINE_VERSION_
  </sql>

  <insert id="insertSelective" parameterType="com.get.workflowcenter.entity.ActReProcdef" keyProperty="id" useGeneratedKeys="true">
    insert into ACT_RE_PROCDEF
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID_,
      </if>
      <if test="rev != null">
        REV_,
      </if>
      <if test="category != null">
        CATEGORY_,
      </if>
      <if test="name != null">
        NAME_,
      </if>
      <if test="key != null">
        KEY_,
      </if>
      <if test="version != null">
        VERSION_,
      </if>
      <if test="deploymentId != null">
        DEPLOYMENT_ID_,
      </if>
      <if test="resourceName != null">
        RESOURCE_NAME_,
      </if>
      <if test="dgrmResourceName != null">
        DGRM_RESOURCE_NAME_,
      </if>
      <if test="description != null">
        DESCRIPTION_,
      </if>
      <if test="hasStartFormKey != null">
        HAS_START_FORM_KEY_,
      </if>
      <if test="hasGraphicalNotation != null">
        HAS_GRAPHICAL_NOTATION_,
      </if>
      <if test="suspensionState != null">
        SUSPENSION_STATE_,
      </if>
      <if test="tenantId != null">
        TENANT_ID_,
      </if>
      <if test="engineVersion != null">
        ENGINE_VERSION_,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="rev != null">
        #{rev,jdbcType=INTEGER},
      </if>
      <if test="category != null">
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="key != null">
        #{key,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="deploymentId != null">
        #{deploymentId,jdbcType=VARCHAR},
      </if>
      <if test="resourceName != null">
        #{resourceName,jdbcType=VARCHAR},
      </if>
      <if test="dgrmResourceName != null">
        #{dgrmResourceName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="hasStartFormKey != null">
        #{hasStartFormKey,jdbcType=TINYINT},
      </if>
      <if test="hasGraphicalNotation != null">
        #{hasGraphicalNotation,jdbcType=TINYINT},
      </if>
      <if test="suspensionState != null">
        #{suspensionState,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="engineVersion != null">
        #{engineVersion,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getProcefList" resultType="com.get.workflowcenter.vo.ActReProcdefVo">
    select a.ID_,a.REV_,a.CATEGORY_,a.NAME_,a.KEY_,a.VERSION_,a.DEPLOYMENT_ID_,a.RESOURCE_NAME_,a.DGRM_RESOURCE_NAME_,
           a.DESCRIPTION_,a.HAS_START_FORM_KEY_,a.HAS_GRAPHICAL_NOTATION_,a.SUSPENSION_STATE_,a.TENANT_ID_,a.ENGINE_VERSION_
    from ACT_RE_PROCDEF a
    inner join
    (select KEY_,max(VERSION_)VERSION_,TENANT_ID_  from ACT_RE_PROCDEF where TENANT_ID_ is not null group by KEY_,TENANT_ID_)b
     on a.KEY_=b.KEY_ and a.VERSION_=b.VERSION_ and a.TENANT_ID_=b.TENANT_ID_
    <where>
      <if test="procdefVo.mode != null and procdefVo.mode == 1" >
        and  a.TENANT_ID_ = ''
      </if>
<!--      <if test="procdefVo.mode != null and procdefVo.mode != 1" >-->
<!--        and  a.TENANT_ID_ != ''-->
<!--      </if>-->
      <if test="procdefVo.key != null and procdefVo.key != ''" >
        and  position(#{procdefVo.key} in a.KEY_)
      </if>
      <if test="procdefVo.deploymentIds != null and procdefVo.deploymentIds.size() > 0" >
        and a.DEPLOYMENT_ID_ IN
        <foreach collection="procdefVo.deploymentIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="procdefVo.name != null and procdefVo.name != ''" >
        and  position(#{procdefVo.name} in a.NAME_)
      </if>
    </where>
    order by a.KEY_
  </select>

</mapper>