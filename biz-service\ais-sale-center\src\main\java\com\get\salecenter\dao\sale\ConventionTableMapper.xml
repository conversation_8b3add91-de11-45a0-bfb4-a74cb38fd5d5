<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ConventionTableMapper">
  <resultMap id="BaseResultMap" type="com.get.salecenter.entity.ConventionTable">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_convention_id" jdbcType="BIGINT" property="fkConventionId" />
    <result column="fk_table_type_key" jdbcType="VARCHAR" property="fkTableTypeKey" />
    <result column="pre_num" jdbcType="VARCHAR" property="preNum" />
    <result column="table_num" jdbcType="VARCHAR" property="tableNum" />
    <result column="seat_count" jdbcType="INTEGER" property="seatCount" />
    <result column="is_vip" jdbcType="INTEGER" property="isVip" />
    <result column="view_order" jdbcType="INTEGER" property="viewOrder" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>

  <insert id="insertSelective" parameterType="com.get.salecenter.entity.ConventionTable" keyProperty="id" useGeneratedKeys="true">
    insert into m_convention_table
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkConventionId != null">
        fk_convention_id,
      </if>
      <if test="fkTableTypeKey != null">
        fk_table_type_key,
      </if>
      <if test="preNum != null">
        pre_num,
      </if>
      <if test="tableNum != null">
        table_num,
      </if>
      <if test="seatCount != null">
        seat_count,
      </if>
      <if test="isVip != null">
        is_vip,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkConventionId != null">
        #{fkConventionId,jdbcType=BIGINT},
      </if>
      <if test="fkTableTypeKey != null">
        #{fkTableTypeKey,jdbcType=VARCHAR},
      </if>
      <if test="preNum != null">
        #{preNum,jdbcType=VARCHAR},
      </if>
      <if test="tableNum != null">
        #{tableNum,jdbcType=VARCHAR},
      </if>
      <if test="seatCount != null">
        #{seatCount,jdbcType=INTEGER},
      </if>
      <if test="isVip != null">
        #{isVip,jdbcType=INTEGER},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getMaxTableNum"  resultType="com.get.salecenter.entity.ConventionTable">
    select
     table_num , view_order
    from
     m_convention_table
    where
     fk_convention_id= #{id}
	and
	 fk_table_type_key = #{tableTypeKey}
	and
	 pre_num = #{preNum}
    ORDER BY
     id
    desc
    LIMIT 1
  </select>

  <select id="getTableChartList" resultType="com.get.salecenter.vo.TableChartVo">
    SELECT
      a.id tableId,
      a.pre_num,
      a.view_order,
      a.seat_count seatCount,
      count( b.id ) count
    FROM
      ( SELECT id, pre_num, view_order, seat_count FROM m_convention_table WHERE fk_convention_id = #{conventionId} AND fk_table_type_key = #{tableType} ) a
      LEFT JOIN r_convention_table_person b ON a.id = b.fk_convention_table_id
    GROUP BY
      a.id
    ORDER BY
      pre_num,
      view_order

  </select>

  <select id="getTableNums" resultType="java.lang.String">
    SELECT
	 table_num
    FROM
	 m_convention_table
    WHERE
	 fk_convention_id = #{conventionId}
	AND fk_table_type_key = #{tableType}
    ORDER BY
	pre_num,
	view_order

  </select>
  
  <select id="getBootName" resultType="java.lang.String">
    SELECT
	 booth_name
    FROM
	 m_convention_registration
    WHERE
	 id = (
	    SELECT fk_convention_registration_id FROM r_convention_table_registration
	WHERE
	 fk_convention_table_id = (
	                        SELECT id FROM m_convention_table WHERE table_num = #{tableNum} AND fk_convention_id = #{conventionId} AND fk_table_type_key = #{tableType}
	                        )
	      )

  </select>

  <select id="conventionTableIsEmpty" parameterType="java.lang.Long" resultType="java.lang.Boolean">
    select IFNULL(max(id),0) id from m_convention_table where fk_convention_id = #{id} LIMIT 1

  </select>

  <select id="getTableCharacterSubtotal" resultType="com.get.salecenter.vo.TableCharacterSubtotal">
    SELECT mcp.type,COUNT(1) AS num FROM
      m_convention_table AS mct
        INNER JOIN r_convention_table_person AS rctp ON rctp.fk_convention_table_id = mct.id
        INNER JOIN m_convention_person AS mcp ON mcp.id = rctp.fk_convention_person_id
    WHERE mct.fk_convention_id = #{conventionTableDto.fkConventionId} AND mct.fk_table_type_key = #{conventionTableDto.fkTableTypeKey}
    <if test="conventionTableDto.tableNum != null and conventionTableDto.tableNum != '' ">
        AND  mct.table_num = #{conventionTableDto.tableNum}
    </if>
    <if test="conventionTableDto.boothName != null and conventionTableDto.boothName != '' ">
      AND EXISTS (
      SELECT 1 FROM m_convention_table AS mct2
      INNER JOIN r_convention_table_registration AS rctr2 ON rctr2.fk_convention_table_id = mct2.id
      INNER JOIN m_convention_registration AS mcr2 ON mcr2.id = rctr2.fk_convention_registration_id
      WHERE mcr2.fk_convention_id = #{conventionTableDto.fkConventionId}
      AND mcr2.booth_name = #{conventionTableDto.boothName}
      AND
      )
    </if>
    <if test="conventionTableDto.personName != null and conventionTableDto.personName != '' ">
      AND (
          REPLACE(mcp.name," ","") like #{conventionTableDto.personName}
      or
      mcp.name like #{conventionTableDto.personName}
      or
      mcp.name_chn like #{conventionTableDto.personName}
      or
      REPLACE(mcp.name_chn," ","") like #{conventionTableDto.personName}
      )
    </if>
    <if test="conventionTableDto.searchRepeatPerson != null and conventionTableDto.searchRepeatPerson == 1">
    AND EXISTS (
      SELECT
      a.fk_convention_person_id
      FROM
      r_convention_table_person a
      left JOIN m_convention_table b on a.fk_convention_table_id = b.id and b.fk_convention_id = #{conventionTableDto.fkConventionId} and b.fk_table_type_key = #{conventionTableDto.fkTableTypeKey}
      where b.id is not null
      GROUP BY a.fk_convention_person_id
      HAVING count(*) > 1
      AND a.fk_convention_person_id = mcp.id
      )
    </if>
    <if test="conventionTableDto.isFull != null">
      AND EXISTS (
        SELECT
        b.id, b.seat_count - COUNT(a.fk_convention_person_id) AS num
        FROM
        r_convention_table_person a
        INNER JOIN m_convention_table b on a.fk_convention_table_id = b.id
        GROUP BY b.id
      HAVING b.id = mct.id
    <if test="conventionTableDto.isFull">
        AND num = 0
    </if>
      <if test="!conventionTableDto.isFull">
        AND num != 0
      </if>
        )
    </if>
        GROUP BY mcp.type
  </select>

</mapper>