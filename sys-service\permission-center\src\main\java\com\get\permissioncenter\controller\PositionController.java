package com.get.permissioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.permissioncenter.dto.PositionDto;
import com.get.permissioncenter.vo.CompanyParallelVo;
import com.get.permissioncenter.vo.PositionVo;
import com.get.permissioncenter.service.IPositionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/6/29
 * @TIME: 16:32
 **/


@Api(tags = "职位管理")
@RestController
@RequestMapping("permission/position")
public class PositionController {
    @Resource
    private IPositionService positionService;

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "公司层级数据结构")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/职位管理/公司结构树")
    @GetMapping("/getCompanyParallelDto")
    public ResponseBo<CompanyParallelVo> getCompanyParallelDto(@RequestParam(value = "showStaff", required = false) Boolean showStaff,
                                                               @RequestParam(name = "companyId", required = false) Long companyId) {
        List<CompanyParallelVo> companyParallelVo = positionService.getCompanyParallelDto(showStaff, companyId);
        return new ListResponseBo<>(companyParallelVo);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.permissioncenter.vo.PositionVo>
     * @Description: 列表数据
     * @Param [voSearchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/公司管理/查询公司")
    @PostMapping("getPositions")
    public ResponseBo<PositionVo> getPositions(@RequestBody SearchBean<PositionDto> voSearchBean) {
        List<PositionVo> positions = positionService.getPositions(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(positions, page);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.permissioncenter.vo.PositionVo>
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "权限中心/职位管理/参数详情")
    @GetMapping("/detail/{id}")
    public ResponseBo<PositionVo> detail(@PathVariable("id") Long id) {
        PositionVo positionVo = positionService.findPositionById(id);
        return new ResponseBo<>(positionVo);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 批量新增信息
     * @Param [positionDtoList]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量新增接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/职位管理/新增参数")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(PositionDto.Add.class) ValidList<PositionDto> positionDtoList) {
        positionService.batchAddPosition(positionDtoList);
        return SaveResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.permissioncenter.vo.PositionVo>
     * @Description: 修改信息
     * @Param [positionDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/职位管理/更新参数")
    @PostMapping("update")
    public ResponseBo<PositionVo> update(@RequestBody @Validated(PositionDto.Update.class) PositionDto positionDto) {
        return UpdateResponseBo.ok(positionService.updatePositionVo(positionDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.DELETE, description = "权限中心/职位管理/删除资源")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        positionService.delete(id);
        return ResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 上移下移
     * @Param [departmentVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/职位管理/排序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<PositionDto> departmentVos) {
        positionService.movingOrder(departmentVos);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 职位下拉框数据
     * @Param [id]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "职位下拉框数据", notes = "id公司id")
    @PostMapping("getPositionSelect")
    public ResponseBo<BaseSelectEntity> getPositionSelect(@RequestParam("companyId") Long companyId,
                                                          @RequestParam("departmentId") Long departmentId) {
        List<BaseSelectEntity> datas = positionService.getPositionSelect(companyId, departmentId);
        return new ListResponseBo<>(datas);
    }

    /**
     * @Description :feign调用 通过员工ids 查找对应的职位编号
     * @Param [ids]
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping(value = "getPositionNumAndStaffIdMap")
    public Map<String, List<Long>> getPositionNumAndStaffIdMap(@RequestBody Set<Long> ids) {
        return positionService.getPositionNumAndStaffIdMap(ids);
    }

}
