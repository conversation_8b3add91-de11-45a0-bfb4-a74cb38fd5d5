package com.get.financecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: Hardy
 * @create: 2022/1/5 11:52
 * @verison: 1.0
 * @description:
 */
@Data
public class InvoiceReceivablePlanDto  extends BaseVoEntity {
    /**
     * 发票Id
     */
    @ApiModelProperty(value = "发票Id")
    private Long fkInvoiceId;

    /**
     * 应收计划Id
     */
    @ApiModelProperty(value = "应收计划Id")
    private Long fkReceivablePlanId;

    /**
     * 发票绑定金额
     */
    @ApiModelProperty(value = "发票绑定金额")
    private BigDecimal amount;

}
