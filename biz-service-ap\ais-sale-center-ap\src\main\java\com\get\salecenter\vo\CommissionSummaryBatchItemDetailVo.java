package com.get.salecenter.vo;

import com.get.financecenter.entity.PayablePlanSettlementBatchExchange;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 财务佣金汇总批次子项列表详情回显
 *
 * <AUTHOR>
 * @date 2021/12/27 17:19
 */
@Data
public class CommissionSummaryBatchItemDetailVo {

    @ApiModelProperty(value = "状态： 0:外币第一弹框 1：展示第一弹框 2：展示第二弹框 3：展示第三弹框 4：只允许下载")
    private int state;

//    @ApiModelProperty(value = "币种组")
//    private List<String> currencyList;

    @ApiModelProperty(value = "汇率")
    private List<PayablePlanSettlementBatchExchangeVo> commissionSummaryBatchItemDtoList;

    @ApiModelProperty(value = "第一步骤汇率")
    private List<PayablePlanSettlementBatchExchange> commissionSummaryBatchOneItemDtoList;

    @ApiModelProperty(value = "第二步骤汇率")
    private List<PayablePlanSettlementBatchExchange> commissionSummaryBatchTwoItemDtoList;
}
