<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.resumecenter.dao.ResumeWorkMapper">
    <resultMap id="BaseResultMap" type="com.get.resumecenter.entity.ResumeWork">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_resume_id" jdbcType="BIGINT" property="fkResumeId"/>
        <result column="fk_industry_type_id" jdbcType="BIGINT" property="fkIndustryTypeId"/>
        <result column="start_date" jdbcType="DATE" property="startDate"/>
        <result column="end_date" jdbcType="DATE" property="endDate"/>
        <result column="company" jdbcType="VARCHAR" property="company"/>
        <result column="position" jdbcType="VARCHAR" property="position"/>
        <result column="department" jdbcType="VARCHAR" property="department"/>
        <result column="company_nature" jdbcType="VARCHAR" property="companyNature"/>
        <result column="company_size" jdbcType="VARCHAR" property="companySize"/>
        <result column="working_type" jdbcType="VARCHAR" property="workingType"/>
        <result column="subordinate_count" jdbcType="INTEGER" property="subordinateCount"/>
        <result column="is_abroad_experience" jdbcType="BIT" property="isAbroadExperience"/>
        <result column="job_description" jdbcType="VARCHAR" property="jobDescription"/>
        <result column="main_achievement" jdbcType="VARCHAR" property="mainAchievement"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, fk_resume_id, fk_industry_type_id, start_date, end_date, company, position, department, 
    company_nature, company_size, working_type, subordinate_count, is_abroad_experience, 
    job_description, main_achievement, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
  </sql>

    <insert id="insertSelective" parameterType="com.get.resumecenter.entity.ResumeWork" keyProperty="id"
            useGeneratedKeys="true">
        insert into m_resume_work
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkResumeId != null">
                fk_resume_id,
            </if>
            <if test="fkIndustryTypeId != null">
                fk_industry_type_id,
            </if>
            <if test="startDate != null">
                start_date,
            </if>
            <if test="endDate != null">
                end_date,
            </if>
            <if test="company != null">
                company,
            </if>
            <if test="position != null">
                position,
            </if>
            <if test="department != null">
                department,
            </if>
            <if test="companyNature != null">
                company_nature,
            </if>
            <if test="companySize != null">
                company_size,
            </if>
            <if test="workingType != null">
                working_type,
            </if>
            <if test="subordinateCount != null">
                subordinate_count,
            </if>
            <if test="isAbroadExperience != null">
                is_abroad_experience,
            </if>
            <if test="jobDescription != null">
                job_description,
            </if>
            <if test="mainAchievement != null">
                main_achievement,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkResumeId != null">
                #{fkResumeId,jdbcType=BIGINT},
            </if>
            <if test="fkIndustryTypeId != null">
                #{fkIndustryTypeId,jdbcType=BIGINT},
            </if>
            <if test="startDate != null">
                #{startDate,jdbcType=DATE},
            </if>
            <if test="endDate != null">
                #{endDate,jdbcType=DATE},
            </if>
            <if test="company != null">
                #{company,jdbcType=VARCHAR},
            </if>
            <if test="position != null">
                #{position,jdbcType=VARCHAR},
            </if>
            <if test="department != null">
                #{department,jdbcType=VARCHAR},
            </if>
            <if test="companyNature != null">
                #{companyNature,jdbcType=VARCHAR},
            </if>
            <if test="companySize != null">
                #{companySize,jdbcType=VARCHAR},
            </if>
            <if test="workingType != null">
                #{workingType,jdbcType=VARCHAR},
            </if>
            <if test="subordinateCount != null">
                #{subordinateCount,jdbcType=INTEGER},
            </if>
            <if test="isAbroadExperience != null">
                #{isAbroadExperience,jdbcType=BIT},
            </if>
            <if test="jobDescription != null">
                #{jobDescription,jdbcType=VARCHAR},
            </if>
            <if test="mainAchievement != null">
                #{mainAchievement,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="isExistByIndustryTypeId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id from m_resume_work where fk_industry_type_id=#{industryTypeId}
    </select>
</mapper>