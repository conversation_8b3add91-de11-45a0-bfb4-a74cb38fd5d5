package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.vo.AssignConventionOperatePersonListVo;
import com.get.salecenter.vo.ConventionVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.entity.Convention;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.ConventionDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/6/30 17:37
 * @verison: 1.0
 * @description: 峰会管理业务接口
 */
public interface IConventionService extends IService<Convention> {

    /**
     * 详情
     *
     * @param id
     * @return
     */
    ConventionVo findConventionById(Long id);

    /**
     * 获取ID
     * @param id
     * @return
     */
    Convention getConventionById(Long id);


    /**
     * 新增
     *
     * @param conventionDto
     * @return
     */
    Long addConvention(ConventionDto conventionDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 修改
     *
     * @param conventionDto
     * @return
     */
    ConventionVo updateConvention(ConventionDto conventionDto);

    /**
     * 列表
     *
     * @param conventionDto
     * @param page
     * @return
     */
    List<ConventionVo> getConventions(ConventionDto conventionDto, Page page);

    /**
     * 保存文件   String tableName = "m_convention"
     *
     * @param mediaAttachedVo
     * @return
     * @
     */
    MediaAndAttachedVo addConventionMedia(MediaAndAttachedDto mediaAttachedVo);

    /**
     * 获取峰会附件类型
     *
     * @return
     */
    List<Map<String, Object>> findMediaAndAttachedType();

    /**
     * 该峰会主题名称
     *
     * @param id
     * @return
     * @
     */
    String getConventionNameById(Long id);

    /**
     * 根据IDS峰会主题名称
     * <AUTHOR>
     * @DateTime 2023/4/13 15:29
     */
    Map<Long,String> getConventionNameByIds(Set<Long> ids);

    /**
     * 关联活动下拉框(百度式搜索)
     *
     * @param companyId
     * @param eventName
     * @return
     */
    List<BaseSelectEntity> getEventListByName(Long companyId, String eventName);

    /**
     * 查询复制峰会活动峰会列表
     * @param conventionCopyDto
     * @return
     */
    List<Convention> getEventCopyList(ConventionCopyDto conventionCopyDto);

    /**
     * 分配峰会操作人员权限
     * @param assignConventionOperatePersonDto
     */
    void assignConventionOperatePerson(AssignConventionOperatePersonDto assignConventionOperatePersonDto);

    /**
     * 分配峰会操作人员列表
     * @param assignConventionOperatePersonListDto
     * @return
     */
    List<AssignConventionOperatePersonListVo> getAssignConventionOperatePersonList(AssignConventionOperatePersonListDto assignConventionOperatePersonListDto);

    /**
     * 删除峰会操作人员
     * @param id
     */
    void deleteAssignConventionOperatePerson(Long id);

    /**
     * 登陆人是否有操作峰会权限
     * @param conventionId
     * @return
     */
    Boolean hasConventionOperatePermission(Long conventionId);

    /**
     * 获取有权限配置的峰会
     * @param companyId
     * @return
     */
    List<BaseSelectEntity> getConventionWithPermissionConfigSelect(Long companyId);

    /**
     * 复制峰会操作人员配置
     * @param sourceConventionId
     * @param targetConventionId
     */
    void copyConventionOperatePersonConfig(Long sourceConventionId, Long targetConventionId);

    /**
     * 活动下拉
     * @param companyId
     * @return
     */
    List<BaseSelectEntity> getEventSelect(Long companyId);
}
