package com.get.permissioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.dao.StaffContractMapper;
import com.get.permissioncenter.dao.StaffMapper;
import com.get.permissioncenter.dto.CommentDto;
import com.get.permissioncenter.dto.MediaAndAttachedDto;
import com.get.permissioncenter.dto.StaffContractDto;
import com.get.permissioncenter.vo.CommentVo;
import com.get.permissioncenter.vo.MediaAndAttachedVo;
import com.get.permissioncenter.vo.StaffContractVo;
import com.get.permissioncenter.entity.Staff;
import com.get.permissioncenter.entity.StaffContract;
import com.get.permissioncenter.service.ICommentService;
import com.get.permissioncenter.service.IConfigService;
import com.get.permissioncenter.service.IMediaAndAttachedService;
import com.get.permissioncenter.service.IStaffContractService;
import com.get.remindercenter.feign.IReminderCenterClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/7/7
 * @TIME: 15:20
 * @Description: 合同管理
 **/
@Service
public class StaffContractServiceImpl extends BaseServiceImpl<StaffContractMapper, StaffContract> implements IStaffContractService {
    @Resource
    private StaffContractMapper contractMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private ICommentService commentService;
    @Resource
    private StaffMapper staffMapper;
    @Resource
    private IReminderCenterClient reminderCenterClient;
    @Resource
    private IConfigService configService;

    @Override
    public StaffContractVo getStaffContractById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
//        StaffContract staffContract = contractMapper.selectByPrimaryKey(id);
        StaffContract staffContract = contractMapper.selectById(id);
        StaffContractVo contractDto = BeanCopyUtils.objClone(staffContract, StaffContractVo::new);
        if (GeneralTool.isEmpty(contractDto)) {
            return null;
        }
        setMedia(id, contractDto);
        contractDto.setStaffName(staffMapper.getStaffNameById(contractDto.getFkStaffId()));
        return contractDto;
    }

    private void setMedia(Long id, StaffContractVo contractDto) {
        MediaAndAttachedDto attachedVo = new MediaAndAttachedDto();
        attachedVo.setFkTableName(TableEnum.PERMISSION_CONTRACT.key);
        attachedVo.setFkTableId(id);
        List<MediaAndAttachedVo> mediaAndAttachedVo = attachedService.getMediaAndAttachedDto(attachedVo);
        contractDto.setMediaAndAttachedDtos(mediaAndAttachedVo);
    }

    @Override
    public Long addStaffContract(StaffContractDto staffContractDto) {
        if (GeneralTool.isEmpty(staffContractDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        StaffContract staffContract = BeanCopyUtils.objClone(staffContractDto, StaffContract::new);
        utilService.updateUserInfoToEntity(staffContract);
        contractMapper.insertSelective(staffContract);

        //新增合同时判断员工的薪资生效时间是否为空，为空则将新增的合同起始时间赋值过去
        Staff staff = staffMapper.selectById(staffContractDto.getFkStaffId());
        if (GeneralTool.isNotEmpty(staff) && GeneralTool.isEmpty(staff.getSalaryEffectiveDate())) {
            staff.setSalaryEffectiveDate(staffContractDto.getStartTime());
            utilService.updateUserInfoToEntity(staff);
            staffMapper.updateById(staff);
        }

//        Long fkCompanyId = staff.getFkCompanyId();
//        ConfigVo configDto = configService.getConfigByKey(ProjectKeyEnum.REMINDER_EMAIL_CONTRACT_EXPIRATION.key);
//        Long days = 0L;
//        if (GeneralTool.isNotEmpty(configDto)) {
//            String value1 = configDto.getValue1();
//            JSONObject value1JsonObject = JSON.parseObject(value1);
//            if (fkCompanyId == 2L){
//                days = value1JsonObject.getLong("GEA");
//            }else if (fkCompanyId == 3L){
//                days = value1JsonObject.getLong("IAE");
//            }else {
//                days = value1JsonObject.getLong("OTHER");
//            }
//        }


//        String staffFullName = Stream.of(staff.getName(), staff.getNameEn()).filter(Objects::nonNull).collect(Collectors.joining("，"));
//        //提醒
//        if (GeneralTool.isNotEmpty(staffContract.getIsActive()) && staffContract.getIsActive() && days>0L && GeneralTool.isNotEmpty(staff.getIsOnDuty()) && staff.getIsOnDuty()) {
//            //激活的合同需要新增提醒
//            List<RemindTaskDto> remindTaskVos = new ArrayList<>();
//            RemindTaskDto remindTaskVo = new RemindTaskDto();
//            remindTaskVo.setFkStaffId(1L);
//            remindTaskVo.setFkRemindEventTypeKey(ProjectKeyEnum.STAFF_CONTRACT_EXPIRE.key);
//            Date endTime = staffContract.getEndTime();
//            Date minTimeByDay = GetDateUtil.getMinTimeByDay(endTime);
//            //提前40天提醒，获取的时间为任务的开始时间
//            Date startTime = GetDateUtil.getAdvanceDateByDay(minTimeByDay, days);
//            remindTaskVo.setStartTime(startTime);
//            remindTaskVo.setAdvanceDays("0");
//            remindTaskVo.setTaskBgColor("#3788d8");
//            remindTaskVo.setRemindMethod("1");
//            remindTaskVo.setStatus(1);
//            remindTaskVo.setTaskTitle("【"+staffFullName+"】员工到期合同提醒，合同到期日："+new SimpleDateFormat("yyyy-MM-dd").format(endTime));
//            remindTaskVo.setFkTableName(TableEnum.PERMISSION_CONTRACT.key);
//            remindTaskVo.setFkTableId(staffContract.getId());
//            remindTaskVos.add(remindTaskVo);
//            reminderCenterClient.batchAdd(remindTaskVos);
//        }
        return staffContract.getId();
    }

    @Override
    public StaffContractVo updateStaffContractVo(StaffContractDto staffContractDto) {
        if (GeneralTool.isEmpty(staffContractDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        //获取修改前的激活状态
        StaffContract data = contractMapper.selectById(staffContractDto.getId());
        if (GeneralTool.isNotEmpty(data)) {
            //判断当前操作是否为激活操作
            if (!data.getIsActive() && staffContractDto.getIsActive()) {
                //修改员工薪资生效时间为当前激活合同开始时间
                staffMapper.updateSalaryEffectiveDate(staffContractDto.getFkStaffId(), staffContractDto.getStartTime());
            }
        }
        StaffContract staffContract = BeanCopyUtils.objClone(staffContractDto, StaffContract::new);
//        utilService.setUpdateInfo(staffContract);
//        contractMapper.updateByPrimaryKeySelective(staffContract);
        utilService.updateUserInfoToEntity(staffContract);
        contractMapper.updateById(staffContract);
//        Staff staff = staffMapper.selectById(staffContractDto.getFkStaffId());
//        String staffFullName = Stream.of(staff.getName(), staff.getNameEn()).filter(Objects::nonNull).collect(Collectors.joining("，"));
//
//        Long fkCompanyId = staff.getFkCompanyId();
//        ConfigVo configDto = configService.getConfigByKey(ProjectKeyEnum.REMINDER_EMAIL_CONTRACT_EXPIRATION.key);
//        Long days = 0L;
//        if (GeneralTool.isNotEmpty(configDto)) {
//            String value1 = configDto.getValue1();
//            JSONObject value1JsonObject = JSON.parseObject(value1);
//            if (fkCompanyId == 2L){
//                days = value1JsonObject.getLong("GEA");
//            }else if (fkCompanyId == 3L){
//                days = value1JsonObject.getLong("IAE");
//            }else {
//                days = value1JsonObject.getLong("OTHER");
//            }
//        }

        //获取该人员的所有合同
//        List<StaffContract> staffContracts = contractMapper.selectList(Wrappers.<StaffContract>query().lambda().eq(StaffContract::getFkStaffId, staffContract.getFkStaffId()));
//        if (GeneralTool.isNotEmpty(staffContracts)) {
//            Set<Long> fkTableIds = new HashSet<>();
//            List<RemindTaskDto> remindTaskVos = new ArrayList<>();
//            RemindTaskDto remindTaskVo = null;
//            //获取行政部的所有人员
//            List<Long> adminDepartmentStaff = staffMapper.getAdminDepartmentStaff();
//            for (StaffContract contract : staffContracts) {
//                fkTableIds.add(contract.getId());
//                if (GeneralTool.isNotEmpty(contract.getIsActive()) && contract.getIsActive() && days>0L && GeneralTool.isNotEmpty(staff.getIsOnDuty()) && staff.getIsOnDuty()) {
//                    //激活的合同需要新增提醒
//                    remindTaskVo = new RemindTaskDto();
//                    remindTaskVo.setFkStaffId(1L);
//                    remindTaskVo.setFkRemindEventTypeKey(ProjectKeyEnum.STAFF_CONTRACT_EXPIRE.key);
//                    Date endTime = contract.getEndTime();
//                    Date minTimeByDay = GetDateUtil.getMinTimeByDay(endTime);
//                    //提前40天提醒，获取的时间为任务的开始时间
//                    Date startTime = GetDateUtil.getAdvanceDateByDay(minTimeByDay, days);
//                    remindTaskVo.setStartTime(startTime);
//                    remindTaskVo.setAdvanceDays("0");
//                    remindTaskVo.setTaskBgColor("#3788d8");
//                    remindTaskVo.setRemindMethod("1");
//                    remindTaskVo.setStatus(1);
//                    remindTaskVo.setTaskTitle("【"+staffFullName+"】员工到期合同提醒，合同到期日："+new SimpleDateFormat("yyyy-MM-dd").format(endTime));
//                    remindTaskVo.setFkTableName(TableEnum.PERMISSION_CONTRACT.key);
//                    remindTaskVo.setFkTableId(contract.getId());
//                    remindTaskVos.add(remindTaskVo);
//                }
//            }
//            reminderCenterClient.batchUpdateByTableIds(remindTaskVos, TableEnum.PERMISSION_CONTRACT.key, fkTableIds);
//        }
        return getStaffContractById(staffContract.getId());
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        int i = contractMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        //删除提醒
        Set<Long> fkTableIds = new HashSet<>();
        fkTableIds.add(id);
        reminderCenterClient.batchUpdateByTableIds(new ArrayList<>(), TableEnum.PERMISSION_CONTRACT.key, fkTableIds);
    }


    @Override
    public List<MediaAndAttachedVo> addContractMedia(List<MediaAndAttachedDto> mediaAndAttachedDtos) {
        if (GeneralTool.isEmpty(mediaAndAttachedDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAndAttachedDtos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.PERMISSION_CONTRACT.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }

    @Override
    public List<MediaAndAttachedVo> getContractMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.PERMISSION_CONTRACT.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);
    }

    @Override
    public List<StaffContractVo> getStaffContract(StaffContractDto contractVo, Page page) {
        if (GeneralTool.isEmpty(contractVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(contractVo.getFkStaffId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staff_id_null"));
        }
//        Example example = new Example(StaffContract.class);
//        Example.Criteria criteria = example.createCriteria();
//        Example.Criteria criteria1 = example.createCriteria();
//        criteria.andEqualTo("fkStaffId", contractVo.getFkStaffId());
//        if (GeneralTool.isNotEmpty(contractVo)) {
//            if (GeneralTool.isNotEmpty(contractVo.getKeyWord())) {
//                criteria.andLike("signingSalary", "%" + contractVo.getKeyWord() + "%");
//                criteria1.orLike("socialInsurancePlace", "%" + contractVo.getKeyWord() + "%");
//                criteria1.orLike("workplace", "%" + contractVo.getKeyWord() + "%");
//                example.and(criteria1);
//            }
//        }
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<StaffContract> staffContracts = contractMapper.selectByExample(example);
//        page.restPage(staffContracts);

//        List<StaffContractVo> collect =
////                staffContracts.stream().map(staffContract -> Tools.objClone(staffContract, StaffContractVo.class)).collect(Collectors.toList());
        LambdaQueryWrapper<StaffContract> wrapper = new LambdaQueryWrapper();

        if (GeneralTool.isNotEmpty(contractVo)) {
            if (GeneralTool.isNotEmpty(contractVo.getFkStaffId())) {
                wrapper.eq(StaffContract::getFkStaffId, contractVo.getFkStaffId());
            }
            if (GeneralTool.isNotEmpty(contractVo.getKeyWord())) {
                wrapper.and(wrapper_ ->
                        wrapper_.like(StaffContract::getSigningSalary, contractVo.getKeyWord()).or()
                                .like(StaffContract::getSocialInsurancePlace, contractVo.getKeyWord())
                                .like(StaffContract::getWorkplace, contractVo.getKeyWord()));
            }
        }
        IPage<StaffContract> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<StaffContract> staffContracts = pages.getRecords();
        List<StaffContractVo> collect = BeanCopyUtils.copyListProperties(staffContracts, StaffContractVo::new);
        if (GeneralTool.isNotEmpty(collect)) {
            for (StaffContractVo contractDto : collect) {
                contractDto.setStaffName(staffMapper.getStaffNameById(contractDto.getFkStaffId()));
            }
        }
        return collect;
    }

    @Override
    public Long editComment(CommentDto commentDto) {
        if (GeneralTool.isNotEmpty(commentDto)) {
            if (GeneralTool.isNotEmpty(commentDto.getId())) {
                commentDto.setFkTableName(TableEnum.PERMISSION_CONTRACT.key);
                commentService.updateComment(commentDto);
            } else {
                commentDto.setFkTableName(TableEnum.PERMISSION_CONTRACT.key);
                commentService.addComment(commentDto);
            }
        }
        return commentDto.getId();
    }

    @Override
    public List<StaffContract> getStaffContract(Set<Long> ids) {
        if (ids.isEmpty()) {
            return Collections.emptyList();
        }
        List<StaffContract> contracts = contractMapper.selectList(Wrappers.<StaffContract>lambdaQuery().in(StaffContract::getFkStaffId, ids));
        if (contracts.isEmpty()) {
            return Collections.emptyList();
        }
        List<StaffContract> result = new ArrayList<>();
        Map<Long, List<StaffContract>> collect = contracts.stream().collect(Collectors.groupingBy(StaffContract::getFkStaffId));
        for (Map.Entry<Long, List<StaffContract>> entry : collect.entrySet()) {
            List<StaffContract> value = entry.getValue();
            value.stream().min((o1, o2) -> o2.getStartTime().compareTo(o1.getStartTime())).ifPresent(result::add);
        }
        return result;
    }

    @Override
    public List<CommentVo> getComments(CommentDto commentDto, Page page) {
        commentDto.setFkTableName(TableEnum.PERMISSION_CONTRACT.key);
        return commentService.datas(commentDto, page);
    }

    @Override
    public StaffContract getStaffContractByStaffId(Long staffId) {
        if (GeneralTool.isEmpty(staffId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staff_id_null"));
        }
        return contractMapper.getStaffContractByStaffId(staffId);
    }
}
