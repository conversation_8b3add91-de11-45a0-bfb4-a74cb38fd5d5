package com.get.insurancecenter.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.consts.AESConstant;
import com.get.common.result.Page;
import com.get.common.utils.AESUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.insurancecenter.dto.card.CreditCardPageDto;
import com.get.insurancecenter.dto.order.MoveOrderDto;
import com.get.insurancecenter.entity.CreditCard;
import com.get.insurancecenter.entity.CreditCardReminderNotifier;
import com.get.insurancecenter.mapper.CreditCardMapper;
import com.get.insurancecenter.mapper.CreditCardReminderNotifierMapper;
import com.get.insurancecenter.service.CreditCardService;
import com.get.insurancecenter.utils.CreditCardDesensitizationUtil;
import com.get.insurancecenter.utils.EncryptionKeyUtils;
import com.get.insurancecenter.utils.SecureEncryptUtil;
import com.get.insurancecenter.vo.card.CreateCardPageVo;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.apache.shiro.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/7/25
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class CreditCardServiceImpl extends ServiceImpl<CreditCardMapper, CreditCard> implements CreditCardService {

    @Autowired
    private CreditCardMapper creditCardMapper;
    @Autowired
    private CreditCardReminderNotifierMapper reminderNotifierMapper;
    @Autowired
    private EncryptionKeyUtils encryptionKeyUtils;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    public Long saveCreditCard(CreditCard creditCard) {
        String secret = encryptionKeyUtils.getSecret();
        if (!StringUtils.hasText(secret)) {
            log.error("获取到的密钥为空");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "INSURANCE_SECRET_KEY_FAIL", "密钥获取失败"));
        }
        creditCard.setGmtModified(new Date());
        creditCard.setGmtModifiedUser(SecureUtil.getLoginId());
        //通过base64解密卡号
        String cardNum = AESUtils.Decrypt(creditCard.getBase64CardNum(), AESConstant.AESKEY);
        String safeCode = AESUtils.Decrypt(creditCard.getBase64SafetyCode(), AESConstant.AESKEY);
        //重新加密密钥
        String encryptCardNum = SecureEncryptUtil.encrypt(cardNum, secret);
        String encryptCode = SecureEncryptUtil.encrypt(safeCode, secret);
        creditCard.setCardNum(encryptCardNum);
        creditCard.setSafetyCode(encryptCode);

        // 设置 expirationDate 为该月最后一天
        if (Objects.nonNull(creditCard.getExpirationDate())) {
            creditCard.setExpirationDate(getLastDayOfMonth(creditCard.getExpirationDate()));
        }
        //新增
        if (Objects.isNull(creditCard.getId()) || creditCard.getId() < 1) {
            creditCard.setGmtCreate(new Date());
            creditCard.setGmtCreateUser(SecureUtil.getLoginId());
            creditCard.setCurrentAmount(creditCard.getQuotaAmount());
            creditCardMapper.insert(creditCard);
            creditCard.setViewOrder(creditCard.getId().intValue());
            creditCardMapper.updateById(creditCard);
            return creditCard.getId();
        }
        creditCardMapper.updateById(creditCard);
        return creditCard.getId();
    }

    @Override
    @SneakyThrows
    public CreditCard getCreditCardBaseInfo(Long id) {
        String secret = encryptionKeyUtils.getSecret();
        if (!StringUtils.hasText(secret)) {
            log.error("获取到的密钥为空");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "INSURANCE_SECRET_KEY_FAIL", "密钥获取失败"));
        }
        CreditCard creditCard = creditCardMapper.selectBaseInfoById(id);
        if (Objects.nonNull(creditCard)) {
            //解密卡号
            String decryptCardNum = SecureEncryptUtil.decrypt(creditCard.getCardNum(), secret);
            String decryptSafetyCode = SecureEncryptUtil.decrypt(creditCard.getSafetyCode(), secret);

            //安全码和卡号不回显
            creditCard.setSafetyCodePlaintext(Strings.EMPTY);
            creditCard.setCardNumPlaintext(Strings.EMPTY);
            //脱敏
            creditCard.setDesensitizationCardNumPlaintext(CreditCardDesensitizationUtil.maskCardNumber(decryptCardNum));
            creditCard.setDesensitizationSafetyCodePlaintext(CreditCardDesensitizationUtil.desensitizeShortString(decryptSafetyCode));

            //base64加密后的卡号和安全码
            String base64CardNum = AESUtils.Encrypt(decryptCardNum, AESConstant.AESKEY);
            String base64SafetyCode = AESUtils.Encrypt(decryptSafetyCode, AESConstant.AESKEY);
            creditCard.setBase64CardNum(base64CardNum);
            creditCard.setBase64SafetyCode(base64SafetyCode);
            return creditCard;
        }
        return null;
    }

    @Override
    @SneakyThrows
    public List<CreateCardPageVo> creditCardPage(CreditCardPageDto params, Page page) {
        String secret = encryptionKeyUtils.getSecret();
        if (!StringUtils.hasText(secret)) {
            log.error("获取到的密钥为空");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "INSURANCE_SECRET_KEY_FAIL", "密钥获取失败"));
        }
        IPage<CreateCardPageVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<CreateCardPageVo> result = creditCardMapper.selectCreateCardPage(pages, params);
        List<Long> createCardIds = result.stream().map(CreateCardPageVo::getId).collect(Collectors.toList());
        //批量查通知人
        List<CreditCardReminderNotifier> notifiers = reminderNotifierMapper.selectNotifierByCreditCardIds(CollectionUtils.isNotEmpty(createCardIds) ? createCardIds : Arrays.asList(0L));
        // 按信用卡ID分组通知人
        Map<Long, List<CreditCardReminderNotifier>> notifierMap = notifiers.stream()
                .collect(Collectors.groupingBy(CreditCardReminderNotifier::getCreditCardId));
        //填充卡号与通知人
        result.stream().forEach(vo -> {
            vo.setCurrentLoginUser(SecureUtil.getLoginId());
            try {
                String decryptCardNumber = SecureEncryptUtil.decrypt(vo.getCardNum(), secret);
                vo.setDesensitizationCardNumPlaintext(CreditCardDesensitizationUtil.maskCardNumber(decryptCardNumber));
            } catch (Exception e) {
                log.error("卡号解密失败:{}", e.getMessage());
//                throw new GetServiceException("卡号解密失败");
                throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "INSURANCE_CARD_DECRYPT_FAIL", "卡号解密失败"));
            }
            List<CreditCardReminderNotifier> notifierList = notifierMap.get(vo.getId());
            if (CollectionUtils.isNotEmpty(notifierList)) {
                vo.setNotifyPerson(notifierList.stream()
                        .map(CreditCardReminderNotifier::getName)
                        .filter(StringUtils::hasText)
                        .collect(Collectors.joining(",")));
            }
            vo.setCardNumPlaintext(Strings.EMPTY);
        });
        int totalCount = (int) pages.getTotal();
        Integer showCount = page.getShowCount();
        page.setTotalPage(page.getShowCount() != null && showCount > 0 ? totalCount % showCount == 0 ? totalCount / showCount : totalCount / showCount + 1 : 0);
        page.setTotalResult(totalCount);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(MoveOrderDto orderDto) {
        Integer start = orderDto.getStart();
        Integer end = orderDto.getEnd();
        LambdaQueryWrapper<CreditCard> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (end > start) {
            lambdaQueryWrapper.between(CreditCard::getViewOrder, start, end).orderByDesc(CreditCard::getViewOrder);
        } else {
            lambdaQueryWrapper.between(CreditCard::getViewOrder, end, start).orderByDesc(CreditCard::getViewOrder);
        }
        List<CreditCard> agentCommissionPlans = list(lambdaQueryWrapper);

        //从下上移 列表倒序排序
        List<CreditCard> updateList = Lists.newArrayList();
        if (end > start) {
            int finalEnd = end;
            List<CreditCard> sortedList = Lists.newArrayList();
            CreditCard policy = agentCommissionPlans.get(agentCommissionPlans.size() - 1);
            sortedList.add(policy);
            agentCommissionPlans.remove(agentCommissionPlans.size() - 1);
            sortedList.addAll(agentCommissionPlans);
            for (CreditCard agentCommissionPlan : sortedList) {
                agentCommissionPlan.setViewOrder(finalEnd);
                finalEnd--;
            }
            updateList.addAll(sortedList);
        } else {
            int finalStart = start;
            List<CreditCard> sortedList = Lists.newArrayList();
            CreditCard policy = agentCommissionPlans.get(0);
            agentCommissionPlans.remove(0);
            sortedList.addAll(agentCommissionPlans);
            sortedList.add(policy);
            for (CreditCard agentCommissionPlan : sortedList) {
                agentCommissionPlan.setViewOrder(finalStart);
                finalStart--;
            }
            updateList.addAll(sortedList);
        }

        if (GeneralTool.isNotEmpty(updateList)) {
            boolean batch = updateBatchById(updateList);
            if (!batch) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
    }


    public static Date getLastDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        // 设置为下月1号
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.MONTH, 1);
        // 回退一天就是本月最后一天
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        return calendar.getTime();
    }
}
