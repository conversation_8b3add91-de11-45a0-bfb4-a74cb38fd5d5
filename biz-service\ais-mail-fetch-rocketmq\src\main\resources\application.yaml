server:
  port: 1100  # 设置端口号
spring:
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    dynamic:
      #设置默认的数据源master
      primary: mail
      datasource:
        mail:
          url: ********************************************************************************************************************************************************************************************************************************************************************
          username: seluser
          password: HTIAIS_Seluser@328HK8Asts
        mail-doris:
          url: ********************************************************************************************************************************************************************************************************************************************************************
          username: root
          password: j41Psi9KcY2V0Ia5
        institution:
          url: *********************************************************************************************************************************
          username: root
          password: fzhmysql
        saledb-doris:
          url: **************************************************************************************************************************
          username: root
          password: fzhmysql
        file:
          username: root
          password: fzhmysql
          url: **************************************************************************************************************************************************************************************************************************************************************************************************


mail:
  exmail: imap.exmail.qq.com
  exmail-get-port: 993
  exmail-send: imap.exmail.qq.com
  exmail-send-port: 587
  exmail-inboxList: INBOX
  exmail-sentList: Sent Messages
  exmail-draftList: drafts
  exmail-deletedList: Deleted Messages

  gmail: imap.gmail.com
  gmail-inboxList: INBOX
  gmail-sentList: Sent Messages
  gmail-draftList: drafts
  gmail-deletedList: Deleted Messages

  163mail: imap.163.com
  163mail-get-port: 143
  163mail-send: smtp.163.com
  163mail-send-port: 465
  163mail-inboxList: INBOX
  163mail-sentList: 已发送
  163mail-draftList: 草稿箱
  163mail-deletedList: 已删除


  outlookmail: outlook.office365.com

  annex-save-path: /work/ais-email/
  up-annex-path: /work/ais-email/

#  annex-save-path: E:\work\gitwork\hti-java-ais\sys-service-ap\ais-mail-ap\src\main\resources\annex\
#  up-annex-path: E:\work\gitwork\hti-java-ais\sys-service-ap\ais-mail-ap\src\main\resources\annex\

encrypt:
  KEY: FZH8888888888888


mybatis:
  mapper-locations: ./mapper/*.xml

file:
  bucketName: s3demo
  local:
    enable: true
    base-path: /Users/<USER>/Downloads/img
  tencentcloudimage:
    secretId: AKIDvkNr8KK71757bv8GDygJoGgEfw0HNHza
    secretKey: lQ8oRyOk12uETcyCGILG0b8sgchpttiT
    apCity: ap-shanghai
    bucketname: hti-email-images-dev-1301376564

  tencentcloudfile:
    secretId: AKIDvkNr8KK71757bv8GDygJoGgEfw0HNHza
    secretKey: lQ8oRyOk12uETcyCGILG0b8sgchpttiT
    apCity: ap-shanghai
    bucketname: hti-email-files-dev-1301376564

rocketmq:
  # 配置 NameServer 地址
  name-server: 192.168.2.28:9876
  # 生产者分组
  producer:
    group: ais_mail
    # 发送超时时间（毫秒）
    send-message-timeout: 3000
    # 生产者发送失败的最大重试次数
    retry-times-when-send-failed: 3
  consumer:
    # 消费者分组
    group: ais_mail
    # 消息最大重试次数（超出后进入死信队列）
    max-reconsume-times: 3
    # 开启消息轨迹
    enable-msg-trace: true