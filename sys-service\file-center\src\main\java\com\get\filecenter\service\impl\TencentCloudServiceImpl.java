package com.get.filecenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.config.ConnectTencentCloud;
import com.get.filecenter.dto.SaleFileDto;
import com.get.filecenter.entity.*;
import com.get.filecenter.mapper.FileConventionMapper;
import com.get.filecenter.mapper.FileExamMapper;
import com.get.filecenter.mapper.FileFinanceMapper;
import com.get.filecenter.mapper.FileIbMapper;
import com.get.filecenter.mapper.FileInstitutionMapper;
import com.get.filecenter.mapper.FileIssueMapper;
import com.get.filecenter.mapper.FileMsoMapper;
import com.get.filecenter.mapper.FileOfficeMapper;
import com.get.filecenter.mapper.FilePermissionMapper;
import com.get.filecenter.mapper.FileResumeMapper;
import com.get.filecenter.mapper.FileSaleMapper;
import com.get.filecenter.mapper.FileVotingMapper;
import com.get.filecenter.service.ITencentCloudService;
import com.get.filecenter.vo.FileVo;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.model.Bucket;
import com.qcloud.cos.model.COSObject;
import com.qcloud.cos.model.COSObjectInputStream;
import com.qcloud.cos.model.CannedAccessControlList;
import com.qcloud.cos.model.CreateBucketRequest;
import com.qcloud.cos.model.GetObjectRequest;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.IOUtils;
import org.apache.http.entity.ContentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.List;

//import static com.get.filecenter.config.ConnectTencentCloud.cosClient;

@Service
public class TencentCloudServiceImpl implements ITencentCloudService {
    private Logger log = LoggerFactory.getLogger(this.getClass());
    @Value("${spring.tencentcloudimage.bucketname}")
    private String imageBucketName;
    @Value("${spring.tencentcloudfile.bucketname}")
    private String fileBucketName;
    //GET和IAE共享私密桶（也就是原来的GET私密桶）
    @Value("${spring.tencentCloudShareFile.bucketname}")
    private String shareBucketName;
    @Value("${spring.htitencentcloudimage.bucketname}")
    private String htiBucketName;
    @Resource
    private FileFinanceMapper fileFinanceMapper;
    @Resource
    private FileInstitutionMapper fileInstitutionMapper;
    @Resource
    private FilePermissionMapper filePermissionMapper;
    @Resource
    private FileResumeMapper fileResumeMapper;
    @Resource
    private FileSaleMapper fileSaleMapper;
    @Resource
    private FileExamMapper fileExamMapper;
    @Resource
    private FileMsoMapper fileMsoMapper;
    @Resource
    private FileOfficeMapper fileOfficeMapper;
    @Resource
    private FileVotingMapper fileVotingMapper;
    @Resource
    private FileIssueMapper fileIssueMapper;
    @Resource
    private FileConventionMapper fileConventionMapper;
    @Resource
    private FileIbMapper fileIbMapper;
    @Resource
    private UtilService utilService;

    /**
     * MultipartFile 转 File
     *
     * @param file
     * @throws Exception
     */
    public static File multipartFileToFile(MultipartFile file) throws Exception {

        File toFile = null;
        if (file.equals("") || file.getSize() <= 0) {
            file = null;
        } else {
            InputStream ins = null;
            ins = file.getInputStream();
            toFile = new File(file.getOriginalFilename());
            inputStreamToFile(ins, toFile);
            ins.close();
        }
        return toFile;
    }

    /**
     * 获取流文件
     * @param ins 传入文件字节流
     * @param file 返回的文件对象
     */
    private static void inputStreamToFile(InputStream ins, File file) {
        try {
            OutputStream os = new FileOutputStream(file);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            ins.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    static String subString(String url) {
        if (url.contains("target")) {
            url = url.replace("/data/project/get/file-center/target", "");
            url = url.replace("/data/project/get/app-file-center/target", "");
            return url;
        } else {
            return url;
        }

    }

//    @Override
//    public BufferedOutputStream downLoadObject(FileVo fileVo, HttpServletResponse response) {
//        try {
//            response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileVo.getFileNameOrc().getBytes("utf-8" ), "ISO-8859-1" ));
//        } catch (UnsupportedEncodingException e) {
//            e.printStackTrace();
//        }
//        BufferedOutputStream outputStream = null;
//        try {
//            GetObjectRequest getObjectRequest = new GetObjectRequest(fileBucketName, fileVo.getFileKey());
//            COSObject cosObject = ConnectTencentCloud.getCosClient().getObject(getObjectRequest);
//            COSObjectInputStream cosObjectInput = cosObject.getObjectContent();
//            // 缓冲文件输出流
//            try {
//                outputStream=new BufferedOutputStream(response.getOutputStream());
//                // 进行解码 为防止文件出现乱码 文件上传时进行编码处理
//                BASE64Decoder base64Decoder = new BASE64Decoder();
//
//                byte[] car=new byte[1024];
//                int L=0;
//                while((L=cosObjectInput.read(car))!=-1){
//                    car =  base64Decoder.decodeBuffer(cosObjectInput);
//                    //L 如果不给长度会有文件损坏
//                    outputStream.write(car, 0,L);
//
//                }
//                if(outputStream!=null){
//                    outputStream.flush();
//                    outputStream.close();
//                }
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//
//        } catch (CosServiceException serverException) {
//            serverException.printStackTrace();
//            throw new GetServiceException(LocaleMessageUtils.getMessage("file_not_exist"));
//        } catch (CosClientException clientException) {
//            clientException.printStackTrace();
//            throw new GetServiceException(LocaleMessageUtils.getMessage("file_not_exist"));
//        }
//        return outputStream;
//    }

    @Override
    public Boolean uploadObject(boolean isPub,String bucketName, MultipartFile file, String fileKey) {
        try {
            // 指定要上传的文件
            File localFile = null;
            try {
                localFile = multipartFileToFile(file);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if(GeneralTool.isNotEmpty(localFile)){
                if (localFile.exists()) {
                    try {
                        Thread.sleep(15);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    // fileKey 指定要上传到 COS 上对象键
                    PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, fileKey, localFile);
                    //公开桶上传
                    if(isPub)
                    {
                        log.info("上传文件到公开桶："+bucketName);
                        if (bucketName.contains("hti")){
                            //上传hti公开桶
                            PutObjectResult putObjectResult = ConnectTencentCloud.getHtiPublicCosClient().putObject(putObjectRequest);
                        }else {
                            PutObjectResult putObjectResult = ConnectTencentCloud.getPublicCosClient().putObject(putObjectRequest);
                        }
                    }else
                    {
                        //私有桶区分GET和IAE两种类型，这里通过bucketname是否包含“iae”来区分，包含则取默认的私有key
                        if(bucketName.contains("iae"))
                        {
                            log.info("上传文件到IAE私密桶："+bucketName);
                            PutObjectResult putObjectResult = ConnectTencentCloud.getPrivateCosClient().putObject(putObjectRequest);
                        }else
                        {
                            log.info("上传文件到GET私密桶："+bucketName);
                            PutObjectResult putObjectResult = ConnectTencentCloud.getPrivateShareCosClient().putObject(putObjectRequest);
                            System.out.println("==========");
                        }
                    }

                    return true;
                } else {
                    return false;
                }
            }else {
                return false;
            }

        } catch (CosServiceException serverException) {
            log.error(serverException.getErrorMessage());
            throw new RuntimeException("上传文件平台Server异常" + serverException.getErrorMessage());
        } catch (CosClientException clientException) {
            log.error(clientException.getMessage());
            throw new RuntimeException("上传文件平台Client异常" + clientException.getMessage());
        }
    }

    @Override
    public void createCosBucket(String bucketName) {
        CreateBucketRequest createBucketRequest = new CreateBucketRequest(bucketName);
        // 设置 bucket 的权限为 PublicRead(公有读私有写), 其他可选有私有读写, 公有读写
        createBucketRequest.setCannedAcl(CannedAccessControlList.PublicRead);
        try {
            Bucket bucketResult = ConnectTencentCloud.getPublicCosClient().createBucket(createBucketRequest);
        } catch (CosServiceException serverException) {
            serverException.printStackTrace();
        } catch (CosClientException clientException) {
            clientException.printStackTrace();
        }
    }

    @Override
    public List<Bucket> queryBucketList(String bucketName) {
        List<Bucket> buckets = null;
        try {
            buckets = ConnectTencentCloud.getPublicCosClient().listBuckets();
            System.out.println(buckets);
        } catch (CosServiceException serverException) {
            serverException.printStackTrace();

        } catch (CosClientException clientException) {
            clientException.printStackTrace();
        }
        return buckets;
    }

    @Override
    public void downLoadObject(FileVo fileVo, HttpServletResponse response,Boolean isPub,Boolean isShare,Boolean isHti) {

        String bucketName = isPub?imageBucketName:fileBucketName;
        if (isHti){
            bucketName = htiBucketName;
        }
        //如为共享私密桶
        if(isShare)
        {
            bucketName = shareBucketName;
        }
        response.setCharacterEncoding("UTF-8");
        GetObjectRequest getObjectRequest = new GetObjectRequest(bucketName, fileVo.getFileKey());
        BufferedOutputStream outputStream = null;
        COSObjectInputStream cosObjectInput = null;
        try {
            COSObject cosObject = null;
            if(!isPub)
            {
                //使用共享私密桶（原GET私密桶）
                if(isShare)
                {
                    cosObject = ConnectTencentCloud.getPrivateShareCosClient().getObject(getObjectRequest);
                }else
                {
                    cosObject = ConnectTencentCloud.getPrivateCosClient().getObject(getObjectRequest);
                }

            }else
            {
                if (isHti){
                    cosObject = ConnectTencentCloud.getHtiPublicCosClient().getObject(getObjectRequest);
                }else {
                    cosObject = ConnectTencentCloud.getPublicCosClient().getObject(getObjectRequest);
                }

            }
            cosObjectInput = cosObject.getObjectContent();
            outputStream = new BufferedOutputStream(response.getOutputStream());

            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(new String(fileVo.getFileNameOrc().getBytes("utf-8"), "UTF-8")));
        } catch (CosServiceException e) {
            e.printStackTrace();
        } catch (CosClientException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }

        // 处理下载到的流
        // 这里是直接读取，按实际情况来处理
        byte[] bytes = null;
        try {
            bytes = IOUtils.toByteArray(cosObjectInput);
            outputStream.write(bytes);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            // 用完流之后一定要调用 close()
            try {
                cosObjectInput.close();
            } catch (Exception e) {
                e.printStackTrace();
            }

        }

        try {
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 在流没有处理完之前，不能关闭 cosClient
            // 确认本进程不再使用 cosClient 实例之后，关闭之
            ConnectTencentCloud.getPublicCosClient().shutdown();
        }
    }

    @Override
    public void deleteObjectRequest(String key) {
        try {
            if (checkIndex(key)) {
                // 指定对象在 COS 上的对象键
                ConnectTencentCloud.getPublicCosClient().deleteObject(imageBucketName, key);
            } else {
                // 指定对象在 COS 上的对象键
                ConnectTencentCloud.getPrivateCosClient().deleteObject(fileBucketName, key);
            }

        } catch (CosServiceException serverException) {
            serverException.printStackTrace();
        } catch (CosClientException clientException) {
            clientException.printStackTrace();
        }
    }

    @Override
    public void synchronizeFiles() throws IOException {
//        List<FileFinance> fileFinances = fileFinanceMapper.selectList(Wrappers.<FileFinance>lambdaQuery());
//        if (GeneralTool.isNotEmpty(fileFinances)) {
//            for (FileFinance fileFinance : fileFinances) {
//                Boolean success = false;
//                String fileurl = append(fileFinance.getFilePath());
//                File file = new File(fileurl);
//                System.out.println(fileurl);
//                if (file.exists()) {
//                    System.out.println("服务器文件存在");
//                    FileInputStream fileInputStream = new FileInputStream(file);
//                    MultipartFile multipartFile = new MockMultipartFile(file.getName(), file.getName(),
//                            ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
//                    if (checkIndex(fileFinance.getFilePath())) {
//                        success = uploadObject(imageBucketName, multipartFile, fileFinance.getFilePath());
//                        System.out.println("已经上传到文件桶");
//                    } else {
//                        success = uploadObject(fileBucketName, multipartFile, subString(fileFinance.getFilePath()));
//                        System.out.println("已经上传到附件桶");
//                    }
//                    if (success) {
//                        System.out.println("已经上传并更新到表");
//                        utilService.updateUserInfoToEntity(fileFinance);
//                        fileFinance.setFileKey(subString(fileFinance.getFilePath()));
//                        fileFinanceMapper.updateById(fileFinance);
//                    }
//
//                }else{
//                    System.out.println("服务器文件不存在");
//                }
//
//
//            }
//        }
//        List<FileInstitution> fileInstitutions = fileInstitutionMapper.selectList(Wrappers.<FileInstitution>lambdaQuery());
//        if (GeneralTool.isNotEmpty(fileInstitutions)) {
//            for (FileInstitution fileInstitution : fileInstitutions) {
//                Boolean success = false;
//                File file = new File(append(fileInstitution.getFilePath()));
//                if (file.exists()) {
//                    FileInputStream fileInputStream = new FileInputStream(file);
//                    MultipartFile multipartFile = new MockMultipartFile(file.getName(), file.getName(),
//                            ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
//                    if (checkIndex(fileInstitution.getFilePath())) {
//                        success = uploadObject(imageBucketName, multipartFile, fileInstitution.getFilePath());
//                    } else {
//                        success = uploadObject(fileBucketName, multipartFile, subString(fileInstitution.getFilePath()));
//                    }
//                    if (success) {
//                        utilService.updateUserInfoToEntity(fileInstitution);
//                        fileInstitution.setFileKey(subString(fileInstitution.getFilePath()));
//                        fileInstitutionMapper.updateById(fileInstitution);
//                    }
//
//                }
//            }
//        }
//        List<FilePermission> filePermissions = filePermissionMapper.selectList(Wrappers.<FilePermission>lambdaQuery());
//        if (GeneralTool.isNotEmpty(filePermissions)) {
//            for (FilePermission filePermission : filePermissions) {
//                Boolean success = false;
//                File file = new File(append(filePermission.getFilePath()));
//                if (file.exists()) {
//                    FileInputStream fileInputStream = new FileInputStream(file);
//                    MultipartFile multipartFile = new MockMultipartFile(file.getName(), file.getName(),
//                            ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
//                    if (checkIndex(filePermission.getFilePath())) {
//                        success = uploadObject(imageBucketName, multipartFile, filePermission.getFilePath());
//                    } else {
//                        success = uploadObject(fileBucketName, multipartFile, subString(filePermission.getFilePath()));
//                    }
//                    if (success) {
//                        utilService.updateUserInfoToEntity(filePermission);
//                        filePermission.setFileKey(subString(filePermission.getFilePath()));
//                        filePermissionMapper.updateById(filePermission);
//                    }
//
//                }
//            }
//        }
//        List<FileResume> fileResumes = fileResumeMapper.selectList(Wrappers.<FileResume>lambdaQuery());
//        if (GeneralTool.isNotEmpty(fileResumes)) {
//            for (FileResume fileResume : fileResumes) {
//                Boolean success = false;
//                File file = new File(append(fileResume.getFilePath()));
//                if (file.exists()) {
//                    FileInputStream fileInputStream = new FileInputStream(file);
//                    MultipartFile multipartFile = new MockMultipartFile(file.getName(), file.getName(),
//                            ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
//                    if (checkIndex(fileResume.getFilePath())) {
//                        success = uploadObject(imageBucketName, multipartFile, fileResume.getFilePath());
//                    } else {
//                        success = uploadObject(fileBucketName, multipartFile, subString(fileResume.getFilePath()));
//                    }
//                    if (success) {
//                        utilService.updateUserInfoToEntity(fileResume);
//                        fileResume.setFileKey(subString(fileResume.getFilePath()));
//                        fileResumeMapper.updateById(fileResume);
//                    }
//
//                }
//            }
//        }
//        List<FileSale> fileSales = fileSaleMapper.selectList(Wrappers.<FileSale>lambdaQuery().ge(FileSale::getId,38815));
//        if (GeneralTool.isNotEmpty(fileSales)) {
//            for (FileSale fileSale : fileSales) {
//                Boolean success = false;
//                File file = new File(append(fileSale.getFilePath()));
//                if (file.exists()) {
//                    FileInputStream fileInputStream = new FileInputStream(file);
//                    MultipartFile multipartFile = new MockMultipartFile(file.getName(), file.getName(),
//                            ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
//                    if (checkIndex(fileSale.getFilePath())) {
//                        success = uploadObject(imageBucketName, multipartFile, fileSale.getFilePath());
//                    } else {
//                        success = uploadObject(fileBucketName, multipartFile, subString(fileSale.getFilePath()));
//                    }
//                    if (success) {
//                        fileSale.setFileKey(subString(fileSale.getFilePath()));
//                        utilService.updateUserInfoToEntity(fileSale);
//                        fileSaleMapper.updateById(fileSale);
//                    }
//                }
//            }
//        }
        List<FileExam> fileExams = fileExamMapper.selectList(Wrappers.<FileExam>lambdaQuery());
        if (GeneralTool.isNotEmpty(fileExams)) {
            for (FileExam fileExam : fileExams) {
                Boolean success = false;
                File file = new File(append(fileExam.getFilePath()));
                if (file.exists()) {
                    FileInputStream fileInputStream = new FileInputStream(file);
                    MultipartFile multipartFile = new MockMultipartFile(file.getName(), file.getName(),
                            ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
                    if (checkIndex(fileExam.getFilePath())) {
                        success = uploadObject(true,imageBucketName, multipartFile, fileExam.getFilePath());
                    } else {
                        success = uploadObject(true,fileBucketName, multipartFile, subString(fileExam.getFilePath()));
                    }
                    if (success) {
                        fileExam.setFileKey(subString(fileExam.getFilePath()));
                        utilService.updateUserInfoToEntity(fileExam);
                        fileExamMapper.updateById(fileExam);
                    }
                }
            }
        }
        List<FileMso> fileMsos = fileMsoMapper.selectList(Wrappers.<FileMso>lambdaQuery());
        if (GeneralTool.isNotEmpty(fileMsos)) {
            for (FileMso fileMso : fileMsos) {
                Boolean success = false;
                File file = new File(appendMso(fileMso.getFilePath()));
                if (file.exists()) {
                    FileInputStream fileInputStream = new FileInputStream(file);
                    MultipartFile multipartFile = new MockMultipartFile(file.getName(), file.getName(),
                            ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
                    if (checkIndex(fileMso.getFilePath())) {
                        success = uploadObject(true,imageBucketName, multipartFile, fileMso.getFilePath());
                    } else {
                        success = uploadObject(true,fileBucketName, multipartFile, subString(fileMso.getFilePath()));
                    }
                    if (success) {
                        utilService.updateUserInfoToEntity(fileMso);
                        fileMso.setFileKey(subString(fileMso.getFilePath()));
                        fileMsoMapper.updateById(fileMso);
                    }

                }
            }
        }
        List<FileOffice> fileOffices = fileOfficeMapper.selectList(Wrappers.<FileOffice>lambdaQuery());
        if (GeneralTool.isNotEmpty(fileOffices)) {
            for (FileOffice fileOffice : fileOffices) {
                Boolean success = false;
                File file = new File(append(fileOffice.getFilePath()));
                if (file.exists()) {
                    FileInputStream fileInputStream = new FileInputStream(file);
                    MultipartFile multipartFile = new MockMultipartFile(file.getName(), file.getName(),
                            ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
                    if (checkIndex(fileOffice.getFilePath())) {
                        success = uploadObject(true,imageBucketName, multipartFile, fileOffice.getFilePath());
                    } else {
                        success = uploadObject(true,fileBucketName, multipartFile, subString(fileOffice.getFilePath()));
                    }
                    if (success) {
                        utilService.updateUserInfoToEntity(fileOffice);
                        fileOffice.setFileKey(subString(fileOffice.getFilePath()));
                        fileOfficeMapper.updateById(fileOffice);
                    }
                }
            }
        }
        List<FileVoting> fileVotings = fileVotingMapper.selectList(Wrappers.<FileVoting>lambdaQuery());
        if (GeneralTool.isNotEmpty(fileVotings)) {
            for (FileVoting fileVoting : fileVotings) {
                Boolean success = false;
                File file = new File(append(fileVoting.getFilePath()));
                if (file.exists()) {
                    FileInputStream fileInputStream = new FileInputStream(file);
                    MultipartFile multipartFile = new MockMultipartFile(file.getName(), file.getName(),
                            ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
                    if (checkIndex(fileVoting.getFilePath())) {
                        success = uploadObject(true,imageBucketName, multipartFile, fileVoting.getFilePath());
                    } else {
                        success = uploadObject(true,fileBucketName, multipartFile, subString(fileVoting.getFilePath()));
                    }
                    if (success) {
                        utilService.updateUserInfoToEntity(fileVoting);
                        fileVoting.setFileKey(subString(fileVoting.getFilePath()));
                        fileVotingMapper.updateById(fileVoting);
                    }

                }
            }
        }
        List<FileIssue> fileIssues = fileIssueMapper.selectList(Wrappers.<FileIssue>lambdaQuery());
        if (GeneralTool.isNotEmpty(fileIssues)) {
            for (FileIssue fileIssue : fileIssues) {
                Boolean success = false;
                File file = new File(appendMso(fileIssue.getFilePath()));
                if (file.exists()) {
                    FileInputStream fileInputStream = new FileInputStream(file);
                    MultipartFile multipartFile = new MockMultipartFile(file.getName(), file.getName(),
                            ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
                    if (checkIndex(fileIssue.getFilePath())) {
                        success = uploadObject(true,imageBucketName, multipartFile, fileIssue.getFilePath());
                    } else {
                        success = uploadObject(true,fileBucketName, multipartFile, subString(fileIssue.getFilePath()));
                    }
                    if (success) {
                        utilService.updateUserInfoToEntity(fileIssue);
                        fileIssue.setFileKey(subString(fileIssue.getFilePath()));
                        fileIssueMapper.updateById(fileIssue);
                    }

                }
            }

        }
        List<FileConvention> fileConventions = fileConventionMapper.selectList(Wrappers.<FileConvention>lambdaQuery());
        if (GeneralTool.isNotEmpty(fileConventions)) {
            for (FileConvention fileConvention : fileConventions) {
                Boolean success = false;
                File file = new File(append(fileConvention.getFilePath()));
                if (file.exists()) {
                    FileInputStream fileInputStream = new FileInputStream(file);
                    MultipartFile multipartFile = new MockMultipartFile(file.getName(), file.getName(),
                            ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
                    if (checkIndex(fileConvention.getFilePath())) {
                        success = uploadObject(true,imageBucketName, multipartFile, fileConvention.getFilePath());
                    } else {
                        success = uploadObject(true,fileBucketName, multipartFile, subString(fileConvention.getFilePath()));
                    }
                    if (success) {
                        utilService.updateUserInfoToEntity(fileConvention);
                        fileConvention.setFileKey(subString(fileConvention.getFilePath()));
                        fileConventionMapper.updateById(fileConvention);
                    }

                }
            }
        }
        List<FileIb> fileIbs = fileIbMapper.selectList(Wrappers.<FileIb>lambdaQuery());
        if (GeneralTool.isNotEmpty(fileIbs)) {
            for (FileIb fileIb : fileIbs) {
                Boolean success = false;
                File file = new File(appendMso(fileIb.getFilePath()));
                if (file.exists()) {
                    FileInputStream fileInputStream = new FileInputStream(file);
                    MultipartFile multipartFile = new MockMultipartFile(file.getName(), file.getName(),
                            ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
                    if (checkIndex(fileIb.getFilePath())) {
                        success = uploadObject(true,imageBucketName, multipartFile, fileIb.getFilePath());
                    } else {
                        success = uploadObject(true,fileBucketName, multipartFile, subString(fileIb.getFilePath()));
                    }

                    if (success) {
                        utilService.updateUserInfoToEntity(fileIb);
                        fileIb.setFileKey(subString(fileIb.getFilePath()));
                        fileIbMapper.updateById(fileIb);
                    }
                }
            }
        }
    }

    @Override
    public SaleFileDto getDownloadFile(FileVo fileVo) {
        //设置文件路径
        boolean isPub = false;
        GetObjectRequest getObjectRequest = new GetObjectRequest(isPub?imageBucketName:fileBucketName, fileVo.getFileKey());
        COSObjectInputStream cosObjectInput = null;
        try {
            COSObject cosObject = ConnectTencentCloud.getPrivateCosClient().getObject(getObjectRequest);
            cosObjectInput = cosObject.getObjectContent();
        } catch (CosClientException e) {
            e.printStackTrace();
            return null;
        }

        // 处理下载到的流
        // 这里是直接读取，按实际情况来处理
        byte[] bytes = null;
        SaleFileDto saleFileDto = new SaleFileDto();
        try {
            bytes = IOUtils.toByteArray(cosObjectInput);

            saleFileDto.setFileName(fileVo.getFileNameOrc());
            saleFileDto.setBytes(bytes);

        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            // 用完流之后一定要调用 close()
            try {
                cosObjectInput.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
            // 在流没有处理完之前，不能关闭 cosClient
            // 确认本进程不再使用 cosClient 实例之后，关闭之
            ConnectTencentCloud.getPublicCosClient().shutdown();
        }

        return saleFileDto;
    }

    /**
     * 获取封装得MultipartFile
     *
     * @param inputStream inputStream
     * @param fileName    fileName
     * @return MultipartFile
     */
    public MultipartFile getMultipartFile(InputStream inputStream, String fileName) {
        FileItem fileItem = createFileItem(inputStream, fileName);
        return new CommonsMultipartFile(fileItem);
    }

    /**
     * FileItem类对象创建
     *
     * @param inputStream inputStream
     * @param fileName    fileName
     * @return FileItem
     */
    public FileItem createFileItem(InputStream inputStream, String fileName) {
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        String textFieldName = "file";
        FileItem item = factory.createItem(textFieldName, MediaType.MULTIPART_FORM_DATA_VALUE, true, fileName);
        int bytesRead = 0;
        byte[] buffer = new byte[8192];
        OutputStream os = null;
        //使用输出流输出输入流的字节
        try {
            os = item.getOutputStream();
            while ((bytesRead = inputStream.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            inputStream.close();
        } catch (IOException e) {
            log.error("Stream copy exception", e);
            throw new IllegalArgumentException("文件上传失败");
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    log.error("Stream close exception", e);

                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("Stream close exception", e);
                }
            }
        }
        return item;
    }


    String append(String url) {
        if (url.contains("student_file")) {
            String str2 = "/data/project/get/app-file-center/target" + url;
            return str2;
        }
        else if (url.contains("files")) {
            String str2 = "/data/project/get/file-center/target" + url;
            return str2;
        }
        else {
            return url;
        }
    }


    String appendMso(String url) {
        if (url.contains("student_file")) {
            String str2 = "/data/project/get/app-file-center/target" + url;
            return str2;
        }
        else if (url.contains("files")) {
            String str2 = "/data/project/get/app-file-center/target" + url;
            return str2;
        }
        else {
            return url;
        }
    }

    Boolean checkIndex(String url) {
        boolean status = true;
        if (url.contains("target")) {
            status = false;
        } else if (url.contains("student_file")) {
            status = false;
        }
        return status;
    }
}
