package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.ContractFormulaAreaCountryStudent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ContractFormulaAreaCountryStudentMapper extends BaseMapper<ContractFormulaAreaCountryStudent> {
    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(ContractFormulaAreaCountryStudent record);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过合同公式id 查找学生来源国家ids
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<Long> getStudentCountryIdListByFkid(@Param("contractFormulaId") Long contractFormulaId);

    /**
     * @return java.util.List<java.lang.String>
     * @Description :通过合同公式id 查找学生来源国家名称
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<String> getStudentCountryNameByFkid(@Param("contractFormulaId") Long contractFormulaId);
}