package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ErrorCodeEnum;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.CommonUtil;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.redis.lock.RedisLock;
import com.get.core.secure.UserInfo;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dto.AgentSettlementDto;
import com.get.financecenter.vo.AgentSettlementItemVo;
import com.get.financecenter.service.AgencyCommissionSettlementService;
import com.get.financecenter.dto.*;
import com.get.financecenter.dto.AgentSettlementBatchExportDto;
import com.get.permissioncenter.dto.CompanyConfigInfoDto;

import com.get.salecenter.vo.CommissionSummaryBatchItemDetailVo;
import com.get.salecenter.vo.CommissionSummaryBatchItemVo;
import com.get.salecenter.vo.CommissionSummaryBatchVo;
import com.get.salecenter.vo.CommissionSummaryVo;
import com.get.salecenter.vo.StudentPlanVo;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.CancelFinancialBatchSettlementDto;
import com.get.financecenter.dto.query.AgentSettlementQueryDto;
import com.get.salecenter.dto.CancelFinancialConfirmationSettlementDto;
import com.get.salecenter.dto.CommissionSummaryBatchDetailDto;
import com.get.salecenter.dto.CommissionSummaryBatchDto;
import com.get.salecenter.dto.CommissionSummaryDto;
import com.get.salecenter.dto.PayablePlanSettlementBatchExchangeDto;
import com.get.salecenter.dto.SettlementAgentAccountUpdateDto;
import com.get.salecenter.dto.SubmitFinancialSettlementSummaryDto;
import com.get.salecenter.dto.SubmitSettlementDto;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 代理结算佣金
 *
 * <AUTHOR>
 * @date 2021/12/20 15:37
 */
@Api(tags = "代理佣金结算管理")
@RestController
@RequestMapping("finance/agencyCommissionSettlement")
public class AgencyCommissionSettlementController {

    @Resource
    private AgencyCommissionSettlementService agencyCommissionSettlementService;

    /**
     * 代理佣金结算列表
     *
     * @Date 11:15 2021/12/21
     * <AUTHOR>
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "代理佣金结算列表")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/代理佣金结算/代理佣金结算列表")
    @PostMapping("datas")
    public ResponseBo agentSettlementList(@RequestBody @Validated SearchBean<AgentSettlementQueryDto> page) {
        return agencyCommissionSettlementService.agentSettlementList(page);
    }


    @ApiOperation(value = "代理佣金结算列表导出")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/代理佣金结算/代理佣金结算列表导出")
    @PostMapping("agentSettlementListExport")
    public void agentSettlementListExport(HttpServletResponse response,@RequestBody AgentSettlementQueryDto agentSettlementVo){
        CommonUtil.ok(response);
        agencyCommissionSettlementService.agentSettlementListExport(agentSettlementVo);
    }

    @ApiOperation(value = "第三步佣金结算总额表导出")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/代理佣金结算/第三步佣金结算总额表导出")
    @PostMapping("agentSettlementGrossAmountExport")
    public void agentSettlementGrossAmountExport(HttpServletResponse response,@RequestBody AgentSettlementQueryDto agentSettlementVo){
        CommonUtil.ok(response);
        agencyCommissionSettlementService.agentSettlementGrossAmountExport(agentSettlementVo);
    }

    /**
     * 代理佣金结算子项列表
     *
     * @Date 18:00 2021/12/21
     * <AUTHOR>
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "代理佣金结算子项列表")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/代理佣金结算/代理佣金结算子项列表")
    @PostMapping("itemDatas")
    public ResponseBo<AgentSettlementItemVo> itemDatas(@RequestBody @Validated AgentSettlementDto agentSettlementDto) {
        AgentSettlementItemVo agentSettlementItemVo = agencyCommissionSettlementService.itemDatas(agentSettlementDto);
        return new ResponseBo<>(agentSettlementItemVo);
    }

//    @ApiOperationSupport(order = 3)
//    @ApiOperation(value = "佣金结算类型下拉框")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/代理佣金结算/佣金结算类型下拉框")
//    @PostMapping("/agentSettlementSelect")
//    public ResponseBo agentSettlementSelect(){
//        List<Map<String, Object>> datas = TableEnum.enumsTranslation2ArraysByName(TableEnum.COMMISSION_SETTLEMENT_TYPE);
//        return new ResponseBo<>(datas);
//    }

    /**
     * 代理佣金结算提交
     *
     * @Date 10:34 2021/12/22
     * <AUTHOR>
     */
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "代理佣金结算提交")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/佣金结算/代理佣金结算提交")
    @PostMapping("submitSettlement")
    public ResponseBo submitSettlement(@RequestBody @Validated List<SubmitSettlementDto> submitSettlementDtoList) {
        agencyCommissionSettlementService.submitSettlement(submitSettlementDtoList);
        return ResponseBo.ok();
    }

    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "删除佣金结算（佣金第一二三步专用）")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/佣金结算/删除佣金结算")
    @PostMapping("deleteSettlement")
    public ResponseBo deleteSettlement(@RequestBody @Validated List<DeleteSettlementDeleteDto> deleteSettlementDeleteDtoList) {
        agencyCommissionSettlementService.deleteSettlement(deleteSettlementDeleteDtoList);
        return ResponseBo.ok();
    }

    /**
     * 代理佣金结算取消
     *
     * @Date 12:38 2021/12/23
     * <AUTHOR>
     */
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "代理佣金结算取消")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/佣金结算/代理佣金结算取消")
    @PostMapping("cancelSettlement")
    public ResponseBo cancelSettlement(@RequestBody List<CancelSettlementDto> cancelSettlementList) {
        agencyCommissionSettlementService.cancelSettlement(cancelSettlementList);
        return ResponseBo.ok();
    }

//    /**
//     * 代理对账单确认列表
//     *
//     * @Date 11:15 2021/12/21
//     * <AUTHOR>
//     */
//    @ApiOperationSupport(order = 6)
//    @ApiOperation(value = "代理对账单确认列表")
//    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/代理佣金结算/代理对账单确认列表")
//    @PostMapping("agencyStatementDatas")
//    public ResponseBo<AgencyStatementDto> agencyStatementDatas(@RequestBody @Validated SearchBean<AgencyStatementVo> page)  {
//        List<AgencyStatementDto> datas = agencyCommissionSettlementService.agencyStatementDatas(page);
//        Page p = Tools.objClone(page, Page.class);
//        return new ListResponseBo<>(datas, p);
//    }

    /**
     * 更新结算标记
     *
     * @Date 12:38 2021/12/23
     * <AUTHOR>
     */
    @ApiOperation(value = "更新结算标记")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/佣金结算/更新结算标记")
    @PostMapping("updateSettlementAgentAccount")
    public ResponseBo updateSettlementAgentAccount(@RequestBody @Validated List<SettlementAgentAccountUpdateDto> settlementAgentAccountUpdateDtoList) {
        agencyCommissionSettlementService.updateSettlementAgentAccount(settlementAgentAccountUpdateDtoList);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "代理附件损坏校验")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/佣金结算/代理附件损坏校验")
    @PostMapping("checkAgentAttachment")
    public ResponseBo checkAgentAttachment(@RequestBody @Validated List<BatchDownloadAgentReconciliationDto> batchDownloadAgentReconciliationVoList) {
        String failAgentName = agencyCommissionSettlementService.checkAgentAttachment(batchDownloadAgentReconciliationVoList);
        if (GeneralTool.isEmpty(failAgentName)) {
            return ResponseBo.ok();
        } else {
            return ResponseBo.ok(ErrorCodeEnum.REQUEST_OK_MESSAGE.getCode(), failAgentName);
        }
    }

    @ApiOperation(value = "批量下载对账单")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/佣金结算/批量下载对账单")
    @RedisLock(value="fzh:batchDownloadAgentReconciliationExcelLock",param = "#userInfo.staffId",waitTime = 10L)
    @PostMapping("batchDownloadAgentReconciliationExcel")
    public ResponseBo batchDownloadAgentReconciliationExcel(@RequestBody @Validated List<BatchDownloadAgentReconciliationDto> batchDownloadAgentReconciliationVoList, UserInfo userInfo, HttpServletResponse response) throws Exception {
        agencyCommissionSettlementService.batchDownloadAgentReconciliationExcel(batchDownloadAgentReconciliationVoList, response);
        return ResponseBo.ok();
    }


    /**
     * 提交代理确认结算 (第二步、第三步提交按钮)
     *
     * @Date 12:38 2021/12/23
     * <AUTHOR>
     */
    @ApiOperation(value = "提交代理确认结算 (第二步、第三步提交按钮)")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/佣金结算/提交代理确认结算(第二步、第三步提交按钮)")
    @RedisLock(value="fzh:agentConfirmSettlementLock",param = "#userInfo.staffId",waitTime = 10L)
    @PostMapping("agentConfirmSettlement")
    public ResponseBo agentConfirmSettlement(@RequestBody @Validated List<AgentSettlementBatchExportDto> agentSettlementBatchExportVoList, UserInfo userInfo) {
        agencyCommissionSettlementService.agentConfirmSettlement(agentSettlementBatchExportVoList);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "待结算标记")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/佣金结算/待结算标记")
    @PostMapping("pendingSettlementMark")
    public ResponseBo pendingSettlementMark(@RequestBody @Validated PendingSettlementMarkDto pendingSettlementMarkDto) {
        agencyCommissionSettlementService.pendingSettlementMark(pendingSettlementMarkDto);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "取消代理确认结算 (第三步取消按钮)")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/佣金结算/取消代理确认结算 (第三步取消按钮)")
    @PostMapping("cancelAgentConfirmSettlement")
    public ResponseBo cancelAgentConfirmSettlement(@RequestBody List<CancelSettlementDto> cancelSettlementList) {
        agencyCommissionSettlementService.cancelAgentConfirmSettlement(cancelSettlementList);
        return ResponseBo.ok();
    }


    @ApiOperation(value = "获取配置key")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/佣金结算/获取配置key")
    @VerifyPermission(IsVerify = false)
    @GetMapping("getSettlementConfigInfo")
    public ListResponseBo<CompanyConfigInfoDto> getSettlementConfigKey(@RequestParam("configKey") String configKey){
        return agencyCommissionSettlementService.getSettlementConfigKey(configKey);
    }

    @ApiOperation(value = "修改实际支付金额")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/佣金结算/修改实际支付金额")
    @PostMapping("updateInstallmentAmountActual")
    public ResponseBo updateInstallmentAmountActual(@RequestBody @Validated List<InstallmentAmountActualUpdateDto> installmentAmountActualUpdateDtos) {
        agencyCommissionSettlementService.updateInstallmentAmountActual(installmentAmountActualUpdateDtos);
        return ResponseBo.ok();
    }


    /**
     * 财务确认代理佣金结算 （第三步提交按钮） 废弃
     *
     * @Date 12:38 2021/12/23
     * <AUTHOR>
     */
    @ApiOperation(value = "财务确认代理佣金结算 （第三步提交按钮）（废弃）")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/佣金结算/财务确认代理佣金结算")
    @PostMapping("financeConfirmSettlement")
    public ResponseBo financeConfirmSettlement(@RequestBody List<Long> payablePlanIdList) {
        agencyCommissionSettlementService.financeConfirmSettlement(payablePlanIdList);
        return ResponseBo.ok();
    }


    /**
     * 应付计划编辑详情回显
     *
     * @Date 12:38 2021/12/23
     * <AUTHOR>
     */
    @ApiOperation(value = "应付计划编辑详情回显", notes = "itemId:学习计划id fkCurrencyTypeNum:应付币种编号 ")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/佣金结算/应付计划编辑详情回显")
    @GetMapping("financePlanDetails")
    public ResponseBo<StudentPlanVo> financePlanDetails(@RequestParam("planId") Long planId) {
        StudentPlanVo studentPlanVo = agencyCommissionSettlementService.financePlanDetails(planId);
        return new ResponseBo<>(studentPlanVo);
    }

    /**
     * 批量编辑应付计划 （废弃）
     *
     * @return
     * @Date 12:38 2021/12/23
     * <AUTHOR>
     */
    @ApiOperation(value = "批量编辑应付计划（废弃）")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/佣金结算/批量编辑应付计划")
    @PostMapping("batchUpdatePayablePlan")
    public ResponseBo batchUpdatePayablePlan(@RequestBody List<PayablePlanDto> payablePlanDtoList) {
        agencyCommissionSettlementService.batchUpdatePayablePlan(payablePlanDtoList);
        return ResponseBo.ok();
    }

    /**
     * 财务佣金汇总列表
     *
     * @Date 15:42 2021/12/24
     * <AUTHOR>
     */
    @ApiOperation(value = "财务佣金汇总列表")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/佣金结算/财务佣金汇总列表")
    @PostMapping("commissionSummary")
    public ListResponseBo<CommissionSummaryVo> commissionSummary(@RequestBody @Validated SearchBean<CommissionSummaryDto> page) {
        return agencyCommissionSettlementService.commissionSummary(page);
    }


    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "财务佣金汇总列表第四步导出")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/佣金结算/财务佣金汇总列表第四步导出")
    @PostMapping("agentSettlementListFourthStepExport")
    public void agentSettlementListFourthStepExport(@RequestBody @Validated CommissionSummaryDto commissionSummaryDto, HttpServletResponse response){
        agencyCommissionSettlementService.agentSettlementListFourthStepExport(commissionSummaryDto,response);
    }

    /**
     * 财务结算汇总提交(第四步提交按钮)
     *
     * @Date 17:24 2021/12/24
     * <AUTHOR>
     */
    @ApiOperation(value = "财务结算汇总提交(第四步提交按钮)")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/佣金结算/财务结算汇总提交")
    @PostMapping("submitFinancialSettlementSummary")
    public ResponseBo submitFinancialSettlementSummary(@RequestBody @Validated List<SubmitFinancialSettlementSummaryDto> submitFinancialSettlementSummaryDto) {
        agencyCommissionSettlementService.submitFinancialSettlementSummary(submitFinancialSettlementSummaryDto);
        return ResponseBo.ok();
    }

    /**
     * 财务结算汇总删除佣金结算(第四步删除佣金结算按钮)
     *
     * @Date 17:24 2021/12/24
     * <AUTHOR>
     */
    @ApiOperation(value = "财务结算汇总删除佣金结算(第四步删除佣金结算按钮)")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/佣金结算/财务结算汇总提交")
    @PostMapping("deleteFinancialSettlementSummary")
    public ResponseBo deleteFinancialSettlementSummary(@RequestBody @Validated List<DeleteFinancialSettlementSummaryDeleteDto> deleteFinancialSettlementSummaryDeleteDtoList) {
        agencyCommissionSettlementService.deleteFinancialSettlementSummary(deleteFinancialSettlementSummaryDeleteDtoList);
        return ResponseBo.ok();
    }

    /**
     * 财务结算汇总锁定代理(第四步锁定代理)
     *
     * @Date 11:46 2022/5/18
     * <AUTHOR>
     */
    @ApiOperation(value = "财务结算汇总锁定代理(第四步锁定代理)")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/佣金结算/财务结算汇总锁定代理(第四步锁定代理)")
    @PostMapping("financialSettlementAgentLocking")
    public ResponseBo financialSettlementAgentLocking(@RequestBody @Validated List<FinancialSettlementAgentLockingDto> financialSettlementAgentLockingDtos) {
        agencyCommissionSettlementService.financialSettlementAgentLocking(financialSettlementAgentLockingDtos);
        return ResponseBo.ok();
    }

    /**
     * 财务结算汇总解锁代理(第四步解锁代理)
     *
     * @Date 11:46 2022/5/18
     * <AUTHOR>
     */
    @ApiOperation(value = "财务结算汇总解锁代理(第四步解锁代理)")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/佣金结算/财务结算汇总解锁代理(第四步解锁代理)")
    @PostMapping("financialSettlementAgentUnlocking")
    public ResponseBo financialSettlementAgentUnlocking(@RequestBody @Validated List<FinancialSettlementAgentLockingDto> financialSettlementAgentLockingDtos) {
        agencyCommissionSettlementService.financialSettlementAgentUnlocking(financialSettlementAgentLockingDtos);
        return ResponseBo.ok();
    }


    /**
     * 取消财务确认结算(第四步取消按钮)
     *
     * @Date 10:22 2021/12/27
     * <AUTHOR>
     */
    @ApiOperation(value = "财务确认结算取消(第四步取消按钮)")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/佣金结算/财务确认结算取消")
    @PostMapping("cancelFinancialConfirmationSettlement")
    public ResponseBo cancelFinancialConfirmationSettlement(@RequestBody @Validated List<CancelFinancialConfirmationSettlementDto> cancelFinancialConfirmationSettlementDtos) {
        agencyCommissionSettlementService.cancelFinancialConfirmationSettlement(cancelFinancialConfirmationSettlementDtos);
        return ResponseBo.ok();
    }

    /**
     * 财务佣金汇总批次列表
     *
     * @Date 15:42 2021/12/24
     * <AUTHOR>
     */
    @ApiOperation(value = "财务佣金汇总批次列表")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/佣金结算/财务佣金汇总批次列表")
    @PostMapping("commissionSummaryBatchList")
    public ResponseBo commissionSummaryBatchList(@RequestBody SearchBean<CommissionSummaryBatchDto> page) {
        List<CommissionSummaryBatchVo> commissionSummaryBatchVos = agencyCommissionSettlementService.commissionSummaryBatchList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(commissionSummaryBatchVos, p);
    }

    /**
     * 财务佣金汇总批次子项列表
     *
     * @Date 15:42 2021/12/24
     * <AUTHOR>
     */
    @ApiOperation(value = "财务佣金汇总批次子项列表")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/佣金结算/财务佣金汇总批次子项列表")
    @PostMapping("commissionSummaryBatchItemList")
    public ResponseBo<CommissionSummaryBatchItemVo> commissionSummaryBatchItemList(@RequestBody CommissionSummaryBatchDto commissionSummaryBatchDto) {
        List<CommissionSummaryBatchItemVo> datas = agencyCommissionSettlementService.commissionSummaryBatchItemList(commissionSummaryBatchDto);
        return new ListResponseBo<>(datas);
    }

    /**
     * 财务佣金汇总批次取消（第五步取消）
     *
     * @Date 16:41 2022/4/19
     * <AUTHOR>
     */
    @ApiOperation(value = "财务佣金汇总批次取消（第五步取消）")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/佣金结算/财务确认结算取消")
    @PostMapping("cancelFinancialBatchSettlement")
    public ResponseBo cancelFinancialBatchSettlement(@RequestBody @Validated List<CancelFinancialBatchSettlementDto> cancelFinancialBatchSettlementDtos) {
        agencyCommissionSettlementService.cancelFinancialBatchSettlement(cancelFinancialBatchSettlementDtos);
        return ResponseBo.ok();
    }

    /**
     * 下载结算汇总表回显
     *
     * @Date 15:42 2021/12/24
     * <AUTHOR>
     */
    @ApiOperation(value = "下载结算汇总表回显")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/佣金结算/下载结算汇总表回显")
    @PostMapping("commissionSummaryBatchItemDetail")
    public ResponseBo<CommissionSummaryBatchItemDetailVo> commissionSummaryBatchItemDetail(@RequestBody @Validated CommissionSummaryBatchDetailDto commissionSummaryBatchDetailDto) {
        CommissionSummaryBatchItemDetailVo commissionSummaryBatchItemDetailVo = agencyCommissionSettlementService.commissionSummaryBatchItemDetail(commissionSummaryBatchDetailDto);
        return new ResponseBo<>(commissionSummaryBatchItemDetailVo);
    }

    /**
     * 保存结算汇总表汇率
     *
     * @Date 10:10 2021/12/28
     * <AUTHOR>
     */
    @ApiOperation(value = "保存结算汇总表汇率")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/佣金结算/保存结算汇总表汇率")
    @PostMapping("saveExchangeRate")
    public ResponseBo saveExchangeRate(@RequestBody @Validated List<PayablePlanSettlementBatchExchangeDto> payablePlanSettlementBatchExchangeDtos) {
        agencyCommissionSettlementService.saveExchangeRate(payablePlanSettlementBatchExchangeDtos);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "批次完成标记接口")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/佣金结算/批次完成标记接口")
    @PostMapping("saveIsExchangeInput")
    public ResponseBo saveIsExchangeInput(@RequestBody  List<PayablePlanSettlementBatchExchangeDto> payablePlanSettlementBatchExchangeDtos) {
        agencyCommissionSettlementService.saveIsExchangeInput(payablePlanSettlementBatchExchangeDtos);
        return ResponseBo.ok();
    }

    /**
     * 财务汇总批次列表导出Excel
     *
     * @Date 10:10 2021/12/28
     * <AUTHOR>
     */
    @ApiOperation(value = "财务汇总批次列表导出Excel")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/佣金结算/财务汇总批次列表导出Excel")
    @PostMapping("commissionSummaryBatchItemExcel")
    public void commissionSummaryBatchItemExcel(@RequestBody @Validated CommissionSummaryBatchDto commissionSummaryBatchDto, HttpServletResponse response) {
        agencyCommissionSettlementService.commissionSummaryBatchItemExcel(commissionSummaryBatchDto, response);
    }

    /**
     * 财务汇总批次列表导出ifile
     *
     * @Date 10:10 2022/03/04
     * <AUTHOR>
     */
    @ApiOperation(value = "财务汇总批次列表导出ifile")
    @PostMapping("/commissionSummaryBatchItemIfile")
    public void commissionSummaryBatchItemIfile(@RequestBody @Validated CommissionSummaryBatchDto commissionSummaryBatchDto, HttpServletResponse response) {
        agencyCommissionSettlementService.commissionSummaryBatchItemIfile(commissionSummaryBatchDto, response);
    }

    /**
     * 自动生成付款单
     *
     * @Date 14:48 2021/12/28
     * <AUTHOR>
     */
    @ApiOperation(value = "自动生成付款单绑定")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/佣金结算/自动生成付款单")
    @PostMapping("autoGeneratePayment")
    @RedisLock(value = "get:autoGeneratePayment:RedisLock", param = "#autoGeneratePaymentDto.numSettlementBatch", waitTime = 15L)
    public ResponseBo autoGeneratePayment(@RequestBody @Validated AutoGeneratePaymentDto autoGeneratePaymentDto) {
        agencyCommissionSettlementService.autoGeneratePayment(autoGeneratePaymentDto);
        return ResponseBo.ok();
    }

    /**
     * 绑定支付流水号
     *
     * @Date 16:56 2021/12/28
     * <AUTHOR>
     */
    @ApiOperation(value = "绑定支付流水号")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/佣金结算/绑定支付流水号")
    @PostMapping("bindPaymentSerialNumber")
    public ResponseBo bindPaymentSerialNumber(@RequestBody @Validated BindPaymentSerialNumberDto bindPaymentSerialNumberDto) {
        agencyCommissionSettlementService.bindPaymentSerialNumber(bindPaymentSerialNumberDto);
        return ResponseBo.ok();
    }

    /**
     * 代理对账单Excel导出 (废弃)
     *
     * @Date 17:21 2022/1/5
     * <AUTHOR>
     */
    @ApiOperation(value = "代理对账单Excel导出")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/佣金结算/代理对账单Excel导出")
    @PostMapping("agentStatementExcelExport")
    public void agentStatementExcelExport(@RequestBody @Validated AgentStatementExcelExportDto agentStatementExcelExportDto, HttpServletResponse response) {
        agencyCommissionSettlementService.agentStatementExcelExport(agentStatementExcelExportDto, response);
    }

}
