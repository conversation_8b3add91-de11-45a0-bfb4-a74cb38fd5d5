package com.get.gateway;

import com.get.common.constant.AppCenterConstant;
import com.get.core.start.GetApplication;
import org.springframework.cloud.client.SpringCloudApplication;
import org.springframework.cloud.netflix.hystrix.EnableHystrix;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 项目启动
 */
@EnableHystrix
@EnableScheduling
@SpringCloudApplication
public class GateWayApplication {

    public static void main(String[] args) {
        GetApplication.run(AppCenterConstant.APPLICATION_GATEWAY_NAME, com.get.gateway.GateWayApplication.class, args);
    }

}
