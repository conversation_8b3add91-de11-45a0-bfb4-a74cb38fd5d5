package com.get.institutioncenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.dto.InstitutionProviderCompanyDto;
import com.get.institutioncenter.entity.InstitutionProviderCompany;
import com.get.permissioncenter.vo.CompanyVo;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/8/21
 * @TIME: 16:10
 * @Description: 学校提供商管理-公司绑定配置
 **/
public interface IInstitutionProviderCompanyService extends BaseService<InstitutionProviderCompany> {

    /**
     * 修改/新增共用接口
     *
     * @param providerCompanyVos
     */
    void editProviderCompanyRelation(List<InstitutionProviderCompanyDto> providerCompanyVos);

    /**
     * @return java.lang.Long
     * @Description: 新增关系
     * @Param [providerCompanyVo]
     * <AUTHOR>
     */
    Long addRelation(InstitutionProviderCompanyDto providerCompanyVo);

    /**
     * 提供商和公司的关系（数据回显）
     *
     * @param providerId
     * @return
     * @
     */
    List<CompanyTreeVo> getProviderCompanyRelation(Long providerId);


    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据公司id查询提供商
     * @Param [companyIds]
     * <AUTHOR>
     */
    List<Long> getRelationByCompanyId(List<Long> companyIds);


    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据提供商id查询公司
     * @Param [providerId]
     * <AUTHOR>
     */
    List<Long> getRelationByProviderId(Long providerId);


    /**
     * 根据提供商ids查询公司
     *
     * @param providerIds
     * @return
     */
    Map<Long, LinkedList<Long>> getRelationByProviderIds(Set<Long> providerIds);


    /**
     * 获取提供商所属公司名称
     * @param id
     * @return
     */
    List<CompanyVo> getProviderCompanyName(Long id);


    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据合同id查询所属提供商对应的公司ids
     * @Param [contractId]
     * <AUTHOR>
     */
    List<Long> getCompanyIdByContractId(Long contractId);
}
