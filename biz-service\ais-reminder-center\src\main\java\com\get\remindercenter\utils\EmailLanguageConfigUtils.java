package com.get.remindercenter.utils;

import com.get.common.eunms.ProjectKeyEnum;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.entity.EmailTemplate;
import com.get.remindercenter.enums.EmailLanguageEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * 邮件语言配置工具类
 * 提供邮件语言处理功能
 *
 * <AUTHOR>
 * @date 2025-01-18
 * @version 1.0
 */
@Slf4j
public class EmailLanguageConfigUtils {

    /**
     * 根据参数Map智能确定语言代码
     * 优先级：versionValue > staffId > 默认中文
     *
     * @param params 参数Map
     * @param permissionCenterClient 权限中心客户端
     * @return 语言代码（zh/en）
     */
    public static String determineLanguageCode(Map<String, String> params, IPermissionCenterClient permissionCenterClient) {
        if (params == null || params.isEmpty()) {
            return EmailLanguageEnum.CHINESE.getCode();
        }

        // 1. 优先使用参数中的versionValue
        String versionValue = params.get("versionValue");
        if (GeneralTool.isNotEmpty(versionValue)) {
            return EmailLanguageEnum.getLanguageCodeByConfigValue(versionValue);
        }

        // 2. 通过staffId获取公司配置
        String staffIdStr = params.get("staffId");
        if (GeneralTool.isNotEmpty(staffIdStr)) {
            try {
                Long staffId = Long.valueOf(staffIdStr);
                return getLanguageCodeByStaffId(staffId, permissionCenterClient);
            } catch (NumberFormatException e) {
                log.warn("staffId格式错误: {}", staffIdStr);
            } catch (Exception e) {
                log.warn("通过staffId获取语言配置失败: {}", e.getMessage());
            }
        }

        // 3. 默认返回中文
        return EmailLanguageEnum.CHINESE.getCode();
    }

    /**
     * 通过员工ID获取语言代码
     *
     * @param staffId 员工ID
     * @param permissionCenterClient 权限中心客户端
     * @return 语言代码（zh/en）
     */
    public static String getLanguageCodeByStaffId(Long staffId, IPermissionCenterClient permissionCenterClient) {
        if (staffId == null) {
            log.warn("员工ID为空，返回默认语言代码");
            return EmailLanguageEnum.CHINESE.getCode();
        }

        try {
            // 1. 获取员工信息
            StaffVo staffVo = getStaffInfo(staffId, permissionCenterClient);
            if (staffVo == null || staffVo.getFkCompanyId() == null) {
                log.warn("员工信息不存在或公司ID为空, staffId: {}", staffId);
                return EmailLanguageEnum.CHINESE.getCode();
            }

            // 2. 通过公司ID获取语言配置
            return getLanguageCodeByCompanyId(staffVo.getFkCompanyId(), permissionCenterClient);

        } catch (Exception e) {
            log.error("获取员工语言配置失败, staffId: {}, 错误: {}", staffId, e.getMessage(), e);
            return EmailLanguageEnum.CHINESE.getCode();
        }
    }

    /**
     * 通过公司ID获取语言代码
     *
     * @param companyId 公司ID
     * @param permissionCenterClient 权限中心客户端
     * @return 语言代码（zh/en）
     */
    public static String getLanguageCodeByCompanyId(Long companyId, IPermissionCenterClient permissionCenterClient) {
        if (companyId == null) {
            log.warn("公司ID为空，返回默认语言代码");
            return EmailLanguageEnum.CHINESE.getCode();
        }

        try {
            // 获取公司的语言配置
            Map<Long, String> versionConfigMap = permissionCenterClient
                    .getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1)
                    .getData();
            
            if (versionConfigMap == null) {
                log.warn("获取公司语言配置失败, companyId: {}", companyId);
                return EmailLanguageEnum.CHINESE.getCode();
            }

            // 获取公司的语言配置值
            String configValue = versionConfigMap.get(companyId);
            return EmailLanguageEnum.getLanguageCodeByConfigValue(configValue);

        } catch (Exception e) {
            log.error("获取公司语言配置失败, companyId: {}, 错误: {}", companyId, e.getMessage(), e);
            return EmailLanguageEnum.CHINESE.getCode();
        }
    }

    /**
     * 获取员工信息
     *
     * @param staffId 员工ID
     * @param permissionCenterClient 权限中心客户端
     * @return 员工信息，获取失败时返回null
     */
    public static StaffVo getStaffInfo(Long staffId, IPermissionCenterClient permissionCenterClient) {
        if (staffId == null) {
            log.warn("员工ID为空，无法获取员工信息");
            return null;
        }

        try {
            return permissionCenterClient.getStaffById(staffId).getData();
        } catch (Exception e) {
            log.error("获取员工信息失败: staffId={}, 错误={}", staffId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据语言代码选择邮件模板
     *
     * @param languageCode 语言代码
     * @param chineseTemplate 中文模板
     * @param englishTemplate 英文模板
     * @return 选择的模板内容
     */
    public static String selectEmailTemplate(String languageCode, String chineseTemplate, String englishTemplate) {
        if (EmailLanguageEnum.isEnglish(languageCode)) {
            return GeneralTool.isNotEmpty(englishTemplate) ? englishTemplate : chineseTemplate;
        } else {
            return GeneralTool.isNotEmpty(chineseTemplate) ? chineseTemplate : englishTemplate;
        }
    }

    /**
     * 处理父模板选择和内容替换
     *
     * @param languageCode 语言代码
     * @param childTemplate 子模板内容
     * @param parentTemplate 父模板对象
     * @param params 替换参数
     * @return 处理后的模板内容
     */
    public static String processParentTemplate(String languageCode, String childTemplate, 
                                               EmailTemplate parentTemplate, Map<String, String> params) {
        if (parentTemplate == null) {
            return childTemplate;
        }

        try {
            // 构建父模板参数
            Map<String, String> parentParams = new HashMap<>();
            if (params != null) {
                parentParams.putAll(params);
            }
            parentParams.put("subtemplate", childTemplate);

            // 根据语言选择父模板
            String parentTemplateContent;
            if (EmailLanguageEnum.isEnglish(languageCode)) {
                parentTemplateContent = parentTemplate.getEmailTemplateEn();
            } else {
                parentTemplateContent = parentTemplate.getEmailTemplate();
            }

            // 使用工具类进行模板替换
            return ReminderTemplateUtils.getReminderTemplate(parentParams, parentTemplateContent);

        } catch (Exception e) {
            log.error("处理父模板失败, languageCode: {}, 错误: {}", languageCode, e.getMessage(), e);
            return childTemplate;
        }
    }

    /**
     * 构建完整的邮件模板处理流程
     * 包括语言确定、模板选择、父模板处理
     *
     * @param params 邮件参数
     * @param chineseTemplate 中文模板
     * @param englishTemplate 英文模板
     * @param parentTemplate 父模板（可选）
     * @param permissionCenterClient 权限中心客户端
     * @return 处理后的邮件模板
     */
    public static String processCompleteEmailTemplate(Map<String, String> params, 
                                                     String chineseTemplate, 
                                                     String englishTemplate,
                                                     EmailTemplate parentTemplate,
                                                     IPermissionCenterClient permissionCenterClient) {
        // 1. 确定语言代码
        String languageCode = determineLanguageCode(params, permissionCenterClient);
        
        // 2. 选择模板
        String selectedTemplate = selectEmailTemplate(languageCode, chineseTemplate, englishTemplate);
        
        // 3. 进行参数替换
        String processedTemplate = ReminderTemplateUtils.getReminderTemplate(params, selectedTemplate);
        
        // 4. 处理父模板（如果存在）
        if (parentTemplate != null && parentTemplate.getFkParentEmailTemplateId() != null 
            && parentTemplate.getFkParentEmailTemplateId() != 0) {
            processedTemplate = processParentTemplate(languageCode, processedTemplate, parentTemplate, params);
        }
        
        return processedTemplate;
    }

    /**
     * 判断是否为英文语言
     *
     * @param languageCode 语言代码
     * @return true表示英文，false表示非英文
     */
    public static boolean isEnglish(String languageCode) {
        return EmailLanguageEnum.isEnglish(languageCode);
    }

    /**
     * 判断是否为中文语言
     *
     * @param languageCode 语言代码
     * @return true表示中文，false表示非中文
     */
    public static boolean isChinese(String languageCode) {
        return EmailLanguageEnum.isChinese(languageCode);
    }

    /**
     * 验证语言代码是否有效
     *
     * @param languageCode 语言代码
     * @return true表示有效，false表示无效
     */
    public static boolean isValidLanguageCode(String languageCode) {
        return EmailLanguageEnum.isValidLanguageCode(languageCode);
    }

    /**
     * 根据语言代码构建国际化标题
     *
     * @param languageCode 语言代码
     * @param chineseTitle 中文标题
     * @param englishTitle 英文标题
     * @return 对应语言的标题
     */
    public static String buildInternationalTitle(String languageCode, String chineseTitle, String englishTitle) {
        if (isEnglish(languageCode)) {
            return GeneralTool.isNotEmpty(englishTitle) ? englishTitle : chineseTitle;
        } else {
            return GeneralTool.isNotEmpty(chineseTitle) ? chineseTitle : englishTitle;
        }
    }

    /**
     * 获取默认语言代码
     *
     * @return 默认语言代码（中文）
     */
    public static String getDefaultLanguageCode() {
        return EmailLanguageEnum.CHINESE.getCode();
    }

    /**
     * 根据公司配置值获取语言代码
     *
     * @param configValue 公司配置值
     * @return 语言代码
     */
    public static String getLanguageCodeByConfigValue(String configValue) {
        return EmailLanguageEnum.getLanguageCodeByConfigValue(configValue);
    }

}