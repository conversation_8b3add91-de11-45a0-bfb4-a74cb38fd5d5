<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.EventPlanThemeOnlineMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.EventPlanThemeOnline">
        <id column="id" property="id" />
        <result column="fk_event_plan_theme_id" property="fkEventPlanThemeId" />
        <result column="name" property="name" />
        <result column="fk_currency_type_num" property="fkCurrencyTypeNum" />
        <result column="amount" property="amount" />
        <result column="unit" property="unit" />
        <result column="view_order" property="viewOrder" />
        <result column="is_active" property="isActive" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_create_user" property="gmtCreateUser" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="gmt_modified_user" property="gmtModifiedUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, fk_event_plan_theme_id, name, fk_currency_type_num, amount, unit, view_order, is_active, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
    </sql>

    <select id="getEventPlanThemeOnlines" resultType="com.get.salecenter.vo.EventPlanThemeOnlineVo">
        SELECT mept.main_title,mepto.* FROM  m_event_plan_theme_online AS mepto
        LEFT JOIN m_event_plan_theme AS mept ON mept.id = mepto.fk_event_plan_theme_id
        <where>
            <if test="fkEventPlanId != null and fkEventPlanId != ''">
                AND mept.fk_event_plan_id = #{fkEventPlanId}
            </if>
            <if test="displayType != null">
                AND mept.display_type = #{displayType}
            </if>
        </where>
        ORDER BY mept.view_order DESC,mepto.view_order DESC
    </select>

    <select id="getMaxViewOrder" resultType="java.lang.Integer">
        SELECT
        max(view_order)+1 view_order
        FROM
        m_event_plan_theme_online
        <where>
            AND fk_event_plan_theme_id = #{fkEventPlanThemeId}
        </where>
    </select>

</mapper>
