package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: Sea
 * @create: 2021/1/13 16:41
 * @verison: 1.0
 * @description:
 */
@Data
public class AreaCountryInfoTypeDto extends BaseVoEntity {
    /**
     * 类型名称
     */
    @NotBlank(message = "类型名称不能为空")
    @ApiModelProperty(value = "类型名称")
    private String typeName;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
}
