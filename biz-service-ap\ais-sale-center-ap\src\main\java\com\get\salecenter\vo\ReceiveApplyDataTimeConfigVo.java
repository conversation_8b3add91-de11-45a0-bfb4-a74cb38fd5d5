package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ReceiveApplyDataTimeConfigVo {

    // 开关配置
    @ApiModelProperty(value = "开关配置，0关/1开")
    private Boolean isSwitch = false;

    @ApiModelProperty(value = "收到申请资料时间")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date receivedApplicationDataDate;
}
