package com.get.aisplatformcenterap.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName u_live_type
 */
@TableName(value ="u_live_type")
@Data
public class ULiveTypeEntity extends BaseEntity implements Serializable {
    /**
     * 类型英文名称
     */
    private String typeName;

    /**
     * 类型中文名称
     */
    private String typeNameChn;

    /**
     * 类型key，枚举类型key
     */
    private String typeKey;

    /**
     * 排序，倒序，数字由大小排列
     */
    private Integer viewOrder;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}