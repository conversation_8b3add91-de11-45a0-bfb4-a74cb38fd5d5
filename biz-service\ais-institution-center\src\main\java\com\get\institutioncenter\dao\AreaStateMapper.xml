<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.AreaStateMapper">

  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.AreaState" keyProperty="id" useGeneratedKeys="true">
    insert into u_area_state
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkAreaCountryId != null">
        fk_area_country_id,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="nameChn != null">
        name_chn,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCountryId != null">
        #{fkAreaCountryId,jdbcType=BIGINT},
      </if>
      <if test="num != null">
        #{num,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="nameChn != null">
        #{nameChn,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getStateNameById" parameterType="java.lang.Long" resultType="java.lang.String">
    select
      name
    from
      u_area_state
    where
      id = #{id}
  </select>
  <select id="getStateFullNameById" parameterType="java.lang.Long" resultType="java.lang.String">
    select
      CASE WHEN IFNULL(name_chn,'')='' THEN `name` ELSE CONCAT(`name`,'（',name_chn,'）') END fullName
    from
      u_area_state
    where
      id = #{id}
  </select>

  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select
     IFNULL(max(view_order)+1,0) view_order
    from
     u_area_state

  </select>
  <select id="selectAreaStateByVo" parameterType="com.get.institutioncenter.dto.AreaCityDto"
          resultType="com.get.institutioncenter.entity.AreaState">
    select
    id, fk_area_country_id, num, name, name_chn, remark, view_order, gmt_create,
    gmt_create_user, gmt_modified, gmt_modified_user
    from u_area_state
    <where>
      <if test="areaStateDto.fkAreaCountryId!=null and areaStateDto.fkAreaCountryId !=''">
        and fk_area_country_id = #{areaStateDto.fkAreaCountryId}
      </if>
      <if test="areaStateDto.keyWord!=null and areaStateDto.keyWord !=''">
        and (num like concat("%",#{areaStateDto.keyWord},"%") or name like concat("%",#{areaStateDto.keyWord},"%") or name_chn like
        concat("%",#{areaStateDto.keyWord},"%"))
      </if>
    </where>
    order by fk_area_country_id, IFNULL(view_order,0) DESC, CONVERT(name USING gbk)
  </select>
  <select id="getCountryIdsByStateIds" resultType="com.get.institutioncenter.entity.AreaState">
    SELECT * FROM u_area_state WHERE
    id IN
    <foreach collection="fkAreaStateIds" item="fkAreaStateId" open="(" separator="," close=")">
    #{fkAreaStateId}
    </foreach>
  </select>
    <select id="getExistsAgentOfferItemAreaStateList"
            resultType="com.get.core.mybatis.base.BaseSelectEntity">
      SELECT
        uas.id,
        uas.num,
        uas.NAME,
        uas.name_chn,
        CASE

          WHEN IFNULL( uas.name_chn, '' )= '' THEN
            uas.NAME ELSE CONCAT( uas.NAME, '（', uas.name_chn, '）' )
          END fullName
      FROM
        u_area_state AS uas
          INNER JOIN (
          SELECT
            a.fk_area_state_id
          FROM
            ais_sale_center.m_student_offer_item AS msoi
              INNER JOIN ais_sale_center.m_student AS ms ON ms.id = msoi.fk_student_id
              INNER JOIN ais_sale_center.m_agent AS a ON  a.id = msoi.fk_agent_id
              LEFT JOIN ais_sale_center.r_staff_bd_code AS rsbc ON rsbc.fk_staff_id = msoi.fk_staff_id
          WHERE
            ms.fk_company_id = #{companyId} AND msoi.STATUS = 1 AND rsbc.bd_code NOT LIKE 'T%'
          AND msoi.fk_area_country_id IN
          <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">
            #{countryId}
          </foreach>
          GROUP BY
            a.fk_area_state_id ) a ON a.fk_area_state_id = uas.id
      where uas.fk_area_country_id = #{countryId}
      ORDER BY
        view_order DESC
    </select>
    <select id="getExistsAgentAreaStateList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
      SELECT
        uas.id,
        uas.num,
        uas.NAME,
        uas.name_chn,
        CASE

          WHEN IFNULL( uas.name_chn, '' )= '' THEN
            uas.NAME ELSE CONCAT( uas.NAME, '（', uas.name_chn, '）' )
          END fullName
      FROM
        u_area_state AS uas
      INNER JOIN ais_sale_center.m_agent AS a ON a.fk_area_state_id = uas.id
      INNER JOIN ais_sale_center.r_agent_company AS rac ON rac.fk_agent_id = a.id
      where uas.fk_area_country_id = #{countryId}
      AND rac.fk_company_id = #{companyId}

      GROUP BY uas.id
      ORDER BY
        view_order DESC
    </select>
</mapper>