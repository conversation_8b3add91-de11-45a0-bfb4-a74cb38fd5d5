<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.SettlementRefundReasonMapper">


    <select id="getMaxOrder" resultType="java.lang.Integer">
        SELECT IFNULL(MAX(view_order),0) FROM u_settlement_refund_reason
    </select>
    <select id="getServiceTypeList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            id,
            reason_name as name
        FROM
            u_settlement_refund_reason
        ORDER BY view_order DESC
    </select>

</mapper>