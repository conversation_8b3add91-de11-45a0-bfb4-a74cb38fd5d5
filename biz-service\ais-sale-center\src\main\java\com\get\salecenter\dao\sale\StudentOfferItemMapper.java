package com.get.salecenter.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.institutioncenter.bo.InstitutionApplicationStaticsQueryBo;
import com.get.institutioncenter.dto.CaseStudyResultsDto;
import com.get.institutioncenter.dto.InstitutionApplicationMetricsDto;
import com.get.institutioncenter.dto.NewEmailGetAgentDto;
import com.get.institutioncenter.vo.ContractFormulaFeignVo;
import com.get.institutioncenter.vo.InstitutionApplicationMetricsVo;
import com.get.institutioncenter.vo.InstitutionApplicationStatisticsVo;
import com.get.salecenter.bo.AgentApplicationRankingQueryBo;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.query.StudentOfferItemListQueryDto;
import com.get.salecenter.dto.query.StudentReceivableAndPaySumQueryDto;
import com.get.salecenter.entity.EnrolFailureReason;
import com.get.salecenter.entity.KpiPlan;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.entity.StudentOfferItemStep;
import com.get.salecenter.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Mapper
public interface StudentOfferItemMapper extends BaseMapper<StudentOfferItem>, GetMapper<StudentOfferItem> {
    int insertSelective(StudentOfferItem record);

    int insert(StudentOfferItem record);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 查询某个学生下的所有项目
     * @Param [studentId]
     * <AUTHOR>
     */
    List<Long> getItemIdByStudentId(@Param("studentId") Long studentId);


    List<SelItem> getOfferItemByTargetIds(@Param("ids") Set<Long> ids);


    List<Long> getStudentOfferItemByPayIds(@Param("ids") Set<Long> ids);

    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [agentId]
     * <AUTHOR>
     */
    Boolean isExistByOfferId(Long offerId);

    /**
     * @return void
     * @Description: 修改状态
     * @Param [offerId, status]
     * <AUTHOR>
     */
    void updateStatusByOfferId(@Param("offerId") Long offerId, @Param("status") Long status);

    /**
     * @return java.lang.String
     * @Description: 根据项目id查询学生名称
     * @Param [itemId]
     * <AUTHOR>
     */
    String getStudentNameByItemId(Long itemId);

    /**
     * @return java.lang.String
     * @Description: 根据项目id查询学生ID
     * @Param [itemId]
     * <AUTHOR>
     */
    Long getStudentIdByItemId(Long itemId);

    /**
     * @return java.lang.String
     * @Description: 根据项目id查询代理名称
     * @Param [itemId]
     * <AUTHOR>
     */
    String getAgentNameByItemId(Long itemId);

    /**
     * @return java.util.List<com.get.salecenter.vo.StudentOfferItemVo>
     * @Description: 获取应收下拉
     * @Param [providerId]
     * <AUTHOR>
     */
    List<StudentOfferItemVo> getItemByProviderId(Long providerId);

    /**
     * @return java.util.List<com.get.salecenter.vo.StudentOfferItemVo>
     * @Description: 获取应收下拉
     * @Param [providerId]
     * <AUTHOR>
     */
    List<StudentOfferItemVo> getItemByAgentId(@Param("agentId") Long agentId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 获取合同公式课程id
     * @Param [providerId, courseIds]
     * <AUTHOR>
     **/
    List<Long> getCourseIdByProviderAndCourse(@Param(value = "providerId") Long providerId, @Param(value = "courseIds") List<Long> courseIds);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据公司id查询
     * @Param [companyId]
     * <AUTHOR>
     */
    List<Long> getItemIdByCompanyId(Long companyId);


    /**
     * @return java.util.List<com.get.salecenter.vo.StudentCountVo>
     * @Description: 首页统计学生数量
     * @Param [companyId]
     * <AUTHOR>
     */
    @DS("saledb-doris")
    List<StudentCountVo> getStudentCount(@Param("companyIds") List<Long> companyIds,
                                         @Param("areaCountryIds") List<Long> areaCountryIds,
                                         @Param("year") String year,
                                         @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                         @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                         @Param("isStudentAdmin") Boolean isStudentAdmin,
                                         @Param("isBd") Boolean isBd,
                                         @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                         @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * @return java.util.List<com.get.salecenter.vo.StudentCountVo>
     * @Description: 首页统计学生数量通过计划id
     * @Param [companyId]
     * <AUTHOR>
     */
    StudentCountVo getStudentCountByItemId(@Param("itemId") Long itemId);

    /**
     * @return java.lang.Long
     * @Description: 获取学生申请总数
     * @Param [companyId]
     * <AUTHOR>
     */
    @DS("saledb-doris")
    Long getStudentTotalSum(@Param("companyIds") List<Long> companyIds, @Param("areaCountryIds") List<Long> areaCountryIds,
                            @Param("year") String year, @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                            @Param("staffFollowerIds") List<Long> staffFollowerIds, @Param("isStudentAdmin") Boolean isStudentAdmin, @Param("isBd") Boolean isBd,
                            @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                            @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    @DS("saledb-doris")
    Long getStudentItemTotalSum(@Param("companyIds") List<Long> companyIds, @Param("areaCountryIds") List<Long> areaCountryIds,
                                @Param("year") String year, @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                @Param("staffFollowerIds") List<Long> staffFollowerIds, @Param("isStudentAdmin") Boolean isStudentAdmin, @Param("isBd") Boolean isBd,
                                @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                @Param("staffBoundBdIds") List<Long> staffBoundBdIds);


    /**
     * @return java.util.List<com.get.salecenter.vo.WorldMapVo>
     * @Description: 获取世界地图
     * @Param [statisticsFlag  true:统计申请计划数  false:统计学生数]
     * <AUTHOR>
     **/
    @DS("saledb-doris")
    List<WorldMapVo> getWorldMapDtos(@Param("companyIds") List<Long> companyIds, @Param("areaCountryIds") List<Long> areaCountryIds, @Param("year") String year, @Param("statisticsFlag") Boolean statisticsFlag,
                                     @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding, @Param("staffFollowerIds") List<Long> staffFollowerIds, @Param("isStudentAdmin") Boolean isStudentAdmin, @Param("isBd") Boolean isBd,
                                     @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                     @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * 统计类型专用方法 获取符合合同公式条件的学生学习计划
     *
     * @Date 11:53 2021/6/15
     * <AUTHOR>
     */
    List<StudentOfferItem> getOferItemByContractFormula(@Param("contractFormulaFeignDto") ContractFormulaFeignVo contractFormulaFeignVo);

    /**
     * @return com.get.salecenter.entity.EnrolFailureReason
     * @Description:入学失败原因下拉数据
     * @Param []
     * <AUTHOR>
     */
    List<EnrolFailureReason> getEnrolFailureReason();


    void saveOfferItem(StudentOfferItem studentOfferItem);

    /**
     * @return java.lang.Long
     * @Description: 根据学生申请方案项目Id和学生申请方案项目状态步骤Id
     * 获取学生申请方案项目与步骤关联表Id
     * @Param [fkStudentOfferItemId, fkStudentOfferItemStepId]
     * <AUTHOR>
     */
    Long getRstudentOfferItemId(@Param("fkStudentOfferItemId") Long fkStudentOfferItemId, @Param("fkStudentOfferItemStepId") Long fkStudentOfferItemStepId);

    /**
     * 学习计划学校tab
     *
     * @Date 16:30 2021/7/7
     * <AUTHOR>
     */
    List<OfferItemInstitutionTabVo> getOfferItemInstitutionTab(@Param("offerId") Long offerId,
                                                               @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                               @Param("staffId") Long staffId,
                                                               @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                               @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                               @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                               @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * @Description: 根据申请方案ids获取学习计划学校tab
     * @Author: Jerry
     * @Date:14:08 2021/8/17
     */
    List<InstitutionTabVo> getOfferItemInstitutionTabByOfferIds(@Param("offerIds") Set<Long> offerIds, @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding);

    /**
     * 根据学生ids查询最新学习计划状态
     *
     * @Date 11:41 2021/7/8
     * <AUTHOR>
     */
    List<Map<String, Object>> getMinStepOrderByStudentId(@Param("studentIds") List<Long> studentIds, @Param("states") List<Long> states);


    /**
     * 根据学生ids查询所有学习计划状态
     *
     * @param studentIds
     * @return
     */
    List<Map<String, Object>> getStepOrderByStudentId(@Param("studentIds") List<Long> studentIds, @Param("fkAreaCountryIds") List<Long> fkAreaCountryIds);

    List<SelItem> getStepByIds(@Param("ids") Set<Long> ids);

    /**
     * 根据学生ids查询代理和BD
     *
     * @param studentIds
     * @return
     */
    @DS("saledb-doris")
    List<Map<String, Object>> getAgentBdByStudentId(@Param("studentIds") List<Long> studentIds);

    List<BaseSelectEntity> getHistoryCommissionRate(@Param("fkInstitutionId") Long fkInstitutionId, @Param("fkInstitutionCourseId") Long fkInstitutionCourseId, @Param("flag") Boolean flag);

    /**
     * 获取学习计划状态变更最新时间
     *
     * @param studentIds
     * @return
     */
    @DS("saledb-doris")
    List<Map<String, Object>> getStatusChangeLastTime(@Param("studentIds") List<Long> studentIds);

    /**
     * 学生步骤状态数据列表
     *
     * @Date 11:33 2021/7/21
     * <AUTHOR>
     */
    List<StudentOfferItemVo> getStudentsStepState(@Param("companyId") Long companyId,
                                                  @Param("studentIds") List<Long> studentIds,
                                                  @Param("userNames") List<String> userNames,
                                                  @Param("beginTime") Date beginTime,
                                                  @Param("endTime") Date endTime,
                                                  @Param("staffId") Long staffId,
                                                  @Param("areaCountryIds") List<Long> areaCountryIds,
                                                  @Param("countryIds") List<Long> countryIds);

    /**
     * 学生步骤状态数据列表New
     *
     * @Date 11:33 2021/7/21
     * <AUTHOR>
     */
    List<StudentOfferItemStatisticalStatusVo> getStudentsStepStateNew(@Param("companyId") Long companyId,
                                                                      @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                                      @Param("beginTime") Date beginTime,
                                                                      @Param("endTime") Date endTime,
                                                                      @Param("fkStaffId") Long fkStaffId,
                                                                      @Param("fkAreaCountryIds") List<Long> fkAreaCountryIds,
                                                                      @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                                      @Param("staffBoundBdIds") List<Long> staffBoundBdIds);


    /**
     * 学习计划汇总表
     *
     * @param studentOfferItemDto
     * @param staffFollowerIds
     * @return
     */
    List<StudentOfferItemListVo> getStudentOfferItemDtoList(@Param("studentOfferItemDto") StudentOfferItemDto studentOfferItemDto, List<Long> staffFollowerIds);

    /**
     * 学习计划汇总表NEW
     *
     * @param studentOfferItemDto
     * @return
     */
    List<StudentOfferItemListVo> getStudentOfferItemDtoListNew(IPage<StudentOfferItemListVo> iPage, @Param("studentOfferItemDto") StudentOfferItemDto studentOfferItemDto, @Param("staffFollowerIds") List<Long> staffFollowerIds);

    /**
     * 客户列表
     *
     * @return
     */
    @DS("saledb-doris")
    List<StudentOfferItemListVo> getCustomersList(@Param("studentOfferItemDto") StudentOfferItemListQueryDto studentOfferItemVo);

    /**
     * 成功客户列表(市场部)
     *
     * @param studentOfferItemVo
     * @param isStudentOfferItemFinancialHiding
     * @param isStudentAdmin
     * @return
     */
    @DS("saledb-doris")
    List<StudentOfferItemListVo> getSuccessfulCustomersListForMarket(@Param("areaCountryIds") List<Long> areaCountryIds, @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                                     @Param("studentOfferItemDto") StudentOfferItemListQueryDto studentOfferItemVo, @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                                     @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                                     @Param("institutionIds") List<Long> institutionIds,
                                                                     @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                                     @Param("staffBoundBdIds") List<Long> staffBoundBdIds);


    /**
     * 成功客户列表 导出
     *
     * @param studentOfferItemDto
     * @return
     */
//    @DS("saledb-doris")
    List<StudentOfferItemListVo> getCustomersListExport(@Param("studentOfferItemDto") StudentOfferItemDto studentOfferItemDto);


    /**
     * 根据itemIds获取学生
     *
     * @param itemIds
     * @return
     */
    List<StudentVo> getStudentByItemIds(@Param("itemIds") String itemIds);

    /**
     * @param studentReceivableAndPaySumVo
     * @return
     */
    //TODO 数据库连接
//    @DS("saledb-doris")
    List<StudentReceivableAndPaySumVo> getStudentReceivableAndPaySumDatas(IPage<StudentReceivableAndPaySumVo> iPage, @Param("studentReceivableAndPaySumDto") StudentReceivableAndPaySumQueryDto studentReceivableAndPaySumVo);

    /**
     * 获取学习计划当前申请步骤
     *
     * @param fkStudentOfferItemId
     * @return
     */
    StudentOfferItemStep getStudentOfferItemStep(@Param("fkStudentOfferItemId") Long fkStudentOfferItemId);

    /**
     * feign 提交财务结算汇总 - 保险
     *
     * @Date 17:27 2021/12/24
     * <AUTHOR>
     */
    List<PayablePlanVo> submitFinancialSettlementSummaryByInsurance(@Param("agentId") Long agentId,
                                                                    @Param("planCurrencyNum") String planCurrencyNum,
                                                                    @Param("accountCurrencyNum") String accountCurrencyNum);

    /**
     * feign 提交财务结算汇总 - 住宿
     *
     * @Date 17:27 2021/12/24
     * <AUTHOR>
     */
    List<PayablePlanVo> submitFinancialSettlementSummaryByAccommodation(@Param("agentId") Long agentId,
                                                                        @Param("planCurrencyNum") String planCurrencyNum,
                                                                        @Param("accountCurrencyNum") String accountCurrencyNum);

    /**
     * feign 提交财务结算汇总 - 服务费
     *
     * @Date 17:27 2021/12/24
     * <AUTHOR>
     */
    List<PayablePlanVo> submitFinancialSettlementSummaryByServiceFee(@Param("agentId") Long agentId,
                                                                     @Param("planCurrencyNum") String planCurrencyNum,
                                                                     @Param("accountCurrencyNum") String accountCurrencyNum);


    /**
     * 判断该财务批次是否有绑定过收款单
     *
     * @Date 9:57 2021/12/28
     * <AUTHOR>
     */
    boolean isExistPaymentFormByNumSettlementBatch(@Param("numSettlementBatch") String numSettlementBatch);

    /**
     * 根据 公司id 省份id 年份获取对应的学习计划数
     *
     * @Date 12:42 2022/1/4
     * <AUTHOR>
     */
    @DS("saledb-doris")
    List<SelCountVo> getCountByState(@Param("companyIds") List<Long> companyIds, @Param("areaCountryIds") List<Long> areaCountryIds, @Param("ids") List<Long> ids,
                                     @Param("year") String year,
                                     @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                     @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                     @Param("isStudentAdmin") Boolean isStudentAdmin,
                                     @Param("isBd") Boolean isBd,
                                     @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                     @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * 根据学生申请方案项目父获取学习方案项目列表
     *
     * @param fkParentStudentOfferItemIds
     * @param staffId
     * @return
     */
    List<StudentOfferItem> getStudentOfferItemByParentIds(@Param("fkParentStudentOfferItemIds") Set<Long> fkParentStudentOfferItemIds,
                                                          @Param("staffId") Long staffId);

    /**
     * 根据 公司id 城市id 年份获取对应的学习计划数
     *
     * @Date 14:57 2022/1/4
     * <AUTHOR>
     */
    @DS("saledb-doris")
    Integer getCountByCity(@Param("companyIds") List<Long> companyIds, @Param("id") Long id, @Param("year") String year,
                           @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                           @Param("staffFollowerIds") List<Long> staffFollowerIds,
                           @Param("isStudentAdmin") Boolean isStudentAdmin,
                           @Param("isBd") Boolean isBd,
                           @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                           @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * 首页年份下拉框
     *
     * @Date 17:05 2022/1/4
     * <AUTHOR>
     */
    List<BaseSelectEntity> selectYear(@Param("companyIds") List<Long> companyIds);

    /**
     * 获取学习计划ids
     *
     * @param receivablePlanDto
     * @return
     */
    List<Long> getStudentOfferItemIdsByVo(@Param("receivablePlanDto") ReceivablePlanDto receivablePlanDto);

    /**
     * 公司ids
     *
     * @param ids
     * @return
     */
    List<StudentOfferItemVo> getCompanyIdsByItemIds(@Param("ids") List<Long> ids);




    /**
     * 根据学习计划id获取绑定过收款单的应收计划ids
     *
     * @Date 18:54 2022/1/25
     * <AUTHOR>
     */
    List<Long> selectPayablePlanIdsByItemId(@Param("offerItemId") Long offerItemId);

    /**
     * 根据学习计划id获取绑定过收款单的应付计划ids
     *
     * @Date 18:54 2022/1/25
     * <AUTHOR>
     */
    List<Long> selectReceivablePlanIdsByItemId(@Param("offerItemId") Long offerItemId);

    /**
     * 根据课程id验证是否存在
     *
     * @return
     */
    boolean isExistByCourseId(@Param("courseId") Long courseId);

    /**
     * 根据学生ids获取对应的学习计划
     *
     * @Date 16:17 2022/2/9
     * <AUTHOR>
     */
    @DS("saledb-doris")
    List<StudentOfferItemVo> selectOfferItemByStudentIds(@Param("studentIdSet") Set<Long> studentIdSet, @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding);

    /**
     * 查询最大延迟入学时间
     *
     * @param itemIds
     * @return
     */
    List<StudentOfferItemVo> getMaxDeferEntranceTime(@Param("itemIds") Set<Long> itemIds);

    /**
     * 根据父id获取子学习计划
     *
     * @param id
     * @return
     */
    List<StudentOfferItem> getChildStudentOfferItem(@Param("id") Long id);

    /**
     * 判断当前学习计划以及子计划是否存在“有效”的应收应付计划
     *
     * @param id
     * @return
     */
    Boolean hasPlan(@Param("id") Long id);

    /**
     * 根据学习计划ids以及子计划ids判断是否存在“有效”的应收应付计划
     *
     * @param itemIds
     * @return
     */
    Boolean hasPlansByItemIds(@Param("itemIds") Set<Long> itemIds);


    /**
     * 判断当前记录步骤是否为回退操作
     *
     * @return
     */
    Boolean isBackStep(@Param("fkStudentOfferItemId") Long fkStudentOfferItemId,
                       @Param("fkStudentOfferItemStepId") Long fkStudentOfferItemStepId);

//
//    List<StudentOfferItem> getChangeStudentOfferItemChild(@Param("id") Long id);

    /**
     * 获取子项目
     *
     * @param id
     * @return
     */
    List<StudentOfferItem> getItemsByParentId(@Param("id") Long id);

    /**
     * 删除子项目
     *
     * @param id
     * @return
     */
    void deleteItemsByParentId(@Param("id") Long id);

    /**
     * 检查前置条件
     *
     * @Date 18:24 2022/3/9
     * <AUTHOR>
     */
    List<Long> checkPreFront(@Param("itemId") Long itemId,
                             @Param("contractFormulaPreInstitutionIds") List<Long> contractFormulaPreInstitutionIds,
                             @Param("contractFormulaPreInstitutionGroupIds") List<Long> contractFormulaPreInstitutionGroupIds,
                             @Param("preMajorLevelIds") List<Long> preMajorLevelIds);


    //更新学习计划学费
    void updateTuitionAmountById(@Param("id") Long id, @Param("tuitionAmount") BigDecimal tuitionAmount);

    /**
     * @ Description :查找有一键的课程
     * @ Param [orderId, nameEn, nameZh]
     * @ return java.util.List<com.get.salecenter.vo.AplOrderVo>
     * @ author LEO
     */
    List<AplOrderVo> getRpaTableList(IPage<AplOrderVo> ipage, @Param("orderId") Integer orderId, @Param("nameEn") String nameEn,
                                     @Param("nameZh") String nameZh,
                                     @Param("staffIdsByNameKey") List<Long> staffIdsByNameKey,
                                     @Param("itemId") List<Long> itemId,
                                     @Param("fkCompanyId") Long fkCompanyId);

    @DS("saledb-doris")
    List<AplOrderVo> getRpaTableListCondition(IPage<AplOrderVo> ipage, @Param("orderId") Integer orderId,
                                              @Param("name") String name,
                                              @Param("staffIdsByNameKey") List<Long> staffIdsByNameKey,
                                              @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                              @Param("fkCompanyIds") List<Long> fkCompanyId, @Param("staffCountrys") List<Long> staffCountrys,
                                              @Param("isStudentAdmin") Boolean isStudentAdmin,
                                              @Param("fkContactPersonEmail") String fkContactPersonEmail,
                                              @Param("institutionIds") List<Long> institutionIds,
                                              @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                              @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                              @Param("staffBoundBdIds") List<Long> staffBoundBdIds);


    List<StudentProjectRoleStaffVo> getProjectName(@Param("itemIds") List<Long> itemIds);


    /**
     * 查询带分页
     *
     * @param staffFollowerIds
     * @param index
     * @param count
     * @param isStudentOfferItemFinancialHiding
     * @param isStudentAdmin
     * @param staffId
     * @param beginOpenTime
     * @param endOpenTime
     * @return
     */
    @DS("saledb-doris")
    List<StudentOfferItemListVo> getStudentOfferItemDtoListNewByLimit(@Param("studentOfferItemDto") StudentOfferItemCollectDto studentOfferItemVo,
                                                                      @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                                      @Param("areaCountryIds") List<Long> areaCountryIds,
                                                                      @Param("index") Integer index,
                                                                      @Param("count") Integer count,
                                                                      @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                                      @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                                      @Param("sort") String sort,
                                                                      @Param("staffId") Long staffId,
                                                                      @Param("beginOpenTime") Date beginOpenTime,
                                                                      @Param("endOpenTime") Date endOpenTime,
                                                                      @Param("bdStudentStatisticalComparisonDto") BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonDto,
                                                                      @Param("beginTime") Date jumpBeginTime,
                                                                      @Param("endTime") Date jumpEndTime,
                                                                      @Param("institutionIds") List<Long> institutionIds,
                                                                      @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                                      @Param("staffBoundBdIds") List<Long> staffBoundBdIds);


    @DS("saledb-doris")
    List<StudentOfferItemListVo> getStudentOfferItemDtoListNew2(@Param("studentOfferItemDto") StudentOfferItemCollectDto studentOfferItemVo,
                                                                @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                                @Param("areaCountryIds") List<Long> areaCountryIds,
                                                                @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                                @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                                @Param("staffId") Long staffId,
                                                                @Param("beginOpenTime") Date beginOpenTime,
                                                                @Param("endOpenTime") Date endOpenTime,
                                                                @Param("bdStudentStatisticalComparisonDto") BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonDto,
                                                                @Param("beginTime") Date jumpBeginTime,
                                                                @Param("endTime") Date jumpEndTime,
                                                                @Param("institutionIds") List<Long> institutionIds,
                                                                @Param("isBd") Boolean isBd,
                                                                @Param("delayConfig") DelayConfigDto delayConfig,
                                                                @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                                @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    List<Map<String, Object>> getOfferAgentBdByStudentId(@Param("studentIds") List<Long> studentIds);

    /**
     * 根据ids获取对应列表
     *
     * @param offerItemIds
     * @return
     */
    List<StudentOfferItemVo> getStudentOfferItemDtoListByIds(@Param("offerItemIds") List<Long> offerItemIds);


    /**
     * 学生申请统计（新建学生、处理申请（含加申）、定校量（按学校）、成功入学量（按学校））
     *
     * @param statisticsDto
     * @param isStudentOfferItemFinancialHiding
     * @param isStudentAdmin
     * @param staffFollowerIds
     * @return
     */
    @DS("saledb-doris")
    List<StudentApplicationStatisticsVo> getStudentApplicationStatistics(@Param("statisticsDto") StatisticsDto statisticsDto,
                                                                         @Param("type") String type,
                                                                         @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                                         @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                                         @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                                         @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                                         @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * 代理送生统计(申请量（按学生）、成功量（按学生）)
     *
     * @param statisticsDto
     * @param isStudentOfferItemFinancialHiding
     * @param isStudentAdmin
     * @param staffFollowerIds
     * @return
     */
    @DS("saledb-doris")
    List<StudentApplicationStatisticsVo> getAgentStateApplicationStatisticsByStudent(@Param("statisticsDto") StatisticsDto statisticsDto,
                                                                                     @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                                                     @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                                                     @Param("type") String type,
                                                                                     @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                                                     @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                                                     @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * 学生申请统计(申请量（按学生）、成功量（按学生）)
     *
     * @param statisticsDto
     * @param isStudentOfferItemFinancialHiding
     * @param isStudentAdmin
     * @param staffFollowerIds
     * @return
     */
    @DS("saledb-doris")
    List<StudentApplicationStatisticsVo> getStudentApplicationStatisticsByStudent(@Param("statisticsDto") StatisticsDto statisticsDto,
                                                                                  @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                                                  @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                                                  @Param("type") String type,
                                                                                  @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                                                  @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                                                  @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * 代理申请排行
     *
     * @param
     * @param isStudentOfferItemFinancialHiding
     * @param isStudentAdmin
     * @param staffFollowerIds
     * @return
     */
    @DS("saledb-doris")
    List<AgentApplicationRankingVo> getAgentApplicationRanking(@Param("agentApplicationRankingDto") AgentApplicationRankingQueryBo agentApplicationRankingQueryBo,
                                                               @Param("type") String type,
                                                               @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                               @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                               @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                               @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                               @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * 代理申请排行按学生
     *
     * @param
     * @param isStudentOfferItemFinancialHiding
     * @param isStudentAdmin
     * @param staffFollowerIds
     * @return
     */
    @DS("saledb-doris")
    List<AgentApplicationRankingVo> getAgentApplicationRankingByStudent(@Param("agentApplicationRankingDto") AgentApplicationRankingQueryBo agentApplicationRankingQueryBo,
                                                                        @Param("type") String type,
                                                                        @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                                        @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                                        @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                                        @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                                        @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * @Description:VIP统计
     * <AUTHOR>
     * @Date 16:03 2022/11/11
     **/
    @DS("saledb-doris")
    List<VipStatisticsVo> getVipStatistics(@Param("vipStatisticsDto") VipStatisticsDto vipStatisticsDto,
                                           @Param("type") String type,
                                           @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                           @Param("isStudentAdmin") Boolean isStudentAdmin,
                                           @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                           @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                           @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * @return java.util.List<com.get.salecenter.vo.VipStatisticsVo>
     * <AUTHOR>
     * @Description VIP统计按学生
     * @Param [vipStatisticsDto]
     * @Date 2022/11/16 12:46
     */
    @DS("saledb-doris")
    List<VipStatisticsVo> getVipStatisticsByStudent(@Param("vipStatisticsDto") VipStatisticsDto vipStatisticsDto,
                                                    @Param("type") String type,
                                                    @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                    @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                    @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                    @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                    @Param("staffBoundBdIds") List<Long> staffBoundBdIds);


    /**
     * 代理业绩统计-国家申请分布图统计
     *
     * <AUTHOR>
     * @DateTime 2022/12/2 12:49
     */
    @DS("saledb-doris")
    List<CountryApplicationStatisticsVo> getCountryApplicationStatistics(@Param("agentPerformanceStatisticsDto") AgentPerformanceStatisticsDto agentPerformanceStatisticsDto,
                                                                         @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                                         @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                                         @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                                         @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                                         @Param("staffBoundBdIds") List<Long> staffBoundBdIds);


    /**
     * 代理业绩统计-申请分布图统计数据
     *
     * <AUTHOR>
     * @DateTime 2022/11/29 16:49
     */
    @DS("saledb-doris")
    AgentApplicationStatisticsVo getAgentCountryApplicationStatistics(@Param("agentPerformanceStatisticsDto") AgentPerformanceStatisticsDto agentPerformanceStatisticsDto,
                                                                      @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                                      @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                                      @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                                      @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                                      @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * 代理业绩统计-申请分布图统计（按学生）数据
     *
     * <AUTHOR>
     * @DateTime 2022/11/29 16:49
     */
    @DS("saledb-doris")
    AgentApplicationStatisticsVo getAgentCountryApplicationStatisticsByStudent(@Param("agentPerformanceStatisticsDto") AgentPerformanceStatisticsDto agentPerformanceStatisticsDto,
                                                                               @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                                               @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                                               @Param("type") String type,
                                                                               @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                                               @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                                               @Param("staffBoundBdIds") List<Long> staffBoundBdIds);


    /**
     * 学校申请统计 - 申请数
     *
     * <AUTHOR>
     * @DateTime 2022/12/8 17:05
     */
    @DS("saledb-doris")
    List<InstitutionApplicationStatisticsVo> institutionApplicationCount(@Param("institutionApplicationStaticsDto") InstitutionApplicationStaticsQueryBo staticsQueryBo,
                                                                         @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                                         @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                                         @Param("parentItemIsNull") Boolean parentItemIsNull,
                                                                         @Param("institutionIds") List<Long> institutionIds,
                                                                         @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                                         @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                                         @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * 学校申请统计 - 申请数小计
     *
     * <AUTHOR>
     * @DateTime 2023/3/21 16:32
     */
    @DS("saledb-doris")
    BigDecimal institutionApplicationSubtotal(@Param("institutionApplicationStaticsDto") InstitutionApplicationStaticsQueryBo staticsQueryBo,
                                              @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                              @Param("isStudentAdmin") Boolean isStudentAdmin,
                                              @Param("parentItemIsNull") Boolean parentItemIsNull,
                                              @Param("institutionIds") List<Long> institutionIds,
                                              @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                              @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                              @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * 学校申请统计 - 学生数
     *
     * <AUTHOR>
     * @DateTime 2022/12/9 12:24
     */
    @DS("saledb-doris")
    List<InstitutionApplicationStatisticsVo> institutionStudentCount(@Param("institutionApplicationStaticsDto") InstitutionApplicationStaticsQueryBo staticsQueryBo,
                                                                     @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                                     @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                                     @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                                     @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                                     @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * 学校申请统计 - 学生数小计
     *
     * <AUTHOR>
     * @DateTime 2022/12/9 12:24
     */
    @DS("saledb-doris")
    BigDecimal institutionStudentSubtotal(@Param("institutionApplicationStaticsDto") InstitutionApplicationStaticsQueryBo staticsQueryBo,
                                          @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                          @Param("isStudentAdmin") Boolean isStudentAdmin,
                                          @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                          @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                          @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * 学校申请统计 - 成功入学（学生数）
     *
     * <AUTHOR>
     * @DateTime 2022/12/9 12:28
     */
    @DS("saledb-doris")
    List<InstitutionApplicationStatisticsVo> institutionSuccesssStudentCount(@Param("institutionApplicationStaticsDto") InstitutionApplicationStaticsQueryBo staticsQueryBo,
                                                                             @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                                             @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                                             @Param("institutionIds") List<Long> institutionIds,
                                                                             @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                                             @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                                             @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * 学校申请统计 - 成功入学（学生数）小计
     *
     * <AUTHOR>
     * @DateTime 2022/12/9 12:28
     */
    @DS("saledb-doris")
    BigDecimal institutionSuccesssStudentSubtotal(@Param("institutionApplicationStaticsDto") InstitutionApplicationStaticsQueryBo staticsQueryBo,
                                                  @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                  @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                  @Param("institutionIds") List<Long> institutionIds,
                                                  @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                  @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                  @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * 学校入学年度转化率统计
     *
     * <AUTHOR>
     * @DateTime 2024/1/19 12:02
     */
    @DS("saledb-doris")
    List<EnrolledConversionRateStatisticsVo> getInstitutionEnrolledConversionRate(@Param("rateDto") InstitutionEnrolledConversionRateDto rateVo,
                                                                                  @Param("fkMajorLevelIdList") List<Long> fkMajorLevelIdList,
                                                                                  @Param("majorNameList") List<String> majorNameList,
                                                                                  @Param("fkCourseTypeGroupIdList") List<Long> fkCourseTypeGroupIdList,
                                                                                  @Param("courseTypeGroupNameList") List<String> courseTypeGroupNameList,
                                                                                  @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                                                  @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                                                  @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                                                  @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                                                  @Param("staffBoundBdIds") List<Long> staffBoundBdIds);


    /**
     * 获取KPI方案申请数统计相关的申请计划列表
     *
     * <AUTHOR>
     * @DateTime 2024/4/22 9:49
     */
    @DS("saledb-doris")
    List<KpiStudentOfferItemVo> getKpiPlanStudentOfferItemList(@Param("kpiPlanStudentOfferItemListDto") KpiPlanStudentOfferItemListDto kpiPlanStudentOfferItemListDto);

    /**
     * 在指定学习计划列表过滤项目角色对应学习计划
     *
     * <AUTHOR>
     * @DateTime 2024/5/7 17:44
     */
    @DS("saledb-doris")
    List<Long> filterProjectRoleStudentOfferItemList(@Param("kpiPlan") KpiPlan kpiPlan,
                                                     @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                     @Param("fkTableName") String fkTableName,
                                                     @Param("companyIds")Set<Long> companyIds);

    /**
     * 获取学习计划中所有学校id列表
     *
     * <AUTHOR>
     * @DateTime 2024/3/15 12:40
     */
    List<Long> getOfferItemInstitutionIdList(@Param("institutionSelectDto") InstitutionSelectDto institutionSelectDto);


    /**
     * 学生步骤状态数据列表New(按步骤状态变更时间统计)
     *
     * @Date 11:33 2021/7/21
     * <AUTHOR>
     */
    @DS("saledb-doris")
    List<StudentOfferItemStatisticalStatusVo> getStudentsRStepStateStu(@Param("companyId") Long companyId,
                                                                       @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                                       @Param("selectType") Integer selectType,
                                                                       @Param("beginTime") Date beginTime,
                                                                       @Param("endTime") Date endTime,
                                                                       @Param("fkStaffId") Long fkStaffId,
                                                                       @Param("fkAreaCountryIds") List<Long> fkAreaCountryIds,
                                                                       @Param("bdCode") String bdCode,
                                                                       @Param("fkProjectMemberName") String fkProjectMemberName,
                                                                       @Param("areaCountryIds") List<Long> areaCountryIds,
                                                                       @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                                       @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                                       @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * 学生步骤状态数据列表New
     *
     * @Date 11:33 2021/7/21
     * <AUTHOR>
     */
    @DS("saledb-doris")
    List<StudentOfferItemStatisticalStatusVo> getStudentsStepStateStu(@Param("companyId") Long companyId,
                                                                      @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                                      @Param("selectType") Integer selectType,
                                                                      @Param("beginTime") Date beginTime,
                                                                      @Param("endTime") Date endTime,
                                                                      @Param("fkStaffId") Long fkStaffId,
                                                                      @Param("fkAreaCountryIds") List<Long> fkAreaCountryIds,
                                                                      @Param("bdCode") String bdCode,
                                                                      @Param("fkProjectMemberName") String fkProjectMemberName,
                                                                      @Param("areaCountryIds") List<Long> areaCountryIds,
                                                                      @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                                      @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                                      @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    List<Long> getofferItemIdByOrderId(@Param("ids") List<Long> ids);

    List<StudentOfferItem> getCountItemId(@Param("staffFollowerIds") List<Long> staffFollowerIds, @Param("fkCompanyId") Long fkCompanyId,
                                          @Param("staffCountrys") List<Long> staffCountrys,
                                          @Param("isStudentAdmin") Boolean isStudentAdmin,
                                          @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                          @Param("staffBoundBdIds") List<Long> staffBoundBdIds);


    List<StudentOfferItemVo> getItemByChannelId(@Param("channelId") Long channelId);


    List<StudentOfferItemVo> getStudentNameByItemIds(@Param("itemIds") Set<Long> itemIds);


    List<StudentOfferItemVo> getAgentNameByOfferIds(@Param("offerIds") Set<Long> offerIds);


    List<StudentOfferItemVo> findOfferItemByIdsAndChannelId(@Param("itemIds") Set<Long> itemIds);


    List<StudentOfferItemVo> getOfferItemSelectByChannelId(@Param("channelId") Long channelId, @Param("receiptFormId") Long receiptFormId, @Param("offset") Integer offset, @Param("pageSize") Integer pageSize);

    List<StudentOfferItemVo> getOfferItemSelectByProviderIdNew(@Param("providerId") Long providerId, @Param("receiptFormId") Long receiptFormId, @Param("offset") Integer offset, @Param("pageSize") Integer pageSize);


    List<StudentOfferItem> getOfferItemByNameACompanyId(@Param("companyIds") List<Long> companyIds, @Param("name") String name);

    List<PayFormDetailVo> getPaidAmount(@Param("itemId") Long itemId);


    List<PayFormDetailVo> getPaidAmountByIds(@Param("itemIds") Set<Long> itemIds);

    /**
     * 判断当前学习计划绑定的“有效”的应收应付计划 有无实收实付
     *
     * @param fkStudentOfferItemId
     * @return
     */
    Boolean hasActualPlanAmount(@Param("fkStudentOfferItemId") Long fkStudentOfferItemId);

    List<StudentOfferItemParentCourseVo> getParentCourseFullName(@Param("ids") Set<Long> ids);

    /**
     * 获取后续课程名
     *
     * @param itemIds
     * @return
     */
    List<StudentOfferItemVo> getFollowCourseName(@Param("itemIds") Set<Long> itemIds);

    /**
     * 学生最终申请状态统计
     *
     * @Date 12:20 2022/6/30
     * <AUTHOR>
     */
    @DS("saledb-doris")
    List<CurrentStudentApplicationStatusVo> getCurrentStudentApplicationStatus(@Param("currentStudentApplicationStatusListDto") CurrentStudentApplicationStatusListDto currentStudentApplicationStatusListDto,
                                                                               @Param("fkAreaCountryIds") List<Long> fkAreaCountryIds,
                                                                               @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                                               @Param("failStepOrder") Integer failStepOrder,
                                                                               @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                                               @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                                               @Param("isBd") Boolean isBd,
                                                                               @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                                               @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    List<AgentVo> getAgentNameByItemIds(Set<Long> itemIds);

    /**
     * 我的学生申请状态统计
     *
     * @Date 14:37 2022/7/14
     * <AUTHOR>
     */
    @DS("saledb-doris")
    List<CurrentStudentApplicationStatusStatisticsVo> getCurrentStudentApplicationStatusStatistics(@Param("currentStudentApplicationStatusStatisticsDto") CurrentStudentApplicationStatusStatisticsDto currentStudentApplicationStatusStatisticsDto,
                                                                                                   @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                                                                   @Param("fkAreaCountryIds") List<Long> fkAreaCountryIds,
                                                                                                   @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                                                                   @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                                                                   @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                                                                   @Param("staffBoundBdIds") List<Long> staffBoundBdIds);


    /**
     * 我的学生申请状态统计 - 业绩统计
     *
     * @param currentStudentApplicationStatusStatisticsDto
     * @param staffFollowerIds
     * @param fkAreaCountryIds
     * @param isStudentOfferItemFinancialHiding
     * @param isStudentAdmin
     * @return
     */
    @DS("saledb-doris")
    List<CurrentStudentApplicationStatusStatisticsVo> getPerformanceStatusStatistics(@Param("currentStudentApplicationStatusStatisticsDto") CurrentStudentApplicationStatusStatisticsDto currentStudentApplicationStatusStatisticsDto,
                                                                                     @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                                                     @Param("fkAreaCountryIds") List<Long> fkAreaCountryIds,
                                                                                     @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                                                     @Param("isStudentAdmin") Boolean isStudentAdmin);


    Set<String> getCourseIdByNames(@Param("names") List<String> names);


    List<BaseSelectEntity> getStudentStudyPlanCountrySelect(Long companyId);

    List<BaseSelectEntity> getStudentStudyPlanSchoolSelect(@Param("companyId") Long companyId, @Param("id") Long id);


    /**
     * 案例统计列表
     *
     * @param page
     * @param statisticsSearchVo
     * @param isStudentOfferItemFinancialHiding
     * @return
     */
    List<StudentCourseStatisticsVo> getStudentCourseStatistics(IPage<StudentCourseStatisticsVo> page,
                                                               @Param("statisticsSearchDto") StudentCourseStatisticsSearchDto statisticsSearchVo,
                                                               @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                               @Param("institutionIds") List<Long> institutionIds);

    /**
     * 查询成功步骤个数
     *
     * @param page
     * @param statisticsSearchVo
     * @param isStudentOfferItemFinancialHiding
     * @return
     */
    List<Integer> getStudentOfferStepStatus(IPage<StudentCourseStatisticsVo> page
            , @Param("statisticsSearchVo") StudentCourseStatisticsSearchDto statisticsSearchVo,
                                            @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                            @Param("institutionIds") List<Long> institutionIds,
                                            @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                            @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    List<StudentCourseStatisticsVo> getStudentCourseStatisticsExportInfo(@Param("statisticsSearchDto") StudentCourseStatisticsSearchDto statisticsSearchVo, @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding);

    List<Long> rc(@Param("queryDto") RecommendedViewingQueryDto queryVo, @Param("suc") Boolean suc);

    List<RecommendedViewingVo> suc(@Param("queryDto") RecommendedViewingQueryDto queryVo, @Param("isStatistic") Boolean isStatistic);

    List<RecommendedViewingVo> details(@Param("queryDto") CaseStudyDetailsQueryDto queryVo);

    List<CaseStudyResultsDto.Statistics> getFailureReasonStatis(@Param("queryDto") RecommendedViewingQueryDto queryVo);

    Boolean existsPlan(@Param("id") Long id);

    List<String> getApplicationPlanTheLatestThreeTuitionFees(@Param("fkCompanyId") Long fkCompanyId, @Param("institutionId") Long institutionId,
                                                             @Param("courseId") Long courseId, @Param("oldCourseName") String oldCourseName);

    /**
     * BD学生统计对比
     *
     * @Date 14:12 2022/12/28
     * <AUTHOR>
     */
    @DS("saledb-doris")
    List<StudentStatistical> getBdStudentStatistical(@Param("bdStudentStatisticalComparisonDto") BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonQueryDto,
                                                     @Param("ids") List<Long> ids,
                                                     @Param("beginTime") Date beginTime,
                                                     @Param("endTime") Date endTime,
                                                     @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                     @Param("areaCountryIds") List<Long> areaCountryIds,
                                                     @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                     @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                     @Param("countryIds") List<Long> countryIds,
                                                     @Param("htiStartTime") Date htiStartTime,
                                                     @Param("isTotal") boolean isTotal,
                                                     @Param("delayConfig") DelayConfigDto delayConfig,
                                                     @Param("isBd") Boolean isBd,
                                                     @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                     @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * bd角色提成结算
     *
     * @Date 17:22 2023/3/10
     * <AUTHOR>
     */
    @DS("saledb-doris")
    List<CommissionPolicySettleAccountsVo> bdStaffCommissionPolicySettleAccounts(@Param("bdStudentStatisticalComparisonDto") BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonDto,
                                                                                 @Param("fkStaffCommissionStepKey") String fkStaffCommissionStepKey,
                                                                                 @Param("countryIds") List<Long> countryIds,
                                                                                 @Param("beginTime") Date beginTime,
                                                                                 @Param("endTime") Date endTime,
                                                                                 @Param("delayConfig") DelayConfigDto delayConfig);


    Set<Long> isEenrolledStep(@Param("offerIds") Set<Long> offerIds);

    /**
     * BD学生统计对比 bd分页
     *
     * @Date 10:08 2023/1/3
     * <AUTHOR>
     */
    @DS("saledb-doris")
    List<Long> selectBdStudentStatistical(IPage<Long> iPage,
                                          @Param("bdStudentStatisticalComparisonDto") BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonDto,
                                          @Param("statisticalComparisonYearDtoList") List<StatisticalComparisonYearDto> statisticalComparisonYearDtoList,
                                          @Param("countryIds") List<Long> countryIds,
                                          @Param("beginTime") Date beginTime,
                                          @Param("endTime") Date endTime,
                                          @Param("htiStartTime") Date htiStartTime);

    /**
     * bd学生统计对比表 无申请代理列表
     *
     * @Date 13:25 2023/1/4
     * <AUTHOR>
     */
    @DS("saledb-doris")
    List<BdStudentStatisticalComparisonVo> noApplicationAgencyList(IPage<BdStudentStatisticalComparisonVo> iPage,
                                                                   @Param("bdStudentStatisticalComparisonDto") BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonDto,
                                                                   @Param("statisticalComparisonYearDtoList") List<StatisticalComparisonYearDto> statisticalComparisonYearDtoList);

    /**
     * BD学生统计对比表 bd比对统计总计
     *
     * @Date 11:19 2023/1/6
     * <AUTHOR>
     */
    @DS("saledb-doris")
    List<StudentStatistical> getBdStudentStatisticalTotal(@Param("bdStudentStatisticalComparisonDto") BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonDto,
                                                          @Param("beginTime") Date beginTime,
                                                          @Param("endTime") Date endTime,
                                                          @Param("countryIds") List<Long> countryIds,
                                                          @Param("htiStartTime") Date htiStartTime);

    /**
     * BD学生统计对比表 bd比对统计 bd数总计
     *
     * @Date 11:19 2023/1/6
     * <AUTHOR>
     */
    @DS("saledb-doris")
    Long getBdStudentStatisticalBdTotal(@Param("bdStudentStatisticalComparisonDto") BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonDto,
                                        @Param("beginTime") Date beginTime,
                                        @Param("endTime") Date endTime,
                                        @Param("statisticalComparisonYearDtoList") List<StatisticalComparisonYearDto> statisticalComparisonYearDtoList,
                                        @Param("countryIds") List<Long> countryIds);

    /**
     * 获取BD学生统计对比 取基础数据，防止每年的国家数不一样
     *
     * @Date 18:45 2023/1/9
     * <AUTHOR>
     */
    @DS("saledb-doris")
    List<BdStudentStatisticalComparisonVo> getBdStudentStatisticalBasicData(@Param("bdStudentStatisticalComparisonDto") BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonDto,
                                                                            @Param("ids") List<Long> ids,
                                                                            @Param("statisticalComparisonYearDtoList") List<StatisticalComparisonYearDto> statisticalComparisonYearDtoList,
                                                                            @Param("countryIds") List<Long> countryIds,
                                                                            @Param("beginTime") Date beginTime,
                                                                            @Param("endTime") Date endTime,
                                                                            @Param("htiStartTime") Date htiStartTime);

    /**
     * BD学生统计对比表 bd比对统计总计 获取基础数据，防止每年的国家数不一样
     *
     * @Date 10:19 2023/1/10
     * <AUTHOR>
     */
    @DS("saledb-doris")
    List<BdStudentStatisticalComparisonVo> getBdStudentStatisticalTotalBasicData(@Param("bdStudentStatisticalComparisonDto") BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonDto,
                                                                                 @Param("beginTime") Date beginTime,
                                                                                 @Param("endTime") Date endTime,
                                                                                 @Param("statisticalComparisonYearDtoList") List<StatisticalComparisonYearDto> statisticalComparisonYearDtoList,
                                                                                 @Param("countryIds") List<Long> countryIds);

    @DS("saledb-doris")
    List<StudentReceivableAndPaySumDetailVo> getReceivableAndPayPaginationInfo(@Param("studentReceivableAndPaySumDto") StudentReceivableAndPaySumDto studentReceivableAndPaySumDto);


    /**
     * 代理年度总表对比统计 分页获取代理ids
     *
     * @Date 11:32 2023/3/16
     * <AUTHOR>
     */
    @DS("saledb-doris")
    List<AgentAnnualSummaryStatisticsVo> selectAgentAnnualSummaryStatistics(@Param("agentAnnualSummaryDto") AgentAnnualSummaryDto agentAnnualSummaryDto,
                                                                            @Param("statisticalComparisonYearDtoList") List<StatisticalComparisonYearDto> statisticalComparisonYearDtoList,
                                                                            @Param("countryIds") List<Long> countryIds,
                                                                            @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                                            @Param("institutionIds") List<Long> institutionIds);

    /**
     * 代理年度总表对比统计 获取数据
     *
     * @param :代理年度总表统计 false:代理时间对比统计
     * @Date 15:31 2023/3/16
     * <AUTHOR>
     */
    @DS("saledb-doris")
    List<AgentAnnualStatisticsVo> agentAnnualSummaryStatistics(@Param("agentAnnualSummaryDto") AgentAnnualSummaryDto agentAnnualSummaryDto,
                                                               @Param("countryIds") List<Long> countryIds,
                                                               @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                               @Param("institutionIds") List<Long> institutionIds);

    /**
     * 代理年度总表对比统计 获取数据 （按代理创建时间统计）
     *
     * @Date 10:15 2023/6/6
     * <AUTHOR>
     */
    @DS("saledb-doris")
    List<AgentAnnualStatisticsVo> agentAnnualSummaryStatisticsByCreateTime(@Param("agentAnnualSummaryDto") AgentAnnualSummaryDto agentAnnualSummaryDto,
                                                                           @Param("countryIds") List<Long> countryIds,
                                                                           @Param("staffFollowerIds") List<Long> staffFollowerIds);


    /**
     * 修改入学时间
     *
     * @param id
     * @param openTime
     */
    void updateOpenTime(@Param("id") Long id, @Param("openTime") Date openTime);

    Boolean existsFollow(@Param("id") Long id, @Param("beginOpenTime") Date beginOpenTime, @Param("endOpenTime") Date endOpenTime);

    StudentOfferItemVo selectCompanyById(@Param("id") Long id);

    Boolean getProviderInstitutionItem(@Param("fkInstitutionId") Long fkInstitutionId, @Param("fkInstitutionProviderId") Long fkInstitutionProviderId);

    /**
     * 通过申请计划ids获取学生
     *
     * @param itemIds
     * @return
     */
    List<StudentOfferItemVo> getStudentByOfferItemIds(@Param("itemIds") List<Long> itemIds);

    /**
     * 判断应收计划是否存在发票
     *
     * @Date 10:57 2023/7/26
     * <AUTHOR>
     */
    boolean existInvoiceByOfferItemId(@Param("id") Long id);

    List<OfferContactsVo> syncContacts();

    List<StudentOfferItemVo> selectEmailUpate();

    List<String> getAgentEmailSelect(@Param("fkAgentId") Long fkAgentId);

    /**
     * 学生最高申请步骤
     *
     * @param studentIds
     * @return
     */
    List<BaseSelectEntity> getMaxOfferItemByStudentIds(@Param("studentIds") List<Long> studentIds, @Param("countryId") Long countryId);


    Set<String> getNewAgentEmails(@Param("newEmailGetAgentDto") NewEmailGetAgentDto newEmailGetAgentDto);

    /**
     * 获取可发新闻的代理邮箱
     *
     * @return
     */
    Set<String> getNewAgentAllEmails();

    /**
     * 根据学生id获取所申请的国家列表
     *
     * @param studentId 学生id
     * @return
     */
    List<BaseSelectEntity> getAreaCountrySelect(@Param("studentId") Long studentId);

    /**
     * 根据条件查询学校申请统计指标
     *
     * @param metricsDto
     * @return
     */
    @DS("saledb-doris")
    List<InstitutionApplicationMetricsVo> getInstitutionApplicationMetrics(IPage<InstitutionApplicationMetricsVo> iPage,
                                                                           InstitutionApplicationMetricsDto metricsDto,
                                                                           @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                                           @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                                           @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                                           @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                                           @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * 获取学生申请状态统计数据
     *
     * @param companyIds
     * @param year
     * @param staffFollowerIds
     * @return
     */
    @DS("saledb-doris")
    Map<String, Object> getStudentApplicationStatusCount(@Param("companyIds") List<Long> companyIds,
                                                         @Param("year") String year,
                                                         @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                         @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                         @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                         @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                         @Param("staffBoundBdIds") List<Long> staffBoundBdIds);


    @DS("saledb-doris")
    List<COEParameterVo> getCOEMonthlyStatistics(@Param("companyIds") List<Long> companyIds,
                                                 @Param("year") String year,
                                                 @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                 @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                 @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                 @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                 @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * 院校统计
     *
     * @param companyIds
     * @param year
     * @param staffFollowerIds
     * @param isStudentAdmin
     * @param isStudentOfferItemFinancialHiding
     * @return
     */
    @DS("saledb-doris")
    List<StudentStatisticsVo> getBusinessCountryGroupStudentCount(@Param("companyIds") List<Long> companyIds,
                                                                  @Param("year") String year,
                                                                  @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                                  @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                                  @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                                  @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                                  @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    @DS("saledb-doris")
    List<CountryStatisticsVo> getCountryStudentCount(@Param("companyIds") List<Long> companyIds,
                                                     @Param("countryIds") List<Long> countryIds,
//                                                     @Param("countryId") Long countryId,
                                                     @Param("year") String year,
                                                     @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                     @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                     @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                     @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                     @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    @DS("saledb-doris")
    List<SchoolStudentCountVo> getSchoolStudentCount(@Param("companyIds") List<Long> companyIds,
                                                     @Param("countryIds") List<Long> countryIds,
//                                                     @Param("countryId") Long countryId,
                                                     @Param("areaCountryIds") List<Long> areaCountryIds,
//                                                     @Param("areaCountryId") Long areaCountryId,
                                                     @Param("year") String year,
                                                     @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                     @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                     @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                     @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                     @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

//     * 院校统计end


    /**
     * 送生占比
     *
     * @param companyIds
     * @param year
     * @param staffFollowerIds
     * @param isStudentOfferItemFinancialHiding
     * @param isStudentAdmin
     * @return
     */
    @DS("saledb-doris")
    List<AreaRegionStatisticsVo> getAreaRegionStudentCount(@Param("companyIds") List<Long> companyIds,
                                                           @Param("year") String year,
                                                           @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                           @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                           @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                           @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                           @Param("staffBoundBdIds") List<Long> staffBoundBdIds
    );

    @DS("saledb-doris")
    List<AgentStaristisVo> getAgentStudentCount(@Param("companyIds") List<Long> companyIds,
                                                @Param("areaRegionIds") List<Long> areaRegionIds,
                                                @Param("bdStaffIds") List<Long> bdStaffIds,
                                                @Param("year") String year,
                                                @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                @Param("staffBoundBdIds") List<Long> staffBoundBdIds);


    @DS("saledb-doris")
    List<BdStaffStatisticsVo> getBdStaffStudentCount(@Param("companyIds") List<Long> companyIds,
                                                     @Param("areaRegionIds") List<Long> areaRegionIds,
                                                     @Param("year") String year,
                                                     @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                     @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                     @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                     @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                     @Param("staffBoundBdIds") List<Long> staffBoundBdIds);
//    送生占比 end


    /**
     * 获取申请计划，应收，实收，实付统计
     *
     * @param companyIds
     * @param year
     * @param staffFollowerIds
     * @param isStudentAdmin
     * @param isStudentOfferItemFinancialHiding
     * @return
     */
    @DS("saledb-doris")
    List<FinanceStatisticVO> getFinanceStatistic(@Param("companyIds") List<Long> companyIds,
                                                 @Param("year") String year,
                                                 @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                 @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                 @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                 @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                 @Param("staffBoundBdIds") List<Long> staffBoundBdIds);


    /**
     * bd奖金预统计
     * @param page
     * @param bdStudentBonusVo
     * @param staffFollowerIds
     * @param isStudentAdmin
     * @param isStudentOfferItemFinancialHiding
     * @return
     */
    @DS("saledb-doris")
    List<BdStudentBonusVo> getBdBonusPreStatistics(@Param("page") IPage<BdStudentBonusDto> page,
                                                   @Param("bdStudentBonusVo") BdStudentBonusDto bdStudentBonusVo,
                                                   @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                   @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                   @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                                   @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                   @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * 获取所有学生申请计划
     * @param ids
     * @return
     */
    @DS("saledb-doris")
    List<StudentOfferItem> selectAllByIds(@Param("ids") Set<Long> ids);

    /**
     * AI获取学生申请计划
     * @param aiStudentIds
     * @return
     */
    @DS("saledb-doris")
    List<AiStudentOfferItemInfoVo> getAiStudentOfferItem(@Param("aiStudentIds") List<Long> aiStudentIds,
                                                         @Param("companyIds") List<Long> companyIds,
                                                         @Param("areaCountryIds") List<Long> fkAreaCountryIds,
                                                         @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                         @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                         @Param("staffBoundBdIds") List<Long> staffBoundBdIds,
                                                         @Param("isStudentOfferItemFinancialHiding")Boolean isStudentOfferItemFinancialHiding,
                                                         @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                         @Param("institutionIds") List<Long> institutionIds);

    /**
     * 获取学生申请计划
     * @param offerItemVo
     * @param isStudentOfferItemFinancialHiding
     * @param permissionGroupInstitutionIds
     * @param staffBoundBdIds
     * @return
     */
    List<StudentOfferItem> selectOfferItemList(@Param("offerItemVo") StudentOfferItemDto offerItemVo,
                                               @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                               @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                               @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                               @Param("isStudentAdmin") Boolean isStudentAdmin,
                                               @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

//    @DS("saledb-doris")
//    List<FinanceStatisticVO> getFinanceStatistic1(@Param("companyIds") List<Long> companyIds,
//                                                 @Param("year") String year,
//                                                 @Param("staffFollowerIds") List<Long> staffFollowerIds,
//                                                 @Param("isStudentAdmin") Boolean isStudentAdmin,
//                                                 @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding);
}