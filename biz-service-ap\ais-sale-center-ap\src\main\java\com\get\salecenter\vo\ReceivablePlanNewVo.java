package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.ReceivablePlan;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/11/19
 * @TIME: 15:54
 * @Description:
 **/
@Data
public class ReceivablePlanNewVo extends BaseEntity {

    @ApiModelProperty(value = "主表Id")
    private Long id;


    @ApiModelProperty("申请步骤状态")
    private String stepName;
    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;


    @ApiModelProperty("BD Id")
    private Long fkStaffId;

    @ApiModelProperty("bd编号")
    private String bdCode;

    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String fkCurrencyTypeName;

    @ApiModelProperty(value = "应收类型名称")
    private String fkTypeName;

    /**
     * 实收折合金额（这个金额币种和应收一致，因为已经是折合了）
     */
    @ApiModelProperty(value = "实收折合金额")
    private BigDecimal sumAmountReceivable;

    /**
     * 课程长度
     */
    @ApiModelProperty(value = "课程长度")
    private BigDecimal duration;

    /**
     * 课程长度类型(0周、1学期、2年)
     */
    @ApiModelProperty(value = "课程长度类型(0周、1学期、2年)")
    private Integer durationType;

    /**
     * 汇率调整金额
     */
    @ApiModelProperty(value = "汇率调整金额")
    private BigDecimal sumAmountExchangeRate;

    /**
     * 港币金额
     */
    @ApiModelProperty(value = "港币金额")
    private BigDecimal sumAmountHkd;


    @ApiModelProperty("应付金额")
    private BigDecimal payableAmount;

    @ApiModelProperty("应付币种名称")
    private String payableCurrencyNumName;

    @ApiModelProperty("应付币种")
    private String payableCurrencyNum;

    @ApiModelProperty("应付id")
    private Long payablePlanId;

    @ApiModelProperty("应付信息")
    private String payableAmountInfo;

    @ApiModelProperty("通知信息")
    private String commissionNotice;

    @ApiModelProperty("是否存在应付")
    private Boolean existPayable;

    /**
     * 人民币金额
     */
    @ApiModelProperty(value = "人民币金额")
    private BigDecimal sumAmountRmb;

    /**
     * 实收金额
     */
    @ApiModelProperty(value = "实收金额")
    private BigDecimal actualReceivableAmount;

    @ApiModelProperty(value = "实收金额")
    private String actualReceivableAmountInfo;

    @ApiModelProperty("实收币种")
    private String receiptCurrencyTypeNum;

    /**
     * 差额
     */
    @ApiModelProperty(value = "差额")
    private BigDecimal diffReceivableAmount;

    /**
     * 发票Id
     */
    @ApiModelProperty(value = "发票Id")
    private String fkInvoiceIds;

    /**
     * 发票编号
     */
    @ApiModelProperty(value = "发票编号")
    private String fkInvoiceNum;

    /**
     * 学校提供商国家Id(所在地)
     */
    @ApiModelProperty(value = "学校提供商国家Id(所在地)")
    private String fkAreaCountryIds;

    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;

    /**
     * 学生id
     */
    @ApiModelProperty(value = "学生id")
    private Long fkStudentId;

    @ApiModelProperty(value = "学生编号")
    private String studentNum;

    @ApiModelProperty(value = "性别")
    private String genderName;

    @ApiModelProperty(value = "学生号")
    private String studentId;

    @ApiModelProperty("代理id")
    private Long fkAgentId;

    @ApiModelProperty(value = "代理编号")
    private String agentNum;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "名称备注")
    private String agentNameNote;

    /**
     * 延迟入学时间
     */
    @ApiModelProperty(value = "延迟入学时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date maxDeferEntranceTimes;

    /**
     * 学生名称
     */
    @ApiModelProperty(value = "学生名称")
    private String studentName;

    @ApiModelProperty(value = "名（英/拼音）")
    private String studentFirstName;

    @ApiModelProperty(value = "姓（英/拼音）")
    private String studentLastName;

    @ApiModelProperty(value = "学生生日")
    private String studentBirthday;

    @ApiModelProperty(value = "业务渠道Id")
    private Long fkInstitutionChannelId;

    @ApiModelProperty(value = "渠道名称")
    private String fkInstitutionChannelName;

    @ApiModelProperty(value = "学校名称")
    private String fkInstitutionName;

    @ApiModelProperty(value = "学校提供商Id")
    private String fkInstitutionProviderId;

    @ApiModelProperty(value = "学校提供商名称")
    private String fkInstitutionProviderName;

    @ApiModelProperty(value = "学校提供商中文名称")
    private String fk_institution_provider_name_cn;

    @ApiModelProperty(value = "学校Id")
    private String fkInstitutionId;

    @ApiModelProperty(value = "学校名称/保险开始时间/公寓名称/应收计划摘要")
    private String istOrApmName;

    @ApiModelProperty(value = "学校简称/保险结束时间/公寓开始时间")
    private String shortNameOrApaStartTime;

    @ApiModelProperty(value = "学校中文名称/公寓结束时间/护照编号")
    private String stuNamePassportApaEnd;

    @ApiModelProperty(value = "学校中文名字简称/公寓天数")
    private String stuCnApaDay;

    @ApiModelProperty(value = "课程Id")
    private Long fkInstitutionCourseId;

    @ApiModelProperty(value = "课程名称")
    private String fkCourseName;

    @ApiModelProperty(value = "中文课程名称")
    private String fkCourseNameCn;

    @ApiModelProperty(value = "开学时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String openingTime;

    @ApiModelProperty("延迟入学时间（最终开学时间）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deferOpeningTime;

    /**
     * 延迟入学标记
     */
    @ApiModelProperty(value = "延迟入学标记")
    private Boolean isDeferEntrance;

    private String bziId24;

    /**
     * 旧系统学校名称
     */
    @ApiModelProperty(value = "旧系统学校名称")
    @Column(name = "old_institution_name")
    private String oldInstitutionName;

    /**
     * 旧系统学校全称
     */
    @ApiModelProperty(value = "旧系统学校全称")
    @Column(name = "old_institution_full_name")
    private String oldInstitutionFullName;

    /**
     * 旧系统课程名称
     */
    @ApiModelProperty(value = "旧系统课程名称")
    @Column(name = "old_course_custom_name")
    private String oldCourseCustomName;

    /**
     * 应付计划的目标对象对应的应收计划列表（receivableAmount-应收金额、actualReceivableAmount-实收金额、diffReceivableAmount-收款差额）
     */
    @ApiModelProperty(value = "应付计划的目标对象对应的应收计划列表（receivableAmount-应收金额、actualReceivableAmount-实收金额、diffReceivableAmount-收款金额）")
    private List<Map> receivablePlanList;

    /**
     * 是否后续课程：0否/1是
     */
    @ApiModelProperty(value = "是否后续课程")
    private Boolean isFollow;

    /**
     * 父计划id
     */
    @ApiModelProperty(value = "父计划id")
    private Long fkParentStudentOfferItemId;

    /**
     * 申请方案Id
     */
    @ApiModelProperty(value = "申请方案Id")
    private Long fkStudentOfferId;

    /**
     * 其他金额类型：0=奖励金额/1=Incentive奖励/2=保险金额/3=活动费用/4=其他收入
     */
    @ApiModelProperty(value = "其他金额类型：0=奖励金额/1=Incentive奖励/2=保险金额/3=活动费用/4=其他收入")
    private Integer bonusType;

    /**
     * 其他金额类型：0=奖励金额/1=Incentive奖励/2=保险金额/3=活动费用/4=其他收入
     */
    @ApiModelProperty(value = "其他金额类型：0=奖励金额/1=Incentive奖励/2=保险金额/3=活动费用/4=其他收入")
    private String bonusTypeName;

    /**
     * 其他金额
     */
    @ApiModelProperty(value = "其他金额")
    private BigDecimal bonusAmount;

    @ApiModelProperty(value = "发票绑定金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "预付金额")
    private BigDecimal payInAdvanceAmount;

    @ApiModelProperty(value = "是否预付，0否/1是")
    private Boolean isPayInAdvance;

    @ApiModelProperty(value = "到账时间")
    private String paymentDate;

    /**
     * 记录总数
     */
    @ApiModelProperty(value = "记录总数")
    private Integer totalCount;

    @ApiModelProperty(value = "申请备注")
    private String appRemark;

    @ApiModelProperty(value = "发票应收计划绑定关系id")
    private Long invoiceReceivablePlanRelationId;

    @ApiModelProperty(value = "备注详情")
    private Map<String, String> remarksMap;

    @ApiModelProperty(value = "备注详情")
    private PlanRemarkDetailVo planRemarkDetailDto;

    @ApiModelProperty(value = "开学时间提醒： 【一般】1,【严重】2")
    private Integer reminderOpentimeStatus;

    @ApiModelProperty(value = "计划收款时间串")
    private String receivablePlanDateStr;

    @ApiModelProperty(value = "申请计划编号")
    private String offerItemNum;

    @ApiModelProperty(value = "学生佣金结算标记关键字")
    private String commissionMark;

    @ApiModelProperty("收款方名")
    private String receivableName;

    @ApiModelProperty(value = "受保人护照编号")
    private String insurantPassportNum;

    @ApiModelProperty(value = "保险业务信息")
    private String insuranceInfo;

    //===========实体类ReceivablePlan==============
    private static final long serialVersionUID = 1L;



    /**
     * 应收类型关键字，枚举，如：m_student_offer_item
     */
    @ApiModelProperty(value = "应收类型关键字，枚举，如：m_student_offer_item")
    @Column(name = "fk_type_key")
    private String fkTypeKey;
    /**
     * 应收类型对应记录Id，如：m_student_offer_item.id
     */
    @ApiModelProperty(value = "应收类型对应记录Id，如：m_student_offer_item.id")
    @Column(name = "fk_type_target_id")
    private Long fkTypeTargetId;
    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    @Column(name = "summary")
    private String summary;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;
    /**
     * 学费金额
     */
    @ApiModelProperty(value = "学费金额")
    @Column(name = "tuition_amount")
    private BigDecimal tuitionAmount;
    /**
     * 费率%
     */
    @ApiModelProperty(value = "费率%")
    @Column(name = "commission_rate")
    private BigDecimal commissionRate;
    /**
     * 渠道费率%
     */
    @ApiModelProperty(value = "渠道费率%")
    @Column(name = "net_rate")
    private BigDecimal netRate;
    /**
     * 佣金金额
     */
    @ApiModelProperty(value = "佣金金额")
    @Column(name = "commission_amount")
    private BigDecimal commissionAmount;
    /**
     * 定额金额
     */
    @UpdateWithNull
    @ApiModelProperty(value = "定额金额")
    @Column(name = "fixed_amount")
    private BigDecimal fixedAmount;

    /**
     * 应收金额
     */
    @ApiModelProperty(value = "应收金额")
    @Column(name = "receivable_amount")
    private BigDecimal receivableAmount;
    /**
     * 计划收款时间
     */
    @ApiModelProperty(value = "计划收款时间")
    @Column(name = "receivable_plan_date")
    @JsonFormat(pattern="yyyy-MM-dd")
    @UpdateWithNull
    private Date receivablePlanDate;
    /**
     * 应收计划额外原因Id
     */
    @ApiModelProperty(value = "应收计划额外原因Id")
    @Column(name = "fk_receivable_reason_id")
    private Integer fkReceivableReasonId;
    /**
     * 状态：0关闭/1打开/2完成
     */
    @ApiModelProperty(value = "状态：0关闭/1打开/2完成")
    @Column(name = "status")
    private Integer status;
    /**
     * 旧数据财务id(gea)
     */
    @ApiModelProperty(value = "旧数据财务id(gea)")
    @Column(name = "id_gea_finance")
    private String idGeaFinance;

}
