package com.get.financecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/12/27 12:59
 */
@Data
public class CommissionSummaryBatchItemVo {

    private Long number;

    @ApiModelProperty(value = "代理id")
    private Long id;

    @ApiModelProperty(value = "业务类型Key")
    private String fkTypeKey;

    @ApiModelProperty(value = "业务类型")
    private String fkTypeKeyName;

    @ApiModelProperty(value = "bd名")
    private String bdName;

    @ApiModelProperty(value = "代理名")
    private String agentName;

    @ApiModelProperty(value = "支付币种")
    private String planCurrencyNum;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal payableAmount;

    @ApiModelProperty(value = "兑换币种")
    private String exchangeCurrencyNum;

    @ApiModelProperty(value = "兑换汇率")
    private BigDecimal exchangeRate;

    @ApiModelProperty(value = "兑换金额")
    private BigDecimal exchangeAmount;

    @ApiModelProperty(value = "兑换港币汇率")
    private BigDecimal exchangeHkdRate;

    @ApiModelProperty(value = "兑换港币金额")
    private BigDecimal exchangeHkdAmount;

    @ApiModelProperty(value = "港币兑换人民币汇率")
    private BigDecimal hkdExchangeRmbRate;

    @ApiModelProperty(value = "港币兑换人民币金额")
    private BigDecimal hkdExchangeRmbAmount;

    @ApiModelProperty(value = "手续费")
    private BigDecimal serviceFee;

    @ApiModelProperty(value = "结算币种")
    private String accountCurrencyNum;

    @ApiModelProperty(value = "收款开户银行")
    private String bankName;

    @ApiModelProperty(value = "银行支行")
    private String bankBranchName;

    @ApiModelProperty(value = "收款账户名")
    private String bankAccount;

    @ApiModelProperty(value = "收款账户号")
    private String bankAccountNum;

    @ApiModelProperty(value = "银行地址")
    private String bankAddress;

    @ApiModelProperty(value = "Swift Code")
    private String swiftCode;

    @ApiModelProperty(value = "其他转账编码")
    private String otherCode;

    @ApiModelProperty(value = "学生姓名")
    private String studentName;

    @ApiModelProperty(value = "转账用途")
    private String transferPurpose;

    @ApiModelProperty(value = "已付金额")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal paidAmount;

    @ApiModelProperty(value = "应付差额")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal differenceAmount;

    @ApiModelProperty(value = "实际支付金额")
    private BigDecimal amountActual;
}
