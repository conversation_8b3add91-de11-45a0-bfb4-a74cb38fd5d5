package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.AccountingItemMapper;
import com.get.financecenter.dao.ExpenseClaimFeeTypeMapper;
import com.get.financecenter.dao.TravelClaimFormMapper;
import com.get.financecenter.dto.ExpenseClaimFeeTypeDto;
import com.get.financecenter.entity.ExpenseClaimFeeType;
import com.get.financecenter.enums.RelationTargetKeyEnum;
import com.get.financecenter.service.IExpenseClaimFeeTypeService;
import com.get.financecenter.utils.GetAccountingCodeNameUtils;
import com.get.financecenter.vo.ExpenseClaimFeeTypeVo;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author: Sea
 * @create: 2021/4/6 16:55
 * @verison: 1.0
 * @description:
 */
@Service
public class ExpenseClaimFeeTypeServiceImpl extends BaseServiceImpl<ExpenseClaimFeeTypeMapper, ExpenseClaimFeeType> implements IExpenseClaimFeeTypeService {
    @Resource
    private ExpenseClaimFeeTypeMapper expenseClaimFeeTypeMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private AccountingItemMapper accountingItemMapper;
    @Resource
    private GetAccountingCodeNameUtils getAccountingCodeNameUtils;
    @Resource
    private TravelClaimFormMapper travelClaimFormMapper;

    @Override
    public ExpenseClaimFeeTypeVo findExpenseClaimFeeTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ExpenseClaimFeeType expenseClaimFeeType = expenseClaimFeeTypeMapper.selectById(id);
        if (GeneralTool.isEmpty(expenseClaimFeeType)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        return BeanCopyUtils.objClone(expenseClaimFeeType, ExpenseClaimFeeTypeVo::new);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<ExpenseClaimFeeTypeDto> expenseClaimFeeTypeDtos) {
        if (GeneralTool.isEmpty(expenseClaimFeeTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //获取最大排序(防止每次都要去查最大值，所以在外面查出一次，循环几次 就自增几次)
//        Integer maxViewOrder = expenseClaimFeeTypeMapper.getMaxViewOrder();
//        for (ExpenseClaimFeeTypeDto expenseClaimFeeTypeDto : expenseClaimFeeTypeDtos) {
//            ExpenseClaimFeeType expenseClaimFeeType = BeanCopyUtils.objClone(expenseClaimFeeTypeDto, ExpenseClaimFeeType::new);
//            if (GeneralTool.isEmpty(expenseClaimFeeTypeDto.getId())) {
//                expenseClaimFeeType.setViewOrder(maxViewOrder);
//                utilService.updateUserInfoToEntity(expenseClaimFeeType);
//                expenseClaimFeeTypeMapper.insert(expenseClaimFeeType);
//                maxViewOrder++;
//            } else {
//                utilService.updateUserInfoToEntity(expenseClaimFeeType);
//                expenseClaimFeeTypeMapper.updateById(expenseClaimFeeType);
//            }
//        }
        if (GeneralTool.isEmpty(expenseClaimFeeTypeDtos)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }

        for (ExpenseClaimFeeTypeDto expenseClaimFeeTypeDto : expenseClaimFeeTypeDtos) {
            ExpenseClaimFeeType expenseClaimFeeType = BeanCopyUtils.objClone(expenseClaimFeeTypeDto, ExpenseClaimFeeType::new);
            checkParam(expenseClaimFeeType);
            expenseClaimFeeType.setTypeName(expenseClaimFeeType.getTypeName().replace(" ", "").trim());
            if (expenseClaimFeeTypeMapper.checkName(expenseClaimFeeType.getTypeName()) > 0){
                throw new GetServiceException(LocaleMessageUtils.getMessage("duplicate_name") + expenseClaimFeeType.getTypeName());
            }
            utilService.setCreateInfo(expenseClaimFeeType);
            expenseClaimFeeType.setViewOrder(expenseClaimFeeTypeMapper.getMaxViewOrder());
            int insert = expenseClaimFeeTypeMapper.insert(expenseClaimFeeType);
            if (insert <= 0){
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
        }

    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (expenseClaimFeeTypeMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        Integer count = travelClaimFormMapper.getTravelClaimFormByTypeId(id);
        if (count > 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("cancel_obj_used_by_travel_claim_form_item"));
        }else {
            expenseClaimFeeTypeMapper.deleteById(id);
        }

    }

    @Override
    public ExpenseClaimFeeTypeVo updateExpenseClaimFeeType(ExpenseClaimFeeTypeDto expenseClaimFeeTypeDto) {
        if (expenseClaimFeeTypeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ExpenseClaimFeeType result = expenseClaimFeeTypeMapper.selectById(expenseClaimFeeTypeDto.getId());
        if (GeneralTool.isEmpty(result)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_obj_null"));
        }
        ExpenseClaimFeeType expenseClaimFeeType = BeanCopyUtils.objClone(expenseClaimFeeTypeDto, ExpenseClaimFeeType::new);
        if (!expenseClaimFeeType.getTypeName().equals(result.getTypeName())){
            expenseClaimFeeType.setTypeName(expenseClaimFeeType.getTypeName().replace(" ", "").trim());
            if (expenseClaimFeeTypeMapper.checkName(expenseClaimFeeType.getTypeName()) > 0){
                throw new GetServiceException(LocaleMessageUtils.getMessage("duplicate_name" )+ "(" + expenseClaimFeeType.getTypeName() + ")");
            }
        }
        utilService.updateUserInfoToEntity(expenseClaimFeeType);
        expenseClaimFeeTypeMapper.updateById(expenseClaimFeeType);
        return findExpenseClaimFeeTypeById(expenseClaimFeeTypeDto.getId());
    }

    @Override
    public List<ExpenseClaimFeeTypeVo> getExpenseClaimFeeTypes(ExpenseClaimFeeTypeDto expenseClaimFeeTypeDto, Page page) {
//        Example example = new Example(ExpenseClaimFeeType.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(expenseClaimFeeTypeVo)) {
//            //查询条件-类型名称
//            if (GeneralTool.isNotEmpty(expenseClaimFeeTypeVo.getTypeName())) {
//                criteria.andLike("typeName", "%" + expenseClaimFeeTypeVo.getTypeName() + "%");
//            }
//        }
//        example.orderBy("viewOrder").desc();
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<ExpenseClaimFeeType> expenseClaimFeeTypes = expenseClaimFeeTypeMapper.selectByExample(example);
//        page.restPage(expenseClaimFeeTypes);
        LambdaQueryWrapper<ExpenseClaimFeeType> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(expenseClaimFeeTypeDto)) {
            //查询条件-类型名称
            if (GeneralTool.isNotEmpty(expenseClaimFeeTypeDto.getTypeName())) {
                wrapper.like(ExpenseClaimFeeType::getTypeName, expenseClaimFeeTypeDto.getTypeName());
            }
            if (GeneralTool.isNotEmpty(expenseClaimFeeTypeDto.getFkAccountingItemId())){
                wrapper.eq(ExpenseClaimFeeType::getFkAccountingItemId, expenseClaimFeeTypeDto.getFkAccountingItemId());
            }
            if (GeneralTool.isNotEmpty(expenseClaimFeeTypeDto.getKeyWord())){
                wrapper.like(ExpenseClaimFeeType::getTypeName, expenseClaimFeeTypeDto.getKeyWord());
            }
        }
        wrapper.orderByDesc(ExpenseClaimFeeType::getViewOrder);
        IPage<ExpenseClaimFeeType> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<ExpenseClaimFeeType> expenseClaimFeeTypes = pages.getRecords();
        page.setAll((int) pages.getTotal());
        return expenseClaimFeeTypes.stream().map(expenseClaimFeeType -> {
            ExpenseClaimFeeTypeVo expenseClaimFeeTypeVo = BeanCopyUtils.objClone(expenseClaimFeeType, ExpenseClaimFeeTypeVo::new);
            expenseClaimFeeTypeVo.setAccountingItemName(getAccountingCodeNameUtils.setAccountingCodeName(expenseClaimFeeType.getFkAccountingItemId()));
            if (GeneralTool.isNotEmpty(expenseClaimFeeType.getRelationTargetKey())){
                expenseClaimFeeTypeVo.setRelationTargetKeyName(RelationTargetKeyEnum.getNameByRelationTargetKey(expenseClaimFeeType.getRelationTargetKey()));
            }
            return expenseClaimFeeTypeVo;
        }).collect(Collectors.toList());
    }

    @Override
    public void sort(List<ExpenseClaimFeeTypeDto> expenseClaimFeeTypeDtos) {
        if (GeneralTool.isEmpty(expenseClaimFeeTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ExpenseClaimFeeType ro = BeanCopyUtils.objClone(expenseClaimFeeTypeDtos.get(0), ExpenseClaimFeeType::new);
        ExpenseClaimFeeType expenseClaimFeeTypeOne = expenseClaimFeeTypeMapper.selectById(ro.getId());
        if (GeneralTool.isEmpty(expenseClaimFeeTypeOne)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        Integer oneorder = expenseClaimFeeTypeOne.getViewOrder();
        ExpenseClaimFeeType rt = BeanCopyUtils.objClone(expenseClaimFeeTypeDtos.get(1), ExpenseClaimFeeType::new);
        ExpenseClaimFeeType expenseClaimFeeTypeTwo = expenseClaimFeeTypeMapper.selectById(rt.getId());
        if (GeneralTool.isEmpty(expenseClaimFeeTypeTwo)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        Integer twoorder = expenseClaimFeeTypeTwo.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        expenseClaimFeeTypeMapper.updateById(ro);
        expenseClaimFeeTypeMapper.updateById(rt);
    }

    /**
     * 费用报销单类型下拉框数据
     * @return
     */
    @Override
    public List<ExpenseClaimFeeTypeVo> getExpenseClaimFeeTypeSelect() {
        List<ExpenseClaimFeeType> expenseClaimFeeTypes = this.expenseClaimFeeTypeMapper.selectList(Wrappers.<ExpenseClaimFeeType>query().lambda()
                .orderByDesc(ExpenseClaimFeeType::getViewOrder));
        List<ExpenseClaimFeeTypeVo> expenseClaimFeeTypeVoList = expenseClaimFeeTypes.stream().map(expenseClaimFeeType -> BeanCopyUtils.objClone(expenseClaimFeeType, ExpenseClaimFeeTypeVo::new)).collect(Collectors.toList());
        //查询科目关联项类型
        for (ExpenseClaimFeeTypeVo expenseClaimFeeTypeVo : expenseClaimFeeTypeVoList) {
            expenseClaimFeeTypeVo.setRelationTargetKey(expenseClaimFeeTypeVo.getRelationTargetKey());
            expenseClaimFeeTypeVo.setRelationTargetKeyName(RelationTargetKeyEnum.getNameByRelationTargetKey(expenseClaimFeeTypeVo.getRelationTargetKey()));
        }
        return expenseClaimFeeTypeVoList;
    }

    @Override
    public String getExpenseClaimFeeTypeNameById(Long expenseClaimFeeTypeId) {
        String expenseClaimFeeTypeName = null;
        ExpenseClaimFeeType expenseClaimFeeType = expenseClaimFeeTypeMapper.selectById(expenseClaimFeeTypeId);
        if (GeneralTool.isNotEmpty(expenseClaimFeeType)) {
            expenseClaimFeeTypeName = expenseClaimFeeType.getTypeName();
        }
        return expenseClaimFeeTypeName;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(Integer start, Integer end) {
        if (GeneralTool.isEmpty(start) || GeneralTool.isEmpty( end)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("sort_param_error"));
        }
        LambdaQueryWrapper<ExpenseClaimFeeType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (end > start){
            lambdaQueryWrapper.between(ExpenseClaimFeeType::getViewOrder,start,end).orderByDesc(ExpenseClaimFeeType::getViewOrder);
        }else {
            lambdaQueryWrapper.between(ExpenseClaimFeeType::getViewOrder,end,start).orderByDesc(ExpenseClaimFeeType::getViewOrder);

        }
        List<ExpenseClaimFeeType> expenseClaimFeeTypes = list(lambdaQueryWrapper);
        List<ExpenseClaimFeeType> updateList = new ArrayList<>();
        if (end > start){
            int finalEnd = end;
            List<ExpenseClaimFeeType> sortedList = Lists.newArrayList();
            ExpenseClaimFeeType expenseClaimFeeTypeLast = expenseClaimFeeTypes.get(expenseClaimFeeTypes.size() - 1);
            sortedList.add(expenseClaimFeeTypeLast);
            expenseClaimFeeTypes.remove(expenseClaimFeeTypes.size() - 1);
            sortedList.addAll(expenseClaimFeeTypes);
            for (ExpenseClaimFeeType expenseClaimFeeType : sortedList) {
                expenseClaimFeeType.setViewOrder(finalEnd);
                finalEnd--;
            }
            updateList.addAll(sortedList);
        }else {
            int finalStart = start;
            List<ExpenseClaimFeeType> sortedList = Lists.newArrayList();
            ExpenseClaimFeeType expenseClaimFeeTypeFirst = expenseClaimFeeTypes.get(0);
            expenseClaimFeeTypes.remove(0);
            sortedList.addAll(expenseClaimFeeTypes);
            sortedList.add(expenseClaimFeeTypeFirst);
            for (ExpenseClaimFeeType expenseClaimFeeType : sortedList) {
                expenseClaimFeeType.setViewOrder(finalStart);
                finalStart--;
            }
            updateList.addAll(sortedList);
        }

        if (GeneralTool.isNotEmpty(updateList)){
            boolean batch = updateBatchById(updateList);
            if (!batch){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
    }

    private static void checkParam(ExpenseClaimFeeType expenseClaimFeeType) {
        if (GeneralTool.isEmpty(expenseClaimFeeType.getTypeName())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("type_name_null"));
        }
        if (GeneralTool.isEmpty(expenseClaimFeeType.getFkAccountingItemId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("accounting_item_id_null"));
        }
    }
}
