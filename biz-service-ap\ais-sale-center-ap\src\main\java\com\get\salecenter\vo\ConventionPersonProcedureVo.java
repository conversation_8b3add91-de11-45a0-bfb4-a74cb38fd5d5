package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.ConventionPersonProcedure;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/16 10:33
 * @verison: 1.0
 * @description:
 */
@Data
@ApiModel(value = "参加流程人员和流程关联信息返回类")
public class ConventionPersonProcedureVo extends BaseEntity {
    /**
     * 参加人姓名
     */
    @ApiModelProperty(value = "参加人姓名")
    private String name;

    /**
     * 参加人姓名（中文）
     */
    @ApiModelProperty(value = "参加人姓名（中文）")
    private String nameChn;

    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男")
    private Integer gender;

    /**
     * 公司
     */
    @ApiModelProperty(value = "公司")
    private String company;

    /**
     * 参展人员类型（枚举定义）：0校方/1代理/2嘉宾/3员工/4工作人员
     */
    @ApiModelProperty(value = "参展人员类型（枚举定义）：0校方/1代理/2嘉宾/3员工/4工作人员")
    private Integer type;

    /**
     * 是否参加
     */
    @ApiModelProperty(value = "是否参加")
    private boolean join;

    /**
     * 培训桌桌子编号
     */
    @ApiModelProperty(value = "培训桌桌子编号")
    private List<String> trainingNumList;

    /**
     * 晚宴桌桌子编号
     */
    @ApiModelProperty(value = "晚宴桌桌子编号")
    private List<String> dinnerNumList;

    /**
     * BD编号
     */
    @JsonIgnore
    @ApiModelProperty(value = "BD编号")
    private String bdCode;

    /**
     * BD名称
     */
    @ApiModelProperty(value = "BD名称")
    private String bdName;

    //==============实体类ConventionPersonProcedure=================
    private static final long serialVersionUID = 1L;
    /**
     * 峰会参展人员Id
     */
    @ApiModelProperty(value = "峰会参展人员Id")
    @Column(name = "fk_convention_person_id")
    private Long fkConventionPersonId;
    /**
     * 峰会流程Id
     */
    @ApiModelProperty(value = "峰会流程Id")
    @Column(name = "fk_convention_procedure_id")
    private Long fkConventionProcedureId;

}
