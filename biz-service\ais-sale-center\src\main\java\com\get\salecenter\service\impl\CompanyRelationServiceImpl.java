package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.AgentCompanyMapper;
import com.get.salecenter.dao.sale.AgentContractCompanyMapper;
import com.get.salecenter.dao.sale.ContactPersonCompanyMapper;
import com.get.salecenter.vo.CompanyTreeVo;
import com.get.salecenter.entity.AgentCompany;
import com.get.salecenter.entity.AgentContractCompany;
import com.get.salecenter.entity.SaleContactPersonCompany;
import com.get.salecenter.service.ICompanyRelationService;
import com.google.common.collect.Lists;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/10/23
 * @TIME: 10:29
 * @Description:
 **/
@Service
public class CompanyRelationServiceImpl implements ICompanyRelationService {
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private AgentContractCompanyMapper contractCompanyMapper;
    @Resource
    private AgentCompanyMapper agentCompanyMapper;
    @Resource
    private ContactPersonCompanyMapper contactPersonCompanyMapper;

    @Override
    public List<CompanyTreeVo> getContractCompanyRelation(Long contractId) {
        if (GeneralTool.isEmpty(contractId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //获取公司
        List<CompanyTreeVo> companyTreeVo = getCompanyTreeDto();
        if (GeneralTool.isEmpty(companyTreeVo)) {
            return null;
        }
        //获取中间表数据
        List<AgentContractCompany> relation = getRelationByContractId(contractId);
        setContractFlag(companyTreeVo, relation);
        return getTreeList(companyTreeVo);
    }

    @Override
    public List<CompanyTreeVo> getAgentCompanyRelation(Long agentId) {
        if (GeneralTool.isEmpty(agentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //获取公司
        List<CompanyTreeVo> companyTreeVo = getCompanyTreeDto();
        if (GeneralTool.isEmpty(companyTreeVo)) {
            return null;
        }
        List<AgentCompany> relation = getRelationByAgentId(agentId);
        setAgentFlag(companyTreeVo, relation);
        return getTreeList(companyTreeVo);
    }

    @Override
    public List<CompanyTreeVo> getContactCompanyRelation(Long contactId) {
        if (GeneralTool.isEmpty(contactId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //获取公司
        List<CompanyTreeVo> companyTreeVo = getCompanyTreeDto();
        if (GeneralTool.isEmpty(companyTreeVo)) {
            return null;
        }

        List<SaleContactPersonCompany> relation = getRelationByContactId(contactId);
        setContactFlag(companyTreeVo, relation);
        return getTreeList(companyTreeVo);
    }

    private void setContractFlag(List<CompanyTreeVo> companyTreeVo, List<AgentContractCompany> relation) {
        for (CompanyTreeVo treeDto : companyTreeVo) {
            for (AgentContractCompany contractCompany : relation) {
                if (treeDto.getId().equals(String.valueOf(contractCompany.getFkCompanyId()))) {
                    treeDto.setFlag(true);
                }
            }
        }
    }

    private void setAgentFlag(List<CompanyTreeVo> companyTreeVo, List<AgentCompany> relation) {
        for (CompanyTreeVo treeDto : companyTreeVo) {
            for (AgentCompany contractCompany : relation) {
                if (treeDto.getId().equals(String.valueOf(contractCompany.getFkCompanyId()))) {
                    treeDto.setFlag(true);
                }
            }
        }
    }

    private void setContactFlag(List<CompanyTreeVo> companyTreeVo, List<SaleContactPersonCompany> relation) {
        for (CompanyTreeVo treeDto : companyTreeVo) {
            for (SaleContactPersonCompany contactCompany : relation) {
                if (treeDto.getId().equals(String.valueOf(contactCompany.getFkCompanyId()))) {
                    treeDto.setFlag(true);
                }
            }
        }
    }

    private List<AgentContractCompany> getRelationByContractId(Long contractId) {
//        Example example = new Example(AgentContractCompany.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkAgentContractId", contractId);
//        return contractCompanyMapper.selectByExample(example);
        return contractCompanyMapper.selectList(Wrappers.<AgentContractCompany>lambdaQuery().eq(AgentContractCompany::getFkAgentContractId, contractId));
    }

    private List<AgentCompany> getRelationByAgentId(Long agentId) {
//        Example example = new Example(AgentCompany.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkAgentId", agentId);
//        return agentCompanyMapper.selectByExample(example);
        return agentCompanyMapper.selectList(Wrappers.<AgentCompany>lambdaQuery().eq(AgentCompany::getFkAgentId, agentId));

    }

    private List<SaleContactPersonCompany> getRelationByContactId(Long contactId) {
//        Example example = new Example(ContactPersonCompany.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkContactPersonId", contactId);
//        return contactPersonCompanyMapper.selectByExample(example);
        return contactPersonCompanyMapper.selectList(Wrappers.<SaleContactPersonCompany>lambdaQuery().eq(SaleContactPersonCompany::getFkContactPersonId, contactId));
    }


    private List<CompanyTreeVo> getCompanyTreeDto() {
//        ListResponseBo responseBo = permissionCenterClient.getAllCompanyDto();
        Result<List<com.get.permissioncenter.vo.tree.CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
            JsonConfig config = new JsonConfig();
            config.setExcludes(new String[]{"departmentTree", "totalNum"});
            JSONArray jsonArray = JSONArray.fromObject(result.getData(), config);
            return JSONArray.toList(jsonArray, new CompanyTreeVo(), new JsonConfig());
        }
        return Lists.newArrayList();
    }


    private List<CompanyTreeVo> getTreeList(List<CompanyTreeVo> entityList) {
        List<CompanyTreeVo> resultList = new ArrayList<>();
        // 获取顶层元素集合
        String parentId;
        for (CompanyTreeVo entity : entityList) {
            parentId = String.valueOf(entity.getFkParentCompanyId());
            if (parentId == null || "0".equals(parentId)) {
                //获取父节点的部门信息
                resultList.add(entity);
            }
        }
        //假如没有父节点
        if (GeneralTool.isEmpty(resultList)) {
            //获取最小节点
            CompanyTreeVo minTreeNode = entityList.stream().min(Comparator.comparing(CompanyTreeVo::getFkParentCompanyId)).get();
            resultList.add(minTreeNode);
            if (GeneralTool.isNotEmpty(minTreeNode)) {
                //获取相同的最小节点
                List<CompanyTreeVo> minTreeNodes = entityList.stream().filter(treeDto ->
                        treeDto.getFkParentCompanyId().equals(minTreeNode.getFkParentCompanyId()) &&
                                !treeDto.getId().equals(minTreeNode.getId())).distinct().collect(Collectors.toList());
                resultList.addAll(minTreeNodes);
            }
        }
        // 获取每个顶层元素的子数据集合
        for (CompanyTreeVo entity : resultList) {
            entity.setChildList(getSubList(entity.getId(), entityList));
        }
        return resultList;
    }

    private List<CompanyTreeVo> getSubList(String id, List<CompanyTreeVo> entityList) {
        List<CompanyTreeVo> childList = new ArrayList<>();
        String parentId;
        // 子集的直接子对象
        for (CompanyTreeVo entity : entityList) {
            parentId = String.valueOf(entity.getFkParentCompanyId());
            if (id.equals(parentId)) {
                //获取子节点的部门信息
                childList.add(entity);
            }
        }
        // 子集的间接子对象
        for (CompanyTreeVo entity : childList) {
            //递归调用
            entity.setChildList(getSubList(entity.getId(), entityList));
        }
        // 递归退出条件
        if (childList.size() == 0) {
            return null;
        }
        return childList;
    }
}
