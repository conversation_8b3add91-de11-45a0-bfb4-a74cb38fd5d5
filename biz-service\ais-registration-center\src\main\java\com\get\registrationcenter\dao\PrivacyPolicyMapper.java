package com.get.registrationcenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.registrationcenter.entity.RegistrationPrivacyPolicy;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface PrivacyPolicyMapper extends BaseMapper<RegistrationPrivacyPolicy> {

    int insertSelective(RegistrationPrivacyPolicy record);

    String getPrivacyPolicyTitleById(Long id);
}