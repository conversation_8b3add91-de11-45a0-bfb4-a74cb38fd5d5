package com.get.votingcenter;

import com.get.common.constant.AppCenterConstant;
import com.get.core.cloud.feign.EnableGetFeign;
import com.get.core.start.GetApplication;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.SpringCloudApplication;

@EnableGetFeign
@SpringCloudApplication
public class VotingCenterApplication {


    private static String url;

    @Value("${get.datasource.dev.voting.url}")
    public void getUrl(String url) {
        this.url=url;
    }

    public static void main(String[] args) {
        System.out.println("url = " + url);
        GetApplication.run(AppCenterConstant.APPLICATION_VOTING_CENTER, VotingCenterApplication.class, args);
        System.out.println("url = " + url);
    }

}
