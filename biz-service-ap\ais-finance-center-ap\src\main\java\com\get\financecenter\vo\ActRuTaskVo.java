package com.get.financecenter.vo;


import com.get.financecenter.entity.FinanceActRuTask;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2020/11/27 11:09
 */
@Data
public class ActRuTaskVo  {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("当前步骤名称")
    private String taskName;
    @ApiModelProperty("版本")
    private int procdefVersion;
    @ApiModelProperty("申请时间")
    private Date procdefStartDate;
    @ApiModelProperty("流程名字")
    private String procdefName;
    @ApiModelProperty("实例id")
    private String procInstId;
    @ApiModelProperty("业务表id")
    private String businessKey;
    @ApiModelProperty("流程实例id")
    private String deployId;
    @ApiModelProperty("流程key")
    private String procdefKey;

    //=========实体类===========

    private String id;

    private Integer rev;

    private String executionId;

    private String procDefId;

    private String name;

    private String parentTaskId;

    private String description;

    private String taskDefKey;

    private String owner;

    private String assignee;

    private String delegation;

    private Integer priority;

    private Date createTime;

    private Date dueDate;

    private String category;

    private Integer suspensionState;

    private String tenantId;

    private String formKey;

    private Date claimTime;

}
