package com.get.financecenter.controller;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.financecenter.dto.ExpenseClaimAgentContentDto;
import com.get.financecenter.service.ExpenseClaimAgentContentService;
import com.get.financecenter.vo.ExpenseClaimAgentContentVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 代理关联费用报销管理
 */
@Api(tags = "代理关联费用报销管理")
@RestController
@RequestMapping("finance/expenseClaimAgentContent")
public class ExpenseClaimAgentContentController{
    /**
     * 服务对象
     */
    @Resource
    private ExpenseClaimAgentContentService expenseClaimAgentContentService;

    /**
     * 分页查询所有数据
     * @param page                      分页对象
     * @return 所有数据
     */
    @ApiOperation(value = "分页查询所有数据", notes = "分页查询所有数据")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/代理关联费用报销管理/分页查询所有数据")
    @PostMapping("list")
    public ResponseBo<ExpenseClaimAgentContentVo> selectAll(@RequestBody SearchBean<ExpenseClaimAgentContentDto> page) {
        List<ExpenseClaimAgentContentVo> datas = expenseClaimAgentContentService.getExpenseClaimAgentContents(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 新增数据(批量新增)
     * @param expenseClaimAgentContentDto 实体对象
     * @return 新增结果
     */

    @ApiOperation(value = "新增数据(批量新增)", notes = "新增数据(批量新增)")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/代理关联费用报销管理/新增数据(批量新增)")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody ExpenseClaimAgentContentDto expenseClaimAgentContentDto) {
        expenseClaimAgentContentService.batchAdd(expenseClaimAgentContentDto);
        return SaveResponseBo.ok();
    }


    @ApiOperation(value = "修改数据", notes = "修改数据")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/代理关联费用报销管理/修改数据")
    @PostMapping("update")
    public ResponseBo<ExpenseClaimAgentContentVo> update(@RequestBody ExpenseClaimAgentContentDto expenseClaimAgentContentDto) {
        return UpdateResponseBo.ok(expenseClaimAgentContentService.update(expenseClaimAgentContentDto));
    }


    @ApiOperation(value = "删除数据",  notes = "删除数据")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/代理关联费用报销管理/删除数据")
    @PostMapping("delete")
    public ResponseBo delete(@RequestParam("id") Long id) {
        expenseClaimAgentContentService.delete(id);
        return DeleteResponseBo.ok();
    }

    @ApiOperation(value = "批量删除" , notes = "批量删除")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/代理关联费用报销管理/批量删除")
    @PostMapping("batchDelete")
    public ResponseBo batchDelete(@RequestBody List<Long> ids) {
        expenseClaimAgentContentService.batchDelete(ids);
        return DeleteResponseBo.ok();
    }

//    @ApiOperation(value = "排序（交换）", notes = "排序接口")
//    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/代理关联费用报销管理/排序（交换）")
//    @PostMapping("sort")
//    public ResponseBo sort(@RequestBody List<Long> ids) {
//        expenseClaimAgentContentService.sort(ids);
//        return UpdateResponseBo.ok();
//    }

    @ApiOperation(value = "排序（拖拽）", notes = "排序接口")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/代理关联费用报销管理/排序（拖拽）")
    @PostMapping("sort")
    public ResponseBo sort(@RequestParam("start") Integer start , @RequestParam("end") Integer end) {
        expenseClaimAgentContentService.movingOrder(start, end);
        return UpdateResponseBo.ok();
    }

    @ApiOperation(value = "费用报销关联代理内容下拉", notes = "费用报销关联代理内容下拉")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/代理关联费用报销管理/费用报销关联代理内容下拉")
    @GetMapping("getExpenseClaimAgentContentList")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<BaseSelectEntity> getExpenseClaimAgentContentList() {
        return new ListResponseBo<>(expenseClaimAgentContentService.getExpenseClaimAgentContentList());
    }


}

