package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/11/18
 * @TIME: 16:28
 * @Description:
 **/
@Data
public class RStudentOfferItemStepDto  extends BaseVoEntity{

    /**
     * 学生申请方案项目Id
     */
    @ApiModelProperty(value = "学生申请方案项目Id", required = true)
    @NotNull(message = "学生申请方案项目Id不能为空", groups = {Add.class, Update.class})
    private Long fkStudentOfferItemId;

    /**
     * 学生申请方案项目状态步骤Id
     */
    @ApiModelProperty(value = "学生申请方案项目状态步骤Id", required = true)
    @NotNull(message = "学生申请方案项目状态步骤Id不能为空", groups = {Add.class, Update.class})
    private Long fkStudentOfferItemStepId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "学习计划备注")
    private String studentOfferItemRemark;


    /**
     * 申请方式：0网申/1扫描/2原件邮递/3其他
     */
    @ApiModelProperty(value = "申请方式：0网申/1扫描/2原件邮递/3其他")
    private Integer appMethod;
    /**
     * 申请备注（网申信息）
     */
    @ApiModelProperty(value = "申请备注（网申信息）")
    private String appRemark;

    /**
     * 学生ID
     */
    @ApiModelProperty(value = "学生ID")
    private String studentId;

    /**
     * 开学时间
     */
    @ApiModelProperty(value = "开学时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openingTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date closingTime;

    /**
     * 课程长度类型(0周、1学期、2年)
     */
    @ApiModelProperty(value = "课程长度类型(0周、1学期、2年)")
    private Integer durationType;

    /**
     * 课程长度
     */
    @ApiModelProperty(value = "课程长度")
    private BigDecimal duration;

    /**
     * 是否主课程：0否/1是（一套方案，只有一个主课程）
     */
    @ApiModelProperty(value = "是否主课程：0否/1是（一套方案，只有一个主课程）")
    private Boolean isMain;

    /**
     * 是否减免学分：0否/1是
     */
    @ApiModelProperty(value = "是否减免学分：0否/1是")
    private Boolean isCreditExemption;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    /**
     * 学费金额
     */
    @ApiModelProperty(value = "学费金额")
    @NotNull(message = "学费不能为空", groups = {Add.class, Update.class})
    private BigDecimal tuitionAmount;

    /**
     * 学习计划业务状态(多选)：0获得第一阶段佣金、1获得第二阶段佣金、2获得第三阶段佣金、3获得第四阶段佣金 / 4只读第一阶段、5只读第二阶段、6只读第三阶段、7只读第四阶段
     */
    @ApiModelProperty(value = "学习计划业务状态(多选)：0获得第一阶段佣金、1获得第二阶段佣金、2获得第三阶段佣金、3获得第四阶段佣金 / 4只读第一阶段、5只读第二阶段、6只读第三阶段、7只读第四阶段")
    private String conditionType;

    /**
     * 入学失败原因Id
     */
    @ApiModelProperty(value = "入学失败原因Id")
    private Long fkEnrolFailureReasonId;

    /**
     * 其他入学失败原因
     */
    @ApiModelProperty(value = "其他入学失败原因")
    private String otherFailureReason;

    /**
     * 学习模式：枚举定义：0未定/1面授/2网课
     */
    @ApiModelProperty(value = "学习模式：枚举定义：0未定/1面授/2网课")
    private Integer learningMode;

    @ApiModelProperty("支付押金时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date depositTime;

    @ApiModelProperty(value = "支付学费时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date tuitionTime;

    /**
     * 支付押金截止时间
     */
    @ApiModelProperty(value = "支付押金截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date depositDeadline;

    /**
     * 接受Offer截止时间
     */
    @ApiModelProperty(value = "接受Offer截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date acceptOfferDeadline;


    /**
     * 延迟入学时间列表
     */
    @ApiModelProperty(value = "延迟入学时间列表")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private List<Date> deferEntranceTimes;

    @ApiModelProperty(value = "设置延迟入学时间 (有数据则表示要新增延迟时间)修改")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deferEntranceTime;

    /**
     * 是否延迟入学
     */
    @ApiModelProperty(value = "是否延迟入学")
    private Boolean isDeferEntrance;

    @ApiModelProperty("状态变更开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startGmtCreate;

    @ApiModelProperty("状态变更结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endGmtCreate;

    @ApiModelProperty("提交申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date submitAppTime;

    /**
     * 学校提供商Id
     */
    @ApiModelProperty(value = "学校提供商Id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

    @ApiModelProperty(value = "课程id")
    private Long fkInstitutionCourseId;

    @ApiModelProperty(value = "旧课程系统名称")
    private String oldCourseCustomName;
    /**
     * 渠道来源Id
     */
    @ApiModelProperty(value = "渠道来源Id")
    private Long fkInstitutionChannelId;

    @ApiModelProperty(value = "新申请状态：枚举：0缺资料/1未开放")
    private Integer newAppStatus;

    @ApiModelProperty(value = "是否失败成功客户列表进入")
    private Boolean isFailurePage = false;

    @ApiModelProperty(value = "子课程学习计划")
    private List<SonStudentItem> sonStudentItemList;

    @ApiModelProperty(value = "是否成功客户列表设置退押金进入")
    private Boolean isApplyRefundPage = false;

    @ApiModelProperty(value = "同步主课申请步骤")
    private Boolean synchmainItem = false;

    @ApiModelProperty("保险购买方式枚举：0买学校保险/1通过Hti买保险/2通过其他机构买保险")
    private Integer insuranceBuyMethod;

    @ApiModelProperty("押金支付方式枚举：0飞汇/1易思汇/2支付宝/3银行/4信用卡/5其他汇款平台")
    private Integer depositPaymentMethod;

    @ApiModelProperty("学费支付方式枚举：0=飞汇/1=易思汇/2=支付宝/3=银行/4=信用卡/5=其他汇款平台")
    private Integer tuitionPaymentMethod;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SonStudentItem{

        @ApiModelProperty(value = "子课程学习计划id")
        private Long itemId;

        @ApiModelProperty(value = "学生编号")
        private String studentId;
    }

   
}