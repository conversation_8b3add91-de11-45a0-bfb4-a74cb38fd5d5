package com.get.salecenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.salecenter.dao.convention.CyclingRegistrationMapper;
import com.get.salecenter.vo.CyclingRegistrationVo;
import com.get.salecenter.vo.CyclingRegistrationExportVo;
import com.get.salecenter.entity.ConventionPerson;
import com.get.salecenter.entity.CyclingRegistration;
import com.get.salecenter.service.ConventionCyclingRegistrationService;
import com.get.salecenter.service.IConventionPersonService;
import com.get.salecenter.service.IConventionService;
import com.get.salecenter.dto.CyclingRegistrationDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/10/14 11:14
 */
@Service
public class ConventionCyclingRegistrationServiceImpl implements ConventionCyclingRegistrationService {

    @Resource
    private CyclingRegistrationMapper cyclingRegistrationMapper;
    @Resource
    private IConventionService conventionService;
    @Resource
    private IConventionPersonService conventionPersonService;

    /**
     * 公益骑行列表
     *
     * @Date 11:31 2021/10/14
     * <AUTHOR>
     */
    @Override
    public List<CyclingRegistrationVo> getCyclingRegistrationList(CyclingRegistrationDto cyclingRegistrationDto) {
        LambdaQueryWrapper<CyclingRegistration> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CyclingRegistration::getFkConventionId, cyclingRegistrationDto.getFkConventionId());
        if (GeneralTool.isNotEmpty(cyclingRegistrationDto.getName())) {
            lambdaQueryWrapper.like(CyclingRegistration::getName, cyclingRegistrationDto.getName());
        }
        if (GeneralTool.isNotEmpty(cyclingRegistrationDto.getGender())) {
            lambdaQueryWrapper.eq(CyclingRegistration::getGender, cyclingRegistrationDto.getGender());
        }
        if (GeneralTool.isNotEmpty(cyclingRegistrationDto.getMobile())) {
            lambdaQueryWrapper.eq(CyclingRegistration::getMobile, cyclingRegistrationDto.getMobile());
        }
        if (GeneralTool.isNotEmpty(cyclingRegistrationDto.getIdType())) {
            lambdaQueryWrapper.eq(CyclingRegistration::getIdType, cyclingRegistrationDto.getIdType());
        }
        if (GeneralTool.isNotEmpty(cyclingRegistrationDto.getIdNum())) {
            lambdaQueryWrapper.eq(CyclingRegistration::getIdNum, cyclingRegistrationDto.getIdNum());
        }
        if (GeneralTool.isNotEmpty(cyclingRegistrationDto.getCompany())) {
            lambdaQueryWrapper.eq(CyclingRegistration::getCompany, cyclingRegistrationDto.getCompany());
        }
        if (GeneralTool.isNotEmpty(cyclingRegistrationDto.getPayStatus())) {
            lambdaQueryWrapper.eq(CyclingRegistration::getPayStatus, cyclingRegistrationDto.getPayStatus());
        }
        if (GeneralTool.isNotEmpty(cyclingRegistrationDto.getPayOrderNum())) {
            lambdaQueryWrapper.eq(CyclingRegistration::getPayOrderNum, cyclingRegistrationDto.getPayOrderNum());
        }
        //参会人员类型和bd名称过滤
        if (GeneralTool.isNotEmpty(cyclingRegistrationDto.getType()) || GeneralTool.isNotEmpty(cyclingRegistrationDto.getBdNameKey())) {
            LambdaQueryWrapper<ConventionPerson> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ConventionPerson::getFkConventionId, cyclingRegistrationDto.getFkConventionId());
            //查询条件-类别
            if(GeneralTool.isNotEmpty(cyclingRegistrationDto.getType())){
                wrapper.eq(ConventionPerson::getType, cyclingRegistrationDto.getType());
            }
            //查询条件-BD名称
            if (GeneralTool.isNotEmpty(cyclingRegistrationDto.getBdNameKey())) {
                //根据bdName模糊查询bdNumList
                List<String> bdCodeList = conventionPersonService.getBdCode(cyclingRegistrationDto.getBdNameKey());
                if (GeneralTool.isNotEmpty(bdCodeList)) {
                    wrapper.in(ConventionPerson::getBdCode, bdCodeList);
                }else {
                    return Collections.emptyList();
                }
            }
            List<ConventionPerson> list = conventionPersonService.list(wrapper);
            lambdaQueryWrapper.in(CyclingRegistration::getFkConventionPersonId,GeneralTool.isNotEmpty(list) ? list.stream().map(ConventionPerson::getId).collect(Collectors.toSet()) : Collections.emptyList());
        }

        List<CyclingRegistration> cyclingRegistrations = cyclingRegistrationMapper.selectList(lambdaQueryWrapper);

        List<CyclingRegistrationVo> cyclingRegistrationVos = BeanCopyUtils.copyListProperties(cyclingRegistrations, CyclingRegistrationVo::new);
        //查询参会人员对应类型以及bd名称、峰会手机号
        Set<Long> fkConventionPersonIds = cyclingRegistrations.stream().map(CyclingRegistration::getFkConventionPersonId).collect(Collectors.toSet());
        if(GeneralTool.isNotEmpty(fkConventionPersonIds)){
            LambdaQueryWrapper<ConventionPerson> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(ConventionPerson::getId, fkConventionPersonIds);
            List<ConventionPerson> personList = conventionPersonService.list(wrapper);

            List<String> bdlist = personList.stream().map(ConventionPerson::getBdCode).filter(Objects::nonNull).collect(Collectors.toList());
            Map<String, String> bdNameMap = conventionPersonService.getBdNameMap(bdlist);
            Map<Long, ConventionPerson> personMap = personList.stream().collect(Collectors.toMap(ConventionPerson::getId, Function.identity()));
            for(CyclingRegistrationVo dto : cyclingRegistrationVos){
                if(GeneralTool.isNotEmpty(dto.getFkConventionPersonId())){
                    ConventionPerson person = personMap.get(dto.getFkConventionPersonId());
                    if(GeneralTool.isNotEmpty(person)){
                        dto.setType(person.getType());
                        dto.setTel(person.getTel());
                        dto.setBdName(bdNameMap.get(person.getBdCode()));
                    }

                }
            }
        }

        return cyclingRegistrationVos;
    }

    @Override
    public void exportCyclingRegistrationExcel(CyclingRegistrationDto cyclingRegistrationDto, HttpServletResponse response) {

        List<CyclingRegistrationVo> cyclingRegistrationList = getCyclingRegistrationList(cyclingRegistrationDto);
        Set<Long> fkConventionIds = cyclingRegistrationList.stream().map(CyclingRegistrationVo::getFkConventionId).collect(Collectors.toSet());
        Map<Long, String> conventionNameMap = conventionService.getConventionNameByIds(fkConventionIds);

        List<CyclingRegistrationExportVo> collect = cyclingRegistrationList.stream().map(c -> {
            CyclingRegistrationExportVo cyclingRegistrationExportVo = BeanCopyUtils.objClone(c, CyclingRegistrationExportVo::new);
            //骑行路线
            if("A_LAKE_WARRIOR_ROUTE".equals(c.getCyclingRoute())){
                cyclingRegistrationExportVo.setCyclingRouteName("A 环湖勇士线");
            }
            if("B_CHARITY_ROUTE".equals(c.getCyclingRoute())){
                cyclingRegistrationExportVo.setCyclingRouteName("B 公益爱心线");
            }

            //性别
            if (c.getGender().equals(0)) {
                cyclingRegistrationExportVo.setGenderName("女");
            } else {
                cyclingRegistrationExportVo.setGenderName("男");
            }

            //身份类型:0身份证/1护照/2港澳回乡证/3台胞证
            if(c.getIdType().equals(0)){
                cyclingRegistrationExportVo.setIdTypeName("身份证");
            }else if(c.getIdType().equals(1)){
                cyclingRegistrationExportVo.setIdTypeName("护照");
            }else if(c.getIdType().equals(2)){
                cyclingRegistrationExportVo.setIdTypeName("港澳回乡证");
            }else if(c.getIdType().equals(3)){
                cyclingRegistrationExportVo.setIdTypeName("台胞证");
            }

            //支付状态
            if (c.getPayStatus().equals(0)) {
                cyclingRegistrationExportVo.setPayStatusName("失败");
            }else{
                cyclingRegistrationExportVo.setPayStatusName("成功");
            }
            //生日
            SimpleDateFormat format = new SimpleDateFormat("YYYY-MM-dd");
            if(GeneralTool.isNotEmpty(c.getBirthday())){
                cyclingRegistrationExportVo.setBirthday(format.format(c.getBirthday()));
            }
            //创建时间
            if(GeneralTool.isNotEmpty(c.getBirthday())){
                cyclingRegistrationExportVo.setGmtCreate(format.format(c.getGmtCreate()));
            }


            cyclingRegistrationExportVo.setConventionName(conventionNameMap.get(c.getFkConventionId()));
            return cyclingRegistrationExportVo;
        }).collect(Collectors.toList());

        FileUtils.exportExcelNotWrapText(response, collect, "CyclingRegistration", CyclingRegistrationExportVo.class);


    }

    /**
     * Author Cream
     * Description : //增加编辑备注
     * Date 2023/4/27 17:22
     * Params:
     * Return
     */
    @Override
    public SaveResponseBo editRemark(Long id, String remark) {
        if (Objects.isNull(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        CyclingRegistration cyclingRegistration = cyclingRegistrationMapper.selectById(id);
        if (Objects.isNull(cyclingRegistration)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        cyclingRegistration.setRemark(remark);
        cyclingRegistrationMapper.updateByIdWithNull(cyclingRegistration);
        return SaveResponseBo.ok(id);
    }

    /**
     * Author Cream
     * Description : // 删除记录
     * Date 2023/4/27 17:22
     * Params:
     * Return
     */
    @Override
    public DeleteResponseBo delete(Long id) {
        if (Objects.isNull(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        CyclingRegistration cyclingRegistration = cyclingRegistrationMapper.selectById(id);
        if (Objects.isNull(cyclingRegistration)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        cyclingRegistrationMapper.deleteById(id);
        return new DeleteResponseBo();
    }


    /**
     * Author Cream
     * Description : //更新支付状态
     * Date 2023/4/27 17:22
     * Params:
     * Return
     */
    @Override
    public SaveResponseBo updatePayStatus(Long id, Integer status) {
        if (Objects.isNull(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        CyclingRegistration cyclingRegistration = cyclingRegistrationMapper.selectById(id);
        if (Objects.isNull(cyclingRegistration)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        cyclingRegistration.setPayStatus(status);
        cyclingRegistrationMapper.updateById(cyclingRegistration);
        return SaveResponseBo.ok(id);
    }


}
