<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.EventPlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.EventPlan">
        <id column="id" property="id" />
        <result column="fk_company_id" property="fkCompanyId" />
        <result column="year" property="year" />
        <result column="title" property="title" />
        <result column="description" property="description" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_create_user" property="gmtCreateUser" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="gmt_modified_user" property="gmtModifiedUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, fk_company_id, year, title, description, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
    </sql>

    <select id="getEventPlans" resultType="com.get.salecenter.vo.EventPlanVo">
       SELECT m.*,t1.onlineCount,t2.offlineCount,t3.workshopCount,t4.registrationCount FROM m_event_plan AS m
        <!-- 线上活动合计 -->
       LEFT JOIN (
        SELECT mep.id,COUNT(mepto.id) AS onlineCount
        FROM m_event_plan AS mep
        INNER JOIN m_event_plan_theme AS mept ON mep.id = mept.fk_event_plan_id
        INNER JOIN m_event_plan_theme_online AS mepto  ON mept.id = mepto.fk_event_plan_theme_id
        <where>
            AND mept.is_active = 1
            AND mepto.is_active = 1
        </where>
        GROUP BY mep.id
        )AS t1 ON m.id = t1.id

        <!-- 线下活动合计 -->
        LEFT JOIN (
        SELECT mep.id,COUNT(mepto.id) AS offlineCount
        FROM m_event_plan AS mep
        INNER JOIN m_event_plan_theme AS mept ON mep.id = mept.fk_event_plan_id
        INNER JOIN m_event_plan_theme_offline AS mepto  ON mept.id = mepto.fk_event_plan_theme_id
        <where>
            AND mept.is_active = 1
            AND mepto.is_active = 1
        </where>
        GROUP BY mep.id
        )AS t2 ON m.id = t2.id

        <!-- 线下专访合计 -->
        LEFT JOIN (
        SELECT mep.id,COUNT(meptw.id) AS workshopCount
        FROM m_event_plan AS mep
        INNER JOIN m_event_plan_theme AS mept ON mep.id = mept.fk_event_plan_id
        INNER JOIN m_event_plan_theme_workshop AS meptw  ON mept.id = meptw.fk_event_plan_theme_id
        <where>
            AND mept.is_active = 1
            AND meptw.is_active = 1
        </where>
        GROUP BY mep.id
        )AS t3 ON m.id = t3.id

        <!-- 报名名册合计 -->
        LEFT JOIN (
        SELECT mep.id,COUNT(mepr.id) AS registrationCount
        FROM m_event_plan AS mep
        INNER JOIN m_event_plan_registration AS mepr ON mep.id = mepr.fk_event_plan_id
        <where>
            <if test="eventPlanSearchDto.fkCompanyId != null and eventPlanSearchDto.fkCompanyId != ''">
                AND mepr.fk_company_id = #{eventPlanSearchDto.fkCompanyId}
            </if>
        </where>
        GROUP BY mep.id
        )AS t4 ON m.id = t4.id
        <where>
            <if test="eventPlanSearchDto.fkCompanyId != null and eventPlanSearchDto.fkCompanyId != ''">
                AND m.fk_company_id = #{eventPlanSearchDto.fkCompanyId}
            </if>
            <if test="eventPlanSearchDto.year != null">
                AND m.`year` = #{eventPlanSearchDto.year}
            </if>
            <if test="eventPlanSearchDto.keyword != null and eventPlanSearchDto.keyword != ''">
                AND m.title LIKE CONCAT('%',#{eventPlanSearchDto.keyword},'%')
            </if>
        </where>
        ORDER BY m.gmt_create DESC
    </select>


    <select id="getCompanyList" resultType="java.lang.Long">
        SELECT fk_company_id FROM m_event_plan GROUP BY fk_company_id
    </select>

    <select id="getYearList" resultType="java.lang.Integer">
        SELECT `year` FROM m_event_plan GROUP BY `year`
    </select>
</mapper>
