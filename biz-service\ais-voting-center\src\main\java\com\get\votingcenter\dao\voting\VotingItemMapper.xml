<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.votingcenter.dao.voting.VotingItemMapper">
  <insert id="insertSelective" parameterType="com.get.votingcenter.entity.VotingItem" keyProperty="id" useGeneratedKeys="true">
    insert into m_voting_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkVotingId != null">
        fk_voting_id,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkVotingId != null">
        #{fkVotingId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
    <select id="getMaxViewOrder" resultType="java.lang.Integer">
      select ifnull(max(view_order)+1,1) from m_voting_item
    </select>
    <select id="getVotingItems" resultType="com.get.votingcenter.vo.VotingItemVo">
      SELECT
      vi.id,
      vi.fk_voting_id as fkVotingId,
      vi.title,
      vi.description,
      vi.start_time as startTime,
      vi.end_time as endTime,
      vi.`status`,
      vi.view_order as viewOrder,
      v.fk_company_id as fkCompanyId
      FROM
        m_voting_item AS vi
          INNER JOIN
        m_voting AS v
        ON
          vi.fk_voting_id = v.id
      WHERE
        1=1
      <if test="votingItemListDto.fkVotingId != null">
        and vi.fk_voting_id = #{votingItemListDto.fkVotingId}
      </if>
      <if test="votingItemListDto.title != null and votingItemListDto.title != ''">
        and vi.title = #{votingItemListDto.title}
      </if>
      <if test="votingItemListDto.fkCompanyId != null">
        and v.fk_company_id = #{votingItemListDto.fkCompanyId}
      </if>
      ORDER BY vi.fk_voting_id asc ,vi.view_order desc
    </select>
</mapper>