package com.get.filecenter.config;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.region.Region;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class ConnectTencentCloud {
    private static COSClient cosClient;

    private static String apCity;
    private static String secretId;
    private static String secretKey;

    private static String privateApCity;
    private static String privateSecretId;
    private static String privateSecretKey;

    private static String privateShareApCity;
    private static String privateShareSecretId;
    private static String privateShareSecretKey;

    private static String htiApCity;
    private static String htiSecretId;
    private static String htiSecretKey;

    @Value("${spring.tencentcloudimage.apCity}")
    public void setApCity(String apCity) {ConnectTencentCloud.apCity = apCity;}
    @Value("${spring.tencentcloudimage.secretId}")
    public void setSecretId(String secretId) {
        ConnectTencentCloud.secretId = secretId;
    }
    @Value("${spring.tencentcloudimage.secretKey}")
    public void setSecretKey(String secretKey) {
        ConnectTencentCloud.secretKey = secretKey;
    }

    @Value("${spring.tencentcloudfile.apCity}")
    public void setPrivateApCity(String apCity) {ConnectTencentCloud.privateApCity = apCity;}
    @Value("${spring.tencentcloudfile.secretId}")
    public void setPrivateSecretId(String secretId) {
        ConnectTencentCloud.privateSecretId = secretId;}
    @Value("${spring.tencentcloudfile.secretKey}")
    public void setPrivateSecretKey(String secretKey) {ConnectTencentCloud.privateSecretKey = secretKey;}

    @Value("${spring.tencentCloudShareFile.apCity}")
    public void setPrivateShareApCity(String apCity) {ConnectTencentCloud.privateShareApCity = apCity;}
    @Value("${spring.tencentCloudShareFile.secretId}")
    public void setPrivateShareSecretId(String secretId) {ConnectTencentCloud.privateShareSecretId = secretId;}
    @Value("${spring.tencentCloudShareFile.secretKey}")
    public void setPrivateShareSecretKey(String secretKey) {ConnectTencentCloud.privateShareSecretKey = secretKey;}

    @Value("${spring.htitencentcloudimage.apCity}")
    public void setHtiApCity(String apCity) {ConnectTencentCloud.htiApCity = apCity;}
    @Value("${spring.htitencentcloudimage.secretId}")
    public void setHtiSecretId(String secretId) {
        ConnectTencentCloud.htiSecretId = secretId;
    }
    @Value("${spring.htitencentcloudimage.secretKey}")
    public void setHtiSecretKey(String secretKey) {
        ConnectTencentCloud.htiSecretKey = secretKey;
    }

    //返回公开桶的client对象
    public static COSClient getPublicCosClient()
    {
        COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);
        Region region = new Region(apCity);
        ClientConfig clientconfig = new ClientConfig(region);
        cosClient = new COSClient(cred, clientconfig);
        return cosClient;
    }

    //返回默认私密桶的client对象
    public static COSClient getPrivateCosClient()
    {
        COSCredentials cred = new BasicCOSCredentials(privateSecretId, privateSecretKey);
        Region region = new Region(privateApCity);
        ClientConfig clientconfig = new ClientConfig(region);
        cosClient = new COSClient(cred, clientconfig);
        return cosClient;
    }

    //返回Share私密桶的client对象
    public static COSClient getPrivateShareCosClient()
    {
        COSCredentials cred = new BasicCOSCredentials(privateShareSecretId, privateShareSecretKey);
        Region region = new Region(privateShareApCity);
        ClientConfig clientconfig = new ClientConfig(region);
        cosClient = new COSClient(cred, clientconfig);
        return cosClient;
    }

    //返回htii公桶的client对象
    public static COSClient getHtiPublicCosClient()
    {
        COSCredentials cred = new BasicCOSCredentials(htiSecretId, htiSecretKey);
        Region region = new Region(htiApCity);
        ClientConfig clientconfig = new ClientConfig(region);
        cosClient = new COSClient(cred, clientconfig);
        return cosClient;
    }
}
