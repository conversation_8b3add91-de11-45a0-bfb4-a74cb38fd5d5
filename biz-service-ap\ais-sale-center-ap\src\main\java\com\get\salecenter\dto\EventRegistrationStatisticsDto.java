package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2024/3/19 15:52
 * @verison: 1.0
 * @description:
 */
@Data
public class EventRegistrationStatisticsDto {

    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "学校提供商名称")
    private String fkInstitutionProviderName;

    @ApiModelProperty(value = "状态：0计划/1结束/2取消（多选）")
    private List<Integer> eventStatusList;

    @ApiModelProperty(value = "活动主题")
    private String eventTheme;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "活动时间开始")
    private Date eventTimeStart;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "活动时间结束")
    private Date eventTimeEnd;
}
