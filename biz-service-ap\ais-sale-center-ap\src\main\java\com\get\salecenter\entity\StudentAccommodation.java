package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("m_student_accommodation")
public class StudentAccommodation extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 学生Id
     */
    @ApiModelProperty(value = "学生Id")
    @Column(name = "fk_student_id")
    private Long fkStudentId;
    /**
     * 代理Id（业绩绑定）
     */
    @ApiModelProperty(value = "代理Id（业绩绑定）")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 员工Id（业绩绑定，BD）
     */
    @ApiModelProperty(value = "员工Id（业绩绑定，BD）")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;
    /**
     * 留学住宿编号
     */
    @ApiModelProperty(value = "留学住宿编号")
    @Column(name = "num")
    private String num;
    /**
     * 前往国家Id
     */
    @ApiModelProperty(value = "前往国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 前往州省Id
     */
    @ApiModelProperty(value = "前往州省Id")
    @Column(name = "fk_area_state_id")
    private Long fkAreaStateId;
    /**
     * 前往城市Id
     */
    @ApiModelProperty(value = "前往城市Id")
    @Column(name = "fk_area_city_id")
    private Long fkAreaCityId;
    /**
     * 公寓名称
     */
    @ApiModelProperty(value = "公寓名称")
    @Column(name = "apartment_name")
    private String apartmentName;
    /**
     * 入住日期
     */
    @ApiModelProperty(value = "入住日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "check_in_date")
    private Date checkInDate;
    /**
     * 退房日期
     */
    @ApiModelProperty(value = "退房日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "check_out_date")
    private Date checkOutDate;
    /**
     * 住宿天数
     */
    @ApiModelProperty(value = "住宿天数")
    @Column(name = "duration")
    private Integer duration;
    /**
     * 业务渠道Id/佣金合同方Id
     */
    @ApiModelProperty(value = "业务渠道Id/佣金合同方Id")
    @Column(name = "fk_business_channel_id")
    private Long fkBusinessChannelId;

    @ApiModelProperty(value = "业务提供商Id")
    @Column(name = "fk_business_provider_id")
    private Long fkBusinessProviderId;
    /**
     * 押金支付日期
     */
    @ApiModelProperty(value = "押金支付日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "deposit_date")
    private Date depositDate;
    /**
     * 住宿费币种
     */
    @ApiModelProperty(value = "住宿费币种")
    @Column(name = "fk_currency_type_num_accommodation")
    private String fkCurrencyTypeNumAccommodation;
    /**
     * （每周/每月）住宿金额
     */
    @ApiModelProperty(value = "（每周/每月）住宿金额")
    @Column(name = "accommodation_amount_per")
    private BigDecimal accommodationAmountPer;
    /**
     * 枚举：1每周/2每月
     */
    @ApiModelProperty(value = "枚举：1每周/2每月")
    @Column(name = "accommodation_amount_per_unit")
    private Integer accommodationAmountPerUnit;
    /**
     * 总住宿金额
     */
    @ApiModelProperty(value = "总住宿金额")
    @Column(name = "accommodation_amount")
    private BigDecimal accommodationAmount;
    /**
     * 住宿金额说明
     */
    @ApiModelProperty(value = "住宿金额说明")
    @Column(name = "accommodation_amount_note")
    private String accommodationAmountNote;
    /**
     * 佣金币种
     */
    @ApiModelProperty(value = "佣金币种")
    @Column(name = "fk_currency_type_num_commission")
    private String fkCurrencyTypeNumCommission;
    /**
     * 收取佣金比例%
     */
    @ApiModelProperty(value = "收取佣金比例%")
    @Column(name = "commission_rate_receivable")
    private BigDecimal commissionRateReceivable;
    /**
     * 支付佣金比例%（代理）
     */
    @ApiModelProperty(value = "支付佣金比例%（代理）")
    @Column(name = "commission_rate_payable")
    private BigDecimal commissionRatePayable;
    /**
     * 固定收取佣金金额
     */
    @ApiModelProperty(value = "固定收取佣金金额")
    @Column(name = "fixed_amount_receivable")
    private BigDecimal fixedAmountReceivable;
    /**
     * 固定支付佣金金额（代理）
     */
    @ApiModelProperty(value = "固定支付佣金金额（代理）")
    @Column(name = "fixed_amount_payable")
    private BigDecimal fixedAmountPayable;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 状态：0关闭/1打开
     */
    @ApiModelProperty(value = "状态：0关闭/1打开")
    @Column(name = "status")
    private Integer status;
}