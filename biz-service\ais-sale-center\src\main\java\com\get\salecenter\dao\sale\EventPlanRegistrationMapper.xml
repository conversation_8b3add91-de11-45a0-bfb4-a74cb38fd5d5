<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.EventPlanRegistrationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.EventPlanRegistration">
        <id column="id" property="id" />
        <result column="fk_company_id" property="fkCompanyId" />
        <result column="fk_event_plan_id" property="fkEventPlanId" />
        <result column="fk_institution_provider_id" property="fkInstitutionProviderId" />
        <result column="fk_currency_type_num_invoice" property="fkCurrencyTypeNumInvoice" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_create_user" property="gmtCreateUser" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="gmt_modified_user" property="gmtModifiedUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, fk_company_id, fk_event_plan_id, fk_institution_provider_id, fk_currency_type_num_invoice, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
    </sql>

    <select id="getEventPlanRegistrations" resultType="com.get.salecenter.vo.EventPlanRegistrationVo">
        SELECT
        <choose>
            <when test="isPage">
                t.id,t.fk_company_id,t.fk_institution_provider_id
            </when>
            <otherwise>
                t.id,t.fk_company_id AS fkCompanyId,
                t.fkEventPlanId,
                t.fk_institution_provider_id AS fkInstitutionProviderId,
                t.fk_currency_type_num_invoice AS fkCurrencyTypeNumInvoice,
                t.`year`,
                t.personName,
                GROUP_CONCAT(CONCAT("联系人：",t.personName,"，邮箱：",t.personEmail) separator "\n") AS personInformation,
                t.main_title AS mainTitle,
                t.activityName,
                t.fkEventPlanRegistrationEventId,
                t.amount,
                t.fk_currency_type_num AS fkCurrencyTypeNum,
                t.fee,
                t.gmt_create,
                t.date,
                t.isCancel
            </otherwise>
        </choose>
         FROM
        (
            SELECT e.id,e.fk_company_id,e.fk_institution_provider_id,e.fk_currency_type_num_invoice,
            p.id AS fkEventPlanId,
            p.`year`,
            cp.`name` AS personName,
            cp.email AS personEmail,
            t.id AS fkEventPlanThemeId,
            t.main_title,
            t.view_order AS themeViewOrder,
            n.view_order AS onlineViewOrder,
            NULL AS offlineViewOrder,
            NULL AS offlineItemViewOrder,
            NULL AS workshopViewOrder,
            n.`name` AS activityName,
            r.id AS fkEventPlanRegistrationEventId,
            r.gmt_create,
            IFNULL(r.is_cancel,0) AS isCancel,
            r.fk_table_id AS fkEventPlanThemeOnlineId,
            NULL AS fkEventPlanThemeOfflineId,
            NULL AS fkEventPlanThemeOfflineItemId,
            NULL AS fkEventPlanThemeWorkshopId,
            NULL AS areaCountryName,
            NULL AS fkAreaCountryIds,
            NULL AS offlineLocation,
            NULL AS workshopLocation,
            n.amount,
            n.fk_currency_type_num,
            CONCAT(n.amount,n.fk_currency_type_num) AS fee,
            NULL AS date
            FROM
            r_event_plan_registration_event AS r
            INNER JOIN m_event_plan_registration AS e ON r.fk_event_plan_registration_id = e.id
            INNER JOIN m_event_plan AS p ON e.fk_event_plan_id = p.id
            INNER JOIN r_event_plan_registration_contact_person AS cp ON e.id = cp.fk_event_plan_registration_id
            INNER JOIN m_event_plan_theme_online AS n ON r.fk_table_id = n.id AND r.fk_table_name = 'm_event_plan_theme_online'
            INNER JOIN m_event_plan_theme AS t ON t.id = n.fk_event_plan_theme_id

            UNION ALL

            SELECT e.id,e.fk_company_id,e.fk_institution_provider_id,e.fk_currency_type_num_invoice,
            p.id AS fkEventPlanId,
            p.`year`,
            cp.`name` AS personName,
            cp.email AS personEmail,
            t.id AS fkEventPlanThemeId,
            t.main_title,
            t.view_order AS themeViewOrder,
            NULL AS onlineViewOrder,
            f.view_order AS offlineViewOrder,
            fi.view_order AS offlineItemViewOrder,
            NULL AS workshopViewOrder,
            CONCAT(f.area_country_name,',',fi.location) AS activityName,
            r.id AS fkEventPlanRegistrationEventId,
            r.gmt_create,
            IFNULL(r.is_cancel,0) AS isCancel,
            NULL AS fkEventPlanThemeOnlineId,
            f.id AS fkEventPlanThemeOfflineId,
            r.fk_table_id AS fkEventPlanThemeOfflineItemId,
            NULL AS fkEventPlanThemeWorkshopId,
            f.area_country_name AS areaCountryName,
            f.fk_area_country_ids AS fkAreaCountryIds,
            fi.location AS offlineLocation,
            NULL AS workshopLocation,
            fi.amount,
            fi.fk_currency_type_num,
            CONCAT(fi.amount,fi.fk_currency_type_num) AS fee,
            fi.date
            FROM
            r_event_plan_registration_event AS r
            INNER JOIN m_event_plan_registration AS e ON r.fk_event_plan_registration_id = e.id
            INNER JOIN m_event_plan AS p ON e.fk_event_plan_id = p.id
            INNER JOIN r_event_plan_registration_contact_person AS cp ON e.id = cp.fk_event_plan_registration_id
            INNER JOIN m_event_plan_theme_offline_item AS fi ON r.fk_table_id = fi.id AND r.fk_table_name = 'm_event_plan_theme_offline_item'
            INNER JOIN m_event_plan_theme_offline AS f ON fi.fk_event_plan_theme_offline_id = f.id
            INNER JOIN m_event_plan_theme AS t ON t.id = f.fk_event_plan_theme_id

            UNION ALL

            SELECT e.id,e.fk_company_id,e.fk_institution_provider_id,e.fk_currency_type_num_invoice,
            p.id AS fkEventPlanId,
            p.`year`,
            cp.`name` AS personName,
            cp.email AS personEmail,
            t.id AS fkEventPlanThemeId,
            t.main_title,
            t.view_order AS themeViewOrder,
            NULL AS onlineViewOrder,
            NULL AS offlineViewOrder,
            NULL AS offlineItemViewOrder,
            w.view_order AS workshopViewOrder,
            w.location AS activityName,
            r.id AS fkEventPlanRegistrationEventId,
            r.gmt_create,
            IFNULL(r.is_cancel,0) AS isCancel,
            NULL AS fkEventPlanThemeOnlineId,
            NULL AS fkEventPlanThemeOfflineId,
            NULL AS fkEventPlanThemeOfflineItemId,
            r.fk_table_id AS fkEventPlanThemeWorkshopId,
            NULL AS areaCountryName,
            NULL AS fkAreaCountryIds,
            NULl AS offlineLocation,
            w.location AS workshopLocation,
            w.amount,
            w.fk_currency_type_num,
            CONCAT(w.amount,w.fk_currency_type_num) AS fee,
            w.date
            FROM
            r_event_plan_registration_event AS r
            INNER JOIN m_event_plan_registration AS e ON r.fk_event_plan_registration_id = e.id
            INNER JOIN m_event_plan AS p ON e.fk_event_plan_id = p.id
            INNER JOIN r_event_plan_registration_contact_person AS cp ON e.id = cp.fk_event_plan_registration_id
            INNER JOIN m_event_plan_theme_workshop AS w ON r.fk_table_id = w.id AND r.fk_table_name = 'm_event_plan_theme_workshop'
            INNER JOIN m_event_plan_theme AS t ON t.id = w.fk_event_plan_theme_id

            UNION ALL

            SELECT
            e.id,
            e.fk_company_id,
            e.fk_institution_provider_id,
            e.fk_currency_type_num_invoice,
            p.id AS fkEventPlanId,
            p.`year`,
            cp.`name` AS personName,
            cp.email AS personEmail,
            t.id AS fkEventPlanThemeId,
            t.main_title,
            t.view_order AS themeViewOrder,
            NULL AS onlineViewOrder,
            NULL AS offlineViewOrder,
            NULL AS offlineItemViewOrder,
            NULL AS workshopViewOrder,
            NULL AS activityName,
            NULL AS fkEventPlanRegistrationEventId,
            NULL AS isCancel,
            NULL AS gmt_create,
            NULL AS fkEventPlanThemeOnlineId,
            NULL AS fkEventPlanThemeOfflineId,
            NULL AS fkEventPlanThemeOfflineItemId,
            NULL AS fkEventPlanThemeWorkshopId,
            NULL AS areaCountryName,
            NULL AS fkAreaCountryIds,
            NULl AS offlineLocation,
            NULL AS workshopLocation,
            NULL amount,
            NULL fk_currency_type_num,
            NULL AS fee,
            NULL AS date
            FROM
            m_event_plan_registration AS e
            INNER JOIN
            m_event_plan AS p
            ON e.fk_event_plan_id = p.id
            INNER JOIN
            r_event_plan_registration_contact_person AS cp
            ON e.id = cp.fk_event_plan_registration_id
            INNER JOIN
            m_event_plan_theme AS t
            ON t.fk_event_plan_id = t.id

        ) AS t

        <where>
            <if test="registrationDto.fkCompanyId != null and registrationDto.fkCompanyId != ''">
                AND t.fk_company_id = #{registrationDto.fkCompanyId}
            </if>
            <if test="registrationDto.fkInstitutionProviderIds != null and registrationDto.fkInstitutionProviderIds.size > 0">
                AND t.fk_institution_provider_id IN
                <foreach collection="registrationDto.fkInstitutionProviderIds" item="fkInstitutionProviderId" open="(" separator="," close=")">
                    #{fkInstitutionProviderId}
                </foreach>
            </if>
            <if test="registrationDto.year != null and registrationDto.year != ''">
                AND t.`year` = #{registrationDto.year}
            </if>

            <if test="registrationDto.monthKeyword != null and registrationDto.monthKeyword !=''">
                AND t.date LIKE CONCAT('%',#{registrationDto.monthKeyword},'%')
            </if>

            <if test="registrationDto.fkEventPlanId != null and registrationDto.fkEventPlanId != ''">
                AND t.fkEventPlanId = #{registrationDto.fkEventPlanId}
            </if>
            <if test="registrationDto.fkEventPlanThemeIds != null and registrationDto.fkEventPlanThemeIds.size() > 0">
                AND t.fkEventPlanThemeId IN
                <foreach collection="registrationDto.fkEventPlanThemeIds" item="fkEventPlanThemeId" open="(" separator="," close=")">
                    #{fkEventPlanThemeId}
                </foreach>
            </if>
            <if test="registrationDto.fkEventPlanThemeOnlineIds != null and registrationDto.fkEventPlanThemeOnlineIds.size() > 0">
                AND t.fkEventPlanThemeOnlineId IN
                <foreach collection="registrationDto.fkEventPlanThemeOnlineIds" item="fkEventPlanThemeOnlineId" open="(" separator="," close=")">
                    #{fkEventPlanThemeOnlineId}
                </foreach>
            </if>
            <if test="registrationDto.fkEventPlanThemeOfflineIds != null and registrationDto.fkEventPlanThemeOfflineIds.size > 0">
                AND t.fkEventPlanThemeOfflineId IN
                <foreach collection="registrationDto.fkEventPlanThemeOfflineIds" item="fkEventPlanThemeOfflineId" open="(" separator="," close=")">
                    #{fkEventPlanThemeOfflineId}
                </foreach>
            </if>

            <if test="registrationDto.fkEventPlanThemeOfflineItemIds != null and registrationDto.fkEventPlanThemeOfflineItemIds.size > 0">
                AND t.fkEventPlanThemeOfflineItemId IN
                <foreach collection="registrationDto.fkEventPlanThemeOfflineItemIds" item="fkEventPlanThemeOfflineItemId" open="(" separator="," close=")">
                    #{fkEventPlanThemeOfflineItemId}
                </foreach>
            </if>
            <if test="registrationDto.fkEventPlanThemeWorkshopIds != null and registrationDto.fkEventPlanThemeWorkshopIds.size > 0">
                AND t.fkEventPlanThemeWorkshopId IN
                <foreach collection="registrationDto.fkEventPlanThemeWorkshopIds" item="fkEventPlanThemeWorkshopId" open="(" separator="," close=")">
                    #{fkEventPlanThemeWorkshopId}
                </foreach>
            </if>
            <if test="registrationDto.fkAreaCountryIds != null and registrationDto.fkAreaCountryIds.size() > 0">
                <foreach collection="registrationDto.fkAreaCountryIds" item="fkAreaCountryId" open="AND (" separator="or" close=")">
                    FIND_IN_SET(#{fkAreaCountryId},t.fkAreaCountryIds)
                </foreach>
            </if>
            <if test="registrationDto.areaCountryNames !=null and registrationDto.areaCountryNames.size() > 0">
                AND t.areaCountryName IN
                <foreach collection="registrationDto.areaCountryNames" item="areaCountryName" open="(" separator="," close=")">
                    #{areaCountryName}
                </foreach>
            </if>
            <if test="registrationDto.offlineLocations != null and registrationDto.offlineLocations.size() > 0">
                AND t.offlineLocation IN
                <foreach collection="registrationDto.offlineLocations" item="offlineLocation" open="(" separator="," close=")">
                    #{offlineLocation}
                </foreach>
            </if>
            <if test="registrationDto.workShopLocations != null and registrationDto.workShopLocations.size() > 0">
                AND t.workShopLocation IN
                <foreach collection="registrationDto.workShopLocations" item="workShopLocation" open="(" separator="," close=")">
                    #{workShopLocation}
                </foreach>
            </if>

            <if test="registrationDto.fkRegistrationIds != null and registrationDto.fkRegistrationIds.size() > 0">
                AND t.id IN
                <foreach collection="registrationDto.fkRegistrationIds" item="fkRegistrationId" open="(" separator="," close=")">
                    #{fkRegistrationId}
                </foreach>
            </if>
        </where>
        <choose>
            <when test="isPage">
                GROUP BY t.id
            </when>
            <otherwise>
                GROUP BY t.id,t.fkEventPlanRegistrationEventId
            </otherwise>
        </choose>
        ORDER BY
        t.id DESC,
        t.personName ASC
    </select>

</mapper>