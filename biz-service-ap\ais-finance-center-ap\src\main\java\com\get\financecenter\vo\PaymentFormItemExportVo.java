package com.get.financecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2021/12/31
 * @TIME: 15:36
 * @Description:
 **/
@Data
public class PaymentFormItemExportVo implements Serializable {
    /**
     * 付款款单编号（系统生成）
     */
    @ApiModelProperty(value = "付款单编号")
    private String numSystem;
    /**
     * 目标名称
     */
    @ApiModelProperty(value = "目标名称")
    private String targetName;

    /**
     * 付款金额（折合人民币）
     */
    @ApiModelProperty(value = "付款总金额")
    private BigDecimal amountRmb;
    /**
     * 应付计划类型
     */
    @ApiModelProperty(value = "应付计划类型")
    private String fkTypeName;

    /**
     * 应付目标对象
     */
    @ApiModelProperty(value = "应付目标对象")
    private String targetNames;
    /**
     * 应付币种名
     */
    @ApiModelProperty(value = "应付币种")
    private String payablePlanCurrencyName;
    /**
     * 总应付金额
     */
    @ApiModelProperty(value = "总应付金额")
    private BigDecimal payablePlanAmount;
    /**
     * 实付币种
     */
    @ApiModelProperty(value = "实付币种")
    private String payFormCurrency;
    /**
     * 实付金额
     */
    @ApiModelProperty(value = "实付金额")
    private BigDecimal amountPayment;
    /**
     * 汇率（折合应付币种汇率）
     */
    @ApiModelProperty(value = "折合应付汇率")
    private BigDecimal exchangeRatePayable;
    /**
     * 折合金额
     */
    @ApiModelProperty(value = "折合金额")
    private BigDecimal amountPayable;
    /**
     * 汇率调整（可正可负，为平衡计算应收金额）
     */
    @ApiModelProperty(value = "汇率调整金额")
    private BigDecimal amountExchangeRate;
    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String gmtCreateUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private String gmtCreateDate;
}
