package com.get.permissioncenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import com.get.permissioncenter.entity.StaffEmail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

@ApiModel("员工返回类")
@Data
public class StaffVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    //学校权限组 使用字段
    @ApiModelProperty(value = "学校权限组员工Id")
    private Long fkInstitutionPermissionGroupStaffId;
    //学校权限组 使用字段-end

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    @ApiModelProperty(value = "部门编号")
    private String departmentNum;

    @ApiModelProperty(value = "职位名称")
    private String positionName;

    @ApiModelProperty(value = "办公室名称")
    private String officeName;

    @ApiModelProperty(value = "权限资源")
    private Set<String> resourceKeys;

    @ApiModelProperty(value = "业务国家")
    private List<String> areaCountryDtos;

    @ApiModelProperty(value = "业务办公室")
    private List<String> staffOfficeDtos;

    @ApiModelProperty(value = "头像链接")
    private MediaAndAttachedVo headImageDto;

    @ApiModelProperty(value = "直属上司名称")
    private String supervisorName;

    @ApiModelProperty(value = "直属上司公司id")
    private Long supervisorCompanyId;

    @ApiModelProperty(value = "手机区号名称")
    private String mobileAreaCodeName;

    @ApiModelProperty(value = "是否激活")
    private String isActiveName;

    /**
     * 全名
     */
    @ApiModelProperty("全名")
    private String fullName;

    @ApiModelProperty(value = "劳动合开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date contractStartTime;

    @ApiModelProperty(value = "联系地址国家名称")
    private String areaCountryName;

    @ApiModelProperty("联系地址州省名称")
    private String areaStateName;

    @ApiModelProperty("联系地址城市名称")
    private String areaCityName;

    @ApiModelProperty("联系地址城市区域名称")
    private String areaCityDivisionName;

    @ApiModelProperty("户口性质名称")
    private String hukouName;

    @ApiModelProperty("证件类型名称")
    private String identityTypeName;

    @ApiModelProperty(value = "职位编号")
    private String positionNum;

    @ApiModelProperty(value = "员工邮箱List")
    private List<StaffEmail> staffEmailList;

    //=========实体类Staff=============

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;
    /**
     * 部门Id
     */
    @ApiModelProperty(value = "部门Id")
    @Column(name = "fk_department_id")
    private Long fkDepartmentId;
    /**
     * 职位Id
     */
    @ApiModelProperty(value = "职位Id")
    @Column(name = "fk_position_id")
    private Long fkPositionId;
    /**
     * 办公室Id
     */
    @ApiModelProperty(value = "办公室Id")
    @Column(name = "fk_office_id")
    private Long fkOfficeId;
    /**
     * 直属上司Id（公司架构）
     */
    @ApiModelProperty(value = "直属上司Id（公司架构）")
    @Column(name = "fk_staff_id_supervisor")
    @UpdateWithNull
    private Long fkStaffIdSupervisor;
    /**
     * 简历guid(人才中心)
     */
    @ApiModelProperty(value = "简历guid(人才中心)")
    @Column(name = "fk_resume_guid")
    @UpdateWithNull
    private String fkResumeGuid;
    /**
     * 登陆用户Id
     */
    @ApiModelProperty(value = "登陆用户Id")
    @Column(name = "login_id")
    private String loginId;
    /**
     * 登陆用户密码
     */
    @ApiModelProperty(value = "登陆用户密码")
    @Column(name = "login_ps")
    private String loginPs;
    /**
     * 员工编号
     */
    @ApiModelProperty(value = "员工编号")
    @Column(name = "num")
    private String num;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @Column(name = "name")
    private String name;
    /**
     * 英文名
     */
    @ApiModelProperty(value = "英文名")
    @Column(name = "name_en")
    private String nameEn;
    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男")
    @Column(name = "gender")
    private Integer gender;
    /**
     * 生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "生日")
    @Column(name = "birthday")
    private Date birthday;
    /**
     * 户口：农业户口/城镇户口
     */
    @ApiModelProperty(value = "户口：农业户口/城镇户口")
    @Column(name = "hukou")
    @UpdateWithNull
    private Integer hukou;
    /**
     * 证件类型枚举：0身份证/1护照/2通行证/3回乡证
     */
    @ApiModelProperty(value = "证件类型枚举：0身份证/1护照/2通行证/3回乡证")
    @Column(name = "identity_type")
    @UpdateWithNull
    private Integer identityType;
    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    @Column(name = "identity_num")
    @UpdateWithNull
    private String identityNum;
    /**
     * 身份证地址
     */
    @ApiModelProperty(value = "身份证地址")
    @Column(name = "identity_card_address")
    @UpdateWithNull
    private String identityCardAddress;
    /**
     * 住址电话区号
     */
    @ApiModelProperty(value = "住址电话区号")
    @Column(name = "home_tel_area_code")
    @UpdateWithNull
    private String homeTelAreaCode;
    /**
     * 住址电话
     */
    @ApiModelProperty(value = "住址电话")
    @Column(name = "home_tel")
    @UpdateWithNull
    private String homeTel;
    /**
     * 工作电话区号
     */
    @ApiModelProperty(value = "工作电话区号")
    @Column(name = "work_tel_area_code")
    @UpdateWithNull
    private String workTelAreaCode;
    /**
     * 工作电话
     */
    @ApiModelProperty(value = "工作电话")
    @Column(name = "work_tel")
    @UpdateWithNull
    private String workTel;
    /**
     * 手机区号
     */
    @ApiModelProperty(value = "手机区号")
    @Column(name = "mobile_area_code")
    @UpdateWithNull
    private String mobileAreaCode;
    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    @Column(name = "mobile")
    @UpdateWithNull
    private String mobile;
    /**
     * Email
     */
    @ApiModelProperty(value = "Email")
    @Column(name = "email")
    @UpdateWithNull
    private String email;

    @ApiModelProperty(value = "Email密码")
    @Column(name = "email_password")
    @UpdateWithNull
    private String emailPassword;
    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    @Column(name = "zip_code")
    @UpdateWithNull
    private String zipCode;
    /**
     * 联系地址国家Id
     */
    @ApiModelProperty(value = "联系地址国家Id")
    @Column(name = "fk_area_country_id")
    @UpdateWithNull
    private Long fkAreaCountryId;
    /**
     * 联系地址州省Id
     */
    @ApiModelProperty(value = "联系地址州省Id")
    @Column(name = "fk_area_state_id")
    @UpdateWithNull
    private Long fkAreaStateId;
    /**
     * 联系地址城市Id
     */
    @ApiModelProperty(value = "联系地址城市Id")
    @Column(name = "fk_area_city_id")
    @UpdateWithNull
    private Long fkAreaCityId;
    /**
     * 联系地址城市区域Id
     */
    @ApiModelProperty(value = "联系地址城市区域Id")
    @Column(name = "fk_area_city_division_id")
    @UpdateWithNull
    private Long fkAreaCityDivisionId;
    /**
     * 地址
     */
    @ApiModelProperty(value = "联系地址")
    @Column(name = "address")
    @UpdateWithNull
    private String address;
    /**
     * QQ号
     */
    @ApiModelProperty(value = "QQ号")
    @Column(name = "qq")
    @UpdateWithNull
    private String qq;
    /**
     * 微信号
     */
    @ApiModelProperty(value = "微信号")
    @Column(name = "wechat")
    @UpdateWithNull
    private String wechat;
    /**
     * whatsapp号
     */
    @ApiModelProperty(value = "whatsapp号")
    @Column(name = "whatsapp")
    @UpdateWithNull
    private String whatsapp;
    /**
     * 紧急联系人
     */
    @ApiModelProperty(value = "紧急联系人")
    @Column(name = "emergency_contact")
    @UpdateWithNull
    private String emergencyContact;
    /**
     * 紧急联系人关系：父母/妻子/儿女/亲戚/朋友
     */
    @ApiModelProperty(value = "紧急联系人关系：父母/妻子/儿女/亲戚/朋友")
    @Column(name = "emergency_relationship")
    @UpdateWithNull
    private String emergencyRelationship;
    /**
     * 紧急联系人电话
     */
    @ApiModelProperty(value = "紧急联系人电话")
    @Column(name = "emergency_tel")
    @UpdateWithNull
    private String emergencyTel;
    /**
     * 职责
     */
    @ApiModelProperty(value = "职责")
    @Column(name = "job_description")
    @UpdateWithNull
    private String jobDescription;
    /**
     * 工资生效日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "工资生效日期")
    @Column(name = "salary_effective_date")
    @UpdateWithNull
    private Date salaryEffectiveDate;
    /**
     * 基本工资
     */
    @ApiModelProperty(value = "基本工资")
    @Column(name = "salary_base")
    @UpdateWithNull
    private BigDecimal salaryBase;
    /**
     * 绩效工资
     */
    @ApiModelProperty(value = "绩效工资")
    @Column(name = "salary_performance")
    @UpdateWithNull
    private BigDecimal salaryPerformance;
    /**
     * 岗位津贴
     */
    @ApiModelProperty(value = "岗位津贴")
    @Column(name = "allowance_position")
    @UpdateWithNull
    private BigDecimal allowancePosition;
    /**
     * 餐饮津贴
     */
    @ApiModelProperty(value = "餐饮津贴")
    @Column(name = "allowance_catering")
    @UpdateWithNull
    private BigDecimal allowanceCatering;
    /**
     * 交通津贴
     */
    @ApiModelProperty(value = "交通津贴")
    @Column(name = "allowance_transportation")
    @UpdateWithNull
    private BigDecimal allowanceTransportation;
    /**
     * 通讯津贴
     */
    @ApiModelProperty(value = "通讯津贴")
    @Column(name = "allowance_telecom")
    @UpdateWithNull
    private BigDecimal allowanceTelecom;
    /**
     * 其他津贴
     */
    @ApiModelProperty(value = "其他津贴")
    @Column(name = "allowance_other")
    @UpdateWithNull
    private BigDecimal allowanceOther;
    /**
     * 入职时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "入职时间")
    @Column(name = "entry_date")
    @UpdateWithNull
    private Date entryDate;
    /**
     * 转正时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "转正时间")
    @Column(name = "pass_probation_date")
    @UpdateWithNull
    private Date passProbationDate;
    /**
     * 离职时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "离职时间")
    @Column(name = "leave_date")
    @UpdateWithNull
    private Date leaveDate;
    /**
     * 是否在职，0否/1是
     */
    @ApiModelProperty(value = "是否在职，0否/1是")
    @Column(name = "is_on_duty")
    @UpdateWithNull
    private Boolean isOnDuty;
    /**
     * 是否领取离职证明，0否/1是
     */
    @ApiModelProperty(value = "是否领取离职证明，0否/1是")
    @Column(name = "is_get_leaving_certificate")
    @UpdateWithNull
    private Boolean isGetLeavingCertificate;
    /**
     * 是否已经停保，0否/1是
     */
    @ApiModelProperty(value = "是否已经停保，0否/1是")
    @Column(name = "is_stop_social_insurance")
    @UpdateWithNull
    private Boolean isStopSocialInsurance;
    /**
     * 停保年月
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM", timezone = "GMT+8")
    @ApiModelProperty(value = "停保年月")
    @Column(name = "stop_social_insurance_month")
    @UpdateWithNull
    private String stopSocialInsuranceMonth;
    /**
     * 年假基数
     */
    @ApiModelProperty(value = "年假基数")
    @Column(name = "annual_leave_base")
    private BigDecimal annualLeaveBase;
    /**
     * 补休基数
     */
    @ApiModelProperty(value = "补休基数")
    @Column(name = "compensatory_leave_base")
    private BigDecimal compensatoryLeaveBase;
    /**
     * 是否强制修改密码，0否/1是
     */
    @ApiModelProperty(value = "是否强制修改密码，0否/1是")
    @Column(name = "is_modified_ps")
    private Boolean isModifiedPs;
    /**
     * 是否可编辑简历，0否/1是
     */
    @ApiModelProperty(value = "是否可编辑简历，0否/1是")
    @Column(name = "is_modified_resume")
    private Boolean isModifiedResume;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    @Column(name = "id_gea")
    private String idGea;
    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    @Column(name = "id_iae")
    private String idIae;
    /**
     * 是否超级管理员：0否/1是
     */
    @ApiModelProperty(value = "是否超级管理员：0否/1是")
    @Column(name = "is_admin")
    private Boolean isAdmin;

    @ApiModelProperty(value = "是否学生管理员：0否/1是")
    @Column(name = "is_student_admin")
    private Boolean isStudentAdmin;
    /**
     * 用户当前登陆会话id
     */
    @ApiModelProperty(value = "用户当前登录会话id")
    @Column(name = "session_id")
    private String sessionId;
    /**
     * 考勤号
     */
    @ApiModelProperty(value = "考勤号")
    @Column(name = "attendance_num")
    private String attendanceNum;

    @ApiModelProperty(value = "ip白名单")
    @Column(name = "ip_white_list")
    private String ipWhiteList;

    @ApiModelProperty(value = "是否需要验证码")
    @Column(name = "is_vcode_required")
    private Boolean isVcodeRequired;

    @ApiModelProperty(value = "绑定的BD staffIds")
    private String staffIdBdIds;

    @ApiModelProperty(value = "绑定的BD name")
    private String bdNames;

}
