package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ConventionExamPrizesNewVo {

    @ApiModelProperty("参与者")
    private String userName;

    @ApiModelProperty("bd名字")
    private String bdName;

    @ApiModelProperty("考试Id")
    private Long fkExaminationId;

    @ApiModelProperty("场次")
    private String examinationPaperName;

    @ApiModelProperty("场次")
    private Long examinationPaperId;

    @ApiModelProperty("答题数")
    private String questionCount;

    @ApiModelProperty("分数")
    private Integer score;

    @ApiModelProperty("证书类型")
    private String certificateTypeName;

    @ApiModelProperty("奖品类型")
    private String prizeTypeName;

    @ApiModelProperty("中奖情况")
    private String winningSituation;

    @ApiModelProperty("奖品名称")
    private String prizeName;

    @ApiModelProperty("是否兑奖")
    private String isExchangeName;

    @ApiModelProperty("考试用户Id")
    private Long fkUserId;

    @ApiModelProperty("参会人员Id")
    private Long fkConventionPersonId;

    @ApiModelProperty("中奖用户Id")
    private Long prizesUserId;

    @ApiModelProperty("中奖参会人员")
    private Long prizesConventionPersonId;
}
