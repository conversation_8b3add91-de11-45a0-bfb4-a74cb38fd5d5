package com.get.examcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.examcenter.vo.MediaAndAttachedVo;
import com.get.examcenter.vo.ScoreTitleVo;
import com.get.examcenter.entity.ExamMediaAndAttached;
import com.get.examcenter.entity.Examination;
import com.get.examcenter.entity.ScoreTitle;
import com.get.examcenter.mapper.exam.ExaminationMapper;
import com.get.examcenter.mapper.exam.MediaAndAttachedMapper;
import com.get.examcenter.mapper.exam.ScoreTitleMapper;
import com.get.examcenter.service.MediaAndAttachedService;
import com.get.examcenter.service.ScoreTitleService;
import com.get.examcenter.dto.MediaAndAttachedDto;
import com.get.examcenter.dto.ScoreTitleDto;
import com.get.examcenter.dto.query.ScoreTitleQueryDto;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by Jerry.
 * Time: 17:29
 * Date: 2021/8/27
 * Description:称号管理实现类
 */
@Service
public class ScoreTitleServiceImpl implements ScoreTitleService {

    @Resource
    private ScoreTitleMapper scoreTitleMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private MediaAndAttachedService mediaAndAttachedService;
    @Resource
    private MediaAndAttachedMapper mediaAndAttachedMapper;
    @Resource
    private ExaminationMapper examinationMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    /**
     * @Description: 列表数据
     * @Author: Jerry
     * @Date:17:30 2021/8/27
     */
    @Override
    public List<ScoreTitleVo> datas(ScoreTitleQueryDto scoreTitleVo, Page page) {
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        Example example = new Example(ScoreTitle.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<ScoreTitle> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(scoreTitleVo.getTitle())) {
//            criteria.andLike("title","%"+scoreTitleVo.getTitle()+"%");
            lambdaQueryWrapper.like(ScoreTitle::getTitle, scoreTitleVo.getTitle());
        }
        if(GeneralTool.isNotEmpty(scoreTitleVo.getFkExaminationId())){
            lambdaQueryWrapper.eq(ScoreTitle::getFkExaminationId, scoreTitleVo.getFkExaminationId());
        }
        if(GeneralTool.isNotEmpty(scoreTitleVo.getFkCompanyId())){
            lambdaQueryWrapper.eq(ScoreTitle::getFkCompanyId, scoreTitleVo.getFkCompanyId());
        }
//        example.orderBy("viewOrder").desc();
        lambdaQueryWrapper.orderByDesc(ScoreTitle::getViewOrder);
        IPage<ScoreTitle> iPage = scoreTitleMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<ScoreTitle> scoreTitles = iPage.getRecords();

        //获取所有的公司ids
        Set<Long> fkComanyIds = scoreTitles.stream().map(ScoreTitle::getFkCompanyId).collect(Collectors.toSet());
        //根据公司ids获取名称
        Map<Long, String> companyNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkComanyIds)) {
            Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(fkComanyIds);
            if (result.isSuccess() && result.getData() != null) {
                companyNamesByIds = result.getData();
            }
        }

        page.setAll((int) iPage.getTotal());
        if (GeneralTool.isEmpty(scoreTitles)) {
            return new ArrayList<>();
        }
//        page.restPage(scoreTitles);
        List<ScoreTitleVo> scoreTitleVos = new ArrayList<>();
        //表ids
        Set<Long> fkTableIds = scoreTitles.stream().map(ScoreTitle::getId).collect(Collectors.toSet());
        //根据表ids获取批量的附件
        Map<Long, List<MediaAndAttachedVo>> mediaAndAttachedDtoByFkTableIds = mediaAndAttachedService.getMediaAndAttachedDtoByFkTableIds(TableEnum.SCORE_TITLE.key, fkTableIds);
        for (ScoreTitle scoreTitle : scoreTitles) {
            ScoreTitleVo scoreTitleDto = BeanCopyUtils.objClone(scoreTitle, ScoreTitleVo::new);
            //获取考试名
            Examination examination = examinationMapper.selectById(scoreTitle.getFkExaminationId());
            if (GeneralTool.isNotEmpty(examination)){
                scoreTitleDto.setFkExaminationName(examination.getName());
            }
            StringBuilder scoreRange = new StringBuilder();
            StringBuilder rankingRange = new StringBuilder();
            //分值范围
            if (GeneralTool.isNotEmpty(scoreTitleDto.getScoreMin()) && GeneralTool.isNotEmpty(scoreTitleDto.getScoreMax())) {
                scoreRange.append(scoreTitleDto.getScoreMin()).append("-").append(scoreTitleDto.getScoreMax());
            } else if (GeneralTool.isNotEmpty(scoreTitleDto.getScoreMin()) || GeneralTool.isNotEmpty(scoreTitleDto.getScoreMax())) {
                scoreRange.append(GeneralTool.isEmpty(scoreTitleDto.getScoreMin()) ? scoreTitleDto.getScoreMax() : scoreTitleDto.getScoreMin());
            }
            //排名范围
            if (GeneralTool.isNotEmpty(scoreTitleDto.getRankingMin()) && GeneralTool.isNotEmpty(scoreTitleDto.getRankingMax())) {
                rankingRange.append(scoreTitleDto.getRankingMin()).append("-").append(scoreTitleDto.getRankingMax());
            } else if (GeneralTool.isNotEmpty(scoreTitleDto.getRankingMin()) || GeneralTool.isNotEmpty(scoreTitleDto.getRankingMax())) {
                rankingRange.append(GeneralTool.isEmpty(scoreTitleDto.getRankingMin()) ? scoreTitleDto.getRankingMax() : scoreTitleDto.getRankingMin());
            }
            //设置公司名称
            scoreTitleDto.setFkCompanyName(companyNamesByIds.get(scoreTitle.getFkCompanyId()));

            scoreTitleDto.setScoreRange(scoreRange.toString());
            scoreTitleDto.setRankingRange(rankingRange.toString());
            //获取附件
            List<MediaAndAttachedVo> mediaAndAttachedVos = mediaAndAttachedDtoByFkTableIds.get(scoreTitle.getId());
            scoreTitleDto.setMediaAndAttachedVoList(mediaAndAttachedVos);
            scoreTitleVos.add(scoreTitleDto);
        }
        return scoreTitleVos;
    }


    /**
     * @Description: 列表数据，不分页（给排行榜使用，需要查询附件）
     * @Author: Jerry
     * @Date:10:02 2021/9/7
     */
    @Override
    public List<ScoreTitleVo> datasNoPage() {
//        Example example = new Example(ScoreTitle.class);
//        example.orderBy("viewOrder").desc();
//        List<ScoreTitle> scoreTitles = scoreTitleMapper.selectByExample(example);
        List<ScoreTitle> scoreTitles = scoreTitleMapper.selectList(Wrappers.<ScoreTitle>lambdaQuery().orderByDesc(ScoreTitle::getViewOrder));
        if (GeneralTool.isEmpty(scoreTitles)) {
            return new ArrayList<>();
        }
        List<ScoreTitleVo> scoreTitleVos = new ArrayList<>();
        //表ids
        Set<Long> fkTableIds = scoreTitles.stream().map(ScoreTitle::getId).collect(Collectors.toSet());
        //根据表ids获取批量的附件
        Map<Long, List<MediaAndAttachedVo>> mediaAndAttachedDtoByFkTableIds = mediaAndAttachedService.getMediaAndAttachedDtoByFkTableIds(TableEnum.SCORE_TITLE.key, fkTableIds);
        for (ScoreTitle scoreTitle : scoreTitles) {
            ScoreTitleVo scoreTitleVo = BeanCopyUtils.objClone(scoreTitle, ScoreTitleVo::new);
            scoreTitleVo.setMediaAndAttachedVoList(mediaAndAttachedDtoByFkTableIds.get(scoreTitle.getId()));
            scoreTitleVos.add(scoreTitleVo);
        }
        return scoreTitleVos;
    }

    /**
     * @Description: 新增接口
     * @Author: Jerry
     * @Date:17:30 2021/8/27
     */
    @Override
    public void add(ScoreTitleDto scoreTitleDto) {
        if (GeneralTool.isEmpty(scoreTitleDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ScoreTitle scoreTitle = BeanCopyUtils.objClone(scoreTitleDto, ScoreTitle::new);
        Integer maxViewOrder = scoreTitleMapper.getMaxViewOrder();
        scoreTitle.setViewOrder(maxViewOrder);
        utilService.updateUserInfoToEntity(scoreTitle);
        scoreTitleMapper.insertSelective(scoreTitle);
        //保存图片
        List<MediaAndAttachedDto> mediaAndAttachedDtoList = scoreTitleDto.getMediaAttachedVos();
        if (GeneralTool.isNotEmpty(mediaAndAttachedDtoList)) {
            for (MediaAndAttachedDto mediaAndAttachedDto : mediaAndAttachedDtoList) {
                mediaAndAttachedDto.setTypeKey(FileTypeEnum.EXAM_SCORE_TITLE_PIC.key);
                mediaAndAttachedDto.setFkTableId(scoreTitle.getId());
                mediaAndAttachedDto.setFkTableName(TableEnum.SCORE_TITLE.key);
                mediaAndAttachedService.addMediaAndAttached(mediaAndAttachedDto);
            }
        }
    }


    /**
     * @Description: 编辑接口
     * @Author: Jerry
     * @Date:17:30 2021/8/27
     */
    @Override
    public void update(ScoreTitleDto scoreTitleDto) {
        if (GeneralTool.isEmpty(scoreTitleDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ScoreTitle beforeScoreTitle = scoreTitleMapper.selectById(scoreTitleDto.getId());
        if (GeneralTool.isEmpty(beforeScoreTitle)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ScoreTitle scoreTitle = BeanCopyUtils.objClone(scoreTitleDto, ScoreTitle::new);
        utilService.updateUserInfoToEntity(scoreTitle);
        scoreTitleMapper.updateByPrimaryKey(scoreTitle);
        //删除图片
//        Example example = new Example(MediaAndAttached.class);
//        example.createCriteria().andEqualTo("fkTableName", TableEnum.SCORE_TITLE.key)
//                .andEqualTo("fkTableId", scoreTitle.getId()).andEqualTo("typeKey", FileTypeEnum.EXAM_SCORE_TITLE_PIC.key);
//        mediaAndAttachedMapper.deleteByExample(example);
        mediaAndAttachedMapper.delete(Wrappers.<ExamMediaAndAttached>lambdaQuery()
                .eq(ExamMediaAndAttached::getFkTableName, TableEnum.SCORE_TITLE.key)
                .eq(ExamMediaAndAttached::getFkTableId, scoreTitle.getId())
                .eq(ExamMediaAndAttached::getTypeKey, FileTypeEnum.EXAM_SCORE_TITLE_PIC.key));
        //保存图片
        List<MediaAndAttachedDto> mediaAndAttachedDtoList = scoreTitleDto.getMediaAttachedVos();
        if (GeneralTool.isNotEmpty(mediaAndAttachedDtoList)) {
            for (MediaAndAttachedDto mediaAndAttachedDto : mediaAndAttachedDtoList) {
                mediaAndAttachedDto.setTypeKey(FileTypeEnum.EXAM_SCORE_TITLE_PIC.key);
                mediaAndAttachedDto.setFkTableId(scoreTitle.getId());
                mediaAndAttachedDto.setFkTableName(TableEnum.SCORE_TITLE.key);
                mediaAndAttachedService.addMediaAndAttached(mediaAndAttachedDto);
            }
        }
    }


    /**
     * @Description: 删除接口
     * @Author: Jerry
     * @Date:17:31 2021/8/27
     */
    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //同时删除该表id下的所有媒体附件
        mediaAndAttachedService.deleteMediaAndAttached(TableEnum.SCORE_TITLE.key, id);
        scoreTitleMapper.deleteById(id);
    }


    /**
     * @Description: 详情接口
     * @Author: Jerry
     * @Date:17:31 2021/8/27
     */
    @Override
    public ScoreTitleVo detail(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ScoreTitle scoreTitle = scoreTitleMapper.selectById(id);
        if (GeneralTool.isEmpty(scoreTitle)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ScoreTitleVo scoreTitleVo = BeanCopyUtils.objClone(scoreTitle, ScoreTitleVo::new);
        Long fkComanyId = scoreTitleVo.getFkCompanyId();
        Result<String> result = permissionCenterClient.getCompanyNameById(fkComanyId);
        if (result.isSuccess() && result.getData() != null) {
            scoreTitleVo.setFkCompanyName(result.getData());
        }
        //查询图片
        MediaAndAttachedDto attachedVo = new MediaAndAttachedDto();
        attachedVo.setFkTableName(TableEnum.SCORE_TITLE.key);
        attachedVo.setFkTableId(id);
        List<MediaAndAttachedVo> mediaAndAttachedVos = mediaAndAttachedService.getMediaAndAttachedDto(attachedVo);
        scoreTitleVo.setMediaAndAttachedVoList(mediaAndAttachedVos);
        scoreTitleVo.setFkTableName(TableEnum.SCORE_TITLE.key);
        return scoreTitleVo;
    }


    /**
     * @Description: 上移下移
     * @Author: Jerry
     * @Date:18:32 2021/8/27
     */
    @Override
    public void movingOrder(List<ScoreTitleDto> scoreTitleDtos) {
        if (GeneralTool.isEmpty(scoreTitleDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ScoreTitle ro = BeanCopyUtils.objClone(scoreTitleDtos.get(0), ScoreTitle::new);
        Integer oneorder = ro.getViewOrder();
        ScoreTitle rt = BeanCopyUtils.objClone(scoreTitleDtos.get(1), ScoreTitle::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        scoreTitleMapper.updateByPrimaryKeySelective(ro);
        scoreTitleMapper.updateByPrimaryKeySelective(rt);
    }
}
