<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.votingcenter.dao.appvoting.UserVotingMapper">
  <insert id="insertSelective" parameterType="com.get.votingcenter.entity.UserVoting" keyProperty="id" useGeneratedKeys="true">
    insert into r_user_voting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkUserId != null">
        fk_user_id,
      </if>
      <if test="fkVotingItemOptionId != null">
        fk_voting_item_option_id,
      </if>
      <if test="voteCount != null">
        vote_count,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkUserId != null">
        #{fkUserId,jdbcType=BIGINT},
      </if>
      <if test="fkVotingItemOptionId != null">
        #{fkVotingItemOptionId,jdbcType=BIGINT},
      </if>
      <if test="voteCount != null">
        #{voteCount,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
    <select id="getVotingCountByOptionId" resultType="java.lang.Long">
      select SUM(vote_count) FROM r_user_voting where fk_voting_item_option_id = #{optionId}
    </select>
  <select id="generateUserAward" resultType="com.get.votingcenter.vo.UserVotingVo">
    select fk_user_id AS fkUserId from r_user_voting where fk_voting_item_option_id in
    <foreach collection="votingItemOptionIds" item="votingItemOptionId" index="index" open="(" separator="," close=")">
      #{votingItemOptionId}
    </foreach>
    <if test="fkUserIds != null">
      and fk_user_id not in
      <foreach collection="fkUserIds" item="fkUserId" index="index" open="(" separator="," close=")">
        #{fkUserId}
      </foreach>
    </if>
    group by fk_user_id
    order by RAND()
    limit #{generateCount}
  </select>
  <select id="getVotingResult" resultType="com.get.votingcenter.vo.UserVotingVo">
    select fk_user_id AS fkUserId from r_user_voting where fk_voting_item_option_id in
    <foreach collection="votingItemOptionIds" item="votingItemOptionId" index="index" open="(" separator="," close=")">
      #{votingItemOptionId}
    </foreach>
    group by fk_user_id
  </select>
</mapper>