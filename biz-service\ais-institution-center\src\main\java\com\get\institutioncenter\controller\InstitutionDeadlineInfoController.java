package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.institutioncenter.vo.InstitutionDeadlineInfoVo;
import com.get.institutioncenter.vo.InstitutionDeadlineInfoVo2;
import com.get.institutioncenter.service.IInstitutionDeadlineInfoService;
import com.get.institutioncenter.dto.InstitutionDeadlineInfoDto;
import com.get.institutioncenter.dto.InstitutionDeadlineInfoDto2;
import com.get.institutioncenter.dto.WeScholarshipAppDto;
import com.get.institutioncenter.dto.query.InstitutionDeadlineInfoQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/6 15:42
 */

@Api(tags = "申请截止信息管理")
@RestController
@RequestMapping("/institution/institutionDeadlineInfo")
public class InstitutionDeadlineInfoController {


    @Resource
    private IInstitutionDeadlineInfoService iInstitutionDeadlineInfoService;

    @ApiOperation("新增")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/申请截止信息管理/新增申请截止信息")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(InstitutionDeadlineInfoDto.Add.class) InstitutionDeadlineInfoDto institutionDeadlineInfoDto) {
        return SaveResponseBo.ok(iInstitutionDeadlineInfoService.addInstitutionDeadlineInfo(institutionDeadlineInfoDto));

    }


    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/申请截止信息管理/申请截止信息详情")
    @GetMapping("/{id}")
    public ResponseBo<InstitutionDeadlineInfoVo> detail(@PathVariable("id") Long id) {
        InstitutionDeadlineInfoVo data = iInstitutionDeadlineInfoService.findInstitutionDeadlineInfoById(id);
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", data);
        return responseBo;
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/申请截止信息管理/删除奖学金信息")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        iInstitutionDeadlineInfoService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/申请截止信息管理/更新申请截止信息")
    @PostMapping("update")
    public ResponseBo<InstitutionDeadlineInfoVo> update(@RequestBody @Validated(InstitutionDeadlineInfoDto.Update.class) InstitutionDeadlineInfoDto institutionDeadlineInfoDto) {
        return UpdateResponseBo.ok(iInstitutionDeadlineInfoService.updateInstitutionDeadlineInfo(institutionDeadlineInfoDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/申请截止信息管理/查询申请截止信息")
    @PostMapping("datas")
    public ResponseBo<InstitutionDeadlineInfoVo> datas(@RequestBody SearchBean<InstitutionDeadlineInfoQueryDto> page) {
        List<InstitutionDeadlineInfoVo> datas = iInstitutionDeadlineInfoService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "小程序申请截止详情", notes = "")
    @GetMapping("getWcInstitutionDeadlineInfoDtoDatas")
    public ResponseBo<InstitutionDeadlineInfoVo2> getWcInstitutionDeadlineInfoDtoDatas(@RequestParam("fkInstitutionId") Long fkInstitutionId) {
        return new ListResponseBo<>(iInstitutionDeadlineInfoService.getWcInstitutionDeadlineInfoDtoDatas(fkInstitutionId));
    }

    @ApiOperation(value = "申请截止信息优先匹配查询", notes = "")
    @VerifyLogin(IsVerify = false)
    @PostMapping("priorityMatchingQuery")
    public ResponseBo<InstitutionDeadlineInfoVo> priorityMatchingQuery(@RequestBody @Validated WeScholarshipAppDto weScholarshipAppDto){
        return new ListResponseBo<>(iInstitutionDeadlineInfoService.priorityMatchingQuery(weScholarshipAppDto));
    }

    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "小程序申请截止列表", notes = "")
    @PostMapping("getWcInstitutionDeadlineInfoList")
    public ResponseBo<InstitutionDeadlineInfoVo2> getWcInstitutionDeadlineInfoList(@RequestBody SearchBean<InstitutionDeadlineInfoDto2> page) {
        List<InstitutionDeadlineInfoVo2> datas = iInstitutionDeadlineInfoService.getWcInstitutionDeadlineInfoList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


}
