package com.get.aisplatformcenter.controller;


import com.get.aisplatformcenter.service.PlatformFileService;
import com.get.aisplatformcenterap.entity.MFilePlatformEntity;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

@Api(tags = "后台管理-文件上传下载")
@RestController
@RequestMapping("platform/appFileManager")
public class PlatformFileController {

    @Resource
    private PlatformFileService platformFileService;


    @ApiOperation(value = "上传附件-公开桶")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.LIST, description = "华通伙伴/高佣维护/列表查询")
    @PostMapping("uploadHtiPublicFile")
    public ResponseBo<List<MFilePlatformEntity>> uploadHtiPublicFile(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files){
        List<MFilePlatformEntity> result= platformFileService.uploadAppendix(files,true);
        return new ResponseBo<>(result);

    }
}
