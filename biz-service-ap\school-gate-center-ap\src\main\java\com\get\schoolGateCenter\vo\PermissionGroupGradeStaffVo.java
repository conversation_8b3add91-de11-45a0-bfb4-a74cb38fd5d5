package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PermissionGroupGradeStaffVo extends BaseVoEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 权限组别Id
     */
    @ApiModelProperty("权限组别Id")
    private Long fkPermissionGroupId;
    /**
     * 权限级别Id
     */
    @ApiModelProperty("权限级别Id")
    private Long fkPermissionGradeId;
    /**
     * 员工Id
     */
    @ApiModelProperty("员工Id")
    private Long fkStaffId;
    @ApiModelProperty("关键词")
    private String keyWord;
    @ApiModelProperty("修改权限的员工id数组")
    private List<Long> staffIds;

}
