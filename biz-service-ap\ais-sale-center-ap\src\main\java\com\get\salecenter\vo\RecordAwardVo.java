package com.get.salecenter.vo;

import com.get.salecenter.entity.ConventionAwardCode;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2021/9/18
 * @TIME: 14:40
 * @Description:
 **/
@Data
public class RecordAwardVo implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long award_id;
    private String picture;
    private String picture_url;
    private String reward_name;
    private Integer count;
    private String dionationor;
    private String grole;
    private Integer restcount;
    private String award_origin_zh;
    private List<ConventionAwardCodeVo> ticketsused;
    private List<ConventionAwardCode> codes;
}
