package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("m_convention_hotel")
public class ConventionHotel extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    @Column(name = "fk_convention_id")
    private Long fkConventionId;
    /**
     * 酒店名称
     */
    @ApiModelProperty(value = "酒店名称")
    @Column(name = "hotel")
    private String hotel;
    /**
     * 房型
     */
    @ApiModelProperty(value = "房型")
    @Column(name = "room_type")
    private String roomType;
    /**
     * 床位数
     */
    @ApiModelProperty(value = "床位数")
    @Column(name = "bed_count")
    private Integer bedCount;
    /**
     * 系统编号前序号（批量创建房间使用）
     */
    @ApiModelProperty(value = "系统编号前序号（批量创建房间使用）")
    @Column(name = "pre_num")
    private String preNum;
    /**
     * 价格币种编号
     */
    @ApiModelProperty(value = "价格币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;
    /**
     * 价格（参考）
     */
    @ApiModelProperty(value = "价格（参考）")
    @Column(name = "price")
    private BigDecimal price;

    @ApiModelProperty(value = "配额限制，没填没限制")
    @Column(name = "quota_limit")
    private Integer quotaLimit;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}