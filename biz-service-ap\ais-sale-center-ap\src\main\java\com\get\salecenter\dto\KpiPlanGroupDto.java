package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2024/4/18
 * @TIME: 14:36
 * @Description:
 **/
@Data
public class KpiPlanGroupDto extends BaseVoEntity {
    @ApiModelProperty(value = "KPI方案ID")
    @NotNull(message = "KPI方案ID不能为空", groups = {Add.class, Update.class})
    private Long fkKpiPlanId;

    @ApiModelProperty(value = "组别名称")
    @NotBlank(message = "组别名称不能为空", groups = {Add.class, Update.class})
    private String groupName;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

   

}
