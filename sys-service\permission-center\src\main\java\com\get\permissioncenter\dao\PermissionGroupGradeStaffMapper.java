package com.get.permissioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.permissioncenter.entity.PermissionGroupGradeStaff;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PermissionGroupGradeStaffMapper extends BaseMapper<PermissionGroupGradeStaff> {
    @Override
    int insert(PermissionGroupGradeStaff record);

    Integer CountByGroupAndGrade(@Param("groupId") Long groupId, @Param("gradeId") Long gradeId);

    void deleteByGroupAndGrade(@Param("groupId") Long groupId, @Param("gradeId") Long gradeId);

    List<Long> selectStaffByGroupAndGrade(@Param("groupId") Long groupId, @Param("gradeId") Long gradeId);

    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [staffId]
     * <AUTHOR>
     */
    Boolean isExistByStaffId(@Param("staffId") Long staffId);
}