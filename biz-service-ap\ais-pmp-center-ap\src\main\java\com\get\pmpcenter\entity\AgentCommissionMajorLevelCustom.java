package com.get.pmpcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@TableName("r_agent_commission_major_level_custom")
public class AgentCommissionMajorLevelCustom extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "代理佣金Id")
    private Long fkAgentCommissionId;

    @ApiModelProperty(value = "用户自定义课程等级Id")
    private Long fkMajorLevelCustomId;
}
