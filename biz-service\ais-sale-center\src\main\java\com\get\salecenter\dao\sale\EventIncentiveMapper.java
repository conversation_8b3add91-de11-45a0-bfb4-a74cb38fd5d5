package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.EventIncentiveListVo;
import com.get.salecenter.entity.EventIncentive;
import com.get.salecenter.dto.EventIncentiveDistributeDto;
import com.get.salecenter.dto.EventIncentiveListDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EventIncentiveMapper extends BaseMapper<EventIncentive>, GetMapper<EventIncentive> {

    /**
     * 列表
     *
     * @param iPage
     * @param eventIncentiveListDto
     * @return
     */
    List<EventIncentiveListVo> getEventIncentives(IPage<EventIncentiveListVo> iPage, @Param("eventIncentiveListDto") EventIncentiveListDto eventIncentiveListDto);

    /**
     * 分配活动列表
     *
     * @param iPage
     * @param eventIncentiveDistributeDto
     * @return
     */
    List<EventIncentiveListVo> getEventIncentiveList(IPage<EventIncentiveListVo> iPage, @Param("eventIncentiveDistributeDto") EventIncentiveDistributeDto eventIncentiveDistributeDto);

    /**
     * 折合费用的币种
     *
     * @param ids
     * @return
     */
    List<EventIncentiveListVo> getAmountReceivableCurrencyNumbyEventIncentiveIds(@Param("ids") List<Long> ids);
}