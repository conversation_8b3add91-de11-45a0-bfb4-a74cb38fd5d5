package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.StaffCommissionPolicyMapper;
import com.get.salecenter.vo.StaffCommissionPolicyVo;
import com.get.salecenter.entity.StaffCommissionPolicy;
import com.get.salecenter.entity.StaffCommissionStep;
import com.get.salecenter.entity.StudentProjectRole;
import com.get.salecenter.service.IStaffCommissionPolicyService;
import com.get.salecenter.service.IStaffCommissionStepService;
import com.get.salecenter.service.IStudentProjectRoleService;
import com.get.salecenter.dto.StaffCommissionPolicyDto;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2023/2/6 15:01
 * @verison: 1.0
 * @description:
 */
@Service
public class StaffCommissionPolicyServiceImpl extends GetServiceImpl<StaffCommissionPolicyMapper, StaffCommissionPolicy> implements IStaffCommissionPolicyService {


    @Resource
    private UtilService utilService;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Lazy
    @Resource
    private IStudentProjectRoleService studentProjectRoleService;
    @Lazy
    @Resource
    private IStaffCommissionStepService staffCommissionStepService;

    /**
     * 新增
     * @param staffCommissionPolicyDto
     * @return
     */
    @Override
    public Long add(StaffCommissionPolicyDto staffCommissionPolicyDto) {
        if (GeneralTool.isEmpty(staffCommissionPolicyDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (GeneralTool.isEmpty(staffCommissionPolicyDto.getFixedAmount())&&GeneralTool.isEmpty(staffCommissionPolicyDto.getCommissionRate())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        if (GeneralTool.isNotEmpty(staffCommissionPolicyDto.getFkStudentProjectRoleId())){
            StudentProjectRole studentProjectRole = studentProjectRoleService.getById(staffCommissionPolicyDto.getFkStudentProjectRoleId());
            staffCommissionPolicyDto.setFkStudentProjectRoleKey(studentProjectRole.getRoleKey());
        }

        if (GeneralTool.isNotEmpty(staffCommissionPolicyDto.getFkStaffCommissionStepKeyId())){
            StaffCommissionStep staffCommissionStep = staffCommissionStepService.getById(staffCommissionPolicyDto.getFkStaffCommissionStepKeyId());
            staffCommissionPolicyDto.setFkStaffCommissionStepKey(staffCommissionStep.getStepKey());
        }
        StaffCommissionPolicy staffCommissionPolicy = BeanCopyUtils.objClone(staffCommissionPolicyDto, StaffCommissionPolicy::new);
        Integer maxPriority = this.baseMapper.getMaxPriority();
        assert staffCommissionPolicy != null;
        staffCommissionPolicy.setPriority(maxPriority);
        utilService.setCreateInfo(staffCommissionPolicy);
        int i = this.baseMapper.insert(staffCommissionPolicy);
        if (i!=1){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return staffCommissionPolicy.getId();
    }


    /**
     * 删除
     * @param id
     */
    @Override
    public void delete(Long id) {
        boolean b = removeById(id);
        if (!b){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    /**
     * 修改
     * @param staffCommissionPolicyDto
     * @return
     */
    @Override
    public StaffCommissionPolicyVo updateStaffCommissionPolicy(StaffCommissionPolicyDto staffCommissionPolicyDto) {
        if (GeneralTool.isEmpty(staffCommissionPolicyDto.getFixedAmount())&&GeneralTool.isEmpty(staffCommissionPolicyDto.getCommissionRate())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        if (GeneralTool.isNotEmpty(staffCommissionPolicyDto.getFkStudentProjectRoleId())){
            StudentProjectRole studentProjectRole = studentProjectRoleService.getById(staffCommissionPolicyDto.getFkStudentProjectRoleId());
            staffCommissionPolicyDto.setFkStudentProjectRoleKey(studentProjectRole.getRoleKey());
        }

        if (GeneralTool.isNotEmpty(staffCommissionPolicyDto.getFkStaffCommissionStepKeyId())){
            StaffCommissionStep staffCommissionStep = staffCommissionStepService.getById(staffCommissionPolicyDto.getFkStaffCommissionStepKeyId());
            staffCommissionPolicyDto.setFkStaffCommissionStepKey(staffCommissionStep.getStepKey());
        }
        StaffCommissionPolicy staffCommissionPolicy = BeanCopyUtils.objClone(staffCommissionPolicyDto, StaffCommissionPolicy::new);
        utilService.setUpdateInfo(staffCommissionPolicy);
        int i = this.baseMapper.updateByIdWithNull(staffCommissionPolicy);
        if (i!=1){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        return BeanCopyUtils.objClone(staffCommissionPolicy, StaffCommissionPolicyVo::new);
    }

    /**
     * 列表
     * @param staffCommissionPolicyDto
     * @param page
     * @return
     */
    @Override
    public List<StaffCommissionPolicyVo> getStaffCommissionPolicyDtos(StaffCommissionPolicyDto staffCommissionPolicyDto, Page page) {

        //条件查询
        List<StaffCommissionPolicyVo> staffCommissionPolicyVos =  doGetStaffCommissionPolicyDtos(staffCommissionPolicyDto,page);
        //设置名称
        doSetStaffCommissionPolicyDtoListName(staffCommissionPolicyVos);

        return staffCommissionPolicyVos;
    }

    /**
     * 排序
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void movingOrder(Integer end,Integer start) {
        LambdaQueryWrapper<StaffCommissionPolicy> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (end>start){
            lambdaQueryWrapper.between(StaffCommissionPolicy::getPriority,start,end).orderByDesc(StaffCommissionPolicy::getPriority);
        }else {
            lambdaQueryWrapper.between(StaffCommissionPolicy::getPriority,end,start).orderByDesc(StaffCommissionPolicy::getPriority);

        }
        List<StaffCommissionPolicy> staffCommissionPolicies = list(lambdaQueryWrapper);

        //从下上移 列表倒序排序
        List<StaffCommissionPolicy> updateList = Lists.newArrayList();
        if (end>start){
            int finalEnd = end;
            List<StaffCommissionPolicy> sortedList = Lists.newArrayList();
            StaffCommissionPolicy policy = staffCommissionPolicies.get(staffCommissionPolicies.size() - 1);
            sortedList.add(policy);
            staffCommissionPolicies.remove(staffCommissionPolicies.size() - 1);
            sortedList.addAll(staffCommissionPolicies);
            for (StaffCommissionPolicy staffCommissionPolicy : sortedList) {
                staffCommissionPolicy.setPriority(finalEnd);
                finalEnd--;
            }
            updateList.addAll(sortedList);
        }else {
            int finalStart = start;
            List<StaffCommissionPolicy> sortedList = Lists.newArrayList();
            StaffCommissionPolicy policy = staffCommissionPolicies.get(0);
            staffCommissionPolicies.remove(0);
            sortedList.addAll(staffCommissionPolicies);
            sortedList.add(policy);
            for (StaffCommissionPolicy staffCommissionPolicy : sortedList) {
                staffCommissionPolicy.setPriority(finalStart);
                finalStart--;
            }
            updateList.addAll(sortedList);
        }

        if (GeneralTool.isNotEmpty(updateList)){
            boolean batch = updateBatchById(updateList);
            if (!batch){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
    }

    /**
     * 学校下拉
     * @return
     */
    @Override
    public List<BaseSelectEntity> getInstitutionsSelect(Long fkCompanyId) {
        List<BaseSelectEntity> baseSelectEntities = Lists.newArrayList();
        List<StaffCommissionPolicy> staffCommissionPolicies = list(Wrappers.lambdaQuery(StaffCommissionPolicy.class).eq(StaffCommissionPolicy::getFkCompanyId, fkCompanyId));
        if (GeneralTool.isEmpty(staffCommissionPolicies)){
            return Collections.emptyList();
        }
        List<Long> institutionIds = staffCommissionPolicies.stream().map(StaffCommissionPolicy::getFkInstitutionId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (GeneralTool.isEmpty(institutionIds)){
            return Collections.emptyList();
        }
        Map<Long, String> data = institutionCenterClient.getInstitutionNamesByIds(Sets.newHashSet(institutionIds)).getData();
        for (Long institutionId : institutionIds) {
            BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
            baseSelectEntity.setName(data.get(institutionId));
            baseSelectEntity.setId(institutionId);
            baseSelectEntities.add(baseSelectEntity);
        }
        return baseSelectEntities;
    }

    /**
     * 项目成员角色下拉
     * @param fkCompanyId
     * @return
     */
    @Override
    public List<BaseSelectEntity> getProjectRoleSelect(Long fkCompanyId) {
        if (GeneralTool.isEmpty(fkCompanyId)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
//        List<StaffCommissionPolicy> staffCommissionPolicies = list(Wrappers.lambdaQuery(StaffCommissionPolicy.class).select(StaffCommissionPolicy::getFkStudentProjectRoleKey).eq(StaffCommissionPolicy::getFkCompanyId, fkCompanyId));
//        if (GeneralTool.isEmpty(staffCommissionPolicies)){
//            return Collections.emptyList();
//        }
//        Set<String> keySet = staffCommissionPolicies.stream().map(StaffCommissionPolicy::getFkStudentProjectRoleKey).collect(Collectors.toSet());
        List<StudentProjectRole> studentProjectRoles = studentProjectRoleService.list(Wrappers.lambdaQuery(StudentProjectRole.class).eq(StudentProjectRole::getFkCompanyId,fkCompanyId).orderByDesc(StudentProjectRole::getViewOrder));
        return studentProjectRoles.stream().map(s->{
            BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
            baseSelectEntity.setId(s.getId());
            baseSelectEntity.setName(s.getRoleName());
            return baseSelectEntity;
        }).collect(Collectors.toList());
    }

    /**
     * 详情
     * @param id
     * @return
     */
    @Override
    public StaffCommissionPolicyVo findStaffCommissionPolicyById(Long id) {
        StaffCommissionPolicy staffCommissionPolicy = getById(id);
        StaffCommissionPolicyVo staffCommissionPolicyVo = BeanCopyUtils.objClone(staffCommissionPolicy, StaffCommissionPolicyVo::new);
        assert staffCommissionPolicyVo != null;
        if (GeneralTool.isNotEmpty(staffCommissionPolicy.getFixedAmount())){
            staffCommissionPolicyVo.setAmountType(0);
            staffCommissionPolicyVo.setAmountTypeName("固定金额");
        }else {
            staffCommissionPolicyVo.setAmountType(1);
            staffCommissionPolicyVo.setAmountTypeName("申请比例");
        }
        if (GeneralTool.isNotEmpty(staffCommissionPolicyVo.getFkStudentProjectRoleKey())){
            StudentProjectRole studentProjectRole = studentProjectRoleService.getOne(Wrappers.lambdaQuery(StudentProjectRole.class).eq(StudentProjectRole::getRoleKey, staffCommissionPolicyVo.getFkStudentProjectRoleKey()));
            staffCommissionPolicyVo.setFkStudentProjectRoleId(studentProjectRole.getId());
        }
        if (GeneralTool.isNotEmpty(staffCommissionPolicyVo.getFkStaffCommissionStepKey())){
            StaffCommissionStep staffCommissionStep = staffCommissionStepService.getOne(Wrappers.lambdaQuery(StaffCommissionStep.class).eq(StaffCommissionStep::getStepKey, staffCommissionPolicyVo.getFkStaffCommissionStepKey()));
            staffCommissionPolicyVo.setFkStaffCommissionStepKeyId(staffCommissionStep.getId());
        }
        return staffCommissionPolicyVo;
    }

    /**
     * 设置名称
     * @param staffCommissionPolicyVos
     */
    private void doSetStaffCommissionPolicyDtoListName(List<StaffCommissionPolicyVo> staffCommissionPolicyVos) {
        if (GeneralTool.isEmpty(staffCommissionPolicyVos)){
            return;
        }

        Set<Long> countryIds = staffCommissionPolicyVos.stream().map(StaffCommissionPolicyVo::getFkAreaCountryId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> countryNameMap = institutionCenterClient.getCountryFullNamesByIds(countryIds).getData();

        Set<Long> institutionIds = staffCommissionPolicyVos.stream().map(StaffCommissionPolicyVo::getFkInstitutionId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> institutionNameMap = institutionCenterClient.getInstitutionNamesByIds(institutionIds).getData();

        Set<Long> majorLevelIds = staffCommissionPolicyVos.stream().map(StaffCommissionPolicyVo::getFkMajorLevelId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> majorLeveNameMap = institutionCenterClient.getMajorLevelNamesByIds(majorLevelIds).getData();

        Set<Long> companyIds = staffCommissionPolicyVos.stream().map(StaffCommissionPolicyVo::getFkCompanyId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> companyNameMap = permissionCenterClient.getCompanyNamesByIds(companyIds).getData();

        for (StaffCommissionPolicyVo staffCommissionPolicyVo : staffCommissionPolicyVos) {
            if (GeneralTool.isNotEmpty(staffCommissionPolicyVo.getFkAreaCountryId())){
                staffCommissionPolicyVo.setFkAreaCountryName(countryNameMap.get(staffCommissionPolicyVo.getFkAreaCountryId()));
            }
            if (GeneralTool.isNotEmpty(staffCommissionPolicyVo.getFkInstitutionId())){
                staffCommissionPolicyVo.setFkInstitutionName(institutionNameMap.get(staffCommissionPolicyVo.getFkInstitutionId()));
            }
            if (GeneralTool.isNotEmpty(staffCommissionPolicyVo.getFkMajorLevelId())){
                staffCommissionPolicyVo.setFkMajorLevelName(majorLeveNameMap.get(staffCommissionPolicyVo.getFkMajorLevelId()));
            }
            if (GeneralTool.isNotEmpty(staffCommissionPolicyVo.getFkCompanyId())){
                staffCommissionPolicyVo.setFkCompanyName(companyNameMap.get(staffCommissionPolicyVo.getFkCompanyId()));
            }

            if (GeneralTool.isNotEmpty(staffCommissionPolicyVo.getFixedAmount())){
                staffCommissionPolicyVo.setAmountType(0);
                staffCommissionPolicyVo.setAmountTypeName(" 固定金额");
                staffCommissionPolicyVo.setCommissionAmount(staffCommissionPolicyVo.getFixedAmount());
            }else if (GeneralTool.isNotEmpty(staffCommissionPolicyVo.getCommissionRate())){
                staffCommissionPolicyVo.setAmountType(1);
                staffCommissionPolicyVo.setAmountTypeName("申请费比例");
                staffCommissionPolicyVo.setCommissionAmount(staffCommissionPolicyVo.getCommissionRate());
            }
        }

    }

    /**
     * 条件查询
     * @param staffCommissionPolicyDto
     * @param page
     * @return
     */
    private List<StaffCommissionPolicyVo> doGetStaffCommissionPolicyDtos(StaffCommissionPolicyDto staffCommissionPolicyDto, Page page) {
        if (GeneralTool.isEmpty(staffCommissionPolicyDto.getFkCompanyId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        IPage<StaffCommissionPolicy> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<StaffCommissionPolicyVo> staffCommissionPolicies = this.baseMapper.getStaffCommissionPolicies(iPage, staffCommissionPolicyDto);
        page.setAll((int) iPage.getTotal());
        if (GeneralTool.isEmpty(staffCommissionPolicies)){
            return Collections.emptyList();
        }
        return staffCommissionPolicies;
    }


}
