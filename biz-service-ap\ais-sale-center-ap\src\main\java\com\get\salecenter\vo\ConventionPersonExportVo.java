package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: Hardy
 * @create: 2021/7/6 16:28
 * @verison: 1.0
 * @description:
 */
@Data
public class ConventionPersonExportVo {
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 参展人员类型（枚举定义）：0校方/1代理/2嘉宾/3员工/4工作人员
     */
    @ApiModelProperty(value = "参展人员类型")
    private String typeName;

    /**
     * 展位名称
     */
    @ApiModelProperty(value = "展位名称")
    private String boothName;

    /**
     * 参会编号
     */
    @ApiModelProperty(value = "参会编号")
    private String num;

    /**
     * 是否vip
     */
    @ApiModelProperty(value = "是否VIP")
    private String vip;

    /**
     * 参加人姓名
     */
    @ApiModelProperty(value = "参加人姓名")
    private String name;

    /**
     * 参加人姓名（中文）
     */
    @ApiModelProperty(value = "参加人姓名（中文）")
    private String nameChn;

    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别")
    private String genderName;

    /**
     * 职位
     */
    @ApiModelProperty(value = "职位")
    private String title;

    /**
     * 公司
     */
    @ApiModelProperty(value = "公司")
    private String company;

    /**
     * 参加人电邮
     */
    @ApiModelProperty(value = "参加人电邮")
    private String email;

    /**
     * 参加人电话
     */
    @ApiModelProperty(value = "参加人电话")
    private String tel;

    /**
     * 护照
     */
    @ApiModelProperty(value = "护照")
    private String passportNum;

    /**
     * 身份证
     */
    @ApiModelProperty(value = "身份证")
    private String idCardNum;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "支付状态")
    private String payType;

    /**
     * 到店日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "到店日期")
    private Date checkInTime;

    /**
     * 离店日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "离店日期")
    private Date checkOutTime;

    /**
     * 预计到达时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "预计到达时间")
    private Date arrivalTime;

    /**
     * 到达交通类型（枚举定义选择）：0飞机/1高铁/2汽车
     */
    @ApiModelProperty(value = "到达交通类型")
    private String arrivalTransportation;

    /**
     * 到达交通编号：航班号/高铁班次/汽车班次
     */
    @ApiModelProperty(value = "到达交通编号")
    private String arrivalTransportationCode;

    /**
     * 预计离开时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "预计离开时间")
    private Date leaveTime;

    /**
     * 离开交通类型（枚举定义选择）：0飞机/1高铁/2汽车
     */
    @ApiModelProperty(value = "离开交通类型")
    private String leaveTransportation;

    /**
     * 离开交通编号：航班号/高铁班次/汽车班次
     */
    @ApiModelProperty(value = "离开交通编号")
    private String leaveTransportationCode;

    /**
     * 酒店住宿费用类型：0公费/1自费
     */
    @ApiModelProperty(value = "酒店住宿费用类型")
    private String hotelFeeTypeName;

    /**
     * 货币类型
     */
    @ApiModelProperty(value = "住宿费用币种")
    private String currencyTypeName;

    /**
     * 住宿费用单价（每晚）
     */
    @ApiModelProperty(value = "住宿费每晚单价")
    private BigDecimal hotelExpense;

    /**
     * 房型
     */
    @ApiModelProperty(value = "意向房型")
    private String intentionRoomType;

    /**
     * 房间类型名称
     */
    @ApiModelProperty(value = "分配房型")
    private String roomTypeName;

    @ApiModelProperty(value = "安排餐桌号")
    private String conventionDinnerTable;

    @ApiModelProperty(value = "安排培训桌号")
    private String conventionTrainingTable;

    @ApiModelProperty(value = "跟进BD")
    private String bdName;

    /**
     * 参加流程
     */
    @ApiModelProperty(value = "参加流程")
    private String joinProcedure;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

}
