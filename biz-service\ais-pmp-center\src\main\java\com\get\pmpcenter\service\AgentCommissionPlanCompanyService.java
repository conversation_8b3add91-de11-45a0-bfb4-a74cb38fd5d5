package com.get.pmpcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.pmpcenter.entity.AgentCommissionPlanCompany;
import com.get.pmpcenter.vo.common.CompanyVo;
import com.get.pmpcenter.vo.institution.ProviderVo;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:17
 * @Version 1.0
 */
public interface AgentCommissionPlanCompanyService extends IService<AgentCommissionPlanCompany> {

    /**
     * 分公司列表
     * @return
     */
    List<CompanyVo> companyList();

    /**
     * 保存代理佣金计划公司
     * @param agentCommissionPlanId
     * @param companyId
     */
    void saveAgentCommissionPlanCompany(Long agentCommissionPlanId, Long companyId);

    /**
     * 供应商列表
     * @param companyId
     * @return
     */
    List<ProviderVo> agentProviderList(Long companyId);
}

