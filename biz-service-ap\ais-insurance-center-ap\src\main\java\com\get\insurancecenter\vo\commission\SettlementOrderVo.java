package com.get.insurancecenter.vo.commission;

import com.get.insurancecenter.entity.InsuranceOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author:Oliver
 * @Date: 2025/5/27
 * @Version 1.0
 * @apiNote:佣金结算订单列表
 */
@Data
public class SettlementOrderVo extends InsuranceOrder {

    @ApiModelProperty(value = "费率%(代理)")
    private BigDecimal commissionRate;

    @ApiModelProperty(value = "佣金金额(代理)")
    private BigDecimal commissionAmount;

    @ApiModelProperty(value = "结算状态1:可结算;2结算中;4已结算")
    private Integer settlementStatus;

    @ApiModelProperty(value = "应付计划Id")
    private Long payablePlanId;

    @ApiModelProperty(value = "代理提交结算批次编号")
    private String numOptBatch;

    @ApiModelProperty(value = "订单结算ID")
    private Long orderSettlementId;
}
