package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.institutioncenter.dao.InstitutionProviderAccountMapper;
import com.get.institutioncenter.vo.InstitutionProviderAccountVo;
import com.get.institutioncenter.entity.InstitutionProviderAccount;
import com.get.institutioncenter.service.InstitutionProviderAccountService;
import com.get.institutioncenter.dto.InstitutionProviderAccountDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class InstitutionProviderAccountServiceImpl extends BaseServiceImpl<InstitutionProviderAccountMapper,InstitutionProviderAccount> implements InstitutionProviderAccountService {

    @Resource
    private InstitutionProviderAccountMapper institutionProviderAccountMapper;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private UtilService<Object> utilService;
    /**
     * Author Cream
     * Description : //获取学校提供商账户列表
     * Date 2023/2/13 15:15
     * Params:
     * Return
     */
    @Override
    public ResponseBo<InstitutionProviderAccountVo> getProviderAccountList(InstitutionProviderAccountDto providerAccountVo, Page page) {
        if (GeneralTool.isNull(providerAccountVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        IPage<InstitutionProviderAccountDto> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<InstitutionProviderAccountVo> providerAccountDtos = institutionProviderAccountMapper.getProviderAccountList(iPage,providerAccountVo);
        page.setAll((int) iPage.getTotal());
        if (providerAccountDtos.isEmpty()) {
            return new ListResponseBo<>();
        }
        Set<String> nums = providerAccountDtos.stream().map(InstitutionProviderAccountVo::getFkCurrencyTypeNum).collect(Collectors.toSet());
        Map<String, String> numsMap = financeCenterClient.getCurrencyNamesByNums(nums).getData();
        for (InstitutionProviderAccountVo accountDto : providerAccountDtos) {
            accountDto.setAccountCardTypeName(ProjectExtraEnum.getInitialValueByKey(accountDto.getAccountCardType(),ProjectExtraEnum.cardType));
            accountDto.setCurrencyTypeName(numsMap.get(accountDto.getFkCurrencyTypeNum()));
        }
        return new ListResponseBo<>(providerAccountDtos, BeanCopyUtils.objClone(page, Page::new));
    }
    /**
     * Author Cream
     * Description : //详情
     * Date 2023/2/13 16:31
     * Params:
     * Return
     */
    @Override
    public InstitutionProviderAccountVo findInfoById(Long id) {
        if (GeneralTool.isNull(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionProviderAccount account = institutionProviderAccountMapper.selectById(id);
        InstitutionProviderAccountVo institutionProviderAccountVo = new InstitutionProviderAccountVo();
        BeanUtils.copyProperties(account, institutionProviderAccountVo);
        institutionProviderAccountVo.setAccountCardTypeName(ProjectExtraEnum.getInitialValueByKey(account.getAccountCardType(),ProjectExtraEnum.cardType));
        String currencyName = financeCenterClient.getCurrencyNameByNum(account.getFkCurrencyTypeNum()).getData();
        institutionProviderAccountVo.setCurrencyTypeName(currencyName);
        return institutionProviderAccountVo;
    }

    /**
     * Author Cream
     * Description : //快捷设置首选合同
     * Date 2023/2/13 16:47
     * Params:
     * Return
     */
    @Override
    public SaveResponseBo quickFirstContractAccount(Long providerId, Long accountId) {
        if (GeneralTool.isNull(providerId) || GeneralTool.isNull(accountId) ) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<InstitutionProviderAccount> providerAccounts = institutionProviderAccountMapper
                .selectList(Wrappers.<InstitutionProviderAccount>lambdaQuery().eq(InstitutionProviderAccount::getFkInstitutionProviderId, providerId));
        if (GeneralTool.isNotEmpty(providerAccounts)) {
            List<InstitutionProviderAccount> updateList = new ArrayList<>();
            for (InstitutionProviderAccount providerAccount : providerAccounts) {
                if (providerAccount.getIsDefault()!=null && providerAccount.getIsDefault()) {
                    providerAccount.setIsDefault(false);
                    updateList.add(providerAccount);
                }
                if (accountId.equals(providerAccount.getId())) {
                    providerAccount.setIsDefault(true);
                    updateList.add(providerAccount);
                }
            }
            if (GeneralTool.isNotEmpty(updateList)) {
                updateBatchById(updateList);
            }
        }
        return new SaveResponseBo(accountId);
    }

    /**
     * Author Cream
     * Description : //快速激活或屏蔽合同账户
     * Date 2023/2/13 16:47
     * Params:
     * Return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SaveResponseBo quickActivationOrMask(Long providerId, Long accountId, Boolean status) {
        if (GeneralTool.isNull(providerId) || GeneralTool.isNull(accountId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionProviderAccount providerAccount = institutionProviderAccountMapper.selectById(accountId);
        if (providerAccount!=null) {
            List<InstitutionProviderAccount> institutionProviderAccounts = institutionProviderAccountMapper.selectList(Wrappers.<InstitutionProviderAccount>lambdaQuery().eq(InstitutionProviderAccount::getFkInstitutionProviderId, providerId)
                    .eq(InstitutionProviderAccount::getFkCurrencyTypeNum, providerAccount.getFkCurrencyTypeNum()));
            if (GeneralTool.isNotEmpty(institutionProviderAccounts)) {
                List<InstitutionProviderAccount> updateList = new ArrayList<>();
                if (status) {
                    for (InstitutionProviderAccount account : institutionProviderAccounts) {
                        if (account.getIsActive()!=null && account.getIsActive()) {
                            account.setIsActive(false);
                            updateList.add(account);
                        }
                        if (accountId.equals(account.getId())) {
                            account.setIsActive(true);
                            updateList.add(account);
                        }
                    }
                    if (GeneralTool.isNotEmpty(updateList)) {
                        updateBatchById(updateList);
                    }
                }else {
                    providerAccount.setIsActive(false);
                    institutionProviderAccountMapper.updateById(providerAccount);
                }
            }
        }
        return SaveResponseBo.ok(accountId);
    }

    /**
     * Author Cream
     * Description : //更新
     * Date 2023/2/14 14:34
     * Params:
     * Return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public InstitutionProviderAccountVo update(InstitutionProviderAccountDto providerAccountVo) {
        if (GeneralTool.isNull(providerAccountVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        InstitutionProviderAccount institutionProviderAccount = institutionProviderAccountMapper.selectById(providerAccountVo.getId());
        if (Objects.isNull(institutionProviderAccount)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        BeanUtils.copyProperties(providerAccountVo,institutionProviderAccount);
        utilService.setUpdateInfo(institutionProviderAccount);
        institutionProviderAccountMapper.updateById(institutionProviderAccount);
        return findInfoById(providerAccountVo.getId());
    }

    /**
     * Author Cream
     * Description : //新增
     * Date 2023/2/14 14:34
     * Params:
     * Return
     */
    @Override
    public SaveResponseBo add(InstitutionProviderAccountDto providerAccountVo) {
        if (GeneralTool.isNull(providerAccountVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        InstitutionProviderAccount institutionProviderAccount = new InstitutionProviderAccount();
        BeanUtils.copyProperties(providerAccountVo,institutionProviderAccount);
        utilService.setCreateInfo(institutionProviderAccount);
        institutionProviderAccountMapper.insert(institutionProviderAccount);
        return SaveResponseBo.ok(institutionProviderAccount.getId());
    }

    /**
     * Author Cream
     * Description : //删除
     * Date 2023/2/14 14:41
     * Params:
     * Return
     */
    @Override
    public SaveResponseBo delete(Long id) {
        if (GeneralTool.isNull(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionProviderAccount institutionProviderAccount = institutionProviderAccountMapper.selectById(id);
        if (Objects.isNull(institutionProviderAccount)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        institutionProviderAccountMapper.deleteById(id);
        return SaveResponseBo.ok(id);
    }


    /**
     * Author Cream
     * Description : //代理合同账户列表账户重复提示
     * Date 2023/2/15 15:48
     * Params:
     * Return
     */
    @Override
    public ResponseBo<String> getContractAccountExist(Long id, Long providerId, String bankAccount, String bankAccountNum) {
        List<InstitutionProviderAccountVo> providerAccountDtoList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        if (GeneralTool.isNotEmpty(bankAccount)) {
            providerAccountDtoList = institutionProviderAccountMapper.getContractAccountExist(providerId, bankAccount, null);
        }
        List<InstitutionProviderAccountVo> p2 = new ArrayList<>();
        if (GeneralTool.isNotEmpty(bankAccountNum)) {
            p2 = institutionProviderAccountMapper.getContractAccountExist(providerId, null, bankAccountNum);
        }
        if (GeneralTool.isNotEmpty(providerAccountDtoList)) {
            if (GeneralTool.isNotEmpty(id)) {
                providerAccountDtoList.stream().filter(d -> !d.getId().equals(id)).forEach(dd -> sb.append(LocaleMessageUtils.getMessage("AGENT")).append("：")
                        .append(dd.getProviderName()).append("，").append(LocaleMessageUtils.getMessage("AGENT_ACCOUNT_NAME")).append("：").append(dd.getBankAccount())
                        .append("，").append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！<br/>"));

            } else {
                providerAccountDtoList.forEach(dd -> sb.append(LocaleMessageUtils.getMessage("AGENT")).append("：")
                        .append(dd.getProviderName()).append("，").append(LocaleMessageUtils.getMessage("AGENT_ACCOUNT_NAME")).append("：").append(dd.getBankAccount())
                        .append("，").append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！<br/>"));
            }
        }
        if (GeneralTool.isNotEmpty(p2)) {
            if (GeneralTool.isNotEmpty(id)) {
                p2.stream().filter(d -> !d.getId().equals(id)).forEach(dd -> sb.append(LocaleMessageUtils.getMessage("AGENT")).append("：")
                        .append(dd.getProviderName()).append("，").append(LocaleMessageUtils.getMessage("AGENT_ACCOUNT_NO")).append("：").append(dd.getBankAccountNum())
                        .append("，").append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！<br/>"));
            } else {
                p2.forEach(dd -> sb.append(LocaleMessageUtils.getMessage("AGENT")).append("：")
                        .append(dd.getProviderName()).append("，").append(LocaleMessageUtils.getMessage("AGENT_ACCOUNT_NO")).append("：").append(dd.getBankAccountNum())
                        .append("，").append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！<br/>"));
            }
        }
        return new ResponseBo<>(sb.toString());
    }

    /**
     * Author Cream
     * Description : //提供商银行账户下拉框
     * Date 2023/2/27 14:42
     * Params:
     * Return
     */
    @Override
    public List<BaseSelectEntity> getInstitutionProviderAccountList(Long fkTargetId) {
        if (Objects.isNull(fkTargetId)) {
            return Collections.emptyList();
        }
        return institutionProviderAccountMapper.getInstitutionProviderAccountList(fkTargetId);
    }

    /**
     * Author Cream
     * Description : //获取银行账户
     * Date 2023/2/27 17:44
     * Params:
     * Return
     */
    @Override
    public String getInstitutionProviderAccountById(Long fkBankAccountId) {
        if (Objects.isNull(fkBankAccountId)) {
            return null;
        }
        return institutionProviderAccountMapper.getInstitutionProviderAccountById(fkBankAccountId);
    }
}
