package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@Data
public class KpiAgentRankVo implements Serializable {

    @ApiModelProperty(value = "代理Id")
    private Long fkAgentId;

    @ApiModelProperty(value = "代理名称")
    private String fkAgentName;

    @ApiModelProperty(value = "BD名称")
    private String bdName;

    /**
     * BD绑定的大区
     */
    @ApiModelProperty("BD绑定的大区")
    private List<AreaRegionVo> areaRegionDtos;

    @ApiModelProperty(value = "国家名称")
    private String countryName;

    @ApiModelProperty(value = "州省名称")
    private String stateName;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "学生数")
    private Integer studentCount;

    @ApiModelProperty(value = "申请计划id列表，用于代理排名中学生数跳转到申请计划汇总")
    private Set<Long> studentCountOfferItemIds;

}
