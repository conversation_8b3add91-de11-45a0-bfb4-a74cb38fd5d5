package com.get.schoolGateCenter.entity;

import com.get.schoolGateCenter.dto.SchoolGateStaffDto;

import javax.servlet.http.Cookie;

public class StaffContext {

    private static ThreadLocal<SchoolGateStaffDto> staffdtos = new ThreadLocal<SchoolGateStaffDto>();
    private static ThreadLocal<Cookie[]> cookies = new ThreadLocal<Cookie[]>();
//    private static ThreadLocal<Integer> translationConfig = new ThreadLocal<Integer>();
    public static String KEY_USERINFO_IN_HTTP_HEADER = "X-AUTO-FP-USERINFO";

    public StaffContext() {
    }

    public static SchoolGateStaffDto getStaff(){
        return (SchoolGateStaffDto)staffdtos.get();
    }

    public static void setStaff(SchoolGateStaffDto staff){
        staffdtos.set(staff);
    }

    public static Cookie[] getCookies(){
        return (<PERSON><PERSON>[])cookies.get();
    }

    public static void setCookies(<PERSON><PERSON>[] cookie){
        cookies.set(cookie);
    }

//    public static Integer getTranslationConfig(){
//        return translationConfig.get();
//    }
//
//    public static void setTranslationConfig(Integer translation){
//        translationConfig.set(translation);
//    }
    //防止内存泄漏
    public static void staffRemove(){
        staffdtos.remove();
    }
    public static void cookiesRemove(){
        cookies.remove();
    }
}
