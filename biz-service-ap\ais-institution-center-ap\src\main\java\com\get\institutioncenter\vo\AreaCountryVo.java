package com.get.institutioncenter.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/21
 * @TIME: 16:50
 * @Description:
 **/
@Data
@ApiModel(value = "国家返回类")
//@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AreaCountryVo extends BaseEntity {

    /**
     * 国家对应的州省
     */
    @ApiModelProperty(value = "国家对应的州省")
    List<AreaStateVo> areaStateDtos;
    /**
     * 媒体附件-国旗
     */
    @ApiModelProperty(value = "媒体附件-国旗")
    List<MediaAndAttachedVo> nationalFlags;
    /**
     * 媒体附件-国徽
     */
    @ApiModelProperty(value = "媒体附件-国徽")
    List<MediaAndAttachedVo> nationalEmblems;
    /**
     * 国家下对应的新闻列表
     */
    @ApiModelProperty(value = "国家下对应的新闻列表")
    List<NewsVo> newsDtos;
    /**
     * 表示国家类型
     */
    @ApiModelProperty(value = "表示国家类型")
    private String type;
    /**
     * 全称
     */
//    @ApiModelProperty(value = "全称")
    @ApiModelProperty(value = "英文全称")
    private String fullName;
    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String fkCurrencyTypeName;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;
    /**
     * 公开对象名称
     */
    @ApiModelProperty(value = "公开对象名称")
    private String publicLevelName;

    /**
     * 业务区域中文名
     */
    @ApiModelProperty(value = "业务区域中文名")
    private String businessAreaName;

    @ApiModelProperty("区号的全称")
    private String areaCodeValue;

    //================实体类AreaCountry========================
    private static final long serialVersionUID = 1L;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;
    /**
     * 国家编号
     */
    @ApiModelProperty(value = "国家编号")
    @Column(name = "num")
    private String num;
    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    @Column(name = "name")
    private String name;
    /**
     * 国家中文名称
     */
    @ApiModelProperty(value = "国家中文名称")
    @Column(name = "name_chn")
    private String nameChn;
    /**
     * 区号
     */
    @ApiModelProperty(value = "区号")
    @Column(name = "area_code")
    private String areaCode;
    /**
     * 首都
     */
    @ApiModelProperty(value = "首都")
    @Column(name = "capital")
    private String capital;
    /**
     * 人口
     */
    @ApiModelProperty(value = "人口")
    @Column(name = "population")
    private String population;
    /**
     * 面积
     */
    @ApiModelProperty(value = "面积")
    @Column(name = "area")
    private String area;
    /**
     * 语言
     */
    @ApiModelProperty(value = "语言")
    @Column(name = "language")
    private String language;
    /**
     * 宗教
     */
    @ApiModelProperty(value = "宗教")
    @Column(name = "religion")
    private String religion;
    /**
     * 时差
     */
    @ApiModelProperty(value = "时差")
    @Column(name = "time_difference")
    private String timeDifference;
    /**
     * 现任国家元首
     */
    @ApiModelProperty(value = "现任国家元首")
    @Column(name = "president")
    private String president;
    /**
     * 国旗意义
     */
    @ApiModelProperty(value = "国旗意义")
    @Column(name = "flag_meaning")
    private String flagMeaning;
    /**
     * 国徽意义
     */
    @ApiModelProperty(value = "国徽意义")
    @Column(name = "emblem_meaning")
    private String emblemMeaning;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @Column(name = "public_level")
    private String publicLevel;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
    /**
     * 业务区域，枚举（US/UK/ANZ/CAN/EUASIA）
     */
    @ApiModelProperty(value = "业务区域，枚举（US/UK/ANZ/CAN/EUASIA）")
    @Column(name = "business_area_key")
    private String businessAreaKey;
    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    @Column(name = "id_gea")
    private String idGea;
    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    @Column(name = "id_iae")
    private String idIae;

    @ApiModelProperty(value = "地理大区Ids，支持多选")
    private String fkAreaRegionIds;

    @ApiModelProperty(value = "地理大区名称")
    private String fkAreaRegionNames;
}
