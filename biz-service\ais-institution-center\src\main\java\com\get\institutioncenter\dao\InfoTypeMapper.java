package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.institutioncenter.entity.InfoType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/5 10:31
 * @verison: 1.0
 * @description: 资讯类型管理mapper
 */
@Mapper
public interface
InfoTypeMapper extends BaseMapper<InfoType> {
    /**
     * 添加
     *
     * @param record
     * @return
     */
    @Override
    int insert(InfoType record);

    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(InfoType record);

    /**
     * 根据资讯类型id查找资讯名称
     *
     * @param id
     * @return
     */
    String getInfoTypeNameById(Long id);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description :资讯类型下拉框
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getInfoTypeList(@Param("translationFlag") boolean translationFlag);

    /**
     * @return java.lang.Integer
     * @Description :获取最大排序值
     * @Param []
     * <AUTHOR>
     */
    Integer getMaxViewOrder();

}