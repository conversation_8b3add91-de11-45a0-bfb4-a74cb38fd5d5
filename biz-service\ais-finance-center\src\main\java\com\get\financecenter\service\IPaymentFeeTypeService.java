package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.financecenter.dto.PaymentFeeTypeDto;
import com.get.financecenter.entity.PaymentFeeType;
import com.get.financecenter.vo.PaymentFeeTypeVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/4/6 14:50
 */
public interface IPaymentFeeTypeService extends BaseService<PaymentFeeType> {


    PaymentFeeTypeVo findPayTypeById(Long id);


    void batchAdd(List<PaymentFeeTypeDto> paymentFeeTypeDtos);


    void delete(Long id);


    PaymentFeeTypeVo updatePayFlowType(PaymentFeeTypeDto paymentFeeTypeDto);


    List<PaymentFeeTypeVo> getPayTypes(PaymentFeeTypeDto paymentFeeTypeDto, Page page);


    void sort(List<PaymentFeeTypeDto> paymentFeeTypeDtos);

    List<PaymentFeeTypeVo> getPayTypeList();

    List<Map<String, Object>> findTargetType();

    /**
     * @return java.lang.String
     * @Description: 根据id 查询名称
     * @Param [payTypeId]
     * <AUTHOR>
     */
    String getTypeNameById(Long payTypeId);


    /**
     * 根据ids 查询名称map
     *
     * @param payTypeIds
     * @return
     */
    Map<Long, String> getTypeNameByIds(Set<Long> payTypeIds);

    List<PaymentFeeTypeVo> getRelationTargetKey();

    void movingOrder(Integer start, Integer end);
}
