package com.get.permissioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.permissioncenter.vo.StaffFollowerVo;
import com.get.permissioncenter.vo.StaffSuperiorVo;
import com.get.permissioncenter.vo.tree.StaffSuperiorTreeVo;
import com.get.permissioncenter.entity.StaffSuperior;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Mapper
public interface StaffSuperiorMapper extends BaseMapper<StaffSuperior> {

    /**
     * 获取业务上司
     *
     * @param staffId
     * @return
     */
    List<StaffSuperiorVo> getStaffSuperior(@Param("staffId") Long staffId);

    /**
     * 获取业务下属
     *
     * @param staffId
     * @return
     */
    List<StaffFollowerVo> getStaffFollower(@Param("staffId") Long staffId, @Param("keyWord") String keyWord);

    /**
     * 员工上司树
     *
     * @param staffId
     * @return
     */
    List<StaffSuperiorTreeVo> getStaffSuperiorTree(@Param("staffId") Long staffId, @Param("companyId") Long companyId);


    /**
     * 上司的上级公司Id
     *
     * @param staffId
     * @return
     */
    List<Long> getSuperiorParentCompanyIds(@Param("staffId") Long staffId);


    /**
     * @return java.lang.Boolean
     * @Description: 查看是否有下属
     * @Param [staffId]
     * <AUTHOR>
     */
    Boolean isExistByStaffId(@Param("staffId") Long staffId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 获取下属ids
     * @Param [staffId]
     * <AUTHOR>
     */
    List<Long> getStaffFollowerId(Long staffId);

    /**
     * 查询指定集合所有的业务上司id
     *
     * @param followerIds
     * @return
     */
    List<Long> getAllFollowerIds(@Param("followerIds") String followerIds);

    /**
     * 当前登录用户业务下属下拉
     *
     * @param staffId
     * @return
     */
    List<BaseSelectEntity> getStaffFollowerSelect(@Param("staffId") Long staffId, @Param("companyId") Long companyId);

    /**
     * 获取业务上司
     *
     * @param staffIds
     * @return
     */
    List<StaffSuperiorVo> getStaffSuperiorByIds(@RequestBody Set<Long> staffIds);

    /**
     * 获取一层下属id
     * @param staffId
     * @return
     */
    List<Long> getBusinessSubordinatesIds(Long staffId);
}