package com.get.salecenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.salecenter.vo.EventPlanThemeOfflineItemVo;
import com.get.salecenter.entity.EventPlanThemeOfflineItem;
import com.get.salecenter.dto.EventPlanThemeOfflineItemDto;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
public interface EventPlanThemeOfflineItemService extends BaseService<EventPlanThemeOfflineItem> {
    /**
     * 获取最大排序
     * <AUTHOR>
     * @DateTime 2023/12/18 10:18
     */
    Integer getMaxViewOrder(Long fkEventPlanThemeOfflineId);

    /**
     * 列表数据
     * <AUTHOR>
     * @DateTime 2023/12/18 10:18
     */
    List<EventPlanThemeOfflineItemVo> getEventPlanThemeOfflineItems(Long fkEventPlanId , Long fkEventPlanThemeOfflineId);

    /**
     * 激活
     * <AUTHOR>
     * @DateTime 2023/12/25 15:56
     */
    void activate(EventPlanThemeOfflineItemDto offlineItemVo);

    /**
     * 拖拽
     * <AUTHOR>
     * @DateTime 2023/12/14 11:02
     */
    void movingOrder(Long fkEventPlanThemeOfflineId,Integer start,Integer end);

}
