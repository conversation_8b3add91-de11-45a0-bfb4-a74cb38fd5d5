package com.get.remindercenter.component;

import com.get.core.mybatis.base.UtilService;
import com.get.remindercenter.dao.EmailSenderQueueMapper;
import com.get.remindercenter.dto.EmailParamsDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import org.apache.poi.ss.formula.functions.T;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 邮件发送抽象类
 */
public abstract class EmailAbstractHelper {

    @Resource
    private EmailSenderQueueMapper emailSenderQueueMapper;


    public abstract void sendMail(EmailSenderQueue emailSenderQueue);

    public abstract Object assembleEmailData (EmailSenderQueue emailSenderQueue);

    /**
     * 插入邮件发送队列
     *
     * @param emailSenderQueue
     * @return
     */
    protected EmailSenderQueue addEmailSenderQueue(EmailSenderQueue emailSenderQueue) {
        emailSenderQueue.setOperationStatus(0);
        emailSenderQueue.setOperationCount(0);
        emailSenderQueueMapper.insert(emailSenderQueue);
        return emailSenderQueue;
    }



}
