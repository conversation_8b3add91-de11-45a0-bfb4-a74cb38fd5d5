<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.StudentInsuranceMapper">
  <select id="getStudentInsuranceList" resultType="com.get.salecenter.vo.StudentInsuranceVo">

    SELECT *
    FROM m_student_insurance sa
    <where>
      <if test="studentInsurance.fkStudentId!=null and studentInsurance.fkStudentId !=''">
        and sa.fk_student_id=#{studentInsurance.fkStudentId}
      </if>
      <!--查询条件-财务专属-->
      <if test="studentInsurance.isHidden!= null and !studentInsurance.isHidden">
        and sa.is_hidden = #{studentInsurance.isHidden}
      </if>
    </where>
  </select>

  <select id="verifyInsPermissions" resultType="java.lang.Long">
    select distinct msi.id from m_student_insurance msi
    LEFT JOIN m_student ms on ms.id = msi.fk_student_id
    LEFT JOIN m_agent ma on ma.id = msi.fk_agent_id
    left join ais_permission_center.m_staff gpcms on gpcms.id = msi.fk_staff_id
    left join  (SELECT ms.name name,sprs.fk_table_id fk_table_id,sprs.fk_staff_id fk_staff_id FROM s_student_project_role_staff sprs,ais_permission_center.m_staff ms
    WHERE ms.id = sprs.fk_staff_id and sprs.fk_table_name = 'm_student_insurance') sprsms on sprsms.fk_table_id = msi.id
    <if test="staffFollowerIds!=null and staffFollowerIds.size()>0">
      left join r_agent_staff ras on ras.fk_agent_id = msi.fk_agent_id
      and ras.is_active =1
    </if>

    where 1=1
      AND msi.id = #{insId}
    <if test="staffFollowerIds!=null and staffFollowerIds.size()>0">
      and (ras.fk_staff_id  in
      <foreach collection="staffFollowerIds" item="id" index="index" open="(" separator="," close=")">
        #{id}
      </foreach>

    </if>
    <if test="staffFollowerIds!=null and staffFollowerIds.size()>0">
      or msi.fk_staff_id  in
      <foreach collection="staffFollowerIds" item="id" index="index" open="(" separator="," close=")">
        #{id}
      </foreach>
      OR (msi.fk_staff_id IS NULL AND msi.fk_agent_id IS NULL)
      OR sprsms.fk_staff_id in
      <foreach collection="staffFollowerIds" item="id" index="index" open="(" separator="," close=")">
        #{id}
      </foreach>
      )
    </if>
  </select>

  <select id="getStudentInsuranceSummary" resultType="com.get.salecenter.vo.StudentInsuranceVo">
    select
      IFNULL(r.ar_status, 0) arStatus,
      IFNULL(p.ap_status, 0) apStatus,
      CASE
      WHEN IFNULL(r.ar_status, 0) = 2
      AND IFNULL(p.ap_status, 0) = 2
      THEN 2
      ELSE 1
      END settlementStatus,
        <!-- 结算状态：1=处理中、2=完成 -->
      r.currency_type_name AS receivableCurrencyTypeName,
      r.receivable_amount AS amountReceivable,
      r.actual_receivable_amount AS actualReceivableAmount,
      r.diff_receivable_amount AS diffReceivableAmount,
      p.currency_type_name AS payableCurrencyTypeName,
      p.payable_amount AS payableAmount,
      p.actual_payable_amount AS actualPayableAmount,
      p.diff_payable_amount AS diffPayableAmount,
      a.*,
      ms.fk_company_id fkCompanyId,
      ms.name studentName,
      ms.last_name lastName,
      ms.first_name firstName,
      ms.birthday birthday,
      ms.gender gender,
      ms.passport_num passportNum,
      ma.name fkAgentName,LENGTH(CONCAT(ms.first_name,ms.last_name)) as weights,
      CONCAT(
        (CASE WHEN IFNULL(mbp.name_chn,'') = ''
            THEN mbp.name
            ELSE CONCAT(mbp.name, '（', mbp.name_chn, '）')
        END),
        '/',
        a.business_provider_product) AS businessProviderAndProductName
    from m_student_insurance a

      <if test="!isStudentAdmin">
        INNER JOIN (
        <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.studentAuthority"/>
        )c ON c.id = a.fk_student_id
      </if>

    <!-- 获取申请计划的总应付状态：0=未付、1=部分已付、2=已付清 -->
    LEFT JOIN (

    SELECT a.fk_type_target_id,
    MIN(a.payable_amount) as payable_amount,
    MIN(a.actual_payable_amount) as actual_payable_amount,
    MIN(a.diff_payable_amount) as diff_payable_amount,
    MIN(a.currency_type_name) as currency_type_name,
    CASE WHEN SUM(a.diff_payable_amount)=0 THEN 2 ELSE
    CASE WHEN SUM(a.actual_payable_amount)>0 THEN 1 ELSE 0 END
    END ap_status
    FROM
    (

    SELECT a.fk_type_target_id,
    CONCAT( c.type_name ,"(",c.num, ")") as currency_type_name,
    IFNULL(a.payable_amount,0) payable_amount, -- 应付金额
    IFNULL(b.sum_amount_payable,0) sum_amount_payable, -- 实付折合金额（这个金额币种和应付一致，因为已经是折合了）
    IFNULL(b.sum_amount_exchange_rate,0) sum_amount_exchange_rate, -- 汇率调整金额
    (IFNULL(b.sum_amount_payable,0) + IFNULL(b.sum_amount_exchange_rate,0)) actual_payable_amount, -- 实付（折合金额+汇率调整金额）
    (IFNULL(b.sum_amount_payable,0) + IFNULL(b.sum_amount_exchange_rate,0) - IFNULL(a.payable_amount,0)) diff_payable_amount -- 差额
    FROM ais_sale_center.m_payable_plan a

    LEFT JOIN (
    <!-- 计算每条应付计划里累计的实付金额 -->
    SELECT a.fk_payable_plan_id,
    SUM(IFNULL(a.amount_payable,0)) sum_amount_payable,
    SUM(IFNULL(a.amount_exchange_rate,0)) sum_amount_exchange_rate
    FROM ais_finance_center.m_payment_form_item a
    LEFT JOIN ais_finance_center.m_payment_form b ON a.fk_payment_form_id=b.id
    WHERE b.`status`!=0 -- 关闭的付款单不作计算
    GROUP BY a.fk_payable_plan_id
    ) b ON a.id=b.fk_payable_plan_id
    LEFT JOIN ais_finance_center.u_currency_type c ON a.fk_currency_type_num=c.num
    WHERE a.fk_type_key = 'm_student_insurance' AND a.status!=0
    ) a
    GROUP BY a.fk_type_target_id
    ) p ON a.id=p.fk_type_target_id

    <!-- 获取申请计划的总应收状态：0=未收、1=部分已收、2=已收齐 -->
    LEFT JOIN (
    SELECT a.fk_type_target_id,
    MIN(a.receivable_amount) as receivable_amount,
    MIN(a.actual_receivable_amount) as actual_receivable_amount,
    MIN(a.diff_receivable_amount) as diff_receivable_amount,
    MIN(a.currency_type_name) as currency_type_name,
    CASE WHEN SUM(a.diff_receivable_amount)=0 THEN 2 ELSE
    CASE WHEN SUM(a.actual_receivable_amount)>0 THEN 1 ELSE 0 END
    END ar_status
    FROM (
    SELECT a.fk_type_target_id,
    CONCAT( c.type_name ,"(",c.num, "）") as currency_type_name,
    IFNULL(a.receivable_amount,0) receivable_amount, <!--  应收金额 -->
    IFNULL(b.sum_amount_receivable,0) sum_amount_receivable, <!--  实收折合金额（这个金额币种和应收一致，因为已经是折合了）-->
    IFNULL(b.sum_amount_exchange_rate,0) sum_amount_exchange_rate, <!--  汇率调整金额 -->
    (IFNULL(b.sum_amount_receivable,0) + IFNULL(b.sum_amount_exchange_rate,0)) actual_receivable_amount, <!--  实收（折合金额（含手续费）+汇率调整金额） -->
    (IFNULL(b.sum_amount_receivable,0) + IFNULL(b.sum_amount_exchange_rate,0) - IFNULL(a.receivable_amount,0)) diff_receivable_amount <!--  差额 -->
    FROM ais_sale_center.m_receivable_plan a
    LEFT JOIN (
    <!--  计算每条应收计划里累计的实收金额 -->
    SELECT a.fk_receivable_plan_id,
    SUM(IFNULL(a.amount_receivable,0)) sum_amount_receivable,
    SUM(IFNULL(a.amount_exchange_rate,0)) sum_amount_exchange_rate
    FROM ais_finance_center.m_receipt_form_item a
    LEFT JOIN ais_finance_center.m_receipt_form b ON a.fk_receipt_form_id=b.id
    WHERE b.`status`!=0 AND b.settlement_status = 1 <!--  关闭的收款单不作计算 -->
    GROUP BY a.fk_receivable_plan_id
    ) b ON a.id=b.fk_receivable_plan_id
    LEFT JOIN ais_finance_center.u_currency_type c ON a.fk_currency_type_num=c.num
    WHERE a.fk_type_key = 'm_student_insurance' AND a.status!=0
    ) a
    GROUP BY a.fk_type_target_id
    ) r ON a.id=r.fk_type_target_id

    LEFT JOIN m_student ms on ms.id = a.fk_student_id
    LEFT JOIN m_agent ma on ma.id = a.fk_agent_id
    left join ais_permission_center.m_staff gpcms on gpcms.id = a.fk_staff_id
    <!-- 好像没用上
    left join (
        SELECT ms.name name,sprs.fk_table_id fk_table_id,sprs.fk_staff_id fk_staff_id FROM s_student_project_role_staff sprs,ais_permission_center.m_staff ms
        WHERE ms.id = sprs.fk_staff_id and sprs.fk_table_name = 'm_student_insurance'
    ) sprsms on sprsms.fk_table_id = a.id
    -->
    <!-- 服务提供商 -->
    LEFT JOIN m_business_provider mbp ON mbp.id = a.fk_business_provider_id
    <!-- 好像没用上
    <if test="studentInsurance.staffFollowerIds!=null and studentInsurance.staffFollowerIds.size()>0">
      left join r_agent_staff ras on ras.fk_agent_id = a.fk_agent_id
      and ras.is_active =1
    </if>
    where 1=1
    <if test="studentInsurance.staffFollowerIds!=null and studentInsurance.staffFollowerIds.size()>0">
      and (ras.fk_staff_id  in
      <foreach collection="studentInsurance.staffFollowerIds" item="id" index="index" open="(" separator="," close=")">
        #{id}
      </foreach>

    </if>
    <if test="studentInsurance.staffFollowerIds!=null and studentInsurance.staffFollowerIds.size()>0">
      or a.fk_staff_id  in
      <foreach collection="studentInsurance.staffFollowerIds" item="id" index="index" open="(" separator="," close=")">
        #{id}
      </foreach>
      OR (a.fk_staff_id IS NULL AND a.fk_agent_id IS NULL)
      OR sprsms.fk_staff_id in
      <foreach collection="studentInsurance.staffFollowerIds" item="id" index="index" open="(" separator="," close=")">
        #{id}
      </foreach>
          )
    </if>
    -->
      where 1=1
    <if test="studentInsurance.fkCompanyId!=null">
      and ms.fk_company_id = #{studentInsurance.fkCompanyId}
    </if>
    <if test="studentInsurance.fkCompanyIds!=null and studentInsurance.fkCompanyIds.size()>0">
      and ms.fk_company_id IN
      <foreach collection="studentInsurance.fkCompanyIds" item="fkCompanyId" index="index" open="(" separator="," close=")">
        #{fkCompanyId}
      </foreach>
    </if>
    <if test="studentInsurance.studentName!=null and studentInsurance.studentName!=''">
      AND (
      REPLACE(CONCAT(LOWER(ms.first_name),LOWER(ms.last_name)),' ','') like concat('%',#{studentInsurance.studentName},'%')
      OR REPLACE(CONCAT(LOWER(ms.last_name),LOWER(ms.first_name)),' ','') like concat('%',#{studentInsurance.studentName},'%')
      OR REPLACE(LOWER(ms.`name`),' ','') like concat('%',#{studentInsurance.studentName},'%')
      OR REPLACE(LOWER(ms.last_name),' ','') like concat('%',#{studentInsurance.studentName},'%')
      OR REPLACE(LOWER(ms.first_name),' ','') like concat('%',#{studentInsurance.studentName},'%') -- 过滤学生中英文名字和受保人名字
      OR LOWER(ms.num) like concat('%',#{studentInsurance.studentName},'%')
      OR LOWER(ms.passport_num) like concat('%',#{studentInsurance.studentName},'%')
      OR REPLACE(CONCAT(LOWER(a.insurant_first_name),LOWER(a.insurant_last_name)),' ','') like concat('%',#{studentInsurance.studentName},'%')
      OR REPLACE(CONCAT(LOWER(a.insurant_last_name),LOWER(a.insurant_first_name)),' ','') like concat('%',#{studentInsurance.studentName},'%')
      OR REPLACE(LOWER(a.`insurant_name`),' ','') like concat('%',#{studentInsurance.studentName},'%')
      OR REPLACE(LOWER(a.insurant_last_name),' ','') like concat('%',#{studentInsurance.studentName},'%')
      OR REPLACE(LOWER(a.insurant_first_name),' ','') like concat('%',#{studentInsurance.studentName},'%') -- 过滤学生中英文名字和受保人名字
      OR LOWER(a.insurant_passport_num) like concat('%',#{studentInsurance.studentName},'%')
      )
    </if>
    <if test="studentInsurance.agentName!=null and studentInsurance.agentName!=''">
      and LOWER(ma.name) like concat("%",#{studentInsurance.agentName},"%")
    </if>
    <if test="studentInsurance.staffName!=null and studentInsurance.staffName!=''">
      and LOWER(gpcms.name) like concat("%",#{studentInsurance.staffName},"%")
    </if>
    <if test="studentInsurance.memberName!=null and studentInsurance.memberName!=''">
      AND EXISTS (
        SELECT 1 FROM s_student_project_role_staff sprs
        LEFT JOIN ais_permission_center.m_staff ms ON ms.id = sprs.fk_staff_id
        WHERE a.id = sprs.fk_table_id and sprs.fk_table_name = 'm_student_insurance'
        AND LOWER(ms.name) like concat("%",#{studentInsurance.memberName},"%")
        )
    </if>
    <if test="studentInsurance.fkBusinessChannelId!=null">
      and a.fk_business_channel_id = #{studentInsurance.fkBusinessChannelId}
    </if>
    <if test="studentInsurance.insuranceNum!=null and studentInsurance.insuranceNum!=''">
      and LOWER(a.insurance_num) like concat("%",#{studentInsurance.insuranceNum},"%")
    </if>
    <if test="studentInsurance.status!=null">
      and a.status = #{studentInsurance.status}
    </if>
    <!--查询条件-开始日期-->
    <if test="studentInsurance.insuranceStartTimeStart != null and studentInsurance.insuranceStartTimeStart.toString() !=''">
      and DATE_FORMAT(insurance_start_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{studentInsurance.insuranceStartTimeStart},'%Y-%m-%d')
    </if>
    <!--查询条件-结束日期-->
    <if test="studentInsurance.insuranceStartTimeEnd!= null and studentInsurance.insuranceStartTimeEnd.toString() !=''">
      and DATE_FORMAT(insurance_start_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{studentInsurance.insuranceStartTimeEnd},'%Y-%m-%d')
    </if>

    <!--查询条件-开始日期-->
    <if test="studentInsurance.insuranceEndTimeStart != null and studentInsurance.insuranceEndTimeStart.toString() !=''">
      and DATE_FORMAT(insurance_end_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{studentInsurance.insuranceEndTimeStart},'%Y-%m-%d')
    </if>
    <!--查询条件-结束日期-->
    <if test="studentInsurance.insuranceEndTimeEnd!= null and studentInsurance.insuranceEndTimeEnd.toString() !=''">
      and DATE_FORMAT(insurance_end_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{studentInsurance.insuranceEndTimeEnd},'%Y-%m-%d')
    </if>

    <!--查询条件-开始日期-->
    <if test="studentInsurance.gmtCreateBeginTime != null and studentInsurance.gmtCreateBeginTime.toString() !=''">
      and DATE_FORMAT(a.gmt_create,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{studentInsurance.gmtCreateBeginTime},'%Y-%m-%d')
    </if>
    <!--查询条件-结束日期-->
    <if test="studentInsurance.gmtCreateEndTime!= null and studentInsurance.gmtCreateEndTime.toString() !=''">
      and DATE_FORMAT(a.gmt_create,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{studentInsurance.gmtCreateEndTime},'%Y-%m-%d')
    </if>
    <!--查询条件-开始日期-->
    <if test="studentInsurance.insuranceBuyTimeStart != null and studentInsurance.insuranceBuyTimeStart.toString() !=''">
      and DATE_FORMAT(insurance_buy_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{studentInsurance.insuranceBuyTimeStart},'%Y-%m-%d')
    </if>
    <!--查询条件-结束日期-->
    <if test="studentInsurance.insuranceBuyTimeEnd!= null and studentInsurance.insuranceBuyTimeEnd.toString() !=''">
      and DATE_FORMAT(insurance_buy_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{studentInsurance.insuranceBuyTimeEnd},'%Y-%m-%d')
    </if>
    <!--查询条件-应收状态-->
    <if test="studentInsurance.arStatus != null">
      and IFNULL(r.ar_status, 0) = #{studentInsurance.arStatus}
    </if>
    <!--查询条件-应付状态-->
    <if test="studentInsurance.apStatus != null">
      and IFNULL(p.ap_status, 0) = #{studentInsurance.apStatus}
    </if>
    <!--查询条件-服务提供商/产品-->
    <if test="studentInsurance.businessProviderAndProductName != null and studentInsurance.businessProviderAndProductName != ''">
      AND CONCAT(
      (CASE WHEN IFNULL(mbp.name_chn,'') = ''
        THEN mbp.name
        ELSE CONCAT(mbp.name, '（', mbp.name_chn, '）')
      END),
      '/',
      a.business_provider_product) LIKE CONCAT('%', #{studentInsurance.businessProviderAndProductName}, '%')
    </if>
    <!--查询条件-财务专属-->
    <if test="studentInsurance.isHidden!= null and !studentInsurance.isHidden">
      and a.is_hidden = #{studentInsurance.isHidden}
    </if>
    <!--GROUP BY msi.id-->
    <choose>
      <when test="studentInsurance.studentName!=null and studentInsurance.studentName!=''">
        ORDER BY weights ASC
      </when>
      <otherwise>
        ORDER BY a.gmt_create desc
      </otherwise>
    </choose>
  </select>
  <select id="getNumById" resultType="java.lang.String">
    SELECT num FROM `m_student_insurance` where id = #{fkTableId}
  </select>
  <select id="getStudentIdById" resultType="java.lang.Long">
    SELECT fk_student_id FROM `m_student_insurance` where id = #{fkTableId}
  </select>
  <select id="getStudentInsuranceSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    SELECT
      id,
      num NAME
    FROM
      `m_student_insurance`
    WHERE
      fk_student_id = #{studentId}
  </select>


  <select id="getStudentInsurances" resultType="com.get.salecenter.entity.StudentInsurance">
    SELECT
    *
    FROM
    `m_student_insurance` msa
    left join  m_student ms on ms.id = msa.fk_student_id
    <if test="keyWord!=null">
      left join m_business_channel mbc on mbc.id = msa.fk_business_channel_id
    </if>
    WHERE msa.status = 1
    <if test="keyWord!=null">
      AND ((ms.name LIKE CONCAT('%', #{keyWord}, '%') OR ms.last_name LIKE CONCAT('%', #{keyWord}, '%') OR ms.first_name LIKE CONCAT('%', #{keyWord}, '%')
          OR msa.insurant_name LIKE CONCAT('%', #{keyWord}, '%') OR msa.insurant_last_name LIKE CONCAT('%', #{keyWord}, '%') OR msa.insurant_first_name LIKE CONCAT('%', #{keyWord}, '%'))
      or (mbc.name LIKE CONCAT('%', #{keyWord}, '%') or mbc.name_chn LIKE CONCAT('%', #{keyWord}, '%'))
      or (msa.num LIKE CONCAT('%', #{keyWord}, '%')))
    </if>
    <if test="studentId!=null">
      and  msa.fk_student_id = #{studentId}
    </if>
    <if test="companyIds!=null and companyIds.size>0">
      and  ms.fk_company_id in
      <foreach collection="companyIds" separator="," open="(" close=")" item="item">
        #{item}
      </foreach>
    </if>
    ORDER BY
    LENGTH(ms.name),LENGTH(mbc.name)
    limit 20
  </select>
  <select id="getCompanyIdsByInsuranceIds" resultType="com.get.salecenter.vo.StudentInsuranceVo">
    SELECT
    msi.id,msi.fk_student_id,ms.fk_company_id
    FROM
    m_student_insurance AS msi
    INNER JOIN
    m_student AS ms
    ON
    msi.fk_student_id = ms.id
    <where>
      <if test="insuranceIds != null and insuranceIds.size()>0">
        AND msi.id in
        <foreach collection="insuranceIds" item="insuranceId" index="index" open="(" separator="," close=")">
          #{insuranceId}
        </foreach>
      </if>
    </where>
  </select>

  <select id="getInsuranceIdsByStudentId" resultType="java.lang.Long">
    select id from m_student_insurance where fk_student_id = #{studentId}
  </select>
  <select id="getStudentInsuranceAgent" resultType="com.get.salecenter.vo.AgentsBindingVo">
    SELECT a.NAME as agentName,
           d.name as bdName,
           m.num
    FROM m_student_insurance m
           LEFT JOIN m_agent a ON a.id = m.fk_agent_id
           LEFT JOIN ais_permission_center.m_staff d ON d.id = m.fk_staff_id
    LEFT JOIN m_student t on m.fk_student_id = t.id
    WHERE t.num = #{fkStudentNum}
    AND d.is_active = 1
    AND a.is_active = 1
  </select>
    <select id="getAgentIdByTargetId" resultType="java.lang.Long" parameterType="Long">
      SELECT
          m.id
      FROM
          m_agent m
      INNER JOIN m_student_insurance s ON s.fk_agent_id = m.id
      WHERE
          s.id = #{targetId}
    </select>
  <select id="getIdByTargetId" resultType="java.lang.Long" parameterType="Long">
     SELECT
        fk_business_channel_id
     FROM
        m_student_insurance
     WHERE
        id = #{targetId}
  </select>
  <select id="getPaidAmountByIds" resultType="com.get.salecenter.vo.InsurancePayFormDetailVo">
    SELECT
    <!--币种-->
    CONCAT( c.type_name, "(", c.num, "）" ) AS payableCurrencyTypeName,
    <!--实收（折合金额+汇率调整）-->
    IFNULL( i.amount_payable, 0 ) + IFNULL( i.amount_exchange_rate, 0 ) AS actualPayableAmount,
    <!--付款时间-->
    i.gmt_create as actualPayTime,
    s.id as insuranceId
    FROM
    ais_finance_center.m_payment_form_item i
    INNER JOIN m_payable_plan p ON p.id = i.fk_payable_plan_id
    INNER JOIN m_student_insurance s ON s.id = p.fk_type_target_id and p.fk_type_key = "m_student_insurance"
    LEFT JOIN ais_finance_center.u_currency_type c ON p.fk_currency_type_num=c.num
    <if test="itemIds!=null and itemIds.size>0">
      where s.id in
      <foreach collection="itemIds" item="itemId" open="(" separator="," close=")">
        #{itemId}
      </foreach>
    </if>
  </select>
</mapper>