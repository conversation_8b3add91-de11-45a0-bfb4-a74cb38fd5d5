package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.salecenter.vo.ConventionAwardCodeVo;
import com.get.salecenter.entity.ConventionAwardCode;
import com.get.salecenter.service.IConventionAwardCodeService;
import com.get.salecenter.dto.ConventionAwardCodeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2021/9/14
 * @TIME: 11:36
 * @Description:
 **/
@Api(tags = "獎券管理")
@RestController
@RequestMapping("sale/conventionAwardCode")
public class ConventionAwardCodeController {
    @Resource
    private IConventionAwardCodeService conventionAwardCodeService;


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :批量新增信息
     * @Param [eventTypeVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/獎券管理/批量新增獎券")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(ConventionAwardCodeDto.Add.class) ConventionAwardCodeDto conventionAwardCodeDto) {
        conventionAwardCodeService.batchAdd(conventionAwardCodeDto);
        return SaveResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/獎券管理/删除獎券")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        conventionAwardCodeService.delete(id);
        return DeleteResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ConventionAwardVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/獎券管理/查询獎券")
    @PostMapping("datas")
    public ResponseBo<ConventionAwardCodeVo> datas(@RequestBody SearchBean<ConventionAwardCodeDto> page) {
        List<ConventionAwardCodeVo> datas = conventionAwardCodeService.getConventionAwardCodes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ConventionAwardVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "通过奖品获取还没有使用的奖券", notes = "")
    @GetMapping("getListTicketsNoUsedByAwardId/{id}")
    @VerifyLogin(IsVerify = false)
    public ResponseBo<ConventionAwardCode> getListTicketsNoUsedByAwardId(@PathVariable("id") Long id, @RequestParam("fkConventionId") Long fkConventionId) {
        List<ConventionAwardCode> data = conventionAwardCodeService.getListTicketsNoUsedByAwardId(id, fkConventionId);
        return new ListResponseBo<>(data);
    }

}
