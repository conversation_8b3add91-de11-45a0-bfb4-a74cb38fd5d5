<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.get.salecenter.dao.sale.BusinessProviderAccountMapper">

    <select id="getBusinessProviderAccountsById"
            resultType="com.get.salecenter.vo.BusinessProviderAccountVo">
        SELECT
            *
        FROM
            m_business_provider_account
        WHERE
            fk_business_provider_id = #{businessProviderId}
    </select>
    <select id="getContractAccountExist" resultType="com.get.salecenter.vo.BusinessProviderAccountVo">
        SELECT
            a.*,p.name_chn as businessProviderName
        FROM
            m_business_provider_account a
            INNER JOIN m_business_provider p ON p.id = a.fk_business_provider_id
        WHERE
            1=1
        <if test="businessProviderId!=null and businessProviderId!=''">
            AND p.id = #{businessProviderId}
        </if>
        <if test="bankAccount!=null and bankAccount!=''">
            AND a.bank_account = #{bankAccount}
        </if>
        <if test="bankAccountNum!=null and bankAccountNum!=''">
            AND a.bank_account_num = #{bankAccountNum}
        </if>
    </select>
    <select id="getBusinessProviderAccountListByFkTargetId"
            resultType="com.get.core.mybatis.base.BaseSelectEntity">
            SELECT
                ba.id,
                CONCAT(
                    '【',
                    CASE
                WHEN IFNULL(ct.type_name, '') = '' THEN
                    ct.num
                ELSE
                    CONCAT(
                        ct.num,
                        '（',
                        ct.type_name,
                        '）'
                    )
                END,
                '】',
                ba.bank_account,
                ',',
                ba.bank_name
                ) AS NAME
            FROM
                m_business_provider_account ba
            LEFT JOIN ais_finance_center.u_currency_type ct ON ct.num = ba.fk_currency_type_num
            WHERE
                ba.is_active = 1
            AND ba.fk_business_provider_id = #{fkTargetId}
    </select>
    <select id="getBusinessProviderBankAccountNameById" resultType="java.lang.String">
            SELECT
                CONCAT(
                    '【',
                    CASE
                WHEN IFNULL(ct.type_name, '') = '' THEN
                    ct.num
                ELSE
                    CONCAT(
                        ct.num,
                        '（',
                        ct.type_name,
                        '）'
                    )
                END,
                '】',
                ba.bank_account,
                ',',
                ba.bank_name
                )
            FROM
                m_business_provider_account ba
            LEFT JOIN ais_finance_center.u_currency_type ct ON ct.num = ba.fk_currency_type_num
            WHERE
                ba.is_active = 1
            AND ba.id = #{fkBankAccountId}
    </select>
</mapper>
