knife4j:
  cloud:
    routes:
#      - name: 认证中心
#        uri: localhost:8090
#        location: /authentication/v2/api-docs
      - name: 权限中心
        uri: ************:9013
        location: /permission-center/v2/api-docs
#      - name: 工作流管理
#        uri: localhost:8090
#        location: /workflow-center/v2/api-docs
      - name: 销售中心
        uri: ************:9013
        location: /sale-center/v2/api-docs
      - name: 学校中心
        uri: ************:9013
        location: /institution-center/v2/api-docs
      - name: 财务中心
        uri: ************:9013
        location: /finance-center/v2/api-docs
#      - name: 配置中心
#        uri: localhost:8090
#        location: /platform-center/v2/api-docs
#      - name: 提醒中心
#        uri: localhost:8090
#        location: /reminder-center/v2/api-docs
#      - name: 办公中心
#        uri: localhost:8090
#        location: /office-center/v2/api-docs
#      - name: 考试中心
#        uri: localhost:8090
#        location: /exam-center/v2/api-docs
#      - name: 帮助中心
#        uri: localhost:8090
#        location: /help-center/v2/api-docs
#      - name: schoolGate中心
#        uri: localhost:8090
#        location: /school-gate-center/v2/api-docs
#      - name: 竞赛中心
#        uri: localhost:8090
#        location: /competition-center/v2/api-docs
#      - name: 投票中心
#        uri: localhost:8090
#        location: /voting-center/v2/api-docs
#      - name: 报表中心
#        uri: localhost:8090
#        location: /report-center/v2/api-docs
#      - name: 人才中心
#        uri: localhost:8090
#        location: /resume-center/v2/api-docs
#      - name: 佣金管理
#        uri: localhost:8090
#        location: /mps-center/v2/api-docs
#      - name: 文件中心
#        uri: localhost:8090
#        location: /file-center/v2/api-docs
