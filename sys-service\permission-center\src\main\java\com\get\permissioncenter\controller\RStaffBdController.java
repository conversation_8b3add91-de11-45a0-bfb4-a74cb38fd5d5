package com.get.permissioncenter.controller;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.permissioncenter.dto.RStaffBdDto;
import com.get.permissioncenter.service.RStaffBdService;
import com.get.salecenter.dto.StaffBdCodeDto;
import com.get.salecenter.vo.StaffBdCodeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 员工绑定bd
 */
@Api(tags = "员工绑定bd")
@RestController
@RequestMapping("permission/staffBd")
public class RStaffBdController{

    @Resource
    private RStaffBdService rStaffBdService;

    @ApiOperation(value = "查询接口", notes = "查询学校权限和员工和BD关系")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/学校权限管理/查询学校权限组别和员工和BD关系")
    @PostMapping("selectInstitutionPermissionGroupStaffAndBd")
    public ResponseBo getInstitutionPermissionGroupStaffAndBd(@RequestBody SearchBean<StaffBdCodeDto> page) {
        List<StaffBdCodeVo> datas  = rStaffBdService.dataList(page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ResponseBo<>(datas, p);
    }
    /**
     * 新增数据
     *
     * @param rStaffBdDto 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "批量新增数据", notes = "新增数据")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/学校权限管理/新增员工和BD关系")
    @PostMapping("add")
    public ResponseBo insert(@RequestBody RStaffBdDto rStaffBdDto) {
        return new ResponseBo<>(rStaffBdService.addStaffBd(rStaffBdDto));
    }

    /**
     * 删除数据
     *
     * @param id 主键
     * @return 删除结果
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DELETE, description = "权限中心/学校权限管理/删除员工和BD关系")
    @GetMapping("delete")
    public ResponseBo delete(@RequestParam("id") Long id) {
        rStaffBdService.removeById(id);
        return ResponseBo.ok();
    }


    @ApiOperation(value = "批量删除接口", notes = "删除接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/学校权限管理/批量删除员工和BD关系")
    @PostMapping("batchDelete")
    public ResponseBo batchDelete(@RequestBody RStaffBdDto rStaffBdDto){
        rStaffBdService.batchDelete(rStaffBdDto);
        return ResponseBo.ok();

    }


}

