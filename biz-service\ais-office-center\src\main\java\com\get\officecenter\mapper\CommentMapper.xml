<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.officecenter.mapper.CommentMapper">
    <insert id="insertBatch">
        INSERT INTO s_comment (
        fk_table_name,
        fk_table_id,
        comment,
        gmt_create,
        gmt_create_user,
        gmt_modified,
        gmt_modified_user
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.fkTableName},
            #{item.fkTableId},
            #{item.comment},
            #{item.gmtCreate},
            #{item.gmtCreateUser},
            #{item.gmtModified},
            #{item.gmtModifiedUser}
            )
        </foreach>
    </insert>

  <select id="getCommentList" resultType="com.get.officecenter.entity.Comment">
      SELECT *
      FROM s_comment
      WHERE fk_table_name = #{fkTableName}
      AND fk_table_id IN
      <foreach item="id" collection="fkTableIds" open="(" separator="," close=")">
          #{id}
      </foreach>
  </select>
  
</mapper>