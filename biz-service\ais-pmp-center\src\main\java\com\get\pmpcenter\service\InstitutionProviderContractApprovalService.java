package com.get.pmpcenter.service;

import com.get.pmpcenter.dto.institution.ApprovalContractDto;
import com.get.pmpcenter.dto.institution.SubmitApprovalDto;
import com.get.pmpcenter.entity.InstitutionProviderContractApproval;
import com.get.pmpcenter.vo.common.StaffVo;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/3/24
 * @Version 1.0
 * @apiNote:
 */
public interface InstitutionProviderContractApprovalService {

    /**
     * 员工列表-审批人
     *
     * @return
     */
    List<StaffVo> getStaffList();

    /**
     * 提交审批
     *
     * @param submitApprovalDto
     */
    void submitApproval(SubmitApprovalDto submitApprovalDto);

    /**
     * 审核合同
     *
     * @param approvalContractDto
     */
    void approvalContract(ApprovalContractDto approvalContractDto);

    /**
     * 获取审批记录
     *
     * @param contractId
     * @return
     */
    List<InstitutionProviderContractApproval> getApprovalList(Long contractId);
}
