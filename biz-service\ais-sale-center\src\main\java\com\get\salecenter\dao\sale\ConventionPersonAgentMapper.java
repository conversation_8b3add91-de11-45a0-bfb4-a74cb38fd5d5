package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.vo.ConventionPersonVo;
import com.get.salecenter.entity.ConventionPersonAgent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/27 12:21
 * @verison: 1.0
 * @description: 参展人员和代理关联中间表mapper
 */
@Mapper
public interface ConventionPersonAgentMapper extends BaseMapper<ConventionPersonAgent> {

    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insert(ConventionPersonAgent record);

    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(ConventionPersonAgent record);

    /**
     * 获取参展人员ids
     *
     * @param agentIds
     * @return
     */
    List<Long> getConventionPersonIds(List<Long> agentIds);

    /**
     * 根据id获取代理名称
     *
     * @param id
     * @return
     */
    Long getAgentId(Long id);

    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [agentId]
     * <AUTHOR>
     */
    Boolean isExistByAgentId(Long agentId);

    List<ConventionPersonVo> getConventionPersonAgentNameMapByIds(@Param("personAgentIds")List<Long> personAgentIds);
}