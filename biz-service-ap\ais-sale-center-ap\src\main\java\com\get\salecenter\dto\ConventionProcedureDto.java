package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @author: Sea
 * @create: 2020/7/2 11:22
 * @verison: 1.0
 * @description:
 */
@Data
public class ConventionProcedureDto extends BaseVoEntity{

    /**
     * 峰会Id
     */
    @NotNull(message = "峰会id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "峰会Id", required = true)
    private Long fkConventionId;

    /**
     * 流程涉及分配桌子类型key(可无)
     */
    @ApiModelProperty(value = "流程涉及分配桌子类型key(可无)")
    private String fkTableTypeKey;

    /**
     * 流程主题
     */
    @NotBlank(message = "流程主题不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "流程主题", required = true)
    private String subject;

    /**
     * 举行地点
     */
//    @NotBlank(message = "举行地点不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "举行地点", required = true)
    private String venue;

    /**
     * 流程描述
     */
    @NotBlank(message = "流程描述不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "流程描述", required = true)
    private String description;

    /**
     * 计划开始时间
     */
    @NotNull(message = "计划开始时间不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "计划开始时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date startTime;

    /**
     * 计划结束时间
     */
    @NotNull(message = "计划结束时间不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "计划结束时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endTime;

    /**
     * 步骤索引
     */
    @NotNull(message = "步骤索引不能为空")
    @ApiModelProperty(value = "步骤索引(创建峰会流程时候的顺序)")
    private Integer stepIndex;

   
}
