package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("r_payable_plan_settlement_installment")
public class PayablePlanSettlementInstallment extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "财务结算汇总批次号")
    private String numSettlementBatch;

    @ApiModelProperty(value = "应付计划Id")
    private Long fkPayablePlanId;

    @ApiModelProperty(value = "收款单子项Id（比对这个收款记录id，如果存在，不需要再创建）")
    private Long fkReceiptFormItemId;

    @ApiModelProperty(value = "付款单子项Id")
    private Long fkPaymentFormItemId;

    @ApiModelProperty(value = "发票和应收计划关系Id（预付时记录发票和应收计划关系Id")
    private Long fkInvoiceReceivablePlanId;

    @ApiModelProperty(value = "发票Id（预付时记录和发票关系）")
    private Long fkInvoiceId;

    @ApiModelProperty(value = "预计支付金额")
    private BigDecimal amountExpect;

    @ApiModelProperty(value = "预计手续费金额")
    private BigDecimal serviceFeeExpect;

    @ApiModelProperty(value = "实际支付金额（初始）")
    private BigDecimal amountActualInit;

    @ApiModelProperty(value = "实际手续费金额（初始）")
    private BigDecimal serviceFeeActualInit;

    @ApiModelProperty(value = "实际支付金额（合并）")
    private BigDecimal amountActual;

    @ApiModelProperty(value = "实际手续费金额（合并）")
    private BigDecimal serviceFeeActual;

    @ApiModelProperty(value = "帐号导出时间")
    private Date accountExportTime;

    @ApiModelProperty(value = "是否回滚，0否/1是")
    @TableField("is_roll_back")
    private Boolean rollBack;

    @ApiModelProperty(value = "回滚时间")
    @UpdateWithNull
    private Date rollBackTime;

    @ApiModelProperty(value = "结算代理Id（第四提交到第五步时记录，结算代理id快照）")
    private Long fkAgentIdSettlement;

    @UpdateWithNull
    @ApiModelProperty(value = "学生代理合同账户Id")
    private Long fkAgentContractAccountId;

    @UpdateWithNull
    @ApiModelProperty(value = "币种编号（代理账户）")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "状态：0=未处理/1=处理中/2=完成")
    private Integer status;

    @ApiModelProperty(value = "结算状态：0未结算/1结算中/2代理确认/3财务确认/4财务汇总")
    private Integer statusSettlement;

    //status_review
    @ApiModelProperty(value = "预审状态：提差异-1/未审核0/已审核1")
    private Integer statusReview;

}