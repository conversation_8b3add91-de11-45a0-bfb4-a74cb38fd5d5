package com.get.salecenter.service.impl;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ErrorCodeEnum;
import com.get.common.result.ResponseBo;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.RStudentIssueStudentMapper;
import com.get.salecenter.entity.RStudentIssueStudent;
import com.get.salecenter.service.RStudentIssueStudentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author:cream
 * @Date: 2023/5/11  12:45
 */
@Service
@Slf4j
public class RStudentIssueStudentServiceImpl implements RStudentIssueStudentService {

    /**
     * 旧ISSUE请求地址
     */
    @Value("${issue.url}")
    private String oldIssueUrl;


    /**
     * ISSUE请求地址
     */
    @Value("${newissue.url}")
    private String newIssueUrl;

    @Resource
    private RStudentIssueStudentMapper rStudentIssueStudentMapper;


    /**
     * Author Cream
     * Description : //删除issue关联表数据
     * Date 2023/5/11 12:47
     * Params:
     * Return
     */
    @Override
    public void deleteByStudentId(Long mergedStudentId) {
        if (GeneralTool.isNotEmpty(mergedStudentId)) {
            if (rStudentIssueStudentMapper.selectCount(Wrappers.<RStudentIssueStudent>lambdaQuery().eq(RStudentIssueStudent::getFkStudentId,mergedStudentId))>0) {
                rStudentIssueStudentMapper.delete(Wrappers.<RStudentIssueStudent>lambdaQuery().eq(RStudentIssueStudent::getFkStudentId,mergedStudentId));
            }
        }
    }

    /**
     * Author Cream
     * Description : //处理合并issue学生
     * Date 2023/5/17 9:42
     * Params:
     * Return
     */
    @Override
    public void processIssueStudent(Long mergedStudentId, Long targetStudentId, String stuSource) {
        RStudentIssueStudent mergedStudent = rStudentIssueStudentMapper.selectOne(Wrappers.<RStudentIssueStudent>lambdaQuery().eq(RStudentIssueStudent::getFkStudentId,mergedStudentId));
        RStudentIssueStudent targetStudent = rStudentIssueStudentMapper.selectOne(Wrappers.<RStudentIssueStudent>lambdaQuery().eq(RStudentIssueStudent::getFkStudentId,targetStudentId));
        if (GeneralTool.isNotEmpty(mergedStudent) && GeneralTool.isNotEmpty(targetStudent)) {
            //旧issue学生
            Long mergedOldIssueId = mergedStudent.getFkStudentIdIssue();
            Long targetOldIssueId = targetStudent.getFkStudentIdIssue();
            //新issue学生
            Long mergedNewIssueId = mergedStudent.getFkStudentIdIssue2();
            Long targetNewIssueId = targetStudent.getFkStudentIdIssue2();
            boolean isOldIssue = GeneralTool.isNotEmpty(mergedOldIssueId);
            boolean isNewIssue = GeneralTool.isNotEmpty(mergedNewIssueId);

            boolean targetIsNewIssue = GeneralTool.isNotEmpty(targetNewIssueId);

            //一个是旧issue，一个是新issue 抛出异常
//            if ((isOldIssue && GeneralTool.isNotEmpty(targetNewIssueId)) || (isNewIssue && GeneralTool.isNotEmpty(targetOldIssueId))){
//                throw new GetServiceException("需要合并的学生分别为新旧ISSUE创建，请联系管理员，需要进行手动合并");
//            }
            if (isOldIssue && targetIsNewIssue) {
                callIssue(mergedOldIssueId,null,stuSource,true);
            } else if (isOldIssue) {
                callIssue(mergedOldIssueId,targetOldIssueId,stuSource,true);
            } else if (isNewIssue) {
                callIssue(mergedNewIssueId,targetNewIssueId,stuSource,false);
            }
        }else if (GeneralTool.isNotEmpty(mergedStudent)){
            Long mergedOldIssueId = mergedStudent.getFkStudentIdIssue();
            Long mergedNewIssueId = mergedStudent.getFkStudentIdIssue2();
            boolean isOldIssue = GeneralTool.isNotEmpty(mergedOldIssueId);
            if (isOldIssue) {
                callIssue(mergedOldIssueId,null,stuSource,true);
            }else {
                callIssue(mergedNewIssueId,null,stuSource,false);
            }
        }
    }

    private void callIssue(Long mergedStudentId, Long targetStudentId, String stuSource,boolean isOld){
        if (Objects.isNull(mergedStudentId)){
            return;
        }
        Map<String,Object> dataMap = new HashMap<>(2);
        dataMap.put("sourceIssueId",mergedStudentId);
        dataMap.put("targetIssueId",targetStudentId);
        dataMap.put("stuSource",stuSource);
        String result;
        if (isOld) {
            log.info("合并旧issue学生");
            result = HttpUtil.get(oldIssueUrl + "/stu_appl/getMergeIssueStudent", dataMap);
        }else {
            log.info("合并新issue学生");
            result = HttpUtil.get(newIssueUrl + "/issue/student/getMergeIssueStudent", dataMap);
        }
        try {
            ResponseBo responseBo = JSON.parseObject(JSON.toJSONString(JSON.parse(result)), ResponseBo.class);
            if (GeneralTool.isNotEmpty(responseBo) && !responseBo.getSuccess() && !ErrorCodeEnum.REQUEST_OK.getCode().equals(responseBo.getCode())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("issue_merge_failed_operation_rollback"));
            }
        }catch (Exception e){
            log.info(e.getMessage());
            throw new GetServiceException(LocaleMessageUtils.getMessage("issue_remote_interface_exception"));
        }

    }
}
