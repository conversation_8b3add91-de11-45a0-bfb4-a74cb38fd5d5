<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.permissioncenter.dao.StaffEmailMapper">

    <select id="checkStaffEmail" resultType="java.lang.String">
        SELECT
            concat(if(ms.is_on_duty=1,'','【离职】'),ms.name,IFNULL(concat('(',ms.name_en,')'),''))
        FROM
            m_staff_email AS mse
                INNER JOIN m_staff AS ms ON ms.id = mse.fk_staff_id
        where ms.fk_company_id = #{fkCompanyId}
          and mse.email = #{email}
        and ms.is_on_duty = 1
        <if test="staffId !=null">
            and mse.fk_staff_id != #{staffId}
        </if>
    </select>

</mapper>