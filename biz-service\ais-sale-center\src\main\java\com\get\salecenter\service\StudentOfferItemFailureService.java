package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.vo.StudentOfferItemFailureVo;
import com.get.salecenter.dto.StudentOfferItemFailureDto;

import java.util.List;

/**
 * 学习计划生成应收、应付计划失败逻辑类
 *
 * <AUTHOR>
 * @date 2021/7/8 16:54
 */
public interface StudentOfferItemFailureService {

    /**
     * 学习计划生成失败列表
     *
     * @Date 17:43 2021/7/8
     * <AUTHOR>
     */
    List<StudentOfferItemFailureVo> getStudentOfferItemFailureList(StudentOfferItemFailureDto itemVo, Page page);

    /**
     * 公司代理下拉框
     *
     * @Date 11:01 2021/7/14
     * <AUTHOR>
     */
    List<BaseSelectEntity> agentSelect(Long companyId);

    /**
     * 重新生成
     *
     * @Date 11:02 2021/7/9
     * <AUTHOR>
     */
    ResponseBo regenerate(Long id);
}
