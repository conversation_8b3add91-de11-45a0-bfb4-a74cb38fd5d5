package com.get.officecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.CommonUtil;
import com.get.core.log.annotation.OperationLogger;
import com.get.officecenter.vo.CommentStatisticsVo;
import com.get.officecenter.vo.TaskItemVo;
import com.get.officecenter.entity.Comment;
import com.get.officecenter.entity.TaskItem;
import com.get.officecenter.service.ITaskItemService;
import com.get.officecenter.dto.TaskItemCommentDto;
import com.get.officecenter.dto.TaskItemDto;
import com.get.officecenter.vo.TaskStatisticsVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;

/**
 * 子任务管理控制器
 */
@Api(tags = "系统任务管理-子任务管理")
@RestController
@RequestMapping("office/taskItem")
public class TaskItemController {

    @Resource
    private ITaskItemService taskItemService;

    @ApiOperation(value = "子任务列表")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/系统任务/任务列表/子任务列表")
    @PostMapping("/getTaskItemList")
    public ListResponseBo<TaskItemVo> getTaskItemList(@RequestBody @Validated SearchBean<TaskItemDto> searchBean) {
        List<TaskItemVo> datas = taskItemService.getTaskItems(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "个人完成统计列表")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/系统任务/任务列表/个人完成统计列表")
    @PostMapping("/getPersonalTaskStatistics")
    public ListResponseBo<TaskStatisticsVo> getPersonalTaskStatistics(@RequestBody TaskItemDto taskItemDto) {
        List<TaskStatisticsVo> datas = taskItemService.getPersonalTaskStatistics(taskItemDto,null);
        //Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(datas);
    }


    @ApiOperation(value = "导出个人完成统计列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/系统任务/任务列表/导出个人完成统计列表")
    @PostMapping("exportTaskStatistics")
    public void exportTaskStatistics(HttpServletResponse response, @RequestBody TaskItemDto taskItemDto) {
        taskItemService.exportTaskStatistics(response,taskItemDto);
    }



    @ApiOperation(value = "导出子任务列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/系统任务/任务列表/导出子任务列表")
    @PostMapping("exportTaskItemList")
    public void exportTaskItemList(HttpServletResponse response, @RequestBody @Validated SearchBean<TaskItemDto> searchBean) {
        CommonUtil.ok(response);
        taskItemService.exportTaskItemList(response,searchBean.getData(), searchBean);
    }


    /**
     * 设置子任务完成状态
     * @param type
     * @param taskItemIds
     * @return
     */
    @ApiOperation(value = "批量操作子任务状态")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.EDIT, description = "办公中心/系统任务/任务列表/子任务设置完成")
    @PostMapping("/batchSetup")
    public ResponseBo batchSetup(@RequestParam(value = "type",required = true) Integer type, @RequestParam("taskItemIds")Set<Long> taskItemIds) {
        return taskItemService.batchSetup(type, taskItemIds);
    }

    @ApiOperation(value = "子任务设置完成")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.EDIT, description = "办公中心/系统任务/任务列表/子任务设置完成")
    @PostMapping("/setupFinish")
    public UpdateResponseBo<TaskItem> setupFinish(@RequestParam("taskItemId") Long taskItemId) {
        return taskItemService.setupFinish(taskItemId);
    }

    @ApiOperation(value = "子任务设置未能完成")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.EDIT, description = "办公中心/系统任务/任务列表/子任务设置未能完成")
    @PostMapping("/setupUnFinish")
    public UpdateResponseBo<TaskItem> setupUnFinish(@RequestParam("taskItemId") Long taskItemId) {
        return taskItemService.setupUnFinish(taskItemId);
    }

    /**
     * 批量设置反馈信息
     * @param taskItemIds
     * @param comment
     * @return
     */
    @ApiOperation(value = "批量新增子任务的评论")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.ADD, description = "办公中心/系统任务/任务列表/新增子任务的评论")
    @PostMapping("/batchAddTaskItemComment")
    public SaveResponseBo batchAddTaskItemComment(@RequestParam("taskItemIds") Set<Long> taskItemIds, @RequestParam("comment") String comment,@RequestParam("status")Integer status) {
        return taskItemService.batchAddTaskItemComment(taskItemIds, comment,status);
    }


    @ApiOperation(value = "新增子任务的评论")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.ADD, description = "办公中心/系统任务/任务列表/新增子任务的评论")
    @PostMapping("/addTaskItemComment")
    public SaveResponseBo addTaskItemComment(@RequestParam("taskItemId") Long taskItemId, @RequestParam("comment") String comment,@RequestParam("status")Integer status) {
        return taskItemService.addTaskItemComment(taskItemId, comment,status);
    }

    @ApiOperation(value = "更新子任务的评论")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.EDIT, description = "办公中心/系统任务/任务列表/更新子任务的评论")
    @PostMapping("/updateTaskItemComment")
    public SaveResponseBo updateTaskItemComment(@RequestParam("taskItemId") Long taskItemId,
                                                @RequestParam("commentId") Long commentId,
                                                @RequestParam("comment") String comment) {
        return taskItemService.updateTaskItemComment(taskItemId, commentId, comment);
    }

    @ApiOperation(value = "获取子任务评论列表")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/系统任务/任务列表/获取子任务评论列表")
    @PostMapping("/getTaskItemCommentList")
    public ListResponseBo<Comment> getTaskItemCommentList(@RequestBody SearchBean<TaskItemCommentDto> searchBean) {
        return taskItemService.getTaskItemCommentList(searchBean.getData().getTaskItemId(), searchBean);
    }

    @ApiOperation(value = "获取任务反馈统计列表")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/系统任务/任务列表/获取任务反馈统计列表")
    @PostMapping("/getTaskFeedbackStatistics")
    public ListResponseBo<CommentStatisticsVo> getTaskFeedbackStatistics(@RequestParam("taskId")Long taskId, @RequestParam( value = "typeKey",required = false)String typeKey) {
        return new ListResponseBo<>(taskItemService.getTaskFeedbackStatistics(taskId,typeKey));

    }

    @ApiOperation(value = "获取任务反馈统计列表总数")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/系统任务/任务列表/获取任务反馈统计列表总数")
    @PostMapping("/getTaskFeedbackStatisticsTotal")
    public ResponseBo<CommentStatisticsVo> getTaskFeedbackStatisticsTotal(@RequestParam("taskId")Long taskId, @RequestParam( value = "typeKey",required = false)String typeKey) {
        return new ResponseBo<>(taskItemService.getTaskFeedbackStatisticsTotal(taskId,typeKey));
    }

    @ApiOperation(value = "导出任务反馈统计表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/系统任务/任务列表/导出任务反馈统计表")
    @PostMapping("exportTaskFeedbackStatistics")
    public void exportTaskFeedbackStatistics(HttpServletResponse response,@RequestParam("taskId")Long taskId, @RequestParam( value = "typeKey",required = false)String typeKey ) {
        taskItemService.exportTaskFeedbackStatistics(response,taskId,typeKey);
    }




}
