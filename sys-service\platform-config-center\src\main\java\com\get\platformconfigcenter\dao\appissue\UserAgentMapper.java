package com.get.platformconfigcenter.dao.appissue;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.UserAgent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2022/3/7
 * @TIME: 14:53
 * @Description:
 **/
@Mapper
@DS("issuedb")
public interface UserAgentMapper extends BaseMapper<UserAgent> {
    /**
     * 根据代理获取用户ids
     *
     * @param fkAgentId
     * @param fkCompanyId
     * @return
     */
//    List<UserAgent> getUserIdsByAgent(@Param("fkAgentId") Long fkAgentId,@Param("fkCompanyId") Long fkCompanyId);
//
//    List<UserAgent> getUsersAgentList(@Param("fkAgentIds")Set<Long> fkAgentIds);
}
