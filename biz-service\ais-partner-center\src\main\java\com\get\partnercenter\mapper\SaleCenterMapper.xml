<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.partnercenter.mapper.SaleCenterMapper">
    <delete id="deleteMediaAndAttachedById">
        delete from s_media_and_attached where id = #{id}
    </delete>


    <select id="selectAgentContacts" resultType="com.get.partnercenter.vo.AgentContactVo">
        select p.id,p.name,p.email,t.type_name from s_contact_person p
        left join u_contact_person_type t on p.fk_contact_person_type_key = t.type_key
        where fk_table_name = 'm_agent'
          and fk_table_id =
            #{agentId}
    </select>


    <select id="selectEmailInfoByAgentId" resultType="com.get.partnercenter.dto.SendEmailInfoDto">
        select ma.id as agent_id,
        s.name as fromUser,
        s.email as fromEmail,
        s.email_password as emailPassword
        from m_agent ma
        INNER JOIN r_agent_staff ras ON ma.id = ras.fk_agent_id and ras.is_active = 1
        left join ais_permission_center.m_staff s on s.id = ras.fk_staff_id
        <where>
            ma.id = #{agentId}
        </where>
    </select>

    <select id="selectAgentList" resultType="com.get.partnercenter.vo.BdVo">
        select ma.id as agentId,
        ma.name as agentName,
        s.name,
        s.id as staffId,
        s.num,
        s.email,
        s.email_password,
        s.fk_company_id as companyId,
        s.login_id
        from m_agent ma
        INNER JOIN (
        <include refid="com.get.partnercenter.mapper.MPartnerUserMapper.agentPermissionSql"/>
        ) z ON ma.id=z.id
        inner join r_agent_staff ras on ma.id = ras.fk_agent_id and ras.is_active = 1
        inner join ais_permission_center.m_staff s on s.id = ras.fk_staff_id
        <where>
            <if test="agentName != null and agentName != ''">
                and ma.name like concat('%',#{agentName},'%')
            </if>
            <if test="keyword != null and keyword != ''">
                and (s.name like concat('%',#{keyword},'%') or s.num like
                concat('%',#{keyword},'%'))
            </if>
        </where>
        order by ma.id desc
    </select>
    <select id="selectEventFileArrays" resultType="com.get.partnercenter.vo.FileArray">

        SELECT
            mFileSle.id,
            mFileSle.file_guid AS fileGuid,
            CONCAT(#{mMageAddress}, mFileSle.file_key) AS fileKeyFile,
            mFileSle.file_name_orc
        from ais_sale_center.s_media_and_attached sAttached
                 INNER JOIN ais_file_center.m_file_sale mFileSle ON mFileSle.file_guid = sAttached.fk_file_guid
        WHERE sAttached.fk_table_name='m_event'
          AND  sAttached.type_key='m_event_partner_file'
          AND  sAttached.fk_table_id=#{id}

    </select>
    <select id="selectEventMediaAndAttached" resultType="com.get.salecenter.vo.MediaAndAttachedVo">
        SELECT
            smaa.*, mfs.file_key
        FROM
            ais_sale_center.s_media_and_attached AS smaa
                INNER JOIN ais_file_center.m_file_sale AS mfs ON mfs.file_guid = smaa.fk_file_guid
        WHERE
            smaa.fk_table_name = 'm_event'
          AND smaa.type_key = 'm_event_partner_logo'
          AND smaa.fk_table_id = #{fkTableId}
    </select>

    <select id="selectEventMediaAndAttachedList" resultType="com.get.salecenter.vo.MediaAndAttachedVo">
        SELECT
            smaa.*, mfs.file_key
        FROM
            ais_sale_center.s_media_and_attached AS smaa
                INNER JOIN ais_file_center.m_file_sale AS mfs ON mfs.file_guid = smaa.fk_file_guid
        WHERE
            smaa.fk_table_name = 'm_event'
          AND smaa.type_key = #{typeKey}
          AND smaa.fk_table_id = #{fkTableId}
    </select>

</mapper>
