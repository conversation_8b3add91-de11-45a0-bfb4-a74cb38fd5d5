package com.get.partnercenter.rocketmq.config;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.get.aisplatformcenterap.entity.MFeedbackOrderEntity;
import com.get.aisplatformcenterap.entity.MFeedbackOrderReplyEntity;
import com.get.partnercenter.mapper.MFeedbackOrderMapper;
import com.get.partnercenter.mapper.MFeedbackOrderReplyMapper;
import com.get.partnercenter.mqmessage.FeedCloseMessage;
import com.get.partnercenter.util.MyDateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;

@Slf4j
@RocketMQMessageListener(
        topic = "send_feed_close_message_topic", // topic
        consumerGroup = "send_feed_close_message_consumer", // 消费组
        maxReconsumeTimes = 3, //最大重试次数
        consumeMode = ConsumeMode.CONCURRENTLY // 并发消费-可选顺序消费
)
@Component
public class SendFeedCloseMessageConsumer  implements RocketMQListener<FeedCloseMessage> {

    @Resource
    MFeedbackOrderMapper mFeedbackOrderMapper;
    @Resource
    MFeedbackOrderReplyMapper mFeedbackOrderReplyMapper;


    @Override
    public void onMessage(FeedCloseMessage message) {
        log.info("收到反馈关闭消息，MQCommissionMessage={}", message);

        try{

            long seventyTwoHoursAgo = Instant.now().minus(72, ChronoUnit.HOURS).toEpochMilli();
            List<MFeedbackOrderEntity> mfeedbackorderArr= mFeedbackOrderMapper.selectList(new LambdaQueryWrapper<MFeedbackOrderEntity>()
                    .eq(MFeedbackOrderEntity::getStatus,1)
                    .lt(MFeedbackOrderEntity::getGmtCreate,seventyTwoHoursAgo)
            );
            if(ObjectUtils.isNotEmpty(mfeedbackorderArr)){
                for(MFeedbackOrderEntity orderEntity:mfeedbackorderArr){
                    try{
                        MFeedbackOrderReplyEntity orderReplyEntity= mFeedbackOrderReplyMapper.selectOne(new LambdaQueryWrapper<MFeedbackOrderReplyEntity>()
                                .eq(MFeedbackOrderReplyEntity::getFkFeedbackOrderId,orderEntity.getId())
                                .orderByDesc(MFeedbackOrderReplyEntity::getGmtCreate).last("limit 1")
                        );
                        if(ObjectUtils.isNotEmpty(orderReplyEntity.getFkStaffId())){
                            continue;
                        }
                        Date gmtCreate= orderReplyEntity.getGmtCreate();
                        if(MyDateUtils.isOlderThan72Hours(gmtCreate)){
                            orderEntity.setStatus(3);
                            orderEntity.setGmtModified(new Date());
                            orderEntity.setGmtModifiedUser("system");
                            mFeedbackOrderMapper.updateById(orderEntity);
                        }
                    }catch (Exception e){
                        log.error("反馈关闭异常{} {}",e.getMessage(),orderEntity.getId());
                    }
                }

            }

        }catch (Exception e){
            log.error("反馈关闭异常{}",e.getMessage());
        }









    }


}
