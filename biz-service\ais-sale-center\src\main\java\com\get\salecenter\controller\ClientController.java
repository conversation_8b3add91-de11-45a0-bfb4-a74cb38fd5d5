package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.CommonUtil;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.dto.*;
import com.get.salecenter.service.IClientService;
import com.get.salecenter.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * author:Neil
 * Time: 12:50
 * Date: 2022/8/17
 * Description: 售前客户管理
 */
@Api(tags = "学生资源管理")
@RestController
@RequestMapping("sale/client")
public class ClientController {

    @Resource
    private IClientService clientService;

    @ApiOperation(value = "资源列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生资源管理/资源列表")
    @PostMapping("datas")
    public ResponseBo<ClientListInfoDto> datas(@RequestBody SearchBean<ClientDto> searchBean){
        return clientService.getListOfPreSalesResources(searchBean.getData(),searchBean);
    }

    @ApiOperation(value = "资源列表导出", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生资源管理/资源列表导出")
    @PostMapping("clientResourcesExport")
    public void clientResourcesExport(@RequestBody ClientSourceExportDto clientSourceExportDto, HttpServletResponse response){
        CommonUtil.ok(response);
        clientService.clientResourcesExport(clientSourceExportDto.getClientVo(), clientSourceExportDto.getFocExportVos(),response);
    }

    /**
     * 获取当前用户的下属selectList
     */
    @ApiOperation(value = "获取当前用户的下属selectList", notes = "")
    @GetMapping("getSubordinatesOfTheCurrentUserSelect")
    @VerifyPermission( IsVerify = false)
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生资源管理/当前用户的下属selectList")
    public ResponseBo<BaseSelectEntity> getObtainSubordinateEmployeesSelect(@RequestParam("companyIds") List<Long> companyIds) {
        return new ListResponseBo<>(clientService.getSubordinatesOfTheCurrentUserSelect(companyIds));
    }


    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "导出资源列表信息Excel表头选项")
    @GetMapping("/getClientOptions")
    public ListResponseBo<FocExportVo> getClientOptions() throws Exception {
        return new ListResponseBo<>(clientService.getClientOptions());
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "跟进状态下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/客户管理/跟进状态下拉")
    @GetMapping("getFollowUpStatusList")
    public ResponseBo<Map<String,Object>> getFollowUpStatusList(){
        return clientService.getFollowUpStatusList();
    }



    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [clientAddDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/客户管理/新增客户")
    @PostMapping("add")
    public ResponseBo add(@RequestBody ClientAddDto clientAddDto) {
        return SaveResponseBo.ok(clientService.addClient(clientAddDto));
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ClientVo>
     * @Description: 修改信息
     * @Param [clientUpdateDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/客户管理/更新客户")
    @PostMapping("update")
    public ResponseBo<ClientVo> update(@RequestBody ClientUpdateDto clientUpdateDto) {
        return UpdateResponseBo.ok(clientService.updateClient(clientUpdateDto));
    }

    /**
     * @Description: 禁用激活学生资源
     * <AUTHOR>
     */
    @ApiOperation(value = "激活禁用", notes = "isActive: true：激活 false：禁用")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/客户管理/激活禁用")
    @PostMapping("updateActive")
    public ResponseBo<ClientVo> updateActive(@RequestBody ClientUpdateDto clientUpdateVo) {
        clientService.updateActive(clientUpdateVo);
        return UpdateResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.dto.StudentDto>
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "客户详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/客户管理/客户详情")
    @GetMapping("/{id}")
    public ResponseBo<ClientVo> detail(@PathVariable("id") Long id) {
        ClientVo data = clientService.findClientById(id);
        return new ResponseBo<>(data);
    }

    @ApiOperation(value = "设置签约时间")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/客户管理/设置签约时间")
    @PostMapping("updateExpectSigningTime")
    public ResponseBo updateExpectSigningTime(@RequestBody ClientExpectSigningDto clientExpectSigningDto){
        clientService.updateExpectSigningTime(clientExpectSigningDto);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "设置客户星级")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/客户管理/设置客户星级")
    @PostMapping("updateStarLevel")
    public ResponseBo updateStarLevel(@RequestBody ClientStarLevelDto clientStarLevelDto){
        clientService.updateStarLevel(clientStarLevelDto);
        return ResponseBo.ok();
    }

    /**
     * 删除客户信息
     * @param id
     * @return
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生管理/删除客户信息")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        clientService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @ Description :
     * @ Param [studentName, value, type]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation("检测是否存在相同咨询客户")
    @GetMapping("getIsExistClient")
    @VerifyPermission(IsVerify = false)
    public ResponseBo getIsExistStudent(@RequestParam("clientName") String clientName,
                                        @RequestParam("companyId") Long companyId,
                                        @RequestParam(value = "id", required = false) Long id,
                                        @RequestParam(value = "email", required = false) String email,
                                        @RequestParam(value = "mobile", required = false) String mobile,
                                        @RequestParam(value = "passpost", required = false) String passpost,
                                        @RequestParam(value = "birthday", required = false) String birthday) {
        Map<String, String> map = clientService.getIsExistStudent(clientName, email, mobile, passpost, birthday, id, companyId);
        return new ResponseBo(map);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 查询咨询客户附件
     * @Param [voSearchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "查询咨询客户附件", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/咨询客户管理/查询附件")
    @PostMapping("getClientMedia")
    public ResponseBo<MediaAndAttachedVo> getClientMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> clientMedia = clientService.getMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(clientMedia, page);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 保存咨询客户附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "保存咨询客户附件")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/咨询客户管理/附件保存接口")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> addMedia(@RequestBody @Validated(MediaAndAttachedDto.Add.class) ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(clientService.addMedia(mediaAttachedVo));
    }

    /**
     * @param commentDto
     * @return
     * @
     */
    @ApiOperation(value = "编辑评论")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/咨询客户管理/编辑评论")
    @PostMapping("editComment")
    public ResponseBo editComment(@RequestBody  @Validated(CommentDto.Add.class) CommentDto commentDto) {
        return SaveResponseBo.ok(clientService.editComment(commentDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AgentContractVo>
     * @Description: 评论列表数据
     * @Param [searchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "评论列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/咨询客户管理/查询")
    @PostMapping("getComments")
    public ResponseBo<CommentVo> getComment(@RequestBody SearchBean<CommentDto> searchBean) {
        List<CommentVo> datas = clientService.getComments(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "查询客户审批列表")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/咨询客户管理/查询客户审批列表")
    @PostMapping("getClientApprovalList")
    public ResponseBo<ClientApprovalVo> getClientApprovalList(@RequestBody SearchBean<ClientApprovalDto> searchBean) {
        List<ClientApprovalVo> datas = clientService.getClientApprovalList(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "修改审批状态")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/咨询客户管理/修改审批状态")
    @PostMapping("upateApprovalStatus")
    public ListResponseBo<String> upateApprovalStatus(@RequestBody ApprovalStatusDto approvalStatusDto) {

        return new ListResponseBo<>(clientService.upateApprovalStatus(approvalStatusDto));
    }


    @ApiOperation(value = "获取相同学生")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/咨询客户管理/获取相同学生")
    @PostMapping("getSameStudentClients")
    public ListResponseBo<SameStudentClientVo> getSameStudentClients(@RequestBody SameStudentClientDto sameStudentClientDto) {
        return new ListResponseBo<>(clientService.getSameStudentClients(sameStudentClientDto));
    }


    @ApiOperation(value = "申请绑定")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/咨询客户管理/申请绑定")
    @PostMapping("bindingStudentClient")
    public ResponseBo<ClientVo> bindingStudentClient(@RequestBody @Validated BindingStudentClientDto bindingStudentClientDto) {
        return new ResponseBo(clientService.bindingStudentClient(bindingStudentClientDto));
    }

    @ApiOperation(value = "创建业务学生")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/咨询客户管理/创建业务学生")
    @PostMapping("createBusinessStudent")
    public ResponseBo createBusinessStudent(@RequestBody CreateBusinessStudentDto createBusinessStudentDto) {
        return SaveResponseBo.ok(clientService.createBusinessStudent(createBusinessStudentDto));
    }

    @ApiOperation(value = "获取业务学生")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/咨询客户管理/获取业务学生")
    @GetMapping("getBusinessStudent")
    public ListResponseBo<BusinessStudentVo> getBusinessStudent(@RequestParam("fkClientId") Long fkClientId) {
        return new ListResponseBo<>(clientService.getBusinessStudent(fkClientId));
    }



    @ApiOperation(value = "绑定业务学生")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/咨询客户管理/绑定业务学生")
    @PostMapping("boundBusinessStudent")
    public ResponseBo boundBusinessStudent(@RequestBody @Validated BoundBusinessStudentDto boundBusinessStudentDto) {
        clientService.boundBusinessStudent(boundBusinessStudentDto);
        return UpdateResponseBo.ok();
    }

    @ApiOperation(value = "取消绑定业务学生")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/咨询客户管理/取消绑定业务学生")
    @PostMapping("unbindBusinessStudent")
    public ResponseBo unbindBusinessStudent(@RequestBody Set<Long> studentIds) {
        clientService.unbindBusinessStudent(studentIds);
        return UpdateResponseBo.ok();
    }

}
