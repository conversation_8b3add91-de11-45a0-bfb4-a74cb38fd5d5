package com.get.salecenter.dto.query;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

@Data
public class UEventThemeQueryDto extends BaseVoEntity {

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    /**
     * 活动主题
     */
    @ApiModelProperty(value = "活动主题")
    @Column(name = "event_theme")
    private String eventTheme;
}
