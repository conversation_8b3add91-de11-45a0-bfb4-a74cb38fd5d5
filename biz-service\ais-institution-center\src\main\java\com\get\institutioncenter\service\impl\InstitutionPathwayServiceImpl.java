package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.InstitutionMapper;
import com.get.institutioncenter.dao.InstitutionPathwayMapper;
import com.get.institutioncenter.dao.InstitutionTypeMapper;
import com.get.institutioncenter.vo.InstitutionVo;
import com.get.institutioncenter.vo.InstitutionPathwayVo;
import com.get.institutioncenter.entity.Institution;
import com.get.institutioncenter.entity.InstitutionPathway;
import com.get.institutioncenter.service.IDeleteService;
import com.get.institutioncenter.service.IInstitutionPathwayService;
import com.get.institutioncenter.service.IInstitutionService;
import com.get.institutioncenter.dto.InstitutionPathwayDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/12/17
 * @TIME: 17:54
 * @Description:
 **/
@Service
public class InstitutionPathwayServiceImpl extends BaseServiceImpl<InstitutionPathwayMapper, InstitutionPathway> implements IInstitutionPathwayService {
    @Resource
    private InstitutionPathwayMapper institutionPathwayMapper;
    @Resource
    private IInstitutionService institutionService;
    @Resource
    private UtilService utilService;
    @Resource
    private InstitutionTypeMapper institutionTypeMapper;
    @Resource
    private InstitutionMapper institutionMapper;
    @Resource
    private IDeleteService deleteService;

    @Override
    public List<InstitutionPathwayVo> datas(InstitutionPathwayDto institutionPathwayDto, Page page) {
        //获取分页数据
        LambdaQueryWrapper<InstitutionPathway> wrapper = new LambdaQueryWrapper();
        IPage<InstitutionPathway> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<InstitutionPathway> institutionPathways = institutionPathwayMapper.selectInstitutionPathway(pages, institutionPathwayDto.getFkInstitutionId(),
                institutionPathwayDto.getFkInstitutionIdPathway());
        page.setAll((int) pages.getTotal());
        List<InstitutionPathwayVo> convertDatas = new ArrayList<>();
        for (InstitutionPathway institutionPathway : institutionPathways) {
            InstitutionVo institutionVo = institutionService.findInstitutionById(
                    GeneralTool.isNotEmpty(institutionPathwayDto.getFkInstitutionIdPathway()) ?
                            institutionPathway.getFkInstitutionId() : institutionPathway.getFkInstitutionIdPathway());
            InstitutionPathwayVo institutionPathwayVo = new InstitutionPathwayVo();
            institutionPathwayVo.setInstitutionDto(institutionVo);
            institutionPathwayVo.setId(institutionPathway.getId());
            convertDatas.add(institutionPathwayVo);
        }
        return convertDatas;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (institutionPathwayMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionPathway institutionPathway = institutionPathwayMapper.selectById(id);
        if (GeneralTool.isEmpty(institutionPathway)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        deleteService.deleteValidatePathwayCourse(institutionPathway);
        institutionPathwayMapper.deleteById(id);
    }

    @Override
    public void update(InstitutionPathwayDto institutionPathwayDto) {
        if (GeneralTool.isEmpty(institutionPathwayDto.getFkInstitutionId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Institution institution = institutionMapper.selectById(institutionPathwayDto.getFkInstitutionId());
        if (GeneralTool.isEmpty(institution)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (institutionTypeMapper.pathwayInstitutionIsEmpty(institution.getFkInstitutionTypeId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("bridge_school_cannot_bound_bridge_school"));
        }
        if (GeneralTool.isEmpty(institutionPathwayDto.getFkInstitutionIdPathways())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("bridge_schoolId_empty"));
        }
        for (Long id : institutionPathwayDto.getFkInstitutionIdPathways()) {
            InstitutionPathway institutionPathway = new InstitutionPathway();
            institutionPathway.setFkInstitutionId(institutionPathwayDto.getFkInstitutionId());
            institutionPathway.setFkInstitutionIdPathway(id);
            utilService.updateUserInfoToEntity(institutionPathway);
            institutionPathwayMapper.insertSelective(institutionPathway);
        }
    }

    /**
     * 桥梁绑定非桥梁学校
     *
     * @Date 16:44 2021/7/22
     * <AUTHOR>
     */
    @Override
    public void pathwayUpdate(InstitutionPathwayDto institutionPathwayDto) {
        if (GeneralTool.isEmpty(institutionPathwayDto.getFkInstitutionIdPathway())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Institution pathwayInstitution = institutionMapper.selectById(institutionPathwayDto.getFkInstitutionIdPathway());
        if (GeneralTool.isEmpty(pathwayInstitution)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (GeneralTool.isEmpty(institutionPathwayDto.getFkInstitutionIds())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("institutionId_null"));
        }
        for (Long id : institutionPathwayDto.getFkInstitutionIds()) {
            Institution institution = institutionMapper.selectById(institutionPathwayDto.getFkInstitutionIdPathway());
            if (GeneralTool.isEmpty(institution)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            if (institutionTypeMapper.pathwayInstitutionIsEmpty(institution.getFkInstitutionTypeId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("bridge_school_cannot_bound_bridge_school"));
            }
            InstitutionPathway institutionPathway = new InstitutionPathway();
            institutionPathway.setFkInstitutionId(id);
            institutionPathway.setFkInstitutionIdPathway(institutionPathwayDto.getFkInstitutionIdPathway());
            utilService.updateUserInfoToEntity(institutionPathway);
            institutionPathwayMapper.insertSelective(institutionPathway);
        }
    }

    @Override
    public List<BaseSelectEntity> getBridgeInstitutionSelect(Long id) {
        return institutionPathwayMapper.getBridgeInstitutionSelect(id);
    }


}
