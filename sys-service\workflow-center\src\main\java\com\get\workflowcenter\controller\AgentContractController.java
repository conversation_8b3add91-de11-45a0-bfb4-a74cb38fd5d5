package com.get.workflowcenter.controller;

//import com.get.common.annotion.VerifyPermission;
//import com.get.common.entity.StaffContext;
//import com.get.common.entity.fegin.StaffVo;
//import com.get.common.exception.YException;
//import com.get.common.exception.GetServiceException;

import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.vo.AgentContractVo;
import com.get.workflowcenter.service.IAgentContractService;
import com.get.workflowcenter.service.IWorkFlowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.activiti.bpmn.model.BpmnModel;
import org.activiti.bpmn.model.FlowNode;
import org.activiti.bpmn.model.SequenceFlow;
import org.activiti.engine.HistoryService;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.task.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/4/15 14:44
 */

@Api(tags = "合同流程")
@RestController
@RequestMapping("workflow/contractFlow")
@Slf4j
public class AgentContractController {
    @Autowired
    private RepositoryService repositoryService;
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private ISaleCenterClient saleCenterClient;
    @Autowired
    private IAgentContractService iAgentContractService;
    @Autowired
    private IWorkFlowService iWorkFlowService;

    /**
     * @ Description :合同流程开始
     * @ Param [businessKey, procdefKey, companyId]
     * @ return java.lang.Boolean
     * @ author LEO
     */
    @ApiIgnore
    @ApiOperation("合同流程开始")
    @PostMapping("/startContractFlow")
    public Boolean startContractFlow(@RequestParam("businessKey") String businessKey,
                                     @RequestParam("procdefKey") String procdefKey,
                                     @RequestParam("companyId") String companyId) {
//        if (GeneralTool.isEmpty(businessKey) || GeneralTool.isEmpty(procdefKey) || GeneralTool.isEmpty(companyId)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
//        }
////        StaffVo staff = StaffContext.getStaff();
//        String username = String.valueOf(GetAuthInfo.getStaffId());
//        Authentication.setAuthenticatedUserId(username);
//        com.get.salecenter.vo.AgentContractVo agentContractById = null;
//        Result<com.get.salecenter.vo.AgentContractVo> result = saleCenterClient.getAgentContractById(Long.valueOf(businessKey));
//        if(result.isSuccess() && GeneralTool.isNotEmpty(result.getData()))
//        {
//            agentContractById = result.getData();
//        }
//        if (agentContractById == null || agentContractById.getStatus() != 0 || agentContractById.getStatus() == 5) {
//            return false;
//        }
//        AgentContractVo agentContract = BeanCopyUtils.objClone(agentContractById, AgentContractVo::new);
//        Map<String, Object> map = new HashMap<>();
//        map.put("userid", username);
//        map.put("companyId", companyId);
//        map.put("tableName", "m_agent_contract");
//        map.put("businessKey", agentContract.getId());
//        map.put("agentContract", agentContract);
//        try {
//
//            //表名 +公司id
//            List<ProcessDefinition> processDefinition =
//                    repositoryService.createProcessDefinitionQuery().processDefinitionKey(procdefKey).processDefinitionTenantId(companyId).orderByProcessDefinitionVersion().desc().list();
//            String id = "";
//            for (ProcessDefinition pdid : processDefinition) {
//                id = pdid.getId();
//                break;
//            }
//
//            ProcessInstance processInstance = runtimeService.startProcessInstanceById(id, String.valueOf(agentContract.getId()), map);
////            AgentContractVo agentContractDto = saleCenterClient.getAgentContractById(Long.valueOf(businessKey));
//            Result<com.get.salecenter.vo.AgentContractVo> agentContractDtoResult = saleCenterClient.getAgentContractById(Long.valueOf(businessKey));
//            if(agentContractDtoResult.isSuccess() && agentContractDtoResult.getData()!=null)
//            {
//                com.get.salecenter.vo.AgentContractVo agentContractDto = agentContractDtoResult.getData();
//                if (agentContractDto.getStatus() == 0) {
//                    agentContract.setStatus(2);
//                    com.get.salecenter.entity.AgentContract agentContract_ = BeanCopyUtils.objClone(agentContract,com.get.salecenter.entity.AgentContract::new);
//                    saleCenterClient.updateChangeStatus(agentContract_);
//                }
//            }
//            return true;
//        } catch (Exception e) {
//            e.printStackTrace();
//            return false;
//        }
        return iAgentContractService.startContractFlow(businessKey, procdefKey, companyId);
    }

    /**
     * @ Description :审批
     * @ Param [status, taskId, msg]
     * @ return void
     * @ author LEO
     */
    @ApiOperation("审批")
    @GetMapping("/getExamineAndApprove")
    public ResponseBo getExamineAndApprove(@RequestParam("status") String status, @RequestParam("taskId") String taskId,
                                           @RequestParam("procInstId") String procInstId, @RequestParam("msg") String msg) {

        if (GeneralTool.isEmpty(status) || GeneralTool.isEmpty(taskId) || GeneralTool.isEmpty(msg) || GeneralTool.isEmpty(procInstId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
//        StaffVo staff = StaffContext.getStaff();
        String username = String.valueOf(SecureUtil.getStaffId());
        Authentication.setAuthenticatedUserId(username);
        taskService.addComment(taskId, procInstId, msg);
        Map<String, Object> map = new HashMap<>();
        map.put("sequenceFlowsStatus", status);
        if ("0".equals(status)) {
            taskService.setVariableLocal(taskId, "approvalAction", 0);
        } else {
            taskService.setVariableLocal(taskId, "approvalAction", 1);
        }
        taskService.complete(taskId, map);
        iWorkFlowService.getStatusToDoSingle(status, procInstId);

        return ResponseBo.ok();
    }

    /**
     * @ Description :查询表单所有任务task版本
     * @ Param [businessKey]
     * @ return com.get.workflowcenter.vo.ActRuTaskVo
     * @ author LEO
     */
    @ApiIgnore
    @ApiOperation("查询表单所有任务task版本")
    @GetMapping("getContractTaskDataByBusinessKey")
    public ActRuTaskVo getContractTaskDataByBusinessKey(@RequestParam("businessKey") String businessKey,
                                                        @RequestParam("procdefKey") String key) {
        return iAgentContractService.getTaskDataByBusinessKey(businessKey, key);
    }

    /**
     * @ Description :显示合同流程流转信息
     * @ Param [businessKey]
     * @ return com.get.common.result.ResponseBo<com.get.workflowcenter.vo.PaymentApplicationFormVo>
     * @ author LEO
     */
    @ApiOperation("显示合同流程流转信息")
    @GetMapping("/getPayFlowData")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<AgentContractVo> getPayFlowData(@RequestParam("businessKey") String businessKey, @RequestParam("procdefKey") String key) throws GetServiceException {
        List<AgentContractVo> mpayDtos = iAgentContractService.getPayFlowData(businessKey, key);
        ListResponseBo<AgentContractVo> mpayDtoListResponseBo = new ListResponseBo<>(mpayDtos);
        return mpayDtoListResponseBo;
    }

    /**
     * @ Description :getSignOrGet
     * @ Param [taskId, version]
     * @ return int
     * @ author LEO
     */
    @ApiIgnore
    @ApiOperation("判断去待签还是代表页面，0待签，1待办")
    @GetMapping("getSignOrGet")
    public int getSignOrGet(@RequestParam(required = false, value = "taskId") String taskId,
                            @RequestParam(required = false, value = "version") Integer version) {
        int signOrGet = iAgentContractService.getSignOrGet(taskId, version);
        return signOrGet;
    }

    /**
     * @ Description : 审批
     * @ Param [taskId, status]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiIgnore
    @GetMapping("getContractUserSubmit")
    public ResponseBo getContractUserSubmit(@RequestParam("taskId") String taskId, @RequestParam("status") String status) {
        HashMap<String, Object> sequenceFlowsStatus = new HashMap<>();
        //0放弃
        if ("0".equals(status)) {
            sequenceFlowsStatus.put("sequenceFlowsStatus", 0);
            taskService.setVariableLocal(taskId, "approvalAction", 0);
            taskService.complete(taskId, sequenceFlowsStatus);
            return ResponseBo.ok();
        }
        taskService.setVariableLocal(taskId, "approvalAction", 1);
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        List<HistoricActivityInstance> historicActivityInstanceQuery = historyService.createHistoricActivityInstanceQuery().processInstanceId(task.getProcessInstanceId()).list();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
        for (HistoricActivityInstance historicActivityInstance : historicActivityInstanceQuery) {

            FlowNode flowElement = (FlowNode) bpmnModel.getMainProcess().getFlowElement(historicActivityInstance.getActivityId());
            List<SequenceFlow> incomingFlows = flowElement.getIncomingFlows();
            for (SequenceFlow sequenceFlow : incomingFlows) {
                if ("vp".equals(sequenceFlow.getTargetRef())) {
                    sequenceFlowsStatus.put("sequenceFlowsStatus", 2);
                    taskService.complete(taskId, sequenceFlowsStatus);

                } else if ("BD".equals(sequenceFlow.getTargetRef())) {
                    sequenceFlowsStatus.put("sequenceFlowsStatus", 1);
                    taskService.complete(taskId, sequenceFlowsStatus);
                }

            }

        }
        return ResponseBo.error();
    }

}