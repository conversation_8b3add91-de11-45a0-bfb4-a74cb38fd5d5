package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_user_agent")
public class NewIssueUserAgent extends BaseEntity implements Serializable {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    /**
     * 注册中心的user id
     */
    @ApiModelProperty(value = "注册中心的user id")
    @Column(name = "fk_user_id")
    private Long fkUserId;

    /**
     * 代理id（业务代理id）
     */
    @ApiModelProperty(value = "代理id（业务代理id）")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;

    /**
     * 角色类型：0普通用户/1运营管理员
     */
    @ApiModelProperty(value = "角色类型：0普通用户/1运营管理员")
    @Column(name = "role_type")
    private Integer roleType;

    /**
     * 是否允许查看子代理数据：0否/1是
     */
    @ApiModelProperty(value = "是否允许查看子代理数据：0否/1是")
    @Column(name = "is_subagent_permission")
    private Boolean isSubagentPermission;

    /**
     * 是否允许查看BMS数据：0否/1是
     */
    @ApiModelProperty(value = "是否允许查看BMS数据：0否/1是")
    @Column(name = "is_bms_permission")
    private Boolean isBmsPermission;

    /**
     * 是否允许查看申请状态（默认否）：0否/1是
     */
    @ApiModelProperty(value = "是否允许查看申请状态（默认否）：0否/1是")
    @Column(name = "is_show_app_status_permission")
    private Boolean isShowAppStatusPermission;

    private static final long serialVersionUID = 1L;
}