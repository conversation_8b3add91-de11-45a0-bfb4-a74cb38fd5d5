package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class PublicReceiptFormDetailVo {

    @ApiModelProperty("币种名称")
    private String receiptCurrencyTypeName;
    @ApiModelProperty("实收金额")
    private BigDecimal actualReceiptAmount;

    @ApiModelProperty("收款时间")
    private Date actualReceiptTime;

    private Long id;
}
