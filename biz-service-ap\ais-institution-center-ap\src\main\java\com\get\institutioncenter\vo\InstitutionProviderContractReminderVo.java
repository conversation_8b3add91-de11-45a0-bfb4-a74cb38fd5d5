package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import lombok.Data;

import java.util.Date;
@Data
public class InstitutionProviderContractReminderVo extends BaseVoEntity {
    //学校提供商id
    private Long id;

    //学校提供商英文名
    private String name;

    //学校提供商合同id
    private Long contractId;

    //合同最新审批人id
    private Long fkStaffId;

    //合同名称
    private String contractName;

    //学校提供商中文名
    private String nameChn;

    //学校提供商名称
    private String fullName;


    //合同开始时间
    private Date startTime;

    //合同结束时间
    private Date endTime;

    //合同创建人id
    private Long gmtCreateUserId;

    //提前提醒天数
    private Integer count;


}
