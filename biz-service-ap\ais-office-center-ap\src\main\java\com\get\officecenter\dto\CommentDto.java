package com.get.officecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author:cream
 * @Date: 2023/5/30  12:32
 */
@Data
public class CommentDto extends BaseVoEntity {

    @ApiModelProperty("备注")
    @NotBlank(message = "评论内容不能为空",groups = {Add.class,Update.class})
    private String comment;


    @ApiModelProperty("表名称")
    private String fkTableName;

    @ApiModelProperty("表id")
    private Long fkTableId;
}
