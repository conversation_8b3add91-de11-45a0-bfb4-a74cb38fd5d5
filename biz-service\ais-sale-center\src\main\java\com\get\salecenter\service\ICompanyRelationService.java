package com.get.salecenter.service;


import com.get.salecenter.vo.CompanyTreeVo;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/10/23
 * @TIME: 10:26
 * @Description: 公司安全配置
 **/
public interface ICompanyRelationService {


    /**
     * @return java.util.List<com.get.salecenter.vo.CompanyTreeVo>
     * @Description: 代理合同和公司的关系（数据回显）
     * @Param [contractId]
     * <AUTHOR>
     */
    List<CompanyTreeVo> getContractCompanyRelation(Long contractId);


    /**
     * @return java.util.List<com.get.salecenter.vo.CompanyTreeVo>
     * @Description: 代理和公司的关系（数据回显）
     * @Param [agentId]
     * <AUTHOR>
     */
    List<CompanyTreeVo> getAgentCompanyRelation(Long agentId);


    /**
     * @return java.util.List<com.get.salecenter.vo.CompanyTreeVo>
     * @Description: 代理联系人和公司的关系（数据回显）
     * @Param [agentId]
     * <AUTHOR>
     */
    List<CompanyTreeVo> getContactCompanyRelation(Long contactId);

}
