package com.get.votingcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_voting")
public class Voting extends BaseEntity{
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;
    /**
     * 主题名称
     */
    @ApiModelProperty(value = "主题名称")
    @Column(name = "theme_name")
    private String themeName;
    /**
     * 主题Key（系统唯一Key，外部功能模块接入时使用）
     */
    @ApiModelProperty(value = "主题Key（系统唯一Key，外部功能模块接入时使用）")
    @Column(name = "theme_key")
    private String themeKey;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Column(name = "description")
    private String description;
}