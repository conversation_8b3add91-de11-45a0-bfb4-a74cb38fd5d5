package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.permissioncenter.vo.DepartmentAndStaffVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/5/9 12:01
 * @verison: 1.0
 * @description:
 */
@Data
public class EventBillUpdateDto  extends BaseVoEntity implements Serializable {
    /**
     * 公司Id
     */
    @NotNull(
            message = "公司Id不能为空！",
            groups = {Add.class, Update.class}
    )
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    /**
     * 学校供应商Id
     */
    @NotNull(
            message = "学校供应商Id不能为空！",
            groups = {Add.class, Update.class}
    )
    @ApiModelProperty(value = "学校供应商Id")
    @Column(name = "fk_institution_provider_id")
    private Long fkInstitutionProviderId;

    /**
     * 活动年份
     */
    @ApiModelProperty(value = "活动年份")
    @Column(name = "event_year")
    private Integer eventYear;

    /**
     * invoice币种
     */
    @NotBlank(
            message = "发起invoice币种不能为空！",
            groups = {Add.class, Update.class}
    )
    @ApiModelProperty(value = "invoice币种")
    @Column(name = "fk_currency_type_num_invoice")
    private String fkCurrencyTypeNumInvoice;

    /**
     * invoice金额
     */
    @ApiModelProperty(value = "invoice金额")
    @Column(name = "invoice_amount")
    private BigDecimal invoiceAmount;

    /**
     * 活动费币种
     */
    @NotBlank(
            message = "活动费币种不能为空！",
            groups = {Add.class, Update.class}
    )
    @ApiModelProperty(value = "活动费币种")
    @Column(name = "fk_currency_type_num_event")
    private String fkCurrencyTypeNumEvent;

    /**
     * 活动费金额
     */
    @NotNull(
            message = "活动费金额不能为空！",
            groups = {Add.class, Update.class}
    )
    @ApiModelProperty(value = "活动费金额")
    @Column(name = "event_amount")
    private BigDecimal eventAmount;


    /**
     * invoice名目
     */
    @NotBlank(
            message = "invoice名目不能为空！",
            groups = {Add.class, Update.class}
    )
    @ApiModelProperty(value = "invoice名目")
    private String invoiceSummary;

    /**
     * invoice收件人
     */
    @ApiModelProperty(value = "invoice收件人")
    private String invoiceContactPerson;

    /**
     * invoice收件人Email
     */
    @NotBlank(
            message = "invoice收件人Email不能为空！",
            groups = {Add.class, Update.class}
    )
    @ApiModelProperty(value = "invoice收件人Email")
    private String invoiceContactEmail;

    /**
     * 摘要Ids
     */
    @ApiModelProperty(value = "摘要Ids")
    private List<Long> fkEventSummaryIdList;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

//    /**
//     * 应收计划Id
//     */
//    @ApiModelProperty(value = "应收计划Id")
//    @Column(name = "fk_receivable_plan_id")
//    private Long fkReceivablePlanId;

    /**
     * 发票编号
     */
    @ApiModelProperty(value = "发票编号")
    @Column(name = "fk_invoice_num")
    private String fkInvoiceNum;

//    @ApiModelProperty(value = "通知人多选")
//    private List<Long> fkStaffIdNoticeList;

    @ApiModelProperty("通知人多选")
    private List<DepartmentAndStaffVo> departmentAndStaffDtos;


    @ApiModelProperty(value = "业务国家多选")
    private List<Long> fkAreaCountryIdList;

    /**
     * 状态：0作废/1打开
     */
    @ApiModelProperty(value = "状态：0作废/1打开")
    private Integer status;

    @ApiModelProperty("批量分配活动金额")
    private List<EventCostDto> eventCostVos;

    @ApiModelProperty(value = "应收计划Ids")
    private List<Long> fkReceivablePlanIds;

  

}
