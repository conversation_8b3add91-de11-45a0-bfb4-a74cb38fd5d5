package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.platformconfigcenter.entity.UserInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: Hardy
 * @create: 2021/11/10 16:09
 * @verison: 1.0
 * @description:
 */
@Data
public class StudentOfferItemListVo extends BaseVoEntity {
    @Id
    @ApiModelProperty("id，主键")
    private Long id;

    @ApiModelProperty(value = "申请计划编号")
    private String studentOfferItemNum;

    @ApiModelProperty("agentIdGea")
    private String agentIdGea;
    /**
     * 记录总名称
     */
    @ApiModelProperty(value = "记录总名称")
    private String targetNames;
    /**
     * 申请方案编号
     */
    @ApiModelProperty(value = "申请方案编号")
    private String studentOfferNum;

    /**
     * 申请方案项目编号
     */
    @ApiModelProperty(value = "申请方案项目编号")
    private String num;

    /**
     * 目标类型
     */
    @ApiModelProperty(value = "目标类型名称")
    private String fkTypeKey;


    @ApiModelProperty("学号")
    private String idNumer;
    /**
     * 目标类型名称
     */
    @ApiModelProperty(value = "目标类型名称")
    private String fkTypeKeyName;

    /**
     * 目标名称
     */
    @ApiModelProperty(value = "目标名称")
    private String targetName;

    /**
     * 学校提供商渠道Id
     */
    @ApiModelProperty(value = "学校提供商渠道Id")
    private Long fkInstitutionChannelId;

    /**
     * 学校提供商Id
     */
    @ApiModelProperty(value = "学校提供商Id")
    private Long fkInstitutionProviderId;

    /**
     * 学校提供商名称
     */
    @ApiModelProperty(value = "学校提供商名称")
    private String fkInstitutionProviderName;

    /**
     * 申请步骤名
     */
    @ApiModelProperty(value = "申请步骤名")
    private String stepName;


    /**
     * 学生姓名
     */
    @ApiModelProperty(value = "学生姓名")
    private String studentName;

    /**
     * 学生id
     */
    @ApiModelProperty(value = "学生id")
    private Long fkStudentId;

    @ApiModelProperty(value = "学生号")
    private String studentId;


    /**
     * 学生生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "学生生日")
    private Date birthday;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "学生生日")
    private String birthdayStr;

    @ApiModelProperty(value = "学生编号")
    private String studentNum;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String institutionFullName;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称")
    private String courseFullName;

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    /**
     * 父课程名称
     */
    @ApiModelProperty(value = "父课程名称")
    private String parentCourseFullName;

    /**
     * 父课程申请步骤名
     */
    @ApiModelProperty(value = "父课程申请步骤名")
    private String parentStepName;
    /**
     * 中文名
     */
    @ApiModelProperty(value = "中文名")
    private String nameChn;

    /**
     * 英文名
     */
    @ApiModelProperty(value = "英文名")
    private String nameEn;
    /**
     * 旧系统学校名称
     */
    @ApiModelProperty(value = "旧系统学校名称")
    @Column(name = "old_institution_name")
    private String oldInstitutionName;

    /**
     * 旧系统学校全称
     */
    @ApiModelProperty(value = "旧系统学校全称")
    @Column(name = "old_institution_full_name")
    private String oldInstitutionFullName;

    /**
     * 旧系统课程名称
     */
    @ApiModelProperty(value = "旧系统课程名称")
    @Column(name = "old_course_custom_name")
    private String oldCourseCustomName;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程类型")
    private String courseType;

    @ApiModelProperty(value = "旧系统课程类型名称")
    private String oldCourseTypeName;

    /**
     * 课程长度类型(0周、1月、2年、3学期)
     */
    @ApiModelProperty(value = "课程长度类型(0周、1月、2年、3学期)")
    private Integer durationType;

    /**
     * 课程长度
     */
    @ApiModelProperty(value = "课程长度")
    private BigDecimal duration;

    @ApiModelProperty(value = "课程长度信息（周、年）")
    private String durationInfo;

    @ApiModelProperty(value = "代理编号")
    private String agentNum;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "代理联系人邮箱")
    private String agentEmail;

    /**
     * 课程id
     */
    @ApiModelProperty(value = "课程id")
    private Long fkInstitutionCourseId;

    /**
     * 自定义课程id
     */
    @ApiModelProperty(value = "自定义课程id")
    private Long fkInstitutionCourseCustomId;

    /**
     * 步骤id
     */
    @ApiModelProperty(value = "步骤id")
    private String stepIdStr;

    /**
     * 开学时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开学时间")
    private Date openingTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("延迟入学时间（最终开学时间）")
    private Date deferOpeningTime;

    @ApiModelProperty("延迟入学时间str（最终开学时间）")
    private String deferOpeningTimeStr;

    /**
     * 申请方案id
     */
    @ApiModelProperty(value = "申请方案id")
    private Long fkStudentOfferId;

    /**
     * 申请方案状态
     */
    @ApiModelProperty(value = "申请方案状态")
    private Long studentOfferStatus;

    /**
     * 申请方案状态order
     */
    @ApiModelProperty(value = "申请方案状态order")
    private Integer stepOrder;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;

    @ApiModelProperty("创建人")
    private String gmtCreateUser;


    /**
     * 学校官网
     */
    @ApiModelProperty(value = "学校官网")
    private String institutionWebsite;

    /**
     * 课程官网
     */
    @ApiModelProperty(value = "课程官网")
    private String courseWebsite;


    @ApiModelProperty(value = "状态名称")
    private String statusName;

    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 一键操作时间
     */
    @ApiModelProperty(value = "一键操作时间")
    @Column(name = "rpa_opt_time")
    private Date rpaOptTime;

    /**
     * 一键完成时间
     */
    @ApiModelProperty(value = "一键完成时间")
    @Column(name = "rpa_finish_time")
    private Date rpaFinishTime;

    @ApiModelProperty(value = "旧系统课程专业等级名称")
    private String oldCourseMajorLevelName;
    /**
     * 一键处理状态：枚举
     */
    @ApiModelProperty(value = "一键处理状态：枚举")
    @Column(name = "status_rpa")
    private String statusRpa;


    @ApiModelProperty(value = "一键申请状态（开始时间）")
    private String statusRpaOptTime;

    /**
     * 旧数据id(issue)
     */
    @ApiModelProperty(value = "旧数据id(issue)")
    private String idIssue;

    /**
     * 新数据id(issue)
     */
    @ApiModelProperty(value = "新数据id(issue)")
    private Long idIssueNew;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

    /**
     * 学校学院Id
     */
    @ApiModelProperty(value = "学校学院Id")
    private Long fkInstitutionFacultyId;

    /**
     * 学校校区Id
     */
    @ApiModelProperty(value = "学校校区Id")
    private Long fkInstitutionZoneId;


    @ApiModelProperty(value = "国家名")
    private String fkAreaCountryName;

    @ApiModelProperty(value = "rpa order id（一键申请对应的Order id）")
    @Column(name = "fk_issue_rpa_order_id")
    private Long fkIssueRpaOrderId;

    /**
     * 一键申请状态
     */
    @ApiModelProperty(value = "一键申请状态:N=New, C=Completed, E=Error")
    private String robotStatus;

    /**
     * 状态：0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销
     */
    @ApiModelProperty(value = "状态：0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销")
    private Integer statusWorkflow;

    /**
     * 申请备注（网申信息）
     */
    @ApiModelProperty(value = "申请备注（网申信息）")
    private String appRemark;

    /**
     * 延迟入学时间
     */
    @ApiModelProperty(value = "延迟入学时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date maxDeferEntranceTimes;

    /**
     * 延迟入学标记
     */
    @ApiModelProperty(value = "延迟入学标记")
    private Boolean isDeferEntrance;

    /**
     * 代理ID
     */
    private Long agentId;

    @ApiModelProperty(value = "旧iussue用户信息")
    UserInfo userInfoDto;

    @ApiModelProperty(value = "新iussue用户信息")
    UserInfo userInfoNewDto;

    @ApiModelProperty(value = "true:可以一键申请 false:不可以")
    private boolean applyFlag;

    @ApiModelProperty(value = "BD名称")
    private String bdName;
    @ApiModelProperty(value = "国籍")
    private Long fkAreaCountryIdNationality;

    @ApiModelProperty(value = "学号")
    private String numGea;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "毕业院校")
    private String fkInstitutionNameEducation;

    @ApiModelProperty(value = "毕业专业")
    private String educationMajor;

    @ApiModelProperty(value = "项目说明")
    private Integer educationProject;

    @ApiModelProperty(value = "手机")
    private String mobile;

    @ApiModelProperty(value = "email")
    private String email;

    @ApiModelProperty(value = "BD")
    private Long fkStaffId;

    @ApiModelProperty(value = "失败原因id")
    private Long fkEnrolFailureReasonId;

    @ApiModelProperty(value = "旧数据id(gea)")
    private String idGea;

    @ApiModelProperty(value = "BD员工id")
    private Long bdStaffId;

    @ApiModelProperty(value = "学校类型id（学校等级）")
    private Long fkInstitutionTypeId;

    @ApiModelProperty(value = "提供商渠道名称")
    private String fkInstitutionChannelName;

    @ApiModelProperty(value = "课程等级名称")
    private String courseMajorLevelNames;

    /**
     * 学历等级类型：中专/高中/大学/大专/专升本/本科/研究生/硕士/博士在读/博士/博士后/HND
     */
    @ApiModelProperty(value = "学历等级")
    private String educationLevelType;

    /**
     * 毕业院校名称（下拉）
     */
    @ApiModelProperty(value = "在读/毕业学校")
    private String educationInstitutionName;

    /**
     * 毕业大学国家Id
     */
    @ApiModelProperty(value = "毕业大学国家Id")
    private Long fkAreaCountryIdEducation;
    /**
     * 毕业大学州省Id
     */
    @ApiModelProperty(value = "毕业大学州省Id")
    private Long fkAreaStateIdEducation;
    /**
     * 毕业大学城市Id
     */
    @ApiModelProperty(value = "毕业大学城市Id")
    private Long fkAreaCityIdEducation;
    /**
     * 毕业大学国家名称
     */
    @ApiModelProperty(value = "毕业大学国家名称")
    private String fkAreaCountryNameEducation;
    /**
     * 毕业大学州省名称
     */
    @ApiModelProperty(value = "毕业大学州省名称")
    private String fkAreaStateNameEducation;
    /**
     * 毕业大学城市名称
     */
    @ApiModelProperty(value = "毕业大学城市名称")
    private String fkAreaCityNameEducation;
    /**
     * 毕业院校Id
     */
    @ApiModelProperty(value = "毕业院校Id")
    private Long fkInstitutionIdEducation;
    /**
     * 毕业学校类型：985/211/其他，默认选项：其他
     */
    @ApiModelProperty(value = "毕业学校类型：985/211/其他，默认选项：其他")
    private String institutionTypeEducation;
    /**
     * 学历等级类型（国际）：中专/高中/大学/大专/专升本/本科/研究生/硕士/博士在读/博士/博士后/HND
     */
    @ApiModelProperty(value = "学历等级类型（国际）：中专/高中/大学/大专/专升本/本科/研究生/硕士/博士在读/博士/博士后/HND")
    private String educationLevelType2;
    /**
     * 毕业专业（国际）
     */
    @ApiModelProperty(value = "毕业专业（国际）")
    private String educationMajor2;
    /**
     * 毕业大学国家Id（国际）
     */
    @ApiModelProperty(value = "毕业大学国家Id（国际）")
    private Long fkAreaCountryIdEducation2;
    /**
     * 毕业大学州省Id（国际）
     */
    @ApiModelProperty(value = "毕业大学州省Id（国际）")
    private Long fkAreaStateIdEducation2;
    /**
     * 毕业大学城市Id（国际）
     */
    @ApiModelProperty(value = "毕业大学城市Id（国际）")
    private Long fkAreaCityIdEducation2;
    /**
     * 毕业大学国家名称（国际）
     */
    @ApiModelProperty(value = "毕业大学国家名称（国际）")
    private String fkAreaCountryNameEducation2;
    /**
     * 毕业大学州省名称（国际）
     */
    @ApiModelProperty(value = "毕业大学州省名称（国际）")
    private String fkAreaStateNameEducation2;
    /**
     * 毕业大学城市名称（国际）
     */
    @ApiModelProperty(value = "毕业大学城市名称（国际）")
    private String fkAreaCityNameEducation2;
    /**
     * 毕业院校Id（国际）
     */
    @ApiModelProperty(value = "毕业院校Id（国际）")
    private Long fkInstitutionIdEducation2;

    @ApiModelProperty(value = "研究生成绩类型")
    private String masterTestType;


    @ApiModelProperty(value = "研究生成绩")
    private String masterTestScore;

    /**
     * 高中成绩类型
     */
    @ApiModelProperty(value = "高中成绩类型")
    private String highSchoolTestType;

    /**
     * 高中成绩类型名称（student表）
     */
    @ApiModelProperty(value = "高中成绩类型名称")
    private String highSchoolTestTypeName;

    /**
     * 高中成绩（student表）
     */
    @ApiModelProperty(value = "高中成绩")
    private String highSchoolTestScore;

    /**
     * 本科成绩类型
     */
    @ApiModelProperty(value = "本科成绩类型")
    private String standardTestType;

    /**
     * 本科成绩类型名称（student表）
     */
    @ApiModelProperty(value = "本科成绩类型名称")
    private String standardTestTypeName;

    /**
     * 本科成绩（student表）
     */
    @ApiModelProperty(value = "本科成绩")
    @Column(name = "standard_test_score")
    private String standardTestScore;

    /**
     * 英语测试类型（student表）
     */
    @ApiModelProperty(value = "英语测试类型")
    private String englishTestType;

    /**
     * 英语测试类型名称（student表）
     */
    @ApiModelProperty(value = "英语测试类型名称")
    private String englishTestTypeName;

    /**
     * 英语测试成绩（student表）
     */
    @ApiModelProperty(value = "英语测试成绩")
    @Column(name = "english_test_score")
    private BigDecimal englishTestScore;

    /**
     * 学生来源
     */
    @ApiModelProperty(value = "学生来源")
    private String fkPlatformType;

    /**
     * 成功年份
     */
    @ApiModelProperty(value = "year")
    private String year;

    /**
     * 成功月份
     */
    @ApiModelProperty(value = "Succ. Date")
    private String successTime;

    /**
     * 成功季度
     */
    @ApiModelProperty(value = "Succ. Q")
    private String succQ;

    /**
     * 代理省份
     */
    @ApiModelProperty(value = "Agent Region")
    private String agentRegion;

    /**
     * cpp学生编号
     */
    @ApiModelProperty(value = "Our ID.")
    private String ourId;

    /**
     * 学生名pinyin
     */
    @ApiModelProperty(value = "studentPinYin")
    private String studentPinYin;

    /**
     * 学校类型
     */
    @ApiModelProperty(value = "School Type")
    private String schoolType;

    /**
     * 学校集团
     */
    @ApiModelProperty(value = "Group")
    private String group;

    /**
     * 报考课程等级
     */
    @ApiModelProperty(value = "Major Type")
    private String majorType;

    /**
     * 渠道
     */
    @ApiModelProperty(value = "CHOP")
    private String chop;

    /**
     * 入学年份
     */
    @ApiModelProperty(value = "Intake Year")
    private String intakeYear;

    /**
     * 入学月份
     */
    @ApiModelProperty(value = "Intake Mth")
    private String intakeMth;

    /**
     * 入学季度
     */
    @ApiModelProperty(value = "Intake Q")
    private String intakeQ;

    /**
     * 是否后续课程：0否/1是
     */
    @ApiModelProperty(value = "是否后续课程：0否/1是")
    private Boolean isFollow;

    /**
     * 是否后续课程隐藏：0否/1是
     */
    @ApiModelProperty(value = "是否后续课程隐藏：0否/1是")
    private Boolean isFollowHidden;
    /**
     * 学习及计划父id
     */
    @ApiModelProperty(value = "学习及计划父id")
    private Long fkParentStudentOfferItemId;

    /**
     * 应收状态
     */
    @ApiModelProperty(value = "应收状态")
    private Integer arStatus;

    /**
     * 应付状态
     */
    @ApiModelProperty(value = "应付状态")
    private Integer apStatus;

    /**
     * 结算状态
     */
    @ApiModelProperty(value = "结算状态")
    private Integer settlementStatus;

    /**
     * 应收状态
     */
    @ApiModelProperty(value = "应收状态名称")
    private String arStatusName;

    /**
     * 应付状态
     */
    @ApiModelProperty(value = "应付状态名称")
    private String apStatusName;

    /**
     * 结算状态
     */
    @ApiModelProperty(value = "结算状态名称")
    private String settlementStatusName;

    /**
     * 支付押金截止时间
     */
    @ApiModelProperty(value = "支付押金截止时间")
    private Date depositDeadline;

    /**
     * 接受Offer截止时间
     */
    @ApiModelProperty(value = "接受Offer截止时间")
    private Date acceptOfferDeadline;

    @ApiModelProperty("支付押金时间")
    private Date depositTime;

    @ApiModelProperty(value = "支付学费时间")
    private Date tuitionTime;

    /**
     * 步骤id
     */
    @ApiModelProperty(value = "步骤id")
    private Long fkStudentOfferItemStepId;

    /**
     * 总记录数
     */
    @ApiModelProperty(value = "总记录数")
    private Integer totalCount;
    /**
     * ISSUE课程输入标记：input/select
     */
    @ApiModelProperty(value = "ISSUE课程输入标记：input/select")
    @Column(name = "issue_course_input_flag")
    private String issueCourseInputFlag;

    @ApiModelProperty(value = "币种")
    private String payableCurrencyTypeName;

    @ApiModelProperty(value = "应付金额")
    private BigDecimal payableAmount;

    @ApiModelProperty(value = "实付金额")
    private BigDecimal actualPayableAmount;

    @ApiModelProperty(value = "差额")
    private BigDecimal diffPayableAmount;

    /**
     * 区分是否为台湾学生
     */
    @ApiModelProperty(value = "区分是否为台湾学生")
    private Boolean isTwStudent;

    /**
     * 付款情况信息显示
     */
    @ApiModelProperty(value = "付款情况信息显示（币种=payableCurrencyTypeName，实付金额=actualPayableAmount，付款时间=gmtCreate）")
    private List<Map> payDetailList;

    @ApiModelProperty("毕业院校名称（按id查出）")
    private String fkInstitutionIdEducationName;

    @ApiModelProperty("毕业院校名称（国际）（按id查出）")
    private String fkInstitutionIdEducation2Name;

    @ApiModelProperty("毕业院校名称（国际）（手写）")
    private String fkInstitutionNameEducation2;

    /**
     * 学生申请学校课程Id（ISSUEv2版申请）
     */
    @ApiModelProperty(value = "学生申请学校课程Id（ISSUEv2版申请）")
    private Long fkStudentInstitutionCourseIdIssue2;
    /**
     * 学生Id（ISSUEv2版申请）
     */
    @ApiModelProperty(value = "学生Id（ISSUEv2版申请）")
    private Long fkStudentIdIssue2;

    @ApiModelProperty(value = "角色员工")
    private List<StudentOfferRoleAndStaffVo> studentOfferRoleAndStaff;

    @ApiModelProperty(value = "新申请状态：枚举：0缺资料/1未开放")
    private Integer newAppStatus;

    @ApiModelProperty(value = "附件")
    List<MediaAndAttachedVo> mediaAndAttachedDtos;

    @ApiModelProperty(value = "缺资料附件")
    private List<MediaAndAttachedVo> missingAttachedDtos;

    /**
     * 是否无佣金：0否/1是
     */
    @ApiModelProperty(value = "是否无佣金：0否/1是")
    private Boolean isNoCommission=false;

    @ApiModelProperty(value = "课程类型等级Id")
    private Long courseLevelId;

    @ApiModelProperty(value = "学生个数")
    private Long businessCount;

    @ApiModelProperty(value = "主课申请数量")
    private Long mainCourseCount;

    @ApiModelProperty(value = "变更步骤时间")
    private String changeStepName;

    @ApiModelProperty(value = "应付摘要")
    private List<String> payPlanSummaryList;

    @ApiModelProperty(value = "是否申请失败过：0否/1是")
    private Boolean isFailureOffer = false;

    @ApiModelProperty(value = "开学时间提醒：0否/1是")
    private Boolean isReminderOpentime = false;

    @ApiModelProperty(value = "开学时间提醒： 【一般】1,【严重】2")
    private Integer reminderOpentimeStatus;

    /**
     * 区分是否为主课
     */
    @ApiModelProperty(value = "区分是否为主课")
    private Boolean isMainCourse;

    @ApiModelProperty(value = "集团名称")
    private String fkInstitutionGroupName;

    @ApiModelProperty(value = "学生创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date studentGmtCreate;

    @ApiModelProperty(value = "最新步骤备注")
    private String stepRemark;

    @ApiModelProperty(value = "最新备注建议")
    private String comment;

    @ApiModelProperty(value = "最新备注建议时间")
    private String commentTime;

    /**
     * 是否预付，0否/1是
     */
    @ApiModelProperty(value = "是否预付，0否/1是")
    private Boolean isPayInAdvance;

    /**
     * 递交申请日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "递交申请日期")
    private Date submitTime;

    @ApiModelProperty(value = "isCanStudent")
    private Boolean isCanStudent;

    /**
     * 课程等级对应的ids，多个用逗号分隔
     */
    @ApiModelProperty(value = "课程等级对应的ids，多个用逗号分隔")
    private String fkInstitutionCourseMajorLevelIds;

    @ApiModelProperty(value = "学校提供商")
    private String providerName;

    @ApiModelProperty(value = "BD+代理编号")
    private String bdAgentNum;

    @ApiModelProperty(value = "最终状态日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date finalProcessingTime;

    @ApiModelProperty(value = "最终处理日")
    private Integer finalProcessingDate;

    @ApiModelProperty(value = "提交申请处理日")
    private Integer processingDate;

    @ApiModelProperty(value = "最后状态变更人")
    private String finalCreateUser;

    //最高状态
    @ApiModelProperty(value = "最高状态")
    private String maxStepName;

    @ApiModelProperty(value = "学生佣金结算标记关键字")
    private String commissionMark;

    @ApiModelProperty(value = "项目成员（外联）")
    private String coordinatorRoleStaffName;

    @ApiModelProperty(value = "支付类型（最新）")
    private Integer payLogLastPaymentType;

    @ApiModelProperty(value = "支付类型名称（最新）")
    private String payLogLastPaymentTypeName;

    @ApiModelProperty(value = "支付状态（最新）")
    private Integer payLogLastPaymentStatus;

    @ApiModelProperty(value = "支付状态名称（最新）")
    private String payLogLastPaymentStatusName;

    @ApiModelProperty(value = "支付时间（最新）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payLogLastCreateTime;

    @ApiModelProperty(value = "支付备注（最新）")
    private String payLogLastRemark;

    @ApiModelProperty("代理标签")
    private List<AgentLabelVo> agentLabelVos;

    @ApiModelProperty("代理标签")
    private List<AgentLabelVo> agentEmailLabelVos;

    @ApiModelProperty(value = "代理标签")
    private String agentLabelNames;
}
