package com.get.insurancecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.insurancecenter.dto.card.BatchSaveReminderDto;
import com.get.insurancecenter.dto.card.SaveCreditCardReminderDto;
import com.get.insurancecenter.entity.CreditCardReminder;

/**
 * @Author:Oliver
 * @Date: 2025/7/25
 * @Version 1.0
 * @apiNote:
 */
public interface CreditCardReminderService extends IService<CreditCardReminder> {

    /**
     * 批量保存信用卡提醒信息
     *
     * @param params
     */
    void batchSaveReminder(BatchSaveReminderDto params);

    /**
     * 保存信用卡提醒信息
     *
     * @param params
     */
    void saveReminder(SaveCreditCardReminderDto params);

    /**
     * 获取信用卡提醒信息
     *
     * @param creditCardId
     * @return
     */
    CreditCardReminder getCreditCardReminder(Long creditCardId);
}
