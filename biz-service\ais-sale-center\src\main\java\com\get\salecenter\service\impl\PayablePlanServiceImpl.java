package com.get.salecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.DataConverter;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseEntity;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.financecenter.dto.OneClickSettlementDto;
import com.get.financecenter.dto.SettlementInstallmentBatchUpdateDto;
import com.get.financecenter.dto.SettlementInstallmentUpdateDto;
import com.get.financecenter.entity.PaymentForm;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.financecenter.vo.AlreadyPayVo;
import com.get.financecenter.vo.PaymentFormVo;
import com.get.financecenter.vo.ReceiptFormItemVo;
import com.get.institutioncenter.entity.ContractFormula;
import com.get.institutioncenter.entity.ContractFormulaCommission;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.institutioncenter.vo.InstitutionCourseVo;
import com.get.permissioncenter.entity.Company;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.CompanyVo;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.rocketmqcenter.dto.InsurancePlanMessageDto;
import com.get.salecenter.dao.insurance.InsuranceOrderMapper;
import com.get.salecenter.dao.insurance.InsuranceOrderSettlementMapper;
import com.get.salecenter.dao.sale.*;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.query.PayablePlanNewQueryDto;
import com.get.salecenter.entity.*;
import com.get.salecenter.service.*;
import com.get.salecenter.utils.ConvertUtils;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.utils.sale.GetAgentLabelDataUtils;
import com.get.salecenter.vo.*;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.get.core.tool.utils.DateUtil.now;

/**
 * <AUTHOR>
 * @DATE: 2020/11/23
 * @TIME: 12:45
 * @Description:
 **/
@Service
public class PayablePlanServiceImpl extends ServiceImpl<PayablePlanMapper, PayablePlan> implements IPayablePlanService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    private PayablePlanMapper payablePlanMapper;
    @Resource
    private DorisService dorisService;
    @Resource
    private InsuranceOrderMapper insuranceOrderMapper;
    @Resource
    private InsuranceOrderSettlementMapper insuranceOrderSettlementMapper;
    @Resource
    @Lazy
    private IStudentOfferItemService offerItemService;
    @Resource
    @Lazy
    private IStudentOfferService offerService;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IBusinessProviderService businessProviderService;
    @Resource
    private UtilService utilService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    @Lazy
    private IAgentService agentService;
    @Resource
    private StudentOfferMapper studentOfferMapper;
    @Resource
    private AgentContractAccountMapper agentContractAccountMapper;
    @Resource
    private LogInsuranceOrderSettlementMapper logInsuranceOrderSettlementMapper;
    @Resource
    private StudentMapper studentMapper;
    @Resource
    private AgentContractFormulaMapper agentContractFormulaMapper;
    @Resource
    private PayablePlanContractFormulaMapper payablePlanContractFormulaMapper;
    @Resource
    private AgentContractFormulaCommissionMapper agentContractFormulaCommissionMapper;
    @Resource
    private StudentOfferItemMapper studentOfferItemMapper;
    @Resource
    private AgentMapper agentMapper;
    @Resource
    private StudentOfferItemDeferEntranceTimeMapper studentOfferItemDeferEntranceTimeMapper;
    @Resource
    private StudentInsuranceMapper studentInsuranceMapper;
    @Resource
    private StudentAccommodationMapper studentAccommodationMapper;
    @Resource
    private BusinessChannelMapper businessChannelMapper;
    @Resource
    private StudentServiceFeeService studentServiceFeeService;
    @Resource
    private ReceivablePlanMapper receivablePlanMapper;
    @Lazy
    @Resource
    private IStudentInsuranceService studentInsuranceService;
    @Lazy
    @Resource
    private IStudentAccommodationService studentAccommodationService;

    @Resource
    private IReminderCenterClient reminderCenterClient;

    @Lazy
    @Resource
    private ICommentService commentService;

    @Resource
    private StudentOfferItemDeferEntranceService studentOfferItemDeferEntranceService;

    @Resource
    private StaffBdCodeMapper staffBdCodeMapper;

    @Resource
    private GetAgentLabelDataUtils getAgentLabelDataUtils;

    private SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");

    @Override
    public List<PayablePlanVo> datas(PayablePlanDto payablePlanDto, Page page, String[] times) {
        if (GeneralTool.isEmpty(payablePlanDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        //查询条件-公司ids
        if (GeneralTool.isNotEmpty(payablePlanDto.getFkCompanyIds())) {
            if (!SecureUtil.validateCompanys(payablePlanDto.getFkCompanyIds())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
//            if(!SecureUtil.validateCompanys(payablePlanDto.getFkCompanyIds()))
//            {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
//            }
        }

//        LocalDateTime start = LocalDateTime.now();
//        long startTime = start.toInstant(ZoneOffset.of("+8")).toEpochMilli();

        long startTime = System.currentTimeMillis();

        Set<Long> ids = new HashSet<>();
        List<PayablePlanVo> payablePlanVos = payablePlanMapper.getPayablePlansInfo(payablePlanDto);
        ids = payablePlanVos.stream().map(PayablePlanVo::getId).collect(Collectors.toSet());
        ids.removeIf(Objects::isNull);
        if (GeneralTool.isEmpty(ids)) {
            ids.add(0L);
        }
        LambdaQueryWrapper<PayablePlan> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(ids) && GeneralTool.isEmpty(payablePlanDto.getFkStudentId())) {
            Set<Long> finalIds = ids;
            lambdaQueryWrapper.and(wrapper -> wrapper.in(PayablePlan::getId, finalIds));
        }
        lambdaQueryWrapper.and(wrapper -> {
            if (GeneralTool.isNotEmpty(payablePlanDto.getFkStudentId())) {
//                criteria.andEqualTo("fkTypeKey", TableEnum.SALE_STUDENT_OFFER_ITEM.key);
//                wrapper.eq(PayablePlan::getFkTypeKey,TableEnum.SALE_STUDENT_OFFER_ITEM.key);
//                List<Long> itemIds = getTypeTargetIds(payablePlanDto);
////                criteria.andIn("fkTypeTargetId", itemIds);
//                wrapper.in(PayablePlan::getFkTypeTargetId,itemIds);
            }
            if (GeneralTool.isNotEmpty(payablePlanDto.getFkCompanyId())) {
                List<Long> planIds = new ArrayList<>();
                //学习计划应付计划ids
                List<Long> studentOfferItemPlanIds = payablePlanMapper.getPlanIdByCompanyId(payablePlanDto.getFkCompanyId(), payablePlanDto.getFkStudentId());
                if (GeneralTool.isNotEmpty(studentOfferItemPlanIds)) {
                    planIds.addAll(studentOfferItemPlanIds);
                }
                //留学保险应付计划ids
                List<Long> studentInsurancePlanIds = payablePlanMapper.getStudentInsurancePlanIds(payablePlanDto.getFkCompanyId(), payablePlanDto.getFkStudentId());
                if (GeneralTool.isNotEmpty(studentInsurancePlanIds)) {
                    planIds.addAll(studentInsurancePlanIds);
                }
                //留学住宿应付计划ids
                List<Long> studentAccommodationIds = payablePlanMapper.getStudentAccommodationIds(payablePlanDto.getFkCompanyId(), payablePlanDto.getFkStudentId());
                if (GeneralTool.isNotEmpty(studentAccommodationIds)) {
                    planIds.addAll(studentAccommodationIds);
                }

                if (GeneralTool.isNotEmpty(studentAccommodationIds)) {
                    planIds.addAll(studentAccommodationIds);
                }

                if (GeneralTool.isEmpty(planIds)) {
                    planIds = new ArrayList<>();
                    planIds.add(0L);
                }
                wrapper.in(PayablePlan::getId, planIds);
            }
            //公司过滤改成多选
            if (GeneralTool.isNotEmpty(payablePlanDto.getFkCompanyIds())) {
                List<Long> planIds = new ArrayList<>();
                //学习计划应付计划ids
                List<Long> studentOfferItemPlanIds = payablePlanMapper.getPlanIdByCompanyIds(payablePlanDto.getFkCompanyIds(), payablePlanDto.getFkStudentId());
                if (GeneralTool.isNotEmpty(studentOfferItemPlanIds)) {
                    planIds.addAll(studentOfferItemPlanIds);
                }
                //留学保险应付计划ids
                List<Long> studentInsurancePlanIds = payablePlanMapper.getStudentInsurancePlanIdsByCompanyIds(payablePlanDto.getFkCompanyIds(), payablePlanDto.getFkStudentId());
                if (GeneralTool.isNotEmpty(studentInsurancePlanIds)) {
                    planIds.addAll(studentInsurancePlanIds);
                }
                //留学住宿应付计划ids
                List<Long> studentAccommodationIds = payablePlanMapper.getStudentAccommodationIdsByCompanyIds(payablePlanDto.getFkCompanyIds(), payablePlanDto.getFkStudentId());
                if (GeneralTool.isNotEmpty(studentAccommodationIds)) {
                    planIds.addAll(studentAccommodationIds);
                }

                if (GeneralTool.isEmpty(planIds)) {
                    planIds = new ArrayList<>();
                    planIds.add(0L);
                }
                wrapper.in(PayablePlan::getId, planIds);
            }

//            if (GeneralTool.isNotEmpty(payablePlanDto.getFkTypeKey())) {
////                criteria.andEqualTo("fkTypeKey", TableEnum.SALE_STUDENT_OFFER_ITEM.key);
//                wrapper.eq(PayablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_OFFER_ITEM.key);
//            }
            if (GeneralTool.isNotEmpty(payablePlanDto.getOfferItemNum())) {
                List<Long> itemIds = offerItemService.getItemIdByNum(payablePlanDto.getOfferItemNum());
                if (GeneralTool.isNotEmpty(itemIds)) {
                    itemIds = new ArrayList<>();
                    itemIds.add(0L);
                }
//                criteria.andEqualTo("fkTypeKey", TableEnum.SALE_STUDENT_OFFER_ITEM.key);
//                criteria.andIn("fkTypeTargetId", itemIds);
//                wrapper.eq(PayablePlan::getFkTypeKey, payablePlanDto.getFkTypeKey());
                wrapper.in(PayablePlan::getFkTypeTargetId, itemIds);
            }
            if (GeneralTool.isNotEmpty(payablePlanDto.getStudentName())) {
                List<Long> planIds = payablePlanMapper.getPlanIdByStudentName(payablePlanDto.getStudentName());
                if (GeneralTool.isEmpty(planIds)) {
                    planIds = new ArrayList<>();
                    planIds.add(0L);
                }
//                criteria.andIn("id", planIds);
                wrapper.in(PayablePlan::getId, planIds);
            }
            if (GeneralTool.isNotEmpty(payablePlanDto.getAgentName())) {
                List<Long> agentListIds = agentService.getAgentListIds(payablePlanDto.getAgentName());
                List<Long> planIds = payablePlanMapper.getPlanIdByAgentId(agentListIds);
                if (GeneralTool.isEmpty(planIds)) {
                    planIds = new ArrayList<>();
                    planIds.add(0L);
                }
//                criteria.andIn("id", planIds);
                wrapper.in(PayablePlan::getId, planIds);
            }
            if (GeneralTool.isNotEmpty(payablePlanDto.getInstitutionName())) {
                List<Long> institutionIds = new ArrayList<>();
                Result<List<Long>> result = institutionCenterClient.getInstitutionIds(payablePlanDto.getInstitutionName());
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    institutionIds = result.getData();
                }
                List<Long> planIds = payablePlanMapper.getPlanIdByInstitutionId(institutionIds);
                if (GeneralTool.isEmpty(planIds)) {
                    planIds = new ArrayList<>();
                    planIds.add(0L);
                }
//                criteria.andIn("id", planIds);
                wrapper.in(PayablePlan::getId, planIds);
            }
            if (GeneralTool.isNotEmpty(payablePlanDto.getCourseName())) {
                List<Long> courseIds = new ArrayList<>();
                Result<List<Long>> result = institutionCenterClient.getCourseIds(payablePlanDto.getCourseName());
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    courseIds = result.getData();
                }
                List<Long> planIds = payablePlanMapper.getPlanIdByCourseId(courseIds);
                if (GeneralTool.isEmpty(planIds)) {
                    planIds = new ArrayList<>();
                    planIds.add(0L);
                }
//                criteria.andIn("id", planIds);
                wrapper.in(PayablePlan::getId, planIds);
            }
            if (GeneralTool.isNotEmpty(payablePlanDto.getBeginTime())) {
//                criteria.andGreaterThanOrEqualTo("gmtCreate", payablePlanDto.getBeginTime());
                wrapper.ge(PayablePlan::getGmtCreate, payablePlanDto.getBeginTime());
            }
            if (GeneralTool.isNotEmpty(payablePlanDto.getEndTime())) {
//                criteria.andLessThanOrEqualTo("gmtCreate", payablePlanDto.getEndTime());
                wrapper.le(PayablePlan::getGmtCreate, payablePlanDto.getEndTime());
            }
            if (GeneralTool.isNotEmpty(payablePlanDto.getSummary())) {
//                criteria.andLike("summary", "%" + payablePlanDto.getSummary() + "%");
                wrapper.like(PayablePlan::getSummary, payablePlanDto.getSummary());
            }
            if (GeneralTool.isNotEmpty(payablePlanDto.getFkTypeTargetId())) {
//                criteria.andEqualTo("fkTypeKey", payablePlanDto.getFkTypeKey());
//                criteria.andEqualTo("fkTypeTargetId", payablePlanDto.getFkTypeTargetId());
//                wrapper.eq(PayablePlan::getFkTypeKey,payablePlanDto.getFkTypeKey());
                wrapper.le(PayablePlan::getFkTypeTargetId, payablePlanDto.getFkTypeTargetId());
            }
            if (GeneralTool.isNotEmpty(payablePlanDto.getFkTypeKey())) {
                wrapper.eq(PayablePlan::getFkTypeKey, payablePlanDto.getFkTypeKey());
            }
        });

        lambdaQueryWrapper.orderByDesc(PayablePlan::getId);

        long fStartTime = System.currentTimeMillis();

        List<PayablePlan> payablePlans = new ArrayList<>();
        if (page == null) {
            payablePlans = payablePlanMapper.selectList(lambdaQueryWrapper);
        } else {
            IPage<PayablePlan> pages = payablePlanMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
            payablePlans = pages.getRecords();
            page.setAll((int) pages.getTotal());
        }
        if (GeneralTool.isEmpty(payablePlans)) {
            return Collections.emptyList();
        }

        long fEndTime = System.currentTimeMillis();

        //筛选出来之后
        Map<Long, BigDecimal> actualPayableAmountMap = payablePlanVos.stream().collect(Collectors.toMap(PayablePlanVo::getId, PayablePlanVo::getActualPayableAmount));
        Map<Long, BigDecimal> diffPayableAmountMap = payablePlanVos.stream().collect(Collectors.toMap(PayablePlanVo::getId, PayablePlanVo::getDiffPayableAmount));
        Map<Long, Integer> payableStatusMap = payablePlanVos.stream().collect(Collectors.toMap(PayablePlanVo::getId, PayablePlanVo::getPayableStatus));

        List<Long> itemIds = payablePlans.stream().filter(receivablePlan -> TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(receivablePlan.getFkTypeKey()))
                .map(PayablePlan::getFkTypeTargetId).collect(Collectors.toList());
        if (GeneralTool.isEmpty(itemIds)) {
            itemIds = new ArrayList<>();
            itemIds.add(0L);
        }


        //学习计划
        List<StudentOfferItemVo> studentOfferItemVos = studentOfferItemMapper.getStudentOfferItemDtoListByIds(itemIds);
        Map<Long, Long> offerIdMap = new HashMap<>();
        Map<Long, Long> offerStudentIdMap = new HashMap<>();
        Map<Long, Boolean> isFollowMap = new HashMap<>();
        Map<Long, Long> parentItemIdMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(studentOfferItemVos)) {
            //学习方案id map
            offerIdMap = studentOfferItemVos.stream().collect(Collectors.toMap(StudentOfferItemVo::getId, StudentOfferItemVo::getFkStudentOfferId));
            //学生ID Map
            offerStudentIdMap = studentOfferItemVos.stream().filter(studentOfferItemDto -> studentOfferItemDto.getFkStudentId() != null).collect(Collectors.toMap(StudentOfferItemVo::getId, StudentOfferItemVo::getFkStudentId));
            //isFollow状态值
            isFollowMap = studentOfferItemVos.stream().filter(studentOfferItemDto -> GeneralTool.isNotEmpty(studentOfferItemDto.getIsFollow()))
                    .collect(Collectors.toMap(StudentOfferItemVo::getId, StudentOfferItemVo::getIsFollow));
            //父计划id
            parentItemIdMap = studentOfferItemVos.stream().filter(studentOfferItemDto -> GeneralTool.isNotEmpty(studentOfferItemDto.getFkParentStudentOfferItemId()))
                    .collect(Collectors.toMap(StudentOfferItemVo::getId, StudentOfferItemVo::getFkParentStudentOfferItemId));
        }
        //学习计划公司ID Map
        Map<Long, Long> offerItemCompanyIdMap = new HashMap<>();
        offerItemCompanyIdMap = offerItemService.getCompanyIdsByItemIds(itemIds);
        //根据申请计划ids获取对应的应收计划
        List<ReceivablePlanVo> offerItemReceivablePlanVos = receivablePlanMapper.getReceivablePlanByTargetsAndType(TableEnum.SALE_STUDENT_OFFER_ITEM.key, itemIds);
        Map<Long, List<ReceivablePlanVo>> offerItemReceivablePlanMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(offerItemReceivablePlanVos)) {
            //TODO 改过
            // offerItemReceivablePlanMap = offerItemReceivablePlanVos.stream().collect(Collectors.groupingBy(ReceivablePlan::getFkTypeTargetId));
            offerItemReceivablePlanMap = offerItemReceivablePlanVos.stream().collect(Collectors.groupingBy(ReceivablePlanVo::getFkTypeTargetId));
        }


        //留学住宿
        List<Long> accommodationIds = payablePlans.stream()
                .filter(p -> TableEnum.SALE_STUDENT_ACCOMMODATION.key.equals(p.getFkTypeKey()))
                .map(PayablePlan::getFkTypeTargetId).collect(Collectors.toList());
        if (GeneralTool.isEmpty(accommodationIds)) {
            accommodationIds = new ArrayList<>();
            accommodationIds.add(0L);
        }
        List<StudentAccommodationVo> studentAccommodationVos = studentAccommodationMapper.getCompanyIdsByAccommodationIds(accommodationIds);
        Map<Long, Long> accommodationCompanyIdMap = new HashMap<>();
        Map<Long, Long> accommodationStudentIdMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(studentAccommodationVos)) {
            //公司Id
            accommodationCompanyIdMap = studentAccommodationVos.stream().filter(s -> s.getFkCompanyId() != null)
                    .collect(Collectors.toMap(StudentAccommodationVo::getId, StudentAccommodationVo::getFkCompanyId));
            //学生Id
            accommodationStudentIdMap = studentAccommodationVos.stream().filter(s -> s.getFkStudentId() != null)
                    .collect(Collectors.toMap(StudentAccommodationVo::getId, StudentAccommodationVo::getFkStudentId));
        }
        //根据申请计划ids获取对应的应收计划
        List<ReceivablePlanVo> accommodationReceivablePlanVos = receivablePlanMapper.getReceivablePlanByTargetsAndType(TableEnum.SALE_STUDENT_ACCOMMODATION.key, accommodationIds);
        Map<Long, List<ReceivablePlanVo>> accommodationReceivablePlanMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(accommodationReceivablePlanVos)) {
            accommodationReceivablePlanMap = accommodationReceivablePlanVos.stream()
                    //TODO 改过
                    //.collect(Collectors.groupingBy(ReceivablePlan::getFkTypeTargetId));
                    .collect(Collectors.groupingBy(ReceivablePlanVo::getFkTypeTargetId));
        }


        //留学保险
        List<Long> insuranceIds = payablePlans.stream()
                .filter(p -> TableEnum.SALE_STUDENT_INSURANCE.key.equals(p.getFkTypeKey()))
                .map(PayablePlan::getFkTypeTargetId).collect(Collectors.toList());
        if (GeneralTool.isEmpty(insuranceIds)) {
            insuranceIds = new ArrayList<>();
            insuranceIds.add(0L);
        }
        List<StudentInsuranceVo> studentInsuranceVos = studentInsuranceMapper.getCompanyIdsByInsuranceIds(insuranceIds);
        Map<Long, Long> insuranceCompanyIdMap = new HashMap<>();
        Map<Long, Long> insuranceStudentIdMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(studentInsuranceVos)) {
            //公司Id
            insuranceCompanyIdMap = studentInsuranceVos.stream().filter(s -> s.getFkCompanyId() != null)
                    .collect(Collectors.toMap(StudentInsuranceVo::getId, StudentInsuranceVo::getFkCompanyId));
            //学生Id
            insuranceStudentIdMap = studentInsuranceVos.stream().filter(s -> s.getFkStudentId() != null)
                    .collect(Collectors.toMap(StudentInsuranceVo::getId, StudentInsuranceVo::getFkStudentId));
        }
        //根据申请计划ids获取对应的应收计划
        List<ReceivablePlanVo> insuranceReceivablePlanVos = receivablePlanMapper.getReceivablePlanByTargetsAndType(TableEnum.SALE_STUDENT_INSURANCE.key, insuranceIds);
        Map<Long, List<ReceivablePlanVo>> insuranceReceivablePlanMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(insuranceReceivablePlanVos)) {
            insuranceReceivablePlanMap = insuranceReceivablePlanVos.stream()
                    //TODO 改过
                    // .collect(Collectors.groupingBy(ReceivablePlan::getFkTypeTargetId));
                    .collect(Collectors.groupingBy(ReceivablePlanVo::getFkTypeTargetId));
        }

        List<PayablePlanVo> payablePlanVoList = payablePlans.stream().map(payablePlan -> BeanCopyUtils.objClone(payablePlan, PayablePlanVo::new)).collect(Collectors.toList());

        for (PayablePlanVo payablePlanVo : payablePlanVoList) {
            payablePlanVo.setActualPayableAmount(actualPayableAmountMap.get(payablePlanVo.getId()));
            payablePlanVo.setDiffPayableAmount(diffPayableAmountMap.get(payablePlanVo.getId()));
            payablePlanVo.setPayableStatus(payableStatusMap.get(payablePlanVo.getId()));

            //申请方案Id
            if (GeneralTool.isNotEmpty(offerIdMap) && payablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_OFFER_ITEM.key)) {
                payablePlanVo.setFkStudentOfferId(offerIdMap.get(payablePlanVo.getFkTypeTargetId()));
            }
            //isFollow是否后续课程状态值
            if (GeneralTool.isNotEmpty(isFollowMap) && payablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_OFFER_ITEM.key)) {
                payablePlanVo.setIsFollow(isFollowMap.get(payablePlanVo.getFkTypeTargetId()));
            }
            //父学习计划id
            if (GeneralTool.isNotEmpty(parentItemIdMap) && payablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_OFFER_ITEM.key)) {
                payablePlanVo.setFkParentStudentOfferItemId(parentItemIdMap.get(payablePlanVo.getFkTypeTargetId()));
            }

            //公司ID
            if (GeneralTool.isNotEmpty(offerItemCompanyIdMap) && payablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_OFFER_ITEM.key)) {
                payablePlanVo.setFkCompanyId(offerItemCompanyIdMap.get(payablePlanVo.getFkTypeTargetId()));
            }
            if (GeneralTool.isNotEmpty(accommodationCompanyIdMap) && payablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_ACCOMMODATION.key)) {
                payablePlanVo.setFkCompanyId(accommodationCompanyIdMap.get(payablePlanVo.getFkTypeTargetId()));
            }
            if (GeneralTool.isNotEmpty(insuranceCompanyIdMap) && payablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_INSURANCE.key)) {
                payablePlanVo.setFkCompanyId(insuranceCompanyIdMap.get(payablePlanVo.getFkTypeTargetId()));
            }

            //学生ID
            if (GeneralTool.isNotEmpty(offerStudentIdMap) && payablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_OFFER_ITEM.key)) {
                payablePlanVo.setFkStudentId(offerStudentIdMap.get(payablePlanVo.getFkTypeTargetId()));
            }
            if (GeneralTool.isNotEmpty(accommodationStudentIdMap) && payablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_ACCOMMODATION.key)) {
                payablePlanVo.setFkStudentId(accommodationStudentIdMap.get(payablePlanVo.getFkTypeTargetId()));
            }
            if (GeneralTool.isNotEmpty(insuranceStudentIdMap) && payablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_INSURANCE.key)) {
                payablePlanVo.setFkStudentId(insuranceStudentIdMap.get(payablePlanVo.getFkTypeTargetId()));
            }

            //应付计划绑定目标id对应应收计划：应收金额、实收金额、收款差额
            if (GeneralTool.isNotEmpty(offerItemReceivablePlanMap) && payablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_OFFER_ITEM.key)) {
                List<ReceivablePlanVo> receivablePlanVos = offerItemReceivablePlanMap.get(payablePlanVo.getFkTypeTargetId());
                List<Map<String, Object>> receivablePlanList = new ArrayList<>();
                if (GeneralTool.isNotEmpty(receivablePlanVos)) {
                    for (ReceivablePlanVo dto : receivablePlanVos) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("receivableAmount", dto.getReceivableAmount());
                        map.put("actualReceivableAmount", dto.getActualReceivableAmount());
                        map.put("diffReceivableAmount", dto.getDiffReceivableAmount());
                        map.put("fkCurrencyTypeNum", dto.getFkCurrencyTypeNum());
                        map.put("fkCurrencyTypeName", dto.getFkCurrencyTypeName());
                        receivablePlanList.add(map);
                    }
                    payablePlanVo.setReceivablePlanList(receivablePlanList);
                }
            }
            if (GeneralTool.isNotEmpty(accommodationReceivablePlanMap) && payablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_OFFER_ITEM.key)) {
                List<ReceivablePlanVo> receivablePlanVos = accommodationReceivablePlanMap.get(payablePlanVo.getFkTypeTargetId());
                List<Map<String, Object>> receivablePlanList = new ArrayList<>();
                if (GeneralTool.isNotEmpty(receivablePlanVos)) {
                    for (ReceivablePlanVo dto : receivablePlanVos) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("receivableAmount", dto.getReceivableAmount());
                        map.put("actualReceivableAmount", dto.getActualReceivableAmount());
                        map.put("diffReceivableAmount", dto.getDiffReceivableAmount());
                        map.put("fkCurrencyTypeNum", dto.getFkCurrencyTypeNum());
                        map.put("fkCurrencyTypeName", dto.getFkCurrencyTypeName());
                        receivablePlanList.add(map);
                    }
                    payablePlanVo.setReceivablePlanList(receivablePlanList);
                }
            }
            if (GeneralTool.isNotEmpty(insuranceReceivablePlanMap) && payablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_OFFER_ITEM.key)) {
                List<ReceivablePlanVo> receivablePlanVos = insuranceReceivablePlanMap.get(payablePlanVo.getFkTypeTargetId());
                List<Map<String, Object>> receivablePlanList = new ArrayList<>();
                if (GeneralTool.isNotEmpty(receivablePlanVos)) {
                    for (ReceivablePlanVo dto : receivablePlanVos) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("receivableAmount", dto.getReceivableAmount());
                        map.put("actualReceivableAmount", dto.getActualReceivableAmount());
                        map.put("diffReceivableAmount", dto.getDiffReceivableAmount());
                        map.put("fkCurrencyTypeNum", dto.getFkCurrencyTypeNum());
                        map.put("fkCurrencyTypeName", dto.getFkCurrencyTypeName());
                        receivablePlanList.add(map);
                    }
                    payablePlanVo.setReceivablePlanList(receivablePlanList);
                }
            }
        }
        //TODO 改过
        //List<PayablePlanVo> collect = payablePlanVoList.stream().filter(payablePlanDto -> Objects.nonNull(payablePlanDto.getPayableStatus())).collect(Collectors.toList());
        List<PayablePlanVo> collect = payablePlanVoList.stream().filter(payablePlanVo -> Objects.nonNull(payablePlanDto.getPayableStatus())).collect(Collectors.toList());
        setListName(collect);
        long endTime = System.currentTimeMillis();
        if (GeneralTool.isNotEmpty(times)) {
            times[0] = String.valueOf((fEndTime - fStartTime));
            times[1] = String.valueOf((endTime - startTime) - (fEndTime - fStartTime));
        }
        return collect;
    }

    /**
     * Author Cream
     * Description : // 付款单获取应付列表
     * Date 2023/2/20 17:10
     * Params:
     * Return
     */
    @Override
    public ResponseBo<PayablePlanVo> getPayableList(PayablePlanDto planVo, Page page) {
        String typeKey = planVo.getFkTypeKey();
        Long targetId = planVo.getFkTypeTargetId();
        Long paymentFormId = planVo.getPaymentFormId();
        if (GeneralTool.isNull(paymentFormId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<PayablePlanVo> planDtoList;
        IPage<PayablePlan> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        //根据的目标类型，获取应付集合
        if (TableEnum.SALE_AGENT.key.equals(typeKey)) {
            planDtoList = payablePlanMapper.getPayableListByAgent(iPage, targetId);
            page.setAll((int) iPage.getTotal());
        } else if (TableEnum.INSTITUTION_PROVIDER.key.equals(typeKey) || TableEnum.SALE_BUSINESS_PROVIDER.key.equals(typeKey)) {
            LambdaQueryWrapper<PayablePlan> queryWrapper = Wrappers.<PayablePlan>lambdaQuery()
                    .eq(PayablePlan::getFkTypeKey, typeKey).eq(PayablePlan::getFkTypeTargetId, targetId).ne(PayablePlan::getStatus, 0);
            IPage<PayablePlan> planIPage = payablePlanMapper.selectPage(iPage, queryWrapper);
            List<PayablePlan> records = planIPage.getRecords();
            planDtoList = BeanCopyUtils.copyListProperties(records, PayablePlanVo::new);
            page.setAll((int) planIPage.getTotal());
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("illegal_payment_order_type"));
        }
        if (planDtoList.isEmpty()) {
            return new ListResponseBo<>();
        }
        Set<Long> ids = planDtoList.stream().map(PayablePlanVo::getId).collect(Collectors.toSet());
        //获取付款单
        PaymentForm paymentForm = financeCenterClient.getPayFormById(paymentFormId);
        String paymentFormCurrency = null;
        BigDecimal hkdRate = BigDecimal.ZERO;
        BigDecimal rmbRate = BigDecimal.ZERO;
        if (GeneralTool.isNotEmpty(ids)) {
            paymentFormCurrency = paymentForm.getFkCurrencyTypeNum();
            //获取人民币和港币汇率
            hkdRate = financeCenterClient.getExchangeRate(paymentFormCurrency, "HKD").getData();
            rmbRate = financeCenterClient.getExchangeRate(paymentFormCurrency, "CNY").getData();
        }
        //获取币种
        String currencyName = financeCenterClient.getCurrencyNameByNum(paymentFormCurrency).getData();
        //根据应付ids获取付款单
        List<PaymentFormVo> payFormListFeignByPlanIds = financeCenterClient.getPayFormListFeignByPlanIds(ids);
        //获取已付信息
        List<AlreadyPayVo> alreadyPayByPlanIds = financeCenterClient.getAlreadyPayByPlanIds(ids);
        Map<Long, List<AlreadyPayVo>> collect = alreadyPayByPlanIds.stream().collect(Collectors.groupingBy(AlreadyPayVo::getFkPayablePlanId));
        Map<Long, List<PaymentFormVo>> listMap = payFormListFeignByPlanIds.stream().collect(Collectors.groupingBy(PaymentFormVo::getFkPayablePlanId));
        setListName(planDtoList);
        //应付列表信息封装
        for (PayablePlanVo planDto : planDtoList) {
            List<PaymentFormVo> paymentFormVos = listMap.get(planDto.getId());
            planDto.setAlreadyPayDtos(collect.get(planDto.getId()));
            //设置实付币种
            planDto.setActualPayableCurrencyName(currencyName);
            //实付汇率
            planDto.setActualPayableExchangeRate(paymentForm.getExchangeRate());
            //手续费默认0
            planDto.setServiceFeeActual(BigDecimal.ZERO);
            BigDecimal totalBeforePayment = BigDecimal.ZERO;
            //计算之前已绑定应付的金额
            if (GeneralTool.isNotEmpty(paymentFormVos)) {
                BigDecimal amountPayble = paymentFormVos.stream().map(PaymentFormVo::getAmountPayable).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal amountExchangeRate = paymentFormVos.stream().map(PaymentFormVo::getChildAmountExchangeRate).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                totalBeforePayment = amountExchangeRate.add(amountPayble);
            }
            //当前可绑定金额
            BigDecimal nowAmountPayment = (planDto.getPayableAmount().subtract(totalBeforePayment)).setScale(2, BigDecimal.ROUND_HALF_UP);
            //实付折算
            planDto.setPaidInConversion(nowAmountPayment.multiply(paymentForm.getExchangeRate()).setScale(3, RoundingMode.HALF_UP));
            nowAmountPayment = planDto.getPaidInConversion();
            //计算折合应付金额
            planDto.setAmountPayable(nowAmountPayment.divide(paymentForm.getExchangeRate(), 3, BigDecimal.ROUND_HALF_UP).setScale(2, RoundingMode.HALF_UP));

            //计算港币
            BigDecimal amountHkd = hkdRate.multiply(nowAmountPayment);
            planDto.setExchangeRateHkd(hkdRate);
            planDto.setAmountHkd(amountHkd.setScale(2, BigDecimal.ROUND_HALF_UP));
            //计算人民币
            planDto.setExchangeRateRmb(rmbRate);
            //折合人名币
            BigDecimal amountRmb = rmbRate.multiply(nowAmountPayment);
            planDto.setAmountRmb(amountRmb.setScale(2, BigDecimal.ROUND_HALF_UP));

        }
        return new ListResponseBo<>(planDtoList, BeanCopyUtils.objClone(page, Page::new));
    }

    /**
     * 批量新增应付
     *
     * @param planList
     */
    @Override
    public void saveBatch(List<PayablePlan> planList) {
        if (GeneralTool.isNotEmpty(planList)) {
            saveBatch(planList, planList.size());
        }
    }

    /**
     * 根据应收id 检查应付状态，没有结算就返回true
     *
     * @param receivablePlanId
     * @return
     */
    @Override
    public Boolean checkPayableInfo(Long receivablePlanId) {
        List<PayablePlan> planList = payablePlanMapper.selectList(Wrappers.<PayablePlan>lambdaQuery().eq(PayablePlan::getFkReceivablePlanId, receivablePlanId).ne(PayablePlan::getStatus, 0));
        if (GeneralTool.isNotEmpty(planList)) {
            Set<Long> ids = planList.stream().map(PayablePlan::getId).collect(Collectors.toSet());

            Result<Boolean> result = financeCenterClient.checkPayableInfo(ids);
            if (!result.isSuccess()) {
                throw new GetServiceException(result.getMessage());
            }
            return result.getData();
//            Result<Integer> result = financeCenterClient.getPayFormItemCount(ids);
//            List<PayablePlanSettlementInstallment> installments = payablePlanSettlementInstallmentMapper.selectList(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
//                    .in(PayablePlanSettlementInstallment::getFkPayablePlanId, ids));
//            if (result.isSuccess() && result.getData() != null) {
//                return result.getData() == 0 && installments.size() == 0;
//            }
        }
        return false;
    }

    //批量作废应付
    @Override
    public void cancelPayPlan(Long receivablePlanId) {
        List<PayablePlan> planList = payablePlanMapper.selectList(Wrappers.<PayablePlan>lambdaQuery().eq(PayablePlan::getFkReceivablePlanId, receivablePlanId));
        if (GeneralTool.isNotEmpty(planList)) {
            planList.forEach(p -> p.setStatus(0));
            batchUpdateByIds(planList);
        }
    }


    /**
     * @Description: 代理应付汇总统计明细
     * @Author: Jerry
     * @Date:12:01 2021/11/22
     */
    @Override
    public List<PayablePlanVo> agentPaySumDetail(AgentPaySumDetailDto agentPaySumDetailDto, Page page) {
        IPage<PayablePlanVo> payablePlanDtoIPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<PayablePlanVo> collect = payablePlanMapper.agentPaySumDetail(payablePlanDtoIPage, agentPaySumDetailDto);
        page.setAll((int) payablePlanDtoIPage.getTotal());

        if (GeneralTool.isEmpty(collect)) {
            return Collections.emptyList();
        }
        List<Long> ids = collect.stream().map(PayablePlanVo::getId).collect(Collectors.toList());
        PayablePlanDto payablePlanDto = new PayablePlanDto();
        payablePlanDto.setIds(ids);
        List<PayablePlanVo> payablePlansInfos = payablePlanMapper.getPayablePlansInfo(payablePlanDto);
        Map<Long, BigDecimal> actualPayableAmountMap = payablePlansInfos.stream().collect(Collectors.toMap(PayablePlanVo::getId, PayablePlanVo::getActualPayableAmount));
        Map<Long, BigDecimal> diffPayableAmountMap = payablePlansInfos.stream().collect(Collectors.toMap(PayablePlanVo::getId, PayablePlanVo::getDiffPayableAmount));

        setListName(collect);
        for (PayablePlanVo payablePlanVo : collect) {
            if (GeneralTool.isNotEmpty(payablePlanVo)) {
//                setListName(payablePlanVo, companyMap, offerItemByIds, currencyNamesByNums);
                //取绝对值
                payablePlanVo.setActualPayableAmount(actualPayableAmountMap.get(payablePlanVo.getId()).abs());
                //取绝对值
                payablePlanVo.setDiffPayableAmount(diffPayableAmountMap.get(payablePlanVo.getId()).abs());
            }
        }
        return collect;
    }

    @Override
    public List<PayablePlanVo> studentPaySumDetail(StudentPaySumDetailDto studentPaySumDetailDto, Page page) {
        IPage<PayablePlanVo> payablePlanDtoIPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<PayablePlanVo> collect = payablePlanMapper.studentPaySumDetail(payablePlanDtoIPage, studentPaySumDetailDto);
        page.setAll((int) payablePlanDtoIPage.getTotal());
        List<Long> ids = collect.stream().map(PayablePlanVo::getId).collect(Collectors.toList());
        PayablePlanDto payablePlanDto = new PayablePlanDto();
        payablePlanDto.setIds(ids);
        List<PayablePlanVo> payablePlansInfos = payablePlanMapper.getPayablePlansInfo(payablePlanDto);
        Map<Long, BigDecimal> actualPayableAmountMap = payablePlansInfos.stream().collect(Collectors.toMap(PayablePlanVo::getId, PayablePlanVo::getActualPayableAmount));
        Map<Long, BigDecimal> diffPayableAmountMap = payablePlansInfos.stream().collect(Collectors.toMap(PayablePlanVo::getId, PayablePlanVo::getDiffPayableAmount));
        List<Long> itemIds = collect.stream().filter(receivablePlan -> TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(receivablePlan.getFkTypeKey()))
                //TODO 改过
                //.map(PayablePlan::getFkTypeTargetId).collect(Collectors.toList());
                .map(PayablePlanVo::getFkTypeTargetId).collect(Collectors.toList());
        if (GeneralTool.isEmpty(itemIds)) {
            itemIds = new ArrayList<>();
            itemIds.add(0L);
        }

        //留学住宿
        List<Long> accommodationIds = collect.stream()
                .filter(p -> TableEnum.SALE_STUDENT_ACCOMMODATION.key.equals(p.getFkTypeKey()))
                //TODO 改过
                // .map(PayablePlan::getFkTypeTargetId).collect(Collectors.toList());
                .map(PayablePlanVo::getFkTypeTargetId).collect(Collectors.toList());
        if (GeneralTool.isEmpty(accommodationIds)) {
            accommodationIds = new ArrayList<>();
            accommodationIds.add(0L);
        }

        //留学保险
        List<Long> insuranceIds = collect.stream()
                .filter(p -> TableEnum.SALE_STUDENT_INSURANCE.key.equals(p.getFkTypeKey()))
                //TODO 改过
                // .map(PayablePlan::getFkTypeTargetId).collect(Collectors.toList());
                .map(PayablePlanVo::getFkTypeTargetId).collect(Collectors.toList());

        if (GeneralTool.isEmpty(insuranceIds)) {
            insuranceIds = new ArrayList<>();
            insuranceIds.add(0L);
        }
        //根据申请计划ids获取对应的应收计划
        List<ReceivablePlanVo> offerItemReceivablePlanVos = receivablePlanMapper.getReceivablePlanByTargetsAndType(TableEnum.SALE_STUDENT_OFFER_ITEM.key, itemIds);
        Map<Long, List<ReceivablePlanVo>> offerItemReceivablePlanMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(offerItemReceivablePlanVos)) {
            offerItemReceivablePlanMap = offerItemReceivablePlanVos.stream().collect(Collectors.groupingBy(BaseEntity::getId));
        }

        //根据申请计划ids获取对应的应收计划
        List<ReceivablePlanVo> accommodationReceivablePlanVos = receivablePlanMapper.getReceivablePlanByTargetsAndType(TableEnum.SALE_STUDENT_ACCOMMODATION.key, accommodationIds);
        Map<Long, List<ReceivablePlanVo>> accommodationReceivablePlanMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(accommodationReceivablePlanVos)) {
            accommodationReceivablePlanMap = accommodationReceivablePlanVos.stream()
                    .collect(Collectors.groupingBy(BaseEntity::getId));
        }

        //根据申请计划ids获取对应的应收计划
        List<ReceivablePlanVo> insuranceReceivablePlanVos = receivablePlanMapper.getReceivablePlanByTargetsAndType(TableEnum.SALE_STUDENT_INSURANCE.key, insuranceIds);
        Map<Long, List<ReceivablePlanVo>> insuranceReceivablePlanMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(insuranceReceivablePlanVos)) {
            insuranceReceivablePlanMap = insuranceReceivablePlanVos.stream()
                    .collect(Collectors.groupingBy(BaseEntity::getId));
        }
        setListName(collect);
        for (PayablePlanVo payablePlanVo : collect) {
            if (GeneralTool.isNotEmpty(payablePlanVo)) {
//                setListName(payablePlanVo, companyMap, offerItemByIds, currencyNamesByNums);
                payablePlanVo.setActualPayableAmount(actualPayableAmountMap.get(payablePlanVo.getId()).abs());
                payablePlanVo.setDiffPayableAmount(diffPayableAmountMap.get(payablePlanVo.getId()).abs());
                if (GeneralTool.isNotEmpty(offerItemReceivablePlanMap) && payablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_OFFER_ITEM.key)) {
                    List<ReceivablePlanVo> receivablePlanVos = offerItemReceivablePlanMap.get(payablePlanVo.getFkReceivablePlanId());
                    List<Map<String, Object>> receivablePlanList = new ArrayList<>();
                    if (GeneralTool.isNotEmpty(receivablePlanVos)) {
                        for (ReceivablePlanVo dto : receivablePlanVos) {
                            Map<String, Object> map = new HashMap<>();
                            map.put("receivableAmount", dto.getReceivableAmount());
                            map.put("actualReceivableAmount", dto.getActualReceivableAmount());
                            map.put("diffReceivableAmount", dto.getDiffReceivableAmount());
                            map.put("fkCurrencyTypeNum", dto.getFkCurrencyTypeNum());
                            map.put("fkCurrencyTypeName", dto.getFkCurrencyTypeName());
                            receivablePlanList.add(map);
                        }
                        payablePlanVo.setReceivablePlanList(receivablePlanList);
                    }
                }
                if (GeneralTool.isNotEmpty(accommodationReceivablePlanMap) && payablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_ACCOMMODATION.key)) {
                    List<ReceivablePlanVo> receivablePlanVos = accommodationReceivablePlanMap.get(payablePlanVo.getFkReceivablePlanId());
                    List<Map<String, Object>> receivablePlanList = new ArrayList<>();
                    if (GeneralTool.isNotEmpty(receivablePlanVos)) {
                        for (ReceivablePlanVo dto : receivablePlanVos) {
                            Map<String, Object> map = new HashMap<>();
                            map.put("receivableAmount", dto.getReceivableAmount());
                            map.put("actualReceivableAmount", dto.getActualReceivableAmount());
                            map.put("diffReceivableAmount", dto.getDiffReceivableAmount());
                            map.put("fkCurrencyTypeNum", dto.getFkCurrencyTypeNum());
                            map.put("fkCurrencyTypeName", dto.getFkCurrencyTypeName());
                            receivablePlanList.add(map);
                        }
                        payablePlanVo.setReceivablePlanList(receivablePlanList);
                    }
                }
                if (GeneralTool.isNotEmpty(insuranceReceivablePlanMap) && payablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_INSURANCE.key)) {
                    List<ReceivablePlanVo> receivablePlanVos = insuranceReceivablePlanMap.get(payablePlanVo.getFkReceivablePlanId());
                    List<Map<String, Object>> receivablePlanList = new ArrayList<>();
                    if (GeneralTool.isNotEmpty(receivablePlanVos)) {
                        for (ReceivablePlanVo dto : receivablePlanVos) {
                            Map<String, Object> map = new HashMap<>();
                            map.put("receivableAmount", dto.getReceivableAmount());
                            map.put("actualReceivableAmount", dto.getActualReceivableAmount());
                            map.put("diffReceivableAmount", dto.getDiffReceivableAmount());
                            map.put("fkCurrencyTypeNum", dto.getFkCurrencyTypeNum());
                            map.put("fkCurrencyTypeName", dto.getFkCurrencyTypeName());
                            receivablePlanList.add(map);
                        }
                        payablePlanVo.setReceivablePlanList(receivablePlanList);
                    }
                }
            }
        }
        return collect;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addPayablePlan(PayablePlanDto planVo) {
        if (GeneralTool.isEmpty(planVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //一条应收计划只能绑定一条应付
        PayablePlan payablePlanOne = payablePlanMapper.selectOne(Wrappers.<PayablePlan>lambdaQuery().eq(PayablePlan::getFkReceivablePlanId, planVo.getFkReceivablePlanId())
                .ne(PayablePlan::getStatus, 0));
        if (GeneralTool.isNotEmpty(payablePlanOne)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("PLAN_ONE_TO_ONE"));
        }
        PayablePlan payablePlan = BeanCopyUtils.objClone(planVo, PayablePlan::new);
        payablePlan.setStatus(1);
//        payablePlan.setStatusReview(0);
        payablePlan.setIsPayInAdvance(false);
        payablePlan.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
        utilService.updateUserInfoToEntity(payablePlan);
        payablePlanMapper.insert(payablePlan);
        //查找对应的应收计划未结算过的收款单子单
        List<ReceiptFormItemVo> receiptFormItemList = financeCenterClient.getSettlementReceiptFormItemListByPlanId(planVo.getFkReceivablePlanId());
//        List<SaleReceiptFormItemVo> receiptFormItemDtoList = BeanCopyUtils.copyListProperties(receiptFormItemList, SaleReceiptFormItemVo::new);
        if (GeneralTool.isNotEmpty(receiptFormItemList)) {
//            payablePlanSettlementInstallmentMapper.delete(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery().eq(PayablePlanSettlementInstallment::getFkPayablePlanId, payablePlan.getId())
//                    .eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_UNTREATED.key));
            List<SettlementInstallmentBatchUpdateDto> settlementInstallmentBatchUpdateDtos = new ArrayList<>();
            //更新对应的分期表数据
            for (ReceiptFormItemVo receiptFormItemVo : receiptFormItemList) {
                SettlementInstallmentUpdateDto settlementInstallmentUpdateDto = new SettlementInstallmentUpdateDto();
                settlementInstallmentUpdateDto.setFkCompanyId(payablePlan.getFkCompanyId());
                settlementInstallmentUpdateDto.setFkPayablePlanId(payablePlan.getId());
                ReceivablePlan receivablePlan = receivablePlanMapper.selectById(payablePlan.getFkReceivablePlanId());
                settlementInstallmentUpdateDto.setFkReceivablePlanId(receivablePlan.getId());
                settlementInstallmentUpdateDto.setReceivableAmount(receivablePlan.getReceivableAmount());
                settlementInstallmentUpdateDto.setPayableAmount(payablePlan.getPayableAmount());
                settlementInstallmentUpdateDto.setReceivableCurrencyTypeNum(receivablePlan.getFkCurrencyTypeNum());
                settlementInstallmentUpdateDto.setPayableCurrencyTypeNum(payablePlan.getFkCurrencyTypeNum());

                SettlementInstallmentBatchUpdateDto settlementInstallmentBatchUpdateDto = new SettlementInstallmentBatchUpdateDto();
                settlementInstallmentBatchUpdateDto.setSettlementInstallmentUpdateDto(settlementInstallmentUpdateDto);
                settlementInstallmentBatchUpdateDto.setReceiptFormItemVo(receiptFormItemVo);
                settlementInstallmentBatchUpdateDtos.add(settlementInstallmentBatchUpdateDto);
//                insertSettlementInstallment(payablePlan, receiptFormItemDto);
            }
            //批量更新、插入 分期表数据
            Result result = financeCenterClient.insertSettlementInstallmentBatch(settlementInstallmentBatchUpdateDtos);
            if (!result.isSuccess()) {
                throw new GetServiceException(result.getMessage());
            }
        }
        return payablePlan.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PayablePlanVo updatePayablePlan(PayablePlanDto planVo) {
        if (GeneralTool.isEmpty(planVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        //一条应收计划只能绑定一条应付
        PayablePlan payablePlanOne = payablePlanMapper.selectOne(Wrappers.<PayablePlan>lambdaQuery()
                .eq(PayablePlan::getFkReceivablePlanId, planVo.getFkReceivablePlanId())
                .ne(PayablePlan::getId, planVo.getId())
                .ne(PayablePlan::getStatus, 0));
        if (GeneralTool.isNotEmpty(payablePlanOne)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("PLAN_ONE_TO_ONE"));
        }

        UpdateWrapper<PayablePlan> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(PayablePlan::getId, planVo.getId())
                .set(GeneralTool.isEmpty(planVo.getTuitionAmount()), PayablePlan::getTuitionAmount, null)
                .set(GeneralTool.isEmpty(planVo.getCommissionRate()), PayablePlan::getCommissionRate, null)
                .set(GeneralTool.isEmpty(planVo.getSplitRate()), PayablePlan::getSplitRate, null)
                .set(GeneralTool.isEmpty(planVo.getCommissionAmount()), PayablePlan::getCommissionAmount, null)
                .set(GeneralTool.isEmpty(planVo.getFixedAmount()), PayablePlan::getFixedAmount, null)
                .set(GeneralTool.isEmpty(planVo.getBonusAmount()), PayablePlan::getBonusAmount, null)
                .set(GeneralTool.isEmpty(planVo.getPayableAmount()), PayablePlan::getPayableAmount, null)
                .set(GeneralTool.isEmpty(planVo.getPayablePlanDate()), PayablePlan::getPayablePlanDate, null);
        PayablePlan payablePlan = payablePlanMapper.selectById(planVo.getId());

        BeanUtils.copyProperties(planVo, payablePlan);
        utilService.updateUserInfoToEntity(payablePlan);
        payablePlanMapper.update(payablePlan, updateWrapper);
        if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(payablePlan.getFkTypeKey()) && GeneralTool.isNotEmpty(payablePlan.getTuitionAmount())) {
            studentOfferItemMapper.updateTuitionAmountById(payablePlan.getFkTypeTargetId(), payablePlan.getTuitionAmount());
        }

        //应付计划为学校提供商类型或者业务提供商类型,不用生成给代理的佣金
        if (!payablePlan.getFkTypeKey().equals(TableEnum.INSTITUTION_PROVIDER.key) && !payablePlan.getFkTypeKey().equals(TableEnum.SALE_BUSINESS_PROVIDER.key)) {
            //佣金结算更新
            SettlementInstallmentUpdateDto settlementInstallmentUpdateDto = new SettlementInstallmentUpdateDto();
            settlementInstallmentUpdateDto.setFkCompanyId(payablePlan.getFkCompanyId());
            settlementInstallmentUpdateDto.setFkPayablePlanId(payablePlan.getId());
            ReceivablePlan receivablePlan = receivablePlanMapper.selectById(payablePlan.getFkReceivablePlanId());
            settlementInstallmentUpdateDto.setFkReceivablePlanId(receivablePlan.getId());
            settlementInstallmentUpdateDto.setReceivableAmount(receivablePlan.getReceivableAmount());
            settlementInstallmentUpdateDto.setPayableAmount(payablePlan.getPayableAmount());
            settlementInstallmentUpdateDto.setReceivableCurrencyTypeNum(receivablePlan.getFkCurrencyTypeNum());
            settlementInstallmentUpdateDto.setPayableCurrencyTypeNum(payablePlan.getFkCurrencyTypeNum());
            Result result = financeCenterClient.settlementInstallmentUpdate(settlementInstallmentUpdateDto);
            if (!result.isSuccess()) {
                throw new GetServiceException(result.getMessage());
            }
        }
//        settlementInstallmentUpdate(payablePlan);
        return findPayablePlanById(planVo.getId());
    }

//    /**
//     * 佣金结算更新
//     *
//     * @Date 14:59 2024/2/18
//     * <AUTHOR>
//     */
//    private void settlementInstallmentUpdate(PayablePlan payablePlan) {
//        //查找对应的应收计划未结算过的收款单子单
//        List<ReceiptFormItemVo> receiptFormItemList = financeCenterClient.getSettlementReceiptFormItemListByPlanId(payablePlan.getFkReceivablePlanId());
//        List<SaleReceiptFormItemVo> receiptFormItemDtoList = BeanCopyUtils.copyListProperties(receiptFormItemList, SaleReceiptFormItemVo::new);
//        if (GeneralTool.isNotEmpty(receiptFormItemDtoList)) {
//            List<Long> receiptFormItemId = receiptFormItemDtoList.stream().map(SaleReceiptFormItemVo::getId).collect(Collectors.toList());
//            payablePlanSettlementInstallmentMapper.delete(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery().eq(PayablePlanSettlementInstallment::getFkPayablePlanId, payablePlan.getId())
//                    .eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_UNTREATED.key)
//                    .in(PayablePlanSettlementInstallment::getFkReceiptFormItemId, receiptFormItemId));
//            //插入应付计划结算分期表
//            for (SaleReceiptFormItemVo receiptFormItemDto : receiptFormItemDtoList) {
//                //旧数据不进入结算
//                if ("admin-1".equals(receiptFormItemDto.getGmtCreateUser())) {
//                    continue;
//                }
//                insertSettlementInstallment(payablePlan, receiptFormItemDto);
//            }
//        }
//
//        //预付重新生成
//        List<PayablePlanSettlementInstallment> payablePlanSettlementInstallments = payablePlanSettlementInstallmentMapper.selectList(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
//                .eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_UNTREATED.key)
//                .eq(PayablePlanSettlementInstallment::getFkPayablePlanId, payablePlan.getId())
//                .isNull(PayablePlanSettlementInstallment::getFkReceiptFormItemId));
//        if (GeneralTool.isNotEmpty(payablePlanSettlementInstallments)) {
//            List<Long> ids = payablePlanSettlementInstallments.stream().map(PayablePlanSettlementInstallment::getId).collect(Collectors.toList());
//            payablePlanSettlementInstallmentMapper.deleteBatchIds(ids);
//
//            BigDecimal serviceFee = getPrepaidHandlingFees(payablePlan);
//
//            if (interfaceConfiguration.equals("GEA")) {
//                //本次预付金额
//                BigDecimal amountActual = payablePlan.getPayableAmount().multiply(new BigDecimal(payablePlan.getPayInAdvancePercent()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)).setScale(2, RoundingMode.HALF_UP);
//
//                amountActual = amountActual.subtract(serviceFee);
//
//                PayablePlanSettlementInstallment payablePlanSettlementInstallment = new PayablePlanSettlementInstallment();
//                payablePlanSettlementInstallment.setFkPayablePlanId(payablePlan.getId());
//                payablePlanSettlementInstallment.setAmountExpect(amountActual);
//                payablePlanSettlementInstallment.setAmountActual(amountActual);
//                payablePlanSettlementInstallment.setAmountActualInit(amountActual);
//                payablePlanSettlementInstallment.setServiceFeeExpect(serviceFee);
//                payablePlanSettlementInstallment.setServiceFeeActual(serviceFee);
//                payablePlanSettlementInstallment.setServiceFeeActualInit(serviceFee);
//                payablePlanSettlementInstallment.setRollBack(false);
//                payablePlanSettlementInstallment.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
//                payablePlanSettlementInstallment.setStatus(ProjectExtraEnum.INSTALLMENT_UNTREATED.key);
//                utilService.setCreateInfo(payablePlanSettlementInstallment);
//                payablePlanSettlementInstallmentMapper.insert(payablePlanSettlementInstallment);
//            } else {
//                List<PayablePlanSettlementInstallment> collect = payablePlanSettlementInstallments.stream().filter(payablePlanSettlementInstallment -> payablePlanSettlementInstallment.getFkInvoiceReceivablePlanId() != null).collect(Collectors.toList());
//                if (GeneralTool.isNotEmpty(collect)) {
//                    List<Long> fkInvoiceReceivablePlanIds = collect.stream().map(PayablePlanSettlementInstallment::getFkInvoiceReceivablePlanId).collect(Collectors.toList());
//                    Map<Long, InvoiceReceivablePlan> data = financeCenterClient.getInvoiceAmountByIds(fkInvoiceReceivablePlanIds).getData();
//                    ReceivablePlan receivablePlan = receivablePlanMapper.selectById(payablePlan.getFkReceivablePlanId());
//                    //应收金额
//                    BigDecimal receivableAmount = receivablePlan.getReceivableAmount();
//                    //应收币种
//                    String fkCurrencyTypeNum = receivablePlan.getFkCurrencyTypeNum();
//                    //应收金额折合成应付币种的应收金额
//                    BigDecimal receivableExchangeAmount;
//                    //统一将全部金额转成应付计划币种进行公式计算
//                    if (fkCurrencyTypeNum.equals(payablePlan.getFkCurrencyTypeNum())) {
//                        receivableExchangeAmount = receivableAmount;
//                    } else {
//                        BigDecimal lastExchangeRate = financeCenterClient.getLastExchangeRate(false, fkCurrencyTypeNum, payablePlan.getFkCurrencyTypeNum()).getData();
//                        receivableExchangeAmount = receivableAmount.multiply(lastExchangeRate).setScale(2, RoundingMode.HALF_UP);
//                    }
//                    BigDecimal proportion = payablePlan.getPayableAmount().divide(receivableExchangeAmount, 12, RoundingMode.DOWN);
//                    Map<Long, BigDecimal> map = new HashMap<>();
//                    for (PayablePlanSettlementInstallment settlementInstallment : collect) {
//                        InvoiceReceivablePlan invoiceReceivablePlan = data.get(settlementInstallment.getFkInvoiceReceivablePlanId());
//                        //本次预付金额
//                        BigDecimal amountActual = invoiceReceivablePlan.getAmount().multiply(proportion).multiply(invoiceReceivablePlan.getPayInAdvancePercent().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)).setScale(2, RoundingMode.HALF_UP);
//
//                        amountActual = amountActual.subtract(serviceFee);
//                        PayablePlanSettlementInstallment payablePlanSettlementInstallment = new PayablePlanSettlementInstallment();
//                        payablePlanSettlementInstallment.setFkPayablePlanId(payablePlan.getId());
//                        payablePlanSettlementInstallment.setAmountExpect(amountActual);
//                        payablePlanSettlementInstallment.setAmountActual(amountActual);
//                        payablePlanSettlementInstallment.setAmountActualInit(amountActual);
//                        payablePlanSettlementInstallment.setServiceFeeExpect(serviceFee);
//                        payablePlanSettlementInstallment.setServiceFeeActual(serviceFee);
//                        payablePlanSettlementInstallment.setServiceFeeActualInit(serviceFee);
//                        payablePlanSettlementInstallment.setRollBack(false);
//                        payablePlanSettlementInstallment.setFkInvoiceReceivablePlanId(settlementInstallment.getFkInvoiceReceivablePlanId());
//                        payablePlanSettlementInstallment.setFkInvoiceId(invoiceReceivablePlan.getFkInvoiceId());
//                        payablePlanSettlementInstallment.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
//                        payablePlanSettlementInstallment.setStatus(ProjectExtraEnum.INSTALLMENT_UNTREATED.key);
//                        utilService.setCreateInfo(payablePlanSettlementInstallment);
//                        payablePlanSettlementInstallmentMapper.insert(payablePlanSettlementInstallment);
//                        map.put(settlementInstallment.getFkInvoiceReceivablePlanId(), amountActual);
//                    }
//                    Result result = financeCenterClient.updateInvoiceReceivablePlanAmount(map);
//                    if (!result.isSuccess()) {
//                        throw new GetServiceException(result.getMessage());
//                    }
//                }
//
//
//            }
//
//        }
//
//
//    }

    /**
     * 获取预付手续费
     *
     * @Date 14:56 2024/2/27
     * <AUTHOR>
     */
    private BigDecimal getPrepaidHandlingFees(PayablePlan payablePlan) {
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.PAY_IN_ADVANCE_SERVICE_FEE.key, 1).getData();
        String configValue1 = companyConfigMap.get(payablePlan.getFkCompanyId());
        return new BigDecimal(configValue1);
//        ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.PAY_IN_ADVANCE_SERVICE_FEE.key).getData();
//        String configJson = configDto.getValue1();
//        JSONObject jsonObject = JSON.parseObject(configJson);
//        //预付强制扣他五元手续费
//        BigDecimal serviceFee = BigDecimal.ZERO;
//        if (payablePlan.getFkCompanyId() == 3L) {
//            String iae = jsonObject.getString("IAE");
//            if (StringUtils.isNotBlank(iae)) {
//                serviceFee = new BigDecimal(iae);
//            }
//        } else if (payablePlan.getFkCompanyId() == 2L) {
//            String gea = jsonObject.getString("GEA");
//            if (StringUtils.isNotBlank(gea)) {
//                serviceFee = new BigDecimal(gea);
//            }
//        } else {
//            String other = jsonObject.getString("OTHER");
//            if (StringUtils.isNotBlank(other)) {
//                serviceFee = new BigDecimal(other);
//            }
//        }
//        return serviceFee;
    }

//    /**
//     * 插入应付计划结算分期表
//     *
//     * @Date 14:52 2022/3/30
//     * <AUTHOR>
//     */
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void insertSettlementInstallment(PayablePlan payablePlan, SaleReceiptFormItemVo saleReceiptFormItemVo) {
//        //获取应收计划信息 计算预计支付金额
//        ReceivablePlan receivablePlan = receivablePlanMapper.selectById(saleReceiptFormItemVo.getFkReceivablePlanId());
//        //应收金额
//        BigDecimal receivableAmount = receivablePlan.getReceivableAmount();
//        //应收币种
//        String fkCurrencyTypeNum = receivablePlan.getFkCurrencyTypeNum();
//        //应收金额折合成应付币种的应收金额
//        BigDecimal receivableExchangeAmount;
//        //统一将全部金额转成应付计划币种进行公式计算
//        if (fkCurrencyTypeNum.equals(payablePlan.getFkCurrencyTypeNum())) {
//            receivableExchangeAmount = receivableAmount;
//        } else {
//            BigDecimal lastExchangeRate = financeCenterClient.getLastExchangeRate(false, fkCurrencyTypeNum, payablePlan.getFkCurrencyTypeNum()).getData();
//            receivableExchangeAmount = receivableAmount.multiply(lastExchangeRate).setScale(2, RoundingMode.HALF_UP);
//        }
//        //收款单付款金额折合成 应付币种的金额
//        BigDecimal receiptFormItemAmount;
//        //收款单手续费金额折合成 应付币种手续费金额
//        BigDecimal receiptFormItemServiceFee;
//        if (saleReceiptFormItemVo.getFkCurrencyTypeNum().equals(payablePlan.getFkCurrencyTypeNum())) {
//            receiptFormItemAmount = saleReceiptFormItemVo.getAmountReceipt();
//            receiptFormItemServiceFee = saleReceiptFormItemVo.getServiceFee();
//        } else if (fkCurrencyTypeNum.equals(payablePlan.getFkCurrencyTypeNum())) {
//            //实收兑应收币种汇率
//            BigDecimal lastExchangeRate = new BigDecimal(1).divide(saleReceiptFormItemVo.getExchangeRateReceivable(), 6, RoundingMode.HALF_UP );
//            receiptFormItemAmount = saleReceiptFormItemVo.getAmountReceipt().multiply(lastExchangeRate).setScale(2, RoundingMode.HALF_UP);
//            receiptFormItemServiceFee = saleReceiptFormItemVo.getServiceFee().multiply(lastExchangeRate).setScale(2, RoundingMode.HALF_UP);
//        } else {
//            BigDecimal lastExchangeRate = financeCenterClient.getLastExchangeRate(false, saleReceiptFormItemVo.getFkCurrencyTypeNum(), payablePlan.getFkCurrencyTypeNum()).getData();
//            receiptFormItemAmount = saleReceiptFormItemVo.getAmountReceipt().multiply(lastExchangeRate).setScale(2, RoundingMode.HALF_UP);
//            receiptFormItemServiceFee = saleReceiptFormItemVo.getServiceFee().multiply(lastExchangeRate).setScale(2, RoundingMode.HALF_UP);
//        }
//        //(应付金额/应收金额转应付币种的金额)比例
//        if (receivableExchangeAmount.compareTo(BigDecimal.ZERO) == 0) {
//            return;
//        }
//        BigDecimal proportion = payablePlan.getPayableAmount().divide(receivableExchangeAmount, 12, RoundingMode.DOWN);
//        PayablePlanSettlementInstallment payablePlanSettlementInstallment = new PayablePlanSettlementInstallment();
//        payablePlanSettlementInstallment.setFkPayablePlanId(payablePlan.getId());
//        payablePlanSettlementInstallment.setFkReceiptFormItemId(saleReceiptFormItemVo.getId());
//        //本次预计支付金额 = 收款单收款金额折合成应付币种的金额   *    (应付金额/应收金额转应付币种的金额)比例     收到的钱乘以应收应付金额比例
//        BigDecimal amountExpect = receiptFormItemAmount.add(receiptFormItemServiceFee).multiply(proportion).setScale(2, RoundingMode.HALF_UP);
//        //本次预计手续费金额/实际手续费
//        BigDecimal serviceFeeExpect = receiptFormItemServiceFee.setScale(2, RoundingMode.HALF_UP);
//
//
//
//        //实际支付金额
//        BigDecimal amountActual;
//        //实际支付手续费金额
//        BigDecimal amountActualServiceFee = serviceFeeExpect;
//            //没有预付直接按比例支付
//            amountActual = amountExpect;
//            //需要减去手续费
//            amountActual = amountActual.subtract(serviceFeeExpect);
//
//        if (saleReceiptFormItemVo.getHedgeAmount() != null) {
//            amountActual = amountActual.subtract(saleReceiptFormItemVo.getHedgeAmount());
//            if (amountActual.compareTo(BigDecimal.ZERO) <= 0) {
//                return;
//            }
//        }
//
//        //已付金额
//        BigDecimal amountPaid = financeCenterClient.getAmountPaidByPayablePlanId(payablePlan.getId()).add(payablePlanSettlementInstallmentMapper.getAmountPaidByPayablePlanId(payablePlan.getId()));
//        //应付未付金额
//        BigDecimal unpaidAmount = payablePlan.getPayableAmount().subtract(amountPaid);
//        //应付未付为 <= 0，已经付完钱给代理了，不需要再结算钱给他  (本次支付金额为负数的，是倒扣的，不需要判断)
//        if (((unpaidAmount.compareTo(new BigDecimal(0)) <= 0 && payablePlan.getPayableAmount().compareTo(BigDecimal.ZERO) > 0) ||
//                (unpaidAmount.compareTo(new BigDecimal(0)) >= 0 && payablePlan.getPayableAmount().compareTo(BigDecimal.ZERO) < 0) ||
//                payablePlan.getPayableAmount().compareTo(BigDecimal.ZERO) == 0
//        ) && amountActual.compareTo(BigDecimal.ZERO) >= 0) {
//            return;
//        }
//        //如果本次结算佣金 + 手续费 > 应付未付   (本次支付金额为负数的，是倒扣的，不需要判断)
//        if (amountActual.compareTo(BigDecimal.ZERO) >= 0 && (amountActual.add(serviceFeeExpect)).compareTo(unpaidAmount) > 0) {
//            amountActual = unpaidAmount.subtract(serviceFeeExpect);
//        }
//
//        //如果负数倒扣金额 等于 已生成佣金金额 则删除佣金
//        BigDecimal firstStepCommission = payablePlanSettlementInstallmentMapper.getFirstStepCommission(payablePlan.getId());
//        if (amountActual.compareTo(firstStepCommission.negate()) == 0) {
//            //删除抵消佣金
//            payablePlanSettlementInstallmentMapper.delete(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
//                    .eq(PayablePlanSettlementInstallment::getFkPayablePlanId, payablePlan.getId())
//                    .eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_UNTREATED.key)
//                    .eq(PayablePlanSettlementInstallment::getStatusSettlement, ProjectExtraEnum.UNSETTLED.key));
//            return;
//        }
//
//        payablePlanSettlementInstallment.setAmountExpect(amountExpect);
//        payablePlanSettlementInstallment.setAmountActual(amountActual);
//        payablePlanSettlementInstallment.setAmountActualInit(amountActual);
//        payablePlanSettlementInstallment.setServiceFeeExpect(serviceFeeExpect);
//        payablePlanSettlementInstallment.setServiceFeeActual(amountActualServiceFee);
//        payablePlanSettlementInstallment.setServiceFeeActualInit(amountActualServiceFee);
//        payablePlanSettlementInstallment.setRollBack(false);
//        payablePlanSettlementInstallment.setStatus(ProjectExtraEnum.INSTALLMENT_UNTREATED.key);
//        payablePlanSettlementInstallment.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
//        utilService.setCreateInfo(payablePlanSettlementInstallment);
//        payablePlanSettlementInstallmentMapper.insert(payablePlanSettlementInstallment);
//
//        PayablePlanSettlementStatus payablePlanSettlementStatus = new PayablePlanSettlementStatus();
//        payablePlanSettlementStatus.setFkPayablePlanId(payablePlan.getId());
//        payablePlanSettlementStatus.setFkPayablePlanSettlementInstallmentId(payablePlanSettlementInstallment.getId());
//        payablePlanSettlementStatus.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
//        utilService.setCreateInfo(payablePlanSettlementStatus);
//        payablePlanSettlementStatusMapper.insert(payablePlanSettlementStatus);
//    }


    /**
     * 应付计划列表数据
     *
     * @param payablePlanNewVo
     * @param page
     * @param times
     * @return
     */
    @Override
    public List<PayablePlanNewVo> payablePlanDatas(PayablePlanNewQueryDto payablePlanNewVo, SearchBean<PayablePlanNewQueryDto> page, String[] times) {
        List<PayablePlanNewVo> collect;
        long fStartTime = System.currentTimeMillis();
        String studentName = payablePlanNewVo.getStudentName();
        if (StringUtils.isNotBlank(studentName)) {
            payablePlanNewVo.setStudentName(studentName.replace(" ", "").trim());
        }

        //针对like的字段转为小写
        if (GeneralTool.isNotEmpty(payablePlanNewVo.getStudentName())) {
            payablePlanNewVo.setStudentName(payablePlanNewVo.getStudentName().toLowerCase());
        }
        if (GeneralTool.isNotEmpty(payablePlanNewVo.getAgentName())) {
            payablePlanNewVo.setAgentName(payablePlanNewVo.getAgentName().toLowerCase());
        }
        if (GeneralTool.isNotEmpty(payablePlanNewVo.getBziName())) {
            payablePlanNewVo.setBziName(payablePlanNewVo.getBziName().toLowerCase());
        }
        if (GeneralTool.isNotEmpty(payablePlanNewVo.getBziNameSupplement())) {
            payablePlanNewVo.setBziNameSupplement(payablePlanNewVo.getBziNameSupplement().toLowerCase());
        }
        if (GeneralTool.isNotEmpty(payablePlanNewVo.getFkInvoiceNums())) {
            payablePlanNewVo.setFkInvoiceNums(payablePlanNewVo.getFkInvoiceNums().toLowerCase().replace("'", "_"));
        }
        if (GeneralTool.isNotEmpty(payablePlanNewVo.getSummary())) {
            payablePlanNewVo.setSummary(payablePlanNewVo.getSummary().replace(" ", "").trim());
        }
        if (GeneralTool.isNotEmpty(payablePlanNewVo.getSummary())) {
            payablePlanNewVo.setSummary(payablePlanNewVo.getSummary().toLowerCase());
        }
        if (GeneralTool.isNotEmpty(payablePlanNewVo.getCommissionMark())) {
            payablePlanNewVo.setCommissionMark(payablePlanNewVo.getCommissionMark().toLowerCase());
        }

        if (page != null) {
            IPage<PayablePlanNewVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            if (payablePlanNewVo.getIsInvoiceFlag()) {
                collect = payablePlanMapper.payablePlanDatas(iPage, payablePlanNewVo);
            } else {
                collect = dorisService.getDorisPayablePlanNewDtos(payablePlanNewVo, iPage);
            }
            page.setAll((int) iPage.getTotal());
        } else {
            collect = dorisService.getDorisPayablePlanNewDtos(payablePlanNewVo, null);
        }
        long fEndTime = System.currentTimeMillis();

        if (GeneralTool.isNotEmpty(collect)) {

            Set<Long> planIds = collect.stream().map(PayablePlanNewVo::getId).collect(Collectors.toSet());
            if (GeneralTool.isEmpty(planIds)) {
                planIds.add(0L);
            }

            List<PlanRemarkDetailResultVo> planRemarkDetailResultVos = payablePlanMapper.getPayablePlanRemarkDetails(planIds);
            Map<Long, List<SaleComment>> studentCommentMap = Maps.newHashMap();
            Map<Long, List<SaleComment>> studentOfferItemCommentMap = Maps.newHashMap();
            Map<Long, PlanRemarkDetailResultVo> planRemarkDetailResultDtoMap = Maps.newHashMap();
            if (GeneralTool.isNotEmpty(planRemarkDetailResultVos)) {
                planRemarkDetailResultDtoMap = planRemarkDetailResultVos.stream().collect(HashMap::new, (m, v) -> m.put(v.getId(), v), HashMap::putAll);
                Set<Long> studentIds = planRemarkDetailResultVos.stream().map(PlanRemarkDetailResultVo::getFkStudentId).filter(Objects::nonNull).collect(Collectors.toSet());
                if (GeneralTool.isEmpty(studentIds)) {
                    studentIds.add(0L);
                }
                Set<Long> studentOfferItemIds = planRemarkDetailResultVos.stream().map(PlanRemarkDetailResultVo::getFkStudentOfferItemId).filter(Objects::nonNull).collect(Collectors.toSet());
                if (GeneralTool.isEmpty(studentOfferItemIds)) {
                    studentOfferItemIds.add(0L);
                }
                List<SaleComment> studentComments = commentService.list(Wrappers.<SaleComment>lambdaQuery()
                        .in(SaleComment::getFkTableId, studentIds)
                        .eq(SaleComment::getFkTableName, TableEnum.SALE_STUDENT.key));
                List<SaleComment> studentOfferItemComments = commentService.list(Wrappers.<SaleComment>lambdaQuery()
                        .in(SaleComment::getFkTableId, studentOfferItemIds)
                        .eq(SaleComment::getFkTableName, TableEnum.SALE_STUDENT_OFFER_ITEM.key));

                if (GeneralTool.isNotEmpty(studentComments)) {
                    studentCommentMap = studentComments.stream().collect(Collectors.groupingBy(SaleComment::getFkTableId));
                }
                if (GeneralTool.isNotEmpty(studentOfferItemComments)) {
                    studentOfferItemCommentMap = studentOfferItemComments.stream().collect(Collectors.groupingBy(SaleComment::getFkTableId));
                }
            }


            Set<Long> ids = collect.stream().filter(f -> TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(f.getFkTypeKey())).map(PayablePlanNewVo::getFkTypeTargetId).collect(Collectors.toSet());
            if (GeneralTool.isEmpty(ids)) {
                ids.add(0L);
            }

            //bd编号
            Map<Long, String> bdMap = new HashMap<>();
            Set<Long> bdIds = collect.stream().map(PayablePlanNewVo::getFkStaffId).collect(Collectors.toSet());
            Set<Long> agentIds = collect.stream().map(PayablePlanNewVo::getFkAgentId).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(bdIds)) {
                List<StaffBdCode> staffBdCodes = staffBdCodeMapper.selectList(Wrappers.<StaffBdCode>lambdaQuery().in(StaffBdCode::getFkStaffId, bdIds));
                bdMap = staffBdCodes.stream().collect(Collectors.toMap(StaffBdCode::getFkStaffId, StaffBdCode::getBdCode));
            }
            Map<Long, List<AgentLabelVo>> agentLabelMap = new HashMap<>();
            if (GeneralTool.isNotEmpty(agentIds)) {
                agentLabelMap = getAgentLabelDataUtils.getAgentLabelMap(agentIds).getAgentLabelMap();
            }

            //延迟入学时间
            Map<Long, Date> maxDeferEntranceTimesMap = new HashMap<>();
            maxDeferEntranceTimesMap = studentOfferItemDeferEntranceService.getMaxDeferEntranceTimesMap(ids);

            List<SelItem> stepByIds = studentOfferItemMapper.getStepByIds(ids);
            Map<Long, Object> convert = ConvertUtils.convert(stepByIds);

            String fkTypeKey = payablePlanNewVo.getFkTypeKey();
            List<ReceivablePlanVo> receivablePlanVoList = new ArrayList<>();
            Map<Long, List<ReceivablePlanVo>> receivablePlanMap = new HashMap<>();
            if (StringUtils.isBlank(fkTypeKey)) {
                //TODO 改过
//                //留学申请
//                List<Long> itemIds = collect.stream().filter(receivablePlan -> TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(receivablePlan.getFkTypeKey()))
//                        .map(PayablePlan::getFkTypeTargetId).collect(Collectors.toList());
//                //留学服务费
//                List<Long> serviceFeeIds = collect.stream().filter(receivablePlan -> TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(receivablePlan.getFkTypeKey()))
//                        .map(PayablePlan::getFkTypeTargetId).collect(Collectors.toList());
//                //留学住宿
//                List<Long> accommodationIds = collect.stream()
//                        .filter(p -> TableEnum.SALE_STUDENT_ACCOMMODATION.key.equals(p.getFkTypeKey()))
//                        .map(PayablePlan::getFkTypeTargetId).collect(Collectors.toList());
//                //留学保险
//                List<Long> insuranceIds = collect.stream()
//                        .filter(p -> TableEnum.SALE_STUDENT_INSURANCE.key.equals(p.getFkTypeKey()))
//                        .map(PayablePlan::getFkTypeTargetId).collect(Collectors.toList());
                //留学申请
                List<Long> itemIds = collect.stream().filter(receivablePlan -> TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(receivablePlan.getFkTypeKey()))
                        .map(PayablePlanNewVo::getFkTypeTargetId).collect(Collectors.toList());
                //留学服务费
                List<Long> serviceFeeIds = collect.stream().filter(receivablePlan -> TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(receivablePlan.getFkTypeKey()))
                        .map(PayablePlanNewVo::getFkTypeTargetId).collect(Collectors.toList());
                //留学住宿
                List<Long> accommodationIds = collect.stream()
                        .filter(p -> TableEnum.SALE_STUDENT_ACCOMMODATION.key.equals(p.getFkTypeKey()))
                        .map(PayablePlanNewVo::getFkTypeTargetId).collect(Collectors.toList());
                //留学保险
                List<Long> insuranceIds = collect.stream()
                        .filter(p -> TableEnum.SALE_STUDENT_INSURANCE.key.equals(p.getFkTypeKey()))
                        .map(PayablePlanNewVo::getFkTypeTargetId).collect(Collectors.toList());


                //根据申请计划ids获取对应的应收计划
                if (GeneralTool.isNotEmpty(itemIds)) {
                    List<ReceivablePlanVo> offerItemReceivablePlanVos = receivablePlanMapper.getReceivablePlanByTargetsAndType(TableEnum.SALE_STUDENT_OFFER_ITEM.key, itemIds);
                    receivablePlanVoList.addAll(offerItemReceivablePlanVos);
                }
                if (GeneralTool.isNotEmpty(serviceFeeIds)) {
                    //根据申请费用计划ids获取对应的应收计划
                    List<ReceivablePlanVo> serviceFeeReceivablePlanVos = receivablePlanMapper.getReceivablePlanByTargetsAndType(TableEnum.SALE_STUDENT_SERVICE_FEE.key, serviceFeeIds);
                    receivablePlanVoList.addAll(serviceFeeReceivablePlanVos);
                }
                if (GeneralTool.isNotEmpty(accommodationIds)) {
                    //根据申请计划ids获取对应的应收计划
                    List<ReceivablePlanVo> accommodationReceivablePlanVos = receivablePlanMapper.getReceivablePlanByTargetsAndType(TableEnum.SALE_STUDENT_ACCOMMODATION.key, accommodationIds);
                    receivablePlanVoList.addAll(accommodationReceivablePlanVos);
                }
                if (GeneralTool.isNotEmpty(insuranceIds)) {
                    //根据申请计划ids获取对应的应收计划
                    List<ReceivablePlanVo> insuranceReceivablePlanVos = receivablePlanMapper.getReceivablePlanByTargetsAndType(TableEnum.SALE_STUDENT_INSURANCE.key, insuranceIds);
                    receivablePlanVoList.addAll(insuranceReceivablePlanVos);
                }
            } else {
                List<Long> targetIds = collect.stream()
                        .filter(p -> fkTypeKey.equals(p.getFkTypeKey()))
                        //TODO 改过
                        //.map(PayablePlan::getFkTypeTargetId).collect(Collectors.toList());
                        .map(PayablePlanNewVo::getFkTypeTargetId).collect(Collectors.toList());
                if (GeneralTool.isNotEmpty(targetIds)) {
                    receivablePlanVoList = receivablePlanMapper.getReceivablePlanByTargetsAndType(fkTypeKey, targetIds);
                }
            }
            if (GeneralTool.isNotEmpty(receivablePlanVoList)) {
                receivablePlanMap = receivablePlanVoList.stream().collect(Collectors.groupingBy(BaseEntity::getId));
            }
            Set<Long> countryIds = Arrays.stream(collect.stream()
                    .filter(f -> TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(f.getFkTypeKey()) && StringUtils.isNotBlank(f.getFkAreaCountryIds()))
                    .map(PayablePlanNewVo::getFkAreaCountryIds)
                    .collect(Collectors.joining(",")).split(",")).filter(StringUtils::isNotBlank).map(Long::valueOf).collect(Collectors.toSet());
            Map<Long, String> country = institutionCenterClient.getCountryFullNamesByIds(countryIds).getData();
            //封装留学服务费信息
            for (PayablePlanNewVo payablePlanNewDto : collect) {
                payablePlanNewDto.setTradeStatusName(LocaleMessageUtils.getMessage(ProjectExtraEnum.getEnum(payablePlanNewDto.getTradeStatus(), ProjectExtraEnum.AGENT_SETTLEMENT_STATUS).name()));
                if (TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(payablePlanNewDto.getFkTypeKey())) {
                    StringBuilder countryName = new StringBuilder();
                    String fkAreaCountryIds = payablePlanNewDto.getFkAreaCountryIds();
                    if (StringUtils.isNotBlank(fkAreaCountryIds)) {
                        String[] split = fkAreaCountryIds.split(",");
                        for (String s : split) {
                            countryName.append(country.get(Long.valueOf(s))).append("，");
                        }
                        countryName.delete(countryName.length() - 1, countryName.length());
                        payablePlanNewDto.setFkAreaCountryName(countryName.toString());
                    }
                }
                if (StringUtils.isNotBlank(payablePlanNewDto.getAgentNameNote())) {
                    payablePlanNewDto.setFkAgentName(payablePlanNewDto.getFkAgentName() + "(" + payablePlanNewDto.getAgentNameNote() + ")");
                }
                if (convert.containsKey(payablePlanNewDto.getFkTypeTargetId())) {
                    payablePlanNewDto.setStepName((String) convert.get(payablePlanNewDto.getFkTypeTargetId()));
                }
                if (GeneralTool.isNotEmpty(bdMap)) {
                    payablePlanNewDto.setBdCode(bdMap.get(payablePlanNewDto.getFkStaffId()));
                }
                if (GeneralTool.isNotEmpty(agentLabelMap)) {
                    payablePlanNewDto.setAgentLabelVos(agentLabelMap.get(payablePlanNewDto.getFkAgentId()));
                }
                payablePlanNewDto.setFkTypeName(TableEnum.getValue(payablePlanNewDto.getFkTypeKey()));
                //应付计划绑定目标id对应应收计划：应收金额、实收金额、收款差额
                if (GeneralTool.isNotEmpty(receivablePlanMap)
                        && (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(payablePlanNewDto.getFkTypeKey())
                        || TableEnum.SALE_STUDENT_ACCOMMODATION.key.equals(payablePlanNewDto.getFkTypeKey())
                        || TableEnum.SALE_STUDENT_INSURANCE.key.equals(payablePlanNewDto.getFkTypeKey())
                        || TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(payablePlanNewDto.getFkTypeKey())
                )) {
                    List<ReceivablePlanVo> receivablePlanVos = receivablePlanMap.get(payablePlanNewDto.getFkReceivablePlanId());
                    List<Map<String, Object>> receivablePlanList = new ArrayList<>();
                    if (GeneralTool.isNotEmpty(receivablePlanVos)) {
                        for (ReceivablePlanVo dto : receivablePlanVos) {
                            Map<String, Object> map = new HashMap<>();
                            map.put("receivableAmount", dto.getReceivableAmount());
                            map.put("actualReceivableAmount", dto.getActualReceivableAmount());
                            map.put("diffReceivableAmount", dto.getDiffReceivableAmount());
                            map.put("fkCurrencyTypeNum", dto.getFkCurrencyTypeNum());
                            map.put("fkCurrencyTypeName", dto.getFkCurrencyTypeName());
                            receivablePlanList.add(map);
                        }
                        payablePlanNewDto.setReceivablePlanList(receivablePlanList);
                    }
                }
                if (payablePlanNewDto.getIsDeferEntrance() && TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(payablePlanNewDto.getFkTypeKey())
                        && GeneralTool.isNotEmpty(maxDeferEntranceTimesMap)) {
//                    payablePlanNewDto.setMaxDeferEntranceTimes(studentOfferItemDeferEntranceTimeMapper.getLastDeferEntranceByItemId(payablePlanNewDto.getFkTypeTargetId()));
                    payablePlanNewDto.setMaxDeferEntranceTimes(maxDeferEntranceTimesMap.get(payablePlanNewDto.getFkTypeTargetId()));
                }
//                if (GeneralTool.isNotEmpty(accommodationReceivablePlanMap) && payablePlanNewDto.getFkTypeKey().equals(TableEnum.SALE_STUDENT_ACCOMMODATION.key)) {
//                    List<ReceivablePlanVo> receivablePlanDtos = accommodationReceivablePlanMap.get(payablePlanNewDto.getFkReceivablePlanId());
//                    List<Map<String,Object>> receivablePlanList = new ArrayList<>();
//                    if (GeneralTool.isNotEmpty(receivablePlanDtos)) {
//                        for (ReceivablePlanVo vo : receivablePlanDtos) {
//                            Map<String,Object> map = new HashMap<>();
//                            map.put("receivableAmount", vo.getReceivableAmount());
//                            map.put("actualReceivableAmount", vo.getActualReceivableAmount());
//                            map.put("diffReceivableAmount", vo.getDiffReceivableAmount());
//                            map.put("fkCurrencyTypeNum", vo.getFkCurrencyTypeNum());
//                            map.put("fkCurrencyTypeName", vo.getFkCurrencyTypeName());
//                            receivablePlanList.add(map);
//                        }
//                        payablePlanNewDto.setReceivablePlanList(receivablePlanList);
//                    }
//                }
//                if (GeneralTool.isNotEmpty(insuranceReceivablePlanMap) && payablePlanNewDto.getFkTypeKey().equals(TableEnum.SALE_STUDENT_INSURANCE.key)) {
//                    List<ReceivablePlanVo> receivablePlanDtos = insuranceReceivablePlanMap.get(payablePlanNewDto.getFkReceivablePlanId());
//                    List<Map<String,Object>> receivablePlanList = new ArrayList<>();
//                    if (GeneralTool.isNotEmpty(receivablePlanDtos)) {
//                        for (ReceivablePlanVo vo : receivablePlanDtos) {
//                            Map<String,Object> map = new HashMap<>();
//                            map.put("receivableAmount", vo.getReceivableAmount());
//                            map.put("actualReceivableAmount", vo.getActualReceivableAmount());
//                            map.put("diffReceivableAmount", vo.getDiffReceivableAmount());
//                            map.put("fkCurrencyTypeNum", vo.getFkCurrencyTypeNum());
//                            map.put("fkCurrencyTypeName", vo.getFkCurrencyTypeName());
//                            receivablePlanList.add(map);
//                        }
//                        payablePlanNewDto.setReceivablePlanList(receivablePlanList);
//                    }
//                }
                if (GeneralTool.isNotEmpty(planRemarkDetailResultDtoMap)) {
                    PlanRemarkDetailResultVo planRemarkDetailResultVo = planRemarkDetailResultDtoMap.get(payablePlanNewDto.getId());
                    PlanRemarkDetailVo planRemarkDetailVo = BeanCopyUtils.objClone(planRemarkDetailResultVo, PlanRemarkDetailVo::new);
                    if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(payablePlanNewDto.getFkTypeKey())) {
                        assert planRemarkDetailVo != null;
                        if (GeneralTool.isNotEmpty(studentCommentMap)
                                && GeneralTool.isNotEmpty(studentCommentMap.get(planRemarkDetailResultVo.getFkStudentId()))) {
                            List<SaleComment> saleComments = studentCommentMap.get(planRemarkDetailResultVo.getFkStudentId());
                            List<String> studentCommentList = saleComments.stream().map(SaleComment::getComment).collect(Collectors.toList());
                            planRemarkDetailVo.setStudentComments(studentCommentList);
                        }
                        if (GeneralTool.isNotEmpty(studentOfferItemCommentMap)
                                && GeneralTool.isNotEmpty(studentOfferItemCommentMap.get(planRemarkDetailResultVo.getFkStudentOfferItemId()))) {
                            List<SaleComment> saleComments = studentOfferItemCommentMap.get(planRemarkDetailResultVo.getFkStudentOfferItemId());
                            List<String> studentOfferItemCommentList = saleComments.stream().map(SaleComment::getComment).collect(Collectors.toList());
                            planRemarkDetailVo.setStudentOfferItemComments(studentOfferItemCommentList);
                        }
                        if (GeneralTool.isNotEmpty(planRemarkDetailResultVo.getEducationProject())) {
                            planRemarkDetailVo.setEducationProjectName(ProjectExtraEnum.getValueByKey(planRemarkDetailResultVo.getEducationProject(), ProjectExtraEnum.EDUCATION_PROJECT));
                        }
                        if (GeneralTool.isNotEmpty(planRemarkDetailResultVo.getEducationDegree())) {
                            planRemarkDetailVo.setEducationDegreeName(ProjectExtraEnum.getValueByKey(planRemarkDetailResultVo.getEducationDegree(), ProjectExtraEnum.EDUCATION_DEGREE));
                        }
                    }

                    payablePlanNewDto.setPlanRemarkDetailDto(planRemarkDetailVo);

                    if (GeneralTool.isEmpty(planRemarkDetailVo)
                            || (GeneralTool.isNotEmpty(planRemarkDetailVo) && MyStringUtils.isAllFieldNull(planRemarkDetailVo))) {
                        payablePlanNewDto.setPlanRemarkDetailDto(null);
                    }
                }
            }
        }

        if (GeneralTool.isNotEmpty(times)) {
            times[0] = String.valueOf((fEndTime - fStartTime));
        }
        return collect;
    }

    /**
     * 查询数据库仓库 应付计划列表
     *
     * @Date 16:15 2022/6/27
     * <AUTHOR>
     */
    @DS("saledb-doris")
    public List<PayablePlanNewVo> getDorisPayablePlanNewDtos(PayablePlanNewQueryDto payablePlanNewVo, IPage<PayablePlanNewVo> iPage) {
        return payablePlanMapper.payablePlanDatas(iPage, payablePlanNewVo);
    }

//    /**
//     * 应付计划取消预付按钮
//     *
//     * @Date 18:16 2022/4/19
//     * <AUTHOR>
//     */
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void cancelPrepaymentButton(PrepaymentButtonCancelDto prepaymentButtonCancelDto) {
//        //该应付计划的分期数据如果在结算中或者结算完了，不能取消预付了
//        List<PayablePlanSettlementInstallment> settlementInstallments = payablePlanSettlementInstallmentMapper.selectList(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
//                .ne(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_UNTREATED.key)
//                .in(PayablePlanSettlementInstallment::getFkPayablePlanId, prepaymentButtonCancelDto.getPayablePlanIds()));
//        if (GeneralTool.isNotEmpty(settlementInstallments)) {
//            //预付中，无法取消预付
//            throw new GetServiceException(LocaleMessageUtils.getMessage("NOT_PAY_ADVANCE_SETTLEMENT_INSTALLMENT"));
//        }
//        //应付计划为学校提供商类型或者业务提供商类型,不用生成给代理的佣金
//        Set<String> set = new HashSet<>();
//        set.add(TableEnum.INSTITUTION_PROVIDER.key);
//        set.add(TableEnum.SALE_BUSINESS_PROVIDER.key);
//        List<PayablePlan> checkPayablePlans = payablePlanMapper.selectList(Wrappers.<PayablePlan>lambdaQuery()
//                .in(PayablePlan::getId, prepaymentButtonCancelDto.getPayablePlanIds())
//                .in(PayablePlan::getFkTypeKey, set));
//        if (GeneralTool.isNotEmpty(checkPayablePlans)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("PAYABLE_PLANS_CANNOT_GENERATE_COMMISSIONS"));
//        }
//
//        //没有预付中 说明生成的预付分期数据 还未进入结算 直接删除，并改变应付计划预付状态
//        List<PayablePlan> payablePlans = payablePlanMapper.selectList(Wrappers.<PayablePlan>lambdaQuery().in(PayablePlan::getId, prepaymentButtonCancelDto.getPayablePlanIds()).eq(PayablePlan::getIsPayInAdvance, true));
//        payablePlanSettlementInstallmentMapper.delete(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
//                .in(PayablePlanSettlementInstallment::getFkPayablePlanId, prepaymentButtonCancelDto.getPayablePlanIds())
//                .eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_UNTREATED.key)
//                .isNull(PayablePlanSettlementInstallment::getFkReceiptFormItemId));
//        for (PayablePlan payablePlan : payablePlans) {
//            payablePlan.setIsPayInAdvance(false);
//            payablePlan.setPayInAdvancePercent(null);
//            utilService.setUpdateInfo(payablePlan);
//            payablePlanMapper.updateByIdWithNull(payablePlan);
//        }
//
//    }


    /**
     * feign 根据应收计划id获取对应的应付计划信息
     *
     * @Date 23:55 2022/4/21
     * <AUTHOR>
     */
    @Override
    public PayablePlan getPayablePlanByReceivablePlanId(Long fkReceivablePlanId) {
        return payablePlanMapper.selectOne(Wrappers.<PayablePlan>lambdaQuery().eq(PayablePlan::getFkReceivablePlanId, fkReceivablePlanId)
                .ne(PayablePlan::getStatus, 0));
    }

    @Override
    public Boolean batchUpdateByIds(List<PayablePlan> payablePlans) {
        if (payablePlans.isEmpty()) {
            return false;
        }
        return updateBatchById(payablePlans, payablePlans.size());
    }

    @Override
    public PayablePlanVo findPayablePlanById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        PayablePlan payablePlan = payablePlanMapper.selectById(id);
        PayablePlanVo payablePlanVo = BeanCopyUtils.objClone(payablePlan, PayablePlanVo::new);
        List<PaymentFormVo> payFormListFeign = new ArrayList<>();
        Result<List<PaymentFormVo>> result = financeCenterClient.getPayFormList(id);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            payFormListFeign.addAll(result.getData());
        }
        //过滤作废的子单
        payFormListFeign = payFormListFeign.stream().filter(paymentFormDto -> paymentFormDto.getStatus() == 1).collect(Collectors.toList());

        BigDecimal amountPayable = new BigDecimal(0);
        BigDecimal amountExchangeRate = new BigDecimal(0);
        BigDecimal actualPayableAmount = BigDecimal.ZERO;
        for (PaymentFormVo paymentFormVo : payFormListFeign) {
            amountPayable = amountPayable.add(paymentFormVo.getAmountPayable()).add(DataConverter.bigDecimalNullConvert(paymentFormVo.getChildAmountExchangeRate()));
            amountExchangeRate = amountExchangeRate.add(DataConverter.bigDecimalNullConvert(paymentFormVo.getAmountExchangeRate()));
            actualPayableAmount = actualPayableAmount.add(DataConverter.bigDecimalNullConvert(paymentFormVo.getAmountPayment()));
        }
        Set<Long> agentIds = new HashSet<>();

        payablePlanVo.setAmountPayable(amountPayable);
        payablePlanVo.setAmountExchangeRate(amountExchangeRate);
        payablePlanVo.setActualPayableAmount(actualPayableAmount);
        if (GeneralTool.isNotEmpty(payablePlan.getPayableAmount())) {
            //payablePlanVo.setDifference(amountPayable.subtract(payablePlan.getPayableAmount()));
            payablePlanVo.setDifference(payablePlan.getPayableAmount().subtract(amountPayable));
        } else {
            payablePlanVo.setDifference(amountPayable);
        }
        Result<String> result_ = financeCenterClient.getCurrencyNameByNum(payablePlan.getFkCurrencyTypeNum());
        if (result_.isSuccess() && GeneralTool.isNotEmpty(result_.getData())) {
            payablePlanVo.setEquivalentCurrencyTypeName(result_.getData());
            payablePlanVo.setFkCurrencyTypeName(result_.getData());
        }

        Map<String, String> companyMap = getCompanyMap();
        setName(payablePlanVo, companyMap);
        payablePlanVo.setCompanyName(companyMap.get(String.valueOf(payablePlanVo.getFkCompanyId())));
        return payablePlanVo;
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        payablePlanMapper.deleteById(id);
    }

    @Override
    public List<PayablePlanVo> getPayablePlanByIds(Set<Long> ids) {
        if (GeneralTool.isEmpty(ids)) {
            ids.add(0L);
        }
        List<PayablePlanVo> payablePlanVos = payablePlanMapper.getPayablePlanByIds(ids);
        if (GeneralTool.isEmpty(payablePlanVos)) {
            return null;
        }
        Set<Long> targetId = payablePlanVos.stream().map(PayablePlanVo::getFkTypeTargetId).collect(Collectors.toSet());
        Map<Long, StudentOfferItemVo> offerItemByIds = offerItemService.findOfferItemByIds(targetId);
        Set<Long> itemId = offerItemByIds.values().stream().map(StudentOfferItemVo::getId).collect(Collectors.toSet());
        Map<Long, String> studentNameByItemIds = offerItemService.getStudentNameByItemIds(itemId);
        Set<Long> offerId = offerItemByIds.values().stream().map(StudentOfferItemVo::getFkStudentOfferId).collect(Collectors.toSet());
        Map<Long, String> agentNameByOfferIds = offerService.getAgentNameByOfferIds(offerId);
        Map<Long, String> targetNames = getTargetNames(new ArrayList<>(offerItemByIds.values()), studentNameByItemIds, agentNameByOfferIds);
        for (PayablePlanVo payablePlanVo : payablePlanVos) {
            payablePlanVo.setFkTypeName(TableEnum.getValue(payablePlanVo.getFkTypeKey()));
            payablePlanVo.setTargetNames(targetNames.get(payablePlanVo.getFkTypeTargetId()));
        }
        return payablePlanVos;
    }

    /**
     * 根据应付ids获取应付详情
     *
     * @param ids
     * @return
     */
    @Override
    public List<PayablePlanVo> getPayablePlanDetailsByIds(Set<Long> ids) {
        if (ids.isEmpty()) {
            return Collections.emptyList();
        }
        List<PayablePlanVo> payablePlanVos = payablePlanMapper.getPayablePlanByIds(ids);
        setListName(payablePlanVos);
        return payablePlanVos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<MediaAndAttachedVo> addMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.SALE_PAYABLE.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;

    }

    @Override
    public List<MediaAndAttachedVo> getMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.SALE_PAYABLE.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);
    }

    @Override
    public void unablePayable(Long id, Long status) {
        Result<Boolean> result = financeCenterClient.deleteSettlementCommissionByPayablePlanId(id);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        PayablePlan payablePlan = payablePlanMapper.selectById(id);
        payablePlan.setStatus(Math.toIntExact(status));
        utilService.setUpdateInfo(payablePlan);
        payablePlanMapper.updateById(payablePlan);
    }

    @Override
    public List<PayablePlanVo> getPayablePlan(String typeKey, Long targetId) {
        if (GeneralTool.isEmpty(typeKey) || GeneralTool.isEmpty(targetId)) {
            return null;
        }
//        Example example = new Example(PayablePlan.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkTypeKey", typeKey);
//        criteria.andEqualTo("fkTypeTargetId", targetId);
//        List<PayablePlan> payablePlans = payablePlanMapper.selectByExample(example);
        List<PayablePlan> payablePlans = payablePlanMapper.selectList(Wrappers.<PayablePlan>lambdaQuery()
                .eq(PayablePlan::getFkTypeKey, typeKey)
                .eq(PayablePlan::getFkTypeTargetId, targetId).eq(PayablePlan::getStatus, 1));
        if (GeneralTool.isEmpty(payablePlans)) {
            return null;
        }
        List<PayablePlanVo> collect =
                payablePlans.stream().map(payablePlan -> BeanCopyUtils.objClone(payablePlan, PayablePlanVo::new)).collect(Collectors.toList());
        Map<String, String> companyMap = getCompanyMap();
        for (PayablePlanVo payablePlanVo : collect) {
            Result<String> result_ = financeCenterClient.getCurrencyNameByNum(payablePlanVo.getFkCurrencyTypeNum());
            if (result_.isSuccess() && GeneralTool.isNotEmpty(result_.getData())) {
                payablePlanVo.setEquivalentCurrencyTypeName(result_.getData());
                payablePlanVo.setFkCurrencyTypeName(result_.getData());
            }
            setName(payablePlanVo, companyMap);
        }
        return collect;
    }

    /**
     * 获取实付信息
     *
     * @param ids
     * @return
     */
    @Override
    public List<PublicPayFormDetailVo> getPaidAmountInfo(List<Long> ids) {
        if (GeneralTool.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return payablePlanMapper.getPaidAmountInfoByIds(ids);
    }

    @Override
    public List<Long> getPayablePlanId(String typeKey, Long targetId) {
        LambdaQueryWrapper<PayablePlan> lambdaQueryWrapper = Wrappers.<PayablePlan>lambdaQuery();
        if (GeneralTool.isNotEmpty(typeKey)) {
            lambdaQueryWrapper.eq(PayablePlan::getFkTypeKey, typeKey);
        }
        if (GeneralTool.isNotEmpty(targetId)) {
            lambdaQueryWrapper.eq(PayablePlan::getFkTypeTargetId, targetId);
        }
        List<PayablePlan> payablePlans = payablePlanMapper.selectList(lambdaQueryWrapper);

        return payablePlans.stream().map(PayablePlan::getId).collect(Collectors.toList());
    }


    /**
     * 根据目标类型，目标对象ids获取应付
     *
     * @param typeKey
     * @param targetIds
     * @return
     */
    @Override
    public Map<Long, List<Long>> getPayablePlanIds(String typeKey, Set<Long> targetIds) {
        LambdaQueryWrapper<PayablePlan> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(typeKey)) {
            lambdaQueryWrapper.eq(PayablePlan::getFkTypeKey, typeKey);
        }
        if (GeneralTool.isNotEmpty(targetIds)) {
            lambdaQueryWrapper.in(PayablePlan::getFkTypeTargetId, targetIds);
        }
        // 去除作废的应付计划
        lambdaQueryWrapper.ne(PayablePlan::getStatus, 0);
        List<PayablePlan> receivablePlans = payablePlanMapper.selectList(lambdaQueryWrapper);
        Map<Long, List<PayablePlan>> collect = receivablePlans.stream().collect(Collectors.groupingBy(PayablePlan::getFkTypeTargetId));
        Map<Long, List<Long>> map = new HashMap<>();
        if (GeneralTool.isNotEmpty(collect)) {
            collect.forEach((k, v) -> map.put(k, v.stream().distinct().map(PayablePlan::getId).collect(Collectors.toList())));
        }
        return map;
    }

    /**
     * @return
     * @Description：feign 根据应付计划id查询应付计划应付金额
     * @Param
     * @Date 17:00 2021/4/23
     * <AUTHOR>
     */
    @Override
    public BigDecimal getPayablePlanAmountById(Long id) {
        PayablePlan payablePlan = payablePlanMapper.selectById(id);
        if (payablePlan == null) {
            return BigDecimal.ZERO;
        }
        return payablePlan.getPayableAmount();
    }

    /**
     * 生成应付计划
     *
     * @Date 12:35 2021/7/12
     * <AUTHOR>
     */
    @Override
    public void generatePayablePlan(ContractFormula contractFormula, StudentOfferItem studentOfferItem, BigDecimal fee,
                                    List<ContractFormulaCommission> contractFormulaCommissions) {
        logger.info("generatePayablePlan<|>生成应付计划开始<|>num:{}<|>studentOfferItem:{}", studentOfferItem.getNum(), JSONObject.toJSONString(studentOfferItem));
        StudentOffer studentOffer = studentOfferMapper.selectById(studentOfferItem.getFkStudentOfferId());
        if (GeneralTool.isEmpty(studentOffer)) {
            logger.error("generatePayablePlan<|>生成应付计划 学生申请方案不存在<|> studentOfferItem:{}", JSONObject.toJSONString(studentOfferItem));
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_offer_null"));
        }

//        //佣金期数
//        Map<Integer, String> steps = new HashMap<>();
//        //判断是否有获取N阶段佣金业务场景
//        boolean flag = false;
//        if (GeneralTool.isNotEmpty(studentOfferItem.getConditionType())) {
//            String conditionType = studentOfferItem.getConditionType();
//            if (GeneralTool.isNotEmpty(ReceivablePlanServiceImpl.ConditionTypeAndStepsEnum.getValue(conditionType))) {
//                flag = true;
//                steps.put(ReceivablePlanServiceImpl.ConditionTypeAndStepsEnum.getStep(conditionType), ReceivablePlanServiceImpl.ConditionTypeAndStepsEnum.getValue(conditionType));
//            }
//        }
        //代理合同公式
//        List<AgentContractFormula> agentContractFormulas = agentContractFormulaMapper.getAgentContractFormulasByFormula(contractFormula, studentOffer.getFkAgentId(), studentOfferItem.getFkCurrencyTypeNum());
        List<AgentContractFormula> agentContractFormulas = agentContractFormulaMapper.getAgentContractFormulasByFormula(studentOffer.getFkAgentId(), studentOfferItem.getFkCurrencyTypeNum());
        //按代理合同公式计算
        if (GeneralTool.isNotEmpty(agentContractFormulas)) {
            logger.info("generatePayablePlan<|>按代理合同公式应付计算开始<|>num:{}<|>studentOfferItem:{}", studentOfferItem.getNum(), JSONObject.toJSONString(studentOfferItem));
            for (AgentContractFormula agentContractFormula : agentContractFormulas) {
//                Example example = new Example(AgentContractFormulaCommission.class);
//                example.createCriteria().andEqualTo("fkAgentContractFormulaId", agentContractFormula.getId());
//                List<AgentContractFormulaCommission> agentContractFormulaCommissions = agentContractFormulaCommissionMapper.selectByExample(example);
                List<AgentContractFormulaCommission> agentContractFormulaCommissions = agentContractFormulaCommissionMapper.selectList(Wrappers.<AgentContractFormulaCommission>lambdaQuery()
                        .eq(AgentContractFormulaCommission::getFkAgentContractFormulaId, agentContractFormula.getId()));
                BigDecimal payableAgentAmountNum = new BigDecimal(0);
                for (int i = 0; i < agentContractFormulaCommissions.size(); i++) {
                    PayablePlan payablePlan = new PayablePlan();
                    AgentContractFormulaCommission agentContractFormulaCommission = agentContractFormulaCommissions.get(i);
                    //当前期数
                    Integer step;
//                    //获取N阶段佣金业务场景
//                    if (flag) {
//                        //当前期数
//                        step = agentContractFormulaCommission.getStep();
//                        //当前期数和学习计划获取第N阶段佣金业务场景不匹配，读取下一阶段
//                        if (GeneralTool.isEmpty(steps.get(step))) {
//                            continue;
//                        }
//                    }
                    // 如果佣金为0，读取下一阶段
//                    boolean commissionRateAgFlag = GeneralTool.isNotEmpty(agentContractFormulaCommission.getCommissionRateAg()) && agentContractFormulaCommission.getCommissionRateAg().compareTo(BigDecimal.ZERO) <= 0 ;
//                    boolean fixedAmountAgFlag = GeneralTool.isNotEmpty(agentContractFormulaCommission.getFixedAmountAg()) && agentContractFormulaCommission.getFixedAmountAg().compareTo(BigDecimal.ZERO) <= 0;
//                    if (commissionRateAgFlag || fixedAmountAgFlag) {
//                        continue;
//                    }

                    payablePlan.setStatus(1);
                    payablePlan.setFkTypeKey(TableEnum.SALE_STUDENT_OFFER_ITEM.key);
                    payablePlan.setFkTypeTargetId(studentOfferItem.getId());
                    payablePlan.setFkCurrencyTypeNum(contractFormula.getFkCurrencyTypeNum());
                    payablePlan.setTuitionAmount(fee);
                    payablePlan.setCommissionRate(agentContractFormulaCommission.getCommissionRateAg());
                    payablePlan.setFixedAmount(agentContractFormulaCommission.getFixedAmountAg());
                    if (GeneralTool.isNotEmpty(agentContractFormulaCommission.getCommissionRateAg())) {
                        BigDecimal payableAmount = BigDecimal.valueOf(agentContractFormulaCommission.getCommissionRateAg().doubleValue() * fee.doubleValue() / 100);
                        //存在代理返佣佣金上限
                        if (GeneralTool.isNotEmpty(agentContractFormulaCommission.getLimitAmountAg())) {
                            //代理返佣大于代理返佣佣金上限
                            if (agentContractFormulaCommission.getLimitAmountAg().compareTo(payableAmount) < 0) {
                                payableAmount = agentContractFormulaCommission.getLimitAmountAg();
                            }
                        }
                        //存在返佣总佣金上限
                        if (GeneralTool.isNotEmpty(agentContractFormula.getLimitAmountAg())) {
                            //累计返佣总金额已经 >= 返佣总佣金上限 后续阶段返佣佣金都为0
                            if (payableAgentAmountNum.compareTo(agentContractFormula.getLimitAmountAg()) >= 0) {
                                continue;
                            } else {
                                payableAgentAmountNum = payableAgentAmountNum.add(payableAmount);
                                //累计总金额+现阶段佣金金额 > 总佣金上限
                                if (payableAgentAmountNum.compareTo(agentContractFormula.getLimitAmountAg()) > 0) {
                                    //佣金 = 佣金上限 - 原有佣金 = 剩余佣金份额      累计金额 = 佣金上限
                                    payableAmount = contractFormula.getLimitAmountAg().subtract(payableAgentAmountNum.subtract(payableAmount));
//                                    payableAmount = payableAmount.subtract(payableAgentAmountNum.subtract(agentContractFormula.getLimitAmountAg()));
                                    payableAgentAmountNum = agentContractFormula.getLimitAmountAg();
                                }
                            }
                        }
                        payablePlan.setPayableAmount(payableAmount);
                        if (contractFormula.getFormulaType().equals(ProjectExtraEnum.AWARD.key) || contractFormula.getFormulaType().equals(ProjectExtraEnum.ONE_AWARD.key)) {
                            payablePlan.setBonusAmount(payableAmount);
                        }
                    } else if (GeneralTool.isNotEmpty(agentContractFormulaCommission.getFixedAmountAg())) {
                        BigDecimal receivableAmount = agentContractFormulaCommission.getFixedAmountAg();
                        if (GeneralTool.isNotEmpty(contractFormula.getLimitAmountAg())) {
                            //累计总金额已经 >= 总佣金上限 后续阶段不生成佣金
                            if (payableAgentAmountNum.compareTo(contractFormula.getLimitAmountAg()) >= 0) {
                                continue;
                            } else {
                                payableAgentAmountNum = payableAgentAmountNum.add(receivableAmount);
                                //累计总金额+现阶段佣金金额 > 总佣金上限
                                if (payableAgentAmountNum.compareTo(contractFormula.getLimitAmountAg()) > 0) {
                                    //佣金 = 佣金上限 - 原有佣金 = 剩余佣金份额      累计金额 = 佣金上限
                                    receivableAmount = contractFormula.getLimitAmountAg().subtract(payableAgentAmountNum.subtract(receivableAmount));
//                                    receivableAmount = receivableAmount.subtract(payableAgentAmountNum.subtract(agentContractFormula.getLimitAmountAg()));
                                    payableAgentAmountNum = contractFormula.getLimitAmountAg();
                                }
                            }
                        }
                        payablePlan.setPayableAmount(receivableAmount);
                        payablePlan.setFixedAmount(receivableAmount);
                        if (contractFormula.getFormulaType().equals(ProjectExtraEnum.AWARD.key) || contractFormula.getFormulaType().equals(ProjectExtraEnum.ONE_AWARD.key)) {
                            payablePlan.setBonusAmount(receivableAmount);
                        }
                    }

//                    if (payablePlan.getPayableAmount() == null || payablePlan.getPayableAmount().compareTo(BigDecimal.ZERO) < 1) {
//                        logger.error("generatePayablePlan<|>代理合同公式返佣金额异常:计算得出应付金额为0<|>receivableAmount:{}<|>studentOfferItem:{}<|>ContractFormula:{}",
//                                payablePlan.getPayableAmount(), JSONObject.toJSONString(studentOfferItem), JSONObject.toJSONString(contractFormula));
//                        throw new GetServiceException(LocaleMessageUtils.getMessage("receivable_amount_error"));
//                    }
                    payablePlan.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
                    utilService.updateUserInfoToEntity(payablePlan);
                    payablePlan.setIsPayInAdvance(false);
                    payablePlanMapper.insert(payablePlan);
                    PayablePlanContractFormula rpcf = new PayablePlanContractFormula();
                    rpcf.setFkContractFormulaId(contractFormula.getId());
                    rpcf.setFkPayablePlanId(payablePlan.getId());
                    utilService.updateUserInfoToEntity(rpcf);
                    payablePlanContractFormulaMapper.insert(rpcf);
                }
            }
            logger.info("generatePayablePlan<|>代理合同应付公式计算结束<|>num:{}<|>studentOfferItem:{}", studentOfferItem.getNum(), JSONObject.toJSONString(studentOfferItem));
            //按提供商合同公式计算
        } else {
            logger.info("generatePayablePlan<|>提供商合同公式应付计算开始<|>num:{}<|>studentOfferItem:{}", studentOfferItem.getNum(), JSONObject.toJSONString(studentOfferItem));
            BigDecimal payableAmountNum = new BigDecimal(0);
            for (ContractFormulaCommission contractFormulaCommission : contractFormulaCommissions) {
                PayablePlan payablePlan = new PayablePlan();
                //当前期数
//                Integer step;
//                //获取N阶段佣金业务场景
//                if (flag) {
//                    //当前期数
//                    step = contractFormulaCommission.getStep();
//                    //当前期数和学习计划获取第N阶段佣金业务场景不匹配，读取下一阶段
//                    if (GeneralTool.isEmpty(steps.get(step))) {
//                        continue;
//                    }
//                }

                //佣金为0，不生成公式
//                boolean commissionRateAgFlag = GeneralTool.isNotEmpty(contractFormulaCommission.getCommissionRateAg()) && contractFormulaCommission.getCommissionRateAg().compareTo(BigDecimal.ZERO) > 0;
//                boolean fixedAmountAgFlag = GeneralTool.isNotEmpty(contractFormulaCommission.getFixedAmountAg()) && contractFormulaCommission.getFixedAmountAg().compareTo(BigDecimal.ZERO) > 0;
//                if (commissionRateAgFlag || fixedAmountAgFlag) {
                payablePlan.setStatus(1);
                payablePlan.setFkTypeKey(TableEnum.SALE_STUDENT_OFFER_ITEM.key);
                payablePlan.setFkTypeTargetId(studentOfferItem.getId());
                payablePlan.setFkCurrencyTypeNum(contractFormula.getFkCurrencyTypeNum());
                payablePlan.setTuitionAmount(fee);
                payablePlan.setCommissionRate(contractFormulaCommission.getCommissionRateAg());
                payablePlan.setFixedAmount(contractFormulaCommission.getFixedAmountAg());

                //应付金额
                if (GeneralTool.isNotEmpty(contractFormulaCommission.getCommissionRateAg())) {
                    BigDecimal payableAmount = BigDecimal.valueOf(contractFormulaCommission.getCommissionRateAg().doubleValue() * fee.doubleValue() / 100);

                    //存在代理返佣佣金上限
                    if (GeneralTool.isNotEmpty(contractFormulaCommission.getLimitAmountAg())) {
                        //代理返佣大于代理返佣佣金上限
                        if (contractFormulaCommission.getLimitAmountAg().compareTo(payableAmount) < 0) {
                            payableAmount = contractFormulaCommission.getLimitAmountAg();
                        }
                    }
                    if (GeneralTool.isNotEmpty(contractFormula.getLimitAmountAg())) {
                        //累计返佣总金额已经 >= 返佣总佣金上限 后续阶段不生成应付计划
                        if (payableAmountNum.compareTo(contractFormula.getLimitAmountAg()) >= 0) {
                            return;
                        } else {
                            payableAmountNum = payableAmountNum.add(payableAmount);
                            //累计总金额+现阶段佣金金额 > 总佣金上限
                            if (payableAmountNum.compareTo(contractFormula.getLimitAmountAg()) > 0) {
                                //佣金 = 佣金上限 - 原有佣金 = 剩余佣金份额      累计金额 = 佣金上限
                                payableAmount = contractFormula.getLimitAmountAg().subtract(payableAmountNum.subtract(payableAmount));
//                                    payableAmount = payableAmount.subtract(payableAmountNum.subtract(payableAmount));
                                payableAmountNum = contractFormula.getLimitAmountAg();
                            }
                        }
                    }
                    payablePlan.setPayableAmount(payableAmount);
                    if (contractFormula.getFormulaType().equals(ProjectExtraEnum.AWARD.key) || contractFormula.getFormulaType().equals(ProjectExtraEnum.ONE_AWARD.key)) {
                        payablePlan.setBonusAmount(payableAmount);
                    }
                } else if (GeneralTool.isNotEmpty(contractFormulaCommission.getFixedAmountAg())) {
                    BigDecimal receivableAmount = contractFormulaCommission.getFixedAmountAg();
                    if (GeneralTool.isNotEmpty(contractFormula.getLimitAmountAg())) {
                        //累计总金额已经 >= 总佣金上限 后续阶段不生成佣金
                        if (payableAmountNum.compareTo(contractFormula.getLimitAmountAg()) >= 0) {
                            continue;
                        } else {
                            payableAmountNum = payableAmountNum.add(receivableAmount);
                            //累计总金额+现阶段佣金金额 > 总佣金上限
                            if (payableAmountNum.compareTo(contractFormula.getLimitAmountAg()) > 0) {
                                //佣金 = 佣金上限 - 原有佣金 = 剩余佣金份额      累计金额 = 佣金上限
                                receivableAmount = contractFormula.getLimitAmountAg().subtract(payableAmountNum.subtract(receivableAmount));
//                                    receivableAmount = receivableAmount.subtract(payableAmountNum.subtract(receivableAmount));
                                payableAmountNum = contractFormula.getLimitAmountAg();
                            }
                        }
                    }
                    payablePlan.setPayableAmount(receivableAmount);
                    payablePlan.setFixedAmount(receivableAmount);
                    if (contractFormula.getFormulaType().equals(ProjectExtraEnum.AWARD.key) || contractFormula.getFormulaType().equals(ProjectExtraEnum.ONE_AWARD.key)) {
                        payablePlan.setBonusAmount(receivableAmount);
                    }
                }
//                    if (payablePlan.getPayableAmount() == null || payablePlan.getPayableAmount().compareTo(BigDecimal.ZERO) < 1) {
//                        logger.error("generatePayablePlan<|>合同公式返佣金额异常:计算得出应付金额为0<|>receivableAmount:{}<|>studentOfferItem:{}<|>ContractFormula:{}",
//                                payablePlan.getPayableAmount(), JSONObject.toJSONString(studentOfferItem),JSONObject.toJSONString(contractFormula));
//                        throw new GetServiceException(LocaleMessageUtils.getMessage("receivable_amount_error"));
//                    }
                payablePlan.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
                utilService.updateUserInfoToEntity(payablePlan);
                payablePlan.setIsPayInAdvance(false);
                payablePlanMapper.insert(payablePlan);
                PayablePlanContractFormula rpcf = new PayablePlanContractFormula();
                rpcf.setFkContractFormulaId(contractFormula.getId());
                rpcf.setFkPayablePlanId(payablePlan.getId());
                utilService.updateUserInfoToEntity(rpcf);
                payablePlanContractFormulaMapper.insert(rpcf);
//                }
            }
            logger.info("generatePayablePlan<|>提供商合同公式应付计算结束<|>num:{}<|>studentOfferItem:{}", studentOfferItem.getNum(), JSONObject.toJSONString(studentOfferItem));
        }
    }

    /**
     * feign 批量编辑应付计划
     *
     * @return
     * @Date 12:38 2021/12/23
     * <AUTHOR>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdatePayablePlan(List<PayablePlanDto> payablePlanDtoList) {
        for (PayablePlanDto payablePlanDto : payablePlanDtoList) {
            PayablePlan payablePlan = payablePlanMapper.selectById(payablePlanDto.getId());
            BeanCopyUtils.copyProperties(payablePlanDto, payablePlan);
            utilService.updateUserInfoToEntity(payablePlan);
            payablePlanMapper.updateById(payablePlan);
        }
        return true;
    }


//    /**
//     * 财务确认代理佣金结算
//     *
//     * @Date 12:39 2021/12/23
//     * <AUTHOR>
//     */
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public Boolean financeConfirmSettlement(List<Long> payablePlanIdList) {
//        List<PayablePlanSettlementAgentAccount> payablePlanSettlementAgentAccounts = payablePlanSettlementAgentAccountMapper.selectList(Wrappers.<PayablePlanSettlementAgentAccount>lambdaQuery()
//                .in(PayablePlanSettlementAgentAccount::getFkPayablePlanId, payablePlanIdList).isNull(PayablePlanSettlementAgentAccount::getNumSettlementBatch));
//        if (GeneralTool.isEmpty(payablePlanSettlementAgentAccounts) || payablePlanIdList.size() != payablePlanSettlementAgentAccounts.size()) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("PAYABLE_PLAN_SETTLEMENT_MARK_NOT_EXISTS"));
//        }
//        //置空回滚时间
//        PayablePlanSettlementInstallment payablePlanSettlementInstallment = new PayablePlanSettlementInstallment();
//        utilService.setUpdateInfo(payablePlanSettlementInstallment);
//        payablePlanSettlementInstallmentMapper.update(payablePlanSettlementInstallment, Wrappers.<PayablePlanSettlementInstallment>lambdaUpdate()
//                .eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_PROCESSING.key)
//                .in(PayablePlanSettlementInstallment::getFkPayablePlanId, payablePlanIdList)
//                .set(PayablePlanSettlementInstallment::getRollBackTime, null));
//        updateStatusSettlement(payablePlanIdList, Collections.singletonList(ProjectExtraEnum.AGENT_CONFIRMATION.key), ProjectExtraEnum.FINANCIAL_RECOGNITION.key, null);
//        return true;
//    }


    /**
     * 更新结算标记
     *
     * @Date 16:23 2021/12/31
     * <AUTHOR>
     */
//    @Transactional(rollbackFor = Exception.class)
//    public void updateStatusSettlement(List<Long> payablePlanIdList, List<Integer> oldStatusSettlements, Integer newStatusSettlement, String num) {
//        List<PayablePlan> payablePlans = payablePlanMapper.selectList(Wrappers.<PayablePlan>lambdaQuery()
//                .in(PayablePlan::getId, payablePlanIdList).in(PayablePlan::getStatusSettlement, oldStatusSettlements));
//        if (payablePlans.size() != payablePlanIdList.size()) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("STATUS_SETTLEMENT_ABNORMAL"));
//        }
//
//        for (PayablePlan payablePlan : payablePlans) {
//            payablePlan.setStatusSettlement(newStatusSettlement);
//            utilService.updateUserInfoToEntity(payablePlan);
//            payablePlanMapper.updateById(payablePlan);
//            PayablePlanSettlementStatus payablePlanSettlementStatus = new PayablePlanSettlementStatus();
//            payablePlanSettlementStatus.setNumSettlementBatch(num);
//            payablePlanSettlementStatus.setFkPayablePlanId(payablePlan.getId());
//            payablePlanSettlementStatus.setStatusSettlement(newStatusSettlement);
//            utilService.updateUserInfoToEntity(payablePlanSettlementStatus);
//            payablePlanSettlementStatusMapper.insert(payablePlanSettlementStatus);
//        }
//        //如果是第二步提交按钮，记录导入时间 设置回滚时间
//        if (ProjectExtraEnum.AGENT_CONFIRMATION.key.equals(newStatusSettlement)) {
//            PayablePlanSettlementInstallment payablePlanSettlementInstallment = new PayablePlanSettlementInstallment();
//            payablePlanSettlementInstallment.setAccountExportTime(new Date());
//            payablePlanSettlementInstallment.setRollBackTime(DateUtil.nextMonth());
//            payablePlanSettlementInstallmentMapper.update(payablePlanSettlementInstallment, Wrappers.<PayablePlanSettlementInstallment>lambdaUpdate()
//                    .in(PayablePlanSettlementInstallment::getFkPayablePlanId, payablePlanIdList)
//                    .eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_PROCESSING.key));
//        }
//    }

    /**
     * feign 财务-应付计划编辑详情回显
     *
     * @Date 12:38 2021/12/23
     * <AUTHOR>
     */
    @Override
    public StudentPlanVo financePlanDetails(Long planId) {
        PayablePlan payablePlan = payablePlanMapper.selectById(planId);
        StudentOfferItem studentOfferItem = studentOfferItemMapper.selectById(payablePlan.getFkTypeTargetId());
        StudentOffer studentOffer = studentOfferMapper.selectById(studentOfferItem.getFkStudentOfferId());
        Student student = studentMapper.selectById(studentOffer.getFkStudentId());
        StudentPlanVo studentPlanVo = new StudentPlanVo();
        BeanCopyUtils.copyProperties(student, studentPlanVo);
        if (GeneralTool.isNotEmpty(studentPlanVo.getFkCompanyId())) {

            Result<String> result = permissionCenterClient.getCompanyNameById(studentPlanVo.getFkCompanyId());
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                studentPlanVo.setFkCompanyName(result.getData());
            }
        }
        if (GeneralTool.isNotEmpty(studentPlanVo.getFkAreaCountryId())) {

            Result<String> result = institutionCenterClient.getCountryNameById(studentPlanVo.getFkAreaCountryId());
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                studentPlanVo.setFkAreaCountryName(result.getData());
            }
        } else {
            studentPlanVo.setFkAreaCountryName(studentPlanVo.getFkAreaCountryName());
        }
        if (GeneralTool.isNotEmpty(studentOfferItem.getFkInstitutionProviderId())) {
            Result<String> result = institutionCenterClient.getInstitutionProviderChannelById(studentOfferItem.getFkInstitutionChannelId());
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                String channelName = result.getData();
                studentPlanVo.setInstitutionProviderName(GeneralTool.isNotEmpty(channelName) ? "【" + channelName + "】" : "" + institutionCenterClient.getInstitutionProviderName(studentOfferItem.getFkInstitutionProviderId()));
            }
        }

        Result<String> institutionNameByIdResult = institutionCenterClient.getInstitutionName(studentOfferItem.getFkInstitutionId());
        if (institutionNameByIdResult.isSuccess() && GeneralTool.isNotEmpty(institutionNameByIdResult.getData())) {
            studentPlanVo.setFkInstitutionName(institutionNameByIdResult.getData());
        }
        if (GeneralTool.isNotEmpty(studentOfferItem.getFkInstitutionCourseId())) {
            Result<String> result3 = institutionCenterClient.getCourseNameById(studentOfferItem.getFkInstitutionCourseId());
            if (result3.isSuccess() && GeneralTool.isNotEmpty(result3.getData())) {
                studentPlanVo.setCourseName(result3.getData());
            }
        } else {
            studentPlanVo.setCourseName("【自定义】" + studentOfferItem.getOldCourseCustomName());
        }
        Agent agent = agentMapper.selectById(studentOffer.getFkAgentId());
        studentPlanVo.setAgentName(agent.getName());

        StringJoiner stringJoiner = new StringJoiner("/");
        stringJoiner.add("【" + studentPlanVo.getNum() + "】").add(student.getName()).add(agent.getName()).add(studentPlanVo.getFkInstitutionName())
                .add(studentPlanVo.getCourseName());
        studentPlanVo.setTargetName(stringJoiner.toString());

        PayablePlanVo payablePlanVo = BeanCopyUtils.objClone(payablePlan, PayablePlanVo::new);
        //应付计划详情
        studentPlanVo.setPayablePlanDto(payablePlanVo);
        studentPlanVo.setFkTypeKeyName(TableEnum.getValue(payablePlan.getFkTypeKey()));
        return studentPlanVo;
    }


    /**
     * Author Cream
     * Description : 获取应付计划
     * Date 2022/5/7 14:10
     * Params:
     * Return
     */
    @Override
    public Result<PayablePlan> doGetPayableInfoById(Long payablePlanId) {
        return Result.data(payablePlanMapper.selectById(payablePlanId));
    }

    /**
     * 应付计划一键结算按钮
     *
     * @Date 14:51 2022/5/12
     * <AUTHOR>
     */
    @Override
    public void oneClickSettlement(OneClickSettlementDto oneClickSettlementDto) {
        if (GeneralTool.isEmpty(oneClickSettlementDto) || GeneralTool.isEmpty(oneClickSettlementDto.getPayablePlanIds())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<PayablePlan> planList = payablePlanMapper.selectList(Wrappers.<PayablePlan>lambdaQuery().in(PayablePlan::getId, oneClickSettlementDto.getPayablePlanIds()));
        Set<String> collect = planList.stream().map(PayablePlan::getFkTypeKey).collect(Collectors.toSet());
        if (collect.contains(TableEnum.INSTITUTION_PROVIDER.key) || collect.contains(TableEnum.SALE_BUSINESS_PROVIDER.key)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("the_payment_cannot_be_settled_for_commission"));
        }
        Result result = financeCenterClient.oneClickSettlement(oneClickSettlementDto);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
    }

    /**
     * 获取留学申请计划的应付计划
     *
     * @param offerItemId
     * @return
     */
    @Override
    public Result<List<SettlementPayablePlanVo>> getPayablePlanByOfferItemId(Long offerItemId) {
        if (GeneralTool.isNull(offerItemId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<PayablePlan> payablePlans = payablePlanMapper.getPayablePlanByOfferItemId(offerItemId);
        if (payablePlans.isEmpty()) {
            return Result.data(Collections.emptyList());
        }
        Set<Long> companyIds = payablePlans.stream().map(PayablePlan::getFkCompanyId).collect(Collectors.toSet());
        Set<String> nums = payablePlans.stream().map(PayablePlan::getFkCurrencyTypeNum).collect(Collectors.toSet());
        Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(companyIds);
        Result<Map<String, String>> byNums = financeCenterClient.getCurrencyNamesByNums(nums);
        Map<Long, String> companyMap = null;
        if (result.isSuccess() && result.getData() != null) {
            companyMap = result.getData();
        }
        Map<String, String> numMap = null;
        if (byNums.isSuccess() && byNums.getData() != null) {
            numMap = byNums.getData();
        }
        List<SettlementPayablePlanVo> planDtoList = new ArrayList<>(payablePlans.size());
        for (PayablePlan payablePlan : payablePlans) {
            SettlementPayablePlanVo planDto = new SettlementPayablePlanVo();
            BeanUtils.copyProperties(payablePlan, planDto);
            if (companyMap != null) {
                planDto.setCompanyName(companyMap.get(payablePlan.getFkCompanyId()));
            }
            if (numMap != null) {
                planDto.setFkCurrencyTypeName(numMap.get(payablePlan.getFkCurrencyTypeNum()));
            }
            planDtoList.add(planDto);
        }
        return Result.data(planDtoList);
    }


    @Override
    public List<String> getPayPlanTheLatestThreeTuitionFees(Long fkCompanyId) {
        return payablePlanMapper.getPayPlanTheLatestThreeTuitionFees(fkCompanyId);
    }


    @Override
    public void sendSettlementCommissionEmail(List<String> emails, Long fkPayablePlanId, String commissionNotice) {
        if (emails.isEmpty()) {
            return;
        }
        PayablePlan payablePlan = payablePlanMapper.selectById(fkPayablePlanId);
        if (GeneralTool.isNotEmpty(payablePlan)) {
            String fkTypeKey = payablePlan.getFkTypeKey();
            Long targetId = payablePlan.getFkTypeTargetId();
            List<Map<String, String>> list = new ArrayList<>();
            if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(fkTypeKey)) {
                StudentOfferItem studentOfferItem = studentOfferItemMapper.selectById(targetId);
                if (GeneralTool.isNotEmpty(studentOfferItem) && !studentOfferItem.getIsFollowHidden()) {
                    Long studentId = studentOfferItem.getFkStudentId();
                    Long fkInstitutionCourseId = studentOfferItem.getFkInstitutionCourseId();
                    Long fkAreaCountryId = studentOfferItem.getFkAreaCountryId();
                    Long fkInstitutionId = studentOfferItem.getFkInstitutionId();
                    Long fkAgentId = studentOfferItem.getFkAgentId();
                    Student student = studentMapper.selectById(studentId);
                    Agent agent = agentService.getAgentById(fkAgentId);
                    String countryName = institutionCenterClient.getCountryNameById(fkAreaCountryId).getData();
                    String institutionName = institutionCenterClient.getInstitutionName(fkInstitutionId).getData();
                    String courseName;
                    if (fkInstitutionCourseId == -1) {
                        courseName = studentOfferItem.getOldCourseCustomName();
                    } else {
                        courseName = institutionCenterClient.getInstitutionCourseNameById(fkInstitutionCourseId).getData();
                    }
                    List<StudentOfferItem> studentOfferItems1 = studentOfferItemMapper.selectList(Wrappers.<StudentOfferItem>lambdaQuery().eq(StudentOfferItem::getFkParentStudentOfferItemId, studentOfferItem.getId()));
                    if (GeneralTool.isNotEmpty(studentOfferItems1)) {
                        for (StudentOfferItem offerItem : studentOfferItems1) {
                            if (!offerItem.getIsFollowHidden()) {
                                Result<InstitutionCourseVo> courseById = institutionCenterClient.getCourseById(offerItem.getFkInstitutionCourseId());
                                if (courseById.getData().getName() != null) {
                                    courseName = courseName + " + " + courseById.getData().getName();
                                }
                            }
                        }
                    }
//                    for (String email : emails) {
//                        Map<String, String> map = new HashMap<>();
//                        map.put("email", email);
//                        map.put("businessType", TableEnum.SALE_STUDENT_OFFER_ITEM.value);
//                        map.put("commissionNotice", commissionNotice);
//                        map.put("agentName", agent.getName());
//                        map.put("studentName", student.getName() + "（" + student.getFirstName() + student.getLastName() + "）");
//                        map.put("countryName", countryName);
//                        map.put("fkInstitutionName", institutionName);
//                        map.put("fkCourseName", courseName);
//                        map.put("title", "佣金结算通知");
//                        map.put("openTime", new SimpleDateFormat("yyyy-MM-dd").format(studentOfferItem.getDeferOpeningTime()));
//                        list.add(map);
//                    }
                    //reminderCenterClient.batchSendEmail(list, TableEnum.STUDENT_OFFER_ITEM_COMMISSION_NOTICE.key);

                        Map<String, String> map = new HashMap<>();
                        map.put("businessType", TableEnum.SALE_STUDENT_OFFER_ITEM.value);
                        map.put("commissionNotice", commissionNotice);
                        map.put("agentName", agent.getName());
                        map.put("studentName", student.getName() + "（" + student.getFirstName() + student.getLastName() + "）");
                        map.put("countryName", countryName);
                        map.put("fkInstitutionName", institutionName);
                        map.put("fkCourseName", courseName);
                        map.put("title", "佣金结算通知");
                        map.put("openTime", new SimpleDateFormat("yyyy-MM-dd").format(studentOfferItem.getDeferOpeningTime()));
                        map.put("staffEmailList",emails.toString());
                    List<EmailSenderQueue> emailSenderQueueList = new ArrayList<>();
                    EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
                    emailSenderQueue.setFkDbName(ProjectKeyEnum.SALE_CENTER.key);
                    emailSenderQueue.setFkTableName(TableEnum.SALE_STUDENT_OFFER_ITEM.key);
                    emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.STUDENT_OFFER_ITEM_COMMISSION_NOTICE.getEmailTemplateKey());
                    emailSenderQueue.setOperationTime(now());
                    emailSenderQueue.setFkTableId(studentOfferItem.getId());
                    emailSenderQueue.setEmailParameter(JSON.toJSONString(map));
                    emailSenderQueueList.add(emailSenderQueue);
                    Result<Boolean> bolleanResult =reminderCenterClient.batchAddEmailQueue(emailSenderQueueList);
                    if (!bolleanResult.isSuccess()) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage(bolleanResult.getMessage()));
                    }
                }
            } else if (TableEnum.SALE_STUDENT_INSURANCE.key.equals(fkTypeKey)) {
                StudentInsurance studentInsurance = studentInsuranceMapper.selectById(targetId);
                if (GeneralTool.isNotEmpty(studentInsurance)) {
                    Long fkStudentId = studentInsurance.getFkStudentId();
                    Long fkAgentId = studentInsurance.getFkAgentId();
                    Long fkAreaCountryId = studentInsurance.getFkAreaCountryId();
                    Student student = studentMapper.selectById(fkStudentId);
                    Agent agent = agentService.getAgentById(fkAgentId);
                    String countryName = institutionCenterClient.getCountryNameById(fkAreaCountryId).getData();
//                    for (String email : emails) {
//                        Map<String, String> map = new HashMap<>();
//                        map.put("email", email);
//                        map.put("businessType", TableEnum.SALE_STUDENT_INSURANCE.value);
//                        map.put("commissionNotice", commissionNotice);
//                        map.put("agentName", agent.getName());
//                        map.put("studentName", student.getName() + "（" + student.getFirstName() + student.getLastName() + "）");
//                        map.put("countryName", countryName);
//                        map.put("title", "佣金结算通知");
//                        map.put("openTime", new SimpleDateFormat("yyyy-MM-dd").format(studentInsurance.getInsuranceStartTime()) + "-" + new SimpleDateFormat("yyyy-MM-dd").format(studentInsurance.getInsuranceEndTime()));
//                        list.add(map);
//                    }

                        Map<String, String> map = new HashMap<>();
                        map.put("businessType", TableEnum.SALE_STUDENT_INSURANCE.value);
                        map.put("commissionNotice", commissionNotice);
                        map.put("agentName", agent.getName());
                        map.put("studentName", student.getName() + "（" + student.getFirstName() + student.getLastName() + "）");
                        map.put("countryName", countryName);
                        map.put("title", "佣金结算通知");
                        map.put("openTime", new SimpleDateFormat("yyyy-MM-dd").format(studentInsurance.getInsuranceStartTime()) + "-" + new SimpleDateFormat("yyyy-MM-dd").format(studentInsurance.getInsuranceEndTime()));
                        map.put("staffEmailList",emails.toString());
                    List<EmailSenderQueue> emailSenderQueueList = new ArrayList<>();
                    EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
                    emailSenderQueue.setFkDbName(ProjectKeyEnum.SALE_CENTER.key);
                    emailSenderQueue.setFkTableName(TableEnum.SALE_STUDENT_INSURANCE.key);
                    emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.INSURANCE_COMMISSION_NOTICE.getEmailTemplateKey());
                    emailSenderQueue.setOperationTime(now());
                    emailSenderQueue.setFkTableId(studentInsurance.getId());
                    emailSenderQueue.setEmailParameter(JSON.toJSONString(map));
                    emailSenderQueueList.add(emailSenderQueue);
                    Result<Boolean> bolleanResult =reminderCenterClient.batchAddEmailQueue(emailSenderQueueList);
                    if (!bolleanResult.isSuccess()) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage(bolleanResult.getMessage()));
                    }
                    //reminderCenterClient.batchSendEmail(list, TableEnum.INSURANCE_COMMISSION_NOTICE.key);

                }
            } else if (TableEnum.SALE_STUDENT_ACCOMMODATION.key.equals(fkTypeKey)) {
                StudentAccommodation studentAccommodation = studentAccommodationMapper.selectById(targetId);
                if (GeneralTool.isNotEmpty(studentAccommodation)) {
                    Long fkStudentId = studentAccommodation.getFkStudentId();
                    Long fkAgentId = studentAccommodation.getFkAgentId();
                    Long fkAreaCountryId = studentAccommodation.getFkAreaCountryId();
                    Student student = studentMapper.selectById(fkStudentId);
                    Agent agent = agentService.getAgentById(fkAgentId);
                    String countryName = institutionCenterClient.getCountryNameById(fkAreaCountryId).getData();
//                    for (String email : emails) {
//                        Map<String, String> map = new HashMap<>();
//                        map.put("email", email);
//                        map.put("businessType", TableEnum.SALE_STUDENT_ACCOMMODATION.value);
//                        map.put("commissionNotice", commissionNotice);
//                        map.put("agentName", agent.getName());
//                        map.put("studentName", student.getName() + "（" + student.getFirstName() + student.getLastName() + "）");
//                        map.put("countryName", countryName);
//                        map.put("title", "佣金结算通知");
//                        map.put("accommodationName", studentAccommodation.getApartmentName());
//                        map.put("accommodationDays", studentAccommodation.getDuration() + "");
//                        list.add(map);
//                    }
                    Map<String, String> map = new HashMap<>();
                    map.put("businessType", TableEnum.SALE_STUDENT_ACCOMMODATION.value);
                    map.put("commissionNotice", commissionNotice);
                    map.put("agentName", agent.getName());
                    map.put("studentName", student.getName() + "（" + student.getFirstName() + student.getLastName() + "）");
                    map.put("countryName", countryName);
                    map.put("title", "佣金结算通知");
                    map.put("accommodationName", studentAccommodation.getApartmentName());
                    map.put("accommodationDays", studentAccommodation.getDuration() + "");
                    map.put("staffEmailList",emails.toString());
                    List<EmailSenderQueue> emailSenderQueueList = new ArrayList<>();
                    EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
                    emailSenderQueue.setFkDbName(ProjectKeyEnum.SALE_CENTER.key);
                    emailSenderQueue.setFkTableName(TableEnum.SALE_STUDENT_ACCOMMODATION.key);
                    emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.ACCOMMODATION_COMMISSION_NOTICE.getEmailTemplateKey());
                    emailSenderQueue.setOperationTime(now());
                    emailSenderQueue.setFkTableId(studentAccommodation.getId());
                    emailSenderQueue.setEmailParameter(JSON.toJSONString(map));
                    emailSenderQueueList.add(emailSenderQueue);
                    Result<Boolean> bolleanResult =reminderCenterClient.batchAddEmailQueue(emailSenderQueueList);
                    if (!bolleanResult.isSuccess()) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage(bolleanResult.getMessage()));
                    }
                    //reminderCenterClient.batchSendEmail(list, TableEnum.ACCOMMODATION_COMMISSION_NOTICE.key);
                }
            } else if (TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(fkTypeKey)) {
                StudentServiceFeeVo serviceFeeDto = studentServiceFeeService.findServiceFeeById(targetId);
                if (GeneralTool.isNotEmpty(serviceFeeDto)) {
//                    for (String email : emails) {
//                        Map<String, String> map = new HashMap<>();
//                        map.put("email", email);
//                        map.put("businessType", TableEnum.SALE_STUDENT_SERVICE_FEE.value);
//                        map.put("commissionNotice", commissionNotice);
//                        map.put("agentName", serviceFeeDto.getAgentName());
//                        map.put("studentName", serviceFeeDto.getFkStudentName());
//                        map.put("countryName", serviceFeeDto.getFkAreaCountryName());
//                        map.put("title", "佣金结算通知");
//                        map.put("serviceFeeType", serviceFeeDto.getServiceTypeName());
//                        list.add(map);
//                    }
                    Map<String, String> map = new HashMap<>();
                    map.put("businessType", TableEnum.SALE_STUDENT_SERVICE_FEE.value);
                    map.put("commissionNotice", commissionNotice);
                    map.put("agentName", serviceFeeDto.getAgentName());
                    map.put("studentName", serviceFeeDto.getFkStudentName());
                    map.put("countryName", serviceFeeDto.getFkAreaCountryName());
                    map.put("title", "佣金结算通知");
                    map.put("serviceFeeType", serviceFeeDto.getServiceTypeName());
                    map.put("staffEmailList",emails.toString());
                    List<EmailSenderQueue> emailSenderQueueList = new ArrayList<>();
                    EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
                    emailSenderQueue.setFkDbName(ProjectKeyEnum.SALE_CENTER.key);
                    emailSenderQueue.setFkTableName(TableEnum.SALE_STUDENT_SERVICE_FEE.key);
                    emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.SERVICE_FEE_COMMISSION_NOTICE.getEmailTemplateKey());
                    emailSenderQueue.setOperationTime(now());
                    emailSenderQueue.setFkTableId(serviceFeeDto.getId());
                    emailSenderQueue.setEmailParameter(JSON.toJSONString(map));
                    emailSenderQueueList.add(emailSenderQueue);
                    Result<Boolean> bolleanResult =reminderCenterClient.batchAddEmailQueue(emailSenderQueueList);
                    if (!bolleanResult.isSuccess()) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage(bolleanResult.getMessage()));
                    }
                    //reminderCenterClient.batchSendEmail(list, TableEnum.SERVICE_FEE_COMMISSION_NOTICE.key);
                }
            }
        }
    }


    /**
     * 批量修改信息
     *
     * @param batchUpdatePayablePlanDto
     */
    @Transactional
    @Override
    public void batchUpdatePayablePlanInfo(BatchUpdatePayablePlanDto batchUpdatePayablePlanDto) {
        if (GeneralTool.isEmpty(batchUpdatePayablePlanDto.getIds())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (GeneralTool.isNotEmpty(batchUpdatePayablePlanDto.getCommissionRate()) && GeneralTool.isNotEmpty(batchUpdatePayablePlanDto.getFixedAmount())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("the_proportion_of_commission_payable_and_the_fixed_amount_payable_cannot_coexist_at_the_same_time"));
        }
        List<PayablePlan> payablePlans = payablePlanMapper.selectBatchIds(batchUpdatePayablePlanDto.getIds());
        if (GeneralTool.isEmpty(payablePlans)) {
            return;
        }

        for (PayablePlan payablePlan : payablePlans) {
            if (GeneralTool.isNotEmpty(batchUpdatePayablePlanDto.getSummary())) {
                if (GeneralTool.isNotEmpty(payablePlan.getSummary())) {
                    payablePlan.setSummary(payablePlan.getSummary() + ";" + batchUpdatePayablePlanDto.getSummary());
                } else {
                    payablePlan.setSummary(batchUpdatePayablePlanDto.getSummary());
                }
            }

            if (GeneralTool.isNotEmpty(payablePlan.getCommissionRate())) {
                if (GeneralTool.isNotEmpty(batchUpdatePayablePlanDto.getSplitRate())) {
                    payablePlan.setSplitRate(batchUpdatePayablePlanDto.getSplitRate());
                }
                if (GeneralTool.isNotEmpty(batchUpdatePayablePlanDto.getCommissionRate()) && GeneralTool.isEmpty(batchUpdatePayablePlanDto.getFixedAmount())) {
                    payablePlan.setCommissionRate(batchUpdatePayablePlanDto.getCommissionRate());
                }
                BigDecimal tuitionAmount = GeneralTool.isNotEmpty(payablePlan.getTuitionAmount()) ? payablePlan.getTuitionAmount() : BigDecimal.ZERO;
                BigDecimal commissionAmount = tuitionAmount.multiply(payablePlan.getCommissionRate().divide(BigDecimal.valueOf(100), 8, RoundingMode.HALF_UP));
                BigDecimal payableAmount = commissionAmount.multiply(payablePlan.getSplitRate().divide(BigDecimal.valueOf(100), 8, RoundingMode.HALF_UP));
                if (GeneralTool.isNotEmpty(payablePlan.getBonusAmount())) {
                    payableAmount = payableAmount.add(payablePlan.getBonusAmount());
                }
                payablePlan.setCommissionAmount(commissionAmount);
                payablePlan.setPayableAmount(payableAmount);
                payablePlan.setFixedAmount(null);
            } else {
                BigDecimal payableAmount = batchUpdatePayablePlanDto.getFixedAmount().multiply(payablePlan.getSplitRate().divide(BigDecimal.valueOf(100), 8, RoundingMode.HALF_UP));
                if (GeneralTool.isNotEmpty(payablePlan.getBonusAmount())) {
                    payableAmount = payableAmount.add(payablePlan.getBonusAmount());
                }
                payablePlan.setFixedAmount(batchUpdatePayablePlanDto.getFixedAmount().multiply(payablePlan.getSplitRate()).setScale(2, RoundingMode.HALF_UP));
                payablePlan.setPayableAmount(payableAmount);
                payablePlan.setCommissionAmount(null);
                payablePlan.setCommissionRate(null);
            }
            utilService.setUpdateInfo(payablePlan);
            payablePlanMapper.updateByIdWithNull(payablePlan);

            //应付计划为学校提供商类型或者业务提供商类型,不用生成给代理的佣金
            if (!payablePlan.getFkTypeKey().equals(TableEnum.INSTITUTION_PROVIDER.key) && !payablePlan.getFkTypeKey().equals(TableEnum.SALE_BUSINESS_PROVIDER.key)) {
                //佣金结算更新
                SettlementInstallmentUpdateDto settlementInstallmentUpdateDto = new SettlementInstallmentUpdateDto();
                settlementInstallmentUpdateDto.setFkCompanyId(payablePlan.getFkCompanyId());
                settlementInstallmentUpdateDto.setFkPayablePlanId(payablePlan.getId());
                ReceivablePlan receivablePlan = receivablePlanMapper.selectById(payablePlan.getFkReceivablePlanId());
                settlementInstallmentUpdateDto.setFkReceivablePlanId(receivablePlan.getId());
                settlementInstallmentUpdateDto.setReceivableAmount(receivablePlan.getReceivableAmount());
                settlementInstallmentUpdateDto.setPayableAmount(payablePlan.getPayableAmount());
                settlementInstallmentUpdateDto.setReceivableCurrencyTypeNum(receivablePlan.getFkCurrencyTypeNum());
                settlementInstallmentUpdateDto.setPayableCurrencyTypeNum(payablePlan.getFkCurrencyTypeNum());
                Result result = financeCenterClient.settlementInstallmentUpdate(settlementInstallmentUpdateDto);
                if (!result.isSuccess()) {
                    throw new GetServiceException(result.getMessage());
                }
            }
//            settlementInstallmentUpdate(payablePlan);
        }
    }

    @Override
    public PayablePlanCheckOutVo checkoutPayablePlan(PayablePlanCheckOutDto payablePlanCheckOutDto) {
        if (GeneralTool.isEmpty(payablePlanCheckOutDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        PayablePlanCheckOutVo payablePlanCheckOutVo = new PayablePlanCheckOutVo();
        ReceivablePlan receivablePlan = receivablePlanMapper.selectById(payablePlanCheckOutDto.getFkReceivablePlanId());
        //比较应收应付金额大小
        if (GeneralTool.isNotEmpty(receivablePlan)) {
            BigDecimal exchangeRete = financeCenterClient.getExchangeRate(payablePlanCheckOutDto.getFkCurrencyTypeNum(), receivablePlan.getFkCurrencyTypeNum()).getData();
            BigDecimal pay = payablePlanCheckOutDto.getPayableAmount().multiply(exchangeRete);
            //校验应付金额是否大于应收金额
            if (pay.compareTo(receivablePlan.getReceivableAmount()) > 0) {
                payablePlanCheckOutVo.setIsPayGreaterThanRec(true);
            }
        }
        return payablePlanCheckOutVo;
    }


    /**
     * 修改应付计划佣金状态
     *
     * @param updatePayablePlanStatusSettlementDto
     * @return
     */
    @Override
    public Boolean updatePayablePlanStatusSettlement(UpdatePayablePlanStatusSettlementDto updatePayablePlanStatusSettlementDto) {
        List<PayablePlan> payablePlans = payablePlanMapper.selectList(Wrappers.<PayablePlan>lambdaQuery()
                .in(PayablePlan::getId, updatePayablePlanStatusSettlementDto.getPayablePlanIdList()).in(PayablePlan::getStatusSettlement, updatePayablePlanStatusSettlementDto.getOldStatusSettlements()));
        if (payablePlans.size() != updatePayablePlanStatusSettlementDto.getPayablePlanIdList().size()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("STATUS_SETTLEMENT_ABNORMAL"));
        }
        PayablePlan payablePlan = new PayablePlan();
        payablePlan.setStatusSettlement(updatePayablePlanStatusSettlementDto.getNewStatusSettlement());
        utilService.updateUserInfoToEntity(payablePlan);
        payablePlanMapper.update(payablePlan, Wrappers.<PayablePlan>lambdaUpdate().eq(PayablePlan::getId, updatePayablePlanStatusSettlementDto.getPayablePlanIdList()));
        return true;
    }

    /**
     * 根据应收计划ids获取对应的应付计划信息
     *
     * @param fkReceivablePlanIdList
     * @return
     */
    @Override
    public List<PayablePlan> getPayablePlanByReceivablePlanIds(List<Long> fkReceivablePlanIdList) {
        List<PayablePlan> payablePlanList = payablePlanMapper.selectList(Wrappers.<PayablePlan>lambdaQuery().in(PayablePlan::getFkReceivablePlanId, fkReceivablePlanIdList)
                .ne(PayablePlan::getStatus, 0));
        return payablePlanList;
    }

    /**
     * 澳小保创建应收应付
     * @param insurancePlanMessageDto
     * @return
     */
    @Override
    public Boolean createInsurancePlan(InsurancePlanMessageDto insurancePlanMessageDto) {
        try {
            ReceivablePlan receivablePlan = new ReceivablePlan();
            receivablePlan.setFkCompanyId(3L);
            receivablePlan.setFkTypeKey(insurancePlanMessageDto.getFkTypeKey());
            receivablePlan.setFkTypeTargetId(insurancePlanMessageDto.getFkTypeTargetId());
            receivablePlan.setFkCurrencyTypeNum(insurancePlanMessageDto.getFkCurrencyTypeNum());
            receivablePlan.setTuitionAmount(insurancePlanMessageDto.getTuitionAmount());
            receivablePlan.setCommissionRate(insurancePlanMessageDto.getReceivablePlanCommissionRate());
            receivablePlan.setNetRate(new BigDecimal(100));
            BigDecimal receivableAmount = insurancePlanMessageDto.getTuitionAmount().multiply(insurancePlanMessageDto.getReceivablePlanCommissionRate()).divide(new BigDecimal(100));
            receivablePlan.setCommissionAmount(receivableAmount);
            receivablePlan.setReceivableAmount(receivableAmount);
            receivablePlan.setSummary("澳小保");
            receivablePlan.setStatus(1);
            utilService.setCreateInfo(receivablePlan);
            receivablePlanMapper.insert(receivablePlan);

            PayablePlan payablePlan = new PayablePlan();
            payablePlan.setFkCompanyId(3L);
            payablePlan.setFkReceivablePlanId(receivablePlan.getId());
            payablePlan.setFkTypeKey(insurancePlanMessageDto.getFkTypeKey());
            payablePlan.setFkTypeTargetId(insurancePlanMessageDto.getFkTypeTargetId());
            payablePlan.setFkCurrencyTypeNum(insurancePlanMessageDto.getFkCurrencyTypeNum());
            payablePlan.setTuitionAmount(insurancePlanMessageDto.getTuitionAmount());
            payablePlan.setCommissionRate(insurancePlanMessageDto.getPayablePlanCommissionRate());
            payablePlan.setSplitRate(new BigDecimal("100"));
            BigDecimal payableAmount = insurancePlanMessageDto.getTuitionAmount().multiply(insurancePlanMessageDto.getPayablePlanCommissionRate()).divide(new BigDecimal(100));
            payablePlan.setCommissionAmount(payableAmount);
            payablePlan.setPayableAmount(payableAmount);
            payablePlan.setSummary("澳小保");
            payablePlan.setStatus(1);
            utilService.setCreateInfo(payablePlan);
            payablePlanMapper.insert(payablePlan);

            List<InsuranceOrderSettlement> insuranceOrderSettlements = insuranceOrderSettlementMapper.selectList(Wrappers.<InsuranceOrderSettlement>lambdaQuery().eq(InsuranceOrderSettlement::getFkInsuranceOrderId, insurancePlanMessageDto.getFkTypeTargetId()));
            for (InsuranceOrderSettlement insuranceOrderSettlement : insuranceOrderSettlements) {
                insuranceOrderSettlement.setFkPayablePlanId(payablePlan.getId());
                utilService.setUpdateInfo(insuranceOrderSettlement);
                insuranceOrderSettlementMapper.updateById(insuranceOrderSettlement);
            }
        } catch (Exception e) {
            String stackTrace = ExceptionUtils.getStackTrace(e);
            LogInsuranceOrderSettlement logInsuranceOrderSettlement =  new LogInsuranceOrderSettlement();
            logInsuranceOrderSettlement.setFkInsuranceOrderId(insurancePlanMessageDto.getFkTypeTargetId());
            logInsuranceOrderSettlement.setOperationKey(insurancePlanMessageDto.getFkTypeKey());
            logInsuranceOrderSettlement.setParamJson(JSONObject.toJSONString(insurancePlanMessageDto));
            logInsuranceOrderSettlement.setStatus(0);
            logInsuranceOrderSettlement.setErrorMessage(e.getMessage() + stackTrace);
            logInsuranceOrderSettlement.setGmtCreate(new Date());
            logInsuranceOrderSettlementMapper.insert(logInsuranceOrderSettlement);
            log.error("澳小保创建应收应付失败" + e.getMessage());
            throw new GetServiceException(e.getMessage());
        }
        return true;
    }


    /**
     * 获取iFile Excel信息
     *
     * @Date 14:14 2022/3/8
     * <AUTHOR>
     */
    @Override
    public List<IFileInfoVo> iFileGroupByAgidAndCurrencyInfo(String numSettlementBatch) {
        List<IFileInfoVo> iFileInfoVos = payablePlanMapper.iFileGroupByAgidAndCurrencyInfo(numSettlementBatch);
        Set<Long> countryIds = iFileInfoVos.stream().map(IFileInfoVo::getAgentAreaCountryId).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Long> cityIds = iFileInfoVos.stream().map(IFileInfoVo::getAgentAreaCityId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> countryNumByCountryIds = institutionCenterClient.getCountryNumByCountryIds(countryIds).getData();
        Map<Long, String> cityFullNamesByIds = new HashMap<>();
        Result<Map<Long, String>> result = institutionCenterClient.getCityFullNamesByIds(cityIds);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            cityFullNamesByIds = result.getData();
        }
        for (IFileInfoVo iFileInfoVo : iFileInfoVos) {
            if (GeneralTool.isNotEmpty(iFileInfoVo.getAgentAreaCountryId())) {
                iFileInfoVo.setAgentAreaCountryNum(countryNumByCountryIds.get(iFileInfoVo.getAgentAreaCountryId()));
            }
            if (GeneralTool.isNotEmpty(iFileInfoVo.getAgentAreaCityId())) {
                iFileInfoVo.setAgentAreaCityName(cityFullNamesByIds.get(iFileInfoVo.getAgentAreaCityId()));
            }
        }
        return iFileInfoVos;
    }

    @Override
    public void deletePayablePlanByItemId(Long id) {
        int j = payablePlanMapper.delete(Wrappers.<PayablePlan>lambdaQuery().eq(PayablePlan::getFkTypeTargetId, id)
                .eq(PayablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_OFFER_ITEM.key)
                .eq(PayablePlan::getStatus, 0));
        if (j < 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

//    @Override
//    public void exportPayablePlanExcel(HttpServletResponse response, PayablePlanDto payablePlanVo) {
//        List<PayablePlanVo> payablePlanDtos = datas(payablePlanVo, null, null);
//        List<PayablePlanExportVo> payablePlanExportDtos = payablePlanDtos.stream().map(payablePlanDto -> {
//            PayablePlanExportVo payablePlanExportDto = new PayablePlanExportVo();
//            BeanCopyUtils.copyProperties(payablePlanDto, payablePlanExportDto);
//            if (payablePlanDto.getStatus() == 0) {
//                payablePlanExportDto.setStatusName("作废");
//            } else if (payablePlanDto.getStatus() == 1) {
//                payablePlanExportDto.setStatusName("有效");
//            } else {
//                payablePlanExportDto.setStatusName("完成");
//            }
//            return payablePlanExportDto;
//        }).collect(Collectors.toList());
//
//        FileUtils.exportExcelNotWrapText(response, payablePlanExportDtos, "PayablePlan", PayablePlanExportVo.class);
//    }

    @Override
    public void exportPayablePlanExcel(HttpServletResponse response, PayablePlanNewQueryDto payablePlanNewVo) {
        List<PayablePlanNewVo> payablePlanNewVos = payablePlanDatas(payablePlanNewVo, null, null);
        List<PayablePlanExportVo> payablePlanExportVos = payablePlanNewVos.stream().map(payablePlanDto -> {
            PayablePlanExportVo payablePlanExportVo = new PayablePlanExportVo();
            BeanCopyUtils.copyProperties(payablePlanDto, payablePlanExportVo);
            if (GeneralTool.isNotEmpty(payablePlanDto.getCommissionMark())) {
                payablePlanExportVo.setCommissionMark("【" + payablePlanDto.getCommissionMark() + "】");
            }
            if (payablePlanDto.getStatus() == 0) {
                payablePlanExportVo.setStatusName("作废");
            } else if (payablePlanDto.getStatus() == 1) {
                payablePlanExportVo.setStatusName("有效");
            } else {
                payablePlanExportVo.setStatusName("完成");
            }
            if (GeneralTool.isNotEmpty(payablePlanDto.getBdCode()) && GeneralTool.isNotEmpty(payablePlanDto.getAgentNum())) {
                payablePlanExportVo.setBdAgentNum(payablePlanDto.getBdCode() + payablePlanDto.getAgentNum());
            }
            if (GeneralTool.isNotEmpty(payablePlanDto.getAgentLabelVos())) {
                payablePlanExportVo.setAgentLabelNames(
                        payablePlanDto.getAgentLabelVos().stream()
                                // 给每个标签名称包裹【】符号
                                .map(vo -> "【" + vo.getLabelName() + "】")
                                // 用逗号拼接所有带符号的标签
                                .collect(Collectors.joining(","))
                );
            }
            StringBuilder studentNameInfo = new StringBuilder();
            if (GeneralTool.isNotEmpty(payablePlanDto.getStudentName())) {
                studentNameInfo.append(payablePlanDto.getStudentName());
            }
            if (GeneralTool.isNotEmpty(payablePlanDto.getStudentLastName())) {
                studentNameInfo.append("(").append(payablePlanDto.getStudentLastName()).append(")");
            }
            payablePlanExportVo.setStudentNameInfo(studentNameInfo.toString());
//            if (GeneralTool.isNotEmpty(payablePlanDto.getStudentName()) && GeneralTool.isNotEmpty(payablePlanDto.getStudentLastName())) {
//                payablePlanExportVo.setStudentNameInfo(payablePlanDto.getStudentName() + "(" + payablePlanDto.getStudentLastName() + ")");
//            }
            if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(payablePlanDto.getFkTypeKey())) {
                if (GeneralTool.isNotEmpty(payablePlanDto.getStudentBirthday())) {
                    payablePlanExportVo.setStudentNameInfo(payablePlanExportVo.getStudentNameInfo() + "/" + payablePlanDto.getStudentBirthday() + "/" + payablePlanDto.getStudentId());
                }
                payablePlanExportVo.setBusinessInformation(payablePlanDto.getIstOrApmName());
                if (GeneralTool.isNotEmpty(payablePlanDto.getShortNameOrApaStartTime())) {
                    payablePlanExportVo.setBusinessInformation(payablePlanExportVo.getBusinessInformation() + "(" + payablePlanDto.getShortNameOrApaStartTime() + ")");
                }
                if (GeneralTool.isNotEmpty(payablePlanDto.getFkInstitutionCourseId())) {
                    if (-1L == payablePlanDto.getFkInstitutionCourseId()) {
                        payablePlanExportVo.setBusinessInformation(payablePlanExportVo.getBusinessInformation() + "/" + payablePlanDto.getOldCourseCustomName());
                    } else {
                        payablePlanExportVo.setBusinessInformation(payablePlanExportVo.getBusinessInformation() + "/" + payablePlanDto.getFkCourseName());
                    }
                }
                if (GeneralTool.isNotEmpty(payablePlanDto.getDeferOpeningTime())) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    payablePlanExportVo.setBusinessInformation(payablePlanExportVo.getBusinessInformation() + "/" + sdf.format(payablePlanDto.getDeferOpeningTime()));
                }
            } else if (TableEnum.SALE_STUDENT_INSURANCE.key.equals(payablePlanDto.getFkTypeKey())) {
                if (GeneralTool.isNotEmpty(payablePlanDto.getStuNamePassportApaEnd())) {
                    payablePlanExportVo.setStudentNameInfo(payablePlanExportVo.getStudentNameInfo() + "/" + payablePlanDto.getStuNamePassportApaEnd());
                }
                payablePlanExportVo.setBusinessInformation(payablePlanDto.getIstOrApmName());
                if (GeneralTool.isNotEmpty(payablePlanDto.getShortNameOrApaStartTime())) {
                    payablePlanExportVo.setBusinessInformation(payablePlanExportVo.getBusinessInformation() + "-" + payablePlanDto.getShortNameOrApaStartTime());
                }
            } else if (TableEnum.SALE_STUDENT_ACCOMMODATION.key.equals(payablePlanDto.getFkTypeKey())) {
                payablePlanExportVo.setBusinessInformation(payablePlanDto.getIstOrApmName());
                if (GeneralTool.isNotEmpty(payablePlanDto.getShortNameOrApaStartTime())) {
                    payablePlanExportVo.setBusinessInformation(payablePlanExportVo.getBusinessInformation() + "(" + payablePlanDto.getShortNameOrApaStartTime() + ")");
                }
                if (GeneralTool.isNotEmpty(payablePlanDto.getFkCourseName())) {
                    payablePlanExportVo.setBusinessInformation(payablePlanExportVo.getBusinessInformation() + "/" + payablePlanDto.getFkCourseName());
                }
                if (GeneralTool.isNotEmpty(payablePlanDto.getStuCnApaDay())) {
                    payablePlanExportVo.setBusinessInformation(payablePlanExportVo.getBusinessInformation() + "/" + payablePlanDto.getStuCnApaDay());
                }

            }

            return payablePlanExportVo;
        }).collect(Collectors.toList());

        // STUDENT_TO_CLIENT_APPROVER_DEFAULT
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.STUDENT_TO_CLIENT_APPROVER_DEFAULT.key, 1).getData();
        // 是否显示佣金结算标记
        boolean commissionMarkFlag = false;
        if (GeneralTool.isNotEmpty(companyConfigMap)) {
            String configValue = companyConfigMap.get(SecureUtil.getFkCompanyId());
            if (GeneralTool.isNotEmpty(configValue)) {
                if (Long.parseLong(configValue) > 0) {
                    commissionMarkFlag = true;
                }
            }
        }

        Set<String> ignoreFields = new HashSet<>();
        if (!commissionMarkFlag) {
            ignoreFields.add("commissionMark");
        }

        if (SecureUtil.getFkCompanyId().equals(3L)) {
            ignoreFields.add("studentNameInfo");
        } else {
            ignoreFields.add("offerItemNum");
            ignoreFields.add("studentName");
            ignoreFields.add("studentNameEng");
            ignoreFields.add("studentBirthday");
            ignoreFields.add("genderName");
            ignoreFields.add("bdAgentNum");
            ignoreFields.add("fkInstitutionName");
            ignoreFields.add("fkCourseName");
            ignoreFields.add("openingTime");
        }

        Map<String, String> fileMap = FileUtils.getFileMapIgnoreSomeField(PayablePlanExportVo.class, ignoreFields);
        FileUtils.exportExcel(response, payablePlanExportVos, "PayablePlan", fileMap);

        //FileUtils.exportExcelNotWrapText(response, payablePlanExportVos, "PayablePlan", PayablePlanExportVo.class);
    }

//    /**
//     * 应付计划预付按钮
//     *
//     * @Date 11:04 2022/3/22
//     * <AUTHOR>
//     */
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void prepaymentButton(PrepaymentButtonDto prepaymentButtonDto) {
//        if (GeneralTool.isEmpty(prepaymentButtonDto) || GeneralTool.isEmpty(prepaymentButtonDto.getPayablePlanIds())) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
//        //如果有收过钱生成分期表数据 或者预付过的不能再次预付
//        List<PayablePlanSettlementInstallment> payablePlanSettlementInstallments = payablePlanSettlementInstallmentMapper.selectList(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
//                .in(PayablePlanSettlementInstallment::getFkPayablePlanId, prepaymentButtonDto.getPayablePlanIds()));
//        if (GeneralTool.isNotEmpty(payablePlanSettlementInstallments) && interfaceConfiguration.equals("GEA")) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("REPEATED_PREPAYMENT"));
//        }
//        //应付计划为学校提供商类型或者业务提供商类型,不用生成给代理的佣金
//        Set<String> set = new HashSet<>();
//        set.add(TableEnum.INSTITUTION_PROVIDER.key);
//        set.add(TableEnum.SALE_BUSINESS_PROVIDER.key);
//        List<PayablePlan> checkPayablePlans = payablePlanMapper.selectList(Wrappers.<PayablePlan>lambdaQuery()
//                .in(PayablePlan::getId, prepaymentButtonDto.getPayablePlanIds())
//                .in(PayablePlan::getFkTypeKey, set));
//        if (GeneralTool.isNotEmpty(checkPayablePlans)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("PAYABLE_PLANS_CANNOT_GENERATE_COMMISSIONS"));
//        }
//
//        LambdaQueryWrapper<PayablePlan> lambdaQueryWrapper = new LambdaQueryWrapper();
//        lambdaQueryWrapper.in(PayablePlan::getId, prepaymentButtonDto.getPayablePlanIds());
//        PayablePlan payablePlan = new PayablePlan();
//        payablePlan.setIsPayInAdvance(true);
//        payablePlan.setPayInAdvancePercent(prepaymentButtonDto.getPercentage().intValue());
//        utilService.setUpdateInfo(payablePlan);
//        payablePlanMapper.update(payablePlan, lambdaQueryWrapper);
//
////        ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.PAY_IN_ADVANCE_SERVICE_FEE.key).getData();
////        String configJson = configDto.getValue1();
////        JSONObject jsonObject = JSON.parseObject(configJson);
//        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.PAY_IN_ADVANCE_SERVICE_FEE.key, 1).getData();
//
//        //插入分期表数据
//        List<PayablePlan> payablePlanList = payablePlanMapper.selectList(Wrappers.<PayablePlan>lambdaQuery().in(PayablePlan::getId, prepaymentButtonDto.getPayablePlanIds()));
//        for (PayablePlan plan : payablePlanList) {
//            //本次预付金额
//            BigDecimal amountActual = plan.getPayableAmount().multiply(prepaymentButtonDto.getPercentage().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)).setScale(2, RoundingMode.HALF_UP);
//            //预付强制扣他五元手续费
//            BigDecimal serviceFee = BigDecimal.ZERO;
//            String configValue1 = companyConfigMap.get(plan.getFkCompanyId());
//            serviceFee = new BigDecimal(configValue1);
////            if (plan.getFkCompanyId() == 3L) {
////                String iae = jsonObject.getString("IAE");
////                if (StringUtils.isNotBlank(iae)) {
////                    serviceFee = new BigDecimal(iae);
////                }
////            } else if (plan.getFkCompanyId() == 2L) {
////                String gea = jsonObject.getString("GEA");
////                if (StringUtils.isNotBlank(gea)) {
////                    serviceFee = new BigDecimal(gea);
////                }
////            } else {
////                String other = jsonObject.getString("OTHER");
////                if (StringUtils.isNotBlank(other)) {
////                    serviceFee = new BigDecimal(other);
////                }
////            }
//            amountActual = amountActual.subtract(serviceFee);
////            //校验多次预付的情况下，预付金额大于付款差额的时候无法进行保存
////            //在结算中是无法生成预付的，所以分期表只有 0未处理或者2已完成的状态，生成预付之后，0未处理的肯定需要删掉,因为还需要对冲掉预付的金额，直到收完钱了才能再生成，但是注意多次预付的情况，需要叠加
////            //所以只需计算 实付支付金额 + 本次预付 是否小于应付， 小于的话才可以生成预付 不然会给多了钱
////            BigDecimal paidInAmount = new BigDecimal(0);
////            List<ReceiptFormItemDto> receiptFormItemList = financeCenterClient.getReceiptFormItemListFeignByPlanIds(Collections.singleton(plan.getFkReceivablePlanId()));
////            if (GeneralTool.isNotEmpty(receiptFormItemList)) {
////                for (ReceiptFormItemDto receiptFormItemDto : receiptFormItemList) {
////                    //需要兑换成应付币种来进行计算
////                    if (receiptFormItemDto.getFkCurrencyTypeNum().equals(plan.getFkCurrencyTypeNum())) {
////                        paidInAmount = paidInAmount.add(receiptFormItemDto.getAmountReceivable());
////                    } else {
////
////                    }
////                }
////
////
////            }
////            //该应付计划已生成的实付总金额
//////            BigDecimal prepaidAmount = payablePlanSettlementInstallmentMapper.getInsertSettlementAmountActual(plan.getId());
////            //如果应收金额小于 已生成实付总金额+本次预付金额
////            if (plan.getPayableAmount().compareTo(amountActual.add(prepaidAmount)) < 0) {
////                throw new GetServiceException(LocaleMessageUtils.getMessage("NON_PREPAYABLE"));
////            }
//

    /// /            List<PayablePlanSettlementInstallment> payablePlanSettlementInstallments = payablePlanSettlementInstallmentMapper.selectList(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
    /// /                    .eq(PayablePlanSettlementInstallment::getFkPayablePlanId, plan.getId())
    /// /                    .isNull(PayablePlanSettlementInstallment::getFkReceiptFormItemId));
    /// /            if (GeneralTool.isNotEmpty(payablePlanSettlementInstallments)) {
    /// /                //已预付金额
    /// /                BigDecimal prepaidAmount = new BigDecimal(0);
    /// /                for (PayablePlanSettlementInstallment planSettlementInstallment : payablePlanSettlementInstallments) {
    /// /                    prepaidAmount = prepaidAmount.add(planSettlementInstallment.getAmountActual());
    /// /                }
    /// /                //如果应收金额小于 已预付金额+本次预付金额
    /// /                if (plan.getPayableAmount().compareTo(amountActual.add(prepaidAmount)) < 0) {
    /// /                    throw new GetServiceException(LocaleMessageUtils.getMessage("NON_PREPAYABLE"));
    /// /                }
    /// /            }
//
//            PayablePlanSettlementInstallment payablePlanSettlementInstallment = new PayablePlanSettlementInstallment();
//            payablePlanSettlementInstallment.setFkPayablePlanId(plan.getId());
//            payablePlanSettlementInstallment.setAmountExpect(amountActual);
//            payablePlanSettlementInstallment.setAmountActual(amountActual);
//            payablePlanSettlementInstallment.setAmountActualInit(amountActual);
//            payablePlanSettlementInstallment.setServiceFeeExpect(serviceFee);
//            payablePlanSettlementInstallment.setServiceFeeActual(serviceFee);
//            payablePlanSettlementInstallment.setServiceFeeActualInit(serviceFee);
//            payablePlanSettlementInstallment.setRollBack(false);
//            payablePlanSettlementInstallment.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
//            payablePlanSettlementInstallment.setStatus(ProjectExtraEnum.INSTALLMENT_UNTREATED.key);
//            utilService.setCreateInfo(payablePlanSettlementInstallment);
//            payablePlanSettlementInstallmentMapper.insert(payablePlanSettlementInstallment);
//
//            PayablePlanSettlementStatus payablePlanSettlementStatus = new PayablePlanSettlementStatus();
//            payablePlanSettlementStatus.setFkPayablePlanId(plan.getId());
//            payablePlanSettlementStatus.setFkPayablePlanSettlementInstallmentId(payablePlanSettlementInstallment.getId());
//            payablePlanSettlementStatus.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
//            utilService.setCreateInfo(payablePlanSettlementStatus);
//            payablePlanSettlementStatusMapper.insert(payablePlanSettlementStatus);
//        }
//
//    }


//    /**
//     * 获取iFile Excel信息
//     *
//     * @Date 14:14 2022/3/5
//     * <AUTHOR>
//     */
//    @Override
//    public List<IFileInfoVo> iFileExcelInfo(String numSettlementBatch) {
//        List<IFileInfoVo> iFileInfoVos = payablePlanMapper.iFileExcelInfo(numSettlementBatch);
//        List<PayablePlanSettlementBatchExchange> payablePlanSettlementBatchExchanges = payablePlanSettlementBatchExchangeMapper.selectList(Wrappers.<PayablePlanSettlementBatchExchange>lambdaQuery()
//                .eq(PayablePlanSettlementBatchExchange::getNumSettlementBatch, numSettlementBatch));
//
//        Set<Long> countryIds = iFileInfoVos.stream().map(IFileInfoVo::getAgentAreaCountryId).filter(Objects::nonNull).collect(Collectors.toSet());
//        Set<Long> cityIds = iFileInfoVos.stream().map(IFileInfoVo::getAgentAreaCityId).filter(Objects::nonNull).collect(Collectors.toSet());
//        Map<Long, String> countryNumByCountryIds = institutionCenterClient.getCountryNumByCountryIds(countryIds).getData();
//        Map<Long, String> cityFullNamesByIds = new HashMap<>();
//        Result<Map<Long, String>> result = institutionCenterClient.getCityFullNamesByIds(cityIds);
//        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
//            cityFullNamesByIds = result.getData();
//        }
//        for (IFileInfoVo iFileInfoVo : iFileInfoVos) {
//            if (GeneralTool.isNotEmpty(iFileInfoVo.getAgentAreaCountryId())) {
//                iFileInfoVo.setAgentAreaCountryNum(countryNumByCountryIds.get(iFileInfoVo.getAgentAreaCountryId()));
//            }
//            if (GeneralTool.isNotEmpty(iFileInfoVo.getAgentAreaCityId())) {
//                String city = cityFullNamesByIds.get(iFileInfoVo.getAgentAreaCityId());
//                //去除中文()
//                if(city.indexOf("（") !=1){
//                    int index = city.indexOf("（");
//                    city = city.substring(0,index);
//                }
//                iFileInfoVo.setAgentAreaCityName(city);
//                //iFileInfoVo.setAgentAreaCityName(cityFullNamesByIds.get(iFileInfoVo.getAgentAreaCityId()));
//            }
//            //获取汇率：NumSettlementBatch、FkAgentId、FkCurrencyTypeNum、FkTypeKey
//            payablePlanSettlementBatchExchanges = payablePlanSettlementBatchExchangeMapper.selectList(Wrappers.<PayablePlanSettlementBatchExchange>lambdaQuery()
//                    .eq(PayablePlanSettlementBatchExchange::getNumSettlementBatch, numSettlementBatch)
//                    .eq(PayablePlanSettlementBatchExchange::getFkAgentId, iFileInfoVo.getAgentId())
//                    .eq(PayablePlanSettlementBatchExchange::getFkCurrencyTypeNum, iFileInfoVo.getFkCurrencyTypeNum())
//                    .eq(PayablePlanSettlementBatchExchange::getFkTypeKey, iFileInfoVo.getFtk()));
//            if (GeneralTool.isNotEmpty(payablePlanSettlementBatchExchanges)) {
//                PayablePlanSettlementBatchExchange payablePlanSettlementBatchExchange = payablePlanSettlementBatchExchanges.get(0);
//                iFileInfoVo.setExchangeCurrencyNum(payablePlanSettlementBatchExchange.getFkCurrencyTypeNumExchange());
//                iFileInfoVo.setExchangeRate(payablePlanSettlementBatchExchange.getExchangeRate());
//                if (GeneralTool.isEmpty(payablePlanSettlementBatchExchange.getExchangeRate())) {
//                    iFileInfoVo.setExchangeAmount(iFileInfoVo.getPayableAmount().multiply(new BigDecimal(1)));
//                } else {
//                    iFileInfoVo.setExchangeAmount(iFileInfoVo.getPayableAmount().multiply(payablePlanSettlementBatchExchange.getExchangeRate()));
//                }
//                //iFileInfoVo.setServiceFee(payablePlanSettlementBatchExchange.getServiceFee());
//            } else {
//                iFileInfoVo.setExchangeCurrencyNum("");
//                iFileInfoVo.setExchangeRate(null);
//                iFileInfoVo.setExchangeAmount(null);
//            }
//        }
//        return iFileInfoVos;
//    }
    private Long getCompanyId(StudentOfferItem studentOfferItem) {
        if (GeneralTool.isNotEmpty(studentOfferItem)) {
            StudentOffer studentOffer = studentOfferMapper.selectById(studentOfferItem.getFkStudentOfferId());
            if (GeneralTool.isNotEmpty(studentOffer)) {
                Student student = studentMapper.selectById(studentOffer.getFkStudentId());
                if (GeneralTool.isNotEmpty(student)) {
                    return student.getFkCompanyId();
                }
            }
        }
        return null;
    }


    private void setName(PayablePlanVo payablePlanVo, Map<String, String> companyMap) {
        payablePlanVo.setFkTypeName(TableEnum.getValue(payablePlanVo.getFkTypeKey()));
        Long studentId = null;
        if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(payablePlanVo.getFkTypeKey())) {
            StudentOfferItemVo offerItem = offerItemService.findOfferItemById(payablePlanVo.getFkTypeTargetId());
            if (GeneralTool.isNotEmpty(offerItem)) {
                studentId = studentOfferMapper.getStudentIdById(offerItem.getFkStudentOfferId());
            }
            if (GeneralTool.isNotEmpty(payablePlanVo.getFkInstitutionChannelId())) {
                Result<String> stringResult = institutionCenterClient.getChannelName(payablePlanVo.getFkInstitutionChannelId());
                if (stringResult.isSuccess() && GeneralTool.isNotEmpty(stringResult.getData())) {
                    payablePlanVo.setFkInstitutionChannelName(stringResult.getData());
                }
            }
            payablePlanVo.setFkAreaCountryName(offerItem.getFkAreaCountryName());
            payablePlanVo.setFkCourseName(offerItem.getFkCourseName());
            payablePlanVo.setFkInstitutionName(offerItem.getFkInstitutionName());
            payablePlanVo.setFkInstitutionProviderName(offerItem.getFkInstitutionProviderName());
            payablePlanVo.setAgentLabelVos(offerItem.getAgentLabelVos());
            Date studentBirthDay = null;
            if (GeneralTool.isNotEmpty(studentId)) {
                studentBirthDay = studentMapper.getStudentBirthDay(studentId);
            }

            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            if (GeneralTool.isEmpty(studentBirthDay)) {
                payablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId));
            } else {
                String format = simpleDateFormat.format(studentBirthDay);
                payablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId) + "/" + format);
            }

//            payablePlanVo.setStudentInformation( studentMapper.getStudentZhEnName(studentId)+"/"+ studentMapper.getStudentBirthDay(studentId));
            String targetName = offerItemService.getStudentNameByItemId(offerItem.getId());
            payablePlanVo.setFkTypeTargetName(targetName);
            payablePlanVo.setOldCourseCustomName(offerItem.getOldCourseCustomName());
            payablePlanVo.setOldInstitutionFullName(offerItem.getOldInstitutionFullName());
            payablePlanVo.setOldInstitutionName(offerItem.getOldInstitutionName());
            payablePlanVo.setFkInstitutionProviderId(offerItem.getFkInstitutionProviderId());
            payablePlanVo.setFkInstitutionId(offerItem.getFkInstitutionId());
            payablePlanVo.setFkInstitutionCourseId(offerItem.getFkInstitutionCourseId());
            payablePlanVo.setFkInstitutionChannelId(offerItem.getFkInstitutionChannelId());
            payablePlanVo.setDuration(offerItem.getDuration());
            payablePlanVo.setDurationType(offerItem.getDurationType());
            payablePlanVo.setAppRemark(offerItem.getAppRemark());
            String agentName = offerService.getAgentNameByOfferId(offerItem.getFkStudentOfferId());
            payablePlanVo.setFkAgentName(agentName);
            payablePlanVo.setFkAgentId(offerItem.getFkAgentId());
            List<StudentOfferItemVo> offerItemDtos = new ArrayList<>();
            Set<Long> agentIds = new HashSet<>();
            agentIds.add(offerItem.getFkAgentId());

            offerItemDtos.add(offerItem);
            Map<Long, String> targetNameMap = new HashMap<>();
            targetNameMap.put(offerItem.getId(), targetName);
            Map<Long, String> agentNameMap = new HashMap<>();
            agentNameMap.put(offerItem.getFkStudentOfferId(), agentName);
            String targetNames = getTargetNames(offerItemDtos, targetNameMap, agentNameMap).get(offerItem.getId());
            payablePlanVo.setTargetNames(targetNames);

            if (GeneralTool.isNotEmpty(payablePlanVo.getFkCurrencyTypeNum())) {
                Result<String> result = financeCenterClient.getCurrencyNameByNum(payablePlanVo.getFkCurrencyTypeNum());
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    String currencyName = result.getData();
                    payablePlanVo.setFkCurrencyTypeName(currencyName);
                }
            }
            Long companyId = payablePlanMapper.getCompanyIdByPlanId(payablePlanVo.getFkTypeTargetId());
            payablePlanVo.setCompanyName(companyMap.get(String.valueOf(companyId)));
        } else if (TableEnum.SALE_STUDENT_INSURANCE.key.equals(payablePlanVo.getFkTypeKey())) {
            StudentInsurance studentInsurance = studentInsuranceMapper.selectById(payablePlanVo.getFkTypeTargetId());
            if (GeneralTool.isNotEmpty(studentInsurance)) {
                //学生信息
                //0学生保险/1陪读人保险
                if (GeneralTool.isNotEmpty(studentInsurance.getType()) && studentInsurance.getType() == 1) {
                    StringBuilder studentInfo = new StringBuilder();
                    if (GeneralTool.isNotEmpty(studentInsurance.getInsurantName())) {
                        studentInfo.append(studentInsurance.getInsurantName());
                    }
                    if (GeneralTool.isNotEmpty(studentInsurance.getInsurantFirstName()) && GeneralTool.isNotEmpty(studentInsurance.getInsurantLastName())) {
                        studentInfo.append("(" + studentInsurance.getInsurantLastName() + " " + studentInsurance.getInsurantFirstName() + ")");
                    }
                    if (GeneralTool.isNotEmpty(studentInsurance.getInsurantPassportNum())) {
                        studentInfo.append(studentInsurance.getInsurantPassportNum());
                    }
                    payablePlanVo.setStudentInformation(studentInfo.toString());
                } else {
                    studentId = studentInsurance.getFkStudentId();
                    String passportNum = studentMapper.getStudentPassportMum(studentId);
                    if (GeneralTool.isEmpty(passportNum)) {
                        payablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId));
                    } else {
                        payablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId) + "/" + passportNum);
                    }
                }

//                    payablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId)+"/"+ studentMapper.getStudentPassportMum(studentId));
                //代理名称
                Long agentId = studentInsurance.getFkAgentId();
                payablePlanVo.setFkAgentName(agentMapper.getAgentNameById(agentId));
                payablePlanVo.setFkAgentId(agentId);
                //国家名称
                if (GeneralTool.isNotEmpty(studentInsurance.getFkAreaCountryId())) {
                    Result<String> result = institutionCenterClient.getCountryNameById(studentInsurance.getFkAreaCountryId());
                    if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                        payablePlanVo.setFkAreaCountryName(result.getData());
                    }
                }
                //业务信息
                payablePlanVo.setBusinessInformation(businessChannelMapper.getFullNameById(studentInsurance.getFkBusinessChannelId()) + "/" + formatter.format(studentInsurance.getInsuranceStartTime())
                        + "/" + formatter.format(studentInsurance.getInsuranceEndTime()));
            }
            Long companyId = payablePlanMapper.getCompanyIdByInsuranceTargetId(payablePlanVo.getFkTypeTargetId());
            payablePlanVo.setCompanyName(companyMap.get(String.valueOf(companyId)));
        } else if (TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(payablePlanVo.getFkTypeKey())) {
            StudentServiceFeeVo serviceFeeDto = studentServiceFeeService.findServiceFeeById(payablePlanVo.getFkTypeTargetId());
            if (GeneralTool.isNotEmpty(serviceFeeDto)) {
                studentId = serviceFeeDto.getFkStudentId();
                payablePlanVo.setStudentInformation(serviceFeeDto.getFkStudentName());
                payablePlanVo.setFkAreaCountryName(serviceFeeDto.getFkAreaCountryName());
                payablePlanVo.setBusinessInformation(serviceFeeDto.getFkStudentName() + "/" + serviceFeeDto.getAgentName() + "/" + serviceFeeDto.getFkAreaCountryName() + "/" + serviceFeeDto.getServiceTypeName());
                payablePlanVo.setFkAgentId(serviceFeeDto.getFkAgentId());
                payablePlanVo.setFkAgentName(serviceFeeDto.getAgentName());
                payablePlanVo.setTargetNames("【" + serviceFeeDto.getNum() + "】"
                        + serviceFeeDto.getFkStudentName() + "/" + serviceFeeDto.getFkAreaCountryName() + "/" + serviceFeeDto.getAgentName() + "/" + serviceFeeDto.getServiceTypeName());
            }
        } else if (TableEnum.INSTITUTION_PROVIDER.key.equals(payablePlanVo.getFkTypeKey())) {
            Result<String> institutionProviderName = institutionCenterClient.getInstitutionProviderName(payablePlanVo.getFkTypeTargetId());
            if (institutionProviderName.isSuccess() && institutionProviderName.getData() != null) {
                String info = institutionProviderName.getData();
                payablePlanVo.setChannelInformation(info);
                payablePlanVo.setBusinessInformation(info);
                payablePlanVo.setTargetNames(info);
            }
            Result<List<CompanyVo>> providerCompanyName = institutionCenterClient.getProviderCompanyName(payablePlanVo.getFkTypeTargetId());
            if (providerCompanyName.isSuccess() && providerCompanyName.getData() != null) {
                String companyName = providerCompanyName.getData().stream().map(CompanyVo::getShortName).collect(Collectors.joining(","));
                payablePlanVo.setTargetCompanyName(companyName);
                payablePlanVo.setTargetCompanyNameId(providerCompanyName.getData().stream().map(CompanyVo::getId).collect(Collectors.toList()));
            }
        } else if (TableEnum.SALE_BUSINESS_PROVIDER.key.equals(payablePlanVo.getFkTypeKey())) {
            String info = businessProviderService.getBusinessProviderNameById(payablePlanVo.getFkTypeTargetId());
            payablePlanVo.setChannelInformation(info);
            payablePlanVo.setBusinessInformation(info);
            payablePlanVo.setTargetNames(info);
            SelItem companyInfo = businessProviderService.getBusinessProviderCompanyInfo(payablePlanVo.getFkTypeTargetId());
            List<Long> list = new ArrayList<>(1);
            list.add(companyInfo.getKeyId());
            payablePlanVo.setTargetCompanyName((String) companyInfo.getVal());
            payablePlanVo.setTargetCompanyNameId(list);
        } else {
            StudentAccommodation studentAccommodation = studentAccommodationMapper.selectById(payablePlanVo.getFkTypeTargetId());
            if (GeneralTool.isNotEmpty(studentAccommodation)) {
                //学生信息
                studentId = studentAccommodation.getFkStudentId();
                Date studentBirthDay = studentMapper.getStudentBirthDay(studentId);
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                if (GeneralTool.isEmpty(studentBirthDay)) {
                    payablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId));
                } else {
                    String format = simpleDateFormat.format(studentBirthDay);
                    payablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId) + "/" + format);
                }
//                    payablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId)+"/"+ studentMapper.getStudentPassportMum(studentId));
                //代理名称
                Long agentId = studentAccommodation.getFkAgentId();
                payablePlanVo.setFkAgentName(agentMapper.getAgentNameById(agentId));
                payablePlanVo.setFkAgentId(agentId);
                //国家名称
                if (GeneralTool.isNotEmpty(studentAccommodation.getFkAreaCountryId())) {
                    Result<String> result = institutionCenterClient.getCountryNameById(studentAccommodation.getFkAreaCountryId());
                    if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                        payablePlanVo.setFkAreaCountryName(result.getData());
                    }
                }
                //业务信息
                payablePlanVo.setBusinessInformation(businessChannelMapper.getFullNameById(studentAccommodation.getFkBusinessChannelId()) + "/" + studentAccommodation.getApartmentName()
                        + "/" + formatter.format(studentAccommodation.getCheckInDate()) + "/" + formatter.format(studentAccommodation.getCheckOutDate())
                        + "(" + studentAccommodation.getDuration() + ")");
            }
            Long companyId = payablePlanMapper.getCompanyIdByAccommodationTargetId(payablePlanVo.getFkTypeTargetId());
            payablePlanVo.setCompanyName(companyMap.get(String.valueOf(companyId)));
        }
        if (studentId != null) {
            Company company = studentMapper.getCompanyNameByStudentId(studentId);
            payablePlanVo.setTargetCompanyName(company.getShortName());
            List<Long> list = new ArrayList<>(1);
            list.add(company.getId());
            payablePlanVo.setTargetCompanyNameId(list);
        }
    }

    private void setListNameOld(PayablePlanVo payablePlanVo, Map<String, String> companyMap,
                                Map<Long, StudentOfferItemVo> offerItemByIds, Map<String, String> currencyNamesByNums) {
        payablePlanVo.setFkTypeName(TableEnum.getValue(payablePlanVo.getFkTypeKey()));
        Long studentId = null;
        if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(payablePlanVo.getFkTypeKey())) {
            StudentOfferItemVo offerItem = offerItemByIds.get(payablePlanVo.getFkTypeTargetId());
            payablePlanVo.setFkAreaCountryName(offerItem.getFkAreaCountryName());
            payablePlanVo.setFkCourseName(offerItem.getFkCourseName());
            payablePlanVo.setFkInstitutionName(offerItem.getFkInstitutionName());
            payablePlanVo.setFkInstitutionProviderName(offerItem.getFkInstitutionProviderName());
            payablePlanVo.setOldInstitutionName(offerItem.getOldInstitutionName());
            payablePlanVo.setOldInstitutionFullName(offerItem.getOldInstitutionFullName());
            payablePlanVo.setOldCourseCustomName(offerItem.getOldCourseCustomName());
            payablePlanVo.setDuration(offerItem.getDuration());
            payablePlanVo.setDurationType(offerItem.getDurationType());
            if (payablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_OFFER_ITEM.key)) {
                //学生信息
                studentId = studentOfferItemMapper.getStudentIdByItemId(offerItem.getId());
                Date studentBirthDay = studentMapper.getStudentBirthDay(studentId);
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                if (GeneralTool.isEmpty(studentBirthDay)) {
                    payablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId));
                } else {
                    String format = simpleDateFormat.format(studentBirthDay);
                    payablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId) + "/" + format);
                }
                if (GeneralTool.isNotEmpty(payablePlanVo.getStudentId())) {
                    payablePlanVo.setStudentInformation(payablePlanVo.getStudentInformation() + "/" + payablePlanVo.getStudentId());
                }
//                payablePlanVo.setStudentInformation( studentMapper.getStudentZhEnName(studentId)+"/"+ studentMapper.getStudentBirthDay(studentId));
                //代理名称
                String agentName = offerService.getAgentNameByOfferId(offerItem.getFkStudentOfferId());
                payablePlanVo.setFkAgentName(agentName);
                //国家名称
                if (GeneralTool.isNotEmpty(offerItem.getFkAreaCountryName())) {
                    payablePlanVo.setFkAreaCountryName(offerItem.getFkAreaCountryName());
                }
                payablePlanVo.setOldInstitutionName(offerItem.getOldInstitutionName());
                payablePlanVo.setOldInstitutionFullName(offerItem.getOldInstitutionFullName());
                payablePlanVo.setOldCourseCustomName(offerItem.getOldCourseCustomName());
                //业务信息
                StringBuffer businessInformation = new StringBuffer();
                if (GeneralTool.isNotEmpty(offerItem.getFkInstitutionName())) {
                    businessInformation.append(offerItem.getFkInstitutionName());
                }
                if (GeneralTool.isNotEmpty(offerItem.getFkCourseName())) {
                    businessInformation.append("/");
                    businessInformation.append(offerItem.getFkCourseName());
                    if (GeneralTool.isNotEmpty(offerItem.getDurationType()) && GeneralTool.isNotEmpty(offerItem.getDuration())) {
                        businessInformation.append(" (").append(offerItem.getDuration()).append(ProjectExtraEnum.getValueByKey(offerItem.getDurationType(), ProjectExtraEnum.DURATION_TYPE)).append(")");
                    }
                }
                if (GeneralTool.isNotEmpty(offerItem.getDeferOpeningTime())) {
                    businessInformation.append("/");
                    businessInformation.append(formatter.format(offerItem.getDeferOpeningTime()));
                }
                payablePlanVo.setBusinessInformation(businessInformation.toString());
            }
            /*String targetNames = getTargetNames(offerItem, targetName, agentName);
            payablePlanVo.setTargetNames(targetNames);*/


        } else if (TableEnum.SALE_STUDENT_INSURANCE.key.equals(payablePlanVo.getFkTypeKey())) {
            StudentInsurance studentInsurance = studentInsuranceMapper.selectById(payablePlanVo.getFkTypeTargetId());
            if (GeneralTool.isNotEmpty(studentInsurance)) {
                //学生信息
                studentId = studentInsurance.getFkStudentId();
                String passportNum = studentMapper.getStudentPassportMum(studentId);
                if (GeneralTool.isEmpty(passportNum)) {
                    payablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId));
                } else {
                    payablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId) + "/" + passportNum);
                }
//                payablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId)+"/"+ studentMapper.getStudentPassportMum(studentId));
                //代理名称
                Long agentId = studentInsurance.getFkAgentId();
                payablePlanVo.setFkAgentName(agentMapper.getAgentNameById(agentId));
                //国家名称
                if (GeneralTool.isNotEmpty(studentInsurance.getFkAreaCountryId())) {
                    Result<String> result = institutionCenterClient.getCountryNameById(studentInsurance.getFkAreaCountryId());
                    if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                        payablePlanVo.setFkAreaCountryName(result.getData());
                    }
                }
                //业务信息
                payablePlanVo.setBusinessInformation(businessChannelMapper.getFullNameById(studentInsurance.getFkBusinessChannelId()) + "/" + formatter.format(studentInsurance.getInsuranceStartTime())
                        + "/" + formatter.format(studentInsurance.getInsuranceEndTime()));
            }
        } else {
            StudentAccommodation studentAccommodation = studentAccommodationMapper.selectById(payablePlanVo.getFkTypeTargetId());
            if (GeneralTool.isNotEmpty(studentAccommodation)) {
                //学生信息
                studentId = studentAccommodation.getFkStudentId();
                Date studentBirthDay = studentMapper.getStudentBirthDay(studentId);
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                if (GeneralTool.isEmpty(studentBirthDay)) {
                    payablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId));
                } else {
                    String format = simpleDateFormat.format(studentBirthDay);
                    payablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId) + "/" + format);
                }
//                payablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId)+"/"+ studentMapper.getStudentPassportMum(studentId));
                //代理名称
                Long agentId = studentAccommodation.getFkAgentId();
                payablePlanVo.setFkAgentName(agentMapper.getAgentNameById(agentId));
                //国家名称
                if (GeneralTool.isNotEmpty(studentAccommodation.getFkAreaCountryId())) {
                    Result<String> result = institutionCenterClient.getCountryNameById(studentAccommodation.getFkAreaCountryId());
                    if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                        payablePlanVo.setFkAreaCountryName(result.getData());
                    }
                }
                //业务信息
                payablePlanVo.setBusinessInformation(businessChannelMapper.getFullNameById(studentAccommodation.getFkBusinessChannelId()) + "/" + studentAccommodation.getApartmentName()
                        + "/" + formatter.format(studentAccommodation.getCheckInDate()) + "/" + formatter.format(studentAccommodation.getCheckOutDate())
                        + "(" + studentAccommodation.getDuration() + ")");
            }
        }
        if (GeneralTool.isNotEmpty(payablePlanVo.getFkCurrencyTypeNum())) {
            String currencyName = currencyNamesByNums.get(payablePlanVo.getFkCurrencyTypeNum());
            payablePlanVo.setFkCurrencyTypeName(currencyName);
        }
        Long companyId = 0L;
        if (GeneralTool.isNotEmpty(payablePlanVo.getFkTypeKey()) && "m_student_offer_item".equals(payablePlanVo.getFkTypeKey())) {
            companyId = payablePlanMapper.getCompanyIdByPlanId(payablePlanVo.getFkTypeTargetId());
        }
        if (GeneralTool.isNotEmpty(payablePlanVo.getFkTypeKey()) && "m_student_insurance".equals(payablePlanVo.getFkTypeKey())) {
            companyId = payablePlanMapper.getCompanyIdByInsuranceTargetId(payablePlanVo.getFkTypeTargetId());
        }
        if (GeneralTool.isNotEmpty(payablePlanVo.getFkTypeKey()) && "m_student_accommodation".equals(payablePlanVo.getFkTypeKey())) {
            companyId = payablePlanMapper.getCompanyIdByAccommodationTargetId(payablePlanVo.getFkTypeTargetId());
        }
        payablePlanVo.setCompanyName(companyMap.get(String.valueOf(companyId)));
    }

    private void setListName(List<PayablePlanVo> payablePlanVos) {

        if (GeneralTool.isEmpty(payablePlanVos)) {
            return;
        }
        //币种编号nums
        Set<String> fkCurrencyTypeNums = payablePlanVos.stream().map(PayablePlanVo::getFkCurrencyTypeNum).collect(Collectors.toSet());
        //根据币种编号nums获取名称map
        Map<String, String> currencyNamesByNums = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkCurrencyTypeNums)) {
            Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(fkCurrencyTypeNums);
            if (result.isSuccess() && result.getData() != null) {
                currencyNamesByNums = result.getData();
            }
        }
        Map<String, String> companyMap = getCompanyMap();
        //应付类型对应记录ids
        Set<Long> fkTypeTargetIds = payablePlanVos.stream().filter(f -> TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(f.getFkTypeKey()))
                .map(PayablePlanVo::getFkTypeTargetId).collect(Collectors.toSet());
        //根据应付类型对应记录ids获取对象map
        Map<Long, StudentOfferItemVo> offerItemByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkTypeTargetIds)) {
            offerItemByIds = offerItemService.findOfferItemByIdsNew(fkTypeTargetIds);
        }
        //获取学生id
        Set<Long> studentIds = Sets.newHashSet();
        Set<Long> businessChannelIds = Sets.newHashSet();
        Set<Long> agentIdSet;
        Map<Long, String> agentNameMap = Maps.newHashMap();
        Map<Long, String> institutionMap = Maps.newHashMap();
        Map<Long, String> courseMap = Maps.newHashMap();
        Map<Long, Long> studentItemCompanyMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(offerItemByIds)) {
            Set<Long> studentIdSet = offerItemByIds.values().stream().map(StudentOfferItemVo::getFkStudentId).filter(Objects::nonNull).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(studentIdSet)) {
                studentIds.addAll(studentIdSet);
            }
            Set<Long> institutionIds = offerItemByIds.values().stream().map(StudentOfferItemVo::getFkInstitutionId).filter(Objects::nonNull).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(institutionIds)) {
                institutionMap = institutionCenterClient.getInstitutionNamesByIds(institutionIds).getData();
            }
            Set<Long> courseIds = offerItemByIds.values().stream().map(StudentOfferItemVo::getFkInstitutionCourseId).filter(Objects::nonNull).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(institutionIds)) {
                courseMap = institutionCenterClient.getInstitutionCourseNamesByIds(courseIds).getData();
            }
            agentIdSet = offerItemByIds.values().stream().map(StudentOfferItemVo::getFkAgentId).filter(Objects::nonNull).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(agentIdSet)) {
                agentNameMap = agentService.getAgentNamesByIds(agentIdSet);
            }

            Set<Long> offerItemIds = offerItemByIds.values().stream().map(StudentOfferItemVo::getId).filter(Objects::nonNull).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(offerItemIds)) {
                List<StudentOfferItemVo> companyIdByPlanIds = payablePlanMapper.getCompanyIdByPlanIds(offerItemIds);
                if (GeneralTool.isNotEmpty(companyIdByPlanIds)) {
                    studentItemCompanyMap = companyIdByPlanIds.stream().collect(HashMap::new, (m, v) -> m.put(v.getId(), v.getFkCompanyId()), HashMap::putAll);
                }
            }


        }

        //获取保险 和保险的学生
        Map<Long, StudentInsurance> studentInsuranceMap = Maps.newHashMap();
        Map<Long, Long> studentInsuranceCompanyMap = Maps.newHashMap();
        Set<Long> insuranceIds = payablePlanVos.stream().filter(p -> TableEnum.SALE_STUDENT_INSURANCE.key.equals(p.getFkTypeKey())).map(PayablePlanVo::getFkTypeTargetId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(insuranceIds)) {
            List<StudentInsurance> studentInsurances = studentInsuranceService.listByIds(insuranceIds);
            if (GeneralTool.isNotEmpty(studentInsurances)) {
                studentInsuranceMap = studentInsurances.stream().collect(HashMap::new, (m, v) -> m.put(v.getId(), v), HashMap::putAll);

                Set<Long> studentIdSet = studentInsurances.stream().map(StudentInsurance::getFkStudentId).filter(Objects::nonNull).collect(Collectors.toSet());
                if (GeneralTool.isNotEmpty(studentIdSet)) {
                    studentIds.addAll(studentIdSet);
                }

                Set<Long> businessChannelIdSet = studentInsurances.stream().map(StudentInsurance::getFkBusinessChannelId).filter(Objects::nonNull).collect(Collectors.toSet());
                if (GeneralTool.isNotEmpty(businessChannelIdSet)) {
                    businessChannelIds.addAll(businessChannelIdSet);
                }

                List<StudentInsuranceVo> companyIdByInsuranceTargetIds = payablePlanMapper.getCompanyIdByInsuranceTargetIds(insuranceIds);
                if (GeneralTool.isNotEmpty(companyIdByInsuranceTargetIds)) {
                    studentInsuranceCompanyMap = companyIdByInsuranceTargetIds.stream().collect(HashMap::new, (m, v) -> m.put(v.getId(), v.getFkCompanyId()), HashMap::putAll);
                }
            }

        }

        //获取住宿 和住宿的学生
        Map<Long, StudentAccommodation> studentAccommodationMap = Maps.newHashMap();
        Map<Long, Long> studentAccommodationCompanyMap = Maps.newHashMap();
        Set<Long> accommodationIds = payablePlanVos.stream().filter(p -> TableEnum.SALE_STUDENT_ACCOMMODATION.key.equals(p.getFkTypeKey())).map(PayablePlanVo::getFkTypeTargetId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(accommodationIds)) {
            List<StudentAccommodation> studentAccommodations = studentAccommodationService.listByIds(insuranceIds);
            if (GeneralTool.isNotEmpty(studentAccommodations)) {
                studentAccommodationMap = studentAccommodations.stream().collect(HashMap::new, (m, v) -> m.put(v.getId(), v), HashMap::putAll);

                Set<Long> studentIdSet = studentAccommodations.stream().map(StudentAccommodation::getFkStudentId).filter(Objects::nonNull).collect(Collectors.toSet());
                if (GeneralTool.isNotEmpty(studentIdSet)) {
                    studentIds.addAll(studentIdSet);
                }

                Set<Long> businessChannelIdSet = studentAccommodations.stream().map(StudentAccommodation::getFkBusinessChannelId).filter(Objects::nonNull).collect(Collectors.toSet());
                if (GeneralTool.isNotEmpty(businessChannelIdSet)) {
                    businessChannelIds.addAll(businessChannelIdSet);
                }

                List<StudentAccommodationVo> companyIdByAccommodationIds = payablePlanMapper.getCompanyIdByAccommodationTargetIds(accommodationIds);
                if (GeneralTool.isNotEmpty(companyIdByAccommodationIds)) {
                    studentAccommodationCompanyMap = companyIdByAccommodationIds.stream().collect(HashMap::new, (m, v) -> m.put(v.getId(), v.getFkCompanyId()), HashMap::putAll);
                }
            }
        }

        //获取澳小保的学生
        Map<Long, InsuranceOrderDto> insuranceOrderMap = Maps.newHashMap();
        Map<Long, Long> insuranceOrderCompanyMap = Maps.newHashMap();
        List<Long> insuranceOrderIds = payablePlanVos.stream().filter(p -> TableEnum.INSURANCE_ORDER.key.equals(p.getFkTypeKey())).map(PayablePlanVo::getFkTypeTargetId).filter(Objects::nonNull).collect(Collectors.toList());
        if (GeneralTool.isNotEmpty(insuranceOrderIds)) {
            List<InsuranceOrderDto> insuranceOrder = insuranceOrderMapper.getInsuranceOrderInformationList(insuranceOrderIds);
            insuranceOrderMap = insuranceOrder.stream().collect(HashMap::new, (m, v) -> m.put(v.getId(), v), HashMap::putAll);
            List<InsuranceOrder> incomeOrderList =  insuranceOrderMapper.selectBatchIds(insuranceIds);
            if (GeneralTool.isNotEmpty(incomeOrderList)) {
                insuranceOrderCompanyMap = incomeOrderList.stream().collect(HashMap::new, (m, v) -> m.put(v.getId(), v.getFkCompanyId()), HashMap::putAll);
            }
        }

        Map<Long, Date> birthdayMap = Maps.newHashMap();
        Map<Long, String> fullNameMap = Maps.newHashMap();
        Map<Long, String> passportNumMap = Maps.newHashMap();
        Map<Long, String> businessChannelNameMap = Maps.newHashMap();
        Set<Long> countryIds = institutionCenterClient.getAllCountryId().getData();
        Map<Long, String> countryNameMap = institutionCenterClient.getCountryNamesByIds(countryIds).getData();
        if (GeneralTool.isNotEmpty(studentIds)) {
            //获取学生生日
            List<Student> studentBirthDayByIds = studentMapper.getStudentBirthDayByIds(studentIds);
            if (GeneralTool.isNotEmpty(studentBirthDayByIds)) {
                birthdayMap = studentBirthDayByIds.stream().filter(s -> GeneralTool.isNotEmpty(s.getBirthday())).collect(HashMap::new, (m, v) -> m.put(v.getId(), v.getBirthday()), HashMap::putAll);
            }
            //获取学生中英文名
            List<StudentVo> studentZhEnNameByStudentIds = studentMapper.getStudentZhEnNameByIds(studentIds);
            if (GeneralTool.isNotEmpty(studentZhEnNameByStudentIds)) {
                fullNameMap = studentZhEnNameByStudentIds.stream().filter(s -> GeneralTool.isNotEmpty(s.getFullName())).collect(HashMap::new, (m, v) -> m.put(v.getId(), v.getFullName()), HashMap::putAll);
            }

            List<StudentVo> studentPassportMumByIds = studentMapper.getStudentPassportMumByIds(studentIds);
            if (GeneralTool.isNotEmpty(studentPassportMumByIds)) {
                passportNumMap = studentPassportMumByIds.stream().filter(s -> GeneralTool.isNotEmpty(s.getPassportNum())).collect(HashMap::new, (m, v) -> m.put(v.getId(), v.getPassportNum()), HashMap::putAll);
            }

        }

        if (GeneralTool.isNotEmpty(businessChannelIds)) {
            List<BusinessChannelVo> businessChannelNameByIds = businessChannelMapper.getFullNameByIds(businessChannelIds);
            if (GeneralTool.isNotEmpty(businessChannelNameByIds)) {
                businessChannelNameMap = businessChannelNameByIds.stream().filter(s -> GeneralTool.isNotEmpty(s.getFullName())).collect(HashMap::new, (m, v) -> m.put(v.getId(), v.getFullName()), HashMap::putAll);
            }
        }

        List<Long> serviceIds = payablePlanVos.stream().filter(f -> TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(f.getFkTypeKey())).map(PayablePlanVo::getFkTypeTargetId).collect(Collectors.toList());
        Map<Long, StudentServiceFeeVo> serviceFeeDtoMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(serviceIds)) {
            List<StudentServiceFeeVo> fee = studentServiceFeeService.getServiceFeeByIds(serviceIds);
            serviceFeeDtoMap = fee.stream().collect(Collectors.toMap(StudentServiceFeeVo::getId, Function.identity()));
        }
        Map<Long, String> institutionProviderSelectNamesByIds = new HashMap<>();
        Set<Long> providerIds = payablePlanVos.stream().filter(f -> TableEnum.INSTITUTION_PROVIDER.key.equals(f.getFkTypeKey())).map(PayablePlanVo::getFkTypeTargetId).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(providerIds)) {
            institutionProviderSelectNamesByIds = institutionCenterClient.getInstitutionProviderNamesByIds(providerIds).getData();
        }

        Map<Long, String> businessProviderMap = new HashMap<>();
        Set<Long> businessProviderIds = payablePlanVos.stream().filter(f -> TableEnum.SALE_BUSINESS_PROVIDER.key.equals(f.getFkTypeKey())).map(PayablePlanVo::getFkTypeTargetId).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(businessProviderIds)) {
            businessProviderMap = businessProviderService.getNamesByIds(businessProviderIds);
        }

        for (PayablePlanVo payablePlanVo : payablePlanVos) {
            payablePlanVo.setFkTypeName(TableEnum.getValue(payablePlanVo.getFkTypeKey()));
            Long studentId = null;
            payablePlanVo.setFkCurrencyTypeName(currencyNamesByNums.get(payablePlanVo.getFkCurrencyTypeNum()));
            if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(payablePlanVo.getFkTypeKey())) {
                StudentOfferItemVo offerItem = offerItemByIds.get(payablePlanVo.getFkTypeTargetId());
                payablePlanVo.setFkAreaCountryName(offerItem.getFkAreaCountryName());
                payablePlanVo.setFkCourseName(offerItem.getFkCourseName());
                payablePlanVo.setFkInstitutionName(offerItem.getFkInstitutionName());
                payablePlanVo.setFkInstitutionProviderName(offerItem.getFkInstitutionProviderName());
                payablePlanVo.setOldInstitutionName(offerItem.getOldInstitutionName());
                payablePlanVo.setOldInstitutionFullName(offerItem.getOldInstitutionFullName());
                payablePlanVo.setOldCourseCustomName(offerItem.getOldCourseCustomName());
                payablePlanVo.setDuration(offerItem.getDuration());
                payablePlanVo.setDurationType(offerItem.getDurationType());
                if (payablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_OFFER_ITEM.key)) {
                    //学生信息
//                    studentId = studentOfferItemMapper.getStudentIdByItemId(offerItem.getId());
                    studentId = offerItem.getFkStudentId();
//                    Date studentBirthDay = studentMapper.getStudentBirthDay(studentId);
                    Date studentBirthDay = birthdayMap.get(studentId);
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    if (GeneralTool.isEmpty(studentBirthDay)) {
//                        payablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId));
                        payablePlanVo.setStudentInformation(fullNameMap.get(studentId));
                    } else {
                        String format = simpleDateFormat.format(studentBirthDay);
//                        payablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId) + "/" + format);
                        payablePlanVo.setStudentInformation(fullNameMap.get(studentId) + "/" + format);
                    }
                    if (GeneralTool.isNotEmpty(payablePlanVo.getStudentId())) {
                        payablePlanVo.setStudentInformation(payablePlanVo.getStudentInformation() + "/" + payablePlanVo.getStudentId());
                    }
                    //代理名称
//                    String agentName = offerService.getAgentNameByOfferId(offerItem.getFkStudentOfferId());
                    String agentName = agentNameMap.get(offerItem.getFkAgentId());
                    payablePlanVo.setFkAgentName(agentName);
                    //国家名称
                    if (GeneralTool.isNotEmpty(offerItem.getFkAreaCountryId())) {
                        payablePlanVo.setFkAreaCountryName(countryNameMap.get(offerItem.getFkAreaCountryId()));
                    }
                    payablePlanVo.setOldInstitutionName(offerItem.getOldInstitutionName());
                    payablePlanVo.setOldInstitutionFullName(offerItem.getOldInstitutionFullName());
                    payablePlanVo.setOldCourseCustomName(offerItem.getOldCourseCustomName());
                    //业务信息
                    StringBuilder businessInformation = new StringBuilder();
                    if (GeneralTool.isNotEmpty(offerItem.getFkInstitutionId())) {
                        businessInformation.append(institutionMap.get(offerItem.getFkInstitutionId()));
                    }
                    if (GeneralTool.isNotEmpty(offerItem.getFkInstitutionCourseId())) {
                        businessInformation.append("/");
                        businessInformation.append(courseMap.get(offerItem.getFkInstitutionCourseId()));
                        if (GeneralTool.isNotEmpty(offerItem.getDurationType()) && GeneralTool.isNotEmpty(offerItem.getDuration())) {
                            businessInformation.append(" (").append(offerItem.getDuration()).append(ProjectExtraEnum.getValueByKey(offerItem.getDurationType(), ProjectExtraEnum.DURATION_TYPE)).append(")");
                        }
                    }
                    if (GeneralTool.isNotEmpty(offerItem.getDeferOpeningTime())) {
                        businessInformation.append("/");
                        businessInformation.append(formatter.format(offerItem.getDeferOpeningTime()));
                    }
                    payablePlanVo.setBusinessInformation(businessInformation.toString());
                }


            } else if (TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(payablePlanVo.getFkTypeKey())) {
                StudentServiceFeeVo serviceFeeDto = serviceFeeDtoMap.get(payablePlanVo.getFkTypeTargetId());
                if (GeneralTool.isNotEmpty(serviceFeeDto)) {
                    payablePlanVo.setStudentInformation(serviceFeeDto.getFkStudentName());
                    payablePlanVo.setFkAreaCountryName(serviceFeeDto.getFkAreaCountryName());
                    payablePlanVo.setBusinessInformation(serviceFeeDto.getFkStudentName() + "/" + serviceFeeDto.getAgentName() + "/" + serviceFeeDto.getFkAreaCountryName() + "/" + serviceFeeDto.getServiceTypeName());
                    payablePlanVo.setFkAgentId(serviceFeeDto.getFkAgentId());
                    payablePlanVo.setFkAgentName(serviceFeeDto.getAgentName());
                    payablePlanVo.setTargetNames("【" + serviceFeeDto.getNum() + "】"
                            + serviceFeeDto.getFkStudentName() + "/" + serviceFeeDto.getFkAreaCountryName() + "/" + serviceFeeDto.getAgentName() + "/" + serviceFeeDto.getServiceTypeName());

                }
            } else if (TableEnum.INSTITUTION_PROVIDER.key.equals(payablePlanVo.getFkTypeKey())) {
                if (GeneralTool.isNotEmpty(institutionProviderSelectNamesByIds) && GeneralTool.isNotEmpty(institutionProviderSelectNamesByIds.get(payablePlanVo.getFkTypeTargetId()))) {
                    String info = institutionProviderSelectNamesByIds.get(payablePlanVo.getFkTypeTargetId());
                    payablePlanVo.setChannelInformation(info);
                    payablePlanVo.setBusinessInformation(info);
//                    //公司
//                    payablePlanVo.setCompanyName(companyMap.get(payablePlanVo.getFkCompanyId().toString()));
                }
            } else if (TableEnum.SALE_BUSINESS_PROVIDER.key.equals(payablePlanVo.getFkTypeKey())) {
                if (GeneralTool.isNotEmpty(businessProviderMap) && GeneralTool.isNotEmpty(businessProviderMap.get(payablePlanVo.getFkTypeTargetId()))) {
                    String info = businessProviderMap.get(payablePlanVo.getFkTypeTargetId());
                    payablePlanVo.setChannelInformation(info);
                    payablePlanVo.setBusinessInformation(info);
//                    //公司
//                    payablePlanVo.setCompanyName(companyMap.get(payablePlanVo.getFkCompanyId().toString()));
                }
            } else if (TableEnum.SALE_STUDENT_INSURANCE.key.equals(payablePlanVo.getFkTypeKey())) {
//                StudentInsurance studentInsurance = studentInsuranceMapper.selectById(payablePlanVo.getFkTypeTargetId());
                StudentInsurance studentInsurance = studentInsuranceMap.get(payablePlanVo.getFkTypeTargetId());
                if (GeneralTool.isNotEmpty(studentInsurance)) {
                    //学生信息
                    studentId = studentInsurance.getFkStudentId();
//                    String passportNum = studentMapper.getStudentPassportMum(studentId);
                    String passportNum = passportNumMap.get(studentId);
                    if (GeneralTool.isEmpty(passportNum)) {
//                        payablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId));
                        payablePlanVo.setStudentInformation(fullNameMap.get(studentId));
                    } else {
//                        payablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId) + "/" + passportNum);
                        payablePlanVo.setStudentInformation(fullNameMap.get(studentId) + "/" + passportNum);
                    }
                    //代理名称
                    Long agentId = studentInsurance.getFkAgentId();
//                    payablePlanVo.setFkAgentName(agentMapper.getAgentNameById(agentId));
                    payablePlanVo.setFkAgentName(agentNameMap.get(agentId));
                    //国家名称
                    if (GeneralTool.isNotEmpty(studentInsurance.getFkAreaCountryId())) {
//                        Result<String> result = institutionCenterClient.getCountryNameById(studentInsurance.getFkAreaCountryId());
//                        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
//                            payablePlanVo.setFkAreaCountryName(result.getData());
//                        }
                        payablePlanVo.setFkAreaCountryName(countryNameMap.get(studentInsurance.getFkAreaCountryId()));

                    }
                    //业务信息
//                    payablePlanVo.setBusinessInformation(businessChannelMapper.getFullNameById(studentInsurance.getFkBusinessChannelId()) + "/" + formatter.format(studentInsurance.getInsuranceStartTime())
//                            + "/" + formatter.format(studentInsurance.getInsuranceEndTime()));
                    payablePlanVo.setBusinessInformation(businessChannelNameMap.get(studentInsurance.getFkBusinessChannelId()) + "/" + formatter.format(studentInsurance.getInsuranceStartTime())
                            + "/" + formatter.format(studentInsurance.getInsuranceEndTime()));
                }
            } else if (TableEnum.INSURANCE_ORDER.key.equals(payablePlanVo.getFkTypeKey())) {
                InsuranceOrderDto insuranceOrder = insuranceOrderMap.get(payablePlanVo.getFkTypeTargetId());
                if (GeneralTool.isNotEmpty(insuranceOrder)) {
                    //学生信息
                    payablePlanVo.setStudentInformation(insuranceOrder.getStudentName());
                    //代理名称
                    Long agentId = insuranceOrder.getFkAgentId();
                    payablePlanVo.setFkAgentName(agentNameMap.get(agentId));
                    //国家名称
                    payablePlanVo.setFkAreaCountryName(insuranceOrder.getFkAreaCountryName());
                    //业务信息
                    payablePlanVo.setBusinessInformation(insuranceOrder.getBusinessInformation());
                }
            } else {
//                StudentAccommodation studentAccommodation = studentAccommodationMapper.selectById(payablePlanVo.getFkTypeTargetId());
                StudentAccommodation studentAccommodation = studentAccommodationMap.get(payablePlanVo.getFkTypeTargetId());
                if (GeneralTool.isNotEmpty(studentAccommodation)) {
                    //学生信息
                    studentId = studentAccommodation.getFkStudentId();
//                    Date studentBirthDay = studentMapper.getStudentBirthDay(studentId);
                    Date studentBirthDay = birthdayMap.get(studentId);
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    if (GeneralTool.isEmpty(studentBirthDay)) {
//                        payablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId));
                        payablePlanVo.setStudentInformation(fullNameMap.get(studentId));
                    } else {
                        String format = simpleDateFormat.format(studentBirthDay);
                        payablePlanVo.setStudentInformation(fullNameMap.get(studentId) + "/" + format);
                    }
                    //代理名称
                    Long agentId = studentAccommodation.getFkAgentId();
//                    payablePlanVo.setFkAgentName(agentMapper.getAgentNameById(agentId));
                    payablePlanVo.setFkAgentName(agentNameMap.get(agentId));
                    //国家名称
                    if (GeneralTool.isNotEmpty(studentAccommodation.getFkAreaCountryId())) {
//                        Result<String> result = institutionCenterClient.getCountryNameById(studentAccommodation.getFkAreaCountryId());
//                        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
//                            payablePlanVo.setFkAreaCountryName(result.getData());
//                        }
                        payablePlanVo.setFkAreaCountryName(countryNameMap.get(studentAccommodation.getFkAreaCountryId()));
                    }
                    //业务信息
//                    payablePlanVo.setBusinessInformation(businessChannelMapper.getFullNameById(studentAccommodation.getFkBusinessChannelId()) + "/" + studentAccommodation.getApartmentName()
//                            + "/" + formatter.format(studentAccommodation.getCheckInDate()) + "/" + formatter.format(studentAccommodation.getCheckOutDate())
//                            + "(" + studentAccommodation.getDuration() + ")");
                    payablePlanVo.setBusinessInformation(businessChannelNameMap.get(studentAccommodation.getFkBusinessChannelId()) + "/" + studentAccommodation.getApartmentName()
                            + "/" + formatter.format(studentAccommodation.getCheckInDate()) + "/" + formatter.format(studentAccommodation.getCheckOutDate())
                            + "(" + studentAccommodation.getDuration() + ")");
                }
            }
            if (GeneralTool.isNotEmpty(payablePlanVo.getFkCurrencyTypeNum())) {
                String currencyName = currencyNamesByNums.get(payablePlanVo.getFkCurrencyTypeNum());
                payablePlanVo.setFkCurrencyTypeName(currencyName);
            }
            Long companyId = 0L;
            if (GeneralTool.isNotEmpty(payablePlanVo.getFkTypeKey()) && "m_student_offer_item".equals(payablePlanVo.getFkTypeKey())) {
//                companyId = payablePlanMapper.getCompanyIdByPlanId(payablePlanVo.getFkTypeTargetId());
                companyId = studentItemCompanyMap.get(payablePlanVo.getFkTypeTargetId());
            }
            if (GeneralTool.isNotEmpty(payablePlanVo.getFkTypeKey()) && "m_student_insurance".equals(payablePlanVo.getFkTypeKey())) {
//                companyId = payablePlanMapper.getCompanyIdByInsuranceTargetId(payablePlanVo.getFkTypeTargetId());
                companyId = studentInsuranceCompanyMap.get(payablePlanVo.getFkTypeTargetId());
            }
            if (GeneralTool.isNotEmpty(payablePlanVo.getFkTypeKey()) && "m_student_accommodation".equals(payablePlanVo.getFkTypeKey())) {
                companyId = studentAccommodationCompanyMap.get(payablePlanVo.getFkTypeTargetId());
            }
            if (GeneralTool.isNotEmpty(payablePlanVo.getFkTypeKey()) && TableEnum.INSURANCE_ORDER.key.equals(payablePlanVo.getFkTypeKey())) {
                companyId = insuranceOrderCompanyMap.get(payablePlanVo.getFkTypeTargetId());
            }
            payablePlanVo.setCompanyName(companyMap.get(String.valueOf(companyId)));
        }

    }

    private Map<Long, String> getTargetNames(List<StudentOfferItemVo> offerItemDtos, Map<Long, String> studentNameByItemIds, Map<Long, String> agentNameByOfferIds) {
        Set<Long> icIds = offerItemDtos.stream().map(StudentOfferItemVo::getFkInstitutionChannelId).collect(Collectors.toSet());
        Map<Long, String> data = institutionCenterClient.getInstitutionProviderChannelByIds(icIds).getData();
        Map<Long, String> result = new HashMap<>();
        for (StudentOfferItemVo offerItem : offerItemDtos) {
            StringJoiner stringJoiner = new StringJoiner("/");
            if (GeneralTool.isNotEmpty(offerItem.getNum())) {
                stringJoiner.add("【" + offerItem.getNum() + "】");
            }
            String targetName = studentNameByItemIds.get(offerItem.getId());
            String agentName = agentNameByOfferIds.get(offerItem.getFkStudentOfferId());

            if (GeneralTool.isNotEmpty(targetName)) {
                stringJoiner.add(targetName);
            }
            if (GeneralTool.isNotEmpty(offerItem.getFkAreaCountryName())) {
                stringJoiner.add(offerItem.getFkAreaCountryName());
            }
            if (GeneralTool.isNotEmpty(offerItem.getFkInstitutionProviderName())) {
                String institutionProviderChannelName = null;
                if (GeneralTool.isNotEmpty(offerItem.getFkInstitutionChannelId())) {
                    institutionProviderChannelName = data.get(offerItem.getFkInstitutionChannelId());
                }
                stringJoiner.add(GeneralTool.isNotEmpty(institutionProviderChannelName) ? "【" + institutionProviderChannelName + "】" : "" + offerItem.getFkInstitutionProviderName());
            }
            if (GeneralTool.isNotEmpty(offerItem.getFkInstitutionName())) {
                stringJoiner.add(offerItem.getFkInstitutionName());
            }
            if (GeneralTool.isNotEmpty(offerItem.getFkCourseName())) {
                stringJoiner.add(offerItem.getFkCourseName());
            }
            if (GeneralTool.isNotEmpty(agentName)) {
                stringJoiner.add(agentName);
            }
            result.put(offerItem.getId(), stringJoiner.toString());
        }
        return result;
    }

    private List<Long> getTypeTargetIds(PayablePlanDto payablePlanDto) {
        List<Long> itemids = new ArrayList<>();
        List<Long> offerItems = offerItemService.getItemIdByStudentId(payablePlanDto.getFkStudentId());
        List<Long> insurances = studentInsuranceMapper.getInsuranceIdsByStudentId(payablePlanDto.getFkStudentId());
        List<Long> accommodations = studentAccommodationMapper.getAccommodationIdsByStudentId(payablePlanDto.getFkStudentId());
        itemids.addAll(offerItems);
        itemids.addAll(insurances);
        itemids.addAll(accommodations);
        if (GeneralTool.isEmpty(itemids)) {
            itemids = new ArrayList<>();
            itemids.add(0L);
        }
        return itemids;
    }

    private Map<String, String> getCompanyMap() {
        Result<List<com.get.permissioncenter.vo.tree.CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
            JsonConfig config = new JsonConfig();
            config.setExcludes(new String[]{"departmentTree", "totalNum"});
            JSONArray jsonArray = JSONArray.fromObject(result.getData(), config);
            List<CompanyTreeVo> companyTreeVos = JSONArray.toList(jsonArray, new CompanyTreeVo(), new JsonConfig());
            //初始为5的map
            Map<String, String> companyMap = new HashMap<>(5);
            if (GeneralTool.isNotEmpty(companyTreeVos)) {
                companyMap = companyTreeVos.stream().collect(Collectors.toMap(CompanyTreeVo::getId, CompanyTreeVo::getShortName));
            }
            return companyMap;
        }
        return new HashMap<>();
    }
}
