package com.get.partnercenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:Oliver
 * @Date: 2025/2/24  18:03
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SendEmailInfoDto {

    @ApiModelProperty("发件人登录账号-代理列表获取(必传)")
    private String fromUser;

    @ApiModelProperty("发件人电邮地址-代理列表获取(必传)")
    private String fromEmail;

    @ApiModelProperty("发件人电邮密码-代理列表获取(必传)")
    private String emailPassword;

    @ApiModelProperty("收件人名称(必传)")
    private String toUser;

    @ApiModelProperty("发件人邮箱(必传)")
    private String toEmail;

    @ApiModelProperty("代理ID(必传)")
    private Long agentId;

    @ApiModelProperty("代理公司ID-代理列表获取(必传)")
    private Long companyId;
}
