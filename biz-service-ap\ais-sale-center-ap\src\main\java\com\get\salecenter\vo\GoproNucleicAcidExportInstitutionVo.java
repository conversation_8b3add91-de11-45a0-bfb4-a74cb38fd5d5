package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Hardy
 * @create: 2022/6/6 11:18
 * @verison: 1.0
 * @description:
 */
@Data
public class GoproNucleicAcidExportInstitutionVo {

    @ApiModelProperty(value = "序号")
    private Integer orderId;

    @ApiModelProperty("名字")
    private String name;

    @ApiModelProperty("代表院校")
    private String institution;

    @ApiModelProperty("联系电话")
    private String mobile;

    @ApiModelProperty("身份证号")
    private String identityCard;

    @ApiModelProperty("出发国家")
    private String areaCountryName;

    @ApiModelProperty("出发省份")
    private String areaStateName;

    @ApiModelProperty("出发城市")
    private String areaCityName;

}
