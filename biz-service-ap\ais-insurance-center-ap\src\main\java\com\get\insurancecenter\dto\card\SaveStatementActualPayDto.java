package com.get.insurancecenter.dto.card;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Author:Oliver
 * @Date: 2025/8/5
 * @Version 1.0
 * @apiNote:保存信用卡交易记录
 */
@Data
public class SaveStatementActualPayDto {

    @ApiModelProperty(value = "交易记录ID")
    @NotNull(message = "交易记录ID不能为空")
    private Long id;

    @ApiModelProperty(value = "实付金额")
    @NotNull(message = "实付金额不能为空")
    private BigDecimal amountRmb;

    @ApiModelProperty(value = "汇率")
    private BigDecimal exchangeRateRmb;

}
