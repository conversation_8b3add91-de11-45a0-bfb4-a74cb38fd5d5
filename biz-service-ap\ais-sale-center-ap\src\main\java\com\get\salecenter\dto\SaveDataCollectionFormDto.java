package com.get.salecenter.dto;

import com.get.salecenter.entity.convention.BoothDataCollection;
import com.get.salecenter.entity.convention.ConventionMediaAndAttached;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 学校机构资料收集表单 考题Dto
 */
@Data
public class SaveDataCollectionFormDto {

    @ApiModelProperty(value = "邀请码")
    @NotBlank(message = "邀请码不能为空")
    private String receiptCode;

    @ApiModelProperty(value = "简介")
    @NotBlank(message = "简介不能为空")
    private String profile;

    @ApiModelProperty(value = "附件")
    @NotNull(message = "附件不能为空")
    private List<ConventionMediaAndAttached> mediaAndAttachedDtoList;

    @ApiModelProperty(value = "考题")
    @NotNull(message = "考题不能为空")
    private List<DataCollectionQuestionDto> dataCollectionQuestionList;

}
