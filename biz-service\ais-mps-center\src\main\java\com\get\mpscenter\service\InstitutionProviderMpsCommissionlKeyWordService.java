package com.get.mpscenter.service;

import com.get.common.result.SearchBean;
import com.get.core.mybatis.service.GetService;
import com.get.core.mybatis.utils.ValidList;
import com.get.mpscenter.vo.KeyWordVo;
import com.get.mpscenter.entity.MpsKeyWord;
import com.get.mpscenter.dto.KeyWordMajorLevelSelectDto;
import com.get.mpscenter.dto.KeyWordMajorLevelDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/3 17:22
 * @desciption:
 */
public interface InstitutionProviderMpsCommissionlKeyWordService extends GetService<MpsKeyWord> {

    /**
     * 批量新增
     * @param keyWordMajorLevelDtos
     */
    void batchAdd(ValidList<KeyWordMajorLevelDto> keyWordMajorLevelDtos);

    /**
     * 删除
     * @param id
     */
    void delete(Long id);

    /**
     * 上移下移
     * @param keyWordMajorLevelVos
     */
    void movingOrder(List<KeyWordMajorLevelDto> keyWordMajorLevelVos);

    /**
     * 修改
     * @param keyWordMajorLevelDto
     * @return
     */
    KeyWordVo updateKeyWord(KeyWordMajorLevelDto keyWordMajorLevelDto);

    /**
     * 列表数据
     * @param data
     * @param page
     * @return
     */
    List<KeyWordVo> getKeyWords(KeyWordMajorLevelSelectDto data, SearchBean<KeyWordMajorLevelSelectDto> page);

    /**
     * 详情
     * @param id
     * @return
     */
    KeyWordVo findKeyWordById(Long id);
}
