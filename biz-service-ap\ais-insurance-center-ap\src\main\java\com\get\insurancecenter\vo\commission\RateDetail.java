package com.get.insurancecenter.vo.commission;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/6/18
 * @Version 1.0
 * @apiNote:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RateDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "原币种")
    private String from;

    @ApiModelProperty(value = "目标币种")
    private String to;

    @ApiModelProperty(value = "原币种名称")
    private String fromname;

    @ApiModelProperty(value = "目标币种名称")
    private String toname;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间")
    private Date updatetime;

    @ApiModelProperty(value = "汇率")
    private BigDecimal rate;

    @ApiModelProperty(value = "目标币种转换后的金额")
    private BigDecimal camount;
}
