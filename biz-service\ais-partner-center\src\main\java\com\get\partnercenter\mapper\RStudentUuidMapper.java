package com.get.partnercenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.get.partnercenter.entity.RStudentUuidEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【r_student_uuid】的数据库操作Mapper
* @createDate 2025-03-31 18:42:26
* @Entity com.get.partnercenter.entity.RStudentUuid
*/
@Mapper
@DS("saledb")
public interface RStudentUuidMapper extends BaseMapper<RStudentUuidEntity> {

}




