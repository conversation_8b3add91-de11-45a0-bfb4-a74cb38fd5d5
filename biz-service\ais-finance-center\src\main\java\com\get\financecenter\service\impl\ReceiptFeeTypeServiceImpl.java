package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.ReceiptFeeTypeMapper;
import com.get.financecenter.dao.ReceiptFormMapper;
import com.get.financecenter.dao.VouchReceiptRegisterMapper;
import com.get.financecenter.dto.ReceiptFeeTypeDto;
import com.get.financecenter.entity.ReceiptFeeType;
import com.get.financecenter.entity.VouchReceiptRegister;
import com.get.financecenter.enums.ReceiptFeeTypeGroupEnum;
import com.get.financecenter.enums.RelationTargetKeyEnum;
import com.get.financecenter.service.IReceiptFeeTypeService;
import com.get.financecenter.utils.GetAccountingCodeNameUtils;
import com.get.financecenter.vo.BaseSelectVo;
import com.get.financecenter.vo.ReceiptFeeTypeVo;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @DATE: 2021/12/6
 * @TIME: 15:59
 * @Description:
 **/
@Service
public class ReceiptFeeTypeServiceImpl extends BaseServiceImpl<ReceiptFeeTypeMapper, ReceiptFeeType> implements IReceiptFeeTypeService {
    @Resource
    private ReceiptFeeTypeMapper receiptFeeTypeMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private ReceiptFormMapper receiptFormMapper;
    @Resource
    private GetAccountingCodeNameUtils  getAccountingCodeNameUtils;

    @Resource
    private VouchReceiptRegisterMapper vouchReceiptRegisterMapper;

    @Override
    public List<ReceiptFeeTypeVo> getReceiptFeeTypes(ReceiptFeeTypeDto receiptFeeTypeDto, Page page) {

        QueryWrapper<ReceiptFeeType> wrapper = new QueryWrapper();
        if (GeneralTool.isNotEmpty(receiptFeeTypeDto.getTypeName())) {
            wrapper.lambda().eq(ReceiptFeeType::getTypeName, receiptFeeTypeDto.getTypeName());
        }
        if (GeneralTool.isNotEmpty(receiptFeeTypeDto.getFkAccountingItemId())){
            wrapper.lambda().eq(ReceiptFeeType::getFkAccountingItemId, receiptFeeTypeDto.getFkAccountingItemId());
        }
        if (GeneralTool.isNotEmpty(receiptFeeTypeDto.getKeyWord())){
            wrapper.lambda().like(ReceiptFeeType::getTypeName, receiptFeeTypeDto.getKeyWord());
            wrapper.lambda().or().like(ReceiptFeeType::getGmtCreateUser, receiptFeeTypeDto.getKeyWord());
            wrapper.lambda().or().like(ReceiptFeeType::getGmtModifiedUser, receiptFeeTypeDto.getKeyWord());
        }
        wrapper.orderByDesc("IFNULL(view_order,0)");
        wrapper.orderByAsc("CONVERT(type_name USING gbk)");
        IPage<ReceiptFeeType> pages = receiptFeeTypeMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        page.setAll((int) pages.getTotal());

        List<ReceiptFeeType> receiptFeeTypes = pages.getRecords();
        page.setAll((int) pages.getTotal());

        List<ReceiptFeeTypeVo> receiptFeeTypeVos = receiptFeeTypes.stream().map(receiptFeeType ->{
            ReceiptFeeTypeVo receiptFeeTypeVo = BeanCopyUtils.objClone(receiptFeeType, ReceiptFeeTypeVo::new);
            receiptFeeTypeVo.setAccountingItemName(getAccountingCodeNameUtils.setAccountingCodeName(receiptFeeTypeVo.getFkAccountingItemId()));
            if (GeneralTool.isNotEmpty(receiptFeeTypeVo.getTypeGroupKey())){
                receiptFeeTypeVo.setTypeGroupKeyName(ReceiptFeeTypeGroupEnum.getNameByTypeGroupKey(receiptFeeTypeVo.getTypeGroupKey()));
            }
            if (GeneralTool.isNotEmpty(receiptFeeTypeVo.getRelationTargetKey())){
                receiptFeeTypeVo.setRelationTargetKeyName(RelationTargetKeyEnum.getNameByRelationTargetKey(receiptFeeTypeVo.getRelationTargetKey()));
            }
            return receiptFeeTypeVo;
        }).collect(Collectors.toList());
        return receiptFeeTypeVos;
    }

    @Override
    public ReceiptFeeTypeVo findReceiptFeeTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ReceiptFeeType receiptFeeType = receiptFeeTypeMapper.selectById(id);
        if (GeneralTool.isEmpty(receiptFeeType)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ReceiptFeeTypeVo receiptFeeTypeVo = BeanCopyUtils.objClone(receiptFeeType, ReceiptFeeTypeVo::new);
        receiptFeeTypeVo.setAccountingItemName(getAccountingCodeNameUtils.setAccountingCodeName(receiptFeeTypeVo.getFkAccountingItemId()));
        if (GeneralTool.isNotEmpty(receiptFeeTypeVo.getTypeGroupKey())){
            receiptFeeTypeVo.setTypeGroupKeyName(ReceiptFeeTypeGroupEnum.getNameByTypeGroupKey(receiptFeeTypeVo.getTypeGroupKey()));
        }
        if (GeneralTool.isNotEmpty(receiptFeeTypeVo.getRelationTargetKey())){
            receiptFeeTypeVo.setRelationTargetKeyName(RelationTargetKeyEnum.getNameByRelationTargetKey(receiptFeeTypeVo.getRelationTargetKey()));
        }
        return receiptFeeTypeVo;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<ReceiptFeeTypeDto> receiptFeeTypeDtos) {
        if (GeneralTool.isEmpty(receiptFeeTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (ReceiptFeeTypeDto receiptFeeTypeDto : receiptFeeTypeDtos) {
            if (GeneralTool.isEmpty(receiptFeeTypeDto.getId())) {
                ReceiptFeeType receiptFeeType = BeanCopyUtils.objClone(receiptFeeTypeDto, ReceiptFeeType::new);

                if (GeneralTool.isNotEmpty(receiptFeeType.getVouchSummary()) && receiptFeeType.getVouchSummary().length() > 200) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("character_limit_exceeded")+"200" + " (" + receiptFeeType.getVouchSummary() + ")");
                }
                if (validateAdd(receiptFeeTypeDto)) {
                    receiptFeeType.setViewOrder(receiptFeeTypeMapper.getMaxViewOrder());
                    utilService.setCreateInfo(receiptFeeType);
                    receiptFeeTypeMapper.insert(receiptFeeType);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("duplicate_name") + " (" + receiptFeeType.getTypeName() + ")");
                }
            } else {
                ReceiptFeeType receiptFeeType = BeanCopyUtils.objClone(receiptFeeTypeDto, ReceiptFeeType::new);
                if (validateUpdate(receiptFeeTypeDto)) {
                    utilService.updateUserInfoToEntity(receiptFeeType);
                    receiptFeeTypeMapper.updateById(receiptFeeType);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("duplicate_name") + " (" + receiptFeeType.getTypeName() + ")");
                }
            }
        }
    }

    private boolean validateAdd(ReceiptFeeTypeDto receiptFeeTypeDto) {
        List<ReceiptFeeType> list = this.receiptFeeTypeMapper.selectList(Wrappers.<ReceiptFeeType>query().lambda()
                .eq(ReceiptFeeType::getTypeName, receiptFeeTypeDto.getTypeName()));
        return GeneralTool.isEmpty(list);
    }

    private boolean validateUpdate(ReceiptFeeTypeDto receiptFeeTypeDto) {
        List<ReceiptFeeType> list = this.receiptFeeTypeMapper.selectList(Wrappers.<ReceiptFeeType>query().lambda()
                .eq(ReceiptFeeType::getTypeName, receiptFeeTypeDto.getTypeName()));
        return list.size() <= 0 || list.get(0).getId().equals(receiptFeeTypeDto.getId());
    }


    @Override
    public Long addReceiptFeeType(ReceiptFeeTypeDto receiptFeeTypeDto) {
        if (GeneralTool.isEmpty(receiptFeeTypeDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ReceiptFeeType receiptFeeType = BeanCopyUtils.objClone(receiptFeeTypeDto, ReceiptFeeType::new);
        utilService.setCreateInfo(receiptFeeType);
        receiptFeeTypeMapper.insert(receiptFeeType);
        return receiptFeeType.getId();
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (receiptFeeTypeMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //校验是否与收款单做关联。
        Integer count = receiptFormMapper.getReceiptFormCountByTypeId(id);
        List<VouchReceiptRegister> vouchReceiptRegisters = vouchReceiptRegisterMapper.selectList(new LambdaQueryWrapper<VouchReceiptRegister>().eq(VouchReceiptRegister::getFkReceiptFeeTypeId, id));
        if (GeneralTool.isNotEmpty(vouchReceiptRegisters)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("cancel_obj_used_by_vouch_receipt_register"));
        }
        if (count > 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        else {
            receiptFeeTypeMapper.deleteById(id);
        }
    }

    @Override
    public ReceiptFeeTypeVo updateReceiptFeeType(ReceiptFeeTypeDto receiptFeeTypeDto) {
        if (receiptFeeTypeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ReceiptFeeType result = receiptFeeTypeMapper.selectById(receiptFeeTypeDto.getId());
        if (GeneralTool.isEmpty(result)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_obj_null"));
        }
        ReceiptFeeType receiptFeeType = BeanCopyUtils.objClone(receiptFeeTypeDto, ReceiptFeeType::new);
        if (!receiptFeeType.getTypeName().equals(result.getTypeName())){
            receiptFeeType.setTypeName(receiptFeeType.getTypeName().replace(" ", "").trim());
            if (receiptFeeTypeMapper.checkName(receiptFeeType.getTypeName()) > 0){
                throw new GetServiceException(LocaleMessageUtils.getMessage("duplicate_name" )+ " (" + receiptFeeType.getTypeName() + ")");
            }
        }
        utilService.updateUserInfoToEntity(receiptFeeType);
        receiptFeeTypeMapper.updateById(receiptFeeType);
        return findReceiptFeeTypeById(receiptFeeType.getId());
    }

    @Override
    public void sort(List<ReceiptFeeTypeDto> receiptFeeTypeDtos) {
        if (GeneralTool.isEmpty(receiptFeeTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ReceiptFeeType r1 = BeanCopyUtils.objClone(receiptFeeTypeDtos.get(0), ReceiptFeeType::new);
        ReceiptFeeType receiptFeeTypeOne = receiptFeeTypeMapper.selectById(r1.getId());
        if (GeneralTool.isEmpty(receiptFeeTypeOne)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        Integer oneorder = receiptFeeTypeOne.getViewOrder();
        ReceiptFeeType r2 = BeanCopyUtils.objClone(receiptFeeTypeDtos.get(1), ReceiptFeeType::new);
        ReceiptFeeType receiptFeeTypeTwo = receiptFeeTypeMapper.selectById(r2.getId());
        if (GeneralTool.isEmpty(receiptFeeTypeTwo)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        Integer twoorder = receiptFeeTypeTwo.getViewOrder();
        r1.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(r1);
        r2.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(r2);
        receiptFeeTypeMapper.updateById(r1);
        receiptFeeTypeMapper.updateById(r2);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(Integer start, Integer end) {
        if (GeneralTool.isEmpty(start) || GeneralTool.isEmpty( end)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("sort_param_error"));
        }
        LambdaQueryWrapper<ReceiptFeeType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (end > start){
            lambdaQueryWrapper.between(ReceiptFeeType::getViewOrder,start,end).orderByDesc(ReceiptFeeType::getViewOrder);
        }else {
            lambdaQueryWrapper.between(ReceiptFeeType::getViewOrder,end,start).orderByDesc(ReceiptFeeType::getViewOrder);

        }
        List<ReceiptFeeType> receiptFeeTypes = list(lambdaQueryWrapper);
        List<ReceiptFeeType> updateList = new ArrayList<>();
        if (end > start){
            int finalEnd = end;
            List<ReceiptFeeType> sortedList = Lists.newArrayList();
            ReceiptFeeType receiptFeeTypeLast = receiptFeeTypes.get(receiptFeeTypes.size() - 1);
            sortedList.add(receiptFeeTypeLast);
            receiptFeeTypes.remove(receiptFeeTypes.size() - 1);
            sortedList.addAll(receiptFeeTypes);
            for (ReceiptFeeType receiptFeeType : sortedList) {
                receiptFeeType.setViewOrder(finalEnd);
                finalEnd--;
            }
            updateList.addAll(sortedList);
        }else {
            int finalStart = start;
            List<ReceiptFeeType> sortedList = Lists.newArrayList();
            ReceiptFeeType receiptFeeTypeFirst = receiptFeeTypes.get(0);
            receiptFeeTypes.remove(0);
            sortedList.addAll(receiptFeeTypes);
            sortedList.add(receiptFeeTypeFirst);
            for (ReceiptFeeType receiptFeeType : sortedList) {
                receiptFeeType.setViewOrder(finalStart);
                finalStart--;
            }
            updateList.addAll(sortedList);
        }

        if (GeneralTool.isNotEmpty(updateList)){
            boolean batch = updateBatchById(updateList);
            if (!batch){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
    }

    @Override
    public List<BaseSelectVo> getReceiptFeeTypeSelect() {
        LambdaQueryWrapper<ReceiptFeeType> receiptFeeTypeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        //查询字段
        receiptFeeTypeLambdaQueryWrapper.select(ReceiptFeeType::getId, ReceiptFeeType::getTypeName, ReceiptFeeType::getRelationTargetKey, ReceiptFeeType::getFkAccountingItemId);
        List<ReceiptFeeType> receiptFeeTypeSelect = receiptFeeTypeMapper.selectList(receiptFeeTypeLambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(receiptFeeTypeSelect)){
            return receiptFeeTypeSelect.stream().map(receiptFeeType -> {
                BaseSelectVo baseSelectVo = new BaseSelectVo();
                baseSelectVo.setId(receiptFeeType.getId());
                baseSelectVo.setName(receiptFeeType.getTypeName());
                if (GeneralTool.isNotEmpty(receiptFeeType.getRelationTargetKey())){
                    baseSelectVo.setRelationTargetKey(receiptFeeType.getRelationTargetKey());
                    baseSelectVo.setRelationTargetKeyName(RelationTargetKeyEnum.getNameByRelationTargetKey(receiptFeeType.getRelationTargetKey()));
                }
                baseSelectVo.setFkAccountingItemId(receiptFeeType.getFkAccountingItemId());
                baseSelectVo.setAccountingItemName(getAccountingCodeNameUtils.setAccountingCodeName(receiptFeeType.getFkAccountingItemId()));
                return baseSelectVo;
            }).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    public List<ReceiptFeeTypeVo> getReceiptFeeTypeGroup() {
        List<ReceiptFeeTypeVo> options = ReceiptFeeTypeGroupEnum.getOptions();
        return options;
    }
}
