package com.get.salecenter.service.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.ContactPersonCompanyMapper;
import com.get.salecenter.entity.SaleContactPersonCompany;
import com.get.salecenter.service.IContactPersonCompanyService;
import com.get.salecenter.dto.ContactPersonCompanyDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/9/15
 * @TIME: 16:18
 * @Description:
 **/
@Service
public class ContactPersonCompanyServiceImpl extends BaseServiceImpl<ContactPersonCompanyMapper,SaleContactPersonCompany> implements IContactPersonCompanyService {
    @Resource
    private ContactPersonCompanyMapper contactPersonCompanyMapper;
    @Resource
    private UtilService utilService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editContactPersonCompany(List<ContactPersonCompanyDto> contactPersonCompanyDtos) {
        if (GeneralTool.isEmpty(contactPersonCompanyDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        Long companyId = contactPersonCompanyDtos.get(0).getFkCompanyId();
        if (GeneralTool.isEmpty(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("edit_companyId"));
        }
        //删除之前的记录
//        Example example = new Example(ContactPersonCompany.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkContactPersonId", contactPersonCompanyDtos.get(0).getFkContactPersonId());
//        contactPersonCompanyMapper.deleteByExample(example);
        List<Long> companyIds = SecureUtil.getCompanyIds();
        contactPersonCompanyMapper.delete(Wrappers.<SaleContactPersonCompany>lambdaQuery().eq(SaleContactPersonCompany::getFkContactPersonId, contactPersonCompanyDtos.get(0).getFkContactPersonId()).in(SaleContactPersonCompany::getFkCompanyId,companyIds));

        List<SaleContactPersonCompany> collect = contactPersonCompanyDtos.stream().map(contactPersonCompanyVo ->
                BeanCopyUtils.objClone(contactPersonCompanyVo, SaleContactPersonCompany::new)).collect(Collectors.toList());
        collect.forEach(contactPersonCompany -> contactPersonCompanyMapper.insertSelective(contactPersonCompany));

    }

    @Override
    public void addBatchRelation(List<ContactPersonCompanyDto> relations) {
        if (GeneralTool.isNotEmpty(relations)) {
            List<SaleContactPersonCompany> companies = relations.stream().map(r -> {
                SaleContactPersonCompany personCompany = new SaleContactPersonCompany();
                BeanCopyUtils.copyProperties(r,personCompany);
                utilService.setCreateInfo(personCompany);
                return personCompany;
            }).collect(Collectors.toList());
            if (GeneralTool.isNotEmpty(companies)) {
                saveBatch(companies);
            }
        }
    }

    @Override
    public Long addRelation(ContactPersonCompanyDto relation) {
        SaleContactPersonCompany personCompany = BeanCopyUtils.objClone(relation, SaleContactPersonCompany::new);
        utilService.updateUserInfoToEntity(personCompany);
        contactPersonCompanyMapper.insertSelective(personCompany);
        return relation.getId();
    }

    @Override
    public List<Long> getRelationByCompanyId(List<Long> companyId) {
        if (GeneralTool.isEmpty(companyId)) {
            return null;
        }
//        Example example = new Example(ContactPersonCompany.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andIn("fkCompanyId", companyId);
//        List<ContactPersonCompany> personCompanies = contactPersonCompanyMapper.selectByExample(example);
        List<SaleContactPersonCompany> personCompanies = contactPersonCompanyMapper.selectList(Wrappers.<SaleContactPersonCompany>lambdaQuery().in(SaleContactPersonCompany::getFkCompanyId, companyId));
        return personCompanies.stream().map(SaleContactPersonCompany::getFkContactPersonId).collect(Collectors.toList());
    }

    @Override
    public List<Long> getRelationByContactId(Long contactPersonId) {
        if (GeneralTool.isEmpty(contactPersonId)) {
            return null;
        }
//        Example example = new Example(ContactPersonCompany.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkContactPersonId", contactPersonId);
//        List<ContactPersonCompany> personCompanies = contactPersonCompanyMapper.selectByExample(example);
        List<SaleContactPersonCompany> personCompanies = contactPersonCompanyMapper.selectList(Wrappers.<SaleContactPersonCompany>lambdaQuery().eq(SaleContactPersonCompany::getFkContactPersonId, contactPersonId));
        return personCompanies.stream().map(SaleContactPersonCompany::getFkCompanyId).collect(Collectors.toList());
    }

    @Override
    public Map<Long, Set<Long>> getRelationByContactIds(Set<Long> contactPersonIds) {
        Map<Long, Set<Long>> map = new HashMap<>();
        if (GeneralTool.isEmpty(contactPersonIds)) {
            return map;
        }
//        Example example = new Example(ContactPersonCompany.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andIn("fkContactPersonId", contactPersonIds);
//        List<ContactPersonCompany> personCompanies = contactPersonCompanyMapper.selectByExample(example);

        List<SaleContactPersonCompany> personCompanies = contactPersonCompanyMapper.selectList(Wrappers.<SaleContactPersonCompany>lambdaQuery().in(SaleContactPersonCompany::getFkContactPersonId, contactPersonIds));

        if (GeneralTool.isEmpty(personCompanies)) {
            return map;
        }
        for (SaleContactPersonCompany personCompany : personCompanies) {
            //如果集合包含这个id,则往原来的集合添加公司id
            if (map.containsKey(personCompany.getFkContactPersonId())) {
                Set<Long> beforeRelationIds = map.get(personCompany.getFkContactPersonId());
                beforeRelationIds.add(personCompany.getFkCompanyId());
                map.put(personCompany.getFkContactPersonId(), beforeRelationIds);
                continue;
            }
            //如果没有包含,则往里面添加新数据
            Set<Long> relationSet = new HashSet<>();
            relationSet.add(personCompany.getFkCompanyId());
            map.put(personCompany.getFkContactPersonId(), relationSet);
        }
        List<Long> companyIds = SecureUtil.getCompanyIds();
        if (GeneralTool.isNotEmpty(companyIds)){
            map.values().stream().forEach(set -> set.removeIf(id -> !companyIds.contains(id)));
        }
        return map;
    }

    /**
     * 删除联系人-公司关系
     *
     * @Date 10:42 2021/7/5
     * <AUTHOR>
     */
    @Override
    public void deletePersonRelation(Long personId) {
//        Example example = new Example(ContactPersonCompany.class);
//        example.createCriteria().andEqualTo("fkContactPersonId", personId);
//        contactPersonCompanyMapper.deleteByExample(example);

        contactPersonCompanyMapper.delete(Wrappers.<SaleContactPersonCompany>lambdaQuery().eq(SaleContactPersonCompany::getFkContactPersonId, personId));

    }

}
