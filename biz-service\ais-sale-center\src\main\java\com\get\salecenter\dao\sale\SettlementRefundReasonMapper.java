package com.get.salecenter.dao.sale;


import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.entity.SettlementRefundReason;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface SettlementRefundReasonMapper  extends GetMapper<SettlementRefundReason> {
    Integer getMaxOrder();

    List<BaseSelectEntity> getServiceTypeList();

}
