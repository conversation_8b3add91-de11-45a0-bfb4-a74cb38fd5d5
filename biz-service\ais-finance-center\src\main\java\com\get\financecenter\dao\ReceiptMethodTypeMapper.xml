<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.ReceiptMethodTypeMapper">

    <select id="getReceiptMethodTypes" resultType="com.get.financecenter.vo.ReceiptMethodTypeVo">
        select
        urmt.id,
        urmt.type_name,
        urmt.fk_accounting_item_id,
        urmt.relation_target_key,
        urmt.view_order,
        urmt.gmt_create_user,
        urmt.gmt_create,
        urmt.gmt_modified_user,
        urmt.gmt_modified
        from u_receipt_method_type urmt
        where 1=1
        <if test="receiptMethodTypeDto.fkAccountingItemId != null and receiptMethodTypeDto.fkAccountingItemId !=''">
            AND urmt.fk_accounting_item_id = #{receiptMethodTypeDto.fkAccountingItemId}
        </if>
        <if test="receiptMethodTypeDto.typeName != null and receiptMethodTypeDto.typeName !=''">
            AND urmt.type_name like concat('%',#{receiptMethodTypeDto.typeName},'%')
        </if>
        <if test="receiptMethodTypeDto.keyWord != null and receiptMethodTypeDto.keyWord !=''">
            AND (urmt.type_name like concat('%',#{receiptMethodTypeDto.keyWord},'%')
            OR urmt.gmt_create_user like concat('%',#{receiptMethodTypeDto.keyWord},'%')
            OR urmt.gmt_modified_user like concat('%',#{receiptMethodTypeDto.keyWord},'%'))
        </if>
        order by urmt.view_order desc
    </select>

    <select id="checkName" resultType="int">
        select count(*) from u_receipt_method_type urmt where urmt.type_name = #{typeName}
    </select>

    <select id="getMaxViewOrder" resultType="java.lang.Integer">
        SELECT
            IFNULL(max(view_order)+1,0) view_order
        FROM u_receipt_method_type
    </select>

    <insert id="insertSelective">

    </insert>

    <update id="updateBatchById">
        <foreach collection="updateList" item="item" index="index" separator=";">
            UPDATE u_receipt_method_type
            SET
            <if test="item.viewOrder != null and item.viewOrder !=''">
                view_order = #{item.viewOrder},
            </if>
            gmt_modified = #{item.gmtModified},
            gmt_modified_user = #{item.gmtModifiedUser}
            WHERE id = #{item.id}
        </foreach>
    </update>

    <select id="selectByFkAccountingItemId" resultType="com.get.financecenter.entity.ReceiptMethodType">
        select * from u_receipt_method_type where fk_accounting_item_id = #{fkAccountingItemId}
    </select>
</mapper>