<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.StudentOfferItemPreMajorLevelMapper">
  <insert id="insert" parameterType="com.get.salecenter.entity.StudentOfferItemPreMajorLevel" useGeneratedKeys="true"
          keyProperty="id">
    insert into r_student_offer_item_pre_major_level (id, fk_student_offer_item_id, fk_major_level_id,
      gmt_create, gmt_create_user, gmt_modified,
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkStudentOfferItemId,jdbcType=BIGINT}, #{fkMajorLevelId,jdbcType=BIGINT},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP},
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.StudentOfferItemPreMajorLevel" useGeneratedKeys="true"
          keyProperty="id">
    insert into r_student_offer_item_pre_major_level
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkStudentOfferItemId != null">
        fk_student_offer_item_id,
      </if>
      <if test="fkMajorLevelId != null">
        fk_major_level_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkStudentOfferItemId != null">
        #{fkStudentOfferItemId,jdbcType=BIGINT},
      </if>
      <if test="fkMajorLevelId != null">
        #{fkMajorLevelId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateById" parameterType="com.get.salecenter.entity.StudentOfferItemPreMajorLevel">
    update r_student_offer_item_pre_major_level
    <set>
      <if test="fkStudentOfferItemId != null">
        fk_student_offer_item_id = #{fkStudentOfferItemId,jdbcType=BIGINT},
      </if>
      <if test="fkMajorLevelId != null">
        fk_major_level_id = #{fkMajorLevelId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.salecenter.entity.StudentOfferItemPreMajorLevel">
    update r_student_offer_item_pre_major_level
    set fk_student_offer_item_id = #{fkStudentOfferItemId,jdbcType=BIGINT},
      fk_major_level_id = #{fkMajorLevelId,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>