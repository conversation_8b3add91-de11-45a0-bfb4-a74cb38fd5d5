package com.get.examcenter.utils;

import cn.hutool.core.io.IoUtil;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.itextpdf.text.Document;
import com.itextpdf.text.pdf.PdfWriter;
import com.itextpdf.tool.xml.XMLWorkerHelper;
import freemarker.core.HTMLOutputFormat;
import freemarker.core.XMLOutputFormat;
import freemarker.template.Configuration;
import freemarker.template.Template;
import org.apache.poi.util.IOUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.OutputStream;
import java.io.StringWriter;
import java.io.Writer;
import java.net.URL;
import java.nio.charset.Charset;
import java.util.Map;

/**
 * Created by <PERSON>.
 * Time: 9:46
 * Date: 2021/9/13
 * Description:创建word文件
 */
public class DocUtils {
    private static Configuration configuration = null;

    private static void init() {
        if (configuration == null) {
            configuration = new Configuration();
            configuration.setDefaultEncoding("utf-8");
            configuration.setOutputFormat(XMLOutputFormat.INSTANCE);
        }
    }

    /**
     * 生成word文件
     *
     * @param dataMap  组装的数据
     * @param response 浏览器的输出流
     * @param ftlName  模板路径，resources/template下的模板文件名称，例如examQuestion.ftl
     * @throws Exception
     */
    public static void createDoc(Map<String, Object> dataMap, HttpServletResponse response, String ftlName, String fileName) throws Exception {
        init();
        //dataMap 要填入模本的数据文件
        //设置模本装置方法和路径,FreeMarker支持多种模板装载方法。可以重servlet，classpath，数据库装载，
        //这里我们的模板是放在template包下面
        configuration.setClassForTemplateLoading(DocUtils.class, "/template");
        //xxx.ftl为要装载的模板
        Template t = configuration.getTemplate(ftlName);
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;fileName=" + fileName + ".doc");
        //获取浏览器的输出流
        //OutputStreamWriter oWriter = new OutputStreamWriter(response.getOutputStream(),"UTF-8");
        //这个地方对流的编码不可或缺，使用main（）单独调用时，应该可以，但是如果是web请求导出时导出后word文档就会打不开，并且包XML文件错误。主要是编码格式不正确，无法解析。
        //Writer out = new BufferedWriter(oWriter);
        ByteArrayInputStream in = null;
        OutputStream out = response.getOutputStream();
        try {
            StringWriter swriter = new StringWriter();
            //生成文件
            t.process(dataMap, swriter);
            in = new ByteArrayInputStream(swriter.toString().getBytes("utf-8"));//这里一定要设置utf-8编码 否则导出的word中中文会是乱码
            IOUtils.copy(in, out);
            out.flush();
        } catch (Exception e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_export_fail"));
        } finally {
            IoUtil.close(in);
            IoUtil.close(out);
        }
    }


    /**
     * 获取模板内容
     *
     * @param templateName 模板文件名
     * @param paramMap     模板参数
     * @return
     * @throws Exception
     */
    public static String getTemplateContent(String templateName, Map<String, Object> paramMap) throws Exception {
        ClassLoader classLoader = DocUtils.class.getClassLoader();
        URL resource = classLoader.getResource("template");
        String templateDirectory = resource.toURI().getPath();
        Configuration configuration = new Configuration();
        configuration.setDefaultEncoding("utf-8");
        configuration.setOutputFormat(HTMLOutputFormat.INSTANCE);
        configuration.setDirectoryForTemplateLoading(new File(templateDirectory));

        Writer out = new StringWriter();
        Template template = configuration.getTemplate(templateName, "UTF-8");
        template.process(paramMap, out);
        out.flush();
        out.close();
        return out.toString();
    }


    /**
     * HTML 转 PDF
     *
     * @param content  html内容
     * @param response 输出pdf路径
     * @return 是否创建成功
     */
    public static void html2Pdf(String content, HttpServletResponse response, String fileName) {
        OutputStream outputStream = null;
        try {
            outputStream = response.getOutputStream();
            Document document = new Document();
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;fileName=" + fileName + ".pdf");
            PdfWriter writer = PdfWriter.getInstance(document, outputStream);
            document.open();
            XMLWorkerHelper.getInstance().parseXHtml(writer, document,
                    new ByteArrayInputStream(content.getBytes()), null, Charset.forName("UTF-8"));
            document.close();
        } catch (Exception e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_export_fail"));
        } finally {
            IoUtil.close(outputStream);
        }
    }

//    public static void main(String[] args) {
//        System.out.println(LocaleMessageUtils.getMessage("id_null"));
//    }
}
