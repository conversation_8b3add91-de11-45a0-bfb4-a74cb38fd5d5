package com.get.officecenter.controller;

import com.alibaba.fastjson.JSONObject;
import com.get.officecenter.config.WxCpTpConfiguration;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;


/**
 * created by lanx<PERSON><PERSON>@2dfire.com on 2020/2/23
 * 授权相关(第三方应用)
 */
@RestController
@RequestMapping("/wx/oauth")
public class WxCpTpOauthController extends BaseController{
    @Value("${wx.cptp.appConfigs[0].suiteId}")
    private String SUIT_ID;

    private WxCpTpService tpService;
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    private String oauthUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&scope=snsapi_privateinfo&state=%s#wechat_redirect";
    private String loginUrl = "http://chenxingxing.51vip.biz/wx/oauth/login";


    /**
     * 拆分链接
     * @param url
     */
    @GetMapping("/jump")
    public void jump(String url,
                     HttpServletResponse response) throws IOException {
        if (StringUtils.isBlank(url)) {
            throw new IllegalArgumentException("url is empty");
        }
        oauthUrl = String.format(oauthUrl, SUIT_ID, loginUrl, url);
        logger.info("跳转url:" + oauthUrl);
        response.sendRedirect(oauthUrl);
    }


//    /**
//     * 授权链接  通过code换取用户信息
//     */
//    @VerifyLogin(IsVerify = false)
//    @VerifyPermission(IsVerify = false)
//    @GetMapping("/login")
//    public void login(String code,
//                      String state,
//                      HttpServletRequest request,
//                      HttpServletResponse response) {
//        if (StringUtils.isBlank(code)) {
//            throw new IllegalArgumentException("code is empty");
//        }
//        try {
//            tpService = WxCpTpConfiguration.getCpTpService(SUIT_ID);
//            JSONObject userInfo3rd = getUserInfo3rd(tpService, code);
//            if (userInfo3rd == null){
//                throw new Exception("用户信息获取失败");
//            }
//            JSONObject userInfoDetail = getUserDetail3rd(tpService, userInfo3rd.get("user_ticket").toString());
//            request.getSession().setAttribute("token", userInfoDetail);
//            response.sendRedirect(state);
//        } catch (Exception e) {
//            this.logger.error(e.getMessage(), e);
//        }
//    }


}
