package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.salecenter.vo.AgentContractTypeVo;
import com.get.salecenter.service.IAgentContractTypeService;
import com.get.salecenter.dto.AgentContractTypeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/10/30
 * @TIME: 15:27
 * @Description: 代理合同类型管理
 **/

@Api(tags = "代理合同类型管理")
@RestController
@RequestMapping("sale/agentContractType")
public class AgentContractTypeController {
    @Resource
    private IAgentContractTypeService contractTypeService;

    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理合同类型管理/查询")
    @PostMapping("datas")
    public ListResponseBo<AgentContractTypeVo> datas(@RequestBody SearchBean<AgentContractTypeDto> page) {
        List<AgentContractTypeVo> contractTypeDtos = contractTypeService.getAgentContractTypeDtos(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(contractTypeDtos, p);
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [agentEventVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理事件类型管理/新增事件")
    @PostMapping("add")
    public ResponseBo add(@RequestBody  @Validated(AgentContractTypeDto.Add.class)  ValidList<AgentContractTypeDto> contractTypeVos) {
        contractTypeService.addAgentContractType(contractTypeVos);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AgentEventTypeVo>
     * @Description: 修改信息
     * @Param [contractTypeVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/代理事件类型管理/更新事件")
    @PostMapping("update")
    public ResponseBo<AgentContractTypeVo> update(@RequestBody @Validated(AgentContractTypeDto.Update.class) AgentContractTypeDto contractTypeVo) {
        return UpdateResponseBo.ok(contractTypeService.updateAgentContractType(contractTypeVo));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/代理事件类型管理/删除代理")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        contractTypeService.deleteAgentContractType(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 上移下移
     * @Param [agentEventTypeVoList]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/代理事件类型管理/排序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<AgentContractTypeDto> agentContractTypeDtos) {
        contractTypeService.movingOrder(agentContractTypeDtos);
        return ResponseBo.ok();
    }


}
