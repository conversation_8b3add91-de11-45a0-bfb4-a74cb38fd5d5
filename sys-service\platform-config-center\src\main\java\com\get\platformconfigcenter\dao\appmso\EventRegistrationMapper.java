package com.get.platformconfigcenter.dao.appmso;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.baomidou.mybatisplus.core.metadata.IPage;

import com.get.platformconfigcenter.entity.MsoEventRegistration;
import com.get.platformconfigcenter.dto.EventRegistrationDto;
import com.get.platformconfigcenter.dto.IsAttendDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName: EventRegistrationMapper
 * @Author: Eric
 * @Date: 2023/5/23 18:27
 * @Version: 1.0
 */
@Mapper
public interface EventRegistrationMapper extends BaseMapper<MsoEventRegistration> {
    void updateisAttend(String registrationCode);

    List<MsoEventRegistration> getEventRegistrationList(@Param("data") EventRegistrationDto data, IPage<MsoEventRegistration> iPage);

    void batchUpdateIsAttend(IsAttendDto isAttendDto);
}
