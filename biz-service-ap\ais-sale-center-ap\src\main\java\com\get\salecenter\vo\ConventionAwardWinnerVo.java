package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.ConventionAwardWinner;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2021/9/9
 * @TIME: 12:09
 * @Description:
 **/
@Data
@ApiModel(value = "中奖名单返回类")
public class ConventionAwardWinnerVo extends BaseEntity {

    /**
     * 奖品图片
     */
    @ApiModelProperty(value = "奖品图片")
    private List<MediaAndAttachedVo> mediaAndAttachedDtoList;

    /**
     * 奖品名称
     */
    @ApiModelProperty(value = "奖品名称")
    private String fkConventionAwardName;

    /**
     * 中奖码
     */
    @ApiModelProperty(value = "中奖码")
    private String fkConventionAwardCode;

    /**
     * 中奖者
     */
    @ApiModelProperty(value = "中奖者")
    private String fkConventionPersonName;

    //================实体类ConventionAwardWinner==============
    private static final long serialVersionUID = 1L;
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    @Column(name = "fk_convention_id")
    private Long fkConventionId;
    /**
     * 峰会奖品Id
     */
    @ApiModelProperty(value = "峰会奖品Id")
    @Column(name = "fk_convention_award_id")
    private Long fkConventionAwardId;
    /**
     * 峰会参展人员Id（中奖人）
     */
    @ApiModelProperty(value = "峰会参展人员Id（中奖人）")
    @Column(name = "fk_convention_person_id")
    private Long fkConventionPersonId;
    /**
     * 峰会抽奖号码Id
     */
    @ApiModelProperty(value = "峰会抽奖号码Id")
    @Column(name = "fk_convention_award_code_id")
    private Long fkConventionAwardCodeId;
}
