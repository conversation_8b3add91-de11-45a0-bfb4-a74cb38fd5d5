package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.vo.StudentOfferItemFailureVo;
import com.get.salecenter.entity.StudentOfferItemFailure;
import com.get.salecenter.dto.StudentOfferItemFailureDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StudentOfferItemFailureMapper extends BaseMapper<StudentOfferItemFailure> {
    int insert(StudentOfferItemFailure record);

    int insertSelective(StudentOfferItemFailure record);

    int updateById(StudentOfferItemFailure record);

    int updateByPrimaryKeyWithBLOBs(StudentOfferItemFailure record);

    int updateByPrimaryKey(StudentOfferItemFailure record);

    /**
     * 学习计划生成失败列表
     *
     * @Date 9:58 2021/7/9
     * <AUTHOR>
     */
    List<StudentOfferItemFailureVo> getStudentOfferItemFailureList(IPage<StudentOfferItemFailureVo> iPage, @Param("studentOfferItemFailureDto") StudentOfferItemFailureDto studentOfferItemFailureDto);

    /**
     * 公司代理下拉框
     *
     * @Date 11:17 2021/7/14
     * <AUTHOR>
     */
    List<BaseSelectEntity> agentSelect(@Param("companyId") Long companyId);
}