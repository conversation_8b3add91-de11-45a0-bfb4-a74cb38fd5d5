package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.ContractFormulaInstitutionChannel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ContractFormulaInstitutionChannelMapper extends BaseMapper<ContractFormulaInstitutionChannel> {
    int insert(ContractFormulaInstitutionChannel record);

    int insertSelective(ContractFormulaInstitutionChannel record);

    int updateByPrimaryKeySelective(ContractFormulaInstitutionChannel record);

    int updateByPrimaryKey(ContractFormulaInstitutionChannel record);

    /**
     * 获取渠道名称
     *
     * @param id
     * @return
     */
    String getInstitutionChannelNamesById(@Param("id") Long id);
}