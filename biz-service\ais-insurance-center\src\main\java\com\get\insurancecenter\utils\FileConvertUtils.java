package com.get.insurancecenter.utils;

import org.springframework.web.multipart.MultipartFile;

import java.io.*;


/**
 * 文件转换工具类
 */
public class FileConvertUtils {

    /**
     * 将 byte[] 转换为 MultipartFile（用于上传）
     *
     * @param content      文件字节内容
     * @param paramName    form表单字段名，如 "file"
     * @param originalName 原始文件名，如 "report.pdf"
     * @param contentType  MIME类型，如 "application/pdf"
     * @return MultipartFile
     */
    public static MultipartFile toMultipartFile(byte[] content, String paramName, String originalName, String contentType) {
        return new ByteArrayMultipartFile(content, paramName, originalName, contentType);
    }

    // 内部类：生产环境可用 MultipartFile 实现
    private static class ByteArrayMultipartFile implements MultipartFile {
        private final byte[] content;
        private final String name;
        private final String originalFilename;
        private final String contentType;

        public ByteArrayMultipartFile(byte[] content, String name, String originalFilename, String contentType) {
            this.content = content;
            this.name = name;
            this.originalFilename = originalFilename;
            this.contentType = contentType;
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public String getOriginalFilename() {
            return originalFilename;
        }

        @Override
        public String getContentType() {
            return contentType;
        }

        @Override
        public boolean isEmpty() {
            return content == null || content.length == 0;
        }

        @Override
        public long getSize() {
            return content.length;
        }

        @Override
        public byte[] getBytes() {
            return content;
        }

        @Override
        public InputStream getInputStream() {
            return new ByteArrayInputStream(content);
        }

        @Override
        public void transferTo(File dest) throws IOException {
            try (FileOutputStream fos = new FileOutputStream(dest)) {
                fos.write(content);
            }
        }
    }
}
