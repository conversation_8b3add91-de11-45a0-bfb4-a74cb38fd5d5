package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2024/1/31 12:06
 * @verison: 1.0
 * @description:
 */
@Data
public class BatchUpdatePayablePlanDto {

    @NotEmpty(message = "应付计划ids")
    @ApiModelProperty(value = "应付计划ids",required = true)
    private List<Long> ids;

    @ApiModelProperty(value = "费率%(代理)")
    private BigDecimal commissionRate;

    @ApiModelProperty(value = "代理分成比率%")
    private BigDecimal splitRate;

    @ApiModelProperty(value = "定额金额")
    private BigDecimal fixedAmount;

    @ApiModelProperty(value = "摘要")
    private String summary;

}
