package com.get.platformconfigcenter.dao.appissue;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.Translation;
import org.apache.ibatis.annotations.Mapper;


/**
 * @author: <PERSON>
 * @create: 2021/6/18 18:13
 * @verison: 1.0
 * @description:
 */
@Mapper
@DS("issuedb")
public interface IssueTranslationMapper extends BaseMapper<Translation> {
//    int insert(Translation record);
//
//    int insertSelective(Translation record);
//
//    int updateByPrimaryKeySelective(Translation record);
//
//    int updateByPrimaryKeyWithBLOBs(Translation record);
//
//    int updateByPrimaryKey(Translation record);
//
//    String getTranslation(TranslationDto translationVo);
//
//    void deleteTranslations(@Param("tableName") String tableName, @Param("id") Long id);
}
