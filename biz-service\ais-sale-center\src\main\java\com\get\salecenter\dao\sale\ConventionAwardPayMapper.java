package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.salecenter.vo.ConventionAwardPayVo;
import com.get.salecenter.entity.ConventionAwardPay;
import com.get.salecenter.dto.ConventionAwardPayDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ConventionAwardPayMapper extends BaseMapper<ConventionAwardPay> {
    int insert(ConventionAwardPay record);

    int insertSelective(ConventionAwardPay record);

    List<ConventionAwardPayVo> datas(IPage<ConventionAwardPayVo> iPage, ConventionAwardPayDto conventionAwardPayDto);
}