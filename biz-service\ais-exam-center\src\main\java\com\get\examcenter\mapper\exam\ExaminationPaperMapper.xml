<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.examcenter.mapper.exam.ExaminationPaperMapper">
  <insert id="insert" parameterType="com.get.examcenter.entity.ExaminationPaper"  keyProperty="id" useGeneratedKeys="true">
    insert into m_examination_paper (id, fk_examination_id, num, 
      name, start_time, end_time, 
      question_count, admin_authentication, is_retest, is_active,
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkExaminationId,jdbcType=BIGINT}, #{num,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, 
      #{questionCount,jdbcType=INTEGER}, #{adminAuthentication,jdbcType=VARCHAR},#{isRetest,jdbcType=BIT}, #{isActive,jdbcType=BIT},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.examcenter.entity.ExaminationPaper"  keyProperty="id" useGeneratedKeys="true">
    insert into m_examination_paper
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkExaminationId != null">
        fk_examination_id,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="questionCount != null">
        question_count,
      </if>
      <if test="adminAuthentication != null">
        admin_authentication,
      </if>
      <if test="isRetest != null">
        is_retest,
      </if>
      <if test="isActive != null">
        is_active,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
        <if test="description != null">
          description,
        </if>
        <if test="paramJson != null">
          param_json,
        </if>
        <if test="fkCompanyId != null">
            fk_company_id,
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkExaminationId != null">
        #{fkExaminationId,jdbcType=BIGINT},
      </if>
      <if test="num != null">
        #{num,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="questionCount != null">
        #{questionCount,jdbcType=INTEGER},
      </if>
      <if test="adminAuthentication != null">
        #{adminAuthentication,jdbcType=VARCHAR},
      </if>
      <if test="isRetest != null">
        #{isRetest,jdbcType=BIT},
      </if>
      <if test="isActive != null">
        #{isActive,jdbcType=BIT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="paramJson != null">
        #{paramJson,jdbcType=VARCHAR},
      </if>
        <if test="fkCompanyId != null">
            #{fkCompanyId,jdbcType=BIGINT},
        </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.get.examcenter.entity.ExaminationPaper">
    update m_examination_paper
    <set>
      <if test="fkExaminationId != null">
        fk_examination_id = #{fkExaminationId,jdbcType=BIGINT},
      </if>
      <if test="num != null">
        num = #{num,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="questionCount != null">
        question_count = #{questionCount,jdbcType=INTEGER},
      </if>
      <if test="adminAuthentication != null">
        admin_authentication = #{adminAuthentication,jdbcType=VARCHAR},
      </if>
      <if test="isRetest != null">
        is_retest = #{isRetest,jdbcType=BIT},
      </if>
      <if test="isActive != null">
        is_active = #{isActive,jdbcType=BIT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.examcenter.entity.ExaminationPaper">
    update m_examination_paper
    set fk_examination_id = #{fkExaminationId,jdbcType=BIGINT},
      num = #{num,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      param_json = #{paramJson,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      question_count = #{questionCount,jdbcType=INTEGER},
      admin_authentication = #{adminAuthentication,jdbcType=VARCHAR},
      is_retest = #{isRetest,jdbcType=BIT},
      is_active = #{isActive,jdbcType=BIT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      fk_company_id = #{fkCompanyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getExaminationPaperList" resultType="com.get.examcenter.vo.ExaminationPaperVo">
    SELECT
      mep.id AS id,
      mep.fk_examination_id AS fkExaminationId,
      mep.num AS num,
      mep.name AS name,
      mep.start_time AS startTime,
      mep.end_time AS endTime,
      mep.question_count AS questionCount,
      mep.admin_authentication AS adminAuthentication,
      mep.is_retest AS isRetest,
      mep.is_active AS isActive,
      mep.gmt_create AS gmtCreate,
      mep.gmt_create_user AS gmtCreateUser,
      mep.gmt_modified AS gmtModified,
      mep.gmt_modified_user AS gmtModifiedUser,
      me.name AS fkExaminationName,mep.fk_company_id fkCompanyId
    FROM
      m_examination_paper mep left join m_examination me on me.id = mep.fk_examination_id
    where me.fk_company_id in
    <foreach collection="fkCompanyIds" item="companyId" index="index" open="(" separator="," close=")">
      #{companyId,jdbcType=BIGINT}
    </foreach>
    <if test="examinationPaperListDto.fkExaminationId != null and examinationPaperListDto.fkExaminationId != ''">
      and me.id = #{examinationPaperListDto.fkExaminationId,jdbcType=BIGINT}
    </if>
    <if test="examinationPaperListDto.fkExaminationName != null and examinationPaperListDto.fkExaminationName != ''">
      and me.name like CONCAT('%', #{examinationPaperListDto.fkExaminationName,jdbcType=VARCHAR}, '%')
    </if>
    <if test="examinationPaperListDto.num != null and examinationPaperListDto.num != ''">
      and mep.num like CONCAT('%', #{examinationPaperListDto.num,jdbcType=VARCHAR}, '%')
    </if>
    <if test="examinationPaperListDto.name != null and examinationPaperListDto.name != ''">
      and mep.name like CONCAT('%', #{examinationPaperListDto.name,jdbcType=VARCHAR}, '%')
    </if>
    <if test="examinationPaperListDto.startTime != null">
      and DATE_FORMAT(mep.start_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{examinationPaperListDto.startTime},'%Y-%m-%d')
    </if>
    <if test="examinationPaperListDto.endTime != null">
      and DATE_FORMAT(mep.end_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{examinationPaperListDto.endTime},'%Y-%m-%d')
    </if>
    <if test="examinationPaperListDto.isActive != null">
      and mep.is_active = #{examinationPaperListDto.isActive,jdbcType=BIT}
    </if>
    <if test="examinationPaperListDto.fkCompanyId != null">
      and mep.fk_company_id = #{examinationPaperListDto.fkCompanyId,jdbcType=BIGINT}
    </if>
    ORDER BY mep.fk_examination_id DESC, CAST(SUBSTRING(mep.name FROM locate('【',mep.name)+1 FOR (locate('】',mep.name) - locate('【',mep.name)-1)) AS SIGNED), mep.num  </select>
  <select id="getExaminationByexaminationPaperNum" resultType="com.get.examcenter.vo.ExaminationActiveAndTimeVo">
    select
    mep.is_active AS examinationPaperActive,me.is_active AS examinationActive,
    mep.start_time AS examinationPaperStartTime,mep.end_time AS examinationPaperEndTime,
    me.start_time AS examinationStartTime,me.end_time AS examinationEndTime,
    mep.is_retest AS isRetest,mep.id AS fkExaminationPaperId,mep.question_count AS questionCount,
    me.id AS fkExaminationId
    from m_examination_paper mep
    join m_examination me on mep.fk_examination_id = me.id
    where mep.num = #{fkExaminationPaperNum}
  </select>
  <select id="getExaminationPaper" resultType="com.get.examcenter.entity.ExaminationPaper">
      <!-- select * from m_examination_paper where fk_company_id=#{fkCompanyId} and
         CAST(SUBSTRING(`name` FROM locate('【',name)+1 FOR (locate('】',name) - locate('【',name)-1)) AS SIGNED)
         union all
       select * from m_examination_paper where fk_company_id=#{fkCompanyId} and
         CAST(SUBSTRING(`name` FROM locate('（',name)+1 FOR (locate('）',name) - locate('（',name)-1)) AS SIGNED)-->
         select * from m_examination_paper where fk_company_id=#{fkCompanyId}
         order by id desc
     </select>
   </mapper>