package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.vo.InstitutionFacultyVo;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.service.IInstitutionFacultyService;
import com.get.institutioncenter.service.IMediaAndAttachedService;
import com.get.institutioncenter.dto.InstitutionFacultyDto;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/8/4
 * @TIME: 16:37
 * @Description:
 **/
@Api(tags = "学院管理")
@RestController
@RequestMapping("/institution/institutionFaculty")
public class InstitutionFacultyController {
    @Resource
    private IInstitutionFacultyService institutionFacultyService;
    @Resource
    private IMediaAndAttachedService mediaAndAttachedService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/学校学院管理/学校学院详情")
    @GetMapping("/{id}")
    public ResponseBo<InstitutionFacultyVo> detail(@PathVariable("id") Long id) {
        InstitutionFacultyVo data = institutionFacultyService.findInstitutionFacultyById(id);
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", data);
        return responseBo;
    }

    /**
     * 新增信息
     *
     * @param institutionFacultyDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/学校学院管理/新增学校学院")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(InstitutionFacultyDto.Add.class) InstitutionFacultyDto institutionFacultyDto) {
        return SaveResponseBo.ok(institutionFacultyService.addInstitutionFaculty(institutionFacultyDto));
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/学校学院管理/删除学校学院")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        institutionFacultyService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param institutionFacultyDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/学校学院管理/更新学校学院")
    @PostMapping("update")
    public ResponseBo<InstitutionFacultyVo> update(@RequestBody @Validated(InstitutionFacultyDto.Update.class) InstitutionFacultyDto institutionFacultyDto) {
        return UpdateResponseBo.ok(institutionFacultyService.updateInstitutionFaculty(institutionFacultyDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校学院管理/查询学校学院")
    @PostMapping("datas")
    public ResponseBo<InstitutionFacultyVo> datas(@RequestBody SearchBean<InstitutionFacultyDto> page) {
        List<InstitutionFacultyVo> datas = institutionFacultyService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 上移下移
     *
     * @param mediaAttachedVos
     * @return
     * @
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/学校学院管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<MediaAndAttachedDto> mediaAttachedVos) {
        mediaAndAttachedService.movingOrder(mediaAttachedVos);
        return ResponseBo.ok();
    }

    /**
     *
     * @param id
     * @return
     * @
     */
/*
    @ApiOperation(value = "删除文件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/学校学院管理/删除文件")
    @PostMapping("deleteFile/{id}")
    public ResponseBo deleteFile(@PathVariable("id") Long id)  {
        mediaAndAttachedService.deleteMediaAttached(id);
        return ResponseBo.ok();
    }*/


    /**
     *
     * @param files
     * @return
     * @
     */

/*    @ApiOperation(value = "上传文件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/学校学院管理/上传文件")
    @PostMapping("upload")
    public ResponseBo upload(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files)  {
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", institutionFacultyService.upload(files));
        return responseBo;
    }*/

    /**
     * @param mediaAttachedVos
     * @return
     * @
     */

    @ApiOperation(value = "保存文件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/学校学院管理/保存附件资料")
    @PostMapping("addMediaAndAttached")
    public ResponseBo addMediaAndAttached(@RequestBody @Validated(MediaAndAttachedDto.Add.class) List<MediaAndAttachedDto> mediaAttachedVos) {
        return new ListResponseBo<>(institutionFacultyService.addInstitutionFacultyMedia(mediaAttachedVos));
    }

    /**
     * 学院下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学院下拉框数据", notes = "")
    @GetMapping("getInstitutionFacultyList/{id}")
    public ResponseBo<InstitutionFacultyVo> getInstitutionFacultyList(@PathVariable("id") Long id) {
        List<InstitutionFacultyVo> datas = institutionFacultyService.getByfkInstitutionId(id);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.InstitutionFacultyVo>
     * @Description :多个学校所有的学院下拉框数据
     * @Param [institutionIdList]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "多个学校所有的学院下拉框数据", notes = "")
    @PostMapping("getInstitutionFacultySelectByInstitutionIdList")
    public ResponseBo<InstitutionFacultyVo> getInstitutionFacultySelectByInstitutionIdList(@RequestBody List<Long> institutionIdList) {
        List<InstitutionFacultyVo> datas = institutionFacultyService.getInstitutionFacultySelectByInstitutionIdList(institutionIdList);
        return new ListResponseBo<>(datas);
    }

    /**
     * 学院附件类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学院附件类型下拉框数据", notes = "")
    @GetMapping("findMediaAndAttachedType")
    public ResponseBo findMediaAndAttachedType() {
        List<Map<String, Object>> datas = institutionFacultyService.findMediaAndAttachedType();
        return new ListResponseBo<>(datas);
    }

    /**
     * 查询学院附件
     *
     * @param voSearchBean
     * @return
     * @
     */
    @ApiOperation(value = "查询学院附件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校管理/查询学院附件")
    @PostMapping("getInstitutionFacultyMedia")
    public ResponseBo<MediaAndAttachedVo> getInstitutionFacultyMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> institutionMedia = institutionFacultyService.getInstitutionFacultyMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(institutionMedia, page);
    }
}
