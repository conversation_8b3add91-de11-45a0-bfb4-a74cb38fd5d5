package com.get.platformconfigcenter.service.impl;

import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.SearchBean;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.platformconfigcenter.dao.hubs.SaleOrderItemMapper;
import com.get.platformconfigcenter.dao.hubs.SaleOrderMapper;
import com.get.platformconfigcenter.dto.AppHubsSearchOrderDto;
import com.get.platformconfigcenter.dto.AppHubsUpdateOrderDto;
import com.get.platformconfigcenter.entity.CountryButler;
import com.get.platformconfigcenter.entity.SaleOrder;
import com.get.platformconfigcenter.entity.SaleOrderItem;
import com.get.platformconfigcenter.service.AppHubsService;
import com.get.platformconfigcenter.vo.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: Eric
 * @Date: 2024/9/23
 * @Description:
 * @Version 1.0
 */
@Service
public class AppHubsServiceImpl extends ServiceImpl<SaleOrderMapper, SaleOrder> implements AppHubsService {

    @Resource
    private SaleOrderItemMapper saleOrderItemMapper;

    @Resource
    private UtilService utilService;

    @Override
    public List<SubmitOrderVo> getAppHubsOrders(AppHubsSearchOrderDto data, SearchBean<AppHubsSearchOrderDto> searchBean) {

        IPage<SubmitOrderVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(searchBean.getCurrentPage(), searchBean.getShowCount()));

       List<SubmitOrderVo> list = this.baseMapper.getAppHubsOrders(iPage,data);
        searchBean.setAll((int) iPage.getTotal());

        if(GeneralTool.isNotEmpty(list)){
            for(SubmitOrderVo submitOrderVo :list){
                submitOrderVo.setPaymentStatusName(ProjectExtraEnum.getValueByKey(submitOrderVo.getPaymentStatus(), ProjectExtraEnum.APP_HUBS_PAY_STATUS));
            }
        }


        return list;
    }

    @Override
    public Map<String, Object> getAppHubsOrdersDetail(Long fkOrderId) {


        Map<String,Object> map = new HashMap<>();
        //学生信息
        SaleOrderUserInfoVo saleOrderUserInfo = this.baseMapper.getSaleOrderUserInfo(fkOrderId);
        if(GeneralTool.isNotEmpty(saleOrderUserInfo)){

            saleOrderUserInfo.setPaymentStatusName(ProjectExtraEnum.getValueByKey(saleOrderUserInfo.getPaymentStatus(), ProjectExtraEnum.APP_HUBS_PAY_STATUS));

        }

        map.put("saleOrderUserInfo",saleOrderUserInfo);

        //商品列表信息
       List<SaleOrderItemVo> saleOrderItemVoList = this.baseMapper.getAppHubsOrdersItemDetailByOrderId(fkOrderId);

       for (SaleOrderItemVo saleOrderItemVo : saleOrderItemVoList){
           Gson gson = new Gson();
           if(GeneralTool.isNotEmpty(saleOrderItemVo.getProductAttributesJson())){
               String json = saleOrderItemVo.getProductAttributesJson();
               ProductDetailVo productDetailVo = gson.fromJson(json, ProductDetailVo.class);
               saleOrderItemVo.setProductDetailDto(productDetailVo);
               saleOrderItemVo.setProductAttributesJson(null);
           }
       }

        map.put("saleOrderItemDtoList", saleOrderItemVoList);

        return map;
    }

    @Override
    public void updateSalqOrder(List<AppHubsUpdateOrderDto> data) {

        if(GeneralTool.isEmpty(data)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        //判断订单是否支付，如果已支付，则不能修改
       SaleOrderItem saleOrderItemVO = saleOrderItemMapper.selectById(data.get(0).getFkOrderItemId());
       Long fkSaleOrderId = saleOrderItemVO.getFkSaleOrderId();
      SaleOrder saleOrder =  this.baseMapper.selectById(fkSaleOrderId);
      if(saleOrder.getPaymentStatus() != 0){
          throw new GetServiceException(LocaleMessageUtils.getMessage("cannot_modify_order"));
      }

        for (AppHubsUpdateOrderDto appHubsUpdateOrderDto :data){
                SaleOrderItem saleOrderItem = new SaleOrderItem();
            saleOrderItem.setId(appHubsUpdateOrderDto.getFkOrderItemId());
            //原价不变，修改交易价格
            saleOrderItem.setDiscountPrice(appHubsUpdateOrderDto.getDiscountPrice());
              utilService.setUpdateInfo(saleOrderItem);
                saleOrderItemMapper.updateById(saleOrderItem);
        }
        //查询当前订单所有的价格
      List<SaleOrderItem> saleOrderItems = saleOrderItemMapper.selectList(Wrappers.<SaleOrderItem>lambdaQuery().eq(SaleOrderItem::getFkSaleOrderId,fkSaleOrderId));
        BigDecimal totalAmount = new BigDecimal(0);
        for(SaleOrderItem dto: saleOrderItems){
            BigDecimal itemTotal = dto.getDiscountPrice().multiply(BigDecimal.valueOf(dto.getQuantity()));
            totalAmount = totalAmount.add(itemTotal);
       }

        //总价格需要重新乘以2
        BigDecimal totalAmountEnd =  multiplyAndRound(totalAmount, 2);
        saleOrder.setTotalAmount(totalAmountEnd);
        utilService.setUpdateInfo(saleOrder);
        this.baseMapper.updateById(saleOrder);


    }

    @Override
    public List<CountryButler> getAppHubsCountry() {

        List<CountryButler> countryButlers = this.baseMapper.getAppHubsCountry();

        return countryButlers;
    }

    @Override
    public List<HubsUserVo> getRegisteredUser(AppHubsSearchOrderDto data, SearchBean<AppHubsSearchOrderDto> searchBean) {
        IPage<HubsUserVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(searchBean.getCurrentPage(), searchBean.getShowCount()));
        List<HubsUserVo> hubsUserVoList = this.baseMapper.getRegisteredUser(iPage, data);
        searchBean.setAll((int) iPage.getTotal());
        return hubsUserVoList;
    }


    /**
     * 将一个 BigDecimal 数乘以另一个 BigDecimal 数，并保留指定的小数位数。乘数 固定是1.02
     *
     * @param amount       被乘数
     * @param scale        保留的小数位数
     * @return             结果
     */
    public static BigDecimal multiplyAndRound(BigDecimal amount,  int scale) {
        BigDecimal  multiplier =  new BigDecimal("1.02");
        // 乘法运算
        BigDecimal multipliedAmount = amount.multiply(multiplier);

        // 设置保留小数位数并四舍五入
        return multipliedAmount.setScale(scale, RoundingMode.HALF_UP);
    }

}
