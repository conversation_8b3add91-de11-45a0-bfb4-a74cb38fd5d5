package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.salecenter.vo.ConventionAwardCodeVo;
import com.get.salecenter.vo.TicketsNoUsedVo;
import com.get.salecenter.entity.ConventionAwardCode;
import com.get.salecenter.dto.ConventionAwardCodeDto;
import com.get.salecenter.dto.LuckDrawDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface ConventionAwardCodeMapper extends BaseMapper<ConventionAwardCode> {
    int insert(ConventionAwardCode record);

    int insertSelective(ConventionAwardCode record);

    void deleteByStatus(@Param("id") Long id);

    /**
     * @return java.lang.List
     * @Description :获取抽奖号码列表
     * @Param conventionAwardCodeDto
     * <AUTHOR>
     */
    List<ConventionAwardCodeVo> getConventionAwardCodes(IPage<ConventionAwardCodeVo> iPage, ConventionAwardCodeDto conventionAwardCodeDto);

    /**
     * @return java.lang.List
     * @Description :获取未使用抽奖号码
     * <AUTHOR>
     */
    List<ConventionAwardCodeVo> getTicketsByGroleAndAwardIdNotUsed(@Param("agrole") String agrole, @Param("award_id") int award_id, @Param("status") String status, @Param("fkConventionId") Long fkConventionId);

    /**
     * @return java.lang.List
     * @Description :获取已使用抽奖号码
     * <AUTHOR>
     */
    List<ConventionAwardCodeVo> getTicketsByGroleAndAwardIdUsed(@Param("agrole") String agrole, @Param("award_id") Long award_id, @Param("status") String status, @Param("fkConventionId") Long fkConventionId);

    /**
     * @Description: 查询还没有用的奖券
     * @Author: jack
     * @Date:16:51 2021/9/23
     */
    List<TicketsNoUsedVo> getListTicketsNoUsedByAwardId(@Param("awardId") Long awardId, @Param("roles") String roles, @Param("personId") String personId);

    ConventionAwardCode getById(@Param("id") Long id);


    List<ConventionAwardCode> getBySystemCode(@Param("num") String num);

    List<TicketsNoUsedVo> getCodeByAwardAndType(@Param("id") Long id);

    /**
     * 普通抽奖
     *
     * @param fkConventionId
     * @param awardCode
     * @param getRole
     * @return
     */
    List<ConventionAwardCode> getLuckyNumber(@Param("fkConventionId") Long fkConventionId,
                                             @Param("awardCodeIds") Set<Long> awardCodeIds,
                                             @Param("getRole") String getRole);

    /**
     * 作弊抽奖
     *
     * @Date 18:24 2023/2/3
     * <AUTHOR>
     */
    List<ConventionAwardCode> getCheatingNumber(@Param("fkConventionId") Long fkConventionId,
                                                @Param("getFkConventionPersonId") String getFkConventionPersonId,
                                                @Param("luckDrawDto") LuckDrawDto luckDrawDto);
}