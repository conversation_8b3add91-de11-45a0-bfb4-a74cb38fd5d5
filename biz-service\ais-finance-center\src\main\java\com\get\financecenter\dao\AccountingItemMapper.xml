<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.get.financecenter.dao.AccountingItemMapper">

    <select id="getAccountingItemAll" resultType="com.get.financecenter.vo.AccountingItemSelectVo">
        SELECT
            mai.id,
            mai.fk_parent_accounting_item_id,
            mai.type,
            mai.grade,
            mai.code,
            mai.code_name,
            mai.direction,
            mai.is_active,
            mai.gmt_create,
            mai.gmt_create_user,
            mai.gmt_modified,
            mai.gmt_modified_user
        FROM
            m_accounting_item mai
        WHERE 1=1
        <if test="accountingItemDto.id != null and accountingItemDto.id !=''">
            AND mai.id = #{accountingItemDto.id}
        </if>
        <if test="accountingItemDto.fkParentAccountingItemId != null and accountingItemDto.fkParentAccountingItemId !=''">
            AND mai.fk_parent_accounting_item_id = #{accountingItemDto.fkParentAccountingItemId}
        </if>
        <if test="accountingItemDto.type != null and accountingItemDto.type !=''">
            AND mai.type = #{accountingItemDto.type}
        </if>
        <if test="accountingItemDto.grade != null and accountingItemDto.grade !=''">
            AND mai.grade = #{accountingItemDto.grade}
        </if>
        <if test="accountingItemDto.code != null and accountingItemDto.code !=''">
            AND mai.code = #{accountingItemDto.code}
        </if>
        <if test="accountingItemDto.codeName != null and accountingItemDto.codeName !=''">
            AND mai.code_name = #{accountingItemDto.codeName}
        </if>
        <if test="accountingItemDto.direction != null">
            AND mai.direction = #{accountingItemDto.direction}
        </if>
        <if test="accountingItemDto.isActive != null">
            AND mai.is_active = #{accountingItemDto.isActive}
        </if>

        <if test="accountingItemDto.keyWord !=null and accountingItemDto.keyWord != ''">
            AND (
            mai.code LIKE CONCAT('%',#{accountingItemDto.keyWord},'%')
            OR mai.code_name LIKE CONCAT('%',#{accountingItemDto.keyWord},'%')
            )
        </if>
        ORDER BY mai.gmt_create DESC

    </select>

    <select id="getAccountingItemByGrade" resultType="com.get.financecenter.vo.AccountingItemDropdownMenuVo">
        SELECT
            mai.id,
            CONCAT(mai.code, '-', mai.code_name) AS name
        FROM
            m_accounting_item mai
        WHERE mai.is_active = 1
            AND mai.grade = #{grade}
    </select>

    <select id="getAccountingItemDropdownMenu" resultType="com.get.financecenter.entity.AccountingItem">
        SELECT id, fk_parent_accounting_item_id,code,code_name,grade FROM m_accounting_item WHERE is_active = 1;
    </select>

    <select id="checkNameOrCode" resultType="java.lang.Integer">
        SELECT count(*) FROM m_accounting_item
        <where>
            <if test="code != null and code != ''">
                AND code = #{code}
            </if>
            <if test="codeName != null and codeName != ''">
                OR code_name = #{codeName}
            </if>
        </where>
    </select>

    <select id="getFkBalanceSheetTypeIdByAccountingItemId" resultType="java.lang.Long">
        SELECT mai.type FROM  m_accounting_item mai WHERE mai.id = #{accountingItemId}
    </select>
</mapper>
