package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.dto.ContractFormulaCompanyDto;
import com.get.institutioncenter.dto.ContractFormulaDto;
import com.get.institutioncenter.vo.CompanyTreeVo;
import com.get.institutioncenter.vo.ContractFormulaVo;
import com.get.institutioncenter.service.IContractFormulaService;
import com.get.salecenter.entity.StudentOfferItem;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @author: Sea
 * @create: 2021/1/8 10:13
 * @verison: 1.0
 * @description:
 */
@Api(tags = "合同公式管理")
@RestController
@RequestMapping("institution/contractFormula")
public class ContractFormulaController {
    @Resource
    private IContractFormulaService contractFormulaService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.ContractFormulaVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/合同公式管理/合同公式详情")
    @GetMapping("/{id}")
    public ResponseBo<ContractFormulaVo> detail(@PathVariable("id") Long id) {
        ContractFormulaVo data = contractFormulaService.findContractFormulaById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增信息
     * @Param [contractFormulaVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/合同公式管理/新增合同公式")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(ContractFormulaDto.Add.class) ContractFormulaDto contractFormulaDto) {
        return SaveResponseBo.ok(contractFormulaService.addContractFormula(contractFormulaDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/合同公式管理/删除合同公式")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        contractFormulaService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.ContractFormulaVo>
     * @Description :修改信息
     * @Param [contractFormulaDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/合同公式管理/更新合同公式")
    @PostMapping("update")
    public ResponseBo<ContractFormulaVo> update(@RequestBody @Validated(ContractFormulaDto.Update.class) ContractFormulaDto contractFormulaDto) {
        return UpdateResponseBo.ok(contractFormulaService.updateContractFormula(contractFormulaDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.ContractFormulaVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "fkAgentId 代理id(必传)")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/合同公式管理/查询合同公式")
    @PostMapping("datas")
    public ResponseBo<ContractFormulaVo> datas(@RequestBody SearchBean<ContractFormulaDto> page) {
        List<ContractFormulaVo> datas = contractFormulaService.getContractFormulas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :上移下移
     * @Param [contractFormulaDtos]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/合同公式管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<ContractFormulaDto> contractFormulaDtos) {
        contractFormulaService.movingOrder(contractFormulaDtos);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :合同公式-安全配置
     * @Param [contractFormulaCompanyDtos]
     * <AUTHOR>
     */
    @ApiOperation(value = "合同公式-安全配置")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/合同公式管理/安全配置")
    @PostMapping("editContractFormulaCompany")
    public ResponseBo editContractFormulaCompany(@RequestBody  @Validated(ContractFormulaCompanyDto.Add.class)  ValidList<ContractFormulaCompanyDto> contractFormulaCompanyDtos) {
        contractFormulaService.editContractFormulaCompany(contractFormulaCompanyDtos);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.CompanyTreeVo>
     * @Description :合同公式-安全配置详情
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    @ApiOperation(value = "合同公式-安全配置详情")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/合同公式管理/安全配置详情")
    @GetMapping("getContractFormulaCompany/{contractFormulaId}")
    public ResponseBo<CompanyTreeVo> getContractFormulaCompany(@PathVariable("contractFormulaId") Long contractFormulaId) {
        return new ListResponseBo<>(contractFormulaService.getContractFormulaCompany(contractFormulaId));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 公式类型下拉
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "公式类型下拉", notes = "")
    @GetMapping("findFormulaType")
    public ResponseBo findFormulaType() {
        List<Map<String, Object>> datas = contractFormulaService.findFormulaType();
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 条件类型下拉
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "条件类型下拉", notes = "")
    @GetMapping("findConditionType")
    public ResponseBo findConditionType() {
        List<Map<String, Object>> datas = contractFormulaService.findConditionType();
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 统计类型下拉
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "统计类型下拉", notes = "")
    @GetMapping("findCountType")
    public ResponseBo findCountType() {
        List<Map<String, Object>> datas = contractFormulaService.findCountType();
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description :根据学校提供商获取合同公式下拉框数据
     * @Param [institutionProviderId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据学校提供商获取合同公式下拉框数据", notes = "")
    @PostMapping("getContractFormulaList")
    public ResponseBo<BaseSelectEntity> getContractFormulaList(@RequestParam(value = "institutionProviderId") Long institutionProviderId) {
        List<BaseSelectEntity> datas = contractFormulaService.getContractFormulaList(institutionProviderId);
        return new ListResponseBo<>(datas);
    }

    /**
     * @Description :feign调用 通过合同公式ids 查找对应的合同公式map
     * @Param [ids]
     * <AUTHOR>
     */
/*    @ApiIgnore
    @PostMapping("getContractFormulasByIds")
    public Map<Long, String> getContractFormulasByIds(@RequestBody Set<Long> ids)  {
        return contractFormulaService.getContractFormulasByIds(ids);
    }*/

    /**
     * @Description: 查询符合学习计划的公式
     * @Param [companyId, studentOfferItem]
     * @return java.util.List<com.get.institutioncenter.entity.ContractFormula>
     * <AUTHOR>
     **/
/*    @ApiIgnore
    @PostMapping("getContractFormulasByOfferItem")
    public ResponseBo getContractFormulasByOfferItem(@RequestParam(value = "companyId") Long companyId, @RequestParam(value = "areaCountryId") Long areaCountryId, @RequestBody StudentOfferItem studentOfferItem)  {
        return new ListResponseBo<>(contractFormulaService.getContractFormulasByOfferItem(companyId,areaCountryId,studentOfferItem));
    }*/

    /**
     * @Description: 通过合同公式查询课程id
     * @Param [id]
     * @return java.util.List<java.lang.Long>
     * <AUTHOR>
     **/
 /*   @ApiIgnore
    @PostMapping("getCourseIdsByContractFormulaId")
    public List<Long>  getCourseIdsByContractFormulaId(@RequestParam(value = "id") Long id)  {
        return contractFormulaService.getCourseIdsByContractFormulaId(id);
    }
*/
    /**
     * feign调用 通过合同公式查询合同公式佣金配置
     *
     * @Date 10:35 2021/6/9
     * <AUTHOR>
     */
/*    @ApiIgnore
    @PostMapping("getContractFormulaCommissionByContractFormulaId")
    public List<ContractFormulaCommissionVo> getContractFormulaCommissionByContractFormulaId(@RequestParam(value = "id") Long id)  {
        return contractFormulaService.getContractFormulaCommissionByContractFormulaId(id);
    }*/


    /**
     * feign调用 通过合同公式查询前置学校
     *
     * @Date 16:01 2021/6/10
     * <AUTHOR>
     */
   /* @ApiIgnore
    @GetMapping("getPreInstitutionByContractFormulaId")
    public List<Long> getContractFormulaPreInstitutionByContractFormulaId(@RequestParam Long id)  {
        return contractFormulaService.getContractFormulaPreInstitutionByContractFormulaId(id);
    }*/

    /**
     * feign调用 通过合同公式查询前置集团
     *
     * @Date 16:01 2021/6/10
     * <AUTHOR>
     */
/*    @ApiIgnore
    @GetMapping("getPreInstitutionGroupByContractFormulaId")
    public List<Long> getPreInstitutionGroupByContractFormulaId(@RequestParam Long id)  {
        return contractFormulaService.getPreInstitutionGroupByContractFormulaId(id);
    }*/

    /**
     * feign调用 通过合同公式查询前置课程等级
     *
     * @Date 16:01 2021/6/10
     * <AUTHOR>
     */
 /*   @ApiIgnore
    @GetMapping("getPreMajorLevelByContractFormulaId")
    public List<Long> getPreMajorLevelByContractFormulaId(@RequestParam Long id)  {
        return contractFormulaService.getPreMajorLevelByContractFormulaId(id);
    }*/


    /**
     * feign调用 通过合同公式id查询合同公式  学校课程信息
     *
     * @Date 12:10 2021/6/15
     * <AUTHOR>
     */
/*    @ApiIgnore
    @GetMapping("getContractFormulaConfigByContractFormulaId")
    public ContractFormulaFeignVo getContractFormulaConfigByContractFormulaId(@RequestParam Long formulaId)  {
        return contractFormulaService.getContractFormulaConfigByContractFormulaId(formulaId);
    }*/

    /**
     * 初步检查学习计划-合同公式匹配
     *
     * @Date 16:13 2021/7/16
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("checkContractFormula")
    String checkContractFormula(@RequestParam("studentCompanyId") Long studentCompanyId, @RequestParam(value = "areaCountryId") Long areaCountryId,
                                @RequestBody StudentOfferItem studentOfferItem, @RequestParam("contractFormulaId") Long contractFormulaId) {
        return contractFormulaService.checkContractFormula(studentCompanyId, areaCountryId, studentOfferItem.getFkInstitutionCourseId(), contractFormulaId);
    }


    /**
     * feign调用 根据合同公式Id获取合同公式信息
     *
     * @Date 11:26 2021/7/19
     * <AUTHOR>
     */
   /* @ApiIgnore
    @GetMapping(value = "getContractFormulaByFormulaId")
    ContractFormula getContractFormulaByFormulaId(@RequestParam(value = "formulaId") Long formulaId)  {
        return contractFormulaService.getContractFormulaByFormulaId(formulaId);
    }*/

}
