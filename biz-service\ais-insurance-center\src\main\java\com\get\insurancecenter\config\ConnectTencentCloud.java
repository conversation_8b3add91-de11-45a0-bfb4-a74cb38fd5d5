package com.get.insurancecenter.config;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.region.Region;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class ConnectTencentCloud {
    private static COSClient cosClient;
    private static String apCity;
    private static String secretId;
    private static String secretKey;

    @Value("${file.tencentcloudfile.apCity:ap-shanghai}")
    public void setApCity(String apCity) {
        ConnectTencentCloud.apCity = apCity;
    }

    @Value("${file.tencentcloudfile.secretId:AKIDvkNr8KK71757bv8GDygJoGgEfw0HNHza}")
    public void setSecretId(String secretId) {
        ConnectTencentCloud.secretId = secretId;
    }

    @Value("${file.tencentcloudfile.secretKey:lQ8oRyOk12uETcyCGILG0b8sgchpttiT}")
    public void setSecretKey(String secretKey) {
        ConnectTencentCloud.secretKey = secretKey;
    }

    //返回公开桶的client对象
    public static COSClient getPublicCosClient() {
        COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);
        Region region = new Region(apCity);
        ClientConfig clientconfig = new ClientConfig(region);
        cosClient = new COSClient(cred, clientconfig);
        return cosClient;
    }

    //返回默认私密桶的client对象
    public static COSClient getPrivateCosClient() {
        COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);
        Region region = new Region(apCity);
        ClientConfig clientconfig = new ClientConfig(region);
        cosClient = new COSClient(cred, clientconfig);
        return cosClient;
    }
}
