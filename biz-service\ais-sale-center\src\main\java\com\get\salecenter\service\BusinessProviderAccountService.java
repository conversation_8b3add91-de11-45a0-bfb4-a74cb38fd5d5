package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.vo.BusinessProviderAccountVo;
import com.get.salecenter.dto.BusinessProviderAccountDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BusinessProviderAccountService {


    /**
     * 获取业务提供商账户列表
     * @param businessProviderAccountDto
     * @param page
     * @return
     */
    ResponseBo<BusinessProviderAccountVo> getBusinessProviderAccounts(BusinessProviderAccountDto businessProviderAccountDto, Page page);


    /**
     * 新增
     * @param businessProviderAccountDto
     * @return
     */
    SaveResponseBo add(BusinessProviderAccountDto businessProviderAccountDto);


    /**
     * 快捷设置首选合同
     * @param businessProviderId
     * @param accountId
     * @return
     */
    SaveResponseBo quickFirstContractAccount(Long businessProviderId,Long accountId);


    /**
     * 快速激活或屏蔽合同账户
     * @param businessProviderId
     * @param accountId
     * @param status
     * @return
     */
    SaveResponseBo quickActivationOrMask(Long businessProviderId,Long accountId,Boolean status);

    /**
     * 详情
     * @param id
     * @return
     */
    BusinessProviderAccountVo findInfoById(Long id);

    /**
     * 更新
     * @param businessProviderAccountDto
     * @return
     */
    BusinessProviderAccountVo update(BusinessProviderAccountDto businessProviderAccountDto);


    /**
     * 删除
     * @param id
     * @return
     */
    SaveResponseBo delete(Long id);


    /**
     * 业务提供商合同账户列表账户重复提示
     * @param id
     * @param businessProviderId
     * @param bankAccount
     * @param bankAccountNum
     * @return
     */
    ResponseBo<String> getBusinessProviderContractAccountExist(Long id, Long businessProviderId, String bankAccount, String bankAccountNum);


    /**
     * 获取业务提供商账户列表
     * @param fkTargetId
     * @return
     */
    List<BaseSelectEntity> getBusinessProviderAccountList(Long fkTargetId);

    /**
     * 获取业务提供商账
     * @param fkBankAccountId
     * @return
     */
    String getBusinessProviderBankAccountNameById(Long fkBankAccountId);
}
