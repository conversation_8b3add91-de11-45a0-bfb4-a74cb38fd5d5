package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 付款方式类型
 */
@Data
@TableName("u_payment_method_type")
public class PaymentMethodType extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "类型名称")
    private String typeName;

    @ApiModelProperty(value = "科目Id")
    private Long fkAccountingItemId;

    @ApiModelProperty(value = "对应凭证类型：转/现收/现付/银收/银付")
    private String vouchType;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

}