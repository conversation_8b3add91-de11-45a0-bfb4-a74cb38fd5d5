package com.get.salecenter.utils.sale;

import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.service.AgentLabelService;
import com.get.salecenter.vo.AgentLabelVo;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class GetAgentLabelDataUtils {
    // 注入服务
    private final AgentLabelService agentLabelService;

    public GetAgentLabelDataUtils(AgentLabelService agentLabelService) {
        this.agentLabelService = agentLabelService;
    }


    public AgentLabelResult getAgentLabelMap(Set<Long> agentIds) {
        if (GeneralTool.isEmpty(agentIds)) {
            return new AgentLabelResult(Collections.emptyMap(),null);
        }
        List<AgentLabelVo> agentLabelList = agentLabelService.getAgentLabelListByAgentDto(agentIds, null);
        Map<Long, List<AgentLabelVo>> agentLabelMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(agentLabelList)) {
            agentLabelMap = agentLabelList.stream()
                    .collect(Collectors.groupingBy(AgentLabelVo::getFkAgentId));
        }

        return new AgentLabelResult(agentLabelMap, null);


    }

    public AgentLabelResult getAgentEmailLabelMap(Set<String> agentEmails) {
        if (GeneralTool.isEmpty(agentEmails)) {
            return new AgentLabelResult(null,Collections.emptyMap());
        }
        List<AgentLabelVo> agentLabelList = agentLabelService.getAgentLabelListByAgentDto(null, agentEmails);
        Map<String, List<AgentLabelVo>> agentEmailLabelMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(agentLabelList)) {
            agentEmailLabelMap = agentLabelList.stream()
                    .collect(Collectors.groupingBy(AgentLabelVo::getLabelEmail));
        }

        return new AgentLabelResult(null, agentEmailLabelMap);


    }


    public static class AgentLabelResult {
        private final Map<Long, List<AgentLabelVo>> agentLabelMap;
        private final Map<String, List<AgentLabelVo>> agentEmailLabelMap;

        public AgentLabelResult(Map<Long, List<AgentLabelVo>> agentLabelMap, Map<String, List<AgentLabelVo>> agentEmailLabelMap) {
            this.agentLabelMap = agentLabelMap;
            this.agentEmailLabelMap = agentEmailLabelMap;
        }
        public Map<Long, List<AgentLabelVo>> getAgentLabelMap() {
            return agentLabelMap;
        }

        public Map<String, List<AgentLabelVo>> getAgentEmailLabelMap() {
            return agentEmailLabelMap;
        }

    }

    public AgentLabelResult getAgentLabelMapById(Long agentId) {
        if (GeneralTool.isEmpty(agentId)) {
            return new AgentLabelResult(Collections.emptyMap(),null);
        }
        Set<Long> agentIds = new HashSet<>();
        agentIds.add(agentId);
        List<AgentLabelVo> agentLabelList = agentLabelService.getAgentLabelListByAgentDto(agentIds, null);
        Map<Long, List<AgentLabelVo>> agentLabelMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(agentLabelList)) {
            agentLabelMap = agentLabelList.stream()
                    .collect(Collectors.groupingBy(AgentLabelVo::getFkAgentId));
        }

        return new AgentLabelResult(agentLabelMap, null);
    }


    public AgentLabelResult getAgentEmailLabelMapByEmail(String agentEmail) {
        HashSet<String> agentEmails = new HashSet<>();
        agentEmails.add(agentEmail);

        if (GeneralTool.isEmpty(agentEmails)) {
            return new AgentLabelResult(null,Collections.emptyMap());
        }
        List<AgentLabelVo> agentLabelList = agentLabelService.getAgentLabelListByAgentDto(null, agentEmails);
        Map<String, List<AgentLabelVo>> agentEmailLabelMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(agentLabelList)) {
            agentEmailLabelMap = agentLabelList.stream()
                    .collect(Collectors.groupingBy(AgentLabelVo::getLabelEmail));
        }

        return new AgentLabelResult(null, agentEmailLabelMap);


    }

    @FunctionalInterface
    public interface AgentLabelSetter<T> {
        void setAgentLabelVos(T target, List<AgentLabelVo> labels);
    }

    public <T> void setAgentLabelVos(T data, Long agentId, AgentLabelSetter<T> setter) {
        Map<Long, List<AgentLabelVo>> agentLabelMap = getAgentLabelMapById(agentId).getAgentLabelMap();
        List<AgentLabelVo> labelList = agentLabelMap.get(agentId);
        setter.setAgentLabelVos(data, labelList);
    }

    public <T> void setAgentEmailLabelVos(T data, String agentEmail, AgentLabelSetter<T> setter) {
        Map<String, List<AgentLabelVo>> agentLabelMap = getAgentEmailLabelMapByEmail(agentEmail).getAgentEmailLabelMap();
        List<AgentLabelVo> labelList = agentLabelMap.get(agentEmail);
        setter.setAgentLabelVos(data, labelList);
    }

    public <T> void setAgentLabelVosByLabelMap(T data, Map<Long, List<AgentLabelVo>> agentLabelMap, Long agentId, AgentLabelSetter<T> setter) {
        List<AgentLabelVo> labelList = agentLabelMap.get(agentId);
        setter.setAgentLabelVos(data, labelList);
    }

    public <T> void setAgentLabelVosByEmailLabelMap(T data, Map<String, List<AgentLabelVo>> agentEmailLabelMap, String email, AgentLabelSetter<T> setter) {
        List<AgentLabelVo> labelList = agentEmailLabelMap.get(email);
        setter.setAgentLabelVos(data, labelList);
    }


}
