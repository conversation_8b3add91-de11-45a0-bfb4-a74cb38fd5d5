package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.service.WechatService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "微信控制类")
@RestController
@RequestMapping("sale/wechat")
@Slf4j
public class WechatController {

    @Resource
    private WechatService wechatService;

    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "微信/生成授权链接")
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @GetMapping("/getAuthUrl")
    public ResponseBo<String> getAuthUrl() {
        return new ResponseBo<>(wechatService.getAuthUrl());
    }

    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "微信/获取用户openId")
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @GetMapping("/getOpenId")
    public ResponseBo<String> getOpenId(@RequestParam("code") String code, @RequestParam("state") String state) {
        return new ResponseBo<>(wechatService.getOpenId(code, state));
    }




}
