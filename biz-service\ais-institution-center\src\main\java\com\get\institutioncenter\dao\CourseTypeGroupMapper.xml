<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.CourseTypeGroupMapper">
  <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.CourseTypeGroup">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type_group_name" jdbcType="VARCHAR" property="typeGroupName" />
    <result column="type_group_name_chn" jdbcType="VARCHAR" property="typeGroupNameChn" />
    <result column="view_order" jdbcType="INTEGER" property="viewOrder" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
    <result column="public_level" jdbcType="VARCHAR" property="publicLevel" />
    <result column="mode" jdbcType="INTEGER" property="mode" />
  </resultMap>
  <sql id="Base_Column_List">
    id,mode,type_group_name,type_group_name_chn, view_order, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
  </sql>
  <!--<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">-->
  <!--select-->
  <!--<include refid="Base_Column_List" />-->
  <!--from u_course_type_group-->
  <!--where id = #{id,jdbcType=BIGINT}-->
  <!--</select>-->
  <!--<delete id="deleteByPrimaryKey" parameterType="java.lang.Long">-->
  <!--delete from u_course_type_group-->
  <!--where id = #{id,jdbcType=BIGINT}-->
  <!--</delete>-->
  <insert id="insert" parameterType="com.get.institutioncenter.entity.CourseTypeGroup">
    insert into u_course_type_group (id, mode,type_group_name,type_group_name_chn, view_order,public_level,
                                     gmt_create, gmt_create_user, gmt_modified,
                                     gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{mode,jdbcType=INTEGER},#{typeGroupName,jdbcType=VARCHAR},  #{typeGroupNameChn,jdbcType=VARCHAR},#{viewOrder,jdbcType=INTEGER},#{publicLevel,jdbcType=VARCHAR},
            #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP},
            #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.CourseTypeGroup">
    insert into u_course_type_group
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="mode != null">
        mode,
      </if>
      <if test="typeGroupName != null">
        type_group_name,
      </if>
      <if test="typeGroupNameChn != null">
        type_group_name_chn,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="mode != null">
        #{mode,jdbcType=INTEGER},
      </if>
      <if test="typeGroupName != null">
        #{typeGroupName,jdbcType=VARCHAR},
      </if>
      <if test="typeGroupNameChn != null">
        #{typeGroupNameChn,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.get.institutioncenter.entity.CourseTypeGroup">
    update u_course_type_group
    <set>
      <if test="typeGroupName != null">
        type_group_name = #{typeGroupName,jdbcType=VARCHAR},
      </if>
      <if test="mode != null">
        mode = #{mode,jdbcType=INTEGER},
      </if>
      <if test="typeGroupNameChn != null">
        type_group_name_chn = #{typeGroupNameChn,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        view_order = #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.institutioncenter.entity.CourseTypeGroup">
    update u_course_type_group
    set type_group_name = #{typeGroupName,jdbcType=VARCHAR},
        mode = #{mode,jdbcType=INTEGER},
        view_order = #{viewOrder,jdbcType=INTEGER},
        type_group_name_chn = #{typeGroupNameChn,jdbcType=VARCHAR},
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select ifnull(max(view_order)+1,1) from u_course_type_group
  </select>
  <select id="getCourseTypeGroupList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    select id,mode,type_group_name as name from u_course_type_group order by mode,view_order desc
  </select>
  <select id="getCourseTypeGroupListModeThree" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    select id,
           mode,
           CASE WHEN IFNULL(type_group_name_chn, '') = '' THEN `type_group_name` ELSE CONCAT(`type_group_name`, '（', type_group_name_chn, '）') END name
    from u_course_type_group
    where mode = 3
    order by mode,view_order desc
  </select>
  <select id="getTypeGroupNameByProviderId" resultType="java.lang.String">
    SELECT
      group_concat( tg.type_group_name SEPARATOR '，' ) countryNames
    FROM
      u_course_type_group tg
        LEFT JOIN r_course_type_group_course_type tcg ON tcg.fk_course_type_group_id = tg.id
    WHERE
      tcg.fk_course_type_id = #{id}
  </select>
  <select id="getGroupTypeIdsByCourseTypeIds" resultType="java.lang.String">
    select GROUP_CONCAT(distinct fk_course_type_group_id)
    from r_course_type_group_course_type
    where 1=1
    and fk_course_type_id in
    <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
      #{id}
    </foreach>
  </select>

  <select id="getBdyData" parameterType="com.get.institutioncenter.entity.CourseTypeGroup"
          resultType="com.get.institutioncenter.entity.CourseTypeGroup">
          SELECT
              id,
              type_group_name,
              type_group_name_chn,
              mode,
              view_order,
              public_level,
              gmt_create_user,
              gmt_create,
              gmt_modified_user,
              gmt_modified
          FROM
              u_course_type_group
              <where>
                <if test="courseTypeGroupDto.keyWord != null and courseTypeGroupDto.keyWord != '' ">
                  and (type_group_name like concat("%",#{courseTypeGroupDto.keyWord},"%") or type_group_name_chn like concat("%",#{courseTypeGroupDto.keyWord},"%"))
                </if>
                <if test="str != null and str.size > 0">
                and
                  <foreach collection="str" item="item" separator="or" open="(" index=")" close=")">
                    FIND_IN_SET(#{item},public_level)
                  </foreach>
                </if>

              </where>
          ORDER BY
              view_order DESC

  </select>
  <select id="getCourseGroupList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT id,type_group_name as `name`,
        type_group_name_chn as name_chn,
        CASE
            WHEN IFNULL(type_group_name_chn, '') = '' THEN
      type_group_name
            ELSE
            CONCAT(
      type_group_name,
            '（',
      type_group_name_chn,
            '）'
            )
            END fullName
        FROM
            u_course_type_group
      WHERE
        mode = 3
      <if test="keyword!=null and keyword!=''">
        AND (type_group_name LIKE concat('%',#{keyword},'%') OR type_group_name_chn LIKE concat('%',#{keyword},'%'))
      </if>
  </select>
    <select id="getCourseGroupByIds" resultType="com.get.salecenter.vo.SelItem">
        SELECT id as keyId,
        CASE
            WHEN IFNULL(type_group_name_chn, '') = '' THEN
            type_group_name
            ELSE
            CONCAT(
            type_group_name,
            '（',
        type_group_name_chn,
            '）'
            )
            END val
        FROM
            u_course_type_group
            WHERE id IN
            <foreach collection="ids" open="(" close=")" separator="," item="uid">
                #{uid}
            </foreach>
    </select>
    <select id="getCourseTypeGroupNameById" resultType="java.lang.String">
        SELECT
        CASE
            WHEN IFNULL(type_group_name_chn, '') = '' THEN
            type_group_name
            ELSE
            CONCAT(
            type_group_name,
            '（',
            type_group_name_chn,
            '）'
            )
            END fullName
        FROM
            u_course_type_group
            WHERE id = #{id}
    </select>


</mapper>