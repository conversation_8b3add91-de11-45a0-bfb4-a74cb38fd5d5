package com.get.partnercenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:Oliver
 * @Date: 2025/1/16  10:10
 * @Version 1.0
 * partner用户保存
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SavePartnerUserDto {

    @ApiModelProperty("平台/应用Code")
    private String platformCode;

    @ApiModelProperty("平台/应用Id")
    private Long platformId;

    @ApiModelProperty("租户id")
    private Long tenantId;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("电子邮箱")
    private String email;

    @ApiModelProperty("角色ID")
    private Long roleId;

    @ApiModelProperty("创建人LoginId")
    private String createUser;

    @ApiModelProperty("系统用户ID-创建后返回")
    private Long userId;

    @ApiModelProperty("默认密码")
    private String password;

    @ApiModelProperty("伙伴角色ID")
    private Long partnerRoleId;
}
