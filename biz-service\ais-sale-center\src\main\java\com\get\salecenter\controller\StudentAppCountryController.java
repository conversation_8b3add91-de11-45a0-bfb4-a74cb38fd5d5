package com.get.salecenter.controller;

import com.get.salecenter.service.StudentAppCountryService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Set;

/**
 * Created by <PERSON>.
 * Time: 18:22
 * Date: 2021/9/8
 * Description:学生申请国家控制器
 */
@Api(tags = "学生申请国家管理")
@RestController
@RequestMapping("sale/studentAppCountry")
public class StudentAppCountryController {

    @Resource
    private StudentAppCountryService studentAppCountryService;

    /**
     * @Description: feign调用 根据学生申请国家ids获取名称
     * @Author: Jerry
     * @Date:18:24 2021/9/8
     */
    @ApiIgnore
    @GetMapping("getNamesByStudentAppCountryIds")
    public Map<Long, String> getNamesByStudentAppCountryIds(@RequestParam("studentAppCountryIds") Set<Long> studentAppCountryIds) {
        return studentAppCountryService.getNamesByStudentAppCountryIds(studentAppCountryIds);
    }
}
