package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.ConventionProcedure;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: Sea
 * @create: 2020/7/27 12:21
 * @verison: 1.0
 * @description: 峰会流程管理mapper
 */
@Mapper
public interface ConventionProcedureMapper extends BaseMapper<ConventionProcedure> {

    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(ConventionProcedure record);

    /**
     * 查找该峰会流程的参加人数
     *
     * @param id
     * @return
     */
    Long getPersonProcedureCount(@Param("id") Long id);

    /**
     * 获取该峰会中流程步骤索引最大值
     *
     * @param id
     * @return
     */
    Integer getMaxStepIndex(@Param("id") Long id);

    /**
     * @return java.lang.Boolean
     * @Description :
     * @Param [id]
     * <AUTHOR>
     */
    Boolean conventionProcedureIsEmpty(Long id);
}