package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.type.Alias;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_permission_group_grade_name")
@Alias("SchoolGatePermissionGroupGradeName")
public class PermissionGroupGradeName extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 权限组别Id
     */
    @ApiModelProperty(value = "权限组别Id")
    @Column(name = "fk_permission_group_id")
    private Long fkPermissionGroupId;
    /**
     * 权限级别Id
     */
    @ApiModelProperty(value = "权限级别Id")
    @Column(name = "fk_permission_grade_id")
    private Long fkPermissionGradeId;
    /**
     * 权格名称
     */
    @ApiModelProperty(value = "权格名称")
    @Column(name = "permission_name")
    private String permissionName;
}