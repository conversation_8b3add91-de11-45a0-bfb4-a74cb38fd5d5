package com.get.salecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-03-13
 */
@Data
public class IncentivePolicyAddOfferItemDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "Incentive奖励政策Id", groups = {BaseVoEntity.Add.class})
    @ApiModelProperty(value = "Incentive奖励政策Id")
    private Long incentivePolicyId;

    @NotNull(message = "学生申请方案项目Ids", groups = {BaseVoEntity.Add.class})
    @ApiModelProperty(value = "学生申请方案项目Ids")
    private List<Long> studentOfferItemIds;

    public interface Add {
    }
}
