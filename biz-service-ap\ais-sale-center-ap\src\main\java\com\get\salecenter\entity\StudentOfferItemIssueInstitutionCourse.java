package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_student_offer_item_issue_institution_course")
public class StudentOfferItemIssueInstitutionCourse extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 学生Id
     */
    @ApiModelProperty(value = "学生Id")
    @Column(name = "fk_student_id")
    private Long fkStudentId;
    /**
     * 学生申请方案项目Id
     */
    @ApiModelProperty(value = "学生申请方案项目Id")
    @Column(name = "fk_student_offer_item_id")
    private Long fkStudentOfferItemId;
    /**
     * 学生申请学校课程Id（ISSUE申请）
     */
    @ApiModelProperty(value = "学生申请学校课程Id（ISSUE申请）")
    @Column(name = "fk_student_institution_course_id_issue")
    private Long fkStudentInstitutionCourseIdIssue;

    /**
     * 学生申请学校课程Id（ISSUEv2版申请）
     */
    @ApiModelProperty(value = "学生申请学校课程Id（ISSUEv2版申请）")
    @Column(name = "fk_student_institution_course_id_issue2")
    private Long fkStudentInstitutionCourseIdIssue2;
}