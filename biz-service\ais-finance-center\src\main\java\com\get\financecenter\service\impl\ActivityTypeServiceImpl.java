package com.get.financecenter.service.impl;

import com.get.financecenter.enums.ActivityTypeEnum;
import com.get.financecenter.service.IActivityTypeService;
import com.get.financecenter.vo.ActivityTypeVo;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
public class ActivityTypeServiceImpl implements IActivityTypeService {

    @Override
    public List<ActivityTypeVo> getActivityType() {
        List<ActivityTypeVo> options = ActivityTypeEnum.getOptions();
        return options;
    }

}
