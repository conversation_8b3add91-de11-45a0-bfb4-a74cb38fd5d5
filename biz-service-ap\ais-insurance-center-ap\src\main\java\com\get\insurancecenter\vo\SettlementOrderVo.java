package com.get.insurancecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: VON
 * @Date: 2025/7/31
 * @Version 1.0
 * @apiNote: 结算订单列表返回VO
 */
@Data
public class SettlementOrderVo {

    @ApiModelProperty(value = "订单编号")
    private String orderNum;

    @ApiModelProperty(value = "订单状态（结算状态）")
    private Integer orderStatus;

    @ApiModelProperty(value = "订单状态名称")
    private String orderStatusName;

    @ApiModelProperty(value = "保险公司")
    private String insuranceCompanyName;

    @ApiModelProperty(value = "保险产品")
    private String productTypeName;

    @ApiModelProperty(value = "类型")
    private String insuranceType;

    @ApiModelProperty(value = "保单号")
    private String insuranceNum;

    @ApiModelProperty(value = "姓名")
    private String insurantName;

    @ApiModelProperty(value = "性别")
    private String insurantGender;

    @ApiModelProperty(value = "国家")
    private String insurantNationality;

    @ApiModelProperty(value = "护照")
    private String insurantPassportNum;

    @ApiModelProperty(value = "保险开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceStartTime;

    @ApiModelProperty(value = "保险结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceEndTime;

    @ApiModelProperty(value = "保单金额")
    private BigDecimal insuranceAmount;

    @ApiModelProperty(value = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderTime;

    @ApiModelProperty(value = "结算比例")
    private Integer commissionRateAgent;

    @ApiModelProperty(value = "应付金额")
    private BigDecimal payableAmount;

    @ApiModelProperty(value = "手续费")
    private BigDecimal serviceFee;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "合作伙伴名称")
    private String partnerName;

    // ========== 业务ID字段 ==========
    @ApiModelProperty(value = "保险订单结算ID")
    private Long insuranceOrderSettlementId;

    @ApiModelProperty(value = "保险订单ID")
    private Long insuranceOrderId;

    @ApiModelProperty(value = "结算单明细ID")
    private Long settlementBillItemId;

    @ApiModelProperty(value = "代理ID")
    private Long agentId;

    @ApiModelProperty(value = "合作伙伴用户ID")
    private Long partnerUserId;

    @ApiModelProperty(value = "保险公司ID")
    private Long insuranceCompanyId;

    @ApiModelProperty(value = "产品类型ID")
    private Long productTypeId;

    @ApiModelProperty(value = "应付计划ID")
    private Long payablePlanId;
}
