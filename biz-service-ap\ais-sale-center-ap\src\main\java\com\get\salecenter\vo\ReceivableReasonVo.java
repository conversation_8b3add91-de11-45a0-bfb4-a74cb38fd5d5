package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.ReceivableReason;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @DATE: 2020/11/2
 * @TIME: 14:29
 * @Description:
 **/
@Data
public class ReceivableReasonVo extends BaseEntity {

    //==============实体类ReceivableReason================
    private static final long serialVersionUID = 1L;
    /**
     * 原因名称
     */
    @ApiModelProperty(value = "原因名称")
    @Column(name = "reason_name")
    private String reasonName;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;

}
