package com.get.resumecenter.service.Impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.resumecenter.dao.ResumeCertificateMapper;
import com.get.resumecenter.vo.ResumeCertificateVo;
import com.get.resumecenter.entity.ResumeCertificate;
import com.get.resumecenter.service.IResumeCertificateService;
import com.get.resumecenter.dto.ResumeCertificateDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/8/10
 * @TIME: 17:26
 * @Description: 证书实现类
 **/
@Service
public class ResumeCertificateServiceImpl implements IResumeCertificateService {
    @Resource
    private ResumeCertificateMapper certificateMapper;
    @Autowired
    private UtilService utilService;

    @Override
    public List<ResumeCertificateVo> getResumeCertificateListDto(Long resumeId) {
        if (GeneralTool.isEmpty(resumeId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("resume_id_null"));
        }
//        Example example = new Example(ResumeCertificate.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkResumeId", resumeId);
        List<ResumeCertificate> resumeCertificates = certificateMapper.selectList(Wrappers.<ResumeCertificate>lambdaQuery().eq(ResumeCertificate::getFkResumeId, resumeId));
        return resumeCertificates.stream()
                .map(resumeCertificate -> BeanCopyUtils.objClone(resumeCertificate, ResumeCertificateVo::new)).collect(Collectors.toList());
    }

    @Override
    public ResumeCertificateVo getResumeCertificateById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ResumeCertificate resumeCertificate = certificateMapper.selectById(id);
        return BeanCopyUtils.objClone(resumeCertificate, ResumeCertificateVo::new);
    }

    @Override
    public Long addResumeCertificate(ResumeCertificateDto certificateVo) {
//        if (!StaffContext.getStaff().getIsAdmin()) {
//            if (!StaffContext.getStaff().getIsModifiedResume()) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
//            }
//        }
        if (!GetAuthInfo.isAdmin()) {
            if (!SecureUtil.getIsModifiedResume()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
            }
        }
        if (GeneralTool.isEmpty(certificateVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ResumeCertificate resumeCertificate = BeanCopyUtils.objClone(certificateVo, ResumeCertificate::new);
        utilService.updateUserInfoToEntity(resumeCertificate);
        int i = certificateMapper.insertSelective(resumeCertificate);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return resumeCertificate.getId();
    }

    @Override
    public ResumeCertificateVo updateResumeCertificate(ResumeCertificateDto certificateVo) {
//        if (!StaffContext.getStaff().getIsAdmin()) {
//            if (!StaffContext.getStaff().getIsModifiedResume()) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
//            }
//        }
        if (!GetAuthInfo.isAdmin()) {
            if (!SecureUtil.getIsModifiedResume()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
            }
        }
        if (GeneralTool.isEmpty(certificateVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ResumeCertificate resumeCertificate = BeanCopyUtils.objClone(certificateVo, ResumeCertificate::new);
        utilService.updateUserInfoToEntity(resumeCertificate);
        certificateMapper.updateById(resumeCertificate);
        return getResumeCertificateById(resumeCertificate.getId());
    }

    @Override
    public void deleteResumeCertificate(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        certificateMapper.deleteById(id);

    }
}
