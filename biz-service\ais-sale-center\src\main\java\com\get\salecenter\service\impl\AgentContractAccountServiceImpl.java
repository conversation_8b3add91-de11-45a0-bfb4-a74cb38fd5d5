package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.salecenter.dao.sale.AgentContractAccountMapper;
import com.get.salecenter.vo.AgentContractAccountVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.vo.PayablePlanSettlementAgentAccountVo;
import com.get.salecenter.vo.SelItem;
import com.get.salecenter.entity.AgentContractAccount;
import com.get.financecenter.entity.PayablePlanSettlementAgentAccount;
import com.get.salecenter.service.BusinessProviderAccountService;
import com.get.salecenter.service.IAgentContractAccountService;
import com.get.salecenter.service.IDeleteService;
import com.get.salecenter.service.IMediaAndAttachedService;
import com.get.salecenter.service.IPayablePlanService;
import com.get.salecenter.utils.ConvertUtils;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.dto.AgentContractAccountDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/9/14
 * @TIME: 18:49
 * @Description:
 **/
@Service
public class AgentContractAccountServiceImpl extends BaseServiceImpl<AgentContractAccountMapper,AgentContractAccount> implements IAgentContractAccountService {
    @Resource
    private AgentContractAccountMapper accountMapper;
    @Resource
    private IInstitutionCenterClient iInstitutionCenterClient;
    @Resource
    private BusinessProviderAccountService businessProviderAccountService;
    @Autowired
    private UtilService utilService;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private IMediaAndAttachedService mediaAndAttachedService;
    @Resource
    private IPayablePlanService payablePlanService;

    /**
     * 账户列表数据
     *
     * @Date 15:08 2021/6/28
     * <AUTHOR>
     */
    @Override
    public List<AgentContractAccountVo> getAgentContractAccount(AgentContractAccountDto agentContractAccountDto, Page page) {
        if (GeneralTool.isEmpty(agentContractAccountDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
//        Example example = new Example(AgentContractAccount.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkAgentId", agentContractAccountDto.getFkAgentId());
//        //获取分页数据
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<AgentContractAccount> agentContractAccounts = accountMapper.selectByExample(example);
//        PageInfo<AgentContractAccount> pageInfo = new PageInfo<AgentContractAccount>(agentContractAccounts);
//        page.setTotalResult(new Long(pageInfo.getTotal()).intValue());

        IPage<AgentContractAccount> pages = accountMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), Wrappers.<AgentContractAccount>lambdaQuery().eq(AgentContractAccount::getFkAgentId, agentContractAccountDto.getFkAgentId()).orderByDesc(AgentContractAccount::getIsDefault));
        List<AgentContractAccount> agentContractAccounts = pages.getRecords();
        page.setAll((int) pages.getTotal());
        if (GeneralTool.isEmpty(agentContractAccounts)) {
            return Collections.emptyList();
        }
        //币种编号nums
        Set<String> fkCurrencyTypeNums = agentContractAccounts.stream().map(AgentContractAccount::getFkCurrencyTypeNum).collect(Collectors.toSet());
        //根据币种编号nums获取名称map
        Map<String, String> currencyNamesByNums = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkCurrencyTypeNums)) {
            Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(fkCurrencyTypeNums);
            if (result.isSuccess() && result.getData() != null) {
                currencyNamesByNums = result.getData();
            }
        }
        List<Long> accountIds = agentContractAccounts.stream().map(AgentContractAccount::getId).collect(Collectors.toList());
        List<Long> resultIds = accountMapper.getAgentContractAccountPaymentFrom(accountIds);
        Map<Long, List<MediaAndAttachedVo>> mediaAndAttachedDtosMap = mediaAndAttachedService.getMediaAndAttachedDtoByFkTableIds(TableEnum.AGENT_CONTRACT_ACCOUNT.key, Sets.newHashSet(accountIds));
        List<AgentContractAccountVo> agentContractAccountVos = new ArrayList<>();
        for (AgentContractAccount agentContractAccount : agentContractAccounts) {
            AgentContractAccountVo agentContractAccountVo = BeanCopyUtils.objClone(agentContractAccount, AgentContractAccountVo::new);
            agentContractAccountVo.setCurrencyTypeName(currencyNamesByNums.get(agentContractAccountVo.getFkCurrencyTypeNum()));
            agentContractAccountVo.setAccountCardTypeName(ProjectExtraEnum.getInitialValueByKey(agentContractAccount.getAccountCardType(),ProjectExtraEnum.cardType));
            agentContractAccountVo.setIsBindingPayment(resultIds.contains(agentContractAccount.getId()));
            //查询银行账户佣金结算状态
            Integer commissionSettlementStatus = financeCenterClient.getAccountCommissionSettlementStatus(agentContractAccount.getId()).getData();
            agentContractAccountVo.setCommissionSettlementStatus(commissionSettlementStatus);
            agentContractAccountVos.add(agentContractAccountVo);

            //设置附件
            List<MediaAndAttachedVo> mediaAndAttachedVos = mediaAndAttachedDtosMap.get(agentContractAccount.getId());
            agentContractAccountVo.setMediaAndAttachedDtos(mediaAndAttachedVos);
        }
        return agentContractAccountVos;
    }

    @Override
    public AgentContractAccountVo findAgentContractAccountById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AgentContractAccountVo branchAccountInfo = accountMapper.findBranchAccountInfoById(id);
        Result<Boolean> result = financeCenterClient.getCommissionSettlementAccountInfo(id);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        branchAccountInfo.setSettlementFlag(result.getData());
        return branchAccountInfo;
    }

    @Override
    public Long addContractAccount(AgentContractAccountDto contractAccountVo) {
        //转回html特殊字符
        MyStringUtils.UnescapeHtml(contractAccountVo);
        //同一个币种账号,只能有一个激活的
        if (contractAccountVo.getIsActive()) {
            Long existId = accountMapper.validateCurrency(contractAccountVo.getFkAgentId(), contractAccountVo.getFkCurrencyTypeNum(), null);
            if (GeneralTool.isNotEmpty(existId)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("agent_account_exist"));
            }
        }
        //新增首选时需判断是否已存在首选
        if (contractAccountVo.getIsDefault()) {
            Integer count = accountMapper.getIsDefaultContractAccount(contractAccountVo.getFkAgentId(),null);
            if (count > 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("agent_account_is_default_exist"));
            }
        }
        //多个币种，分别添加对应币种账号
        AgentContractAccount agentContractAccount = BeanCopyUtils.objClone(contractAccountVo, AgentContractAccount::new);
        agentContractAccount.setFkCurrencyTypeNum(contractAccountVo.getFkCurrencyTypeNum());
        utilService.updateUserInfoToEntity(agentContractAccount);
        int i = accountMapper.insert(agentContractAccount);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
//        for(String fkCurrencyTypeNum : contractAccountVo.getFkCurrencyTypeNumList()){
//            agentContractAccount.setFkCurrencyTypeNum(fkCurrencyTypeNum);
//            utilService.setCreateInfo(agentContractAccount);
//            int i = accountMapper.insertSelective(agentContractAccount);
//            if (i <= 0) {
//                throw new YServiceException(LocaleMessageUtils.getMessage("insert_fail"));
//            }
//        }
        return agentContractAccount.getId();
    }

    @Override
    public AgentContractAccountVo updateContractAccount(AgentContractAccountDto contractAccountVo) {
        //转回html特殊字符
        MyStringUtils.UnescapeHtml(contractAccountVo);
        //同一个币种账号,只能有一个激活的
        if (contractAccountVo.getIsActive()) {
            Long existId = accountMapper.validateCurrency(contractAccountVo.getFkAgentId(), contractAccountVo.getFkCurrencyTypeNum(), contractAccountVo.getId());

            if (GeneralTool.isNotEmpty(existId)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("agent_account_exist"));
            }

        }
        //修改为首选时需判断是否已存在首选
        if (contractAccountVo.getIsDefault()) {
            Integer count = accountMapper.getIsDefaultContractAccount(contractAccountVo.getFkAgentId(),contractAccountVo.getId());
            if (count > 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("agent_account_is_default_exist"));
            }
        }
        AgentContractAccount agentContractAccount = BeanCopyUtils.objClone(contractAccountVo, AgentContractAccount::new);
        utilService.updateUserInfoToEntity(agentContractAccount);
        accountMapper.updateById(agentContractAccount);

        //修改未结算佣金的银行币种标记
        Result result = financeCenterClient.updateCommissionSettlementAccountCurrencyTypeNum(agentContractAccount.getId(), agentContractAccount.getFkCurrencyTypeNum());
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        return findAgentContractAccountById(agentContractAccount.getId());
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //校验合同是否有绑定账号
        deleteService.deleteValidateAgentAccount(id);
        accountMapper.deleteById(id);
    }

    /**
     * 根据代理ids 获取银行账户
     *
     * @return
     * @Date 16:55 2021/12/22
     * <AUTHOR>
     */
    @Override
    public Map<Long, List<AgentContractAccountVo>> getAgentContractAccountByAgentIds(List<Long> agentIds) {
        List<AgentContractAccount> agentContractAccounts = accountMapper.selectagentContractAccountByAgentIds(agentIds);
        Map<Long, List<AgentContractAccountVo>> map = new HashMap<>();
        Set<String> currencyTypeNum = agentContractAccounts.stream().map(AgentContractAccount::getFkCurrencyTypeNum).collect(Collectors.toSet());
        Map<String, String> currencyNamesByNums = new HashMap<>();
        Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNum);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            currencyNamesByNums = result.getData();
        }
        for (AgentContractAccount agentContractAccount : agentContractAccounts) {
            AgentContractAccountVo account = new AgentContractAccountVo();
            account = BeanCopyUtils.objClone(agentContractAccount, AgentContractAccountVo::new);
            if (GeneralTool.isNotEmpty(account.getFkCurrencyTypeNum())) {
                account.setCurrencyTypeName(currencyNamesByNums.get(account.getFkCurrencyTypeNum()));
            }
            List<AgentContractAccountVo> agentContractAccountList = map.get(agentContractAccount.getFkAgentId());
            if (GeneralTool.isEmpty(agentContractAccountList)) {
                agentContractAccountList = new ArrayList<>();
            }
            agentContractAccountList.add(account);
            map.put(agentContractAccount.getFkAgentId(), agentContractAccountList);
        }
        return map;
    }

//    /**
//     * 根据应付计划ids 获取结算标记
//     *
//     * @Date 12:07 2022/1/11
//     * <AUTHOR>
//     */
//    @Override
//    public Map<Long, List<PayablePlanSettlementAgentAccountVo>> getSettlementMarkByPayablePlanIds(List<Long> payablePlanIds) {
//        LambdaQueryWrapper<PayablePlanSettlementAgentAccount> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.in(PayablePlanSettlementAgentAccount::getFkPayablePlanId, payablePlanIds);
//        lambdaQueryWrapper.isNull(PayablePlanSettlementAgentAccount::getNumSettlementBatch);
//        List<PayablePlanSettlementAgentAccount> payablePlanSettlementAgentAccounts = payablePlanSettlementAgentAccountMapper.selectList(lambdaQueryWrapper);
//
//        Map<Long, List<PayablePlanSettlementAgentAccountVo>> map = new HashMap<>();
//        Set<String> currencyTypeNum = payablePlanSettlementAgentAccounts.stream().map(PayablePlanSettlementAgentAccount::getFkCurrencyTypeNum).collect(Collectors.toSet());
//        Map<String, String> currencyNamesByNums = new HashMap<>();
//        Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNum);
//        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
//            currencyNamesByNums = result.getData();
//        }
//        for (PayablePlanSettlementAgentAccount payablePlanSettlementAgentAccount : payablePlanSettlementAgentAccounts) {
//            List<PayablePlanSettlementAgentAccountVo> payablePlanSettlementAgentAccountVos = map.get(payablePlanSettlementAgentAccount.getFkPayablePlanId());
//            if (GeneralTool.isEmpty(payablePlanSettlementAgentAccountVos)) {
//                payablePlanSettlementAgentAccountVos = new ArrayList<>();
//            }
//            PayablePlanSettlementAgentAccountVo studentOfferItemSettlementAgentAccountDto = new PayablePlanSettlementAgentAccountVo();
//            BeanCopyUtils.copyProperties(payablePlanSettlementAgentAccount, studentOfferItemSettlementAgentAccountDto);
//            studentOfferItemSettlementAgentAccountDto.setCurrencyTypeName(currencyNamesByNums.get(studentOfferItemSettlementAgentAccountDto.getFkCurrencyTypeNum()));
//            payablePlanSettlementAgentAccountVos.add(studentOfferItemSettlementAgentAccountDto);
//            map.put(payablePlanSettlementAgentAccount.getFkPayablePlanId(), payablePlanSettlementAgentAccountVos);
//        }
//        return map;
//    }

    /**
     * 银行账户下拉框
     *
     * @return
     */
    @Override
    public List<BaseSelectEntity> getContractAccountSelect(String fkTypeKey, Long fkTargetId) {
        if (TableEnum.SALE_AGENT.key.equals(fkTypeKey)) {
            return accountMapper.getAgentContractAccountSelect(fkTargetId);
        }else if (TableEnum.INSTITUTION_PROVIDER.key.equals(fkTypeKey)){
            return iInstitutionCenterClient.getInstitutionProviderAccountList(fkTargetId);
        }else if (TableEnum.SALE_BUSINESS_PROVIDER.key.equals(fkTypeKey)){
            return businessProviderAccountService.getBusinessProviderAccountList(fkTargetId);
        }
        throw new GetServiceException(LocaleMessageUtils.getMessage("illegal_target_type"));
    }

    @Override
    public List<AgentContractAccountVo> getAgentContractAccountSelectById(Long agentId) {
        return accountMapper.getAgentContractAccountSelectById(agentId);
    }

    /**
     * feign 根据代理银行账号ids获取银行账号信息
     *
     * @return
     * @Date 16:42 2022/1/6
     * <AUTHOR>
     */
    @Override
    public Map<Long, AgentContractAccountVo> getAgentContractAccountByAccountIds(List<Long> accountIds) {
        List<AgentContractAccountVo> agentContractAccountList = accountMapper.selectAgentContractAccountByAccountIds(accountIds);
        Set<String> currencyTypeNumSet = agentContractAccountList.stream().map(AgentContractAccountVo::getFkCurrencyTypeNum).collect(Collectors.toSet());
        Map<String, String> currencyNamesByNums = new HashMap<>();
        Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNumSet);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            currencyNamesByNums = result.getData();
        }
        Map<Long, AgentContractAccountVo> map = new HashMap<>();
        for (AgentContractAccountVo agentContractAccount : agentContractAccountList) {
            AgentContractAccountVo agentContractAccountVo = BeanCopyUtils.objClone(agentContractAccount, AgentContractAccountVo::new);
            agentContractAccountVo.setCurrencyTypeName(currencyNamesByNums.get(agentContractAccount.getFkCurrencyTypeNum()));
            map.put(agentContractAccountVo.getId(), agentContractAccountVo);
        }
        return map;
    }

    @Override
    public AgentContractAccount getFirstAgentContractAccount(Long agentId) {
        return accountMapper.getFirstAgentContractAccountByAgentId(agentId);
    }

    /**
     * feign调用，获取代理账户名称
     *
     * @param id
     * @return
     */
    @Override
    public String getAgentContractBankAccountNameById(Long id) {
        return accountMapper.getAgentContractBankAccountNameById(id);
    }

    @Override
    public String getAgentContractAccountExist(Long id, Long companyId, String bankAccount, String bankAccountNum) {
        List<AgentContractAccountVo> bankAccountList = null;
        StringBuilder sb = new StringBuilder();
        if (GeneralTool.isNotEmpty(bankAccount)) {
            bankAccountList = accountMapper.getAgentContractAccountExist(companyId, bankAccount, null);
        }
        List<AgentContractAccountVo> banNum = null;
        if (GeneralTool.isNotEmpty(bankAccountNum)) {
            banNum = accountMapper.getAgentContractAccountExist(companyId, null, bankAccountNum);
        }

        if (GeneralTool.isNotEmpty(bankAccountList)) {
            if (GeneralTool.isNotEmpty(id)) {
                bankAccountList.stream().filter(d -> !d.getId().equals(id)).forEach(dd -> sb.append(LocaleMessageUtils.getMessage("AGENT")).append("：")
                        .append(dd.getFkAgentName()).append("，").append(LocaleMessageUtils.getMessage("AGENT_ACCOUNT_NAME")).append("：").append(dd.getBankAccount())
                        .append("，").append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！<br/>"));

            } else {
                bankAccountList.stream().forEach(dd -> sb.append(LocaleMessageUtils.getMessage("AGENT")).append("：")
                        .append(dd.getFkAgentName()).append("，").append(LocaleMessageUtils.getMessage("AGENT_ACCOUNT_NAME")).append("：").append(dd.getBankAccount())
                        .append("，").append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！<br/>"));
            }
        }
        if (GeneralTool.isNotEmpty(banNum)) {
            if (GeneralTool.isNotEmpty(id)) {
                banNum.stream().filter(d -> !d.getId().equals(id)).forEach(dd -> sb.append(LocaleMessageUtils.getMessage("AGENT")).append("：")
                        .append(dd.getFkAgentName()).append("，").append(LocaleMessageUtils.getMessage("AGENT_ACCOUNT_NO")).append("：").append(dd.getBankAccountNum())
                        .append("，").append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！<br/>"));
            } else {
                banNum.stream().forEach(dd -> sb.append(LocaleMessageUtils.getMessage("AGENT")).append("：")
                        .append(dd.getFkAgentName()).append("，").append(LocaleMessageUtils.getMessage("AGENT_ACCOUNT_NO")).append("：").append(dd.getBankAccountNum())
                        .append("，").append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！<br/>"));
            }
        }
        return sb.toString();


    }

    /**
     * 快捷设置首选合同
     * @param agentId
     * @return
     */
    @Override
    public ResponseBo<Void> quickFirstContractAccount(Long agentId,Long accountId) {
        List<AgentContractAccount> accounts = accountMapper.selectList(Wrappers.<AgentContractAccount>lambdaQuery().eq(AgentContractAccount::getFkAgentId, agentId));
        List<AgentContractAccount> updateList = new ArrayList<>();
        if (GeneralTool.isNotEmpty(accounts)) {
            for (AgentContractAccount account : accounts) {
                if (account.getIsDefault()!=null && account.getIsDefault()) {
                    account.setIsDefault(false);
                    updateList.add(account);
                }
                if (accountId.equals(account.getId())) {
                    account.setIsDefault(true);
                    updateList.add(account);
                }
            }
            updateBatchById(updateList);
        }
        return new ResponseBo<>();
    }

    /**
     * Author Cream
     * Description : //快速激活或屏蔽合同账户
     * Date 2022/12/5 11:57
     * Params:  status： true:激活 false:屏蔽
     * Return
     */
    @Override
    public ResponseBo<Void> quickActivationOrMask(Long agentId, Long accountId, Boolean status) {
        AgentContractAccount account = accountMapper.selectById(accountId);
        if (account!=null) {
            List<AgentContractAccount> accounts = accountMapper.selectList(Wrappers.<AgentContractAccount>lambdaQuery()
                    .eq(AgentContractAccount::getFkAgentId, agentId)
                    .eq(AgentContractAccount::getFkCurrencyTypeNum,account.getFkCurrencyTypeNum()));
            List<AgentContractAccount> updateList = new ArrayList<>();
            if (GeneralTool.isNotEmpty(accounts)) {
                if (status) {
                    for (AgentContractAccount contractAccount : accounts) {
                        if (contractAccount.getIsActive()!=null && contractAccount.getIsActive()) {
                            contractAccount.setIsActive(false);
                            updateList.add(contractAccount);
                        }
                        if (accountId.equals(contractAccount.getId())) {
                            contractAccount.setIsActive(true);
                            updateList.add(contractAccount);
                        }
                    }
                    updateBatchById(updateList);
                }else {
                    account.setIsActive(false);
                    accountMapper.updateById(account);
                }
            }
        }
        return new ResponseBo<>();
    }

    @Override
    public Map<Long, Object> getAgentContractPersonMobileByAgentId(Set<Long> agentIds) {
        if (agentIds.isEmpty()) {
            return Collections.emptyMap();
        }
        List<SelItem> selItems = accountMapper.getAgentContractPersonMobile(agentIds);
        return ConvertUtils.convert(selItems);
    }

    /**
     * 获取代理合同
     * @param fkBankAccountId
     * @param fkTypeKey
     * @return
     */
    @Override
    public String getContractBankAccountNameById(Long fkBankAccountId, String fkTypeKey) {
        if (TableEnum.SALE_AGENT.key.equals(fkTypeKey)) {
            return accountMapper.getAgentContractBankAccountNameById(fkBankAccountId);
        }else if(TableEnum.INSTITUTION_PROVIDER.key.equals(fkTypeKey)){
            return iInstitutionCenterClient.getInstitutionProviderAccountById(fkBankAccountId);
        }else if (TableEnum.SALE_BUSINESS_PROVIDER.key.equals(fkTypeKey)){
            return businessProviderAccountService.getBusinessProviderBankAccountNameById(fkBankAccountId);
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<MediaAndAttachedVo> addMedia(List<MediaAndAttachedDto> mediaAndAttachedDtos) {
        if (GeneralTool.isEmpty(mediaAndAttachedDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAndAttachedDtos) {
            if (GeneralTool.isEmpty(mediaAndAttachedDto.getFkTableId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("tableId_null"));
            }
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.AGENT_CONTRACT_ACCOUNT.key);
            mediaAndAttachedVos.add(mediaAndAttachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }
}
