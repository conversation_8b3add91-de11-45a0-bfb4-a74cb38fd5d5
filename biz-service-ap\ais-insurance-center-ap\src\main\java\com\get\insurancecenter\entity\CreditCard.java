package com.get.insurancecenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/7/25
 * @Version 1.0
 * @apiNote:
 */
@Data
@TableName("m_credit_card")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CreditCard extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "信用卡类型：Visa/Master/UnionPay")
    @NotBlank(message = "信用卡类型不能为空")
    private String cardTypeKey;

    @ApiModelProperty(value = "信用卡号-加密")
    private String cardNum;

    @ApiModelProperty(value = "信用卡安全码-加密")
    private String safetyCode;

    @ApiModelProperty(value = "持卡人姓名")
    @NotBlank(message = "持卡人姓名不能为空")
    private String holderName;

    @ApiModelProperty(value = "持卡人电话")
    @NotBlank(message = "持卡人电话不能为空")
    private String holderTel;

    @ApiModelProperty(value = "持卡人电邮")
    @NotBlank(message = "持卡人邮箱不能为空")
    private String holderEmail;

    @DateTimeFormat(pattern = "yyyy-MM")
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    @ApiModelProperty(value = "有效期（年月）")
    @NotNull(message = "有效期不能为空")
    private Date expirationDate;

    @ApiModelProperty(value = "发卡银行Id")
    @NotNull(message = "发卡银行Id不能为空")
    private Long fkBankId;

    @ApiModelProperty(value = "银行地址")
    private String bankAddress;

    @ApiModelProperty(value = "额度币种")
    @NotBlank(message = "额度币种不能为空")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "额度金额")
    @NotNull(message = "额度金额不能为空")
    private BigDecimal quotaAmount;

    @ApiModelProperty(value = "当前金额/额度")
    private BigDecimal currentAmount;

    @ApiModelProperty(value = "账单日")
    @NotNull(message = "账单日不能为空")
    private Integer statementDate;

    @ApiModelProperty(value = "还款日")
    @NotNull(message = "还款日不能为空")
    private Integer paymentDate;

    @ApiModelProperty(value = "是否激活：0否/1是")
    @NotNull(message = "是否激活不能为空")
    private Integer isActive;

    @ApiModelProperty(value = "排序-越大扣款顺序越靠前")
    private Integer viewOrder;

    @TableField(exist = false)
    @ApiModelProperty(value = "信用卡号-明文")
    private String cardNumPlaintext;

    @TableField(exist = false)
    @ApiModelProperty(value = "信用卡安全码-明文")
    private String safetyCodePlaintext;

    @TableField(exist = false)
    @ApiModelProperty(value = "信用卡号-明文脱敏后的")
    private String desensitizationCardNumPlaintext;

    @TableField(exist = false)
    @ApiModelProperty(value = "信用卡安全码-明文脱敏后的")
    private String desensitizationSafetyCodePlaintext;

    @TableField(exist = false)
    @ApiModelProperty(value = "信用卡号-base64加密后")
    @NotBlank(message = "信用卡号-base64加密不能为空")
    private String base64CardNum;

    @TableField(exist = false)
    @ApiModelProperty(value = "信用卡安全码-base64加密后")
    @NotBlank(message = "信用卡安全码-base64加密不能为空")
    private String base64SafetyCode;

    @TableField(exist = false)
    @ApiModelProperty(value = "发卡行")
    private String bankName;

}
