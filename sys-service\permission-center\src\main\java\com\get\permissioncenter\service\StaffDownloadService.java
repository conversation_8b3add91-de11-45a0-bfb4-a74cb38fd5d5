package com.get.permissioncenter.service;

import com.get.common.eunms.FileTypeEnum;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.permissioncenter.vo.StaffDownloadVo;
import com.get.permissioncenter.entity.StaffDownload;
import com.get.permissioncenter.dto.StaffDownloadDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface StaffDownloadService {

    /**
     * 新增下载记录
     * @param staffDownload
     */
    Long addRecord(StaffDownload staffDownload);


    /**
     * 更新记录状态
     * @param staffDownload
     */
    void update(StaffDownload staffDownload);

    /**
     * 获取下载列表
     * @param page
     * @return
     */
    List<StaffDownloadVo> getDownloadList(SearchBean<StaffDownloadDto> page);



    /**
     * 获取下拉
     * @return
     */
    List<Map<String, Object>> doGetSelection(FileTypeEnum[] fileTypeEnums);

    /**
     * 删除单条记录
     * @param id
     * @return
     */
    ResponseBo deleteRecord(Long id);


    ResponseBo deleteAll();
}
