package com.get.common.result;

import lombok.Data;

import javax.validation.Valid;

/**
 * @description: 基础搜索类
 * @author: jack
 * @create: 2020-04-16
 */
@Data
public class SearchBean<T> extends Page {

    /* *//**
     * 模糊查询匹配字段
     *//*
    private String search;

    *//**
     * 查询分类
     *//*
    private String sel;
    *//**
     * 查询关键字
     *//*
    private String key;

    *//**
     * 状态
     * 督办通报中的状态。
     * 月报中填报状态
     *//*
    private Integer status;


    *//**
     * 通用类型字段
     *//*
    private String type;*/
    /**
     * 查询参数泛型
     */
    @Valid
    private T data;

    /**
     * 获取分页参数
     * @return
     */
/*    public Page getPage(){
        Page page = new Page();
        page.setCurrentPage(this.getCurrentPage());
        page.setCurrentResult(this.getCurrentResult());
        page.setShowCount(this.getShowCount());
        page.setTotalResult(this.getTotalResult());
        page.setTotalPage(this.getTotalPage());
        return page;
    }*/

   /* public String getLeftLikeSearch(){
        return "%"+this.search;
    }
    public String getRightLikeSearch(){
        return this.search+"%";
    }
    public String getAllLikeSearch(){
        return "%"+this.search+"%";
    }*/
}
