package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/2/17 10:42
 * @verison: 1.0
 * @description:
 */
@Data
public class CommissionStaffDatasVo {

    @ApiModelProperty("员工id")
    private Long fkStaffId;

    @ApiModelProperty("员工名称")
    private String fkStaffName;

    @ApiModelProperty("角色")
    private String fkStudentProjectRoleIds;

    @ApiModelProperty("角色Key")
    private List<String> fkStudentProjectRoleKeys;

    @ApiModelProperty("角色名称")
    private String fkStudentProjectRoleName;

    @ApiModelProperty("员工结算数据")
    private List<CommissionStaffDatasItemVo> commissionStaffDatasItemDtos;

//    @ApiModelProperty("结算")
//    private List<StaffCommissionDatasVo<BigDecimal>> sumCommissionData;
}
