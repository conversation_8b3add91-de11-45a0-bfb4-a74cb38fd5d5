package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.vo.*;
import com.get.institutioncenter.entity.BaseSelectEntityPlus;
import com.get.institutioncenter.service.IInstitutionProviderService;
import com.get.institutioncenter.service.IInstitutionService;
import com.get.institutioncenter.dto.*;
import com.get.institutioncenter.dto.query.InstitutionQueryDto;
import com.get.institutioncenter.dto.query.InstitutionZoneQueryDto;
import com.get.institutioncenter.dto.query.NewsQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @author: Sea
 * @create: 2020/7/13 15:36
 * @verison: 1.0
 * @description: 学校管理
 */

@Api(tags = "学校管理")
@RestController
@RequestMapping("/institution/institution")
public class InstitutionController {
    @Resource
    private IInstitutionService institutionService;
    @Resource
    private IInstitutionProviderService institutionProviderService;

    /**
     * 学校下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学校下拉框数据", notes = "")
    @GetMapping("getInstitutionList")
    public ResponseBo<BaseSelectEntity> getInstitutionList() {
        List<BaseSelectEntity> datas = institutionService.getInstitutionList();
        return new ListResponseBo<>(datas);
    }

    /**
     * 根据国家获取学校下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据国家获取学校下拉框数据", notes = "")
    @GetMapping("getInstitutionListByCountryId/{id}")
    public ResponseBo<BaseSelectEntity> getInstitutionListByCountryId(@PathVariable("id") Long countryId) {
        List<BaseSelectEntity> datas = institutionService.getInstitutionListByCountryId(countryId);
        return new ListResponseBo<>(datas);
    }

    /**
     * 根据国家（多选）获取学校下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据国家（多选）获取学校下拉框数据", notes = "")
    @PostMapping("getInstitutionListByCountryIdList")
    public ResponseBo<BaseSelectEntity> getInstitutionListByCountryIdList(@RequestBody List<Long> fkCountryIdList) {
        List<BaseSelectEntity> datas = institutionService.getInstitutionListByCountryIdList(fkCountryIdList);
        return new ListResponseBo<>(datas);
    }

    /**
     * 根据提供商id查询学校下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据提供商id查询学校下拉框数据", notes = "")
    @GetMapping("getInstitutionListByProviderId")
    public ResponseBo<BaseSelectEntity> getInstitutionListByProviderId(@RequestParam(value = "providerId") Long providerId) {
        List<BaseSelectEntity> datas = institutionService.getInstitutionListByProviderId(providerId);
        return new ListResponseBo<>(datas);
    }


    /**
     * feign调用 根据输入的学校名称 模糊查询对应的学校ids
     *
     * @param institutionName
     * @return
     * @
     */
 /*   @ApiIgnore
    @GetMapping(value = "getInstitutionIds")
    public List<Long> getInstitutionIds(@RequestParam String institutionName)  {
        return institutionService.getInstitutionIdsByName(institutionName);
    }*/

    /**
     * feign调用 根据学校id查找学校名称
     *
     * @param id
     * @return
     * @
     */
  /*  @ApiIgnore
    @GetMapping(value = "getInstitutionName")
    public String getInstitutionName(@RequestParam(required = false) Long id)  {
        return institutionService.getInstitutionNameById(id);
    }*/

    /**
     * 根据输入的学校名称 模糊查询对应的学校ids
     *
     * @param institutionName
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @GetMapping(value = "getInstitutionIdsByName")
    public ResponseBo getInstitutionIdsByName(@RequestParam String institutionName) {
        return new ListResponseBo<>(institutionService.getInstitutionIdsByNames(institutionName));
    }

    /**
     * 根据输入的学校名称 模糊查询对应的学校ids和学校名称
     *
     * @param institutionByNameDto
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据输入关键字模糊查询学校列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校管理/根据输入关键字模糊查询学校列表")
    @PostMapping(value = "getInstitutionByName")
    public ResponseBo getInstitutionByName(@RequestBody InstitutionByNameDto institutionByNameDto) {
        return new ListResponseBo<>(institutionService.getInstitutionByName(institutionByNameDto));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "模糊查询学校列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校管理/模糊查询学校列表")
    @PostMapping(value = "getInstitutionListByKeyword")
    public ResponseBo getInstitutionListByKeyword(@RequestParam("keyword") String keyword){
        return new ListResponseBo<>(institutionService.getInstitutionListByKeyword(keyword));
    }

    /**
     * 新增信息
     *
     * @param institutionDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/学校管理/新增学校")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(InstitutionDto.Add.class) InstitutionDto institutionDto) {
        return SaveResponseBo.ok(institutionService.addInstitution(institutionDto));
    }

    /**
     * 修改信息
     *
     * @param institutionDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/学校管理/更新学校")
    @PostMapping("update")
    public ResponseBo<InstitutionVo> update(@RequestBody @Validated(InstitutionDto.Update.class) InstitutionDto institutionDto) {
        return UpdateResponseBo.ok(institutionService.updateInstitution(institutionDto));
    }


    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校管理/查询学校")
    @PostMapping("datas")
    public ResponseBo<InstitutionVo> datas(@RequestBody SearchBean<InstitutionQueryDto> page) {
        List<InstitutionVo> datas = institutionService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "校区列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校管理/查询校区")
    @PostMapping("getInstitutionZoneDatas")
    public ResponseBo<InstitutionZoneVo> getInstitutionZoneDatas(@RequestBody SearchBean<InstitutionZoneQueryDto> page) {
        List<InstitutionZoneVo> datas = institutionService.getInstitutionZoneData(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/学校管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<InstitutionVo> detail(@PathVariable("id") Long id) {
        InstitutionVo institutionVo = institutionService.findInstitutionById(id);
        return new ResponseBo<>(institutionVo);
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/学校管理/删除学校")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        institutionService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增学校联系人
     * @Param [contactPersonDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增学校联系人")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/学校管理/新增学校联系人")
    @PostMapping(value = "addContactPerson")
    public ResponseBo addContactPerson(@RequestBody @Validated(ContactPersonDto.Add.class) ContactPersonDto contactPersonDto) {
        return SaveResponseBo.ok(institutionService.addContactPerson(contactPersonDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 学校联系人列表
     * @Param [searchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "学校联系人列表")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校管理/代理联系人列表")
    @PostMapping("getAgentContactPerson")
    public ResponseBo<ContactPersonVo> getAgentContactPerson(@RequestBody SearchBean<ContactPersonDto> searchBean) {
        List<ContactPersonVo> contactPersonVos = institutionService.getContactPersonDtos(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(contactPersonVos, p);
    }

    /**
     * 查询学校附件
     *
     * @param voSearchBean
     * @return
     * @
     */
    @ApiOperation(value = "查询学校附件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校管理/查询学校附件")
    @PostMapping("getInstitutionMedia")
    public ResponseBo<MediaAndAttachedVo> getInstitutionMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> institutionMedia = institutionService.getInstitutionMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(institutionMedia, page);
    }

    /**
     * 保存学校附件接口
     *
     * @param mediaAttachedVo
     * @return
     * @
     */

    @ApiOperation(value = "保存学校附件接口")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/学校管理/保存学校附件接口")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> upload(@RequestBody @Validated(MediaAndAttachedDto.Add.class) List<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(institutionService.addInstitutionMedia(mediaAttachedVo));
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.InstitutionProviderVo>
     * @Description: 学校提供商列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "学校提供商列表数据")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校管理/学校提供商列表数据")
    @PostMapping("getInstitutionProviderDatas")
    public ResponseBo<InstitutionProviderVo> getInstitutionProviderDatas(@RequestBody SearchBean<InstitutionProviderDto> page) {
        List<InstitutionProviderVo> datas = institutionService.getProviderList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 回显学校-提供商关系
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "回显学校-学校提供商关系", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/配置学校提供商/查询学校提供商")
    @PostMapping(value = "getInstitutionProviders")
    public ResponseBo<InstitutionProviderVo> getInstitutionProviders(@RequestBody SearchBean<InstitutionProviderDto> page) {
        List<InstitutionProviderVo> data = institutionProviderService.getInstitutionProviders(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(data, p);
    }

    /**
     * 配置学校-提供商
     *
     * @param institutionDto
     * @return
     * @
     */
    @ApiOperation(value = "配置学校-提供商关系", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/配置学校提供商/提交选中学校提供商")
    @PostMapping(value = "addInstitutionProviderInstitution")
    public ResponseBo addInstitutionProviderInstitution(@RequestBody InstitutionDto institutionDto) {
        institutionService.addInstitutionProviderInstitution(institutionDto);
        return UpdateResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增学校新闻
     * @Param [newsDto]
     * <AUTHOR>
     **/
    @ApiOperation(value = "新增学校新闻接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/学校管理/新增新闻")
    @PostMapping("addNews")
    public ResponseBo addNews(@RequestBody @Validated(NewsDto.Add.class) NewsDto newsDto) {
        return SaveResponseBo.ok(institutionService.addNews(newsDto));
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.NewsVo>
     * @Description: 学校新闻列表数据
     * @Param [page]
     * <AUTHOR>
     **/
    @ApiOperation(value = "学校新闻列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校管理/查询新闻")
    @PostMapping("getNewsDatas")
    public ResponseBo<NewsVo> getNewsDatas(@RequestBody SearchBean<NewsQueryDto> page) {
        List<NewsVo> datas = institutionService.getNewsData(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * 学校下拉框数据
     *
     * @Date 16:12 2021/7/22
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学校下拉框数据", notes = "")
    @GetMapping("getInstitutionSelect/{id}")
    public ResponseBo<BaseSelectEntity> getInstitutionSelect(@PathVariable("id") Long id) {
        List<BaseSelectEntity> datas = institutionService.getInstitutionSelect(id);
        return new ListResponseBo<>(datas);
    }

    /**
     * 学校桥梁学校下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学校桥梁学校下拉框数据", notes = "")
    @GetMapping("getBridgeInstitutionSelect/{id}")
    public ResponseBo<BaseSelectEntity> getBridgeInstitutionSelect(@PathVariable("id") Long id) {
        List<BaseSelectEntity> datas = institutionService.getBridgeInstitutionSelect(id);
        return new ListResponseBo<>(datas);
    }

    /**
     * 非桥梁学校下拉框数据
     *
     * @Date 16:12 2021/7/22
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学校桥梁学校下拉框数据", notes = "")
    @GetMapping("getNonBridgeInstitutionSelect/{id}")
    public ResponseBo<BaseSelectEntity> getNonBridgeInstitutionSelect(@PathVariable("id") Long id) {
        List<BaseSelectEntity> datas = institutionService.getNonBridgeInstitutionSelect(id);
        return new ListResponseBo<>(datas);
    }

    /**
     * 课程 非桥梁学校下拉框数据
     *
     * @Date 18:00 2021/7/27
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "课程 非桥梁学校下拉框数据", notes = "")
    @GetMapping("getNonBridgeInstitutionSelectByCourse/{id}")
    public ResponseBo<BaseSelectEntity> getNonBridgeInstitutionSelectByCourse(@PathVariable("id") Long id) {
        List<BaseSelectEntity> datas = institutionService.getNonBridgeInstitutionSelectByCourse(id);
        return new ListResponseBo<>(datas);
    }



    /**
     * 课程桥梁学校下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "课程桥梁学校下拉框数据", notes = "")
    @GetMapping("getBridgeInstitutionSelectByCourse")
    public ResponseBo<BaseSelectEntity> getBridgeInstitutionSelectByCourse(@RequestParam("institutionId") Long institutionId) {
        List<BaseSelectEntity> datas = institutionService.getBridgeInstitutionSelectByCourse(institutionId);
        return new ListResponseBo<>(datas);
    }


    /**
     * 排名列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "特性列表数据", notes = "")
    @PostMapping("getCharacterDatas")
    public ResponseBo<CharacterVo> getCharacterDatas(@RequestBody SearchBean<CharacterDto> page) {
        List<CharacterVo> datas = institutionService.getCharacterDatas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 批量新增信息
     *
     * @param resources
     * @return
     * @
     */
    @ApiOperation(value = "批量新增特性接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/学校管理/批量保存")
    @PostMapping("batchAddCharacter")
    public ResponseBo batchAddCharacter(@RequestBody @Validated(CharacterDto.Add.class) ValidList<CharacterDto> resources) {
        institutionService.batchAddCharacter(resources);
        return ResponseBo.ok();
    }

    /**
     * @Description :feign调用 根据学校ids 查找对应名称map
     * @Param [institutionIdSet]
     * <AUTHOR>
     */
 /*   @PostMapping(value = "getInstitutionNamesByIds")
    @VerifyLogin(IsVerify = false)
    public Map<Long, String> getInstitutionNamesByIds(@RequestBody Set<Long> institutionIdSet)  {
        return institutionService.getInstitutionNamesByIds(institutionIdSet);
    }*/

    /**
     * feign调用 根据学校id查找学校名字
     *
     * @Date 16:19 2021/5/24
     * <AUTHOR>
     */
/*    @ApiIgnore
    @PostMapping(value = "getInstitutionNamesById")
    public String getInstitutionNamesById(@RequestParam("id") Long id)  {
        return institutionService.getInstitutionNamesById(id);
    }*/

    /**
     * feign调用 根据学校ids查找国家名字
     *
     * @Date 10:29 2021/8/26
     * <AUTHOR>
     */
/*    @ApiIgnore
    @PostMapping(value = "getCountryNamesByInstitutionIds")
    public Map<Long, String>  getCountryNamesByInstitutionIds(@RequestBody Set<Long> institutionIds)  {
        return institutionService.getCountryNamesByInstitutionIds(institutionIds);
    }*/

    /**
     * feign调用 根据学校id查找学校国家
     *
     * @Date 18:08 2021/7/13
     * <AUTHOR>
     */
  /*  @ApiIgnore
    @PostMapping(value = "getCountryIdByInstitutionId")
    public Map<Long, Long> getCountryIdByInstitutionId(@RequestBody Set<Long> institutionIdSet)  {
        return institutionService.getCountryIdByInstitutionId(institutionIdSet);
    }*/

    /**
     * KPI推荐等级下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "KPI推荐等级下拉框数据", notes = "")
    @GetMapping("getKpiLevelSelect")
    public ResponseBo getKpiLevelSelect() {
        return new ListResponseBo<>(ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.KEI_LEVEL));
    }


    /**
     * 数据采集等级下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "数据采集等级下拉框数据", notes = "")
    @GetMapping("getDataLevelSelect")
    public ResponseBo getDataLevelSelect() {
        List<Map<String, Object>> datas = institutionService.getDataLevelSelect();
        return new ListResponseBo<>(datas);
    }

    /**
     * 根据学校id获取学校国家下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据学校id获取学校国家下拉框数据", notes = "")
    @GetMapping("getCountryByInstitutionIdSelect/{id}")
    public ResponseBo<BaseSelectEntity> getCountryByInstitutionIdSelect(@PathVariable("id") Long id) {
        List<BaseSelectEntity> datas = institutionService.getCountryByInstitutionIdSelect(id);
        return new ListResponseBo<>(datas);
    }

    /**
     * 已绑定提供商学校下拉框数据
     *
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "已绑定提供商学校下拉框数据", notes = "")
    @GetMapping("getProviderInstitutionList")
    public ResponseBo<BaseSelectEntityPlus> getProviderInstitutionList(@RequestParam("countryId") Long countryId) {
        List<BaseSelectEntityPlus> datas = institutionService.getProviderInstitutionList(countryId);
        return new ListResponseBo<>(datas);
    }

    /**
     * 学校下拉框数据包括简称
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学校下拉框数据包括简称", notes = "")
    @GetMapping("getInstitutionSfList")
    public ResponseBo<BaseSelectEntity> getInstitutionSfList() {
        List<BaseSelectEntity> datas = institutionService.getInstitutionSfList();
        return new ListResponseBo<>(datas);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "有新闻数据的 学校下拉框数据包括简称", notes = "")
    @GetMapping("getNewsInstitutionSfList")
    public ResponseBo<BaseSelectEntity> getNewsInstitutionSfList() {
        List<BaseSelectEntity> datas = institutionService.getNewsInstitutionSfList();
        return new ListResponseBo<>(datas);
    }


    @ApiOperation(value = "AI获取学校信息", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "AI获取学校信息")
    @PostMapping("getAiInstitutionInfo")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<AiInstitutionVo> getAiInstitutionInfo(@RequestBody AiInstitutionDto aiInstitutionDto) {
        List<AiInstitutionVo> aiInstitutionVoList = institutionService.getAiInstitutionInfo(aiInstitutionDto);
        return new ListResponseBo<>(aiInstitutionVoList);
    }

}
