package com.get.salecenter.strategy.config;

import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.strategy.ClientSourceStrategy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2024/1/31 17:17
 * @verison: 1.0
 * @description:
 */
@Configuration
public class ClientSourceServiceConfig {

    @Resource
    private List<ClientSourceStrategy> clientSourceStrategies;

    @Bean("clientSourceStrategiesMap")
    public Map<String, ClientSourceStrategy> getClientSourceStrategiesMap() {
        if (GeneralTool.isEmpty(clientSourceStrategies)){
            return Collections.emptyMap();
        }
        return clientSourceStrategies.stream().collect(Collectors.toMap(ClientSourceStrategy::getType, Function.identity()));
    }

}
