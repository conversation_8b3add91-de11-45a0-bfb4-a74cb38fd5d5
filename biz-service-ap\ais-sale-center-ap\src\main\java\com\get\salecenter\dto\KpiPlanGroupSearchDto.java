package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 过滤条件
 */
@Data
public class KpiPlanGroupSearchDto {
    @ApiModelProperty(value = "K<PERSON>方案ID")
    @NotNull(message = "K<PERSON>方案ID不能为空")
    private Long fkKpiPlanId;

//    @ApiModelProperty(value = "国家/地区列表")
//    private Set<Long> fkAreaCountryIdList;

    @ApiModelProperty(value = "根节点员工Id（以其为根节点维度）")
    @NotNull(message = "根节点员工Id不能为空")
    private Long rootFkStaffId;
}
