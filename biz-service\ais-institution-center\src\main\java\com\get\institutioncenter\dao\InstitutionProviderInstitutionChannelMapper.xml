<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionProviderInstitutionChannelMapper">
  <insert id="insert" parameterType="com.get.institutioncenter.entity.InstitutionProviderInstitutionChannel">
    insert into r_institution_provider_institution_channel (id, fk_institution_provider_id, fk_institution_channel_id, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkInstitutionProviderId,jdbcType=BIGINT}, #{fkInstitutionChannelId,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionProviderInstitutionChannel" keyProperty="id" useGeneratedKeys="true">
    insert into r_institution_provider_institution_channel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkInstitutionProviderId != null">
        fk_institution_provider_id,
      </if>
      <if test="fkInstitutionChannelId != null">
        fk_institution_channel_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionProviderId != null">
        #{fkInstitutionProviderId,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionChannelId != null">
        #{fkInstitutionChannelId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.get.institutioncenter.entity.InstitutionProviderInstitutionChannel">
    update r_institution_provider_institution_channel
    <set>
      <if test="fkInstitutionProviderId != null">
        fk_institution_provider_id = #{fkInstitutionProviderId,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionChannelId != null">
        fk_institution_channel_id = #{fkInstitutionChannelId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.institutioncenter.entity.InstitutionProviderInstitutionChannel">
    update r_institution_provider_institution_channel
    set fk_institution_provider_id = #{fkInstitutionProviderId,jdbcType=BIGINT},
      fk_institution_channel_id = #{fkInstitutionChannelId,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
    <select id="getInstitutionChannelNamesById" resultType="java.lang.String">
      SELECT group_concat(DISTINCT c.name separator '，') channelNames
      FROM  r_institution_provider_institution_channel p
          LEFT join m_institution_channel c on p.fk_institution_channel_id=c.id
          LEFT join r_institution_channel_company d on d.fk_institution_channel_id=c.id
      where p.fk_institution_provider_id=#{id}
            and d.fk_company_id in
            <foreach collection="companyIds" item="companyId" index="index" open="(" separator="," close=")">
              #{companyId}
            </foreach>
    </select>
</mapper>