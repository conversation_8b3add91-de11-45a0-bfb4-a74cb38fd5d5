package com.get.workflowcenter.listener;

import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.SpringUtil;
import com.get.financecenter.entity.PaymentApplicationForm;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.financecenter.vo.PaymentApplicationFormVo;
import com.get.permissioncenter.vo.StaffVo;
import com.get.workflowcenter.component.IWorkFlowHelper;
import com.get.workflowcenter.entity.WorkFlowPaymentApplicationForm;
import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;
import org.activiti.engine.HistoryService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.repository.ProcessDefinition;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/1/8 11:52
 */

public class PayListener implements Serializable, ExecutionListener {
    @Override
    public void notify(DelegateExecution delegateExecution) {
        IWorkFlowHelper workFlowHelper = SpringUtil.getBean(IWorkFlowHelper.class);
        //获取流程名称
        ProcessEngine processEngine = SpringUtil.getBean(ProcessEngine.class);
        ProcessDefinition processDefinition = processEngine.getRepositoryService()
                .createProcessDefinitionQuery()
                .processDefinitionId(delegateExecution.getProcessDefinitionId())
                .singleResult();
        StaffVo staffVo = workFlowHelper.getExecutionStaffDto(delegateExecution);

        String processInstanceBusinessKey = delegateExecution.getProcessInstanceBusinessKey();
        String processInstanceId = delegateExecution.getProcessInstanceId();
        HistoryService historyService = SpringUtil.getBean(HistoryService.class);
        IFinanceCenterClient feginFinanceService = SpringUtil.getBean(IFinanceCenterClient.class);
        UtilService utilService = SpringUtil.getBean(UtilService.class);
        if ("end".equals(delegateExecution.getEventName())) {
            List<HistoricActivityInstance> list = historyService.createHistoricActivityInstanceQuery().processInstanceId(processInstanceId).orderByHistoricActivityInstanceStartTime().desc().list();
            Result<PaymentApplicationFormVo> paymentApplicationFormDtoResult = feginFinanceService.getMpayById(Long.valueOf(processInstanceBusinessKey));
            if (paymentApplicationFormDtoResult.isSuccess() && GeneralTool.isNotEmpty(paymentApplicationFormDtoResult.getData())) {
                PaymentApplicationFormVo paymentApplicationFormVo = paymentApplicationFormDtoResult.getData();
                WorkFlowPaymentApplicationForm paymentApplicationForm = BeanCopyUtils.objClone(paymentApplicationFormVo, WorkFlowPaymentApplicationForm::new);
                StringJoiner stringJoiner = new StringJoiner(",");
                stringJoiner.add(ProjectKeyEnum.WORKFLOW_CENTER.key).add(ProjectKeyEnum.WORKFLOW_CENTER_TO_DO.key);
                for (HistoricActivityInstance hti : list) {
                    if (hti.getActivityName() != null && "资料有误重新申请".equals(hti.getActivityName())) {
                        utilService.updateUserInfoToEntity(paymentApplicationForm);
                        paymentApplicationForm.setStatus(4);
                        break;
                    } else {
                        utilService.updateUserInfoToEntity(paymentApplicationForm);
                        paymentApplicationForm.setStatus(1);
                        workFlowHelper.sendMessage(staffVo, null, processDefinition, "审批通过", delegateExecution, stringJoiner.toString(),null);
                        break;
                    }
                }
                PaymentApplicationForm payForm = BeanCopyUtils.objClone(paymentApplicationForm, PaymentApplicationForm::new);
                feginFinanceService.updateMpay(payForm);
            }

        } else {
            Object sequenceFlowsStatus = delegateExecution.getVariable("sequenceFlowsStatus");
            Result<PaymentApplicationFormVo> result = feginFinanceService.getMpayById(Long.valueOf(processInstanceBusinessKey));
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                PaymentApplicationFormVo paymentApplicationFormVo = result.getData();
                WorkFlowPaymentApplicationForm paymentApplicationForm = BeanCopyUtils.objClone(paymentApplicationFormVo, WorkFlowPaymentApplicationForm::new);
                if ("0".equals(sequenceFlowsStatus)) {
                    utilService.updateUserInfoToEntity(paymentApplicationForm);
                    paymentApplicationForm.setStatus(3);
                } else {
                    utilService.updateUserInfoToEntity(paymentApplicationForm);
                    paymentApplicationForm.setStatus(2);
                }
                PaymentApplicationForm paymentApplicationForm_ = BeanCopyUtils.objClone(paymentApplicationForm, PaymentApplicationForm::new);
                feginFinanceService.updateMpay(paymentApplicationForm_);
            }
        }
    }
}


