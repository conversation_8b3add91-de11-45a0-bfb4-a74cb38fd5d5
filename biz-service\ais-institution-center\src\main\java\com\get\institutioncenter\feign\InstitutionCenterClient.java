package com.get.institutioncenter.feign;

import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
 
 import com.get.institutioncenter.bo.InstitutionApplicationStaticsQueryBo;
import com.get.institutioncenter.dao.InstitutionChannelMapper;
import com.get.institutioncenter.dao.InstitutionRankingMapper;
import com.get.institutioncenter.dto.NewsEmailTmpeleteVo;
import com.get.institutioncenter.dto.query.InstitutionQueryDto;
import com.get.institutioncenter.service.*;
import com.get.institutioncenter.vo.*;
import com.get.institutioncenter.vo.AreaCityVo;
import com.get.institutioncenter.entity.AreaCountry;
import com.get.institutioncenter.entity.Contract;
import com.get.institutioncenter.entity.ContractFormula;
import com.get.institutioncenter.entity.Institution;
import com.get.institutioncenter.entity.MajorLevel;
import com.get.institutioncenter.dto.*;
import com.get.institutioncenter.dto.CaseStudyResultsDto;
import com.get.permissioncenter.vo.CompanyVo;
import com.get.remindercenter.dto.AliyunSendMailDto;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2021/12/30
 * @TIME: 10:20
 * @Description:
 **/
@RestController
@AllArgsConstructor
@VerifyPermission(IsVerify = false)
public class InstitutionCenterClient implements IInstitutionCenterClient {

    private final IAppInfoService appInfoService;
    @Resource
    private final IAreaCityService areaCityService;
    @Resource
    private final IAreaCityDivisionService areaCityDivisionService;
    @Resource
    private final IAreaCountryService areaCountryService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private final IAreaRegionService areaRegionService;
    @Resource
    private final IAreaRegionStateService areaRegionStateService;
    @Resource
    private final IAreaStateService areaStateService;
    @Resource
    private final IInstitutionCourseService institutionCourseService;
    @Autowired
    private final IContractService contractService;
    @Resource
    private final IContractFormulaService contractFormulaService;
    @Resource
    private final IContractFormulaPreInstitutionGroupService contractFormulaPreInstitutionGroupService;
    @Resource
    private final ICourseTypeService courseTypeService;
    @Resource
    private final IInstitutionChannelService institutionChannelService;
    @Resource
    private final IInstitutionService institutionService;
    @Resource
    private final IInstitutionProviderService institutionProviderService;
    @Resource
    private final InstitutionProviderAccountService institutionProviderAccountService;
    @Resource
    private final IInstitutionProviderCompanyService institutionProviderCompanyService;
    @Resource
    private final IMajorLevelService majorLevelService;
    @Resource
    private final INewsService newsService;
    @Resource
    private final INewsTypeService newsTypeService;
    @Resource
    private final InstitutionChannelMapper institutionChannelMapper;
    @Resource
    private final InstitutionRankingMapper institutionRankingMapper;
    @Resource
    private final IInstitutionZoneService institutionZoneService;
    @Resource
    private IInstitutionTypeService institutionTypeService;
    @Resource
    private ICourseTypeGroupService courseTypeGroupService;
    @Resource
    private AsyncCompletions asyncCompletions;
    @Resource
    private IInstitutionAppFeeService institutionAppFeeService;

    @Override
    public Integer updateContractStatus(Long id, Integer status) {
        return institutionProviderService.updateContractStatus(id,status);
    }

    @Override
    public Result<Integer> getRankingMaxYear() {
        return Result.data(institutionRankingMapper.getMaxYear());
    }

    @Override
    public Result<Map<Long, String>> getWebSiteByTable(String tableName, String typeKey) {
        return Result.data(appInfoService.getWebSiteByTable(tableName, typeKey));
    }

    @Override
    public Result<Map<Long, String>> getWebSiteByAppInfoFeignDto(AppInfoFeignVo appInfoFeignVo) {
        return Result.data(appInfoService.getWebSiteByAppInfoFeignDto(appInfoFeignVo));
    }

    @Override
    public Result<String> getCityNameById(Long id) {
        return Result.data(areaCityService.getCityNameById(id));
    }

    @Override
    public Result<Map<Long, String>> getCityNamesByIds(Set<Long> ids) {
        return Result.data(areaCityService.getCityNamesByIds(ids));
    }

    @Override
    public Result<Map<Long, String>> getCityNameChnsByIds(Set<Long> ids) {
        return Result.data(areaCityService.getCityNameChnsByIds(ids));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Map<Long, String>> getCityFullNamesByIds(Set<Long> ids) {
        return Result.data(areaCityService.getCityFullNamesByIds(ids));
    }

    @Override
    public Result<Map<Long, String>> getCityFullNames() {
        return Result.data(areaCityService.getCityFullNames());
    }

    @Override
    public Result<Map<Long, String>> getCityDivisionFullNamesByIds(Set<Long> ids) {
        return Result.data(areaCityDivisionService.getCityDivisionFullNamesByIds(ids));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<String> getCountryName(String countryKey) {
        return Result.data(areaCountryService.getCountryNameByKey(countryKey));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<String> getCountryNameEn(String countryKey) {
        return Result.data(areaCountryService.getCountryNameEnByKey(countryKey));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Set<Long>> getAllCountryId() {
        return Result.data(areaCountryService.getAllCountryId());
    }

    @Override
    @VerifyLogin(IsVerify = false)  //定时任务免登陆
    public Result<String> getCountryNameById(Long id) {
        return Result.data(areaCountryService.getCountryNameById(id));
    }

    @Override
    @VerifyLogin(IsVerify = false)  //定时任务免登陆
    public Result<AreaCountry> getCountryById(Long id) {
        return Result.data(areaCountryService.getCountryById(id));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<String> getCountryChnNameById(Long id) {
        return Result.data(areaCountryService.getCountryChnNameById(id));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Map<Long, String>> getCountryChnNameByIds(Set<Long> ids) {
        return Result.data(areaCountryService.getCountryChnNameByIds(ids));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<List<Long>> getCountryIdByKey(List<String> keys) {
        return Result.data(areaCountryService.getCountryIdByKey(keys));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Map<Long, String>> getCountryNamesByIds(Set<Long> ids) {
        return Result.data(areaCountryService.getCountryNamesByIds(ids));
    }

    @Override
    public Result<Map<Long, String>> getCountryNameMap() {
        return Result.data(areaCountryService.getCountryNameMap());
    }

//    @Override
//    public Result<Map<Long, String>> getCompanyNamesByIds(Set<Long> ids) {
//        return Result.data(companyService.getCompanyNamesByIds(ids));
//    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Map<Long, AreaRegionVo>> getAreaRegionDtoByIds(Set<Long> ids) {
        return Result.data(areaRegionStateService.getAreaRegionDtoByIds(ids));
    }

    @Override
    public Result<String> getStateNameById(Long id) {
        return Result.data(areaStateService.getStateNameById(id));
    }

    @Override
    public Result<String> getStateFullNameById(Long id) {
        return Result.data(areaStateService.getStateFullNameById(id));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Map<Long, String>> getStateNamesByIds(Set<Long> ids) {
        return Result.data(areaStateService.getStateNamesByIds(ids));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Map<Long, String>> getStateFullNamesByIds(Set<Long> ids) {
        return Result.data(areaStateService.getStateFullNamesByIds(ids));
    }

    @Override
    public Result<Map<Long, String>> getStateFullNames() {
        return Result.data(areaStateService.getStateFullNames());
    }

    @Override
    public Result<ContractVo> getInstitutionContractById(Long id) {
        return Result.data(contractService.getInstitutionContractById(id));
    }

    @Override
    public Result<Boolean> updateChangeStatus(Contract contract) {
        return Result.data(contractService.updateChangeStatus(contract));
    }

    @Override
    public Result<Boolean> changeStatus(Integer status, String tableName, Long businessKey) {
        return Result.data(contractService.changeStatus(status, tableName, businessKey));
    }

    @Override
    public Result<Map<Long, String>> getContractFormulasByIds(Set<Long> ids) {
        return Result.data(contractFormulaService.getContractFormulasByIds(ids));
    }

    @Override
    public Result<List<ContractFormulaCommissionVo>> getContractFormulaCommissionByContractFormulaId(Long id) {
        return Result.data(contractFormulaService.getContractFormulaCommissionByContractFormulaId(id));
    }

    @Override
    public Result<List<Long>> getContractFormulaPreInstitutionByContractFormulaId(Long id) {
        return Result.data(contractFormulaService.getContractFormulaPreInstitutionByContractFormulaId(id));
    }

    @Override
    public Result<List<Long>> getPreInstitutionGroupByContractFormulaId(Long id) {
        return Result.data(contractFormulaService.getPreInstitutionGroupByContractFormulaId(id));
    }

    @Override
    public Result<List<Long>> getPreMajorLevelByContractFormulaId(Long id) {
        return Result.data(contractFormulaService.getPreMajorLevelByContractFormulaId(id));
    }

    @Override
    public Result<ContractFormulaFeignVo> getContractFormulaConfigByContractFormulaId(Long formulaId) {
        return Result.data(contractFormulaService.getContractFormulaConfigByContractFormulaId(formulaId));
    }

    @Override
    public Result<Map<Long, String>> getCourseTypeNamesByIds(Set<Long> ids) {
        return Result.data(courseTypeService.getCourseTypeNamesByIds(ids));
    }

    @Override
    public Result<Map<Long, String>> getCourseTypeNamesByCourseGroupTypeIds(Set<Long> ids) {
        return Result.data(courseTypeGroupService.getCourseTypeNamesByCourseGroupTypeIds(ids));
    }

    @Override
    public Result<Map<Long, String>> getCourseTypeNamesByCourseIds(Set<Long> ids) {
        return Result.data(courseTypeService.getCourseTypeNamesByCourseIds(ids));
    }

    @Override
    public Result<Map<Long, String>> getChannelByIds(Set<Long> ids) {
        return Result.data(institutionChannelService.getChannelByIds(ids));
    }

    @Override
    public Result<List<Long>> getInstitutionIds(String institutionName) {
        return Result.data(institutionService.getInstitutionIdsByName(institutionName));
    }

    @Override
    @VerifyLogin(IsVerify = false)  //定时任务免登陆
    public Result<String> getInstitutionName(Long id) {
        return Result.data(institutionService.getInstitutionNameById(id));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Institution> getInstitutionById(Long id) {
        return Result.data(institutionService.getInstitutionById(id));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Map<Long, String>> getInstitutionNamesByIds(Set<Long> institutionIdSet) {
        return Result.data(institutionService.getInstitutionNamesByIds(institutionIdSet));
    }

    @Override
    public Result<String> getInstitutionNamesById(Long id) {
        return Result.data(institutionService.getInstitutionNameById(id));
    }

    @Override
    public Result<Map<Long, String>> getCountryNamesByInstitutionIds(Set<Long> institutionIds) {
        return Result.data(institutionService.getCountryNamesByInstitutionIds(institutionIds));
    }

    @Override
    public Result<Map<Long, Long>> getCountryIdByInstitutionId(Set<Long> institutionIdSet) {
        return Result.data(institutionService.getCountryIdByInstitutionId(institutionIdSet));
    }

    @Override
    public Result<List<Long>> getCourseIds(String name) {
        return Result.data(institutionCourseService.getCourseIds(name));
    }

    @Override
    @VerifyLogin(IsVerify = false)  //定时任务免登陆
    public Result<String> getCourseNameById(Long id) {
        return Result.data(institutionCourseService.getCourseNameById(id));
    }

    @Override
    public Result<Map<Long, String>> getCourseNameByIds(Set<Long> ids) {
        return Result.data(institutionCourseService.getCourseNameByIds(ids));
    }

    @Override
    public Result<Map<Long, String>> getCourseNameByCustomIds(Set<Long> ids) {
        return Result.data(institutionCourseService.getCourseNameByCustomIds(ids));
    }

    @Override
    public Result<String> getCourseNameChnById(Long id) {
        return Result.data(institutionCourseService.getCourseNameChnById(id));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<Map<Long, String>> getCourseEnNameByIds(Set<Long> courseIds) {
        return Result.data(institutionCourseService.getCourseEnNameByIds(courseIds));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<InstitutionCourseVo> getCourseById(Long id) {
        return Result.data(institutionCourseService.getCourseById(id));
    }

    @Override
    public Result<Map<Long, String>> getCourseNameChnByIds(Set<Long> ids) {
        return Result.data(institutionCourseService.getCourseNameByIds(ids));
    }

    @Override
    public Result<BigDecimal> getFeeById(Long id) {
        return Result.data(institutionCourseService.getFeeById(id));
    }

    @Override
    public Result<BigDecimal> getSumFeeByIds(List<Long> ids) {
        return Result.data(institutionCourseService.getSumFeeByIds(ids));
    }

    @Override
    public Result<Map<Long, String>> getInstitutionCourseNamesByIds(Set<Long> institutionCourseIdSet) {
        return Result.data(institutionCourseService.getInstitutionCourseNamesByIds(institutionCourseIdSet));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<String> getInstitutionCourseNameById(Long id) {
        return Result.data(institutionCourseService.getCourseNameById(id));
    }

    @Override
    public Result<Map<Long, String>> getInstitutionGroupNamesByIds(Set<Long> institutionGroupIdSet) {
        return Result.data(institutionCourseService.getInstitutionCourseNamesByIds(institutionGroupIdSet));
    }

    @Override
    public Result<List<Long>> getInstitutionProviderIdsByName(String institutionProviderName) {
        return Result.data(institutionProviderService.getInstitutionProviderIdsByName(institutionProviderName));
    }

    @Override
    public Result<String> getInstitutionProviderName(Long id) {
        return Result.data(institutionProviderService.getInstitutionProviderNameById(id));
    }

    @Override
    public Result<Map<Long, String>> getInstitutionProviderNamesByIds(Set<Long> ids) {
        return Result.data(institutionProviderService.getInstitutionProviderNamesByIds(ids));
    }

    @Override
    public Result<String> getInstitutionProviderType(Long id) {
        return Result.data(institutionProviderService.getInstitutionProviderTypeById(id));
    }

    @Override
    public Result<List<BaseSelectEntity>> getInstitutionProviderSelect(Long companyId) {
        return Result.data(institutionProviderService.getInstitutionProviderSelect(companyId));
    }

    @Override
    public Result<Map<Long, LinkedList<Long>>> getCompanyIdsByProviderIds(Set<Long> providerIds) {
        return Result.data(institutionProviderCompanyService.getRelationByProviderIds(providerIds));
    }

    @Override
    public Result<List<CompanyVo>> getProviderCompanyName(Long id) {
        return Result.data(institutionProviderCompanyService.getProviderCompanyName(id));
    }

    @Override
    public Result<Map<Long, String>> getInstitutionProviderChannel() {
        return Result.data(institutionProviderService.getInstitutionProviderChannel());
    }

    @Override
    public Result<String> getInstitutionProviderChannelById(Long id) {
        return Result.data(institutionProviderService.getInstitutionProviderChannelById(id));
    }

    @Override
    public Result<Map<Long, String>> getInstitutionProviderChannelByIds(Set<Long> ids) {
        return Result.data(institutionProviderService.getInstitutionProviderChannelByIds(ids));
    }

    @Override
    public Result<Map<Long, String>> getMajorLevelNamesByIds(Set<Long> ids) {
        return Result.data(majorLevelService.getMajorLevelNamesByIds(ids));
    }

    @Override
    public Result<Map<Long, Set<Long>>> getMajorLevelIdsByIds(Set<Long> ids) {
        return Result.data(majorLevelService.getMajorLevelIdsByIds(ids));
    }

    @Override
    public Result<Map<Long, String>> getMajorLevelNameChnsByIds(Set<Long> ids) {
        return Result.data(majorLevelService.getMajorLevelNameChnsByIds(ids));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Map<Long, MajorLevel>> getMajorLevelByIds(Set<Long> ids) {
        return Result.data(majorLevelService.getMajorLevelByIds(ids));
    }


    @Override
    public Result<String> getMajorLevelNamesById(Long id) {
        return Result.data(majorLevelService.getMajorLevelNamesById(id));
    }

    @Override
    public Result<Map<Long, String>> getNewsTitlesByIds(Set<Long> ids) {
        return Result.data(newsService.getNewsTitlesByIds(ids));
    }

    @Override
    public Result<List<ContractFormula>> getContractFormulasByOfferItem(Long companyId, Long areaCountryId, InstitutionStudentOfferItemDto studentOfferItem) {
        return Result.data(contractFormulaService.getContractFormulasByOfferItem(companyId, areaCountryId, studentOfferItem));
    }

    @Override
    public Result<List<Long>> getCourseIdsByContractFormulaId(Long id) {
        return Result.data(contractFormulaService.getCourseIdsByContractFormulaId(id));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<List<AreaStateVo>> getByFkAreaCountryId(Long fkAreaCountryId) {
        return Result.data(areaStateService.getByFkAreaCountryId(fkAreaCountryId));
    }

    @Override
    public Result<List<AreaCityVo>> getByFkAreaStateId(Long id) {
        return Result.data(areaCityService.getByFkAreaStateId(id));
    }

    @Override
    public Result<List<AreaCountryVo>> getAreaCountrys(String keyWord) {
        return Result.data(areaCountryService.getAreaCountrys(keyWord));
    }

    @Override
    public Result<String> checkContractFormula(Long studentCompanyId, Long areaCountryId, Long fkInstitutionCourseId, Long contractFormulaId) {
        return Result.data(contractFormulaService.checkContractFormula(studentCompanyId, areaCountryId, fkInstitutionCourseId, contractFormulaId));
    }

    @Override
    public Result<ContractFormula> getContractFormulaByFormulaId(Long formulaId) {
        return Result.data(contractFormulaService.getContractFormulaByFormulaId(formulaId));
    }

    @Override
    public Result<String> getCityFullNameById(Long id) {
        return Result.data(areaCityService.getCityFullNameById(id));
    }

    @Override
    public Result<String> getCountryNameAndNumById(Long id) {
        return Result.data(areaCountryService.getCountryNameAndNumById(id));
    }

    @Override
    public Map<Long, MediaAndAttachedVo> getMediaAndAttachedDtos(MediaAndAttachedQueryDto queryVo) {
        return attachedService.getMediaAndAttachedDtos(queryVo);
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<List<AreaCountryVo>> getCountryByKey(List<String> keys) {
        return Result.data(areaCountryService.getCountryByKey(keys));
    }

    @Override
    public Result<List<Long>> getCountryByName(@RequestParam("name") String name){
        return Result.data(areaCountryService.getCountryByName(name));
    }

    @Override
    public Result<String> getChannelName(Long id) {
        return Result.data(institutionChannelMapper.getNameById(id));
    }

    @Override
    public Result<Boolean> updateCoursefeeCny(String updateType) {
        return Result.data(institutionCourseService.updateCoursefeeCny(updateType));
    }

    @Override
    public Map<Long, String> getAllInstitutionTypeName() {
        return institutionTypeService.getAllInstitutionTypeName();
    }

    @Override
    public List<Long> getInstitutionProviderIds(String institutionProviderName) {
        return institutionProviderService.getInstitutionProviderIdsByName(institutionProviderName);
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public List<InstitutionProviderVo> getInstitutionProvidersByName(String keyword){
        return institutionProviderService.getInstitutionProvidersByName(keyword);
    }

    @Override
    public Map<Long, String> getInstitutionProviderSelectNamesByIds(Set<Long> ids) {
        return institutionProviderService.getInstitutionProviderSelectNamesByIds(ids);
    }

    @Override
    public Result<Map<Long, String>> getInstitutionZoneNamesByIds(Set<Long> institutionZoneIdSet) {
        return Result.data(institutionZoneService.getInstitutionZoneNamesByIds(institutionZoneIdSet));
    }

    @Override
    public Result<Map<Long, String>> getCountryNumByCountryIds(Set<Long> countryIdIdSet) {
        return Result.data(areaCountryService.getCountryNumByCountryIds(countryIdIdSet));
    }

    @Override
    public Map<Long, String> getMajorLevelNamesByCourIds(Set<Long> ids) {
        return majorLevelService.getMajorLevelNamesByCourIds(ids);
    }

    @Override
    public List<Long> getInstitutionProviderIdsByCompanyIds(List<Long> fkCompanyIds) {
        return institutionProviderService.getInstitutionProviderIdsByCompanyIds(fkCompanyIds);
    }

    @Override
    public List<Long> getInstitutionProviderIdsByCompanyIdAndName(List<Long> companyIds, String institutionProviderName) {
        return institutionProviderService.getInstitutionProviderIdsByCompanyIdAndName(companyIds, institutionProviderName);
    }

    @Override
    public List<Long> getInstitutionProviderIdsByCompanyId(Long companyId) {
        return institutionProviderService.getInstitutionProviderIdsByCompanyId(companyId);
    }

    @Override
    public String getMajorLevelIdStringByCourseId(Long id) {
        return majorLevelService.getMajorLevelIdStringByCourseId(id);
    }

    @Override
    public String getTypeIdStringByCourseId(Long id) {
        return institutionTypeService.getTypeIdStringByCourseId(id);
    }

    @Override
    public Result<List<BaseSelectEntity>> getInstitutionProviderChannelSelect() {
        return Result.data(institutionChannelService.getInstitutionChannelSelect());
    }

    @Override
    public Result<List<Long>> getInstitutionProviderChannelIdsByName(String channelName) {
        return Result.data(institutionChannelService.getInstitutionProviderChannelIdsByName(channelName));
    }

    @Override
    public Map<Long, Long> getInstitutionRpaMappingRelation(Set<Long> institutionIds) {
        return institutionService.getInstitutionRpaMappingRelation(institutionIds);
    }

    @Override
    public Result<String> getCountryKeyById(Long id) {
        return Result.data(areaCountryService.getCountryKeyById(id));
    }

    @Override
    public Result<Map<Long,Long>> getCountryIdsByStateIds(Set<Long> fkAreaStateIds){
        return Result.data(areaStateService.getCountryIdsByStateIds(fkAreaStateIds));
    }

    @Override
    public Result<List<BaseSelectEntity>> fuzzSearchInstitutionChannel(String keyword, List<Long> companyIds) {
        return Result.data(institutionChannelService.fuzzSearchInstitutionChannel(keyword,companyIds));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Map<Long, String>> getCountryFullNamesByIds(Set<Long> ids) {
        return Result.data(areaCountryService.getCountryFullNamesByIds(ids));
    }

    @Override
    public Result<String> getCourseTypeGroupIdsStringByCourseId(String fkInstitutionCourseTypeIds) {
        return Result.data(courseTypeGroupService.getGroupTypeIdsByCourseTypeIds(fkInstitutionCourseTypeIds));
    }

    @Override
    public Result<String> getCityChnNameById(Long id) {
        return Result.data(areaCityService.getCityChnNameById(id));
    }

    @Override
    public Result<Map<String, String>> getCountryNameByNums(Set<String> cNums) {
        return Result.data(areaCountryService.getCityChnNameById(cNums));
    }

    @Override
    public Result<Map<Long, String>> getAllAreaRegionChnNames() {
        return Result.data(areaRegionService.getAllAreaRegionChnNames());
    }

    @Override
    public Result<String> getCurrencyNumByCountryName(String countryName) {
        return Result.data(areaCountryService.getCurrencyNumByCountryName(countryName));
    }

    @Override
    public Result<List<InstitutionProviderVo>> getEventRegistrationProviderByIds(List<Long> providerIds) {
        return Result.data(institutionProviderService.getEventRegistrationProviderByIds(providerIds));
    }

    @Override
    public Result<EventRegistrationListResultVo> getEventRegistrationProviders(SearchBean<InstitutionProviderDto> page) {
        List<InstitutionProviderVo> datas = institutionProviderService.getEventRegistrationProviders(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);

        EventRegistrationListResultVo eventRegistrationListResultVo = new EventRegistrationListResultVo();
        eventRegistrationListResultVo.setInstitutionProviderDtos(datas);
        eventRegistrationListResultVo.setPage(p);
//        ListResponseBo<InstitutionProviderVo> listResponseBo = new ListResponseBo<>(datas, p);
//        String string = JSONObject.toJSONString(listResponseBo);
        return Result.data(eventRegistrationListResultVo);
    }

    @Override
    public Result<CaseStudyResultsDto> getCaseStudyResults(CaseStudyQueryDto queryVo) {
        return Result.data(institutionService.getCaseStudyResults(queryVo));
    }

    @Override
    public Result<List<InstitutionCourseMatchVo>> getInstitutionCourseByNameMatch(String courseName, Long institutionId) {
        return Result.data(institutionCourseService.getInstitutionCourseByNameMatch(courseName,institutionId));
    }

    @Override
    public Result<Map<Long, String>> getCourseGroupTypeNameByIds(Set<Long> ids) {
        return Result.data(courseTypeGroupService.getCourseGroupTypeNameByIds(ids));
    }

    @Override
    public Result<Map<Long, String>> getCourseGroupTypeNameChnByIds(Set<Long> ids) {
        return Result.data(courseTypeGroupService.getCourseGroupTypeNameChnByIds(ids));
    }

    @Override
    public Result<Map<Long, String>> getCourseGroupTypeFullNameByIds(Set<Long> ids){
        return Result.data(courseTypeGroupService.getCourseGroupTypeFullNameByIds(ids));
    }

    @Override
    public Result<Set<Long>> getLikeInstitutionIds(InstitutionApplicationStaticsDto institutionApplicationStaticsDto){
        return Result.data(institutionService.getLikeInstitutionIds(institutionApplicationStaticsDto));
    }

    @Override
    public Result<List<Long>> getInstitutionIdsByKeyword(@RequestParam("keyword") String keyword){
        return Result.data(institutionService.getInstitutionIdsByKeyword(keyword));
    }

    @Override
    public Result<List<InstitutionApplicationStatisticsVo>> getInstitutionDtoList(InstitutionApplicationStaticsQueryBo staticsQueryBo){
        return  Result.data(institutionService.getInstitutionDtoList(staticsQueryBo));
    }

    @Override
    public Result<String> getAreaCityNameByIds(Set<Long> ids) {
        return Result.data(areaCityService.getAreaCityNameByIds(ids));
    }

    @Override
    public Result<String> getAreaStateNameByIds(Set<Long> ids) {
        return Result.data(areaStateService.getAreaStateNameByIds(ids));
    }

    @Override
    public List<BaseSelectEntity> getInstitutionSelectByCompanyId(Long companyId) {
        return institutionProviderService.getInstitutionSelectByCompanyId(companyId);
    }

    @Override
    public List<BaseSelectEntity> getInstitutionProviderByTargetName(String targetName) {
        return institutionProviderService.getInstitutionProviderByTargetName(targetName);
    }

    @Override
    public List<BaseSelectEntity> getInstitutionProviderAccountList(Long fkTargetId) {
        return institutionProviderAccountService.getInstitutionProviderAccountList(fkTargetId);
    }

    @Override
    public String getInstitutionProviderAccountById(Long fkBankAccountId) {
        return institutionProviderAccountService.getInstitutionProviderAccountById(fkBankAccountId);
    }

    //系统调度任务录入无需登录
    @Override
    @VerifyLogin(IsVerify = false)
    public Long addNews(NewsDto newsDto) {
        return newsService.addNews(newsDto);
    }
    //系统调度任务录入无需登录
    @Override
    @VerifyLogin(IsVerify = false)
    public List<NewsTypeVo> getNewsTypes() {
        NewsTypeDto newsTypeDto = new NewsTypeDto();
        Page page = new Page();
        page.setCurrentPage(1);
        page.setShowCount(500);//模拟500，实际不超过500
        return newsTypeService.getNewsTypes(newsTypeDto,page);
    }
    //系统调度任务录入无需登录
    @Override
    @VerifyLogin(IsVerify = false)
    public List<AreaCountryVo> getAllCountry() {
        return areaCountryService.getAllAreaCountryList();
    }

    //系统调度任务录入无需登录
    @Override
    @VerifyLogin(IsVerify = false)
    public List<MediaAndAttachedVo> addNewsMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
        return newsService.addNewsMedia(mediaAttachedVos);
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Boolean checkNews(NewsDto newsDto) {
        return newsService.checkNews(newsDto);
    }

    @Override
    public Result<Map<Long, String>> getInstitutionChannelProviderNamesByIds(Set<Long> providerIds) {
        return Result.data(institutionProviderService.getInstitutionChannelProviderNamesByIds(providerIds));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public List<AreaCountryVo> getCountryByPublicLevel(Integer publicLevel) {
        return areaCountryService.getCountryByPublicLevel(publicLevel);
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public InstitutionProviderVo getInstitutionProviderById(Long fkInstitutionProviderId) {
        return institutionProviderService.getInstitutionProviderById(fkInstitutionProviderId);
    }

    @Override
    public Set<Long>  getInstitutionProviderByInstitution(Long institutionId) {
        return institutionProviderService.getInstitutionProviderByInstitution(institutionId);
    }

    @Override
    public String getContractNewByProviderId(Long fkInstitutionProviderId) {
        return contractService.getContractNewByProviderId(fkInstitutionProviderId);
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public InstitutionProviderContractReminderVo getContractExpiredByProviderId(Long contractId) {
        return institutionProviderService.getContractExpiredByProviderId(contractId);
    }

    @Override
    public Result<Map<Long, AreaCountryVo>> getCountryDtoMapByIds(Set<Long> countryIds) {
        return Result.data(areaCountryService.getCountryDtoMapByIds(countryIds));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Map<Long, InstitutionProviderVo>> getInstitutionProviderMapByIds(Set<Long> institutionProviderIds) {
        return Result.data(institutionProviderService.getInstitutionProviderMapByIds(institutionProviderIds));
    }

    @Override
    public Result<Map<Long, InstitutionVo>> getInstitutionDtoMapByIds(Set<Long> institutionIdSet) {
        return Result.data(institutionService.getInstitutionDtoMapByIds(institutionIdSet));
    }

    @Override
    public Result<Map<Long, InstitutionVo>> getInstitutionDtoByIds(Set<Long> institutionIdSet) {
        return Result.data(institutionService.getInstitutionDtoByIds(institutionIdSet));
    }

    @Override
    public Map<Long, AreaRegionVo> getRegionMapByStateIds(List<Long> stateIds) {
        return areaRegionService.getRegionMapByStateIds(stateIds);
    }

    @Override
    public Map<Long, String> getAreaRegionNameByIds(Set<Long> fkAreaRegionIds) {
        return areaRegionService.getAreaRegionNameByIds(fkAreaRegionIds);
    }

    @Override
    public Result<AreaRegionVo> findAreaRegionById(Long id) {
        return Result.data(areaRegionService.findAreaRegionById(id));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<List<InstitutionProviderInstitutionVo>> getInstitutionByProvider(Set<Long> fkCompanyIds, Set<Long> fkInstitutionProviderIds) {
       return Result.data(institutionProviderService.getInstitutionByProvider(fkCompanyIds, fkInstitutionProviderIds));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<List<InstitutionVo>> getInstitutionByCountryIds(Set<Long> fkAreaCountryIds) {
        return Result.data(institutionService.getInstitutionByCountryIds(fkAreaCountryIds));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public void batchTranslationInstitutionAndCourseInfo() {
        asyncCompletions.batchTranslationInstitutionAndCourseInfo();
    }


    @PostMapping(GET_MPS_INSTITUTION_PROVIDER_LIST)
    public Result<List<InstitutionVo>> getMpsInstitutionProviderList(@RequestBody Long fkProviderId){
        return Result.data(institutionProviderService.getMpsInstitutionProviderList(fkProviderId));
    }

    // 根据国家key集合获取对应的id
    @Override
    public Result<Map<String, Long>> getCountryIdByKeys(Set<String> keys) {
        return Result.data(areaCountryService.getCountryIdByKeys(keys));
    }

    //根据新闻id和发送类型获取模板
    @Override
    @VerifyLogin(IsVerify = false)
    public Result<NewsEmailTmpeleteVo> getNewsEmailTmpelete(Long newsId, Integer sendType){
        // 这里由于ap包不能引ap包，所以建了一个新的模板类返回参数
        AliyunSendMailDto emailTmpelte = newsService.getNewEmailTmpelte(newsId, sendType);
        NewsEmailTmpeleteVo newsEmailTmpeleteVo = BeanCopyUtils.objClone(emailTmpelte, NewsEmailTmpeleteVo::new);
        return Result.data(newsEmailTmpeleteVo);

    }

    // 根据目标表名和ID获取申请费信息
    @Override
    public Result<List<InstitutionAppFeeVo>> getAppFees(String fkTableName, Long fkTableId) {
        return Result.data(institutionAppFeeService.getAppFees(fkTableName, fkTableId));
    }

    @Override
    public Result<ResponseBo> getInstitutionByInstitutionPermissionGroup(SearchBean<InstitutionQueryDto> page) {
        List<InstitutionVo> datas = institutionService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return Result.data(new ResponseBo<>(datas, p));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<List<String>> getContractApplyCountryByContractId(@RequestBody Long id) {
        return Result.data(institutionProviderService.getContractApplyCountryByContractId(id));
    }


    @Override
    @VerifyLogin(IsVerify = false)
    public List<AreaCountryVo> getCountryByPublicLevelBySummit(Integer publicLevel) {
        return areaCountryService.getCountryByPublicLevelBySummit(publicLevel);
    }


}
