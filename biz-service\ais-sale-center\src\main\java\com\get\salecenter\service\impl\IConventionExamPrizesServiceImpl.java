package com.get.salecenter.service.impl;

import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.ConventionExamPrizesMapper;
import com.get.salecenter.entity.ConventionExamPrizes;
import com.get.salecenter.service.IConventionExamPrizesService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class IConventionExamPrizesServiceImpl implements IConventionExamPrizesService {

    @Resource
    private ConventionExamPrizesMapper conventionExamPrizesMapper;


//    @Override
//    public List<ConventionExamPrizesVo> getDatas(ConventionExamPrizesDto conventionExamPrizesVo, SearchBean<ConventionExamPrizesDto> page) {
//        if (GeneralTool.isEmpty(conventionExamPrizesVo)){
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
//        }
//        IPage<ConventionExamPrizesVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
//        List<ConventionExamPrizesVo> conventionExamPrizesList = conventionExamPrizesMapper.getConventionExamPrizesList(iPage, conventionExamPrizesVo);
//        page.setAll((int) iPage.getTotal());
////        if (GeneralTool.isNotEmpty(conventionExamPrizesList)){
////            Set<Long> examIds = conventionExamPrizesList.stream().map(ConventionExamPrizesVo::getFkExaminationId).collect(Collectors.toSet());
////
////            for (ConventionExamPrizesVo conventionExamPrizesDto : conventionExamPrizesList) {
////
////            }
////        }
//        return conventionExamPrizesList;
//    }

    @Override
    public void updateExamPrizesExchange(Long id, Boolean isExchange) {
        ConventionExamPrizes conventionExamPrizes = conventionExamPrizesMapper.selectById(id);
        if (GeneralTool.isEmpty(conventionExamPrizes)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        conventionExamPrizes.setIsExchange(isExchange);
        conventionExamPrizesMapper.updateById(conventionExamPrizes);
    }

    @Override
    public List<Map<String, Object>> findPrizesType() {
        return ProjectKeyEnum.enums2Arrays(ProjectKeyEnum.PRIZE_TYPE);
    }

//    @Override
//    public List<BaseSelectEntity> examinationSelectByConventionId(Long fkConventionId) {
//        return conventionExamPrizesMapper.examinationSelectByConventionId(fkConventionId);
//    }

//    @Override
//    public void exportExcel(HttpServletResponse response, Long fkConventionId) {
        /*if (GeneralTool.isEmpty(fkConventionId)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<ConventionExamPrizesNewVo> conventionExamPrizesNewDtos= conventionExamPrizesMapper.getConventionExamExportList(fkConventionId);

        if (GeneralTool.isNotEmpty(conventionExamPrizesNewDtos)){

            conventionExamPrizesNewDtos.stream().forEach(e ->{
                int conventionExamScore = conventionExamPrizesMapper.getConventionExamScore(e.getFkUserId(), e.getFkExaminationId());
                int conventionExamScoreSum = conventionExamPrizesMapper.getConventionExamScoreSum(e.getFkUserId());
                e.setScore(conventionExamScore);
                if (2<=conventionExamScoreSum && conventionExamScoreSum<=100) {
                    e.setCertificateTypeName("Star Award星耀证书");
                }else if (102<=conventionExamScoreSum && conventionExamScoreSum<=200){
                    e.setCertificateTypeName("Gold Award白银证书");
                }else if (202<=conventionExamScoreSum && conventionExamScoreSum<=300){
                    e.setCertificateTypeName("Gold Award黄金证书");
                }else if (302<=conventionExamScoreSum && conventionExamScoreSum<=350){
                    e.setCertificateTypeName("Platinum Award白金证书");
                }else if (352<=conventionExamScoreSum && conventionExamScoreSum<=400){
                    e.setCertificateTypeName("Diamond Award钻石证书");
                }else {
                    e.setCertificateTypeName("无");
                }
                //是否有抽奖记录
                Long flag = conventionExamPrizesMapper.getConventionExamPrizesLog(fkConventionId,e.getFkExaminationId(),e.getFkUserId());
                //是否中奖
                Long isWinning=conventionExamPrizesMapper.getConventionPrizesWinning(fkConventionId,e.getFkExaminationId(),e.getFkUserId());
                if (e.getScore()!=null && e.getScore() < 70){
                    e.setWinningSituation("未达标");
                } else if (e.getScore()!=null && e.getScore() >= 70 && GeneralTool.isEmpty(flag)) {
                    e.setWinningSituation("未开袋");
                }else if (e.getScore()!=null && e.getScore() >= 70 && GeneralTool.isNotEmpty(flag) && GeneralTool.isEmpty(isWinning)) {
                    e.setWinningSituation("开袋未中");
                }else if (e.getScore()!=null && e.getScore() >= 70 && GeneralTool.isNotEmpty(flag) && GeneralTool.isNotEmpty(isWinning)) {
                    e.setWinningSituation("已中奖");
                }
            });

        }

        List<ConventionExamExportPrizesVo> insuranceExportDtos = conventionExamPrizesNewDtos.stream().map(c -> BeanCopyUtils.objClone(c, ConventionExamExportPrizesVo::new)).collect(Collectors.toList());
        FileUtils.exportExcelNotWrapText(response, insuranceExportDtos, "ConventionExamPrizesDto", ConventionExamExportPrizesVo.class);*/
//    }
}
