package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("r_payable_plan_settlement_agent_account")
public class PayablePlanSettlementAgentAccount extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 财务结算汇总批次号
     */
    @ApiModelProperty(value = "财务结算汇总批次号")
    private String numSettlementBatch;
    /**
     * 应付计划Id
     */
    @ApiModelProperty(value = "应付计划Id")
    private Long fkPayablePlanId;
    /**
     * 学生代理合同账户Id
     */
    @ApiModelProperty(value = "学生代理合同账户Id")
    private Long fkAgentContractAccountId;
    /**
     * 币种编号（代理账户）
     */
    @ApiModelProperty(value = "币种编号（代理账户）")
    private String fkCurrencyTypeNum;
}