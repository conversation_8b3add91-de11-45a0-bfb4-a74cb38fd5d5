package com.get.resumecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.resumecenter.entity.ResumeSkill;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @DATE: 2020/8/10
 * @TIME: 15:21
 * @Description: 技能DTO
 **/
@Data
public class ResumeSkillVo extends BaseEntity {

    /**
     * 类型名称
     */
    @ApiModelProperty(value = "技能类型名称")
    private String fkSkillTypeName;

    /**
     * 所属简历Id
     */
    @ApiModelProperty(value = "所属简历Id")
    @Column(name = "fk_resume_id")
    private Long fkResumeId;
    /**
     * 技能类型Id
     */
    @ApiModelProperty(value = "技能类型Id")
    @Column(name = "fk_skill_type_id")
    private Long fkSkillTypeId;
    /**
     * 掌握程度：一般/良好/熟练/精通
     */
    @ApiModelProperty(value = "掌握程度：一般/良好/熟练/精通")
    @Column(name = "skill_level")
    private String skillLevel;

}
