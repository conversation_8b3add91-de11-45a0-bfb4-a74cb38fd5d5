<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.platformconfigcenter.dao.appissue.AppFormDivisionMapper">
  <resultMap id="BaseResultMap" type="com.get.platformconfigcenter.entity.AppFormDivision">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="division_key" jdbcType="VARCHAR" property="divisionKey" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_active" jdbcType="BIT" property="isActive" />
    <result column="is_required" jdbcType="BIT" property="isRequired" />
    <result column="view_order" jdbcType="INTEGER" property="viewOrder" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <sql id="Base_Column_List">
    id, division_key, name, remark, is_active, view_order, gmt_create, gmt_create_user, 
    gmt_modified, gmt_modified_user
  </sql>
  <!--TODO 注释sql-->
<!--  <insert id="insert" parameterType="com.get.platformconfigcenter.entity.AppFormDivision" keyProperty="id" useGeneratedKeys="true">-->
<!--    insert into u_app_form_division (id, division_key, name,-->
<!--      remark, is_active, view_order, is_required,-->
<!--      gmt_create, gmt_create_user, gmt_modified, -->
<!--      gmt_modified_user)-->
<!--    values (#{id,jdbcType=BIGINT}, #{divisionKey,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, -->
<!--      #{remark,jdbcType=VARCHAR}, #{isActive,jdbcType=BIT}, #{viewOrder,jdbcType=INTEGER}, #{isRequired,jdbcType=BIT},-->
<!--      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, -->
<!--      #{gmtModifiedUser,jdbcType=VARCHAR})-->
<!--  </insert>-->
<!--  <insert id="insertSelective" parameterType="com.get.platformconfigcenter.entity.AppFormDivision">-->
<!--    insert into u_app_form_division-->
<!--    <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        id,-->
<!--      </if>-->
<!--      <if test="divisionKey != null">-->
<!--        division_key,-->
<!--      </if>-->
<!--      <if test="name != null">-->
<!--        name,-->
<!--      </if>-->
<!--      <if test="remark != null">-->
<!--        remark,-->
<!--      </if>-->
<!--      <if test="isActive != null">-->
<!--        is_active,-->
<!--      </if>-->
<!--      <if test="isRequired != null">-->
<!--        is_required,-->
<!--      </if>-->
<!--      <if test="viewOrder != null">-->
<!--        view_order,-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        gmt_create,-->
<!--      </if>-->
<!--      <if test="gmtCreateUser != null">-->
<!--        gmt_create_user,-->
<!--      </if>-->
<!--      <if test="gmtModified != null">-->
<!--        gmt_modified,-->
<!--      </if>-->
<!--      <if test="gmtModifiedUser != null">-->
<!--        gmt_modified_user,-->
<!--      </if>-->
<!--    </trim>-->
<!--    <trim prefix="values (" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        #{id,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="divisionKey != null">-->
<!--        #{divisionKey,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="name != null">-->
<!--        #{name,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="remark != null">-->
<!--        #{remark,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="isActive != null">-->
<!--        #{isActive,jdbcType=BIT},-->
<!--      </if>-->
<!--      <if test="isRequired != null">-->
<!--        #{isRequired,jdbcType=BIT},-->
<!--      </if>-->
<!--      <if test="viewOrder != null">-->
<!--        #{viewOrder,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        #{gmtCreate,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtCreateUser != null">-->
<!--        #{gmtCreateUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="gmtModified != null">-->
<!--        #{gmtModified,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtModifiedUser != null">-->
<!--        #{gmtModifiedUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--    </trim>-->
<!--  </insert>-->
<!--  <update id="updateByPrimaryKeySelective" parameterType="com.get.platformconfigcenter.entity.AppFormDivision">-->
<!--    update u_app_form_division-->
<!--    <set>-->
<!--      <if test="divisionKey != null">-->
<!--        division_key = #{divisionKey,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="name != null">-->
<!--        name = #{name,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="remark != null">-->
<!--        remark = #{remark,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="isActive != null">-->
<!--        is_active = #{isActive,jdbcType=BIT},-->
<!--      </if>-->
<!--      <if test="isRequired != null">-->
<!--        is_required = #{isRequired,jdbcType=BIT},-->
<!--      </if>-->
<!--      <if test="viewOrder != null">-->
<!--        view_order = #{viewOrder,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtCreateUser != null">-->
<!--        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="gmtModified != null">-->
<!--        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtModifiedUser != null">-->
<!--        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--    </set>-->
<!--    where id = #{id,jdbcType=BIGINT}-->
<!--  </update>-->
<!--  <update id="updateByPrimaryKey" parameterType="com.get.platformconfigcenter.entity.AppFormDivision">-->
<!--    update u_app_form_division-->
<!--    set division_key = #{divisionKey,jdbcType=VARCHAR},-->
<!--      name = #{name,jdbcType=VARCHAR},-->
<!--      remark = #{remark,jdbcType=VARCHAR},-->
<!--      is_active = #{isActive,jdbcType=BIT},-->
<!--      is_required = #{isRequired,jdbcType=BIT},-->
<!--      view_order = #{viewOrder,jdbcType=INTEGER},-->
<!--      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},-->
<!--      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},-->
<!--      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},-->
<!--      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}-->
<!--    where id = #{id,jdbcType=BIGINT}-->
<!--  </update>-->
<!--  <select id="getMaxViewOrder" resultType="java.lang.Integer">-->
<!--    select-->
<!--      IFNULL(max(view_order)+1,0) view_order-->
<!--    from-->
<!--      u_app_form_division-->
<!--  </select>-->
</mapper>