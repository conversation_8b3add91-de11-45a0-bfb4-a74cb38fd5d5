package com.get.resumecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.resumecenter.entity.OtherType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

;

@Mapper
public interface OtherTypeMapper extends BaseMapper<OtherType> {
    int insert(OtherType record);

    int insertSelective(OtherType record);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 下拉
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getOtherTypeSelect();

    Integer getMaxViewOrder();
}