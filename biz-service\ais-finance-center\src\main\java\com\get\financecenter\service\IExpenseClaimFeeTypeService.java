package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.financecenter.vo.ExpenseClaimFeeTypeVo;
import com.get.financecenter.entity.ExpenseClaimFeeType;
import com.get.financecenter.dto.ExpenseClaimFeeTypeDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/6 16:54
 * @verison: 1.0
 * @description:
 */
public interface IExpenseClaimFeeTypeService extends BaseService<ExpenseClaimFeeType> {
    /**
     * @return com.get.financecenter.vo.ExpenseClaimFeeTypeDto
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    ExpenseClaimFeeTypeVo findExpenseClaimFeeTypeById(Long id);

    /**
     * @return void
     * @Description :批量新增
     * @Param [expenseClaimFeeTypeVos]
     * <AUTHOR>
     */
    void batchAdd(List<ExpenseClaimFeeTypeDto> expenseClaimFeeTypeDtos);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.financecenter.vo.ExpenseClaimFeeTypeDto
     * @Description :修改
     * @Param [expenseClaimFeeTypeVo]
     * <AUTHOR>
     */
    ExpenseClaimFeeTypeVo updateExpenseClaimFeeType(ExpenseClaimFeeTypeDto expenseClaimFeeTypeDto);

    /**
     * @return java.util.List<com.get.financecenter.vo.ExpenseClaimFeeTypeDto>
     * @Description :列表
     * @Param [expenseClaimFeeTypeVo, page]
     * <AUTHOR>
     */
    List<ExpenseClaimFeeTypeVo> getExpenseClaimFeeTypes(ExpenseClaimFeeTypeDto expenseClaimFeeTypeDto, Page page);

    /**
     * @return void
     * @Description :上移下移
     * @Param [expenseClaimFeeTypeVos]
     * <AUTHOR>
     */
    void sort(List<ExpenseClaimFeeTypeDto> expenseClaimFeeTypeDtos);

    /**
     * @return java.util.List<com.get.financecenter.vo.ExpenseClaimFeeTypeDto>
     * @Description :费用报销单类型下拉框数据
     * @Param []
     * <AUTHOR>
     */
    List<ExpenseClaimFeeTypeVo> getExpenseClaimFeeTypeSelect();

    /**
     * @return java.lang.String
     * @Description :根据id查找对应名称
     * @Param [expenseClaimFeeTypeId]
     * <AUTHOR>
     */
    String getExpenseClaimFeeTypeNameById(Long expenseClaimFeeTypeId);

    void movingOrder(Integer start, Integer end);
}
