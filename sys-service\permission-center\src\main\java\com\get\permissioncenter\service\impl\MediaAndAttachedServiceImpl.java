package com.get.permissioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.permissioncenter.dao.MediaAndAttachedMapper;
import com.get.permissioncenter.dto.MediaAndAttachedDto;
import com.get.permissioncenter.vo.MediaAndAttachedVo;
import com.get.permissioncenter.entity.PermissionMediaAndAttached;
import com.get.permissioncenter.service.IMediaAndAttachedService;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/7/14
 * @TIME: 18:20
 * @Description:
 **/
@Service
public class MediaAndAttachedServiceImpl extends GetServiceImpl<MediaAndAttachedMapper,PermissionMediaAndAttached> implements IMediaAndAttachedService {

    @Resource
    private MediaAndAttachedMapper mediaAndAttachedMapper;
    @Resource
    private IFileCenterClient fileCenterClient;
    @Resource
    private UtilService utilService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        PermissionMediaAndAttached mediaAndAttached = mediaAndAttachedMapper.selectById(id);
        if (GeneralTool.isEmpty(mediaAndAttached)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_not_exist"));
        }
        int i = mediaAndAttachedMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }else
        {
            Result<Boolean> result = fileCenterClient.delete(mediaAndAttached.getFkFileGuid(), LoggerModulesConsts.PERMISSIONCENTER);
            if (!result.isSuccess()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
            }
        }

    }

    @Override
    public List<FileDto> upload(MultipartFile[] multipartFiles) {
        if (GeneralTool.isEmpty(multipartFiles)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }
        List<FileDto> fileDtos = null;
//        try {
//            ListResponseBo responseBo = fileCenterClient.upload(multipartFiles, LoggerModulesConsts.PERMISSIONCENTER);
//            if (!ErrorCodeEnum.REQUEST_OK.getCode().equals(responseBo.getCode())) {
//                throw new GetServiceException("图片上传失败,请重试");
//            }
//            JSONArray jsonArray = JSONArray.fromObject(responseBo.getDatas());
//            fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
//        } catch (IOException e) {
//            e.printStackTrace();
//        }

        Result<List<FileDto>> result = fileCenterClient.upload(multipartFiles, LoggerModulesConsts.PERMISSIONCENTER);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            JSONArray jsonArray = JSONArray.fromObject(result.getData());
            fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_upload_fail"));
        }
        return fileDtos;
    }

    @Override
    public List<FileDto> uploadAttached(MultipartFile[] multipartFiles) {
        if (GeneralTool.isEmpty(multipartFiles)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }
        List<FileDto> fileDtos = null;
//        try {
//            ListResponseBo responseBo = fileCenterClient.uploadAppendix(multipartFiles, LoggerModulesConsts.PERMISSIONCENTER);
//            if (!ErrorCodeEnum.REQUEST_OK.getCode().equals(responseBo.getCode())) {
//                throw new GetServiceException("上传失败,请重试");
//            }
//            JSONArray jsonArray = JSONArray.fromObject(responseBo.getDatas());
//            fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
//        } catch (IOException e) {
//            e.printStackTrace();
//        }

        Result<List<FileDto>> result = fileCenterClient.uploadAppendix(multipartFiles, LoggerModulesConsts.PERMISSIONCENTER);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            JSONArray jsonArray = JSONArray.fromObject(result.getData());
            fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_upload_fail"));
        }
        return fileDtos;
    }

    @Override
    public MediaAndAttachedVo addMediaAndAttached(MediaAndAttachedDto mediaAndAttachedDto) {
        if (GeneralTool.isEmpty(mediaAndAttachedDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        if (GeneralTool.isEmpty(mediaAndAttachedDto.getFkTableName())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("tableName_null"));
        }
//        MediaAndAttached mediaAndAttached = Tools.objClone(mediaAndAttachedDto, MediaAndAttached.class);
        PermissionMediaAndAttached mediaAndAttached = BeanCopyUtils.objClone(mediaAndAttachedDto, PermissionMediaAndAttached::new);
        //查询索引值
        Integer nextIndexKey = mediaAndAttachedMapper.getNextIndexKey(mediaAndAttached.getFkTableId(), mediaAndAttachedDto.getFkTableName());
        nextIndexKey = GeneralTool.isNotEmpty(nextIndexKey) ? nextIndexKey : 0;
        //设置属性
        mediaAndAttached.setIndexKey(nextIndexKey);
//        utilService.setCreateInfo(mediaAndAttached);
        utilService.updateUserInfoToEntity(mediaAndAttached);
        mediaAndAttachedMapper.insertSelective(mediaAndAttached);
//        MediaAndAttachedDto mediaAndAttachedVo = Tools.objClone(mediaAndAttachedDto, MediaAndAttachedDto.class);
        MediaAndAttachedVo mediaAndAttachedVo = BeanCopyUtils.objClone(mediaAndAttachedDto, MediaAndAttachedVo::new);
        //设置回显的值
        mediaAndAttachedVo.setTypeValue(FileTypeEnum.getValue(mediaAndAttachedDto.getTypeKey()));
        mediaAndAttachedVo.setFilePath(mediaAndAttachedDto.getFilePath());
        mediaAndAttachedVo.setFileNameOrc(mediaAndAttachedDto.getFileNameOrc());
        mediaAndAttachedVo.setId(mediaAndAttached.getId());
        mediaAndAttachedVo.setFileKey(mediaAndAttachedDto.getFileKey());
        return mediaAndAttachedVo;
    }

    @Override
    public void updateTableId(Long mediaId, Long fkTableId) {
        PermissionMediaAndAttached mediaAndAttached = new PermissionMediaAndAttached();
        mediaAndAttached.setFkTableId(fkTableId);
        mediaAndAttached.setId(mediaId);
//        attachedMapper.updateByPrimaryKeySelective(mediaAndAttached);
        mediaAndAttachedMapper.updateById(mediaAndAttached);
    }

    @Override
    public void delete(MediaAndAttachedDto attachedVo) {
//        Example example = new Example(MediaAndAttached.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkTableName", attachedVo.getFkTableName());
//        criteria.andEqualTo("fkTableId", attachedVo.getFkTableId());
//        criteria.andEqualTo("typeKey", attachedVo.getTypeKey());
//        attachedMapper.deleteByExample(example);
        mediaAndAttachedMapper.delete(Wrappers.<PermissionMediaAndAttached>query().lambda()
                .eq(PermissionMediaAndAttached::getFkTableName, attachedVo.getFkTableName())
                .eq(PermissionMediaAndAttached::getFkTableId, attachedVo.getFkTableId())
                .eq(PermissionMediaAndAttached::getTypeKey, attachedVo.getTypeKey()));
    }

    @Override
    public List<Map<String, Object>> findStaffMediaType() {
        return FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.PERMISSIONSFATT);
    }

    @Override
    public List<Map<String, Object>> findHrEventMediaType() {
        return FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.PERMISSIONHREVENT);
    }

    @Override
    public List<Map<String, Object>> findContractMediaType() {
        return FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.PERMISSIONCONTRACT);
    }

    @Override
    public List<PermissionMediaAndAttached> getMediaAndAttachedByIaeCrm(List<String> fkTableNames) {
        LambdaQueryWrapper<PermissionMediaAndAttached> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.like(PermissionMediaAndAttached::getRemark,"AttachmentFileID");//iae文件标记
        if(GeneralTool.isNotEmpty(fkTableNames) && fkTableNames.size()>0)
        {
            lambdaQueryWrapper.in(PermissionMediaAndAttached::getFkTableName,fkTableNames);
        }
//        lambdaQueryWrapper.last("limit 5");//临时测试只读5条
        return mediaAndAttachedMapper.selectList(lambdaQueryWrapper);
    }

    @Override
    public Boolean updateMediaAndAttachedById(PermissionMediaAndAttached permissionMediaAndAttached) {
        mediaAndAttachedMapper.updateById(permissionMediaAndAttached);
        return true;
    }

    @Override
    public List<MediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo) {
        LambdaQueryWrapper<PermissionMediaAndAttached> lambdaQueryWrapper = getLambdaQueryWrapper(attachedVo);
        List<PermissionMediaAndAttached> mediaAndAttacheds = this.mediaAndAttachedMapper.selectList(lambdaQueryWrapper);
        return getFileMedia(mediaAndAttacheds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
//        MediaAndAttached ro = Tools.objClone(mediaAttachedVos.get(0), MediaAndAttached.class);
//        Integer oneorder = ro.getIndexKey();
//        MediaAndAttached rt = Tools.objClone(mediaAttachedVos.get(1), MediaAndAttached.class);
//        Integer twoorder = rt.getIndexKey();
//        ro.setIndexKey(twoorder);
//        utilService.setUpdateInfo(ro);
//        rt.setIndexKey(oneorder);
//        utilService.setUpdateInfo(rt);
//        attachedMapper.updateByPrimaryKeySelective(ro);
//        attachedMapper.updateByPrimaryKeySelective(rt);
        PermissionMediaAndAttached ro = BeanCopyUtils.objClone(mediaAttachedVos.get(0), PermissionMediaAndAttached::new);
        Integer oneorder = ro.getIndexKey();
        PermissionMediaAndAttached rt = BeanCopyUtils.objClone(mediaAttachedVos.get(1), PermissionMediaAndAttached::new);
        Integer twoorder = rt.getIndexKey();
        ro.setIndexKey(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setIndexKey(oneorder);
        utilService.updateUserInfoToEntity(rt);
        mediaAndAttachedMapper.updateById(ro);
        mediaAndAttachedMapper.updateById(rt);
    }

    //分頁邏輯需要確認
    @Override
    public List<MediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo, Page page) {
        LambdaQueryWrapper<PermissionMediaAndAttached> lambdaQueryWrapper = getLambdaQueryWrapper(attachedVo);
        IPage<PermissionMediaAndAttached> pages = this.mediaAndAttachedMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<PermissionMediaAndAttached> mediaAndAttacheds = pages.getRecords();
        page.setAll((int) pages.getTotal());
        return getFileMedia(mediaAndAttacheds);
    }

//    /**
//     * 获取媒体附件
//     * @param attachedVo
//     * @return
//     * @throws GetServiceException
//     */
//    private List<PermissionMediaAndAttached> getMediaAndAttacheds(MediaAndAttachedDto attachedVo) throws GetServiceException {
//        if (GeneralTool.isEmpty(attachedVo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
//        }
////        Example example = new Example(MediaAndAttached.class);
////        Example.Criteria criteria = example.createCriteria();
////        if (GeneralTool.isNotEmpty(attachedVo.getTypeKey())) {
////            criteria.andEqualTo("typeKey", attachedVo.getTypeKey());
////        }
////        if (GeneralTool.isEmpty(attachedVo.getTypeKey())) {
////            criteria.andNotEqualTo("typeKey", FileTypeEnum.PERMISSION_HEAD_ICON.key);
////        }
////        criteria.andEqualTo("fkTableName", attachedVo.getFkTableName());
////        criteria.andEqualTo("fkTableId", attachedVo.getFkTableId());
////        example.orderBy("indexKey").desc();
//        LambdaQueryWrapper<PermissionMediaAndAttached> wrapper = new LambdaQueryWrapper();
//        if (GeneralTool.isNotEmpty(attachedVo.getTypeKey())) {
//            wrapper.eq(PermissionMediaAndAttached::getTypeKey, attachedVo.getTypeKey());
//        }
//        if (GeneralTool.isEmpty(attachedVo.getTypeKey())) {
//            wrapper.ne(PermissionMediaAndAttached::getTypeKey, FileTypeEnum.PERMISSION_HEAD_ICON.key);
//        }
//        wrapper.eq(PermissionMediaAndAttached::getFkTableName, attachedVo.getFkTableName());
//        wrapper.eq(PermissionMediaAndAttached::getFkTableId, attachedVo.getFkTableId());
//        wrapper.orderByDesc(PermissionMediaAndAttached::getIndexKey);
//        return mediaAndAttachedMapper.selectList(wrapper);
//    }

    /**
     * 获取媒体附件
     *
     * @param attachedVo
     * @return
     * @throws GetServiceException
     */
    private LambdaQueryWrapper<PermissionMediaAndAttached> getLambdaQueryWrapper(MediaAndAttachedDto attachedVo) {
        if (GeneralTool.isEmpty(attachedVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
//        Example example = new Example(MediaAndAttached.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(attachedVo.getTypeKey())) {
//            criteria.andEqualTo("typeKey", attachedVo.getTypeKey());
//        }
//        if (GeneralTool.isEmpty(attachedVo.getTypeKey())) {
//            criteria.andNotEqualTo("typeKey", FileTypeEnum.PERMISSION_HEAD_ICON.key);
//        }
//        criteria.andEqualTo("fkTableName", attachedVo.getFkTableName());
//        criteria.andEqualTo("fkTableId", attachedVo.getFkTableId());
//        example.orderBy("indexKey").desc();
        LambdaQueryWrapper<PermissionMediaAndAttached> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(attachedVo.getTypeKey())) {
            wrapper.eq(PermissionMediaAndAttached::getTypeKey, attachedVo.getTypeKey());
        }
        if (GeneralTool.isEmpty(attachedVo.getTypeKey())) {
            wrapper.ne(PermissionMediaAndAttached::getTypeKey, FileTypeEnum.PERMISSION_HEAD_ICON.key);
        }
        wrapper.eq(PermissionMediaAndAttached::getFkTableName, attachedVo.getFkTableName());
        wrapper.eq(PermissionMediaAndAttached::getFkTableId, attachedVo.getFkTableId());
        wrapper.orderByDesc(PermissionMediaAndAttached::getIndexKey);
        return wrapper;
    }


    //修改為feign調用
    private List<MediaAndAttachedVo> getFileMedia(List<PermissionMediaAndAttached> mediaAndAttachedList) {
        if (GeneralTool.isEmpty(mediaAndAttachedList)) {
            return null;
        }
        //获取guid集合
        List<String> guidList = mediaAndAttachedList.stream().map(PermissionMediaAndAttached::getFkFileGuid).collect(Collectors.toList());
        //转换成DTO
        List<MediaAndAttachedVo> mediaAndAttachedVos = mediaAndAttachedList.stream()
                .map(mediaAndAttached -> BeanCopyUtils.objClone(mediaAndAttached, MediaAndAttachedVo::new)).collect(Collectors.toList());
        //根据GUID服务调用查询
//        ListResponseBo responseBo = fileCenterClient.findFileByGuid(guidList, LoggerModulesConsts.PERMISSIONCENTER);
        Map<String, List<String>> guidListWithTypeMap = new HashMap<>();
        guidListWithTypeMap.put(LoggerModulesConsts.PERMISSIONCENTER, guidList);
        Result<List<FileDto>> result = fileCenterClient.findFileByGuid(guidListWithTypeMap);
        List<FileDto> fileDtos = new ArrayList<>();
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            JSONArray jsonArray = JSONArray.fromObject(result.getData());
            fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
        }

        //返回结果不为空时
        List<MediaAndAttachedVo> collect = null;
        if (GeneralTool.isNotEmpty(fileDtos)) {
            //遍历查询GUID是否一致
            List<FileDto> finalFileDtos = fileDtos;
            collect = mediaAndAttachedVos.stream().map(mediaAndAttachedDto -> finalFileDtos
                    .stream()
                    .filter(fileDto -> fileDto.getFileGuid().equals(mediaAndAttachedDto.getFkFileGuid()))
                    .findFirst()
                    .map(fileDto -> {
                        mediaAndAttachedDto.setFilePath(fileDto.getFilePath());
                        mediaAndAttachedDto.setFileNameOrc(fileDto.getFileNameOrc());
                        mediaAndAttachedDto.setTypeValue(FileTypeEnum.getValue(mediaAndAttachedDto.getTypeKey()));
                        mediaAndAttachedDto.setFileKey(fileDto.getFileKey());
                        mediaAndAttachedDto.setFkTableName(null);
                        return mediaAndAttachedDto;
                    }).orElse(null)
            ).collect(Collectors.toList());
        }
        return collect;
    }

}
