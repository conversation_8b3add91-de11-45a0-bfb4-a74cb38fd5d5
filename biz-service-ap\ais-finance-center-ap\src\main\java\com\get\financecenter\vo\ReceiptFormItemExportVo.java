package com.get.financecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2021/12/29
 * @TIME: 15:25
 * @Description:收款单子项导出类
 **/
@Data
public class ReceiptFormItemExportVo implements Serializable {
    /**
     * 收款单编号（系统生成）
     */
    @ApiModelProperty(value = "收款单编号")
    private String numSystem;
    /**
     * 目标名称
     */
    @ApiModelProperty(value = "目标名称")
    private String targetName;

    /**
     * 收款金额（折合人民币）
     */
    @ApiModelProperty(value = "收款总金额")
    private BigDecimal amountRmb;

    /**
     * 付款银行
     */
    @ApiModelProperty(value = "收款银行")
    private String fkBankAccountName;

    @ApiModelProperty(value = "收款日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String receiptDate;
    /**
     * 应收类型名称
     */
    @ApiModelProperty(value = "应收计划类型名称")
    private String fkTypeName;

    /**
     * 应收目标对象
     */
    @ApiModelProperty(value = "应收目标对象")
    private String targetNames;
    /**
     * 应收币种名
     */
    @ApiModelProperty(value = "应收币种")
    private String receivablePlanCurrencyName;
    /**
     * 总应收金额
     */
    @ApiModelProperty(value = "总应收金额")
    private BigDecimal receivablePlanAmount;
    /**
     * 实收币种
     */
    @ApiModelProperty(value = "实收币种")
    private String receiptFormCurrency;
    /**
     * 实收金额
     */
    @ApiModelProperty(value = "实收金额")
    private BigDecimal amountReceipt;
    /**
     * 手续费
     */
    @ApiModelProperty(value = "手续费")
    private BigDecimal serviceFee;
    /**
     * 汇率（折合应收币种汇率）
     */
    @ApiModelProperty(value = "折合应收汇率")
    private BigDecimal exchangeRateReceivable;
    /**
     * 折合金额
     */
    @ApiModelProperty(value = "折合金额")
    private BigDecimal amountReceivable;
    /**
     * 汇率调整（可正可负，为平衡计算应收金额）
     */
    @ApiModelProperty(value = "汇率调整金额")
    private BigDecimal amountExchangeRate;
    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String gmtCreateUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private String gmtCreateDate;
}
