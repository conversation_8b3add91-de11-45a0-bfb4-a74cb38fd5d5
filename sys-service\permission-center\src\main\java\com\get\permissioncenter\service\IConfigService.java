package com.get.permissioncenter.service;

import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.permissioncenter.dto.ConfigDto;
import com.get.permissioncenter.vo.CompanyConfigValueVo;
import com.get.permissioncenter.vo.CompanyConfigVo;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.permissioncenter.entity.Config;
import com.get.permissioncenter.dto.CompanyConfigInfoDto;

import java.util.List;
import java.util.Map;

/**
 * @author: jack
 * @create: 2020/6/8
 * @verison: 1.0
 * @description: 系统配置业务接口
 */
public interface IConfigService extends BaseService<Config> {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    ConfigVo findConfigById(Long id);

    /**
     * 列表数据
     *
     * @param configDto
     * @param page
     * @return
     */
    List<ConfigVo> getConfigs(ConfigDto configDto, Page page);

    /**
     * 修改
     *
     * @param configDto
     * @return
     */
    ConfigVo updateConfig(ConfigDto configDto);

    /**
     * 保存
     *
     * @param configDto
     * @return
     */
    Long addConfig(ConfigDto configDto);

    /**
     * @return com.get.systemcenter.vo.ConfigVo
     * @Description: key查找对应的配置
     * @Param [key]
     * <AUTHOR>
     **/
    String getConfigValueByConfigKey(String key);


    /**
     * @Description: 配置key和value1查询对象
     * @Author: Jerry
     * @Date:17:55 2021/9/15
     */
    ConfigVo getConfigValueByConfigKeyAndValue(String key, Long value1);

    /**
     * @Description: 配置key查询value（AES加密key，需解密）
     * @Author: Jerry
     * @Date:12:10 2021/9/15
     */
    String getConfigValueByConfigKeyAndAESKey(String key, String AESKey);

    /**
     * @return java.util.List<java.lang.String>
     * @Description: 查询类型下拉框
     * @Param []
     * <AUTHOR>
     **/
    List<String> getGroupSelect();

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * feign调用 查询配置信息
     *
     * @Date 14:40 2021/8/16
     * <AUTHOR>
     */
    ConfigVo getConfigByKey(String key);

    /**
     *  获取公司业务配置信息
     * @param key
     * @param column value1 OR value2
     * @return
     */
    CompanyConfigVo getCompanyConfigInfo(String key, Integer column);

    /**
     * 获取公司配置
     * @param configKey
     * @return
     */
    ListResponseBo<CompanyConfigInfoDto> getCompanySettlementConfigInfo(String configKey);

    /**
     * 获得项目成员限制配置
     * @param configKey
     * @return
     */
    CompanyConfigValueVo getProjectLimitConfigKey(String configKey);

    Map<Long, Integer> getCompanySettlementConfigInfoMap(String configKey);
}
