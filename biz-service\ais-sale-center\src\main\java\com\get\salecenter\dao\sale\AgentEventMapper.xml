<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.AgentEventMapper">
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.AgentEvent">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_agent_id" jdbcType="BIGINT" property="fkAgentId"/>
        <result column="fk_agent_event_type_id" jdbcType="BIGINT" property="fkAgentEventTypeId"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, fk_agent_id, fk_agent_event_type_id, description, gmt_create, gmt_create_user, 
    gmt_modified, gmt_modified_user
  </sql>
    <insert id="insertSelective" parameterType="com.get.salecenter.entity.AgentEvent" keyProperty="id"
            useGeneratedKeys="true">
        insert into m_agent_event
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkAgentId != null">
                fk_agent_id,
            </if>
            <if test="fkAgentEventTypeId != null">
                fk_agent_event_type_id,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkAgentId != null">
                #{fkAgentId,jdbcType=BIGINT},
            </if>
            <if test="fkAgentEventTypeId != null">
                #{fkAgentEventTypeId,jdbcType=BIGINT},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="findAgentEventsById" resultType="com.get.salecenter.vo.AgentEventVo">
        select a.id, a.fk_agent_id, a.fk_agent_event_type_id, a.description, a.gmt_create, a.gmt_create_user,
        a.gmt_modified, a.gmt_modified_user,t.type_name
        from m_agent_event a
        left join u_agent_event_type t on a.fk_agent_event_type_id=t.id
        where 1=1
        <if test="agentEventDto.fkAgentEventTypeId != null and agentEventDto.fkAgentEventTypeId != ''">
            and fk_agent_event_type_id = #{agentEventDto.fkAgentEventTypeId}
        </if>
        <if test="agentEventDto.keyWord != null and agentEventDto.keyWord != ''">
            and description like #{agentEventDto.keyWord}
        </if>
        and a.fk_agent_id=#{agentEventDto.fkAgentId,jdbcType=BIGINT};
    </select>

    <select id="isExistByAgentId" resultType="java.lang.Boolean">
      SELECT IFNULL(max(id),0) id from m_agent_event where fk_agent_id=#{agentId}
    </select>
</mapper>