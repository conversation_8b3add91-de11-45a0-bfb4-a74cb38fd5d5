package com.get.aismail.dto;

import com.get.aismail.entity.AreaCountry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "国家返回类")
public class AreaCountryDto extends AreaCountry {
    /**
     * 国家对应的州省
     */
    @ApiModelProperty(value = "国家对应的州省")
    List<AreaStateDto> areaStateDtos;
    /**
     * 媒体附件-国旗
     */
    @ApiModelProperty(value = "媒体附件-国旗")
    List<MediaAndAttachedDto> nationalFlags;
    /**
     * 媒体附件-国徽
     */
    @ApiModelProperty(value = "媒体附件-国徽")
    List<MediaAndAttachedDto> nationalEmblems;
    /**
     * 国家下对应的新闻列表
     */
    @ApiModelProperty(value = "国家下对应的新闻列表")
    List<NewsDto> newsDtos;
    /**
     * 表示国家类型
     */
    @ApiModelProperty(value = "表示国家类型")
    private String type;
    /**
     * 全称
     */
//    @ApiModelProperty(value = "全称")
    @ApiModelProperty(value = "英文全称")
    private String fullName;
    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String fkCurrencyTypeName;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;
    /**
     * 公开对象名称
     */
    @ApiModelProperty(value = "公开对象名称")
    private String publicLevelName;

    /**
     * 业务区域中文名
     */
    @ApiModelProperty(value = "业务区域中文名")
    private String businessAreaName;

    @ApiModelProperty("区号的全称")
    private String areaCodeValue;
}
