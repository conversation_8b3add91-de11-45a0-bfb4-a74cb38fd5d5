package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("u_cancel_offer_reason")
public class CancelOfferReason extends BaseEntity implements Serializable {
    /**
     * 原因名称
     */
    @ApiModelProperty(value = "原因名称")
    private String reasonName;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    private static final long serialVersionUID = 1L;
}