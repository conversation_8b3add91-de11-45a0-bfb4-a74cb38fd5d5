package com.get.platformconfigcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.platformconfigcenter.dao.appissue.IssueToRpaFieldMappingMapper;
import com.get.platformconfigcenter.vo.IssueToRpaFieldMappingVo;
import com.get.platformconfigcenter.entity.IssueToRpaFieldMapping;
import com.get.platformconfigcenter.service.IssueToRpaFieldMappingService;
import com.get.platformconfigcenter.dto.IssueToRpaFieldMappingDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　　　◆　　　　　　　　　　　　◆　　　　　　　　　　　　　　◆　　　　　　　　　　　　　◆◆　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　◆◆◆　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆
 * 　　　◆◆　　　　　◆◆　　　　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆◆◆◆
 * 　　　◆◆◆　　　◆◆◆　　　　　　　◆◆　　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆◆
 * 　　　　◆◆◆　◆◆◆　　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆
 * 　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　　◆◆
 * 　　　　　　◆◆◆　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆
 * 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆◆
 * <p>
 * Time: 11:08
 * Date: 2021/11/8
 * Description:数据字段映射管理业务类实现类
 */
@Service
public class IssueToRpaFieldMappingServiceImpl implements IssueToRpaFieldMappingService {

//    @Resource
//    private IssueToRpaFieldMappingMapper issueToRpaFieldMappingMapper;
//    @Resource
//    private IInstitutionCenterClient institutionCenterClient;
//    @Resource
//    private UtilService utilService;


    //TODO 注释ISSUE相关功能 lucky  2024/12/23
    /**
     * @Description: 数据字段映射列表数据
     * @Author: Jerry
     * @Date:11:09 2021/11/8
     */
//    @Override
//    public List<IssueToRpaFieldMappingVo> datas(IssueToRpaFieldMappingDto issueToRpaFieldMappingDto, Page page) {
////        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
////        Example example = new Example(IssueToRpaFieldMapping.class);
////        Example.Criteria criteria = example.createCriteria();
////        if(GeneralTool.isNotEmpty(issueToRpaFieldMappingDto.getFkInstitutionId())){
////            criteria.andEqualTo("fkInstitutionId",issueToRpaFieldMappingDto.getFkInstitutionId());
////        }
////        if(GeneralTool.isNotEmpty(issueToRpaFieldMappingDto.getFkMajorLevelId())){
////            criteria.andEqualTo("fkMajorLevelId",issueToRpaFieldMappingDto.getFkMajorLevelId());
////        }
////        if(GeneralTool.isNotEmpty(issueToRpaFieldMappingDto.getIssueTableName())){
////            criteria.andLike("issueTableName","%"+issueToRpaFieldMappingDto.getIssueTableName()+"%");
////        }
////        if(GeneralTool.isNotEmpty(issueToRpaFieldMappingDto.getIssueFieldName())){
////            criteria.andLike("issueFieldName","%"+issueToRpaFieldMappingDto.getIssueFieldName()+"%");
////        }
////        if(GeneralTool.isNotEmpty(issueToRpaFieldMappingDto.getRpaFieldName())){
////            criteria.andLike("rpaFieldName","%"+issueToRpaFieldMappingDto.getRpaFieldName()+"%");
////        }
////        example.setOrderByClause("issue_table_name,issue_field_name");
////        List<IssueToRpaFieldMapping> issueToRpaFieldMappings = issueToRpaFieldMappingMapper.selectByExample(example);
////        if(GeneralTool.isEmpty(issueToRpaFieldMappings)){
////            return new ArrayList<>();
////        }
////        page.restPage(issueToRpaFieldMappings);
//
//
//        LambdaQueryWrapper<IssueToRpaFieldMapping> lambdaQueryWrapper = Wrappers.<IssueToRpaFieldMapping>lambdaQuery();
//        if (GeneralTool.isNotEmpty(issueToRpaFieldMappingDto.getFkInstitutionId())) {
//            lambdaQueryWrapper.eq(IssueToRpaFieldMapping::getFkInstitutionId, issueToRpaFieldMappingDto.getFkInstitutionId());
//        }
//        if (GeneralTool.isNotEmpty(issueToRpaFieldMappingDto.getFkMajorLevelId())) {
//            lambdaQueryWrapper.eq(IssueToRpaFieldMapping::getFkMajorLevelId, issueToRpaFieldMappingDto.getFkMajorLevelId());
//        }
//        if (GeneralTool.isNotEmpty(issueToRpaFieldMappingDto.getIssueTableName())) {
//            lambdaQueryWrapper.like(IssueToRpaFieldMapping::getIssueTableName, issueToRpaFieldMappingDto.getIssueTableName());
//        }
//        if (GeneralTool.isNotEmpty(issueToRpaFieldMappingDto.getIssueFieldName())) {
//            lambdaQueryWrapper.like(IssueToRpaFieldMapping::getIssueFieldName, issueToRpaFieldMappingDto.getIssueFieldName());
//        }
//        if (GeneralTool.isNotEmpty(issueToRpaFieldMappingDto.getRpaFieldName())) {
//            lambdaQueryWrapper.like(IssueToRpaFieldMapping::getRpaFieldName, issueToRpaFieldMappingDto.getRpaFieldName());
//        }
//        lambdaQueryWrapper.orderByAsc(IssueToRpaFieldMapping::getIssueTableName, IssueToRpaFieldMapping::getIssueFieldName);
//        List<IssueToRpaFieldMapping> issueToRpaFieldMappings = issueToRpaFieldMappingMapper.selectList(lambdaQueryWrapper);
//        if (GeneralTool.isEmpty(issueToRpaFieldMappings)) {
//            return new ArrayList<>();
//        }
//
//
//        //获取学校ids
//        Set<Long> fkInstitutionIds = issueToRpaFieldMappings.stream().map(IssueToRpaFieldMapping::getFkInstitutionId).collect(Collectors.toSet());
//        fkInstitutionIds.removeIf(Objects::isNull);
//        //获取专业等级ids
//        Set<Long> fkMajorLevelIds = issueToRpaFieldMappings.stream().map(IssueToRpaFieldMapping::getFkMajorLevelId).collect(Collectors.toSet());
//        fkMajorLevelIds.removeIf(Objects::isNull);
//        //根据学校ids获取学校名称
//        Map<Long, String> institutionNamesByIds = new HashMap<>();
//        //111111
//        if (GeneralTool.isNotEmpty(fkInstitutionIds)) {
//            Result<Map<Long, String>> resultinstitutionNamesByIds = institutionCenterClient.getInstitutionNamesByIds(fkInstitutionIds);
//            if (resultinstitutionNamesByIds.isSuccess()) {
//                institutionNamesByIds = resultinstitutionNamesByIds.getData();
//            }
//        }
////        //根据专业等级ids获取专业等级名称
//        Map<Long, String> majorLevelNamesByIds = new HashMap<>();
//        //111111
//        if (GeneralTool.isNotEmpty(fkMajorLevelIds)) {
//            Result<Map<Long, String>> resultmajorLevelNamesByIds = institutionCenterClient.getMajorLevelNamesByIds(fkMajorLevelIds);
//            if (resultmajorLevelNamesByIds.isSuccess()) {
//                majorLevelNamesByIds = resultmajorLevelNamesByIds.getData();
//            }
//        }
//        List<IssueToRpaFieldMappingVo> issueToRpaFieldMappingVos = new ArrayList<>();
//        for (IssueToRpaFieldMapping issueToRpaFieldMapping : issueToRpaFieldMappings) {
//            IssueToRpaFieldMappingVo issueToRpaFieldMappingVo = BeanCopyUtils.objClone(issueToRpaFieldMapping, IssueToRpaFieldMappingVo::new);
//            issueToRpaFieldMappingVo.setFkInstitutionName(institutionNamesByIds.get(issueToRpaFieldMapping.getFkInstitutionId()));
//            issueToRpaFieldMappingVo.setFkMajorLevelName(majorLevelNamesByIds.get(issueToRpaFieldMapping.getFkMajorLevelId()));
//            issueToRpaFieldMappingVos.add(issueToRpaFieldMappingVo);
//        }
//        return issueToRpaFieldMappingVos;
//    }

    //TODO 注释ISSUE相关功能 lucky  2024/12/23
    /**
     * @Description: 新增数据字段映射
     * @Author: Jerry
     * @Date:11:09 2021/11/8
     */
//    @Override
//    public void add(IssueToRpaFieldMappingDto issueToRpaFieldMappingDto) {
//        if (GeneralTool.isEmpty(issueToRpaFieldMappingDto)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
//        }
//        IssueToRpaFieldMapping issueToRpaFieldMapping = BeanCopyUtils.objClone(issueToRpaFieldMappingDto, IssueToRpaFieldMapping::new);
//        utilService.updateUserInfoToEntity(issueToRpaFieldMapping);
//        issueToRpaFieldMappingMapper.insert(issueToRpaFieldMapping);
//    }
//
//
//    /**
//     * @Description: 更新数据字段映射
//     * @Author: Jerry
//     * @Date:11:09 2021/11/8
//     */
//    @Override
//    public void update(IssueToRpaFieldMappingDto issueToRpaFieldMappingDto) {
//        if (GeneralTool.isEmpty(issueToRpaFieldMappingDto)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
//        }
//        IssueToRpaFieldMapping issueToRpaFieldMappingBefore = issueToRpaFieldMappingMapper.selectById(issueToRpaFieldMappingDto.getId());
//        if (GeneralTool.isEmpty(issueToRpaFieldMappingBefore)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        }
//        IssueToRpaFieldMapping issueToRpaFieldMapping = BeanCopyUtils.objClone(issueToRpaFieldMappingDto, IssueToRpaFieldMapping::new);
//        utilService.updateUserInfoToEntity(issueToRpaFieldMapping);
//        issueToRpaFieldMappingMapper.updateById(issueToRpaFieldMapping);
//    }
//
//
//    /**
//     * @Description: 数据字段映射详情
//     * @Author: Jerry
//     * @Date:11:09 2021/11/8
//     */
//    @Override
//    public IssueToRpaFieldMappingVo detail(Long id) {
//        if (GeneralTool.isEmpty(id)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
//        IssueToRpaFieldMapping issueToRpaFieldMapping = issueToRpaFieldMappingMapper.selectById(id);
//        if (GeneralTool.isEmpty(issueToRpaFieldMapping)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        }
//        IssueToRpaFieldMappingVo issueToRpaFieldMappingVo = BeanCopyUtils.objClone(issueToRpaFieldMapping, IssueToRpaFieldMappingVo::new);
//        //111111
//        if (GeneralTool.isNotEmpty(issueToRpaFieldMappingVo.getFkInstitutionId())) {
//            Result<String> institutionNames = institutionCenterClient.getInstitutionNamesById(issueToRpaFieldMappingVo.getFkInstitutionId());
//            if (institutionNames.isSuccess()) {
//                issueToRpaFieldMappingVo.setFkInstitutionName(institutionNames.getData());
//            }
//
//        }
//        if (GeneralTool.isNotEmpty(issueToRpaFieldMappingVo.getFkMajorLevelId())) {
//            Result<String> majorLevelNames = institutionCenterClient.getMajorLevelNamesById(issueToRpaFieldMappingVo.getFkMajorLevelId());
//            if (majorLevelNames.isSuccess()) {
//                issueToRpaFieldMappingVo.setFkMajorLevelName(majorLevelNames.getData());
//            }
//
//        }
//        return issueToRpaFieldMappingVo;
//    }
//
//
//    /**
//     * @Description: 删除数据字段映射
//     * @Author: Jerry
//     * @Date:11:09 2021/11/8
//     */
//    @Override
//    public void delete(Long id) {
//        if (GeneralTool.isEmpty(id)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
//        issueToRpaFieldMappingMapper.deleteById(id);
//    }
}
