package com.get.salecenter.service;


import com.get.salecenter.dto.ContactPersonCompanyDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/9/15
 * @TIME: 16:16
 * @Description:
 **/
public interface IContactPersonCompanyService {

    /**
     * 安全配置
     *
     * @param contactPersonCompanyDtos
     * @
     */
    void editContactPersonCompany(List<ContactPersonCompanyDto> contactPersonCompanyDtos);

    /**
     * @return java.lang.Long
     * @Description: 添加
     * @Param [relation]
     * <AUTHOR>
     */
    Long addRelation(ContactPersonCompanyDto relation);

    void addBatchRelation(List<ContactPersonCompanyDto> relations);
    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据公司查询联系人ids
     * @Param [companyId]
     * <AUTHOR>
     */
    List<Long> getRelationByCompanyId(List<Long> companyId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据联系人id查询公司ids
     * @Param [contactPersonId]
     * <AUTHOR>
     */
    List<Long> getRelationByContactId(Long contactPersonId);

    /**
     * 根据联系人ids查询公司ids
     *
     * @param contactPersonIds
     * @return
     */
    Map<Long, Set<Long>> getRelationByContactIds(Set<Long> contactPersonIds);

    /**
     * 删除联系人-公司关系
     *
     * @Date 10:41 2021/7/5
     * <AUTHOR>
     */
    void deletePersonRelation(Long personId);
}
