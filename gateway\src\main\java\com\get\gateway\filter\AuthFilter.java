package com.get.gateway.filter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.get.core.jwt.JwtUtil;
import com.get.core.jwt.props.JwtProperties;
import com.get.core.start.constant.TokenConstant;
import com.get.gateway.props.AuthProperties;
import com.get.gateway.provider.AuthProvider;
import com.get.gateway.provider.RequestProvider;
import com.get.gateway.provider.ResponseProvider;
import io.jsonwebtoken.Claims;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;

/**
 * 鉴权认证
 */
@Slf4j
@Component
@AllArgsConstructor
public class AuthFilter implements GlobalFilter, Ordered {
    private final AuthProperties authProperties;
    private final ObjectMapper objectMapper;
    private final JwtProperties jwtProperties;
    private final AntPathMatcher antPathMatcher = new AntPathMatcher();

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        //校验 Token 放行
        String originalRequestUrl = RequestProvider.getOriginalRequestUrl(exchange);
        String path = exchange.getRequest().getURI().getPath();
        if (isSkip(path) || isSkip(originalRequestUrl)) {
            return chain.filter(exchange);
        }
        //校验 Token 合法性，如没有at则提示：无效的令牌
        ServerHttpResponse resp = exchange.getResponse();
        String headerToken = exchange.getRequest().getHeaders().getFirst(AuthProvider.AUTH_KEY);
        String paramToken = exchange.getRequest().getQueryParams().getFirst(AuthProvider.AUTH_KEY);
        if (StringUtils.isBlank(headerToken) && StringUtils.isBlank(paramToken)) {
            return unAuth(resp, "无效的令牌");
        }
        //如
        String auth = StringUtils.isBlank(headerToken) ? paramToken : headerToken;
//		System.out.println("===================>需要校验token的auth："+auth);
        String token = JwtUtil.getToken(auth);
        Claims claims = JwtUtil.parseJWT(token);
        if (null == claims) {
            return unAuth(resp, "失效的令牌");
        }
        String platformType = (String) claims.get("platformType");
        //schoolGate登录平台校验  || !"SCHOOL_GATE".equals(platformType) || !"APP_SCHOOL_GATE".equals(platformType)
        if (originalRequestUrl.contains("school-gate-center")||path.contains("schoolGate")){
            if ( !platformType.equals("") && !platformType.contains("SCHOOL_GATE")){
                return unAuth(resp, "失效的令牌");
            }
        }
//		System.out.println("===================>需要校验token的auth-token："+token);
//		System.out.println("===================>需要校验token的auth-claims："+claims);
        if (token == null || claims == null) {
            return unAuth(resp, "失效的令牌");
        }
        //判断 Token 状态
        if (jwtProperties.getState()) {
            String userId = String.valueOf(claims.get(TokenConstant.STAFFID));
            String tokenSessionId = String.valueOf(claims.get(TokenConstant.SESSION_ID));
            String accessToken = JwtUtil.getAccessToken(userId,tokenSessionId, token);
            if (!token.equalsIgnoreCase(accessToken)) {
                return unAuth(resp, "失效的令牌");
            }
        }
        return chain.filter(exchange);
    }

    private boolean isSkip(String path) {
        return AuthProvider.getDefaultSkipUrl().stream().anyMatch(pattern -> antPathMatcher.match(pattern, path))
                || authProperties.getSkipUrl().stream().anyMatch(pattern -> antPathMatcher.match(pattern, path));
    }

    private Mono<Void> unAuth(ServerHttpResponse resp, String msg) {
        resp.setStatusCode(HttpStatus.UNAUTHORIZED);
        resp.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        String result = "";
        try {
            result = objectMapper.writeValueAsString(ResponseProvider.unAuth(msg));
        } catch (JsonProcessingException e) {
            log.error(e.getMessage(), e);
        }
        DataBuffer buffer = resp.bufferFactory().wrap(result.getBytes(StandardCharsets.UTF_8));
        return resp.writeWith(Flux.just(buffer));
    }


    @Override
    public int getOrder() {
        return -100;
    }

}
