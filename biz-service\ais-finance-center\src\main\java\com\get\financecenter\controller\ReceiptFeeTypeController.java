package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.financecenter.dto.ReceiptFeeTypeDto;
import com.get.financecenter.service.IReceiptFeeTypeService;
import com.get.financecenter.vo.BaseSelectVo;
import com.get.financecenter.vo.ReceiptFeeTypeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @DATE: 2021/12/6
 * @TIME: 15:42
 * @Description:
 **/
@Api(tags = "收款费用类型管理")
@RestController
@RequestMapping("finance/receiptFeeType")
public class ReceiptFeeTypeController {
    @Resource
    private IReceiptFeeTypeService IReceiptFeeTypeService;

    /**
     * 列表数据
     *
     * @param page
     * @return
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/收款费用类型管理/查询收款费用类型")
    @PostMapping("datas")
    public ResponseBo<ReceiptFeeTypeVo> datas(@RequestBody SearchBean<ReceiptFeeTypeDto> page) {
        List<ReceiptFeeTypeVo> datas = IReceiptFeeTypeService.getReceiptFeeTypes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/收款费用类型管理/收款费用类型详情")
    @GetMapping("/{id}")
    public ResponseBo<ReceiptFeeTypeVo> detail(@PathVariable("id") Long id) {
        ReceiptFeeTypeVo data = IReceiptFeeTypeService.findReceiptFeeTypeById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 批量新增信息
     *
     * @param receiptFeeTypeDtos
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/收款费用类型管理/批量新增收款费用类型")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(ReceiptFeeTypeDto.Add.class)  ValidList<ReceiptFeeTypeDto> receiptFeeTypeDtos) {
        IReceiptFeeTypeService.batchAdd(receiptFeeTypeDtos);
        return SaveResponseBo.ok();
    }

    /**
     * 新增信息
     *
     * @param receiptFeeTypeDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/收款费用类型管理/新增收款费用类型")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(ReceiptFeeTypeDto.Add.class)  ReceiptFeeTypeDto receiptFeeTypeDto) {
        return SaveResponseBo.ok(IReceiptFeeTypeService.addReceiptFeeType(receiptFeeTypeDto));
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/收款费用类型管理/删除收款费用类型")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        IReceiptFeeTypeService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param receiptFeeTypeDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/收款费用类型管理/更新收款费用类型")
    @PostMapping("update")
    public ResponseBo<ReceiptFeeTypeVo> update(@RequestBody @Validated(ReceiptFeeTypeDto.Update.class)  ReceiptFeeTypeDto receiptFeeTypeDto) {
        return UpdateResponseBo.ok(IReceiptFeeTypeService.updateReceiptFeeType(receiptFeeTypeDto));
    }

//    /**
//     * 上移下移
//     *
//     * @param receiptFeeTypeDtos
//     * @return
//     * @
//     */
//    @ApiOperation(value = "上移下移", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/收款费用类型管理/移动顺序")
//    @PostMapping("movingOrder")
//    public ResponseBo movingOrder(@RequestBody List<ReceiptFeeTypeDto> receiptFeeTypeDtos) {
//        IReceiptFeeTypeService.sort(receiptFeeTypeDtos);
//        return ResponseBo.ok();
//    }

    @ApiOperation(value = "排序（拖拽）", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/收款费用类型管理/移动顺序（拖拽）")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestParam("start") Integer start , @RequestParam("end") Integer end) {
        IReceiptFeeTypeService.movingOrder(start, end);
        return ResponseBo.ok();
    }

    /**
     * 收款费用类型下拉
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "收款费用类型下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/收款费用类型管理/收款费用类型下拉")
    @GetMapping("getOtherTypeSelect")
    public ResponseBo<BaseSelectVo> getReceiptFeeTypeSelect() {
        return new ListResponseBo<>(IReceiptFeeTypeService.getReceiptFeeTypeSelect());
    }

    @ApiOperation("收款费用类型组别Key下拉")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/收款费用类型管理/收款费用类型组别Key下拉")
    @GetMapping("getReceiptFeeTypeGroup")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<ReceiptFeeTypeVo> getReceiptFeeTypeGroup() {
        return new ListResponseBo<>(IReceiptFeeTypeService.getReceiptFeeTypeGroup());
    }

}
