package com.get.permissioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.filecenter.dto.FileDto;
import com.get.permissioncenter.dto.MediaAndAttachedDto;
import com.get.permissioncenter.service.IMediaAndAttachedService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/7/14
 * @TIME: 18:17
 * @Description: 附件管理控制层
 **/

@Api(tags = "附件管理")
@RestController
@RequestMapping("permission/media")
public class MediaAndAttachedController {
    @Resource
    private IMediaAndAttachedService attachedService;


    /**
     * 删除
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DELETE, description = "权限中心/附件管理/删除资源")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        attachedService.delete(id);
        return ResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "上传文件接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/附件管理/上传文件")
    @PostMapping("upload")
    public ResponseBo upload(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files) {
        List<FileDto> upload = attachedService.upload(files);
        return new ResponseBo<>(upload);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "上传附件接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/附件管理/上传文件")
    @PostMapping("uploadAttached")
    public ResponseBo uploadAttached(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files) {
        List<FileDto> upload = attachedService.uploadAttached(files);
        return new ResponseBo<>(upload);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 员工附件类型
     * @Param []
     * <AUTHOR>
     */
    @ApiOperation(value = "员工附件类型", notes = "")
    @GetMapping("findStaffMediaType")
    public ResponseBo findStaffMediaType() {
        List<Map<String, Object>> datas = attachedService.findStaffMediaType();
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 人事事件附件类型
     * @Param []
     * <AUTHOR>
     */
    @ApiOperation(value = "人事事件附件类型", notes = "")
    @GetMapping("findHrEventMediaType")
    public ResponseBo findHrEventMediaType() {
        List<Map<String, Object>> datas = attachedService.findHrEventMediaType();
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 合同附件类型
     * @Param []
     * <AUTHOR>
     */
    @ApiOperation(value = "合同附件类型", notes = "")
    @GetMapping("findContractMediaType")
    public ResponseBo findContractMediaType() {
        List<Map<String, Object>> datas = attachedService.findContractMediaType();
        return new ListResponseBo<>(datas);
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 上移下移
     * @Param [mediaAttachedVos]
     * <AUTHOR>
     **/
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/附件管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<MediaAndAttachedDto> mediaAttachedVos) {
        attachedService.movingOrder(mediaAttachedVos);
        return ResponseBo.ok();
    }
}
