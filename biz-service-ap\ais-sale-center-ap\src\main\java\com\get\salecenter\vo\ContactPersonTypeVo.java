package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.ContactPersonType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * @author: <PERSON>
 * @create: 2020/7/22 16:01
 * @verison: 1.0
 * @description:
 */
@Data
public class ContactPersonTypeVo extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    private String typeName;
    /**
     * 类型Key，枚举类型Key
     */
    @ApiModelProperty(value = "类型Key，枚举类型Key")
    private String typeKey;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    private Integer viewOrder;

    //===========实体类ContactPersonType==============

}
