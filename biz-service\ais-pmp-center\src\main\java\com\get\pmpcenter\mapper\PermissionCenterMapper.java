package com.get.pmpcenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.get.pmpcenter.vo.common.CompanyVo;
import com.get.pmpcenter.vo.common.StaffVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * ais_institution_center
 */
@Mapper
@DS("permission")
public interface PermissionCenterMapper {

    /**
     * 获取公司列表
     *
     * @return
     */
    List<CompanyVo> getCompanyList(@Param("companyIds") List<Long> companyIds);


    /**
     * 获取员工列表
     * @param staffIds
     * @return
     */
    List<StaffVo> getStaffList(@Param("staffIds") List<Long> staffIds,Long companyId);


    /**
     * 根据登录名获取员工列表
     * @param loginIds
     * @return
     */
    List<StaffVo> getStaffListByLoginIds(@Param("loginIds") List<String> loginIds);


    /**
     * 获取员工国家列表
     * @param staffId
     * @return
     */
    List<Long> getStaffCountryIds(@Param("staffId") Long staffId);

    /**
     * 根据登录名获取员工列表
     * @param loginIds
     * @return
     */
    List<StaffVo> getStaffByLoginIds(@Param("loginIds") List<String> loginIds);

}
