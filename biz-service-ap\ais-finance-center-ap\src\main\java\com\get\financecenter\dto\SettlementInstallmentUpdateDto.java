package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 佣金结算更新Dto
 */
@Data
public class SettlementInstallmentUpdateDto {

    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "应付计划Id")
    private Long fkPayablePlanId;

    @ApiModelProperty(value = "应收计划Id")
    private Long fkReceivablePlanId;

    @ApiModelProperty(value = "应收金额")
    private BigDecimal receivableAmount;

    @ApiModelProperty(value = "应付金额")
    private BigDecimal payableAmount;

    @ApiModelProperty(value = "应收币种编号")
    private String receivableCurrencyTypeNum;

    @ApiModelProperty(value = "应付币种编号")
    private String payableCurrencyTypeNum;

    @ApiModelProperty(value = "对冲金额")
    private BigDecimal hedgeAmount;


}
