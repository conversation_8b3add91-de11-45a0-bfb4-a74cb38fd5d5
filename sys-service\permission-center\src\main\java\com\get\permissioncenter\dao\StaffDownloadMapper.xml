<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.get.permissioncenter.dao.StaffDownloadMapper">

    <insert id="insertSelective" parameterType="com.get.permissioncenter.entity.StaffDownload" keyProperty="id" useGeneratedKeys="true">
        insert into m_staff_download
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkStaffId != null">
                fk_staff_id,
            </if>
            <if test="fkFileGuid != null">
                fk_file_guid,
            </if>
            <if test="optKey != null">
                opt_key,
            </if>
            <if test="optDescription != null">
                opt_description,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkStaffId != null">
                #{fkStaffId,jdbcType=BIGINT},
            </if>
            <if test="fkFileGuid != null">
                #{fkFileGuid,jdbcType=VARCHAR},
            </if>
            <if test="optKey != null">
                #{optKey,jdbcType=VARCHAR},
            </if>
            <if test="optDescription != null">
                #{optDescription,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateInfo" parameterType="com.get.permissioncenter.entity.StaffDownload">
        UPDATE m_staff_download
        SET
        fk_file_guid = #{fkFileGuid},
        status = #{status},
        remark = #{remark},
        gmt_modified = #{gmtModified},
        gmt_modified_user = #{gmtModifiedUser}
        WHERE id = #{id}
    </update>
    <delete id="deleteByGuid">
        DELETE FROM ais_file_center.m_file_export WHERE file_guid = #{guid}
    </delete>

    <select id="lists" parameterType="com.get.permissioncenter.dto.StaffDownloadDto" resultType="com.get.permissioncenter.vo.StaffDownloadVo">
        SELECT
            m.id,
            m.fk_staff_id,
            m.fk_file_guid,
            m.opt_key,
            m.opt_description,
            m.status,
            m.remark,
            f.file_path,
            f.file_key,
            m.gmt_create,
            m.gmt_create_user,
            m.gmt_modified
        FROM
            m_staff_download m LEFT JOIN ais_file_center.m_file_export f ON f.file_guid= m.fk_file_guid
        WHERE
            fk_staff_id = #{staffDownloadDto.staffId}
        <if test="staffDownloadDto.operation!=null and  staffDownloadDto.operation!=''">
            AND opt_key =#{staffDownloadDto.operation}
        </if>
        <if test="staffDownloadDto.status!=null and  staffDownloadDto.status!=''">
            AND status = #{staffDownloadDto.status}
        </if>
        <if test="staffDownloadDto.beginTime!=null">
            AND DATE_FORMAT(m.gmt_create,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{staffDownloadDto.beginTime},'%Y-%m-%d')
            <if test="staffDownloadDto.endTime!=null">
                AND DATE_FORMAT(m.gmt_create,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{staffDownloadDto.endTime},'%Y-%m-%d')
            </if>
        </if>
        ORDER BY id DESC
    </select>



</mapper>
