package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.KpiPlanStaffLabelMapper;
import com.get.salecenter.dto.KpiPlanStaffLabelDto;
import com.get.salecenter.entity.KpiPlanStaffLabel;
import com.get.salecenter.service.KpiPlanStaffLabelService;
import com.get.salecenter.vo.KpiPlanStaffLabelVo;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 服务实现类
 */
@Service
public class KpiPlanStaffLabelServiceImpl extends ServiceImpl<KpiPlanStaffLabelMapper, KpiPlanStaffLabel> implements KpiPlanStaffLabelService {

    @Resource
    private KpiPlanStaffLabelMapper kpiPlanStaffLabelMapper;

    @Resource
    private UtilService utilService;

    @Override
    public void batchAdd(KpiPlanStaffLabelDto kpiPlanStaffLabelDto) {
        if (GeneralTool.isEmpty(kpiPlanStaffLabelDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        List<Long> fkKpiPlanStaffIdList = kpiPlanStaffLabelDto.getFkKpiPlanStaffIdList();
        String wordLabel = kpiPlanStaffLabelDto.getWordLabel();

        List<KpiPlanStaffLabel> kpiPlanStaffLabelList = Lists.newArrayList();
        for (Long fkKpiPlanStaffId : fkKpiPlanStaffIdList) {
            KpiPlanStaffLabel kpiPlanStaffLabel = new KpiPlanStaffLabel();
            kpiPlanStaffLabel.setFkKpiPlanStaffId(fkKpiPlanStaffId);
            kpiPlanStaffLabel.setWordLabel(wordLabel);
            utilService.setCreateInfo(kpiPlanStaffLabel);
            kpiPlanStaffLabelList.add(kpiPlanStaffLabel);
        }
        if (GeneralTool.isNotEmpty(kpiPlanStaffLabelList)) {
            boolean saveBatch = saveBatch(kpiPlanStaffLabelList, DEFAULT_BATCH_SIZE);
            if (!saveBatch) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
        }
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        int delete = kpiPlanStaffLabelMapper.deleteById(id);
        if (delete <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }
}
