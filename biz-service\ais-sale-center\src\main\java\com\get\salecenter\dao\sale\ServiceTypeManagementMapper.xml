<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.get.salecenter.dao.sale.ServiceTypeManagementMapper">

    <select id="datas" resultType="com.get.salecenter.vo.StudentServiceFeeTypeVo">
        SELECT
            f.*
        FROM
            u_student_service_fee_type f
        LEFT JOIN r_student_service_fee_type_company r ON r.fk_student_service_fee_type_id = f.id
        WHERE
            1=1
            <if test="studentServiceFeeTypeDto.fkCompanyId!=null and studentServiceFeeTypeDto.fkCompanyId!=''">
               AND r.fk_company_id = #{studentServiceFeeTypeDto.fkCompanyId}
            </if>
            <if test="studentServiceFeeTypeDto.typeName!=null and studentServiceFeeTypeDto.typeName!=''">
                AND f.type_name LIKE CONCAT('%',#{studentServiceFeeTypeDto.typeName},'%')
            </if>
        GROUP BY f.id
        ORDER by view_order DESC
    </select>
    <select id="getMaxOrder" resultType="java.lang.Integer">
        SELECT IFNULL(MAX(view_order),0) FROM u_student_service_fee_type
    </select>
    <select id="getServiceTypeList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            t.id,
            t.type_name as name
        FROM
            u_student_service_fee_type t
            LEFT JOIN  r_student_service_fee_type_company a ON a.fk_student_service_fee_type_id = t.id
            WHERE
                a.fk_company_id = #{companyId}
        ORDER BY view_order DESC
    </select>

</mapper>
