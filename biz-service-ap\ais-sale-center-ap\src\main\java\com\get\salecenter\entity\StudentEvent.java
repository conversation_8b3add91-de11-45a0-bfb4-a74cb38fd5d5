package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_student_event")
public class StudentEvent extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 学生Id
     */
    @ApiModelProperty(value = "学生Id")
    @Column(name = "fk_student_id")
    private Long fkStudentId;
    /**
     * 学生事件类型Id
     */
    @ApiModelProperty(value = "学生事件类型Id")
    @Column(name = "fk_student_event_type_id")
    private Long fkStudentEventTypeId;
    /**
     * 事件内容
     */
    @ApiModelProperty(value = "事件内容")
    @Column(name = "description")
    private String description;
}