package com.get.salecenter.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.salecenter.dao.sale.PayablePlanMapper;
import com.get.salecenter.dao.sale.ReceivablePlanMapper;
import com.get.salecenter.vo.PayablePlanNewVo;
import com.get.salecenter.vo.ReceivablePlanNewVo;
import com.get.salecenter.service.DorisService;
import com.get.salecenter.dto.ReceivablePlanNewDto;
import com.get.salecenter.dto.query.PayablePlanNewQueryDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class DorisServiceImpl implements DorisService {
    @Resource
    private ReceivablePlanMapper receivablePlanMapper;
    @Resource
    private PayablePlanMapper payablePlanMapper;

    @DS("saledb-doris")
    @Override
    public List<ReceivablePlanNewVo> getDorisReceivablePlanNewDtos(ReceivablePlanNewDto receivablePlanNewDto, IPage<ReceivablePlanNewVo> iPage) {
        return receivablePlanMapper.getReceivablePlanNew(iPage, receivablePlanNewDto);
    }

    //TODO 数据库连接
//    @DS("saledb-doris")
    @Override
    public List<PayablePlanNewVo> getDorisPayablePlanNewDtos(PayablePlanNewQueryDto payablePlanNewVo, IPage<PayablePlanNewVo> iPage) {
        return payablePlanMapper.payablePlanDatas(iPage, payablePlanNewVo);
    }
}
