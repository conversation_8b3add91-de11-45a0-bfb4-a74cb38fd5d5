package com.get.officecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2022/10/26
 * @TIME: 16:33
 * @Description:
 **/
@Data
public class LeaveDto {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 员工Id列表
     */
    @ApiModelProperty(value = "员工Id列表")
    private Set<Long> fkStaffIds;

    /**
     * 工休类型关键字，枚举，同工休单类型一致
     */
    @ApiModelProperty(value = "工休类型关键字，枚举，同工休单类型一致")
    private String leaveType;

    /**
     * 操作类型关键字，枚举
     */
    @ApiModelProperty(value = " 操作类型关键字，枚举")
    private String optTypeKey;

    /**
     * 考勤开始时间
     */
    @ApiModelProperty("考勤开始时间")
    private Date startDate;

    /**
     * 考勤结束时间
     */
    @ApiModelProperty("考勤结束时间")
    private Date endDate;

    /**
     * 工休申请状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销
     */
    @ApiModelProperty(value = "工休申请状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销")
    private Integer leaveApplicationFormStatus;

    /**
     * true:查询新增时长;false:扣减时长
     */
    @ApiModelProperty("true:查询新增时长;false:扣减时长")
    private Boolean type;
}
