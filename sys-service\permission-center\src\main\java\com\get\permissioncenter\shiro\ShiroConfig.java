//package com.get.permissioncenter.shiro;
//
//import com.get.core.tool.utils.GeneralTool;
//import com.get.permissioncenter.shiro.filter.SystemAuthorizationFilter;
//import com.get.permissioncenter.shiro.filter.SystemUnauthorizedFilter;
//import org.apache.commons.lang.StringUtils;
//import org.apache.shiro.session.SessionListener;
//import org.apache.shiro.spring.LifecycleBeanPostProcessor;
//import org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor;
//import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
//import org.apache.shiro.web.mgt.CookieRememberMeManager;
//import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
//import org.apache.shiro.web.servlet.SimpleCookie;
//import org.apache.shiro.web.session.mgt.DefaultWebSessionManager;
//import org.crazycake.shiro.RedisCacheManager;
//import org.crazycake.shiro.RedisManager;
//import org.crazycake.shiro.RedisSessionDAO;
//import org.springframework.aop.framework.autoproxy.DefaultAdvisorAutoProxyCreator;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.DependsOn;
//
//import javax.servlet.Filter;
//import java.util.*;
//
///**
// * Shiro 配置类
// *
// */
//@Configuration
//public class ShiroConfig {
//
//    @Autowired
//    private FebsProperties febsProperties;
//
//
//    @Value("${spring.redis.host}")
//    private String host;
//
//    @Value("${spring.redis.port}")
//    private int port;
//
//    @Value("${spring.redis.password}")
//    private String password;
//
//    @Value("${spring.redis.timeout}")
//    private int timeout;
//
//    /**
//     * shiro 中配置 redis 缓存
//     *
//     * @return RedisManager
//     */
//    private RedisManager redisManager() {
//        RedisManager redisManager = new RedisManager();
//        // 缓存时间，单位为秒
//        //redisManager.setExpire(febsProperties.getShiro().getExpireIn()); // removed from shiro-redis v3.1.0 api
//        redisManager.setHost(host+":"+port);
///*        redisManager.setPort(port);*/
//        if (GeneralTool.isNotEmpty(password)){
//            redisManager.setPassword(password);
//        }
//        redisManager.setTimeout(timeout);
//        return redisManager;
//    }
//
//    private RedisCacheManager cacheManager() {
//        RedisCacheManager redisCacheManager = new RedisCacheManager();
////        redisCacheManager.setExpire(1000*30);//设置过期时间30秒
//        redisCacheManager.setRedisManager(redisManager());
//        return redisCacheManager;
//    }
//
//    /**
//     * filter工厂
//     * @param securityManager
//     * @return
//     */
//    @Bean
//    public ShiroFilterFactoryBean shiroFilterFactoryBean(SecurityManager securityManager) {
//        System.out.println("ShiroConfiguration.shirFilter()");
//        ShiroFilterFactoryBean shiroFilterFactoryBean = new ShiroFilterFactoryBean();
//        // 设置 securityManager
//        shiroFilterFactoryBean.setSecurityManager(securityManager);
//        addFilter(shiroFilterFactoryBean);//将自定义 的FormAuthenticationFilter注入shiroFilter中
//
//        LinkedHashMap<String, String> filterChainDefinitionMap = new LinkedHashMap<>();
//        // 设置免认证 url
//        String[] anonUrls = StringUtils.splitByWholeSeparatorPreserveAllTokens(febsProperties.getShiro().getAnonUrl(), ",");
//        if(GeneralTool.isNotEmpty(anonUrls)) {
//            for (String url : anonUrls) {
//                filterChainDefinitionMap.put(url, "anon");
//            }
//        }
//
//        /*List<Auth> alist = authMapper.findNeedAuth();
//        for (Auth auth:alist) {
//            if(StringUtils.isNotBlank(auth.getUrl())&&StringUtils.isNotBlank(auth.getPerms()) ){
//                filterChainDefinitionMap.put(auth.getUrl(),"perms["+auth.getPerms()+"]");
//            }
//        }*/
//
//        // 配置退出过滤器，其中具体的退出代码 Shiro已经替我们实现了
//        filterChainDefinitionMap.put(febsProperties.getShiro().getLogoutUrl(), "logout");
////        filterChainDefinitionMap.put("/","authc");
//        // 除上以外所有 url都必须认证通过才可以访问，未通过认证自动访问 LoginUrl
//        filterChainDefinitionMap.put("/**", "user");
//
//        shiroFilterFactoryBean.setFilterChainDefinitionMap(filterChainDefinitionMap);
//
//        return shiroFilterFactoryBean;
//    }
//
//    /**
//     * 安全管理器
//     * @return
//     */
//    @Bean
//    public SecurityManager securityManager() {
//        DefaultWebSecurityManager securityManager = new DefaultWebSecurityManager();
//        // 配置 rememberMeCookie
//        securityManager.setRememberMeManager(rememberMeManager());
//
//        // 配置 缓存管理类 cacheManager
//        securityManager.setCacheManager(cacheManager());
//
//
//        securityManager.setSessionManager(sessionManager());
//
//        // 配置 SecurityManager，并注入 shiroRealm
//        securityManager.setRealm(shiroRealm());
//        return securityManager;
//    }
//
//
//
//    @Bean(name = "lifecycleBeanPostProcessor")
//    public static LifecycleBeanPostProcessor lifecycleBeanPostProcessor() {
//        // shiro 生命周期处理器
//        return new LifecycleBeanPostProcessor();
//    }
//
//    @Bean
//    public ShiroRealm shiroRealm() {
//        return new ShiroRealm();
//    }
//    private void addFilter(ShiroFilterFactoryBean shiroFilterFactoryBean){
//        Map<String, Filter> filters = shiroFilterFactoryBean.getFilters();//获取filters
//        filters.put("perms", authorizationFilter());//将自定义 的FormAuthenticationFilter注入shiroFilter中
//        filters.put("loginF", unauthorizedFilter());//将自定义 的FormAuthenticationFilter注入shiroFilter中
//        shiroFilterFactoryBean.setFilters(filters);
//    }
//
//    @Bean("authorizationFilter")
//    public SystemAuthorizationFilter authorizationFilter() {
//        return new SystemAuthorizationFilter();
//    }
//
//    @Bean("unauthorizedFilter")
//    public SystemUnauthorizedFilter unauthorizedFilter(){
//        return new SystemUnauthorizedFilter();
//    }
//
//
//    /**
//     * rememberMe cookie 效果是重开浏览器后无需重新登录
//     *
//     * @return SimpleCookie
//     */
//    private SimpleCookie rememberMeCookie() {
//        // 设置 cookie 名称，对应 login.html 页面的 <input type="checkbox" name="rememberMe"/>
//        SimpleCookie cookie = new SimpleCookie("rememberMe");
//        // 设置 cookie 的过期时间，单位为秒，这里为一天
//        cookie.setMaxAge(febsProperties.getShiro().getCookieTimeout());
//        return cookie;
//    }
//
//    /**
//     * cookie管理对象
//     *
//     * @return CookieRememberMeManager
//     */
//    private CookieRememberMeManager rememberMeManager() {
//        CookieRememberMeManager cookieRememberMeManager = new CookieRememberMeManager();
//        cookieRememberMeManager.setCookie(rememberMeCookie());
//        // rememberMe cookie 加密的密钥
//        cookieRememberMeManager.setCipherKey(Base64.decode("4AvVhmFLUs0KTA3Kprsdag=="));
//        return cookieRememberMeManager;
//    }
//
//    /**
//     * DefaultAdvisorAutoProxyCreator 和 AuthorizationAttributeSourceAdvisor 用于开启 shiro 注解的使用
//     * 如 @RequiresAuthentication， @RequiresUser， @RequiresPermissions 等
//     *
//     * @return DefaultAdvisorAutoProxyCreator
//     */
//    @Bean
//    @DependsOn({"lifecycleBeanPostProcessor"})
//    public DefaultAdvisorAutoProxyCreator advisorAutoProxyCreator() {
//        System.out.println("lifecycleBeanPostProcessor");
//        System.out.println(febsProperties.getClass());
//        DefaultAdvisorAutoProxyCreator advisorAutoProxyCreator = new DefaultAdvisorAutoProxyCreator();
//        advisorAutoProxyCreator.setProxyTargetClass(true);
//        return advisorAutoProxyCreator;
//    }
//
//    @Bean
//    public AuthorizationAttributeSourceAdvisor authorizationAttributeSourceAdvisor(SecurityManager securityManager) {
//        AuthorizationAttributeSourceAdvisor authorizationAttributeSourceAdvisor = new AuthorizationAttributeSourceAdvisor();
//        authorizationAttributeSourceAdvisor.setSecurityManager(securityManager);
//        return authorizationAttributeSourceAdvisor;
//    }
//
//
//
//    @Bean
//    public RedisSessionDAO redisSessionDAO() {
//        RedisSessionDAO redisSessionDAO = new RedisSessionDAO();
//        redisSessionDAO.setRedisManager(redisManager());
//        return redisSessionDAO;
//    }
//
//    /**
//     * session 管理对象
//     *
//     * @return DefaultWebSessionManager
//     */
//    @Bean
//    public DefaultWebSessionManager sessionManager() {
////        DefaultWebSessionManager sessionManager = new DefaultWebSessionManager();
//        DefaultWebSessionManager sessionManager = new StatelessSessionManager();
//        Collection<SessionListener> listeners = new ArrayList<>();
//        listeners.add(new ShiroSessionListener());
//        // 设置session超时时间，单位为毫秒
//        sessionManager.setGlobalSessionTimeout(febsProperties.getShiro().getSessionTimeout());
//        sessionManager.setSessionListeners(listeners);
//        sessionManager.setSessionDAO(redisSessionDAO());
//        sessionManager.setSessionIdUrlRewritingEnabled(false);
//        return sessionManager;
//    }
//}
