package com.get.resumecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2020/8/11
 * @TIME: 10:57
 * @Description:
 **/
@Data
public class ResumeTrainingDto  extends BaseVoEntity implements Serializable {

        private static final long serialVersionUID = 1L;

    /**
     * 所属简历Id
     */
    @ApiModelProperty(value = "所属简历Id", required = true)
    @NotNull(message = "简历id不能为空", groups = {Add.class, Update.class})
    private Long fkResumeId;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;
    /**
     * 培训课程
     */
    @ApiModelProperty(value = "培训课程")
    private String course;
    /**
     * 培训机构
     */
    @ApiModelProperty(value = "培训机构")
    private String institution;
    /**
     * 培训地点
     */
    @ApiModelProperty(value = "培训地点")
    private String place;
    /**
     * 培训描述
     */
    @ApiModelProperty(value = "培训描述")
    private String description;

}
