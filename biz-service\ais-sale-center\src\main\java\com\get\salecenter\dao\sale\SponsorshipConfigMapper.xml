<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.SponsorshipConfigMapper">
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.SponsorshipConfig">
    insert into m_sponsorship_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="text != null">
        text,
      </if>
      <if test="date != null">
        date,
      </if>
      <if test="time != null">
        time,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="note != null">
        note,
      </if>
      <if test="imgUrl != null">
        img_url,
      </if>
      <if test="initNum != null">
        init_num,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="text != null">
        #{text,jdbcType=VARCHAR},
      </if>
      <if test="date != null">
        #{date,jdbcType=VARCHAR},
      </if>
      <if test="time != null">
        #{time,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=VARCHAR},
      </if>
      <if test="note != null">
        #{note,jdbcType=VARCHAR},
      </if>
      <if test="imgUrl != null">
        #{imgUrl,jdbcType=VARCHAR},
      </if>
      <if test="initNum != null">
        #{initNum,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>