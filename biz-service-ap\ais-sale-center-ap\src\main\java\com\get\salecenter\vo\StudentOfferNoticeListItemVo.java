package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/11/23 11:27
 * @verison: 1.0
 * @description:
 */
@Data
public class StudentOfferNoticeListItemVo {

    @ApiModelProperty("申请计划id")
    private Long id;

    @ApiModelProperty(value = "学生申请方案Id")
    private Long fkStudentOfferId;

    @ApiModelProperty("学校id")
    private Long institutionId;

    @ApiModelProperty("学校名称")
    private String institutionName;

    @ApiModelProperty("课程id")
    private Long courseId;

    @ApiModelProperty("课程名称")
    private String courseName;

    @ApiModelProperty("步骤id")
    private Long stepId;

    @ApiModelProperty("步骤名称")
    private String stepName;

    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @ApiModelProperty("开学时间")
    private Date openingTime;

    @ApiModelProperty("项目成员")
    private List<StudentRoleAndStaffVo> studentRoleAndStaffList;

    @ApiModelProperty("代理id")
    private Long agentId;

    @ApiModelProperty("代理名称")
    private String agentName;

    @ApiModelProperty("国家id")
    private Long countryId;

    @ApiModelProperty("国家名称")
    private String countryName;

    @ApiModelProperty("创建人")
    private String gmtCreateUser;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    @ApiModelProperty("更新人")
    private String gmtModifiedUser;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("更新时间")
    private Date gmtModified;


}
