package com.get.platformconfigcenter.dao.appissue;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.TranslationMapping;
import org.apache.ibatis.annotations.Mapper;

/**
 * @author: Hardy
 * @create: 2021/6/18 18:14
 * @verison: 1.0
 * @description:
 */
@Mapper
@DS("issuedb")
public interface IssueTranslationMappingMapper extends BaseMapper<TranslationMapping> {

    int insert(TranslationMapping record);

    int insertSelective(TranslationMapping record);

    int updateByPrimaryKeySelective(TranslationMapping record);

    int updateByPrimaryKey(TranslationMapping record);
}
