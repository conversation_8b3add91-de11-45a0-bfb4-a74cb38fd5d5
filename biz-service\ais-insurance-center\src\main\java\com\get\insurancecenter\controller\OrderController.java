package com.get.insurancecenter.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.secure.utils.SecureUtil;
import com.get.insurancecenter.dto.order.OrderListDto;
import com.get.insurancecenter.dto.order.UpdateOrderDto;
import com.get.insurancecenter.entity.InsuranceCompany;
import com.get.insurancecenter.entity.ProductType;
import com.get.insurancecenter.enums.InsuranceTypeEnum;
import com.get.insurancecenter.mapper.InsuranceCompanyMapper;
import com.get.insurancecenter.mapper.ProductTypeMapper;
import com.get.insurancecenter.service.InsuranceOrderService;
import com.get.insurancecenter.vo.order.OrderDetailVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @DATE: 2020/7/14
 * @TIME: 18:17
 * @Description: 订单管理控制层
 **/

@Api(tags = "订单管理")
@RestController
@RequestMapping("/order")
public class OrderController {

    @Autowired
    private InsuranceOrderService insuranceOrderService;
    @Autowired
    private InsuranceCompanyMapper insuranceCompanyMapper;
    @Autowired
    private ProductTypeMapper productTypeMapper;

    @ApiOperation(value = "订单列表")
    @PostMapping("/orderList")
    public ResponseBo<OrderDetailVo> orderList(@RequestBody SearchBean<OrderListDto> page) {
        List<OrderDetailVo> list = insuranceOrderService.orderList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(list, p);
    }

    @ApiOperation(value = "保险公司列表", notes = "保险公司列表")
    @GetMapping("/insuranceCompanyList")
    public ResponseBo<List<InsuranceCompany>> insuranceCompanyList() {
        return new ResponseBo<>(insuranceCompanyMapper.selectList(new LambdaQueryWrapper<InsuranceCompany>()
                .orderByDesc(InsuranceCompany::getViewOrder)));
    }

    @ApiOperation(value = "保险单类型列表")
    @GetMapping("/insuranceTypeList")
    public ResponseBo<Map<String, String>> insuranceTypeList() {
        return new ResponseBo<>(Arrays.stream(InsuranceTypeEnum.values())
                .collect(Collectors.toMap(InsuranceTypeEnum::getCode, InsuranceTypeEnum::getMsg)));
    }

    @ApiOperation(value = "保险产品类型列表", notes = "保险产品类型列表")
    @GetMapping("/productTypeList")
    public ResponseBo<List<ProductType>> productTypeList(Long insuranceCompanyId) {
        return new ResponseBo<>(productTypeMapper.selectList(new LambdaQueryWrapper<ProductType>()
                .eq(Objects.nonNull(insuranceCompanyId), ProductType::getFkInsuranceCompanyId, insuranceCompanyId)
                .orderByDesc(ProductType::getViewOrder)));
    }

    @ApiOperation(value = "订单详情")
    @GetMapping("/getOrderInfo/{id}")
    public ResponseBo<OrderDetailVo> insuranceTypeList(@PathVariable("id") Long id) {
        return new ResponseBo<>(insuranceOrderService.getOrderInfo(id));
    }

    @ApiOperation(value = "订单变更")
    @GetMapping("/updateOrder")
    public ResponseBo<String> updateOrder(@RequestBody UpdateOrderDto orderDto) {
        insuranceOrderService.updateOrder(orderDto);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }

}
