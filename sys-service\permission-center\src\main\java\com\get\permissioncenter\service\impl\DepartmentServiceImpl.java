package com.get.permissioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.dao.DepartmentMapper;
import com.get.permissioncenter.dto.CompanyIdsRequestDto;
import com.get.permissioncenter.dto.DepartmentDto;
import com.get.permissioncenter.entity.Department;
import com.get.permissioncenter.service.ICompanyService;
import com.get.permissioncenter.service.IDeleteService;
import com.get.permissioncenter.service.IDepartmentService;
import com.get.permissioncenter.vo.CompanyVo;
import com.get.permissioncenter.vo.DepartmentVo;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @DATE: 2020/6/28
 * @TIME: 16:43
 **/
@Service
public class DepartmentServiceImpl extends BaseServiceImpl<DepartmentMapper, Department> implements IDepartmentService {
    @Resource
    private DepartmentMapper departmentMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private ICompanyService companyService;
    @Resource
    private IDeleteService deleteService;


    @Override
    public List<DepartmentVo> getDepartments(DepartmentDto departMentDto) {
//        Example example = new Example(Department.class);
//        Example.Criteria criteria = example.createCriteria();
//        Example.Criteria criteria1 = example.createCriteria();
//
//        if (GeneralTool.isEmpty(departMentDto) || GeneralTool.isEmpty(departMentDto.getFkCompanyId())) {
//            List<Long> companyIds = getCompanyIds();
//            criteria.andIn("fkCompanyId", companyIds);
//        }
//        if (GeneralTool.isNotEmpty(departMentDto)) {
//            if (GeneralTool.isNotEmpty(departMentDto.getFkCompanyId())) {
//                SecureUtil.validateCompany(departMentDto.getFkCompanyId());
//                criteria.andEqualTo("fkCompanyId", departMentDto.getFkCompanyId());
//            }
//            if (GeneralTool.isNotEmpty(departMentDto.getKeyWord())) {
//                criteria1.andLike("num", "%" + departMentDto.getKeyWord() + "%");
//                criteria1.orLike("name", "%" + departMentDto.getKeyWord() + "%");
//            }
//        }
//        example.and(criteria1);
//        example.orderBy("viewOrder").asc();
//        //获取分页数据
//        List<Department> departments = departmentMapper.selectByExample(example);
//        List<DepartmentVo> departmentVos =
//                departments.stream().map(department -> Tools.objClone(department, DepartmentVo.class)).collect(Collectors.toList());
        LambdaQueryWrapper<Department> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isEmpty(departMentDto) || GeneralTool.isEmpty(departMentDto.getFkCompanyId())) {
            List<Long> companyIds = getCompanyIds();
            wrapper.in(Department::getFkCompanyId, companyIds);
        }
        if (GeneralTool.isNotEmpty(departMentDto)) {
            if (GeneralTool.isNotEmpty(departMentDto.getFkCompanyId())) {
                if (!SecureUtil.validateCompany(departMentDto.getFkCompanyId())) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
                }
                wrapper.eq(Department::getFkCompanyId, departMentDto.getFkCompanyId());
            }
            if (GeneralTool.isNotEmpty(departMentDto.getKeyWord())) {
                wrapper.and(wrapper_ ->
                        wrapper_.like(Department::getName, departMentDto.getKeyWord()).or()
                                .like(Department::getNum, departMentDto.getKeyWord()));
            }
        }
        wrapper.orderByAsc(Department::getViewOrder);

        List<Department> departments = this.list(wrapper);
        List<DepartmentVo> departmentVos = BeanCopyUtils.copyListProperties(departments, DepartmentVo::new);

        for (DepartmentVo departmentVo : departmentVos) {
            //设置公司名
            setCompanyName(departmentVo);
        }
        return departmentVos;
    }

    @Override
    public List<DepartmentVo> getAllDepartments(Long companyId) {
        if (GeneralTool.isEmpty(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_id_null"));
        }
//        Example example = new Example(Department.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkCompanyId", companyId);
//        example.orderBy("viewOrder").asc();
//        List<Department> departments = departmentMapper.selectByExample(example);
        List<Department> departments = this.list(Wrappers.<Department>query().lambda().eq(Department::getFkCompanyId, companyId).orderByAsc(Department::getViewOrder));
//        return departments.stream().map(department -> Tools.objClone(department, DepartmentVo.class)).collect(Collectors.toList());
        return BeanCopyUtils.copyListProperties(departments, DepartmentVo::new);
    }

    @Override
    public DepartmentVo findDepartmentById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
//        Department department = departmentMapper.selectByPrimaryKey(id);
        Department department = departmentMapper.selectById(id);
//        //权限校验
//        if (GeneralTool.isNotEmpty(department)) {
//            if(!SecureUtil.validateCompany(department.getFkCompanyId()))
//            {
//                throw new GetServiceException("没有权限查看此条数据");
//            }
//        }
//        DepartmentVo departMentVo = Tools.objClone(department, DepartmentVo.class);
        DepartmentVo departMentVo = BeanCopyUtils.objClone(department, DepartmentVo::new);
        setCompanyName(departMentVo);
        return departMentVo;
    }

    @Override
    public Long addDepartment(DepartmentDto departMentDto) {
        if (GeneralTool.isEmpty(departMentDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (!validateChange(departMentDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("num_exist"));
        }
//        Department department = Tools.objClone(departMentDto, Department.class);
        Department department = BeanCopyUtils.objClone(departMentDto, Department::new);
        utilService.updateUserInfoToEntity(department);
        //查询最大排序号
        Integer maxViewOrder = departmentMapper.getMaxViewOrder();
        maxViewOrder = GeneralTool.isEmpty(maxViewOrder) ? 0 : maxViewOrder;
        department.setViewOrder(maxViewOrder);
        int i = departmentMapper.insertSelective(department);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return department.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAddDepartment(List<DepartmentDto> departmentDtos) {
        for (DepartmentDto departmentDto : departmentDtos) {
            if (GeneralTool.isEmpty(departmentDto.getId())) {
                addDepartment(departmentDto);
            } else {
                updateDepartment(departmentDto);
            }

        }
    }

    @Override
    public DepartmentVo updateDepartment(DepartmentDto departMentDto) {
        if (GeneralTool.isEmpty(departMentDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Long aLong = validateUpdate(departMentDto);
        if (!departMentDto.getId().equals(aLong)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("num_exist"));
        }
//        Department department = Tools.objClone(departMentDto, Department.class);
        Department department = BeanCopyUtils.objClone(departMentDto, Department::new);
        utilService.updateUserInfoToEntity(department);
//        departmentMapper.updateByPrimaryKeySelective(department);
        departmentMapper.updateById(department);
        return findDepartmentById(departMentDto.getId());
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        deleteService.deleteValidateDepartment(id);
//        int i = departmentMapper.deleteByPrimaryKey(id);
        int i = departmentMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<DepartmentDto> departmentDtoList) {
        if (GeneralTool.isEmpty(departmentDtoList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
//        Department department1 = Tools.objClone(departmentDtoList.get(0), Department.class);
//        Department department2 = Tools.objClone(departmentDtoList.get(1), Department.class);
        Department department1 = BeanCopyUtils.objClone(departmentDtoList.get(0), Department::new);
        Department department2 = BeanCopyUtils.objClone(departmentDtoList.get(1), Department::new);
        Integer viewOrder = department1.getViewOrder();
        department1.setViewOrder(department2.getViewOrder());
        department2.setViewOrder(viewOrder);
//        utilService.setUpdateInfo(department1);
//        utilService.setUpdateInfo(department2);
        utilService.updateUserInfoToEntity(department1);
        utilService.updateUserInfoToEntity(department2);
//        departmentMapper.updateByPrimaryKeySelective(department1);
//        departmentMapper.updateByPrimaryKeySelective(department2);
        departmentMapper.updateById(department1);
        departmentMapper.updateById(department2);
    }

    @Override
    public List<BaseSelectEntity> getDepartmentSelect(Long companyId) {
        if (!SecureUtil.validateCompany(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        return departmentMapper.getDepartmentSelect(companyId);
    }

    /**
     * @param companyIdsRequestVo
     * 批量获取部门列表
     * @return
     */
    @Override
    public List<BaseSelectEntity> batchObtainDepartmentList(CompanyIdsRequestDto companyIdsRequestVo) {
        if (GeneralTool.isEmpty(companyIdsRequestVo.getCompanyIds())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (!SecureUtil.validateCompanys(companyIdsRequestVo.getCompanyIds())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }

        List<BaseSelectEntity> baseSelectEntities = companyIdsRequestVo.getCompanyIds().stream()
                .map(departmentMapper::getDepartmentSelect)
                .filter(GeneralTool::isNotEmpty)
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
        return baseSelectEntities;
    }

    @Override
    public List<String> getDepartmentNameList(String[] departmentNumList) {
        return departmentMapper.getDepartmentNameList(Arrays.asList(departmentNumList));
    }

    @Override
    public String getDepartmentNameById(Long departmentId) {
        return departmentMapper.getDepartmentNameById(departmentId);
    }

    @Override
    public Map<Long, String> getDepartmentNamesByIds(Set<Long> departmentIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(departmentIds)) {
            return map;
        }
//        Example example = new Example(Department.class);
//        example.createCriteria().andIn("id",departmentIds);
//        List<Department> departments = departmentMapper.selectByExample(example);
        List<Department> departments = this.list(Wrappers.<Department>query().lambda().in(Department::getId, departmentIds));
        if (GeneralTool.isEmpty(departments)) {
            return map;
        }
        for (Department department : departments) {
            map.put(department.getId(), department.getName());
        }
        return map;
    }

    @Override
    public Long getDepartmentIdByNum(String num) {
//        Example example = new Example(Department.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("num",num);
//        List<Department> departments = departmentMapper.selectByExample(example);
        List<Department> departments = this.list(Wrappers.<Department>query().lambda().eq(Department::getNum, num));
        if (GeneralTool.isEmpty(departments)) {
            return 0L;
        }
        return departments.get(0).getId();
    }

    @Override
    public String getDepartmentNumById(Long id) {
        Department department = this.getById(id);
        if (GeneralTool.isEmpty(department)) {
            return "";
        }
        return department.getNum();
    }

    @Override
    public CompanyVo multiCompanyDepartmentList() {
        List<Long> companyIds = SecureUtil.getCompanyIds();
        if (GeneralTool.isEmpty(companyIds)) {
            return null;
        }
        CompanyVo companyVos = companyService.getChildCompanyDto();

        Map<Long, List<BaseSelectEntity>> companyDepartmentMap = companyIds.stream()
                .collect(Collectors.toMap(
                        companyId -> companyId,
                        companyId -> departmentMapper.getDepartmentSelect(companyId),
                        (oldList, newList) -> oldList  // 避免公司ID重复时的冲突（保留先加载的数据）
                ));

//        companyAndDepartmentVo.forEach(companyAndDepartmentVo -> {
//            companyAndDepartmentVo.setDepartmentData(companyDepartmentMap.get(companyAndDepartmentVo.getId()));
//            companyAndDepartmentVo.getChildCompanyDto().forEach(childCompany -> {
//                childCompany.setDepartmentData(companyDepartmentMap.get(childCompany.getId()));
//            });
//        });
        companyVos.setDepartmentData(companyDepartmentMap.getOrDefault(companyVos.getId(), Collections.emptyList()));

        bindChildDepartment(companyVos.getChildCompanyDto(), companyDepartmentMap);




//        List<BaseSelectEntity> baseSelectEntities = companyIds.stream()
//                .map(departmentMapper::getDepartmentSelect)
//                .filter(GeneralTool::isNotEmpty)
//                .flatMap(List::stream)
//                .distinct()
//                .collect(Collectors.toList());

        return companyVos;
    }

    private void bindChildDepartment(List<CompanyVo> childCompanies,
                                     Map<Long, List<BaseSelectEntity>> departmentMap) {
        if (GeneralTool.isEmpty(childCompanies)) {
            return;
        }

        for (CompanyVo child : childCompanies) {
            child.setDepartmentData(departmentMap.getOrDefault(child.getId(), Collections.emptyList()));
            if (GeneralTool.isEmpty(child.getChildCompanyDto())) {
                bindChildDepartment(child.getChildCompanyDto(), departmentMap);
            }
        }
    }

    private void setCompanyName(DepartmentVo departMentVo) {
        if (GeneralTool.isNotEmpty(departMentVo)) {
            if (GeneralTool.isNotEmpty(departMentVo.getFkCompanyId())) {
                String name = companyService.getCompanyNameById(departMentVo.getFkCompanyId());
                departMentVo.setFkCompanyName(name);

            }
        }
    }

    private List<Long> getCompanyIds() {
//        List<Long> companyIds = StaffContext.getStaff().getCompanyIds();
        List<Long> companyIds = SecureUtil.getCompanyIdsByStaffId(GetAuthInfo.getStaffId());
        if (GeneralTool.isEmpty(companyIds)) {
            companyIds.add(0L);
        }
        return companyIds;
    }

    //新增校验是否存在
    private boolean validateChange(DepartmentDto departMentDto) {
//        Example example = new Example(Office.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("num", departMentDto.getNum());
//        criteria.andEqualTo("fkCompanyId", departMentDto.getFkCompanyId());
//        List<Department> departments = departmentMapper.selectByExample(example);
        List<Department> departments = this.list(Wrappers.<Department>query().lambda().eq(Department::getNum, departMentDto.getNum()).eq(Department::getFkCompanyId, departMentDto.getFkCompanyId()));
        return GeneralTool.isEmpty(departments);
    }

    //编辑校验是否存在
    private Long validateUpdate(DepartmentDto departMentDto) {
//        Example example = new Example(Office.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("num", departMentDto.getNum());
//        criteria.andEqualTo("fkCompanyId", departMentDto.getFkCompanyId());
//        List<Department> departments = departmentMapper.selectByExample(example);
        List<Department> departments = this.list(Wrappers.<Department>query().lambda().eq(Department::getNum, departMentDto.getNum()).eq(Department::getFkCompanyId, departMentDto.getFkCompanyId()));
        return GeneralTool.isNotEmpty(departments) ? departments.get(0).getId() : departMentDto.getId();
    }

}
