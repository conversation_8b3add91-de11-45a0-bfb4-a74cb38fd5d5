package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-03-13
 */
@Data
public class IncentiveRewardDto extends BaseVoEntity {

    @NotNull(message = "公司Id", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "公司Id", required = true)
    private Long fkCompanyId;

    @NotNull(message = "奖品名称", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "奖品名称", required = true)
    private String rewardName;

    @ApiModelProperty(value = "奖品key", required = false)
    private String rewardKey;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "奖品ids")
    private List<Long> rewardIds;

  

}
