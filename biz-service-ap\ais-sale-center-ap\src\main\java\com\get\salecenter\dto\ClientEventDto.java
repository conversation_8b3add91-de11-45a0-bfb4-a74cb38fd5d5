package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2020/11/10
 * @TIME: 16:08
 * @Description:
 **/
@Data
public class ClientEventDto {

    /**
     * 客户Id
     */
    @NotNull(message = "客户Id不能为空")
    @ApiModelProperty(value = "客户Id")
    private Long fkClientId;

    /**
     * 客户事件类型Id
     */
    @ApiModelProperty(value = "客户事件类型Id")
    private Long fkStudentEventTypeId;

    /**
     * 事件内容
     */
    @ApiModelProperty(value = "事件内容")
    private String description;

    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyWord;

}
