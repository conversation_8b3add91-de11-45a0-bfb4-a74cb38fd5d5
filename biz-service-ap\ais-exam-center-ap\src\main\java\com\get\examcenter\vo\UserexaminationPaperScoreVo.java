package com.get.examcenter.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * Created by <PERSON>.
 * Time: 14:04
 * Date: 2021/8/23
 * Description:排行榜返回类
 */
@Data
@ApiModel("排行榜返回类")
public class UserexaminationPaperScoreVo {

    /**
     * 注册中心的user id
     */
    @ApiModelProperty(value = "注册中心的user id")
    private Long fkUserId;

    /**
     * 注册中心的用户名称
     */
    @ApiModelProperty(value = "注册中心的用户名称")
    private String fkUserName;

    /**
     * 排名
     */
    @ApiModelProperty(value = "排名")
    private String rank;

    /**
     * 分数
     */
    @ApiModelProperty(value = "分数")
    private Integer score;

    /**
     * 用时（秒）
     */
    @ApiModelProperty(value = "用时（秒）")
    private Integer useTime;

    /**
     * 考试次数
     */
    @ApiModelProperty("考试次数")
    private Long paperNumber;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    private String phoneNumber;

    /**
     * 考试编号
     */
    @ApiModelProperty("考试编号")
    private String fkExaminationNum;

    /**
     * 考试名称
     */
    @ApiModelProperty("考试名称")
    private String fkExaminationName;
    /**
     * 考试Id
     */
    @ApiModelProperty(value = "考试Id")
    private Long fkExaminationId;

    /**
     * 操作guid
     */
    @ApiModelProperty(value = "操作guid")
    private String optGuid;

    /**
     * 考卷编号
     */
    @ApiModelProperty(value = "考卷编号")
    private String fkPaperNum;

    /**
     * 考场名称
     */
    @ApiModelProperty(value = "考卷名称")
    private String FkPaperName;

    /**
     * 考场id
     */
    @ApiModelProperty(value = "考场id")
    private Long fkExaminationPaperId;

    /**
     * 称号
     */
    @ApiModelProperty(value = "称号")
    private String scoreTitleName;

    /**
     * 所在城市
     */
    @ApiModelProperty(value = "所在城市")
    private String fkAreaCityName;

    /**
     * BD
     */
    @ApiModelProperty(value = "BD")
    private String bdName;

    /**
     * 称号颜色编码
     */
    @ApiModelProperty(value = "称号颜色编码")
    private String colorCode;

    /**
     * 称号图片
     */
    @ApiModelProperty(value = "称号图片")
    private List<MediaAndAttachedVo> scoreTitleFile;

    /**
     * 考试次数
     */
    @ApiModelProperty(value = "考试次数")
    private Integer examCount;


    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码")
    private String mobile;

    /**
     * 峰会用户名称
     */
    @ApiModelProperty(value = "峰会用户名称")
    private String conventionPersonName;

    @ApiModelProperty(value = "(新)考试次数")
    private Integer count;
}
