package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.vo.SelItem;
import com.get.salecenter.entity.ClientEvent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * author:Neil
 * Time: 15:56
 * Date: 2022/8/17
 * Description:
 */
@Mapper
public interface ClientEventMapper extends BaseMapper<ClientEvent> {

    List<SelItem> getLastVisitTime(@Param("list") Set<Long> ids);


    List<SelItem> getVisitTime(@Param("list")Set<Long> ids,@Param("beginFollowUpTime") Date beginFollowUpTime,@Param("endFollowUpTime") Date endFollowUpTime);
    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [clientId]
     * <AUTHOR>
     */
    Boolean isExistByClientId(@Param("clientId")Long clientId);
}
