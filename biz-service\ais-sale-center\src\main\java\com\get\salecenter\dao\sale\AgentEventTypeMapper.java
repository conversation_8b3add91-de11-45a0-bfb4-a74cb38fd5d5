package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.AgentEventType;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface AgentEventTypeMapper extends BaseMapper<AgentEventType> {

    int insertSelective(AgentEventType record);

    /**
     * @return java.lang.Integer
     * @Description: 查找最大排序序号
     * @Param []
     * <AUTHOR>
     */
    Integer getMaxViewOrder();

}