package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;

/**
 * @author: Hardy
 * @create: 2021/10/12 16:10
 * @verison: 1.0
 * @description:
 */
@Data
public class ConventionPersonListVo {
    @ApiModelProperty("id，主键")
    @Id
    private Long id;

    /**
     * 跟进人员的bdCode
     */
    private String bdCode;


    /**
     * 参展人员类型（枚举定义）：0校方/1代理/2嘉宾/3员工/4工作人员
     */
    @ApiModelProperty(value = "参展人员类型（枚举定义）：0校方/1代理/2嘉宾/3员工/4工作人员")
    @Column(name = "type")
    private Integer type;


    /**
     * 参加人姓名
     */
    @ApiModelProperty(value = "参加人姓名")
    @Column(name = "name")
    private String name;

    /**
     * 参加人姓名（中文）
     */
    @ApiModelProperty(value = "参加人姓名（中文）")
    @Column(name = "name_chn")
    private String nameChn;

    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男")
    @Column(name = "gender")
    private Integer gender;

    /**
     * 是否出席：0否/1是
     */
    @ApiModelProperty(value = "是否出席：0否/1是")
    @Column(name = "is_attend")
    private Boolean isAttend;


    private Long conventionHotelRoomPersonId;

    private Long conventionHotelRoomId;


    /**
     * 机构名
     */
    @ApiModelProperty(value = "机构名")
    private String institutionName;


}
