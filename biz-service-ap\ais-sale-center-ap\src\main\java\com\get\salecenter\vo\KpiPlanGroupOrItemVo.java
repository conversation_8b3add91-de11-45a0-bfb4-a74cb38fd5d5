package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2024/4/22
 * @TIME: 18:50
 * @Description:
 **/
@Data
public class KpiPlanGroupOrItemVo extends BaseEntity implements Serializable {
    @ApiModelProperty(value = "KPI方案ID")
    private Long fkKpiPlanId;

    @ApiModelProperty(value = "组别名称")
    private String groupName;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;


    @ApiModelProperty(value = "KPI方案组别Id")
    private Long fkKpiPlanGroupId;

    @ApiModelProperty(value = "学校提供商Id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "学校提供商名称")
    private String fkInstitutionProviderName;

    @ApiModelProperty(value = "专业等级Ids(多选)，格式：1,2,3")
    private String fkMajorLevelIds;

    @ApiModelProperty(value = "专业等级名称")
    private String fkMajorLevelNames;

    @ApiModelProperty(value = "申请国家Ids(多选)，格式：1,2,3")
    private String fkAreaCountryIds;

    @ApiModelProperty(value = "申请国家名称")
    private String fkAreaCountryNames;

    @ApiModelProperty(value = "国家包含类型：null没设置/0不包含/1包含")
    private Integer countryIncludeType;

    @ApiModelProperty(value = "剔除提供商下的学校Ids(多选)，格式：1,2,3")
    private String fkInstitutionIds;

    @ApiModelProperty(value = "剔除提供商下的学校名称")
    private String fkInstitutionIdsExcludingNames;

    @ApiModelProperty(value = "是否统计后续课程：0否/1是，默认0不做统计")
    private Boolean  isFollow;

    @ApiModelProperty(value = "学校包含类型：null没设置/0不包含/1包含")
    private Integer institutionIncludeType;

    @ApiModelProperty(value = "时间设定")
    private String timeSet;

    @ApiModelProperty(value = "学生创建时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date studentCreateTimeStart;

    @ApiModelProperty(value = "学生创建时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date studentCreateTimeEnd;

    @ApiModelProperty(value = "入学时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date intakeTimeStart;

    @ApiModelProperty(value = "入学时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date intakeTimeEnd;

    @ApiModelProperty(value = "步骤登记时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stepTimeStart;

    /**
     * 申请创建开始时间
     */
    @ApiModelProperty(value = "申请计划创建时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date offerItemCreateTimeStart;

    /**
     * 申请创建结束时间
     */
    @ApiModelProperty(value = "申请计划结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date offerItemCreateTimeEnd;


    @ApiModelProperty(value = "步骤登记时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stepTimeEnd;

    @ApiModelProperty(value = "目标设置（成功入学）")
    private Integer targetEnrolled;

    @ApiModelProperty(value = "描述")
    private String description;


    @ApiModelProperty(value = "KPI小计（目标）")
    private String kpiSubtotal;

    @ApiModelProperty(value = "KPI目标设置列表")
    private List<KpiPlanTargetVo> kpiPlanTargetDtoList;
}
