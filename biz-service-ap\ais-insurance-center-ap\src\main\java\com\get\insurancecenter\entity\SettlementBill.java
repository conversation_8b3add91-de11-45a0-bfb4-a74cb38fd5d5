package com.get.insurancecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 结算账单
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("m_settlement_bill")
public class SettlementBill extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "结算代理Id")
    private Long fkAgentId;

    @ApiModelProperty(value = "代理合同结算账户Id")
    private Long fkAgentContractAccountId;

    @ApiModelProperty(value = "结算账户币种")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "对账单总金额（根据账户币种）")
    private BigDecimal amount;
}
