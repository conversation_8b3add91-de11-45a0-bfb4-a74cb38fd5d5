package com.get.financecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.financecenter.entity.ProviderContactPerson;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Sea
 * @create: 2020/12/28 11:46
 * @verison: 1.0
 * @description:
 */
@Data
public class ProviderContactPersonVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    //=================实体类ProviderContactPerson===================
    /**
     * 供应商Id
     */
    @ApiModelProperty(value = "供应商Id")
    private Long fkProviderId;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String gender;
    /**
     * 职业
     */
    @ApiModelProperty(value = "职业")
    private String job;
    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    private String mobile;
    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String tel;
    /**
     * Email
     */
    @ApiModelProperty(value = "Email")
    private String email;
    /**
     * qq
     */
    @ApiModelProperty(value = "qq")
    private String qq;
    /**
     * 微信号
     */
    @ApiModelProperty(value = "微信号")
    private String wechat;
    /**
     * whatsapp
     */
    @ApiModelProperty(value = "whatsapp")
    private String whatsapp;
    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    private String contactAddress;
}
