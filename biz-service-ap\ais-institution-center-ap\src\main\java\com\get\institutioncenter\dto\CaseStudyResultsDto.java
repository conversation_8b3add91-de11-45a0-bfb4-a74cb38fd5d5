package com.get.institutioncenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CaseStudyResultsDto {

    @ApiModelProperty(value = "院校背景成功案例")
    List<Statistics> schoolBackGroundSuc;

    @ApiModelProperty(value = "专业背景成功案例")
    List<Statistics> professionalBackGroundSuc;

    @ApiModelProperty(value = "失败原因统计")
    List<Statistics> failureCauseCase;


    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class Statistics {

        private Long id;

        private String name;

        private Integer num;

        private Integer type;
    }
}
