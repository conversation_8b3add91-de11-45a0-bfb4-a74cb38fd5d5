package com.get.core.log.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.core.log.dto.LogInstitutionProviderDto;
import com.get.core.log.model.LogInstitutionProvider;
import com.get.core.log.vo.LogInstitutionProviderVo;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2021/4/15
 * @TIME: 18:23
 * @Description:
 **/
public interface ILogInstitutionProviderService extends IService<LogInstitutionProvider> {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    //LogInstitutionProviderDto findLogInstitutionProviderById(Long id);

    /**
     * 列表数据
     *
     * @param logInstitutionProviderVo
     * @param page
     * @return
     */
    List<LogInstitutionProviderDto> getLogInstitutionProviders(LogInstitutionProviderVo logInstitutionProviderVo, Page page);

    /**
     * 保存
     *
     * @param logInstitutionProvider
     * @return
     */
   // Long addLogInstitutionProvider(LogInstitutionProviderVo logInstitutionProvider);
}
