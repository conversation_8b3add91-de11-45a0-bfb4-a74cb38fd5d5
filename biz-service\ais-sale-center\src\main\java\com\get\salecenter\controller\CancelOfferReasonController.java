package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.CancelOfferReasonVo;
import com.get.salecenter.service.ICancelOfferReasonService;
import com.get.salecenter.dto.CancelOfferReasonDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/1/11 10:46
 * @verison: 1.0
 * @description:
 */
@Api(tags = "申请方案终止作废原因管理")
@RestController
@RequestMapping("sale/cancelOfferReason")
public class CancelOfferReasonController {

    @Resource
    private ICancelOfferReasonService cancelOfferReasonService;

    /**
     * @return com.get.common.result.ListResponseBo<com.get.salecenter.vo.EnrolFailureReasonVo>
     * @Description:列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/申请方案终止作废原因管理/查询")
    @PostMapping("datas")
    public ListResponseBo<CancelOfferReasonVo> datas(@RequestBody SearchBean<CancelOfferReasonDto> page) {
        List<CancelOfferReasonVo> datas = cancelOfferReasonService.getCancelOfferReasonDtos(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description:新增信息
     * @Param [enrolFailureReasonVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/申请方案终止作废原因管理/新增原因")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(CancelOfferReasonDto.Add.class) ValidList<CancelOfferReasonDto> cancelOfferReasonDtos) {
        cancelOfferReasonService.addCancelOfferReason(cancelOfferReasonDtos);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EnrolFailureReasonVo>
     * @Description:修改信息
     * @Param [enrolFailureReasonVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/申请方案终止作废原因管理/更新原因")
    @PostMapping("update")
    public ResponseBo<CancelOfferReasonVo> update(@RequestBody  @Validated(CancelOfferReasonDto.Update.class) CancelOfferReasonDto cancelOfferReasonDto) {
        return UpdateResponseBo.ok(cancelOfferReasonService.updateCancelOfferReason(cancelOfferReasonDto));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description:删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/申请方案终止作废原因管理/删除原因")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        cancelOfferReasonService.deletecancelOfferReason(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description:上移下移
     * @Param [enrolFailureReasonVoList]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/申请方案终止作废原因管理/排序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<CancelOfferReasonDto> cancelOfferReasonDtos) {
        cancelOfferReasonService.movingOrder(cancelOfferReasonDtos);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description:申请方案终止作废原因下拉数据
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "申请方案终止作废原因下拉数据", notes = "")
    @GetMapping("getCancelOfferReasonSelect")
    public ResponseBo<BaseSelectEntity> getCancelOfferReasonSelect() {
        return new ListResponseBo<>(cancelOfferReasonService.getCancelOfferReasonSelect());
    }

}
