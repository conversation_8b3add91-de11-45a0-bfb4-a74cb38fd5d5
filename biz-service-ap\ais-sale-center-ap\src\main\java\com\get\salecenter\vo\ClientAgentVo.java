package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @DATE: 2022/8/1
 * @TIME: 16:17
 * @Description:
 **/
@Data
public class ClientAgentVo {

    @ApiModelProperty(value = "代理名称")
    @Column(name = "agent_name")
    private String agentName;

    @ApiModelProperty(value = "代理联系电话，多个用【;+空格】隔开")
    @Column(name = "agent_contact_tel")
    private String agentContactTel;
}
