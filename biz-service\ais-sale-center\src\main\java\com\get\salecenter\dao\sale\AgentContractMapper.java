package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.dto.query.AgentContractQueryDto;
import com.get.salecenter.entity.AgentContract;
import com.get.salecenter.vo.AgentContractVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface AgentContractMapper extends GetMapper<AgentContract>, BaseMapper<AgentContract> {

    List<Long> getContractByCountryIds(List<Long> countryIds);

    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [agentId]
     * <AUTHOR>
     */
    Boolean isExistByAgentId(Long agentId);

    Long getStaffByAgentId(@Param("fkAgentId") Long fkAgentId);

    void changeStatus(@Param("status") Integer status, @Param("tableName") String tableName, @Param("businessKey") Long businessKey);

    boolean getExistParentId(Long id);


    /**
     * @param contractVo
     * @param staffFollowerIds
     * @param companyIds
     * @return
     */
    List<AgentContractVo> getAgentContracts(IPage<AgentContract> pages,
                                            @Param("contractDto") AgentContractQueryDto contractVo,
                                            @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                            @Param("companyIds") List<Long> companyIds,
                                            @Param("advanceDays") Integer advanceDays,
                                            @Param("advanceType") String advanceType);


    Long getFkAgentidByCppAgId(@Param("agentid") String agentid);

    /**
     * 查询代理有合同信息且当前时间在有效期内，否则返回无效合同的代理名
     *
     * @Date 14:56 2022/7/13
     * <AUTHOR>
     */
    List<String> getInvalidContractAgentName(@Param("agentIdSet") Set<Long> agentIdSet);

    void updateByAgencyidAndEndtm(@Param("fkAgentid") String fkAgentid, @Param("endTm") String endTm);

    AgentContract getAttByAgencyidAndEndtm(@Param("fkAgentid") String fkAgentid, @Param("endTm") String endTm);

    /**
     * 根据代理id检查代理合同时间  true：未过期  false:已过期
     *
     * @Date 11:54 2022/11/28
     * <AUTHOR>
     */
    List<Long> checkAgentContractExpirationTime(@Param("fkAgentId") Long fkAgentId);

    //    获取代理合同编号最大值
    String getFkAgentContractNum(@Param("contractPrefix") String contractPrefix);

    //统计合同数量
    int countContractNum(@Param("companyId") Long companyId);
}