package com.get.partnercenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.partnercenter.dto.MAppStudentCheckDto;
import com.get.partnercenter.dto.MAppStudentCheckSerachDto;
import com.get.partnercenter.dto.MAppStudentOfferItemCheckDto;
import com.get.partnercenter.entity.MAppStudentCheckEntity;
import com.get.partnercenter.vo.MAppStudentCheckSearchVo;
import com.get.partnercenter.vo.MAppStudentVo;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【m_app_student_check】的数据库操作Service
 * @createDate 2025-02-13 17:57:46
 */
public interface MAppStudentCheckService extends IService<MAppStudentCheckEntity> {
    /**
     * 学生审核列表
     *
     * @param params
     * @param page
     * @return
     */
    List<MAppStudentCheckSearchVo> searchPage(MAppStudentCheckSerachDto params, Page page);

    /**
     * 学生详情
     *
     * @param id
     * @return
     */
    MAppStudentVo searchDetail(Long id);

    /**
     * 学生申请详情
     *
     * @param id
     * @return
     */
    MAppStudentVo getOfferItemDetail(Long id);

    /**
     * 学生审核
     *
     * @param checkdto
     * @return
     */
    long checkUser(MAppStudentCheckDto checkdto);

    /**
     * 加申审核
     *
     * @param checkdto
     * @return
     */
    boolean checkItemOffer(MAppStudentOfferItemCheckDto checkdto);


}
