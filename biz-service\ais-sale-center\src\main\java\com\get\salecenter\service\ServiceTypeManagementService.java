package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.salecenter.vo.StudentServiceFeeTypeVo;
import com.get.salecenter.entity.StudentServiceFeeType;
import com.get.salecenter.dto.StudentServiceFeeTypeDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ServiceTypeManagementService extends IService<StudentServiceFeeType> {

    /**
     * 获取服务类型管理列表数据
     * @param studentServiceFeeTypeDto
     * @param page
     * @return
     */
    ListResponseBo<StudentServiceFeeTypeVo> datas(StudentServiceFeeTypeDto studentServiceFeeTypeDto, Page<StudentServiceFeeTypeDto> page);

    /**
     * 新增服务类型
     * @param studentServiceFeeTypeDto
     * @return
     */
    SaveResponseBo save(StudentServiceFeeTypeDto studentServiceFeeTypeDto);


    /**
     * 获取详情
     * @param id
     * @return
     */
    ResponseBo<StudentServiceFeeTypeVo> findInfoById(Long id);

    /**
     * 更新服务类型
     * @param studentServiceFeeTypeDto
     * @return
     */
    ResponseBo update(StudentServiceFeeTypeDto studentServiceFeeTypeDto);


    /**
     * 删除服务类型
     * @param id
     * @return
     */
    ResponseBo delete(Long id);


    /**
     * 移动
     * @param feeTypeVos
     * @return
     */
    ResponseBo movingOrder(List<StudentServiceFeeTypeDto> feeTypeVos);


    /**
     * 安全分配
     * @param studentServiceFeeTypeDto
     * @return
     */
    ResponseBo securityDistribution(StudentServiceFeeTypeDto studentServiceFeeTypeDto);


    /**
     * 回显类型和公司的关系
     * @param typeId
     * @return
     */
    ListResponseBo<CompanyTreeVo> getTypeCompanyRelation(Long typeId);


    /**
     * 服务费类型下拉
     * @param companyId
     * @return
     */
    List<BaseSelectEntity> getServiceTypeList(Long companyId);
}
