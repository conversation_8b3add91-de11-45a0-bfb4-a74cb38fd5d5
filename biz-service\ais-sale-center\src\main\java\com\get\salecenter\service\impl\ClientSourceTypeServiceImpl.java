package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.ClientSourceTypeMapper;
import com.get.salecenter.vo.ClientSourceTypeVo;
import com.get.salecenter.entity.ClientSourceType;
import com.get.salecenter.service.IClientSourceTypeService;
import com.get.salecenter.dto.ClientSourceTypeListDto;
import com.get.salecenter.dto.ClientSourceTypeDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 学生资源推荐来源类型Service实现类
 */
@Service
public class ClientSourceTypeServiceImpl extends ServiceImpl<ClientSourceTypeMapper, ClientSourceType> implements IClientSourceTypeService {

    @Resource
    private ClientSourceTypeMapper clientSourceTypeMapper;
    @Resource
    private UtilService utilService;


    /**
     * 新增
     *
     * @param clientSourceTypeDto 新增参数
     * @return
     */
    @Override
    public Long addClientSourceType(ClientSourceTypeDto clientSourceTypeDto) {
        if (GeneralTool.isEmpty(clientSourceTypeDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        Integer maxViewOrder = clientSourceTypeMapper.getMaxViewOrder();
        ClientSourceType clientSourceType = BeanCopyUtils.objClone(clientSourceTypeDto, ClientSourceType::new);
        if (validateAdd(clientSourceTypeDto)) {
            clientSourceType.setViewOrder(maxViewOrder);
            utilService.setCreateInfo(clientSourceType);
            clientSourceTypeMapper.insert(clientSourceType);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        return clientSourceType.getId();
    }

    private boolean validateAdd(ClientSourceTypeDto clientSourceTypeDto) {
        List<ClientSourceType> list = this.clientSourceTypeMapper.selectList(Wrappers.<ClientSourceType>lambdaQuery()
                .eq(ClientSourceType::getTypeKey, clientSourceTypeDto.getTypeKey()));
        return GeneralTool.isEmpty(list);
    }

    /**
     * 修改
     *
     * @param clientSourceTypeDto 修改参数
     * @return
     */
    @Override
    public ClientSourceTypeVo updateClientSourceType(ClientSourceTypeDto clientSourceTypeDto) {
        if (GeneralTool.isEmpty(clientSourceTypeDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(clientSourceTypeDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ClientSourceType clientSourceType = BeanCopyUtils.objClone(clientSourceTypeDto, ClientSourceType::new);
        utilService.setUpdateInfo(clientSourceType);
        clientSourceTypeMapper.updateById(clientSourceType);
        return findClientSourceTypeById(clientSourceTypeDto.getId());
    }

    /**
     * 删除
     *
     * @param id
     */
    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ClientSourceType clientSourceType = clientSourceTypeMapper.selectById(id);
        if (GeneralTool.isEmpty(clientSourceType)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        int delete = clientSourceTypeMapper.deleteById(id);
        if (delete < 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @Override
    public ClientSourceTypeVo findClientSourceTypeById(Long id) {
        ClientSourceType clientSourceType = clientSourceTypeMapper.selectById(id);
        return BeanCopyUtils.objClone(clientSourceType, ClientSourceTypeVo::new);
    }

    /**
     * 获取学生资源推荐来源类型列表
     *
     * @param clientSourceTypeListDto 查询条件
     * @param page                   分页
     * @return
     */
    @Override
    public List<ClientSourceTypeVo> getClientSourceTypeList(ClientSourceTypeListDto clientSourceTypeListDto, Page page) {
        LambdaQueryWrapper<ClientSourceType> wrapper = Wrappers.lambdaQuery(ClientSourceType.class);
        if (GeneralTool.isNotEmpty(clientSourceTypeListDto.getTypeName())) {
            wrapper.like(ClientSourceType::getTypeName, clientSourceTypeListDto.getTypeName());
        }
        wrapper.orderByDesc(ClientSourceType::getViewOrder);
        IPage<ClientSourceType> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<ClientSourceType> clientSourceTypes = clientSourceTypeMapper.selectPage(iPage, wrapper).getRecords();
        page.setAll((int) iPage.getTotal());
        if (GeneralTool.isEmpty(clientSourceTypes)) {
            return Collections.emptyList();
        }
        return BeanCopyUtils.copyListProperties(clientSourceTypes, ClientSourceTypeVo::new);
    }

    /**
     * 上下移动
     *
     * @param clientSourceTypeDtos 移动参数列表
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void movingOrder(List<ClientSourceTypeDto> clientSourceTypeDtos) {
        if (GeneralTool.isEmpty(clientSourceTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        ClientSourceType clientSourceType1 = BeanCopyUtils.objClone(clientSourceTypeDtos.get(0), ClientSourceType::new);
        ClientSourceType clientSourceType2 = BeanCopyUtils.objClone(clientSourceTypeDtos.get(1), ClientSourceType::new);
        Integer oneOrder = clientSourceType1.getViewOrder();
        Integer twoOrder = clientSourceType2.getViewOrder();
        clientSourceType1.setViewOrder(twoOrder);
        clientSourceType2.setViewOrder(oneOrder);
        utilService.setUpdateInfo(clientSourceType1);
        utilService.setUpdateInfo(clientSourceType2);
        clientSourceTypeMapper.updateById(clientSourceType1);
        clientSourceTypeMapper.updateById(clientSourceType2);
    }

    @Override
    public List<ClientSourceTypeVo> getClientSourceTypeSelect() {
        List<ClientSourceType> clientSourceTypes = clientSourceTypeMapper.selectList(Wrappers.lambdaQuery(ClientSourceType.class)
                .orderByDesc(ClientSourceType::getViewOrder));
        if (GeneralTool.isEmpty(clientSourceTypes)) {
            return Collections.emptyList();
        }
        return BeanCopyUtils.copyListProperties(clientSourceTypes, ClientSourceTypeVo::new);
    }

}
