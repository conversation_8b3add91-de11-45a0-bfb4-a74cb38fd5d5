package com.get.salecenter.dto.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Data
public class BdStudentStatisticalComparisonQueryDto {

//    @ApiModelProperty(value = "公司id")
//    private List<Long> fkCompanyIds;
//
//    @ApiModelProperty(value = "员工ids")
//    private Set<Long> fkStaffIds;
//
//    @ApiModelProperty("同期比对年数")
//    private Integer comparisonYears;
//
//    @ApiModelProperty("创建时间(开始)")
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date studentBeginTime;
//
//    @ApiModelProperty("创建时间(结束)")
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date studentEndTime;
//
//    @ApiModelProperty(value = "项目角色key")
//    private String projectRoleKey;
//
//    @ApiModelProperty(value = "项目角色名")
//    private String projectRoleName;
//
//    @ApiModelProperty("统计类型 1：按bd比对统计 2：按区域比对统计 3：按大区比对统计 4：按代理比对统计 5：无申请代理列表 6:项目学生统计对比报表 7:首页柱状图 8：结算表统计")
//    @NotNull(message = "统计类型不能为空")
//    private Integer statisticalType;
//
//    @ApiModelProperty("小计显示模式 true:隐藏小计 false:不隐藏小计")
//    private Boolean displayModeFlag = true;
//
//    @ApiModelProperty("总计显示模式 true:隐藏总计 false:不隐藏总计")
//    private Boolean totalDisplayModeFlag = true;
//
//    @ApiModelProperty("是否区分业务国家 true:是 false:不区分")
//    private Boolean isDistinguishCountryFlag = false;
//
//    @ApiModelProperty(value = "业务国家ids")
//    private Set<Long> areaCountryIds;
//
//    @ApiModelProperty(value = "结算状态：0未结算 1已结算")
//    private Integer statusSettlement;
//
//    //========================
//    @ApiModelProperty(value = "bd大区Id List")
//    private Set<Long> fkAreaRegionIdLIst;
//    @ApiModelProperty(value = "代理所在城市Id LIst")
//    private Set<Long> fkAreaCityIdList;
//    @ApiModelProperty(value = "代理所在州省Id LIst")
//    private Set<Long> fkAreaStateIdList;
//
//    @ApiModelProperty(value = "代理编号文本 逗号分割")
//    private String agentNumStr;

    @ApiModelProperty(value = "公司id")
    @NotEmpty(message = "公司id不能为空")
    private List<Long> fkCompanyIds;

    @ApiModelProperty("统计类型 1：按bd比对统计 2：按区域比对统计 3：按大区比对统计 4：按代理比对统计 5：无申请代理列表 6:项目学生统计对比报表 7:首页柱状图 8：结算表统计")
    @NotNull(message = "统计类型不能为空")
    private Integer statisticalType;

    @ApiModelProperty(value = "结算状态：0未结算 1已结算")
    private Integer statusSettlement;

    @ApiModelProperty("创建时间(开始)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date studentBeginTime;

    @ApiModelProperty("创建时间(结束)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date studentEndTime;

    @ApiModelProperty("同期比对年数")
    private Integer comparisonYears;

    @ApiModelProperty("是否区分业务国家 true:是  false:不区分")
    private Boolean isDistinguishCountryFlag = false;

    @ApiModelProperty("小计显示模式 true:隐藏小计  false:不隐藏小计")
    private Boolean displayModeFlag = true;

    @ApiModelProperty("总计显示模式 true:隐藏总计  false:不隐藏总计")
    private Boolean totalDisplayModeFlag = true;

    @ApiModelProperty(value = "业务国家ids")
    private Set<Long> areaCountryIds;

    @ApiModelProperty(value = "代理所在国家Id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "代理所在州省Id LIst")
    private Set<Long> fkAreaStateIdList;

    @ApiModelProperty(value = "代理所在城市Id LIst")
    private Set<Long> fkAreaCityIdList;

    @ApiModelProperty(value = "bd大区Id List")
    private Set<Long> fkAreaRegionIdLIst;

    @ApiModelProperty(value = "员工ids")
    private Set<Long> fkStaffIds;

    @ApiModelProperty(value = "代理编号文本 逗号分割")
    private String agentNumStr;

//    @ApiModelProperty(value = "项目角色id")
//    private Long projectRoleId;

    @ApiModelProperty(value = "项目角色key")
    private String projectRoleKey;

    @ApiModelProperty(value = "项目角色名")
    private String projectRoleName;

    @ApiModelProperty(value = "角色成员Ids")
    private List<Long> projectStaffIds;

    @ApiModelProperty(value = "角色成员名称")
    private String projectStaffNameOrEnName;

    @ApiModelProperty(value = "绑定BD id")
    private List<Long> fkBdIds;

    @ApiModelProperty(value = "绑定BD名称或编号")
    private String bdNameOrCode;

    /**
     * 结算日期
     */
    @ApiModelProperty(value = "结算日期（4位年月），格式如：202303")
    private String settlementDate;


    @ApiModelProperty("柱状图跳转类型 1：旧学生数（首次） 2：新学生数（首次） 3：提交数 4：OS数 5：签证数 6：入学数 7：旧学生数" +
            " 8：新学生数 9:已申请反馈数 10:学生数（首次） 11：未结算OS  12：未结算提交数 13：未结算签证  14：未结算成功" +
            " 15：未结算申请反馈数  16：已结算OS  17：已结算提交数 18：已结算签证  19：已结算成功 20：已结算申请反馈数 21:未结算后补签证数" +
            " 22：已结算后补签证数  23：后补签证数 24：OS重复学生数  25：延迟提交数  26：延迟反馈数 27:未结算延迟提交数 28:已结算延迟提交数" +
            " 29：未结算延迟反馈数  30:已结算延迟反馈数 31:缺资料数")
    private Integer jumpType;

    @ApiModelProperty("跳转参数：国家id")
    private Long jumpCountryId;

    @ApiModelProperty("跳转参数：bdId")
    private Integer jumpBdId;

    @ApiModelProperty("跳转参数：城市区域Id")
    private Integer jumpCityId;

    @ApiModelProperty("跳转参数：大区Id")
    private Integer jumpAreaRegionId;

    @ApiModelProperty("跳转参数：代理Id")
    private Integer jumpAgentId;

    @ApiModelProperty("跳转参数：项目成员角色Id")
    private Integer jumpRoleId;

    @ApiModelProperty("跳转参数：项目成员员工Id")
    private Integer jumpRoleStaffId;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列  1：新学生数（首次） 2：新学生数 3：OS数")
    private Integer viewOrder;


}
