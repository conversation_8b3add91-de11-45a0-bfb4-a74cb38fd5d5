package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseEntity;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.*;
import com.get.institutioncenter.vo.CompanyTreeVo;
import com.get.institutioncenter.vo.ContractFormulaCommissionVo;
import com.get.institutioncenter.vo.ContractFormulaFeignVo;
import com.get.institutioncenter.vo.ContractFormulaVo;
import com.get.institutioncenter.entity.*;
import com.get.institutioncenter.service.*;
import com.get.institutioncenter.dto.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2021/1/8 10:12
 * @verison: 1.0
 * @description:
 */
@Service
public class ContractFormulaServiceImpl extends BaseServiceImpl<ContractFormulaMapper, ContractFormula> implements IContractFormulaService {
    @Resource
    private ContractFormulaMapper contractFormulaMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IContractFormulaAreaCountryService contractFormulaAreaCountryService;
    @Resource
    private IContractFormulaCourseTypeService contractFormulaCourseTypeService;
    @Resource
    private IContractFormulaMajorLevelService contractFormulaMajorLevelService;
    @Resource
    private IContractFormulaCompanyService contractFormulaCompanyService;
    @Resource
    private IContractFormulaAreaCountryStudentService contractFormulaAreaCountryStudentService;
    @Resource
    private InstitutionCourseTypeMapper institutionCourseTypeMapper;
    @Resource
    private InstitutionCourseMapper institutionCourseMapper;
    @Resource
    private MajorLevelMapper majorLevelMapper;
    @Resource
    private ContractFormulaCompanyMapper contractFormulaCompanyMapper;
    @Resource
    private IContractFormulaInstitutionService contractFormulaInstitutionService;
    @Resource
    private IContractFormulaInstitutionCourseService contractFormulaInstitutionCourseService;
    @Resource
    private IContractFormulaCommissionService contractFormulaCommissionService;
    @Resource
    private IContractFormulaInstitutionFacultyService contractFormulaInstitutionFacultyService;
    @Resource
    private IContractFormulaPreInstitutionGroupService contractFormulaPreInstitutionGroupService;
    @Resource
    private IContractFormulaInstitutionZoneService contractFormulaInstitutionZoneService;
    @Resource
    private IContractFormulaPreInstitutionService contractFormulaPreInstitutionService;
    @Resource
    private IContractFormulaPreMajorLevelService contractFormulaPreMajorLevelService;
    @Resource
    private ContractFormulaInstitutionCourseMapper contractFormulaInstitutionCourseMapper;
    @Resource
    private ContractFormulaAreaCountryStudentMapper contractFormulaAreaCountryStudentMapper;
    @Resource
    private ContractFormulaAreaCountryMapper contractFormulaAreaCountryMapper;
    @Resource
    private ContractFormulaCourseTypeMapper contractFormulaCourseTypeMapper;
    @Resource
    private ContractFormulaMajorLevelMapper contractFormulaMajorLevelMapper;
    @Resource
    private InstitutionCourseMajorLevelMapper institutionCourseMajorLevelMapper;
    @Resource
    private ContractFormulaInstitutionMapper contractFormulaInstitutionMapper;
    @Resource
    private ContractFormulaInstitutionFacultyMapper contractFormulaInstitutionFacultyMapper;
    @Resource
    private InstitutionCourseFacultyMapper institutionCourseFacultyMapper;
    @Resource
    private ContractFormulaInstitutionZoneMapper contractFormulaInstitutionZoneMapper;
    @Resource
    private InstitutionCourseZoneMapper institutionCourseZoneMapper;
    @Resource
    private IContractFormulaInstitutionChannelService contractFormulaInstitutionChannelService;
    @Resource
    private ContractFormulaAreaCountryStudentExcludeMapper excludeMapper;
    @Resource
    private IContractFormulaAreaCountryStudentExcludeService contractFormulaAreaCountryStudentExcludeService;

    @Override
    public ContractFormulaVo findContractFormulaById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ContractFormula contractFormula = contractFormulaMapper.selectById(id);
        if (GeneralTool.isEmpty(contractFormula)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ContractFormulaVo contractFormulaVo = BeanCopyUtils.objClone(contractFormula, ContractFormulaVo::new);
        contractFormulaVo.setCountryIdList(contractFormulaAreaCountryService.getCountryIdListByFkid(id));
        contractFormulaVo.setCourseTypeIdList(contractFormulaCourseTypeService.getCourseTypeIdListByFkid(id));
        contractFormulaVo.setMajorLevelIdList(contractFormulaMajorLevelService.getMajorLevelIdListByFkid(id));
        contractFormulaVo.setCompanyIdList(contractFormulaCompanyService.getCompanyIdListByFkid(id));
        contractFormulaVo.setStudentCountryIdList(contractFormulaAreaCountryStudentService.getStudentCountryIdListByFkid(id));
        contractFormulaVo.setInstitutionIdList(contractFormulaInstitutionService.getInstitutionIdListByFkid(id));
        contractFormulaVo.setCourseIdList(contractFormulaInstitutionCourseService.getCourseIdListByFkid(id));
        contractFormulaVo.setContractFormulaCommissionDtos(contractFormulaCommissionService.getContractFormulaCommissionDtoByFkid(id));
        contractFormulaVo.setMajorLevelIdList(contractFormulaMajorLevelService.getMajorLevelIdListByFkid(id));
        contractFormulaVo.setZoneIdList(contractFormulaInstitutionZoneService.getZoneIdListByFkid(id));
        contractFormulaVo.setFacultyIdList(contractFormulaInstitutionFacultyService.getFacultyIdListByFkid(id));
        contractFormulaVo.setPreGroupIdList(contractFormulaPreInstitutionGroupService.getGroupIdListByFkid(id));
        contractFormulaVo.setPreInstitutionIdList(contractFormulaPreInstitutionService.getInstiutionIdListByFkId(id));
        contractFormulaVo.setPreMajorLevelList(contractFormulaPreMajorLevelService.getMajorLevelIdListByFkid(id));
        contractFormulaVo.setStudentCountryIdNotInList(contractFormulaAreaCountryService.getCountryIdNotInListByFkid(id));

        //设置所属渠道
        contractFormulaVo.setInstitutionChannelNames(contractFormulaInstitutionChannelService.getInstitutionChannelNamesById(id));
        contractFormulaVo.setInstitutionChannelIds(contractFormulaInstitutionChannelService.getInstitutionChannelIdsById(id));
        return contractFormulaVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addContractFormula(ContractFormulaDto contractFormulaDto) {
        if (GeneralTool.isEmpty(contractFormulaDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (GeneralTool.isNotEmpty(contractFormulaDto.getCountType())) {
            if (GeneralTool.isEmpty(contractFormulaDto.getCountValeMax()) || GeneralTool.isEmpty(contractFormulaDto.getCountValeMin())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("missing_maximum_or_minimum"));
            }
        }
        ContractFormula contractFormula = BeanCopyUtils.objClone(contractFormulaDto, ContractFormula::new);
        utilService.updateUserInfoToEntity(contractFormula);
        contractFormulaMapper.insertSelective(contractFormula);
        Long contractFormulaId = contractFormula.getId();
        //设置最大排序
        contractFormula.setViewOrder(contractFormulaMapper.getMaxViewOrder(contractFormulaDto.getFkInstitutionProviderId()));
        contractFormulaMapper.updateById(contractFormula);
        //国籍（学生出生地国家/地区）【不包括】
        if (GeneralTool.isNotEmpty(contractFormulaDto.getStudentCountryIdNotInList())) {
            List<Long> contractFormulaAreaCountryStudentExcludes = contractFormulaDto.getStudentCountryIdNotInList();
            for (Long countryId : contractFormulaAreaCountryStudentExcludes) {
                ContractFormulaAreaCountryStudentExclude contractFormulaAreaCountryStudentExclude = new ContractFormulaAreaCountryStudentExclude();
                contractFormulaAreaCountryStudentExclude.setFkAreaCountryId(countryId);
                contractFormulaAreaCountryStudentExclude.setFkContractFormulaId(contractFormulaId);
                utilService.updateUserInfoToEntity(contractFormulaAreaCountryStudentExclude);
                excludeMapper.insertSelective(contractFormulaAreaCountryStudentExclude);
            }
        }

        //设置渠道-多选
        if (GeneralTool.isNotEmpty(contractFormulaDto.getInstitutionChannelIds())) {
            List<Long> fkInstitutionChannelIdList = contractFormulaDto.getInstitutionChannelIds().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (GeneralTool.isNotEmpty(fkInstitutionChannelIdList)) {
                for (Long fkInstitutionChannelId : fkInstitutionChannelIdList) {
                    ContractFormulaInstitutionChannelDto contractFormulaInstitutionChannelDto = new ContractFormulaInstitutionChannelDto();
                    contractFormulaInstitutionChannelDto.setFkContractFormulaId(contractFormulaId);
                    contractFormulaInstitutionChannelDto.setFkInstitutionChannelId(fkInstitutionChannelId);
                    contractFormulaInstitutionChannelService.addContractFormulaInstitutionChannel(contractFormulaInstitutionChannelDto);
                }
            }
        }
        //同时插入中间表内容
        insertTable(contractFormulaDto, contractFormulaId);
        //默认为登录人所属公司
        List<ContractFormulaCompanyDto> list = new ArrayList<>();
        for (Long companyId : SecureUtil.getCompanyIds()) {
            ContractFormulaCompanyDto agentContractFormulaCompanyDto = new ContractFormulaCompanyDto();
            agentContractFormulaCompanyDto.setFkContractFormulaId(contractFormulaId);
            agentContractFormulaCompanyDto.setFkCompanyId(companyId);
            list.add(agentContractFormulaCompanyDto);
        }
        contractFormulaCompanyService.editContractFormulaCompany(list);
        return contractFormulaId;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (contractFormulaMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //删除中间表
        deleteTable(id);
        contractFormulaMapper.deleteById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ContractFormulaVo updateContractFormula(ContractFormulaDto contractFormulaDto) {
        if (contractFormulaDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ContractFormula result = contractFormulaMapper.selectById(contractFormulaDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (GeneralTool.isNotEmpty(contractFormulaDto.getCountType())) {
            if (GeneralTool.isEmpty(contractFormulaDto.getCountValeMax()) || GeneralTool.isEmpty(contractFormulaDto.getCountValeMin())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("missing_maximum_or_minimum"));
            }
        }
        ContractFormula contractFormula = BeanCopyUtils.objClone(contractFormulaDto, ContractFormula::new);
        //修改中间表 先删在增
        deleteTable(contractFormula.getId());
        insertTable(contractFormulaDto, contractFormulaDto.getId());

        utilService.updateUserInfoToEntity(contractFormula);
        contractFormulaMapper.updateByIdWithNull(contractFormula);

        //所属渠道修改
        List<Long> fkInstitutionChannelIdList = contractFormulaDto.getInstitutionChannelIds();
        if (GeneralTool.isNotEmpty(fkInstitutionChannelIdList)) {
            contractFormulaInstitutionChannelService.deleteByContractFormulaId(contractFormulaDto.getId());
            for (Long fkInstitutionChannelId : fkInstitutionChannelIdList) {
                ContractFormulaInstitutionChannelDto contractFormulaInstitutionChannelDto = new ContractFormulaInstitutionChannelDto();
                contractFormulaInstitutionChannelDto.setFkContractFormulaId(contractFormulaDto.getId());
                contractFormulaInstitutionChannelDto.setFkInstitutionChannelId(fkInstitutionChannelId);
                contractFormulaInstitutionChannelService.addContractFormulaInstitutionChannel(contractFormulaInstitutionChannelDto);
            }
        }
        return findContractFormulaById(contractFormulaDto.getId());
    }

    @Override
    public List<ContractFormulaVo> getContractFormulas(ContractFormulaDto contractFormulaDto, Page page) {
        List<ContractFormulaVo> convertDatas = new ArrayList<>();
        if (GeneralTool.isEmpty(SecureUtil.getCompanyIds())) {
            return convertDatas;
        }
        //获取分页数据
        IPage<ContractFormula> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<ContractFormula> contractFormulas = contractFormulaMapper.getContractFormulas(pages, contractFormulaDto, SecureUtil.getCompanyIds());
        page.setAll((int) pages.getTotal());
        //合同公式id集合
        List<Long> contractFormulaIds = contractFormulas.stream().map(BaseEntity::getId).collect(Collectors.toList());
        //对应公司名称map
        Map<Long, String> companyNameMap = contractFormulaCompanyService.getCompanyNameMapByFkids(contractFormulaIds);

        for (ContractFormula contractFormula : contractFormulas) {
            ContractFormulaVo contractFormulaVo = BeanCopyUtils.objClone(contractFormula, ContractFormulaVo::new);
            //设置返回结果
            contractFormulaVo.setCountryName(contractFormulaAreaCountryService.getCountryNameByFkid(contractFormulaVo.getId()));
            contractFormulaVo.setCourseTypeName(contractFormulaCourseTypeService.getCourseTypeNameByFkid(contractFormulaVo.getId()));
            contractFormulaVo.setMajorLevelName(contractFormulaMajorLevelService.getMajorLevelNameByFkid(contractFormulaVo.getId()));
            contractFormulaVo.setStudentCountryName(contractFormulaAreaCountryStudentService.getStudentCountryNameByFkid(contractFormulaVo.getId()));
            contractFormulaVo.setCompanyName(companyNameMap.get(contractFormulaVo.getId()));
            contractFormulaVo.setFormulaTypeName(ProjectExtraEnum.getValueByKey(contractFormulaVo.getFormulaType(), ProjectExtraEnum.FORMULA_TYPE));
            contractFormulaVo.setCountTypeName(ProjectExtraEnum.getValueByKey(contractFormulaVo.getCountType(), ProjectExtraEnum.COUNT_TYPE));
            contractFormulaVo.setInstitutionName(contractFormulaInstitutionService.getInstitutionNameByFkid(contractFormulaVo.getId()));
            contractFormulaVo.setCourseName(contractFormulaInstitutionCourseService.getCourseNameByFkid(contractFormulaVo.getId()));
            contractFormulaVo.setContractFormulaCommissionDtos(contractFormulaCommissionService.getContractFormulaCommissionDtoByFkid(contractFormulaVo.getId()));
            convertDatas.add(contractFormulaVo);
        }
        return convertDatas;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<ContractFormulaDto> contractFormulaDtos) {
        if (GeneralTool.isEmpty(contractFormulaDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ContractFormula ro = BeanCopyUtils.objClone(contractFormulaDtos.get(0), ContractFormula::new);
        Integer oneorder = ro.getViewOrder();
        ContractFormula rt = BeanCopyUtils.objClone(contractFormulaDtos.get(1), ContractFormula::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        contractFormulaMapper.updateById(ro);
        contractFormulaMapper.updateById(rt);
    }

    @Override
    public void editContractFormulaCompany(List<ContractFormulaCompanyDto> contractFormulaCompanyDtos) {
        contractFormulaCompanyService.editContractFormulaCompany(contractFormulaCompanyDtos);
    }

    @Override
    public List<Map<String, Object>> findFormulaType() {
        return ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.FORMULA_TYPE);
    }

    @Override
    public List<Map<String, Object>> findConditionType() {
        return ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.CONDITION_TYPE_ONLY);
    }

    @Override
    public List<Map<String, Object>> findCountType() {
        /*     String translationConfig = redisService.get(GetAuthInfo.getLoginId());*/
        return ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.COUNT_TYPE);
    }

    @Override
    public List<BaseSelectEntity> getContractFormulaList(Long institutionProviderId) {
        List<BaseSelectEntity> baseSelectEntityList = new ArrayList<>();
        LambdaQueryWrapper<ContractFormula> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContractFormula::getFkInstitutionProviderId, institutionProviderId);
        wrapper.orderByDesc(ContractFormula::getViewOrder);
        List<ContractFormula> contractFormulas = contractFormulaMapper.selectList(wrapper);
        for (ContractFormula contractFormula : contractFormulas) {
            BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
            baseSelectEntity.setId(contractFormula.getId());
            baseSelectEntity.setName(contractFormula.getTitle());
            baseSelectEntityList.add(baseSelectEntity);
        }
        return baseSelectEntityList;
    }

    @Override
    public Map<Long, String> getContractFormulasByIds(Set<Long> ids) {
        if (GeneralTool.isEmpty(ids)) {
            ids.add(0L);
        }
        Map<Long, String> map = new HashMap<>();
        LambdaQueryWrapper<ContractFormula> wrapper = new LambdaQueryWrapper();
        wrapper.in(ContractFormula::getId, ids);
        wrapper.orderByDesc(ContractFormula::getViewOrder);
        List<ContractFormula> contractFormulas = contractFormulaMapper.selectList(wrapper);
        for (ContractFormula contractFormula : contractFormulas) {
            //key-合同公式id  value-合同公式内容拼接内容
            map.put(contractFormula.getId(), contractFormula.getTitle());
        }
        return map;
    }

    @Override
    public List<ContractFormula> getContractFormulasByOfferItem(Long companyId, Long areaCountryId, InstitutionStudentOfferItemDto studentOfferItem) {
        return contractFormulaMapper.getContractFormulasByOfferItem(companyId, areaCountryId, studentOfferItem);
    }

    @Override
    public List<Long> getCourseIdsByContractFormulaId(Long id) {
        List<Long> typeIds = institutionCourseTypeMapper.getTypeIdsByContractFormula(id);
        List<Long> levelIds = majorLevelMapper.getMajorLevelByContractFormula(id);
        List<Long> courseIds = institutionCourseMapper.getCourseIdsByTypeAndLevel(typeIds, levelIds);
        return courseIds;
    }

    @Override
    public List<ContractFormulaCommissionVo> getContractFormulaCommissionByContractFormulaId(Long id) {
        return contractFormulaCommissionService.getContractFormulaCommissionDtoByFkid(id);
    }

    /**
     * feign调用 通过合同公式查询前置学校
     *
     * @Date 16:03 2021/6/10
     * <AUTHOR>
     */
    @Override
    public List<Long> getContractFormulaPreInstitutionByContractFormulaId(Long id) {
        return contractFormulaPreInstitutionService.getInstiutionIdListByFkId(id);
    }

    /**
     * feign调用 通过合同公式查询前置集团
     *
     * @Date 16:18 2021/6/10
     * <AUTHOR>
     */
    @Override
    public List<Long> getPreInstitutionGroupByContractFormulaId(Long id) {
        return contractFormulaPreInstitutionGroupService.getGroupIdListByFkid(id);
    }

    /**
     * feign调用 通过合同公式查询前置课程等级
     *
     * @Date 16:19 2021/6/10
     * <AUTHOR>
     */
    @Override
    public List<Long> getPreMajorLevelByContractFormulaId(Long id) {
        return contractFormulaPreMajorLevelService.getMajorLevelIdListByFkid(id);
    }

    /**
     * feign调用 通过合同公式id查询合同公式  学校课程信息
     *
     * @Date 12:13 2021/6/15
     * <AUTHOR>
     */
    @Override
    public ContractFormulaFeignVo getContractFormulaConfigByContractFormulaId(Long formulaId) {
        ContractFormulaFeignVo contractFormulaFeignVo = new ContractFormulaFeignVo();
        //合同公式国家区域
        List<Long> countryIdList = contractFormulaAreaCountryService.getCountryIdListByFkid(formulaId);
        contractFormulaFeignVo.setAreaCountryIdList(countryIdList);
        //合同公式学校
        List<Long> institutionIdList = contractFormulaInstitutionService.getInstitutionIdListByFkid(formulaId);
        contractFormulaFeignVo.setInstitutionIdList(institutionIdList);
        //合同公式课程
        List<Long> courseIdList = contractFormulaInstitutionCourseService.getCourseIdListByFkid(formulaId);
        //合同公式校区
        List<Long> zoneIdList = contractFormulaInstitutionZoneService.getZoneIdListByFkid(formulaId);
        //合同公式学院
        List<Long> facultyIdList = contractFormulaInstitutionFacultyService.getFacultyIdListByFkid(formulaId);
        //合同公式课程等级
        List<Long> majorLevelIdList = contractFormulaMajorLevelService.getMajorLevelIdListByFkid(formulaId);
        //合同公式课程类型
        List<Long> courseTypeIdList = contractFormulaCourseTypeService.getCourseTypeIdListByFkid(formulaId);
        courseIdList = contractFormulaInstitutionCourseService.getCourseIdListByFaculty(courseIdList, zoneIdList, facultyIdList, majorLevelIdList, courseTypeIdList);
        contractFormulaFeignVo.setInstitutionCourseIdList(courseIdList);
        return contractFormulaFeignVo;
    }

    /**
     * 初步检查学习计划-合同公式匹配
     *
     * @Date 16:13 2021/7/16
     * <AUTHOR>
     */
    @Override
    public String checkContractFormula(Long studentCompanyId, Long areaCountryId, Long fkInstitutionCourseId, Long contractFormulaId) {
        LambdaQueryWrapper<ContractFormulaCompany> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContractFormulaCompany::getFkContractFormulaId, contractFormulaId).eq(ContractFormulaCompany::getFkCompanyId, studentCompanyId);
        List<ContractFormulaCompany> contractFormulaCompanies = contractFormulaCompanyMapper.selectList(wrapper);
        if (GeneralTool.isEmpty(contractFormulaCompanies)) {
            return "学生公司与合同公式公司不匹配";
        }
        LambdaQueryWrapper<ContractFormulaInstitutionCourse> wrapper1 = new LambdaQueryWrapper();
        wrapper1.eq(ContractFormulaInstitutionCourse::getFkContractFormulaId, contractFormulaId);
        List<ContractFormulaInstitutionCourse> contractFormulaInstitutionCourses = contractFormulaInstitutionCourseMapper.selectList(wrapper1);
        //有课程限制
        if (GeneralTool.isNotEmpty(contractFormulaInstitutionCourses)) {
            wrapper1.eq(ContractFormulaInstitutionCourse::getFkInstitutionCourseId, fkInstitutionCourseId);
            contractFormulaInstitutionCourses = contractFormulaInstitutionCourseMapper.selectList(wrapper1);
            if (GeneralTool.isEmpty(contractFormulaInstitutionCourses)) {
                return "学生课程与合同公式课程限制不匹配";
            }
        }
        LambdaQueryWrapper<ContractFormulaAreaCountryStudent> wrapper2 = new LambdaQueryWrapper();
        wrapper2.eq(ContractFormulaAreaCountryStudent::getFkContractFormulaId, contractFormulaId);
        List<ContractFormulaAreaCountryStudent> contractFormulaAreaCountryStudents = contractFormulaAreaCountryStudentMapper.selectList(wrapper2);
        //有学生来源国家/区域限制
        if (GeneralTool.isNotEmpty(contractFormulaAreaCountryStudents)) {
            wrapper2.eq(ContractFormulaAreaCountryStudent::getFkAreaCountryId, areaCountryId);
            contractFormulaAreaCountryStudents = contractFormulaAreaCountryStudentMapper.selectList(wrapper2);
            if (GeneralTool.isNotEmpty(contractFormulaAreaCountryStudents)) {
                return "学生来源国家/区域与合同公式限制不匹配";
            }
        }
        LambdaQueryWrapper<ContractFormulaAreaCountry> wrapper3 = new LambdaQueryWrapper();
        wrapper3.eq(ContractFormulaAreaCountry::getFkContractFormulaId, contractFormulaId);
        List<ContractFormulaAreaCountry> contractFormulaAreaCountries = contractFormulaAreaCountryMapper.selectList(wrapper3);
        //有学生申请国家/区域限制
        if (GeneralTool.isNotEmpty(contractFormulaAreaCountries)) {
            wrapper3.eq(ContractFormulaAreaCountry::getFkAreaCountryId, areaCountryId);
            contractFormulaAreaCountries = contractFormulaAreaCountryMapper.selectList(wrapper3);
            if (GeneralTool.isNotEmpty(contractFormulaAreaCountries)) {
                return "学生申请国家/区域与合同公式限制不匹配";
            }
        }
        LambdaQueryWrapper<ContractFormulaCourseType> wrapper4 = new LambdaQueryWrapper();
        wrapper4.eq(ContractFormulaCourseType::getFkContractFormulaId, contractFormulaId);
        List<ContractFormulaCourseType> contractFormulaCourseTypes = contractFormulaCourseTypeMapper.selectList(wrapper4);
        //有课程类型限制
        if (GeneralTool.isNotEmpty(contractFormulaCourseTypes)) {
            LambdaQueryWrapper<InstitutionCourseType> wrapper5 = new LambdaQueryWrapper();
            wrapper5.eq(InstitutionCourseType::getFkInstitutionCourseId, fkInstitutionCourseId);
            List<InstitutionCourseType> institutionCourseTypes = institutionCourseTypeMapper.selectList(wrapper5);
            Set<Long> courseTypeIdS = institutionCourseTypes.stream().map(InstitutionCourseType::getFkCourseTypeId).collect(Collectors.toSet());
            wrapper5.in(InstitutionCourseType::getFkCourseTypeId, courseTypeIdS);
            contractFormulaCourseTypes = contractFormulaCourseTypeMapper.selectList(wrapper4);
            if (GeneralTool.isNotEmpty(contractFormulaCourseTypes)) {
                return "学生课程类型与合同公式限制不匹配";
            }
        }
        LambdaQueryWrapper<ContractFormulaMajorLevel> wrapper6 = new LambdaQueryWrapper();
        wrapper6.eq(ContractFormulaMajorLevel::getFkContractFormulaId, contractFormulaId);
        List<ContractFormulaMajorLevel> contractFormulaMajorLevels = contractFormulaMajorLevelMapper.selectList(wrapper6);
        //有课程等级限制
        if (GeneralTool.isNotEmpty(contractFormulaMajorLevels)) {
            LambdaQueryWrapper<InstitutionCourseMajorLevel> wrapper7 = new LambdaQueryWrapper();
            wrapper7.eq(InstitutionCourseMajorLevel::getFkInstitutionCourseId, fkInstitutionCourseId);
            List<InstitutionCourseMajorLevel> institutionCourseMajorLevels = institutionCourseMajorLevelMapper.selectList(wrapper7);
            Set<Long> courseMajorLevelIdS = institutionCourseMajorLevels.stream().map(InstitutionCourseMajorLevel::getFkMajorLevelId).collect(Collectors.toSet());
            wrapper6.eq(ContractFormulaMajorLevel::getFkMajorLevelId, courseMajorLevelIdS);
            contractFormulaMajorLevels = contractFormulaMajorLevelMapper.selectList(wrapper6);
            if (GeneralTool.isNotEmpty(contractFormulaMajorLevels)) {
                return "学生课程等级与合同公式限制不匹配";
            }
        }
        LambdaQueryWrapper<ContractFormulaInstitution> wrapper8 = new LambdaQueryWrapper();
        wrapper8.eq(ContractFormulaInstitution::getFkContractFormulaId, contractFormulaId);
        List<ContractFormulaInstitution> contractFormulaInstitutions = contractFormulaInstitutionMapper.selectList(wrapper8);
        //有学生申请学校
        if (GeneralTool.isNotEmpty(contractFormulaInstitutions)) {
            wrapper8.eq(ContractFormulaInstitution::getFkInstitutionId, areaCountryId);
            contractFormulaInstitutions = contractFormulaInstitutionMapper.selectList(wrapper8);
            if (GeneralTool.isNotEmpty(contractFormulaInstitutions)) {
                return "学生申请学校与合同公式限制不匹配";
            }
        }
        LambdaQueryWrapper<ContractFormulaInstitutionFaculty> wrapper9 = new LambdaQueryWrapper();
        wrapper9.eq(ContractFormulaInstitutionFaculty::getFkContractFormulaId, contractFormulaId);
        List<ContractFormulaInstitutionFaculty> contractFormulaInstitutionFaculties = contractFormulaInstitutionFacultyMapper.selectList(wrapper9);
        //有课程等级限制
        if (GeneralTool.isNotEmpty(contractFormulaInstitutionFaculties)) {
            LambdaQueryWrapper<InstitutionCourseFaculty> wrapper10 = new LambdaQueryWrapper();
            wrapper10.eq(InstitutionCourseFaculty::getFkInstitutionCourseId, fkInstitutionCourseId);
            List<InstitutionCourseFaculty> institutionCourseFaculties = institutionCourseFacultyMapper.selectList(wrapper10);
            Set<Long> facultyIdS = institutionCourseFaculties.stream().map(InstitutionCourseFaculty::getFkInstitutionFacultyId).collect(Collectors.toSet());
            wrapper9.in(ContractFormulaInstitutionFaculty::getFkInstitutionFacultyId, facultyIdS);
            contractFormulaInstitutionFaculties = contractFormulaInstitutionFacultyMapper.selectList(wrapper9);
            if (GeneralTool.isNotEmpty(contractFormulaInstitutionFaculties)) {
                return "学生课程学院与合同公式限制不匹配";
            }
        }
        LambdaQueryWrapper<ContractFormulaInstitutionZone> wrapper11 = new LambdaQueryWrapper();
        wrapper11.eq(ContractFormulaInstitutionZone::getFkContractFormulaId, contractFormulaId);
        List<ContractFormulaInstitutionZone> contractFormulaInstitutionZones = contractFormulaInstitutionZoneMapper.selectList(wrapper11);
        //有课程校区限制
       /* if (GeneralTool.isNotEmpty(contractFormulaInstitutionZones)) {
            Example example1 = new Example(InstitutionCourseFaculty::new);
            example1.createCriteria().andEqualTo("fkInstitutionCourseId", studentOfferItem.getFkInstitutionCourseId());
            List<InstitutionCourseZone> institutionCourseZones = institutionCourseZoneMapper.selectByExample(example1);
            Set<Long> institutionCourseZoneIds = institutionCourseZones.stream().map(InstitutionCourseZone::getFkInstitutionZoneId).collect(Collectors.toSet());
            criteria.andIn("fkInstitutionZoneId", institutionCourseZoneIds);
            contractFormulaInstitutionZones = contractFormulaInstitutionZoneMapper.selectByExample(example);
            if (GeneralTool.isNotEmpty(contractFormulaInstitutionZones)) {
                return "学生课程校区与合同公式限制不匹配";
            }
        }

        ContractFormula contractFormula = contractFormulaMapper.selectById(contractFormulaId);
        if (!contractFormula.getIsActive()) {
            return "合同公式为未生效状态";
        }
        if (GeneralTool.isNotEmpty(contractFormula.getStartTime())) {
            if (DateUtil.before(new Date(), contractFormula.getStartTime())) {
                return "合同公式未到开始时间";
            }
        }
        if (GeneralTool.isNotEmpty(contractFormula.getStartTime())) {
            if (DateUtil.before(contractFormula.getEndTime(), new Date())) {
                return "合同公式已过结束时间";
            }
        }
        if (!contractFormula.getFkCurrencyTypeNum().equals(studentOfferItem.getFkCurrencyTypeNum())) {
            return "合同公式币种不一致";
        }*/
        return null;

    }

    /**
     * feign调用 根据合同公式Id获取合同公式信息
     *
     * @Date 11:26 2021/7/19
     * <AUTHOR>
     */
   /* @Override
    public com.get.common.entity.fegin.ContractFormula getContractFormulaByFormulaId(Long formulaId)  {
        ContractFormula contractFormula = contractFormulaMapper.selectById(formulaId);
        return BeanCopyUtils.objClone(contractFormula, com.get.common.entity.fegin.ContractFormula::new);
    }*/
    @Override
    public List<CompanyTreeVo> getContractFormulaCompany(Long contractFormulaId) {
        if (GeneralTool.isEmpty(contractFormulaId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return contractFormulaCompanyService.getContractFormulaCompany(contractFormulaId);
    }

    /**
     * @return void
     * @Description :中间表新增
     * @Param [contractFormulaDto, contractFormulaId]
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertTable(ContractFormulaDto contractFormulaDto, Long contractFormulaId) {
        //国家
        if (GeneralTool.isNotEmpty(contractFormulaDto.getCountryIdList())) {
            for (Long areaCountryId : contractFormulaDto.getCountryIdList()) {
                ContractFormulaAreaCountryDto contractFormulaAreaCountryDto = new ContractFormulaAreaCountryDto();
                contractFormulaAreaCountryDto.setFkContractFormulaId(contractFormulaId);
                contractFormulaAreaCountryDto.setFkAreaCountryId(areaCountryId);
                contractFormulaAreaCountryService.addContractFormulaAreaCountry(contractFormulaAreaCountryDto);
            }
        }
        //课程类型
        if (GeneralTool.isNotEmpty(contractFormulaDto.getCourseTypeIdList())) {
            for (Long courseTypeId : contractFormulaDto.getCourseTypeIdList()) {
                ContractFormulaCourseTypeDto contractFormulaCourseTypeDto = new ContractFormulaCourseTypeDto();
                contractFormulaCourseTypeDto.setFkContractFormulaId(contractFormulaId);
                contractFormulaCourseTypeDto.setFkCourseTypeId(courseTypeId);
                contractFormulaCourseTypeService.addContractFormulaCourseType(contractFormulaCourseTypeDto);
            }
        }
        //课程等级
        if (GeneralTool.isNotEmpty(contractFormulaDto.getMajorLevelIdList())) {
            for (Long majorLevelId : contractFormulaDto.getMajorLevelIdList()) {
                ContractFormulaMajorLevelDto contractFormulaMajorLevelDto = new ContractFormulaMajorLevelDto();
                contractFormulaMajorLevelDto.setFkContractFormulaId(contractFormulaId);
                contractFormulaMajorLevelDto.setFkMajorLevelId(majorLevelId);
                contractFormulaMajorLevelService.addContractFormulaMajorLevel(contractFormulaMajorLevelDto);
            }
        }
        //学生来源
        if (GeneralTool.isNotEmpty(contractFormulaDto.getStudentCountryIdList())) {
            for (Long studentCountryId : contractFormulaDto.getStudentCountryIdList()) {
                ContractFormulaAreaCountryStudentDto contractFormulaAreaCountryStudentDto = new ContractFormulaAreaCountryStudentDto();
                contractFormulaAreaCountryStudentDto.setFkContractFormulaId(contractFormulaId);
                contractFormulaAreaCountryStudentDto.setFkAreaCountryId(studentCountryId);
                contractFormulaAreaCountryStudentService.addContractFormulaAreaCountryStudent(contractFormulaAreaCountryStudentDto);
            }
        }
        //学校
        if (GeneralTool.isNotEmpty(contractFormulaDto.getInstitutionIdList())) {
            for (Long institutionId : contractFormulaDto.getInstitutionIdList()) {
                ContractFormulaInstitutionDto contractFormulaInstitutionDto = new ContractFormulaInstitutionDto();
                contractFormulaInstitutionDto.setFkContractFormulaId(contractFormulaId);
                contractFormulaInstitutionDto.setFkInstitutionId(institutionId);
                contractFormulaInstitutionService.addContractFormulaInstitution(contractFormulaInstitutionDto);
            }
        }
        //课程
        if (GeneralTool.isNotEmpty(contractFormulaDto.getCourseIdList())) {
            for (Long course : contractFormulaDto.getCourseIdList()) {
                ContractFormulaInstitutionCourseDto contractFormulaInstitutionCourseDto = new ContractFormulaInstitutionCourseDto();
                contractFormulaInstitutionCourseDto.setFkContractFormulaId(contractFormulaId);
                contractFormulaInstitutionCourseDto.setFkInstitutionCourseId(course);
                contractFormulaInstitutionCourseService.addContractFormulaInstitutionCourse(contractFormulaInstitutionCourseDto);
            }
        }
        //合同公式佣金配置
        if (GeneralTool.isNotEmpty(contractFormulaDto.getContractFormulaCommissionVos())) {
            for (ContractFormulaCommissionDto contractFormulaCommissionDto : contractFormulaDto.getContractFormulaCommissionVos()) {
                contractFormulaCommissionDto.setFkContractFormulaId(contractFormulaId);
                contractFormulaCommissionService.addContractFormulaCommission(contractFormulaCommissionDto);
            }
        }
        //前置学校集团
        if (GeneralTool.isNotEmpty(contractFormulaDto.getPreGroupIdList())) {
            for (Long groupId : contractFormulaDto.getPreGroupIdList()) {
                ContractFormulaInstitutionGroupDto contractFormulaInstitutionGroupDto = new ContractFormulaInstitutionGroupDto();
                contractFormulaInstitutionGroupDto.setFkContractFormulaId(contractFormulaId);
                contractFormulaInstitutionGroupDto.setFkInstitutionGroupId(groupId);
                contractFormulaPreInstitutionGroupService.addContractFormulaInstitutionGroup(contractFormulaInstitutionGroupDto);
            }
        }
        //前置学校
        if (GeneralTool.isNotEmpty(contractFormulaDto.getPreInstitutionIdList())) {
            for (Long institutionId : contractFormulaDto.getPreInstitutionIdList()) {
                ContractFormulaPreInstitutionDto contractFormulaPreInstitutionDto = new ContractFormulaPreInstitutionDto();
                contractFormulaPreInstitutionDto.setFkContractFormulaId(contractFormulaId);
                contractFormulaPreInstitutionDto.setFkInstitutionId(institutionId);
                contractFormulaPreInstitutionService.addContractFormulaPreInstitution(contractFormulaPreInstitutionDto);
            }
        }
        //前置课程等级
        if (GeneralTool.isNotEmpty(contractFormulaDto.getPreMajorLevelList())) {
            for (Long majorLevel : contractFormulaDto.getPreMajorLevelList()) {
                ContractFormulaPreMajorLevelDto contractFormulaPreMajorLevelDto = new ContractFormulaPreMajorLevelDto();
                contractFormulaPreMajorLevelDto.setFkContractFormulaId(contractFormulaId);
                contractFormulaPreMajorLevelDto.setFkMajorLevelId(majorLevel);
                contractFormulaPreMajorLevelService.addContractFormulaPreMajorLevel(contractFormulaPreMajorLevelDto);
            }
        }
        //校区
        if (GeneralTool.isNotEmpty(contractFormulaDto.getZoneIdList())) {
            for (Long zoneId : contractFormulaDto.getZoneIdList()) {
                ContractFormulaInstitutionZoneDto contractFormulaInstitutionZoneDto = new ContractFormulaInstitutionZoneDto();
                contractFormulaInstitutionZoneDto.setFkContractFormulaId(contractFormulaId);
                contractFormulaInstitutionZoneDto.setFkInstitutionZoneId(zoneId);
                contractFormulaInstitutionZoneService.addContractFormulaInstitutionZone(contractFormulaInstitutionZoneDto);
            }
        }
        //学院
        if (GeneralTool.isNotEmpty(contractFormulaDto.getFacultyIdList())) {
            for (Long facultyId : contractFormulaDto.getFacultyIdList()) {
                ContractFormulaInstitutionFacultyDto contractFormulaInstitutionFacultyDto = new ContractFormulaInstitutionFacultyDto();
                contractFormulaInstitutionFacultyDto.setFkContractFormulaId(contractFormulaId);
                contractFormulaInstitutionFacultyDto.setFkInstitutionFacultyId(facultyId);
                contractFormulaInstitutionFacultyService.addContractFormulaInstitutionFaculty(contractFormulaInstitutionFacultyDto);
            }
        }
    }

    /**
     * @return void
     * @Description :中间表删除
     * @Param [id]
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteTable(Long id) {
        contractFormulaAreaCountryService.deleteByFkid(id);
        contractFormulaCourseTypeService.deleteByFkid(id);
        contractFormulaMajorLevelService.deleteByFkid(id);
        contractFormulaAreaCountryStudentService.deleteByFkid(id);
        contractFormulaInstitutionService.deleteByFkid(id);
        contractFormulaInstitutionCourseService.deleteByFkid(id);
        contractFormulaCommissionService.deleteByFkid(id);
        contractFormulaPreInstitutionGroupService.deleteByFkid(id);
        contractFormulaPreMajorLevelService.deleteByFkid(id);
        contractFormulaPreInstitutionService.deleteByFkid(id);
        contractFormulaInstitutionZoneService.deleteByFkid(id);
        contractFormulaInstitutionFacultyService.deleteByFkid(id);
        contractFormulaCompanyService.deleteByFkid(id);
        contractFormulaInstitutionChannelService.deleteByContractFormulaId(id);
        contractFormulaAreaCountryStudentExcludeService.deleteByFkid(id);
    }


    /**
     * feign调用 根据合同公式Id获取合同公式信息
     *
     * @Date 11:26 2021/7/19
     * <AUTHOR>
     */
    @Override
    public ContractFormula getContractFormulaByFormulaId(Long formulaId) {
        ContractFormula contractFormula = contractFormulaMapper.selectById(formulaId);
        return BeanCopyUtils.objClone(contractFormula, ContractFormula::new);
    }

}
