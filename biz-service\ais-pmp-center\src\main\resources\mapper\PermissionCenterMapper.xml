<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.pmpcenter.mapper.PermissionCenterMapper">

    <select id="getCompanyList" resultType="com.get.pmpcenter.vo.common.CompanyVo">
        select id as companyId, name, name_chn,num
        from m_company
        where id in
        <foreach collection="companyIds" item="companyId" open="(" separator="," close=")">
            #{companyId}
        </foreach>
        order by view_order desc
    </select>


    <select id="getStaffList" resultType="com.get.pmpcenter.vo.common.StaffVo">
        select id as staffId, name, name_en,email
        from m_staff
        <where>
            is_active = 1
            <if test="companyId != null">
                and fk_company_id =
                #{companyId}
            </if>
            <if test="staffIds != null and staffIds.size() > 0">
                and id in
                <foreach collection="staffIds" item="staffId" open="(" separator="," close=")">
                    #{staffId}
                </foreach>
            </if>
        </where>
        ORDER BY CONVERT(name USING gbk) ASC
    </select>

    <select id="getStaffListByLoginIds" resultType="com.get.pmpcenter.vo.common.StaffVo">
        select id as staffId, name,login_id
        from m_staff
        <where>
            is_active = 1
            <if test="loginIds != null and loginIds.size() > 0">
                and login_id in
                <foreach collection="loginIds" item="loginId" open="(" separator="," close=")">
                    #{loginId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getStaffCountryIds" resultType="java.lang.Long">
        select c.id
        from ais_institution_center.u_area_country c
        where c.num in
              (select sac.fk_area_country_key
               from ais_permission_center.r_staff_area_country sac
               where sac.fk_staff_id = #{staffId})
    </select>

    <select id="getStaffByLoginIds" resultType="com.get.pmpcenter.vo.common.StaffVo">
        select id as staffId,
        name,
        name_email,
        login_id
        from m_staff
        where login_id in
        <foreach collection="loginIds" item="loginId" open="(" separator="," close=")">
            #{loginId}
        </foreach>
    </select>


</mapper>