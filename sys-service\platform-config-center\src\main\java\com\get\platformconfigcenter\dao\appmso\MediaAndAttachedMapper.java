package com.get.platformconfigcenter.dao.appmso;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.ConfigMediaAndAttached;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
@DS("appmsodb")
public interface MediaAndAttachedMapper extends BaseMapper<ConfigMediaAndAttached> {

//    int insert(ConfigMediaAndAttached record);
//
//    int insertSelective(ConfigMediaAndAttached record);
//
//    int updateByPrimaryKeySelective(ConfigMediaAndAttached record);
//
//    int updateByPrimaryKey(ConfigMediaAndAttached record);
//
//    Integer getNextIndexKey(@Param("tableId") Long tableId, @Param("tableName") String tableName);

}