package com.get.workflowcenter.listener;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.SpringUtil;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.workflowcenter.component.IWorkFlowHelper;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.StringJoiner;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.TaskService;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.activiti.engine.repository.ProcessDefinition;
import org.apache.commons.lang.StringUtils;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/4/21 23:16
 */
//按职位查询
public class GetInstitutionByPositionNum implements TaskListener {
    @Override
    public void notify(DelegateTask delegateTask) {
        //111111
        TaskService taskService = SpringUtil.getBean(TaskService.class);
        IWorkFlowHelper workFlowHelper = SpringUtil.getBean(IWorkFlowHelper.class);
        //获取流程名称
        ProcessEngine processEngine = SpringUtil.getBean(ProcessEngine.class);
        ProcessDefinition processDefinition = processEngine.getRepositoryService()
                .createProcessDefinitionQuery()
                .processDefinitionId(delegateTask.getProcessDefinitionId())
                .singleResult();

        StaffVo staffVo = workFlowHelper.getStaffDto(delegateTask);
        IPermissionCenterClient permissionCenterClient = SpringUtil.getBean(IPermissionCenterClient.class);
        IInstitutionCenterClient institutionCenterClient = SpringUtil.getBean(IInstitutionCenterClient.class);
        String description = delegateTask.getDescription();
        String tablename = String.valueOf(delegateTask.getVariable("tableName"));
        String businessKey = String.valueOf(delegateTask.getVariable("businessKey"));
        if (StringUtils.isNotBlank(description)) {

            String regEx = "[`~!@#$%^&*()+=|{}':;'\\[\\].<>/?~！@#￥%……&*（）——+|{}【】'；：”“’。、？·]";
            Pattern pattern = Pattern.compile(regEx);
            Matcher matcher = pattern.matcher(description);
            String newString = matcher.replaceAll("").trim();

            String[] split = newString.split(",");
            ArrayList<String> objects = new ArrayList<>();
            for (String splits : split) {
                objects.add(splits);
            }
            Result<List<Long>> result = permissionCenterClient.getPositionByNum(objects);
            if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
                JSONArray jsonArray = JSONUtil.parseArray(result.getData());
                List<String> evpList = JSONUtil.toList(jsonArray, String.class);
                if (evpList.size() != 0) {
                    if (evpList.size() > 1) {
                        delegateTask.addCandidateUsers(evpList);
                        StringJoiner stringJoiner = new StringJoiner(",");
                        stringJoiner.add(ProjectKeyEnum.WORKFLOW_CENTER.key).add(ProjectKeyEnum.WORKFLOW_CENTER_TO_SIGN.key);
                        workFlowHelper.sendMessage(staffVo, evpList, processDefinition, "待签取", delegateTask, stringJoiner.toString());
                    } else {
                        taskService.setAssignee(delegateTask.getId(), evpList.get(0));
                        StringJoiner stringJoiner = new StringJoiner(",");
                        stringJoiner.add(ProjectKeyEnum.WORKFLOW_CENTER.key).add(ProjectKeyEnum.WORKFLOW_CENTER_TO_DO.key);
                        workFlowHelper.sendMessage(staffVo, evpList, processDefinition, "待审核", delegateTask, stringJoiner.toString());
                    }
                } else {
                    HashMap<String, Object> map = new HashMap<>();
                    //职位没人，默认拒绝
                    map.put("sequenceFlowsStatus", 0);
                    taskService.setVariableLocal(delegateTask.getId(), "approvalAction", 0);
                    taskService.addComment(delegateTask.getId(), delegateTask.getProcessInstanceId(),
                            "系统自动驳回，" + delegateTask.getName() + "：找不到处理人。");
                    institutionCenterClient.changeStatus(3, tablename, Long.valueOf(businessKey));
                    taskService.complete(delegateTask.getId(), map);
                }
            }


        } else {
            HashMap<String, Object> map = new HashMap<>();
            //职位没人，默认拒绝
            map.put("sequenceFlowsStatus", 0);
            taskService.setVariableLocal(delegateTask.getId(), "approvalAction", 0);
            taskService.addComment(delegateTask.getId(), delegateTask.getProcessInstanceId(),
                    "系统自动驳回，" + delegateTask.getName() + "：找不到处理人。");
            institutionCenterClient.changeStatus(3, tablename, Long.valueOf(businessKey));
            taskService.complete(delegateTask.getId(), map);
        }

    }
}

