package com.get.aisplatformcenterap.vo.work;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.get.aisplatformcenterap.entity.MFeedbackOrderReplyEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MFeedbackOrderReplyVo extends MFeedbackOrderReplyEntity {

    @ApiModelProperty("1:华通用户 2:AIS用户")
    private String userType;

    @ApiModelProperty("是否本人1是 2否")
    private String isLoginUser;

    public String getUserType() {
        if(ObjectUtils.isNotEmpty(getFkStaffId()) ){
            userType="2";
        }else {
            userType="1";
        }
        return userType;
    }

    public void setIsLoginUser(String isLoginUser) {
        this.isLoginUser = isLoginUser;
    }



}
