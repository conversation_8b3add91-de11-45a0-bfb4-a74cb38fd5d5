package com.get.salecenter.service.impl;

import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.StudentAppCountryMapper;
import com.get.salecenter.entity.StudentAppCountry;
import com.get.salecenter.service.StudentAppCountryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by <PERSON>.
 * Time: 18:26
 * Date: 2021/9/8
 * Description:学生申请国家实现类
 */
@Service
public class StudentAppCountryServiceImpl implements StudentAppCountryService {

    @Resource
    private StudentAppCountryMapper studentAppCountryMapper;

    /**
     * @Description: feign调用 根据学生申请国家ids获取名称
     * @Author: Jerry
     * @Date:18:27 2021/9/8
     */
    @Override
    public Map<Long, String> getNamesByStudentAppCountryIds(Set<Long> studentAppCountryIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(studentAppCountryIds)) {
            return map;
        }
//        Example example = new Example(StudentAppCountry.class);
//        example.createCriteria().andIn("id",studentAppCountryIds);
        List<StudentAppCountry> studentAppCountries = studentAppCountryMapper.selectBatchIds(studentAppCountryIds);
        if (GeneralTool.isEmpty(studentAppCountries)) {
            return map;
        }
        for (StudentAppCountry studentAppCountry : studentAppCountries) {
            map.put(studentAppCountry.getId(), studentAppCountry.getName());
        }
        return map;
    }
}
