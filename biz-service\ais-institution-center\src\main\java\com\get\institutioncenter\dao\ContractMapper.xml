<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.ContractMapper">


    <sql id="Base_Column_List">
   c.id,c.fk_institution_provider_id,c.fk_institution_id,c.fk_institution_course_id,c. fk_contract_type_id,c.contract_num,
   c.title,c.start_time,c.end_time,c.market_expense,c.follow_up_expense,c.commission_rate,c.follow_up_commission_rate,c.remark,c.fk_contract_id_revoke,
   c.is_active,c.gmt_create,c.gmt_create_user,c.gmt_modified,c.gmt_modified_user,c.status,c.contract_approval_mode,
   p.name fk_institution_provider_name,s.name fk_institution_name,o.name fk_institution_course_name,y.type_name fk_contract_type_name
  </sql>
<!--    <select id="selectById" parameterType="java.lang.Long" resultType="com.get.institutioncenter.voContractVo>-->
<!--        select-->
<!--        <include refid="Base_Column_List"/>-->
<!--        from m_contract c-->
<!--        left join m_institution_provider p on c.fk_institution_provider_id=p.id-->
<!--        left join m_institution s on c.fk_institution_id=s.id-->
<!--        left join m_institution_course o on c.fk_institution_course_id=o.id-->
<!--        left join u_contract_type y on c.fk_contract_type_id=y.id-->
<!--        where c.id = #{id,jdbcType=BIGINT}-->
<!--    </select>-->

    <select id="selectByProviderId" parameterType="java.lang.Long"
            resultType="com.get.institutioncenter.vo.ContractVo">
        select
        <include refid="Base_Column_List"/>
        from m_contract c
        left join m_institution_provider p on c.fk_institution_provider_id=p.id
        left join m_institution s on c.fk_institution_id=s.id
        left join m_institution_course o on c.fk_institution_course_id=o.id
        left join u_contract_type y on c.fk_contract_type_id=y.id
        where c.fk_institution_provider_id = #{providerId,jdbcType=BIGINT}
    </select>

    <select id="selectByContractVo" resultType="com.get.institutioncenter.vo.ContractVo">
        SELECT p.name fkInstitutionProviderName,i.name fkInstitutionName,s.name fkInstitutionCourseName,t.type_name
        fkContractTypeName,c.*
        from m_contract c
        left join m_institution_provider p on c.fk_institution_provider_id= p.id
        left join m_institution i on c.fk_institution_id=i.id
        left join m_institution_course s on c.fk_institution_course_id=s.id
        left join u_contract_type t on c.fk_contract_type_id=t.id

        where EXISTS (
                select 1 from r_contract_company AS rcc
                WHERE rcc.fk_contract_id = c.id
                and rcc.fk_company_id IN
                <foreach collection="companyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
                    #{fkCompanyId}
                </foreach>
            )
            <if test="contractDto.applicationCycle!=null and contractDto.applicationCycle!=''">
                AND c.application_cycle LIKE CONCAT('%',#{contractDto.applicationCycle},'%')
            </if>
            <if test="contractDto.settlementCycle!=null and contractDto.settlementCycle!=''">
                AND c.settlement_cycle LIKE CONCAT('%',#{contractDto.settlementCycle},'%')
            </if>
            <if test="contractDto.fkContractTypeId != null">
                and c.fk_contract_type_id=#{contractDto.fkContractTypeId}
            </if>
            <if test="contractDto.fkInstitutionProviderId != null">
                and c.fk_institution_provider_id=#{contractDto.fkInstitutionProviderId}
            </if>
            <if test="contractDto.fkInstitutionId != null">
                and c.fk_institution_id=#{contractDto.fkInstitutionId}
            </if>
            <if test="contractDto.fkInstitutionCourseId != null">
                and c.fk_institution_course_id=#{contractDto.fkInstitutionCourseId}
            </if>
            <if test="contractDto.isActive != null">
                and c.is_active=#{contractDto.isActive}
            </if>
            <if test="contractDto.contractNum != null and contractDto.contractNum!=''">
                and position(#{contractDto.contractNum,jdbcType=VARCHAR} in c.contract_num)
            </if>

            <if test="status == null ">
                <if test="contractDto.fkCompanyId != null">
                    and c.id in
                    <foreach collection="contractIds" item="contractId" index="index" open="(" separator="," close=")">
                        #{contractId}
                    </foreach>
                </if>
            </if>

            <if test="contractDto.fkCountryId != null">
                and c.fk_institution_provider_id in
                <foreach collection="providerIds" item="providerId" index="index" open="(" separator="," close=")">
                    #{providerId}
                </foreach>
            </if>
            <if test="contractDto.keyWord != null and contractDto.keyWord != ''">
                and position(#{contractDto.keyWord,jdbcType=VARCHAR} in c.title)
            </if>
            <if test="ids !=null ">
                and c.id in
                <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="myApplication !=null">
                and c.gmt_create_user=#{contractDto.gmtCreateUser}
            </if>

        order by c.is_active desc,c.id desc
    </select>


    <update id="changeStatus">
  update ${tableName} set status = #{status} where id = #{businessKey}

</update>
    <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.Contract" keyProperty="id"
            useGeneratedKeys="true">
        insert into m_contract
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkInstitutionProviderId != null">
                fk_institution_provider_id,
            </if>
            <if test="fkInstitutionId != null">
                fk_institution_id,
            </if>
            <if test="fkInstitutionCourseId != null">
                fk_institution_course_id,
            </if>
            <if test="fkContractTypeId != null">
                fk_contract_type_id,
            </if>
            <if test="contractNum != null">
                contract_num,
            </if>
            <if test="title != null">
                title,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="marketExpense != null">
                market_expense,
            </if>
            <if test="followUpExpense != null">
                follow_up_expense,
            </if>
            <if test="commissionRate != null">
                commission_rate,
            </if>
            <if test="followUpCommissionRate != null">
                follow_up_commission_rate,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="isActive != null">
                is_active,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
            <if test="fkContractIdRevoke != null">
                fk_contract_id_revoke,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="contractApprovalMode !=null">
                contract_approval_mode,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkInstitutionProviderId != null">
                #{fkInstitutionProviderId,jdbcType=BIGINT},
            </if>
            <if test="fkInstitutionId != null">
                #{fkInstitutionId,jdbcType=BIGINT},
            </if>
            <if test="fkInstitutionCourseId != null">
                #{fkInstitutionCourseId,jdbcType=BIGINT},
            </if>
            <if test="fkContractTypeId != null">
                #{fkContractTypeId,jdbcType=BIGINT},
            </if>
            <if test="contractNum != null">
                #{contractNum,jdbcType=VARCHAR},
            </if>
            <if test="title != null">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="marketExpense != null">
                #{marketExpense,jdbcType=VARCHAR},
            </if>
            <if test="followUpExpense != null">
                #{followUpExpense,jdbcType=VARCHAR},
            </if>
            <if test="commissionRate != null">
                #{commissionRate,jdbcType=VARCHAR},
            </if>
            <if test="followUpCommissionRate != null">
                #{followUpCommissionRate,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="isActive != null">
                #{isActive,jdbcType=BIT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
            <if test="fkContractIdRevoke != null">
                #{fkContractIdRevoke,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="contractApprovalMode !=null ">
                #{contractApprovalMode,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <select id="checkProviderInfoIsEmptyByProviderId" resultType="java.lang.Boolean">
    select IFNULL(max(id),0) id from m_contract where fk_institution_provider_id = #{providerId}  LIMIT 1
  </select>
    <select id="isExistByCourseId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id from m_contract where fk_institution_course_id=#{courseId}
    </select>
    <select id="getExistParentId" resultType="java.lang.Boolean">
 SELECT IFNULL(max(id),0) id from m_contract where fk_contract_id_revoke=#{id}
    </select>
    <select id="isExistByInstitutionId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id from m_contract where fk_institution_id=#{id}
    </select>
</mapper>