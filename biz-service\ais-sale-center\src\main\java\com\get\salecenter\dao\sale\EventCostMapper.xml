<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.EventCostMapper">
  <select id="getReceiptFormBalance" resultType="java.math.BigDecimal">
    SELECT
    mrf.amount - IFNULL(SUM( mec.amount ), 0)
    FROM
    ais_finance_center.m_receipt_form AS mrf
    LEFT JOIN ais_sale_center.m_event_cost AS mec ON mrf.id = mec.fk_receipt_form_id
    WHERE mrf.id = #{fkReceiptFormId}
    <if test="eventCostId != null">
      AND mec.id != #{eventCostId}
    </if>
    GROUP BY mrf.id
  </select>
  <select id="getReceiptFormBalances" resultType="com.get.salecenter.vo.ReceiptFormVo">
    SELECT
    mrf.id,
    CASE WHEN IFNULL(MAX(mec.amount),0)>0 THEN
    mrf.amount - SUM( mec.amount )
    ELSE
    0
    END receiptFormBalance
    FROM
    ais_finance_center.m_receipt_form AS mrf
    LEFT JOIN ais_sale_center.m_event_cost AS mec ON mrf.id = mec.fk_receipt_form_id
    WHERE 1=1
    <if test="fkReceiptFormIds != null and fkReceiptFormIds.size()>0">
      and mrf.id in
      <foreach collection="fkReceiptFormIds" item="fkReceiptFormId" index="index" open="(" separator="," close=")">
        #{fkReceiptFormId}
      </foreach>
    </if>
    <if test="eventCostId != null">
      AND mec.id != #{eventCostId}
    </if>
    GROUP BY mrf.id
  </select>
    <select id="getReceiptFormInfo" resultType="com.get.salecenter.vo.EventCostVo">
      SELECT
        a.id,
        CONCAT("发票总金额：",l.invoice_amount,k.type_name,"（",k.num,"）","，应收未收：",(IFNULL(i.receivable_amount,0) -(IFNULL(j.sum_amount_receivable,0) - IFNULL(j.sum_amount_exchange_rate,0)) ),k.type_name,"（",k.num,"）") receiptFormInfo
      FROM
        m_event_cost a
          LEFT JOIN m_event_bill b on a.fk_event_bill_id = b.id
          LEFT JOIN m_receivable_plan i on i.id = b.fk_receivable_plan_id
          LEFT JOIN (
          -- 计算每条应收计划里累计的实收金额
          SELECT a.fk_receivable_plan_id,
                 SUM(IFNULL(a.amount_receivable,0)) sum_amount_receivable,
                 SUM(IFNULL(a.amount_exchange_rate,0)) sum_amount_exchange_rate,
                 SUM(IFNULL(a.amount_hkd,0)) sum_amount_hkd,
                 SUM(IFNULL(a.amount_rmb,0)) sum_amount_rmb
          FROM ais_finance_center.m_receipt_form_item a
                 LEFT JOIN ais_finance_center.m_receipt_form b ON a.fk_receipt_form_id=b.id
               -- 关闭的收款单不作计算
          WHERE b.`status`!=0
          GROUP BY a.fk_receivable_plan_id
        ) j ON j.fk_receivable_plan_id=b.fk_receivable_plan_id
          LEFT JOIN ais_finance_center.u_currency_type k on k.num = i.fk_currency_type_num
          LEFT JOIN ais_finance_center.m_invoice l on l.num = b.fk_invoice_num
          LEFT JOIN ais_finance_center.u_currency_type m on m.num = l.fk_currency_type_num

      where 1=1
        <if test="eventCostIds !=null and eventCostIds.size() > 0">
            and a.id in
          <foreach collection="eventCostIds" item="eventCostId" index="index" open="(" separator="," close=")">
            #{eventCostId}
          </foreach>
        </if>
      GROUP BY a.id
    </select>

</mapper>