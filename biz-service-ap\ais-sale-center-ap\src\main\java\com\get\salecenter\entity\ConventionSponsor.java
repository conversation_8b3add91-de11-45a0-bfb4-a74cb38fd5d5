package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("m_convention_sponsor")
public class ConventionSponsor extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    @Column(name = "fk_convention_id")
    private Long fkConventionId;
    /**
     * 学校提供商Id（费用归口）
     */
    @ApiModelProperty(value = "学校提供商Id（费用归口）")
    @Column(name = "fk_institution_provider_id")
    private Long fkInstitutionProviderId;

    /**
     * 活动费用绑定Id
     */
    @UpdateWithNull
    @ApiModelProperty(value = "活动费用绑定Id")
    @Column(name = "fk_event_cost_id")
    private Long fkEventCostId;

    /**
     * 赞助商名称
     */
    @ApiModelProperty(value = "赞助商名称")
    @Column(name = "sponsor_name")
    private String sponsorName;
    /**
     * 回执码，8位数字随机数(和报名一致)
     */
    @ApiModelProperty(value = "回执码，8位数字随机数(和报名一致)")
    @Column(name = "receipt_code")
    private String receiptCode;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;

    /**
     * 费用金额
     */
    @ApiModelProperty(value = "费用金额")
    @Column(name = "fee_other")
    private BigDecimal feeOther;

    /**
     * 费用金额（折合人民币）
     */
    @ApiModelProperty(value = "费用金额（折合人民币）")
    @Column(name = "fee_other_cny")
    private BigDecimal feeOtherCny;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    /**
     * 状态：0新建/1已确认/2已收款
     */
    @ApiModelProperty(value = "状态：0新建/1已确认/2已收款")
    @Column(name = "status")
    private Integer status;
}