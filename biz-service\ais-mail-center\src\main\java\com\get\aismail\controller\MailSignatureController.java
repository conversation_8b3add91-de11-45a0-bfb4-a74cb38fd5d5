package com.get.aismail.controller;

import com.get.aismail.entity.MFileMail;
import com.get.aismail.entity.MMailFolder;
import com.get.aismail.entity.MMailSignature;
import com.get.aismail.service.impl.MailSignatureServiceImpl;
import com.get.aismail.vo.AddNewSignature;
import com.get.common.result.ResponseBo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "邮件签名相关操作")
@RestController
@RequestMapping("/mail")
public class MailSignatureController {
    @Resource
    private MailSignatureServiceImpl mailSignatureServiceImpl;

    @ApiOperation(value = "添加签名")
    @PostMapping("/addNewSignature")
    public ResponseBo addNewSignature(@RequestBody AddNewSignature addNewSignature) throws Exception {
        mailSignatureServiceImpl.addNewSignature(addNewSignature);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "上传附件用于签名")
    @PostMapping("/uploadFileForSignature")
    public ResponseBo<MFileMail> uploadFileForSignature(@RequestParam("file") MultipartFile file) throws Exception {
        return new ResponseBo<>(mailSignatureServiceImpl.uploadFileForSignature(file));
    }

    @ApiOperation(value = "删除签名")
    @PostMapping("/deleteSignature")
    public ResponseBo deleteSignature(@RequestBody MMailSignature mSignature) throws Exception {
        mailSignatureServiceImpl.deleteSignature(mSignature);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "修改签名")
    @PostMapping("/updateSignature")
    public ResponseBo updateSignature(@RequestBody List<MMailSignature> mSignatures) throws Exception {
        mailSignatureServiceImpl.updateSignature(mSignatures);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "获取所有签名")
    @PostMapping("/selectAllSignature")
    public ResponseBo<List<MMailSignature>> selectAllSignature() throws Exception {
        return new ResponseBo<>(mailSignatureServiceImpl.selectAllSignatures());
    }
}
