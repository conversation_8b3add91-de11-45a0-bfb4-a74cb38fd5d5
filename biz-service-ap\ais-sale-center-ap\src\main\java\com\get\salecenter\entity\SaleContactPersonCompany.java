package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_contact_person_company")
public class SaleContactPersonCompany extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 联系人Id
     */
    @ApiModelProperty(value = "联系人Id")
    @Column(name = "fk_contact_person_id")
    private Long fkContactPersonId;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;
}