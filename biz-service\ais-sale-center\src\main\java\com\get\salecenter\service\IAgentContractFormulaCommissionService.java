package com.get.salecenter.service;


import com.get.salecenter.vo.AgentContractFormulaCommissionVo;
import com.get.salecenter.dto.AgentContractFormulaCommissionDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/23 18:35
 * @verison: 1.0
 * @description:
 */
public interface IAgentContractFormulaCommissionService {
    /**
     * @return java.lang.Long
     * @Description :新增
     * @Param [contractFormulaCommissionVo]
     * <AUTHOR>
     */
    Long addContractFormulaCommission(AgentContractFormulaCommissionDto contractFormulaCommissionVo);

    /**
     * @return void
     * @Description :根据contractFormulaId删除
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    void deleteByFkid(Long contractFormulaId);

    /**
     * @return java.util.List<com.get.salecenter.vo.AgentContractFormulaCommissionVo>
     * @Description :获取佣金配置对象dtos
     * @Param [id]
     * <AUTHOR>
     */
    List<AgentContractFormulaCommissionVo> getAgentContractFormulaCommissionByFkid(Long id);
}
