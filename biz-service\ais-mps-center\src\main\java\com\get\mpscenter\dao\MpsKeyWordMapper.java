package com.get.mpscenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.mpscenter.vo.KeyWordVo;
import com.get.mpscenter.entity.MpsKeyWord;
import com.get.mpscenter.dto.KeyWordMajorLevelSelectDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MpsKeyWordMapper extends BaseMapper<MpsKeyWord>, GetMapper<MpsKeyWord> {

    Integer getMaxOrder();

    List<KeyWordVo> getKeyWords(IPage<KeyWordVo> pages, @Param("keyWordMajorLevelSelectDto") KeyWordMajorLevelSelectDto keyWordMajorLevelSelectDto);
}
