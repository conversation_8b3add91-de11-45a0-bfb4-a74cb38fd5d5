package com.get.core.log.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @author: Sea
 * @create: 2020/6/19 17:21
 * @verison: 1.0
 * @description:
 */
@Data
public class LogLoginVo extends BaseVoEntity {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;

    /**
     * 登陆用户Id
     */
    @ApiModelProperty(value = "登陆用户Id")
    private String staffLoginId;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String staffName;

    /**
     * IP地址
     */
    @ApiModelProperty(value = "IP地址")
    private String ip;

    /**
     * 登入时间
     */
    @ApiModelProperty(value = "登入时间")
    private Date loginTime;

    /**
     * 登出时间
     */
    @ApiModelProperty(value = "登出时间")
    private Date logoutTime;

    //自定义内容
    /**
     * 登录开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "登录开始时间")
    private Date loginStartTime;

    /**
     * 登录结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "登录结束时间")
    private Date loginEndTime;

    /**
     * 登出开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "登出开始时间")
    private Date loginOutStartTime;

    /**
     * 登出结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "登出结束时间")
    private Date loginOutEndTime;

    /**
     * 登录账号
     */
    @ApiModelProperty(value = "登录账号")
    private String gmtCreateUser;
}
