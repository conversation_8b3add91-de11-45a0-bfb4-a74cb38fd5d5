package com.get.salecenter.vo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2023/4/13
 * @TIME: 15:15
 * @Description:
 **/
@Data
public class CyclingRegistrationExportVo {
    /**
     * 峰会名称
     */
    @ApiModelProperty(value = "峰会名称")
    private String conventionName;

    /**
     * 骑行路线
     */
    @ApiModelProperty(value = "骑行路线名称")
    private String cyclingRouteName;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;
    /**
     * 英文名
     */
    @ApiModelProperty(value = "英文名")
    private String nameEn;
    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别")
    private String genderName;

    /**
     * 生日
     */
    @ApiModelProperty(value = "生日")
    private String birthday;

    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    private String mobile;
    /**
     * 身份类型：0身份证/1护照
     */
    @ApiModelProperty(value = "身份类型")
    private String idTypeName;
    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
    private String idNum;
    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String company;

    /**
     * 第一套骑行服尺码
     */
    @ApiModelProperty(value = "第一套骑行服尺码")
    private String suitSize1;

    /**
     * 第二套骑行服尺码
     */
    @ApiModelProperty(value = "第二套骑行服尺码")
    private String suitSize2;

    /**
     * 支付金额
     */
    @ApiModelProperty(value = "支付金额")
    private BigDecimal payAmount;
    /**
     * 支付流水号
     */
    @ApiModelProperty(value = "支付流水号")
    private String payOrderNum;
    /**
     * 支付状态：0失败/1成功
     */
    @ApiModelProperty(value = "支付状态")
    private String payStatusName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

   /**
    * 创建时间
    */
    @ApiModelProperty(value = "创建时间")
    private String gmtCreate;
}
