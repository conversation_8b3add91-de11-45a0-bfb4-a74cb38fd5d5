package com.get.financecenter.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.GetStringUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.api.ResultCode;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestHeaderHandler;
import com.get.file.utils.FileUtils;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.dto.SaleFileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.filecenter.vo.FileVo;
import com.get.financecenter.dao.CurrencyTypeMapper;
import com.get.financecenter.dao.PayablePlanSettlementAgentAccountMapper;
import com.get.financecenter.dao.PayablePlanSettlementBatchExchangeMapper;
import com.get.financecenter.dao.PayablePlanSettlementInstallmentMapper;
import com.get.financecenter.dao.PayablePlanSettlementStatusMapper;
import com.get.financecenter.dao.PayableSettlementMessageMapper;
import com.get.financecenter.dao.PaymentFormItemMapper;
import com.get.financecenter.dao.PaymentFormMapper;
import com.get.financecenter.dao.RPayablePlanSettlementFlagMapper;
import com.get.financecenter.dao.ReceiptFormMapper;
import com.get.financecenter.dto.AgentSettlementBatchExportDto;
import com.get.financecenter.dto.AgentSettlementDto;
import com.get.financecenter.dto.AgentStatementExcelExportDto;
import com.get.financecenter.dto.AutoGeneratePaymentDto;
import com.get.financecenter.dto.BatchDownloadAgentReconciliationDto;
import com.get.financecenter.dto.BindPaymentSerialNumberDto;
import com.get.financecenter.dto.CancelSettlementDto;
import com.get.financecenter.dto.MediaAndAttachedDto;
import com.get.financecenter.dto.query.AgentSettlementQueryDto;
import com.get.financecenter.entity.InvoiceReceivablePlan;
import com.get.financecenter.entity.PayablePlanSettlementAgentAccount;
import com.get.financecenter.entity.PayablePlanSettlementBatchExchange;
import com.get.financecenter.entity.PayablePlanSettlementInstallment;
import com.get.financecenter.entity.PayablePlanSettlementStatus;
import com.get.financecenter.entity.PaymentForm;
import com.get.financecenter.entity.PaymentFormItem;
import com.get.financecenter.entity.RPayablePlanSettlementFlag;
import com.get.financecenter.entity.SettlementCommissionNotice;
import com.get.financecenter.excelmodel.AgentSettlementAccommodationModel;
import com.get.financecenter.excelmodel.AgentSettlementInsuranceModel;
import com.get.financecenter.excelmodel.AgentSettlementIntermediaryModel;
import com.get.financecenter.excelmodel.AgentSettlementServiceFeeModel;
import com.get.financecenter.excelmodel.AgentSettlementTransferModel;
import com.get.financecenter.excelmodel.CurrencyAccountModel;
import com.get.financecenter.service.AgencyCommissionSettlementService;
import com.get.financecenter.service.AsyncExportService;
import com.get.financecenter.service.HtiAgencyCommissionSettlementService;
import com.get.financecenter.service.ICurrencyTypeService;
import com.get.financecenter.service.IExchangeRateService;
import com.get.financecenter.service.IInvoiceReceivablePlanService;
import com.get.financecenter.service.IInvoiceService;
import com.get.financecenter.service.IPaymentFormService;
import com.get.financecenter.service.IReceiptFormItemService;
import com.get.financecenter.utils.TemplateExcelUtils;
import com.get.financecenter.vo.AgentSettlementAccommodationVo;
import com.get.financecenter.vo.AgentSettlementInsuranceVo;
import com.get.financecenter.vo.AgentSettlementItemVo;
import com.get.financecenter.vo.AgentSettlementOfferItemVo;
import com.get.financecenter.vo.AgentSettlementServiceFeeVo;
import com.get.financecenter.vo.AgentSettlementVo;
import com.get.financecenter.vo.MSettlementBillItemVo;
import com.get.financecenter.vo.OfferSettlementVo;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.dto.CompanyConfigInfoDto;
import com.get.permissioncenter.entity.StaffDownload;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.salecenter.dto.CancelFinancialBatchSettlementDto;
import com.get.salecenter.dto.CancelFinancialConfirmationSettlementDto;
import com.get.salecenter.dto.CommissionSummaryBatchDetailDto;
import com.get.salecenter.dto.CommissionSummaryBatchDto;
import com.get.salecenter.dto.CommissionSummaryDto;
import com.get.salecenter.dto.DeleteFinancialSettlementSummaryDeleteDto;
import com.get.salecenter.dto.DeleteSettlementDeleteDto;
import com.get.salecenter.dto.FinancialSettlementAgentLockingDto;
import com.get.salecenter.dto.InstallmentAmountActualUpdateDto;
import com.get.salecenter.dto.PayablePlanDto;
import com.get.salecenter.dto.PayablePlanSettlementBatchExchangeDto;
import com.get.salecenter.dto.PendingSettlementMarkDto;
import com.get.salecenter.dto.PrepaymentButtonHtiDto;
import com.get.salecenter.dto.SettlementAgentAccountUpdateDto;
import com.get.salecenter.dto.SubmitFinancialSettlementSummaryDto;
import com.get.salecenter.dto.SubmitSettlementDto;
import com.get.salecenter.dto.SubmitSettlementItemDto;
import com.get.salecenter.dto.UpdatePayablePlanStatusSettlementDto;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.entity.PayablePlan;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.salecenter.vo.AgentContractAccountVo;
import com.get.salecenter.vo.AgentSettlementPageVo;
import com.get.salecenter.vo.CommissionSummaryBatchItemDetailVo;
import com.get.salecenter.vo.CommissionSummaryBatchItemVo;
import com.get.salecenter.vo.CommissionSummaryBatchPageVo;
import com.get.salecenter.vo.CommissionSummaryBatchPayablePlanVo;
import com.get.salecenter.vo.CommissionSummaryBatchVo;
import com.get.salecenter.vo.CommissionSummaryExportVo;
import com.get.salecenter.vo.CommissionSummaryPageVo;
import com.get.salecenter.vo.CommissionSummaryVo;
import com.get.salecenter.vo.IFileInfoVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.vo.PayablePlanSettlementAgentAccountVo;
import com.get.salecenter.vo.PayablePlanSettlementBatchExchangeVo;
import com.get.salecenter.vo.PayablePlanVo;
import com.get.salecenter.vo.StudentOfferItemVo;
import com.get.salecenter.vo.StudentPlanVo;
import com.itextpdf.text.Chunk;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Element;
import com.itextpdf.text.Font;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfWriter;
import com.itextpdf.text.pdf.draw.LineSeparator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 代理结算佣金业务逻辑类
 *
 * <AUTHOR>
 * @date 2021/12/21 11:17
 */
@Slf4j
@Service("financeAgencyCommissionSettlementServiceImpl")
public class AgencyCommissionSettlementServiceImpl implements AgencyCommissionSettlementService {
    @Resource
    private PaymentFormItemMapper paymentFormItemMapper;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private ISaleCenterClient saleCenterClient;
    @Resource
    private HtiAgencyCommissionSettlementService htiAgencyCommissionSettlementService;
    @Resource
    private AsyncExportService asyncExportService;
    @Resource
    private ICurrencyTypeService currencyTypeService;
    @Resource
    private PayableSettlementMessageMapper payableSettlementMessageMapper;
    @Resource
    private IFileCenterClient fileCenterClient;
    @Resource
    private IExchangeRateService exchangeRateService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private RPayablePlanSettlementFlagMapper payablePlanSettlementFlagMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private PaymentFormMapper paymentFormMapper;
    @Resource
    private PayablePlanSettlementBatchExchangeMapper payablePlanSettlementBatchExchangeMapper;
    @Resource
    private ReceiptFormMapper receiptFormMapper;
    @Resource
    private IPaymentFormService paymentFormService;
    @Resource
    private TemplateExcelUtils templateExcelUtils;
    @Resource
    private CurrencyTypeMapper currencyTypeMapper;
    @Resource
    @Lazy
    private PayablePlanSettlementAgentAccountMapper payablePlanSettlementAgentAccountMapper;
    @Resource
    private PayablePlanSettlementInstallmentMapper payablePlanSettlementInstallmentMapper;
    @Resource
    private PayablePlanSettlementStatusMapper payablePlanSettlementStatusMapper;
    @Resource
    @Lazy
    private IReceiptFormItemService receiptFormItemService;
    @Resource
    private IReminderCenterClient reminderCenterClient;
    @Resource
    private IInvoiceReceivablePlanService iInvoiceReceivablePlanService;
    @Resource
    private IInvoiceService iInvoiceService;




    //判断是否加逗号
    private static String addSpot(Object value) {
        String result = value == null ? "" : value.toString();
        return GeneralTool.isBlank(result) ? "," : ",";
    }

    private static String null2String(Object value) {
        String result = value == null ? "" : value.toString();
        return result;
    }

    //返回bankCode
    private static Map<String, String> getBankCode(String bankCode) {
        Map<String, String> codeMap = new HashMap<String, String>();
        int index = bankCode.indexOf("BSB:");
        if (index >= 0) {
            String substring = bankCode.substring(4);
            codeMap.put("address", substring);
            codeMap.put("addressCode", "BCD");

        } else {
            codeMap.put("address", bankCode);
            codeMap.put("addressCode", "SWF");
        }
        return codeMap;
    }

    private static void initAgencyPDF(Map agentInfoMap, Map<String, String> dynamicInfoMap, ZipOutputStream zipOutputStream) {
        //实例化一个PDF
        Document doc = new Document();
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            PdfWriter.getInstance(doc, bos);
            doc.open();
            //字体样式
            BaseFont bfChinese = null;
        /*try {
            bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        } catch (IOException e) {
            e.printStackTrace();
        }*/
            Font title_font = new Font(bfChinese, 18, Font.ITALIC);
            Font content_font = new Font(bfChinese, 12, Font.NORMAL);
            Font chinese_font = new Font(bfChinese, 14, Font.BOLD);
            Font amount_font = new Font(bfChinese, 14, Font.BOLD);

            //标题
            Paragraph title = new Paragraph("Make Payments");
            title.setAlignment(Element.ALIGN_CENTER);
            title.setSpacingBefore(10);
            doc.add(title);

            //二级标题 reference
            Paragraph second_title = new Paragraph("");
            Chunk second_content = new Chunk("Reference", title_font);
            second_title.setSpacingBefore(30);
            second_title.add(second_content);
            doc.add(second_title);

            //第二行内容
            Paragraph number_title = new Paragraph("");
            number_title.setSpacingBefore(10);
            Chunk number_content_title = new Chunk("Transaction reference number\n", content_font);
            //自定义日期+IFILE 序号


            Chunk number_content = new Chunk(dynamicInfoMap.get("mydate2") + "-" + dynamicInfoMap.get("customSerial"));
            number_title.add(number_content_title);
            number_title.add(number_content);
            doc.add(number_title);

            //分割线
            Paragraph line = new Paragraph("");
            line.setSpacingBefore(15);
            line.add(new Chunk(new LineSeparator()));
            doc.add(line);

            //三级标题
            Paragraph third_title = new Paragraph("");
            Chunk third_title_chunk = new Chunk("Pay to", title_font);
            third_title.setSpacingBefore(15);
            third_title.add(third_title_chunk);
            doc.add(third_title);

            Paragraph third_content = new Paragraph("");
            third_content.setSpacingBefore(10);
            Chunk loaction_title = new Chunk("Beneficiary bank location\n", content_font);
            //国家
            //Chunk location_chunk = new Chunk(agentInfoMap.get("countrycode").toString() + "\n\n");
            Chunk location_chunk = new Chunk(null2String(agentInfoMap.get("countrycode")) + "\n\n");

            Chunk acount_title = new Chunk("Beneficiary account number/IBAN\n", content_font);
            Chunk account_chunk = new Chunk(dynamicInfoMap.get("faccountNoPdf") + "\n\n");

            Chunk acountname_title = new Chunk("Beneficiary account name\n", content_font);
            Chunk accountname_chunk = new Chunk(dynamicInfoMap.get("faccountNamePdf") + "\n\n", chinese_font);

            Chunk currency_title = new Chunk("Payment currency\n", content_font);
            Chunk currency_chunk = new Chunk(dynamicInfoMap.get("outCurrencyPdf") + "\n\n");

            third_content.add(loaction_title);
            third_content.add(location_chunk);
            third_content.add(acount_title);
            third_content.add(account_chunk);
            third_content.add(acountname_title);
            third_content.add(accountname_chunk);
            third_content.add(currency_title);
            third_content.add(currency_chunk);

            doc.add(third_content);

            //分割线
            doc.add(line);

            //四级标题
            Paragraph fouth_title = new Paragraph("");
            Chunk fouth_title_chunk = new Chunk("Amount", title_font);
            fouth_title.setSpacingBefore(10);
            fouth_title.add(fouth_title_chunk);
            doc.add(fouth_title);

            Paragraph fouth_content = new Paragraph("");
            fouth_content.setSpacingBefore(10);
            Chunk amount_title = new Chunk("Payment Amount\n", content_font);

            Chunk amount_currency_chunk = new Chunk(dynamicInfoMap.get("outCurrencyPdf"));
            Chunk amount_chunk = new Chunk(dynamicInfoMap.get("outFeeTotal"), amount_font);

            fouth_content.add(amount_title);
            fouth_content.add(amount_currency_chunk);
            fouth_content.add(amount_chunk);
            doc.add(fouth_content);

            //分割线
            doc.add(line);

            //五级标题
            Paragraph fifth_title = new Paragraph("");
            Chunk fifth_title_chunk = new Chunk("Payment date", title_font);
            fifth_title.setSpacingBefore(15);
            fifth_title.add(fifth_title_chunk);
            doc.add(fifth_title);

            Paragraph fifth_content = new Paragraph("");
            fouth_content.setSpacingBefore(10);
            Chunk date_chunk = new Chunk(dynamicInfoMap.get("mydate"));
            fifth_content.add(date_chunk);
            doc.add(fifth_content);

            doc.close();
            bos.writeTo(zipOutputStream);
            bos.close();
        } catch (DocumentException | IOException e) {
            e.printStackTrace();
        }


    }

    /**
     * 代理佣金结算列表
     *
     * @Date 11:33 2021/12/21
     * <AUTHOR>
     */
    @Override
    public ListResponseBo agentSettlementList(SearchBean<AgentSettlementQueryDto> page) {
        AgentSettlementPageVo agentSettlementPageVo = saleCenterClient.agentSettlementList(page);
        Page p = BeanCopyUtils.objClone(agentSettlementPageVo.getPage(), Page::new);
        return new ListResponseBo<>(agentSettlementPageVo.getAgentSettlementDtoList(), p);
    }

    @Override
    public void agentSettlementListExport(AgentSettlementQueryDto agentSettlementVo) {
        String locale = SecureUtil.getLocale();
        UserInfo user = SecureUtil.getUser();
        agentSettlementVo.setBusinessType(TableEnum.SALE_STUDENT_OFFER_ITEM.key);
        if (GeneralTool.isEmpty(agentSettlementVo.getFkCompanyIds())) {
            agentSettlementVo.setFkCompanyIds(SecureUtil.getCompanyIds());
        }
        asyncExportService.asyncExportAgencySettlement(agentSettlementVo, RequestHeaderHandler.getHeaderMap(), locale, user);
    }

    @Override
    public void agentSettlementGrossAmountExport(AgentSettlementQueryDto agentSettlementVo) {
        String locale = SecureUtil.getLocale();
        UserInfo user = SecureUtil.getUser();
        Long fkCompanyId = SecureUtil.getFkCompanyId();
        if (GeneralTool.isEmpty(agentSettlementVo.getFkCompanyIds())) {
            agentSettlementVo.setFkCompanyIds(SecureUtil.getCompanyIds());
        }
        asyncExportService.asyncExportAgentSettlementGrossAmount(agentSettlementVo, RequestHeaderHandler.getHeaderMap(), locale, user, fkCompanyId);
    }

    /**
     * 代理佣金结算子项列表
     *
     * @Date 10:34 2021/12/22
     * <AUTHOR>
     */
    @Override
    public AgentSettlementItemVo itemDatas(AgentSettlementDto agentSettlementDto) {
        if (GeneralTool.isEmpty(agentSettlementDto.getAgentId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (GeneralTool.isNotEmpty(agentSettlementDto.getFkCompanyIds())) {
            if (!SecureUtil.validateCompanys(agentSettlementDto.getFkCompanyIds())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }
        if (GeneralTool.isNotEmpty(agentSettlementDto.getFkCompanyId())) {
            //兼容单公司条件
            if (!SecureUtil.validateCompany(agentSettlementDto.getFkCompanyId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }
        if (GeneralTool.isNotEmpty(agentSettlementDto.getCommissionMark())) {
            agentSettlementDto.setCommissionMark(agentSettlementDto.getCommissionMark().toLowerCase());
        }

        Long agentId = agentSettlementDto.getAgentId();
        AgentSettlementItemVo agentSettlementItemVo = new AgentSettlementItemVo();

        OfferSettlementVo  offerSettlementVo=null;
        List<AgentSettlementOfferItemVo> agentSettlementItemList = null;
        List<AgentSettlementInsuranceVo> agentSettlementInsuranceDtoList = null;
        List<AgentSettlementAccommodationVo> agentSettlementAccommodationDtoList = null;
        List<AgentSettlementServiceFeeVo> agentSettlementServiceFeeDtoList = null;

        Set<Long> countryIds = new HashSet<>();
        Set<Long> institutionIds = null;
        Set<Long> institutionCourseIds = null;
        Set<String> currencyTypeNumSet = new HashSet<>();
        List<Long> ids = new ArrayList<>();

//        ConfigVo configDto = permissionCenterClient.getConfigByKey("SETTLEMENT_COMMISSION_LIST_LIMIT").getData();
//        String configJson = configDto.getValue1();
//        JSONObject jsonObject = JSON.parseObject(configJson);
//        boolean geaFlag = SecureUtil.getStaffInfo().getFkCompanyId().equals(1L);
//        boolean iaeFlag = SecureUtil.getStaffInfo().getFkCompanyId().equals(3L);
//        boolean payInAdvanceFlag = false;
//        if (iaeFlag){
//            String iae = jsonObject.getString("IAE");
//            if (StringUtils.isNotBlank(iae)) {
//                payInAdvanceFlag = Integer.parseInt(iae)==1;
//            }
//        } else if (geaFlag) {
//            String gea = jsonObject.getString("GEA");
//            if (StringUtils.isNotBlank(gea)) {
//                payInAdvanceFlag = Integer.parseInt(gea)==1;
//            }
//        } else{
//            String other = jsonObject.getString("OTHER");
//            if (StringUtils.isNotBlank(other)) {
//                payInAdvanceFlag = Integer.parseInt(other)==1;
//            }
//        }
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.SETTLEMENT_COMMISSION_LIST_LIMIT.key, 1).getData();
        String configValue1 = companyConfigMap.get(SecureUtil.getFkCompanyId());
        boolean payInAdvanceFlag = configValue1.equals("1");

        List<Long> list = new ArrayList<>(1);
        list.add(agentId);

        //留学申请
        if (GeneralTool.isEmpty(agentSettlementDto.getBusinessType()) || TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(agentSettlementDto.getBusinessType())) {
            agentSettlementItemList = paymentFormItemMapper.agentSettlementOfferItemList(agentSettlementDto, list, ProjectKeyEnum.STEP_ENROLLED.key, ProjectKeyEnum.STEP_ENROLLED_TBC.key, false, payInAdvanceFlag);
            countryIds = agentSettlementItemList.stream().map(AgentSettlementOfferItemVo::getFkAreaCountryId).filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
            institutionIds = agentSettlementItemList.stream().map(AgentSettlementOfferItemVo::getFkInstitutionId).filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
            institutionCourseIds = agentSettlementItemList.stream().map(AgentSettlementOfferItemVo::getFkInstitutionCourseId).filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
            ids = agentSettlementItemList.stream().map(AgentSettlementOfferItemVo::getId).filter(GeneralTool::isNotEmpty).collect(Collectors.toList());
            currencyTypeNumSet = agentSettlementItemList.stream().map(AgentSettlementOfferItemVo::getAccountCurrencyTypeNum).filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
            Set<Long> collect = agentSettlementItemList.stream().map(AgentSettlementOfferItemVo::getStudentOfferItemId).collect(Collectors.toSet());
            Result<Map<Long, Object>> result = saleCenterClient.getDeferEntranceTimeByIds(collect);
            Map<Long, Object> data = result.getData();

            Map<Long,MSettlementBillItemVo> settlementBillMap;
            if(GeneralTool.isNotEmpty(ids)){
                List<MSettlementBillItemVo> msettlementbillList=paymentFormItemMapper.getAllSettlementBillItem(ids);
                if(GeneralTool.isNotEmpty(msettlementbillList)){
                    offerSettlementVo=new OfferSettlementVo();
                    offerSettlementVo.setFkSettlementBillId(msettlementbillList.get(0).getFkSettlementBillId());
                    offerSettlementVo.setFileKey(msettlementbillList.get(0).getFileKey());
                    offerSettlementVo.setFileNameOrc(msettlementbillList.get(0).getFileNameOrc());


                    settlementBillMap=msettlementbillList.stream().collect(
                            Collectors.toMap(MSettlementBillItemVo::getFkPayablePlanId,o->o,(v1, v2)->v1)
                    );
                } else {
                    settlementBillMap = null;
                }
            } else {
                settlementBillMap = null;
            }


            agentSettlementItemList.forEach(a -> {
                SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
                try {
                    Object o = data.get(a.getStudentOfferItemId());
                    if (o != null) {
                        a.setMaxDeferEntranceTimes(sf.parse(o.toString()));
                    }
                    if(GeneralTool.isNotEmpty(settlementBillMap)){
                        MSettlementBillItemVo settlementBillItemVo=settlementBillMap.get(a.getId());
                        if(GeneralTool.isNotEmpty(settlementBillItemVo)){
                            a.setFkSettlementBillId(settlementBillItemVo.getFkSettlementBillId());
                            a.setFileKey(settlementBillItemVo.getFileKey());
                            a.setFileNameOrc(settlementBillItemVo.getFileNameOrc());
                        }

                    }
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            });
        }
        //留学保险
        if (GeneralTool.isEmpty(agentSettlementDto.getBusinessType()) || TableEnum.SALE_STUDENT_INSURANCE.key.equals(agentSettlementDto.getBusinessType())) {
            agentSettlementInsuranceDtoList = paymentFormItemMapper.agentSettlementInsuranceList(agentSettlementDto, list);
            countryIds.addAll(agentSettlementInsuranceDtoList.stream().map(AgentSettlementInsuranceVo::getFkAreaCountryId).filter(GeneralTool::isNotEmpty).collect(Collectors.toSet()));
            currencyTypeNumSet.addAll(agentSettlementInsuranceDtoList.stream().map(AgentSettlementInsuranceVo::getAccountCurrencyTypeNum).filter(GeneralTool::isNotEmpty).collect(Collectors.toSet()));
            ids.addAll(agentSettlementInsuranceDtoList.stream().map(AgentSettlementInsuranceVo::getId).filter(GeneralTool::isNotEmpty).collect(Collectors.toSet()));
        }
        //留学住宿
        if (GeneralTool.isEmpty(agentSettlementDto.getBusinessType()) || TableEnum.SALE_STUDENT_ACCOMMODATION.key.equals(agentSettlementDto.getBusinessType())) {
            agentSettlementAccommodationDtoList = paymentFormItemMapper.agentSettlementAccommodationList(agentSettlementDto, list);
            countryIds.addAll(agentSettlementAccommodationDtoList.stream().map(AgentSettlementAccommodationVo::getFkAreaCountryId).filter(GeneralTool::isNotEmpty).collect(Collectors.toSet()));
            currencyTypeNumSet.addAll(agentSettlementAccommodationDtoList.stream().map(AgentSettlementAccommodationVo::getAccountCurrencyTypeNum).filter(GeneralTool::isNotEmpty).collect(Collectors.toSet()));
            ids.addAll(agentSettlementAccommodationDtoList.stream().map(AgentSettlementAccommodationVo::getId).filter(GeneralTool::isNotEmpty).collect(Collectors.toSet()));
        }
        //留学服务费
        if (GeneralTool.isEmpty(agentSettlementDto.getBusinessType()) || TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(agentSettlementDto.getBusinessType())) {
            agentSettlementServiceFeeDtoList = paymentFormItemMapper.agentSettlementServiceFeeList(agentSettlementDto, list);
            for (AgentSettlementServiceFeeVo agentSettlementServiceFeeVo : agentSettlementServiceFeeDtoList) {
                String[] split = agentSettlementServiceFeeVo.getFkAreaCountryIds().split(",");
                for (String fkAreaCountryId : split) {
                    countryIds.add(Long.valueOf(fkAreaCountryId));
                }
            }
            currencyTypeNumSet.addAll(agentSettlementServiceFeeDtoList.stream().map(AgentSettlementServiceFeeVo::getAccountCurrencyTypeNum).filter(GeneralTool::isNotEmpty).collect(Collectors.toSet()));
            ids.addAll(agentSettlementServiceFeeDtoList.stream().map(AgentSettlementServiceFeeVo::getId).filter(GeneralTool::isNotEmpty).collect(Collectors.toSet()));
        }
        if (GeneralTool.isEmpty(ids)) {
            return agentSettlementItemVo;
        }

        //根据国家ids获取国家名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIds)) {
            Result<Map<Long, String>> result1 = institutionCenterClient.getCountryNamesByIds(countryIds);
            if (!result1.isSuccess()) {
                throw new GetServiceException(result1.getMessage());
            }
            countryNamesByIds = result1.getData();
        }
        Map<Long, String> institutionNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(institutionIds)) {
            Result<Map<Long, String>> result2 = institutionCenterClient.getInstitutionNamesByIds(institutionIds);
            if (!result2.isSuccess()) {
                throw new GetServiceException(result2.getMessage());
            }
            institutionNamesByIds = result2.getData();
        }
        Map<Long, String> institutionCourseNameMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(institutionCourseIds)) {
            Result<Map<Long, String>> result3 = institutionCenterClient.getCourseNameByIds(institutionCourseIds);
            if (!result3.isSuccess()) {
                throw new GetServiceException(result3.getMessage());
            }
            institutionCourseNameMap = result3.getData();
        }
        Map<String, String> currencyNamesByNums = new HashMap<>();
        if (GeneralTool.isNotEmpty(currencyTypeNumSet)) {
            currencyNamesByNums = currencyTypeService.getCurrencyNamesByNums(currencyTypeNumSet);
        }


        List<SettlementCommissionNotice> settlementCommissionNotices = payableSettlementMessageMapper.selectList(Wrappers.<SettlementCommissionNotice>lambdaQuery()
                .in(SettlementCommissionNotice::getFkPayablePlanId, ids)
                .isNull(SettlementCommissionNotice::getNumSettlementBatch));
        Map<Long, SettlementCommissionNotice> settlementInfo = settlementCommissionNotices.stream().collect(Collectors.toMap(SettlementCommissionNotice::getFkPayablePlanId, Function.identity()));
        //应付计划- 结算标记
        Map<Long, List<PayablePlanSettlementAgentAccountVo>> settlementMarkMap = htiAgencyCommissionSettlementService.getSettlementMarkByPayablePlanIds(ids);


        if (GeneralTool.isNotEmpty(agentSettlementItemList)) {
            for (AgentSettlementOfferItemVo agentSettlementOfferItemVo : agentSettlementItemList) {
                agentSettlementOfferItemVo.setPayableAmount(agentSettlementOfferItemVo.getPayableAmount().setScale(2, RoundingMode.HALF_UP));
                if (GeneralTool.isNotEmpty(agentSettlementOfferItemVo.getFkAreaCountryId())) {
                    //设置国家名称
                    agentSettlementOfferItemVo.setCountryName(countryNamesByIds.get(agentSettlementOfferItemVo.getFkAreaCountryId()));
                }
                if (GeneralTool.isNotEmpty(agentSettlementOfferItemVo.getFkInstitutionId())) {
                    //学校名称
                    agentSettlementOfferItemVo.setInstitutionName(institutionNamesByIds.get(agentSettlementOfferItemVo.getFkInstitutionId()));
                }
                if (GeneralTool.isNotEmpty(agentSettlementOfferItemVo.getAccountCurrencyTypeNum())) {
                    agentSettlementOfferItemVo.setAccountCurrencyName(currencyNamesByNums.get(agentSettlementOfferItemVo.getAccountCurrencyTypeNum()));
                }
                SettlementCommissionNotice settlementCommissionNotice = settlementInfo.get(agentSettlementOfferItemVo.getId());
                if (GeneralTool.isNotEmpty(settlementCommissionNotice)) {
                    agentSettlementOfferItemVo.setCommissionNoticeId(settlementCommissionNotice.getId());
                    agentSettlementOfferItemVo.setCommissionNotice(settlementCommissionNotice.getCommissionNotice());
                    agentSettlementOfferItemVo.setNumSettlementBatch(settlementCommissionNotice.getNumSettlementBatch());
                    agentSettlementOfferItemVo.setRemark(settlementCommissionNotice.getRemark());
                }
                if (GeneralTool.isNotEmpty(agentSettlementOfferItemVo.getFkInstitutionCourseId())) {
                    //课程名称
                    StringBuilder str = new StringBuilder(institutionCourseNameMap.get(agentSettlementOfferItemVo.getFkInstitutionCourseId()));
                    if (GeneralTool.isNotEmpty(agentSettlementOfferItemVo.getDurationType()) && GeneralTool.isNotEmpty(agentSettlementOfferItemVo.getDuration())) {
                        str.append(" (").append(agentSettlementOfferItemVo.getDuration()).append(ProjectExtraEnum.getValueByKey(agentSettlementOfferItemVo.getDurationType(), ProjectExtraEnum.DURATION_TYPE)).append(")");
                    }
                    agentSettlementOfferItemVo.setCourseName(str.toString());
                }
                if (GeneralTool.isNotEmpty(agentSettlementOfferItemVo.getOldCourseCustomName())) {
                    //旧课程名称
                    StringBuilder oldCourseCustomName = new StringBuilder(agentSettlementOfferItemVo.getOldCourseCustomName());
                    if (GeneralTool.isNotEmpty(agentSettlementOfferItemVo.getDurationType()) && GeneralTool.isNotEmpty(agentSettlementOfferItemVo.getDuration())) {
                        oldCourseCustomName.append(" (").append(agentSettlementOfferItemVo.getDuration()).append(ProjectExtraEnum.getValueByKey(agentSettlementOfferItemVo.getDurationType(), ProjectExtraEnum.DURATION_TYPE)).append(")");
                    }
                    agentSettlementOfferItemVo.setOldCourseCustomName(oldCourseCustomName.toString());
                }
                agentSettlementOfferItemVo.setStatusName(LocaleMessageUtils.getMessage(ProjectExtraEnum.getEnum(agentSettlementOfferItemVo.getStatus(), ProjectExtraEnum.AGENT_SETTLEMENT_STATUS).name()));
                if (GeneralTool.isNotEmpty(settlementMarkMap.get(agentSettlementOfferItemVo.getId()))) {
                    agentSettlementOfferItemVo.setPayablePlanSettlementAgentAccountDtoList(BeanCopyUtils.copyListProperties(settlementMarkMap.get(agentSettlementOfferItemVo.getId()), com.get.financecenter.vo.PayablePlanSettlementAgentAccountVo::new));
                }
                //根据应付计划id以及结算状态获取对应的预付金额
                BigDecimal prepaymentAmount = getPrepaymentAmountByPayablePlanId(agentSettlementOfferItemVo.getId(), agentSettlementDto.getStatusSettlement());
                agentSettlementOfferItemVo.setPrepaymentAmount(prepaymentAmount);
            }
        }

        if (GeneralTool.isNotEmpty(agentSettlementInsuranceDtoList)) {
            for (AgentSettlementInsuranceVo agentSettlementInsuranceVo : agentSettlementInsuranceDtoList) {
                agentSettlementInsuranceVo.setPayableAmount(agentSettlementInsuranceVo.getPayableAmount().setScale(2, RoundingMode.HALF_UP));
                if (GeneralTool.isNotEmpty(agentSettlementInsuranceVo.getFkAreaCountryId())) {
                    //设置国家名称
                    agentSettlementInsuranceVo.setCountryName(countryNamesByIds.get(agentSettlementInsuranceVo.getFkAreaCountryId()));
                }
                if (GeneralTool.isNotEmpty(agentSettlementInsuranceVo.getAccountCurrencyTypeNum())) {
                    agentSettlementInsuranceVo.setAccountCurrencyName(currencyNamesByNums.get(agentSettlementInsuranceVo.getAccountCurrencyTypeNum()));
                }
                SettlementCommissionNotice settlementCommissionNotice = settlementInfo.get(agentSettlementInsuranceVo.getId());
                if (GeneralTool.isNotEmpty(settlementCommissionNotice)) {
                    agentSettlementInsuranceVo.setCommissionNotice(settlementCommissionNotice.getCommissionNotice());
                    agentSettlementInsuranceVo.setNumSettlementBatch(settlementCommissionNotice.getNumSettlementBatch());
                    agentSettlementInsuranceVo.setRemark(settlementCommissionNotice.getRemark());
                }
                agentSettlementInsuranceVo.setStatusName(LocaleMessageUtils.getMessage(ProjectExtraEnum.getEnum(agentSettlementInsuranceVo.getStatus(), ProjectExtraEnum.AGENT_SETTLEMENT_STATUS).name()));
                if (GeneralTool.isNotEmpty(settlementMarkMap.get(agentSettlementInsuranceVo.getId()))) {
                    agentSettlementInsuranceVo.setPayablePlanSettlementAgentAccountDtoList(BeanCopyUtils.copyListProperties(settlementMarkMap.get(agentSettlementInsuranceVo.getId()), com.get.financecenter.vo.PayablePlanSettlementAgentAccountVo::new));
                }
                //根据应付计划id以及结算状态获取对应的预付金额
                BigDecimal prepaymentAmount = getPrepaymentAmountByPayablePlanId(agentSettlementInsuranceVo.getId(), agentSettlementDto.getStatusSettlement());
                agentSettlementInsuranceVo.setPrepaymentAmount(prepaymentAmount);
            }
        }

        if (GeneralTool.isNotEmpty(agentSettlementAccommodationDtoList)) {
            for (AgentSettlementAccommodationVo agentSettlementAccommodationVo : agentSettlementAccommodationDtoList) {
                agentSettlementAccommodationVo.setPayableAmount(agentSettlementAccommodationVo.getPayableAmount().setScale(2, RoundingMode.HALF_UP));
                if (GeneralTool.isNotEmpty(agentSettlementAccommodationVo.getFkAreaCountryId())) {
                    //设置国家名称
                    agentSettlementAccommodationVo.setCountryName(countryNamesByIds.get(agentSettlementAccommodationVo.getFkAreaCountryId()));
                }
                if (GeneralTool.isNotEmpty(agentSettlementAccommodationVo.getAccountCurrencyTypeNum())) {
                    agentSettlementAccommodationVo.setAccountCurrencyName(currencyNamesByNums.get(agentSettlementAccommodationVo.getAccountCurrencyTypeNum()));
                }
                SettlementCommissionNotice settlementCommissionNotice = settlementInfo.get(agentSettlementAccommodationVo.getId());
                if (GeneralTool.isNotEmpty(settlementCommissionNotice)) {
                    agentSettlementAccommodationVo.setCommissionNotice(settlementCommissionNotice.getCommissionNotice());
                    agentSettlementAccommodationVo.setNumSettlementBatch(settlementCommissionNotice.getNumSettlementBatch());
                    agentSettlementAccommodationVo.setRemark(settlementCommissionNotice.getRemark());
                }
                agentSettlementAccommodationVo.setStatusName(LocaleMessageUtils.getMessage(ProjectExtraEnum.getEnum(agentSettlementAccommodationVo.getStatus(), ProjectExtraEnum.AGENT_SETTLEMENT_STATUS).name()));
                if (GeneralTool.isNotEmpty(settlementMarkMap.get(agentSettlementAccommodationVo.getId()))) {
                    agentSettlementAccommodationVo.setPayablePlanSettlementAgentAccountDtoList(BeanCopyUtils.copyListProperties(settlementMarkMap.get(agentSettlementAccommodationVo.getId()), com.get.financecenter.vo.PayablePlanSettlementAgentAccountVo::new));
                }
                //根据应付计划id以及结算状态获取对应的预付金额
                BigDecimal prepaymentAmount = getPrepaymentAmountByPayablePlanId(agentSettlementAccommodationVo.getId(), agentSettlementDto.getStatusSettlement());
                agentSettlementAccommodationVo.setPrepaymentAmount(prepaymentAmount);
            }
        }
        if (GeneralTool.isNotEmpty(agentSettlementServiceFeeDtoList)) {
            for (AgentSettlementServiceFeeVo agentSettlementServiceFeeVo : agentSettlementServiceFeeDtoList) {
                agentSettlementServiceFeeVo.setPayableAmount(agentSettlementServiceFeeVo.getPayableAmount().setScale(2, RoundingMode.HALF_UP));
                //设置国家名称
                if (GeneralTool.isNotEmpty(agentSettlementServiceFeeVo.getFkAreaCountryIds())) {
                    StringBuilder countryName = new StringBuilder();
                    String[] split = agentSettlementServiceFeeVo.getFkAreaCountryIds().split(",");
                    for (int i = 0; i < split.length; i++) {
                        if (i != 0) {
                            countryName.append(",");
                        }
                        countryName.append(countryNamesByIds.get(Long.valueOf(split[i])));
                    }
                    agentSettlementServiceFeeVo.setCountryName(countryName.toString());
                }
                if (GeneralTool.isNotEmpty(agentSettlementServiceFeeVo.getAccountCurrencyTypeNum())) {
                    agentSettlementServiceFeeVo.setAccountCurrencyName(currencyNamesByNums.get(agentSettlementServiceFeeVo.getAccountCurrencyTypeNum()));
                }
                SettlementCommissionNotice settlementCommissionNotice = settlementInfo.get(agentSettlementServiceFeeVo.getId());
                if (GeneralTool.isNotEmpty(settlementCommissionNotice)) {
                    agentSettlementServiceFeeVo.setCommissionNotice(settlementCommissionNotice.getCommissionNotice());
                    agentSettlementServiceFeeVo.setNumSettlementBatch(settlementCommissionNotice.getNumSettlementBatch());
                    agentSettlementServiceFeeVo.setRemark(settlementCommissionNotice.getRemark());
                }
                agentSettlementServiceFeeVo.setStatusName(LocaleMessageUtils.getMessage(ProjectExtraEnum.getEnum(agentSettlementServiceFeeVo.getStatus(), ProjectExtraEnum.AGENT_SETTLEMENT_STATUS).name()));
                if (GeneralTool.isNotEmpty(settlementMarkMap.get(agentSettlementServiceFeeVo.getId()))) {
                    agentSettlementServiceFeeVo.setPayablePlanSettlementAgentAccountDtoList(BeanCopyUtils.copyListProperties(settlementMarkMap.get(agentSettlementServiceFeeVo.getId()), com.get.financecenter.vo.PayablePlanSettlementAgentAccountVo::new));
                }
                //根据应付计划id以及结算状态获取对应的预付金额
                BigDecimal prepaymentAmount = getPrepaymentAmountByPayablePlanId(agentSettlementServiceFeeVo.getId(), agentSettlementDto.getStatusSettlement());
                agentSettlementServiceFeeVo.setPrepaymentAmount(prepaymentAmount);
            }
        }

        agentSettlementItemVo.setOfferSettlement(offerSettlementVo);
        agentSettlementItemVo.setAgentSettlementOfferItemDtoList(agentSettlementItemList);
        agentSettlementItemVo.setAgentSettlementInsuranceDtoList(agentSettlementInsuranceDtoList);
        agentSettlementItemVo.setAgentSettlementAccommodationDtoList(agentSettlementAccommodationDtoList);
        agentSettlementItemVo.setAgentSettlementServiceFeeDtoList(agentSettlementServiceFeeDtoList);
        return agentSettlementItemVo;
    }

    /**
     * Author Cream
     * Description : //佣金结算第四步导出
     * Date 2023/9/6 14:08
     * Params:
     * Return
     *
     * @param commissionSummaryDto1
     * @param response
     */
    @Override
    public void agentSettlementListFourthStepExport(CommissionSummaryDto commissionSummaryDto1, HttpServletResponse response) {
        SearchBean<CommissionSummaryDto> searchBean = new SearchBean<>();
        searchBean.setData(commissionSummaryDto1);
        searchBean.setCurrentPage(1);
        searchBean.setShowCount(50000);
        List<CommissionSummaryExportVo> commissionSummaryExportVoList = new ArrayList<>();
        //查询数据
        List<CommissionSummaryVo> commissionSummaryVoList = commissionSummary(searchBean).getDatas();
        if (GeneralTool.isNotEmpty(commissionSummaryVoList)) {
            //导出数据封装
            commissionSummaryVoList.forEach(commissionSummaryDto -> {
                CommissionSummaryExportVo commissionSummaryExportVo = new CommissionSummaryExportVo();
                BeanUtils.copyProperties(commissionSummaryDto, commissionSummaryExportVo);
                Boolean lockFlag = commissionSummaryDto.getLockFlag();
                if (GeneralTool.isNotEmpty(lockFlag)) {
                    commissionSummaryExportVo.setLock(lockFlag ? "是" : "否");
                }
                if (GeneralTool.isNotEmpty(commissionSummaryDto.getBankBranchName())) {
                    commissionSummaryExportVo.setBankName(commissionSummaryExportVo.getBankName() + "(" + commissionSummaryDto.getBankBranchName() + ")");
                }
                commissionSummaryExportVoList.add(commissionSummaryExportVo);
            });
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_available_for_export"));
        }
        FileUtils.exportExcel(response, commissionSummaryExportVoList, "agentSettlementListFourthStepExport", CommissionSummaryExportVo.class);
    }

    /**
     * 代理佣金结算提交
     *
     * @Date 10:34 2021/12/22
     * <AUTHOR>
     */
    @Override
    @Transactional
    public void submitSettlement(List<SubmitSettlementDto> submitSettlementDtoList) {
        for (SubmitSettlementDto submitSettlementDto : submitSettlementDtoList) {
            List<SubmitSettlementItemDto> submitSettlementItemDtos = submitSettlementDto.getSubmitSettlementItemVos();
            for (SubmitSettlementItemDto submitSettlementItemDto : submitSettlementItemDtos) {
                List<Long> settlementIds = Arrays.asList(submitSettlementItemDto.getSettlementIds().split(",")).stream()
                        .map(Long::valueOf)
                        .collect(Collectors.toList());
                LambdaQueryWrapper<PayablePlanSettlementInstallment> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(PayablePlanSettlementInstallment::getFkPayablePlanId, submitSettlementItemDto.getFkPayablePlanId())
                        .eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_UNTREATED.key)
                        .in(PayablePlanSettlementInstallment::getId, settlementIds);

                PayablePlanSettlementInstallment payablePlanSettlementInstallment = new PayablePlanSettlementInstallment();
                //插入结算默认账号
                if (GeneralTool.isNotEmpty(submitSettlementItemDto.getFkAgentContractAccountId()) && GeneralTool.isNotEmpty(submitSettlementItemDto.getAgentContractAccountNum())) {
                    payablePlanSettlementInstallment.setFkAgentContractAccountId(submitSettlementItemDto.getFkAgentContractAccountId());
                    payablePlanSettlementInstallment.setFkCurrencyTypeNum(submitSettlementItemDto.getAgentContractAccountNum());
                }

                List<PayablePlanSettlementInstallment> payablePlanSettlementInstallments = payablePlanSettlementInstallmentMapper.selectList(wrapper);
                //计算应付计划 分期表状态为0的预计支付金额 加起来   + 下一步同应付 同币种的金额
                BigDecimal amountActual = payablePlanSettlementInstallments.stream().map(PayablePlanSettlementInstallment::getAmountActual).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal serviceFeeActual = payablePlanSettlementInstallments.stream().map(PayablePlanSettlementInstallment::getServiceFeeActual).reduce(BigDecimal.ZERO, BigDecimal::add);
                List<PayablePlanSettlementInstallment> settlementInstallments = payablePlanSettlementInstallmentMapper.selectList(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                        .eq(PayablePlanSettlementInstallment::getFkPayablePlanId, submitSettlementItemDto.getFkPayablePlanId())
                        .eq(PayablePlanSettlementInstallment::getFkAgentContractAccountId, submitSettlementItemDto.getFkAgentContractAccountId())
                        .eq(PayablePlanSettlementInstallment::getStatusSettlement, ProjectExtraEnum.SETTLEMENT_IN_PROGRESS.key));
                if (GeneralTool.isNotEmpty(settlementInstallments)) {
                    PayablePlanSettlementInstallment settlementInstallment = settlementInstallments.get(0);
                    amountActual = amountActual.add(settlementInstallment.getAmountActual());
                    serviceFeeActual = serviceFeeActual.add(settlementInstallment.getServiceFeeActual());
                    settlementIds.addAll(settlementInstallments.stream().map(PayablePlanSettlementInstallment::getId).collect(Collectors.toList()));
                }

                payablePlanSettlementInstallment.setAmountActual(amountActual);
                payablePlanSettlementInstallment.setServiceFeeActual(serviceFeeActual);
                payablePlanSettlementInstallment.setStatus(ProjectExtraEnum.INSTALLMENT_PROCESSING.key);
                payablePlanSettlementInstallment.setStatusSettlement(ProjectExtraEnum.SETTLEMENT_IN_PROGRESS.key);
                utilService.setUpdateInfo(payablePlanSettlementInstallment);
                payablePlanSettlementInstallmentMapper.update(payablePlanSettlementInstallment, Wrappers.<PayablePlanSettlementInstallment>lambdaUpdate()
                        .in(PayablePlanSettlementInstallment::getId, settlementIds));

                for (Long settlementId : settlementIds) {
                    PayablePlanSettlementStatus payablePlanSettlementStatus = new PayablePlanSettlementStatus();
                    payablePlanSettlementStatus.setFkPayablePlanId(submitSettlementItemDto.getFkPayablePlanId());
                    payablePlanSettlementStatus.setFkPayablePlanSettlementInstallmentId(settlementId);
                    payablePlanSettlementStatus.setStatusSettlement(ProjectExtraEnum.SETTLEMENT_IN_PROGRESS.key);
                    utilService.updateUserInfoToEntity(payablePlanSettlementStatus);
                    payablePlanSettlementStatusMapper.insert(payablePlanSettlementStatus);
                }


            }
        }
    }

    /**
     * 更新结算标记
     *
     * @Date 12:38 2021/12/23
     * <AUTHOR>
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean updateSettlementAgentAccount(List<SettlementAgentAccountUpdateDto> settlementAgentAccountUpdateDtoList) {
        for (SettlementAgentAccountUpdateDto settlementAgentAccountUpdateDto : settlementAgentAccountUpdateDtoList) {
            if (settlementAgentAccountUpdateDto.getFkAgentContractAccountId().equals(settlementAgentAccountUpdateDto.getSourceFkAgentContractAccountId())) {
                continue;
            }
            LambdaQueryWrapper<PayablePlanSettlementInstallment> lambdaQueryWrapper = Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                    .eq(PayablePlanSettlementInstallment::getFkPayablePlanId, settlementAgentAccountUpdateDto.getPayablePlanId())
                    .eq(PayablePlanSettlementInstallment::getStatusSettlement, settlementAgentAccountUpdateDto.getStatusSettlement());
            if (GeneralTool.isNotEmpty(settlementAgentAccountUpdateDto.getSourceFkAgentContractAccountId())) {
                lambdaQueryWrapper.eq(PayablePlanSettlementInstallment::getFkAgentContractAccountId, settlementAgentAccountUpdateDto.getSourceFkAgentContractAccountId());
            } else {
                lambdaQueryWrapper.isNull(PayablePlanSettlementInstallment::getFkAgentContractAccountId);
            }
            List<PayablePlanSettlementInstallment> payablePlanSettlementInstallments = payablePlanSettlementInstallmentMapper.selectList(lambdaQueryWrapper);
            List<Long> settlementIds = payablePlanSettlementInstallments.stream().map(PayablePlanSettlementInstallment::getId).collect(Collectors.toList());

            List<PayablePlanSettlementInstallment> targetPayablePlanSettlementInstallments = payablePlanSettlementInstallmentMapper.selectList(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                    .eq(PayablePlanSettlementInstallment::getFkPayablePlanId, settlementAgentAccountUpdateDto.getPayablePlanId())
                    .eq(PayablePlanSettlementInstallment::getStatusSettlement, settlementAgentAccountUpdateDto.getStatusSettlement())
                    .eq(PayablePlanSettlementInstallment::getFkAgentContractAccountId, settlementAgentAccountUpdateDto.getFkAgentContractAccountId()));

            PayablePlanSettlementInstallment updatePayablePlanSettlementInstallment = new PayablePlanSettlementInstallment();
            updatePayablePlanSettlementInstallment.setFkAgentContractAccountId(settlementAgentAccountUpdateDto.getFkAgentContractAccountId());
            updatePayablePlanSettlementInstallment.setFkCurrencyTypeNum(settlementAgentAccountUpdateDto.getAgentContractAccountNum());
            if (GeneralTool.isNotEmpty(targetPayablePlanSettlementInstallments)) {
                PayablePlanSettlementInstallment payablePlanSettlementInstallment = payablePlanSettlementInstallments.get(0);
                BigDecimal amountActual = payablePlanSettlementInstallment.getAmountActual();
                BigDecimal serviceFeeActual = payablePlanSettlementInstallment.getServiceFeeActual();
                PayablePlanSettlementInstallment targetPayablePlanSettlementInstallment = targetPayablePlanSettlementInstallments.get(0);
                amountActual = amountActual.add(targetPayablePlanSettlementInstallment.getAmountActual());
                serviceFeeActual = serviceFeeActual.add(targetPayablePlanSettlementInstallment.getServiceFeeActual());
                updatePayablePlanSettlementInstallment.setAmountActual(amountActual);
                updatePayablePlanSettlementInstallment.setServiceFeeActual(serviceFeeActual);
                settlementIds.addAll(targetPayablePlanSettlementInstallments.stream().map(PayablePlanSettlementInstallment::getId).collect(Collectors.toList()));
            }
            utilService.setUpdateInfo(updatePayablePlanSettlementInstallment);
            payablePlanSettlementInstallmentMapper.update(updatePayablePlanSettlementInstallment, Wrappers.<PayablePlanSettlementInstallment>lambdaQuery().in(PayablePlanSettlementInstallment::getId, settlementIds));
        }
        return true;
    }


    /**
     * 代理附件校验
     *
     * @Date 12:23 2022/12/13
     * <AUTHOR>
     */
    @Override
    public String checkAgentAttachment(List<BatchDownloadAgentReconciliationDto> batchDownloadAgentReconciliationVoList) {
        //附件导出失败的代理名
        StringBuilder failAgentName = new StringBuilder();

        Set<Long> agentIdSet = batchDownloadAgentReconciliationVoList.stream().map(b -> {
            if (GeneralTool.isNotEmpty(b.getSettlementPortAgentId())) {
                return b.getSettlementPortAgentId();
            } else {
                return b.getFkAgentId();
            }
        }).collect(Collectors.toSet());

        //根据代理id获取佣金所需附件
        List<MediaAndAttachedVo> agentCommissionMedias = saleCenterClient.getAgentCommissionMedias(agentIdSet).getData();
//        List<CompanyConfigInfoDto> agentInfoOptLimit = permissionCenterClient.getCompanySettlementConfigInfo(ProjectKeyEnum.AGENT_INFO_OPT_LIMIT.key);
//        Map<Long, Integer> agentInfoOptLimitMap = agentInfoOptLimit.stream().collect(Collectors.toMap(CompanyConfigInfoDto::getCompanyId, CompanyConfigInfoDto::getValue));
        Map<Long, String> agentInfoOptLimitMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.AGENT_INFO_OPT_LIMIT.key, 1).getData();
        Map<Long, Long> agentCompanyMap = saleCenterClient.getAgentCompanyIdByIds(agentIdSet);

        //代理信息
        Map<Long, Agent> agentMap = saleCenterClient.getAgentsByIds(agentIdSet).getData();

        //先按代理分 再按银行账户分 再按业务分 再按 币种分
        for (BatchDownloadAgentReconciliationDto batchDownloadAgentReconciliationDto : batchDownloadAgentReconciliationVoList) {
            Agent agent = agentMap.get(batchDownloadAgentReconciliationDto.getSettlementPortAgentId());
            String agentName = agent.getName();
            //String agentName = saleCenterClient.getAgentNameById(batchDownloadAgentReconciliationVo.getFkAgentId()).getData();
            //该代理下是否有人民币结算
            List<AgentSettlementBatchExportDto> cnyAccount = batchDownloadAgentReconciliationDto.getAgentSettlementOfferItemDtoList().stream().filter(a -> "CNY".equals(a.getAgentAccountCurrencyTypeNum())).collect(Collectors.toList());
            //该代理文件是否
            int fileSuccessFlag = 0;
            if (GeneralTool.isNotEmpty(cnyAccount)) {
                //代理身份证是否有限制
                Integer agentLimit = 1;
                if (GeneralTool.isNotEmpty(agentCompanyMap) && GeneralTool.isNotEmpty(agentCompanyMap.get(batchDownloadAgentReconciliationDto.getFkAgentId()))) {
                    agentLimit = Integer.parseInt(agentInfoOptLimitMap.get(agentCompanyMap.get(batchDownloadAgentReconciliationDto.getFkAgentId())));
                }
                //如果是人民币账户，需要导出身份证以及营业执照
                //营业执照
                List<MediaAndAttachedVo> licenseDtos = agentCommissionMedias.stream().filter(i -> i.getTypeKey().equals(FileTypeEnum.BUSINESS_LICENSE.key)).collect(Collectors.toList());
                //身份证
                List<MediaAndAttachedVo> idCardDtos = agentCommissionMedias.stream().filter(i -> i.getTypeKey().equals(FileTypeEnum.SALE_AGENT_ID_CARD_FRONT.key) || i.getTypeKey().equals(FileTypeEnum.SALE_AGENT_ID_CARD_BACK.key)).collect(Collectors.toList());
                if (agent.getNature().equals(ProjectExtraEnum.AGENT_NATURE_COMPANY.key.toString())) {
                    //公司
                    if (GeneralTool.isNotEmpty(licenseDtos)) {
                        for (MediaAndAttachedVo datum : licenseDtos) {
                            if (datum.getFkTableId().equals(batchDownloadAgentReconciliationDto.getFkAgentId())) {
                                FileVo fileVo = new FileVo();
                                fileVo.setFileKey(datum.getFileKey());
                                fileVo.setFileNameOrc(datum.getFileNameOrc());
                                Result<SaleFileDto> result = fileCenterClient.getDownloadFile(fileVo);
                                if (!result.isSuccess() || null == result.getData()) {
                                    //读不出附件，该代理导出失败
                                    fileSuccessFlag = 1;
                                }
                            }
                        }
                    } else {
                        fileSuccessFlag = 1;
                    }
                    //身份证
                    if (GeneralTool.isNotEmpty(idCardDtos)) {
                        for (MediaAndAttachedVo datum : idCardDtos) {
                            if (datum.getFkTableId().equals(batchDownloadAgentReconciliationDto.getFkAgentId())) {
                                FileVo fileVo = new FileVo();
                                fileVo.setFileKey(datum.getFileKey());
                                fileVo.setFileNameOrc(datum.getFileNameOrc());
                                Result<SaleFileDto> result = fileCenterClient.getDownloadFile(fileVo);
                                if ((!result.isSuccess() || null == result.getData()) && agentLimit == 1) {
                                    //读不出附件，该代理导出失败
                                    fileSuccessFlag = 2;
                                }
                            }
                        }
                    } else {
                        if (agentLimit == 1) {
                            fileSuccessFlag = 2;
                        }
                    }

                } else {
                    //其它类型
                    if (GeneralTool.isNotEmpty(idCardDtos)) {
                        for (MediaAndAttachedVo datum : idCardDtos) {
                            if (datum.getFkTableId().equals(batchDownloadAgentReconciliationDto.getFkAgentId())) {
                                FileVo fileVo = new FileVo();
                                fileVo.setFileKey(datum.getFileKey());
                                fileVo.setFileNameOrc(datum.getFileNameOrc());
                                Result<SaleFileDto> result = fileCenterClient.getDownloadFile(fileVo);
                                if ((!result.isSuccess() || null == result.getData()) && agentLimit == 1) {
                                    //读不出附件，该代理导出失败
                                    fileSuccessFlag = 2;
                                }
                            }
                        }
                    } else {
                        if (agentLimit == 1) {
                            fileSuccessFlag = 2;
                        }
                    }
                }
            }
            //有附件读不出来，该代理导出失败
            if (fileSuccessFlag != 0) {
                if (!"".equals(failAgentName.toString())) {
                    failAgentName.append("</br></br>");
                }
                switch (fileSuccessFlag) {
                    case 1:
                        failAgentName.append(LocaleMessageUtils.getFormatMessage("agent_file_download_license_failed", agentName));
                        break;
                    case 2:
                        failAgentName.append(LocaleMessageUtils.getFormatMessage("agent_file_download_idcard_failed", agentName));
                        break;
                }

            }
            log.info("类型为：" + fileSuccessFlag);
        }

        log.info(failAgentName.toString());
        return failAgentName.toString();
    }

    /**
     * 批量下载对账单
     *
     * @return
     * @Date 16:32 2022/3/15
     * <AUTHOR>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDownloadAgentReconciliationExcel(List<BatchDownloadAgentReconciliationDto> batchDownloadAgentReconciliationVoList, HttpServletResponse response) {
        ZipOutputStream zos = null;
        try {
            ServletOutputStream responseOutputStream = response.getOutputStream();
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            zos = new ZipOutputStream(outputStream);
//            response.setContentType("application/octet-stream;charset=UTF-8");
//            response.setHeader("Content-Disposition", "attachment;filename="
//                    + "代理对账单"
//                    + ".zip");
//            response.addHeader("Pargam", "no-cache");
//            response.addHeader("Cache-Control", "no-cache");
//            response.reset();
            //限制代理有合同信息且当前时间在有效期内才允许导单
            Set<Long> agentIdSet = batchDownloadAgentReconciliationVoList.stream().map(b -> {
                if (GeneralTool.isNotEmpty(b.getSettlementPortAgentId())) {
                    return b.getSettlementPortAgentId();
                } else {
                    return b.getFkAgentId();
                }
            }).collect(Collectors.toSet());
//            Result<List<String>> checkResult = saleCenterClient.checkAgentContractByAgentIds(agentIdSet);
//            if (!checkResult.isSuccess()) {
//                throw new GetServiceException(checkResult.getMessage());
//            }
//            List<String> failedAgentName = checkResult.getData();
//            if (GeneralTool.isNotEmpty(failedAgentName)) {
//                String str = StringUtils.join(failedAgentName.stream().map(String::toString).distinct().toArray(), ",");
//                throw new GetServiceException(LocaleMessageUtils.getFormatMessage("invalidation_of_agency_contract", str));
//            }
            if (!saleCenterClient.checkAgentData(batchDownloadAgentReconciliationVoList)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("missing_agent_data"));
            }
            //获取代理-结算口代理id映射 原代理-结算口代理
            Map<Long, Long> agentIdMap = new HashMap<>();
            for (BatchDownloadAgentReconciliationDto batchDownloadAgentReconciliationDto : batchDownloadAgentReconciliationVoList) {
                agentIdMap.put(batchDownloadAgentReconciliationDto.getFkAgentId(), batchDownloadAgentReconciliationDto.getSettlementPortAgentId());
            }
            //根据代理id获取佣金所需附件
            List<MediaAndAttachedVo> agentCommissionMedias = saleCenterClient.getAgentCommissionMedias(agentIdSet).getData();
            SimpleDateFormat formatter = new SimpleDateFormat("yyyyMM");

            //附件导出失败的代理名
            StringBuilder failAgentName = new StringBuilder();

            //先按代理分 再按银行账户分 再按业务分 再按 币种分
            for (BatchDownloadAgentReconciliationDto batchDownloadAgentReconciliationDto : batchDownloadAgentReconciliationVoList) {
                String agentName = saleCenterClient.getAgentNameById(batchDownloadAgentReconciliationDto.getFkAgentId()).getData();
                //该代理下是否有人民币结算
                List<AgentSettlementBatchExportDto> cnyAccount = batchDownloadAgentReconciliationDto.getAgentSettlementOfferItemDtoList().stream().filter(a -> "CNY".equals(a.getAgentAccountCurrencyTypeNum())).collect(Collectors.toList());

                Map<String, byte[]> mediaMap = new HashMap<>();
                if (GeneralTool.isNotEmpty(cnyAccount)) {
                    //如果是人民币账户，导出身份证以及营业执照
                    if (GeneralTool.isNotEmpty(agentCommissionMedias)) {
                        for (MediaAndAttachedVo datum : agentCommissionMedias) {
                            if (datum.getFkTableId().equals(GeneralTool.isNotEmpty(batchDownloadAgentReconciliationDto.getSettlementPortAgentId()) ?
                                    batchDownloadAgentReconciliationDto.getSettlementPortAgentId() : batchDownloadAgentReconciliationDto.getFkAgentId())) {
                                FileVo fileVo = new FileVo();
                                fileVo.setFileKey(datum.getFileKey());
                                fileVo.setFileNameOrc(datum.getFileNameOrc());
                                Result<SaleFileDto> result = fileCenterClient.getDownloadFile(fileVo);
                                if (!result.isSuccess() || null == result.getData()) {
                                    //读不出附件，该代理导出失败
                                } else {
                                    SaleFileDto dto = result.getData();
                                    if (dto != null) {
                                        StringBuilder str = new StringBuilder();
                                        str.append(agentName).append("-").append(formatter.format(new Date())).append("-");
                                        if (datum.getTypeKey().equals(FileTypeEnum.SALE_AGENT_ID_CARD_FRONT.key)) {
                                            str.append(FileTypeEnum.SALE_AGENT_ID_CARD_FRONT.value);
                                        } else if (datum.getTypeKey().equals(FileTypeEnum.SALE_AGENT_ID_CARD_BACK.key)) {
                                            str.append(FileTypeEnum.SALE_AGENT_ID_CARD_BACK.value);
                                        } else if (datum.getTypeKey().equals(FileTypeEnum.BUSINESS_LICENSE.key)) {
                                            str.append(FileTypeEnum.BUSINESS_LICENSE.value);
                                        }
                                        str.append(datum.getFileTypeOrc());
                                        mediaMap.put(str.toString(), dto.getBytes());
                                    }
                                }
                            }
                        }
                    }
                }
                //附件都可以读取,往流里面加入附件
//                if (fileSuccessFlag) {
                TemplateExcelUtils.mergeAttachments(mediaMap, zos);
//                }
                List<AgentSettlementBatchExportDto> agentSettlementBatchExportDtos = batchDownloadAgentReconciliationDto.getAgentSettlementOfferItemDtoList();

                Map<Long, List<AgentSettlementBatchExportDto>> agentSettlementOfferItemAccountMap = new HashMap<>();
                for (AgentSettlementBatchExportDto agentSettlementBatchExportDto : agentSettlementBatchExportDtos) {
                    List<AgentSettlementBatchExportDto> agentSettlementOfferItemVoList = agentSettlementOfferItemAccountMap.get(agentSettlementBatchExportDto.getFkAgentContractAccountId());
                    if (GeneralTool.isEmpty(agentSettlementOfferItemVoList)) {
                        agentSettlementOfferItemVoList = new ArrayList<>();
                    }
                    agentSettlementOfferItemVoList.add(agentSettlementBatchExportDto);
                    agentSettlementOfferItemAccountMap.put(agentSettlementBatchExportDto.getFkAgentContractAccountId(), agentSettlementOfferItemVoList);
                }
                Set<Long> agentContractAccountIds = agentSettlementOfferItemAccountMap.keySet();
                //代理银行账户
                Map<Long, AgentContractAccountVo> agentContractAccountDtoMap = saleCenterClient.getAgentContractAccountByAccountIds(new ArrayList<>(agentContractAccountIds)).getData();
                //账户分
                for (Long agentContractAccountId : agentSettlementOfferItemAccountMap.keySet()) {
                    List<AgentSettlementBatchExportDto> agentSettlementOfferItemVoList = agentSettlementOfferItemAccountMap.get(agentContractAccountId);
                    Map<String, List<AgentSettlementBatchExportDto>> agentSettlementTypeKeyMap = new HashMap<>();
                    for (AgentSettlementBatchExportDto agentSettlementBatchExportDto : agentSettlementOfferItemVoList) {
                        List<AgentSettlementBatchExportDto> agentSettlementBatchExportTypeKetVoList = agentSettlementTypeKeyMap.get(agentSettlementBatchExportDto.getFkTypeKey());
                        if (GeneralTool.isEmpty(agentSettlementBatchExportTypeKetVoList)) {
                            agentSettlementBatchExportTypeKetVoList = new ArrayList<>();
                        }
                        agentSettlementBatchExportTypeKetVoList.add(agentSettlementBatchExportDto);
                        agentSettlementTypeKeyMap.put(agentSettlementBatchExportDto.getFkTypeKey(), agentSettlementBatchExportTypeKetVoList);
                    }
                    //业务分
                    for (String fkTypeKey : agentSettlementTypeKeyMap.keySet()) {
                        // 同代理 同账户 同业务的学习计划
                        List<AgentSettlementBatchExportDto> agentSettlementBatchExportFkTypeKeyList = agentSettlementTypeKeyMap.get(fkTypeKey);
                        AgentContractAccountVo agentContractAccountVo = agentContractAccountDtoMap.get(agentContractAccountId);
//                        Integer company = saleCenterClient.getAgentCompanyIdById(batchDownloadAgentReconciliationVoList.get(0).getFkAgentId());
                        if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(fkTypeKey)) {
                            //学习计划有两种不同币种excel表需要for循环再区分一遍
                            Map<String, List<AgentSettlementBatchExportDto>> agentSettlementOfferItemCurrencyTypeMap = new HashMap<>();
                            String rmbCurrency = "人民币";
                            String foreignCurrency = "外币";
                            for (AgentSettlementBatchExportDto agentSettlementBatchExportDto : agentSettlementBatchExportFkTypeKeyList) {
                                //币种分
                                if ("CNY".equals(agentSettlementBatchExportDto.getAgentAccountCurrencyTypeNum())) {
                                    List<AgentSettlementBatchExportDto> agentSettlementOfferItemVoRmbList = agentSettlementOfferItemCurrencyTypeMap.get(rmbCurrency);
                                    if (GeneralTool.isEmpty(agentSettlementOfferItemVoRmbList)) {
                                        agentSettlementOfferItemVoRmbList = new ArrayList<>();
                                    }
                                    agentSettlementOfferItemVoRmbList.add(agentSettlementBatchExportDto);
                                    agentSettlementOfferItemCurrencyTypeMap.put(rmbCurrency, agentSettlementOfferItemVoRmbList);
                                } else {
                                    List<AgentSettlementBatchExportDto> agentSettlementOfferItemVoRmbList = agentSettlementOfferItemCurrencyTypeMap.get(foreignCurrency);
                                    if (GeneralTool.isEmpty(agentSettlementOfferItemVoRmbList)) {
                                        agentSettlementOfferItemVoRmbList = new ArrayList<>();
                                    }
                                    agentSettlementOfferItemVoRmbList.add(agentSettlementBatchExportDto);
                                    agentSettlementOfferItemCurrencyTypeMap.put(foreignCurrency, agentSettlementOfferItemVoRmbList);
                                }
                            }
                            for (String currency : agentSettlementOfferItemCurrencyTypeMap.keySet()) {
                                //同代理 同业务 同账户 同币种的应付计划
                                List<AgentSettlementBatchExportDto> agentSettlementOfferItemVoCurrencyList = agentSettlementOfferItemCurrencyTypeMap.get(currency);
                                List<Long> payablePlanIdList = agentSettlementOfferItemVoCurrencyList.stream().map(AgentSettlementBatchExportDto::getPayablePlanId).collect(Collectors.toList());
                                List<AgentSettlementIntermediaryModel> agentSettlementIntermediaryModelList = paymentFormItemMapper.agentSettlementItemIntermediaryExport(agentSettlementOfferItemVoCurrencyList, batchDownloadAgentReconciliationDto.getFlag(), currency);
                                if (GeneralTool.isNotEmpty(agentContractAccountVo.getFkCurrencyTypeNum())) {
                                    String currencyName = currencyTypeMapper.getCurrencyNameByNum(agentContractAccountVo.getFkCurrencyTypeNum());
                                    agentContractAccountVo.setCurrencyTypeName(currencyName);
                                }
                                if (GeneralTool.isNotEmpty(agentContractAccountVo.getFkAreaCountryId())) {
                                    String countryName = institutionCenterClient.getCountryNameAndNumById(agentContractAccountVo.getFkAreaCountryId()).getData();
                                    if (GeneralTool.isNotEmpty(countryName)) {
                                        agentContractAccountVo.setCountryName(countryName);
                                    }
                                }


                                Map<String, BigDecimal> payablePlanCurrencyMap = new HashMap<>();
                                List<CurrencyAccountModel> currencyAccountModelList = new ArrayList<>();
                                for (AgentSettlementIntermediaryModel agentSettlementIntermediaryModel : agentSettlementIntermediaryModelList) {
                                    //Split:  ${list.splitRate}
                                    if (GeneralTool.isEmpty(agentSettlementIntermediaryModel.getSplitRate())) {
                                        agentSettlementIntermediaryModel.setSplitRate("100.00%");
                                    } else {
                                        agentSettlementIntermediaryModel.setSplitRate(agentSettlementIntermediaryModel.getSplitRate() + "%");
                                    }
                                    //RATE: ${list.commissionRate}
                                    if (GeneralTool.isEmpty(agentSettlementIntermediaryModel.getCommissionRate())) {
                                        agentSettlementIntermediaryModel.setCommissionRate("FIX");
                                    } else {
                                        agentSettlementIntermediaryModel.setCommissionRate(agentSettlementIntermediaryModel.getCommissionRate() + "%");
                                    }

                                    BigDecimal amount = payablePlanCurrencyMap.get(agentSettlementIntermediaryModel.getPayablePlanCurrency());
                                    if (amount == null) {
                                        amount = BigDecimal.ZERO;
                                    }
                                    amount = amount.add(agentSettlementIntermediaryModel.getOutstanding());
                                    payablePlanCurrencyMap.put(agentSettlementIntermediaryModel.getPayablePlanCurrency(), amount);
                                    if (GeneralTool.isNotEmpty(agentSettlementIntermediaryModel.getTuitionAmountIae())) {
                                        agentSettlementIntermediaryModel.setTuitionAmountIae(agentSettlementIntermediaryModel.getTuitionAmountIae().setScale(2, RoundingMode.HALF_UP));
                                    }
                                }

                                for (String payablePlanCurrency : payablePlanCurrencyMap.keySet()) {
                                    BigDecimal amount = payablePlanCurrencyMap.get(payablePlanCurrency);
                                    CurrencyAccountModel currencyAccountModel = new CurrencyAccountModel();
                                    currencyAccountModel.setAmount(amount);
                                    currencyAccountModel.setCurrencyNum(payablePlanCurrency);
                                    currencyAccountModelList.add(currencyAccountModel);
                                }

//                                for (AgentSettlementIntermediaryModel agentSettlementIntermediaryModel : agentSettlementIntermediaryModelList) {
//                                    //插入结算标记
//                                    PayablePlanSettlementAgentAccount payablePlanSettlementAgentAccount = new PayablePlanSettlementAgentAccount();
//                                    payablePlanSettlementAgentAccount.setFkPayablePlanId(agentSettlementIntermediaryModel.getId());
//                                    payablePlanSettlementAgentAccount.setFkAgentContractAccountId(agentContractAccountVo.getId());
//                                    payablePlanSettlementAgentAccount.setFkCurrencyTypeNum(agentContractAccountVo.getFkCurrencyTypeNum());
//                                    //111111  如有异常如何回滚（需要改造）
//                                    saleCenterClient.insertPayablePlanSettlementAgentAccount(payablePlanSettlementAgentAccount);
//                                }
//                                //提交代理确认结算
//                                agentConfirmSettlementSubmit(payablePlanIdList);

                                int accumulatedRows = agentSettlementIntermediaryModelList.size() + currencyAccountModelList.size();
                                Map<String, Object> param = new HashMap<>();
                                param.put("currencyAccountModelList", currencyAccountModelList);
                                param.put("agentName", agentName);
                                param.put("list", agentSettlementIntermediaryModelList);
                                param.put("accountInfo", agentContractAccountVo);
                                param.put("userName", SecureUtil.getStaffName());
                                param.put("date", DateUtil.now());
                                StringBuilder fileName = new StringBuilder();
                                //学生数，超过五个的文件就只显示五个学生名 不能太长了
                                int studentNum = 0;
                                fileName.append(agentName).append("-").append(formatter.format(new Date())).append("-");
                                for (AgentSettlementIntermediaryModel agentSettlementIntermediaryModel : agentSettlementIntermediaryModelList) {
                                    studentNum = studentNum + 1;
                                    if (studentNum > 5) {
                                        continue;
                                    }
                                    fileName.append(agentSettlementIntermediaryModel.getStudentName()).append(".");
                                }
                                if (studentNum > 5) {
                                    fileName.append("等共").append(studentNum).append("个学生");
                                }

                                if (rmbCurrency.equals(currency)) {
                                    Set<Long> targetIds = batchDownloadAgentReconciliationDto.getAgentSettlementOfferItemDtoList().stream().map(AgentSettlementBatchExportDto::getPayablePlanId).collect(Collectors.toSet());
                                    List<Long> itemIds = saleCenterClient.getStudentOfferItemByPayIds(targetIds).getData();
                                    List<MediaAndAttachedVo> data = saleCenterClient.getItemMedias(new HashSet<>(itemIds)).getData();
                                    List<StudentOfferItemVo> studentOfferItemVoList = saleCenterClient.getStudentByOfferItemIds(itemIds);
                                    Map<Long, String> studentMap = studentOfferItemVoList.stream().collect(Collectors.toMap(StudentOfferItemVo::getId, StudentOfferItemVo::getFkStudentName));
                                    Map<String, byte[]> media = new HashMap<>();
                                    if (GeneralTool.isNotEmpty(data)) {
                                        for (MediaAndAttachedVo datum : data) {
                                            FileVo fileVo = new FileVo();
                                            fileVo.setFileKey(datum.getFileKey());
                                            fileVo.setFileNameOrc(datum.getFileNameOrc());
                                            Result<SaleFileDto> result = fileCenterClient.getDownloadFile(fileVo);
                                            if (result.isSuccess()) {
                                                SaleFileDto dto = result.getData();
                                                if (dto != null) {
                                                    String studentName = studentMap.get(datum.getFkTableId());
                                                    String dtoFileName = dto.getFileName();
                                                    if (media.containsKey(dtoFileName)) {
                                                        media.put(studentName + "_" + dtoFileName + new Random().nextInt(10), dto.getBytes());
                                                    } else {
                                                        media.put(studentName + "_" + dtoFileName, dto.getBytes());
                                                    }
                                                }
                                            }
                                        }
                                        TemplateExcelUtils.mergeAttachments(media, zos);
                                    }
                                    fileName.append("-人民币中介结算汇出表");
                                    TemplateExcelUtils.downLoadExcelZip(fileName.toString(), ExcelTypeEnum.HIT_SETTLEMENT.excelTemplateName, param, zos);
                                    List<AgentSettlementTransferModel> agentSettlementTransferModelList = paymentFormItemMapper.agentSettlementItemTransferExport(payablePlanIdList, batchDownloadAgentReconciliationDto.getFlag(), agentContractAccountVo.getFkCurrencyTypeNum());
                                    //结算ids
                                    Set<Long> agentIds = new HashSet<>();
                                    for (AgentSettlementTransferModel agentSettlementTransferModel : agentSettlementTransferModelList) {
                                        if (agentIdMap.containsKey(agentSettlementTransferModel.getAgentId())) {
                                            agentIds.add(agentIdMap.get(agentSettlementTransferModel.getAgentId()));
                                        } else {
                                            agentIds.add(agentSettlementTransferModel.getAgentId());
                                        }
                                    }

                                    Result<Map<Long, Agent>> agentsByIdsResult = saleCenterClient.getAgentsByIds(agentIds);
                                    if (!agentsByIdsResult.isSuccess()) {
                                        throw new GetServiceException(agentsByIdsResult.getMessage());
                                    }
                                    Map<Long, Agent> agentMap = agentsByIdsResult.getData();
                                    for (AgentSettlementTransferModel agentSettlementTransferModel : agentSettlementTransferModelList) {
                                        Long portAgentId;
                                        if (agentIdMap.containsKey(agentSettlementTransferModel.getAgentId())) {
                                            portAgentId = agentIdMap.get(agentSettlementTransferModel.getAgentId());
                                        } else {
                                            portAgentId = agentSettlementTransferModel.getAgentId();
                                        }
                                        Agent portAgent = agentMap.get(portAgentId);
                                        agentSettlementTransferModel.setLegalPersonName(portAgent.getLegalPerson());
                                        agentSettlementTransferModel.setIdCard(portAgent.getIdCardNum());
                                        if ("1".equals(portAgent.getNature())) {
                                            agentSettlementTransferModel.setIdCardNum(portAgent.getTaxCode());
                                        } else {
                                            agentSettlementTransferModel.setIdCardNum(portAgent.getIdCardNum());
                                        }
                                        if (GeneralTool.isNotEmpty(portAgent.getNature())) {
                                            agentSettlementTransferModel.setNature(Integer.valueOf(portAgent.getNature()));
                                        }
                                        agentSettlementTransferModel.setSocialCreditCode(portAgent.getTaxCode());
                                    }

                                    Map<Long, Object> mobile = saleCenterClient.getAgentContractPersonMobileByAgentId(agentIds);
                                    for (AgentSettlementTransferModel model : agentSettlementTransferModelList) {
                                        if (mobile.containsKey(model.getAgentId())) {
                                            model.setContactInformation(mobile.get(model.getAgentId()).toString());
                                        }
                                        model.setCardType(ProjectExtraEnum.getInitialValueByKey(model.getAccountCardType(), ProjectExtraEnum.cardType));
                                        model.setNatureOfBeneficiary(ProjectExtraEnum.getValueByKey(model.getNature(), ProjectExtraEnum.AGENT_NATURE_TYPE_NAME));
                                    }
                                    AgentSettlementTransferModel agentSettlementTransferModel = agentSettlementTransferModelList.get(0);
                                    param = new HashMap<>();
                                    param.put("list", agentSettlementTransferModelList);
                                    param.put("userName", SecureUtil.getStaffName());
                                    param.put("date", DateUtil.now());
                                    fileName = new StringBuilder();
                                    studentNum = 0;
                                    fileName.append(agentSettlementTransferModel.getAgentName()).append("-").append(formatter.format(new Date())).append("-");
                                    for (AgentSettlementTransferModel settlementTransferModel : agentSettlementTransferModelList) {
                                        studentNum = studentNum + 1;
                                        if (studentNum > 5) {
                                            continue;
                                        }
                                        fileName.append(settlementTransferModel.getStudentName()).append(".");
                                    }
                                    if (studentNum > 5) {
                                        fileName.append("等共").append(studentNum).append("个学生");
                                    }
                                    fileName.append("-易思汇");
                                    TemplateExcelUtils.downLoadExcelZip(fileName.toString(), ExcelTypeEnum.EASY_TRANSFER.excelTemplateName, param, zos);
                                } else {
                                    fileName.append("-外币中介结算汇出表");
                                    TemplateExcelUtils.downLoadExcelZip(fileName.toString(), ExcelTypeEnum.HIT_SETTLEMENT.excelTemplateName, param, zos);
                                }

                            }

                        } else if (TableEnum.SALE_STUDENT_ACCOMMODATION.key.equals(fkTypeKey)) {
                            List<Long> payablePlanIdList = agentSettlementBatchExportFkTypeKeyList.stream().map(AgentSettlementBatchExportDto::getPayablePlanId).collect(Collectors.toList());

                            List<AgentSettlementAccommodationModel> agentSettlementAccommodationModelList = paymentFormItemMapper.agentSettlementAccommodationExport(agentSettlementBatchExportFkTypeKeyList, batchDownloadAgentReconciliationDto.getFlag(), agentContractAccountVo.getFkCurrencyTypeNum());
                            StringBuilder fileName = new StringBuilder();
                            //学生数，超过五个的文件就只显示五个学生名 不能太长了
                            int studentNum = 0;
                            fileName.append(agentName).append("-").append(formatter.format(new Date())).append("-");
                            Map<String, BigDecimal> currencyAccountMap = new HashMap<>();
                            List<CurrencyAccountModel> currencyAccountModelList = new ArrayList<>();
                            for (AgentSettlementAccommodationModel agentSettlementAccommodationModel : agentSettlementAccommodationModelList) {
                                BigDecimal amount = currencyAccountMap.get(agentSettlementAccommodationModel.getPayablePlanCurrency());
                                if (amount == null) {
                                    amount = BigDecimal.ZERO;
                                }
                                amount = amount.add(agentSettlementAccommodationModel.getOutstanding());
                                currencyAccountMap.put(agentSettlementAccommodationModel.getPayablePlanCurrency(), amount);
                                studentNum = studentNum + 1;
                                if (studentNum > 5) {
                                    continue;
                                }
                                fileName.append(agentSettlementAccommodationModel.getStudentName()).append(".");
                            }
                            if (studentNum > 5) {
                                fileName.append("等共").append(studentNum).append("个学生");
                            }

                            for (String currency : currencyAccountMap.keySet()) {
                                BigDecimal amount = currencyAccountMap.get(currency);
                                CurrencyAccountModel currencyAccountModel = new CurrencyAccountModel();
                                currencyAccountModel.setAmount(amount);
                                currencyAccountModel.setCurrencyNum(currency);
                                currencyAccountModelList.add(currencyAccountModel);
                            }

                            int accumulatedRows = agentSettlementAccommodationModelList.size() + currencyAccountModelList.size();
                            Map<String, Object> param = new HashMap<>();
                            param.put("currencyAccountModelList", currencyAccountModelList);
                            param.put("agentName", agentName);
                            param.put("list", agentSettlementAccommodationModelList);
                            param.put("accountInfo", agentContractAccountVo);
                            param.put("userName", SecureUtil.getStaffName());
                            param.put("date", DateUtil.now());
                            fileName.append("-留学小屋汇出表");
                            TemplateExcelUtils.downLoadExcelZip(fileName.toString(), ExcelTypeEnum.HIT_SETTLEMENT.excelTemplateName, param, zos);
                        } else if (TableEnum.SALE_STUDENT_INSURANCE.key.equals(fkTypeKey)) {
                            List<Long> payablePlanIdList = agentSettlementBatchExportFkTypeKeyList.stream().map(AgentSettlementBatchExportDto::getPayablePlanId).collect(Collectors.toList());

                            List<AgentSettlementInsuranceModel> agentSettlementItemInsuranceExport = paymentFormItemMapper.agentSettlementItemInsuranceExport(agentSettlementBatchExportFkTypeKeyList, batchDownloadAgentReconciliationDto.getFlag(), agentContractAccountVo.getFkCurrencyTypeNum());
                            Map<String, BigDecimal> currencyAccountMap = new HashMap<>();
                            List<CurrencyAccountModel> currencyAccountModelList = new ArrayList<>();
                            for (AgentSettlementInsuranceModel agentSettlementInsuranceModel : agentSettlementItemInsuranceExport) {
                                BigDecimal amount = currencyAccountMap.get(agentSettlementInsuranceModel.getPayablePlanCurrency());
                                if (amount == null) {
                                    amount = BigDecimal.ZERO;
                                }
                                amount = amount.add(agentSettlementInsuranceModel.getOutstanding());
                                currencyAccountMap.put(agentSettlementInsuranceModel.getPayablePlanCurrency(), amount);
                            }

                            for (String currency : currencyAccountMap.keySet()) {
                                BigDecimal amount = currencyAccountMap.get(currency);
                                CurrencyAccountModel currencyAccountModel = new CurrencyAccountModel();
                                currencyAccountModel.setAmount(amount);
                                currencyAccountModel.setCurrencyNum(currency);
                                currencyAccountModelList.add(currencyAccountModel);
                            }
                            StringBuilder fileName = new StringBuilder();
                            //学生数，超过五个的文件就只显示五个学生名 不能太长了
                            int studentNum = 0;
                            fileName.append(agentName).append("-").append(formatter.format(new Date())).append("-");
                            for (AgentSettlementInsuranceModel agentSettlementInsuranceModel : agentSettlementItemInsuranceExport) {
                                agentSettlementInsuranceModel.setDescription("Insurance");
                                studentNum = studentNum + 1;
                                if (studentNum > 5) {
                                    continue;
                                }
                                fileName.append(agentSettlementInsuranceModel.getStudentName()).append(".");
                            }
                            if (studentNum > 5) {
                                fileName.append("等共").append(studentNum).append("个学生");
                            }
                            int accumulatedRows = agentSettlementItemInsuranceExport.size() + currencyAccountModelList.size();
                            Map<String, Object> param = new HashMap<>();
                            param.put("currencyAccountModelList", currencyAccountModelList);
                            param.put("agentName", agentName);
                            param.put("list", agentSettlementItemInsuranceExport);
                            param.put("accountInfo", agentContractAccountVo);
                            param.put("userName", SecureUtil.getStaffName());
                            param.put("date", DateUtil.now());
                            fileName.append("-留学保险汇出表");
                            TemplateExcelUtils.downLoadExcelZip(fileName.toString(), ExcelTypeEnum.HIT_SETTLEMENT.excelTemplateName, param, zos);
                        } else if (TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(fkTypeKey)) {
//                            List<Long> payablePlanIdList = agentSettlementBatchExportFkTypeKeyList.stream().map(AgentSettlementBatchExportDto::getPayablePlanId).collect(Collectors.toList());
//
//                            List<AgentSettlementServiceFeeModel> agentSettlementServiceFeeExport = paymentFormItemMapper.agentSettlementServiceFeeExport(agentSettlementBatchExportFkTypeKeyList, batchDownloadAgentReconciliationDto.getFlag(), agentContractAccountVo.getFkCurrencyTypeNum());
//
//                            List<AgentSettlementServiceFeeModel> serviceFeeModel = agentSettlementServiceFeeExport.stream().filter(agentSettlementServiceFeeModel -> 21L != agentSettlementServiceFeeModel.getFkStudentServiceFeeTypeId()).collect(Collectors.toList());
//                            List<AgentSettlementServiceFeeModel> tourServiceFeeModel = agentSettlementServiceFeeExport.stream().filter(agentSettlementServiceFeeModel -> 21L == agentSettlementServiceFeeModel.getFkStudentServiceFeeTypeId()).collect(Collectors.toList());
//                            if (GeneralTool.isNotEmpty(serviceFeeModel)) {
//                                serviceFeeModel(batchDownloadAgentReconciliationDto, serviceFeeModel, agentName, formatter, agentContractAccountVo, zos, agentSettlementBatchExportFkTypeKeyList, ExcelTypeEnum.SERVICE_FEE.excelTemplateName);
//                            }
//                            if (GeneralTool.isNotEmpty(tourServiceFeeModel)) {
//                                serviceFeeModel(batchDownloadAgentReconciliationDto, tourServiceFeeModel, agentName, formatter, agentContractAccountVo, zos, agentSettlementBatchExportFkTypeKeyList, ExcelTypeEnum.TOUR_SERVICE_FEE.excelTemplateName);
//                            }
//                            if (batchDownloadAgentReconciliationDto.getFlag()) {
//                                Result<Boolean> result = saleCenterClient.agentConfirmSettlement(agentSettlementBatchExportFkTypeKeyList);
//                                if (!result.isSuccess() || GeneralTool.isEmpty(result.getData())) {
//                                    throw new GetServiceException(result.getMessage());
//                                }
//                            }
                        }


                    }
                }


            }
            zos.finish();
            MultipartFile multipartFile = FileUtils.getFile(outputStream, "statementOfAccount.zip", "zip");
            Result<List<FileDto>> result = fileCenterClient.uploadAppendix(new MultipartFile[]{multipartFile}, LoggerModulesConsts.EXPORT);
            if (result.isSuccess()) {
                List<FileDto> data = result.getData();
                FileDto fileDto = data.get(0);
                if (GeneralTool.isNotEmpty(fileDto)) {
                    StaffDownload download = new StaffDownload();
                    download.setFkStaffId(SecureUtil.getStaffId())
                            .setOptKey(FileTypeEnum.D_LIST_EXPORT.key)
                            .setOptDescription("《代理对账单确认表》" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy_MM_dd_HH_mm_ss")))
                            .setStatus(2)
                            .setFkFileGuid(fileDto.getFileGuid())
                            .setGmtCreate(new Date());
                    download.setGmtCreateUser(SecureUtil.getLoginId());
                    download.setId(permissionCenterClient.addDownloadRecord(download));
                }
                List<Long> agentIdList = batchDownloadAgentReconciliationVoList.stream().map(BatchDownloadAgentReconciliationDto::getFkAgentId).collect(Collectors.toList());
                //删除佣金结算第二步标记
                LambdaQueryWrapper<RPayablePlanSettlementFlag> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(RPayablePlanSettlementFlag::getStatusSettlement, ProjectExtraEnum.SETTLEMENT_IN_PROGRESS.key).in(RPayablePlanSettlementFlag::getFkAgentId, agentIdList);
                payablePlanSettlementFlagMapper.delete(lambdaQueryWrapper);
            }
            //手动将字节流写出去
            responseOutputStream.write(outputStream.toByteArray());
            responseOutputStream.flush();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("下载失败：" + e.getMessage());
            throw new GetServiceException(LocaleMessageUtils.getMessage("download_failed") + e.getMessage());
        } finally {
            IoUtil.close(zos);
        }
    }

    /**
     * 提交代理确认结算
     * @param agentSettlementBatchExportVoList
     */
    private void agentConfirmSettlementSubmit(List<AgentSettlementBatchExportDto> agentSettlementBatchExportVoList) {
        LambdaQueryWrapper<PayablePlanSettlementInstallment> payablePlanSettlementInstallmentLambdaQueryWrapper = Wrappers.<PayablePlanSettlementInstallment>lambdaQuery();
        for (AgentSettlementBatchExportDto agentSettlementBatchExportDto : agentSettlementBatchExportVoList) {
            payablePlanSettlementInstallmentLambdaQueryWrapper.or(queryWrapper ->
                    queryWrapper.eq(PayablePlanSettlementInstallment::getFkPayablePlanId, agentSettlementBatchExportDto.getPayablePlanId())
                            .eq(PayablePlanSettlementInstallment::getFkAgentContractAccountId, agentSettlementBatchExportDto.getFkAgentContractAccountId())
                            .eq(PayablePlanSettlementInstallment::getStatusSettlement, agentSettlementBatchExportDto.getStatusSettlement()));
        }
        //检查是否有结算标记 结算银行账号
        List<PayablePlanSettlementInstallment> settlementInstallmentList = payablePlanSettlementInstallmentMapper.selectList(payablePlanSettlementInstallmentLambdaQueryWrapper);
        if (settlementInstallmentList.stream().anyMatch(settlementInstallment -> GeneralTool.isEmpty(settlementInstallment.getFkAgentContractAccountId()))) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("SETTLEMENT_BANK_ACCOUNT_IS_NULL"));
        }

        for (AgentSettlementBatchExportDto agentSettlementBatchExportDto : agentSettlementBatchExportVoList) {
            Integer statusSettlement = agentSettlementBatchExportDto.getStatusSettlement();
            Integer targetStatusSettlement;
            if (statusSettlement.equals(ProjectExtraEnum.SETTLEMENT_IN_PROGRESS.key)) {
                targetStatusSettlement = ProjectExtraEnum.AGENT_CONFIRMATION.key;
            } else if (statusSettlement.equals(ProjectExtraEnum.AGENT_CONFIRMATION.key)) {
                targetStatusSettlement = ProjectExtraEnum.FINANCIAL_RECOGNITION.key;
            } else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
            }

            PayablePlanSettlementInstallment payablePlanSettlementInstallment = new PayablePlanSettlementInstallment();
            LambdaQueryWrapper<PayablePlanSettlementInstallment> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PayablePlanSettlementInstallment::getFkPayablePlanId, agentSettlementBatchExportDto.getPayablePlanId())
                    .eq(PayablePlanSettlementInstallment::getStatusSettlement, statusSettlement)
                    .eq(PayablePlanSettlementInstallment::getFkAgentContractAccountId, agentSettlementBatchExportDto.getFkAgentContractAccountId());
            List<PayablePlanSettlementInstallment> payablePlanSettlementInstallments = payablePlanSettlementInstallmentMapper.selectList(wrapper);
            List<Long> settlementIds = payablePlanSettlementInstallments.stream().map(PayablePlanSettlementInstallment::getId).collect(Collectors.toList());

            BigDecimal amountActual = payablePlanSettlementInstallments.get(0).getAmountActual();
            BigDecimal serviceFeeActual = payablePlanSettlementInstallments.get(0).getServiceFeeActual();
            List<PayablePlanSettlementInstallment> settlementInstallments = payablePlanSettlementInstallmentMapper.selectList(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                    .eq(PayablePlanSettlementInstallment::getFkPayablePlanId, agentSettlementBatchExportDto.getPayablePlanId())
                    .eq(PayablePlanSettlementInstallment::getFkAgentContractAccountId, agentSettlementBatchExportDto.getFkAgentContractAccountId())
                    .eq(PayablePlanSettlementInstallment::getStatusSettlement, targetStatusSettlement));
            if (GeneralTool.isNotEmpty(settlementInstallments)) {
                PayablePlanSettlementInstallment settlementInstallment = settlementInstallments.get(0);
                amountActual = amountActual.add(settlementInstallment.getAmountActual());
                serviceFeeActual = serviceFeeActual.add(settlementInstallment.getServiceFeeActual());
                settlementIds.addAll(settlementInstallments.stream().map(PayablePlanSettlementInstallment::getId).collect(Collectors.toList()));
            }
            payablePlanSettlementInstallment.setAmountActual(amountActual);
            payablePlanSettlementInstallment.setServiceFeeActual(serviceFeeActual);
            payablePlanSettlementInstallment.setStatusSettlement(targetStatusSettlement);
            payablePlanSettlementInstallment.setAccountExportTime(new Date());
            payablePlanSettlementInstallment.setRollBackTime(DateUtil.nextMonth());
            utilService.setUpdateInfo(payablePlanSettlementInstallment);
            payablePlanSettlementInstallmentMapper.update(payablePlanSettlementInstallment, Wrappers.<PayablePlanSettlementInstallment>lambdaUpdate().in(PayablePlanSettlementInstallment::getId, settlementIds));

            for (Long settlementId : settlementIds) {
                PayablePlanSettlementStatus payablePlanSettlementStatus = new PayablePlanSettlementStatus();
                payablePlanSettlementStatus.setFkPayablePlanId(agentSettlementBatchExportDto.getPayablePlanId());
                payablePlanSettlementStatus.setStatusSettlement(targetStatusSettlement);
                payablePlanSettlementStatus.setFkPayablePlanSettlementInstallmentId(settlementId);
                utilService.updateUserInfoToEntity(payablePlanSettlementStatus);
                payablePlanSettlementStatusMapper.insert(payablePlanSettlementStatus);
            }
        }
    }
//
//    private void serviceFeeModel(BatchDownloadAgentReconciliationDto batchDownloadAgentReconciliationDto, List<AgentSettlementServiceFeeModel> agentSettlementServiceFeeExport, String agentName, SimpleDateFormat formatter, AgentContractAccountVo agentContractAccountVo, ZipOutputStream zos, List<AgentSettlementBatchExportDto> agentSettlementBatchExportFkTypeKeyList, String excelTemplateName) {
//        Map<String, BigDecimal> currencyAccountMap = new HashMap<>();
//        List<CurrencyAccountModel> currencyAccountModelList = new ArrayList<>();
//        for (AgentSettlementServiceFeeModel agentSettlementServiceFeeModel : agentSettlementServiceFeeExport) {
//            BigDecimal amount = currencyAccountMap.get(agentSettlementServiceFeeModel.getPayablePlanCurrency());
//            if (amount == null) {
//                amount = BigDecimal.ZERO;
//            }
//            amount = amount.add(agentSettlementServiceFeeModel.getOutstanding());
//            currencyAccountMap.put(agentSettlementServiceFeeModel.getPayablePlanCurrency(), amount);
//        }
//
//        for (String currency : currencyAccountMap.keySet()) {
//            BigDecimal amount = currencyAccountMap.get(currency);
//            CurrencyAccountModel currencyAccountModel = new CurrencyAccountModel();
//            currencyAccountModel.setAmount(amount);
//            currencyAccountModel.setCurrencyNum(currency);
//            currencyAccountModelList.add(currencyAccountModel);
//        }
//        StringBuilder fileName = new StringBuilder();
//        //学生数，超过五个的文件就只显示五个学生名 不能太长了
//        int studentNum = 0;
//        fileName.append(agentName).append("-").append(formatter.format(new Date())).append("-");
//        for (AgentSettlementServiceFeeModel agentSettlementServiceFeeModel : agentSettlementServiceFeeExport) {
//            studentNum = studentNum + 1;
//            if (studentNum > 5) {
//                continue;
//            }
//            fileName.append(agentSettlementServiceFeeModel.getStudentName()).append(".");
//        }
//        if (studentNum > 5) {
//            fileName.append("等共").append(studentNum).append("个学生");
//        }
//        int accumulatedRows = agentSettlementServiceFeeExport.size() + currencyAccountModelList.size();
//        Map<String, Object> param = new HashMap<>();
//        param.put("currencyAccountModelList", currencyAccountModelList);
//        param.put("agentName", agentName);
//        param.put("list", agentSettlementServiceFeeExport);
//        param.put("accountInfo", agentContractAccountVo);
//        param.put("userName", SecureUtil.getStaffName());
//        param.put("date", DateUtil.now());
//        fileName.append("-留学服务费汇出表");
//
//        TemplateExcelUtils.commissionSettlementDownLoadExcelZip(fileName.toString(), excelTemplateName, param, zos, 8, 25, 9, 26, accumulatedRows, "Seal.png");
//
//    }

    /**
     * 提交代理确认结算
     *
     * @param agentSettlementBatchExportVoList
     * @Date 12:38 2021/12/23
     * <AUTHOR>
     */
    @Override
    @Transactional
    public void agentConfirmSettlement(List<AgentSettlementBatchExportDto> agentSettlementBatchExportVoList) {
        if (GeneralTool.isEmpty(agentSettlementBatchExportVoList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //提交结算 校验存在 第三步和第四步 时，同一个代理下不能重复提交结算
        List<Long> agentIds = agentSettlementBatchExportVoList.stream().map(AgentSettlementBatchExportDto::getAgentId).collect(Collectors.toList());
        Integer statusSettlement=agentSettlementBatchExportVoList.get(0).getStatusSettlement();
        if(ObjectUtils.isNotEmpty(agentIds) && statusSettlement!=null && statusSettlement == 1){
            for(Long agentId:agentIds){
                int partnerSettmentCount=paymentFormItemMapper.selectstatusSettlement(agentId);
                if(partnerSettmentCount>0){
                    throw new GetServiceException(ResultCode.INVALID_PARAM,
                            LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","代理存在处理中对账单不能提交!"));
                }
            }
        }

        agentConfirmSettlementSubmit(agentSettlementBatchExportVoList);

        if (statusSettlement == 1) {
            List<Long> agentIdList = agentSettlementBatchExportVoList.stream().map(AgentSettlementBatchExportDto::getAgentId).collect(Collectors.toList());
            //删除佣金结算第二步标记
            LambdaQueryWrapper<RPayablePlanSettlementFlag> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(RPayablePlanSettlementFlag::getStatusSettlement, ProjectExtraEnum.SETTLEMENT_IN_PROGRESS.key).in(RPayablePlanSettlementFlag::getFkAgentId, agentIdList);
            payablePlanSettlementFlagMapper.delete(lambdaQueryWrapper);
        }
    }

    /**
     * 取消代理确认结算 (第三步取消按钮)
     *
     * @Date 14:25 2023/2/21
     * <AUTHOR>
     */
    @Override
    @Transactional
    public void cancelAgentConfirmSettlement(List<CancelSettlementDto> cancelSettlementVoList) {
        if (GeneralTool.isEmpty(cancelSettlementVoList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        for (CancelSettlementDto cancelSettlementDto : cancelSettlementVoList) {
            List<PayablePlanSettlementInstallment> payablePlanSettlementInstallments = payablePlanSettlementInstallmentMapper.selectList(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                    .eq(PayablePlanSettlementInstallment::getFkPayablePlanId, cancelSettlementDto.getPayablePlanId())
                    .eq(PayablePlanSettlementInstallment::getFkAgentContractAccountId, cancelSettlementDto.getFkAgentContractAccountId())
                    .eq(PayablePlanSettlementInstallment::getStatusSettlement, ProjectExtraEnum.AGENT_CONFIRMATION.key));

            List<Long> settlementIds = payablePlanSettlementInstallments.stream().map(PayablePlanSettlementInstallment::getId).collect(Collectors.toList());
            BigDecimal amountActual = payablePlanSettlementInstallments.get(0).getAmountActual();
            BigDecimal serviceFeeActual = payablePlanSettlementInstallments.get(0).getServiceFeeActual();
            List<PayablePlanSettlementInstallment> settlementInstallments = payablePlanSettlementInstallmentMapper.selectList(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                    .eq(PayablePlanSettlementInstallment::getFkPayablePlanId, cancelSettlementDto.getPayablePlanId())
                    .eq(PayablePlanSettlementInstallment::getFkAgentContractAccountId, cancelSettlementDto.getFkAgentContractAccountId())
                    .eq(PayablePlanSettlementInstallment::getStatusSettlement, ProjectExtraEnum.SETTLEMENT_IN_PROGRESS.key));
            if (GeneralTool.isNotEmpty(settlementInstallments)) {
                PayablePlanSettlementInstallment settlementInstallment = settlementInstallments.get(0);
                amountActual = amountActual.add(settlementInstallment.getAmountActual());
                serviceFeeActual = serviceFeeActual.add(settlementInstallment.getServiceFeeActual());
                settlementIds.addAll(settlementInstallments.stream().map(PayablePlanSettlementInstallment::getId).collect(Collectors.toList()));
            }
            PayablePlanSettlementInstallment payablePlanSettlementInstallment = new PayablePlanSettlementInstallment();
            payablePlanSettlementInstallment.setAmountActual(amountActual);
            payablePlanSettlementInstallment.setServiceFeeActual(serviceFeeActual);
            payablePlanSettlementInstallment.setRollBackTime(null);
            payablePlanSettlementInstallment.setStatusSettlement(ProjectExtraEnum.SETTLEMENT_IN_PROGRESS.key);
            utilService.setUpdateInfo(payablePlanSettlementInstallment);
            payablePlanSettlementInstallmentMapper.update(payablePlanSettlementInstallment, Wrappers.<PayablePlanSettlementInstallment>lambdaUpdate().in(PayablePlanSettlementInstallment::getId, settlementIds).set(PayablePlanSettlementInstallment::getRollBackTime, null));

            for (PayablePlanSettlementInstallment settlementInstallment : settlementInstallments) {
                PayablePlanSettlementStatus payablePlanSettlementStatus = new PayablePlanSettlementStatus();
                payablePlanSettlementStatus.setFkPayablePlanId(settlementInstallment.getFkPayablePlanId());
                payablePlanSettlementStatus.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
                payablePlanSettlementStatus.setFkPayablePlanSettlementInstallmentId(settlementInstallment.getId());
                utilService.updateUserInfoToEntity(payablePlanSettlementStatus);
                payablePlanSettlementStatusMapper.insert(payablePlanSettlementStatus);
            }

        }
    }

    /**
     * Author Cream
     * Description : //获取佣金结算配置key
     * Date 2023/1/3 12:36
     * Params:
     * Return
     *
     * @return
     */
    @Override
    public ListResponseBo<CompanyConfigInfoDto> getSettlementConfigKey(String configKey) {
        if (StringUtils.isBlank(configKey)) {
            return new ListResponseBo<>(Collections.emptyList());
        }
        ConfigVo configVo = permissionCenterClient.getConfigByKey(configKey).getData();
        if (configVo == null || StringUtils.isBlank(configVo.getValue1())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("MISSING_RELATED_BUSINESS_CONFIGURATION"));
        }
        JSONObject jsonObject = JSON.parseObject(configVo.getValue1());
        Integer iae = Integer.parseInt(jsonObject.getString("IAE"));
        Integer other = Integer.parseInt(jsonObject.getString("OTHER"));
        List<CompanyTreeVo> data = permissionCenterClient.getAllCompanyDto().getData();
        List<CompanyConfigInfoDto> configInfoVos = new ArrayList<>();
        for (CompanyTreeVo datum : data) {
            CompanyConfigInfoDto companyConfigInfoDto = new CompanyConfigInfoDto();
            companyConfigInfoDto.setCompanyId(datum.getId());
            if (datum.getId() == 3) {
                companyConfigInfoDto.setValue(iae);
            } else {
                companyConfigInfoDto.setValue(other);
            }
            configInfoVos.add(companyConfigInfoDto);
        }
        return new ListResponseBo<>(configInfoVos);
    }

    /**
     * 修改实际支付金额
     *
     * @Date 17:28 2022/4/2
     * <AUTHOR>
     */
    @Override
    @Transactional
    public void updateInstallmentAmountActual(List<InstallmentAmountActualUpdateDto> installmentAmountActualUpdateDtos) {
        for (InstallmentAmountActualUpdateDto installmentAmountActualUpdateDto : installmentAmountActualUpdateDtos) {
            if (ProjectExtraEnum.AGENT_CONFIRMATION.key.equals(installmentAmountActualUpdateDto.getStatusSettlement())) {
                if (GeneralTool.isEmpty(installmentAmountActualUpdateDto.getFkAgentContractAccountId())) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("SETTLEMENT_BANK_ACCOUNT_IS_NULL"));
                }
                List<PayablePlanSettlementInstallment> payablePlanSettlementInstallments = payablePlanSettlementInstallmentMapper.selectList(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                        .eq(PayablePlanSettlementInstallment::getFkPayablePlanId, installmentAmountActualUpdateDto.getPayablePlanId())
                        .eq(PayablePlanSettlementInstallment::getStatusSettlement, installmentAmountActualUpdateDto.getStatusSettlement())
                        .eq(PayablePlanSettlementInstallment::getFkAgentContractAccountId, installmentAmountActualUpdateDto.getFkAgentContractAccountId()));
                if (GeneralTool.isEmpty(payablePlanSettlementInstallments)) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("STATUS_SETTLEMENT_ABNORMAL"));
                }
                for (PayablePlanSettlementInstallment payablePlanSettlementInstallment : payablePlanSettlementInstallments) {
                    payablePlanSettlementInstallment.setAmountActual(installmentAmountActualUpdateDto.getAmountActual());
                    payablePlanSettlementInstallment.setServiceFeeActual(installmentAmountActualUpdateDto.getServiceFeeActual());
                    utilService.setUpdateInfo(payablePlanSettlementInstallment);
                    payablePlanSettlementInstallmentMapper.updateById(payablePlanSettlementInstallment);
                }
            } else {
                List<PayablePlanSettlementInstallment> payablePlanSettlementInstallments = payablePlanSettlementInstallmentMapper.selectList(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                        .eq(PayablePlanSettlementInstallment::getFkPayablePlanId, installmentAmountActualUpdateDto.getPayablePlanId())
                        .eq(PayablePlanSettlementInstallment::getStatusSettlement, installmentAmountActualUpdateDto.getStatusSettlement()));
                if (GeneralTool.isEmpty(payablePlanSettlementInstallments)) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("STATUS_SETTLEMENT_ABNORMAL"));
                }
                //均分金额
                BigDecimal amount = installmentAmountActualUpdateDto.getAmountActual().divide(new BigDecimal(payablePlanSettlementInstallments.size()), 2, RoundingMode.HALF_UP);
                //累计金额
                BigDecimal cumulativeAmount = new BigDecimal(0);
                //均分手续费金额
                BigDecimal serviceFeeActualAmount = installmentAmountActualUpdateDto.getServiceFeeActual().divide(new BigDecimal(payablePlanSettlementInstallments.size()), 2, RoundingMode.HALF_UP);
                //累计手续费金额
                BigDecimal serviceFeeActualCumulativeAmount = new BigDecimal(0);

                for (int i = 0; i < payablePlanSettlementInstallments.size(); i++) {
                    PayablePlanSettlementInstallment planSettlementInstallment = payablePlanSettlementInstallments.get(i);
                    if (payablePlanSettlementInstallments.size() == 1) {
                        planSettlementInstallment.setAmountActual(installmentAmountActualUpdateDto.getAmountActual());
                        planSettlementInstallment.setServiceFeeActual(installmentAmountActualUpdateDto.getServiceFeeActual());
                    } else {
                        if (i == payablePlanSettlementInstallments.size() - 1 ) {
                            //最后一个分期的金额 = 总金额 - 之前累加的金额
                            planSettlementInstallment.setAmountActual(installmentAmountActualUpdateDto.getAmountActual().subtract(cumulativeAmount));
                            planSettlementInstallment.setServiceFeeActual(installmentAmountActualUpdateDto.getServiceFeeActual().subtract(serviceFeeActualCumulativeAmount));
                        } else {
                            planSettlementInstallment.setAmountActual(amount);
                            cumulativeAmount = cumulativeAmount.add(amount);
                            planSettlementInstallment.setServiceFeeActual(serviceFeeActualAmount);
                            serviceFeeActualCumulativeAmount = serviceFeeActualCumulativeAmount.add(serviceFeeActualAmount);
                        }
                    }
                    if ((GeneralTool.isEmpty(planSettlementInstallment.getFkReceiptFormItemId()) || 0L == planSettlementInstallment.getFkReceiptFormItemId()) && GeneralTool.isEmpty(planSettlementInstallment.getFkInvoiceReceivablePlanId())) {
                        planSettlementInstallment.setAmountActualInit(installmentAmountActualUpdateDto.getAmountActual());
                        planSettlementInstallment.setServiceFeeActualInit(installmentAmountActualUpdateDto.getServiceFeeActual());
                    }
                    utilService.setUpdateInfo(planSettlementInstallment);
                    payablePlanSettlementInstallmentMapper.updateById(planSettlementInstallment);
                }

            }
        }
    }

    /**
     * 代理佣金结算取消
     *
     * @Date 12:38 2021/12/23
     * <AUTHOR>
     */
    @Override
    @Transactional
    public void cancelSettlement(List<CancelSettlementDto> cancelSettlementVoList) {
        if (GeneralTool.isEmpty(cancelSettlementVoList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<PayablePlanSettlementInstallment> payablePlanSettlementInstallments = payablePlanSettlementInstallmentMapper.getCancelSettlement(cancelSettlementVoList);

        //如果一个应付计划有多条分期表数据回退重新激活 计算会有问题，需要先删分期表再生成
        List<Long> settlementInstallmentIds = payablePlanSettlementInstallments.stream().filter(payablePlanSettlementInstallment -> GeneralTool.isNotEmpty(payablePlanSettlementInstallment.getFkReceiptFormItemId()) && 0L != payablePlanSettlementInstallment.getFkReceiptFormItemId()).map(BaseEntity::getId).collect(Collectors.toList());
        if (GeneralTool.isNotEmpty(settlementInstallmentIds)) {
            payablePlanSettlementInstallmentMapper.deleteBatchIds(settlementInstallmentIds);
        }

        for (PayablePlanSettlementInstallment payablePlanSettlementInstallment : payablePlanSettlementInstallments) {
            payablePlanSettlementInstallment.setStatus(ProjectExtraEnum.INSTALLMENT_UNTREATED.key);
            payablePlanSettlementInstallment.setAmountActual(payablePlanSettlementInstallment.getAmountActualInit());
            payablePlanSettlementInstallment.setServiceFeeActual(payablePlanSettlementInstallment.getServiceFeeActualInit());
            payablePlanSettlementInstallment.setFkAgentContractAccountId(null);
            payablePlanSettlementInstallment.setFkCurrencyTypeNum(null);
            payablePlanSettlementInstallment.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
            utilService.setUpdateInfo(payablePlanSettlementInstallment);
            payablePlanSettlementInstallmentMapper.updateByIdWithNull(payablePlanSettlementInstallment);

            PayablePlanSettlementStatus payablePlanSettlementStatus = new PayablePlanSettlementStatus();
            payablePlanSettlementStatus.setFkPayablePlanId(payablePlanSettlementInstallment.getFkPayablePlanId());
            payablePlanSettlementStatus.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
            payablePlanSettlementStatus.setFkPayablePlanSettlementInstallmentId(payablePlanSettlementInstallment.getId());
            utilService.updateUserInfoToEntity(payablePlanSettlementStatus);
            payablePlanSettlementStatusMapper.insert(payablePlanSettlementStatus);
            if (GeneralTool.isNotEmpty(payablePlanSettlementInstallment.getFkReceiptFormItemId()) && 0L != payablePlanSettlementInstallment.getFkReceiptFormItemId()) {
                receiptFormItemService.activateCommissionSettlement(Collections.singleton(payablePlanSettlementInstallment.getFkReceiptFormItemId()));
            } else if (GeneralTool.isNotEmpty(payablePlanSettlementInstallment.getFkInvoiceReceivablePlanId())) {
                //根据发票应收id,获取预付百分比
                InvoiceReceivablePlan invoiceReceivablePlan = iInvoiceReceivablePlanService.getInvoiceReceivablePlanById(payablePlanSettlementInstallment.getFkInvoiceReceivablePlanId());
                payablePlanSettlementInstallmentMapper.delete(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                        .eq(PayablePlanSettlementInstallment::getFkInvoiceReceivablePlanId, payablePlanSettlementInstallment.getFkInvoiceReceivablePlanId())
                        .eq(PayablePlanSettlementInstallment::getStatusSettlement, ProjectExtraEnum.UNSETTLED.key)
                        .eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_UNTREATED.key)
                        .eq(PayablePlanSettlementInstallment::getId, payablePlanSettlementInstallment.getId()));

                PayablePlan payablePlan = saleCenterClient.getPayableInfoById(payablePlanSettlementStatus.getFkPayablePlanId()).getData();
                PrepaymentButtonHtiDto prepaymentButtonHtiVo = new PrepaymentButtonHtiDto();
                prepaymentButtonHtiVo.setFkInvoiceId(payablePlanSettlementInstallment.getFkInvoiceId());
                prepaymentButtonHtiVo.setFkReceivablePlanId(payablePlan.getFkReceivablePlanId());
                prepaymentButtonHtiVo.setPayInAdvancePercent(invoiceReceivablePlan.getPayInAdvancePercent());
                prepaymentButtonHtiVo.setSubtotal(invoiceReceivablePlan.getAmount());
                prepaymentButtonHtiVo.setInvoiceReceivablePlanRelationId(invoiceReceivablePlan.getId());
                iInvoiceService.prepaymentButtonHti(Collections.singletonList(prepaymentButtonHtiVo));
            }
        }

    }

    /**
     * 财务确认代理佣金结算
     *
     * @Date 12:38 2021/12/23
     * <AUTHOR>
     */
    @Override
    public Boolean financeConfirmSettlement(List<Long> payablePlanIdList) {
        if (GeneralTool.isEmpty(payablePlanIdList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<PayablePlanSettlementAgentAccount> payablePlanSettlementAgentAccounts = payablePlanSettlementAgentAccountMapper.selectList(Wrappers.<PayablePlanSettlementAgentAccount>lambdaQuery()
                .in(PayablePlanSettlementAgentAccount::getFkPayablePlanId, payablePlanIdList).isNull(PayablePlanSettlementAgentAccount::getNumSettlementBatch));
        if (GeneralTool.isEmpty(payablePlanSettlementAgentAccounts) || payablePlanIdList.size() != payablePlanSettlementAgentAccounts.size()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("PAYABLE_PLAN_SETTLEMENT_MARK_NOT_EXISTS"));
        }
        //置空回滚时间
        PayablePlanSettlementInstallment payablePlanSettlementInstallment = new PayablePlanSettlementInstallment();
        utilService.setUpdateInfo(payablePlanSettlementInstallment);
        payablePlanSettlementInstallmentMapper.update(payablePlanSettlementInstallment, Wrappers.<PayablePlanSettlementInstallment>lambdaUpdate()
                .eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_PROCESSING.key)
                .in(PayablePlanSettlementInstallment::getFkPayablePlanId, payablePlanIdList)
                .set(PayablePlanSettlementInstallment::getRollBackTime, null));
        updateStatusSettlement(payablePlanIdList, Collections.singletonList(ProjectExtraEnum.AGENT_CONFIRMATION.key), ProjectExtraEnum.FINANCIAL_RECOGNITION.key, null);
        return true;
//
//        Result<Boolean> result = saleCenterClient.financeConfirmSettlement(payablePlanIdList);
//        if (!result.isSuccess()) {
//            throw new GetServiceException(result.getMessage());
//        }
//        return result.getData();
    }

    /**
     * 应付计划编辑详情回显
     *
     * @Date 12:38 2021/12/23
     * <AUTHOR>
     */
    @Override
    public StudentPlanVo financePlanDetails(Long planId) {
        if (GeneralTool.isEmpty(planId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Result<StudentPlanVo> result = saleCenterClient.financePlanDetails(planId);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        return result.getData();
    }

    /**
     * 批量编辑应付计划
     *
     * @return
     * @Date 12:38 2021/12/23
     * <AUTHOR>
     */
    @Override
    public void batchUpdatePayablePlan(List<PayablePlanDto> payablePlanDtoList) {
        if (GeneralTool.isEmpty(payablePlanDtoList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Result<Boolean> result = saleCenterClient.batchUpdatePayablePlan(payablePlanDtoList);
        if (!result.isSuccess() || result.getData() != true) {
            throw new GetServiceException(result.getMessage());
        }
    }

    /**
     * 财务佣金汇总列表
     *
     * @Date 15:42 2021/12/24
     * <AUTHOR>
     */
    @Override
    public ListResponseBo<CommissionSummaryVo> commissionSummary(SearchBean<CommissionSummaryDto> page) {
        Result<CommissionSummaryPageVo> result = saleCenterClient.commissionSummary(page);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        Page p = new Page();
        if (GeneralTool.isEmpty(result.getData())) {
            return new ListResponseBo<>(null, p);
        }
//        JSONArray jsonArray = JSONArray.fromObject(result.getData());
//        List<CommissionSummaryVo> commissionSummaryVos = JSONArray.toList(jsonArray, new CommissionSummaryVo(), new JsonConfig());
        CommissionSummaryPageVo commissionSummaryPageVo = result.getData();
        List<CommissionSummaryVo> commissionSummaryVos = commissionSummaryPageVo.getCommissionSummaryDtoList();
        p = commissionSummaryPageVo.getPage();
        for (CommissionSummaryVo commissionSummaryVo : commissionSummaryVos) {
            if (GeneralTool.isNotEmpty(commissionSummaryVo.getPayableAmount())) {
                commissionSummaryVo.setPayableAmount(commissionSummaryVo.getPayableAmount().setScale(2, RoundingMode.HALF_UP));
            }
            commissionSummaryVo.setRmbRate(exchangeRateService.getLastExchangeRate(false, commissionSummaryVo.getPlanCurrencyNum(),
                    "CNY").getExchangeRate().setScale(4, BigDecimal.ROUND_HALF_UP));
            commissionSummaryVo.setRmbAmount(commissionSummaryVo.getAmountActual().multiply(commissionSummaryVo.getRmbRate()).setScale(2, RoundingMode.HALF_UP));
            commissionSummaryVo.setTransferPurpose("往来款");
            commissionSummaryVo.setFkTypeKeyName(TableEnum.getValueByKey(commissionSummaryVo.getFkTypeKey(), TableEnum.COMMISSION_SETTLEMENT_TYPE));
        }

        return new ListResponseBo<>(commissionSummaryVos, p);
    }

    /**
     * 提交财务结算汇总
     *
     * @Date 17:24 2021/12/24
     * <AUTHOR>
     */
    @Override
    @Transactional
    public void submitFinancialSettlementSummary(List<SubmitFinancialSettlementSummaryDto> submitFinancialSettlementSummaryDtoList) {
        String num = GetStringUtils.geFinancialSettlementNum();

        for (SubmitFinancialSettlementSummaryDto submitFinancialSettlementSummaryDto : submitFinancialSettlementSummaryDtoList) {
            List<Long> settlementIds = Arrays.stream(submitFinancialSettlementSummaryDto.getSettlementIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
            LambdaUpdateWrapper<PayablePlanSettlementInstallment> wrapper = Wrappers.<PayablePlanSettlementInstallment>lambdaUpdate().in(PayablePlanSettlementInstallment::getId, settlementIds);
            PayablePlanSettlementInstallment payablePlanSettlementInstallment = new PayablePlanSettlementInstallment();
            payablePlanSettlementInstallment.setNumSettlementBatch(num);
            payablePlanSettlementInstallment.setFkAgentIdSettlement(submitFinancialSettlementSummaryDto.getAgentId());
            payablePlanSettlementInstallment.setStatusSettlement(ProjectExtraEnum.FINANCIAL_SUMMARY.key);
            utilService.setUpdateInfo(payablePlanSettlementInstallment);
            payablePlanSettlementInstallmentMapper.update(payablePlanSettlementInstallment, wrapper);

            List<PayablePlanSettlementInstallment> payablePlanSettlementInstallments = payablePlanSettlementInstallmentMapper.selectList(wrapper);
            for (PayablePlanSettlementInstallment planSettlementInstallment : payablePlanSettlementInstallments) {
                PayablePlanSettlementStatus payablePlanSettlementStatus = new PayablePlanSettlementStatus();
                payablePlanSettlementStatus.setNumSettlementBatch(num);
                payablePlanSettlementStatus.setFkPayablePlanId(planSettlementInstallment.getFkPayablePlanId());
                payablePlanSettlementStatus.setFkPayablePlanSettlementInstallmentId(planSettlementInstallment.getId());
                payablePlanSettlementStatus.setStatusSettlement(ProjectExtraEnum.FINANCIAL_SUMMARY.key);
                utilService.updateUserInfoToEntity(payablePlanSettlementStatus);
                payablePlanSettlementStatusMapper.insert(payablePlanSettlementStatus);
            }
        }

        //清空锁定表
        LambdaQueryWrapper<RPayablePlanSettlementFlag> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(RPayablePlanSettlementFlag::getStatusSettlement, ProjectExtraEnum.FINANCIAL_RECOGNITION.key);
        payablePlanSettlementFlagMapper.delete(lambdaQueryWrapper);
    }

    /**
     * 取消财务确认结算
     *
     * @Date 10:22 2021/12/27
     * <AUTHOR>
     */
    @Override
    @Transactional
    public void cancelFinancialConfirmationSettlement(List<CancelFinancialConfirmationSettlementDto> cancelFinancialConfirmationSettlementDtos) {
        List<String> settlementIds = cancelFinancialConfirmationSettlementDtos.stream().map(cancelFinancialConfirmationSettlementVo -> cancelFinancialConfirmationSettlementVo.getSettlementIds().split(",")).flatMap(Arrays::stream).collect(Collectors.toList());
        LambdaQueryWrapper<PayablePlanSettlementInstallment> lambdaQueryWrapper = Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                .in(PayablePlanSettlementInstallment::getId, settlementIds)
                .eq(PayablePlanSettlementInstallment::getStatusSettlement, ProjectExtraEnum.FINANCIAL_RECOGNITION.key);
        List<PayablePlanSettlementInstallment> payablePlanSettlementInstallments = payablePlanSettlementInstallmentMapper.selectList(lambdaQueryWrapper);
        for (PayablePlanSettlementInstallment payablePlanSettlementInstallment : payablePlanSettlementInstallments) {
            PayablePlanSettlementStatus payablePlanSettlementStatus = new PayablePlanSettlementStatus();
            payablePlanSettlementStatus.setFkPayablePlanId(payablePlanSettlementInstallment.getFkPayablePlanId());
            payablePlanSettlementStatus.setFkPayablePlanSettlementInstallmentId(payablePlanSettlementInstallment.getId());
            payablePlanSettlementStatus.setStatusSettlement(ProjectExtraEnum.AGENT_CONFIRMATION.key);
            utilService.updateUserInfoToEntity(payablePlanSettlementStatus);
            payablePlanSettlementStatusMapper.insert(payablePlanSettlementStatus);
        }

        PayablePlanSettlementInstallment payablePlanSettlementInstallment = new PayablePlanSettlementInstallment();
        payablePlanSettlementInstallment.setStatusSettlement(ProjectExtraEnum.AGENT_CONFIRMATION.key);
        utilService.setUpdateInfo(payablePlanSettlementInstallment);
        payablePlanSettlementInstallmentMapper.update(payablePlanSettlementInstallment, lambdaQueryWrapper);
    }

    /**
     * 财务佣金汇总批次列表
     *
     * @Date 15:42 2021/12/24
     * <AUTHOR>
     */
    @Override
    public List<CommissionSummaryBatchVo> commissionSummaryBatchList(CommissionSummaryBatchDto commissionSummaryBatchDto, SearchBean<CommissionSummaryBatchDto> page) {
        if (GeneralTool.isNotEmpty(commissionSummaryBatchDto.getFkCompanyId())) {
            if (!SecureUtil.validateCompany(commissionSummaryBatchDto.getFkCompanyId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }
        if (GeneralTool.isNotEmpty(commissionSummaryBatchDto.getFkCompanyIds())) {
            if (!SecureUtil.validateCompanys(commissionSummaryBatchDto.getFkCompanyIds())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }

        List<CommissionSummaryBatchVo> commissionSummaryBatchVos = new ArrayList<>();
        //先进行第一次筛选
        List<String> numSettlementBatchList = payablePlanSettlementInstallmentMapper.getNumSettlementBatchByVo(commissionSummaryBatchDto);
        if (GeneralTool.isEmpty(numSettlementBatchList)) {
            return commissionSummaryBatchVos;
        }
        List<String> numSettlementBatchNo=payablePlanSettlementInstallmentMapper.selectisExchangeInputNo(numSettlementBatchList);

        IPage<CommissionSummaryBatchVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        commissionSummaryBatchVos = payablePlanSettlementInstallmentMapper.commissionSummaryBatchList(iPage, commissionSummaryBatchDto, numSettlementBatchList);
        page.setAll((int) iPage.getTotal());
        String companyName = permissionCenterClient.getCompanyNameById(commissionSummaryBatchDto.getFkCompanyId()).getData();

        for (CommissionSummaryBatchVo commissionSummaryBatchVo : commissionSummaryBatchVos) {
            //commissionSummaryBatchVo.setFkCompanyName(companyName);
            //查询结算币种 前端要用
            String agentAccountCurrencyTypeNum = payablePlanSettlementInstallmentMapper.selectAgentAccountCurrencyTypeNum(commissionSummaryBatchVo.getNumSettlementBatch());
            commissionSummaryBatchVo.setAgentAccountCurrencyTypeNum(agentAccountCurrencyTypeNum);
            if ("CNY".equals(agentAccountCurrencyTypeNum)) {
                PayablePlanSettlementBatchExchange payablePlanSettlementBatchExchange = payablePlanSettlementBatchExchangeMapper.selectOne(Wrappers.<PayablePlanSettlementBatchExchange>lambdaQuery()
                        .eq(PayablePlanSettlementBatchExchange::getNumSettlementBatch, commissionSummaryBatchVo.getNumSettlementBatch())
                        .eq(PayablePlanSettlementBatchExchange::getFkCurrencyTypeNum, "HKD")
                        .eq(PayablePlanSettlementBatchExchange::getFkCurrencyTypeNumExchange, "CNY"));
                commissionSummaryBatchVo.setFlag(GeneralTool.isNotEmpty(payablePlanSettlementBatchExchange));
            } else {
                commissionSummaryBatchVo.setFlag(true);
            }
            if(GeneralTool.isNotEmpty(numSettlementBatchNo) && numSettlementBatchNo.contains(commissionSummaryBatchVo.getNumSettlementBatch()) ){
                commissionSummaryBatchVo.setIsEexchangeInputFlag(true);
            }else {
                commissionSummaryBatchVo.setIsEexchangeInputFlag(false);
            }

        }
        return commissionSummaryBatchVos;
    }

    /**
     * 财务佣金汇总批次子项列表
     *
     * @Date 15:42 2021/12/24
     * <AUTHOR>
     */
    @Override
    public List<CommissionSummaryBatchItemVo> commissionSummaryBatchItemList(CommissionSummaryBatchDto commissionSummaryBatchDto) {
        if (GeneralTool.isNotEmpty(commissionSummaryBatchDto.getFkCompanyId())) {
            if (!SecureUtil.validateCompany(commissionSummaryBatchDto.getFkCompanyId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }
        if (GeneralTool.isNotEmpty(commissionSummaryBatchDto.getFkCompanyIds())) {
            if (!SecureUtil.validateCompanys(commissionSummaryBatchDto.getFkCompanyIds())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }
        if (GeneralTool.isNotEmpty(commissionSummaryBatchDto.getFkCompanyId())) {
            //兼容单公司条件
            if (!SecureUtil.validateCompany(commissionSummaryBatchDto.getFkCompanyId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }
        List<CommissionSummaryBatchItemVo> commissionSummaryBatchItemList = payablePlanSettlementInstallmentMapper.commissionSummaryBatchItemList(commissionSummaryBatchDto);
        List<PayablePlanSettlementBatchExchange> payablePlanSettlementBatchExchanges = payablePlanSettlementBatchExchangeMapper.selectList(Wrappers.<PayablePlanSettlementBatchExchange>lambdaQuery()
                .eq(PayablePlanSettlementBatchExchange::getNumSettlementBatch, commissionSummaryBatchDto.getNumSettlementBatch()));

        //人民币结算方式专用：港币兑人民币汇率
        BigDecimal hkdExchangeRmbRate = null;
        BigDecimal hkdExchangeRmbServiceFee = null;
        //人民币结算方式专用Map：各币种兑香港汇率
        Map<String, BigDecimal> hkdRateMap = new HashMap<>();
        for (PayablePlanSettlementBatchExchange payablePlanSettlementBatchExchange : payablePlanSettlementBatchExchanges) {
            if ("HKD".equals(payablePlanSettlementBatchExchange.getFkCurrencyTypeNumExchange())) {
                hkdRateMap.put(payablePlanSettlementBatchExchange.getFkCurrencyTypeNum(), payablePlanSettlementBatchExchange.getExchangeRate());
            }
            if ("CNY".equals(payablePlanSettlementBatchExchange.getFkCurrencyTypeNumExchange()) && "HKD".equals(payablePlanSettlementBatchExchange.getFkCurrencyTypeNum())) {
                hkdExchangeRmbRate = payablePlanSettlementBatchExchange.getExchangeRate();
                hkdExchangeRmbServiceFee = payablePlanSettlementBatchExchange.getServiceFee();
            }
        }

        Set<String> currencyNum = commissionSummaryBatchItemList.stream().map(CommissionSummaryBatchItemVo::getAccountCurrencyNum).collect(Collectors.toSet());
        Map<String, String> currencyNamesByNums = currencyTypeService.getCurrencyNamesByNums(currencyNum);

        for (CommissionSummaryBatchItemVo commissionSummaryBatchItemVo : commissionSummaryBatchItemList) {
            //人民币结算
            if ("CNY".equals(commissionSummaryBatchItemVo.getAccountCurrencyNum())) {
                BigDecimal exchangeRate = hkdRateMap.get(commissionSummaryBatchItemVo.getPlanCurrencyNum());
                if (GeneralTool.isNotEmpty(hkdRateMap)) {
                    if (GeneralTool.isNotEmpty(exchangeRate)) {
                        commissionSummaryBatchItemVo.setExchangeHkdRate(exchangeRate.setScale(4, RoundingMode.HALF_UP));
                        commissionSummaryBatchItemVo.setExchangeHkdAmount(commissionSummaryBatchItemVo.getAmountActual().multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP));
                    }
                    if (GeneralTool.isNotEmpty(hkdExchangeRmbServiceFee)) {
                        hkdExchangeRmbServiceFee = hkdExchangeRmbServiceFee.setScale(2, RoundingMode.HALF_UP);
                    }
                    commissionSummaryBatchItemVo.setServiceFee(hkdExchangeRmbServiceFee);
                }
                if (GeneralTool.isNotEmpty(hkdExchangeRmbRate)) {
                    hkdExchangeRmbRate = hkdExchangeRmbRate.setScale(4, RoundingMode.HALF_UP);
                    commissionSummaryBatchItemVo.setHkdExchangeRmbRate(hkdExchangeRmbRate);
                    commissionSummaryBatchItemVo.setHkdExchangeRmbAmount(commissionSummaryBatchItemVo.getAmountActual().multiply(exchangeRate).multiply(hkdExchangeRmbRate).setScale(2, RoundingMode.HALF_UP));
                }
            } else {
                //外币结算
                payablePlanSettlementBatchExchanges = payablePlanSettlementBatchExchangeMapper.selectList(Wrappers.<PayablePlanSettlementBatchExchange>lambdaQuery()
                        .eq(PayablePlanSettlementBatchExchange::getNumSettlementBatch, commissionSummaryBatchDto.getNumSettlementBatch())
                        .eq(PayablePlanSettlementBatchExchange::getFkAgentId, commissionSummaryBatchItemVo.getId())
                        .eq(PayablePlanSettlementBatchExchange::getFkCurrencyTypeNum, commissionSummaryBatchItemVo.getPlanCurrencyNum())
                        .eq(PayablePlanSettlementBatchExchange::getFkTypeKey, commissionSummaryBatchItemVo.getFkTypeKey()));
                if (GeneralTool.isNotEmpty(payablePlanSettlementBatchExchanges)) {
                    PayablePlanSettlementBatchExchange payablePlanSettlementBatchExchange = payablePlanSettlementBatchExchanges.get(0);
                    commissionSummaryBatchItemVo.setExchangeCurrencyNum(payablePlanSettlementBatchExchange.getFkCurrencyTypeNumExchange());
                    if (GeneralTool.isNotEmpty(payablePlanSettlementBatchExchange.getExchangeRate())) {
                        commissionSummaryBatchItemVo.setExchangeRate(payablePlanSettlementBatchExchange.getExchangeRate());
                        commissionSummaryBatchItemVo.setExchangeAmount(commissionSummaryBatchItemVo.getAmountActual().multiply(payablePlanSettlementBatchExchange.getExchangeRate()).setScale(2, RoundingMode.HALF_UP));
                    }
                    BigDecimal serviceFee = payablePlanSettlementBatchExchange.getServiceFee();
                    if (GeneralTool.isNotEmpty(serviceFee)) {
                        serviceFee = serviceFee.setScale(2, RoundingMode.HALF_UP);
                    }
                    commissionSummaryBatchItemVo.setServiceFee(serviceFee);
                }
            }

            commissionSummaryBatchItemVo.setAccountCurrencyNumName(currencyNamesByNums.get(commissionSummaryBatchItemVo.getAccountCurrencyNum()));
            commissionSummaryBatchItemVo.setFkTypeKeyName(TableEnum.getValueByKey(commissionSummaryBatchItemVo.getFkTypeKey(), TableEnum.COMMISSION_SETTLEMENT_TYPE));
            commissionSummaryBatchItemVo.setTransferPurpose("往来款");
        }
        return commissionSummaryBatchItemList;
    }

    /**
     * 财务佣金汇总批次子项列表详情回显
     *
     * @Date 15:42 2021/12/24
     * <AUTHOR>
     */
    @Override
    public CommissionSummaryBatchItemDetailVo commissionSummaryBatchItemDetail(CommissionSummaryBatchDetailDto commissionSummaryBatchDetailDto) {
        CommissionSummaryBatchItemDetailVo commissionSummaryBatchItemDetailVo = new CommissionSummaryBatchItemDetailVo();
        //判断该财务批次是否有绑定过付款单
        List<PayablePlanSettlementInstallment> payablePlanSettlementInstallments = payablePlanSettlementInstallmentMapper.selectList(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                .eq(PayablePlanSettlementInstallment::getNumSettlementBatch, commissionSummaryBatchDetailDto.getNumSettlementBatch())
                .isNotNull(PayablePlanSettlementInstallment::getFkPaymentFormItemId));
        if (GeneralTool.isNotEmpty(payablePlanSettlementInstallments)) {
            commissionSummaryBatchItemDetailVo.setState(4);
            //return commissionSummaryBatchItemDetailVo;
        }

//        List<PayablePlan> payablePlanList = payablePlanMapper.selectList(Wrappers.<PayablePlan>query().lambda().eq(PayablePlan::getNumSettlementBatch,commissionSummaryBatchDetailDto.getNumSettlementBatch()));
        List<CommissionSummaryBatchPayablePlanVo> commissionSummaryBatchPayablePlanVoList = payablePlanSettlementInstallmentMapper.getPayablePlanByNumSettlementBatch(commissionSummaryBatchDetailDto.getNumSettlementBatch());
        //判断是外币结算还是人民币结算
//        PayablePlan payablePlan = commissionSummaryBatchPayablePlanVoList.get(0);
//        List<PayablePlanSettlementAgentAccount> payablePlanSettlementAgentAccounts = payablePlanSettlementAgentAccountMapper.selectList(Wrappers.<PayablePlanSettlementAgentAccount>query().lambda()
//                .eq(PayablePlanSettlementAgentAccount::getFkPayablePlanId, payablePlan.getId()).eq(PayablePlanSettlementAgentAccount::getNumSettlementBatch, commissionSummaryBatchDetailDto.getNumSettlementBatch()));
        //结算币种
        String fkCurrencyTypeNum = commissionSummaryBatchPayablePlanVoList.get(0).getAgentCurrencyTypeNum();

        List<PayablePlanSettlementBatchExchange> payablePlanSettlementBatchExchanges = payablePlanSettlementBatchExchangeMapper.selectList(Wrappers.<PayablePlanSettlementBatchExchange>lambdaQuery()
                .eq(PayablePlanSettlementBatchExchange::getNumSettlementBatch, commissionSummaryBatchDetailDto.getNumSettlementBatch()));

        //人民币结算
        if ("CNY".equals(fkCurrencyTypeNum)) {
            if (GeneralTool.isEmpty(payablePlanSettlementBatchExchanges)) {
                //人民币第一弹框
                List<PayablePlanSettlementBatchExchangeVo> payablePlanSettlementBatchExchangeCommList = new ArrayList<>();
                commissionSummaryBatchItemDetailVo.setState(1);
                //TODO 改过
                // Set<String> currencyTypeSet = commissionSummaryBatchPayablePlanVoList.stream().map(PayablePlan::getFkCurrencyTypeNum).collect(Collectors.toSet());
                Set<String> currencyTypeSet = commissionSummaryBatchPayablePlanVoList.stream().map(CommissionSummaryBatchPayablePlanVo::getFkCurrencyTypeNum).collect(Collectors.toSet());
                for (String currency : currencyTypeSet) {
                    PayablePlanSettlementBatchExchangeVo payablePlanSettlementBatchExchange = new PayablePlanSettlementBatchExchangeVo();
                    payablePlanSettlementBatchExchange.setNumSettlementBatch(commissionSummaryBatchDetailDto.getNumSettlementBatch());
                    payablePlanSettlementBatchExchange.setFkCurrencyTypeNum(currency);
                    payablePlanSettlementBatchExchange.setFkCurrencyTypeNumExchange("HKD");
                    payablePlanSettlementBatchExchangeCommList.add(payablePlanSettlementBatchExchange);
                }
                commissionSummaryBatchItemDetailVo.setCommissionSummaryBatchItemDtoList(payablePlanSettlementBatchExchangeCommList);
            } else {
                boolean flag = true;
                //第一步骤汇率
                List<PayablePlanSettlementBatchExchange> commissionSummaryBatchOneItemDtoList = new ArrayList<>();

                //如果汇率里有港币兑人民币的汇率
                for (PayablePlanSettlementBatchExchange payablePlanSettlementBatchExchange : payablePlanSettlementBatchExchanges) {

                    PayablePlanSettlementBatchExchangeVo payablePlanSettlementBatchExchangeComm = new PayablePlanSettlementBatchExchangeVo();
                    BeanCopyUtils.copyProperties(payablePlanSettlementBatchExchange, payablePlanSettlementBatchExchangeComm);

                    if ("HKD".equals(payablePlanSettlementBatchExchange.getFkCurrencyTypeNum()) &&
                            "CNY".equals(payablePlanSettlementBatchExchange.getFkCurrencyTypeNumExchange())) {
                        commissionSummaryBatchItemDetailVo.setCommissionSummaryBatchItemDtoList(Collections.singletonList(payablePlanSettlementBatchExchangeComm));

                        commissionSummaryBatchItemDetailVo.setState(3);

                        //第3弹框时 上一步骤的第二步骤汇率
                        List<PayablePlanSettlementBatchExchange> commissionSummaryBatchOldItemDtoList = new ArrayList<>();
                        // commissionSummaryBatchOldItemDtoList.add(payablePlanSettlementBatchExchangeComm);
                        commissionSummaryBatchOldItemDtoList.add(payablePlanSettlementBatchExchange);
                        commissionSummaryBatchItemDetailVo.setCommissionSummaryBatchTwoItemDtoList(commissionSummaryBatchOldItemDtoList);
                        flag = false;
                    } else {
                        //TODO 改过
                        //commissionSummaryBatchOneItemDtoList.add(payablePlanSettlementBatchExchangeComm);
                        commissionSummaryBatchOneItemDtoList.add(payablePlanSettlementBatchExchange);
                    }
                }

                if (flag) {
                    commissionSummaryBatchItemDetailVo.setState(2);
                    PayablePlanSettlementBatchExchangeVo payablePlanSettlementBatchExchange = new PayablePlanSettlementBatchExchangeVo();
                    payablePlanSettlementBatchExchange.setFkCurrencyTypeNum("HKD");
                    payablePlanSettlementBatchExchange.setFkCurrencyTypeNumExchange("CNY");
                    commissionSummaryBatchItemDetailVo.setCommissionSummaryBatchItemDtoList(Collections.singletonList(payablePlanSettlementBatchExchange));
                    List<PayablePlanSettlementBatchExchange> payablePlanSettlementBatchExchangeCommList = payablePlanSettlementBatchExchanges.stream().map(studentOfferItemSettlementBatchExchange -> BeanCopyUtils.objClone(studentOfferItemSettlementBatchExchange,
                            PayablePlanSettlementBatchExchange::new)).collect(Collectors.toList());
                    commissionSummaryBatchItemDetailVo.setCommissionSummaryBatchOneItemDtoList(payablePlanSettlementBatchExchangeCommList);
                } else {
                    //第3弹框时 第一步骤汇率
                    commissionSummaryBatchItemDetailVo.setCommissionSummaryBatchOneItemDtoList(commissionSummaryBatchOneItemDtoList);
                }
            }
        } else {
            //外币结算
            //外币第一弹框
            List<PayablePlanSettlementBatchExchangeVo> payablePlanSettlementBatchExchangeCommList = new ArrayList<>();

            if (commissionSummaryBatchItemDetailVo.getState() == 4) {

            } else {
                commissionSummaryBatchItemDetailVo.setState(0);
            }

            List<PayablePlanVo> payablePlanInfoAndAgentIdByNumSettlementBatch = payablePlanSettlementInstallmentMapper.getPayablePlanInfoAndAgentIdByNumSettlementBatch(commissionSummaryBatchDetailDto.getNumSettlementBatch());
            for (PayablePlanVo payablePlanVo : payablePlanInfoAndAgentIdByNumSettlementBatch) {
                PayablePlanSettlementBatchExchangeVo payablePlanSettlementBatchExchange = new PayablePlanSettlementBatchExchangeVo();
                payablePlanSettlementBatchExchange.setNumSettlementBatch(commissionSummaryBatchDetailDto.getNumSettlementBatch());
                payablePlanSettlementBatchExchange.setFkCurrencyTypeNum(payablePlanVo.getFkCurrencyTypeNum());
                payablePlanSettlementBatchExchange.setFkAgentId(payablePlanVo.getFkAgentId());
                payablePlanSettlementBatchExchange.setAgentName(payablePlanVo.getAgentName());
                payablePlanSettlementBatchExchange.setFkTypeKey(payablePlanVo.getFkTypeKey());
                payablePlanSettlementBatchExchange.setFkTypeName(TableEnum.getValue(payablePlanVo.getFkTypeKey()));
                payablePlanSettlementBatchExchange.setFlag(false);
                if (GeneralTool.isNotEmpty(payablePlanSettlementBatchExchanges)) {
                    for (PayablePlanSettlementBatchExchange planSettlementBatchExchange : payablePlanSettlementBatchExchanges) {
                        if (planSettlementBatchExchange.getFkTypeKey().equals(payablePlanSettlementBatchExchange.getFkTypeKey()) &&
                                planSettlementBatchExchange.getFkAgentId().equals(payablePlanSettlementBatchExchange.getFkAgentId()) &&
                                planSettlementBatchExchange.getFkCurrencyTypeNum().equals(payablePlanSettlementBatchExchange.getFkCurrencyTypeNum())) {
                            payablePlanSettlementBatchExchange.setFkCurrencyTypeNumExchange(planSettlementBatchExchange.getFkCurrencyTypeNumExchange());
                            payablePlanSettlementBatchExchange.setExchangeRate(planSettlementBatchExchange.getExchangeRate());
                            payablePlanSettlementBatchExchange.setFlag(true);
                            break;
                        }
                    }
                }
                payablePlanSettlementBatchExchangeCommList.add(payablePlanSettlementBatchExchange);
            }
            commissionSummaryBatchItemDetailVo.setCommissionSummaryBatchItemDtoList(payablePlanSettlementBatchExchangeCommList);
        }
        return commissionSummaryBatchItemDetailVo;
    }

    /**
     * 保存结算汇总表汇率
     *
     * @param payablePlanSettlementBatchExchangeDtos
     * @Date 10:10 2021/12/28
     * <AUTHOR>
     */
    @Override
    public void saveExchangeRate(List<PayablePlanSettlementBatchExchangeDto> payablePlanSettlementBatchExchangeDtos) {
        PayablePlanSettlementBatchExchangeDto oneVo = payablePlanSettlementBatchExchangeDtos.get(0);
        LambdaQueryWrapper<PayablePlanSettlementBatchExchange> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PayablePlanSettlementBatchExchange::getNumSettlementBatch, oneVo.getNumSettlementBatch());
        if (oneVo.getState() == 2) {
            lambdaQueryWrapper.eq(PayablePlanSettlementBatchExchange::getFkCurrencyTypeNum, oneVo.getFkCurrencyTypeNum());
            lambdaQueryWrapper.eq(PayablePlanSettlementBatchExchange::getFkCurrencyTypeNumExchange, oneVo.getFkCurrencyTypeNumExchange());
        }
        payablePlanSettlementBatchExchangeMapper.delete(lambdaQueryWrapper);

        for (PayablePlanSettlementBatchExchangeDto payablePlanSettlementBatchExchangeDto : payablePlanSettlementBatchExchangeDtos) {
            PayablePlanSettlementBatchExchange payablePlanSettlementBatchExchange = new PayablePlanSettlementBatchExchange();
            BeanCopyUtils.copyProperties(payablePlanSettlementBatchExchangeDto, payablePlanSettlementBatchExchange);
            utilService.updateUserInfoToEntity(payablePlanSettlementBatchExchange);
            payablePlanSettlementBatchExchangeMapper.insert(payablePlanSettlementBatchExchange);
        }
    }

    @Override
    public void saveIsExchangeInput(List<PayablePlanSettlementBatchExchangeDto> payablePlanSettlementBatchExchangeDtos) {
        int complete=1;
        if(ObjectUtils.isNotEmpty(payablePlanSettlementBatchExchangeDtos)){
            Boolean flagComplete=payablePlanSettlementBatchExchangeDtos.get(0).getFlagComplete();
            if(!flagComplete){
                complete=0;
            }

        }
        payablePlanSettlementInstallmentMapper.saveIsExchangeInput(payablePlanSettlementBatchExchangeDtos,complete);
    }


    /**
     * 保存结算汇总表汇率并导出Excel
     *
     * @param
     * @Date 10:10 2021/12/28
     * <AUTHOR>
     */
    @Override
    public void commissionSummaryBatchItemExcel(CommissionSummaryBatchDto commissionSummaryBatchDto, HttpServletResponse response) {
        List<CommissionSummaryBatchItemVo> commissionSummaryBatchItemListResult = commissionSummaryBatchItemList(commissionSummaryBatchDto);
        List<CommissionSummaryBatchItemVo> commissionSummaryBatchItemList = BeanCopyUtils.copyListProperties(commissionSummaryBatchItemListResult, CommissionSummaryBatchItemVo::new);
        for (int i = 0; i < commissionSummaryBatchItemList.size(); i++) {
            CommissionSummaryBatchItemVo commissionSummaryBatchItemVo = commissionSummaryBatchItemList.get(i);
            commissionSummaryBatchItemVo.setNumber((long) i + 1);
        }
        Map<String, BigDecimal> currencyAccountMap = new HashMap<>();
        List<CurrencyAccountModel> currencyAccountModelList = new ArrayList<>();
        for (CommissionSummaryBatchItemVo commissionSummaryBatchItemVo : commissionSummaryBatchItemList) {
            String currencyNum;
            BigDecimal amount;
            if ("CNY".equals(commissionSummaryBatchItemVo.getAccountCurrencyNum())) {
                currencyNum = commissionSummaryBatchItemVo.getPlanCurrencyNum();
                amount = commissionSummaryBatchItemVo.getAmountActual().setScale(2, RoundingMode.HALF_UP);
            } else if (GeneralTool.isNotEmpty(commissionSummaryBatchItemVo.getExchangeCurrencyNum())) {
                currencyNum = commissionSummaryBatchItemVo.getExchangeCurrencyNum();
                amount = commissionSummaryBatchItemVo.getExchangeAmount().setScale(2, RoundingMode.HALF_UP);
            } else {
                currencyNum = commissionSummaryBatchItemVo.getPlanCurrencyNum();
                amount = commissionSummaryBatchItemVo.getAmountActual().setScale(2, RoundingMode.HALF_UP);
            }
            BigDecimal totalAmount = currencyAccountMap.get(currencyNum);
            if (totalAmount == null) {
                totalAmount = BigDecimal.ZERO;
            }
            totalAmount = totalAmount.add(amount).setScale(2, RoundingMode.HALF_UP);
            currencyAccountMap.put(currencyNum, totalAmount);
        }

        for (String currency : currencyAccountMap.keySet()) {
            BigDecimal amount = currencyAccountMap.get(currency);
            CurrencyAccountModel currencyAccountModel = new CurrencyAccountModel();
            currencyAccountModel.setAmount(amount.setScale(2, RoundingMode.HALF_UP));
            currencyAccountModel.setCurrencyNum(currency);
            currencyAccountModelList.add(currencyAccountModel);
        }

        Map<String, Object> param = new HashMap<>();
        param.put("currencyAccountModelList", currencyAccountModelList);
        param.put("list", commissionSummaryBatchItemList);
        TemplateExcelUtils.downLoadExcel("summary", ExcelTypeEnum.SUMMARY.excelTemplateName, param, response);
    }

    @Override
    public void commissionSummaryBatchItemIfile(CommissionSummaryBatchDto commissionSummaryBatchDto, HttpServletResponse response) {
        //返回的list
        String numSettlementBatch = commissionSummaryBatchDto.getNumSettlementBatch();
        //List<CurrencyAccountModel> currencyAccountModelList = new ArrayList<>();
        //int totalLine = currencyAccountModelList.size();
        String mydate = new SimpleDateFormat("yyyy/MM/dd").format(Calendar.getInstance().getTime());
        StringBuilder builder = new StringBuilder();
        String mytime = new SimpleDateFormat("HH:mm:ss").format(Calendar.getInstance().getTime());
        String random = UUID.randomUUID().toString().replaceAll("-", "").substring(0, 10);

        //批次数据  xls用
        List<IFileInfoVo> commissionSummaryBatchItemList = iFileExcelInfo(numSettlementBatch);

        //按代理、币种分组数据 txt数据,pdf数据
        List<IFileInfoVo> ifileDataGroupByCurrencyLsit = iFileGroupByCurrencyInfo(numSettlementBatch);
        int totalLine = ifileDataGroupByCurrencyLsit.size();
        //按照代理和币种分组之后获取下标
        List<Map<String, Object>> listAll = new ArrayList<>();
        for (int i = 0; i < ifileDataGroupByCurrencyLsit.size(); i++) {
            Map<String, Object> map = new HashMap<>();
            map.put("agentid", ifileDataGroupByCurrencyLsit.get(i).getAgentId());
            map.put("currency", ifileDataGroupByCurrencyLsit.get(i).getFkCurrencyTypeNum());
            map.put("index", i + 1);
            listAll.add(map);
        }

        //按币种，代理分组数据 xls数据(待定)
        //List<IFileInfoVo> ifileDataGroupByAgidAndCurrencyLsit = saleCenterClient.iFileGroupByAgidAndCurrencyInfo(numSettlementBatch);

        //Batch部分，循环代理
        //定义统一的时间格式
        //Date self_date = null;
        //self_date = new SimpleDateFormat("yyyy/MM/dd").parse(mydate);
        //String mydate2 = new SimpleDateFormat("yyyyMMdd").format(self_date);
        String mydate2 = new SimpleDateFormat("yyyyMMdd").format(Calendar.getInstance().getTime());

        builder.append("IFH,")
                .append("IFILE,")
                .append("CSV,")
                .append(",")//HSBC Connect Customer ID
                .append("HKHBAPGHK411760895").append(",")//客户id HSBCnet Customer ID
                .append("IFLEXAMP").append(random).append(",")//文件参考,重复检查，12个月内唯一
                .append(mydate).append(",")
                .append(mytime).append(",")
                .append("P").append(",")//授权模式
                .append("1.0").append(",")//版本号 version
                .append(totalLine * 2 + 1).append(",")//总行数包括header
                .append("U801")
                .append("\n");
        //总导出金额
        String outFeeTotal = null;
        for (int i = 0; i < ifileDataGroupByCurrencyLsit.size(); i++) {
            IFileInfoVo iFileInfo = ifileDataGroupByCurrencyLsit.get(i);
            String type = null;
            if ("HK".equals(iFileInfo.getAreaCountryCode())) {
                //本地转账 当为香港账号时则为本地转账
                type = "LTR";
            } else {
                //国外转账
                type = "FTR";
            }

            String exchangeCurrencyNum = "";
            BigDecimal getExchangeAmount = null;

            if (!"".equals(iFileInfo.getExchangeCurrencyNum())) {//折算币种
                getExchangeAmount = iFileInfo.getExchangeAmount().setScale(2, BigDecimal.ROUND_HALF_UP);
                exchangeCurrencyNum = iFileInfo.getExchangeCurrencyNum();
            } else {
                getExchangeAmount = iFileInfo.getPayableAmount();
                exchangeCurrencyNum = iFileInfo.getFkCurrencyTypeNum();
            }

            builder.append("BATHDR,");
            //本地转账 当为香港账号时则为本地转账
            builder.append(type).append(",")
                    //builder.append("FTR").append(",")
                    .append(1).append(",")//Number of LIN segments  有多少个second
                    .append(",")//Batch Reference  Must provide comma delimiter even if blank value is provided
                    .append(",")//filter
                    .append(",")//filter
                    .append(",")//Regulatory Reporting (Batch Level
                    .append(",")//filter
                    .append(",")//filter
                    .append("@1ST@").append(",")//Constant Eye Catcher
                    .append(mydate2).append(",")//第一方记录起息日 (未确定)
                    //.append("741432041838SA").append(iFileInfo.getFkCurrencyTypeNum()).append(",")//第一方账户.
                    //.append(iFileInfo.getFkCurrencyTypeNum()).append(",")//第一方交易货币 Transaction Currency
                    //.append(iFileInfo.getPayableAmount()).append(",")//金额  Transaction Amount
                    .append("741432041838SA").append(iFileInfo.getFkCurrencyTypeNum()).append(",")//第一方账户.
                    .append(iFileInfo.getFkCurrencyTypeNum()).append(",")//第一方交易货币 Transaction Currency
                    .append(iFileInfo.getPayableAmount().setScale(2, BigDecimal.ROUND_HALF_UP)).append(",")//金额  Transaction Amount
                    .append(",")//Template Mode
                    .append(",")//Batch Template ID
                    .append("HK").append(",")//第一方借方帐户国家/地区 固定HK
                    .append(",")//First Party Account Institution
                    .append(",")//第一方借方帐户货币 (不确定)  First Party Account Currency*
                    .append(",")//Payment Amount in Debit account currency*
                    .append("Global Education Alliance Limited").append(",")//第一方订购方名称 First Party Name
                    .append("Flat B 15 F Block 1, King Wing Plaza 1 3 On Kwan Street, Shatin NT").append(",")//第一方订购方地址
                    .append(",")//First Party Information Line 4
                    .append(",")//Payment Code
                    .append(",")//Reference Line 1
                    .append(",")//Reference Line 2
                    .append(",")//Reference Line 1
                    .append(",")// Ordering Party Account / ID
                    .append("\n");

            builder.append("SECPTY,");
            //使用国内账户
            /*if ("CNY".equals(iFileInfo.getFkCurrencyTypeNum())) {
                builder.append(iFileInfo.getBankAccountNum()).append(iFileInfo.getBankAccountNum())//第二方收款人帐号
                        .append(iFileInfo.getBankName().replaceAll("\r|\n|,", " ")).append(",")//第二方收款人姓名
                        .append(",")//Second Party Identifier
                        .append(",")//Beneficiary Bank Number / ID
                        .append(",")//Beneficiary Branch Number
                        .append(",")//Transaction Code
                        .append(iFileInfo.getPayableAmount().multiply(exchangeRateService.getLastExchangeRate(false, iFileInfo.getFkCurrencyTypeNum(), "CNY").getExchangeRate()).toString()).append(",")//第二方交易金额 Second Party Transaction Amount
                        .append(mydate2).append(",")// 标记//Entry Value Date
                        .append(",")//Second Party Reference
                        .append(",")//第二方受益人地址
                        .append(",")//第二方受益人地址
                        .append(",")//第二方受益人地址
                        .append(",")//第二方受益人地址
                        .append("N").append(",")//Advice Indicator
                        .append("N").append(",")//WHT Indicator
                        .append(",")// 标记
                        .append(",")// 标记
                        .append(",")// 标记
                        .append(",")// 标记
                        .append(",")// 标记
                        .append("@HVP@").append(",")//Constant Eye Catcher
                        .append(",")//First Party Reference
                        .append(",")//Payment Currency
                        .append(",")//emplate ID
                        .append(",")//Exchange Rate
                        .append(",")//第二方中介银行 Intermediary Institution Bank ID/SWIFT Address Code
                        .append(",")//Intermediary Institution Bank Name
                        .append(",")//Intermediary Institution Bank Name 1
                        .append(",")//Intermediary Institution Bank Name 2
                        .append(",")//Intermediary Institution Bank Name 3
                        .append(",")//Intermediary Institution Bank ID/SWIFT Address
                        .append(",")//Intermediary Institution Account Number
                        .append(",");//标记
                //当只有swifcode时
                if (iFileInfo.getSwiftCode() != null) {
                    builder.append("SWF").append(",")//Beneficiary Bank ID/SWIFT Address Code  36
                            .append(iFileInfo.getSwiftCode()).append(iFileInfo.getSwiftCode())  //36
                            .append(",")//  fbankname_chil
                            .append(",")//  fbankaddress
                            .append(",")//  Beneficiary Bank Address Line 2
                            .append(",")//  Beneficiary Bank Address Line 3
                            .append(",")//  Beneficiary Bank Address Line 4
                            .append(iFileInfo.getAgentAreaCountryId());//第二方受益人银行国家 Beneficiary Bank Country7
                } else {
                    //当swiftcode为空时 Item 37(Beneficiary Bank name) is not required if Item 35&36 are provided.
                    builder.append(",")////Beneficiary Bank ID/SWIFT Address Code
                            .append(",")//swiftcode
                            .append(iFileInfo.getBankBranchName().replaceAll("\r|\n|,", " ")).append(addSpot(iFileInfo.getBankBranchName()))//第二方收款人银行名称   Beneficiary Bank Name
                            .append(iFileInfo.getAddress().replaceAll("\r|\n|,", " "))//Beneficiary Bank Address Line 1
                            .append(iFileInfo.getAgentAreaCityName().replaceAll("\r|\n|,", " ")).append(addSpot(iFileInfo.getAgentAreaCityName()))//Beneficiary Bank Address Line 1
                            //修改
                            //.append(mapS.get("bankaddress3").toString().replaceAll("\r|\n|,", " ")).append(addSpot(mapS.get("bankaddress3").toString()))//Beneficiary Bank Address Line 1
                            //.append( mapS.get("bankaddress4").toString().replaceAll("\r|\n|,", " ")).append(addSpot(mapS.get("bankaddress4").toString()))//Beneficiary Bank Address Line 1
                            .append(iFileInfo.getAddress().replaceAll("\r|\n|,", " ")).append(addSpot(iFileInfo.getAddress()))//Beneficiary Bank Address Line 1
                            .append(iFileInfo.getAddress().replaceAll("\r|\n|,", " ")).append(addSpot(iFileInfo.getAddress()))//Beneficiary Bank Address Line 1
                            .append(",")//Beneficiary Bank Address Line 1
                            .append(",");//第二方受益人银行国家 Beneficiary Bank Country
                }
            } else {*/
            //使用国外账户
            builder.append(iFileInfo.getBankAccountNum()).append(",")//第二方收款人帐号
                    .append(iFileInfo.getBankAccount().replaceAll("\r|\n|,", " ")).append(",")//第二方收款人姓名
                    .append(",")//Second Party Identifier
                    .append(",")//Beneficiary Bank Number / ID
                    .append(",")//Beneficiary Branch Number
                    .append(",")//Transaction Code
                    .append(iFileInfo.getPayableAmount().setScale(2, BigDecimal.ROUND_HALF_UP)).append(",")//第二方交易金额 Second Party Transaction Amount
                    .append(mydate2).append(",")// 标记//Entry Value Date
                    .append(",")//Second Party Reference
                    .append(",")//第二方受益人地址
                    .append(",")//第二方受益人地址
                    .append(",")//第二方受益人地址
                    .append(",")//第二方受益人地址
                    .append("N").append(",")//Advice Indicator
                    .append("N").append(",")//WHT Indicator
                    .append(",")// 标记
                    .append(",")// 标记
                    .append(",")// 标记
                    .append(",")// 标记
                    .append(",")// 标记
                    .append("@HVP@").append(",")//Constant Eye Catcher
                    .append(",")//First Party Reference
                    .append(",")//Payment Currency
                    .append(",")//Template ID
                    .append(",")//Exchange Rate
                    .append(",")//第二方中介银行 Intermediary Institution Bank ID/SWIFT Address Code
                    .append(",")//Intermediary Institution Bank Name
                    .append(",")//Intermediary Institution Bank Name 1
                    .append(",")//Intermediary Institution Bank Name 2
                    .append(",")//Intermediary Institution Bank Name 3
                    .append(",")//Intermediary Institution Bank ID/SWIFT Address
                    .append(",")//Intermediary Institution Account Number
                    .append(",");//标记
            if (GeneralTool.isNotBlank(iFileInfo.getSwiftCode())) {
                //区分swfitcode 和 BSB 码
                String bankCode = iFileInfo.getSwiftCode();
                Map<String, String> addressCodeMap = getBankCode(bankCode);
                builder.append(addressCodeMap.get("addressCode")).append(",")//Beneficiary Bank ID/SWIFT Address Code8
                        .append(addressCodeMap.get("address"))
                        .append(addSpot(iFileInfo.getSwiftCode()))
                        .append(",")//  fbankname_chil
                        .append(",")//  fbankaddress
                        .append(",")//  Beneficiary Bank Address Line 2
                        .append(",")//  Beneficiary Bank Address Line 3
                        .append(",")//  Beneficiary Bank Address Line 4
                        .append(iFileInfo.getAreaCountryCode()).append(",");//第二方受益人银行国家 Beneficiary Bank Country7
            } else {
                //当swiftcode为空时 Item 37(Beneficiary Bank name) is not required if Item 35&36 are provided.
                builder.append(",")//标记Beneficiary Bank ID/SWIFT Address Code
                        .append(",")//标记 swiftcode
                        /*.append(objToStr(agentInfoMap.get("fbankname_chil")).replaceAll("\r|\n|,", " ")).append(addSpot(agentInfoMap.get("fbankname_chil")))//第二方收款人银行名称   Beneficiary Bank Name
                        .append(objToStr(agentInfoMap.get("fbankaddress")).replaceAll("\r|\n|,", " ")).append(addSpot(agentInfoMap.get("fbankaddress")))//Beneficiary Bank Address Line
                        .append(objToStr(agentInfoMap.get("fbankaddress2")).replaceAll("\r|\n|,", " ")).append(addSpot(agentInfoMap.get("fbankaddress2")))//Beneficiary Bank Address Line
                        .append(objToStr(agentInfoMap.get("fbankaddress3")).replaceAll("\r|\n|,", " ")).append(addSpot(agentInfoMap.get("fbankaddress3")))//Beneficiary Bank Address Line
                        .append(objToStr(agentInfoMap.get("fbankaddress4")).replaceAll("\r|\n|,", " ")).append(addSpot(agentInfoMap.get("fbankaddress4")))//Beneficiary Bank Address Line*/
                        .append(null2String(iFileInfo.getBankBranchName()).replaceAll("\r|\n|,", " ")).append(addSpot(iFileInfo.getBankBranchName()))//第二方收款人银行名称   Beneficiary Bank Name
                        .append(null2String(iFileInfo.getAddress()).replaceAll("\r|\n|,", " ")).append(addSpot(iFileInfo.getAddress()))//Beneficiary Bank Address Line 1
                        .append(null2String(iFileInfo.getAgentAreaCityName()).replaceAll("\r|\n|,", " ")).append(addSpot(iFileInfo.getAgentAreaCityName()))//Beneficiary Bank Address Line 1
                        .append(null2String(iFileInfo.getAddress()).replaceAll("\r|\n|,", " ")).append(addSpot(iFileInfo.getAddress()))//Beneficiary Bank Address Line 1
                        .append(null2String(iFileInfo.getAddress()).replaceAll("\r|\n|,", " ")).append(addSpot(iFileInfo.getAddress()))//Beneficiary Bank Address Line 1
                        .append(null2String(iFileInfo.getAgentAreaCountryNum())).append(",");//第二方受益人银行国家 Beneficiary Bank Country
            }
            /*}*/
            String nature = iFileInfo.getNature();
            String personalAccount = "";
            if ("个人".equals(nature) || "工作室".equals(nature)) {
                personalAccount = "SERVICE TRADE";
            } else {
                personalAccount = "";
            }
            builder.append(",")//Beneficiary CHIPS UID
                    .append(personalAccount).append(",")//付费凭证
                    .append(",")//Payment Details Line 2
                    .append(",")//Payment Details Line 3
                    .append(",")//Payment Details Line 4
                    .append("BEN")
                    .append(",")//Sender Charges
                    .append(",")//Charges Account
                    .append(",")//Instruction Code Line 1
                    .append(",")//Instruction Code Line 2
                    .append(",")//Instruction Code Line 3
                    .append(",")//Instruction Code Line 4
                    .append(",")//Bank to Bank Information Line 1
                    //.append(transferPurpose).append(",")//Bank to Bank Information Line 1
                    .append(",")//Bank to Bank Information Line 2
                    .append(",")//Bank to Bank Information Line 3
                    .append(",")//Bank to Bank Information Line 4
                    .append(",")//Bank to Bank Information Line 5
                    .append(",")//1st Exchange Contract Number
                    .append(",")//1st Exchange Contract Amount
                    .append(",")//2nd Exchange Contract Number
                    .append(",")//2nd Exchange Contract Number
                    .append(",")//2nd Exchange Contract Amount
                    .append(",")//Regulatory Reporting Line 1
                    .append(",")//Regulatory Reporting Line 2
                    .append(",")//Regulatory Reporting Line 3
                    .append(",")//Advise By
                    .append(",")//Advise By Number
                    .append(",")//Intermediary Bank Country
                    .append("\n");
        }
        DecimalFormat df = new DecimalFormat("#0.00");

        //创建表格
        HSSFWorkbook wb = new HSSFWorkbook();

        //生成.xls文件
        HSSFSheet sheet = wb.createSheet("账单BILL");
        HSSFRow row1 = sheet.createRow(0);

        String[] cellnane = {"序号", "导出时间", "合作方", "收款账号", "收款户名", "收款银行", "银行支行", "是否兴业账号", "(外币)开户银行", "(外币)银行支行", "(外币)账户名", "(外币)账户号码", "(外币)银行地址", "汇入地址",
                "原金额", "原币种", "转账金额", "支付币种", "汇率", "代收代付金额", "学生名称", "SWIFTCODE", "转账用途", "个人账户"};

        for (int i = 0; i < cellnane.length; i++) {//第一行
            row1.createCell(i).setCellValue(cellnane[i]);
        }
        for (int i = 0; i < commissionSummaryBatchItemList.size(); i++) {
            String index = "";
            IFileInfoVo ifDto = commissionSummaryBatchItemList.get(i);
            for (int j = 0; j <= listAll.size() - 1; j++) {
                //有折算币种
                if (!"".equals(ifDto.getExchangeCurrencyNum())) {
                    if (listAll.get(j).get("agentid").equals(ifDto.getAgentId()) && listAll.get(j).get("currency").equals(ifDto.getExchangeCurrencyNum())) {
                        index = listAll.get(j).get("index").toString();
                    }
                } else {
                    if (listAll.get(j).get("agentid").equals(ifDto.getAgentId()) && listAll.get(j).get("currency").equals(ifDto.getFkCurrencyTypeNum())) {
                        index = listAll.get(j).get("index").toString();
                    }
                }
            }
            HSSFRow row2 = sheet.createRow(i + 1);
            row2.createCell(0).setCellValue(index);//序号
            row2.createCell(1).setCellValue(mydate);
            row2.createCell(2).setCellValue(ifDto.getAgentName());
            row2.createCell(3).setCellValue(ifDto.getCBankAccountNum());//收款账号
            row2.createCell(4).setCellValue(ifDto.getCBankAccount());//收款户名
            row2.createCell(5).setCellValue(ifDto.getCBankName());//收款银行
            row2.createCell(6).setCellValue(ifDto.getCBankBranchName());//银行支行
            row2.createCell(7).setCellValue("否");
            row2.createCell(8).setCellValue(ifDto.getBankName());//(外币)开户银行
            row2.createCell(9).setCellValue(ifDto.getBankBranchName());//(外币)银行支行
            row2.createCell(10).setCellValue(ifDto.getBankAccount());//(外币)账户名
            row2.createCell(11).setCellValue(ifDto.getBankAccountNum());//(外币)账户号码
            row2.createCell(12).setCellValue(ifDto.getBankAddress());//(外币)银行地址
            row2.createCell(13).setCellValue(ifDto.getBankAddress());//汇入地址
            row2.createCell(14).setCellValue(ifDto.getPayableAmount().toString());//原金额
            row2.createCell(15).setCellValue(ifDto.getFkCurrencyTypeNum());//原币种
            //处理    16、17、18行
            if (!"".equals(ifDto.getExchangeCurrencyNum())) {
                row2.createCell(16).setCellValue((ifDto.getPayableAmount().multiply(ifDto.getExchangeRate())).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                row2.createCell(17).setCellValue(ifDto.getExchangeCurrencyNum());
                row2.createCell(18).setCellValue(ifDto.getExchangeRate().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            } else {
                row2.createCell(16).setCellValue(ifDto.getPayableAmount().toString());
                row2.createCell(17).setCellValue(ifDto.getFkCurrencyTypeNum());
                row2.createCell(18).setCellValue(1);
            }

            row2.createCell(19).setCellValue(0);

            /*String[] currency = ifDto.getPayAmount().split(";");
            //总数
            BigDecimal outFeeTotalBig = new BigDecimal("0");
            if(currency.length>0){
                for(int j=0;j<=currency.length-1;j++){
                    String[] payList = currency[j].split(",");
                    String currencyPay = payList[0];
                    String currencyAcount = payList[1];
                    System.out.println(currencyPay+currencyAcount);
                }
            }*/
            //row2.createCell(20).setCellValue(df.format(ifDto.getPayableAmount().multiply(exchangeRateService.getLastExchangeRate(false, ifDto.getFkCurrencyTypeNum(), "CNY").getExchangeRate())));
            row2.createCell(20).setCellValue(ifDto.getStudentName());
            row2.createCell(21).setCellValue(ifDto.getSwiftCode());
            row2.createCell(22).setCellValue("");
            if ("个人".equals(ifDto.getNature()) || "工作室".equals(ifDto.getNature())) {
                row2.createCell(23).setCellValue("是");
            } else {
                row2.createCell(23).setCellValue("否");
            }

        }

        try {
            //输出流
            OutputStream output = null;
            ZipOutputStream zipOutputStream = null;
            //输出文件名
            String fillname = "HTI_IFILE_" + mydate2;
            //需要重置，否则报错
            response.reset();
            //获取输出流
            output = response.getOutputStream();
            //实例化输出流
            zipOutputStream = new ZipOutputStream(output);
            //设置响应头
            response.setContentType("application/octet-stream; charset=utf-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + fillname + ".zip");

            //创建压缩文件
            ZipEntry z = new ZipEntry(fillname + ".xls");
            zipOutputStream.putNextEntry(z);
            wb.write(zipOutputStream);
            zipOutputStream.flush();

            ZipEntry z1 = new ZipEntry(fillname + ".txt");
            zipOutputStream.putNextEntry(z1);
            //写入一个压缩文件
            zipOutputStream.write(builder.toString().getBytes("UTF-8"));

            for (int i = 0; i < ifileDataGroupByCurrencyLsit.size(); i++) {
                //实例化PDF回单
                Map<String, String> dynamicInfoMap = null;
                Map agentInfoMap = new HashMap();
                agentInfoMap.put("countrycode", ifileDataGroupByCurrencyLsit.get(i).getAreaCountryCode());

                dynamicInfoMap = new HashMap<String, String>();
                dynamicInfoMap.put("mydate", mydate);
                dynamicInfoMap.put("mydate2", mydate2);
                dynamicInfoMap.put("faccountNoPdf", ifileDataGroupByCurrencyLsit.get(i).getBankAccountNum());
                dynamicInfoMap.put("faccountNamePdf", ifileDataGroupByCurrencyLsit.get(i).getBankAccount());
                //2022-5-19注释
                /*if (!"".equals(ifileDataGroupByCurrencyLsit.get(i).getExchangeCurrencyNum())) {
                    dynamicInfoMap.put("outFeeTotal", ifileDataGroupByCurrencyLsit.get(i).getExchangeAmount().toString());
                    dynamicInfoMap.put("outCurrencyPdf", ifileDataGroupByCurrencyLsit.get(i).getExchangeCurrencyNum());
                } else {
                    dynamicInfoMap.put("outFeeTotal", ifileDataGroupByCurrencyLsit.get(i).getPayableAmount().toString());
                    dynamicInfoMap.put("outCurrencyPdf", ifileDataGroupByCurrencyLsit.get(i).getFkCurrencyTypeNum());
                }*/
                dynamicInfoMap.put("outFeeTotal", ifileDataGroupByCurrencyLsit.get(i).getPayableAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                dynamicInfoMap.put("outCurrencyPdf", ifileDataGroupByCurrencyLsit.get(i).getFkCurrencyTypeNum());


                //下标获取
                String index = "";
                for (int j = 0; j <= listAll.size() - 1; j++) {
                    if (listAll.get(j).get("agentid").equals(ifileDataGroupByCurrencyLsit.get(i).getAgentId()) && listAll.get(j).get("currency").equals(ifileDataGroupByCurrencyLsit.get(i).getFkCurrencyTypeNum())) {
                        index = listAll.get(j).get("index").toString();
                    }
                }
                //序号
                dynamicInfoMap.put("customSerial", index);
                ZipEntry z2 = new ZipEntry(fillname + "_" + ifileDataGroupByCurrencyLsit.get(i).getAgentName() + "_" + index + ".pdf");
                zipOutputStream.putNextEntry(z2);
                initAgencyPDF(agentInfoMap, dynamicInfoMap, zipOutputStream);
            }

            //关闭
            zipOutputStream.flush();
            zipOutputStream.close();
            output.flush();
            output.close();
        } catch (Exception e) {

        }
    }

    /**
     * 获取iFile Excel信息
     *
     * @Date 14:14 2022/3/5
     * <AUTHOR>
     */
    private List<IFileInfoVo> iFileExcelInfo(String numSettlementBatch) {
        List<IFileInfoVo> iFileInfoVos = payablePlanSettlementInstallmentMapper.iFileExcelInfo(numSettlementBatch);
        List<PayablePlanSettlementBatchExchange> payablePlanSettlementBatchExchanges = payablePlanSettlementBatchExchangeMapper.selectList(Wrappers.<PayablePlanSettlementBatchExchange>lambdaQuery()
                .eq(PayablePlanSettlementBatchExchange::getNumSettlementBatch, numSettlementBatch));

        Set<Long> countryIds = iFileInfoVos.stream().map(IFileInfoVo::getAgentAreaCountryId).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Long> cityIds = iFileInfoVos.stream().map(IFileInfoVo::getAgentAreaCityId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> countryNumByCountryIds = institutionCenterClient.getCountryNumByCountryIds(countryIds).getData();
        Map<Long, String> cityFullNamesByIds = new HashMap<>();
        Result<Map<Long, String>> result = institutionCenterClient.getCityFullNamesByIds(cityIds);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            cityFullNamesByIds = result.getData();
        }
        for (IFileInfoVo iFileInfoVo : iFileInfoVos) {
            if (GeneralTool.isNotEmpty(iFileInfoVo.getAgentAreaCountryId())) {
                iFileInfoVo.setAgentAreaCountryNum(countryNumByCountryIds.get(iFileInfoVo.getAgentAreaCountryId()));
            }
            if (GeneralTool.isNotEmpty(iFileInfoVo.getAgentAreaCityId())) {
                String city = cityFullNamesByIds.get(iFileInfoVo.getAgentAreaCityId());
                //去除中文()
                if(city.indexOf("（") !=1){
                    int index = city.indexOf("（");
                    city = city.substring(0,index);
                }
                iFileInfoVo.setAgentAreaCityName(city);
                //iFileInfoVo.setAgentAreaCityName(cityFullNamesByIds.get(iFileInfoVo.getAgentAreaCityId()));
            }
            //获取汇率：NumSettlementBatch、FkAgentId、FkCurrencyTypeNum、FkTypeKey
            payablePlanSettlementBatchExchanges = payablePlanSettlementBatchExchangeMapper.selectList(Wrappers.<PayablePlanSettlementBatchExchange>lambdaQuery()
                    .eq(PayablePlanSettlementBatchExchange::getNumSettlementBatch, numSettlementBatch)
                    .eq(PayablePlanSettlementBatchExchange::getFkAgentId, iFileInfoVo.getAgentId())
                    .eq(PayablePlanSettlementBatchExchange::getFkCurrencyTypeNum, iFileInfoVo.getFkCurrencyTypeNum())
                    .eq(PayablePlanSettlementBatchExchange::getFkTypeKey, iFileInfoVo.getFtk()));
            if (GeneralTool.isNotEmpty(payablePlanSettlementBatchExchanges)) {
                PayablePlanSettlementBatchExchange payablePlanSettlementBatchExchange = payablePlanSettlementBatchExchanges.get(0);
                iFileInfoVo.setExchangeCurrencyNum(payablePlanSettlementBatchExchange.getFkCurrencyTypeNumExchange());
                iFileInfoVo.setExchangeRate(payablePlanSettlementBatchExchange.getExchangeRate());
                if (GeneralTool.isEmpty(payablePlanSettlementBatchExchange.getExchangeRate())) {
                    iFileInfoVo.setExchangeAmount(iFileInfoVo.getPayableAmount().multiply(new BigDecimal(1)));
                } else {
                    iFileInfoVo.setExchangeAmount(iFileInfoVo.getPayableAmount().multiply(payablePlanSettlementBatchExchange.getExchangeRate()));
                }
                //iFileInfoVo.setServiceFee(payablePlanSettlementBatchExchange.getServiceFee());
            } else {
                iFileInfoVo.setExchangeCurrencyNum("");
                iFileInfoVo.setExchangeRate(null);
                iFileInfoVo.setExchangeAmount(null);
            }
        }
        return iFileInfoVos;
    }


    /**
     * 获取iFile Excel信息
     *
     * @Date 14:14 2022/3/8
     * <AUTHOR>
     */
    public List<IFileInfoVo> iFileGroupByCurrencyInfo(String numSettlementBatch) {
        List<IFileInfoVo> iFileInfoVos = payablePlanSettlementInstallmentMapper.iFileGroupByCurrencyInfo(numSettlementBatch);
        List<PayablePlanSettlementBatchExchange> payablePlanSettlementBatchExchanges = payablePlanSettlementBatchExchangeMapper.selectList(Wrappers.<PayablePlanSettlementBatchExchange>lambdaQuery()
                .eq(PayablePlanSettlementBatchExchange::getNumSettlementBatch, numSettlementBatch));

        Set<Long> countryIds = iFileInfoVos.stream().map(IFileInfoVo::getAgentAreaCountryId).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Long> cityIds = iFileInfoVos.stream().map(IFileInfoVo::getAgentAreaCityId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> countryNumByCountryIds = institutionCenterClient.getCountryNumByCountryIds(countryIds).getData();
        Map<Long, String> cityFullNamesByIds = new HashMap<>();
        Result<Map<Long, String>> result = institutionCenterClient.getCityFullNamesByIds(cityIds);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            cityFullNamesByIds = result.getData();
        }
        for (IFileInfoVo iFileInfoVo : iFileInfoVos) {
            if (GeneralTool.isNotEmpty(iFileInfoVo.getAgentAreaCountryId())) {
                iFileInfoVo.setAgentAreaCountryNum(countryNumByCountryIds.get(iFileInfoVo.getAgentAreaCountryId()));
            }
            if (GeneralTool.isNotEmpty(iFileInfoVo.getAgentAreaCityId())) {
                iFileInfoVo.setAgentAreaCityName(cityFullNamesByIds.get(iFileInfoVo.getAgentAreaCityId()));
            }
            payablePlanSettlementBatchExchanges = payablePlanSettlementBatchExchangeMapper.selectList(Wrappers.<PayablePlanSettlementBatchExchange>lambdaQuery()
                    .eq(PayablePlanSettlementBatchExchange::getNumSettlementBatch, numSettlementBatch)
                    .eq(PayablePlanSettlementBatchExchange::getFkAgentId, iFileInfoVo.getAgentId())
                    .eq(PayablePlanSettlementBatchExchange::getFkCurrencyTypeNum, iFileInfoVo.getFkCurrencyTypeNum())
                    .eq(PayablePlanSettlementBatchExchange::getFkTypeKey, iFileInfoVo.getFtk()));
            if (GeneralTool.isNotEmpty(payablePlanSettlementBatchExchanges)) {
                PayablePlanSettlementBatchExchange payablePlanSettlementBatchExchange = payablePlanSettlementBatchExchanges.get(0);
                iFileInfoVo.setExchangeCurrencyNum(payablePlanSettlementBatchExchange.getFkCurrencyTypeNumExchange());
                iFileInfoVo.setExchangeRate(payablePlanSettlementBatchExchange.getExchangeRate());
                if (GeneralTool.isEmpty(payablePlanSettlementBatchExchange.getExchangeRate())) {
                    iFileInfoVo.setExchangeAmount(iFileInfoVo.getPayableAmount().multiply(new BigDecimal(1)));
                } else {
                    iFileInfoVo.setExchangeAmount(iFileInfoVo.getPayableAmount().multiply(payablePlanSettlementBatchExchange.getExchangeRate()));
                }
                //iFileInfoVo.setServiceFee(payablePlanSettlementBatchExchange.getServiceFee());
            } else {
                iFileInfoVo.setExchangeCurrencyNum("");
                iFileInfoVo.setExchangeRate(null);
                iFileInfoVo.setExchangeAmount(null);
            }
        }
        return iFileInfoVos;
    }

    /**
     * 自动生成付款单
     *
     * @param autoGeneratePaymentDto
     * @Date 14:48 2021/12/28
     * <AUTHOR>
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void autoGeneratePayment(AutoGeneratePaymentDto autoGeneratePaymentDto) {
        //先检测是否绑定过付款单
        List<PaymentForm> paymentForms = paymentFormMapper.getPayFormListByNumSettlementBatch(autoGeneratePaymentDto.getNumSettlementBatch());
        if (GeneralTool.isNotEmpty(paymentForms)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("PAYMENT_FORM_EXISTS"));
        }
        //生成付款单
        Result<List<PayablePlanVo>> result = saleCenterClient.getAgentPayablePlanByNumSettlementBatch(autoGeneratePaymentDto.getNumSettlementBatch());
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        List<PayablePlanVo> agentPayablePlanByNumSettlementBatch = result.getData();
        //先根据代理筛选一遍数据，把应付计划以代理进行区分
        Map<Long, List<PayablePlanVo>> agentPlanMap = new HashMap<>();
        for (PayablePlanVo payablePlanByNumSettlementBatch : agentPayablePlanByNumSettlementBatch) {
            List<PayablePlanVo> payablePlanVos = agentPlanMap.get(payablePlanByNumSettlementBatch.getFkAgentId());
            if (payablePlanVos == null) {
                payablePlanVos = new ArrayList<>();
            }
            payablePlanVos.add(payablePlanByNumSettlementBatch);
            agentPlanMap.put(payablePlanByNumSettlementBatch.getFkAgentId(), payablePlanVos);
        }

        //以代理为单位生成付款单
        for (Long agentId : agentPlanMap.keySet()) {
            List<PayablePlanVo> payablePlanVos = agentPlanMap.get(agentId);
            Long fkCompanyId = null;
            //根据币种筛选一遍数据，将该代理的应付计划以币种进行区分
            Map<String, List<PayablePlanVo>> currencyPlanMap = new HashMap<>();
            for (PayablePlanVo payablePlanVo : payablePlanVos) {
                List<PayablePlanVo> payablePlanVoList = currencyPlanMap.get(payablePlanVo.getFkCurrencyTypeNum());
                if (payablePlanVoList == null) {
                    payablePlanVoList = new ArrayList<>();
                }
                payablePlanVoList.add(payablePlanVo);
                currencyPlanMap.put(payablePlanVo.getFkCurrencyTypeNum(), payablePlanVoList);
                fkCompanyId = payablePlanVo.getFkCompanyId();

//                //如果该应付计划结算的是预付的，需要回溯找出对应的收款单 判断是否收齐钱 收齐则表示可以进行第二次结算了 给这些收款单生成分期表数据 以供结算
//                if (payablePlanVo.getIsPayInAdvance() && interfaceConfiguration.equals("GEA")) {
//                    if (GeneralTool.isEmpty(payablePlanVo.getFkReceiptFormItemId())) {
//                        //有预付的情况并且本次是结算预付的钱 则根据应收计划id判断收款状态 生成分期数据
//                        receiptFormItemService.isPayInAdvanceInsertSettlementInstallment(payablePlanVo.getFkReceivablePlanId());
//                    }
//                }
            }

            for (String currency : currencyPlanMap.keySet()) {
                List<PayablePlanVo> payablePlanVoCurrencyList = currencyPlanMap.get(currency);
                //根据结算银行账户再筛选一遍数据，将应付计划以代理-币种-结算银行账户为单位生成付款单
                Map<Long, List<PayablePlanVo>> bankAccountPlanMap = new HashMap<>();
                for (PayablePlanVo payablePlanVo : payablePlanVoCurrencyList) {
                    List<PayablePlanVo> payablePlanVoBankList = bankAccountPlanMap.get(payablePlanVo.getFkAgentContractAccountId());
                    if (payablePlanVoBankList == null) {
                        payablePlanVoBankList = new ArrayList<>();
                    }
                    payablePlanVoBankList.add(payablePlanVo);
                    bankAccountPlanMap.put(payablePlanVo.getFkAgentContractAccountId(), payablePlanVoBankList);
                }

                for (Long bankAccountId : bankAccountPlanMap.keySet()) {
//                    List<PayablePlanSettlementBatchExchange> payablePlanSettlementBatchExchanges =
//                            saleCenterClient.getSettlementBatchExchang(autoGeneratePaymentVo.getNumSettlementBatch(), currency, "HKD", agentId);
//                    //应付币种兑换港币汇率
//                    BigDecimal exchangeHkdRate = null;
//                    //应付币种兑人民币汇率
//                    BigDecimal exchangeRmbRate = null;
//                    if (payablePlanSettlementBatchExchanges != null) {
//                        PayablePlanSettlementBatchExchange payablePlanSettlementBatchExchange = payablePlanSettlementBatchExchanges.get(0);
//
//                        exchangeHkdRate = payablePlanSettlementBatchExchange.getExchangeRate();
//                        payablePlanSettlementBatchExchanges = saleCenterClient.getSettlementBatchExchang(autoGeneratePaymentVo.getNumSettlementBatch(), currency, "CNY", agentId);
//                        if (GeneralTool.isNotEmpty(payablePlanSettlementBatchExchanges)) {
//                            //港币兑人民币汇率
//                            BigDecimal hkExchangeRmbRate = payablePlanSettlementBatchExchange.getExchangeRate();
//                            //应付币种兑人民币汇率
//                            exchangeRmbRate = hkExchangeRmbRate.multiply(exchangeHkdRate);
//                        }
//                    }
                    List<PayablePlanVo> payablePlanVoBankList = bankAccountPlanMap.get(bankAccountId);
                    //计算总金额
                    BigDecimal payableAmountActualNum = payablePlanVoBankList.stream().map(PayablePlanVo::getAmountActual).reduce(BigDecimal.ZERO, BigDecimal::add);
                    //开始生成付款单
                    PaymentForm paymentForm = new PaymentForm();
                    paymentForm.setFkCompanyId(fkCompanyId);
                    paymentForm.setFkTypeKey(TableEnum.SALE_AGENT.key);
                    paymentForm.setFkTypeTargetId(agentId);
                    paymentForm.setExchangeRate(BigDecimal.ONE);
                    paymentForm.setNumBank(autoGeneratePaymentDto.getNumBank());
                    paymentForm.setFkBankAccountId(bankAccountId);
                    paymentForm.setFkBankAccountIdCompany(autoGeneratePaymentDto.getFkBankAccountIdCompany());
                    paymentForm.setFkCurrencyTypeNum(currency);
                    paymentForm.setAmount(payableAmountActualNum);
                    if (GeneralTool.isNotEmpty(autoGeneratePaymentDto.getPaymentDate())) {
                        paymentForm.setPaymentDate(autoGeneratePaymentDto.getPaymentDate());
                    }
                    //判断下本批次结算账号的币种
                    if ("CNY".equals(payablePlanVoBankList.get(0).getAccountCurrencyTypeNum())) {
                        List<PayablePlanSettlementBatchExchange> payablePlanSettlementBatchExchanges = payablePlanSettlementBatchExchangeMapper.selectList(Wrappers.<PayablePlanSettlementBatchExchange>lambdaQuery()
                                .eq(PayablePlanSettlementBatchExchange::getNumSettlementBatch, autoGeneratePaymentDto.getNumSettlementBatch())
                                .eq(PayablePlanSettlementBatchExchange::getFkCurrencyTypeNum, currency)
                                .eq(PayablePlanSettlementBatchExchange::getFkCurrencyTypeNumExchange, "HKD"));
                        //人民币账户结算的话取录入的汇率
                        PayablePlanSettlementBatchExchange payablePlanSettlementBatchExchange = payablePlanSettlementBatchExchanges.get(0);
                        //原币种兑港币
                        BigDecimal exchangeRateHkd = payablePlanSettlementBatchExchange.getExchangeRate();
                        paymentForm.setExchangeRateHkd(exchangeRateHkd);
                        paymentForm.setAmountHkd(payableAmountActualNum.multiply(paymentForm.getExchangeRateHkd()));

                        payablePlanSettlementBatchExchanges = payablePlanSettlementBatchExchangeMapper.selectList(Wrappers.<PayablePlanSettlementBatchExchange>lambdaQuery()
                                .eq(PayablePlanSettlementBatchExchange::getNumSettlementBatch, autoGeneratePaymentDto.getNumSettlementBatch())
                                .eq(PayablePlanSettlementBatchExchange::getFkCurrencyTypeNum, "HKD")
                                .eq(PayablePlanSettlementBatchExchange::getFkCurrencyTypeNumExchange, "CNY"));
                        payablePlanSettlementBatchExchange = payablePlanSettlementBatchExchanges.get(0);
                        //港币兑人民币
                        BigDecimal exchangeRateHkdCny = payablePlanSettlementBatchExchange.getExchangeRate();
                        //原币种兑人民币
                        BigDecimal exchangeRateCny = exchangeRateHkd.multiply(exchangeRateHkdCny);
                        paymentForm.setExchangeRateRmb(exchangeRateCny);
                        paymentForm.setAmountRmb(payableAmountActualNum.multiply(paymentForm.getExchangeRateRmb()));
                    } else {
                        paymentForm.setExchangeRateHkd(exchangeRateService.getLastExchangeRate(false, currency, "HKD").getExchangeRate());
                        paymentForm.setAmountHkd(payableAmountActualNum.multiply(paymentForm.getExchangeRateHkd()));
                        paymentForm.setExchangeRateRmb(exchangeRateService.getLastExchangeRate(false, currency, "CNY").getExchangeRate());
                        paymentForm.setAmountRmb(payableAmountActualNum.multiply(paymentForm.getExchangeRateRmb()));
                    }
                    paymentForm.setServiceFee(payablePlanVoBankList.stream().map(PayablePlanVo::getServiceFeeActual).reduce(BigDecimal.ZERO, BigDecimal::add));
                    paymentForm.setStatus(1);
                    utilService.updateUserInfoToEntity(paymentForm);
                    paymentFormMapper.insert(paymentForm);
                    paymentForm.setNumSystem(GetStringUtils.getPayFormNum(paymentForm.getId()));
                    paymentFormMapper.updateById(paymentForm);
                    //插入附件
                    List<MediaAndAttachedDto> mediaAndAttachedVoList = autoGeneratePaymentDto.getMediaAndAttachedVoList();
                    if (GeneralTool.isNotEmpty(mediaAndAttachedVoList)) {
                        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAndAttachedVoList) {
                            mediaAndAttachedDto.setFkTableId(paymentForm.getId());
                        }
                        paymentFormService.addMedia(mediaAndAttachedVoList);
                    }
                    //绑定付款单子项
                    for (PayablePlanVo payablePlanVo : payablePlanVoBankList) {
                        PaymentFormItem paymentFormItem = new PaymentFormItem();
                        paymentFormItem.setFkPaymentFormId(paymentForm.getId());
                        paymentFormItem.setFkPayablePlanId(payablePlanVo.getId());
                        paymentFormItem.setAmountPayment(payablePlanVo.getAmountActual());
                        paymentFormItem.setExchangeRatePayable(BigDecimal.ONE);
                        paymentFormItem.setAmountPayable(payablePlanVo.getAmountActual().add(payablePlanVo.getServiceFeeActual()));
                        paymentFormItem.setAmountExchangeRate(BigDecimal.ZERO);
                        paymentFormItem.setExchangeRateHkd(paymentForm.getExchangeRateHkd());
                        paymentFormItem.setAmountHkd(payablePlanVo.getAmountActual().add(payablePlanVo.getServiceFeeActual()).multiply(paymentForm.getExchangeRateHkd()));
                        paymentFormItem.setExchangeRateRmb(paymentForm.getExchangeRateRmb());
                        paymentFormItem.setAmountRmb(payablePlanVo.getAmountActual().add(payablePlanVo.getServiceFeeActual()).multiply(paymentForm.getExchangeRateRmb()));
                        paymentFormItem.setServiceFee(payablePlanVo.getServiceFeeActual());
                        utilService.updateUserInfoToEntity(paymentFormItem);
                        paymentFormItemMapper.insertSelective(paymentFormItem);

                        //佣金生成付款单后 根据应付计划id更新 分期表付款单id
                        LambdaQueryWrapper<PayablePlanSettlementInstallment> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                        lambdaQueryWrapper.eq(PayablePlanSettlementInstallment::getNumSettlementBatch, autoGeneratePaymentDto.getNumSettlementBatch()).eq(PayablePlanSettlementInstallment::getFkPayablePlanId, payablePlanVo.getId())
                                .eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_PROCESSING.key);
                        PayablePlanSettlementInstallment payablePlanSettlementInstallment = new PayablePlanSettlementInstallment();
                        payablePlanSettlementInstallment.setFkPaymentFormItemId(paymentFormItem.getId());
                        payablePlanSettlementInstallment.setStatus(ProjectExtraEnum.INSTALLMENT_COMPLETE.key);
                        utilService.setUpdateInfo(payablePlanSettlementInstallment);
                        payablePlanSettlementInstallmentMapper.update(payablePlanSettlementInstallment, lambdaQueryWrapper);
                    }

                }

            }

        }
    }

    /**
     * 绑定支付流水号
     *
     * @Date 16:56 2021/12/28
     * <AUTHOR>
     */
    @Override
    public void bindPaymentSerialNumber(BindPaymentSerialNumberDto bindPaymentSerialNumberDto) {
        List<PaymentForm> paymentForms = paymentFormMapper.getPayFormListByNumSettlementBatch(bindPaymentSerialNumberDto.getNumSettlementBatch());
        Set<Long> paymentFormIds = paymentForms.stream().map(PaymentForm::getId).collect(Collectors.toSet());

        PaymentForm paymentForm = new PaymentForm();
        paymentForm.setNumBank(bindPaymentSerialNumberDto.getNumBank());
        utilService.updateUserInfoToEntity(paymentForm);
        LambdaQueryWrapper<PaymentForm> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(PaymentForm::getId, paymentFormIds);
        paymentFormMapper.update(paymentForm, lambdaQueryWrapper);
        //插入附件
        paymentFormService.addMedia(bindPaymentSerialNumberDto.getMediaAndAttachedVoList());
    }

    /**
     * 代理对账单Excel导出
     *
     * @Date 17:21 2022/1/5
     * <AUTHOR>
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void agentStatementExcelExport(AgentStatementExcelExportDto agentStatementExcelExportDto, HttpServletResponse response) {
//        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMM");
//        if (ExcelTypeEnum.INTERMEDIARY_SETTLEMENT.excelType.equals(agentStatementExcelExportVo.getExcelType()) ||
//                ExcelTypeEnum.FOREIGN_CURRENCY.excelType.equals(agentStatementExcelExportVo.getExcelType())) {
//            List<AgentSettlementIntermediaryModel> agentSettlementIntermediaryModelList = paymentFormItemMapper.agentSettlementItemIntermediaryExport(agentStatementExcelExportVo.getPayablePlanIdList());
////            if (agentStatementExcelExportVo.getPayablePlanIdList().size() != agentSettlementIntermediaryModelList.size()) {
////                throw new GetServiceException(LocaleMessageUtils.getMessage("AGENT_STATEMENT_EXPORT_EXCEPTION"));
////            }
//            //代理银行账户
//            Result<Map<Long, AgentContractAccountVo>> result = saleCenterClient.getAgentContractAccountByAccountIds(Collections.singletonList(agentStatementExcelExportVo.getFkAgentContractAccountId()));
//            if (result.isSuccess()) {
//                throw new GetServiceException(result.getMessage());
//            }
//            Map<Long, AgentContractAccountVo> agentContractAccountMap = result.getData();
//
//            AgentContractAccountVo agentContractAccountDto = agentContractAccountMap.get(agentStatementExcelExportVo.getFkAgentContractAccountId());
//            if (GeneralTool.isNotEmpty(agentContractAccountDto.getFkCurrencyTypeNum())) {
//                String currencyName = currencyTypeMapper.getCurrencyNameByNum(agentContractAccountDto.getFkCurrencyTypeNum());
//                agentContractAccountDto.setCurrencyTypeName(currencyName);
//            }
//            if (GeneralTool.isNotEmpty(agentContractAccountDto.getFkAreaCountryId())) {
//                Result<String> result1 = institutionCenterClient.getCountryNameAndNumById(agentContractAccountDto.getFkAreaCountryId());
//                if (result1.isSuccess()) {
//                    throw new GetServiceException(result1.getMessage());
//                }
//                String countryName = result1.getData();
//                agentContractAccountDto.setCountryName(countryName);
//            }
//
//            Result<String> result2 = saleCenterClient.getAgentNameById(agentStatementExcelExportVo.getFkAgentId());
//            if (result2.isSuccess()) {
//                throw new GetServiceException(result2.getMessage());
//            }
//            String agentName = result2.getData();
//
//            Map<String, BigDecimal> currencyAccountMap = new HashMap<>();
//            List<CurrencyAccountModel> currencyAccountModelList = new ArrayList<>();
//            for (AgentSettlementIntermediaryModel agentSettlementIntermediaryModel : agentSettlementIntermediaryModelList) {
//                BigDecimal amount = currencyAccountMap.get(agentSettlementIntermediaryModel.getPayablePlanCurrency());
//                if (amount == null) {
//                    amount = BigDecimal.ZERO;
//                }
//                amount = amount.add(agentSettlementIntermediaryModel.getPayableAmount());
//                currencyAccountMap.put(agentSettlementIntermediaryModel.getPayablePlanCurrency(), amount);
//            }
//
//            for (String currency : currencyAccountMap.keySet()) {
//                BigDecimal amount = currencyAccountMap.get(currency);
//                CurrencyAccountModel currencyAccountModel = new CurrencyAccountModel();
//                currencyAccountModel.setAmount(amount);
//                currencyAccountModel.setCurrencyNum(currency);
//                currencyAccountModelList.add(currencyAccountModel);
//            }
//
//            for (AgentSettlementIntermediaryModel agentSettlementIntermediaryModel : agentSettlementIntermediaryModelList) {
//                //插入结算标记
//                PayablePlanSettlementAgentAccount payablePlanSettlementAgentAccount = new PayablePlanSettlementAgentAccount();
//                payablePlanSettlementAgentAccount.setFkPayablePlanId(agentSettlementIntermediaryModel.getId());
//                payablePlanSettlementAgentAccount.setFkAgentContractAccountId(agentContractAccountDto.getId());
//                payablePlanSettlementAgentAccount.setFkCurrencyTypeNum(agentContractAccountDto.getFkCurrencyTypeNum());
//                Result<Boolean> result3 = saleCenterClient.insertPayablePlanSettlementAgentAccount(payablePlanSettlementAgentAccount);
//                if (!result3.isSuccess() || result3.getData() != true) {
//                    throw new GetServiceException(result3.getMessage());
//                }
//            }
//            //提交代理确认结算
//            Result<Boolean> result_ = saleCenterClient.agentConfirmSettlement(agentStatementExcelExportVo.getPayablePlanIdList());
//            if (!result_.isSuccess() || !result_.getData()) {
//                throw new GetServiceException(result_.getMessage());
//            }
//
//            Map<String, Object> param = new HashMap<>();
//            param.put("currencyAccountModelList", currencyAccountModelList);
//            param.put("agentName", agentName);
//            param.put("list", agentSettlementIntermediaryModelList);
//            param.put("accountInfo", agentContractAccountDto);
//            param.put("userName", GetAuthInfo.getUser().getName());
//            param.put("date", DateUtil.now());
//            StringBuilder fileName = new StringBuilder();
//            fileName.append(agentName).append("-").append(formatter.format(new Date())).append("-");
//            for (AgentSettlementIntermediaryModel agentSettlementIntermediaryModel : agentSettlementIntermediaryModelList) {
//                fileName.append(agentSettlementIntermediaryModel.getStudentName()).append(".");
//            }
//            if (ExcelTypeEnum.INTERMEDIARY_SETTLEMENT.excelType.equals(agentStatementExcelExportVo.getExcelType())) {
//                fileName.append("-人民币中介结算汇出表");
//                TemplateExcelUtils.downLoadExcel(fileName.toString(), ExcelTypeEnum.INTERMEDIARY_SETTLEMENT.excelTemplateName, param, response);
//            } else {
//                fileName.append("-外币中介结算汇出表");
//                TemplateExcelUtils.downLoadExcel(fileName.toString(), ExcelTypeEnum.FOREIGN_CURRENCY.excelTemplateName, param, response);
//            }
//        } else if (ExcelTypeEnum.EASY_TRANSFER.excelType.equals(agentStatementExcelExportVo.getExcelType())) {
//            List<AgentSettlementTransferModel> agentSettlementTransferModelList = paymentFormItemMapper.agentSettlementItemTransferExport(agentStatementExcelExportVo.getPayablePlanIdList());
////            if (agentStatementExcelExportVo.getPayablePlanIdList().size() != agentSettlementTransferModelList.size()) {
////                throw new YException(LocaleMessageUtils.getMessage("AGENT_STATEMENT_EXPORT_EXCEPTION"));
////            }
//            AgentSettlementTransferModel agentSettlementTransferModel = agentSettlementTransferModelList.get(0);
//            Map<String, Object> param = new HashMap<>();
//            param.put("list", agentSettlementTransferModelList);
//            param.put("userName", SecureUtil.getStaffInfo().getName());
//            param.put("date", DateUtil.now());
//            StringBuilder fileName = new StringBuilder();
//            fileName.append(agentSettlementTransferModel.getAgentName()).append("-").append(formatter.format(new Date())).append("-");
//            for (AgentSettlementTransferModel settlementTransferModel : agentSettlementTransferModelList) {
//                fileName.append(settlementTransferModel.getStudentName()).append(".");
//            }
//            fileName.append("-易思汇");
//            TemplateExcelUtils.downLoadExcel(fileName.toString(), ExcelTypeEnum.EASY_TRANSFER.excelTemplateName, param, response);
//
//        } else if (ExcelTypeEnum.INSURANCE.excelType.equals(agentStatementExcelExportVo.getExcelType())) {
//            List<AgentSettlementInsuranceModel> agentSettlementItemInsuranceExport = paymentFormItemMapper.agentSettlementItemInsuranceExport(agentStatementExcelExportVo.getPayablePlanIdList());
//            if (agentStatementExcelExportVo.getPayablePlanIdList().size() != agentSettlementItemInsuranceExport.size()) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("AGENT_STATEMENT_EXPORT_EXCEPTION"));
//            }
//            //代理银行账户
//            Result<Map<Long, AgentContractAccountVo>> result = saleCenterClient.getAgentContractAccountByAccountIds(Collections.singletonList(agentStatementExcelExportVo.getFkAgentContractAccountId()));
//            if (result.isSuccess()) {
//                throw new GetServiceException(result.getMessage());
//            }
//            Map<Long, AgentContractAccountVo> agentContractAccountMap = result.getData();
//            AgentContractAccount agentContractAccount = agentContractAccountMap.get(agentStatementExcelExportVo.getFkAgentContractAccountId());
//            Result<String> result1 = saleCenterClient.getAgentNameById(agentStatementExcelExportVo.getFkAgentId());
//            if (result1.isSuccess()) {
//                throw new GetServiceException(result1.getMessage());
//            }
//            String agentName = result1.getData();
//            Map<String, BigDecimal> currencyAccountMap = new HashMap<>();
//            List<CurrencyAccountModel> currencyAccountModelList = new ArrayList<>();
//            for (AgentSettlementInsuranceModel agentSettlementInsuranceModel : agentSettlementItemInsuranceExport) {
//                BigDecimal amount = currencyAccountMap.get(agentSettlementInsuranceModel.getPayablePlanCurrency());
//                if (amount == null) {
//                    amount = BigDecimal.ZERO;
//                }
//                amount = amount.add(agentSettlementInsuranceModel.getPayableAmount());
//                currencyAccountMap.put(agentSettlementInsuranceModel.getPayablePlanCurrency(), amount);
//            }
//
//            for (String currency : currencyAccountMap.keySet()) {
//                BigDecimal amount = currencyAccountMap.get(currency);
//                CurrencyAccountModel currencyAccountModel = new CurrencyAccountModel();
//                currencyAccountModel.setAmount(amount);
//                currencyAccountModel.setCurrencyNum(currency);
//                currencyAccountModelList.add(currencyAccountModel);
//            }
//            StringBuilder fileName = new StringBuilder();
//            fileName.append(agentName).append("-").append(formatter.format(new Date())).append("-");
//            for (AgentSettlementInsuranceModel agentSettlementInsuranceModel : agentSettlementItemInsuranceExport) {
//                agentSettlementInsuranceModel.setDescription("Insurance");
//                fileName.append(agentSettlementInsuranceModel.getStudentName()).append(".");
//            }
//            Map<String, Object> param = new HashMap<>();
//            param.put("currencyAccountModelList", currencyAccountModelList);
//            param.put("agentName", agentName);
//            param.put("list", agentSettlementItemInsuranceExport);
//            param.put("accountInfo", agentContractAccount);
//            param.put("userName", GetAuthInfo.getUser().getName());
//            param.put("date", DateUtil.now());
//            fileName.append("-留学小屋汇出表");
//            TemplateExcelUtils.downLoadExcel(fileName.toString(), ExcelTypeEnum.INSURANCE.excelTemplateName, param, response);
//            for (AgentSettlementInsuranceModel agentSettlementInsuranceModel : agentSettlementItemInsuranceExport) {
//                //插入结算标记
//                PayablePlanSettlementAgentAccount payablePlanSettlementAgentAccount = new PayablePlanSettlementAgentAccount();
//                payablePlanSettlementAgentAccount.setFkPayablePlanId(agentSettlementInsuranceModel.getId());
//                payablePlanSettlementAgentAccount.setFkAgentContractAccountId(agentContractAccount.getId());
//                payablePlanSettlementAgentAccount.setFkCurrencyTypeNum(agentContractAccount.getFkCurrencyTypeNum());
//                Result<Boolean> result2 = saleCenterClient.insertPayablePlanSettlementAgentAccount(payablePlanSettlementAgentAccount);
//                if (!result2.isSuccess() || result2.getData() != true) {
//                    throw new GetServiceException(result2.getMessage());
//                }
//            }
//            //提交代理确认结算
//            Result<Boolean> result_ = saleCenterClient.agentConfirmSettlement(agentStatementExcelExportVo.getPayablePlanIdList());
//            if (!result_.isSuccess() || !result_.getData()) {
//                throw new GetServiceException(result_.getMessage());
//            }
//        } else if (ExcelTypeEnum.ACCOMMODATION.excelType.equals(agentStatementExcelExportVo.getExcelType())) {
//            List<AgentSettlementAccommodationModel> agentSettlementAccommodationModelList = paymentFormItemMapper.agentSettlementAccommodationExport(agentStatementExcelExportVo.getPayablePlanIdList());
////            if (agentStatementExcelExportVo.getPayablePlanIdList().size() != agentSettlementAccommodationModelList.size()) {
////                throw new GetServiceException(LocaleMessageUtils.getMessage("AGENT_STATEMENT_EXPORT_EXCEPTION"));
////            }
//            //代理银行账户
//            Result<Map<Long, AgentContractAccountVo>> result2 = saleCenterClient.getAgentContractAccountByAccountIds(Collections.singletonList(agentStatementExcelExportVo.getFkAgentContractAccountId()));
//            if (result2.isSuccess()) {
//                throw new GetServiceException(result2.getMessage());
//            }
//            Map<Long, AgentContractAccountVo> agentContractAccountMap = result2.getData();
//            AgentContractAccount agentContractAccount = agentContractAccountMap.get(agentStatementExcelExportVo.getFkAgentContractAccountId());
//            Result<String> result3 = saleCenterClient.getAgentNameById(agentStatementExcelExportVo.getFkAgentId());
//            if (result3.isSuccess()) {
//                throw new GetServiceException(result3.getMessage());
//            }
//            String agentName = result3.getData();
//            StringBuilder fileName = new StringBuilder();
//            fileName.append(agentName).append("-").append(formatter.format(new Date())).append("-");
//            Map<String, BigDecimal> currencyAccountMap = new HashMap<>();
//            List<CurrencyAccountModel> currencyAccountModelList = new ArrayList<>();
//            for (AgentSettlementAccommodationModel agentSettlementAccommodationModel : agentSettlementAccommodationModelList) {
//                BigDecimal amount = currencyAccountMap.get(agentSettlementAccommodationModel.getPayablePlanCurrency());
//                if (amount == null) {
//                    amount = BigDecimal.ZERO;
//                }
//                amount = amount.add(agentSettlementAccommodationModel.getPayableAmount());
//                currencyAccountMap.put(agentSettlementAccommodationModel.getPayablePlanCurrency(), amount);
//                fileName.append(agentSettlementAccommodationModel.getStudentName()).append(".");
//            }
//
//            for (String currency : currencyAccountMap.keySet()) {
//                BigDecimal amount = currencyAccountMap.get(currency);
//                CurrencyAccountModel currencyAccountModel = new CurrencyAccountModel();
//                currencyAccountModel.setAmount(amount);
//                currencyAccountModel.setCurrencyNum(currency);
//                currencyAccountModelList.add(currencyAccountModel);
//            }
//
//            Map<String, Object> param = new HashMap<>();
//            param.put("currencyAccountModelList", currencyAccountModelList);
//            param.put("agentName", agentName);
//            param.put("list", agentSettlementAccommodationModelList);
//            param.put("accountInfo", agentContractAccount);
//            param.put("userName", GetAuthInfo.getUser().getName());
//            param.put("date", DateUtil.now());
//            fileName.append("-留学小屋汇出表");
//            TemplateExcelUtils.downLoadExcel(fileName.toString(), ExcelTypeEnum.ACCOMMODATION.excelTemplateName, param, response);
//            for (AgentSettlementAccommodationModel agentSettlementAccommodationModel : agentSettlementAccommodationModelList) {
//                //插入结算标记
//                PayablePlanSettlementAgentAccount payablePlanSettlementAgentAccount = new PayablePlanSettlementAgentAccount();
//                payablePlanSettlementAgentAccount.setFkPayablePlanId(agentSettlementAccommodationModel.getId());
//                payablePlanSettlementAgentAccount.setFkAgentContractAccountId(agentContractAccount.getId());
//                payablePlanSettlementAgentAccount.setFkCurrencyTypeNum(agentContractAccount.getFkCurrencyTypeNum());
//                Result<Boolean> result = saleCenterClient.insertPayablePlanSettlementAgentAccount(payablePlanSettlementAgentAccount);
//                if (!result.isSuccess() || result.getData() != true) {
//                    throw new GetServiceException(result.getMessage());
//                }
//            }
//            //提交代理确认结算
//            Result<Boolean> result_ = saleCenterClient.agentConfirmSettlement(agentStatementExcelExportVo.getPayablePlanIdList());
//            if (!result_.isSuccess() || !result_.getData()) {
//                throw new GetServiceException(result_.getMessage());
//            }
//        }

    }

    /**
     * 财务佣金汇总批次取消（第五步取消）
     *
     * @Date 16:41 2022/4/19
     * <AUTHOR>
     */
    @Override
    @Transactional
    public void cancelFinancialBatchSettlement(List<CancelFinancialBatchSettlementDto> cancelFinancialBatchSettlementDtos) {
        Set<String> numSettlementBatchSet = cancelFinancialBatchSettlementDtos.stream().map(CancelFinancialBatchSettlementDto::getNumSettlementBatch).collect(Collectors.toSet());
        List<PayablePlanSettlementInstallment> payablePlanSettlementInstallmentList = payablePlanSettlementInstallmentMapper.selectList(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                .in(PayablePlanSettlementInstallment::getNumSettlementBatch, numSettlementBatchSet)
                .isNotNull(PayablePlanSettlementInstallment::getFkPaymentFormItemId));
        if (GeneralTool.isNotEmpty(payablePlanSettlementInstallmentList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("SALE_NUM_SETTLEMENT_BATCH_PAYMENT_FORM_EXIST"));
        }
        for (CancelFinancialBatchSettlementDto cancelFinancialBatchSettlementDto : cancelFinancialBatchSettlementDtos) {
            List<CancelSettlementDto> cancelSettlementDtos = payablePlanSettlementInstallmentMapper.cancelFinancialBatchSettlement(cancelFinancialBatchSettlementDto.getNumSettlementBatch());
            for (CancelSettlementDto cancelSettlementDto : cancelSettlementDtos) {

                List<PayablePlanSettlementInstallment> settlementInstallments = payablePlanSettlementInstallmentMapper.selectList(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                        .eq(PayablePlanSettlementInstallment::getFkPayablePlanId, cancelSettlementDto.getPayablePlanId())
                        .eq(PayablePlanSettlementInstallment::getFkAgentContractAccountId, cancelSettlementDto.getFkAgentContractAccountId())
                        .eq(PayablePlanSettlementInstallment::getStatusSettlement, ProjectExtraEnum.FINANCIAL_SUMMARY.key)
                        .eq(PayablePlanSettlementInstallment::getNumSettlementBatch, cancelFinancialBatchSettlementDto.getNumSettlementBatch()));
                BigDecimal amountActual = settlementInstallments.get(0).getAmountActual();
                BigDecimal serviceFeeActual = settlementInstallments.get(0).getServiceFeeActual();
                List<Long> settlementIds = settlementInstallments.stream().map(PayablePlanSettlementInstallment::getId).collect(Collectors.toList());

                List<PayablePlanSettlementInstallment> previousStepSettlementInstallments = payablePlanSettlementInstallmentMapper.selectList(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                        .eq(PayablePlanSettlementInstallment::getFkPayablePlanId, cancelSettlementDto.getPayablePlanId())
                        .eq(PayablePlanSettlementInstallment::getFkAgentContractAccountId, cancelSettlementDto.getFkAgentContractAccountId())
                        .eq(PayablePlanSettlementInstallment::getStatusSettlement, ProjectExtraEnum.FINANCIAL_RECOGNITION.key));

                if (GeneralTool.isNotEmpty(previousStepSettlementInstallments)) {
                    PayablePlanSettlementInstallment settlementInstallment = previousStepSettlementInstallments.get(0);
                    amountActual = amountActual.add(settlementInstallment.getAmountActual());
                    serviceFeeActual = serviceFeeActual.add(settlementInstallment.getServiceFeeActual());
                    settlementIds.addAll(previousStepSettlementInstallments.stream().map(PayablePlanSettlementInstallment::getId).collect(Collectors.toList()));
                }
                PayablePlanSettlementInstallment payablePlanSettlementInstallment = new PayablePlanSettlementInstallment();
                payablePlanSettlementInstallment.setAmountActual(amountActual);
                payablePlanSettlementInstallment.setServiceFeeActual(serviceFeeActual);
                payablePlanSettlementInstallment.setStatusSettlement(ProjectExtraEnum.FINANCIAL_RECOGNITION.key);
                payablePlanSettlementInstallment.setNumSettlementBatch(null);
                utilService.setUpdateInfo(payablePlanSettlementInstallment);
                payablePlanSettlementInstallmentMapper.update(payablePlanSettlementInstallment, Wrappers.<PayablePlanSettlementInstallment>lambdaUpdate().in(PayablePlanSettlementInstallment::getId, settlementIds).set(PayablePlanSettlementInstallment::getNumSettlementBatch, null));

                for (PayablePlanSettlementInstallment settlementInstallment : settlementInstallments) {
                    PayablePlanSettlementStatus payablePlanSettlementStatus = new PayablePlanSettlementStatus();
                    payablePlanSettlementStatus.setFkPayablePlanSettlementInstallmentId(settlementInstallment.getId());
                    payablePlanSettlementStatus.setFkPayablePlanId(settlementInstallment.getFkPayablePlanId());
                    payablePlanSettlementStatus.setStatusSettlement(ProjectExtraEnum.FINANCIAL_RECOGNITION.key);
                    utilService.updateUserInfoToEntity(payablePlanSettlementStatus);
                    payablePlanSettlementStatusMapper.insert(payablePlanSettlementStatus);
                }

            }

            //删除汇率
            LambdaQueryWrapper<PayablePlanSettlementBatchExchange> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(PayablePlanSettlementBatchExchange::getNumSettlementBatch, cancelFinancialBatchSettlementDto.getNumSettlementBatch());
            payablePlanSettlementBatchExchangeMapper.delete(lambdaQueryWrapper);

        }
    }

    /**
     * 财务结算汇总锁定代理(第四步锁定代理)
     *
     * @Date 11:46 2022/5/18
     * <AUTHOR>
     */
    @Override
    public void financialSettlementAgentLocking(List<FinancialSettlementAgentLockingDto> financialSettlementAgentLockingDtos) {
        for (FinancialSettlementAgentLockingDto financialSettlementAgentLockingDto : financialSettlementAgentLockingDtos) {
            List<RPayablePlanSettlementFlag> payablePlanSettlementFlagList = payablePlanSettlementFlagMapper.selectList(Wrappers.<RPayablePlanSettlementFlag>lambdaQuery()
                    .eq(RPayablePlanSettlementFlag::getFkAgentId, financialSettlementAgentLockingDto.getAgentId())
                    .eq(RPayablePlanSettlementFlag::getFkCurrencyTypeNum, financialSettlementAgentLockingDto.getPlanCurrencyNum())
                    .eq(RPayablePlanSettlementFlag::getFkCurrencyTypeNumAccount, financialSettlementAgentLockingDto.getAccountCurrencyNum())
                    .eq(RPayablePlanSettlementFlag::getFkTypeKey, financialSettlementAgentLockingDto.getFkTypeKey())
                    .eq(RPayablePlanSettlementFlag::getStatusSettlement, ProjectExtraEnum.FINANCIAL_RECOGNITION.key));
            if (GeneralTool.isNotEmpty(payablePlanSettlementFlagList)) {
                continue;
            }
            RPayablePlanSettlementFlag payablePlanSettlementFlag = new RPayablePlanSettlementFlag();
            payablePlanSettlementFlag.setFkAgentId(financialSettlementAgentLockingDto.getAgentId());
            payablePlanSettlementFlag.setFkCurrencyTypeNum(financialSettlementAgentLockingDto.getPlanCurrencyNum());
            payablePlanSettlementFlag.setFkCurrencyTypeNumAccount(financialSettlementAgentLockingDto.getAccountCurrencyNum());
            payablePlanSettlementFlag.setFkTypeKey(financialSettlementAgentLockingDto.getFkTypeKey());
            payablePlanSettlementFlag.setStatusSettlement(ProjectExtraEnum.FINANCIAL_RECOGNITION.key);
            utilService.setCreateInfo(payablePlanSettlementFlag);
            payablePlanSettlementFlagMapper.insert(payablePlanSettlementFlag);
        }
    }

    /**
     * 财务结算汇总解锁代理(第四步解锁代理)
     *
     * @Date 11:46 2022/5/18
     * <AUTHOR>
     */
    @Override
    public void financialSettlementAgentUnlocking(List<FinancialSettlementAgentLockingDto> financialSettlementAgentLockingDtos) {
        for (FinancialSettlementAgentLockingDto financialSettlementAgentLockingDto : financialSettlementAgentLockingDtos) {
            payablePlanSettlementFlagMapper.delete(Wrappers.<RPayablePlanSettlementFlag>lambdaQuery()
                    .eq(RPayablePlanSettlementFlag::getFkAgentId, financialSettlementAgentLockingDto.getAgentId())
                    .eq(RPayablePlanSettlementFlag::getFkCurrencyTypeNum, financialSettlementAgentLockingDto.getPlanCurrencyNum())
                    .eq(RPayablePlanSettlementFlag::getFkCurrencyTypeNumAccount, financialSettlementAgentLockingDto.getAccountCurrencyNum())
                    .eq(RPayablePlanSettlementFlag::getFkTypeKey, financialSettlementAgentLockingDto.getFkTypeKey())
                    .eq(RPayablePlanSettlementFlag::getStatusSettlement, ProjectExtraEnum.FINANCIAL_RECOGNITION.key));
        }
    }

    /**
     * 删除佣金结算（佣金第一二三步专用）
     *
     * @Date 16:38 2022/5/23
     * <AUTHOR>
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void deleteSettlement(List<DeleteSettlementDeleteDto> deleteSettlementDeleteDtoList) {
        LambdaQueryWrapper<PayablePlanSettlementInstallment> payablePlanSettlementInstallmentLambdaQueryWrapper = Wrappers.<PayablePlanSettlementInstallment>lambdaQuery();
        for (DeleteSettlementDeleteDto deleteSettlementDeleteDto : deleteSettlementDeleteDtoList) {
            payablePlanSettlementInstallmentLambdaQueryWrapper.or(queryWrapper -> {
                        queryWrapper.eq(PayablePlanSettlementInstallment::getFkPayablePlanId, deleteSettlementDeleteDto.getPayablePlanId())
                                .eq(PayablePlanSettlementInstallment::getStatusSettlement, deleteSettlementDeleteDto.getStatusSettlement());
                        if (GeneralTool.isNotEmpty(deleteSettlementDeleteDto.getFkAgentContractAccountId())) {
                            queryWrapper.eq(PayablePlanSettlementInstallment::getFkAgentContractAccountId, deleteSettlementDeleteDto.getFkAgentContractAccountId());
                        } else {
                            queryWrapper.isNull(PayablePlanSettlementInstallment::getFkAgentContractAccountId);
                        }
                    }
            );
        }
        payablePlanSettlementInstallmentMapper.delete(payablePlanSettlementInstallmentLambdaQueryWrapper);
    }

    /**
     * 财务结算汇总删除佣金结算(第四步删除佣金结算按钮)
     *
     * @Date 17:24 2021/12/24
     * <AUTHOR>
     */
    @Override
    @Transactional
    public void deleteFinancialSettlementSummary(List<DeleteFinancialSettlementSummaryDeleteDto> deleteFinancialSettlementSummaryDeleteDtoList) {
        for (DeleteFinancialSettlementSummaryDeleteDto deleteFinancialSettlementSummaryDeleteDto : deleteFinancialSettlementSummaryDeleteDtoList) {
            //删除锁定表
            payablePlanSettlementFlagMapper.delete(Wrappers.<RPayablePlanSettlementFlag>lambdaQuery()
                    .eq(RPayablePlanSettlementFlag::getFkTypeKey, deleteFinancialSettlementSummaryDeleteDto.getFkTypeKey())
                    .eq(RPayablePlanSettlementFlag::getFkAgentId, deleteFinancialSettlementSummaryDeleteDto.getAgentId())
                    .eq(RPayablePlanSettlementFlag::getFkCurrencyTypeNum, deleteFinancialSettlementSummaryDeleteDto.getPlanCurrencyNum())
                    .eq(RPayablePlanSettlementFlag::getFkCurrencyTypeNumAccount, deleteFinancialSettlementSummaryDeleteDto.getAccountCurrencyNum()));
        }

        List<String> settlementIds = deleteFinancialSettlementSummaryDeleteDtoList.stream().map(deleteFinancialSettlementSummaryDeleteVo -> deleteFinancialSettlementSummaryDeleteVo.getSettlementIds().split(",")).flatMap(Arrays::stream).collect(Collectors.toList());
        LambdaUpdateWrapper<PayablePlanSettlementInstallment> wrapper = Wrappers.<PayablePlanSettlementInstallment>lambdaUpdate()
                .in(PayablePlanSettlementInstallment::getId, settlementIds)
                .eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_PROCESSING.key)
                .eq(PayablePlanSettlementInstallment::getStatusSettlement, ProjectExtraEnum.FINANCIAL_RECOGNITION.key);
        payablePlanSettlementInstallmentMapper.delete(wrapper);
    }

    /**
     * 待结算标记
     *
     * @Date 18:11 2024/1/17
     * <AUTHOR>
     */
    @Override
    @Transactional
    public void pendingSettlementMark(PendingSettlementMarkDto pendingSettlementMarkDto) {
        if (pendingSettlementMarkDto.getFlag()) {
            RPayablePlanSettlementFlag rPayablePlanSettlementFlag = new RPayablePlanSettlementFlag();
            rPayablePlanSettlementFlag.setFkAgentId(pendingSettlementMarkDto.getAgentId());
            rPayablePlanSettlementFlag.setStatusSettlement(ProjectExtraEnum.SETTLEMENT_IN_PROGRESS.key);
            utilService.setCreateInfo(rPayablePlanSettlementFlag);
            payablePlanSettlementFlagMapper.insert(rPayablePlanSettlementFlag);
        } else {
            payablePlanSettlementFlagMapper.delete(Wrappers.<RPayablePlanSettlementFlag>lambdaUpdate()
                    .eq(RPayablePlanSettlementFlag::getStatusSettlement, ProjectExtraEnum.SETTLEMENT_IN_PROGRESS.key)
                    .eq(RPayablePlanSettlementFlag::getFkAgentId, pendingSettlementMarkDto.getAgentId()));
        }
    }

    @Override
    public Integer getAccountCommissionSettlementStatus(Long accountId) {
        return payablePlanSettlementAgentAccountMapper.getCommissionSettlementStatus(accountId);
    }

    /**
     * 修改未结算佣金的银行币种标记
     * @param accountId
     * @param fkCurrencyTypeNum
     * @return
     */
    @Override
    public Boolean updateCommissionSettlementAccountCurrencyTypeNum(Long accountId, String fkCurrencyTypeNum) {
        Integer commissionSettlementStatus = payablePlanSettlementAgentAccountMapper.getCommissionSettlementStatus(accountId);
        if (commissionSettlementStatus != 3) {
            PayablePlanSettlementInstallment payablePlanSettlementInstallment = new PayablePlanSettlementInstallment();
            payablePlanSettlementInstallment.setFkCurrencyTypeNum(fkCurrencyTypeNum);
            utilService.setUpdateInfo(payablePlanSettlementInstallment);
            List<Integer> settlementInstallmentStatus = new ArrayList<>();
            settlementInstallmentStatus.add(ProjectExtraEnum.UNSETTLED.key);
            settlementInstallmentStatus.add(ProjectExtraEnum.SETTLEMENT_IN_PROGRESS.key);
            settlementInstallmentStatus.add(ProjectExtraEnum.AGENT_CONFIRMATION.key);
            payablePlanSettlementInstallmentMapper.update(payablePlanSettlementInstallment, Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                    .eq(PayablePlanSettlementInstallment::getFkAgentContractAccountId, accountId)
                    .in(PayablePlanSettlementInstallment::getStatusSettlement, settlementInstallmentStatus)
                    .ne(PayablePlanSettlementInstallment::getFkCurrencyTypeNum,fkCurrencyTypeNum));
        }
        return true;
    }

    @Override
    public Boolean getCommissionSettlementAccountInfo(Long accountId) {
        return payablePlanSettlementAgentAccountMapper.getCommissionSettlementAccountInfo(accountId);
    }

    /**
     * 区域名字set
     *
     * @Date 10:48 2021/12/22
     * <AUTHOR>
     */
    private void setAreaName(AgentSettlementVo agentDto, Map<Long, String> countryNamesByIds, Map<Long, String> stateNamesByIds,
                             Map<Long, String> cityNamesByIds) {
        if (GeneralTool.isNotEmpty(agentDto)) {
            if (GeneralTool.isNotEmpty(agentDto.getFkAreaCountryId())) {
                //设置国家名称
                agentDto.setCountryName(countryNamesByIds.get(agentDto.getFkAreaCountryId()));
            }
            if (GeneralTool.isNotEmpty(agentDto.getFkAreaStateId())) {
                //设置州省名称
                agentDto.setStateName(stateNamesByIds.get(agentDto.getFkAreaStateId()));
            }
            if (GeneralTool.isNotEmpty(agentDto.getFkAreaCityId())) {
                //设置城市名称
                agentDto.setCityName(cityNamesByIds.get(agentDto.getFkAreaCityId()));
            }
        }
    }


    /**
     * 佣金结算Excel类型枚举
     *
     * @Date 16:21 2022/1/7
     * <AUTHOR>
     */
    public enum ExcelTypeEnum {

        HIT_SETTLEMENT("HIT_SETTLEMENT", "hitSettlement.xlsx"),
        /**
         * 易思汇
         */
        EASY_TRANSFER("EASY_TRANSFER", "easyTransfer.xlsx"),
        /**
         * 财务汇总
         */
        SUMMARY("SUMMARY", "summary.xlsx");
        public final String excelType;

        public final String excelTemplateName;

        ExcelTypeEnum(String excelType, String excelTemplateName) {
            this.excelType = excelType;
            this.excelTemplateName = excelTemplateName;
        }

        public String getExcelTemplateNameByType(String excelType) {
            ExcelTypeEnum[] excelTypeEnums = values();
            for (ExcelTypeEnum excelTypeEnum : excelTypeEnums) {
                if (excelTypeEnum.excelType.equals(excelType)) {
                    return excelTypeEnum.excelTemplateName;
                }
            }
            return null;
        }
    }

    /**
     * 更新结算标记
     *
     * @Date 16:23 2021/12/31
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStatusSettlement(List<Long> payablePlanIdList, List<Integer> oldStatusSettlements, Integer newStatusSettlement, String num) {
        for (Long payablePlanId : payablePlanIdList) {
            PayablePlanSettlementStatus payablePlanSettlementStatus = new PayablePlanSettlementStatus();
            payablePlanSettlementStatus.setNumSettlementBatch(num);
            payablePlanSettlementStatus.setFkPayablePlanId(payablePlanId);
            payablePlanSettlementStatus.setStatusSettlement(newStatusSettlement);
            utilService.updateUserInfoToEntity(payablePlanSettlementStatus);
            payablePlanSettlementStatusMapper.insert(payablePlanSettlementStatus);
        }
        //如果是第二步提交按钮，记录导入时间 设置回滚时间
        if (ProjectExtraEnum.AGENT_CONFIRMATION.key.equals(newStatusSettlement)) {
            PayablePlanSettlementInstallment payablePlanSettlementInstallment = new PayablePlanSettlementInstallment();
            payablePlanSettlementInstallment.setAccountExportTime(new Date());
            payablePlanSettlementInstallment.setRollBackTime(DateUtil.nextMonth());
            payablePlanSettlementInstallmentMapper.update(payablePlanSettlementInstallment, Wrappers.<PayablePlanSettlementInstallment>lambdaUpdate()
                    .in(PayablePlanSettlementInstallment::getFkPayablePlanId, payablePlanIdList)
                    .eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_PROCESSING.key));
        }

        //修改应付计划佣金状态
        UpdatePayablePlanStatusSettlementDto updatePayablePlanStatusSettlementDto = new UpdatePayablePlanStatusSettlementDto();
        updatePayablePlanStatusSettlementDto.setPayablePlanIdList(payablePlanIdList);
        updatePayablePlanStatusSettlementDto.setOldStatusSettlements(oldStatusSettlements);
        updatePayablePlanStatusSettlementDto.setNewStatusSettlement(newStatusSettlement);
        saleCenterClient.updatePayablePlanStatusSettlement(updatePayablePlanStatusSettlementDto);

    }

    /**
     * 根据应付计划id以及结算状态获取对应的预付金额
     *
     * @Date 21:15 2022/4/20
     * <AUTHOR>
     */
    public BigDecimal getPrepaymentAmountByPayablePlanId(Long payablePlanId, Integer statusSettlement) {
        BigDecimal amount = new BigDecimal(0);
        LambdaQueryWrapper<PayablePlanSettlementInstallment> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PayablePlanSettlementInstallment::getFkPayablePlanId, payablePlanId)
                .isNull(PayablePlanSettlementInstallment::getFkReceiptFormItemId);
        if (ProjectExtraEnum.UNSETTLED.key.equals(statusSettlement)) {
            lambdaQueryWrapper.eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_UNTREATED.key);
        } else {
            lambdaQueryWrapper.eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_PROCESSING.key);
        }
        List<PayablePlanSettlementInstallment> payablePlanSettlementInstallments = payablePlanSettlementInstallmentMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(payablePlanSettlementInstallments)) {
            if (ProjectExtraEnum.UNSETTLED.key.equals(statusSettlement)) {
                for (PayablePlanSettlementInstallment payablePlanSettlementInstallment : payablePlanSettlementInstallments) {
                    //如果是在佣金第一步，则将金额加起来等于预付金额
                    amount = amount.add(payablePlanSettlementInstallment.getAmountActual());
                }
            } else {
                //如果已经进入佣金结算，取实际支付金额（合并）即可
                amount = payablePlanSettlementInstallments.get(0).getAmountActual();
            }
        }
        return amount;
    }


}
