package com.get.institutioncenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.vo.CompanyTreeVo;
import com.get.institutioncenter.entity.ContractFormulaCompany;
import com.get.institutioncenter.dto.ContractFormulaCompanyDto;

import java.util.List;
import java.util.Map;

/**
 * @author: Sea
 * @create: 2021/1/8 10:27
 * @verison: 1.0
 * @description:
 */
public interface IContractFormulaCompanyService extends BaseService<ContractFormulaCompany> {
    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过合同公式id 查找对应公司ids
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<Long> getCompanyIdListByFkid(Long contractFormulaId);

    /**
     * @Description :通过合同公式id 查找对应公司名称
     * @Param [contractFormulaIds]
     * <AUTHOR>
     */
    Map<Long, String> getCompanyNameMapByFkids(List<Long> contractFormulaIds);

    /**
     * @return void
     * @Description :合同公式-安全配置
     * @Param [contractFormulaCompanyDtos]
     * <AUTHOR>
     */
    void editContractFormulaCompany(List<ContractFormulaCompanyDto> contractFormulaCompanyDtos);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.CompanyTreeVo>
     * @Description :合同公式-安全配置详情
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<CompanyTreeVo> getContractFormulaCompany(Long contractFormulaId);

    /**
     * @return void
     * @Description :根据contractFormulaId删除
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    void deleteByFkid(Long contractFormulaId);
}
