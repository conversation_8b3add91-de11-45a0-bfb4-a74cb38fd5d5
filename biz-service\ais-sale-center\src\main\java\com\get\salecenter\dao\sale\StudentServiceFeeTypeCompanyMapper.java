package com.get.salecenter.dao.sale;

import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.SelItem;
import com.get.salecenter.entity.StudentServiceFeeTypeCompany;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface StudentServiceFeeTypeCompanyMapper extends GetMapper<StudentServiceFeeTypeCompany> {
    /**
     * 获取公司信息
     * @param ids
     * @return
     */
    List<SelItem> getServiceFeeTypeCompanyInfo(@Param("ids") Set<Long> ids,@Param("companyIds") List<Long> companyIds);

    /**
     * 获取公司信息
     * @param id
     * @return
     */
    String getServiceFeeTypeCompanyInfoById(@Param("id") Long id);

    /**
     * 获取类型的公司id
     * @param id
     * @return
     */
    List<Long> getServiceFeeTypeCompanyId(@Param("id")Long id);
}
