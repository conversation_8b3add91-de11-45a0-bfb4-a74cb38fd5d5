package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.vo.InfoTypeVo;
import com.get.institutioncenter.entity.InfoType;
import com.get.institutioncenter.dto.InfoTypeDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/8/5 10:32
 * @verison: 1.0
 * @description: 资讯类型管理接口
 */
public interface IInfoTypeService extends BaseService<InfoType> {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    InfoTypeVo findInfoTypeById(Long id);

    /**
     * 批量新增
     *
     * @param infoTypeDtos
     * @return
     */
    void batchAdd(List<InfoTypeDto> infoTypeDtos);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    void delete(Long id);

    /**
     * 修改
     *
     * @param infoTypeDto
     * @return
     */
    InfoTypeVo updateInfoType(InfoTypeDto infoTypeDto);


    /**
     * 列表
     *
     * @param infoTypeDto
     * @param page
     * @return
     */
    List<InfoTypeVo> getInfoTypes(InfoTypeDto infoTypeDto, Page page);

    /**
     * 上移下移
     *
     * @param infoTypeDtos
     * @return
     */
    void movingOrder(List<InfoTypeDto> infoTypeDtos);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description :资讯类型下拉框
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getInfoTypeList();


    /**
     * 根据ids获取名称map
     *
     * @param ids
     * @return
     */
    Map<Long, String> getInfoTypeNamesByIds(Set<Long> ids);
}
