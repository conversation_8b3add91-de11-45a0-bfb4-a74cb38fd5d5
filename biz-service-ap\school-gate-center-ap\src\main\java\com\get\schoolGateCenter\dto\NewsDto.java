package com.get.schoolGateCenter.dto;
import com.get.schoolGateCenter.entity.News;
import com.get.schoolGateCenter.entity.NewsType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/7
 * @TIME: 11:36
 * @Description:
 **/
@Data
@ApiModel("新闻返回类")
public class NewsDto extends News {

    /**
     * 目标集合
     */
    @ApiModelProperty(value = "目标集合")
    private List<RNewsTypeDto> rNewsTypeDtos;

    @ApiModelProperty(value = "新闻推荐类型")
    private List<NewsType> newsTypes;

    /**
     * 文件下载链接
     */
    @ApiModelProperty(value = "新闻附件信息")
    private List<MediaAndAttachedDto> mediaAndAttachedDtos;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;
    /**
     * 公开对象名称
     */
    @ApiModelProperty(value = "公开对象名称")
    private String publicLevelName;

    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;
    /**
     * 目标类型对应的表名
     */
    @ApiModelProperty(value = "目标类型对应的表名")
    private String tableName;

    @ApiModelProperty(value = "新闻推荐类型")
    private String newTypeNames;

}
