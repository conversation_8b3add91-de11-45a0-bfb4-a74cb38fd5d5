<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.KpiPlanMapper">

    <select id="getSumTimeKpiData" resultType="java.lang.String">
        SELECT
            CONCAT(SUM(sumTime), '') AS sumTimeStr
        FROM (
            SELECT SUM(IFNULL(UNIX_TIMESTAMP(gmt_create), 0) + IFNULL(UNIX_TIMESTAMP(gmt_modified), 0)) AS sumTime
            FROM m_kpi_plan
            WHERE id = #{fkKpiPlanId}
            UNION ALL
            SELECT SUM(IFNULL(UNIX_TIMESTAMP(gmt_create), 0) + IFNULL(UNIX_TIMESTAMP(gmt_modified), 0)) AS sumTime
            FROM m_kpi_plan_group
            WHERE fk_kpi_plan_id = #{fkKpiPlanId}
            UNION ALL
            SELECT SUM(IFNULL(UNIX_TIMESTAMP(gmt_create), 0) + IFNULL(UNIX_TIMESTAMP(gmt_modified), 0)) AS sumTime
            FROM m_kpi_plan_group_item
            WHERE fk_kpi_plan_id = #{fkKpiPlanId}
            UNION ALL
            SELECT SUM(IFNULL(UNIX_TIMESTAMP(gmt_create), 0) + IFNULL(UNIX_TIMESTAMP(gmt_modified), 0)) AS sumTime
            FROM m_kpi_plan_staff
            WHERE fk_kpi_plan_id = #{fkKpiPlanId}
            UNION ALL
            SELECT SUM(IFNULL(UNIX_TIMESTAMP(b.gmt_create), 0) + IFNULL(UNIX_TIMESTAMP(b.gmt_modified), 0)) AS sumTime
            FROM m_kpi_plan_staff a
            INNER JOIN m_kpi_plan_staff_label b ON a.id = b.fk_kpi_plan_staff_id
            WHERE a.fk_kpi_plan_id = #{fkKpiPlanId}
            UNION ALL
            SELECT SUM(IFNULL(UNIX_TIMESTAMP(b.gmt_create), 0) + IFNULL(UNIX_TIMESTAMP(b.gmt_modified), 0)) AS sumTime
            FROM m_kpi_plan_group_item a
            INNER JOIN m_kpi_plan_target b ON a.id = b.fk_kpi_plan_group_item_id
            WHERE a.fk_kpi_plan_id = #{fkKpiPlanId}
        ) AS res
    </select>
    <select id="selectKpiPlanList" resultType="com.get.salecenter.entity.KpiPlan">
        SELECT plan.*
        FROM m_kpi_plan AS plan LEFT JOIN m_kpi_plan_staff AS planStaff ON plan.id = planStaff.fk_kpi_plan_id
        <where>
            <if test="companyIds != null and companyIds != ''">
                AND (
                <foreach collection="companyIds" item="companyId" separator=" OR ">
                    FIND_IN_SET(#{companyId}, fk_company_ids) > 0
                </foreach>
                )
            </if>
            <if test="kpiPlanSearchDto.isEnableScheduledCount != null">
                AND is_enable_scheduled_count = #{isEnableScheduledCount}
            </if>
            <if test="kpiPlanSearchDto.keyword != null and kpiPlanSearchDto.keyword != ''">
                AND (title LIKE CONCAT('%', #{kpiPlanSearchDto.keyword}, '%')
                OR description LIKE CONCAT('%', #{kpiPlanSearchDto.keyword}, '%'))
            </if>
            <if test="staffFollowerIds.size()>0">
            AND    planStaff.fk_staff_id_add IN
                <foreach collection="staffFollowerIds" item="staffFollowerId" separator="," open="(" close=")">
                    #{staffFollowerId}
                </foreach>
                 OR
                planStaff.fk_staff_id IN
                <foreach collection="staffFollowerIds" item="staffFollowerId" separator="," open="(" close=")">
                    #{staffFollowerId}
                </foreach>
            </if>
           <if test="currentUser!=null and currentUser!=''">
              OR  plan.gmt_create_user = #{currentUser}
           </if>
        </where>
        GROUP BY plan.id
        ORDER BY gmt_create DESC

    </select>

</mapper>
