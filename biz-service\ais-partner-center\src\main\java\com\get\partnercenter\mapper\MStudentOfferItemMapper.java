package com.get.partnercenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.partnercenter.entity.MStudentOfferEntity;
import com.get.partnercenter.entity.MStudentOfferItemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
* <AUTHOR>
* @description 针对表【m_student_offer_item(学生申请方案学习计划)】的数据库操作Mapper
* @createDate 2025-01-18 11:51:30
* @Entity com.partner.entity.MStudentOfferItem
*/
@Mapper
@DS("saledb")
public interface MStudentOfferItemMapper extends BaseMapper<MStudentOfferItemEntity> {


    List<MStudentOfferEntity> selectByOfferArr(@Param("fkStudentId")Long fkStudentId);



}




