package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("m_insurance_order_settlement")
public class InsuranceOrderSettlement extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "代理提交结算批次编号")
    private String fkNumOptBatch;

    @ApiModelProperty(value = "保险订单Id")
    private Long fkInsuranceOrderId;

    @ApiModelProperty(value = "应付计划Id")
    private Long fkPayablePlanId;

    @ApiModelProperty(value = "付款单子项Id")
    private Long fkPaymentFormItemId;

    @ApiModelProperty(value = "结算状态：0待确认/1已确认/2代理确认/3财务确认/4结算完成")
    private Integer statusSettlement;


}
