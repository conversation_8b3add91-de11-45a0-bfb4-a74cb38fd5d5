package com.get.insurancecenter.vo.commission;

import com.get.insurancecenter.vo.order.OrderDetailVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/5/27
 * @Version 1.0
 * @apiNote:代理列表
 */
@Data
public class AgentVo {

    @ApiModelProperty(value = "代理ID")
    private Long agentId;

    @ApiModelProperty(value = "代理编号")
    private String agentNum;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "是否有效0否1是")
    private Integer isActive;

    @ApiModelProperty(value = "国家ID")
    private Long areaCountryId;

    @ApiModelProperty(value = "州省Id")
    private Long areaStateId;

    @ApiModelProperty(value = "城市Id")
    private Long areaCityId;

    @ApiModelProperty(value = "国家名称")
    private String areaCountryName;

    @ApiModelProperty(value = "州省名称")
    private String areaStateName;

    @ApiModelProperty(value = "城市名称")
    private String areaCityName;

    @ApiModelProperty(value = "国家名称-中文")
    private String areaCountryNameChn;

    @ApiModelProperty(value = "州省名称-中文")
    private String areaStateNameChn;

    @ApiModelProperty(value = "城市名称-中文")
    private String areaCityNameChn;

    @ApiModelProperty(value = "所属分公司Ids")
    private List<Long> companyIds;

    @ApiModelProperty(value = "所属分公司编号")
    private List<String> companyNums;

    @ApiModelProperty(value = "所属分公司名称")
    private List<String> companyNames;

    @ApiModelProperty(value = "保单数")
    private Integer orderCount;

    @ApiModelProperty(value = "订单列表")
    private List<OrderDetailVo> orderList;

    @ApiModelProperty(value = "代理账户信息-用于确认结算列表和已结算列表")
    private AgentAccountVo agentAccount;

    @ApiModelProperty(value = "对账单ID-确认结算订单/已结算订单列表有值")
    private Long settlementBillId;

}
