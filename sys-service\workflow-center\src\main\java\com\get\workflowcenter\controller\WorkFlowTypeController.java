package com.get.workflowcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.workflowcenter.vo.WorkFlowTypeVo;
import com.get.workflowcenter.service.IWorkFlowTypeService;
import com.get.workflowcenter.dto.WorkFlowTypeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/1/13 12:08
 * @verison: 1.0
 * @description:
 */
@Api(tags = "流程类型管理")
@RestController
@RequestMapping("workflow/workFlowType")
public class WorkFlowTypeController {
    @Resource
    private IWorkFlowTypeService workFlowTypeService;

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/BD团队配置管理/BD团队配置详情")
    @GetMapping("/{id}")
    public ResponseBo<WorkFlowTypeVo> detail(@PathVariable("id") Long id) {
        WorkFlowTypeVo data = workFlowTypeService.findWorkFlowTypeById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 批量新增信息
     *
     * @param workFlowTypeDtos
     * @return
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/BD团队配置管理/新增BD团队配置")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(WorkFlowTypeDto.Add.class) ValidList<WorkFlowTypeDto> workFlowTypeDtos) {
        workFlowTypeService.batchAdd(workFlowTypeDtos);
        return SaveResponseBo.ok();
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/BD团队配置管理/删除BD团队配置")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        workFlowTypeService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param workFlowTypeDto
     * @return
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/BD团队配置管理/更新BD团队配置")
    @PostMapping("update")
    public ResponseBo<WorkFlowTypeVo> update(@RequestBody @Validated(WorkFlowTypeDto.Update.class) WorkFlowTypeDto workFlowTypeDto) {
        return UpdateResponseBo.ok(workFlowTypeService.updateWorkFlowType(workFlowTypeDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/BD团队配置管理/查询BD团队配置")
    @PostMapping("datas")
    public ResponseBo<WorkFlowTypeVo> datas(@RequestBody SearchBean<WorkFlowTypeDto> page) {
        List<WorkFlowTypeVo> datas = workFlowTypeService.getWorkFlowTypes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :上移下移
     * @Param [eventTypeVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动类型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<WorkFlowTypeDto> workFlowTypeDtos) {
        workFlowTypeService.movingOrder(workFlowTypeDtos);
        return ResponseBo.ok();
    }

    /**
     * 流程类型下拉框数据
     *
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "流程类型下拉框数据", notes = "")
    @GetMapping("getWorkFlowTypeList")
    public ResponseBo<WorkFlowTypeVo> getWorkFlowTypeList() {
        List<WorkFlowTypeVo> datas = workFlowTypeService.getWorkFlowTypeList();
        return new ListResponseBo<>(datas);
    }
}
