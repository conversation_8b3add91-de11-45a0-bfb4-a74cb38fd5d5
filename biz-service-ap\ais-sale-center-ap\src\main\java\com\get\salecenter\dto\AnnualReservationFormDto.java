package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/7/7 15:46
 * @verison: 1.0
 * @description:
 */
@Data
public class AnnualReservationFormDto extends BaseVoEntity{

    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    private Long fkConventionId;

    /**
     * 参展人员类型（枚举定义）：0校方/1代理/2嘉宾/3员工/4工作人员
     */
    @ApiModelProperty(value = "参展人员类型（枚举定义）：0校方/1代理/2嘉宾/3员工/4工作人员")
    private Integer type;

    /**
     * 参会编号
     */
    @ApiModelProperty(value = "参会编号")
    private String num;

    /**
     * 参加人姓名
     */
    @ApiModelProperty(value = "参加人姓名")
    private String name;

    /**
     * 参加人姓名（中文）
     */
    @ApiModelProperty(value = "参加人姓名（中文）")
    private String nameChn;

    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男")
    private Integer gender;

    /**
     * 参加人职位
     */
    @ApiModelProperty(value = "参加人职位")
    private String title;

    /**
     * 公司
     */
    @ApiModelProperty(value = "公司")
    private String company;

    /**
     * 参加人电邮
     */
    @ApiModelProperty(value = "参加人电邮")
    private String email;

    /**
     * 参加人电话
     */
    @ApiModelProperty(value = "参加人电话")
    private String tel;

    /**
     * 护照号
     */
    @ApiModelProperty(value = "护照号")
    private String passportNum;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    private String idCardNum;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 到店日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "到店日期")
    private Date checkInTime;

    /**
     * 离店日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "离店日期")
    private Date checkOutTime;

    /**
     * 预计到达时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "预计到达时间")
    private Date arrivalTime;

    /**
     * 到达交通类型（枚举定义选择）：0飞机/1高铁/2汽车
     */
    @ApiModelProperty("到达交通类型（枚举定义选择）：0飞机/1高铁/2汽车")
    private Integer arrivalTransportation;

    /**
     * 到达交通编号：航班号/高铁班次/汽车班次
     */
    @ApiModelProperty("到达交通编号：航班号/高铁班次/汽车班次")
    private String arrivalTransportationCode;

    /**
     * 预计离开时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "预计离开时间")
    private Date leaveTime;

    /**
     * 离开交通类型（枚举定义选择）：0飞机/1高铁/2汽车
     */
    @ApiModelProperty("离开交通类型（枚举定义选择）：0飞机/1高铁/2汽车")
    private Integer leaveTransportation;

    /**
     * 到达交通编号：航班号/高铁班次/汽车班次
     */
    @ApiModelProperty("离开交通编号：航班号/高铁班次/汽车班次")
    private String leaveTransportationCode;

    /**
     * 酒店住宿费用类型：0公费/1自费
     */
    @ApiModelProperty(value = "酒店住宿费用类型：0公费/1自费")
    private Integer hotelFeeType;

    /**
     * 价格币种编号
     */
    @ApiModelProperty(value = "价格币种编号")
    private String fkCurrencyTypeNumHotelExpense;

    /**
     * 住宿费用单价（每晚）
     */
    @ApiModelProperty(value = "住宿费用单价（每晚）")
    private BigDecimal hotelExpense;

    /**
     * 酒店房型Id
     */
    @ApiModelProperty(value = "酒店房型Id")
    private Long fkConventionHotelId;

    /**
     * BD编号（4位）
     */
    @ApiModelProperty(value = "BD编号（4位）")
    private String bdCode;

    /**
     * 是否出席：0否/1是
     */
    @ApiModelProperty(value = "是否出席：0否/1是")
    private Boolean isAttend;

    //自定义
    /**
     * 回执码
     */
    @ApiModelProperty(value = "回执码")
    private String receiptCode;

//    /**
//     * 中文姓
//     */
//    @ApiModelProperty(value = "中文姓")
//    private String lastNameChn;
//
//    /**
//     * 中文名
//     */
//    @ApiModelProperty(value = "中文名")
//    private String firstNameChn;
//
//    /**
//     * 英文姓/拼音姓
//     */
//    @ApiModelProperty(value = "英文姓/拼音姓")
//    private String lastName;
//
//    /**
//     * 英文名/拼音名
//     */
//    @ApiModelProperty(value = "英文名/拼音名")
//    private String firstName;

    /**
     * 机构名
     */
    @ApiModelProperty(value = "机构名")
    private String institutionName;

    /**
     * 展位名
     */
    @ApiModelProperty(value = "展位名")
    private String boothName;

    /**
     * 房间型号
     */
//    @ApiModelProperty("房间型号")
//    private String roomTypeName;

    /**
     * 峰会流程
     */
    @ApiModelProperty(value = "峰会流程")
    private List<Long> conventionProcedureIds;

    /**
     * 报名名册id
     */
    @ApiModelProperty(value = "报名名册id")
    private Long conventionRegistrationId;

//    /**
//     * 峰会id
//     */
//    @ApiModelProperty(value = "峰会id")
//    private Long conventionId;

    /**
     * 证件内容
     */
    @ApiModelProperty(value = "证件内容")
    private String documentContent;

    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型")
    private Integer documentType;


//    @ApiModelProperty(value = "同住人id")
//    private Long residentId;

    @ApiModelProperty(value = "同住人姓名")
    private String residentName;

    @ApiModelProperty(value = "同住人姓名（中文）")
    private String residentNChn;

}
