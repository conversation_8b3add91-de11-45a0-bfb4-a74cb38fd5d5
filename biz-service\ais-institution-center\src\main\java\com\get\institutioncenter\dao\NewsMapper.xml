<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.NewsMapper">
    <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.News">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_table_name" jdbcType="VARCHAR" property="fkTableName"/>
        <result column="fk_table_id" jdbcType="BIGINT" property="fkTableId"/>
        <result column="fk_news_type_id" jdbcType="BIGINT" property="fkNewsTypeId"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="public_level" jdbcType="VARCHAR" property="publicLevel"/>
        <result column="profile" jdbcType="VARCHAR" property="profile"/>
        <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
        <result column="web_title" jdbcType="VARCHAR" property="webTitle"/>
        <result column="web_meta_description" jdbcType="VARCHAR" property="webMetaDescription"/>
        <result column="web_meta_keywords" jdbcType="VARCHAR" property="webMetaKeywords"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.get.institutioncenter.entity.News">
        <result column="description" jdbcType="LONGVARCHAR" property="description"/>
    </resultMap>
    <sql id="Blob_Column_List">
        description
    </sql>
    <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.News" keyProperty="id"
            useGeneratedKeys="true">
        insert into s_news
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkTableName != null">
                fk_table_name,
            </if>
            <if test="fkTableId != null">
                fk_table_id,
            </if>
            <if test="fkNewsTypeId != null">
                fk_news_type_id,
            </if>
            <if test="title != null">
                title,
            </if>
            <if test="publicLevel != null">
                public_level,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="webTitle != null">
                web_title,
            </if>
            <if test="webMetaDescription != null">
                web_meta_description,
            </if>
            <if test="webMetaKeywords != null">
                web_meta_keywords,
            </if>
            <if test="profile != null">
                profile,
            </if>
            <if test="publishTime != null">
                publish_time,
            </if>
            <if test="effectiveStartTime != null">
                effective_start_time,
            </if>
            <if test="effectiveEndTime != null">
                effective_end_time,
            </if>
            <if test="fkNewsTypeIdRecommend != null">
                fk_news_type_id_recommend,
            </if>
            <if test="gotoUrl != null">
                goto_url
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkTableName != null">
                #{fkTableName,jdbcType=VARCHAR},
            </if>
            <if test="fkTableId != null">
                #{fkTableId,jdbcType=BIGINT},
            </if>
            <if test="fkNewsTypeId != null">
                #{fkNewsTypeId,jdbcType=BIGINT},
            </if>
            <if test="title != null">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="publicLevel != null">
                #{publicLevel,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=LONGVARCHAR},
            </if>
            <if test="webTitle != null">
                #{webTitle,jdbcType=VARCHAR},
            </if>
            <if test="webMetaDescription != null">
                #{webMetaDescription,jdbcType=VARCHAR},
            </if>
            <if test="webMetaKeywords != null">
                #{webMetaKeywords,jdbcType=VARCHAR},
            </if>
            <if test="profile != null">
                #{profile,jdbcType=LONGVARCHAR},
            </if>
            <if test="publishTime != null">
                #{publishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="effectiveStartTime != null">
                #{effectiveStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="effectiveEndTime != null">
                #{effectiveEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="fkNewsTypeIdRecommend != null">
                #{fkNewsTypeIdRecommend,jdbcType=VARCHAR},
            </if>
            <if test="gotoUrl != null">
                #{gotoUrl,jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>
    <select id="datas" parameterType="com.get.institutioncenter.dto.NewsDto"
            resultType="com.get.institutioncenter.vo.NewsVo">
        select cp.id,
               cp.fk_table_name,
               cp.fk_table_id,
               cp.fk_news_type_id,
               cp.title,
               cp.profile,
               cp.description_preview,
               cp.description_m,
               cp.description_m_preview,
               cp.web_title,
               cp.web_meta_description,
               cp.web_meta_keywords,
               cp.public_level,
               cp.publish_time,
               cp.effective_start_time,
               cp.effective_end_time,
               cp.fk_news_type_id_recommend,
               cp.goto_url,
               cp.send_email_time,
               cp.send_email_offer_item_steps,
               cp.send_email_agent_time,
               cp.send_email_all_agent_time,
               cp.gmt_create,
               cp.gmt_create_user,
               cp.gmt_modified,
               cp.gmt_modified_user
        from s_news cp
            left join r_news_type rnt on rnt.fk_news_id = cp.id
        <if test="newsDto.companyId != null and newsDto.companyId != ''">
            left join r_news_company nc on cp.id = nc.fk_news_id
        </if>
        where 1 = 1
        <if test="newsDto.fkTableName != null and newsDto.fkTableName != ''">
            and rnt.fk_table_name = #{newsDto.fkTableName}
        </if>
        <if test="newsDto.keyWord != null and newsDto.keyWord != ''">
            and position(#{newsDto.keyWord,jdbcType=VARCHAR} in cp.title)
        </if>
        <if test="newsDto.fkNewsTypeId != null and newsDto.fkNewsTypeId != ''">
            and rnt.fk_news_type_id = #{newsDto.fkNewsTypeId}
        </if>
        <if test="newsDto.targetName != null and newsDto.targetName != ''">
            and position(#{newsDto.targetName,jdbcType=VARCHAR} in ip.name)
        </if>
        <if test="newsDto.companyId != null and newsDto.companyId != ''">
            and nc.fk_company_id = #{newsDto.companyId}
        </if>
        <if test="newsDto.fkTableId != null and newsDto.fkTableId != ''">
            and rnt.fk_table_id = #{newsDto.fkTableId}
        </if>
        <if test="newsDto.publicLevel != null">
            and position(#{newsDto.publicLevel} in cp.public_level)
        </if>
        group by cp.id
        <choose>
            <when test="newsDto.sortOrder == 1">
                order by IFNULL(cp.gmt_modified, cp.gmt_create) desc
            </when>
            <when test="newsDto.sortOrder == 2">
                order by IFNULL(cp.gmt_create,cp.gmt_modified) desc
            </when>
            <when test="newsDto.sortOrder == 3">
                order by IFNULL(cp.publish_time, cp.gmt_create) desc, IFNULL(cp.gmt_modified, cp.gmt_create) desc
            </when>
            <otherwise>
                order by IFNULL(cp.gmt_modified, cp.gmt_create) desc
            </otherwise>
        </choose>
    </select>
    <select id="getNewsTarget" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        <if test="tableName != 'm_institution_course' and tableName != 'm_institution_provider'">
            select id,
                   CASE WHEN IFNULL(name_chn, '') = '' THEN name ELSE CONCAT(name, '（', name_chn, '）') END name,
                   name_chn
            from ${tableName}
        </if>
        <if test="tableName == 'm_institution_provider'">
            select institutionProvider.id,
            CASE WHEN IFNULL(name_chn,'')='' THEN name ELSE CONCAT(name,'（',name_chn,'）') END name,name_chn
            from
                ${tableName} AS institutionProvider
                INNER JOIN r_institution_provider_area_country AS institutionProviderAreaCountry
            ON
                institutionProviderAreaCountry.fk_institution_provider_id = institutionProvider.id
            where institutionProviderAreaCountry.fk_area_country_id in
            <choose>
                <when test="companyIds != null and companyIds.size() > 0">
                    <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                        #{companyId}
                    </foreach>
                </when>
                <otherwise>
                    (NULL)
                </otherwise>
            </choose>
        </if>
        <if test="tableName == 'm_institution_course'">
            SELECT course.id,
                   CASE
                       WHEN IFNULL(course.name_chn, '') = '' THEN CONCAT('【', institution.name, '】', course.name)
                       ELSE CONCAT('【', institution.name, '】', course.name, '（', course.name_chn, '）')
                       END AS name,
                   course.name_chn
            FROM ${tableName} AS course
                     INNER JOIN m_institution AS institution ON course.fk_institution_id =
                                                                institution.id
        </if>
    </select>
    <select id="getTargetName" resultType="java.lang.String">
        select CASE WHEN IFNULL(name_chn, '') = '' THEN `name` ELSE CONCAT(`name`, '（', name_chn, '）') END name
        from ${tableName}
        where id = #{id}
    </select>
    <select id="getCountByInstitutionId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select count(*)
        from s_news
        where fk_table_name = 'm_institution'
          and fk_table_id = #{id}
    </select>
    <select id="getCountByCourseId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select count(*)
        from s_news
        where fk_table_name = 'm_institution_course'
          and fk_table_id = #{id}
    </select>

    <select id="newsIsEmpty" resultType="java.lang.Boolean">
        select IFNULL(max(id), 0) id
        from s_news
        where fk_table_id = #{areaCountryId}
          and fk_table_name = #{tableName} LIMIT 1
    </select>
    <select id="getNewsIds" resultType="java.lang.Long">
        select id
        from s_news
        where ${columnName} is not NULL
          and ${columnName} != ""
    </select>
    <select id="getNewsById" resultType="com.get.institutioncenter.entity.News">
        select *
        from s_news
        where id = #{id}
    </select>
    <select id="getAllNewsSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        select n.id, n.title AS name
        from s_news n
        ORDER BY IFNULL(n.gmt_modified, n.gmt_create) desc
    </select>
    <select id="isExistByCourseId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id), 0) id
        from s_news
        where fk_table_id = #{courseId}
          and fk_table_name = 'm_institution_course'
    </select>
</mapper>