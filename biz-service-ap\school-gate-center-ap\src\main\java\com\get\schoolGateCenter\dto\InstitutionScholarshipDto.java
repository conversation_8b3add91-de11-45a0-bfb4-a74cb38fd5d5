package com.get.schoolGateCenter.dto;

import com.get.schoolGateCenter.entity.InstitutionScholarship;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/6 12:04
 */
@Data
public class InstitutionScholarshipDto extends InstitutionScholarship {

    @ApiModelProperty("学校名")
    private String fkInstitutionName;


    @ApiModelProperty("学校中文名")
    private String fkInstitutionNameZh;

    @ApiModelProperty("学校英文名")
    private String fkInstitutionNameEn;

    @ApiModelProperty("学院名")
    private String institutionFacultyName;

    /**
     * 学校学院Id（二级）
     */
    @ApiModelProperty(value = "学校学院Id（二级）")
    private Long fkInstitutionFacultyIdSub;

    /**
     * 学校学院Id（二级）
     */
    @ApiModelProperty(value = "学校学院Id（二级）")
    private String fkInstitutionFacultyIdSubName;

//    @ApiModelProperty("二级学院奖学金信息")
//    private List<InstitutionScholarshipSubDto> fkFacultyDatas = new ArrayList<>();

    @ApiModelProperty("课程等级")
    private String majorLeveName;

    @ApiModelProperty("公开对象名称")
    private String publicLevelName;

    @ApiModelProperty("学院等级，1是1级，2是二级")
    private int Level;

    @ApiModelProperty("学校封面url")
    private String coversUrl;

    @ApiModelProperty("是否有申请费用")
    private Boolean isAppFee;

    @ApiModelProperty("是否有申请截止")
    private Boolean isDeadInfo;

    @ApiModelProperty("是否有奖学金")
    private Boolean isScholarship;
}
