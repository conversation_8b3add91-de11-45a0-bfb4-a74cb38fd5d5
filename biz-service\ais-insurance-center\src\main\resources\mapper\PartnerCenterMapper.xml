<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.insurancecenter.mapper.PartnerCenterMapper">
    <insert id="insertExchangeRate">
        INSERT INTO app_partner_center.u_exchange_rate (fk_currency_type_num_from,
                                                        fk_currency_type_num_to,
                                                        exchange_rate,
                                                        get_date,
                                                        gmt_create,
                                                        gmt_create_user,
                                                        gmt_modified,
                                                        gmt_modified_user)
        VALUES (#{from},
                #{to},
                #{rate},
                CURRENT_DATE(),
                NOW(),
                'admin',
                NOW(),
                'admin')
    </insert>

    <select id="selectPartnerUserNameByIds" resultType="java.util.Map">
        SELECT id, name
        FROM app_partner_center.m_partner_user
        WHERE id IN
        <foreach collection="partnerUserIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectAgentByIds" resultType="com.get.insurancecenter.vo.agent.AgentEntity">
        select * from ais_sale_center.m_agent
        where id in
        <foreach collection="agentIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectCompanyByIds" resultType="com.get.permissioncenter.vo.CompanyVo">
        select id, num,name from ais_permission_center.m_company
        where id in
        <foreach collection="companyIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getExchangeRate" resultType="java.math.BigDecimal">
        select exchange_rate
        from app_partner_center.u_exchange_rate
        where fk_currency_type_num_from = #{from}
          and fk_currency_type_num_to = #{to}
          and get_date = CURRENT_DATE()
        order by id desc
        limit 1;
    </select>
</mapper>