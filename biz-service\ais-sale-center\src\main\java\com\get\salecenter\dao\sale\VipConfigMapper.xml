<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.VipConfigMapper">
    <select id="getVipConfigs" resultType="com.get.salecenter.entity.VipConfig">
        SELECT * FROM u_vip_config
        <where>
            <if test="vipConfigDto.fkCompanyId != null">
                AND fk_company_id = #{vipConfigDto.fkCompanyId}
            </if>
            <if test="(countryIds != null and countryIds.size() > 0) and !(providerIds != null and providerIds.size() > 0)">
                AND fk_table_name = 'u_area_country' AND fk_table_id IN
                <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">
                    #{countryId}
                </foreach>
            </if>
            <if test="(providerIds != null and providerIds.size() > 0) and !(countryIds != null and countryIds.size() > 0)">
                AND fk_table_name = 'm_institution_provider' AND fk_table_id IN
                <foreach collection="providerIds" item="providerId" open="(" separator="," close=")">
                    #{providerId}
                </foreach>
            </if>

            <if test="(countryIds != null and countryIds.size() > 0) and (providerIds != null and providerIds.size() > 0)">
                AND(
                (fk_table_name = 'u_area_country' AND fk_table_id IN
                    <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">#{countryId}
                    </foreach>
                    )
                OR
                (fk_table_name = 'm_institution_provider' AND fk_table_id IN
                <foreach collection="providerIds" item="providerId" open="(" separator="," close=")">#{providerId}
                </foreach>
                    )
                )
            </if>
        </where>
        ORDER BY view_order DESC
    </select>
    <select id="getMaxViewOrder" resultType="java.lang.Integer">
         select
     IFNULL(max(view_order)+1,0) view_order
    from
     u_vip_config
    </select>
</mapper>