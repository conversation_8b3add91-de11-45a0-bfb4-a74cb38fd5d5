package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/11/22 10:39
 * @verison: 1.0
 * @description:
 */
@Data
public class AppAgentUpdateDto extends BaseVoEntity {

    /**
     * 公司Id
     */
    @NotNull(message = "公司Id", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * BD员工Id
     */
    @NotNull(message = "BD员工Id", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "BD员工Id")
    private Long fkStaffId;

    /**
     * 国家Id
     */
    @NotNull(message = "国家Id", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;

    /**
     * 州省Id
     */
    @NotNull(message = "州省Id", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "州省Id")
    private Long fkAreaStateId;

    /**
     * 城市Id
     */
    @ApiModelProperty(value = "城市Id")
    private Long fkAreaCityId;

    /**
     * 代理名称
     */
    @NotBlank(message = "代理名称", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "代理名称")
    private String name;

    /**
     * 名称备注
     */
    @ApiModelProperty(value = "名称备注")
    private String nameNote;

    /**
     * 个人姓名
     */
    @ApiModelProperty(value = "个人姓名")
    private String personalName;

    /**
     * 代理昵称
     */
    @ApiModelProperty(value = "代理昵称")
    private String nickName;

    /**
     * 性质：公司/个人/工作室/国际学校/其他
     */
    @ApiModelProperty(value = "性质：公司/个人/工作室/国际学校/其他")
    private String nature;

    /**
     * 性质备注
     */
    @ApiModelProperty(value = "性质备注")
    private String natureNote;

    /**
     * 法定代表人
     */
    @ApiModelProperty(value = "法定代表人")
    private String legalPerson;

    /**
     * 税号/统一社会信用代码（公司）
     */
    @ApiModelProperty(value = "税号/统一社会信用代码（公司）")
    private String taxCode;

    /**
     * 身份证号（个人）
     */
    @ApiModelProperty(value = "身份证号（个人）")
    private String idCardNum;

    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    private String address;

    /**
     * 合同开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "合同开始时间", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "合同开始时间")
    private Date contractStartTime;

    /**
     * 合同结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "合同结束时间", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "合同结束时间")
    private Date contractEndTime;

    /**
     * 合同佣金联系邮箱
     */
    @ApiModelProperty(value = "合同佣金联系邮箱")
    private String email1;

    /**
     * 业务新闻接收邮箱
     */
    @ApiModelProperty(value = "业务新闻接收邮箱")
    private String email2;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 申请状态：0新申请/1审核中/2通过/3拒绝
     */
    @ApiModelProperty(value = "申请状态：0新申请/1审核中/2通过/3拒绝")
    private Integer appStatus;

    /**
     * 申请状态修改时间
     */
    @ApiModelProperty(value = "申请状态修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date appStatusModifyTime;

    /**
     * 申请状态修改人(登录账号)
     */
    @ApiModelProperty(value = "申请状态修改人(登录账号)")
    private String appStatusModifyUser;


    /**
     * 转化到代理Id
     */
    @ApiModelProperty(value = "转化到代理Id")
    private Long fkAgentId;

    @Valid
    private MediaAndAttachedDto mediaAndAttachedVo;

    private List<AgentIdCardDto> agentIdCardVos;

   
}
