package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.entity.ReceiptFormInvoice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ReceiptFormInvoiceMapper extends BaseMapper<ReceiptFormInvoice> {
    @Override
    int insert(ReceiptFormInvoice record);

    int insertSelective(ReceiptFormInvoice record);

    List<Long> getfkInvoiceIdsByfkReceiptFormId(@Param("fkReceiptFormId") Long fkReceiptFormId);

    List<ReceiptFormInvoice> getReceiptFormInvoiceByfkReceiptFormId(@Param("fkReceiptFormId") Long fkReceiptFormId);

    /**
     * 根据收款单id删除所有绑定
     *
     * @param fkReceiptFormId
     * @return
     */
    void deleteByReceiptFormId(@Param("fkReceiptFormId") Long fkReceiptFormId);


    Long getLastReceiptFormIdByInvoiceId(Long id);
}