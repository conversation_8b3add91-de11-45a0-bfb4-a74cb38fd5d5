package com.get.permissioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.permissioncenter.dto.StaffAreaCountryDto;
import com.get.permissioncenter.vo.StaffAreaCountryVo;
import com.get.permissioncenter.service.IStaffAreaCountryService;
import com.get.permissioncenter.dto.StaffAreaCountryBatchUpdateDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/22
 * @TIME: 14:29
 * @Description: 员工业务国家管理
 **/

@Api(tags = "员工业务国家管理")
@RestController
@RequestMapping("permission/areaCountry")
public class StaffAreaCountryController {

    @Resource
    private IStaffAreaCountryService areaCountryService;


    /**
     * 国家列表 根据用户id查询
     *
     * @param staffId
     * @return
     */
    @ApiOperation(value = "员工国家列表")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "权限中心/业务国家管理/国家列表")
    @GetMapping("/datas")
    public ResponseBo detailList(@RequestParam(value = "staffId") Long staffId, @RequestParam(value = "keyWord", required = false) String keyWord) {
        List<StaffAreaCountryVo> areaCountryDtos = areaCountryService.getStaffAreaCountryById(staffId, keyWord);
        return new ListResponseBo<>(areaCountryDtos);
    }


    /**
     * 修改信息
     *
     * @param staffId
     * @param staffAreaCountryDto
     * @return
     */
    @ApiOperation(value = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/业务国家管理/更新参数")
    @PostMapping("update")
    public ResponseBo update(@NotNull(message = "staffId不能为空") @RequestParam("staffId") Long staffId, @RequestBody List<StaffAreaCountryDto> staffAreaCountryDto) {
        return UpdateResponseBo.ok(areaCountryService.updateStaffAreaCountry(staffId, staffAreaCountryDto));
    }

    @ApiOperation(value = "批量修改业务国家")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/业务国家管理/批量修改业务国家")
    @PostMapping("batchUpdateStaffAreaCountry")
    public ResponseBo batchUpdateStaffAreaCountry(@RequestBody StaffAreaCountryBatchUpdateDto staffAreaCountryBatchUpdateDto) {
        areaCountryService.batchUpdateStaffAreaCountry(staffAreaCountryBatchUpdateDto);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 员工业务国家
     * @Param [staffId]
     * <AUTHOR>
     */
    @ApiIgnore
    @GetMapping("getStaffAreaCountryKeysByfkStaffId")
    public List<String> getStaffAreaCountryKeysByfkStaffId(@RequestParam(value = "staffId") Long staffId) {
        System.out.println("ydhgfhddddddddddddddddddddddddddddddddddddddddddddddddddd");
        List<String> staffCountry = areaCountryService.getStaffAreaCountryKeysByfkStaffId(staffId);
        return staffCountry;
    }

    /**
     * feign调用
     * 根据业务国家keys和员工ids获取对应员工
     *
     * @param staffIds
     * @param areaCountryKeys
     * @return
     */
    @ApiIgnore
    @PostMapping("getStaffByAreaCountryKeys")
    public List<Long> getStaffByAreaCountryKeys(@RequestParam("staffIds") List<Long> staffIds, @RequestParam("areaCountryKeys") List<String> areaCountryKeys) {
        return areaCountryService.getStaffByAreaCountryKeys(staffIds, areaCountryKeys);
    }

    @ApiOperation(value = "员工业务国家下拉")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "权限中心/业务国家管理/员工业务国家下拉")
    @GetMapping("getStaffAreaCountryKeysSelect")
    @VerifyPermission(IsVerify = false)
    public ResponseBo getStaffAreaCountryKeysSelect() {
        return new ListResponseBo(areaCountryService.getStaffAreaCountryKeysSelect());
    }


}
