package com.get.officecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.officecenter.vo.*;
import com.get.officecenter.vo.LeaveApplicationFormVo;
import com.get.officecenter.entity.LeaveApplicationForm;
import com.get.officecenter.service.ILeaveApplicationFormService;
import com.get.officecenter.dto.*;
import com.get.officecenter.dto.LeaveApplicationFormDto;
import com.get.officecenter.dto.query.LeaveApplicationFormQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/12 19:15
 * @verison: 1.0
 * @description:
 */
@Api(tags = "工休申请单管理")
@RestController
@RequestMapping("office/leaveApplicationForm")
public class LeaveApplicationFormController {
    @Resource
    private ILeaveApplicationFormService leaveApplicationFormService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.officecenter.vo.LeaveApplicationFormVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DETAIL, description = "办公中心/工休申请单管理/工休申请单详情")
    @GetMapping("/{id}")
    public ResponseBo<LeaveApplicationFormVo> detail(@PathVariable("id") Long id) {
        LeaveApplicationFormVo data = leaveApplicationFormService.findLeaveApplicationFormById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增信息
     * @Param [leaveApplicationFormVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.ADD, description = "办公中心/工休申请单管理/新增工休申请单")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(LeaveApplicationFormDto.Add.class) LeaveApplicationFormDto leaveApplicationFormDto) {
        return SaveResponseBo.ok(leaveApplicationFormService.addLeaveApplicationForm(leaveApplicationFormDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增信息
     * @Param [leaveApplicationFormDtos]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.ADD, description = "办公中心/工休申请单管理/批量新增工休申请单")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(LeaveApplicationFormDto.Add.class) ValidList<LeaveApplicationFormDto> leaveApplicationFormDtos) {
        leaveApplicationFormService.batchAddLeaveApplicationForm(leaveApplicationFormDtos);
        return SaveResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.officecenter.vo.LeaveApplicationFormVo>
     * @Description :修改信息
     * @Param [leaveApplicationFormDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.EDIT, description = "办公中心/工休申请单管理/更新工休申请单")
    @PostMapping("update")
    public ResponseBo<LeaveApplicationFormVo> update(@RequestBody @Validated(LeaveApplicationFormDto.Update.class) LeaveApplicationFormDto leaveApplicationFormDto) {
        return UpdateResponseBo.ok(leaveApplicationFormService.updateLeaveApplicationForm(leaveApplicationFormDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.officecenter.vo.LeaveApplicationFormVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/工休申请单管理/查询工休申请单")
    @PostMapping("datas")
    public ResponseBo<LeaveApplicationFormVo> datas(@RequestBody SearchBean<LeaveApplicationFormQueryDto> page) {
        List<LeaveApplicationFormVo> datas = leaveApplicationFormService.getLeaveApplicationForms(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedVo>
     * @Description : 查询工休申请单附件
     * @Param [voSearchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "查询工休申请单附件", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/工休申请单管理/查询附件")
    @PostMapping("getItemMedia")
    public ResponseBo<OfficeMediaAndAttachedVo> getItemMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<OfficeMediaAndAttachedVo> staffMedia = leaveApplicationFormService.getItemMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedVo>
     * @Description : 保存工休申请单附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "保存工休申请单附件")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.ADD, description = "办公中心/工休申请单管理/保存附件")
    @PostMapping("upload")
    public ResponseBo<OfficeMediaAndAttachedVo> addItemMedia(@RequestBody @Validated(MediaAndAttachedDto.Add.class) ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(leaveApplicationFormService.addItemMedia(mediaAttachedVo));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :开启工休申请单流程
     * @Param [businessKey, procdefKey]
     * <AUTHOR>
     */
    @ApiOperation(value = "开启工休申请单流程", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.ADD, description = "办公中心/工休申请单管理/开启工休申请单流程")
    @PostMapping("startProcess")
    public ResponseBo startProcess(@RequestParam("companyId") Long companyId, @RequestParam("businessKey") Long businessKey, @RequestParam("procdefKey") String procdefKey) {
        leaveApplicationFormService.startProcess(companyId, businessKey, procdefKey);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :作废接口
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "作废接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DELETE, description = "办公中心/付款单管理/作废")
    @GetMapping("updateStatus/{id}")
    public ResponseBo updateStatus(@PathVariable("id") Long id) {
        leaveApplicationFormService.updateStatus(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :作废接口
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "作废工休单接口（行政权限）", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DELETE, description = "办公中心/付款单管理/作废（行政权限）")
    @GetMapping("cancelLeaveApplicationForm/{id}")
    public ResponseBo cancelLeaveApplicationForm(@PathVariable("id") Long id) {
        leaveApplicationFormService.cancelLeaveApplicationForm(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :获取剩余天数
     * @Param []
     * <AUTHOR>
     */
    @ApiOperation(value = "获取剩余天数", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DETAIL, description = "办公中心/工休申请单管理/获取剩余天数")
    @PostMapping("getRemainingDays")
    public ResponseBo getRemainingDays(@RequestBody RemainingDayDto remainingDayDto) {
        RemainingDayVo remainingDayVo = leaveApplicationFormService.getRemainingDays(remainingDayDto);
        return new ResponseBo(remainingDayVo);
    }

    /**
     * @return String
     * @Description :根据表单id获取表单名
     * @Param [id]
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getLeaveTypeNameById")
    public String getLeaveTypeNameById(@RequestParam("id") Long id) {
        return leaveApplicationFormService.getLeaveTypeNameById(id);
    }

    /**
     * @return LeaveApplicationForm
     * @Description : fegin调用，根据表单id获取表单
     * @Param [id]
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getLeaveApplicationForm")
    public LeaveApplicationForm getLeaveApplicationForm(@RequestParam("id") Long id) {
        return leaveApplicationFormService.getLeaveApplicationForm(id);
    }


    /**
     * @return LeaveApplicationFormVo
     * @Description :撤单回显内容
     * @Param [id]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "撤单", notes = "id为被撤销工休单的id")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.ADD, description = "办公中心/工休申请单管理/撤单")
    @GetMapping("revokeLeaveApplicationForm/{id}")
    public ResponseBo<LeaveApplicationFormVo> revokeLeaveApplicationForm(@PathVariable("id") Long id) {
        LeaveApplicationFormVo data = leaveApplicationFormService.revokeLeaveApplicationForm(id);
        return new ResponseBo<>(data);
    }


    @ApiOperation(value = "获取上下班时间配置")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.ADD, description = "办公中心/工休申请单管理/获取上下班时间配置")
    @PostMapping("getTimeConfig")
    public ResponseBo<TimeConfigVo> getTimeConfig(@RequestBody TimeConfigDto timeConfigDto){
       return new ResponseBo<>(leaveApplicationFormService.getTimeConfig(timeConfigDto));
    }


    @ApiOperation(value = "计算工休申请时长")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.ADD, description = "办公中心/工休申请单管理/获取工休申请时长")
    @PostMapping("getApplicationFormDays")
    @VerifyLogin(IsVerify = false)
    public ResponseBo<ApplicationFormDaysVo> getApplicationFormDays(@RequestBody GetApplicationFormDaysDto getApplicationFormDaysDto){
        return new ResponseBo<>(leaveApplicationFormService.getApplicationFormDays(getApplicationFormDaysDto));
    }


    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DELETE, description = "办公中心/工休申请单管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        leaveApplicationFormService.delete(id);
        return DeleteResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "审批延迟配置", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DELETE, description = "办公中心/工休申请单管理/审批延迟配置")
    @PostMapping("getApprovalDelayConfig")
    public ResponseBo getApprovalDelayConfig(@RequestParam(value = "id",required = false)Long id) {
        return new ResponseBo<>(leaveApplicationFormService.getApprovalDelayConfig(id));
    }


    @ApiOperation(value = "发送企业微信消息-企业微信专用", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DELETE, description = "办公中心/工休申请单管理/发送企业微信消息-企业微信专用")
    @PostMapping("sendWxCpMessage")
    public ResponseBo sendWxCpMessage(@RequestParam(value = "id")Long id) throws Exception {
        Boolean aBoolean = leaveApplicationFormService.sendWxCpMessage(String.valueOf(id));
        return aBoolean?new ResponseBo<>():ResponseBo.error();
    }


}
