package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.InstitutionGroupMapper;
import com.get.institutioncenter.dto.InstitutionGroupDto;
import com.get.institutioncenter.vo.InstitutionGroupVo;
import com.get.institutioncenter.entity.InstitutionGroup;
import com.get.institutioncenter.service.IInstitutionGroupService;
import com.get.institutioncenter.utils.MyStringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @DATE: 2020/10/20
 * @TIME: 10:35
 * @Description:学校集团
 **/
@Service
public class InstitutionGroupServiceImpl extends BaseServiceImpl<InstitutionGroupMapper, InstitutionGroup> implements IInstitutionGroupService {
    @Resource
    private InstitutionGroupMapper institutionGroupMapper;
    @Resource
    private UtilService utilService;

    @Override
    public InstitutionGroupVo findInstitutionGroupById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionGroup institutionGroup = institutionGroupMapper.selectById(id);
        if (GeneralTool.isEmpty(institutionGroup)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionGroupVo institutionGroupVo = BeanCopyUtils.objClone(institutionGroup, InstitutionGroupVo::new);
        String fullName = institutionGroupVo.getName();
        if (GeneralTool.isNotEmpty(institutionGroupVo.getNameChn())) {
            fullName = fullName + "（" + institutionGroupVo.getNameChn() + "）";
        }
        institutionGroupVo.setFullName(fullName);
        return institutionGroupVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(ValidList<InstitutionGroupDto> institutionGroupDtos) {
        if (GeneralTool.isEmpty(institutionGroupDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (InstitutionGroupDto institutionGroupDto : institutionGroupDtos) {
            if (GeneralTool.isEmpty(institutionGroupDto.getId())) {
                if (validateAdd(institutionGroupDto)) {
                    InstitutionGroup institutionGroup = BeanCopyUtils.objClone(institutionGroupDto, InstitutionGroup::new);
                    utilService.updateUserInfoToEntity(institutionGroup);
                    institutionGroupMapper.insertSelective(institutionGroup);
                    //自动生成编号
                    institutionGroup.setNum(MyStringUtils.getGroupNum(institutionGroup.getId()));
                    institutionGroupMapper.updateById(institutionGroup);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("code_or_name_exists"));
                }
            } else {
                if (validateUpdate(institutionGroupDto)) {
                    InstitutionGroup institutionGroup = BeanCopyUtils.objClone(institutionGroupDto, InstitutionGroup::new);
                    utilService.updateUserInfoToEntity(institutionGroup);
                    institutionGroupMapper.updateById(institutionGroup);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("code_or_name_exists"));
                }
            }

        }
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (findInstitutionGroupById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        institutionGroupMapper.deleteById(id);
    }

    @Override
    public InstitutionGroupVo updateInstitutionGroup(InstitutionGroupDto institutionGroupDto) {
        if (institutionGroupDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        InstitutionGroup i = institutionGroupMapper.selectById(institutionGroupDto.getId());
        if (i == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (validateUpdate(institutionGroupDto)) {
            InstitutionGroup institutionGroup = BeanCopyUtils.objClone(institutionGroupDto, InstitutionGroup::new);
            utilService.updateUserInfoToEntity(institutionGroup);
            institutionGroupMapper.updateById(institutionGroup);
            return findInstitutionGroupById(institutionGroupDto.getId());
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("code_or_name_exists"));
        }
    }

    @Override
    public List<InstitutionGroupVo> getInstitutionGroups(InstitutionGroupDto institutionGroupDto, Page page) {
        IPage<InstitutionGroup> institutionGroupIPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<InstitutionGroup> institutionGroups = institutionGroupMapper.getInstitutionGroups(institutionGroupIPage, institutionGroupDto.getName());
        page.setAll((int) institutionGroupIPage.getTotal());

        List<InstitutionGroupVo> convertDatas = new ArrayList<>();
        for (InstitutionGroup institutionGroup : institutionGroups) {
            InstitutionGroupVo institutionGroupVo = BeanCopyUtils.objClone(institutionGroup, InstitutionGroupVo::new);
            String fullName = institutionGroupVo.getName();
            if (GeneralTool.isNotEmpty(institutionGroupVo.getNameChn())) {
                fullName = fullName + "（" + institutionGroupVo.getNameChn() + "）";
            }
            institutionGroupVo.setFullName(fullName);
            convertDatas.add(institutionGroupVo);
        }
        return convertDatas;
    }

    @Override
    public List<BaseSelectEntity> getInstitutionGroupSelect() {
        return institutionGroupMapper.getInstitutionGroupSelect();
    }

    /**
     * feign调用 通过集团ids 查找对应的集团名称map
     *
     * @Date 17:54 2021/6/3
     * <AUTHOR>
     */
    @Override
    public Map<Long, String> getInstitutionGroupNamesByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        LambdaQueryWrapper<InstitutionGroup> wrapper = new LambdaQueryWrapper();
        wrapper.in(InstitutionGroup::getId, ids);
        List<InstitutionGroup> institutionGroups = institutionGroupMapper.selectList(wrapper);
        if (GeneralTool.isEmpty(institutionGroups)) {
            return map;
        }
        for (InstitutionGroup institutionGroup : institutionGroups) {
            String name = GeneralTool.isEmpty(institutionGroup.getName()) ? "" : institutionGroup.getName();
            StringBuilder sb = new StringBuilder(name);
            //如果中文名称不为空，则名称继续拼接中文名称
            if (GeneralTool.isNotEmpty(institutionGroup.getNameChn())) {
                sb.append("（");
                sb.append(institutionGroup.getNameChn());
                sb.append("）");
            }
            map.put(institutionGroup.getId(), sb.toString());
        }
        return map;
    }

    private boolean validateAdd(InstitutionGroupDto institutionGroupDto) {
        LambdaQueryWrapper<InstitutionGroup> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionGroup::getName, institutionGroupDto.getName());
        List<InstitutionGroup> list = this.institutionGroupMapper.selectList(wrapper);
        return GeneralTool.isEmpty(list);
    }

    private boolean validateUpdate(InstitutionGroupDto institutionGroupDto) {
        LambdaQueryWrapper<InstitutionGroup> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionGroup::getName, institutionGroupDto.getName());
        List<InstitutionGroup> list = this.institutionGroupMapper.selectList(wrapper);
        return list.size() <= 0 || list.get(0).getId().equals(institutionGroupDto.getId());
    }
}
