package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.dto.ClientStaffDto;
import com.get.salecenter.service.ClientStaffService;
import com.get.salecenter.vo.ClientStaffVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "学生资源负责人管理")
@RequestMapping("sale/clientStaff")
public class ClientStaffController {
    @Resource
    private ClientStaffService clientStaffService;

    /**
     * @param page
     * 查询学生资源项目负责人
     * @return
     */
    @ApiOperation(value = "查询接口", notes = "")
    @PostMapping("getAllClientStaff")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生资源管理/学生负责人")
    public ResponseBo<ClientStaffVo> getAllClientStaff(@RequestBody SearchBean<ClientStaffDto> page) {
        List<ClientStaffVo> datas = clientStaffService.findAllByClientStaff(page.getData(),page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);

    }


    /**
     * 新增学生资源项目负责人
     *
     * @param clientStaffDto
     * @return
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生资源管理/学生负责人")
    @PostMapping("add")
    public ResponseBo add(@RequestBody ClientStaffDto clientStaffDto) {
        clientStaffService.addClientStaff(clientStaffDto);
        return SaveResponseBo.ok();
    }

    /**
     * 获取当前用户的下属selectList
     *
     * @param staffId
     * @return
     */
    @ApiOperation(value = "获取当前用户的下属selectList", notes = "")
    @GetMapping("getObtainSubordinateEmployeesSelect")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生资源管理/当前用户的下属selectList")
    public ResponseBo<BaseSelectEntity> getObtainSubordinateEmployeesSelect() {
        return new ListResponseBo<>(clientStaffService.getObtainSubordinateEmployeesSelect());
    }

    /**
     * 更新学生资源负责人
     *
     * @param clientStaffDto
     * @return
     */
    @ApiOperation(value = "更新接口", notes = "")
    @PutMapping("updateClientStaff")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生资源管理/学生负责人")
    public ResponseBo<ClientStaffVo> updateClientStaff(@RequestBody ClientStaffDto clientStaffDto) {
        clientStaffService.updateCLientStaff(clientStaffDto);
        return new ResponseBo<>();

    }


}
