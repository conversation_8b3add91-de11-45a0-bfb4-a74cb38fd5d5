package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/6/13 10:13
 * @desciption: 导出字段
 */
@Data
public class PayablePlanAuditSummaryExportVo {

    @ApiModelProperty(value = "财务归口所属公司")
    private String companyName;

    @ApiModelProperty(value = "审核状态")
    private String statusReviewName;

    @ApiModelProperty(value = "应付计划类型")
    private String fkTypeName;

    @ApiModelProperty(value = "学生信息")
    private String studentNameInfo;

    @ApiModelProperty(value = "国家/地区")
    private String fkAreaCountryName;

    @ApiModelProperty(value = "代理信息")
    private String fkAgentName;

    @ApiModelProperty(value = "业务信息")
    private String businessInformation;

    @ApiModelProperty(value = "应付币种")
    private String fkCurrencyTypeName;

    @ApiModelProperty(value = "学费")
    private BigDecimal tuitionAmount;

    @ApiModelProperty(value = "代理佣金费率")
    private BigDecimal commissionRate;

    @ApiModelProperty(value = "代理分成比率%")
    private BigDecimal splitRate;

//    @ApiModelProperty(value = "代理佣金金额")
//    private BigDecimal commissionAmount;

    @ApiModelProperty(value = "定额金额")
    private BigDecimal fixedAmount;

//    @ApiModelProperty(value = "奖励金额")
//    private BigDecimal bonusAmount;

    @ApiModelProperty(value = "应付金额")
    private BigDecimal payableAmount;

    @ApiModelProperty(value = "实付金额")
    private BigDecimal actualPayableAmount;

    /**
     * 差额
     */
    @ApiModelProperty(value = "应付未付")
    private BigDecimal diffPayableAmount;

    @ApiModelProperty(value = "申请状态")
    private String stepName;

    @ApiModelProperty("申请创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String itemGmtCreate;

    //审核人
    @ApiModelProperty(value = "审核人")
    private String payReviewName;

    //审核时间
    @ApiModelProperty("审核时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private String payReviewTime;
}
