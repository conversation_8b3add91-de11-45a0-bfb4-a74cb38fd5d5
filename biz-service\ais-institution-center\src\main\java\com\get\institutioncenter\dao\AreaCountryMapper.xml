<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.AreaCountryMapper">


  <select id="getCountryNameByKey" parameterType="java.lang.String" resultType="string">
    select
     c.name_chn
    from
     u_area_country c
    where
     c.num = #{key}
  </select>

  <select id="getCountryNameEnByKey" parameterType="java.lang.String" resultType="string">
    select
      c.name
    from
      u_area_country c
    where
      c.num = #{key}
  </select>


  <select id="getCountryNameById" resultType="java.lang.String">
    select
      CASE WHEN IFNULL(name_chn,'')='' THEN `name` ELSE CONCAT(`name`,'（',name_chn,'）') END fullName
    from
      u_area_country c
    where
      c.id = #{id}
  </select>
  <select id="getCountryList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    select id,num,name,name_chn,
    CASE WHEN IFNULL(name_chn,'')='' THEN `name` ELSE CONCAT(`name`,'（',name_chn,'）') END fullName
    from u_area_country
    <where>
        and id in
      <foreach collection="countryIds" item="countryId" index="index" open="(" separator="," close=")">
        #{countryId}
      </foreach>
    </where>
    order by view_order desc
  </select>
  <select id="getAllCountryList" resultType="com.get.institutioncenter.vo.AreaCountryVo">
    select id,num,name,name_chn,
    <!--name as fullName-->
    CASE WHEN IFNULL(name_chn,'')='' THEN `name` ELSE CONCAT(`name`,'（',name_chn,'）') END fullName
    from u_area_country  order by view_order desc
  </select>
  <select id="getCountryIdByKey" parameterType="java.lang.String" resultType="java.lang.Long">
    select
      c.id
    from
      u_area_country c
    where
      c.num = #{key}
  </select>

  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select
     IFNULL(max(view_order)+1,0) view_order
    from
     u_area_country

  </select>
  <select id="getCountryFullNameById" resultType="java.lang.String">
    select
      CASE WHEN IFNULL(name_chn,'')='' THEN `name` ELSE CONCAT(`name`,'（',name_chn,'）') END fullName
    from
      u_area_country c
    where
      c.id = #{id}
  </select>

  <select id="getCountryNameChnById" resultType="java.lang.String">
    select c.name_chn
    from
      u_area_country c
    where
      c.id = #{id}
  </select>
    <select id="getAreaCountryIds" resultType="java.lang.Long">
      select id from u_area_country where ${columnName} is not NULL and ${columnName} != ""
    </select>
  <select id="getAreaCountryById" resultType="com.get.institutioncenter.entity.AreaCountry">
    select * from u_area_country where id = #{id}
  </select>

  <select id="selectPublicCountryHomeNums" resultType="java.util.Map">
    select `name` as name ,name_chn as zhCn,num as id from u_area_country where FIND_IN_SET(#{publicLevel}, public_level) order by view_order desc
  </select>

  <select id="getCountryNameAndNumById" resultType="java.lang.String">
    SELECT
      CONCAT(
              "[",
              c.num,
              "] ",
              CASE

                WHEN IFNULL( name_chn, '' )= '' THEN
                  `name` ELSE CONCAT( `name`, '（', name_chn, '）' )
                END
        ) fullName
    FROM
      u_area_country c
    where c.id = #{id}
  </select>
  <select id="getCountryByKey" resultType="com.get.institutioncenter.vo.AreaCountryVo">
    SELECT num AS num,name AS name,name_chn as nameChn,
    CASE WHEN IFNULL(name_chn,'')='' THEN `name` ELSE CONCAT(`name`,'（',name_chn,'）') END fullName,
    id AS id
    FROM u_area_country
    <where>
      <if test="keys!=null">
        num IN
        <foreach collection="keys" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
    order by view_order desc
  </select>

  <select id="getCountryByName" resultType="java.lang.Long">
    SELECT id FROM u_area_country WHERE name LIKE CONCAT('%',#{name},'%') OR name_chn LIKE CONCAT('%',#{name},'%')
  </select>

  <select id="getAreaCode" resultType="com.get.institutioncenter.vo.AreaCountryVo">
    SELECT
      GROUP_CONCAT( areaCodeValue ) as areaCodeValue,
      area_code
    FROM
      (
        SELECT
          CONCAT( name_chn, ' +', area_code ) AS areaCodeValue,
          area_code
        FROM
          u_area_country
        WHERE
          area_code IS NOT NULL
          AND area_code != ''
        ORDER BY
          view_order DESC
      ) a
    GROUP BY
      a.area_code
    </select>
    <select id="getCurrencyNumByName" resultType="java.lang.String">
      SELECT
	    fk_currency_type_num
      FROM
	    u_area_country
      WHERE
	    name = #{name}
    </select>
    <select id="getAreaCountryListByKeyWord" resultType="com.get.core.mybatis.base.BaseSelectEntity">
      SELECT
        id,
        NAME AS NAME,
        name_chn AS nameChn,
        CASE
      WHEN IFNULL(name_chn, '') = '' THEN
          `name`
      ELSE
          CONCAT(
              `name`,
              '（',
              name_chn,
              '）'
          )
      END fullName
    FROM
      u_area_country
    WHERE
      <if test="countryIds!=null and countryIds.size>0">
        id IN
        <foreach collection="countryIds" open="(" separator="," close=")" item="cid">
          #{cid}
        </foreach>
      </if>
    <if test="keyword!=null and keyword!=''">
      AND (`name` LIKE concat('%',#{keyword},'%') OR name_chn LIKE concat('%',#{keyword},'%'))
    </if>
      LIMIT 50
    </select>

  <select id="getExistsOfferItemAreaCountryList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    SELECT
      uac.id,
      uac.num,
      uac.NAME,
      uac.name_chn,
      CASE

        WHEN IFNULL( uac.name_chn, '' )= '' THEN
          uac.NAME ELSE CONCAT( uac.NAME, '（', uac.name_chn, '）' )
        END fullName
    FROM
      u_area_country AS uac
        INNER JOIN (
        SELECT
          msoi.fk_area_country_id
        FROM
          ais_sale_center.m_student_offer_item AS msoi
            INNER JOIN ais_sale_center.m_student AS ms ON ms.id = msoi.fk_student_id
            INNER JOIN ais_sale_center.m_agent AS a ON  a.id = msoi.fk_agent_id
            LEFT JOIN ais_sale_center.r_staff_bd_code AS rsbc ON rsbc.fk_staff_id = msoi.fk_staff_id
        WHERE
          ms.fk_company_id = #{companyId} AND msoi.STATUS = 1 AND rsbc.bd_code NOT LIKE 'T%'
        AND msoi.fk_area_country_id IN
        <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">
          #{countryId}
        </foreach>
        GROUP BY
          msoi.fk_area_country_id ) a ON a.fk_area_country_id = uac.id
    ORDER BY
      view_order DESC

  </select>
  <select id="getExistsAgentOfferItemAreaCountryList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    SELECT
      uac.id,
      uac.num,
      uac.NAME,
      uac.name_chn,
      CASE

        WHEN IFNULL( uac.name_chn, '' )= '' THEN
          uac.NAME ELSE CONCAT( uac.NAME, '（', uac.name_chn, '）' )
        END fullName
    FROM
      u_area_country AS uac
        INNER JOIN (
        SELECT
          a.fk_area_country_id
        FROM
          ais_sale_center.m_student_offer_item AS msoi
            INNER JOIN ais_sale_center.m_student AS ms ON ms.id = msoi.fk_student_id
            INNER JOIN ais_sale_center.m_agent AS a ON  a.id = msoi.fk_agent_id
            LEFT JOIN ais_sale_center.r_staff_bd_code AS rsbc ON rsbc.fk_staff_id = msoi.fk_staff_id
        WHERE
          ms.fk_company_id = #{companyId} AND msoi.STATUS = 1 AND rsbc.bd_code NOT LIKE 'T%'
        AND msoi.fk_area_country_id IN
        <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">
          #{countryId}
        </foreach>
        GROUP BY
          a.fk_area_country_id ) a ON a.fk_area_country_id = uac.id
    ORDER BY
      view_order DESC
  </select>
    <select id="getExistsAgentAreaCountryList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
      SELECT
      uac.id,
      uac.num,
      uac.NAME,
      uac.name_chn,
      CASE

      WHEN IFNULL( uac.name_chn, '' )= '' THEN
      uac.NAME ELSE CONCAT( uac.NAME, '（', uac.name_chn, '）' )
      END fullName
      FROM
      u_area_country AS uac
      INNER JOIN ais_sale_center.m_agent AS a ON a.fk_area_country_id = uac.id
      INNER JOIN ais_sale_center.r_agent_company AS rac ON rac.fk_agent_id = a.id
      where uac.id IN
      <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">
        #{countryId}
      </foreach>
      AND rac.fk_company_id = #{companyId}

      GROUP BY uac.id
      ORDER BY
      view_order DESC
    </select>

  <select id="getNewsAreaCountryList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    SELECT
    DISTINCT
      uac.id,
      uac.num,
      uac.NAME,
      uac.name_chn,
      CASE

        WHEN IFNULL( uac.name_chn, '' )= '' THEN
          uac.NAME ELSE CONCAT( uac.NAME, '（', uac.name_chn, '）' )
        END fullName
    FROM
      u_area_country AS uac
      INNER JOIN r_news_type AS rnt ON uac.id = rnt.fk_table_id AND rnt.fk_table_name = 'u_area_country'
      ORDER BY
      view_order DESC
  </select>
  <select id="getCountryByPublicLevel" resultType="com.get.institutioncenter.vo.AreaCountryVo">
      SELECT
        id, num, name, name_chn,
        CASE
          WHEN IFNULL(name_chn, '') = '' THEN `name`
          ELSE CONCAT(`name`, '（', name_chn, '）')
          END AS fullName,
        view_order
      FROM u_area_country
      WHERE FIND_IN_SET(#{publicLevel}, public_level)
    ORDER BY view_order DESC;
  </select>

  <select id="getAreaCountryListHti" resultType="com.get.institutioncenter.vo.AreaCountryHtiVo">
    SELECT
      uac.id,
      uac.num,
      uac.NAME,
      uac.name_chn,
      CASE

        WHEN IFNULL( uac.name_chn, '' )= '' THEN
          uac.NAME ELSE CONCAT( uac.NAME, '（', uac.name_chn, '）' )
        END fullName
    FROM
      u_area_country AS uac
    GROUP BY
      uac.id
    ORDER BY
      view_order DESC
  </select>
  <select id="getCommonAreaCodes" resultType="com.get.institutioncenter.vo.AreaCountryVo">
    SELECT
      GROUP_CONCAT(areaCodeValue ORDER BY sort_order DESC) AS areaCodeValue,
      area_code
    FROM (
           SELECT
             CONCAT(name, ' +', area_code) AS areaCodeValue,
             area_code,
             view_order AS sort_order
           FROM
             u_area_country u
           WHERE
             area_code IS NOT NULL
             AND area_code != ''
        AND FIND_IN_SET(11, public_level)
         ) a
    GROUP BY a.area_code;
  </select>
  <select id="getCountryByPublicLevelBySummit" resultType="com.get.institutioncenter.vo.AreaCountryVo">
    (
      SELECT
        id, num, name, name_chn,
        CASE
          WHEN IFNULL(name_chn, '') = '' THEN `name`
          ELSE CONCAT(`name`, '（', name_chn, '）')
          END AS fullName,
        view_order
      FROM u_area_country
      WHERE FIND_IN_SET(#{publicLevel}, public_level)
    )

    UNION ALL

    (
      SELECT
        id, num, name, name_chn,
        CASE
          WHEN IFNULL(name_chn, '') = '' THEN `name`
          ELSE CONCAT(`name`, '（', name_chn, '）')
          END AS fullName,
        view_order
      FROM u_area_country
      WHERE id = 34
    )

    ORDER BY view_order DESC;
  </select>

</mapper>