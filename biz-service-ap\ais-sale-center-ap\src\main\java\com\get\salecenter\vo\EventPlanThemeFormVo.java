package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2023/12/12
 * @TIME: 18:30
 * @Description:
 **/
@Data
public class EventPlanThemeFormVo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "线上项目Id")
    private Long fkEventPlanThemeOnlineId;

    @ApiModelProperty(value = "线下项目Id")
    private Long fkEventPlanThemeOfflineId;

    @ApiModelProperty(value = "线下项目子项Id")
    private Long fkEventPlanThemeOfflineItemId;

    @ApiModelProperty(value = "线下专访Id")
    private Long fkEventPlanThemeWorkShopId;

    @ApiModelProperty(value = "展示类型：线上活动1/线下活动2/线下专坊3")
    private Integer displayType;

    @ApiModelProperty(value = "展示类型名称：线上活动/线下活动/线下专坊")
    private String displayTypeName;

    @ApiModelProperty(value = "主标题")
    private String mainTitle;

    @ApiModelProperty(value = "副标题")
    private String subTitle;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "是否激活：0否/1是，若否需要灰掉整个主题")
    private Boolean isActive;


    @ApiModelProperty(value = "项目名称")
    private String name;

    @ApiModelProperty(value = "收费")
    private String fee;

    @ApiModelProperty(value = "活动适用业务国家地区名称（表格展示名称）")
    private String areaCountryName;


    @ApiModelProperty(value = "地点")
    private String location;

    @ApiModelProperty(value = "日期")
    private String date;

    @ApiModelProperty(value = "时长")
    private String duration;

    @ApiModelProperty(value = "规模")
    private String scale;

    @ApiModelProperty(value = "前端表格勾选占位符")
    private String fill;

    @ApiModelProperty(value = "说明")
    private String note;

    @ApiModelProperty(value = "是否已报名(是否勾选)")
    private Boolean isSelected;
}
