package com.get.institutioncenter.dto.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class NewsQueryDto {
    @ApiModelProperty(value = "所属公司id")
    private Long companyId;

    /**
     * 新闻类型Id
     */
    @ApiModelProperty(value = "新闻类型Id", required = true)
    private Long fkNewsTypeId;

    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id", required = true)
    private Long fkTableId;

    /**
     * 表名
     */
    @ApiModelProperty(value = "表名", required = true)
    private String fkTableName;

    @ApiModelProperty(value = "关键词")
    private String keyWord;

    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @NotBlank(message = "公开对象")
    private String publicLevel;

    @ApiModelProperty(value = "目标名称")
    private String targetName;

    //=============新增属性==================
    @ApiModelProperty(value = "排序方式。1是按最新编辑时间，2是最新创建时间，3是最新发布时间")
    private String sortOrder;
}
