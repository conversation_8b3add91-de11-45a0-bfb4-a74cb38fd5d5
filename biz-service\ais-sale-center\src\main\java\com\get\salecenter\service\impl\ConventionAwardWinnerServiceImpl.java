package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.ConventionAwardCodeMapper;
import com.get.salecenter.dao.sale.ConventionAwardMapper;
import com.get.salecenter.dao.sale.ConventionAwardWinnerMapper;
import com.get.salecenter.dto.ConventionAwardWinnerDto;
import com.get.salecenter.dto.LuckDrawDto;
import com.get.salecenter.entity.ConventionAward;
import com.get.salecenter.entity.ConventionAwardCode;
import com.get.salecenter.entity.ConventionAwardWinner;
import com.get.salecenter.entity.ConventionPerson;
import com.get.salecenter.service.*;
import com.get.salecenter.vo.ConventionAwardWinnerVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2021/9/13 11:08
 * @verison: 1.0
 * @description:中奖名单管理实现类
 */
@Service
public class ConventionAwardWinnerServiceImpl implements IConventionAwardWinnerService {

    @Resource
    private ConventionAwardWinnerMapper conventionAwardWinnerMapper;
    @Resource
    private IConventionAwardService iConventionAwardService;
    @Resource
    private IConventionAwardCodeService iConventionAwardCodeService;
    @Resource
    private IConventionPersonService iConventionPersonService;
    @Resource
    private IMediaAndAttachedService mediaAndAttachedService;
    @Resource
    private UtilService utilService;
    @Resource
    private ConventionAwardCodeMapper conventionAwardCodeMapper;
    @Resource
    private ConventionAwardMapper conventionAwardMapper;

    /**
     * @Description: 列表数据
     * @Author: Jerry
     * @Date:16:04 2021/9/15
     */
    @Override
    public List<ConventionAwardWinnerVo> datas(ConventionAwardWinnerDto conventionAwardWinnerDto) {
        List<ConventionAwardWinnerVo> conventionAwardWinnerVos = new ArrayList<>();
//        Example example = new Example(ConventionAwardWinner.class);
//        Example.Criteria criteria = example.createCriteria();
//        //搜索关键字
//        String keyword = conventionAwardWinnerDto.getKeyword();
//        if(GeneralTool.isNotEmpty(keyword)){
//            //根据名称模糊搜索奖品ids
//            Set<Long> conventionAwardsIds = iConventionAwardService.getConventionAwardsIdsByConventionAwardsName(keyword);
//            if(GeneralTool.isNotEmpty(conventionAwardsIds)){
//                criteria.orIn("fkConventionAwardId",conventionAwardsIds);
//            }
//            //根据名称模糊搜索抽奖号码ids
//            Set<Long> conventionAwardCodesIds = iConventionAwardCodeService.getConventionAwardCodesIdsByConventionAwardCodesName(keyword);
//            if(GeneralTool.isNotEmpty(conventionAwardCodesIds)){
//                criteria.orIn("fkConventionAwardCodeId",conventionAwardCodesIds);
//            }
//            //根据名称模糊搜索参展人员ids
//            Set<Long> conventionPersonIds = iConventionPersonService.getConventionPersonIdsByName(keyword);
//            if(GeneralTool.isNotEmpty(conventionPersonIds)){
//                criteria.orIn("fkConventionPersonId",conventionPersonIds);
//            }
//            if(GeneralTool.isEmpty(conventionAwardsIds) && GeneralTool.isEmpty(conventionAwardCodesIds) && GeneralTool.isEmpty(conventionPersonIds)){
//                return conventionAwardWinnerVos;
//            }
//        }
//        if(GeneralTool.isNotEmpty(conventionAwardWinnerDto.getFkConventionId())){
//            Example.Criteria criteria1 = example.createCriteria();
//            criteria1.andEqualTo("fkConventionId",conventionAwardWinnerDto.getFkConventionId());
//            example.and(criteria1);
//        }
//        List<ConventionAwardWinner> conventionAwardWinners = conventionAwardWinnerMapper.selectByExample(example);
        LambdaQueryWrapper<ConventionAwardWinner> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(conventionAwardWinnerDto.getFkConventionId())) {
            lambdaQueryWrapper.eq(ConventionAwardWinner::getFkConventionId, conventionAwardWinnerDto.getFkConventionId());
        }
        //搜索关键字
        String keyword = conventionAwardWinnerDto.getKeyword();
        //根据名称模糊搜索奖品ids
        Set<Long> conventionAwardsIds = iConventionAwardService.getConventionAwardsIdsByConventionAwardsName(keyword);
        //根据名称模糊搜索抽奖号码ids
        Set<Long> conventionAwardCodesIds = iConventionAwardCodeService.getConventionAwardCodesIdsByConventionAwardCodesName(keyword);
        //根据名称模糊搜索参展人员ids
        Set<Long> conventionPersonIds = iConventionPersonService.getConventionPersonIdsByName(keyword);
        if (GeneralTool.isNotEmpty(keyword)) {
            lambdaQueryWrapper.and(wrapper -> {
                if (GeneralTool.isNotEmpty(conventionAwardsIds)) {
                    wrapper.or().in(ConventionAwardWinner::getFkConventionAwardId, conventionAwardsIds);
                }
                if (GeneralTool.isNotEmpty(conventionAwardCodesIds)) {
                    wrapper.or().in(ConventionAwardWinner::getFkConventionAwardCodeId, conventionAwardCodesIds);
                }
                if (GeneralTool.isNotEmpty(conventionPersonIds)) {
                    wrapper.or().in(ConventionAwardWinner::getFkConventionPersonId, conventionPersonIds);
                }
            });
        }
        if (GeneralTool.isEmpty(conventionAwardsIds) && GeneralTool.isEmpty(conventionAwardCodesIds) && GeneralTool.isEmpty(conventionPersonIds)) {
            return Collections.emptyList();
        }
        List<ConventionAwardWinner> conventionAwardWinners = conventionAwardWinnerMapper.selectList(lambdaQueryWrapper);

        if (GeneralTool.isEmpty(conventionAwardWinners)) {
            return conventionAwardWinnerVos;
        }
        //表ids
        Set<Long> fkTableIds = new HashSet<>();
        //奖品ids
        Set<Long> conventionAwardsIds_ = new HashSet<>();
        //抽奖号码ids
        Set<Long> conventionAwardCodeIds_ = new HashSet<>();
        //中奖人员ids
        Set<Long> conventionPersonIds_ = new HashSet<>();
        for (ConventionAwardWinner conventionAwardWinner : conventionAwardWinners) {
            fkTableIds.add(conventionAwardWinner.getId());
            conventionAwardsIds_.add(conventionAwardWinner.getFkConventionAwardId());
            conventionAwardCodeIds_.add(conventionAwardWinner.getFkConventionAwardCodeId());
            conventionPersonIds_.add(conventionAwardWinner.getFkConventionPersonId());
        }


        Map<Long, String> conventionAwardsNameMap = new HashMap<>();
        Map<Long, List<MediaAndAttachedVo>> mediaAndAttachedDtoByFkTableIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(conventionAwardsIds_)) {
            //根据奖品ids获取名称
            conventionAwardsNameMap = iConventionAwardService.getConventionAwardsNameByConventionAwardsIds(conventionAwardsIds_);
            //根据表ids获取批量的附件
            mediaAndAttachedDtoByFkTableIds = mediaAndAttachedService.getMediaAndAttachedDtoByFkTableIds(TableEnum.SALE_CONVENTION_AWARD.key, conventionAwardsIds_);
        }
        //根据抽奖号码ids获取对象
        Map<Long, ConventionAwardCode> conventionAwardCodeMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(conventionAwardCodeIds_)) {
            conventionAwardCodeMap = iConventionAwardCodeService.getConventionAwardCodesByConventionAwardCodesIds(conventionAwardCodeIds_);
        }
        //根据中奖人员ids获取对象
        Map<Long, ConventionPerson> conventionPersonMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(conventionPersonIds_)) {
            conventionPersonMap = iConventionPersonService.getConventionPersonByConventionPersonIds(conventionPersonIds_);
        }
        for (ConventionAwardWinner conventionAwardWinner : conventionAwardWinners) {
            ConventionAwardWinnerVo conventionAwardWinnerVo = BeanCopyUtils.objClone(conventionAwardWinner, ConventionAwardWinnerVo::new);
            //获取附件
            List<MediaAndAttachedVo> mediaAndAttachedVos = mediaAndAttachedDtoByFkTableIds.get(conventionAwardWinner.getFkConventionAwardId());
            //奖品图片
            conventionAwardWinnerVo.setMediaAndAttachedDtoList(mediaAndAttachedVos);
            //奖品名称
            conventionAwardWinnerVo.setFkConventionAwardName(conventionAwardsNameMap.get(conventionAwardWinnerVo.getFkConventionAwardId()));

            ConventionAwardCode conventionAwardCode = conventionAwardCodeMap.get(conventionAwardWinnerVo.getFkConventionAwardCodeId());
            if (GeneralTool.isNotEmpty(conventionAwardCode)) {
                //中奖码
                conventionAwardWinnerVo.setFkConventionAwardCode(conventionAwardCode.getAwardCode());
            }

            ConventionPerson conventionPerson = conventionPersonMap.get(conventionAwardWinnerVo.getFkConventionPersonId());
            if (GeneralTool.isNotEmpty(conventionPerson)) {
                StringBuilder sb = new StringBuilder();
                //名字（角色）
                sb.append(conventionPerson.getNameChn());
                String type = ProjectExtraEnum.getValueByKey(conventionPerson.getType(), ProjectExtraEnum.CONVENTION_PERSON_TYPE);
                if (GeneralTool.isNotEmpty(type)) {
                    sb.append("（");
                    sb.append(type);
                    sb.append("）");
                }
                conventionAwardWinnerVo.setFkConventionPersonName(sb.toString());
            }
            //中奖者
            conventionAwardWinnerVos.add(conventionAwardWinnerVo);
        }
        return conventionAwardWinnerVos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLottery(ConventionAwardWinnerDto conventionAwardWinnerDto) {
//        Example example = new Example(ConventionAwardWinner.class);
//        example.createCriteria().andEqualTo("fkConventionAwardId", conventionAwardWinnerDto.getFkConventionAwardId());
//        List<ConventionAwardWinner> conventionAwardWinners =  conventionAwardWinnerMapper.selectByExample(example);

        LambdaQueryWrapper<ConventionAwardWinner> lambdaQueryWrapper = Wrappers.<ConventionAwardWinner>lambdaQuery().eq(ConventionAwardWinner::getFkConventionAwardId, conventionAwardWinnerDto.getFkConventionAwardId());
        List<ConventionAwardWinner> conventionAwardWinners = conventionAwardWinnerMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(conventionAwardWinners)) {
            for (ConventionAwardWinner conventionAwardWinner : conventionAwardWinners) {
                ConventionAwardCode cac = conventionAwardCodeMapper.getById(conventionAwardWinner.getFkConventionAwardCodeId());
                cac.setUseType(1);
                utilService.updateUserInfoToEntity(cac);
                conventionAwardCodeMapper.updateById(cac);
            }
        }
        conventionAwardWinnerMapper.delete(lambdaQueryWrapper);
        ConventionAwardCode conventionAwardCode = conventionAwardCodeMapper.getById(conventionAwardWinnerDto.getFkConventionAwardCodeId());
        if (GeneralTool.isEmpty(conventionAwardCode)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("unable_to_find_lottery_ticket"));
        }
        conventionAwardCode.setUseType(2);
        conventionAwardCodeMapper.updateById(conventionAwardCode);
        ConventionAwardWinner caw = BeanCopyUtils.objClone(conventionAwardWinnerDto, ConventionAwardWinner::new);
        utilService.updateUserInfoToEntity(caw);
        caw.setFkConventionPersonId(conventionAwardCode.getFkConventionPersonId());
        caw.setFkConventionAwardCodeId(conventionAwardCode.getId());
        conventionAwardWinnerMapper.insert(caw);
    }



    /**
     * 2022年峰会抽奖
     *
     * @Date 16:59 2023/2/2
     * <AUTHOR>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ConventionAwardCode> luckDraw(LuckDrawDto luckDrawDto) {
        //奖品信息
        ConventionAward conventionAward = conventionAwardMapper.selectOne(Wrappers.<ConventionAward>lambdaQuery()
                .eq(ConventionAward::getFkConventionId, luckDrawDto.getFkConventionId())
                .eq(ConventionAward::getId, luckDrawDto.getFkConventionAwardId()));
        //获奖code
        List<ConventionAwardCode> winningCodeList = new ArrayList<>();
        for (int i = 0; i < luckDrawDto.getLotteryCount(); i++) {
            //可中奖号码
            List<ConventionAwardCode> conventionAwardCodeList;
            if (GeneralTool.isNotEmpty(conventionAward.getGetFkConventionPersonId())) {
                conventionAwardCodeList = conventionAwardCodeMapper.getCheatingNumber(luckDrawDto.getFkConventionId(), conventionAward.getGetFkConventionPersonId(), luckDrawDto);
            } else {
                conventionAwardCodeList = conventionAwardCodeMapper.getLuckyNumber(luckDrawDto.getFkConventionId(),
                        null, conventionAward.getGetRole());
            }
            if (GeneralTool.isEmpty(conventionAwardCodeList)) {
                conventionAwardCodeList = conventionAwardCodeMapper.getLuckyNumber(luckDrawDto.getFkConventionId(),
                        null, null);
            }
            if (GeneralTool.isEmpty(conventionAwardCodeList)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_valid_lottery_tickets_available"));
            }
            Random rand = new Random();
            //中奖号码
            ConventionAwardCode conventionAwardCode = conventionAwardCodeList.get(rand.nextInt(conventionAwardCodeList.size()));
            conventionAwardCode.setUseType(2);
            utilService.setUpdateInfo(conventionAwardCode);
            conventionAwardCodeMapper.updateById(conventionAwardCode);
            //中奖记录
            ConventionAwardWinner conventionAwardWinner = new ConventionAwardWinner();
            conventionAwardWinner.setFkConventionId(luckDrawDto.getFkConventionId());
            conventionAwardWinner.setFkConventionAwardId(luckDrawDto.getFkConventionAwardId());
            conventionAwardWinner.setFkConventionAwardCodeId(conventionAwardCode.getId());
            conventionAwardWinner.setFkConventionPersonId(conventionAwardCode.getFkConventionPersonId());
            utilService.setCreateInfo(conventionAwardWinner);
            conventionAwardWinnerMapper.insert(conventionAwardWinner);
            winningCodeList.add(conventionAwardCode);
        }
        return winningCodeList;
    }

    /**
     * 2022年峰会重新抽奖
     *
     * @Date 11:23 2023/2/6
     * <AUTHOR>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ConventionAwardCode> luckDrawAgain(LuckDrawDto luckDrawDto) {
        //奖品信息
        ConventionAward conventionAward = conventionAwardMapper.selectOne(Wrappers.<ConventionAward>lambdaQuery()
                .eq(ConventionAward::getFkConventionId, luckDrawDto.getFkConventionId())
                .eq(ConventionAward::getId, luckDrawDto.getFkConventionAwardId()));

        List<Long> redrawAwardCodeIds = null;
        if (GeneralTool.isNotEmpty(luckDrawDto.getAwardCodeList())) {
            List<ConventionAwardCode> conventionAwardCodes = conventionAwardCodeMapper.selectList(Wrappers.<ConventionAwardCode>lambdaQuery().in(ConventionAwardCode::getAwardCode, luckDrawDto.getAwardCodeList()));
            redrawAwardCodeIds = conventionAwardCodes.stream().map(ConventionAwardCode::getId).collect(Collectors.toList());
        }

        //删除该奖品原有中奖信息
        LambdaQueryWrapper<ConventionAwardWinner> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ConventionAwardWinner::getFkConventionId, luckDrawDto.getFkConventionId())
                .eq(ConventionAwardWinner::getFkConventionAwardId, luckDrawDto.getFkConventionAwardId());
        if (GeneralTool.isNotEmpty(redrawAwardCodeIds)) {
            lambdaQueryWrapper.in(ConventionAwardWinner::getFkConventionAwardCodeId, redrawAwardCodeIds);
        }

        List<ConventionAwardWinner> conventionAwardWinners = conventionAwardWinnerMapper.selectList(lambdaQueryWrapper);
        conventionAwardWinnerMapper.delete(lambdaQueryWrapper);
        //把抽奖code变回来
        Set<Long> awardCodeIds = new HashSet<>();
        for (ConventionAwardWinner conventionAwardWinner : conventionAwardWinners) {
            List<ConventionAwardCode> conventionAwardCodeList = conventionAwardCodeMapper.selectList(Wrappers.<ConventionAwardCode>lambdaQuery()
                    .eq(ConventionAwardCode::getId, conventionAwardWinner.getFkConventionAwardCodeId())
                    .eq(ConventionAwardCode::getFkConventionId, luckDrawDto.getFkConventionId()));
            for (ConventionAwardCode conventionAwardCode : conventionAwardCodeList) {
                conventionAwardCode.setUseType(1);
                utilService.setUpdateInfo(conventionAwardCode);
                conventionAwardCodeMapper.updateById(conventionAwardCode);
                awardCodeIds.add(conventionAwardCode.getId());
            }
        }
        //获奖code
        List<ConventionAwardCode> winningCodeList = new ArrayList<>();
        for (int i = 0; i < luckDrawDto.getLotteryCount(); i++) {
            //可中奖号码
            List<ConventionAwardCode> conventionAwardCodeList;
            if (GeneralTool.isNotEmpty(conventionAward.getGetFkConventionPersonId())) {
                conventionAwardCodeList = conventionAwardCodeMapper.getCheatingNumber(luckDrawDto.getFkConventionId(), conventionAward.getGetFkConventionPersonId(), luckDrawDto);
            } else {
                conventionAwardCodeList = conventionAwardCodeMapper.getLuckyNumber(luckDrawDto.getFkConventionId(),
                        awardCodeIds,
                        conventionAward.getGetRole());
            }
            if (GeneralTool.isEmpty(conventionAwardCodeList)) {
                conventionAwardCodeList = conventionAwardCodeMapper.getLuckyNumber(luckDrawDto.getFkConventionId(),
                        awardCodeIds, null);
            }
            if (GeneralTool.isEmpty(conventionAwardCodeList)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_valid_lottery_tickets_available"));
            }
            Random rand = new Random();
            //中奖号码
            ConventionAwardCode conventionAwardCode = conventionAwardCodeList.get(rand.nextInt(conventionAwardCodeList.size()));
            conventionAwardCode.setUseType(2);
            utilService.setUpdateInfo(conventionAwardCode);
            conventionAwardCodeMapper.updateById(conventionAwardCode);
            ConventionAwardWinner conventionAwardWinner = new ConventionAwardWinner();
            conventionAwardWinner.setFkConventionId(luckDrawDto.getFkConventionId());
            conventionAwardWinner.setFkConventionAwardId(luckDrawDto.getFkConventionAwardId());
            conventionAwardWinner.setFkConventionAwardCodeId(conventionAwardCode.getId());
            conventionAwardWinner.setFkConventionPersonId(conventionAwardCode.getFkConventionPersonId());
            utilService.setCreateInfo(conventionAwardWinner);
            conventionAwardWinnerMapper.insert(conventionAwardWinner);
            winningCodeList.add(conventionAwardCode);
        }
        return winningCodeList;
    }


}
