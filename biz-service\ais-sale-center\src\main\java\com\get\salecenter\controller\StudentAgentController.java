package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.vo.StudentAgentVo;
import com.get.salecenter.service.IStudentAgentService;
import com.get.salecenter.dto.StudentAgentDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/11/16
 * @TIME: 18:53
 * @Description:
 **/

@Api(tags = "绑定代理配置管理")
@RestController
@RequestMapping("sale/studentAgent")
public class StudentAgentController {
    @Resource
    private IStudentAgentService agentService;


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [studentVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/绑定代理配置管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(StudentAgentDto.Add.class) StudentAgentDto studentAgentDto) {
        return SaveResponseBo.ok(agentService.addStudentAgent(studentAgentDto));
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentVo>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "num代理编号 name代理名称")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/绑定代理配置管理/查询")
    @PostMapping("datas")
    public ResponseBo<StudentAgentVo> datas(@RequestBody SearchBean<StudentAgentDto> page) {
        List<StudentAgentVo> datas = agentService.getStudentAgent(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentVo>
     * @Description: 修改信息
     * @Param [studentVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/绑定代理配置管理/更新")
    @PostMapping("update")
    public ResponseBo<StudentAgentVo> update(@RequestBody @Validated(StudentAgentDto.Update.class)  StudentAgentDto studentAgentDto) {
        return UpdateResponseBo.ok(agentService.updateStudentAgent(studentAgentDto));
    }


}
