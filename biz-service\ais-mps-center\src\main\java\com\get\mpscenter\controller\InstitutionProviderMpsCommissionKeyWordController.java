package com.get.mpscenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.mpscenter.dto.KeyWordMajorLevelDto;
import com.get.mpscenter.dto.KeyWordMajorLevelSelectDto;
import com.get.mpscenter.service.InstitutionProviderMpsCommissionlKeyWordService;
import com.get.mpscenter.vo.KeyWordVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/3 17:18
 * @desciption: mps佣金关键字
 */

@Api(tags = "mps佣金关键字管理")
@RestController
@RequestMapping("mps/keyWord")
public class InstitutionProviderMpsCommissionKeyWordController {

    @Resource
    private InstitutionProviderMpsCommissionlKeyWordService institutionProviderMpsCommissionlKeyWordService;


    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.MPSCENTER, type = LoggerOptTypeConst.ADD, description = "佣金中心/mps佣金关键字管理/批量新增关键字")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(KeyWordMajorLevelDto.Add.class) ValidList<KeyWordMajorLevelDto> keyWordMajorLevelVos) {
        institutionProviderMpsCommissionlKeyWordService.batchAdd(keyWordMajorLevelVos);
        return SaveResponseBo.ok();
    }

    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.MPSCENTER, type = LoggerOptTypeConst.DELETE, description = "佣金中心/mps佣金关键字管理/删除关键字")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        institutionProviderMpsCommissionlKeyWordService.delete(id);
        return DeleteResponseBo.ok();
    }


    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.MPSCENTER, type = LoggerOptTypeConst.EDIT, description = "佣金中心/mps佣金关键字管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<KeyWordMajorLevelDto> keyWordMajorLevelDtos) {
        institutionProviderMpsCommissionlKeyWordService.movingOrder(keyWordMajorLevelDtos);
        return ResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param keyWordMajorLevelVo
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.MPSCENTER, type = LoggerOptTypeConst.EDIT, description = "佣金中心/mps佣金关键字管理/更新关键字")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated(KeyWordMajorLevelDto.Update.class) KeyWordMajorLevelDto keyWordMajorLevelDto) {
        return UpdateResponseBo.ok(institutionProviderMpsCommissionlKeyWordService.updateKeyWord(keyWordMajorLevelDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "typeName类型名称")
    @OperationLogger(module = LoggerModulesConsts.MPSCENTER, type = LoggerOptTypeConst.LIST, description = "佣金中心/mps佣金关键字管理/关键字列表数据")
    @PostMapping("datas")
    public ResponseBo datas(@RequestBody SearchBean<KeyWordMajorLevelSelectDto> page) {
        List<KeyWordVo> datas = institutionProviderMpsCommissionlKeyWordService.getKeyWords(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "关键字详情", notes = "详情")
    @OperationLogger(module = LoggerModulesConsts.MPSCENTER, type = LoggerOptTypeConst.DETAIL, description = "佣金中心/mps佣金关键字管理/关键字详情")
    @GetMapping("/{id}")
    public ResponseBo<KeyWordVo> detail(@PathVariable("id") Long id) {
        KeyWordVo data = institutionProviderMpsCommissionlKeyWordService.findKeyWordById(id);
        return new ResponseBo<>(data);
    }
}
