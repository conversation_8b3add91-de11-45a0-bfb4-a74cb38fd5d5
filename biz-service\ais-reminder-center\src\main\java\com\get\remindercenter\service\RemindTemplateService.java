package com.get.remindercenter.service;

import com.get.common.result.Page;
import com.get.remindercenter.vo.RemindTemplateVo;
import com.get.remindercenter.entity.RemindTemplate;
import com.get.remindercenter.dto.RemindTemplateListDto;
import com.get.remindercenter.dto.RemindTemplateUpdateDto;

import java.util.List;

/**
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　　　◆　　　　　　　　　　　　◆　　　　　　　　　　　　　　◆　　　　　　　　　　　　　◆◆　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　◆◆◆　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆
 * 　　　◆◆　　　　　◆◆　　　　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆◆◆◆
 * 　　　◆◆◆　　　◆◆◆　　　　　　　◆◆　　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆◆
 * 　　　　◆◆◆　◆◆◆　　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆
 * 　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　　◆◆
 * 　　　　　　◆◆◆　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆
 * 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆◆
 * <p>
 * Time: 11:37
 * Date: 2021/11/12
 * Description:提醒模板管理业务层
 */
public interface RemindTemplateService {

    /**
     * @Description: 列表数据
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    List<RemindTemplateVo> datas(RemindTemplateListDto remindTemplateListDto, Page page);

    /**
     * @Description: 新增
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    void add(RemindTemplateUpdateDto remindTemplateUpdateDto);

    /**
     * @Description: 更新
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    void update(RemindTemplateUpdateDto remindTemplateUpdateDto);

    /**
     * @Description: 详情
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    RemindTemplateVo detail(Long id);

    /**
     * @Description: 删除
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    void delete(Long id);

    /**
     * 获取邮件模板
     * @param typeKey
     * @return
     */
    RemindTemplate getRemindTemplateByTypeKey(String typeKey);
}
