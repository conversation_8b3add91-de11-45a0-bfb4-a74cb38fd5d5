package com.get.aismail.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.aismail.entity.AreaCity;
import org.mapstruct.Mapper;

@DS("institution")
@Mapper
public interface AreaCityMapper extends BaseMapper<AreaCity> {
    /**
     * @return java.lang.String
     * @Description :通过城市id 查找对应城市全称
     * @Param [id]
     * <AUTHOR>
     */
    String getCityFullNameById(Long id);
}
