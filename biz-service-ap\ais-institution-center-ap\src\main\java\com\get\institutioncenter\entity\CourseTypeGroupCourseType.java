package com.get.institutioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_course_type_group_course_type")
public class CourseTypeGroupCourseType extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 课程类型组别Id
     */
    @ApiModelProperty(value = "课程类型组别Id")
    @Column(name = "fk_course_type_group_id")
    private Long fkCourseTypeGroupId;
    /**
     * 课程类型Id
     */
    @ApiModelProperty(value = "课程类型Id")
    @Column(name = "fk_course_type_id")
    private Long fkCourseTypeId;
}