package com.get.partnercenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/7/7
 * @Version 1.0
 * @apiNote:注册伙伴用户请求类
 */
@Data
public class RegisterPartnerUserDto {

    @ApiModelProperty("发件人名称")
    @NotBlank(message = "发件人名称不能为空")
    private String fromUser;

    @ApiModelProperty("发件人电邮地址")
    @NotBlank(message = "发件人电邮地址不能为空")
    private String fromEmail;

    @ApiModelProperty("发件人电邮密码")
    @NotBlank(message = "发件人电邮密码不能为空")
    private String emailPassword;

    @ApiModelProperty("收件人名称")
    @NotBlank(message = "收件人名称不能为空")
    private String toUser;

    @ApiModelProperty("收件人邮箱")
    @NotBlank(message = "收件人邮箱不能为空")
    private String toEmail;

    @ApiModelProperty("代理ID")
    @NotNull(message = "代理ID不能为空")
    private Long agentId;

    @ApiModelProperty("代理公司ID")
    @NotNull(message = "代理公司ID不能为空")
    private Long companyId;

    @ApiModelProperty("伙伴角色Code")
    @NotBlank(message = "伙伴角色Code不能为空")
    private String partnerRoleCode;

    @ApiModelProperty("伙伴角色Id")
    @NotBlank(message = "伙伴角色Id不能为空")
    private Long partnerRoleId;

    @ApiModelProperty("邮件模板Key:企业负责人注册账号:CHIEF_REGISTER;佣金结算负责人注册账号:INVITE_TO_REGISTER")
    @NotBlank(message = "邮件模板Key不能为空")
    private String templateKey;

    @ApiModelProperty("代理名称")
    private String agentName;

    @ApiModelProperty("申请代理名称")
    private String applyAgentName;

    @ApiModelProperty("当前登录人")
    private String loginId;
}
