package com.get.platformconfigcenter.dao.appmso;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.Translation;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("appmsodb")
public interface TranslationMapper extends BaseMapper<Translation> {

//    int insert(Translation record);
//
//    int insertSelective(Translation record);
//
//    int updateByPrimaryKeySelective(Translation record);
//
//    int updateByPrimaryKeyWithBLOBs(Translation record);
//
//    int updateByPrimaryKey(Translation record);
//
//    String getTranslation(TranslationDto translationVo);
//
//    void deleteTranslations(@Param("tableName") String tableName, @Param("id") Long id);
}