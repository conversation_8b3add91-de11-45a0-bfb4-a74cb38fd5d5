package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.AreaCountryInfoType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: Sea
 * @create: 2021/1/13 16:40
 * @verison: 1.0
 * @description:
 */
@Mapper
public interface AreaCountryInfoTypeMapper extends BaseMapper<AreaCountryInfoType> {

    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(AreaCountryInfoType record);

    /**
     * @return java.lang.Integer
     * @Description :获取最大排序值
     * @Param []
     * <AUTHOR>
     */
    Integer getMaxViewOrder();

    /**
     * @return java.lang.String
     * @Description :通过id获取类型名称
     * @Param [areaCountryInfoTypeId]
     * <AUTHOR>
     */
    String getAreaCountryInfoTypeNameById(@Param("areaCountryInfoTypeId") Long areaCountryInfoTypeId);
}