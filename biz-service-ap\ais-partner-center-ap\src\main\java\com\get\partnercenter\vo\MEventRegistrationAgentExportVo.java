package com.get.partnercenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class MEventRegistrationAgentExportVo {

    @ApiModelProperty("所属公司")
    private String companyName;
    @ApiModelProperty("状态")
    private String statusName;
    @ApiModelProperty("姓名")
    private String name;
    @ApiModelProperty("移动电话")
    private String mobile;
    @ApiModelProperty("参加人数")
    private Integer peopleCount;
    @ApiModelProperty("BD编号/名字")
    private String agentBD;
    @ApiModelProperty("代理名称")
    private String ASmStaffName;
    @ApiModelProperty("账号")
    private String partnerUserName;

    @ApiModelProperty("角色")
    private String roleName;

    @ApiModelProperty("报名时间")
    private Date gmtCreate;




}
