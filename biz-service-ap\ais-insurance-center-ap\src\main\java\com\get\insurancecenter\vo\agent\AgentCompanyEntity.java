package com.get.insurancecenter.vo.agent;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class AgentCompanyEntity {

    @ApiModelProperty("关系Id")
    private Long id;

    @ApiModelProperty("学生代理Id")
    private Long fkAgentId;

    @ApiModelProperty("公司Id")
    private Long fkCompanyId;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    @ApiModelProperty("创建用户(登录账号)")
    private String gmtCreateUser;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("修改时间")
    private Date gmtModified;

    @ApiModelProperty("修改用户(登录账号)")
    private String gmtModifiedUser;
}
