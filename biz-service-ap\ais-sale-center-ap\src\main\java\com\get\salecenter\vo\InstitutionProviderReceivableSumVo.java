package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　　　◆　　　　　　　　　　　　◆　　　　　　　　　　　　　　◆　　　　　　　　　　　　　◆◆　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　◆◆◆　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆
 * 　　　◆◆　　　　　◆◆　　　　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆◆◆◆
 * 　　　◆◆◆　　　◆◆◆　　　　　　　◆◆　　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆◆
 * 　　　　◆◆◆　◆◆◆　　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆
 * 　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　　◆◆
 * 　　　　　　◆◆◆　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆
 * 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆◆
 * <p>
 * Time: 16:22
 * Date: 2021/11/19
 * Description:代理应付汇总统计返回类
 */
@Data
public class InstitutionProviderReceivableSumVo {
    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    /**
     * 提供商id
     */
    @ApiModelProperty(value = "提供商id")
    private Long institutionProviderId;

    /**
     * 提供商名称
     */
    @ApiModelProperty(value = "提供商名称")
    private String institutionProviderName;

    /**
     * 提供商中文名称
     */
    @ApiModelProperty(value = "提供商中文名称")
    private String institutionProviderNameChn;

    /**
     * 提供商全称
     */
    @ApiModelProperty(value = "提供商全称")
    private String institutionProviderFullName;

    /**
     * 应收计划数
     */
    @ApiModelProperty(value = "应收计划数")
    private Integer compleApplyCount;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;


    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String fkCurrencyTypeName;

    /**
     * 应收金额
     */
    @ApiModelProperty(value = "应收金额")
    private String payableAmount;

    /**
     * 应收金额（带币种名称）
     */
    @ApiModelProperty(value = "应收金额（带币种名称）")
    private String payableAmountStr;

    /**
     * 实收金额
     */
    @ApiModelProperty(value = "实收金额")
    private String amountPayable;

    /**
     * 实收金额（带币种名称）
     */
    @ApiModelProperty(value = "实收金额（带币种名称）")
    private String amountPayableStr;

    /**
     * 实收差额
     */
    @ApiModelProperty(value = "实收差额")
    private String differPay;


    /**
     * 实收差额（带币种名称）
     */
    @ApiModelProperty(value = "实收差额（带币种名称）")
    private String differPayStr;

}
