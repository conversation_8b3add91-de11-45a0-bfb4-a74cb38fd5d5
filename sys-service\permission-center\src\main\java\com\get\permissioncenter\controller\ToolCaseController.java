package com.get.permissioncenter.controller;

import com.alibaba.fastjson.JSONObject;
import com.get.common.cache.CacheNames;
import com.get.common.consts.AESConstant;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ErrorCodeEnum;
import com.get.common.result.ResponseBo;
import com.get.common.utils.AESUtils;
import com.get.core.cache.utils.CacheUtil;
import com.get.core.log.annotation.OperationLogger;
import com.get.permissioncenter.service.IToolCaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Created by Jerry.
 * User: 14:15
 * Date: 2021/6/11
 * Description:工具箱控制器
 */
@Api(tags = "工具箱管理")
@RestController
@RequestMapping("system/toolCase")
public class ToolCaseController {

    @Resource
    private IToolCaseService iToolCaseService;

    @ApiOperation(value = "成绩查询", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.DETAIL, description = "系统中心/工具箱管理/成绩查询")
    @PostMapping("scoreInquiry")
    public ResponseBo scoreInquiry(@RequestParam("code") String code, HttpServletRequest request, HttpServletResponse response) {
        try {
            String html = iToolCaseService.loginAndGetPersonHtml(code, request, response);
            return new ResponseBo(html);
        } catch (Exception e) {
            return ResponseBo.error(ErrorCodeEnum.SERVER_EXCEPTION.getCode(), e.getMessage());
        }
    }


    /**
     * @Description: AES加密
     * @Author: Jerry
     * @Date:11:11 2021/9/15
     */
    @ApiOperation(value = "AES加密", notes = "param为要加密的参数")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.DETAIL, description = "系统中心/工具箱管理/AES加密")
    @PostMapping("aesGenerator")
    public ResponseBo aesGenerator(@RequestParam("param") String param) {
        try {
            String encrypt = AESUtils.Encrypt(param, AESConstant.AESKEY);
            return new ResponseBo(encrypt);
        } catch (Exception e) {
            return ResponseBo.error(ErrorCodeEnum.SERVER_EXCEPTION.getCode(), e.getMessage());
        }
    }

    /**
     * @Description: AES加密
     * @Author: Jerry
     * @Date:11:11 2021/9/15
     */
    @ApiOperation(value = "更新系统提醒缓存", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.DETAIL, description = "系统中心/工具箱管理/更新系统提醒缓存")
    @PostMapping("flushReminderCache")
    public ResponseBo flushReminderCache() {

        CacheUtil.clear(CacheNames.TASK_CACHE);
        ResponseBo responseBo = new ResponseBo();
        responseBo.setMessage("更新系统提醒缓存成功");
        responseBo.setSuccess(true);
        responseBo.setCode(ErrorCodeEnum.REQUEST_OK.getCode());
        return responseBo;

    }

    @ApiOperation(value = "人脸匹配", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.DETAIL, description = "系统中心/工具箱管理/图片比对")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "imagePath1",value = "图片地址1"),
            @ApiImplicitParam(name = "imagePath2",value = "图片地址2")
    })
    @PostMapping("/faceMatch")
    public ResponseBo faceMatch(@RequestParam("imagePath1") String imagePath1,
                                @RequestParam("imagePath2") String imagePath2) throws Exception {
       return new ResponseBo(iToolCaseService.faceMatch(imagePath1,imagePath2));
    }

}
