package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestHeaderHandler;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.vo.SettlementCommissionNoticeVo;
import com.get.financecenter.entity.SettlementCommissionNotice;
import com.get.salecenter.service.IMediaAndAttachedService;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.SettlementCommissionNoticeDto;
import com.get.salecenter.service.AsyncReminderService;
import com.get.salecenter.service.PayableSettlementMessageService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class PayableSettlementMessageServiceImpl implements PayableSettlementMessageService {


    @Resource
    private UtilService<Object> utilService;

    @Resource
    private AsyncReminderService asyncReminderService;

    @Resource
    private IMediaAndAttachedService mediaAndAttachedService;

//    /**
//     * Author Cream
//     * Description : //新增
//     * Date 2023/2/28 11:13
//     * Params:
//     * Return
//     */
//    @Override
//    public SaveResponseBo add(SettlementCommissionNoticeDto settlementCommissionNoticeDto) {
//        if (Objects.isNull(settlementCommissionNoticeDto)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
//        }
//        SettlementCommissionNotice settlementCommissionNotice = new SettlementCommissionNotice();
//        BeanUtils.copyProperties(settlementCommissionNoticeDto,settlementCommissionNotice);
//        utilService.setCreateInfo(settlementCommissionNotice);
//        payableSettlementMessageMapper.insert(settlementCommissionNotice);
//        return SaveResponseBo.ok(settlementCommissionNotice.getId());
//    }




    /**
     * Author Cream
     * Description : //发送邮件
     * Date 2023/2/28 17:22
     * Params:
     * Return
     */
    @Override
    public void sendEmail(SettlementCommissionNoticeDto settlementCommissionNoticeDto) {
        if (StringUtils.isBlank(settlementCommissionNoticeDto.getCommissionNotice())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("notification_information_cannot_be_empty"));
        }
        asyncReminderService.sendSettlementCommissionEmail(settlementCommissionNoticeDto.getFkPayablePlanId()
                , settlementCommissionNoticeDto.getCommissionNotice(), RequestHeaderHandler.getHeaderMap(), SecureUtil.getFkCompanyId());
    }


}