package com.get.partnercenter.controller;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.partnercenter.dto.MAppStudentCheckDto;
import com.get.partnercenter.dto.MAppStudentCheckSerachDto;
import com.get.partnercenter.dto.MAppStudentOfferItemCheckDto;
import com.get.partnercenter.service.MAppStudentCheckService;
import com.get.partnercenter.vo.MAppStudentCheckSearchVo;
import com.get.partnercenter.vo.MAppStudentVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Api(tags = "后台管理-学生审核")
@RestController
@RequestMapping("partner/mAppStudentCheck")
public class MAppStudentController {


    @Autowired
    private MAppStudentCheckService mAppStudentCheckService;


    @ApiOperation(value = "审核列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/学生管理/审核列表")
    @PostMapping("search")
    public ResponseBo search(@RequestBody @Valid SearchBean<MAppStudentCheckSerachDto> page){
        List<MAppStudentCheckSearchVo> datas =
                mAppStudentCheckService.searchPage(page.getData(),page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    @ApiOperation(value = "审核学生详情", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/学生管理/审核学生详情")
    @GetMapping("getDetail/{id}")
    public ResponseBo getDetail(@PathVariable("id") Long id){
        MAppStudentVo datas = mAppStudentCheckService.searchDetail(id);
        return new ResponseBo<>(datas);
    }
    /*@ApiOperation(value = "申请计划加申-审核详情", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/学生管理/审核学生详情")
    @GetMapping("getOfferItemDetail/{id}")
    public ResponseBo getOfferItemDetail(@PathVariable("id") Long id){
        MAppStudentVo datas =
                mAppStudentCheckService.getOfferItemDetail(id);
        return new ResponseBo<>(datas);
    }*/



    @ApiOperation(value = "审核-学生接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/学生管理/审核")
    @PostMapping("check")
    public ResponseBo check(@RequestBody @Validated(BaseVoEntity.Add.class) MAppStudentCheckDto checkdto){
        return SaveResponseBo.ok(mAppStudentCheckService.checkUser(checkdto));
    }



    @ApiOperation(value = "审核-(申请计划通过后)通过接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/学生管理/(申请计划通过后)通过接口")
    @PostMapping("checkItemOffer")
    public ResponseBo checkItemOffer(@RequestBody @Valid MAppStudentOfferItemCheckDto itemOfferdto){

        mAppStudentCheckService.checkItemOffer(itemOfferdto);

        return SaveResponseBo.ok();
    }
}
