package get.middlecenter.service.impl; 

import get.middlecenter.consts.MiddleConstant;
import get.middlecenter.dto.TransferRequest;
import get.middlecenter.service.DemoService; 
import get.middlecenter.utils.HttpUtils; 
import org.springframework.stereotype.Service;

import java.math.BigDecimal; 

@Service 
public class DemoServiceImpl implements DemoService { 
    private final HttpUtils httpUtils; 

    public DemoServiceImpl(HttpUtils httpUtils) { 
        this.httpUtils = httpUtils; 
    } 

    @Override 
    public String demo(String nonce) {

        TransferRequest transferRequest = TransferRequest.builder() 
                .fromAccountId("张三") 
                .toAccountId("李四") 
                .transferPrice(new BigDecimal("100.00")) 
                .build(); 
        String responseBody = httpUtils.sendPostRequest(transferRequest, MiddleConstant.MIDDLE_CENTER_INTERFACE1, nonce);
        System.out.println("\n响应体：" + responseBody); 
        return responseBody;

    }
}