<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.platformconfigcenter.dao.appissue.AppFormConfigDivisionMapper">
  <resultMap id="BaseResultMap" type="com.get.platformconfigcenter.entity.AppFormConfigDivision">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_app_form_config_id" jdbcType="BIGINT" property="fkAppFormConfigId" />
    <result column="fk_app_form_division_id" jdbcType="BIGINT" property="fkAppFormDivisionId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <sql id="Base_Column_List">
    id, fk_app_form_config_id, fk_app_form_division_id, gmt_create, gmt_create_user, 
    gmt_modified, gmt_modified_user
  </sql>
  <!--TODO 注释sql-->
<!--  <insert id="insert" parameterType="com.get.platformconfigcenter.entity.AppFormConfigDivision"  keyProperty="id" useGeneratedKeys="true">-->
<!--    insert into r_app_form_config_division (id, fk_app_form_config_id, fk_app_form_division_id, -->
<!--      gmt_create, gmt_create_user, gmt_modified, -->
<!--      gmt_modified_user)-->
<!--    values (#{id,jdbcType=BIGINT}, #{fkAppFormConfigId,jdbcType=BIGINT}, #{fkAppFormDivisionId,jdbcType=BIGINT}, -->
<!--      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, -->
<!--      #{gmtModifiedUser,jdbcType=VARCHAR})-->
<!--  </insert>-->
<!--  <insert id="insertSelective" parameterType="com.get.platformconfigcenter.entity.AppFormConfigDivision">-->
<!--    insert into r_app_form_config_division-->
<!--    <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        id,-->
<!--      </if>-->
<!--      <if test="fkAppFormConfigId != null">-->
<!--        fk_app_form_config_id,-->
<!--      </if>-->
<!--      <if test="fkAppFormDivisionId != null">-->
<!--        fk_app_form_division_id,-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        gmt_create,-->
<!--      </if>-->
<!--      <if test="gmtCreateUser != null">-->
<!--        gmt_create_user,-->
<!--      </if>-->
<!--      <if test="gmtModified != null">-->
<!--        gmt_modified,-->
<!--      </if>-->
<!--      <if test="gmtModifiedUser != null">-->
<!--        gmt_modified_user,-->
<!--      </if>-->
<!--    </trim>-->
<!--    <trim prefix="values (" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        #{id,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="fkAppFormConfigId != null">-->
<!--        #{fkAppFormConfigId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="fkAppFormDivisionId != null">-->
<!--        #{fkAppFormDivisionId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        #{gmtCreate,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtCreateUser != null">-->
<!--        #{gmtCreateUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="gmtModified != null">-->
<!--        #{gmtModified,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtModifiedUser != null">-->
<!--        #{gmtModifiedUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--    </trim>-->
<!--  </insert>-->
<!--  <update id="updateByPrimaryKeySelective" parameterType="com.get.platformconfigcenter.entity.AppFormConfigDivision">-->
<!--    update r_app_form_config_division-->
<!--    <set>-->
<!--      <if test="fkAppFormConfigId != null">-->
<!--        fk_app_form_config_id = #{fkAppFormConfigId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="fkAppFormDivisionId != null">-->
<!--        fk_app_form_division_id = #{fkAppFormDivisionId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtCreateUser != null">-->
<!--        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="gmtModified != null">-->
<!--        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtModifiedUser != null">-->
<!--        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--    </set>-->
<!--    where id = #{id,jdbcType=BIGINT}-->
<!--  </update>-->
<!--  <update id="updateByPrimaryKey" parameterType="com.get.platformconfigcenter.entity.AppFormConfigDivision">-->
<!--    update r_app_form_config_division-->
<!--    set fk_app_form_config_id = #{fkAppFormConfigId,jdbcType=BIGINT},-->
<!--      fk_app_form_division_id = #{fkAppFormDivisionId,jdbcType=BIGINT},-->
<!--      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},-->
<!--      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},-->
<!--      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},-->
<!--      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}-->
<!--    where id = #{id,jdbcType=BIGINT}-->
<!--  </update>-->
</mapper>