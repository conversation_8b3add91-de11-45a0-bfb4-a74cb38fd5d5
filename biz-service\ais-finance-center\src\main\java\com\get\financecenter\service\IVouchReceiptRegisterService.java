package com.get.financecenter.service;


import com.get.common.result.Page;
import com.get.financecenter.dto.VouchReceiptRegisterDto;
import com.get.financecenter.vo.VouchReceiptRegisterVo;

import javax.validation.Valid;
import java.util.List;

public interface IVouchReceiptRegisterService {

    void add(VouchReceiptRegisterDto vouchReceiptRegisterDto);

    void updateById(VouchReceiptRegisterDto vouchReceiptRegisterDto);


    List<VouchReceiptRegisterVo> getVouchReceiptRegister(@Valid VouchReceiptRegisterDto vouchReceiptRegisterDto, Page page);
}

