package com.get.registrationcenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import com.get.registrationcenter.entity.RegistrationPrivacyPolicy;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Hardy
 * @create: 2021/5/14 15:49
 * @verison: 1.0
 * @description:
 */
@Data
public class PrivacyPolicyVo extends BaseVoEntity {
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;
    /**
     * 政策内容
     */
    @ApiModelProperty(value = "政策内容")
    private String policyContent;
}
