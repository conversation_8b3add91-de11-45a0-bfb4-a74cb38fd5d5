package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * author:Neil
 * Time: 15:01
 * Date: 2022/8/19
 * Description:
 */
@Data
public class ClientOfferDto extends BaseEntity{
    @ApiModelProperty(value = "客户Id")
    private Long fkClientId;

    @ApiModelProperty(value = "代理Id（业绩绑定）")
    private Long fkAgentId;

    @ApiModelProperty(value = "代理联系人Id")
    private Long fkContactPersonId;

    @ApiModelProperty(value = "员工Id（业绩绑定，BD）")
    private Long fkStaffId;

    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "咨询方案编号")
    private String num;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "状态：0新建/1跟进中/2已签约/3长期跟进")
    private Integer status;
}
