package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ServiceFeePayFormDetailVo {


    @ApiModelProperty("币种名称")
    private String payableCurrencyTypeName;
    @ApiModelProperty("实收金额")
    private BigDecimal actualPayableAmount;
    @ApiModelProperty("付款时间")
    private Date actualPayTime;
    @ApiModelProperty("服务费id")
    private Long serviceFeeId;
}
