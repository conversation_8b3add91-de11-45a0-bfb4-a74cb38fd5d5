package get.middlecenter.common;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.
 * Time: 9:39
 * Date: 2025/7/2
 * Description:异常管理
 */
public class ResultUtils {
    public static <T> Result<T> success() {
        return new Result<>(true, null, null);
    }

    public static <T> Result<T> success(T data) {
        return new Result<>(true, data, null);
    }

    public static <T> Result<T> error(String msg) {
        return new Result<>(false, null, null, msg);
    }

    public static <T> Result<T> error(String code, String msg) {
        return new Result<>(false, null, code, msg);
    }
}
