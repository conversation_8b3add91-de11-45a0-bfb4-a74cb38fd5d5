package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.dto.CommentDto;
import com.get.institutioncenter.dto.ContractCompanyDto;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.vo.CommentVo;
import com.get.institutioncenter.vo.CompanyTreeVo;
import com.get.institutioncenter.vo.ContractVo;
import com.get.institutioncenter.entity.Contract;
import com.get.institutioncenter.dto.ContractDto;
import com.get.institutioncenter.dto.query.ContractQueryDto;
import com.get.institutioncenter.vo.MediaAndAttachedVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/8/20
 * @TIME: 18:24
 * @Description: 合同管理
 **/
public interface IContractService extends BaseService<Contract> {

    /**
     * @return java.util.List<com.get.institutioncenter.vo.ContractVo>
     * @Description: 分页获取所有合同
     * @Param [contractVo, page]
     * <AUTHOR>
     */
    List<ContractVo> getAllContract(ContractQueryDto contractVo, Page page, List<Long> companyIds);


    /**
     * 获取合同详细
     *
     * @param id
     * @return
     */
    ContractVo getContractById(Long id);

    /**
     * 新增
     *
     * @param contractDto
     * @return
     */
    Long addContract(ContractDto contractDto);


    /**
     * 修改
     *
     * @param contractDto
     * @return
     */

    ContractVo updateContract(ContractDto contractDto);

    /**
     * 删除
     *
     * @param id
     * @
     */
    void delete(Long id);


    /**
     * @return com.get.institutioncenter.vo.MediaAndAttachedDto
     * @Description: 保存上传的文件
     * @Param [mediaAndAttacheds]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> addContractMedia(List<MediaAndAttachedDto> mediaAndAttachedDto);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.MediaAndAttachedDto>
     * @Description: 附件列表
     * @Param [data, page]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> getContractMedia(MediaAndAttachedDto data, Page page);

    /**
     * @return void
     * @Description: 安全配置
     * @Param [validList]
     * <AUTHOR>
     */
    void editContractCompanyRelation(List<ContractCompanyDto> validList);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.CompanyTreeVo>
     * @Description: 合同和公司的关系（数据回显）
     * @Param [contractId]
     * <AUTHOR>
     */
    List<CompanyTreeVo> getContractCompanyRelation(Long contractId);

    /**
     * @return java.lang.Long
     * @Description: 增加或者更新评论
     * @Param [commentDto]
     * <AUTHOR>
     **/
    Long editComment(CommentDto commentDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.CommentVo>
     * @Description: 获取所有评论
     * @Param [commentDto, page]
     * <AUTHOR>
     */
    List<CommentVo> getComments(CommentDto commentDto, Page page);


    /**
     * @return java.lang.Long
     * @Description: 新增提供商合同
     * @Param [contractDto]
     * <AUTHOR>
     */
    Long addProviderContract(ContractDto contractDto);


    /**
     * @ Description :feign调用，根据id查询表
     * @ Param [id]
     * @ return com.get.institutioncenter.vo.InstitutionProviderVo
     * @ author LEO
     */
    ContractVo getInstitutionContractById(Long id);

    /**
     * @ Description :定义下拉枚举：0新签/1续签
     * @ Param []
     * @ return com.get.institutioncenter.vo.InstitutionProviderVo
     * @ author LEO
     */
    List<Map<String, Object>> getContractApprovalMode();

    /**
     * @ Description :开始流程
     * @ Param [businessKey, procdefKey, companyId]
     * @ return void
     * @ author LEO
     */
    void startInstitutionContractFlow(String businessKey, String procdefKey, String companyId);

    boolean updateChangeStatus(Contract contract);

    void updateCancellationBusiness(Long id);

    boolean changeStatus(Integer status, String tableName, Long businessKey);

    void getUserSubmit(String taskId, String status);

    void getRevokeContract(Long id, String summary);

    String getContractNewByProviderId(Long fkInstitutionProviderId);
}
