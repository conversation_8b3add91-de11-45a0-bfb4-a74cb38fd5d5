package com.get.institutioncenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Data
public class InstitutionApplicationMetricsDto {

    @ApiModelProperty(value = "公司")
    private List<Long> companyIds;

    @ApiModelProperty(value = "国家")
    private List<Long> countryIds;

    @ApiModelProperty(value = "学校")
    private List<Long> institutionIds;

    @ApiModelProperty(value = "开学开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String deferOpeningStartTime;

    @ApiModelProperty(value = "开学结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String deferOpeningEndTime;

    @ApiModelProperty(value = "排序")
    private String sort;

    @ApiModelProperty(value = "排序名称")
    private String orderByName;

    @ApiModelProperty(value = "qs年份")
    private Integer year;

    @ApiModelProperty("课程等级ids")
    private Set<Long> majorLevelIds;

    @ApiModelProperty("是否只统计主课，枚举：0否/1是")
    private Boolean isMainCourse;

    @ApiModelProperty(value = "所属集团Id")
    private List<Long> fkInstitutionGroupIds;

    @ApiModelProperty(value = "是否统计后续课程：0否/1是")
    private Boolean isStatisticsFollow;

    @ApiModelProperty(value = "QS排名开始")
    private Integer qsRankStart;

    @ApiModelProperty(value = "QS排名结束")
    private Integer qsRankEnd;



}
