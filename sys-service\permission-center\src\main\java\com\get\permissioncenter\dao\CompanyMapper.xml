<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.permissioncenter.dao.CompanyMapper">

    <insert id="insertSelective" parameterType="com.get.permissioncenter.entity.Company" useGeneratedKeys="true"
            keyProperty="id">
        insert into m_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkParentCompanyId != null">
                fk_parent_company_id,
            </if>
            <if test="num != null">
                num,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="nameChn != null">
                name_chn,
            </if>
            <if test="shortName != null">
                short_name,
            </if>
            <if test="shortNameChn != null">
                short_name_chn,
            </if>
            <if test="viewOrder != null">
                view_order,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkParentCompanyId != null">
                #{fkParentCompanyId,jdbcType=BIGINT},
            </if>
            <if test="num != null">
                #{num,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="nameChn != null">
                #{nameChn,jdbcType=VARCHAR},
            </if>
            <if test="shortName != null">
                #{shortName,jdbcType=VARCHAR},
            </if>
            <if test="shortNameChn != null">
                #{shortNameChn,jdbcType=VARCHAR},
            </if>
            <if test="viewOrder != null">
                #{viewOrder,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="getAllChildCompanyId" resultType="java.lang.Long">
        select id from m_company where 1=1
        <if test="ids != null and ids.size()>0">
            AND fk_parent_company_id IN
            <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                #{id,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="getChildCompany" resultType="com.get.permissioncenter.vo.CompanyVo">
        select id,name,name_chn,short_name,short_name_chn
        from m_company
        where fk_parent_company_id = #{fkParentCompanyId} ORDER BY view_order DESC
    </select>

    <select id="getMaxViewOrder" resultType="java.lang.Integer">
        select ifnull(max(view_order) + 1, 1)
        from m_company
    </select>

    <select id="getParentCompanyIds" resultType="java.lang.Long" parameterType="java.lang.Long">
        SELECT id
        FROM (
                 SELECT @r                                                                AS _id,
                        (SELECT @r := fk_parent_company_id FROM m_company WHERE id = _id) AS parent_id,
                        @l := @l + 1                                                      AS lvl
                 FROM (SELECT @r := #{id}, @l := 0) vars,
                      m_company h
                 WHERE @r != 0) T1
                 JOIN m_company T2
                      ON T1._id = T2.id
        ORDER BY id;
    </select>

    <select id="getCompanyIdByName" resultType="java.lang.Long">
        SELECT id FROM m_company WHERE `name` LIKE CONCAT('%',#{keyWord},'%') OR name_chn LIKE CONCAT('%',#{keyWord},'%')
    </select>

    <select id="getParentCompany" resultType="java.lang.Long">
        SELECT id
        FROM (
                 SELECT @ids         AS _ids,
                        (SELECT @ids := GROUP_CONCAT(id)
                         FROM m_company
                         WHERE FIND_IN_SET(fk_parent_company_id, @ids)
                        )            AS cids,
                        @l := @l + 1 AS LEVEL
                 FROM m_company,
                      (SELECT @ids := #{companyId}, @l := 0) b
                 WHERE @ids IS NOT NULL
             ) id,
             m_company DATA
        WHERE FIND_IN_SET(DATA.fk_parent_company_id, id._ids)
    </select>

    <select id="getTopCompany" resultType="java.lang.Boolean">
        SELECT id
        FROM m_company
        where fk_parent_company_id = 0
        LIMIT 1
    </select>

    <select id="isExistByCompanyId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id), 0) id
        FROM m_company
        where fk_parent_company_id = #{companyId}
    </select>
    <select id="fuzzGetCompanyInfo" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            id,
	        short_name as name
        FROM
            m_company
        WHERE
            (num LIKE CONCAT('%',#{keyword}, '%')
        OR REPLACE (NAME, ' ', '') LIKE CONCAT('%', #{keyword}, '%')
        OR REPLACE (name_chn, ' ', '') LIKE CONCAT('%',#{keyword}, '%')
        OR short_name LIKE CONCAT('%',#{keyword}, '%')
        OR short_name_chn LIKE CONCAT('%',#{keyword}, '%'))
        and  (id = #{companyId} or fk_parent_company_id=#{companyId})
    </select>

</mapper>