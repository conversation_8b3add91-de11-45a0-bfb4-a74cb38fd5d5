<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.StudentOfferItemStepMapper">
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.StudentOfferItemStep">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="step_name" jdbcType="VARCHAR" property="stepName"/>
        <result column="step_key" jdbcType="VARCHAR" property="stepKey"/>
        <result column="step_order" jdbcType="INTEGER" property="stepOrder"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <insert id="insertSelective" parameterType="com.get.salecenter.entity.StudentOfferItemStep" keyProperty="id"
            useGeneratedKeys="true">
        insert into u_student_offer_item_step
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="studentOfferItemStepIdPrecondition != null">
                fk_student_offer_item_step_id_precondition,
            </if>
            <if test="stepName != null">
                step_name,
            </if>
            <if test="stepKey != null">
                step_key,
            </if>
            <if test="stepOrder != null">
                step_order,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="studentOfferItemStepIdPrecondition != null">
                #{studentOfferItemStepIdPrecondition,jdbcType=BIGINT},
            </if>
            <if test="stepName != null">
                #{stepName,jdbcType=VARCHAR},
            </if>
            <if test="stepKey != null">
                #{stepKey,jdbcType=VARCHAR},
            </if>
            <if test="stepOrder != null">
                #{stepOrder,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="getMaxStepOrder" resultType="java.lang.Integer">
        select
            IFNULL(max(step_order)+1,0) step_order
        from
            u_student_offer_item_step

    </select>

    <select id="getItemStepPrecondition" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        select id,step_name name from u_student_offer_item_step
        where id &lt;&gt; #{itemStepId}
        order by step_order asc
    </select>

    <select id="getItemStepSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT id AS id,step_name name, step_key AS num from u_student_offer_item_step order by step_order asc
    </select>
    <select id="getUnexecutedItemStep" resultType="com.get.salecenter.entity.StudentOfferItemStep">
        SELECT * from u_student_offer_item_step
        <where>
            <if test="itemStepId != null">
                FIND_IN_SET(#{itemStepId},fk_student_offer_item_step_id_precondition)
            </if>
            <if test="itemStepId == null">
                id=1
            </if>
        </where>
        order by step_order asc
    </select>
    <select id="getExecutedItemStep" resultType="com.get.salecenter.entity.StudentOfferItemStep">
        SELECT * from u_student_offer_item_step
        WHERE step_order &lt; (SELECT step_order FROM u_student_offer_item_step WHERE id=#{itemStepId})
    </select>


    <select id="getStudentIdByState" resultType="java.lang.Long">
        SELECT
        DISTINCT o.fk_student_id
        FROM
        m_student_offer AS o
        INNER JOIN m_student_offer_item AS i ON i.fk_student_offer_id = o.id
        INNER JOIN (
        SELECT
        rsois.fk_student_offer_item_id AS itemId,
        MIN( usois.step_order ) AS stepOrder
        FROM
        r_student_offer_item_step AS rsois
        INNER JOIN u_student_offer_item_step AS usois ON usois.id = rsois.fk_student_offer_item_step_id
        GROUP BY
        rsois.fk_student_offer_item_id
        ) a ON a.itemId = i.id
        WHERE 1=1
        <if test="states != null and states.size()>0">
            AND a.stepOrder IN
            <foreach collection="states" item="state" index="index" open="(" separator="," close=")">
                #{state,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>
    <select id="getItemStepMap" resultType="java.util.HashMap">
        SELECT step_order AS steOrder,step_name AS stepName FROM u_student_offer_item_step
    </select>

    <select id="getStepIdByOrder" resultType="java.lang.Long">
        SELECT id FROM u_student_offer_item_step WHERE step_order IN
        <foreach collection="orders" item="order" open="(" separator="," close=")">
            #{order}
        </foreach>
    </select>

    <select id="getSuccessfulCustomerStepSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT id AS id,step_name name, step_key AS num from u_student_offer_item_step
        where 1=1
        <if test="list != null and list.size()>0">
            and step_key in
            <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by step_order asc
    </select>
    <select id="getUnexecutedItemStepWithCountryConfig"
            resultType="com.get.salecenter.entity.StudentOfferItemStep">
        select a.* from(
        SELECT
        a.id,
        CASE WHEN FIND_IN_SET((SELECT fk_area_country_id FROM m_student_offer_item where id = #{fkStudentOfferItemId}),b.fk_area_country_ids) >0 THEN
        b.fk_student_offer_item_step_id_precondition
        ELSE
        a.fk_student_offer_item_step_id_precondition
        END fk_student_offer_item_step_id_precondition,
        a.step_name,
        a.step_key,
        a.step_order,
        a.role_key,
        a.config_json,
        a.remark,
        a.gmt_create,
        a.gmt_create_user,
        a.gmt_modified,
        a.gmt_modified_user
        from
        u_student_offer_item_step a
        LEFT JOIN r_student_offer_item_step_country b on a.id = b.fk_student_offer_item_step_id
        ) a
        <where>
            <if test="itemStepId != null">
                FIND_IN_SET(#{itemStepId},a.fk_student_offer_item_step_id_precondition)
            </if>
            <if test="itemStepId == null">
                id=1
            </if>
        </where>
        order by a.step_order asc
    </select>
    <select id="getItemStepPostpositionByStepId" resultType="com.get.salecenter.entity.StudentOfferItemStep">
        select *
        from u_student_offer_item_step
        where find_in_set(#{itemStepId},fk_student_offer_item_step_id_precondition)
    </select>
    <select id="getItemStepMapAndId" resultType="java.util.Map">
        SELECT step_order AS steOrder,step_name AS stepName,id FROM u_student_offer_item_step
    </select>
</mapper>