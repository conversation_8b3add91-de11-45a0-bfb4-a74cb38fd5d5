package com.get.insurancecenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.utils.SecureUtil;
import com.get.insurancecenter.dto.file.AppFileCenter;
import com.get.insurancecenter.entity.InsuranceMediaAndAttached;
import com.get.insurancecenter.entity.SettlementBill;
import com.get.insurancecenter.entity.SettlementBillSignature;
import com.get.insurancecenter.mapper.*;
import com.get.insurancecenter.service.InsuranceMediaAndAttachedService;
import com.get.insurancecenter.service.SettlementBillService;
import com.get.insurancecenter.utils.ExchangeRateUtils;
import com.get.insurancecenter.utils.PdfExportUtils;
import com.get.insurancecenter.vo.commission.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/6/20
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class SettlementBillServiceImpl extends ServiceImpl<SettlementBillMapper, SettlementBill> implements SettlementBillService {

    @Autowired
    private SettlementBillMapper billMapper;
    @Autowired
    private SettlementBillSignatureMapper billSignatureMapper;
    @Autowired
    private SettlementBillItemMapper billItemMapper;
    @Autowired
    private ExchangeRateUtils exchangeRateUtils;
    @Autowired
    private InsuranceOrderSettlementMapper settlementMapper;
    @Autowired
    private SaleCenterMapper saleCenterMapper;
    @Autowired
    private InsuranceMediaAndAttachedService attachedService;
    @Autowired
    private AppFileCenterMapper appFileCenterMapper;
    @Autowired
    private PdfExportUtils pdfExportUtils;

    @Override
    public SettlementBillDetailVo getSettlementBillDetail(Long id) {
        SettlementBill settlementBill = billMapper.selectById(id);
        if (Objects.isNull(settlementBill)) {
//            throw new GetServiceException("结算账单不存在!");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "INSURANCE_BILL_NOT_EXIST", "结算账单不存在"));
        }

        //结算账单明细
        List<SettlementBillItemVo> billItems = billItemMapper.selectSettlementBillItemListByBillId(id);
        List<Long> payablePlanIds = billItems.stream().map(SettlementBillItemVo::getPayablePlanId).collect(Collectors.toList());
        Map<Long, PayablePlanVo> planMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(payablePlanIds)) {
            planMap = settlementMapper.getPayablePlanList(payablePlanIds)
                    .stream()
                    .collect(Collectors.toMap(
                            PayablePlanVo::getPayablePlanId,
                            Function.identity()));
        }
        //填充结算比例和结算金额
        Map<Long, PayablePlanVo> finalPlanMap = planMap;
        billItems.stream().forEach(item -> {
            item.setSettlementAmount(BigDecimal.ZERO);
            BigDecimal commissionRate = finalPlanMap.getOrDefault(item.getPayablePlanId(), new PayablePlanVo()).getCommissionRate();
            if (Objects.nonNull(commissionRate)) {
                item.setCommissionRate(commissionRate.setScale(2, RoundingMode.HALF_UP));
                BigDecimal settlementAmount = item.getInsuranceAmount().multiply(commissionRate.divide(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP));
                item.setSettlementAmount(settlementAmount);
                item.setCommissionRateStr(commissionRate.setScale(2, RoundingMode.HALF_UP) + "%");
            }
        });
        //订单币种结算总额
        BigDecimal originSettlementAmount = billItems.stream()
                .map(SettlementBillItemVo::getSettlementAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(4, RoundingMode.HALF_UP);

        //代理账户
        AgentAccountVo agentAccount = saleCenterMapper.getAgentAccount(settlementBill.getFkAgentContractAccountId());

        //签名
        SettlementBillSignature billSignature = billSignatureMapper.selectOne(new LambdaQueryWrapper<SettlementBillSignature>().eq(SettlementBillSignature::getFkSettlementBillId, id));

        SettlementBillDetailVo detail = SettlementBillDetailVo.builder()
                .settlementBillId(id)
                .settlementCurrencyTypeNum(settlementBill.getFkCurrencyTypeNum())
                .totalSettlementAmount(originSettlementAmount)
                .rate(null)
                .originTotalSettlementAmount(originSettlementAmount)
                .orderCurrencyTypeNum(CollectionUtils.isNotEmpty(billItems) ? billItems.get(0).getFkCurrencyTypeNum() : null)
                .signature(Objects.nonNull(billSignature) ? billSignature.getSignature() : null)
                .billItems(billItems)
                .agentAccount(agentAccount)
                .build();
        //结算币种不一样-获取汇率
        if (Objects.nonNull(detail.getSettlementCurrencyTypeNum()) &&
                !detail.getOrderCurrencyTypeNum().equals(agentAccount.getCurrencyTypeNum())) {
            RateDetail rateDetail = exchangeRateUtils.getRateDetail(detail.getOrderCurrencyTypeNum(), detail.getSettlementCurrencyTypeNum());
            if (Objects.isNull(rateDetail) || Objects.isNull(rateDetail.getRate())) {
                log.error("获取汇率异常,汇率结果为空:{}", JSONObject.toJSONString(rateDetail));
//                throw new GetServiceException("获取汇率失败");
                throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "INSURANCE_EXCHANGE_RATE_FAIL", "获取汇率失败"));
            }
            BigDecimal totalSettlementAmount = detail.getOriginTotalSettlementAmount().multiply(rateDetail.getRate()).setScale(4, RoundingMode.HALF_UP);
            detail.setTotalSettlementAmount(totalSettlementAmount);
            detail.setRate(rateDetail.getRate());
        }
        return detail;
    }

    @Override
    @SneakyThrows
    public void downloadSettlementBill(Long id, HttpServletResponse response) {
        //如果已经上传了,直接从腾讯云下载
        List<InsuranceMediaAndAttached> attachedList = attachedService.getBaseMapper().selectList(new LambdaQueryWrapper<InsuranceMediaAndAttached>()
                .eq(InsuranceMediaAndAttached::getFkTableName, "m_settlement_bill")
                .eq(InsuranceMediaAndAttached::getFkTableId, id)
                .orderByDesc(InsuranceMediaAndAttached::getGmtCreate));
        if (CollectionUtils.isNotEmpty(attachedList)) {
            InsuranceMediaAndAttached mediaAndAttached = attachedList.get(0);
            AppFileCenter appFileCenter = appFileCenterMapper.selectAppFileByGuid(mediaAndAttached.getFkFileGuid());
            if (Objects.nonNull(appFileCenter)) {
                log.info("从桶里下载文件,fileGuid:{},fileKey:{}", appFileCenter.getFileGuid(), appFileCenter.getFileKey());
                attachedService.downloadFile(appFileCenter, response);
                return;
            }
        }
        //没有上传或者找不到文件,重新生成并且上传和下载
        SettlementBillDetailVo billDetail = getSettlementBillDetail(id);
        log.info("生成对账单并且重新上传到私密桶,账单ID:{}", id);
        pdfExportUtils.downloadPdf(billDetail, response, true);
    }
}
