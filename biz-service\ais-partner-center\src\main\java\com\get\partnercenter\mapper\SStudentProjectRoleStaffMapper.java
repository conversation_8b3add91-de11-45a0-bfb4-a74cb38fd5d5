package com.get.partnercenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.partnercenter.entity.SStudentProjectRoleStaffEntity;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【s_student_project_role_staff】的数据库操作Mapper
* @createDate 2025-05-19 15:33:40
* @Entity com.get.partnercenter.entity.SStudentProjectRoleStaff
*/
@Mapper
@DS("saledb")
public interface SStudentProjectRoleStaffMapper extends BaseMapper<SStudentProjectRoleStaffEntity> {

}




