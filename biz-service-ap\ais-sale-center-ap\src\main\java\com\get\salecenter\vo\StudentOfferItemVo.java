package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import com.get.platformconfigcenter.entity.UserInfo;
import com.get.salecenter.entity.StudentOfferItemDeferEntranceTime;
import com.get.salecenter.entity.StudentOfferItemStep;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/11/18
 * @TIME: 12:38
 * @Description:
 **/
@Data
public class StudentOfferItemVo extends BaseEntity {

    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;

    /**
     * 子項目数组
     */
    @ApiModelProperty(value = "子項目数组")
    private List<ChildRenStudentOfferItemVo> childRenStudentOfferItemDtos;
    /**
     * 记录总名称
     */
    @ApiModelProperty(value = "记录总名称")
    private String targetNames;
    /**
     * 提供商名称
     */
    @ApiModelProperty(value = "提供商名称")
    private String fkInstitutionProviderName;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String fkInstitutionName;

    /**
     * 学校业务标签
     */
    @ApiModelProperty(value = "学校业务标签")
    private String label;

    /**
     * 学校业务标签显示位置：0名字前面/1名字后面
     */
    @ApiModelProperty(value = "学校业务标签显示位置：0名字前面/1名字后面")
    private Integer positionType;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称")
    private String fkCourseName;

    /**
     * 课程等级名称
     */
    @ApiModelProperty(value = "课程等级名称")
    private String majorLeavelName;

    /**
     * 课程类型名称
     */
    @ApiModelProperty(value = "课程类型名称")
    private String courseTypeName;

    /**
     * 自定义课程名称
     */
    @ApiModelProperty(value = "自定义课程名称")
    private String customCourseName;

    /**
     * 学校校区名
     */
    @ApiModelProperty(value = "学校校区名")
    private String fkInstitutionZoneName;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程中文名称")
    private String fkCourseNameChn;

    /**
     * 当前执行到的步骤名称
     */
    @ApiModelProperty(value = "当前执行到的步骤名称")
    private String fkItemStepName;


    /**
     * 学生名称
     */
    @ApiModelProperty(value = "学生名称")
    private String fkStudentName;

    /**
     * 学生名称
     */
    @ApiModelProperty(value = "学生英文名称")
    private String fkStudentNameEn;

    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    private String fkAgentName;

    /**
     * 状态：0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销
     */
    @ApiModelProperty(value = "状态：0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销")
    private Integer statusWorkflow;

    /**
     * 应收/应付计划id
     */
    private Long planId;

    @ApiModelProperty("币种中英")
    private String fkCurrencyTypeNumName;

    /**
     * 学习计划业务状态名
     */
    @ApiModelProperty(value = "学习计划业务状态名")
    private String conditionTypeName;

    /**
     * 学习计划业务状态名（只展示只读）
     */
    @ApiModelProperty(value = "学习计划业务状态名（展示只读）")
    private String conditionTypeOnlyName;

    /**
     * 学习计划业务状态名（只展示获得）
     */
    @ApiModelProperty(value = "学习计划业务状态名（展示获得）")
    private String conditionTypeGetName;

    /**
     * 学校Id(前置学校)
     */
    @ApiModelProperty(value = "学校Id(前置学校)")
    private Long fkPreInstitutionId;

    /**
     * 学校集团Id(前置集团)
     */
    @ApiModelProperty(value = "学校集团Id(前置集团)")
    private Long fkPreInstitutionGroupId;

    /**
     * 专业等级Id(前置等级)
     */
    @ApiModelProperty(value = "专业等级Id(前置等级)")
    private Long fkPreMajorLevelId;

    /**
     * 学校名字(前置学校)
     */
    @ApiModelProperty(value = "学校名字(前置学校)")
    private String fkPreInstitutionName;

    /**
     * 学校集团名字(前置集团)
     */
    @ApiModelProperty(value = "学校集团名字(前置集团)")
    private String fkPreInstitutionGroupName;

    /**
     * 专业等级名字(前置等级)
     */
    @ApiModelProperty(value = "专业等级名字(前置等级)")
    private String fkPreMajorLevelName;

    /**
     * 入学失败原因
     */
    @ApiModelProperty(value = "入学失败原因")
    private String fkEnrolFailureReason;

    /**
     * 其他入学失败原因
     */
    @ApiModelProperty(value = "其他入学失败原因")
    private String otherFailureReason;

    /**
     * 是否延迟入学标记：0否/1是
     */
    @ApiModelProperty(value = "是否延迟入学标记：0否/1是")
    private Boolean isDeferEntrance;

    @ApiModelProperty(value = "延迟入学时间")
    private List<StudentOfferItemDeferEntranceTime> studentOfferItemDeferEntranceTimes;


    @ApiModelProperty(value = "当前执行到的步骤对象")
    private StudentOfferItemStep studentOfferItemStep;

    /**
     * 申请步骤排序，由0开始按顺序排列
     */
    @ApiModelProperty(value = "申请步骤排序，由0开始按顺序排列")
    private Integer stepOrder;

    /**
     * 申请步骤名
     */
    @ApiModelProperty(value = "申请步骤名")
    private String stepName;

    /**
     * 数量
     */
    @ApiModelProperty(value = "学习计划数量")
    private Long itemNum;

    /**
     * 学习模式名称
     */
    @ApiModelProperty(value = "学习模式名称")
    private String learningModeName;

    /**
     * 学校提供商下拉框副id（渠道id）
     */
    @ApiModelProperty(value = "学校提供商下拉框副id（渠道id）")
    private Long deputyId;

    /**
     * 申请方案编号
     */
    @ApiModelProperty(value = "申请方案编号")
    private String studentOfferNum;

    /**
     * 学生姓名
     */
    @ApiModelProperty(value = "学生姓名")
    private String studentName;

    /**
     * 学生id
     */
    @ApiModelProperty(value = "学生id")
    private Long fkStudentId;


    /**
     * 学生生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "学生生日")
    private Date birthday;

    /**
     * 学生国籍所在国家Id
     */
    @ApiModelProperty(value = "学生国籍所在国家Id")
    private Long fkAreaCountryIdNationality;

    /**
     * 学生国籍所在国家名称
     */
    @ApiModelProperty(value = "学生国籍所在国家名称")
    private String fkAreaCountryNameNationality;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String institutionFullName;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称")
    private String courseFullName;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程类型")
    private String courseType;

    /**
     * 学习方案状态：0关闭/1打开
     */
    @ApiModelProperty(value = "学习方案状态：0关闭/1打开")
    private Integer offerStatus;


    /**
     * @Description: 是否可以生成应收应付
     * @Author: Jerry
     * @Date:12:49 2021/11/19
     */
    @ApiModelProperty(value = "是否可以生成应收应付 true:可生成 false:不可生成")
    private Boolean isSureGeneratePlan;

    /**
     * @Description: 是否可以更新应收应付
     * @Author: Jerry
     * @Date:12:49 2021/11/19
     */
    @ApiModelProperty(value = "是否可以更新应收应付 true:可更新 false:不可更新")
    private Boolean isSureUpdateGeneratePlan;

    @ApiModelProperty(value = "是否有绑定应收应付  true：绑定 false：未绑定")
    private Boolean existsPlanFlag = false;

    /**
     * issueCourseId
     */
    @ApiModelProperty(value = "issueCourseId")
    private Long issueCourseId;

    /**
     * 验证的key
     */
    @ApiModelProperty(value = "验证的key")
    private String key;

    /**
     * 一键申请状态
     */
    @ApiModelProperty(value = "一键申请状态:N=New, C=Completed, E=Error")
    private String robotStatus;


    /**
     * 次课列表
     */
    @ApiModelProperty(value = "次课列表")
    private List<StudentOfferItemVo> studentOfferItemDtos;

    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "渠道来源名")
    private String fkInstitutionChannelName;

    @ApiModelProperty(value = "最大延迟入学时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date maxDeferEntranceTimes;

    @ApiModelProperty(value = "业务信息")
    private String businessInformation;
    /**
     * 公寓名称
     */
    @ApiModelProperty(value = "公寓名称")
    private String apartmentName;

    /**
     * 保险有效日期
     */
    @ApiModelProperty(value = "保险有效日期")
    private String insuranceEffectiveDate;

    /**
     * 入住信息
     */
    @ApiModelProperty(value = "入住信息")
    private String checkInInformation;

    @ApiModelProperty(value = "true:可以一键申请 false:不可以")
    private boolean applyFlag;

    /**
     * 区分是否为台湾学生
     */
    @ApiModelProperty(value = "区分是否为台湾学生")
    private Boolean isTwStudent;

    @ApiModelProperty(value = "agentId")
    private Long agentId;

    @ApiModelProperty(value = "agentIdGea")
    private String agentIdGea;

    /**
     * 学生申请学校课程Id（ISSUEv2版申请）
     */
    @ApiModelProperty(value = "学生申请学校课程Id（ISSUEv2版申请）")
    private Long fkStudentInstitutionCourseIdIssue2;

    /**
     * 学生Id（ISSUEv2版申请）
     */
    @ApiModelProperty(value = "学生Id（ISSUEv2版申请）")
    private Long fkStudentIdIssue2;

    @ApiModelProperty(value = "新申请状态名称")
    private String newAppStatusName;

    /**
     * 新数据id(issue)
     */
    @ApiModelProperty(value = "新数据id(issue)")
    private Long idIssueNew;

    @ApiModelProperty(value = "旧iussue用户信息")
    UserInfo userInfoDto;

    @ApiModelProperty(value = "新iussue用户信息")
    UserInfo userInfoNewDto;

    @ApiModelProperty(value = "角色员工")
    private List<StudentOfferRoleAndStaffVo> studentOfferRoleAndStaff;

    @ApiModelProperty(value = "2022年后续 true:有后续 false:没有后续")
    private Boolean existsFollow;

    @ApiModelProperty(value = "isCanStudent")
    private Boolean isCanStudent;

    @ApiModelProperty("保险购买方式")
    private String insuranceBuyMethodName;

    @ApiModelProperty("押金支付方式枚举：0飞汇/1易思汇/2支付宝/3银行/4信用卡/5其他汇款平台")
    private String depositPaymentMethodName;

    @ApiModelProperty("学费支付方式枚举：0=飞汇/1=易思汇/2=支付宝/3=银行/4=信用卡/5=其他汇款平台")
    private String tuitionPaymentMethodName;

    @ApiModelProperty("缺资料")
    private List<MediaAndAttachedVo> missMediaAndAttachedDtoList;

    /**
     * 申请费币种
     */
    @ApiModelProperty(value = "申请费币种")
    private String fkAppFeeCurrencyTypeNum;

    /**
     * 学校或者课程的tableName，为空表示无申请费
     */
    @ApiModelProperty(value = "学校或者课程的tableName，为空表示无申请费")
    private String appFeeFkTableName;

    /**
     * 学校或者课程的tableId，为空表示无申请费
     */
    @ApiModelProperty(value = "学校或者课程的tableId，为空表示无申请费")
    private Long appFeeFkTableId;

    /**
     * 学校或者课程的名称，用于模糊查询
     */
    @ApiModelProperty(value = "学校或者课程的名称，用于模糊查询")
    private String appFeeKeyword;

    @ApiModelProperty(value = "学费付费状态名称")
    private String tuitionStatusName;

    @ApiModelProperty(value = "申请费币种名称")
    private String fkAppFeeCurrencyTypeNumName;

    @ApiModelProperty(value = "申请费付费状态名称")
    private String appFeeStatusName;



    //==========实体类StudentOfferItem===============
    private static final long serialVersionUID = 1L;

    /**
     * 申请费付费状态：0.付费失败 1.已发送代付邮件 2.已付费(支付渠道) 3.已付费(学校)
     */
    @ApiModelProperty(value = "申请费付费状态：0.付费失败 1.已发送代付邮件 2.已付费(支付渠道) 3.已付费(学校)")
    @Column(name = "app_fee_status")
    private Integer appFeeStatus;

    /**
     * 学费付费状态：0.付费失败 1.已发送代付邮件 2.已付费(支付渠道) 3.已付费(学校)
     */
    @ApiModelProperty(value = "学费付费状态：0.付费失败 1.已发送代付邮件 2.已付费(支付渠道) 3.已付费(学校)")
    @Column(name = "tuition_status")
    private Integer tuitionStatus;

    /**
     * 学生申请方案项目父Id
     */
    @ApiModelProperty(value = "学生申请方案项目父Id")
    @Column(name = "fk_parent_student_offer_item_id")
    private Long fkParentStudentOfferItemId;

    /**
     * 代理Id（业绩绑定）
     */
    @ApiModelProperty(value = "代理Id（业绩绑定）")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 员工Id（业绩绑定，BD）
     */
    @ApiModelProperty(value = "员工Id（业绩绑定，BD）")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;
    /**
     * 学生申请方案Id
     */
    @ApiModelProperty(value = "学生申请方案Id")
    @Column(name = "fk_student_offer_id")
    private Long fkStudentOfferId;
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;
    /**
     * 学校学院Id
     */
    @ApiModelProperty(value = "学校学院Id")
    @Column(name = "fk_institution_faculty_id")
    private Long fkInstitutionFacultyId;
    /**
     * 学校校区Id
     */
    @ApiModelProperty(value = "学校校区Id")
    @Column(name = "fk_institution_zone_id")
    private Long fkInstitutionZoneId;
    /**
     * 课程Id
     */
    @ApiModelProperty(value = "课程Id")
    @Column(name = "fk_institution_course_id")
    private Long fkInstitutionCourseId;
    /**
     * 自定义课程Id
     */
    @ApiModelProperty(value = "自定义课程Id")
    @Column(name = "fk_institution_course_custom_id")
    @UpdateWithNull
    private Long fkInstitutionCourseCustomId;
    /**
     * 学校提供商Id
     */
    @ApiModelProperty(value = "学校提供商Id")
    @Column(name = "fk_institution_provider_id")
    @UpdateWithNull
    private Long fkInstitutionProviderId;
    /**
     * 渠道来源Id
     */
    @ApiModelProperty(value = "渠道来源Id")
    @Column(name = "fk_institution_channel_id")
    private Long fkInstitutionChannelId;
    /**
     * 学生申请方案项目状态步骤Id
     */
    @ApiModelProperty(value = "学生申请方案项目状态步骤Id")
    @Column(name = "fk_student_offer_item_step_id")
    private Long fkStudentOfferItemStepId;

    @ApiModelProperty(value = "申请方案项目状态步骤时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "student_offer_item_step_time")
    private Date studentOfferItemStepTime;

    /**
     * rpa order id（一键申请对应的Order id）
     */
    @ApiModelProperty(value = "rpa order id（一键申请对应的Order id）")
    @Column(name = "fk_issue_rpa_order_id")
    private Long fkIssueRpaOrderId;
    /**
     * 申请方案项目编号
     */
    @ApiModelProperty(value = "申请方案项目编号")
    @Column(name = "num")
    private String num;
    /**
     * 学生ID
     */
    @ApiModelProperty(value = "学生ID")
    @Column(name = "student_id")
    private String studentId;
    /**
     * 课程长度类型(0周、1学期、2年)
     */
    @UpdateWithNull
    @ApiModelProperty(value = "课程长度类型(0周、1学期、2年)")
    @Column(name = "duration_type")
    private Integer durationType;
    /**
     * 课程长度
     */
    @UpdateWithNull
    @ApiModelProperty(value = "课程长度")
    @Column(name = "duration")
    private BigDecimal duration;
    /**
     * 开学时间
     */
    @ApiModelProperty(value = "开学时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "opening_time")
    private Date openingTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "closing_time")
    private Date closingTime;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;
    /**
     * 学费金额
     */
    @ApiModelProperty(value = "学费金额")
    @Column(name = "tuition_amount")
    private BigDecimal tuitionAmount;
    /**
     * 课程官网URL
     */
    @ApiModelProperty(value = "课程官网URL")
    @Column(name = "course_website")
    private String courseWebsite;
    /**
     * 是否主课程：0否/1是（一套方案，只有一个主课程）
     */
    @ApiModelProperty(value = "是否主课程：0否/1是（一套方案，只有一个主课程）")
    @Column(name = "is_main")
    private Boolean isMain;
    /**
     * 是否后续课程：0否/1是
     */
    @ApiModelProperty(value = "是否后续课程：0否/1是")
    @Column(name = "is_follow")
    private Boolean isFollow;

    /**
     * 是否后续课程隐藏：0否/1是
     */
    @ApiModelProperty(value = "是否后续课程隐藏：0否/1是")
    @Column(name = "is_follow_hidden")
    private Boolean isFollowHidden = false;
    /**
     * 是否减免学分：0否/1是
     */
    @ApiModelProperty(value = "是否减免学分：0否/1是")
    @Column(name = "is_credit_exemption")
    private Boolean isCreditExemption;
    /**
     * 是否加申，0否/1是
     */
    @ApiModelProperty(value = "是否加申，0否/1是")
    @Column(name = "is_add_app")
    private Boolean isAddApp;

//    /**
//     * 是否记录跟进步骤，0否/1是
//     */
//    @ApiModelProperty(value = "是否记录跟进步骤，0否/1是")
//    @Column(name = "is_add_step")
//    private Boolean isAddStep;
    /**
     * 是否步骤更随主课，0否/1是
     */
    @ApiModelProperty(value = "是否步骤更随主课，0否/1是")
    @Column(name = "is_step_follow")
    private Boolean isStepFollow;

    /**
     * 是否无佣金：0否/1是
     */
    @ApiModelProperty(value = "是否无佣金：0否/1是")
    @Column(name = "is_no_commission")
    private Boolean isNoCommission;

    /**
     * 申请方式：0网申/1扫描/2原件邮递/3其他
     */
    @ApiModelProperty(value = "申请方式：0网申/1扫描/2原件邮递/3其他")
    @Column(name = "app_method")
    private Integer appMethod;
    /**
     * 申请备注（网申信息）
     */
    @ApiModelProperty(value = "申请备注（网申信息）")
    @UpdateWithNull
    @Column(name = "app_remark")
    private String appRemark;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 学习计划业务状态(多选)：0获得第一阶段佣金、1获得第二阶段佣金、2获得第三阶段佣金、3获得第四阶段佣金 / 4只读第一阶段、5只读第二阶段、6只读第三阶段、7只读第四阶段
     */
    @ApiModelProperty(value = "学习计划业务状态(多选)：0获得第一阶段佣金、1获得第二阶段佣金、2获得第三阶段佣金、3获得第四阶段佣金 / 4只读第一阶段、5只读第二阶段、6只读第三阶段、7只读第四阶段")
    @Column(name = "condition_type")
    private String conditionType;
    /**
     * 入学失败原因Id
     */
    @UpdateWithNull
    @ApiModelProperty(value = "入学失败原因Id")
    @Column(name = "fk_enrol_failure_reason_id")
    private Long fkEnrolFailureReasonId;

    /**
     * 学习模式：枚举定义：0未定/1面授/2网课
     */
    @ApiModelProperty(value = "学习模式：枚举定义：0未定/1面授/2网课")
    @Column(name = "learning_mode")
    private Integer learningMode;


    @ApiModelProperty("支付押金时间")
    @Column(name = "deposit_time")
    @UpdateWithNull
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date depositTime;


    @ApiModelProperty("提交申请时间")
    @Column(name = "submit_app_time")
    @UpdateWithNull
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date submitAppTime;

    /**
     * 支付押金截止时间
     */
    @ApiModelProperty(value = "支付押金截止时间")
    @Column(name = "deposit_deadline")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @UpdateWithNull
    private Date depositDeadline;
    /**
     * 接受Offer截止时间
     */
    @ApiModelProperty(value = "接受Offer截止时间")
    @Column(name = "accept_offer_deadline")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @UpdateWithNull
    private Date acceptOfferDeadline;

    @ApiModelProperty(value = "支付学费时间")
    @Column(name = "tuition_time")
    @UpdateWithNull
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date tuitionTime;
    /**
     * 状态：0关闭/1打开
     */
    @ApiModelProperty(value = "状态：0关闭/1打开")
    @Column(name = "status")
    private Integer status;
    /**
     * 一键操作时间
     */
    @ApiModelProperty(value = "一键操作时间")
    @Column(name = "rpa_opt_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date rpaOptTime;
    /**
     * 一键完成时间
     */
    @ApiModelProperty(value = "一键完成时间")
    @Column(name = "rpa_finish_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date rpaFinishTime;
    /**
     * 一键处理状态：枚举
     */
    @ApiModelProperty(value = "一键处理状态：枚举")
    @Column(name = "status_rpa")
    private String statusRpa;
    /**
     * 来自平台类型：get_mso
     */
    @ApiModelProperty(value = "来自平台类型：get_mso")
    @Column(name = "fk_platform_type")
    private String fkPlatformType;
    /**
     * 旧系统学校名称
     */
    @ApiModelProperty(value = "旧系统学校名称")
    @Column(name = "old_institution_name")
    private String oldInstitutionName;
    /**
     * 旧系统学校全称
     */
    @ApiModelProperty(value = "旧系统学校全称")
    @Column(name = "old_institution_full_name")
    private String oldInstitutionFullName;
    /**
     * 旧系统课程名称
     */
    @ApiModelProperty(value = "旧系统课程名称")
    @Column(name = "old_course_custom_name")
    private String oldCourseCustomName;
    /**
     * 旧系统课程专业等级名称
     */
    @ApiModelProperty(value = "旧系统课程专业等级名称")
    @Column(name = "old_course_major_level_name")
    private String oldCourseMajorLevelName;
    /**
     * 旧系统课程类型名称
     */
    @ApiModelProperty(value = "旧系统课程类型名称")
    @Column(name = "old_course_type_name")
    private String oldCourseTypeName;
    /**
     * ISSUE课程输入标记：input/select
     */
    @ApiModelProperty(value = "ISSUE课程输入标记：input/select")
    @Column(name = "issue_course_input_flag")
    private String issueCourseInputFlag;
    /**
     * 旧数据财务id(gea)
     */
    @ApiModelProperty(value = "旧数据财务id(gea)")
    @Column(name = "id_gea_finance")
    private String idGeaFinance;
    /**
     * 旧数据id(issue)
     */
    @UpdateWithNull
    @ApiModelProperty(value = "旧数据id(issue)")
    @Column(name = "id_issue")
    private String idIssue;
    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    @Column(name = "id_gea")
    private String idGea;
    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    @Column(name = "id_iae")
    private String idIae;
    /**
     * 一键备注
     */
    @ApiModelProperty(value = "一键备注")
    @Column(name = "rpa_remark")
    private String rpaRemark;
    /**
     * 课程等级对应的ids，多个用逗号分隔
     */
    @ApiModelProperty(value = "课程等级对应的ids，多个用逗号分隔")
    @Column(name = "fk_institution_course_major_level_ids")
    @UpdateWithNull
    private String fkInstitutionCourseMajorLevelIds;
    /**
     * 课程类型对应的ids，多个用逗号分隔
     */
    @ApiModelProperty(value = "课程类型对应的ids，多个用逗号分隔")
    @Column(name = "fk_institution_course_type_ids")
    @UpdateWithNull
    private String fkInstitutionCourseTypeIds;

    @ApiModelProperty(value = "课程类型组别对应的ids，多个用逗号分隔")
    @Column(name = "fk_institution_course_type_group_ids")
    @UpdateWithNull
    private String fkInstitutionCourseTypeGroupIds;


    @ApiModelProperty("申请计划延时入学时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deferOpeningTime;

    @ApiModelProperty(value = "新申请状态：枚举：0缺资料/1未开放")
    @UpdateWithNull
    @Column(name = "new_app_status")
    private Integer newAppStatus;

    @ApiModelProperty(value = "课程开放时间")
    @Column(name = "course_open_time")
    @UpdateWithNull
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date courseOpenTime;

    //new_app_opt_time
    @ApiModelProperty(value = "新申请操作时间")
    @Column(name = "new_app_opt_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date newAppOptTime;

    @ApiModelProperty("保险购买方式枚举：0买学校保险/1通过Hti买保险/2通过其他机构买保险")
    @Column(name = "insurance_buy_method")
    @UpdateWithNull
    private Integer insuranceBuyMethod;

    @ApiModelProperty("押金支付方式枚举：0飞汇/1易思汇/2支付宝/3银行/4信用卡/5其他汇款平台")
    @Column(name = "deposit_payment_method")
    @UpdateWithNull
    private Integer depositPaymentMethod;

    @ApiModelProperty("学费支付方式枚举：0=飞汇/1=易思汇/2=支付宝/3=银行/4=信用卡/5=其他汇款平台")
    @Column(name = "tuition_payment_method")
    @UpdateWithNull
    private Integer tuitionPaymentMethod;

    @ApiModelProperty("一键状态信息")
    private String robotReturnMsg;

    @ApiModelProperty("代理标签")
    private List<AgentLabelVo> agentLabelVos;

    @ApiModelProperty(value = "保单开始时间")
    private Date insuranceStartTime;

    @ApiModelProperty(value = "保单结束时间")
    private Date insuranceEndTime;

    @ApiModelProperty(value = "保险单号")
    private String insuranceNum;

    @ApiModelProperty(value = "保险产品类型名称")
    private String productTypeName;

}
