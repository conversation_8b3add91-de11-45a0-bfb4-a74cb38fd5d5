package com.get.votingcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: Hardy
 * @create: 2021/9/24 11:15
 * @verison: 1.0
 * @description:
 */
@Data
public class VotingItemListDto extends BaseVoEntity implements Serializable {
    /**
     * 投票主题Id
     */
    @ApiModelProperty(value = "投票主题Id")
    private Long fkVotingId;

    /**
     * 投票项标题
     */
    @ApiModelProperty(value = "投票项标题")
    private String title;

    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer viewOrder;

}
