<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionCourseStudyModeMapper">
<!--  <insert id="insert" parameterType="com.get.institutioncenter.entity.InstitutionCourseStudyMode">-->
<!--    insert into m_institution_course_study_mode (id, fk_institution_course_id, fk_app_info_id, -->
<!--      app_fee, fee, fee_note, -->
<!--      duration_type, duration, duration_note, -->
<!--      start_year, start_month, start_date_note, -->
<!--      apply_date, apply_date_note, apply_date_deadline, -->
<!--      apply_date_deadline_note, teaching_language, -->
<!--      study_mode, remark, is_active, -->
<!--      gmt_create, gmt_create_user, gmt_modified, -->
<!--      gmt_modified_user)-->
<!--    values (#{id,jdbcType=BIGINT}, #{fkInstitutionCourseId,jdbcType=BIGINT}, #{fkAppInfoId,jdbcType=BIGINT}, -->
<!--      #{appFee,jdbcType=DECIMAL}, #{fee,jdbcType=DECIMAL}, #{feeNote,jdbcType=VARCHAR}, -->
<!--      #{durationType,jdbcType=INTEGER}, #{duration,jdbcType=DECIMAL}, #{durationNote,jdbcType=VARCHAR}, -->
<!--      #{startYear,jdbcType=VARCHAR}, #{startMonth,jdbcType=VARCHAR}, #{startDateNote,jdbcType=VARCHAR}, -->
<!--      #{applyDate,jdbcType=VARCHAR}, #{applyDateNote,jdbcType=VARCHAR}, #{applyDateDeadline,jdbcType=VARCHAR}, -->
<!--      #{applyDateDeadlineNote,jdbcType=VARCHAR}, #{teachingLanguage,jdbcType=VARCHAR}, -->
<!--      #{studyMode,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{isActive,jdbcType=BIT}, -->
<!--      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, -->
<!--      #{gmtModifiedUser,jdbcType=VARCHAR})-->
<!--  </insert>-->
<!--  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionCourseStudyMode">-->
<!--    insert into m_institution_course_study_mode-->
<!--    <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        id,-->
<!--      </if>-->
<!--      <if test="fkInstitutionCourseId != null">-->
<!--        fk_institution_course_id,-->
<!--      </if>-->
<!--      <if test="fkAppInfoId != null">-->
<!--        fk_app_info_id,-->
<!--      </if>-->
<!--      <if test="appFee != null">-->
<!--        app_fee,-->
<!--      </if>-->
<!--      <if test="fee != null">-->
<!--        fee,-->
<!--      </if>-->
<!--      <if test="feeNote != null">-->
<!--        fee_note,-->
<!--      </if>-->
<!--      <if test="durationType != null">-->
<!--        duration_type,-->
<!--      </if>-->
<!--      <if test="duration != null">-->
<!--        duration,-->
<!--      </if>-->
<!--      <if test="durationNote != null">-->
<!--        duration_note,-->
<!--      </if>-->
<!--      <if test="startYear != null">-->
<!--        start_year,-->
<!--      </if>-->
<!--      <if test="startMonth != null">-->
<!--        start_month,-->
<!--      </if>-->
<!--      <if test="startDateNote != null">-->
<!--        start_date_note,-->
<!--      </if>-->
<!--      <if test="applyDate != null">-->
<!--        apply_date,-->
<!--      </if>-->
<!--      <if test="applyDateNote != null">-->
<!--        apply_date_note,-->
<!--      </if>-->
<!--      <if test="applyDateDeadline != null">-->
<!--        apply_date_deadline,-->
<!--      </if>-->
<!--      <if test="applyDateDeadlineNote != null">-->
<!--        apply_date_deadline_note,-->
<!--      </if>-->
<!--      <if test="teachingLanguage != null">-->
<!--        teaching_language,-->
<!--      </if>-->
<!--      <if test="studyMode != null">-->
<!--        study_mode,-->
<!--      </if>-->
<!--      <if test="remark != null">-->
<!--        remark,-->
<!--      </if>-->
<!--      <if test="isActive != null">-->
<!--        is_active,-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        gmt_create,-->
<!--      </if>-->
<!--      <if test="gmtCreateUser != null">-->
<!--        gmt_create_user,-->
<!--      </if>-->
<!--      <if test="gmtModified != null">-->
<!--        gmt_modified,-->
<!--      </if>-->
<!--      <if test="gmtModifiedUser != null">-->
<!--        gmt_modified_user,-->
<!--      </if>-->
<!--    </trim>-->
<!--    <trim prefix="values (" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        #{id,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="fkInstitutionCourseId != null">-->
<!--        #{fkInstitutionCourseId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="fkAppInfoId != null">-->
<!--        #{fkAppInfoId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="appFee != null">-->
<!--        #{appFee,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="fee != null">-->
<!--        #{fee,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="feeNote != null">-->
<!--        #{feeNote,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="durationType != null">-->
<!--        #{durationType,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="duration != null">-->
<!--        #{duration,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="durationNote != null">-->
<!--        #{durationNote,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="startYear != null">-->
<!--        #{startYear,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="startMonth != null">-->
<!--        #{startMonth,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="startDateNote != null">-->
<!--        #{startDateNote,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="applyDate != null">-->
<!--        #{applyDate,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="applyDateNote != null">-->
<!--        #{applyDateNote,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="applyDateDeadline != null">-->
<!--        #{applyDateDeadline,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="applyDateDeadlineNote != null">-->
<!--        #{applyDateDeadlineNote,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="teachingLanguage != null">-->
<!--        #{teachingLanguage,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="studyMode != null">-->
<!--        #{studyMode,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="remark != null">-->
<!--        #{remark,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="isActive != null">-->
<!--        #{isActive,jdbcType=BIT},-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        #{gmtCreate,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtCreateUser != null">-->
<!--        #{gmtCreateUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="gmtModified != null">-->
<!--        #{gmtModified,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtModifiedUser != null">-->
<!--        #{gmtModifiedUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--    </trim>-->
<!--  </insert>-->

  <select id="checkCourseWebsite" resultType="java.lang.Long">
      SELECT mic.id FROM m_institution_course_study_mode AS micsm
    INNER JOIN s_app_info AS ai ON ai.id = micsm.fk_app_info_id
        INNER JOIN  m_institution_course AS mic ON mic.id = micsm.fk_institution_course_id
    WHERE mic.fk_institution_id = #{fkInstitutionId} AND ai.type_value = #{courseWebsite}
    LIMIT 1
    </select>
</mapper>