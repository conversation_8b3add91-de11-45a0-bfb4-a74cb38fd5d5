package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.ConventionPersonInstitution;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/27 12:21
 * @verison: 1.0
 * @description: 参展人员和学校关联中间表mapper
 */
@Mapper
public interface ConventionPersonInstitutionMapper extends BaseMapper<ConventionPersonInstitution> {
    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insert(ConventionPersonInstitution record);

    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(ConventionPersonInstitution record);

    /**
     * 通过学校id查找对应的参展人员ids
     *
     * @param institutionIds
     * @return
     */
    List<Long> getConventionPersonIds(List<Long> institutionIds);

    /**
     * 通过参展人员id查找对应的学校id
     *
     * @param id
     * @return
     */
    Long getInstitutionId(Long id);
}