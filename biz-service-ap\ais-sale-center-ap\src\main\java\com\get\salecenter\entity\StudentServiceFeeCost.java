package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-05
 */
@Data
@TableName("m_student_service_fee_cost")
@ApiModel(value="StudentServiceFeeCost对象", description="")
public class StudentServiceFeeCost extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "学生留学服务费id")
    @Column(name = "fk_student_service_fee_id")
    private Long fkStudentServiceFeeId;

    @ApiModelProperty(value = "业务提供商Id")
    @Column(name = "fk_business_provider_id")
    private Long fkBusinessProviderId;

    @ApiModelProperty(value = "币种")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "金额")
    @Column(name = "amount")
    private BigDecimal amount;

    @ApiModelProperty("税金")
    @Column(name = "taxes")
    private BigDecimal taxes;

    @ApiModelProperty("银行帐号Id（公司付款银行）")
    @Column(name = "fk_bank_account_id_company")
    private Long fkBankAccountIdCompany;

    @ApiModelProperty(value = "付款日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "payment_time")
    private Date paymentTime;

    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    @ApiModelProperty(value = "状态：0作废/1生效")
    @Column(name = "status")
    private Integer status;

}
