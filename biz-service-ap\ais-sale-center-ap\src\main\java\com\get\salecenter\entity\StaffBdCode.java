package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_staff_bd_code")
public class StaffBdCode extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;
    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;
    /**
     * BD编号（4位）
     */
    @ApiModelProperty(value = "BD编号（4位）")
    @Column(name = "bd_code")
    private String bdCode;
    /**
     * 颜色编码，用于突显，可不填
     */
    @ApiModelProperty(value = "颜色编码，用于突显，可不填")
    @Column(name = "color_code")
    private String colorCode;
    /**
     * 大区Id，支持多选（格式为：1,2,3），暂为属性字段，可不填
     */
    @ApiModelProperty(value = "大区Id，支持多选（格式为：1,2,3），暂为属性字段，可不填")
    @Column(name = "fk_area_region_id")
    private String fkAreaRegionId;
}