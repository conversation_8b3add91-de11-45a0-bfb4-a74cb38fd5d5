package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/5/18 10:59
 * @verison: 1.0
 * @description:
 */
@Data
public class BoothVo {

    /**
     * 状态：0新建/1已确认/2已收款
     */
    @ApiModelProperty(value = "状态：0新建/1已确认/2已收款")
    private Integer status;

    /**
     * 展位信息列表数据
     */
    @ApiModelProperty(value = "展位信息列表数据")
    private List<ConventionRegistrationVo> conventionRegistrationDtos;

    /**
     * 总计费用
     */
    @ApiModelProperty(value = "总计费用")
    private BigDecimal sumRegistrationFee;

    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String currencyName;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;


    @ApiModelProperty(value = "状态名称")
    private String statusName;
}
