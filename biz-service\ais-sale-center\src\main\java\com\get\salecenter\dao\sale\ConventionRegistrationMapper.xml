<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ConventionRegistrationMapper">
    <insert id="insert" parameterType="com.get.salecenter.entity.ConventionRegistration" keyProperty="id" useGeneratedKeys="true">
        insert into m_convention_registration (id, fk_convention_id, fk_institution_provider_id,
                                               fk_event_cost_id, fk_currency_type_num, registration_fee,
                                               registration_fee_cny, summary_fee, provider_name,
                                               booth_num, booth_name, contact_person_name,
                                               contact_person_name_chn, contact_email, contact_tel,
                                               express_info, receipt_code, status,
                                               gmt_create, gmt_create_user, gmt_modified,
                                               gmt_modified_user)
        values (#{id,jdbcType=BIGINT}, #{fkConventionId,jdbcType=BIGINT}, #{fkInstitutionProviderId,jdbcType=BIGINT},
                #{fkEventCostId,jdbcType=BIGINT}, #{fkCurrencyTypeNum,jdbcType=VARCHAR}, #{registrationFee,jdbcType=DECIMAL},
                #{registrationFeeCny,jdbcType=DECIMAL}, #{summaryFee,jdbcType=VARCHAR}, #{providerName,jdbcType=VARCHAR},
                #{boothNum,jdbcType=VARCHAR}, #{boothName,jdbcType=VARCHAR}, #{contactPersonName,jdbcType=VARCHAR},
                #{contactPersonNameChn,jdbcType=VARCHAR}, #{contactEmail,jdbcType=VARCHAR}, #{contactTel,jdbcType=VARCHAR},
                #{expressInfo,jdbcType=VARCHAR}, #{receiptCode,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
                #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP},
                #{gmtModifiedUser,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.get.salecenter.entity.ConventionRegistration" keyProperty="id" useGeneratedKeys="true">
        insert into m_convention_registration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkConventionId != null">
                fk_convention_id,
            </if>
            <if test="fkInstitutionProviderId != null">
                fk_institution_provider_id,
            </if>
            <if test="fkEventCostId != null">
                fk_event_cost_id,
            </if>
            <if test="fkCurrencyTypeNum != null">
                fk_currency_type_num,
            </if>
            <if test="registrationFee != null">
                registration_fee,
            </if>
            <if test="registrationFeeCny != null">
                registration_fee_cny,
            </if>
            <if test="summaryFee != null">
                summary_fee,
            </if>
            <if test="providerName != null">
                provider_name,
            </if>
            <if test="boothNum != null">
                booth_num,
            </if>
            <if test="boothName != null">
                booth_name,
            </if>
            <if test="contactPersonName != null">
                contact_person_name,
            </if>
            <if test="contactPersonNameChn != null">
                contact_person_name_chn,
            </if>
            <if test="contactEmail != null">
                contact_email,
            </if>
            <if test="contactTel != null">
                contact_tel,
            </if>
            <if test="expressInfo != null">
                express_info,
            </if>
            <if test="receiptCode != null">
                receipt_code,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkConventionId != null">
                #{fkConventionId,jdbcType=BIGINT},
            </if>
            <if test="fkInstitutionProviderId != null">
                #{fkInstitutionProviderId,jdbcType=BIGINT},
            </if>
            <if test="fkEventCostId != null">
                #{fkEventCostId,jdbcType=BIGINT},
            </if>
            <if test="fkCurrencyTypeNum != null">
                #{fkCurrencyTypeNum,jdbcType=VARCHAR},
            </if>
            <if test="registrationFee != null">
                #{registrationFee,jdbcType=DECIMAL},
            </if>
            <if test="registrationFeeCny != null">
                #{registrationFeeCny,jdbcType=DECIMAL},
            </if>
            <if test="summaryFee != null">
                #{summaryFee,jdbcType=VARCHAR},
            </if>
            <if test="providerName != null">
                #{providerName,jdbcType=VARCHAR},
            </if>
            <if test="boothNum != null">
                #{boothNum,jdbcType=VARCHAR},
            </if>
            <if test="boothName != null">
                #{boothName,jdbcType=VARCHAR},
            </if>
            <if test="contactPersonName != null">
                #{contactPersonName,jdbcType=VARCHAR},
            </if>
            <if test="contactPersonNameChn != null">
                #{contactPersonNameChn,jdbcType=VARCHAR},
            </if>
            <if test="contactEmail != null">
                #{contactEmail,jdbcType=VARCHAR},
            </if>
            <if test="contactTel != null">
                #{contactTel,jdbcType=VARCHAR},
            </if>
            <if test="expressInfo != null">
                #{expressInfo,jdbcType=VARCHAR},
            </if>
            <if test="receiptCode != null">
                #{receiptCode,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateStatus">
        update m_convention_registration set status=#{status} where id=#{id}
    </update>

    <select id="getBoothNameById" parameterType="java.lang.Long" resultType="string">
     select
      booth_name
     from
      m_convention_registration
     where
      id = #{id}
    </select>

    <select id="getConventionRegistrationDtoList" parameterType="java.lang.Long"
            resultType="com.get.salecenter.vo.ConventionRegistrationVo">
     select
      id,booth_name
     from
      m_convention_registration
     where
	  fk_convention_id =#{conventionId}
	 ORDER BY
	  CONVERT(booth_name USING GBK) asc;

    </select>

    <select id="getRegistrationIdsByName" parameterType="java.lang.String" resultType="java.lang.Long">
      select
       id
      from
       m_convention_registration
      where
       booth_name
      like
       #{boothName}

    </select>

    <select id="conventionRegistrationIsEmpty" parameterType="java.lang.Long" resultType="java.lang.Boolean">
     select IFNULL(max(id),0) id from m_convention_registration where fk_convention_id = #{id} LIMIT 1
    </select>

    <select id="validateAdd" resultType="java.lang.Integer">
        SELECT count(id) sums  FROM m_convention_registration where fk_convention_id =#{conventionId} and booth_num=#{boothNum}
    </select>

    <select id="getReceiptCodes" resultType="com.get.salecenter.vo.ConventionRegistrationVo">
        SELECT receipt_code,provider_name FROM m_convention_registration WHERE 1=1 and  fk_convention_id = #{conventionRegistrationDto.fkConventionId}
        and receipt_code is not null
        <if test="conventionRegistrationDto.status !='' and conventionRegistrationDto.status != null or conventionRegistrationDto.status == 0">
            and status = #{conventionRegistrationDto.status}
        </if>
        <if test=" conventionRegistrationDto.institutionProviderIds != null and conventionRegistrationDto.institutionProviderIds.size()>0">
            and fk_institution_provider_id in
        <foreach collection="conventionRegistrationDto.institutionProviderIds" item="institutionProviderId" index="index" open="(" separator="," close=")">
            #{institutionProviderId}
        </foreach>
        </if>
        <if test=" conventionRegistrationDto.contactPersonName != null and conventionRegistrationDto.contactPersonName != ''">
            and position(#{conventionRegistrationDto.contactPersonName} in contact_person_name)
        </if>
        <if test=" conventionRegistrationDto.boothNum != null and conventionRegistrationDto.boothNum != ''">
            and position(#{conventionRegistrationDto.boothNum} in booth_num)
        </if>
        <if test=" conventionRegistrationDto.receiptCode != null and conventionRegistrationDto.receiptCode != ''">
            and position(#{conventionRegistrationDto.receiptCode} in receipt_code)
        </if>
        <if test=" conventionRegistrationDto.providerName != null and conventionRegistrationDto.providerName != ''">
            and  position(#{conventionRegistrationDto.providerName} in provider_name)
        </if>

        GROUP BY receipt_code,provider_name
    </select>


    <select id="getBoothsByReceiptCode" resultType="com.get.salecenter.vo.ConventionRegistrationVo">
        SELECT * FROM m_convention_registration where receipt_code= #{receiptCode} and fk_convention_id =#{conventionId} and status=#{status}
    </select>
    <select id="providerNameVerify" resultType="com.get.salecenter.vo.ConventionRegistrationVo">
        SELECT
            *
        FROM
            m_convention_registration
        WHERE
            fk_convention_id = #{conventionId}
          AND provider_name = #{providerName}
    </select>
    <select id="getSumRegistrationFreeByReceiptCode" resultType="com.get.salecenter.entity.ConventionRegistration">
        SELECT
            *
        FROM
            m_convention_registration
        WHERE
            receipt_code = #{receiptCode}
          AND status = #{status}
    </select>
    <select id="getBoothListByReceiptCode" resultType="com.get.salecenter.vo.ConventionRegistrationVo">
        SELECT * FROM m_convention_registration where receipt_code= #{receiptCode} and fk_convention_id =#{conventionId}
    </select>
    <select id="getSumRegistrationFreeListByReceiptCode"  resultType="com.get.salecenter.entity.ConventionRegistration">
        SELECT
            *
        FROM
            m_convention_registration
        WHERE
            receipt_code = #{receiptCode}
    </select>
    <select id="getConventionIdByReceiptCode" resultType="java.lang.Long">
        SELECT fk_convention_id FROM `m_convention_registration` where receipt_code = #{receiptCode} GROUP BY fk_convention_id LIMIT 1
    </select>
    <select id="getInstitutionNameSelect" resultType="com.get.salecenter.vo.ConventionRegistrationVo">
        SELECT * FROM `m_convention_registration` where receipt_code = #{receiptCode} AND fk_convention_id = #{fkConventionId}
    </select>
    <select id="getConventionRegistrationByReceiptCode"
            resultType="com.get.salecenter.entity.ConventionRegistration">
        SELECT * FROM `m_convention_registration` where receipt_code = #{receiptCode}
    </select>
    <select id="getReceiptCodesByConventionId" resultType="java.lang.String">
        SELECT receipt_code FROM `m_convention_registration` WHERE fk_convention_id = #{conventionId} GROUP BY receipt_code
    </select>
    <select id="getConventionRegistrationsByConventionId"
            resultType="com.get.salecenter.vo.ConventionRegistrationVo">
        SELECT
            cr.id,
            cr.provider_name,
            cpr.fk_convention_person_id
        FROM
            r_convention_person_registration AS cpr
                INNER JOIN
            m_convention_person AS cp
            ON
                cpr.fk_convention_person_id = cp.id
                INNER JOIN
            m_convention_registration AS cr
            ON
                cpr.fk_convention_registration_id = cr.id
        WHERE
            cp.fk_convention_id = #{conventionId}
    </select>
    <select id="getPlaceCount" resultType="java.lang.Integer">
        SELECT
            COUNT(*)
        FROM
            `m_convention_registration`
        where FIND_IN_SET(#{placeName},REPLACE(summary_fee,";",",")) and fk_convention_id = #{conventionId}
    </select>

    <select id="getConventionRegistrationIdByProviderName" resultType="java.lang.Long">
        SELECT mcr.id FROM ais_sale_center.m_convention_registration AS mcr
        INNER JOIN ais_institution_center.m_institution_provider AS mip ON mip.id = mcr.fk_institution_provider_id
        WHERE mip.name like concat('%',#{providerName},'%') OR mip.name_chn like concat('%',#{providerName},'%')
        AND mcr.fk_convention_id = #{fkConventionId}
        GROUP BY mcr.id
    </select>

</mapper>