package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.persistence.Column;
import javax.validation.constraints.NotNull;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/6/8 17:19
 */
@Data
public class InstitutionViewOrderDto extends BaseVoEntity {
    /**
     * 排序方式枚举：学校资讯小程序=0
     */
    @ApiModelProperty(value = "排序方式枚举：学校资讯小程序=0")
    private Integer type;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "国家id")
    private Long fkCountryId;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty("学校名称")
    private String institutionName;
}
