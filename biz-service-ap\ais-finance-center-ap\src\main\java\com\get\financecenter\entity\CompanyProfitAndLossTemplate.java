package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 公司损益表项模板
 */
@Data
@TableName("m_company_profit_and_loss_template")
public class CompanyProfitAndLossTemplate extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目名称")
    private String title;

    @ApiModelProperty(value = "显示模式：Code/Expand/Sum")
    private String showMode;

    @ApiModelProperty(value = "科目Id")
    private Long fkAccountingItemId;

    @ApiModelProperty(value = "加减方向：1/-1")
    private Integer directionValue;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
}

