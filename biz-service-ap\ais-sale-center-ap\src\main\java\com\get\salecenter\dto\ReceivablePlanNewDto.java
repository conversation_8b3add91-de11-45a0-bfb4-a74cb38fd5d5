package com.get.salecenter.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.permissioncenter.dto.ConfigRemindDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/11/19
 * @TIME: 15:54
 * @Description:
 **/
@Data
public class ReceivablePlanNewDto extends BaseVoEntity{
    /**
     * 应收类型关键字，枚举，如：m_student_offer_item
     */
//    @NotNull(message = "应收类型不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "应收计划类型")
    private String fkTypeKey;

    /**
     * 国家Id
     */
    @ApiModelProperty(value="国家Id")
    private Long fkAreaCountryIds;

    /**
     * 学生名称
     */
    @ApiModelProperty(value = "学生信息（中英名称/生日）")
    private String studentName;

    @ApiModelProperty(value = "渠道信息（渠道/学校提供商）")
    private String bzoName;

    @ApiModelProperty(value = "业务信息（学校/课程/保险/住宿）")
    private String bziName;

    @ApiModelProperty(value = "业务信息补充（学校/课程）")
    private String bziNameSupplement;

    @ApiModelProperty(value = "发票编号")
    private String fkInvoiceNums;

    @ApiModelProperty(value = "收齐状态：0未收/1部分已收/2已收齐")
    private Integer receiveStatus;

    @ApiModelProperty("创建时间(开始)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty("创建时间(结束)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;
    /**
     * 公司ids
     */
    @ApiModelProperty(value = "公司ids")
    private List<Long> fkCompanyIds;

    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;

    /**
     * 其他金额类型：0=奖励金额/1=Incentive奖励/2=保险金额/3=活动费用/4=其他收入
     */
    @ApiModelProperty(value = "其他金额类型：0=奖励金额/1=Incentive奖励/2=保险金额/3=活动费用/4=其他收入")
    private Integer bonusType;

    /**
     * 其他金额类型：0=奖励金额/1=Incentive奖励/2=保险金额/3=活动费用/4=其他收入
     */
//    @ApiModelProperty(value = "其他金额类型：0=奖励金额/1=Incentive奖励/2=保险金额/3=活动费用/4=其他收入")
//    private String bonusTypeName;

//    @ApiModelProperty(value = "学生Id")
//    private Long fkStudentId;
    @ApiModelProperty(value = "发票Id")
    private Long fkInvoiceId;

    @ApiModelProperty(value = "是否为发票详情调用flag true:发票调用保证实时性  false：非发票调用 使用数据仓库")
    private Boolean isInvoiceFlag = false;

    @ApiModelProperty(value = "实收金额")
    private BigDecimal receiptAmount;

    @ApiModelProperty(value = "1：90-120天 ;2：120-180天；3：180天以上")
    private Integer uncollectedType;

    @ApiModelProperty("计划收款开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date receivablePlanBeginDate;

    @ApiModelProperty("计划收款结算时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date receivablePlanEndDate;

    //=========path=========
    @ApiModelProperty(value = "提醒时间vo")
    private ConfigRemindDto configRemindVo;
    private ConfigRemindDto configRemindDto;


    @ApiModelProperty(value = "是否绑定发票false否/true是")
    private Boolean isBindingInvoice;

    @ApiModelProperty(value = "课程等级")
    private Long majorLevelId;

    @ApiModelProperty("应收应付学费flag")
    private Boolean aprpTuitionFlag;

    @ApiModelProperty(value = "导出ids")
    private List<Long> exportIds;


    /**
     * 课程长度类型(0周、1学期、2年)
     */
    @ApiModelProperty(value = "课程长度类型(0周、1学期、2年、3学期)")
    private Integer durationType;

    /**
     * 课程长度
     */
    @ApiModelProperty(value = "课程长度")
    private BigDecimal duration;

    @ApiModelProperty(value = "学生佣金结算标记关键字")
    private String commissionMark;


    //=========path=========
    /**
     * 学生id
     */
    @ApiModelProperty(value = "学生id")
    private Long fkStudentId;

   
}
