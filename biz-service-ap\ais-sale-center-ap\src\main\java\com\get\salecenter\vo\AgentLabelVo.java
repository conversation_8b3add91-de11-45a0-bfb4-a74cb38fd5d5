package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
//@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AgentLabelVo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "代理标记关系Id")
    private Long id;

    @ApiModelProperty(value = "代理Id")
    private Long fkAgentId;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "代理电邮地址")
    private String labelEmail;

    @ApiModelProperty(value = "标签Id")
    private Long fkLabelId;

    @ApiModelProperty(value = "标签类型")
    private String labelType;

    @ApiModelProperty(value = "标签类型ID")
    private Long labelTypeId;

    @ApiModelProperty(value = "标签名称，各种语言，独立起对应的标签，对应不同公司区域关系")
    private String labelName;

    @ApiModelProperty(value = "标签关键字")
    private String labelKey;

    @ApiModelProperty(value = "标签关键字")
    private String iconName;

    @ApiModelProperty(value = "标签颜色")
    private String color;

    @ApiModelProperty(value = "描述")
    private String labelRemark;

}
