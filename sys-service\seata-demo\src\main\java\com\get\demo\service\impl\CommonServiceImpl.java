package com.get.demo.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.api.Result;
import com.get.demo.entity.User;
import com.get.demo.service.ICommonService;
import com.get.demo.service.IGoodsService;
import com.get.demo.service.IOrderService;
import com.get.demo.service.IUserService;
import com.get.helpcenter.feign.IHelpCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-07
 */
@Service
@Slf4j
public class CommonServiceImpl implements ICommonService {
    @Resource
    private IGoodsService goodsService;
    @Resource
    private IOrderService orderService;
    @Resource
    private IUserService userService;
    @Resource
    private IHelpCenterClient helpCenterClient;

    @Override
    @DSTransactional//跨多数据源事务专用
    public Boolean updateDemo() {
        this.goodsService.updateByDemo();
        this.orderService.updateByDemo();
        this.userService.updateByDemo();
        return true;
    }

    @Override
//    @GlobalTransactional(rollbackFor = Exception.class)//需要根据feign的执行结果来主动抛出异常，没有异常抛出不回滚；本微服务内任何异常都会回滚
//    @Transactional(rollbackFor = Exception.class)//这里可以不加
    public Boolean updateFeignDemo() {
        this.userService.updateByDemo();
        Result<Boolean> result = helpCenterClient.updateByDemo();
        log.info("====>feign返回的结果：" + result.isSuccess());
        //如没有处理feign的返回结果，则seata不会回滚，会认为是成功的。
        if (!result.isSuccess()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("feign_execution_failed"));
        }
        return true;
    }

    @Override
    public Boolean testRedisLock(User user) {
        log.info("=====测试分布式锁======》开始！" + user.getId());
        try {
            Thread.sleep(15000);
        } catch (Exception e) {
            throw new GetServiceException(e.getMessage());
        }
        log.info("=====测试分布式锁======》结束！" + user.getId());
        return true;
    }
}
