<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.oldissue.AgencyCompanyHasAgencyUsersMapper">

    <select id="getAllCppAgencyId" resultType="java.lang.Long">
        SELECT cpp_agency_id FROM agency_company_has_agency_users WHERE cpp_agency_id IS NOT NULL
        <if test="idGeas != null and idGeas.size()>0">
            AND cpp_agency_id IN
            <foreach collection="idGeas" item="idGea" open="(" separator="," close=")">
                #{idGea}
            </foreach>
        </if>
    </select>

    <select id="getAllBmsAgencyId" resultType="java.lang.Long">
        SELECT bms_agency_id FROM agency_company_has_agency_users WHERE bms_agency_id IS NOT NULL
        <if test="ids != null and ids.size()>0">
            AND bms_agency_id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>