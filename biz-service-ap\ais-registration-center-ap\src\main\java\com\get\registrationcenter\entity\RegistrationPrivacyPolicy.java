package com.get.registrationcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("m_privacy_policy")
public class RegistrationPrivacyPolicy extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;
    /**
     * 政策内容
     */
    @ApiModelProperty(value = "政策内容")
    private String policyContent;
}