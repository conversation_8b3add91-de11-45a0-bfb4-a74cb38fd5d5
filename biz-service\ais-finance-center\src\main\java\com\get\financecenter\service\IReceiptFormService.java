package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.financecenter.dto.*;
import com.get.financecenter.vo.*;
import com.get.financecenter.entity.ReceiptForm;
import com.get.financecenter.dto.query.ReceiptFormQueryDto;


import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/12/22
 * @TIME: 16:49
 * @Description:
 **/
public interface IReceiptFormService extends BaseService<ReceiptForm> {
    /**
     * 列表数据
     *
     * @param receiptFormVo
     * @param page
     * @return
     */
    List<ReceiptFormVo> getReceiptForms(ReceiptFormQueryDto receiptFormVo, Page page);


    /**
     * @Description: 导出收款大单列表
     * @Author: Jerry
     * @Date:16:14 2021/12/20
     */
    void exportReceiptFormExcel(HttpServletResponse response, ReceiptFormQueryDto receiptFormVo);


    /**
     * 修改收款单结算状态
     * @param receiptFormId
     * @return
     */
    SaveResponseBo updateSettlementStatus(Long receiptFormId);

    /**
     * 保存
     *
     * @param receiptFormDto
     * @return
     */
    Long addReceiptForm(ReceiptFormDto receiptFormDto);

    /**
     * 获取实收汇率
     * @return
     */
    ResponseBo<BigDecimal> obtainThePaidInExchangeRate(ReceiptFormExchangeRateDto receiptFormExchangeRateDto);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    ReceiptFormVo findReceiptFormById(Long id);

    /**
     * 删除
     *
     * @param id
     */
    void updateStatus(Long id);

    /**
     * 修改
     *
     * @param receiptFormDto
     * @return
     */
    ReceiptFormVo updateReceiptForm(ReceiptFormDto receiptFormDto);

    /**
     * 修改付款单中的部分信息
     *
     * @param receiptFormPortionVo 修改参数
     * @return
     */
    Long updatePortionReceiptForm(ReceiptFormPortionDto receiptFormPortionVo);

    /**
     * @return java.util.List<com.get.salecenter.dto.InvoiceDto>
     * @Description :列表
     * @Param [invoiceVo, page]
     * <AUTHOR>
     */
    List<InvoiceVo> getInvoices(ReceiptFormDto receiptFormDto, Page page);

    /**
     * @return java.lang.String
     * @Description: 根据收款单id获取货币类型
     * @Param [formId]
     * <AUTHOR>
     */
    String getCurrencyByFormId(Long formId);


    /**
     * @return java.lang.Long
     * @Description: 根据收款单id获取收款单金额
     * @Param [formId]
     * <AUTHOR>
     */
    BigDecimal getAmountByFormId(Long formId);

    /**
     * @return java.util.List<com.get.financecenter.vo.MediaAndAttachedDto>
     * @Description: 添加附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    List<FMediaAndAttachedVo> addMedia(List<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * @return java.util.List<com.get.financecenter.vo.MediaAndAttachedDto>
     * @Description: 获取附件
     * @Param [attachedVo, page]
     * <AUTHOR>
     */
    List<FMediaAndAttachedVo> getMedia(MediaAndAttachedDto attachedVo, Page page);

    /**
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Description: 下拉
     * @Param []
     * <AUTHOR>
     */
    List<Map<String, Object>> findTypeKeySelect();

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 目标对象下拉
     * @Param [tableName, companyId]
     * <AUTHOR>
     */
    List<BaseSelectEntity> findTypeTargetSelect(String tableName, Long companyId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据vo获取付款单ids
     * @Param [fkCompanyId]
     * <AUTHOR>
     */
    List<Long> getFormByCompanyId(Long fkCompanyId);

    /**
     * @return java.lang.Long
     * @Description: 增加或者更新评论
     * @Param [commentVo]
     * <AUTHOR>
     **/
    Long editComment(CommentDto commentDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.CommentVo>
     * @Description: 获取所有评论
     * @Param [commentVo, page]
     * <AUTHOR>
     */
    List<FCommentVo> getComments(CommentDto commentDto, Page page);

    /**
     * @return java.util.List<com.get.financecenter.vo.ReceiptFormVo>
     * @Description: 获取收款单列表
     * @Param [receiptFormItemId]
     * <AUTHOR>
     */
    List<ReceiptFormVo> getReceiptFormList(Long planId);

    /**
     * feign根据应收计划ids获取所绑定的收款单子项
     *
     * @Date 16:13 2021/12/2
     * <AUTHOR>
     */
    List<ReceiptFormVo> getReceiptFormListFeignByPlanIds(Set<Long> planIds);


    /**
     * 获取收款单
     *
     * @param id
     * @return
     */
    ReceiptFormVo getReceiptFormByFormId(Long id);

    /**
     * 留学服务费，批量创建收款单，涉及表：
     * m_receipt_form、m_receipt_form_item、r_receipt_form_invoice、r_invoice_receivable_plan
     *
     * @param serviceFeeReceiptFormDto
     */
    Boolean saveBatchReceiptForms(ServiceFeeReceiptFormDto serviceFeeReceiptFormDto);

    /**
     * 根据目标类型关键字和目标id集合获取收款单
     *
     * @param fkTypeKey       目标类型关键字，枚举：m_institution_provider学校供应商/m_student学生
     * @param fkTypeTargetIds 目标id集合
     * @return key：目标id，value：收款单集合
     */
    Map<Long, List<ReceiptFormVo>> getReceiptFormsByTargetIds(String fkTypeKey, Set<Long> fkTypeTargetIds);

    /**
     * 根据服务费id获取对应的收款单信息
     * @param feeIds
     * @return
     */
    Map<Long, List<ReceiptFormVo>> getReceiptFormsByTargetIds(List<Long> feeIds);

    List<ReceiptFormVo> findReceiptFormAll();
}
