package com.get.remindercenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/26 14:10
 * @desciption:
 */
@Data
@TableName("m_invalid_email")
public class InvalidEmail extends BaseEntity {
    @ApiModelProperty(value = "无效邮箱地址")
    private String email;

    @ApiModelProperty(value = "备注")
    private String remark;

}
