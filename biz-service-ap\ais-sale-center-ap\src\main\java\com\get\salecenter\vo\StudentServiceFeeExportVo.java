package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2024/1/9 14:39
 * @verison: 1.0
 * @description:
 */
@Data
public class StudentServiceFeeExportVo {

    @ApiModelProperty("所属公司")
    private String companyName;

    @ApiModelProperty("业务状态")
    private String businessStatusName;

    @ApiModelProperty("结算审批状态")
    private String approveStatusName;

    @ApiModelProperty("审批时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date approveTime;

    @ApiModelProperty("销售时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String salesTimeStr;

    @ApiModelProperty("结算状态")
    private String settlementStatusName;

    @ApiModelProperty("应收币种")
    private String fkReceivableCurrencyNum;

    @ApiModelProperty("应收金额")
    private BigDecimal receivableAmount;

    @ApiModelProperty("实收金额")
    private BigDecimal receiptAmount;

    @ApiModelProperty("应付币种")
    private String fkPayableCurrencyNum;

    @ApiModelProperty("应付金额")
    private BigDecimal payableAmount;

    @ApiModelProperty("应付比例 (%)")
    private BigDecimal commissionRate;

    @ApiModelProperty("实付金额")
    private BigDecimal paidAmount;

    @ApiModelProperty("应付未付")
    private BigDecimal payableDiffAmount;

    @ApiModelProperty("收款状态")
    private String collectionStatus;

    @ApiModelProperty("收款日期")
    private String receiptDate;

    @ApiModelProperty("付款状态")
    private String paymentStatus;

    @ApiModelProperty("付款情况")
    private String payDetail;

    @ApiModelProperty("学生名称")
    private String studentName;

    @ApiModelProperty("服务费编号")
    private String serviceFeeNum;

    @ApiModelProperty("代理名称")
    private String fkAgentName;

    @ApiModelProperty("负责BD")
    private String bdName;

    @ApiModelProperty("业务国家/地区")
    private String countryNames;

    @ApiModelProperty("收款方")
    private String payeeName;

    @ApiModelProperty("服务费类型")
    private String serviceFeeTypeName;

    @ApiModelProperty("服务费币种")
    private String serviceFeeCurrencyName;

    /**
     * amount+IFNULL(taxes,0)
     */
    @ApiModelProperty("服务费金额")
    private BigDecimal serviceFeeAmount;

    /**
     * amount字段
     */
    @ApiModelProperty("税前")
    private BigDecimal pretaxAmount;

    /**
     * taxes字段
     */
    @ApiModelProperty("税金")
    private BigDecimal serviceFeeTaxes;

    @ApiModelProperty("业务发生时间")
    private String businessTimeStr;

    @ApiModelProperty("成本支出描述")
    private String studentServiceFeeCosts;

    @ApiModelProperty("成本币种")
    private String feeCostCurrencyTypeNumName;

    /**
     * amount + IFNULL(taxes,0)
     */
    @ApiModelProperty("成本总金额")
    private BigDecimal feeCostAmount;

    /**
     * amount字段
     */
    @ApiModelProperty("成本税前")
    private BigDecimal feeCostPretaxAmount;

    /**
     * taxes字段
     */
    @ApiModelProperty("成本税金")
    private BigDecimal feeCostTaxes;

    @ApiModelProperty("备注")
    private String remark;

    /**
     * 项目成员列表，该属性不需要导出，就不需要加上@ApiModelProperty注解
     */
    private List<StudentProjectRoleStaffVo> projectRoleStaffDtos;

    @ApiModelProperty("绑定项目成员")
    private String projectRoleStaffInfo;

    @ApiModelProperty("状态")
    private String serviceFeeStatus;

    @ApiModelProperty("创建人")
    private String gmtCreateUser;

    @ApiModelProperty("创建时间")
    private Object gmtCreate;

    @ApiModelProperty("员工+AN1奖金计数")
    private BigDecimal an1;

    @ApiModelProperty("比率")
    private Object royaltyRate;

    @ApiModelProperty("奖金金额")
    private BigDecimal royaltyAmount;

    @ApiModelProperty("币种")
    private String settlementCurrencyTypeNumName;

    @ApiModelProperty("当日汇率（服务费）")
    private BigDecimal curRateServiceFee;

    @ApiModelProperty("当日汇率（成本）")
    private BigDecimal curRateServiceFeeCost;

    @ApiModelProperty(value = "代理标签")
    private String agentLabelNames;

}
