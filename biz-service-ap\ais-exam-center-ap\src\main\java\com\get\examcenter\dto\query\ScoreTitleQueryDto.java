package com.get.examcenter.dto.query;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ScoreTitleQueryDto extends BaseVoEntity {
    /**
     * 称号名称
     */
    @ApiModelProperty(value = "称号名称")
    @NotBlank(message = "称号名称不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private String title;

    /**
     * 考试Id
     */
    @ApiModelProperty(value = "考试Id")
    private Long fkExaminationId;

    /**
     * 考试Id
     */
    @ApiModelProperty(value = "所属公司Id")
    private Long fkCompanyId;

}
