package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @DATE: 2020/11/5
 * @TIME: 15:40
 * @Description:
 **/
@Data
public class CommentVo extends BaseEntity {
    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;

    //=============实体类Comment=================
    private static final long serialVersionUID = 1L;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    @Column(name = "fk_table_name")
    private String fkTableName;
    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    @Column(name = "fk_table_id")
    private Long fkTableId;
    /**
     * 评论
     */
    @ApiModelProperty(value = "评论")
    @Column(name = "comment")
    private String comment;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fkTableName=").append(fkTableName);
        sb.append(", fkTableId=").append(fkTableId);
        sb.append(", comment=").append(comment);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
