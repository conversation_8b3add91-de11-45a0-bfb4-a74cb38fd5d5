package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;

/**
 * @author: Hardy
 * @create: 2023/2/6 10:09
 * @verison: 1.0
 * @description:
 */
@Data
public class StaffCommissionStepDto extends BaseVoEntity {
    /**
     * 公司Id
     */
    @NotNull(message = "公司Id", groups = {Add.class,Update.class})
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    /**
     * 学生申请方案项目状态步骤Ids，多个用英文逗号隔开（1,2,3），操作过可计入。
     */
    @ApiModelProperty(value = "学生申请方案项目状态步骤Ids，多个用英文逗号隔开（1,2,3），操作过可计入。")
    @Column(name = "fk_student_offer_item_step_ids")
    private String fkStudentOfferItemStepIds;

    /**
     * 提成申请步骤名称
     */
    @ApiModelProperty(value = "提成申请步骤名称")
    @Column(name = "step_name")
    private String stepName;

    /**
     * 提成申请步骤Key
     */
    @NotNull(message = "提成申请步骤Key", groups = {Add.class,Update.class})
    @ApiModelProperty(value = "提成申请步骤Key")
    @Column(name = "step_key")
    private String stepKey;

    /**
     * 提成申请步骤排序，由0开始按顺序排列
     */
    @ApiModelProperty(value = "提成申请步骤排序，由0开始按顺序排列")
    @Column(name = "step_order")
    private Integer stepOrder;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    /**
     * 特殊步骤模式，0普通/1特殊（设置定额后，只要勾选就增加提成金额）
     */
    @NotNull(message = "步骤模式", groups = {Add.class,Update.class})
    @ApiModelProperty(value = "特殊步骤模式，0普通/1特殊（设置定额后，只要勾选就增加提成金额）")
    @Column(name = "special_mode")
    private Integer specialMode;

    private static final long serialVersionUID = 1L;


}
