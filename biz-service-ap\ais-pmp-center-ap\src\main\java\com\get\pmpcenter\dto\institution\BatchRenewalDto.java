package com.get.pmpcenter.dto.institution;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/8/22
 * @Version 1.0
 * @apiNote:批量续约
 */
@Data
public class BatchRenewalDto {

    @ApiModelProperty(value = "佣金方案ID")
    @NotNull(message = "佣金方案ID不能为空")
    private List<Long> planIds;

    @ApiModelProperty(value = "是否续约:0否1是")
    @NotNull(message = "是否续约不能为空")
    private Integer isRenewal;
}
