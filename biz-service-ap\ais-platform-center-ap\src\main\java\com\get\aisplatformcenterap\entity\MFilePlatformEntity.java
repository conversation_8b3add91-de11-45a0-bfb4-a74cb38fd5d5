package com.get.aisplatformcenterap.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-03-03 18:34:58
 */

@Data
@TableName("m_file_platform") 
public class MFilePlatformEntity extends BaseEntity implements Serializable {

  @ApiModelProperty("文件Id")
  private Long id;
 

  @ApiModelProperty("文件guid")
  private String fileGuid;
 

  @ApiModelProperty("源文件类型")
  private String fileTypeOrc;
 

  @ApiModelProperty("源文件名")
  private String fileNameOrc;
 

  @ApiModelProperty("目标文件名")
  private String fileName;
 

  @ApiModelProperty("目标文件路径")
  private String filePath;
 

  @ApiModelProperty("文件外部存储Key（如：腾讯云COS）")
  private String fileKey;
 

}
