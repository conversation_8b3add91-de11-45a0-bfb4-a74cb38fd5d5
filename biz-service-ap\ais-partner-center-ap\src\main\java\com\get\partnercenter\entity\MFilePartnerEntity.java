package com.get.partnercenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-01-06 14:41:29
 */

@Data
@TableName("m_file_partner")
public class MFilePartnerEntity extends BaseEntity {

  @ApiModelProperty("文件Id")
  private Long id;
 
 
  @ApiModelProperty("文件guid")
  private String fileGuid;
 

  @ApiModelProperty("源文件类型")
  private String fileTypeOrc;
 

  @ApiModelProperty("源文件名")
  private String fileNameOrc;
 

  @ApiModelProperty("目标文件名")
  private String fileName;
 

  @ApiModelProperty("目标文件路径")
  private String filePath;
 

  @ApiModelProperty("文件外部存储Key（如：腾讯云COS）")
  private String fileKey;

 

}
