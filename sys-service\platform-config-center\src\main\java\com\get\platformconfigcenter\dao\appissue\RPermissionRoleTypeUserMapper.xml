<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.platformconfigcenter.dao.appissue.RPermissionRoleTypeUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.get.platformconfigcenter.entity.IssueRPermissionRoleTypeUser">
        <result column="id" jdbcType="BIGINT" property="id" />
        <result column="fk_company_id" jdbcType="BIGINT" property="fkCompanyId" />
        <result column="fk_agent_id" jdbcType="BIGINT" property="fkAgentId" />
        <result column="fk_role_type_id" jdbcType="BIGINT" property="fkRoleTypeId" />
        <result column="fk_user_id" jdbcType="BIGINT" property="fkUserId" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
    </resultMap>
</mapper>
