package com.get.examcenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by <PERSON>.
 * Time: 9:43
 * Date: 2021/8/23
 * Description:考场管理列表Vo
 */
@Data
public class ExaminationPaperUpdateDto extends BaseVoEntity implements Serializable {

    /**
     * 考试Id
     */
    @ApiModelProperty(value = "考试Id")
    @NotNull(message = "考试Id不能为空", groups = {Add.class, Update.class})
    private Long fkExaminationId;

    /**
     * 考卷编号
     */
    @ApiModelProperty(value = "考卷编号")
    private String num;

    /**
     * 考卷名称
     */
    @ApiModelProperty(value = "考卷名称")
    @NotBlank(message = "考卷名称不能为空", groups = {Add.class, Update.class})
    private String name;

    /**
     * 开放时间
     */
    @ApiModelProperty(value = "开放时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 题目数量
     */
    @ApiModelProperty(value = "题目数量")
    private Integer questionCount;

    /**
     * 管理员认证，如：手机号。多个用逗号分隔
     */
    @ApiModelProperty(value = "管理员认证，如：手机号。多个用逗号分隔")
    private String adminAuthentication;

    /**
     * 是否允许重考：0否/1是
     */
    @ApiModelProperty(value = "是否允许重考：0否/1是")
    @NotNull(message = "是否允许重考不能为空", groups = {Add.class, Update.class})
    private Boolean isRetest;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @NotNull(message = "是否激活不能为空", groups = {Add.class, Update.class})
    private Boolean isActive;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;
    /**
     * 参数配置
     */
    @ApiModelProperty(value = "参数配置")
    private String paramJson;

    /**
     * 所属公司Id
     */
    @ApiModelProperty(value = "所属公司Id")
    private Long fkCompanyId;
}
