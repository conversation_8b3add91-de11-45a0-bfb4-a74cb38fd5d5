package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2020/11/18
 * @TIME: 12:38
 * @Description:
 **/
@Data
public class StudentOfferItemReceivablePlanDto extends BaseVoEntity {
    /**
     * 学习计划业务状态(多选)：0获得第一阶段佣金、1获得第二阶段佣金、2获得第三阶段佣金、3获得第四阶段佣金 / 4只读第一阶段、5只读第二阶段、6只读第三阶段、7只读第四阶段
     */
    @ApiModelProperty(value = "学习计划业务状态(多选)：0获得第一阶段佣金、1获得第二阶段佣金、2获得第三阶段佣金、3获得第四阶段佣金 / 4只读第一阶段、5只读第二阶段、6只读第三阶段、7只读第四阶段")
    private String conditionType;

    /**
     * 学费金额
     */
    @ApiModelProperty(value = "学费金额")
    @NotNull(message = "学费不能为空", groups = {Update.class})
    private BigDecimal tuitionAmount;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @NotBlank(message = "币种编号不能为空", groups = {Update.class})
    private String fkCurrencyTypeNum;

    /**
     * 学校Id(前置学校)
     */
    @ApiModelProperty(value = "学校Id(前置学校)")
    private Long fkPreInstitutionId;

    /**
     * 学校集团Id(前置集团)
     */
    @ApiModelProperty(value = "学校集团Id(前置集团)")
    private Long fkPreInstitutionGroupId;

    /**
     * 专业等级Id(前置等级)
     */
    @ApiModelProperty(value = "专业等级Id(前置等级)")
    private Long fkPreMajorLevelId;

    @ApiModelProperty(value = "是否同步学费到申请计划")
    private Boolean flag=false;

  
}
