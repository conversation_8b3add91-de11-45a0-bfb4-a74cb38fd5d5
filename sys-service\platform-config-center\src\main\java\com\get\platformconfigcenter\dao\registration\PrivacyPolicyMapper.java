package com.get.platformconfigcenter.dao.registration;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.PrivacyPolicy;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("registrationdb")
public interface PrivacyPolicyMapper extends BaseMapper<PrivacyPolicy> {

    int insert(PrivacyPolicy record);

    int insertSelective(PrivacyPolicy record);

    int updateByPrimaryKeySelective(PrivacyPolicy record);

    int updateByPrimaryKeyWithBLOBs(PrivacyPolicy record);

    int updateByPrimaryKey(PrivacyPolicy record);

    String getPrivacyPolicyTitleById(Long id);

}