package com.get.salecenter.service.impl;

import cn.hutool.core.lang.Validator;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.aisplatformcenterap.feign.IPlatformCenterClient;
import com.get.aisplatformcenterap.vo.LabelSearchAboutAgentVo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.AgentLabelMapper;
import com.get.salecenter.dto.AgentLabelDto;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.entity.AgentLabel;
import com.get.salecenter.service.AgentLabelService;
import com.get.salecenter.service.IAgentService;
import com.get.salecenter.vo.AgentLabelVo;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class AgentLabelServiceImpl implements AgentLabelService {
    @Resource
    private AgentLabelMapper agentLabelMapper;
    @Resource
    private UtilService utilService;
    @Resource
    @Lazy
    private IAgentService agentService;

    @Resource
    private IPlatformCenterClient platformCenterClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> addAgentLabel(AgentLabelDto agentLabelDto) {
        validateParams(agentLabelDto);
        checkExistingRalations(agentLabelDto);
        List<AgentLabel> agentLabels = buildAgentLabels(agentLabelDto);
        agentLabelMapper.insertBatchSomeColumn(agentLabels);
        List<Long> ids = agentLabels.stream().map(AgentLabel::getId).collect(Collectors.toList());
        return ids;

    }

    @Override
    public int deleteAgentLabel(Long id) {
        return agentLabelMapper.deleteById(id);
    }

    @Override
    public List<AgentLabelVo> dataList(AgentLabelDto agentLabelVo, Page page) {
        IPage<AgentLabelVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        //获取分页数据
        List<AgentLabelVo> agentLabels = agentLabelMapper.getAgentLabelList(iPage, agentLabelVo);
        page.setAll((int) iPage.getTotal());
        Set<Long> agentIds = agentLabels.stream().map(AgentLabelVo::getFkAgentId).collect(Collectors.toSet());
        //根据代理ids获取名称
        Map<Long, Agent> agentMaps = !GeneralTool.isEmpty(agentIds) ? agentService.getAgentsByIds(agentIds) : new HashMap<>();
        agentLabels.forEach(vo -> {
            Agent agent = agentMaps.get(vo.getFkAgentId());
            vo.setAgentName(agent != null ? agent.getName() : "");
        });
        return agentLabels;
    }

    @Override
    public List<AgentLabelVo> getAgentLabelListByAgentDto(Set<Long> agentIds, Set<String> labelEmails) {
        AgentLabelDto agentLabelDto = new AgentLabelDto();
        if (GeneralTool.isEmpty(agentIds) && GeneralTool.isEmpty(labelEmails)) {
            return new ArrayList<>();
        }
        if (GeneralTool.isNotEmpty(agentIds)) {
            ArrayList<Long> agentIdList = new ArrayList<>(agentIds);
            agentLabelDto.setFkAgentIds(agentIdList);
        }
        if (GeneralTool.isNotEmpty(labelEmails)) {
            agentLabelDto.setLabelEmails(new ArrayList<>(labelEmails));
        }
        return agentLabelMapper.getAgentLabelList(null, agentLabelDto);

    }

    @Override
    public ResponseBo getLabels(SearchBean<AgentLabelDto> page) {

        AgentLabelDto agentLabelDto = page.getData();
        if (GeneralTool.isEmpty(agentLabelDto.getFkAgentId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(LocaleMessageUtils.getMessage("id_is_null")));
        }
        Result<LabelSearchAboutAgentVo> labels = platformCenterClient.getLabelByLabelTypeIdAndKeyWord(page);
        if (!(labels.isSuccess()) && GeneralTool.isEmpty(labels.getData())) {
            return new ResponseBo<>(null);
        }
        LabelSearchAboutAgentVo data = labels.getData();
        return new ResponseBo<>(data.getLabelAboutAgentVoList(), data.getPage());
    }


    private void validateParams(AgentLabelDto agentLabelDto) {
        if (agentLabelDto == null) {
            log.info("创建代理标签关系失败: 请求对象为空");
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_label_request_null"));
        }

        if (agentLabelDto.getFkAgentId() == null) {
            log.info("创建代理标签关系失败: 学生代理ID为空 | dto: {}", agentLabelDto);
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_id_null"));
        }

        if (CollectionUtils.isEmpty(agentLabelDto.getFkLabelIds())) {
            log.info("创建代理标签关系失败: 标签ID列表为空 | dto: {}", agentLabelDto);
            throw new GetServiceException(LocaleMessageUtils.getMessage("label_list_null"));
        }
    }

    private void checkExistingRalations(AgentLabelDto agentLabelDto) {
        List<Long> labelIdList = agentLabelDto.getFkLabelIds();
        LambdaQueryWrapper<AgentLabel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AgentLabel::getFkAgentId, agentLabelDto.getFkAgentId())
                .in(AgentLabel::getFkLabelId, labelIdList);
        if (GeneralTool.isNotEmpty(agentLabelDto.getLabelEmail())) {
            if (Validator.isEmail(agentLabelDto.getLabelEmail().trim())){
                wrapper.eq(AgentLabel::getEmail, agentLabelDto.getLabelEmail().trim());
            }else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("email_format_error"));
            }
        }else {
            wrapper.eq(AgentLabel::getEmail,"");
        }
        List<AgentLabel> agentLabels = agentLabelMapper.selectList(wrapper);
        if (!agentLabels.isEmpty()) {
            Set<Long> conflictLabelIds = agentLabels.stream()
                    .map(AgentLabel::getFkLabelId)
                    .collect(Collectors.toSet());
            String errorMsg = String.format("代理标签关系已存在，冲突标签ID: %s", conflictLabelIds);
            log.error(errorMsg);
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_relation_exist"));
        }
    }

    private List<AgentLabel> buildAgentLabels(AgentLabelDto agentLabelDto) {
        return agentLabelDto.getFkLabelIds().stream()
                .map(labelId -> {
                    AgentLabel agentLabel = BeanCopyUtils.objClone(agentLabelDto, AgentLabel::new);
                    utilService.setCreateInfo(agentLabel);
                    agentLabel.setEmail(agentLabelDto.getLabelEmail().trim());
                    agentLabel.setFkLabelId(labelId);
                    return agentLabel;
                })
                .collect(Collectors.toList());
    }


}
