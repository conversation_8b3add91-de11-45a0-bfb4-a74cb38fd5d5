package com.get.institutioncenter.service;


import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.vo.AreaCityVo;
import com.get.institutioncenter.dto.AreaCityDto;
import com.get.institutioncenter.vo.MediaAndAttachedVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/7/28 10:52
 * @verison: 1.0
 * @description: 区域管理-城市配置接口
 */
public interface IAreaCityService {

    /**
     * 详情
     *
     * @param id
     * @return
     */
    AreaCityVo findAreaCityById(Long id);

    /**
     * 批量新增
     *
     * @param areaCityDtos
     * @return
     */
    void batchAdd(ValidList<AreaCityDto> areaCityDtos);


    /**
     * 删除
     *
     * @param id
     * @return
     */
    void delete(Long id);

    /**
     * 修改
     *
     * @param areaCityDto
     * @return
     */
    AreaCityVo updateAreaCity(AreaCityDto areaCityDto);


    /**
     * 列表
     *
     * @param areaCityDto
     * @param page
     * @return
     */
    List<AreaCityVo> getAreaCitys(AreaCityDto areaCityDto, Page page);

    /**
     * 查询州省下面的城市
     *
     * @param id
     * @return
     */
    List<AreaCityVo> getByFkAreaStateId(Long id);


    /**
     * @Description: 查询国家下面城市
     * @Author: Jerry
     * @Date:12:38 2021/9/10
     */
    List<AreaCityVo> getByFkAreaCountryId(Long fkAreaCountryId);

    /**
     * @return java.lang.String
     * @Description :通过城市id 查找对应的城市名称
     * @Param [id]
     * <AUTHOR>
     */
    String getCityNameById(Long id);

    /**
     * 通过城市id 查找对应的城市名称
     *
     * @param id
     * @return
     */
    String getCityFullNameById(Long id);

    /**
     * @return java.util.Map<java.lang.Long, java.lang.String>
     * @Description :feign调用 通过城市ids 查找对应的城市名称map
     * @Param [ids]
     * <AUTHOR>
     */
    Map<Long, String> getCityNamesByIds(Set<Long> ids);

    /**
     * 通过城市ids 查找对应的城市名称map（拼接name_cn）
     *
     * @param ids
     * @return
     */
    Map<Long, String> getCityFullNamesByIds(Set<Long> ids);

    /**
     * @return java.util.Map<java.lang.Long, java.lang.String>
     * @Description :feign调用 通过城市ids 查找对应的城市中文名称map
     * @Param [ids]
     * <AUTHOR>
     */
    Map<Long, String> getCityNameChnsByIds(Set<Long> ids);

    /**
     * @return java.util.List<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 附件列表
     * @Param [data, page]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> getItemMedia(MediaAndAttachedDto data, Page page);

    /**
     * @return java.lang.Long
     * @Description: 新增附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * 获取城市中文名称
     *
     * @param id
     * @return
     */
    String getCityChnNameById(Long id);

    /**
     * 获取对应国家、公司下 有申请计划的代理所在的 城市下拉框数据
     *
     * @Date 19:01 2023/1/5
     * <AUTHOR>
     */
    List<BaseSelectEntity> getExistsAgentOfferItemAreaStateCityList(Long companyId, String stateIds);

    /**
     * 获取对应国家、公司下的代理所在的 城市下拉框数据
     *
     * @Date 10:36 2023/3/23
     * <AUTHOR>
     */
    List<BaseSelectEntity> getExistsAgentAreaStateCityList(Long companyId, String stateIds);

    /**
     * 根据城市ids返回会省份/城市格式
     */
    String getAreaCityNameByIds(Set<Long> ids);

    /**
     * 获取所有城市名称
     */
    Map<Long, String> getCityFullNames();
}
