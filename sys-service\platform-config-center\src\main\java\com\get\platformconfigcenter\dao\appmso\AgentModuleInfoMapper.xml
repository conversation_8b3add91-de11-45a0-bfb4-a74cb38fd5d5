<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.platformconfigcenter.dao.appmso.AgentModuleInfoMapper">
  <resultMap id="BaseResultMap" type="com.get.platformconfigcenter.entity.AgentModuleInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_agent_id" jdbcType="BIGINT" property="fkAgentId" />
    <result column="module_key" jdbcType="VARCHAR" property="moduleKey" />
    <result column="fk_area_country_id" jdbcType="BIGINT" property="fkAreaCountryId" />
    <result column="fk_info_type_id" jdbcType="BIGINT" property="fkInfoTypeId" />
    <result column="component_name" jdbcType="VARCHAR" property="componentName" />
    <result column="column_title" jdbcType="VARCHAR" property="columnTitle" />
    <result column="view_order" jdbcType="INTEGER" property="viewOrder" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <sql id="Base_Column_List">
    id, fk_agent_id, module_key, fk_area_country_id, fk_info_type_id, component_name, 
    column_title, view_order, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
  </sql>

  <!--<insert id="insert" parameterType="com.get.platformconfigcenter.entity.AgentModuleInfo" keyProperty="id" useGeneratedKeys="true">
    insert into m_agent_module_info (id, fk_agent_id, module_key, 
      fk_area_country_id, fk_info_type_id, component_name, 
      column_title, view_order, gmt_create, 
      gmt_create_user, gmt_modified, gmt_modified_user
      )
    values (#{id,jdbcType=BIGINT}, #{fkAgentId,jdbcType=BIGINT}, #{moduleKey,jdbcType=VARCHAR}, 
      #{fkAreaCountryId,jdbcType=BIGINT}, #{fkInfoTypeId,jdbcType=BIGINT}, #{componentName,jdbcType=VARCHAR}, 
      #{columnTitle,jdbcType=VARCHAR}, #{viewOrder,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.get.platformconfigcenter.entity.AgentModuleInfo" keyProperty="id" useGeneratedKeys="true">
    insert into m_agent_module_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkAgentId != null">
        fk_agent_id,
      </if>
      <if test="moduleKey != null">
        module_key,
      </if>
      <if test="fkAreaCountryId != null">
        fk_area_country_id,
      </if>
      <if test="fkInfoTypeId != null">
        fk_info_type_id,
      </if>
      <if test="componentName != null">
        component_name,
      </if>
      <if test="columnTitle != null">
        column_title,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkAgentId != null">
        #{fkAgentId,jdbcType=BIGINT},
      </if>
      <if test="moduleKey != null">
        #{moduleKey,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCountryId != null">
        #{fkAreaCountryId,jdbcType=BIGINT},
      </if>
      <if test="fkInfoTypeId != null">
        #{fkInfoTypeId,jdbcType=BIGINT},
      </if>
      <if test="componentName != null">
        #{componentName,jdbcType=VARCHAR},
      </if>
      <if test="columnTitle != null">
        #{columnTitle,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.get.platformconfigcenter.entity.AgentModuleInfo">
    update m_agent_module_info
    <set>
      <if test="fkAgentId != null">
        fk_agent_id = #{fkAgentId,jdbcType=BIGINT},
      </if>
      <if test="moduleKey != null">
        module_key = #{moduleKey,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCountryId != null">
        fk_area_country_id = #{fkAreaCountryId,jdbcType=BIGINT},
      </if>
      <if test="fkInfoTypeId != null">
        fk_info_type_id = #{fkInfoTypeId,jdbcType=BIGINT},
      </if>
      <if test="componentName != null">
        component_name = #{componentName,jdbcType=VARCHAR},
      </if>
      <if test="columnTitle != null">
        column_title = #{columnTitle,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        view_order = #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.platformconfigcenter.entity.AgentModuleInfo">
    update m_agent_module_info
    set fk_agent_id = #{fkAgentId,jdbcType=BIGINT},
      module_key = #{moduleKey,jdbcType=VARCHAR},
      fk_area_country_id = #{fkAreaCountryId,jdbcType=BIGINT},
      fk_info_type_id = #{fkInfoTypeId,jdbcType=BIGINT},
      component_name = #{componentName,jdbcType=VARCHAR},
      column_title = #{columnTitle,jdbcType=VARCHAR},
      view_order = #{viewOrder,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select
      IFNULL(max(view_order)+1,0) view_order
    from
      m_agent_module_info
  </select>-->
</mapper>