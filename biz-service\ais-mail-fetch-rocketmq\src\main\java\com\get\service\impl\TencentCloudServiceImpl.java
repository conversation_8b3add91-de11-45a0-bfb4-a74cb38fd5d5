package com.get.service.impl;

import cn.hutool.core.util.ObjectUtil;

import com.get.config.ConnectTencentCloud;
import com.get.entity.MFileMail;
import com.get.service.ITencentCloudService;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.model.*;
import com.qcloud.cos.utils.IOUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;

@Slf4j
@Service
public class TencentCloudServiceImpl implements ITencentCloudService {
    //图片存储地址-公开桶
    @Value("${file.tencentcloudimage.bucketname}")
    private String imageBucketName;

    //文件存储
    @Value("${file.tencentcloudfile.bucketname}")
    private String fileBucketName;

    @Override
    public Boolean uploadObject(boolean isPub, String bucketName, File file, String fileKey) {
        try {
           /* // 指定要上传的文件
            File localFile = null;
            try {
                localFile = multipartFileToFile(file);
            } catch (Exception e) {
                e.printStackTrace();
            }*/
            if (ObjectUtil.isNotEmpty(file)) {
                if (file.exists()) {
                    try {
                        Thread.sleep(15);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    // fileKey 指定要上传到 COS 上对象键
                    PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, fileKey, file);

                    //公开桶上传
                    if (isPub) {
                        log.info("上传文件到公开桶：" + bucketName);
                        PutObjectResult putObjectResult = ConnectTencentCloud.getPublicCosClient().putObject(putObjectRequest);
                    } else {
                        log.info("上传文件到私密桶：" + bucketName);
                        PutObjectResult putObjectResult = ConnectTencentCloud.getPrivateCosClient().putObject(putObjectRequest);
                    }
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return true;
    }

    @Override
    public void downLoadObject(MFileMail mFileMail, HttpServletResponse response, Boolean isPub, Boolean isShare, File file, Boolean isParse) {
        String bucketName = isPub ? imageBucketName : fileBucketName;
        response.setCharacterEncoding("UTF-8");
        GetObjectRequest getObjectRequest = new GetObjectRequest(bucketName, mFileMail.getFileKey());
        BufferedOutputStream outputStream = null;
        COSObjectInputStream cosObjectInput = null;
        try {
            COSObject cosObject = null;
            if (!isPub) {
                cosObject = ConnectTencentCloud.getPrivateCosClient().getObject(getObjectRequest);
            } else {
                cosObject = ConnectTencentCloud.getPublicCosClient().getObject(getObjectRequest);
            }
            cosObjectInput = cosObject.getObjectContent();
            outputStream = new BufferedOutputStream(response.getOutputStream());
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(new String(mFileMail.getFileNameOrc().getBytes("utf-8"), "UTF-8")));
        } catch (CosServiceException e) {
            e.printStackTrace();
        } catch (CosClientException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (isParse) {
            try (FileOutputStream fos = new FileOutputStream(file)) {
                // 写入字节数组到文件
                fos.write(IOUtils.toByteArray(cosObjectInput));
                return;
            } catch (IOException e) {
                System.err.println("Error writing to file: " + e.getMessage());
                e.printStackTrace();
                return;
            }
        }
        // 处理下载到的流
        // 这里是直接读取，按实际情况来处理
        byte[] bytes = null;
        try {
            bytes = IOUtils.toByteArray(cosObjectInput);
            outputStream.write(bytes);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            // 用完流之后一定要调用 close()
            try {
                cosObjectInput.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        try {
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 在流没有处理完之前，不能关闭 cosClient
            // 确认本进程不再使用 cosClient 实例之后，关闭之
            if (!isPub) {
                ConnectTencentCloud.getPrivateCosClient().shutdown();
            } else {
                ConnectTencentCloud.getPublicCosClient().shutdown();
            }
        }
    }

    @Override
    public void downLoadTofile(MFileMail mFileMail, Boolean isPub, File file) {
        String bucketName = isPub ? imageBucketName : fileBucketName;
        GetObjectRequest getObjectRequest = new GetObjectRequest(bucketName, mFileMail.getFileKey());
        BufferedOutputStream outputStream = null;
        COSObjectInputStream cosObjectInput = null;
        try {
            COSObject cosObject = null;
            if (!isPub) {
                cosObject = ConnectTencentCloud.getPrivateCosClient().getObject(getObjectRequest);
            } else {
                cosObject = ConnectTencentCloud.getPublicCosClient().getObject(getObjectRequest);
            }
            cosObjectInput = cosObject.getObjectContent();
        } catch (CosServiceException e) {
            e.printStackTrace();
        } catch (CosClientException e) {
            e.printStackTrace();
        }
        try (FileOutputStream fos = new FileOutputStream(file)) {
            // 写入字节数组到文件
            fos.write(IOUtils.toByteArray(cosObjectInput));
        } catch (IOException e) {
            System.err.println("Error writing to file: " + e.getMessage());
            e.printStackTrace();
        }

    }

    /**
     * MultipartFile 转 File
     *
     * @param file
     * @throws Exception
     */
    public static File multipartFileToFile(MultipartFile file) throws Exception {

        File toFile = null;
        if (file.equals("") || file.getSize() <= 0) {
            file = null;
        } else {
            InputStream ins = null;
            ins = file.getInputStream();
            toFile = new File(file.getOriginalFilename());
            inputStreamToFile(ins, toFile);
            ins.close();
        }
        return toFile;
    }

    /**
     * 获取流文件
     *
     * @param ins  传入文件字节流
     * @param file 返回的文件对象
     */
    private static void inputStreamToFile(InputStream ins, File file) {
        try {
            OutputStream os = new FileOutputStream(file);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            ins.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
