<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.examcenter.mapper.exam.ExaminationQuestionMapper">

    <select id="getExaminationQuestionByIdsAndCount" resultType="com.get.examcenter.vo.ExaminationQuestionVo">
        SELECT
        id AS id,
        fk_question_type_id AS fkQuestionTypeId,
        num AS num,
        question_type AS questionType,
        question AS question,
        score AS score,
        time_limit AS timeLimit,
        is_review AS isReview,
        is_retest AS isRetest,
        is_active AS isActive,
        view_order AS viewOrder,
        gmt_create AS gmtCreate,
        gmt_create_user AS gmtCreateUser,
        gmt_modified AS gmtModified,
        gmt_modified_user AS gmtModifiedUser
        FROM
        m_examination_question where is_active = 1 and id in
        <foreach collection="questionIds" item="questionId" index="index" open="(" separator="," close=")">
            #{questionId}
        </foreach>
        ORDER BY view_order desc,RAND()
        <if test="questionCount != null">
            limit #{questionCount}
        </if>
    </select>


    <select id="getQuestionSumCount" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            m_examination_question where is_active = 1
    </select>

    <select id="getExaminationQuestionByTel" resultType="com.get.examcenter.entity.ExaminationQuestion">
        SELECT
            meq.*
        FROM
            ais_exam_center.m_examination_question AS meq
                INNER JOIN m_examination_question_assign AS meqa ON meqa.target_id = meq.id
                INNER JOIN m_examination_paper AS mep ON mep.id = meqa.fk_examination_paper_id
        WHERE mep.admin_authentication LIKE CONCAT('%', #{contactTel}, '%')
        AND mep.fk_examination_id = #{fkExaminationId}
    </select>

</mapper>