package com.get.officecenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2023/1/10
 * @TIME: 12:32
 * @Description:
 **/
@Data
public class TimeConfigVo extends BaseVoEntity {
    /**
     * 工作开始时间
     */
    @ApiModelProperty(value = "工作开始时间")
    private String workingStartTime;

    /**
     * 工作结束时间
     */
    @ApiModelProperty(value = "工作结束时间")
    private String workingEndTime;
}
