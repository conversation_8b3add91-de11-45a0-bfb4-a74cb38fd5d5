package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2021/4/30
 * @TIME: 17:05
 * @Description:
 **/
@Data
public class PermissionGroupGradeNameVo extends BaseVoEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 权限组别Id
     */
    @ApiModelProperty(value = "权限组别Id")
    private Long fkPermissionGroupId;

    /**
     * 权限级别Id
     */
    @ApiModelProperty(value = "权限级别Id")
    private Long fkPermissionGradeId;

    /**
     * 权格名称
     */
    @ApiModelProperty(value = "权格名称")
    private String permissionName;
}
