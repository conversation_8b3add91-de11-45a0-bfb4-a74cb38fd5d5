package com.get.platformconfigcenter.dao.appissue;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.AppFormConfigAttachment;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("issuedb")
public interface AppFormConfigAttachmentMapper extends BaseMapper<AppFormConfigAttachment> {

//    int insert(AppFormConfigAttachment record);
//
//    int insertSelective(AppFormConfigAttachment record);
//
//    int updateByPrimaryKeySelective(AppFormConfigAttachment record);
//
//    int updateByPrimaryKey(AppFormConfigAttachment record);
}