package com.get.salecenter.dto;

import com.get.salecenter.vo.StudentProjectRoleStaffVo;
import com.get.salecenter.vo.StudentRoleAndStaffVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ClientOfferInfoDto {

    private Long id;

//    @ApiModelProperty(value = "BD名称")
//    private String bdName;

    @ApiModelProperty(value = "联系人邮箱")
    private String email;

//    @ApiModelProperty(value = "代理名称")
//    private String fkAgentName;

    @ApiModelProperty(value = "客户名称")
    private String clientName;


    @ApiModelProperty(value = "客户编号")
    private String num;

    @ApiModelProperty(value = "代理id")
    private Long fkAgentId;

    @ApiModelProperty(value = "国家id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "国家名字")
    private String fkAreaCountryName;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "代理联系电话，多个用【;+空格】隔开")
    private String agentContactTel;

    @ApiModelProperty(value = "代理联系电邮")
    private String agentContactEmail;

    @ApiModelProperty(value = "绑定的项目成员")
    private List<StudentProjectRoleStaffVo> studentProjectRoleStaffDtos;

    @ApiModelProperty(value = "角色员工")
    private List<StudentRoleAndStaffVo> studentRoleAndStaffList;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "代理联系电话区号")
    private String agentContactTelAreaCode;

}
