package com.get.platformconfigcenter.dao.hubs;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.platformconfigcenter.vo.HubsUserVo;
import com.get.platformconfigcenter.vo.SaleOrderItemVo;
import com.get.platformconfigcenter.vo.SaleOrderUserInfoVo;
import com.get.platformconfigcenter.vo.SubmitOrderVo;
import com.get.platformconfigcenter.entity.CountryButler;
import com.get.platformconfigcenter.entity.SaleOrder;
import com.get.platformconfigcenter.dto.AppHubsSearchOrderDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *  Mapper
 *
 * <AUTHOR>
 * @since 2024-09-03 09:53
 */
@Mapper
@DS("apphubsdb")
public interface SaleOrderMapper extends BaseMapper<SaleOrder> {


    List<SubmitOrderVo> getAppHubsOrders(IPage<SubmitOrderVo> iPage, @Param("data") AppHubsSearchOrderDto data);

    SaleOrderUserInfoVo getSaleOrderUserInfo(@Param("fkOrderId") Long fkOrderId);

    List<SaleOrderItemVo> getAppHubsOrdersItemDetailByOrderId(@Param("fkOrderId") Long fkOrderId);

    List<CountryButler> getAppHubsCountry();

    List<HubsUserVo> getRegisteredUser(IPage<HubsUserVo> iPage, @Param("data") AppHubsSearchOrderDto data);
}
