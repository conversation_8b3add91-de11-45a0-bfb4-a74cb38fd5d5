package com.get.insurancecenter.dto.card;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Author:Oliver
 * @Date: 2025/8/5
 * @Version 1.0
 * @apiNote:保存信用卡交易记录
 */
@Data
public class SaveStatementDto {

    @ApiModelProperty(value = "信用卡ID")
    @NotNull(message = "信用卡ID不能为空")
    private Long creditCardId;

    @ApiModelProperty(value = "交易类型：0调整(校正金额)/1支出/2收取(还款)/3退款")
    @NotNull(message = "交易类型不能为空")
    private Integer businessType;

    @ApiModelProperty(value = "业务类型")
    private String relationTargetKey;

    @ApiModelProperty(value = "关联的订单ID")
    private Long orderId;

    @ApiModelProperty(value = "币种")
    @NotBlank(message = "币种不能为空")
    private String currencyTypeNum;

    @ApiModelProperty(value = "交易金额")
    @NotNull(message = "交易金额不能为空")
    private BigDecimal amount;

    @ApiModelProperty("交易状态：0失败/1成功")
    private Integer status;


}
