package com.get.institutioncenter.service;

import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.institutioncenter.vo.AreaRegionVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2021/7/30
 * @TIME: 11:32
 * @Description:
 **/
public interface IAreaRegionService {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    AreaRegionVo findAreaRegionById(Long id);

    /**
     * 大区下拉
     *
     * @return
     */
    List<BaseSelectEntity> getAreaRegionSelect(Long fkCompanyId);
    /**
     * 根据公司IDs获取大区下拉
     * <AUTHOR>
     * @DateTime 2022/12/14 10:42
     */
    List<BaseSelectEntity> getAreaRegionSelectByCompanyIds(List<Long> fkCompanyIds);

    Map<Long, String> getAllAreaRegionChnNames();

    /**
     * 根据州省ids获取对应的大区对象Map
     *
     * @Date 17:25 2023/12/19
     * <AUTHOR>
     */
    Map<Long, AreaRegionVo> getRegionMapByStateIds(List<Long> stateIds);

    Map<Long,String> getAreaRegionNameByIds(Set<Long> fkAreaRegionIds);
}
