package com.get.aismail.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("u_area_state")
public class AreaState extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 州省编号
     */
    @ApiModelProperty(value = "州省编号")
    @Column(name = "num")
    private String num;
    /**
     * 州省名称
     */
    @ApiModelProperty(value = "州省名称")
    @Column(name = "name")
    private String name;
    /**
     * 州省中文名称
     */
    @ApiModelProperty(value = "州省中文名称")
    @Column(name = "name_chn")
    private String nameChn;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}