package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.salecenter.dao.sale.PayablePlanMapper;
import com.get.salecenter.vo.AgentPaySumVo;
import com.get.salecenter.vo.AgentPaySumExportVo;
import com.get.salecenter.service.AgentPaySumService;
import com.get.salecenter.dto.AgentPaySumDto;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　　　◆　　　　　　　　　　　　◆　　　　　　　　　　　　　　◆　　　　　　　　　　　　　◆◆　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　◆◆◆　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆
 * 　　　◆◆　　　　　◆◆　　　　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆◆◆◆
 * 　　　◆◆◆　　　◆◆◆　　　　　　　◆◆　　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆◆
 * 　　　　◆◆◆　◆◆◆　　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆
 * 　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　　◆◆
 * 　　　　　　◆◆◆　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆
 * 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆◆
 * <p>
 * Time: 17:12
 * Date: 2021/11/19
 * Description:代理应付汇总统计业务实现类
 */
@Service
public class AgentPaySumServiceImpl implements AgentPaySumService {

    @Resource
    private PayablePlanMapper payablePlanMapper;
//    @Resource
//    private IAgentCompanyService agentCompanyService;
//    @Resource
//    private IFinanceCenterClient financeCenterClient;
//    @Resource
//    private IPermissionCenterClient permissionCenterClient;

    /**
     * @Description: 代理应付汇总统计列表
     * @Author: Jerry
     * @Date:17:13 2021/11/19
     */
    @Override
    public List<AgentPaySumVo> datas(AgentPaySumDto agentPaySumDto, Page page) {
        List<AgentPaySumVo> agentPaySumVos = new ArrayList<>();
        if (GeneralTool.isNotEmpty(agentPaySumDto.getFkCompanyIds())) {
            //查看的公司权限
            if (!SecureUtil.validateCompany(agentPaySumDto.getFkCompanyId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }
        if (page == null) {
            agentPaySumVos = payablePlanMapper.getAgentPaySumDatas(null, agentPaySumDto);
        } else {
            IPage<AgentPaySumVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            agentPaySumVos = payablePlanMapper.getAgentPaySumDatas(iPage, agentPaySumDto);
            page.setAll((int) iPage.getTotal());
        }
        if (GeneralTool.isEmpty(agentPaySumVos)) {
            return Collections.emptyList();
        }
        for (AgentPaySumVo agentPaySumVo : agentPaySumVos) {
            String currencyTypeName = agentPaySumVo.getFkCurrencyTypeName();
            StringBuilder sb = new StringBuilder();
            sb.append("(").append(currencyTypeName).append(")");
            agentPaySumVo.setFkCurrencyTypeName(currencyTypeName);
            if (StringUtils.isNotBlank(agentPaySumVo.getPayableAmount())) {
                agentPaySumVo.setPayableAmountStr(Math.abs(Double.parseDouble(agentPaySumVo.getPayableAmount())) + sb.toString());
            }
            if (GeneralTool.isNotEmpty(agentPaySumVo.getSettlementAmountPayable())){
                agentPaySumVo.setSettlementAmountPayableStr(Math.abs(Double.parseDouble(agentPaySumVo.getSettlementAmountPayable())) + sb.toString());
            }
            if (StringUtils.isNotBlank(agentPaySumVo.getAmountPayable())) {
                agentPaySumVo.setAmountPayableStr(Math.abs(Double.parseDouble(agentPaySumVo.getAmountPayable())) + sb.toString());
            }
            agentPaySumVo.setDifferPayStr(agentPaySumVo.getDifferPay() + sb.toString());
        }
        return agentPaySumVos;
    }


    /**
     * @Description: 导出代理应付汇总统计列表Excel
     * @Author: Jerry
     * @Date:11:43 2021/11/20
     */
    @Override
    public void exportAgentPaySumExcel(HttpServletResponse response, AgentPaySumDto agentPaySumDto) {
        List<AgentPaySumVo> dataList = datas(agentPaySumDto, null);
        if (GeneralTool.isEmpty(dataList)) {
            return;
        }
        List<AgentPaySumExportVo> agentPaySumExportVos = new ArrayList<>();
        for (AgentPaySumVo agentPaySumVo : dataList) {
            AgentPaySumExportVo agentPaySumExportVo = BeanCopyUtils.objClone(agentPaySumVo, AgentPaySumExportVo::new);
            if (GeneralTool.isNotEmpty(agentPaySumVo.getFkCurrencyTypeNum())) {
                agentPaySumExportVo.setFkCurrencyTypeName(agentPaySumVo.getFkCurrencyTypeName() + "（" + agentPaySumVo.getFkCurrencyTypeNum() + "）");
            }
            agentPaySumExportVos.add(agentPaySumExportVo);
        }
        FileUtils.exportExcelNotWrapText(response, agentPaySumExportVos, "AgentPaySumInfo", AgentPaySumExportVo.class);
    }
}
