package com.get.pmpcenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.pmpcenter.dto.agent.AgentCommissionPlanListDto;
import com.get.pmpcenter.dto.agent.InstitutionAgentPlanDto;
import com.get.pmpcenter.dto.agent.TimeOverlapVerifyDto;
import com.get.pmpcenter.dto.agent.UpdateAgentCommissionPlanDto;
import com.get.pmpcenter.dto.common.LogDto;
import com.get.pmpcenter.dto.common.UpdatePlanStatusDto;
import com.get.pmpcenter.entity.*;
import com.get.pmpcenter.enums.ApprovalStatusEnum;
import com.get.pmpcenter.enums.LogEventEnum;
import com.get.pmpcenter.enums.LogTableEnum;
import com.get.pmpcenter.enums.LogTypeEnum;
import com.get.pmpcenter.event.publisher.CommissionChangedEventPublisher;
import com.get.pmpcenter.mapper.*;
import com.get.pmpcenter.service.*;
import com.get.pmpcenter.utils.EntityCompareUtil;
import com.get.pmpcenter.utils.GeneratorLogsUtil;
import com.get.pmpcenter.vo.agent.*;
import com.get.pmpcenter.vo.common.*;
import com.get.pmpcenter.vo.institution.InstitutionLabelVo;
import com.get.pmpcenter.vo.institution.ProviderVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:26
 * @Version 1.0
 */
@Service
@Slf4j
public class AgentCommissionPlanServiceImpl extends ServiceImpl<AgentCommissionPlanMapper, AgentCommissionPlan> implements AgentCommissionPlanService {

    @Autowired
    private AgentCommissionPlanMapper agentCommissionPlanMapper;
    @Autowired
    private AgentCommissionPlanCompanyService planCompanyService;
    @Autowired
    private AgentCommissionPlanCompanyMapper planCompanyMapper;
    @Autowired
    private InstitutionProviderCommissionPlanMapper providerCommissionPlanMapper;
    @Autowired
    private InstitutionCenterMapper institutionCenterMapper;
    @Autowired
    private AgentCommissionPlanInstitutionService planInstitutionService;
    @Autowired
    private AgentCommissionService agentCommissionService;
    @Autowired
    private AgentCommissionMapper agentCommissionMapper;
    @Autowired
    private AgentCommissionMajorLevelCustomService commissionMajorLevelCustomService;
    @Autowired
    private InstitutionProviderCommissionPlanInstitutionMapper providerCommissionPlanInstitutionMapper;
    @Autowired
    private AgentCommissionPlanInstitutionMapper agentCommissionPlanInstitutionMapper;
    @Autowired
    private CommissionChangedEventPublisher commissionChangedEventPublisher;
    @Autowired
    private AgentCommissionTypeMapper agentCommissionTypeMapper;
    @Autowired
    private AgentCommissionPlanService agentCommissionPlanService;
    @Autowired
    private InstitutionProviderContractService providerContractService;
    @Autowired
    private AgentCommissionPlanApprovalMapper planApprovalMapper;
    @Autowired
    private PermissionCenterMapper permissionCenterMapper;
    @Autowired
    private InstitutionProviderCommissionPlanService commissionPlanService;
    @Autowired
    private InstitutionProviderCommissionPlanInstitutionService providerCommissionPlanInstitutionService;
    @Autowired
    private InstitutionProviderCommissionPlanTerritoryMapper commissionPlanTerritoryMapper;
    @Autowired
    private AgentCommissionPlanApprovalService agentCommissionPlanApprovalService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long updateAgentCommissionPlan(UpdateAgentCommissionPlanDto saveAgentCommissionPlanDto) {
        List<LogDto> logs = new ArrayList<>();
        UserInfo user = SecureUtil.getUser();
        if (saveAgentCommissionPlanDto.getIsTimeless().equals(0) && Objects.isNull(saveAgentCommissionPlanDto.getEndTime())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_END_DATE_REQUIRED", "方案结束时间不能为空"));
        }
        if (Objects.nonNull(saveAgentCommissionPlanDto.getId()) && saveAgentCommissionPlanDto.getId() > 0) {
            AgentCommissionPlan current = agentCommissionPlanMapper.selectById(saveAgentCommissionPlanDto.getId());
            if (Objects.isNull(current)) {
                log.error("编辑佣金方案失败，该佣金方案不存在:参数{}", JSONObject.toJSONString(saveAgentCommissionPlanDto));
                throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_NOT_FOUND", "方案不存在"));
            }

            //有代理类型的情况下，学校方案模板只能被一种类型继承，不能多种
            if (Objects.nonNull(saveAgentCommissionPlanDto.getFkAgentCommissionTypeId())) {
                Set<Long> typeSet = agentCommissionPlanMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlan>().eq(AgentCommissionPlan::getFkInstitutionProviderCommissionPlanId, saveAgentCommissionPlanDto.getFkInstitutionProviderCommissionPlanId()).eq(AgentCommissionPlan::getIsGobal, 0).eq(AgentCommissionPlan::getFkInstitutionProviderId, saveAgentCommissionPlanDto.getFkInstitutionProviderId()).isNotNull(AgentCommissionPlan::getFkAgentCommissionTypeId).ne(AgentCommissionPlan::getId, saveAgentCommissionPlanDto.getId()).exists(Objects.nonNull(saveAgentCommissionPlanDto.getCompanyId()), "select id from r_agent_commission_plan_company where fk_agent_commission_plan_id " + "= m_agent_commission_plan.id and fk_company_id = " + saveAgentCommissionPlanDto.getCompanyId())).stream().map(AgentCommissionPlan::getFkAgentCommissionTypeId).collect(Collectors.toSet());
                if (typeSet.contains(saveAgentCommissionPlanDto.getFkAgentCommissionTypeId())) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_DUPLICATE_PLAN", "已存在该类型的佣金方案，请勿重复添加"));
                }
            }

            AgentCommissionPlan original = new AgentCommissionPlan();
            BeanCopyUtils.copyProperties(current, original);


            BeanCopyUtils.copyProperties(saveAgentCommissionPlanDto, current);
            current.setGmtModified(new Date());
            current.setGmtModifiedUser(user.getLoginId());

            boolean change = EntityCompareUtil.compareFields(original, current);
            if (change) {
                logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.UPDATE_PLAN, LogTableEnum.AGENT_PLAN, saveAgentCommissionPlanDto.getId(), LogEventEnum.UPDATE_PLAN, user.getLoginId(), current.getName(), null));
            }
            //如果方案处于审核中,自动上锁
            if (current.getApprovalStatus().equals(ApprovalStatusEnum.PENDING_APPROVAL.getCode())) {
                current.setIsLocked(1);
            }
            agentCommissionPlanMapper.updateById(current);
            planCompanyService.saveAgentCommissionPlanCompany(current.getId(), saveAgentCommissionPlanDto.getCompanyId());
            commissionChangedEventPublisher.publishCommissionChangedEvent(logs);
            return current.getId();
        }
        AgentCommissionPlan plan = new AgentCommissionPlan();
        BeanCopyUtils.copyProperties(saveAgentCommissionPlanDto, plan);
        plan.setGmtCreate(new Date());
        plan.setGmtCreateUser(user.getLoginId());
        plan.setGmtModified(new Date());
        plan.setGmtModifiedUser(user.getLoginId());
        plan.setIsActive(1);
        plan.setIsLocked(0);
        plan.setApprovalStatus(ApprovalStatusEnum.UN_COMMITTED.getCode());
        plan.setIsInstitutionProviderCommissionModify(0);
        agentCommissionPlanMapper.insert(plan);
        planCompanyService.saveAgentCommissionPlanCompany(plan.getId(), saveAgentCommissionPlanDto.getCompanyId());
        plan.setViewOrder(plan.getId().intValue());
        agentCommissionPlanMapper.updateById(plan);
        return plan.getId();
    }

    @Override
    public AgentCommissionPlanBaseInfoVo getAgentCommissionPlanBaseInfo(Long id) {
        AgentCommissionPlanBaseInfoVo vo = new AgentCommissionPlanBaseInfoVo();
        AgentCommissionPlan plan = agentCommissionPlanMapper.selectById(id);
        if (Objects.isNull(plan)) {
            return vo;
        }
        BeanCopyUtils.copyProperties(plan, vo);
        AgentCommissionPlanCompany planCompany = planCompanyMapper.selectOne(new LambdaQueryWrapper<AgentCommissionPlanCompany>().eq(AgentCommissionPlanCompany::getFkAgentCommissionPlanId, id));
        if (Objects.nonNull(planCompany)) {
            vo.setCompanyId(planCompany.getFkCompanyId());
        }
        //继承模板名称
        InstitutionProviderCommissionPlan providerCommissionPlan = providerCommissionPlanMapper.selectById(plan.getFkInstitutionProviderCommissionPlanId());
        if (Objects.nonNull(providerCommissionPlan)) {
            vo.setExtendTemplateName(providerCommissionPlan.getName());
        }
        return vo;
    }

    @Override
    public List<AgentCommissionPlanVo> getAgentCommissionPlanList(Long institutionProviderId, Long companyId) {
        UserPlanIds userPlanIds = getUserPlanIds(companyId);
        List<Long> agentPlanIds = userPlanIds.getAgentPlanIds();
        List<Long> contractPlanIds = userPlanIds.getContractPlanIds();
        List<Long> expirePlanIds = providerContractService.getExpirePlanIds(institutionProviderId);
        log.info("过期的佣金方案id:{}", expirePlanIds);
        //非默认模板-如果继承的模板已经过期，则该模板失效
        List<AgentCommissionPlanVo> list = new ArrayList<>();
        List<AgentCommissionPlanVo> planList = agentCommissionPlanMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlan>()
                .eq(AgentCommissionPlan::getFkInstitutionProviderId, institutionProviderId)
                .exists(Objects.nonNull(companyId), "select id from r_agent_commission_plan_company where " +
                        "fk_agent_commission_plan_id = m_agent_commission_plan.id and fk_company_id = " + companyId)
                .notIn(CollectionUtils.isNotEmpty(expirePlanIds), AgentCommissionPlan::getFkInstitutionProviderCommissionPlanId, expirePlanIds)
                .in(AgentCommissionPlan::getId, agentPlanIds)
                .orderByDesc(AgentCommissionPlan::getId)).stream().map(plan -> {
            AgentCommissionPlanVo vo = new AgentCommissionPlanVo();
            BeanCopyUtils.copyProperties(plan, vo);
            return vo;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(planList)) {
            planList.stream().forEach(plan -> {
                AgentCommissionPlanVo agentCommissionPlanVo = checkAgentCommissionPlanPermission(plan);
                plan.setLockPermission(agentCommissionPlanVo.getLockPermission());
                plan.setApprovalPermission(agentCommissionPlanVo.getApprovalPermission());
            });
            list.addAll(planList);
        }
        List<Long> typeIds = planList.stream().map(AgentCommissionPlanVo::getFkAgentCommissionTypeId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(typeIds)) {
            Map<Long, String> typeMap = agentCommissionTypeMapper.selectBatchIds(typeIds).stream().collect(Collectors.toMap(AgentCommissionType::getId, item -> item.getTypeName()));
            planList.stream().forEach(vo -> {
                if (Objects.nonNull(vo.getFkAgentCommissionTypeId()) && typeMap.containsKey(vo.getFkAgentCommissionTypeId())) {
                    vo.setAgentCommissionTypeName(typeMap.get(vo.getFkAgentCommissionTypeId()));
                }
            });
        }
        //默认模板
        List<InstitutionProviderCommissionPlan> providerCommissionPlans = providerCommissionPlanMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
                .eq(InstitutionProviderCommissionPlan::getFkInstitutionProviderId, institutionProviderId)
                .notIn(CollectionUtils.isNotEmpty(expirePlanIds), InstitutionProviderCommissionPlan::getId, expirePlanIds)
                .eq(InstitutionProviderCommissionPlan::getIsActive, 1)
                .eq(InstitutionProviderCommissionPlan::getApprovalStatus, ApprovalStatusEnum.PASS.getCode())
                .in(InstitutionProviderCommissionPlan::getId, contractPlanIds)
                .exists("select 1 from m_institution_provider_commission where" +
                        " fk_institution_provider_commission_plan_id = m_institution_provider_commission_plan.id "));

        //过滤模板
        List<Long> gobalIds = planList.stream().filter(plan -> plan.getIsGobal().equals(1)).map(AgentCommissionPlanVo::getFkInstitutionProviderCommissionPlanId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(gobalIds)) {
            providerCommissionPlans = providerCommissionPlans.stream().filter(plan -> !gobalIds.contains(plan.getId())).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(providerCommissionPlans)) {
            List<AgentCommissionPlanVo> providerPlanList = providerCommissionPlans.stream().map(plan -> {
                AgentCommissionPlanVo agentCommissionPlan = AgentCommissionPlanVo.builder().fkInstitutionProviderCommissionPlanId(plan.getId()).name(plan.getName()).remark(plan.getRemark()).startTime(plan.getStartTime()).endTime(plan.getEndTime()).isTimeless(plan.getIsTimeless()).fkInstitutionProviderId(plan.getFkInstitutionProviderId()).isGobal(1).build();
                return agentCommissionPlan;
            }).collect(Collectors.toList());
            list.addAll(providerPlanList);
        }

        //返回模板名称
        List<Long> providerPlanIds = list.stream().map(AgentCommissionPlanVo::getFkInstitutionProviderCommissionPlanId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(providerPlanIds)) {
            Map<Long, String> providerPlanMap = providerCommissionPlanMapper.selectBatchIds(providerPlanIds).stream().collect(Collectors.toMap(InstitutionProviderCommissionPlan::getId, item -> item.getName()));
            list.stream().forEach(vo -> {
                if (Objects.nonNull(vo.getFkInstitutionProviderCommissionPlanId()) && providerPlanMap.containsKey(vo.getFkInstitutionProviderCommissionPlanId())) {
                    vo.setExtendTemplateName(providerPlanMap.get(vo.getFkInstitutionProviderCommissionPlanId()));
                }
            });
        }
        //过期的方案失效
//        if (CollectionUtils.isNotEmpty(expirePlanIds)) {
//            List<AgentCommissionPlan> unActiveList = agentCommissionPlanMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlan>().eq(AgentCommissionPlan::getFkInstitutionProviderId, institutionProviderId).eq(AgentCommissionPlan::getIsActive, 1).exists(Objects.nonNull(companyId), "select id from r_agent_commission_plan_company where fk_agent_commission_plan_id = m_agent_commission_plan.id and fk_company_id = " + companyId).in(CollectionUtils.isNotEmpty(expirePlanIds), AgentCommissionPlan::getFkInstitutionProviderCommissionPlanId, expirePlanIds));
//            if (CollectionUtils.isNotEmpty(unActiveList)) {
//                unActiveList.stream().forEach(plan -> {
//                    plan.setIsActive(0);
//                    plan.setGmtModified(new Date());
//                });
//                agentCommissionPlanService.updateBatchById(unActiveList);
//            }
//        }

        return list;
    }

    @Override
    public List<InstitutionVo> getAgentInstitutionList(Integer type, Long institutionProviderId, Long id) {
        List<Long> companyIds = SecureUtil.getCompanyIds();
        if (CollectionUtils.isEmpty(companyIds)) {
            log.error("登录用户无companyIds:{}", JSONObject.toJSONString(SecureUtil.getUser()));
            return Collections.EMPTY_LIST;
        }
        List<InstitutionVo> list = institutionCenterMapper.institutionList(institutionProviderId, companyIds);
        if (type.equals(2)) {
            log.info("学校IDS:{}", list.stream().map(InstitutionVo::getInstitutionId).collect(Collectors.toList()));
            List<Long> institutionIds = providerCommissionPlanInstitutionMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanInstitution>().eq(InstitutionProviderCommissionPlanInstitution::getFkInstitutionProviderCommissionPlanId, id)).stream().map(InstitutionProviderCommissionPlanInstitution::getFkInstitutionId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(institutionIds)) {
                //继承的模板没有保存学校,返回空
                return new ArrayList<>();
            }
            list = list.stream().filter(institutionVo -> institutionIds.contains(institutionVo.getInstitutionId())).collect(Collectors.toList());
        } else {
            List<Long> agentInstitutionIds = agentCommissionPlanInstitutionMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlanInstitution>().eq(AgentCommissionPlanInstitution::getFkAgentCommissionPlanId, id)).stream().map(AgentCommissionPlanInstitution::getFkInstitutionId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(agentInstitutionIds)) {
                //继承的模板没有保存学校,返回空
                return new ArrayList<>();
            }
            list = list.stream().filter(institutionVo -> agentInstitutionIds.contains(institutionVo.getInstitutionId())).collect(Collectors.toList());
        }
        list.stream().forEach(institutionVo -> {
            List<AgentPlanInstitutionVo> agentPlanInstitution = agentCommissionPlanInstitutionMapper.getAgentPlanInstitution(institutionVo.getInstitutionId());
            if (CollectionUtils.isNotEmpty(agentPlanInstitution)) {
                institutionVo.setDefPlans(agentPlanInstitution);
            }
            //填充学校标签
            InstitutionLabelVo institutionLabel = providerCommissionPlanInstitutionService.getInstitutionLabel(institutionVo.getInstitutionId());
            institutionVo.setInstitutionLabel(institutionLabel);
        });
        return list;
    }

    @Override
    public List<ExtendTemplateVo> getExtendTemplateList(Long institutionProviderId, Long companyId) {
        List<AgentCommissionPlanVo> agentCommissionPlanList = getAgentCommissionPlanList(institutionProviderId, companyId);
        List<ExtendTemplateVo> list = agentCommissionPlanList.stream().map(plan -> {
            ExtendTemplateVo templateVo = new ExtendTemplateVo();
            templateVo.setType(Objects.nonNull(plan.getId()) ? 1 : 2);
            templateVo.setId(Objects.nonNull(plan.getId()) ? plan.getId() : plan.getFkInstitutionProviderCommissionPlanId());
            templateVo.setName(plan.getName());
            templateVo.setStartTime(plan.getStartTime());
            templateVo.setEndTime(plan.getEndTime());
            templateVo.setIsTimeless(plan.getIsTimeless());
            templateVo.setRemark(plan.getRemark());
            templateVo.setProviderCommissionPlanId(plan.getFkInstitutionProviderCommissionPlanId());
            return templateVo;
        }).collect(Collectors.toList());
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delAgentCommissionPlan(Long id) {
        AgentCommissionPlan plan = agentCommissionPlanMapper.selectById(id);
        if (Objects.isNull(plan)) {
            return;
        }
        if (plan.getIsLocked().equals(1) && !SecureUtil.getLoginId().equals(plan.getGmtCreateUser())) {
            log.error("删除方案失败,没有权限,当前操作人:{},合同创建人:{},id:{}", SecureUtil.getLoginId(), plan.getGmtCreateUser(), id);
//            throw new GetServiceException("方案已锁定,没有权限操作");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_LOCKED", "方案已锁定,没有权限操作"));
        }
        List<LogDto> logs = new ArrayList<>();
        UserInfo user = SecureUtil.getUser();
        //1:佣金方案与学校的关联
        planInstitutionService.remove(new LambdaQueryWrapper<AgentCommissionPlanInstitution>().eq(AgentCommissionPlanInstitution::getFkAgentCommissionPlanId, id));
        //2:佣金方案与分公司的关联
        planCompanyService.remove(new LambdaQueryWrapper<AgentCommissionPlanCompany>().eq(AgentCommissionPlanCompany::getFkAgentCommissionPlanId, id));
        //3:佣金方案与课程等级的关联
        List<Long> commissionIds = agentCommissionMapper.selectList(new LambdaQueryWrapper<AgentCommission>().eq(AgentCommission::getFkAgentCommissionPlanId, id)).stream().map(AgentCommission::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(commissionIds)) {
            commissionMajorLevelCustomService.remove(new LambdaQueryWrapper<AgentCommissionMajorLevelCustom>().in(AgentCommissionMajorLevelCustom::getFkAgentCommissionId, commissionIds));
        }
        //4:佣金方案明细
        agentCommissionService.remove(new LambdaQueryWrapper<AgentCommission>().eq(AgentCommission::getFkAgentCommissionPlanId, id));
        //5:佣金方案
        agentCommissionPlanMapper.deleteById(id);
        //6:删除审核记录
        agentCommissionPlanApprovalService.remove(new LambdaQueryWrapper<AgentCommissionPlanApproval>().eq(AgentCommissionPlanApproval::getFkAgentCommissionPlanId, id));
        logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.DELETE_PLAN, LogTableEnum.AGENT_PLAN, id, LogEventEnum.DEL_PLAN, user.getLoginId(), "", null));
        commissionChangedEventPublisher.publishCommissionChangedEvent(logs);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delAgentCommissionPlanByProviderPlanId(Long providerPlanId) {
        if (Objects.isNull(providerPlanId)) {
            return;
        }
        List<AgentCommissionPlan> agentCommissionPlans = agentCommissionPlanMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlan>().eq(AgentCommissionPlan::getFkInstitutionProviderCommissionPlanId, providerPlanId));
        agentCommissionPlans.stream().forEach(plan -> {
            delAgentCommissionPlan(plan.getId());
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void ineffectiveAgentCommissionPlan(List<Long> providerPlanIds) {
        //合同下架或者过期，代理佣金对应的所有方案都改成下架，然后用户端对应的那些佣金数据都不显示
        if (CollectionUtils.isEmpty(providerPlanIds)) {
            return;
        }
        List<AgentCommissionPlan> agentCommissionPlans = agentCommissionPlanMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlan>().in(AgentCommissionPlan::getFkInstitutionProviderCommissionPlanId, providerPlanIds).eq(AgentCommissionPlan::getIsActive, 1));
        if (CollectionUtils.isNotEmpty(agentCommissionPlans)) {
            agentCommissionPlans.stream().forEach(plan -> {
                plan.setIsActive(0);
            });
            agentCommissionPlanService.updateBatchById(agentCommissionPlans);
        }
    }

    @Override
    public AgentCommissionPlanVo checkAgentCommissionPlanPermission(AgentCommissionPlanVo agentCommissionPlanVo) {
        if (Objects.isNull(agentCommissionPlanVo.getIsLocked())) {
            agentCommissionPlanVo.setIsLocked(0);
        }
        if (Objects.isNull(agentCommissionPlanVo.getApprovalStatus())) {
            agentCommissionPlanVo.setApprovalStatus(ApprovalStatusEnum.UN_COMMITTED.getCode());
        }

        if (SecureUtil.getLoginId().equals(agentCommissionPlanVo.getGmtCreateUser())) {
            //如果创建人和当前用户一致,有锁定权限
            agentCommissionPlanVo.setLockPermission(1);
        } else {
            //默认无锁定权限
            agentCommissionPlanVo.setLockPermission(0);
        }
        if (agentCommissionPlanVo.getIsLocked().equals(1)) {
            //判断锁定权限-默认无权限
            agentCommissionPlanVo.setLockPermission(2);
            if (SecureUtil.getLoginId().equals(agentCommissionPlanVo.getGmtCreateUser())) {
                agentCommissionPlanVo.setLockPermission(3);
            }
        }
        //判断审核权限
        if (SecureUtil.getLoginId().equals(agentCommissionPlanVo.getGmtCreateUser())) {
            //如果创建人和当前用户一致,有审核权限
            agentCommissionPlanVo.setApprovalPermission(1);
        } else {
            //默认无审核权限
            agentCommissionPlanVo.setApprovalPermission(0);
        }
        if (agentCommissionPlanVo.getApprovalStatus().equals(ApprovalStatusEnum.PENDING_APPROVAL.getCode())) {
            List<AgentCommissionPlanApproval> planApprovals = planApprovalMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlanApproval>().eq(AgentCommissionPlanApproval::getFkAgentCommissionPlanId, agentCommissionPlanVo.getId()).eq(AgentCommissionPlanApproval::getApprovalStatus, ApprovalStatusEnum.PENDING_APPROVAL.getCode()).orderByDesc(AgentCommissionPlanApproval::getGmtCreate));
            if (CollectionUtils.isNotEmpty(planApprovals)) {
                agentCommissionPlanVo.setApprovalPermission(2);
                AgentCommissionPlanApproval latestPlanApproval = planApprovals.get(0);
                if (latestPlanApproval.getFkStaffId().equals(SecureUtil.getStaffId())) {
                    agentCommissionPlanVo.setApprovalPermission(3);
                    //审批人-也可以解锁
                    if (agentCommissionPlanVo.getIsLocked().equals(1)) {
                        agentCommissionPlanVo.setLockPermission(3);
                    }
                }
            }
        }
        return agentCommissionPlanVo;
    }

    @Override
    public void updateAgentCommissionPlanName(Long providerCommissionPlanId, String name) {
        this.update(new UpdateWrapper<AgentCommissionPlan>().lambda().eq(AgentCommissionPlan::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId).isNull(AgentCommissionPlan::getFkAgentCommissionTypeId).set(AgentCommissionPlan::getName, name).set(AgentCommissionPlan::getGmtModified, new Date()).set(AgentCommissionPlan::getGmtModifiedUser, SecureUtil.getLoginId()));
    }

    @Override
    public void updateAgentCommissionPlanStatus(UpdatePlanStatusDto statusDto) {
        AgentCommissionPlan agentCommissionPlan = agentCommissionPlanMapper.selectById(statusDto.getPlanId());
        if (Objects.isNull(agentCommissionPlan)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_NOT_FOUND", "方案不存在"));
        }
        agentCommissionPlan.setIsActive(statusDto.getIsActive());
        agentCommissionPlan.setGmtModified(new Date());
        agentCommissionPlan.setGmtModifiedUser(SecureUtil.getLoginId());
        if (statusDto.getIsActive().equals(1)) {
            agentCommissionPlan.setViewOrder(0);
        }
        agentCommissionPlanMapper.updateById(agentCommissionPlan);

        //操作日志-上下架方案
        List<LogDto> logs = new ArrayList<>();
        logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.UPDATE_PLAN,
                LogTableEnum.AGENT_PLAN,
                statusDto.getPlanId(),
                statusDto.getIsActive().equals(1) ? LogEventEnum.ACTIVE_PLAN : LogEventEnum.UN_ACTIVE_PLAN,
                SecureUtil.getLoginId(),
                agentCommissionPlan.getName(),
                null));
        commissionChangedEventPublisher.publishCommissionChangedEvent(logs);
    }

    @Override
    public List<AgentCommissionPlanListVo> agentCommissionPlanList(AgentCommissionPlanListDto params, Page page) {
        page.setTotalPage(0);
        page.setTotalResult(0);
        //获取合同端过期的方案
        //获取当前登录人能看到的学校
        List<Long> countryIds = SecureUtil.getCountryIds();
        if (CollectionUtils.isEmpty(countryIds)) {
            page.setTotalPage(0);
            page.setTotalResult(0);
            log.error("登录用户无countryIds:{}", JSONObject.toJSONString(SecureUtil.getCountryIds()));
            return Collections.EMPTY_LIST;
        }
        List<InstitutionVo> allInstitution = institutionCenterMapper.getInstitutionIdsCountryIds(countryIds);
        List<Long> allInstitutionIds = allInstitution.stream().map(InstitutionVo::getInstitutionId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allInstitutionIds)) {
            log.error("当前用户国家ID下没有学校,登录用户:{},国家:{}", SecureUtil.getLoginId(), SecureUtil.getCountryIds());
            return new ArrayList<>();
        }

        //查询当前用户可以看到的代理方案和合同方案-根据当前员工的所属分公司和安全组别过滤-代理端
        UserPlanIds userPlanIds = getUserPlanIds(params.getCompanyId());
        List<Long> agentPlanIds = userPlanIds.getAgentPlanIds();
        List<Long> contractPlanIds = userPlanIds.getContractPlanIds();

        List<Long> countryInstitutionIds = new ArrayList<>();
        List<Long> expirePlanIds = providerContractService.getExpirePlanIds(params.getInstitutionProviderId());
        if (Objects.nonNull(params.getCountryId()) && params.getCountryId() > 0) {
            //根据国家查询可以看到的学校
            countryInstitutionIds = institutionCenterMapper.getInstitutionIdsCountryIds(Arrays.asList(params.getCountryId())).stream().map(InstitutionVo::getInstitutionId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(countryInstitutionIds)) {
                countryInstitutionIds.add(0L);
            }
        }
        Map<Long, InstitutionVo> institutionMap = allInstitution.stream()
                .collect(Collectors.toMap(
                        InstitutionVo::getInstitutionId,
                        Function.identity(),
                        (existing, replacement) -> existing));
        List<Long> userPermissionPlanIds = commissionPlanService.getUserPermissionPlanIds(null);

        //适用地区过滤
        //适用地区
        List<Long> territoryPlanIds = Collections.EMPTY_LIST;
        if (CollectionUtils.isNotEmpty(params.getTerritoryIds())) {
            List<String> regionIds = institutionCenterMapper.selectAreaRegionIdsByCountryIds(params.getTerritoryIds());
            List<Long> regionIdList = regionIds.stream()
                    .filter(Objects::nonNull)
                    .flatMap(s -> Arrays.stream(s.split(",")))
                    .map(String::trim)
                    .filter(str -> !str.isEmpty())
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            territoryPlanIds = commissionPlanTerritoryMapper.selectPlansByTerritoryIds(params.getTerritoryIds(),
                    regionIdList,
                    CollectionUtils.isNotEmpty(userPermissionPlanIds) ? userPermissionPlanIds : Arrays.asList(0L));
        }
        IPage<AgentCommissionPlanListVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<AgentCommissionPlanListVo> list = agentCommissionPlanMapper.selectAgentCommissionPlan(pages, params, SecureUtil.getLoginId(),
                SecureUtil.getStaffId(),
                expirePlanIds,
                allInstitutionIds,
                countryInstitutionIds,
                userPermissionPlanIds,
                SecureUtil.getCompanyIds(),
                params.getCompanyId(),
                agentPlanIds,
                contractPlanIds,
                CollectionUtils.isNotEmpty(territoryPlanIds) ? territoryPlanIds : Arrays.asList(0L));
        List<Long> companyIds = list.stream().map(AgentCommissionPlanListVo::getCompanyId).distinct().collect(Collectors.toList());
        Map<Long, String> companyMap = new HashMap();
        if (CollectionUtils.isNotEmpty(companyIds)) {
            companyMap = permissionCenterMapper.getCompanyList(companyIds)
                    .stream()
                    .collect(Collectors.toMap(
                            CompanyVo::getCompanyId,
                            CompanyVo::getNum,
                            (num1, num2) -> num1));
        }
        //续约中状态map
        Map<Long, Integer> renewalMap = new HashMap();
        List<Long> providerCommissionPlanIds = list.stream().map(AgentCommissionPlanListVo::getFkInstitutionProviderCommissionPlanId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(providerCommissionPlanIds)) {
            renewalMap = providerCommissionPlanMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
                            .in(InstitutionProviderCommissionPlan::getId, providerCommissionPlanIds))
                    .stream()
                    .collect(Collectors.toMap(
                            InstitutionProviderCommissionPlan::getId,
                            InstitutionProviderCommissionPlan::getIsRenewal,
                            (num1, num2) -> num1));
        }
        Map<Long, String> finalCompanyMap = companyMap;
        Map<Long, Integer> finalRenewalMap = renewalMap;
        list.stream().forEach(vo -> {
            //填充学校名称、学校数
            if (CollectionUtils.isNotEmpty(vo.getInstitutionIdList())) {
                vo.setInstitutionCount(vo.getInstitutionIdList().size());
                if (Objects.nonNull(institutionMap) && Objects.nonNull(vo.getInstitutionIdList().get(0))) {
                    vo.setInstitutionName(institutionMap.getOrDefault(vo.getInstitutionIdList().get(0), new InstitutionVo()).getName());
                    vo.setInstitutionNameChn(institutionMap.getOrDefault(vo.getInstitutionIdList().get(0), new InstitutionVo()).getNameChn());
                }
            }
            //填充分公司名称
            if (Objects.nonNull(vo.getCompanyId())) {
                vo.setCompanyNum(finalCompanyMap.get(vo.getCompanyId()));
            }
            //填充续约中状态
            vo.setIsRenewal(finalRenewalMap.getOrDefault(vo.getFkInstitutionProviderCommissionPlanId(), 0));
        });
        int totalCount = (int) pages.getTotal();
        Integer showCount = page.getShowCount();
        page.setTotalPage(page.getShowCount() != null && showCount > 0 ? totalCount % showCount == 0 ? totalCount / showCount : totalCount / showCount + 1 : 0);
        page.setTotalResult(totalCount);

        return list;
    }


    @Override
    public List<CountryVo> getAgentCountryList() {
        List<Long> currentUserCountryIds = SecureUtil.getCountryIds();
        if (CollectionUtils.isEmpty(currentUserCountryIds)) {
            log.error("登录用户无countryIds:{}", JSONObject.toJSONString(SecureUtil.getCountryIds()));
            return Collections.EMPTY_LIST;
        }
        return institutionCenterMapper.countryList().stream().filter(countryVo -> currentUserCountryIds.contains(countryVo.getCountryId())).collect(Collectors.toList());
    }

    @Override
    public void readAgentCommissionPlanModify(Long planId) {
        AgentCommissionPlan agentCommissionPlan = agentCommissionPlanMapper.selectById(planId);
        if (Objects.isNull(agentCommissionPlan)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_NOT_FOUND", "方案不存在"));
        }
        agentCommissionPlan.setIsInstitutionProviderCommissionModify(0);
        agentCommissionPlan.setGmtModified(new Date());
        agentCommissionPlan.setGmtModifiedUser(SecureUtil.getLoginId());
        agentCommissionPlanMapper.updateById(agentCommissionPlan);
    }

    @Override
    public List<InstitutionAgentPlanListVo> getInstitutionAgentPlanList(InstitutionAgentPlanDto agentPlanDtos) {
        if (CollectionUtils.isEmpty(agentPlanDtos.getInstitutionIds())) {
            return Collections.emptyList();
        }
        List<InstitutionVo> userInstitution = institutionCenterMapper.getInstitutionIdsCountryIds(SecureUtil.getCountryIds());
        List<Long> userInstitutionIds = userInstitution.stream().map(InstitutionVo::getInstitutionId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userInstitutionIds)) {
            log.error("当前用户国家ID下没有学校,登录用户:{},国家:{}", SecureUtil.getLoginId(), SecureUtil.getCountryIds());
            return new ArrayList<>();
        }
        List<InstitutionVo> institutionList = institutionCenterMapper.getInstitutionListByIds(agentPlanDtos.getInstitutionIds());
        if (CollectionUtils.isEmpty(institutionList)) {
            return Collections.emptyList();
        }
        institutionList = institutionList.stream().filter(institution -> userInstitutionIds.contains(institution.getInstitutionId())).collect(Collectors.toList());
        List<Long> providerIds = new ArrayList<>();
        List<InstitutionAgentPlanListVo> result = institutionList.stream().map(institution -> {
            InstitutionAgentPlanListVo agentPlanList = new InstitutionAgentPlanListVo();
            agentPlanList.setInstitutionId(institution.getInstitutionId());
            agentPlanList.setInstitutionName(institution.getName());
            List<Long> planIds = agentCommissionPlanInstitutionMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlanInstitution>()
                            .eq(AgentCommissionPlanInstitution::getFkInstitutionId, institution.getInstitutionId())
                            .exists(Objects.nonNull(agentPlanDtos.getCompanyId()),
                                    "select 1 from m_agent_commission_plan p where p.id = r_agent_commission_plan_institution.fk_agent_commission_plan_id " +
                                            "and exists(select id from r_agent_commission_plan_company where fk_agent_commission_plan_id " +
                                            "= p.id and fk_company_id = " + agentPlanDtos.getCompanyId() + " )"))
                    .stream()
                    .map(AgentCommissionPlanInstitution::getFkAgentCommissionPlanId)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(planIds)) {
//                List<AgentCommissionPlan> agentCommissionPlans = agentCommissionPlanMapper.selectList(
//                        new LambdaQueryWrapper<AgentCommissionPlan>()
//                                .in(AgentCommissionPlan::getId, planIds)
//                                .orderByDesc(AgentCommissionPlan::getIsActive)
//                                .orderByDesc(AgentCommissionPlan::getViewOrder)
//                                .orderByDesc(AgentCommissionPlan::getGmtCreate));
                List<AgentCommissionPlan> agentCommissionPlans = agentCommissionPlanMapper.selectPlanAndCommissionTypeNameByIds(planIds);
                providerIds.addAll(agentCommissionPlans.stream().map(AgentCommissionPlan::getFkInstitutionProviderId).distinct().collect(Collectors.toList()));
                List<InstitutionAgentPlanVo> planList = agentCommissionPlans.stream().map(agentCommissionPlan -> {
                    InstitutionAgentPlanVo institutionAgentPlanVo = new InstitutionAgentPlanVo();
                    institutionAgentPlanVo.setInstitutionProviderId(agentCommissionPlan.getFkInstitutionProviderId());
                    institutionAgentPlanVo.setPlanId(agentCommissionPlan.getId());
                    institutionAgentPlanVo.setPlanName(agentCommissionPlan.getName());
                    institutionAgentPlanVo.setViewOrder(agentCommissionPlan.getViewOrder());
                    institutionAgentPlanVo.setIsActive(agentCommissionPlan.getIsActive());
                    institutionAgentPlanVo.setStartTime(agentCommissionPlan.getStartTime());
                    institutionAgentPlanVo.setEndTime(agentCommissionPlan.getEndTime());
                    institutionAgentPlanVo.setIsTimeless(agentCommissionPlan.getIsTimeless());
                    institutionAgentPlanVo.setIsLocked(agentCommissionPlan.getIsLocked());
                    institutionAgentPlanVo.setAgentCommissionTypeName(agentCommissionPlan.getAgentCommissionTypeName());
                    institutionAgentPlanVo.setFkAgentCommissionTypeId(agentCommissionPlan.getFkAgentCommissionTypeId());
                    return institutionAgentPlanVo;
                }).collect(Collectors.toList());
                agentPlanList.setAgentPlans(planList);
            }
            return agentPlanList;
        }).collect(Collectors.toList());
        Map<Long, ProviderVo> providerMap;
        //填充学校名称-提供商名称
        List<ProviderVo> providerList = institutionCenterMapper.institutionProviderList(SecureUtil.getCompanyIds(), SecureUtil.getCountryIds());
        providerMap = providerList.stream()
                .filter(providerVo -> providerIds.contains(providerVo.getInstitutionProviderId()))
                .collect(Collectors.toMap(
                        ProviderVo::getInstitutionProviderId,
                        Function.identity(),
                        (existing, replacement) -> existing));
        Map<Long, ProviderVo> finalProviderMap = providerMap;
        result.stream().forEach(agentPlanList -> {
            if (CollectionUtils.isNotEmpty(agentPlanList.getAgentPlans())) {
                agentPlanList.getAgentPlans().stream().forEach(agentPlan -> {
                    agentPlan.setInstitutionProviderName(finalProviderMap.getOrDefault(agentPlan.getInstitutionProviderId(), new ProviderVo()).getName());
                });
            }
        });
        result = result.stream()
                .filter(vo -> vo.getAgentPlans() != null && vo.getAgentPlans().size() > 1)
                .collect(Collectors.toList());
        return result;
    }

    @Override
    public Integer hasManyPlans(Long planId, List<Long> institutionIds) {
        //同一个分公司下是否有多个方案
        if (CollectionUtils.isEmpty(institutionIds)) {
            return 0;
        }
        Long companyId = planCompanyService.getBaseMapper()
                .selectList(new LambdaQueryWrapper<AgentCommissionPlanCompany>()
                        .eq(AgentCommissionPlanCompany::getFkAgentCommissionPlanId, planId))
                .stream()
                .map(AgentCommissionPlanCompany::getFkCompanyId)
                .filter(Objects::nonNull)
                .distinct()
                .findFirst()
                .orElse(null);
        long count = planInstitutionService.getBaseMapper().selectList(new LambdaQueryWrapper<AgentCommissionPlanInstitution>()
                        .in(AgentCommissionPlanInstitution::getFkInstitutionId, institutionIds)
                        .ne(AgentCommissionPlanInstitution::getFkAgentCommissionPlanId, planId)
                        .exists(Objects.nonNull(companyId),
                                "select 1 from m_agent_commission_plan p where p.id = r_agent_commission_plan_institution.fk_agent_commission_plan_id " +
                                        "and exists(select id from r_agent_commission_plan_company where fk_agent_commission_plan_id " +
                                        "= p.id and fk_company_id = " + companyId + " )"))
                .stream()
                .map(AgentCommissionPlanInstitution::getFkAgentCommissionPlanId)
                .distinct()
                .count();
        return (int) count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sortAgentPlan(List<Long> sortList) {
        if (CollectionUtils.isEmpty(sortList)) {
            return;
        }
        if (!(sortList.size() == 2)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "status_invalid", "无效参数"));
        }
        //两个方案的viewOrder对调,靠前的id为第一个
        Long firstPlanId = sortList.get(0);
        Long secondPlanId = sortList.get(1);
        AgentCommissionPlan firstPlan = agentCommissionPlanMapper.selectById(firstPlanId);
        AgentCommissionPlan secondPlan = agentCommissionPlanMapper.selectById(secondPlanId);
        if (Objects.isNull(firstPlan) || Objects.isNull(secondPlan)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "status_invalid", "无效参数"));
        }
        int firstPlanNewViewOrder = Objects.isNull(secondPlan.getViewOrder()) ? secondPlan.getId().intValue() : secondPlan.getViewOrder();
        int secondPlanNewViewOrder = Objects.isNull(firstPlan.getViewOrder()) ? firstPlan.getId().intValue() : firstPlan.getViewOrder();

        firstPlan.setViewOrder(firstPlanNewViewOrder);
        firstPlan.setGmtModified(new Date());
        firstPlan.setGmtModifiedUser(SecureUtil.getLoginId());

        secondPlan.setViewOrder(secondPlanNewViewOrder);
        secondPlan.setGmtModified(new Date());
        secondPlan.setGmtModifiedUser(SecureUtil.getLoginId());
        agentCommissionPlanMapper.updateById(firstPlan);
        agentCommissionPlanMapper.updateById(secondPlan);

    }

    @Override
    public List<TimeOverlapPlanVo> getTimeOverlapPlanList(TimeOverlapVerifyDto verifyDto) {
        List<InstitutionVo> userInstitution = institutionCenterMapper.getInstitutionIdsCountryIds(SecureUtil.getCountryIds());
        List<Long> userInstitutionIds = userInstitution.stream().map(InstitutionVo::getInstitutionId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userInstitutionIds)) {
            log.error("当前用户国家ID下没有学校,登录用户:{},国家:{}", SecureUtil.getLoginId(), SecureUtil.getCountryIds());
            return new ArrayList<>();
        }
        List<AgentCommissionPlan> plans = agentCommissionPlanMapper.selectTimeOverlapPlans(verifyDto);
        if (CollectionUtils.isEmpty(plans)) {
            return Collections.emptyList();
        }
        List<Long> allInstitutionIds = new ArrayList<>();
        List<TimeOverlapPlanVo> list = plans.stream().map(plan -> {
            TimeOverlapPlanVo vo = new TimeOverlapPlanVo();
            BeanCopyUtils.copyProperties(plan, vo);
            vo.setPlanId(plan.getId());
            vo.setPlanName(plan.getName());
            vo.setInstitutionProviderId(plan.getFkInstitutionProviderId());
            vo.setCompanyId(verifyDto.getCompanyId());
            //学校名称,学校ID
            List<Long> institutionIds = planInstitutionService.getBaseMapper().selectList(new LambdaQueryWrapper<AgentCommissionPlanInstitution>()
                            .eq(AgentCommissionPlanInstitution::getFkAgentCommissionPlanId, plan.getId())
                            .in(AgentCommissionPlanInstitution::getFkInstitutionId, userInstitutionIds))
                    .stream().map(AgentCommissionPlanInstitution::getFkInstitutionId)
                    .collect(Collectors.toList());
            vo.setInstitutionIds(institutionIds);
            allInstitutionIds.addAll(institutionIds);
            return vo;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(list)) {
            Map<Long, InstitutionVo> institutionMap = new HashMap<>();
            TimeOverlapPlanVo overlapPlan = list.get(0);
            //填充提供商、分公司
            TimeOverlapPlanVo providerNameAndCompanyNum = institutionCenterMapper.getProviderNameAndCompanyNum(overlapPlan.getInstitutionProviderId(), overlapPlan.getCompanyId());
            if (CollectionUtils.isNotEmpty(allInstitutionIds)) {
                institutionMap = institutionCenterMapper.getInstitutionListByIds(allInstitutionIds).stream()
                        .collect(Collectors.toMap(InstitutionVo::getInstitutionId, Function.identity(), (a, b) -> a));
                Map<Long, InstitutionVo> finalInstitutionMap = institutionMap;
                list = list.stream()
                        .peek(vo -> {
                            String names = Optional.ofNullable(vo.getInstitutionIds())
                                    .orElse(Collections.emptyList())
                                    .stream()
                                    .map(finalInstitutionMap::get)
                                    .filter(Objects::nonNull)
                                    .map(InstitutionVo::getName)
                                    .collect(Collectors.joining(","));
                            vo.setInstitutionNames(names);
                            vo.setInstitutionProviderName(providerNameAndCompanyNum.getInstitutionProviderName());
                            vo.setCompanyNum(providerNameAndCompanyNum.getCompanyNum());
                        }).collect(Collectors.toList());
            }
        }
        return list.stream()
                .sorted(Comparator.comparing(
                        TimeOverlapPlanVo::getViewOrder,
                        Comparator.nullsFirst(Comparator.reverseOrder())
                ).thenComparing(
                        TimeOverlapPlanVo::getPlanId,
                        Comparator.nullsLast(Comparator.reverseOrder())
                ))
                .collect(Collectors.toList());
    }

    @Override
    public UserPlanIds getUserPlanIds(Long companyId) {
        List<InstitutionVo> allInstitution = institutionCenterMapper.getInstitutionIdsCountryIds(SecureUtil.getCountryIds());
        List<Long> allInstitutionIds = allInstitution.stream().map(InstitutionVo::getInstitutionId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allInstitutionIds)) {
            log.error("当前用户国家ID下没有学校,登录用户:{},国家:{}", SecureUtil.getLoginId(), SecureUtil.getCountryIds());
            return UserPlanIds.builder()
                    .agentPlanIds(Collections.singletonList(0L))
                    .contractPlanIds(Collections.singletonList(0L))
                    .build();
        }
        List<Long> agentPlanIds = new ArrayList<>();
        List<Long> contractPlanIds = new ArrayList<>();
        //查询当前用户可以看到的代理方案-根据当前员工的所属分公司和安全组别过滤-代理端
        List<ProviderInstitutionVo> currentProviderAgentPlanList = institutionCenterMapper.selectProviderInstitutionByCompanyIds(companyId,
                SecureUtil.getCompanyIds(), allInstitutionIds);
        if (CollectionUtils.isNotEmpty(currentProviderAgentPlanList)) {
            agentPlanIds = currentProviderAgentPlanList.stream()
                    .map(ProviderInstitutionVo::getPlanId)
                    .distinct()
                    .collect(Collectors.toList());
        } else {
            agentPlanIds.add(0L);
        }
        //查询当前用户可以看到的方案-根据当前员工的所属分公司和安全组别过滤-合同端
        List<ProviderInstitutionVo> currentProviderContractPlanList = institutionCenterMapper.selectContractProviderInstitutionByCompanyIds(SecureUtil.getCompanyIds(), allInstitutionIds);
        if (CollectionUtils.isNotEmpty(currentProviderContractPlanList)) {
            contractPlanIds = currentProviderContractPlanList.stream()
                    .map(ProviderInstitutionVo::getPlanId)
                    .distinct()
                    .collect(Collectors.toList());
        } else {
            contractPlanIds.add(0L);
        }

        //如果是台湾分公司和越南分公司,要根据使用国家过滤
        //Global-r_institution_provider_commission_plan_territory无记录表示全球国家适用
        //AU onshore-include=3;UK onshore-include=4;
        //包括:-include=1;case by case:include=2;
        //-1:除外-include=-1
        Set<Long> allowedCompanyIds = new HashSet<>(Arrays.asList(31L, 32L));
        if (allowedCompanyIds.contains(companyId)) {
            // companyId 是 31-台湾 或 32-越南-对应的国家id是台湾:33,越南36
            Long countryId = companyId.equals(31L) ? 33L : 36L;
            List<Long> regionIds = Collections.emptyList();
            //查询该国家关联的大区
            String areaRegionIds = institutionCenterMapper.selectCountryAreaRegionIds(countryId);
            if (StringUtils.isNotBlank(areaRegionIds)) {
                regionIds = Arrays.stream(areaRegionIds.split(","))
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
            }
            List<Long> providerPlanIds = commissionPlanTerritoryMapper.selectPlansByTerritoryId(countryId, regionIds);
            if (CollectionUtils.isEmpty(providerPlanIds)) {
                agentPlanIds = Collections.singletonList(0L);
                contractPlanIds = Collections.singletonList(0L);
                return UserPlanIds.builder()
                        .agentPlanIds(agentPlanIds)
                        .contractPlanIds(contractPlanIds)
                        .build();
            }
            //过滤合同方案
            contractPlanIds.retainAll(providerPlanIds);
            List<Long> territoryAgentPlanIds = agentCommissionPlanMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlan>()
                            .in(AgentCommissionPlan::getFkInstitutionProviderCommissionPlanId, providerPlanIds))
                    .stream()
                    .map(AgentCommissionPlan::getId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(territoryAgentPlanIds)) {
                agentPlanIds.retainAll(territoryAgentPlanIds);
            } else {
                // 没有匹配项，设置为默认值
                agentPlanIds = Collections.singletonList(0L);
            }
        }
        return UserPlanIds.builder()
                .agentPlanIds(CollectionUtils.isNotEmpty(agentPlanIds) ? agentPlanIds : Collections.singletonList(0L))
                .contractPlanIds(CollectionUtils.isNotEmpty(contractPlanIds) ? contractPlanIds : Collections.singletonList(0L))
                .build();
    }

    /**
     * 根据学校获取佣金-AI助手,初版,现已不适用
     *
     * @param institutionName
     * @return
     */
    @Override
    public List<ExtendAgentCommissionVo> getInstitutionAgentCommissionList(String institutionName) {
        List<InstitutionVo> allInstitution = institutionCenterMapper.getInstitutionIdByName(SecureUtil.getCountryIds(), institutionName);
        if (CollectionUtils.isEmpty(allInstitution)) {
            return Collections.emptyList();
        }
        List<Long> institutionIds = allInstitution.stream().map(InstitutionVo::getInstitutionId).collect(Collectors.toList());
        List<ProviderInstitutionVo> providerInstitutions = institutionCenterMapper.selectPassProviderInstitution(SecureUtil.getCompanyIds(), institutionIds);
        if (CollectionUtils.isEmpty(providerInstitutions)) {
            return Collections.emptyList();
        }
        List<Long> planIds = providerInstitutions.stream().map(ProviderInstitutionVo::getPlanId).distinct().collect(Collectors.toList());
        List<ExtendAgentCommissionVo> list = planIds.stream().map(planId -> {
            try {
                ExtendAgentCommissionVo detail = agentCommissionService.getAgentCommissionDetail(planId, false, false, null);
                return detail;
            } catch (Exception e) {
                log.error("获取学校代理佣金方案详情异常,planId:{}", planId);
                log.error("异常信息:{}", e.getMessage());
                return null;
            }
        }).collect(Collectors.toList());
        return list.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * AI助手-学校佣金方案列表-新版
     *
     * @param institutionName
     * @return
     */
    @Override
    public List<AiInstitutionCommissionVo> getAiInstitutionCommissionList(String institutionName) {
        List<InstitutionVo> allInstitution = institutionCenterMapper.getInstitutionIdByName(SecureUtil.getCountryIds(), institutionName);
        if (CollectionUtils.isEmpty(allInstitution)) {
            return Collections.emptyList();
        }
        // 1. 查出 学校id-institutionId 列表
        List<Long> institutionIds = allInstitution.stream()
                .map(InstitutionVo::getInstitutionId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 2. 查询 学校列表-ProviderInstitution 列表
        List<ProviderInstitutionVo> providerInstitutions =
                institutionCenterMapper.selectPassProviderInstitution(SecureUtil.getCompanyIds(), institutionIds);
        if (CollectionUtils.isEmpty(providerInstitutions)) {
            return Collections.emptyList();
        }

        // 3. 根据 planId 查询佣金明细- ExtendAgentCommissionVo
        Map<Long, ExtendAgentCommissionVo> planDetailMap = providerInstitutions.stream()
                .map(ProviderInstitutionVo::getPlanId)
                .filter(Objects::nonNull)
                .distinct()
                .map(planId -> {
                    try {
                        ExtendAgentCommissionVo vo = agentCommissionService.getAgentCommissionDetail(planId, false, false, null);
                        return new AbstractMap.SimpleEntry<>(planId, vo);
                    } catch (Exception e) {
                        log.error("获取学校代理佣金方案详情异常, planId:{}", planId, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        // 4. 按 学校id-institutionId 分组
        Map<Long, List<ProviderInstitutionVo>> institutionGroup = providerInstitutions.stream()
                .filter(p -> p.getInstitutionId() != null)
                .collect(Collectors.groupingBy(ProviderInstitutionVo::getInstitutionId));

        // 5. 组装成 List<AiInstitutionCommissionVo>
        List<AiInstitutionCommissionVo> result = institutionGroup.entrySet().stream()
                .map(entry -> {
                    Long institutionId = entry.getKey();
                    List<ProviderInstitutionVo> providerList = entry.getValue();

                    AiInstitutionCommissionVo aiVo = new AiInstitutionCommissionVo();
                    // 用第一个 provider 的学校名称
                    aiVo.setInstitutionName(providerList.get(0).getInstitutionName());

                    // 收集该学校所有方案
                    List<AiInstitutionCommissionVo.AIInstitutionPlan> planList = providerList.stream()
                            .map(ProviderInstitutionVo::getPlanId)
                            .filter(Objects::nonNull)
                            .distinct()
                            .map(planId -> {
                                ExtendAgentCommissionVo ext = planDetailMap.get(planId);
                                if (ext == null) {
                                    return null;
                                }

                                AiInstitutionCommissionVo.AIInstitutionPlan plan =
                                        new AiInstitutionCommissionVo.AIInstitutionPlan();

                                plan.setPlanName(Optional.ofNullable(ext.getUpdateAgentCommissionPlanDto())
                                        .map(UpdateAgentCommissionPlanDto::getName)
                                        .orElse(null));

                                // 方案明细
                                List<AiInstitutionCommissionVo.AIInstitutionCommissionDetail> commissionList =
                                        Optional.ofNullable(ext.getAgentCommissionInfoList())
                                                .orElse(Collections.emptyList())
                                                .stream()
                                                .filter(Objects::nonNull)
                                                .map(info -> {
                                                    AiInstitutionCommissionVo.AIInstitutionCommissionDetail detail =
                                                            new AiInstitutionCommissionVo.AIInstitutionCommissionDetail();
                                                    detail.setCourse(StringUtils.isNotBlank(info.getCourse()) ? info.getCourse() : "");
                                                    detail.setLevelName(StringUtils.isNotBlank(info.getCustomName()) ? info.getCustomName() : "");
                                                    detail.setCommission(Objects.nonNull(info.getCommission()) ? info.getCommission() : BigDecimal.ZERO);
                                                    detail.setCommissionUnit(StringUtils.isNotBlank(info.getCommissionUnit()) ? info.getCommissionUnit() : "");
                                                    return detail;
                                                })
                                                .collect(Collectors.toList());

                                plan.setCommissionList(commissionList);
                                return plan;
                            })
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());

                    aiVo.setPlanList(planList);
                    return aiVo;
                })
                .collect(Collectors.toList());
        return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 拖拽
     *
     * @param start
     * @param end
     */
    @Override
    public void movingOrder(Integer start, Integer end) {
        LambdaQueryWrapper<AgentCommissionPlan> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (end > start) {
            lambdaQueryWrapper.between(AgentCommissionPlan::getViewOrder, start, end).orderByDesc(AgentCommissionPlan::getViewOrder);
        } else {
            lambdaQueryWrapper.between(AgentCommissionPlan::getViewOrder, end, start).orderByDesc(AgentCommissionPlan::getViewOrder);

        }
        List<AgentCommissionPlan> agentCommissionPlans = list(lambdaQueryWrapper);

        //从下上移 列表倒序排序
        List<AgentCommissionPlan> updateList = Lists.newArrayList();
        if (end > start) {
            int finalEnd = end;
            List<AgentCommissionPlan> sortedList = Lists.newArrayList();
            AgentCommissionPlan policy = agentCommissionPlans.get(agentCommissionPlans.size() - 1);
            sortedList.add(policy);
            agentCommissionPlans.remove(agentCommissionPlans.size() - 1);
            sortedList.addAll(agentCommissionPlans);
            for (AgentCommissionPlan agentCommissionPlan : sortedList) {
                agentCommissionPlan.setViewOrder(finalEnd);
                finalEnd--;
            }
            updateList.addAll(sortedList);
        } else {
            int finalStart = start;
            List<AgentCommissionPlan> sortedList = Lists.newArrayList();
            AgentCommissionPlan policy = agentCommissionPlans.get(0);
            agentCommissionPlans.remove(0);
            sortedList.addAll(agentCommissionPlans);
            sortedList.add(policy);
            for (AgentCommissionPlan agentCommissionPlan : sortedList) {
                agentCommissionPlan.setViewOrder(finalStart);
                finalStart--;
            }
            updateList.addAll(sortedList);
        }

        if (GeneralTool.isNotEmpty(updateList)) {
            boolean batch = updateBatchById(updateList);
            if (!batch) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
    }
}
