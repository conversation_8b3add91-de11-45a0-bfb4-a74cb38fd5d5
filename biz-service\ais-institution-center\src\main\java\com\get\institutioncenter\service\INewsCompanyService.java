package com.get.institutioncenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.entity.NewsCompany;
import com.get.institutioncenter.dto.NewsCompanyDto;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/11/19
 * @TIME: 10:33
 * @Description:
 **/
public interface INewsCompanyService extends BaseService<NewsCompany> {

    /**
     * @return java.util.List<com.get.institutioncenter.vo.CompanyTreeVo>
     * @Description: 新闻和公司的关系（数据回显）
     * @Param [contactId]
     * <AUTHOR>
     **/
    List<CompanyTreeVo> getNewCompanyRelation(Long newId);

    /**
     * 安全配置
     *
     * @param newsCompanyDtos
     * @
     */
    void editNewsCompany(List<NewsCompanyDto> newsCompanyDtos);


    /**
     * 根据新闻ids获取公司ids
     *
     * @return
     */
    Map<Long, Set<Long>> getCompanyIdsByNewIds(Set<Long> ids);
}
