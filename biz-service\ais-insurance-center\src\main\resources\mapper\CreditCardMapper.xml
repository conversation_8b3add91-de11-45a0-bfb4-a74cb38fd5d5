<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.insurancecenter.mapper.CreditCardMapper">

    <select id="selectCreateCardPage" resultType="com.get.insurancecenter.vo.card.CreateCardPageVo">
        select c.*,
        b.bank_name as bankName
        from m_credit_card c
        left join ais_finance_center.u_bank b on c.fk_bank_id = b.id
        <where>
            <trim prefixOverrides="and |or">
                <if test="param.cardTypeKey != null and param.cardTypeKey != ''">
                    and c.card_type_key =
                    #{param.cardTypeKey}
                </if>
                <if test="param.isActive != null">
                    and c.is_active =
                    #{param.isActive}
                </if>
            </trim>
        </where>
        order by c.view_order desc
    </select>


    <select id="selectBankList" resultType="com.get.insurancecenter.vo.card.BankVo">
        select id                   as bankId,
               bank_name            as bankName,
               fk_currency_type_num as currencyTypeNum
        from ais_finance_center.u_bank
        order by id desc
    </select>

    <select id="selectBaseInfoById" resultType="com.get.insurancecenter.entity.CreditCard">
        select c.*,
               b.bank_name as bankName
        from app_insurance_center.m_credit_card c
                 left join ais_finance_center.u_bank b on c.fk_bank_id = b.id
        where c.id = #{id}
    </select>
</mapper>
