<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hti-java-ais</artifactId>
        <groupId>com.get</groupId>
        <version>1.0.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>biz-service-ap</artifactId>
    <name>${project.artifactId}</name>
    <version>1.0.RELEASE</version>
    <packaging>pom</packaging>
    <description>业务微服务AP适配器集合</description>

    <modules>
<!--        <module>institution-center-ap</module>-->
<!--        <module>sale-center-ap</module>-->
<!--        <module>finance-center-ap</module>-->
<!--        <module>office-center-ap</module>-->
<!--        <module>voting-center-ap</module>-->
<!--        <module>resume-center-ap</module>-->
<!--        <module>registration-center-ap</module>-->
<!--        <module>report-center-ap</module>-->
<!--        <module>reminder-center-ap</module>-->
<!--        <module>exam-center-ap</module>-->
<!--        <module>mps-center-ap</module>-->


        <module>ais-sale-center-ap</module>
        <module>ais-institution-center-ap</module>
        <module>ais-finance-center-ap</module>
        <module>ais-office-center-ap</module>
        <module>ais-voting-center-ap</module>
        <module>ais-resume-center-ap</module>
        <module>ais-registration-center-ap</module>
        <module>ais-report-center-ap</module>
        <module>ais-reminder-center-ap</module>
        <module>ais-exam-center-ap</module>
        <module>ais-mps-center-ap</module>
        <module>ais-partner-center-ap</module>
        <module>ais-mail-center-ap</module>
        <module>ais-platform-center-ap</module>
        <module>ais-pmp-center-ap</module>
        <module>ais-rocketmq-center-ap</module>
        <module>ais-insurance-center-ap</module>
        <module>ais-middle-center-ap</module>
    </modules>
    <dependencies>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-mybatis</artifactId>
            <version>${get.project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-models</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-auto</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                    <finalName>${project.name}</finalName>
                </configuration>
            </plugin>

            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>1.2.2</version>
                <configuration>
                    <skipDockerBuild>true</skipDockerBuild>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>