package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 修改付款单代理VO
 *
 * <AUTHOR>
 * @date 2022/12/21 16:24
 */
@Data
public class PaymentFormAgentUpdateDto {

    @NotNull(message = "付款单Id不能为空")
    @ApiModelProperty(value = "付款单Id")
    private Long fkPaymentFormId;

    @NotNull(message = "代理id不能为空")
    @ApiModelProperty(value = "代理id")
    private Long agentId;

    @NotNull(message = "目标银行账户id不能为空")
    @ApiModelProperty(value = "目标银行账户id")
    private Long targetBankAccountId;


}
