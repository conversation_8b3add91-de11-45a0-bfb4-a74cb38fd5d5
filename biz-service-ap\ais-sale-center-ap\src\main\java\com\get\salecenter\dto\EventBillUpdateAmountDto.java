package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @author: Hardy
 * @create: 2023/12/20 10:41
 * @verison: 1.0
 * @description:
 */
@Data
public class EventBillUpdateAmountDto extends BaseVoEntity  {

    /**
     * invoice币种
     */
    @NotBlank(message = "发起invoice币种不能为空！", groups = {Update.class})
    @ApiModelProperty(value = "invoice币种")
    @Column(name = "fk_currency_type_num_invoice")
    private String fkCurrencyTypeNumInvoice;

    /**
     * invoice金额
     */
    @ApiModelProperty(value = "invoice金额")
    @Column(name = "invoice_amount")
    private BigDecimal invoiceAmount;

    /**
     * 活动费币种
     */
    @NotBlank(message = "活动费币种不能为空！", groups = {Update.class})
    @ApiModelProperty(value = "活动费币种")
    private String fkCurrencyTypeNumEvent;

    /**
     * 活动费金额
     */
    @NotNull(message = "活动费金额不能为空！", groups = {Update.class})
    @ApiModelProperty(value = "活动费金额")
    private BigDecimal eventAmount;

   

}
