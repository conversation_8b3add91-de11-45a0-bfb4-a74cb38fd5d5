package com.get.pmpcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.pmpcenter.dto.common.LogDto;
import com.get.pmpcenter.dto.institution.SaveInstitutionLabelDto;
import com.get.pmpcenter.entity.InstitutionProviderCommissionPlanInstitution;
import com.get.pmpcenter.vo.institution.InstitutionLabelVo;
import com.get.pmpcenter.vo.institution.ProviderCommissionListVo;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:17
 * @Version 1.0
 */
public interface InstitutionProviderCommissionPlanInstitutionService extends IService<InstitutionProviderCommissionPlanInstitution> {

    /**
     * 保存佣金方案关联学校
     *
     * @param institutionIds
     * @param providerCommissionPlanId
     */
    List<LogDto> saveProviderCommissionPlanInstitution(List<Long> institutionIds, Long providerCommissionPlanId, Long contractId);


    /**
     * 获取佣金方案下学校列表
     *
     * @param type
     * @param id
     * @return1:合同 2:学校提供商佣金方案
     */
    ProviderCommissionListVo getPlanInstitutionList(Integer type, Long id);


    /**
     * 获取学校标签
     *
     * @param institutionId
     * @return
     */
    InstitutionLabelVo getInstitutionLabel(Long institutionId);

    /**
     * 保存学校标签
     *
     * @param institutionLabelDto
     */
    InstitutionLabelVo saveInstitutionLabel(SaveInstitutionLabelDto institutionLabelDto);
}
