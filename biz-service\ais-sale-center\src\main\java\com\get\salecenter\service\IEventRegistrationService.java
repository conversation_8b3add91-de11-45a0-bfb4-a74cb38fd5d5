package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.core.mybatis.service.GetService;
import com.get.core.mybatis.utils.ValidList;
import com.get.institutioncenter.dto.InstitutionProviderDto;
import com.get.institutioncenter.vo.InstitutionProviderVo;
import com.get.salecenter.entity.EventRegistration;
import com.get.salecenter.dto.EventRegistrationDto;
import com.get.salecenter.dto.EventRegistrationStatusDto;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/9/13 16:27
 * @verison: 1.0
 * @description:
 */
public interface IEventRegistrationService extends GetService<EventRegistration> {

    /**
     * 提供商报名名册
     *
     * @param data
     * @param page
     * @return
     */
    List<InstitutionProviderVo> getEventRegistrations(EventRegistrationDto data, Page page);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 编辑备注
     * @param id
     * @param remark
     * @return
     */
    ResponseBo editRemark(Long id,String remark);

    /**
     * 分配学校
     *
     * @param eventRegistrationDtos
     */
    void editEventRegistration(ValidList<EventRegistrationDto> eventRegistrationDtos);

    /**
     * 绑定关系的提供商列表
     *
     * @param page
     * @return
     */
    ResponseBo<InstitutionProviderVo> getEventRegistrationProviders(SearchBean<InstitutionProviderDto> page);

    /**
     * 批量修改活动名册状态
     * @param eventRegistrationStatusDto
     */
    void editEventRegistrationStatus(EventRegistrationStatusDto eventRegistrationStatusDto);

}
