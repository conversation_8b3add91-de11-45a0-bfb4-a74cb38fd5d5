package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/12/18
 * @TIME: 16:17
 * @Description:
 **/
@Data
public class InstitutionCoursePathwayDto extends BaseVoEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 课程Id
     */
    @ApiModelProperty(value = "课程Id")
    @NotNull(message = "课程Id不能为空", groups = {Add.class, Update.class})
    private Long fkInstitutionCourseId;
    /**
     * 桥梁课程Id
     */
    @ApiModelProperty(value = "桥梁课程Id")
    @NotNull(message = "桥梁课程Id不能为空", groups = {Add.class, Update.class})
    private Long fkInstitutionCourseIdPathway;
    /**
     * 桥梁课程Id数组
     */
    @ApiModelProperty(value = "桥梁课程Id数组，绑定参数")
    private List<Long> fkCourseIdPathways;
    /**
     * 绑定课程Id数组
     *
     * @Date 17:55 2021/7/22
     * <AUTHOR>
     */
    @ApiModelProperty(value = "绑定课程Id数组，绑定参数")
    private List<Long> fkCourseIds;
    /**
     * 升读学术要求
     */
    @ApiModelProperty(value = "升读学术要求")
    private String reqAcademic1;
    /**
     * 升读英语要求
     */
    @ApiModelProperty(value = "升读英语要求")
    private String reqEng1;
    /**
     * 升读特定科目要求
     */
    @ApiModelProperty(value = "升读特定科目要求")
    private String reqParticularSubject;
    /**
     * 升读大二学术要求
     */
    @ApiModelProperty(value = "升读大二学术要求")
    private String reqAcademic2;
    /**
     * 升读大二英语要求
     */
    @ApiModelProperty(value = "升读大二英语要求")
    private String reqEng2;
}
