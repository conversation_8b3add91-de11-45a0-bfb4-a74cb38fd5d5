package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.salecenter.vo.StudentReceivableAndPaySumVo;
import com.get.salecenter.vo.StudentReceivableAndPaySummaryVo;
import com.get.salecenter.dto.StudentReceivableAndPaySumDto;
import com.get.salecenter.dto.query.StudentReceivableAndPaySumQueryDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/11/22 10:26
 * @verison: 1.0
 * @description:
 */
public interface IStudentReceivableAndPaySumService {

    /**
     * 学生应收应付汇总统计
     *
     * @param studentReceivableAndPaySumVo
     * @param page
     * @return
     */
    List<StudentReceivableAndPaySumVo> datas(StudentReceivableAndPaySumQueryDto studentReceivableAndPaySumVo, Page page);

    /**
     * 导出excel
     *
     * @param response
     * @param studentReceivableAndPaySumVo
     */
    void exportStudentReceivableAndPaySumExcel(HttpServletResponse response, StudentReceivableAndPaySumQueryDto studentReceivableAndPaySumVo);

    /**
     * 获取学生收付款信息(拷贝学生应收应付汇总统计列表逻辑,学生详情专用接口)
     *
     * @Date 9:48 2022/6/28
     * <AUTHOR>
     */
    List<StudentReceivableAndPaySumVo> getStudentReceivableAndPaySum(StudentReceivableAndPaySumQueryDto studentReceivableAndPaySumVo);

    StudentReceivableAndPaySummaryVo getReceivableAndPayPaginationInfo(StudentReceivableAndPaySumDto studentReceivableAndPaySumDto);

}
