package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-09
 */
@Data
@TableName("r_event_bill_receivable_plan")
@ApiModel(value="EventBillReceivablePlan对象", description="")
public class EventBillReceivablePlan extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动Id")
    private Long fkEventBillId;

    @ApiModelProperty(value = "应收计划Id")
    private Long fkReceivablePlanId;
}
