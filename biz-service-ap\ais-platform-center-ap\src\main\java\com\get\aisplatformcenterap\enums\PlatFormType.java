package com.get.aisplatformcenterap.enums;

import com.get.aisplatformcenterap.vo.PlatFormTypeVo;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum PlatFormType {
    AIS(1L, "AIS", "AIS"),
    PARTNER(2L, "华通伙伴", "PARTNER"),
//    ,AOXIAOBAO(3, "澳小宝")
    ;

    private final Long id;
    private final String name;
    private final String code;

    PlatFormType(Long id, String name, String code) {
        this.id = id;
        this.name = name;
        this.code = code;
    }

    public Long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public static String getNameById(Long id) {
        for (PlatFormType itemType : PlatFormType.values()) {
            if (itemType.getId().equals(id)) {
                return itemType.getName();
            }
        }
        return null;
    }

    public static String getCodeById(Long id) {
        for (PlatFormType itemType : PlatFormType.values()) {
            if (itemType.getId().equals(id)) {
                return itemType.getCode();
            }
        }
        return null;
    }

    /**
     * 获取所有枚举值作为选择列表
     */
    public static List<PlatFormTypeVo> asSelectList() {
        return Arrays.stream(values())
                .map(type ->{
                    PlatFormTypeVo selectEntity = new PlatFormTypeVo();
                    selectEntity.setId(type.getId());
                    selectEntity.setName(type.getName());
                    selectEntity.setCode(type.getCode());
                    return selectEntity;
                })
                .collect(Collectors.toList());
    }


}
