package com.get.insurancecenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.insurancecenter.dto.card.SaveStatementActualPayDto;
import com.get.insurancecenter.dto.card.SaveStatementDto;
import com.get.insurancecenter.dto.card.TradeRecordDto;
import com.get.insurancecenter.entity.CreditCard;
import com.get.insurancecenter.entity.CreditCardStatement;
import com.get.insurancecenter.enums.BusinessTypeEnum;
import com.get.insurancecenter.enums.RelationTargetEnum;
import com.get.insurancecenter.enums.StatementStatusEnum;
import com.get.insurancecenter.mapper.CreditCardMapper;
import com.get.insurancecenter.mapper.CreditCardStatementMapper;
import com.get.insurancecenter.service.CreditCardStatementService;
import com.get.insurancecenter.utils.ExchangeRateUtils;
import com.get.insurancecenter.utils.OrderNoUtil;
import com.get.insurancecenter.vo.card.TradeRecordVo;
import com.get.insurancecenter.vo.commission.RateDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class CreditCardStatementServiceImpl extends ServiceImpl<CreditCardStatementMapper, CreditCardStatement> implements CreditCardStatementService {

    @Autowired
    private CreditCardStatementMapper creditCardStatementMapper;
    @Autowired
    private CreditCardMapper creditCardMapper;
    @Autowired
    private ExchangeRateUtils rateUtils;

    @Override
    public List<TradeRecordVo> tradeRecordPage(TradeRecordDto params, Page page) {
        //type-1:交易记录-只包含支出和还款
        //type-2:详细列表-信用卡详情的流水记录,包含完整的4种交易类型
        IPage<TradeRecordVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<TradeRecordVo> result = creditCardStatementMapper.selectTradeRecordPage(pages, params);
        result.stream().forEach(tradeRecordVo -> {
            //交易对象
            RelationTargetEnum targetEnum = RelationTargetEnum.getEnumByCode(tradeRecordVo.getRelationTargetKey());
            if (Objects.nonNull(targetEnum)) {
                tradeRecordVo.setRelationTargetName(targetEnum.getMsg());
            }
            //差额
            tradeRecordVo.setDifferenceAmount(tradeRecordVo.getAmountRmbFormula());
            if (Objects.nonNull(tradeRecordVo.getAmountRmb()) && Objects.nonNull(tradeRecordVo.getAmountRmbFormula())) {
                tradeRecordVo.setDifferenceAmount(tradeRecordVo.getAmountRmbFormula().subtract(tradeRecordVo.getAmountRmb()));
            }
        });
        int totalCount = (int) pages.getTotal();
        Integer showCount = page.getShowCount();
        page.setTotalPage(page.getShowCount() != null && showCount > 0 ? totalCount % showCount == 0 ? totalCount / showCount : totalCount / showCount + 1 : 0);
        page.setTotalResult(totalCount);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveStatement(SaveStatementDto saveStatementDto) {
        CreditCard creditCard = creditCardMapper.selectById(saveStatementDto.getCreditCardId());
        if (Objects.isNull(creditCard)) {
            log.error("保存失败,未找到该信用卡,id:{}", saveStatementDto.getCreditCardId());
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_NOT_FOUND", "该信用卡不存在"));
        }
        //根据输入的币种计算相应的汇率
        BigDecimal rate = BigDecimal.ONE;
        if (!Objects.equals(creditCard.getFkCurrencyTypeNum(), saveStatementDto.getCurrencyTypeNum())) {
            RateDetail rateDetail = rateUtils.getRateDetail(saveStatementDto.getCurrencyTypeNum(), creditCard.getFkCurrencyTypeNum());
            if (Objects.isNull(rateDetail) || Objects.isNull(rateDetail.getRate())) {
                log.error("获取汇率异常,汇率结果为空:{},原币种:{},目标币种:{}", JSONObject.toJSONString(rateDetail), creditCard.getFkCurrencyTypeNum(), saveStatementDto.getCurrencyTypeNum());
                throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "INSURANCE_EXCHANGE_RATE_FAIL", "获取汇率失败"));
            }
            rate = rateDetail.getRate();
        }
        //计算信用卡相同币种的对应的金额-变动金额
        BigDecimal changeAmount = saveStatementDto.getAmount().multiply(rate).setScale(2, RoundingMode.HALF_UP);
        //计算人名币金额
        RateDetail cnyRateDetail = rateUtils.getRateDetail(saveStatementDto.getCurrencyTypeNum(), "CNY");
        if (Objects.isNull(cnyRateDetail) || Objects.isNull(cnyRateDetail.getRate())) {
            log.error("获取汇率异常,汇率结果为空:{},原币种:{},目标币种:{}", JSONObject.toJSONString(cnyRateDetail), creditCard.getFkCurrencyTypeNum(), saveStatementDto.getCurrencyTypeNum());
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "INSURANCE_EXCHANGE_RATE_FAIL", "获取汇率失败"));
        }
        BigDecimal cnyAmount = saveStatementDto.getAmount().multiply(cnyRateDetail.getRate()).setScale(2, RoundingMode.HALF_UP);
        log.info("计算信用卡相同币种对应的金额,变动金额:{}", changeAmount);
        CreditCardStatement statement = CreditCardStatement.builder()
                .fkCreditCardId(saveStatementDto.getCreditCardId())
                .businessType(saveStatementDto.getBusinessType())
                .relationTargetKey(saveStatementDto.getRelationTargetKey())
                .relationTargetId(saveStatementDto.getOrderId())
                .fkCurrencyTypeNum(saveStatementDto.getCurrencyTypeNum())
                .amount(saveStatementDto.getAmount())
                .status(saveStatementDto.getStatus())
                .num(OrderNoUtil.generateTradeNo())
                .amountRmbFormula(cnyAmount)
                .build();
        statement.setGmtCreate(new Date());
        statement.setGmtModified(new Date());
        statement.setGmtCreateUser(SecureUtil.getLoginId());
        statement.setGmtModifiedUser(SecureUtil.getLoginId());

        //2还款和3校正金额没有状态区分
        //2还款-恢复信用卡相应的额度
        if (saveStatementDto.getBusinessType().equals(BusinessTypeEnum.REPAYMENT.getCode())) {
            statement.setRelationTargetKey(null);
            statement.setRelationTargetId(null);
            statement.setStatus(StatementStatusEnum.SUCCESS.getCode());
            //恢复信用卡相应的额度
            creditCard.setCurrentAmount(creditCard.getCurrentAmount().add(changeAmount));
        }
        //1支出-扣除信用卡相应的额度
        if (saveStatementDto.getBusinessType().equals(BusinessTypeEnum.PAY.getCode())
                && saveStatementDto.getStatus().equals(StatementStatusEnum.SUCCESS.getCode())) {
            creditCard.setCurrentAmount(creditCard.getCurrentAmount().subtract(changeAmount));
        }
        //3校正金额：-直接重置信用卡的额度
        if (saveStatementDto.getBusinessType().equals(BusinessTypeEnum.ADJUST.getCode())) {
            statement.setRelationTargetKey(null);
            statement.setRelationTargetId(null);
            statement.setStatus(StatementStatusEnum.SUCCESS.getCode());
            creditCard.setCurrentAmount(changeAmount);
        }
        //4退款-恢复信用卡相应的额度
        if (saveStatementDto.getBusinessType().equals(BusinessTypeEnum.REFUND.getCode())
                && saveStatementDto.getStatus().equals(StatementStatusEnum.SUCCESS.getCode())) {
            creditCard.setCurrentAmount(creditCard.getCurrentAmount().add(changeAmount));
        }
        //判断当前余额不能大于额度
        if (creditCard.getCurrentAmount().compareTo(creditCard.getQuotaAmount()) > 0) {
            log.error("保存失败,当前余额不能大于额度,当前余额:{},额度:{}", creditCard.getCurrentAmount(), creditCard.getQuotaAmount());
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "INSURANCE_CREDIT_CARD_QUOTA_ERROR", "当前余额不能大于信用卡额度"));
        }

        //保存记录
        this.save(statement);
        //修改信用卡额度
        creditCard.setGmtModified(new Date());
        creditCard.setGmtModifiedUser(SecureUtil.getLoginId());
        creditCardMapper.updateById(creditCard);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveStatementActualPay(SaveStatementActualPayDto actualPayDto) {
        CreditCardStatement creditCardStatement = creditCardStatementMapper.selectById(actualPayDto.getId());
        if (Objects.nonNull(creditCardStatement)) {
            creditCardStatement.setAmountRmb(actualPayDto.getAmountRmb());
            creditCardStatement.setExchangeRateRmb(actualPayDto.getExchangeRateRmb());
            creditCardStatement.setGmtModified(new Date());
            creditCardStatement.setGmtModifiedUser(SecureUtil.getLoginId());
            creditCardStatementMapper.updateById(creditCardStatement);
        }
    }
}
