package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/1/13
 * @TIME: 15:47
 * @Description:
 **/
@Data
public class AccommodationSummaryDto extends BaseVoEntity {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 学生名称编号
     */
    @ApiModelProperty(value = "学生名称编号")
    private String studentName;

    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    private String agentName;

    /**
     * bd名称
     */
    @ApiModelProperty(value = "bd名称")
    private String staffName;

    /**
     * 项目成员名称
     */
    @ApiModelProperty(value = "项目成员名称")
    private String memberName;

    /**
     * 业务渠道
     */
    @ApiModelProperty(value = "业务渠道")
    private Long fkBusinessChannelId;

    /**
     * 业务提供商Id
     */
    @ApiModelProperty(value = "业务提供商Id")
    private Long fkBusinessProviderId;


    /**
     * 状态：0关闭/1打开
     */
    @ApiModelProperty(value = "状态：0关闭/1打开")
    private Integer status;

    /**
     * 前往国家Id
     */
    @ApiModelProperty(value = "前往国家Id")
    private Long fkAreaCountryId;

    /**
     * 前往州省Id
     */
    @ApiModelProperty(value = "前往州省Id")
    private Long fkAreaStateId;

    /**
     * 前往城市Id
     */
    @ApiModelProperty(value = "前往城市Id")
    private Long fkAreaCityId;

    /**
     * 公寓名称
     */
    @ApiModelProperty(value = "公寓名称")
    private String apartmentName;
    /**
     * 入住日期开始时间
     */
    @ApiModelProperty(value = "入住日期开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date checkInDateStart;
    /**
     * 入住日期结束时间
     */
    @ApiModelProperty(value = "入住日期结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date checkInDateEnd;
    /**
     * 退房日期开始时间
     */
    @ApiModelProperty(value = "退房日期开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date checkOutDateStart;
    /**
     * 退房日期结束时间
     */
    @ApiModelProperty(value = "退房日期结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date checkOutDateEnd;

    /**
     * 住宿批量生成应收应付数组
     */
    @ApiModelProperty(value = "住宿批量生成应收应付数组")
    private List<Long> accommodationIds;


    /**
     * 佣金币种
     */
    @ApiModelProperty(value = "佣金币种")
    private String fkCurrencyTypeNumCommission;

    /**
     * 收取佣金比例%
     */
    @ApiModelProperty(value = "收取佣金比例%")
    private BigDecimal commissionRateReceivable;

    /**
     * 支付佣金比例%（代理）
     */
    @ApiModelProperty(value = "支付佣金比例%（代理）")
    private BigDecimal commissionRatePayable;

    /**
     * 固定收取佣金金额
     */
    @ApiModelProperty(value = "固定收取佣金金额")
    private BigDecimal fixedAmountReceivable;

    /**
     * 固定支付佣金金额（代理）
     */
    @ApiModelProperty(value = "固定支付佣金金额（代理）")
    private BigDecimal fixedAmountPayable;

    private List<Long> staffFollowerIds;

    /**
     * 应收状态
     */
    @ApiModelProperty(value = "应收状态：0/未收 1/部分已收 2/已收齐")
    private Integer arStatus;

    /**
     * 应付状态
     */
    @ApiModelProperty(value = "应付状态：0/未付 1/部分已付 2/已付清")
    private Integer apStatus;

    


}
