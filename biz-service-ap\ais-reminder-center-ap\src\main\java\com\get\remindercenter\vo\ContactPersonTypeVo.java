package com.get.remindercenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import com.get.remindercenter.entity.RemindContactPersonType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Sea
 * @create: 2020/7/22 16:01
 * @verison: 1.0
 * @description:
 */
@Data
public class ContactPersonTypeVo extends BaseVoEntity {

    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    private String typeName;
    /**
     * 类型Key，枚举类型Key
     */
    @ApiModelProperty(value = "类型Key，枚举类型Key")
    private String typeKey;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    private Integer viewOrder;
}
