package com.get.pmpcenter.feign;

import com.get.common.constant.AppCenterConstant;
import com.get.core.tool.api.Result;
import com.get.permissioncenter.vo.workbench.WorkbenchApprovalVo;
import com.get.pmpcenter.dto.institution.UnbindProviderInstitutionDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/3/24
 * @Version 1.0
 * @apiNote: pmp-feign
 */
@FeignClient(value = AppCenterConstant.APPLICATION_PMP_CENTER)
public interface IPmpCenterClient {

    @PostMapping("/feign/unbindProviderInstitution")
    Result<Boolean> unbindProviderInstitution(@RequestBody @Valid UnbindProviderInstitutionDto unbindProviderInstitutionDto);

    @GetMapping("/feign/getWorkbenchApprovalList")
    Result<List<WorkbenchApprovalVo>> getWorkbenchApprovalList(@RequestParam("staffId") Long staffId,
                                                               @RequestParam("approvalType") String approvalType,
                                                               @RequestParam("approvalStatus") Integer approvalStatus,
                                                               @RequestParam("loginId") String loginId);
}
