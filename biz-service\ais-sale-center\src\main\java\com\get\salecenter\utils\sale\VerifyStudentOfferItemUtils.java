package com.get.salecenter.utils.sale;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.cache.utils.CacheUtil;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.StudentOfferItemMapper;
import com.get.salecenter.entity.StudentOfferItem;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.get.common.cache.CacheNames.STAFF_FOLLOWER_IDS_CACHE;

@Component
public class VerifyStudentOfferItemUtils {

    @Resource
    private StudentOfferItemMapper offerItemMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    /**
     * 学习计划修改校验
     * @param offerItem
     */
    public void  verifyOfferItemUpdate(StudentOfferItem offerItem) {
        StudentOfferItem studentOfferItem = offerItemMapper.selectOne(Wrappers.<StudentOfferItem>lambdaQuery().eq(StudentOfferItem::getId,offerItem.getId()));
        //非New App状态，课程不能为空。分两种情况,一种是修改课程，另一种
        if (GeneralTool.isNotEmpty(offerItem.getFkStudentOfferItemStepId()) && !offerItem.getFkStudentOfferItemStepId().equals(1L)
                && (GeneralTool.isEmpty(studentOfferItem.getFkInstitutionCourseId()) || (GeneralTool.isEmpty(studentOfferItem.getOldCourseCustomName()) && studentOfferItem.getFkInstitutionCourseId().equals(-1L)))
                && (GeneralTool.isEmpty(offerItem.getFkInstitutionCourseId()) || (GeneralTool.isEmpty(offerItem.getOldCourseCustomName()) && offerItem.getFkInstitutionCourseId().equals(-1L)))){
            if (GeneralTool.isNotEmpty(studentOfferItem.getFkParentStudentOfferItemId()) && studentOfferItem.getIsFollow()){
                throw new GetServiceException(LocaleMessageUtils.getMessage("not_new_app_item_follow_status"));
            }else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("not_new_app_status"));
            }
        }
    }

    /**
     * 学习计划新增校验
     * @param studentOfferItem
     */
    public void verifyOfferItemInsert(StudentOfferItem studentOfferItem) {
        //非New App状态，课程不能为空
        if (!studentOfferItem.getFkStudentOfferItemStepId().equals(1L) && (GeneralTool.isEmpty(studentOfferItem.getFkInstitutionCourseId()) || (GeneralTool.isEmpty(studentOfferItem.getOldCourseCustomName()) && studentOfferItem.getFkInstitutionCourseId().equals(-1L)))){

            throw new GetServiceException(LocaleMessageUtils.getMessage("not_new_app_status"));
        }
    }


    /**
     * 获取下属
     * @param staffId
     * @return
     */
    public List<Long> getStaffFollowerIds(Long staffId) {
        List<Long> staffFollowerIds = Lists.newArrayList();
        List<Long> followerIds = CacheUtil.get(
                STAFF_FOLLOWER_IDS_CACHE,
                "staffId:",
                staffId,
                ()->permissionCenterClient.getStaffFollowerIds(staffId).getData()
        );
        if (GeneralTool.isNotEmpty(followerIds)) {
            staffFollowerIds.addAll(followerIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        }
        staffFollowerIds.add(staffId);
        return staffFollowerIds;
    }

}
