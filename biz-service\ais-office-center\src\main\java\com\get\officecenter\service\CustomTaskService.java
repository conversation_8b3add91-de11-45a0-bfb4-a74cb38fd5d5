package com.get.officecenter.service;

import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.officecenter.entity.Task;
import com.get.officecenter.vo.CustomTaskVo;
import com.get.officecenter.entity.Comment;
import com.get.officecenter.dto.CreateTaskAndTaskItemDto;
import com.get.officecenter.dto.CustomTaskSearchDto;
import com.get.officecenter.dto.CustomTaskDto;
import com.get.permissioncenter.vo.DepartmentAndStaffVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Author:cream
 * @Date: 2023/5/30  9:41
 */
public interface CustomTaskService {


    /**
     * 新增任务
     * @param customTaskDto
     * @return
     */
    SaveResponseBo addTask(CustomTaskDto customTaskDto);


    /**
     * 获取任务详情
     * @return
     * @param taskId
     */
    ResponseBo<CustomTaskVo> getTaskInfoById(Long taskId);

    /**
     * 获取任务详情
     * @return
     * @param taskId
     */
    CustomTaskVo getTaskById(Long taskId);

    /**
     * 任务列表
     * @param customTaskSearchDto
     * @param page
     * @return
     */
    ListResponseBo<CustomTaskVo> taskList(CustomTaskSearchDto customTaskSearchDto, Page page);

    /**
     * 更新任务
     * @param customTaskDto
     * @return
     */
    SaveResponseBo updateTask(CustomTaskDto customTaskDto);


    /**
     * 添加任务评论
     * @param taskId
     * @param comment
     * @return
     */
    SaveResponseBo addTaskComment(Long taskId,String comment);


    /**
     * 获取任务评论列表
     * @param taskId
     * @param page
     * @return
     */
    ListResponseBo<Comment> getTaskCommentList(Long taskId,Page page);


    /**
     * 删除任务评论
     * @param commentId
     * @return
     */
    DeleteResponseBo removeTaskComment(Long commentId);

    /**
     * 编辑任务评论
     * @param taskId
     * @param commentId
     * @param comment
     * @return
     */
    SaveResponseBo updateTaskComment(Long taskId,Long commentId,String comment);


    /**
     * 任务委派
     * @param customTaskDto
     * @return
     */
    SaveResponseBo taskDelegation(CustomTaskDto customTaskDto);


    /**
     * 任务设置完成
     * @param taskId
     * @return
     */
    SaveResponseBo taskCompletion(Long taskId);


    /**
     * 任务删除
     * @param taskId
     * @return
     */
    DeleteResponseBo taskDelete(Long taskId);

    /**
     * 新增主任务和子任务
     *
     * @param createTaskAndTaskItemDto
     * @return
     */
    Boolean addTaskAndTaskItem(CreateTaskAndTaskItemDto createTaskAndTaskItemDto);


    ListResponseBo<DepartmentAndStaffVo> getTaskDepartment(Integer type);

    void exportTaskList(HttpServletResponse response, CustomTaskSearchDto data);

    List<Task> getTaskByEndTime();
}
