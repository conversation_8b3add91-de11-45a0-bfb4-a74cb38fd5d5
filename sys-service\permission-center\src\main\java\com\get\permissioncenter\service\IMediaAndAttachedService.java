package com.get.permissioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.service.GetService;
import com.get.filecenter.dto.FileDto;
import com.get.permissioncenter.dto.MediaAndAttachedDto;
import com.get.permissioncenter.vo.MediaAndAttachedVo;
import com.get.permissioncenter.entity.PermissionMediaAndAttached;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/7/14
 * @TIME: 18:20
 * @Description:
 **/
public interface IMediaAndAttachedService extends GetService<PermissionMediaAndAttached> {

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 上传文件
     *
     * @param multipartFiles
     * @return
     */
    List<FileDto> upload(MultipartFile[] multipartFiles);

    /**
     * 上传附件
     *
     * @param multipartFiles
     * @return
     */
    List<FileDto> uploadAttached(MultipartFile[] multipartFiles);

    /**
     * 上移下移
     *
     * @param mediaAttachedVos
     * @return
     */
    void movingOrder(List<MediaAndAttachedDto> mediaAttachedVos);

    /**
     * 保存上传的文件
     *
     * @param mediaAndAttachedDto
     * @return
     */
    MediaAndAttachedVo addMediaAndAttached(MediaAndAttachedDto mediaAndAttachedDto);


    /**
     * 通过附件GUID获取DTO
     *
     * @param attachedVo
     * @return
     */
    List<MediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo);


    /**
     * 通过附件GUID分页获取DTO
     *
     * @param attachedVo
     * @return
     */
    List<MediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo, Page page);


    /**
     * 修改媒体附件表的fk_table_id
     *
     * @param mediaId
     * @param fkTableId
     */
    void updateTableId(Long mediaId, Long fkTableId);


    /**
     * @return void
     * @Description: 删除
     * @Param [attachedVo]
     * <AUTHOR>
     */
    void delete(MediaAndAttachedDto attachedVo);


    /**
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Description: 获取员工附件类型
     * @Param []
     * <AUTHOR>
     */
    List<Map<String, Object>> findStaffMediaType();

    /**
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Description: 获取人事事件附件类型
     * @Param []
     * <AUTHOR>
     */
    List<Map<String, Object>> findHrEventMediaType();

    /**
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Description: 获取人事事件附件类型
     * @Param []
     * <AUTHOR>
     */
    List<Map<String, Object>> findContractMediaType();


    List<PermissionMediaAndAttached> getMediaAndAttachedByIaeCrm(List<String> fkTableNames);

    Boolean updateMediaAndAttachedById(PermissionMediaAndAttached permissionMediaAndAttached);
}
