package com.get.salecenter.vo;

import com.get.salecenter.dto.DataCollectionQuestionDto;
import com.get.salecenter.entity.convention.ConventionMediaAndAttached;
import com.get.salecenter.vo.convention.ConventionMediaAndAttachedVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 学校机构资料Vo
 */
@Data
public class DataCollectionFormInfoVo {

    @ApiModelProperty(value = "简介")
    private String profile;

    @ApiModelProperty(value = "考题列表")
    private List<DataCollectionQuestionDto> dataCollectionQuestionDtoList;

    @ApiModelProperty(value = "机构附件")
    List<ConventionMediaAndAttachedVo> mediaAndAttachedList;

}
