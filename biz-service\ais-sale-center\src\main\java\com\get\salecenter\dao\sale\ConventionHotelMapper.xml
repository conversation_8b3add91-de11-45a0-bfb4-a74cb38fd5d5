<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ConventionHotelMapper">


  <select id="getHotelList" resultType="string">
    select
     hotel
    from
     m_convention_hotel
    where
     fk_convention_id = #{conventionId}
    GROUP BY
     hotel
  </select>

  <select id="getRoomTypeList" parameterType="java.lang.String" resultType="string">
    select
     room_type, view_order
    from
     m_convention_hotel
    where
     hotel = #{hotel}
    ORDER BY
	 view_order
  </select>

  <select id="getConventionHotelIdsByHotel" resultType="java.lang.Long">
    select
     id
    from
     m_convention_hotel
    where
     hotel = #{hotel}
    and
     fk_convention_id = #{conventionId}

  </select>

  <select id="getConventionHotelId" resultType="java.lang.Long">
    select
     id
    from
     m_convention_hotel
    where
     hotel = #{hotel}
    and
     room_type = #{roomType}
    and
     fk_convention_id = #{conventionId}

  </select>

  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select
     IFNULL(max(view_order)+1,0) view_order
    from
     m_convention_hotel
    where
     fk_convention_id = #{conventionId}

  </select>
  <select id="getConventionHotelIds" parameterType="java.lang.Long" resultType="java.lang.Long">
    select
     id
    from
     m_convention_hotel
    where
     fk_convention_id = #{conventionId}

  </select>

  <select id="conventionHotelIsEmpty" parameterType="java.lang.Long" resultType="java.lang.Boolean">
    select IFNULL(max(id),0) id from m_convention_hotel where fk_convention_id = #{id} LIMIT 1

  </select>
    <select id="getConventionHotelByConventionPerson"
            resultType="com.get.salecenter.entity.ConventionHotel">
      SELECT
        m_convention_hotel.*
      FROM
        m_convention_hotel
          LEFT JOIN
        m_convention_hotel_room
        ON
          m_convention_hotel.id = m_convention_hotel_room.fk_convention_hotel_id
          INNER JOIN
        m_convention_person
          INNER JOIN
        r_convention_hotel_room_person
        ON
              m_convention_person.id = r_convention_hotel_room_person.fk_convention_person_id AND
              m_convention_hotel_room.id = r_convention_hotel_room_person.fk_convention_hotel_room_id
      WHERE
        m_convention_person.id = #{conventionPersonId}
      GROUP BY m_convention_hotel.id
        LIMIT 1
    </select>
</mapper>