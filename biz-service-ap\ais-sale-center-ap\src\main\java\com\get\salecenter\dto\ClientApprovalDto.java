package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ClientApprovalDto {

    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty("审批状态：0新申请/1通过/2拒绝")
    private Integer approvalStatus;

    @ApiModelProperty("学生编号")
    private String fkStudentNum;

    @ApiModelProperty("学生名字")
    private String fkStudentName;

    @ApiModelProperty("代理名字")
    private String fkAgentName;

    @ApiModelProperty("部门id")
    private Long fkDepartmentId;
}
