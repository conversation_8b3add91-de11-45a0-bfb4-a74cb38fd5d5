<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ConventionRegistrationAreaCountryMapper">
  <resultMap id="BaseResultMap" type="com.get.salecenter.entity.ConventionRegistrationAreaCountry">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_convention_registration_id" jdbcType="BIGINT" property="fkConventionRegistrationId" />
    <result column="fk_area_country_key" jdbcType="VARCHAR" property="fkAreaCountryKey" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>

  <select id="getCountryKey" parameterType="java.lang.Long" resultType="string">
      select
       fk_area_country_key
	  from
	   r_convention_registration_area_country
	  where
	   fk_convention_registration_id = #{id}
    </select>


</mapper>