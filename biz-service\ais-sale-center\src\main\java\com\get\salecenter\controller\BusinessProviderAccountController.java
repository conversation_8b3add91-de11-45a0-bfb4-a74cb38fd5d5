package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.salecenter.vo.BusinessProviderAccountVo;
import com.get.salecenter.service.BusinessProviderAccountService;
import com.get.salecenter.dto.BusinessProviderAccountDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Api(tags = "业务提供商账户")
@RestController
@RequestMapping("sale/businessProviderAccount")
public class BusinessProviderAccountController {

    @Resource
    private BusinessProviderAccountService businessProviderAccountService;

    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/业务提供商账户管理/查询列表")
    @PostMapping("getBusinessProviderAccounts")
    public ResponseBo<BusinessProviderAccountVo> getBusinessProviderAccounts(@RequestBody SearchBean<BusinessProviderAccountDto> searchBean) {
        return businessProviderAccountService.getBusinessProviderAccounts(searchBean.getData(),searchBean);
    }

    @ApiOperation(value = "新增", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/业务提供商账户管理/新增")
    @PostMapping("add")
    public SaveResponseBo add(@RequestBody @Validated(value = {BaseVoEntity.Add.class}) BusinessProviderAccountDto businessProviderAccountDto) {
        return businessProviderAccountService.add(businessProviderAccountDto);
    }


    @ApiOperation(value = "更新", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/业务提供商账户管理/更新")
    @PostMapping("update")
    public ResponseBo<BusinessProviderAccountVo> update(@RequestBody @Validated(value = {BaseVoEntity.Update.class}) BusinessProviderAccountDto businessProviderAccountDto) {
        return new ResponseBo<>(businessProviderAccountService.update(businessProviderAccountDto));
    }



    @ApiOperation(value = "快捷设置首选合同")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/业务提供商账户管理/快捷设置首选合同")
    @GetMapping("quickFirstContractAccount")
    public SaveResponseBo quickFirstContractAccount(@RequestParam("businessProviderId") Long businessProviderId,@RequestParam("accountId") Long accountId) {
        return businessProviderAccountService.quickFirstContractAccount(businessProviderId,accountId);
    }

    @ApiOperation(value = "快速激活或屏蔽合同账户")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/业务提供商账户管理/快速激活或屏蔽合同账户")
    @GetMapping("quickActivationOrMask")
    public SaveResponseBo quickActivationOrMask(@RequestParam("businessProviderId") Long businessProviderId,@RequestParam("accountId") Long accountId,@RequestParam("status") Boolean status) {
        return businessProviderAccountService.quickActivationOrMask(businessProviderId,accountId,status);
    }


    @ApiOperation(value = "详情", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/业务提供商账户管理/详情")
    @GetMapping("{id}")
    public ResponseBo<BusinessProviderAccountVo> findInfoById(@PathVariable("id") Long id) {
        return new ResponseBo<>(businessProviderAccountService.findInfoById(id));
    }

    @ApiOperation(value = "删除", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/业务提供商账户管理/删除")
    @GetMapping("delete/{id}")
    public SaveResponseBo delete(@PathVariable("id") Long id) {
        return businessProviderAccountService.delete(id);
    }


    @ApiOperation(value = "业务提供商合同账户列表账户重复提示")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/业务提供商账户管理/业务提供商合同账户列表账户重复提示")
    @GetMapping("getContractAccountExist")
    public ResponseBo<String> getContractAccountExist(@RequestParam(value = "id", required = false) Long id, @RequestParam("businessProviderId") Long businessProviderId, @RequestParam("bankAccount") String bankAccount,
                                                      @RequestParam("bankAccountNum") String bankAccountNum) {
        return businessProviderAccountService.getBusinessProviderContractAccountExist(id, businessProviderId, bankAccount, bankAccountNum);
    }
}
