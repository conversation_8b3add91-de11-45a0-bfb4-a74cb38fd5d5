package com.get.mpscenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@TableName("r_institution_provider_mps_commission_institution")
@Data
public class InstitutionProviderMpsCommissionInstitution extends BaseEntity implements Serializable {

    @ApiModelProperty("学校提供商佣金Id")
    private Long fkInstitutionProviderMpsCommissionId;

    @ApiModelProperty("学校Id")
    private Long fkInstitutionId;
}
