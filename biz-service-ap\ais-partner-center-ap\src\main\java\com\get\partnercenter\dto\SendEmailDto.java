package com.get.partnercenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/24  15:49
 * @Version 1.0
 * 邮件发送对象
 */
@Data
public class SendEmailDto {

    @ApiModelProperty("邮件类型-1新建/2重置密码")
    @NotNull(message = "邮件类型不能为空")
    private Integer type;

    @ApiModelProperty("重置密码新密码-前端忽略不用传")
    private String newPassword;

    @ApiModelProperty("邮件详细信息")
    @NotNull(message = "邮件详细信息不能为空")
    private List<SendEmailInfoDto> emailInfos;

}
