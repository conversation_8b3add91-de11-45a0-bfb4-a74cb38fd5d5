package com.get.partnercenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.get.partnercenter.entity.MAppStudentOfferItemEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.partnercenter.vo.MAppStudentOfferItemVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_app_student_offer_item】的数据库操作Mapper
* @createDate 2025-04-02 09:43:49
* @Entity com.get.partnercenter.entity.MAppStudentOfferItem
*/
@Mapper
@DS("saledb")
public interface MAppStudentOfferItemMapper extends BaseMapper<MAppStudentOfferItemEntity> {
    List<MAppStudentOfferItemVo> selectItemList(Long id,int type);
}




