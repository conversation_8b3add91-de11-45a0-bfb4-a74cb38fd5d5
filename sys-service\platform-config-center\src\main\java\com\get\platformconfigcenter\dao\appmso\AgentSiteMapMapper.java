package com.get.platformconfigcenter.dao.appmso;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.AgentSiteMap;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("appmsodb")
public interface AgentSiteMapMapper extends BaseMapper<AgentSiteMap> {
//    int insert(AgentSiteMap record);
//
//    int insertSelective(AgentSiteMap record);
//
//    int updateByPrimaryKeySelective(AgentSiteMap record);
//
//    int updateByPrimaryKey(AgentSiteMap record);
}