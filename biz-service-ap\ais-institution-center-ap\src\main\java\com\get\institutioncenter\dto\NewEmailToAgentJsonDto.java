package com.get.institutioncenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/7/18 14:17
 * @desciption:
 */
@Data
public class NewEmailToAgentJsonDto {

    @ApiModelProperty(value = "步骤ids")
    @NotNull(message = "步骤ids不能为空", groups = {BaseVoEntity.Update.class})
    private Set<Long> stepIds;

    @ApiModelProperty(value = "入学时间（开始）")
    private String intakeTimeStart;

    @ApiModelProperty(value = "入学时间（结束）")
    private String intakeTimeEnd;
}
