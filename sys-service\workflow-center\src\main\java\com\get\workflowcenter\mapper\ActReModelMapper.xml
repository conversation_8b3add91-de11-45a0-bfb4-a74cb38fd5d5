<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.workflowcenter.mapper.ActReModelMapper">
    <resultMap id="BaseResultMap" type="com.get.workflowcenter.entity.ActReModel">
        <id column="ID_" jdbcType="VARCHAR" property="id"/>
        <result column="REV_" jdbcType="INTEGER" property="rev"/>
        <result column="NAME_" jdbcType="VARCHAR" property="name"/>
        <result column="KEY_" jdbcType="VARCHAR" property="key"/>
        <result column="CATEGORY_" jdbcType="VARCHAR" property="category"/>
        <result column="CREATE_TIME_" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="LAST_UPDATE_TIME_" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="VERSION_" jdbcType="INTEGER" property="version"/>
        <result column="META_INFO_" jdbcType="VARCHAR" property="metaInfo"/>
        <result column="DEPLOYMENT_ID_" jdbcType="VARCHAR" property="deploymentId"/>
        <result column="EDITOR_SOURCE_VALUE_ID_" jdbcType="VARCHAR" property="editorSourceValueId"/>
        <result column="EDITOR_SOURCE_EXTRA_VALUE_ID_" jdbcType="VARCHAR" property="editorSourceExtraValueId"/>
        <result column="TENANT_ID_" jdbcType="VARCHAR" property="tenantId"/>
    </resultMap>
    <sql id="Base_Column_List">
    ID_, REV_, NAME_, KEY_, CATEGORY_, CREATE_TIME_, LAST_UPDATE_TIME_, VERSION_, META_INFO_, 
    DEPLOYMENT_ID_, EDITOR_SOURCE_VALUE_ID_, EDITOR_SOURCE_EXTRA_VALUE_ID_, TENANT_ID_
  </sql>

    <insert id="insertSelective" parameterType="com.get.workflowcenter.entity.ActReModel" keyProperty="id"
            useGeneratedKeys="true">
        insert into ACT_RE_MODEL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID_,
            </if>
            <if test="rev != null">
                REV_,
            </if>
            <if test="name != null">
                NAME_,
            </if>
            <if test="key != null">
                KEY_,
            </if>
            <if test="category != null">
                CATEGORY_,
            </if>
            <if test="createTime != null">
                CREATE_TIME_,
            </if>
            <if test="lastUpdateTime != null">
                LAST_UPDATE_TIME_,
            </if>
            <if test="version != null">
                VERSION_,
            </if>
            <if test="metaInfo != null">
                META_INFO_,
            </if>
            <if test="deploymentId != null">
                DEPLOYMENT_ID_,
            </if>
            <if test="editorSourceValueId != null">
                EDITOR_SOURCE_VALUE_ID_,
            </if>
            <if test="editorSourceExtraValueId != null">
                EDITOR_SOURCE_EXTRA_VALUE_ID_,
            </if>
            <if test="tenantId != null">
                TENANT_ID_,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="rev != null">
                #{rev,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="key != null">
                #{key,jdbcType=VARCHAR},
            </if>
            <if test="category != null">
                #{category,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                #{version,jdbcType=INTEGER},
            </if>
            <if test="metaInfo != null">
                #{metaInfo,jdbcType=VARCHAR},
            </if>
            <if test="deploymentId != null">
                #{deploymentId,jdbcType=VARCHAR},
            </if>
            <if test="editorSourceValueId != null">
                #{editorSourceValueId,jdbcType=VARCHAR},
            </if>
            <if test="editorSourceExtraValueId != null">
                #{editorSourceExtraValueId,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="getModelList" resultType="com.get.workflowcenter.vo.ActReModelVo">
   select arm.*,ard.DEPLOY_TIME_,arp.ID_ AS procdef_Id,arp.SUSPENSION_STATE_ from ACT_RE_MODEL arm
   LEFT JOIN ACT_RE_DEPLOYMENT ard on arm.deployment_id_=ard.id_
   LEFT JOIN ACT_RE_PROCDEF arp    on arp.DEPLOYMENT_ID_=ard.ID_
   where arm.DEPLOYMENT_ID_ is not null
  </select>

</mapper>