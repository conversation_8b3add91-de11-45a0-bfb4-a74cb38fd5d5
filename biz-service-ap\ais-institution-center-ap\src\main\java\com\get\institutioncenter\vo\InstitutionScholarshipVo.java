package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.institutioncenter.dto.InstitutionCourseAppInfoDataProcessDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/6 12:04
 */
@Data
public class InstitutionScholarshipVo extends BaseEntity {


    @ApiModelProperty("目标对象")
    private String targetName;

    @ApiModelProperty(value = "目标类型")
    private String fkTableName;
    /**
     * 目标对象id
     */
    @ApiModelProperty(value = "目标对象id")
    private Long fkTableId;


    @ApiModelProperty("公开对象名称")
    private String publicLevelName;

    @ApiModelProperty("学院等级，1是1级，2是二级")
    private int Level;

    @ApiModelProperty("学校封面url")
    private String coversUrl;

    @ApiModelProperty("是否有申请费用")
    private Boolean isAppFee;

    @ApiModelProperty("是否有申请截止")
    private Boolean isDeadInfo;

    @ApiModelProperty("是否有奖学金")
    private Boolean isScholarship;

    @ApiModelProperty("目标对象信息")
    private List<InstitutionCourseAppInfoDataProcessDto.CourseAppInfoResultMappingVo> courseAppInfoResultMappingVos;

    @ApiModelProperty(value = "优先级匹配用的key")
    private String fk;

    @ApiModelProperty(value = "优先级匹配用的key")
    private String fc;

    private Integer priority;

    private Date gmtPriorityTime;

    @ApiModelProperty(value = "生效时间")
    private String effectiveDate;

    @ApiModelProperty(value = "学校名称")
    private String fkInstitutionName;

    //===============实体类InstitutionScholarship========================
    /**
     * 奖学金标题
     */
    @ApiModelProperty(value = "奖学金标题")
    @Column(name = "scholarship_title")
    private String scholarshipTitle;

    /**
     * 奖学金金额
     */
    @ApiModelProperty(value = "奖学金金额")
    @Column(name = "scholarship_amount")
    private String scholarshipAmount;

    /**
     * 奖学金名额
     */
    @ApiModelProperty(value = "奖学金名额")
    @Column(name = "scholarship_quota")
    private String scholarshipQuota;


    /**
     * 奖学金适用学生
     */
    @ApiModelProperty(value = "奖学金适用学生")
    @Column(name = "scholarship_apply_to")
    private String scholarshipApplyTo;

    /**
     * 申请条件
     */
    @ApiModelProperty(value = "申请条件")
    @Column(name = "app_condition")
    private String appCondition;

    /**
     * 申请方式
     */
    @ApiModelProperty(value = "申请方式")
    @Column(name = "app_method")
    private String appMethod;

    /**
     * 申请截止日期：[2022-1-5,2023-10-5][2022-1-5,2023-10-5]
     */
    @ApiModelProperty(value = "申请截止日期：[2022-1-5,2023-10-5][2022-1-5,2023-10-5]")
    @Column(name = "app_deadline")
    private String appDeadline;

    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @Column(name = "public_level")
    private String publicLevel;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    @Column(name = "publish_time")
    private Date publishTime;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;

    /**
     * 详细信息
     */
    @ApiModelProperty(value = "详细信息")
    @Column(name = "app_detail")
    private String appDetail;

    private static final long serialVersionUID = 1L;
}
