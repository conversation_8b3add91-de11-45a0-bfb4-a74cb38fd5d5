package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.UIncentiveRewardMapper;
import com.get.salecenter.vo.IncentiveRewardVo;
import com.get.salecenter.entity.IncentivePolicy;
import com.get.salecenter.entity.IncentiveReward;
import com.get.salecenter.service.IIncentivePolicyService;
import com.get.salecenter.service.IIncentiveRewardService;
import com.get.salecenter.dto.IncentiveRewardSearchDto;
import com.get.salecenter.dto.IncentiveRewardDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-03-13
 */
@Service
public class IncentiveRewardServiceImpl extends BaseServiceImpl<UIncentiveRewardMapper, IncentiveReward> implements IIncentiveRewardService {

    @Resource
    private UIncentiveRewardMapper incentiveRewardMapper;
    @Resource
    private IIncentivePolicyService incentivePolicyService;
    @Resource
    private UtilService utilService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Override
    public IncentiveRewardVo selectById(Long id) {
        IncentiveRewardVo uIncentiveRewardVo = null;
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        IncentiveReward uIncentiveReward = this.getById(id);
        if(GeneralTool.isNotEmpty(uIncentiveReward))
        {
            uIncentiveRewardVo = BeanCopyUtils.objClone(uIncentiveReward, IncentiveRewardVo::new);
        }
        return uIncentiveRewardVo;
    }

    @Override
    public Long addUIncentiveReward(IncentiveRewardDto uIncentiveRewardDto) {
        IncentiveReward uIncentiveReward = BeanCopyUtils.objClone(uIncentiveRewardDto, IncentiveReward::new);
        //校验奖品名称是否重复
        List<IncentiveReward> uIncentiveRewards = this.list(Wrappers.<IncentiveReward>lambdaQuery().eq(IncentiveReward::getRewardName, uIncentiveReward.getRewardName()));
        if(GeneralTool.isNotEmpty(uIncentiveRewards) && uIncentiveRewards.size()>0)
        {
            throw new GetServiceException(LocaleMessageUtils.getMessage("the_prize_name_already_exists"));
        }
        //获取最大的排序值,这种方式在高并发下会有问题，普通操作不影响
        QueryWrapper<IncentiveReward> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("max(view_order) as viewOrder");
        IncentiveReward incentiveReward = incentiveRewardMapper.selectOne(queryWrapper);
        Integer maxOrder = 0;
        if (GeneralTool.isNotEmpty(incentiveReward) && GeneralTool.isNotEmpty(incentiveReward.getViewOrder())) {
            maxOrder = incentiveReward.getViewOrder();
        }
        uIncentiveReward.setViewOrder(maxOrder+1);
        utilService.setCreateInfo(uIncentiveReward);
        incentiveRewardMapper.insert(uIncentiveReward);
        return uIncentiveReward.getId();
    }

    @Override
    public void deleteDataById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        IncentiveReward uIncentiveReward = this.getById(id);
        if(GeneralTool.isEmpty(uIncentiveReward))
        {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //判断该奖品是否在使用，已使用则不能删除，需要从奖励政策的json里面去判断是否存在
        Wrapper<IncentivePolicy> wrapper = Wrappers.<IncentivePolicy>lambdaQuery()
                .and(w -> w.apply("JSON_CONTAINS(reward_json->'$.rewardIds', '"+id+"') = 1"));//这里需要进行修改 JSON_CONTAINS(t.report_query -> '$.fkCompanyIdList',JSON_Array(#{fkCompanyId}))

        List<IncentivePolicy> incentivePolicies = incentivePolicyService.list(wrapper);
        if(GeneralTool.isEmpty(incentivePolicies) && incentivePolicies.size()>0)
        {
            throw new GetServiceException(LocaleMessageUtils.getMessage("the_prize_is_currently_in_use"));
        }
        incentiveRewardMapper.deleteById(id);
    }

    @Override
    public IncentiveRewardVo updateUIncentiveReward(IncentiveRewardDto uIncentiveRewardDto) {
        if (uIncentiveRewardDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        IncentiveReward oldUIncentiveReward = this.getById(uIncentiveRewardDto.getId());
        if(GeneralTool.isEmpty(oldUIncentiveReward))
        {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        IncentiveReward uIncentiveReward = BeanCopyUtils.objClone(uIncentiveRewardDto, IncentiveReward::new);
        utilService.updateUserInfoToEntity(uIncentiveReward);
        incentiveRewardMapper.updateById(uIncentiveReward);
        return selectById(uIncentiveReward.getId());
    }

    @Override
    public List<IncentiveRewardVo> getIncentiveRewards(IncentiveRewardDto uIncentiveRewardDto, SearchBean<IncentiveRewardDto> page) {
        LambdaQueryWrapper<IncentiveReward> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(uIncentiveRewardDto)) {
            if (GeneralTool.isNotEmpty(uIncentiveRewardDto.getFkCompanyId())) {
                lambdaQueryWrapper.eq(IncentiveReward::getFkCompanyId, uIncentiveRewardDto.getFkCompanyId());
            }
            if (GeneralTool.isNotEmpty(uIncentiveRewardDto.getRewardName())) {
                lambdaQueryWrapper.like(IncentiveReward::getRewardName, uIncentiveRewardDto.getRewardName());
            }
            if (GeneralTool.isNotEmpty(uIncentiveRewardDto.getRewardIds())) {
                lambdaQueryWrapper.in(IncentiveReward::getId, uIncentiveRewardDto.getRewardIds());
            }
        }
        lambdaQueryWrapper.orderByDesc(IncentiveReward::getViewOrder);
        IPage<IncentiveReward> iPage = incentiveRewardMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<IncentiveReward> uIncentiveRewards = iPage.getRecords();
        page.setAll((int) iPage.getTotal());

//        List<IncentiveReward> uIncentiveRewards = incentiveRewardMapper.selectList(lambdaQueryWrapper);
        List<IncentiveRewardVo> incentiveRewardVos = BeanCopyUtils.copyListProperties(uIncentiveRewards, IncentiveRewardVo::new);
        if(GeneralTool.isNotEmpty(uIncentiveRewards) && uIncentiveRewards.size()>0)
        {
            Set<Long> fkComanyIds = uIncentiveRewards.stream().map(IncentiveReward::getFkCompanyId).collect(Collectors.toSet());
            //根据公司ids获取名称
            Map<Long, String> companyNamesByIds = new HashMap<>();
            if (GeneralTool.isNotEmpty(fkComanyIds)) {
                Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(fkComanyIds);
                if (result.isSuccess() && result.getData() != null) {
                    companyNamesByIds = result.getData();
                }
            }
            if(GeneralTool.isNotEmpty(incentiveRewardVos) && incentiveRewardVos.size()>0)
            {
                for(IncentiveRewardVo incentiveRewardVo : incentiveRewardVos)
                {
                    incentiveRewardVo.setFkCompanyName(companyNamesByIds.get(incentiveRewardVo.getFkCompanyId()));
                }
            }
        }
        return incentiveRewardVos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<IncentiveRewardDto> voList) {
        if (GeneralTool.isEmpty(voList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        //如果前端是上下逐个移动的两个对象，互换排序值即可，前端不用处理
        IncentiveReward ro = BeanCopyUtils.objClone(voList.get(0), IncentiveReward::new);
        Integer oneorder = ro.getViewOrder();
        IncentiveReward rt = BeanCopyUtils.objClone(voList.get(1), IncentiveReward::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        incentiveRewardMapper.updateById(ro);
        incentiveRewardMapper.updateById(rt);

        //如果前端是跨越多个行拖动，那么前端需要排列最新的值集合，传递给后端。
//        List<IncentiveReward> uIncentiveRewards = BeanCopyUtils.copyListProperties(voList, IncentiveReward::new);
//        this.saveOrUpdateBatch(uIncentiveRewards);
    }

    @Override
    public List<IncentiveRewardVo> getIncentiveRewardListByIds(IncentiveRewardSearchDto incentiveRewardSearchDto) {
        List<IncentiveReward> list = incentiveRewardMapper.selectBatchIds(incentiveRewardSearchDto.getRewardIds());
        return BeanCopyUtils.copyListProperties(list, IncentiveRewardVo::new);
    }
}
