<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.resumecenter.dao.ResumeOtherMapper">
    <resultMap id="BaseResultMap" type="com.get.resumecenter.entity.ResumeOther">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_resume_id" jdbcType="BIGINT" property="fkResumeId"/>
        <result column="fk_other_type_id" jdbcType="BIGINT" property="fkOtherTypeId"/>
        <result column="subject" jdbcType="VARCHAR" property="subject"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <sql id="Base_Column_List">
    m.id, m.fk_resume_id, m.fk_other_type_id, m.subject, m.description, m.gmt_create, m.gmt_create_user,
    m.gmt_modified, m.gmt_modified_user,o.type_name
  </sql>
    <insert id="insertSelective" parameterType="com.get.resumecenter.entity.ResumeOther" keyProperty="id"
            useGeneratedKeys="true">
        insert into m_resume_other
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkResumeId != null">
                fk_resume_id,
            </if>
            <if test="fkOtherTypeId != null">
                fk_other_type_id,
            </if>
            <if test="subject != null">
                subject,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkResumeId != null">
                #{fkResumeId,jdbcType=BIGINT},
            </if>
            <if test="fkOtherTypeId != null">
                #{fkOtherTypeId,jdbcType=BIGINT},
            </if>
            <if test="subject != null">
                #{subject,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="selectByOtherId" parameterType="java.lang.Long" resultType="com.get.resumecenter.vo.ResumeOtherVo">
        select
        <include refid="Base_Column_List"/>
        from m_resume_other m
        left join u_other_type o
        on m.fk_other_type_id=o.id
        where m.id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByResumeId" parameterType="java.lang.Long" resultType="com.get.resumecenter.vo.ResumeOtherVo">
        select
        <include refid="Base_Column_List"/>
        from m_resume_other m
        left join u_other_type o
        on m.fk_other_type_id=o.id
        where m.fk_resume_id = #{id,jdbcType=BIGINT}
    </select>

    <select id="isExistByOtherTypeId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id from m_resume_other where fk_other_type_id=#{otherTypeId}
    </select>

</mapper>