package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.service.GetService;
import com.get.salecenter.vo.AgentRoleStaffVo;
import com.get.salecenter.vo.AgentVo;
import com.get.salecenter.entity.AgentRoleStaff;
import com.get.salecenter.dto.AgentRoleStaffDto;

import java.util.List;

/**
 * 代理项目成员配置管理逻辑处理类
 *
 * <AUTHOR>
 * @date 2021/8/2 11:14
 */
public interface AgentRoleStaffService extends GetService<AgentRoleStaff> {

    /**
     * 代理项目成员配置管理列表
     *
     * @Date 11:34 2021/8/2
     * <AUTHOR>
     */
    List<AgentVo> getAgentRoleStaffs(AgentRoleStaffDto data, Page page);

    /**
     * 批量分配项目成员
     *
     * @Date 15:47 2021/8/2
     * <AUTHOR>
     */
    void batchUpdate(AgentRoleStaffDto agentRoleStaffDto);

    /**
     * 代理项目成员配置详情
     *
     * @return
     * @Date 16:20 2021/8/2
     * <AUTHOR>
     */
    List<AgentRoleStaffVo> detail(Long id, Long countryId, Long companyId, String fkTypeKey);

    /**
     * 获取代理项目成员配置
     *
     * @Date 14:46 2022/1/28
     * <AUTHOR>
     */
    List<AgentRoleStaffVo> getAgentRoleStaff(Long agentId, Long countryId, Long companyId, String fkTypeKey);

    /**
     * 更新代理项目成员配置
     *
     * @Date 16:39 2021/8/2
     * <AUTHOR>
     */
    void update(List<AgentRoleStaffDto> agentRoleStaffDtoList);

    /**
     * 删除绑定关系
     *
     * @Date 16:16 2021/8/3
     * <AUTHOR>
     */
    void delete(AgentRoleStaffDto agentRoleStaffDto);

    /**
     * 批量移除项目成员
     *
     * @Date 10:09 2021/8/23
     * <AUTHOR>
     */
    void batchRemoveUpdate(AgentRoleStaffDto agentRoleStaffDto);

    /**
     * 通用项目成员配置列表
     *
     * @param agentRoleStaffDto
     * @param page
     * @return
     */
    List<AgentRoleStaffVo> getCommonAgentRoleStaffs(AgentRoleStaffDto agentRoleStaffDto, Page page);

    /**
     * 新增通用项目成员配置
     *
     * @param agentRoleStaffDto
     * @return
     */
    void addCommonAgentRoleStaff(AgentRoleStaffDto agentRoleStaffDto);

    /**
     * 修改通用项目成员配置
     *
     * @param agentRoleStaffDto
     * @return
     */
    AgentRoleStaffVo updateCommonAgentRoleStaff(AgentRoleStaffDto agentRoleStaffDto);

    /**
     * 通用项目成员配置详情
     *
     * @param id
     * @return
     */
    AgentRoleStaffVo findCommonAgentRoleStaffById(Long id);

    /**
     * 获取
     *
     * @param fkCompanyId
     * @return
     */
    List<BaseSelectEntity> getStaffNameSelect(Long fkCompanyId);

    /**
     * 删除
     *
     * @param id
     */
    void deleteCommonAgentRoleStaff(Long id);
}
