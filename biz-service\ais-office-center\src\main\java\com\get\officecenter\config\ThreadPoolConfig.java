package com.get.officecenter.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * @author: Hardy
 * @create: 2022/2/18 12:24
 * @verison: 1.0
 * @description:
 */
@Configuration
public class ThreadPoolConfig {

    @Bean("officeTaskExecutor")
    public ThreadPoolTaskExecutor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(20);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(20);
        // 线程的前缀
        executor.setThreadNamePrefix("sale-prefix-");
        // 对拒绝任务的处理策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        //使用后自动关闭
        executor.setWaitForTasksToCompleteOnShutdown(true);
        return executor;
    }

}
