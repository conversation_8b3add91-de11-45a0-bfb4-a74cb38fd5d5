package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * author:Neil
 * Time: 19:39
 * Date: 2023/1/16
 * Description:
 */
@Data
public class StudentReceivableAndPaySummaryVo {

    @ApiModelProperty(value = "实收金额hkd")
    private String receivableActualAmountInfoHkdSum;

    @ApiModelProperty(value = "应收金额hkd")
    private String receivablePlanAmountInfoHkdSum;

    @ApiModelProperty(value = "应收未收hkd")
    private String receivableDiffAmountInfoHkdSum;




    @ApiModelProperty(value = "应付金额hkd")
    private String payablePlanAmountInfoHkdSum;

    @ApiModelProperty(value = "实付金额Hkd")
    private String payableActualAmountInfoHkdSum;

    @ApiModelProperty(value = "应付未付Hkd")
    private String payableDiffAmountInfoHkdSum;

    @ApiModelProperty(value = "利润总计")
    private String totalProfitInfoHkdSum;

}
