package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.dto.EventPlanRegistrationContactPersonFormDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2023/12/12
 * @TIME: 17:35
 * @Description:
 **/
@Data
public class EventPlanFormVo extends BaseEntity implements Serializable {
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "活动计划主题列表")
    private List<EventPlanFormResoutVo> resoutDtos;

    @ApiModelProperty(value = "联系人信息")
    private List<EventPlanRegistrationContactPersonFormDto> personList;

    @ApiModelProperty(value = "学校提供商Id")
    private Long institutionProviderId;

    @ApiModelProperty(value = "发票建议币种")
    private String currencyTypeNumInvoice;
}
