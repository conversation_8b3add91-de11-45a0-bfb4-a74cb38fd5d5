package com.get.officecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/11/7 12:47
 */
@Data
@ApiModel(value = "工作时间设置VO")
public class WorkScheduleTimeConfigDto extends BaseVoEntity {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 部门Id
     */
    @ApiModelProperty(value = "部门Id")
    private Long fkDepartmentId;

    /**
     * 工作开始时间
     */
    @ApiModelProperty(value = "工作开始时间")
    private String workingStartTime;

    /**
     * 工作结束时间
     */
    @ApiModelProperty(value = "工作结束时间")
    private String workingEndTime;

    /**
     * 午休开始时间
     */
    @ApiModelProperty(value = "午休开始时间")
    private String noonBreakStartTime;

    /**
     * 午休结束时间
     */
    @ApiModelProperty(value = "午休结束时间")
    private String noonBreakEndTime;

    /**
     * 工作时长（小时）
     */
    @ApiModelProperty(value = "工作时长（小时）")
    private BigDecimal workingDuration;

    /**
     * 每周工作日：如：1,2,3,4,5
     */
    @ApiModelProperty(value = "每周工作日：如：1,2,3,4,5")
    private String workingWeekCycle;
}
