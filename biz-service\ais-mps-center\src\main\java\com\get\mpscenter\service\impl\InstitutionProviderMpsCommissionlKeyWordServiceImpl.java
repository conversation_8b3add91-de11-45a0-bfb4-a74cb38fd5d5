package com.get.mpscenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.mpscenter.dao.MpsKeyWordMajorLevelMapper;
import com.get.mpscenter.dao.MpsKeyWordMapper;
import com.get.mpscenter.dto.KeyWordMajorLevelDto;
import com.get.mpscenter.dto.KeyWordMajorLevelSelectDto;
import com.get.mpscenter.entity.MpsKeyWord;
import com.get.mpscenter.entity.MpsKeyWordMajorLevel;
import com.get.mpscenter.service.InstitutionProviderMpsCommissionlKeyWordService;
import com.get.mpscenter.vo.KeyWordVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/3 17:23
 * @desciption:
 */
@Service
public class InstitutionProviderMpsCommissionlKeyWordServiceImpl extends GetServiceImpl<MpsKeyWordMapper, MpsKeyWord> implements InstitutionProviderMpsCommissionlKeyWordService {

    @Resource
    private MpsKeyWordMapper mpsKeyWordMapper;

    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Resource
    private UtilService utilService;
    @Resource
    private MpsKeyWordMajorLevelMapper mpsKeyWordMajorLevelMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAdd(ValidList<KeyWordMajorLevelDto> keyWordMajorLevelDtos) {
        if (GeneralTool.isEmpty(keyWordMajorLevelDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //获取最大排序(防止每次都要去查最大值，所以在外面查出一次，循环几次 就自增几次)
        Integer maxViewOrder = mpsKeyWordMapper.getMaxOrder();
        for (KeyWordMajorLevelDto keyWordMajorLevelDto : keyWordMajorLevelDtos) {
            if (GeneralTool.isEmpty(keyWordMajorLevelDto.getId())) {
                if (validateAdd(keyWordMajorLevelDto)) {
                    MpsKeyWord mpsKeyWord = BeanCopyUtils.objClone(keyWordMajorLevelDto, MpsKeyWord::new);
                    mpsKeyWord.setViewOrder(maxViewOrder);
                    utilService.updateUserInfoToEntity(mpsKeyWord);
                    int i = mpsKeyWordMapper.insert(mpsKeyWord);
                    if (i < 0) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
                    }
                    maxViewOrder++;

                    //添加课程等级关系表
                    for (Long fkMajorLevelId : keyWordMajorLevelDto.getFkMajorLevelIds()) {
                        MpsKeyWordMajorLevel mpsKeyWordMajorLevel = new MpsKeyWordMajorLevel();
                        mpsKeyWordMajorLevel.setFkMpsKeyWordId(mpsKeyWord.getId());
                        mpsKeyWordMajorLevel.setFkMajorLevelId(fkMajorLevelId);
                        utilService.updateUserInfoToEntity(mpsKeyWordMajorLevel);
                        mpsKeyWordMajorLevelMapper.insert(mpsKeyWordMajorLevel);
                    }
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("key_word_exist"));
                }
            } else {
                if (validateUpdate(keyWordMajorLevelDto)) {
                    MpsKeyWord mpsKeyWord = BeanCopyUtils.objClone(keyWordMajorLevelDto, MpsKeyWord::new);
                    utilService.updateUserInfoToEntity(mpsKeyWord);
                    mpsKeyWordMapper.updateById(mpsKeyWord);
                    //删除关系表
                    mpsKeyWordMajorLevelMapper.delete(Wrappers.<MpsKeyWordMajorLevel>lambdaQuery().eq(MpsKeyWordMajorLevel::getFkMpsKeyWordId, mpsKeyWord.getId()));
                    //添加课程等级关系表
                    for (Long fkMajorLevelId : keyWordMajorLevelDto.getFkMajorLevelIds()) {
                        MpsKeyWordMajorLevel mpsKeyWordMajorLevel = new MpsKeyWordMajorLevel();
                        mpsKeyWordMajorLevel.setFkMpsKeyWordId(mpsKeyWord.getId());
                        mpsKeyWordMajorLevel.setFkMajorLevelId(fkMajorLevelId);
                        utilService.updateUserInfoToEntity(mpsKeyWordMajorLevel);
                        mpsKeyWordMajorLevelMapper.insert(mpsKeyWordMajorLevel);
                    }

                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("key_word_exist"));
                }
            }

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //校验该类型是否绑定相关联系人
        mpsKeyWordMapper.deleteById(id);
        //删除关系表
        mpsKeyWordMajorLevelMapper.delete(Wrappers.<MpsKeyWordMajorLevel>lambdaQuery().eq(MpsKeyWordMajorLevel::getFkMpsKeyWordId, id));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void movingOrder(List<KeyWordMajorLevelDto> keyWordMajorLevelVos) {
        if (GeneralTool.isEmpty(keyWordMajorLevelVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        MpsKeyWord ro = BeanCopyUtils.objClone(keyWordMajorLevelVos.get(0), MpsKeyWord::new);
        Integer oneOrder = ro.getViewOrder();
        MpsKeyWord rt = BeanCopyUtils.objClone(keyWordMajorLevelVos.get(1), MpsKeyWord::new);
        Integer twoOrder = rt.getViewOrder();
        ro.setViewOrder(oneOrder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(twoOrder);
        utilService.updateUserInfoToEntity(rt);
        mpsKeyWordMapper.updateById(ro);
        mpsKeyWordMapper.updateById(rt);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public KeyWordVo updateKeyWord(KeyWordMajorLevelDto keyWordMajorLevelDto) {
        MpsKeyWord mpsKeyWord = BeanCopyUtils.objClone(keyWordMajorLevelDto, MpsKeyWord::new);
        if (mpsKeyWord == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        MpsKeyWord result = mpsKeyWordMapper.selectById(keyWordMajorLevelDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_obj_null"));
        }
        if (validateUpdate(keyWordMajorLevelDto)) {
            utilService.updateUserInfoToEntity(mpsKeyWord);
            mpsKeyWordMapper.updateById(mpsKeyWord);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("key_word_exist"));
        }
        if (GeneralTool.isNotEmpty(keyWordMajorLevelDto.getFkMajorLevelIds())) {
            //删除关系表
            mpsKeyWordMajorLevelMapper.delete(Wrappers.<MpsKeyWordMajorLevel>lambdaQuery().eq(MpsKeyWordMajorLevel::getFkMpsKeyWordId, keyWordMajorLevelDto.getId()));
            for (Long fkMajorLevelId : keyWordMajorLevelDto.getFkMajorLevelIds()) {
                MpsKeyWordMajorLevel mpsKeyWordMajorLevel = new MpsKeyWordMajorLevel();
                mpsKeyWordMajorLevel.setFkMpsKeyWordId(mpsKeyWord.getId());
                mpsKeyWordMajorLevel.setFkMajorLevelId(fkMajorLevelId);
                utilService.updateUserInfoToEntity(mpsKeyWordMajorLevel);
                mpsKeyWordMajorLevelMapper.insert(mpsKeyWordMajorLevel);
            }
        }
        return findKeyWordById(mpsKeyWord.getId());
    }

    @Override
    public List<KeyWordVo> getKeyWords(KeyWordMajorLevelSelectDto keyWordMajorLevelSelectDto, SearchBean<KeyWordMajorLevelSelectDto> page) {
        if (GeneralTool.isEmpty(keyWordMajorLevelSelectDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        IPage<KeyWordVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<KeyWordVo> keyWordVos = mpsKeyWordMapper.getKeyWords(pages, keyWordMajorLevelSelectDto);
        page.setAll((int) pages.getTotal());
        //赋值
        setName(keyWordVos);
        return keyWordVos;
    }

    private void setName(List<KeyWordVo> keyWordVos) {
        if (GeneralTool.isEmpty(keyWordVos)) {
            return;
        }
        Set<Long> ids = keyWordVos.stream().map(KeyWordVo::getId).collect(Collectors.toSet());
        List<MpsKeyWordMajorLevel> mpsKeyWordMajorLevels = mpsKeyWordMajorLevelMapper.selectList(Wrappers.<MpsKeyWordMajorLevel>lambdaQuery().in(MpsKeyWordMajorLevel::getFkMpsKeyWordId, ids));
        Map<Long, String> majorLevelNamesMap = institutionCenterClient.getMajorLevelNamesByIds(Optional.ofNullable(mpsKeyWordMajorLevels).map(list ->
                list.stream().map(MpsKeyWordMajorLevel::getFkMajorLevelId).collect(Collectors.toSet())).orElse(null)).getData();
        for (KeyWordVo keyWordVo : keyWordVos) {
            keyWordVo.setMajorLevelIds(Optional.ofNullable(mpsKeyWordMajorLevels).map(list -> list.stream()
                    .filter(mpsKeyWordMajorLevel -> mpsKeyWordMajorLevel.getFkMpsKeyWordId().equals(keyWordVo.getId()))
                    .map(MpsKeyWordMajorLevel::getFkMajorLevelId)
                    .collect(Collectors.toSet())).orElse(null));
            Set<Long> majorLevelIds = keyWordVo.getMajorLevelIds();
            if (GeneralTool.isNotEmpty(majorLevelIds)) {
                List<String> levelNames = new ArrayList<>();
                for (Long majorLevelId : majorLevelIds) {
                    levelNames.add(majorLevelNamesMap.get(majorLevelId));
                }
                keyWordVo.setMajorLevelNames(levelNames);
            }
        }
    }

    @Override
    public KeyWordVo findKeyWordById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        MpsKeyWord mpsKeyWord = mpsKeyWordMapper.selectById(id);
        KeyWordVo keyWordVo = BeanCopyUtils.objClone(mpsKeyWord, KeyWordVo::new);
        if (GeneralTool.isNotEmpty(keyWordVo)) {
            //课程等级
            List<MpsKeyWordMajorLevel> mpsKeyWordMajorLevels = mpsKeyWordMajorLevelMapper.selectList(Wrappers.<MpsKeyWordMajorLevel>lambdaQuery().eq(MpsKeyWordMajorLevel::getFkMpsKeyWordId, id));
            keyWordVo.setMajorLevelIds(Optional.ofNullable(mpsKeyWordMajorLevels).map(list -> list.stream()
                    .filter(mpsKeyWordMajorLevel -> mpsKeyWordMajorLevel.getFkMpsKeyWordId().equals(keyWordVo.getId()))
                    .map(MpsKeyWordMajorLevel::getFkMajorLevelId)
                    .collect(Collectors.toSet())).orElse(null));
            Map<Long, String> majorLevelNamesMap = institutionCenterClient.getMajorLevelNamesByIds(keyWordVo.getMajorLevelIds()).getData();
            keyWordVo.setMajorLevelNames(Optional.ofNullable(majorLevelNamesMap)
                    .map(map -> map.values().stream().collect(Collectors.toList()))
                    .orElse(null));

        }
        return keyWordVo;
    }

    /**
     * 添加验证
     * @param keyWordMajorLevelDto
     * @return
     */
    private boolean validateAdd(KeyWordMajorLevelDto keyWordMajorLevelDto) {
        List<MpsKeyWord> list = this.mpsKeyWordMapper.selectList(Wrappers.<MpsKeyWord>lambdaQuery().eq(MpsKeyWord::getKeyWord, keyWordMajorLevelDto.getKeyWord()));
        return GeneralTool.isEmpty(list);
    }

    /**
     * 更新验证
     * @param keyWordMajorLevelDto
     * @return
     */
    private boolean validateUpdate(KeyWordMajorLevelDto keyWordMajorLevelDto) {
        List<MpsKeyWord> list = this.mpsKeyWordMapper.selectList(Wrappers.<MpsKeyWord>lambdaQuery().eq(MpsKeyWord::getKeyWord, keyWordMajorLevelDto.getKeyWord()));
        System.out.println(list);
        return list.size() <= 0 || list.get(0).getId().equals(keyWordMajorLevelDto.getId());
    }
}
