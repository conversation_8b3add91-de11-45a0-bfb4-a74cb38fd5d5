package com.get.officecenter.service.impl;

import com.get.officecenter.entity.LeaveApplicationForm;
import com.get.officecenter.service.IAsyncReminderService;
import com.get.workflowcenter.feign.IWorkflowCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @author: Hardy
 * @create: 2022/8/15 15:22
 * @verison: 1.0
 * @description:
 */
@Service
@Slf4j
public class AsyncReminderServiceImpl implements IAsyncReminderService {


    @Resource
    private IWorkflowCenterClient workflowCenterClient;

    @Async("officeTaskExecutor")
    @Override
    public void doSendApplicationRemind(Map<String, String> headerMap, LeaveApplicationForm leaveApplicationForm, String title) {
        workflowCenterClient.doSendRemind(leaveApplicationForm,title);
    }
}
