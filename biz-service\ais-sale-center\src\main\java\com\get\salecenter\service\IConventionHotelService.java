package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.salecenter.vo.ConventionHotelVo;
import com.get.salecenter.entity.ConventionHotel;
import com.get.salecenter.dto.ConventionHotelDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/6 11:25
 * @verison: 1.0
 * @description: 酒店房型管理业务接口
 */
public interface IConventionHotelService {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    ConventionHotelVo findConventionHotelById(Long id);

    /**
     * 批量新增
     *
     * @param conventionHotelDtos
     */
    void batchAdd(List<ConventionHotelDto> conventionHotelDtos);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 修改
     *
     * @param conventionHotelDto
     * @return
     */
    ConventionHotelVo updateConventionHotel(ConventionHotelDto conventionHotelDto);

    /**
     * 列表
     *
     * @param conventionHotelDto
     * @param page
     * @return
     */
    List<ConventionHotelVo> getConventionHotels(ConventionHotelDto conventionHotelDto, Page page);

    /**
     * 上移下移
     *
     * @param conventionHotelDtos
     */
    void movingOrder(List<ConventionHotelDto> conventionHotelDtos);

    /**
     * 酒店下拉框数据
     *
     * @param conventionId
     * @return
     */
    List<String> getHotelList(Long conventionId);

    /**
     * 房型下拉框数据
     */
    List<String> getRoomTypeList(String hotel);

    /**
     * 根据峰会id和酒店名称查询对应ids
     *
     * @param hotel
     * @return
     */
    List<Long> getConventionHotelIdsByHotel(String hotel, Long conventionId);

    /**
     * 根据峰会id,酒店名称和房型查询对应id
     *
     * @param roomType
     * @param hotel
     * @return
     */
    Long getConventionHotelId(String roomType, String hotel, Long conventionId);

    /**
     * 酒店房型下拉框数据
     *
     * @param conventionId
     * @return
     */
    List<ConventionHotel> getConventionHotelList(Long conventionId);

    /**
     * 根据峰会id 查找该峰会下所有房型ids
     *
     * @param conventionId
     * @return
     */
    List<Long> getConventionHotelIds(Long conventionId);

    /**
     * 根据峰会id和房型名称 查询房型id
     *
     * @param conventionId 峰会id
     * @param roomTypeName 房型名称
     * @return
     */
    ConventionHotel getRoomTypeId(Long conventionId, String roomTypeName);
}
