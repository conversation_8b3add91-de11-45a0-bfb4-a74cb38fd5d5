package com.get.platformconfigcenter.dao.appmso;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.UserInstitutionCollection;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("appmsodb")
public interface UserInstitutionCollectionMapper extends BaseMapper<UserInstitutionCollection> {
//    int insert(UserInstitutionCollection record);

//    int insertSelective(UserInstitutionCollection record);
}