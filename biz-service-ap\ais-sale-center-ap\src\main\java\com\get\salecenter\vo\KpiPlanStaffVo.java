package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2024/4/16
 * @TIME: 16:35
 * @Description:
 **/
@Data
public class KpiPlanStaffVo extends BaseEntity implements Serializable {
    @ApiModelProperty(value = "KPI方案Id")
    private Long fkKpiPlanId;

    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;

    @ApiModelProperty(value = "员工姓名")
    private String staffName;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "统计方式，枚举：个人=1/团队(含业务下属)=2")
    private String countModeName;

    @ApiModelProperty(value = "统计方式，枚举：个人=1/团队(含业务下属)=2")
    private Integer countMode;

    @ApiModelProperty(value = "统计角色，枚举：BD=1/项目成员=2/PM=3")
    private String countRoleName;

    @ApiModelProperty(value = "统计角色，枚举：BD=1/项目成员=2/PM=3")
    //@Column(name = "count_role")
    private Integer countRole;
    @ApiModelProperty(value = "考核人员标签集合")
    private List<KpiPlanStaffLabelVo> KpiPlanStaffLabelDtoList;

    @ApiModelProperty(value = "目标设置人Id")
    //@Column(name = "fk_staff_id_add")
    private Long fkStaffIdAdd;


    @ApiModelProperty(value = "所有下级配置成员姓名列表")
    private List<String> staffNameChildren;

    @ApiModelProperty(value = "公司名称")
    private String companyName;
}
