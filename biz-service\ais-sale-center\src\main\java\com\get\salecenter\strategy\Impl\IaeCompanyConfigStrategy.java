package com.get.salecenter.strategy.Impl;

import com.get.salecenter.strategy.CompanyConfigStrategy;
import com.get.salecenter.utils.sale.SaleCenterConfigUtils;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * @author: Hardy
 * @create: 2024/3/19 18:28
 * @verison: 1.0
 * @description:
 */
@Component
public class IaeCompanyConfigStrategy implements CompanyConfigStrategy {

    private static final Long COMPANY_ID = 3L;

    private static final String COMPANY = "IAE";

    @Override
    public Long getCompanyId() {
        return COMPANY_ID;
    }

    @Override
    public String getCompany() {
        return COMPANY;
    }

    @Override
    public Optional<Object> getValue1Config(String key, SaleCenterConfigUtils saleCenterConfigUtils) {
        return saleCenterConfigUtils.getValue1Config(key, getCompany());
    }

    @Override
    public Optional<Object> getValue2Config(String key, SaleCenterConfigUtils saleCenterConfigUtils) {
        return saleCenterConfigUtils.getValue2Config(key, getCompany());
    }

    @Override
    public Optional<Object> getValue3Config(String key, SaleCenterConfigUtils saleCenterConfigUtils) {
        return saleCenterConfigUtils.getValue3Config(key, getCompany());
    }

    @Override
    public Optional<Object> getValue4Config(String key, SaleCenterConfigUtils saleCenterConfigUtils) {
        return saleCenterConfigUtils.getValue4Config(key, getCompany());
    }
}
