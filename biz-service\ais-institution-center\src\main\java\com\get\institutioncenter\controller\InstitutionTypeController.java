package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.vo.InstitutionTypeVo;
import com.get.institutioncenter.service.IInstitutionTypeService;
import com.get.institutioncenter.dto.InstitutionTypeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @author: Sea
 * @create: 2020/7/30 11:13
 * @verison: 1.0
 * @description: 学校类型管理控制器
 */
@Api(tags = "学校类型管理")
@RestController
@RequestMapping("/institution/institutionType")
public class InstitutionTypeController {

    @Resource
    private IInstitutionTypeService institutionTypeService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/学校类型管理/学校类型详情")
    @GetMapping("/{id}")
    public ResponseBo<InstitutionTypeVo> detail(@PathVariable("id") Long id) {
        InstitutionTypeVo data = institutionTypeService.findInstitutionTypeById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 批量新增信息
     *
     * @param institutionTypeDtos
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/学校类型管理/新增学校类型")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(InstitutionTypeDto.Add.class) ValidList<InstitutionTypeDto> institutionTypeDtos) {
        institutionTypeService.batchAdd(institutionTypeDtos);
        return SaveResponseBo.ok();
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/学校类型管理/删除学校类型")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        institutionTypeService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param institutionTypeDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/学校类型管理/更新学校类型")
    @PostMapping("update")
    public ResponseBo<InstitutionTypeVo> update(@RequestBody @Validated(InstitutionTypeDto.Update.class) InstitutionTypeDto institutionTypeDto) {
        return UpdateResponseBo.ok(institutionTypeService.updateInstitutionType(institutionTypeDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为搜索关键字(typeName类型名称)")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校类型管理/查询学校类型")
    @PostMapping("datas")
    public ResponseBo<InstitutionTypeVo> datas(@RequestBody SearchBean<InstitutionTypeDto> page) {
        List<InstitutionTypeVo> datas = institutionTypeService.getInstitutionTypes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 上移下移
     *
     * @param institutionTypeDtos
     * @return
     * @
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/学校类型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<InstitutionTypeDto> institutionTypeDtos) {
        institutionTypeService.movingOrder(institutionTypeDtos);
        return ResponseBo.ok();
    }


    /**
     * 学校类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学校类型下拉框数据", notes = "")
    @PostMapping("getInstitutionTypeList")
    public ResponseBo<BaseSelectEntity> getInstitutionTypeList() {
        return new ListResponseBo<>(institutionTypeService.getInstitutionTypeList());
    }

    /**
     * 学校K12类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学校K12类型下拉框数据", notes = "多选逗号分隔：0=幼儿园/1=小学/2=初中/3=高中")
    @GetMapping("getInstitutionK12TypeList")
    public ResponseBo<BaseSelectEntity> getInstitutionK12TypeList() {
        return new ListResponseBo<>(institutionTypeService.getInstitutionK12TypeList());
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "提供给学生住宿方式", notes = "多选逗号分隔：1=走读/2=寄宿/3=混合（即偶尔住校）")
    @GetMapping("getAccommodationTypeList")
    public ResponseBo<BaseSelectEntity> getAccommodationTypeList() {
        return new ListResponseBo<>(institutionTypeService.getAccommodationTypeList());
    }

    /**
     * 学校类型下拉框数据
     *
     * @return
     */
    @ApiIgnore
    @PostMapping("getAllInstitutionTypeName")
    public Map<Long, String> getAllInstitutionTypeName() {
        return institutionTypeService.getAllInstitutionTypeName();
    }

    /**
     * 学校类型下拉框数据
     *
     * @return
     */
    @ApiIgnore
    @GetMapping("getTypeIdStringByCourseId")
    public String getTypeIdStringByCourseId(@RequestParam Long id) {
        return institutionTypeService.getTypeIdStringByCourseId(id);
    }
}
