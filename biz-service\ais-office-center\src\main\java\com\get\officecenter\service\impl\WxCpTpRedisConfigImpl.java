package com.get.officecenter.service.impl;

import com.get.common.cache.CacheNames;
import com.get.core.redis.cache.GetRedis;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxAccessToken;
import me.chanjar.weixin.cp.config.impl.WxCpTpDefaultConfigImpl;

/**
 * @author: Hardy
 * @create: 2022/8/10 10:41
 * @verison: 1.0
 * @description: 企业微信第三方，缓存支持 1.suiteAccessToken
 */
@Slf4j
public class WxCpTpRedisConfigImpl extends WxCpTpDefaultConfigImpl {

    private GetRedis getRedis;


    public WxCpTpRedisConfigImpl(GetRedis getRedis) {
        this.getRedis = getRedis;
    }

    @Override
    public void setSuiteId(String corpId) {
//        SUITE_ACCESS_TOKEN_KEY += corpId;
        super.setSuiteId(corpId);
    }


    /**-------------------------- suite ticket -------------------------**/
    @Override
    public String getSuiteTicket() {
        if (isSuiteTicketExpired()){
            String result = "{\"errcode\":40085, \"errmsg\":\"invaild suite ticket\"}";
            System.err.println(result);
            return null;
        }
        return getRedis.get(CacheNames.SUITE_TICKET_KEY);
    }


    @Override
    public boolean isSuiteTicketExpired() {
        return getRedis.ttl(CacheNames.SUITE_TICKET_KEY) < 2;
    }


    @Override
    public synchronized void updateSuiteTicket(String suiteTicket, int expiresInSeconds) {
        log.info("redis缓存更新成功：" + suiteTicket);
        getRedis.setEx(CacheNames.SUITE_TICKET_KEY, suiteTicket, (long) expiresInSeconds - 200);
    }


    @Override
    public void expireSuiteTicket() {
        getRedis.expire(CacheNames.SUITE_TICKET_KEY, 0);
    }


    /**-------------------------- suite access token -------------------------**/

    @Override
    public String getSuiteAccessToken() {
        if (isSuiteAccessTokenExpired()){
            return null;
        }
        return getRedis.get(CacheNames.SUITE_ACCESS_TOKEN_KEY);
    }

    @Override
    public boolean isSuiteAccessTokenExpired() {
        return getRedis.ttl(CacheNames.SUITE_ACCESS_TOKEN_KEY) < 2;
    }

    @Override
    public synchronized void updateSuiteAccessToken(WxAccessToken suiteAccessToken) {
        this.updateSuiteAccessToken(suiteAccessToken.getAccessToken(), suiteAccessToken.getExpiresIn());
    }

    @Override
    public synchronized void updateSuiteAccessToken(String suiteAccessToken, int expiresInSeconds) {
        getRedis.setEx(CacheNames.SUITE_ACCESS_TOKEN_KEY,suiteAccessToken, (long)expiresInSeconds - 200);
    }

    @Override
    public void expireSuiteAccessToken() {
        getRedis.expire(CacheNames.SUITE_ACCESS_TOKEN_KEY, 0);
    }



    /**-------------------------- jsapi ticket -------------------------**/

    public String getJsApiTicket(String authCorpId) {
        System.out.println("缓存中获取jsApiTicket");
        if (isJsApiTicketExpired(authCorpId)){
            return null;
        }
        return getRedis.get(CacheNames.JS_API_TICKET_KEY + authCorpId);
    }

    public boolean isJsApiTicketExpired(String authCorpId) {
        return getRedis.ttl(CacheNames.JS_API_TICKET_KEY + authCorpId) < 2;
    }


    // authCorpId 使用第三方企业对应的id
    public synchronized void updateJsApiTicket(String jsApiTicket, int expiresInSeconds, String authCorpId) {
        getRedis.setEx(CacheNames.JS_API_TICKET_KEY + authCorpId,jsApiTicket, (long)expiresInSeconds - 200);
    }

    public void expireJsApiTicket(String authCorpId) {
        getRedis.expire(CacheNames.JS_API_TICKET_KEY + authCorpId, 0);
    }

}
