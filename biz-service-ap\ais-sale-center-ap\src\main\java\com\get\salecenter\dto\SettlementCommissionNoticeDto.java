package com.get.salecenter.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SettlementCommissionNoticeDto {

    @ApiModelProperty("批次号")
    private String numSettlementBatch;

    @ApiModelProperty("应付id")
    @NotNull(message = "应付id不能为空")
    private Long fkPayablePlanId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("佣金通知")
    private String commissionNotice;

    @ApiModelProperty("附件")
    private List<MediaAndAttachedDto> mediaAndAttachedVos;
}
