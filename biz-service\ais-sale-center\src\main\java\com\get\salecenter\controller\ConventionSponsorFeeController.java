package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.vo.ConventionSponsorFeeVo;
import com.get.salecenter.service.IConventionSponsorFeeService;
import com.get.salecenter.dto.ConventionSponsorFeeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/5/8 11:08
 * @verison: 1.0
 * @description:
 */
@Api(tags = "峰会赞助费用类型管理")
@RestController
@RequestMapping("sale/conventionSponsorFee")
public class ConventionSponsorFeeController {
    @Resource
    private IConventionSponsorFeeService conventionSponsorFeeService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ConventionSponsorFeeVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/峰会赞助费用类型管理/峰会赞助费用类型详情")
    @GetMapping("/{id}")
    public ResponseBo<ConventionSponsorFeeVo> detail(@PathVariable("id") Long id) {
        ConventionSponsorFeeVo data = conventionSponsorFeeService.findConventionSponsorFeeById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增信息
     * @Param [conventionSponsorFeeDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/峰会赞助费用类型管理/新增峰会赞助费用类型")
    @PostMapping("add")
    public ResponseBo add(@RequestBody  @Validated(ConventionSponsorFeeDto.Add.class)  ConventionSponsorFeeDto conventionSponsorFeeDto) {
        return SaveResponseBo.ok(this.conventionSponsorFeeService.addConventionSponsorFee(conventionSponsorFeeDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/峰会赞助费用类型管理/删除峰会赞助费用类型")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        this.conventionSponsorFeeService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ConventionSponsorFeeVo>
     * @Description :修改信息
     * @Param [conventionSponsorFeeDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/峰会赞助费用类型管理/更新峰会赞助费用类型")
    @PostMapping("update")
    public ResponseBo<ConventionSponsorFeeVo> update(@RequestBody @Validated(ConventionSponsorFeeDto.Update.class) ConventionSponsorFeeDto conventionSponsorFeeDto) {
        return UpdateResponseBo.ok(conventionSponsorFeeService.updateConventionSponsorFee(conventionSponsorFeeDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ConventionSponsorFeeVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/峰会赞助费用类型管理/查询峰会赞助费用类型")
    @PostMapping("datas")
    public ResponseBo<ConventionSponsorFeeVo> datas(@RequestBody SearchBean<ConventionSponsorFeeDto> page) {
        List<ConventionSponsorFeeVo> datas = conventionSponsorFeeService.getConventionSponsorFees(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :上移下移
     * @Param [eventTypeVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动类型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<ConventionSponsorFeeDto> conventionSponsorFeeDtos) {
        conventionSponsorFeeService.movingOrder(conventionSponsorFeeDtos);
        return ResponseBo.ok();
    }
}
