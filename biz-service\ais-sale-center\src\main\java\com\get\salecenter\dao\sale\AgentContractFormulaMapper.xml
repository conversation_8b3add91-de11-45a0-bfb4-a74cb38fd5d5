<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.AgentContractFormulaMapper">
  <resultMap id="BaseResultMap" type="com.get.salecenter.entity.AgentContractFormula">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_agent_id" jdbcType="BIGINT" property="fkAgentId" />
    <result column="fk_institution_provider_id" jdbcType="BIGINT" property="fkInstitutionProviderId" />
    <result column="fk_contract_formula_id" jdbcType="BIGINT" property="fkContractFormulaId" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="fk_currency_type_num" jdbcType="VARCHAR" property="fkCurrencyTypeNum" />
    <result column="limit_amount_ag" jdbcType="DECIMAL" property="limitAmountAg" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="view_order" jdbcType="INTEGER" property="viewOrder" />
    <result column="is_active" jdbcType="BIT" property="isActive" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.AgentContractFormula" keyProperty="id" useGeneratedKeys="true">
    insert into m_agent_contract_formula
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkAgentId != null">
        fk_agent_id,
      </if>
      <if test="fkInstitutionProviderId != null">
        fk_institution_provider_id,
      </if>
      <if test="fkContractFormulaId != null">
        fk_contract_formula_id,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="fkCurrencyTypeNum != null">
        fk_currency_type_num,
      </if>
    <if test="limitAmountAg != null">
      limit_amount_ag,
    </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="isActive != null">
        is_active,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkAgentId != null">
        #{fkAgentId,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionProviderId != null">
        #{fkInstitutionProviderId,jdbcType=BIGINT},
      </if>
      <if test="fkContractFormulaId != null">
        #{fkContractFormulaId,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="fkCurrencyTypeNum != null">
        #{fkCurrencyTypeNum,jdbcType=VARCHAR},
      </if>
      <if test="limitAmountAg != null">
        #{limitAmountAg,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="isActive != null">
        #{isActive,jdbcType=BIT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select
     IFNULL(max(view_order)+1,0) view_order
    from
     m_agent_contract_formula
    where
     fk_agent_id = #{agentId}
  </select>

  <select id="agentContractFormulaIsEmpty" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id from m_agent_contract_formula where fk_agent_id=#{agentId} LIMIT 1
    </select>
  <select id="getAgentContractFormulasByFormula" resultMap="BaseResultMap">
    select * from m_agent_contract_formula
    <where>
      <if test="agentId!=null">
        and fk_agent_id = #{agentId}
      </if>
      and  is_active = 1 and (start_time <![CDATA[ <= ]]> now() or start_time is null) and (end_time <![CDATA[ >= ]]> now() or end_time is null)
      order by gmt_modified desc, gmt_create DESC
    </where>
  </select>
</mapper>