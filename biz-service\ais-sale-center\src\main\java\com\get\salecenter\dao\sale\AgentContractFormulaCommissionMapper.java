package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.AgentContractFormulaCommission;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface AgentContractFormulaCommissionMapper extends BaseMapper<AgentContractFormulaCommission> {

    /**
     * @return java.lang.Integer
     * @Description :获取最大期数
     * @Param [agentContractFormulaId]
     * <AUTHOR>
     */
    Integer getMaxStep(Long agentContractFormulaId);
}