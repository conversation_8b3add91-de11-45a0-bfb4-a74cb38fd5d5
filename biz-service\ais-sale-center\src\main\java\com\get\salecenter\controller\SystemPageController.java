package com.get.salecenter.controller;

import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.service.ISystemPageService;
import com.get.salecenter.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2021/2/2
 * @TIME: 17:04
 * @Description:
 **/
@Api(tags = "系统首页")
@RestController
@RequestMapping("sale/systemPage")
public class SystemPageController {
    @Resource
    private ISystemPageService systemPageService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentVo>
     * @Description: 列表数据
     * @Param [statisticsFlag true:统计申请计划数  false:统计学生数]
     * <AUTHOR>
     */
    @ApiOperation(value = "系统首页数据", notes = "")
    @OperationLogger(type = LoggerOptTypeConst.DETAIL, description = "系统首页")
    @PostMapping("datas")
    public ResponseBo<SystemPageVo1> datas(@RequestParam List<Long> companyIds, @RequestParam("num") String num, @RequestParam("year") String year
            , @RequestParam("statisticsFlag") Boolean statisticsFlag) {
        return new ResponseBo<>(systemPageService.getInitialDatas(companyIds, num, year, statisticsFlag));
    }

//    @VerifyLogin(IsVerify = false)
//    @ApiOperation(value = "展示系统首页数据", notes = "")
//    @OperationLogger(type = LoggerOptTypeConst.DETAIL, description = "系统首页")
//    @PostMapping("newDates")
//    public ResponseBo<SystemPageVo1> newDates(@RequestParam List<Long> companyIds, @RequestParam("num") String num, @RequestParam("year") String year) {
//        return new ResponseBo<>(systemPageService.getInitialDatas(companyIds, num, year, statisticsFlag));
//    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "首页年份下拉框", notes = "")
    @OperationLogger(type = LoggerOptTypeConst.LIST, description = "首页年份下拉框")
    @PostMapping("yearSelect")
    public ResponseBo<BaseSelectEntity> yearSelect(@RequestParam List<Long> companyIds) {
        return new ListResponseBo<>(systemPageService.yearSelect(companyIds));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentVo>
     * @Description: 获取总学生数
     * @Param [statisticsFlag true:统计申请计划数  false:统计学生数]
     * <AUTHOR>
     */
    @ApiOperation(value = "系统首页数据-获取总学生数", notes = "")
    @OperationLogger(type = LoggerOptTypeConst.DETAIL, description = "系统首页数据-获取总学生数")
    @PostMapping("getStudentTotalSum")
    public ResponseBo<Long> getStudentTotalSum(@RequestParam List<Long> companyIds, @RequestParam("num") String num, @RequestParam("year") String year,
                                               @RequestParam("statisticsFlag") Boolean statisticsFlag) {
        return new ResponseBo<>(systemPageService.getStudentTotalSum(companyIds, null, num, year, statisticsFlag));
    }

//    @ApiOperation(value = "系统首页数据-获取总学生申请数", notes = "")
//    @OperationLogger(type = LoggerOptTypeConst.DETAIL, description = "系统首页数据-获取总学生申请数")
//    @PostMapping("getStudentItemTotalSum")
//    public ResponseBo<Long> getStudentItemTotalSum(@RequestParam List<Long> companyIds, @RequestParam("year") String year) {
//        return new ResponseBo<>(systemPageService.getStudentItemTotalSum(companyIds, SecureUtil.getCountryIds(), year));
//    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentVo>
     * @Description: 获取总学生数
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "系统首页数据-获取学生申请记录", notes = "")
    @OperationLogger(type = LoggerOptTypeConst.DETAIL, description = "系统首页数据-获取学生申请记录")
    @PostMapping("getStudentCount")
    public ResponseBo<List<StudentCountVo>> getStudentCountRecords(@RequestParam List<Long> companyIds, @RequestParam("num") String num, @RequestParam("year") String year) {
        return new ResponseBo<>(systemPageService.getStudentCountRecords(companyIds, num, year));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentVo>
     * @Description: 获取国家学生数
     * @Param [statisticsFlag true:统计申请计划数  false:统计学生数 ]
     * <AUTHOR>
     */
    @ApiOperation(value = "系统首页数据-国家学生数/学习计划数", notes = "")
    @OperationLogger(type = LoggerOptTypeConst.DETAIL, description = "系统首页数据-国家学生数/学习计划数")
    @PostMapping("getCountryStudentCount")
    public ResponseBo<List<WorldHistogramVo>> getCountryStudentCount(@RequestParam List<Long> companyIds, @RequestParam("num") String num,
                                                                     @RequestParam("year") String year, @RequestParam("statisticsFlag") Boolean statisticsFlag) {
        return new ResponseBo<>(systemPageService.getCountryStudentCount(companyIds, null, num, year, statisticsFlag));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentVo>
     * @Description: 获取国家学生数
     * @Param [statisticsFlag true:统计申请计划数  false:统计学生数 ]
     * <AUTHOR>
     */
    @ApiOperation(value = "系统首页数据-州省学生数", notes = "")
    @OperationLogger(type = LoggerOptTypeConst.DETAIL, description = "系统首页数据-州省学生数")
    @PostMapping("getStateStudentCount")
    public ResponseBo<List<WorldHistogramVo>> getStateStudentCount(@RequestParam List<Long> companyIds, @RequestParam("num") String num,
                                                                   @RequestParam("year") String year, @RequestParam("statisticsFlag") Boolean statisticsFlag) {
        return new ResponseBo<>(systemPageService.getStateStudentCount(companyIds, null, num, year, statisticsFlag));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentVo>
     * @Description: 世界地图
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "系统首页数据-世界地图", notes = "statisticsFlag  true:统计申请计划数  false:统计学生数")
    @OperationLogger(type = LoggerOptTypeConst.DETAIL, description = "系统首页数据-世界地图")
    @PostMapping("getWorldMap")
    public ResponseBo<List<WorldMapVo>> getWorldMap(@RequestParam List<Long> companyIds, @RequestParam("num") String num, @RequestParam("year") String year, @RequestParam("statisticsFlag") Boolean statisticsFlag) {
        return new ResponseBo<>(systemPageService.getWorldMap(companyIds, null, num, year, statisticsFlag));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentVo>
     * @Description: 经纬度信息
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "系统首页数据-经纬度信息", notes = "")
    @OperationLogger(type = LoggerOptTypeConst.DETAIL, description = "系统首页数据-经纬度信息")
    @PostMapping("getLonlat")
    public ResponseBo<SystemPageLonlatVo> getLonlat(@RequestParam List<Long> companyIds, @RequestParam("num") String num, @RequestParam("year") String year) {
        return new ResponseBo<>(systemPageService.getLonlat(companyIds, num, year));
    }
    @ApiOperation(value = "系统首页数据-学生申请状态", notes = "")
    @OperationLogger(type = LoggerOptTypeConst.DETAIL, description = "系统首页数据-学生申请状态")
    @PostMapping("getStudentApplicationStatusCount")
    public ResponseBo<List<StudentApplicationStatusCountVo>> getStudentStatusCount(@RequestParam List<Long> companyIds, @RequestParam("num") String num, @RequestParam("year") String year) {
        return new ResponseBo<>(systemPageService.getStudentApplicationStatusCount(companyIds, num, year));
    }

    @ApiOperation(value = "系统首页数据-院校学生统计", notes = "")
    @OperationLogger(type = LoggerOptTypeConst.DETAIL, description = "系统首页数据-院校学生统计")
    @PostMapping("getSchoolStudentCount")
    public ResponseBo<List<AssistantVo>> getStudentStatisticsCount(@RequestParam List<Long> companyIds, @RequestParam("num") String num, @RequestParam("year") String year) {
        return new ResponseBo<>(systemPageService.getStudentStatisticsCount(companyIds, num, year));
    }
    @ApiOperation(value = "系统首页数据-送生统计占比", notes = "")
    @OperationLogger(type = LoggerOptTypeConst.DETAIL, description = "系统首页数据-送生统计占比")
    @PostMapping("getStudentStatistics")
    public ResponseBo<List<AssistantVo>> getStudentStatistics(@RequestParam List<Long> companyIds, @RequestParam("num") String num, @RequestParam("year") String year) {
        return new ResponseBo<>(systemPageService.getStudentStatistics(companyIds, num, year));
    }

    @ApiOperation(value = "系统首页数据-COE月度统计", notes = "")
    @OperationLogger(type = LoggerOptTypeConst.DETAIL, description = "系统首页数据-COE月度统计")
    @PostMapping("getCOEMonthlyStatistics")
    public ResponseBo<COEMonthlyStatisticsVo> getCOEMonthlyStatistics(@RequestParam List<Long> companyIds, @RequestParam("num") String num, @RequestParam("year") String year) {
        return new ResponseBo<>(systemPageService.getCOEMonthlyStatistics(companyIds, num, year));
    }

    @ApiOperation(value = "系统首页数据-申请计划，应收，实收，实付统计", notes = "")
    @OperationLogger(type = LoggerOptTypeConst.DETAIL, description = "系统首页数据-申请计划，应收，实收，实付统计")
    @PostMapping("getFinanceStatistic")
    public ResponseBo<Map<String,List<FinanceStatisticVO>>> getFinanceStatistic(@RequestParam List<Long> companyIds, @RequestParam("num") String num, @RequestParam("year") String year, @RequestParam(value = "currencyTypeNum" ,required = false)String currencyTypeNum) {
        return new ResponseBo<>(systemPageService.getFinanceStatistic(companyIds, num, year,currencyTypeNum));
    }

    @ApiOperation(value = "系统首页数据-大区学生数", notes = "")
    @OperationLogger(type = LoggerOptTypeConst.DETAIL, description = "系统首页数据-大区学生数")
    @PostMapping("getRegionStudentCount")
    public ResponseBo<List<AreaRegionStatisticsVo>> getRegionStudentCount(@RequestParam List<Long> companyIds, @RequestParam("num") String num, @RequestParam("year") String year) {
        return new ResponseBo<>(systemPageService.getRegionStudentCount(companyIds, num, year));
    }




}
