package com.get.financecenter.service.impl;

import cn.hutool.poi.excel.BigExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.CommonUtil;
import com.get.common.utils.DataConverter;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.cache.utils.CacheUtil;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.UserInfo;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestHeaderHandler;
import com.get.file.utils.FileUtils;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.financecenter.dao.PaymentFormItemMapper;
import com.get.financecenter.dao.PaymentFormMapper;
import com.get.financecenter.dao.ReceiptFormItemMapper;
import com.get.financecenter.vo.AgencyCommissionSettlementServiceExport;
import com.get.financecenter.dto.AgentSettlementDto;
import com.get.financecenter.dto.AgentSettlementGrossAmountExport;
import com.get.financecenter.dto.PaymentFormDto;
import com.get.financecenter.dto.PaymentFormItemDto;
import com.get.financecenter.dto.ReceiptFormDto;
import com.get.financecenter.dto.query.AgentSettlementQueryDto;
import com.get.financecenter.dto.query.ReceiptFormQueryDto;
import com.get.financecenter.entity.PaymentForm;
import com.get.financecenter.entity.PaymentFormItem;
import com.get.financecenter.entity.ReceiptFormItem;
import com.get.financecenter.service.AsyncExportService;
import com.get.financecenter.service.ICurrencyTypeService;
import com.get.financecenter.service.IExchangeRateService;
import com.get.financecenter.service.IPaymentFormService;
import com.get.financecenter.service.IReceiptFormService;
import com.get.financecenter.utils.TemplateExcelUtils;
import com.get.financecenter.vo.AgentSettlementAccommodationVo;
import com.get.financecenter.vo.AgentSettlementGrossAmountVo;
import com.get.financecenter.vo.AgentSettlementInsuranceVo;
import com.get.financecenter.vo.AgentSettlementItemVo;
import com.get.financecenter.vo.AgentSettlementOfferItemVo;
import com.get.financecenter.vo.AgentSettlementServiceFeeVo;
import com.get.financecenter.vo.CurrencyTypeVo;
import com.get.financecenter.vo.PaymentFormExportVo;
import com.get.financecenter.vo.PaymentFormItemExportVo;
import com.get.financecenter.vo.PaymentFormItemVo;
import com.get.financecenter.vo.PaymentFormVo;
import com.get.financecenter.vo.ReceiptFormExportVo;
import com.get.financecenter.vo.ReceiptFormItemExportVo;
import com.get.financecenter.vo.ReceiptFormItemVo;
import com.get.financecenter.vo.ReceiptFormVo;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.institutioncenter.vo.AreaCountryVo;
import com.get.institutioncenter.vo.InstitutionProviderVo;
import com.get.institutioncenter.vo.InstitutionVo;
import com.get.permissioncenter.entity.StaffDownload;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.salecenter.vo.AgentContractAccountVo;
import com.get.salecenter.vo.PayablePlanVo;
import com.get.salecenter.vo.ReceivablePlanVo;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AsyncExportServiceImpl implements AsyncExportService {

    @Resource
    private PaymentFormMapper paymentFormMapper;

    @Resource
    private PaymentFormItemMapper paymentFormItemMapper;

    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Resource
    private ISaleCenterClient saleCenterClient;

    @Resource
    private IExchangeRateService exchangeRateService;

    @Resource
    private ICurrencyTypeService currencyTypeService;

    @Resource
    private IFileCenterClient fileCenterClient;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Lazy
    @Resource
    private IPaymentFormService paymentFormService;

    @Lazy
    @Resource
    private IReceiptFormService receiptFormService;

    @Resource
    private ReceiptFormItemMapper receiptFormItemMapper;

    @Async
    @Override
    public void asyncExportPaymentFormItemExcel(PaymentFormDto paymentFormDto, Map<String, String> headerMap, UserInfo user, String locale) {
        RequestHeaderHandler.setHeaderMap(headerMap);
        log.info("asyncExportPaymentFormItemExcel userInfo{}", user);
        Long staffId = user.getStaffId();
        StaffDownload download = new StaffDownload();
        try {
            Map<Long, String> cName = permissionCenterClient.getCompanyNamesByIds(new HashSet<>(paymentFormDto.getFkCompanyIds())).getData();
            Collection<String> values = cName.values();
            CommonUtil<PaymentFormDto> commonUtil = new CommonUtil<>();
            String description = commonUtil.getFiledValue(paymentFormDto, PaymentFormDto.class);
            download.setFkStaffId(staffId)
                    .setOptKey(FileTypeEnum.D_LIST_EXPORT.key)
                    .setOptDescription(String.format("【付款管理】《付款单明细》 公司=%s" + description, values))
                    .setStatus(1)
                    .setGmtCreate(new Date());
            download.setGmtCreateUser(user.getLoginId());
            download.setId(permissionCenterClient.addDownloadRecord(download));
            //查询所有付款单
            LambdaQueryWrapper<PaymentForm> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            if (GeneralTool.isNotEmpty(paymentFormDto.getFkCompanyId())) {
                lambdaQueryWrapper.eq(PaymentForm::getFkCompanyId, paymentFormDto.getFkCompanyId());
            }
            List<PaymentForm> paymentForms = paymentFormMapper.selectList(lambdaQueryWrapper);

            //根据所有付款单ids获取付款单子项列表
            List<Long> formIds = paymentForms.stream().map(PaymentForm::getId).collect(Collectors.toList());
            PaymentFormItemDto paymentFormItemDto = new PaymentFormItemDto();
            paymentFormItemDto.setFkPaymentFormIds(formIds);
            LambdaQueryWrapper<PaymentFormItem> paymentFormItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
            if (GeneralTool.isNotEmpty(paymentFormItemDto.getFkPaymentFormIds())) {
                paymentFormItemLambdaQueryWrapper.in(PaymentFormItem::getFkPaymentFormId, paymentFormItemDto.getFkPaymentFormIds());
            }
            List<PaymentFormItem> paymentFormItems = paymentFormItemMapper.selectList(paymentFormItemLambdaQueryWrapper);
            List<PaymentFormItemVo> paymentFormItemDtoList =
                    paymentFormItems.stream().map(paymentFormItem -> BeanCopyUtils.objClone(paymentFormItem, PaymentFormItemVo::new)).collect(Collectors.toList());
            //目标类型为提供商
            Set<Long> institutionProviderIds = paymentForms.stream().filter(r -> TableEnum.INSTITUTION_PROVIDER.key.equals(r.getFkTypeKey()))
                    .map(PaymentForm::getFkTypeTargetId).collect(Collectors.toSet());
            //获取目标类型为提供商的名称
            Map<Long, String> institutionProviderNameMap = new HashMap<>();
            if (GeneralTool.isNotEmpty(institutionProviderIds)) {
                Result<Map<Long, String>> resultinstitutionProviderNamesByIds = institutionCenterClient.getInstitutionProviderNamesByIds(institutionProviderIds);
                if (resultinstitutionProviderNamesByIds.isSuccess()) {
                    institutionProviderNameMap = resultinstitutionProviderNamesByIds.getData();
                }
            }

            //目标类型为学生
            Set<Long> studentIds = paymentForms.stream().filter(r -> TableEnum.SALE_STUDENT.key.equals(r.getFkTypeKey()))
                    .map(PaymentForm::getFkTypeTargetId).collect(Collectors.toSet());
            //获取目标类型为学生的名称map
            Map<Long, String> studentNameMap = new HashMap<>();
            if (GeneralTool.isNotEmpty(studentIds)) {
                Result<Map<Long, String>> resultNameByIds = saleCenterClient.getStudentNameByIds(studentIds);
                if (resultNameByIds.isSuccess()) {
                    studentNameMap = resultNameByIds.getData();
                }
            }

            //目标类型为代理
            Set<Long> agentIds = paymentForms.stream().filter(r -> TableEnum.SALE_AGENT.key.equals(r.getFkTypeKey()))
                    .map(PaymentForm::getFkTypeTargetId).collect(Collectors.toSet());
            //获取目标类型为代理的名称map
            Map<Long, String> agentNameMap = new HashMap<>();
            if (GeneralTool.isNotEmpty(agentIds)) {
                Result<Map<Long, String>> result1 = saleCenterClient.getAgentNamesByIds(agentIds);
                if (result1.isSuccess() && GeneralTool.isNotEmpty(result1.getData())) {
                    agentNameMap = result1.getData();
                }
            }
            //根据付款单子项列表关联的应付计划ids，获取应付计划列表信息
            Set<Long> planIds = paymentFormItemDtoList.stream().map(PaymentFormItemVo::getFkPayablePlanId).collect(Collectors.toSet());
            List<PayablePlanVo> payablePlanVos = new ArrayList<>();
            if (GeneralTool.isNotEmpty(planIds)) {
                payablePlanVos = saleCenterClient.getPayablePlanByIds(planIds).getData();
            }

            Map<Long, PayablePlanVo> payablePlanDtoMap = new HashMap<>();
            if (GeneralTool.isNotEmpty(payablePlanVos)) {
                payablePlanDtoMap = payablePlanVos.stream().collect(Collectors.toMap(PayablePlanVo::getId, Function.identity()));
            }

            List<PaymentFormItemExportVo> paymentFormItemExportVos = new ArrayList<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Set<String> nums = payablePlanDtoMap.values().stream().map(PayablePlanVo::getFkCurrencyTypeNum).collect(Collectors.toSet());
            Map<String, String> planNums = currencyTypeService.getCurrencyNamesByNums(nums);
            Set<Long> collect = paymentFormItemDtoList.stream().map(PaymentFormItemVo::getFkPaymentFormId).collect(Collectors.toSet());
            List<PaymentForm> paymentFormList = paymentFormMapper.selectList(Wrappers.<PaymentForm>lambdaQuery().in(PaymentForm::getId, collect));
            CommonUtil<PaymentForm> convertUtils = new CommonUtil<>();
            Map<Long, Object> convertItem = convertUtils.convertSupperItem(paymentFormList);
            Set<String> payNums = paymentFormList.stream().map(PaymentForm::getFkCurrencyTypeNum).collect(Collectors.toSet());
            Map<String, String> payFormNums = currencyTypeService.getCurrencyNamesByNums(payNums);
            for (PaymentFormItemVo paymentFormItemVo : paymentFormItemDtoList) {
                PaymentFormItemExportVo paymentFormItemExportVo = BeanCopyUtils.objClone(paymentFormItemVo, PaymentFormItemExportVo::new);
                //------付款单的编号、学校、总金额----
                if (paymentFormItemExportVo != null && GeneralTool.isNotEmpty(paymentFormItemVo.getFkPaymentFormId())) {
                    //-------应付计划类型、应付目标对象、应付币种、总应付金额--------
                    PayablePlanVo payablePlanVo = payablePlanDtoMap.get(paymentFormItemVo.getFkPayablePlanId());
                    if (GeneralTool.isNotEmpty(payablePlanVo)) {
                        paymentFormItemExportVo.setFkTypeName(payablePlanVo.getFkTypeName());
                        paymentFormItemExportVo.setTargetNames(payablePlanVo.getTargetNames());
                        paymentFormItemExportVo.setPayablePlanAmount(payablePlanVo.getPayableAmount());
                        paymentFormItemExportVo.setPayablePlanCurrencyName(planNums.get(payablePlanVo.getFkCurrencyTypeNum()));
                    }
                    PaymentForm paymentForm = (PaymentForm) convertItem.get(paymentFormItemVo.getFkPaymentFormId());
                    if (GeneralTool.isNotEmpty(paymentForm)) {
                        //实付币种
                        String fkCurrencyTypeNum = paymentForm.getFkCurrencyTypeNum();
                        if (GeneralTool.isNotEmpty(fkCurrencyTypeNum)) {
                            paymentFormItemExportVo.setPayFormCurrency(payFormNums.get(fkCurrencyTypeNum));
                        }

                        //付款单编号
                        paymentFormItemExportVo.setNumSystem(paymentForm.getNumSystem());
                        //目标名称
                        if (GeneralTool.isNotEmpty(paymentForm.getFkTypeTargetId())) {
                            String targetName = null;
                            if (TableEnum.INSTITUTION_PROVIDER.key.equals(paymentForm.getFkTypeKey())) {
                                targetName = institutionProviderNameMap.get(paymentForm.getFkTypeTargetId());
                            } else if (TableEnum.SALE_STUDENT.key.equals(paymentForm.getFkTypeKey())) {
                                targetName = studentNameMap.get(paymentForm.getFkTypeTargetId());
                            } else if (TableEnum.SALE_AGENT.key.equals(paymentForm.getFkTypeKey())) {
                                targetName = agentNameMap.get(paymentForm.getFkTypeTargetId());
                            }
                            paymentFormItemExportVo.setTargetName(targetName);
                        }
                        //总金额
                        paymentFormItemExportVo.setAmountRmb(paymentForm.getAmountRmb());
                    }
                }

                //日期处理
                if (GeneralTool.isNotEmpty(paymentFormItemVo.getGmtCreate())) {
                    paymentFormItemExportVo.setGmtCreateDate(sdf.format(paymentFormItemVo.getGmtCreate()));
                }

                paymentFormItemExportVos.add(paymentFormItemExportVo);
            }
            Map<String, Object> param = new HashMap<>();
            param.put("list", paymentFormItemExportVos);
            MultipartFile multipartFile = TemplateExcelUtils.packageExcel("付款单子项列表.xlsx", "PaymentFormItemInfo.xlsx", param);
            Result<List<FileDto>> upload = fileCenterClient.uploadAppendix(new MultipartFile[]{multipartFile}, LoggerModulesConsts.EXPORT);
            boolean flag = false;
            if (upload.isSuccess()) {
                List<FileDto> data = upload.getData();
                if (data != null && !data.isEmpty()) {
                    FileDto fileDto = data.get(0);
                    if (fileDto.getFileGuid() != null) {
                        download.setStatus(2)
                                .setFkFileGuid(fileDto.getFileGuid());
                        flag = true;
                    }
                }
            }
            if (!flag) {
                download.setStatus(0);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info(e.getMessage());
            download.setStatus(0);
            download.setRemark(LocaleMessageUtils.getMessage("no_data_search"));
        } finally {
            download.setGmtModifiedUser(user.getLoginId());
            download.setGmtModified(new Date());
            permissionCenterClient.updateDownload(download);
        }
    }

    @Async
    @Override
    public void asyncExportPaymentFormExcel(PaymentFormDto paymentFormDto, Map<String, String> headerMap, UserInfo user, String locale) {
        RequestHeaderHandler.setHeaderMap(headerMap);
        log.info("asyncExportPaymentFormExcel userInfo{}", user);
        Long staffId = user.getStaffId();
        StaffDownload download = new StaffDownload();
        try {
            Map<Long, String> cName = permissionCenterClient.getCompanyNamesByIds(new HashSet<>(paymentFormDto.getFkCompanyIds())).getData();
            Collection<String> values = cName.values();
            CommonUtil<PaymentFormDto> commonUtil = new CommonUtil<>();
            String description = commonUtil.getFiledValue(paymentFormDto, PaymentFormDto.class);
            download.setFkStaffId(staffId)
                    .setOptKey(FileTypeEnum.D_LIST_EXPORT.key)
                    .setOptDescription(String.format("【付款管理】《付款单汇总表》 公司=%s" + description, values))
                    .setStatus(1)
                    .setGmtCreate(new Date());
            download.setGmtCreateUser(user.getLoginId());
            download.setId(permissionCenterClient.addDownloadRecord(download));
            LambdaQueryWrapper<PaymentForm> wrapper = paymentFormService.getWrapper(paymentFormDto);
            List<PaymentForm> paymentForms = paymentFormMapper.selectList(wrapper);
            List<PaymentFormVo> collect = BeanCopyUtils.copyListProperties(paymentForms, PaymentFormVo::new);
            collect = paymentFormService.packageData(collect, paymentFormDto, locale);
            List<PaymentFormExportVo> paymentFormExportVos = new ArrayList<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            for (PaymentFormVo paymentFormVo : collect) {
                PaymentFormExportVo paymentFormExportVo = BeanCopyUtils.objClone(paymentFormVo, PaymentFormExportVo::new);
                if (GeneralTool.isNotEmpty(paymentFormVo.getStatus())) {
                    if (paymentFormVo.getStatus().equals(1)) {
                        paymentFormExportVo.setStatus("有效");
                    } else {
                        paymentFormExportVo.setStatus("作废");
                    }
                }
                if (GeneralTool.isNotEmpty(paymentFormVo.getGmtCreate())) {
                    paymentFormExportVo.setGmtCreateDate(sdf.format(paymentFormVo.getGmtCreate()));
                }
                if (GeneralTool.isNotEmpty(paymentFormVo.getPaymentDate())) {
                    sdf = new SimpleDateFormat("yyyy-MM-dd");
                    paymentFormExportVo.setPaymentDate(sdf.format(paymentFormVo.getPaymentDate()));
                }
                paymentFormExportVos.add(paymentFormExportVo);
            }
            Map<String, Object> param = new HashMap<>();
            param.put("list", paymentFormExportVos);
            MultipartFile multipartFile = TemplateExcelUtils.packageExcel("付款单列表.xlsx", "PaymentFormInfo.xlsx", param);
            Result<List<FileDto>> upload = fileCenterClient.uploadAppendix(new MultipartFile[]{multipartFile}, LoggerModulesConsts.EXPORT);
            boolean flag = false;
            if (upload.isSuccess()) {
                List<FileDto> data = upload.getData();
                if (data != null && !data.isEmpty()) {
                    FileDto fileDto = data.get(0);
                    if (fileDto.getFileGuid() != null) {
                        download.setStatus(2)
                                .setFkFileGuid(fileDto.getFileGuid());
                        flag = true;
                    }
                }
            }
            if (!flag) {
                download.setStatus(0);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info(e.getMessage());
            download.setStatus(0);
            download.setRemark(LocaleMessageUtils.getMessage("no_data_search"));
        } finally {
            download.setGmtModifiedUser(user.getLoginId());
            download.setGmtModified(new Date());
            permissionCenterClient.updateDownload(download);
        }
    }

    /**
     * 佣金导出
     *
     * @param agentSettlementVo
     * @param headerMap
     * @param local
     * @param user
     */
    @Override
    @Async
    public void asyncExportAgencySettlement(AgentSettlementQueryDto agentSettlementVo, Map<String, String> headerMap, String local, UserInfo user) {
        RequestHeaderHandler.setHeaderMap(headerMap);
        log.info("asyncExportAgencySettlement");
        StaffDownload download = new StaffDownload();
        try {
//            ConfigVo configDto = permissionCenterClient.getConfigByKey("SETTLEMENT_COMMISSION_LIST_LIMIT").getData();
//            String configJson = configDto.getValue1();
//            JSONObject jsonObject = JSON.parseObject(configJson);
//            boolean geaFlag = agentSettlementVo.getFkCompanyIds().contains(1L);
//            boolean iaeFlag = agentSettlementVo.getFkCompanyIds().contains(3L);
//            boolean payInAdvanceFlag = false;
//            if (iaeFlag){
//                String iae = jsonObject.getString("IAE");
//                if (StringUtils.isNotBlank(iae)) {
//                    payInAdvanceFlag = Integer.parseInt(iae)==1;
//                }
//            } else if (geaFlag) {
//                String gea = jsonObject.getString("GEA");
//                if (StringUtils.isNotBlank(gea)) {
//                    payInAdvanceFlag = Integer.parseInt(gea)==1;
//                }
//            } else{
//                String other = jsonObject.getString("OTHER");
//                if (StringUtils.isNotBlank(other)) {
//                    payInAdvanceFlag = Integer.parseInt(other)==1;
//                }
//            }
            Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.SETTLEMENT_COMMISSION_LIST_LIMIT.key, 1).getData();
            String configValue1 = companyConfigMap.get(agentSettlementVo.getFkCompanyIds().get(0));
            boolean payInAdvanceFlag = configValue1.equals("1");

            Long staffId = user.getStaffId();
            download.setFkStaffId(staffId)
                    .setOptKey(FileTypeEnum.D_LIST_EXPORT.key)
                    .setOptDescription("【佣金管理】《学习计划佣金核对明细表》")
                    .setStatus(1)
                    .setGmtCreate(new Date());
            download.setGmtCreateUser(user.getLoginId());
            download.setId(permissionCenterClient.addDownloadRecord(download));
            List<Long> agentIds = saleCenterClient.getAgentSettlementIds(agentSettlementVo, local, staffId, payInAdvanceFlag, true);
            List<AgentSettlementOfferItemVo> agentSettlementItemList;
            AgentSettlementDto settlementVo = new AgentSettlementDto();
            BeanUtils.copyProperties(agentSettlementVo, settlementVo);
            agentSettlementItemList = paymentFormItemMapper.agentSettlementOfferItemList(settlementVo, agentIds, ProjectKeyEnum.STEP_ENROLLED.key, ProjectKeyEnum.STEP_ENROLLED_TBC.key, true, payInAdvanceFlag);
            if (settlementVo.getStatusSettlement() == 0) {
                List<AgentSettlementOfferItemVo> agentNoSettlementItemList = paymentFormItemMapper.getHtiNoCommission(settlementVo);
                if (GeneralTool.isNotEmpty(agentNoSettlementItemList)) {
                    agentSettlementItemList.addAll(agentNoSettlementItemList);
                }
            }
            Set<Long> countryIds = agentSettlementItemList.stream().map(AgentSettlementOfferItemVo::getFkAreaCountryId).filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
            Set<Long> institutionIds = agentSettlementItemList.stream().map(AgentSettlementOfferItemVo::getFkInstitutionId).filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
            Set<Long> institutionCourseIds = agentSettlementItemList.stream().map(AgentSettlementOfferItemVo::getFkInstitutionCourseId).filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
            Set<Long> institutionProviderIds = agentSettlementItemList.stream().map(AgentSettlementOfferItemVo::getFkInstitutionProviderId).filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
            Set<Long> receivablePlanIds = agentSettlementItemList.stream().map(AgentSettlementOfferItemVo::getFkReceivablePlanId).filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
            //根据国家ids获取国家名称
            Map<Long, AreaCountryVo> countryNamesByIds = new HashMap<>();
            if (GeneralTool.isNotEmpty(countryIds)) {
                Result<Map<Long, AreaCountryVo>> result1 = institutionCenterClient.getCountryDtoMapByIds(countryIds);
                if (!result1.isSuccess()) {
                    throw new GetServiceException(result1.getMessage());
                }
                countryNamesByIds = result1.getData();
            }
            Map<Long, InstitutionVo> institutionNamesByIds = new HashMap<>();
            if (GeneralTool.isNotEmpty(institutionIds)) {
                Result<Map<Long, InstitutionVo>> result2 = institutionCenterClient.getInstitutionDtoMapByIds(institutionIds);
                if (!result2.isSuccess()) {
                    throw new GetServiceException(result2.getMessage());
                }
                institutionNamesByIds = result2.getData();
            }
            Map<Long, String> institutionCourseNameMap = new HashMap<>();
            if (GeneralTool.isNotEmpty(institutionCourseIds)) {
                Result<Map<Long, String>> result3 = institutionCenterClient.getCourseNameByIds(institutionCourseIds);
                if (!result3.isSuccess()) {
                    throw new GetServiceException(result3.getMessage());
                }
                institutionCourseNameMap = result3.getData();
            }
            Map<Long, InstitutionProviderVo> providerMap = new HashMap<>();
            if (GeneralTool.isNotEmpty(institutionProviderIds)) {
                Result<Map<Long, InstitutionProviderVo>> result3 = institutionCenterClient.getInstitutionProviderMapByIds(institutionProviderIds);
                if (!result3.isSuccess()) {
                    throw new GetServiceException(result3.getMessage());
                }
                providerMap = result3.getData();
            }
            List<com.get.salecenter.vo.ReceivablePlanVo> receivablePlanVoList = saleCenterClient.getReceivableAmountInfo(receivablePlanIds);
//            Map<Long, List<AgentContract>> agentContract = saleCenterClient.getAgentContractByAgentIds(agentIds);
            Set<String> currencyNums = receivablePlanVoList.stream().map(ReceivablePlanVo::getFkCurrencyTypeNum).collect(Collectors.toSet());
            Set<String> payNums = agentSettlementItemList.stream().filter(e -> GeneralTool.isNotEmpty(e.getFkCurrencyTypeNum())).map(AgentSettlementOfferItemVo::getFkCurrencyTypeNum).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(payNums)) {
                currencyNums.addAll(payNums);
            }
            Map<Long, ReceivablePlanVo> collect = receivablePlanVoList.stream().collect(Collectors.toMap(ReceivablePlanVo::getId, Function.identity()));
            Map<String, CurrencyTypeVo> currencyNamesByNums = currencyTypeService.getCurrencyTypeDtoByNums(currencyNums);
            Map<String, BigDecimal> exchangeRate = exchangeRateService.getLastExchangeRate(currencyNums, "CNY");
            List<AgencyCommissionSettlementServiceExport> agencyCommissionSettlementServiceExports = new ArrayList<>();
            DateTimeFormatter d1 = DateTimeFormatter.ofPattern("yyyy/MM");
            DateTimeFormatter d2 = DateTimeFormatter.ofPattern("yyyy/MM/dd");
            for (AgentSettlementOfferItemVo itemDto : agentSettlementItemList) {
                AgencyCommissionSettlementServiceExport export = new AgencyCommissionSettlementServiceExport();
                export.setCompanyName(itemDto.getCompanyName());
                export.setStudentNum(itemDto.getStudentNum());
                export.setInstitutionProviderName(providerMap.get(itemDto.getFkInstitutionProviderId()).getName());
                export.setInstitutionProviderNameChn(providerMap.get(itemDto.getFkInstitutionProviderId()).getNameChn());
                export.setSummary(itemDto.getSummary());
                export.setDifferenceAmount(itemDto.getDifferenceAmount());
                com.get.salecenter.vo.ReceivablePlanVo planDto = collect.get(itemDto.getFkReceivablePlanId());
                BigDecimal reCommissionRate = null;
                if (GeneralTool.isNotEmpty(planDto)) {
                    export.setReceivableAmount(planDto.getReceivableAmount());
                    export.setFkReceivableCurrencyNum(currencyNamesByNums.get(planDto.getFkCurrencyTypeNum()).getNum());
                    export.setFkReceivableCurrencyNumChn(currencyNamesByNums.get(planDto.getFkCurrencyTypeNum()).getTypeName());
                    export.setReceiptAmount(itemDto.getAmountReceivable());
                    BigDecimal reExchange = exchangeRate.get(planDto.getFkCurrencyTypeNum());
                    export.setRmbExchangeRate(reExchange.toString());
                    if (GeneralTool.isNotEmpty(export.getReceiptAmount())) {
                        export.setReceivableConvertedAmount(export.getReceiptAmount().multiply(reExchange).setScale(2, RoundingMode.UP));
                    }
                    if (DataConverter.bigDecimalNullConvert(planDto.getTuitionAmount()).compareTo(BigDecimal.ZERO) > 0
                            && DataConverter.bigDecimalNullConvert(itemDto.getPayableCommissionRate()).compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal tuitionAmount = DataConverter.bigDecimalNullConvert(itemDto.getAmountActual()).multiply(BigDecimal.valueOf(100)).divide(DataConverter.bigDecimalNullConvert(itemDto.getPayableCommissionRate()), 2, RoundingMode.UP);
                        export.setTuitionAmount(tuitionAmount);
                    } else {
                        export.setTuitionAmount(planDto.getTuitionAmount());
                    }
                    export.setReceivableCommissionRate(DataConverter.bigDecimalNullConvert(planDto.getCommissionRate()) + "%");
//                    export.setReceivableNatRate(DataConverter.bigDecimalNullConvert(planDto.getNetRate()) + "%");
                    reCommissionRate = planDto.getCommissionRate();
//                    export.setCommissionAmount(planDto.getCommissionAmount());
                }
                export.setCountryName(countryNamesByIds.get(itemDto.getFkAreaCountryId()).getName());
                export.setCountryNameChn(countryNamesByIds.get(itemDto.getFkAreaCountryId()).getNameChn());
                export.setInstitutionName(institutionNamesByIds.get(itemDto.getFkInstitutionId()).getName());
                export.setInstitutionNameChn(institutionNamesByIds.get(itemDto.getFkInstitutionId()).getNameChn());
                export.setStudentName(itemDto.getName());
                if (-1 == itemDto.getFkInstitutionCourseId()) {
                    export.setCourseName(itemDto.getOldCourseCustomName());
                } else {
                    export.setCourseName(institutionCourseNameMap.get(itemDto.getFkInstitutionCourseId()));
                }
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
                export.setOpenTime(sdf.format(itemDto.getOpeningTime()));
                if (GeneralTool.isNotEmpty(itemDto.getFkCurrencyTypeNum())) {
                    export.setPayableCurrencyNum(currencyNamesByNums.get(itemDto.getFkCurrencyTypeNum()).getNum());
                    export.setPayableCurrencyNumChn(currencyNamesByNums.get(itemDto.getFkCurrencyTypeNum()).getTypeName());
                    export.setPayableRmbExchangeRate(exchangeRate.get(itemDto.getFkCurrencyTypeNum()).toString());
                }
                export.setAgentNum(itemDto.getAgentNum());
                export.setAgentName(itemDto.getAgentName());
                if (GeneralTool.isNotEmpty(itemDto.getStudentOfferCreateTime())) {
                    export.setItemCreateTime(sdf.format(itemDto.getGmtCreate()));
                }
                BigDecimal payableCommissionRate = itemDto.getPayableCommissionRate();
                export.setPayableCommissionRate(DataConverter.bigDecimalNullConvert(payableCommissionRate) + "%");

                if (reCommissionRate != null && reCommissionRate.compareTo(BigDecimal.ZERO) > 0) {
                    export.setPods(DataConverter.bigDecimalNullConvert(payableCommissionRate).divide(reCommissionRate, 2, RoundingMode.UP).multiply(BigDecimal.valueOf(100)) + "%");
                }
                if (GeneralTool.isNotEmpty(itemDto.getIsPayInAdvance()) && itemDto.getIsPayInAdvance() && GeneralTool.isEmpty(itemDto.getMaxFkReceiptFormItemId())) {
                    export.setPaymentInAdvance("是");
                } else {
                    export.setPaymentInAdvance("否");
                }
                if (GeneralTool.isNotEmpty(itemDto.getInstallmentCreateTime())) {
                    export.setReceiptMonth(itemDto.getInstallmentCreateTime().format(d1));
                    export.setReceiptTime(itemDto.getInstallmentCreateTime().format(d2));
                }
                export.setPaidAmount(itemDto.getAmountActual());
                export.setPayableAmount(itemDto.getPayableAmount());
                BigDecimal payExchange = exchangeRate.get(itemDto.getFkCurrencyTypeNum());
                if (GeneralTool.isNotEmpty(itemDto.getAmountActual())) {
                    export.setPayableConvertAmount(DataConverter.bigDecimalNullConvert(itemDto.getAmountActual()).multiply(payExchange).setScale(2, RoundingMode.UP).toString());
                }
                if (GeneralTool.isNotEmpty(itemDto.getCommissionMark())) {
                    export.setCommissionMark("【" + itemDto.getCommissionMark() + "】");
                }
                agencyCommissionSettlementServiceExports.add(export);
            }
            BigExcelWriter writer = FileUtils.getExcelWhithHeaderStyle(agencyCommissionSettlementServiceExports, "agencySettlement", AgencyCommissionSettlementServiceExport.class, local);
            MultipartFile studyInfo = FileUtils.getFile(writer, "agencySettlement.xlsx");
            Result<List<FileDto>> upload = fileCenterClient.uploadAppendix(new MultipartFile[]{studyInfo}, LoggerModulesConsts.EXPORT);
            boolean flag = false;
            if (upload.isSuccess()) {
                List<FileDto> data = upload.getData();
                if (data != null && !data.isEmpty()) {
                    FileDto fileDto = data.get(0);
                    if (fileDto.getFileGuid() != null) {
                        download.setStatus(2)
                                .setFkFileGuid(fileDto.getFileGuid());
                        flag = true;
                    }
                }
            }
            if (!flag) {
                download.setStatus(0);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info(e.getMessage());
            download.setStatus(0);
            download.setRemark(LocaleMessageUtils.getMessage("no_data_search"));
        } finally {
            download.setGmtModifiedUser(user.getLoginId());
            download.setGmtModified(new Date());
            permissionCenterClient.updateDownload(download);
        }
    }

    /**
     * 根据代理id集合获取所有的应付计划类型列表
     *
     * @param agentSettlementVo 过滤参数
     * @param agentIdSet        代理id集合
     * @return
     */
    private AgentSettlementItemVo agentItemData(AgentSettlementQueryDto agentSettlementVo, Set<Long> agentIdSet, Long fkCompanyId) {
        AgentSettlementDto settlementVo = new AgentSettlementDto();
        BeanUtils.copyProperties(agentSettlementVo, settlementVo);

        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.SETTLEMENT_COMMISSION_LIST_LIMIT.key, 1).getData();
        String configValue1 = companyConfigMap.get(fkCompanyId);
        boolean payInAdvanceFlag = configValue1.equals("1");

        List<Long> agentIds = new ArrayList<>(agentIdSet);

        // 留学申请
        List<AgentSettlementOfferItemVo> agentSettlementItemList = null;
        // 留学保险
        List<AgentSettlementInsuranceVo> agentSettlementInsuranceDtoList = null;
        // 留学住宿
        List<AgentSettlementAccommodationVo> agentSettlementAccommodationDtoList = null;
        // 留学服务费
        List<AgentSettlementServiceFeeVo> agentSettlementServiceFeeDtoList = null;

        //留学申请
        if (GeneralTool.isEmpty(agentSettlementVo.getBusinessType()) || TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(settlementVo.getBusinessType())) {
            agentSettlementItemList = paymentFormItemMapper.agentSettlementOfferItemList(settlementVo, agentIds, ProjectKeyEnum.STEP_ENROLLED.key, ProjectKeyEnum.STEP_ENROLLED_TBC.key, false, payInAdvanceFlag);
        }
        //留学保险
        if (GeneralTool.isEmpty(agentSettlementVo.getBusinessType()) || TableEnum.SALE_STUDENT_INSURANCE.key.equals(settlementVo.getBusinessType())) {
            agentSettlementInsuranceDtoList = paymentFormItemMapper.agentSettlementInsuranceList(settlementVo, agentIds);
        }
        //留学住宿
        if (GeneralTool.isEmpty(agentSettlementVo.getBusinessType()) || TableEnum.SALE_STUDENT_ACCOMMODATION.key.equals(settlementVo.getBusinessType())) {
            agentSettlementAccommodationDtoList = paymentFormItemMapper.agentSettlementAccommodationList(settlementVo, agentIds);
        }
        //留学服务费
        if (GeneralTool.isEmpty(agentSettlementVo.getBusinessType()) || TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(settlementVo.getBusinessType())) {
            agentSettlementServiceFeeDtoList = paymentFormItemMapper.agentSettlementServiceFeeList(settlementVo, agentIds);
        }
        AgentSettlementItemVo agentSettlementItemVo = new AgentSettlementItemVo();
        agentSettlementItemVo.setAgentSettlementOfferItemDtoList(agentSettlementItemList);
        agentSettlementItemVo.setAgentSettlementInsuranceDtoList(agentSettlementInsuranceDtoList);
        agentSettlementItemVo.setAgentSettlementAccommodationDtoList(agentSettlementAccommodationDtoList);
        agentSettlementItemVo.setAgentSettlementServiceFeeDtoList(agentSettlementServiceFeeDtoList);
        return agentSettlementItemVo;
    }

    /**
     * 第三步佣金结算总额表导出
     *
     * @param agentSettlementVo
     * @param headerMap
     * @param local
     * @param user
     */
    @Override
    @Async
    public void asyncExportAgentSettlementGrossAmount(AgentSettlementQueryDto agentSettlementVo,
                                                      Map<String, String> headerMap, String local, UserInfo user, Long fkCompanyId) {
        RequestHeaderHandler.setHeaderMap(headerMap);
        log.info("asyncExportAgentSettlementGrossAmount");
        StaffDownload download = new StaffDownload();
        try {
            Long staffId = user.getStaffId();
            download.setFkStaffId(staffId)
                    .setOptKey(FileTypeEnum.D_LIST_EXPORT.key)
                    .setOptDescription("【佣金管理】《佣金结算总额表》")
                    .setStatus(1)
                    .setGmtCreate(new Date());
            download.setGmtCreateUser(user.getLoginId());
            download.setId(permissionCenterClient.addDownloadRecord(download));
            // 导出列表
            List<AgentSettlementGrossAmountExport> agentSettlementGrossAmountExportList = new ArrayList<>();
            // 第一步
            Result<List<AgentSettlementGrossAmountVo>> agentListToExport = saleCenterClient.getAgentListToExport(agentSettlementVo);
            if (agentListToExport.isSuccess() && GeneralTool.isNotEmpty(agentListToExport.getData())) {
                List<AgentSettlementGrossAmountVo> agentList = agentListToExport.getData();
                Set<Long> agentIdSet = agentList.stream().map(AgentSettlementGrossAmountVo::getAgentId).collect(Collectors.toSet());
                // 第二步
                AgentSettlementItemVo agentSettlementItemVo = agentItemData(agentSettlementVo, agentIdSet, fkCompanyId);

                Set<Long> fkAgentContractAccountIdSet = new HashSet<>();
                Set<String> formCurrencyNumSet = new HashSet<>();
                Set<String> currencyTypeNumSet = new HashSet<>();

                // 留学申请
                List<AgentSettlementOfferItemVo> offerItemDtoList = agentSettlementItemVo.getAgentSettlementOfferItemDtoList();
                if (GeneralTool.isNotEmpty(offerItemDtoList)) {
                    Set<Long> fkAgentContractAccountIds = offerItemDtoList.stream().map(AgentSettlementOfferItemVo::getFkAgentContractAccountId)
                            .filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
                    fkAgentContractAccountIdSet.addAll(fkAgentContractAccountIds);
                    Set<String> currencyNums = offerItemDtoList.stream().map(AgentSettlementOfferItemVo::getFkCurrencyTypeNum)
                            .filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
                    formCurrencyNumSet.addAll(currencyNums);
                    Set<String> accountCurrencyTypeNums = offerItemDtoList.stream().map(AgentSettlementOfferItemVo::getAccountCurrencyTypeNum)
                            .filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
                    currencyTypeNumSet.addAll(accountCurrencyTypeNums);
                }
                // 留学保险
                List<AgentSettlementInsuranceVo> insuranceDtoList = agentSettlementItemVo.getAgentSettlementInsuranceDtoList();
                if (GeneralTool.isNotEmpty(insuranceDtoList)) {
                    Set<Long> fkAgentContractAccountIds = insuranceDtoList.stream().map(AgentSettlementInsuranceVo::getFkAgentContractAccountId)
                            .filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
                    fkAgentContractAccountIdSet.addAll(fkAgentContractAccountIds);
                    Set<String> currencyNums = insuranceDtoList.stream().map(AgentSettlementInsuranceVo::getFkCurrencyTypeNum)
                            .filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
                    formCurrencyNumSet.addAll(currencyNums);
                    Set<String> accountCurrencyTypeNums = insuranceDtoList.stream().map(AgentSettlementInsuranceVo::getAccountCurrencyTypeNum)
                            .filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
                    currencyTypeNumSet.addAll(accountCurrencyTypeNums);
                }
                // 留学住宿
                List<AgentSettlementAccommodationVo> accommodationDtoList = agentSettlementItemVo.getAgentSettlementAccommodationDtoList();
                if (GeneralTool.isNotEmpty(accommodationDtoList)) {
                    Set<Long> fkAgentContractAccountIds = accommodationDtoList.stream().map(AgentSettlementAccommodationVo::getFkAgentContractAccountId)
                            .filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
                    fkAgentContractAccountIdSet.addAll(fkAgentContractAccountIds);
                    Set<String> currencyNums = accommodationDtoList.stream().map(AgentSettlementAccommodationVo::getFkCurrencyTypeNum)
                            .filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
                    formCurrencyNumSet.addAll(currencyNums);
                    Set<String> accountCurrencyTypeNums = accommodationDtoList.stream().map(AgentSettlementAccommodationVo::getAccountCurrencyTypeNum)
                            .filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
                    currencyTypeNumSet.addAll(accountCurrencyTypeNums);
                }
                // 留学服务费
                List<AgentSettlementServiceFeeVo> serviceFeeDtoList = agentSettlementItemVo.getAgentSettlementServiceFeeDtoList();
                if (GeneralTool.isNotEmpty(serviceFeeDtoList)) {
                    Set<Long> fkAgentContractAccountIds = serviceFeeDtoList.stream().map(AgentSettlementServiceFeeVo::getFkAgentContractAccountId)
                            .filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
                    fkAgentContractAccountIdSet.addAll(fkAgentContractAccountIds);
                    Set<String> currencyNums = serviceFeeDtoList.stream().map(AgentSettlementServiceFeeVo::getFkCurrencyTypeNum)
                            .filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
                    formCurrencyNumSet.addAll(currencyNums);
                    Set<String> accountCurrencyTypeNums = serviceFeeDtoList.stream().map(AgentSettlementServiceFeeVo::getAccountCurrencyTypeNum)
                            .filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
                    currencyTypeNumSet.addAll(accountCurrencyTypeNums);
                }
                // 获取结算银行名称
                Map<Long, String> bankNameMap = new HashMap<>();
                Result<Map<Long, AgentContractAccountVo>> result = saleCenterClient.getAgentContractAccountByAccountIds(new ArrayList<>(fkAgentContractAccountIdSet));
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    Map<Long, AgentContractAccountVo> data = result.getData();
                    bankNameMap = data.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getBankName()));
                }
                // 获取实时汇率，把结算币种统一转为港币进行计算
                Map<String, BigDecimal> exchangeRateMap = exchangeRateService.getLastExchangeRate(formCurrencyNumSet, "HKD");
                // 获取结算货币标记名称
                Map<String, String> currencyNamesByNums = new HashMap<>();
                if (GeneralTool.isNotEmpty(currencyTypeNumSet)) {
                    currencyNamesByNums = currencyTypeService.getCurrencyNamesByNums(currencyTypeNumSet);
                }

                // 组装表格导出信息
                for (AgentSettlementGrossAmountVo agent : agentList) {
                    // 代理id
                    Long agentId = agent.getAgentId();
                    String agentName = agent.getAgentName();
                    String bdName = agent.getBdName();
                    // 区域 countryName + cityName + stateName
                    StringBuilder region = new StringBuilder();
                    if (GeneralTool.isNotEmpty(agent.getCountryName())) {
                        region.append(agent.getCountryName()).append(" / ");
                    }
                    if (GeneralTool.isNotEmpty(agent.getCityName())) {
                        region.append(agent.getCityName()).append(" / ");
                    }
                    if (GeneralTool.isNotEmpty(agent.getStateName())) {
                        region.append(agent.getStateName()).append(" / ");
                    }
                    // 留学申请
                    List<AgentSettlementOfferItemVo> agentSettlementOfferItemDtoList = agentSettlementItemVo.getAgentSettlementOfferItemDtoList();
                    if (GeneralTool.isNotEmpty(agentSettlementOfferItemDtoList)) {
                        List<AgentSettlementOfferItemVo> offerList = agentSettlementOfferItemDtoList.stream().filter(i -> agentId.equals(i.getAgentId())).collect(Collectors.toList());
                        for (AgentSettlementOfferItemVo offer : offerList) {
                            AgentSettlementGrossAmountExport export = new AgentSettlementGrossAmountExport();
                            // 代理相关信息
                            export.setFkTypeName(TableEnum.SALE_STUDENT_OFFER_ITEM.value);
                            export.setAgentName(agentName);
                            export.setBdName(bdName);
                            export.setRegion(region.toString());
                            // 学生相关信息
                            export.setStudentName(offer.getName());
                            export.setCurrencyTypeName(currencyNamesByNums.get(offer.getAccountCurrencyTypeNum()));
                            export.setBankName(bankNameMap.get(offer.getFkAgentContractAccountId()));
                            export.setFkCurrencyTypeNum(offer.getFkCurrencyTypeNum());
                            export.setPayableAmount(offer.getPayableAmount());
                            BigDecimal exchangeRate = exchangeRateMap.get(offer.getFkCurrencyTypeNum());
                            export.setExchangeRate(exchangeRate.toString());
                            if (GeneralTool.isNotEmpty(export.getPayableAmount())) {
                                export.setPayableAmountHkd(export.getPayableAmount().multiply(exchangeRate)
                                        .setScale(4, RoundingMode.UP).toString());
                            }
                            export.setAccountExportTime(offer.getAccountExportTime());
                            export.setPrepaidMark(offer.getPrepaidMark() ? "是" : "否");
                            agentSettlementGrossAmountExportList.add(export);
                        }
                    }
                    // 留学保险
                    List<AgentSettlementInsuranceVo> agentSettlementInsuranceDtoList = agentSettlementItemVo.getAgentSettlementInsuranceDtoList();
                    if (GeneralTool.isNotEmpty(agentSettlementInsuranceDtoList)) {
                        List<AgentSettlementInsuranceVo> insuranceList = agentSettlementInsuranceDtoList.stream().filter(i -> agentId.equals(i.getAgentId())).collect(Collectors.toList());
                        for (AgentSettlementInsuranceVo insurance : insuranceList) {
                            AgentSettlementGrossAmountExport export = new AgentSettlementGrossAmountExport();
                            // 代理相关信息
                            export.setFkTypeName(TableEnum.SALE_STUDENT_INSURANCE.value);
                            export.setAgentName(agentName);
                            export.setBdName(bdName);
                            export.setRegion(region.toString());
                            // 学生相关信息
                            export.setStudentName(insurance.getStudentName());
                            export.setCurrencyTypeName(currencyNamesByNums.get(insurance.getAccountCurrencyTypeNum()));
                            export.setBankName(bankNameMap.get(insurance.getFkAgentContractAccountId()));
                            export.setFkCurrencyTypeNum(insurance.getFkCurrencyTypeNum());
                            export.setPayableAmount(insurance.getPayableAmount());
                            BigDecimal exchangeRate = exchangeRateMap.get(insurance.getFkCurrencyTypeNum());
                            export.setExchangeRate(exchangeRate.toString());
                            if (GeneralTool.isNotEmpty(export.getPayableAmount())) {
                                export.setPayableAmountHkd(export.getPayableAmount().multiply(exchangeRate)
                                        .setScale(4, RoundingMode.UP).toString());
                            }
                            export.setAccountExportTime(insurance.getAccountExportTime());
                            export.setPrepaidMark(insurance.getPrepaidMark() ? "是" : "否");
                            agentSettlementGrossAmountExportList.add(export);
                        }
                    }
                    // 留学住宿
                    List<AgentSettlementAccommodationVo> agentSettlementAccommodationDtoList = agentSettlementItemVo.getAgentSettlementAccommodationDtoList();
                    if (GeneralTool.isNotEmpty(agentSettlementAccommodationDtoList)) {
                        List<AgentSettlementAccommodationVo> accommodationList = agentSettlementAccommodationDtoList.stream().filter(i -> agentId.equals(i.getAgentId())).collect(Collectors.toList());
                        for (AgentSettlementAccommodationVo accommodation : accommodationList) {
                            AgentSettlementGrossAmountExport export = new AgentSettlementGrossAmountExport();
                            // 代理相关信息
                            export.setFkTypeName(TableEnum.SALE_STUDENT_INSURANCE.value);
                            export.setAgentName(agentName);
                            export.setBdName(bdName);
                            export.setRegion(region.toString());
                            // 学生相关信息
                            export.setStudentName(accommodation.getStudentName());
                            export.setCurrencyTypeName(currencyNamesByNums.get(accommodation.getAccountCurrencyTypeNum()));
                            export.setBankName(bankNameMap.get(accommodation.getFkAgentContractAccountId()));
                            export.setFkCurrencyTypeNum(accommodation.getFkCurrencyTypeNum());
                            export.setPayableAmount(accommodation.getPayableAmount());
                            BigDecimal exchangeRate = exchangeRateMap.get(accommodation.getFkCurrencyTypeNum());
                            export.setExchangeRate(exchangeRate.toString());
                            if (GeneralTool.isNotEmpty(export.getPayableAmount())) {
                                export.setPayableAmountHkd(export.getPayableAmount().multiply(exchangeRate)
                                        .setScale(4, RoundingMode.UP).toString());
                            }
                            export.setAccountExportTime(accommodation.getAccountExportTime());
                            export.setPrepaidMark(accommodation.getPrepaidMark() ? "是" : "否");
                            agentSettlementGrossAmountExportList.add(export);
                        }
                    }
                    // 留学服务费
                    List<AgentSettlementServiceFeeVo> agentSettlementServiceFeeDtoList = agentSettlementItemVo.getAgentSettlementServiceFeeDtoList();
                    if (GeneralTool.isNotEmpty(agentSettlementServiceFeeDtoList)) {
                        List<AgentSettlementServiceFeeVo> serviceFeeList = agentSettlementServiceFeeDtoList.stream().filter(i -> agentId.equals(i.getAgentId())).collect(Collectors.toList());
                        for (AgentSettlementServiceFeeVo serviceFee : serviceFeeList) {
                            AgentSettlementGrossAmountExport export = new AgentSettlementGrossAmountExport();
                            // 代理相关信息
                            export.setFkTypeName(TableEnum.SALE_STUDENT_INSURANCE.value);
                            export.setAgentName(agentName);
                            export.setBdName(bdName);
                            export.setRegion(region.toString());
                            // 学生相关信息
                            export.setStudentName(serviceFee.getName());
                            export.setCurrencyTypeName(currencyNamesByNums.get(serviceFee.getAccountCurrencyTypeNum()));
                            export.setBankName(bankNameMap.get(serviceFee.getFkAgentContractAccountId()));
                            export.setFkCurrencyTypeNum(serviceFee.getFkCurrencyTypeNum());
                            export.setPayableAmount(serviceFee.getPayableAmount());
                            BigDecimal exchangeRate = exchangeRateMap.get(serviceFee.getFkCurrencyTypeNum());
                            export.setExchangeRate(exchangeRate.toString());
                            if (GeneralTool.isNotEmpty(export.getPayableAmount())) {
                                export.setPayableAmountHkd(export.getPayableAmount().multiply(exchangeRate)
                                        .setScale(4, RoundingMode.UP).toString());
                            }
                            export.setAccountExportTime(serviceFee.getAccountExportTime());
                            export.setPrepaidMark(serviceFee.getPrepaidMark() ? "是" : "否");
                            agentSettlementGrossAmountExportList.add(export);
                        }
                    }
                }
            }
            // 总计
            AgentSettlementGrossAmountExport export = new AgentSettlementGrossAmountExport();
            export.setAgentName("总计");
            BigDecimal sum = agentSettlementGrossAmountExportList.stream()
                    .map(AgentSettlementGrossAmountExport::getPayableAmountHkd)
                    .filter(Objects::nonNull)
                    .filter(GeneralTool::isNotEmpty)
                    .map(payableAmountHkd -> {
                        try {
                            return new BigDecimal(payableAmountHkd);
                        } catch (Exception e) {
                            return BigDecimal.ZERO;
                        }
                    })
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            export.setPayableAmountHkd(sum.toString());
            agentSettlementGrossAmountExportList.add(export);

            // 文件导出
            BigExcelWriter writer = FileUtils.getExcelWhithHeaderStyle(agentSettlementGrossAmountExportList, "agentSettlementGrossAmount", AgentSettlementGrossAmountExport.class, local);
            MultipartFile fileInfo = FileUtils.getFile(writer, "agentSettlementGrossAmount.xlsx");
            Result<List<FileDto>> upload = fileCenterClient.uploadAppendix(new MultipartFile[]{fileInfo}, LoggerModulesConsts.EXPORT);
            boolean flag = false;
            if (upload.isSuccess()) {
                List<FileDto> data = upload.getData();
                if (data != null && !data.isEmpty()) {
                    FileDto fileDto = data.get(0);
                    if (fileDto.getFileGuid() != null) {
                        download.setStatus(2)
                                .setFkFileGuid(fileDto.getFileGuid());
                        flag = true;
                    }
                }
            }
            if (!flag) {
                download.setStatus(0);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info(e.getMessage());
            download.setStatus(0);
            download.setRemark(LocaleMessageUtils.getMessage("no_data_search"));
        } finally {
            download.setGmtModifiedUser(user.getLoginId());
            download.setGmtModified(new Date());
            permissionCenterClient.updateDownload(download);
        }
    }


    /**
     * @param receiptFormVo
     * @param headerMap
     * @param user
     * @param locale
     */
    @Async
    @Override
    public void exportReceiptFormExcel(ReceiptFormQueryDto receiptFormVo, Map<String, String> headerMap, UserInfo user, String locale) {
        RequestHeaderHandler.setHeaderMap(headerMap);
        log.info("asyncExportReceiptFormExcel userInfo{}", user);
        Long staffId = user.getStaffId();
        StaffDownload download = new StaffDownload();
        try {
            Map<Long, String> cName = permissionCenterClient.getCompanyNamesByIds(new HashSet<>(receiptFormVo.getFkCompanyIds())).getData();
            Collection<String> values = cName.values();
            CommonUtil<ReceiptFormQueryDto> commonUtil = new CommonUtil<>();
            String description = commonUtil.getFiledValue(receiptFormVo, ReceiptFormQueryDto.class);
            download.setFkStaffId(staffId)
                    .setOptKey(FileTypeEnum.D_LIST_EXPORT.key)
                    .setOptDescription(String.format("【收款管理】《收款单汇总表》 公司=%s" + description, values))
                    .setStatus(1)
                    .setGmtCreate(new Date());
            download.setGmtCreateUser(user.getLoginId());
            download.setId(permissionCenterClient.addDownloadRecord(download));

            if (GeneralTool.isNotEmpty(receiptFormVo.getFkCompanyIds())) {
                validateCompanys(receiptFormVo.getFkCompanyIds(), user);
            }
            List<ReceiptFormVo> receiptFormVos = receiptFormService.getReceiptForms(receiptFormVo, null);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            List<ReceiptFormExportVo> receiptFormExportVos = BeanCopyUtils.copyListProperties(receiptFormVos, ReceiptFormExportVo::new, (receiptFormDto, receiptFormExportVo) -> {
                if (GeneralTool.isNotEmpty(receiptFormDto.getStatus())) {
                    if (receiptFormDto.getStatus().equals(1)) {
                        receiptFormExportVo.setStatusName("有效");
                    } else {
                        receiptFormExportVo.setStatusName("作废");
                    }
                }
                if (GeneralTool.isNotEmpty(receiptFormDto.getAmount())) {
                    receiptFormExportVo.setAmountStr(String.format("%.02f", receiptFormDto.getAmount()));
                }
                if (GeneralTool.isNotEmpty(receiptFormDto.getAmountReceipt())) {
                    receiptFormExportVo.setAmountReceiptStr(String.format("%.02f", receiptFormDto.getAmountReceipt()));
                }
                if (GeneralTool.isNotEmpty(receiptFormDto.getDiffAmount())) {
                    receiptFormExportVo.setDiffAmountStr(String.format("%.02f", receiptFormDto.getDiffAmount()));
                }
                if (GeneralTool.isNotEmpty(receiptFormDto.getGmtCreate())) {
                    receiptFormExportVo.setGmtCreateDate(sdf.format(receiptFormDto.getGmtCreate()));
                }
            });

            Map<String, Object> param = new HashMap<>();
            param.put("list", receiptFormExportVos);
            MultipartFile multipartFile = TemplateExcelUtils.packageExcel("收款单列表.xlsx", "ReceiptFormInfo.xlsx", param);
            Result<List<FileDto>> upload = fileCenterClient.uploadAppendix(new MultipartFile[]{multipartFile}, LoggerModulesConsts.EXPORT);
            boolean flag = false;
            if (upload.isSuccess()) {
                List<FileDto> data = upload.getData();
                if (data != null && !data.isEmpty()) {
                    FileDto fileDto = data.get(0);
                    if (fileDto.getFileGuid() != null) {
                        download.setStatus(2)
                                .setFkFileGuid(fileDto.getFileGuid());
                        flag = true;
                    }
                }
            }
            if (!flag) {
                download.setStatus(0);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info(e.getMessage());
            download.setStatus(0);
            download.setRemark(LocaleMessageUtils.getMessage("no_data_search"));
        } finally {
            download.setGmtModifiedUser(user.getLoginId());
            download.setGmtModified(new Date());
            permissionCenterClient.updateDownload(download);
        }
    }

    /**
     * 导出明细汇总
     *
     * @param receiptFormVo
     * @param headerMap
     * @param user
     * @param locale
     */
//    @Async
    @Override
    public void exportReceiptFormItemExcel(ReceiptFormQueryDto receiptFormVo, Map<String, String> headerMap, UserInfo user, String locale) {
        RequestHeaderHandler.setHeaderMap(headerMap);
        log.info("asyncExportReceiptFormItemExcel userInfo{}", user);
        Long staffId = user.getStaffId();
        StaffDownload download = new StaffDownload();
        try {
            Map<Long, String> cName = permissionCenterClient.getCompanyNamesByIds(new HashSet<>(receiptFormVo.getFkCompanyIds())).getData();
            Collection<String> values = cName.values();
            CommonUtil<ReceiptFormDto> commonUtil = new CommonUtil<>();
            String description = commonUtil.getFiledValue(receiptFormVo, ReceiptFormDto.class);
            download.setFkStaffId(staffId)
                    .setOptKey(FileTypeEnum.D_LIST_EXPORT.key)
                    .setOptDescription(String.format("【收款管理】《收款单明细表》 公司=%s" + description, values))
                    .setStatus(1)
                    .setGmtCreate(new Date());
            download.setGmtCreateUser(user.getLoginId());
            download.setId(permissionCenterClient.addDownloadRecord(download));

            if (GeneralTool.isNotEmpty(receiptFormVo.getFkCompanyIds())) {
                validateCompanys(receiptFormVo.getFkCompanyIds(), user);
            }
            List<ReceiptFormVo> receiptForms = receiptFormService.getReceiptForms(receiptFormVo, null);
            Set<Long> receiptFormIds = receiptForms.stream().map(ReceiptFormVo::getId).collect(Collectors.toSet());
            //获取收款单列表Map
            Map<Long, ReceiptFormVo> receiptFormVoMap = receiptFormService.findReceiptFormAll().stream().collect(Collectors.toMap(ReceiptFormVo::getId, Function.identity(), (frist, end) -> frist));

            //根据所有付款单ids获取付款单子项列表 TODO 改过
           // List<Long> fromIds = receiptForms.stream().map(ReceiptForm::getId).collect(Collectors.toList());
            List<Long> fromIds = receiptForms.stream().map(ReceiptFormVo::getId).collect(Collectors.toList());
            LambdaQueryWrapper<ReceiptFormItem> receiptFormItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
            if (GeneralTool.isNotEmpty(fromIds)) {
                receiptFormItemLambdaQueryWrapper.in(ReceiptFormItem::getFkReceiptFormId, fromIds);
            }
            List<ReceiptFormItem> receiptFormItems = receiptFormItemMapper.selectList(receiptFormItemLambdaQueryWrapper);
            List<ReceiptFormItemVo> receiptFormItemDtoList =
                    receiptFormItems.stream().map(paymentFormItem -> BeanCopyUtils.objClone(paymentFormItem, ReceiptFormItemVo::new)).collect(Collectors.toList());


            //收款单转map，方便后面赋值属性 TODO 改过
            //Map<Long, ReceiptForm> receiptFormMap = receiptForms.stream().collect(Collectors.toMap(ReceiptForm::getId, Function.identity()));
            Map<Long, ReceiptFormVo> receiptFormMap = receiptForms.stream().collect(Collectors.toMap(ReceiptFormVo::getId, Function.identity()));
            //目标类型为提供商 TODO 改过
//            Set<Long> institutionProviderIds = receiptForms.stream().filter(r -> TableEnum.INSTITUTION_PROVIDER.key.equals(r.getFkTypeKey()))
//                    .map(ReceiptForm::getFkTypeTargetId).collect(Collectors.toSet());
            Set<Long> institutionProviderIds = receiptForms.stream().filter(r -> TableEnum.INSTITUTION_PROVIDER.key.equals(r.getFkTypeKey()))
                    .map(ReceiptFormVo::getFkTypeTargetId).collect(Collectors.toSet());

            //获取目标类型为提供商的名称
            Map<Long, String> institutionProviderNameMap = new HashMap<>();
            if (GeneralTool.isNotEmpty(institutionProviderIds)) {
                Result<Map<Long, String>> resultProviderNamesByIds = institutionCenterClient.getInstitutionProviderNamesByIds(institutionProviderIds);
                if (resultProviderNamesByIds.isSuccess()) {
                    institutionProviderNameMap = resultProviderNamesByIds.getData();
                }
            }

            //目标类型为学生 TODO 改过
//            Set<Long> studentIds = receiptForms.stream().filter(r -> TableEnum.SALE_STUDENT.key.equals(r.getFkTypeKey()))
//                    .map(ReceiptForm::getFkTypeTargetId).collect(Collectors.toSet());
            Set<Long> studentIds = receiptForms.stream().filter(r -> TableEnum.SALE_STUDENT.key.equals(r.getFkTypeKey()))
                    .map(ReceiptFormVo::getFkTypeTargetId).collect(Collectors.toSet());
            //获取目标类型为学生的名称map
            Map<Long, String> studentNameMap = new HashMap<>();
            if (GeneralTool.isNotEmpty(studentIds)) {
                Result<Map<Long, String>> resultNameByIds = saleCenterClient.getStudentNameByIds(studentIds);
                if (resultNameByIds.isSuccess()) {
                    studentNameMap = resultNameByIds.getData();
                }
            }

            //根据付款单子项列表关联的应付计划ids，获取应付计划列表信息
            Set<Long> planIds = receiptFormItemDtoList.stream().map(ReceiptFormItemVo::getFkReceivablePlanId).collect(Collectors.toSet());
            List<com.get.salecenter.vo.ReceivablePlanVo> receivablePlanVos = new ArrayList<>();
            if (GeneralTool.isNotEmpty(planIds)) {
                receivablePlanVos = saleCenterClient.findReceivablePlanByIds(planIds).getData();
            }

            Map<Long, com.get.salecenter.vo.ReceivablePlanVo> receivablePlanDtoMap = new HashMap<>();
            if (GeneralTool.isNotEmpty(receivablePlanVos)) {
                receivablePlanDtoMap = receivablePlanVos.stream().collect(Collectors.toMap(com.get.salecenter.vo.ReceivablePlanVo::getId, Function.identity()));
            }
            Map<String, String> allCurrencyTypeNames = currencyTypeService.getAllCurrencyTypeNames();

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            Map<Long, com.get.salecenter.vo.ReceivablePlanVo> finalReceivablePlanDtoMap = receivablePlanDtoMap;
            Map<Long, String> finalInstitutionProviderNameMap = institutionProviderNameMap;
            Map<Long, String> finalStudentNameMap = studentNameMap;
            List<ReceiptFormItemExportVo> receiptFormItemExportVos = BeanCopyUtils.copyListProperties(receiptFormItemDtoList, ReceiptFormItemExportVo::new, (receiptFormItemDto, receiptFormItemExportVo) -> {
                //------收款单的编号、学校、总金额----
                if (GeneralTool.isNotEmpty(receiptFormItemDto.getFkReceiptFormId())) {

                    //-------应付计划类型、应付目标对象、应付币种、总应付金额--------
                    ReceivablePlanVo receivablePlanVo = finalReceivablePlanDtoMap.get(receiptFormItemDto.getFkReceivablePlanId());
                    if (GeneralTool.isNotEmpty(receivablePlanVo)) {
                        receiptFormItemExportVo.setFkTypeName(receivablePlanVo.getFkTypeName());
                        receiptFormItemExportVo.setTargetNames(receivablePlanVo.getTargetNames());

                        receiptFormItemExportVo.setReceivablePlanAmount(receivablePlanVo.getReceivableAmount());
                        receiptFormItemExportVo.setReceivablePlanCurrencyName(allCurrencyTypeNames.get(receivablePlanVo.getFkCurrencyTypeNum()));
                    }
                    //------实付币种、收款单的编号、学校、总金额----
                    //TODO 改过
                    ReceiptFormVo receiptForm = receiptFormMap.get(receiptFormItemDto.getFkReceiptFormId());
                    if (GeneralTool.isNotEmpty(receiptForm)) {
                        //实付币种
                        String fkCurrencyTypeNum = receiptForm.getFkCurrencyTypeNum();
                        if (GeneralTool.isNotEmpty(fkCurrencyTypeNum)) {
                            receiptFormItemExportVo.setReceiptFormCurrency(allCurrencyTypeNames.get(fkCurrencyTypeNum));
                        }
                        //收款单编号
                        receiptFormItemExportVo.setNumSystem(receiptForm.getNumSystem());
                        //目标名称
                        if (GeneralTool.isNotEmpty(receiptForm.getFkTypeTargetId())) {
                            String targetName = null;
                            if (TableEnum.INSTITUTION_PROVIDER.key.equals(receiptForm.getFkTypeKey())) {
                                targetName = finalInstitutionProviderNameMap.get(receiptForm.getFkTypeTargetId());
                            } else if (TableEnum.SALE_STUDENT.key.equals(receiptForm.getFkTypeKey())) {
                                targetName = finalStudentNameMap.get(receiptForm.getFkTypeTargetId());
                            }
                            receiptFormItemExportVo.setTargetName(targetName);
                        }
                        //总金额
                        receiptFormItemExportVo.setAmountRmb(receiptForm.getAmountRmb());
                    }

                    receiptFormItemExportVo.setFkBankAccountName(GeneralTool.isNotEmpty(receiptForm.getFkBankAccountName())?receiptForm.getFkBankAccountName():"");
                    ReceiptFormVo receiptFormById = receiptFormVoMap.get(receiptFormItemDto.getFkReceiptFormId());
                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                    if (GeneralTool.isNotEmpty(receiptFormById)){
                        Date formattedDate = receiptFormById.getReceiptDate();
                        String receiptDate = formatter.format(formattedDate);
                        receiptFormItemExportVo.setReceiptDate(GeneralTool.isNotEmpty(receiptDate)?receiptDate: "");
                    }
                }
                //日期处理
                if (GeneralTool.isNotEmpty(receiptFormItemDto.getGmtCreate())) {
                    receiptFormItemExportVo.setGmtCreateDate(sdf.format(receiptFormItemDto.getGmtCreate()));
                }
            });

            Map<String, Object> param = new HashMap<>();
            param.put("list", receiptFormItemExportVos);
            MultipartFile multipartFile = TemplateExcelUtils.packageExcel("收款单子项列表.xlsx", "ReceiptFormItemInfo.xlsx", param);
            Result<List<FileDto>> upload = fileCenterClient.uploadAppendix(new MultipartFile[]{multipartFile}, LoggerModulesConsts.EXPORT);
            boolean flag = false;
            if (upload.isSuccess()) {
                List<FileDto> data = upload.getData();
                if (data != null && !data.isEmpty()) {
                    FileDto fileDto = data.get(0);
                    if (fileDto.getFileGuid() != null) {
                        download.setStatus(2)
                                .setFkFileGuid(fileDto.getFileGuid());
                        flag = true;
                    }
                }
            }
            if (!flag) {
                download.setStatus(0);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info(e.getMessage());
            download.setStatus(0);
            download.setRemark(LocaleMessageUtils.getMessage("no_data_search"));
        } finally {
            download.setGmtModifiedUser(user.getLoginId());
            download.setGmtModified(new Date());
            permissionCenterClient.updateDownload(download);
        }
    }


    private Boolean validateCompanys(List<Long> companyIds, UserInfo user) {
        if (user.getIsAdmin()) {
            return true;
        } else {
            if (user != null) {
                List<String> staffCompanyIds = (List) CacheUtil.get("get:user", String.valueOf(user.getStaffId()), "staffCompanyIds:code:", List.class);
                if (CollectionUtil.isNotEmpty(staffCompanyIds)) {
                    return staffCompanyIds.containsAll(companyIds);
                }
            }
            return false;
        }
    }
}
