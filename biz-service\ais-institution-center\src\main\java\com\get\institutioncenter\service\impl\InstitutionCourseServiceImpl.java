package com.get.institutioncenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.redis.cache.GetRedis;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.institutioncenter.dao.*;
import com.get.institutioncenter.vo.*;
import com.get.institutioncenter.entity.*;
import com.get.institutioncenter.service.*;
import com.get.institutioncenter.utils.HttpClientUtils;
import com.get.institutioncenter.utils.MyStringUtils;
import com.get.institutioncenter.dto.*;
import com.get.institutioncenter.dto.query.InstitutionCourseQueryDto;
import com.get.institutioncenter.dto.query.NewsQueryDto;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/8/10
 * @TIME: 10:07
 * @Description:
 **/
@Service
public class InstitutionCourseServiceImpl extends BaseServiceImpl<InstitutionCourseMapper, InstitutionCourse> implements IInstitutionCourseService {
    //线程数
    static int threadCounts = 10;
    static long sum = 0;
    @Resource
    private InstitutionCourseMapper institutionCourseMapper;
    @Resource
    private InstitutionCourseStudyModeMapper institutionCourseStudyModeMapper;
    @Resource
    private InstitutionMapper institutionMapper;
    @Resource
    private InstitutionFacultyMapper institutionFacultyMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private INewsService newsService;
    @Resource
    private InstitutionMajorMapper institutionMajorMapper;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private IInstitutionCourseEngScoreService institutionCourseEngScoreService;
    @Resource
    private IInstitutionCourseAcademicScoreService institutionCourseAcademicScoreService;
    @Resource
    private CourseTypeMapper courseTypeMapper;
    @Resource
    private IAppInfoService appInfoService;
    @Resource
    private InstitutionCoursePathwayMapper institutionCoursePathwayMapper;
    @Resource
    private IInstitutionCourseRankingService institutionCourseRankingService;
    @Resource
    private InstitutionCourseTypeMapper institutionCourseTypeMapper;
    @Resource
    private InstitutionCourseFacultyMapper institutionCourseFacultyMapper;
    @Resource
    private InstitutionCourseMajorLevelMapper institutionCourseMajorLevelMapper;
    @Resource
    private ICharacterService characterService;
    @Resource
    private InstitutionCourseZoneMapper institutionCourseZoneMapper;
    @Resource
    private InstitutionZoneMapper institutionZoneMapper;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    @Lazy
    private IInstitutionService institutionService;
    @Resource
    private IInstitutionFacultyService institutionFacultyService;
    @Resource
    private InstitutionTypeMapper institutionTypeMapper;
    @Resource
    private ITranslationMappingService translationMappingService;
    @Resource
    private InstitutionCourseCustomMapper institutionCourseCustomMapper;
    @Resource
    private GetRedis redisClient;
    @Value("${institution.oldplatform.addresses}")
    private String adminAddresses;
    @Resource
    private InstitutionCourseSubjectMapper institutionCourseSubjectMapper;

    @Override
    public List<InstitutionCourseVo> datas(InstitutionCourseQueryDto institutionCourseVo, Page page) {
        IPage<InstitutionCourseVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        //获取分页数据
        List<InstitutionCourseVo> institutionCourses = institutionCourseMapper.datas(iPage, institutionCourseVo);
        page.setAll((int) iPage.getTotal());
        Map<Long, String> webSiteByTable = appInfoService.getWebSiteByTable(TableEnum.INSTITUTION_COURSE.key, ProjectKeyEnum.APP_COURSE_WEBSITE.key);

        //学校ids
        Set<Long> institutionIds = institutionCourses.stream().map(InstitutionCourseVo::getFkInstitutionId).collect(Collectors.toSet());
        //根据学校ids获取名称
        Map<Long, String> institutionNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(institutionIds)) {
            institutionNamesByIds = institutionService.getInstitutionNamesByIds(institutionIds);
        }

        //根据课程ids获取等级名称map
        Map<Long, String> levelNamesByCourseIds = getLevelNamesByCourseIds(institutionCourses);
        //根据课程ids获取课程类型名称map
        Map<Long, String> courseTypeNamesByCourseIds = getCourseTypeNamesByCourseIds(institutionCourses);
        //根据课程ids获取校区名称map
        Map<Long, String> zoneNamesByCourseIds = getZoneNamesByCourseIds(institutionCourses);

        //学校课程ids
        Set<Long> ids = institutionCourses.stream().map(InstitutionCourseVo::getId).collect(Collectors.toSet());
        //根据学校课程ids获取学院名称
        Map<Long, String> institutionFacultyNameByCourseIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(ids)) {
            institutionFacultyNameByCourseIds = institutionFacultyService.getInstitutionFacultyNameByCourseIds(ids);
        }

        for (InstitutionCourseVo institutionCourseDto : institutionCourses) {
            //插入官网网站信息
            if (GeneralTool.isNotEmpty(webSiteByTable)) {
                institutionCourseDto.setWebSite(webSiteByTable.get(institutionCourseDto.getId()));
            }

            if (GeneralTool.isNotEmpty(institutionCourseDto.getFkInstitutionId())) {
                institutionCourseDto.setInstitutionName(institutionNamesByIds.get(institutionCourseDto.getFkInstitutionId()));
            }
            institutionCourseDto.setInstitutionFacultyName(institutionFacultyNameByCourseIds.get(institutionCourseDto.getId()));
            institutionCourseDto.setMajorLevelName(levelNamesByCourseIds.get(institutionCourseDto.getId()));
            institutionCourseDto.setTypeName(courseTypeNamesByCourseIds.get(institutionCourseDto.getId()));
            String fullName = institutionCourseDto.getName();
            if (GeneralTool.isNotEmpty(institutionCourseDto.getNameChn())) {
                fullName = fullName + "（" + institutionCourseDto.getNameChn() + "）";
            }
            institutionCourseDto.setFullName(fullName);
            StringJoiner stringJoiner = new StringJoiner(" ");
            if (GeneralTool.isNotEmpty(institutionCourseDto.getPublicLevel())) {
                List<String> result = Arrays.asList(institutionCourseDto.getPublicLevel().split(","));
                for (String name : result) {
                    stringJoiner.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(name), ProjectExtraEnum.PUBLIC_OBJECTS));
                }
                institutionCourseDto.setPublicLevelName(stringJoiner.toString());
            }
            institutionCourseDto.setZoneName(zoneNamesByCourseIds.get(institutionCourseDto.getId()));
        }
        return institutionCourses;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addInstitutionCourse(InstitutionCourseDto institutionCourseDto) {
        MyStringUtils.UnescapeHtml(institutionCourseDto);
        LambdaQueryWrapper<InstitutionCourse> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionCourse::getName, institutionCourseDto.getName());
        wrapper.eq(InstitutionCourse::getFkInstitutionId, institutionCourseDto.getFkInstitutionId());
        List<InstitutionCourse> institutionCourses = institutionCourseMapper.selectList(wrapper);
        if (GeneralTool.isNotEmpty(institutionCourses)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("course_name_exists"));
        }
        InstitutionCourse institutionCourse = BeanCopyUtils.objClone(institutionCourseDto, InstitutionCourse::new);
        utilService.updateUserInfoToEntity(institutionCourse);
        int i = institutionCourseMapper.insert(institutionCourse);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        if (GeneralTool.isNotEmpty(institutionCourse.getFee())) {
            LambdaQueryWrapper<Institution> wrapper1 = new LambdaQueryWrapper();
            wrapper1.eq(Institution::getId, institutionCourse.getFkInstitutionId());
            Institution institution = institutionMapper.selectList(wrapper1).get(0);
            //111111
            Result<BigDecimal> result = financeCenterClient.getExchangeRate(institution.getFkCurrencyTypeNum(), "CNY");
//            InstitutionCourse institutionCourse1 = new InstitutionCourse();
            institutionCourse.setFeeCny(institutionCourse.getFee().multiply(result.getData()));
//            institutionCourse1.setId(institutionCourse.getId());
//            institutionCourseMapper.updateById(institutionCourse);
        }

        institutionCourse.setNum(MyStringUtils.getCourseNum(institutionCourse.getId()));
        institutionCourseMapper.updateById(institutionCourse);
        for (Long id : institutionCourseDto.getFkCourseTypeIds()) {
            InstitutionCourseType institutionCourseType = new InstitutionCourseType();
            institutionCourseType.setFkInstitutionCourseId(institutionCourse.getId());
            institutionCourseType.setFkCourseTypeId(id);
            utilService.updateUserInfoToEntity(institutionCourseType);
            institutionCourseTypeMapper.insert(institutionCourseType);
        }
        for (Long id : institutionCourseDto.getFkInstitutionFacultyIds()) {
            InstitutionCourseFaculty institutionCourseFaculty = new InstitutionCourseFaculty();
            institutionCourseFaculty.setFkInstitutionCourseId(institutionCourse.getId());
            institutionCourseFaculty.setFkInstitutionFacultyId(id);
            utilService.updateUserInfoToEntity(institutionCourseFaculty);
            institutionCourseFacultyMapper.insert(institutionCourseFaculty);
        }
        for (Long id : institutionCourseDto.getFkMajorLevelIds()) {
            InstitutionCourseMajorLevel institutionCourseMajorLevel = new InstitutionCourseMajorLevel();
            institutionCourseMajorLevel.setFkInstitutionCourseId(institutionCourse.getId());
            institutionCourseMajorLevel.setFkMajorLevelId(id);
            utilService.updateUserInfoToEntity(institutionCourseMajorLevel);
            institutionCourseMajorLevelMapper.insert(institutionCourseMajorLevel);
        }
        for (Long id : institutionCourseDto.getFkInstitutionZoneIds()) {
            InstitutionCourseZone institutionCourseZone = new InstitutionCourseZone();
            institutionCourseZone.setFkInstitutionCourseId(institutionCourse.getId());
            institutionCourseZone.setFkInstitutionZoneId(id);
            utilService.updateUserInfoToEntity(institutionCourseZone);
            institutionCourseZoneMapper.insert(institutionCourseZone);
        }
        //添加m_institution_course_study_mode数据
        addInstitutionCourseStudyMode(institutionCourse);
        return institutionCourse.getId();
    }

    private void addInstitutionCourseStudyMode(InstitutionCourse institutionCourse) {
        Institution institution = institutionMapper.selectById(institutionCourse.getFkInstitutionId());
        if (GeneralTool.isEmpty(institution)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("institutionId_null"));
        }

        InstitutionCourseStudyMode institutionCourseStudyMode = new InstitutionCourseStudyMode();
        institutionCourseStudyMode.setFkInstitutionCourseId(institutionCourse.getId());
        institutionCourseStudyMode.setFkCurrencyTypeNum(institution.getFkCurrencyTypeNum());
        institutionCourseStudyMode.setFee(institutionCourse.getFeeMax());
        institutionCourseStudyMode.setFeeNote(institutionCourse.getFeeNote());
        institutionCourseStudyMode.setFeeCny(institutionCourse.getFeeCny());
        institutionCourseStudyMode.setDurationType(2);      //默认为年
        institutionCourseStudyMode.setDuration(institutionCourse.getDurationYear());
        institutionCourseStudyMode.setDurationNote(institutionCourse.getDurationNote());
        institutionCourseStudyMode.setStartMonth(institutionCourse.getStartMonth());
        institutionCourseStudyMode.setStartDateNote(institutionCourse.getStartDateNote());
        institutionCourseStudyMode.setApplyDate(institutionCourse.getApplyMonth());
        institutionCourseStudyMode.setApplyDateNote(institutionCourse.getApplyDateNote());
        institutionCourseStudyMode.setIsActive(institutionCourse.getIsActive());
        institutionCourseStudyMode.setGmtCreate(institutionCourse.getGmtCreate());
        institutionCourseStudyMode.setGmtModified(institutionCourse.getGmtModified());
        institutionCourseStudyMode.setGmtCreateUser(institutionCourse.getGmtCreateUser());
        institutionCourseStudyMode.setGmtModifiedUser(institutionCourse.getGmtModifiedUser());
        institutionCourseStudyModeMapper.insert(institutionCourseStudyMode);
    }

    @Override
    public InstitutionCourseVo findInstitutionCourseById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionCourse institutionCourse = institutionCourseMapper.selectById(id);
        if (GeneralTool.isEmpty(institutionCourse)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionCourseVo institutionCourseVo = BeanCopyUtils.objClone(institutionCourse, InstitutionCourseVo::new);
        Institution institution = institutionMapper.selectById(institutionCourse.getFkInstitutionId());
        String translation = SecureUtil.getLocale();
        String institutionType = institutionTypeMapper.getInstitutionTypeNameById(institution.getFkInstitutionTypeId());
        //查询学校类型key
        if (GeneralTool.isNotEmpty(institution.getFkInstitutionTypeId())) {
            institutionCourseVo.setFkInstitutionTypeKey(institutionTypeMapper.getInstitutionTypeKeyById(institution.getFkInstitutionTypeId()));
        }

        if (GeneralTool.isNotEmpty(institution.getFkCurrencyTypeNum())) {
            institutionCourseVo.setFkCurrencyTypeNum(institution.getFkCurrencyTypeNum());
            //111111
            institutionCourseVo.setFkCurrencyTypeNumName(financeCenterClient.getCurrencyNameByNum(institution.getFkCurrencyTypeNum()).getData());
        }
        institutionCourseVo.setFkInstitutionTypeName(institutionType);
        if (GeneralTool.isNotEmpty(institutionCourseVo.getFkInstitutionId())) {
            institutionCourseVo.setInstitutionName(institutionMapper.getInstitutionNameById(institutionCourseVo.getFkInstitutionId()));
        }

        //学校课程ids
        Set<Long> courseIds = new HashSet<>();
        courseIds.add(id);
        //根据学校课程ids获取学院名称
        Map<Long, String> institutionFacultyNameByCourseIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(courseIds)) {
            institutionFacultyNameByCourseIds = institutionFacultyService.getInstitutionFacultyNameByCourseIds(courseIds);
        }

        institutionCourseVo.setFkCourseTypeIds(institutionCourseTypeMapper.getTypeIdsByCourseId(id));
        institutionCourseVo.setFkInstitutionFacultyIds(institutionCourseFacultyMapper.getFacultyIdsByCourseId(id));
        institutionCourseVo.setFkMajorLevelIds(institutionCourseMajorLevelMapper.getMajorLevelIdsByCourseId(id));
        institutionCourseVo.setFkInstitutionZoneIds(institutionCourseZoneMapper.getZoneIdsByCourseId(id));
        institutionCourseVo.setInstitutionFacultyName(institutionFacultyNameByCourseIds.get(id));
        institutionCourseVo.setMajorLevelName(institutionCourseMajorLevelMapper.getFullNamesByCourseId(id));
        institutionCourseVo.setTypeName(institutionCourseTypeMapper.getFullNamesByCourseId(id));
        String fullName = institutionCourseVo.getName();
        if (GeneralTool.isNotEmpty(institutionCourseVo.getNameChn())) {
            fullName = fullName + "（" + institutionCourseVo.getNameChn() + "）";
        }
        //数据等级名
        if (GeneralTool.isNotEmpty(institutionCourseVo.getDataLevel())) {
            institutionCourseVo.setDataLevelName(ProjectExtraEnum.getValueByKey(institutionCourseVo.getDataLevel(), ProjectExtraEnum.DATA_LEVEL));
        }
        institutionCourseVo.setFullName(fullName);
        institutionCourseVo.setFkTableName(TableEnum.INSTITUTION_COURSE.key);
        //设置附件
        setMediaAndAttachedDtos(id, institutionCourseVo);
        StringJoiner stringJoiner = new StringJoiner(" ");
        if (GeneralTool.isNotEmpty(institutionCourseVo.getPublicLevel())) {
            List<String> result = Arrays.asList(institutionCourseVo.getPublicLevel().split(","));
            for (String name : result) {
                stringJoiner.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(name), ProjectExtraEnum.PUBLIC_OBJECTS));
            }
            institutionCourseVo.setPublicLevelName(stringJoiner.toString());
        }

        StringJoiner zoneJoiner = new StringJoiner("，");
        List<Long> ids = institutionCourseZoneMapper.getZoneIdsByCourseId(id);
        if (GeneralTool.isNotEmpty(ids)) {
            for (Long zoneid : ids) {
                zoneJoiner.add(institutionZoneMapper.getNameById(zoneid));
            }
            institutionCourseVo.setZoneName(zoneJoiner.toString());
        }
        return institutionCourseVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        //TODO 改过
        //InstitutionCourse institutionCourse = findInstitutionCourseById(id);
        InstitutionCourseVo institutionCourse = findInstitutionCourseById(id);
        if (institutionCourse == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        deleteService.deleteCourseRelation(id);

        int i = institutionCourseMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }

        //删除翻译内容
        translationMappingService.deleteTranslations(TableEnum.INSTITUTION_COURSE.key, id);
    }

    private String getFacultyName(Long id) {
        List<Long> facultyIds = institutionCourseFacultyMapper.getFacultyIdsByCourseId(id);
        StringBuffer resultBuffer = new StringBuffer();
        for (int i = 0; i < facultyIds.size(); i++) {
            String result = institutionFacultyMapper.getInstitutionFacultyNameById(facultyIds.get(i));
            if (i == 0) {
                resultBuffer.append(result);
            } else {
                resultBuffer.append("，" + result);
            }
        }
        return resultBuffer.toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InstitutionCourseVo updateInstitutionCourse(InstitutionCourseDto institutionCourseDto) {
        if (institutionCourseDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(institutionCourseDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionCourse rs = institutionCourseMapper.selectById(institutionCourseDto.getId());
        if (rs == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        MyStringUtils.UnescapeHtml(institutionCourseDto);
        LambdaQueryWrapper<InstitutionCourse> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionCourse::getName, institutionCourseDto.getName());
        wrapper.eq(InstitutionCourse::getFkInstitutionId, institutionCourseDto.getFkInstitutionId());
        List<InstitutionCourse> institutionCourses = institutionCourseMapper.selectList(wrapper);
        if (GeneralTool.isNotEmpty(institutionCourses)) {
            for (InstitutionCourse ic : institutionCourses) {
                if (ic.getId().longValue() != institutionCourseDto.getId().longValue() && ic.getIsActive()) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("course_name_exists"));
                }
            }

        }
        InstitutionCourse institutionCourse = BeanCopyUtils.objClone(institutionCourseDto, InstitutionCourse::new);
        utilService.updateUserInfoToEntity(institutionCourse);
        institutionCourseMapper.updateById(institutionCourse);
        institutionCourseTypeMapper.deleteByByCourseId(institutionCourse.getId());

        if (GeneralTool.isNotEmpty(institutionCourseDto.getFkCourseTypeIds())) {
            for (Long id : institutionCourseDto.getFkCourseTypeIds()) {
                InstitutionCourseType institutionCourseType = new InstitutionCourseType();
                institutionCourseType.setFkInstitutionCourseId(institutionCourse.getId());
                institutionCourseType.setFkCourseTypeId(id);
                utilService.updateUserInfoToEntity(institutionCourseType);
                institutionCourseTypeMapper.insert(institutionCourseType);
            }
        }
        institutionCourseFacultyMapper.deleteByByCourseId(institutionCourse.getId());
        if (GeneralTool.isNotEmpty(institutionCourseDto.getFkInstitutionFacultyIds())) {
            for (Long id : institutionCourseDto.getFkInstitutionFacultyIds()) {
                InstitutionCourseFaculty institutionCourseFaculty = new InstitutionCourseFaculty();
                institutionCourseFaculty.setFkInstitutionCourseId(institutionCourse.getId());
                institutionCourseFaculty.setFkInstitutionFacultyId(id);
                utilService.updateUserInfoToEntity(institutionCourseFaculty);
                institutionCourseFacultyMapper.insert(institutionCourseFaculty);
            }
        }
        institutionCourseMajorLevelMapper.deleteByByCourseId(institutionCourse.getId());
        if (GeneralTool.isNotEmpty(institutionCourseDto.getFkMajorLevelIds())) {
            for (Long id : institutionCourseDto.getFkMajorLevelIds()) {
                InstitutionCourseMajorLevel institutionCourseMajorLevel = new InstitutionCourseMajorLevel();
                institutionCourseMajorLevel.setFkInstitutionCourseId(institutionCourse.getId());
                institutionCourseMajorLevel.setFkMajorLevelId(id);
                utilService.updateUserInfoToEntity(institutionCourseMajorLevel);
                institutionCourseMajorLevelMapper.insert(institutionCourseMajorLevel);
            }
        }
        institutionCourseZoneMapper.deleteByCourseId(institutionCourse.getId());
        for (Long id : institutionCourseDto.getFkInstitutionZoneIds()) {
            InstitutionCourseZone institutionCourseZone = new InstitutionCourseZone();
            institutionCourseZone.setFkInstitutionCourseId(institutionCourse.getId());
            institutionCourseZone.setFkInstitutionZoneId(id);
            utilService.updateUserInfoToEntity(institutionCourseZone);
            institutionCourseZoneMapper.insert(institutionCourseZone);
        }
        return findInstitutionCourseById(institutionCourse.getId());
    }

    @Override
    public Integer getCourseCountByInstitutionId(Long institutionId) {
        if (GeneralTool.isEmpty(institutionId)) {
            return null;
        }
        LambdaQueryWrapper<InstitutionCourse> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionCourse::getFkInstitutionId, institutionId);
        return institutionCourseMapper.selectCount(wrapper);
    }

    @Override
    public List<MediaAndAttachedVo> addInstitutionCourseMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            String tableName = TableEnum.INSTITUTION_COURSE.key;
            mediaAndAttachedDto.setFkTableName(tableName);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }

    @Override
    public List<Map<String, Object>> findMediaAndAttachedType() {
        return FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.INSTITUTIONCOURSE);
    }

    @Override
    public InstitutionCourseVo getCourseById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionCourse course = institutionCourseMapper.selectById(id);
        InstitutionCourseVo dto = BeanCopyUtils.objClone(course, InstitutionCourseVo::new);
        return dto;
    }

    @Override
    public List<Long> getCourseIds(String name) {
        if (GeneralTool.isEmpty(name)) {
            return null;
        }
        LambdaQueryWrapper<InstitutionCourse> wrapper = new LambdaQueryWrapper();
        wrapper.like(InstitutionCourse::getName, name);
        List<InstitutionCourse> institutionCourses = institutionCourseMapper.selectList(wrapper);
        List<Long> collect = institutionCourses.stream().map(InstitutionCourse::getId).collect(Collectors.toList());
        if (GeneralTool.isEmpty(collect)) {
            collect = new ArrayList<>();
            collect.add(0L);
        }
        return collect;
    }

    @Override
    public String getCourseNameById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            return null;
        }
        InstitutionCourse course = institutionCourseMapper.selectById(id);
        if (GeneralTool.isEmpty(course)) {
            return null;
        }
        String name = course.getName();
        if (GeneralTool.isNotEmpty(course.getNameChn())) {
            name = name + "(" + course.getNameChn() + ")";
        }
        return name;
    }

    /**
     * feign调用 更新所有课程的feeCny
     *
     * @param updateType
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateCoursefeeCny(String updateType) {

        List<InstitutionCourseVo> institutionDtos = institutionCourseMapper.getAllcourseDel(updateType);
        //所有学校的币种编号
        List<String> schoolFkCurrencyTypeNum = institutionCourseMapper.getAllSchoolFkCurrencyTypeNum();
        Map<String, BigDecimal> map = new HashMap<>();
        schoolFkCurrencyTypeNum.forEach(typeNum -> {
            if (!"FCY".equals(typeNum)) {
                BigDecimal exchangeRate = financeCenterClient.getLastExchangeRate(true, typeNum, "CNY").getData();
                map.put(typeNum, exchangeRate);
            }

        });

        if (GeneralTool.isNotEmpty(institutionDtos) && institutionDtos.size() > 0) {
            for (InstitutionCourseVo institutionCourseVo : institutionDtos) {
                if (GeneralTool.isEmpty(institutionCourseVo.getFkCurrencyTypeNum()) || GeneralTool.isEmpty(institutionCourseVo.getFee())
                        || "FCY".equals(institutionCourseVo.getFkCurrencyTypeNum())) {
                    continue;
                }
                BigDecimal courseRate = map.get(institutionCourseVo.getFkCurrencyTypeNum());
                if (GeneralTool.isNotEmpty(courseRate)) {
                    institutionCourseVo.setFeeCny(institutionCourseVo.getFee().multiply(courseRate));
                    institutionCourseMapper.updateAllCourseFerCny(institutionCourseVo.getId(), institutionCourseVo.getFeeCny());

                }
            }


        }

        return true;
    }

    /**
     * 检查获取课程链接
     *
     * @Date 18:50 2022/2/28
     * <AUTHOR>
     */
    @Override
    public Long checkCourseWebsite(Long fkInstitutionId, String courseWebsite) {
        return institutionCourseStudyModeMapper.checkCourseWebsite(fkInstitutionId, courseWebsite);
    }

    @Override
    public List<Long> getInstitutionCourseByName(String courseName,Long institutionId) {
        List<Long> listInstitutionCourse = institutionCourseMapper.getInstitutionCourseByName(courseName,institutionId);
        return listInstitutionCourse;
    }

    @Override
    public List<Long> getInstitutionCourseIdsByLevelAndName(String name, Long levelId) {
        return institutionCourseMapper.getInstitutionCourseIdsByLevelAndName(name, levelId);
    }

    @Override
    public List<BaseSelectEntity> getCourseSelected(String keyword,Long institutionId) {
        return institutionCourseMapper.getCourseSelectedByKeyword(keyword,institutionId);
    }

    @Override
    public Map<Long, String> getCourseNameByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        LambdaQueryWrapper<InstitutionCourse> wrapper = new LambdaQueryWrapper();
        wrapper.in(InstitutionCourse::getId, ids);
        List<InstitutionCourse> institutionCourses = institutionCourseMapper.selectList(wrapper);
        if (GeneralTool.isEmpty(institutionCourses)) {
            return map;
        }
        for (InstitutionCourse institutionCours : institutionCourses) {
            String name = institutionCours.getName();
            if (GeneralTool.isNotEmpty(institutionCours.getNameChn())) {
                name = name + "(" + institutionCours.getNameChn() + ")";
            }
            map.put(institutionCours.getId(), name);
        }
        return map;
    }

    @Override
    public Long addNews(NewsDto newsDto) {
        newsDto.setFkTableName(TableEnum.INSTITUTION_COURSE.key);
        return newsService.addNews(newsDto);
    }

    @Override
    public String getCourseNameChnById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            return null;
        }
        InstitutionCourse course = institutionCourseMapper.selectById(id);
        if (GeneralTool.isEmpty(course)) {
            return null;
        }
        return course.getNameChn();
    }

    @Override
    public Map<Long, String> getCourseNameChnByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        LambdaQueryWrapper<InstitutionCourse> wrapper = new LambdaQueryWrapper();
        wrapper.in(InstitutionCourse::getId, ids);
        List<InstitutionCourse> institutionCourses = institutionCourseMapper.selectList(wrapper);
        if (GeneralTool.isEmpty(institutionCourses)) {
            return map;
        }
        for (InstitutionCourse institutionCours : institutionCourses) {
            map.put(institutionCours.getId(), institutionCours.getNameChn());
        }
        return map;
    }

    @Override
    public List<BaseSelectEntity> getInstitutionCourseByInstitution(Long institutionId) {
        return institutionCourseMapper.getInstitutionCourseByInstitution(institutionId);
    }

    @Override
    public List<BaseSelectEntity> getInstitutionCourseByInstitutionIdList(List<Long> institutionIdList) {
        return institutionCourseMapper.getInstitutionCourseByInstitutionIdList(institutionIdList);
    }

    /**
     * 设置附件
     *
     * @param id
     * @param institutionCourseVo
     * @
     */
    private void setMediaAndAttachedDtos(Long id, InstitutionCourseVo institutionCourseVo) {
        //查询学校下面的附件
        String tableName = TableEnum.INSTITUTION_COURSE.key;
        MediaAndAttachedDto attachedVo = new MediaAndAttachedDto();
        attachedVo.setFkTableName(tableName);
        attachedVo.setFkTableId(id);
        List<MediaAndAttachedVo> mediaAndAttachedVo = attachedService.getMediaAndAttachedDto(attachedVo);
        institutionCourseVo.setMediaAndAttachedDtos(mediaAndAttachedVo);
    }

    /**
     * 设置新闻
     *
     * @param id
     * @param institutionCourseVo
     * @
     */
    private void setNewsDtos(Long id, InstitutionCourseVo institutionCourseVo) {
        //查询学校下面的新闻
        NewsDto news = new NewsDto();
        news.setFkTableId(id);
        news.setFkTableName(TableEnum.INSTITUTION_COURSE.key);
        institutionCourseVo.setNewsDtos(newsService.getNewsDtoByTarget(news));
    }

    @Override
    public List<NewsVo> getNewsData(NewsQueryDto newsVo, Page page) {
        if (GeneralTool.isEmpty(newsVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        //查询课程下面的新闻
        newsVo.setFkTableId(newsVo.getFkTableId());
        newsVo.setFkTableName(TableEnum.INSTITUTION_COURSE.key);
        return newsService.datas(newsVo, page);
    }

    @Override
    public List<MediaAndAttachedVo> getCourseMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.INSTITUTION_COURSE.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);
    }

    @Override
    public List<InstitutionCourseEngScoreVo> getEngScoreDatas(InstitutionCourseEngScoreDto data, Page page) {
        return institutionCourseEngScoreService.getInstitutionCourseEngScores(data, page);
    }

    @Override
    public List<InstitutionCourseAcademicScoreVo> getAcademicScoreDatas(InstitutionCourseAcademicScoreDto data, Page page) {
        return institutionCourseAcademicScoreService.getInstitutionCourseAcademicScores(data, page);
    }

    @Override
    public List<AppInfoVo> getAppInfoDatas(AppInfoDto data, Page page) {
        return appInfoService.getAppInfos(data, page);
    }

    @Override
    public List<InstitutionCourseRankingVo> getInstitutionCourseRankingDatas(InstitutionCourseRankingDto data, Page page) {
        return institutionCourseRankingService.getInstitutionCourseRankings(data, page);
    }

    @Override
    public List<Map<String, Object>> findTargetType() {
        return TableEnum.enumsTranslation2Arrays(TableEnum.APP_INFO_TYPE);
    }

    @Override
    public List<BaseSelectEntity> findTarget(String tableName) {
        return institutionCourseMapper.getTarget(tableName);
    }

    @Override
    public void batchAdd(List<AppInfoDto> appInfoDtos) {
        if (GeneralTool.isEmpty(appInfoDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (AppInfoDto appInfoDto : appInfoDtos) {
            appInfoDto.setFkTableName(TableEnum.INSTITUTION_COURSE.key);
        }
        appInfoService.batchAdd(appInfoDtos);
    }

    @Override
    public void batchAddInstitutionCourseRanking(List<InstitutionCourseRankingDto> istitutionCourseRankingVos) {
        if (GeneralTool.isEmpty(istitutionCourseRankingVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        institutionCourseRankingService.batchAdd(istitutionCourseRankingVos);
    }

    @Override
    public List<BaseSelectEntity> getInstitutionCoursePathwayByInstitution(InstitutionCourseDto institutionCourseDto) {
        List<Long> ids = institutionCoursePathwayMapper.getBridgeInstitutionCourseIds(institutionCourseDto.getId(), institutionCourseDto.getUpdateCourseId());
        return institutionCourseMapper.getBridgeInstitutionCourseSelect(ids, institutionCourseDto.getFkInstitutionId());
    }

    /**
     * 非桥梁课程下拉框数据
     *
     * @Date 17:36 2021/7/22
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> getNonBridgeInstitutionCoursePathwayByInstitution(InstitutionCourseDto institutionCourseDto) {
        List<Long> ids = institutionCoursePathwayMapper.getNonBridgeInstitutionCoursePathwayByInstitution(institutionCourseDto.getId(), institutionCourseDto.getUpdateCourseId());
        return institutionCourseMapper.getNonBridgeInstitutionCoursePathwayByInstitution(ids, institutionCourseDto.getFkInstitutionId());
    }

    @Override
    public List<CharacterVo> getCharacterDatas(CharacterDto data, Page page) {
        return characterService.getCharacters(data, page);
    }

    @Override
    public void batchAddCharacter(List<CharacterDto> characterDtos) {
        if (GeneralTool.isEmpty(characterDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (CharacterDto characterDto : characterDtos) {
            characterDto.setFkTableName(TableEnum.INSTITUTION_COURSE.key);
        }
        characterService.batchAdd(characterDtos);
    }

    @Override
    public BigDecimal getFeeById(Long id) {
        return new BigDecimal(institutionCourseMapper.getFeeById(id));
    }

    @Override
    public BigDecimal getSumFeeByIds(List<Long> ids) {
        return new BigDecimal(institutionCourseMapper.getSumFeeByIds(ids));
    }

    @Override
    public Map<Long, String> getInstitutionCourseNamesByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        LambdaQueryWrapper<InstitutionCourse> wrapper = new LambdaQueryWrapper();
        wrapper.in(InstitutionCourse::getId, ids);
        List<InstitutionCourse> institutionCourses = institutionCourseMapper.selectList(wrapper);
        if (GeneralTool.isEmpty(institutionCourses)) {
            return map;
        }
        for (InstitutionCourse institutionCours : institutionCourses) {
            String name = GeneralTool.isEmpty(institutionCours.getName()) ? "" : institutionCours.getName();
            StringBuilder sb = new StringBuilder(name);
            //如果中文名称不为空，则名称继续拼接中文名称
            if (GeneralTool.isNotEmpty(institutionCours.getNameChn())) {
                sb.append("（");
                sb.append(institutionCours.getNameChn());
                sb.append("）");
            }
            map.put(institutionCours.getId(), sb.toString());
        }
        return map;
    }

    /**
     * feign调用 根据课程ids查找对应课程名称
     *
     * @Date 2:22 2021/6/19
     * <AUTHOR>
     */
    @Override
    public String getInstitutionCourseNameById(Long institutionCourseId) {
        return institutionCourseMapper.getNameById(institutionCourseId);
    }

    /**
     * feign调用 通过自定义课程ids 查找对应的自定义课程名称
     *
     * @param ids
     * @return
     */
    @Override
    public Map<Long, String> getCourseNameByCustomIds(Set<Long> ids) {

        LambdaQueryWrapper<InstitutionCourseCustom> wrapper = new LambdaQueryWrapper();
        wrapper.in(InstitutionCourseCustom::getId, ids);
        List<InstitutionCourseCustom> institutionCourseCustoms = institutionCourseCustomMapper.selectList(wrapper);
        Map<Long, String> longStringMap = new HashMap<>();
        institutionCourseCustoms.stream().forEach(institutionCourseCustom -> longStringMap.put(institutionCourseCustom.getId(), institutionCourseCustom.getName()));
        return longStringMap;
    }


    /**
     * 根据课程ids获取等级名称map
     *
     * @param institutionCourses
     * @return
     */
    private Map<Long, String> getLevelNamesByCourseIds(List<InstitutionCourseVo> institutionCourses) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(institutionCourses)) {
            return map;
        }
        Set<Long> fkInstitutionCourseIds = institutionCourses.stream().map(InstitutionCourseVo::getId).collect(Collectors.toSet());
        List<MajorLevelVo> namesByCourseIds = institutionCourseMajorLevelMapper.getNamesByCourseIds(fkInstitutionCourseIds);
        if (GeneralTool.isEmpty(namesByCourseIds)) {
            return map;
        }
        for (MajorLevelVo namesByCourseId : namesByCourseIds) {
            map.put(namesByCourseId.getFkInstitutionCourseId(), institutionCourseMajorLevelMapper.getFullNamesByCourseId(namesByCourseId.getFkInstitutionCourseId()));
        }
        return map;
    }

    /**
     * 根据课程ids获取课程类型名称map
     *
     * @param institutionCourses
     * @return
     */
    private Map<Long, String> getCourseTypeNamesByCourseIds(List<InstitutionCourseVo> institutionCourses) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(institutionCourses)) {
            return map;
        }
        Set<Long> fkInstitutionCourseIds = institutionCourses.stream().map(InstitutionCourseVo::getId).collect(Collectors.toSet());
        List<CourseTypeVo> namesByCourseIds = institutionCourseTypeMapper.getNamesByCourseIds(fkInstitutionCourseIds);
        if (GeneralTool.isEmpty(namesByCourseIds)) {
            return map;
        }
        for (CourseTypeVo courseTypeVo : namesByCourseIds) {
            map.put(courseTypeVo.getFkInstitutionCourseId(), institutionCourseTypeMapper.getFullNamesByCourseId(courseTypeVo.getFkInstitutionCourseId()));
        }
        return map;
    }


    /**
     * 根据课程ids获取校区名称map
     *
     * @param institutionCourses
     * @return
     */
    private Map<Long, String> getZoneNamesByCourseIds(List<InstitutionCourseVo> institutionCourses) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(institutionCourses)) {
            return map;
        }
        Set<Long> fkInstitutionCourseIds = institutionCourses.stream().map(InstitutionCourseVo::getId).collect(Collectors.toSet());
        List<InstitutionZoneVo> nameByIds = institutionZoneMapper.getNameByIds(fkInstitutionCourseIds);
        if (GeneralTool.isEmpty(nameByIds)) {
            return map;
        }
        for (InstitutionZoneVo institutionZoneVo : nameByIds) {
            map.put(institutionZoneVo.getFkInstitutionCourseId(), institutionZoneVo.getFullName());
        }
        return map;
    }

    @Override
    public List<InstitutionCourseMatchVo> getInstitutionCourseByNameMatch(String courseName, Long institutionId) {
        //先进行sql模糊匹配
        LambdaQueryWrapper<InstitutionCourse> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionCourse::getFkInstitutionId,institutionId);
//        wrapper.eq(InstitutionCourse::getIsActive,1);
        wrapper.like(InstitutionCourse::getName,courseName).last("limit 100");
        List<InstitutionCourse> courseList = institutionCourseMapper.selectList(wrapper);
        List<InstitutionCourseMatchVo> institutionCourseMatchList = new ArrayList();
        if (GeneralTool.isNotEmpty(courseList)){
            for (InstitutionCourse collect : courseList) {
                InstitutionCourseMatchVo institutionCourseMatchVo = new InstitutionCourseMatchVo();
                institutionCourseMatchVo.setCourseName(collect.getName());
                institutionCourseMatchVo.setCourseId(collect.getId());
                institutionCourseMatchVo.setIsActive(collect.getIsActive());
                //课程链接
                InstitutionCourseVo institutionCourseVo = institutionCourseMapper.getInstitutionCourseWesiteByName(collect.getName(), institutionId);
                if(GeneralTool.isNotEmpty(institutionCourseVo)){
                    institutionCourseMatchVo.setCourseWebsite(institutionCourseVo.getTypeValue());
                }
                institutionCourseMatchVo.setInstitutionId(institutionId);
                institutionCourseMatchList.add(institutionCourseMatchVo);
            }
        }else{
            //sql匹配不到则进行精准匹配
            //查询学校所有课程id进行匹配
            LambdaQueryWrapper<InstitutionCourse> lambdaQueryWrapper = new LambdaQueryWrapper();
            lambdaQueryWrapper.eq(InstitutionCourse::getFkInstitutionId,institutionId);
//            lambdaQueryWrapper.eq(InstitutionCourse::getIsActive,1);
            List<InstitutionCourse> institutionCourses = institutionCourseMapper.selectList(lambdaQueryWrapper);
            List<String> courseNameList = institutionCourses.stream().map(InstitutionCourse::getName).collect(Collectors.toList());
            if (GeneralTool.isNotEmpty(courseNameList)){
                //根据所有课程与用户输入课程进行匹配
                Map headerParams = new HashMap();
                headerParams.put("Content-Type", "application/json");
                Map bodyParams = new HashMap();
                ArrayList<String> list = new ArrayList<>();
                list.add(courseName);
                bodyParams.put("long_list",courseNameList);
                bodyParams.put("short_list",list);
                String addresses = adminAddresses+"/course_name_match/?is_strict=false";

                //发送请求，拿到匹配结果
                String postRequest = HttpClientUtils.sendPostRequest(addresses, headerParams, bodyParams);
                //取出匹配数据
                Map map = (Map) JSONObject.parse(postRequest);
                List lists = (List) map.get("data");
                Map data = (Map) lists.get(0);
                List<Map> matchList = (List) data.get("match_list");
                if (GeneralTool.isNotEmpty(matchList)){
                    for (Map matchLists : matchList) {
                        InstitutionCourseMatchVo institutionCourseMatchVo = new InstitutionCourseMatchVo();
                        String courseNames = (String) matchLists.get("course_name");
                        BigDecimal  score = (BigDecimal) matchLists.get("score");
                        institutionCourseMatchVo.setCourseName(courseNames);
                        //课程链接
                        InstitutionCourseVo institutionCourseVo = institutionCourseMapper.getInstitutionCourseWesiteByName(courseNames, institutionId);
                        if (GeneralTool.isNotEmpty(institutionCourseVo)){
                            institutionCourseMatchVo.setCourseWebsite(institutionCourseVo.getTypeValue());
                            institutionCourseMatchVo.setCourseId(institutionCourseVo.getId());
                        }
                        institutionCourseMatchVo.setScore(score);
                        institutionCourseMatchVo.setInstitutionId(institutionId);
                        institutionCourseMatchList.add(institutionCourseMatchVo);
                    }
                }
            }
        }
        //取前面20条
        institutionCourseMatchList = institutionCourseMatchList.stream().limit(100).collect(Collectors.toList());
        if (GeneralTool.isNotEmpty(institutionCourseMatchList)) {
            List<Long> courseIds = institutionCourseMatchList.stream().map(InstitutionCourseMatchVo::getCourseId).collect(Collectors.toList());
            List<InstitutionCourseMajorLevel> institutionCourseMajorLevels = institutionCourseMajorLevelMapper.selectList(Wrappers.<InstitutionCourseMajorLevel>lambdaQuery().in(InstitutionCourseMajorLevel::getFkInstitutionCourseId, courseIds));
            Map<Long, List<Long>> courseLevelMap = institutionCourseMajorLevels.stream().collect(Collectors.groupingBy(InstitutionCourseMajorLevel::getFkInstitutionCourseId, Collectors.mapping(InstitutionCourseMajorLevel::getFkMajorLevelId, Collectors.toList())));
            institutionCourseMatchList.forEach(institutionCourseMatchDto -> {
                institutionCourseMatchDto.setCourseLevelList(courseLevelMap.get(institutionCourseMatchDto.getCourseId()));
            });

        }
        return institutionCourseMatchList;
    }

    @Override
    public List<BaseSelectEntity> InstitutionCourseListByName(String courseName, List<Long> institutionIds, List<Long> courseIds) {
        if (GeneralTool.isNotEmpty(courseName)){
            courseName = courseName.toLowerCase();
        }
        List<BaseSelectEntity> listInstitutionCourse = institutionCourseMapper.InstitutionCourseListByName(courseName,institutionIds,courseIds);
        return listInstitutionCourse;
    }

    @Override
    public List<CaseStudyResultsDto.Statistics> getCourseStatic(Set<Long> courseIds){
        if (GeneralTool.isEmpty(courseIds)) {
            return Collections.emptyList();
        }
        return institutionCourseMapper.getCourseStatistics(courseIds);
    }

    @Override
    public List<BaseSelectEntity> getInstitutionCourseList(InstitutionCourseNameSearchDto institutionCourseNameSearchDto) {
        return institutionCourseMapper.getInstitutionCourseList(institutionCourseNameSearchDto.getCourseName(), institutionCourseNameSearchDto.getCourseIds());
    }

    @Override
    public List<BaseSelectEntity> getInstitutionCourseListByIds(InstitutionCourseNameSearchDto institutionCourseNameSearchDto) {
        return institutionCourseMapper.getInstitutionCourseListByIds(institutionCourseNameSearchDto.getCourseIds());
    }

    @Override
    public List<InstitutionCourseSubjectVo> getInstitutionCourseSubjectDatas(InstitutionCourseSubjectDto institutionCourseSubjectDto, Page page) {
        LambdaQueryWrapper<InstitutionCourseSubject> wrapper = Wrappers.lambdaQuery(InstitutionCourseSubject.class);
        if (GeneralTool.isNotEmpty(institutionCourseSubjectDto.getFkInstitutionCourseId())){
            wrapper.eq(InstitutionCourseSubject::getFkInstitutionCourseId, institutionCourseSubjectDto.getFkInstitutionCourseId());
        }
        wrapper.orderByDesc(InstitutionCourseSubject::getViewOrder);
        IPage<InstitutionCourseSubject> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<InstitutionCourseSubject> institutionCourseSubjects = institutionCourseSubjectMapper.selectPage(iPage, wrapper).getRecords();
        page.setAll((int) iPage.getTotal());
        if (GeneralTool.isEmpty(institutionCourseSubjects)){
            return Collections.emptyList();
        }
        return BeanCopyUtils.copyListProperties(institutionCourseSubjects, InstitutionCourseSubjectVo::new);
    }

    /**
     * 根据课程ids获取英文课程名map
     */
    @Override
    public Map<Long, String> getCourseEnNameByIds(Set<Long> courseIds) {
        List<InstitutionCourse> institutionCourses = institutionCourseMapper.selectBatchIds(courseIds);
        if (GeneralTool.isNotEmpty(institutionCourses)){
            return institutionCourses.stream().collect(Collectors.toMap(InstitutionCourse::getId, InstitutionCourse::getName));
        }
        return Collections.emptyMap();
    }

}
