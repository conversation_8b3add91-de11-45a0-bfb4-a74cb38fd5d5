package com.get.financecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @author: Sea
 * @create: 2021/4/7 15:19
 * @verison: 1.0
 * @description:
 */
@Data
public class ExpenseClaimFormItemVo extends BaseEntity {

    @ApiModelProperty(value = "报销单费用类型名称")
    private String expenseClaimFeeTypeName;

    @ApiModelProperty(value = "关联项名称")
    private String relationTargetName;

    @ApiModelProperty(value = "关联活动名称")
    private String eventTableName;

    @ApiModelProperty(value = "涉及代理")
    private String detail;

    @ApiModelProperty(value = "学生代理名称")
    private String agentName;

    @ApiModelProperty(value = "币种名称")
    private String fkCurrencyTypeName;


    //=================实体类ExpenseClaimFormItem=====================
    @ApiModelProperty(value = "费用报销申请单Id")
    private Long fkExpenseClaimFormId;

    @ApiModelProperty(value = "费用报销费用类型Id")
    private Long fkExpenseClaimFeeTypeId;

    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "报销金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "发票金额")
    private BigDecimal invoiceAmount;

    @ApiModelProperty(value = "摘要")
    private String summary;

    @ApiModelProperty(value = "关联类型Key（目标类型表名）")
    private String relationTargetKey;

    @ApiModelProperty(value = "关联类型Id（目标类型表对应记录项Id）")
    private Long relationTargetId;

    @ApiModelProperty(value = "活动类型表名（目标类型表名）")
    private String fkEventTableName;

    @ApiModelProperty(value = "关联活动公司id")
    private Long eventTableCompanyId;

    @ApiModelProperty(value = "活动Id（目标类型表对应记录项Id）")
    private Long fkEventTableId;

    @ApiModelProperty(value = "关联项公司id")
    private Long relationTargetCompanyId;

    @ApiModelProperty(value = "学生代理Id")
    private Long fkAgentId;

    @ApiModelProperty(value = "费用报销关联代理内容Id")
    private Long fkExpenseClaimAgentContentId;

    @ApiModelProperty(value = "人数")
    private Integer peopleCount;

}
