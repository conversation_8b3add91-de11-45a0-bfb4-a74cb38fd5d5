package com.get.common.query.security;

import com.get.common.query.enums.QueryType;
import com.get.common.query.exception.ParameterValidationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;

/**
 * SQL安全验证器
 * 防止SQL注入攻击，验证字段名和参数值的安全性
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */
@Slf4j
@Component
@ConditionalOnProperty(
    name = "dynamic.query.security.enabled",
    havingValue = "true",
    matchIfMissing = true
)
public class SecurityValidator {
    
    /**
     * 字段名最大长度
     */
    @Value("${dynamic.query.security.max-column-length:64}")
    private int maxColumnLength;
    
    /**
     * 参数值最大长度
     */
    @Value("${dynamic.query.security.max-value-length:4000}")
    private int maxValueLength;
    
    /**
     * 是否启用严格模式
     */
    @Value("${dynamic.query.security.strict-mode:true}")
    private boolean strictMode;
    
    /**
     * 字段名白名单正则（字母、数字、下划线）
     */
    private static final Pattern COLUMN_NAME_PATTERN = Pattern.compile("^[a-zA-Z][a-zA-Z0-9_]*$");
    
    /**
     * SQL关键字黑名单
     */
    private static final Set<String> SQL_KEYWORDS = new HashSet<>(Arrays.asList(
        "SELECT", "INSERT", "UPDATE", "DELETE", "DROP", "CREATE", "ALTER", "GRANT", "REVOKE",
        "UNION", "EXEC", "EXECUTE", "SCRIPT", "DECLARE", "CURSOR", "TRUNCATE", "MERGE",
        "INFORMATION_SCHEMA", "SYSOBJECTS", "SYSCOLUMNS", "XP_", "SP_", "WAITFOR",
        "CHAR", "NCHAR", "VARCHAR", "NVARCHAR", "ASCII", "SUBSTRING", "CONVERT",
        "CAST", "CONCAT", "CASE", "WHEN", "THEN", "ELSE", "END"
    ));
    
    /**
     * LIKE查询中需要转义的特殊字符
     */
    private static final Map<String, String> LIKE_ESCAPE_MAP = new HashMap<>();
    
    static {
        LIKE_ESCAPE_MAP.put("%", "\\%");
        LIKE_ESCAPE_MAP.put("_", "\\_");
        LIKE_ESCAPE_MAP.put("\\", "\\\\");
        LIKE_ESCAPE_MAP.put("'", "''");
        LIKE_ESCAPE_MAP.put("[", "\\[");
        LIKE_ESCAPE_MAP.put("]", "\\]");
    }
    
    @PostConstruct
    public void init() {
        log.info("SQL安全验证器初始化完成 - 字段名长度限制: {}, 参数值长度限制: {}, 严格模式: {}", 
            maxColumnLength, maxValueLength, strictMode);
    }
    
    /**
     * 验证字段名的安全性
     * 
     * @param columnName 字段名
     * @throws ParameterValidationException 验证失败时抛出
     */
    public void validateColumnName(String columnName) {
        if (columnName == null || columnName.trim().isEmpty()) {
            throw new ParameterValidationException("字段名不能为空");
        }
        
        String trimmedName = columnName.trim();
        
        // 长度检查
        if (trimmedName.length() > maxColumnLength) {
            throw new ParameterValidationException(
                String.format("字段名长度超过限制: %d > %d", trimmedName.length(), maxColumnLength));
        }
        
        // 格式检查
        if (!COLUMN_NAME_PATTERN.matcher(trimmedName).matches()) {
            throw new ParameterValidationException(
                String.format("字段名格式不合法: %s（只允许字母、数字、下划线，且必须以字母开头）", trimmedName));
        }
        
        // SQL关键字检查
        if (strictMode && containsSqlKeywords(trimmedName)) {
            throw new ParameterValidationException(
                String.format("字段名包含SQL关键字: %s", trimmedName));
        }
        
        log.debug("字段名验证通过: {}", trimmedName);
    }
    
    /**
     * 验证参数值的安全性
     * 
     * @param value 参数值
     * @throws ParameterValidationException 验证失败时抛出
     */
    public void validateParameterValue(Object value) {
        if (value == null) {
            return; // null值是安全的
        }
        
        String stringValue = value.toString();
        
        // 长度检查
        if (stringValue.length() > maxValueLength) {
            throw new ParameterValidationException(
                String.format("参数值长度超过限制: %d > %d", stringValue.length(), maxValueLength));
        }
        
        // 严格模式下的SQL注入检查
        if (strictMode && (containsPotentialSqlInjection(stringValue) || detectAdvancedSqlInjection(stringValue))) {
            log.warn("检测到潜在的SQL注入攻击: {}", stringValue.length() > 50 ? 
                stringValue.substring(0, 50) + "..." : stringValue);
            throw new ParameterValidationException("参数值包含潜在的SQL注入代码");
        }
        
        log.debug("参数值验证通过: {}", stringValue.length() > 100 ? 
            stringValue.substring(0, 100) + "..." : stringValue);
    }
    
    /**
     * 转义LIKE查询中的特殊字符
     * 
     * @param value LIKE查询的值
     * @return 转义后的值
     */
    public String escapeLikeValue(String value) {
        if (value == null || value.isEmpty()) {
            return value;
        }
        
        String escapedValue = value;
        for (Map.Entry<String, String> entry : LIKE_ESCAPE_MAP.entrySet()) {
            escapedValue = escapedValue.replace(entry.getKey(), entry.getValue());
        }
        
        log.debug("LIKE值转义: {} -> {}", value, escapedValue);
        return escapedValue;
    }
    
    /**
     * 验证集合参数的安全性
     * 
     * @param collection 集合参数
     * @param maxSize 最大集合大小
     * @throws ParameterValidationException 验证失败时抛出
     */
    public void validateCollectionParameter(Collection<?> collection, int maxSize) {
        if (collection == null) {
            return;
        }
        
        if (collection.size() > maxSize) {
            throw new ParameterValidationException(
                String.format("集合参数大小超过限制: %d > %d", collection.size(), maxSize));
        }
        
        // 验证集合中的每个元素
        for (Object item : collection) {
            validateParameterValue(item);
        }
        
        log.debug("集合参数验证通过 - 大小: {}", collection.size());
    }
    
    /**
     * 检查字符串是否包含SQL关键字
     */
    private boolean containsSqlKeywords(String value) {
        String upperValue = value.toUpperCase();
        return SQL_KEYWORDS.stream().anyMatch(upperValue::contains);
    }
    
    /**
     * 检查字符串是否包含潜在的SQL注入代码
     */
    private boolean containsPotentialSqlInjection(String value) {
        if (value == null || value.trim().isEmpty()) {
            return false;
        }
        
        String upperValue = value.toUpperCase();
        
        // 检查常见的SQL注入模式
        String[] injectionPatterns = {
            "'.*OR.*'",
            "'.*AND.*'",
            "';.*--",
            "'.*UNION.*SELECT",
            "'.*DROP.*TABLE",
            "'.*INSERT.*INTO",
            "'.*UPDATE.*SET",
            "'.*DELETE.*FROM",
            "EXEC\\s*\\(",
            "EXECUTE\\s*\\(",
            "XP_\\w+",
            "SP_\\w+",
            "WAITFOR\\s+DELAY",
            "INFORMATION_SCHEMA"
        };
        
        for (String pattern : injectionPatterns) {
            if (upperValue.matches(".*" + pattern + ".*")) {
                return true;
            }
        }
        
        // 检查多个连续的单引号或双引号
        if (value.contains("''") || value.contains("\"\"")) {
            return true;
        }
        
        // 检查注释符号
        if (upperValue.contains("--") || upperValue.contains("/*") || upperValue.contains("*/")) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 验证数字类型参数
     * 
     * @param value 数字值
     * @param minValue 最小值（可选）
     * @param maxValue 最大值（可选）
     * @throws ParameterValidationException 验证失败时抛出
     */
    public void validateNumericParameter(Object value, Number minValue, Number maxValue) {
        if (value == null) {
            return;
        }
        
        if (!(value instanceof Number)) {
            throw new ParameterValidationException(
                String.format("参数不是有效的数字类型: %s", value.getClass().getSimpleName()));
        }
        
        Number numValue = (Number) value;
        
        // 检查是否为有效数字（排除NaN和无穷大）
        if (numValue instanceof Double) {
            Double doubleValue = (Double) numValue;
            if (doubleValue.isNaN() || doubleValue.isInfinite()) {
                throw new ParameterValidationException("数字参数不能为NaN或无穷大");
            }
        }
        if (numValue instanceof Float) {
            Float floatValue = (Float) numValue;
            if (floatValue.isNaN() || floatValue.isInfinite()) {
                throw new ParameterValidationException("数字参数不能为NaN或无穷大");
            }
        }
        
        // 范围检查
        if (minValue != null && numValue.doubleValue() < minValue.doubleValue()) {
            throw new ParameterValidationException(
                String.format("数字参数小于最小值: %s < %s", numValue, minValue));
        }
        
        if (maxValue != null && numValue.doubleValue() > maxValue.doubleValue()) {
            throw new ParameterValidationException(
                String.format("数字参数大于最大值: %s > %s", numValue, maxValue));
        }
        
        log.debug("数字参数验证通过: {}", numValue);
    }
    
    /**
     * 验证日期类型参数
     * 
     * @param value 日期值
     * @param allowFuture 是否允许将来日期
     * @param allowPast 是否允许过去日期
     * @throws ParameterValidationException 验证失败时抛出
     */
    public void validateDateParameter(Object value, boolean allowFuture, boolean allowPast) {
        if (value == null) {
            return;
        }
        
        LocalDateTime dateTime = null;
        
        try {
            if (value instanceof LocalDateTime) {
                dateTime = (LocalDateTime) value;
            } else if (value instanceof LocalDate) {
                dateTime = ((LocalDate) value).atStartOfDay();
            } else if (value instanceof java.util.Date) {
                dateTime = LocalDateTime.ofInstant(((java.util.Date) value).toInstant(), 
                    java.time.ZoneId.systemDefault());
            } else if (value instanceof java.sql.Timestamp) {
                dateTime = ((java.sql.Timestamp) value).toLocalDateTime();
            } else {
                throw new ParameterValidationException(
                    String.format("参数不是有效的日期类型: %s", value.getClass().getSimpleName()));
            }
        } catch (Exception e) {
            throw new ParameterValidationException(
                String.format("日期参数转换失败: %s", e.getMessage()));
        }
        
        LocalDateTime now = LocalDateTime.now();
        
        if (!allowFuture && dateTime.isAfter(now)) {
            throw new ParameterValidationException("不允许将来日期参数");
        }
        
        if (!allowPast && dateTime.isBefore(now)) {
            throw new ParameterValidationException("不允许过去日期参数");
        }
        
        log.debug("日期参数验证通过: {}", dateTime);
    }
    
    /**
     * 验证字符串参数的特殊字符
     * 
     * @param value 字符串值
     * @param allowedSpecialChars 允许的特殊字符集合
     * @throws ParameterValidationException 验证失败时抛出
     */
    public void validateStringSpecialChars(String value, Set<Character> allowedSpecialChars) {
        if (value == null || value.isEmpty()) {
            return;
        }
        
        Set<Character> foundSpecialChars = new HashSet<>();
        
        for (char c : value.toCharArray()) {
            if (!Character.isLetterOrDigit(c) && !Character.isWhitespace(c)) {
                if (allowedSpecialChars == null || !allowedSpecialChars.contains(c)) {
                    foundSpecialChars.add(c);
                }
            }
        }
        
        if (!foundSpecialChars.isEmpty()) {
            throw new ParameterValidationException(
                String.format("字符串包含不允许的特殊字符: %s", foundSpecialChars));
        }
        
        log.debug("字符串特殊字符验证通过: {}", value.length() > 50 ? 
            value.substring(0, 50) + "..." : value);
    }
    
    /**
     * 增强的SQL注入检测
     * 
     * @param value 待检测的值
     * @return true表示检测到SQL注入风险，false表示安全
     */
    public boolean detectAdvancedSqlInjection(String value) {
        if (value == null || value.trim().isEmpty()) {
            return false;
        }
        
        String upperValue = value.toUpperCase().replaceAll("\\s+", " ");
        
        // 高风险SQL注入模式
        String[] highRiskPatterns = {
            // 经典注入模式
            "'.*(OR|AND).*'.*=.*'",
            "'.*(OR|AND).*1.*=.*1",
            "'.*(OR|AND).*'.*'.*=.*'.*'",
            
            // UNION注入
            "UNION.*SELECT",
            "UNION.*ALL.*SELECT",
            
            // 时间盲注
            "WAITFOR.*DELAY",
            "SLEEP\\s*\\(",
            "BENCHMARK\\s*\\(",
            
            // 堆叠查询
            ";.*CREATE",
            ";.*DROP",
            ";.*INSERT",
            ";.*UPDATE",
            ";.*DELETE",
            ";.*ALTER",
            ";.*EXEC",
            ";.*EXECUTE",
            
            // 系统函数调用
            "XP_CMDSHELL",
            "SP_EXECUTESQL",
            "OPENROWSET",
            "OPENDATASOURCE",
            
            // 注释绕过
            "/\\*.*\\*/",
            "--.*",
            "#.*",
            
            // 编码绕过
            "CHAR\\s*\\(",
            "ASCII\\s*\\(",
            "UNHEX\\s*\\(",
            "HEX\\s*\\(",
            
            // 子查询注入
            "\\(.*SELECT.*\\)",
            "EXISTS\\s*\\(.*SELECT",
            
            // 条件注入
            "CASE.*WHEN.*THEN",
            "IF\\s*\\(.*,.*,.*\\)",
            
            // 错误注入
            "EXTRACTVALUE\\s*\\(",
            "UPDATEXML\\s*\\(",
            "XMLTYPE\\s*\\("
        };
        
        for (String pattern : highRiskPatterns) {
            if (upperValue.matches(".*" + pattern + ".*")) {
                log.warn("检测到高风险SQL注入模式: {} - 值: {}", pattern, 
                    value.length() > 100 ? value.substring(0, 100) + "..." : value);
                return true;
            }
        }
        
        // 检查可疑的字符组合
        int suspiciousScore = 0;
        
        if (upperValue.contains("'")) suspiciousScore += 1;
        if (upperValue.contains("\"")) suspiciousScore += 1;
        if (upperValue.contains("--")) suspiciousScore += 2;
        if (upperValue.contains("/*")) suspiciousScore += 2;
        if (upperValue.contains(";")) suspiciousScore += 1;
        if (upperValue.contains("OR")) suspiciousScore += 1;
        if (upperValue.contains("AND")) suspiciousScore += 1;
        if (upperValue.contains("SELECT")) suspiciousScore += 2;
        if (upperValue.contains("FROM")) suspiciousScore += 1;
        if (upperValue.contains("WHERE")) suspiciousScore += 1;
        if (upperValue.contains("DROP")) suspiciousScore += 3;
        if (upperValue.contains("DELETE")) suspiciousScore += 2;
        if (upperValue.contains("UPDATE")) suspiciousScore += 2;
        if (upperValue.contains("INSERT")) suspiciousScore += 2;
        if (upperValue.contains("EXEC")) suspiciousScore += 3;
        
        if (suspiciousScore >= 4) {
            log.warn("检测到可疑SQL注入组合，风险评分: {} - 值: {}", suspiciousScore,
                value.length() > 100 ? value.substring(0, 100) + "..." : value);
            return true;
        }
        
        return false;
    }
    
    /**
     * 验证查询类型与参数值的兼容性
     * 
     * @param queryType 查询类型
     * @param value 参数值
     * @throws ParameterValidationException 验证失败时抛出
     */
    public void validateQueryTypeCompatibility(QueryType queryType, Object value) {
        if (value == null) {
            return;
        }
        
        switch (queryType) {
            case IN:
            case NOT_IN:
                if (!(value instanceof Collection)) {
                    throw new ParameterValidationException(
                        String.format("%s查询类型需要集合类型参数，实际类型: %s", 
                            queryType, value.getClass().getSimpleName()));
                }
                break;
                
            case BETWEEN:
            case NOT_BETWEEN:
                if (!(value instanceof List)) {
                    throw new ParameterValidationException(
                        String.format("%s查询类型需要List类型参数，实际类型: %s", 
                            queryType, value.getClass().getSimpleName()));
                }
                List<?> list = (List<?>) value;
                if (list.size() != 2) {
                    throw new ParameterValidationException(
                        String.format("%s查询类型需要包含2个元素的List，实际大小: %d", 
                            queryType, list.size()));
                }
                break;
                
            case LIKE:
            case LIKE_LEFT:
            case LIKE_RIGHT:
                if (!(value instanceof String)) {
                    throw new ParameterValidationException(
                        String.format("%s查询类型需要String类型参数，实际类型: %s", 
                            queryType, value.getClass().getSimpleName()));
                }
                break;
                
            case GT:
            case GE:
            case LT:
            case LE:
                if (!(value instanceof Comparable)) {
                    throw new ParameterValidationException(
                        String.format("%s查询类型需要Comparable类型参数，实际类型: %s", 
                            queryType, value.getClass().getSimpleName()));
                }
                break;
                
            default:
                // 其他类型不做特殊检查
                break;
        }
        
        log.debug("查询类型兼容性验证通过 - 类型: {}, 参数类型: {}", 
            queryType, value.getClass().getSimpleName());
    }
    
    /**
     * 获取安全配置信息
     */
    public Map<String, Object> getSecurityConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("maxColumnLength", maxColumnLength);
        config.put("maxValueLength", maxValueLength);
        config.put("strictMode", strictMode);
        config.put("sqlKeywordsCount", SQL_KEYWORDS.size());
        config.put("likeEscapeRulesCount", LIKE_ESCAPE_MAP.size());
        return config;
    }
}