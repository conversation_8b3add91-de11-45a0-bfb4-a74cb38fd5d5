package com.get.officecenter.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.cache.CacheNames;
import com.get.common.consts.CacheKeyConstants;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.GetDateUtil;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.cache.utils.CacheUtil;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.redis.cache.GetRedis;
import com.get.core.redis.lock.RedisLock;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.start.constant.AppConstant;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestContextUtil;
import com.get.officecenter.controller.BaseController;
import com.get.officecenter.dto.GetApplicationFormDaysDto;
import com.get.officecenter.dto.LeaveApplicationFormDto;
import com.get.officecenter.dto.LeaveStockDto;
import com.get.officecenter.dto.MediaAndAttachedDto;
import com.get.officecenter.dto.RemainingDayDto;
import com.get.officecenter.dto.TimeConfigDto;
import com.get.officecenter.dto.query.AiLeaveQueryParam;
import com.get.officecenter.dto.query.LeaveApplicationFormQueryDto;
import com.get.officecenter.entity.LeaveApplicationForm;
import com.get.officecenter.entity.LeaveApplicationFormType;
import com.get.officecenter.entity.LeaveLog;
import com.get.officecenter.entity.LeaveStock;
import com.get.officecenter.entity.OfficeMediaAndAttached;
import com.get.officecenter.entity.WorkScheduleDateConfig;
import com.get.officecenter.entity.WorkScheduleStaffConfig;
import com.get.officecenter.entity.WorkScheduleTimeConfig;
import com.get.officecenter.mapper.LeaveApplicationFormMapper;
import com.get.officecenter.mapper.LeaveApplicationFormTypeMapper;
import com.get.officecenter.mapper.WorkScheduleTimeConfigMapper;
import com.get.officecenter.service.IAsyncReminderService;
import com.get.officecenter.service.ILeaveApplicationFormService;
import com.get.officecenter.service.ILeaveApplicationFormTypeService;
import com.get.officecenter.service.IMediaAndAttachedService;
import com.get.officecenter.service.LeaveLogService;
import com.get.officecenter.service.LeaveStockService;
import com.get.officecenter.service.WorkScheduleDateConfigService;
import com.get.officecenter.service.WorkScheduleStaffConfigService;
import com.get.officecenter.utils.AttendanceUtils;
import com.get.officecenter.utils.MyStringUtils;
import com.get.officecenter.vo.AiLeaveApplicationFormVo;
import com.get.officecenter.vo.ApplicationFormDaysVo;
import com.get.officecenter.vo.ApprovalDelayConfigVo;
import com.get.officecenter.vo.LeaveApplicationFormExportVo;
import com.get.officecenter.vo.LeaveApplicationFormVo;
import com.get.officecenter.vo.LeaveBalance;
import com.get.officecenter.vo.LeaveStockVo;
import com.get.officecenter.vo.OfficeMediaAndAttachedVo;
import com.get.officecenter.vo.RemainingDayVo;
import com.get.officecenter.vo.TimeConfigVo;
import com.get.permissioncenter.entity.Staff;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.dto.RemindTaskDto;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.remindercenter.vo.ReminderTaskCountVo;
import com.get.workflowcenter.feign.IWorkflowCenterClient;
import com.get.workflowcenter.vo.ActHiTaskInstVo;
import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.vo.HiCommentFeignVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.Set;
import java.util.StringJoiner;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author: Sea
 * @create: 2021/4/12 19:14
 * @verison: 1.0
 * @description:
 */
@Slf4j
@Service
public class LeaveApplicationFormServiceImpl implements ILeaveApplicationFormService {

    private static final String cache_key = "task_count";

    @Value("${wx.cptp.corpId}")
    private String CORP_ID;

    @Value("${wx.cptp.corpsecret}")
    private String CORP_SECRET;

    @Resource
    private LeaveApplicationFormMapper leaveApplicationFormMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private IWorkflowCenterClient workflowCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ILeaveApplicationFormTypeService leaveApplicationFormTypeService;
    @Resource
    private LeaveApplicationFormTypeMapper leaveApplicationFormTypeMapper;
    @Lazy
    @Resource
    private LeaveStockService leaveStockService;
    @Resource
    private IReminderCenterClient reminderCenterClient;
    @Resource
    private IMediaAndAttachedService mediaAndAttachedService;
    @Resource
    private IAsyncReminderService asyncReminderService;
    @Resource
    private GetRedis getRedis;
    @Resource
    private WorkScheduleTimeConfigMapper workScheduleTimeConfigMapper;
    @Resource
    private WorkScheduleStaffConfigService workScheduleStaffConfigService;
    @Lazy
    @Resource
    private LeaveLogService leaveLogService;
    @Resource
    private WorkScheduleDateConfigService workScheduleDateConfigService;

    @Override
    public LeaveApplicationFormVo findLeaveApplicationFormById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        LeaveApplicationForm leaveApplicationForm = leaveApplicationFormMapper.selectById(id);
        if (GeneralTool.isEmpty(leaveApplicationForm)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //申请人撤单时在原因加上撤单申请
        if (GeneralTool.isNotEmpty(leaveApplicationForm.getFkLeaveApplicationFormIdRevoke())){
            leaveApplicationForm.setReason("【撤单申请】"+leaveApplicationForm.getReason());
        }
        LeaveApplicationFormVo leaveApplicationFormVo = BeanCopyUtils.objClone(leaveApplicationForm, LeaveApplicationFormVo::new);
//        StaffVo staffVo = permissionCenterClient.getCompanyIdByStaffId(leaveApplicationFormVo.getFkStaffId());
        Result<StaffVo> result = permissionCenterClient.getCompanyIdByStaffId(leaveApplicationFormVo.getFkStaffId());
        StaffVo staffVo = new StaffVo();
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            staffVo = result.getData();
        }

        List<OfficeMediaAndAttached> officeMediaAndAttacheds = mediaAndAttachedService.list(Wrappers.<OfficeMediaAndAttached>lambdaQuery()
                .eq(OfficeMediaAndAttached::getFkTableName, TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)
                .eq(OfficeMediaAndAttached::getFkTableId, id));

        leaveApplicationFormVo.setExistMediaAndAttacheds(GeneralTool.isNotEmpty(officeMediaAndAttacheds));
//        StringBuilder sb = new StringBuilder();
//        sb.append(staffVo.getName());
//        if (GeneralTool.isNotEmpty(staffVo.getNameEn())) {
//            sb.append("（").append(staffVo.getNameEn()).append("）");
//        }
//        staffVo.setFullName(sb.toString());
        //类型名称
        LeaveApplicationFormType formType = leaveApplicationFormTypeService.getById(leaveApplicationFormVo.getFkLeaveApplicationFormTypeId());
        String typeName = "";
        if (GeneralTool.isNotEmpty(formType)){
            typeName = formType.getTypeName();
            leaveApplicationFormVo.setLeaveApplicationFormTypeName(typeName);
            leaveApplicationFormVo.setLeaveApplicationFormTypeKey(formType.getTypeKey());
        }
//        leaveApplicationFormVo.setLeaveApplicationFormTypeName(leaveApplicationFormTypeService.getLeaveApplicationFormTypeNameById(leaveApplicationFormVo.getFkLeaveApplicationFormTypeId()));

        Result<String> companyNameResult = permissionCenterClient.getCompanyNameById(leaveApplicationFormVo.getFkCompanyId());
        if (companyNameResult.isSuccess() && GeneralTool.isNotEmpty(companyNameResult.getData())) {
            //公司名称
            leaveApplicationFormVo.setCompanyName(companyNameResult.getData());
        }
        Result<String> departmentNameResult = permissionCenterClient.getDepartmentNameById(leaveApplicationFormVo.getFkDepartmentId());
        if (departmentNameResult.isSuccess() && GeneralTool.isNotEmpty(departmentNameResult.getData())) {
            //部门名称
            leaveApplicationFormVo.setDepartmentName(departmentNameResult.getData());
        }

        Result<String> officeNameResult = permissionCenterClient.getOfficeNameById(leaveApplicationFormVo.getFkOfficeId());
        if (officeNameResult.isSuccess() && GeneralTool.isNotEmpty(officeNameResult.getData())) {
            //办公室名称
            leaveApplicationFormVo.setOfficeName(officeNameResult.getData());
        }
        //申请人名称
        if (GeneralTool.isNotEmpty(staffVo.getFullName())) {
            leaveApplicationFormVo.setStaffName(staffVo.getFullName());
        }else if (GeneralTool.isNotEmpty(staffVo.getName())){
            leaveApplicationFormVo.setStaffName(staffVo.getName());
        }
        //入职日期
        leaveApplicationFormVo.setEntryDate(staffVo.getEntryDate());

        //年假剩余天数
        BigDecimal annualLeaveDays = leaveStockService.getLeaveStockByStaffId(staffVo.getId(), "annualVacation").getLeaveStockSum();
        if (GeneralTool.isEmpty(annualLeaveDays)) {
            leaveApplicationFormVo.setAnnualLeaveDays(new BigDecimal(0));
        } else {
            leaveApplicationFormVo.setAnnualLeaveDays(annualLeaveDays);
        }
        //补休剩余天数
        BigDecimal compensatoryLeaveDays = leaveStockService.getLeaveStockByStaffId(staffVo.getId(), "takeDeferredHolidays").getLeaveStockSum();
        if (GeneralTool.isEmpty(compensatoryLeaveDays)) {
            leaveApplicationFormVo.setCompensatoryLeaveDays(new BigDecimal(0));
        } else {
            leaveApplicationFormVo.setCompensatoryLeaveDays(compensatoryLeaveDays);
        }
        //工休单状态
        Map<Long, Integer> map = new HashMap<>();
        Result<Map<Long, Integer>> fromIdResult = workflowCenterClient.getFromIdsByStaffId(GetAuthInfo.getStaffId(), TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key);
        if (fromIdResult.isSuccess() && GeneralTool.isNotEmpty(fromIdResult.getData())) {
            map = fromIdResult.getData();
        }
        if (GeneralTool.isEmpty(map.get(leaveApplicationFormVo.getId()))) {
            //我的申请列表和所有表单列表点详情进去的话，该表单又不属于登录人 需要给这个
            leaveApplicationFormVo.setExpenseClaimFormStatus(2);
        } else {
            leaveApplicationFormVo.setExpenseClaimFormStatus(map.get(leaveApplicationFormVo.getId()));
        }
        //流程对象
        Map<Long, ActRuTaskVo> actRuTaskDtoMap = null;
        Map<String, List<Long>> businessIdsWithProcdefKey = new HashMap<>();
        businessIdsWithProcdefKey.put(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key, Collections.singletonList(id));
        Result<Map<Long, ActRuTaskVo>> taskResult = workflowCenterClient.getActRuTaskDtosByBusinessKey(businessIdsWithProcdefKey);
        if (taskResult.isSuccess() && GeneralTool.isNotEmpty(taskResult.getData())) {
            actRuTaskDtoMap = taskResult.getData();
            ActRuTaskVo actRuTaskVo = actRuTaskDtoMap.get(id);
            if (GeneralTool.isNotEmpty(actRuTaskVo.getId())) {
                leaveApplicationFormVo.setTaskId(Long.valueOf(actRuTaskVo.getId()));
            }
            if (GeneralTool.isNotEmpty(actRuTaskVo.getProcInstId())) {
                leaveApplicationFormVo.setProcInstId(Long.valueOf(actRuTaskVo.getProcInstId()));
            }
            if (GeneralTool.isNotEmpty(actRuTaskVo.getTaskVersion())) {
                leaveApplicationFormVo.setTaskVersion(actRuTaskVo.getTaskVersion());
            }
        }
        Result<HiCommentFeignVo> hiCommentFeignDtoResult = workflowCenterClient.getHiComment(id, TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key);
        if (hiCommentFeignDtoResult.isSuccess() && GeneralTool.isNotEmpty(hiCommentFeignDtoResult.getData())) {
            HiCommentFeignVo hiComment = hiCommentFeignDtoResult.getData();
            if (GeneralTool.isNotEmpty(hiComment)) {
                leaveApplicationFormVo.setAgreeButtonType(hiComment.getAgreeButtonType());
                leaveApplicationFormVo.setRefuseButtonType(hiComment.getRefuseButtonType());
            }
        }

        if (formType.getTypeKey().equals(ProjectKeyEnum.TAKE_DEFERRED_HOLIDAYS.key)||formType.getTypeKey().equals(ProjectKeyEnum.ANNUAL_VACATION.key)){
            List<LeaveApplicationForm> leaveApplicationForms = leaveApplicationFormMapper.selectList(Wrappers.lambdaQuery(LeaveApplicationForm.class)
                    .eq(LeaveApplicationForm::getFkStaffId, leaveApplicationForm.getFkStaffId())
                    .eq(LeaveApplicationForm::getStatus, ProjectExtraEnum.APPROVAL_IN_PROGRESS.key)
                    .eq(LeaveApplicationForm::getFkLeaveApplicationFormTypeId, leaveApplicationForm.getFkLeaveApplicationFormTypeId()));

            if (GeneralTool.isNotEmpty(leaveApplicationForms)){
                BigDecimal approvalStock = leaveApplicationForms.stream().map(LeaveApplicationForm::getDays).reduce(BigDecimal.ZERO, BigDecimal::add);
                leaveApplicationFormVo.setApplyingStockRemind("有"+ approvalStock.toString()+"小时"+typeName+"申请正在审批中，未进行扣减。");
            }

        }

        MediaAndAttachedDto attachedVo = new MediaAndAttachedDto();
        attachedVo.setFkTableName(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key);
        attachedVo.setFkTableId(id);
        List<OfficeMediaAndAttachedVo> mediaAndAttachedDtos = attachedService.getMediaAndAttachedDto(attachedVo);
        leaveApplicationFormVo.setMediaAndAttachedDtos(mediaAndAttachedDtos);

        return leaveApplicationFormVo;
    }

    //    @Transactional(propagation = Propagation.REQUIRED,rollbackFor = YException.class)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addLeaveApplicationForm(LeaveApplicationFormDto leaveApplicationFormDto) {
        if (GeneralTool.isEmpty(leaveApplicationFormDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //校验日期
        if (leaveApplicationFormDto.getStartTime().after(leaveApplicationFormDto.getEndTime())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("start_time_end_time_error"));
        }
        //校验HTI,加班时长需要大于2小时
            if(leaveApplicationFormDto.getFkLeaveApplicationFormTypeId().equals(12L) && GeneralTool.isEmpty(leaveApplicationFormDto.getFkLeaveApplicationFormIdRevoke())){
                if(leaveApplicationFormDto.getDays().compareTo(new BigDecimal(2)) < 0){
                    throw new GetServiceException(LocaleMessageUtils.getMessage("apply_for_overtime_fail"));
                }
            }
            //hti用户没有直属上司提示
            Result<Long> supervisorId = permissionCenterClient.getStaffSupervisorIdByStaffId(leaveApplicationFormDto.getFkStaffId());

            if (GeneralTool.isEmpty(supervisorId.getData())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_immediate_superior"));
            }

        //校验请假时间最低一个钟
        if (GeneralTool.isEmpty(leaveApplicationFormDto.getFkLeaveApplicationFormIdRevoke())) {
            //父id为空，说明为工休单 不是撤回申请
            BigDecimal subtractResult = leaveApplicationFormDto.getDays().subtract(new BigDecimal(1));
            //小于1个钟
            if (subtractResult.compareTo(new BigDecimal(0)) < 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("leaveApplicationForm_days_error"));
            }
        }
        validateLeaveType(leaveApplicationFormDto);
        LeaveApplicationForm leaveApplicationForm = BeanCopyUtils.objClone(leaveApplicationFormDto, LeaveApplicationForm::new);
        //设置表单编号
        String leaveApplicationFormNum = MyStringUtils.getLeaveApplicationFormNum();
        leaveApplicationForm.setNum(leaveApplicationFormNum);
        //待发起状态
        leaveApplicationForm.setStatus(0);
        utilService.updateUserInfoToEntity(leaveApplicationForm);
        leaveApplicationFormMapper.insert(leaveApplicationForm);
        //如果是撤销单-则把原单改状态
        if (GeneralTool.isNotEmpty(leaveApplicationFormDto.getFkLeaveApplicationFormIdRevoke())) {
            LeaveApplicationForm parentLeaveApplicationForm = leaveApplicationFormMapper.selectById(leaveApplicationFormDto.getFkLeaveApplicationFormIdRevoke());
            if (GeneralTool.isEmpty(parentLeaveApplicationForm)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            //改为已撤单状态
            parentLeaveApplicationForm.setStatus(ProjectExtraEnum.REVOKED.key);
            utilService.updateUserInfoToEntity(parentLeaveApplicationForm);
            leaveApplicationFormMapper.updateById(parentLeaveApplicationForm);
        }

        if (GeneralTool.isNotEmpty(leaveApplicationFormDto.getMediaAndAttachedVos())){
            for (MediaAndAttachedDto mediaAndAttachedDto : leaveApplicationFormDto.getMediaAndAttachedVos()) {
                if (GeneralTool.isEmpty(mediaAndAttachedDto.getFkTableName())){
                    mediaAndAttachedDto.setFkTableName(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key);
                }
                mediaAndAttachedDto.setFkTableId(leaveApplicationForm.getId());
            }
            Boolean aBoolean = attachedService.saveBatchMediaAndAttached(leaveApplicationFormDto.getMediaAndAttachedVos());
            if (!aBoolean){
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
        }

        //验证能否发起
        validateDelay(leaveApplicationForm);

        CacheUtil.evict(CacheNames.TASK_CACHE,String.valueOf(leaveApplicationForm.getFkStaffId()), cache_key);
        return leaveApplicationForm.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAddLeaveApplicationForm(ValidList<LeaveApplicationFormDto> leaveApplicationFormDtos) {
        if (leaveApplicationFormDtos.size()>1){
            Set<Long> typeIds = leaveApplicationFormDtos.stream().map(LeaveApplicationFormDto::getFkLeaveApplicationFormTypeId).collect(Collectors.toSet());
            List<LeaveApplicationFormType> leaveApplicationFormTypes = leaveApplicationFormTypeMapper.selectList(null);
            Set<Long> allowToAddIds = new HashSet<>();
            for (LeaveApplicationFormType leaveApplicationFormType : leaveApplicationFormTypes) {
                if (!ProjectKeyEnum.ONLINE_WORK.key.equals(leaveApplicationFormType.getTypeKey())
                        && !ProjectKeyEnum.OUTSOURCING.key.equals(leaveApplicationFormType.getTypeKey())
                        && !ProjectKeyEnum.EVECTION.key.equals(leaveApplicationFormType.getTypeKey())){
                    allowToAddIds.add(leaveApplicationFormType.getId());
                }
            }
            if (!allowToAddIds.containsAll(typeIds)){
                throw new GetServiceException(LocaleMessageUtils.getMessage("only_vacation_type"));
            }
            //生成唯一的uuid
            String uuid = UUID.randomUUID().toString();;
            for (LeaveApplicationFormDto leaveApplicationFormDto : leaveApplicationFormDtos) {
                Long aLong = addLeaveApplicationForm(leaveApplicationFormDto);
                LeaveApplicationForm leaveApplicationForm = leaveApplicationFormMapper.selectById(aLong);
                leaveApplicationForm.setGroupGuid(uuid);
                utilService.setUpdateInfo(leaveApplicationForm);
                leaveApplicationFormMapper.updateById(leaveApplicationForm);
            }
        }else if (leaveApplicationFormDtos.size()==1){
            //生成唯一的uuid
            String uuid = UUID.randomUUID().toString();;
            for (LeaveApplicationFormDto leaveApplicationFormDto : leaveApplicationFormDtos) {
                Long aLong = addLeaveApplicationForm(leaveApplicationFormDto);
                LeaveApplicationForm leaveApplicationForm = leaveApplicationFormMapper.selectById(aLong);
                leaveApplicationForm.setGroupGuid(uuid);
                utilService.setUpdateInfo(leaveApplicationForm);
                leaveApplicationFormMapper.updateById(leaveApplicationForm);
            }
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public LeaveApplicationFormVo updateLeaveApplicationForm(LeaveApplicationFormDto leaveApplicationFormDto) {
        if (leaveApplicationFormDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        LeaveApplicationForm result = leaveApplicationFormMapper.selectById(leaveApplicationFormDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //校验日期
        if (leaveApplicationFormDto.getStartTime().after(leaveApplicationFormDto.getEndTime())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("start_time_end_time_error"));
        }
        validateLeaveType(leaveApplicationFormDto);
        LeaveApplicationForm leaveApplicationForm = BeanCopyUtils.objClone(leaveApplicationFormDto, LeaveApplicationForm::new);
        utilService.updateUserInfoToEntity(leaveApplicationForm);
        leaveApplicationFormMapper.updateById(leaveApplicationForm);

//        MediaAndAttachedVo attachedVo = new MediaAndAttachedVo();
//        attachedVo.setFkTableName(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key);
//        attachedVo.setFkTableId(leaveApplicationFormDto.getId());
//        List<OfficeMediaAndAttachedVo> mediaAndAttachedDtos = attachedService.getMediaAndAttachedDto(attachedVo);

        //先删
        attachedService.remove(Wrappers.lambdaQuery(OfficeMediaAndAttached.class)
                .eq(OfficeMediaAndAttached::getFkTableId, leaveApplicationFormDto.getId())
                .eq(OfficeMediaAndAttached::getFkTableName,TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)
                .eq(OfficeMediaAndAttached::getTypeKey,FileTypeEnum.OFFICE_LEAVE_APPLICATION_FORM_FILE.key)
        );
        List<MediaAndAttachedDto> mediaAndAttachedDtos = leaveApplicationFormDto.getMediaAndAttachedVos();

        if (GeneralTool.isEmpty(mediaAndAttachedDtos)){
            return findLeaveApplicationFormById(leaveApplicationFormDto.getId());
        }
        mediaAndAttachedDtos.forEach(mediaAndAttachedVo -> mediaAndAttachedVo.setFkTableName(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key));
        Boolean aBoolean = attachedService.saveBatchMediaAndAttached(leaveApplicationFormDto.getMediaAndAttachedVos());
        if (!aBoolean){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return findLeaveApplicationFormById(leaveApplicationFormDto.getId());
    }

    @Override
    public List<LeaveApplicationFormVo> getLeaveApplicationForms(LeaveApplicationFormQueryDto leaveApplicationFormQueryDto, Page page) {
        final String myApplicationStatus = "0";
        final String allStatus = "1";
        final String myApprovalStatus = "2";

        Map<Long, Integer> map = new HashMap<>();
        Result<Map<Long, Integer>> fromIdsResult = workflowCenterClient.getFromIdsByStaffId(GetAuthInfo.getStaffId(), TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key);
        if (fromIdsResult.isSuccess() && GeneralTool.isNotEmpty(fromIdsResult.getData())) {
            map = fromIdsResult.getData();
        }
//        Example example = new Example(LeaveApplicationForm.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<LeaveApplicationForm> leaveApplicationFormLambdaQueryWrapper = new LambdaQueryWrapper<>();


        //不选所属公司时
        if (GeneralTool.isEmpty(leaveApplicationFormQueryDto) || GeneralTool.isEmpty(leaveApplicationFormQueryDto.getFkCompanyId())) {
            List<Long> companyIds = SecureUtil.getCompanyIds();
//            criteria.andIn("fkCompanyId", companyIds);
            leaveApplicationFormLambdaQueryWrapper.in(LeaveApplicationForm::getFkCompanyId, companyIds);
        }

        if (GeneralTool.isNotEmpty(leaveApplicationFormQueryDto.getBigType())){
            String examiningType = "examining";
            String finishingType = "completed";
            List<Integer> statusList = new ArrayList<>();
            if (examiningType.equals(leaveApplicationFormQueryDto.getBigType())){
                statusList.add(ProjectExtraEnum.TO_BE_INITIATED.key);
                statusList.add(ProjectExtraEnum.APPROVAL_IN_PROGRESS.key);
                statusList.add(ProjectExtraEnum.APPROVAL_REJECT.key);
            }else if (finishingType.equals(leaveApplicationFormQueryDto.getBigType())){
                statusList.add(ProjectExtraEnum.APPROVAL_FINISHED.key);
                statusList.add(ProjectExtraEnum.APPROVAL_ABANDONED.key);
                statusList.add(ProjectExtraEnum.CANCELLATION.key);
                statusList.add(ProjectExtraEnum.REVOKED.key);
            }
            leaveApplicationFormLambdaQueryWrapper.in(LeaveApplicationForm::getStatus, statusList);
        }
        if (GeneralTool.isNotEmpty(leaveApplicationFormQueryDto)) {
            //查询条件-所属公司
            if (GeneralTool.isNotEmpty(leaveApplicationFormQueryDto.getFkCompanyId())) {
                if (!SecureUtil.validateCompany(leaveApplicationFormQueryDto.getFkCompanyId())) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
                }
//                criteria.andEqualTo("fkCompanyId", leaveApplicationFormVo.getFkCompanyId());
                leaveApplicationFormLambdaQueryWrapper.eq(LeaveApplicationForm::getFkCompanyId, leaveApplicationFormQueryDto.getFkCompanyId());
            }
            //查询条件-表单类型
            if (GeneralTool.isNotEmpty(leaveApplicationFormQueryDto.getFkLeaveApplicationFormTypeId())) {
//                criteria.andEqualTo("fkLeaveApplicationFormTypeId", leaveApplicationFormVo.getFkLeaveApplicationFormTypeId());
                leaveApplicationFormLambdaQueryWrapper.eq(LeaveApplicationForm::getFkLeaveApplicationFormTypeId, leaveApplicationFormQueryDto.getFkLeaveApplicationFormTypeId());
            }
            //查询条件-表单编号
            if (GeneralTool.isNotEmpty(leaveApplicationFormQueryDto.getNum())) {
//                criteria.andLike("num", "%" + leaveApplicationFormVo.getNum() + "%");
                leaveApplicationFormLambdaQueryWrapper.like(LeaveApplicationForm::getNum, leaveApplicationFormQueryDto.getNum());
            }
            //查询条件-请假开始日期
            if (GeneralTool.isNotEmpty(leaveApplicationFormQueryDto.getStartTime())) {
//                criteria.andGreaterThanOrEqualTo("startTime", leaveApplicationFormVo.getStartTime());
                leaveApplicationFormLambdaQueryWrapper.ge(LeaveApplicationForm::getStartTime, leaveApplicationFormQueryDto.getStartTime());
            }
            //查询条件-请假结束日期
            if (GeneralTool.isNotEmpty(leaveApplicationFormQueryDto.getEndTime())) {
//                criteria.andLessThanOrEqualTo("endTime", leaveApplicationFormVo.getEndTime());
                leaveApplicationFormLambdaQueryWrapper.le(LeaveApplicationForm::getEndTime, leaveApplicationFormQueryDto.getEndTime());
            }
            //查询条件-表单创建日期-开始
            if (GeneralTool.isNotEmpty(leaveApplicationFormQueryDto.getCreateStartTime())) {
//                criteria.andGreaterThanOrEqualTo("gmtCreate", leaveApplicationFormVo.getCreateStartTime());
                leaveApplicationFormLambdaQueryWrapper.ge(LeaveApplicationForm::getGmtCreate, leaveApplicationFormQueryDto.getCreateStartTime());
            }
            //查询条件-表单创建日期-结束
            if (GeneralTool.isNotEmpty(leaveApplicationFormQueryDto.getCreateEndTime())) {
//                criteria.andLessThanOrEqualTo("gmtCreate", leaveApplicationFormVo.getCreateEndTime());
                leaveApplicationFormLambdaQueryWrapper.le(LeaveApplicationForm::getGmtCreate, leaveApplicationFormQueryDto.getCreateEndTime());
            }
            //查询条件-原因
            if (GeneralTool.isNotEmpty(leaveApplicationFormQueryDto.getReason())) {
//                criteria.andLike("reason", "%"+leaveApplicationFormVo.getReason()+"%");
                leaveApplicationFormLambdaQueryWrapper.like(LeaveApplicationForm::getReason, leaveApplicationFormQueryDto.getReason());
            }
            //查询条件-审批状态
            if (GeneralTool.isNotEmpty(leaveApplicationFormQueryDto.getStatus())) {
//                criteria.andEqualTo("status", leaveApplicationFormVo.getStatus());
                leaveApplicationFormLambdaQueryWrapper.eq(LeaveApplicationForm::getStatus, leaveApplicationFormQueryDto.getStatus());
            }
            //查询条件-创建人
            if (GeneralTool.isNotEmpty(leaveApplicationFormQueryDto.getGmtCreateUser())) {
//                criteria.andLike("gmtCreateUser", "%"+leaveApplicationFormVo.getGmtCreateUser()+"%");
                List<Long> staffIds = permissionCenterClient.getStaffIdsByLikeCondition(leaveApplicationFormQueryDto.getGmtCreateUser()).getData();
                if (GeneralTool.isEmpty(staffIds)){
                    staffIds.add(0L);
                }
//                leaveApplicationFormLambdaQueryWrapper.like(LeaveApplicationForm::getGmtCreateUser, leaveApplicationFormVo.getGmtCreateUser());
                leaveApplicationFormLambdaQueryWrapper.in(LeaveApplicationForm::getFkStaffId, staffIds);
            }
            if (GeneralTool.isNotEmpty(leaveApplicationFormQueryDto.getFkDepartmentId())){
                leaveApplicationFormLambdaQueryWrapper.eq(LeaveApplicationForm::getFkDepartmentId, leaveApplicationFormQueryDto.getFkDepartmentId());
            }
            //根据不同的选择状态拼接不同的条件
            if (myApplicationStatus.equals(leaveApplicationFormQueryDto.getSelectStatus())) {
                //0表示要显示我的申请列表，即我创建的表单
//                criteria.andEqualTo("fkStaffId", StaffContext.getStaff().getId());
                leaveApplicationFormLambdaQueryWrapper.eq(LeaveApplicationForm::getFkStaffId, GetAuthInfo.getStaffId());
            } else if (allStatus.equals(leaveApplicationFormQueryDto.getSelectStatus())) {
                //1表示所有表单

            } else if (myApprovalStatus.equals(leaveApplicationFormQueryDto.getSelectStatus())) {
                //2表示显示我的审批列表，即我操作过的表单都要显示
                List<Long> fromIds = new ArrayList<>(map.keySet());
                if (GeneralTool.isEmpty(fromIds)) {
                    fromIds.add(0L);
                }
//                criteria.andIn("id", fromIds);
                leaveApplicationFormLambdaQueryWrapper.in(LeaveApplicationForm::getId, fromIds);

                //只显示在职申请人的申请信息
                List<LeaveApplicationForm> leaveApplicationForms = leaveApplicationFormMapper.selectList(leaveApplicationFormLambdaQueryWrapper);
                //在职申请人集合
                Set<Long> staffIds = new HashSet<>();
                for (LeaveApplicationForm leaveApplicationForm : leaveApplicationForms) {
                    staffIds.add(leaveApplicationForm.getFkStaffId());
                }
                //查询在职人员
                List<StaffVo> staffVoByIds = permissionCenterClient.getStaffDtoByIds(staffIds);
                //在职人员Ids
                if (GeneralTool.isNotEmpty(staffVoByIds)){
                    List<Long> longList = staffVoByIds.stream().map(s -> s.getId()).collect(Collectors.toList());
                    leaveApplicationFormLambdaQueryWrapper.in(LeaveApplicationForm::getFkStaffId, longList);
                }

            }
        }
//        example.orderBy("gmtCreate").desc();
        leaveApplicationFormLambdaQueryWrapper.orderByDesc(LeaveApplicationForm::getGmtCreate);
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        List<LeaveApplicationForm> leaveApplicationForms = Lists.newArrayList();
        IPage<LeaveApplicationForm> pages = null;
        if (GeneralTool.isNotEmpty(page)){
            pages = leaveApplicationFormMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), leaveApplicationFormLambdaQueryWrapper);
            leaveApplicationForms = pages.getRecords();
        }else {
            leaveApplicationForms = leaveApplicationFormMapper.selectList(leaveApplicationFormLambdaQueryWrapper);
        }


        if (GeneralTool.isEmpty(leaveApplicationForms)){
            return new ArrayList<>();
        }
        List<String> loginIds = leaveApplicationForms.stream().map(LeaveApplicationForm::getGmtCreateUser).collect(Collectors.toList());
        List<Staff> staffGmtCreateList = permissionCenterClient.getStaffByLoginIds(loginIds);
        Map<String, Staff> staffMap = staffGmtCreateList.stream().collect(Collectors.toMap(Staff::getLoginId, Function.identity()));
//        System.out.println(staffIdsMap);
//        page.restPage(leaveApplicationForms);
        List<LeaveApplicationFormVo> convertDatas = new ArrayList<>();
        //公司id集合
        Set<Long> companyIds = new HashSet<>();
        //申请人id集合
        Set<Long> staffIds = new HashSet<>();
        //报销单id集合
        List<Long> leaveApplicationFormIds = new ArrayList<>();
        //获取各自集合的值
        for (LeaveApplicationForm leaveApplicationForm : leaveApplicationForms) {
            companyIds.add(leaveApplicationForm.getFkCompanyId());
            staffIds.add(leaveApplicationForm.getFkStaffId());
            leaveApplicationFormIds.add(leaveApplicationForm.getId());
        }
        //员工在职状态
        Result<Map<Long, Boolean>> reDuty = permissionCenterClient.getStaffIsOnDuty(staffIds);
        Map<Long, Boolean> onDutyMap = new HashMap<>();
        if (reDuty.isSuccess()) {
            if (GeneralTool.isNotEmpty(reDuty.getData())) {
                onDutyMap = reDuty.getData();
            }
        }
        //feign调用 获取公司 id-name的map
        companyIds.removeIf(Objects::isNull);
        Map<Long, String> companyNameMap = new HashMap<>();
        Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(companyIds);
        if (result.isSuccess() && result.getData() != null) {
            companyNameMap = result.getData();
        }

        //feign调用 获取流程方面dto 报销单id-actRuTaskDot的map
        leaveApplicationFormIds.removeIf(Objects::isNull);
        Map<Long, ActRuTaskVo> actRuTaskDtoMap = null;
        Map<String, List<Long>> businessIdsWithProcdefKey = new HashMap<>();
        businessIdsWithProcdefKey.put(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key, leaveApplicationFormIds);
        Result<Map<Long, ActRuTaskVo>> taskResult = workflowCenterClient.getActRuTaskDtosByBusinessKey(businessIdsWithProcdefKey);
        if (taskResult.isSuccess() && GeneralTool.isNotEmpty(taskResult.getData())) {
            if (GeneralTool.isNotEmpty(pages)){
                page.setAll((int) pages.getTotal());//成功获取到数据后再写入分页信息
            }
            actRuTaskDtoMap = taskResult.getData();

            List<ActRuTaskVo> actRuTaskVos = new ArrayList<>();
            if (GeneralTool.isNotEmpty(actRuTaskDtoMap)) {
                for (Map.Entry<Long, ActRuTaskVo> longActRuTaskDtoEntry : actRuTaskDtoMap.entrySet()) {
                    actRuTaskVos.add(longActRuTaskDtoEntry.getValue());
                }

            }
            Set<Long> assigneeIds = new HashSet<>();
            if (GeneralTool.isNotEmpty(actRuTaskVos)) {
                assigneeIds = actRuTaskVos.stream()
                        .filter(actRuTaskDto -> GeneralTool.isNotEmpty(actRuTaskDto.getAssignee()))
                        .map(actRuTaskDto -> Long.valueOf(actRuTaskDto.getAssignee()))
                        .collect(Collectors.toSet());
            }
            staffIds.addAll(assigneeIds);

            //feign调用 获取报销人id-name的map
            staffIds.removeIf(Objects::isNull);
            Map<Long, String> staffNameMap = permissionCenterClient.getStaffNameMap(staffIds).getData();
//            List<StaffVo> staffDtos = permissionCenterClient.getStaffDtoByIds(staffIds);
//            Map<Long, String> staffNameChnMap = new HashMap<>();
//            if (GeneralTool.isNotEmpty(staffDtos)){
//                staffNameChnMap = staffDtos.stream().collect(Collectors.toMap(StaffVo::getId, StaffVo::getName));
//            }

            //部门ids
            Set<Long> fkDepartmentIds = leaveApplicationForms.stream().map(LeaveApplicationForm::getFkDepartmentId).collect(Collectors.toSet());
            //部门name map
            Map<Long, String> departmentNameMap = Maps.newHashMap();
            if (GeneralTool.isNotEmpty(fkDepartmentIds)){
                departmentNameMap = permissionCenterClient.getDepartmentNamesByIds(fkDepartmentIds).getData();
            }

            Set<Long> ids = leaveApplicationForms.stream().map(LeaveApplicationForm::getId).collect(Collectors.toSet());

            List<OfficeMediaAndAttached> officeMediaAndAttacheds = mediaAndAttachedService.list(Wrappers.<OfficeMediaAndAttached>lambdaQuery()
                    .eq(OfficeMediaAndAttached::getFkTableName, TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)
                    .in(OfficeMediaAndAttached::getFkTableId, ids));

            Map<Long, List<OfficeMediaAndAttached>> officeMediaAndAttachedsMap = officeMediaAndAttacheds.stream().collect(Collectors.groupingBy(OfficeMediaAndAttached::getFkTableId));

            for (LeaveApplicationForm leaveApplicationForm : leaveApplicationForms) {
                LeaveApplicationFormVo leaveApplicationFormVo = BeanCopyUtils.objClone(leaveApplicationForm, LeaveApplicationFormVo::new);
                //类型名称
                leaveApplicationFormVo.setLeaveApplicationFormTypeName(leaveApplicationFormTypeService.getLeaveApplicationFormTypeNameById(leaveApplicationFormVo.getFkLeaveApplicationFormTypeId()));
                //公司名称
                leaveApplicationFormVo.setCompanyName(companyNameMap.get(leaveApplicationFormVo.getFkCompanyId()));
                //申请人名称
                leaveApplicationFormVo.setStaffName(staffNameMap.get(leaveApplicationFormVo.getFkStaffId()));
                Staff staff = staffMap.get(leaveApplicationFormVo.getGmtCreateUser());
                if (GeneralTool.isNotEmpty(staff)) {
                    leaveApplicationFormVo.setGmtCreateUser(staff.getName() + "（" + staff.getNameEn() + "）");
                }
                //员工在职状态
                leaveApplicationFormVo.setIsOnDuty(onDutyMap.get(staff.getId()));
                //申请人撤单时在原因加上撤单申请
                if (GeneralTool.isNotEmpty(leaveApplicationForm.getFkLeaveApplicationFormIdRevoke())) {
                    leaveApplicationFormVo.setReason("【撤单申请】" + leaveApplicationForm.getReason());
                }
                //工休单状态
                leaveApplicationFormVo.setExpenseClaimFormStatus(map.get(leaveApplicationFormVo.getId()));
                //部门名称
                leaveApplicationFormVo.setDepartmentName(departmentNameMap.get(leaveApplicationFormVo.getFkDepartmentId()));
                //流程对象
                ActRuTaskVo actRuTaskVo = actRuTaskDtoMap.get(leaveApplicationFormVo.getId());
                //正在进行的任务id
                if (GeneralTool.isNotEmpty(actRuTaskVo.getId())) {
                    leaveApplicationFormVo.setTaskId(Long.valueOf(actRuTaskVo.getId()));
                }
                //流程实例id
                if (GeneralTool.isNotEmpty(actRuTaskVo.getProcInstId())) {
                    leaveApplicationFormVo.setProcInstId(Long.valueOf(actRuTaskVo.getProcInstId()));
                }
                //任务版本
                if (GeneralTool.isNotEmpty(actRuTaskVo.getTaskVersion())) {
                    leaveApplicationFormVo.setTaskVersion(actRuTaskVo.getTaskVersion());
                }
                //审批人名称
                if (GeneralTool.isNotEmpty(actRuTaskVo.getAssignee())) {
                    leaveApplicationFormVo.setAssigneeName(staffNameMap.get(Long.valueOf(actRuTaskVo.getAssignee())));
                }

                if(GeneralTool.isNotEmpty(officeMediaAndAttachedsMap)&&GeneralTool.isNotEmpty(officeMediaAndAttachedsMap.get(leaveApplicationFormVo.getId()))){
                    leaveApplicationFormVo.setExistMediaAndAttacheds(GeneralTool.isNotEmpty(officeMediaAndAttachedsMap.get(leaveApplicationFormVo.getId())));
                }else {
                    leaveApplicationFormVo.setExistMediaAndAttacheds(false);
                }

                convertDatas.add(leaveApplicationFormVo);
            }
        }else{
            if (GeneralTool.isNotEmpty(pages)){
                page.setAll((int) pages.getTotal());//成功获取到数据后再写入分页信息
            }
        }
        return convertDatas;
    }

    @Override
    public List<OfficeMediaAndAttachedVo> getItemMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<OfficeMediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        Long fkTableId = mediaAttachedVos.get(0).getFkTableId();
        LeaveApplicationForm data = getLeaveApplicationForm(fkTableId);
        String typeKey = getLeaveTypeKeyById(fkTableId);
        if (ProjectKeyEnum.DISEASE_VACATION.key.equals(typeKey)){
            List<ActHiTaskInstVo> actHiTaskInstVos = workflowCenterClient.getActHiTaskInstDtosByBusinessKey(String.valueOf(fkTableId), TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key).getData();
            if (GeneralTool.isNotEmpty(actHiTaskInstVos)){
                String procInstId = actHiTaskInstVos.get(0).getProcInstId();
                Object isHavingAttachments = workflowCenterClient.getVariableByHisInstanceAndName(procInstId, "isHavingAttachments").getData();
                if (GeneralTool.isNotEmpty(isHavingAttachments)){
                    String taskId = actHiTaskInstVos.get(0).getId();
                    if (isHavingAttachments.equals(0)){
                        List<RemindTaskDto> remindTaskDtos = new ArrayList<>();
                        Set<String> nums = new HashSet<>(1);
                        nums.add("ZLSH01");
                        List<Long> ids = permissionCenterClient.getStaffIdsByPositionNums(nums);
                        StaffVo staffVo = permissionCenterClient.getCompanyIdByStaffId(data.getFkStaffId()).getData();
                        if (GeneralTool.isNotEmpty(ids)&&GeneralTool.isNotEmpty(staffVo)){
                            Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.FILE_SRC_PREFIX.key, 2).getData();
                            String configValue2 = companyConfigMap.get(SecureUtil.getFkCompanyId());
                            String domainName = "";
                            if (GeneralTool.isNotEmpty(configValue2)){
                                domainName = configValue2;
                            } else {
                                Result<String> domainNameResult = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.FILE_SRC_PREFIX.key);
                                if (domainNameResult.isSuccess() && GeneralTool.isNotEmpty(domainNameResult.getData())) {
                                    domainName = domainNameResult.getData();
                                }
                            }

//                            String domainName = "";
//                            Properties props = System.getProperties();
//                            String profile = props.getProperty("spring.profiles.active");
//                            if (GeneralTool.isNotEmpty(profile)) {
//                                if (profile.equals(AppConstant.PROD_CODE) || profile.equals(AppConstant.IAE_CODE)|| profile.equals(AppConstant.TW_CODE)) {
//                                    ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.FILE_SRC_PREFIX.key).getData();
//                                    if (GeneralTool.isNotEmpty(configDto)){
//                                        String configJson = configDto.getValue2();
//                                        JSONObject configJsonObject = JSON.parseObject(configJson);
//                                        if (SecureUtil.getFkCompanyId().equals(3L)){
//                                            domainName = configJsonObject.getString("IAE");
//                                        }else {
//                                            domainName = configJsonObject.getString("OTHER");
//                                        }
//                                    }
//                                }else {
//                                    Result<String> domainNameResult = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.FILE_SRC_PREFIX.key);
//                                    if (domainNameResult.isSuccess() && GeneralTool.isNotEmpty(domainNameResult.getData())) {
//                                        domainName = domainNameResult.getData();
//                                    }
//                                }
//                            }

//                            ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.FILE_SRC_PREFIX.key).getData();
//                            if (GeneralTool.isNotEmpty(configDto)){
//                                String configJson = configDto.getValue2();
//                                JSONObject configJsonObject = JSON.parseObject(configJson);
//                                if (SecureUtil.getFkCompanyId().equals(3L)){
//                                    domainName = configJsonObject.getString("IAE");
//                                }else {
//                                    domainName = configJsonObject.getString("OTHER");
//                                }
//                            }

                            for (Long id : ids) {
                                //发送邮件
                                StringBuilder stringBuilder = new StringBuilder();
                                stringBuilder.append("工休申请流程");
                                if (GeneralTool.isNotEmpty(staffVo)) {
                                    stringBuilder.append("-");
                                    if (GeneralTool.isNotEmpty(staffVo.getName())) {
                                        stringBuilder.append(staffVo.getName());
                                    }
                                    if (GeneralTool.isNotEmpty(staffVo.getNameEn())) {
                                        stringBuilder.append("（").append(staffVo.getNameEn()).append("）");
                                    }
                                }
                                stringBuilder.append("-").append("资料已补充，可进行审批");

//                                RemindTaskDto remindTask = new RemindTaskDto();
//                                remindTask.setFkTableName(TableEnum.ACT_HI_TASKINST.key);
//                                remindTask.setFkTableId(Long.valueOf(taskId));
//                                remindTask.setFkStaffId(id);
//                                remindTask.setStartTime(new Date());
//                                //邮件方式发送
//                                remindTask.setRemindMethod("1");
//                                //默认设置执行中
//                                remindTask.setStatus(1);
//                                //默认背景颜色
//                                remindTask.setFkRemindEventTypeKey("CUSTOM");
//                                remindTask.setTaskBgColor("#3788d8");
//                                remindTask.setTaskTitle(stringBuilder.toString());
//                                remindTaskDtos.add(remindTask);

                                RemindTaskDto remindTaskDto = new RemindTaskDto();
                                remindTaskDto.setFkTableName(TableEnum.ACT_HI_TASKINST.key);
                                remindTaskDto.setTaskRemark(" ");
                                remindTaskDto.setFkTableId(Long.valueOf(taskId));
                                remindTaskDto.setFkStaffId(id);
                                remindTaskDto.setStartTime(new Date());
                                //邮件方式发送
                                remindTaskDto.setRemindMethod("1");
                                //默认设置执行中
                                remindTaskDto.setStatus(1);
                                //默认背景颜色
                                remindTaskDto.setFkRemindEventTypeKey("WORKFLOW_LEAVE_FORM");
                                remindTaskDto.setTaskBgColor("#3788d8");
                                remindTaskDto.setTaskTitle(stringBuilder.toString());
                                //如果ids等于1 跳转详情页，大于1跳转待签页
//                                if (ids.size() > 1) {
//                                    //待签页
//                                    String businessKey = String.valueOf(fkTableId);
//                                    String url = domainName + "/office_office-form-management_workbreak-detail/"+businessKey
//                                            +"?taskId="+taskId
//                                            +"&procInstId="+procInstId
//                                            +"&procdefKey="+TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key;
//                                    remindTaskDto.setTaskLink(url);
//                                } else if (ids.size() == 1) {
//                                    //待办详情
//                                    String businessKey = String.valueOf(fkTableId);
//                                    String url = domainName + "/office_office-form-management_workbreak-detail/"+businessKey
//                                            +"?taskId="+taskId
//                                            +"&procInstId="+procInstId
//                                            +"&procdefKey="+TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key;
//                                    remindTaskDto.setTaskLink(url);
//                                }
                                remindTaskDtos.add(remindTaskDto);
                            }
                        }
                        try {
                            reminderCenterClient.batchAdd(remindTaskDtos);
                        } catch (Exception e) {
                            e.printStackTrace();
                            log.error("发送提醒信息异常" + e.getMessage());
                        }
                    }
                }
            }
        }
        List<OfficeMediaAndAttachedVo> mediaAndAttachedDtos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key);
            mediaAndAttachedDtos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedDtos;
    }

    /**
     * 新增注解分布式锁，待测试
     *
     * @param companyId
     * @param businessKey
     * @param procdefKey
     */
    @Override
    @RedisLock(value = CacheKeyConstants.LEAVE_FORM_SUBMIT_LOCK_KEY, param = "#businessKey", waitTime = 15L)
    public void startProcess(Long companyId, Long businessKey, String procdefKey) {
        LeaveApplicationForm leaveApplicationForm = leaveApplicationFormMapper.selectById(businessKey);
        //验证能否发起
        validateDelay(leaveApplicationForm);
        if(leaveApplicationForm.getStatus() != 0){
            throw new GetServiceException(LocaleMessageUtils.getMessage("start_process_fail_status"));
        }

        List<LeaveApplicationFormType> leaveApplicationFormTypes = leaveApplicationFormTypeMapper.selectList(Wrappers.<LeaveApplicationFormType>query().lambda());
        Map<Long, String> formTypeMap = leaveApplicationFormTypes.stream().collect(Collectors.toMap(LeaveApplicationFormType::getId, LeaveApplicationFormType::getTypeKey));
        BigDecimal days = leaveApplicationForm.getDays();
        //如果类型为年假
        if (ProjectKeyEnum.ANNUAL_VACATION.key.equals(formTypeMap.get(leaveApplicationForm.getFkLeaveApplicationFormTypeId()))&&leaveApplicationForm.getFkCompanyId()!=1L) {

            //审批中的年假
            List<LeaveApplicationForm> leaveApplicationForms = leaveApplicationFormMapper.selectList(Wrappers.<LeaveApplicationForm>query().lambda()
                    .eq(LeaveApplicationForm::getFkStaffId, leaveApplicationForm.getFkStaffId())
                    .eq(LeaveApplicationForm::getFkLeaveApplicationFormTypeId, leaveApplicationForm.getFkLeaveApplicationFormTypeId())
                    .eq(LeaveApplicationForm::getStatus, 2));
            if (GeneralTool.isNotEmpty(leaveApplicationForms)) {
                for (LeaveApplicationForm applicationForm : leaveApplicationForms) {
                    days = days.add(applicationForm.getDays());
                }
            }
            LeaveApplicationFormDto leaveApplicationFormDto = BeanCopyUtils.objClone(leaveApplicationForm, LeaveApplicationFormDto::new);
            //校验假期库存 撤单是不存在年假不足的情况，则不校验年假
            if (GeneralTool.isEmpty(leaveApplicationFormDto.getFkLeaveApplicationFormIdRevoke())) {
                String messageKey = GeneralTool.isNotEmpty(leaveApplicationForms) ? "annualLeaveBase_not_enough_examine" : "annualLeaveBase_not_enough";
                isStockEnough(days, leaveApplicationFormDto, ProjectKeyEnum.ANNUAL_VACATION.key, messageKey);
//                isStockEnough(days, leaveApplicationFormDto, ProjectKeyEnum.ANNUAL_VACATION.key, "annualLeaveBase_not_enough");
            }
        } else if (ProjectKeyEnum.TAKE_DEFERRED_HOLIDAYS.key.equals(formTypeMap.get(leaveApplicationForm.getFkLeaveApplicationFormTypeId()))&&leaveApplicationForm.getFkCompanyId()!=1L) {
            List<LeaveApplicationForm> leaveApplicationForms = leaveApplicationFormMapper.selectList(Wrappers.<LeaveApplicationForm>query().lambda()
                    .eq(LeaveApplicationForm::getFkStaffId, leaveApplicationForm.getFkStaffId())
                    .eq(LeaveApplicationForm::getFkLeaveApplicationFormTypeId, leaveApplicationForm.getFkLeaveApplicationFormTypeId())
                    .eq(LeaveApplicationForm::getStatus, 2));

            if (GeneralTool.isNotEmpty(leaveApplicationForms)) {
                for (LeaveApplicationForm applicationForm : leaveApplicationForms) {
                    days = days.add(applicationForm.getDays());
                }
            }
            LeaveApplicationFormDto leaveApplicationFormDto = BeanCopyUtils.objClone(leaveApplicationForm, LeaveApplicationFormDto::new);
            String messageKey = GeneralTool.isNotEmpty(leaveApplicationForms) ? "compensatoryLeaveBase_not_enough_examine" : "compensatoryLeaveBase_not_enough";
            isStockEnough(days, leaveApplicationFormDto, ProjectKeyEnum.TAKE_DEFERRED_HOLIDAYS.key, messageKey);
//            isStockEnough(days, leaveApplicationFormDto, ProjectKeyEnum.TAKE_DEFERRED_HOLIDAYS.key, "compensatoryLeaveBase_not_enough");
        } else if (ProjectKeyEnum.DISEASE_VACATION.key.equals(formTypeMap.get(leaveApplicationForm.getFkLeaveApplicationFormTypeId()))&&leaveApplicationForm.getFkCompanyId()!=1L) {
            List<LeaveApplicationForm> leaveApplicationForms = leaveApplicationFormMapper.selectList(Wrappers.<LeaveApplicationForm>query().lambda()
                    .eq(LeaveApplicationForm::getFkStaffId, leaveApplicationForm.getFkStaffId())
                    .eq(LeaveApplicationForm::getFkLeaveApplicationFormTypeId, leaveApplicationForm.getFkLeaveApplicationFormTypeId())
                    .eq(LeaveApplicationForm::getStatus, 2));

            if (GeneralTool.isNotEmpty(leaveApplicationForms)) {
                for (LeaveApplicationForm applicationForm : leaveApplicationForms) {
                    days = days.add(applicationForm.getDays());
                }
            }
            LeaveApplicationFormDto leaveApplicationFormDto = BeanCopyUtils.objClone(leaveApplicationForm, LeaveApplicationFormDto::new);

            //判断是否没上传附件
            isMissMediaAndAttach(businessKey, leaveApplicationForm);
        }


        Map<String, Object> map = new HashMap<>();
        Long staffId = GetAuthInfo.getStaffId();
        List<Long> staffIdList = new ArrayList<>();
        //获取部门最高职位员工ids
        Result<List<Long>> result = permissionCenterClient.getTopPositionStaffIds(SecureUtil.getFkCompanyId(), SecureUtil.getFkDepartmentId());
        if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
            staffIdList = result.getData();
        }
        Result<String> resultNum = permissionCenterClient.getDepartmentNumById(SecureUtil.getFkDepartmentId());
        if (resultNum.isSuccess()) {
            if (ProjectKeyEnum.IT_DEPARTMENT.key.equals(resultNum.getData()) || ProjectKeyEnum.MARKETING_DEPARTMENT_SUPPORT.key.equals(resultNum.getData())) {
                map.put("isStaff", 1);
            } else if (staffIdList.contains(staffId)) {
                map.put("isStaff", 0);
            } else {
                map.put("isStaff", 1);
            }

                //iae的业务部门
                List<String> numList = Arrays.stream(ProjectKeyEnum.IAE_BUSINESS_DEPARTMENT).map(e -> e.key).collect(Collectors.toList());
                if (numList.contains(resultNum.getData())){
                    map.put("isIaeBusinessDepartment", 1);
                }else {
                    map.put("isIaeBusinessDepartment", 0);
                }
                if (ProjectKeyEnum.IAE_ID11.key.equals(resultNum.getData())){
                    //如果是对外发展部门
                    map.put("isID11", 1);
                }else {
                    map.put("isID11", 0);
                }

            if (SecureUtil.getFkCompanyId().equals(30L)){
                map.put("isID11", 0);
            }
        }

        //如果有直属上司就走直属上司的那一条线路
        Result<Long> staffSupervisorIdResult = permissionCenterClient.getStaffSupervisorIdByStaffId(staffId);
        log.info("直属上司详情1：{}", JSON.toJSONString(staffSupervisorIdResult));
        if (staffSupervisorIdResult.isSuccess()) {
            Long staffSupervisorId = staffSupervisorIdResult.getData();
            if (GeneralTool.isEmpty(staffSupervisorId)) {
                map.put("isSupervisor", "0");
            } else {
                map.put("isSupervisor", "1");
            }
            if (staffSupervisorId == null || staffSupervisorId == 602 || staffSupervisorId == 603) {
                map.put("isID12", "1");
            }else{
                Result<Long>  staffSupervisorIdResultInfo= permissionCenterClient.getStaffSupervisorIdByStaffId(staffSupervisorId);
                log.info("直属上司详情2：{}", JSON.toJSONString(staffSupervisorIdResultInfo));
                if (staffSupervisorIdResultInfo.isSuccess() && staffSupervisorIdResultInfo.getData() != null) {
                    Long staffSupervisorIdInfo = staffSupervisorIdResultInfo.getData();
                    //如果请假天数等于或超过3天并且直属上司为黄总或Lydia则直接到人事经理审批
                    if (days != null && days.compareTo(new BigDecimal(24)) >= 0) {
                        if (staffSupervisorIdInfo == 602 || staffSupervisorIdInfo == 603) {
                            map.put("isID12", "1");
                        } else {
                            map.put("isID12", "0");
                        }
                    }
                }else{
                    map.put("isID12", "0");
                }
            }
        }
        LeaveApplicationFormVo leaveApplicationFormTypeDto = leaveApplicationFormTypeMapper.getLeaveApplicationFormTypeByKey(ProjectKeyEnum.HOME_OFFICE.key);
        if (GeneralTool.isNotEmpty(leaveApplicationFormTypeDto)) {
            if (leaveApplicationFormTypeDto.getId().equals(leaveApplicationForm.getFkLeaveApplicationFormTypeId())) {
                map.put("isHomeOffice", 1);
            } else {
                map.put("isHomeOffice", 0);
            }
        } else {
            map.put("isHomeOffice", 0);
        }

        //天数的分支参数
//        if (SecureUtil.getFkCompanyId().equals(30L)){
        //非3的才不走人事行政经理
        if (!SecureUtil.getFkCompanyId().equals(3L)){
         //HTISYD不走人事行政经理
            map.put("days", 16);
        }else {
            map.put("days", leaveApplicationForm.getDays());
        }
        //调整申请处理人参数
        map.put("applyUser", staffId);
        leaveApplicationForm.setStatus(2);
        utilService.updateUserInfoToEntity(leaveApplicationForm);
        leaveApplicationFormMapper.updateById(leaveApplicationForm);

        List<Long> leaveApplicationFormIds = new ArrayList<>(1);
        leaveApplicationFormIds.add(businessKey);

        Map<String, List<Long>> leaveApplicationFormIdsMap = new HashMap<>();
        leaveApplicationFormIdsMap.put(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key, leaveApplicationFormIds);
        //根据业务id查找正在执行的任务id
        Result<Map<Long, ActRuTaskVo>> actRuTaskDtoMapResult = workflowCenterClient.getActRuTaskDtosByBusinessKey(leaveApplicationFormIdsMap);
        Map<Long, ActRuTaskVo> actRuTaskDtoMap = actRuTaskDtoMapResult.getData();
        if (GeneralTool.isEmpty(actRuTaskDtoMap)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("start_process_fail_status"));
        }

        Result<Boolean> result_ = workflowCenterClient.startProcess(String.valueOf(businessKey), procdefKey, String.valueOf(companyId), map);
        if (!result_.isSuccess() || (result_.isSuccess() && !result_.getData())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("start_process_fail_status"));
        }

        List<Long> staffIdsByResourceKey = permissionCenterClient.getStaffIdsByResourceKey("officeLeaveApplicationForm.AllForms", true);
        if (GeneralTool.isNotEmpty(staffIdsByResourceKey)){
            for (Long aLong : staffIdsByResourceKey) {
                CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(aLong), cache_key);
            }
        }

        try {
            Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
            //异步的方式发送提醒至行政专员
            asyncReminderService.doSendApplicationRemind(headerMap,leaveApplicationForm,"已发起");
        }catch (Exception e) {
            log.error("-------------提醒发送异常----------",e);
        }
    }

    /**
     * 验证延迟时间
     * @param leaveApplicationForm
     */
    private void validateDelay(LeaveApplicationForm leaveApplicationForm) {
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.LEAVE_APPLICATION_FORM_SUBMIT_LIMIT.key, 1).getData();
        String configValue1 = companyConfigMap.get(SecureUtil.getFkCompanyId());
        List<Double> configList = new ArrayList<>(JSON.parseArray(configValue1, Double.class));
        //        ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.LEAVE_APPLICATION_FORM_SUBMIT_LIMIT.key).getData();
//        String configJson = configDto.getValue1();
//        JSONObject configJsonObject = JSON.parseObject(configJson);
//        JSONArray configArray = null;
//        if (GeneralTool.isEmpty(SecureUtil.getFkCompanyId())||(!SecureUtil.getFkCompanyId().equals(2L)&&!SecureUtil.getFkCompanyId().equals(3L))){
//            configArray = configJsonObject.getJSONArray("OTHER");
//        }
//        if(SecureUtil.getFkCompanyId().equals(2L)){
//            configArray = configJsonObject.getJSONArray("GEA");
//        }else if (SecureUtil.getFkCompanyId().equals(3L)){
//            configArray = configJsonObject.getJSONArray("IAE");
//        }
//        List<Double> configList = configArray.toJavaList(Double.class);
        //发起人
        Double delayHours = configList.get(0);

        if (delayHours == -1){
            return;
        }

        Date applyDate = new Date();
//        Date advanceDateByHour = GetDateUtil.getAdvanceDateByHour(applyDate, -1L * delayHours);
        Date advanceDateByHour = GetDateUtil.getDateAfterMs(applyDate, (long)(delayHours * GetDateUtil.ONE_HOUR_MILLISECOND));

        if (advanceDateByHour.compareTo(leaveApplicationForm.getStartTime())<0){
            return;
        }

        if (GeneralTool.isEmpty(SecureUtil.getStaffId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("not_logged_in"));

        }
        StaffVo staffVo = permissionCenterClient.getStaffById(SecureUtil.getStaffId()).getData();
        String loginId = staffVo.getLoginId();

        LambdaQueryWrapper<OfficeMediaAndAttached> wrapper = Wrappers.lambdaQuery(OfficeMediaAndAttached.class);
        wrapper.eq(OfficeMediaAndAttached::getFkTableName,TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)
                .eq(OfficeMediaAndAttached::getFkTableId,leaveApplicationForm.getId())
                .eq(OfficeMediaAndAttached::getTypeKey, FileTypeEnum.OFFICE_LEAVE_APPLICATION_FORM_FILE.key)
                .eq(OfficeMediaAndAttached::getGmtCreateUser,loginId);
        List<OfficeMediaAndAttached> officeMediaAndAttacheds = mediaAndAttachedService.list(wrapper);
//      如果是撤单申请则不需要校验附件
        if (GeneralTool.isNotEmpty(leaveApplicationForm.getFkLeaveApplicationFormIdRevoke())) {
            return;
        }
        if (GeneralTool.isEmpty(officeMediaAndAttacheds)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("startup_exception"));
        }

    }

    private void isMissMediaAndAttach(Long businessKey, LeaveApplicationForm leaveApplicationForm) {
//        ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.LEAVE_APPLICATION_FORM_SUBMIT_LIMIT.key).getData();
//        String configJson = configDto.getValue1();
//        JSONObject configJsonObject = JSON.parseObject(configJson);
//        JSONArray configArray = null;
//        if (GeneralTool.isEmpty(SecureUtil.getFkCompanyId())||(!SecureUtil.getFkCompanyId().equals(2L)&&!SecureUtil.getFkCompanyId().equals(3L))){
//            configArray = configJsonObject.getJSONArray("OTHER");
//        }
//        if(SecureUtil.getFkCompanyId().equals(2L)){
//            configArray = configJsonObject.getJSONArray("GEA");
//        }else if (SecureUtil.getFkCompanyId().equals(3L)){
//            configArray = configJsonObject.getJSONArray("IAE");
//        }
//        List<Integer> configList = configArray.toJavaList(Integer.class);
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.LEAVE_APPLICATION_FORM_SUBMIT_LIMIT.key, 1).getData();
        String configValue1 = companyConfigMap.get(SecureUtil.getFkCompanyId());
        List<Integer> configList = new ArrayList<>(JSON.parseArray(configValue1, Integer.class));
        if (configList.get(0)==-1){
            return;
        }

        //判断时间
        Date startTime = leaveApplicationForm.getStartTime();
        Date formCreateTime = leaveApplicationForm.getGmtCreate();
        if (startTime.before(formCreateTime)){
            //需要判断上传附件
            List<OfficeMediaAndAttached> officeMediaAndAttacheds = mediaAndAttachedService.list(Wrappers.<OfficeMediaAndAttached>lambdaQuery()
                    .eq(OfficeMediaAndAttached::getFkTableName, TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)
                    .eq(OfficeMediaAndAttached::getFkTableId, businessKey));
            if (GeneralTool.isEmpty(officeMediaAndAttacheds)){
                throw new GetServiceException(LocaleMessageUtils.getMessage("leave_form_miss_mediaAndAttached"));
            }
        }
    }

    @Override
    public void updateStatus(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        LeaveApplicationForm leaveApplicationForm = leaveApplicationFormMapper.selectById(id);
        if (GeneralTool.isEmpty(leaveApplicationForm)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }

        if (GeneralTool.isNotEmpty(leaveApplicationForm.getFkLeaveApplicationFormIdRevoke())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("leave_form_cannot_be_void"));
        }

        if (0 != leaveApplicationForm.getStatus()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("process_already_start_cannot_Invalid"));
        }

        //作废同时去掉提醒任务
//        workflowCenterClient.getActRuTaskDtosByBusinessKey()
        Result<List<ActHiTaskInstVo>> result = workflowCenterClient.getActHiTaskInstDtosByBusinessKey(String.valueOf(id), TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key);
        if (result.isSuccess()&&GeneralTool.isNotEmpty(result.getData())){
            List<ActHiTaskInstVo> actHiTaskInstVos = result.getData();
            Set<Long> tableIds = actHiTaskInstVos.stream()
                    .map(a->Long.valueOf(a.getId()))
                    .collect(Collectors.toSet());

            //删除提醒任务
            Result<Boolean> booleanResult = reminderCenterClient.batchUpdateByTableIds(new ArrayList<>(), TableEnum.ACT_HI_TASKINST.key, tableIds);
            if (!booleanResult.isSuccess()&&GeneralTool.isEmpty(booleanResult.getData())){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }

        }
        //如果审批中-》作废
        if (2==leaveApplicationForm.getStatus()){
            List<Long> staffIdsByResourceKey = permissionCenterClient.getStaffIdsByResourceKey("officeLeaveApplicationForm.AllForms", true);
            if (GeneralTool.isNotEmpty(staffIdsByResourceKey)){
                for (Long aLong : staffIdsByResourceKey) {
                    CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(aLong), cache_key);
                }
            }
        }

        leaveApplicationForm.setStatus(5);
        leaveApplicationFormMapper.updateById(leaveApplicationForm);
        if (GeneralTool.isNotEmpty(leaveApplicationForm.getFkStaffId())){
            //更新缓存
            CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(leaveApplicationForm.getFkStaffId()), cache_key);
        }

    }

    /**
     * 根据工休单id获取假期类型
     *
     * @param id
     * @return
     */
    @Override
    public String getLeaveTypeNameById(Long id) {
        LeaveApplicationForm leaveApplicationForm = leaveApplicationFormMapper.selectById(id);
        Long fkLeaveApplicationFormTypeId = leaveApplicationForm.getFkLeaveApplicationFormTypeId();
//        leaveApplicationFormTypeService.getLeaveApplicationFormTypeNameById(fkLeaveApplicationFormTypeId);
        return leaveApplicationFormTypeService.getLeaveApplicationFormTypeNameById(fkLeaveApplicationFormTypeId);
    }

    @Override
    public String getLeaveTypeKeyById(Long id) {
        LeaveApplicationForm leaveApplicationForm = leaveApplicationFormMapper.selectById(id);
        Long fkLeaveApplicationFormTypeId = leaveApplicationForm.getFkLeaveApplicationFormTypeId();
        return leaveApplicationFormTypeService.getLeaveApplicationFormTypeKeyById(fkLeaveApplicationFormTypeId);
    }

    /**
     * 根据id获得工休单详情
     *
     * @param id
     * @return
     */
    @Override
    public LeaveApplicationForm getLeaveApplicationForm(Long id) {
        return leaveApplicationFormMapper.selectById(id);
    }

    /**
     * 根据被撤单id回显撤销单的内容
     *
     * @param id
     * @return
     */
    @Override
    public LeaveApplicationFormVo revokeLeaveApplicationForm(Long id) {
        LeaveApplicationForm leaveApplicationForm = getLeaveApplicationForm(id);
        //判断有无子单和子单状态
        List<LeaveApplicationForm> leaveApplicationForms = getChildLeaveApplicationForm(id);
        if (ProjectExtraEnum.APPROVAL_FINISHED.key.equals(leaveApplicationForm.getStatus())&&leaveApplicationForm.getEndTime().compareTo(new Date())<0){
            throw new GetServiceException(LocaleMessageUtils.getMessage("leave_form_overtime_no_revoke"));
        }
        if (GeneralTool.isNotEmpty(leaveApplicationForms)) {
            //有撤销单，按创建时间拿最新一条
            LeaveApplicationForm chilLeaveApplicationForm = leaveApplicationForms.get(0);
            if (chilLeaveApplicationForm.getStatus().equals(ProjectExtraEnum.APPROVAL_FINISHED.key)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("leaveApplicationForm_is_revoke"));
            }
            if (chilLeaveApplicationForm.getStatus().equals(ProjectExtraEnum.TO_BE_INITIATED.key)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("childLeaveApplicationForm_to_be_initiated"));
            }
            if (chilLeaveApplicationForm.getStatus().equals(ProjectExtraEnum.APPROVAL_IN_PROGRESS.key)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("childLeaveApplicationForm_is_approval_in_progress"));
            }
        }
        //将被撤销单的请假时间设置为负数
        //BigDecimal subtractResult = new BigDecimal(0).subtract(leaveApplicationForm.getDays());
        leaveApplicationForm.setDays(leaveApplicationForm.getDays().negate());
        //设置父表单id
        leaveApplicationForm.setFkLeaveApplicationFormIdRevoke(id);
        leaveApplicationForm.setId(null);
        //设置为待发起
        leaveApplicationForm.setStatus(null);
        leaveApplicationForm.setNum(null);
        leaveApplicationForm.setGmtModified(null);
        leaveApplicationForm.setGmtModifiedUser(null);
        leaveApplicationForm.setGmtCreate(null);
        leaveApplicationForm.setGmtCreateUser(null);
        leaveApplicationForm.setReason(null);
        LeaveApplicationFormVo leaveApplicationFormVo = BeanCopyUtils.objClone(leaveApplicationForm, LeaveApplicationFormVo::new);
//        StaffVo staffVo = permissionCenterClient.getCompanyIdByStaffId(leaveApplicationForm.getFkStaffId());
        Result<StaffVo> result = permissionCenterClient.getCompanyIdByStaffId(leaveApplicationForm.getFkStaffId());
        StaffVo staffVo = new StaffVo();
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            staffVo = result.getData();
        }

        //设置年假剩余天数，补休剩余天数
        if (GeneralTool.isEmpty(staffVo.getAnnualLeaveBase())) {
            leaveApplicationFormVo.setAnnualLeaveDays(new BigDecimal(0));
        }
        if (GeneralTool.isEmpty(staffVo.getCompensatoryLeaveBase())) {
            leaveApplicationFormVo.setCompensatoryLeaveDays(new BigDecimal(0));
        }
        return leaveApplicationFormVo;
    }

    /**
     * 根据id查找子单（撤销单）
     *
     * @param id
     * @return
     */
    private List<LeaveApplicationForm> getChildLeaveApplicationForm(Long id) {
//        Example example = new Example(LeaveApplicationForm.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkLeaveApplicationFormIdRevoke", id);
//        //按创建时间排序
//        example.orderBy("gmtCreate").desc();
//        List<LeaveApplicationForm> leaveApplicationForms = leaveApplicationFormMapper.selectByExample(example);
        List<LeaveApplicationForm> leaveApplicationForms = leaveApplicationFormMapper.selectList(Wrappers.<LeaveApplicationForm>query().lambda()
                .eq(LeaveApplicationForm::getFkLeaveApplicationFormIdRevoke, id)
                .orderByDesc(LeaveApplicationForm::getGmtCreate));
        return leaveApplicationForms;
    }

//    /**
//     * @return java.util.List<java.lang.Long>
//     * @Description :获取登录人对应所有公司id集合
//     * @Param []
//     * <AUTHOR>
//     */
//    private List<Long> getCompanyIds() {
//        List<Long> companyIds = StaffContext.getStaff().getCompanyIds();
//        if (GeneralTool.isEmpty(companyIds)) {
//            companyIds.add(0L);
//        }
//        return companyIds;
//    }

//    /**
//     * 验证年假剩余天数、补休天数
//     *
//     * @param leaveApplicationFormVo
//     */
//    private void validateLeaveType(LeaveApplicationFormDto leaveApplicationFormVo) {
//        Result<StaffVo> result = permissionCenterClient.getCompanyIdByStaffId(GetAuthInfo.getStaffId());
//        StaffVo staffDto = new StaffVo();
//        if(result.isSuccess() && GeneralTool.isNotEmpty(result.getData()))
//        {
//            staffDto = result.getData();
//        }
//        String leaveApplicationFormType = leaveApplicationFormTypeService.getLeaveApplicationFormTypeNameById(leaveApplicationFormVo.getFkLeaveApplicationFormTypeId());
//
//        if (ANNUAL_LEAVE.equals(leaveApplicationFormType)) {
//            if (staffDto.getAnnualLeaveBase() == null) {
//                staffDto.setAnnualLeaveBase(new BigDecimal(0));
//            }
//            BigDecimal days = leaveApplicationFormVo.getDays().divide(new BigDecimal(8), 3, BigDecimal.ROUND_HALF_UP);
//            BigDecimal annualLeaveBase = staffDto.getAnnualLeaveBase().subtract(days);
//            if (annualLeaveBase.compareTo(new BigDecimal(0)) < 0) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("annualLeaveBase_not_enough"));
//            }
//        }
//        if (COMPENSATORY_LEAVE.equals(leaveApplicationFormType)) {
//            if (staffDto.getCompensatoryLeaveBase() == null) {
//                staffDto.setCompensatoryLeaveBase(new BigDecimal(0));
//            }
//            BigDecimal days = leaveApplicationFormVo.getDays().divide(new BigDecimal(8), 3, BigDecimal.ROUND_HALF_UP);
//            BigDecimal compensatoryLeaveBase = staffDto.getCompensatoryLeaveBase().subtract(days);
//            if (compensatoryLeaveBase.compareTo(new BigDecimal(0)) < 0) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("compensatoryLeaveBase_not_enough"));
//            }
//        }
//    }


    /**
     * 验证年假剩余天数、补休天数
     *
     * @param leaveApplicationFormDto
     */
    private void validateLeaveType(LeaveApplicationFormDto leaveApplicationFormDto) {
        //如果是撤单是不存在年假不足的情况，则不校验年假
        if (GeneralTool.isNotEmpty(leaveApplicationFormDto.getFkLeaveApplicationFormIdRevoke())) {
            return;
        }
        List<LeaveApplicationFormType> leaveApplicationFormTypes = leaveApplicationFormTypeMapper.selectList(Wrappers.<LeaveApplicationFormType>query().lambda());
        Map<Long, String> formTypeMap = leaveApplicationFormTypes.stream().collect(Collectors.toMap(LeaveApplicationFormType::getId, LeaveApplicationFormType::getTypeKey));
        BigDecimal days = leaveApplicationFormDto.getDays();
        //如果类型为年假
        if (ProjectKeyEnum.ANNUAL_VACATION.key.equals(formTypeMap.get(leaveApplicationFormDto.getFkLeaveApplicationFormTypeId()))&& leaveApplicationFormDto.getFkCompanyId()!=1L) {
            isStockEnough(days, leaveApplicationFormDto, ProjectKeyEnum.ANNUAL_VACATION.key, "annualLeaveBase_not_enough");
        } else if (ProjectKeyEnum.TAKE_DEFERRED_HOLIDAYS.key.equals(formTypeMap.get(leaveApplicationFormDto.getFkLeaveApplicationFormTypeId()))&& leaveApplicationFormDto.getFkCompanyId()!=1L) {
            isStockEnough(days, leaveApplicationFormDto, ProjectKeyEnum.TAKE_DEFERRED_HOLIDAYS.key, "compensatoryLeaveBase_not_enough");
        }
//        else if (ProjectKeyEnum.DISEASE_VACATION.key.equals(formTypeMap.get(leaveApplicationFormDto.getFkLeaveApplicationFormTypeId()))&& leaveApplicationFormDto.getFkCompanyId()!=1L
//                && !"GEA".equals(interfaceConfiguration) && !"HTI".equals(interfaceConfiguration)) {
//            //gea（前公司）的申请不考虑病假库存、iae也不考虑库存
//            isStockEnough(days, leaveApplicationFormDto, ProjectKeyEnum.DISEASE_VACATION.key, "disease_vacation_not_enough");
//        }
    }

//    private void isStockEnough(BigDecimal days,LeaveApplicationFormDto leaveApplicationFormVo,String leaveTypeKey,String msg) throws YServiceException {
//        LeaveStockDto leaveStockVo = new LeaveStockDto();
//        leaveStockVo.setFkCompanyId(leaveApplicationFormVo.getFkCompanyId());
//        List<Long> staffIds = new ArrayList<>(1);
//        staffIds.add(leaveApplicationFormVo.getFkStaffId());
//        leaveStockVo.setFkStaffIds(staffIds);
//        leaveStockVo.setFkStaffId(leaveApplicationFormVo.getFkStaffId());
//        leaveStockVo.setLeaveTypeKey(leaveTypeKey);
//        List<LeaveStockVo> leaveStockDtos = leaveStockService.getLeaveStockDtos(leaveStockVo);
//        if (CheckUtils.isEmpty(leaveStockDtos)){
//            throw new YServiceException(LocaleMessageUtils.getMessage(msg));
//        }
//        List<LeaveStockVo> stockDtos = leaveStockDtos.stream().filter(leaveStockDto -> leaveStockDto.getEffectiveDeadline() != null
//                &&leaveStockDto.getEffectiveDeadline().compareTo(leaveApplicationFormVo.getStartTime())>=0
//                &&leaveStockDto.getEffectiveDeadline().compareTo(leaveApplicationFormVo.getEndTime())>=0).collect(Collectors.toList());
//        List<LeaveStockVo> longTimeleaveStockDtos = leaveStockDtos.stream().filter(leaveStockDto -> leaveStockDto.getEffectiveDeadline() == null ).collect(Collectors.toList());
//        if (CheckUtils.isEmpty(stockDtos)){
//            stockDtos = new ArrayList<>();
//        }
//        stockDtos.addAll(longTimeleaveStockDtos);
//        if (CheckUtils.isEmpty(stockDtos)){
//            throw new YServiceException(LocaleMessageUtils.getMessage(msg));
//        }
//        for (LeaveStockVo leaveStockDto : stockDtos) {
//            System.out.println("leaveStockDto.getLeaveStock().compareTo(days) = " + leaveStockDto.getLeaveStock().compareTo(days));
//            if (leaveStockDto.getLeaveStock().compareTo(days) >= 0){
//                return;
//            }else {
//                days = days.subtract(leaveStockDto.getLeaveStock());
//            }
//        }
//        if (days.compareTo(BigDecimal.ZERO)>0){
//            throw new YServiceException(LocaleMessageUtils.getMessage(msg));
//        }
//    }

    @Override
    public Map<Long, String> getLeaveApplicationFormByFkStaffIds(Set<Long> fkStaffIds,
                                                                 Date startTime,
                                                                 Date endTime,
                                                                 Long fkCompanyId,
                                                                 Map<Long, Long> departmentIdMap,
                                                                 List<WorkScheduleTimeConfig> workScheduleTimeConfigList,
                                                                 Map<Long,List<WorkScheduleStaffConfig>> workScheduleStaffConfigListMap,
                                                                 List<WorkScheduleDateConfig> workScheduleDateConfigList) {
        List<LeaveApplicationFormVo> list = leaveApplicationFormMapper.getLeaveApplicationFormByFkStaffIds(fkStaffIds,fkCompanyId,startTime,endTime);
        Map<Long, String> map = new HashMap();
        if (GeneralTool.isEmpty(list)) {
            return map;
        }
        List<LeaveApplicationFormVo> dataList = processing(list,startTime,endTime,fkCompanyId,departmentIdMap,workScheduleTimeConfigList,workScheduleStaffConfigListMap,workScheduleDateConfigList);
        for (LeaveApplicationFormVo data : dataList) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            StringBuffer stringBuffer = new StringBuffer();
            stringBuffer.append(sdf.format(data.getStartTime()))
                    .append("至")
                    .append(sdf.format(data.getEndTime()))
                    .append(",")
                    .append(data.getLeaveApplicationFormTypeName())
                    .append(data.getDays())
                    .append("小时；");
            if (map.get(data.getFkStaffId()) != null) {
                String value = map.get(data.getFkStaffId());
                stringBuffer.insert(0, value);
                map.put(data.getFkStaffId(), stringBuffer.toString());
            } else {
                map.put(data.getFkStaffId(), stringBuffer.toString());
            }
        }
        return map;
    }

    /**
     * 公休申请范围超过考勤范围情况处理
     * <AUTHOR>
     * @DateTime 2023/2/9 15:56
     */
    public List<LeaveApplicationFormVo> processing(List<LeaveApplicationFormVo> dataList,
                                                   Date startTime ,
                                                   Date endTime,
                                                   Long fkCompanyId,
                                                   Map<Long, Long> departmentIdMap,
                                                   List<WorkScheduleTimeConfig> workScheduleTimeConfigList,
                                                   Map<Long,List<WorkScheduleStaffConfig>> workScheduleStaffConfigListMap,
                                                   List<WorkScheduleDateConfig> workScheduleDateConfigList
                                                    ) {
        for (LeaveApplicationFormVo leaveApplicationFormVo : dataList) {
            //类型为加班,零点后加班算到新的一天
            if (leaveApplicationFormVo.getFkLeaveApplicationFormTypeId().equals(12)) {
                //加班类型申请开始时间小于考勤开始时间
                Date overStartTime = GetDateUtil.setTime(startTime, 0, 0, 0);
                if (leaveApplicationFormVo.getStartTime().compareTo(startTime) < 0 && leaveApplicationFormVo.getEndTime().compareTo(overStartTime) > 0) {
                    //重新设置申请开始时间
                    Date applicationStartTime = GetDateUtil.setTime(startTime, 0, 0, 0);
                    leaveApplicationFormVo.setStartTime(applicationStartTime);
                    //重新设置申请时长
                    leaveApplicationFormVo.setDays(new BigDecimal(DateUtil.between(applicationStartTime, leaveApplicationFormVo.getEndTime(), DateUnit.HOUR)));
                    continue;
                }
                //加班类型申请结束时间大于考勤结束时间
                Date overEndTime = GetDateUtil.setTime(endTime, 23, 59, 59);
                if (leaveApplicationFormVo.getStartTime().compareTo(overEndTime) <= 0 && leaveApplicationFormVo.getEndTime().compareTo(overEndTime) > 0) {
                    //重新设置申请结束时间
                    Date applicationEndTime = GetDateUtil.setTime(endTime, 23, 59, 59);
                    leaveApplicationFormVo.setEndTime(applicationEndTime);
                    //重新设置申请时长
                    leaveApplicationFormVo.setDays(new BigDecimal(DateUtil.between(leaveApplicationFormVo.getStartTime(), applicationEndTime, DateUnit.HOUR)));
                    continue;
                }
            }

            WorkScheduleTimeConfig timeConfig = AttendanceUtils.getTimeConfig(workScheduleTimeConfigList, fkCompanyId, departmentIdMap.get(leaveApplicationFormVo.getFkStaffId()));
            //获取特殊人员时间配置
            List<WorkScheduleStaffConfig> workScheduleStaffConfigList = workScheduleStaffConfigListMap.get(leaveApplicationFormVo.getFkStaffId());
            //工休申请开始时间小于考勤范围开始时间
            if (leaveApplicationFormVo.getStartTime().compareTo(startTime) < 0 && leaveApplicationFormVo.getEndTime().compareTo(startTime) >= 0) {
                WorkScheduleStaffConfig staffConfig = AttendanceUtils.getWorkScheduleStaffConfig(startTime, null, workScheduleStaffConfigList);
                //重新设置申请开始时间
                List<Integer> workingStart = AttendanceUtils.getWorkScheduleTimeConfig(timeConfig, staffConfig, 1);
                leaveApplicationFormVo.setStartTime(GetDateUtil.setTime(startTime, workingStart.get(0), workingStart.get(1), workingStart.get(2)));
            }

            //工休申请结束时间大于考勤结束时间
            if (leaveApplicationFormVo.getStartTime().compareTo(endTime) < 0 && leaveApplicationFormVo.getEndTime().compareTo(endTime) > 0) {
                Date newEndTime = GetDateUtil.getYesterdayDate(endTime);
                WorkScheduleStaffConfig staffConfig = AttendanceUtils.getWorkScheduleStaffConfig(newEndTime, null, workScheduleStaffConfigList);

                //重新设定工休申请结束时间
                List<Integer> workingEnd = AttendanceUtils.getWorkScheduleTimeConfig(timeConfig, staffConfig, 2);
                leaveApplicationFormVo.setEndTime(GetDateUtil.setTime(newEndTime, workingEnd.get(0), workingEnd.get(1), workingEnd.get(2)));
            }
            //重新计算工休申请时长
            BigDecimal day = AttendanceUtils.getDuration(workScheduleTimeConfigList, workScheduleStaffConfigList, workScheduleDateConfigList,
                    fkCompanyId, departmentIdMap.get(leaveApplicationFormVo.getFkStaffId()), null,
                    leaveApplicationFormVo.getStartTime(), leaveApplicationFormVo.getEndTime(), 2);
            leaveApplicationFormVo.setDays(day);
        }

        return dataList;
    }

    /**
     * 校验假期库存
     * @param days
     * @param leaveApplicationFormDto
     * @param leaveTypeKey
     * @param msg
     */
    private void isStockEnough(BigDecimal days, LeaveApplicationFormDto leaveApplicationFormDto, String leaveTypeKey, String msg) {
        LeaveStockDto leaveStockDto = new LeaveStockDto();
        leaveStockDto.setFkCompanyId(leaveApplicationFormDto.getFkCompanyId());
        List<Long> staffIds = new ArrayList<>(1);
        staffIds.add(leaveApplicationFormDto.getFkStaffId());
        leaveStockDto.setFkStaffIds(staffIds);
        leaveStockDto.setFkStaffId(leaveApplicationFormDto.getFkStaffId());
        leaveStockDto.setLeaveTypeKey(leaveTypeKey);
        List<LeaveStockVo> leaveStockVos = leaveStockService.getLeaveStockDtos(leaveStockDto);
        if (GeneralTool.isEmpty(leaveStockVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(msg));
        }
        List<LeaveStockVo> stockDtos = leaveStockVos.stream().filter(leaveStockVo -> leaveStockVo.getEffectiveDeadline() != null
                && leaveStockVo.getEffectiveDeadline().compareTo(leaveApplicationFormDto.getStartTime()) >= 0
                && leaveStockVo.getEffectiveDeadline().compareTo(leaveApplicationFormDto.getEndTime()) >= 0).collect(Collectors.toList());
        List<LeaveStockVo> longTimeleaveStockVos = leaveStockVos.stream().filter(leaveStockVo -> leaveStockVo.getEffectiveDeadline() == null).collect(Collectors.toList());
        if (GeneralTool.isEmpty(stockDtos)) {
            stockDtos = new ArrayList<>();
        }
        stockDtos.addAll(longTimeleaveStockVos);
        if (GeneralTool.isEmpty(stockDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(msg));
        }
        for (LeaveStockVo leaveStockVo : stockDtos) {
            System.out.println("leaveStockVo.getLeaveStock().compareTo(days) = " + leaveStockVo.getLeaveStock().compareTo(days));
            if (leaveStockVo.getLeaveStock().compareTo(days) >= 0) {
                return;
            } else {
                days = days.subtract(leaveStockVo.getLeaveStock());
            }
        }
        if (days.compareTo(BigDecimal.ZERO) > 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(msg));
        }
    }

    @Override
    public List<LeaveApplicationFormVo> getLeaveApplicationFormList(Set<Long> fkStaffIds, Date startTime, Date endTime, Long fkCompanyId){
         List<LeaveApplicationFormVo> list = leaveApplicationFormMapper.getLeaveApplicationFormList(fkStaffIds, startTime,endTime,fkCompanyId);
         return list;
    }

    @Override
    public List<ReminderTaskCountVo> getApplicationAndApprovalCount(Long staffId) {
        List<ReminderTaskCountVo> reminderTaskCountVos = new ArrayList<>();
        List<Integer> statusList = new ArrayList<>();
        statusList.add(ProjectExtraEnum.APPROVAL_IN_PROGRESS.key);
//        Map<Long, Integer> map = new HashMap<>();
//        Result<Map<Long, Integer>> fromIdsResult = workflowCenterClient.getFromIdsByStaffId(GetAuthInfo.getStaffId(), TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key);
//        if (fromIdsResult.isSuccess() && GeneralTool.isNotEmpty(fromIdsResult.getData())) {
//            map = fromIdsResult.getData();
//        }
//        List<Long> fromIds = new ArrayList<>(map.keySet());
//        if (GeneralTool.isEmpty(fromIds)) {
//            fromIds.add(0L);
//        }
        Set<Long> fromIds = new HashSet<>();
        //查出代办的
        Set<String> businessKeysToDo = workflowCenterClient.getToDoByStaffIdAndTableName(staffId, TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key).getData();
        if (GeneralTool.isEmpty(businessKeysToDo)){
            businessKeysToDo.add("0");
        }
        Set<Long> formIdsToDo = businessKeysToDo.stream().map(Long::valueOf).collect(Collectors.toSet());

        //查出待签的
        Set<String> businessKeysToSign = workflowCenterClient.getToSignByStaffIdAndTableName(staffId, TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key).getData();
        if (GeneralTool.isEmpty(businessKeysToSign)){
            businessKeysToSign.add("0");
        }
        Set<Long> formIdsToSign = businessKeysToSign.stream().map(Long::valueOf).collect(Collectors.toSet());
        fromIds.addAll(formIdsToDo);
        fromIds.addAll(formIdsToSign);

        List<LeaveApplicationForm> myApprovalForm = leaveApplicationFormMapper.selectList(Wrappers.<LeaveApplicationForm>lambdaQuery()
                .in(LeaveApplicationForm::getId, fromIds)
                .in(LeaveApplicationForm::getStatus, statusList));


        ReminderTaskCountVo officeCenterApprovalTaskCountDto = new ReminderTaskCountVo();
        officeCenterApprovalTaskCountDto.setKey(ProjectKeyEnum.OFFICE_CENTER_APPROVAL.key);
        officeCenterApprovalTaskCountDto.setCount(myApprovalForm.size());
        reminderTaskCountVos.add(officeCenterApprovalTaskCountDto);


//        statusList.clear();
//        statusList.add(ProjectExtraEnum.TO_BE_INITIATED.key);
//        statusList.add(ProjectExtraEnum.APPROVAL_REJECT.key);
//        //        statusList.add(ProjectExtraEnum.APPROVAL_REJECT.key);
//        //        statusList.add(ProjectExtraEnum.APPROVAL_IN_PROGRESS.key);
//        List<LeaveApplicationForm> myApplicationForm = leaveApplicationFormMapper.selectList(Wrappers.<LeaveApplicationForm>lambdaQuery()
//                .eq(LeaveApplicationForm::getFkStaffId, staffId)
//                .in(LeaveApplicationForm::getStatus, statusList));
//
        ReminderTaskCountVo officeCenterApplicationTaskCountDto = new ReminderTaskCountVo();
        officeCenterApplicationTaskCountDto.setKey(ProjectKeyEnum.OFFICE_CENTER_APPLICATION.key);
        officeCenterApplicationTaskCountDto.setCount(0);
        reminderTaskCountVos.add(officeCenterApplicationTaskCountDto);

        ReminderTaskCountVo officeCenterAllFormTaskCountDto = new ReminderTaskCountVo();
        List<String> apiResourceKeys = SecureUtil.getApiResourceKeys();
        String allFormPermission = "officeLeaveApplicationForm.AllForms";
        List<LeaveApplicationForm> allForms = Lists.newArrayList();
        if (apiResourceKeys.contains(allFormPermission)||SecureUtil.getStaffInfo().getIsAdmin()){
            allForms = leaveApplicationFormMapper.selectList(Wrappers.<LeaveApplicationForm>lambdaQuery()
                    .in(LeaveApplicationForm::getFkCompanyId,SecureUtil.getFkCompanyId())
                    .in(LeaveApplicationForm::getStatus, statusList));
        }
        officeCenterAllFormTaskCountDto.setKey(ProjectKeyEnum.OFFICE_CENTER_ALL_FORM.key);
        officeCenterAllFormTaskCountDto.setCount(allForms.size());
        reminderTaskCountVos.add(officeCenterAllFormTaskCountDto);


        return reminderTaskCountVos;
    }

    @Override
    public Boolean hasMediaAndAttach(String key, Long id) {
        List<OfficeMediaAndAttached> officeMediaAndAttacheds = mediaAndAttachedService.list(Wrappers.<OfficeMediaAndAttached>lambdaQuery()
                .eq(OfficeMediaAndAttached::getFkTableName, TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)
                .eq(OfficeMediaAndAttached::getFkTableId, id));
        if (GeneralTool.isNotEmpty(officeMediaAndAttacheds)){
            return true;
        }
        return false;
    }

    /**
     * 发送企业微信提醒
     *
     * @param businessKey
     * @return
     */
    @Override
    public Boolean sendWxCpMessage(String businessKey) throws Exception {
        Long staffId = workflowCenterClient.getAssigneeStaffId(businessKey).getData();
        if (GeneralTool.isEmpty(staffId)){
            return false;
        }
        Set<Long> userIds = Sets.newHashSet(staffId);
        Properties props = System.getProperties();
        String profile = props.getProperty("spring.profiles.active");
        if (GeneralTool.isNotEmpty(profile)) {
            if (profile.equals(AppConstant.PROD_CODE)){
                Map<Long, String> loginIdMap = permissionCenterClient.getStaffLoginIdByIds(userIds).getData();

                ConfigVo configVo = permissionCenterClient.getConfigByKey(ProjectKeyEnum.FILE_SRC_PREFIX.key).getData();
                String domain = configVo.getValue1();

                LeaveApplicationForm leaveApplicationForm = leaveApplicationFormMapper.selectById(Long.valueOf(businessKey));
                if (GeneralTool.isEmpty(leaveApplicationForm)){
                    return false;
                }
                LeaveApplicationFormType formType = leaveApplicationFormTypeService.getById(leaveApplicationForm.getFkLeaveApplicationFormTypeId());
                String staffName = permissionCenterClient.getStaffName(leaveApplicationForm.getFkStaffId()).getData();
                StaffVo staff = permissionCenterClient.getStaffById(leaveApplicationForm.getFkStaffId()).getData();
//                if (!"GEA".equals(interfaceConfiguration)){
//                    log.error("@sendWxCpMessage#非gea不发送企业微信消息！！");
//                    return false;
//                }
                //企业微信返回errCode
                String errCode = "errcode";
                //企业微信返回errmsg
                String errmsg = "errmsg";
                //企业微信返回errmsg中请求成功标志
                String successFlag = "ok";
                //获取企业accessToken
                String accessToken = getRedis.get(CacheNames.ACCESS_TOKEN_KEY);
                log.info("@sendWxCpMessage#获取缓存中accessToken:{}",accessToken);
                if (GeneralTool.isEmpty(accessToken)){
                    JSONObject accessTokenJson = BaseController.getAccessToken(CORP_ID, CORP_SECRET);
                    if (!accessTokenJson.get(errCode).equals(0)||!successFlag.equals(accessTokenJson.get(errmsg))){
                        log.error("@sendWxCpMessage#企业accessToken失败！！:{}",accessTokenJson.toJSONString());
                        return false;
//                        throw new GetServiceException(LocaleMessageUtils.getMessage("get_accessToken_failed"));
                    }
                    accessToken = accessTokenJson.get("access_token").toString();
                    log.info("@sendWxCpMessage#调用企业微信accessToken接口:{}",accessTokenJson.toJSONString());
                    Long expiresIn = Long.valueOf(accessTokenJson.get("expires_in").toString());
                    getRedis.setEx(CacheNames.ACCESS_TOKEN_KEY,accessToken,expiresIn);
                }
                String toUser = "";
                StringJoiner sj = new StringJoiner("|");
                for (Long userId : userIds) {
                    String loginId = loginIdMap.get(userId);
                    sj.add(loginId);
                }
                toUser = sj.toString();
                JSONObject messageBody = new JSONObject();
                messageBody.put("touser",toUser);
                messageBody.put("msgtype","textcard");
                messageBody.put("agentid",1000004);
                messageBody.put("enable_id_trans",0);
                messageBody.put("enable_duplicate_check",0);
                messageBody.put("duplicate_check_interval",1800);
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(new Date());
                int starYear = calendar.get(Calendar.YEAR);
                int starMonth = calendar.get(Calendar.MONTH) + 1;
                int starDay = calendar.get(Calendar.DAY_OF_MONTH);
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String contentStr = "<div class=\"gray\">#{date}</div><div class=\"normal\">申请类型：#{formType}</div><div class=\"normal\">申请用户：#{userName}</div><div class=\"normal\">开始时间：#{startTime}</div><div class=\"normal\">结束时间：#{endTime}</div>";
                String date = starYear+"年"+starMonth+"月"+starDay+"日";
                contentStr = contentStr.replace("#{date}",date);
                contentStr = contentStr.replace("#{formType}",formType.getTypeName());
                contentStr = contentStr.replace("#{userName}",staffName);
                contentStr = contentStr.replace("#{startTime}",simpleDateFormat.format(leaveApplicationForm.getStartTime()));
                contentStr = contentStr.replace("#{endTime}",simpleDateFormat.format(leaveApplicationForm.getEndTime()));
                //详情链接
                String detailUrl = domain + "/mobile/work-apply/look-apply-detail?id="+leaveApplicationForm.getId()+"&selectStatus=2";
                Map<String,String> content = Maps.newHashMap();
                content.put("title","您收到新的工休申请单，请处理");
                content.put("description",contentStr);
                content.put("url",detailUrl);
                content.put("btntxt","详情");
                messageBody.put("textcard",content);
                JSONObject sendMessageResult = BaseController.doSendMessageUrl(accessToken, messageBody);
                if (!sendMessageResult.get(errCode).equals(0)||!successFlag.equals(sendMessageResult.get(errmsg))){
                    log.error("@sendWxCpMessage#发送消息！:{}",sendMessageResult.toJSONString());
                    return false;
//                    throw new GetServiceException(LocaleMessageUtils.getMessage("failed_to_send_message"));
                }
                return true;
            }
        }
        log.info("非生产环境 不发送企业微信消息");
        return false;
    }

    /**
     * 获取某一休假类型的剩余时长
     *
     * @param remainingDayDto
     * @return
     */
    @Override
    public RemainingDayVo getRemainingDays(RemainingDayDto remainingDayDto) {
        //验证参数
        doValidateRemainingDayVoParam(remainingDayDto);
        //获取有效的库存
        List<LeaveStock> leaveStocks = doGetValidLeaveStocks(remainingDayDto);
        //累加库存包装成返回值
        RemainingDayVo result = doPackageRemainingDayDto(leaveStocks);

        return result;
    }

    /**
     * 累加库存
     *
     * @param leaveStocks
     * @return
     */
    private RemainingDayVo doPackageRemainingDayDto(List<LeaveStock> leaveStocks) {
        RemainingDayVo remainingDayVo = new RemainingDayVo();
        BigDecimal result = leaveStocks.stream()
                // 将LeaveStock对象的LeaveStock取出来map为Bigdecimal
                .map(LeaveStock::getLeaveStock)
                // 使用reduce()聚合函数,实现累加器
                .reduce(BigDecimal.ZERO,BigDecimal::add);
        remainingDayVo.setRemainingTime(result);
        return remainingDayVo;
    }

    /**
     * 获取有效的库存
     * @param remainingDayDto
     * @return
     */
    private List<LeaveStock> doGetValidLeaveStocks(RemainingDayDto remainingDayDto) {
        return leaveStockService.list(Wrappers.<LeaveStock>lambdaQuery()
                .eq(LeaveStock::getFkStaffId, remainingDayDto.getFkStaffId())
                .like(LeaveStock::getLeaveTypeKey, remainingDayDto.getTypeKey())
                .and(wrapper_ -> wrapper_
                        .gt(LeaveStock::getEffectiveDeadline, new Date())
                        .or()
                        .isNull(LeaveStock::getEffectiveDeadline)));
    }

    /**
     * 验证参数合法性
     * @param remainingDayDto
     */
    private void doValidateRemainingDayVoParam(RemainingDayDto remainingDayDto) {
        if (GeneralTool.isEmpty(remainingDayDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(remainingDayDto.getFkStaffId())||GeneralTool.isEmpty(remainingDayDto.getTypeKey())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
    }

    @Override
    public TimeConfigVo getTimeConfig(TimeConfigDto timeConfigDto){
        if (GeneralTool.isEmpty(timeConfigDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if(GeneralTool.isEmpty(timeConfigDto.getFkLeaveApplicationFormTypeId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("type_error"));
        }

        timeConfigDto.setFkCompanyId(SecureUtil.getStaffInfo().getFkCompanyId());
        timeConfigDto.setFkStaffId(SecureUtil.getStaffId());
        timeConfigDto.setFkDepartmentId(SecureUtil.getFkDepartmentId());
        //根据时间范围的所有排班
        List<WorkScheduleDateConfig> workScheduleDateConfigList = workScheduleDateConfigService.getWorkScheduleDateConfigByDate(timeConfigDto.getApplicationTime(), timeConfigDto.getApplicationTime(), timeConfigDto.getFkCompanyId(), timeConfigDto.getFkDepartmentId());

        WorkScheduleStaffConfig staffTimeConfig = workScheduleStaffConfigService.getWorkScheduleStaffConfig(timeConfigDto);
        //校验是否为非工作日
        Boolean ignoreDate= AttendanceUtils.dayOff(timeConfigDto.getApplicationTime(),workScheduleDateConfigList,staffTimeConfig);
        //获取工休申请单类型
        LeaveApplicationFormType leaveApplicationFormType = leaveApplicationFormTypeMapper.selectById(timeConfigDto.getFkLeaveApplicationFormTypeId());
        if(!ProjectKeyEnum.ESCORT_VACATION.key.equals(leaveApplicationFormType.getTypeKey()) &&
           !ProjectKeyEnum.MATERNITY_VACATION.key.equals(leaveApplicationFormType.getTypeKey()) &&
           !ProjectKeyEnum.OVERTIEM.key.equals(leaveApplicationFormType.getTypeKey())
        ){
            if(ignoreDate){
                throw new GetServiceException(LocaleMessageUtils.getMessage("date_error"));
            }
        }





        List<WorkScheduleTimeConfig> timeConfigList = workScheduleTimeConfigMapper.getWorkScheduleTimeConfig(timeConfigDto);


        List<WorkScheduleTimeConfig> list = timeConfigList.stream().filter(t->GeneralTool.isNotEmpty(t.getFkDepartmentId())).collect(Collectors.toList());
        Map<Long, WorkScheduleTimeConfig> timeConfigMap = list.stream()
                .collect(Collectors.toMap(WorkScheduleTimeConfig::getFkDepartmentId, Function.identity()));
        //通过部门获取对应时间设定
        WorkScheduleTimeConfig workScheduleTimeConfig = timeConfigMap.get(SecureUtil.getStaffInfo().getFkDepartmentId());
        //部门未设置时间设定使用默认
        if(GeneralTool.isEmpty(workScheduleTimeConfig)){
            List<WorkScheduleTimeConfig> configs =  timeConfigList.stream()
                    .filter(c->GeneralTool.isEmpty(c.getFkDepartmentId()) && c.getFkCompanyId().equals(SecureUtil.getStaffInfo().getFkCompanyId()))
                    .collect(Collectors.toList());
            if(GeneralTool.isNotEmpty(configs)){
                workScheduleTimeConfig = configs.get(0);
            }
        }



        if(GeneralTool.isEmpty(workScheduleTimeConfig)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("work_schedule_time_config_null"));
        }
        TimeConfigVo timeConfigVo = new TimeConfigVo();
        if(timeConfigDto.getType().equals(1)){
            if(GeneralTool.isNotEmpty(staffTimeConfig)){
                timeConfigVo.setWorkingStartTime(staffTimeConfig.getWorkingStartTime());
            }else{
                timeConfigVo.setWorkingStartTime(workScheduleTimeConfig.getWorkingStartTime());
            }
        }else{
            if(GeneralTool.isNotEmpty(staffTimeConfig)){
                timeConfigVo.setWorkingEndTime(staffTimeConfig.getWorkingEndTime());
            }else{
                timeConfigVo.setWorkingEndTime(workScheduleTimeConfig.getWorkingEndTime());
            }
        }


        return timeConfigVo;
    }

    @Override
    public ApplicationFormDaysVo getApplicationFormDays(GetApplicationFormDaysDto vo){
        if (GeneralTool.isEmpty(vo)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if(GeneralTool.isEmpty(vo.getFkLeaveApplicationFormTypeId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ApplicationFormDaysVo daysDto = new ApplicationFormDaysVo();
        Long fkDepartmentId = SecureUtil.getStaffInfo().getFkDepartmentId();
        Long fkCompanyId = SecureUtil.getStaffInfo().getFkCompanyId();
        Long fkStaffId = SecureUtil.getStaffId();

        //获取工休申请单类型
        LeaveApplicationFormType leaveApplicationFormType = leaveApplicationFormTypeMapper.selectById(vo.getFkLeaveApplicationFormTypeId());

        //特殊人员排班时间设定
        Map<Long,List<WorkScheduleStaffConfig>> staffConfigListMap = workScheduleStaffConfigService.getWorkScheduleStaffConfigList(fkCompanyId);
        List<WorkScheduleStaffConfig> workScheduleStaffConfigList = staffConfigListMap.get(fkStaffId);

        //获取时间配置
        List<WorkScheduleTimeConfig> workScheduleTimeConfigList = workScheduleTimeConfigMapper.selectList(Wrappers.<WorkScheduleTimeConfig>lambdaQuery()
                .eq(WorkScheduleTimeConfig::getFkCompanyId,fkCompanyId));

        //根据时间范围的所有排班
        List<WorkScheduleDateConfig> workScheduleDateConfigList = workScheduleDateConfigService.getWorkScheduleDateConfigByDate(vo.getStartTime(),vo.getEndTime(),fkCompanyId,fkDepartmentId);

        //陪产假、产假时长为自然日（计算日期间隔）
        if(ProjectKeyEnum.ESCORT_VACATION.key.equals(leaveApplicationFormType.getTypeKey()) || ProjectKeyEnum.MATERNITY_VACATION.key.equals(leaveApplicationFormType.getTypeKey())){
            BigDecimal days = new BigDecimal(GetDateUtil.compareDayDiff(vo.getStartTime(), vo.getEndTime()));
            days = days.add(new BigDecimal(1));
            daysDto.setHour(days.multiply(new BigDecimal(8)));
            daysDto.setDay(days);
            return daysDto;
        }

        BigDecimal durationSum = AttendanceUtils.getDuration(workScheduleTimeConfigList,workScheduleStaffConfigList,workScheduleDateConfigList,
                fkCompanyId,fkDepartmentId,leaveApplicationFormType.getTypeKey(), vo.getStartTime(),vo.getEndTime(),1);
        BigDecimal days = AttendanceUtils.getDuration(workScheduleTimeConfigList,workScheduleStaffConfigList,workScheduleDateConfigList,
                fkCompanyId,fkDepartmentId,leaveApplicationFormType.getTypeKey(), vo.getStartTime(),vo.getEndTime(),2);

        daysDto.setHour(durationSum);
        daysDto.setDay(days);
        return daysDto;
    }

    /**
     * 作废工休单（行政人员权限）
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancelLeaveApplicationForm(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        LeaveApplicationForm leaveApplicationForm = leaveApplicationFormMapper.selectById(id);
        if (GeneralTool.isEmpty(leaveApplicationForm)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (GeneralTool.isNotEmpty(leaveApplicationForm.getFkLeaveApplicationFormIdRevoke())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("void_exception"));
        }

        Integer formStatus = leaveApplicationForm.getStatus();
        //库存恢复
        if (1 == formStatus){
            List<LeaveApplicationFormType> leaveApplicationFormTypes = leaveApplicationFormTypeService.list();
            Map<String, Long> typekeyMap = leaveApplicationFormTypes.stream().collect(HashMap::new, (m, v) -> m.put(v.getTypeKey(), v.getId()), HashMap::putAll);

            if (leaveApplicationForm.getFkLeaveApplicationFormTypeId().equals(typekeyMap.get("annualVacation"))
                    ||leaveApplicationForm.getFkLeaveApplicationFormTypeId().equals(typekeyMap.get("diseaseVacation"))
                    ||leaveApplicationForm.getFkLeaveApplicationFormTypeId().equals(typekeyMap.get("takeDeferredHolidays"))
                    ||leaveApplicationForm.getFkLeaveApplicationFormTypeId().equals(typekeyMap.get("overtime"))) {

                LeaveLog leaveLog = leaveLogService.getOne(Wrappers.<LeaveLog>lambdaQuery().eq(LeaveLog::getFkLeaveApplicationFormId, id));
                BigDecimal stock = leaveLog.getDuration().negate();
                if (GeneralTool.isNotEmpty(leaveLog.getFkLeaveStockId())){
                    LeaveStock leaveStock = leaveStockService.getById(leaveLog.getFkLeaveStockId());
                    leaveStock.setLeaveStock(leaveStock.getLeaveStock().add(stock));
                    utilService.setUpdateInfo(leaveStock);
                    boolean b = leaveStockService.updateById(leaveStock);
                    if (!b){
                        throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
                    }
                }

                leaveLog.setDuration(stock);
                leaveLog.setOptTypeKey(ProjectKeyEnum.SYSTEM_ADD_STOCK.key);
                leaveLog.setFkLeaveStockId(null);
                leaveLog.setId(null);
                utilService.setCreateInfo(leaveLog);
                boolean save = leaveLogService.save(leaveLog);
                if (!save){
                    throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
                }
            }
        }

        //作废同时去掉提醒任务
        Result<List<ActHiTaskInstVo>> result = workflowCenterClient.getActHiTaskInstDtosByBusinessKey(String.valueOf(id), TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key);
        if (result.isSuccess()&&GeneralTool.isNotEmpty(result.getData())){
            List<ActHiTaskInstVo> actHiTaskInstVos = result.getData();
            Set<Long> tableIds = actHiTaskInstVos.stream()
                    .map(a->Long.valueOf(a.getId()))
                    .collect(Collectors.toSet());

            //删除提醒任务
            Result<Boolean> booleanResult = reminderCenterClient.batchUpdateByTableIds(new ArrayList<>(), TableEnum.ACT_HI_TASKINST.key, tableIds);
            if (!booleanResult.isSuccess()&&GeneralTool.isEmpty(booleanResult.getData())){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
            //删除流程
            if (0 != formStatus) {
                String procInstId = actHiTaskInstVos.get(0).getProcInstId();
                Boolean aBoolean = workflowCenterClient.stopExecution(procInstId, "行政人员/管理员终止流程", TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key, String.valueOf(id)).getData();
                if (!aBoolean){
                    throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
                }
                CacheUtil.clear(CacheNames.TASK_CACHE);
            }

        }else {

            if (2 == leaveApplicationForm.getStatus()){
                List<Long> staffIdsByResourceKey = permissionCenterClient.getStaffIdsByResourceKey("officeLeaveApplicationForm.AllForms", true);
                if (GeneralTool.isNotEmpty(staffIdsByResourceKey)){
                    for (Long aLong : staffIdsByResourceKey) {
                        CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(aLong), cache_key);
                    }
                }
            }

            leaveApplicationForm.setStatus(ProjectExtraEnum.CANCELLATION.key);
            utilService.setUpdateInfo(leaveApplicationForm);
            leaveApplicationFormMapper.updateById(leaveApplicationForm);
            if (GeneralTool.isNotEmpty(leaveApplicationForm.getFkStaffId())&&formStatus==0){
                //更新缓存
                CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(leaveApplicationForm.getFkStaffId()), cache_key);
            }else {
                CacheUtil.clear(CacheNames.TASK_CACHE);
            }
        }

    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        LeaveApplicationForm leaveApplicationForm = leaveApplicationFormMapper.selectById(id);
        if (GeneralTool.isEmpty(leaveApplicationForm)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //待发起 && 登陆人是发起人
        if (leaveApplicationForm.getStatus().equals(ProjectExtraEnum.TO_BE_INITIATED.key)&&leaveApplicationForm.getFkStaffId().equals(SecureUtil.getStaffId())){
            int i = leaveApplicationFormMapper.deleteById(id);
            if (i!=1){
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
            }
        }else {
            if (!leaveApplicationForm.getStatus().equals(ProjectExtraEnum.TO_BE_INITIATED.key)){
                throw new GetServiceException(LocaleMessageUtils.getMessage("process_already_start_cannot_delete"));
            }
            throw new GetServiceException(LocaleMessageUtils.getMessage("permissions_insufficient"));
        }
    }

    /**
     * 延迟的时间和是否上传了附件
     * @param id
     * @return
     */
    @Override
    public ApprovalDelayConfigVo getApprovalDelayConfig(Long id) {
        ApprovalDelayConfigVo result = new ApprovalDelayConfigVo();
//        ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.LEAVE_APPLICATION_FORM_SUBMIT_LIMIT.key).getData();
//        String configJson = configDto.getValue1();
//        JSONObject configJsonObject = JSON.parseObject(configJson);
//        JSONArray configArray = null;
//        if (GeneralTool.isEmpty(SecureUtil.getFkCompanyId())||(!SecureUtil.getFkCompanyId().equals(2L)&&!SecureUtil.getFkCompanyId().equals(3L))){
//            configArray = configJsonObject.getJSONArray("OTHER");
//        }
//        if(SecureUtil.getFkCompanyId().equals(2L)){
//            configArray = configJsonObject.getJSONArray("GEA");
//        }else if (SecureUtil.getFkCompanyId().equals(3L)){
//            configArray = configJsonObject.getJSONArray("IAE");
//        }
//        List<Integer> configList = configArray.toJavaList(Integer.class);
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.LEAVE_APPLICATION_FORM_SUBMIT_LIMIT.key, 1).getData();
        String configValue1 = companyConfigMap.get(SecureUtil.getFkCompanyId());
        List<Integer> configList = new ArrayList<>(JSON.parseArray(configValue1, Integer.class));
        if (GeneralTool.isEmpty(id)){
            result.setDelayHours(configList.get(0));
            return result;
        }

        LeaveApplicationForm leaveApplicationForm = leaveApplicationFormMapper.selectById(id);
        if (GeneralTool.isEmpty(leaveApplicationForm)){
            result.setDelayHours(configList.get(0));
            result.setHasUpdateAttachment(true);
        }else {
            if (GeneralTool.isNotEmpty(SecureUtil.getStaffId())){
                StaffVo staffVo = permissionCenterClient.getStaffById(SecureUtil.getStaffId()).getData();
                String loginId = staffVo.getLoginId();

                LambdaQueryWrapper<OfficeMediaAndAttached> wrapper = Wrappers.lambdaQuery(OfficeMediaAndAttached.class);
                wrapper.eq(OfficeMediaAndAttached::getFkTableName,TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)
                        .eq(OfficeMediaAndAttached::getFkTableId,id)
                        .eq(OfficeMediaAndAttached::getTypeKey, FileTypeEnum.OFFICE_LEAVE_APPLICATION_FORM_FILE.key)
                        .eq(OfficeMediaAndAttached::getGmtCreateUser,loginId);
                List<OfficeMediaAndAttached> officeMediaAndAttacheds = mediaAndAttachedService.list(wrapper);
                //发起人
                if (leaveApplicationForm.getGmtCreateUser().equals(loginId)){
                    result.setDelayHours(configList.get(0));
                }else {
                    result.setDelayHours(configList.get(1));
                }
                result.setHasUpdateAttachment(GeneralTool.isNotEmpty(officeMediaAndAttacheds));
            }
        }

        return result;
    }

    @Override
    public List<LeaveApplicationFormExportVo> getLeaveApplicationFormExportDtos(LeaveApplicationFormQueryDto leaveApplicationFormQueryDto) {
        List<LeaveApplicationFormVo> datas = getLeaveApplicationForms(leaveApplicationFormQueryDto, null);
        List<LeaveApplicationFormExportVo> leaveApplicationFormExportVos = BeanCopyUtils.copyListProperties(datas, LeaveApplicationFormExportVo::new, (data, leaveApplicationFormExportDto) -> {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            leaveApplicationFormExportDto.setLeaveTime(simpleDateFormat.format(data.getStartTime()) + " - " + simpleDateFormat.format(data.getEndTime()));
            leaveApplicationFormExportDto.setDays(data.getDays().toString() + "（" + data.getDays().divide(new BigDecimal("8"),2,BigDecimal.ROUND_HALF_UP) + "天）");
            leaveApplicationFormExportDto.setGmtCreate(simpleDateFormat.format(data.getGmtCreate()));
            leaveApplicationFormExportDto.setStatusName(ProjectExtraEnum.getValueByKey(data.getStatus(), ProjectExtraEnum.STATUS_OF_APPROVE));
        });
        return leaveApplicationFormExportVos;
    }

    @Override
    public List<AiLeaveApplicationFormVo> getAllLeaveApplicationForms(String selectStatus, AiLeaveQueryParam aiLeaveQueryParam) {
        final String myApplicationStatus = "0";
        final String allStatus = "1";
        final String myApprovalStatus = "2";

        Map<Long, Integer> map = new HashMap<>();
        Result<Map<Long, Integer>> fromIdsResult = workflowCenterClient.getFromIdsByStaffId(GetAuthInfo.getStaffId(), TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key);
        if (fromIdsResult.isSuccess() && GeneralTool.isNotEmpty(fromIdsResult.getData())) {
            map = fromIdsResult.getData();
        }

        LambdaQueryWrapper<LeaveApplicationForm> leaveApplicationFormLambdaQueryWrapper = new LambdaQueryWrapper<>();
        //不选所属公司时
        List<Long> companyIds = SecureUtil.getCompanyIds();
        leaveApplicationFormLambdaQueryWrapper.in(LeaveApplicationForm::getFkCompanyId, companyIds);

        //查询条件-表单创建日期-开始
        if (GeneralTool.isNotEmpty(aiLeaveQueryParam.getStartTime())) {
            leaveApplicationFormLambdaQueryWrapper.ge(LeaveApplicationForm::getStartTime, aiLeaveQueryParam.getStartTime());
        }
        //查询条件-表单创建日期-结束
        if (GeneralTool.isNotEmpty(aiLeaveQueryParam.getEndTime())) {
            leaveApplicationFormLambdaQueryWrapper.le(LeaveApplicationForm::getEndTime, aiLeaveQueryParam.getEndTime());
        }
        //根据不同的选择状态拼接不同的条件
        if (myApplicationStatus.equals(selectStatus)) {
            //0表示要显示我的申请列表，即我创建的表单
            leaveApplicationFormLambdaQueryWrapper.eq(LeaveApplicationForm::getFkStaffId, GetAuthInfo.getStaffId());
        } else if (allStatus.equals(selectStatus)) {
            //1表示所有表单
            Long staffId = SecureUtil.getStaffId();
            Set<Long> staffFollowerIds = new HashSet<>();
            //员工id + 业务下属员工ids
            List<Long> staffFollowerIdsResult = permissionCenterClient.getStaffFollowerIds(staffId).getData();
            if (GeneralTool.isNotEmpty(staffFollowerIdsResult)) {
                staffFollowerIds = staffFollowerIdsResult.stream().filter(Objects::nonNull).collect(Collectors.toSet());
            }
            staffFollowerIds.add(staffId);

            //查询在职人员
            List<StaffVo> staffVoByIds = permissionCenterClient.getStaffDtoByIds(staffFollowerIds);
            //在职人员Ids
            if (GeneralTool.isNotEmpty(staffVoByIds)) {
                List<Long> longList = staffVoByIds.stream().map(s -> s.getId()).collect(Collectors.toList());
                leaveApplicationFormLambdaQueryWrapper.in(LeaveApplicationForm::getFkStaffId, longList);
            }

        } else if (myApprovalStatus.equals(selectStatus)) {
            //2表示显示我的审批列表，即我操作过的表单都要显示
            List<Long> fromIds = new ArrayList<>(map.keySet());
            if (GeneralTool.isEmpty(fromIds)) {
                fromIds.add(0L);
            }
            leaveApplicationFormLambdaQueryWrapper.in(LeaveApplicationForm::getId, fromIds);

            //只显示在职申请人的申请信息
            List<LeaveApplicationForm> leaveApplicationForms = leaveApplicationFormMapper.selectList(leaveApplicationFormLambdaQueryWrapper);
            //在职申请人集合
            Set<Long> staffIds = new HashSet<>();
            for (LeaveApplicationForm leaveApplicationForm : leaveApplicationForms) {
                staffIds.add(leaveApplicationForm.getFkStaffId());
            }
            //查询在职人员
            List<StaffVo> staffVoByIds = permissionCenterClient.getStaffDtoByIds(staffIds);
            //在职人员Ids
            if (GeneralTool.isNotEmpty(staffVoByIds)) {
                List<Long> longList = staffVoByIds.stream().map(s -> s.getId()).collect(Collectors.toList());
                leaveApplicationFormLambdaQueryWrapper.in(LeaveApplicationForm::getFkStaffId, longList);
            }

        }

        leaveApplicationFormLambdaQueryWrapper.orderByDesc(LeaveApplicationForm::getGmtCreate);

        List<LeaveApplicationForm> leaveApplicationForms = leaveApplicationFormMapper.selectList(leaveApplicationFormLambdaQueryWrapper);
        if (GeneralTool.isEmpty(leaveApplicationForms)) {
            return new ArrayList<>();
        }

        List<String> loginIds = leaveApplicationForms.stream().map(LeaveApplicationForm::getGmtCreateUser).collect(Collectors.toList());
        List<Staff> staffGmtCreateList = permissionCenterClient.getStaffByLoginIds(loginIds);
        Map<String, Staff> staffMap = staffGmtCreateList.stream().collect(Collectors.toMap(Staff::getLoginId, Function.identity()));
        //        System.out.println(staffIdsMap);
//        page.restPage(leaveApplicationForms);
        List<AiLeaveApplicationFormVo> convertDatas = new ArrayList<>();
        //公司id集合
//        Set<Long> companyIds = new HashSet<>();
        //申请人id集合
        Set<Long> staffIds = new HashSet<>();
        //报销单id集合
//        List<Long> leaveApplicationFormIds = new ArrayList<>();
        //获取各自集合的值
        for (LeaveApplicationForm leaveApplicationForm : leaveApplicationForms) {
//            companyIds.add(leaveApplicationForm.getFkCompanyId());
            staffIds.add(leaveApplicationForm.getFkStaffId());
//            leaveApplicationFormIds.add(leaveApplicationForm.getId());
        }

        //员工在职状态
//        Result<Map<Long, Boolean>> reDuty = permissionCenterClient.getStaffIsOnDuty(staffIds);
//        Map<Long, Boolean> onDutyMap = new HashMap<>();
//        if (reDuty.isSuccess()) {
//            if (GeneralTool.isNotEmpty(reDuty.getData())) {
//                onDutyMap = reDuty.getData();
//            }
//        }
        //feign调用 获取公司 id-name的map
//        companyIds.removeIf(Objects::isNull);
//        Map<Long, String> companyNameMap = new HashMap<>();
//        Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(companyIds);
//        if (result.isSuccess() && result.getData() != null) {
//            companyNameMap = result.getData();
//        }

        //feign调用 获取流程方面dto 报销单id-actRuTaskDot的map
//        leaveApplicationFormIds.removeIf(Objects::isNull);
//        Map<Long, ActRuTaskVo> actRuTaskDtoMap = null;
//        Map<String, List<Long>> businessIdsWithProcdefKey = new HashMap<>();
//        businessIdsWithProcdefKey.put(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key, leaveApplicationFormIds);
//        Result<Map<Long, ActRuTaskVo>> taskResult = workflowCenterClient.getActRuTaskDtosByBusinessKey(businessIdsWithProcdefKey);
//        if (taskResult.isSuccess() && GeneralTool.isNotEmpty(taskResult.getData())) {
//            actRuTaskDtoMap = taskResult.getData();
//
//            List<ActRuTaskVo> actRuTaskVos = new ArrayList<>();
//            if (GeneralTool.isNotEmpty(actRuTaskDtoMap)) {
//                for (Map.Entry<Long, ActRuTaskVo> longActRuTaskDtoEntry : actRuTaskDtoMap.entrySet()) {
//                    actRuTaskVos.add(longActRuTaskDtoEntry.getValue());
//                }
//
//            }
//            Set<Long> assigneeIds = new HashSet<>();
//            if (GeneralTool.isNotEmpty(actRuTaskVos)) {
//                assigneeIds = actRuTaskVos.stream()
//                        .filter(actRuTaskDto -> GeneralTool.isNotEmpty(actRuTaskDto.getAssignee()))
//                        .map(actRuTaskDto -> Long.valueOf(actRuTaskDto.getAssignee()))
//                        .collect(Collectors.toSet());
//            }
//            staffIds.addAll(assigneeIds);
//        }

        //feign调用 获取报销人id-name的map
        staffIds.removeIf(Objects::isNull);
        Map<Long, String> staffNameMap = permissionCenterClient.getStaffNameMap(staffIds).getData();

//            //部门ids
//            Set<Long> fkDepartmentIds = leaveApplicationForms.stream().map(LeaveApplicationForm::getFkDepartmentId).collect(Collectors.toSet());
//            //部门name map
//            Map<Long, String> departmentNameMap = Maps.newHashMap();
//            if (GeneralTool.isNotEmpty(fkDepartmentIds)) {
//                departmentNameMap = permissionCenterClient.getDepartmentNamesByIds(fkDepartmentIds).getData();
//            }

//        //获取报销单附件
//        Set<Long> ids = leaveApplicationForms.stream().map(LeaveApplicationForm::getId).collect(Collectors.toSet());
//
//        List<OfficeMediaAndAttached> officeMediaAndAttacheds = mediaAndAttachedService.list(Wrappers.<OfficeMediaAndAttached>lambdaQuery()
//                .eq(OfficeMediaAndAttached::getFkTableName, TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)
//                .in(OfficeMediaAndAttached::getFkTableId, ids));
//
//        Map<Long, List<OfficeMediaAndAttached>> officeMediaAndAttachedsMap = officeMediaAndAttacheds.stream().collect(Collectors.groupingBy(OfficeMediaAndAttached::getFkTableId));

        for (LeaveApplicationForm leaveApplicationForm : leaveApplicationForms) {
            AiLeaveApplicationFormVo leaveApplicationFormVo = BeanCopyUtils.objClone(leaveApplicationForm, AiLeaveApplicationFormVo::new);
            //类型名称
            leaveApplicationFormVo.setLeaveApplicationFormTypeName(leaveApplicationFormTypeService.getLeaveApplicationFormTypeNameById(leaveApplicationFormVo.getFkLeaveApplicationFormTypeId()));
            //公司名称
//                leaveApplicationFormVo.setCompanyName(companyNameMap.get(leaveApplicationFormVo.getFkCompanyId()));
            if (!(myApplicationStatus.equals(selectStatus))) {
                //申请人名称
                leaveApplicationFormVo.setStaffName(staffNameMap.get(leaveApplicationFormVo.getFkStaffId()));
                Staff staff = staffMap.get(leaveApplicationFormVo.getGmtCreateUser());
                if (GeneralTool.isNotEmpty(staff)) {
                    leaveApplicationFormVo.setGmtCreateUser(staff.getName() + "（" + staff.getNameEn() + "）");
                }
            }
            //员工在职状态
//                leaveApplicationFormVo.setIsOnDuty(onDutyMap.get(staff.getId()));
            //申请人撤单时在原因加上撤单申请
            if (GeneralTool.isNotEmpty(leaveApplicationForm.getFkLeaveApplicationFormIdRevoke())) {
                leaveApplicationFormVo.setReason("【撤单申请】" + leaveApplicationForm.getReason());
            }

            //工休单状态
//            leaveApplicationFormVo.setExpenseClaimFormStatus(map.get(leaveApplicationFormVo.getId()));
            //            0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销
            if (GeneralTool.isNotEmpty(leaveApplicationForm.getStatus())) {
                Integer status = leaveApplicationForm.getStatus();
                if (status == 0) {
                    leaveApplicationFormVo.setStatus("待发起");
                }
                if (status == 1) {
                    leaveApplicationFormVo.setStatus("审批结束");
                }
                if (status == 2) {
                    leaveApplicationFormVo.setStatus("审批中");
                }
                if (status == 3) {
                    leaveApplicationFormVo.setStatus("审批拒绝");
                }
                if (status == 4) {
                    leaveApplicationFormVo.setStatus("申请放弃");
                }
                if (status == 5) {
                    leaveApplicationFormVo.setStatus("作废");
                }
                if (status == 6) {
                    leaveApplicationFormVo.setStatus("撤销");
                }
            }

            //部门名称
//                leaveApplicationFormVo.setDepartmentName(departmentNameMap.get(leaveApplicationFormVo.getFkDepartmentId()));
            //流程对象
//                ActRuTaskVo actRuTaskVo = actRuTaskDtoMap.get(leaveApplicationFormVo.getId());
//                //正在进行的任务id
//                if (GeneralTool.isNotEmpty(actRuTaskVo.getId())) {
//                    leaveApplicationFormVo.setTaskId(Long.valueOf(actRuTaskVo.getId()));
//                }
//                //流程实例id
//                if (GeneralTool.isNotEmpty(actRuTaskVo.getProcInstId())) {
//                    leaveApplicationFormVo.setProcInstId(Long.valueOf(actRuTaskVo.getProcInstId()));
//                }
//                //任务版本
//                if (GeneralTool.isNotEmpty(actRuTaskVo.getTaskVersion())) {
//                    leaveApplicationFormVo.setTaskVersion(actRuTaskVo.getTaskVersion());
//                }
//                //审批人名称
//                if (GeneralTool.isNotEmpty(actRuTaskVo.getAssignee())) {
//                    leaveApplicationFormVo.setAssigneeName(staffNameMap.get(Long.valueOf(actRuTaskVo.getAssignee())));
//                }

//                if (GeneralTool.isNotEmpty(officeMediaAndAttachedsMap) && GeneralTool.isNotEmpty(officeMediaAndAttachedsMap.get(leaveApplicationFormVo.getId()))) {
//                    leaveApplicationFormVo.setExistMediaAndAttacheds(GeneralTool.isNotEmpty(officeMediaAndAttachedsMap.get(leaveApplicationFormVo.getId())));
//                } else {
//                    leaveApplicationFormVo.setExistMediaAndAttacheds(false);
//                }

            convertDatas.add(leaveApplicationFormVo);
        }

        return convertDatas;
    }

    public LeaveBalance holidayBalance(Long staffId) {
        LeaveBalance leaveBalance = new LeaveBalance();
        Result<StaffVo> result = permissionCenterClient.getCompanyIdByStaffId(staffId);
        StaffVo staffVo = new StaffVo();
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            staffVo = result.getData();
        }
        //年假剩余天数
        BigDecimal annualLeaveDays = leaveStockService.getLeaveStockByStaffId(staffVo.getId(), "annualVacation").getLeaveStockSum();
        if (GeneralTool.isEmpty(annualLeaveDays)) {
            leaveBalance.setAnnualLeaveDays(new BigDecimal(0));
        } else {
            leaveBalance.setAnnualLeaveDays(annualLeaveDays);
        }
        //补休剩余天数
        BigDecimal compensatoryLeaveDays = leaveStockService.getLeaveStockByStaffId(staffVo.getId(), "takeDeferredHolidays").getLeaveStockSum();
        if (GeneralTool.isEmpty(compensatoryLeaveDays)) {
            leaveBalance.setCompensatoryLeaveDays(new BigDecimal(0));
        } else {
            leaveBalance.setCompensatoryLeaveDays(compensatoryLeaveDays);
        }
        return leaveBalance;
    }

}

