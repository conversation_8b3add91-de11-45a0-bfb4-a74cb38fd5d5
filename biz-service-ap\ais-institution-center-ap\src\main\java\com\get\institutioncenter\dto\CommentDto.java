package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2020/11/5
 * @TIME: 15:34
 * @Description:
 **/
@Data
public class CommentDto extends BaseVoEntity {
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;
    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    @NotNull(message = "表id不能为空", groups = {Add.class, Update.class})
    private Long fkTableId;

    /**
     * 评论
     */
    @ApiModelProperty(value = "评论", required = true)
    private String comment;

}
