package com.get.financecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @author: Sea
 * @create: 2021/4/7 16:26
 * @verison: 1.0
 * @description:
 */
@Data
public class ExpenseClaimFormItemDto extends BaseVoEntity {


    @NotNull(message = "费用报销费用类型Id不能为空")
    @ApiModelProperty(value = "费用报销费用类型Id")
    private Long fkExpenseClaimFeeTypeId;

    @NotBlank(message = "关联类型Key不能为空")
    @ApiModelProperty(value = "关联类型Key（目标类型表名）")
    private String relationTargetKey;

    @NotNull(message = "关联类型Id不能为空")
    @ApiModelProperty(value = "关联类型Id（目标类型表对应记录项Id）")
    private Long relationTargetId;

    @ApiModelProperty(value = "活动类型表名（目标类型表名）")
    private String fkEventTableName;

    @ApiModelProperty(value = "活动Id（目标类型表对应记录项Id）")
    private Long fkEventTableId;

    @NotBlank(message = "报销摘要不能为空")
    @ApiModelProperty(value = "报销摘要")
    private String summary;

    @NotBlank(message = "币种编号不能为空")
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    @NotNull(message = "报销金额不能为空")
    @ApiModelProperty(value = "报销金额")
    private BigDecimal amount;

    @NotNull(message = "发票金额不能为空")
    @ApiModelProperty(value = "发票金额")
    private BigDecimal invoiceAmount;

    @ApiModelProperty(value = "费用关联代理id")
    private Long fkAgentId;

    @ApiModelProperty(value = "费用报销关联代理内容Id")
    private Long fkExpenseClaimAgentContentId;

    @ApiModelProperty(value = "人数")
    private Integer peopleCount;

}
