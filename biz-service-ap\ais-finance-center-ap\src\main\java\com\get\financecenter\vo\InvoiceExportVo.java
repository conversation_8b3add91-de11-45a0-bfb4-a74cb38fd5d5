package com.get.financecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class InvoiceExportVo {

    @ApiModelProperty(value = "所属公司")
    private String companyName;


    @ApiModelProperty(value = "是否到账")
    private String isAccountStr;

    @ApiModelProperty(value = "发票业务类型")
    private String fkTypeKeyName;

    @ApiModelProperty(value = "Invoice Number")
    private String num;


    @ApiModelProperty(value = "发票业务对象")
    private String targetName;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开票时间")
    private Date invoiceDate;

    @ApiModelProperty(value = "币种")
    private String currencyTypeName;

    @ApiModelProperty(value = "发票金额")
    private BigDecimal invoiceAmount;

    @ApiModelProperty(value = "摘要")
    private String summary;

    @ApiModelProperty(value = "发票状态")
    private String statusStr;

    @ApiModelProperty("创建人")
    private String gmtCreateUser;

    @ApiModelProperty("创建时间")
    private Date gmtCreate;
}
