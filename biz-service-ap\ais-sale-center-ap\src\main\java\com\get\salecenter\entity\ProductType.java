package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author:Oliver
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:保险产品类型
 */
@Data
@TableName("u_product_type")
public class ProductType extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "保险公司Id")
    private Long fkInsuranceCompanyId;

    @ApiModelProperty(value = "产品类型名称")
    private String typeName;

    @ApiModelProperty(value = "产品类型_key")
    private String typeKey;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;
}

