package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.CommonUtil;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.financecenter.vo.FCommentVo;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import com.get.financecenter.vo.PaymentFormVo;
import com.get.financecenter.service.IPaymentFormService;
import com.get.financecenter.dto.*;
import com.get.financecenter.dto.PaymentFormAgentUpdateDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <AUTHOR>
 * @DATE: 2020/12/23
 * @TIME: 16:06
 * @Description:
 **/

@Api(tags = "付款单管理")
@RestController
@RequestMapping("finance/paymentForm")
public class PaymentFormController {

    @Resource
    private IPaymentFormService paymentFormService;


    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.PaymentFormItemDto>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/付款单管理/查询")
    @PostMapping("datas")
    public ResponseBo<PaymentFormVo> datas(@RequestBody SearchBean<PaymentFormDto> page) {
        List<PaymentFormVo> datas = paymentFormService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 导出付款大单列表
     *
     * @param paymentFormDto
     */
    @ApiOperation(value = "导出付款大单列表", notes = "")
    @PostMapping("/exportPaymentFormExcel")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/付款单管理/导出付款大单列表")
    @ResponseBody
    public void exportPaymentFormExcel(HttpServletResponse response, @RequestBody PaymentFormDto paymentFormDto) {
        CommonUtil.ok(response);
        paymentFormService.exportPaymentFormExcel(paymentFormDto);
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [paymentFormItemVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/付款单管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody  @Validated(PaymentFormDto.Add.class)  PaymentFormDto paymentFormDto) {
        return SaveResponseBo.ok(paymentFormService.add(paymentFormDto));
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.PaymentFormItemDto>
     * @Description: 修改信息
     * @Param [paymentFormItemVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/付款单管理/更新")
    @PostMapping("update")
    public ResponseBo<PaymentFormVo> update(@RequestBody  @Validated(PaymentFormDto.Update.class)  PaymentFormDto paymentFormDto) {
        return UpdateResponseBo.ok(paymentFormService.update(paymentFormDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.PaymentFormItemDto>
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/付款单管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<PaymentFormVo> detail(@PathVariable("id") Long id) {
        PaymentFormVo data = paymentFormService.findPaymentFormById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/付款单管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        paymentFormService.delete(id);
        return DeleteResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 作废接口
     * @Param [id]
     * <AUTHOR>
     **/
    @ApiOperation(value = "作废接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/付款单管理/作废")
    @GetMapping("updateStatus/{id}")
    public ResponseBo updateStatus(@PathVariable("id") Long id) {
        Set<Long> ids = new HashSet<>();
        ids.add(id);
        paymentFormService.updateStatus(ids);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.MediaAndAttachedDto>
     * @Description: 附件列表
     * @Param [voSearchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "附件列表", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/付款单管理/查询附件")
    @PostMapping("getMedia")
    public ResponseBo<FMediaAndAttachedVo> getMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<FMediaAndAttachedVo> staffMedia = paymentFormService.getMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.MediaAndAttachedDto>
     * @Description: 附件保存
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "附件保存")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/付款单管理/附件保存接口")
    @PostMapping("upload")
    public ResponseBo<FMediaAndAttachedVo> addMedia(@RequestBody @Validated(MediaAndAttachedDto.Add.class)  ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(paymentFormService.addMedia(mediaAttachedVo));
    }

    /**
     * 目标类型下拉框数据
     *
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "目标类型下拉", notes = "")
    @GetMapping("findTypeKeySelect")
    public ResponseBo findTargetType() {
        List<Map<String, Object>> datas = paymentFormService.findTypeKeySelect();
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 目标对象下拉
     * @Param [tableName, companyId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "目标对象下拉", notes = "")
    @PostMapping("findTypeTargetSelect")
    public ResponseBo<BaseSelectEntity> findTypeTargetSelect(@RequestParam(value = "tableName") String tableName,
                                                             @RequestParam(value = "companyId") Long companyId) {
        return new ListResponseBo<>(paymentFormService.findTypeTargetSelect(tableName, companyId));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "目标对象下拉", notes = "")
    @PostMapping("getTargetSelectByName")
    public ResponseBo<BaseSelectEntity> getTargetSelectByName(@RequestParam(value = "tableName")String tableName,@RequestParam(value = "targetName") String targetName) {
        return new ListResponseBo<>(paymentFormService.getTargetSelectByName(tableName, targetName));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description : 编辑评论
     * @Param [commentVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑评论")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/付款单管理/编辑评论")
    @PostMapping("editComment")
    public ResponseBo editComment(@RequestBody @Validated(CommentDto.Add.class)  CommentDto commentDto) {
        return SaveResponseBo.ok(paymentFormService.editComment(commentDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AgentContractVo>
     * @Description : 评论列表数据
     * @Param [searchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "评论列表数据")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/付款单管理/查询评论")
    @PostMapping("getComments")
    public ResponseBo<FCommentVo> getComment(@RequestBody SearchBean<CommentDto> searchBean) {
        List<FCommentVo> datas = paymentFormService.getComments(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 修改付款单代理
     *
     * @Date 18:09 2022/12/21
     * <AUTHOR>
     */
    @ApiOperation(value = "修改付款单代理", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/付款单管理/修改付款单代理")
    @PostMapping("updatePaymentFormAgent")
    public ResponseBo updatePaymentFormAgent(@RequestBody PaymentFormAgentUpdateDto paymentFormAgentUpdateDto) {
        paymentFormService.updatePaymentFormAgent(paymentFormAgentUpdateDto);
        return SaveResponseBo.ok();
    }

    @ApiOperation(value = "修改付款单付款时间")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/付款单管理/修改付款单付款时间")
    @PostMapping("updatePaymentFormDate")
    public ResponseBo updatePaymentFormDate(@RequestBody PaymentFormDateDto paymentFormDateDto) {
        paymentFormService.updatePaymentFormDate(paymentFormDateDto);
        return SaveResponseBo.ok();
    }


}
