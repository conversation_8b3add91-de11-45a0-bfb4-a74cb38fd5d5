package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.log.model.LogLogin;
import com.get.financecenter.dto.query.PaymentApplicationFormQueryDto;
import com.get.financecenter.vo.PaymentApplicationFormVo;
import com.get.financecenter.entity.PaymentApplicationForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface PaymentApplicationFormMapper extends BaseMapper<PaymentApplicationForm> {

    List<PaymentApplicationFormVo> getPayFormByData(IPage page,
                                                    @Param("paymentApplicationFormVo") PaymentApplicationFormQueryDto paymentApplicationFormVo,
                                                    @Param("businessKeys") List<Long> businessKeys,
                                                    @Param("num") String num,
                                                    @Param("userId") String userId
    );

    Boolean getExistParentId(@Param("id") Long id);

    /**
     * 获取付款申请单总金额
     *
     * @param id
     * @return
     */
    BigDecimal getPaymentApplicationFormTotalAmount(@Param("id") Long id);
}