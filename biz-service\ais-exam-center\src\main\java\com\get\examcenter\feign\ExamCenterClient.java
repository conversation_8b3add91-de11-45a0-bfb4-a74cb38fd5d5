package com.get.examcenter.feign;

import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.examcenter.service.ExaminationService;
import com.get.examcenter.service.IExaminationQuestionService;
import com.get.examcenter.service.IQuestionTypeService;
import com.get.examcenter.service.UserService;
import com.get.salecenter.dto.DataCollectionQuestionDto;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Set;

@RestController
@AllArgsConstructor
@VerifyPermission(IsVerify = false)
public class  ExamCenterClient implements IExamCenterClient {

    private final ExaminationService examinationService;
    private final IQuestionTypeService questionTypeService;
    private final UserService userService;
    private final IExaminationQuestionService examinationQuestionService;

    @Override
    public Result<Map<Long, String>> getExaminationNamesByExaminationIds(Set<Long> examinationIds) {
        return Result.data(examinationService.getExaminationNamesByExaminationIds(examinationIds));
    }

    @Override
    public Result<Map<Long, String>> getNamesByQuestionTypeIds(Set<Long> questionTypeIds) {
        return Result.data(questionTypeService.getNamesByQuestionTypeIds(questionTypeIds));
    }

    @Override
    public Result<Set<Long>> getUserIdsByStaffIds(Set<Long> fkStaffIds) {
        return Result.data(userService.getUserIdsByStaffIds(fkStaffIds));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<List<DataCollectionQuestionDto>> getDataCollectionQuestions(String contactTel) {
        return Result.data(userService.getDataCollectionQuestions(contactTel));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<Boolean> saveDataCollectionQuestion(List<DataCollectionQuestionDto> dataCollectionQuestionList, String receiptCode, String contactTel) {
        return Result.data(examinationQuestionService.saveDataCollectionQuestion(dataCollectionQuestionList, receiptCode, contactTel));
    }

}
