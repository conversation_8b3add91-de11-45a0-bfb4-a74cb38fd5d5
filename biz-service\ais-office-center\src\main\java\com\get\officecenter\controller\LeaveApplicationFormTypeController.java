package com.get.officecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.officecenter.vo.LeaveApplicationFormTypeVo;
import com.get.officecenter.service.ILeaveApplicationFormTypeService;
import com.get.officecenter.dto.LeaveApplicationFormTypeCompanyDto;
import com.get.officecenter.dto.LeaveApplicationFormTypeDto;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/12 16:03
 * @verison: 1.0
 * @description:
 */
@Api(tags = "工休申请类型管理")
@RestController
@RequestMapping("office/leaveApplicationFormType")
public class LeaveApplicationFormTypeController {
    @Resource
    private ILeaveApplicationFormTypeService leaveApplicationFormTypeService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.officecenter.vo.LeaveApplicationFormTypeVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DETAIL, description = "办公中心/工休申请类型管理/工休申请类型详情")
    @GetMapping("/{id}")
    public ResponseBo<LeaveApplicationFormTypeVo> detail(@PathVariable("id") Long id) {
        LeaveApplicationFormTypeVo data = leaveApplicationFormTypeService.findLeaveApplicationFormTypeById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :批量新增信息
     * @Param [leaveApplicationFormTypeDtos]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "办公中心/工休申请类型管理/新增工休申请类型")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(LeaveApplicationFormTypeDto.Add.class) ValidList<LeaveApplicationFormTypeDto> leaveApplicationFormTypeDtos) {
        leaveApplicationFormTypeService.batchAdd(leaveApplicationFormTypeDtos);
        return SaveResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DELETE, description = "办公中心/工休申请类型管理/删除工休申请类型")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        leaveApplicationFormTypeService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.officecenter.vo.LeaveApplicationFormTypeVo>
     * @Description :修改信息
     * @Param [leaveApplicationFormTypeDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.EDIT, description = "办公中心/工休申请类型管理/更新工休申请类型")
    @PostMapping("update")
    public ResponseBo<LeaveApplicationFormTypeVo> update(@RequestBody @Validated(LeaveApplicationFormTypeDto.Update.class) LeaveApplicationFormTypeDto leaveApplicationFormTypeDto) {
        return UpdateResponseBo.ok(leaveApplicationFormTypeService.updateLeaveApplicationFormType(leaveApplicationFormTypeDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.officecenter.vo.LeaveApplicationFormTypeVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/工休申请类型管理/查询工休申请类型")
    @PostMapping("datas")
    public ResponseBo<LeaveApplicationFormTypeVo> datas(@RequestBody SearchBean<LeaveApplicationFormTypeDto> page) {
        List<LeaveApplicationFormTypeVo> datas = leaveApplicationFormTypeService.getLeaveApplicationFormTypes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :上移下移
     * @Param [leaveApplicationFormTypeDtos]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.EDIT, description = "办公中心/工休申请类型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<LeaveApplicationFormTypeDto> leaveApplicationFormTypeDtos) {
        leaveApplicationFormTypeService.movingOrder(leaveApplicationFormTypeDtos);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.officecenter.vo.LeaveApplicationFormTypeVo>
     * @Description :工休申请类型下拉框数据
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "工休申请类型下拉框数据", notes = "")
    @GetMapping("getLeaveApplicationFormTypeList")
    public ResponseBo<LeaveApplicationFormTypeVo> getLeaveApplicationFormTypeList(@RequestParam(value = "companyId",required = false)Long companyId) {
        List<LeaveApplicationFormTypeVo> leaveApplicationFormTypeList = leaveApplicationFormTypeService.getLeaveApplicationFormTypeSelect(companyId);
        return new ListResponseBo<>(leaveApplicationFormTypeList);
    }


    @ApiOperation(value = "工休申请类型-公司安全配置")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.ADD, description = "办公中心/工休申请类型管理/安全配置")
    @PostMapping("editLeaveApplicationFormTypeCompanyRelation")
    public ResponseBo editLeaveApplicationFormTypeCompanyRelation(@RequestBody @Validated(LeaveApplicationFormTypeCompanyDto.Add.class) ValidList<LeaveApplicationFormTypeCompanyDto> leaveApplicationFormTypeCompanyDtos) {
        leaveApplicationFormTypeService.editLeaveApplicationFormTypeCompanyRelation(leaveApplicationFormTypeCompanyDtos);
        return UpdateResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.CompanyTreeVo>
     * @Description: 回显工休申请类型和公司的关系
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "回显工休申请类型和公司的关系", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DETAIL, description = "办公中心/工休申请类型管理/工休申请类型和公司的关系（数据回显）")
    @GetMapping("getLeaveApplicationFormTypeCompanyRelation/{leaveApplicationFormTypeId}")
    public ResponseBo<CompanyTreeVo> getLeaveApplicationFormTypeCompanyRelation(@PathVariable("leaveApplicationFormTypeId") Long id) {
        List<CompanyTreeVo> leaveApplicationFormTypeCompanyRelation = leaveApplicationFormTypeService.getLeaveApplicationFormTypeCompanyRelation(id);
        return new ListResponseBo<>(leaveApplicationFormTypeCompanyRelation);
    }
}
