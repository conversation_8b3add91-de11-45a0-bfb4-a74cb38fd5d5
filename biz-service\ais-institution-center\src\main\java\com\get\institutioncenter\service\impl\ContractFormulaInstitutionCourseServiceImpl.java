package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.ContractFormulaInstitutionCourseMapper;
import com.get.institutioncenter.dto.ContractFormulaInstitutionCourseDto;
import com.get.institutioncenter.entity.ContractFormulaInstitutionCourse;
import com.get.institutioncenter.service.IContractFormulaInstitutionCourseService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/22 14:07
 * @verison: 1.0
 * @description:
 */
@Service
public class ContractFormulaInstitutionCourseServiceImpl extends BaseServiceImpl<ContractFormulaInstitutionCourseMapper, ContractFormulaInstitutionCourse> implements IContractFormulaInstitutionCourseService {
    @Resource
    private ContractFormulaInstitutionCourseMapper contractFormulaInstitutionCourseMapper;
    @Resource
    private UtilService utilService;

    @Override
    public Long addContractFormulaInstitutionCourse(ContractFormulaInstitutionCourseDto contractFormulaInstitutionCourseDto) {
        if (GeneralTool.isEmpty(contractFormulaInstitutionCourseDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ContractFormulaInstitutionCourse contractFormulaInstitutionCourse = BeanCopyUtils.objClone(contractFormulaInstitutionCourseDto, ContractFormulaInstitutionCourse::new);
        utilService.updateUserInfoToEntity(contractFormulaInstitutionCourse);
        contractFormulaInstitutionCourseMapper.insertSelective(contractFormulaInstitutionCourse);
        return contractFormulaInstitutionCourse.getId();
    }

    @Override
    public void deleteByFkid(Long contractFormulaId) {
        LambdaQueryWrapper<ContractFormulaInstitutionCourse> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContractFormulaInstitutionCourse::getFkContractFormulaId, contractFormulaId);
        contractFormulaInstitutionCourseMapper.delete(wrapper);
    }

    @Override
    public List<Long> getCourseIdListByFkid(Long contractFormulaId) {
        return contractFormulaInstitutionCourseMapper.getCourseIdListByFkid(contractFormulaId);
    }

    @Override
    public String getCourseNameByFkid(Long contractFormulaId) {
        List<String> courseNameList = contractFormulaInstitutionCourseMapper.getCourseNameByFkid(contractFormulaId);
        String result = "";
        if (GeneralTool.isNotEmpty(courseNameList)) {
            result = StringUtils.join(courseNameList, ",");
        }
        return result;
    }

    /**
     * 根据合同公式筛选课程
     *
     * @Date 14:39 2021/6/15
     * <AUTHOR>
     */
    @Override
    public List<Long> getCourseIdListByFaculty(List<Long> courseIdList, List<Long> zoneIdList, List<Long> facultyIdList, List<Long> majorLevelIdList, List<Long> courseTypeIdList) {
        return contractFormulaInstitutionCourseMapper.getCourseIdListByFaculty(courseIdList, zoneIdList, facultyIdList, majorLevelIdList, courseTypeIdList);
    }
}
