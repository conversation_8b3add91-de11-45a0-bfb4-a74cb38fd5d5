package com.get.aisplatformcenter.controller;

import com.get.aisplatformcenter.service.MFeedbackOrderService;
import com.get.aisplatformcenterap.dto.work.MFeedbackOrderDto;
import com.get.aisplatformcenterap.dto.work.MFeedbackOrderReplyDto;
import com.get.aisplatformcenterap.vo.work.MFeedbackOrderDetailVo;
import com.get.aisplatformcenterap.vo.work.MFeedbackOrderVo;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "反馈-后端管理")
@RestController
@RequestMapping("/platform/mFeedbackOrder")
public class MFeedbackOrderController {

    @Resource
    private MFeedbackOrderService mFeedbackOrderService;

    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.LIST, description = "华通伙伴/反馈信息/列表查询")
    @PostMapping("searchPage")
    public ResponseBo<MFeedbackOrderVo> searchPage(@RequestBody  @Validated  SearchBean<MFeedbackOrderDto> page) {
        List<MFeedbackOrderVo> datas = mFeedbackOrderService.searchPage(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.DETAIL, description = "华通伙伴/反馈/详情")
    @GetMapping("/getDetail/{id}")
    public ResponseBo<MFeedbackOrderDetailVo> getDetail(@PathVariable Long id){
        MFeedbackOrderDetailVo data=mFeedbackOrderService.getDetail(id);
        return new ResponseBo<>(data);
    }

    @ApiOperation(value = "反馈回复" , notes = "反馈回复" )
    @PostMapping("/feedDack" )
    public ResponseBo feedDack(@RequestBody  @Validated MFeedbackOrderReplyDto replydto) {
        mFeedbackOrderService.feedDack(replydto);
        return SaveResponseBo.ok();
    }



    @ApiOperation(value = "反馈解决" , notes = "反馈解决" )
    @GetMapping("/solve/{id}" )
    public ResponseBo solve(@PathVariable Long id) {
        mFeedbackOrderService.solve(id);
        return SaveResponseBo.ok();
    }

    @ApiOperation(value = "反馈类型查询" , notes = "反馈类型查询" )
    @GetMapping("/getUFeedbackOrderType" )
    public ResponseBo getUFeedbackOrderType() {
        return new ResponseBo<>(mFeedbackOrderService.getUFeedbackOrderType());
    }

}
