package com.get.filecenter.controller;

import com.get.common.annotion.RequestType;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.service.IFileService;
import com.get.filecenter.service.ITencentCloudService;
import com.get.filecenter.vo.FileVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "文件管理")
@RestController
@RequestMapping("file")
public class FileCenterController {
    @Resource
    private IFileService fileService;
    @Resource
    private ITencentCloudService tencentCloudService;

    @VerifyPermission(IsVerify = false)
    @RequestType(IsRequest = false)
    @ApiOperation(value = "检查是否登录", notes = "")
    @PostMapping(value = "isLogin")
    public ResponseBo isLogin() {
        return new ResponseBo<>();
    }

    /**
     * 上传文件
     *
     * @param files
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "上传文件接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FILECENTER, type = LoggerOptTypeConst.ADD, description = "文件中心/上传文件")
    @PostMapping(value = "upload")
    public ResponseBo upload(@RequestParam("files") MultipartFile[] files, @RequestParam("type") String type) {
        return new ListResponseBo<>(this.fileService.upload(files, type));
    }


    /**
     * 上传附件
     *
     * @param files
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "上传附件接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FILECENTER, type = LoggerOptTypeConst.ADD, description = "文件中心/上传附件")
    @PostMapping(value = "uploadAppendix")
    public ResponseBo uploadAppendix(@RequestParam("files") MultipartFile[] files, @RequestParam("type") String type) {
        return new ListResponseBo<>(this.fileService.uploadAppendix(files, type));
    }


    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "上传HTI公开桶附件接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FILECENTER, type = LoggerOptTypeConst.ADD, description = "文件中心/上传HTI公开桶附件接口")
    @PostMapping(value = "uploadHtiPublicFile")
    public ResponseBo uploadHtiPublicFile(@RequestParam("files") MultipartFile[] files, @RequestParam("type") String type) {
        return new ListResponseBo<>(this.fileService.uploadHtiPublicFile(files, type, null, null));
    }

    /**
     * feign上传文件
     *
     * @param files
     * @return
     */
    @ApiIgnore
    @VerifyLogin(IsVerify = false)
    @PostMapping(value = "feignUploadAppendix")
    public List<FileDto> feignUploadAppendix(@RequestParam("files") MultipartFile[] files, @RequestParam("type") String type) {
        return fileService.uploadAppendix(files, type).stream().map(fileDto -> BeanCopyUtils.objClone(fileDto, FileDto::new)).collect(Collectors.toList());
    }

    /**
     * 上传图片
     *
     * @param files
     * @return
     * @throws YException
     */
 /*   @ApiOperation(value = "上传图片接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FILECENTER, type = LoggerOptTypeConst.ADD, description = "文件中心/上传图片")
    @PostMapping(value = "uploadimage")
    public ResponseBo uploadimage(@RequestParam("files") MultipartFile[] files, @RequestParam("type") String type) {
        return new ListResponseBo<>(this.fileService.uploadImage(files, type));
    }*/

    /**
     * @param guidList
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "查询文件接口")
    @OperationLogger(module = LoggerModulesConsts.FILECENTER, type = LoggerOptTypeConst.ADD, description = "文件中心/获取文件")
    @PostMapping(value = "getFileByGuid")
    public ResponseBo getFile(@RequestParam("guidList") @NotNull(message = "guid不能为空") List<String> guidList,
                              @NotBlank(message = "type不能为空") @RequestParam("type") String type) {
        return new ListResponseBo<>(fileService.findFileByGuid(guidList, type));
    }

    /**
     * feign 根据guid tyoe 查询文件
     *
     * @Date 17:32 2021/11/30
     * <AUTHOR>
     */
    @VerifyLogin(IsVerify = false)
    @PostMapping(value = "getFileByGuidFeign")
    public List<FileDto> getFileByGuidFeign(@RequestParam("guidList") @NotNull(message = "guid不能为空") List<String> guidList,
                                            @NotBlank(message = "type不能为空") @RequestParam("type") String type) {
        List<FileDto> fileByGuid = fileService.findFileByGuid(guidList, type);
        return fileByGuid.stream().map(fileDto -> BeanCopyUtils.objClone(fileDto, FileDto::new)).collect(Collectors.toList());
    }

    //
//    /**
//     *
//     * @param
//     * @return
//     */
//    @ApiOperation(value = "删除文件")
//    @OperationLogger(module = LoggerModulesConsts.FILECENTER, type = LoggerOptTypeConst.DELETE, description = "文件中心/删除文件")
//    @PostMapping(value = "delete")
//    public ResponseBo delete(@RequestParam("fileVo") @NotNull(message = "fileVo不能为空") FileVo fileVo){
//        ResponseBo responseBo = new ResponseBo();
//        responseBo.put("data", fileService.delete(fileVo));
//        return responseBo;
//    }
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学校下载文件接口-GET共享私密桶")
    @OperationLogger(module = LoggerModulesConsts.FILECENTER, type = LoggerOptTypeConst.ADD, description = "文件中心/下载文件-私密桶")
    @PostMapping("downloadByShare")
    public void downloadByShare(HttpServletResponse response, @RequestBody FileVo fileVo) {
        //设置文件路径
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        this.tencentCloudService.downLoadObject(fileVo, response,false,true,false);
    }

    @ApiOperation(value = "下载文件接口-私密桶")
    @OperationLogger(module = LoggerModulesConsts.FILECENTER, type = LoggerOptTypeConst.ADD, description = "文件中心/下载文件-私密桶")
    @PostMapping("download")
    public void download(HttpServletResponse response, @RequestBody FileVo fileVo) {
        //设置文件路径
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        this.tencentCloudService.downLoadObject(fileVo, response,false,false,false);
    }

    @ApiOperation(value = "下载文件接口-公开桶")
    @OperationLogger(module = LoggerModulesConsts.FILECENTER, type = LoggerOptTypeConst.ADD, description = "文件中心/下载文件-公开桶")
    @PostMapping("downloadByPublic")
    @VerifyPermission(IsVerify = false)
    public void downloadByPublic(HttpServletResponse response, @RequestBody FileVo fileVo) {
        //设置文件路径
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        this.tencentCloudService.downLoadObject(fileVo, response,true,false,false);
    }

    @ApiOperation(value = "下载文件接口-Hti公开桶")
    @OperationLogger(module = LoggerModulesConsts.FILECENTER, type = LoggerOptTypeConst.ADD, description = "文件中心/下载文件-Hti公开桶")
    @PostMapping("downloadByHtiPublic")
    @VerifyPermission(IsVerify = false)
    public void downloadByHtiPublic(HttpServletResponse response, @RequestBody FileVo fileVo) {
        //设置文件路径
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        this.tencentCloudService.downLoadObject(fileVo, response,true,false,true);
    }

    @ApiOperation(value = "同步文件到腾讯云")
    @PostMapping("synchronizeFiles")
    public ResponseBo synchronizeFiles() throws IOException {
        tencentCloudService.synchronizeFiles();
        return ResponseBo.ok();
    }

/*    @ApiOperation(value = "腾讯云下载文件接口")
    @OperationLogger(module = LoggerModulesConsts.FILECENTER, type = LoggerOptTypeConst.ADD, description = "文件中心/下载文件")
    @PostMapping("clouddownload" )
    public void clouddownload(HttpServletResponse response, @RequestBody FileVo fileVo) {
        //设置文件路径
        response.setHeader("content-type", "application/octet-stream" );
        response.setContentType("application/octet-stream" );
        this.tencentCloudService.downLoadObject(fileVo, response);
    }*/

    @ApiIgnore
    @ApiOperation(value = "下载文件测试接口")
    @OperationLogger(module = LoggerModulesConsts.FILECENTER, type = LoggerOptTypeConst.ADD, description = "文件中心/下载文件测试接口")
    @PostMapping("downloadDemo")
    public OutputStream downloadDemo(HttpServletResponse response, @RequestParam String path, @RequestParam String url) {
        //设置文件路径
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        return fileService.downloadDemo(response, path, url);
    }

    }
