package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.InstitutionProviderAreaCountryMapper;
import com.get.institutioncenter.vo.InstitutionProviderAreaCountryVo;
import com.get.institutioncenter.entity.InstitutionProviderAreaCountry;
import com.get.institutioncenter.service.IInstitutionProviderAreaCountryService;
import com.get.institutioncenter.dto.InstitutionProviderAreaCountryDto;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2021/3/29 10:16
 * @verison: 1.0
 * @description:
 */
@Service
public class InstitutionProviderAreaCountryServiceImpl extends BaseServiceImpl<InstitutionProviderAreaCountryMapper, InstitutionProviderAreaCountry> implements IInstitutionProviderAreaCountryService {
    @Resource
    private InstitutionProviderAreaCountryMapper institutionProviderAreaCountryMapper;
    @Resource
    private UtilService utilService;

    @Override
    public List<InstitutionProviderAreaCountryVo> getInstitutionProviderAreaCountrySelect(Long institutionProviderId) {
        return institutionProviderAreaCountryMapper.getInstitutionProviderAreaCountrySelect(institutionProviderId);
    }

    @Override
    public void addProviderAreaCountry(InstitutionProviderAreaCountryDto providerAreaCountryVo) {
        InstitutionProviderAreaCountry areaCountry = BeanCopyUtils.objClone(providerAreaCountryVo, InstitutionProviderAreaCountry::new);
        utilService.updateUserInfoToEntity(areaCountry);
        institutionProviderAreaCountryMapper.insertSelective(areaCountry);
    }

    @Override
    public void deleteByProviderId(Long providerId) {
        LambdaQueryWrapper<InstitutionProviderAreaCountry> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionProviderAreaCountry::getFkInstitutionProviderId, providerId);
        institutionProviderAreaCountryMapper.delete(wrapper);
    }

    @Override
    public String getAreaCountryNameByProviderId(Long providerId) {
        if (GeneralTool.isEmpty(providerId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        return institutionProviderAreaCountryMapper.getAreaCountryNameByProviderId(providerId);
    }

    @Override
    public List<Long> getAreaCountryIdByProviderId(Long providerId) {
        LambdaQueryWrapper<InstitutionProviderAreaCountry> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionProviderAreaCountry::getFkInstitutionProviderId, providerId);
        List<InstitutionProviderAreaCountry> providerAreaCountries = institutionProviderAreaCountryMapper.selectList(wrapper);
        return providerAreaCountries.stream().map(InstitutionProviderAreaCountry::getFkAreaCountryId).collect(Collectors.toList());
    }

    @Override
    public List<Long> getProviderIds(List<Long> areaCountryIds) {
        if (GeneralTool.isEmpty(areaCountryIds)) {
            areaCountryIds = SecureUtil.getCountryIds();
        }
        if (GeneralTool.isEmpty(areaCountryIds)) {
            return Lists.newArrayList();
        }
        //todo 没有对业务国家进行过滤，怀疑有问题
        LambdaQueryWrapper<InstitutionProviderAreaCountry> wrapper = new LambdaQueryWrapper();
        wrapper.in(InstitutionProviderAreaCountry::getFkAreaCountryId, areaCountryIds);
        List<InstitutionProviderAreaCountry> providerAreaCountries = institutionProviderAreaCountryMapper.selectList(wrapper);
        return providerAreaCountries.stream().map(InstitutionProviderAreaCountry::getFkInstitutionProviderId).collect(Collectors.toList());
    }
}
