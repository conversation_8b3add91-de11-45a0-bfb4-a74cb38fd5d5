package com.get.remindercenter.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.remindercenter.vo.RemindTaskQueueVo;
import com.get.remindercenter.entity.RemindTaskQueue;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface RemindTaskQueueMapper extends BaseMapper<RemindTaskQueue> {
    int insert(RemindTaskQueue record);

    int insertSelective(RemindTaskQueue record);

    int updateByPrimaryKeySelective(RemindTaskQueue record);

    int updateByPrimaryKey(RemindTaskQueue record);


    /**
     * @Description: 获取当前时间大于执行任务时间的数据
     * @Author: Jerry
     * @Date:14:34 2021/11/15
     */
    List<RemindTaskQueue> getRemindTaskQueues(@Param("nowDate") Date nowDate);

    /**
     * @Description: 获取系统内消息
     * @Author: Jerry
     * @Date:15:37 2021/12/2
     */
    @DS("reminderdb-doris")
    List<RemindTaskQueueVo> getSystemMessage(@Param("nowDate") Date nowDate, @Param("fkStaffId") Long fkStaffId);

    Long getStaffContractCompanyId(@Param("fkTableId") Long fkTableId);

    Boolean getStaffContractIsOnDuty(@Param("fkTableId")Long fkTableId);
}