package com.get.core.log.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: Sea
 * @create: 2020/6/22 10:45
 * @verison: 1.0
 * @description:
 */
@Data
public class LogOperationVo extends BaseVoEntity {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;

    /**
     * 登陆用户Id
     */
    @ApiModelProperty(value = "登陆用户Id")
    private String staffLoginId;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String staffName;

    /**
     * 操作编号
     */
    @ApiModelProperty(value = "操作编号")
    private String optCode;

    /**
     * 操作模块名称
     */
    @ApiModelProperty(value = "操作模块名称")
    private String optModuleName;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private String optType;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    //自定义内容
    /**
     * 操作开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "操作开始时间")
    private Date logOperationStartTime;

    /**
     * 操作结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "操作结束时间")
    private Date logOperationEndTime;

    /**
     * 登录账号
     */
    @ApiModelProperty(value = "登录账号")
    private String gmtCreateUser;
}
