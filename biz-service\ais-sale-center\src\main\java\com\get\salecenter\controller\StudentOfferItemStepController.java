package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.OfferItemFileConfigVo;
import com.get.salecenter.vo.OfferItemLimitConfigVo;
import com.get.salecenter.vo.StudentOfferItemStepVo;
import com.get.salecenter.entity.StudentOfferItemStep;
import com.get.salecenter.service.IStudentOfferItemStepService;
import com.get.salecenter.dto.StudentOfferItemStepDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @author: Sea
 * @create: 2020/11/3 11:33
 * @verison: 1.0
 * @description:
 */
@Api(tags = "学生申请方案项目状态步骤管理")
@RestController
@RequestMapping("sale/studentOfferItemStep")
public class StudentOfferItemStepController {
    @Resource
    private IStudentOfferItemStepService studentOfferItemStepService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentOfferItemStepVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生申请方案项目状态步骤管理/学生申请方案项目状态步骤详情")
    @GetMapping("/{id}")
    public ResponseBo<StudentOfferItemStepVo> detail(@PathVariable("id") Long id) {
        StudentOfferItemStepVo data = studentOfferItemStepService.findStudentOfferItemStepById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :批量新增信息
     * @Param [studentOfferItemStepDtos]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生申请方案项目状态步骤管理/新增学生申请方案项目状态步骤")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody  @Validated(StudentOfferItemStepDto.Add.class) ValidList<StudentOfferItemStepDto> studentOfferItemStepDtos) {
        studentOfferItemStepService.batchAdd(studentOfferItemStepDtos);
        return SaveResponseBo.ok();
    }

    @ApiOperation(value = "获取步骤文件类型配置", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生申请方案项目状态步骤管理/获取步骤文件类型配置")
    @GetMapping("getFileConfig")
    public ResponseBo<OfferItemFileConfigVo> getFileConfig(@RequestParam("fkStudentOfferItemStepId") Long fkStudentOfferItemStepId) {
        return new ResponseBo<>(studentOfferItemStepService.getFileConfig(fkStudentOfferItemStepId));
    }


    @ApiOperation(value = "获取步骤校验配置", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生申请方案项目状态步骤管理/获取步骤校验配置")
    @GetMapping("getItemStepLimtit")
    public ResponseBo<OfferItemLimitConfigVo> getItemStepLimtit(@RequestParam(value = "fkStudentOfferItemStepId",required = false) Long fkStudentOfferItemStepId, @RequestParam(value = "fkStudentOfferItemId",required = false) Long fkStudentOfferItemId) {
        return new ResponseBo<>(studentOfferItemStepService.getItemStepLimtit(fkStudentOfferItemStepId,fkStudentOfferItemId));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生申请方案项目状态步骤管理/删除学生申请方案项目状态步骤")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        studentOfferItemStepService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentOfferItemStepVo>
     * @Description :修改信息
     * @Param [studentOfferItemStepDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案项目状态步骤管理/更新学生申请方案项目状态步骤")
    @PostMapping("update")
    public ResponseBo<StudentOfferItemStepVo> update(@RequestBody @Validated(StudentOfferItemStepDto.Update.class) StudentOfferItemStepDto studentOfferItemStepDto) {
        return UpdateResponseBo.ok(studentOfferItemStepService.updateStudentOfferItemStep(studentOfferItemStepDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentOfferItemStepVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "stepName步骤名称")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生申请方案项目状态步骤管理/查询学生申请方案项目状态步骤")
    @PostMapping("datas")
    public ResponseBo<StudentOfferItemStepVo> datas(@RequestBody SearchBean<StudentOfferItemStepDto> page) {
        List<StudentOfferItemStepVo> datas = studentOfferItemStepService.getStudentOfferItemSteps(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description:步骤前置下拉
     * @Param [itemStepId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "步骤前置下拉", notes = "当前步骤Id")
    @PostMapping("getItemStepPrecondition")
    public ResponseBo<BaseSelectEntity> getItemStepPrecondition(@RequestParam("itemStepId") Long itemStepId) {
        return new ListResponseBo<>(studentOfferItemStepService.getItemStepPrecondition(itemStepId));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :上移下移
     * @Param [studentOfferItemStepDtos]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案项目状态步骤管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<StudentOfferItemStepDto> studentOfferItemStepDtos) {
        studentOfferItemStepService.movingOrder(studentOfferItemStepDtos);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 学生申请方案下拉
     * @Param [providerId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学生申请方案项目步骤下拉", notes = "")
    @PostMapping("getStudentOfferItemStepSelect")
    public ResponseBo<BaseSelectEntity> getStudentOfferItemSelect() {
        return new ListResponseBo<>(studentOfferItemStepService.getItemStepSelect());
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description:学生申请方案项目未执行步骤下拉
     * @Param [fkStudentOfferItemId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学生申请方案项目未执行步骤下拉", notes = "fkStudentOfferItemId为学生申请方案学习计划Id")
    @PostMapping("getUnexecutedItemStep")
    public ResponseBo<BaseSelectEntity> getUnexecutedItemStep(@RequestParam("fkStudentOfferItemId") Long fkStudentOfferItemId, @RequestParam("fkStudentOfferId") Long fkStudentOfferId) {
        return new ListResponseBo<>(studentOfferItemStepService.getUnexecutedItemStep(fkStudentOfferItemId, fkStudentOfferId));
    }

//    /**
//     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
//     * @Description:学生申请方案项目未执行步骤下拉（无权限）
//     * @Param [fkStudentOfferItemId]
//     * <AUTHOR>
//     */
//    @ApiOperation(value = "学生申请方案项目未执行步骤下拉（无权限）", notes = "fkStudentOfferItemId为学生申请方案学习计划Id")
//    @PostMapping("getUnexecutedItemStepNoPermission")
//    public ResponseBo<BaseSelectEntity> getUnexecutedItemStepNoPermission(@RequestParam("fkStudentOfferItemId") Long fkStudentOfferItemId) {
//        return new ListResponseBo<>(studentOfferItemStepService.getUnexecutedItemStepWhitoutPermission(fkStudentOfferItemId));
//    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "申请计划是否存在提供商或者渠道")
    @GetMapping("hasChannelOrProvider")
    public ResponseBo hasChannelOrProvider(@RequestParam("fkStudentOfferItemId") Long fkStudentOfferItemId){
        return new ResponseBo(studentOfferItemStepService.hasChannelOrProvider(fkStudentOfferItemId));
    }

    @ApiOperation(value = "enrolled步骤入学条件限制(凭证上传是否必传)")
    @GetMapping("stepEnrolledLimit")
    public ResponseBo<Map> stepEnrolledLimit(){
        return studentOfferItemStepService.stepEnrolledLimit();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 学生申请方案下拉
     * @Param [providerId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "成功客户列表项目步骤下拉", notes = "")
    @PostMapping("getSuccessfulCustomerStepSelect")
    public ResponseBo<BaseSelectEntity> getSuccessfulCustomerStepSelect(@RequestParam("fkCompanyId") Long fkCompanyId) {
        return new ListResponseBo<>(studentOfferItemStepService.getSuccessfulCustomerStepSelect(fkCompanyId));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "失败客户列表项目步骤下拉", notes = "")
    @PostMapping("getFailureCustomerStepSelect")
    public ResponseBo<BaseSelectEntity> getFailureCustomerStepSelect(@RequestParam("fkCompanyId") Long fkCompanyId) {
        return new ListResponseBo<>(studentOfferItemStepService.getFailureCustomerStepSelect(fkCompanyId));
    }

    //获取后置步骤
    @ApiOperation(value = "获取后置步骤", notes = "")
    @PostMapping("getItemStepPostpositionByStepId")
    public ResponseBo<StudentOfferItemStep> getItemStepPostpositionByStepId(@RequestParam("itemStepId") Long itemStepId) {
        return new ListResponseBo<>(studentOfferItemStepService.getItemStepPostpositionByStepId(itemStepId));
    }

    @ApiOperation(value = "获取COE后置步骤", notes = "")
    @GetMapping("getItemStepPostpositionCOE")
    public ResponseBo<StudentOfferItemStep> getItemStepPostpositionCOE() {
        return new ListResponseBo<>(studentOfferItemStepService.getItemStepPostpositionCOE());
    }


}
