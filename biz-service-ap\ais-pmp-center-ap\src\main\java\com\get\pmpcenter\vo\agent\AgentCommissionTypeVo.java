package com.get.pmpcenter.vo.agent;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AgentCommissionTypeVo {
    @ApiModelProperty
    private Long id;

    @ApiModelProperty(value = "公司Id（每个区域分公司都有自己的分类及命名）")
    private Long fkCompanyId;

    @ApiModelProperty(value = "公司名 简称")
    private String companyName;

    @ApiModelProperty(value = "分类名称，如：1级代理，2级代理")
    private String typeName;

    @ApiModelProperty(value = "佣金比例")
    private BigDecimal commissionRate;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否激活：0否/1是，当设置屏蔽后，其绑定对应的佣金模板也会失效")
    private Integer isActive;

    @ApiModelProperty(value = "创建人")
    private String gmtCreateUser;

    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date gmtCreate;

    @ApiModelProperty(value = "是否只显示等级佣金：0否/1是")
    private Integer isShowOnly;

    @ApiModelProperty(value = "默认佣金比例，按国家线分")
    private String commissionRateDefault ;



}
