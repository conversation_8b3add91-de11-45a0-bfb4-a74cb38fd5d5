package com.get.salecenter.vo;

import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.ConventionSponsor;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/5/8 11:22
 * @verison: 1.0
 * @description:
 */
@Data
public class ConventionSponsorVo extends BaseEntity {
    /**
     * 所选赞助类型对象
     */
    @ApiModelProperty(value = "所选赞助类型对象")
    private List<ConventionSponsorFeeVo> conventionSponsorFeeDtoList;

    /**
     * 学校提供商名称
     */
    @ApiModelProperty(value = "学校提供商名称")
    private String institutionProviderName;

    /**
     * 报名名称机构名称
     */
    @ApiModelProperty(value = "报名名称机构名称")
    private String providerName;

    /**
     * 赞助类型总数
     */
    @ApiModelProperty(value = "赞助类型总数")
    private Integer total;

    /**
     * 已选赞助类型数
     */
    @ApiModelProperty(value = "已选赞助类型数")
    private Integer selectNum;

    /**
     * 合计折合人民币
     */
    @ApiModelProperty(value = "合计折合人民币")
    private BigDecimal sumFeeCny;

    /**
     * 费用金额
     */
    @ApiModelProperty(value = "费用金额(带币种）")
    private String feeOtherCurrency;

   /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String currencyTypeName;

    /**
     * 类型名称
     */
    @ApiModelProperty(value = "赞助费用类型")
    private String typeName;

    /**
     * 费用金额
     */
    @ApiModelProperty(value = "总费用金额")
    private BigDecimal sumFee;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "总费用币种")
    private String fkCurrencyTypeNumFee;

    @ApiModelProperty(value = "归口金额")
    private BigDecimal eventCostAmount;

    @ApiModelProperty(value = "归口金额币种编号")
    private String eventCostAmountCurrencyNum;

    @ApiModelProperty(value = "归口金额币种编号名称")
    private String eventCostAmountCurrencyNumName;

    //==============实体类ConventionSponsor====================
    private static final long serialVersionUID = 1L;
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    @Column(name = "fk_convention_id")
    private Long fkConventionId;
    /**
     * 学校提供商Id（费用归口）
     */
    @ApiModelProperty(value = "学校提供商Id（费用归口）")
    @Column(name = "fk_institution_provider_id")
    private Long fkInstitutionProviderId;

    /**
     * 活动费用绑定Id
     */
    @UpdateWithNull
    @ApiModelProperty(value = "活动费用绑定Id")
    @Column(name = "fk_event_cost_id")
    private Long fkEventCostId;

    /**
     * 赞助商名称
     */
    @ApiModelProperty(value = "赞助商名称")
    @Column(name = "sponsor_name")
    private String sponsorName;
    /**
     * 回执码，8位数字随机数(和报名一致)
     */
    @ApiModelProperty(value = "回执码，8位数字随机数(和报名一致)")
    @Column(name = "receipt_code")
    private String receiptCode;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;

    /**
     * 费用金额
     */
    @ApiModelProperty(value = "费用金额")
    @Column(name = "fee_other")
    private BigDecimal feeOther;

    /**
     * 费用金额（折合人民币）
     */
    @ApiModelProperty(value = "费用金额（折合人民币）")
    @Column(name = "fee_other_cny")
    private BigDecimal feeOtherCny;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    /**
     * 状态：0新建/1已确认/2已收款
     */
    @ApiModelProperty(value = "状态：0新建/1已确认/2已收款")
    @Column(name = "status")
    private Integer status;

}
