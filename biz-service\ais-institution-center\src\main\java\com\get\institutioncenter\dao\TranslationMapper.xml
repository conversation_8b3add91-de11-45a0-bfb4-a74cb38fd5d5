<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.TranslationMapper">
  <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.InstitutionTranslation">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_table_name" jdbcType="VARCHAR" property="fkTableName" />
    <result column="fk_table_id" jdbcType="BIGINT" property="fkTableId" />
    <result column="fk_translation_mapping_id" jdbcType="BIGINT" property="fkTranslationMappingId" />
    <result column="language_code" jdbcType="VARCHAR" property="languageCode" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.get.institutioncenter.entity.InstitutionTranslation">
    <result column="translation" jdbcType="LONGVARCHAR" property="translation" />
  </resultMap>
  <sql id="Blob_Column_List">
    translation
  </sql>
  <insert id="insert" parameterType="com.get.institutioncenter.entity.InstitutionTranslation">
    insert into s_translation (id, fk_table_name, fk_table_id, 
      fk_translation_mapping_id, language_code, gmt_create, 
      gmt_create_user, gmt_modified, gmt_modified_user, 
      translation)
    values (#{id,jdbcType=BIGINT}, #{fkTableName,jdbcType=VARCHAR}, #{fkTableId,jdbcType=BIGINT}, 
      #{fkTranslationMappingId,jdbcType=BIGINT}, #{languageCode,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR}, 
      #{translation,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionTranslation">
    insert into s_translation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkTableName != null">
        fk_table_name,
      </if>
      <if test="fkTableId != null">
        fk_table_id,
      </if>
      <if test="fkTranslationMappingId != null">
        fk_translation_mapping_id,
      </if>
      <if test="languageCode != null">
        language_code,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
      <if test="translation != null">
        translation,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkTableName != null">
        #{fkTableName,jdbcType=VARCHAR},
      </if>
      <if test="fkTableId != null">
        #{fkTableId,jdbcType=BIGINT},
      </if>
      <if test="fkTranslationMappingId != null">
        #{fkTranslationMappingId,jdbcType=BIGINT},
      </if>
      <if test="languageCode != null">
        #{languageCode,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
      <if test="translation != null">
        #{translation,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getTranslation" resultType="java.lang.String">
     select translation from s_translation
     <where>
       <if test="fkTableName != null and  fkTableName !=''">
         and fk_table_name =#{fkTableName}
       </if>
       <if test="fkTableId != null">
         and fk_table_id = #{fkTableId}
       </if>
       <if test="fkTranslationMappingId != null">
         and fk_translation_mapping_id = #{fkTranslationMappingId}
       </if>
       <if test="languageCode != null and  languageCode !=''">
         and language_code =#{languageCode}
       </if>
     </where>
  </select>
    <select id="getTranslationIdsByTableNameAndColumnAndLanguageCode" resultType="java.lang.Long">
      SELECT
        t.fk_table_id
      FROM
        s_translation AS t
          LEFT JOIN s_news AS n ON t.fk_table_id = n.id
          LEFT JOIN s_translation_mapping AS tm ON t.fk_translation_mapping_id = tm.id
      WHERE
          t.fk_translation_mapping_id = ( SELECT id FROM s_translation_mapping WHERE fk_table_name = #{fkTableName} AND fk_column_name =#{fkColumnName} )
        AND t.language_code = #{languageCode}
        AND t.fk_table_name = #{fkTableName}
      GROUP BY
        t.fk_table_id
    </select>
  <select id="getTranslationIdListByTableNameAndColumnAndLanguageCode" resultType="java.lang.Long">
      SELECT
        t.id
      FROM
        s_translation AS t
          LEFT JOIN s_news AS n ON t.fk_table_id = n.id
          LEFT JOIN s_translation_mapping AS tm ON t.fk_translation_mapping_id = tm.id
      WHERE
          t.fk_translation_mapping_id = ( SELECT id FROM s_translation_mapping WHERE fk_table_name = #{fkTableName} AND fk_column_name =#{fkColumnName} )
        AND t.language_code = #{languageCode}
        AND t.fk_table_name = #{fkTableName}
      GROUP BY
        t.id
    </select>
  <select id="getTranslationById" resultType="com.get.institutioncenter.entity.InstitutionTranslation">
    select * from s_translation where id = #{id}
  </select>

  <select id="selectByListMaps" resultType="java.util.Map">
    select
    <foreach collection="mapList" item="item" index="index">
      <if test="index == 0">
        ${item.tableName}.id,
        <foreach collection="item.map" item="map" index="mapIndex" separator=","  >
          map${mapIndex}.*
        </foreach>
        <if test="item.mapDto != null">
          <foreach collection="item.mapDto" item="mapDto" index="mapIndex" open="," separator=","  >
            mapDto${mapIndex}.*
          </foreach>
        </if>
      </if>
    </foreach>

    from
    <foreach collection="mapList" item="item" index="index">
      <if test="index == 0">
        ${item.tableName}
      </if>
    </foreach>

    <foreach collection="mapList" item="item" index="index">
      <if test="index == 0">
        <foreach collection="item.map" item="map" index="mapIndex" open="Left JOIN"  separator="Left JOIN">
          (
          SELECT a.fk_table_id,
          a.translation
          <choose>
            <when test="map == 'fk_table_id'">
              AS ${map}mapIndex
            </when>
            <otherwise>
              AS ${map}
            </otherwise>
          </choose>
          FROM s_translation a
          LEFT JOIN s_translation_mapping b ON a.fk_translation_mapping_id=b.id
          WHERE a.fk_table_name= #{item.tableName} AND a.language_code= #{item.languageFromHeader} AND b.fk_column_name= #{map}
          ) map${mapIndex} on ${item.tableName}.id =  map${mapIndex}.fk_table_id
        </foreach>
      </if>

    </foreach>
    /* 循环Dto类型 */
    <foreach collection="mapList" item="item" index="index">
      <if test="index == 0">
        <foreach collection="item.mapDto" item="itemDto" index="key"  >
          left join ${itemDto.tableNameDto} ${itemDto.tableNameDto}${key}   on ${item.tableName}.${itemDto.columnDtoMainId} =${itemDto.tableNameDto}${key}.id
          LEFT JOIN (
          SELECT a.fk_table_id, a.translation as ${itemDto.entityColumnDto} FROM s_translation a
          LEFT JOIN s_translation_mapping b ON a.fk_translation_mapping_id=b.id
          WHERE a.fk_table_name= #{itemDto.tableNameDto} AND a.language_code=#{item.languageFromHeader} AND b.fk_column_name = #{itemDto.columnDto}
          ) mapDto${key} ON ${itemDto.tableNameDto}${key} .id=mapDto${key}.fk_table_id
        </foreach>
      </if>
    </foreach>


    <where>
      <foreach collection="mapList" item="item" index="index">
        <if test="index == 0">
          ${item.tableName}.id in
        </if>
      </foreach>
      <foreach collection="mapList" item="item"  index="index" open="(" close=")" separator=",">
        ${item.id}
      </foreach>

    </where>
  </select>
    <select id="getInstitutionFacultyForTranslation"
            resultType="com.get.institutioncenter.entity.InstitutionFaculty">
      SELECT * FROM ais_institution_center.m_institution_faculty a
      WHERE a.fk_institution_id IN
      <foreach collection="institutionIds" item="item" index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>
      and a.name is not null and a.name !=''
    </select>
  <select id="getInstitutionCourseSubjectForTranslation"
          resultType="com.get.institutioncenter.entity.InstitutionCourseSubject">
    SELECT a.* FROM ais_institution_center.m_institution_course_subject a
                      INNER JOIN ais_institution_center.m_institution_course b ON a.fk_institution_course_id=b.id
                      INNER JOIN ais_institution_center.m_institution c ON b.fk_institution_id=c.id
    WHERE c.id IN
    <foreach collection="institutionIds" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
      and (a.subject_name_chn is null or a.subject_name_chn = '')
  </select>
  <select id="getInstitutionForTranslationDetail" resultType="com.get.institutioncenter.entity.Institution">
    SELECT a.* FROM ais_institution_center.m_institution a
    WHERE a.id IN
    <foreach collection="institutionIds" item="item" index="index" open="(" close=")" separator=",">
    #{item}
    </foreach>
    and a.detail is not null and a.detail != ''
  </select>
  <select id="getInstitutionCourseForTranslation"
          resultType="com.get.institutioncenter.entity.InstitutionCourse">
    SELECT a.* FROM ais_institution_center.m_institution_course a
                      INNER JOIN ais_institution_center.m_institution b ON a.fk_institution_id=b.id
                      INNER JOIN (
      SELECT a.fk_institution_course_id FROM ais_institution_center.r_institution_course_major_level a WHERE a.fk_major_level_id IN(11,12,37,39,41,43)
      GROUP BY a.fk_institution_course_id
    ) c ON a.id=c.fk_institution_course_id
    WHERE b.id IN
      <foreach collection="institutionIds" item="item" index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>
      AND a.is_active=1
    ORDER BY a.id
  </select>
  <select id="getInstitutionCourEngScoreForTranslation"
          resultType="com.get.institutioncenter.entity.InstitutionCourseEngScore">
    SELECT a.* FROM m_institution_course_eng_score a
                                                      INNER JOIN ais_institution_center.m_institution_course b ON a.fk_institution_course_id=b.id
                                                      INNER JOIN (
      SELECT a.fk_institution_course_id FROM ais_institution_center.r_institution_course_major_level a WHERE a.fk_major_level_id IN(11,12,37,39,41,43)
      GROUP BY a.fk_institution_course_id
    ) c ON b.id=c.fk_institution_course_id
                                                      INNER JOIN ais_institution_center.m_institution d ON b.fk_institution_id=d.id
    WHERE d.id IN
    <foreach collection="institutionIds" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
      AND b.is_active=1
      AND a.condition_type = 1
  </select>
    <select id="getInstitutionCourAcademicScoreForTranslation"
            resultType="com.get.institutioncenter.entity.InstitutionCourseAcademicScore">
        SELECT a.* FROM m_institution_course_academic_score a
                                                            INNER JOIN ais_institution_center.m_institution_course b ON a.fk_institution_course_id=b.id
                                                            INNER JOIN (
            SELECT a.fk_institution_course_id FROM ais_institution_center.r_institution_course_major_level a WHERE a.fk_major_level_id IN(11,12,37,39,41,43)
            GROUP BY a.fk_institution_course_id
        ) c ON b.id=c.fk_institution_course_id
                                                            INNER JOIN ais_institution_center.m_institution d ON b.fk_institution_id=d.id
        WHERE d.id IN
        <foreach collection="institutionIds" item="item" index="index" open="(" close=")" separator=",">
          #{item}
        </foreach>
          AND b.is_active=1
          AND a.condition_type IN(12,13,14,15,16,17,18,21,22,23,26)
        order by a.id
    </select>

  <select id="getInstitutionFacultyForTranslations"
          resultType="com.get.institutioncenter.entity.InstitutionFaculty">
      SELECT a.*
FROM ais_institution_center.m_institution_faculty a
JOIN ais_institution_center.m_institution i  ON a.fk_institution_id = i.id
WHERE a.name IS NOT NULL
  AND a.name != '';
    </select>
  <select id="getInstitutionCourseSubjectForTranslations"
          resultType="com.get.institutioncenter.entity.InstitutionCourseSubject">
SELECT a.* FROM ais_institution_center.m_institution_course_subject a
                      INNER JOIN ais_institution_center.m_institution_course b ON a.fk_institution_course_id=b.id
                      INNER JOIN ais_institution_center.m_institution c ON b.fk_institution_id=c.id
					  INNER JOIN	ais_institution_center.r_institution_course_major_level a1
        ON b.id = a1.fk_institution_course_id AND a1.fk_major_level_id IN ( 11, 12, 37, 39, 41, 43 )
    WHERE (a.subject_name_chn is null or a.subject_name_chn = '') and a.subject_name != '';
  </select>
  <select id="getInstitutionForTranslationDetails" resultType="com.get.institutioncenter.entity.Institution">
     SELECT a.* FROM ais_institution_center.m_institution a
    WHERE  a.detail is not null and a.detail != ''
  </select>
  <select id="getInstitutionCourseForTranslations"
          resultType="com.get.institutioncenter.entity.InstitutionCourse">
     SELECT a.* FROM ais_institution_center.m_institution_course a
                      INNER JOIN ais_institution_center.m_institution b ON a.fk_institution_id=b.id
                      INNER JOIN (
      SELECT a.fk_institution_course_id FROM ais_institution_center.r_institution_course_major_level a WHERE a.fk_major_level_id IN(11,12,37,39,41,43)
      GROUP BY a.fk_institution_course_id
    ) c ON a.id=c.fk_institution_course_id
    WHERE  a.is_active=1
    ORDER BY a.id
  </select>
  <select id="getInstitutionCourEngScoreForTranslations"
          resultType="com.get.institutioncenter.entity.InstitutionCourseEngScore">
    SELECT
	a.*
FROM
	m_institution_course_eng_score a
	INNER JOIN ais_institution_center.m_institution_course b ON a.fk_institution_course_id = b.id
	INNER JOIN (
	SELECT
		a.fk_institution_course_id
	FROM
		ais_institution_center.r_institution_course_major_level a
	WHERE
		a.fk_major_level_id IN ( 11, 12, 37, 39, 41, 43 )
	GROUP BY
		a.fk_institution_course_id
	) c ON b.id = c.fk_institution_course_id
	INNER JOIN ais_institution_center.m_institution d ON b.fk_institution_id = d.id
    WHERE  b.is_active = 1
	AND a.condition_type = 1
  </select>
  <select id="getInstitutionCourAcademicScoreForTranslations"
          resultType="com.get.institutioncenter.entity.InstitutionCourseAcademicScore">
        SELECT a.* FROM m_institution_course_academic_score a
                                                            INNER JOIN ais_institution_center.m_institution_course b ON a.fk_institution_course_id=b.id
                                                            INNER JOIN (
            SELECT a.fk_institution_course_id FROM ais_institution_center.r_institution_course_major_level a WHERE a.fk_major_level_id IN(11,12,37,39,41,43)
            GROUP BY a.fk_institution_course_id
        ) c ON b.id=c.fk_institution_course_id
                                                            INNER JOIN ais_institution_center.m_institution d ON b.fk_institution_id=d.id
        WHERE
				b.is_active=1
          AND a.condition_type IN(12,13,14,15,16,17,18,21,22,23,26)
        order by a.id
    </select>

</mapper>