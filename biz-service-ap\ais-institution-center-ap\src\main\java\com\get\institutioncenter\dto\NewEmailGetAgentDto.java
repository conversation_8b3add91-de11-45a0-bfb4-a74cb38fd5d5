package com.get.institutioncenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/7/18 16:17
 * @desciption:
 */
@Data
public class NewEmailGetAgentDto extends BaseVoEntity {

    @ApiModelProperty(value = "步骤ids")
    private Set<Long> stepIds;


    @ApiModelProperty(value = "入学时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date intakeTimeStart;

    @ApiModelProperty(value = "入学时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date intakeTimeEnd;

    @ApiModelProperty(value = "国家id")
    private Long fkCountryId;

    @ApiModelProperty(value = "学校id")
    private Long fkInstitutionId;
    @ApiModelProperty(value = "新闻id")
    private Long newsId;
}
