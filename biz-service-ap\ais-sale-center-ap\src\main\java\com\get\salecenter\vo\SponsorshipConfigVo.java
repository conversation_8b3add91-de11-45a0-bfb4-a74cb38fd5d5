package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.SponsorshipConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Sea
 * @create: 2021/4/30 17:12
 * @verison: 1.0
 * @description:
 */
@Data
public class SponsorshipConfigVo extends BaseEntity {
    /**
     * 是否剩余 false未售完/true售完
     */
    @ApiModelProperty(value = "是否剩余 false未售完/true售完")
    private Boolean soldOut;

    //==========实体类SponsorshipConfig================
    private static final long serialVersionUID = 1L;
    /**
     * 内容
     */
    @ApiModelProperty(value = "内容")
    @Column(name = "text")
    private String text;
    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    @Column(name = "date")
    private String date;
    /**
     * 时间
     */
    @ApiModelProperty(value = "时间")
    @Column(name = "time")
    private String time;
    /**
     * 价格
     */
    @ApiModelProperty(value = "价格")
    @Column(name = "price")
    private String price;
    /**
     * 注意
     */
    @ApiModelProperty(value = "注意")
    @Column(name = "note")
    private String note;
    /**
     * 图片路径
     */
    @ApiModelProperty(value = "图片路径")
    @Column(name = "img_url")
    private String imgUrl;
    /**
     * 初始次数
     */
    @ApiModelProperty(value = "初始次数")
    @Column(name = "init_num")
    private Integer initNum;
    /**
     * 赞助类型
     */
    @ApiModelProperty(value = "赞助类型")
    @Column(name = "type")
    private String type;
}
