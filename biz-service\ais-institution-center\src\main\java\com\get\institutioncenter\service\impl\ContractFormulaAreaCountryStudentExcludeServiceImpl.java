package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.institutioncenter.dao.ContractFormulaAreaCountryStudentExcludeMapper;
import com.get.institutioncenter.entity.ContractFormulaAreaCountryStudentExclude;
import com.get.institutioncenter.service.IContractFormulaAreaCountryStudentExcludeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: Neil
 * @description:
 * @date: 2022/4/22 17:01
 * @param 
 * @return
 */
@Service
public class ContractFormulaAreaCountryStudentExcludeServiceImpl extends BaseServiceImpl<ContractFormulaAreaCountryStudentExcludeMapper,
        ContractFormulaAreaCountryStudentExclude> implements IContractFormulaAreaCountryStudentExcludeService {

    @Resource ContractFormulaAreaCountryStudentExcludeMapper contractFormulaAreaCountryStudentExcludeMapper;

    //删除关系表
    @Override
    public void deleteByFkid(Long id) {
        LambdaQueryWrapper<ContractFormulaAreaCountryStudentExclude> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContractFormulaAreaCountryStudentExclude::getFkContractFormulaId, id);
        contractFormulaAreaCountryStudentExcludeMapper.delete(wrapper);
    }
}
