package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.salecenter.vo.StaffCommissionInstitutionVo;
import com.get.salecenter.service.IStaffCommissionInstitutionService;
import com.get.salecenter.dto.StaffCommissionInstitutionDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/2/7 16:33
 * @verison: 1.0
 * @description:
 */
@Api(tags = "员工提成学校关系管理")
@RestController
@RequestMapping("sale/staffCommissionInstitution")
public class StaffCommissionInstitutionController {

    @Resource
    private IStaffCommissionInstitutionService staffCommissionInstitutionService;

    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/员工提成学校关系管理/列表")
    @PostMapping("datas")
    public ListResponseBo<StaffCommissionInstitutionVo> datas(@RequestBody SearchBean<StaffCommissionInstitutionDto> page) {
        List<StaffCommissionInstitutionVo> datas = staffCommissionInstitutionService.getStaffCommissionInstitutionDtos(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "设置学校激活提成", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/员工提成学校关系管理/设置学校激活提成")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated({BaseVoEntity.Add.class}) StaffCommissionInstitutionDto staffCommissionInstitutionDto) {
        return SaveResponseBo.ok(staffCommissionInstitutionService.addStaffCommissionInstitution(staffCommissionInstitutionDto));
    }

    @ApiOperation(value = "批量设置学校激活提成", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/员工提成学校关系管理/批量设置学校激活提成")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated({BaseVoEntity.Add.class}) ValidList<StaffCommissionInstitutionDto> staffCommissionInstitutionDtos) {
        staffCommissionInstitutionService.batchAdd(staffCommissionInstitutionDtos);
        return SaveResponseBo.ok();
    }

    @ApiOperation(value = "删除", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/员工提成学校关系管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        staffCommissionInstitutionService.delete(id);
        return DeleteResponseBo.ok();
    }
}
