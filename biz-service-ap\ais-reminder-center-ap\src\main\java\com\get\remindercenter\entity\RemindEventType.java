package com.get.remindercenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("u_remind_event_type")
public class RemindEventType extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 类型名称，必填，如：自定义提醒，学习计划接受Offer截止时间提醒，学习计划支付押金截止时间提醒
     */
    @ApiModelProperty(value = "类型名称，必填，如：自定义提醒，学习计划接受Offer截止时间提醒，学习计划支付押金截止时间提醒")
    private String typeName;
    /**
     * 类型Key，枚举类型Key，必填
     */
    @ApiModelProperty(value = "类型Key，枚举类型Key，必填")
    private String typeKey;
    /**
     * 是否显示到日程，0否/1是
     */
    @ApiModelProperty(value = "是否显示到日程，0否/1是")
    private Boolean isDisplay;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
}