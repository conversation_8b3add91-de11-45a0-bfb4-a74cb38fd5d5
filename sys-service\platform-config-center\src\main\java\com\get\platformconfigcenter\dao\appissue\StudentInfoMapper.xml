<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.platformconfigcenter.dao.appissue.StudentInfoMapper">
  <insert id="insert" parameterType="com.get.platformconfigcenter.entity.StudentInfo">
    insert into m_student (id, fk_agent_id, name_zh, 
      name_en, last_name, first_name, 
      phone_region, phone_number, email, 
      email_password, fk_student_app_country_id_from, 
      fk_area_state_id_from, fk_area_city_id_from, fk_area_city_division_id_from, 
      contact_address_1, contact_address_2, contact_postcode, 
      birthday, gender, title, 
      fk_student_app_country_id, fk_student_app_ancestry_id, 
      fk_student_app_mother_language_id, fk_student_app_country_id_birth, 
      city_birth, is_lived_since_birth, fk_student_app_country_id_live, 
      migration_time, staying_right_time, fk_student_app_country_id_passport, 
      is_have_passport, passport_issue_date, passport_issue_expried, 
      passport_number, follow_up_phone_code_1, follow_up_phone_code_2, 
      follow_up_phone_code_3, follow_up_phone_number_1, 
      follow_up_phone_number_2, follow_up_phone_number_3, 
      follow_up_email_1, follow_up_email_2, follow_up_email_3, 
      father_last_name, father_first_name, father_email, 
      father_phone_number, monter_last_name, monter_first_name, 
      monter_email, monter_phone_number, parent_contact_postcode, 
      parent_contact_address1, parent_contact_address2, 
      fk_student_app_country_id_parent, fk_area_state_id_parent, 
      fk_area_city_id_parent, is_need_accommodation, accommodation_type, 
      is_accommodation_smoke, is_accommodation_pet, is_accommodation_children, 
      is_accommodation_food, accommodation_food_note, 
      is_accommodation_other, accommodation_other_note, 
      is_need_pick_up, status, gmt_create, 
      gmt_create_user, gmt_modified, gmt_modified_user
      )
    values (#{id,jdbcType=BIGINT}, #{fkAgentId,jdbcType=BIGINT}, #{nameZh,jdbcType=VARCHAR}, 
      #{nameEn,jdbcType=VARCHAR}, #{lastName,jdbcType=VARCHAR}, #{firstName,jdbcType=VARCHAR}, 
      #{phoneRegion,jdbcType=VARCHAR}, #{phoneNumber,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, 
      #{emailPassword,jdbcType=VARCHAR}, #{fkStudentAppCountryIdFrom,jdbcType=VARCHAR}, 
      #{fkAreaStateIdFrom,jdbcType=VARCHAR}, #{fkAreaCityIdFrom,jdbcType=VARCHAR}, #{fkAreaCityDivisionIdFrom,jdbcType=VARCHAR}, 
      #{contactAddress1,jdbcType=VARCHAR}, #{contactAddress2,jdbcType=VARCHAR}, #{contactPostcode,jdbcType=VARCHAR}, 
      #{birthday,jdbcType=DATE}, #{gender,jdbcType=INTEGER}, #{title,jdbcType=INTEGER}, 
      #{fkStudentAppCountryId,jdbcType=BIGINT}, #{fkStudentAppAncestryId,jdbcType=BIGINT}, 
      #{fkStudentAppMotherLanguageId,jdbcType=BIGINT}, #{fkStudentAppCountryIdBirth,jdbcType=BIGINT}, 
      #{cityBirth,jdbcType=VARCHAR}, #{isLivedSinceBirth,jdbcType=BIT}, #{fkStudentAppCountryIdLive,jdbcType=BIGINT}, 
      #{migrationTime,jdbcType=DATE}, #{stayingRightTime,jdbcType=DATE}, #{fkStudentAppCountryIdPassport,jdbcType=BIGINT}, 
      #{isHavePassport,jdbcType=BIT}, #{passportIssueDate,jdbcType=DATE}, #{passportIssueExpried,jdbcType=DATE}, 
      #{passportNumber,jdbcType=VARCHAR}, #{followUpPhoneCode1,jdbcType=VARCHAR}, #{followUpPhoneCode2,jdbcType=VARCHAR}, 
      #{followUpPhoneCode3,jdbcType=VARCHAR}, #{followUpPhoneNumber1,jdbcType=VARCHAR}, 
      #{followUpPhoneNumber2,jdbcType=VARCHAR}, #{followUpPhoneNumber3,jdbcType=VARCHAR}, 
      #{followUpEmail1,jdbcType=VARCHAR}, #{followUpEmail2,jdbcType=VARCHAR}, #{followUpEmail3,jdbcType=VARCHAR}, 
      #{fatherLastName,jdbcType=VARCHAR}, #{fatherFirstName,jdbcType=VARCHAR}, #{fatherEmail,jdbcType=VARCHAR}, 
      #{fatherPhoneNumber,jdbcType=VARCHAR}, #{monterLastName,jdbcType=VARCHAR}, #{monterFirstName,jdbcType=VARCHAR}, 
      #{monterEmail,jdbcType=VARCHAR}, #{monterPhoneNumber,jdbcType=VARCHAR}, #{parentContactPostcode,jdbcType=VARCHAR}, 
      #{parentContactAddress1,jdbcType=VARCHAR}, #{parentContactAddress2,jdbcType=VARCHAR}, 
      #{fkStudentAppCountryIdParent,jdbcType=VARCHAR}, #{fkAreaStateIdParent,jdbcType=VARCHAR}, 
      #{fkAreaCityIdParent,jdbcType=VARCHAR}, #{isNeedAccommodation,jdbcType=BIT}, #{accommodationType,jdbcType=INTEGER}, 
      #{isAccommodationSmoke,jdbcType=BIT}, #{isAccommodationPet,jdbcType=BIT}, #{isAccommodationChildren,jdbcType=BIT}, 
      #{isAccommodationFood,jdbcType=BIT}, #{accommodationFoodNote,jdbcType=VARCHAR}, 
      #{isAccommodationOther,jdbcType=BIT}, #{accommodationOtherNote,jdbcType=VARCHAR}, 
      #{isNeedPickUp,jdbcType=BIT}, #{status,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.get.platformconfigcenter.entity.StudentInfo">
    insert into m_student
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkAgentId != null">
        fk_agent_id,
      </if>
      <if test="nameZh != null">
        name_zh,
      </if>
      <if test="nameEn != null">
        name_en,
      </if>
      <if test="lastName != null">
        last_name,
      </if>
      <if test="firstName != null">
        first_name,
      </if>
      <if test="phoneRegion != null">
        phone_region,
      </if>
      <if test="phoneNumber != null">
        phone_number,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="emailPassword != null">
        email_password,
      </if>
      <if test="fkStudentAppCountryIdFrom != null">
        fk_student_app_country_id_from,
      </if>
      <if test="fkAreaStateIdFrom != null">
        fk_area_state_id_from,
      </if>
      <if test="fkAreaCityIdFrom != null">
        fk_area_city_id_from,
      </if>
      <if test="fkAreaCityDivisionIdFrom != null">
        fk_area_city_division_id_from,
      </if>
      <if test="contactAddress1 != null">
        contact_address_1,
      </if>
      <if test="contactAddress2 != null">
        contact_address_2,
      </if>
      <if test="contactPostcode != null">
        contact_postcode,
      </if>
      <if test="birthday != null">
        birthday,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="fkStudentAppCountryId != null">
        fk_student_app_country_id,
      </if>
      <if test="fkStudentAppAncestryId != null">
        fk_student_app_ancestry_id,
      </if>
      <if test="fkStudentAppMotherLanguageId != null">
        fk_student_app_mother_language_id,
      </if>
      <if test="fkStudentAppCountryIdBirth != null">
        fk_student_app_country_id_birth,
      </if>
      <if test="cityBirth != null">
        city_birth,
      </if>
      <if test="isLivedSinceBirth != null">
        is_lived_since_birth,
      </if>
      <if test="fkStudentAppCountryIdLive != null">
        fk_student_app_country_id_live,
      </if>
      <if test="migrationTime != null">
        migration_time,
      </if>
      <if test="stayingRightTime != null">
        staying_right_time,
      </if>
      <if test="fkStudentAppCountryIdPassport != null">
        fk_student_app_country_id_passport,
      </if>
      <if test="isHavePassport != null">
        is_have_passport,
      </if>
      <if test="passportIssueDate != null">
        passport_issue_date,
      </if>
      <if test="passportIssueExpried != null">
        passport_issue_expried,
      </if>
      <if test="passportNumber != null">
        passport_number,
      </if>
      <if test="followUpPhoneCode1 != null">
        follow_up_phone_code_1,
      </if>
      <if test="followUpPhoneCode2 != null">
        follow_up_phone_code_2,
      </if>
      <if test="followUpPhoneCode3 != null">
        follow_up_phone_code_3,
      </if>
      <if test="followUpPhoneNumber1 != null">
        follow_up_phone_number_1,
      </if>
      <if test="followUpPhoneNumber2 != null">
        follow_up_phone_number_2,
      </if>
      <if test="followUpPhoneNumber3 != null">
        follow_up_phone_number_3,
      </if>
      <if test="followUpEmail1 != null">
        follow_up_email_1,
      </if>
      <if test="followUpEmail2 != null">
        follow_up_email_2,
      </if>
      <if test="followUpEmail3 != null">
        follow_up_email_3,
      </if>
      <if test="fatherLastName != null">
        father_last_name,
      </if>
      <if test="fatherFirstName != null">
        father_first_name,
      </if>
      <if test="fatherEmail != null">
        father_email,
      </if>
      <if test="fatherPhoneNumber != null">
        father_phone_number,
      </if>
      <if test="monterLastName != null">
        monter_last_name,
      </if>
      <if test="monterFirstName != null">
        monter_first_name,
      </if>
      <if test="monterEmail != null">
        monter_email,
      </if>
      <if test="monterPhoneNumber != null">
        monter_phone_number,
      </if>
      <if test="parentContactPostcode != null">
        parent_contact_postcode,
      </if>
      <if test="parentContactAddress1 != null">
        parent_contact_address1,
      </if>
      <if test="parentContactAddress2 != null">
        parent_contact_address2,
      </if>
      <if test="fkStudentAppCountryIdParent != null">
        fk_student_app_country_id_parent,
      </if>
      <if test="fkAreaStateIdParent != null">
        fk_area_state_id_parent,
      </if>
      <if test="fkAreaCityIdParent != null">
        fk_area_city_id_parent,
      </if>
      <if test="isNeedAccommodation != null">
        is_need_accommodation,
      </if>
      <if test="accommodationType != null">
        accommodation_type,
      </if>
      <if test="isAccommodationSmoke != null">
        is_accommodation_smoke,
      </if>
      <if test="isAccommodationPet != null">
        is_accommodation_pet,
      </if>
      <if test="isAccommodationChildren != null">
        is_accommodation_children,
      </if>
      <if test="isAccommodationFood != null">
        is_accommodation_food,
      </if>
      <if test="accommodationFoodNote != null">
        accommodation_food_note,
      </if>
      <if test="isAccommodationOther != null">
        is_accommodation_other,
      </if>
      <if test="accommodationOtherNote != null">
        accommodation_other_note,
      </if>
      <if test="isNeedPickUp != null">
        is_need_pick_up,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkAgentId != null">
        #{fkAgentId,jdbcType=BIGINT},
      </if>
      <if test="nameZh != null">
        #{nameZh,jdbcType=VARCHAR},
      </if>
      <if test="nameEn != null">
        #{nameEn,jdbcType=VARCHAR},
      </if>
      <if test="lastName != null">
        #{lastName,jdbcType=VARCHAR},
      </if>
      <if test="firstName != null">
        #{firstName,jdbcType=VARCHAR},
      </if>
      <if test="phoneRegion != null">
        #{phoneRegion,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="emailPassword != null">
        #{emailPassword,jdbcType=VARCHAR},
      </if>
      <if test="fkStudentAppCountryIdFrom != null">
        #{fkStudentAppCountryIdFrom,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaStateIdFrom != null">
        #{fkAreaStateIdFrom,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCityIdFrom != null">
        #{fkAreaCityIdFrom,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCityDivisionIdFrom != null">
        #{fkAreaCityDivisionIdFrom,jdbcType=VARCHAR},
      </if>
      <if test="contactAddress1 != null">
        #{contactAddress1,jdbcType=VARCHAR},
      </if>
      <if test="contactAddress2 != null">
        #{contactAddress2,jdbcType=VARCHAR},
      </if>
      <if test="contactPostcode != null">
        #{contactPostcode,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null">
        #{birthday,jdbcType=DATE},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=INTEGER},
      </if>
      <if test="title != null">
        #{title,jdbcType=INTEGER},
      </if>
      <if test="fkStudentAppCountryId != null">
        #{fkStudentAppCountryId,jdbcType=BIGINT},
      </if>
      <if test="fkStudentAppAncestryId != null">
        #{fkStudentAppAncestryId,jdbcType=BIGINT},
      </if>
      <if test="fkStudentAppMotherLanguageId != null">
        #{fkStudentAppMotherLanguageId,jdbcType=BIGINT},
      </if>
      <if test="fkStudentAppCountryIdBirth != null">
        #{fkStudentAppCountryIdBirth,jdbcType=BIGINT},
      </if>
      <if test="cityBirth != null">
        #{cityBirth,jdbcType=VARCHAR},
      </if>
      <if test="isLivedSinceBirth != null">
        #{isLivedSinceBirth,jdbcType=BIT},
      </if>
      <if test="fkStudentAppCountryIdLive != null">
        #{fkStudentAppCountryIdLive,jdbcType=BIGINT},
      </if>
      <if test="migrationTime != null">
        #{migrationTime,jdbcType=DATE},
      </if>
      <if test="stayingRightTime != null">
        #{stayingRightTime,jdbcType=DATE},
      </if>
      <if test="fkStudentAppCountryIdPassport != null">
        #{fkStudentAppCountryIdPassport,jdbcType=BIGINT},
      </if>
      <if test="isHavePassport != null">
        #{isHavePassport,jdbcType=BIT},
      </if>
      <if test="passportIssueDate != null">
        #{passportIssueDate,jdbcType=DATE},
      </if>
      <if test="passportIssueExpried != null">
        #{passportIssueExpried,jdbcType=DATE},
      </if>
      <if test="passportNumber != null">
        #{passportNumber,jdbcType=VARCHAR},
      </if>
      <if test="followUpPhoneCode1 != null">
        #{followUpPhoneCode1,jdbcType=VARCHAR},
      </if>
      <if test="followUpPhoneCode2 != null">
        #{followUpPhoneCode2,jdbcType=VARCHAR},
      </if>
      <if test="followUpPhoneCode3 != null">
        #{followUpPhoneCode3,jdbcType=VARCHAR},
      </if>
      <if test="followUpPhoneNumber1 != null">
        #{followUpPhoneNumber1,jdbcType=VARCHAR},
      </if>
      <if test="followUpPhoneNumber2 != null">
        #{followUpPhoneNumber2,jdbcType=VARCHAR},
      </if>
      <if test="followUpPhoneNumber3 != null">
        #{followUpPhoneNumber3,jdbcType=VARCHAR},
      </if>
      <if test="followUpEmail1 != null">
        #{followUpEmail1,jdbcType=VARCHAR},
      </if>
      <if test="followUpEmail2 != null">
        #{followUpEmail2,jdbcType=VARCHAR},
      </if>
      <if test="followUpEmail3 != null">
        #{followUpEmail3,jdbcType=VARCHAR},
      </if>
      <if test="fatherLastName != null">
        #{fatherLastName,jdbcType=VARCHAR},
      </if>
      <if test="fatherFirstName != null">
        #{fatherFirstName,jdbcType=VARCHAR},
      </if>
      <if test="fatherEmail != null">
        #{fatherEmail,jdbcType=VARCHAR},
      </if>
      <if test="fatherPhoneNumber != null">
        #{fatherPhoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="monterLastName != null">
        #{monterLastName,jdbcType=VARCHAR},
      </if>
      <if test="monterFirstName != null">
        #{monterFirstName,jdbcType=VARCHAR},
      </if>
      <if test="monterEmail != null">
        #{monterEmail,jdbcType=VARCHAR},
      </if>
      <if test="monterPhoneNumber != null">
        #{monterPhoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="parentContactPostcode != null">
        #{parentContactPostcode,jdbcType=VARCHAR},
      </if>
      <if test="parentContactAddress1 != null">
        #{parentContactAddress1,jdbcType=VARCHAR},
      </if>
      <if test="parentContactAddress2 != null">
        #{parentContactAddress2,jdbcType=VARCHAR},
      </if>
      <if test="fkStudentAppCountryIdParent != null">
        #{fkStudentAppCountryIdParent,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaStateIdParent != null">
        #{fkAreaStateIdParent,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCityIdParent != null">
        #{fkAreaCityIdParent,jdbcType=VARCHAR},
      </if>
      <if test="isNeedAccommodation != null">
        #{isNeedAccommodation,jdbcType=BIT},
      </if>
      <if test="accommodationType != null">
        #{accommodationType,jdbcType=INTEGER},
      </if>
      <if test="isAccommodationSmoke != null">
        #{isAccommodationSmoke,jdbcType=BIT},
      </if>
      <if test="isAccommodationPet != null">
        #{isAccommodationPet,jdbcType=BIT},
      </if>
      <if test="isAccommodationChildren != null">
        #{isAccommodationChildren,jdbcType=BIT},
      </if>
      <if test="isAccommodationFood != null">
        #{isAccommodationFood,jdbcType=BIT},
      </if>
      <if test="accommodationFoodNote != null">
        #{accommodationFoodNote,jdbcType=VARCHAR},
      </if>
      <if test="isAccommodationOther != null">
        #{isAccommodationOther,jdbcType=BIT},
      </if>
      <if test="accommodationOtherNote != null">
        #{accommodationOtherNote,jdbcType=VARCHAR},
      </if>
      <if test="isNeedPickUp != null">
        #{isNeedPickUp,jdbcType=BIT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.get.platformconfigcenter.entity.StudentInfo">
    update m_student
    <set>
      <if test="fkAgentId != null">
        fk_agent_id = #{fkAgentId,jdbcType=BIGINT},
      </if>
      <if test="nameZh != null">
        name_zh = #{nameZh,jdbcType=VARCHAR},
      </if>
      <if test="nameEn != null">
        name_en = #{nameEn,jdbcType=VARCHAR},
      </if>
      <if test="lastName != null">
        last_name = #{lastName,jdbcType=VARCHAR},
      </if>
      <if test="firstName != null">
        first_name = #{firstName,jdbcType=VARCHAR},
      </if>
      <if test="phoneRegion != null">
        phone_region = #{phoneRegion,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        phone_number = #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="emailPassword != null">
        email_password = #{emailPassword,jdbcType=VARCHAR},
      </if>
      <if test="fkStudentAppCountryIdFrom != null">
        fk_student_app_country_id_from = #{fkStudentAppCountryIdFrom,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaStateIdFrom != null">
        fk_area_state_id_from = #{fkAreaStateIdFrom,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCityIdFrom != null">
        fk_area_city_id_from = #{fkAreaCityIdFrom,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCityDivisionIdFrom != null">
        fk_area_city_division_id_from = #{fkAreaCityDivisionIdFrom,jdbcType=VARCHAR},
      </if>
      <if test="contactAddress1 != null">
        contact_address_1 = #{contactAddress1,jdbcType=VARCHAR},
      </if>
      <if test="contactAddress2 != null">
        contact_address_2 = #{contactAddress2,jdbcType=VARCHAR},
      </if>
      <if test="contactPostcode != null">
        contact_postcode = #{contactPostcode,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null">
        birthday = #{birthday,jdbcType=DATE},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=INTEGER},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=INTEGER},
      </if>
      <if test="fkStudentAppCountryId != null">
        fk_student_app_country_id = #{fkStudentAppCountryId,jdbcType=BIGINT},
      </if>
      <if test="fkStudentAppAncestryId != null">
        fk_student_app_ancestry_id = #{fkStudentAppAncestryId,jdbcType=BIGINT},
      </if>
      <if test="fkStudentAppMotherLanguageId != null">
        fk_student_app_mother_language_id = #{fkStudentAppMotherLanguageId,jdbcType=BIGINT},
      </if>
      <if test="fkStudentAppCountryIdBirth != null">
        fk_student_app_country_id_birth = #{fkStudentAppCountryIdBirth,jdbcType=BIGINT},
      </if>
      <if test="cityBirth != null">
        city_birth = #{cityBirth,jdbcType=VARCHAR},
      </if>
      <if test="isLivedSinceBirth != null">
        is_lived_since_birth = #{isLivedSinceBirth,jdbcType=BIT},
      </if>
      <if test="fkStudentAppCountryIdLive != null">
        fk_student_app_country_id_live = #{fkStudentAppCountryIdLive,jdbcType=BIGINT},
      </if>
      <if test="migrationTime != null">
        migration_time = #{migrationTime,jdbcType=DATE},
      </if>
      <if test="stayingRightTime != null">
        staying_right_time = #{stayingRightTime,jdbcType=DATE},
      </if>
      <if test="fkStudentAppCountryIdPassport != null">
        fk_student_app_country_id_passport = #{fkStudentAppCountryIdPassport,jdbcType=BIGINT},
      </if>
      <if test="isHavePassport != null">
        is_have_passport = #{isHavePassport,jdbcType=BIT},
      </if>
      <if test="passportIssueDate != null">
        passport_issue_date = #{passportIssueDate,jdbcType=DATE},
      </if>
      <if test="passportIssueExpried != null">
        passport_issue_expried = #{passportIssueExpried,jdbcType=DATE},
      </if>
      <if test="passportNumber != null">
        passport_number = #{passportNumber,jdbcType=VARCHAR},
      </if>
      <if test="followUpPhoneCode1 != null">
        follow_up_phone_code_1 = #{followUpPhoneCode1,jdbcType=VARCHAR},
      </if>
      <if test="followUpPhoneCode2 != null">
        follow_up_phone_code_2 = #{followUpPhoneCode2,jdbcType=VARCHAR},
      </if>
      <if test="followUpPhoneCode3 != null">
        follow_up_phone_code_3 = #{followUpPhoneCode3,jdbcType=VARCHAR},
      </if>
      <if test="followUpPhoneNumber1 != null">
        follow_up_phone_number_1 = #{followUpPhoneNumber1,jdbcType=VARCHAR},
      </if>
      <if test="followUpPhoneNumber2 != null">
        follow_up_phone_number_2 = #{followUpPhoneNumber2,jdbcType=VARCHAR},
      </if>
      <if test="followUpPhoneNumber3 != null">
        follow_up_phone_number_3 = #{followUpPhoneNumber3,jdbcType=VARCHAR},
      </if>
      <if test="followUpEmail1 != null">
        follow_up_email_1 = #{followUpEmail1,jdbcType=VARCHAR},
      </if>
      <if test="followUpEmail2 != null">
        follow_up_email_2 = #{followUpEmail2,jdbcType=VARCHAR},
      </if>
      <if test="followUpEmail3 != null">
        follow_up_email_3 = #{followUpEmail3,jdbcType=VARCHAR},
      </if>
      <if test="fatherLastName != null">
        father_last_name = #{fatherLastName,jdbcType=VARCHAR},
      </if>
      <if test="fatherFirstName != null">
        father_first_name = #{fatherFirstName,jdbcType=VARCHAR},
      </if>
      <if test="fatherEmail != null">
        father_email = #{fatherEmail,jdbcType=VARCHAR},
      </if>
      <if test="fatherPhoneNumber != null">
        father_phone_number = #{fatherPhoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="monterLastName != null">
        monter_last_name = #{monterLastName,jdbcType=VARCHAR},
      </if>
      <if test="monterFirstName != null">
        monter_first_name = #{monterFirstName,jdbcType=VARCHAR},
      </if>
      <if test="monterEmail != null">
        monter_email = #{monterEmail,jdbcType=VARCHAR},
      </if>
      <if test="monterPhoneNumber != null">
        monter_phone_number = #{monterPhoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="parentContactPostcode != null">
        parent_contact_postcode = #{parentContactPostcode,jdbcType=VARCHAR},
      </if>
      <if test="parentContactAddress1 != null">
        parent_contact_address1 = #{parentContactAddress1,jdbcType=VARCHAR},
      </if>
      <if test="parentContactAddress2 != null">
        parent_contact_address2 = #{parentContactAddress2,jdbcType=VARCHAR},
      </if>
      <if test="fkStudentAppCountryIdParent != null">
        fk_student_app_country_id_parent = #{fkStudentAppCountryIdParent,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaStateIdParent != null">
        fk_area_state_id_parent = #{fkAreaStateIdParent,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCityIdParent != null">
        fk_area_city_id_parent = #{fkAreaCityIdParent,jdbcType=VARCHAR},
      </if>
      <if test="isNeedAccommodation != null">
        is_need_accommodation = #{isNeedAccommodation,jdbcType=BIT},
      </if>
      <if test="accommodationType != null">
        accommodation_type = #{accommodationType,jdbcType=INTEGER},
      </if>
      <if test="isAccommodationSmoke != null">
        is_accommodation_smoke = #{isAccommodationSmoke,jdbcType=BIT},
      </if>
      <if test="isAccommodationPet != null">
        is_accommodation_pet = #{isAccommodationPet,jdbcType=BIT},
      </if>
      <if test="isAccommodationChildren != null">
        is_accommodation_children = #{isAccommodationChildren,jdbcType=BIT},
      </if>
      <if test="isAccommodationFood != null">
        is_accommodation_food = #{isAccommodationFood,jdbcType=BIT},
      </if>
      <if test="accommodationFoodNote != null">
        accommodation_food_note = #{accommodationFoodNote,jdbcType=VARCHAR},
      </if>
      <if test="isAccommodationOther != null">
        is_accommodation_other = #{isAccommodationOther,jdbcType=BIT},
      </if>
      <if test="accommodationOtherNote != null">
        accommodation_other_note = #{accommodationOtherNote,jdbcType=VARCHAR},
      </if>
      <if test="isNeedPickUp != null">
        is_need_pick_up = #{isNeedPickUp,jdbcType=BIT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.platformconfigcenter.entity.StudentInfo">
    update m_student
    set fk_agent_id = #{fkAgentId,jdbcType=BIGINT},
      name_zh = #{nameZh,jdbcType=VARCHAR},
      name_en = #{nameEn,jdbcType=VARCHAR},
      last_name = #{lastName,jdbcType=VARCHAR},
      first_name = #{firstName,jdbcType=VARCHAR},
      phone_region = #{phoneRegion,jdbcType=VARCHAR},
      phone_number = #{phoneNumber,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      email_password = #{emailPassword,jdbcType=VARCHAR},
      fk_student_app_country_id_from = #{fkStudentAppCountryIdFrom,jdbcType=VARCHAR},
      fk_area_state_id_from = #{fkAreaStateIdFrom,jdbcType=VARCHAR},
      fk_area_city_id_from = #{fkAreaCityIdFrom,jdbcType=VARCHAR},
      fk_area_city_division_id_from = #{fkAreaCityDivisionIdFrom,jdbcType=VARCHAR},
      contact_address_1 = #{contactAddress1,jdbcType=VARCHAR},
      contact_address_2 = #{contactAddress2,jdbcType=VARCHAR},
      contact_postcode = #{contactPostcode,jdbcType=VARCHAR},
      birthday = #{birthday,jdbcType=DATE},
      gender = #{gender,jdbcType=INTEGER},
      title = #{title,jdbcType=INTEGER},
      fk_student_app_country_id = #{fkStudentAppCountryId,jdbcType=BIGINT},
      fk_student_app_ancestry_id = #{fkStudentAppAncestryId,jdbcType=BIGINT},
      fk_student_app_mother_language_id = #{fkStudentAppMotherLanguageId,jdbcType=BIGINT},
      fk_student_app_country_id_birth = #{fkStudentAppCountryIdBirth,jdbcType=BIGINT},
      city_birth = #{cityBirth,jdbcType=VARCHAR},
      is_lived_since_birth = #{isLivedSinceBirth,jdbcType=BIT},
      fk_student_app_country_id_live = #{fkStudentAppCountryIdLive,jdbcType=BIGINT},
      migration_time = #{migrationTime,jdbcType=DATE},
      staying_right_time = #{stayingRightTime,jdbcType=DATE},
      fk_student_app_country_id_passport = #{fkStudentAppCountryIdPassport,jdbcType=BIGINT},
      is_have_passport = #{isHavePassport,jdbcType=BIT},
      passport_issue_date = #{passportIssueDate,jdbcType=DATE},
      passport_issue_expried = #{passportIssueExpried,jdbcType=DATE},
      passport_number = #{passportNumber,jdbcType=VARCHAR},
      follow_up_phone_code_1 = #{followUpPhoneCode1,jdbcType=VARCHAR},
      follow_up_phone_code_2 = #{followUpPhoneCode2,jdbcType=VARCHAR},
      follow_up_phone_code_3 = #{followUpPhoneCode3,jdbcType=VARCHAR},
      follow_up_phone_number_1 = #{followUpPhoneNumber1,jdbcType=VARCHAR},
      follow_up_phone_number_2 = #{followUpPhoneNumber2,jdbcType=VARCHAR},
      follow_up_phone_number_3 = #{followUpPhoneNumber3,jdbcType=VARCHAR},
      follow_up_email_1 = #{followUpEmail1,jdbcType=VARCHAR},
      follow_up_email_2 = #{followUpEmail2,jdbcType=VARCHAR},
      follow_up_email_3 = #{followUpEmail3,jdbcType=VARCHAR},
      father_last_name = #{fatherLastName,jdbcType=VARCHAR},
      father_first_name = #{fatherFirstName,jdbcType=VARCHAR},
      father_email = #{fatherEmail,jdbcType=VARCHAR},
      father_phone_number = #{fatherPhoneNumber,jdbcType=VARCHAR},
      monter_last_name = #{monterLastName,jdbcType=VARCHAR},
      monter_first_name = #{monterFirstName,jdbcType=VARCHAR},
      monter_email = #{monterEmail,jdbcType=VARCHAR},
      monter_phone_number = #{monterPhoneNumber,jdbcType=VARCHAR},
      parent_contact_postcode = #{parentContactPostcode,jdbcType=VARCHAR},
      parent_contact_address1 = #{parentContactAddress1,jdbcType=VARCHAR},
      parent_contact_address2 = #{parentContactAddress2,jdbcType=VARCHAR},
      fk_student_app_country_id_parent = #{fkStudentAppCountryIdParent,jdbcType=VARCHAR},
      fk_area_state_id_parent = #{fkAreaStateIdParent,jdbcType=VARCHAR},
      fk_area_city_id_parent = #{fkAreaCityIdParent,jdbcType=VARCHAR},
      is_need_accommodation = #{isNeedAccommodation,jdbcType=BIT},
      accommodation_type = #{accommodationType,jdbcType=INTEGER},
      is_accommodation_smoke = #{isAccommodationSmoke,jdbcType=BIT},
      is_accommodation_pet = #{isAccommodationPet,jdbcType=BIT},
      is_accommodation_children = #{isAccommodationChildren,jdbcType=BIT},
      is_accommodation_food = #{isAccommodationFood,jdbcType=BIT},
      accommodation_food_note = #{accommodationFoodNote,jdbcType=VARCHAR},
      is_accommodation_other = #{isAccommodationOther,jdbcType=BIT},
      accommodation_other_note = #{accommodationOtherNote,jdbcType=VARCHAR},
      is_need_pick_up = #{isNeedPickUp,jdbcType=BIT},
      status = #{status,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="getStudentInfo" resultType="com.get.platformconfigcenter.vo.StudentInfoVo">
    select
    ms.id,
    ms.fk_agent_id AS fkAgentId,
    ms.name_zh AS nameZh,
    ms.name_en AS nameEn,
    ms.last_name AS lastName,
    ms.first_name AS firstName,
    ms.phone_region AS phoneRegion,
    ms.phone_number AS phoneNumber,
    ms.email AS email,
    ms.email_password AS emailPassword,
    ms.fk_student_app_country_id_from AS fkStudentAppCountryIdFrom,
    ms.fk_area_state_id_from AS fkAreaStateIdFrom,
    ms.fk_area_city_id_from AS fkAreaCityIdFrom,
    ms.fk_area_city_division_id_from AS fkAreaCityDivisionIdFrom,
    ms.contact_address_1 AS contactAddress1,
    ms.contact_address_2 AS contactAddress2,
    ms.contact_postcode AS contactPostcode,
    ms.birthday AS birthday,
    ms.gender AS gender,
    ms.title AS title,
    ms.fk_student_app_country_id AS fkStudentAppCountryId,
    ms.fk_student_app_ancestry_id AS fkStudentAppAncestryId,
    ms.fk_student_app_mother_language_id AS fkStudentAppMotherLanguageId,
    ms.fk_student_app_country_id_birth AS fkStudentAppCountryIdBirth,
    ms.city_birth AS cityBirth,
    ms.is_lived_since_birth AS isLivedSinceBirth,
    ms.fk_student_app_country_id_live AS fkStudentAppCountryIdLive,
    ms.migration_time AS migrationTime,
    ms.staying_right_time AS stayingRightTime,
    ms.fk_student_app_country_id_passport AS fkStudentAppCountryIdPassport,
    ms.is_have_passport AS isHavePassport,
    ms.passport_issue_date AS passportIssueDate,
    ms.passport_issue_expried AS passportIssueExpried,
    ms.passport_number AS passportNumber,
    ms.follow_up_phone_code_1 AS followUpPhoneCode1,
    ms.follow_up_phone_code_2 AS followUpPhoneCode2,
    ms.follow_up_phone_code_3 AS followUpPhoneCode3,
    ms.follow_up_phone_number_1 AS followUpPhoneNumber1,
    ms.follow_up_phone_number_2 AS followUpPhoneNumber2,
    ms.follow_up_phone_number_3 AS followUpPhoneNumber3,
    ms.follow_up_email_1 AS followUpEmail1,
    ms.follow_up_email_2 AS followUpEmail2,
    ms.follow_up_email_3 AS followUpEmail3,
    ms.father_last_name AS fatherLastName,
    ms.father_first_name AS fatherFirstName,
    ms.father_email AS fatherEmail,
    ms.father_phone_number AS fatherPhoneNumber,
    ms.monter_last_name AS monterLastName,
    ms.monter_first_name AS monterFirstName,
    ms.monter_email AS monterEmail,
    ms.monter_phone_number AS monterPhoneNumber,
    ms.parent_contact_postcode AS parentContactPostcode,
    ms.parent_contact_address1 AS parentContactAddress1,
    ms.parent_contact_address2 AS parentContactAddress2,
    ms.fk_student_app_country_id_parent AS fkStudentAppCountryIdParent,
    ms.fk_area_state_id_parent AS fkAreaStateIdParent,
    ms.fk_area_city_id_parent AS fkAreaCityIdParent,
    ms.is_need_accommodation AS isNeedAccommodation,
    ms.accommodation_type AS accommodationType,
    ms.is_accommodation_smoke AS isAccommodationSmoke,
    ms.is_accommodation_pet AS isAccommodationPet,
    ms.is_accommodation_children AS isAccommodationChildren,
    ms.is_accommodation_food AS isAccommodationFood,
    ms.accommodation_food_note AS accommodationFoodNote,
    ms.is_accommodation_other AS isAccommodationOther,
    ms.accommodation_other_note AS accommodationOtherNote,
    ms.is_need_pick_up AS isNeedPickUp,
    ms.`status` AS status,
    ms.gmt_create AS gmtCreate,
    ms.gmt_create_user AS gmtCreateUser,
    ms.gmt_modified AS gmtModified,
    ms.gmt_modified_user AS gmtModifiedUser,
    GROUP_CONCAT(distinct msic.fk_area_country_id) AS targetCountryId
    from m_student ms
           left join m_student_institution_course msic on ms.id = msic.fk_student_id
    <where>
      <if test="studentInfoVo.fkAgentId != null">
        and ms.fk_agent_id = #{studentInfoVo.fkAgentId}
      </if>
      <if test="studentInfoVo.nameZh != null and studentInfoVo.nameZh != ''">
        and ms.name_zh like concat("%",#{studentInfoVo.nameZh},"%")
      </if>
      <if test="studentInfoVo.nameEn != null and studentInfoVo.nameEn != ''">
        and concat(ms.first_name,ms.last_name) like concat("%",#{studentInfoVo.nameEn},"%")
      </if>
      <if test="studentInfoVo.phoneNumber != null and studentInfoVo.phoneNumber != ''">
        and ms.phone_number = #{studentInfoVo.phoneNumber}
      </if>
      <if test="studentInfoVo.gender != null">
        and ms.gender = #{studentInfoVo.gender}
      </if>
      <if test="studentInfoVo.email != null and studentInfoVo.email != ''">
        and ms.email = #{studentInfoVo.email}
      </if>
      <if test="studentInfoVo.fkStudentAppCountryIdFrom != null and studentInfoVo.fkStudentAppCountryIdFrom != ''">
        and ms.fk_student_app_country_id_from = #{studentInfoVo.fkStudentAppCountryIdFrom}
      </if>
      <if test="studentInfoVo.fkAreaStateIdFrom != null and studentInfoVo.fkAreaStateIdFrom != ''">
        and ms.fk_area_state_id_from = #{studentInfoVo.fkAreaStateIdFrom}
      </if>
      <if test="studentInfoVo.fkAreaCityIdFrom != null and studentInfoVo.fkAreaCityIdFrom != ''">
        and ms.fk_area_city_id_from = #{studentInfoVo.fkAreaCityIdFrom}
      </if>
      <if test="studentInfoVo.status != null">
        and ms.status = #{studentInfoVo.status}
      </if>
      <if test="studentInfoVo.createBeginDate != null">
        and DATE_FORMAT(ms.gmt_create,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{studentInfoVo.createBeginDate},'%Y-%m-%d')
      </if>
      <if test="studentInfoVo.createEndDate != null">
        and DATE_FORMAT(ms.gmt_create,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{studentInfoVo.createEndDate},'%Y-%m-%d')
      </if>
      <if test="studentInfoVo.id != null">
        and ms.id = #{studentInfoVo.id}
      </if>
      <if test="studentInfoVo.targetCountryId != null">
        and ms.id in (select fk_student_id from m_student_institution_course where fk_area_country_id = #{studentInfoVo.targetCountryId})
      </if>
    </where>
    group by ms.id
    order by ms.gmt_create desc
  </select>

  <update id="updateStudentStatusByStudentId">
    update
    m_student set
    status = #{status,jdbcType=INTEGER},
    gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
    gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>