package com.get.platformconfigcenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.platformconfigcenter.entity.Notice;
import com.get.platformconfigcenter.mapper.NoticeMapper;
import com.get.platformconfigcenter.service.INoticeService;
import org.springframework.stereotype.Service;

/**
 * 服务实现类
 */
@Service
public class NoticeServiceImpl extends BaseServiceImpl<NoticeMapper, Notice> implements INoticeService {

    @Override
    public IPage<Notice> selectNoticePage(IPage<Notice> page, Notice notice) {
        return page.setRecords(baseMapper.selectNoticePage(page, notice));
    }

}
