package com.get.partnercenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.institutioncenter.dto.InstitutionApplicationMetricsDto;
import com.get.partnercenter.dto.MEventParamsDto;
import com.get.partnercenter.dto.PutAwayParamsDto;
import com.get.partnercenter.entity.MEventRegistrationAgentEntity;
import com.get.partnercenter.mapper.MEventRegistrationAgentMapper;
import com.get.partnercenter.service.MEventService;
import com.get.partnercenter.vo.MEventRegistrationAgentVo;
import com.get.partnercenter.vo.MEventVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

@Api(tags = "后台管理-热门活动")
@RestController
@RequestMapping("partner/mEvent")
public class MEventController {

    @Autowired
    private MEventService mEventService;


    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.LIST, description = "华通伙伴/热门活动/列表查询")
    @PostMapping("searchPage")
    public ResponseBo<MEventVo> searchPage(@RequestBody SearchBean<MEventParamsDto> page) {
        List<MEventVo> datas = mEventService.searchPage(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    @ApiOperation(value = "新增(修改)接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/热门活动/新增(修改)")
    @PostMapping("saveOrUpdate")
    public ResponseBo saveOrUpdate(@RequestBody @Validated(value = {BaseVoEntity.Add.class}) MEventParamsDto meventdto){
        return SaveResponseBo.ok(mEventService.saveOrUpdateMEvent(meventdto));
    }


    @ApiOperation(value = "数据删除", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.DETAIL, description = "华通伙伴/热门活动/删除")
    @PostMapping("/delete/{id}")
    public ResponseBo<MEventVo> delete(@PathVariable Long id){
        mEventService.removeByIdInfo(id);
        return SaveResponseBo.ok();
    }


    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.DETAIL, description = "华通伙伴/热门活动/详情")
    @GetMapping("/getDetail/{id}")
    public ResponseBo<MEventVo> getDetail(@PathVariable Long id){
        MEventVo data=mEventService.selectByDetail(id);

        return new ResponseBo<>(data);
    }

    @ApiOperation(value = "一键(上架)下架-接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/热门活动/一键(上架)下架-接口")
    @PostMapping("putAway")
    public ResponseBo putAway(@RequestBody  @Valid PutAwayParamsDto params){
        return SaveResponseBo.ok(mEventService.putAway(params));
    }

    @ApiOperation(value = "报名列表")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.LIST, description = "华通伙伴/热门活动/报名列表")
    @PostMapping("searchRegistration")
    public ResponseBo<MEventRegistrationAgentVo> searchRegistration(@RequestBody SearchBean<MEventParamsDto> page) {
        List<MEventRegistrationAgentVo> datas = mEventService.searchRegistration(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "导出报名列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.LIST, description = "华通伙伴/热门活动/导出报名列表")
    @PostMapping("exportRegistration")
    public void exportRegistration(HttpServletResponse response, @RequestBody MEventParamsDto paramsDto) {
        mEventService.exportRegistration(response,paramsDto);
    }




    @ApiOperation(value = "修改报名状态")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.LIST, description = "华通伙伴/热门活动/修改报名状态")
    @PostMapping("modifyRegistration")
    public ResponseBo<MEventRegistrationAgentVo> modifyRegistration(@RequestBody MEventRegistrationAgentEntity params) {
        return SaveResponseBo.ok(mEventService.modifyRegistration(params));
    }

    @ApiOperation(value = "活动引用", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/热门活动/活动引用")
    @PostMapping("quoteMevent")
    public ResponseBo quoteMevent(@RequestBody MEventParamsDto meventdto){
        return SaveResponseBo.ok(mEventService.quoteMevent(meventdto));
    }

}
