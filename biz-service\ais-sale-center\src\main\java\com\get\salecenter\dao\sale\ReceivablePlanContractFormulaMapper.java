package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.ReceivablePlanContractFormula;
import com.get.salecenter.dto.GenerateMatchingPlanDto;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;

@Mapper
public interface ReceivablePlanContractFormulaMapper extends BaseMapper<ReceivablePlanContractFormula> {
    int insert(ReceivablePlanContractFormula record);

    int insertSelective(ReceivablePlanContractFormula record);

    boolean isExistByFormulaId(Long id);

    /**
     * 获取该学习计划 该合同公式 已生成的应收计划总金额（用于计算佣金上限）
     *
     * @Date 18:27 2022/1/24
     * <AUTHOR>
     */
    BigDecimal getReceivableTotalAmount(GenerateMatchingPlanDto generateMatchingPlanDto);

    /**
     * 获取该学习计划 该合同公式 已生成的应付计划总金额（用于计算佣金上限）
     *
     * @Date 18:27 2022/1/24
     * <AUTHOR>
     */
    BigDecimal getPayableTotalAmount(GenerateMatchingPlanDto generateMatchingPlanDto);
}