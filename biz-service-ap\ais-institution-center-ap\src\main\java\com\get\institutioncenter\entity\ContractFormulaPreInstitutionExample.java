package com.get.institutioncenter.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ContractFormulaPreInstitutionExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ContractFormulaPreInstitutionExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andFkContractFormulaIdIsNull() {
            addCriterion("fk_contract_formula_id is null");
            return (Criteria) this;
        }

        public Criteria andFkContractFormulaIdIsNotNull() {
            addCriterion("fk_contract_formula_id is not null");
            return (Criteria) this;
        }

        public Criteria andFkContractFormulaIdEqualTo(Long value) {
            addCriterion("fk_contract_formula_id =", value, "fkContractFormulaId");
            return (Criteria) this;
        }

        public Criteria andFkContractFormulaIdNotEqualTo(Long value) {
            addCriterion("fk_contract_formula_id <>", value, "fkContractFormulaId");
            return (Criteria) this;
        }

        public Criteria andFkContractFormulaIdGreaterThan(Long value) {
            addCriterion("fk_contract_formula_id >", value, "fkContractFormulaId");
            return (Criteria) this;
        }

        public Criteria andFkContractFormulaIdGreaterThanOrEqualTo(Long value) {
            addCriterion("fk_contract_formula_id >=", value, "fkContractFormulaId");
            return (Criteria) this;
        }

        public Criteria andFkContractFormulaIdLessThan(Long value) {
            addCriterion("fk_contract_formula_id <", value, "fkContractFormulaId");
            return (Criteria) this;
        }

        public Criteria andFkContractFormulaIdLessThanOrEqualTo(Long value) {
            addCriterion("fk_contract_formula_id <=", value, "fkContractFormulaId");
            return (Criteria) this;
        }

        public Criteria andFkContractFormulaIdIn(List<Long> values) {
            addCriterion("fk_contract_formula_id in", values, "fkContractFormulaId");
            return (Criteria) this;
        }

        public Criteria andFkContractFormulaIdNotIn(List<Long> values) {
            addCriterion("fk_contract_formula_id not in", values, "fkContractFormulaId");
            return (Criteria) this;
        }

        public Criteria andFkContractFormulaIdBetween(Long value1, Long value2) {
            addCriterion("fk_contract_formula_id between", value1, value2, "fkContractFormulaId");
            return (Criteria) this;
        }

        public Criteria andFkContractFormulaIdNotBetween(Long value1, Long value2) {
            addCriterion("fk_contract_formula_id not between", value1, value2, "fkContractFormulaId");
            return (Criteria) this;
        }

        public Criteria andFkInstitutionIdIsNull() {
            addCriterion("fk_institution_id is null");
            return (Criteria) this;
        }

        public Criteria andFkInstitutionIdIsNotNull() {
            addCriterion("fk_institution_id is not null");
            return (Criteria) this;
        }

        public Criteria andFkInstitutionIdEqualTo(Long value) {
            addCriterion("fk_institution_id =", value, "fkInstitutionId");
            return (Criteria) this;
        }

        public Criteria andFkInstitutionIdNotEqualTo(Long value) {
            addCriterion("fk_institution_id <>", value, "fkInstitutionId");
            return (Criteria) this;
        }

        public Criteria andFkInstitutionIdGreaterThan(Long value) {
            addCriterion("fk_institution_id >", value, "fkInstitutionId");
            return (Criteria) this;
        }

        public Criteria andFkInstitutionIdGreaterThanOrEqualTo(Long value) {
            addCriterion("fk_institution_id >=", value, "fkInstitutionId");
            return (Criteria) this;
        }

        public Criteria andFkInstitutionIdLessThan(Long value) {
            addCriterion("fk_institution_id <", value, "fkInstitutionId");
            return (Criteria) this;
        }

        public Criteria andFkInstitutionIdLessThanOrEqualTo(Long value) {
            addCriterion("fk_institution_id <=", value, "fkInstitutionId");
            return (Criteria) this;
        }

        public Criteria andFkInstitutionIdIn(List<Long> values) {
            addCriterion("fk_institution_id in", values, "fkInstitutionId");
            return (Criteria) this;
        }

        public Criteria andFkInstitutionIdNotIn(List<Long> values) {
            addCriterion("fk_institution_id not in", values, "fkInstitutionId");
            return (Criteria) this;
        }

        public Criteria andFkInstitutionIdBetween(Long value1, Long value2) {
            addCriterion("fk_institution_id between", value1, value2, "fkInstitutionId");
            return (Criteria) this;
        }

        public Criteria andFkInstitutionIdNotBetween(Long value1, Long value2) {
            addCriterion("fk_institution_id not between", value1, value2, "fkInstitutionId");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateUserIsNull() {
            addCriterion("gmt_create_user is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateUserIsNotNull() {
            addCriterion("gmt_create_user is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateUserEqualTo(String value) {
            addCriterion("gmt_create_user =", value, "gmtCreateUser");
            return (Criteria) this;
        }

        public Criteria andGmtCreateUserNotEqualTo(String value) {
            addCriterion("gmt_create_user <>", value, "gmtCreateUser");
            return (Criteria) this;
        }

        public Criteria andGmtCreateUserGreaterThan(String value) {
            addCriterion("gmt_create_user >", value, "gmtCreateUser");
            return (Criteria) this;
        }

        public Criteria andGmtCreateUserGreaterThanOrEqualTo(String value) {
            addCriterion("gmt_create_user >=", value, "gmtCreateUser");
            return (Criteria) this;
        }

        public Criteria andGmtCreateUserLessThan(String value) {
            addCriterion("gmt_create_user <", value, "gmtCreateUser");
            return (Criteria) this;
        }

        public Criteria andGmtCreateUserLessThanOrEqualTo(String value) {
            addCriterion("gmt_create_user <=", value, "gmtCreateUser");
            return (Criteria) this;
        }

        public Criteria andGmtCreateUserLike(String value) {
            addCriterion("gmt_create_user like", value, "gmtCreateUser");
            return (Criteria) this;
        }

        public Criteria andGmtCreateUserNotLike(String value) {
            addCriterion("gmt_create_user not like", value, "gmtCreateUser");
            return (Criteria) this;
        }

        public Criteria andGmtCreateUserIn(List<String> values) {
            addCriterion("gmt_create_user in", values, "gmtCreateUser");
            return (Criteria) this;
        }

        public Criteria andGmtCreateUserNotIn(List<String> values) {
            addCriterion("gmt_create_user not in", values, "gmtCreateUser");
            return (Criteria) this;
        }

        public Criteria andGmtCreateUserBetween(String value1, String value2) {
            addCriterion("gmt_create_user between", value1, value2, "gmtCreateUser");
            return (Criteria) this;
        }

        public Criteria andGmtCreateUserNotBetween(String value1, String value2) {
            addCriterion("gmt_create_user not between", value1, value2, "gmtCreateUser");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedUserIsNull() {
            addCriterion("gmt_modified_user is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedUserIsNotNull() {
            addCriterion("gmt_modified_user is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedUserEqualTo(String value) {
            addCriterion("gmt_modified_user =", value, "gmtModifiedUser");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedUserNotEqualTo(String value) {
            addCriterion("gmt_modified_user <>", value, "gmtModifiedUser");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedUserGreaterThan(String value) {
            addCriterion("gmt_modified_user >", value, "gmtModifiedUser");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedUserGreaterThanOrEqualTo(String value) {
            addCriterion("gmt_modified_user >=", value, "gmtModifiedUser");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedUserLessThan(String value) {
            addCriterion("gmt_modified_user <", value, "gmtModifiedUser");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedUserLessThanOrEqualTo(String value) {
            addCriterion("gmt_modified_user <=", value, "gmtModifiedUser");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedUserLike(String value) {
            addCriterion("gmt_modified_user like", value, "gmtModifiedUser");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedUserNotLike(String value) {
            addCriterion("gmt_modified_user not like", value, "gmtModifiedUser");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedUserIn(List<String> values) {
            addCriterion("gmt_modified_user in", values, "gmtModifiedUser");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedUserNotIn(List<String> values) {
            addCriterion("gmt_modified_user not in", values, "gmtModifiedUser");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedUserBetween(String value1, String value2) {
            addCriterion("gmt_modified_user between", value1, value2, "gmtModifiedUser");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedUserNotBetween(String value1, String value2) {
            addCriterion("gmt_modified_user not between", value1, value2, "gmtModifiedUser");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }
    }
}