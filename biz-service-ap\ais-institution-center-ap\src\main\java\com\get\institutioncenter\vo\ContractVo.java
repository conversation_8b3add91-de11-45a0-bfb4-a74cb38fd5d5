package com.get.institutioncenter.vo;

import com.get.common.annotion.TableDto;
import com.get.institutioncenter.entity.Contract;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/20
 * @TIME: 17:48
 * @Description: 合同管理DTO
 **/
@Data
public class ContractVo extends Contract implements Serializable {

    @ApiModelProperty(value = "学校提供商名称")
    private String fkInstitutionProviderName;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校名称")
    private String fkInstitutionName;

    /**
     * 学校课程Id
     */
    @ApiModelProperty(value = "学校课程名称")
    private String fkInstitutionCourseName;

    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    @TableDto(tableName = "u_contract_type", columnDto = "type_name", entityColumnDto = "fkContractTypeName", columnDtoMainId = "fk_contract_type_id")
    private String fkContractTypeName;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    /**
     * 流程状态
     */
    @ApiModelProperty(value = "流程状态")
    private Integer status;
    /**
     * 定义下拉枚举：0新签/1续签
     */
    @ApiModelProperty(value = "定义下拉枚举：0新签/1续签")
    private List<String> contractApprovalModeEnum;
    /**
     * 任务版本号
     */
    @ApiModelProperty("任务版本号")
    private Integer taskVersion;
    /**
     * 任务id
     */
    @ApiModelProperty("任务id")
    private String taskId;
    /**
     * 待签或代表
     */
    @ApiModelProperty("待签或代表")
    private int signOrGetStatus;
    /**
     * 流程实例id
     */
    @ApiModelProperty("流程实例id")
    private String procInstId;
    /**
     * 创建人公司id
     */
    @ApiModelProperty("创建人公司id")
    private Long fkcompanyId;

    @ApiModelProperty("发起人id")
    private Long fkStaffId;
    /**
     * 同表父id
     */
    @ApiModelProperty("同表父id")
    private Long fkTableParentId;
    /**
     * 同意按钮状态
     */
    @ApiModelProperty(value = "同意按钮状态")
    private Boolean agreeButtonType;

    /**
     * 拒绝按钮状态
     */
    @ApiModelProperty(value = "拒绝按钮状态")
    private Boolean refuseButtonType;

    //附件列表
    @ApiModelProperty(value ="附件列表")
    private List<MediaAndAttachedVo> mediaAndAttachedDto;

    //=================实体类Contract=======================
    private static final long serialVersionUID = 1L;
    /**
     * 学校提供商Id
     */
    @ApiModelProperty(value = "学校提供商Id")
    @Column(name = "fk_institution_provider_id")
    private Long fkInstitutionProviderId;
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;
    /**
     * 学校课程Id
     */
    @ApiModelProperty(value = "学校课程Id")
    @Column(name = "fk_institution_course_id")
    private Long fkInstitutionCourseId;
    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    @Column(name = "fk_contract_type_id")
    private Long fkContractTypeId;
    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @Column(name = "contract_num")
    private String contractNum;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @Column(name = "title")
    private String title;
    /**
     * 合同开始时间
     */
    @ApiModelProperty(value = "合同开始时间")
    @Column(name = "start_time")
    private Date startTime;
    /**
     * 合同结束时间
     */
    @ApiModelProperty(value = "合同结束时间")
    @Column(name = "end_time")
    private Date endTime;
    /**
     * 市场费用
     */
    @ApiModelProperty(value = "市场费用")
    @Column(name = "market_expense")
    private String marketExpense;
    /**
     * 后续费用
     */
    @ApiModelProperty(value = "后续费用")
    @Column(name = "follow_up_expense")
    private String followUpExpense;
    /**
     * 实际佣金
     */
    @ApiModelProperty(value = "实际佣金")
    @Column(name = "commission_rate")
    private String commissionRate;
    /**
     * 后续佣金
     */
    @ApiModelProperty(value = "后续佣金")
    @Column(name = "follow_up_commission_rate")
    private String followUpCommissionRate;
    /**
     * 合同备注
     */
    @ApiModelProperty(value = "合同备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
    @ApiModelProperty(value = "合同表父id")
    @Column(name = "fk_contract_id_revoke")
    private Long fkContractIdRevoke;

    /**
     * 0新签/1续签
     */
    @ApiModelProperty(value = "0新签/1续签")
    @Column(name = "contract_approval_mode")
    private Integer contractApprovalMode;

    @ApiModelProperty("申请周期，如：1-2周")
    @Column(name = "application_cycle")
    private String applicationCycle;


    @ApiModelProperty("结算周期，如：8-12周")
    @Column(name = "settlement_cycle")
    private String settlementCycle;

}
