package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.vo.MajorLevelVo;
import com.get.institutioncenter.vo.MajorLevelSelectVo;
import com.get.institutioncenter.service.IMajorLevelService;
import com.get.institutioncenter.dto.MajorLevelDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/7/31 12:12
 * @verison: 1.0
 * @description: 专业等级管理控制器
 */
@Api(tags = "专业等级管理")
@RestController
@RequestMapping("/institution/majorLevel")
public class MajorLevelController {

    @Resource
    private IMajorLevelService majorLevelService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/专业等级管理/专业等级详情")
    @GetMapping("/{id}")
    public ResponseBo<MajorLevelVo> detail(@PathVariable("id") Long id) {
        MajorLevelVo data = majorLevelService.findMajorLevelById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 批量新增信息
     *
     * @param majorLevelDtos
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/专业等级管理/新增专业等级")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(MajorLevelDto.Add.class) ValidList<MajorLevelDto> majorLevelDtos) {
        majorLevelService.batchAdd(majorLevelDtos);
        return SaveResponseBo.ok();
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/专业等级管理/删除专业等级")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        majorLevelService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param majorLevelDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/专业等级管理/更新专业等级")
    @PostMapping("update")
    public ResponseBo<MajorLevelVo> update(@RequestBody @Validated(MajorLevelDto.Update.class) MajorLevelDto majorLevelDto) {
        return UpdateResponseBo.ok(majorLevelService.updateMajorLevel(majorLevelDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为搜索关键字(levelName等级名称)")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/专业等级管理/查询专业等级")
    @PostMapping("datas")
    public ResponseBo<MajorLevelVo> datas(@RequestBody SearchBean<MajorLevelDto> page) {
        List<MajorLevelVo> datas = majorLevelService.getMajorLevels(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 上移下移
     *
     * @param majorLevelDtos
     * @return
     * @
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/专业等级管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<MajorLevelDto> majorLevelDtos) {
        majorLevelService.movingOrder(majorLevelDtos);
        return ResponseBo.ok();
    }


    /**
     * 专业等级下拉框
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "专业等级下拉框", notes = "")
    @GetMapping("getMajorLevelSelect")
    public ResponseBo<BaseSelectEntity> getMajorLevelSelect() {
        return new ListResponseBo<>(majorLevelService.getMajorLevelSelect());
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "专业等级下拉框(带组别)", notes = "")
    @GetMapping("getMajorLevelAndGroupSelect")
    public ResponseBo<MajorLevelSelectVo> getMajorLevelAndGroupSelect() {
        return new ListResponseBo<>(majorLevelService.getMajorLevelAndGroupSelect());
    }

    /**
     * @Description :feign调用 通过课程等级ids 查找对应的课程等级名称map
     * @Param [ids]
     * <AUTHOR>
     */
/*    @ApiIgnore
    @PostMapping(value = "getMajorLevelNamesByIds")
    @VerifyLogin(IsVerify = false)
    public Map<Long, String> getMajorLevelNamesByIds(@RequestBody Set<Long> ids) {
        return majorLevelService.getMajorLevelNamesByIds(ids);
    }*/

    /**
     * feign调用 通过课程等级id 查找对应的课程等级名称
     *
     * @Date 16:23 2021/5/24
     * <AUTHOR>
     */
 /*   @ApiIgnore
    @GetMapping(value = "getMajorLevelNamesById")
    public String getMajorLevelNamesById(@RequestParam("id") Long id)  {
        return majorLevelService.getMajorLevelNamesById(id);
    }*/
    @ApiIgnore
    @PostMapping(value = "getMajorLevelNamesByCourIds")
    public Map<Long, String> getMajorLevelNamesByCourIds(@RequestParam("ids") Set<Long> ids) {
        return majorLevelService.getMajorLevelNamesByCourIds(ids);
    }

    /**
     * 学校类型下拉框数据
     *
     * @return
     */
    @ApiIgnore
    @GetMapping("getMajorLevelIdStringByCourseId")
    public String getMajorLevelIdStringByCourseId(@RequestParam Long id) {
        return majorLevelService.getMajorLevelIdStringByCourseId(id);
    }
}
