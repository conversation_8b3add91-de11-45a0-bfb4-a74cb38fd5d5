package com.get.aisplatformcenterap.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_user_type")
public class UserTypeEntity extends BaseEntity implements Serializable {

    @ApiModelProperty("用户Id")
    @Column(name = "fk_user_id")
    private Long fkUserId;


    @ApiModelProperty("用户类型：1=VIP")
    @Column(name = "type")
    private Integer type;
}
