package com.get.salecenter.utils;

import com.get.common.eunms.ProjectKeyEnum;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.enums.EmailLanguageEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 销售中心语言配置工具类
 * 提供语言配置获取和处理功能
 *
 * <AUTHOR>
 * @date 2025-01-18
 * @version 1.0
 */
@Slf4j
public class SaleLanguageConfigUtils {

    /**
     * 根据参数Map智能确定语言代码
     * 优先级：versionValue > staffId > 默认中文
     *
     * @param params 参数Map
     * @param permissionCenterClient 权限中心客户端
     * @return 语言代码（zh/en）
     */
    public static String determineLanguageCode(Map<String, String> params, IPermissionCenterClient permissionCenterClient) {
        if (params == null || params.isEmpty()) {
            return EmailLanguageEnum.CHINESE.getCode();
        }

        // 1. 优先使用参数中的versionValue
        String versionValue = params.get("versionValue");
        if (GeneralTool.isNotEmpty(versionValue)) {
            return EmailLanguageEnum.getLanguageCodeByConfigValue(versionValue);
        }

        // 2. 通过staffId获取公司配置
        String staffIdStr = params.get("staffId");
        if (GeneralTool.isNotEmpty(staffIdStr)) {
            try {
                Long staffId = Long.valueOf(staffIdStr);
                return getLanguageCodeByStaffId(staffId, permissionCenterClient);
            } catch (NumberFormatException e) {
                log.warn("staffId格式错误: {}", staffIdStr);
            }
        }

        // 3. 默认返回中文
        return EmailLanguageEnum.CHINESE.getCode();
    }

    /**
     * 通过员工ID获取语言代码
     *
     * @param staffId 员工ID
     * @param permissionCenterClient 权限中心客户端
     * @return 语言代码（zh/en）
     */
    public static String getLanguageCodeByStaffId(Long staffId, IPermissionCenterClient permissionCenterClient) {
        if (staffId == null) {
            log.warn("员工ID为空，返回默认语言代码");
            return EmailLanguageEnum.CHINESE.getCode();
        }

        try {
            // 1. 获取员工信息
            StaffVo staffVo = getStaffInfo(staffId, permissionCenterClient);
            if (staffVo == null || staffVo.getFkCompanyId() == null) {
                log.warn("员工信息不存在或公司ID为空, staffId: {}", staffId);
                return EmailLanguageEnum.CHINESE.getCode();
            }

            // 2. 通过公司ID获取语言配置
            return getLanguageCodeByCompanyId(staffVo.getFkCompanyId(), permissionCenterClient);

        } catch (Exception e) {
            log.error("获取员工语言配置失败, staffId: {}, 错误: {}", staffId, e.getMessage(), e);
            return EmailLanguageEnum.CHINESE.getCode();
        }
    }

    /**
     * 通过公司ID获取语言代码
     *
     * @param companyId 公司ID
     * @param permissionCenterClient 权限中心客户端
     * @return 语言代码（zh/en）
     */
    public static String getLanguageCodeByCompanyId(Long companyId, IPermissionCenterClient permissionCenterClient) {
        if (companyId == null) {
            log.warn("公司ID为空，返回默认语言代码");
            return EmailLanguageEnum.CHINESE.getCode();
        }

        try {
            // 获取公司的语言配置
            Map<Long, String> versionConfigMap = permissionCenterClient
                    .getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1)
                    .getData();
            
            if (versionConfigMap == null) {
                log.warn("获取公司语言配置失败, companyId: {}", companyId);
                return EmailLanguageEnum.CHINESE.getCode();
            }

            // 获取公司的语言配置值
            String configValue = versionConfigMap.get(companyId);
            return EmailLanguageEnum.getLanguageCodeByConfigValue(configValue);

        } catch (Exception e) {
            log.error("获取公司语言配置失败, companyId: {}, 错误: {}", companyId, e.getMessage(), e);
            return EmailLanguageEnum.CHINESE.getCode();
        }
    }

    /**
     * 获取员工信息
     *
     * @param staffId 员工ID
     * @param permissionCenterClient 权限中心客户端
     * @return 员工信息
     */
    public static StaffVo getStaffInfo(Long staffId, IPermissionCenterClient permissionCenterClient) {
        if (staffId == null) {
            log.warn("员工ID为空，无法获取员工信息");
            return null;
        }

        try {
            return permissionCenterClient.getStaffById(staffId).getData();
        } catch (Exception e) {
            log.error("获取员工信息失败, staffId: {}, 错误: {}", staffId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 判断是否为英文语言
     *
     * @param languageCode 语言代码
     * @return true表示英文，false表示非英文
     */
    public static boolean isEnglish(String languageCode) {
        return EmailLanguageEnum.isEnglish(languageCode);
    }

    /**
     * 判断是否为中文语言
     *
     * @param languageCode 语言代码
     * @return true表示中文，false表示非中文
     */
    public static boolean isChinese(String languageCode) {
        return EmailLanguageEnum.isChinese(languageCode);
    }

    /**
     * 验证语言代码是否有效
     *
     * @param languageCode 语言代码
     * @return true表示有效，false表示无效
     */
    public static boolean isValidLanguageCode(String languageCode) {
        return EmailLanguageEnum.isValidLanguageCode(languageCode);
    }

    /**
     * 根据公司配置值获取语言代码
     * 如果配置值为"en"则返回英文，否则返回中文（包括null和其他值）
     *
     * @param configValue 公司配置值
     * @return 语言代码
     */
    public static String getLanguageCodeByConfigValue(String configValue) {
        return EmailLanguageEnum.getLanguageCodeByConfigValue(configValue);
    }

    /**
     * 根据语言代码构建国际化标题
     * 
     * @param languageCode 语言代码
     * @param chineseTitle 中文标题
     * @param englishTitle 英文标题
     * @return 对应语言的标题
     */
    public static String buildInternationalTitle(String languageCode, String chineseTitle, String englishTitle) {
        if (isEnglish(languageCode)) {
            return GeneralTool.isNotEmpty(englishTitle) ? englishTitle : chineseTitle;
        } else {
            return GeneralTool.isNotEmpty(chineseTitle) ? chineseTitle : englishTitle;
        }
    }

    /**
     * 获取默认语言代码
     *
     * @return 默认语言代码（中文）
     */
    public static String getDefaultLanguageCode() {
        return EmailLanguageEnum.CHINESE.getCode();
    }

}