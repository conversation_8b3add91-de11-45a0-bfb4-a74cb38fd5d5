package com.get.institutioncenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.dto.ContractCompanyDto;
import com.get.institutioncenter.vo.CompanyTreeVo;
import com.get.institutioncenter.vo.ContractCompanyVo;
import com.get.institutioncenter.entity.ContractCompany;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/10/29
 * @TIME: 10:23
 * @Description:
 **/
public interface IContractCompanyService extends BaseService<ContractCompany> {

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据公司ids 获取合同ids
     * @Param [companyIds]
     * <AUTHOR>
     */
    List<ContractCompanyVo> getContractCompany(List<Long> companyIds);


    /**
     * @return void
     * @Description: 安全配置
     * @Param [contractCompanyDtos]
     * <AUTHOR>
     */
    void editContractCompanyRelation(List<ContractCompanyDto> contractCompanyDtos);

    /**
     * @return java.lang.Long
     * @Description: 添加关系
     * @Param [contractCompanyDto]
     * <AUTHOR>
     */
    Long addRelation(ContractCompanyDto contractCompanyDto);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.CompanyTreeVo>
     * @Description: 合同和公司的关系（数据回显）
     * @Param [contractId]
     * <AUTHOR>
     */
    List<CompanyTreeVo> getContractCompanyRelation(Long contractId);

}
