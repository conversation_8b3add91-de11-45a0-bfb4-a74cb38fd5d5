package com.get.core.log.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2021/4/15
 * @TIME: 18:13
 * @Description:
 **/
@Data
public class LogInstitutionProviderVo extends BaseVoEntity {
    /**
     * 学校供应商Id
     */
    @ApiModelProperty(value = "学校供应商Id")
    private Long fkInstitutionProviderId;

    /**
     * 操作类型：更新集团/UPDATE_INSTITUTION_GROUP, 更新渠道/UPDATE_INSTITUTION_CHANNEL
     */
    @ApiModelProperty(value = "操作类型：更新集团/UPDATE_INSTITUTION_GROUP, 更新渠道/UPDATE_INSTITUTION_CHANNEL")
    private String optType;

    /**
     * 操作信息，格式如：AAAA -> BBBB, CCCC -> DDDD
     */
    @ApiModelProperty(value = "操作信息，格式如：AAAA -> BBBB, CCCC -> DDDD")
    private String optInfo;
}
