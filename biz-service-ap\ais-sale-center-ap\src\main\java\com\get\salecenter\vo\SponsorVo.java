package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/5/18 11:06
 * @verison: 1.0
 * @description:
 */
@Data
public class SponsorVo {

    /**
     * 状态：0新建/1已确认/2已收款
     */
    @ApiModelProperty(value = "状态：0新建/1已确认/2已收款")
    private Integer status;

//    /**
//     * 赞助对象数据列表
//     */
//    @ApiModelProperty(value = "赞助对象数据列表")
//    private List<ConventionSponsorVo> conventionSponsorDtos;

    /**
     * 赞助项目列表
     */
    @ApiModelProperty(value = "赞助项目列表")
    private List<ConventionSponsorFeeVo> conventionSponsorFeeDtoList;
}
