package com.get.financecenter.dto;

import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 公司利润表项目
 */
@Data
public class CompanyProfitAndLossItemDto extends BaseEntity {

    @ApiModelProperty("公司id")
    private Long fkCompanyId;

    @ApiModelProperty("项目名称")
    private String title;

    @ApiModelProperty("显示模式：Code/Expand/Sum")
    private String showMode;

    @ApiModelProperty("科目Id")
    @UpdateWithNull
    private Long fkAccountingItemId;

    @ApiModelProperty("加减方向：1/-1")
    @UpdateWithNull
    private Integer directionValue;

    @ApiModelProperty("排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty("颜色代码RGB")
    @UpdateWithNull
    private String colorCode;


}

