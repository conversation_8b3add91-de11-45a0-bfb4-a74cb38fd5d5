package com.get.pmpcenter.strategy.workbench;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.permissioncenter.enums.WorkbenchApprovalTypeEnum;
import com.get.permissioncenter.vo.workbench.WorkbenchApprovalVo;
import com.get.pmpcenter.dto.agent.PmpWorkbenchApprovalDto;
import com.get.pmpcenter.entity.InstitutionProviderCommissionPlan;
import com.get.pmpcenter.entity.InstitutionProviderCommissionPlanApproval;
import com.get.pmpcenter.enums.ApprovalStatusEnum;
import com.get.pmpcenter.enums.ProviderCommissionPlanApprovalTypeEnum;
import com.get.pmpcenter.mapper.InstitutionProviderCommissionPlanApprovalMapper;
import com.get.pmpcenter.mapper.InstitutionProviderCommissionPlanMapper;
import com.get.pmpcenter.mapper.PermissionCenterMapper;
import com.get.pmpcenter.strategy.WorkbenchApprovalStrategy;
import com.get.pmpcenter.vo.common.StaffVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/4/27
 * @Version 1.0
 * @apiNote:
 */
@Component
public class ProviderCommissionPlanTerritoryApprovalStrategy implements WorkbenchApprovalStrategy {

    @Autowired
    private InstitutionProviderCommissionPlanApprovalMapper commissionPlanApprovalMapper;
    @Autowired
    private PermissionCenterMapper permissionCenterMapper;
    @Autowired
    private InstitutionProviderCommissionPlanMapper institutionProviderCommissionPlanMapper;

    @Override
    public List<WorkbenchApprovalVo> getApprovalList(PmpWorkbenchApprovalDto workbenchApprovalDto) {
        List<InstitutionProviderCommissionPlanApproval> approvals;
        if (Objects.nonNull(workbenchApprovalDto.getApprovalStatus())) {
            approvals = getBatchApprovalListByStatus(workbenchApprovalDto.getStaffId()
                    , workbenchApprovalDto.getApprovalStatus(), workbenchApprovalDto.getLoginId());
        } else {
            approvals = new ArrayList<>();
            List<InstitutionProviderCommissionPlanApproval> finalApprovals = approvals;
            Arrays.asList(ApprovalStatusEnum.PENDING_APPROVAL.getCode(), ApprovalStatusEnum.REJECT.getCode()).stream().forEach(status -> {
                List<InstitutionProviderCommissionPlanApproval> statusList = getBatchApprovalListByStatus(workbenchApprovalDto.getStaffId(),
                        status, workbenchApprovalDto.getLoginId());
                finalApprovals.addAll(statusList);
            });
        }
        if (CollectionUtils.isNotEmpty(approvals)) {
            List<String> initiatorLoginIds = approvals.stream().map(InstitutionProviderCommissionPlanApproval::getGmtCreateUser).distinct().collect(Collectors.toList());
            List<String> passOrRejectPlanIds = approvals.stream().filter(approval -> approval.getApprovalStatus() >= ApprovalStatusEnum.PASS.getCode())
                    .map(InstitutionProviderCommissionPlanApproval::getFkInstitutionProviderCommissionPlanIds)
                    .collect(Collectors.toList());
            Map<String, String> passOrRejectPlanApprovalMap = Collections.emptyMap();
            if (CollectionUtils.isNotEmpty(passOrRejectPlanIds)) {
                passOrRejectPlanApprovalMap = commissionPlanApprovalMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanApproval>()
                                .eq(InstitutionProviderCommissionPlanApproval::getTypeKey, ProviderCommissionPlanApprovalTypeEnum.MASS.getCode())
                                .eq(InstitutionProviderCommissionPlanApproval::getApprovalStatus, ApprovalStatusEnum.PENDING_APPROVAL.getCode())
                                .in(InstitutionProviderCommissionPlanApproval::getFkInstitutionProviderCommissionPlanIds, passOrRejectPlanIds))
                        .stream()
                        .collect(Collectors.groupingBy(
                                InstitutionProviderCommissionPlanApproval::getFkInstitutionProviderCommissionPlanIds,
                                Collectors.collectingAndThen(
                                        Collectors.maxBy(Comparator.comparing(InstitutionProviderCommissionPlanApproval::getGmtCreate)),
                                        opt -> opt.map(InstitutionProviderCommissionPlanApproval::getGmtCreateUser).orElse(null)
                                )
                        ));
                passOrRejectPlanApprovalMap = passOrRejectPlanApprovalMap.entrySet().stream()
                        .filter(entry -> entry.getValue() != null)
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                Map.Entry::getValue
                        ));
                initiatorLoginIds.addAll(passOrRejectPlanApprovalMap.values());
            }
            List<StaffVo> staffVos = permissionCenterMapper.getStaffListByLoginIds(initiatorLoginIds);
            Map<String, String> nameMap = staffVos.stream().collect(Collectors.toMap(StaffVo::getLoginId, StaffVo::getName));
            Map<String, String> finalPassOrRejectPlanApprovalMap = passOrRejectPlanApprovalMap;
            return approvals.stream().map(approval -> {
                WorkbenchApprovalVo vo = new WorkbenchApprovalVo();
                List<String> planIds = Arrays.stream(approval.getFkInstitutionProviderCommissionPlanIds().split(",")).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(planIds)) {
                    List<InstitutionProviderCommissionPlan> planList = institutionProviderCommissionPlanMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
                            .in(InstitutionProviderCommissionPlan::getId, planIds));
                    String planNames = planList.stream().map(InstitutionProviderCommissionPlan::getName).collect(Collectors.joining(","));
                    vo.setName(planNames);
                    Map<String, Object> defData = new HashMap<>();
                    defData.put("contractId", Objects.nonNull(planList.get(0)) ? planList.get(0).getFkInstitutionProviderContractId() : null);
                    defData.put("providerCommissionPlanIds", approval.getFkInstitutionProviderCommissionPlanIds());
                    vo.setDefData(defData);

                }
                vo.setId(approval.getId());
                vo.setApprovalType(WorkbenchApprovalTypeEnum.PMP_PROVIDER_BATCH_TERRITORY.getCode());
                vo.setApprovalStatus(approval.getApprovalStatus());
                vo.setGmtCreate(approval.getGmtCreate());
//                vo.setInitiatorIdName(Objects.nonNull(nameMap.get(approval.getGmtCreateUser()))
//                        ? nameMap.get(approval.getGmtCreateUser()) : "");
                if (approval.getApprovalStatus() >= ApprovalStatusEnum.PASS.getCode()) {
                    String initiatorId = finalPassOrRejectPlanApprovalMap.getOrDefault(approval.getFkInstitutionProviderCommissionPlanIds(), "");
                    if (StringUtils.isNotBlank(initiatorId)) {
                        vo.setInitiatorIdName(nameMap.getOrDefault(initiatorId, ""));
                    }
                } else {
                    vo.setInitiatorIdName(nameMap.getOrDefault(approval.getGmtCreateUser(), ""));
                }
                return vo;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public String getApprovalType() {
        return WorkbenchApprovalTypeEnum.PMP_PROVIDER_BATCH_TERRITORY.getCode();
    }

    private List<InstitutionProviderCommissionPlanApproval> getBatchApprovalListByStatus(Long staffId, Integer approvalStatus, String loginId) {
        //只需要查询待审核和审核失败的方案 只会有1和3两种状态：1：需要我审核的 3：我提交的但是是审核失败的
        if (approvalStatus.equals(ApprovalStatusEnum.PENDING_APPROVAL.getCode())) {
            List<InstitutionProviderCommissionPlanApproval> planApprovals = commissionPlanApprovalMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanApproval>()
                            .eq(InstitutionProviderCommissionPlanApproval::getFkStaffId, staffId)
                            .eq(InstitutionProviderCommissionPlanApproval::getTypeKey, ProviderCommissionPlanApprovalTypeEnum.MASS.getCode())
                            .eq(InstitutionProviderCommissionPlanApproval::getApprovalStatus, ApprovalStatusEnum.PENDING_APPROVAL.getCode())
                            .exists("select 1 from m_institution_provider_commission_plan where" +
                                    " find_in_set(id, m_institution_provider_commission_plan_approval.fk_institution_provider_commission_plan_ids) and approval_status = 1"))
                    .stream()
                    .filter(item -> item.getFkInstitutionProviderCommissionPlanIds() != null)
                    .collect(Collectors.toMap(
                            InstitutionProviderCommissionPlanApproval::getFkInstitutionProviderCommissionPlanIds,
                            Function.identity(),
                            (a, b) -> a.getGmtCreate().after(b.getGmtCreate()) ? a : b))
                    .values()
                    .stream()
                    .collect(Collectors.toList());
            //再校验方案是否已经审核过了的
            return planApprovals.stream().map(approval -> {
                List<InstitutionProviderCommissionPlanApproval> approvalList = commissionPlanApprovalMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanApproval>()
                        .eq(InstitutionProviderCommissionPlanApproval::getFkInstitutionProviderCommissionPlanIds, approval.getFkInstitutionProviderCommissionPlanIds())
                        .orderByDesc(InstitutionProviderCommissionPlanApproval::getGmtCreate));
                InstitutionProviderCommissionPlanApproval latestPlanApproval = approvalList.get(0);
                if (latestPlanApproval.getApprovalStatus().equals(ApprovalStatusEnum.PASS.getCode())
                        || latestPlanApproval.getApprovalStatus().equals(ApprovalStatusEnum.REJECT.getCode())) {
                    //如果最新的审核记录存在审核通过或者拒绝的记录,就说明该批量审批不是处于待审核的状态
                    return null;
                }
                //如果最新的审核记录不是批量审核,剔除
                if (!latestPlanApproval.getTypeKey().equals(ProviderCommissionPlanApprovalTypeEnum.MASS.getCode())) {
                    return null;
                }
                //如果最新的审核记录的审核人不是当前用户,剔除
                if (!latestPlanApproval.getFkStaffId().equals(staffId)) {
                    return null;
                }
                return approval;
            }).collect(Collectors.toList()).stream().filter(Objects::nonNull).collect(Collectors.toList());
        }
        List<InstitutionProviderCommissionPlanApproval> planApprovals = commissionPlanApprovalMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanApproval>()
                .eq(InstitutionProviderCommissionPlanApproval::getTypeKey, ProviderCommissionPlanApprovalTypeEnum.MASS.getCode())
                .eq(InstitutionProviderCommissionPlanApproval::getApprovalStatus, approvalStatus)
                .exists("select 1 from m_institution_provider_commission_plan where" +
                        " find_in_set(id, m_institution_provider_commission_plan_approval.fk_institution_provider_commission_plan_ids)" +
                        " and approval_status = " + approvalStatus +
                        " and gmt_create_user = '" + loginId + "'"));
        planApprovals = planApprovals.stream()
                .filter(item -> item.getFkInstitutionProviderCommissionPlanIds() != null)
                .collect(Collectors.toMap(
                        InstitutionProviderCommissionPlanApproval::getFkInstitutionProviderCommissionPlanIds,
                        Function.identity(),
                        (a, b) -> a.getGmtCreate().after(b.getGmtCreate()) ? a : b))
                .values()
                .stream()
                .collect(Collectors.toList());
        //再校验方案的最新审核记录是否是审核失败
        return planApprovals.stream().map(approval -> {
            List<InstitutionProviderCommissionPlanApproval> approvalList = commissionPlanApprovalMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanApproval>()
                    .eq(InstitutionProviderCommissionPlanApproval::getFkInstitutionProviderCommissionPlanIds, approval.getFkInstitutionProviderCommissionPlanIds())
                    .orderByDesc(InstitutionProviderCommissionPlanApproval::getGmtCreate));
            InstitutionProviderCommissionPlanApproval latestPlanApproval = approvalList.get(0);
            if (!latestPlanApproval.getApprovalStatus().equals(ApprovalStatusEnum.REJECT.getCode())) {
                //如果存在非拒绝的记录,就说明该批量审批不是处于审核失败的状态
                return null;
            }
            //如果最新的审核记录不是批量审核,剔除
            if (!latestPlanApproval.getTypeKey().equals(ProviderCommissionPlanApprovalTypeEnum.MASS.getCode())) {
                return null;
            }
            return approval;
        }).collect(Collectors.toList()).stream().filter(Objects::nonNull).collect(Collectors.toList());
    }
}
