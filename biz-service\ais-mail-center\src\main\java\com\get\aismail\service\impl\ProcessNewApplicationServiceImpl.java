package com.get.aismail.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.get.aismail.dao.AreaCountryMapper;
import com.get.aismail.dao.MInstitutionMapper;
import com.get.aismail.dao.StudentEducationLevelTypeMapper;
import com.get.aismail.dao.SynonymReferenceMapper;
import com.get.aismail.dto.*;
import com.get.aismail.entity.AreaCountry;
import com.get.aismail.entity.MInstitution;
import com.get.aismail.entity.SynonymReference;
import com.get.aismail.service.IProcessNewApplicationService;
import com.get.aismail.dto.InstitutionCompare;
import com.get.aismail.utils.ProcessNewApplicationUtils;
import com.get.aismail.vo.AddNewStudentVo;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.core.mybatis.base.BaseSelectEntity;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class ProcessNewApplicationServiceImpl implements IProcessNewApplicationService {

    @Autowired
    private AreaCountryMapper areaCountryMapper;

    @Autowired
    private AreaStateServiceImpl areaStateService;

    @Autowired
    private AreaCityServiceImpl areaCityService;

    @Autowired
    private StudentEducationLevelTypeMapper studentEducationLevelTypeMapper;

    @Resource
    private SynonymReferenceMapper synonymReferenceMapper;

    @Resource
    private MInstitutionMapper mInstitutionMapper;


    @Override
    public NewApplicationInfoDto getStudentInfo(XWPFTable table) throws Exception {
        if (table.getText().isEmpty()) {
            return new NewApplicationInfoDto();
        }
        List<AreaCountryDto> areaCountryDtoList = areaCountryMapper.getAllCountryList();
        String name = ProcessNewApplicationUtils.findBestMatchCell("姓名", table).getText();
        String gender = ProcessNewApplicationUtils.findBestMatchCell("性别", table).getText();
        String d = ProcessNewApplicationUtils.findBestMatchCell("出生日期阳历", table).getText();

        Date birthday = null;
        List<SimpleDateFormat> dateFormats = new ArrayList<>();
        dateFormats.add(new SimpleDateFormat("yyyy-MM-dd"));
        dateFormats.add(new SimpleDateFormat("yyyy.MM.dd"));
        dateFormats.add(new SimpleDateFormat("yyyy/MM/dd"));
        dateFormats.add(new SimpleDateFormat("dd/MM/yyyy"));
        dateFormats.add(new SimpleDateFormat("yyyy-MM"));
        dateFormats.add(new SimpleDateFormat("yyyy.MM"));
        dateFormats.add(new SimpleDateFormat("yyyy/MM"));
        dateFormats.add(new SimpleDateFormat("MM/yyyy"));
        dateFormats.add(new SimpleDateFormat("dd/MM"));
        try {
            for (int j = 0; j < dateFormats.size(); j++) {
                SimpleDateFormat dateFormat = dateFormats.get(j);
                try {
                    birthday = dateFormat.parse(d);
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(birthday);
                    int year = calendar.get(Calendar.YEAR);
                    if (year < 1920) {
                        continue;
                    }
                    break;
                } catch (ParseException e3) {
                    if (j == dateFormats.size() - 1) {
                        birthday = null;
                    }
                }
            }
        } catch (Exception e) {
            birthday = null;
        }
        String passport = ProcessNewApplicationUtils.findBestMatchCell("护照号码", table).getText();
        String passportLocation = ProcessNewApplicationUtils.findBestMatchCell("护照签发地", table).getText();
        String nationality = ProcessNewApplicationUtils.findBestMatchCell("国籍地区", table).getText();
        String idCardLocation = ProcessNewApplicationUtils.findBestMatchCell("永久居留许可证国家", table).getText();
        if (idCardLocation != null && !idCardLocation.isEmpty()) {
            LambdaQueryWrapper<SynonymReference> synonymReferenceLambdaQueryWrapper = new LambdaQueryWrapper<>();
            synonymReferenceLambdaQueryWrapper.like(SynonymReference::getInputTerm, idCardLocation);
            List<SynonymReference> synonymReference = synonymReferenceMapper.selectList(synonymReferenceLambdaQueryWrapper);
            if (!synonymReference.isEmpty()) {
                idCardLocation = synonymReference.get(0).getStandardTerm();
            }
        }
        String bornCountry = ProcessNewApplicationUtils.findBestMatchCell("出生地国家地区", table).getText();
        String bornProvince = ProcessNewApplicationUtils.findBestMatchCell("出生地省份", table).getText();
        String bornCity = ProcessNewApplicationUtils.findBestMatchCell("出生地所在市", table).getText();
        String shareLink = ProcessNewApplicationUtils.findBestMatchCell("共享链接", table).getText();
        String remark = ProcessNewApplicationUtils.findBestMatchCell("备注", table).getText();
        AreaCountryDto fkAreaCountryIdPassportDto = new AreaCountryDto();
        AreaCountryDto fkAreaCountryIdNationalityDto = new AreaCountryDto();
        AreaCountryDto fkAreaCountryIdGreenCardDto = new AreaCountryDto();
        AreaCountryDto fkAreaCountryIdBirthDto = new AreaCountryDto();
        double maxLength = 0;
        double maxLength1 = 0;
        double maxLength2 = 0;
        double maxLength3 = 0;

        boolean findPassportLocation = false;
        boolean findNationality = false;
        boolean findIdCardLocation = false;
        boolean findBornCountry = false;
        if (passportLocation != null && !passportLocation.isEmpty()) {
            for (AreaCountryDto areaCountryDto : areaCountryDtoList) {
                int currentLength = ProcessNewApplicationUtils.lcsLength(passportLocation, areaCountryDto.getFullName());
                double matchPercentage = currentLength * 1.0 / areaCountryDto.getFullName().length();
                if (matchPercentage > maxLength && !findPassportLocation) {
                    maxLength = matchPercentage;
                    fkAreaCountryIdPassportDto = areaCountryDto;
                    if (passportLocation.contains(areaCountryDto.getNameChn())) {
                        findPassportLocation = true;
                    }
                }
            }
        }

        if (nationality != null && !nationality.isEmpty()) {
            for (AreaCountryDto areaCountryDto : areaCountryDtoList) {
                int currentLength = ProcessNewApplicationUtils.lcsLength(nationality, areaCountryDto.getFullName());
                double matchPercentage = currentLength * 1.0 / areaCountryDto.getFullName().length();
                if (matchPercentage > maxLength1 && !findNationality) {
                    maxLength1 = matchPercentage;
                    fkAreaCountryIdNationalityDto = areaCountryDto;
                    if (nationality.contains(areaCountryDto.getNameChn())) {
                        findNationality = true;
                    }
                }
            }
        }

        if (bornCountry != null && !bornCountry.isEmpty()) {
            for (AreaCountryDto areaCountryDto : areaCountryDtoList) {
                int currentLength = ProcessNewApplicationUtils.lcsLength(bornCountry, areaCountryDto.getFullName());
                double matchPercentage = currentLength * 1.0 / areaCountryDto.getFullName().length();
                if (matchPercentage > maxLength2 && !findBornCountry) {
                    maxLength2 = matchPercentage;
                    fkAreaCountryIdBirthDto = areaCountryDto;
                    if (bornCountry.contains(areaCountryDto.getNameChn())) {
                        findBornCountry = true;
                    }
                }
            }
        }

        if (idCardLocation != null && !idCardLocation.isEmpty()) {
            for (AreaCountryDto areaCountryDto : areaCountryDtoList) {
                int currentLength = ProcessNewApplicationUtils.lcsLength(idCardLocation, areaCountryDto.getFullName());
                double matchPercentage = currentLength * 1.0 / areaCountryDto.getFullName().length();
                if (matchPercentage > maxLength3 && !findIdCardLocation) {
                    maxLength3 = matchPercentage;
                    fkAreaCountryIdGreenCardDto = areaCountryDto;
                    if (idCardLocation.contains(areaCountryDto.getNameChn())) {
                        findIdCardLocation = true;
                    }
                }
            }
        }
        AreaStateDto fkAreaStateIdBirth = new AreaStateDto();
        boolean findBronProvince = false;
        if (bornProvince != null && !bornProvince.isEmpty()) {
            List<AreaStateDto> areaStateDtoList = areaStateService.getByFkAreaCountryId(fkAreaCountryIdBirthDto.getId());
            maxLength = 0;
            for (AreaStateDto areaStateDto : areaStateDtoList) {
                int currentLength = ProcessNewApplicationUtils.lcsLength(bornProvince, areaStateDto.getFullName().replaceAll("Province", ""));
                double matchPercentage = currentLength * 1.0 / areaStateDto.getFullName().replaceAll("Province", "").length();
                if (!findBronProvince && matchPercentage > maxLength) {
                    maxLength = matchPercentage;
                    fkAreaStateIdBirth = areaStateDto;
                    if (bornProvince.contains(areaStateDto.getNameChn())) {
                        findBronProvince = true;
                    }
                }
            }
        }
        AreaCityDto fkAreaCityIdBirth = new AreaCityDto();
        boolean findBronCity = false;
        if (bornCity != null && !bornCity.isEmpty()) {
            List<AreaCityDto> areaCityDtoList = areaCityService.getByFkAreaStateId(fkAreaStateIdBirth.getId());
            maxLength = 0;
            for (AreaCityDto areaCityDto : areaCityDtoList) {
                int currentLength = ProcessNewApplicationUtils.lcsLength(bornCity, areaCityDto.getFullName());
                double matchPercentage = currentLength * 1.0 / areaCityDto.getFullName().length();
                if (!findBronCity && matchPercentage > maxLength) {
                    maxLength = matchPercentage;
                    fkAreaCityIdBirth = areaCityDto;
                    if (bornCity.contains(areaCityDto.getNameChn())) {
                        findBronCity = true;
                    }
                }
            }
        }
        return new NewApplicationInfoDto(name, gender, birthday, passport, fkAreaCountryIdPassportDto, fkAreaCountryIdNationalityDto, fkAreaCountryIdGreenCardDto, fkAreaCountryIdBirthDto, fkAreaStateIdBirth, fkAreaCityIdBirth, shareLink, remark);
    }

    @Override
    public ContactInformationDto getContactInfo(XWPFTable table) throws Exception {
        if (table.getText().isEmpty()) {
            return new ContactInformationDto();
        }
        String mobilePhoneNumberCode = ProcessNewApplicationUtils.findBestMatchCell("移区", table).getText();
        String mobilePhoneNumber = ProcessNewApplicationUtils.findBestMatchCell("移动电话号码", table).getText();
        String phoneNumberCode = ProcessNewApplicationUtils.findBestMatchCell("固区", table).getText();
        String phoneNumber = ProcessNewApplicationUtils.findBestMatchCell("固定电话号码", table).getText();
        String emailAddress = ProcessNewApplicationUtils.findBestMatchCell("申请电子邮箱", table).getText();
        String residentialZip = ProcessNewApplicationUtils.findBestMatchCell("居住地邮编", table).getText();
        String residentialCountry = ProcessNewApplicationUtils.findBestMatchCell("居住地址国家", table).getText();
        String residentialProvince = ProcessNewApplicationUtils.findBestMatchCell("居住地址省份", table).getText();
        String residentialCity = ProcessNewApplicationUtils.findBestMatchCell("永久居住居住地址所在城市", table).getText();
        String nowResidential = ProcessNewApplicationUtils.findBestMatchCell("居住联系地址永久居住详细地址", table).getText();
        List<AreaCountry> areaCountryList = areaCountryMapper.getAreaCode();
        AreaCountry mobileAreaCode = new AreaCountry();
        AreaCountry areaCodeValue = new AreaCountry();

        if (residentialCountry != null && !residentialCountry.isEmpty()) {
            LambdaQueryWrapper<SynonymReference> synonymReferenceLambdaQueryWrapper = new LambdaQueryWrapper<>();
            synonymReferenceLambdaQueryWrapper.like(SynonymReference::getInputTerm, residentialCountry);
            List<SynonymReference> synonymReference = synonymReferenceMapper.selectList(synonymReferenceLambdaQueryWrapper);
            if (!synonymReference.isEmpty()) {
                residentialCountry = synonymReference.get(0).getStandardTerm();
            }
        }

        if (mobilePhoneNumberCode != null && !mobilePhoneNumberCode.isEmpty()) {
            double maxLengt1 = 0;
            for (AreaCountry areaCountry : areaCountryList) {
                int currentLength = ProcessNewApplicationUtils.lcsLength(mobilePhoneNumberCode, areaCountry.getAreaCode());
                double matchPercentage = currentLength * 1.0 / areaCountry.getAreaCode().length();
                if (matchPercentage > maxLengt1) {
                    maxLengt1 = matchPercentage;
                    mobileAreaCode = areaCountry;
                }
                // 优先选择完全匹配的字符串
                if (areaCountry.getAreaCode().equals(mobilePhoneNumberCode)) {
                    mobileAreaCode = areaCountry;
                    break;
                }

            }
        }
        if (phoneNumberCode != null && !phoneNumberCode.isEmpty()) {
            double maxLength2 = 0;
            for (AreaCountry areaCountry : areaCountryList) {
                int currentLength = ProcessNewApplicationUtils.lcsLength(phoneNumberCode, areaCountry.getAreaCode());
                double matchPercentage = currentLength * 1.0 / areaCountry.getAreaCode().length();
                if (matchPercentage > maxLength2) {
                    maxLength2 = matchPercentage;
                    areaCodeValue = areaCountry;
                }
                if (areaCountry.getAreaCode().equals(phoneNumberCode)) {
                    areaCodeValue = areaCountry;
                    break;
                }

            }
        }
        List<AreaCountryDto> areaCountryDtoList = areaCountryMapper.getAllCountryList();
        AreaCountryDto fkAreaCountryIdDto = new AreaCountryDto();
        boolean findResidentialCountry = false;
        if (residentialCountry != null && !residentialCountry.isEmpty()) {
            double maxLength = 0;
            for (AreaCountryDto areaCountryDto : areaCountryDtoList) {
                int currentLength = ProcessNewApplicationUtils.lcsLength(residentialCountry, areaCountryDto.getFullName());
                double matchPercentage = currentLength * 1.0 / areaCountryDto.getFullName().length();
                if (!findResidentialCountry && matchPercentage > maxLength) {
                    maxLength = matchPercentage;
                    fkAreaCountryIdDto = areaCountryDto;
                    if (residentialCountry.contains(areaCountryDto.getNameChn())) {
                        findResidentialCountry = true;
                    }
                }
            }
        }
        AreaStateDto fkAreaStateIdDto = new AreaStateDto();
        boolean findResidentialProvince = false;
        if (residentialProvince != null && !residentialProvince.isEmpty() && fkAreaCountryIdDto != null) {
            List<AreaStateDto> areaStateDtoList = areaStateService.getByFkAreaCountryId(fkAreaCountryIdDto.getId());
            double maxLength = 0;
            for (AreaStateDto areaStateDto : areaStateDtoList) {
                int currentLength = ProcessNewApplicationUtils.lcsLength(residentialProvince, areaStateDto.getFullName().replaceAll("Province", ""));
                double matchPercentage = currentLength * 1.0 / areaStateDto.getFullName().replaceAll("Province", "").length();
                if (currentLength == residentialProvince.length()) {
                    findResidentialProvince = true;
                    maxLength = matchPercentage;
                    fkAreaStateIdDto = areaStateDto;
                }
                if (!findResidentialProvince && matchPercentage > maxLength) {
                    maxLength = matchPercentage;
                    fkAreaStateIdDto = areaStateDto;
                    if (residentialProvince.contains(areaStateDto.getNameChn())) {
                        findResidentialProvince = true;
                    }
                }
            }
        }
        AreaCityDto fkAreaCityIdDto = new AreaCityDto();
        boolean findResidentialCity = false;
        if (residentialCity != null && !residentialCity.isEmpty() && fkAreaStateIdDto != null) {
            List<AreaCityDto> areaCityDtoList = areaCityService.getByFkAreaStateId(fkAreaStateIdDto.getId());
            double maxLength = 0;
            for (AreaCityDto areaCityDto : areaCityDtoList) {
                int currentLength = ProcessNewApplicationUtils.lcsLength(residentialCity, areaCityDto.getFullName());
                double matchPercentage = currentLength * 1.0 / areaCityDto.getFullName().length();
                if (!findResidentialCity && matchPercentage > maxLength) {
                    maxLength = matchPercentage;
                    fkAreaCityIdDto = areaCityDto;
                    if (residentialCity.contains(areaCityDto.getNameChn())) {
                        findResidentialCity = true;
                    }
                }
            }
        }

        return new ContactInformationDto(mobileAreaCode, mobilePhoneNumber, areaCodeValue, phoneNumber, emailAddress, residentialZip, fkAreaCountryIdDto, fkAreaStateIdDto, fkAreaCityIdDto, nowResidential);
    }

    @Override
    public HighestDegreeInfoDto getHighestDegreeInfo(XWPFTable table) throws Exception {
        if (table.getText().isEmpty()) {
            new HighestDegreeInfoDto();
        }
        List<BaseSelectEntity> baseSelectEntityList = studentEducationLevelTypeMapper.getEducationDropDown();
        String degreeLevel = ProcessNewApplicationUtils.findBestMatchCell("学历等级类型", table).getText();
        String schoolName = ProcessNewApplicationUtils.findBestMatchCell("学校名称", table).getText();
        String graduationSchoolCountry = ProcessNewApplicationUtils.findBestMatchCell("学校国家", table).getText();
        String graduationSchoolProvince = ProcessNewApplicationUtils.findBestMatchCell("学校省份", table).getText();
        String graduationSchoolCity = ProcessNewApplicationUtils.findBestMatchCell("学校所在市", table).getText();
        String graduationSpecialized = ProcessNewApplicationUtils.findBestMatchCell("毕业专业", table).getText();

        // 区分国内学历和国外学历
        String graduationSchoolType = "";
        if (table.getRow(0).getCell(0).getText().contains("国内")) {
            graduationSchoolType = ProcessNewApplicationUtils.findBestMatchCell("毕业类型", table).getText();
        }
        BaseSelectEntity educationLevelTypeDto = new BaseSelectEntity();
        if (degreeLevel != null && !degreeLevel.isEmpty()) {
            double maxLength = 0;
            for (BaseSelectEntity baseSelectEntity : baseSelectEntityList) {
                int currentLength = ProcessNewApplicationUtils.lcsLength(degreeLevel, baseSelectEntity.getFullName());
                double matchPercentage = currentLength * 1.0 / baseSelectEntity.getFullName().length();
                if (matchPercentage > maxLength) {
                    maxLength = matchPercentage;
                    educationLevelTypeDto = baseSelectEntity;
                    if (baseSelectEntity.getFullName().equals(degreeLevel)) {
                        break;
                    }
                }
            }
        }
        AreaCountryDto fkAreaCountryIdEducationDto = new AreaCountryDto();
        boolean findGraduationSchoolCountry = false;
        if (graduationSchoolCountry != null && !graduationSchoolCountry.isEmpty()) {
            List<AreaCountryDto> areaCountryDtoList = areaCountryMapper.getAllCountryList();
            double maxLength = 0;
            for (AreaCountryDto areaCountryDto : areaCountryDtoList) {
                int currentLength = ProcessNewApplicationUtils.lcsLength(graduationSchoolCountry, areaCountryDto.getFullName());
                double matchPercentage = currentLength * 1.0 / areaCountryDto.getFullName().length();
                if (!findGraduationSchoolCountry && matchPercentage > maxLength) {
                    maxLength = matchPercentage;
                    fkAreaCountryIdEducationDto = areaCountryDto;
                    if (graduationSchoolCountry.contains(areaCountryDto.getNameChn())) {
                        findGraduationSchoolCountry = true;
                    }
                }
            }
        }
        AreaStateDto fkAreaStateIdEducationDto = new AreaStateDto();
        boolean findGraduationSchoolProvince = false;
        if (graduationSchoolProvince != null && !graduationSchoolProvince.isEmpty() && fkAreaCountryIdEducationDto != null) {
            List<AreaStateDto> areaStateDtoList = areaStateService.getByFkAreaCountryId(fkAreaCountryIdEducationDto.getId());
            double maxLength = 0;
            for (AreaStateDto areaStateDto : areaStateDtoList) {
                int currentLength = ProcessNewApplicationUtils.lcsLength(graduationSchoolProvince, areaStateDto.getFullName().replaceAll("Province", ""));
                double matchPercentage = currentLength * 1.0 / areaStateDto.getFullName().replaceAll("Province", "").length();
                if (!findGraduationSchoolProvince && matchPercentage > maxLength) {
                    maxLength = matchPercentage;
                    fkAreaStateIdEducationDto = areaStateDto;
                    if (graduationSchoolProvince.contains(areaStateDto.getNameChn())) {
                        findGraduationSchoolProvince = true;
                    }
                }
            }
        }
        AreaCityDto fkAreaCityIdEducationDto = new AreaCityDto();
        boolean findGraduationSchoolCity = false;
        if (graduationSchoolCity != null && !graduationSchoolCity.isEmpty() && fkAreaStateIdEducationDto != null) {
            List<AreaCityDto> areaCityDtoList = areaCityService.getByFkAreaStateId(fkAreaStateIdEducationDto.getId());
            double maxLength = 0;
            for (AreaCityDto areaCityDto : areaCityDtoList) {
                int currentLength = ProcessNewApplicationUtils.lcsLength(graduationSchoolCity, areaCityDto.getFullName());
                double matchPercentage = currentLength * 1.0 / areaCityDto.getFullName().length();
                if (!findGraduationSchoolCity && matchPercentage > maxLength) {
                    maxLength = matchPercentage;
                    fkAreaCityIdEducationDto = areaCityDto;
                    if (graduationSchoolCity.contains(areaCityDto.getNameChn())) {
                        findGraduationSchoolCity = true;
                    }
                }
            }
        }
        return new HighestDegreeInfoDto(educationLevelTypeDto, schoolName, fkAreaCountryIdEducationDto, fkAreaStateIdEducationDto, fkAreaCityIdEducationDto, graduationSpecialized, graduationSchoolType);
    }

    @Override
    public DegreeInfoRemarkDto getDegreeInfoRemark(XWPFTable table) throws Exception {
        if (table.getText().isEmpty()) {
            return new DegreeInfoRemarkDto();
        }
        String remark = ProcessNewApplicationUtils.findBestMatchCell("项目说明", table).getText();
        String degreeIllustrate = ProcessNewApplicationUtils.findBestMatchCell("学位情况", table).getText();
        String isComplexDegree = ProcessNewApplicationUtils.findBestMatchCell("是否复杂学历", table).getText();
        List<String> remarkList = new ArrayList<>();
        remarkList.add("3+2");
        remarkList.add("2+2");
        remarkList.add("4+0");
        remarkList.add("交换生");
        double maxLength = 0;
        Map<String, Object> educationProjectMap = new HashMap<>();
        for (int i = 0; i < remarkList.size(); i++) {
            int currentLength = ProcessNewApplicationUtils.lcsLength(remark, remarkList.get(i));
            double matchPercentage = currentLength * 1.0 / remarkList.get(i).length();
            if (matchPercentage > maxLength) {
                maxLength = matchPercentage;
                educationProjectMap.clear();
                educationProjectMap.put(String.valueOf(i), remarkList.get(i));
            }
            if (remark.equals(remarkList.get(i))) {
                break;
            }
        }
        List<String> degreeList = new ArrayList<>();
        degreeList.add("获得双学位");
        degreeList.add("获得国际学位");
        degreeList.add("获得国内学位");
        Map<String, Object> degreeIllustrateMap = new HashMap<>();
        maxLength = 0;
        for (int i = 0; i < degreeList.size(); i++) {
            int currentLength = ProcessNewApplicationUtils.lcsLength(degreeIllustrate, degreeList.get(i));
            double matchPercentage = currentLength * 1.0 / degreeList.get(i).length();
            if (matchPercentage > maxLength) {
                maxLength = matchPercentage;
                degreeIllustrateMap.clear();
                degreeIllustrateMap.put(String.valueOf(i), degreeList.get(i));
                if (degreeIllustrate.equals(degreeList.get(i))) {
                    break;
                }
            }
        }

        return new DegreeInfoRemarkDto(educationProjectMap, degreeIllustrateMap, isComplexDegree);
    }

    @Override
    public GradesInfoDto getGradesInfo(XWPFTable table) throws Exception {
        if (table.getText().isEmpty()) {
            return new GradesInfoDto();
        }
        String highGrades = ProcessNewApplicationUtils.findBestMatchCell("高中成绩", table).getText();
        String highLevel = ProcessNewApplicationUtils.findBestMatchCell("高中成绩评分", table).getText();
        String degreeGrades = ProcessNewApplicationUtils.findBestMatchCell("本科成绩", table).getText();
        String degreeLevel = ProcessNewApplicationUtils.findBestMatchCell("本科成绩评分", table).getText();
        String graduateGrades = ProcessNewApplicationUtils.findBestMatchCell("研究生成绩", table).getText();
        String graduateLevel = ProcessNewApplicationUtils.findBestMatchCell("研究生成绩评分", table).getText();
        String englishTestType = ProcessNewApplicationUtils.findBestMatchCell("英语测试类型", table).getText();
        String englishGrades = ProcessNewApplicationUtils.findBestMatchCell("英语测试成绩", table).getText();
        Map<String, Object> hightGradeMap = new HashMap<>();
        Map<String, Object> degreeGradeMap = new HashMap<>();
        Map<String, Object> graduateGradesMap = new HashMap<>();
        Map<String, Object> englishTestTypeMap = new HashMap<>();
        List<Map<String, Object>> map = ProjectExtraEnum.enums2Arrays(ProjectExtraEnum.HIGH_SCHOOL_GRADES);
        for (Map<String, Object> map1 : map) {
            if (map1.values().toArray()[0].equals(highGrades)) {
                hightGradeMap = map1;
            }
        }
        List<Map<String, Object>> map1 = ProjectExtraEnum.enums2Arrays(ProjectExtraEnum.UNDERGRADUATE_ACHIEVEMENT);
        for (Map<String, Object> map2 : map1) {
            if (map2.values().toArray()[0].equals(degreeGrades)) {
                degreeGradeMap = map2;
            }
            if (map2.values().toArray()[0].equals(graduateGrades)) {
                graduateGradesMap = map2;
            }
        }
        List<Map<String, Object>> map2 = ProjectExtraEnum.enums2Arrays(ProjectExtraEnum.English_subtype);
        for (Map<String, Object> map3 : map2) {
            if (map3.values().toArray()[0].equals(englishTestType)) {
                englishTestTypeMap = map3;
            }
        }
        return new GradesInfoDto(hightGradeMap, highLevel, degreeGradeMap, degreeLevel, graduateGradesMap, graduateLevel, englishTestTypeMap, englishGrades);
    }

    @Override
    public ApplySchoolInfoDto getApplySchoolInfo(XWPFTable table) throws Exception {
        if (table.getText().isEmpty()) {
            return new ApplySchoolInfoDto();
        }
        List<SchoolInfoDto> schoolInfoList = new ArrayList<>();
        List<XWPFTableCell> schoolCountryCells = ProcessNewApplicationUtils.findBestMatchCells("学校国家", table);
        List<XWPFTableCell> schoolNameCells = ProcessNewApplicationUtils.findBestMatchCells("学校名称", table);
        List<XWPFTableCell> schoolProjectCells = ProcessNewApplicationUtils.findBestMatchCells("申请专业名称后续", table);
        List<XWPFTableCell> schoolStartTimeCells = ProcessNewApplicationUtils.findBestMatchCells("开课时间", table);
        List<AreaCountryDto> areaCountryDtoList = areaCountryMapper.getAllCountryList();

        for (int i = 0; i < schoolNameCells.size(); i++) {
            String schoolCountry = "";
            try {
                schoolCountry = schoolCountryCells.get(i).getText();
                if (schoolCountry != null && !schoolCountry.isEmpty()) {
                    LambdaQueryWrapper<SynonymReference> synonymReferenceLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    synonymReferenceLambdaQueryWrapper.eq(SynonymReference::getTableName, "u_area_country");
                    synonymReferenceLambdaQueryWrapper.like(SynonymReference::getInputTerm, schoolCountry);
                    List<SynonymReference> synonymReference = synonymReferenceMapper.selectList(synonymReferenceLambdaQueryWrapper);
                    if (!synonymReference.isEmpty()) {
                        schoolCountry = synonymReference.get(0).getStandardTerm();
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                schoolCountry = "";
            }
            AreaCountryDto areaCountryDto1 = new AreaCountryDto();
            if (!schoolCountry.isEmpty()) {
                double maxLength = 0;
                boolean findPassportLocation = false;
                for (AreaCountryDto areaCountryDto : areaCountryDtoList) {
                    int currentLength = ProcessNewApplicationUtils.lcsLength(schoolCountry, areaCountryDto.getFullName());
                    double matchPercentage = currentLength * 1.0 / areaCountryDto.getFullName().length();
                    if (matchPercentage > maxLength && !findPassportLocation) {
                        maxLength = matchPercentage;
                        areaCountryDto1 = areaCountryDto;
                        if (schoolCountry.contains(areaCountryDto.getNameChn())) {
                            findPassportLocation = true;
                        }
                    }
                }
            }


            String schoolName = schoolNameCells.get(i).getText();
            InstitutionCompare institutionCompare = new InstitutionCompare();
            if (schoolName != null && !schoolName.isEmpty()) {
                // 先根据简称匹配
                QueryWrapper<SynonymReference> synonymReferenceLambdaQueryWrapper = new QueryWrapper<>();
                synonymReferenceLambdaQueryWrapper.eq("table_name", "m_institution");
                synonymReferenceLambdaQueryWrapper.apply("LOWER(input_term) = LOWER({0})", schoolName);
                List<SynonymReference> synonymReference = synonymReferenceMapper.selectList(synonymReferenceLambdaQueryWrapper);
                if (!synonymReference.isEmpty()) {
                    Long mInstitutionId = Long.parseLong(synonymReference.get(0).getStandardTerm());
                    QueryWrapper<MInstitution> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("id", mInstitutionId);
                    List<MInstitution> mInstitutions = mInstitutionMapper.selectList(queryWrapper);
                    if (!mInstitutions.isEmpty()) {
                        institutionCompare.setInput(schoolName);
                        institutionCompare.setInstitution(mInstitutions.get(0));

                        // 如果没有国家没有填，则需要补全国家
                        if (areaCountryDto1.getFullName() == null || !areaCountryDto1.getFullName().isEmpty()) {
                            for (AreaCountryDto areaCountryDto : areaCountryDtoList) {
                                if (areaCountryDto.getId().equals(mInstitutions.get(0).getFkAreaCountryId())) {
                                    areaCountryDto1 = areaCountryDto;
                                    break;
                                }
                            }
                        }


                    }
                } else {
                    QueryWrapper<MInstitution> queryWrapper = new QueryWrapper<>();
                    if (areaCountryDto1.getFullName() != null && !areaCountryDto1.getFullName().isEmpty()) {
                        queryWrapper.eq("fk_area_country_id", areaCountryDto1.getId());
                    }
                    // 获取所有学校
                    List<MInstitution> mInstitutionList = mInstitutionMapper.selectList(queryWrapper);
                    try {
                        List<InstitutionCompare> institutionCompares = new ArrayList<>();
                        for (int k = 0; k < mInstitutionList.size(); k++) {
                            MInstitution mInstitution = mInstitutionList.get(k);
                            String institutionFullName = mInstitution.getName() + mInstitution.getNameChn();
                            int currentLength = ProcessNewApplicationUtils.lcsLength(schoolName, institutionFullName);
                            double inputPercentage = currentLength * 1.0 / schoolName.length();
                            double institutionPercentage = currentLength * 1.0 / institutionFullName.length();
                            InstitutionCompare institutionCompare1 = new InstitutionCompare();
                            institutionCompare1.setInstitution(mInstitution);
                            institutionCompare1.setInput(schoolName);
                            institutionCompare1.setInstitutionPercentage(institutionPercentage);
                            institutionCompare1.setInputPercentage(inputPercentage);
                            institutionCompares.add(institutionCompare1);
                        }
                        // 使用 sort 方法并传入自定义的 Comparator 来进行排序
                        institutionCompares.sort(new Comparator<InstitutionCompare>() {
                            @Override
                            public int compare(InstitutionCompare o1, InstitutionCompare o2) {
                                // 首先比较 inputPercentage，如果不同则按其降序排列
                                if (Double.compare(o1.getInputPercentage(), o2.getInputPercentage()) != 0) {
                                    return Double.compare(o2.getInputPercentage(), o1.getInputPercentage());
                                } else {
                                    // 如果 inputPercentage 相同，则按 institutionPercentage 降序排列
                                    return Double.compare(o2.getInstitutionPercentage(), o1.getInstitutionPercentage());
                                }
                            }
                        });
                        institutionCompare = institutionCompares.get(0);

                        // 如果没有国家没有填，则需要补全国家
                        if (areaCountryDto1.getFullName() == null || !areaCountryDto1.getFullName().isEmpty()) {
                            for (AreaCountryDto areaCountryDto : areaCountryDtoList) {
                                if (areaCountryDto.getId().equals(institutionCompare.getInstitution().getFkAreaCountryId())) {
                                    areaCountryDto1 = areaCountryDto;
                                    break;
                                }
                            }
                        }

                    } catch (Exception e) {
                        schoolName = "";
                    }
                }
            }
            String schoolProject;
            try {
                schoolProject = schoolProjectCells.get(i).getText();
            } catch (Exception e) {
                schoolProject = "";
            }
            Date startTime = null;
            try {
                String startTimeStr = schoolStartTimeCells.get(i).getText();
                List<SimpleDateFormat> dateFormats = new ArrayList<>();
                dateFormats.add(new SimpleDateFormat("yyyy-MM-dd"));
                dateFormats.add(new SimpleDateFormat("yyyy.MM.dd"));
                dateFormats.add(new SimpleDateFormat("yyyy/MM/dd"));
                dateFormats.add(new SimpleDateFormat("dd/MM/yyyy"));
                dateFormats.add(new SimpleDateFormat("yyyy-MM"));
                dateFormats.add(new SimpleDateFormat("yyyy.MM"));
                dateFormats.add(new SimpleDateFormat("yyyy/MM"));
                dateFormats.add(new SimpleDateFormat("MM/yyyy"));
                dateFormats.add(new SimpleDateFormat("dd/MM"));
                for (int j = 0; j < dateFormats.size(); j++) {
                    SimpleDateFormat dateFormat = dateFormats.get(j);
                    try {
                        startTime = dateFormat.parse(startTimeStr);
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(startTime);
                        int year = calendar.get(Calendar.YEAR);
                        if (year < 1920) {
                            continue;
                        }
                        break;
                    } catch (ParseException e3) {
                        if (j == dateFormats.size() - 1) {
                            startTime = null;
                        }
                    }
                }
            } catch (Exception e) {
                startTime = null;
            }
            if (areaCountryDto1.getFullName() == null && institutionCompare.getInstitution() == null) {
                continue;
            }
            schoolInfoList.add(new SchoolInfoDto(areaCountryDto1, institutionCompare, schoolProject, startTime));


        }


        /*for (int i = 0; i < schoolCountryCells.size(); i++) {
            AreaCountryDto areaCountryDto1 = new AreaCountryDto();
            String schoolCountry = "";
            try {
                schoolCountry = schoolCountryCells.get(i).getText();

                if (schoolCountry != null && !schoolCountry.isEmpty()) {
                    LambdaQueryWrapper<SynonymReference> synonymReferenceLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    synonymReferenceLambdaQueryWrapper.eq(SynonymReference::getTableName, "u_area_country");
                    synonymReferenceLambdaQueryWrapper.like(SynonymReference::getInputTerm, schoolCountry);
                    List<SynonymReference> synonymReference = synonymReferenceMapper.selectList(synonymReferenceLambdaQueryWrapper);
                    if (!synonymReference.isEmpty()) {
                        schoolCountry = synonymReference.get(0).getStandardTerm();
                    }
                }

            } catch (Exception e) {
                e.printStackTrace();
                schoolCountry = "";
            }
            if (!schoolCountry.isEmpty()) {
                double maxLength = 0;
                boolean findPassportLocation = false;
                for (AreaCountryDto areaCountryDto : areaCountryDtoList) {
                    int currentLength = ProcessNewApplicationUtils.lcsLength(schoolCountry, areaCountryDto.getFullName());
                    double matchPercentage = currentLength * 1.0 / areaCountryDto.getFullName().length();
                    if (matchPercentage > maxLength && !findPassportLocation) {
                        maxLength = matchPercentage;
                        areaCountryDto1 = areaCountryDto;
                        if (schoolCountry.contains(areaCountryDto.getNameChn())) {
                            findPassportLocation = true;
                        }
                    }
                }
            }
            String schoolName = schoolNameCells.get(i).getText();
            InstitutionCompare institutionCompare = new InstitutionCompare();
            if (schoolName != null && !schoolName.isEmpty()) {
                // 先根据简称匹配
                QueryWrapper<SynonymReference> synonymReferenceLambdaQueryWrapper = new QueryWrapper<>();
                synonymReferenceLambdaQueryWrapper.eq("table_name", "m_institution");
                synonymReferenceLambdaQueryWrapper.apply("LOWER(input_term) = LOWER({0})", schoolName);
                List<SynonymReference> synonymReference = synonymReferenceMapper.selectList(synonymReferenceLambdaQueryWrapper);
                if (!synonymReference.isEmpty()) {
                    Long mInstitutionId = Long.parseLong(synonymReference.get(0).getStandardTerm());
                    QueryWrapper<MInstitution> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("id", mInstitutionId);
                    List<MInstitution> mInstitutions = mInstitutionMapper.selectList(queryWrapper);
                    if (!mInstitutions.isEmpty()) {
                        institutionCompare.setInput(schoolName);
                        institutionCompare.setInstitution(mInstitutions.get(0));
                    }
                } else {
                    QueryWrapper<MInstitution> queryWrapper = new QueryWrapper<>();
                    if (areaCountryDto1.getFullName() != null && !areaCountryDto1.getFullName().isEmpty()) {
                        queryWrapper.eq("fk_area_country_id", areaCountryDto1.getId());
                    }
                    // 获取所有学校
                    List<MInstitution> mInstitutionList = mInstitutionMapper.selectList(queryWrapper);
                    try {
                        List<InstitutionCompare> institutionCompares = new ArrayList<>();
                        for (int k = 0; k < mInstitutionList.size(); k++) {
                            MInstitution mInstitution = mInstitutionList.get(k);
                            String institutionFullName = mInstitution.getName() + mInstitution.getNameChn();
                            int currentLength = ProcessNewApplicationUtils.lcsLength(schoolName, institutionFullName);
                            double inputPercentage = currentLength * 1.0 / schoolName.length();
                            double institutionPercentage = currentLength * 1.0 / institutionFullName.length();
                            InstitutionCompare institutionCompare1 = new InstitutionCompare();
                            institutionCompare1.setInstitution(mInstitution);
                            institutionCompare1.setInput(schoolName);
                            institutionCompare1.setInstitutionPercentage(institutionPercentage);
                            institutionCompare1.setInputPercentage(inputPercentage);
                            institutionCompares.add(institutionCompare1);
                        }
                        // 使用 sort 方法并传入自定义的 Comparator 来进行排序
                        institutionCompares.sort(new Comparator<InstitutionCompare>() {
                            @Override
                            public int compare(InstitutionCompare o1, InstitutionCompare o2) {
                                // 首先比较 inputPercentage，如果不同则按其降序排列
                                if (Double.compare(o1.getInputPercentage(), o2.getInputPercentage()) != 0) {
                                    return Double.compare(o2.getInputPercentage(), o1.getInputPercentage());
                                } else {
                                    // 如果 inputPercentage 相同，则按 institutionPercentage 降序排列
                                    return Double.compare(o2.getInstitutionPercentage(), o1.getInstitutionPercentage());
                                }
                            }
                        });
                        institutionCompare = institutionCompares.get(0);
                    } catch (Exception e) {
                        schoolName = "";
                    }
                }
            }
            String schoolProject;
            try {
                schoolProject = schoolProjectCells.get(i).getText();
            } catch (Exception e) {
                schoolProject = "";
            }
            Date startTime = null;
            try {
                String startTimeStr = schoolStartTimeCells.get(i).getText();
                List<SimpleDateFormat> dateFormats = new ArrayList<>();
                dateFormats.add(new SimpleDateFormat("yyyy-MM-dd"));
                dateFormats.add(new SimpleDateFormat("yyyy.MM.dd"));
                dateFormats.add(new SimpleDateFormat("yyyy/MM/dd"));
                dateFormats.add(new SimpleDateFormat("dd/MM/yyyy"));
                dateFormats.add(new SimpleDateFormat("yyyy-MM"));
                dateFormats.add(new SimpleDateFormat("yyyy.MM"));
                dateFormats.add(new SimpleDateFormat("yyyy/MM"));
                dateFormats.add(new SimpleDateFormat("MM/yyyy"));
                dateFormats.add(new SimpleDateFormat("dd/MM"));
                for (int j = 0; j < dateFormats.size(); j++) {
                    SimpleDateFormat dateFormat = dateFormats.get(j);
                    try {
                        startTime = dateFormat.parse(startTimeStr);
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(startTime);
                        int year = calendar.get(Calendar.YEAR);
                        if (year < 1920) {
                            continue;
                        }
                        break;
                    } catch (ParseException e3) {
                        if (j == dateFormats.size() - 1) {
                            startTime = null;
                        }
                    }
                }
            } catch (Exception e) {
                startTime = null;
            }
            if (areaCountryDto1.getFullName() == null && institutionCompare.getInstitution() == null) {
                continue;
            }
            schoolInfoList.add(new SchoolInfoDto(areaCountryDto1, institutionCompare, schoolProject, startTime));
        }*/
        return new ApplySchoolInfoDto(schoolInfoList);
    }

    @Override
    public AddNewStudentVo addNewStudentIssue(Elements trs) throws Exception {
        // 学生基本信息
        NewApplicationInfoDto newApplicationInfo = new NewApplicationInfoDto();
        // 学生联系信息
        ContactInformationDto contactInfo = new ContactInformationDto();
        // 国内最高学历
        HighestDegreeInfoDto chinaHighestDegreeInfo = new HighestDegreeInfoDto();
        // 国际最高学历
        HighestDegreeInfoDto intinationalHighestDegreeInfo = new HighestDegreeInfoDto();
        // 学历情况备注
        DegreeInfoRemarkDto degreeInfoRemark = new DegreeInfoRemarkDto();
        // 成绩情况
        GradesInfoDto gradesInfo = new GradesInfoDto();
        // 申请学校信息
        ApplySchoolInfoDto aSchoolInfo = new ApplySchoolInfoDto();

        // 基本信息获取
        String name = ProcessNewApplicationUtils.findBestMatchCellIssue("*中文名Chinesename", trs).text();
        String gender = ProcessNewApplicationUtils.findBestMatchCellIssue("*性别Gender", trs).text();
        String d = ProcessNewApplicationUtils.findBestMatchCellIssue("*出生日期DOB(dd/mm/yyyy)", trs).text();

        Date birthday = null;
        List<SimpleDateFormat> dateFormats = new ArrayList<>();
        dateFormats.add(new SimpleDateFormat("yyyy-MM-dd"));
        dateFormats.add(new SimpleDateFormat("yyyy.MM.dd"));
        dateFormats.add(new SimpleDateFormat("yyyy/MM/dd"));
        dateFormats.add(new SimpleDateFormat("dd/MM/yyyy"));
        dateFormats.add(new SimpleDateFormat("yyyy-MM"));
        dateFormats.add(new SimpleDateFormat("yyyy.MM"));
        dateFormats.add(new SimpleDateFormat("yyyy/MM"));
        dateFormats.add(new SimpleDateFormat("MM/yyyy"));
        dateFormats.add(new SimpleDateFormat("dd/MM"));
        try {
            for (int j = 0; j < dateFormats.size(); j++) {
                SimpleDateFormat dateFormat = dateFormats.get(j);
                try {
                    birthday = dateFormat.parse(d);
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(birthday);
                    int year = calendar.get(Calendar.YEAR);
                    if (year < 1920) {
                        continue;
                    }
                    break;
                } catch (ParseException e3) {
                    if (j == dateFormats.size() - 1) {
                        birthday = null;
                    }
                }
            }
        } catch (Exception e) {
            birthday = null;
        }
        String passport = ProcessNewApplicationUtils.findBestMatchCellIssue("护照号码PassportNo.", trs).text();
        String passportLocation = ProcessNewApplicationUtils.findBestMatchCellIssue("签发地PlaceofIssue", trs).text();
        List<AreaCountryDto> areaCountryDtoList = areaCountryMapper.getAllCountryList();
        if (passportLocation != null && !passportLocation.isEmpty()) {
            double maxLength = 0;
            boolean findPassportLocation = false;
            AreaCountryDto fkAreaCountryIdPassportDto = new AreaCountryDto();
            for (AreaCountryDto areaCountryDto : areaCountryDtoList) {
                int currentLength = ProcessNewApplicationUtils.lcsLength(passportLocation, areaCountryDto.getFullName());
                double matchPercentage = currentLength * 1.0 / areaCountryDto.getFullName().length();
                if (matchPercentage > maxLength && !findPassportLocation) {
                    maxLength = matchPercentage;
                    fkAreaCountryIdPassportDto = areaCountryDto;
                    if (passportLocation.contains(areaCountryDto.getNameChn())) {
                        findPassportLocation = true;
                    }
                }
            }
            newApplicationInfo.setFkAreaCountryIdPassport(fkAreaCountryIdPassportDto);
        }
        String nationality = ProcessNewApplicationUtils.findBestMatchCellIssue("*国籍Nationality", trs).text();
        if (nationality != null && !nationality.isEmpty()) {
            double maxLength = 0;
            boolean findPassportLocation = false;
            AreaCountryDto fkCountryDto = new AreaCountryDto();
            for (AreaCountryDto areaCountryDto : areaCountryDtoList) {
                int currentLength = ProcessNewApplicationUtils.lcsLength(nationality, areaCountryDto.getFullName());
                double matchPercentage = currentLength * 1.0 / areaCountryDto.getFullName().length();
                if (matchPercentage > maxLength && !findPassportLocation) {
                    maxLength = matchPercentage;
                    fkCountryDto = areaCountryDto;
                    if (nationality.contains(areaCountryDto.getNameChn())) {
                        findPassportLocation = true;
                    }
                }
            }
            newApplicationInfo.setFkAreaCountryIdNationality(fkCountryDto);
        }

        String idCardLocation = ProcessNewApplicationUtils.findBestMatchCellIssue("*所在国家或地区CountryorRegion", trs).text();
        if (idCardLocation != null && !idCardLocation.isEmpty()) {
            LambdaQueryWrapper<SynonymReference> synonymReferenceLambdaQueryWrapper = new LambdaQueryWrapper<>();
            synonymReferenceLambdaQueryWrapper.like(SynonymReference::getInputTerm, idCardLocation);
            List<SynonymReference> synonymReference = synonymReferenceMapper.selectList(synonymReferenceLambdaQueryWrapper);
            if (!synonymReference.isEmpty()) {
                idCardLocation = synonymReference.get(0).getStandardTerm();
            }
            double maxLength = 0;
            boolean findPassportLocation = false;
            AreaCountryDto fkCountryDto = new AreaCountryDto();
            for (AreaCountryDto areaCountryDto : areaCountryDtoList) {
                int currentLength = ProcessNewApplicationUtils.lcsLength(idCardLocation, areaCountryDto.getFullName());
                double matchPercentage = currentLength * 1.0 / areaCountryDto.getFullName().length();
                if (matchPercentage > maxLength && !findPassportLocation) {
                    maxLength = matchPercentage;
                    fkCountryDto = areaCountryDto;
                    if (idCardLocation.contains(areaCountryDto.getNameChn())) {
                        findPassportLocation = true;
                    }
                }
            }
            newApplicationInfo.setFkAreaCountryIdGreenCard(fkCountryDto);
        }
        String bornCountry = ProcessNewApplicationUtils.findBestMatchCellIssue("*所在国家或地区CountryorRegion", trs).text();
        if (bornCountry != null && !bornCountry.isEmpty()) {
            LambdaQueryWrapper<SynonymReference> synonymReferenceLambdaQueryWrapper = new LambdaQueryWrapper<>();
            synonymReferenceLambdaQueryWrapper.like(SynonymReference::getInputTerm, bornCountry);
            List<SynonymReference> synonymReference = synonymReferenceMapper.selectList(synonymReferenceLambdaQueryWrapper);
            if (!synonymReference.isEmpty()) {
                bornCountry = synonymReference.get(0).getStandardTerm();
            }
            double maxLength = 0;
            boolean findCountry = false;
            AreaCountryDto fkCountryDto = new AreaCountryDto();
            for (AreaCountryDto areaCountryDto : areaCountryDtoList) {
                int currentLength = ProcessNewApplicationUtils.lcsLength(bornCountry, areaCountryDto.getFullName());
                double matchPercentage = currentLength * 1.0 / areaCountryDto.getFullName().length();
                if (matchPercentage > maxLength && !findCountry) {
                    maxLength = matchPercentage;
                    fkCountryDto = areaCountryDto;
                    if (bornCountry.contains(areaCountryDto.getNameChn())) {
                        findCountry = true;
                    }
                }
            }
            newApplicationInfo.setFkAreaCountryIdBirth(fkCountryDto);
            // 联系信息
            contactInfo.setFkAreaCountryId(fkCountryDto);
        }
        if (newApplicationInfo.getFkAreaCountryIdBirth() != null) {
            String bornProvince = ProcessNewApplicationUtils.findBestMatchCellIssue("*所在省份或州ProvinceorState", trs).text();
            if (bornProvince != null && !bornProvince.isEmpty()) {
                List<AreaStateDto> areaStateDtoList = areaStateService.getByFkAreaCountryId(newApplicationInfo.getFkAreaCountryIdBirth().getId());
                double maxLength = 0;
                boolean findBronProvince = false;
                AreaStateDto fkAreaStateIdBirth = new AreaStateDto();
                for (AreaStateDto areaStateDto : areaStateDtoList) {
                    int currentLength = ProcessNewApplicationUtils.lcsLength(bornProvince, areaStateDto.getFullName());
                    double matchPercentage = currentLength * 1.0 / areaStateDto.getFullName().length();
                    if (!findBronProvince && matchPercentage > maxLength) {
                        maxLength = matchPercentage;
                        fkAreaStateIdBirth = areaStateDto;
                        if (bornProvince.contains(areaStateDto.getNameChn())) {
                            findBronProvince = true;
                        }
                    }
                }
                newApplicationInfo.setFkAreaStateIdBirth(fkAreaStateIdBirth);
                // 联系信息
                contactInfo.setFkAreaStateId(fkAreaStateIdBirth);
            }

        }
        if (newApplicationInfo.getFkAreaStateIdBirth() != null) {
            String bornCity = ProcessNewApplicationUtils.findBestMatchCellIssue("*所在城市City", trs).text();
            if (bornCity != null && !bornCity.isEmpty()) {
                List<AreaCityDto> areaCityDtoList = areaCityService.getByFkAreaStateId(newApplicationInfo.getFkAreaStateIdBirth().getId());
                double maxLength = 0;
                boolean findBronCity = false;
                AreaCityDto fkAreaCityIdBirth = new AreaCityDto();
                for (AreaCityDto areaCityDto : areaCityDtoList) {
                    int currentLength = ProcessNewApplicationUtils.lcsLength(bornCity, areaCityDto.getFullName());
                    double matchPercentage = currentLength * 1.0 / areaCityDto.getFullName().length();
                    if (!findBronCity && matchPercentage > maxLength) {
                        maxLength = matchPercentage;
                        fkAreaCityIdBirth = areaCityDto;
                        if (bornCity.contains(areaCityDto.getNameChn())) {
                            findBronCity = true;
                        }
                    }
                }
                newApplicationInfo.setFkAreaCityIdBirth(fkAreaCityIdBirth);
                // 联系信息
                contactInfo.setFkAreaCityId(fkAreaCityIdBirth);
            }
        }
        newApplicationInfo.setName(name);
        newApplicationInfo.setGender(gender);
        newApplicationInfo.setBirthday(birthday);
        newApplicationInfo.setPassportNum(passport);

        // 联系信息获取
        String mobilePhoneNumberCode = ProcessNewApplicationUtils.findBestMatchCellIssue("*区域号Areacode", trs).text();
        List<AreaCountry> areaCountryList = areaCountryMapper.getAreaCode();
        if (mobilePhoneNumberCode != null && !mobilePhoneNumberCode.isEmpty()) {
            double maxLengt1 = 0;
            AreaCountry mobileAreaCode = new AreaCountry();
            for (AreaCountry areaCountry : areaCountryList) {
                int currentLength = ProcessNewApplicationUtils.lcsLength(mobilePhoneNumberCode, areaCountry.getAreaCode());
                double matchPercentage = currentLength * 1.0 / areaCountry.getAreaCode().length();
                if (matchPercentage > maxLengt1) {
                    maxLengt1 = matchPercentage;
                    mobileAreaCode = areaCountry;
                }
                // 优先选择完全匹配的字符串
                if (areaCountry.getAreaCode().equals(mobilePhoneNumberCode)) {
                    mobileAreaCode = areaCountry;
                    break;
                }
            }
            contactInfo.setMobileAreaCode(mobileAreaCode);
        }
        String mobilePhoneNumber = ProcessNewApplicationUtils.findBestMatchCellIssue("*电话Tel", trs).text();
        contactInfo.setMobile(mobilePhoneNumber);

        String zipcode = ProcessNewApplicationUtils.findBestMatchCellIssue("*邮编Postalcode", trs).text();
        contactInfo.setZipcode(zipcode);
        String emailAddress = ProcessNewApplicationUtils.findBestMatchCellIssue("*邮箱（学生本人）Email", trs).text();
        contactInfo.setEmail(emailAddress);
        String nowResidential = ProcessNewApplicationUtils.findBestMatchCellIssue("*所在地址详情一Detailedaddress1", trs).text();
        contactInfo.setContactAddress(nowResidential);

        // 申请学校
        List<Element> schoolCountry = ProcessNewApplicationUtils.findBestMatchCellIssueRow("1.请选择申请国家Stepone:Selectcountry", trs);
        List<Element> schoolNames = ProcessNewApplicationUtils.findBestMatchCellIssueRow("2.请选择申请学校Steptwo:Selectinstitution", trs);
        List<Element> Projects = ProcessNewApplicationUtils.findBestMatchCellIssueRow("专业Programme（请标明学位名称MSc或MA等）", trs);
        List<Element> startTimes = ProcessNewApplicationUtils.findBestMatchCellIssueRow("入学时间DateofEntry", trs);
        List<SchoolInfoDto> schoolInfoList = new ArrayList<>();
        for (int i = 0; i < schoolNames.size(); i++) {
            AreaCountryDto countryDto = new AreaCountryDto();
            try {
                String schoolCountryName = schoolCountry.get(i).text();
                if (!schoolCountryName.isEmpty()) {
                    LambdaQueryWrapper<SynonymReference> synonymReferenceLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    synonymReferenceLambdaQueryWrapper.like(SynonymReference::getInputTerm, schoolCountryName);
                    List<SynonymReference> synonymReference = synonymReferenceMapper.selectList(synonymReferenceLambdaQueryWrapper);
                    if (!synonymReference.isEmpty()) {
                        schoolCountryName = synonymReference.get(0).getStandardTerm();
                    }
                }
                if (schoolCountryName != null && !schoolCountryName.isEmpty()) {
                    double maxLength = 0;
                    boolean findPassportLocation = false;
                    for (AreaCountryDto areaCountryDto : areaCountryDtoList) {
                        int currentLength = ProcessNewApplicationUtils.lcsLength(schoolCountryName, areaCountryDto.getFullName());
                        double matchPercentage = currentLength * 1.0 / areaCountryDto.getFullName().length();
                        if (matchPercentage > maxLength && !findPassportLocation) {
                            maxLength = matchPercentage;
                            countryDto = areaCountryDto;
                            if (schoolCountryName.contains(areaCountryDto.getNameChn())) {
                                findPassportLocation = true;
                            }
                        }
                    }
                }
            } catch (Exception e) {
                System.out.println("issue 获取国家名称出错");
                e.printStackTrace();
            }


            String schoolName = schoolNames.get(i).text();
            InstitutionCompare institutionCompare = new InstitutionCompare();
            if (!schoolName.isEmpty()) {
                // 先根据简称匹配
                QueryWrapper<SynonymReference> synonymReferenceLambdaQueryWrapper = new QueryWrapper<>();
                synonymReferenceLambdaQueryWrapper.eq("table_name", "m_institution");
                synonymReferenceLambdaQueryWrapper.apply("LOWER(input_term) = LOWER({0})", schoolName);
                List<SynonymReference> synonymReference = synonymReferenceMapper.selectList(synonymReferenceLambdaQueryWrapper);
                if (!synonymReference.isEmpty()) {
                    Long mInstitutionId = Long.parseLong(synonymReference.get(0).getStandardTerm());
                    QueryWrapper<MInstitution> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("id", mInstitutionId);
                    List<MInstitution> mInstitutions = mInstitutionMapper.selectList(queryWrapper);
                    if (!mInstitutions.isEmpty()) {
                        institutionCompare.setInput(schoolName);
                        institutionCompare.setInstitution(mInstitutions.get(0));
                        // 如果没有国家没有填，则需要补全国家
                        if (countryDto.getFullName() != null && !countryDto.getFullName().isEmpty()) {
                            for (AreaCountryDto areaCountryDto : areaCountryDtoList) {
                                if (areaCountryDto.getId().equals(mInstitutions.get(0).getFkAreaCountryId())) {
                                    countryDto = areaCountryDto;
                                    break;
                                }
                            }
                        }
                    }
                } else {
                    QueryWrapper<MInstitution> queryWrapper = new QueryWrapper<>();
                    if (countryDto.getFullName() == null || !countryDto.getFullName().isEmpty()) {
                        queryWrapper.eq("fk_area_country_id", countryDto.getId());
                    }
                    // 获取所有学校
                    List<MInstitution> mInstitutionList = mInstitutionMapper.selectList(queryWrapper);
                    try {
                        List<InstitutionCompare> institutionCompares = new ArrayList<>();
                        for (int k = 0; k < mInstitutionList.size(); k++) {
                            MInstitution mInstitution = mInstitutionList.get(k);
                            String institutionFullName = mInstitution.getName() + mInstitution.getNameChn();
                            int currentLength = ProcessNewApplicationUtils.lcsLength(schoolName, institutionFullName);
                            double inputPercentage = currentLength * 1.0 / schoolName.length();
                            double institutionPercentage = currentLength * 1.0 / institutionFullName.length();
                            InstitutionCompare institutionCompare1 = new InstitutionCompare();
                            institutionCompare1.setInstitution(mInstitution);
                            institutionCompare1.setInput(schoolName);
                            institutionCompare1.setInstitutionPercentage(institutionPercentage);
                            institutionCompare1.setInputPercentage(inputPercentage);
                            institutionCompares.add(institutionCompare1);
                        }
                        // 使用 sort 方法并传入自定义的 Comparator 来进行排序
                        institutionCompares.sort(new Comparator<InstitutionCompare>() {
                            @Override
                            public int compare(InstitutionCompare o1, InstitutionCompare o2) {
                                // 首先比较 inputPercentage，如果不同则按其降序排列
                                if (Double.compare(o1.getInputPercentage(), o2.getInputPercentage()) != 0) {
                                    return Double.compare(o2.getInputPercentage(), o1.getInputPercentage());
                                } else {
                                    // 如果 inputPercentage 相同，则按 institutionPercentage 降序排列
                                    return Double.compare(o2.getInstitutionPercentage(), o1.getInstitutionPercentage());
                                }
                            }
                        });
                        institutionCompare = institutionCompares.get(0);

                        // 如果没有国家没有填，则需要补全国家
                        if (countryDto.getFullName() == null || !countryDto.getFullName().isEmpty()) {
                            for (AreaCountryDto areaCountryDto : areaCountryDtoList) {
                                if (areaCountryDto.getId().equals(institutionCompare.getInstitution().getFkAreaCountryId())) {
                                    countryDto = areaCountryDto;
                                    break;
                                }
                            }
                        }


                    } catch (Exception e) {
                        schoolName = "";
                    }
                }
            }
            String startTime = startTimes.get(i).text();
            String specialized = Projects.get(i).text();
            Date startDate = null;
            try {
                for (int j = 0; j < dateFormats.size(); j++) {
                    SimpleDateFormat dateFormat = dateFormats.get(j);
                    try {
                        startDate = dateFormat.parse(startTime);
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(startDate);
                        int year = calendar.get(Calendar.YEAR);
                        if (year < 1920) {
                            continue;
                        }
                        break;
                    } catch (ParseException e3) {
                        if (j == dateFormats.size() - 1) {
                            startDate = null;
                        }
                    }
                }
            } catch (Exception e) {
                startDate = null;
            }
            if (countryDto.getFullName() == null && institutionCompare.getInstitution().getName() == null) {
                continue;
            }
            SchoolInfoDto schoolInfoDto = new SchoolInfoDto(countryDto, institutionCompare, specialized, startDate);
            schoolInfoList.add(schoolInfoDto);

        }
        aSchoolInfo.setSchoolInfoList(schoolInfoList);
        AddNewStudentVo addNewStudentVo = new AddNewStudentVo();
        addNewStudentVo.setNewApplication(newApplicationInfo);
        addNewStudentVo.setContact(contactInfo);
        addNewStudentVo.setHighestDegree(chinaHighestDegreeInfo);
        addNewStudentVo.setIntinationalHighestDegreeInfo(intinationalHighestDegreeInfo);
        addNewStudentVo.setDegree(degreeInfoRemark);
        addNewStudentVo.setGrades(gradesInfo);
        addNewStudentVo.setApplication(aSchoolInfo);
        return addNewStudentVo;
    }
}
