package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.ContractFormulaPreInstitution;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ContractFormulaPreInstitutionMapper extends BaseMapper<ContractFormulaPreInstitution> {
    @Override
    int insert(ContractFormulaPreInstitution record);

    int insertSelective(ContractFormulaPreInstitution record);

//    List<ContractFormulaPreInstitution> selectByExample(ContractFormulaPreInstitutionExample example);

    int updateByPrimaryKeySelective(ContractFormulaPreInstitution record);

    int updateByPrimaryKey(ContractFormulaPreInstitution record);

    /**
     * @return list
     * @Description :查询学校id
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<Long> getInstiutionIdListByFkId(Long contractFormulaId);

    /**
     * 校验是否存在
     *
     * @Date 11:08 2021/4/16
     * <AUTHOR>
     */
    boolean isExistByInstitutionId(@Param("fkInstitutionId") Long fkInstitutionId, @Param("fkInstitutionProviderId") Long fkInstitutionProviderId);
}