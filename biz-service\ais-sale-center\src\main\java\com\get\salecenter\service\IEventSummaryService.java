package com.get.salecenter.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.vo.EventSummaryListVo;
import com.get.salecenter.vo.EventSummaryVo;
import com.get.salecenter.entity.EventSummary;
import com.get.salecenter.dto.EventSummaryListDto;
import com.get.salecenter.dto.EventSummaryUpdateDto;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/6/7 15:07
 * @verison: 1.0
 * @description:
 */
public interface IEventSummaryService {

    /**
     * 新增活动汇总费用摘要
     *
     * @param eventSummaryVo
     * @return
     */
    Long addEventSummary(EventSummaryUpdateDto eventSummaryVo);

    /**
     * 详情接口
     *
     * @param id
     * @return
     */
    EventSummaryVo findEventSummaryById(Long id);

    /**
     * 列表接口
     *
     * @param eventSummaryListDto
     * @param page
     * @return
     */
    List<EventSummaryListVo> getEventSummaries(EventSummaryListDto eventSummaryListDto, Page page);

    /**
     * 编辑接口
     *
     * @param eventSummaryUpdateDto
     * @return
     */
    EventSummaryVo updateeventSummary(EventSummaryUpdateDto eventSummaryUpdateDto);

    /**
     * 删除接口
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 上移下移
     *
     * @param eventSummaryUpdateDtos
     */
    void movingOrder(List<EventSummaryUpdateDto> eventSummaryUpdateDtos);

    /**
     * 费用摘要下拉框
     *
     * @param fkCompanyId
     * @return
     */
    List<BaseSelectEntity> getEventSummariesSelect(Long fkCompanyId);

    /**
     * 根据条件查询
     *
     * @param lambdaQueryWrapper
     * @return
     */
    List<EventSummary> getEventSummariesByCondition(LambdaQueryWrapper<EventSummary> lambdaQueryWrapper);
}
