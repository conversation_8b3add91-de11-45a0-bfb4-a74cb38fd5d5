package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.type.Alias;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("m_staff_hr_event")
@Alias("SchoolGateStaffHrEvent")
public class StaffHrEvent extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;
    /**
     * 事件类型：升职/降职/岗位调动/外调/薪资上调/薪资下调/停薪留职/入职/离职/试用期转正/试用期延期/合同变更
     */
    @ApiModelProperty(value = "事件类型：升职/降职/岗位调动/外调/薪资上调/薪资下调/停薪留职/入职/离职/试用期转正/试用期延期/合同变更")
    @Column(name = "event_type")
    private String eventType;
    /**
     * 原办公室Id
     */
    @ApiModelProperty(value = "原办公室Id")
    @Column(name = "fk_office_id_from")
    @UpdateWithNull
    private Long fkOfficeIdFrom;
    /**
     * 原部门Id
     */
    @ApiModelProperty(value = "原部门Id")
    @Column(name = "fk_department_id_from")
    @UpdateWithNull
    private Long fkDepartmentIdFrom;
    /**
     * 原职位Id
     */
    @ApiModelProperty(value = "原职位Id")
    @Column(name = "fk_position_id_from")
    @UpdateWithNull
    private Long fkPositionIdFrom;
    /**
     * 目标办公室Id
     */
    @ApiModelProperty(value = "目标办公室Id")
    @Column(name = "fk_office_id_to")
    @UpdateWithNull
    private Long fkOfficeIdTo;
    /**
     * 目标部门Id
     */
    @ApiModelProperty(value = "目标部门Id")
    @Column(name = "fk_department_id_to")
    @UpdateWithNull
    private Long fkDepartmentIdTo;
    /**
     * 目标职位Id
     */
    @ApiModelProperty(value = "目标职位Id")
    @Column(name = "fk_position_id_to")
    @UpdateWithNull
    private Long fkPositionIdTo;
    /**
     * 原基本工资
     */
    @ApiModelProperty(value = "原基本工资")
    @Column(name = "from_salary_base")
    @UpdateWithNull
    private BigDecimal fromSalaryBase;
    /**
     * 原绩效工资
     */
    @ApiModelProperty(value = "原绩效工资")
    @Column(name = "from_salary_performance")
    @UpdateWithNull
    private BigDecimal fromSalaryPerformance;
    /**
     * 原岗位津贴
     */
    @ApiModelProperty(value = "原岗位津贴")
    @Column(name = "from_allowance_position")
    @UpdateWithNull
    private BigDecimal fromAllowancePosition;
    /**
     * 原餐饮津贴
     */
    @ApiModelProperty(value = "原餐饮津贴")
    @Column(name = "from_allowance_catering")
    @UpdateWithNull
    private BigDecimal fromAllowanceCatering;
    /**
     * 原交通津贴
     */
    @ApiModelProperty(value = "原交通津贴")
    @Column(name = "from_allowance_transportation")
    @UpdateWithNull
    private BigDecimal fromAllowanceTransportation;
    /**
     * 原通讯津贴
     */
    @ApiModelProperty(value = "原通讯津贴")
    @Column(name = "from_allowance_telecom")
    @UpdateWithNull
    private BigDecimal fromAllowanceTelecom;
    /**
     * 原其他津贴
     */
    @ApiModelProperty(value = "原其他津贴")
    @Column(name = "from_allowance_other")
    @UpdateWithNull
    private BigDecimal fromAllowanceOther;
    /**
     * 目标基本工资
     */
    @ApiModelProperty(value = "目标基本工资")
    @Column(name = "to_salary_base")
    @UpdateWithNull
    private BigDecimal toSalaryBase;
    /**
     * 目标绩效工资
     */
    @ApiModelProperty(value = "目标绩效工资")
    @Column(name = "to_salary_performance")
    private BigDecimal toSalaryPerformance;
    /**
     * 目标岗位津贴
     */
    @ApiModelProperty(value = "目标岗位津贴")
    @Column(name = "to_allowance_position")
    @UpdateWithNull
    private BigDecimal toAllowancePosition;
    /**
     * 目标餐饮津贴
     */
    @ApiModelProperty(value = "目标餐饮津贴")
    @Column(name = "to_allowance_catering")
    @UpdateWithNull
    private BigDecimal toAllowanceCatering;
    /**
     * 目标交通津贴
     */
    @ApiModelProperty(value = "目标交通津贴")
    @Column(name = "to_allowance_transportation")
    @UpdateWithNull
    private BigDecimal toAllowanceTransportation;
    /**
     * 目标通讯津贴
     */
    @ApiModelProperty(value = "目标通讯津贴")
    @Column(name = "to_allowance_telecom")
    @UpdateWithNull
    private BigDecimal toAllowanceTelecom;
    /**
     * 目标其他津贴
     */
    @ApiModelProperty(value = "目标其他津贴")
    @Column(name = "to_allowance_other")
    @UpdateWithNull
    private BigDecimal toAllowanceOther;
    /**
     * 生效日期
     */
    @ApiModelProperty(value = "生效日期")
    @Column(name = "effective_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date effectiveDate;
    /**
     * 调整原因
     */
    @ApiModelProperty(value = "调整原因")
    @Column(name = "reason")
    private String reason;
}