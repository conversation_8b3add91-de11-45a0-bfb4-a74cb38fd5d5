package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.service.IConventionTableRegistrationService;
import com.get.salecenter.dto.ConventionTableRegistrationDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: Sea
 * @create: 2020/8/31 11:39
 * @verison: 1.0
 * @description:
 */
@Api(tags = "配置桌台-培训桌管理")
@RestController
@RequestMapping("sale/conventionTableRegistration")
public class ConventionTableRegistrationController {

    @Resource
    private IConventionTableRegistrationService conventionTableRegistrationService;

    /**
     * 配置桌台-培训桌
     *
     * @param conventionTableRegistrationDto
     * @return
     * @
     */
    @ApiOperation(value = "配置桌台", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/配置桌台-培训桌管理/配置桌台")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(ConventionTableRegistrationDto.Add.class)  ConventionTableRegistrationDto conventionTableRegistrationDto) {
        conventionTableRegistrationService.configurationTable(conventionTableRegistrationDto);
        return SaveResponseBo.ok();
    }
}
