<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.permissioncenter.dao.StaffContractMapper">
  <resultMap id="BaseResultMap" type="com.get.permissioncenter.entity.StaffContract">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_staff_id" jdbcType="BIGINT" property="fkStaffId" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="signing_company" jdbcType="VARCHAR" property="signingCompany" />
    <result column="signing_salary" jdbcType="DECIMAL" property="signingSalary" />
    <result column="social_insurance_place" jdbcType="VARCHAR" property="socialInsurancePlace" />
    <result column="workplace" jdbcType="VARCHAR" property="workplace" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_active" jdbcType="BIT" property="isActive" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <sql id="Base_Column_List">
    id, fk_staff_id, start_time, end_time, signing_company, signing_salary, social_insurance_place, 
    workplace, remark, is_active, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
  </sql>

  <insert id="insertSelective" parameterType="com.get.permissioncenter.entity.StaffContract" keyProperty="id" useGeneratedKeys="true">
    insert into m_staff_contract
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkStaffId != null">
        fk_staff_id,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="signingCompany != null">
        signing_company,
      </if>
      <if test="signingSalary != null">
        signing_salary,
      </if>
      <if test="socialInsurancePlace != null">
        social_insurance_place,
      </if>
      <if test="workplace != null">
        workplace,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isActive != null">
        is_active,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkStaffId != null">
        #{fkStaffId,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="signingCompany != null">
        #{signingCompany,jdbcType=VARCHAR},
      </if>
      <if test="signingSalary != null">
        #{signingSalary,jdbcType=DECIMAL},
      </if>
      <if test="socialInsurancePlace != null">
        #{socialInsurancePlace,jdbcType=VARCHAR},
      </if>
      <if test="workplace != null">
        #{workplace,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isActive != null">
        #{isActive,jdbcType=BIT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

    <select id="isExistByStaffId" resultType="java.lang.Boolean">
      SELECT IFNULL(max(id),0) id from m_staff_contract where fk_staff_id=#{staffId}
    </select>
  <select id="getStaffContractByStaffId" resultType="com.get.permissioncenter.entity.StaffContract">
    SELECT * FROM m_staff_contract WHERE is_active=1 AND fk_staff_id=#{staffId} order by gmt_create DESC LIMIT 1
  </select>
</mapper>