package com.get.salecenter.service;

import com.get.common.result.SearchBean;
import com.get.salecenter.vo.BusinessProviderContactPersonVo;
import com.get.salecenter.dto.BusinessProviderContactPersonListDto;
import com.get.salecenter.dto.BusinessProviderContactPersonDto;

import java.util.List;

public interface BusinessProviderContactPersonService {
    List<BusinessProviderContactPersonVo> getBusinessProviderContactPersonDtos(BusinessProviderContactPersonListDto data, SearchBean<BusinessProviderContactPersonListDto> page);

    void addBusinessProviderContactPerson(BusinessProviderContactPersonDto businessProviderContactPersonDto);

    BusinessProviderContactPersonVo updateBusinessProviderContactPerson(BusinessProviderContactPersonDto businessProviderContactPersonDto);

    void deleteBusinessProviderContactPerson(Long id);
}
