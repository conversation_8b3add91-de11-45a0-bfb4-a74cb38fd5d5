package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("s_institution_iengine_score")
public class InstitutionIengineScore extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 表名1
     */
    @ApiModelProperty(value = "表名1")
    @Column(name = "fk_table_name_1")
    private String fkTableName1;
    /**
     * 表Id1
     */
    @ApiModelProperty(value = "表Id1")
    @Column(name = "fk_table_id_1")
    private Long fkTableId1;
    /**
     * 表名2
     */
    @ApiModelProperty(value = "表名2")
    @Column(name = "fk_table_name_2")
    private String fkTableName2;
    /**
     * 表Id2
     */
    @ApiModelProperty(value = "表Id2")
    @Column(name = "fk_table_id_2")
    private Long fkTableId2;
    /**
     * 类型key，必填，区分同表记录对应不同类型的分数，如：生命灵数/天赋数
     */
    @ApiModelProperty(value = "类型key，必填，区分同表记录对应不同类型的分数，如：生命灵数/天赋数")
    @Column(name = "type_key")
    private String typeKey;
    /**
     * 分数1：命数1/天赋1/城市宜居指数
     */
    @ApiModelProperty(value = "分数1：命数1/天赋1/城市宜居指数")
    @Column(name = "score1")
    private Integer score1;
    /**
     * 分数2：命数2/天赋2/生活消费指数
     */
    @ApiModelProperty(value = "分数2：命数2/天赋2/生活消费指数")
    @Column(name = "score2")
    private Integer score2;
    /**
     * 分数3：命数3/天赋3/学校排名得分
     */
    @ApiModelProperty(value = "分数3：命数3/天赋3/学校排名得分")
    @Column(name = "score3")
    private Integer score3;
    /**
     * 分数4：命数4/天赋4/学科排名得分(学校+科目分类)
     */
    @ApiModelProperty(value = "分数4：命数4/天赋4/学科排名得分(学校+科目分类)")
    @Column(name = "score4")
    private Integer score4;
    /**
     * 分数5：命数5/天赋5/学费水平得分(国家+科目分类)
     */
    @ApiModelProperty(value = "分数5：命数5/天赋5/学费水平得分(国家+科目分类)")
    @Column(name = "score5")
    private Integer score5;
    /**
     * 分数6：命数6/天赋6/奖学金得分
     */
    @ApiModelProperty(value = "分数6：命数6/天赋6/奖学金得分")
    @Column(name = "score6")
    private Integer score6;
    /**
     * 分数7：命数7/天赋7/实习机会得分
     */
    @ApiModelProperty(value = "分数7：命数7/天赋7/实习机会得分")
    @Column(name = "score7")
    private Integer score7;
    /**
     * 分数8：命数8/天赋8/毕业专家资格(学校+课程)
     */
    @ApiModelProperty(value = "分数8：命数8/天赋8/毕业专家资格(学校+课程)")
    @Column(name = "score8")
    private Integer score8;
    /**
     * 分数9：命数9/天赋9/毕业就业率(学校+课程)
     */
    @ApiModelProperty(value = "分数9：命数9/天赋9/毕业就业率(学校+课程)")
    @Column(name = "score9")
    private Integer score9;
    /**
     * 分数10：毕业工资水平
     */
    @ApiModelProperty(value = "分数10：毕业工资水平")
    @Column(name = "score10")
    private Integer score10;
    /**
     * 分数11：移民优惠政策
     */
    @ApiModelProperty(value = "分数11：移民优惠政策")
    @Column(name = "score11")
    private Integer score11;
    /**
     * 分数12：亲友同城
     */
    @ApiModelProperty(value = "分数12：亲友同城")
    @Column(name = "score12")
    private Integer score12;
    /**
     * 分数13：KPI得分
     */
    @ApiModelProperty(value = "分数13：KPI得分")
    @Column(name = "score13")
    private Integer score13;
    /**
     * 分数14：学科奖学金(学校+课程)
     */
    @ApiModelProperty(value = "分数14：学科奖学金(学校+课程)")
    @Column(name = "score14")
    private Integer score14;
    /**
     * 分数15：安全指数
     */
    @ApiModelProperty(value = "分数15：安全指数")
    @Column(name = "score15")
    private Integer score15;
}