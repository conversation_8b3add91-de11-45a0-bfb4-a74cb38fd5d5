package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/5/23 10:16
 * @desciption: 学习计划限制配置
 */
@Data
public class OfferItemLimitConfigVo {

    @ApiModelProperty("已付押金支付方式限制1/0(开启/关闭)")
    private Boolean depositPaymentLimit = false;

    @ApiModelProperty("学费支付方式限制1/0(开启/关闭)")
    private Boolean tuitionPaymentLimit = false;
}
