package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.entity.ExpenseClaimFeeType;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ExpenseClaimFeeTypeMapper extends BaseMapper<ExpenseClaimFeeType> {


    /**
     * @return java.lang.Integer
     * @Description :获取最大排序值
     * @Param []
     * <AUTHOR>
     */
    Integer getMaxViewOrder();

    int checkName(String typeName);

    ExpenseClaimFeeType selectByFkAccountingItemId(Long id);
}