package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.financecenter.dto.query.PrepayApplicationFormQueryDto;
import com.get.financecenter.vo.PrepayApplicationFormVo;
import com.get.financecenter.entity.PrepayApplicationForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PrepayApplicationFormMapper extends BaseMapper<PrepayApplicationForm> {

    List<PrepayApplicationFormVo> getBorrowFormData(IPage iPage,
                                                    @Param("prepayApplicationFormQueryDto")PrepayApplicationFormQueryDto prepayApplicationFormQueryDto,
                                                    @Param("businessKeys") List<Long> businessKeys,
                                                    @Param("num") String num,
                                                    @Param("userId") String userId);

    Boolean getExistParentId(Long id);
}