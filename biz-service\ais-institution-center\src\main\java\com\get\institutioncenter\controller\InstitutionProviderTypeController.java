package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.dto.InstitutionProviderTypeDto;
import com.get.institutioncenter.vo.InstitutionProviderTypeVo;
import com.get.institutioncenter.service.IDeleteService;
import com.get.institutioncenter.service.IInstitutionProviderTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/12 11:23
 * @verison: 1.0
 * @description: 学校提供商类型管理控制器
 */
@Api(tags = "学校提供商类型管理")
@RestController
@RequestMapping("/institution/institutionProviderType")
public class InstitutionProviderTypeController {

    @Resource
    private IInstitutionProviderTypeService institutionProviderTypeService;
    @Resource
    private IDeleteService deleteService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/学校提供商类型管理/学校提供商类型详情")
    @GetMapping("/{id}")
    public ResponseBo detail(@PathVariable("id") Long id) {
        InstitutionProviderTypeVo data = institutionProviderTypeService.findInstitutionProviderTypeById(id);
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", data);
        return responseBo;
    }

    /**
     * 批量新增信息
     *
     * @param institutionProviderTypeDtos
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学校提供商类型管理/新增学校提供商类型")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(InstitutionProviderTypeDto.Add.class) ValidList<InstitutionProviderTypeDto> institutionProviderTypeDtos) {
        institutionProviderTypeService.batchAdd(institutionProviderTypeDtos);
        return SaveResponseBo.ok();
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/学校提供商类型管理/删除学校提供商类型")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        deleteService.deleteValidateProviderType(id);
        institutionProviderTypeService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param institutionProviderTypeDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/学校提供商类型管理/更新学校提供商类型")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated(InstitutionProviderTypeDto.Update.class) InstitutionProviderTypeDto institutionProviderTypeDto) {
        return UpdateResponseBo.ok(institutionProviderTypeService.updateInstitutionProviderType(institutionProviderTypeDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校提供商类型管理/查询学校提供商类型")
    @PostMapping("datas")
    public ResponseBo datas(@RequestBody SearchBean<InstitutionProviderTypeDto> page) {
        List<InstitutionProviderTypeVo> datas = institutionProviderTypeService.getInstitutionProviderTypes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 上移下移
     *
     * @param institutionProviderTypeDtos
     * @return
     * @
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/学校提供商类型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<InstitutionProviderTypeDto> institutionProviderTypeDtos) {
        institutionProviderTypeService.movingOrder(institutionProviderTypeDtos);
        return ResponseBo.ok();
    }


    /**
     * 学校提供商类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学校提供商类型下拉框数据", notes = "")
    @PostMapping("getinstitutionProviderTypeList")
    public ResponseBo<BaseSelectEntity> getinstitutionProviderTypeList() {
        return new ListResponseBo<>(institutionProviderTypeService.getinstitutionProviderTypeList());
    }
}
