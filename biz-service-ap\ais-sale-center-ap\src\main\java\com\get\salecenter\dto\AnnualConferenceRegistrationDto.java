package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/29 10:42
 * @verison: 1.0
 * @description:
 */
@Data
public class AnnualConferenceRegistrationDto {
    /**
     * 展位对象集合
     */
    @ApiModelProperty(value = "展位对象集合")
    List<ConventionRegistrationDto> registrationVos;

    /**
     * 赞助商对象
     */
    @ApiModelProperty(value = "赞助商对象")
    ConventionSponsorDto sponsorVo;

}
