package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.TravelClaimFeeTypeMapper;
import com.get.financecenter.dto.TravelClaimFeeTypeDto;
import com.get.financecenter.entity.TravelClaimFeeType;
import com.get.financecenter.service.TravelClaimFeeTypeService;
import com.get.financecenter.utils.GetAccountingCodeNameUtils;
import com.get.financecenter.vo.TravelClaimFeeTypeVo;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 差旅报销费用类型服务实现类
 */
@Service("financeTravelClaimFeeTypeService")
public class TravelClaimFeeTypeServiceImpl extends ServiceImpl<TravelClaimFeeTypeMapper, TravelClaimFeeType> implements TravelClaimFeeTypeService {
    @Resource
    private TravelClaimFeeTypeMapper travelClaimFeeTypeMapper;
    @Resource
    private UtilService utilService;

    @Resource
    private GetAccountingCodeNameUtils getAccountingCodeNameUtils;

    @Override
    public List<TravelClaimFeeTypeVo> getTravelClaimFeeTypes(TravelClaimFeeTypeDto travelClaimFeeTypeDto, Page page) {
        if (GeneralTool.isEmpty(travelClaimFeeTypeDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isNotEmpty(travelClaimFeeTypeDto.getKeyWord())) {
            travelClaimFeeTypeDto.setKeyWord(travelClaimFeeTypeDto.getKeyWord().replace(" ", "").trim());
        }

        LambdaQueryWrapper<TravelClaimFeeType> wrapper = new LambdaQueryWrapper();
        IPage<TravelClaimFeeType> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);

        List<TravelClaimFeeTypeVo> travelClaimFeeTypeVos = travelClaimFeeTypeMapper.getTravelClaimFeeTypes(pages, travelClaimFeeTypeDto);
        page.setAll((int) pages.getTotal());

        if (GeneralTool.isNotEmpty(travelClaimFeeTypeVos)) {
            for (TravelClaimFeeTypeVo travelClaimFeeTypeVo : travelClaimFeeTypeVos) {
                //处理科目类型参数
                if (GeneralTool.isNotEmpty(travelClaimFeeTypeVo.getFkAccountingItemId())) {
                    String accountingName = getAccountingCodeNameUtils.setAccountingCodeName(travelClaimFeeTypeVo.getFkAccountingItemId());
                    travelClaimFeeTypeVo.setAccountingItemName(accountingName);
                }

            }

        }
        return travelClaimFeeTypeVos;
    }

    @Override
    @Transactional
    public void batchAdd(List<TravelClaimFeeTypeDto> travelClaimFeeTypeDtos) {
        if (GeneralTool.isEmpty(travelClaimFeeTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (TravelClaimFeeTypeDto travelClaimFeeTypeDto : travelClaimFeeTypeDtos) {
            TravelClaimFeeType travelClaimFeeType = BeanCopyUtils.objClone(travelClaimFeeTypeDto, TravelClaimFeeType::new);
            checkParam(travelClaimFeeType);
            travelClaimFeeType.setTypeName(travelClaimFeeType.getTypeName().replace(" ", "").trim());
            if (travelClaimFeeTypeMapper.checkName(travelClaimFeeType.getTypeName()) > 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("duplicate_name") + "(" + travelClaimFeeType.getTypeName() + ")");
            }
            utilService.setCreateInfo(travelClaimFeeType);
            travelClaimFeeType.setViewOrder(travelClaimFeeTypeMapper.getMaxViewOrder());
            int insert = travelClaimFeeTypeMapper.insert(travelClaimFeeType);
            if (insert <= 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
        }

    }

    @Override
    public TravelClaimFeeTypeVo update(TravelClaimFeeTypeDto travelClaimFeeTypeDto) {
        if (GeneralTool.isEmpty(travelClaimFeeTypeDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(travelClaimFeeTypeDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        TravelClaimFeeType searchById = travelClaimFeeTypeMapper.selectById(travelClaimFeeTypeDto.getId());
        if (GeneralTool.isEmpty(searchById)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        TravelClaimFeeType travelClaimFeeType = BeanCopyUtils.objClone(travelClaimFeeTypeDto, TravelClaimFeeType::new);

        if (!travelClaimFeeType.getTypeName().equals(searchById.getTypeName())) {
            travelClaimFeeType.setTypeName(travelClaimFeeType.getTypeName().replace(" ", "").trim());
            if (travelClaimFeeTypeMapper.selectByTypeName(travelClaimFeeTypeDto.getTypeName()) > 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("duplicate_name") + "(" + travelClaimFeeTypeDto.getTypeName() + ")");
            }
        }

        utilService.updateUserInfoToEntity(travelClaimFeeType);
        travelClaimFeeTypeMapper.updateById(travelClaimFeeType);

        return findTavelClaimFeeTypeById(travelClaimFeeTypeDto.getId());
    }


    @Override
    public void deleteTravelClaimFeeType(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        TravelClaimFeeType travelClaimFeeType = travelClaimFeeTypeMapper.selectById(id);
        if (GeneralTool.isEmpty(travelClaimFeeType)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        travelClaimFeeTypeMapper.deleteById(id);
    }

    @Override
    public void sort(List<Long> ids) {
        if (GeneralTool.isEmpty(ids)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }

        if (ids.size() != 2) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("sort_param_error"));
        }

        List<TravelClaimFeeType> travelClaimFeeTypes = travelClaimFeeTypeMapper.selectBatchIds(ids);
        if (travelClaimFeeTypes.size() != ids.size()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }

        Map<Long, Integer> orderMap = travelClaimFeeTypes.stream().collect(Collectors.toMap(TravelClaimFeeType::getId, TravelClaimFeeType::getViewOrder));
        List<TravelClaimFeeType> updateList = new ArrayList<>();

        for (int i = 0; i < ids.size(); i++) {
            Long currentId = ids.get(i);
            Long swapId = ids.get((i + 1) % ids.size());
            if (orderMap.containsKey(swapId)) {
                TravelClaimFeeType entity = travelClaimFeeTypeMapper.selectById(currentId);
                if (entity.getViewOrder() == orderMap.get(swapId)) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("inconsistent_data"));
                }
                entity.setViewOrder(orderMap.get(swapId));
                utilService.updateUserInfoToEntity(entity);
                updateList.add(entity);
            }
        }

        if (!updateList.isEmpty()) {
            travelClaimFeeTypeMapper.updateBatchById(updateList);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(Integer start, Integer end) {
        if (GeneralTool.isEmpty(start) || GeneralTool.isEmpty( end)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("sort_param_error"));
        }
        LambdaQueryWrapper<TravelClaimFeeType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (end > start){
            lambdaQueryWrapper.between(TravelClaimFeeType::getViewOrder,start,end).orderByDesc(TravelClaimFeeType::getViewOrder);
        }else {
            lambdaQueryWrapper.between(TravelClaimFeeType::getViewOrder,end,start).orderByDesc(TravelClaimFeeType::getViewOrder);

        }
        List<TravelClaimFeeType> travelClaimFeeTypes = list(lambdaQueryWrapper);
        List<TravelClaimFeeType> updateList = new ArrayList<>();
        if (end > start){
            int finalEnd = end;
            List<TravelClaimFeeType> sortedList = Lists.newArrayList();
            TravelClaimFeeType travelClaimFeeTypeLast = travelClaimFeeTypes.get(travelClaimFeeTypes.size() - 1);
            sortedList.add(travelClaimFeeTypeLast);
            travelClaimFeeTypes.remove(travelClaimFeeTypes.size() - 1);
            sortedList.addAll(travelClaimFeeTypes);
            for (TravelClaimFeeType travelClaimFeeType : sortedList) {
                travelClaimFeeType.setViewOrder(finalEnd);
                finalEnd--;
            }
            updateList.addAll(sortedList);
        }else {
            int finalStart = start;
            List<TravelClaimFeeType> sortedList = Lists.newArrayList();
            TravelClaimFeeType travelClaimFeeTypeFirst = travelClaimFeeTypes.get(0);
            travelClaimFeeTypes.remove(0);
            sortedList.addAll(travelClaimFeeTypes);
            sortedList.add(travelClaimFeeTypeFirst);
            for (TravelClaimFeeType travelClaimFeeType: sortedList) {
                travelClaimFeeType.setViewOrder(finalStart);
                finalStart--;
            }
            updateList.addAll(sortedList);
        }

        if (GeneralTool.isNotEmpty(updateList)){
            boolean batch = updateBatchById(updateList);
            if (!batch){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
    }

    public TravelClaimFeeTypeVo findTavelClaimFeeTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        TravelClaimFeeType travelClaimFeeType = travelClaimFeeTypeMapper.selectById(id);
        if (GeneralTool.isEmpty(travelClaimFeeType)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        TravelClaimFeeTypeVo travelClaimFeeTypeVo = BeanCopyUtils.objClone(travelClaimFeeType, TravelClaimFeeTypeVo::new);
        return travelClaimFeeTypeVo;
    }

    private static void checkParam(TravelClaimFeeType travelClaimFeeType) {
        if (GeneralTool.isEmpty(travelClaimFeeType.getTypeName())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("type_name_null"));
        }
//        if (GeneralTool.isEmpty(travelClaimFeeType.getFkAccountingItemId())) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("accounting_item_id_null"));
//        }
    }

}

