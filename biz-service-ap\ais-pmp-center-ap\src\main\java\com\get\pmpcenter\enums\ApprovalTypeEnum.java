package com.get.pmpcenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 工作台-审核类型枚举类
 */

@Getter
@AllArgsConstructor
public enum ApprovalTypeEnum {

    PMP_PROVIDER("PMP_PROVIDER", "合同端佣金方案审批","Institution Provider Commission Plan Approval"),
    PMP_AGENT("PMP_AGENT", "代理佣金方案审批","Agent Commission Plan Approval"),
    PMP_PROVIDER_BATCH_TERRITORY("PMP_PROVIDER_BATCH_TERRITORY", "方案territory审批","Territory Approval"),
    ;

    private String code;

    private String msg;

    private String enMsg;

    public static ApprovalTypeEnum getEnumByCode(String code) {
        for (ApprovalTypeEnum value : ApprovalTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
