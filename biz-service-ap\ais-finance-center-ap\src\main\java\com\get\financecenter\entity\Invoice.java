package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("m_invoice")
public class Invoice extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

//    /**
//     * 学校提供商Id（开票对象）
//     */
//    @ApiModelProperty(value = "学校提供商Id（开票对象）")
//    private Long fkInstitutionProviderId;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
    /**
     * 发票编号
     */
    @ApiModelProperty(value = "发票编号")
    private String num;
    /**
     * 国内发票编号
     */
    @ApiModelProperty(value = "国内发票编号")
    private String localInvoiceNum;

    /**
     * po号码
     */
    @ApiModelProperty(value = "po号码")
    private String poNum;

    /**
     * 应付类型关键字，枚举，如：m_student_offer_item
     */
    @ApiModelProperty(value = "业务类型关键字，枚举，如：m_student_offer_item留学申请/m_student_insurance留学保险/m_student_accommodation留学住宿")
    private String fkTypeKey;
    /**
     * 开票日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开票日期")
    private Date invoiceDate;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;
    /**
     * 付款方实付单号
     */
    @ApiModelProperty(value = "付款方实付单号")
    private String typeTargetPaymentNum;
    /**
     * 开票金额
     */
    @ApiModelProperty(value = "开票金额")
    private BigDecimal invoiceAmount;
    /**
     * 实收日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "实收日期")
    private Date receiptDate;
    /**
     * 实收币种
     */
    @ApiModelProperty(value = "实收币种")
    private String fkCurrencyTypeNumReceipt;
    /**
     * 实收金额
     */
    @ApiModelProperty("实收金额")
    private BigDecimal receiptAmount;
    /**
     * 原币种
     */
    @ApiModelProperty("原币种")
    private String fkCurrencyTypeNumOrc;
    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;
    /**
     * 状态：0作废/1打开
     */
    @ApiModelProperty(value = "状态：0作废/1打开")
    private Integer status;
}