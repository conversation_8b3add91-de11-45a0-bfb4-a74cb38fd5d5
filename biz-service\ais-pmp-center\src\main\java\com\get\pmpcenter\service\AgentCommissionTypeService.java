package com.get.pmpcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.pmpcenter.dto.agent.AgentCommissionTypeDto;
import com.get.pmpcenter.entity.AgentCommissionType;
import com.get.pmpcenter.vo.agent.AgentCommissionTypeVo;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:17
 * @Version 1.0
 */
public interface AgentCommissionTypeService extends IService<AgentCommissionType> {

    /**
     * 获取代理佣金类型列表-代理商等级列表
     *
     * @return
     */
    List<AgentCommissionType> getAgentCommissionTypeList(Long companyId);

    /**
     * 新增代理佣金类型
     * @param agentCommissionTypeDto
     * @return
     */
    int add(AgentCommissionTypeDto agentCommissionTypeDto);

    /**
     * 代理佣金类型列表
     * @param agentCommissionTypeDto
     * @param page
     * @return
     */
    List<AgentCommissionTypeVo> dataList(AgentCommissionTypeDto agentCommissionTypeDto, Page page);

    Long update(AgentCommissionTypeDto agentCommissionTypeDto);

    void delete(Long id);

    AgentCommissionTypeVo findAgentCommissionTypeById(Long id);

    void sortAgentCommissionType(List<AgentCommissionTypeDto> agentCommissionTypeDtos);
}
