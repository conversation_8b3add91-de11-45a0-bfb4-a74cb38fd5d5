package com.get.votingcenter.dao.appvoting;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.votingcenter.vo.UserVotingVo;
import com.get.votingcenter.entity.UserVoting;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
@DS("appvoting")
public interface UserVotingMapper extends BaseMapper<UserVoting> {

    int insertSelective(UserVoting record);

    /**
     * 获取选项的所有投票数
     *
     * @param optionId
     * @return
     */
    Long getVotingCountByOptionId(@Param("optionId") Long optionId);


    /**
     * @Description: 随机抽奖
     * @Author: Jerry
     * @Date:10:38 2021/10/20
     */
    List<UserVotingVo> generateUserAward(@Param("votingItemOptionIds") Set<Long> votingItemOptionIds,
                                         @Param("fkUserIds") Set<Long> fkUserIds,
                                         @Param("generateCount") Integer generateCount);

    /**
     * @Description: 获取投过票的用户
     * @Author: Jerry
     * @Date:9:33 2021/10/21
     */
    List<UserVotingVo> getVotingResult(@Param("votingItemOptionIds") Set<Long> votingItemOptionIds);
}