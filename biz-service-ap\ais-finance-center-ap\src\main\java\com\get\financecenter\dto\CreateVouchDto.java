package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class CreateVouchDto {

    @NotBlank(message = "财务单据表名不能为空")
    @ApiModelProperty(value = "财务单据表名")
    private String fkTableName;

    @NotNull(message = "财务单据表记录Id不能为空")
    @ApiModelProperty(value = "财务单据表记录Id")
    private Long fkTableId;

    /**
     * 单据编号
     */
    private String num;

}
