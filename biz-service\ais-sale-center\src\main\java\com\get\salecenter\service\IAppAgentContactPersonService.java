package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.service.GetService;
import com.get.salecenter.dto.AppAgentContactPersonAddDto;
import com.get.salecenter.dto.AppAgentContactPersonListDto;
import com.get.salecenter.dto.AppAgentContactPersonUpdateDto;
import com.get.salecenter.entity.AppAgentContactPerson;
import com.get.salecenter.vo.AppAgentContactPersonVo;

import java.util.List;
import java.util.Set;

/**
 * @author: Hardy
 * @create: 2022/11/17 18:14
 * @verison: 1.0
 * @description:
 */
public interface IAppAgentContactPersonService extends GetService<AppAgentContactPerson> {

    /**
     * 列表搜索
     * @param appAgentContactPersonListDto
     * @param page
     * @return
     */
    List<AppAgentContactPerson> getAppAgentContactPersons(AppAgentContactPersonListDto appAgentContactPersonListDto, Page page);

    /**
     * 新增
     * @param appAgentContactPersonAddDto
     * @return
     */
    Long addAppAgentContactPerson(AppAgentContactPersonAddDto appAgentContactPersonAddDto);

    /**
     * 详情接口
     * @param id
     * @return
     */
    AppAgentContactPersonVo finAppAgentContactPersonById(Long id);

    /**
     * 编辑接口
     * @param appAgentContactPersonUpdateDto
     * @return
     */
    AppAgentContactPersonVo updateAppAgentContactPerson(AppAgentContactPersonUpdateDto appAgentContactPersonUpdateDto);

    Set<String> getUsedNewTypeKeys(Long fkAppAgentId);

    /**
     * 根据代理申请id获取数据
     *
     * @param fkAppAgentId
     * @return
     */
    List<AppAgentContactPersonVo> getByAppAgentId(Long fkAppAgentId);

}
