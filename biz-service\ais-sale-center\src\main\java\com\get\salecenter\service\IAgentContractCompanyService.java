package com.get.salecenter.service;


import com.get.salecenter.dto.AgentContractCompanyDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/9/15
 * @TIME: 15:23
 * @Description: 合同公司安全配置
 **/
public interface IAgentContractCompanyService {

    /**
     * @return void
     * @Description: 编辑中间表关系
     * @Param [contractCompanyVo]
     * <AUTHOR>
     */
    void editAgentContractCompany(List<AgentContractCompanyDto> contractCompanyVo);


    /**
     * @return java.lang.Long
     * @Description: 新增关系
     * @Param [relation]
     * <AUTHOR>
     */
    Long addRelation(AgentContractCompanyDto relation);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据公司id查询合同ids
     * @Param [companyIds]
     * <AUTHOR>
     */
    List<Long> getRelationByCompanyId(List<Long> companyIds);


    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据合同id查询公司ids
     * @Param [contractId]
     * <AUTHOR>
     */
    List<Long> getRelationByContractId(Long contractId);


    /**
     * 根据合同ids查询公司ids
     *
     * @param contractIds 合同ids
     * @return
     */
    Map<Long, Set<Long>> getRelationByContractIds(Set<Long> contractIds);

    /**
     * 获取代理合同公司
     * @param contractIds
     * @return
     */
    Map<Long,Long> getAgentContractCompanyId(List<Long> contractIds);
}
