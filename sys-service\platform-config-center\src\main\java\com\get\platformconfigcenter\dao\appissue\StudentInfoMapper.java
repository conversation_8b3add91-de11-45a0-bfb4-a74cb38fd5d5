package com.get.platformconfigcenter.dao.appissue;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.platformconfigcenter.vo.StudentInfoVo;
import com.get.platformconfigcenter.entity.StudentInfo;
import com.get.platformconfigcenter.dto.StudentInfoDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS("issuedb")
public interface StudentInfoMapper extends BaseMapper<StudentInfo> {
    int insert(StudentInfo record);

    int insertSelective(StudentInfo record);

    int updateByPrimaryKeySelective(StudentInfo record);

    int updateByPrimaryKey(StudentInfo record);

    List<StudentInfoVo> getStudentInfo(IPage<StudentInfoVo> page, @Param(value = "studentInfoDto") StudentInfoDto studentInfoDto);

    void updateStudentStatusByStudentId(StudentInfo studentInfo);
}