package com.get.financecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/12/31
 * @TIME: 11:35
 * @Description:
 **/
@Data
public class ReceiptFormItemDto   extends BaseVoEntity{
    /**
     * 收款单Id
     */
    @ApiModelProperty(value = "收款单Id", required = true)
    @NotNull(message = "收款单Id不能为空", groups = {Add.class, Update.class})
    private Long fkReceiptFormId;

    /**
     * 应收计划Id
     */
    @ApiModelProperty(value = "应收计划Id")
    @NotNull(message = "应收计划Id不能为空", groups = {Add.class, Update.class})
    private Long fkReceivablePlanId;

    /**
     * 收款金额（拆分/总）
     */
    @NotNull(message = "收款金额不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "收款金额（拆分/总）")
    private BigDecimal amountReceipt;

    /**
     * 汇率（折合应收币种汇率）
     */
    @NotNull(message = "折合应收币种汇率不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "汇率（折合应收币种汇率）")
    private BigDecimal exchangeRateReceivable;

    /**
     * 收款金额（折合应收币种金额）
     */
    @NotNull(message = "收款金额（折合应收币种金额）不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "收款金额（折合应收币种金额）")
    private BigDecimal amountReceivable;

    /**
     * 汇率调整（可正可负，为平衡计算应收金额）
     */
    @NotNull(message = "汇率调整不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "汇率调整（可正可负，为平衡计算应收金额）")
    private BigDecimal amountExchangeRate;

    /**
     * 汇率（折合港币）
     */
    @ApiModelProperty(value = "汇率（折合港币）")
    private BigDecimal exchangeRateHkd;

    /**
     * 收款金额（折合港币）
     */
    @ApiModelProperty(value = "收款金额（折合港币）")
    private BigDecimal amountHkd;

    /**
     * 汇率（折合人民币）
     */
    @ApiModelProperty(value = "汇率（折合人民币）")
    private BigDecimal exchangeRateRmb;

    /**
     * 收款金额（折合人民币）
     */
    @ApiModelProperty(value = "收款金额（折合人民币）")
    private BigDecimal amountRmb;

    /**
     * 手续费
     */
    @NotNull(message = "手续费不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "手续费")
    private BigDecimal serviceFee;

    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 创建时间(开始)
     */
    @ApiModelProperty("创建时间(开始)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;

    /**
     * 创建时间(结束)
     */
    @ApiModelProperty("创建时间(结束)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    /**
     * 目标类型关键字，枚举：m_agent代理
     */
    @ApiModelProperty(value = "目标类型关键字，枚举：m_student_offer_item")
    private String fkTypeKey;

    /**
     * 对应记录Id
     */
    @ApiModelProperty(value = "目标类型id")
    private Long fkTypeTargetId;

    /**
     * 发票编号
     */
    @ApiModelProperty(value = "发票编号")
    private String fkInvoiceNum;

    /**
     * 原币种
     */
    @ApiModelProperty(value = "原币种")
    private String fkCurrencyTypeNumOrc;

    /**
     * 实收金额
     */
    @ApiModelProperty(value = "实收金额")
    private String fkCurrencyTypeNumReceipt;

    /**
     * 收款单id列表
     */
    @ApiModelProperty(value = "收款单id列表")
    private List<Long> fkReceiptFormIds;

    @ApiModelProperty(value = "学生名字")
    private String studentName;

    @ApiModelProperty(value = "实收汇率")
    private BigDecimal exchangeRate;
}
