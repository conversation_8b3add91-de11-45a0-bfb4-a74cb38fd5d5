package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2024/3/25
 * @TIME: 11:10
 * @Description:业务标记Dto
 **/
@Data
public class NameLabelVo extends BaseEntity {
    @ApiModelProperty(value = "表名：m_institution学校")
    private String fkTableName;

    @ApiModelProperty(value = "表Id")
    private Long fkTableId;

    @ApiModelProperty(value = "0名字前面/1名字后面")
    private Integer positionType;

    @ApiModelProperty(value = "目标类型")
    private String type;

    @ApiModelProperty(value = "目标名称")
    private String name;

    @ApiModelProperty(value = "标签")
    private String label;

    @ApiModelProperty(value = "排序")
    private Integer viewOrder;
}
