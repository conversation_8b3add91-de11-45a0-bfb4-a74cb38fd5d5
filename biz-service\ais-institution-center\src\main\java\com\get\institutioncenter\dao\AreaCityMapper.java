package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.institutioncenter.dto.AreaCityDto;
import com.get.institutioncenter.entity.AreaCity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/28 10:52
 * @verison: 1.0
 * @description: 区域管理-城市配置mapper
 */
@Mapper
public interface AreaCityMapper extends BaseMapper<AreaCity> {

    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(AreaCity record);

    /**
     * @return java.lang.String
     * @Description :通过城市id 查找对应城市名称
     * @Param [id]
     * <AUTHOR>
     */
    String getCityNameById(Long id);
    /**
     * @Description :通过城市id 查找对应城市全称
     * @Param [id]
     * @return java.lang.String
     * <AUTHOR>
     */
    /**
     * @return java.lang.String
     * @Description :通过城市id 查找对应城市名称
     * @Param [id]
     * <AUTHOR>
     */
    String getCityNameChnById(Long id);

    /**
     * @return java.lang.String
     * @Description :通过城市id 查找对应城市全称
     * @Param [id]
     * <AUTHOR>
     */
    String getCityFullNameById(Long id);

    /**
     * @return java.lang.Integer
     * @Description :获取最大排序值
     * @Param []
     * <AUTHOR>
     */
    Integer getMaxViewOrder();

    /**
     * @return list
     * @Description :获取城市列表
     * @Param areaCityDto
     * <AUTHOR>
     */
    List<AreaCity> getAreaCitys(IPage<AreaCity> page,@Param("areaCityDto") AreaCityDto areaCityDto);


    /**
     * @return list
     * @Description :获取城市列表
     * @Param areaCityDto
     * <AUTHOR>
     */
    List<AreaCity> selectAreaCityByVo(@Param("areaCityDto")AreaCityDto areaCityDto);


    String getCityChnNameById(@Param("id") Long id);

    /**
     * 获取对应国家、公司下 有申请计划的代理所在的 城市下拉框数据
     *
     * @Date 19:01 2023/1/5
     * <AUTHOR>
     */
    List<BaseSelectEntity> getExistsAgentOfferItemAreaStateCityList(@Param("companyId") Long companyId,
                                                                    @Param("stateIds") String stateIds,
                                                                    @Param("countryIds") List<Long> countryIds);

    /**
     * 获取对应国家、公司下的代理所在的 城市下拉框数据
     *
     * @Date 10:36 2023/3/23
     * <AUTHOR>
     */
    List<BaseSelectEntity> getExistsAgentAreaStateCityList(@Param("companyId") Long companyId,
                                                           @Param("stateIds") String stateIds,
                                                           @Param("countryIds") List<Long> countryIds);
}