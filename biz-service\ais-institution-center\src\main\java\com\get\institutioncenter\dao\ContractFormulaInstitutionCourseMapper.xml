<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.ContractFormulaInstitutionCourseMapper">

  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.ContractFormulaInstitutionCourse" keyProperty="id" useGeneratedKeys="true">
    insert into r_contract_formula_institution_course
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkContractFormulaId != null">
        fk_contract_formula_id,
      </if>
      <if test="fkInstitutionCourseId != null">
        fk_institution_course_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkContractFormulaId != null">
        #{fkContractFormulaId,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionCourseId != null">
        #{fkInstitutionCourseId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getCourseIdListByFkid" resultType="java.lang.Long">
    select
     fk_institution_course_id
    from
     r_contract_formula_institution_course
    where
     fk_contract_formula_id = #{contractFormulaId}
  </select>

  <select id="getCourseNameByFkid" resultType="java.lang.String">
    SELECT
    CASE WHEN IFNULL(b.name_chn,'')='' THEN b.`name` ELSE CONCAT(b.`name`,'（',b.name_chn,'）') END fullName
    FROM
        r_contract_formula_institution_course a
        LEFT JOIN m_institution_course b ON a.fk_institution_course_id = b.id
    WHERE
        fk_contract_formula_id = #{contractFormulaId}
  </select>
  <select id="isExistByCourseId" resultType="java.lang.Boolean">
    SELECT IFNULL(max(id),0) id from r_contract_formula_institution_course where fk_institution_course_id=#{courseId}
  </select>

  <select id="getCourseIdListByFaculty"  resultType="java.lang.Long">
    SELECT ic.id FROM m_institution_course AS ic
    -- 合同公式课程
--     INNER JOIN r_contract_formula_institution_course AS cfic ON cfic.fk_institution_course_id = ic.id
--     AND cfic.fk_contract_formula_id = 1

    -- 合同公式校区
    <if test="zoneIdList != null and zoneIdList.size() > 0">
      INNER JOIN r_institution_course_zone AS icz ON icz.fk_institution_course_id = ic.id
      AND icz.fk_institution_zone_id IN
      <foreach collection="zoneIdList" item="zoneId" index="index" open="(" separator="," close=")">
        #{zoneId,jdbcType=BIGINT}
      </foreach>
    </if>

    -- 合同公式学院
    <if test="facultyIdList != null and facultyIdList.size() > 0">
    INNER JOIN r_institution_course_faculty AS icf ON icf.fk_institution_course_id = ic.id
    AND icf.fk_institution_faculty_id IN
      <foreach collection="facultyIdList" item="facultyId" index="index" open="(" separator="," close=")">
        #{facultyId,jdbcType=BIGINT}
      </foreach>
    </if>

    -- 合同公式课程等级
    <if test="majorLevelIdList != null and majorLevelIdList.size() > 0">
    INNER JOIN r_institution_course_major_level AS icml ON icml.fk_institution_course_id = ic.id
    AND icml.fk_major_level_id IN
      <foreach collection="majorLevelIdList" item="majorLevelId" index="index" open="(" separator="," close=")">
        #{majorLevelId,jdbcType=BIGINT}
      </foreach>
    </if>

    -- 合同公式课程类型
    <if test="courseTypeIdList != null and courseTypeIdList.size() > 0">
    INNER JOIN r_institution_course_type AS ict ON ict.fk_institution_course_id = ic.id
     AND ict.fk_course_type_id IN
      <foreach collection="courseTypeIdList" item="courseTypeId" index="index" open="(" separator="," close=")">
        #{courseTypeId,jdbcType=BIGINT}
      </foreach>
    </if>

    <where>
      <if test="courseIdList != null and courseTypeIdList.size() > 0">
        AND ic.id IN
        <foreach collection="courseIdList" item="courseId" index="index" open="(" separator="," close=")">
          #{courseId,jdbcType=BIGINT}
        </foreach>
      </if>
    </where>
    GROUP BY ic.id
  </select>
</mapper>