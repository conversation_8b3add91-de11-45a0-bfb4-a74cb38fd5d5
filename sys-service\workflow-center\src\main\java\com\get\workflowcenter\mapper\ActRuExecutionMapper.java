package com.get.workflowcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.log.model.LogLogin;
import com.get.workflowcenter.vo.ActRuExecutionVo;
import com.get.workflowcenter.entity.ActRuExecution;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ActRuExecutionMapper extends BaseMapper<ActRuExecution> {


    int insertSelective(ActRuExecution record);

    /**
     * @ Description :
     * @ Param [procdefKey, ID, procInstId]
     * @ return java.util.List<com.get.workflowcenter.vo.ActRuExecutionDto>
     * @ author LEO
     */
    List<ActRuExecutionVo> getExecutionList(IPage<LogLogin> page, @Param("procdefKey") String procdefKey, @Param("id") String ID, @Param("procInstId") String procInstId);

    /**
     * @ Description :
     * @ Param [procdefKey, ID, procInstId, username]
     * @ return java.util.List<com.get.workflowcenter.vo.ActRuExecutionDto>
     * @ author LEO
     */
    List<ActRuExecutionVo> getExecutionListByNormalUser(IPage<LogLogin> page, @Param("procdefKey") String procdefKey, @Param("id") String ID, @Param("procInstId") String procInstId, @Param("username") String username);

}