package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

//@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class BdStudentBonusVo {

    //    【所属公司】提示词：所属公司。根据自己权限，多选下拉，默认全选
    @ApiModelProperty(value = "公司id")
    private Long companyId;
    @ApiModelProperty(value = "公司名")
    private String companyName;

    @ApiModelProperty(value = "公司id")
    private Long fkAreaCountryId;

    //【代理名称】提示词：申请绑定代理名称/名称备注/原公司/编号。单选
    @ApiModelProperty(value = "代理名称/代理编号")
    private String agentNameNum;
    //【BD名称】提示词：申请绑定BD名称（中英）或BD编号。多选
    @ApiModelProperty(value = "BD名称/BD编号")
    private String bdNameNum;

    //【课程名称】提示词：课程名称（中/英）。模糊搜索
    @ApiModelProperty(value = "旧系统课程名称")
    private String courseCustomName;
    //【实收时间】提示词：实收时间（开始范围）- 实收时间（结束范围）
    @ApiModelProperty(value = "实收开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "实收结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyShortName;

    /**
     * 学生编号
     */
    @ApiModelProperty(value = "学生编号")
    private String studentNum;

    /**
     * 学生名称
     */
    @ApiModelProperty(value = "学生名称")
    private String studentName;
    @ApiModelProperty(value = "学生名称")
    private String studentNameChn;


    @ApiModelProperty(value = "课程id")
    private Long fkInstitutionCourseId;



    /**
     * 国家
     */
    @ApiModelProperty(value = "国家")
    private String countryName;

    /**
     * 国家中文名
     */
    @ApiModelProperty(value = "国家中文名")
    private String countryNameChn;

    /**
     * 学校名
     */
    @ApiModelProperty(value = "学校名")
    private String institutionName;
    /**
     * 学校中文名
     */
    @ApiModelProperty(value = "学校中文名")
    private String institutionNameChn;
    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    private String fkAgentName;

    public String getFkAgentName() {
        if (StringUtils.isNotBlank(nameNote)) {
            fkAgentName += "(" + nameNote +")";
        }
        return fkAgentName;
    }

    @ApiModelProperty("代理名称备注")
    private String nameNote;

    /**
     * 课程名
     */
    @ApiModelProperty(value = "课程名")
    private String institutionCourseName;

    /**
     * 课程中文名
     */
    @ApiModelProperty(value = "课程中文名")
    private String institutionCourseNameChn;

//    /**
//     * 课程长度类型(0周、1月、2年、3学期)
//     */
//    @ApiModelProperty(value = "课程长度类型(0周、1月、2年、3学期)")
//    private Integer durationType;
//
//    /**
//     * 课程长度
//     */
//    @ApiModelProperty(value = "课程长度")
//    private BigDecimal duration;

//    /**
//     * 应收金额
//     */
//    @ApiModelProperty(value = "应收金额")
//    private String receivablePlanAmountInfo;
//
//    /**
//     * 实收金额
//     */
//    @ApiModelProperty(value = "实收金额")
//    private String receivableActualAmountInfo;

    /**
     * 学校id
     */
    @ApiModelProperty(value = "学校id")
    private String fkInstitutionId;

    @ApiModelProperty("发票绑定金额")
    private BigDecimal invoiceBindAmount;


    /**
     * 应收金额
     */
    @ApiModelProperty("应收金额")
    private String receivableActualAmount;
    /**
     * 实收金额
     */
    @ApiModelProperty("实收金额")
    private String amountReceivable;

    /**
     * 应付金额
     */
    @ApiModelProperty("应付金额")
    private String payableAmount;


    @ApiModelProperty(value = "实收时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date receiptDate;


    @ApiModelProperty("发票绑定应付金额")
    private BigDecimal invoicePayableActualAmount;

    /**
     * 实收金额币种
     */
    @ApiModelProperty("实收金额币种")
    private String receivableActualAmountCurrencyType;

    /**
     * 旧系统学校名称
     */
    @ApiModelProperty(value = "旧系统学校名称")
    private String oldInstitutionName;

    /**
     * 旧系统学校全称
     */
    @ApiModelProperty(value = "旧系统学校全称")
    private String oldInstitutionFullName;

    /**
     * 旧系统课程名称
     */
    @ApiModelProperty(value = "旧系统课程名称")
    private String oldCourseCustomName;

    @ApiModelProperty(value = "bd名称")
    private String bdName;

    @ApiModelProperty(value = "代理id")
    private Long agentId;

    @ApiModelProperty(value = "代理编号")
    private String agentNum;
    @ApiModelProperty(value = "代理名称")
    private String agentName;

//    @ApiModelProperty(value = "BD+代理编号")
//    private String fkBdAgentNum;

//    public String getFkBdAgentNum() {
//        if (StringUtils.isNotBlank(bdName)) {
//            fkBdAgentNum = bdName + " " + agentNum;
//        }else {
//            fkBdAgentNum= agentNum;
//        }
//        return fkBdAgentNum;
//    }



    @ApiModelProperty(value = "渠道名")
    private String channelName;

    @ApiModelProperty(value = "实收金额")
    private String receivableActualAmountInfo;

    @ApiModelProperty("应收金额币种")
    private String receivableCurrencyTypeNum;





    @ApiModelProperty("开学时间（申请计划延时入学时间）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deferOpeningTime;

    @ApiModelProperty(value = "佣金费率")
    private String commissionRate;

    @ApiModelProperty(value = "学费金额")
    private BigDecimal tuitionAmount;

    @ApiModelProperty(value = "代理佣金费率")
    private String agentCommissionRate;

    @ApiModelProperty(value = "奖金费率")
    private BigDecimal bonusCommissionRate;
    
    @ApiModelProperty(value = "固定奖金")
    private BigDecimal bonusFixedAmount;

    @ApiModelProperty(value = "BD奖金")
    private BigDecimal bdBonus;

    @ApiModelProperty("代理标签")
    private List<AgentLabelVo> agentLabelVos;


//    @ApiModelProperty("申请计划创建时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date itemGmtCreate;

}
