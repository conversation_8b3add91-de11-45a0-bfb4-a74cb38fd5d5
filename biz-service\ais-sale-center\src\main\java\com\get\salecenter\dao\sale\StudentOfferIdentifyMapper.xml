<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.StudentOfferIdentifyMapper">
  <resultMap id="BaseResultMap" type="com.get.salecenter.entity.StudentOfferIdentify">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_student_offer_item_id" jdbcType="BIGINT" property="fkStudentOfferItemId" />
    <result column="fk_currency_type_num" jdbcType="VARCHAR" property="fkCurrencyTypeNum" />
    <result column="tuition_amount" jdbcType="DECIMAL" property="tuitionAmount" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>


</mapper>