package com.get.salecenter.utils;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.ObjectUtil;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.salecenter.dto.EmailSendContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 邮件发送工具类
 * 
 * <AUTHOR>
 * @date 2025/07/18
 */
@Slf4j
@Component
public class EmailSenderUtils {

    @Resource
    private IReminderCenterClient centerClient;

    /**
     * 发送单个邮件
     *
     * @param context 邮件发送上下文
     */
    public void sendSingleEmail(EmailSendContext context) {
        List<EmailSendContext> contexts = Arrays.asList(context);
        sendBatchEmails(contexts, context.getTableId());
    }

    /**
     * 批量发送邮件
     *
     * @param contexts   邮件发送上下文列表
     */
    public void sendBatchEmails(List<EmailSendContext> contexts) {
        if (CollectionUtil.isEmpty(contexts)) {
            log.warn("邮件发送上下文为空，跳过发送");
            return;
        }

        // 参数校验
        for (EmailSendContext context : contexts) {
            // 校验收件人邮箱
            if (GeneralTool.isEmpty(context.getRecipient())) {
                log.error("收件人邮箱为空");
                throw new GetServiceException(LocaleMessageUtils.getMessage("email_recipient_not_empty"));
            }

            // 校验邮件模板
            if (context.getEmailTemplate() == null) {
                log.error("邮件模板为空");
                throw new GetServiceException(LocaleMessageUtils.getMessage("email_template_not_empty"));
            }

            // 校验邮箱格式
            if (!isValidEmail(context.getRecipient())) {
                log.error("收件人邮箱格式不正确: {}", context.getRecipient());
                throw new GetServiceException(LocaleMessageUtils.getMessage("email_format_invalid"));
            }
        }

        try {
            log.info("开始发送邮件, 邮件数量: {}", contexts.size());

            List<EmailSenderQueue> emailSenderQueueList = new ArrayList<>();

            for (EmailSendContext context : contexts) {
                EmailSenderQueue emailSenderQueue = buildEmailSenderQueue(context);
                emailSenderQueueList.add(emailSenderQueue);

                log.debug("构建邮件队列, 收件人: {}, 模板: {}", context.getRecipient(), context.getEmailTemplate().getEmailTemplateKey());
            }

            // 批量发送邮件
            Result<Boolean> result = centerClient.batchAddEmailQueue(emailSenderQueueList);
            if (!result.isSuccess()) {
                log.error("添加邮件队列失败, 错误码: {}, 错误信息: {}"
                        , result.getCode(), result.getMessage());
                throw new GetServiceException(LocaleMessageUtils.getMessage(result.getMessage()));
            }

            log.info("成功添加邮件队列, 邮件数量: {}", emailSenderQueueList.size());

        } catch (Exception e) {
            log.error("发送邮件异常", e);
            throw new GetServiceException(LocaleMessageUtils.getMessage("mail_send_failed"));
        }
    }

    /**
     * 批量发送邮件
     *
     * @param contexts   邮件发送上下文列表
     * @param businessId 业务主键ID（用于日志记录）
     */
    public void sendBatchEmails(List<EmailSendContext> contexts, Long businessId) {
        if (CollectionUtil.isEmpty(contexts)) {
            log.warn("邮件发送上下文为空，跳过发送，业务ID: {}", businessId);
            return;
        }

        // 参数校验
//        for (EmailSendContext context : contexts) {
//            // 校验收件人邮箱
//            if (GeneralTool.isEmpty(context.getRecipient())) {
//                log.error("收件人邮箱为空，业务ID: {}", businessId);
//                throw new GetServiceException(LocaleMessageUtils.getMessage("email_recipient_not_empty"));
//            }
//
//            // 校验邮件模板
//            if (context.getEmailTemplate() == null) {
//                log.error("邮件模板为空，业务ID: {}", businessId);
//                throw new GetServiceException(LocaleMessageUtils.getMessage("email_template_not_empty"));
//            }
//
//            // 校验邮箱格式
//            if (!isValidEmail(context.getRecipient())) {
//                log.error("收件人邮箱格式不正确: {}, 业务ID: {}", context.getRecipient(), businessId);
//                throw new GetServiceException(LocaleMessageUtils.getMessage("email_format_invalid"));
//            }
//        }
        contexts = contexts.stream().filter(context -> {
            return ObjectUtils.isNotNull(context)
                    && GeneralTool.isNotEmpty(context.getRecipient())
                    && GeneralTool.isNotEmpty(context.getEmailTemplate())
                    && isValidEmail(context.getRecipient());
        }).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(contexts)) {
            return;
        }

        try {
            log.info("开始发送邮件，业务ID: {}, 邮件数量: {}", businessId, contexts.size());

            List<EmailSenderQueue> emailSenderQueueList = new ArrayList<>();

            for (EmailSendContext context : contexts) {
                EmailSenderQueue emailSenderQueue = buildEmailSenderQueue(context);
                emailSenderQueueList.add(emailSenderQueue);

                log.debug("构建邮件队列，业务ID: {}, 收件人: {}, 模板: {}",
                    businessId, context.getRecipient(), context.getEmailTemplate().getEmailTemplateKey());
            }

            // 批量发送邮件
            Result<Boolean> result = centerClient.batchAddEmailQueue(emailSenderQueueList);
            if (!result.isSuccess()) {
                log.error("添加邮件队列失败，业务ID: {}, 错误码: {}, 错误信息: {}",
                    businessId, result.getCode(), result.getMessage());
                throw new GetServiceException(LocaleMessageUtils.getMessage(result.getMessage()));
            }

            log.info("成功添加邮件队列，业务ID: {}, 邮件数量: {}", businessId, emailSenderQueueList.size());

        } catch (Exception e) {
            log.error("发送邮件异常，业务ID: {}", businessId, e);
            throw new GetServiceException(LocaleMessageUtils.getMessage("mail_send_failed"));
        }
    }

    /**
     * 构建EmailSenderQueue对象
     *
     * @param context 邮件发送上下文
     * @return EmailSenderQueue对象
     */
    private EmailSenderQueue buildEmailSenderQueue(EmailSendContext context) {
        EmailSenderQueue emailSenderQueue = new EmailSenderQueue();

        // 设置业务关联信息
        emailSenderQueue.setFkDbName(context.getProjectKey().key);
        emailSenderQueue.setFkTableId(context.getTableId());
        emailSenderQueue.setFkTableName(context.getTableName().key);

        // 设置邮件内容
        emailSenderQueue.setEmailTo(context.getRecipient());
        emailSenderQueue.setEmailTitle(context.getTitle());
        emailSenderQueue.setFkEmailTypeKey(context.getEmailTemplate().getEmailTemplateKey());

        // 设置执行时间
        emailSenderQueue.setOperationTime(
            context.getOperationTime() != null ? context.getOperationTime() : new Date()
        );

        // 序列化邮件参数
        if (CollectionUtil.isNotEmpty(context.getParameters())) {
            emailSenderQueue.setEmailParameter(JSONObject.toJSONString(context.getParameters()));
            log.debug("邮件参数: {}", JSONObject.toJSONString(context.getParameters()));
        }

        return emailSenderQueue;
    }

    /**
     * 校验邮箱格式
     *
     * @param email 邮箱地址
     * @return 是否为有效邮箱格式
     */
    private boolean isValidEmail(String email) {
        return email != null && email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    }

}