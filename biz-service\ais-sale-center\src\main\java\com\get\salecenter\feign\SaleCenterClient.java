package com.get.salecenter.feign;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.financecenter.dto.BatchDownloadAgentReconciliationDto;
import com.get.financecenter.dto.query.AgentSettlementQueryDto;
import com.get.financecenter.vo.AgentSettlementGrossAmountVo;
import com.get.institutioncenter.dto.NewEmailGetAgentDto;
import com.get.pmpcenter.dto.agent.AgentCommissionTypeAgentDto;
import com.get.rocketmqcenter.dto.InsurancePlanMessageDto;
import com.get.salecenter.dao.sale.StudentMapper;
import com.get.salecenter.dao.sale.StudentOfferItemMapper;
import com.get.salecenter.dao.sale.StudentOfferMapper;
import com.get.salecenter.dto.CommissionSummaryDto;
import com.get.salecenter.dto.EventBillAccountNoticeDto;
import com.get.salecenter.dto.EventCostDto;
import com.get.salecenter.dto.EventIncentiveCostUpdateDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.PayablePlanDto;
import com.get.salecenter.dto.StaffBdCodeDto;
import com.get.salecenter.dto.UpdatePayablePlanStatusSettlementDto;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.entity.AgentContract;
import com.get.salecenter.entity.AgentContractAccount;
import com.get.salecenter.entity.Client;
import com.get.salecenter.entity.Convention;
import com.get.salecenter.entity.NameLabel;
import com.get.salecenter.entity.PayablePlan;
import com.get.salecenter.entity.SaleMediaAndAttached;
import com.get.salecenter.entity.Student;
import com.get.salecenter.entity.StudentAccommodation;
import com.get.salecenter.entity.StudentInsurance;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.entity.StudentProjectRole;
import com.get.salecenter.service.AccountService;
import com.get.salecenter.service.DataImportService;
import com.get.salecenter.service.IAgentContractAccountService;
import com.get.salecenter.service.IAgentContractService;
import com.get.salecenter.service.IAgentService;
import com.get.salecenter.service.IAgentStaffService;
import com.get.salecenter.service.IBusinessChannelService;
import com.get.salecenter.service.IBusinessProviderService;
import com.get.salecenter.service.IClientService;
import com.get.salecenter.service.IConventionPersonService;
import com.get.salecenter.service.IConventionRegistrationService;
import com.get.salecenter.service.IConventionService;
import com.get.salecenter.service.IEventBillService;
import com.get.salecenter.service.IEventCostService;
import com.get.salecenter.service.IEventIncentiveCostService;
import com.get.salecenter.service.IEventIncentiveService;
import com.get.salecenter.service.IEventService;
import com.get.salecenter.service.IMediaAndAttachedService;
import com.get.salecenter.service.IPayablePlanService;
import com.get.salecenter.service.IReceivablePlanService;
import com.get.salecenter.service.IStaffBdCodeService;
import com.get.salecenter.service.IStaffCommissionInstitutionService;
import com.get.salecenter.service.IStudentAccommodationService;
import com.get.salecenter.service.IStudentInsuranceService;
import com.get.salecenter.service.IStudentOfferItemService;
import com.get.salecenter.service.IStudentOfferItemStepService;
import com.get.salecenter.service.IStudentOfferService;
import com.get.salecenter.service.IStudentProjectRoleService;
import com.get.salecenter.service.IStudentProjectRoleStaffService;
import com.get.salecenter.service.IStudentService;
import com.get.salecenter.service.InsuranceOrdereService;
import com.get.salecenter.service.KpiPlanService;
import com.get.salecenter.service.LogStudentOfferItemOfferFileIdentifyService;
import com.get.salecenter.service.NameLabelService;
import com.get.salecenter.service.StudentAppCountryService;
import com.get.salecenter.service.StudentServiceFeeService;
import com.get.salecenter.vo.AgenCommissionAndAgentSearchVo;
import com.get.salecenter.vo.AgentContractAccountVo;
import com.get.salecenter.vo.AgentContractVo;
import com.get.salecenter.vo.AgentSettlementPageVo;
import com.get.salecenter.vo.ClientVo;
import com.get.salecenter.vo.CommissionSummaryPageVo;
import com.get.salecenter.vo.ConventionRegistrationVo;
import com.get.salecenter.vo.EventBillVo;
import com.get.salecenter.vo.EventCostVo;
import com.get.salecenter.vo.EventIncentiveVo;
import com.get.salecenter.vo.EventVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.vo.PayablePlanVo;
import com.get.salecenter.vo.ReceivablePlanVo;
import com.get.salecenter.vo.SelItem;
import com.get.salecenter.vo.StaffBdCodeVo;
import com.get.salecenter.vo.StudentOfferItemSendEmailVo;
import com.get.salecenter.vo.StudentOfferItemVo;
import com.get.salecenter.vo.StudentOfferVo;
import com.get.salecenter.vo.StudentPlanVo;
import com.get.salecenter.vo.StudentServiceFeeSummaryVo;
import com.get.salecenter.vo.StudentServiceFeeVo;
import feign.Request;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@VerifyPermission(IsVerify = false)
public class SaleCenterClient implements ISaleCenterClient {
    private final IAgentService agentService;
    private final IStudentProjectRoleStaffService projectRoleStaffService;
    private final IPayablePlanService payablePlanService;
    private final IReceivablePlanService planService;
    private final StudentAppCountryService studentAppCountryService;
    private final IStudentService studentService;
    private final IStudentOfferService offerService;
    private final IAgentContractService agentContractService;
    private final IConventionPersonService conventionPersonService;
    private final IStudentAccommodationService iStudentAccommodationService;
    private final IStaffBdCodeService staffBdCodeService;
    private final IStudentOfferItemService offerItemService;
    private final IAgentStaffService agentStaffService;
    private final IAgentContractAccountService agentContractAccountService;
    private final IStudentInsuranceService iStudentInsuranceService;
    private final IEventCostService eventCostService;
    private final AccountService accountService;
    private final IBusinessChannelService businessChannelService;
    private final StudentServiceFeeService studentServiceFeeService;
    private final DataImportService dataImportService;
    private final IBusinessProviderService businessProviderService;
    private final IMediaAndAttachedService mediaAndAttachedService;
    private final IStudentOfferItemStepService studentOfferItemStepService;
    private final InsuranceOrdereService insuranceOrdereService;
    private final IEventBillService eventBillService;
    private final IStaffCommissionInstitutionService staffCommissionInstitutionService;
    private final StudentOfferItemMapper offerItemMapper;
    private final NameLabelService nameLabelService;
    private final StudentMapper studentMapper;
    private final StudentOfferMapper studentOfferMapper;
    private final LogStudentOfferItemOfferFileIdentifyService logStudentOfferItemOfferFileIdentifyService;
    private final KpiPlanService kpiPlanService;

    private final IConventionRegistrationService conventionRegistrationService;

    private final IConventionService conventionService;

    private final IStudentProjectRoleService studentProjectRoleService;

    private final  IClientService clientService;

    private final IEventIncentiveService eventIncentiveService;

    private final IEventService eventService;

    @Lazy
    private final IEventIncentiveCostService iEventIncentiveCostService;

    @Override
    public Result<List<StudentOfferItem>> getStudentOfferItemByParentId(Long id) {
        return Result.data(offerItemMapper.selectList(Wrappers.<StudentOfferItem>lambdaQuery().eq(StudentOfferItem::getFkParentStudentOfferItemId, id)));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<Boolean> analyzeOffer() {
        logStudentOfferItemOfferFileIdentifyService.analyzeOffer();
        return Result.data(true);
    }
    @VerifyLogin(IsVerify = false)
    @Override
    public Result<Map<Long, String>> getAgentNamesByIds(Set<Long> agentIds) {
        return Result.data(agentService.getAgentNamesByIds(agentIds));
    }

    @Override
    public Result<List<PayablePlanVo>> getPayablePlan(String typeKey, Long targetId) {
        return Result.data(payablePlanService.getPayablePlan(typeKey, targetId));
    }

    @Override
    public Result<List<Long>> getPayablePlanId(String typeKey, Long targetId) {
        return Result.data(payablePlanService.getPayablePlanId(typeKey, targetId));
    }

    @Override
    public Result<PayablePlanVo> getPayablePlanDetail(Long payPlanId) {
        return Result.data(payablePlanService.findPayablePlanById(payPlanId));
    }

    @Override
    public Result<BigDecimal> getPayablePlanAmountById(Long id) {
        return Result.data(payablePlanService.getPayablePlanAmountById(id));
    }

    @Override
    public Result<List<Long>> getReceivablePlanId(String typeKey, Long targetId) {
        return Result.data(planService.getReceivablePlanId(typeKey, targetId));
    }

    @Override
    public Result<BigDecimal> getReceivablePlanAmountById(Long id) {
        return Result.data(planService.getReceivablePlanAmountById(id));
    }

    @Override
    public Result<ReceivablePlanVo> getReceivablePlanById(Long id) {
        return Result.data(planService.findReceivablePlan(id));
    }

    @Override
    public Result<Map<Long, String>> getNamesByStudentAppCountryIds(Set<Long> studentAppCountryIds) {
        return Result.data(studentAppCountryService.getNamesByStudentAppCountryIds(studentAppCountryIds));
    }

    @Override
    public Result<String> getCurrencyNum(Long id) {
        return Result.data(planService.getCurrencyNum(id));
    }

    @Override
    public Result<List<Long>> getStudentIds(String name) {
        return Result.data(studentService.getStudentIds(name));
    }

    @Override
    public List<Long> getServiceFeeStudentIds(String targetName) {
        return studentServiceFeeService.getServiceFeeStudentIds(targetName);
    }

    @Override
    public Result<List<Long>> getChannelIds(String tableName, String channelName) {
        return Result.data(businessChannelService.getChannelIds(tableName, channelName));
    }

    @Override
    public Result<Map<Long, String>> getChannelNamesByIds(Set<Long> ids) {
        return Result.data(businessChannelService.getChannelNamesByIds(ids));
    }

    @Override
    public Result<String> getStudentNameById(Long id) {
        return Result.data(studentService.getStudentNameById(id));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<StudentOfferVo> getStudentOfferDetail(Long id) {
        return Result.data(offerService.getStudentOfferDetail(id));
    }

    @Override
    public Result<Map<String,Set<Long>>> getInChannelAndProviderIds(Set<Long> targetIds) {
        return Result.data(offerItemService.getInChannelAndProviderIds(targetIds));
    }

    @Override
    public Result<List<Long>> getStudentOfferItemByPayIds(Set<Long> ids) {
        return Result.data(offerItemService.getStudentOfferItemByPayIds(ids));
    }

    @Override
    public Result<List<MediaAndAttachedVo>> getItemMedias(Set<Long> ids) {
        return Result.data(offerItemService.getItemMedias(ids));
    }

    @Override
    public Result<List<MediaAndAttachedVo>> getAgentCommissionMedias(Set<Long> ids) {
        return Result.data(offerItemService.getAgentCommissionMedias(ids));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<StudentOfferItem> getStudentOfferItemById(Long id) {
        return Result.data(offerItemService.getStudentOfferItemById(id));
    }

    @Override
    public Result<List<SelItem>> getStudentOfferItemByIds(Set<Long> targetIds) {
        return Result.data(offerItemService.getStudentOfferItemByIds(targetIds));
    }

    @Override
    public Result<Map<Long, String>> getStudentNameByIds(Set<Long> ids) {
        return Result.data(studentService.getStudentNameByIds(ids));
    }

    @Override
    public List<BaseSelectEntity> getInvoiceStudentSelection(Long companyId) {
        return studentServiceFeeService.getInvoiceStudentSelection(companyId);
    }



    @Override
    public Result<List<BaseSelectEntity>> getTargetName(Long companyId, String tableName) {
        return Result.data(businessChannelService.getTargetName(tableName, companyId));
    }

    @Override
    public Result<Boolean> updateStudentOffer(StudentOfferVo studentOffer) {
        return Result.data(offerService.updateStudentOffer(studentOffer));
    }

    @Override
    public Result<Boolean> nettyPush(Long itemId) {
//        return Result.data(nettyPushHelper.nettyPush(itemId));111111
        return Result.data(false);
    }

    @Override
    public Result<Boolean> updateChangeStatus(AgentContract agentContract) {
        return Result.data(agentContractService.updateChangeStatus(agentContract));
    }

    @Override
    public Result<Boolean> startContractFlow(String businessKey, String procdefKey, String companyId) {
        if (businessKey == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        return Result.data(agentContractService.startContractFlow(businessKey, procdefKey, companyId));
    }

    @Override
    public Result<Boolean> changeStatus(Integer status, String tableName, Long businessKey) {
        return Result.data(agentContractService.changeStatus(status, tableName, businessKey));
    }

    @Override
    public Result<AgentContractVo> getAgentContractById(Long id) {
        return Result.data(agentContractService.getAgentContractById(id));
    }

    @Override
    public Result<Map<String, String>> getNamesByMobiles(Set<String> mobiles) {
        return Result.data(conventionPersonService.getNamesByMobiles(mobiles));
    }

    @Override
    public Result<Set<Long>> getAreaRegionIdsByCompanyId() {
        return Result.data(staffBdCodeService.getAreaRegionIdsByCompanyId());
    }

    @Override
    public Result<List<BaseSelectEntity>> getOfferItemSelectByAgentId(String tableName, Long agentId) {
        return Result.data(offerItemService.getOfferItemSelectByAgentId(tableName, agentId));
    }

    @Override
    public Result<List<BaseSelectEntity>> getOfferItemSelectByProviderId(String tableName, Long providerId) {
        return Result.data(offerItemService.getOfferItemSelectByProviderId(tableName, providerId));
    }

    @Override
    public Result<List<Long>> getAgentIds(String name) {
        return Result.data(agentService.getAgentListIds(name));
    }

    @Override
    public Result<List<BaseSelectEntity>> getAgentSelect(Long companyId) {
        return Result.data(agentService.getAgentList(companyId,false));
    }

    @Override
    public Result<String> getAgentNameById(Long agentId) {
        return Result.data(agentService.getNameById(agentId));
    }

    @Override
    public Result<Long> getStaffByAgentId(Long fkAgentId) {
        return Result.data(agentContractService.getStaffByAgentId(fkAgentId));
    }

    @Override
    public AgentSettlementPageVo agentSettlementList(SearchBean<AgentSettlementQueryDto> page) {
        return agentService.agentSettlementList(page.getData(), page);
    }

    @Override
    public List<Long> getAgentSettlementIds(AgentSettlementQueryDto agentSettlementVo, String local, Long staffId, boolean payInAdvanceFlag, boolean exportFlag) {
        return agentService.getAgentSettlementIds(agentSettlementVo,local,staffId, payInAdvanceFlag, exportFlag);
    }

    @Override
    public boolean checkAgentData(List<BatchDownloadAgentReconciliationDto> batchDownloadAgentReconciliationVoList) {
        return agentService.checkAgentData(batchDownloadAgentReconciliationVoList);
    }

    @Override
    public Integer getAgentCompanyIdById(Long id) {
        return agentService.getAgentCompanyIdById(id);
    }

    @Override
    public Map<Long, Long> getAgentCompanyIdByIds(Set<Long> ids) {
        return agentService.getAgentCompanyIdByIds(ids);
    }

    @Override
    public Result<List<Long>> getSubordinateNotPortAgentIdsById(Long agentId) {
        return Result.data(agentService.getSubordinateNotPortAgentIdsById(agentId));
    }

    @Override
    public Result<StudentPlanVo> financePlanDetails(Long planId) {
        return Result.data(payablePlanService.financePlanDetails(planId));
    }

//    @Override
//    public Result<Map<Long, List<PayablePlanSettlementAgentAccountVo>>> getSettlementMarkByPayablePlanIds(List<Long> payablePlanIds) {
//        return Result.data(agentContractAccountService.getSettlementMarkByPayablePlanIds(payablePlanIds));
//    }


//    @Override
//    public Result<Boolean> financeConfirmSettlement(List<Long> payablePlanIdList) {
//        return Result.data(payablePlanService.financeConfirmSettlement(payablePlanIdList));
//    }

    @Override
    public Result<Boolean> batchUpdatePayablePlan(List<PayablePlanDto> payablePlanDtoList) {
        return Result.data(payablePlanService.batchUpdatePayablePlan(payablePlanDtoList));
    }

    @Override
    public Result<CommissionSummaryPageVo> commissionSummary(SearchBean<CommissionSummaryDto> page) {
        return Result.data(agentService.commissionSummary(page.getData(), page));
    }


    @Override
    public Result<List<PayablePlanVo>> getAgentPayablePlanByNumSettlementBatch(String numSettlementBatch) {
        return Result.data(agentService.getAgentPayablePlanByNumSettlementBatch(numSettlementBatch));
    }

    @Override
    public Result<Map<Long, AgentContractAccountVo>> getAgentContractAccountByAccountIds(List<Long> accountIds) {
        return Result.data(agentContractAccountService.getAgentContractAccountByAccountIds(accountIds));
    }

    @Override
    public Result<Map<Long, List<AgentContractAccountVo>>> getAgentContractAccountByAgentIds(List<Long> agentIds) {
        return Result.data(agentContractAccountService.getAgentContractAccountByAgentIds(agentIds));
    }

    @Override
    public Result<String> getAgentContractBankAccountNameById(Long id) {
        return Result.data(agentContractAccountService.getAgentContractBankAccountNameById(id));
    }

    @Override
    public Result<Map<Long, List<EventCostVo>>> getEventCostDtoByReceiptFormIds(Set<Long> receiptFormIds) {
        return Result.data(eventCostService.getEventCostDtoByReceiptFormIds(receiptFormIds));
    }

    @Override
    public Result<List<EventCostVo>> getEventCostDtoByReceiptFormId(Long receiptFormId) {
        return Result.data(eventCostService.getEventCostDtoByReceiptFormId(receiptFormId));
    }

    @Override
    public Result<List<ReceivablePlanVo>> getReceivablePlanDtosByIds(List<Long> fkReceivablePlanIds) {
        return Result.data(planService.getReceivablePlanDtosByIds(fkReceivablePlanIds));
    }

    @Override
    public Result<List<ReceivablePlanVo>> getReceivablePlansDetail(Set<Long> ids) {
        return Result.data(planService.getReceivablePlansDetail(ids));
    }

    @Override
    public Result<Boolean> deleteValidateCourse(Long courseId) {
        return Result.data(offerItemService.deleteValidateCourse(courseId));
    }

    @Override
    public Result<List<Long>> getAgentIdListByLoginStaffPower() {
        return Result.data(agentService.getAgentIdList());
    }


    @Override
    public Result<List<PayablePlanVo>> getPayablePlanByIds(Set<Long> ids) {
        return Result.data(payablePlanService.getPayablePlanByIds(ids));
    }

    @Override
    public Result<List<PayablePlanVo>> getPayablePlanDetailsByIds(Set<Long> ids) {
        return Result.data(payablePlanService.getPayablePlanDetailsByIds(ids));
    }

    @Override
    public Result<Map<Long, PayablePlanVo>> findOfferItemByPayIds(Set<Long> ids) {
        return Result.data(offerItemService.findOfferItemByPayIds(ids));
    }

    @Override
    public Result<Map<Long, Object>> getDeferEntranceTimeByIds(Set<Long> ids) {
        return Result.data(offerItemService.getDeferEntranceTimeByIds(ids));
    }

    @Override
    public Result<Map<Long, ReceivablePlanVo>> findOfferItemByReceivableIds(Set<Long> ids) {
        return Result.data(offerItemService.findOfferItemByReceivableIds(ids));
    }

    @Override
    public Result<List<ReceivablePlanVo>> findReceivablePlanByIds(Set<Long> ids) {
        return Result.data(planService.getReceivablePlanByIds(ids));
    }

    @Override
    public Result<List<BaseSelectEntity>> getPlanIdsByTableNameAndChannelId(String tableName, Long channelId,Long receiptFormId,String fkTypeKey,Integer pageNumber,Integer pageSize) {
        return Result.data(businessChannelService.getPlanIdsByTableNameAndChannelId(tableName, channelId,receiptFormId,fkTypeKey,pageNumber,pageSize));
    }


    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Boolean> agentIsKeyExpired() {
        return Result.data(agentService.agentIsKeyExpired());
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<AgentContractAccount> getAccount(Long agentId, String backNum) {
        return accountService.getAccount(agentId,backNum);
    }

    @Override
    public Result<List<StudentOfferItemVo>> getStudentOfferItemByStudentOfferItemStepId(List<Long> fkTableIds) {
        return  Result.data(offerItemService.getStudentOfferItemByStudentOfferItemStepId(fkTableIds));
    }

    @Override
    public Result<PayablePlan> getPayablePlanByReceivablePlanId(Long fkReceivablePlanId) {
        return Result.data(payablePlanService.getPayablePlanByReceivablePlanId(fkReceivablePlanId));
    }

    @Override
    public Result<List<PayablePlan>> getPayablePlanByReceivablePlanIds(List<Long> fkReceivablePlanIdList) {
        return Result.data(payablePlanService.getPayablePlanByReceivablePlanIds(fkReceivablePlanIdList));
    }

    @Override
    public Result<List<BaseSelectEntity>> getOfferItemSelectByChannelId(String tableName, Long channelId, Long receiptFormId,Integer pageNumber,Integer pageSize) {
        return Result.data(offerItemService.getOfferItemSelectByChannelId(tableName, channelId, receiptFormId,pageNumber,pageSize));
    }

    @Override
    public Result<List<ReceivablePlanVo>> getReceivablePlansDetailNew(Set<Long> ids) {
        return Result.data(planService.getReceivablePlansDetailNew(ids));
    }

    @VerifyLogin(IsVerify=false)
    @Override
    public void dataImport() {
        dataImportService.dataImport();
    }

    @Override
    public Result<List<BaseSelectEntity>> getOfferItemSelectByProviderIdNew(String tableName, Long providerId, Long receiptFormId,Integer pageNumber,Integer pageSize) {
        return Result.data(offerItemService.getOfferItemSelectByProviderIdNew(tableName, providerId,receiptFormId,pageNumber,pageSize));
    }

    @Override
    public Result<Page> getReceiptFormReceivablePlanPaginationInfo(String tableName, Long channelId, Long receiptFormId, String fkTypeKey,Page page) {
        return Result.data(planService.getReceiptFormReceivablePlanPaginationInfo(tableName,channelId,receiptFormId,fkTypeKey,page));
    }

    @Override
    public Result<List<ReceivablePlanVo>> packReceivablePlanResult(List<ReceivablePlanVo> receivablePlanVos) {
        return Result.data(planService.packResult(receivablePlanVos));
    }

    @Override
    public Result<PayablePlan> getPayableInfoById(Long payablePlanId) {
        return payablePlanService.doGetPayableInfoById(payablePlanId);
    }

    @Override
    public Result<Long> getAgentIdByPayablePlanId(Long payablePlanId) {
        return Result.data(agentService.doGetAgentIdByPayablePlanId(payablePlanId));
    }

    @Override
    public Result<List<BaseSelectEntity>> getPlanIdsAndCompanyName(String typeKey, Long targetId,Long receiptFormId) {
        return Result.data(planService.getPlanIdsAndCompanyName(typeKey,targetId,receiptFormId));
    }

    @Override
    public Result<Long> getAccommodationAgentId(Long targetId) {
        return Result.data(iStudentAccommodationService.getAccommodationAgentId(targetId));
    }

    @Override
    public Result<Long> getInsuranceAgentId(Long targetId) {
        return Result.data(iStudentInsuranceService.getInsuranceAgentId(targetId));
    }

    @Override
    public Result<Long> getStudentAccommodationId(Long targetId) {
        return Result.data(iStudentAccommodationService.getStudentAccommodationId(targetId));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<StudentAccommodation> getStudentAccommodationById(Long id) {
        return Result.data(iStudentAccommodationService.getStudentAccommodationById(id));
    }

    @Override
    public Result<Long> getStudentInsuranceId(Long targetId) {
        return Result.data(iStudentInsuranceService.getStudentInsuranceId(targetId));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<StudentInsurance> getStudentInsuranceById(Long id) {
        return  Result.data(iStudentInsuranceService.getStudentInsuranceById(id));
    }


    /**
     * 获取业务id
     * @param params
     * @return
     */
    @Override
    public Result<Set<Long>> getBusinessId(Map<String, Set<Long>> params) {
        return Result.data(businessChannelService.getBusinessId(params));
    }

    @Override
    public Result<Set<Long>> getBusinessProviderIdByAccIds(Set<Long> ids) {
        return Result.data(businessChannelService.getBusinessProviderId(ids));
    }

    /**
     * 获取代理map
     * @param agentIds
     * @return
     */
    @Override
    public Result<Map<Long, Agent>> getAgentsByIds(Set<Long> agentIds) {
        return Result.data(agentService.getAgentsByIds(agentIds));
    }


    /**
     * 获取留学住宿提供商id
     * @param ids
     * @return
     */
    @Override
    public Result<Map<Long, String>> getAStudyAccommodationProvider(Set<Long> ids) {
        return Result.data(businessProviderService.getNamesByIds(ids));
    }

    @Override
    public Result<List<Long>> getBusinessProviderId(String targetName) {
        return Result.data(businessProviderService.getBusinessProviderId(targetName));
    }

    @Override
    public String getBusinessProviderNameById(Long id) {
        return businessProviderService.getBusinessProviderNameById(id);
    }


    @Override
    public Result<List<BaseSelectEntity>> getBusinessObjectSelection(Long companyId) {
        return Result.data(businessProviderService.getBusinessObjectSelection(companyId));
    }

    @Override
    public Result<List<BaseSelectEntity>> getPlanAndTargetName(Long targetId, Long receiptFormId) {
        return Result.data(businessProviderService.getPlanAndTargetName(targetId,receiptFormId));
    }

    @Override
    public Result<List<String>> checkAgentContractByAgentIds(Set<Long> agentIdSet) {
        return Result.data(agentContractService.checkAgentContractByAgentIds(agentIdSet));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<List<AgentContractVo>> getAgentContractsByEndTime(String date) {
        return Result.data(agentContractService.getAgentContractsByEndTime(date));
    }

    /**
     * IAE文件更新调度任务不需要权限
     * @return
     */
    @Override
    @VerifyLogin(IsVerify = false)
    public List<SaleMediaAndAttached> getMediaAndAttachedByIaeCrm() {
        return mediaAndAttachedService.getMediaAndAttachedByIaeCrm();
    }

    /**
     * IAE文件更新调度任务不需要权限
     * @return
     */
    @Override
    @VerifyLogin(IsVerify = false)
    public Boolean updateMediaAndAttachedById(SaleMediaAndAttached mediaAndAttached) {
        return mediaAndAttachedService.updateMediaAndAttachedById(mediaAndAttached);
    }

    @Override
    public Result<Boolean> autoRelationReceipten(Set<Long> receivablePlanIds) {
        return Result.data(planService.autoRelationReceipten(receivablePlanIds));
    }

    @Override
    public Map<Long, Object> getAgentContractPersonMobileByAgentId(Set<Long> agentIds) {
        return agentContractAccountService.getAgentContractPersonMobileByAgentId(agentIds);
    }

    @Override
    public Result<List<BaseSelectEntity>> getItemStepSelect() {
        return Result.data(studentOfferItemStepService.getItemStepSelect());
    }

    @Override
    public Result<Set<String>> getItemStepSelectByStepKey(List<String> stepKeys) {
        return Result.data(studentOfferItemStepService.getItemStepSelectByStepKey(stepKeys));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<StudentOfferVo> getStudentOfferForWorkFlow(Long id) {
        return Result.data(offerService.getStudentOfferForWorkFlow(id));
    }

    @Override
    public Result<ResponseBo> sendEventBillAccountEmail(EventBillAccountNoticeDto eventBillAccountNoticeDto) {
        ResponseBo responseBo = eventBillService.sendAccountEmail(eventBillAccountNoticeDto);
        if (!responseBo.getSuccess()){
            return Result.fail(responseBo.getMessage());
        }
        return Result.data(responseBo);
    }

    @Override
    public Result<List<BaseSelectEntity>> getStudentServiceFeeReceivablePlan(Long targetId, Long receiptFormId, Integer pageNumber, Integer pageSize) {
        return Result.data(studentServiceFeeService.getStudentServiceFeeReceivablePlan(targetId,receiptFormId,pageNumber,pageSize));
    }

    @Override
    public List<Long> getServiceFeeStudentIdsByIds(@RequestBody List<Long> ids) {
        return studentServiceFeeService.getServiceFeeStudentIdsByIds(ids);
    }

    @Override
    public Result<Long> getServiceFeeStudentIdsById(Long targetId) {
        return Result.data(studentServiceFeeService.getServiceFeeStudentIdsById(targetId));
    }

    @Override
    public Result<StudentServiceFeeVo> getServiceFeeById(Long targetId) {
        return Result.data(studentServiceFeeService.findServiceFeeById(targetId));
    }

    @Override
    public Result<Map<Long, String>> getBusinessProviderNameByIds(Set<Long> businessProviderIds) {
        return Result.data(businessProviderService.getNamesByIds(businessProviderIds));
    }

    @Override
    public List<BaseSelectEntity> getAgentByTargetName(String targetName) {
        return agentService.getAgentByTargetName(targetName);
    }

    @Override
    public List<BaseSelectEntity> getBusinessProviderByTargetName(String targetName) {
        return businessProviderService.getBusinessProviderByTargetName(targetName);
    }

    @Override
    public Result<Map<Long, Boolean>> getCommissionActiveStatusByInstitutionIds(Set<Long> institutionIds) {
        return Result.data(staffCommissionInstitutionService.getCommissionActiveStatusByInstitutionIds(institutionIds));
    }

    @Override
    public List<ReceivablePlanVo> getReceivableAmountInfo(Set<Long> receivablePlanIds) {
        return planService.getReceivableAmountInfo(receivablePlanIds);
    }

    /**
     * 获取代理合同
     * @param agentIds
     * @return
     */
    @Override
    public Map<Long, List<AgentContract>> getAgentContractByAgentIds(List<Long> agentIds) {
        return agentContractService.getAgentContractByAgentIds(agentIds);
    }

    @Override
    public Result<String> getContractBankAccountNameById(Long fkBankAccountId, String fkTypeKey) {
        return Result.data(agentContractAccountService.getContractBankAccountNameById(fkBankAccountId,fkTypeKey));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public List<MediaAndAttachedVo> getMediaAndAttachedByAgentIds(List<String> fkAgentIds_) {
        return mediaAndAttachedService.getMediaAndAttachedByAgentIds(fkAgentIds_);
    }


    @VerifyLogin(IsVerify = false)
    @Override
    public Result<Map<Long, List<Long>>> getBdIdByAgentIds(Set<Long> agentIds) {
        return Result.data(agentStaffService.getBdIdByAgentIds(agentIds));
    }

    @Override
    public Result<Boolean> getProviderInstitutionItem(Long fkInstitutionId,Long fkInstitutionProviderId) {
        return Result.data(offerItemMapper.getProviderInstitutionItem(fkInstitutionId,fkInstitutionProviderId));
    }

    @Override
    public List<ReceivablePlanVo> getReceivablePlansBySort(Set<Long> planIds, Boolean invoiceAmountSort, Boolean receiptAmountSort, String studentName, Long receiptFormId) {
        return planService.getReceivablePlansBySort(planIds,invoiceAmountSort,receiptAmountSort,studentName,receiptFormId);
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public List<StudentOfferItemVo> getStudentByOfferItemIds(List<Long> itemIds) {
        return offerItemService.getStudentByOfferItemIds(itemIds);
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Map<String, String> getContactPersonEmailMap(Long itemId) {
        return offerItemService.getContactPersonEmailMap(itemId);
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public StudentOfferItemSendEmailVo getContactPersonEmailStaff(Long itemId) {
        return offerItemService.getContactPersonEmailStaff(itemId);
    }

    @Override
    @VerifyLogin(IsVerify=false)
    public void dataImportItemEmail() {
        offerItemService.dataImportItemEmail();
    }

    @Override
    public Result<Map<Long,NameLabel>> getNameLabelListByFkTableName(String fkTableName){
        return Result.data(nameLabelService.getNameLabelListByFkTableName(fkTableName));
    }

    @Override
    public Result<List<AgentSettlementGrossAmountVo>> getAgentListToExport(AgentSettlementQueryDto agentSettlementVo) {
        return Result.data(agentService.getAgentListToExport(agentSettlementVo));
    }

    @Override
    public Result<Set<String>> getNewAgentEmails(NewEmailGetAgentDto newEmailGetAgentVo, Request.Options options) {
        return Result.data(agentService.getNewAgentEmails(newEmailGetAgentVo));
    }

    @Override
    public Result<Set<String>> getNewAgentAllEmails(Long newsId, Integer type, Request.Options options) {
        return Result.data(agentService.getNewAgentAllEmails(newsId, type));
    }

    @Override
    public Result<List<Long>> getServiceFeeProviderIdsByFeeIds(List<Long> feeIds) {
        return Result.data(studentServiceFeeService.getServiceFeeProviderIdsByFeeIds(feeIds));
    }

    @Override
    public Result<List<BaseSelectEntity>> getReceivablePlanSelectByProvider(Long providerId, Long receiptFormId) {
        return Result.data(planService.getReceivablePlanSelectByProvider(providerId, receiptFormId));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public void kpiPlanStatistics() {
        kpiPlanService.kpiPlanStatistics();
    }

    @Override
    public Result<List<BaseSelectEntity>> getBusinessProviderSelectByTypeKey(Long companyId, String typeKey) {
        return Result.data(businessProviderService.getBusinessProviderSelectByTypeKey(companyId, typeKey));
    }

//    @Override
//    public Result<List<AgentVo>> getAgentsAll(AgenCommissionAndAgentSearchDto agenCommissionAndAgentSearchDto) {
//        return Result.data(agentService.getAgentsAll(agenCommissionAndAgentSearchDto));
//    }

    @Override
    public Result<AgenCommissionAndAgentSearchVo> getAgentCommissionTypeAndAgentIsBind(SearchBean<AgentCommissionTypeAgentDto> page) {
        return Result.data(agentService.getAgentCommissionTypeAndAgentIsBind(page.getData(), page));
    }

    @Override
    public Result<Boolean> updatePayablePlanStatusSettlement(UpdatePayablePlanStatusSettlementDto updatePayablePlanStatusSettlementDto) {
        return Result.data(payablePlanService.updatePayablePlanStatusSettlement(updatePayablePlanStatusSettlementDto));
    }

    @Override
    public Result<Boolean> saveBatchMediaAndAttached(List<MediaAndAttachedDto> mediaAndAttachedDtos) {
        return Result.data(mediaAndAttachedService.saveBatchMediaAndAttached(mediaAndAttachedDtos));
    }

    @Override
    public Result<Map<Long, List<MediaAndAttachedVo>>> getMediaAndAttachedDtoByFkTableIds(String key, Set<Long> fkTableId) {
        return Result.data(mediaAndAttachedService.getMediaAndAttachedDtoByFkTableIds(key, fkTableId));
    }


    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Agent> getAgentById(Long fkAgentId) {
        return Result.data(agentService.getAgentById(fkAgentId));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Student> getStudentById(Long studentId) {
        return Result.data(studentMapper.selectById(studentId));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Set<Long>> getRoleAndStaffByTableIdAndRoles(Long fkStudentOfferId, String key, List<String> roleList) {
        return Result.data(projectRoleStaffService.getRoleAndStaffByTableIdAndRoles(fkStudentOfferId, key, roleList));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<EventBillVo> getEventBillById(Long eventBillId) {
        return Result.data(eventBillService.findEventBillById(eventBillId));
    }

    /**
     * 澳小保创建应收应付
     * @param insurancePlanMessageDto
     * @return
     */
    @VerifyLogin(IsVerify = false)
    @Override
    public Result createInsurancePlan(InsurancePlanMessageDto insurancePlanMessageDto) {
        return Result.data(payablePlanService.createInsurancePlan(insurancePlanMessageDto));
    }

    @Override
    public Result<List<BaseSelectEntity>> getInsuranceOrderSelect(Long companyId) {
        return Result.data(insuranceOrdereService.getInsuranceOrderSelect(companyId));
    }

    /**
     * 获取留学保险提供商可绑定的应收计划
     * @param targetId
     * @param receiptFormId
     * @return
     */
    @Override
    public Result<List<BaseSelectEntity>> getPlanIdsByBusinessProviderId(Long targetId, Long receiptFormId) {
        return Result.data(businessProviderService.getPlanIdsByBusinessProviderId(targetId, receiptFormId));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<List<ConventionRegistrationVo>> getConventionRegistrationByReceiptCode(List<String> receiptCodeList) {
        return Result.data(conventionRegistrationService.getConventionRegistrationsByReceiptCode(receiptCodeList));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Convention> getConventionRegistrationById(Long id) {
        return Result.data(conventionService.getConventionById(id));
    }

    @Override
    public Result<Map<Long, String>> getInsuranceProviderNameByIds(Set<Long> ids) {
        return Result.data(businessProviderService.getInsuranceProviderNameByIds(ids));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public List<StudentProjectRole> getStudentProjectRoleListByRoleIds(Set<Long> roleIds) {
        return studentProjectRoleService.getStudentProjectRoleListByRoleIds(roleIds);
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<StudentServiceFeeSummaryVo> getServiceFeeInfoById(Long id) {
        return Result.data(studentServiceFeeService.getServiceFeeInfoById(id));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<StudentServiceFeeVo> findServiceFeeById(Long fkTableId) {
        return Result.data(studentServiceFeeService.findServiceFeeById(fkTableId));
    }

    @Override
    public Result<List<StaffBdCodeVo>> getBdInformation(SearchBean<StaffBdCodeDto> page) {
        return Result.data(staffBdCodeService.getStaffBdCodes(page.getData(), null));
    }

    @Override
    public Result<List<MediaAndAttachedVo>> addMediaAndAttachedList(List<MediaAndAttachedDto> mediaAttachedVos) {
        return Result.data(mediaAndAttachedService.addMediaAndAttachedList(mediaAttachedVos));
    }

    @Override
    public Result<Boolean> copyPartnerStudentAttached(Long studentId, Long partnerStudentId) {
        return Result.data(mediaAndAttachedService.copyPartnerStudentAttached(studentId, partnerStudentId));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Client> findClientById(Long fkTableId) {
        return Result.data(clientService.getClientById(fkTableId));
    }

    @Override
    public Result<EventIncentiveVo> getEventIncentiveById(Long id) {
        return Result.data(eventIncentiveService.findEventIncentiveById(id));
    }

    @Override
    public Result<EventVo> getEventById(Long id) {
        return Result.data(eventService.findEventById(id));
    }

    @Override
    public Result<BigDecimal> getEventIncentiveCostSubtotal(Long  fkEventIncentiveId) {
        EventIncentiveCostUpdateDto eventIncentiveCostUpdateDto = new EventIncentiveCostUpdateDto();
        eventIncentiveCostUpdateDto.setFkEventIncentiveId(fkEventIncentiveId);
        return Result.data(iEventIncentiveCostService.getEventIncentiveCostSubtotal(eventIncentiveCostUpdateDto));
    }

    @Override
    public Result<BigDecimal> getEventCostSubtotal(Long  fkEventId) {
        EventCostDto eventCostDto = new EventCostDto();
        eventCostDto.setFkEventId(fkEventId);
        return Result.data(eventCostService.getEventCostSubtotal(eventCostDto));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Boolean> sendAgentContractUnsignedReminders() {
        try {
            agentContractService.sendAgentContractUnsignedReminders();
            return Result.data(true);
        } catch (Exception e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("news_email_send_fail"));
        }
    }


}
