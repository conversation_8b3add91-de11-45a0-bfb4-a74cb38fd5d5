package com.get.common.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * Java8通用Builder ,用于快速创建对象及赋值，示例如下：
 * GirlFriend myGirlFriend = Builder.of(GirlFriend::new)
 * .with(GirlFriend::setName, "小美")
 * .with(GirlFriend::setAge, 18)
 * .with(GirlFriend::setVitalStatistics, 33, 23, 33)
 * .with(GirlFriend::setBirthday, "2001-10-26")
 * .build();
 *
 * @param <T>
 */
public class GetBuilder<T> {
    private final Supplier<T> instantiator;
    private List<Consumer<T>> setterList = new ArrayList<>();

    public GetBuilder(Supplier<T> instantiator) {
        this.instantiator = instantiator;
    }

    public static <T> GetBuilder<T> of(Supplier<T> instantiator) {
        return new GetBuilder<>(instantiator);
    }

    public <P> GetBuilder<T> with(IOneParamSetter<T, P> setter, P p) {
        Consumer<T> c = instance -> setter.accept(instance, p);
        setterList.add(c);
        return this;
    }

    public <P0, P1> GetBuilder<T> with(ITwoParamSetter<T, P0, P1> setter, P0 p0, P1 p1) {
        Consumer<T> c = instance -> setter.accept(instance, p0, p1);
        setterList.add(c);
        return this;
    }

    public <P0, P1, P2> GetBuilder<T> with(IThreeParamSetter<T, P0, P1, P2> setter, P0 p0, P1 p1, P2 p2) {
        Consumer<T> c = instance -> setter.accept(instance, p0, p1, p2);
        setterList.add(c);
        return this;
    }

    public T build() {
        T value = instantiator.get();
        setterList.forEach(setter -> setter.accept(value));
        setterList.clear();
        return value;
    }

    /**
     * 1 参数 Consumer
     */
    @FunctionalInterface
    public interface IOneParamSetter<T, P1> {
        void accept(T t, P1 p1);
    }

    /**
     * 2 参数 Consumer
     */
    @FunctionalInterface
    public interface ITwoParamSetter<T, P1, P2> {
        void accept(T t, P1 p1, P2 p2);
    }

    /**
     * 3 参数 Consumer
     */
    @FunctionalInterface
    public interface IThreeParamSetter<T, P1, P2, P3> {
        void accept(T t, P1 p1, P2 p2, P3 p3);
    }
}
