package com.get.officecenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.officecenter.entity.Comment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author:cream
 * @Date: 2023/5/30  12:30
 */
@Mapper
public interface CommentMapper extends GetMapper<Comment> {

    @DS("officedb-doris")
    List<Comment> getCommentList(String fkTableName, List<Long> fkTableIds);

    // 添加批量插入方法
    int insertBatch(@Param("list") List<Comment> comments);


}
