package com.get.remindercenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.remindercenter.vo.RemindTaskListVo;
import com.get.remindercenter.vo.StaffContractRemindVo;
import com.get.remindercenter.entity.RemindTask;
import com.get.remindercenter.dto.RemindTaskListDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface RemindTaskMapper extends BaseMapper<RemindTask> {

    int updateByPrimaryKeySelective(RemindTask record);

    int updateByPrimaryKey(RemindTask record);


    /**
     * @Description: 获取任务数据
     * @Author: Jerry
     * @Date:10:29 2021/11/16
     */
    List<RemindTask> getRemindTaskList(@Param("remindTaskListDto") RemindTaskListDto remindTaskListDto, @Param("fkStaffId") Long fkStaffId);


    /**
     * @Description: 获取任务总数
     * @Author: Jerry
     * @Date:10:29 2021/11/23
     */
    List<RemindTask> getTaskCount(@Param("fkStaffId") Long fkStaffId, @Param("nowDate") Date nowDate,@Param("isDate") boolean isDate,
                                  @Param("keys") List<String> keys);

    StaffContractRemindVo getStaffContractRemindDto(@Param("fkTableId")Long fkTableId);

    List<RemindTaskListVo> getRemindTaskDatas(IPage<RemindTaskListVo> iPage, @Param("remindTaskListDto") RemindTaskListDto remindTaskListDto);
}