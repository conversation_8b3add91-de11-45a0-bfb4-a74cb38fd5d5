package com.get.schoolGateCenter.dto;

import com.get.schoolGateCenter.entity.StaffResource;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/21
 * @TIME: 10:44
 * @Description:
 **/
@ApiModel("权限允许禁止返回类")
@Data
public class StaffResourceDto extends StaffResource {
    /**
     * 允许权限
     */
    @ApiModelProperty(value = "允许权限")
    private List<String> allowResources;
    /**
     * 禁止权限
     */
    @ApiModelProperty(value = "禁止权限")
    private List<String> notAllowResources;


}
