package com.get.platformconfigcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.platformconfigcenter.service.MediaAndAttachedMsoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * 附件管理控制层
 *
 * <AUTHOR>
 * @date 2021/5/12 12:08
 */
@Api(tags = "附件管理")
@RestController
@RequestMapping("platform/media")
public class MediaAndAttachedController {
    @Resource
    private MediaAndAttachedMsoService mediaAndAttachedMsoService;


    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "ISSUE上传文件")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.ADD, description = "业务平台配置中心/ISSUE上传文件")
    @PostMapping("/issue/upload")
    public ResponseBo issueUpload(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files) {
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", mediaAndAttachedMsoService.upload(files, LoggerModulesConsts.PLATFORMCENTER_ISSUE));
        return responseBo;
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "MSO上传文件")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.ADD, description = "业务平台配置中心/MSO上传文件")
    @PostMapping("/mso/upload")
    public ResponseBo msoUpload(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files) {
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", mediaAndAttachedMsoService.upload(files, LoggerModulesConsts.PLATFORMCENTER_MSO));
        return responseBo;
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "ISSUE上传附件")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.ADD, description = "业务平台配置中心/ISSUE上传附件")
    @PostMapping("/issue/uploadAppendix")
    public ResponseBo issueUploadAppendix(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files) {
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", mediaAndAttachedMsoService.uploadAppendix(files, LoggerModulesConsts.PLATFORMCENTER_ISSUE));
        return responseBo;
    }


    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "MSO上传附件")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.ADD, description = "业务平台配置中心/MSO上传附件")
    @PostMapping("/mso/uploadAppendix")
    public ResponseBo msoUploadAppendix(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files) {
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", mediaAndAttachedMsoService.uploadAppendix(files, LoggerModulesConsts.PLATFORMCENTER_MSO));
        return responseBo;
    }

}
