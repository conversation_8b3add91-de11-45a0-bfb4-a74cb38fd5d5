package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dto.MediaAndAttachedDto;
import com.get.financecenter.dto.PrepayApplicationFormDto;
import com.get.financecenter.dto.query.PrepayApplicationFormQueryDto;
import com.get.financecenter.service.IPrepayApplicationFormService;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import com.get.financecenter.vo.PrepayApplicationFormVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/4/1 10:25
 */
@Api(tags = "借款流程")
@RestController
@RequestMapping("finance/prepayApplication")
public class PrepayApplicationController {

    @Resource
    IPrepayApplicationFormService iPrepayApplicationFormService;

    /**
     * @ Description :
     * @ Param [prepayApplicationFormVo]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation("借款保存")
    @PostMapping("/savePrepay")
    public ResponseBo savePrepay(@RequestBody PrepayApplicationFormDto prepayApplicationFormDto) {
        if (prepayApplicationFormDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        iPrepayApplicationFormService.save(prepayApplicationFormDto);
        return ResponseBo.ok();
    }

    /**
     * @ Description :
     * @ Param [businessKey, procdefKey, companyId]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation("借款流程开始")
    @GetMapping("/startPrepayFlow")
    public ResponseBo startPrepayFlow(@RequestParam("businessKey") String businessKey,
                                      @RequestParam("procdefKey") String procdefKey,
                                      @RequestParam("companyId") String companyId) {
        if (businessKey == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }

        iPrepayApplicationFormService.startBorrowFlow(businessKey, procdefKey, companyId);
        return ResponseBo.ok();
    }

    /**
     * @ Description :
     * @ Param [prepayApplicationForm]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
/*
    @ApiIgnore
    @PostMapping("/updateBorrowMoneyStatus")
    public ResponseBo updateBorrowMoneyStatus(@RequestBody PrepayApplicationForm prepayApplicationForm) {
        iPrepayApplicationFormService.updateBorrowMoneyStatus(prepayApplicationForm);

        return ResponseBo.ok();
    }
*/

    /**
     * @ Description :
     * @ Param [id]
     * @ return com.get.financecenter.vo.PrepayApplicationFormVo
     * @ author LEO
     */
    @ApiIgnore
    @GetMapping("/getBorrowMoneyById")
    @VerifyPermission(IsVerify = false)
    public PrepayApplicationFormVo getBorrowMoneyById(@RequestParam("id") Long id) throws GetServiceException {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        PrepayApplicationFormVo borrowMoneyDto = iPrepayApplicationFormService.getBorrowMoneyById(id);
        return borrowMoneyDto;
    }

    /**
     * @ Description :
     * @ Param [page]
     * @ return com.get.common.result.ResponseBo<com.get.financecenter.vo.PrepayApplicationFormVo>
     * @ author LEO
     */
    @ApiOperation("借款表单列表，selectStatus属性:0查登当前录人，1查全部,2我的审批")
    @PostMapping("/getPrepayData")
    public ResponseBo<PrepayApplicationFormVo> getPrepayData(@RequestBody SearchBean<PrepayApplicationFormQueryDto> page) {
        List<PrepayApplicationFormVo> mpayList = iPrepayApplicationFormService.getBorrowMoneyData(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        ListResponseBo<PrepayApplicationFormVo> mpayDtoListResponseBo = new ListResponseBo<>(mpayList, p);
        return mpayDtoListResponseBo;
    }

    /**
     * @ Description :
     * @ Param [businessKey]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation("借款表详细数据")
    @GetMapping("/getPrepayDetailData")
    public ResponseBo getPrepayDetailData(@RequestParam("businessKey") Long businessKey) {
        PrepayApplicationFormVo prepayApplicationFormVo = iPrepayApplicationFormService.getPrepayDetailData(businessKey);

        return new ResponseBo<>(prepayApplicationFormVo);
    }

    /**
     * @ Description :
     * @ Param [mediaAttachedVo]
     * @ return com.get.common.result.ResponseBo<com.get.financecenter.vo.MediaAndAttachedDto>
     * @ author LEO
     */
    @ApiOperation(value = "保存借款单附件接口")
    @PostMapping("upload")
    public ResponseBo<FMediaAndAttachedVo> upload(@RequestBody  List<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(iPrepayApplicationFormService.addInstitutionMedia(mediaAttachedVo));
    }

    /**
     * @ Description :
     * @ Param [prepayApplicationFormVo]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation("单纯更新表单数据")
    @PostMapping("updataPrepayData")
    public ResponseBo updataMpayData(@RequestBody PrepayApplicationFormDto prepayApplicationFormDto) {
        iPrepayApplicationFormService.
                updataPrepayData(prepayApplicationFormDto);
        return ResponseBo.ok();

    }


    /**
     * @ Description :
     * @ Param [taskId, status]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation("重新提交或放弃")
    @GetMapping("getUserSubmit")
    public ResponseBo getUserSubmit(@RequestParam("taskId") String taskId, @RequestParam("status") String status) {
        iPrepayApplicationFormService.getUserSubmit(taskId, status);
        return ResponseBo.ok();
    }

    /**
     * @ Description :
     * @ Param [page]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation("文件回显")
    @PostMapping("getPrepayFileData")
    public ResponseBo getPrepayFileData(@RequestBody SearchBean<MediaAndAttachedDto> page) {
        List<FMediaAndAttachedVo> payFileData = iPrepayApplicationFormService.getPrepayFileData(page.getData(), page);
        return new ListResponseBo<>(payFileData);

    }

    /**
     * @ Description :
     * @ Param [id]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation("借款申请单作废")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/申请表单管理/借款申请单作废")
    @PostMapping("cancelPrepayApplicationForm")
    public ResponseBo cancelPrepayApplicationForm(@RequestParam("id") Long id) {
        iPrepayApplicationFormService.cancelPrepayApplicationForm(id);
        return ResponseBo.ok();
    }

    /**
     * @ Description :
     * @ Param [id]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation("撤单保存内容")
    @GetMapping("getRevokePrepayApplication")
    @VerifyPermission(IsVerify = false)
    public ResponseBo getRevokePrepayApplication(@RequestParam("id") Long id, @RequestParam("summary") String summary) {
        iPrepayApplicationFormService.getRevokePrepayApplication(id, summary);
        return ResponseBo.ok();
    }


}
