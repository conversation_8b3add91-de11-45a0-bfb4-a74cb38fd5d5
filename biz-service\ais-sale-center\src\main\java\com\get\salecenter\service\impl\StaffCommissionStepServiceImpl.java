package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.StaffCommissionStepMapper;
import com.get.salecenter.vo.StaffCommissionStepVo;
import com.get.salecenter.entity.StaffCommissionAction;
import com.get.salecenter.entity.StaffCommissionStep;
import com.get.salecenter.service.IStaffCommissionActionService;
import com.get.salecenter.service.IStaffCommissionStepService;
import com.get.salecenter.dto.StaffCommissionStepDto;
import com.google.common.collect.Lists;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2023/2/6 10:06
 * @verison: 1.0
 * @description:
 */
@Service
public class StaffCommissionStepServiceImpl extends GetServiceImpl<StaffCommissionStepMapper, StaffCommissionStep> implements IStaffCommissionStepService {

    @Resource
    private UtilService utilService;

    @Lazy
    @Resource
    private IStaffCommissionActionService staffCommissionActionService;

    /**
     * 批量新增
     * @param staffCommissionStepDtos
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<StaffCommissionStepDto> staffCommissionStepDtos) {
        //验证key是否重复
        doValidateBatchAdd(staffCommissionStepDtos);
        //处理批量新增
        doSaveBatchStaffCommissionSteps(staffCommissionStepDtos);

    }

    /**
     * 删除
     * @param id
     */
    @Override
    public void delete(Long id) {
        //验证删除
        doValidateDelete(id);

        boolean b = removeById(id);
        if (!b){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    /**
     * 更新
     * @param staffCommissionStepDto
     * @return
     */
    @Override
    public StaffCommissionStepVo updateStaffCommissionStep(StaffCommissionStepDto staffCommissionStepDto) {
        //验证更新
        doValidateUpdate(staffCommissionStepDto);

        StaffCommissionStep staffCommissionStep = BeanCopyUtils.objClone(staffCommissionStepDto, StaffCommissionStep::new);
        utilService.setUpdateInfo(staffCommissionStep);
        boolean b = updateById(staffCommissionStep);
        if (!b){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        return BeanCopyUtils.objClone(staffCommissionStep, StaffCommissionStepVo::new);
    }

    /**
     * 列表
     * @param staffCommissionStepDto
     * @param page
     * @return
     */
    @Override
    public List<StaffCommissionStepVo> getStaffCommissionStepDtos(StaffCommissionStepDto staffCommissionStepDto, Page page) {
        LambdaQueryWrapper<StaffCommissionStep> wrapper = Wrappers.<StaffCommissionStep>lambdaQuery();
        if (GeneralTool.isNotEmpty(staffCommissionStepDto.getFkCompanyId())){
            wrapper.eq(StaffCommissionStep::getFkCompanyId, staffCommissionStepDto.getFkCompanyId());
        }
        if (GeneralTool.isNotEmpty(staffCommissionStepDto.getStepName())){
            wrapper.like(StaffCommissionStep::getStepName, staffCommissionStepDto.getStepName());
        }
        wrapper.orderByDesc(StaffCommissionStep::getStepOrder);
        IPage<StaffCommissionStep> iPage = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<StaffCommissionStep> staffCommissionSteps = iPage.getRecords();
        page.setAll((int) iPage.getTotal());
        if (GeneralTool.isEmpty(staffCommissionSteps)){
            return Collections.emptyList();
        }
        return BeanCopyUtils.copyListProperties(staffCommissionSteps, StaffCommissionStepVo::new);
    }

    /**
     * 拖拽排序
     * @param staffCommissionStepDtos
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void movingOrder(List<StaffCommissionStepDto> staffCommissionStepDtos) {
        if (GeneralTool.isEmpty(staffCommissionStepDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        StaffCommissionStep ro = BeanCopyUtils.objClone(staffCommissionStepDtos.get(0), StaffCommissionStep::new);
        Integer oneorder = ro.getStepOrder();
        StaffCommissionStep rt = BeanCopyUtils.objClone(staffCommissionStepDtos.get(1), StaffCommissionStep::new);
        Integer twoorder = rt.getStepOrder();
        ro.setStepOrder(twoorder);
        utilService.setUpdateInfo(ro);
        rt.setStepOrder(oneorder);
        utilService.setUpdateInfo(rt);
        List<StaffCommissionStep> staffCommissionSteps = Lists.newArrayList(ro, rt);
        boolean b = updateBatchById(staffCommissionSteps);
        if (!b) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
    }

    /**
     * 下拉
     * @param companyId
     * @return
     */
    @Override
    public List<BaseSelectEntity> getCancelOfferReasonSelect(Long companyId) {
        List<StaffCommissionStep> staffCommissionSteps = list(Wrappers.<StaffCommissionStep>lambdaQuery().eq(StaffCommissionStep::getFkCompanyId, companyId).orderByDesc(StaffCommissionStep::getStepOrder));
        if (GeneralTool.isEmpty(staffCommissionSteps)){
            return Collections.emptyList();
        }
        return staffCommissionSteps.stream().map(s->{
            BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
            baseSelectEntity.setId(s.getId());
            baseSelectEntity.setName(s.getStepName());
            baseSelectEntity.setNum(s.getStepKey());
            return baseSelectEntity;
        }).collect(Collectors.toList());
    }

    /**
     * 验证更新
     * @param staffCommissionStepDto
     */
    private void doValidateUpdate(StaffCommissionStepDto staffCommissionStepDto) {
        List<StaffCommissionStep> staffCommissionSteps = list(Wrappers.<StaffCommissionStep>lambdaQuery()
                .eq(StaffCommissionStep::getStepKey, staffCommissionStepDto.getStepKey())
                .ne(StaffCommissionStep::getId, staffCommissionStepDto.getId()).last("LIMIT 1"));
        if (GeneralTool.isNotEmpty(staffCommissionSteps)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("KEY_CODE_INFO_EXITS"));
        }
    }

    /**
     * 验证是否能删除
     * @param id
     */
    private void doValidateDelete(Long id) {
        if (GeneralTool.isEmpty(id)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StaffCommissionStep staffCommissionStep = getById(id);
        List<StaffCommissionAction> staffCommissionActions = staffCommissionActionService.list(Wrappers.<StaffCommissionAction>lambdaQuery()
                .eq(StaffCommissionAction::getFkStaffCommissionStepKey, staffCommissionStep.getStepKey()).last("LIMIT 1"));
        if (GeneralTool.isNotEmpty(staffCommissionActions)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("has_associated"));
        }
    }

    /**
     * 处理批量新增
     * @param staffCommissionStepDtos
     */
    private void doSaveBatchStaffCommissionSteps(List<StaffCommissionStepDto> staffCommissionStepDtos) {
        final Integer[] stepOrder = {this.baseMapper.getMaxStepOrder()};
        List<StaffCommissionStep> staffCommissionSteps = BeanCopyUtils.copyListProperties(staffCommissionStepDtos, StaffCommissionStep::new);
        staffCommissionSteps.forEach(staffCommissionStep -> {
            staffCommissionStep.setStepOrder(stepOrder[0]);
            stepOrder[0]++;
            utilService.setCreateInfo(staffCommissionStep);
        });
        boolean b = saveBatch(staffCommissionSteps);
        if (!b){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
    }


    /**
     * 批量新增校验
     * @param staffCommissionStepDtos
     */
    private void doValidateBatchAdd(List<StaffCommissionStepDto> staffCommissionStepDtos) {
        if (GeneralTool.isEmpty(staffCommissionStepDtos)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        Set<String> keys = staffCommissionStepDtos.stream().map(StaffCommissionStepDto::getStepKey).collect(Collectors.toSet());
        if (keys.size()!= staffCommissionStepDtos.size()){
            throw new GetServiceException(LocaleMessageUtils.getMessage("KEY_CODE_INFO_EXITS"));
        }
        List<StaffCommissionStep> staffCommissionSteps = list(Wrappers.<StaffCommissionStep>lambdaQuery()
                .in(StaffCommissionStep::getStepKey, keys).last("LIMIT 1"));
        if (GeneralTool.isNotEmpty(staffCommissionSteps)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("KEY_CODE_INFO_EXITS"));
        }

    }
}
