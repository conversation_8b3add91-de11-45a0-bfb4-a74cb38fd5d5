<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ConventionTablePersonMapper">
  <resultMap id="BaseResultMap" type="com.get.salecenter.entity.ConventionTablePerson">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_convention_table_id" jdbcType="BIGINT" property="fkConventionTableId" />
    <result column="fk_convention_person_id" jdbcType="BIGINT" property="fkConventionPersonId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>

  <insert id="insertSelective" parameterType="com.get.salecenter.entity.ConventionTablePerson" keyProperty="id" useGeneratedKeys="true">
    insert into r_convention_table_person
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkConventionTableId != null">
        fk_convention_table_id,
      </if>
      <if test="fkConventionPersonId != null">
        fk_convention_person_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkConventionTableId != null">
        #{fkConventionTableId,jdbcType=BIGINT},
      </if>
      <if test="fkConventionPersonId != null">
        #{fkConventionPersonId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getPersonIds" resultType="java.lang.Long">
    select
     fk_convention_person_id
    from
     r_convention_table_person
    where
     fk_convention_table_id
    IN
     <foreach item="item" index="index" collection="tableIds" open="(" separator="," close=")">
       #{item}
     </foreach>

  </select>

  <select id="getTableByPersonId" resultType="com.get.salecenter.entity.ConventionTable">
    select
     b.fk_table_type_key, b.table_num
    from
     r_convention_table_person a
    left join
     m_convention_table b
    on
     a.fk_convention_table_id = b.id
    where
     a.fk_convention_person_id = #{id}

  </select>

  <select id="conventionTablePersonIsEmpty" resultType="java.lang.Boolean">
    select IFNULL(max(id),0) id from r_convention_table_person where ${fieldName} = #{id} LIMIT 1

  </select>
    <select id="getConventionTablePersonByPersonId"
            resultType="com.get.salecenter.entity.ConventionTablePerson">
      SELECT
        a.*
      FROM
        r_convention_table_person a
          INNER JOIN m_convention_table b on a.fk_convention_table_id = b.id and b.fk_convention_id = #{conventionId} and b.fk_table_type_key = #{key}
        where a.fk_convention_person_id = #{conventionPersonId}
    </select>

  <select id="getTableByPersonIds" resultType="com.get.salecenter.vo.ConventionTablePersonNumVo">
    select b.fk_table_type_key,a.fk_convention_person_id,
    group_concat(b.table_num) as tableNum
    from r_convention_table_person a
    left join
    m_convention_table b
    on
    a.fk_convention_table_id = b.id
    where a.fk_convention_person_id in
    <foreach item="id" index="index" collection="ids" open="(" separator="," close=")">
      #{id}
    </foreach>
    GROUP BY b.fk_table_type_key,a.fk_convention_person_id
  </select>
</mapper>