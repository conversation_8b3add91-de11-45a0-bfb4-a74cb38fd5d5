package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.ConventionTableType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Sea
 * @create: 2020/8/26 16:02
 * @verison: 1.0
 * @description:
 */
@Data
@ApiModel(value = "峰会桌台类型返回类")
public class ConventionTableTypeVo extends BaseEntity {

    //=========实体类ConventionTableType==============
    private static final long serialVersionUID = 1L;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    @Column(name = "type_name")
    private String typeName;
    /**
     * 类型关键字
     */
    @ApiModelProperty(value = "类型关键字")
    @Column(name = "type_key")
    private String typeKey;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    @Column(name = "view_order")
    private Integer viewOrder;

}
