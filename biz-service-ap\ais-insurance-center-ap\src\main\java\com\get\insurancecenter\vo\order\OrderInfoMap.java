package com.get.insurancecenter.vo.order;

import com.get.insurancecenter.entity.InsuranceCompany;
import com.get.insurancecenter.entity.ProductType;
import com.get.insurancecenter.vo.agent.AgentEntity;
import com.get.permissioncenter.vo.CompanyVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author:Oliver
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:订单详情相关MAP
 */
@Data
public class OrderInfoMap {

    @ApiModelProperty(value = "保险公司Map")
    private Map<Long, InsuranceCompany> insuranceCompanyMap;

    @ApiModelProperty(value = "保险产品类型Map")
    private Map<Long, ProductType> productTypeMap;

    @ApiModelProperty(value = "代理信息Map")
    private Map<Long, AgentEntity> agentMap;

    @ApiModelProperty(value = "分公司信息Map")
    private Map<Long, CompanyVo> companyMap;

    @ApiModelProperty(value = "创建人名称Map")
    private Map<Long, String> creatorMap;

    public OrderInfoMap() {
        agentMap = new HashMap<>();
        companyMap = new HashMap<>();
        creatorMap = new HashMap<>();
        insuranceCompanyMap = new HashMap<>();
        productTypeMap = new HashMap<>();
    }

}
