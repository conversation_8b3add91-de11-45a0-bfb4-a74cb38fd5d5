package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.institutioncenter.dto.InstitutionCoursePathwayDto;
import com.get.institutioncenter.vo.InstitutionCoursePathwayVo;
import com.get.institutioncenter.service.IInstitutionCoursePathwayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/12/18
 * @TIME: 16:36
 * @Description:
 **/
@Api(tags = "桥梁课程管理")
@RestController
@RequestMapping("/institution/institutionCoursePathway")
public class InstitutionCoursePathwayController {
    @Resource
    private IInstitutionCoursePathwayService institutionCoursePathwayService;

    /**
     * 修改信息
     *
     * @param institutionCoursePathwayDto
     * @return
     * @
     */
    @ApiOperation(value = "绑定桥梁课程", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/桥梁课程管理/绑定桥梁课程")
    @PostMapping("update")
    public ResponseBo update(@RequestBody InstitutionCoursePathwayDto institutionCoursePathwayDto) {
        institutionCoursePathwayService.update(institutionCoursePathwayDto);
        return ResponseBo.ok();
    }

    /**
     * 修改绑定的信息
     *
     * @param institutionCoursePathwayDto
     * @return
     * @
     */
    @ApiOperation(value = "修改绑定的桥梁课程", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/桥梁课程管理/修改绑定桥梁课程")
    @PostMapping("updateInstitutionCoursePathway")
    public ResponseBo updateInstitutionCoursePathway(@RequestBody InstitutionCoursePathwayDto institutionCoursePathwayDto) {
        institutionCoursePathwayService.updateInstitutionCoursePathway(institutionCoursePathwayDto);
        return ResponseBo.ok();
    }
    /**
     * 反向绑定非桥梁课程
     *
     * @Date 17:55 2021/7/22
     * <AUTHOR>
     */
//    @ApiOperation(value = "反向绑定非桥梁课程", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/桥梁课程管理/绑定桥梁课程")
//    @PostMapping("pathwayUpdate")
//    public ResponseBo pathwayUpdate(@RequestBody InstitutionCoursePathwayDto institutionCoursePathwayVo)  {
//        institutionCoursePathwayService.pathwayUpdate(institutionCoursePathwayVo);
//        return ResponseBo.ok();
//    }


    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "桥梁课程列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/桥梁课程管理/查询桥梁课程")
    @PostMapping("datas")
    public ResponseBo<InstitutionCoursePathwayVo> datas(@RequestBody SearchBean<InstitutionCoursePathwayDto> page) {
        List<InstitutionCoursePathwayVo> datas = institutionCoursePathwayService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @param id
     * @return
     * @
     */

    @ApiOperation(value = "删除桥梁课程")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/桥梁课程管理/删除桥梁课程")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        institutionCoursePathwayService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/桥梁课程管理/桥梁课程详情")
    @GetMapping("/{id}")
    public ResponseBo<InstitutionCoursePathwayVo> detail(@PathVariable("id") Long id) {
        InstitutionCoursePathwayVo data = institutionCoursePathwayService.findInstitutionCoursePathwayById(id);
        return new ResponseBo<>(data);
    }

}
