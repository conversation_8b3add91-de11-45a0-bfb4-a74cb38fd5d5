package com.get.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.get.entity.MMailDoris;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DS("mail-doris")
@Mapper
public interface MMailDorisMapper extends BaseMapper<MMailDoris> {
//    IPage<Long> selectMailDoris(Page<Long> page, @Param("searchMailVo") SearchMailVo searchMailVo);
    void deleteMailDoris(@Param("id") Long id);
    void insertDoris(MMailDoris mailDoris);
    List<Long> selectAllIds();
    List<Long> selectIdsByDate(String date);
}
