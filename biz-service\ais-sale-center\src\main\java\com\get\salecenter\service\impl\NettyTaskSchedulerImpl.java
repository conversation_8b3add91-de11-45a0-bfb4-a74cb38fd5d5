package com.get.salecenter.service.impl;

import com.get.common.eunms.ProjectKeyEnum;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.config.NettyPushConfig;
import com.get.salecenter.vo.StudentCountVo;
import com.get.salecenter.service.ISystemPageService;
import com.get.salecenter.service.NettyTaskScheduler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2021/2/6
 * @TIME: 15:52
 * @Description:
 **/
@Service
@EnableScheduling
@Slf4j
public class NettyTaskSchedulerImpl implements NettyTaskScheduler {

    @Resource
    private TaskScheduler taskScheduler;
    @Resource
    private ISystemPageService systemPageService;


    @Override
    public void watchDog() {
        taskScheduler.schedule(() -> {
            List<StudentCountVo> pushFlag = NettyPushConfig.getPushFlagMap().get(ProjectKeyEnum.PUSH_FLAG.key);
            //todo getPushFlagMap可能要清空一下
            if (GeneralTool.isNotEmpty(pushFlag)) {
                log.info("=========调度websocket定时器==============>pushFlag:{}",pushFlag);
                //推送
                systemPageService.pushDatas();
            }
        }, triggerContext -> {
            //4秒查询一次
            String cron = "*/5 * * * * ?";
            CronTrigger trigger = new CronTrigger(cron);
            return trigger.nextExecutionTime(triggerContext);
        });
    }
}
