package com.get.officecenter.feign;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.ResponseBo;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.officecenter.dto.TaskItemDto;
import com.get.officecenter.entity.Task;
import com.get.officecenter.entity.TaskItem;
import com.get.officecenter.service.*;
import com.get.officecenter.vo.*;
import com.get.officecenter.entity.LeaveApplicationForm;
import com.get.officecenter.entity.OfficeMediaAndAttached;
import com.get.officecenter.dto.CreateTaskAndTaskItemDto;
import com.get.officecenter.dto.LeaveLogDto;
import com.get.officecenter.dto.LeaveStockDto;
import com.get.officecenter.dto.query.LeaveApplicationFormQueryDto;
import com.get.permissioncenter.vo.WxCpUserVo;
import com.get.remindercenter.vo.ReminderTaskCountVo;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@RestController
@AllArgsConstructor
@VerifyPermission(IsVerify = false)
public class OfficeCenterClient implements IOfficeCenterClient {

    private ICommonService commonService;
    private ILeaveApplicationFormService leaveApplicationFormService;
    private LeaveLogService leaveLogService;
    private LeaveStockService leaveStockService;
    private IWxCpService wxCpService;
    private IMediaAndAttachedService mediaAndAttachedService;
    private CustomTaskService customTaskService;


    @Resource
    private ITaskItemService taskItemService;

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Boolean> changeStatus(Integer status, String tableName, Long businessKey) {
        return Result.data(commonService.changeStatus(status, tableName, businessKey));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<String> getLeaveTypeNameById(Long id) {
        return Result.data(leaveApplicationFormService.getLeaveTypeNameById(id));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<LeaveApplicationForm> getLeaveApplicationForm(Long id) {
        return Result.data(leaveApplicationFormService.getLeaveApplicationForm(id));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Boolean> addSystemLeaveLog(LeaveLogDto leaveLogDto) {
        return Result.data(leaveLogService.addSystemLeaveLog(leaveLogDto));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Long> addSystem(LeaveStockDto leaveStockDto) {
        return Result.data(leaveStockService.addSystem(leaveStockDto));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Boolean> updateSystemLeavetock(LeaveStockDto leaveStockDto) {
        return Result.data(leaveStockService.adjustSystemLeavetock(leaveStockDto));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public List<LeaveStockVo> getLeaveStockDtos(LeaveStockDto leaveStockDto) {
        return leaveStockService.getLeaveStockDtos(leaveStockDto);
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public String getLeaveTypeKeyById(Long id) {
        return leaveApplicationFormService.getLeaveTypeKeyById(id);
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public List<LeaveLogVo> getSystemLeaveLog(LeaveLogDto leaveLogDto) {
        return leaveLogService.getSystemLeaveLog(leaveLogDto);
    }

    @Override
    public Result<List<ReminderTaskCountVo>> getApplicationAndApprovalCount(Long staffId) {
        return Result.data(leaveApplicationFormService.getApplicationAndApprovalCount(staffId));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<Boolean> hasMediaAndAttach(String key, Long id) {
        return Result.data(leaveApplicationFormService.hasMediaAndAttach(key,id));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<WxCpUserVo> getWxCpUserIdByCode(String code) {
        return Result.data(wxCpService.getWxCpUserIdByCode(code));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public List<LeaveStockVo> getEfficientLeaveStockDtos(LeaveStockDto leaveStockDto) {
        return leaveStockService.getEfficientLeaveStockDtos(leaveStockDto);
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<List<OfficeMediaAndAttached>> getOfficeMediaAndAttacheds(Long id,String loginId) {
        LambdaQueryWrapper<OfficeMediaAndAttached> wrapper = Wrappers.lambdaQuery(OfficeMediaAndAttached.class);
        wrapper.eq(OfficeMediaAndAttached::getFkTableName, TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)
                .eq(OfficeMediaAndAttached::getFkTableId,id)
                .eq(OfficeMediaAndAttached::getTypeKey, FileTypeEnum.OFFICE_LEAVE_APPLICATION_FORM_FILE.key)
                .eq(OfficeMediaAndAttached::getGmtCreateUser,loginId);
        return Result.data(mediaAndAttachedService.list(wrapper));
    }

    @Override
    public Result<List<LeaveApplicationFormExportVo>> getLeaveApplicationFormExportDtos(LeaveApplicationFormQueryDto leaveApplicationFormVo) {
        return Result.data(leaveApplicationFormService.getLeaveApplicationFormExportDtos(leaveApplicationFormVo));
    }

    @Override
    public Result<Boolean> addTaskAndTaskItem(CreateTaskAndTaskItemDto createTaskAndTaskItemDto) {
        return Result.data(customTaskService.addTaskAndTaskItem(createTaskAndTaskItemDto));
    }

    /**
     * 获取任务详情
     * @param taskId
     * @return
     */
    @Override
    @VerifyLogin(IsVerify = false)
    public Result<CustomTaskVo> getTask(Long taskId) {
        return Result.data(customTaskService.getTaskById(taskId));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<List<TaskItem>> getTaskList(Long taskId) {
       List<Long> ids =new ArrayList<>();
       ids.add(taskId);
        return Result.data(taskItemService.getTaskItemList(ids));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<List<Long>> getTaskItemIds(Long taskId) {
        List<Long> ids =new ArrayList<>();
        ids.add(taskId);
        return Result.data(taskItemService.getFkStaffIdTo(ids));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<List<Long>> getUnfinishedTaskItemReceiver(Long taskId) {
        List<Long> ids =new ArrayList<>();
        ids.add(taskId);
        return Result.data(taskItemService.getStaffIdByStatus(ids));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<TaskItem> getTaskItem(Long taskItemId) {
        return Result.data(taskItemService.getTaskItem(taskItemId));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<List<Task>> getTaskByEndTime() {
        return  Result.data(customTaskService.getTaskByEndTime());
    }

}
