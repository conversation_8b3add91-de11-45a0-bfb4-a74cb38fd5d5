package com.get.salecenter.dao.newissue;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.NewIssueUserSuperior;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
@DS("newissuedb")
public interface newIssueUserSuperiorMapper extends BaseMapper<NewIssueUserSuperior> {

    List<NewIssueUserSuperior> selectExistDatas(@Param("fkUserId")Long fkUserId);
}