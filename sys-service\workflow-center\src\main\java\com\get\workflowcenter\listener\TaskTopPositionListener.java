package com.get.workflowcenter.listener;

import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.secure.StaffInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.SpringUtil;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.workflowcenter.component.IWorkFlowHelper;
import lombok.SneakyThrows;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.TaskService;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.activiti.engine.repository.ProcessDefinition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.StringJoiner;

/**
 * @author: Sea
 * @create: 2021/3/24 10:39
 * @verison: 1.0
 * @description: 流程节点用来获取任务部门最高职位人设置候选人的监听器
 */
public class TaskTopPositionListener implements TaskListener {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @SneakyThrows
    @Override
    public void notify(DelegateTask task) {
        System.out.println("进入任务最上级监听--------------------------------");
        //获取feign调用service
        IPermissionCenterClient permissionCenterClient = SpringUtil.getBean(IPermissionCenterClient.class);
        IWorkFlowHelper workFlowHelper = SpringUtil.getBean(IWorkFlowHelper.class);

        TaskService taskService = SpringUtil.getBean(TaskService.class);

        StaffVo staffVo = workFlowHelper.getStaffDto(task);

        //登录人公司id和部门id 查找该部门最高职位人ids
        StaffInfo staff;
        if (GeneralTool.isNotEmpty(staffVo)) {
            staff = BeanCopyUtils.objClone(staffVo, StaffInfo::new);
            staff.setStaffId(staffVo.getId());
        } else {
            staff = SecureUtil.getStaffInfo();
        }

        Long departmentId;
        //如果描述中没有设置部门id 表示查找当前登录人部门的最高职位人，不然就是指定部门的最高职位人
        if (GeneralTool.isEmpty(task.getDescription())) {
            departmentId = staff.getFkDepartmentId();
        } else {
            departmentId = Long.valueOf(task.getDescription());
        }
        System.out.println("用户当前公司：" + staff.getFkCompanyId() + "，用户当前部门：" + departmentId + "--------------------------------");
//        ListResponseBo responseBo = permissionCenterClient.getTopPositionStaffIds(staff.getFkCompanyId(), departmentId);
//        JSONArray objects = JSONUtil.parseArray(responseBo.getDatas());
//        List<String> staffIdList = JSONUtil.toList(objects, String.class);

        List<String> staffIdList = new ArrayList<>();
        Result<List<Long>> result = permissionCenterClient.getTopPositionStaffIds(staff.getFkCompanyId(), departmentId);
        if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
            result.getData().forEach(id -> {
                if (id != null) {
                    String id_ = String.valueOf(id);
                    staffIdList.add(id_);
                }

            });
        }
        //获取流程名称
        ProcessEngine processEngine = SpringUtil.getBean(ProcessEngine.class);

        ProcessDefinition processDefinition = processEngine.getRepositoryService()
                .createProcessDefinitionQuery()
                .processDefinitionId(task.getProcessDefinitionId())
                .singleResult();

        if (GeneralTool.isNotEmpty(staffIdList)) {
            if (staffIdList.size() == 1) {
                //只有一个人就是是待办人
                taskService.setAssignee(task.getId(), staffIdList.get(0));
                StringJoiner stringJoiner = new StringJoiner(",");
                stringJoiner.add(ProjectKeyEnum.WORKFLOW_CENTER.key).add(ProjectKeyEnum.WORKFLOW_CENTER_TO_DO.key);
                if (TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key.equals(processDefinition.getKey())) {
                    stringJoiner.add(ProjectKeyEnum.OFFICE_CENTER.key);
                }
                workFlowHelper.sendMessage(staffVo, staffIdList, processDefinition, "待审核", task, stringJoiner.toString());

            } else {
                //有多个就全部设置为候选人
                task.addCandidateUsers(staffIdList);
                StringJoiner stringJoiner = new StringJoiner(",");
                stringJoiner.add(ProjectKeyEnum.WORKFLOW_CENTER.key).add(ProjectKeyEnum.WORKFLOW_CENTER_TO_SIGN.key);
                if (TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key.equals(processDefinition.getKey())) {
                    stringJoiner.add(ProjectKeyEnum.OFFICE_CENTER.key);
                }
                workFlowHelper.sendMessage(staffVo, staffIdList, processDefinition, "待签取", task, stringJoiner.toString());
            }
        } else {
            HashMap<String, Object> map = new HashMap<>();
            map.put("sequenceFlowsStatus", 0);
            taskService.setVariableLocal(task.getId(), "approvalAction", 0);
            //没有处理人，系统自动驳回
            taskService.addComment(task.getId(), task.getProcessInstanceId(), "系统自动驳回，" + task.getName() + "：找不到处理人。");
            taskService.complete(task.getId(), map);

        }
    }
}
