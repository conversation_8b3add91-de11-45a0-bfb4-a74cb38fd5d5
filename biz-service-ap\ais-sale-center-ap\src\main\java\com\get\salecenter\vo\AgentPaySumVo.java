package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　　　◆　　　　　　　　　　　　◆　　　　　　　　　　　　　　◆　　　　　　　　　　　　　◆◆　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　◆◆◆　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆
 * 　　　◆◆　　　　　◆◆　　　　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆◆◆◆
 * 　　　◆◆◆　　　◆◆◆　　　　　　　◆◆　　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆◆
 * 　　　　◆◆◆　◆◆◆　　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆
 * 　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　　◆◆
 * 　　　　　　◆◆◆　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆
 * 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆◆
 * <p>
 * Time: 16:22
 * Date: 2021/11/19
 * Description:代理应付汇总统计返回类
 */
@Data
public class AgentPaySumVo {
    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    /**
     * 代理id
     */
    @ApiModelProperty(value = "代理id")
    private Long agentId;

    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    private String agentName;

    public String getAgentName() {
        if (StringUtils.isNotBlank(nameNote)) {
            agentName += "(" + nameNote +")";
        }
        return agentName;
    }

    @ApiModelProperty("代理名称备注")
    private String nameNote;

    /**
     * 完成申请数
     */
    @ApiModelProperty(value = "完成申请数")
    private Integer compleApplyCount;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;


    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String fkCurrencyTypeName;

    /**
     * 应付金额
     */
    @ApiModelProperty(value = "应付金额")
    private String payableAmount;

    /**
     * 应付金额（带币种名称）
     */
    @ApiModelProperty(value = "应付金额（带币种名称）")
    private String payableAmountStr;

    /**
     * 实付金额
     */
    @ApiModelProperty(value = "实付金额")
    private String amountPayable;

    /**
     * 结算时间段实付金额
     */
    @ApiModelProperty(value = "结算时间段实付金额")
    private String settlementAmountPayable;

    /**
     * 结算时间段实付金额
     */
    @ApiModelProperty(value = "结算时间段实付金额（带币种名称）")
    private String settlementAmountPayableStr;

    /**
     * 实付金额（带币种名称）
     */
    @ApiModelProperty(value = "实付金额（带币种名称）")
    private String amountPayableStr;

    /**
     * 实付差额
     */
    @ApiModelProperty(value = "实付差额")
    private String differPay;

    public String getDifferPay() {
        return String.valueOf(Math.abs(Double.parseDouble(differPay)));
    }

    /**
     * 实付差额（带币种名称）
     */
    @ApiModelProperty(value = "实付差额（带币种名称）")
    private String differPayStr;

}
