package com.get.examcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.utils.GeneralTool;
import com.get.examcenter.vo.ScoreTitleVo;
import com.get.examcenter.service.MediaAndAttachedService;
import com.get.examcenter.service.ScoreTitleService;
import com.get.examcenter.dto.ScoreTitleDto;
import com.get.examcenter.dto.query.ScoreTitleQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Created by Jerry.
 * Time: 17:11
 * Date: 2021/8/27
 * Description:称号管理控制器
 */
@Api(tags = "称号管理")
@RestController
@RequestMapping("exam/scoreTitle")
public class ScoreTitleController {

    @Resource
    private ScoreTitleService scoreTitleService;
    @Resource
    private MediaAndAttachedService mediaAndAttachedMsoService;


    /**
     * @Description: 列表数据
     * @Author: Jerry
     * @Date:18:31 2021/8/27
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.LIST, description = "考试中心/称号管理/列表数据")
    @PostMapping("datas")
    public ResponseBo<ScoreTitleVo> datas(@RequestBody SearchBean<ScoreTitleQueryDto> page) {
        List<ScoreTitleVo> datas = scoreTitleService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * @Description: 新增
     * @Author: Jerry
     * @Date:18:31 2021/8/27
     */
    @ApiOperation(value = "新增", notes = "")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.ADD, description = "考试中心/称号管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(ScoreTitleDto.Add.class) ScoreTitleDto scoreTitleDto) {
        scoreTitleService.add(scoreTitleDto);
        return SaveResponseBo.ok();
    }

    /**
     * @Description: 更新
     * @Author: Jerry
     * @Date:18:31 2021/8/27
     */
    @ApiOperation(value = "更新", notes = "")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.EDIT, description = "考试中心/称号管理/更新")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated(ScoreTitleDto.Update.class) ScoreTitleDto scoreTitleDto) {
        scoreTitleService.update(scoreTitleDto);
        return UpdateResponseBo.ok();
    }

    /**
     * @Description: 详情
     * @Author: Jerry
     * @Date:18:30 2021/8/27
     */
    @ApiOperation(value = "详情", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.DETAIL, description = "考试中心/称号管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<ScoreTitleVo> detail(@PathVariable("id") Long id) {
        return new ResponseBo<>(scoreTitleService.detail(id));
    }

    /**
     * @Description: 删除
     * @Author: Jerry
     * @Date:18:30 2021/8/27
     */
    @ApiOperation(value = "删除", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.DELETE, description = "考试中心/称号管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        scoreTitleService.delete(id);
        return DeleteResponseBo.ok();
    }


    /**
     * @Description: 上移下移
     * @Author: Jerry
     * @Date:18:30 2021/8/27
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.EDIT, description = "考试中心/称号管理/上移下移")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<ScoreTitleDto> sitemapVo) {
        scoreTitleService.movingOrder(sitemapVo);
        return ResponseBo.ok();
    }


    /**
     * @Description: 称号上传文件
     * @Author: Jerry
     * @Date:14:51 2021/9/7
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "称号上传文件")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.ADD, description = "考试中心/称号管理/称号上传文件")
    @PostMapping("/upload")
    public ResponseBo msoUpload(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files) {
        ResponseBo responseBo = new ResponseBo();
        if (GeneralTool.isNotEmpty(files)) {
            responseBo.put("data", mediaAndAttachedMsoService.upload(files, LoggerModulesConsts.EXAMCENTER));
        }
        return responseBo;
    }
}
