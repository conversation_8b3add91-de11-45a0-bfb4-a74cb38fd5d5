package com.get.institutioncenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.entity.ContractFormulaInstitutionFaculty;
import com.get.institutioncenter.dto.ContractFormulaInstitutionFacultyDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/26 14:59
 * @verison: 1.0
 * @description:
 */
public interface IContractFormulaInstitutionFacultyService extends BaseService<ContractFormulaInstitutionFaculty> {

    /**
     * @return java.lang.Long
     * @Description :新增
     * @Param [contractFormulaInstitutionFacultyDto]
     * <AUTHOR>
     */
    Long addContractFormulaInstitutionFaculty(ContractFormulaInstitutionFacultyDto contractFormulaInstitutionFacultyDto);

    /**
     * @return void
     * @Description :
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    void deleteByFkid(Long contractFormulaId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过合同公式id 查找对应学院ids
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<Long> getFacultyIdListByFkid(Long contractFormulaId);
}
