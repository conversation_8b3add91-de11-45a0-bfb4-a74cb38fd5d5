package com.get.partnercenter.util;


import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class TencentCloudUtils {

    //图片存储地址-公开桶
    @Value("${file.tencentcloudimage.bucketname}")
    private String imageBucketName;

    public   String getTencentCloudUrl(){

        String baseurl="https://hti-ais-images-dev-1301376564.cos.ap-shanghai.myqcloud.com";

        String results=baseurl;

        String tmpName="hti-ais-images-dev-1301376564";
        if(imageBucketName!=null && !"".equals(imageBucketName) && baseurl.indexOf(tmpName)!=-1){
            results=results.replace(tmpName,imageBucketName);
        }
        return results;
    }
}

