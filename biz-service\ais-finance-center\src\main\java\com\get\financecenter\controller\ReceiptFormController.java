package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.CommonUtil;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.financecenter.dto.*;
import com.get.financecenter.vo.*;
import com.get.financecenter.service.IReceiptFormService;
import com.get.financecenter.dto.query.ReceiptFormQueryDto;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/12/23
 * @TIME: 11:44
 * @Description:
 **/
@Api(tags = "收款单管理")
@RestController
@RequestMapping("finance/receiptForm")
public class ReceiptFormController {
    @Resource
    private IReceiptFormService receiptFormService;

    /**
     * 列表数据
     *
     * @param page
     * @return
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/收款管理/查询收款单")
    @PostMapping("datas")
    public ResponseBo<ReceiptFormVo> datas(@RequestBody SearchBean<ReceiptFormQueryDto> page) {
        List<ReceiptFormVo> datas = receiptFormService.getReceiptForms(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 修改收款单结算状态
     * @param receiptFormId
     * @param
     * @return
     */
    @ApiOperation(value = "修改收款单结算状态", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/收款管理/修改收款单结算状态")
    @PostMapping("updateSettlementStatus")
    public SaveResponseBo updateSettlementStatus(@RequestParam("receiptFormId") Long receiptFormId) {
        return receiptFormService.updateSettlementStatus(receiptFormId);
    }

    /**
     * @Description: 导出收款大单列表
     * @Author: Jerry
     * @Date:16:13 2021/12/20
     */
    @ApiOperation(value = "导出收款大单列表", notes = "")
    @PostMapping("/exportReceiptFormExcel")
    @ResponseBody
    public void exportReceiptFormExcel(HttpServletResponse response, @RequestBody ReceiptFormQueryDto receiptFormVo) {
        CommonUtil.ok(response);
        receiptFormService.exportReceiptFormExcel(response, receiptFormVo);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.BankAccountDto>
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     **/
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/收款管理/收款单详情")
    @GetMapping("/{id}")
    public ResponseBo<ReceiptFormVo> detail(@PathVariable("id") Long id) {
        ReceiptFormVo data = receiptFormService.findReceiptFormById(id);
        return new ResponseBo<>(data);
    }

    @ApiOperation(value = "获取实收汇率", notes = "")
    @VerifyPermission(IsVerify = false)
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/收款管理/获取实收汇率")
    @PostMapping("/getThePaidInExchangeRate")
    public ResponseBo<BigDecimal> obtainThePaidInExchangeRate(@RequestBody @Validated ReceiptFormExchangeRateDto receiptFormExchangeRateDto) {
        return receiptFormService.obtainThePaidInExchangeRate(receiptFormExchangeRateDto);
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [bankAccountVos]
     * <AUTHOR>
     **/
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/收款管理/新增收款单")
    @PostMapping("add")
    public ResponseBo add(@RequestBody  @Validated(ReceiptFormDto.Add.class)  ReceiptFormDto receiptFormDto) {
        return SaveResponseBo.ok(receiptFormService.addReceiptForm(receiptFormDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.ReceiptFormVo>
     * @Description: 修改信息
     * @Param [receiptFormVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/收款单管理/更新")
    @PostMapping("update")
    public ResponseBo<ReceiptFormVo> update(@RequestBody @Validated(ReceiptFormDto.Update.class) ReceiptFormDto receiptFormDto) {
        return UpdateResponseBo.ok(receiptFormService.updateReceiptForm(receiptFormDto));
    }

    /**
     * 修改付款单中的付款银行和付款日期
     *
     * @param receiptFormPortionVo 修改参数
     * @return
     */
    @ApiOperation(value = "修改接口", notes = "留学服务费中修改付款单中的付款银行和付款日期")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/收款单管理/更新（留学服务费中修改付款单中的付款银行和付款日期）")
    @PostMapping("updatePortionReceiptForm")
    public UpdateResponseBo updatePortionReceiptForm(@RequestBody @Validated ReceiptFormPortionDto receiptFormPortionVo) {
        return UpdateResponseBo.ok(receiptFormService.updatePortionReceiptForm(receiptFormPortionVo));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 作废接口
     * @Param [id]
     * <AUTHOR>
     **/
    @ApiOperation(value = "作废接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/付款单管理/作废")
    @GetMapping("updateStatus/{id}")
    public ResponseBo updateStatus(@PathVariable("id") Long id) {
        receiptFormService.updateStatus(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<InvoiceDto>
     * @Description: 发票列表数据
     * @Param [page]
     * <AUTHOR>
     **/
    @ApiOperation(value = "发票列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/收款管理/查询发票")
    @PostMapping("getInvoices")
    public ResponseBo<InvoiceVo> getInvoices(@RequestBody SearchBean<ReceiptFormDto> page) {
        List<InvoiceVo> datas = receiptFormService.getInvoices(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.MediaAndAttachedDto>
     * @Description: 附件列表
     * @Param [voSearchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "附件列表", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/收款管理/查询附件")
    @PostMapping("getMedia")
    public ResponseBo<FMediaAndAttachedVo> getMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<FMediaAndAttachedVo> staffMedia = receiptFormService.getMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.MediaAndAttachedDto>
     * @Description: 附件保存
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "附件保存")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/收款管理/附件保存接口")
    @PostMapping("upload")
    public ResponseBo<FMediaAndAttachedVo> addMedia(@RequestBody   @Validated(MediaAndAttachedDto.Add.class) ValidList<MediaAndAttachedDto> mediaAndAttachedDtoList) {
        return new ListResponseBo<>(receiptFormService.addMedia(mediaAndAttachedDtoList));
    }

    /**
     * 目标类型下拉框数据
     *
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "目标类型下拉", notes = "")
    @GetMapping("findTypeKeySelect")
    public ResponseBo findTargetType() {
        List<Map<String, Object>> datas = receiptFormService.findTypeKeySelect();
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 目标对象下拉
     * @Param [tableName, companyId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "目标对象下拉", notes = "")
    @PostMapping("findTypeTargetSelect")
    public ResponseBo<BaseSelectEntity> findTypeTargetSelect(@RequestParam(value = "tableName") String tableName, @RequestParam(value = "companyId") Long companyId) {
        return new ListResponseBo<>(receiptFormService.findTypeTargetSelect(tableName, companyId));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description : 编辑评论
     * @Param [commentVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑评论")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/收款单管理/编辑评论")
    @PostMapping("editComment")
    public ResponseBo editComment(@RequestBody @Validated(CommentDto.Add.class)  CommentDto commentDto) {
        return SaveResponseBo.ok(receiptFormService.editComment(commentDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AgentContractVo>
     * @Description : 评论列表数据
     * @Param [searchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "评论列表数据")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/收款单管理/查询评论")
    @PostMapping("getComments")
    public ResponseBo<FCommentVo> getComment(@RequestBody SearchBean<CommentDto> searchBean) {
        List<FCommentVo> datas = receiptFormService.getComments(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * @return com.get.financecenter.vo.ReceiptFormVo
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     **/
    @ApiIgnore
    @GetMapping("/getReceiptFormByFormId/{id}")
    public ReceiptFormVo getReceiptFormByFormId(@PathVariable("id") Long id) {
        return receiptFormService.getReceiptFormByFormId(id);
    }


}
