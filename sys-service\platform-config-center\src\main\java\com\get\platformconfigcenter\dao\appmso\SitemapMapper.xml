<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.platformconfigcenter.dao.appmso.SitemapMapper">
  <!-- <insert id="insert" parameterType="com.get.platformconfigcenter.entity.Sitemap">
   insert into m_sitemap (id, fk_parent_sitemap_id, menu_name,
     menu_key, menu_link_type, fk_area_country_id,
     fk_info_type_id, menu_link_entrance,menu_link_params,menu_link_description, web_title,
     web_meta_description, web_meta_keywords, view_order,
     gmt_create, gmt_create_user, gmt_modified,
     gmt_modified_user)
   values (#{id,jdbcType=BIGINT}, #{fkParentSitemapId,jdbcType=BIGINT}, #{menuName,jdbcType=VARCHAR},
     #{menuKey,jdbcType=VARCHAR}, #{menuLinkType,jdbcType=VARCHAR}, #{fkAreaCountryId,jdbcType=BIGINT},
     #{fkInfoTypeId,jdbcType=BIGINT}, #{menuLinkEntrance,jdbcType=VARCHAR},
      #{menuLinkParams,jdbcType=VARCHAR}, #{menuLinkDescription,jdbcType=VARCHAR}
      #{webTitle,jdbcType=VARCHAR},
     #{webMetaDescription,jdbcType=VARCHAR}, #{webMetaKeywords,jdbcType=VARCHAR}, #{viewOrder,jdbcType=INTEGER},
     #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP},
     #{gmtModifiedUser,jdbcType=VARCHAR})
 </insert>
<insert id="insertSelective" parameterType="com.get.platformconfigcenter.entity.Sitemap" keyProperty="id" useGeneratedKeys="true">
   insert into m_sitemap
   <trim prefix="(" suffix=")" suffixOverrides=",">
     <if test="id != null">
       id,
     </if>
     <if test="fkParentSitemapId != null">
       fk_parent_sitemap_id,
     </if>
     <if test="menuName != null">
       menu_name,
     </if>
     <if test="menuKey != null">
       menu_key,
     </if>
     <if test="menuLinkType != null">
       menu_link_type,
     </if>
     <if test="fkAreaCountryId != null">
       fk_area_country_id,
     </if>
     <if test="fkInfoTypeId != null">
       fk_info_type_id,
     </if>
     <if test="menuLinkEntrance != null">
       menu_link_entrance,
     </if>
     <if test="menuLinkParams != null">
       menu_link_params,
     </if>
     <if test="menuLinkDescription != null">
       menu_link_description,
     </if>
     <if test="webTitle != null">
       web_title,
     </if>
     <if test="webMetaDescription != null">
       web_meta_description,
     </if>
     <if test="webMetaKeywords != null">
       web_meta_keywords,
     </if>
     <if test="viewOrder != null">
       view_order,
     </if>
     <if test="gmtCreate != null">
       gmt_create,
     </if>
     <if test="gmtCreateUser != null">
       gmt_create_user,
     </if>
     <if test="gmtModified != null">
       gmt_modified,
     </if>
     <if test="gmtModifiedUser != null">
       gmt_modified_user,
     </if>
     <if test="effectiveStartTime != null">
       effective_start_time,
     </if>
     <if test="effectiveEndTime !=null"  >
       effective_end_time,
     </if>

   </trim>
   <trim prefix="values (" suffix=")" suffixOverrides=",">
     <if test="id != null">
       #{id,jdbcType=BIGINT},
     </if>
     <if test="fkParentSitemapId != null">
       #{fkParentSitemapId,jdbcType=BIGINT},
     </if>
     <if test="menuName != null">
       #{menuName,jdbcType=VARCHAR},
     </if>
     <if test="menuKey != null">
       #{menuKey,jdbcType=VARCHAR},
     </if>
     <if test="menuLinkType != null">
       #{menuLinkType,jdbcType=VARCHAR},
     </if>
     <if test="fkAreaCountryId != null">
       #{fkAreaCountryId,jdbcType=BIGINT},
     </if>
     <if test="fkInfoTypeId != null">
       #{fkInfoTypeId,jdbcType=BIGINT},
     </if>
     <if test="menuLinkEntrance != null">
       #{menuLinkEntrance,jdbcType=VARCHAR},
     </if>
     <if test="menuLinkParams != null">
       #{menuLinkParams,jdbcType=VARCHAR},
     </if>
     <if test="menuLinkDescription != null">
       #{menuLinkDescription,jdbcType=VARCHAR},
     </if>
     <if test="webTitle != null">
       #{webTitle,jdbcType=VARCHAR},
     </if>
     <if test="webMetaDescription != null">
       #{webMetaDescription,jdbcType=VARCHAR},
     </if>
     <if test="webMetaKeywords != null">
       #{webMetaKeywords,jdbcType=VARCHAR},
     </if>
     <if test="viewOrder != null">
       #{viewOrder,jdbcType=INTEGER},
     </if>
     <if test="gmtCreate != null">
       #{gmtCreate,jdbcType=TIMESTAMP},
     </if>
     <if test="gmtCreateUser != null">
       #{gmtCreateUser,jdbcType=VARCHAR},
     </if>
     <if test="gmtModified != null">
       #{gmtModified,jdbcType=TIMESTAMP},
     </if>
     <if test="gmtModifiedUser != null">
       #{gmtModifiedUser,jdbcType=VARCHAR},
     </if>
     <if test="effectiveStartTime !=null">
       #{effectiveStartTime,jdbcType=TIMESTAMP},
     </if>
     <if test="effectiveEndTime !=null">
       #{effectiveEndTime,jdbcType=TIMESTAMP},
     </if>
   </trim>
 </insert>
 <update id="updateByPrimaryKeySelective" parameterType="com.get.platformconfigcenter.entity.Sitemap">
   update m_sitemap
   <set>
     <if test="fkParentSitemapId != null">
       fk_parent_sitemap_id = #{fkParentSitemapId,jdbcType=BIGINT},
     </if>
     <if test="menuName != null">
       menu_name = #{menuName,jdbcType=VARCHAR},
     </if>
     <if test="menuKey != null">
       menu_key = #{menuKey,jdbcType=VARCHAR},
     </if>
     <if test="menuLinkType != null">
       menu_link_type = #{menuLinkType,jdbcType=VARCHAR},
     </if>
     <if test="fkAreaCountryId != null">
       fk_area_country_id = #{fkAreaCountryId,jdbcType=BIGINT},
     </if>
     <if test="fkInfoTypeId != null">
       fk_info_type_id = #{fkInfoTypeId,jdbcType=BIGINT},
     </if>
     <if test="menuLinkEntrance != null">
       menu_link_entrance = #{menuLinkEntrance,jdbcType=VARCHAR},
     </if>
     <if test="menuLinkParams != null">
       menu_link_params = #{menuLinkParams,jdbcType=VARCHAR},
     </if>
     <if test="menuLinkDescription != null">
       menu_link_description = #{menuLinkDescription,jdbcType=VARCHAR},
     </if>
     <if test="webTitle != null">
       web_title = #{webTitle,jdbcType=VARCHAR},
     </if>
     <if test="webMetaDescription != null">
       web_meta_description = #{webMetaDescription,jdbcType=VARCHAR},
     </if>
     <if test="webMetaKeywords != null">
       web_meta_keywords = #{webMetaKeywords,jdbcType=VARCHAR},
     </if>
     <if test="viewOrder != null">
       view_order = #{viewOrder,jdbcType=INTEGER},
     </if>
     <if test="gmtCreate != null">
       gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
     </if>
     <if test="gmtCreateUser != null">
       gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
     </if>
     <if test="gmtModified != null">
       gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
     </if>
     <if test="gmtModifiedUser != null">
       gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
     </if>
   </set>
   where id = #{id,jdbcType=BIGINT}
 </update>
 <update id="updateByPrimaryKey" parameterType="com.get.platformconfigcenter.entity.Sitemap">
   update m_sitemap
   set fk_parent_sitemap_id = #{fkParentSitemapId,jdbcType=BIGINT},
     menu_name = #{menuName,jdbcType=VARCHAR},
     menu_key = #{menuKey,jdbcType=VARCHAR},
     menu_link_type = #{menuLinkType,jdbcType=VARCHAR},
     fk_area_country_id = #{fkAreaCountryId,jdbcType=BIGINT},
     fk_info_type_id = #{fkInfoTypeId,jdbcType=BIGINT},
     menu_link_entrance = #{menuLinkEntrance,jdbcType=VARCHAR},
     menu_link_params = #{menuLinkParams,jdbcType=VARCHAR},
     menu_link_description = #{menuLinkDescription,jdbcType=VARCHAR},
     web_title = #{webTitle,jdbcType=VARCHAR},
     web_meta_description = #{webMetaDescription,jdbcType=VARCHAR},
     web_meta_keywords = #{webMetaKeywords,jdbcType=VARCHAR},
     view_order = #{viewOrder,jdbcType=INTEGER},
     gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
     gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
     gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
     gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
     effective_start_time = #{effectiveStartTime,jdbcType=TIMESTAMP},
     effective_end_time = #{effectiveEndTime,jdbcType=TIMESTAMP}
   where id = #{id,jdbcType=BIGINT}
 </update>
 <select id="getChildSitemap" resultType="com.get.platformconfigcenter.dto.SitemapVo">
     SELECT
     id AS id,
     fk_parent_sitemap_id AS fkParentSitemapId,
     menu_name AS menuName,
     menu_key AS menuKey,
     menu_link_type AS menuLinkType,
     fk_area_country_id AS fkAreaCountryId,
     fk_info_type_id AS fkInfoTypeId,
     menu_link_entrance AS menuLinkEntrance,
     menu_link_params AS menuLinkParams,
     menu_link_description AS menuLinkDescription,
     web_title AS webTitle,
     web_meta_description AS webMetaDescription,
     web_meta_keywords AS webMetaKeywords,
     view_order AS viewOrder,
     gmt_create AS gmtCreate,
     gmt_create_user AS gmtCreateUser,
     gmt_modified AS gmtModified,
     gmt_modified_user AS gmtModifiedUser
     FROM
     m_sitemap
     WHERE fk_parent_sitemap_id = #{sitemapId} order by view_order desc
   </select>

 <select id="getMaxViewOrder" resultType="java.lang.Integer">
   select
     IFNULL(max(view_order)+1,0) view_order
   from
     m_sitemap

 </select>-->
</mapper>