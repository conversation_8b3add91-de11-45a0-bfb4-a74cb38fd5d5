package com.get.partnercenter.enums;


public enum TypeEnum {
    COMMISSION_LABEL_INCENTIVE(100l, "奖励"),
    COMMISSION_LABEL_NONE(101l, "让利"),
    COMMISSION_LABEL_OTHER(102l, "其他"),

    /**
     * 学历情况备注-项目说明
     */
    THREE_ONE(0, "3+2"),
    TWO_TWO(1, "2+2"),
    FOUR_ZERO(2, "4+0"),
    EXCHANGE_STUDENTS(3, "交换生"),
    /**
     * 学历情况备注-学位情况
     */
    DOUBLE_DEGREE(0, "获得双学位"),
    INTERNATIONAL_DEGREE(1, "获得国际学位"),
    DOMESTIC_DEGREE(2, "获得国内学位"),
    //英语成绩类型学术
    IELTS(1, "IELTS"),
    TOEFL_IBT(2, "TOEFL-IBT"),
    TOEFL_PBT(3, "TOEFL-PBT"),
    PTE(4, "PTE"),
    HKDSE_ENG(5, "HKDSE-ENG"),
    CET(6, "CET"),
    DUOLINGO(7, "DUOLINGO"),
    BEC(8, "BEC"),
    LANGUAGE_CERT(9,"LanguageCert"),
    SCHOOL_INTERNAL_TEST(10, "UniversityTest"),

    //高中成绩枚举部分
    NCEE(1, "National College Entrance Exam (USA)"),
    HKDSE(2, "HKDSE (Hong Kong)"),
    ALEVEL(3, "GCE A Level"),
    IB(4, "IB Diploma"),
    MSS(5, "Middle School Score"),
    UG(6, "UG"),
    OLEVEL(101, "GCE O Level"),
    OSSD(102, "Ontario Secondary School Diploma"),
    NCUK(103, "NCUK"),
    ATAR(104, "Australian Tertiary Amission Rank"),
    GAOKAO(105, "GaoKao (China)"),
    SAT1(106, "SAT1 (USA)"),
    SAT2(118, "SAT2 (USA)"),
    ACT(119, "ACT (USA)"),
    AD(107, "Advanced Diploma (Sinfgapore)"),
    STPM(108, "STPM (Malaysia)"),
    MUFY(109, "Monash University Foundation year (Malaysia)"),
    PM(110, "Program Matrikulasi (Malaysia)"),
    UEC(111, "UEC (Malaysia)"),
    ISCE(112, "Indian School Certificate Examination (Malaysia)"),
    DS(113, "Diploma Studies (Mapaysia)"),
    SPM(120,"SPM(Mapaysia)"),
    SMA3(114, "SMA3 (Indonesia)"),
    SMA2(115, "SMA2 (Indonesia)"),
    MATAYOM6(116, "Matayom 6 (Thailand)"),
    NE(117, "National Exam (Vietnam)"),
    AEAS(121, "Australian Education Assessment Services"),
    QCE(122, "Queensland Certificate of Education"),
    QTAC(123, "Queensland Tertiary Admissions Centre"),
    SACE(124, "South Australian Certificate of Education"),
    GCE(125, "GCE"),
    GCSE(126, "GCSE"),
    IGCSE(127, "IGCSE"),
    HKAL(128,"HK A LEVEL (Hong Kong)"),
    HKAS(129,"HK AS LEVEL (Hong Kong)"),
    HKCEE(130,"HKCEE (Hong Kong)"),
    NCAE(131,"NCAE (China)"),
    AP(132,"Advanced Placement"),
    //标准成绩类型本科
    CLASS(11, "Class"),
    GPA(21, "GPA"),
    GPA4(12, "GPA(out of 4 points)"),
    GPA4_2(13, "GPA(out of 4.2)"),
    GPA4_3(23, "GPA(out of 4.3)"),
    GPA4_5(14, "GPA(out of 4.5)"),
    GPA5(15, "GPA(out of 5 points)"),
    GPA7(16, "GPA(out of 7 points)"),
    GPA9(17, "GPA(out of 9 points)"),
    GPA10(22, "GPA(out of 10 points)"),
    GPA12(26, "GPA(out of 12 points)"),
    PERCENTAGE(18, "Percentage"),
    GRADING(19, "Grading"),
    GU(27, "GU"),
    GRE(24, "GRE"),
    GMAT(25, "GMAT"),
    OTHER_REQUIREMENTS(20, "other requirements")
    ;

    public static final TypeEnum[] COMMISSION_LABEL = new TypeEnum[]{COMMISSION_LABEL_INCENTIVE,COMMISSION_LABEL_NONE,COMMISSION_LABEL_OTHER};
    /**
     * 学历情况备注-项目说明
     */
    public static final TypeEnum[] EDUCATION_PROJECT = new TypeEnum[]{THREE_ONE, TWO_TWO, FOUR_ZERO, EXCHANGE_STUDENTS};
    /**
     * 学历情况备注-学位情况
     */
    public static final TypeEnum[] EDUCATION_DEGREE = new TypeEnum[]{DOUBLE_DEGREE, INTERNATIONAL_DEGREE, DOMESTIC_DEGREE};

    //高中成绩枚举,学历背景下拉
    public static final TypeEnum[] HIGH_SCHOOL_GRADES = new TypeEnum[]{
            MSS,IB,AP,ALEVEL,OLEVEL,GCE,GCSE,IGCSE,NCUK,UG,ATAR,AEAS,QCE,QTAC,SACE,GAOKAO,
            NCAE,HKDSE,HKAL,HKAS,HKCEE,OSSD,NCEE,SAT1,SAT2,ACT,AD,STPM,MUFY,PM,UEC,ISCE,DS,SPM,SMA3,SMA2,MATAYOM6,NE
    };
    //本科成绩枚举,学历背景下拉
    public static final TypeEnum[] UNDERGRADUATE_ACHIEVEMENT = new TypeEnum[]{
            CLASS, GPA, GPA4, GPA4_2, GPA4_3, GPA4_5, GPA5, GPA7, GPA9, GPA10,GPA12,GRE,
            GMAT, PERCENTAGE, GRADING, GU, OTHER_REQUIREMENTS
    };

    //英语子表成绩类型枚举,课程子表下拉
    public static final TypeEnum[] English_subtype = new TypeEnum[]{
            IELTS, TOEFL_IBT, TOEFL_PBT, PTE, HKDSE_ENG, CET, DUOLINGO, BEC , LANGUAGE_CERT

    };



    public long key;
    public String value;

    TypeEnum(long key, String value) {
        this.key = key;
        this.value = value;
    }





    public static String getBaseCombox(String key,TypeEnum[] enums) {
        String value="";
        for(TypeEnum enumItem : enums) {
            Long keytmp=enumItem.key;
            if(key.equals(keytmp.toString())) {
                value=enumItem.value;
                break;
            }

        }
        return value;
    }






}
