package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/12/29
 * @TIME: 16:44
 * @Description:
 **/
@Data
public class ResumeAttachmentVo extends BaseVoEntity {
    @ApiModelProperty(value = "附件")
    List<MediaAndAttachedVo> mediaAndAttacheds;
    /**
     * 所属简历Id
     */
    @ApiModelProperty(value = "所属简历Id", required = true)
    @NotNull(message = "所属简历Id不能为空", groups = {Add.class, Update.class})
    private Long fkResumeId;
    /**
     * 上传方式：0上传附件/1附件链接
     */
    @ApiModelProperty(value = "上传方式：0上传附件/1附件链接", required = true)
    @NotNull(message = "上传方式不能为空", groups = {Add.class, Update.class})
    private Integer mode;
    /**
     * 附件链接
     */
    @ApiModelProperty(value = "附件链接")
    private String link;
    /**
     * 附件名称
     */
    @ApiModelProperty(value = "附件名称")
    private String name;
    /**
     * 附件描述
     */
    @ApiModelProperty(value = "附件描述")
    private String description;

}
