package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Hardy
 * @create: 2021/12/6 10:17
 * @verison: 1.0
 * @description:
 */
@Data
public class AttendeesDto {

    @ApiModelProperty("参会人员id")
    private Long id;

    @ApiModelProperty("性别")
    private Integer gender;

//    @ApiModelProperty("姓")
//    private String firstNameChinese;
//
//    @ApiModelProperty("名")
//    private String lastNameChinese;
//
//    @ApiModelProperty("英文姓")
//    private String firstNameCapitals;
//
//    @ApiModelProperty("英文名")
//    private String lastNameCapitals;

    @ApiModelProperty(value = "中文名")
    private String chineseName;

    @ApiModelProperty(value = "英文名")
    private String englishName;

    @ApiModelProperty("职位")
    private String position;

    @ApiModelProperty("电话")
    private String tel;

    @ApiModelProperty("邮箱")
    private String email;
}
