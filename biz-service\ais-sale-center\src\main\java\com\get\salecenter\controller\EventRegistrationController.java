package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.institutioncenter.dto.InstitutionProviderDto;
import com.get.institutioncenter.vo.InstitutionProviderVo;
import com.get.salecenter.service.IEventRegistrationService;
import com.get.salecenter.dto.EventRegistrationDto;
import com.get.salecenter.dto.EventRegistrationStatusDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/9/13 16:22
 * @verison: 1.0
 * @description: 提供商报名名册
 */
@Api(tags = "提供商报名名册管理")
@RestController
@RequestMapping("sale/eventRegistration")
public class EventRegistrationController {

    @Resource
    private IEventRegistrationService eventRegistrationService;


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventCostVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/提供商报名名册管理/列表")
    @PostMapping("datas")
    public ResponseBo<InstitutionProviderVo> datas(@RequestBody SearchBean<EventRegistrationDto> page) {
        List<InstitutionProviderVo> datas = eventRegistrationService.getEventRegistrations(page.getData(),page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas,p);
    }

    @ApiOperation(value = "编辑备注", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/提供商报名名册管理/编辑备注")
    @PostMapping("editRemark")
    public ResponseBo editRemark(@RequestParam("id") Long id,@RequestParam("remark") String remark){
        return eventRegistrationService.editRemark(id , remark);
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/提供商报名名册管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        eventRegistrationService.delete(id);
        return DeleteResponseBo.ok();
    }



    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增信息
     * @Param [eventCostVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "添加报名", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/提供商报名名册管理/添加报名")
    @PostMapping("editEventRegistration")
    public ResponseBo editEventRegistration(@RequestBody @Validated(EventRegistrationDto.Add.class) ValidList<EventRegistrationDto> eventRegistrationDtos) {
        eventRegistrationService.editEventRegistration(eventRegistrationDtos);
        return SaveResponseBo.ok();
    }

    @ApiModelProperty(value = "批量修改活动名册状态")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/提供商报名名册管理/批量修改活动名册状态")
    @PostMapping("editEventRegistrationStatus")
    public ResponseBo editEventRegistrationStatus(@RequestBody @Validated(EventRegistrationStatusDto.Add.class) EventRegistrationStatusDto eventRegistrationStatusDto){
        eventRegistrationService.editEventRegistrationStatus(eventRegistrationStatusDto);
        return UpdateResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增信息
     * @Param [eventCostVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "获取分配提供商列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/提供商报名名册管理/获取分配提供商列表")
    @PostMapping("getEventRegistrationProviders")
    public ResponseBo<InstitutionProviderVo> getEventRegistrationProviders(@RequestBody SearchBean<InstitutionProviderDto> page) {
        return eventRegistrationService.getEventRegistrationProviders(page);
    }

}
