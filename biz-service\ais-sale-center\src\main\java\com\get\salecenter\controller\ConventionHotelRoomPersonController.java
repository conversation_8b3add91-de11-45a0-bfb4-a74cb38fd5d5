package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.ConventionHotelRoomListVo;
import com.get.salecenter.vo.ConventionHotelRoomPersonVo;
import com.get.salecenter.vo.ConventionPersonVo;
import com.get.salecenter.vo.LiveDateVo;
import com.get.salecenter.service.IConventionHotelRoomPersonService;
import com.get.salecenter.dto.ConventionHotelRoomDto;
import com.get.salecenter.dto.ConventionHotelRoomPersonDto;
import com.get.salecenter.dto.ConventionPersonDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/26 9:57
 * @verison: 1.0
 * @description:
 */
@Api(tags = "酒店住房安排管理")
@RestController
@RequestMapping("sale/ConventionHotelRoomPerson")
public class ConventionHotelRoomPersonController {

    @Resource
    private IConventionHotelRoomPersonService conventionHotelRoomPersonService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "床位配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/酒店住房安排管理/床位详情")
    @GetMapping("/{id}")
    public ResponseBo<ConventionHotelRoomPersonVo> detail(@PathVariable("id") Long id) {
        ConventionHotelRoomPersonVo data = conventionHotelRoomPersonService.findconventionHotelRoomPersonById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 配置床位
     *
     * @param conventionHotelRoomPersonDtoList
     * @return
     * @
     */
    @ApiOperation(value = "配置床位", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/酒店住房安排管理/配置床位")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(ConventionHotelRoomPersonDto.Add.class)  ValidList<ConventionHotelRoomPersonDto> conventionHotelRoomPersonDtoList) {
        conventionHotelRoomPersonService.configurationBeds(conventionHotelRoomPersonDtoList);
        return SaveResponseBo.ok();
    }

    /**
     * 删除床位
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除床位", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/酒店住房安排管理/删除床位")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        conventionHotelRoomPersonService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改床位
     *
     * @param conventionHotelRoomPersonDtoList
     * @return
     * @
     */
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "修改床位", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/酒店住房安排管理/修改床位")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated(ConventionHotelRoomPersonDto.Update.class) ValidList<ConventionHotelRoomPersonDto> conventionHotelRoomPersonDtoList) {
        for (ConventionHotelRoomPersonDto conventionHotelRoomPersonDto : conventionHotelRoomPersonDtoList) {
            conventionHotelRoomPersonService.update(conventionHotelRoomPersonDto);
        }
        return UpdateResponseBo.ok();
    }

    /**
     * 列表数据
     *
     * @param conventionHotelRoomDto
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "hotel酒店名称 roomType房型（输入酒店名称后调用房型下拉框才能选择） roomNum房号 conventionId峰会id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/酒店住房安排管理/查询酒店住房安排")
    @PostMapping("datas")
    public ResponseBo datas(@RequestBody ConventionHotelRoomDto conventionHotelRoomDto) {
        ListResponseBo<Object> responseBo = new ListResponseBo<>();
        //获取房间数据集合
        List<ConventionHotelRoomListVo> datas = conventionHotelRoomPersonService.getHotelRoomsAndPersons(conventionHotelRoomDto);
        //获取入住时间集合
        List<String> dates = conventionHotelRoomPersonService.getDates(conventionHotelRoomDto.getConventionId());
        responseBo.setDatas(datas);
        responseBo.setData(dates);
        return responseBo;
    }

    @ApiOperation(value = "导出酒店住房安排")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/酒店住房安排管理/导出酒店住房安排")
    @PostMapping("/exportConventionHotelRoomPersonExcel")
    public void exportConventionHotelRoomPersonExcel(HttpServletResponse response, @RequestBody ConventionHotelRoomDto conventionHotelRoomDto) {
        conventionHotelRoomPersonService.exportConventionHotelRoomPersonExcel(response, conventionHotelRoomDto);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ConventionPersonVo>
     * @Description :房间未安排人员详细
     * @Param [page, roomDate]
     * <AUTHOR>
     */
    @ApiOperation(value = "房间未安排人员详细", notes = "fkConventionId峰会id（必传）,type人员类型，nameKey人员姓名")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/酒店住房安排管理/房间未安排人员详细")
    @PostMapping("getRoomNotArrangedPersonList")
    public ResponseBo<ConventionPersonVo> getRoomNotArrangedPersonList(@RequestBody SearchBean<ConventionPersonDto> page, String roomDate) {
        List<ConventionPersonVo> datas = conventionHotelRoomPersonService.getRoomNotArrangedPersonList(page.getData(), page, roomDate);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :日期选择下拉框
     * @Param [conventionHotelRoomVo]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "日期选择下拉框", notes = "conventionId峰会id（必传）")
    @GetMapping("getDates")
    public ResponseBo getDates(@RequestParam("conventionId") Long conventionId) {
        List<String> datas = conventionHotelRoomPersonService.getDates(conventionId);
        return new ListResponseBo<>(datas);
    }


    /**
     * @return ResponseBo
     * @Description :批量移除住房人员
     * @Param [conventionPersonId]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量移除住房人员", notes = "conventionPersonId（必传），conventionHotelId（必传），systemRoomNum（必传）")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/酒店住房安排管理/批量移除住房人员")
    @PostMapping("batchRemovePerson")
    public ResponseBo batchRemovePerson(@RequestParam("conventionPersonId") Long conventionPersonId, @RequestParam("conventionHotelId") Long conventionHotelId, @RequestParam("systemRoomNum") String systemRoomNum) {
        conventionHotelRoomPersonService.batchRemovePerson(conventionPersonId, conventionHotelId, systemRoomNum);
        return DeleteResponseBo.ok();
    }


    /**
     * @return ResponseBo
     * @Description :参会人签到
     * @Param
     * <AUTHOR>
     */
    @ApiOperation(value = "参会人员签到", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/酒店住房安排管理/参会人员签到")
    @PostMapping("getPersonSign")
    public ResponseBo getPersonSign(@RequestBody List<ConventionHotelRoomPersonDto> conventionHotelRoomPersonDtoList) {
        conventionHotelRoomPersonService.getPersonSign(conventionHotelRoomPersonDtoList);
        return ResponseBo.ok();
    }

    /**
     * @return ResponseBo
     * @Description :可住房日期列表
     * @Param
     * <AUTHOR>
     */
    @ApiOperation(value = "可住房日期列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/酒店住房安排管理/可住房日期列表")
    @PostMapping("getUsableDateList")
    public ResponseBo getUsableDateList(@RequestBody ConventionHotelRoomDto conventionHotelRoomDto) {
        List<LiveDateVo> liveDateVoList = conventionHotelRoomPersonService.getUsableDateList(conventionHotelRoomDto);
        return new ListResponseBo<>(liveDateVoList);
    }


    /**
     * @return ResponseBo
     * @Description :获取签到人员信息
     * @Param
     * <AUTHOR>
     */
    @ApiOperation(value = "获取签到人员信息", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/酒店住房安排管理/获取签到人员信息")
    @PostMapping("getSignInfo")
    public ResponseBo getSignInfo(@RequestParam("conventionPersonId") Long conventionPersonId) {
        List<ConventionPersonVo> datas = conventionHotelRoomPersonService.getSignInfo(conventionPersonId);
        return new ListResponseBo<>(datas);
    }
}
