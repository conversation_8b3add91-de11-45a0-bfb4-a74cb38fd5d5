package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.institutioncenter.vo.InstitutionPathwayVo;
import com.get.institutioncenter.service.IInstitutionPathwayService;
import com.get.institutioncenter.dto.InstitutionPathwayDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/12/18
 * @TIME: 9:32
 * @Description:
 **/
@Api(tags = "桥梁学校管理")
@RestController
@RequestMapping("/institution/institutionPathway")
public class InstitutionPathwayController {
    @Resource
    private IInstitutionPathwayService institutionPathwayService;

    /**
     * 修改信息
     *
     * @param institutionPathwayDto
     * @return
     * @
     */
    @ApiOperation(value = "绑定桥梁学校", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/桥梁学校管理/绑定桥梁学校")
    @PostMapping("update")
    public ResponseBo update(@RequestBody InstitutionPathwayDto institutionPathwayDto) {
        institutionPathwayService.update(institutionPathwayDto);
        return ResponseBo.ok();
    }

    /**
     * 桥梁绑定非桥梁学校
     *
     * @Date 16:44 2021/7/22
     * <AUTHOR>
     */
    @ApiOperation(value = "桥梁绑定非桥梁学校", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/桥梁学校管理/桥梁绑定非桥梁学校")
    @PostMapping("pathwayUpdate")
    public ResponseBo pathwayUpdate(@RequestBody InstitutionPathwayDto institutionPathwayDto) {
        institutionPathwayService.pathwayUpdate(institutionPathwayDto);
        return ResponseBo.ok();
    }


    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "桥梁学校列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/桥梁学校管理/查询桥梁学校")
    @PostMapping("datas")
    public ResponseBo<InstitutionPathwayVo> datas(@RequestBody SearchBean<InstitutionPathwayDto> page) {
        List<InstitutionPathwayVo> datas = institutionPathwayService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @param id
     * @return
     * @
     */

    @ApiOperation(value = "删除桥梁学校")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/桥梁学校管理/删除桥梁学校")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        institutionPathwayService.delete(id);
        return ResponseBo.ok();
    }

}
