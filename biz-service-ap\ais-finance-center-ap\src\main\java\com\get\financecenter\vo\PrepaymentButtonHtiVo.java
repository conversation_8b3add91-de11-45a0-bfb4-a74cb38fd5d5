package com.get.financecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/6/14 17:45
 */
@Data
public class PrepaymentButtonHtiVo {

    @ApiModelProperty(value = "预付金额百分比")
    private BigDecimal percentage;

    @ApiModelProperty(value = "预付金额")
    private BigDecimal payInAdvanceAmount;

    @ApiModelProperty(value = "发票应收计划绑定关系id")
    private Long invoiceReceivablePlanRelationId;

}
