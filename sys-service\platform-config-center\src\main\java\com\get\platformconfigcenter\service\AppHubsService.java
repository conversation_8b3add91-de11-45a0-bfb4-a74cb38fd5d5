package com.get.platformconfigcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.SearchBean;
import com.get.platformconfigcenter.vo.HubsUserVo;
import com.get.platformconfigcenter.vo.SubmitOrderVo;
import com.get.platformconfigcenter.entity.CountryButler;
import com.get.platformconfigcenter.entity.SaleOrder;
import com.get.platformconfigcenter.dto.AppHubsSearchOrderDto;
import com.get.platformconfigcenter.dto.AppHubsUpdateOrderDto;

import java.util.List;
import java.util.Map;

/**
 * @Author: Eric
 * @Date: 2024/9/23
 * @Description:
 * @Version 1.0
 */
public interface AppHubsService extends IService<SaleOrder> {
    List<SubmitOrderVo> getAppHubsOrders(AppHubsSearchOrderDto data, SearchBean<AppHubsSearchOrderDto> searchBean);

    Map<String, Object> getAppHubsOrdersDetail(Long fkOrderId);

    void updateSalqOrder(List<AppHubsUpdateOrderDto> data);

    List<CountryButler> getAppHubsCountry();

    List<HubsUserVo> getRegisteredUser(AppHubsSearchOrderDto data, SearchBean<AppHubsSearchOrderDto> searchBean);
}
