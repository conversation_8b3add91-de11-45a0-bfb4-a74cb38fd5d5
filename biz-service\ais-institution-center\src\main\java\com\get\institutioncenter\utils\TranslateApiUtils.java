package com.get.institutioncenter.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * Author: Smail
 * Date: 6/5/2024
 */
@Component
public class TranslateApiUtils {

//    private static final String BASE_URL = "http://192.168.1.96:3001/v1/chat/completions";
    private static final String BASE_URL = "http://159.138.6.177:3001/v1/chat/completions";
    private static final String BEARER_TOKEN = "Bearer sk-wTJxKPNlwY6vgLJM53Fa981d0a204428BeA691F88eF3E6Ce";
    public static final String MODEL = "qwen2.5-32b";
    public static final String MODEL_PLUS = "qwen-plus";


    public static final String EN_USER = "You are an overseas study expert who is proficient in Chinese and English, Please help me translate some text into English without losing the information. Return it in plain text format and only return the translated results.";
    public static final String ZH_USER = "你是一个精通中英的海外留学专家，请你帮我翻译成中文，不能丢失信息，使用纯文本格式返回，只返回翻译后的结果。";
    public static final String ZH_COURSE_USER = "你是一个精通中英的海外留学专家，请你帮我翻译一些【课程】成中文，不能丢失信息，使用纯文本格式返回，只返回翻译后的结果。";

    /**
     * from 固定的
     * trans 翻译的内容
     * */
    public String getTrans(String from,String trans,String model) throws JsonProcessingException {
        CloseableHttpClient httpClient = HttpClients.createDefault();

        HttpPost httpPost = new HttpPost(BASE_URL);

        // 设置Authorization header
        httpPost.setHeader("Authorization", BEARER_TOKEN);

        // 设置请求体
        Map<String, Object> request = new HashMap<>();
        // 设置model
        request.put("model", model);
        // 创建messages列表
        List<Map<String, String>> messages = new ArrayList<>();
        // 添加system消息
        Map<String, String> systemMessage = new HashMap<>();
        systemMessage.put("role", "system");
        systemMessage.put("content", from);
        messages.add(systemMessage);

        // 添加user消息，
        Map<String, String> userMessage = new HashMap<>();
        userMessage.put("role", "user");

        String actualContent = "```text\\n" + trans + "\\n```";
        userMessage.put("content", actualContent);
        messages.add(userMessage);

        // 将messages列表添加到主Map中
        request.put("messages", messages);

        // 设置其他参数
        request.put("max_tokens", 2000);
        request.put("temperature", 0);
        request.put("top_p", 1);
        request.put("n", 1);
        request.put("stream", false);

        // 序列化Map为JSON字符串
        ObjectMapper objectMapper = new ObjectMapper();
        String requestBody = objectMapper.writeValueAsString(request);
        StringEntity entity = new StringEntity(requestBody, "UTF-8");
        httpPost.setEntity(entity);
        httpPost.setHeader("Content-type", "application/json");
        String content = null;
        try {
            // 发送请求并处理响应
            HttpResponse response = httpClient.execute(httpPost);
            HttpEntity responseEntity = response.getEntity();
            if (responseEntity != null) {
                String responseString = EntityUtils.toString(responseEntity, "UTF-8");
                JsonNode jsonNode = objectMapper.readTree(responseString);
                JsonNode choicesNode = jsonNode.get("choices");
                if (choicesNode != null && choicesNode.isArray() && !choicesNode.isEmpty()) {
                    JsonNode firstChoiceNode = choicesNode.get(0); // 获取第一个choices元素
                    JsonNode messageNode = firstChoiceNode.get("message"); // 获取message对象
                    if (messageNode != null) {
                         content = messageNode.get("content").asText(); // 获取content属性
                    }
                }

            }

        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException("Error processing API request", e);
        }

            return content;
    }
}