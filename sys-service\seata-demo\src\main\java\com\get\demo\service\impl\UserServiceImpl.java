package com.get.demo.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.demo.entity.User;
import com.get.demo.mapper.UserMapper;
import com.get.demo.service.IUserService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-07
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {

    @Override
    public Boolean updateByDemo() {
        User user = this.getById(1);
        this.update(Wrappers.<User>lambdaUpdate().set(User::getOrderNum, user.getOrderNum() + 1).eq(User::getId, 1));
//        if(1==1)
//        {
//            throw new GetServiceException("执行失败");
//        }
        return true;
    }
}
