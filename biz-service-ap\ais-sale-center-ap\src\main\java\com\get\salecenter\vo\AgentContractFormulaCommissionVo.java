package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.AgentContractFormulaCommission;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: Sea
 * @create: 2021/4/23 18:35
 * @verison: 1.0
 * @description:
 */
@Data
public class AgentContractFormulaCommissionVo extends BaseEntity implements Serializable {

    //===========实体类AgentContractFormulaCommission===================
    private static final long serialVersionUID = 1L;
    /**
     * 学生代理合同公式Id
     */
    @ApiModelProperty(value = "学生代理合同公式Id")
    @Column(name = "fk_agent_contract_formula_id")
    private Long fkAgentContractFormulaId;
    /**
     * 期数，从1开始，按顺序生成，并按顺序排序
     */
    @ApiModelProperty(value = "期数，从1开始，按顺序生成，并按顺序排序")
    @Column(name = "step")
    private Integer step;
    /**
     * 代理佣金比例
     */
    @ApiModelProperty(value = "代理佣金比例（学费比例）")
    @Column(name = "commission_rate_ag")
    private BigDecimal commissionRateAg;

    @ApiModelProperty(value = "代理佣金比例（应收比例）")
    @Column(name = "receivable_rate_ag")
    private BigDecimal receivableRateAg;

    /**
     * 代理固定金额
     */
    @ApiModelProperty(value = "代理固定金额")
    @Column(name = "fixed_amount_ag")
    private BigDecimal fixedAmountAg;
    /**
     * 代理佣金上限
     */
    @ApiModelProperty(value = "代理佣金上限")
    @Column(name = "limit_amount_ag")
    private BigDecimal limitAmountAg;
}
