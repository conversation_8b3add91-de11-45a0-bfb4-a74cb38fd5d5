package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("u_exchange_rate")
public class ExchangeRate extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 参照币种编号
     */
    @ApiModelProperty(value = "参照币种编号")
    private String fkCurrencyTypeNumFrom;
    /**
     * 目标币种编号
     */
    @ApiModelProperty(value = "目标币种编号")
    private String fkCurrencyTypeNumTo;
    /**
     * 获取日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "获取日期")
    private Date getDate;
    /**
     * 汇率
     */
    @ApiModelProperty(value = "汇率")
    private BigDecimal exchangeRate;


}