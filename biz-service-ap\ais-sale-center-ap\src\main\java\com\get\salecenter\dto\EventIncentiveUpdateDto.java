package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/7/19 14:59
 * @verison: 1.0
 * @description:
 */
@Data
public class EventIncentiveUpdateDto extends BaseVoEntity {
    /**
     * 公司Id
     */
    @NotNull(
            message = "公司Id不能为空！",
            groups = {Add.class, Update.class}
    )
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 学校提供商Id
     */
    @NotNull(
            message = "学校供应商Id不能为空！",
            groups = {Add.class, Update.class}
    )
    @ApiModelProperty(value = "学校提供商Id")
    private Long fkInstitutionProviderId;

    /**
     * 活动编号
     */
    @ApiModelProperty(value = "活动编号")
    private String num;

    /**
     * 收到奖励时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "收到奖励时间")
    private Date receivingRewardTime;

    /**
     * 实际宣传时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "实际宣传时间")
    private Date actualPublicityTime;

    /**
     * 活动开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "活动开始时间")
    private Date eventStartTime;

    /**
     * 活动结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "活动结束时间")
    private Date eventEndTime;

    /**
     * 符合奖励intake
     */
    @NotBlank(
            message = "符合奖励intake不能为空！",
            groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class}
    )
    @ApiModelProperty(value = "符合奖励intake")
    private String accordWithIncentiveIntake;

    /**
     * 激励政策
     */
    @NotBlank(
            message = "激励政策不能为空！",
            groups = {Add.class, Update.class}
    )
    @ApiModelProperty(value = "激励政策")
    private String incentivePolicy;

    /**
     * 建议核对时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "建议核对时间")
    private Date suggestCheckTime;

    /**
     * 预计完成学生数
     */
    @NotNull(
            message = "预计完成学生数不能为空！",
            groups = {Add.class, Update.class}
    )
    @ApiModelProperty(value = "预计完成学生数")
    private Integer expectTargetCount;

    /**
     * 实际完成学生数
     */
    @ApiModelProperty(value = "实际完成学生数")
    private Integer actualTargetCount;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    /**
     * 实际支付奖励金额
     */
    @ApiModelProperty(value = "实际支付奖励金额")
    private BigDecimal actualPayAmount;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 状态：0计划/1结束/2取消/3延期
     */
    @ApiModelProperty(value = "状态：0计划/1结束/2取消/3延期")
    private Integer status;

    @NotNull(
            message = "业务国家不能为空！",
            groups = {Add.class, Update.class}
    )
    @ApiModelProperty(value = "业务国家多选")
    private List<Long> fkAreaCountryIdList;


    @ApiModelProperty("活动标题")
    private String eventTitle;

    @ApiModelProperty("是否下发奖励：0否/1是")
    private Boolean isDistributed;

    @ApiModelProperty("公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    private String publicLevel;
}
