package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("u_event_type")
public class EventType extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    @Column(name = "type_name")
    private String typeName;

    /**
     * 类型名称
     */
    @UpdateWithNull
    @ApiModelProperty(value = "专属部门，没有设置表示无限制，支持多部门，逗号分隔：1,2,3")
    @Column(name = "fk_department_ids")
    private String fkDepartmentIds;


    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}