package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/5/9 18:09
 * @verison: 1.0
 * @description:
 */
@Data
public class EventBillListVo extends BaseEntity {
    @ApiModelProperty("公司名称")
    private String fkCompanyName;

    @ApiModelProperty("状态名称")
    private String statusName;

    @ApiModelProperty("提供商名称")
    private String fkInstitutionProviderName;

    @ApiModelProperty("国家名称")
    private String fkAreaCountryName;

    @ApiModelProperty("活动摘要")
    private String fkEventSummaryName;

    @ApiModelProperty("通知人名称")
    private String fkStaffNoticeName;

//    @ApiModelProperty("活动费用金额（带币种）")
    @ApiModelProperty("活动费用金额")
    private String eventAmountCurrency;

    @ApiModelProperty("活动费用币种")
    private String eventCurrencyTypeNum;

//    @ApiModelProperty("发起invoice金额（带币种）")
    @ApiModelProperty("发起invoice金额")
    private String invoiceAmountCurrency;

    @ApiModelProperty("发起invoice币种")
    private String invoiceCurrencyTypeNum;

    @ApiModelProperty("应收币种")
    private String receivableCurrencyTypeNum;

//    @ApiModelProperty("应收金额（带币种）")
    @ApiModelProperty("应收金额")
    private String receivableAmountCurrency;

    @ApiModelProperty("应收金额")
    private BigDecimal receivableAmount;

//    @ApiModelProperty("实收金额（带币种）")
    @ApiModelProperty("实收金额")
    private String actualReceivableAmountCurrency;

    @ApiModelProperty("实收金额")
    private BigDecimal actualReceivableAmount;

//    @ApiModelProperty("应收未收（带币种）")
    @ApiModelProperty("应收未收")
    private String differenceAmountCurrency;






    @ApiModelProperty("应收未收")
    private BigDecimal differenceAmount;

    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @ApiModelProperty("发票创建时间")
    private Date invoiceCreateTime;

    @ApiModelProperty("分配余额")
    private String differenceEventAmountCurrency;

    @ApiModelProperty("分配余额状态：未分配/已经分配/完成分配：0/1/2")
    private Integer allocationStatus;

    @ApiModelProperty("分配余额状态名称")
    private String allocationStatusName;

    @ApiModelProperty("分配余额状态名称")
    private Boolean reinitiateStatus=false;

    @ApiModelProperty("已分金额")
    private BigDecimal assignedAmount;

    @ApiModelProperty("可分金额")
    private BigDecimal differenceEventAmount;

    @ApiModelProperty("活动摘要idList")
    private List<Long> fkEventSummaryIdList;

    @ApiModelProperty("活动摘要id")
    private String fkEventSummaryIds;

    //========实体类EventBill============
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    /**
     * 学校供应商Id
     */
    @ApiModelProperty(value = "学校供应商Id")
    @Column(name = "fk_institution_provider_id")
    private Long fkInstitutionProviderId;

    /**
     * 活动年份
     */
    @ApiModelProperty(value = "活动年份")
    @Column(name = "event_year")
    private Integer eventYear;

    /**
     * invoice币种
     */
    @ApiModelProperty(value = "invoice币种")
    @Column(name = "fk_currency_type_num_invoice")
    private String fkCurrencyTypeNumInvoice;

    /**
     * invoice金额
     */
    @UpdateWithNull
    @ApiModelProperty(value = "invoice金额")
    @Column(name = "invoice_amount")
    private BigDecimal invoiceAmount;

    /**
     * 活动费币种
     */
    @ApiModelProperty(value = "活动费币种")
    @Column(name = "fk_currency_type_num_event")
    private String fkCurrencyTypeNumEvent;

    /**
     * 活动费金额
     */
    @ApiModelProperty(value = "活动费金额")
    @Column(name = "event_amount")
    private BigDecimal eventAmount;

    /**
     * invoice名目
     */
    @ApiModelProperty(value = "invoice名目")
    @Column(name = "invoice_summary")
    private String invoiceSummary;

    /**
     * invoice收件人
     */
    @ApiModelProperty(value = "invoice收件人")
    @Column(name = "invoice_contact_person")
    private String invoiceContactPerson;

    /**
     * invoice收件人Email
     */
    @ApiModelProperty(value = "invoice收件人Email")
    @Column(name = "invoice_contact_email")
    private String invoiceContactEmail;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    /**
     * 应收计划Id
     */
//    @UpdateWithNull
//    @ApiModelProperty(value = "应收计划Id")
//    @Column(name = "fk_receivable_plan_id")
//    private Long fkReceivablePlanId;

    /**
     * 发票编号
     */
    @UpdateWithNull
    @ApiModelProperty(value = "发票编号")
    @Column(name = "fk_invoice_num")
    private String fkInvoiceNum;

    /**
     * 状态：0作废/1打开
     */
    @ApiModelProperty(value = "状态：0作废/1打开")
    @Column(name = "status")
    private Integer status;

    private static final long serialVersionUID = 1L;
}

