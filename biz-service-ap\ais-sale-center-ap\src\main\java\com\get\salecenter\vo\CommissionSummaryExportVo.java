package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author:cream
 * @Date: 2023/9/6  14:14
 */
@Data
public class CommissionSummaryExportVo {

    @ApiModelProperty(value = "是否锁定")
    private String lock;

    @ApiModelProperty(value = "所属公司")
    private String fkCompanyName;

    @ApiModelProperty(value = "业务类型")
    private String fkTypeKeyName;

    @ApiModelProperty(value = "合作伙伴")
    private String agentName;

    @ApiModelProperty(value = "代理身份证号码/营业执照号")
    private String agentIdCard;

    @ApiModelProperty(value = "支付币种")
    private String planCurrencyNum;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal amountActual;

    @ApiModelProperty(value = "实际手续费金额")
    private BigDecimal serviceFeeActual;

    @ApiModelProperty(value = "兑人民币汇率")
    private BigDecimal rmbRate;

    @ApiModelProperty(value = "折合人民币")
    private BigDecimal rmbAmount;

    @ApiModelProperty(value = "结算币种")
    private String accountCurrencyNumName;

    @ApiModelProperty(value = "收款开户银行")
    private String bankName;

    //    @ApiModelProperty(value = "银行支行")
    //    private String bankBranchName;

    @ApiModelProperty(value = "收款账户名")
    private String bankAccount;

    @ApiModelProperty(value = "收款账户号")
    private String bankAccountNum;

    @ApiModelProperty(value = "收款银行地址")
    private String bankAddress;

    @ApiModelProperty(value = "Swift Code")
    private String swiftCode;

    @ApiModelProperty(value = "学生姓名")
    private String studentName;

    @ApiModelProperty(value = "转账用途")
    private String transferPurpose;

    @ApiModelProperty(value = "绑定BD")
    private String bdName;
}
