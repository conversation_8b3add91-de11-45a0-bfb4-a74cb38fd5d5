package ${package.Service};

import ${package.Entity}.${entity};
<#--import ${superServiceClassPackage};-->
import com.get.core.mybatis.base.BaseService;

/**
 * <p>
 * ${table.comment!} 服务类
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
<#if kotlin>
interface ${table.serviceName} : ${superServiceClass}<${entity}>
<#else>
<#--public interface ${table.serviceName} extends ${superServiceClass}<${entity}> {-->
public interface ${table.serviceName} extends BaseService<${entity}> {

}
</#if>
