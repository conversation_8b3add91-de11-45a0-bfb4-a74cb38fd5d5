package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.financecenter.vo.BankVo;
import com.get.financecenter.dto.BankDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/2/14
 * @TIME: 14:40
 * @Description:
 **/
public interface IBankService {

    /**
     * @return java.lang.Long
     * @Description: 新增
     * @Param [businessChannelVo]
     * <AUTHOR>
     */
    void addBank(List<BankDto> bankDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.BusinessChannelVo>
     * @Description: 查询
     * @Param [businessChannelVo, page]
     * <AUTHOR>
     */
    List<BankVo> getBankDtos(BankDto bankDto, Page page);


    /**
     * @return com.get.salecenter.vo.BusinessChannelVo
     * @Description: 修改
     * @Param [businessChannelVo]
     * <AUTHOR>
     */
    BankVo updateBank(BankDto bankDto);

    /**
     * @return void
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    void deleteBank(Long id);

    /**
     * 查询byID
     *
     * @param id
     * @return
     */
    BankVo findBankById(Long id);


    List<BaseSelectEntity> bankSelect(String num);
}
