package com.get.insurancecenter.dto.card;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/7/28
 * @Version 1.0
 * @apiNote:base64加密解密
 */
@Data
public class EncryptParam {

    @ApiModelProperty(value = "待加密/解密字符串")
    @NotBlank(message = "待加密/解密字符串不能为空")
    private String str;

    @ApiModelProperty(value = "加密/解密类型：1加密/2解密")
    @NotNull(message = "加密/解密类型不能为空")
    private Integer type;
}
