package com.get.institutioncenter.utils;

import com.get.core.tool.utils.GeneralTool;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang.StringEscapeUtils;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.Calendar;

/**
 * <AUTHOR>
 * @DATE: 2020/8/14
 * @TIME: 11:28
 * @Description: 学校中心编码规则工具类
 **/
@UtilityClass
public class MyStringUtils {
    /**
     * 学校编号
     *
     * @param num
     * @return
     */
    public static String getInstitutionNum(String countryNum, Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 7) {
            code = String.format("%06d", num);
        }
        return "INS" + countryNum + code;
    }

    /**
     * 专业编号
     *
     * @param num
     * @return
     */
    public static String getMajorNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 7) {
            code = String.format("%06d", num);
        }
        return "MA" + code;
    }

    /**
     * 课程编号
     *
     * @param num
     * @return
     */
    public static String getCourseNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 11) {
            code = String.format("%010d", num);
        }
        return "PRG" + code;
    }


    /**
     * 州省编号
     *
     * @param num
     * @return
     */
    public static String getStateNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 7) {
            code = String.format("%06d", num);
        }
        return "PR" + code;
    }

    /**
     * 业务区域编号
     *
     * @param num
     * @return
     */
    public static String getRegionStateNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 7) {
            code = String.format("%06d", num);
        }
        return "RG" + code;
    }

    /**
     * 城市编号
     *
     * @param num
     * @return
     */
    public static String getCityNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 7) {
            code = String.format("%06d", num);
        }
        return "CT" + code;
    }

    /**
     * 集团编号
     *
     * @param num
     * @return
     */
    public static String getGroupNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 7) {
            code = String.format("%06d", num);
        }
        return "IG" + code;
    }

    /**
     * 渠道编号
     *
     * @param num
     * @return
     */
    public static String getChannelNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 7) {
            code = String.format("%06d", num);
        }
        return "IC" + code;
    }

    /**
     * 获取合同编号
     *
     * @return
     */
    public static String getContractNum() {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        return "AGC" + formatter.format(calendar.getTime());
    }

    /**
     * @return
     * @Description: 学校提供商编号
     * @Param
     * <AUTHOR>
     */
    public static String getInstitutionProviderNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 7) {
            code = String.format("%06d", num);
        }
        return "INSPVD" + code;
    }

    /**
     * 把转义的字符转回正常
     * @param target
     * @param <T>
     */
    public <T>void UnescapeHtml(T target){
        Class clazz = target.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if(field.getName().equals("serialVersionUID")) {
                continue; //忽略 serialVersionUID，类中 serialVersionUID = 1L;
            }
            //设置属性是可以访问的(私有的也可以)
            field.setAccessible(true);
            Object value = null;
            try {
                value = field.get(target);
                if (String.class.equals(field.getType())){
                    // 属性不为空
                    if (GeneralTool.isNotEmpty(value)) {
                        PropertyDescriptor propertyDescriptor = new PropertyDescriptor(field.getName(),clazz);
                        //获取写方法 setXX
                        Method writeMethod = propertyDescriptor.getWriteMethod();
                        //调用写方法 setXX
                        writeMethod.invoke(target, StringEscapeUtils.unescapeHtml(value.toString()));
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        //继承的父类中属性值也要判断
        Field[] superFields = clazz.getSuperclass().getDeclaredFields();
        for (Field superField : superFields) {
            if(superField.getName().equals("serialVersionUID")){
                continue;
            }
            //设置属性是可以访问的(私有的也可以)
            superField.setAccessible(true);
            Object value = null;
            try {
                value = superField.get(target);
                // 只要有1个属性不为空,那么就不是所有的属性值都为空
                if (String.class.equals(superField.getType())){
                    // 属性不为空
                    if (GeneralTool.isNotEmpty(value)) {
                        PropertyDescriptor propertyDescriptor = new PropertyDescriptor(superField.getName(),clazz);
                        //获取写方法 setXX
                        Method writeMethod = propertyDescriptor.getWriteMethod();
                        //调用写方法 setXX
                        writeMethod.invoke(target,StringEscapeUtils.unescapeHtml(value.toString()));
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    // 判断字符串中是否包含中文字符
    public static boolean containsChineseCharacters(String str) {
        if (str == null) {
            return false;
        }
        return str.chars().anyMatch(c -> isChineseCharacter((char) c));
    }

    // 判断单个字符是否为中文字符
    public static boolean isChineseCharacter(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        return ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B
                || ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION
                || ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS
                || ub == Character.UnicodeBlock.GENERAL_PUNCTUATION;
    }

}
