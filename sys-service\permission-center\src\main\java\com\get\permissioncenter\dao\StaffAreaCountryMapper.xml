<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.permissioncenter.dao.StaffAreaCountryMapper">
    <resultMap id="BaseResultMap" type="com.get.permissioncenter.entity.StaffAreaCountry">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_staff_id" jdbcType="BIGINT" property="fkStaffId"/>
        <result column="fk_area_country_key" jdbcType="VARCHAR" property="fkAreaCountryKey"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, fk_staff_id, fk_area_country_key, gmt_create, gmt_create_user, gmt_modified, 
    gmt_modified_user
    </sql>
    <select id="getStaffAreaCountrysByfkStaffId" resultMap="BaseResultMap">
      select * from r_staff_area_country where fk_staff_id = #{fkStaffId}
    </select>
    <select id="getStaffAreaCountryKeysByfkStaffId" resultType="java.lang.String">
        select fk_area_country_key from r_staff_area_country where fk_staff_id = #{staffId}
    </select>
    <select id="isExistByStaffId" resultType="java.lang.Boolean">
    SELECT IFNULL(max(id),0) id FROM  r_staff_area_country where fk_staff_id =#{staffId}
    </select>
    <select id="getStaffByAreaCountryKeys" resultType="java.lang.Long">
        SELECT DISTINCT fk_staff_id FROM r_staff_area_country
        <where>
            <if test="staffIds != null">
                AND fk_staff_id IN
                <foreach collection="staffIds" item="staffId" open="(" separator="," close=")">
                    #{staffId}
                </foreach>
            </if>
            <if test="areaCountryKeys != null">
                AND fk_area_country_key IN
                <foreach collection="areaCountryKeys" item="areaCountryKey" open="(" separator="," close=")">
                    #{areaCountryKey}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getStaffAreaCountryIdByfkStaffId" resultType="java.lang.Long">
        select uc.id from r_staff_area_country rc LEFT JOIN ais_institution_center.u_area_country uc ON rc.fk_area_country_key = uc.num
                where rc.fk_staff_id = #{staffId}
    </select>
</mapper>