package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.vo.StudentContactPersonVo;
import com.get.salecenter.entity.StudentContactPerson;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StudentContactPersonMapper extends BaseMapper<StudentContactPerson> {

    int insertSelective(StudentContactPerson record);

    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [agentId]
     * <AUTHOR>
     */
    Boolean isExistByStudentId(Long studentId);

    /**
     * 根据学生Id获取联系人列表
     *
     * @param studentId
     * @return
     */
    List<StudentContactPersonVo> getStudentContactPersonByStudentId(@Param("studentId") Long studentId);
}