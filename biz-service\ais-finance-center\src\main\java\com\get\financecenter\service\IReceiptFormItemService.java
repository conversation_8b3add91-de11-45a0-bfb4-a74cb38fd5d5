package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.financecenter.dto.MediaAndAttachedDto;
import com.get.financecenter.dto.ReceiptFormItemDto;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import com.get.financecenter.vo.ReceiptFormItemVo;
import com.get.financecenter.entity.ReceiptFormItem;
import com.get.financecenter.dto.ReceiptReDto;
import com.get.financecenter.dto.query.ReceiptFormQueryDto;
import com.get.financecenter.vo.ReceiptFormVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/12/31
 * @TIME: 11:42
 * @Description:
 **/
public interface IReceiptFormItemService extends BaseService<ReceiptFormItem> {
    /**
     * @return java.util.List<com.get.financecenter.vo.ReceiptFormItemDto>
     * @Description: 列表数据
     * @Param [receiptFormItemVo, page]
     * <AUTHOR>
     */
    List<ReceiptFormItemVo> datas(ReceiptFormItemDto receiptFormItemDto, Page page);

    /**
     * 导出收款单子项列表
     *
     * @param response
     * @param receiptFormVo
     */
    void exportReceiptFormItemExcel(HttpServletResponse response, ReceiptFormQueryDto receiptFormVo);


    /**
     * @return java.lang.Long
     * @Description: 保存
     * @Param [receiptFormItemVo]
     * <AUTHOR>
     */
    void addReceiptFormItem(List<ReceiptFormItemDto> receiptFormItemDto);


    /**
     * @return com.get.financecenter.vo.ReceiptFormItemDto
     * @Description: 修改
     * @Param [receiptFormItemVo]
     * <AUTHOR>
     */
    ReceiptFormItemVo updateReceiptFormItem(ReceiptFormItemDto receiptFormItemDto);

//    /**
//     * 有预付的情况：根据应收计划id判断收款状态，收齐了才能生成分期表进行结算
//     *
//     * @Date 11:04 2022/4/22
//     * <AUTHOR>
//     */
//    boolean isPayInAdvanceInsertSettlementInstallment(Long receivablePlanId);


    /**
     * @return com.get.financecenter.vo.ReceiptFormItemDto
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    ReceiptFormItemVo findReceiptFormItemById(Long id);

    /**
     * @return void
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return java.util.List<com.get.financecenter.vo.MediaAndAttachedDto>
     * @Description: 添加附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    List<FMediaAndAttachedVo> addMedia(List<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * @return java.util.List<com.get.financecenter.vo.MediaAndAttachedDto>
     * @Description: 获取附件
     * @Param [attachedVo, page]
     * <AUTHOR>
     */
    List<FMediaAndAttachedVo> getMedia(MediaAndAttachedDto attachedVo, Page page);


    /**
     * @return com.get.financecenter.vo.ReceiptFormItemDto
     * @Description:
     * @Param [typeKey, targetId, formId]
     * <AUTHOR>
     */
    ReceiptFormItemVo getReceiptFormItem(Long planId, Long formId);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 学生申请方案下拉
     * @Param [tableName, studentId]
     * <AUTHOR>
     */
    List<BaseSelectEntity> getStudentOfferItemSelect(String tableName, String fkTypeKey, Long fkTypeTargetId);

    /**
     * @return java.util.List<com.get.financecenter.vo.ReceiptFormVo>
     * @Description: 获取收款单列表
     * @Param [receiptFormItemId]
     * <AUTHOR>
     */
    List<ReceiptFormVo> getReceiptFormList(Long planId);

    /**
     * feign根据应收计划ids获取所绑定的收款单子项
     *
     * @Date 16:49 2021/11/19
     * <AUTHOR>
     */
    List<ReceiptFormVo> getReceiptFormListFeignByPlanIds(Set<Long> planIds);

    /**
     * feign 查找对应的应收计划是否绑定 未结算过的收款单子单
     *
     * @Date 16:49 2021/11/19
     * <AUTHOR>
     */
    List<ReceiptFormItemVo> getSettlementReceiptFormItemListByPlanId(Long receivablePlanId);

    /**
     * 获取收款单列表数据
     * @param receiptReDto
     * @return
     */
    List<ReceiptFormItemVo> getReceiptFormItemList(ReceiptReDto receiptReDto, String[] times, Integer pageNumber, Integer pageSize);

    ResponseBo  getReceiptFormItemListPagination(Long formId, String[] times,Page page);

    /**
     * feign 根据应收计划id获取绑定的收款单子单列表
     *
     * @Date 17:58 2022/4/21
     * <AUTHOR>
     */
    List<ReceiptFormItemVo> getReceiptFormItemListFeignByPlanIds(Set<Long> planIds);

    /**
     * feign 根据收款单子单ids获取收款单子单信息
     *
     * @Date 17:58 2022/4/21
     * <AUTHOR>
     */
    List<ReceiptFormItemVo> getReceiptFormItemListFeignByFormItemIds(Set<Long> receiptFormItemIds);

    /**
     * 激活佣金结算
     *
     * @Date 17:16 2022/5/5
     * <AUTHOR>
     */
    Boolean activateCommissionSettlement(Set<Long> receiptFormItemIds);



    /**
     * 根据应收计划id批量激活佣金结算
     *
     * @param receivablePlanIds 应收计划ids
     * @return
     */
    Boolean activateCommissionSettlementByReceivablePlanIds(Set<Long> receivablePlanIds);

    /**
     * 应收是否已经绑定收款
     *
     * @param fkReceivablePlanId
     * @return
     */
    Boolean isReceivablePlanBound(Long fkReceivablePlanId);


    List<ReceiptFormItemVo> getReceiptFormItemsByFormIds(Set<Long> ids);

    List<ReceiptFormItemVo> getReceiptFormByInvoiceId(Long fkInvoiceId);
}
