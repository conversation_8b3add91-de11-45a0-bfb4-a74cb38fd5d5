<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.PayablePlanSettlementAgentAccountMapper">
    <update id="updateCommissionSettlement">
    UPDATE ais_finance_center.r_payable_plan_settlement_installment AS rppsi
    INNER JOIN ais_finance_center.m_payment_form_item AS mpfi ON mpfi.id = rppsi.fk_payment_form_item_id
    SET rppsi.fk_agent_contract_account_id = #{paymentFormAgentUpdateDto.targetBankAccountId}, rppsi.fk_currency_type_num = #{fkCurrencyTypeNum}
    WHERE
      mpfi.fk_payment_form_id = #{paymentFormAgentUpdateDto.fkPaymentFormId}
    <!--
      UPDATE r_payable_plan_settlement_agent_account
      SET fk_agent_contract_account_id = #{paymentFormAgentUpdateDto.targetBankAccountId},fk_currency_type_num = #{fkCurrencyTypeNum} where id IN (
        SELECT * FROM (
        SELECT
          rppsaa.id
        FROM
          ais_sale_center.r_payable_plan_settlement_agent_account AS rppsaa
            INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi ON rppsaa.num_settlement_batch = rppsi.num_settlement_batch
            AND rppsaa.fk_payable_plan_id = rppsi.fk_payable_plan_id
            INNER JOIN ais_finance_center.m_payment_form_item AS mpfi ON mpfi.id = rppsi.fk_payment_form_item_id
        WHERE
          mpfi.fk_payment_form_id = #{paymentFormAgentUpdateDto.fkPaymentFormId})a
      )
      -->
    </update>

    <select id="getCommissionSettlementStatus" resultType="java.lang.Integer">
      SELECT
        CASE

          WHEN
            MAX( status_settlement ) IN ( 3, 4 ) THEN
            3
          WHEN MAX( status_settlement ) IS NULL THEN
            1 ELSE 2
          END
      FROM
       ais_finance_center.r_payable_plan_settlement_installment
      WHERE
        fk_agent_contract_account_id = #{agentContractAccountId}
    </select>
    <select id="getCommissionSettlementAccountInfo" resultType="java.lang.Boolean">
      SELECT IFNULL(MAX(id),0) FROM ais_finance_center.r_payable_plan_settlement_installment WHERE fk_agent_contract_account_id = #{agentContractAccountId}
    </select>
</mapper>