package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class StaffCommissionRefundRoleVo {

    @ApiModelProperty("os学生总数")
    private int studentTotal;

    @ApiModelProperty("非os学生总数")
    private int unOsStudentTotal;

    @ApiModelProperty("角色金额详情")
    private List<RoleAmountDto> roleAmountList;


    @Data
    public static class RoleAmountDto{
        @ApiModelProperty("角色名称")
        private String roleName;

        @ApiModelProperty("确认退款金额")
        private BigDecimal confirmRefundAmount;

        @ApiModelProperty("无需退款金额")
        private BigDecimal noRefundRequired;

        @ApiModelProperty("总退费")
        private BigDecimal unapprovedAmount;
    }
}
