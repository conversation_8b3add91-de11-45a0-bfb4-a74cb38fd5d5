package com.get.institutioncenter.dto.query;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ContactPersonQueryDto {

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 联系人类型Key，多值逗号隔开
     */
    @ApiModelProperty(value = "联系人类型Key，多值逗号隔开", required = true)
    @NotBlank(message = "联系人类型不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private String fkContactPersonTypeKey;

    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    @NotBlank(message = "表不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private String fkTableName;

    @ApiModelProperty(value = "查询关键字")
    private String keyWord;

    @ApiModelProperty(value = "目标名称")
    private String targetName;

}
