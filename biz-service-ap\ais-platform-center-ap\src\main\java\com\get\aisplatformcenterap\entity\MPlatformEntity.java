package com.get.aisplatformcenterap.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 
 * @TableName m_platform
 */
@TableName(value ="m_platform")
@Data
public class MPlatformEntity extends BaseEntity implements Serializable {

    @ApiModelProperty("平台应用名称")
    private String name;

    /**
     * 平台应用名称（英文）
     */
    private String nameEn;

    /**
     * 平台应用CODE
     */
    private String code;

    /**
     * 描述
     */
    private String remark;

    /**
     * 状态，0已停用，1启用中
     */
    private Integer status;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}