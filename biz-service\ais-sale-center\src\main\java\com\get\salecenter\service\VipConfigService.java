package com.get.salecenter.service;
import com.get.common.result.SearchBean;
import com.get.salecenter.vo.VipConfigVo;
import com.get.salecenter.dto.VipConfigDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/11/9
 * @TIME: 16:53
 * @Description:
 **/
public interface VipConfigService {
    /**
     * @Description:查询所有VIP设置
     * <AUTHOR>
     * @Date 17:01 2022/11/9
     **/
    List<VipConfigVo> getVipConfigs(VipConfigDto vipConfigDto, SearchBean<VipConfigDto> page);

    /**
     * @Description:新增VIP设置
     * <AUTHOR>
     * @Date 17:02 2022/11/9
     **/
    void add(VipConfigDto vipConfigDto);

    /**
     * @Description:删除VIP设置
     * <AUTHOR>
     * @Date 17:02 2022/11/9
     **/
    void delete(Long id);

    /**
     * @Description:VIP设置详情
     * <AUTHOR>
     * @Date 17:02 2022/11/9
     **/
    VipConfigVo findVipConfigById(Long id);

    /**
     * @Description:修改VIP设置
     * <AUTHOR>
     * @Date 17:03 2022/11/9
     **/
    void updateVipConfig(VipConfigDto vipConfigDto);

    /**
     * @Description:
     * <AUTHOR>
     * @Date 17:03 2022/11/9
     **/
    void movingOrder(List<VipConfigDto> vipConfigDtos);
}
