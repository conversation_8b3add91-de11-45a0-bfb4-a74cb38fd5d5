package com.get.votingcenter.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseEntity;
import com.get.votingcenter.entity.VotingRule;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.util.Date;

/**
 * @author: Hardy
 * @create: 2021/9/23 14:42
 * @verison: 1.0
 * @description:
 */
@Data
public class VotingRuleVo  extends BaseEntity {

    /**
     * 投票主题Id
     */
    @ApiModelProperty(value = "投票主题Id")
    @Column(name = "fk_voting_id")
    private Long fkVotingId;
    /**
     * 每次投票票数，默认为1，必填
     */
    @ApiModelProperty(value = "每次投票票数，默认为1，必填")
    @Column(name = "vote_count")
    private Integer voteCount;
    /**
     * 投票次数限制，无限制为-1，默认为1，必填
     */
    @ApiModelProperty(value = "投票次数限制，无限制为-1，默认为1，必填")
    @Column(name = "vote_limit")
    private Integer voteLimit;
    /**
     * 是否能重复投票，0否/1是
     */
    @ApiModelProperty(value = "是否能重复投票，0否/1是")
    @Column(name = "is_repeat_voting")
    private Boolean isRepeatVoting;
    /**
     * 投票规则适用对象：填手机号，多个手机号逗号分隔。若为空为默认所有用户规则
     */
    @ApiModelProperty(value = "投票规则适用对象：填手机号，多个手机号逗号分隔。若为空为默认所有用户规则")
    @Column(name = "vote_rule_auth")
    private String voteRuleAuth;
    /**
     * 默认为0，为0时随机排序，排序为从大到小顺序排列
     */
    @ApiModelProperty(value = "默认为0，为0时随机排序，排序为从大到小顺序排列")
    @Column(name = "view_order")
    private Integer viewOrder;


}
