<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.workflowcenter.mapper.ActRuIdentitylinkMapper">
  <insert id="insert" parameterType="com.get.workflowcenter.entity.ActRuIdentitylink">
    insert into act_ru_identitylink (ID_, REV_, GROUP_ID_, 
      TYPE_, USER_ID_, TASK_ID_, 
      PROC_INST_ID_, PROC_DEF_ID_)
    values (#{id,jdbcType=VARCHAR}, #{rev,jdbcType=INTEGER}, #{groupId,jdbcType=VARCHAR}, 
      #{type,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{taskId,jdbcType=VARCHAR}, 
      #{procInstId,jdbcType=VARCHAR}, #{procDefId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.workflowcenter.entity.ActRuIdentitylink">
    insert into act_ru_identitylink
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID_,
      </if>
      <if test="rev != null">
        REV_,
      </if>
      <if test="groupId != null">
        GROUP_ID_,
      </if>
      <if test="type != null">
        TYPE_,
      </if>
      <if test="userId != null">
        USER_ID_,
      </if>
      <if test="taskId != null">
        TASK_ID_,
      </if>
      <if test="procInstId != null">
        PROC_INST_ID_,
      </if>
      <if test="procDefId != null">
        PROC_DEF_ID_,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="rev != null">
        #{rev,jdbcType=INTEGER},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="procInstId != null">
        #{procInstId,jdbcType=VARCHAR},
      </if>
      <if test="procDefId != null">
        #{procDefId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.get.workflowcenter.entity.ActRuIdentitylink">
    update act_ru_identitylink
    <set>
      <if test="rev != null">
        REV_ = #{rev,jdbcType=INTEGER},
      </if>
      <if test="groupId != null">
        GROUP_ID_ = #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        TYPE_ = #{type,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        USER_ID_ = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        TASK_ID_ = #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="procInstId != null">
        PROC_INST_ID_ = #{procInstId,jdbcType=VARCHAR},
      </if>
      <if test="procDefId != null">
        PROC_DEF_ID_ = #{procDefId,jdbcType=VARCHAR},
      </if>
    </set>
    where ID_ = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.workflowcenter.entity.ActRuIdentitylink">
    update act_ru_identitylink
    set REV_ = #{rev,jdbcType=INTEGER},
      GROUP_ID_ = #{groupId,jdbcType=VARCHAR},
      TYPE_ = #{type,jdbcType=VARCHAR},
      USER_ID_ = #{userId,jdbcType=VARCHAR},
      TASK_ID_ = #{taskId,jdbcType=VARCHAR},
      PROC_INST_ID_ = #{procInstId,jdbcType=VARCHAR},
      PROC_DEF_ID_ = #{procDefId,jdbcType=VARCHAR}
    where ID_ = #{id,jdbcType=VARCHAR}
  </update>
    <select id="getStarterId" resultType="com.get.workflowcenter.entity.ActRuIdentitylink">
      SELECT
        ari.*
      FROM
        act_ru_identitylink AS ari
          LEFT JOIN act_ru_task AS art ON ari.PROC_INST_ID_ = art.PROC_INST_ID_
      WHERE
        ari.TYPE_ = "starter"
        AND ari.PROC_INST_ID_ = #{procInstId}
    </select>
</mapper>