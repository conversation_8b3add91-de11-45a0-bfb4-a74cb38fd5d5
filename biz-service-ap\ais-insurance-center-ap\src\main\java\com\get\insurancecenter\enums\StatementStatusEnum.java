package com.get.insurancecenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 订单状态枚举类
 */

@Getter
@AllArgsConstructor
public enum StatementStatusEnum {

    FAIL(0, "失败"),
    SUCCESS(1, "成功"),
    ;


    private Integer code;

    private String msg;


    public static StatementStatusEnum getEnumByCode(Integer code) {
        for (StatementStatusEnum value : StatementStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
