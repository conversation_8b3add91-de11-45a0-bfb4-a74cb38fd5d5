package com.get.partnercenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 伙伴用户角色关系表
 */
@Data
@TableName("r_partner_user_partner_role")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PartnerUserPartnerRole extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("租户Id")
    private Long fkTenantId;

    @ApiModelProperty("伙伴用户Id")
    private Long fkPartnerUserId;

    @ApiModelProperty("伙伴角色Id")
    private Long fkPartnerRoleId;
}
