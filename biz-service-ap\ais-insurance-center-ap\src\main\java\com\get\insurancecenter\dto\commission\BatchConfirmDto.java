package com.get.insurancecenter.dto.commission;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/5/27
 * @Version 1.0
 * @apiNote:批；批量确认
 */
@Data
public class BatchConfirmDto {

    @ApiModelProperty(value = "订单Id集合")
    @NotNull(message = "订单Id集合不能为空")
    private List<Long> orderIds;

    @ApiModelProperty(value = "批量确认类型-0待确认列表 2确认结算列表")
    @NotNull(message = "批量确认类型不能为空")
    private Integer type;
}
