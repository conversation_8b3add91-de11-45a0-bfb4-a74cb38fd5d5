package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.salecenter.vo.AgentContractFormulaVo;
import com.get.salecenter.vo.CompanyTreeVo;
import com.get.salecenter.service.IAgentContractFormulaService;
import com.get.salecenter.dto.AgentContractFormulaCompanyDto;
import com.get.salecenter.dto.AgentContractFormulaDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/1/6 12:21
 * @verison: 1.0
 * @description:
 */
@Api(tags = "学生代理合同公式管理")
@RestController
@RequestMapping("sale/agentContractFormula")
public class AgentContractFormulaController {
    @Resource
    private IAgentContractFormulaService agentContractFormulaService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AgentContractFormulaVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生代理合同公式管理/学生代理合同公式详情")
    @GetMapping("/{id}")
    public ResponseBo<AgentContractFormulaVo> detail(@PathVariable("id") Long id) {
        AgentContractFormulaVo data = agentContractFormulaService.findAgentContractFormulaById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增信息
     * @Param [agentContractFormulaVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生代理合同公式管理/新增学生代理合同公式")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(AgentContractFormulaDto.Add.class)  AgentContractFormulaDto agentContractFormulaDto) {
        return SaveResponseBo.ok(agentContractFormulaService.addAgentContractFormula(agentContractFormulaDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生代理合同公式管理/删除学生代理合同公式")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        agentContractFormulaService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AgentContractFormulaVo>
     * @Description :修改信息
     * @Param [agentContractFormulaDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生代理合同公式管理/更新学生代理合同公式")
    @PostMapping("update")
    public ResponseBo<AgentContractFormulaVo> update(@RequestBody  @Validated(AgentContractFormulaDto.Update.class) AgentContractFormulaDto agentContractFormulaDto) {
        return UpdateResponseBo.ok(agentContractFormulaService.updateAgentContractFormula(agentContractFormulaDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AgentContractFormulaVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "fkAgentId 代理id(必传)")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生代理合同公式管理/查询学生代理合同公式")
    @PostMapping("datas")
    public ResponseBo<AgentContractFormulaVo> datas(@RequestBody SearchBean<AgentContractFormulaDto> page) {
        List<AgentContractFormulaVo> datas = agentContractFormulaService.getAgentContractFormulas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :上移下移
     * @Param [agentContractFormulaDtos]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生代理合同公式管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<AgentContractFormulaDto> agentContractFormulaDtos) {
        agentContractFormulaService.movingOrder(agentContractFormulaDtos);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :学生代理合同公式-安全配置
     * @Param [agentContractFormulaCompanyDtos]
     * <AUTHOR>
     */
    @ApiOperation(value = "学生代理合同公式-安全配置")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生代理合同公式管理/安全配置")
    @PostMapping("editAgentContractFormulaCompany")
    public ResponseBo editAgentContractFormulaCompany(@RequestBody  @Validated(AgentContractFormulaCompanyDto.Add.class) ValidList<AgentContractFormulaCompanyDto> agentContractFormulaCompanyDtos) {
        agentContractFormulaService.editAgentContractFormulaCompany(agentContractFormulaCompanyDtos);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.CompanyTreeVo>
     * @Description :学生代理合同公式-安全配置详情
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    @ApiOperation(value = "学生代理合同公式-安全配置详情")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生代理合同公式管理/安全配置详情")
    @GetMapping("getContractFormulaCompany/{agentContractFormulaId}")
    public ResponseBo<CompanyTreeVo> getContractFormulaCompany(@PathVariable("agentContractFormulaId") Long agentContractFormulaId) {
        return new ListResponseBo<>(agentContractFormulaService.getAgentContractFormulaCompany(agentContractFormulaId));
    }
}
