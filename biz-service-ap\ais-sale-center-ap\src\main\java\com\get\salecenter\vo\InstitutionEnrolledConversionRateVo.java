package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2024/1/17
 * @TIME: 10:26
 * @Description:
 **/
@Data
public class InstitutionEnrolledConversionRateVo {
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

    @ApiModelProperty(value = "学校名称")
    private String institutionName;

    @ApiModelProperty(value = "入学年份")
    private Integer year;

    @ApiModelProperty(value = "学校所在国家id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "学校所在国家名称")
    private String fkAreaCountryName;

    @ApiModelProperty(value = "学校所在州省id")
    private Long fkAreaStateId;

    @ApiModelProperty(value = "学校所在州省名称")
    private String fkAreaStateName;

    @ApiModelProperty(value = "统计")
    private List<EnrolledConversionRateStatisticsVo> statistics;
}
