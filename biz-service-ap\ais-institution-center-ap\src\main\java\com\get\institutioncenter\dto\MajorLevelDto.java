package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: Sea
 * @create: 2020/7/31 12:12
 * @verison: 1.0
 * @description:
 */
@Data
public class MajorLevelDto extends BaseVoEntity {
    /**
     * 等级名字
     */
    @NotBlank(message = "等级名字不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "等级名字", required = true)
    private String levelName;
    /**
     * 等级名字
     */
    @ApiModelProperty(value = "等级名字中文")
    private String levelNameChn;

    @ApiModelProperty(value = "组别名字")
    private String groupName;


    @ApiModelProperty(value = "组别中文名字")
    private String groupNameChn;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    private Integer viewOrder;

    //自定义内容
    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyWord;
}
