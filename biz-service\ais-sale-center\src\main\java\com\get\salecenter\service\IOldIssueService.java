package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.common.result.SearchBean;
import com.get.salecenter.vo.AplOrderVo;
import com.get.salecenter.dto.AplOldIssueOrderDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/3/15 20:18
 */
public interface IOldIssueService {

    List<AplOrderVo> getRpaTableList(AplOldIssueOrderDto data, Page page);

    void exportRobotTable(HttpServletResponse response, SearchBean<AplOldIssueOrderDto> page);
}
