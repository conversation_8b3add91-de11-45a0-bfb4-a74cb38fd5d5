package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.AgentContractFormulaInstitution;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AgentContractFormulaInstitutionMapper extends BaseMapper<AgentContractFormulaInstitution> {
    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(AgentContractFormulaInstitution record);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :查找agentContractFormulaId对应学校ids
     * @Param [agentContractFormulaId]
     * <AUTHOR>
     */
    List<Long> getInstitutionIdsByFkid(@Param("agentContractFormulaId") Long agentContractFormulaId);
}