package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.financecenter.dto.ReceiptMethodTypeDto;
import com.get.financecenter.entity.ReceiptMethodType;
import com.get.financecenter.vo.ReceiptMethodTypeVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * 收款方式类型Mapper
 */
@Mapper
public interface ReceiptMethodTypeMapper extends BaseMapper<ReceiptMethodType> {

    List<ReceiptMethodTypeVo> getReceiptMethodTypes(IPage<ReceiptMethodType> pages,@Param("receiptMethodTypeDto") ReceiptMethodTypeDto receiptMethodTypeDto);

    int checkName(String typeName);

    Integer getMaxViewOrder();

    int insertSelective(ReceiptMethodType receiptMethodType);

    void updateBatchById(@Param("updateList") List<ReceiptMethodType> updateList);

    ReceiptMethodType selectByFkAccountingItemId(Long id);
}

