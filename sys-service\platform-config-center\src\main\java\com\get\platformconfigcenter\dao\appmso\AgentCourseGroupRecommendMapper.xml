<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.platformconfigcenter.dao.appmso.AgentCourseGroupRecommendMapper">
  <resultMap id="BaseResultMap" type="com.get.platformconfigcenter.entity.AgentCourseGroupRecommend">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_agent_id" jdbcType="BIGINT" property="fkAgentId" />
    <result column="fk_course_type_group_id" jdbcType="BIGINT" property="fkCourseTypeGroupId" />
    <result column="target_type" jdbcType="VARCHAR" property="targetType" />
    <result column="target_id" jdbcType="BIGINT" property="targetId" />
    <result column="view_order" jdbcType="INTEGER" property="viewOrder" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <sql id="Base_Column_List">
    id, fk_agent_id, fk_course_type_group_id, target_type, target_id, view_order, gmt_create, 
    gmt_create_user, gmt_modified, gmt_modified_user
  </sql>
 <!-- <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from m_agent_course_group_recommend
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from m_agent_course_group_recommend
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.get.platformconfigcenter.entity.AgentCourseGroupRecommend">
    insert into m_agent_course_group_recommend (id, fk_agent_id, fk_course_type_group_id, 
      target_type, target_id, view_order, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkAgentId,jdbcType=BIGINT}, #{fkCourseTypeGroupId,jdbcType=BIGINT}, 
      #{targetType,jdbcType=VARCHAR}, #{targetId,jdbcType=BIGINT}, #{viewOrder,jdbcType=INTEGER}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.platformconfigcenter.entity.AgentCourseGroupRecommend">
    insert into m_agent_course_group_recommend
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkAgentId != null">
        fk_agent_id,
      </if>
      <if test="fkCourseTypeGroupId != null">
        fk_course_type_group_id,
      </if>
      <if test="targetType != null">
        target_type,
      </if>
      <if test="targetId != null">
        target_id,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkAgentId != null">
        #{fkAgentId,jdbcType=BIGINT},
      </if>
      <if test="fkCourseTypeGroupId != null">
        #{fkCourseTypeGroupId,jdbcType=BIGINT},
      </if>
      <if test="targetType != null">
        #{targetType,jdbcType=VARCHAR},
      </if>
      <if test="targetId != null">
        #{targetId,jdbcType=BIGINT},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.get.platformconfigcenter.entity.AgentCourseGroupRecommend">
    update m_agent_course_group_recommend
    <set>
      <if test="fkAgentId != null">
        fk_agent_id = #{fkAgentId,jdbcType=BIGINT},
      </if>
      <if test="fkCourseTypeGroupId != null">
        fk_course_type_group_id = #{fkCourseTypeGroupId,jdbcType=BIGINT},
      </if>
      <if test="targetType != null">
        target_type = #{targetType,jdbcType=VARCHAR},
      </if>
      <if test="targetId != null">
        target_id = #{targetId,jdbcType=BIGINT},
      </if>
      <if test="viewOrder != null">
        view_order = #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.platformconfigcenter.entity.AgentCourseGroupRecommend">
    update m_agent_course_group_recommend
    set fk_agent_id = #{fkAgentId,jdbcType=BIGINT},
      fk_course_type_group_id = #{fkCourseTypeGroupId,jdbcType=BIGINT},
      target_type = #{targetType,jdbcType=VARCHAR},
      target_id = #{targetId,jdbcType=BIGINT},
      view_order = #{viewOrder,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select
      IFNULL(max(view_order)+1,0) view_order
    from
      m_agent_course_group_recommend
  </select>
    <select id="getCourseGroupRecommendationsList"
            resultType="com.get.platformconfigcenter.entity.AgentCourseGroupRecommend">

      SELECT
        id,
        fk_agent_id,
        fk_course_type_group_id,
        target_type,
        target_id,
        view_order,
        gmt_create_user,
        gmt_create,
        gmt_modified_user,
        gmt_modified
      FROM
        m_agent_course_group_recommend
      <where>
        fk_agent_id = #{data.fkAgentId}
        <if test="data.fkCourseTypeGroupId != null and data.fkCourseTypeGroupId != '' " >
        and  fk_course_type_group_id = #{data.fkCourseTypeGroupId}
        </if>
        GROUP BY fk_course_type_group_id
        order by view_order DESC
      </where>

    </select>-->

</mapper>