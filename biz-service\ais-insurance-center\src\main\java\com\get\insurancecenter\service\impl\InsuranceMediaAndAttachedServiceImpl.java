package com.get.insurancecenter.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.utils.SecureUtil;
import com.get.insurancecenter.config.ConnectTencentCloud;
import com.get.insurancecenter.dto.file.AppFileCenter;
import com.get.insurancecenter.entity.InsuranceMediaAndAttached;
import com.get.insurancecenter.mapper.AppFileCenterMapper;
import com.get.insurancecenter.mapper.InsuranceMediaAndAttachedMapper;
import com.get.insurancecenter.service.InsuranceMediaAndAttachedService;
import com.get.insurancecenter.service.TencentCloudService;
import com.get.insurancecenter.utils.FileUtils;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.model.COSObject;
import com.qcloud.cos.model.COSObjectInputStream;
import com.qcloud.cos.model.GetObjectRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.UUID;

/**
 * @Author:Oliver
 * @Date: 2025/6/20
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class InsuranceMediaAndAttachedServiceImpl extends ServiceImpl<InsuranceMediaAndAttachedMapper, InsuranceMediaAndAttached> implements InsuranceMediaAndAttachedService {

    @Value("${file.tencentcloudfile.bucketname:hti-ais-files-dev-1301376564}")
    private String fileBucketName;

    @Autowired
    private TencentCloudService tencentCloudService;
    @Autowired
    private AppFileCenterMapper appFileCenterMapper;

    @Override
    public InsuranceMediaAndAttached uploadAttached(MultipartFile file, Long id) {
        FileUtils.validateFile(file);
        //获取文件路径
        String fileUrl = FileUtils.getFilePath(file);
        //获取文件名
        String filename = file.getOriginalFilename();
        int i = filename.lastIndexOf(".");
        int j = fileUrl.lastIndexOf("/");
        //获取后缀名
        String substring = filename.substring(i, filename.length()).toLowerCase();
        //获取目标文件名称
        String ossPath = subString(fileUrl);
        //获取目标文件名称
        String targetFileName = fileUrl.substring(j + 1, fileUrl.length());
        //上传文件
        Boolean b = tencentCloudService.uploadObject(fileBucketName, file, ossPath);
        if (!b) {
//            throw new GetServiceException("上传文件失败");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "INSURANCE_FILE_UPLOAD_FAIL", "上传文件失败"));
        }
        //app文件中心新增
        String fileGuid = UUID.randomUUID().toString().replaceAll("-", "");
        AppFileCenter appFile = AppFileCenter.builder()
                .filePath(fileUrl)
                .fileNameOrc(filename)
                .fileTypeOrc(substring)
                .fileName(targetFileName)
                .fileGuid(fileGuid)
                .fileKey(ossPath)
                .build();
        appFileCenterMapper.insertAppFile(appFile, SecureUtil.getLoginId());
        //附件信息新增
        InsuranceMediaAndAttached mediaAndAttached = InsuranceMediaAndAttached.builder()
                .fkFileGuid(fileGuid)
                .fkTableName("m_settlement_bill")
                .fkTableId(id)
                .typeKey("m_settlement_bill")
                .link(fileUrl)
                .build();
        mediaAndAttached.setGmtCreate(new Date());
        mediaAndAttached.setGmtModified(new Date());
        mediaAndAttached.setGmtCreateUser(SecureUtil.getLoginId());
        mediaAndAttached.setGmtModifiedUser(SecureUtil.getLoginId());
        this.save(mediaAndAttached);
        return mediaAndAttached;
    }

    @Override
    public void downloadFile(AppFileCenter appFileCenter, HttpServletResponse response) {
        //查询fileKey
        String fileName = appFileCenter.getFileNameOrc();
        if (StringUtils.isBlank(appFileCenter.getFileNameOrc())) {
            fileName = UUID.randomUUID().toString().replace("-", "").substring(0, 10);
        }

        response.setCharacterEncoding("UTF-8");
        GetObjectRequest getObjectRequest = new GetObjectRequest(fileBucketName, appFileCenter.getFileKey());
        BufferedOutputStream outputStream = null;
        COSObjectInputStream cosObjectInput = null;
        try {
            COSObject cosObject = null;

            cosObject = ConnectTencentCloud.getPublicCosClient().getObject(getObjectRequest);

            cosObjectInput = cosObject.getObjectContent();
            outputStream = new BufferedOutputStream(response.getOutputStream());

            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(new String(fileName.getBytes("utf-8"), "UTF-8")));
        } catch (CosServiceException e) {
            e.printStackTrace();
        } catch (CosClientException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        // 处理下载到的流
        // 这里是直接读取，按实际情况来处理
        byte[] bytes = null;
        try {
            bytes = IOUtils.toByteArray(cosObjectInput);
            outputStream.write(bytes);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            // 用完流之后一定要调用 close()
            try {
                cosObjectInput.close();
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        try {
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 在流没有处理完之前，不能关闭 cosClient
            // 确认本进程不再使用 cosClient 实例之后，关闭之
            ConnectTencentCloud.getPrivateCosClient().shutdown();
        }
    }

    static String subString(String url) {
        if (url.contains("target")) {
            url = url.replace("/data/project/get/file-center/target", "");
            url = url.replace("/data/project/get/app-file-center/target", "");
            return url;
        } else {
            return url;
        }
    }
}
