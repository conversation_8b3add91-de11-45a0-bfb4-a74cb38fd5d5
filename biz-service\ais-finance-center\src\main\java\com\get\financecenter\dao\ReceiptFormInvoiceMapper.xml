<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.ReceiptFormInvoiceMapper">
  <resultMap id="BaseResultMap" type="com.get.financecenter.entity.ReceiptFormInvoice">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_receipt_form_id" jdbcType="BIGINT" property="fkReceiptFormId" />
    <result column="fk_invoice_id" jdbcType="BIGINT" property="fkInvoiceId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insert" parameterType="com.get.financecenter.entity.ReceiptFormInvoice">
    insert into r_receipt_form_invoice (id, fk_receipt_form_id, fk_invoice_id, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkReceiptFormId,jdbcType=BIGINT}, #{fkInvoiceId,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.financecenter.entity.ReceiptFormInvoice">
    insert into r_receipt_form_invoice
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkReceiptFormId != null">
        fk_receipt_form_id,
      </if>
      <if test="fkInvoiceId != null">
        fk_invoice_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkReceiptFormId != null">
        #{fkReceiptFormId,jdbcType=BIGINT},
      </if>
      <if test="fkInvoiceId != null">
        #{fkInvoiceId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getfkInvoiceIdsByfkReceiptFormId" resultType="java.lang.Long">
    select fk_invoice_id from r_receipt_form_invoice where 1=1 and
      fk_receipt_form_id = #{fkReceiptFormId}
</select>
  <select id="getReceiptFormInvoiceByfkReceiptFormId" resultMap="BaseResultMap">
    select * from r_receipt_form_invoice where 1=1 and
      fk_receipt_form_id = #{fkReceiptFormId}
  </select>
    <select id="getLastReceiptFormIdByInvoiceId" resultType="java.lang.Long">
      SELECT
          f.id
      FROM
          r_receipt_form_invoice r
      INNER JOIN m_receipt_form f ON f.id = r.fk_receipt_form_id
      WHERE
          r.fk_invoice_id = #{id}
      ORDER BY
          r.gmt_create DESC
      LIMIT 1
    </select>

    <delete id="deleteByReceiptFormId">
    DELETE FROM r_receipt_form_invoice WHERE fk_receipt_form_id = #{fkReceiptFormId}
  </delete>
</mapper>