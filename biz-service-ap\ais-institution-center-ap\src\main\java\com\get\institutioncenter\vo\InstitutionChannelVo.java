package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @DATE: 2021/4/12
 * @TIME: 15:18
 * @Description:
 **/
@Data
public class InstitutionChannelVo extends BaseEntity {
    /**
     * 全称
     */
    @ApiModelProperty(value = "全称")
    private String fullName;

    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司ids")
    private String fkCompanyIds;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    //============实体类InstitutionChannel=============
    private static final long serialVersionUID = 1L;
    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @Column(name = "num")
    private String num;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Column(name = "name")
    private String name;
    /**
     * 中文名称
     */
    @ApiModelProperty(value = "中文名称")
    @Column(name = "name_chn")
    private String nameChn;
}
