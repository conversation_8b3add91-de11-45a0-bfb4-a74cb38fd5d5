package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.vo.ClientContactPersonVo;
import com.get.salecenter.service.IClientContactPersonService;
import com.get.salecenter.dto.ClientContactPersonDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * author:Neil
 * Time: 11:22
 * Date: 2022/8/19
 * Description:
 */
@Api(tags = "客户联系人管理")
@RestController
@RequestMapping("sale/clientContactPerson")
public class ClientContactPersonController {


    @Resource
    private IClientContactPersonService clientContactPersonService;


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [ClientContactPersonDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/咨询客户联系人管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(ClientContactPersonDto.Add.class) ClientContactPersonDto contactPersonVo) {
        return SaveResponseBo.ok(clientContactPersonService.addContactPerson(contactPersonVo));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ClientContactPersonDto>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/客户联系人管理/查询")
    @PostMapping("datas")
    public ResponseBo<ClientContactPersonVo> datas(@RequestBody SearchBean<ClientContactPersonDto> page) {
        List<ClientContactPersonVo> datas = clientContactPersonService.getContactPersons(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生联系人管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        clientContactPersonService.delete(id);
        return DeleteResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentContactPersonVo>
     * @Description: 修改信息
     * @Param [StudentContactPersonDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生联系人管理/更新")
    @PostMapping("update")
    public ResponseBo<ClientContactPersonVo> update(@RequestBody @Validated(ClientContactPersonDto.Update.class) ClientContactPersonDto clientContactPersonDto) {
        return UpdateResponseBo.ok(clientContactPersonService.updateContactPerson(clientContactPersonDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ClientContactPersonVo>
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/客户联系人管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<ClientContactPersonVo> detail(@PathVariable("id") Long id) {
        ClientContactPersonVo data = clientContactPersonService.findContactPersonById(id);
        return new ResponseBo<>(data);
    }

}
