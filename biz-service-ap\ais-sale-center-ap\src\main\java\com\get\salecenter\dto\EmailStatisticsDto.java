package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/11/14 10:53
 * @verison: 1.0
 * @description:
 */
@Data
public class EmailStatisticsDto {

    @NotNull(message = "开学时间开始范围")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开学时间开始")
    private Date openingTimeStart;

    @NotNull(message = "开学时间结束范围")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开学时间结束")
    private Date openingTimeEnd;

    @NotNull(message = "公司id")
    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

    @NotNull(message = "员工id")
    @ApiModelProperty(value = "员工id")
    private Long staffId;

    @ApiModelProperty(value = "学生ids")
    private List<Long> studentIds;


}
