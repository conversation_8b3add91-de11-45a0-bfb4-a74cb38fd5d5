package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.institutioncenter.service.IBatchUpdateService;
import com.get.institutioncenter.dto.BatchUpdateDto;
import com.get.permissioncenter.dto.BatchModifyConfigDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @author: Sea
 * @create: 2021/3/1 12:15
 * @verison: 1.0
 * @description:批量修改操作管理
 */
@Api(tags = "批量修改管理")
@RestController
@RequestMapping("/institution/batchUpdate")
public class BatchUpdateController {
    @Resource
    private IBatchUpdateService batchUpdateService;

    /**
     * @Description :加载课程批量修改列表接口
     * @Param [courseBatchModifyVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "加载课程批量修改列表接口", notes = "返回的map中：自己定义的常量col 加上批量修改配置的id组成key")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/批量修改管理/加载课程批量修改列表接口")
    @PostMapping("getBatchUpdates")
    public ResponseBo<Map<String, String>> getBatchUpdates(@RequestBody BatchModifyConfigDto batchModifyConfigDto) {
        List<Map<String, String>> datas = batchUpdateService.getBatchUpdates(batchModifyConfigDto);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :批量修改接口
     * @Param [batchUpdateVoList]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/批量修改管理/批量修改接口")
    @PostMapping("batchUpdate")
    public ResponseBo batchUpdate(@RequestBody List<BatchUpdateDto> batchUpdateVoList) {
        batchUpdateService.batchUpdate(batchUpdateVoList);
        return ResponseBo.ok();
    }
}
