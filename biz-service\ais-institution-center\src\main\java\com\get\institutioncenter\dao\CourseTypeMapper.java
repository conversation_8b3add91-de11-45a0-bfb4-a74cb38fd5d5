package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.institutioncenter.entity.CourseType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CourseTypeMapper extends BaseMapper<CourseType> {

    int insertSelective(CourseType record);

    /**
     * @Description：获取最大排序字段
     * @Param
     * @Date 14:32 2021/4/27
     * <AUTHOR>
     */
    Integer getMaxViewOrder();

    /**
     * @Description：获取课程类型下拉框
     * @Param
     * @Date 14:32 2021/4/27
     * <AUTHOR>
     */
    List<BaseSelectEntity> getCourseTypeList();


    List<BaseSelectEntity> getCourseTypeListByKeyword(String keyword);

    /**
     * @Description：通过mode获取课程类型下拉框
     * @Param
     * @Date 14:32 2021/4/27
     * <AUTHOR>
     */
    List<BaseSelectEntity> getCourseTypeListByMode();

    /**
     * @Description：id获取名称
     * @Param
     * @Date 14:32 2021/4/27
     * <AUTHOR>
     */
    String getNameById(Long id);

    /**
     * @Description：条件获取课程类型下拉框
     * @Param
     * @Date 14:32 2021/4/27
     * <AUTHOR>
     */
    List<CourseType> selectCourseTypeList(IPage<CourseType> pages, @Param("keyWord") String keyWord, @Param("courseTypeGroupIds") List<Long> courseTypeGroupIds,@Param("strPublic") List<String> strPublic);
}