package com.get.insurancecenter.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/7/25
 * @Version 1.0
 * @apiNote:
 */
@Data
public class MoveOrderDto {

    @ApiModelProperty(value = "开始的viewOrder")
    @NotNull(message = "开始viewOrder不能为空")
    private Integer start;

    @ApiModelProperty(value = "结束的viewOrder")
    @NotNull(message = "结束viewOrder不能为空")
    private Integer end;
}
