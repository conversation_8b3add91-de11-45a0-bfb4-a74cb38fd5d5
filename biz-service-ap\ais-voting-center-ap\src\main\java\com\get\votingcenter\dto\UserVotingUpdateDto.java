package com.get.votingcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @author: Hardy
 * @create: 2021/9/27 16:35
 * @verison: 1.0
 * @description:
 */
@Data
public class UserVotingUpdateDto extends BaseVoEntity implements Serializable {
    /**
     * 注册中心的user id
     */
    @ApiModelProperty(value = "注册中心的user id")
    @NotNull(message = "用户id不能为空", groups = {Add.class, Update.class})
    private Long fkUserId;

    /**
     * 投票选项Id
     */
    @ApiModelProperty(value = "投票选项Id")
    @NotNull(message = "投票选项Id不能为空", groups = {Add.class, Update.class})
    private Long fkVotingItemOptionId;

}
