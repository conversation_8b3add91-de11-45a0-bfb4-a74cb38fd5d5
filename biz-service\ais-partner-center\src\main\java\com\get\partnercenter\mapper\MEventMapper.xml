<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.partnercenter.mapper.MEventMapper">

    <resultMap id="BaseResultMap" type="com.get.partnercenter.entity.MEventEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkCompanyId" column="fk_company_id" jdbcType="BIGINT"/>
            <result property="fkEventTypeId" column="fk_event_type_id" jdbcType="BIGINT"/>
            <result property="num" column="num" jdbcType="VARCHAR"/>
            <result property="eventTime" column="event_time" jdbcType="TIMESTAMP"/>
            <result property="eventTimeEnd" column="event_time_end" jdbcType="TIMESTAMP"/>
            <result property="eventTheme" column="event_theme" jdbcType="VARCHAR"/>
            <result property="eventTarget" column="event_target" jdbcType="VARCHAR"/>
            <result property="eventTargetLeader" column="event_target_leader" jdbcType="VARCHAR"/>
            <result property="fkAreaCountryIdHold" column="fk_area_country_id_hold" jdbcType="BIGINT"/>
            <result property="fkAreaStateIdHold" column="fk_area_state_id_hold" jdbcType="BIGINT"/>
            <result property="fkAreaCityIdHold" column="fk_area_city_id_hold" jdbcType="BIGINT"/>
            <result property="fkStaffIdLeader1" column="fk_staff_id_leader1" jdbcType="BIGINT"/>
            <result property="fkStaffIdLeader2" column="fk_staff_id_leader2" jdbcType="BIGINT"/>
            <result property="fkCurrencyTypeNum" column="fk_currency_type_num" jdbcType="VARCHAR"/>
            <result property="bdVenueAmount" column="bd_venue_amount" jdbcType="DECIMAL"/>
            <result property="bdFoodAmount" column="bd_food_amount" jdbcType="DECIMAL"/>
            <result property="bdPrizeAmount" column="bd_prize_amount" jdbcType="DECIMAL"/>
            <result property="bdOtherAmount" column="bd_other_amount" jdbcType="DECIMAL"/>
            <result property="budgetAmount" column="budget_amount" jdbcType="DECIMAL"/>
            <result property="actualAmount" column="actual_amount" jdbcType="DECIMAL"/>
            <result property="budgetCount" column="budget_count" jdbcType="INTEGER"/>
            <result property="attendedCount" column="attended_count" jdbcType="INTEGER"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="isActive" column="is_active" jdbcType="BIT"/>
            <result property="publicLevel" column="public_level" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

        <select id="searchPage" resultType="com.get.partnercenter.vo.MEventVo">
            SELECT mEvent.*,
                   uAreaCountry.name_chn               AS areaCountryName,
                   uAreaState.name_chn               AS areaStateName,
                   uAreaCity.name_chn            AS areaCityName,
                   uEventType.type_name          AS typeName,
                   (SELECT  CONCAT(#{query.mMageAddress}, partnerFile.file_key) FROM app_partner_center.s_media_and_attached sAttached
                           INNER JOIN app_file_center.m_file_partner partnerFile ON partnerFile.file_guid = sAttached.fk_file_guid
                           WHERE sAttached.fk_table_name='m_event'  AND  sAttached.type_key='m_event_partner_logo'
                             AND sAttached.fk_table_id=mEvent.id limit 1
                   ) AS file_key,
                   company.num AS companyName,
                   (SELECT `comment` from ais_sale_center.s_comment sComment
                    WHERE fk_table_name='m_event' and fk_table_id=mEvent.id limit 1) AS comment,
                   mStaff.name                   AS tartgerName

                   FROM ais_sale_center.m_event mEvent
                        LEFT JOIN ais_institution_center.u_area_country uAreaCountry ON uAreaCountry.id = mEvent.fk_area_country_id_hold
                        LEFT JOIN ais_institution_center.u_area_state uAreaState ON uAreaState.id = mEvent.fk_area_state_id_hold
                        LEFT JOIN ais_institution_center.u_area_city uAreaCity ON uAreaCity.id = mEvent.fk_area_city_id_hold
                        LEFT JOIN ais_sale_center.u_event_type uEventType ON uEventType.id = mEvent.fk_event_type_id
                        LEFT JOIN ais_permission_center.m_company company ON mEvent.fk_company_id=company.id
                        LEFT JOIN ais_permission_center.m_staff mStaff    ON mStaff.id=mEvent.fk_staff_id_leader1
                   WHERE  FIND_IN_SET(13, mEvent.public_level)
            <if test="query.fkCompanyId!=null ">
                AND mEvent.fk_company_id=#{query.fkCompanyId}
            </if>


            <if test="query.fkAreaCountryIdHold!=null ">
                AND mEvent.fk_area_country_id_hold=#{query.fkAreaCountryIdHold}
            </if>
            <if test="query.fkAreaStateIdHold!=null ">
                AND mEvent.fk_area_state_id_hold=#{query.fkAreaStateIdHold}
            </if>
            <if test="query.fkAreaCityIdHold!=null ">
                AND mEvent.fk_area_city_id_hold=#{query.fkAreaCityIdHold}
            </if>

            <if test="query.isActive!=null ">
                AND mEvent.is_active=#{query.isActive}
            </if>
            <if test="query.status!=null ">
                AND mEvent.status=#{query.status}
            </if>



            <if test="query.num!=null and query.num!='' ">
                AND mEvent.num=#{query.num}
            </if>

            <if test="query.eventTargetLeader!=null ">
                AND mEvent.event_target_leader like concat('%',#{query.eventTargetLeader},'%')
            </if>



            <if test="query.eventTheme != null and query.eventTheme != '' ">
                AND position(#{query.eventTheme,jdbcType=VARCHAR} IN mEvent.event_theme)
            </if>



            <if test="query.eventTime!=null and query.eventTimeEnd!=null">
                AND mEvent.event_time BETWEEN #{query.eventTime} AND #{query.eventTimeEnd}
            </if>


            <if test="query.fkEventTypeIdArray != null and query.fkEventTypeIdArray.length > 0">
                AND mEvent.fk_event_type_id IN
                <foreach collection="query.fkEventTypeIdArray" item="fkEventTypeId" open="(" separator="," close=")">
                    #{fkEventTypeId}
                </foreach>
            </if>





            ORDER BY mEvent.is_active DESC,mEvent.gmt_create ASC

    </select>

    <select id="selectByDetail" resultType="com.get.partnercenter.vo.MEventVo">
            SELECT mEvent.*,
                   uAreaCountry.name_chn               AS areaCountryName,
                   uAreaState.name_chn               AS areaStateName,
                   uAreaCity.name_chn            AS areaCityName,
                   uEventType.type_name          AS typeName,
                   (SELECT  CONCAT(#{mMageAddress}, partnerFile.file_key) FROM app_partner_center.s_media_and_attached sAttached
                                                                                             INNER JOIN app_file_center.m_file_partner partnerFile ON partnerFile.file_guid = sAttached.fk_file_guid
                    WHERE sAttached.fk_table_name='m_event'  AND  sAttached.type_key='m_event_partner_logo'
                      AND sAttached.fk_table_id=mEvent.id limit 1
                   ) AS file_key,
                   (SELECT `comment` from ais_sale_center.s_comment sComment
                    WHERE fk_table_name='m_event' and fk_table_id=mEvent.id limit 1) AS comment

            FROM ais_sale_center.m_event mEvent
                         LEFT JOIN ais_institution_center.u_area_country uAreaCountry ON uAreaCountry.id = mEvent.fk_area_country_id_hold
                         LEFT JOIN ais_institution_center.u_area_state uAreaState ON uAreaState.id = mEvent.fk_area_state_id_hold
                         LEFT JOIN ais_institution_center.u_area_city uAreaCity ON uAreaCity.id = mEvent.fk_area_city_id_hold
                         LEFT JOIN ais_sale_center.u_event_type uEventType ON uEventType.id = mEvent.fk_event_type_id
            WHERE  FIND_IN_SET(13, mEvent.public_level)
                 AND mEvent.id= #{id}


    </select>


    <update id="updateByIds">
            UPDATE m_event
            <if test="type!=null and type==0">
                    SET is_active=0
            </if>
            <if test="type!=null and type==1">
                    SET is_active=1
            </if>
            WHERE
            id IN
            <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                    #{id}
            </foreach>
    </update>


    <select id="searchRegistration" resultType="com.get.partnercenter.vo.MEventRegistrationAgentVo">
        SELECT mEventRegistrationAgent.id,
               mEventRegistrationAgent.gmt_create,
               mAgent.name  AS agentName,
               mPartnerUser.name AS partnerUserName,
               mPartnerUser.fk_user_login_id,
               mEventRegistrationAgent.fk_agent_id,
               mEventRegistrationAgent.fk_partner_user_id,
               mEventRegistrationAgent.status,

               mEventRegistrationAgent.name,
               mEventRegistrationAgent.mobile,
               mEventRegistrationAgent.people_count,

               systemRole.role_name,
               systemRole.role_name_en,
               company.num AS companyName,
               mStaff.num AS mStaffNum,
               mStaff.name ASmStaffName
        FROM ais_sale_center.m_event mEvent
            INNER JOIN ais_sale_center.m_event_registration_agent mEventRegistrationAgent ON mEvent.id=mEventRegistrationAgent.fk_event_id
            INNER JOIN ais_sale_center.m_agent mAgent ON mAgent.id=mEventRegistrationAgent.fk_agent_id
            INNER JOIN app_partner_center.m_partner_user mPartnerUser  ON mPartnerUser.id=mEventRegistrationAgent.fk_partner_user_id
            LEFT JOIN  app_system_center.system_user_role systemUseRole ON mPartnerUser.fk_user_id=systemUseRole.fk_user_id
            LEFT JOIN  app_system_center.system_role systemRole ON systemRole.id=systemUseRole.fk_role_id
            LEFT JOIN ais_permission_center.m_company company ON mPartnerUser.fk_company_id=company.id
            LEFT JOIN ais_sale_center.r_agent_staff rAgentStaff ON mAgent.id=rAgentStaff.fk_agent_id and rAgentStaff.is_active=1
            LEFT JOIN ais_permission_center.m_staff mStaff ON rAgentStaff.fk_staff_id=mStaff.id and  mStaff.is_active=1


        WHERE mEvent.id=#{query.id}




    </select>


</mapper>
