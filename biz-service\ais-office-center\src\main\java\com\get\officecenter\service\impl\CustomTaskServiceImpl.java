package com.get.officecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.officecenter.constant.TaskEnum;
import com.get.officecenter.vo.CustomExportTaskVo;
import com.get.officecenter.vo.CustomTaskVo;
import com.get.officecenter.entity.Comment;
import com.get.officecenter.entity.Task;
import com.get.officecenter.entity.TaskItem;
import com.get.officecenter.mapper.CommentMapper;
import com.get.officecenter.mapper.CustomTaskMapper;
import com.get.officecenter.mapper.TaskItemMapper;
import com.get.officecenter.service.CommentService;
import com.get.officecenter.service.CustomTaskService;
import com.get.officecenter.service.ITaskItemService;
import com.get.officecenter.utils.MyStringUtils;
import com.get.officecenter.dto.CommentDto;
import com.get.officecenter.dto.CreateTaskAndTaskItemDto;
import com.get.officecenter.dto.CustomTaskSearchDto;
import com.get.officecenter.dto.CustomTaskDto;
import com.get.officecenter.vo.TaskStatisticsExportVo;
import com.get.officecenter.vo.TaskStatisticsVo;
import com.get.permissioncenter.vo.DepartmentAndStaffVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.dto.RemindTaskDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.remindercenter.vo.RemindTaskVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.get.core.tool.utils.DateUtil.now;

/**
 * @Author:cream
 * @Date: 2023/5/30  9:46
 */
@Service
public class CustomTaskServiceImpl implements CustomTaskService {

    @Resource
    private UtilService<Object> utilService;

    @Resource
    private CustomTaskMapper customTaskMapper;

    @Resource
    private CommentService commentService;


    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private TaskItemMapper taskItemMapper;
    @Resource
    private ITaskItemService taskItemService;
    @Resource
    private CommentMapper commentMapper;

    @Resource
    private IReminderCenterClient reminderCenterClient;

    /**
     * Author Cream
     * Description : //新增任务
     * Date 2023/5/30 10:02
     * Params:
     * Return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SaveResponseBo addTask(CustomTaskDto customTaskDto) {
        Task task = new Task();
        task.setFkStaffIdFrom(SecureUtil.getStaffId());
        task.setFkStaffIdTo(customTaskDto.getFkStaffIdTo());
        task.setStatus(0);
        task.setTaskDeadline(customTaskDto.getTaskDeadline());
        if (GeneralTool.isNotEmpty(customTaskDto.getTaskDescription())) {
            try {
                String decodedDesc = URLDecoder.decode(
                        customTaskDto.getTaskDescription(),
                        StandardCharsets.UTF_8.name()
                );
                task.setTaskDescription(decodedDesc);
            } catch (UnsupportedEncodingException e) {
                // 实际上不会发生，因为 UTF-8 是支持的
                throw new RuntimeException("UTF-8 not supported", e);
            }
        }

        if (GeneralTool.isNotEmpty(customTaskDto.getTaskJsonList())) {
            customTaskDto.getTaskJsonList().removeIf(str -> str == null || str.trim().isEmpty() || str.equals("\"\""));
            String taskJsonList = JSON.toJSONString(customTaskDto.getTaskJsonList());
            task.setTaskJson(taskJsonList);
        }
        utilService.setCreateInfo(task);
        customTaskMapper.insert(task);
        String taskNum = MyStringUtils.getCustomTaskNum(task.getId());
        task.setNum(taskNum);
        customTaskMapper.updateById(task);
        //发送邮件任务
        Date now   = new Date();
        if(GeneralTool.isNotEmpty(task.getTaskDeadline())&&task.getTaskDeadline().compareTo(now) > 0){
            List<Integer> list = new ArrayList<>();
            list.add(0);
            list.add(1);
            List<EmailSenderQueue> queueList = new ArrayList<>();
            List<Long> ids =new ArrayList<>();
            ids.add(task.getId());
            //List<TaskItem> taskItemList =taskItemService.getTaskItemList(ids);
            //获取指定发送人
            Long staffId = task.getFkStaffIdTo();
            //获取创建人
            StaffVo staffVo = permissionCenterClient.getStaffById(SecureUtil.getStaffId()).getData();
            //获取委派人
            Long staffIdFrom = task.getFkStaffIdFrom();
            StaffVo staffVoFrom = permissionCenterClient.getStaffById(staffIdFrom).getData();

            //获取中英文配置
            Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
            String versionValue2 = versionConfigMap.get(staffVo.getFkCompanyId());
            String title1 = "";
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            if(!versionValue2.equals("en")){
                title1 = "单人任务提醒："+staffVoFrom.getFullName()+"委派单人任务，【截止时间："+sdf.format(task.getTaskDeadline())+"】";
            }else {
                title1  = "Single Task Reminder："+staffVoFrom.getName()+"Delegate single person tasks,【Deadline："+sdf.format(task.getTaskDeadline())+"】";
            }
            //加入日程提醒
            List<RemindTaskDto> remindTaskVos = new ArrayList<>();

                RemindTaskDto remindTaskVo = new RemindTaskDto();
                remindTaskVo.setFkStaffId(staffId);
                remindTaskVo.setStartTime(now());
                remindTaskVo.setEndTime(task.getTaskDeadline());
                remindTaskVo.setTaskTitle(title1);
                remindTaskVo.setFkTableName(TableEnum.M_TASK.key);
                remindTaskVo.setFkTableId(task.getId());
                remindTaskVo.setFkDbName(ProjectKeyEnum.OFFICE_CENTER.key);
                remindTaskVo.setFkRemindEventTypeKey(EmailTemplateEnum.MULTI_USER_TASK_REMINDER.getEmailTemplateKey());
                remindTaskVos.add(remindTaskVo);


            Result<Boolean> result=reminderCenterClient.batchAddTask(remindTaskVos);
            if (!result.isSuccess()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage(result.getMessage()));
            }
            //  发送提醒任务
            for(Integer i : list){
                EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
                emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.MULTI_USER_TASK_REMINDER.getEmailTemplateKey());
                emailSenderQueue.setFkTableName(TableEnum.M_TASK.key);
                emailSenderQueue.setFkTableId(task.getId());
                emailSenderQueue.setEmailTitle(title1);
                emailSenderQueue.setFkDbName(ProjectKeyEnum.OFFICE_CENTER.key);
                if(i==0){
                    emailSenderQueue.setOperationTime(now());
                }else if(i==1){
                    emailSenderQueue.setOperationTime(task.getTaskDeadline());
                }
                queueList.add(emailSenderQueue);
            }
            Result<Boolean> bolleanResult =reminderCenterClient.batchAddEmailQueue(queueList);
            if (!bolleanResult.isSuccess()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage(bolleanResult.getMessage()));
            }
        }
        return SaveResponseBo.ok(task.getId());
    }

    /**
     * Author Cream
     * Description : //获取任务详情
     * Date 2023/5/30 14:14
     * Params:
     * Return
     * @param taskId
     */
    @Override
    public ResponseBo<CustomTaskVo> getTaskInfoById(Long taskId) {
        if (Objects.isNull(taskId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Task task = customTaskMapper.selectById(taskId);
        if (Objects.isNull(task)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("task_obj_null"));
        }
        Set<Long> staffIds = new HashSet<>(2);
        staffIds.add(task.getFkStaffIdFrom());
        staffIds.add(task.getFkStaffIdTo());
        List<DepartmentAndStaffVo> departmentAndStaffVoList = permissionCenterClient.getDepartmentAndStaffDtoByStaffIds(staffIds).getData();
        Map<Long, DepartmentAndStaffVo> staffDtoMap = departmentAndStaffVoList.stream().collect(Collectors.toMap(DepartmentAndStaffVo::getFkStaffId, Function.identity()));
        CustomTaskVo taskDto = new CustomTaskVo();
        BeanUtils.copyProperties(task,taskDto);
        Long fkStaffIdFrom = taskDto.getFkStaffIdFrom();
        Long fkStaffIdTo = taskDto.getFkStaffIdTo();
        Long staffId = SecureUtil.getStaffId();
        DepartmentAndStaffVo fromDto = staffDtoMap.get(fkStaffIdFrom);
        if (GeneralTool.isNotEmpty(fromDto)) {
            taskDto.setDelegationDepartmentName(fromDto.getDepartmentName());
            taskDto.setDelegation(fromDto.getStaffName());
        }
        DepartmentAndStaffVo toDto = staffDtoMap.get(fkStaffIdTo);
        if (GeneralTool.isNotEmpty(toDto)) {
            taskDto.setRecipientDepartmentName(toDto.getDepartmentName());
            taskDto.setRecipient(toDto.getStaffName());
        }
        if (staffId.equals(fkStaffIdFrom)) {
            taskDto.setTaskType(1);
        }else if (staffId.equals(fkStaffIdTo)){
            taskDto.setTaskType(2);
        }else {
            taskDto.setTaskType(3);
        }
        if (GeneralTool.isNotEmpty(task.getTaskJson())) {
            List<String> taskJsonList = JSON.parseArray(task.getTaskJson(), String.class);;
            taskDto.setTaskJsonList(taskJsonList);
        }


        return new ResponseBo<>(taskDto);
    }

    public CustomTaskVo getTaskById(Long taskId) {
        if (Objects.isNull(taskId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Task task = customTaskMapper.selectById(taskId);
        if (Objects.isNull(task)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("task_obj_null"));
        }
        Set<Long> staffIds = new HashSet<>(2);
        staffIds.add(task.getFkStaffIdFrom());
        staffIds.add(task.getFkStaffIdTo());
        List<DepartmentAndStaffVo> departmentAndStaffVoList = permissionCenterClient.getDepartmentAndStaffDtoByStaffIds(staffIds).getData();
        Map<Long, DepartmentAndStaffVo> staffDtoMap = departmentAndStaffVoList.stream().collect(Collectors.toMap(DepartmentAndStaffVo::getFkStaffId, Function.identity()));
        CustomTaskVo taskDto = new CustomTaskVo();
        BeanUtils.copyProperties(task,taskDto);
        Long fkStaffIdFrom = taskDto.getFkStaffIdFrom();
        Long fkStaffIdTo = taskDto.getFkStaffIdTo();
        Long staffId = SecureUtil.getStaffId();
        DepartmentAndStaffVo fromDto = staffDtoMap.get(fkStaffIdFrom);
        if (GeneralTool.isNotEmpty(fromDto)) {
            taskDto.setDelegationDepartmentName(fromDto.getDepartmentName());
            taskDto.setDelegation(fromDto.getStaffName());
        }
        DepartmentAndStaffVo toDto = staffDtoMap.get(fkStaffIdTo);
        if (GeneralTool.isNotEmpty(toDto)) {
            taskDto.setRecipientDepartmentName(toDto.getDepartmentName());
            taskDto.setRecipient(toDto.getStaffName());
        }
        if (staffId.equals(fkStaffIdFrom)) {
            taskDto.setTaskType(1);
        }else if (staffId.equals(fkStaffIdTo)){
            taskDto.setTaskType(2);
        }else {
            taskDto.setTaskType(3);
        }
        if (GeneralTool.isNotEmpty(task.getTaskJson())) {
            List<String> taskJsonList = JSON.parseArray(task.getTaskJson(), String.class);;
            taskDto.setTaskJsonList(taskJsonList);
        }

        return taskDto;
    }




    /**
     * 获取任务列表
     * @param customTaskSearchDto
     * @param page
     * @return
     */
    @Override
    public ListResponseBo<CustomTaskVo> taskList(CustomTaskSearchDto customTaskSearchDto, Page page) {
        IPage<CustomTaskVo> iPage = null;
        if(GeneralTool.isNotEmpty(page)){
         iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        }

        if (GeneralTool.isEmpty(customTaskSearchDto.getIsMyDelegation())) {
            customTaskSearchDto.setIsMyDelegation(false);
        }
        if (GeneralTool.isEmpty(customTaskSearchDto.getIsMyTask())) {
            customTaskSearchDto.setIsMyTask(false);
        }
        if (GeneralTool.isEmpty(customTaskSearchDto.getIsSubordinateTask())) {
            customTaskSearchDto.setIsSubordinateTask(false);
        }
        //如果为2就是超时任务，设置标识为 true，然后根据任务截止时间过滤
        if(GeneralTool.isNotEmpty(customTaskSearchDto.getTaskStatus())&&customTaskSearchDto.getTaskStatus()==2){
            customTaskSearchDto.setTaskStatus(null);
            customTaskSearchDto.setIsTimeout(true);
        }


        Long curStaffId = SecureUtil.getStaffId();
        // 登录人 + 下属Id
        List<Long> staffIds = Lists.newArrayList();
        // 当前登录人的下属id集合
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(curStaffId).getData();
        staffIds.addAll(staffFollowerIds);
        staffIds.add(curStaffId);
        long startTime = System.currentTimeMillis(); // 开始时间
        List<CustomTaskVo> taskDtoList = null;
        if (GeneralTool.isNotEmpty(page)) {
             taskDtoList = customTaskMapper.taskList(iPage, customTaskSearchDto, curStaffId, staffFollowerIds, staffIds);
            page.setAll((int) iPage.getTotal());
        }else {
            taskDtoList = customTaskMapper.taskList(null, customTaskSearchDto, curStaffId, staffFollowerIds, staffIds);
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime; // 执行时间，单位毫秒
        System.out.println("主任务方法执行耗时：" + duration + " 毫秒");
        if (taskDtoList.isEmpty()) {
            return new ListResponseBo<>(Collections.emptyList(), page);
        }
        //获取员工的个人信息
        Set<Long> formIds = taskDtoList.stream().map(CustomTaskVo::getFkStaffIdFrom).collect(Collectors.toSet());
        Set<Long> toIds = taskDtoList.stream().map(CustomTaskVo::getFkStaffIdTo).collect(Collectors.toSet());
        formIds.addAll(toIds);
        List<DepartmentAndStaffVo> departmentAndStaffVoList = permissionCenterClient.getDepartmentAndStaffDtoByStaffIds(formIds).getData();
        Map<Long, DepartmentAndStaffVo> staffDtoMap = departmentAndStaffVoList.stream().collect(Collectors.toMap(DepartmentAndStaffVo::getFkStaffId, Function.identity()));
        // 获取主任务的评论数据
//        List<Long> taskIds = taskDtoList.stream().map(CustomTaskVo::getId).collect(Collectors.toList());
//        long startTime = System.currentTimeMillis();
//        List<Comment> commentList = commentService.getCommentList(TableEnum.M_TASK.key, taskIds);
//        long endTime = System.currentTimeMillis();   // 记录结束时间
//        System.out.println("Comment耗时：" + (endTime - startTime) + " 毫秒");
//        Map<Long, List<Comment>> commentGroup = commentList.stream().collect(Collectors.groupingBy(Comment::getFkTableId));

        // 获取子任务列表
        //long startTime1 = System.currentTimeMillis();
//        List<TaskItem> taskItemList = taskItemService.getTaskItemList(taskIds);
        //long endTime1 = System.currentTimeMillis();
        //System.out.println("子任务列表耗时：" + (endTime1 - startTime1) + " 毫秒");
//        Map<Long, List<TaskItem>> taskItemMap = taskItemList.stream().collect(Collectors.groupingBy(TaskItem::getFkTaskId));

        // 获取子任务的评论数据中最新的评论
//        List<Long> taskItemIds = taskItemList.stream().map(TaskItem::getId).collect(Collectors.toList());
        //long startTime2 = System.currentTimeMillis();
        //List<Comment> taskItemCommentList = commentService.getCommentList(TableEnum.M_TASK_ITEM.key, taskItemIds);
//        List<Comment> taskItemCommentList = commentService.getCommentList(TableEnum.M_TASK_ITEM.key,taskIds);
//        long endTime2 = System.currentTimeMillis();
//        System.out.println("子任务列表评论耗时：" + (endTime2 - startTime2) + " 毫秒");
        // key：子任务id value：子任务评论中最新的评论
//        Map<Long, Comment> taskItemMaxCommentMap = Maps.newHashMap();
//        if (GeneralTool.isNotEmpty(taskItemCommentList)) {
//            Map<Long, List<Comment>> taskItemCommentMap = taskItemCommentList.stream().collect(Collectors.groupingBy(Comment::getFkTableId));
//            for (Map.Entry<Long, List<Comment>> entry : taskItemCommentMap.entrySet()) {
//                List<Comment> comments = GeneralTool.isNotEmpty(entry.getValue()) ? entry.getValue() : Collections.emptyList();
//                comments.removeIf(Objects::isNull);
//                if (GeneralTool.isNotEmpty(comments)) {
//                    // 最新的评论
//                    Comment maxComment = Collections.max(comments,
//                            Comparator.comparingLong(comment -> {
//                                // 防止空指针
//                                Long gmtCreate = Optional.ofNullable(comment.getGmtCreate()).map(Date::getTime).orElse(0L);
//                                Long gmtModified = Optional.ofNullable(comment.getGmtModified()).map(Date::getTime).orElse(0L);
//                                return Math.max(gmtCreate, gmtModified);
//                            })
//                    );
//                    taskItemMaxCommentMap.put(entry.getKey(), maxComment);
//                }
//            }
//        }

        for (CustomTaskVo taskDto : taskDtoList) {
            Long fkStaffIdFrom = taskDto.getFkStaffIdFrom();
            Long fkStaffIdTo = taskDto.getFkStaffIdTo();
            DepartmentAndStaffVo fromDto = staffDtoMap.get(fkStaffIdFrom);
            if (GeneralTool.isNotEmpty(fromDto)) {
                taskDto.setDelegationDepartmentName(fromDto.getDepartmentName());
                taskDto.setDelegation(fromDto.getStaffName());
            }
            if (curStaffId.equals(fkStaffIdFrom)) {
                taskDto.setTaskType(1);
            } else if (curStaffId.equals(fkStaffIdTo)) {
                taskDto.setTaskType(2);
            } else {
                taskDto.setTaskType(3);
            }

//            List<Comment> comments = commentGroup.getOrDefault(taskDto.getId(), Collections.emptyList());
//            comments.removeIf(Objects::isNull);
//            if (GeneralTool.isNotEmpty(comments)) {
//                Comment maxComment = Collections.max(comments,
//                        Comparator.comparingLong(comment -> {
//                            // 防止空指针
//                            Long gmtCreate = Optional.ofNullable(comment.getGmtCreate()).map(Date::getTime).orElse(0L);
//                            Long gmtModified = Optional.ofNullable(comment.getGmtModified()).map(Date::getTime).orElse(0L);
//                            return Math.max(gmtCreate, gmtModified);
//                        })
//                );
//                taskDto.setComment(maxComment.getComment());
//            }
            DepartmentAndStaffVo toDto = staffDtoMap.get(fkStaffIdTo);
            if (GeneralTool.isNotEmpty(toDto)) {
                taskDto.setRecipientDepartmentName(toDto.getDepartmentName());
                taskDto.setRecipient(toDto.getStaffName());
            }

            // 设置有子任务的相关属性
//            List<TaskItem/*> taskItems = taskItemMap.get(taskDto.getId());
//            List<TaskItem> taskItemList1 = null;
//            if(GeneralTool.isNotEmpty(taskItems)){
//                taskItemList1  =  taskItems.stream()
//                        .collect(Collectors.groupingBy(
//                                TaskItem::getFkTableId,
//                                Collectors.collectingAndThen(
//                                        Collectors.toList(),
//                                        list -> {
//                                            // 取第一条记录作为模板
//                                            TaskItem template = list.get(0);
//                                            TaskItem taskItem =new TaskItem();
//                                            BeanCopyUtils.copyProperties(template, taskItem);
//                                            return taskItem;
//                                        }
//                                )
//                        ))
//                        .values()
//                        .stream()
//                        .collect(Collectors.toList());
//            }*/
//            if (GeneralTool.isNotEmpty(taskItems)) {
//                Map<Integer, List<TaskItem>> statusMap = taskItemList1.stream().collect(Collectors.groupingBy(TaskItem::getStatus));
//                taskDto.setTaskItemTodoCount(statusMap.containsKey(0) ? statusMap.get(0).size() : 0);
//                taskDto.setTaskItemFinishedCount(statusMap.containsKey(1) ? statusMap.get(1).size() : 0);
//                taskDto.setTaskItemUnfinishedCount(statusMap.containsKey(2) ? statusMap.get(2).size() : 0);
//                // 子任务接收人id集合
//                List<Long> staffIdToList = taskItems.stream().map(TaskItem::getFkStaffIdTo).collect(Collectors.toList());
//                if (staffIdToList.contains(curStaffId)) { // 包含登录人的子任务
//                    taskDto.setTaskType(2);
//                } else if (staffFollowerIds.stream().anyMatch(staffIdToList::contains)) { // 只要存在一条我的下属id，返回true
//                    taskDto.setTaskType(3);
//                }
//
//                // 子任务最新的评论
//                List<Long> curTaskItemIds = taskItems.stream().map(TaskItem::getId).collect(Collectors.toList());
//                List<Comment> curTaskItemCommentList = curTaskItemIds.stream().map(taskItemMaxCommentMap::get).collect(Collectors.toList());
//                // 主任务的评论也需要比较
//                curTaskItemCommentList.addAll(comments);
//                curTaskItemCommentList.removeIf(Objects::isNull);
//                if (GeneralTool.isNotEmpty(curTaskItemCommentList)) {
//                    Comment maxComment = Collections.max(curTaskItemCommentList,
//                            Comparator.comparingLong(comment -> {
//                                // 防止空指针
//                                Long gmtCreate = Optional.ofNullable(comment.getGmtCreate()).map(Date::getTime).orElse(0L);
//                                Long gmtModified = Optional.ofNullable(comment.getGmtModified()).map(Date::getTime).orElse(0L);
//                                return Math.max(gmtCreate, gmtModified);
//                            })
//                    );
//                    taskDto.setComment(maxComment.getComment());
//                }
//            }
        }
        return new ListResponseBo<>(taskDtoList, BeanCopyUtils.objClone(page, Page::new));
    }

    /**
     * Author Cream
     * Description : //更新任务
     * Date 2023/5/30 10:16
     * Params:
     * Return
     */
    @Override
    public SaveResponseBo updateTask(CustomTaskDto customTaskDto) {
        Long taskId = customTaskDto.getTaskId();
        if (Objects.isNull(taskId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Task task = customTaskMapper.selectById(taskId);
        Date date = task.getTaskDeadline();
        if (Objects.isNull(task)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("task_obj_null"));
        }
        task.setFkStaffIdFrom(SecureUtil.getStaffId());
        task.setFkStaffIdTo(customTaskDto.getFkStaffIdTo());
        if (GeneralTool.isNotEmpty(customTaskDto.getTaskDescription())) {
            try {
                String decodedDesc = URLDecoder.decode(
                        customTaskDto.getTaskDescription(),
                        StandardCharsets.UTF_8.name()
                );
                task.setTaskDescription(decodedDesc);
            } catch (UnsupportedEncodingException e) {
                // 实际上不会发生，因为 UTF-8 是支持的
                throw new RuntimeException("UTF-8 not supported", e);
            }
        }

        String taskJson=null;
        if(GeneralTool.isNotEmpty(customTaskDto.getTaskJsonList())){
            customTaskDto.getTaskJsonList().removeIf(str -> str == null || str.trim().isEmpty() || str.equals("\"\""));
            taskJson=JSON.toJSONString(customTaskDto.getTaskJsonList());
        }
        task.setTaskDeadline(customTaskDto.getTaskDeadline());
        task.setTaskJson(taskJson);
        task.setFkStaffIdsView(customTaskDto.getFkStaffIdsView());
        utilService.setUpdateInfo(task);
        customTaskMapper.updateById(task);
        Date now   = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        //截止时间要晚于当前时间，并且不能修改成相同任务时间
        if(GeneralTool.isNotEmpty(task.getTaskDeadline())&&task.getTaskDeadline().compareTo(now) >= 0&&!task.getTaskDeadline().equals(date)){
            List<Integer> list = new ArrayList<>();
            list.add(0);
            list.add(1);
            List<EmailSenderQueue> queueList = new ArrayList<>();
            List<Long> ids =new ArrayList<>();
            ids.add(task.getId());
            Set<Long> staffIdList=new HashSet<>();
            //获取所有发送人
            if(GeneralTool.isNotEmpty(customTaskDto.getFkStaffIdTo())&&customTaskDto.getFkStaffIdTo()==-1){
                List<Long> taskItemList =taskItemService.getFkStaffIdTo(ids);
                staffIdList= taskItemList.stream().collect(Collectors.toSet());
            }else {
                if (GeneralTool.isNotEmpty(customTaskDto.getFkStaffIdTo())) {
                    staffIdList.add(customTaskDto.getFkStaffIdTo());
                }

            }
            //获取创建人
            StaffVo staffVo = permissionCenterClient.getStaffById(task.getFkStaffIdFrom()).getData();
            //获取中英文配置
            Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
            String versionValue2 = versionConfigMap.get(staffVo.getFkCompanyId());
            String title1 = "";

            if(!versionValue2.equals("en")){
                if (GeneralTool.isNotEmpty(task.getFkStaffIdTo())&&task.getFkStaffIdTo()==-1) {
                    title1 = "多人任务提醒："+staffVo.getFullName()+"委派多人任务，【截止时间："+sdf.format(task.getTaskDeadline())+"】";

                }else {
                    title1 = "单人任务提醒："+staffVo.getFullName()+"委派单人任务，【截止时间："+sdf.format(task.getTaskDeadline())+"】";

                }
            }else {
                if (GeneralTool.isNotEmpty(task.getFkStaffIdTo())&&task.getFkStaffIdTo()==-1) {
                    title1  = "Multi Person Task Reminder："+staffVo.getName()+"Delegate multi person tasks,【Deadline："+sdf.format(task.getTaskDeadline())+"】";
                }else {
                    title1  = "Single Task Reminder："+staffVo.getName()+"Delegate single person tasks,,【Deadline："+sdf.format(task.getTaskDeadline())+"】";
                }
            }

            //加入日程提醒
            List<RemindTaskDto> remindTaskVos = new ArrayList<>();
            for (Long id:staffIdList){
                RemindTaskDto remindTaskVo = new RemindTaskDto();
                remindTaskVo.setFkStaffId(id);
                remindTaskVo.setStartTime(now());
                remindTaskVo.setEndTime(task.getTaskDeadline());
                remindTaskVo.setTaskTitle(title1);
                remindTaskVo.setFkTableName(TableEnum.M_TASK.key);
                remindTaskVo.setFkTableId(task.getId());
                remindTaskVo.setFkDbName(ProjectKeyEnum.OFFICE_CENTER.key);
                remindTaskVo.setFkRemindEventTypeKey(EmailTemplateEnum.MULTI_USER_TASK_REMINDER.getEmailTemplateKey());
                remindTaskVos.add(remindTaskVo);
            }
            Result<Boolean> result=reminderCenterClient.batchUpdateTaskNew(remindTaskVos);
            if (!result.isSuccess()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage(result.getMessage()));
            }
            //  发送提醒任务
            for(Long id:staffIdList){
                EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
                emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.NEW_MULTI_USER_TASK_REMINDER.getEmailTemplateKey());
                emailSenderQueue.setFkTableName(TableEnum.M_TASK.key);
                emailSenderQueue.setFkTableId(task.getId());
                emailSenderQueue.setEmailParameter(String.valueOf(id));
                emailSenderQueue.setEmailTitle(title1);
                emailSenderQueue.setFkDbName(ProjectKeyEnum.OFFICE_CENTER.key);
                queueList.add(emailSenderQueue);
            }
            Result<Boolean> bolleanResult =reminderCenterClient.batchAddEmailQueue(queueList);
            if (!bolleanResult.isSuccess()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage(bolleanResult.getMessage()));
            }
        }
        return SaveResponseBo.ok(taskId);
    }
    /**
     * Author Cream
     * Description : //添加任务评论
     * Date 2023/5/30 12:42
     * Params:
     * Return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SaveResponseBo addTaskComment(Long taskId, String comment) {
        if (Objects.isNull(taskId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Task task = customTaskMapper.selectById(taskId);
        if (Objects.isNull(task)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("task_obj_null"));
        }
        CommentDto commentDto = new CommentDto();
        commentDto.setFkTableName(TableEnum.M_TASK.key);
        commentDto.setFkTableId(taskId);
        commentDto.setComment(comment);
        commentService.addComment(commentDto);
        utilService.setUpdateInfo(task);
        customTaskMapper.updateById(task);
        return SaveResponseBo.ok(taskId);
    }

    /**
     * Author Cream
     * Description : //获取任务评论列表
     * Date 2023/6/2 11:09
     * Params:
     * Return
     */
    @Override
    public ListResponseBo<Comment> getTaskCommentList(Long taskId, Page page) {
        if (Objects.isNull(taskId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<Comment> commentList = commentService.getCommentList(TableEnum.M_TASK.key, taskId, page);
        return new ListResponseBo<>(commentList,BeanCopyUtils.objClone(page,Page::new));
    }

    /**
     * Author Cream
     * Description : //删除任务评论
     * Date 2023/6/2 11:20
     * Params:
     * Return
     */
    @Override
    public DeleteResponseBo removeTaskComment(Long commentId) {
        commentService.removeComment(commentId);
        return new DeleteResponseBo();
    }

    /**
     * Author Cream
     * Description : //编辑任务评论
     * Date 2023/5/30 12:45
     * Params:
     * Return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SaveResponseBo updateTaskComment(Long taskId, Long commentId, String comment) {
        if (Objects.isNull(taskId) || Objects.isNull(commentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Task task = customTaskMapper.selectById(taskId);
        if (Objects.isNull(task)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("task_obj_null"));
        }
        CommentDto commentDto = new CommentDto();
        commentDto.setComment(comment);
        commentDto.setId(commentId);
        commentService.updateComment(commentDto);
        utilService.setUpdateInfo(task);
        customTaskMapper.updateById(task);
        return SaveResponseBo.ok(taskId);
    }


    /**
     * Author Cream
     * Description : //任务重新委派
     * Date 2023/5/30 10:02
     * Params:
     * Return
     */
    @Override
    public SaveResponseBo taskDelegation(CustomTaskDto customTaskDto) {
        Long taskId = customTaskDto.getTaskId();
        if (Objects.isNull(taskId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Task task = customTaskMapper.selectById(taskId);
        if (Objects.isNull(task)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("task_obj_null"));
        }
        if (1 == task.getStatus()) {
            task.setStatusModifiedTime(new Date());
        }
        if (GeneralTool.isNotEmpty(customTaskDto.getTaskDescription())) {
            try {
                String decodedDesc = URLDecoder.decode(
                        customTaskDto.getTaskDescription(),
                        StandardCharsets.UTF_8.name()
                );
                task.setTaskDescription(decodedDesc);
            } catch (UnsupportedEncodingException e) {
                // 实际上不会发生，因为 UTF-8 是支持的
                throw new RuntimeException("UTF-8 not supported", e);
            }
        }
        task.setFkStaffIdFrom(SecureUtil.getStaffId());
        task.setFkStaffIdTo(customTaskDto.getFkStaffIdTo());
        task.setTaskDeadline(customTaskDto.getTaskDeadline());
        if (GeneralTool.isNotEmpty(customTaskDto.getTaskJsonList())) {
            customTaskDto.getTaskJsonList().removeIf(str -> str == null || str.trim().isEmpty() || str.equals("\"\""));
            String taskJsonList = JSON.toJSONString(customTaskDto.getTaskJsonList());
            task.setTaskJson(taskJsonList);
        }
        //task.setTaskDescription(customTaskDto.getTaskDescription());
        task.setStatus(0);
        utilService.setUpdateInfo(task);
        customTaskMapper.updateById(task);
        return SaveResponseBo.ok(taskId);
    }

    /**
     * Author Cream
     * Description : //任务设置完成
     * Date 2023/5/30 10:04
     * Params:
     * Return
     */
    @Override
    public SaveResponseBo taskCompletion(Long taskId) {
        Task task = customTaskMapper.selectById(taskId);
        if (Objects.isNull(task)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("task_obj_null"));
        }
        Integer originalState = task.getStatus();
        if (1 == originalState) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("task_completed"));
        }
        task.setStatus(1);
        task.setStatusModifiedTime(new Date());
        utilService.setUpdateInfo(task);
        customTaskMapper.updateById(task);
        return SaveResponseBo.ok(taskId);
    }

    /**
     * Author Cream
     * Description : //任务删除
     * Date 2023/5/30 10:19
     * Params:
     * Return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public DeleteResponseBo taskDelete(Long taskId) {
        if (Objects.isNull(taskId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Task task = customTaskMapper.selectById(taskId);
        if (Objects.isNull(task)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("task_obj_null"));
        }
        // 只要有一条子任务状态是非待处理（!=0），则不能删除
        List<TaskItem> taskItemList = taskItemMapper.selectList(Wrappers.<TaskItem>lambdaQuery().eq(TaskItem::getFkTaskId, taskId));
        if (GeneralTool.isNotEmpty(taskItemList)) {
            long count = taskItemList.stream().filter(taskItem -> taskItem.getStatus() != 0).count();
            if (count >= 1) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("exist_processed_task_item_cannot_delete"));
            }
            Set<Long> taskItemIds = taskItemList.stream().map(TaskItem::getId).collect(Collectors.toSet());
            // 删除所有子任务及子任务反馈意见
            final int batchSize = 1000;
            List<Long> taskItemIdsList = new ArrayList<>(taskItemIds);
            for (int i = 0; i < taskItemIdsList.size(); i += batchSize) {
                int end = Math.min(i + batchSize, taskItemIdsList.size());
                List<Long> subList = taskItemIdsList.subList(i, end);
                taskItemMapper.deleteBatchIds(subList);
                commentService.deleteComment(TableEnum.M_TASK_ITEM.key, new HashSet<>(subList));
            }
        }
        customTaskMapper.deleteById(taskId);
        commentService.deleteComment(TableEnum.M_TASK.key, Collections.singleton(taskId));
        return new DeleteResponseBo();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean addTaskAndTaskItem(CreateTaskAndTaskItemDto createTaskAndTaskItemDto) {
        // 添加主任务
        Task task = new Task();
        task.setFkStaffIdFrom(SecureUtil.getStaffId());
        task.setFkStaffIdTo(-1L);
        task.setStatus(0);
        task.setFkStaffIdsView(createTaskAndTaskItemDto.getFkStaffIdsView());
        task.setTaskDeadline(createTaskAndTaskItemDto.getTaskDeadline());
        task.setTaskDescription(createTaskAndTaskItemDto.getTaskDescription());
        task.setRemark(createTaskAndTaskItemDto.getRemark());
        task.setTaskJson(createTaskAndTaskItemDto.getTaskJson());
        utilService.setCreateInfo(task);
        customTaskMapper.insert(task);
        String taskNum = MyStringUtils.getCustomTaskNum(task.getId());
        task.setNum(taskNum);
        customTaskMapper.updateById(task);

        // 添加子任务
        List<TaskItem> taskItemList = createTaskAndTaskItemDto.getTaskItemList();
        if (GeneralTool.isEmpty(taskItemList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_task_item_null"));
        }
        Date  now = new Date();
        for (TaskItem taskItem : taskItemList) {
            taskItem.setFkTaskId(task.getId());
            taskItem.setStatus(0);
            utilService.setCreateInfo(taskItem);
//            taskItemMapper.insert(taskItem);
        }
        //批量插入
        batchInsertTaskItems(taskItemList, 1000);
        if(GeneralTool.isNotEmpty(createTaskAndTaskItemDto.getTaskDeadline())&&createTaskAndTaskItemDto.getTaskDeadline().compareTo(now) >=0){
            List<Integer> list = new ArrayList<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            list.add(0);
            list.add(1);
            List<EmailSenderQueue> queueList = new ArrayList<>();
            //获取所有发送人
            Set<Long> staffIdList = taskItemList.stream().map(TaskItem::getFkStaffIdTo).collect(Collectors.toSet());
            //获取创建人
            StaffVo staffVo = permissionCenterClient.getStaffById(task.getFkStaffIdFrom()).getData();
            //获取中英文配置
            Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
            String versionValue2 = versionConfigMap.get(staffVo.getFkCompanyId());
            String title1 = "";
            if(!versionValue2.equals("en")){
                title1 = "多人任务提醒："+staffVo.getFullName()+"委派多人任务，【截止时间："+sdf.format(task.getTaskDeadline())+"】";
            }else {
                title1  = "Multi Person Task Reminder："+staffVo.getName()+"Delegate multi person tasks,【Deadline："+sdf.format(task.getTaskDeadline())+"】";
            }
            //加入日程提醒
            List<RemindTaskDto> remindTaskVos = new ArrayList<>();
            for (Long id:staffIdList){
                RemindTaskDto remindTaskVo = new RemindTaskDto();
                remindTaskVo.setFkStaffId(id);
                remindTaskVo.setStartTime(now());
                remindTaskVo.setEndTime(createTaskAndTaskItemDto.getTaskDeadline());
                remindTaskVo.setTaskTitle(title1);
                remindTaskVo.setFkTableName(TableEnum.M_TASK.key);
                remindTaskVo.setFkTableId(task.getId());
                remindTaskVo.setFkDbName(ProjectKeyEnum.OFFICE_CENTER.key);
                remindTaskVo.setFkRemindEventTypeKey(EmailTemplateEnum.MULTI_USER_TASK_REMINDER.getEmailTemplateKey());
                remindTaskVos.add(remindTaskVo);
            }

            Result<Boolean> result=reminderCenterClient.batchAddTask(remindTaskVos);
            if (!result.isSuccess()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage(result.getMessage()));
            }
            //  发送提醒任务
            for(Long id:staffIdList){
                EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
                emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.NEW_MULTI_USER_TASK_REMINDER.getEmailTemplateKey());
                emailSenderQueue.setFkTableName(TableEnum.M_TASK.key);
                emailSenderQueue.setFkTableId(task.getId());
                emailSenderQueue.setEmailParameter(String.valueOf(id));
                emailSenderQueue.setEmailTitle(title1);
                emailSenderQueue.setFkDbName(ProjectKeyEnum.OFFICE_CENTER.key);
                emailSenderQueue.setOperationTime(now());
               queueList.add(emailSenderQueue);
            }
            Result<Boolean> bolleanResult =reminderCenterClient.batchAddEmailQueue(queueList);
            if (!bolleanResult.isSuccess()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage(bolleanResult.getMessage()));
            }
        }
        return true;
    }

    @Override
    public ListResponseBo<DepartmentAndStaffVo> getTaskDepartment(Integer type) {
        List<DepartmentAndStaffVo> departmentAndStaffVos = new ArrayList<>();
        Long curStaffId = SecureUtil.getStaffId();
        // 登录人 + 下属Id
        List<Long> staffIds = Lists.newArrayList();
        // 当前登录人的下属id集合
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(curStaffId).getData();
        staffIds.addAll(staffFollowerIds);
        staffIds.add(curStaffId);
        CustomTaskSearchDto customTaskSearchDto = new CustomTaskSearchDto();
        if (GeneralTool.isEmpty(customTaskSearchDto.getIsMyDelegation())) {
            customTaskSearchDto.setIsMyDelegation(false);
        }
        if (GeneralTool.isEmpty(customTaskSearchDto.getIsMyTask())) {
            customTaskSearchDto.setIsMyTask(false);
        }
        if (GeneralTool.isEmpty(customTaskSearchDto.getIsSubordinateTask())) {
            customTaskSearchDto.setIsSubordinateTask(false);
        }
        List<CustomTaskVo> taskList = customTaskMapper.taskList(null, customTaskSearchDto, curStaffId, staffFollowerIds, staffIds);
        //获取所有任务
        //List<Task> taskList = customTaskMapper.selectList(new LambdaQueryWrapper<Task>());
        Set<Long> fkStaffIdList = null;
        if(GeneralTool.isNotEmpty(type)){
            if(type == 1){ //委派人部门
                fkStaffIdList= taskList.stream().map(CustomTaskVo::getFkStaffIdFrom).collect(Collectors.toSet());
            } else if (type==0) { //接收人部门
                fkStaffIdList= taskList.stream().map(CustomTaskVo::getFkStaffIdTo).collect(Collectors.toSet());
            }
        }
        List<DepartmentAndStaffVo> departmentAndStaffVoList = permissionCenterClient.getDepartmentAndStaffDtoByStaffIds(fkStaffIdList).getData();
        // 方法1：直接提取部门信息（去重）
        if(GeneralTool.isNotEmpty(departmentAndStaffVoList)){
            departmentAndStaffVos = departmentAndStaffVoList.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(
                                    DepartmentAndStaffVo::getFkDepartmentId, // 以部门ID为Key
                                    Function.identity(),                     // 保留原始对象
                                    (existing, replacement) -> existing      // 如果重复，保留已存在的
                            ),
                            map -> new ArrayList<>(map.values())         // 转换回List
                    ));
        }

        return new ListResponseBo<>(departmentAndStaffVos);
    }

    @Override
    public void exportTaskList(HttpServletResponse response, CustomTaskSearchDto data) {
        // 准备要导出的数据
        ListResponseBo<CustomTaskVo> dataList = taskList(data,null);

        if (dataList.getDatas() == null || dataList.getDatas().isEmpty()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
         SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<CustomExportTaskVo> customExportTaskVos = new ArrayList<>();
        for (CustomTaskVo customTaskVo:dataList.getDatas()){
            CustomExportTaskVo exportVo= BeanCopyUtils.objClone(customTaskVo, CustomExportTaskVo::new);
            if (GeneralTool.isNotEmpty(customTaskVo.getTaskDeadline())) {
                exportVo.setTaskDeadline(sd.format(customTaskVo.getTaskDeadline()));
            }
            if (GeneralTool.isNotEmpty(customTaskVo.getGmtCreate())) {
                exportVo.setGmtCreate(sd.format(customTaskVo.getGmtCreate()));
            }
            if (GeneralTool.isNotEmpty(customTaskVo.getGmtModified())) {
                exportVo.setGmtModified(sd.format(customTaskVo.getGmtModified()));
            }
            if (GeneralTool.isNotEmpty(customTaskVo.getStatusModifiedTime())) {
                exportVo.setStatusModifiedTime(sd.format(customTaskVo.getStatusModifiedTime()));
            }
            if (GeneralTool.isNotEmpty(customTaskVo.getStatus())) {
                exportVo.setStatus(TaskEnum.getValueByKey(customTaskVo.getStatus(), TaskEnum.MULTI_TASK_STATUS));
            }
            if (GeneralTool.isNotEmpty(customTaskVo.getTaskType())) {
                exportVo.setTaskType(TaskEnum.getValueByKey(customTaskVo.getTaskType(), TaskEnum.TASK_TYPE));
            }
            if (GeneralTool.isNotEmpty(customTaskVo.getFkStaffIdTo())&&customTaskVo.getFkStaffIdTo()==-1) {
                exportVo.setRecipient("多人任务");
            }
            customExportTaskVos.add(exportVo);
        }
        List<String> fields = new ArrayList<>();
        fields.add("taskDescription");
        fields.add("comment");
        FileUtils.exportExcelWithImages(response, customExportTaskVos, "customExportTaskVos", CustomExportTaskVo.class,fields);
    }

    @Override
    public List<Task> getTaskByEndTime() {
        return customTaskMapper.getTaskByEndTime();
    }

    public void batchInsertTaskItems(List<TaskItem> taskItems, int batchSize) {
        // 如果传入的任务列表为空或没有数据，直接返回
        if (taskItems == null || taskItems.isEmpty()) {
            System.out.println("任务列表为空，无需插入");
            return;
        }

        // 获取总数据量（例如30000条）
        int totalSize = taskItems.size();

        // 初始化成功和失败计数器
        int successCount = 0;
        int errorCount = 0;

        // 开始循环处理，每次处理 batchSize 条数据
        for (int i = 0; i < totalSize; i += batchSize) {
            // 计算当前批次的结束索引，防止越界
            int end = Math.min(i + batchSize, totalSize);

            // 截取当前批次的数据子列表
            List<TaskItem> subList = taskItems.subList(i, end);

            try {
                // 调用 MyBatis Mapper 的批量插入方法
                taskItemMapper.batchInsert(subList);

                // 当前批次插入成功，累加成功数量
                successCount += subList.size();

                // 输出日志：当前插入范围
                System.out.printf("已成功插入第 %d - %d 条数据%n", i, end);
            } catch (Exception e) {
                // 插入失败时，累加失败数量
                errorCount += subList.size();

                // 输出错误信息（可替换为 log.error 使用日志框架）
                System.err.printf("插入第 %d - %d 条数据时发生错误: %s%n", i, end, e.getMessage());
            }
        }
        // 最后输出总的插入统计结果
        System.out.printf("插入完成，共 %d 条数据，成功：%d，失败：%d%n", totalSize, successCount, errorCount);
    }

}
