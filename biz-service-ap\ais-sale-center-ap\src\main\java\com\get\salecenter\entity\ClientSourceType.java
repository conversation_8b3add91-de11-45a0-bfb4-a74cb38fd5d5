package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("u_client_source_type")
public class ClientSourceType extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "类型名称")
    @Column(name = "type_name")
    private String typeName;

    /**
     * 类型Key：bms_student_num/crm_contract_num/m_agent/bms_student_num_not_os/m_business_provider
     */
    @ApiModelProperty(value = "类型Key：bms_student_num/crm_contract_num/m_agent/bms_student_num_not_os/m_business_provider")
    @Column(name = "type_key")
    private String typeKey;

    @ApiModelProperty(value = "类型标识：100%, 30%")
    @Column(name = "type_mark")
    private String typeMark;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;

}