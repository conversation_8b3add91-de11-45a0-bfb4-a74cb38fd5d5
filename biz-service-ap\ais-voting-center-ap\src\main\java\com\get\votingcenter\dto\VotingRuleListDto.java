package com.get.votingcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: Hardy
 * @create: 2021/9/23 14:46
 * @verison: 1.0
 * @description:
 */
@Data
public class VotingRuleListDto extends BaseVoEntity implements Serializable {
    /**
     * 投票主题Id
     */
    @ApiModelProperty(value = "投票主题Id")
    private Long fkVotingId;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer viewOrder;


}
