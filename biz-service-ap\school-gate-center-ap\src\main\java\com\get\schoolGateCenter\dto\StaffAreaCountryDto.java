package com.get.schoolGateCenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2020/7/22
 * @TIME: 10:49
 * @Description: 员工业务国家DTO
 **/
@Data
public class StaffAreaCountryDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 国家编号
     */
    @ApiModelProperty(value = "国家编号")
    private String num;

    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String name;

    /**
     * 国家中文名称
     */
    @ApiModelProperty(value = "国家中文名称")
    private String nameChn;

    /**
     * 是否选择国家
     */
    @ApiModelProperty(value = "是否选择国家")
    private Boolean isSelect;
    private String fkCurrencyTypeName;

}
