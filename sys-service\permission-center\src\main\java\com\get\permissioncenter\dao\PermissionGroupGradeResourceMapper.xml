<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.permissioncenter.dao.PermissionGroupGradeResourceMapper">
  <resultMap id="BaseResultMap" type="com.get.permissioncenter.entity.PermissionGroupGradeResource">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_permission_group_id" jdbcType="BIGINT" property="fkPermissionGroupId" />
    <result column="fk_permission_grade_id" jdbcType="BIGINT" property="fkPermissionGradeId" />
    <result column="fk_resource_key" jdbcType="VARCHAR" property="fkResourceKey" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insert" parameterType="com.get.permissioncenter.entity.PermissionGroupGradeResource" keyProperty="id" useGeneratedKeys="true">
    insert into r_permission_group_grade_resource (id, fk_permission_group_id, fk_permission_grade_id,
      fk_resource_key, gmt_create, gmt_create_user,
      gmt_modified, gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkPermissionGroupId,jdbcType=BIGINT}, #{fkPermissionGradeId,jdbcType=BIGINT},
      #{fkResourceKey,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR},
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <select id="findResourcesByStaffId" resultType="java.lang.String">
      select distinct pr.fk_resource_key
      from r_permission_group_grade_staff ps left join  r_permission_group_grade_resource pr
      on ps.fk_permission_group_id = pr.fk_permission_group_id and ps.fk_permission_grade_id = pr.fk_permission_grade_id
      where ps.fk_staff_id = #{StaffId}
  </select>
  <select id="CountByGroupAndGrade" resultType="java.lang.Integer">
     select count(*) from r_permission_group_grade_resource pggs where
     pggs.fk_permission_group_id = #{groupId} and pggs.fk_permission_grade_id = #{gradeId}
  </select>
    <select id="deleteByGroupAndGrade">
        delete from r_permission_group_grade_resource  where
        fk_permission_group_id = #{groupId} and fk_permission_grade_id = #{gradeId}
    </select>
    <select id="deleteByMap" resultType="java.lang.Integer">
        delete from r_permission_group_grade_resource where fk_resource_key = #{resourceKey}
    </select>
    <select id="findByMap" resultMap="BaseResultMap">
        select * from r_permission_group_grade_resource  where  fk_resource_key = #{resourceKey}
    </select>
    <select id="updateByResourceKey">
        update r_permission_group_grade_resource set fk_resource_key = #{resourceKey} where fk_resource_key =#{oldResourceKey}
    </select>
    <select id="selectListByCompanyId"
            resultType="com.get.permissioncenter.entity.PermissionGroupGradeResource">
        SELECT
            r.*
        FROM
            r_permission_group_grade_resource r
        INNER JOIN m_resource m ON m.resource_key = r.fk_resource_key
        WHERE
            1=1
            <if test="permissionGroupGradeResourceDto.fkPermissionGradeId!=null and permissionGroupGradeResourceDto.fkPermissionGradeId!=''">
                AND r.fk_permission_grade_id = #{permissionGroupGradeResourceDto.fkPermissionGradeId}
            </if>
            <if test="permissionGroupGradeResourceDto.fkPermissionGroupId!=null and permissionGroupGradeResourceDto.fkPermissionGroupId!=''">
                AND r.fk_permission_group_id =  #{permissionGroupGradeResourceDto.fkPermissionGroupId}
            </if>
            <if test="fkCompanyId!=null">
                AND
                IF (
                fk_company_ids IS NULL
                OR fk_company_ids = '',
                1 = 1,
                FIND_IN_SET(#{fkCompanyId}, fk_company_ids)
                )
            </if>
    </select>
</mapper>