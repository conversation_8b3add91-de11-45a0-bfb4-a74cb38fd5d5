package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: Hardy
 * @create: 2022/9/5 15:21
 * @verison: 1.0
 * @description:
 */
@Data
public class AgentConventionPersonSaveDto {

    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    private Long fkConventionId;
//    /**
//     * 参展人员类型（枚举定义）：0校方/1代理/2嘉宾/3员工/4工作人员
//     */
//    @ApiModelProperty(value = "参展人员类型（枚举定义）：0校方/1代理/2嘉宾/3员工/4工作人员")
//    private Integer type = 1;

//    /**
//     * 参会编号
//     */
//    @ApiModelProperty(value = "参会编号")
//    private String num;
    /**
     * 参加人姓名
     */
    @ApiModelProperty(value = "参加人姓名（拼音）")
    private String name;
    /**
     * 参加人姓名（中文）
     */
    @ApiModelProperty(value = "参加人姓名（中文）")
    private String nameChn;
    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男")
    private Integer gender;
    /**
     * 参加人职位
     */
    @ApiModelProperty(value = "参加人职位")
    private String title;
    /**
     * 公司
     */
    @ApiModelProperty(value = "机构")
    private String company;
    /**
     * 参加人电邮
     */
    @ApiModelProperty(value = "参加人电邮")
    private String email;
    /**
     * 参加人电话
     */
    @ApiModelProperty(value = "参加人电话")
    private String tel;
//    /**
//     * 护照号
//     */
//    @ApiModelProperty(value = "护照号")
//    private String passportNum;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号/护照/港澳回乡证/台胞证")
    private String idCardNum;

    /**
     * 备注
     */
    @ApiModelProperty(value = "bdId")
    private Long fkStaffId;

    /**
     * 出发国家/地区
     */
    @ApiModelProperty(value = "所属国家/地区")
    private String areaCountryName;

    /**
     * 出发州省
     */
    @ApiModelProperty(value = "所属州省")
    private String areaStateName;

    /**
     * 出发城市
     */
    @ApiModelProperty(value = "所属城市")
    private String areaCityName;

    @ApiModelProperty(value = "身份类型：0身份证/1护照/2港澳回乡证/3台胞证")
    private Integer idType;

    /**
     * 常驻国家
     */
    @ApiModelProperty(value = "常驻国家")
    private String residentCountryName;

    /**
     * 常驻州省
     */
    @ApiModelProperty(value = "常驻州省")
    private String residentStateName;

    /**
     * 常驻城市
     */
    @ApiModelProperty(value = "常驻城市")
    private String residentCityName;

    /**
     * 桌子类型key
     */
    @ApiModelProperty(value = "桌子类型key")
    @NotBlank(message = "桌子类型key不能为空")
    private String fkTableTypeKey;

    /**
     * 桌子编号
     */
    @ApiModelProperty(value = "桌子编号")
    @NotBlank(message = "桌子编号不能为空")
    private String tableNum;
}
