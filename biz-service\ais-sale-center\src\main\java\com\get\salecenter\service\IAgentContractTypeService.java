package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.salecenter.vo.AgentContractTypeVo;
import com.get.salecenter.dto.AgentContractTypeDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/9/14
 * @TIME: 14:52
 * @Description:
 **/

public interface IAgentContractTypeService {

    List<AgentContractTypeVo> getAllAgentContractTypes();

    /**
     * @return void
     * @Description: 新增事件
     * @Param [contractTypeDtos]
     * <AUTHOR>
     */
    void addAgentContractType(List<AgentContractTypeDto> contractTypeDtos);


    /**
     * @return java.util.List<com.get.salecenter.vo.AgentContractTypeVo>
     * @Description: 查询所有事件
     * @Param [agentEventTypeVo, page]
     * <AUTHOR>
     */
    List<AgentContractTypeVo> getAgentContractTypeDtos(AgentContractTypeDto agentEventTypeVo, Page page);


    /**
     * @return com.get.salecenter.vo.AgentContractTypeVo
     * @Description: 修改
     * @Param [agentEventTypeVo]
     * <AUTHOR>
     */
    AgentContractTypeVo updateAgentContractType(AgentContractTypeDto agentEventTypeVo);

    /**
     * @return void
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    void deleteAgentContractType(Long id);

    /**
     * @return com.get.salecenter.vo.AgentContractTypeVo
     * @Description: 查询byID
     * @Param [id]
     * <AUTHOR>
     */
    AgentContractTypeVo findAgentContractTypeById(Long id);


    /**
     * @return void
     * @Description: 上移下移
     * @Param [contractTypeDtos]
     * <AUTHOR>
     */
    void movingOrder(List<AgentContractTypeDto> contractTypeDtos);

}
