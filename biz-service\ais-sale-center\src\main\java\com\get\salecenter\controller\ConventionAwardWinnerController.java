package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.ConventionAwardWinnerVo;
import com.get.salecenter.entity.ConventionAwardCode;
import com.get.salecenter.service.IConventionAwardWinnerService;
import com.get.salecenter.dto.ConventionAwardWinnerDto;
import com.get.salecenter.dto.LuckDrawDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by Jerry.
 * Time: 15:54
 * Date: 2021/9/15
 * Description:中奖名单控制器
 */
@Api(tags = "中奖名单管理")
@RestController
@RequestMapping("sale/conventionAwardWinner")
public class ConventionAwardWinnerController {

    @Resource
    private IConventionAwardWinnerService iConventionAwardWinnerService;

    /**
     * @Description: 列表数据
     * @Author: Jerry
     * @Date:16:02 2021/9/15
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/中奖名单管理/列表数据")
    @PostMapping("datas")
    public ResponseBo<ConventionAwardWinnerVo> datas(@RequestBody ConventionAwardWinnerDto conventionAwardWinnerDto) {
        List<ConventionAwardWinnerVo> datas = iConventionAwardWinnerService.datas(conventionAwardWinnerDto);
        return new ListResponseBo<>(datas);
    }

    /**
     * 新增信息
     *
     * @param conventionAwardWinnerDto
     * @return
     * @
     */
    @ApiOperation(value = "插入中奖纪录", notes = "")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(ConventionAwardWinnerDto.Add.class) ConventionAwardWinnerDto conventionAwardWinnerDto) {
        iConventionAwardWinnerService.saveLottery(conventionAwardWinnerDto);
        return SaveResponseBo.ok();
    }

    @ApiOperation(value = "2022年峰会抽奖", notes = "")
    @VerifyPermission(IsVerify = false)
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/中奖名单管理/2022年峰会抽奖")
    @PostMapping("luckDraw")
    public ResponseBo<ConventionAwardCode> luckDraw(@RequestBody @Validated(LuckDrawDto.Add.class) LuckDrawDto luckDrawDto) {
        List<ConventionAwardCode> conventionAwardCodeList = iConventionAwardWinnerService.luckDraw(luckDrawDto);
        return new ListResponseBo<>(conventionAwardCodeList);
    }

    @ApiOperation(value = "2022年峰会重新抽奖", notes = "")
    @VerifyPermission(IsVerify = false)
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/中奖名单管理/2022年峰会重新抽奖")
    @PostMapping("luckDrawAgain")
    public ResponseBo<ConventionAwardCode> luckDrawAgain(@RequestBody  @Validated(LuckDrawDto.Add.class) LuckDrawDto luckDrawDto) {
        List<ConventionAwardCode> conventionAwardCodeList = iConventionAwardWinnerService.luckDrawAgain(luckDrawDto);
        return new ListResponseBo<>(conventionAwardCodeList);
    }


}
