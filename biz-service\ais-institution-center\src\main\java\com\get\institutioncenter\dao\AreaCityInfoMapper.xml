<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.AreaCityInfoMapper">
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.AreaCityInfo" keyProperty="id" useGeneratedKeys="true">
    insert into u_area_city_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkAreaCityInfoTypeId != null">
        fk_area_city_info_type_id,
      </if>
      <if test="fkAreaCityId != null">
        fk_area_city_id,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="publicLevel != null">
        public_level,
      </if>
      <if test="publishTime != null">
        publish_time,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="webTitle != null">
        web_title,
      </if>
      <if test="webMetaDescription != null">
        web_meta_description,
      </if>
      <if test="webMetaKeywords != null">
        web_meta_keywords,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCityInfoTypeId != null">
        #{fkAreaCityInfoTypeId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCityId != null">
        #{fkAreaCityId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="publicLevel != null">
        #{publicLevel,jdbcType=VARCHAR},
      </if>
      <if test="publishTime != null">
        #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="webTitle != null">
        #{webTitle,jdbcType=VARCHAR},
      </if>
      <if test="webMetaDescription != null">
        #{webMetaDescription,jdbcType=VARCHAR},
      </if>
      <if test="webMetaKeywords != null">
        #{webMetaKeywords,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="areaCityInfoIsEmpty"  resultType="java.lang.Boolean">
    select IFNULL(max(id),0) id from u_area_city_info where fk_area_city_id = #{areaCityId} LIMIT 1
  </select>

</mapper>