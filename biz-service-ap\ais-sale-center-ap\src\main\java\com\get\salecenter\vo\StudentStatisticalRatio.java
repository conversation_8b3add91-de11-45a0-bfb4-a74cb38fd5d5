package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 学生数统计比率类
 */
@Data
public class StudentStatisticalRatio {
    @ApiModelProperty(value = "旧学生数增减数 (首次，如果学生数增减数有值，就显示学生数增减数，没有值就显示学生数增减比率)")
    private BigDecimal oldStudentNumRatioNum;

    @ApiModelProperty(value = "旧学生数增减比率(首次）")
    private BigDecimal oldStudentNumRatio;

    @ApiModelProperty(value = "新学生数增减数 (首次，如果学生数增减数有值，就显示学生数增减数，没有值就显示学生数增减比率)")
    private BigDecimal newStudentNumRatioNum;

    @ApiModelProperty(value = "新学生数增减比率(首次）")
    private BigDecimal newStudentNumRatio;

    @ApiModelProperty(value = "总学生数增减数 (如果学生数增减数有值，就显示学生数增减数，没有值就显示学生数增减比率)")
    private BigDecimal totalStudentNum;

    @ApiModelProperty(value = "总学生数增减比率")
    private BigDecimal totalStudentNumRatio;

    @ApiModelProperty(value = "新总学生数增减数 (如果新学生数增减数有值，就显示新学生数增减数，没有值就显示新学生数增减比率)")
    private BigDecimal newTotalStudentNum;

    @ApiModelProperty(value = "新总学生数增减比率")
    private BigDecimal newTotalStudentNumRatio;

    @ApiModelProperty(value = "旧总学生数增减数 (如果旧学生数增减数有值，就显示旧学生数增减数，没有值就显示旧学生数增减比率)")
    private BigDecimal oldTotalStudentNum;

    @ApiModelProperty(value = "旧总学生数增减比率")
    private BigDecimal oldTotalStudentNumRatio;

    @ApiModelProperty(value = "缺资料数 (如果缺资料数增减数有值，就显示缺资料数增减数，没有值就显示缺资料数增减比率)")
    private BigDecimal appOptStudentNum;

    @ApiModelProperty(value = "缺资料数增减比率")
    private BigDecimal appOptStudentNumRatio;

    @ApiModelProperty(value = "申请数增减数 (如果申请数增减数有值，就显示申请数增减数，没有值就显示申请数增减比率)")
    private BigDecimal itemNumRatioNum;

    @ApiModelProperty(value = "申请数增减比率")
    private BigDecimal itemNumRatio;

    @ApiModelProperty(value = "延迟申请数增减数 (如果延迟申请数增减数有值，就显示延迟申请数增减数，没有值就显示延迟申请数增减比率)")
    private BigDecimal itemNumRatioDelayNum;

    @ApiModelProperty(value = "延迟申请数增减比率")
    private BigDecimal itemNumDelayRatio;

    @ApiModelProperty(value = "os数增减比率")
    private BigDecimal osNumRatio;

    @ApiModelProperty(value = "os数增减比率数 (如果os数增减比率数有值，就显示os数增减比率数，没有值就显示os数增减比率)")
    private BigDecimal osNumRatioNum;

    @ApiModelProperty(value = "签证数增减数 (如果签证数增减数有值，就显示签证数数增减数，没有值就显示签证数增减比率)")
    private BigDecimal visaNumRatioNum;

    @ApiModelProperty(value = "签证数增减比率")
    private BigDecimal visaNumRatio;

    @ApiModelProperty(value = "入学数增减数 (如果入学数增减数有值，就显示入学数增减数，没有值就显示入学数增减比率)")
    private BigDecimal enrolledNumRatioNum;

    @ApiModelProperty(value = "入学数增减比率")
    private BigDecimal enrolledNumRatio;

    @ApiModelProperty(value = "后补签证数增减数 (如果补签证数增减数有值，就显示补签证数增减数，没有值就显示补签证数增减比率)")
    private BigDecimal enrolledVisaNumRatioNum;

    @ApiModelProperty(value = "后补签证数增减比率")
    private BigDecimal enrolledVisaNumRatio;

    @ApiModelProperty(value = "已申请反馈数增减数 (如果已录取增减数有值，就显示已录取增减数，没有值就显示已录取增减比率)")
    private BigDecimal admittedRatioNum;

    @ApiModelProperty(value = "已申请反馈数增减比率")
    private BigDecimal admittedNumRatio;

    @ApiModelProperty(value = "延迟申请反馈数增减数 (如果延迟申请反馈数增减数有值，就显示延迟申请反馈数增减数，没有值就显示延迟申请反馈数增减比率)")
    private BigDecimal admittedRatioDelayNum;

    @ApiModelProperty(value = "延迟申请反馈数增减比率")
    private BigDecimal admittedNumDelayRatio;
}
