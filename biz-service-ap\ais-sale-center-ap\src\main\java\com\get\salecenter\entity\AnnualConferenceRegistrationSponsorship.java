package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_annual_conference_registration_sponsorship")
public class AnnualConferenceRegistrationSponsorship extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 年度会议注册id
     */
    @ApiModelProperty(value = "年度会议注册id")
    @Column(name = "fk_annual_conference_registration_id")
    private Long fkAnnualConferenceRegistrationId;
    /**
     * 赞助id
     */
    @ApiModelProperty(value = "赞助id")
    @Column(name = "fk_sponsorship_config_id")
    private Long fkSponsorshipConfigId;
}