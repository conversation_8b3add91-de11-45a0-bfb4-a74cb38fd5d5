<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.AgentContractAgentAccountMapper">
  <insert id="insert" parameterType="com.get.salecenter.entity.AgentContractAgentAccount">
    insert into r_agent_contract_agent_account (id, fk_agent_contract_id, fk_agent_contract_account_id, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkAgentContractId,jdbcType=BIGINT}, #{fkAgentContractAccountId,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.AgentContractAgentAccount">
    insert into r_agent_contract_agent_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkAgentContractId != null">
        fk_agent_contract_id,
      </if>
      <if test="fkAgentContractAccountId != null">
        fk_agent_contract_account_id,
      </if>

      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkAgentContractId != null">
        #{fkAgentContractId,jdbcType=BIGINT},
      </if>
      <if test="fkAgentContractAccountId != null">
        #{fkAgentContractAccountId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateById" parameterType="com.get.salecenter.entity.AgentContractAgentAccount">
    update r_agent_contract_agent_account
    <set>
      <if test="fkAgentContractId != null">
        fk_agent_contract_id = #{fkAgentContractId,jdbcType=BIGINT},
      </if>
      <if test="fkAgentContractAccountId != null">
        fk_agent_contract_account_id = #{fkAgentContractAccountId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.salecenter.entity.AgentContractAgentAccount">
    update r_agent_contract_agent_account
    set fk_agent_contract_id = #{fkAgentContractId,jdbcType=BIGINT},
      fk_agent_contract_account_id = #{fkAgentContractAccountId,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectAgentContractAgentAccount" resultType="com.get.salecenter.vo.AgentContractAccountVo">
    SELECT aca.* FROM r_agent_contract_agent_account AS acac
    LEFT JOIN m_agent_contract_account AS aca ON aca.id = acac.fk_agent_contract_account_id
    WHERE 1=1
    <if test="fkAgentContractId != null">
      AND acac.fk_agent_contract_id = #{fkAgentContractId}
    </if>
  </select>
</mapper>