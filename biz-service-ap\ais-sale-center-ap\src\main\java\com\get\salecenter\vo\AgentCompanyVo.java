package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.AgentCompany;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2020/9/15
 * @TIME: 14:26
 * @Description: 代理-公司安全配置DTO
 **/
@Data
public class AgentCompanyVo extends BaseEntity implements Serializable {

    //==============实体类AgentCompany==================
    private static final long serialVersionUID = 1L;
    /**
     * 学生代理Id
     */
    @ApiModelProperty(value = "学生代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;
}
