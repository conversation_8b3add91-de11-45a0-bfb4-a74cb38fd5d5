package com.get.permissioncenter.service;

/**
 * <AUTHOR>
 * @DATE: 2020/11/25
 * @TIME: 17:11
 * @Description:
 **/
public interface IDeleteService {

    /**
     * @return java.lang.Boolean
     * @Description: 删除员工判断校验
     * @Param [staffId]
     * <AUTHOR>
     */
    <PERSON><PERSON><PERSON> deleteValidateStaff(Long staffId);

    /**
     * @return java.lang.Boolean
     * @Description: 删除公司判断校验
     * @Param [staffId]
     * <AUTHOR>
     */
    Bo<PERSON>an deleteValidateCompany(Long companyId);

    /**
     * @return java.lang.Boolean
     * @Description: 删除部门校验判断
     * @Param [officeId]
     * <AUTHOR>
     */
    Boolean deleteValidateDepartment(Long departmentId);

    /**
     * @return java.lang.Boolean
     * @Description: 删除职位校验判断
     * @Param [officeId]
     * <AUTHOR>
     */
    Bo<PERSON>an deleteValidatePosition(Long positionId);


    /**
     * @return java.lang.Boolean
     * @Description: 删除办公室校验判断
     * @Param [officeId]
     * <AUTHOR>
     */
    <PERSON><PERSON><PERSON> deleteValidateOffice(Long officeId);

}
