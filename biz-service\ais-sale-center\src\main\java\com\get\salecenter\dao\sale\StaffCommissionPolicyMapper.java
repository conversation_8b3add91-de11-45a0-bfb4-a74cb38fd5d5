package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.StaffCommissionPolicyVo;
import com.get.salecenter.entity.StaffCommissionPolicy;
import com.get.salecenter.dto.StaffCommissionActionDto;
import com.get.salecenter.dto.StaffCommissionPolicyDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StaffCommissionPolicyMapper extends GetMapper<StaffCommissionPolicy> {
    /**
     * 列表查询
     * @param iPage
     * @param staffCommissionPolicyDto
     * @return
     */
    List<StaffCommissionPolicyVo> getStaffCommissionPolicies(IPage<StaffCommissionPolicy> iPage, @Param("staffCommissionPolicyDto") StaffCommissionPolicyDto staffCommissionPolicyDto);

    Integer getMaxPriority();

    /**
     * 查询匹配的提成规则
     * @param staffCommissionActionDto
     * @return
     */
    List<StaffCommissionPolicy> getStaffCommissionPoliciesByStaffCommissionActionVo(@Param("staffCommissionActionDto") StaffCommissionActionDto staffCommissionActionDto);

    /**
     * 获取该角色bd员工 各步骤提成金额
     *
     * @Date 15:56 2023/3/8
     * <AUTHOR>
     */
    List<StaffCommissionPolicyVo> getBdStaffCommissionPolicy(@Param("fkCompanyId") Long fkCompanyId,
                                                             @Param("projectRoleKey") String projectRoleKey);

}