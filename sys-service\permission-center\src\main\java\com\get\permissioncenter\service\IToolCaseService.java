package com.get.permissioncenter.service;

import com.alibaba.fastjson.JSONObject;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;

/**
 * Created by <PERSON>.
 * User: 14:55
 * Date: 2021/6/11
 * Description:工具箱业务接口
 */
public interface IToolCaseService {
    /**
     * 登录网址并根据code获取人员信息
     *
     * @param code
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    String loginAndGetPersonHtml(String code, HttpServletRequest request, HttpServletResponse response);

    /**
     * 图片比对
     * <AUTHOR>
     * @DateTime 2023/10/18 14:56
     */
    JSONObject faceMatch(String imagePath1, String imagePath2) throws Exception;
}
