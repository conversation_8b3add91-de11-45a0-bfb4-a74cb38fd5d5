package com.get.insurancecenter.utils;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import org.apache.batik.transcoder.TranscoderInput;
import org.apache.batik.transcoder.TranscoderOutput;
import org.apache.batik.transcoder.image.PNGTranscoder;
import org.springframework.util.ObjectUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;

/**
 * PDF 工具类
 */
public class PdfUtils {

    private static final SimpleDateFormat formatter = new SimpleDateFormat("yyyy/MM/dd");

    /**
     * 从 classpath 中加载指定路径的资源文件，并返回字节数组形式
     *
     * @param path 资源文件路径，相对路径
     * @return 字节数组
     * @throws IOException 文件未找到或读取失败
     */
    public static byte[] getResourceAsBytes(String path) throws IOException {
        try (InputStream inputStream = PdfUtils.class.getClassLoader().getResourceAsStream(path);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            if (inputStream == null) {
                throw new IOException("Resource not found in PdfUtils: " + path);
            }

            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            return outputStream.toByteArray();
        }
    }

    /**
     * 创建 PDF 表格的标题或多列合并单元格
     *
     * @param col       合并列数
     * @param value     单元格文本
     * @param colors    背景颜色（可空）
     * @param colHeight 单元格高度
     * @param fontSize  字体大小
     * @return PdfPCell
     */
    public static PdfPCell addTitleOrColCell(int col, String value, BaseColor colors, int colHeight, float fontSize) {
        try {
            BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.EMBEDDED);
            Font chineseFont = new Font(baseFont, fontSize, Font.NORMAL);

            if (!ObjectUtils.isEmpty(colors)) {
                chineseFont.setColor(new BaseColor(68, 84, 106));
            }

            PdfPCell cell = new PdfPCell(new Paragraph(value, chineseFont));
            cell.setHorizontalAlignment(PdfPCell.ALIGN_CENTER);
            cell.setVerticalAlignment(PdfPCell.ALIGN_MIDDLE);
            cell.setColspan(col);
            cell.setFixedHeight(colHeight);
            cell.setBorderWidth(0.1f);

            if (!ObjectUtils.isEmpty(colors)) {
                cell.setBackgroundColor(colors);
            }

            return cell;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 创建用于表头的居中样式单元格
     *
     * @param value 表头文字
     * @return PdfPCell
     */
    public static PdfPCell addTableHeadCell(String value) {
        try {
            BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.EMBEDDED);
            Font chineseFont = new Font(baseFont, 8, Font.NORMAL);
            chineseFont.setColor(new BaseColor(68, 84, 106));

            PdfPCell cell = new PdfPCell(new Paragraph(value, chineseFont));
            cell.setBackgroundColor(new BaseColor(216, 231, 244));
            cell.setBorderWidth(0.1f);
            cell.setHorizontalAlignment(PdfPCell.ALIGN_CENTER);
            cell.setVerticalAlignment(PdfPCell.ALIGN_MIDDLE);

            return cell;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 创建一个带部分红色高亮提示文本的单元格（用于说明信息）
     *
     * @param col       合并列数
     * @param colHeight 单元格高度
     * @param fontSize  字体大小
     * @return PdfPCell
     */
    public static PdfPCell addPartialColorCell(int col, int colHeight, float fontSize) {
        try {
            BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.EMBEDDED);

            Font defaultFont = new Font(baseFont, fontSize, Font.BOLD, BaseColor.BLACK);
            Font redFont = new Font(baseFont, fontSize, Font.BOLD, BaseColor.RED);

            Paragraph paragraph = new Paragraph();
            paragraph.setLeading(fontSize + 2, 1.5f);

            paragraph.add(new Chunk("需要回复确认人民币结算 or 外币结算\n", redFont));
            paragraph.add(new Chunk("如果人民币结算，提供人民币账户信息（中文填写）\n", defaultFont));
            paragraph.add(new Chunk("如果外币结算，提供外币账户信息（英文填写）\n", defaultFont));
            paragraph.add(new Chunk("外币结算默认统一结算金额较大币种，如有币种要求，回复确认；如收款银行是汇丰银行，默认分币种划款。", defaultFont));

            PdfPCell cell = new PdfPCell(paragraph);
            cell.setColspan(col);
            cell.setFixedHeight(colHeight);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setVerticalAlignment(PdfPCell.ALIGN_MIDDLE);
            cell.setBorderWidth(0.1f);

            return cell;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 将 base64 编码的 SVG 图片转为 PNG 格式的字节数组
     *
     * @param base64Svg base64 编码的 SVG 数据（含 data:image/svg+xml;base64, 前缀）
     * @return PNG 字节数组
     * @throws Exception 转码失败时抛出
     */
    public static byte[] convertBase64ToPng(String base64Svg) throws Exception {
        byte[] svgBytes = Base64.getDecoder().decode(base64Svg.split(",")[1]);

        TranscoderInput input = new TranscoderInput(new ByteArrayInputStream(svgBytes));
        PNGTranscoder transcoder = new PNGTranscoder();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        TranscoderOutput output = new TranscoderOutput(outputStream);

        transcoder.transcode(input, output);
        return outputStream.toByteArray();
    }

    /**
     * 将 Date 格式化为 yyyy/MM/dd 字符串
     *
     * @param date 日期
     * @return 格式化后的字符串，若为 null 返回空字符串
     */
    public static String formatDate(Date date) {
        if (date == null) {
            return "";
        }
        return formatter.format(date);
    }
} 