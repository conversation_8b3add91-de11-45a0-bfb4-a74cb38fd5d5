package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class BdStudentBonusDto {

    @ApiModelProperty(value = "公司ids")
    private List<Long> companyIds;

//    @ApiModelProperty(value = "地区id")
//    private Long fkAreaCountryId;

    //【学生名称】提示词：学生名称（中/英）。模糊搜索
    @ApiModelProperty(value = "学生名字")
    private String studentName;
    //【代理名称】提示词：申请绑定代理名称/名称备注/原公司/编号。单选
    @ApiModelProperty(value = "代理名称/代理编号")
    private String agentNameNum;
    @ApiModelProperty(value = "代理名称/代理编号")
    private List<Long> agentIds;
    //【BD名称】提示词：申请绑定BD名称（中英）或BD编号。多选
    @ApiModelProperty(value = "BD名称/BD编号")
    private String bdNameNum;

    @ApiModelProperty(value = "（一级）下属员工id")
    private Long fkStaffId;

    @ApiModelProperty(value = "BD名称/BD编号")
    private List<Long> fkBdIds;
    //【学校名称】提示词：学校名称（中/英）。模糊搜索
    @ApiModelProperty(value = "学校名称")
    private String institutionName;

    //【课程名称】提示词：课程名称（中/英）。模糊搜索
    @ApiModelProperty(value = "旧系统课程名称")
    private String courseCustomName;
    //【实收时间】提示词：实收时间（开始范围）- 实收时间（结束范围）
    @ApiModelProperty(value = "实收开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "实收结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;
}
