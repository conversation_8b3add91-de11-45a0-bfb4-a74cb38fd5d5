package com.get.salecenter.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.StudentOfferItemIssueInstitutionCourse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface StudentOfferItemIssueInstitutionCourseMapper extends BaseMapper<StudentOfferItemIssueInstitutionCourse> {
    int insert(StudentOfferItemIssueInstitutionCourse record);

    int insertSelective(StudentOfferItemIssueInstitutionCourse record);

    Long getCourseIdIssueByOfferItemId(@Param("id") Long id);

    List<StudentOfferItemIssueInstitutionCourse> getCourseIdIssue2ByOfferItemIds(@Param("ids") Set<Long> ids);
}