package com.get.financecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/12/23
 * @TIME: 14:47
 * @Description:
 **/
@Data
public class PaymentFormDto  extends BaseVoEntity {
    /**
     * 公司Id
     */
    @NotNull(message = "公司Id不能为空", groups = {Add.class, Update.class})
    @Min(value = 1, message = "缺少公司id参数", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "公司Id", required = true)
    private Long fkCompanyId;

    /**
     * 目标类型关键字，枚举：m_agent代理
     */
    @ApiModelProperty(value = "目标类型关键字，枚举：m_agent代理", required = true)
    @NotBlank(message = "目标类型关键字不能为空", groups = {Add.class, Update.class})
    private String fkTypeKey;

    /**
     * 对应记录Id
     */
    @NotNull(message = "对应记录Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "对应记录Id", required = true)
    private Long fkTypeTargetId;

    /**
     * 银行帐号Id（代理/供应商）
     */
    @ApiModelProperty(value = "银行帐号Id（代理/供应商）")
    private Long fkBankAccountId;

    /**
     * 银行帐号Id（公司）
     */
    @ApiModelProperty(value = "银行帐号Id（公司）")
    private Long fkBankAccountIdCompany;

    /**
     * 付款费用类型id
     */
    @ApiModelProperty(value = "付款费用类型id")
    private Long fkPaymentFeeTypeId;

    /**
     * 付款单编号（系统生成）
     */
    @ApiModelProperty(value = "付款单编号（系统生成）")
    private String numSystem;

    /**
     * 付款单编号（凭证号）
     */
    @ApiModelProperty(value = "付款单编号（凭证号）")
    private String numBank;
    /**
     * 付款日期
     */
    @ApiModelProperty(value = "付款日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date paymentDate;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号", required = true)
    @NotBlank(message = "币种编号不能为空", groups = {Add.class, Update.class})
    private String fkCurrencyTypeNum;

    /**
     * 付款总金额（含手续费）
     */
    @ApiModelProperty(value = "付款总金额（含手续费）")
    private BigDecimal amount;

    /**
     * 汇率（港币）
     */
    @ApiModelProperty(value = "汇率（港币）")
    private BigDecimal exchangeRateHkd;

    /**
     * 付款总金额（港币，含手续费）
     */
    @ApiModelProperty(value = "付款总金额（港币，含手续费）")
    private BigDecimal amountHkd;

    /**
     * 汇率（人民币）
     */
    @ApiModelProperty(value = "汇率（人民币）")
    private BigDecimal exchangeRateRmb;

    /**
     * 付款总金额（人民币，含手续费）
     */
    @ApiModelProperty(value = "付款总金额（人民币，含手续费）")
    private BigDecimal amountRmb;

    /**
     * 手续费
     */
    @ApiModelProperty(value = "手续费")
    private BigDecimal serviceFee;

    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;

    /**
     * 状态：0作废/1打开
     */
    @ApiModelProperty(value = "状态：0作废/1打开")
    private Integer status;

    /**
     * 对应记录名称
     */
    @ApiModelProperty(value = "对应记录名称")
    private String targetName;


    @ApiModelProperty(value = "实付汇率")
    private BigDecimal exchangeRate;

    @ApiModelProperty(value = "汇率调整金额（可正可负）")
    private BigDecimal amountExchangeRate;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String endTime;

    /**
     * 关键词
     */
    @ApiModelProperty(value = "关键词")
    private String keyWord;

    /**
     * 应付计划Id
     */
    @ApiModelProperty(value = "应付计划Id")
    private Long fkPayablePlanId;


    /**
     * 绑定状态（0：未绑定 1：绑定部分 2：绑定完成）
     */
    @ApiModelProperty(value = "绑定状态（0：未绑定 1：绑定部分 2：绑定完成）")
    private Integer bindingStatus;


    /**
     * 公司ids
     */
    @ApiModelProperty(value = "公司ids")
    private List<Long> fkCompanyIds;


    @ApiModelProperty(value = "付款金额（含手续费）开始范围")
    private BigDecimal amountStart;

    @ApiModelProperty(value = "付款金额（含手续费）结束范围")
    private BigDecimal amountEnd;

    /**
     * 付款日期开始
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "付款日期开始")
    private Date paymentDateStart;

    /**
     * 付款日期结束
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "付款日期结束")
    private Date paymentDateEnd;

}
