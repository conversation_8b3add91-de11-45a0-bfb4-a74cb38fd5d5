package com.get.aismail.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.get.aismail.dao.*;
import com.get.aismail.dto.AttachedDelete;
import com.get.aismail.dto.MailBox;
import com.get.aismail.dto.MailDto;
import com.get.aismail.entity.*;
import com.get.aismail.service.IMailService;
import com.get.aismail.service.ITencentCloudService;
import com.get.aismail.utils.*;
import com.get.aismail.vo.*;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.StaffInfo;
import com.get.core.secure.utils.SecureUtil;
import com.sun.mail.imap.IMAPFolder;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.annotation.Resource;
import javax.mail.*;
import javax.mail.internet.*;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.get.core.tool.api.ResultCode.BAD_REQUEST;

@Slf4j
@Service
public class MailServiceImpl implements IMailService {

    @Resource
    private MMailAccountMapper accountMapper;
    @Resource
    private MMailAttachedMapper attachedMapper;
    @Resource
    private MMailMapper mailMapper;
    @Resource
    private MMailDorisMapper mailDorisMapper;
    @Resource
    private MMailSyncQueueMapper syncQueueMapper;

    @Resource
    private MMailSyncQueueDorisMapper mailSyncQueueDorisMapper;

    @Resource
    private MFileMailMapper mFileMailMapper;
    @Resource
    private SMediaAndAttachedMapper sMediaAndAttachedMapper;

    @Resource
    private ITencentCloudService tencentCloudService;

    @Value("${file.tencentcloudfile.bucketname}")
    private String fileBucketName;

    @Value("${file.tencentcloudimage.bucketname}")
    private String imageBucketName;
    @Resource
    private Environment env;
//    @Resource
//    private GetRedis getRedis;

    @Override
    public IPage<MailDto> getAllMail(SearchMailVo searchMailVo) throws Exception {
        String emailAccount = searchMailVo.getEmailAccount();
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        Long userId = staffInfo.getStaffId();
        LambdaQueryWrapper<MMailAccount> accountQueryWrapper = new LambdaQueryWrapper<>();
        if (emailAccount == null || emailAccount.isEmpty()) {
            accountQueryWrapper.eq(MMailAccount::getFkPlatformUserId, userId).eq(MMailAccount::getIsDefault, true);

        } else {
            accountQueryWrapper.eq(MMailAccount::getFkPlatformUserId, userId).eq(MMailAccount::getEmailAccount, emailAccount);
        }
        List<MMailAccount> accounts = accountMapper.selectList(accountQueryWrapper);
        if (accounts.isEmpty()) {
            throw new GetServiceException(BAD_REQUEST, "mail account is not exist");
        }
        Long id = accounts.get(0).getId();
        searchMailVo.setMailAccountId(id);
        log.info("开始寻找平台id是{},用户id是:{}的数据", userId, id);
        // 获取分页参数
        int currentPage = searchMailVo.getCurrentPage() == null ? 1 : searchMailVo.getCurrentPage();
        int pageSize = searchMailVo.getPageSize() == null ? 10 : searchMailVo.getPageSize();
        Page<Long> ids = new Page<>(currentPage, pageSize);
        IPage<Long> ids1 = mailDorisMapper.selectMailDoris(ids, searchMailVo);
        // 创建分页对象
        Page<MailDto> page = new Page<>(1, pageSize);
        if (ids1.getRecords().isEmpty()) {
            return page;
        }
        // 使用分页查询
        IPage<MailDto> mailPage = mailMapper.selectMailByIds(page, ids1.getRecords());
        mailPage.setCurrent(ids.getCurrent());
        mailPage.setSize(ids.getSize());
        mailPage.setTotal(ids.getTotal());
        mailPage.setPages(ids.getPages());

        // 使用分页查询
//        IPage<MailDto> mailPage = mailMapper.selectMail(page, searchMailVo);
        List<MailDto> mailDtoList = mailPage.getRecords();
        for (MailDto mailDto : mailDtoList) {
            // 获取邮件附件
            LambdaQueryWrapper<MMailAttached> attachedQueryWrapper = new LambdaQueryWrapper<>();
            attachedQueryWrapper.eq(MMailAttached::getFkMailId, mailDto.getId());
            List<MMailAttached> attaches = attachedMapper.selectList(attachedQueryWrapper);
            List<AttachedVo> attachedVos = new ArrayList<>();
            for (MMailAttached attached : attaches) {
                AttachedVo attachedVo = new AttachedVo();
                attachedVo.setAnnexName(attached.getFileName());
                attachedVo.setId(attached.getFileId());
                attachedVos.add(attachedVo);
            }
            mailDto.setAttachedVos(attachedVos);
            // 邮件日期分类
            mailDto.setDateSort(searchMailVo.getDateSort());
        }
        return mailPage;
    }

    @Override
    public Long changeMailStatus(ChangeMailStatusVo changeMailStatusVo) throws Exception {
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        Long userId = staffInfo.getStaffId();
        String name = staffInfo.getName();
        MMailSyncQueue mailSyncQueue = new MMailSyncQueue();
        mailSyncQueue.setFkMailAccountId(changeMailStatusVo.getFkMailAccountId());
        mailSyncQueue.setFkMailId(changeMailStatusVo.getFkMailId());
        mailSyncQueue.setMailId(changeMailStatusVo.getMailId());
        mailSyncQueue.setOperationType(changeMailStatusVo.getOperationType());
        mailSyncQueue.setOperationValue(changeMailStatusVo.getOperationValue());
        mailSyncQueue.setSync(false);
        mailSyncQueue.setGmtCreate(LocalDateTime.now());
        mailSyncQueue.setGmtCreateUser(name);
        syncQueueMapper.insert(mailSyncQueue);

        /*// doris同步添加
        MMailSyncQueueDoris mMailSyncQueueDoris = new MMailSyncQueueDoris();
        mMailSyncQueueDoris.setId(mailSyncQueue.getId());
        mMailSyncQueueDoris.setFkMailAccountId(mailSyncQueue.getFkMailAccountId());
        mMailSyncQueueDoris.setFkMailId(mailSyncQueue.getFkMailId());
        mMailSyncQueueDoris.setMailId(mailSyncQueue.getMailId());
        mMailSyncQueueDoris.setOperationType(mailSyncQueue.getOperationType());
        mMailSyncQueueDoris.setOperationValue(mailSyncQueue.getOperationValue());
        mMailSyncQueueDoris.setSync(mailSyncQueue.isSync());
        mMailSyncQueueDoris.setGmtCreate(mailSyncQueue.getGmtCreate());
        mMailSyncQueueDoris.setGmtCreateUser(mailSyncQueue.getGmtCreateUser());
        mailSyncQueueDorisMapper.insertDoris(mMailSyncQueueDoris);*/


        LambdaQueryWrapper<MMail> mailQueryWrapper = new LambdaQueryWrapper<>();
        mailQueryWrapper.eq(MMail::getMailId, changeMailStatusVo.getMailId());
        mailQueryWrapper.eq(MMail::getFkMailAccountId, changeMailStatusVo.getFkMailAccountId());
        List<MMail> mails = mailMapper.selectList(mailQueryWrapper);
        for (MMail mail : mails) {
            if (changeMailStatusVo.getOperationType() == 1) {
                mail.setIsRead("1".equals(changeMailStatusVo.getOperationValue()));
            } else if (changeMailStatusVo.getOperationType() == 2) {
                mail.setStar("1".equals(changeMailStatusVo.getOperationValue()));
            } /*else if (changeMailStatusVo.getOperationType() == 3) {
                mail.setFoldBox(changeMailStatusVo.getOperationValue());
            }*/
            mailMapper.updateById(mail);

            /*// doris同步进行更新，先进行删除在插入
            mailDorisMapper.deleteMailDoris(mail.getId());
            // 删除后重新插入
            MMailDoris mMailDoris = new MMailDoris();
            mMailDoris.setId(mail.getId());
            mMailDoris.setFkMailAccountId(mail.getFkMailAccountId());
            mMailDoris.setStar(mail.isStar());
            mMailDoris.setDate(mail.getDate());
            mMailDoris.setBodyText(mail.getBodyText());
            mMailDoris.setSubject(mail.getSubject());
            mMailDoris.setFromMail(mail.getFromMail());
            mMailDoris.setLlmMailType(mail.getLlmMailType());
            mMailDoris.setFoldBox(mail.getFoldBox());
            mailDorisMapper.insertDoris(mMailDoris);*/
        }
        return userId;
    }

    @Override
    public ResponseEntity<org.springframework.core.io.Resource> downloadAttached(HttpServletResponse response, DownloadAttachedVo downloadAttachedVo) throws Exception {
        long start = System.currentTimeMillis();
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        Long userId = staffInfo.getStaffId();
        // 解析桶里的文件
        if (downloadAttachedVo.getGuid() != null && !downloadAttachedVo.getGuid().isEmpty()) {
            log.info("下载用户在桶里的文件");
            QueryWrapper<MFileMail> fileMailQueryWrapper = new QueryWrapper<>();
            fileMailQueryWrapper.eq("file_guid", downloadAttachedVo.getGuid());
            List<MFileMail> mFileMailList = mFileMailMapper.selectList(fileMailQueryWrapper);
            if (mFileMailList.isEmpty()) {
                log.info("在桶里没有找到文件");
            } else {
                MFileMail mFileMail = mFileMailList.get(0);
                tencentCloudService.downLoadObject(mFileMail, response, false, true, null, false);
                return null;
            }
        }
        LambdaQueryWrapper<MMailAccount> accountQueryWrapper = new LambdaQueryWrapper<>();
        accountQueryWrapper.eq(MMailAccount::getFkPlatformUserId, userId).eq(MMailAccount::getEmailAccount, downloadAttachedVo.getEmailAccount());
        List<MMailAccount> accounts = accountMapper.selectList(accountQueryWrapper);
        if (accounts.isEmpty()) {
            throw new GetServiceException(BAD_REQUEST, "mail account is not exit");
        }
        MMailAccount account = accounts.get(0);
        // 首先找到mail的位置找到id
        QueryWrapper<MMail> mailQueryWrapper1 = new QueryWrapper<>();
        mailQueryWrapper1.eq("fk_mail_account_id", account.getId());
        mailQueryWrapper1.eq("fold_box", downloadAttachedVo.getFoldName());
        mailQueryWrapper1.eq("mail_id", downloadAttachedVo.getMailId());
        List<MMail> mails1 = mailMapper.selectList(mailQueryWrapper1);
        if (mails1.isEmpty()) {
            throw new GetServiceException(BAD_REQUEST, "没有找到对应邮件");
        }
        MMail mail1 = mails1.get(0);
        log.info("开始下载邮件id是{}的附件", mail1.getId());
        QueryWrapper<MMailAttached> attachedQueryWrapper = new QueryWrapper<>();
        attachedQueryWrapper.eq("fk_mail_id", mail1.getId())
                .eq("file_id", downloadAttachedVo.getAnnexId())
                .and(wrapper -> wrapper.eq("file_name", downloadAttachedVo.getFileName())
                        .or()
                        .eq("file_name", downloadAttachedVo.getFileName().replaceAll(" ", " ")))
                .eq("fk_mail_account_id", account.getId());
        List<MMailAttached> attacheds = attachedMapper.selectList(attachedQueryWrapper);
        if (attacheds.isEmpty()) {
            throw new GetServiceException(BAD_REQUEST, "mail attached is not exit");
        }
        MMailAttached attached = attacheds.get(0);
        QueryWrapper<SMediaAndAttachedEntity> mediaAndAttachedEntityQueryWrapper = new QueryWrapper<>();
        mediaAndAttachedEntityQueryWrapper.eq("fk_table_name", "m_mail_attached");
        mediaAndAttachedEntityQueryWrapper.eq("fk_table_id", attached.getId());
        List<SMediaAndAttachedEntity> sMediaAndAttachedEntityList = sMediaAndAttachedMapper.selectList(mediaAndAttachedEntityQueryWrapper);
        if (sMediaAndAttachedEntityList.isEmpty()) {
            String host = env.getProperty("mail." + account.getEmailType());
            byte[] key = Objects.requireNonNull(env.getProperty("encrypt.KEY")).getBytes();
            MailBox mailBox = MailBoxUtils.login(account.getEmailAccount(), AesUtil.decrypt(account.getEmailPassword(), key), host);
            LambdaQueryWrapper<MMail> mailQueryWrapper = new LambdaQueryWrapper<>();
            mailQueryWrapper.eq(MMail::getMailId, downloadAttachedVo.getMailId());
            mailQueryWrapper.eq(MMail::getFkMailAccountId, account.getId());
            List<MMail> mails = mailMapper.selectList(mailQueryWrapper);
            if (mails.isEmpty()) {
                throw new GetServiceException(BAD_REQUEST, "not find mail");
            }
            MMail mail = mails.get(0);
            String foldName = env.getProperty("mail." + account.getEmailType() + "-" + mail.getFoldBox());
            String annexSavePath = env.getProperty("mail.annex-save-path");
            downloadAttachedVo.setDate(mail.getDate());
            String annexPath = MailBoxUtils.downAnnex(mailBox, foldName, downloadAttachedVo, annexSavePath);
            long end = System.currentTimeMillis();
            log.info("下载附件总计耗时：{}", end - start);
            mailBox.getStore().close();
            if (annexPath == null) {
                HttpHeaders headers = new HttpHeaders();
                headers.set("X-Status-Code", "1002");
                headers.set("X-Message", "没有找到附件");
                headers.set("X-Success", "false");
                return new ResponseEntity<>(null, headers, HttpStatus.OK);
            }
            Path filePath = Paths.get(annexPath);
            org.springframework.core.io.Resource resource = new UrlResource(filePath.toUri());
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment")
                    .body(resource);
        } else {
            SMediaAndAttachedEntity sMediaAndAttachedEntity = sMediaAndAttachedEntityList.get(0);
            log.info("寻找桶里面guid是{}的文件", sMediaAndAttachedEntity.getFkFileGuid());
            QueryWrapper<MFileMail> fileMailQueryWrapper = new QueryWrapper<>();
            fileMailQueryWrapper.eq("file_guid", sMediaAndAttachedEntity.getFkFileGuid());
            List<MFileMail> mFileMailList = mFileMailMapper.selectList(fileMailQueryWrapper);
            if (mFileMailList.isEmpty()) {
                log.info("在桶里没有找到文件");
            } else {
                MFileMail mFileMail = mFileMailList.get(0);
                tencentCloudService.downLoadObject(mFileMail, response, false, true, null, false);
            }
        }
        return null;
    }

    @Override
    public List<String> uploadFile(MultipartFile[] files) throws Exception {
        String annexSavePath = env.getProperty("mail.up-annex-path");
        List<String> savePaths = new ArrayList<>();
        for (MultipartFile file : files) {
            if (file.isEmpty()) {
                throw new GetServiceException(BAD_REQUEST, "上传失败");
            }
            String fileName = file.getOriginalFilename();
            String savePath = annexSavePath + UUID.randomUUID() + "_" + fileName.replaceAll(" ", "").replaceAll(" ", "");
            File dest = new File(savePath);
            // 将文件保存到指定路径
            file.transferTo(dest);
            savePaths.add(savePath);
        }
        return savePaths;
    }

    @Override
    public void sendMail(SendMailVo sendMailVo) throws Exception {
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        Long userId = staffInfo.getStaffId();
        LambdaQueryWrapper<MMailAccount> accountQueryWrapper = new LambdaQueryWrapper<>();
        accountQueryWrapper.eq(MMailAccount::getFkPlatformUserId, userId).eq(MMailAccount::getEmailAccount, sendMailVo.getEmailAccount());
        MMailAccount account = accountMapper.selectOne(accountQueryWrapper);
        if (account == null) {
            throw new GetServiceException(BAD_REQUEST, "mail account is not exit");
        }
        // 内容进行解码
        sendMailVo.setContent(java.net.URLDecoder.decode(sendMailVo.getContent(), "utf-8"));
        byte[] key = Objects.requireNonNull(env.getProperty("encrypt.KEY")).getBytes();
        String hostSend = env.getProperty("mail." + account.getEmailType() + "-send");
        String port = env.getProperty("mail." + account.getEmailType() + "-send-port");
        String emailPassword = AesUtil.decrypt(account.getEmailPassword(), key);
        Session session = MailBoxUtils.buildSendSession(account.getEmailAccount(), emailPassword, hostSend, port);
        MailBox mailBox = MailBoxUtils.login(account.getEmailAccount(), emailPassword, hostSend);
        String foldName = env.getProperty("mail." + account.getEmailType() + "-sentList");
        try {
            Message message = sendMail(session, mailBox, foldName, sendMailVo, account);
        } catch (Exception e) {
            throw new GetServiceException(BAD_REQUEST, "mail send failed " + e.getMessage());
        }
        // 重新获取邮件
        String sentFoldName = env.getProperty("mail." + account.getEmailType() + "-sentList");
        Folder sentFolder = mailBox.getStore().getFolder(sentFoldName);
        sentFolder.open(Folder.READ_ONLY);
        // 获取目标邮件的UID，之后以此为参考来获取该邮件之后的邮件
        UIDFolder uidFolder = (UIDFolder) sentFolder;
        try {
            // 刷新并获取最新的邮件
            Message[] messages = sentFolder.getMessages();
            // 假设刚添加的邮件是最新的，即它是数组中的最后一个元素
            if (messages.length > 0) {
                Message latestMessage = messages[messages.length - 1];
                String mailID = String.valueOf(uidFolder.getUID(latestMessage));
                processMessage(latestMessage, mailID, account, "sentList");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new GetServiceException(BAD_REQUEST, "邮件发送成功，但暂时无法查看");
        }
    }

    @Override
    public void fetchMail(MMailAccount mMailAccount) throws Exception {
        // 判断是否有用户在手动获取邮件
//        Object emailAccounts = getRedis.getValueOps().get(mMailAccount.getId().toString());
       /* boolean checkIsFetch = false;
        while (emailAccounts != null && emailAccounts.toString().equals(mMailAccount.getEmailAccount())) {
            checkIsFetch = true;
            System.out.println("邮件已经在自动拉取");
            Thread.sleep(1000 * 10);
            emailAccounts = getRedis.getValueOps().get(mMailAccount.getId().toString());
        }
        if (checkIsFetch) {
            return;
        }
        // 将用户储存在redis,五分钟超时
        getRedis.getValueOps().set(mMailAccount.getId().toString(), mMailAccount.getEmailAccount(),5, TimeUnit.MINUTES);*/
        byte[] key = Objects.requireNonNull(env.getProperty("encrypt.KEY")).getBytes();
//                    Long fkPlatformUserId = mMailAccount.getFkPlatformUserId();
        String emailAccount = mMailAccount.getEmailAccount();
        String emailPassword = AesUtil.decrypt(mMailAccount.getEmailPassword(), key);
        String emailType = mMailAccount.getEmailType();
        String host = env.getProperty("mail." + emailType);
        MailBox mailBox = MailBoxUtils.login(emailAccount, emailPassword, host);
        QueryWrapper<MMail> mMailQueryWrapper = new QueryWrapper<>();
        mMailQueryWrapper.eq("fk_mail_account_id", mMailAccount.getId())
                .eq("fold_box", "inboxList")
                .orderByDesc("date") // 按照日期降序排列
                .last("LIMIT 1"); // 只取一条记录;
        List<MMail> mails = mailMapper.selectList(mMailQueryWrapper);
        MMail mMail = new MMail();
        if (mails.isEmpty()) {
            mMail.setDate(mMailAccount.getGmtCreate());
        } else {
            mMail = mails.get(0);
        }
        // 获取收件箱最新的邮件
        String inFoldName = env.getProperty("mail." + emailType + "-inboxList");
//        fetchAllMail(mMailAccount, mMail, mailBox, inFoldName, "inboxList");
        // 将用户储存在 redis删除
//        getRedis.del(mMailAccount.getId().toString());
        // 关闭连接
        mailBox.getStore().close();
    }

    @Override
    public IPage<MailDto> getAllMailManually(SearchMailVo searchMailVo) throws Exception {
        String emailAccount = searchMailVo.getEmailAccount();
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        Long userId = staffInfo.getStaffId();
        LambdaQueryWrapper<MMailAccount> accountQueryWrapper = new LambdaQueryWrapper<>();
        if (emailAccount == null || emailAccount.isEmpty()) {
            accountQueryWrapper.eq(MMailAccount::getFkPlatformUserId, userId).eq(MMailAccount::getIsDefault, true);
        } else {
            accountQueryWrapper.eq(MMailAccount::getFkPlatformUserId, userId).eq(MMailAccount::getEmailAccount, emailAccount);
        }
        MMailAccount account = accountMapper.selectOne(accountQueryWrapper);
        if (account == null) {
            throw new GetServiceException(BAD_REQUEST, "mail account is not exist");
        }
        // 先去获取所有邮件在邮箱
        fetchMail(account);
        return getAllMail(searchMailVo);
    }

    @Override
    public void saveMail(SendMailVo sendMailVo) throws Exception {
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        Long userId = staffInfo.getStaffId();
        LambdaQueryWrapper<MMailAccount> accountQueryWrapper = new LambdaQueryWrapper<>();
        accountQueryWrapper.eq(MMailAccount::getFkPlatformUserId, userId).eq(MMailAccount::getEmailAccount, sendMailVo.getEmailAccount());
        MMailAccount account = accountMapper.selectOne(accountQueryWrapper);
        if (account == null) {
            throw new GetServiceException(BAD_REQUEST, "mail account is not exit");
        }
        //解码
        sendMailVo.setContent(java.net.URLDecoder.decode(sendMailVo.getContent(), "utf-8"));
        byte[] key = Objects.requireNonNull(env.getProperty("encrypt.KEY")).getBytes();
        String hostSend = env.getProperty("mail." + account.getEmailType());
        String emailPassword = AesUtil.decrypt(account.getEmailPassword(), key);
        MailBox mailBox = MailBoxUtils.login(account.getEmailAccount(), emailPassword, hostSend);
        String foldName = env.getProperty("mail." + account.getEmailType() + "-draftList");
        try {
            Message message = saveMail(mailBox, foldName, sendMailVo, account);
        } catch (Exception e) {
            throw new GetServiceException(BAD_REQUEST, "mail send failed");
        }
        try {
            Folder draftFolder = mailBox.getStore().getFolder(foldName);
            draftFolder.open(Folder.READ_ONLY);
            // 获取目标邮件的UID，之后以此为参考来获取该邮件之后的邮件
            UIDFolder uidFolder = (UIDFolder) draftFolder;
            // 刷新并获取最新的邮件
            Message[] messages = draftFolder.getMessages();
            // 假设刚添加的邮件是最新的，即它是数组中的最后一个元素
            if (messages.length > 0) {
                Message latestMessage = messages[messages.length - 1];
                String mailID = String.valueOf(uidFolder.getUID(latestMessage));
                processMessage(latestMessage, mailID, account, "draftList");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new GetServiceException(BAD_REQUEST, "邮件保存成功，但暂时无法查看");
        }
    }

    private static void inputStreamToFile(InputStream ins, File file) {
        try {
            OutputStream os = new FileOutputStream(file);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            ins.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /*public static void processAttachments(Multipart multipart, List<File> files, List<MMailAttached> mMailAttacheds, MMailAccount mMailAccount, String mailId) throws Exception {
        int partCount = multipart.getCount();
        for (int j = 0; j < partCount; j++) {
            BodyPart bodyPart = multipart.getBodyPart(j);
            // 如果这是一个嵌套的multipart部分，则递归处理
            if (bodyPart.isMimeType("multipart/*")) {
                processAttachments((Multipart) bodyPart.getContent(), files, mMailAttacheds, mMailAccount, mailId);
            } else {
                try {
                    // 尝试获取文件名
                    String fileName = MimeUtility.decodeText(bodyPart.getFileName());
                    // 检查是否有文件名或disposition为attachment
                    String disposition = bodyPart.getDisposition();
                    // 获取 Content-ID 头信息并去除角括号
                    String contentId = null;
                    String[] contentIdHeaders = bodyPart.getHeader("Content-ID");
                    if (contentIdHeaders != null && contentIdHeaders.length > 0) {
                        contentId = contentIdHeaders[0].trim();
                        if (contentId.startsWith("<") && contentId.endsWith(">")) {
                            contentId = contentId.substring(1, contentId.length() - 1).trim();
                        }
                    }
                    // 只处理真正意义上的附件，忽略内嵌资源（如HTML中的图片）
                    if ((fileName != null && !fileName.trim().isEmpty()) ||
                            (disposition != null && disposition.equalsIgnoreCase(Part.ATTACHMENT))) {
                        // 忽略具有Content-ID的部件，它们通常是内嵌资源
                        if (contentId != null && !contentId.isEmpty()) {
                            continue;
                        }
                        if (fileName == null || fileName.trim().isEmpty()) {
                            // 如果没有文件名，创建一个默认名称
                            fileName = "unnamed_attachment_" + j;
                        }
                        // 创建并填充附件实体对象
                        MMailAttached mMailAttached = new MMailAttached();
                        mMailAttached.setFkMailAccountId(mMailAccount.getId());
                        mMailAttached.setMailId(mailId);
                        mMailAttached.setFileName(fileName);
                        mMailAttached.setFileId(String.valueOf(j));
                        // 获取文件扩展名
                        int lastDotIndex = fileName.lastIndexOf('.');
                        if (lastDotIndex != -1) {
                            if (fileName.substring(lastDotIndex + 1).length() > 4) {
                                mMailAttached.setFileExtension(fileName.substring(fileName.length() - 4));
                            } else {
                                mMailAttached.setFileExtension(fileName.substring(lastDotIndex + 1));
                            }
                        } else {
                            mMailAttached.setFileExtension(".");
                        }
                        mMailAttached.setGmtCreate(LocalDateTime.now());
                        mMailAttacheds.add(mMailAttached);
                        if (mMailAttached.getFileExtension() != null && mMailAttached.getFileExtension().equals("docx") || mMailAttached.getFileExtension().equals("doc")) {
                            File file = new File(fileName);
                            inputStreamToFile(bodyPart.getInputStream(), file);
                            files.add(file);
                        }
                    }
                } catch (Exception e) {
                    // 记录错误但继续处理其他部分
                    System.out.println("Error processing attachment: " + e.getMessage());
                }
            }
        }
    }*/

    static String subString(String url) {
        if (url.contains("target")) {
            url = url.replace("/data/project/get/file-center/target", "");
            url = url.replace("/data/project/get/app-file-center/target", "");
            return url;
        } else {
            return url;
        }

    }

    private boolean findHtmlContent(MimeMultipart multipart) throws MessagingException, IOException {
        int count = multipart.getCount();
        for (int i = 0; i < count; i++) {
            BodyPart bodyPart = multipart.getBodyPart(i);
            if (bodyPart.isMimeType("multipart/*")) {
                if (findHtmlContent((MimeMultipart) bodyPart.getContent())) {
                    return true;
                }
            } else if (bodyPart.isMimeType("text/html")) {
                return true;
            }
        }
        return false;
    }

    private StringBuilder processNestedMultipart(MimeMultipart multipart) throws Exception {
        int partCount = multipart.getCount();
        boolean htmlFound = false;
        StringBuilder contentBuilder = new StringBuilder();
        List<String> textContents = new ArrayList<>();

        // 第一遍：查找是否有HTML内容
        for (int i = 0; i < partCount; i++) {
            BodyPart bodyPart = multipart.getBodyPart(i);
            if (bodyPart.isMimeType("multipart/*")) {
                MimeMultipart innerMultipart = (MimeMultipart) bodyPart.getContent();
                htmlFound = htmlFound || findHtmlContent(innerMultipart);
            } else if (bodyPart.isMimeType("text/html")) {
                htmlFound = true;
            }
        }

        // 第二遍：基于htmlFound标志决定处理方式
        for (int i = 0; i < partCount; i++) {
            BodyPart bodyPart = multipart.getBodyPart(i);
            String disposition = bodyPart.getDisposition();

            if (bodyPart.isMimeType("multipart/*")) {
                contentBuilder.append(processNestedMultipart((MimeMultipart) bodyPart.getContent()));
            } else if (!htmlFound && bodyPart.isMimeType("text/plain")) {
                // 如果没有找到HTML内容，则处理纯文本
                Object contentObject = bodyPart.getContent();
                if (contentObject instanceof String) {
                    ContentType ct = new ContentType(bodyPart.getContentType());
                    String charset = ct.getParameter("charset");
                    if (charset == null || charset.isEmpty()) {
                        charset = "utf-8"; // 默认使用utf-8
                    }
                    try {
                        String content = new String(((String) contentObject).getBytes(charset), charset);
                        contentBuilder.append(content);
                    } catch (UnsupportedEncodingException e) {
                        e.printStackTrace();
                        log.info("邮件内容解码失败");
                    }
                }
            } else if (bodyPart.isMimeType("text/html") && htmlFound) {
                // 如果已经确定要使用HTML内容，则在这里处理它
                Object contentObject = bodyPart.getContent();
                if (contentObject instanceof String) {
                    contentBuilder.append(contentObject.toString());
                }
            } else if (disposition != null && (disposition.equalsIgnoreCase(Part.ATTACHMENT) || disposition.equalsIgnoreCase(Part.INLINE))) {
                // 跳过附件或内联资源
                continue;
            } else {
                // 对于其他MIME类型，记录或按需处理
                log.info("未处理的MIME类型: {}", bodyPart.getContentType());
            }
        }

        return contentBuilder;
    }

    public void processAttachments(Multipart multipart, List<File> files, List<EmbeddedFile> embeddedFiles, List<MMailAttached> mMailAttacheds, MMailAccount
            mMailAccount, String mailId) throws Exception {
        int partCount = multipart.getCount();
        for (int j = 0; j < partCount; j++) {
            BodyPart bodyPart = multipart.getBodyPart(j);
            // 如果这是一个嵌套的multipart部分，则递归处理
            if (bodyPart.isMimeType("multipart/*")) {
                processAttachments((Multipart) bodyPart.getContent(), files, embeddedFiles, mMailAttacheds, mMailAccount, mailId);
            } else {
                try {
                    if (bodyPart.getFileName() == null || bodyPart.getFileName().isEmpty()) {
                        continue;
                    }
                    // 尝试获取文件名
                    String fileName = MimeUtility.decodeText(bodyPart.getFileName());
                    // 检查是否有文件名或disposition为attachment
                    String disposition = bodyPart.getDisposition();
                    // 获取 Content-ID 头信息并去除角括号
                    String contentId = null;
                    String[] contentIdHeaders = bodyPart.getHeader("Content-ID");
                    if (contentIdHeaders != null && contentIdHeaders.length > 0) {
                        contentId = contentIdHeaders[0].trim();
                        if (contentId.startsWith("<") && contentId.endsWith(">")) {
                            contentId = contentId.substring(1, contentId.length() - 1).trim();
                        }
                    }
                    // 处理附件
                    if ((fileName != null && !fileName.trim().isEmpty()) ||
                            (disposition != null && disposition.equalsIgnoreCase(Part.ATTACHMENT))) {
                        // 忽略具有Content-ID的部件，它们通常是内嵌资源
                        if (contentId != null && !contentId.isEmpty()) {
                            EmbeddedFile embeddedFile = new EmbeddedFile();
                            embeddedFile.setContentId(contentId);
                            // 创建临时文件，并确保它被正确关闭
                            try {
                                InputStream inputStream = bodyPart.getInputStream();
                                Path tempFile = Files.createTempFile("email" + UUID.randomUUID() + contentId, null); // 创建临时文件，前缀为"email-attachment-"
                                Files.copy(inputStream, tempFile, StandardCopyOption.REPLACE_EXISTING); // 将输入流内容复制到临时文件
                                File file = tempFile.toFile(); // 转换Path为File对象
                                embeddedFile.setFile(file);
                                embeddedFiles.add(embeddedFile);
                            } catch (IOException e) {
//                                e.printStackTrace();
                                log.info("处理内嵌资源出错：{}", e.getMessage());
                            }
                            continue;
                        }
                        if (fileName == null || fileName.trim().isEmpty()) {
                            // 如果没有文件名，创建一个默认名称
                            fileName = "unnamed_attachment_" + j;
                        }
                        // 创建并填充附件实体对象
                        MMailAttached mMailAttached = new MMailAttached();
                        mMailAttached.setFkMailAccountId(mMailAccount.getId());
                        mMailAttached.setMailId(mailId);
                        mMailAttached.setFileName(fileName);
                        mMailAttached.setFileId(String.valueOf(j));
                        // 获取文件扩展名
                        int lastDotIndex = fileName.lastIndexOf('.');
                        String suffixName = "";
                        if (lastDotIndex != -1) {
                            if (fileName.substring(lastDotIndex + 1).length() > 4) {
                                suffixName = fileName.substring(fileName.length() - 4);
                                mMailAttached.setFileExtension(fileName.substring(fileName.length() - 4));
                            } else {
                                suffixName = fileName.substring(lastDotIndex + 1);
                                mMailAttached.setFileExtension(fileName.substring(lastDotIndex + 1));
                            }
                        } else {
                            mMailAttached.setFileExtension(".");
                        }
                        mMailAttached.setGmtCreate(LocalDateTime.now());
                        mMailAttacheds.add(mMailAttached);
                        if (mMailAttached.getFileExtension() != null && mMailAttached.getFileExtension().equals("docx") || mMailAttached.getFileExtension().equals("doc")) {
                            try {
                                InputStream inputStream = bodyPart.getInputStream();
                                // 创建临时文件，并确保它被正确关闭
                                Path tempFile = Files.createTempFile(fileName, suffixName); // 创建临时文件，前缀为"email-attachment-"
                                Files.copy(inputStream, tempFile, StandardCopyOption.REPLACE_EXISTING); // 将输入流内容复制到临时文件
                                File file = tempFile.toFile(); // 转换Path为File对象
                                files.add(file); // 添加到你的文件列表中
                            } catch (IOException e) {
                                files.add(null);
//                                e.printStackTrace();
                                log.info("处理附件出错：{}", e.getMessage());
                            }
                        } else {
                            files.add(null);
                        }
                    }
                } catch (Exception e) {
//                    e.printStackTrace();
                    // 记录错误但继续处理其他部分
                    log.info("Error processing attachment: {}", e.getMessage());
                }
            }
        }
    }


    /*@Override
    public void fetchAllMail(MMailAccount mMailAccount, MMail mMail, MailBox mailBox, String foldName, String saveFoldName) throws Exception {
        Store store = mailBox.getStore();
        Folder folder = store.getFolder(foldName);
        folder.open(Folder.READ_ONLY);
        // 转换为 Date
        Date afterDate = Date.from(mMail.getDate().atZone(ZoneId.systemDefault()).toInstant());
        // 使用 ComparisonTerm.GT 表示"大于"，即晚于给定日期
        SearchTerm receivedAfter = new ReceivedDateTerm(ComparisonTerm.GT, afterDate);
        // 搜索邮件
        Message[] messages = folder.search(receivedAfter);
        QueryWrapper<MMail> queryWrapper = new QueryWrapper<>();
        queryWrapper.apply("DATE(date) = {0}", mMail.getDate().toLocalDate());
        queryWrapper.eq("fk_mail_account_id", mMailAccount.getId());
        List<MMail> mails = mailMapper.selectList(queryWrapper);
        List<String> mailIds = mails.stream()
                .map(MMail::getMailId) // 提取每个 MMail 对象的 mailId 属性
                .filter(Objects::nonNull) // 可选：过滤掉可能为 null 的值
                .collect(Collectors.toList()); // 收集结果到一个新的 List<String> 中
        // 多线程处理
        ExecutorService executor = Executors.newFixedThreadPool(10);
        for (int i = messages.length - 1; i >= 0; i--) {
            Message message = messages[i];
            MimeMessage mimeMessage = (MimeMessage) message;
            // 多线程处理
            executor.submit(() -> {
                try {
                    // 获取邮件id
                    String mailId;
                    if (!folder.isOpen()) {
                        folder.open(Folder.READ_ONLY);
                    }
                    if (mimeMessage.getMessageID() != null && !mimeMessage.getMessageID().isEmpty()) {
                        mailId = mimeMessage.getMessageID().replaceAll("<", "").replaceAll(">", "");
                    } else {
                        mailId = "";
                    }
                    if (!mailIds.contains(mailId)) {
                        mailIds.add(mailId);
                        FetchMailDto fetchMailDto = new FetchMailDto();
                        List<MMailAttached> mMailAttacheds = new ArrayList<>();
                        List<File> files = new ArrayList<>();
                        fetchMailDto.setMailAttachList(mMailAttacheds);
                        // 获取邮件的主题
                        if (!folder.isOpen()) {
                            folder.open(Folder.READ_ONLY);
                        }
                        String subject = mimeMessage.getSubject();
                        // 获取邮件内容
                        if (!folder.isOpen()) {
                            folder.open(Folder.READ_ONLY);
                        }
                        Object mailContent = mimeMessage.getContent();
                        // 获取邮件正文内容和附件
                        String content = "";
                        if (mailContent instanceof String) {
                            content = (String) mailContent;
                        } else if (mailContent instanceof Multipart) {
                            MimeMultipart multipart = (MimeMultipart) mailContent;
                            processAttachments(multipart, files, mMailAttacheds, mMailAccount, mailId);
                            int partCount = multipart.getCount();
                            for (int j = 0; j < partCount; j++) {
                                BodyPart bodyPart = multipart.getBodyPart(j);
                                if (bodyPart.isMimeType("text/html")) {
                                    try {
                                        content = java.net.URLDecoder.decode(String.valueOf(bodyPart.getContent()), "utf-8");
                                    } catch (Exception e) {
                                        System.out.println("解析附件中的内容出错" + e.getMessage());
                                    }
                                } else if (bodyPart.isMimeType("multipart/*")) {
                                    // 处理嵌套的多部分内容
                                    content = processNestedMultipart((MimeMultipart) bodyPart.getContent());
                                }
                            }
                        }
                        if (!folder.isOpen()) {
                            folder.open(Folder.READ_ONLY);
                        }
                        // 获取发件人
                        String from = ((InternetAddress) message.getFrom()[0]).getAddress();
                        // 获取收件人
                        StringBuilder toName = new StringBuilder();
                        // 获取收件人信息
                        if (!folder.isOpen()) {
                            folder.open(Folder.READ_ONLY);
                        }
                        Address[] recipients = message.getAllRecipients();
                        if (recipients != null) {
                            for (Address recipient : recipients) {
                                toName.append(((InternetAddress) recipient).getAddress()).append(",");
                            }
                        }
                        // 如果 toName 不为空，则删除最后一个逗号
                        if (toName.length() > 0) {
                            toName.setLength(toName.length() - 1); // 删除最后一个字符
                        }
                        // 获取抄送人
                        StringBuilder ccName = new StringBuilder();
                        if (!folder.isOpen()) {
                            folder.open(Folder.READ_ONLY);
                        }
                        Address[] ccAddresses = message.getRecipients(Message.RecipientType.CC);
                        if (ccAddresses != null && ccAddresses.length > 0) {
                            System.out.println("CC recipients for message with subject: " + message.getSubject());
                            for (Address address : ccAddresses) {
                                ccName.append(((InternetAddress) address).getAddress()).append(",");
                            }
                        }
                        // 如果 ccName 不为空，则删除最后一个逗号
                        if (ccName.length() > 0) {
                            ccName.setLength(ccName.length() - 1); // 删除最后一个字符
                        }
                        if (!folder.isOpen()) {
                            folder.open(Folder.READ_ONLY);
                        }
                        boolean isRead = message.isSet(Flags.Flag.SEEN);
                        if (!folder.isOpen()) {
                            folder.open(Folder.READ_ONLY);
                        }
                        boolean isStar = message.isSet(Flags.Flag.FLAGGED);
                        MMail mail = new MMail();
                        mail.setFkPlatformCode(mMailAccount.getFkPlatformCode());
                        mail.setFkPlatformUserId(mMailAccount.getFkPlatformUserId());
                        mail.setFkMailAccountId(mMailAccount.getId());
                        mail.setMailId(mailId);
                        mail.setFoldBox(saveFoldName);
                        mail.setSubject(subject);
                        mail.setBody(content);
                        mail.setFromMail(from);
                        mail.setToMail(toName.toString());
                        mail.setCcMail(ccName.toString());
                        mail.setSeparately(false);
                        // 获取邮件日期
                        if (!folder.isOpen()) {
                            folder.open(Folder.READ_ONLY);
                        }
                        Date date = mimeMessage.getReceivedDate();
                        // 将 Date 转换为 LocalDateTime
                        LocalDateTime localDateTime = date.toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDateTime();
                        mail.setDate(localDateTime);
                        mail.setLlmMailType(0);
                        mail.setIsRead(isRead);
                        mail.setStar(isStar);
                        mail.setGmtCreate(LocalDateTime.now());
                        try {
                            QueryWrapper<MMail> mMailQueryWrapper2 = new QueryWrapper<>();
                            mMailQueryWrapper2.eq("mail_id", mail.getMailId());
                            mMailQueryWrapper2.eq("fk_mail_account_id", mMailAccount.getId());
                            List<MMail> mailList = mailMapper.selectList(mMailQueryWrapper2);
                            if (mailList.isEmpty()) {
                                mailMapper.insert(mail);
                                for (int k = 0; k < mMailAttacheds.size(); k++) {
                                    MMailAttached mailAttached = mMailAttacheds.get(k);
                                    mailAttached.setFkMailId(mail.getId());
                                    attachedMapper.insert(mailAttached);
                                    if (mailAttached.getFileExtension() != null && mailAttached.getFileExtension().equals("docx") || mailAttached.getFileExtension().equals("doc")) {
                                        // 将附件保存
                                        File file = files.get(k);
                                        MFileMail fileMail = new MFileMail();
                                        // 源文件名
                                        String fileNameOrc = mailAttached.getFileName();
                                        // 获取后缀名
                                        String fileTypeOrc = mailAttached.getFileExtension();
                                        // 文件guid
                                        String fileGuid = UUID.randomUUID().toString().replaceAll("-", "");
                                        String fileurl = subString(AppendixUtils.getFileHtiPath(fileNameOrc));
                                        int j = fileurl.lastIndexOf("/");
                                        // 新文件名
                                        String fileName = fileurl.substring(j + 1, fileurl.length());
                                        fileMail.setFilePath(fileurl);
                                        fileMail.setFileNameOrc(fileNameOrc);
                                        fileMail.setFileTypeOrc(fileTypeOrc);
                                        fileMail.setFileName(fileName);
                                        fileMail.setFileGuid(fileGuid);
                                        fileMail.setGmtCreate(LocalDateTime.now());
                                        fileMail.setGmtCreateUser(mMailAccount.getGmtCreateUser());
                                        tencentCloudService.uploadObject(true, fileBucketName, file, fileurl);
                                        fileMail.setFileKey(fileurl);
                                        mFileMailMapper.insert(fileMail);
                                        // 本地删除
                                        Files.delete(file.toPath());
                                        SMediaAndAttachedEntity sMediaAndAttachedEntity = new SMediaAndAttachedEntity();
                                        sMediaAndAttachedEntity.setFkFileGuid(fileGuid);
                                        sMediaAndAttachedEntity.setFkTableId(mailAttached.getId());
                                        sMediaAndAttachedEntity.setFkTableName("m_mail_attached");
                                        sMediaAndAttachedEntity.setGmtCreate(LocalDateTime.now());
                                        sMediaAndAttachedEntity.setGmtCreateUser(mMailAccount.getGmtCreateUser());
                                        sMediaAndAttachedMapper.insert(sMediaAndAttachedEntity);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            System.out.println("手动获取邮件失败：------------------" + e.getMessage());
                            e.printStackTrace();
                        }
                    }
                } catch (Exception e) {
                    System.out.println("手动获取邮件失败：++++++++++++++++++++++++++" + e.getMessage());
                    e.printStackTrace();
                }
            });
        }
        // 关闭线程池，不再接受新的任务，但会继续处理已提交的任务
        executor.shutdown();
        // 等待所有任务完成，最长等待时间为五分钟
        executor.awaitTermination(60 * 5, TimeUnit.SECONDS);
    }*/

    @Override
    public MFileMail uploadFileToParse(MultipartFile file, boolean isBody) throws Exception {
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        String annexSavePath = env.getProperty("mail.up-annex-path");
        if (file.isEmpty()) {
            throw new GetServiceException(BAD_REQUEST, "上传失败");
        }
        String fileName1 = file.getOriginalFilename();
        String savePath = annexSavePath + UUID.randomUUID() + "_" + fileName1.replaceAll(" ", "").replaceAll(" ", "");
        File dest = new File(savePath);
        // 将文件保存到指定路径
        file.transferTo(dest);
        MFileMail fileMail = new MFileMail();
        // 源文件名
        String fileNameOrc = dest.getName();
        // 获取后缀名
        String fileTypeOrc = "";
        int lastDotIndex = fileNameOrc.lastIndexOf('.');
        if (lastDotIndex != -1) {
            if (fileNameOrc.substring(lastDotIndex + 1).length() > 4) {
                fileTypeOrc = fileNameOrc.substring(fileNameOrc.length() - 4);
            } else {
                fileTypeOrc = fileNameOrc.substring(lastDotIndex + 1);
            }
        }
        // 文件guid
        String fileGuid = UUID.randomUUID().toString().replaceAll("-", "");
        String fileurl = subString(AppendixUtils.getFileHtiPath(fileNameOrc));
        int j = fileurl.lastIndexOf("/");
        // 新文件名
        String fileName = fileurl.substring(j + 1, fileurl.length());
        fileMail.setFilePath(fileurl);
        fileMail.setFileNameOrc(fileName1);
        fileMail.setFileTypeOrc(fileTypeOrc);
        fileMail.setFileName(fileName);
        fileMail.setFileGuid(fileGuid);
        fileMail.setGmtCreate(LocalDateTime.now());
        fileMail.setGmtCreateUser(staffInfo.getName());
        String bucketName = isBody ? imageBucketName : fileBucketName;
        log.info("文件传入的桶的名字是{}", bucketName);
        tencentCloudService.uploadObject(true, bucketName, dest, fileurl);
        fileMail.setFileKey(fileurl);
        mFileMailMapper.insert(fileMail);
        // 本地删除
        Files.delete(dest.toPath());
        return fileMail;
    }

    @Override
    public CountNotReadNumVo countNotReadNum(SearchMailVo searchMailVo) throws Exception {
        String emailAccount = searchMailVo.getEmailAccount();
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        Long userId = staffInfo.getStaffId();
        LambdaQueryWrapper<MMailAccount> accountQueryWrapper = new LambdaQueryWrapper<>();
        if (emailAccount == null || emailAccount.isEmpty()) {
            accountQueryWrapper.eq(MMailAccount::getFkPlatformUserId, userId).eq(MMailAccount::getIsDefault, true);

        } else {
            accountQueryWrapper.eq(MMailAccount::getFkPlatformUserId, userId).eq(MMailAccount::getEmailAccount, emailAccount);
        }
        List<MMailAccount> accounts = accountMapper.selectList(accountQueryWrapper);
        if (accounts.isEmpty()) {
            throw new GetServiceException(BAD_REQUEST, "mail account is not exist");
        }
        Long id = accounts.get(0).getId();
        searchMailVo.setMailAccountId(id);
        // 新申请未读数量
        int newApplicationNum = 0;
        // 新申请今天未读数量
        int todayNewApplicationNum = 0;
        // 新申请昨天未读数量
        int yesterdayNewApplicationNum = 0;
        // 新申请其他未读数量
        int otherNewApplicationNum = 0;

        // 提交完成数量
        int finishSubmitNum = 0;
        // 提交完成今天未读数
        int todayFinishSubmitNum = 0;
        // 提交完成昨天未读数
        int yesterdayFinishSubmitNum = 0;
        // 提交完成其他未读数
        int otherFinishSubmitNum = 0;

        // 已录取数量
        int admissionNum = 0;
        // 已录取今天未读数
        int todayAdmissionNum = 0;
        // 已录取昨天未读数
        int yesterdayAdmissionNum = 0;
        // 已录取其他维度
        int otherAdmissionNum = 0;

        // 已付学费
        int payTuitionNum = 0;
        // 已付学费今天未读数
        int todayPayTuitionNum = 0;
        // 已付学费昨天未读书
        int yesterdayPayTuitionNum = 0;
        // 已付学费其他未读数
        int otherPayTuitionNum = 0;

        // 收到签证涵
        int visaNum = 0;
        // 收到签证涵今天未读数
        int todayVisaNum = 0;
        // 收到签证涵昨天未读数
        int yesterdayVisaNum = 0;
        // 收到签证涵其他未读
        int otherVisaNum = 0;

        // 获得签证
        int getVisaNum = 0;
        // 获得签证今天未读数
        int todayGetVisaNum = 0;
        // 获得签证昨天未读数
        int yesterdayGetVisaNum = 0;
        // 获得签证其他未读数
        int otherGetVisaNum = 0;

        // 系统提醒未读总数
        int systemReminderNum = 0;
        // 系统提醒今天未读总数
        int todaySystemReminderNum = 0;
        // 系统提醒昨天未读总数
        int yesterdaySystemReminderNum = 0;
        // 系统其他未读总数
        int otherSystemReminderNum = 0;

        // 未读数量
        int notReadNum = 0;
        // 今天未读数量
        int todayNotReadNum = 0;
        // 昨天未读数量
        int yesterdayNotReadNum = 0;
        // 其他未读数量
        int otherNotReadNum = 0;

        SearchMailVo searchMailVo1 = new SearchMailVo();
        searchMailVo1.setEmailAccount(searchMailVo.getEmailAccount());
        searchMailVo1.setMailAccountId(searchMailVo.getMailAccountId());
        searchMailVo1.setBoxName(searchMailVo.getBoxName());
//            searchMailVo1.setLlmMailType(searchMailVo.getLlmMailType());
        searchMailVo1.setDateSort(1);
        todayNotReadNum = mailMapper.selectNotRead(searchMailVo1);
        searchMailVo1.setDateSort(2);
        yesterdayNotReadNum = mailMapper.selectNotRead(searchMailVo1);
        searchMailVo1.setDateSort(3);
        otherNotReadNum = mailMapper.selectNotRead(searchMailVo1);
        searchMailVo1.setDateSort(4);
        notReadNum = mailMapper.selectNotRead(searchMailVo1);


        // 处理新申请未读数
        searchMailVo1.setLlmMailType(1);
        searchMailVo1.setDateSort(1);
        todayNewApplicationNum = mailMapper.selectNotRead(searchMailVo1);
        // 处理新申请昨天未读数
        searchMailVo1.setDateSort(2);
        yesterdayNewApplicationNum = mailMapper.selectNotRead(searchMailVo1);
        // 处理新申请其他未读数
        searchMailVo1.setDateSort(3);
        otherNewApplicationNum = mailMapper.selectNotRead(searchMailVo1);
        // 处理新申请总的未读数
        searchMailVo1.setDateSort(4);
        newApplicationNum = mailMapper.selectNotRead(searchMailVo1);

        // 处理提交完成未读数
        searchMailVo1.setLlmMailType(2);
        searchMailVo1.setDateSort(1);
        todayFinishSubmitNum = mailMapper.selectNotRead(searchMailVo1);
        // 处理提交完成昨天未读数
        searchMailVo1.setDateSort(2);
        yesterdayFinishSubmitNum = mailMapper.selectNotRead(searchMailVo1);
        // 处理提交完成其他未读数
        searchMailVo1.setDateSort(3);
        otherFinishSubmitNum = mailMapper.selectNotRead(searchMailVo1);
        // 处理提交完成总的未读数
        searchMailVo1.setDateSort(4);
        finishSubmitNum = mailMapper.selectNotRead(searchMailVo1);

        // 处理已录取未读数
        searchMailVo1.setLlmMailType(3);
        searchMailVo1.setDateSort(1);
        todayAdmissionNum = mailMapper.selectNotRead(searchMailVo1);
        // 处理已录取昨天未读数
        searchMailVo1.setDateSort(2);
        yesterdayAdmissionNum = mailMapper.selectNotRead(searchMailVo1);
        // 处理已录取其他未读数
        searchMailVo1.setDateSort(3);
        otherAdmissionNum = mailMapper.selectNotRead(searchMailVo1);
        // 处理已录取总的未读数
        searchMailVo1.setDateSort(4);
        admissionNum = mailMapper.selectNotRead(searchMailVo1);
        // 处理已录取未读数
        searchMailVo1.setLlmMailType(4);
        searchMailVo1.setDateSort(1);
        todayPayTuitionNum = mailMapper.selectNotRead(searchMailVo1);
        // 处理已录取昨天未读数
        searchMailVo1.setDateSort(2);
        yesterdayPayTuitionNum = mailMapper.selectNotRead(searchMailVo1);
        // 处理已录取其他未读数
        searchMailVo1.setDateSort(3);
        otherPayTuitionNum = mailMapper.selectNotRead(searchMailVo1);
        // 处理已录取总的未读数
        searchMailVo1.setDateSort(4);
        payTuitionNum = mailMapper.selectNotRead(searchMailVo1);
        // 处理签证涵未读数
        searchMailVo1.setLlmMailType(5);
        searchMailVo1.setDateSort(1);
        todayVisaNum = mailMapper.selectNotRead(searchMailVo1);
        // 处理签证涵昨天未读数
        searchMailVo1.setDateSort(2);
        yesterdayVisaNum = mailMapper.selectNotRead(searchMailVo1);
        // 处理签证涵其他未读数
        searchMailVo1.setDateSort(3);
        otherVisaNum = mailMapper.selectNotRead(searchMailVo1);
        // 处理签证涵总的未读数
        searchMailVo1.setDateSort(4);
        visaNum = mailMapper.selectNotRead(searchMailVo1);
        // 处理获得签证未读数
        searchMailVo1.setLlmMailType(6);
        searchMailVo1.setDateSort(1);
        todayGetVisaNum = mailMapper.selectNotRead(searchMailVo1);
        // 处理获得签证昨天未读数
        searchMailVo1.setDateSort(2);
        yesterdayGetVisaNum = mailMapper.selectNotRead(searchMailVo1);
        // 处理获得签证其他未读数
        searchMailVo1.setDateSort(3);
        otherGetVisaNum = mailMapper.selectNotRead(searchMailVo1);
        // 处理获得签证总的未读数
        searchMailVo1.setDateSort(4);
        getVisaNum = mailMapper.selectNotRead(searchMailVo1);
        // 处理系统提醒未读数
        searchMailVo1.setLlmMailType(8);
        searchMailVo1.setDateSort(1);
        todaySystemReminderNum = mailMapper.selectNotRead(searchMailVo1);
        // 处理系统提醒昨天未读数
        searchMailVo1.setDateSort(2);
        yesterdaySystemReminderNum = mailMapper.selectNotRead(searchMailVo1);
        // 处理系统提醒其他未读数
        searchMailVo1.setDateSort(3);
        otherSystemReminderNum = mailMapper.selectNotRead(searchMailVo1);
        // 处理系统提醒总的未读数
        searchMailVo1.setDateSort(4);
        systemReminderNum = mailMapper.selectNotRead(searchMailVo1);

        CountNotReadNumVo countNotReadNumVo = new CountNotReadNumVo();


        // 添加数量
        countNotReadNumVo.setNotReadNum(notReadNum);
        countNotReadNumVo.setTodayNotReadNum(todayNotReadNum);
        countNotReadNumVo.setYesterdayNotReadNum(yesterdayNotReadNum);
        countNotReadNumVo.setOtherNotReadNum(otherNotReadNum);
        // 处理新申请类型未读数
        countNotReadNumVo.setNewApplicationNum(newApplicationNum);
        countNotReadNumVo.setYesterdayNewApplicationNum(yesterdayNewApplicationNum);
        countNotReadNumVo.setOtherNewApplicationNum(otherNewApplicationNum);
        countNotReadNumVo.setTodayNewApplicationNum(todayNewApplicationNum);
        // 处理提交完成未读数
        countNotReadNumVo.setFinishSubmitNum(finishSubmitNum);
        countNotReadNumVo.setTodayFinishSubmitNum(todayFinishSubmitNum);
        countNotReadNumVo.setYesterdayFinishSubmitNum(yesterdayFinishSubmitNum);
        countNotReadNumVo.setOtherFinishSubmitNum(otherFinishSubmitNum);
        // 处理已录取未读数
        countNotReadNumVo.setAdmissionNum(admissionNum);
        countNotReadNumVo.setTodayAdmissionNum(todayAdmissionNum);
        countNotReadNumVo.setYesterdayAdmissionNum(yesterdayAdmissionNum);
        countNotReadNumVo.setOtherAdmissionNum(otherAdmissionNum);
        // 处理已付学费未读数
        countNotReadNumVo.setPayTuitionNum(payTuitionNum);
        countNotReadNumVo.setTodayPayTuitionNum(todayPayTuitionNum);
        countNotReadNumVo.setYesterdayPayTuitionNum(yesterdayPayTuitionNum);
        countNotReadNumVo.setOtherPayTuitionNum(otherPayTuitionNum);
        // 处理签证涵
        countNotReadNumVo.setVisaNum(visaNum);
        countNotReadNumVo.setTodayVisaNum(todayVisaNum);
        countNotReadNumVo.setYesterdayVisaNum(yesterdayVisaNum);
        countNotReadNumVo.setOtherVisaNum(otherVisaNum);
        // 处理获得签证
        countNotReadNumVo.setGetVisaNum(getVisaNum);
        countNotReadNumVo.setTodayGetVisaNum(todayGetVisaNum);
        countNotReadNumVo.setYesterdayGetVisaNum(yesterdayGetVisaNum);
        countNotReadNumVo.setOtherGetVisaNum(otherGetVisaNum);
        // 处理系统提醒
        countNotReadNumVo.setSystemReminderNum(systemReminderNum);
        countNotReadNumVo.setTodaySystemReminderNum(todaySystemReminderNum);
        countNotReadNumVo.setYesterdaySystemReminderNum(yesterdaySystemReminderNum);
        countNotReadNumVo.setOtherSystemReminderNum(otherSystemReminderNum);
        return countNotReadNumVo;
    }

    @Override
    public Message sendMail(Session session, MailBox mailBox, String foldName, SendMailVo sendMailVo, MMailAccount account) throws Exception {
        List<Path> needDeletePath = new ArrayList<>();
        try {
            Store store = mailBox.getStore();
            // 创建MimeMessage对象
            Message message = new MimeMessage(session);
            // 发件人
            message.setFrom(new InternetAddress(sendMailVo.getEmailAccount()));
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(sendMailVo.getRecipient()));
            // 抄送人，多个抄送人之间用，号隔开
            if (sendMailVo.getCcPeople() != null && !sendMailVo.getCcPeople().isEmpty()) {
                message.setRecipients(Message.RecipientType.CC, InternetAddress.parse(sendMailVo.getCcPeople()));
            }
            // 密送人，多个密送人直接用“,”号隔开
            if (sendMailVo.getBccPeople() != null && !sendMailVo.getBccPeople().isEmpty()) {
                message.setRecipients(Message.RecipientType.BCC, InternetAddress.parse(sendMailVo.getBccPeople()));
            }
            // 邮件标题
            message.setSubject(sendMailVo.getSubject());
            BodyPart messageBodyPart = new MimeBodyPart();
            // 处理内嵌文件
            String bodyContent = sendMailVo.getContent();
            // 正则表达式匹配<img>标签中的src属性
            String imgRegex = "<img[^>]+src\\s*=\\s*['\"]([^'\"]+)['\"][^>]*>";
            Pattern pattern = Pattern.compile(imgRegex, Pattern.CASE_INSENSITIVE);
            Matcher matcher = pattern.matcher(bodyContent);
            // 创建一个列表来保存所有的src属性值
            List<String> srcList = new ArrayList<>();
            // 查找所有匹配的src属性值并添加到列表中
            while (matcher.find()) {
                srcList.add(matcher.group(1));
            }
            // 将body中的原本在桶里面的内容，替换成cid，形式。
            List<ContentReplace> contentIds = new ArrayList<>();
            String bucket = "https://" + imageBucketName + ".cos.ap-shanghai.myqcloud.com";
            for (String str : srcList) {
                if (str.contains(bucket)) {
                    String contentId = UUID.randomUUID().toString();
                    String bodyContentId = "cid:" + contentId;
                    bodyContent = bodyContent.replace(str, bodyContentId);
                    ContentReplace contentReplace = new ContentReplace();
                    contentReplace.setKey(str.replace(bucket, ""));
                    contentReplace.setContentId(contentId);
                    contentIds.add(contentReplace);
                }
            }
            // 邮件内容
            messageBodyPart.setContent(bodyContent, "text/html; charset=UTF-8");
            // 创建多部件消息
            Multipart multipart = new MimeMultipart();
            // body 中的html添加
            multipart.addBodyPart(messageBodyPart);
            // 添加嵌套附件
            for (ContentReplace contentReplace : contentIds) {
                QueryWrapper<MFileMail> mFileMailQueryWrapper = new QueryWrapper<>();
                mFileMailQueryWrapper.eq("file_key", contentReplace.getKey());
                List<MFileMail> mFileMailList = mFileMailMapper.selectList(mFileMailQueryWrapper);
                if (!mFileMailList.isEmpty()) {
                    MFileMail mFileMail = mFileMailList.get(0);
                    Path tempFile = Files.createTempFile(contentReplace.getContentId(), null);
                    File file = tempFile.toFile();
                    try {
                        tencentCloudService.downLoadTofile(mFileMail, true, file);
                        messageBodyPart = new MimeBodyPart();
                        DataSource fds = new FileDataSource(file);
                        messageBodyPart.setDataHandler(new DataHandler(fds));
                        // 这里的 Content-ID 应与 HTML 中的 cid 匹配
                        messageBodyPart.setHeader("Content-ID", contentReplace.getContentId());
                        messageBodyPart.setFileName(file.getName());
                        multipart.addBodyPart(messageBodyPart);
                    } finally {
                        // 将需要删除的路径保存
                        needDeletePath.add(tempFile);
                        if (!mFileMail.getFileKey().contains("signature")) {
                            // 在桶中将文件删除
                            tencentCloudService.deleteObject(imageBucketName, mFileMail.getFileKey());
                            // 在 mFile库中将数据删除
                            mFileMailMapper.deleteById(mFileMail.getId());
                        }
                    }
                }
            }
            // 如果是已经存在的草稿附件处理
            if (sendMailVo.getMailId() != null && !sendMailVo.getMailId().isEmpty()) {
                Long id = sendMailVo.getId();
                QueryWrapper<MMail> mMailQueryWrapper = new QueryWrapper<>();
                mMailQueryWrapper.eq("id", id);
                List<MMail> mailList = mailMapper.selectList(mMailQueryWrapper);
                if (!mailList.isEmpty()) {
                    MMail mMail = mailList.get(0);
                    String mailInFold = env.getProperty("mail." + account.getEmailType() + "-" + mMail.getFoldBox());
                    // 打开已发送邮件文件夹
                    Folder mailOriginalFold = store.getFolder(mailInFold);
                    mailOriginalFold.open(Folder.READ_WRITE);
                    IMAPFolder imapFolder = (IMAPFolder) mailOriginalFold;
                    long uid = Long.parseLong(sendMailVo.getMailId());
                    Message originalMessage = imapFolder.getMessageByUID(uid);
                    // 取出来指定的邮件之后，删除用户删除的附件
                    MimeMessage mimeMessage = (MimeMessage) originalMessage;
                    if (mimeMessage.isMimeType("multipart/*")) {
                        MimeMultipart mimeMultipart = (MimeMultipart) mimeMessage.getContent();
                        int partCount = mimeMultipart.getCount();
                        for (int i = 0; i < partCount; i++) {
                            BodyPart bodyPart = mimeMultipart.getBodyPart(i);
                            if (Part.ATTACHMENT.equalsIgnoreCase(bodyPart.getDisposition())) {
                                // 获取 Content-ID 头信息并去除角括号
                                String contentId = null;
                                String[] contentIdHeaders = bodyPart.getHeader("Content-ID");
                                if (contentIdHeaders != null && contentIdHeaders.length > 0) {
                                    contentId = contentIdHeaders[0].trim();
                                    if (contentId.startsWith("<") && contentId.endsWith(">")) {
                                        contentId = contentId.substring(1, contentId.length() - 1).trim();
                                    }
                                }
                                // 忽略具有Content-ID的部件，它们通常是内嵌资源
                                if (contentId != null && !contentId.isEmpty()) {
                                    continue;
                                }
                                // 获取附件名称
                                String fileName = bodyPart.getFileName();
                                fileName = MimeUtility.decodeText(fileName);
                                boolean needDelete = false;
                                for (AttachedDelete attachedDelete : sendMailVo.getAttachedDeletes()) {
                                    if ((fileName.equals(attachedDelete.getAttachName()) || fileName.equals(attachedDelete.getAttachName().replaceAll(" ", " "))) && String.valueOf(i).equals(attachedDelete.getAttachId())) {
                                        needDelete = true;
                                    }
                                }
                                if (!needDelete) {
                                    Path tempFile = Files.createTempFile(UUID.randomUUID().toString(), null);
                                    try {
                                        File file = tempFile.toFile();
                                        MailBoxUtils.saveAttachment(bodyPart, file.getAbsolutePath());
                                        // 将附件添加到邮件中
                                        messageBodyPart = new MimeBodyPart();
                                        DataSource source = new FileDataSource(file);
                                        messageBodyPart.setDataHandler(new DataHandler(source));
                                        messageBodyPart.setFileName(fileName);
                                        multipart.addBodyPart(messageBodyPart);
                                    } finally {
                                        // 将需要删除的路径保存
                                        needDeletePath.add(tempFile);
                                    }
                                }
                            }
                        }
                    }
                    // 如果如果邮件在草稿箱应该从草稿箱删除这封邮件
                    if (mMail.getFoldBox().equals("draftList")) {
                        // 标记原邮件为删除
                        originalMessage.setFlag(Flags.Flag.DELETED, true);
                        mailOriginalFold.close(true);
                        LambdaQueryWrapper<MMail> mailQueryWrapper = new LambdaQueryWrapper<>();
                        mailQueryWrapper.eq(MMail::getMailId, sendMailVo.getMailId());
                        mailQueryWrapper.eq(MMail::getFkMailAccountId, account.getId());
                        mailQueryWrapper.eq(MMail::getId, sendMailVo.getId());
                        mailMapper.delete(mailQueryWrapper);
                        /*// doris同步删除
                        mailDorisMapper.deleteMailDoris(mMail.getId());*/
                        LambdaQueryWrapper<MMailAttached> mailAttachedQueryWrapper = new LambdaQueryWrapper<>();
                        mailAttachedQueryWrapper.eq(MMailAttached::getMailId, sendMailVo.getMailId());
                        mailAttachedQueryWrapper.eq(MMailAttached::getFkMailAccountId, account.getId());
                        // 删除附件桶里面的附件一同删除
                        List<MMailAttached> mMailAttachedList = attachedMapper.selectList(mailAttachedQueryWrapper);
                        for (MMailAttached mMailAttached : mMailAttachedList) {
                            LambdaQueryWrapper<SMediaAndAttachedEntity> sMediaAndAttachedEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
                            sMediaAndAttachedEntityLambdaQueryWrapper.eq(SMediaAndAttachedEntity::getFkTableName, "m_mail_attached");
                            sMediaAndAttachedEntityLambdaQueryWrapper.eq(SMediaAndAttachedEntity::getFkTableId, mMailAttached.getId());
                            List<SMediaAndAttachedEntity> sMediaAndAttachedEntityList = sMediaAndAttachedMapper.selectList(sMediaAndAttachedEntityLambdaQueryWrapper);
                            for (SMediaAndAttachedEntity sMediaAndAttachedEntity : sMediaAndAttachedEntityList) {
                                String guid = sMediaAndAttachedEntity.getFkFileGuid();
                                LambdaQueryWrapper<MFileMail> mFileMailQueryWrapper = new LambdaQueryWrapper<>();
                                mFileMailQueryWrapper.eq(MFileMail::getFileGuid, guid);
                                List<MFileMail> mFileMailList = mFileMailMapper.selectList(mFileMailQueryWrapper);
                                for (MFileMail mFileMail : mFileMailList) {
                                    // 从桶里面删除
                                    tencentCloudService.deleteObject(fileBucketName, mFileMail.getFileKey());
                                    mFileMailMapper.deleteById(mFileMail.getId());
                                }
                                sMediaAndAttachedMapper.deleteById(sMediaAndAttachedEntity.getId());
                            }
                            attachedMapper.deleteById(mMailAttached.getId());
                        }
                        LambdaQueryWrapper<MMailSyncQueue> mailSyncQueueQueryWrapper = new LambdaQueryWrapper<>();
                        mailSyncQueueQueryWrapper.eq(MMailSyncQueue::getMailId, sendMailVo.getMailId());
                        mailSyncQueueQueryWrapper.eq(MMailSyncQueue::getFkMailAccountId, account.getId());
                        mailSyncQueueQueryWrapper.eq(MMailSyncQueue::getFkMailId, mMail.getId());
                        List<MMailSyncQueue> mMailSyncQueueList = syncQueueMapper.selectList(mailSyncQueueQueryWrapper);
                        for (MMailSyncQueue mMailSyncQueue : mMailSyncQueueList) {
                            // doris同步删除
                            mailSyncQueueDorisMapper.deleteById(mMailSyncQueue.getId());
                        }
                        syncQueueMapper.delete(mailSyncQueueQueryWrapper);

                    }
                }
            }
            // 加载真实附件
            if (!sendMailVo.getAttachments().isEmpty()) {
                for (MFileMail mFileMail : sendMailVo.getAttachments()) {
                    QueryWrapper<MFileMail> mFileMailQueryWrapper = new QueryWrapper<>();
                    mFileMailQueryWrapper.eq("file_key", mFileMail.getFileKey());
                    List<MFileMail> mFileMailList = mFileMailMapper.selectList(mFileMailQueryWrapper);
                    if (!mFileMailList.isEmpty()) {
                        MFileMail mFileMail1 = mFileMailList.get(0);
                        Path tempFile = Files.createTempFile(mFileMail1.getFileGuid(), null);
                        File file = tempFile.toFile();
                        try {
                            tencentCloudService.downLoadTofile(mFileMail1, false, file);
                            // 将附件添加到邮件中
                            messageBodyPart = new MimeBodyPart();
                            DataSource source = new FileDataSource(file);
                            messageBodyPart.setDataHandler(new DataHandler(source));
                            messageBodyPart.setFileName(mFileMail1.getFileNameOrc());
                            multipart.addBodyPart(messageBodyPart);
                        } finally {
                            // 将需要删除的路径保存
                            needDeletePath.add(tempFile);
                            // 在桶中将文件删除
                            tencentCloudService.deleteObject(imageBucketName, mFileMail1.getFileKey());
                            // 在 mFile库中将数据删除
                            mFileMailMapper.deleteById(mFileMail.getId());
                        }
                    }
                }
            }
            // 将多部件消息设置为邮件内容
            message.setContent(multipart);
            // 发送邮件
            Transport.send(message);
            // 打开已发送邮件文件夹
            Folder sentFolder = store.getFolder(foldName);
            sentFolder.open(Folder.READ_WRITE);
            // 将邮件复制到已发送邮件文件夹
            sentFolder.appendMessages(new Message[]{message});
            sentFolder.close(true);
            return message;
        } finally {
            for (Path path : needDeletePath) {
                log.info("删除临时文件");
                Files.delete(path);
            }
        }
    }

    @Override
    public Message saveMail(MailBox mailBox, String foldName, SendMailVo sendMailVo, MMailAccount account) throws Exception {
        List<Path> needDeletePath = new ArrayList<>();
        try {
            Store store = mailBox.getStore();
            // 打开已发送邮件文件夹
            Folder draftFold = store.getFolder(foldName);
            draftFold.open(Folder.READ_WRITE);
            // 创建MimeMessage对象
            Message message = new MimeMessage(mailBox.getSession());
            if (sendMailVo.getEmailAccount() != null) {
                // 发件人
                message.setFrom(new InternetAddress(sendMailVo.getEmailAccount()));
            }
            if (sendMailVo.getRecipient() != null && !sendMailVo.getRecipient().isEmpty()) {
                message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(sendMailVo.getRecipient()));
            }
            // 抄送人，多个抄送人之间用，号隔开
            if (sendMailVo.getCcPeople() != null && !sendMailVo.getCcPeople().isEmpty()) {
                message.setRecipients(Message.RecipientType.CC, InternetAddress.parse(sendMailVo.getCcPeople()));
            }
            // 密送人，多个密送人直接用“,”号隔开
            if (sendMailVo.getBccPeople() != null && !sendMailVo.getBccPeople().isEmpty()) {
                message.setRecipients(Message.RecipientType.BCC, InternetAddress.parse(sendMailVo.getBccPeople()));
            }
            if (sendMailVo.getSubject() != null) {
                // 邮件标题
                message.setSubject(sendMailVo.getSubject());
            }
            BodyPart messageBodyPart = new MimeBodyPart();
            // 将body中的原本在桶里面的内容，替换成cid，形式。
            List<ContentReplace> contentIds = new ArrayList<>();
            if (sendMailVo.getContent() != null) {
                // 处理内嵌文件
                String bodyContent = sendMailVo.getContent();
                // 正则表达式匹配<img>标签中的src属性
                String imgRegex = "<img[^>]+src\\s*=\\s*['\"]([^'\"]+)['\"][^>]*>";
                Pattern pattern = Pattern.compile(imgRegex, Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(bodyContent);
                // 创建一个列表来保存所有的src属性值
                List<String> srcList = new ArrayList<>();
                // 查找所有匹配的src属性值并添加到列表中
                while (matcher.find()) {
                    srcList.add(matcher.group(1));
                }
                String bucket = "https://" + imageBucketName + ".cos.ap-shanghai.myqcloud.com";
                for (String str : srcList) {
                    if (str.contains(bucket)) {
                        String contentId = UUID.randomUUID().toString();
                        String bodyContentId = "cid:" + contentId;
                        bodyContent = bodyContent.replace(str, bodyContentId);
                        ContentReplace contentReplace = new ContentReplace();
                        contentReplace.setKey(str.replace(bucket, ""));
                        contentReplace.setContentId(contentId);
                        contentIds.add(contentReplace);
                    }
                }
                // 邮件内容
                messageBodyPart.setContent(bodyContent, "text/html; charset=UTF-8");
            }
            // 创建多部件消息
            Multipart multipart = new MimeMultipart();
            // body 中的html添加
            multipart.addBodyPart(messageBodyPart);
            // 添加嵌套附件
            for (ContentReplace contentReplace : contentIds) {
                QueryWrapper<MFileMail> mFileMailQueryWrapper = new QueryWrapper<>();
                mFileMailQueryWrapper.eq("file_key", contentReplace.getKey());
                List<MFileMail> mFileMailList = mFileMailMapper.selectList(mFileMailQueryWrapper);
                if (!mFileMailList.isEmpty()) {
                    MFileMail mFileMail = mFileMailList.get(0);
                    Path tempFile = Files.createTempFile(contentReplace.getContentId(), null);
                    File file = tempFile.toFile();
                    try {
                        tencentCloudService.downLoadTofile(mFileMail, true, file);
                        messageBodyPart = new MimeBodyPart();
                        DataSource fds = new FileDataSource(file);
                        messageBodyPart.setDataHandler(new DataHandler(fds));
                        // 这里的 Content-ID 应与 HTML 中的 cid 匹配
                        messageBodyPart.setHeader("Content-ID", contentReplace.getContentId());
                        messageBodyPart.setFileName(file.getName());
                        multipart.addBodyPart(messageBodyPart);
                    } finally {
                        // 将需要删除的路径保存
                        needDeletePath.add(tempFile);
                        // 在桶中将文件删除
                        tencentCloudService.deleteObject(imageBucketName, mFileMail.getFileKey());
                        // 在 mFile库中将数据删除
                        mFileMailMapper.deleteById(mFileMail.getId());
                    }
                }
            }
            // 如果是已经存在的草稿附件处理
            if (sendMailVo.getMailId() != null && !sendMailVo.getMailId().isEmpty()) {
                IMAPFolder imapFolder = (IMAPFolder) draftFold;
                long uid = Long.parseLong(sendMailVo.getMailId());
                Message originalMessage = imapFolder.getMessageByUID(uid);
                // 取出来指定的邮件之后，删除用户删除的附件
                MimeMessage mimeMessage = (MimeMessage) originalMessage;
                if (mimeMessage.isMimeType("multipart/*")) {
                    MimeMultipart mimeMultipart = (MimeMultipart) mimeMessage.getContent();
                    int partCount = mimeMultipart.getCount();
                    for (int i = 0; i < partCount; i++) {
                        BodyPart bodyPart = mimeMultipart.getBodyPart(i);
                        if (Part.ATTACHMENT.equalsIgnoreCase(bodyPart.getDisposition())) {
                            // 获取 Content-ID 头信息并去除角括号
                            String contentId = null;
                            String[] contentIdHeaders = bodyPart.getHeader("Content-ID");
                            if (contentIdHeaders != null && contentIdHeaders.length > 0) {
                                contentId = contentIdHeaders[0].trim();
                                if (contentId.startsWith("<") && contentId.endsWith(">")) {
                                    contentId = contentId.substring(1, contentId.length() - 1).trim();
                                }
                            }
                            // 忽略具有Content-ID的部件，它们通常是内嵌资源
                            if (contentId != null && !contentId.isEmpty()) {
                                continue;
                            }
                            // 获取附件名称
                            String fileName = bodyPart.getFileName();
                            fileName = MimeUtility.decodeText(fileName);
                            boolean needDelete = false;
                            for (AttachedDelete attachedDelete : sendMailVo.getAttachedDeletes()) {
                                if ((fileName.equals(attachedDelete.getAttachName()) || fileName.equals(attachedDelete.getAttachName().replaceAll(" ", " "))) && String.valueOf(i).equals(attachedDelete.getAttachId())) {
                                    needDelete = true;
                                }
                            }
                            if (!needDelete) {
                                Path tempFile = Files.createTempFile(UUID.randomUUID().toString(), null);
                                try {
                                    File file = tempFile.toFile();
                                    MailBoxUtils.saveAttachment(bodyPart, file.getAbsolutePath());
                                    // 将附件添加到邮件中
                                    messageBodyPart = new MimeBodyPart();
                                    DataSource source = new FileDataSource(file);
                                    messageBodyPart.setDataHandler(new DataHandler(source));
                                    messageBodyPart.setFileName(fileName);
                                    multipart.addBodyPart(messageBodyPart);
                                } finally {
                                    // 将需要删除的路径保存
                                    needDeletePath.add(tempFile);
                                }
                            }
                        }
                    }
                }
                // 标记原邮件为删除
                originalMessage.setFlag(Flags.Flag.DELETED, true);
            }
            // 加载真实附件
            if (!sendMailVo.getAttachments().isEmpty()) {
                for (MFileMail mFileMail : sendMailVo.getAttachments()) {
                    QueryWrapper<MFileMail> mFileMailQueryWrapper = new QueryWrapper<>();
                    mFileMailQueryWrapper.eq("file_key", mFileMail.getFileKey());
                    List<MFileMail> mFileMailList = mFileMailMapper.selectList(mFileMailQueryWrapper);
                    if (!mFileMailList.isEmpty()) {
                        MFileMail mFileMail1 = mFileMailList.get(0);
                        Path tempFile = Files.createTempFile(mFileMail1.getFileGuid(), null);
                        File file = tempFile.toFile();
                        try {
                            tencentCloudService.downLoadTofile(mFileMail1, false, file);
                            // 将附件添加到邮件中
                            messageBodyPart = new MimeBodyPart();
                            DataSource source = new FileDataSource(file);
                            messageBodyPart.setDataHandler(new DataHandler(source));
                            messageBodyPart.setFileName(mFileMail1.getFileNameOrc());
                            multipart.addBodyPart(messageBodyPart);
                        } finally {
                            // 将需要删除的路径保存
                            needDeletePath.add(tempFile);
                            // 在桶中将文件删除
                            tencentCloudService.deleteObject(imageBucketName, mFileMail1.getFileKey());
                            // 在 mFile库中将数据删除
                            mFileMailMapper.deleteById(mFileMail.getId());
                        }
                    }
                }
            }
            // 将多部件消息设置为邮件内容
            message.setContent(multipart);

            // 如果已经有mailId，则认为是需要修改邮件，而不是保存附件
            if (sendMailVo.getMailId() != null && !sendMailVo.getMailId().isEmpty()) {
                LambdaQueryWrapper<MMail> mailQueryWrapper = new LambdaQueryWrapper<>();
                mailQueryWrapper.eq(MMail::getMailId, sendMailVo.getMailId());
                mailQueryWrapper.eq(MMail::getFkMailAccountId, account.getId());
                mailQueryWrapper.eq(MMail::getId, sendMailVo.getId());
                mailMapper.delete(mailQueryWrapper);
                /*// doris中同步删除
                mailDorisMapper.deleteMailDoris(sendMailVo.getId());*/
                LambdaQueryWrapper<MMailAttached> mailAttachedQueryWrapper = new LambdaQueryWrapper<>();
                mailAttachedQueryWrapper.eq(MMailAttached::getMailId, sendMailVo.getMailId());
                mailAttachedQueryWrapper.eq(MMailAttached::getFkMailAccountId, account.getId());
                // 删除附件桶里面的附件一同删除
                List<MMailAttached> mMailAttachedList = attachedMapper.selectList(mailAttachedQueryWrapper);
                for (MMailAttached mMailAttached : mMailAttachedList) {
                    LambdaQueryWrapper<SMediaAndAttachedEntity> sMediaAndAttachedEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    sMediaAndAttachedEntityLambdaQueryWrapper.eq(SMediaAndAttachedEntity::getFkTableName, "m_mail_attached");
                    sMediaAndAttachedEntityLambdaQueryWrapper.eq(SMediaAndAttachedEntity::getFkTableId, mMailAttached.getId());
                    List<SMediaAndAttachedEntity> sMediaAndAttachedEntityList = sMediaAndAttachedMapper.selectList(sMediaAndAttachedEntityLambdaQueryWrapper);
                    for (SMediaAndAttachedEntity sMediaAndAttachedEntity : sMediaAndAttachedEntityList) {
                        String guid = sMediaAndAttachedEntity.getFkFileGuid();
                        LambdaQueryWrapper<MFileMail> mFileMailQueryWrapper = new LambdaQueryWrapper<>();
                        mFileMailQueryWrapper.eq(MFileMail::getFileGuid, guid);
                        List<MFileMail> mFileMailList = mFileMailMapper.selectList(mFileMailQueryWrapper);
                        for (MFileMail mFileMail : mFileMailList) {
                            // 从桶里面删除
                            tencentCloudService.deleteObject(fileBucketName, mFileMail.getFileKey());
                            mFileMailMapper.deleteById(mFileMail.getId());
                        }
                        sMediaAndAttachedMapper.deleteById(sMediaAndAttachedEntity.getId());
                    }
                    attachedMapper.deleteById(mMailAttached.getId());
                }
                LambdaQueryWrapper<MMailSyncQueue> mailSyncQueueQueryWrapper = new LambdaQueryWrapper<>();
                mailSyncQueueQueryWrapper.eq(MMailSyncQueue::getMailId, sendMailVo.getMailId());
                mailSyncQueueQueryWrapper.eq(MMailSyncQueue::getFkMailId,sendMailVo.getId());
                mailSyncQueueQueryWrapper.eq(MMailSyncQueue::getFkMailAccountId, account.getId());
                List<MMailSyncQueue> mMailSyncQueueList = syncQueueMapper.selectList(mailSyncQueueQueryWrapper);
                for (MMailSyncQueue mMailSyncQueue : mMailSyncQueueList) {
                    // doris同步删除
                    mailSyncQueueDorisMapper.deleteById(mMailSyncQueue.getId());
                }
                syncQueueMapper.delete(mailSyncQueueQueryWrapper);
            }
            // 将邮件复制到草稿箱文件夹
            draftFold.appendMessages(new Message[]{message});
            draftFold.close(true);
            return message;
        } finally {
            for (Path path : needDeletePath) {
                log.info("删除临时文件");
                Files.delete(path);
            }
        }
    }


    @Override
    public void processMessage(Message message, String mailId, MMailAccount mMailAccount, String saveFoldName) throws Exception {
        MimeMessage mimeMessage = (MimeMessage) message;
        try {
            List<MMailAttached> mMailAttacheds = new ArrayList<>();
            List<File> files = new ArrayList<>();
            List<EmbeddedFile> embeddedFiles = new ArrayList<>();
            // 获取邮件的主题
            String subject = mimeMessage.getSubject();
            // 获取邮件内容
            Object mailContent = mimeMessage.getContent();
            // 获取邮件正文内容
            String content = "";
            if (mailContent instanceof String) {
                content = (String) mailContent;
            } else if (mailContent instanceof Multipart) {
                MimeMultipart multipart = (MimeMultipart) mailContent;
//                int partCount = multipart.getCount();
                StringBuilder contentBuilder = new StringBuilder();
                try {
                    // 处理嵌套的多部分内容
                    contentBuilder.append(processNestedMultipart(multipart));
                } catch (Exception e) {
                    log.info("处理邮件嵌套多部份出错主流程继续执行");
                }
                content = contentBuilder.toString();
                // 处理附件
                MimeMessage mimeMessage1 = (MimeMessage) message;
                Object mailContent1 = mimeMessage1.getContent();
                MimeMultipart multipart1 = (MimeMultipart) mailContent1;
                processAttachments(multipart1, files, embeddedFiles, mMailAttacheds, mMailAccount, mailId);
                // 将内嵌图片上传到桶里
                for (EmbeddedFile embeddedFile : embeddedFiles) {
                    try {
                        File file = embeddedFile.getFile();
                        MFileMail fileMail = new MFileMail();
                        // 源文件名
                        String fileNameOrc = file.getName();
                        // 获取后缀名
                        String fileTypeOrc = "";
                        int lastDotIndex = file.getName().lastIndexOf('.');
                        if (lastDotIndex != -1) {
                            if (file.getName().substring(lastDotIndex + 1).length() > 4) {
                                fileTypeOrc = file.getName().substring(file.getName().length() - 4);
                            } else {
                                fileTypeOrc = file.getName().substring(lastDotIndex + 1);
                            }
                        }
                        // 文件guid
                        String fileGuid = UUID.randomUUID().toString().replaceAll("-", "");
                        String fileurl = subString(AppendixUtils.getFileHtiPath(fileNameOrc));
                        int j = fileurl.lastIndexOf("/");
                        // 新文件名
                        String fileName = fileurl.substring(j + 1, fileurl.length());
                        fileMail.setFilePath(fileurl);
                        fileMail.setFileNameOrc(fileNameOrc);
                        fileMail.setFileTypeOrc(fileTypeOrc);
                        fileMail.setFileName(fileName);
                        fileMail.setFileGuid(fileGuid);
                        fileMail.setGmtCreate(LocalDateTime.now());
                        fileMail.setGmtCreateUser(mMailAccount.getGmtCreateUser());
                        log.info("将内嵌文件传入的桶名称是：{}", imageBucketName);
                        tencentCloudService.uploadObject(true, imageBucketName, file, fileurl);
                        fileMail.setFileKey(fileurl);
                        mFileMailMapper.insert(fileMail);
                        // 本地删除
//                            Files.delete(file.toPath());
                        String embeddedUrl = "https://" + imageBucketName + ".cos.ap-shanghai.myqcloud.com" + fileurl;
                        String embeddedContentId = "cid:" + embeddedFile.getContentId();
                        content = content.replace(embeddedContentId, embeddedUrl);
                    } catch (Exception e) {
                        log.info("将嵌套附件保存出错");
                    }
                }
            }
            // 获取发件人
            String from = ((InternetAddress) message.getFrom()[0]).getAddress();
            // 获取收件人
            StringBuilder toName = new StringBuilder();
            // 获取收件人信息
            Address[] recipients = message.getRecipients(Message.RecipientType.TO);
            if (recipients != null) {
                for (Address recipient : recipients) {
                    toName.append(((InternetAddress) recipient).getAddress()).append(",");
                }
            }
            // 如果 toName 不为空，则删除最后一个逗号
            if (toName.length() > 0) {
                toName.setLength(toName.length() - 1); // 删除最后一个字符
            }
            // 获取抄送人
            StringBuilder ccName = new StringBuilder();
            Address[] ccAddresses = message.getRecipients(Message.RecipientType.CC);
            if (ccAddresses != null) {
                for (Address address : ccAddresses) {
                    ccName.append(((InternetAddress) address).getAddress()).append(",");
                }
            }
            // 如果 ccName 不为空，则删除最后一个逗号
            if (ccName.length() > 0) {
                ccName.setLength(ccName.length() - 1); // 删除最后一个字符
            }
            // 获取密送人
            StringBuilder bccName = new StringBuilder();
            Address[] bccAddresses = message.getRecipients(Message.RecipientType.BCC);
            if (bccAddresses != null) {
                for (Address address : bccAddresses) {
                    bccName.append(((InternetAddress) address).getAddress()).append(",");
                }
            }
            // 如果密送人不为空，删除最后一个逗号
            if (bccName.length() > 0) {
                bccName.setLength(bccName.length() - 1);
            }


            boolean isRead = mimeMessage.isSet(Flags.Flag.SEEN);
            boolean isStar = mimeMessage.isSet(Flags.Flag.FLAGGED);
            MMail mail = new MMail();
            mail.setFkPlatformCode(mMailAccount.getFkPlatformCode());
            mail.setFkPlatformUserId(mMailAccount.getFkPlatformUserId());
            mail.setFkMailAccountId(mMailAccount.getId());
            mail.setMailId(mailId);
            mail.setFoldBox(saveFoldName);
            mail.setSubject(subject);
            // 根据标题区分出系统提醒邮件
            Pattern pattern = Pattern.compile("^【.*提醒】.*");
            Matcher matcher = pattern.matcher(subject);
            if (matcher.find()) {
                mail.setLlmMailType(8);
            } else {
                mail.setLlmMailType(0);
            }
            if (content.contains("https://hti-public-image-prd-**********.cos.ap-shanghai.myqcloud.com/share/newbg.png")) {
                mail.setLlmMailType(8);
            }
            mail.setBody(content);
            // 提取纯文本内容
            mail.setBodyText(Jsoup.parse(content).text());
            mail.setFromMail(from);
            mail.setToMail(toName.toString());
            mail.setCcMail(ccName.toString());
            mail.setBccMail(bccName.toString());
            mail.setSeparately(false);
            // 将 Date 转换为 LocalDateTime
            // 获取邮件日期
            Date date = mimeMessage.getReceivedDate();
            if (saveFoldName.equals("sentList")) {
                date = mimeMessage.getSentDate();
            }
            LocalDateTime localDateTime = date.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
            mail.setDate(localDateTime);
            mail.setIsRead(isRead);
            mail.setStar(isStar);
            mail.setGmtCreate(LocalDateTime.now());
            try {
                QueryWrapper<MMail> queryWrapper1 = new QueryWrapper<>();
                queryWrapper1.eq("mail_id", mail.getMailId());
                queryWrapper1.eq("fold_box", saveFoldName);
                queryWrapper1.eq("fk_mail_account_id", mMailAccount.getId());
                List<MMail> mMails = mailMapper.selectList(queryWrapper1);
                if (!mMails.isEmpty()) {
                    System.out.println("邮件已经存在了");
                }
                mailMapper.insert(mail);

                /*// 将邮件插入doris
                MMailDoris mMailDoris = new MMailDoris();
                mMailDoris.setId(mail.getId());
                mMailDoris.setFkMailAccountId(mail.getFkMailAccountId());
                mMailDoris.setStar(mail.isStar());
                mMailDoris.setDate(mail.getDate());
                // 提取纯文本内容
                mMailDoris.setBodyText(Jsoup.parse(mail.getBody()).text());
                mMailDoris.setSubject(mail.getSubject());
                mMailDoris.setFromMail(mail.getFromMail());
                mMailDoris.setLlmMailType(mail.getLlmMailType());
                mMailDoris.setFoldBox(mail.getFoldBox());
                mailDorisMapper.insertDoris(mMailDoris);*/

                for (int k = 0; k < mMailAttacheds.size(); k++) {
                    MMailAttached mailAttached = mMailAttacheds.get(k);
                    mailAttached.setFkMailId(mail.getId());
                    attachedMapper.insert(mailAttached);
                    if (mailAttached.getFileExtension() != null && mailAttached.getFileExtension().equals("docx") || mailAttached.getFileExtension().equals("doc")) {
                        // 将附件保存
                        File file = files.get(k);
                        MFileMail fileMail = new MFileMail();
                        // 源文件名
                        String fileNameOrc = mailAttached.getFileName();
                        // 获取后缀名
                        String fileTypeOrc = mailAttached.getFileExtension();
                        // 文件guid
                        String fileGuid = UUID.randomUUID().toString().replaceAll("-", "");
                        String fileurl = subString(AppendixUtils.getFileHtiPath(fileNameOrc));
                        int j = fileurl.lastIndexOf("/");
                        // 新文件名
                        String fileName = fileurl.substring(j + 1, fileurl.length());
                        fileMail.setFilePath(fileurl);
                        fileMail.setFileNameOrc(fileNameOrc);
                        fileMail.setFileTypeOrc(fileTypeOrc);
                        fileMail.setFileName(fileName);
                        fileMail.setFileGuid(fileGuid);
                        fileMail.setGmtCreate(LocalDateTime.now());
                        fileMail.setGmtCreateUser(mMailAccount.getGmtCreateUser());
                        log.info("将文件传入的桶名称是：{}", fileBucketName);
                        tencentCloudService.uploadObject(false, fileBucketName, file, fileurl);
                        fileMail.setFileKey(fileurl);
                        mFileMailMapper.insert(fileMail);
                        SMediaAndAttachedEntity sMediaAndAttachedEntity = new SMediaAndAttachedEntity();
                        sMediaAndAttachedEntity.setFkFileGuid(fileGuid);
                        sMediaAndAttachedEntity.setFkTableId(mailAttached.getId());
                        sMediaAndAttachedEntity.setFkTableName("m_mail_attached");
                        sMediaAndAttachedEntity.setGmtCreate(LocalDateTime.now());
                        sMediaAndAttachedEntity.setGmtCreateUser(mMailAccount.getGmtCreateUser());
                        sMediaAndAttachedMapper.insert(sMediaAndAttachedEntity);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.info("邮件插入数据库失败：{}{}", mail.getMailId(), e.getMessage());
            } finally {
                //删除保存在本地的内嵌资源
                log.info("{}删除内嵌资源", mMailAccount.getEmailAccount());
                for (EmbeddedFile embeddedFile : embeddedFiles) {
                    try {
                        Files.delete(embeddedFile.getFile().toPath());
                    } catch (Exception e) {
                        log.info("{}删除内嵌资源出错", mMailAccount.getEmailAccount());
                    }
                }
                //删除本地保存的附件
                log.info("{}删除附件资源", mMailAccount.getEmailAccount());
                for (File file : files) {
                    if (file != null) {
                        try {
                            Files.delete(file.toPath());
                        } catch (Exception e) {
                            e.printStackTrace();
                            log.info("{}删除附件资源出错", mMailAccount.getEmailAccount());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("拉取邮件失败{}{}", mMailAccount.getEmailAccount(), e.getMessage());
        }
    }

    @Override
    public MailDto reGetMessage(MailDto mailDto) throws Exception {
        //找到邮件的位置
        LambdaQueryWrapper<MMail> mailLambdaQueryWrapper = new LambdaQueryWrapper<>();
        mailLambdaQueryWrapper.eq(MMail::getId, mailDto.getId());
        List<MMail> mails = mailMapper.selectList(mailLambdaQueryWrapper);
        if (!mails.isEmpty()) {
            throw new GetServiceException(BAD_REQUEST, "not find mail");

        }
        MMail mMail = mails.get(0);
        LambdaQueryWrapper<MMailAccount> accountQueryWrapper = new LambdaQueryWrapper<>();
        accountQueryWrapper.eq(MMailAccount::getId, mailDto.getFkAccountId());
        List<MMailAccount> accounts = accountMapper.selectList(accountQueryWrapper);
        if (accounts.isEmpty()) {
            throw new GetServiceException(BAD_REQUEST, "mail account is not exit");
        }
        MMailAccount account = accounts.get(0);
        String host = env.getProperty("mail." + account.getEmailType());
        byte[] key = Objects.requireNonNull(env.getProperty("encrypt.KEY")).getBytes();

        MailBox mailBox = MailBoxUtils.login(account.getEmailAccount(), AesUtil.decrypt(account.getEmailPassword(), key), host);
        Folder folder = mailBox.getStore().getFolder(mMail.getFoldBox());
        folder.open(Folder.READ_ONLY);
        IMAPFolder imapFolder = (IMAPFolder) folder;
        long uid = Long.parseLong(mMail.getMailId());
        Message message = imapFolder.getMessageByUID(uid);
        if (message != null) {
            MimeMessage mimeMessage = (MimeMessage) message;
            List<File> files = new ArrayList<>();
            List<EmbeddedFile> embeddedFiles = new ArrayList<>();
            List<MMailAttached> mMailAttacheds = new ArrayList<>();
            try {
                // 获取邮件内容
                Object mailContent = mimeMessage.getContent();
                // 获取邮件正文内容
                String content = "";
                if (mailContent instanceof String) {
                    content = (String) mailContent;
                } else if (mailContent instanceof Multipart) {
                    MimeMultipart multipart = (MimeMultipart) mailContent;
//                int partCount = multipart.getCount();
                    StringBuilder contentBuilder = new StringBuilder();
                    try {
                        // 处理嵌套的多部分内容
                        contentBuilder.append(processNestedMultipart(multipart));
                    } catch (Exception e) {
                        log.info("处理邮件嵌套多部份出错主流程继续执行");
                    }
                    content = contentBuilder.toString();
                    // 处理附件
                    MimeMessage mimeMessage1 = (MimeMessage) message;
                    Object mailContent1 = mimeMessage1.getContent();
                    MimeMultipart multipart1 = (MimeMultipart) mailContent1;
                    processAttachments(multipart1, files, embeddedFiles, mMailAttacheds, account, mMail.getMailId());
                    // 将内嵌图片上传到桶里
                    for (EmbeddedFile embeddedFile : embeddedFiles) {
                        try {
                            File file = embeddedFile.getFile();
                            MFileMail fileMail = new MFileMail();
                            // 源文件名
                            String fileNameOrc = file.getName();
                            // 获取后缀名
                            String fileTypeOrc = "";
                            int lastDotIndex = file.getName().lastIndexOf('.');
                            if (lastDotIndex != -1) {
                                if (file.getName().substring(lastDotIndex + 1).length() > 4) {
                                    fileTypeOrc = file.getName().substring(file.getName().length() - 4);
                                } else {
                                    fileTypeOrc = file.getName().substring(lastDotIndex + 1);
                                }
                            }
                            // 文件guid
                            String fileGuid = UUID.randomUUID().toString().replaceAll("-", "");
                            String fileurl = subString(AppendixUtils.getFileHtiPath(fileNameOrc));
                            int j = fileurl.lastIndexOf("/");
                            // 新文件名
                            String fileName = fileurl.substring(j + 1, fileurl.length());
                            fileMail.setFilePath(fileurl);
                            fileMail.setFileNameOrc(fileNameOrc);
                            fileMail.setFileTypeOrc(fileTypeOrc);
                            fileMail.setFileName(fileName);
                            fileMail.setFileGuid(fileGuid);
                            fileMail.setGmtCreate(LocalDateTime.now());
                            fileMail.setGmtCreateUser(account.getGmtCreateUser());
                            log.info("将内嵌文件传入的桶名称是：{}", imageBucketName);
                            tencentCloudService.uploadObject(true, imageBucketName, file, fileurl);
                            fileMail.setFileKey(fileurl);
                            mFileMailMapper.insert(fileMail);
                            String embeddedUrl = "https://" + imageBucketName + ".cos.ap-shanghai.myqcloud.com" + fileurl;
                            String embeddedContentId = "cid:" + embeddedFile.getContentId();
                            content = content.replace(embeddedContentId, embeddedUrl);
                        } catch (Exception e) {
                            log.info("将嵌套附件保存出错");
                        }
                    }
                }
                mailDto.setBody(content);
                // 重新插入数据库
                mMail.setBody(content);
                mailMapper.updateById(mMail);
                return mailDto;
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                //删除保存在本地的内嵌资源
                log.info("删除内嵌资源");
                for (EmbeddedFile embeddedFile : embeddedFiles) {
                    try {
                        Files.delete(embeddedFile.getFile().toPath());
                    } catch (Exception e) {
                        log.info("删除内嵌资源出错");
                    }
                }
                for (File file : files) {
                    if (file != null) {
                        try {
                            Files.delete(file.toPath());
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
        }
        return null;
    }


    void testMail() throws Exception {
        String emailAccount = "<EMAIL>";
        String mailId = "9585";
        LambdaQueryWrapper<MMailAccount> accountQueryWrapper = new LambdaQueryWrapper<>();
        accountQueryWrapper.eq(MMailAccount::getFkPlatformUserId, 3).eq(MMailAccount::getEmailAccount, emailAccount);
        List<MMailAccount> accounts = accountMapper.selectList(accountQueryWrapper);
        if (accounts.isEmpty()) {
            throw new GetServiceException(BAD_REQUEST, "mail account is not exist");
        }
        String host = "imap.exmail.qq.com";
        MailBox mailBox = MailBoxUtils.login(emailAccount, "Huatong@365", host);
        Store store = mailBox.getStore();
        Folder folder = store.getFolder("INBOX");
        folder.open(Folder.READ_ONLY);
        IMAPFolder imapFolder = (IMAPFolder) folder;
        long uid = Long.parseLong(mailId);
        Message message = imapFolder.getMessageByUID(uid);
        processMessage(message, mailId, accounts.get(0), "inboxList");
    }
}
