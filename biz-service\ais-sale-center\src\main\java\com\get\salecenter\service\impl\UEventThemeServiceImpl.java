package com.get.salecenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.aisplatformcenterap.entity.ULabelTypeEntity;
import com.get.common.result.Page;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.entity.InstitutionProvider;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.salecenter.dao.sale.UEventThemeMapper;

import com.get.salecenter.dto.query.UEventThemeQueryDto;
import com.get.salecenter.entity.ReceivablePlan;
import com.get.salecenter.entity.UEventTheme;
import com.get.salecenter.service.UEventThemeService;
import com.get.salecenter.vo.UEventThemeVo;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
public class UEventThemeServiceImpl extends GetServiceImpl<UEventThemeMapper, UEventTheme> implements UEventThemeService {


    @Resource
    private UEventThemeMapper uEventThemeMapper;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Autowired
    private UtilService utilService;

    @Override
    public UEventThemeVo findUEventThemeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        UEventTheme uEventTheme = uEventThemeMapper.selectById(id);
        if(GeneralTool.isEmpty(uEventTheme)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //获取所有公司
        Map<Long, String> companyMap = getCompanyMap();

        UEventThemeVo uEventThemeVo = new UEventThemeVo();
        //设置公司名称
        uEventThemeVo.setCompanyName(companyMap.get(uEventTheme.getFkCompanyId()));
        BeanCopyUtils.copyProperties(uEventTheme,uEventThemeVo);
        return uEventThemeVo;
    }

    @Override
    public Long addUEventTheme(UEventThemeQueryDto uEventThemeQueryDto) {
        UEventTheme uEventTheme = new UEventTheme();
        if(GeneralTool.isEmpty(uEventThemeQueryDto)){
            LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","insert_vo_null!");
        }

        BeanCopyUtils.copyProperties(uEventThemeQueryDto,uEventTheme);
        if(check(uEventTheme)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("name_exists"));
        }
        utilService.updateUserInfoToEntity(uEventTheme);
        int count =uEventThemeMapper.insert(uEventTheme);
        if(count<=0){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return uEventTheme.getId();
    }




    @Override
    public UEventThemeVo updateUEventTheme(UEventThemeQueryDto uEventThemeQueryDto) {
        if(GeneralTool.isEmpty(uEventThemeQueryDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        UEventTheme uEventTheme = uEventThemeMapper.selectById(uEventThemeQueryDto.getId());
        if(GeneralTool.isEmpty(uEventTheme)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        LambdaQueryWrapper <UEventTheme>queryWrapper = new LambdaQueryWrapper();
        if(GeneralTool.isNotEmpty(uEventThemeQueryDto.getEventTheme())){
            uEventTheme.setEventTheme(uEventThemeQueryDto.getEventTheme());
            queryWrapper.eq(UEventTheme::getEventTheme,uEventThemeQueryDto.getEventTheme());
        }
        if(GeneralTool.isNotEmpty(uEventThemeQueryDto.getFkCompanyId())){
            uEventTheme.setFkCompanyId(uEventThemeQueryDto.getFkCompanyId());
            queryWrapper.eq(UEventTheme::getFkCompanyId,uEventThemeQueryDto.getFkCompanyId());
        }
        List<UEventTheme> uEventThemes = uEventThemeMapper.selectList(queryWrapper);

        if(GeneralTool.isNotEmpty(uEventThemes)){
            if (!uEventThemes.get(0).getId().equals(uEventThemeQueryDto.getId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("name_exists"));
            }
        }
        utilService.updateUserInfoToEntity(uEventTheme);
        uEventThemeMapper.updateById(uEventTheme);

        return findUEventThemeById(uEventTheme.getId());
    }

    @Override
    public List<UEventThemeVo> getUEventThemes(UEventThemeQueryDto data, Page page) {
        LambdaQueryWrapper <UEventTheme>queryWrapper = new LambdaQueryWrapper();
        //根据名称查询
        if (StringUtils.isNotEmpty(data.getEventTheme())) {
            queryWrapper.like(UEventTheme::getEventTheme, data.getEventTheme());
        }

        //根据公司查询
        if (GeneralTool.isNotEmpty(data.getFkCompanyId())) {
            queryWrapper.eq(UEventTheme::getFkCompanyId, data.getFkCompanyId());
        }

        queryWrapper.orderByDesc(UEventTheme::getGmtCreate);
        IPage<UEventTheme> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));

        IPage<UEventTheme> pages= uEventThemeMapper.selectPage(iPage,queryWrapper);

        List<UEventTheme> uEventThemeList = pages.getRecords();

        //获取所有公司
        Map<Long, String> companyMap = getCompanyMap();
        List<UEventThemeVo> uEventThemeVos = new ArrayList<>();
        if(GeneralTool.isNotEmpty(uEventThemeList)){
            for (UEventTheme uEventTheme:uEventThemeList){
                UEventThemeVo uEventThemeVo = new UEventThemeVo();
                //设置公司名称
                uEventThemeVo.setCompanyName(companyMap.get(uEventTheme.getFkCompanyId()));
                BeanCopyUtils.copyProperties(uEventTheme,uEventThemeVo);
                uEventThemeVos.add(uEventThemeVo);
            }
        }
        page.setAll((int) pages.getTotal());

        return uEventThemeVos;
    }

    /**
     * 获取所有公司
     * @return
     */
    private Map<Long, String> getCompanyMap() {
        Result<List<CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        List<com.get.permissioncenter.vo.tree.CompanyTreeVo> companyTreeVos = result.getData();
        //初始为5的map
        Map<Long, String> companyMap = new HashMap<>(5);
        if (GeneralTool.isNotEmpty(companyTreeVos)) {
            companyMap = companyTreeVos.stream().collect(Collectors.toMap(com.get.permissioncenter.vo.tree.CompanyTreeVo::getId, com.get.permissioncenter.vo.tree.CompanyTreeVo::getShortName));
        }
        return companyMap;
    }



    @Override
    public void deleteUEventTheme(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (findUEventThemeById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        uEventThemeMapper.deleteById(id);
    }


    public Boolean check(UEventTheme uEventTheme) {
        LambdaQueryWrapper<UEventTheme> wrapper = new LambdaQueryWrapper<>();

        if(GeneralTool.isEmpty(uEventTheme.getFkCompanyId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }

        wrapper.eq(UEventTheme::getFkCompanyId,uEventTheme.getFkCompanyId());
        wrapper.eq(UEventTheme::getEventTheme,uEventTheme.getEventTheme());

        UEventTheme uEventTheme1 = uEventThemeMapper.selectOne(wrapper);
        if(GeneralTool.isNotEmpty(uEventTheme1)){
            return true;
        }

        return false;
    }


}
