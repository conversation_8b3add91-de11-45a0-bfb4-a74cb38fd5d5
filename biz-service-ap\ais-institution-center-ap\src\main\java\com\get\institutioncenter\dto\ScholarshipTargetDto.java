package com.get.institutioncenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class ScholarshipTargetDto {

    @ApiModelProperty("目标类型")
    @NotNull(message = "目标类型不能为空")
    private String targetName;

    @ApiModelProperty("搜索值")
    private String  keyword;

    @ApiModelProperty("国家id")
    private Long countryId;

    @ApiModelProperty("学校id")
    private Long institutionId;
}
