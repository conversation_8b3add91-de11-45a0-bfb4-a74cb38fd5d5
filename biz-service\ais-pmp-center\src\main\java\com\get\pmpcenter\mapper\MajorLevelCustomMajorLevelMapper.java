package com.get.pmpcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.pmpcenter.entity.MajorLevelCustomMajorLevel;
import com.get.pmpcenter.vo.common.CurrencyTypeVo;
import com.get.pmpcenter.vo.common.MajorLevelTreeVo;
import com.get.pmpcenter.vo.common.MajorLevelVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface MajorLevelCustomMajorLevelMapper extends BaseMapper<MajorLevelCustomMajorLevel> {

    /**
     * 查询课程等级列表
     *
     * @param isGeneral
     * @return
     */
    List<MajorLevelVo> selectMajorLevel(@Param("isGeneral") Integer isGeneral);

    /**
     * PMP货币列表
     * @param type
     * @return
     */
    List<CurrencyTypeVo> getCurrencyTypes(@Param("type") String type);

}
