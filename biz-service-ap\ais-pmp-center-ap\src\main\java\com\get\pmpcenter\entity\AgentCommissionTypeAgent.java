package com.get.pmpcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("r_agent_commission_type_agent")
public class AgentCommissionTypeAgent extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "代理佣金分类Id")
    private Long fkAgentCommissionTypeId;

    @ApiModelProperty(value = "学生代理Id")
    private Long fkAgentId;
}
