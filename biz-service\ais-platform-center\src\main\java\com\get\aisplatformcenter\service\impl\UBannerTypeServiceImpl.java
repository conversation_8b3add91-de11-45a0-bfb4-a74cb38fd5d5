package com.get.aisplatformcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.aisplatformcenter.mapper.UBannerTypeMapper;
import com.get.aisplatformcenter.service.UBannerTypeService;
import com.get.aisplatformcenterap.entity.UBannerTypeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【u_banner_type】的数据库操作Service实现
* @createDate 2024-12-16 18:03:59
*/
@Service
public class UBannerTypeServiceImpl extends ServiceImpl<UBannerTypeMapper, UBannerTypeEntity>
    implements UBannerTypeService {
    @Autowired
    private UBannerTypeMapper ubannerTypeMapper;

    @Override
    public List<UBannerTypeEntity> searchList(UBannerTypeEntity dto) {
        List<UBannerTypeEntity> result = ubannerTypeMapper.selectList(
                new LambdaQueryWrapper<UBannerTypeEntity>().orderBy(true,true,UBannerTypeEntity::getViewOrder));
        return result;
    }

    @Override
    public Long delete(Long id) {

        ubannerTypeMapper.deleteById(id);
        return id;
    }


}




