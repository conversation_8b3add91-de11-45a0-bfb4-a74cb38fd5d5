package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.financecenter.dto.ReceiptMethodTypeDto;
import com.get.financecenter.vo.BaseSelectVo;
import com.get.financecenter.vo.ReceiptMethodTypeVo;
import java.util.List;
import javax.validation.Valid;

/**
 * 收款方式类型服务
 */
public interface ReceiptMethodTypeService  {

    List<ReceiptMethodTypeVo> getReceiptMethodTypes(@Valid ReceiptMethodTypeDto receiptMethodTypeDto, Page page);

    void batchAdd(List<ReceiptMethodTypeDto> receiptMethodTypeDtos);

    void updateReceiptMethodTypes(ReceiptMethodTypeDto receiptMethodTypeDto);

    void deleteReceiptMethodType(Long id);


    void sort(List<Long> ids);

    List<BaseSelectVo> getReceiptMethodTypeDropDown();

    void movingOrder(Integer start, Integer end);
}

