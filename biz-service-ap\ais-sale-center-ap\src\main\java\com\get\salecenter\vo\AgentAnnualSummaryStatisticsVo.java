package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * 代理统计报DTO
 * <AUTHOR>
 * @date 2023/3/16 15:47
 */
@Data
public class AgentAnnualSummaryStatisticsVo {

    @ApiModelProperty(value = "bdId")
    private Long bdId;

    @ApiModelProperty(value = "bd名")
    private String bdName;

    @ApiModelProperty(value = "大区id")
    private Long regionId;

    @ApiModelProperty(value = "大区名字")
    private String areaRegionName;

    @ApiModelProperty(value = "年份 - (月份-代理数统计）")
    private Map<Integer, Map<String, AgentAnnualStatisticsVo>> agentAnnualStatisticsMap;

    @ApiModelProperty(value = "年份 - 代理数统计")
    private Map<Integer, AgentAnnualStatisticsVo> agentAnnualStatisticsResultMap;

    @ApiModelProperty(value = "年份 - 代理数统计增减")
    private Map<String, AgentAnnualStatisticsVo> agentAnnualStatisticsRatioMap;

    @ApiModelProperty(value = "总计")
    private Long agentTotalNumber;

}
