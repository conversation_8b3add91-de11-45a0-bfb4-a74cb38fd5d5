package com.get.officecenter.controller;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.get.common.utils.LocaleMessageUtils;
import com.get.officecenter.service.impl.WxCpTpRedisConfigImpl;
import com.get.officecenter.utils.HttpUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.utils.GeneralTool;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxAccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.bean.WxCpTpCorp;
import me.chanjar.weixin.cp.config.WxCpTpConfigStorage;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <NAME_EMAIL> on 2020/2/24
 * 对第三方一些常用接口进行封装
 */
@Slf4j
public class BaseController {
    public static Map<String/*suitId*/, String/*永久授权码*/> permanentCodeMap = Maps.newHashMap();
    public static Map<String/*suitId*/, String/*第三方获取的企业Id*/> authCropIdMap = Maps.newHashMap();
    static {
        permanentCodeMap.put("wwf953aba73d7d8344", "FOVTfXPp16hx-iZW8765FMhTPdz9aV0iN5mPqC6gouw"); //生产用
        authCropIdMap.put("wwf953aba73d7d8344", "wpYKCwIgAAa3ZiRW7Xqdj-nH9ZvYmYMQ"); // 生产用
    }


    /**
     * 第三方用户信息url
     */
    private static String getuserinfo3rdUrl = "https://qyapi.weixin.qq.com/cgi-bin/service/getuserinfo3rd?suite_access_token=%s&code=%s";
    /**
     * 第三方用户敏感信息url
     */
    private static String getuserdetail3rdUrl = "https://qyapi.weixin.qq.com/cgi-bin/service/getuserdetail3rd?suite_access_token=%s";
    /**
     * 获取jsapiurl
     */
    private static String getJsApiTicketUrl = "https://qyapi.weixin.qq.com/cgi-bin/get_jsapi_ticket?access_token=%s";
    /**
     * 获取预授权码url
     */
    private static String getPreAuthCodeUrl = "https://qyapi.weixin.qq.com/cgi-bin/service/get_pre_auth_code?suite_access_token=%s";
    /**
     * 获取第三方应用凭证url
     */
    private static String getSuiteAccessTokenUrl = "https://qyapi.weixin.qq.com/cgi-bin/service/get_suite_token";
    /**
     * 设置授权类型url
     */
    private static String doSetAuthTypeUrl = "https://qyapi.weixin.qq.com/cgi-bin/service/set_session_info?suite_access_token=%s";
    /**
     * 获取永久授权码url
     */
    private static String getPermanentCodeUrl = "https://qyapi.weixin.qq.com/cgi-bin/service/get_permanent_code?suite_access_token=%s";
    /**
     * 获取第三获取企业凭证url
     */
    private static String getCorpTokenUrl = "https://qyapi.weixin.qq.com/cgi-bin/service/get_corp_token?suite_access_token=%s";
    /**
     * 获取企业凭证url
     */
    private static String getAccessTokenUrl = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=%s&corpsecret=%s";
    /**
     * openid换取明文userid的url
     */
    private static String getUserIdUrl = "https://qyapi.weixin.qq.com/cgi-bin/batch/openuserid_to_userid?access_token=%s";
    /**
     * 获取企业授权信息url
     */
    private static String getAuthInfoUrl = "https://qyapi.weixin.qq.com/cgi-bin/service/get_auth_info?suite_access_token=%s";
    /**
     * 获取用户信息url
     */
    private static String getUserInfoUrl = "https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo?access_token=%s&code=%s";
    /**
     * 企业内部应用url
     */
    private static String doSendMessageUrl = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=%s";
    /**
     * 用户敏感信息url
     */
    private static String getUserDetailUrl = "https://qyapi.weixin.qq.com/cgi-bin/auth/getuserdetail?access_token=%s";


//    /**
//     * 获取用户信息
//     * getSuiteAccessToken 缓存期2小时
//     * @throws Exception
//     */
//    public static JSONObject getUserInfo3rd(WxCpTpService tpService, String code) throws Exception{
//        JSONObject result = HttpUtils.sendGet(String.format(getuserinfo3rdUrl, tpService.getSuiteAccessToken(), code));
//        System.out.println(result.toString());
//        System.out.println("user_ticket：" + result.get("user_ticket"));
//        return result;
//    }


    /**
     * 获取访问用户敏感信息
     * @throws Exception
     */
    public static JSONObject getUserDetail3rd(WxCpTpService tpService, String user_ticket) throws Exception{
        JSONObject object = new JSONObject();
        object.put("user_ticket", user_ticket);
        JSONObject result = HttpUtils.sendPost(String.format(getuserdetail3rdUrl, tpService.getSuiteAccessToken()), object.toJSONString());
        System.out.println("用户详细信息："+result.toJSONString());
        return result;
    }


    /**
     * 获取企业的jsapi_ticket
     * 有效期为7200s
     * @return
     */
    public static JSONObject getJsApiTicket(String suiteAccessToken,WxCpTpService tpService,String suiteId){
        WxCpTpConfigStorage wxCpTpConfigStorage = tpService.getWxCpTpConfigStorage();

        if (wxCpTpConfigStorage instanceof WxCpTpRedisConfigImpl){
            WxCpTpRedisConfigImpl cpTpRedisConfig = (WxCpTpRedisConfigImpl) wxCpTpConfigStorage;
            String jsApiTicket = cpTpRedisConfig.getJsApiTicket(authCropIdMap.get(suiteId));
            if (StringUtils.isNoneEmpty(jsApiTicket)){
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("ticket", jsApiTicket);
                return jsonObject;
            }
            try {
                WxAccessToken wxAccessToken = getCorpToken(suiteAccessToken,authCropIdMap.get(suiteId), permanentCodeMap.get(suiteId));
                JSONObject result = innerGetJsApiTicket(wxAccessToken.getAccessToken());
                System.out.println(result.toString());
                String ticket = result.getString("ticket");
                int expires = result.getInteger("expires_in");
                // 更新到缓存
                cpTpRedisConfig.updateJsApiTicket(ticket, expires, authCropIdMap.get(suiteId));
                return result;
            }catch (Exception e){
                e.printStackTrace();
            }
        }else {
            try {
                WxAccessToken wxAccessToken = getCorpToken(suiteAccessToken,authCropIdMap.get(suiteId), permanentCodeMap.get(suiteId));
                JSONObject result = innerGetJsApiTicket(wxAccessToken.getAccessToken());
                System.out.println(result.toString());
                return result;
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        return null;
    }


    private static JSONObject innerGetJsApiTicket(String accessToken){
        try {
            JSONObject result = HttpUtils.sendGet(String.format(getJsApiTicketUrl, accessToken));
            System.out.println(result.toString());
            System.out.println("ticket：" + result.get("ticket"));
            if (GeneralTool.isEmpty(result.get("ticket"))||"null".equals(result.get("ticket"))){
                throw new GetServiceException(LocaleMessageUtils.getMessage("FAILED_TO_GET_JSAPI_TICKET"));
            }
            return result;
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }


    public static Map<String, Object> getParameter(String url) {
        Map<String, Object> map = new HashMap<>();
        try {
            final String charset = "utf-8";
            url = URLDecoder.decode(url, charset);
            if (url.indexOf('?') != -1) {
                final String contents = url.substring(url.indexOf('?') + 1);
                String[] keyValues = contents.split("&");
                for (int i = 0; i < keyValues.length; i++) {
                    String key = keyValues[i].substring(0, keyValues[i].indexOf("="));
                    String value = keyValues[i].substring(keyValues[i].indexOf("=") + 1);
                    map.put(key, value);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return map;
    }


    public static String getPreAuthCode(String suiteAccessToken){
        try {
            JSONObject result = HttpUtils.sendGet(String.format(getPreAuthCodeUrl, suiteAccessToken));
            System.out.println(result.toString());
            System.out.println("pre_auth_code：" + result.get("pre_auth_code"));
            return String.valueOf(result.get("pre_auth_code"));
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    public static String getSuiteAccessToken(String ticket,String suiteId,String suiteSecret){
        try {
            JSONObject object = new JSONObject();
            object.put("suite_id", suiteId);
            object.put("suite_secret", suiteSecret);
            object.put("suite_ticket", ticket);
            String paramStr = object.toJSONString();
            String post = HttpUtil.post(getSuiteAccessTokenUrl, paramStr);
            JSONObject result = JSON.parseObject(post);
            if (GeneralTool.isEmpty(result.get("suite_access_token"))){
                throw new GetServiceException((String)result.get("errmsg"));
            }
            System.out.println("suite_access_token：" + result.get("suite_access_token"));
            return String.valueOf(result.get("suite_access_token"));
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    public static String doSetAuthType(String suiteAccessToken,String perAuthCode){
        try {
            // TODO: 2022/9/21 发布生产改为0
            Integer authType = 0;
            String urlNew = String.format(doSetAuthTypeUrl, suiteAccessToken);
            JSONObject object = new JSONObject();
            object.put("pre_auth_code", perAuthCode);
            JSONObject sessionInfo = new JSONObject();
            sessionInfo.put("appid",new ArrayList<>());
            sessionInfo.put("auth_type",authType);
            object.put("session_info", sessionInfo);
            String paramStr = object.toJSONString();
            String post = HttpUtil.post(urlNew, paramStr);
            JSONObject result = JSON.parseObject(post);
            if (GeneralTool.isNotEmpty(result.get("errmsg"))){
               if ("ok".equals(result.get("errmsg"))){
                   log.info("设置授权成功！");
                   return String.valueOf(authType);
               }
            }
            log.info("设置授权失败！");
            throw new GetServiceException(
                    LocaleMessageUtils.getMessage("AUTHORIZATION_SETUP_FAILED"));
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }


    public static WxCpTpCorp getPermanentCode(String suiteAccessToken, String authCode){
        try {
            String urlNew = String.format(getPermanentCodeUrl, suiteAccessToken);
            JSONObject object = new JSONObject();
            object.put("auth_code", authCode);
            String paramStr = object.toJSONString();
            String post = HttpUtil.post(urlNew, paramStr);
            JSONObject result = JSON.parseObject(post);
            WxCpTpCorp wxCpTpCorp = WxCpTpCorp.fromJson(result.getJSONObject("auth_corp_info").toJSONString());
            wxCpTpCorp.setPermanentCode(result.get("permanent_code").toString());
            log.info("--------------permanent_code:"+result.get("permanent_code").toString());
            return wxCpTpCorp;
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }



    public static WxAccessToken getCorpToken(String suiteAccessToken,String authCorpId, String permanentCode) throws GetServiceException {
        try {
            String urlNew = String.format(getCorpTokenUrl, suiteAccessToken);
            JSONObject object = new JSONObject();
            object.put("auth_corpid",authCorpId);
            object.put("permanent_code",permanentCode);
            String paramStr = object.toJSONString();
            String post = HttpUtil.post(urlNew, paramStr);
            JSONObject result = JSON.parseObject(post);
            if (GeneralTool.isNotEmpty(result.get("errmsg"))){
                throw new GetServiceException(LocaleMessageUtils.getMessage("THIRD_PARTY_GET_ENTERPRISE_ACCESS_TOKEN_FAILED"));
            }
            return WxAccessToken.fromJson(result.toJSONString());
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    public static JSONObject getUserInfo3rd(String suiteAccessToken, String code) throws Exception{
        JSONObject result = HttpUtils.sendGet(String.format(getuserinfo3rdUrl, suiteAccessToken, code));
        System.out.println("user_ticket：" + result.get("user_ticket"));
        return result;
    }


    public static JSONObject getAccessToken(String corpId, String corpSecret) throws Exception{
        JSONObject result = HttpUtils.sendGet(String.format(getAccessTokenUrl, corpId, corpSecret));
        System.out.println("access_token：" + result.get("access_token"));
        return result;
    }


    public static JSONObject getUserId(String accessToken,String openUserId,Long sourceAgentid) throws Exception{
        String urlNew = String.format(getUserIdUrl, accessToken);
        List<String> openUseridList = new ArrayList<>(1);
        openUseridList.add(openUserId);
        JSONObject object = new JSONObject();
        object.put("open_userid_list", openUseridList);
        object.put("source_agentid", sourceAgentid);
        String paramStr = object.toJSONString();
        String post = HttpUtil.post(urlNew, paramStr);
        JSONObject result = JSON.parseObject(post);
        return result;
    }


    public static JSONObject getAuthInfo(String accessToken) throws Exception{
        String urlNew = String.format(getAuthInfoUrl, accessToken);
        JSONObject object = new JSONObject();
        object.put("auth_corpid", "wpYKCwIgAAa3ZiRW7Xqdj-nH9ZvYmYMQ");
        object.put("permanent_code", "FOVTfXPp16hx-iZW8765FMhTPdz9aV0iN5mPqC6gouw");
        String paramStr = object.toJSONString();
        String post = HttpUtil.post(urlNew, paramStr);
        JSONObject result = JSON.parseObject(post);
        return result;
    }


    public static JSONObject getUserInfo(String accessToken, String code) throws Exception{
        JSONObject result = HttpUtils.sendGet(String.format(getUserInfoUrl, accessToken, code));
        System.out.println("user_ticket：" + result.get("user_ticket"));
        return result;
    }

    public static JSONObject doSendMessageUrl(String accessToken, JSONObject messageBody) throws Exception{
        String urlNew = String.format(doSendMessageUrl, accessToken);
        String paramStr = messageBody.toJSONString();
        String post = HttpUtil.post(urlNew, paramStr);
        return JSON.parseObject(post);
    }

    public static JSONObject getUserDetail(String accessToken, String userTicket) throws Exception{
        String urlNew = String.format(getUserDetailUrl, accessToken);
        JSONObject object = new JSONObject();
        object.put("user_ticket", userTicket);
        String paramStr = object.toJSONString();
        String post = HttpUtil.post(urlNew, paramStr);
        return JSON.parseObject(post);
    }
}
