package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.institutioncenter.vo.InstitutionAppFeeVo;
import com.get.institutioncenter.entity.InstitutionAppFee;
import com.get.institutioncenter.dto.InstitutionAppFeeDto;
import com.get.institutioncenter.dto.WeScholarshipAppDto;
import com.get.institutioncenter.dto.query.InstitutionAppFeeQueryDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface InstitutionAppFeeMapper extends BaseMapper<InstitutionAppFee> {


    int insertSelective(InstitutionAppFee record);


    List<InstitutionAppFeeVo> getWcInstitutionAppFeeList(IPage<InstitutionAppFeeVo> ipage,
                                                         @Param("data") InstitutionAppFeeDto data);

    List<InstitutionAppFeeVo> getWcInstitutionAppFeeDatas(IPage<InstitutionAppFeeVo> ipage,
                                                          @Param("data") InstitutionAppFeeDto data);

    InstitutionAppFeeVo selectInfoById(Long id);

    List<InstitutionAppFeeVo> datas(IPage<InstitutionAppFee> ipage, @Param("data") InstitutionAppFeeQueryDto data, @Param("countryIds") List<Long> countryIds);

    /**
     *  申请费用优先匹配
     * @param weScholarshipAppDto
     * @param priorityTypeKey
     * @param fkTableName
     * @return
     */
    List<InstitutionAppFeeVo> priorityMatchingQuery(@Param("weScholarshipAppDto") WeScholarshipAppDto weScholarshipAppDto,
                                                    @Param("priorityTypeKey") Map<Integer,String> priorityTypeKey, @Param("fkTableName")String fkTableName);

    List<InstitutionAppFeeVo> selectInfoByIds(List<Long> ids);

    /**
     * 根据目标表名和ID获取申请费信息
     *
     * @param fkTableName 目标表名
     * @param fkTableId   主键ID
     * @return
     */
    List<InstitutionAppFeeVo> getAppFees(@Param("fkTableName") String fkTableName,@Param("fkTableId") Long fkTableId);
}