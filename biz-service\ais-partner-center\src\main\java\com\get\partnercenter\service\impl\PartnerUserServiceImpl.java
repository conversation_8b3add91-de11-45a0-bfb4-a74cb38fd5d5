package com.get.partnercenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.partnercenter.dto.*;
import com.get.partnercenter.dto.wechat.LinkAndQrCodeVo;
import com.get.partnercenter.dto.wechat.MinProgramQrCodeDto;
import com.get.partnercenter.entity.*;
import com.get.partnercenter.enums.MailTemplateTypeEnum;
import com.get.partnercenter.mapper.*;
import com.get.partnercenter.rocketmq.msg.UserOfflineDto;
import com.get.partnercenter.rocketmq.producer.UserOfflineProducer;
import com.get.partnercenter.service.MailLogService;
import com.get.partnercenter.service.PartnerUserService;
import com.get.partnercenter.service.WeChatService;
import com.get.partnercenter.util.PasswordGenerator;
import com.get.partnercenter.vo.*;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/2/20  11:04
 * @Version 1.0
 */
@Service
@Slf4j
public class PartnerUserServiceImpl implements PartnerUserService {

    @Autowired
    private InstitutionCenterMapper institutionCenterMapper;
    @Autowired
    private SaleCenterMapper saleCenterMapper;
    @Autowired
    private MPartnerUserMapper partnerUserMapper;
    @Autowired
    private AppSystemCenterMapper systemCenterMapper;
    private final PasswordEncoder encoder = new BCryptPasswordEncoder();
    @Autowired
    private AgentDisableMapper disableMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Autowired
    private MailTemplateMapper mailTemplateMapper;
    @Autowired
    private MailLogMapper mailLogMapper;
    @Autowired
    private MailLogService mailLogService;
    @Autowired
    private PermissionCenterMapper permissionCenterMapper;
    @Autowired
    private UserOfflineProducer userOfflineProducer;
    @Autowired
    private PartnerUserPartnerRoleMapper userPartnerRoleMapper;
    @Autowired
    private PartnerRoleMapper partnerRoleMapper;
    @Autowired
    private PartnerUserSuperiorMapper userSuperiorMapper;
    @Autowired
    private WeChatService weChatService;

    @Override
    public List<ReginVo> getRegionList() {
        return institutionCenterMapper.getRegionList();
    }

    @Override
    public List<AgentAccountVo> agentAccountPage(AgentPageDto params, Page page) {
        List<Long> staffFollowerIds = getStaffFollowerIds();
        IPage<AgentAccountVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<AgentAccountVo> list = partnerUserMapper.selectAgentAccount(pages, params, staffFollowerIds);
        for (AgentAccountVo agentAccountVo : list) {
            //管理员邮箱
//            String userEmails = systemCenterMapper.getUserEmails(agentAccountVo.getAgentId());
            List<String> adminUserEmails = systemCenterMapper.getAdminUserEmails(agentAccountVo.getAgentId());
            String emailStr = adminUserEmails.stream()
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.joining(","));
            agentAccountVo.setEmail(emailStr);
            //areaRegionName-所属大区
            if (StringUtils.isNotBlank(agentAccountVo.getAreaRegionId())) {
                AgentAccountVo regionNames = institutionCenterMapper.getRegionNames(agentAccountVo.getAreaRegionId());
                agentAccountVo.setAreaRegionName(regionNames.getAreaRegionName());
                agentAccountVo.setAreaRegionNameChn(regionNames.getAreaRegionNameChn());
            }

        }
        int totalCount = (int) pages.getTotal();
        Integer showCount = page.getShowCount();
        page.setTotalPage(page.getShowCount() != null && showCount > 0 ? totalCount % showCount == 0 ? totalCount / showCount : totalCount / showCount + 1 : 0);
        page.setTotalResult(totalCount);
        return list;
    }

    @Override
    public List<MailLogVo> mailLogPage(MailLogDto mailLogDto, Page page) {
        List<Long> staffFollowerIds = getStaffFollowerIds();
        IPage<MailLogVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<MailLogVo> list = mailLogMapper.selectMailLog(pages, mailLogDto, staffFollowerIds);
        int totalCount = (int) pages.getTotal();
        Integer showCount = page.getShowCount();
        page.setTotalPage(page.getShowCount() != null && showCount > 0 ? totalCount % showCount == 0 ? totalCount / showCount : totalCount / showCount + 1 : 0);
        page.setTotalResult(totalCount);
        return list;
    }

    @Override
    @DSTransactional
    public void saveAgentStatus(StatusDto statusDto) {
        UserInfo user = SecureUtil.getUser();
        List<MPartnerUserEntity> partnerUserList = partnerUserMapper.selectList(new LambdaQueryWrapper<MPartnerUserEntity>()
                .eq(MPartnerUserEntity::getFkAgentId, statusDto.getId()));
        if (CollectionUtils.isNotEmpty(partnerUserList)) {
            List<Long> userIds = partnerUserList.stream().map(MPartnerUserEntity::getFkUserId).collect(Collectors.toList());
            List<Long> partnerUserIds = partnerUserList.stream().map(MPartnerUserEntity::getId).collect(Collectors.toList());
            List<String> userEmails = partnerUserList.stream().map(MPartnerUserEntity::getEmail).collect(Collectors.toList());
            systemCenterMapper.batchUpdateUserStatus(userIds,
                    statusDto.getStatus().equals(1) ? 0 : 1, user.getLoginId());
            partnerUserMapper.batchUpdateStatus(partnerUserIds, statusDto.getStatus(), user.getLoginId());
            if (statusDto.getStatus().equals(0)) {
                //用户下线
                userOffline(userEmails);
            }
        }
        if (statusDto.getStatus().equals(1)) {
            disableMapper.delete(new LambdaQueryWrapper<AgentDisableEntity>().eq(AgentDisableEntity::getFkAgentId, statusDto.getId()));
        } else {
            AgentDisableEntity agentDisable = new AgentDisableEntity();
            agentDisable.setFkAgentId(statusDto.getId());
            agentDisable.setGmtCreate(new Date());
            agentDisable.setGmtCreateUser(user.getLoginId());
            disableMapper.insert(agentDisable);
        }
    }

    @Override
    public List<PartnerUserVo> partnerUserPage(IdDto idDto, Page page) {
        IPage<PartnerUserVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<PartnerUserVo> list = partnerUserMapper.selectPartnerUser(pages, idDto.getId());
        List<Long> partnerUserIds = list.stream().map(PartnerUserVo::getPartnerUserId).collect(Collectors.toList());
        //角色
        Map<Long, List<PartnerUserPartnerRole>> userRoleMap = userPartnerRoleMapper.selectList(
                        new LambdaQueryWrapper<PartnerUserPartnerRole>()
                                .in(PartnerUserPartnerRole::getFkPartnerUserId,
                                        CollectionUtils.isEmpty(partnerUserIds) ? Arrays.asList(0L) : partnerUserIds))
                .stream().collect(Collectors.groupingBy(PartnerUserPartnerRole::getFkPartnerUserId));
        Set<Long> allRoleIds = userRoleMap.values().stream()
                .flatMap(List::stream)
                .map(PartnerUserPartnerRole::getFkPartnerRoleId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Map<Long, String> roleIdNameMap = partnerRoleMapper.selectList(new LambdaQueryWrapper<PartnerRole>()
                        .in(PartnerRole::getId, CollectionUtils.isEmpty(allRoleIds) ? Arrays.asList(0L) : allRoleIds))
                .stream()
                .collect(Collectors.toMap(PartnerRole::getId, PartnerRole::getRoleName));

        //上司
        Map<Long, List<Long>> userSuperiorIdMap = userSuperiorMapper.selectList(
                        new LambdaQueryWrapper<PartnerUserSuperiorEntity>()
                                .in(PartnerUserSuperiorEntity::getFkPartnerUserId,
                                        CollectionUtils.isEmpty(partnerUserIds) ? Arrays.asList(0L) : partnerUserIds))
                .stream()
                .collect(Collectors.groupingBy(
                        PartnerUserSuperiorEntity::getFkPartnerUserId,
                        Collectors.mapping(PartnerUserSuperiorEntity::getFkPartnerUserIdSuperior, Collectors.toList())
                ));

        // 提取所有上司ID并去重
        Set<Long> allSuperiorIds = userSuperiorIdMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toSet());
        // 查询上司信息并构建 Map
        Map<Long, String> userSuperiorNameMap = partnerUserMapper.selectIdAndNameByIds(
                        CollectionUtils.isEmpty(allSuperiorIds) ? Arrays.asList(0L) : allSuperiorIds.stream().collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(PartnerUserSimpleVo::getId, PartnerUserSimpleVo::getName));

        for (PartnerUserVo partnerUserVo : list) {
            List<Long> countryIds = partnerUserMapper.selectPartnerUserCountryIds(partnerUserVo.getPartnerUserId());
            if (CollectionUtils.isNotEmpty(countryIds)) {
                String countryName = institutionCenterMapper.getCountryNameByIds(countryIds);
                partnerUserVo.setCountry(countryName);
            }
            //填充角色名称
            Long userId = partnerUserVo.getPartnerUserId();
            List<PartnerUserPartnerRole> userRoles = userRoleMap.getOrDefault(userId, Collections.emptyList());
            List<Long> roleIds = userRoles.stream()
                    .map(PartnerUserPartnerRole::getFkPartnerRoleId)
                    .collect(Collectors.toList());

            List<String> roleNames = roleIds.stream()
                    .map(roleIdNameMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            partnerUserVo.setRoleIds(roleIds);
            partnerUserVo.setRoleNames(roleNames);
            // 填充上司
            List<Long> superiorIds = userSuperiorIdMap.getOrDefault(userId, Collections.emptyList());
            List<String> superiorNames = superiorIds.stream()
                    .map(userSuperiorNameMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            partnerUserVo.setSuperiorNames(superiorNames);

        }
        int totalCount = (int) pages.getTotal();
        Integer showCount = page.getShowCount();
        page.setTotalPage(page.getShowCount() != null && showCount > 0 ? totalCount % showCount == 0 ? totalCount / showCount : totalCount / showCount + 1 : 0);
        page.setTotalResult(totalCount);
        return list;
    }

    @Override
    @DSTransactional
    public void saveAccountStatus(StatusDto statusDto) {
        UserInfo user = SecureUtil.getUser();
        MPartnerUserEntity partnerUser = partnerUserMapper.selectById(statusDto.getId());
        if (Objects.nonNull(partnerUser)) {
            systemCenterMapper.batchUpdateUserStatus(Arrays.asList(partnerUser.getFkUserId()),
                    statusDto.getStatus().equals(1) ? 0 : 1, user.getLoginId());
            partnerUserMapper.batchUpdateStatus(Arrays.asList(partnerUser.getId()), statusDto.getStatus(), user.getLoginId());
            if (statusDto.getStatus().equals(0)) {
                //用户下线
                userOffline(Arrays.asList(partnerUser.getEmail()));
            }
        }
    }

    @Override
    @DSTransactional
    public String resetPassword(IdDto idDto) {
        UserInfo user = SecureUtil.getUser();
        MPartnerUserEntity partnerUser = partnerUserMapper.selectById(idDto.getId());
        if (Objects.nonNull(partnerUser)) {
            //查询代理邮箱、名称、邮件密码
            SendEmailInfoDto agentEmailInfo = saleCenterMapper.selectEmailInfoByAgentId(partnerUser.getFkAgentId());
            if (Objects.isNull(agentEmailInfo) || StringUtils.isBlank(agentEmailInfo.getEmailPassword())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PARTNER_EMAIL_PASSWORD_EMPTY", "邮箱密码为空,请完善邮箱密码"));
            }
            //发邮件
            SendEmailDto sendEmailDto = new SendEmailDto();
            sendEmailDto.setType(2);
            SendEmailInfoDto sendEmailInfo = new SendEmailInfoDto();
            sendEmailInfo.setToUser(partnerUser.getName());
            sendEmailInfo.setToEmail(partnerUser.getEmail());
            sendEmailInfo.setAgentId(partnerUser.getFkAgentId());
            sendEmailInfo.setCompanyId(partnerUser.getFkCompanyId());
            sendEmailInfo.setFromUser(agentEmailInfo.getFromUser());
            sendEmailInfo.setFromEmail(agentEmailInfo.getFromEmail());
            sendEmailInfo.setEmailPassword(agentEmailInfo.getEmailPassword());
            sendEmailDto.setEmailInfos(Arrays.asList(sendEmailInfo));

            String newPassword = PasswordGenerator.generateNumericPassword();
            sendEmailDto.setNewPassword(newPassword);
            Boolean sentMail = sendMail(sendEmailDto);
            if (!sentMail) {
                return "邮件发送失败";
            }
            String password = encoder.encode(newPassword);
            systemCenterMapper.updateUserPassword(partnerUser.getFkUserId(), password, user.getLoginId());
            //用户下线
            userOffline(Arrays.asList(partnerUser.getEmail()));
        }
        return "";
    }

    @Override
    @DSTransactional
    public Long savePartnerUser(Long agentId, Long companyId, SavePartnerUserDto savePartnerUserDto, String password, String loginId, List<String> existEmail) {
        Optional<MPartnerUserEntity> existing = partnerUserMapper.selectList(
                new LambdaQueryWrapper<MPartnerUserEntity>()
                        .eq(MPartnerUserEntity::getEmail, savePartnerUserDto.getEmail())
                        .eq(MPartnerUserEntity::getFkAgentId, agentId)
                        .eq(MPartnerUserEntity::getFkCompanyId, companyId)).stream().findFirst();
        if (existing.isPresent()) {
            MPartnerUserEntity partnerUser = existing.get();
            log.error("创建Partner失败,用户已存在：邮箱:{},代理ID:{},分公司ID:{}", partnerUser.getEmail(), agentId, companyId);
            return 0L;
        }
        //保存系统用户信息
        if (!existEmail.contains(savePartnerUserDto.getEmail())) {
            Long systemDefaultRoleId = systemCenterMapper.getPartnerDefaultRole();
            savePartnerUserDto.setRoleId(Objects.nonNull(systemDefaultRoleId) ? systemDefaultRoleId : 0L);
            systemCenterMapper.saveUser(savePartnerUserDto);
            if (Objects.isNull(savePartnerUserDto.getUserId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PARTNER_SAVE_USER_FAILED", "保存用户失败"));
            }
            savePartnerUserDto.setPassword(encoder.encode(password));
            String num = generateUserCode(savePartnerUserDto.getPlatformId(), savePartnerUserDto.getUserId());
            systemCenterMapper.updateUserNum(savePartnerUserDto.getUserId(), num);
            systemCenterMapper.saveUserRole(savePartnerUserDto);
            systemCenterMapper.saveUserLogin(savePartnerUserDto);
        } else {
            Long systemUserId = systemCenterMapper.selectSystemUserId(savePartnerUserDto.getEmail());
            savePartnerUserDto.setUserId(systemUserId);
        }
        //保存伙伴用户信息
        //判断是否是第一个保存的
        Integer isAdmin = 0;
        Integer count = partnerUserMapper.selectCount(new LambdaQueryWrapper<MPartnerUserEntity>().eq(MPartnerUserEntity::getFkAgentId, agentId));
        if (count < 1) {
            isAdmin = 1;
        }
        MPartnerUserEntity partnerUser = MPartnerUserEntity.builder()
                .fkTenantId(2L)
                .fkUserId(savePartnerUserDto.getUserId())
                .fkUserLoginId(savePartnerUserDto.getEmail())
                .name(savePartnerUserDto.getName())
                .email(savePartnerUserDto.getEmail())
                .isIdentityChecked(true)
                .isModifiedPs(false)
                .isActive(true)
                .fkAgentId(agentId)
                .fkCompanyId(companyId)
                .isAdmin(isAdmin)
                .build();
        partnerUser.setGmtCreateUser(loginId);
        partnerUser.setGmtModifiedUser(loginId);
        partnerUser.setGmtCreate(new Date());
        partnerUser.setGmtModified(new Date());
        partnerUserMapper.insert(partnerUser);
        //保存伙伴角色-默认管理员
        Long partnerAdminRoleId = savePartnerUserDto.getPartnerRoleId();
        if (Objects.isNull(partnerAdminRoleId)) {
            partnerAdminRoleId = userPartnerRoleMapper.getPartnerAdminRoleId();
        }
        PartnerUserPartnerRole userPartnerRole = PartnerUserPartnerRole.builder()
                .fkTenantId(partnerUser.getFkTenantId())
                .fkPartnerUserId(partnerUser.getId())
                .fkPartnerRoleId(Objects.isNull(partnerAdminRoleId) ? 2L : partnerAdminRoleId)
                .build();
        userPartnerRole.setGmtCreateUser(loginId);
        userPartnerRole.setGmtModifiedUser(loginId);
        userPartnerRole.setGmtCreate(new Date());
        userPartnerRole.setGmtModified(new Date());
        userPartnerRoleMapper.insert(userPartnerRole);
        return partnerUser.getId();
    }

    @Override
    public List<AgentContactVo> getAgentContactList(Long agentId) {
        return saleCenterMapper.selectAgentContacts(agentId);
    }

    @Override
    public MailLogVo getMailLogById(Long id) {
        return mailLogMapper.selectDetail(id);
    }

    @Override
    public List<BdVo> getBdPage(BdPageDto bdPageDto, Page page) {
        List<Long> staffFollowerIds = getStaffFollowerIds();
        IPage<BdVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<BdVo> list = saleCenterMapper.selectAgentList(pages, staffFollowerIds, bdPageDto.getKeyword(), bdPageDto.getAgentName());
        int totalCount = (int) pages.getTotal();
        Integer showCount = page.getShowCount();
        page.setTotalPage(page.getShowCount() != null && showCount > 0 ? totalCount % showCount == 0 ? totalCount / showCount : totalCount / showCount + 1 : 0);
        page.setTotalResult(totalCount);
        return list;
    }

    @Override
    public List<BdVo> getAgentList() {
        List<Long> staffFollowerIds = getStaffFollowerIds();
        List<BdVo> list = saleCenterMapper.selectAgentList(null, staffFollowerIds, null, null);
        return list;
    }

    @Override
    public List<BdVo> getStaffList() {
        List<Long> staffFollowerIds = getStaffFollowerIds();
        List<BdVo> list = permissionCenterMapper.getStaffList(staffFollowerIds);
        return list;
    }

    @Override
    @DSTransactional
    public Boolean sendMail(SendEmailDto sendEmailDto) {

        MailTemplateTypeEnum mailTemplateEnum = MailTemplateTypeEnum.getEnumByType(sendEmailDto.getType());
        if (Objects.isNull(mailTemplateEnum)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PARTNER_EMAIL_TEMPLATE_TYPE_ERROR", "邮件模板类型错误"));
        }
        if (CollectionUtils.isEmpty(sendEmailDto.getEmailInfos())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PARTNER_EMAIL_INFO_EMPTY", "邮件信息不能为空"));
        }
        List<SendEmailInfoDto> emailInfos = sendEmailDto.getEmailInfos();
        emailInfos = emailInfos.stream().filter(
                sendEmail -> GeneralTool.isNotEmpty(sendEmail.getEmailPassword())).collect(Collectors.toList());
        //原逻辑是账号是否唯一,现在是一个账号可以对应多个PartnerUser
        List<String> existEmail = new ArrayList<>();
        if (sendEmailDto.getType().equals(1)) {
            List<String> emails = emailInfos.stream().map(SendEmailInfoDto::getToEmail).distinct().collect(Collectors.toList());
            existEmail = systemCenterMapper.checkSaveEmail(emails);
        }
        Boolean sendStatus = true;
        //获取小程序二维码
        MinProgramQrCodeDto qrCodeDto = MinProgramQrCodeDto
                .builder()
                .path("pages/index/index")
                .build();
        LinkAndQrCodeVo linkAndQrCode = weChatService.getLinkAndQrCode(null, qrCodeDto);
        for (SendEmailInfoDto sendEmail : emailInfos) {
            //默认是邀请邮件
            String mailTemplateCode = MailTemplateTypeEnum.INVITE_TO_REGISTER.getCode();
            if (sendEmailDto.getType().equals(1) && existEmail.contains(sendEmail.getToEmail())) {
                //如果该邮箱已存在，说明只是新增伙伴用户，用别的邮件模板发送
                mailTemplateCode = MailTemplateTypeEnum.REGISTER_PARTNER_USER.getCode();
            }
            if (sendEmailDto.getType().equals(2)) {
                mailTemplateCode = MailTemplateTypeEnum.RESET_PASSWORD.getCode();
            }
            MailTemplateEntity template = mailTemplateMapper.selectOne(new LambdaQueryWrapper<MailTemplateEntity>()
                    .eq(MailTemplateEntity::getTypeKey, mailTemplateCode));
            if (Objects.isNull(template)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PARTNER_EMAIL_TEMPLATE_NOT_EXIST", "邮件模板不存在"));
            }
            //全新用户,新增系统用户和伙伴用户
            String password = PasswordGenerator.generateNumericPassword();
            if (sendEmailDto.getType().equals(2)) {
                password = sendEmailDto.getNewPassword();
            }
            // 替换账号和密码
            Map replacements = new HashMap();
            replacements.put("account", sendEmail.getToEmail());
            replacements.put("qrCode", StringUtils.isNotBlank(linkAndQrCode.getQrCode()) ? linkAndQrCode.getQrCode() : "");
            replacements.put("shortLink", StringUtils.isNotBlank(linkAndQrCode.getUrl()) ? linkAndQrCode.getUrl() : "");
            if (mailTemplateCode.equals(MailTemplateTypeEnum.INVITE_TO_REGISTER.getCode())
                    || mailTemplateCode.equals(MailTemplateTypeEnum.RESET_PASSWORD.getCode())) {
                replacements.put("password", password);
            }
            if (mailTemplateCode.equals(MailTemplateTypeEnum.REGISTER_PARTNER_USER.getCode())) {
                replacements.put("name", sendEmail.getToUser());
            }
            String content = replaceTemplate(template.getEmailTemplate(), replacements);
            //发邮件
            String result = mailLogService.sendEmail(sendEmail, template.getTitle(), content);
            //邮件记录
            MailLogEntity logEntity = MailLogEntity.builder()
                    .optType(sendEmailDto.getType())
                    .fromUser(sendEmail.getFromUser())
                    .toUser(sendEmail.getToUser())
                    .fromEmail(sendEmail.getFromEmail())
                    .toEmail(sendEmail.getToEmail())
                    .sendDate(new Date())
                    .subject(template.getTitle())
                    .fkAgentId(sendEmail.getAgentId())
                    .sendStatus(StringUtils.isNotBlank(result) ? 0 : 1)
                    .sendMessage(result)
//                    .body(content)
                    .build();
            logEntity.setGmtCreate(new Date());
            logEntity.setGmtCreateUser(SecureUtil.getLoginId());
            mailLogMapper.insert(logEntity);
            if (StringUtils.isNotBlank(result)) {
                log.error("邮件发送失败,邮件参数:{}", JSONObject.toJSONString(sendEmailDto));
                log.error("邮件发送失败,失败原因:{}", result);
                sendStatus = false;
            }
            if (sendEmailDto.getType().equals(1) && logEntity.getSendStatus().equals(1)) {
                SavePartnerUserDto partner = SavePartnerUserDto.builder()
                        .platformCode("PARTNER")
                        .platformId(2L)
                        .tenantId(2L)
                        .name(sendEmail.getToUser())
                        .email(sendEmail.getToEmail())
                        .createUser(sendEmail.getFromEmail()).build();
                savePartnerUser(sendEmail.getAgentId(), sendEmail.getCompanyId(), partner, password, SecureUtil.getLoginId(), existEmail);
            }
        }
        return sendStatus;
    }

    @Override
    public void userOffline(List<String> userEmails) {
        Long partnerPlatFormId = systemCenterMapper.getPartnerPlatFormId();
        if (Objects.isNull(partnerPlatFormId)) {
            log.error("获取华通伙伴平台ID失败");
            return;
        }
        UserOfflineDto userOfflineDto = new UserOfflineDto(userEmails, partnerPlatFormId);
        userOfflineProducer.sendUserOfflineMessage(userOfflineDto);
    }

    @Override
    public void clearUserCache(List<Long> partnerUserIds) {
        log.info("开始清理用户缓存，伙伴用户ID列表: {}", partnerUserIds);

        if (CollectionUtils.isEmpty(partnerUserIds)) {
            log.warn("伙伴用户ID列表为空，跳过处理");
            return;
        }

        // 1. 批量查询伙伴用户信息
        List<MPartnerUserEntity> partnerUsers = partnerUserMapper.selectBatchIds(partnerUserIds);
        if (CollectionUtils.isEmpty(partnerUsers)) {
            log.error("未找到任何伙伴用户，伙伴用户ID列表: {}", partnerUserIds);
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PARTNER_USER_NOT_FOUND", "未找到任何伙伴用户"));
        }
        
        // 2. 提取有效的用户登录ID
        List<String> loginIds = partnerUsers.stream()
                .filter(user -> StringUtils.isNotBlank(user.getFkUserLoginId()))
                .map(MPartnerUserEntity::getFkUserLoginId)
                .collect(Collectors.toList());
        
        if (CollectionUtils.isEmpty(loginIds)) {
            log.error("没有找到有效的用户登录ID，无法清理缓存");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PARTNER_USER_LOGIN_ID_EMPTY", "没有找到有效的用户登录ID"));
        }
        
        // 3. 获取华通伙伴平台ID（与现有userOffline方法保持一致）
        Long partnerPlatFormId = systemCenterMapper.getPartnerPlatFormId();
        if (Objects.isNull(partnerPlatFormId)) {
            log.error("获取华通伙伴平台ID失败");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PARTNER_PLATFORM_ID_NOT_FOUND", "获取华通伙伴平台ID失败"));
        }
        
        // 4. 发送用户下线MQ消息来清理缓存
        UserOfflineDto userOfflineDto = new UserOfflineDto(loginIds, partnerPlatFormId);
        log.info("发送用户缓存清理消息，platformId: {}, loginIds: {}", partnerPlatFormId, loginIds);
        userOfflineProducer.sendUserOfflineMessage(userOfflineDto);
        
        log.info("用户缓存清理消息发送成功，处理用户数: {}", loginIds.size());
    }

    @Override
    @DSTransactional
    public List<RegisterPartnerUserVo> registerPartnerUser(List<RegisterPartnerUserDto> list) {
        List<RegisterPartnerUserVo> result = new ArrayList<>();
        //校验账号是否已经添加过
        list = filterNonExistingUsers(list);
        if (CollectionUtils.isEmpty(list)) {
            log.info("注册失败,账号已存在或者数据为空:{}", JSONObject.toJSONString(list));
            return result;
        }
        List<String> existEmail;
        List<String> emails = list.stream().map(RegisterPartnerUserDto::getToEmail).distinct().collect(Collectors.toList());
        existEmail = systemCenterMapper.checkSaveEmail(emails);
        for (RegisterPartnerUserDto registerPartnerUserDto : list) {
            String password = PasswordGenerator.generateNumericPassword();
            SavePartnerUserDto partner = SavePartnerUserDto.builder()
                    .platformCode("PARTNER")
                    .platformId(2L)
                    .tenantId(2L)
                    .name(registerPartnerUserDto.getToUser())
                    .email(registerPartnerUserDto.getToEmail())
                    .partnerRoleId(registerPartnerUserDto.getPartnerRoleId())
                    .createUser(registerPartnerUserDto.getFromEmail()).build();
            Long partnerUserId = savePartnerUser(registerPartnerUserDto.getAgentId(), registerPartnerUserDto.getCompanyId(), partner, password, registerPartnerUserDto.getLoginId(), existEmail);
            if (Objects.nonNull(partnerUserId) && partnerUserId > 0 && !existEmail.contains(registerPartnerUserDto.getToEmail())) {
                RegisterPartnerUserVo newPartnerUser = RegisterPartnerUserVo.builder()
                        .account(partner.getEmail())
                        .password(password).build();
                result.add(newPartnerUser);
            }
        }
        return result;
    }


    public List<RegisterPartnerUserDto> filterNonExistingUsers(List<RegisterPartnerUserDto> inputList) {
        // 构造有效 key：email|agentId|companyId
        Set<String> inputKeys = inputList.stream()
                .map(dto -> buildKey(dto.getToEmail(), dto.getAgentId(), dto.getCompanyId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (inputKeys.isEmpty()) {
            return Collections.emptyList();
        }
        // 查询已存在的 partnerUser
        List<MPartnerUserEntity> existingUsers = partnerUserMapper.selectList(
                new LambdaQueryWrapper<MPartnerUserEntity>()
                        .in(MPartnerUserEntity::getEmail,
                                inputList.stream().map(RegisterPartnerUserDto::getToEmail)
                                        .filter(StringUtils::isNotBlank)
                                        .collect(Collectors.toSet())));
        // 构造数据库中已有记录的 key 集合
        Set<String> existingKeys = existingUsers.stream()
                .map(user -> buildKey(user.getEmail(), user.getFkAgentId(), user.getFkCompanyId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 返回未存在的用户
        return inputList.stream()
                .filter(dto -> {
                    String key = buildKey(dto.getToEmail(), dto.getAgentId(), dto.getCompanyId());
                    return key != null && !existingKeys.contains(key);
                }).collect(Collectors.toList());
    }

    /**
     * 构建 key
     *
     * @param email
     * @param agentId
     * @param companyId
     * @return
     */
    private String buildKey(String email, Long agentId, Long companyId) {
        if (StringUtils.isBlank(email) || Objects.isNull(agentId) || Objects.isNull(companyId)) {
            return null;
        }
        return email + "|" + agentId + "|" + companyId;
    }


    /**
     * 模板动态内容替换
     *
     * @param template
     * @param replacements
     * @return
     */
    private String replaceTemplate(String template, Map<String, String> replacements) {
        for (Map.Entry<String, String> entry : replacements.entrySet()) {
            template = template.replace("#{" + entry.getKey() + "}", entry.getValue());
        }
        return template;
    }

    /**
     * 生成用户编号
     *
     * @param platformId
     * @param userId
     * @return
     */
    public static String generateUserCode(Long platformId, Long userId) {
        // 格式化平台ID为三位，不足补0
        String formattedPlatformId = String.format("%03d", platformId);
        // 格式化用户ID为八位，不足补0
        String formattedUserId = String.format("%08d", userId);
        // 拼接生成用户编号
        return formattedPlatformId + formattedUserId;
    }

    /**
     * 获取业务下属员工ids
     *
     * @return
     */
    private List<Long> getStaffFollowerIds() {
        //获取业务下属
        Long staffId = SecureUtil.getStaffId();
        //员工id + 业务下属员工ids
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData();
        staffFollowerIds.add(staffId);
        return staffFollowerIds;
    }
}
