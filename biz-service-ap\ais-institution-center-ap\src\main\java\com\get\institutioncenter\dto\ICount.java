package com.get.institutioncenter.dto;

import lombok.Data;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * @author: Hardy
 * @create: 2021/6/10 14:45
 * @verison: 1.0
 * @description:
 */
@Data
public class ICount {

    AtomicInteger sumCount = new AtomicInteger(0);
    AtomicInteger completedCount = new AtomicInteger(0);
    AtomicInteger errorCount = new AtomicInteger(0);

    ExceptionInner exception = new ExceptionInner();

    class ExceptionInner {
        Throwable exception;
        String message;

        public Throwable getException() {
            return exception;
        }

        public void setException(Throwable exception) {
            this.exception = exception;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }
}
