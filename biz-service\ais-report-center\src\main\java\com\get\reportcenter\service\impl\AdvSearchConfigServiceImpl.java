package com.get.reportcenter.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.QueryParamterEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.reportcenter.dao.report.AdvSearchConfigMapper;
import com.get.reportcenter.dao.report.AdvSearchRunMapper;
import com.get.reportcenter.vo.AdvSearchConfigVo;
import com.get.reportcenter.vo.StudentVo;
import com.get.reportcenter.entity.AdvSearchConfig;
import com.get.reportcenter.entity.AdvSearchRun;
import com.get.reportcenter.service.IAdvSearchConfigService;
import com.get.reportcenter.dto.AdvSearchRunDto;
import com.get.reportcenter.dto.StudentDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @DATE: 2021/3/4
 * @TIME: 18:24
 * @Description:
 **/
@Service
public class AdvSearchConfigServiceImpl implements IAdvSearchConfigService {
    @Resource
    private AdvSearchConfigMapper advSearchConfigMapper;
    @Resource
    private AdvSearchRunMapper advSearchRunMapper;
    @Resource
    private UtilService utilService;

    @Override
    public AdvSearchConfigVo getAdvSearchConfigByQueryKey(String queryKey) {
        LambdaQueryWrapper<AdvSearchConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(queryKey)) {
            lambdaQueryWrapper.eq(AdvSearchConfig::getQueryKey, queryKey);
        }
        List<AdvSearchConfig> advSearchConfigs = advSearchConfigMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(advSearchConfigs)) {
            AdvSearchConfigVo advSearchConfigVo = BeanCopyUtils.objClone(advSearchConfigs.get(0), AdvSearchConfigVo::new);
//            JSONArray jsonArray= JSONArray.parseArray(advSearchConfigVo.getQueryParamter());
//            List<QueryParamterEntity> dataArr = JSONArray.parseArray(jsonArray, new Class[]{QueryParamterEntity.class});
            List<QueryParamterEntity> queryParamterEntities =
                    JSONUtil.toList(JSONUtil.parseArray(advSearchConfigVo.getQueryParamter()), QueryParamterEntity.class);
            advSearchConfigVo.setQueryParamterEntitys(queryParamterEntities);
            return advSearchConfigVo;
        }
        return null;
    }

    @Override
    public List<LinkedHashMap<String, String>> getDatasByAdvSearchRunVo(List<AdvSearchRunDto> advSearchRunDtos, Page page) {
        if (GeneralTool.isEmpty(advSearchRunDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        List<LinkedHashMap<String, String>> map = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        if (GeneralTool.isNotEmpty(advSearchRunDtos)) {
            String uuid = UUID.randomUUID().toString().replaceAll("-", "");
            for (int i = 0; i < advSearchRunDtos.size(); i++) {
                AdvSearchRun advSearchRun = BeanCopyUtils.objClone(advSearchRunDtos.get(i), AdvSearchRun::new);
                if (GeneralTool.isEmpty(advSearchRun.getQueryRunGuid())) {
                    advSearchRun.setQueryRunGuid(uuid);
                }
                /*advSearchRun.setQueryRunGuid(UUID.randomUUID().toString().replaceAll("-", ""));*/
                utilService.updateUserInfoToEntity(advSearchRun);
                advSearchRunMapper.insertSelective(advSearchRun);
                if ("AND".equals(advSearchRun.getParamCondition())) {
                    if (i - 1 >= 0) {
                        if ("OR".equals(advSearchRunDtos.get(i - 1).getParamCondition())) {
                            sb.append(")");
                        }
                    }
                    sb.append(advSearchRunDtos.get(i).getParamCondition()).append(" ");
                    if (i + 1 < advSearchRunDtos.size()) {
                        if ("OR".equals(advSearchRunDtos.get(i + 1).getParamCondition())) {
                            sb.append("( ");
                        }
                    }
                } else {
                    sb.append(advSearchRunDtos.get(i).getParamCondition()).append(" ");
                }
                sb.append(advSearchRun.getParamName()).append(" ")
                        .append(advSearchRun.getParamOperation()).append(" ");
                if ("like".equals(advSearchRun.getParamOperation())) {
                    String operation = "'%" + advSearchRun.getParamValue() + "%'";
                    sb.append(operation);
                } else {
                    if ("STRING".equals(advSearchRun.getParamType())) {
                        if (GeneralTool.isNotEmpty(advSearchRun.getParamValue())) {
                            sb.append("'").append(advSearchRun.getParamValue()).append("'");
                        } else {
                            sb.append(advSearchRun.getParamValue());
                        }
                    } else {
                        sb.append(advSearchRun.getParamValue());
                    }
                }
                if ((i + 1 == advSearchRunDtos.size()) && ("OR".equals(advSearchRunDtos.get(i).getParamCondition()))) {
                    sb.append(")");
                }
            }
//            Example example = new Example(AdvSearchConfig.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andEqualTo("queryKey", advSearchRunDtos.get(0).getQueryKey());
            List<AdvSearchConfig> advSearchConfigs = advSearchConfigMapper.selectList(Wrappers.<AdvSearchConfig>lambdaQuery().eq(AdvSearchConfig::getQueryKey, advSearchRunDtos.get(0).getQueryKey()));
            if (GeneralTool.isNotEmpty(advSearchConfigs)) {
                String query = advSearchConfigs.get(0).getQuerySql().replace("{0}", sb.toString());
                //获取分页数据
//                PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
                IPage<LinkedHashMap<String, String>> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
                map = advSearchConfigMapper.getQueryObject(iPage, query);
//                PageInfo<LinkedHashMap<String, String>> pageInfo = new PageInfo<LinkedHashMap<String, String>>(map);
                page.setAll((int) iPage.getTotal());
//                page.setTotalResult(new Long(pageInfo.getTotal()).intValue());
            }
        }

        return map;
    }

    @Override
    public List<LinkedHashMap<String, String>> getStudentDatasByAdvSearchRunVo(List<AdvSearchRunDto> advSearchRunDtos, Page page) {
        if (GeneralTool.isEmpty(advSearchRunDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        List<LinkedHashMap<String, String>> map = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        if (GeneralTool.isNotEmpty(advSearchRunDtos)) {
            String uuid = UUID.randomUUID().toString().replaceAll("-", "");
            for (int i = 0; i < advSearchRunDtos.size(); i++) {
                AdvSearchRun advSearchRun = BeanCopyUtils.objClone(advSearchRunDtos.get(i), AdvSearchRun::new);
                if (GeneralTool.isEmpty(advSearchRun.getQueryRunGuid())) {
                    advSearchRun.setQueryRunGuid(uuid);
                }
                /*advSearchRun.setQueryRunGuid(UUID.randomUUID().toString().replaceAll("-", ""));*/
                utilService.updateUserInfoToEntity(advSearchRun);
                advSearchRunMapper.insertSelective(advSearchRun);
                if ("AND".equals(advSearchRun.getParamCondition())) {
                    if (i - 1 >= 0) {
                        if ("OR".equals(advSearchRunDtos.get(i - 1).getParamCondition())) {
                            sb.append(")");
                        }
                    }
                    sb.append(advSearchRunDtos.get(i).getParamCondition()).append(" ");
                    if (i + 1 < advSearchRunDtos.size()) {
                        if ("OR".equals(advSearchRunDtos.get(i + 1).getParamCondition())) {
                            sb.append("( ");
                        }
                    }
                } else {
                    sb.append(advSearchRunDtos.get(i).getParamCondition()).append(" ");
                }
                sb.append(advSearchRun.getParamName()).append(" ")
                        .append(advSearchRun.getParamOperation()).append(" ");
                if ("like".equals(advSearchRun.getParamOperation())) {
                    String operation = "'%" + advSearchRun.getParamValue() + "%'";
                    sb.append(operation);
                } else {
                    if ("STRING".equals(advSearchRun.getParamType())) {
                        if (GeneralTool.isNotEmpty(advSearchRun.getParamValue())) {
                            sb.append("'").append(advSearchRun.getParamValue()).append("'");
                        } else {
                            sb.append(advSearchRun.getParamValue());
                        }
                    } else {
                        sb.append(advSearchRun.getParamValue());
                    }
                }
                if ((i + 1 == advSearchRunDtos.size()) && ("OR".equals(advSearchRunDtos.get(i).getParamCondition()))) {
                    sb.append(")");
                }
            }
            List<AdvSearchConfig> advSearchConfigs = advSearchConfigMapper.selectList(Wrappers.<AdvSearchConfig>query().lambda()
                    .eq(AdvSearchConfig::getQueryKey, advSearchRunDtos.get(0).getQueryKey()));
            if (GeneralTool.isNotEmpty(advSearchConfigs)) {
                String query = advSearchConfigs.get(0).getQuerySql().replace("{0}", sb.toString());

                //获取分页数据
                IPage<LinkedHashMap<String, String>> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
                map = advSearchConfigMapper.getQueryObject(pages, query);
                page.setAll((int) pages.getTotal());
            }
        }
        return map;
    }

    @Override
    public void exportEventExcel(HttpServletResponse response, List<AdvSearchRunDto> advSearchRunDtos) {
        if (GeneralTool.isEmpty(advSearchRunDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        List<LinkedHashMap<String, String>> map = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < advSearchRunDtos.size(); i++) {
            AdvSearchRun advSearchRun = BeanCopyUtils.objClone(advSearchRunDtos.get(i), AdvSearchRun::new);
            if ("AND".equals(advSearchRun.getParamCondition())) {
                if (i - 1 >= 0) {
                    if ("OR".equals(advSearchRunDtos.get(i - 1).getParamCondition())) {
                        sb.append(")");
                    }
                }
                sb.append(advSearchRunDtos.get(i).getParamCondition()).append(" ");
                if (i + 1 < advSearchRunDtos.size()) {
                    if ("OR".equals(advSearchRunDtos.get(i + 1).getParamCondition())) {
                        sb.append("( ");
                    }
                }
            } else {
                sb.append(advSearchRunDtos.get(i).getParamCondition()).append(" ");
            }
            sb.append(advSearchRun.getParamName()).append(" ")
                    .append(advSearchRun.getParamOperation()).append(" ");
            if ("like".equals(advSearchRun.getParamOperation())) {
                String operation = "'%" + advSearchRun.getParamValue() + "%'";
                sb.append(operation);
            } else {
                sb.append(advSearchRun.getParamValue());
            }
            if ((i + 1 == advSearchRunDtos.size()) && ("OR".equals(advSearchRunDtos.get(i).getParamCondition()))) {
                sb.append(")");
            }
        }

//        Example example = new Example(AdvSearchConfig.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("queryKey", advSearchRunDtos.get(0).getQueryKey());
        List<AdvSearchConfig> advSearchConfigs = advSearchConfigMapper.selectList(Wrappers.<AdvSearchConfig>lambdaQuery().eq(AdvSearchConfig::getQueryKey, advSearchRunDtos.get(0).getQueryKey()));
        if (GeneralTool.isNotEmpty(advSearchConfigs)) {
            String query = advSearchConfigs.get(0).getQuerySql().replace("{0}", sb.toString());
            map = advSearchConfigMapper.getQueryObject(null, query);
        }
        FileUtils.exportExcel(response, map, "高级搜索结果");
    }

    @Override
    public List<StudentVo> getStudents(StudentDto studentDto, Page page) {
        return null;
    }


    private String getQueryWhere(String query) {
      /*  if ("O".equals(query.substring(0, 1))) {
            query = query.substring(3, query.length());
        } else if ("A".equals(query.substring(0, 1))) {
            query = query.substring(4, query.length());
        }*/
        /*query = "((" + query + "))";*/
        return query;
    }
}
