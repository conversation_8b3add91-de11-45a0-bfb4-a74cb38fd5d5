package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.institutioncenter.dto.AreaStateDto;
import com.get.institutioncenter.entity.AreaState;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/7/27 12:21
 * @verison: 1.0
 * @description: 区域管理-州省配置mapper
 */
@Mapper
public interface AreaStateMapper extends BaseMapper<AreaState> {
    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(AreaState record);

    /**
     * @return java.lang.String
     * @Description :通过州省id 查找对应的州省名称
     * @Param [id]
     * <AUTHOR>
     */
    String getStateNameById(Long id);

    /**
     * @return java.lang.String
     * @Description :通过州省id 查找对应的州省全称
     * @Param [id]
     * <AUTHOR>
     */
    String getStateFullNameById(Long id);

    /**
     * @return java.lang.Integer
     * @Description :获取最大排序值
     * @Param []
     * <AUTHOR>
     */
    Integer getMaxViewOrder();

    /**
     * @return list
     * @Description :获取州省列表
     * @Param areaCityVo
     * <AUTHOR>
     */
    List<AreaState> selectAreaStateByVo(IPage<AreaState> pages,@Param("areaStateDto") AreaStateDto areaStateDto);

    /**
     * @return list
     * @Description :获取州省列表
     * @Param areaCityVo
     * <AUTHOR>
     */
    List<AreaState> selectAreaStateByVo(@Param("areaStateDto") AreaStateDto areaStateDto);

    /**
     * 根据省份IDs获取国家IDs
     * @param fkAreaStateIds
     * @return
     */
    List<AreaState> getCountryIdsByStateIds(Set<Long> fkAreaStateIds);

    /**
     * 获取对应国家、公司下 有申请计划的代理所在的 州省下拉框数据
     *
     * @Date 18:53 2023/1/5
     * <AUTHOR>
     */
    List<BaseSelectEntity> getExistsAgentOfferItemAreaStateList(@Param("companyId") Long companyId,
                                                                @Param("countryId") Long countryId,
                                                                @Param("countryIds") List<Long> countryIds);

    /**
     * 获取对应国家、公司下 的代理所在的 州省下拉框数据
     *
     * @Date 11:13 2023/3/15
     * <AUTHOR>
     */
    List<BaseSelectEntity> getExistsAgentAreaStateList(@Param("companyId") Long companyId,
                                                       @Param("countryId") Long countryId);
}