package com.get.examcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON>.
 * Time: 10:57
 * Date: 2021/8/25
 * Description:答题记录Vo
 */
@Data
public class UserExaminationQuestionScoreDto extends BaseVoEntity implements Serializable {

    /**
     * 操作guid
     */
    @ApiModelProperty(value = "操作guid")
    private String optGuid;

    /**
     * 注册中心的user id
     */
    @ApiModelProperty(value = "注册中心的user id")
    private Long fkUserId;

    /**
     * 考卷Id
     */
    @ApiModelProperty(value = "考卷Id")
    private Long fkExaminationPaperId;

    /**
     * 问题Id
     */
    @ApiModelProperty(value = "问题Id")
    private Long fkExaminationQuestionId;

    /**
     * 答案Ids
     */
    @ApiModelProperty(value = "答案Ids")
    private String fkExaminationAnswerIds;

    /**
     * 获得分数
     */
    @ApiModelProperty(value = "获得分数")
    private Integer score;

    /**
     * 用时（秒）
     */
    @ApiModelProperty(value = "用时（秒）")
    private Integer useTime;

}
