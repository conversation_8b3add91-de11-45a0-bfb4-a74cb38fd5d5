package com.get.permissioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.dao.PermissionGroupGradeResourceMapper;
import com.get.permissioncenter.dao.PermissionGroupGradeStaffMapper;
import com.get.permissioncenter.dao.PermissionGroupMapper;
import com.get.permissioncenter.dto.PermissionGroupDto;
import com.get.permissioncenter.vo.PermissionGroupVo;
import com.get.permissioncenter.entity.PermissionGroup;
import com.get.permissioncenter.entity.PermissionGroupGradeResource;
import com.get.permissioncenter.entity.PermissionGroupGradeStaff;
import com.get.permissioncenter.service.ICompanyService;
import com.get.permissioncenter.service.IPermissionGroupService;
import com.get.permissioncenter.utils.MyStringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: jack
 * @create: 2020/7/6
 * @verison: 1.0
 * @description: 权限组别业务实现类
 */
@Service
public class PermissionGroupServiceImpl extends BaseServiceImpl<PermissionGroupMapper, PermissionGroup> implements IPermissionGroupService {
    @Resource
    PermissionGroupMapper permissionGroupMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private ICompanyService companyService;
    @Resource
    private PermissionGroupGradeResourceMapper permissionGroupGradeResourceMapper;
    @Resource
    private PermissionGroupGradeStaffMapper permissionGroupGradeStaffMapper;

    @Override
    public PermissionGroupVo findPermissionGroupById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
//        PermissionGroup permissionGroup = permissionGroupMapper.selectByPrimaryKey(id);
        PermissionGroup permissionGroup = permissionGroupMapper.selectById(id);
        if (GeneralTool.isEmpty(permissionGroup)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        PermissionGroupVo permissionGroupVo = BeanCopyUtils.objClone(permissionGroup, PermissionGroupVo::new);
        permissionGroupVo.setCompanyName(companyService.getCompanyNameById(permissionGroupVo.getFkCompanyId()));
        return permissionGroupVo;
    }

    @Override
    public List<PermissionGroupVo> getPermissionGroups(PermissionGroupDto permissionGroupDto, Page page) {
//        Example example = new Example(PermissionGroup.class);
//        if (GeneralTool.isEmpty(permissionGroupDto)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
//        }
//        if (GeneralTool.isEmpty(permissionGroupDto.getFkCompanyId())) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("company_id_null"));
//        }
//        Long companyId = permissionGroupDto.getFkCompanyId();
//        Example.Criteria criteria = example.createCriteria();
//        Example.Criteria criteria1 = example.createCriteria();
//        if (GeneralTool.isNotEmpty(permissionGroupDto)) {
//            if (GeneralTool.isNotEmpty(permissionGroupDto.getKeyword())) {
//                criteria.andLike("groupNum", "%" + permissionGroupDto.getKeyword() + "%");
//                criteria1.andLike("groupName", "%" + permissionGroupDto.getKeyword() + "%");
//            }
//            if (GeneralTool.isNotEmpty(permissionGroupDto.getFkCompanyId())) {
//                criteria.andEqualTo("fkCompanyId", companyId);
//                criteria1.andEqualTo("fkCompanyId", companyId);
//            }
//            example.or(criteria1);
//        }
//        example.orderBy("viewOrder").desc();
//        //获取分页数据
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<PermissionGroup> permissionGroups = permissionGroupMapper.selectByExample(example);
//        PageInfo<PermissionGroup> pageInfo = new PageInfo<PermissionGroup>(permissionGroups);
//        page.setTotalResult(new Long(pageInfo.getTotal()).intValue());

        LambdaQueryWrapper<PermissionGroup> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isEmpty(permissionGroupDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(permissionGroupDto.getFkCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_id_null"));
        }
        Long companyId = permissionGroupDto.getFkCompanyId();
        if (GeneralTool.isNotEmpty(permissionGroupDto)) {
            if (GeneralTool.isNotEmpty(permissionGroupDto.getKeyword())) {
                wrapper.and(wrapper_ ->
                        wrapper_.like(PermissionGroup::getGroupNum, permissionGroupDto.getKeyword()).or()
                                .like(PermissionGroup::getGroupName, permissionGroupDto.getKeyword()));
            }
            if (GeneralTool.isNotEmpty(permissionGroupDto.getFkCompanyId())) {
                wrapper.eq(PermissionGroup::getFkCompanyId, companyId);//111111原来是2个条件，现在换一个需要测试
            }
        }
        wrapper.orderByDesc(PermissionGroup::getViewOrder);

        IPage<PermissionGroup> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<PermissionGroup> permissionGroups = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<PermissionGroupVo> convertDatas = new ArrayList<>();
        for (PermissionGroup permissionGrade : permissionGroups) {
            PermissionGroupVo permissionGroupVo = BeanCopyUtils.objClone(permissionGrade, PermissionGroupVo::new);
            permissionGroupVo.setCompanyName(companyService.getCompanyNameById(permissionGroupVo.getFkCompanyId()));
            convertDatas.add(permissionGroupVo);
        }
        return convertDatas;
    }

    @Override
    public PermissionGroupVo updatePermissionGroup(PermissionGroupDto permissionGroupDto) {
        if (permissionGroupDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
//        PermissionGroup pg = selectByKey(permissionGroupDto.getId());
        PermissionGroup pg = this.getById(permissionGroupDto.getId());
        if (pg == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
//        PermissionGroup permissionGroup = Tools.objClone(permissionGroupDto, PermissionGroup.class);
        PermissionGroup permissionGroup = BeanCopyUtils.objClone(permissionGroupDto, PermissionGroup::new);
//        utilService.setUpdateInfo(permissionGroup);
//        permissionGroupMapper.updateByPrimaryKeySelective(permissionGroup);
        utilService.updateUserInfoToEntity(permissionGroup);
        permissionGroupMapper.updateById(permissionGroup);
        return findPermissionGroupById(permissionGroup.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addPermissionGroup(PermissionGroupDto permissionGroupDto) {
//        PermissionGroup permissionGroup = Tools.objClone(permissionGroupDto, PermissionGroup.class);
        PermissionGroup permissionGroup = BeanCopyUtils.objClone(permissionGroupDto, PermissionGroup::new);
        permissionGroup.setViewOrder(permissionGroupMapper.getMaxViewOrder());
//        utilService.setCreateInfo(permissionGroup);
        utilService.updateUserInfoToEntity(permissionGroup);
        permissionGroupMapper.insert(permissionGroup);
        permissionGroup.setGroupNum(MyStringUtils.getGroupNum(permissionGroup.getId()));
//        permissionGroupMapper.updateByPrimaryKeySelective(permissionGroup);
        permissionGroupMapper.updateById(permissionGroup);
        return permissionGroup.getId();
    }

    @Override
    public void delete(Long id) {
        //TODO 改过
        //PermissionGroup permissionGroup = findPermissionGroupById(id);
        PermissionGroupVo permissionGroup = findPermissionGroupById(id);
        if (permissionGroup == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
//        Example example = new Example(PermissionGroupGradeResource.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkPermissionGroupId", id);
//        List<PermissionGroupGradeResource> list = this.permissionGroupGradeResourceMapper.selectByExample(example);
//        if (GeneralTool.isNotEmpty(list)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("group_resource_data_association"));
//        }
//        Example example1 = new Example(PermissionGroupGradeStaff.class);
//        Example.Criteria criteria1 = example1.createCriteria();
//        criteria1.andEqualTo("fkPermissionGroupId", id);
//        List<PermissionGroupGradeStaff> list1 = this.permissionGroupGradeStaffMapper.selectByExample(example1);

        List<PermissionGroupGradeResource> list = this.permissionGroupGradeResourceMapper.selectList(Wrappers.<PermissionGroupGradeResource>query().lambda().eq(PermissionGroupGradeResource::getFkPermissionGroupId, id));
        if (GeneralTool.isNotEmpty(list)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("group_resource_data_association"));
        }
        List<PermissionGroupGradeStaff> list1 = this.permissionGroupGradeStaffMapper.selectList(Wrappers.<PermissionGroupGradeStaff>query().lambda().eq(PermissionGroupGradeStaff::getFkPermissionGroupId, id));
        if (GeneralTool.isNotEmpty(list1)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("group_staff_data_association"));
        }
//        permissionGroupMapper.delete(permissionGroup);
        permissionGroupMapper.deleteById(id);
    }

    //    @Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = YException.class)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<PermissionGroupDto> permissionGroupDtos) {
        for (PermissionGroupDto permissionGroupDto : permissionGroupDtos) {
            if (GeneralTool.isEmpty(permissionGroupDto.getId())) {
//                PermissionGroup permissionGroup = Tools.objClone(permissionGroupDto, PermissionGroup.class);
                PermissionGroup permissionGroup = BeanCopyUtils.objClone(permissionGroupDto, PermissionGroup::new);
                permissionGroup.setViewOrder(permissionGroupMapper.getMaxViewOrder());
                utilService.updateUserInfoToEntity(permissionGroup);
                permissionGroupMapper.insert(permissionGroup);
                permissionGroup.setGroupNum(MyStringUtils.getGroupNum(permissionGroup.getId()));
//                permissionGroupMapper.updateByPrimaryKeySelective(permissionGroup);
                permissionGroupMapper.updateById(permissionGroup);
            } else {
//                PermissionGroup permissionGroup = Tools.objClone(permissionGroupDto, PermissionGroup.class);
//                utilService.setUpdateInfo(permissionGroup);
//                permissionGroupMapper.updateByPrimaryKeySelective(permissionGroup);
                PermissionGroup permissionGroup = BeanCopyUtils.objClone(permissionGroupDto, PermissionGroup::new);
                utilService.updateUserInfoToEntity(permissionGroup);
                permissionGroupMapper.updateById(permissionGroup);
            }

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<PermissionGroupDto> permissionGroupDtos) {
        if (GeneralTool.isEmpty(permissionGroupDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        PermissionGroup ro = BeanCopyUtils.objClone(permissionGroupDtos.get(0), PermissionGroup::new);
        Integer oneorder = ro.getViewOrder();
        PermissionGroup rt = BeanCopyUtils.objClone(permissionGroupDtos.get(1), PermissionGroup::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
//        permissionGroupMapper.updateByPrimaryKeySelective(ro);
//        permissionGroupMapper.updateByPrimaryKeySelective(rt);
        permissionGroupMapper.updateById(ro);
        permissionGroupMapper.updateById(rt);
    }

    private boolean validateUpdate(PermissionGroupDto permissionGroupDto) {
//        Example example = new Example(PermissionGroup.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("groupName", permissionGroupDto.getGroupName());
//        List<PermissionGroup> list = this.permissionGroupMapper.selectByExample(example);
        List<PermissionGroup> list = this.permissionGroupMapper.selectList(Wrappers.<PermissionGroup>query().lambda().eq(PermissionGroup::getGroupName, permissionGroupDto.getGroupName()));
        return list.size() <= 0 || list.get(0).getId().equals(permissionGroupDto.getId());
    }

    private boolean validateAdd(PermissionGroupDto permissionGroupDto) {
//        Example example = new Example(PermissionGroup.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("groupName", permissionGroupDto.getGroupName());
//        List<PermissionGroup> list = this.permissionGroupMapper.selectByExample(example);
        List<PermissionGroup> list = this.permissionGroupMapper.selectList(Wrappers.<PermissionGroup>query().lambda().eq(PermissionGroup::getGroupName, permissionGroupDto.getGroupName()));
        return GeneralTool.isEmpty(list);
    }

    private boolean validateAdd(List<PermissionGroupDto> permissionGroupDtos) {
        boolean success = true;
        for (PermissionGroupDto permissionGroupDto : permissionGroupDtos) {
//            Example example = new Example(PermissionGroup.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andEqualTo("groupName", permissionGroupDto.getGroupName());
//            List<PermissionGroup> list = this.permissionGroupMapper.selectByExample(example);
            List<PermissionGroup> list = this.permissionGroupMapper.selectList(Wrappers.<PermissionGroup>query().lambda().eq(PermissionGroup::getGroupName, permissionGroupDto.getGroupName()));

            if (!GeneralTool.isEmpty(list)) {
                success = false;
            }
        }
        return success;
    }
}
