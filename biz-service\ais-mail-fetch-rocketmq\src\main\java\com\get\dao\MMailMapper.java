package com.get.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.get.entity.MMail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DS("mail")
@Mapper
public interface MMailMapper extends BaseMapper<MMail> {
    List<String> selectDistinctDates();
    List<Long> selectIdsByDateNotInList(@Param("date") String date, @Param("excludeIds") List<Long> excludeIds);
}
