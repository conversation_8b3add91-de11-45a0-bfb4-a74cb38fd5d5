package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Hardy
 * @create: 2021/7/1 18:00
 * @verison: 1.0
 * @description:
 */
@Data
public class SendMailDto {

    /**
     * 发送人名
     */
    @ApiModelProperty(value = "发送人名")
    String name;
    /**
     * 回执码
     */
    @ApiModelProperty(value = "回执码")
    String receiptCode;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    String title;
    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    String email;
    /**
     * 抄送邮箱
     */
    @ApiModelProperty(value = "抄送邮箱")
    String ccEmail;
}
