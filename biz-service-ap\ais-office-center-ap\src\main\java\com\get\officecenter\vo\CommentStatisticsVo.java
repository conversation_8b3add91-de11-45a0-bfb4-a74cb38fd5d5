package com.get.officecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class CommentStatisticsVo {

    @ApiModelProperty(value = "接收人id")
    private Long staffId;

    @ApiModelProperty(value = "接收人名称")
    private String staffName;

    private List<CommentVo> comments;

    @ApiModelProperty(value = "待解决")
    private Integer pending;

    @ApiModelProperty(value = "待反馈")
    private Integer feedback;

    @ApiModelProperty(value = "总数")
    private Integer total;
}
