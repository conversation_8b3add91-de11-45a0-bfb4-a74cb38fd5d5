package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.financecenter.dto.ServiceFeePaymentFormDto;
import com.get.financecenter.dto.ServiceFeeReceiptFormDto;
import com.get.salecenter.dto.CommentDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.ServiceFeeAPDto;
import com.get.salecenter.dto.ServiceFeeARAPDto;
import com.get.salecenter.dto.StudentFeeCommissionStatementExportDto;
import com.get.salecenter.dto.StudentServiceFeeDto;
import com.get.salecenter.dto.StudentServiceFeeSummaryDto;
import com.get.salecenter.entity.StudentServiceFee;
import com.get.salecenter.vo.CommentVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.vo.StudentServiceFeeSummaryVo;
import com.get.salecenter.vo.StudentServiceFeeVo;
import java.util.List;
import java.util.Set;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
public interface StudentServiceFeeService extends IService<StudentServiceFee> {

    /**
     * 学生服务费列表
     *
     * @param studentServiceFeeDto
     * @param page
     * @return
     */
    ListResponseBo<StudentServiceFeeVo> datas(StudentServiceFeeDto studentServiceFeeDto, Page page);


    /**
     * 详情
     *
     * @param id
     * @return
     */
    StudentServiceFeeVo findServiceFeeById(Long id);


    /**
     * 获取留学服务费
     *
     * @param ids
     * @return
     */
    List<StudentServiceFeeVo> getServiceFeeByIds(List<Long> ids);

    /**
     * 新增学生服务费信息
     *
     * @param studentServiceFeeDto
     * @return
     */
    SaveResponseBo save(StudentServiceFeeDto studentServiceFeeDto);


    /**
     * 编辑
     *
     * @param studentServiceFeeDto
     * @return
     */
    ResponseBo<StudentServiceFeeVo> update(StudentServiceFeeDto studentServiceFeeDto);


    /**
     * 一键作废
     *
     * @param id
     * @param status
     * @return
     */
    SaveResponseBo cancel(Long id, Integer status);

    /**
     * 作废应收应付
     *
     * @param serviceFeeId 服务费Id
     * @return
     */
    UpdateResponseBo cancelReceivablePayable(Long serviceFeeId);


    /**
     * 获取附件列表
     *
     * @param attachedVo
     * @param page
     * @return
     */
    ResponseBo<MediaAndAttachedVo> getAttacheFileMedia(MediaAndAttachedDto attachedVo, Page page);


    /**
     * 上传附件
     *
     * @param mediaAttachedVo
     * @return
     */
    List<MediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * 编辑备注
     *
     * @param commentDto
     * @return
     */
    SaveResponseBo editComment(CommentDto commentDto);

    /**
     * 获取评论备注
     *
     * @param commentDto
     * @param page
     * @return
     */
    ResponseBo<CommentVo> getComments(CommentDto commentDto, Page page);

    /**
     * 获取目标对象id
     *
     * @param targetName
     * @return
     */
    List<Long> getServiceFeeStudentIds(String targetName);


    /**
     * 获取服务费列表的学生
     *
     * @param companyId
     * @return
     */
    List<BaseSelectEntity> getInvoiceStudentSelection(Long companyId);


    /**
     * 留学服务费应收应付汇总
     *
     * @param studentServiceFeeSummaryDto
     * @param page
     * @return
     */
    ResponseBo<StudentServiceFeeSummaryVo> serviceFeeSummary(StudentServiceFeeSummaryDto studentServiceFeeSummaryDto, Page page);


    /**
     * 创建应收应付
     *
     * @param serviceFeeARAPDto
     * @return
     */
    SaveResponseBo createARAP(ServiceFeeARAPDto serviceFeeARAPDto);

    /**
     * 批量创建应付
     *
     * @param serviceFeeAPDto 应付参数
     * @return
     */
    SaveResponseBo createAP(ServiceFeeAPDto serviceFeeAPDto);

    /**
     * 收款单获取学生（客户）类型的应收计划
     *
     * @param targetId
     * @param receiptFormId
     * @param pageNumber
     * @param pageSize
     * @return
     */
    List<BaseSelectEntity> getStudentServiceFeeReceivablePlan(Long targetId, Long receiptFormId, Integer pageNumber, Integer pageSize);

    /**
     * 获取留学服务费的学生ids
     *
     * @param ids
     * @return
     */
    List<Long> getServiceFeeStudentIdsByIds(List<Long> ids);

    /**
     * 获取留学服务费的学生id
     *
     * @param targetId
     * @return
     */
    Long getServiceFeeStudentIdsById(Long targetId);

    /**
     * 合并服务费数据
     *
     * @param mergedStudentId
     * @param targetStudentId
     */
    void mergeData(Long mergedStudentId, Long targetStudentId);

    /**
     * 学生服务费汇总导出Excel
     *
     * @param response
     * @param studentServiceFeeSummaryDto
     */
    void exportStudentServiceFeeSummary(HttpServletResponse response, StudentServiceFeeSummaryDto studentServiceFeeSummaryDto);

    /**
     * 导出佣金提成结算表
     *
     * @param response
     * @param studentFeeCommissionStatementExportVo
     */
    void exportFeeCommissionStatement(HttpServletResponse response, StudentFeeCommissionStatementExportDto studentFeeCommissionStatementExportVo);

    /**
     * 部门下拉
     *
     * @param companyId
     * @return
     */
    List<BaseSelectEntity> getDepartmentSelect(Long companyId);

    /**
     * 批量创建实收，未创建应收计划时，会同时创建应收计划及收款单；已创建应收计划时，创建收款单
     *
     * @param serviceFeeReceiptFormDto
     * @return
     */

    ResponseBo batchCreateReceiptForm(ServiceFeeReceiptFormDto serviceFeeReceiptFormDto);

    /**
     * 批量创建实付，未创建应付计划时，会同时创建应付计划及付款单；已创建应付计划时，创建付款单
     *
     * @param serviceFeePaymentFormDto
     * @return
     */
    String batchCreatePaymentForm(ServiceFeePaymentFormDto serviceFeePaymentFormDto);

    /**
     * 取消已完成的业务状态
     *
     * @param serviceFeeId 服务费Id
     * @return
     */
    SaveResponseBo cancelCompletedBusinessStatus(Long serviceFeeId);

    /**
     * 批量完成业务状态
     *
     * @param serviceFeeIds 服务费Ids
     * @return
     */
    ResponseBo batchCompleteBusinessStatus(Set<Long> serviceFeeIds);

    /**
     * 取消已完成的结算审批
     *
     * @param serviceFeeId 服务费Ids
     * @return
     */
    SaveResponseBo cancelCompletedSettlementApproval(Long serviceFeeId);

    /**
     * 批量完成结算审批
     *
     * @param serviceFeeIds 服务费Ids
     * @return
     */
    ResponseBo batchCompleteSettlementApproval(Set<Long> serviceFeeIds);

    /**
     * 根据服务费id获取提供商id
     *
     * @param feeIds
     * @return
     */
    List<Long> getServiceFeeProviderIdsByFeeIds(List<Long> feeIds);

    ResponseBo batchCompleteSettlementStatus(Set<Long> serviceFeeIds);

    void createServiceFeePdf(Long id, HttpServletResponse response);


    StudentServiceFeeSummaryVo getServiceFeeInfoById(Long id);
}
