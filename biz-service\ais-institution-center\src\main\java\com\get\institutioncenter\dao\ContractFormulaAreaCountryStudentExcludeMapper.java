package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.ContractFormulaAreaCountryStudentExclude;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ContractFormulaAreaCountryStudentExcludeMapper extends BaseMapper<ContractFormulaAreaCountryStudentExclude> {
    int insertSelective(ContractFormulaAreaCountryStudentExclude record);

    List<Long> getCountryIdNotInListByFkid(@Param("contractFormulaId") Long contractFormulaId);
}