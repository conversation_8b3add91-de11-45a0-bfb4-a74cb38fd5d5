package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2020/12/16
 * @TIME: 12:42
 * @Description:
 **/
@Data
public class TranslationMappingDto extends BaseVoEntity {
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    @NotBlank(message = "表名不能为空", groups = {Add.class, Update.class})
    private String fkTableName;

    /**
     * 字段名
     */
    @ApiModelProperty(value = "字段名")
    private String fkColumnName;

    /**
     * 字段标题名
     */
    @ApiModelProperty(value = "字段标题名")
    private String inputTitle;

    /**
     * 输入类型：0单行/1多行/2富文本
     */
    @ApiModelProperty(value = "输入类型：0单行/1多行/2富文本")
    private Integer inputType;

    /**
     * 最大字符限制数
     */
    @ApiModelProperty(value = "最大字符限制数")
    private Integer maxLength;

    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    private Integer viewOrder;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

}
