package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.AgentContractAgentAccount;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/6/28 16:06
 */
@Data
public class AgentContractAgentAccountVo extends BaseEntity implements Serializable {

    //========实体类AgentContractAgentAccount=============
    private static final long serialVersionUID = 1L;
    /**
     * 学生代理合同Id
     */
    @ApiModelProperty(value = "学生代理合同Id")
    @Column(name = "fk_agent_contract_id")
    private Long fkAgentContractId;
    /**
     * 学生代理合同账户Id
     */
    @ApiModelProperty(value = "学生代理合同账户Id")
    @Column(name = "fk_agent_contract_account_id")
    private Long fkAgentContractAccountId;
}
