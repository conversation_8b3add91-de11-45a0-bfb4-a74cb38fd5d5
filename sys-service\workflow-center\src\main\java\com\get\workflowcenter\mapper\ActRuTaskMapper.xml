<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.workflowcenter.mapper.ActRuTaskMapper">
    <resultMap id="BaseResultMap" type="com.get.workflowcenter.entity.ActRuTask">
        <id column="ID_" jdbcType="VARCHAR" property="id"/>
        <result column="REV_" jdbcType="INTEGER" property="rev"/>
        <result column="EXECUTION_ID_" jdbcType="VARCHAR" property="executionId"/>
        <result column="PROC_INST_ID_" jdbcType="VARCHAR" property="procInstId"/>
        <result column="PROC_DEF_ID_" jdbcType="VARCHAR" property="procDefId"/>
        <result column="NAME_" jdbcType="VARCHAR" property="name"/>
        <result column="PARENT_TASK_ID_" jdbcType="VARCHAR" property="parentTaskId"/>
        <result column="DESCRIPTION_" jdbcType="VARCHAR" property="description"/>
        <result column="TASK_DEF_KEY_" jdbcType="VARCHAR" property="taskDefKey"/>
        <result column="OWNER_" jdbcType="VARCHAR" property="owner"/>
        <result column="ASSIGNEE_" jdbcType="VARCHAR" property="assignee"/>
        <result column="DELEGATION_" jdbcType="VARCHAR" property="delegation"/>
        <result column="PRIORITY_" jdbcType="INTEGER" property="priority"/>
        <result column="CREATE_TIME_" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="DUE_DATE_" jdbcType="TIMESTAMP" property="dueDate"/>
        <result column="CATEGORY_" jdbcType="VARCHAR" property="category"/>
        <result column="SUSPENSION_STATE_" jdbcType="INTEGER" property="suspensionState"/>
        <result column="TENANT_ID_" jdbcType="VARCHAR" property="tenantId"/>
        <result column="FORM_KEY_" jdbcType="VARCHAR" property="formKey"/>
        <result column="CLAIM_TIME_" jdbcType="TIMESTAMP" property="claimTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    ID_, REV_, EXECUTION_ID_, PROC_INST_ID_, PROC_DEF_ID_, NAME_, PARENT_TASK_ID_, DESCRIPTION_,
    TASK_DEF_KEY_, OWNER_, ASSIGNEE_, DELEGATION_, PRIORITY_, CREATE_TIME_, DUE_DATE_,
    CATEGORY_, SUSPENSION_STATE_, TENANT_ID_, FORM_KEY_, CLAIM_TIME_
  </sql>


    <insert id="insertSelective" parameterType="com.get.workflowcenter.entity.ActRuTask" keyProperty="id"
            useGeneratedKeys="true">
        insert into ACT_RU_TASK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID_,
            </if>
            <if test="rev != null">
                REV_,
            </if>
            <if test="executionId != null">
                EXECUTION_ID_,
            </if>
            <if test="procInstId != null">
                PROC_INST_ID_,
            </if>
            <if test="procDefId != null">
                PROC_DEF_ID_,
            </if>
            <if test="name != null">
                NAME_,
            </if>
            <if test="parentTaskId != null">
                PARENT_TASK_ID_,
            </if>
            <if test="description != null">
                DESCRIPTION_,
            </if>
            <if test="taskDefKey != null">
                TASK_DEF_KEY_,
            </if>
            <if test="owner != null">
                OWNER_,
            </if>
            <if test="assignee != null">
                ASSIGNEE_,
            </if>
            <if test="delegation != null">
                DELEGATION_,
            </if>
            <if test="priority != null">
                PRIORITY_,
            </if>
            <if test="createTime != null">
                CREATE_TIME_,
            </if>
            <if test="dueDate != null">
                DUE_DATE_,
            </if>
            <if test="category != null">
                CATEGORY_,
            </if>
            <if test="suspensionState != null">
                SUSPENSION_STATE_,
            </if>
            <if test="tenantId != null">
                TENANT_ID_,
            </if>
            <if test="formKey != null">
                FORM_KEY_,
            </if>
            <if test="claimTime != null">
                CLAIM_TIME_,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="rev != null">
                #{rev,jdbcType=INTEGER},
            </if>
            <if test="executionId != null">
                #{executionId,jdbcType=VARCHAR},
            </if>
            <if test="procInstId != null">
                #{procInstId,jdbcType=VARCHAR},
            </if>
            <if test="procDefId != null">
                #{procDefId,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="parentTaskId != null">
                #{parentTaskId,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="taskDefKey != null">
                #{taskDefKey,jdbcType=VARCHAR},
            </if>
            <if test="owner != null">
                #{owner,jdbcType=VARCHAR},
            </if>
            <if test="assignee != null">
                #{assignee,jdbcType=VARCHAR},
            </if>
            <if test="delegation != null">
                #{delegation,jdbcType=VARCHAR},
            </if>
            <if test="priority != null">
                #{priority,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dueDate != null">
                #{dueDate,jdbcType=TIMESTAMP},
            </if>
            <if test="category != null">
                #{category,jdbcType=VARCHAR},
            </if>
            <if test="suspensionState != null">
                #{suspensionState,jdbcType=INTEGER},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="formKey != null">
                #{formKey,jdbcType=VARCHAR},
            </if>
            <if test="claimTime != null">
                #{claimTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <select id="getToDoSelectList" resultType="com.get.workflowcenter.vo.ActRuTaskVo">
        select arp.VERSION_ as procdefVersion,ahp.START_TIME_ as procdefStartDate, ahp.ID_ as procInstId,art.NAME_ as
        taskName, art.ID_ ,arp.KEY_ as procdefKey ,arp.DEPLOYMENT_ID_ as deployId ,arp.NAME_ as procdefName,
        ahp.BUSINESS_KEY_ as businessKey, art.REV_ as taskVersion ,ahp.START_USER_ID_ as fkStaffName,ard.CATEGORY_ from
        ACT_RU_TASK
        art
        left join ACT_RE_PROCDEF arp on arp.ID_ =art.PROC_DEF_ID_
        left join ACT_RE_DEPLOYMENT ard on ard.ID_ =arp.DEPLOYMENT_ID_
        left join act_hi_procinst ahp on ahp.ID_=art.PROC_INST_ID_
        where 1=1
        <if test="category!=null and category!=''">
            and ard.CATEGORY_=#{category}
        </if>
        <if test="name!=null and name!=''">
            and arp.NAME_ like #{name}
        </if>
        ORDER BY art.CREATE_TIME_ DESC
    </select>

    <select id="getToDoSelectListByNormalUser" resultType="com.get.workflowcenter.vo.ActRuTaskVo">
        select arp.VERSION_ as procdefVersion,ahp.START_TIME_ as procdefStartDate, ahp.ID_ as procInstId,art.NAME_ as
        taskName, art.ID_ ,arp.KEY_ as procdefKey ,arp.DEPLOYMENT_ID_ as deployId ,arp.NAME_ as procdefName,
        ahp.BUSINESS_KEY_ as businessKey, art.REV_ as taskVersion ,ahp.START_USER_ID_ as fkStaffName,ard.CATEGORY_ from
        ACT_RU_TASK
        art
        left join ACT_RE_PROCDEF arp on arp.ID_ =art.PROC_DEF_ID_
        left join ACT_RE_DEPLOYMENT ard on ard.ID_ =arp.DEPLOYMENT_ID_
        left join act_hi_procinst ahp on ahp.ID_=art.PROC_INST_ID_
        where 1=1
        <if test="category!=null and category!=''">
            and ard.CATEGORY_=#{category}
        </if>
        <if test="name!=null and name!=''">
            and arp.name_ like #{name}
        </if>
        <if test="staffIdByNames!=null and staffIdByNames.size>0">
            and ahp.START_USER_ID_ in
            <foreach collection="staffIdByNames" item="staffIdByName" index="index" open="(" separator="," close=")">
                #{staffIdByName}
            </foreach>
        </if>
        and art.ASSIGNEE_=#{assignee}

        ORDER BY art.CREATE_TIME_ DESC
    </select>

    <select id="getSignedList" resultType="com.get.workflowcenter.vo.ActRuTaskVo">
        select DISTINCT art.id_ , arp.name_ ,art.name_ as task_name, arp.version_ as procdefVersion
        ,arp.DEPLOYMENT_ID_ as deployId ,arp.KEY_ as procdefKey,art.PROC_INST_ID_,ari.user_id_,art.rev_ as taskVersion
        ,ahp.START_USER_ID_ as fkStaffName, ahp.START_TIME_ as procdefStartDate from ACT_RU_TASK art
        left join ACT_RU_IDENTITYLINK ari on ari.TASK_ID_ =art.ID_
        left join ACT_RE_PROCDEF arp on arp.ID_=art.PROC_DEF_ID_
        left join ACT_RE_DEPLOYMENT ard on ard.ID_=arp.DEPLOYMENT_ID_
        left join ACT_HI_PROCINST ahp on ahp.ID_ = art.PROC_INST_ID_
        where ari.TYPE_='candidate'
        <if test="category!=null and category!=''">
            and ard.CATEGORY_=#{category}
        </if>
        <if test="name!=null and name!=''">
            and arp.NAME_=#{name}
        </if>

    </select>

    <select id="getSignedListByNormalUser" resultType="com.get.workflowcenter.vo.ActRuTaskVo">
        select DISTINCT art.id_ , arp.name_ ,art.name_ as task_name, arp.version_ as procdefVersion
        ,arp.DEPLOYMENT_ID_ as deployId ,arp.KEY_ as procdefKey,art.PROC_INST_ID_,ari.user_id_,art.rev_ as taskVersion
        ,ahp.START_USER_ID_ as fkStaffName, ahp.START_TIME_ as procdefStartDate from ACT_RU_TASK art
        left join ACT_RU_IDENTITYLINK ari on ari.TASK_ID_ =art.ID_
        left join ACT_RE_PROCDEF arp on arp.ID_=art.PROC_DEF_ID_
        left join ACT_RE_DEPLOYMENT ard on ard.ID_=arp.DEPLOYMENT_ID_
        left join ACT_HI_PROCINST ahp on ahp.ID_ = art.PROC_INST_ID_
        where ari.TYPE_='candidate'
        <if test="category!=null and category!=''">
            and ard.CATEGORY_=#{category}
        </if>
        <if test="name!=null and name!=''">
            and arp.NAME_=#{name}
        </if>
        <if test="staffIdByNames!=null and staffIdByNames.size>0">
            and ahp.START_USER_ID_ in
            <foreach collection="staffIdByNames" item="staffIdByName" index="index" open="(" separator="," close=")">
                #{staffIdByName}
            </foreach>
        </if>
        and user_id_=#{userid} AND art.`ASSIGNEE_` IS NULL
        order by ahp.START_TIME_ desc
    </select>

    <select id="getTaskVersionByProcessInId" resultType="com.get.workflowcenter.vo.ActRuTaskVo">
        SELECT * FROM `ACT_RU_TASK` where
        PROC_INST_ID_=#{pinId};
    </select>

    <select id="comparisonVersion" resultType="com.get.workflowcenter.vo.ActRuTaskVo">
        SELECT * FROM `ACT_RU_TASK` where ID_=#{taskId}  and REV_=#{version};
</select>

    <select id="getVersionById" resultType="java.lang.Integer">
        select REV_ from ACT_RU_TASK where ID_=#{taskId}
    </select>

    <select id="getApprovalRecordsList" resultType="com.get.workflowcenter.vo.ApprovalRecordListVo">
    SELECT * FROM (
    <if test="approvalRecordDto.approvalType == null or approvalRecordDto.approvalType == '' or approvalRecordDto.approvalType == 'm_leave_application_form'">
        SELECT
            mlaf.id,
            mlaf.fk_company_id,
            'm_leave_application_form' AS businessKey,
            mlaf.num,
            CONCAT(ulaft.type_name, ' - ', mlaf.days , '小时') AS title,
            CASE

                WHEN IFNULL(ms.name_en, '') = '' THEN
                    ms.`name` ELSE CONCAT(ms.`name`, '（', ms.name_en, '）')
                END AS applyName,
            md.name AS departmentName,
            mlaf.reason,
            mlaf.status,
            mlaf.gmt_create,
            mc.num AS companyNum
        FROM ais_office_center.m_leave_application_form AS mlaf
                 LEFT JOIN ais_office_center.u_leave_application_form_type AS ulaft ON ulaft.id = mlaf.fk_leave_application_form_type_id
                 LEFT JOIN ais_permission_center.m_staff AS ms ON ms.id = mlaf.fk_staff_id
                 LEFT JOIN ais_permission_center.m_department AS md ON md.id = mlaf.fk_department_id
                 LEFT JOIN ais_permission_center.m_company AS mc ON mc.id = mlaf.fk_company_id
            <if test="approvalRecordDto.approvalTab != null and approvalRecordDto.approvalTab != '' and approvalRecordDto.approvalTab == 'myApproval'">
            INNER JOIN (
                SELECT DISTINCT
                    CAST(pi.BUSINESS_KEY_ AS SIGNED) AS businessKey
                FROM
                    ais_workflow_center.ACT_RU_TASK rt
                        INNER JOIN ais_workflow_center.ACT_RU_EXECUTION ru ON rt.EXECUTION_ID_ = ru.ID_
                        INNER JOIN ais_workflow_center.ACT_HI_PROCINST pi ON ru.PROC_INST_ID_ = pi.ID_
                WHERE
                    SUBSTRING_INDEX(pi.PROC_DEF_ID_, ':', 1) = 'm_leave_application_form'
                  AND (
            <!-- 已分配给你的任务 -->
                    rt.ASSIGNEE_ =  #{staffId}
            <!-- 你是候选人的任务 -->
                OR (
                    rt.ASSIGNEE_ IS NULL
                    AND EXISTS (
                        SELECT 1 FROM ais_workflow_center.ACT_RU_IDENTITYLINK ril WHERE ril.TASK_ID_ = rt.ID_ AND ril.TYPE_ = 'candidate' AND ril.USER_ID_ = #{staffId})
                    )
                )
            )a ON a.businessKey = mlaf.id
            </if>
        <where>
            <if test="approvalRecordDto.companyIds != null and approvalRecordDto.companyIds.size() > 0">
                AND mlaf.fk_company_id IN
                <foreach collection="approvalRecordDto.companyIds" item="companyId" index="index" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
            <if test="approvalRecordDto.approvalStatus!=null and approvalRecordDto.approvalStatus!=''">
                AND mlaf.status = #{approvalRecordDto.approvalStatus}
            </if>
            <if test="approvalRecordDto.title!=null and approvalRecordDto.title!=''">
                AND (
                REPLACE(LOWER(ulaft.type_name),' ','') LIKE CONCAT('%', REPLACE(LOWER(#{approvalRecordDto.title}), ' ', ''), '%')
                OR
                REPLACE(LOWER(ulaft.type_key),' ','') LIKE CONCAT('%', REPLACE(LOWER(#{approvalRecordDto.title}), ' ', ''), '%')
                OR mlaf.days = #{approvalRecordDto.title}
                )
            </if>
            <if test="approvalRecordDto.approvalTab != null and approvalRecordDto.approvalTab != '' and approvalRecordDto.approvalTab == 'myApplication'">
                AND mlaf.fk_staff_id = #{staffId}
            </if>
        </where>

        </if>

        <if test="approvalRecordDto.approvalType == null or approvalRecordDto.approvalType == ''">
            UNION ALL
        </if>

        <if test="approvalRecordDto.approvalType == null or approvalRecordDto.approvalType == '' or approvalRecordDto.approvalType == 'm_expense_claim_form'">
        SELECT
            mecf.id,
            mecf.fk_company_id,
            'm_expense_claim_form' AS businessKey,
            mecf.num,
            CONCAT(b.amount, mecf.fk_currency_type_num, ' - ', mecf.num) AS title,
            CASE

                WHEN IFNULL(ms.name_en, '') = '' THEN
                    ms.`name` ELSE CONCAT(ms.`name`, '（', ms.name_en, '）')
                END AS applyName,
            md.name AS departmentName,
            mecf.summary AS reason,
            mecf.status,
            mecf.gmt_create,
            mc.num AS companyNum
        FROM ais_finance_center.m_expense_claim_form AS mecf
            LEFT JOIN ais_permission_center.m_staff AS ms ON ms.id = mecf.fk_staff_id
            LEFT JOIN ais_permission_center.m_department AS md ON md.id = mecf.fk_department_id
            LEFT JOIN (
                SELECT mecf2.id, SUM(mecfi2.amount) AS amount
                FROM ais_finance_center.m_expense_claim_form AS mecf2
                LEFT JOIN ais_finance_center.m_expense_claim_form_item AS mecfi2 ON mecfi2.fk_expense_claim_form_id = mecf2.id
                GROUP BY
                mecf2.id
            ) b ON b.id = mecf.id
            LEFT JOIN ais_permission_center.m_company AS mc ON mc.id = mecf.fk_company_id

        <if test="approvalRecordDto.approvalTab != null and approvalRecordDto.approvalTab != '' and approvalRecordDto.approvalTab == 'myApproval'">
            INNER JOIN (
            SELECT DISTINCT
                CAST(pi.BUSINESS_KEY_ AS SIGNED) AS businessKey
            FROM
                ais_workflow_center.ACT_RU_TASK rt
                    INNER JOIN ais_workflow_center.ACT_RU_EXECUTION ru ON rt.EXECUTION_ID_ = ru.ID_
                    INNER JOIN ais_workflow_center.ACT_HI_PROCINST pi ON ru.PROC_INST_ID_ = pi.ID_
            WHERE
                SUBSTRING_INDEX(pi.PROC_DEF_ID_, ':', 1) = 'm_expense_claim_form'
              AND (
                <!-- 已分配给你的任务 -->
                rt.ASSIGNEE_ =  #{staffId}
                <!-- 你是候选人的任务 -->
                OR (
                    rt.ASSIGNEE_ IS NULL
                    AND EXISTS (
                        SELECT 1 FROM ais_workflow_center.ACT_RU_IDENTITYLINK ril WHERE ril.TASK_ID_ = rt.ID_ AND ril.TYPE_ = 'candidate' AND ril.USER_ID_ = #{staffId})
                    )
                )
            )a ON a.businessKey = mecf.id
        </if>
        <where>
            <if test="approvalRecordDto.companyIds != null and approvalRecordDto.companyIds.size() > 0">
                AND mecf.fk_company_id IN
                <foreach collection="approvalRecordDto.companyIds" item="companyId" index="index" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
            <if test="approvalRecordDto.approvalStatus!=null and approvalRecordDto.approvalStatus!=''">
                AND mecf.status = #{approvalRecordDto.approvalStatus}
            </if>
            <if test="approvalRecordDto.title!=null and approvalRecordDto.title!=''">
                AND (
                REPLACE(LOWER(mecf.fk_currency_type_num),' ','') LIKE CONCAT('%', REPLACE(LOWER(#{approvalRecordDto.title}), ' ', ''), '%')
                OR
                REPLACE(LOWER(mecf.num),' ','') LIKE CONCAT('%', REPLACE(LOWER(#{approvalRecordDto.title}), ' ', ''), '%')
                OR b.amount = #{approvalRecordDto.title}
                )
            </if>
            <if test="approvalRecordDto.approvalTab != null and approvalRecordDto.approvalTab != '' and approvalRecordDto.approvalTab == 'myApplication'">
                AND mecf.fk_staff_id = #{staffId}
            </if>
        </where>

        </if>

        <if test="approvalRecordDto.approvalType == null or approvalRecordDto.approvalType == ''">
            UNION ALL
        </if>

        <if test="approvalRecordDto.approvalType == null or approvalRecordDto.approvalType == '' or approvalRecordDto.approvalType == 'm_payment_application_form'">
        SELECT
            mpaf.id,
            mpaf.fk_company_id,
            'm_payment_application_form' AS businessKey,
            mpaf.num,
            CONCAT(b.amount, mpaf.fk_currency_type_num, ' - ', mpaf.num) AS title,
            CASE

                WHEN IFNULL(ms.name_en, '') = '' THEN
                    ms.`name` ELSE CONCAT(ms.`name`, '（', ms.name_en, '）')
                END AS applyName,
            md.name AS departmentName,
            mpaf.summary AS reason,
            mpaf.status,
            mpaf.gmt_create,
            mc.num AS companyNum
        FROM ais_finance_center.m_payment_application_form AS mpaf
                 LEFT JOIN ais_permission_center.m_staff AS ms ON ms.id = mpaf.fk_staff_id
                 LEFT JOIN ais_permission_center.m_department AS md ON md.id = mpaf.fk_department_id
            LEFT JOIN (
                SELECT mpaf.id, SUM(mpafi.amount) AS amount
                FROM ais_finance_center.m_payment_application_form AS mpaf
                LEFT JOIN ais_finance_center.m_payment_application_form_item AS mpafi ON mpafi.fk_payment_application_form_id = mpaf.id
                GROUP BY
                mpaf.id
            ) b ON b.id = mpaf.id
            LEFT JOIN ais_permission_center.m_company AS mc ON mc.id = mpaf.fk_company_id
        <if test="approvalRecordDto.approvalTab != null and approvalRecordDto.approvalTab != '' and approvalRecordDto.approvalTab == 'myApproval'">
            INNER JOIN (
            SELECT DISTINCT
                CAST(pi.BUSINESS_KEY_ AS SIGNED) AS businessKey
            FROM
                ais_workflow_center.ACT_RU_TASK rt
                    INNER JOIN ais_workflow_center.ACT_RU_EXECUTION ru ON rt.EXECUTION_ID_ = ru.ID_
                    INNER JOIN ais_workflow_center.ACT_HI_PROCINST pi ON ru.PROC_INST_ID_ = pi.ID_
            WHERE
                SUBSTRING_INDEX(pi.PROC_DEF_ID_, ':', 1) = 'm_payment_application_form'
              AND (
            <!-- 已分配给你的任务 -->
                rt.ASSIGNEE_ =  #{staffId}
            <!-- 你是候选人的任务 -->
                OR (
                    rt.ASSIGNEE_ IS NULL
                    AND EXISTS (
                        SELECT 1 FROM ais_workflow_center.ACT_RU_IDENTITYLINK ril WHERE ril.TASK_ID_ = rt.ID_ AND ril.TYPE_ = 'candidate' AND ril.USER_ID_ = #{staffId})
                    )
            )
            )a ON a.businessKey = mpaf.id
        </if>
        <where>
            <if test="approvalRecordDto.companyIds != null and approvalRecordDto.companyIds.size() > 0">
                AND mpaf.fk_company_id IN
                <foreach collection="approvalRecordDto.companyIds" item="companyId" index="index" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
            <if test="approvalRecordDto.approvalStatus!=null and approvalRecordDto.approvalStatus!=''">
                AND mpaf.status = #{approvalRecordDto.approvalStatus}
            </if>
            <if test="approvalRecordDto.title!=null and approvalRecordDto.title!=''">
                AND (
                REPLACE(LOWER(mpaf.fk_currency_type_num),' ','') LIKE CONCAT('%', REPLACE(LOWER(#{approvalRecordDto.title}), ' ', ''), '%')
                OR
                REPLACE(LOWER(mpaf.num),' ','') LIKE CONCAT('%', REPLACE(LOWER(#{approvalRecordDto.title}), ' ', ''), '%')
                OR b.amount = #{approvalRecordDto.title}
                )
            </if>
            <if test="approvalRecordDto.approvalTab != null and approvalRecordDto.approvalTab != '' and approvalRecordDto.approvalTab == 'myApplication'">
                AND mpaf.fk_staff_id = #{staffId}
            </if>
        </where>
        </if>

        <if test="approvalRecordDto.approvalType == null or approvalRecordDto.approvalType == ''">
            UNION ALL
        </if>

        <if test="approvalRecordDto.approvalType == null or approvalRecordDto.approvalType == '' or approvalRecordDto.approvalType == 'm_travel_claim_form'">
        SELECT
            mtcf.id,
            mtcf.fk_company_id,
            'm_travel_claim_form' AS businessKey,
            mtcf.num,
            CONCAT(b.amount, mtcf.fk_currency_type_num, ' - ', mtcf.num) AS title,
            CASE

                WHEN IFNULL(ms.name_en, '') = '' THEN
                    ms.`name` ELSE CONCAT(ms.`name`, '（', ms.name_en, '）')
                END AS applyName,
            md.name AS departmentName,
            mtcf.summary AS reason,
            mtcf.status,
            mtcf.gmt_create,
            mc.num AS companyNum
        FROM ais_finance_center.m_travel_claim_form AS mtcf
                 LEFT JOIN ais_permission_center.m_staff AS ms ON ms.id = mtcf.fk_staff_id
                 LEFT JOIN ais_permission_center.m_department AS md ON md.id = mtcf.fk_department_id
            LEFT JOIN (
                SELECT mtcf.id, SUM(mtcfi.amount) AS amount
                FROM ais_finance_center.m_travel_claim_form AS mtcf
                LEFT JOIN ais_finance_center.m_travel_claim_form_item AS mtcfi ON mtcfi.fk_travel_claim_form_id = mtcf.id
                GROUP BY
                mtcf.id
            ) b ON b.id = mtcf.id
            LEFT JOIN ais_permission_center.m_company AS mc ON mc.id = mtcf.fk_company_id
        <if test="approvalRecordDto.approvalTab != null and approvalRecordDto.approvalTab != '' and approvalRecordDto.approvalTab == 'myApproval'">
            INNER JOIN (
            SELECT DISTINCT
                CAST(pi.BUSINESS_KEY_ AS SIGNED) AS businessKey
            FROM
                ais_workflow_center.ACT_RU_TASK rt
                    INNER JOIN ais_workflow_center.ACT_RU_EXECUTION ru ON rt.EXECUTION_ID_ = ru.ID_
                    INNER JOIN ais_workflow_center.ACT_HI_PROCINST pi ON ru.PROC_INST_ID_ = pi.ID_
            WHERE
                SUBSTRING_INDEX(pi.PROC_DEF_ID_, ':', 1) = 'm_travel_claim_form'
              AND (
            <!-- 已分配给你的任务 -->
                rt.ASSIGNEE_ =  #{staffId}
            <!-- 你是候选人的任务 -->
                OR (
                rt.ASSIGNEE_ IS NULL
                AND EXISTS (
                    SELECT 1 FROM ais_workflow_center.ACT_RU_IDENTITYLINK ril WHERE ril.TASK_ID_ = rt.ID_ AND ril.TYPE_ = 'candidate' AND ril.USER_ID_ = #{staffId}
                        )
                    )
                )
            )a ON a.businessKey = mtcf.id
        </if>
        <where>
            <if test="approvalRecordDto.companyIds != null and approvalRecordDto.companyIds.size() > 0">
                AND mtcf.fk_company_id IN
                <foreach collection="approvalRecordDto.companyIds" item="companyId" index="index" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
            <if test="approvalRecordDto.approvalStatus!=null and approvalRecordDto.approvalStatus!=''">
                AND mtcf.status = #{approvalRecordDto.approvalStatus}
            </if>
            <if test="approvalRecordDto.title!=null and approvalRecordDto.title!=''">
                AND (
                REPLACE(LOWER(mtcf.fk_currency_type_num),' ','') LIKE CONCAT('%', REPLACE(LOWER(#{approvalRecordDto.title}), ' ', ''), '%')
                OR
                REPLACE(LOWER(mtcf.num),' ','') LIKE CONCAT('%', REPLACE(LOWER(#{approvalRecordDto.title}), ' ', ''), '%')
                OR b.amount = #{approvalRecordDto.title}
                )
            </if>
            <if test="approvalRecordDto.approvalTab != null and approvalRecordDto.approvalTab != '' and approvalRecordDto.approvalTab == 'myApplication'">
                AND mtcf.fk_staff_id = #{staffId}
            </if>
        </where>
        </if>

        <if test="approvalRecordDto.approvalType == null or approvalRecordDto.approvalType == null or approvalRecordDto.approvalType == ''">
            UNION ALL
        </if>

        <if test="approvalRecordDto.approvalType == null or approvalRecordDto.approvalType == '' or approvalRecordDto.approvalType == 'm_prepay_application_form'">
        SELECT
            mpaf.id,
            mpaf.fk_company_id,
            'm_prepay_application_form' AS businessKey,
            mpaf.num,
            CONCAT(mpaf.amount, mpaf.fk_currency_type_num, ' - ', mpaf.num) AS title,
            CASE

                WHEN IFNULL(ms.name_en, '') = '' THEN
                    ms.`name` ELSE CONCAT(ms.`name`, '（', ms.name_en, '）')
                END AS applyName,
            md.name AS departmentName,
            mpaf.summary AS reason,
            mpaf.status,
            mpaf.gmt_create,
            mc.num AS companyNum
        FROM ais_finance_center.m_prepay_application_form AS mpaf
                 LEFT JOIN ais_permission_center.m_staff AS ms ON ms.id = mpaf.fk_staff_id
                 LEFT JOIN ais_permission_center.m_department AS md ON md.id = mpaf.fk_department_id
                 LEFT JOIN ais_permission_center.m_company AS mc ON mc.id = mpaf.fk_company_id
        <if test="approvalRecordDto.approvalTab != null and approvalRecordDto.approvalTab != '' and approvalRecordDto.approvalTab == 'myApproval'">
            INNER JOIN (
                SELECT DISTINCT
                    CAST(pi.BUSINESS_KEY_ AS SIGNED) AS businessKey
                FROM
                    ais_workflow_center.ACT_RU_TASK rt
                        INNER JOIN ais_workflow_center.ACT_RU_EXECUTION ru ON rt.EXECUTION_ID_ = ru.ID_
                        INNER JOIN ais_workflow_center.ACT_HI_PROCINST pi ON ru.PROC_INST_ID_ = pi.ID_
                WHERE
                    SUBSTRING_INDEX(pi.PROC_DEF_ID_, ':', 1) = 'm_prepay_application_form'
                  AND (
                <!-- 已分配给你的任务 -->
                    rt.ASSIGNEE_ =  #{staffId}
                <!-- 你是候选人的任务 -->
            OR (
                rt.ASSIGNEE_ IS NULL
                AND EXISTS (
                    SELECT 1 FROM ais_workflow_center.ACT_RU_IDENTITYLINK ril WHERE ril.TASK_ID_ = rt.ID_ AND ril.TYPE_ = 'candidate' AND ril.USER_ID_ = #{staffId})
                     )
                )
            )a ON a.businessKey = mpaf.id
        </if>
        <where>
            <if test="approvalRecordDto.companyIds != null and approvalRecordDto.companyIds.size() > 0">
                AND mpaf.fk_company_id IN
                <foreach collection="approvalRecordDto.companyIds" item="companyId" index="index" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
            <if test="approvalRecordDto.approvalStatus!=null and approvalRecordDto.approvalStatus!=''">
                AND mpaf.status = #{approvalRecordDto.approvalStatus}
            </if>
            <if test="approvalRecordDto.title!=null and approvalRecordDto.title!=''">
                AND (
                REPLACE(LOWER(mpaf.fk_currency_type_num),' ','') LIKE CONCAT('%', REPLACE(LOWER(#{approvalRecordDto.title}), ' ', ''), '%')
                OR
                REPLACE(LOWER(mpaf.num),' ','') LIKE CONCAT('%', REPLACE(LOWER(#{approvalRecordDto.title}), ' ', ''), '%')
                OR mpaf.amount = #{approvalRecordDto.title}
                )
            </if>
            <if test="approvalRecordDto.approvalTab != null and approvalRecordDto.approvalTab != '' and approvalRecordDto.approvalTab == 'myApplication'">
                AND mpaf.fk_staff_id = #{staffId}
            </if>
        </where>
        </if>
        )a
    order by a.gmt_create desc
    </select>
    <select id="getAssigneeInfo" resultType="com.get.workflowcenter.vo.AssigneeVo">
        SELECT
        a.businessKey AS formId,
        a.assignee,
        CASE
        WHEN IFNULL(ms.name_en, '') = '' THEN
        ms.`name` ELSE CONCAT(ms.`name`, '（', ms.name_en, '）')
        END AS assigneeName
        FROM ais_permission_center.m_staff AS ms
        LEFT JOIN (
            SELECT
                t1.businessKey,
                t1.assignee
            FROM (
                     SELECT
                         CAST(pi.BUSINESS_KEY_ AS SIGNED) AS businessKey,
                         rt.ASSIGNEE_ AS assignee,
                         rt.CREATE_TIME_
                     FROM ais_workflow_center.ACT_RU_TASK rt
                              INNER JOIN ais_workflow_center.ACT_RU_EXECUTION ru
                                         ON rt.EXECUTION_ID_ = ru.ID_
                              INNER JOIN ais_workflow_center.ACT_HI_PROCINST pi
                                         ON ru.PROC_INST_ID_ = pi.ID_
                     WHERE SUBSTRING_INDEX(pi.PROC_DEF_ID_, ':', 1) = #{tableName}
                     AND pi.BUSINESS_KEY_  IN
                        <foreach collection="formIds" item="formId" index="index" open="(" separator="," close=")">
                            #{formId}
                        </foreach>
                 ) t1
                LEFT JOIN (
                SELECT
                    CAST(pi.BUSINESS_KEY_ AS SIGNED) AS businessKey,
                    MAX(rt.CREATE_TIME_) AS max_create_time
                FROM ais_workflow_center.ACT_RU_TASK rt
                         INNER JOIN ais_workflow_center.ACT_RU_EXECUTION ru
                                    ON rt.EXECUTION_ID_ = ru.ID_
                         INNER JOIN ais_workflow_center.ACT_HI_PROCINST pi
                                    ON ru.PROC_INST_ID_ = pi.ID_
                WHERE SUBSTRING_INDEX(pi.PROC_DEF_ID_, ':', 1) = #{tableName}
                AND pi.BUSINESS_KEY_  IN
                <foreach collection="formIds" item="formId" index="index" open="(" separator="," close=")">
                    #{formId}
                </foreach>
                GROUP BY CAST(pi.BUSINESS_KEY_ AS SIGNED)
            ) t2 ON t1.businessKey = t2.businessKey AND t1.CREATE_TIME_ = t2.max_create_time
            WHERE t2.businessKey IS NOT NULL
        )a ON a.assignee = ms.id
    </select>

</mapper>