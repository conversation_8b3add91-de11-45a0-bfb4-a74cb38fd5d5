package com.get.financecenter.utils;

import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.ProviderMapper;
import com.get.financecenter.entity.Provider;
import com.get.financecenter.enums.RelationTargetKeyEnum;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.salecenter.entity.Student;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.salecenter.vo.StudentServiceFeeSummaryVo;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class RelationTargetProcessorUtils {

    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ProviderMapper providerMapper;
    @Resource
    private ISaleCenterClient saleCenterClient;

    private Map<String, String> displayNameMap = new HashMap<>();
    @PostConstruct
    public void init() {
        // 初始化关系目标类型与显示名称的映射
        for (RelationTargetKeyEnum type : RelationTargetKeyEnum.values()) {
            displayNameMap.put(type.relationTargetKey, type.name);
        }
    }

    public <T> void processRelationTarget(String relationTargetKey,
                                          Long relationTargetId,
                                          Consumer<String> nameHandler, //单独的名称
                                          Consumer<Long> companyIdHandler,
                                          Consumer<String> displayNameHandler //拼接的名称
    ) {
        if (relationTargetKey == null || relationTargetId == null) return;

        String name = null;
        Long companyId = null;

        if (relationTargetKey.equals(RelationTargetKeyEnum.STAFF.relationTargetKey)) {
            StaffVo staffVo = permissionCenterClient.getStaffById(relationTargetId).getData();
            if (GeneralTool.isNotEmpty(staffVo)) {
                name = staffVo.getFullName();
                companyId = staffVo.getFkCompanyId();
            }
        } else if (relationTargetKey.equals(RelationTargetKeyEnum.PROVIDER.relationTargetKey)) {
            Provider provider = providerMapper.selectById(relationTargetId);
            if (GeneralTool.isNotEmpty(provider)) {
                name = provider.getName();
                companyId = provider.getFkCompanyId();
            }
        } else if (relationTargetKey.equals(RelationTargetKeyEnum.STUDENT.relationTargetKey)) {
            Student student = saleCenterClient.getStudentById(relationTargetId).getData();
            if (GeneralTool.isNotEmpty(student)) {
                name = student.getName();
                companyId = student.getFkCompanyId();
            }
        } else if (relationTargetKey.equals(RelationTargetKeyEnum.STUDENT_SERVICE_FEE.relationTargetKey)) {
            StudentServiceFeeSummaryVo vo = saleCenterClient.getServiceFeeInfoById(relationTargetId).getData();
            if (GeneralTool.isNotEmpty(vo)) {
                name = vo.getServiceFeeNum();
                companyId = vo.getTargetCompanyNameId();
            }
        }

        // 处理回调
        if (name != null) {
            if (nameHandler != null) nameHandler.accept(name);
            if (companyIdHandler != null) companyIdHandler.accept(companyId);
            if (displayNameHandler != null) {
                String displayName = displayNameMap.getOrDefault(relationTargetKey, "") + ":" + name;
                displayNameHandler.accept(displayName);
            }
        }
    }

}
