<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.workflowcenter.mapper.ActRuExecutionMapper">
    <resultMap id="BaseResultMap" type="com.get.workflowcenter.entity.ActRuExecution">
        <id column="ID_" jdbcType="VARCHAR" property="id"/>
        <result column="REV_" jdbcType="INTEGER" property="rev"/>
        <result column="PROC_INST_ID_" jdbcType="VARCHAR" property="procInstId"/>
        <result column="BUSINESS_KEY_" jdbcType="VARCHAR" property="businessKey"/>
        <result column="PARENT_ID_" jdbcType="VARCHAR" property="parentId"/>
        <result column="PROC_DEF_ID_" jdbcType="VARCHAR" property="procDefId"/>
        <result column="SUPER_EXEC_" jdbcType="VARCHAR" property="superExec"/>
        <result column="ROOT_PROC_INST_ID_" jdbcType="VARCHAR" property="rootProcInstId"/>
        <result column="ACT_ID_" jdbcType="VARCHAR" property="actId"/>
        <result column="IS_ACTIVE_" jdbcType="TINYINT" property="isActive"/>
        <result column="IS_CONCURRENT_" jdbcType="TINYINT" property="isConcurrent"/>
        <result column="IS_SCOPE_" jdbcType="TINYINT" property="isScope"/>
        <result column="IS_EVENT_SCOPE_" jdbcType="TINYINT" property="isEventScope"/>
        <result column="IS_MI_ROOT_" jdbcType="TINYINT" property="isMiRoot"/>
        <result column="SUSPENSION_STATE_" jdbcType="INTEGER" property="suspensionState"/>
        <result column="CACHED_ENT_STATE_" jdbcType="INTEGER" property="cachedEntState"/>
        <result column="TENANT_ID_" jdbcType="VARCHAR" property="tenantId"/>
        <result column="NAME_" jdbcType="VARCHAR" property="name"/>
        <result column="START_TIME_" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="START_USER_ID_" jdbcType="VARCHAR" property="startUserId"/>
        <result column="LOCK_TIME_" jdbcType="TIMESTAMP" property="lockTime"/>
        <result column="IS_COUNT_ENABLED_" jdbcType="TINYINT" property="isCountEnabled"/>
        <result column="EVT_SUBSCR_COUNT_" jdbcType="INTEGER" property="evtSubscrCount"/>
        <result column="TASK_COUNT_" jdbcType="INTEGER" property="taskCount"/>
        <result column="JOB_COUNT_" jdbcType="INTEGER" property="jobCount"/>
        <result column="TIMER_JOB_COUNT_" jdbcType="INTEGER" property="timerJobCount"/>
        <result column="SUSP_JOB_COUNT_" jdbcType="INTEGER" property="suspJobCount"/>
        <result column="DEADLETTER_JOB_COUNT_" jdbcType="INTEGER" property="deadletterJobCount"/>
        <result column="VAR_COUNT_" jdbcType="INTEGER" property="varCount"/>
        <result column="ID_LINK_COUNT_" jdbcType="INTEGER" property="idLinkCount"/>
    </resultMap>
    <sql id="Base_Column_List">
    ID_, REV_, PROC_INST_ID_, BUSINESS_KEY_, PARENT_ID_, PROC_DEF_ID_, SUPER_EXEC_, ROOT_PROC_INST_ID_, 
    ACT_ID_, IS_ACTIVE_, IS_CONCURRENT_, IS_SCOPE_, IS_EVENT_SCOPE_, IS_MI_ROOT_, SUSPENSION_STATE_, 
    CACHED_ENT_STATE_, TENANT_ID_, NAME_, START_TIME_, START_USER_ID_, LOCK_TIME_, IS_COUNT_ENABLED_, 
    EVT_SUBSCR_COUNT_, TASK_COUNT_, JOB_COUNT_, TIMER_JOB_COUNT_, SUSP_JOB_COUNT_, DEADLETTER_JOB_COUNT_, 
    VAR_COUNT_, ID_LINK_COUNT_
  </sql>


    <insert id="insertSelective" parameterType="com.get.workflowcenter.entity.ActRuExecution" keyProperty="id"
            useGeneratedKeys="true">
        insert into act_ru_execution
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID_,
            </if>
            <if test="rev != null">
                REV_,
            </if>
            <if test="procInstId != null">
                PROC_INST_ID_,
            </if>
            <if test="businessKey != null">
                BUSINESS_KEY_,
            </if>
            <if test="parentId != null">
                PARENT_ID_,
            </if>
            <if test="procDefId != null">
                PROC_DEF_ID_,
            </if>
            <if test="superExec != null">
                SUPER_EXEC_,
            </if>
            <if test="rootProcInstId != null">
                ROOT_PROC_INST_ID_,
            </if>
            <if test="actId != null">
                ACT_ID_,
            </if>
            <if test="isActive != null">
                IS_ACTIVE_,
            </if>
            <if test="isConcurrent != null">
                IS_CONCURRENT_,
            </if>
            <if test="isScope != null">
                IS_SCOPE_,
            </if>
            <if test="isEventScope != null">
                IS_EVENT_SCOPE_,
            </if>
            <if test="isMiRoot != null">
                IS_MI_ROOT_,
            </if>
            <if test="suspensionState != null">
                SUSPENSION_STATE_,
            </if>
            <if test="cachedEntState != null">
                CACHED_ENT_STATE_,
            </if>
            <if test="tenantId != null">
                TENANT_ID_,
            </if>
            <if test="name != null">
                NAME_,
            </if>
            <if test="startTime != null">
                START_TIME_,
            </if>
            <if test="startUserId != null">
                START_USER_ID_,
            </if>
            <if test="lockTime != null">
                LOCK_TIME_,
            </if>
            <if test="isCountEnabled != null">
                IS_COUNT_ENABLED_,
            </if>
            <if test="evtSubscrCount != null">
                EVT_SUBSCR_COUNT_,
            </if>
            <if test="taskCount != null">
                TASK_COUNT_,
            </if>
            <if test="jobCount != null">
                JOB_COUNT_,
            </if>
            <if test="timerJobCount != null">
                TIMER_JOB_COUNT_,
            </if>
            <if test="suspJobCount != null">
                SUSP_JOB_COUNT_,
            </if>
            <if test="deadletterJobCount != null">
                DEADLETTER_JOB_COUNT_,
            </if>
            <if test="varCount != null">
                VAR_COUNT_,
            </if>
            <if test="idLinkCount != null">
                ID_LINK_COUNT_,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="rev != null">
                #{rev,jdbcType=INTEGER},
            </if>
            <if test="procInstId != null">
                #{procInstId,jdbcType=VARCHAR},
            </if>
            <if test="businessKey != null">
                #{businessKey,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null">
                #{parentId,jdbcType=VARCHAR},
            </if>
            <if test="procDefId != null">
                #{procDefId,jdbcType=VARCHAR},
            </if>
            <if test="superExec != null">
                #{superExec,jdbcType=VARCHAR},
            </if>
            <if test="rootProcInstId != null">
                #{rootProcInstId,jdbcType=VARCHAR},
            </if>
            <if test="actId != null">
                #{actId,jdbcType=VARCHAR},
            </if>
            <if test="isActive != null">
                #{isActive,jdbcType=TINYINT},
            </if>
            <if test="isConcurrent != null">
                #{isConcurrent,jdbcType=TINYINT},
            </if>
            <if test="isScope != null">
                #{isScope,jdbcType=TINYINT},
            </if>
            <if test="isEventScope != null">
                #{isEventScope,jdbcType=TINYINT},
            </if>
            <if test="isMiRoot != null">
                #{isMiRoot,jdbcType=TINYINT},
            </if>
            <if test="suspensionState != null">
                #{suspensionState,jdbcType=INTEGER},
            </if>
            <if test="cachedEntState != null">
                #{cachedEntState,jdbcType=INTEGER},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="startUserId != null">
                #{startUserId,jdbcType=VARCHAR},
            </if>
            <if test="lockTime != null">
                #{lockTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isCountEnabled != null">
                #{isCountEnabled,jdbcType=TINYINT},
            </if>
            <if test="evtSubscrCount != null">
                #{evtSubscrCount,jdbcType=INTEGER},
            </if>
            <if test="taskCount != null">
                #{taskCount,jdbcType=INTEGER},
            </if>
            <if test="jobCount != null">
                #{jobCount,jdbcType=INTEGER},
            </if>
            <if test="timerJobCount != null">
                #{timerJobCount,jdbcType=INTEGER},
            </if>
            <if test="suspJobCount != null">
                #{suspJobCount,jdbcType=INTEGER},
            </if>
            <if test="deadletterJobCount != null">
                #{deadletterJobCount,jdbcType=INTEGER},
            </if>
            <if test="varCount != null">
                #{varCount,jdbcType=INTEGER},
            </if>
            <if test="idLinkCount != null">
                #{idLinkCount,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <select id="getExecutionList" resultType="com.get.workflowcenter.vo.ActRuExecutionVo">
        select ahp.START_USER_ID_ as startByName,arp.KEY_ as procdefKey, are.ID_,
        are.PROC_INST_ID_,are.PARENT_ID_,are.SUSPENSION_STATE_,are.START_TIME_ ,ahp.BUSINESS_KEY_ from ACT_RU_EXECUTION
        are
        left join ACT_HI_PROCINST ahp on are.PROC_INST_ID_ =ahp.ID_
        left join ACT_RE_PROCDEF arp on arp.ID_ =are.PROC_DEF_ID_ where 1=1
        <if test="procdefKey != null and procdefKey!=''">
            and arp.KEY_ like #{procdefKey}
        </if>
        <if test="id != null and id !=''">
            and are.ID_=#{id}
        </if>
        <if test="procInstId!=null and procInstId!=''">
            and are.PROC_INST_ID_=#{procInstId}
        </if>
        and are.PARENT_ID_ is not null order by ahp.START_TIME_ DESC
    </select>

    <select id="getExecutionListByNormalUser" resultType="com.get.workflowcenter.vo.ActRuExecutionVo">
        select ahp.START_USER_ID_ as startByName,arp.KEY_ as procdefKey, are.ID_,
        are.PROC_INST_ID_,are.PARENT_ID_,are.SUSPENSION_STATE_,are.START_TIME_ ,ahp.BUSINESS_KEY_ from ACT_RU_EXECUTION
        are
        left join ACT_HI_PROCINST ahp on are.PROC_INST_ID_ =ahp.ID_
        left join ACT_RE_PROCDEF arp on arp.ID_ =are.PROC_DEF_ID_ where 1=1
        <if test="procdefKey != null and procdefKey!=''">
            and arp.KEY_ like #{procdefKey}
        </if>
        <if test="id != null and id !=''">
            and are.ID_=#{id}
        </if>
        <if test="procInstId!=null and procInstId!=''">
            and are.PROC_INST_ID_=#{procInstId}
        </if>
        and ahp.START_USER_ID_=#{username}

        and are.PARENT_ID_ is not null order by ahp.START_TIME_ DESC
    </select>

</mapper>