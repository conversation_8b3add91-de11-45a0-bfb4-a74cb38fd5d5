<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.votingcenter.dao.voting.VotingRuleMapper">
  <insert id="insertSelective" parameterType="com.get.votingcenter.entity.VotingRule" keyProperty="id" useGeneratedKeys="true">
    insert into m_voting_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkVotingId != null">
        fk_voting_id,
      </if>
      <if test="voteCount != null">
        vote_count,
      </if>
      <if test="voteLimit != null">
        vote_limit,
      </if>
      <if test="isRepeatVoting != null">
        is_repeat_voting,
      </if>
      <if test="voteRuleAuth != null">
        vote_rule_auth,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkVotingId != null">
        #{fkVotingId,jdbcType=BIGINT},
      </if>
      <if test="voteCount != null">
        #{voteCount,jdbcType=INTEGER},
      </if>
      <if test="voteLimit != null">
        #{voteLimit,jdbcType=INTEGER},
      </if>
      <if test="isRepeatVoting != null">
        #{isRepeatVoting,jdbcType=BIT},
      </if>
      <if test="voteRuleAuth != null">
        #{voteRuleAuth,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select ifnull(max(view_order)+1,1) from m_voting_rule
  </select>
</mapper>