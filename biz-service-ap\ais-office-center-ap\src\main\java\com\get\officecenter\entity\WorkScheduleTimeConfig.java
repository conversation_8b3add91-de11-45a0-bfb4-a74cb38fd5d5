package com.get.officecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2022/10/27
 * @TIME: 17:41
 * @Description:排班时间设定
 **/
@Data
@TableName("m_work_schedule_time_config")
public class WorkScheduleTimeConfig extends BaseEntity implements Serializable {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 部门Id
     */
    @ApiModelProperty(value = "部门Id")
    @UpdateWithNull
    private Long fkDepartmentId;

    /**
     * 工作开始时间
     */
    @ApiModelProperty(value = "工作开始时间")
    private String workingStartTime;

    /**
     * 工作结束时间
     */
    @ApiModelProperty(value = "工作结束时间")
    private String workingEndTime;

    /**
     * 午休开始时间
     */
    @ApiModelProperty(value = "午休开始时间")
    private String noonBreakStartTime;

    /**
     * 午休结束时间
     */
    @ApiModelProperty(value = "午休结束时间")
    private String noonBreakEndTime;

    /**
     * 工作时长（小时）
     */
    @ApiModelProperty(value = "工作时长（小时）")
    @UpdateWithNull
    private BigDecimal workingDuration;

    /**
     * 每周工作日：如：1,2,3,4,5
     */
    @ApiModelProperty(value = "每周工作日：如：1,2,3,4,5")
    private String workingWeekCycle;
}
