package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * author:Neil
 * Time: 16:19
 * Date: 2022/8/18
 * Description:
 */
@Data
public class ClientEventRemindDto {

    @ApiModelProperty("咨询客户Id")
    private Long fkClientId;

    @ApiModelProperty(value = "客户事件类型Id（学生共用）")
    private Long fkStudentEventTypeId;

    @ApiModelProperty(value = "预约回访时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    private Date followUpTime;

    @ApiModelProperty(value = "事件内容")
    private String description;


}
