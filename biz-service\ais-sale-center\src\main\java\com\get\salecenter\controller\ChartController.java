package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.ChartVo;
import com.get.salecenter.vo.DateRoomChartVo;
import com.get.salecenter.vo.UsedRoomChartVo;
import com.get.salecenter.service.ChartService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/10/27 15:00
 * @verison: 1.0
 * @description:
 */
@Api(tags = "统计图管理")
@RestController
@RequestMapping("sale/statisticalDiagram")
public class ChartController {
    @Resource
    private ChartService chartService;

    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "房间汇总统计图", notes = "isArrange切换查询安排和未安排")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/统计图管理/查询房间汇总统计图")
    @PostMapping("getRoomChartList")
    public ResponseBo getRoomChartList(@RequestParam boolean isArrange, @RequestParam Long conventionId,@RequestParam(value = "types",required = false)List<Integer> types) {
        ListResponseBo<Object> responseBo = new ListResponseBo<>();
        List<DateRoomChartVo> datas = chartService.getRoomChartList(isArrange, conventionId,types);
        List<String> roomTypeNames = chartService.getRoomTypeNames(conventionId);
        responseBo.setData(roomTypeNames);
        responseBo.setDatas(datas);
        return responseBo;
    }

    @ApiOperation(value = "晚宴桌座位汇总统计图", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/统计图管理/晚宴桌座位汇总统计图")
    @PostMapping("getDinnerChartList")
    public ResponseBo getDinnerChartList(@RequestParam Long conventionId) {
        ListResponseBo<Object> responseBo = new ListResponseBo<>();
        String tableType = ProjectKeyEnum.CONVENTION_DINNER_TABLE.key;
        ChartVo datas = chartService.getTableChartList(conventionId, tableType);
        //获取桌台编号集合
        List<String> tableNums = chartService.getTableNums(conventionId, tableType);
        responseBo.setDatas(tableNums);
        responseBo.setData(datas);
        return responseBo;
    }

    @ApiOperation(value = "培训桌座位汇总统计图", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/统计图管理/培训桌座位汇总统计图")
    @PostMapping("getTrainingChartList")
    public ResponseBo getTrainingChartList(@RequestParam Long conventionId) {
        ListResponseBo<Object> responseBo = new ListResponseBo<>();
        String tableType = ProjectKeyEnum.CONVENTION_TRAINING_TABLE.key;
        ChartVo datas = chartService.getTableChartList(conventionId, tableType);
        //获取桌台编号集合
        List<String> tableNums = chartService.getTableNums(conventionId, tableType);
        responseBo.setDatas(tableNums);
        responseBo.setData(datas);
        return responseBo;
    }

    @ApiIgnore
    @ApiOperation(value = "房间使用汇总统计图", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/统计图管理/查询房间使用汇总统计图")
    @PostMapping("getUsedRoomChartList")
    public ResponseBo getUsedRoomChartList(@RequestParam boolean isUsed, @RequestParam Long conventionId) {
        ListResponseBo<Object> responseBo = new ListResponseBo<>();
        List<UsedRoomChartVo> datas = chartService.getUsedRoomChartList(isUsed, conventionId);
        //获取入住时间集合
        List<String> dates = chartService.getDates(conventionId);
        responseBo.setDatas(datas);
        responseBo.setData(dates);
        return responseBo;

    }

    //    @ApiOperation(value = "房间汇总统计图", notes = "第一个数组为对应房型安排人数，第二个数组为对应房型安排的房间数，isArrange切换查询安排和未安排")
//    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/统计图管理/查询房间汇总统计图")
//    @PostMapping("getRoomChartList")
//    public ResponseBo getRoomChartList(@RequestParam boolean isArrange, @RequestParam Long conventionId)  {
//        ListResponseBo<Object> responseBo = new ListResponseBo<>();
//        List<RoomChartVo> datas = chartService.getRoomChartList(isArrange, conventionId);
//        //获取入住时间集合
//        List<String> dates = chartService.getDates(conventionId);
//        responseBo.setDatas(datas);
//        responseBo.setData(dates);
//        return responseBo;
//
//    }
}
