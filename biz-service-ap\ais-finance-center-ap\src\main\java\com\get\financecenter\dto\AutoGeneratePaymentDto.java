package com.get.financecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 自动生成付款单Vo
 *
 * <AUTHOR>
 * @date 2021/12/29 15:21
 */
@Data
public class AutoGeneratePaymentDto {

    @ApiModelProperty(value = "附件")
    List<MediaAndAttachedDto> mediaAndAttachedVoList;
    @ApiModelProperty(value = "批次号")
    @NotBlank(message = "批次号不能为空")
    private String numSettlementBatch;
    @NotNull(message = "银行帐号Id（公司）不能为空")
    @ApiModelProperty(value = "银行帐号Id（公司）")
    private Long fkBankAccountIdCompany;
    @ApiModelProperty(value = "付款单编号（凭证号）")
    private String numBank;

    @ApiModelProperty(value = "付款日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date paymentDate;

}
