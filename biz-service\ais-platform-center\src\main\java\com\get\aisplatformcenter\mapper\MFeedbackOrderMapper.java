package com.get.aisplatformcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.aisplatformcenterap.dto.work.MFeedbackOrderDto;
import com.get.aisplatformcenterap.entity.MFeedbackOrderEntity;
import com.get.aisplatformcenterap.entity.UFeedbackOrderTypeEntity;
import com.get.aisplatformcenterap.vo.work.MFeedbackOrderDetailVo;
import com.get.aisplatformcenterap.vo.work.MFeedbackOrderVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_feedback_order】的数据库操作Mapper
* @createDate 2025-05-26 16:46:43
* @Entity com.get.aisplatformcenter.entity.MFeedbackOrder
*/
@Mapper
public interface MFeedbackOrderMapper extends BaseMapper<MFeedbackOrderEntity> {

    List<MFeedbackOrderVo> searchPage(IPage<MFeedbackOrderVo> page, @Param("query") MFeedbackOrderDto params);

    MFeedbackOrderDetailVo  getDetail(Long id);

    List<UFeedbackOrderTypeEntity> getUFeedbackOrderType();


}




