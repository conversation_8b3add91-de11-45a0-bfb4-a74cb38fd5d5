package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/10/28 9:46
 * @verison: 1.0
 * @description:
 */
@Data
public class TableChartVo {
    /**
     * 每一条柱状图的名字
     */
    @ApiModelProperty(value = "每一条柱状图的名字")
    private String name;

    /**
     * 统计集合
     */
    @ApiModelProperty(value = "统计集合")
    private List<Integer> countList;

    /**
     * 每个桌台得安排人数
     */
    private Integer count;
    /**
     * 桌台id
     */
    private Long tableId;
    /**
     * 该桌台座位数
     */
    private Integer seatCount;


}
