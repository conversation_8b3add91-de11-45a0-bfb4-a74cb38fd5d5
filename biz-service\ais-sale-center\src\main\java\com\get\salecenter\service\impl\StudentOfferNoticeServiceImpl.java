package com.get.salecenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.cache.CacheNames;
import com.get.common.consts.AESConstant;
import com.get.common.eunms.ErrorCodeEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.utils.AESUtils;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.redis.cache.GetRedis;
import com.get.core.secure.StaffInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestContextUtil;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.remindercenter.dto.MailDto;
//import com.get.remindercenter.vo.MailVo;
import com.get.reportcenter.dto.ReportSaleDto;
import com.get.reportcenter.feign.IReportCenterClient;
//import com.get.reportcenter.vo.ReportSaleVo;
import com.get.salecenter.dao.sale.AgentMapper;
import com.get.salecenter.dao.sale.StudentMapper;
import com.get.salecenter.dao.sale.StudentOfferItemStepMapper;
import com.get.salecenter.vo.*;
import com.get.salecenter.vo.EmailStatisticsOfferItemVo;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.entity.StudentOfferItemStep;
import com.get.salecenter.entity.StudentOfferNotice;
import com.get.salecenter.dao.sale.StudentOfferNoticeMapper;
import com.get.salecenter.service.AsyncReminderService;
import com.get.salecenter.service.AsyncStatisticsService;
import com.get.salecenter.service.IAgentService;
import com.get.salecenter.service.IStudentOfferService;
import com.get.salecenter.service.IStudentProjectRoleStaffService;
import com.get.salecenter.service.StudentOfferNoticeService;
import com.get.salecenter.utils.sale.VerifyStudentOfferItemUtils;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.BatchStudentOfferNoticeDto;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.get.core.mybatis.base.BaseServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Service
public class StudentOfferNoticeServiceImpl extends BaseServiceImpl<StudentOfferNoticeMapper, StudentOfferNotice> implements StudentOfferNoticeService {

    @Resource
    private AsyncStatisticsService asyncStatisticsService;

    @Resource
    private IReportCenterClient reportCenterClient;

    @Resource
    private UtilService utilService;

    @Resource
    private StudentMapper studentMapper;

    @Resource
    private AsyncReminderService asyncReminderService;

    @Value("${spring.mail.port}")
    private int port;

    @Resource
    private GetRedis getRedis;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private VerifyStudentOfferItemUtils verifyStudentOfferItemUtils;

    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Resource
    private IAgentService agentService;

    @Resource
    private StudentOfferItemStepMapper studentOfferItemStepMapper;

    @Resource
    private IStudentProjectRoleStaffService studentProjectRoleStaffService;
    @Resource
    private AgentMapper agentMapper;

    @Resource
    private IStudentOfferService studentOfferService;

    /**
     * 邮件统计按钮
     * @param emailStatisticsDto
     */
    @GlobalTransactional
    @Override
    public ResponseBo doEmailStatistics(EmailStatisticsDto emailStatisticsDto) {
        Boolean flag = getRedis.setNx(CacheNames.EMAIL_STATISTICS_KEY+SecureUtil.getStaffId(), 1, 10*60L);
        if (flag){
            try {
                String emailStatisticsVoString = JSONObject.toJSONString(emailStatisticsDto);
                ReportSaleDto reportSaleVo = new ReportSaleDto();
                reportSaleVo.setReportQuery(emailStatisticsVoString);
                reportSaleVo.setReportStatus(1);
                reportSaleVo.setReportTime(new Date());
                reportSaleVo.setReportName(ProjectKeyEnum.EMAIL_STATISTICS.value);
                reportSaleVo.setFkUserId(SecureUtil.getStaffId());
                utilService.setCreateInfo(reportSaleVo);
                Result<Long> result = reportCenterClient.addReportSaleCommon(reportSaleVo);
                if (!result.isSuccess()) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("feign_execution_failed"));
                }
                Long id = result.getData();
                StaffInfo staffInfo = SecureUtil.getStaffInfo();
                String locale = SecureUtil.getLocale();
                Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
                asyncStatisticsService.asyncEmailStatistics(headerMap,staffInfo,locale,SecureUtil.getCountryIds(), emailStatisticsDto,id,CacheNames.EMAIL_STATISTICS_KEY+SecureUtil.getStaffId());
                return ResponseBo.ok(ErrorCodeEnum.REQUEST_OK.getCode(),"邮件统计成功");
            }catch (Exception e){
                //失败释放锁
                getRedis.del(CacheNames.EMAIL_STATISTICS_KEY+SecureUtil.getStaffId());
                return ResponseBo.ok(ErrorCodeEnum.INVALID_PARAM.getCode(),e.getMessage());
            }
        }else {
            return ResponseBo.ok(ErrorCodeEnum.INVALID_PARAM.getCode(),"邮件统计执行中。。。");
        }
    }

    /**
     * 列表数据
     * @param studentOfferNoticeListDto
     * @param page
     * @return
     */
    @Override
    public List<StudentOfferNoticeListVo> getStudentOfferNotices(StudentOfferNoticeListDto studentOfferNoticeListDto, Page page) {
        List<StudentOfferNoticeListVo> studentOfferNoticeListDtoList;
        if (GeneralTool.isNotEmpty(page)){
            IPage<StudentOfferNoticeListVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            studentOfferNoticeListDtoList = this.baseMapper.getStudentOfferNotices(iPage, studentOfferNoticeListDto,SecureUtil.getStaffInfo().getLoginId());
            page.setAll((int) iPage.getTotal());
        }else {
            studentOfferNoticeListDtoList = this.baseMapper.getStudentOfferNotices(null, studentOfferNoticeListDto,SecureUtil.getStaffInfo().getLoginId());
        }
        if (GeneralTool.isEmpty(studentOfferNoticeListDtoList)){
            return Collections.emptyList();
        }
        Set<Long> studentIds = studentOfferNoticeListDtoList.stream().map(StudentOfferNoticeListVo::getFkStudentId).collect(Collectors.toSet());
        List<StudentVo> studentZhEnNameByIds = studentMapper.getStudentZhEnNameByIds(studentIds);
        Map<Long, String> studentNameMap = studentZhEnNameByIds.stream().collect(Collectors.toMap(StudentVo::getId, StudentVo::getFullName));
        for (StudentOfferNoticeListVo studentOfferNoticeListVo : studentOfferNoticeListDtoList) {
            String studentName = studentNameMap.get(studentOfferNoticeListVo.getFkStudentId());
            studentOfferNoticeListVo.setStudentName(studentName);
        }
        return studentOfferNoticeListDtoList;
    }

    /**
     * 批量发送邮件
     * @param batchStudentOfferNoticeDto
     */
    @Override
    public void batchSendStudentOfferNotice(BatchStudentOfferNoticeDto batchStudentOfferNoticeDto) {
        if (GeneralTool.isEmpty(batchStudentOfferNoticeDto.getStudentOfferNoticeIds())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }

        List<StudentOfferNotice> studentOfferNotices = this.baseMapper.selectBatchIds(batchStudentOfferNoticeDto.getStudentOfferNoticeIds());
        Set<Long> staffIds = studentOfferNotices.stream().map(StudentOfferNotice::getFkStaffIdFrom).collect(Collectors.toSet());
        List<StaffVo> staffVos = permissionCenterClient.getStaffByIds(staffIds);
        Map<Long, String> emailPasswordMap = staffVos.stream().filter(e->GeneralTool.isNotEmpty(e.getEmailPassword())).collect(Collectors.toMap(StaffVo::getId, StaffVo::getEmailPassword));

        //拒收邮件的代理
        List<Agent> agents = agentMapper.selectList(Wrappers.<Agent>lambdaQuery().eq(Agent::getIsRejectEmail, 1));
        List<Long> rejectEmailAgentIds = new ArrayList<>();
        if (GeneralTool.isNotEmpty(agents)) {
            rejectEmailAgentIds = agents.stream().map(Agent::getId).collect(Collectors.toList());
        }
        Map<Long, MailDto> mailVoMap = Maps.newHashMap();
        for (StudentOfferNotice studentOfferNotice : studentOfferNotices) {
            MailDto mailVo = new MailDto();
            mailVo.setDefaultEncoding("utf-8");
            if (GeneralTool.isNotEmpty(studentOfferNotice.getFromEmail())){
                if (studentOfferNotice.getFromEmail().contains("@geaworld.org")){
                    mailVo.setHost("smtp.qiye.aliyun.com");
                }else if (studentOfferNotice.getFromEmail().contains("@geteducation.org")){
                    mailVo.setHost("smtp.exmail.qq.com");
                }else if (studentOfferNotice.getFromEmail().contains("@qq.com")){
                    mailVo.setHost("smtp.qq.com");
                }
            }else {
                mailVo.setHost("smtp.qiye.aliyun.com");
            }
            mailVo.setPort(465);
            mailVo.setProtocol("smtps");
            mailVo.setUserName(studentOfferNotice.getFromEmail());
            String password = emailPasswordMap.get(studentOfferNotice.getFkStaffIdFrom());
            String decrypt = "";
            try {
                decrypt = AESUtils.Decrypt(password, AESConstant.AESKEY);
            } catch (Exception e) {
                log.error("邮箱密码解析失败！studentOfferNotice::id="+studentOfferNotice.getId());
            }
            if (GeneralTool.isNotEmpty(decrypt)){
                mailVo.setPassword(decrypt);
            }
            mailVo.setTitle(studentOfferNotice.getEmailSubject());
            String toEmail = studentOfferNotice.getToEmail();
            if (GeneralTool.isNotEmpty(toEmail)){
                String[] toEmails = toEmail.split(";");
                mailVo.setToEmails(toEmails);
            }
            String ccEmail = studentOfferNotice.getCcEmail();
            if (GeneralTool.isNotEmpty(ccEmail)){
                String[] ccEmails = ccEmail.split(";");
                mailVo.setCcEmails(ccEmails);
            }
            mailVo.setTemplate(studentOfferNotice.getEmailContent());
            mailVo.setFlag(true);

            mailVoMap.put(studentOfferNotice.getId(),mailVo);
        }
        //异步发送邮件
        asyncReminderService.customSendStudentOfferNotice(mailVoMap,SecureUtil.getStaffInfo(), rejectEmailAgentIds);

    }

    /**
     * 批量删除
     * @param batchStudentOfferNoticeDto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchDeleteStudentOfferNotice(BatchStudentOfferNoticeDto batchStudentOfferNoticeDto) {
        List<Long> studentOfferNoticeIds = batchStudentOfferNoticeDto.getStudentOfferNoticeIds();
        if (GeneralTool.isNotEmpty(studentOfferNoticeIds)){
            List<StudentOfferNotice> studentOfferNotices = studentOfferNoticeIds.stream().map(s -> {
                StudentOfferNotice studentOfferNotice = new StudentOfferNotice();
                studentOfferNotice.setId(s);
                studentOfferNotice.setStatus(-2);
                utilService.setUpdateInfo(studentOfferNotice);
                return studentOfferNotice;
            }).collect(Collectors.toList());
            if (GeneralTool.isNotEmpty(studentOfferNotices)){
                this.updateBatchById(studentOfferNotices);
            }
        }
    }

    /**
     * 批量匹配
     * @param batchStudentOfferNoticeDto
     */
    @Override
    public void batchMatchStudentOfferNotice(BatchStudentOfferNoticeDto batchStudentOfferNoticeDto) {
//        if (GeneralTool.isEmpty(batchStudentOfferNoticeDto.getOpeningTimeStart())||GeneralTool.isEmpty(batchStudentOfferNoticeDto.getOpeningTimeEnd())){
//            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
//        }

        Boolean flag = getRedis.setNx(CacheNames.BATCH_MATCH_EMAIL_KEY+SecureUtil.getStaffId(), 1, 30L);
        if (flag){

            try {
                List<Long> studentOfferNoticeIds = batchStudentOfferNoticeDto.getStudentOfferNoticeIds();
                List<StudentOfferNotice> studentOfferNotices = this.baseMapper.selectBatchIds(studentOfferNoticeIds);
                if (GeneralTool.isEmpty(studentOfferNotices)){
                    throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
                }
                List<Long> studentIds = studentOfferNotices.stream().map(StudentOfferNotice::getFkStudentId).collect(Collectors.toList());
                Long fkReportSaleId = studentOfferNotices.get(0).getFkReportSaleId();
                if (GeneralTool.isEmpty(fkReportSaleId)){
                    throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
                }
                ReportSaleDto saleVo = reportCenterClient.getReportSaleById(fkReportSaleId).getData();
                if (GeneralTool.isEmpty(saleVo)){
                    throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
                }
                String reportQuery = saleVo.getReportQuery();
                EmailStatisticsDto statisticsVo = JSONObject.parseObject(reportQuery, EmailStatisticsDto.class);
                if (GeneralTool.isEmpty(statisticsVo.getOpeningTimeStart())||GeneralTool.isEmpty(statisticsVo.getOpeningTimeEnd())){
                    throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
                }
                EmailStatisticsDto emailStatisticsDto = new EmailStatisticsDto();
                emailStatisticsDto.setFkCompanyId(statisticsVo.getFkCompanyId());
                emailStatisticsDto.setStudentIds(studentIds);
                emailStatisticsDto.setOpeningTimeStart(statisticsVo.getOpeningTimeStart());
                emailStatisticsDto.setOpeningTimeEnd(statisticsVo.getOpeningTimeEnd());

                String emailStatisticsVoString = JSONObject.toJSONString(emailStatisticsDto);
                ReportSaleDto reportSaleVo = new ReportSaleDto();
                reportSaleVo.setReportQuery(emailStatisticsVoString);
                reportSaleVo.setReportStatus(1);
                reportSaleVo.setReportTime(new Date());
                reportSaleVo.setReportName(ProjectKeyEnum.BATCH_MATCH_EMAIL.value);
                reportSaleVo.setFkUserId(SecureUtil.getStaffId());
                utilService.setCreateInfo(reportSaleVo);
                Long id = reportCenterClient.addReportSaleCommon(reportSaleVo).getData();
                StaffInfo staffInfo = SecureUtil.getStaffInfo();
                String locale = SecureUtil.getLocale();
                Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
                asyncStatisticsService.asyncEmailStatistics(headerMap,staffInfo,locale,SecureUtil.getCountryIds(), emailStatisticsDto,id,CacheNames.BATCH_MATCH_EMAIL_KEY+SecureUtil.getStaffId());
            }catch (Exception e){
                getRedis.del(CacheNames.BATCH_MATCH_EMAIL_KEY+SecureUtil.getStaffId());
                throw new GetServiceException(e.getMessage());
            }
        }else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("batch_matching_in_progress"));
        }

    }


    @Override
    public StudentOfferNoticeListVo updateStudentOfferNotice(StudentOfferNoticeUpdateDto studentOfferNoticeUpdateDto) {
        StudentOfferNotice studentOfferNotice = BeanCopyUtils.objClone(studentOfferNoticeUpdateDto, StudentOfferNotice::new);
        if(GeneralTool.isNotEmpty(studentOfferNoticeUpdateDto.getFkStaffIdFrom())){
            StaffVo staffVo = permissionCenterClient.getStaffById(studentOfferNoticeUpdateDto.getFkStaffIdFrom()).getData();
            if (GeneralTool.isNotEmpty(staffVo)&&GeneralTool.isNotEmpty(staffVo.getEmail())){
                studentOfferNotice.setFromEmail(staffVo.getEmail());
            }
        }
        utilService.setUpdateInfo(studentOfferNotice);
        updateById(studentOfferNotice);
        return BeanCopyUtils.objClone(studentOfferNotice, StudentOfferNoticeListVo::new);
    }

    /**
     * 二级列表
     *
     * @param studentOfferNoticeListItemDto
     * @return
     */
    @Override
    public List<StudentOfferNoticeListItemVo> getDatasItem(StudentOfferNoticeListItemDto studentOfferNoticeListItemDto) {

        StudentOfferNotice studentOfferNotice = getById(studentOfferNoticeListItemDto.getStudentOfferNoticeId());
        Long fkReportSaleId = studentOfferNotice.getFkReportSaleId();
        if (GeneralTool.isEmpty(fkReportSaleId)){
            return Collections.emptyList();
        }
        ReportSaleDto reportSaleVo = reportCenterClient.getReportSaleById(fkReportSaleId).getData();
        if (GeneralTool.isEmpty(reportSaleVo)){
            return Collections.emptyList();
        }
        String reportQuery = reportSaleVo.getReportQuery();
        EmailStatisticsDto emailStatisticsDto = JSONObject.parseObject(reportQuery, EmailStatisticsDto.class);
        emailStatisticsDto.setStudentIds(Lists.newArrayList(studentOfferNotice.getFkStudentId()));
        List<Long> staffFollowerIds = verifyStudentOfferItemUtils.getStaffFollowerIds(SecureUtil.getStaffId());

        Boolean isBd = studentOfferService.getIsBd(SecureUtil.getStaffId());
        List<EmailStatisticsOfferItemVo> emailStatisticsOfferItemVos =this.baseMapper.getEmailStatisticsOfferItem(emailStatisticsDto,staffFollowerIds,
                SecureUtil.getCountryIds(),
                SecureUtil.getIsStudentOfferItemFinancialHiding(),
                SecureUtil.getStaffInfo().getIsStudentAdmin(),
                isBd,
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds());

        if (GeneralTool.isEmpty(emailStatisticsOfferItemVos)){
            return Collections.emptyList();
        }

        Set<Long> institutionIds = emailStatisticsOfferItemVos.stream().map(EmailStatisticsOfferItemVo::getFkInstitutionId).collect(Collectors.toSet());
        Set<Long> institutionCourseIds = emailStatisticsOfferItemVos.stream().map(EmailStatisticsOfferItemVo::getFkInstitutionCourseId).collect(Collectors.toSet());
        Set<Long> agentIds = emailStatisticsOfferItemVos.stream().map(EmailStatisticsOfferItemVo::getFkAgentId).collect(Collectors.toSet());
        Set<Long> stepIds = emailStatisticsOfferItemVos.stream().map(EmailStatisticsOfferItemVo::getFkStudentOfferItemStepId).collect(Collectors.toSet());
        Set<Long> countryIds = emailStatisticsOfferItemVos.stream().map(EmailStatisticsOfferItemVo::getFkAreaCountryId).collect(Collectors.toSet());

        //学校名称
        Map<Long, String> institutionNameMap = institutionCenterClient.getInstitutionNamesByIds(institutionIds).getData();
        //课程名称
        Map<Long, String> courseNameMap = institutionCenterClient.getInstitutionCourseNamesByIds(institutionCourseIds).getData();
        //代理名称
        Map<Long, String> agentNameMap = agentService.getAgentNamesByIds(agentIds);
        //步骤名称
        List<StudentOfferItemStep> studentOfferItemSteps = studentOfferItemStepMapper.selectBatchIds(stepIds);
        Map<Long, String> stepNameMap = studentOfferItemSteps.stream().collect(Collectors.toMap(StudentOfferItemStep::getId, StudentOfferItemStep::getStepName));
        //步骤名称
        Map<Long, String> countryNameMap = institutionCenterClient.getCountryFullNamesByIds(countryIds).getData();

        //查询项目成员
        Map<Long, List<StudentRoleAndStaffVo>> staffByTableIds = Maps.newHashMap();
        Set<Long> offerIds = emailStatisticsOfferItemVos.stream().map(EmailStatisticsOfferItemVo::getFkStudentOfferId).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(offerIds)){
            staffByTableIds = studentProjectRoleStaffService.getRoleAndStaffByTableIds(offerIds);
        }


        Map<Long, List<StudentRoleAndStaffVo>> finalProjectRoleStaffMap = staffByTableIds;
        List<StudentOfferNoticeListItemVo> studentOfferNoticeListItemVos = BeanCopyUtils.copyListProperties(emailStatisticsOfferItemVos, StudentOfferNoticeListItemVo::new,
                (emailStatisticsOfferItemVo, studentOfferNoticeListItemVo)->{
                if (GeneralTool.isNotEmpty(emailStatisticsOfferItemVo.getFkInstitutionId())){
                    studentOfferNoticeListItemVo.setInstitutionId(emailStatisticsOfferItemVo.getFkInstitutionId());
                    if (emailStatisticsOfferItemVo.getFkInstitutionId().equals(-1L)){
                        studentOfferNoticeListItemVo.setInstitutionName(emailStatisticsOfferItemVo.getOldInstitutionName());
                    }else {
                        studentOfferNoticeListItemVo.setInstitutionName(institutionNameMap.get(emailStatisticsOfferItemVo.getFkInstitutionId()));
                    }
                }
                if (GeneralTool.isNotEmpty(emailStatisticsOfferItemVo.getFkInstitutionCourseId())){
                    studentOfferNoticeListItemVo.setCourseId(emailStatisticsOfferItemVo.getFkInstitutionCourseId());
                    if (emailStatisticsOfferItemVo.getFkInstitutionCourseId().equals(-1L)){
                        studentOfferNoticeListItemVo.setCourseName(emailStatisticsOfferItemVo.getOldCourseCustomName());
                    }else {
                        studentOfferNoticeListItemVo.setCourseName(courseNameMap.get(emailStatisticsOfferItemVo.getFkInstitutionCourseId()));
                    }
                }
                if (GeneralTool.isNotEmpty(emailStatisticsOfferItemVo.getFkAgentId())){
                    studentOfferNoticeListItemVo.setAgentId(emailStatisticsOfferItemVo.getFkAgentId());
                    studentOfferNoticeListItemVo.setAgentName(agentNameMap.get(emailStatisticsOfferItemVo.getFkAgentId()));
                }
                if (GeneralTool.isNotEmpty(emailStatisticsOfferItemVo.getDeferOpeningTime())){
                    studentOfferNoticeListItemVo.setOpeningTime(emailStatisticsOfferItemVo.getDeferOpeningTime());
                }
                if (GeneralTool.isNotEmpty(emailStatisticsOfferItemVo.getFkStudentOfferItemStepId())){
                    studentOfferNoticeListItemVo.setStepId(emailStatisticsOfferItemVo.getFkStudentOfferItemStepId());
                    studentOfferNoticeListItemVo.setStepName(stepNameMap.get(emailStatisticsOfferItemVo.getFkStudentOfferItemStepId()));
                }

                if (GeneralTool.isNotEmpty(emailStatisticsOfferItemVo.getFkStudentOfferId())){
                    List<StudentRoleAndStaffVo> studentRoleAndStaffVos = finalProjectRoleStaffMap.get(emailStatisticsOfferItemVo.getFkStudentOfferId());
                    if(GeneralTool.isNotEmpty(studentRoleAndStaffVos)){
                        studentOfferNoticeListItemVo.setStudentRoleAndStaffList(studentRoleAndStaffVos);
                    }
                }
                if (GeneralTool.isNotEmpty(emailStatisticsOfferItemVo.getFkAreaCountryId())){
                    studentOfferNoticeListItemVo.setCountryId(emailStatisticsOfferItemVo.getFkAreaCountryId());
                    studentOfferNoticeListItemVo.setCountryName(countryNameMap.get(emailStatisticsOfferItemVo.getFkAreaCountryId()));
                }
                });
        return studentOfferNoticeListItemVos;
    }

    @Override
    public Integer getEmailStatisticsStatus() {
        ReportSaleDto reportStatistics = reportCenterClient.getLastReportSaleVoByReportNameAndUserId(ProjectKeyEnum.EMAIL_STATISTICS.value,SecureUtil.getStaffId()).getData();
        ReportSaleDto reportMatch = reportCenterClient.getLastReportSaleVoByReportNameAndUserId(ProjectKeyEnum.BATCH_MATCH_EMAIL.value,SecureUtil.getStaffId()).getData();
        if (GeneralTool.isNotEmpty(reportStatistics)&&GeneralTool.isNotEmpty(reportMatch)){
            if (reportStatistics.getGmtCreate().compareTo(reportMatch.getGmtCreate())>=0){
                return reportStatistics.getReportStatus();
            }else {
                return reportMatch.getReportStatus();
            }
        }
        else if (GeneralTool.isNotEmpty(reportStatistics)&&GeneralTool.isEmpty(reportMatch)){
            return reportStatistics.getReportStatus();
        }
        else if (GeneralTool.isEmpty(reportStatistics)&&GeneralTool.isNotEmpty(reportMatch)){
            return reportMatch.getReportStatus();
        }
        return 0;
    }

    @Override
    public void sendAllWithOneClick() {
        StudentOfferNoticeListDto studentOfferNoticeListDto = new StudentOfferNoticeListDto();
        studentOfferNoticeListDto.setReportStatus(0);
        List<StudentOfferNoticeListVo> studentOfferNotices = getStudentOfferNotices(studentOfferNoticeListDto, null);
        if (GeneralTool.isNotEmpty(studentOfferNotices)){
            List<Long> ids = studentOfferNotices.stream().map(StudentOfferNoticeListVo::getId).collect(Collectors.toList());
            if(GeneralTool.isNotEmpty(ids)){
                BatchStudentOfferNoticeDto batchStudentOfferNoticeDto = new BatchStudentOfferNoticeDto();
                batchStudentOfferNoticeDto.setStudentOfferNoticeIds(ids);
                batchSendStudentOfferNotice(batchStudentOfferNoticeDto);
            }
        }
    }


}
