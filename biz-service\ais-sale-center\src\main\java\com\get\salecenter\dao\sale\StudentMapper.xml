<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.StudentMapper">


    <select id="getStudentIds" parameterType="java.lang.String" resultType="long">
        select ip.id
        from m_student ip
        where ip.name like
            concat("%",#{name},"%")
            or
            concat(ip.name,"（",ip.last_name," ",ip.first_name,"）")
            like
            concat("%",#{name},"%")
        ORDER BY
            LENGTH(ip.name)
    </select>
    <select id="getStudentNameById" parameterType="java.lang.Long" resultType="string">
        select ip.name
        from m_student ip
        where ip.id = #{id}
    </select>

    <select id="getStudentNationalityByIds" parameterType="java.lang.Long" resultType="com.get.salecenter.entity.Student">
    select ip.id,ip.fk_area_country_id_nationality
    from m_student ip
    <if test="ids!=null and ids.size>0">
        where ip.id in
       <foreach collection="ids" item="item" open="(" separator="," close=")">
           #{item}
       </foreach>
    </if>
  </select>

    <select id="getStudentNationalityById" parameterType="java.lang.Long" resultType="java.lang.Long">
        select ip.fk_area_country_id_nationality
        from m_student ip
        where ip.id = #{id}
    </select>

    <select id="getCountByProviderAndCourse" resultType="java.lang.Integer">
        select COUNT(*) from m_student ms where ms.id
        in(select mso.fk_student_id from m_student_offer mso where mso.id
        in (select msoi.fk_student_offer_id from m_student_offer_item msoi LEFT JOIN r_student_offer_item_step rsois
        on rsois.fk_student_offer_item_id = msoi.id
        where msoi.fk_institution_provider_id = #{contractFormula.providerId}
        <if test="courseIds != null and courseIds.size()>0">
            and msoi.fk_institution_course_id in
            <foreach collection="courseIds" item="courseId" index="index" open="(" separator="," close=")">
                #{courseId}
            </foreach>
        </if>
        and rsois.fk_student_offer_item_step_id =
        (select id from u_student_offer_item_step where step_key = 'STEP_OFFER_SELECTION')
        <if test="contractFormula.startTime!=null and contractFormula.startTime!=''">
            and DATE_FORMAT(rsois.gmt_create,'%Y-%m-%d') <![CDATA[>= ]]>
            DATE_FORMAT(#{contractFormula.startTime},'%Y-%m-%d')
        </if>
        <if test="contractFormula.endTime!=null and contractFormula.endTime!=''">
            and DATE_FORMAT(rsois.gmt_create,'%Y-%m-%d') <![CDATA[<= ]]>
            DATE_FORMAT(#{contractFormula.endTime},'%Y-%m-%d')
        </if>
        ))
    </select>
    <select id="getCountByCountry" resultType="java.lang.Integer">
        select count(*) from m_student_offer_item msoi
        INNER JOIN m_student AS ms ON ms.id = msoi.fk_student_id
        where msoi.fk_student_offer_id in
        (select DISTINCT mso.id from m_student_offer mso LEFT JOIN m_student ms on mso.fk_student_id = ms.id where 1=1
        <if test="companyIds != null and companyIds.size()>0">
            AND ms.fk_company_id IN
            <foreach collection="companyIds" item="companyId" index="index" open="(" separator="," close=")">
                #{companyId,jdbcType=BIGINT}
            </foreach>
        </if>
        )
        and msoi.status = 1
        and year(ms.gmt_create)=#{year}
        <if test="!isStudentOfferItemFinancialHiding">
            AND IFNULL(msoi.is_follow_hidden, 0) != 1
        </if>
        <if test="id!=null">
            and msoi.fk_area_country_id =#{id}
        </if>
        GROUP BY msoi.fk_area_country_id
    </select>

    <select id="getCountByCountryTwo" resultType="com.get.salecenter.vo.SelItem">
        select msoi.fk_area_country_id as keyId,count(DISTINCT msoi.id) as val from m_student_offer_item msoi
        INNER JOIN m_student AS ms ON ms.id = msoi.fk_student_id
        <if test="!isStudentAdmin">
            INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.studentAuthority"/>
            ) z ON ms.id=z.id
        </if>
        where msoi.fk_student_offer_id in
        (select DISTINCT mso.id from m_student_offer mso LEFT JOIN m_student ms on mso.fk_student_id = ms.id where 1=1
        <if test="companyIds != null and companyIds.size()>0">
            AND ms.fk_company_id IN
            <foreach collection="companyIds" item="companyId" index="index" open="(" separator="," close=")">
                #{companyId,jdbcType=BIGINT}
            </foreach>
        </if>
        )
        and msoi.status = 1
        and year(msoi.gmt_create)=#{year} and msoi.is_follow = 0
        <if test="!isStudentOfferItemFinancialHiding">
            AND IFNULL(msoi.is_follow_hidden, 0) != 1
        </if>
        <if test="fkAreaCountryIds!=null">
            and msoi.fk_area_country_id IN
            <foreach collection="fkAreaCountryIds" item="aid" separator="," open="(" close=")">
                #{aid}
            </foreach>
        </if>
        GROUP BY msoi.fk_area_country_id
    </select>

    <select id="getCountByState" resultType="com.get.salecenter.vo.SelCountVo">
        select a.fk_area_state_id as id,count(DISTINCT ms.id) as num from m_student ms

        <if test="!isStudentAdmin">
        INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.studentAuthority"/>
        ) z ON ms.id=z.id
        </if>

        INNER JOIN m_student_offer_item AS msoi ON msoi.fk_student_id = ms.id AND msoi.status = 1
        INNER JOIN m_agent AS a ON a.id = msoi.fk_agent_id
        <where>
        <if test="areaCountryIds != null and areaCountryIds.size()>0">
            AND msoi.fk_area_country_id IN
            <foreach collection="areaCountryIds" item="areaCountryId" open="(" separator="," close=")">
                #{areaCountryId}
            </foreach>
        </if>
        AND year(ms.gmt_create)=#{year} and msoi.is_follow = 0
        <if test="ids != null and ids.size()>0">
            AND a.fk_area_state_id IN
            <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                #{id,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size()>0">
            AND ms.fk_company_id IN
            <foreach collection="companyIds" item="companyId" index="index" open="(" separator="," close=")">
                #{companyId,jdbcType=BIGINT}
            </foreach>
        </if>
            <if test="!isStudentOfferItemFinancialHiding">
                AND IFNULL(msoi.is_follow_hidden, 0) != 1
            </if>
        </where>
        group by a.fk_area_state_id
    </select>


    <select id="getStudentsByTargetCountryAndStudentIds" resultType="com.get.salecenter.vo.StudentVo">
        select
        distinct ms.id,ms.fk_company_id,ms.num,ms.name,ms.last_name,ms.first_name,ms.gender,ms.birthday,
        ms.mobile,ms.tel,ms.email,ms.fk_area_country_id,ms.fk_area_state_id,
        ms.fk_area_city_id,ms.fk_area_country_name,ms.fk_area_state_name,
        ms.fk_area_city_name,ms.zipcode,ms.contact_address,

        ms.education_level_type,
        ms.education_major,
        ms.fk_area_country_id_education,
        ms.fk_area_state_id_education,
        ms.fk_area_city_id_education,
        ms.fk_area_country_name_education,
        ms.fk_area_state_name_education,
        ms.fk_area_city_name_education,
        ms.institution_type_education,

        ms.education_level_type2,
        ms.education_major2,
        ms.fk_area_country_id_education2,
        ms.fk_area_state_id_education2,
        ms.fk_area_city_id_education2,
        ms.fk_area_country_name_education2,
        ms.fk_area_state_name_education2,
        ms.fk_area_city_name_education2,
        ms.fk_institution_id_education2,
        ms.fk_institution_name_education2,
        ms.high_school_test_type,
        ms.high_school_test_score,
        ms.standard_test_type,
        ms.standard_test_score,
        ms.english_test_type,
        ms.english_test_score,ms.remark,ms.condition_type,ms.id_gea,ms.id_iae,ms.gmt_create,ms.gmt_create_user,
        ms.gmt_modified,ms.gmt_modified_user,ms.fk_institution_id_education,ms.fk_institution_name_education,ms.mobile_area_code,ms.tel_area_code
        from m_student ms
        left join m_student_offer mso on ms.id = mso.fk_student_id
        left join m_student_offer_item msoi on mso.id = msoi.fk_student_offer_id
        left join u_student_offer_item_step AS usois ON usois.id = msoi.fk_student_offer_item_step_id
        left join r_student_offer_item_step rsois ON rsois.fk_student_offer_item_id = msoi.id AND
        rsois.fk_student_offer_item_step_id = usois.id
        where mso.status = 1 and msoi.status = 1 and
        ms.id IN
        <foreach collection="studentIds" item="studentId" index="index" open="(" separator="," close=")">
            #{studentId,jdbcType=BIGINT}
        </foreach>
        <if test="failureReasonId!=null">
            and msoi.fk_enrol_failure_reason_id =#{failureReasonId}
        </if>
        <if test="targetCountryId!=null">
            and msoi.fk_area_country_id =#{targetCountryId}
        </if>
        <if test="targetCountryIdList!=null">
            and msoi.fk_area_country_id in
            <foreach collection="targetCountryIdList" item="targetCountryId" index="index" open="(" separator=","
                     close=")">
                #{targetCountryId}
            </foreach>
        </if>
        <if test="isDeferEntrance!=null and isDeferEntrance == true ">
            and msoi.is_defer_entrance =#{isDeferEntrance}
        </if>
        <if test="isDeferEntrance!=null and isDeferEntrance == false ">
            and msoi.is_defer_entrance is null or msoi.is_defer_entrance =#{isDeferEntrance}
        </if>
        <if test="beginOpeningTime != null">
            and DATE_FORMAT(msoi.defer_opening_time,'%Y-%m-%d') &gt;= DATE_FORMAT(#{beginOpeningTime},'%Y-%m-%d')
        </if>
        <if test="endOpeningTime != null">
            and DATE_FORMAT(msoi.defer_opening_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{endOpeningTime},'%Y-%m-%d')
        </if>
        <if test="statusBeginTime!=null">
            and DATE_FORMAT(rsois.gmt_create,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{statusBeginTime},'%Y-%m-%d')
        </if>
        <if test="statusEndTime!=null">
            and DATE_FORMAT(rsois.gmt_create,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{statusEndTime},'%Y-%m-%d')
        </if>
        <if test="states!=null">
            AND msoi.fk_student_offer_item_step_id in
            <foreach collection="states" item="state" index="index" open="(" separator="," close=")">
                #{state}
            </foreach>
        </if>
    </select>
    <select id="getStudentNumByOfferItemStepOrder" resultType="com.get.salecenter.vo.StudentVo">
        SELECT
        s.*
        FROM
        m_student AS s
        INNER JOIN m_student_offer AS so ON so.fk_student_id = s.id AND so.status = 1
        INNER JOIN m_student_offer_item AS soi ON soi.fk_student_offer_id = so.id
        INNER JOIN u_student_offer_item_step AS usois ON usois.id = soi.fk_student_offer_item_step_id
        WHERE soi.status = 1
        AND soi.fk_area_country_id  IN
        <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">
            #{countryId}
        </foreach>
        <if test="companyId != null">
            AND s.fk_company_id = #{companyId}
        </if>
        <if test="staffId != null">
            AND so.fk_staff_id = #{staffId}
        </if>
        <if test="areaCountryIds != null and areaCountryIds.size()>0">
            AND so.fk_area_country_id IN
            <foreach collection="areaCountryIds" item="areaCountryId" open="(" separator="," close=")">
                #{areaCountryId}
            </foreach>
        </if>
        <if test="stepOrder != null">
            AND usois.step_order = #{stepOrder}
        </if>
        AND (
        1=1
        <if test="studentIds != null and  studentIds.size() > 0">
            AND s.id IN
            <foreach collection="studentIds" item="studentId" index="index" open="(" separator="," close=")">
                #{studentId}
            </foreach>
        </if>
        <if test="userNames != null and userNames.size() > 0">
            OR s.gmt_create_user IN
            <foreach collection="userNames" item="userName" open="(" separator="," close=")">
                #{userName}
            </foreach>
        </if>
        )
        <if test="beginTime != null">
            AND DATE_FORMAT( soi.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{beginTime}, '%Y-%m-%d' )
        </if>
        <if test="endTime != null">
            AND DATE_FORMAT( soi.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{endTime}, '%Y-%m-%d' )
        </if>
        GROUP BY s.id
    </select>

    <select id="getStudentNumByFailureReasonKey" resultType="com.get.salecenter.vo.StudentVo">
        SELECT
        s.*
        FROM
        m_student AS s
        INNER JOIN m_student_offer AS so ON so.fk_student_id = s.id AND so.status = 1
        INNER JOIN m_student_offer_item AS soi ON soi.fk_student_offer_id = so.id AND soi.status = 1
        -- INNER JOIN r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = soi.id
        -- AND NOT EXISTS ( SELECT 1 FROM r_student_offer_item_step WHERE fk_student_offer_item_id =
        -- rsois.fk_student_offer_item_id AND gmt_create > rsois.gmt_create )
        INNER JOIN u_student_offer_item_step AS usois ON usois.id = soi.fk_student_offer_item_step_id
        INNER JOIN u_enrol_failure_reason AS efr ON efr.id = soi.fk_enrol_failure_reason_id
        WHERE usois.step_key = 'STEP_FAILURE'
        AND soi.fk_area_country_id  IN
        <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">
            #{countryId}
        </foreach>
        <if test="reasonId != null">
            AND efr.id = #{reasonId}
        </if>
        <if test="companyId != null">
            AND s.fk_company_id = #{companyId}
        </if>
        <if test="staffId != null">
            AND (so.fk_staff_id = #{staffId}
            OR so.fk_staff_id IS NULL)
        </if>
        <if test="areaCountryIds != null and areaCountryIds.size()>0">
            AND so.fk_area_country_id IN
            <foreach collection="areaCountryIds" item="areaCountryId" open="(" separator="," close=")">
                #{areaCountryId}
            </foreach>
        </if>
        <if test="beginTime != null">
            AND ( DATE_FORMAT( soi.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{beginTime}, '%Y-%m-%d' ))
        </if>
        <if test="endTime != null">
            AND ( DATE_FORMAT( soi.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{endTime}, '%Y-%m-%d' ))
        </if>
        AND (
        1=1
        <if test="studentIds != null">
            AND s.id IN
            <foreach collection="studentIds" item="studentId" index="index" open="(" separator=","
                     close=")">
                #{studentId}
            </foreach>
            OR s.id IS NULL
        </if>
        <if test="userNames != null and userNames.size() > 0">
            OR s.gmt_create_user IN
            <foreach collection="userNames" item="userName" open="(" separator="," close=")">
                #{userName}
            </foreach>
        </if>
        )
        GROUP BY s.id
    </select>
    <select id="getStudentNumByDeferEntranceTime" resultType="com.get.salecenter.vo.StudentVo">
        SELECT s.* FROM m_student AS s
        INNER JOIN m_student_offer AS so ON so.fk_student_id = s.id AND so.status = 1
        INNER JOIN m_student_offer_item AS soi ON soi.fk_student_offer_id = so.id AND soi.status = 1
        -- INNER JOIN m_student_offer_item_defer_entrance_time AS defer ON defer.fk_student_offer_item_id = soi.id
        WHERE soi.is_defer_entrance = 1
        AND soi.fk_area_country_id  IN
        <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">
            #{countryId}
        </foreach>
        <if test="companyId != null">
            AND s.fk_company_id = #{companyId}
        </if>
        <if test="staffId != null">
            AND (so.fk_staff_id = #{staffId}
            OR so.fk_staff_id IS NULL)
        </if>
        <if test="areaCountryIds != null and areaCountryIds.size()>0">
            AND so.fk_area_country_id IN
            <foreach collection="areaCountryIds" item="areaCountryId" open="(" separator="," close=")">
                #{areaCountryId}
            </foreach>
        </if>
        AND (
        1=1
        <if test="studentIds != null">
            AND s.id IN
            <foreach collection="studentIds" item="studentId" index="index" open="(" separator=","
                     close=")">
                #{studentId}
            </foreach>
            OR s.id IS NULL
        </if>
        <if test="userNames != null and userNames.size() > 0">
            OR s.gmt_create_user IN
            <foreach collection="userNames" item="userName" open="(" separator="," close=")">
                #{userName}
            </foreach>
        </if>
        )
        <if test="beginTime != null">
            AND DATE_FORMAT( soi.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{beginTime}, '%Y-%m-%d' )
        </if>
        <if test="endTime != null">
            AND DATE_FORMAT( soi.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{endTime}, '%Y-%m-%d' )
        </if>
        GROUP BY s.id
    </select>
    <select id="getStudentByNameAndBirthday" resultType="com.get.salecenter.entity.Student">
        SELECT
        *
        FROM
        m_student
        WHERE
        1=1
        <if test="studentDto.name != null and studentDto.name !=''">
            and name = #{studentDto.name}
        </if>
        <if test="studentDto.birthday != null">
            and DATE_FORMAT( birthday, '%Y-%m-%d' ) = DATE_FORMAT( #{studentDto.birthday}, '%Y-%m-%d' )
        </if>
        <if test="studentDto.id != null and studentDto.id !=''">
            and id != #{studentDto.id}
        </if>
    </select>

    <select id="getCountByCity" resultType="java.lang.Integer">
        select count(DISTINCT ms.id) from m_student ms
        <if test="!isStudentAdmin">
            INNER JOIN (
                <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.studentAuthority"/>
            ) z ON ms.id=z.id
        </if>
        INNER JOIN m_student_offer_item AS msoi ON msoi.fk_student_id = ms.id AND msoi.status = 1
        INNER JOIN m_agent AS a ON a.id = msoi.fk_agent_id
        where year(ms.gmt_create)=#{year} and msoi.is_follow = 0
        <if test="id!=null">
            and a.fk_area_city_id =#{id}
        </if>
        <if test="companyIds != null and companyIds.size()>0">
            AND ms.fk_company_id IN
            <foreach collection="companyIds" item="companyId" index="index" open="(" separator="," close=")">
                #{companyId,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="!isStudentOfferItemFinancialHiding">
            AND IFNULL(msoi.is_follow_hidden, 0) != 1
        </if>
    </select>
    <select id="getStudentPassportMum" resultType="java.lang.String">
        select passport_num from m_student
        <where>
            <if test="studentId!=null">
                and id = #{studentId}
            </if>
        </where>
    </select>
    <select id="getStudentBirthDay" resultType="java.util.Date">
        select DATE_FORMAT(birthday, "%Y-%m-%d") from m_student
        <where>
            <if test="studentId!=null">
                and id = #{studentId}
            </if>
        </where>
    </select>
    <select id="getStudentZhEnName" resultType="java.lang.String">
        select CASE WHEN IFNULL(CONCAT(s.first_name,s.last_name), '') = '' THEN s.name ELSE CONCAT(s.name, '（',
        CONCAT(s.first_name," ",s.last_name), '）') END fullName from m_student s
        <where>
            <if test="studentId!=null">
                and id = #{studentId}
            </if>
        </where>
    </select>
    <select id="getIsExitStudent" resultType="com.get.salecenter.vo.StudentVo">
        SELECT ms.id,ms.`name`,ms.mobile,ms.birthday,ms.passport_num,ms.email FROM `m_student` ms
        WHERE 1=1 and ms.fk_company_id=#{companyId}
        <if test="studentName!=null and studentName!=''">
            and ms.`name` = #{studentName}
        </if>
        <if test="birthday!=null and birthday!=''">
            and   ms.birthday = #{birthday}
        </if>
        <if test="mobile!=null and mobile!='' ">
            and ms.mobile =#{mobile}
        </if>
        <if test="passpost!=null and passpost!='' ">
            and ms.passport_num=#{passpost}
        </if>
        <if test="email!=null and email!='' ">
            and ms.email =#{email}
        </if>
        <if test="id!=null and id !=''">
            and ms.id=#{id}
        </if>
        limit 1
    </select>
    <select id="getStudentAgents" resultType="com.get.salecenter.vo.AgentVo">
        select  rsa.fk_student_id as fkStudentId, ras.fk_agent_id,ras.fk_staff_id as fkStaffId,ma.`name` from r_agent_staff ras
        join r_student_agent
        rsa on ras.fk_agent_id=rsa.fk_agent_id
        join m_agent ma on ma.id=rsa.fk_agent_id
        where rsa.fk_student_id in
        <foreach collection="fkStudentId" item="ids" index="index" open="(" separator="," close=")">
            #{ids}
        </foreach>
        and ras.is_active=1 and rsa.is_active=1
    </select>
    <select id="getStudentZhEnNameByStudentIds" resultType="com.get.salecenter.vo.StudentVo">
        select
        id,CASE WHEN IFNULL(CONCAT(s.first_name,s.last_name), '') = '' THEN s.name ELSE CONCAT(s.name, '（',
        CONCAT(s.first_name," ",s.last_name), '）') END fullName from m_student s
        <where>
            <if test="studentIds !=null and studentIds.size()>0">
                and id in
                <foreach collection="studentIds" item="studentId" index="index" open="(" separator="," close=")">
                    #{studentId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getStudents" resultType="com.get.salecenter.vo.StudentVo">
        <include refid="getStudent"/>
    </select>

    <select id="getExportStudents" resultType="com.get.salecenter.vo.StudentVo">
        <include refid="getStudent"/>
    </select>

    <select id="getStudentOfferItemNumCount" resultType="java.lang.Integer">
        SELECT SUM(sub.studentOfferNum) AS totalStudentOfferCount
        FROM (
        <include refid="getStudent"/>
             ) sub
    </select>

    <sql id="getStudent">
        SELECT
        <if test="studentDto.pageNumber!=null">
            a.*
            ,b.short_name AS companyName, l.areaCountrySuccessfulNames
        </if>

        <if test="studentDto.pageNumber==null">
            count(DISTINCT a.id) as total_count
            ,count( DISTINCT c.id ) AS studentOfferNum
        </if>
        <if test="isExport">
            ,m.max_step_order, m.min_step_order, k.reasonName
        </if>

        FROM ais_sale_center.m_student a
        LEFT JOIN ais_permission_center.m_company b ON a.fk_company_id=b.id
        <if test="studentDto.pageNumber==null">
        LEFT JOIN ais_sale_center.m_student_offer c ON a.id=c.fk_student_id
        </if>
        <!-- 首页柱状图专用 -->
        <if test="currentStudentApplicationStatusStatisticsDto !=null">
        INNER JOIN (
        SELECT
        ms.id
        FROM
        m_student AS ms
        INNER JOIN m_student_offer AS mso ON mso.fk_student_id = ms.id
        INNER JOIN m_student_offer_item AS msoi ON mso.id = msoi.fk_student_offer_id
        INNER JOIN r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = msoi.id
        INNER JOIN u_student_offer_item_step AS usois ON usois.id = rsois.fk_student_offer_item_step_id
        LEFT JOIN ais_sale_center.m_agent g ON msoi.fk_agent_id = g.id
        LEFT JOIN ais_institution_center.r_area_region_state r on g.fk_area_state_id = r.fk_area_state_id
        LEFT JOIN r_staff_bd_code AS rsbc ON rsbc.fk_staff_id = mso.fk_staff_id
        INNER JOIN (
        SELECT
        ms.id,
        rsois.fk_student_offer_item_step_id,
        MIN( rsois.gmt_create ) AS minGmtCreate
        FROM
        m_student AS ms
        INNER JOIN m_student_offer_item AS msoi ON ms.id = msoi.fk_student_id AND msoi.status = 1
        INNER JOIN r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = msoi.id
        INNER JOIN u_student_offer_item_step AS usois ON usois.id = rsois.fk_student_offer_item_step_id
        <!-- 学习计划权限 -->
        INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.itemPermissionSql"/>
        ) z ON msoi.id=z.id
            <!--权限国家过滤-->
            <where>
                <choose>
                    <when test="fkAreaCountryIds != null and fkAreaCountryIds.size()>0">
                        AND msoi.fk_area_country_id IN
                        <foreach collection="fkAreaCountryIds" item="fkAreaCountryId" open="(" separator="," close=")">
                            #{fkAreaCountryId}
                        </foreach>
                    </when>
                    <otherwise>
                        AND msoi.fk_area_country_id = -1
                    </otherwise>
                </choose>
                <!--报考国家-->
                <if test="currentStudentApplicationStatusStatisticsDto.countryIds != null and currentStudentApplicationStatusStatisticsDto.countryIds.size()>0">
                    AND msoi.fk_area_country_id IN
                    <foreach collection="currentStudentApplicationStatusStatisticsDto.countryIds" item="countryId" open="(" separator="," close=")">
                        #{countryId}
                    </foreach>
                </if>
                AND ( (
                msoi.fk_student_offer_item_step_id = 9
                AND rsois.fk_student_offer_item_step_id != 8
                ) OR ( msoi.fk_student_offer_item_step_id != 9 ) )
            </where>
        GROUP BY
        ms.id,
        rsois.fk_student_offer_item_step_id
        ) a ON a.id = ms.id
        AND a.fk_student_offer_item_step_id = rsois.fk_student_offer_item_step_id
        AND a.minGmtCreate = rsois.gmt_create
        <if test="currentStudentApplicationStatusStatisticsDto.fkProjectMemberIds !=null and currentStudentApplicationStatusStatisticsDto.fkProjectMemberIds.size()>0">
            INNER JOIN (
            SELECT fk_table_id FROM ais_sale_center.s_student_project_role_staff
            WHERE fk_table_name='m_student_offer' AND is_active=1 AND fk_staff_id IN
            <foreach collection="currentStudentApplicationStatusStatisticsDto.fkProjectMemberIds" item="memberId" index="index" open="(" separator="," close=")">
                #{memberId}
            </foreach>
            GROUP BY fk_table_id
            ) b1 ON mso.id = b1.fk_table_id
        </if>

        <!-- 学生绑定bd -->
        <if test="(currentStudentApplicationStatusStatisticsDto.bdNameOrCode!=null and currentStudentApplicationStatusStatisticsDto.bdNameOrCode!='' )
                         or (currentStudentApplicationStatusStatisticsDto.fkBdId != null and currentStudentApplicationStatusStatisticsDto.fkBdId != '')">
            INNER JOIN (
            SELECT mso1.fk_student_id
            FROM ais_sale_center.m_student_offer AS mso1
            LEFT JOIN ais_permission_center.m_staff c ON mso1.fk_staff_id=c.id
            LEFT JOIN ais_sale_center.r_staff_bd_code d ON c.id = d.fk_staff_id
            WHERE mso1.status=1 AND (
            <if test="currentStudentApplicationStatusStatisticsDto.bdNameOrCode!=null and currentStudentApplicationStatusStatisticsDto.bdNameOrCode!=''">
                LOWER(c.`name`) like concat("%",#{currentStudentApplicationStatusStatisticsDto.bdNameOrCode},"%")
                OR LOWER(c.name_en) like concat("%",#{currentStudentApplicationStatusStatisticsDto.bdNameOrCode},"%")
                OR LOWER(d.bd_code) like concat("%",#{currentStudentApplicationStatusStatisticsDto.bdNameOrCode},"%")
            </if>
            <if test ="currentStudentApplicationStatusStatisticsDto.fkBdId!=null and currentStudentApplicationStatusStatisticsDto.fkBdId != ''" >
                OR d.fk_staff_id = #{currentStudentApplicationStatusStatisticsDto.fkBdId}
            </if>
            )
            )bd ON bd.fk_student_id = ms.id
        </if>


        WHERE
        msoi.is_follow=0
        AND msoi.`status`=1
        and msoi.fk_student_offer_item_step_id is not null
        AND mso.status IN (1,2,3)
        AND IFNULL(mso.fk_agent_id,0) NOT IN(11195,13103,13282)
            <if test="currentStudentApplicationStatusStatisticsDto.stepId!=null and currentStudentApplicationStatusStatisticsDto.stepId != ''">
                AND a.fk_student_offer_item_step_id = #{currentStudentApplicationStatusStatisticsDto.stepId}
            </if>
            <if test="currentStudentApplicationStatusStatisticsDto.stepIdList!=null and currentStudentApplicationStatusStatisticsDto.stepIdList.size()>0">
                AND a.fk_student_offer_item_step_id IN
                <foreach collection="currentStudentApplicationStatusStatisticsDto.stepIdList" item="stepId" index="index" open="(" separator="," close=")">
                    #{stepId}
                </foreach>
            </if>

        <if test="!isStudentOfferItemFinancialHiding">
            AND IFNULL(msoi.is_follow_hidden, 0)!=1
        </if>

            <if test="currentStudentApplicationStatusStatisticsDto.beginTime != null">
                AND DATE_FORMAT( a.minGmtCreate, '%Y-%m-%d' ) <![CDATA[>=]]>
                DATE_FORMAT(#{currentStudentApplicationStatusStatisticsDto.beginTime}, '%Y-%m-%d' )
            </if>
            <if test="currentStudentApplicationStatusStatisticsDto.endTime != null">
                AND DATE_FORMAT( a.minGmtCreate, '%Y-%m-%d' ) <![CDATA[<=]]>
                DATE_FORMAT(#{currentStudentApplicationStatusStatisticsDto.endTime}, '%Y-%m-%d' )
            </if>


        <if test="currentStudentApplicationStatusStatisticsDto.companyId!=null and currentStudentApplicationStatusStatisticsDto.companyId != ''">
            AND ms.fk_company_id = #{currentStudentApplicationStatusStatisticsDto.companyId}
        </if>

        <!--代理国家-->
        <if test="currentStudentApplicationStatusStatisticsDto.fkAreaCountryIdAgent!=null and currentStudentApplicationStatusStatisticsDto.fkAreaCountryIdAgent != ''">
            AND g.fk_area_country_id = #{currentStudentApplicationStatusStatisticsDto.fkAreaCountryIdAgent}
        </if>
        <!--代理省份过滤-->
        <if test="currentStudentApplicationStatusStatisticsDto.fkAreaStateIdAgent!=null and currentStudentApplicationStatusStatisticsDto.fkAreaStateIdAgent.size()>0">
            AND g.fk_area_state_id IN
            <foreach collection="currentStudentApplicationStatusStatisticsDto.fkAreaStateIdAgent" item="fkAreaStateIdAgent" open="(" separator="," close=")">
                #{fkAreaStateIdAgent}
            </foreach>
        </if>
        <!-- 绑定bd大区 -->
        <if test="currentStudentApplicationStatusStatisticsDto.fkAreaRegionId!=null ">
            AND  FIND_IN_SET(#{currentStudentApplicationStatusStatisticsDto.fkAreaRegionId}, rsbc.fk_area_region_id) > 0
        </if>

        group by ms.id
        )d ON d.id = a.id
        </if>
        <!-- 首页业绩柱状图sql结束 -->

        <!-- IAE首页柱状图专用 -->
        <if test="bdStudentStatisticalComparisonDto != null">
        INNER JOIN (

            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.bdStatisticalJumpSql"></include>

            )e ON e.fk_student_id = a.id
        </if>
        <!-- IAE首页柱状图sql结束 -->

        <!--主要过滤sql-->
        INNER JOIN (

        SELECT DISTINCT a.id
        FROM (
        SELECT a.id
        -- 第一层
        FROM ais_sale_center.m_student a

        -- 过滤：学生当前绑定代理名称+名字备注+原公司
        <if test="(studentDto.agentName!=null and studentDto.agentName!='') or (studentDto.fkAgentIds != null and studentDto.fkAgentIds.size()>0) or (studentDto.fkAreaStateIdAgentList != null and studentDto.fkAreaStateIdAgentList.size()>0)">
            INNER JOIN (
            SELECT a.fk_student_id
            FROM ais_sale_center.r_student_agent a
            LEFT JOIN ais_sale_center.m_agent b ON a.fk_agent_id=b.id
            WHERE a.is_active=1
            -- 代理名称+名字备注+原公司过滤
            <if test="studentDto.agentName!=null and studentDto.agentName!=''">
                AND (LOWER(b.`name`) like concat("%",#{studentDto.agentName},"%") OR LOWER(b.name_note) like concat("%",#{studentDto.agentName},"%") OR concat(LOWER(b.`name`),"（",b.name_note,"）") like concat("%",#{studentDto.agentName},"%"))
            </if>
            <if test="studentDto.fkAgentIds != null and studentDto.fkAgentIds.size()>0">
                AND b.id IN
                <foreach collection="studentDto.fkAgentIds" item="agentId" open="(" separator="," close=")">
                    #{agentId}
                </foreach>
            </if>
            -- 代理省份过滤
            <if test="studentDto.fkAreaStateIdAgentList!=null and studentDto.fkAreaStateIdAgentList.size()>0">
                AND b.fk_area_state_id IN
                <foreach collection="studentDto.fkAreaStateIdAgentList" item="fkAreaStateIdAgent" open="(" separator="," close=")">
                    #{fkAreaStateIdAgent}
                </foreach>
            </if>
            GROUP BY a.fk_student_id
            ) a1 ON a.id=a1.fk_student_id
        </if>


--         过滤学生备注
        <if test="studentDto.remarkComment != null and studentDto.remarkComment != ''">
            LEFT JOIN s_comment a3 on a3.fk_table_id = a.id and a3.fk_table_name = 'm_student'
        </if>

        -- 第二层
        LEFT JOIN ais_sale_center.m_student_offer b ON a.id=b.fk_student_id

        -- 过滤：BD中英名字/编号/大区
        <if test="(studentDto.bdNameOrCode != null and studentDto.bdNameOrCode!='') or
                  (studentDto.fkBdIds != null and studentDto.fkBdIds.size()>0) or
                  (studentDto.fkAreaRegionId != null)">
            INNER JOIN (
            SELECT mso1.id
            FROM ais_sale_center.m_student_offer AS mso1
            LEFT JOIN ais_permission_center.m_staff c ON mso1.fk_staff_id=c.id
            LEFT JOIN ais_sale_center.r_staff_bd_code d ON c.id = d.fk_staff_id
            WHERE mso1.status=1
            <if test="(studentDto.bdNameOrCode!=null and studentDto.bdNameOrCode!='' ) or (studentDto.fkBdIds!=null and studentDto.fkBdIds.size()>0)">
                AND (
                <if test="studentDto.bdNameOrCode!=null and studentDto.bdNameOrCode!=''">
                    LOWER(c.`name`) like concat("%",#{studentDto.bdNameOrCode},"%")
                    OR LOWER(c.name_en) like concat("%",#{studentDto.bdNameOrCode},"%")
                    OR LOWER(d.bd_code) like concat("%",#{studentDto.bdNameOrCode},"%")
                </if>
                <if test="studentDto.fkBdIds!=null and studentDto.fkBdIds.size()>0">
                    <if test="studentDto.bdNameOrCode!=null and studentDto.bdNameOrCode!=''">OR</if>
                    d.fk_staff_id IN
                    <foreach collection="studentDto.fkBdIds" item="fkBdId" open="(" separator="," close=")">
                        #{fkBdId}
                    </foreach>
                </if>
                )
            </if>
            <if test="studentDto.fkAreaRegionId != null">
                AND FIND_IN_SET(#{studentDto.fkAreaRegionId},d.fk_area_region_id)>0
            </if>
            GROUP BY mso1.id
            ) a2 ON b.id=a2.id
        </if>

        --  项目成员的过滤条件
        <if test="studentDto.fkProjectMemberId !=null and studentDto.fkProjectMemberId.size()>0">
            INNER JOIN (
            SELECT fk_table_id FROM ais_sale_center.s_student_project_role_staff
            WHERE fk_table_name='m_student_offer' AND is_active=1 AND fk_staff_id IN
            <foreach collection="studentDto.fkProjectMemberId" item="memberId" index="index" open="(" separator="," close=")">
                #{memberId}
            </foreach>
            GROUP BY fk_table_id
            ) b1 ON b.id = b1.fk_table_id
        </if>

        -- 获取学校的kpi值，积分的过滤
        <if test="studentDto.institutionIsKpi !=null and studentDto.institutionIsKpi!=''">
            INNER JOIN(
            SELECT a.fk_student_id from (
            SELECT  a.fk_student_id,
            CASE
            WHEN MAX( CASE ukip.score WHEN 3 THEN 3 WHEN 2 THEN 2 ELSE 1 END ) >= 3 THEN
            3
            WHEN MAX( CASE ukip.score WHEN 3 THEN 3 WHEN 2 THEN 2 ELSE 1 END ) >= 2 THEN
            2 ELSE 1
            END AS kpiScore
            FROM m_student_offer_item a
            LEFT JOIN (
            SELECT a.fk_student_offer_item_id, MIN(a.gmt_create) dp_time FROM r_student_offer_item_step a
            LEFT JOIN u_student_offer_item_step b ON a.fk_student_offer_item_step_id=b.id
            WHERE b.step_key ='STEP_DEPOSIT_PAID'
            GROUP BY a.fk_student_offer_item_id
            ) c ON a.id=c.fk_student_offer_item_id
            LEFT JOIN u_kpi_institution_provider ukip ON ukip.fk_institution_provider_id = a.fk_institution_provider_id
            <if test="studentDto.fkInstitutionIdsExcluding != null and studentDto.fkInstitutionIdsExcluding != ''">
                and NOT FIND_IN_SET(a.fk_institution_id,#{studentDto.fkInstitutionIdsExcluding}) >0
            </if>
            WHERE a.`status`=1  AND a.is_follow=0
            <if test="!isStudentOfferItemFinancialHiding">
                AND IFNULL(a.is_follow_hidden, 0)!=1
            </if>
            AND c.dp_time>=#{studentDto.dpTimeStart} AND c.dp_time &lt;=#{studentDto.dpTimeEnd}
            GROUP BY a.fk_student_id
            ) a where 1=1
            <if test="studentDto.institutionIsKpi !=null and studentDto.institutionIsKpi!=''">
                <if test="studentDto.institutionIsKpi==1">
                    AND a.kpiScore=1
                </if>
                <if test="studentDto.institutionIsKpi==2">
                    AND a.kpiScore=2
                </if>
                <if test="studentDto.institutionIsKpi==3">
                    AND a.kpiScore=3
                </if>
            </if>
            )a4 ON a.id=a4.fk_student_id
        </if>

        -- 第三层
        <if test="
      (studentDto.currentState!=null and studentDto.currentState.size()>0) or
      (studentDto.majorLevelIds!=null and studentDto.majorLevelIds.size()>0) or
      (studentDto.oldCourseMajorLevelNames!=null and studentDto.oldCourseMajorLevelNames.size()>0) or
      (studentDto.courseTypeGroupIds!=null and studentDto.courseTypeGroupIds.size()>0) or
      (studentDto.oldCourseTypeGroupNames!=null and studentDto.oldCourseTypeGroupNames.size()>0) or
      (studentDto.unionApplyCountryIds!=null and studentDto.unionApplyCountryIds.size()>0) or
      (studentDto.targetCountryIdList!=null and studentDto.targetCountryIdList.size()>0) or
      studentDto.beginOpeningTime !=null or studentDto.endOpeningTime !=null or
      studentDto.itemBeginTime !=null or studentDto.itemEndTime !=null or
      (studentDto.isDeferEntrance !=null and studentDto.isDeferEntrance !='') or
      (studentDto.failureReasonId!=null and studentDto.failureReasonId!='') or
      (studentDto.failureReasonIds!=null and studentDto.failureReasonIds.size()>0) or
      (studentDto.institutionName!=null and studentDto.institutionName!='') or
      (studentDto.courseName!=null and studentDto.courseName!='') or
      (studentDto.majorLevelId !=null and studentDto.majorLevelId !='') or

      (studentDto.changeStepId!=null and studentDto.changeStepId.size>0 and (studentDto.statusBeginTime !=null or studentDto.statusEndTime !=null)) or
      (studentDto.groupName !=null and studentDto.groupName !='') or
      (studentDto.fkInstitutionGroupIds != null and studentDto.fkInstitutionGroupIds.size()>0)
      (studentDto.channelName !=null and studentDto.channelName !='') or
      (studentDto.channelNames !=null and studentDto.channelNames.size()>0) or
      (studentDto.dpTimeStart !=null and studentDto.dpTimeEnd !=null) or
      (studentDto.newAppStatus !=null) or
      (studentDto.institutionIsKpi !=null and studentDto.institutionIsKpi!='') or
      (studentDto.succTimeStart !=null and studentDto.succTimeEnd!=null) or
      (studentDto.newTimeStart !=null and studentDto.newTimeEnd!=null) or
      (studentDto.itemAgentName!=null and studentDto.itemAgentName!='') or
      (studentDto.fkInstitutionProviderId!=null and studentDto.fkInstitutionProviderId!='') or
      (studentDto.itemAgentIds!=null and studentDto.itemAgentIds.size>0) or
      (studentDto.fkAreaStateIdAgentList!=null and studentDto.fkAreaStateIdAgentList.size>0) or
      (studentDto.fkAreaStateIdAgent!=null) or
      (studentDto.fkAreaCountryIdAgent !=null and studentDto.fkAreaCountryIdAgent !='') or
      (studentDto.businessType==1) or
      (studentDto.beginOpeningTime != null) or
      (studentDto.endOpeningTime != null) or

      (studentDto.bdNameOrCode != null and studentDto.bdNameOrCode!='') or
                  (studentDto.fkBdIds != null and studentDto.fkBdIds.size()>0) or
                  (studentDto.fkAreaRegionId != null) or

       (studentDto.fkAreaStateIdAgentItemList != null and studentDto.fkAreaStateIdAgentItemList.size()>0) or
       (studentDto.fkAreaCountryIdItemAgent !=null and studentDto.fkAreaCountryIdItemAgent !='') or
       (studentDto.fkAreaCountryIdItemAgent != null and studentDto.fkAreaCountryIdItemAgent.size()>0)
      ">
            INNER JOIN ais_sale_center.m_student_offer_item c ON b.id=c.fk_student_offer_id and c.`status` = 1
            INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.itemPermissionSql"/>
            ) z ON c.id=z.id
            <!-- 显示2022年的学生 -->
            <if test="staffId == 1247 or staffId ==  1245">
                INNER JOIN (
                SELECT
                id
                FROM
                m_student_offer_item
                WHERE
                DATE_FORMAT(defer_opening_time,'%Y-%m-%d') <![CDATA[>=]]> DATE_FORMAT(#{beginOpenTime},'%Y-%m-%d')
                AND DATE_FORMAT(defer_opening_time,'%Y-%m-%d') <![CDATA[<=]]> DATE_FORMAT(#{endOpenTime},'%Y-%m-%d')
                GROUP BY id
                )2022Student  ON 2022Student.id = c.id
            </if>
            <if test="!isStudentOfferItemFinancialHiding">
                AND IFNULL(c.is_follow_hidden, 0)!=1
            </if>
        </if>

        <!-- 过滤：申请绑定的代理的 国家 + 州省 -->
        <if test="(studentDto.fkAreaStateIdAgentItemList != null and studentDto.fkAreaStateIdAgentItemList.size()>0 ) or
            (studentDto.fkAreaCountryIdItemAgent != null and studentDto.fkAreaCountryIdItemAgent.size()>0)">
            INNER JOIN (
            SELECT msoi1.id
            FROM ais_sale_center.m_student_offer_item msoi1
            LEFT JOIN ais_sale_center.m_agent b ON msoi1.fk_agent_id=b.id
            WHERE msoi1.status=1
            <!-- 过滤代理国家 -->
            <if test="studentDto.fkAreaCountryIdItemAgent !=null and studentDto.fkAreaCountryIdItemAgent !=''">
                AND b.fk_area_country_id = #{studentDto.fkAreaCountryIdItemAgent}
            </if>
            <!-- 代理省份过滤 -->
            <if test="studentDto.fkAreaStateIdAgentItemList!=null and studentDto.fkAreaStateIdAgentItemList.size()>0">
                AND b.fk_area_state_id IN
                <foreach collection="studentDto.fkAreaStateIdAgentItemList" item="fkAreaStateIdAgent" open="(" separator="," close=")">
                    #{fkAreaStateIdAgent}
                </foreach>
            </if>
            GROUP BY msoi1.id
            ) agnetItem ON c.id = agnetItem.id
        </if>

        <if
                test="(studentDto.itemAgentName!=null and studentDto.itemAgentName!='')
                or (studentDto.itemAgentIds!=null and studentDto.itemAgentIds.size>0)
                or (studentDto.fkAreaStateIdAgentList!=null and studentDto.fkAreaStateIdAgentList.size>0)
                or (studentDto.fkAreaStateIdAgent!=null)
                or (studentDto.fkAreaCountryIdAgent !=null and studentDto.fkAreaCountryIdAgent !='')
">
            LEFT JOIN  m_agent f ON c.fk_agent_id = f.id
        </if>

        -- 变更步骤过滤
        <if test="studentDto.changeStepId!=null and studentDto.changeStepId.size>0 and (studentDto.statusBeginTime !=null or studentDto.statusEndTime !=null)">
            INNER JOIN (
            SELECT fk_student_offer_item_id FROM ais_sale_center.r_student_offer_item_step
            WHERE fk_student_offer_item_step_id in
            <foreach collection="studentDto.changeStepId" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
            <if test="studentDto.statusBeginTime !=null">
                and DATE_FORMAT(gmt_create,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{studentDto.statusBeginTime},'%Y-%m-%d')
            </if>
            <if test="studentDto.statusEndTime !=null">
                and DATE_FORMAT(gmt_create,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{studentDto.statusEndTime},'%Y-%m-%d')
            </if>
            GROUP BY fk_student_offer_item_id
            ) c1 ON c.id=c1.fk_student_offer_item_id
        </if>


        -- 集团过滤(gea关系表过滤、iae fk_institution_provider_id过滤)
        <if test="(studentDto.groupName !=null and studentDto.groupName !='') or
        (studentDto.fkInstitutionGroupIds != null and studentDto.fkInstitutionGroupIds.size()>0)">
            INNER JOIN (
            SELECT i.id
            FROM ais_sale_center.m_student_offer_item i
            LEFT JOIN ais_institution_center.r_institution_provider_institution a on a.fk_institution_id = i.fk_institution_id
            LEFT JOIN ais_institution_center.m_institution_provider b ON a.fk_institution_provider_id=b.id
            LEFT JOIN ais_institution_center.m_institution_group c ON b.fk_institution_group_id=c.id
            <where>
            <if test="studentDto.groupName != null and studentDto.groupName !=''">
               AND LOWER(c.name) like concat("%",#{studentDto.groupName},"%") OR LOWER(c.name_chn) like concat("%",#{studentDto.groupName},"%")
            </if>
            <if test="studentDto.fkInstitutionGroupIds != null and studentDto.fkInstitutionGroupIds.size()>0">
               AND i.fk_institution_provider_id=-1 AND c.id IN
                <foreach collection="studentDto.fkInstitutionGroupIds" item="fkInstitutionGroupId" open="(" separator="," close=")">
                    #{fkInstitutionGroupId}
                </foreach>
            </if>
            </where>
            UNION
            SELECT i.id
            FROM ais_sale_center.m_student_offer_item i
            LEFT JOIN ais_institution_center.m_institution_provider b ON i.fk_institution_provider_id=b.id
            LEFT JOIN ais_institution_center.m_institution_group c ON b.fk_institution_group_id=c.id
            <where>
                <if test="studentDto.groupName != null and studentDto.groupName !=''">
                    AND LOWER(c.name) like concat("%",#{studentDto.groupName},"%") OR LOWER(c.name_chn) like concat("%",#{studentDto.groupName},"%")
                </if>
                <if test="studentDto.fkInstitutionGroupIds != null and studentDto.fkInstitutionGroupIds.size()>0">
                    AND c.id IN
                    <foreach collection="studentDto.fkInstitutionGroupIds" item="fkInstitutionGroupId" open="(" separator="," close=")">
                        #{fkInstitutionGroupId}
                    </foreach>
                </if>
            </where>
            ) c2 ON c.id=c2.id
        </if>

        -- 渠道过滤
        <if test="studentDto.channelName !=null and studentDto.channelName !=''">
            INNER JOIN ais_institution_center.m_institution_channel c3 ON c.fk_institution_channel_id=c3.id
            AND (LOWER(c3.name) like concat("%",#{studentDto.channelName},"%") OR LOWER(c3.name_chn) like concat("%",#{studentDto.channelName},"%"))
        </if>

        -- 渠道过滤 多选
        <if test="studentDto.channelNames !=null and studentDto.channelNames.size()>0">
            INNER JOIN ais_institution_center.m_institution_channel c3 ON c.fk_institution_channel_id=c3.id
            and (
            <foreach collection="studentDto.channelNames" item="channelName" index="index" separator="OR">
                LOWER(c3.name) like concat("%",#{channelName},"%") OR LOWER(c3.name_chn) like concat("%",#{channelName},"%")
            </foreach>
            )
        </if>


        -- 是否延迟入学过滤条件之一
        <if test="studentDto.isDeferEntrance !=null and studentDto.isDeferEntrance !=''">
            LEFT JOIN (
            SELECT fk_student_offer_item_id, MAX(defer_entrance_time) max_defer_entrance_time
            FROM ais_sale_center.m_student_offer_item_defer_entrance_time
            GROUP BY fk_student_offer_item_id
            ) c8 ON c.id=c8.fk_student_offer_item_id
        </if>

        <!-- 获取dp_time，积分，业绩，周报，排行的跳转 -->
        <if test="studentDto.dpTimeStart !=null and studentDto.dpTimeEnd !=null and (studentDto.jumpState ==1 or studentDto.jumpState ==2)">
            LEFT JOIN (
            SELECT a.fk_student_offer_item_id, MIN(a.gmt_create) dp_time
            FROM ais_sale_center.r_student_offer_item_step a
            LEFT JOIN ais_sale_center.u_student_offer_item_step b ON a.fk_student_offer_item_step_id=b.id
            WHERE b.step_key IN
            <choose>
                <when test="studentDto.fkCompanyId != null and studentDto.fkCompanyId == 3">
                    <foreach collection="studentDto.iaeConfirmationStatisticsStepList"
                             item="iaeConfirmationStatisticsStep" open="(" separator="," close=")">
                        #{iaeConfirmationStatisticsStep}
                    </foreach>
                </when>
                <otherwise>
                    <foreach collection="studentDto.geaConfirmationStatisticsStepList"
                             item="geaConfirmationStatisticsStep" open="(" separator="," close=")">
                        #{geaConfirmationStatisticsStep}
                    </foreach>
                </otherwise>
            </choose>
            GROUP BY a.fk_student_offer_item_id
            ) c9 ON c.id=c9.fk_student_offer_item_id
        </if>


        <!--  获取succ_time，业绩，周报，排行的跳转 -->
        <if test="(studentDto.succTimeStart !=null and studentDto.succTimeEnd !=null and (studentDto.jumpState == 1 or studentDto.jumpState == 2)) or studentDto.jumpState == 10">
                INNER JOIN (
                    SELECT a.fk_student_offer_item_id, MIN(a.gmt_create) succ_time
                    FROM ais_sale_center.m_student_offer_item m
                    LEFT JOIN ais_sale_center.r_student_offer_item_step a ON a.fk_student_offer_item_id = m.id
                    LEFT JOIN ais_sale_center.u_student_offer_item_step b ON a.fk_student_offer_item_step_id=b.id
                    WHERE b.step_key IN
                    <choose>
                        <when test="studentDto.fkCompanyId != null and studentDto.fkCompanyId == 3">
                            <foreach collection="studentDto.iaeSuccessStatisticsStepList" item="iaeSuccessStatisticsStep" open="(" separator="," close=")">
                                #{iaeSuccessStatisticsStep}
                            </foreach>
                        </when>
                        <otherwise>
                            <foreach collection="studentDto.geaSuccessStatisticsStepList" item="geaSuccessStatisticsStep" open="(" separator="," close=")">
                                #{geaSuccessStatisticsStep}
                            </foreach>
                        </otherwise>
                    </choose>
                    AND m.fk_student_offer_item_step_id = 8

                    GROUP BY a.fk_student_offer_item_id
                ) c11 ON c.id=c11.fk_student_offer_item_id
            </if>

        -- #获取new_time，业绩，周报，排行的跳转
        <if test="studentDto.newTimeStart !=null and studentDto.newTimeEnd !=null">
            LEFT JOIN (
             SELECT a.fk_student_offer_item_id, MIN(a.gmt_create) new_time FROM ais_sale_center.r_student_offer_item_step a
             LEFT JOIN ais_sale_center.u_student_offer_item_step b ON a.fk_student_offer_item_step_id=b.id
             WHERE b.step_key IN('STEP_NEW_APP')
             GROUP BY a.fk_student_offer_item_id
            ) c12 ON c.id=c12.fk_student_offer_item_id
        </if>

        -- 获取业务类型
        <if test="studentDto.businessTypeFlag !=null">
            inner join(
            select DISTINCT ms.id from ais_sale_center.m_student ms
            left join ais_sale_center.m_student_offer mo on ms.id = mo.fk_student_id AND mo.status = 1
            left join ais_sale_center.m_student_offer_item moi on ms.id = moi.fk_student_id AND moi.status = 1
            left join ais_sale_center.m_student_insurance msisa on ms.id = msisa.fk_student_id AND msisa.status = 1
            left join ais_sale_center.m_student_accommodation msa on ms.id = msa.fk_student_id AND msa.status = 1
            left join ais_sale_center.m_student_service_fee mssf on ms.id = mssf.fk_student_id AND mssf.status = 1
            where 1=1
            <if test="studentDto.businessTypeFlag == 1">
                AND mo.id IS NULL AND moi.id IS NULL AND msisa.id IS NULL AND msa.id IS NULL AND mssf.id IS NULL
            </if>
            <if test="studentDto.businessTypeFlag == 2">
                AND mo.id IS NOT NULL AND moi.id IS NULL AND msisa.id IS NULL AND msa.id IS NULL AND mssf.id IS NULL
            </if>
            <if test="studentDto.businessTypeFlag == 3">
                AND moi.id IS NOT NULL AND msisa.id IS NULL AND msa.id IS NULL AND mssf.id IS NULL
            </if>
            <if test="studentDto.businessTypeFlag == 4">
                AND moi.id IS NULL AND msisa.id IS NOT NULL AND msa.id IS NULL AND mssf.id IS NULL
            </if>
            <if test="studentDto.businessTypeFlag == 5">
                AND moi.id IS NULL AND msisa.id IS NULL AND msa.id IS NOT NULL AND mssf.id IS NULL
            </if>
            <if test="studentDto.businessTypeFlag == 6">
                AND moi.id IS NULL AND msisa.id IS NULL AND msa.id IS NULL AND mssf.id IS NOT NULL
            </if>
            <if test="studentDto.businessTypeFlag == 7">
                AND moi.id IS NOT NULL
            </if>
            <if test="studentDto.businessTypeFlag == 8">
                AND msisa.id IS NOT NULL
            </if>
            <if test="studentDto.businessTypeFlag == 9">
                AND msa.id IS NOT NULL
            </if>
            <if test="studentDto.businessTypeFlag == 10">
                AND mssf.id IS NOT NULL
            </if>
            )c13 on c13.id = a.id
        </if>

        WHERE 1=1

        <if test="studentDto.itemAgentName !=null and studentDto.itemAgentName!=''">
            AND (f.`name` like concat("%",#{studentDto.itemAgentName},"%") OR f.name_note like concat("%",#{studentDto.itemAgentName},"%"))
        </if>

        <if test="studentDto.itemAgentIds != null and studentDto.itemAgentIds.size()>0">
            AND f.id IN
            <foreach collection="studentDto.itemAgentIds" item="agentIds" open="(" separator="," close=")">
                #{agentIds}
            </foreach>
        </if>

        <!-- 业务学校 -->
        <if test="institutionIds !=null and institutionIds.size() > 0">
            AND EXISTS (SELECT 1 FROM m_student_offer_item msoi1 where msoi1.fk_student_id = a.id and msoi1.status = 1 and msoi1.fk_institution_id in
            <foreach collection="institutionIds" item="institutionId" open="(" separator="," close=")">
                #{institutionId}
            </foreach>
            )
        </if>

        <if test="studentDto.fkAreaStateIdAgentList != null and studentDto.fkAreaStateIdAgentList.size()>0">
            AND f.fk_area_state_id IN
            <foreach collection="studentDto.fkAreaStateIdAgentList" item="fkAreaStateIdAgent" open="(" separator="," close=")">
                #{fkAreaStateIdAgent}
            </foreach>
        </if>
        <if test="studentDto.fkAreaStateIdAgent!=null">
            AND f.fk_area_state_id = #{studentDto.fkAreaStateIdAgent}
        </if>
        <if test="studentDto.fkAreaCountryIdAgent !=null and studentDto.fkAreaCountryIdAgent !=''">
            AND f.fk_area_country_id = #{studentDto.fkAreaCountryIdAgent} -- #过滤代理国家
        </if>
        -- 第一层####################
        <if test="studentDto.fkCompanyIds !=null and studentDto.fkCompanyIds.size>0">
            AND a.fk_company_id in
              <foreach collection="studentDto.fkCompanyIds" item="item" separator="," open="(" close=")">
                  #{item}
              </foreach>
              -- 所属分公司
        </if>

        <if test="studentDto.name !=null and studentDto.name!=''">
            AND (
            REPLACE(CONCAT(LOWER(a.first_name),LOWER(a.last_name)),' ','') like concat('%',#{studentDto.name},'%')
            OR REPLACE(CONCAT(LOWER(a.last_name),LOWER(a.first_name)),' ','') like concat('%',#{studentDto.name},'%')
            OR REPLACE(LOWER(a.`name`),' ','') like concat('%',#{studentDto.name},'%')
            OR REPLACE(LOWER(a.last_name),' ','') like concat('%',#{studentDto.name},'%')
            OR REPLACE(LOWER(a.first_name),' ','') like concat('%',#{studentDto.name},'%')) -- 过滤学生中英文名字
        </if>

        <if test="studentDto.num !=null and studentDto.num !=''">
            AND a.num=#{studentDto.num} -- 过滤学生编号
        </if>
        <if test="studentDto.birthday!=null">
            AND DATE_FORMAT(a.birthday,'%Y-%m-%d') = DATE_FORMAT(#{studentDto.birthday},'%Y-%m-%d')
        </if>

        <if test="studentDto.isComplexEducation !=null">
            AND IFNULL(a.is_complex_education,0) = #{studentDto.isComplexEducation} -- 过滤是否复杂学历
        </if>

        <if test="studentDto.fkAreaCountryIdNationality !=null and studentDto.fkAreaCountryIdNationality !=''">
            AND a.fk_area_country_id_nationality=#{studentDto.fkAreaCountryIdNationality} -- 学生国籍
        </if>

        <if test="studentDto.fkAreaCountryIdNationalitys !=null and studentDto.fkAreaCountryIdNationalitys.size()>0">
            AND a.fk_area_country_id_nationality IN
            <foreach collection="studentDto.fkAreaCountryIdNationalitys" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
             -- 学生国籍
        </if>

        <if test="studentDto.tel !=null and studentDto.tel !=''">
            AND ( a.tel = #{studentDto.tel} OR a.mobile = #{studentDto.tel} )
        </if>

        <if test="studentDto.email !=null and studentDto.email !=''">
            AND LOWER(a.email) like concat("%",#{studentDto.email},"%") -- 过滤电邮
        </if>

        <if test="studentDto.beginTime !=null and studentDto.endTime !=null">
            <if test="studentDto.jumpState == 1">
                AND DATE_FORMAT(a.gmt_create,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{studentDto.beginTime},'%Y-%m-%d') -- 学生创建时间
                AND DATE_FORMAT(a.gmt_create,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{studentDto.endTime},'%Y-%m-%d') -- 学生创建时间
            </if>
            <if test="studentDto.jumpState == 2">
                AND DATE_FORMAT(a.gmt_create,'%Y-%m-%d %H:%i:%s') <![CDATA[>= ]]> DATE_FORMAT(#{studentDto.beginTime},'%Y-%m-%d %H:%i:%s') -- 学生创建时间
                AND DATE_FORMAT(a.gmt_create,'%Y-%m-%d %H:%i:%s') <![CDATA[<= ]]> DATE_FORMAT(#{studentDto.endTime},'%Y-%m-%d %H:%i:%s') -- 学生创建时间
            </if>
        </if>


        <if test="studentDto.fkInstitutionNameEducation!=null and studentDto.fkInstitutionNameEducation!=''">
            AND (
            <if test="studentDto.fkInstitutionIdsEducation!=null and studentDto.fkInstitutionIdsEducation.size()>0">
                a.fk_institution_id_education IN
                <foreach collection="studentDto.fkInstitutionIdsEducation" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
            </if>
            <if test="studentDto.fkInstitutionNameCnEducation!=null and studentDto.fkInstitutionNameCnEducation!=''">
                LOWER(a.fk_institution_name_education) like concat("%",#{studentDto.fkInstitutionNameCnEducation},"%") OR
            </if>
            LOWER(a.fk_institution_name_education) like concat("%",#{studentDto.fkInstitutionNameEducation},"%")) -- 国内毕业院校
        </if>

        <if test="studentDto.fkInstitutionNameEducation2!=null and studentDto.fkInstitutionNameEducation2!=''">
            AND (
            <if test="studentDto.fkInstitutionIdsEducation2!=null and studentDto.fkInstitutionIdsEducation2.size()>0">
                LOWER(a.fk_institution_id_education2) IN
                <foreach collection="studentDto.fkInstitutionIdsEducation2" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
            </if>
            LOWER(a.fk_institution_name_education2) like concat("%",#{studentDto.fkInstitutionNameEducation2},"%")) -- 国际毕业院校
        </if>
        <if test="studentDto.highSchoolTestType!=null and studentDto.highSchoolTestType!='' and studentDto.highSchoolTestScore!=null and studentDto.highSchoolTestScore!=''">
            AND a.high_school_test_type=#{studentDto.highSchoolTestType} AND a.high_school_test_score>=#{studentDto.highSchoolTestScore} -- 高中成绩类型+高中成绩
        </if>

        <if test="studentDto.standardTestType!=null and studentDto.standardTestType!='' and studentDto.standardTestScore!=null and studentDto.standardTestScore!=''">
            AND a.standard_test_type=#{studentDto.standardTestType} AND a.standard_test_score>=#{studentDto.standardTestScore} -- 本科成绩类型+本科成绩
        </if>

        -- 第二层####################

        -- 过滤国家权限
        AND (
            b.fk_student_id IS NULL
            OR (b.`status` IN (0,1,2,3)
        <if test="fkAreaCountryIds!=null and fkAreaCountryIds.size()>0">
            AND b.fk_area_country_id IN
            <foreach collection="fkAreaCountryIds" item="countryId" index="index" open="(" separator="," close=")">
                    #{countryId}
            </foreach>
        </if>
            )
        )

        <if test="studentDto.noAgentIds!=null and studentDto.noAgentIds.size()>0">
            AND IFNULL(b.fk_agent_id, 0) NOT IN
            <foreach collection="studentDto.noAgentIds" item="agentId" index="index" open="(" separator="," close=")">
                #{agentId}
            </foreach>
          -- 积分，业绩，周报，排行的跳转
        </if>
--         学生备注
        <if test="studentDto.remarkComment != null and studentDto.remarkComment != ''">
            AND (REPLACE(LOWER(a3.comment),' ','') like concat('%',#{studentDto.remarkComment},'%')
            OR REPLACE(LOWER(a.remark),' ','') like concat('%',#{studentDto.remarkComment},'%'))
        </if>

        -- 第三层####################
        <if test="
      (studentDto.currentState!=null and studentDto.currentState.size()>0) or
      (studentDto.majorLevelIds!=null and studentDto.majorLevelIds.size()>0) or
      (studentDto.oldCourseMajorLevelNames!=null and studentDto.oldCourseMajorLevelNames.size()>0) or
      (studentDto.courseTypeGroupIds!=null and studentDto.courseTypeGroupIds.size()>0) or
      (studentDto.oldCourseTypeGroupNames!=null and studentDto.oldCourseTypeGroupNames.size()>0) or
      (studentDto.unionApplyCountryIds!=null and studentDto.unionApplyCountryIds.size()>0) or
      (studentDto.targetCountryIdList!=null and studentDto.targetCountryIdList.size()>0) or
      studentDto.beginOpeningTime !=null or studentDto.endOpeningTime !=null or
      studentDto.itemBeginTime !=null or studentDto.itemEndTime !=null or
      (studentDto.isDeferEntrance !=null and studentDto.isDeferEntrance !='') or
      (studentDto.failureReasonId!=null and studentDto.failureReasonId!='') or
      (studentDto.failureReasonIds!=null and studentDto.failureReasonIds.size()>0) or
      (studentDto.institutionName!=null and studentDto.institutionName!='') or
      (studentDto.courseName!=null and studentDto.courseName!='') or
      (studentDto.majorLevelId !=null and studentDto.majorLevelId !='') or

      (studentDto.changeStepId!=null and studentDto.changeStepId.size>0 and (studentDto.statusBeginTime !=null or studentDto.statusEndTime !=null)) or
      (studentDto.groupName !=null and studentDto.groupName !='') or
      (studentDto.channelName !=null and studentDto.channelName !='') or
      (studentDto.channelNames !=null and studentDto.channelNames.size()>0) or
      (studentDto.dpTimeStart !=null and studentDto.dpTimeEnd !=null) or
      (studentDto.newAppStatus !=null) or
      (studentDto.fkInstitutionProviderId!=null and studentDto.fkInstitutionProviderId!='') or
      (studentDto.institutionIsKpi !=null and studentDto.institutionIsKpi!='') or
      (studentDto.succTimeStart !=null and studentDto.succTimeEnd!=null) or
      (studentDto.newTimeStart !=null and studentDto.newTimeEnd!=null) or
      (studentDto.itemAgentName!=null and studentDto.itemAgentName!='') or
      (studentDto.fkAreaStateIdAgentList!=null and studentDto.fkAreaStateIdAgentList.size>0) or
      (studentDto.businessType==1) or
      (studentDto.beginOpeningTime != null) or
      (studentDto.endOpeningTime != null)
      ">

        <!-- 显示2022年的学生 -->
        <if test="staffId != 1247 and staffId !=  1245">
            AND c.is_follow=0 -- 默认值 当加第三层表时默认出来
        </if>
            AND c.`status`=1 -- 默认值 当加第三层表时默认出来 默认为有效状态
        </if>
        <if test="studentDto.fkInstitutionProviderId!=null and studentDto.fkInstitutionProviderId!=''">
            AND c.fk_institution_provider_id = #{studentDto.fkInstitutionProviderId}
        </if>
        <if test="studentDto.currentState!=null and studentDto.currentState.size()>0">
            AND c.fk_student_offer_item_step_id IN
            <foreach collection="studentDto.currentState" item="currentState" index="index" open="(" separator="," close=")">
                #{currentState}
            </foreach>
            -- 过滤步骤状态
        </if>

        <if test="studentDto.targetCountryIdList!=null and studentDto.targetCountryIdList.size()>0">
            AND c.fk_area_country_id in
            <foreach collection="studentDto.targetCountryIdList" item="targetCountryId" index="index" open="(" separator="," close=")">
                #{targetCountryId}
            </foreach>
            -- 申请计划国家过滤
        </if>
        <if test="studentDto.unionApplyCountryIds!=null and studentDto.unionApplyCountryIds.size()>0">
            AND exists(
            select 1 from ais_sale_center.m_student_offer_item msoi
            WHERE msoi.fk_student_id = a.id
            and msoi.fk_area_country_id IN
            <foreach collection="studentDto.unionApplyCountryIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            group by msoi.fk_student_id
            having count(DISTINCT msoi.fk_area_country_id) = #{studentDto.unionApplyCountryCount}
            )
        </if>

        <if test="studentDto.beginOpeningTime != null">
            AND DATE_FORMAT(c.defer_opening_time,'%Y-%m-%d') <![CDATA[>=]]> DATE_FORMAT(#{studentDto.beginOpeningTime},'%Y-%m-%d')
        </if>
        <if test="studentDto.endOpeningTime != null">
            AND DATE_FORMAT(c.defer_opening_time,'%Y-%m-%d') <![CDATA[<=]]> DATE_FORMAT(#{studentDto.endOpeningTime},'%Y-%m-%d')
        </if>
        -- 开学时间过滤

        <if test="studentDto.newAppStatus!=null">
            <choose>
                <when test="studentDto.newAppStatus == -1">
                    AND c.new_app_status is null
                </when>
                <otherwise>
                    AND c.new_app_status = #{studentDto.newAppStatus}
                </otherwise>
            </choose>
        </if>

        -- 创建时间过滤
        <if test="studentDto.itemBeginTime !=null">
            and DATE_FORMAT(c.gmt_create,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{studentDto.itemBeginTime},'%Y-%m-%d')
        </if>
        <if test="studentDto.itemEndTime !=null">
            and DATE_FORMAT(c.gmt_create,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{studentDto.itemEndTime},'%Y-%m-%d')
        </if>

        <if test="studentDto.isDeferEntrance!=null and studentDto.isDeferEntrance!=''">
            <if test="studentDto.isDeferEntrance==1">
                AND c.is_defer_entrance=1 AND c8.max_defer_entrance_time > NOW() -- 过滤是否延迟入学（是）
            </if>
            <if test="studentDto.isDeferEntrance==0">
                AND ((c.is_defer_entrance=1 AND c8.max_defer_entrance_time &lt; NOW()) OR IFNULL(c.is_defer_entrance, 0)=0) -- 过滤是否延迟入学（否）
            </if>
        </if>

        <if test="studentDto.failureReasonId!=null and studentDto.failureReasonId!=''">
            AND c.fk_enrol_failure_reason_id=#{studentDto.failureReasonId}-- 过滤失败原因
        </if>
        -- 过滤失败原因
        <if test="studentDto.failureReasonIds!=null and studentDto.failureReasonIds.size()>0">
            AND c.fk_enrol_failure_reason_id in
            <foreach collection="studentDto.failureReasonIds" item="failureReasonId" index="index" open="(" separator="," close=")">
                #{failureReasonId}
            </foreach>
        </if>

        <!-- 学校名称过滤，根据接口模糊搜索，先获取学校Id -->
        <if test="studentDto.institutionIds!=null and studentDto.institutionIds.size()>0">
            AND c.fk_institution_id IN
            <foreach collection="studentDto.institutionIds" item="institutionId" index="index" open="(" separator="," close=")">
                #{institutionId}
            </foreach>
        </if>

        <if test="studentDto.courseName!=null and studentDto.courseName!=''">
            AND (
            <if test="studentDto.courseIds!=null and studentDto.courseIds.size()>0">
                c.fk_institution_course_id IN
                <foreach collection="studentDto.courseIds" item="courseId" index="index" open="(" separator="," close=")">
                    #{courseId}
                </foreach>
                OR
            </if>
            LOWER(c.old_course_custom_name) like concat("%",#{studentDto.courseName},"%")) -- 课程名称过滤，根据接口模糊搜索，先获取课程Id
        </if>

        -- 课程等级过滤
        <if test="(studentDto.majorLevelId !=null and studentDto.majorLevelId !='') or (studentDto.oldCourseMajorLevelName !=null and studentDto.oldCourseMajorLevelName!='')">
            AND (LOWER(c.old_course_major_level_name) LIKE concat("%",#{studentDto.oldCourseMajorLevelName},"%")
            <if test="studentDto.majorLevelId !=null and studentDto.majorLevelId !=''">
                OR FIND_IN_SET(#{studentDto.majorLevelId}, c.fk_institution_course_major_level_ids)>0
            </if>
            )
        </if>

        -- 课程等级过滤
        <if
                test="(studentDto.majorLevelIds !=null and studentDto.majorLevelIds.size()>0) or (studentDto.oldCourseMajorLevelNames !=null and studentDto.oldCourseMajorLevelNames.size()>0)">
            AND (
            <foreach collection="studentDto.oldCourseMajorLevelNames" item="oldCourseMajorLevelName" index="index"  separator="OR">
                LOWER(c.old_course_major_level_name) like concat("%",#{oldCourseMajorLevelName},"%")
            </foreach>

            <if test="studentDto.majorLevelIds !=null and studentDto.majorLevelIds.size()>0">
                or
                <foreach collection="studentDto.majorLevelIds" item="majorLevelId" index="index" separator="OR">
                    FIND_IN_SET(#{majorLevelId}, c.fk_institution_course_major_level_ids)>0
                </foreach>
            </if>
            )
        </if>

        -- 课程类型过滤
        <if
                test="(studentDto.courseTypeGroupIds !=null and studentDto.courseTypeGroupIds.size()>0) or (studentDto.oldCourseTypeGroupNames !=null and studentDto.oldCourseTypeGroupNames.size()>0)">
            AND (
            <foreach collection="studentDto.courseTypeGroupIds" item="courseTypeGroupId" index="index" separator="OR">
                FIND_IN_SET(#{courseTypeGroupId}, c.fk_institution_course_type_group_ids)>0
            </foreach>
            <if test="studentDto.oldCourseTypeGroupNames !=null and studentDto.oldCourseTypeGroupNames.size()>0">
                or
                <foreach collection="studentDto.oldCourseTypeGroupNames" item="oldCourseTypeGroupName" index="index"  separator="OR">
                    LOWER(c.old_course_type_name) like concat("%",#{oldCourseTypeGroupName},"%")
                </foreach>
            </if>
            )
        </if>

        <!-- 获取dp_time，积分，业绩，周报，排行的跳转 -->
        <if test="studentDto.dpTimeStart !=null and studentDto.dpTimeEnd !=null">
            <if test="studentDto.jumpState==1">
                and DATE_FORMAT(c9.dp_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{studentDto.dpTimeStart},'%Y-%m-%d')
                and DATE_FORMAT(c9.dp_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{studentDto.dpTimeEnd},'%Y-%m-%d')
            </if>
            <if test="studentDto.jumpState==2">
                and DATE_FORMAT(c9.dp_time,'%Y-%m-%d %H:%i:%s') <![CDATA[>= ]]> DATE_FORMAT(#{studentDto.dpTimeStart},'%Y-%m-%d %H:%i:%s')
                and DATE_FORMAT(c9.dp_time,'%Y-%m-%d %H:%i:%s') <![CDATA[<= ]]> DATE_FORMAT(#{studentDto.dpTimeEnd},'%Y-%m-%d %H:%i:%s')
            </if>
        </if>


        <!--  获取succ_time，业绩，周报，排行的跳转-->
        <if test="studentDto.succTimeStart !=null and studentDto.succTimeEnd !=null">
            <if test="studentDto.jumpState==1 || studentDto.jumpState == 10">
                AND DATE_FORMAT(c11.succ_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{studentDto.succTimeStart},'%Y-%m-%d')
                AND DATE_FORMAT(c11.succ_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{studentDto.succTimeEnd},'%Y-%m-%d')
            </if>
            <if test="studentDto.jumpState==2">
                AND DATE_FORMAT(c11.succ_time,'%Y-%m-%d %H:%i:%s') <![CDATA[>= ]]> DATE_FORMAT(#{studentDto.succTimeStart},'%Y-%m-%d %H:%i:%s')
                AND DATE_FORMAT(c11.succ_time,'%Y-%m-%d %H:%i:%s') <![CDATA[<= ]]> DATE_FORMAT(#{studentDto.succTimeEnd},'%Y-%m-%d %H:%i:%s')
            </if>
        </if>
        <!-- 获取new_time，业绩，周报，排行的跳转 -->
        <if test="studentDto.newTimeStart !=null and studentDto.newTimeEnd !=null">
            <if test="studentDto.jumpState==1">
                AND DATE_FORMAT(c12.new_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{studentDto.newTimeStart},'%Y-%m-%d')
                AND DATE_FORMAT(c12.new_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{studentDto.newTimeEnd},'%Y-%m-%d')
            </if>
            <if test="studentDto.jumpState==2">
                AND DATE_FORMAT(c12.new_time,'%Y-%m-%d %H:%i:%s') <![CDATA[>= ]]> DATE_FORMAT(#{studentDto.newTimeStart},'%Y-%m-%d %H:%i:%s')
                AND DATE_FORMAT(c12.new_time,'%Y-%m-%d %H:%i:%s') <![CDATA[<= ]]> DATE_FORMAT(#{studentDto.newTimeEnd},'%Y-%m-%d %H:%i:%s')
            </if>
        </if>

        <if test="studentDto.jumpState == 9 || studentDto.jumpState == 10">
            GROUP BY c.fk_institution_id,a.id
        </if>
        ) a

        ) s ON a.id=s.id
        <!--主要过滤sql结束-->
        <if test="!isStudentAdmin">
        INNER JOIN (
        <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.studentAuthority"/>
        ) z ON s.id=z.id
        </if>
        <!-- 学生最高/最低状态 -->
        <if test="( studentDto.maxStepIdList != null and studentDto.maxStepIdList.size()>0 ) or
            ( studentDto.minStepIdList != null and studentDto.minStepIdList.size()>0 ) or
            isExport">
            LEFT JOIN (
            <include refid="studentStatus"/>
            )m ON m.id = s.id
        </if>
        <!-- 成功入读国家 -->
        LEFT JOIN (
        SELECT
        fk_student_id,
        GROUP_CONCAT(country_name) AS areaCountrySuccessfulNames
        FROM (
        SELECT DISTINCT
        i.fk_student_id,
        c.name AS country_name
        FROM
        m_student_offer_item i
        INNER JOIN ais_institution_center.u_area_country c ON i.fk_area_country_id = c.id
        LEFT JOIN r_student_offer_item_step s ON s.fk_student_offer_item_id = i.id
        WHERE
        i.status = 1
        AND i.fk_student_offer_item_step_id != 9
        AND (s.fk_student_offer_item_step_id = 8 OR s.fk_student_offer_item_step_id = 6)
        ) t
        GROUP BY
        fk_student_id
        ) l ON l.fk_student_id = s.id
        <if test="isExport">
            <!-- 失败原因 -->
            LEFT JOIN (
                SELECT
                fk_student_id,
                GROUP_CONCAT(reason_item) AS reasonName
                FROM (
                SELECT DISTINCT
                msoi.fk_student_id,
                CONCAT_WS('', uefr.reason_name, msoi.other_failure_reason) AS reason_item
                FROM
                `m_student_offer_item` AS msoi
                LEFT JOIN u_enrol_failure_reason AS uefr
                ON uefr.id = msoi.fk_enrol_failure_reason_id
                WHERE
                (uefr.reason_name IS NOT NULL AND TRIM(uefr.reason_name) != '' AND TRIM(uefr.reason_name) != ' ')
                OR
                (msoi.other_failure_reason IS NOT NULL AND TRIM(msoi.other_failure_reason) != '' AND TRIM(msoi.other_failure_reason) != ' ')
                ) AS t
                GROUP BY
                fk_student_id
            )k ON k.fk_student_id = s.id

        </if>


        <!-- jumpState为3、5、6、7： 业绩统计跳转（申请量（按学生）、成功入学量（按学生）） -->
        <!-- jumpState为8：代理申请排行、代理送生跳转（申请量（按学生）、成功入学量（按学生）） -->
        <if test="studentDto.jumpState == 3 or
                studentDto.jumpState == 5 or
                studentDto.jumpState == 6 or
                studentDto.jumpState == 7 or
                studentDto.jumpState == 8">
            INNER JOIN (
                SELECT
                 msoi.fk_student_id AS id,
                 MIN(rsois.gmt_create) min_time
                FROM
                m_student AS s
                INNER JOIN m_student_offer AS mso ON mso.fk_student_id = s.id
                INNER JOIN m_student_offer_item AS msoi ON msoi.fk_student_offer_id = mso.id
                INNER JOIN r_student_offer_item_step rsois ON msoi.id = rsois.fk_student_offer_item_id
                INNER JOIN u_student_offer_item_step AS usois1 ON usois1.id = rsois.fk_student_offer_item_step_id
                LEFT JOIN r_staff_bd_code AS rsbc ON rsbc.fk_staff_id = mso.fk_staff_id
                -- 过滤：BD中英名字/编号/大区
                <if test="(studentDto.bdNameOrCode != null and studentDto.bdNameOrCode!='') or
                      (studentDto.fkBdIds != null and studentDto.fkBdIds.size()>0) or
                      (studentDto.fkAreaRegionId != null)">
                    INNER JOIN (
                    SELECT mso1.fk_student_id
                    FROM ais_sale_center.m_student_offer AS mso1
                    LEFT JOIN ais_permission_center.m_staff c ON mso1.fk_staff_id=c.id
                    LEFT JOIN ais_sale_center.r_staff_bd_code d ON c.id = d.fk_staff_id
                    WHERE mso1.status=1
                    <if test="(studentDto.bdNameOrCode!=null and studentDto.bdNameOrCode!='' ) or (studentDto.fkBdIds!=null and studentDto.fkBdIds.size()>0)">
                        AND (
                        <if test="studentDto.bdNameOrCode!=null and studentDto.bdNameOrCode!=''">
                            LOWER(c.`name`) like concat("%",#{studentDto.bdNameOrCode},"%")
                            OR LOWER(c.name_en) like concat("%",#{studentDto.bdNameOrCode},"%")
                            OR LOWER(d.bd_code) like concat("%",#{studentDto.bdNameOrCode},"%")
                        </if>
                        <if test="studentDto.fkBdIds!=null and studentDto.fkBdIds.size()>0">
                            OR d.fk_staff_id IN
                            <foreach collection="studentDto.fkBdIds" item="fkBdId" open="(" separator="," close=")">
                                #{fkBdId}
                            </foreach>
                        </if>
                        )
                    </if>
                    <if test="studentDto.fkAreaRegionId != null">
                        AND FIND_IN_SET(#{studentDto.fkAreaRegionId},d.fk_area_region_id)>0
                    </if>
                    GROUP BY mso1.fk_student_id
                    ) a2 ON s.id=a2.fk_student_id
                </if>

                -- 集团过滤(gea关系表过滤、iae fk_institution_provider_id过滤)
                <if test="(studentDto.groupName !=null and studentDto.groupName !='') or
                      (studentDto.fkInstitutionGroupIds != null and studentDto.fkInstitutionGroupIds.size()>0)">
                    INNER JOIN (
                    SELECT i.id
                    FROM ais_sale_center.m_student_offer_item i
                    LEFT JOIN ais_institution_center.r_institution_provider_institution a on a.fk_institution_id = i.fk_institution_id
                    LEFT JOIN ais_institution_center.m_institution_provider b ON a.fk_institution_provider_id=b.id
                    LEFT JOIN ais_institution_center.m_institution_group c ON b.fk_institution_group_id=c.id
                    <where>
                        <if test="studentDto.groupName != null and studentDto.groupName !=''">
                            AND LOWER(c.name) like concat("%",#{studentDto.groupName},"%") OR LOWER(c.name_chn) like concat("%",#{studentDto.groupName},"%")
                        </if>
                        <if test="studentDto.fkInstitutionGroupIds != null and studentDto.fkInstitutionGroupIds.size()>0">
                            AND i.fk_institution_provider_id=-1 AND c.id IN
                            <foreach collection="studentDto.fkInstitutionGroupIds" item="fkInstitutionGroupId" open="(" separator="," close=")">
                                #{fkInstitutionGroupId}
                            </foreach>
                        </if>
                    </where>
                    UNION
                    SELECT i.id
                    FROM ais_sale_center.m_student_offer_item i
                    LEFT JOIN ais_institution_center.m_institution_provider b ON i.fk_institution_provider_id=b.id
                    LEFT JOIN ais_institution_center.m_institution_group c ON b.fk_institution_group_id=c.id
                    <where>
                        <if test="studentDto.groupName != null and studentDto.groupName !=''">
                            AND LOWER(c.name) like concat("%",#{studentDto.groupName},"%") OR LOWER(c.name_chn) like concat("%",#{studentDto.groupName},"%")
                        </if>
                        <if test="studentDto.fkInstitutionGroupIds != null and studentDto.fkInstitutionGroupIds.size()>0">
                            AND c.id IN
                            <foreach collection="studentDto.fkInstitutionGroupIds" item="fkInstitutionGroupId" open="(" separator="," close=")">
                                #{fkInstitutionGroupId}
                            </foreach>
                        </if>
                    </where>
                    ) c2 ON msoi.id=c2.id
                </if>

                /*权限处理*/
                <if test="staffFollowerIds != null and staffFollowerIds.size()>0">
                    <if test="!isStudentAdmin">
                        INNER JOIN (
                        <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.itemPermissionSql"/>
                        ) z ON msoi.id=z.id
                    </if>
                 </if>
                  <!-- 权限sql结束 -->

                <where>
                    AND msoi.status = 1
                    AND msoi.is_follow = 0
                    AND mso.`status` IN (1,2,3)
                    AND IFNULL(mso.fk_agent_id,0) NOT IN(11195,13103,13282)

                    <if test="!isStudentOfferItemFinancialHiding">
                        AND IFNULL(msoi.is_follow_hidden, 0)!=1
                    </if>
                    <!-- 权限国家过滤 -->
                    <if test="fkAreaCountryIds != null and fkAreaCountryIds.size()>0">
                        AND msoi.fk_area_country_id IN
                        <foreach collection="fkAreaCountryIds" item="fkAreaCountryId" open="("
                                 separator="," close=")">
                            #{fkAreaCountryId}
                        </foreach>
                    </if>
                    <!-- 公司id -->
                    <if test="studentDto.fkCompanyIds !=null and studentDto.fkCompanyIds.size>0">
                        AND s.fk_company_id in
                        <foreach collection="studentDto.fkCompanyIds" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>
                    <!-- 报考国家 -->
                    <if test="studentDto.targetCountryIdList!=null and studentDto.targetCountryIdList.size()>0">
                        AND msoi.fk_area_country_id in
                        <foreach collection="studentDto.targetCountryIdList" item="targetCountryId" index="index" open="(" separator="," close=")">
                            #{targetCountryId}
                        </foreach>
                    </if>

                    <if test="studentDto.dpTimeStart !=null and studentDto.dpTimeEnd !=null">
                        AND usois1.step_key IN
                        <choose>
                            <when test="studentDto.fkCompanyId != null and studentDto.fkCompanyId == 3">
                                <foreach collection="studentDto.iaeConfirmationStatisticsStepList" item="iaeConfirmationStatisticsStep" open="(" separator="," close=")">
                                    #{iaeConfirmationStatisticsStep}
                                </foreach>
                            </when>
                            <otherwise>
                                <foreach collection="studentDto.geaConfirmationStatisticsStepList" item="geaConfirmationStatisticsStep" open="(" separator="," close=")">
                                    #{geaConfirmationStatisticsStep}
                                </foreach>
                            </otherwise>
                        </choose>
                    </if>

                    <if test="studentDto.succTimeStart !=null and studentDto.succTimeEnd !=null">
                        AND msoi.fk_student_offer_item_step_id = 8
                        AND usois1.step_key IN
                        <choose>
                            <when test="studentDto.fkCompanyId != null and studentDto.fkCompanyId == 3">
                                <foreach collection="studentDto.iaeSuccessStatisticsStepList" item="iaeSuccessStatisticsStep" open="(" separator="," close=")">
                                    #{iaeSuccessStatisticsStep}
                                </foreach>
                            </when>
                            <otherwise>
                                <foreach collection="studentDto.geaSuccessStatisticsStepList" item="geaSuccessStatisticsStep" open="(" separator="," close=")">
                                    #{geaSuccessStatisticsStep}
                                </foreach>
                            </otherwise>
                        </choose>
                    </if>

                    -- 学校过滤
                    <if test="studentDto.institutionIds!=null and studentDto.institutionIds.size()>0">
                        AND msoi.fk_institution_id IN
                        <foreach collection="studentDto.institutionIds" item="institutionId" index="index" open="(" separator="," close=")">
                            #{institutionId}
                        </foreach>
                    </if>


                    <if test="studentDto.jumpState == 8">
                        <if test="studentDto.dpTimeStart != null and studentDto.dpTimeEnd !=null">
                            AND DATE_FORMAT(s.gmt_create,'%Y-%m-%d') &gt;= DATE_FORMAT(#{studentDto.dpTimeStart},'%Y-%m-%d')
                            AND DATE_FORMAT(s.gmt_create,'%Y-%m-%d') &lt;= DATE_FORMAT(#{studentDto.dpTimeEnd},'%Y-%m-%d')
                        </if>
                        <if test="studentDto.succTimeStart !=null and studentDto.succTimeEnd !=null">
                            AND DATE_FORMAT(s.gmt_create,'%Y-%m-%d') &gt;= DATE_FORMAT(#{studentDto.succTimeStart},'%Y-%m-%d')
                            AND DATE_FORMAT(s.gmt_create,'%Y-%m-%d') &lt;= DATE_FORMAT(#{studentDto.succTimeEnd},'%Y-%m-%d')
                        </if>
                    </if>


                    <if test="studentDto.jumpState == 6 or studentDto.jumpState == 7 or studentDto.jumpState == 8">
                        GROUP BY msoi.fk_student_id
                    </if>
                    <if test="studentDto.jumpState == 3 or studentDto.jumpState == 5">
                        GROUP BY msoi.fk_student_id,msoi.fk_area_country_id
                    </if>
                </where>
                HAVING 1=1
                <if test="studentDto.dpTimeStart != null and studentDto.dpTimeEnd !=null">
                    <if test="studentDto.jumpState == 3 or studentDto.jumpState == 6 or studentDto.jumpState == 8">
                        AND DATE_FORMAT(min_time,'%Y-%m-%d') &gt;= DATE_FORMAT(#{studentDto.dpTimeStart},'%Y-%m-%d')
                        AND DATE_FORMAT(min_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{studentDto.dpTimeEnd},'%Y-%m-%d')
                    </if>
                    <if test="studentDto.jumpState==5 or studentDto.jumpState == 7">
                        AND DATE_FORMAT(min_time,'%Y-%m-%d %H:%i:%s') &gt;= DATE_FORMAT(#{studentDto.dpTimeStart},'%Y-%m-%d %H:%i:%s')
                        AND DATE_FORMAT(min_time,'%Y-%m-%d %H:%i:%s') &lt;= DATE_FORMAT(#{studentDto.dpTimeEnd},'%Y-%m-%d %H:%i:%s')
                    </if>
                </if>
                <if test="studentDto.succTimeStart !=null and studentDto.succTimeEnd !=null">
                    <if test="studentDto.jumpState == 3 or studentDto.jumpState == 6 or studentDto.jumpState == 8">
                        AND DATE_FORMAT(min_time,'%Y-%m-%d') &gt;= DATE_FORMAT(#{studentDto.succTimeStart},'%Y-%m-%d')
                        AND DATE_FORMAT(min_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{studentDto.succTimeEnd},'%Y-%m-%d')
                    </if>
                    <if test="studentDto.jumpState==5 or studentDto.jumpState == 7">
                        AND DATE_FORMAT(min_time,'%Y-%m-%d %H:%i:%s') &gt;= DATE_FORMAT(#{studentDto.succTimeStart},'%Y-%m-%d %H:%i:%s')
                        AND DATE_FORMAT(min_time,'%Y-%m-%d %H:%i:%s') &lt;= DATE_FORMAT(#{studentDto.succTimeEnd},'%Y-%m-%d %H:%i:%s')
                    </if>
                </if>
            ) AS t on t.id = s.id
        </if>



        <where>
            <if test="studentDto.maxStepIdList != null and studentDto.maxStepIdList.size()>0 ">
                AND m.max_step_order IN ( SELECT step_order FROM u_student_offer_item_step WHERE id IN
                <foreach collection="studentDto.maxStepIdList" item="maxStepId" index="index" open="(" separator="," close=")">
                    #{maxStepId}
                </foreach>
            )
            </if>
            <if test="studentDto.minStepIdList != null and studentDto.minStepIdList.size()>0 ">
                AND m.min_step_order IN ( SELECT step_order FROM u_student_offer_item_step WHERE id IN
                <foreach collection="studentDto.minStepIdList" item="minStepId" index="index" open="(" separator="," close=")">
                    #{minStepId}
                </foreach>
                )
            </if>
        </where>

        <if test="studentDto.pageNumber != null">
            ORDER BY a.gmt_create DESC
<!--            <choose>-->
<!--                <when test="studentDto.name!=null and studentDto.name!=''">-->
<!--                    ORDER BY weights ASC-->
<!--                </when>-->
<!--                <otherwise>-->
<!--                    ORDER BY a.gmt_create DESC-->
<!--                </otherwise>-->
<!--            </choose>-->
            <if test="studentDto.pageSize != null">
            LIMIT #{studentDto.offset},#{studentDto.pageSize}
            </if>
        </if>
    </sql>

    <select id="getStudentItemStatus" resultType="com.get.salecenter.vo.StudentItemStatusVo">
        <include refid="studentStatus"/>
    </select>

    <sql id="studentStatus">
    SELECT studentStatus.id,
           CASE WHEN studentStatus.min_step_order = studentStatus.failure_max_step_order THEN min_step_order
                WHEN studentStatus.min_step_order != studentStatus.failure_max_step_order THEN max_step_order END AS max_step_order,
            studentStatus.min_step_order
        FROM (
            SELECT
            s.id,
            MAX( CASE WHEN usois1.id != 9 THEN usois1.step_order ELSE NULL END ) AS max_step_order,
            MAX( usois1.step_order ) AS failure_max_step_order,
            MIN( usois1.step_order ) AS min_step_order
            FROM
            m_student AS s
            INNER JOIN m_student_offer AS mso ON mso.fk_student_id = s.id
            INNER JOIN m_student_offer_item AS msoi ON msoi.fk_student_offer_id = mso.id
            <!-- 显示2022年的学生 -->
            <if test="staffId == 1247 or staffId ==  1245">
                INNER JOIN (
                SELECT
                id
                FROM
                m_student_offer_item
                WHERE
                DATE_FORMAT(defer_opening_time,'%Y-%m-%d') <![CDATA[>=]]> DATE_FORMAT(#{beginOpenTime},'%Y-%m-%d')
                AND DATE_FORMAT(defer_opening_time,'%Y-%m-%d') <![CDATA[<=]]> DATE_FORMAT(#{endOpenTime},'%Y-%m-%d')
                GROUP BY id
                )2022StudentItem1 ON 2022StudentItem1.id = msoi.id
            </if>
            INNER JOIN u_student_offer_item_step AS usois1 ON usois1.id = msoi.fk_student_offer_item_step_id
            LEFT JOIN r_staff_bd_code AS rsbc ON rsbc.fk_staff_id = mso.fk_staff_id
            <!-- 权限sql -->
            INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.itemPermissionSql"/>
            ) z ON msoi.id=z.id

            where
            msoi.status = 1
            AND mso.status = 1
            <if test="staffId != 1247 and staffId !=  1245">
                AND msoi.is_follow = 0
            </if>
            <if test="!isStudentOfferItemFinancialHiding">
                AND IFNULL(msoi.is_follow_hidden, 0)!=1
            </if>
            <!-- 权限国家过滤 -->
            <if test="fkAreaCountryIds != null and fkAreaCountryIds.size()>0">
                AND msoi.fk_area_country_id IN
                <foreach collection="fkAreaCountryIds" item="fkAreaCountryId" open="("
                         separator="," close=")">
                    #{fkAreaCountryId}
                </foreach>
            </if>
            <!-- 公司id -->
            <if test="studentDto.fkCompanyIds !=null and studentDto.fkCompanyIds.size>0">
                AND s.fk_company_id in
                <foreach collection="studentDto.fkCompanyIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 报考国家 -->
            <if test="studentDto.targetCountryIdList!=null and studentDto.targetCountryIdList.size()>0">
                AND msoi.fk_area_country_id in
                <foreach collection="studentDto.targetCountryIdList" item="targetCountryId" index="index" open="("
                         separator="," close=")">
                    #{targetCountryId}
                </foreach>
            </if>
            <if test="studentIds!=null and studentIds.size>0">
                AND s.id IN
                <foreach collection="studentIds" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            GROUP BY
            s.id
        )studentStatus
    </sql>


    <select id="hasPassportNum" resultType="java.lang.Boolean">
        SELECT CASE WHEN (passport_num IS NULL OR  passport_num = '') THEN 0 ELSE 1 END hasPassportNum
        FROM m_student WHERE id = #{fkStudentId}
    </select>
    <select id="getStudentById" resultType="com.get.salecenter.entity.Student">
        SELECT * FROM m_student WHERE id = #{fkStudentId}
    </select>
    <select id="getStudentAgentBinding" resultType="com.get.salecenter.vo.AgentsBindingVo">
        SELECT m.NAME as agentName,
        d.name as bdName
        FROM m_agent m
        LEFT JOIN r_student_agent s ON s.fk_agent_id = m.id
        LEFT JOIN r_agent_staff a ON s.fk_agent_id = a.fk_agent_id
        LEFT JOIN ais_permission_center.m_staff d ON d.id = a.fk_staff_id
        LEFT JOIN m_student t on s.fk_student_id = t.id
        WHERE t.num = #{fkStudentNum}
        AND s.is_active = 1
        AND a.is_active = 1
    </select>
    <select id="getCompanyNameByStudentId" resultType="com.get.permissioncenter.entity.Company">
        SELECT
            m.id,
            m.short_name
        FROM
            m_student s
        INNER JOIN ais_permission_center.m_company m ON m.id = s.fk_company_id
        WHERE
            s.id = #{companyId}
    </select>
    <select id="getReasonNameByStudentIds" resultType="com.get.salecenter.vo.SelItem">
        SELECT
            msoi.fk_student_id as keyId,
            IFNULL(GROUP_CONCAT(DISTINCT uefr.reason_name, msoi.other_failure_reason),'') as val
        FROM
            `m_student_offer_item` AS msoi
                LEFT JOIN u_enrol_failure_reason AS uefr ON uefr.id = msoi.fk_enrol_failure_reason_id
                <if test="ids!=null and ids.size>0">
                    WHERE msoi.fk_student_id in
                    <foreach collection="ids" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
        GROUP BY
            fk_student_id
    </select>
    <select id="getStudentCountByCountry" resultType="com.get.salecenter.vo.SelItem">
        select msoi.fk_area_country_id as keyId,count(DISTINCT s.id) as val from m_student AS s
        INNER JOIN m_student_offer_item AS msoi ON msoi.fk_student_id = s.id AND msoi.status = 1
        <if test="!isStudentAdmin">
        INNER JOIN (
        <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.studentAuthority"/>
        ) z ON s.id=z.id
        </if>
        WHERE msoi.fk_area_country_id IN
        <foreach collection="fkAreaCountryIds" item="aid" separator="," open="(" close=")">
            #{aid}
        </foreach>
        AND year(s.gmt_create)=#{year} and msoi.is_follow = 0
        <if test="!isStudentOfferItemFinancialHiding">
            AND IFNULL(msoi.is_follow_hidden, 0) != 1
        </if>
        <if test="companyIds != null and companyIds.size()>0">
            AND s.fk_company_id IN
            <foreach collection="companyIds" item="companyId" index="index" open="(" separator="," close=")">
                #{companyId,jdbcType=BIGINT}
            </foreach>
        </if>
        GROUP BY msoi.fk_area_country_id
    </select>
    <select id="getStudentAllGraduatedCountrySelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            c.id,
            c.name,
            c.name_chn
        FROM
            m_student s
        INNER JOIN ais_institution_center.u_area_country c ON c.id = s.fk_area_country_id_education or c.id = s.fk_area_country_id_education2
        WHERE s.fk_company_id = #{companyId}
        GROUP BY c.id
    </select>
    <select id="getStudentGraduateCountryMpStateSelect"
            resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            c.id,
            c. NAME,
            c.name_chn
        FROM
            m_student s
        INNER JOIN ais_institution_center.u_area_state c ON
             <choose>
                 <when test="id!=null and id ==3">
                     c.id = s.fk_area_state_id_education
                 </when>
                 <otherwise>
                     c.id = s.fk_area_state_id_education2
                 </otherwise>
             </choose>
        WHERE
        s.fk_company_id = #{companyId}
            <choose>
                <when test="id!=null and id ==3">
                    AND s.fk_area_country_id_education = #{id}
                </when>
                <otherwise>
                    AND s.fk_area_country_id_education2 = #{id}
                </otherwise>
            </choose>
        GROUP BY
            c.id
    </select>
    <select id="getStudentGraduateSchool" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            c.id,
            c. NAME,
            c.name_chn
        FROM
            m_student s
        INNER JOIN ais_institution_center.m_institution c ON
        <choose>
            <when test="countryId!=null and countryId ==3">
                c.id = s.fk_institution_id_education
            </when>
            <otherwise>
                c.id = s.fk_institution_id_education2
            </otherwise>
        </choose>
        WHERE
        s.fk_company_id = #{companyId}
        <choose>
            <when test="countryId!=null and countryId ==3">
                AND s.fk_area_country_id_education = #{countryId}
            </when>
            <otherwise>
                AND s.fk_area_country_id_education2 = #{countryId}
            </otherwise>
        </choose>
            and s.fk_area_state_id_education = #{stateId}
        GROUP BY
            c.id
    </select>

    <select id="getSuccessfulApplicationCountry" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        select i.fk_student_id as id,GROUP_CONCAT(DISTINCT c.name) as name
        from m_student_offer_item i
        INNER JOIN ais_institution_center.u_area_country c ON i.fk_area_country_id = c.id
        left join r_student_offer_item_step s on s.fk_student_offer_item_id = i.id
        where i.status = 1
        AND i.fk_student_offer_item_step_id!=9
        and (s.fk_student_offer_item_step_id = 8 OR s.fk_student_offer_item_step_id = 6)
        <if test="ids.size() &lt; 9000">
            and i.fk_student_id in
            <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY i.fk_student_id
    </select>
    <select id="getTotalCount" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM m_student
    </select>
    <select id="getStudentBirthDayByIds" resultType="com.get.salecenter.entity.Student">
        select id,birthday from m_student
        <where>
            <if test="studentIds !=null and studentIds.size()>0">
                and id in
                <foreach collection="studentIds" item="studentId" index="index" open="(" separator="," close=")">
                    #{studentId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getStudentZhEnNameByIds" resultType="com.get.salecenter.vo.StudentVo">
        select s.id,CASE WHEN IFNULL(CONCAT(s.first_name,s.last_name), '') = '' THEN s.name ELSE CONCAT(s.name, '（',
        CONCAT(s.first_name,s.last_name), '）') END fullName from m_student s
        <where>
            <if test="studentIds !=null and studentIds.size()>0">
                and s.id in
                <foreach collection="studentIds" item="studentId" index="index" open="(" separator="," close=")">
                    #{studentId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getStudentPassportMumByIds" resultType="com.get.salecenter.vo.StudentVo">
        select id,passport_num from m_student
        <where>
            <if test="studentIds !=null and studentIds.size()>0">
                and id in
                <foreach collection="studentIds" item="studentId" index="index" open="(" separator="," close=")">
                    #{studentId}
                </foreach>
            </if>
        </where>
    </select>


    <select id="getStudentAgentBindingByStudentName" resultType="com.get.salecenter.vo.StudentAgentBindingNewVo">
        SELECT a.id as studentId,
        a.name as studentName,a.num as studentNum,
        CONCAT(a.first_name,' ',a.last_name) as studentNameEng,sis.fk_student_id_issue,sis.fk_student_id_issue2,
        CASE
        WHEN sis.fk_student_id_issue is not null and sis.fk_student_id_issue != '' THEN
        1
        when sis.fk_student_id_issue2 and sis.fk_student_id_issue2 != '' is not null then 2
        ELSE
        0
        END fkPlatformType
        FROM m_student a
        LEFT JOIN r_student_issue_student sis on sis.fk_student_id = a.id
        where a.fk_company_id = #{fkCompanyId}
        <if test="fkStudentName !=null and fkStudentName != ''">
            AND (
            REPLACE(CONCAT(a.first_name,a.last_name),' ','') like concat('%',#{fkStudentName},'%')
            OR REPLACE(CONCAT(a.last_name,a.first_name),' ','') like concat('%',#{fkStudentName},'%')
            OR REPLACE(a.`name`,' ','') like concat('%',#{fkStudentName},'%')
            OR REPLACE(a.last_name,' ','') like concat('%',#{fkStudentName},'%')
            OR REPLACE(a.first_name,' ','') like concat('%',#{fkStudentName},'%'))
        </if>
    </select>
    <select id="getStudentInfoByEmail" resultType="com.get.salecenter.vo.StudentAgentEmailVo">
        SELECT ms.id AS studentId,ms.name AS studentName,ms.email,GROUP_CONCAT(ma.name) AS
        agentName,GROUP_CONCAT(b.name) AS bdName
        FROM m_student ms
        LEFT JOIN r_student_agent rsa on rsa.fk_student_id = ms.id
        LEFT JOIN m_agent ma on ma.id = rsa.fk_agent_id
        LEFT JOIN r_agent_staff AS s ON s.fk_agent_id = ma.id AND s.is_active = 1
        LEFT JOIN ais_permission_center.m_staff b on b.id = s.fk_staff_id
        WHERE 1=1
        <if test="ids != null and ids.size()>0">
            AND ms.id IN
            <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY ms.id
    </select>
    <select id="getStudentGraduationBackgroundInfo"
            resultType="com.get.salecenter.vo.StudentGraduationBackgroundVo">
        SELECT COUNT(DISTINCT x.id) as offerItemCount, COUNT(DISTINCT x.studentId) as offerItemStudentCount FROM
        (
        SELECT
        a.id,
        s.id as studentId
        FROM
        m_student_offer_item a
        INNER JOIN m_student s ON a.fk_student_id = s.id
        LEFT JOIN ais_institution_center.m_institution m ON s.fk_institution_id_education = m.id
        LEFT JOIN ais_institution_center.u_area_country c ON a.fk_area_country_id = c.id
        LEFT JOIN ais_institution_center.m_institution m2 ON a.fk_institution_id = m2.id
        LEFT JOIN ais_institution_center.m_institution_course c2 ON a.fk_institution_course_id = c2.id
        LEFT JOIN r_student_offer_item_step r ON r.fk_student_offer_item_id = a.id
        AND r.fk_student_offer_item_step_id in(
        4,5,6,7,8,10,13,14,15) AND a.fk_student_offer_item_step_id!=9
        WHERE
        1=1
        AND a.status = 1
        <if test="!isStudentOfferItemFinancialHiding">
            AND IFNULL(a.is_follow_hidden, 0) != 1
        </if>
        <if test="statisticsSearchDto.companyId!=null">
            AND s.fk_company_id = #{statisticsSearchDto.companyId}
        </if>
        <if test="statisticsSearchDto.countryId!=null and statisticsSearchDto.countryId!=''">
            AND s.fk_area_country_id_education = #{statisticsSearchDto.countryId}
        </if>
        <if test="statisticsSearchDto.provinceId!=null and statisticsSearchDto.provinceId!=''">
            AND s.fk_area_state_id_education = #{statisticsSearchDto.provinceId}
        </if>
        <if test="statisticsSearchDto.graduateSchoolId!=null and statisticsSearchDto.graduateSchoolId!=''">
            AND  s.fk_institution_id_education = #{statisticsSearchDto.graduateSchoolId}
        </if>
        <if test="statisticsSearchDto.graduateSchoolName!=null and statisticsSearchDto.graduateSchoolName!=''">
            AND (m.name LIKE CONCAT('%', #{statisticsSearchDto.graduateSchoolName}, '%')
            OR m.name_chn LIKE CONCAT('%',#{statisticsSearchDto.graduateSchoolName},'%')
            OR s.fk_institution_name_education LIKE CONCAT('%',#{statisticsSearchDto.graduateSchoolName},
            '%'))
        </if>
        <if test="statisticsSearchDto.majorName!=null and statisticsSearchDto.majorName!=''">
            AND s.education_major LIKE CONCAT('%',#{statisticsSearchDto.majorName},'%')
        </if>
        <if test="statisticsSearchDto.applyCountryId!=null and statisticsSearchDto.applyCountryId!=''">
            AND a.fk_area_country_id = #{statisticsSearchDto.applyCountryId}
        </if>
        <if test="statisticsSearchDto.applySchoolId!=null and statisticsSearchDto.applySchoolId!=''">
            AND a.fk_institution_id = #{statisticsSearchDto.applySchoolId}
        </if>
        <if test="statisticsSearchDto.beginTime!=null">
            AND DATE_FORMAT(a.gmt_create,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{statisticsSearchDto.beginTime},'%Y-%m-%d')
        </if>
        <if test="statisticsSearchDto.endTime!=null">
            AND DATE_FORMAT(a.gmt_create,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{statisticsSearchDto.endTime},'%Y-%m-%d')
        </if>
        GROUP BY a.id
        UNION ALL

        SELECT
        a.id,
        s.id as studentId
        FROM
        m_student_offer_item a
        INNER JOIN m_student s ON a.fk_student_id = s.id
        LEFT JOIN ais_institution_center.m_institution m ON s.fk_institution_id_education2 = m.id
        LEFT JOIN ais_institution_center.u_area_country c ON a.fk_area_country_id = c.id
        LEFT JOIN ais_institution_center.m_institution m2 ON a.fk_institution_id = m2.id
        LEFT JOIN ais_institution_center.m_institution_course c2 ON a.fk_institution_course_id = c2.id
        LEFT JOIN r_student_offer_item_step r ON r.fk_student_offer_item_id = a.id
        AND r.fk_student_offer_item_step_id in(
        4,5,6,7,8,10,13,14,15) AND a.fk_student_offer_item_step_id!=9
        WHERE
        1=1
        AND a.status = 1
        <if test="!isStudentOfferItemFinancialHiding">
            AND IFNULL(a.is_follow_hidden, 0) != 1
        </if>
        <if test="statisticsSearchDto.companyId!=null">
            AND s.fk_company_id = #{statisticsSearchDto.companyId}
        </if>
        <if test="statisticsSearchDto.countryId!=null and statisticsSearchDto.countryId!=''">
            AND s.fk_area_country_id_education2 = #{statisticsSearchDto.countryId}
        </if>
        <if test="statisticsSearchDto.provinceId!=null and statisticsSearchDto.provinceId!=''">
            AND s.fk_area_state_id_education2 = #{statisticsSearchDto.provinceId}
        </if>
        <if test="statisticsSearchDto.graduateSchoolId!=null and statisticsSearchDto.graduateSchoolId!=''">
            AND  s.fk_institution_id_education2 = #{statisticsSearchDto.graduateSchoolId}
        </if>
        <if test="statisticsSearchDto.graduateSchoolName!=null and statisticsSearchDto.graduateSchoolName!=''">
            AND (m.name LIKE CONCAT('%', #{statisticsSearchDto.graduateSchoolName}, '%')
            OR m.name_chn LIKE CONCAT('%',#{statisticsSearchDto.graduateSchoolName},'%')
            OR s.fk_institution_name_education2 LIKE CONCAT('%',#{statisticsSearchDto.graduateSchoolName},
            '%'))
        </if>
        <if test="statisticsSearchDto.majorName!=null and statisticsSearchDto.majorName!=''">
            AND s.education_major2 LIKE CONCAT('%',#{statisticsSearchDto.majorName},'%')
        </if>
        <if test="statisticsSearchDto.applyCountryId!=null and statisticsSearchDto.applyCountryId!=''">
            AND a.fk_area_country_id = #{statisticsSearchDto.applyCountryId}
        </if>
        <if test="statisticsSearchDto.applySchoolId!=null and statisticsSearchDto.applySchoolId!=''">
            AND a.fk_institution_id = #{statisticsSearchDto.applySchoolId}
        </if>
        <if test="statisticsSearchDto.beginTime!=null">
            AND DATE_FORMAT(a.gmt_create,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{statisticsSearchDto.beginTime},'%Y-%m-%d')
        </if>
        <if test="statisticsSearchDto.endTime!=null">
            AND DATE_FORMAT(a.gmt_create,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{statisticsSearchDto.endTime},'%Y-%m-%d')
        </if>
        ) x
    </select>
    <select id="getStudentByNum" resultType="com.get.salecenter.vo.StudentVo">
        SELECT
            s.`name`,s.id,
            s.first_name,
            s.last_name,
            s.gmt_create,
            sis.fk_student_id_issue,
            sis.fk_student_id_issue2
        FROM
            m_student s
        left join r_student_issue_student sis on sis.fk_student_id = s.id
        WHERE
            s.num LIKE CONCAT('',#{num},'%')
            LIMIT 1
    </select>
    <select id="getClientStudent" resultType="com.get.salecenter.vo.StudentVo">
        SELECT s.*,
        CASE WHEN IFNULL(CONCAT(s.first_name,s.last_name), '') = '' THEN s.name ELSE CONCAT(s.name, '（',
        CONCAT(s.first_name,s.last_name), '）') END fullName
        FROM  m_student s
        INNER JOIN (
        SELECT s.id FROM
        m_student AS s
        LEFT JOIN m_student_offer_item i on i.fk_student_id = s.id
        LEFT JOIN r_student_agent sa on sa.fk_student_id = s.id
        LEFT JOIN m_agent a on a.id = sa.fk_agent_id
        LEFT JOIN m_student_service_fee AS mssf on mssf.fk_student_id = s.id
        LEFT JOIN m_agent a2 on mssf.fk_agent_id = a2.id
        where ( (i.id is not null AND i.status = 1) or ( mssf.id is not null and mssf.status = 1) )
        <if test="clientStudentDto.fkCompanyIds != null and clientStudentDto.fkCompanyIds.size>0">
            AND s.fk_company_id in
            <foreach collection="clientStudentDto.fkCompanyIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="clientStudentDto.fkBusinessCountryIds != null and clientStudentDto.fkBusinessCountryIds.size>0">
            AND (
                i.fk_area_country_id in
            <foreach collection="clientStudentDto.fkBusinessCountryIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            OR
            <foreach collection="clientStudentDto.fkBusinessCountryIds" item="item" separator="or" open="(" close=")">
                 FIND_IN_SET(#{item},mssf.fk_area_country_ids) > 0
            </foreach>
                )
        </if>
        <if test="clientStudentDto.num != null and clientStudentDto.num != ''">
            AND s.num = #{clientStudentDto.num}
        </if>
        <if test="clientStudentDto.name != null and clientStudentDto.name != ''">
            AND (s.name LIKE CONCAT('%', #{clientStudentDto.name}, '%') OR s.last_name LIKE CONCAT('%', #{clientStudentDto.name}, '%') OR s.first_name LIKE CONCAT('%', #{clientStudentDto.name}, '%'))
        </if>
        <if test="clientStudentDto.birthday!=null">
            AND DATE_FORMAT(s.birthday,'%Y-%m-%d') = DATE_FORMAT(#{clientStudentDto.birthday},'%Y-%m-%d')
        </if>
        <if test="clientStudentDto.agentName != null and clientStudentDto.agentName !=''">
            AND ( a.name LIKE CONCAT('%', #{clientStudentDto.agentName}, '%') or a2.name LIKE CONCAT('%', #{clientStudentDto.agentName}, '%') )
        </if>
        <if test="clientStudentDto.countryId != null">
            AND ( i.fk_area_country_id = #{clientStudentDto.countryId} or (FIND_IN_SET(#{clientStudentDto.countryId},mssf.fk_area_country_ids) > 0) )
        </if>
        GROUP BY
        s.id) b ON b.id = s.id
        ORDER BY s.gmt_create DESC
    </select>
    <select id="getStudentSelect" resultType="com.get.salecenter.entity.Student">
        SELECT * FROM m_student
        WHERE fk_company_id in
            <foreach collection="companyIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        <if test="name != null and name != ''">
            AND
            (
            REPLACE(CONCAT(LOWER(first_name),LOWER(last_name)),' ','') LIKE REPLACE(concat('%',LOWER(#{name}),'%'),' ','')
            OR REPLACE(CONCAT(LOWER(last_name),LOWER(first_name)),' ','') LIKE REPLACE(concat('%',LOWER(#{name}),'%'),' ','')
            OR REPLACE(LOWER(`name`),' ','') LIKE REPLACE(concat('%',LOWER(#{name}),'%'),' ','')
            OR REPLACE(LOWER(last_name),' ','') LIKE REPLACE(concat('%',LOWER(#{name}),'%'),' ','')
            OR REPLACE(LOWER(first_name),' ','') LIKE REPLACE(concat('%',LOWER(#{name}),'%'),' ','')
            OR LOWER(num) LIKE concat('%',LOWER(#{name}),'%')
            )
        </if>
        <if test="studentId != null">
            AND id = #{studentId}
        </if>
        limit 100
    </select>

    <select id="getAiStudentInfo" resultType="com.get.salecenter.vo.AiStudentInfoVo">
        SELECT
            ms.id AS studentId,
            any_value(mc.num) AS companyName,
            any_value(ms.birthday),
            MAX(CASE WHEN ms.gender = 0 THEN '女' ELSE '男' END) AS gender,
            MAX( CASE WHEN (ms.first_name IS NULL OR ms.first_name = '') AND (ms.last_name IS NULL OR ms.last_name = '')
            THEN ms.NAME
            ELSE CONCAT(ms.NAME, '（', COALESCE(ms.first_name, ''), ' ', COALESCE(ms.last_name, ''), '）')
            END ) AS studentName,
            any_value(ma.name) AS agentName,
            any_value(ms.gmt_create) AS createTime
        FROM
            m_student AS ms
                LEFT JOIN r_student_agent as rsa ON rsa.fk_student_id = ms.id AND rsa.is_active = 1
                LEFT JOIN m_agent AS ma ON ma.id = rsa.fk_agent_id
                LEFT JOIN ais_permission_center.m_company AS mc ON mc.id = ms.fk_company_id
                <!--主要过滤sql结束-->
                <if test="!isStudentAdmin">
                    INNER JOIN (
                    <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.studentAuthority"/>
                    ) z ON ms.id=z.id
                </if>
        WHERE ms.fk_company_id IN
        <foreach collection="companyIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND ms.name = #{studentName}

        GROUP BY ms.id
    </select>

</mapper>