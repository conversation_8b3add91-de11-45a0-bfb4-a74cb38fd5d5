package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @author: Sea
 * @create: 2021/4/23 18:35
 * @verison: 1.0
 * @description:
 */
@Data
public class AgentContractFormulaCommissionDto extends BaseVoEntity {
    /**
     * 学生代理合同公式Id
     */
    @ApiModelProperty(value = "学生代理合同公式Id")
    private Long fkAgentContractFormulaId;

    /**
     * 期数，从1开始，按顺序生成，并按顺序排序
     */
    @ApiModelProperty(value = "期数，从1开始，按顺序生成，并按顺序排序")
    private Integer step;

    /**
     * 代理佣金比例
     */
    @ApiModelProperty(value = "代理佣金比例（学费比例）")
    private BigDecimal commissionRateAg;

    @ApiModelProperty(value = "代理佣金比例（应收比例）")
    private BigDecimal receivableRateAg;

    /**
     * 代理固定金额
     */
    @ApiModelProperty(value = "代理固定金额")
    private BigDecimal fixedAmountAg;

    /**
     * 代理佣金上限
     */
    @ApiModelProperty(value = "代理佣金上限")
    @Column(name = "limit_amount_ag")
    private BigDecimal limitAmountAg;
}
