package com.get.platformconfigcenter.service;


import com.get.common.result.SearchBean;
import com.get.platformconfigcenter.entity.MsoEventRegistration;
import com.get.platformconfigcenter.dto.EventRegistrationDto;
import com.get.platformconfigcenter.dto.IsAttendDto;

import java.util.List;

/**
 * @ClassName: EventRegistrationService
 * @Author: Eric
 * @Date: 2023/5/24 9:55
 * @Version: 1.0
 */
public interface EventRegistrationService {

    List<MsoEventRegistration> getEventRegistrationList(EventRegistrationDto data, SearchBean<EventRegistrationDto> eventRegistrationVo);

    void batchUpdateIsAttend(IsAttendDto isAttendDto);

    void batchDeleteEventRegistration(List<Long> ids);
}
