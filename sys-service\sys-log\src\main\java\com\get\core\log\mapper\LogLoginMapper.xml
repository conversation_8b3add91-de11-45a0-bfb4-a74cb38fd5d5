<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.core.log.mapper.LogLoginMapper">
  <resultMap id="BaseResultMap" type="com.get.core.log.model.LogLogin">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_staff_id" jdbcType="BIGINT" property="fkStaffId" />
    <result column="staff_login_id" jdbcType="VARCHAR" property="staffLoginId" />
    <result column="staff_name" jdbcType="VARCHAR" property="staffName" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="fk_company_id" jdbcType="BIGINT" property="fkCompanyId" />
    <result column="login_time" jdbcType="TIMESTAMP" property="loginTime" />
    <result column="logout_time" jdbcType="TIMESTAMP" property="logoutTime" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.get.core.log.model.LogLogin">
    insert into log_login
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkStaffId != null">
        fk_staff_id,
      </if>
      <if test="staffLoginId != null">
        staff_login_id,
      </if>
      <if test="staffName != null">
        staff_name,
      </if>
      <if test="ip != null">
        ip,
      </if>
      <if test="loginTime != null">
        login_time,
      </if>
      <if test="logoutTime != null">
        logout_time,
      </if>
      <if test="fkCompanyId != null">
        fk_company_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkStaffId != null">
        #{fkStaffId,jdbcType=BIGINT},
      </if>
      <if test="staffLoginId != null">
        #{staffLoginId,jdbcType=VARCHAR},
      </if>
      <if test="staffName != null">
        #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="loginTime != null">
        #{loginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="logoutTime != null">
        #{logoutTime,jdbcType=TIMESTAMP},
      </if>
      <if test="fkCompanyId != null">
        #{fkCompanyId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
    <update id="updateLogoutTime">
        UPDATE log_login SET logout_time = #{now},gmt_modified = #{now},gmt_modified_user = #{loginId} WHERE fk_staff_id = #{fkStaffId} AND logout_time IS NULL
    </update>

    <select id="getLogLogins" resultType="com.get.core.log.model.LogLogin">
    SELECT
     id,gmt_create,gmt_modified,gmt_create_user,gmt_modified_user,fk_company_id,fk_staff_id,staff_login_id,staff_name,ip,login_time,logout_time
    FROM
     log_login
    WHERE
    fk_company_id = #{logLoginVo.fkCompanyId}
    <if test=" logLoginVo.gmtCreateUser != null and logLoginVo.gmtCreateUser !=''">
      and gmt_create_user like #{logLoginVo.gmtCreateUser}
    </if>
    <if test=" logLoginVo.staffName != null and logLoginVo.staffName !=''">
      and staff_name like #{logLoginVo.staffName}
    </if>
    <if test=" logLoginVo.loginStartTime != null and logLoginVo.loginStartTime.toString() !=''">
      and DATE_FORMAT(login_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{logLoginVo.loginStartTime},'%Y-%m-%d')
    </if>
    <if test=" logLoginVo.loginEndTime != null and logLoginVo.loginEndTime.toString() !=''">
      and DATE_FORMAT(login_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{logLoginVo.loginEndTime},'%Y-%m-%d')
    </if>
    <if test=" logLoginVo.loginOutStartTime != null and logLoginVo.loginOutStartTime.toString() !=''">
      and DATE_FORMAT(logout_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{logLoginVo.loginOutStartTime},'%Y-%m-%d')
    </if>
    <if test=" logLoginVo.loginOutEndTime != null and logLoginVo.loginOutEndTime.toString() !=''">
      and DATE_FORMAT(logout_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{logLoginVo.loginOutEndTime},'%Y-%m-%d')
    </if>
     order by login_time desc
  </select>
    <select id="getStaffLoginInfo" resultType="com.get.core.log.dto.LogLoginDto">
        SELECT
            g1.id,
            g1.fk_company_id,
            g1.fk_staff_id,
            g1.staff_login_id,
            g1.staff_name,
            p.name as positionName,
            IF(g1.logout_time IS NULL,true,false) as isOnline,
            g1.ip,
            g1.login_time,
            IFNULL(
                timestampdiff(second, g1.login_time,g1.logout_time),
                0
            ) AS onlineDurationVal,
            c.short_name AS companyName,
            d.`name` AS departmentName
        FROM
            log_login g1
        INNER JOIN (
            SELECT
                fk_staff_id,
                MAX(login_time) AS max_c
            FROM
                log_login
            GROUP BY
                fk_staff_id,session_id
        ) g2 ON g2.fk_staff_id = g1.fk_staff_id
        AND g1.login_time = g2.max_c
        INNER JOIN ais_permission_center.m_staff m ON m.id = g1.fk_staff_id
        LEFT JOIN ais_permission_center.m_company c ON c.id = g1.fk_company_id
        LEFT JOIN ais_permission_center.m_department d ON d.id = m.fk_department_id
        LEFT JOIN ais_permission_center.m_position p ON p.id = m.fk_position_id
        WHERE
            m.is_on_duty = 1
            AND g1.session_id IS NOT NULL
            <if test="logLoginVo.fkCompanyId!=null and logLoginVo.fkCompanyId!=''">
                AND g1.fk_company_id = #{logLoginVo.fkCompanyId}
            </if>
            <if test="logLoginVo.staffName!=null and logLoginVo.staffName!=''">
                AND (m.login_id LIKE CONCAT('%',#{logLoginVo.staffName},'%')
                OR m.name LIKE CONCAT('%',#{logLoginVo.staffName},'%')
                OR m.name_en LIKE CONCAT('%',#{logLoginVo.staffName},'%'))
            </if>
        GROUP BY
            g1.fk_staff_id,g1.session_id
        ORDER BY
            g1.login_time DESC
    </select>
    <select id="getLoginInfoByIpAndId" resultType="com.get.core.log.model.LogLogin">
        SELECT
            g1.id
        FROM
            log_login g1
        INNER JOIN (
            SELECT
                fk_staff_id,
                MAX(login_time) AS max_c
            FROM
                log_login
            WHERE
                1=1
                <if test="sessionId!=null">
                    AND session_id = #{sessionId}
                </if>
                <if test="fkStaffId!=null">
                    AND fk_staff_id = #{fkStaffId}
                </if>

        ) g2 ON g2.fk_staff_id = g1.fk_staff_id
        AND g1.login_time = g2.max_c
        WHERE
            1=1
            <if test="sessionId!=null">
                AND g1.session_id = #{sessionId}
            </if>
            <if test="fkStaffId!=null">
                AND g1.fk_staff_id = #{fkStaffId}
            </if>
    </select>
</mapper>