package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2020/9/15
 * @TIME: 16:14
 * @Description: 新闻-公司安全配置VO
 **/
@Data
public class NewsCompanyDto  extends BaseVoEntity {
    /**
     * 新闻Id
     */
    @ApiModelProperty(value = "新闻Id")
    private Long fkNewsId;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
}
