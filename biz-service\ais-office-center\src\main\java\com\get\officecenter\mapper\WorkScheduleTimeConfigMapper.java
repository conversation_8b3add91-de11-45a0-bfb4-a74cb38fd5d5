package com.get.officecenter.mapper;

import com.get.core.mybatis.mapper.GetMapper;
import com.get.officecenter.entity.WorkScheduleTimeConfig;
import com.get.officecenter.dto.TimeConfigDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/10/27
 * @TIME: 17:57
 * @Description:
 **/
@Mapper
public interface WorkScheduleTimeConfigMapper extends GetMapper<WorkScheduleTimeConfig> {
    Integer getCount(@Param("fkCompanyId")Long fkCompanyId,@Param("fkDepartmentId") Long fkDepartmentId);


    List<WorkScheduleTimeConfig> getWorkScheduleTimeConfig(@Param("timeConfigDto") TimeConfigDto timeConfigDto);
}
