package com.get.permissioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.dao.OfficeMapper;
import com.get.permissioncenter.dto.OfficeDto;
import com.get.permissioncenter.vo.OfficeVo;
import com.get.permissioncenter.entity.Office;
import com.get.permissioncenter.service.ICompanyService;
import com.get.permissioncenter.service.IDeleteService;
import com.get.permissioncenter.service.IOfficeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/6/24
 * @TIME: 18:28
 **/
@Service
public class OfficeServiceImpl extends BaseServiceImpl<OfficeMapper, Office> implements IOfficeService {
    @Resource
    private OfficeMapper officeMapper;
    @Resource
    private ICompanyService companyService;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private UtilService utilService;

    @Override
    public List<OfficeVo> getOfficeDtos(OfficeDto officeDto) {
//        Example example = getExample(officeDto);
//        List<Office> offices = officeMapper.selectByExample(example);
//        List<OfficeVo> collect = offices.stream().map(office -> Tools.objClone(office, OfficeVo.class)).collect(Collectors.toList());
//        for (OfficeVo officeDto : collect) {
//            setCompanyName(officeDto);
//        }
//        return collect;

        LambdaQueryWrapper<Office> lambdaQueryWrapper = getLambdaQueryWrapper(officeDto);
        List<Office> officeList = officeMapper.selectList(lambdaQueryWrapper);
        List<OfficeVo> collect = BeanCopyUtils.copyListProperties(officeList, OfficeVo::new);
        for (OfficeVo officeVo : collect) {
            setCompanyName(officeVo);
        }
        return collect;
    }

    @Override
    public List<OfficeVo> getOffice(OfficeDto officeDto) {
//        Example example = getExample(officeDto);
//        List<Office> officeList = officeMapper.selectByExample(example);
//        return officeList.stream().map(office -> Tools.objClone(office, OfficeVo.class)).collect(Collectors.toList());
        LambdaQueryWrapper<Office> lambdaQueryWrapper = getLambdaQueryWrapper(officeDto);
        List<Office> officeList = officeMapper.selectList(lambdaQueryWrapper);
        return BeanCopyUtils.copyListProperties(officeList, OfficeVo::new);
    }

    //    private Example getExample(OfficeDto officeDto){
//        Example example = new Example(Office.class);
//        Example.Criteria criteria = example.createCriteria();
//        Example.Criteria criteria1 = example.createCriteria();
//        if (GeneralTool.isEmpty(officeDto) || GeneralTool.isEmpty(officeDto.getFkCompanyId())) {
//            List<Long> companyIds = getCompanyIds();
//            criteria.andIn("fkCompanyId", companyIds);
//        }
//        if (GeneralTool.isNotEmpty(officeDto)) {
//            if (GeneralTool.isNotEmpty(officeDto.getFkCompanyId())) {
//                if(!SecureUtil.validateCompany(officeDto.getFkCompanyId()))
//                {
//                    throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
//                }
//                criteria.andEqualTo("fkCompanyId", officeDto.getFkCompanyId());
//            }
//            if (GeneralTool.isNotEmpty(officeDto.getKeyWord())) {
//                criteria1.andLike("num", "%" + officeDto.getKeyWord() + "%");
//                criteria1.orLike("name", "%" + officeDto.getKeyWord() + "%");
//            }
//        }
//        example.and(criteria1);
//        example.orderBy("viewOrder").asc();
//        return example;
//    }
    private LambdaQueryWrapper<Office> getLambdaQueryWrapper(OfficeDto officeDto) {
        LambdaQueryWrapper<Office> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isEmpty(officeDto) || GeneralTool.isEmpty(officeDto.getFkCompanyId())) {
            List<Long> companyIds = getCompanyIds();
            wrapper.in(Office::getFkCompanyId, companyIds);
        }
        if (GeneralTool.isNotEmpty(officeDto)) {
            if (GeneralTool.isNotEmpty(officeDto.getFkCompanyId())) {
                if (!SecureUtil.validateCompany(officeDto.getFkCompanyId())) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
                }
                wrapper.eq(Office::getFkCompanyId, officeDto.getFkCompanyId());
            }
            if (GeneralTool.isNotEmpty(officeDto.getKeyWord())) {
                wrapper.and(wrapper_ ->
                        wrapper_.like(Office::getNum, officeDto.getKeyWord()).or()
                                .like(Office::getName, officeDto.getKeyWord()));
            }
        }
        wrapper.orderByAsc(Office::getViewOrder);
        return wrapper;
    }

    private List<Long> getCompanyIds() {
        List<Long> companyIds = SecureUtil.getCompanyIds();
        if (GeneralTool.isEmpty(companyIds)) {
            companyIds.add(0L);
        }
        return companyIds;
    }

    private void setCompanyName(OfficeVo officeVo) {
        if (GeneralTool.isNotEmpty(officeVo)) {
            if (GeneralTool.isNotEmpty(officeVo.getFkCompanyId())) {
                String name = companyService.getCompanyNameById(officeVo.getFkCompanyId());
                officeVo.setFkCompanyName(name);
            }
        }
    }

    @Override
    public List<OfficeVo> getStaffOffice(Long staffId) {
        if (GeneralTool.isEmpty(staffId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<Office> officeList = officeMapper.getStaffOfficeById(staffId);
//        return officeList.stream().map(office -> Tools.objClone(office, OfficeVo.class)).collect(Collectors.toList());
        return BeanCopyUtils.copyListProperties(officeList, OfficeVo::new);
    }

    @Override
    public OfficeVo findOfficeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
//        Office office = officeMapper.selectByPrimaryKey(id);
        Office office = officeMapper.selectById(id);
        if (GeneralTool.isNotEmpty(office)) {
            if (!SecureUtil.validateCompany(office.getFkCompanyId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }
//        return Tools.objClone(office, OfficeVo.class);
        return BeanCopyUtils.objClone(office, OfficeVo::new);
    }

    @Override
    public Long addOffice(OfficeDto officeDto) {
        if (GeneralTool.isEmpty(officeDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (!validateChange(officeDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("num_exist"));
        }
//        Office office = Tools.objClone(officeDto, Office.class);
//        utilService.setCreateInfo(office);
        Office office = BeanCopyUtils.objClone(officeDto, Office::new);
        utilService.updateUserInfoToEntity(office);
        Integer maxViewOrder = officeMapper.getMaxViewOrder();
        maxViewOrder = GeneralTool.isEmpty(maxViewOrder) ? 0 : maxViewOrder;
        office.setViewOrder(maxViewOrder);
        int i = officeMapper.insertSelective(office);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return office.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAddOffice(List<OfficeDto> officeDtos) {
        for (OfficeDto officeDto : officeDtos) {
            if (GeneralTool.isEmpty(officeDto.getId())) {
                addOffice(officeDto);
            } else {
                updateOfficeVo(officeDto);
            }

        }
    }

    @Override
    public List<BaseSelectEntity> getOfficeSelect(Long companyId) {
//        SecureUtil.validateCompany(companyId);
        if (!SecureUtil.validateCompany(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        return officeMapper.getOfficeSelect(companyId);
    }

    @Override
    public String getOfficeNameById(Long officeId) {
        return officeMapper.getOfficeNameById(officeId);
    }

    @Override
    public OfficeVo updateOfficeVo(OfficeDto officeDto) {
        if (GeneralTool.isEmpty(officeDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Long aLong = validateUpdate(officeDto);
        if (!officeDto.getId().equals(aLong)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("num_exist"));

        }
//        Office office = Tools.objClone(officeDto, Office.class);
//        utilService.setUpdateInfo(office);
//        officeMapper.updateByPrimaryKeySelective(office);
        Office office = BeanCopyUtils.objClone(officeDto, Office::new);
        utilService.updateUserInfoToEntity(office);
        officeMapper.updateById(office);
        return findOfficeById(officeDto.getId());
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        deleteService.deleteValidateOffice(id);
//        int i = officeMapper.deleteByPrimaryKey(id);
        int i = officeMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<OfficeDto> officeDtos) {
        if (GeneralTool.isEmpty(officeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
//        Office office1 = Tools.objClone(officeDtos.get(0), Office.class);
//        Office office2 = Tools.objClone(officeDtos.get(1), Office.class);
        Office office1 = BeanCopyUtils.objClone(officeDtos.get(0), Office::new);
        Office office2 = BeanCopyUtils.objClone(officeDtos.get(1), Office::new);
        Integer viewOrder = office1.getViewOrder();
        office1.setViewOrder(office2.getViewOrder());
        office2.setViewOrder(viewOrder);
//        utilService.setUpdateInfo(office1);
//        utilService.setUpdateInfo(office2);
        utilService.updateUserInfoToEntity(office1);
        utilService.updateUserInfoToEntity(office2);
//        officeMapper.updateByPrimaryKeySelective(office1);
//        officeMapper.updateByPrimaryKeySelective(office2);
        officeMapper.updateById(office1);
        officeMapper.updateById(office2);
    }

    //新增校验是否存在
    private boolean validateChange(OfficeDto officeDto) {
//        Example example = new Example(Office.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("num", officeDto.getNum());
//        criteria.andEqualTo("fkCompanyId", officeDto.getFkCompanyId());
//        List<Office> offices = officeMapper.selectByExample(example);
        List<Office> offices = officeMapper.selectList(Wrappers.<Office>query().lambda().eq(Office::getNum, officeDto.getNum()).eq(Office::getFkCompanyId, officeDto.getFkCompanyId()));
        return GeneralTool.isEmpty(offices);
    }

    //编辑校验是否存在
    private Long validateUpdate(OfficeDto officeDto) {
//        Example example = new Example(Office.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("num", officeDto.getNum());
//        criteria.andEqualTo("fkCompanyId", officeDto.getFkCompanyId());
//        List<Office> offices = officeMapper.selectByExample(example);
        List<Office> offices = officeMapper.selectList(Wrappers.<Office>query().lambda().eq(Office::getNum, officeDto.getNum()).eq(Office::getFkCompanyId, officeDto.getFkCompanyId()));
        return GeneralTool.isNotEmpty(offices) ? offices.get(0).getId() : officeDto.getId();
    }

    @Override
    public OfficeVo getOfficeById(Long officeId) {
//        Office office = officeMapper.selectByPrimaryKey(officeId);
        Office office = officeMapper.selectById(officeId);
//        OfficeVo officeVo = Tools.objClone(office, OfficeVo.class);
        OfficeVo officeVo = BeanCopyUtils.objClone(office, OfficeVo::new);
        return officeVo;
    }

    @Override
    public Map<Long, String> getofficeNamesByIds(Set<Long> officeIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(officeIds)) {
            return map;
        }
//        Example example = new Example(Office.class);
//        example.createCriteria().andIn("id",officeIds);
//        List<Office> offices = officeMapper.selectByExample(example);

        List<Office> offices = officeMapper.selectBatchIds(officeIds);
        if (GeneralTool.isEmpty(offices)) {
            return map;
        }
        for (Office office : offices) {
            map.put(office.getId(), office.getName());
        }
        return map;
    }
}
