package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.entity.StudentEventType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface StudentEventTypeMapper extends BaseMapper<StudentEventType> {

    int insert(StudentEventType record);

    int insertSelective(StudentEventType record);

    Integer getMaxViewOrder();

    List<BaseSelectEntity> getStudentEventTypeSelect();

    List<BaseSelectEntity> getClientEventTypeSelect();
}