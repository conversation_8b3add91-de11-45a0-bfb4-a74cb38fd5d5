package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.entity.BusinessProviderType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface BusinessProviderTypeMapper extends BaseMapper<BusinessProviderType>, GetMapper<BusinessProviderType> {
    Integer getMaxViewOrder();

    List<BaseSelectEntity> selectBusinessProviderType();
}
