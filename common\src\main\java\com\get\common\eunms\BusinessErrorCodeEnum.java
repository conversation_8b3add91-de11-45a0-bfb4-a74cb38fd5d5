package com.get.common.eunms;

import com.fasterxml.jackson.annotation.JsonValue;

public enum BusinessErrorCodeEnum{

    LABEL_EXISTENCE("1009", "标签存在数据");


    private String code;
    private String message;

    BusinessErrorCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @JsonValue
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getValue() {
        return this.code;
    }

}
