package com.get.examcenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * Created by <PERSON>.
 * Time: 14:32
 * Date: 2021/8/24
 * Description:考题分配VO
 */
@Data
public class ExaminationQuestionAssignDto extends BaseVoEntity implements Serializable {
    /**
     * 考卷Id
     */
    @ApiModelProperty(value = "考卷Id")
    @NotNull(message = "考卷Id不能为空", groups = {Add.class, Update.class})
    private Long fkExaminationPaperId;

    /**
     * 分配目标类型：0考题类型/1考题
     */
    @ApiModelProperty(value = "分配目标类型：0考题类型/1考题")
    @NotNull(message = "分配目标类型不能为空", groups = {Add.class, Update.class})
    private Integer targetType;

    /**
     * 分配目标id
     */
    @ApiModelProperty(value = "分配目标id")
    @NotNull(message = "分配目标id不能为空", groups = {Add.class, Update.class})
    private Long targetId;
}
