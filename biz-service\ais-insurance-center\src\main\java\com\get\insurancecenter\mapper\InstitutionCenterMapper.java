package com.get.insurancecenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.get.institutioncenter.entity.AreaCity;
import com.get.institutioncenter.entity.AreaCountry;
import com.get.institutioncenter.entity.AreaState;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:
 */
@DS("institution")
@Mapper
public interface InstitutionCenterMapper {

    /**
     * 查询国家列表
     *
     * @param countryIds
     * @return
     */
    List<AreaCountry> selectAreaCountryList(@Param("countryIds") List<Long> countryIds);

    /**
     * 查询州省列表
     *
     * @param stateIds
     * @return
     */
    List<AreaState> selectAreaStateList(@Param("stateIds") List<Long> stateIds);

    /**
     * 查询城市列表
     *
     * @param cityIds
     * @return
     */
    List<AreaCity> selectAreaCityList(@Param("cityIds") List<Long> cityIds);

}
