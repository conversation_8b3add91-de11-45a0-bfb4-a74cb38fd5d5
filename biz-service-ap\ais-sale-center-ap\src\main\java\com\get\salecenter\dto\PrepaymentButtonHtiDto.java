package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/6/13 17:48
 */
@Data
public class PrepaymentButtonHtiDto extends BaseVoEntity  {

    @ApiModelProperty(value = "应付计划id")
    @NotNull(message = "应收计划id不能为空")
    private Long fkReceivablePlanId;

    @ApiModelProperty(value = "预付金额百分比" )
    @NotNull(message = "预付金额百分比不能为空", groups = {Add.class})
    private BigDecimal payInAdvancePercent;

    @ApiModelProperty(value = "发票id")
    @NotNull(message = "发票id不能为空")
    private Long fkInvoiceId;

    @NotNull(message = "发票绑定金额不能为空")
    @ApiModelProperty("发票绑定金额")
    private BigDecimal subtotal;

    @NotNull(message = "发票应收计划绑定关系id不能为空")
    @ApiModelProperty(value = "发票应收计划绑定关系id")
    private Long invoiceReceivablePlanRelationId;

    @ApiModelProperty(value = "学生名")
    private String studentName;

    
}
