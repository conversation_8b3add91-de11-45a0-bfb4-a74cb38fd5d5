package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.StudentProjectRole;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/11/2 16:24
 * @verison: 1.0
 * @description:
 */
@Data
public class StudentProjectRoleVo extends BaseEntity {
    /**
     * 所属公司名称
     */
    @ApiModelProperty(value = "所属公司名称")
    private String companyName;

    /**
     * 涉及部门名称集合
     */
    @ApiModelProperty(value = "涉及部门名称集合")
    private List<String> departmentNameList;
    /**
     * 格式化名
     */
    @ApiModelProperty(value = "格式化名")
    private String formatName;

    //=========实体类StudentProjectRole=============
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;
    /**
     * 角色名称
     */
    @ApiModelProperty(value = "角色名称")
    @Column(name = "role_name")
    private String roleName;
    /**
     * 角色Key(系统使用)
     */
    @ApiModelProperty(value = "角色Key(系统使用)")
    @Column(name = "role_key")
    private String roleKey;
    /**
     * 部门编号引用，可以多选，用逗号隔开，如：D001,D002
     */
    @ApiModelProperty(value = "部门编号引用，可以多选，用逗号隔开，如：D001,D002")
    @Column(name = "department_num")
    private String departmentNum;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}
