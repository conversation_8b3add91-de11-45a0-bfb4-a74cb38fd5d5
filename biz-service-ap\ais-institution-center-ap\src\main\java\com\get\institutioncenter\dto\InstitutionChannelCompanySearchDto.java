package com.get.institutioncenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class InstitutionChannelCompanySearchDto {

    @ApiModelProperty("渠道提供商名称")
    private String name;

    @ApiModelProperty("公司ids")
    @NotEmpty(message = "公司ids不能为空")
    private List<Long> fkCompanyIds;
}
