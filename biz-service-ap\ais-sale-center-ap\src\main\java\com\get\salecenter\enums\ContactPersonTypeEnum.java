package com.get.salecenter.enums;

import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.logging.log4j.util.Strings;

import java.util.*;

/**
 * 联系人类型
 *
 * <AUTHOR>
 * @Date 2025/6/27 下午3:36
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum ContactPersonTypeEnum {

    /**
     * 顾问（Counselor）
     */
    SALES("CONTACT_AGENT_SALES", "顾问（Counselor）", false, "COUNSELOR", 12L),

    /**
     * 财务（Accountant）
     */
    FINANCE("CONTACT_AGENT_FINANCE", "财务（Accountant）", false, null, null),

    /**
     * 管理者（Management Team）
     */
    VP("CONTACT_AGENT_VP", "管理者（Management Team）", false, null, null),

    /**
     * 文案（Application & Visa Specialist）
     */
    COPYWRITING("CONTACT_AGENT_COPYWRITING", "文案（Application & Visa Specialist）", false, "DOCUMENT", 14L),

    /**
     * 企业管理员（Enterprise Administrator）
     */
    ADMIN("CONTACT_AGENT_ADMIN", "企业管理员（Enterprise Administrator）", false, "CHIEF", 4L),

    /**
     * 佣金结算负责人（Commission Settlement Manager）
     */
    COMMISSION("CONTACT_AGENT_COMMISSION", "佣金结算负责人（Commission Settlement Manager）", false, "FINANCE", 10L),

    /**
     * 【New】紧急联系人（Emergency Contact）
     */
    EMERGENCY("CONTACT_AGENT_EMERGENCY", "【New】紧急联系人（Emergency Contact）", false, null, null);

    /**
     * 唯一标识码
     */
    private final String code;

    /**
     * 联系人类型的显示名称
     */
    private final String msg;

    /**
     * 是否为佣金邮件接收地址
     */
    private final Boolean defaultIsCommissionEmail;

    /**
     * 合作伙伴角色编码
     */
    private final String partnerRoleCode;

    /**
     * 合作伙伴角色ID
     */
    private final Long partnerRoleId;

    /**
     * 联系人枚举类型映射表
     */
    private static final Map<String, ContactPersonTypeEnum> CONTACT_PERSON_TYPE_MAP = new HashMap<>();

    static {
        for (ContactPersonTypeEnum contactPersonTypeEnum : ContactPersonTypeEnum.values()) {
            CONTACT_PERSON_TYPE_MAP.put(contactPersonTypeEnum.getCode(), contactPersonTypeEnum);
        }
    }

    /**
     * 根据唯一表示获取对应的联系人类型枚举实例
     *
     * @param code 联系人类型
     * @return 对应的联系人类型枚举实例，如果找不到则返回null
     */
    public static ContactPersonTypeEnum getContactPersonTypeByCode(String code) {
        return CONTACT_PERSON_TYPE_MAP.get(code);
    }

    /**
     * 获取指定联系人类型的默认佣金邮件接收设置
     *
     * @param code 联系人类型
     * @return true表示默认接收佣金邮件，false表示默认不接收
     */
    public static Boolean getDefaultIsCommissionEmail(String code) {
        ContactPersonTypeEnum typeEnum = getContactPersonTypeByCode(code);
        return typeEnum != null ? typeEnum.getDefaultIsCommissionEmail() : Boolean.FALSE;
    }

    /**
     * 判断给定的联系人类型是否为新增类型
     * 新增类型包括：企业管理员、佣金结算负责人、紧急联系人
     *
     * @param code 联系人类型
     * @return true表示是新增类型，false表示是传统类型
     */
    public static boolean isNewContactPersonType(String code) {
        if (Strings.isBlank(code)) {
            return false;
        }
        ContactPersonTypeEnum contactPersonTypeEnum = getContactPersonTypeByCode(code);
        if (contactPersonTypeEnum == null) {
            return false;
        }
        switch (contactPersonTypeEnum) {
            case ADMIN:
            case COMMISSION:
            case EMERGENCY:
                return true;
            default:
                return false;
        }
    }

    /**
     * 校验新申请的联系人类型是否包含所需的三种类型(ADMIN, COMMISSION, EMERGENCY)
     *
     * @param contactPersonTypeCodes 联系人类型代码列表
     * @return true表示校验通过，false表示校验失败
     */
    public static boolean validateRequiredContactPersonTypes(List<String> contactPersonTypeCodes) {
        if (contactPersonTypeCodes == null || contactPersonTypeCodes.size() != 3) {
            return false;
        }

        boolean hasAdmin = false;
        boolean hasCommission = false;
        boolean hasEmergency = false;

        for (String typeCode : contactPersonTypeCodes) {
            ContactPersonTypeEnum typeEnum = getContactPersonTypeByCode(typeCode);
            if (typeEnum == null) {
                return false;
            }

            switch (typeEnum) {
                case ADMIN:
                    hasAdmin = true;
                    break;
                case COMMISSION:
                    hasCommission = true;
                    break;
                case EMERGENCY:
                    hasEmergency = true;
                    break;
                default:
                    return false;
            }
        }

        return hasAdmin && hasCommission && hasEmergency;
    }

    /**
     * 检查类型组合是否存在冲突
     * 紧急联系人不能与管理员或佣金结算负责人同时存在
     *
     * @param typeKeys 类型键集合
     * @return true: 存在冲突, false: 不存在冲突
     */
    public static boolean hasConflictingNewTypes(Set<String> typeKeys) {
        // 如果集合为空或元素少于2个，不可能存在冲突
        if (CollectionUtil.isEmpty(typeKeys) || typeKeys.size() < 2) {
            return false;
        }

        // 如果不包含紧急联系人类型，则不存在冲突
        if (!typeKeys.contains(EMERGENCY.getCode())) {
            return false;
        }

        // 检查是否同时包含ADMIN或COMMISSION
        return typeKeys.contains(ADMIN.getCode()) || typeKeys.contains(COMMISSION.getCode());
    }

    /**
     * 获取新联系人类型的数量
     *
     * @param typeKeyStr
     * @return
     */
    public static long getNewTypeCount(String typeKeyStr) {
        if (StringUtil.isBlank(typeKeyStr)) {
            return 0;
        }
        long newTypeCount = Arrays.stream(typeKeyStr.split(","))
                .map(String::trim)
                .filter(ContactPersonTypeEnum::isNewContactPersonType)
                .count();

        return newTypeCount;
    }

    /**
     * 判断是否有冲突的联系人类型
     *
     * @param typeKey
     * @return
     */
    public static boolean hasConflictingNewTypes(String typeKey) {
        if (StringUtil.isBlank(typeKey)) {
            return false;
        }
        if (!typeKey.contains(EMERGENCY.getCode())) {
            return false;
        }

        return typeKey.contains(ADMIN.getCode()) || typeKey.contains(COMMISSION.getCode());
    }

}
