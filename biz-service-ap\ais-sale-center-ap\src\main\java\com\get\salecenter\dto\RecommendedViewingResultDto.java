package com.get.salecenter.dto;

import com.get.salecenter.vo.MediaAndAttachedVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class RecommendedViewingResultDto {

    @ApiModelProperty(value = "学校id")
    private Long fkInstitutionId;

    @ApiModelProperty(value = "学校中文名称")
    private String institutionChnName;

    @ApiModelProperty(value = "学校英文名称")
    private String institutionName;

    @ApiModelProperty(value = "课程名称")
    private List<String> courseName;

    @ApiModelProperty(value = "学校logo")
    private MediaAndAttachedVo mediaAndAttachedDto;
}
