package com.get.pmpcenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.utils.SecureUtil;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.pmpcenter.dto.agent.ApprovalAgentCompanyDto;
import com.get.pmpcenter.dto.agent.SubmitAgentApprovalDto;
import com.get.pmpcenter.entity.AgentCommissionPlan;
import com.get.pmpcenter.entity.InstitutionProviderCompanyAgent;
import com.get.pmpcenter.entity.InstitutionProviderCompanyAgentApproval;
import com.get.pmpcenter.enums.ApprovalStatusEnum;
import com.get.pmpcenter.mapper.InstitutionProviderCompanyAgentApprovalMapper;
import com.get.pmpcenter.mapper.InstitutionProviderCompanyAgentMapper;
import com.get.pmpcenter.mapper.PermissionCenterMapper;
import com.get.pmpcenter.service.AgentCommissionPlanService;
import com.get.pmpcenter.service.InstitutionProviderCompanyAgentApprovalService;
import com.get.pmpcenter.vo.common.StaffVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * @Author:Oliver
 * @Date: 2025/3/31
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class InstitutionProviderCompanyAgentApprovalImpl extends ServiceImpl<InstitutionProviderCompanyAgentApprovalMapper, InstitutionProviderCompanyAgentApproval> implements InstitutionProviderCompanyAgentApprovalService {

    @Autowired
    private InstitutionProviderCompanyAgentApprovalMapper providerCompanyAgentApprovalMapper;
    @Autowired
    private InstitutionProviderCompanyAgentMapper providerCompanyAgentMapper;
    @Autowired
    private IPermissionCenterClient permissionCenterClient;
    @Autowired
    private PermissionCenterMapper permissionCenterMapper;
    @Autowired
    private AgentCommissionPlanService agentCommissionPlanService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public InstitutionProviderCompanyAgent initApproveRecord(Long institutionProviderId, Long companyId) {
        InstitutionProviderCompanyAgent companyAgent = providerCompanyAgentMapper.selectOne(new LambdaQueryWrapper<InstitutionProviderCompanyAgent>()
                .eq(InstitutionProviderCompanyAgent::getFkInstitutionProviderId, institutionProviderId)
                .eq(InstitutionProviderCompanyAgent::getFkCompanyId, companyId));
        if (Objects.isNull(companyAgent)) {
            InstitutionProviderCompanyAgent newCompanyAgent = InstitutionProviderCompanyAgent.builder()
                    .fkInstitutionProviderId(institutionProviderId)
                    .fkCompanyId(companyId)
                    .isLocked(0)
                    .approvalStatus(ApprovalStatusEnum.UN_COMMITTED.getCode())
                    .build();
            newCompanyAgent.setGmtCreate(new Date());
            newCompanyAgent.setGmtCreateUser(SecureUtil.getLoginId());
            newCompanyAgent.setGmtModified(new Date());
            newCompanyAgent.setGmtModifiedUser(SecureUtil.getLoginId());
            providerCompanyAgentMapper.insert(newCompanyAgent);
            return newCompanyAgent;
        }
        return companyAgent;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitAgentApproval(SubmitAgentApprovalDto submitApprovalDto) {
        InstitutionProviderCompanyAgent companyAgent = initApproveRecord(submitApprovalDto.getInstitutionProviderId(), submitApprovalDto.getCompanyId());
        if (companyAgent.getApprovalStatus().equals(ApprovalStatusEnum.PENDING_APPROVAL.getCode())) {
//            throw new GetServiceException("该分公司的审批已提交，请勿重复提交");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"PMP_PLAN_SUBMITTED_COMPANY","该分公司的审批已提交，请勿重复提交"));
        }
        //新增审核记录
        InstitutionProviderCompanyAgentApproval approval = InstitutionProviderCompanyAgentApproval.builder()
                .fkInstitutionProviderId(submitApprovalDto.getInstitutionProviderId())
                .fkCompanyId(submitApprovalDto.getCompanyId())
                .fkStaffId(submitApprovalDto.getStaffId())
                .approvalStatus(ApprovalStatusEnum.PENDING_APPROVAL.getCode())
                .build();
        approval.setGmtCreate(new Date());
        approval.setGmtCreateUser(SecureUtil.getLoginId());
        approval.setGmtModified(new Date());
        approval.setGmtModifiedUser(SecureUtil.getLoginId());
        providerCompanyAgentApprovalMapper.insert(approval);
        companyAgent.setApprovalStatus(ApprovalStatusEnum.PENDING_APPROVAL.getCode());
        companyAgent.setGmtModified(new Date());
        companyAgent.setGmtModifiedUser(SecureUtil.getLoginId());
        companyAgent.setIsLocked(1);
        providerCompanyAgentMapper.updateById(companyAgent);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approvalApprovalAgentCompany(ApprovalAgentCompanyDto approvalAgentCompanyDto) {
        InstitutionProviderCompanyAgent companyAgent = providerCompanyAgentMapper.selectOne(new LambdaQueryWrapper<InstitutionProviderCompanyAgent>()
                .eq(InstitutionProviderCompanyAgent::getFkInstitutionProviderId, approvalAgentCompanyDto.getInstitutionProviderId())
                .eq(InstitutionProviderCompanyAgent::getFkCompanyId, approvalAgentCompanyDto.getCompanyId()));
        if (Objects.isNull(companyAgent)) {
//            throw new GetServiceException("审批信息不存在");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"PMP_PLAN_RECORD_NOT_FOUND","审批信息不存在"));
        }
        List<InstitutionProviderCompanyAgentApproval> agentApprovalList = getAgentApprovalList(approvalAgentCompanyDto.getInstitutionProviderId(), approvalAgentCompanyDto.getCompanyId());
        if (CollectionUtils.isEmpty(agentApprovalList)) {
//            throw new GetServiceException("审批记录不存在");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"PMP_PLAN_RECORD_NOT_FOUND","审批信息不存在"));
        }
        if (!agentApprovalList.get(0).getFkStaffId().equals(SecureUtil.getStaffId())) {
            log.error("审批失败,非合同指定审批人,审批参数：{},审批人ID：{},,登录人id：{}", JSON.toJSONString(approvalAgentCompanyDto),
                    agentApprovalList.get(0).getFkStaffId(), SecureUtil.getStaffId());
//            throw new GetServiceException("审核失败,当前用户不是指定审批人");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"PMP_NOT_APPROVER","审核失败,当前用户不是指定审批人"));
        }
        InstitutionProviderCompanyAgentApproval approval = InstitutionProviderCompanyAgentApproval.builder()
                .fkInstitutionProviderId(approvalAgentCompanyDto.getInstitutionProviderId())
                .fkCompanyId(approvalAgentCompanyDto.getCompanyId())
                .fkStaffId(SecureUtil.getStaffId())
                .approvalStatus(approvalAgentCompanyDto.getApprovalStatus())
                .approvalComment(approvalAgentCompanyDto.getApprovalComment())
                .approvalTime(new Date())
                .build();
        approval.setGmtCreate(new Date());
        approval.setGmtCreateUser(SecureUtil.getLoginId());
        approval.setGmtModified(new Date());
        approval.setGmtModifiedUser(SecureUtil.getLoginId());
        providerCompanyAgentApprovalMapper.insert(approval);

        companyAgent.setApprovalStatus(approvalAgentCompanyDto.getApprovalStatus());
        companyAgent.setGmtModified(new Date());
        companyAgent.setGmtModifiedUser(SecureUtil.getLoginId());
        providerCompanyAgentMapper.updateById(companyAgent);
    }

    @Override
    public List<InstitutionProviderCompanyAgentApproval> getAgentApprovalList(Long institutionProviderId, Long companyId) {
        return providerCompanyAgentApprovalMapper.getApprovalList(institutionProviderId, companyId);
    }

    @Override
    public Integer getAgentApprovalPermission(Long institutionProviderId, Long companyId) {
        int planCount = agentCommissionPlanService.getBaseMapper().selectCount(new LambdaQueryWrapper<AgentCommissionPlan>()
                .eq(AgentCommissionPlan::getFkInstitutionProviderId, institutionProviderId)
                .exists(Objects.nonNull(companyId), "select id from r_agent_commission_plan_company where fk_agent_commission_plan_id = m_agent_commission_plan.id and fk_company_id = " + companyId));
        if (planCount < 1) {
            return 0;
        }
        //0:还没有新增合同 1:有提交审核的权限 2:已提交审核但是没有权限审核 3:已提交审核且有权限审核
        InstitutionProviderCompanyAgent companyAgent = initApproveRecord(institutionProviderId, companyId);
        if (companyAgent.getApprovalStatus().equals(ApprovalStatusEnum.UN_COMMITTED.getCode())
                || companyAgent.getApprovalStatus().equals(ApprovalStatusEnum.REJECT.getCode())
                || companyAgent.getApprovalStatus().equals(ApprovalStatusEnum.PASS.getCode())) {
            return 1;
        }
        if (companyAgent.getApprovalStatus().equals(ApprovalStatusEnum.PENDING_APPROVAL.getCode())) {
            //已经提交审核，查询最新一条的待审核记录
            List<InstitutionProviderCompanyAgentApproval> approvalList = providerCompanyAgentApprovalMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCompanyAgentApproval>()
                    .eq(InstitutionProviderCompanyAgentApproval::getFkInstitutionProviderId, institutionProviderId)
                    .eq(InstitutionProviderCompanyAgentApproval::getFkCompanyId, companyId)
                    .eq(InstitutionProviderCompanyAgentApproval::getApprovalStatus, ApprovalStatusEnum.PENDING_APPROVAL.getCode())
                    .orderByDesc(InstitutionProviderCompanyAgentApproval::getGmtCreate));
            if (CollectionUtils.isEmpty(approvalList)) {
                return 1;
            }
            Long fkStaffId = approvalList.get(0).getFkStaffId();
            if (fkStaffId.equals(SecureUtil.getStaffId())) {
                return 3;
            } else {
                return 2;
            }
        }
        return 1;
    }

    @Override
    public List<StaffVo> getAgentStaff() {
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_PMP_APPROVE.key, 2).getData();
        log.info("CompanyConfigMap: {}", JSONObject.toJSONString(companyConfigMap));
        if (Objects.nonNull(companyConfigMap) && companyConfigMap.containsKey(SecureUtil.getFkCompanyId())) {
            String examineIds = companyConfigMap.get(SecureUtil.getFkCompanyId());
            List<Long> list = JSON.parseArray(examineIds, Long.class);
            if (CollectionUtils.isEmpty(list)) {
                log.error("未获取到审批人配置信息,分公司ID：{}", SecureUtil.getFkCompanyId());
                return new ArrayList<>();
            }
            return permissionCenterMapper.getStaffList(list, SecureUtil.getFkCompanyId());
        } else {
            log.info("未获取到审批人配置信息,分公司ID：{}", SecureUtil.getFkCompanyId());
            return new ArrayList<>();
        }
    }
}
