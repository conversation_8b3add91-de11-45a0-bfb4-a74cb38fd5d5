<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.RStudentIssueStudentMapper">

    <select id="getRStudentIssueStudentByStudentId" resultType="com.get.salecenter.entity.RStudentIssueStudent">
        SELECT * FROM r_student_issue_student
        <where>
            <if test="fkStudentId != null">
                fk_student_id = #{fkStudentId}
            </if>
        </where>
    </select>
    <select id="getRStudentIssueStudent" resultType="com.get.salecenter.entity.RStudentIssueStudent">
        SELECT * FROM r_student_issue_student
        <where>
            <if test="fkStudentIds != null and fkStudentIds.size()>0">
                fk_student_id IN
                <foreach collection="fkStudentIds" item="fkStudentId" open="(" separator="," close=")">
                #{fkStudentId}
                </foreach>
            </if>
        </where>
    </select>
</mapper>