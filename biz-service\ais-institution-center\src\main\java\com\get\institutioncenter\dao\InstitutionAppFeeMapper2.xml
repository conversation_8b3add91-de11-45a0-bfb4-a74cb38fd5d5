<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionAppFeeMapper2">
    <select id="getWcInstitutionAppFeeList" resultType="com.get.institutioncenter.vo.InstitutionAppFeeVo2">
        select mis.id,mis.fk_institution_id,GROUP_CONCAT(mis.public_level)as public_level from m_institution_app_fee2 mis
        left join m_institution mi on mi.id = mis.fk_institution_id
        left join r_institution_view_order vo on vo.fk_institution_id=mis.fk_institution_id
        where mis.is_active=1 and mi.fk_area_country_id=#{fkCountryId} and vo.type=0
        <if test="schoolName !=''">
            and(
            mi.name like concat('%',#{schoolName},'%')
            or mi.name_chn like concat('%',#{schoolName},'%')
            or mi.short_name like concat('%',#{schoolName},'%')
            or mi.short_name_chn like concat('%',#{schoolName},'%')
            or CONCAT(mi.name,'（',mi.name_chn,'）')like concat('%',#{schoolName},'%'))
        </if>
        group by mis.fk_institution_id order by vo.view_order asc
    </select>
    <select id="getWcInstitutionAppFeeDatas" resultType="com.get.institutioncenter.vo.InstitutionAppFeeVo2">
        select * from m_institution_app_fee2 mis
        left join r_institution_view_order vo on vo.fk_institution_id=mis.fk_institution_id
        where 1=1
        <if test="data.fkInstitutionId!=null">
            and mis.fk_institution_id=#{data.fkInstitutionId}
        </if>
        <if test="data.levelType!=null">
            and mis.level_type=#{data.levelType}
        </if>
        <if test="data.isFree!=null">
            and mis.is_free=#{data.isFree}
        </if>
        <if test="fkInstitutionIds!=null and fkInstitutionIds.size()>0">
            and mis.fk_institution_id in
            <foreach collection="fkInstitutionIds" item="datas" index="index" open="(" separator="," close=")">
                #{datas}
            </foreach>
        </if>
        order by vo.view_order asc
    </select>
</mapper>