package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.ConventionPersonProcedureVo;
import com.get.salecenter.vo.ConventionPersonVo;
import com.get.salecenter.entity.ConventionPersonProcedure;
import com.get.salecenter.dto.ConventionPersonProcedureDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/27 12:21
 * @verison: 1.0
 * @description: 参展人员和峰会流程关联中间表mapper
 */
@Mapper
public interface ConventionPersonProcedureMapper extends BaseMapper<ConventionPersonProcedure>, GetMapper<ConventionPersonProcedure> {
    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insert(ConventionPersonProcedure record);

    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(ConventionPersonProcedure record);

    /**
     * 通过参会人员id 和 流程id查找对应参会流程配置id
     *
     * @param personId
     * @param procedureId
     * @return
     */
    Long getConventionPersonProcedureId(@Param("personId") Long personId, @Param("procedureId") Long procedureId);

    /**
     * @return java.util.List<com.get.salecenter.vo.ConventionPersonProcedureVo>
     * @Description :根据流程id 条件查找对应参加流程的人员信息
     * @Param [procedureId, conventionPersonProcedureDto]
     * <AUTHOR>
     */
    List<ConventionPersonProcedureVo> getConventionPerson(IPage<ConventionPersonProcedureVo> iPage, @Param("procedureId") Long procedureId, @Param("conventionPersonProcedureDto") ConventionPersonProcedureDto conventionPersonProcedureDto, @Param("bdCodeList") List<String> bdCodeList);

    /**
     * @return java.util.List<com.get.salecenter.vo.ConventionPersonProcedureVo>
     * @Description :根据流程id 条件查找对应不参加流程的人员信息
     * @Param [personIds, conventionPersonProcedureDto, conventionId]
     * <AUTHOR>
     */
    List<ConventionPersonProcedureVo> getNotJoinConventionPerson(IPage<ConventionPersonProcedureVo> iPage, @Param("personIds") List<Long> personIds, @Param("conventionPersonProcedureDto") ConventionPersonProcedureDto conventionPersonProcedureDto, @Param("bdCodeList") List<String> bdCodeList, @Param("conventionId") Long conventionId);

    /**
     * @return java.lang.Boolean
     * @Description :
     * @Param [fieldName, id]
     * <AUTHOR>
     */
    Boolean conventionPersonProcedureIsEmpty(@Param("fieldName") String fieldName, @Param("id") Long id);

    /**
     * 参会人对应参与流程map
     *
     * @param personIds
     * @return
     */
    List<ConventionPersonVo> getConventionPersonProceduresMap(@Param("personIds") List<Long> personIds);
}
