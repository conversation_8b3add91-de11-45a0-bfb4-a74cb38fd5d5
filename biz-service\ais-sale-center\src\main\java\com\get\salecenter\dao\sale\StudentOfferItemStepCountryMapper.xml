<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.StudentOfferItemStepCountryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.StudentOfferItemStepCountry">
        <id column="id" property="id" />
        <result column="fk_student_offer_item_step_id" property="fkStudentOfferItemStepId" />
        <result column="fk_student_offer_item_step_id_precondition" property="fkStudentOfferItemStepIdPrecondition" />
        <result column="fk_area_country_ids" property="fkAreaCountryIds" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_create_user" property="gmtCreateUser" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="gmt_modified_user" property="gmtModifiedUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, fk_student_offer_item_step_id, fk_student_offer_item_step_id_precondition, fk_area_country_ids, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
    </sql>

</mapper>
