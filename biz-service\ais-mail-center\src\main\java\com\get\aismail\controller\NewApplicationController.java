package com.get.aismail.controller;

import com.get.aismail.service.impl.NewApplicationServiceImpl;
import com.get.aismail.vo.AddNewStudentVo;
import com.get.aismail.vo.AllMailVo;
import com.get.aismail.vo.DownloadAttachedVo;
import com.get.common.result.ResponseBo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

@Api(tags = "新增申请")
@RestController
@RequestMapping("/addNewAppliction")
public class NewApplicationController {
    @Resource
    private NewApplicationServiceImpl newApplicationService;

    @ApiOperation(value = "新增申请")
    @PostMapping("/addNewStudent")
    public ResponseBo<AddNewStudentVo> addNewStudent(@RequestBody DownloadAttachedVo downloadAttachedVo) throws Exception {
        return new ResponseBo<>(newApplicationService.addNewStudent(downloadAttachedVo));
    }
}
