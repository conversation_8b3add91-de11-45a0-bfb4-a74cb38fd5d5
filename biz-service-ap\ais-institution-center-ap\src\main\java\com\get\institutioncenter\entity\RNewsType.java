package com.get.institutioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_news_type")
public class RNewsType extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 新闻Id
     */
    @ApiModelProperty(value = "新闻Id")
    @Column(name = "fk_news_id")
    private Long fkNewsId;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    @Column(name = "fk_table_name")
    private String fkTableName;
    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    @Column(name = "fk_table_id")
    private Long fkTableId;
    /**
     * 新闻类型Id
     */
    @ApiModelProperty(value = "新闻类型Id")
    @Column(name = "fk_news_type_id")
    private Long fkNewsTypeId;
}