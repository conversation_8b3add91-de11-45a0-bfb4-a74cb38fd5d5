package com.get.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.time.LocalDateTime;

@Data
@TableName("M_MAIL_ATTACHED")
public class MMailAttached {
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "唯一主键")
    @Column(name = "id")
    private Long id;

    @ApiModelProperty(value = "平台应用对应的创建用户Id，AIS=fk_staff_id / PARTNER=fk_partner_user_id")
    @Column(name = "fk_mail_account_id")
    private Long fkMailAccountId;

    @ApiModelProperty(value = "用户邮件Id")
    @Column(name = "fk_mail_id")
    private Long fkMailId;

    @ApiModelProperty(value = "用户邮件Id")
    @Column(name = "mail_id")
    private String mailId;

    @ApiModelProperty(value = "附件Id（邮件服务器自带邮件附件Id）")
    @Column(name = "file_id")
    private String fileId;

    @ApiModelProperty(value = "附件名称")
    @Column(name = "file_name")
    private String fileName;

    @ApiModelProperty(value = "附件扩展名")
    @Column(name = "file_extension")
    private String fileExtension;

    @ApiModelProperty(value = "创建时间")
    @Column(name = "gmt_create")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "创建用户(登录账号)")
    @Column(name = "gmt_create_user")
    private String gmtCreateUser;

    @ApiModelProperty(value = "修改时间")
    @Column(name = "gmt_modified")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "修改用户(登录账号)")
    @Column(name = "gmt_modified_user")
    private String gmtModifiedUser;



}
