package com.get.permissioncenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.permissioncenter.dto.PermissionGroupGradeResourceDto;
import com.get.permissioncenter.dto.ResourceDto;
import com.get.permissioncenter.vo.PermissionGroupGradeResourceVo;
import com.get.permissioncenter.vo.ResourceVo;
import com.get.permissioncenter.entity.PermissionGroupGradeResource;
import com.get.permissioncenter.dto.PermissionGroupGradeForMovingAndCopyingDto;

import java.util.List;
import java.util.Set;

/**
 * @author: jack
 * @create: 2020/7/13
 * @verison: 1.0
 * @description: 权限系统资源配置业务接口
 */
public interface IGroupGradeResourceService extends BaseService<PermissionGroupGradeResource> {
    /**
     * 权限资源列表数据
     *
     * @return
     */
    PermissionGroupGradeResourceVo getGroupGradeResource(Long companyId);

    /**
     * 组别等级权限集合
     *
     * @param
     * @return
     */
    List<String> getGroupGradeResources(PermissionGroupGradeResourceDto permissionGroupGradeResourceDto);

    /**
     * 更新权限
     *
     * @param
     * @return
     */
    void updateGroupGradeResources(PermissionGroupGradeResourceDto permissionGroupGradeResourceDto);

    /**
     * 保存权限
     *
     * @param
     * @return
     */
    void addGroupGradeResources(PermissionGroupGradeResourceDto permissionGroupGradeResourceDto);

    /**
     * 删除权限
     *
     * @param
     * @return
     */
    void deleteGroupGradeResources(PermissionGroupGradeResourceDto permissionGroupGradeResourceDto);

    /**
     * 通过Resource删除权限
     *
     * @param
     * @return
     */
    Boolean getGroupGradeResourcesByResource(String resourceKey);

    /**
     * 通过Resource更新权限
     *
     * @param
     * @return
     */
    ResourceVo updateGroupGradeResourcesByResource(ResourceDto resourceKey);

    /**
     * 获取员工权限
     *
     * @param
     * @return
     */
    Set<String> getStaffApiKeys(Long staffId);

    /**
     * 获取员工资源key
     *
     * @param
     * @return
     */
    List<String> getStaffResourceKeys(Long staffId);

    /**
     * @return void
     * @Description:更新员工session
     * @Param [staffIds]
     * <AUTHOR>
     **/
    void updateStaffSession(List<Long> staffIds);

    /**
     * 移动网点资源
     *
     * @param permissionGroupGradeForMovingAndCopyingDto
     */
    void movePermissionGroupGrade(PermissionGroupGradeForMovingAndCopyingDto permissionGroupGradeForMovingAndCopyingDto);

    /**
     * 移动网点资源
     *
     * @param permissionGroupGradeForMovingAndCopyingDto
     */
    void copyPermissionGroupGrade(PermissionGroupGradeForMovingAndCopyingDto permissionGroupGradeForMovingAndCopyingDto);
}
