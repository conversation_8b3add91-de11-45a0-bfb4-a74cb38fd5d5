package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.entity.ProviderAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProviderAccountMapper extends BaseMapper<ProviderAccount> {

    /**
     * @return java.lang.Boolean
     * @Description :验证是否为空
     * @Param [providerId]
     * <AUTHOR>
     */
    Boolean providerAccountIsEmpty(@Param("id") Long id);

}