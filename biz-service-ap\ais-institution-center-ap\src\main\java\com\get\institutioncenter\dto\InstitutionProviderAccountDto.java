package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class InstitutionProviderAccountDto extends BaseVoEntity {
    @ApiModelProperty("提供商id")
    @NotNull(message = "提供商id不能为空",groups = {Add.class,Update.class})
    private Long fkInstitutionProviderId;

    @ApiModelProperty("币种编号")
    @NotNull(message = "币种编号不能为空",groups = {Add.class,Update.class})
    private String fkCurrencyTypeNum;

    @ApiModelProperty("账户卡类型")
    @NotNull(message = "账户卡类型不能为空",groups = {Add.class,Update.class})
    private Integer accountCardType;

    @ApiModelProperty("账户名称")
    @NotNull(message = "账户名称不能为空",groups = {Add.class,Update.class})
    private String bankAccount;

    @ApiModelProperty("银行账户")
    @NotNull(message = "银行账户不能为空",groups = {Add.class,Update.class})
    private String bankAccountNum;

    @ApiModelProperty("银行名称")
    @NotNull(message = "银行名称不能为空",groups = {Add.class,Update.class})
    private String bankName;

    @ApiModelProperty("支行名称")
    private String bankBranchName;

    @ApiModelProperty("国家id")
    private Long fkAreaCountryId;

    @ApiModelProperty("州省id")
    private Long fkAreaStateId;

    @ApiModelProperty("城市id")
    private Long fkAreaCityId;

    @ApiModelProperty("城市区域id")
    private Long fkAreaCityDivisionId;

    @ApiModelProperty("银行地址")
    private String bankAddress;

    @ApiModelProperty("银行编号类型：SwiftCode/BSB")
    @NotNull(message = "银行编号类型不能为空",groups = {Add.class,Update.class})
    private String bankCodeType;

    @ApiModelProperty("银行编号")
    @NotNull(message = "银行编号不能为空",groups = {Add.class,Update.class})
    private String bankCode;

    @ApiModelProperty("国家编码")
    @NotNull(message = "国家编码不能为空",groups = {Add.class,Update.class})
    private String areaCountryCode;

    @ApiModelProperty("是否默认首选：0否/1是")
    @NotNull(message = "请选择是否首选",groups = {Add.class,Update.class})
    private Boolean isDefault;

    @ApiModelProperty("是否激活：0否/1是")
    @NotNull(message = "请选择是否激活",groups = {Add.class,Update.class})
    private Boolean isActive;

    @ApiModelProperty("备注")
    private String remark;
}
