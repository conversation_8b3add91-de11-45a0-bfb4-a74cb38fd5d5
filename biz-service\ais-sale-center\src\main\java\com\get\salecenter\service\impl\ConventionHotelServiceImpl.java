package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.api.ResultCode;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.salecenter.dao.sale.ConventionHotelMapper;
import com.get.salecenter.vo.ConventionHotelVo;
import com.get.salecenter.entity.ConventionHotel;
import com.get.salecenter.service.IConventionHotelService;
import com.get.salecenter.service.IDeleteService;
import com.get.salecenter.dto.ConventionHotelDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/6 11:25
 * @verison: 1.0
 * @description: 酒店房型管理业务实现类
 */
@Service
public class ConventionHotelServiceImpl implements IConventionHotelService {
    @Resource
    private ConventionHotelMapper conventionHotelMapper;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private UtilService utilService;
    @Resource
    private IDeleteService deleteService;

    @Override
    public ConventionHotelVo findConventionHotelById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ConventionHotel conventionHotel = conventionHotelMapper.selectById(id);
        return BeanCopyUtils.objClone(conventionHotel, ConventionHotelVo::new);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<ConventionHotelDto> conventionHotelDtos) {
        if (GeneralTool.isEmpty(conventionHotelDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (ConventionHotelDto conventionHotelDto : conventionHotelDtos) {
            ConventionHotel conventionHotel = BeanCopyUtils.objClone(conventionHotelDto, ConventionHotel::new);
            String validateResult = validateAdd(conventionHotelDto);
            if (GeneralTool.isEmpty(validateResult)) {
                //查找排序最大值
                Integer maxViewOrder = conventionHotelMapper.getMaxViewOrder(conventionHotelDto.getFkConventionId());
                conventionHotel.setViewOrder(maxViewOrder);
                utilService.updateUserInfoToEntity(conventionHotel);
                conventionHotelMapper.insert(conventionHotel);
            } else {
                throw new GetServiceException(validateResult);
            }
        }
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (findConventionHotelById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //通过酒店房型id查找是否有开好的房间 用于判断该酒店房型能否删除
        deleteService.deleteValidateHotel(id);
        int i = conventionHotelMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }

    }

    @Override
    public ConventionHotelVo updateConventionHotel(ConventionHotelDto conventionHotelDto) {
        if (conventionHotelDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ConventionHotel result = conventionHotelMapper.selectById(conventionHotelDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ConventionHotel conventionHotel = BeanCopyUtils.objClone(conventionHotelDto, ConventionHotel::new);
        String validateResult = validateUpdate(conventionHotelDto);
        if (GeneralTool.isEmpty(validateResult)) {
            utilService.updateUserInfoToEntity(conventionHotel);
            conventionHotelMapper.updateById(conventionHotel);
        } else {
            throw new GetServiceException(validateResult);
        }
        return findConventionHotelById(conventionHotel.getId());
    }

    @Override
    public List<ConventionHotelVo> getConventionHotels(ConventionHotelDto conventionHotelDto, Page page) {
//        Example example = new Example(ConventionHotel.class);
//        Example.Criteria criteria = example.createCriteria();
//        Example.Criteria criteria1 = example.createCriteria();
//        criteria.andEqualTo("fkConventionId", conventionHotelDto.getFkConventionId());
//        if (GeneralTool.isNotEmpty(conventionHotelDto)) {
//            //查询条件-酒店名称 or 房型
//            if (GeneralTool.isNotEmpty(conventionHotelDto.getKeyWord())) {
//                criteria1.andLike("hotel", "%" + conventionHotelDto.getKeyWord() + "%");
//                criteria1.orLike("roomType", "%" + conventionHotelDto.getKeyWord() + "%");
//            }
//            example.and(criteria1);
//            example.orderBy("viewOrder").desc();
//        }
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<ConventionHotel> conventionHotels = conventionHotelMapper.selectByExample(example);
//        page.restPage(conventionHotels);
        LambdaQueryWrapper<ConventionHotel> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ConventionHotel::getFkConventionId, conventionHotelDto.getFkConventionId());
        if (GeneralTool.isNotEmpty(conventionHotelDto)) {
            //查询条件-酒店名称 or 房型
            if (GeneralTool.isNotEmpty(conventionHotelDto.getKeyWord())) {
                lambdaQueryWrapper.and(wrapper -> wrapper.like(ConventionHotel::getHotel, conventionHotelDto.getKeyWord())
                        .or().like(ConventionHotel::getRoomType, conventionHotelDto.getKeyWord()));
            }
            lambdaQueryWrapper.orderByDesc(ConventionHotel::getViewOrder);
        }
        IPage<ConventionHotel> iPage = conventionHotelMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<ConventionHotel> conventionHotels = iPage.getRecords();
        page.setAll((int) iPage.getTotal());

        List<ConventionHotelVo> converDatas = new ArrayList<>();
        for (ConventionHotel conventionHotel : conventionHotels) {
            //feign调用 根据币种编号查找币种名称
            ConventionHotelVo conventionHotelVo = BeanCopyUtils.objClone(conventionHotel, ConventionHotelVo::new);
            Result<String> result = financeCenterClient.getCurrencyNameByNum(conventionHotel.getFkCurrencyTypeNum());
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                conventionHotelVo.setCurrencyTypeName(result.getData());
            }
            converDatas.add(conventionHotelVo);
        }
        return converDatas;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<ConventionHotelDto> conventionHotelDtos) {
        if (GeneralTool.isEmpty(conventionHotelDtos)) {
            throw new GetServiceException(ResultCode.INVALID_PARAM, "传入值为空");
        }
        ConventionHotel ro = BeanCopyUtils.objClone(conventionHotelDtos.get(0), ConventionHotel::new);
        Integer oneorder = ro.getViewOrder();
        ConventionHotel rt = BeanCopyUtils.objClone(conventionHotelDtos.get(1), ConventionHotel::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        conventionHotelMapper.updateById(ro);
        conventionHotelMapper.updateById(rt);
    }

    @Override
    public List<String> getHotelList(Long conventionId) {
        return conventionHotelMapper.getHotelList(conventionId);
    }

    @Override
    public List<String> getRoomTypeList(String hotel) {
        return conventionHotelMapper.getRoomTypeList(hotel);
    }

    @Override
    public List<Long> getConventionHotelIdsByHotel(String hotel, Long conventionId) {
        List<Long> conventionHotelIds = conventionHotelMapper.getConventionHotelIdsByHotel(hotel, conventionId);
        //当为空时，设为0，防止in()报错
        if (GeneralTool.isEmpty(conventionHotelIds)) {
            conventionHotelIds.add(0L);
        }
        return conventionHotelIds;
    }

    @Override
    public Long getConventionHotelId(String roomType, String hotel, Long conventionId) {
        return conventionHotelMapper.getConventionHotelId(roomType, hotel, conventionId);
    }

    @Override
    public List<ConventionHotel> getConventionHotelList(Long conventionId) {
//        Example example = new Example(ConventionHotel.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(conventionId)) {
//            criteria.andEqualTo("fkConventionId", conventionId);
//        }
//        example.orderBy("viewOrder").asc();
//        return conventionHotelMapper.selectByExample(example);

//        Example example = new Example(ConventionHotel.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<ConventionHotel> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(conventionId)) {
            lambdaQueryWrapper.eq(ConventionHotel::getFkConventionId, conventionId);
        }
        lambdaQueryWrapper.orderByDesc(ConventionHotel::getViewOrder);
        return conventionHotelMapper.selectList(lambdaQueryWrapper);
    }

    @Override
    public List<Long> getConventionHotelIds(Long conventionId) {
        List<Long> conventionHotelIds = conventionHotelMapper.getConventionHotelIds(conventionId);
        //当为空时，设为0，防止in()报错
        if (GeneralTool.isEmpty(conventionHotelIds)) {
            conventionHotelIds.add(0L);
        }
        return conventionHotelIds;
    }

    /**
     * 根据峰会id和房型名称 查询房型id
     *
     * @param conventionId 峰会id
     * @param roomTypeName 房型名称
     * @return 房型id
     * @
     */
    @Override
    public ConventionHotel getRoomTypeId(Long conventionId, String roomTypeName) {
//        Example example = new Example(ConventionHotel.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(conventionId)) {
//            criteria.andEqualTo("fkConventionId", conventionId);
//        }
//        criteria.andEqualTo("roomType",roomTypeName);
//        List<ConventionHotel> conventionHotels = conventionHotelMapper.selectByExample(example);
        LambdaQueryWrapper<ConventionHotel> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(conventionId)) {
            lambdaQueryWrapper.eq(ConventionHotel::getFkConventionId, conventionId);
        }
        lambdaQueryWrapper.eq(ConventionHotel::getRoomType, roomTypeName);
        List<ConventionHotel> conventionHotels = conventionHotelMapper.selectList(lambdaQueryWrapper);
        if (conventionHotels.size() <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        return conventionHotels.get(0);
    }

    private String validateAdd(ConventionHotelDto conventionHotelDto) {
        List<ConventionHotel> list = getConventionHotelList(conventionHotelDto);
        if (GeneralTool.isNotEmpty(list)) {
            StringBuilder sb = new StringBuilder();
            String resultMsg = null;
            for (ConventionHotel conventionHotel : list) {
                resultMsg = setValidMsg(conventionHotelDto, sb, conventionHotel);
            }
            return resultMsg;
        }
        return null;
    }

    private String validateUpdate(ConventionHotelDto conventionHotelDto) {
        List<ConventionHotel> list = getConventionHotelList(conventionHotelDto);
        if (GeneralTool.isNotEmpty(list)) {
            StringBuilder sb = new StringBuilder();
            String resultMsg = null;
            for (ConventionHotel conventionHotel : list) {
                if (!conventionHotelDto.getId().equals(conventionHotel.getId())) {
                    resultMsg = setValidMsg(conventionHotelDto, sb, conventionHotel);
                }
            }
            return resultMsg;
        }
        return null;
    }

    /**
     * @return java.util.List<com.get.salecenter.entity.ConventionHotel>
     * @Description :根据验证条件获取list
     * @Param [conventionHotelDto]
     * <AUTHOR>
     */
    private List<ConventionHotel> getConventionHotelList(ConventionHotelDto conventionHotelDto) {
//        Example example = new Example(ConventionHotel.class);
//        Example.Criteria criteria = example.createCriteria();
//        Example.Criteria criteria1 = example.createCriteria();
//        criteria.andEqualTo("hotel", conventionHotelDto.getHotel());
//        criteria.andEqualTo("roomType", conventionHotelDto.getRoomType());
//        criteria.andEqualTo("fkConventionId", conventionHotelDto.getFkConventionId());
//        criteria1.andEqualTo("fkConventionId", conventionHotelDto.getFkConventionId());
//        criteria1.andEqualTo("preNum", conventionHotelDto.getPreNum());
//        example.or(criteria1);
//        return this.conventionHotelMapper.selectByExample(example);
        LambdaQueryWrapper<ConventionHotel> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.and(wrapper -> wrapper.eq(ConventionHotel::getHotel, conventionHotelDto.getHotel())
                .eq(ConventionHotel::getRoomType, conventionHotelDto.getRoomType())
                .eq(ConventionHotel::getFkConventionId, conventionHotelDto.getFkConventionId()));
        lambdaQueryWrapper.or(wrapper -> wrapper.eq(ConventionHotel::getFkConventionId, conventionHotelDto.getFkConventionId())
                .eq(ConventionHotel::getPreNum, conventionHotelDto.getPreNum()));
        return this.conventionHotelMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * @return java.lang.String
     * @Description :设置返回验证信息
     * @Param [conventionHotelDto, sb, conventionPerson]
     * <AUTHOR>
     */
    private String setValidMsg(ConventionHotelDto conventionHotelDto, StringBuilder sb, ConventionHotel conventionPerson) {
        if (conventionPerson.getHotel().equals(conventionHotelDto.getHotel()) && conventionPerson.getRoomType().equals(conventionHotelDto.getRoomType())) {
            sb.append("房型已存在，");
        }
        if (conventionPerson.getPreNum().equals(conventionHotelDto.getPreNum())) {
            sb.append("前序号（房号）已存在，");
        }
        return sub(sb);
    }

    /**
     * @return java.lang.String
     * @Description :截取字符串逗号
     * @Param [sb]
     * <AUTHOR>
     */
    private String sub(StringBuilder sb) {
        if (GeneralTool.isEmpty(sb)) {
            return null;
        }
        String substring = null;
        int i = sb.lastIndexOf("，");
        if (i != -1) {
            substring = sb.substring(0, i);
        }
        return substring;
    }


}
