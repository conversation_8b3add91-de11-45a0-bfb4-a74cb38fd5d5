package com.get.votingcenter.service;

import com.get.votingcenter.vo.UserAwardVo;

import java.util.List;

/**
 * @Description: 随机抽奖管理业务层
 * @Author: <PERSON>
 * @Date:10:23 2021/10/20
 */
public interface IUserAwardService {

    /**
     * @Description: 随机抽奖
     * @Author: Jerry
     * @Date:10:25 2021/10/20
     */
    void generateUserAward(Long fkVotingId, Integer generateCount);


    /**
     * @Description: 抽奖列表数据
     * @Author: Jerry
     * @Date:11:05 2021/10/20
     */
    List<UserAwardVo> datas(Long fkVotingId);


    /**
     * @Description: 删除中奖人员名单
     * @Author: Jerry
     * @Date:16:53 2021/10/21
     */
    void delete(Long fkVotingAwardId);
}
