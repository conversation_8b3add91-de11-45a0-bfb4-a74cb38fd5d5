package com.get.salecenter.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.SaleMediaAndAttached;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
@DS("saledb")
public interface MediaAndAttachedMapper extends BaseMapper<SaleMediaAndAttached> {

    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(SaleMediaAndAttached record);

    Integer getNextIndexKey(@Param("tableId") Long tableId, @Param("tableName") String tableName);

    int getItemMediaCount(SaleMediaAndAttached mediaAndAttached);

    Long getIsExistIssueStudent(@Param("id") Long id);

}