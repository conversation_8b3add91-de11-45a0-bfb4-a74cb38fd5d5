package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.service.IMediaAndAttachedService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/7/14
 * @TIME: 18:17
 * @Description: 附件管理控制层
 **/

@Api(tags = "附件管理")
@RestController
@RequestMapping("institution/media")
public class MediaAndAttachedController {
    @Resource
    private IMediaAndAttachedService attachedService;


    /**
     * 删除
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除文件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/学校管理/删除文件")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        attachedService.deleteMediaAttached(id);
        return ResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "上传文件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/学校管理/上传文件")
    @PostMapping("upload")
    public ResponseBo upload(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files) {
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", attachedService.upload(files));
        return responseBo;
    }

    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "上传附件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/学校管理/上传文件")
    @PostMapping("uploadAttached")
    public ResponseBo uploadAttached(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files) {
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", attachedService.uploadAttached(files));
        return responseBo;
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description：上移下移
     * @Param [mediaAttachedVos]
     * <AUTHOR>
     **/
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/附件管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<MediaAndAttachedDto> mediaAttachedVos) {
        attachedService.movingOrder(mediaAttachedVos);
        return ResponseBo.ok();
    }


    /**
     * 学校附件类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学校附件类型", notes = "")
    @PostMapping("getInstitutionMediaType")
    public ResponseBo getInstitutionMediaType() {
        List<Map<String, Object>> datas = attachedService.findMediaAndAttachedType();
        return new ListResponseBo<>(datas);
    }

    /**
     * 学校附件类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学校提供商附件类型", notes = "")
    @PostMapping("getProviderMediaType")
    public ResponseBo getProviderMediaType() {
        List<Map<String, Object>> datas = attachedService.getProviderMediaType();
        return new ListResponseBo<>(datas);
    }

    /**
     * 学校附件类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "合同附件类型", notes = "")
    @PostMapping("getContractMediaType")
    public ResponseBo getContractMediaType() {
        List<Map<String, Object>> datas = attachedService.getContractMediaType();
        return new ListResponseBo<>(datas);
    }


}
