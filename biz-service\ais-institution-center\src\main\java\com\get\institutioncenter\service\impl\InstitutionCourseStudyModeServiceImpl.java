package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.institutioncenter.dao.InstitutionCourseStudyModeMapper;
import com.get.institutioncenter.entity.InstitutionCourseStudyMode;
import com.get.institutioncenter.service.IInstitutionCourseStudyModeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @DATE: 2022/1/28
 * @TIME: 11:05
 * @Description:
 **/
@Service
public class InstitutionCourseStudyModeServiceImpl implements IInstitutionCourseStudyModeService {
    @Resource
    private InstitutionCourseStudyModeMapper institutionCourseStudyModeMapper;

    @Override
    public void deleteModeByCourseId(Long id) {
        LambdaQueryWrapper<InstitutionCourseStudyMode> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionCourseStudyMode::getFkInstitutionCourseId, id);
        int j = institutionCourseStudyModeMapper.delete(wrapper);
        if (j < 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }
}
