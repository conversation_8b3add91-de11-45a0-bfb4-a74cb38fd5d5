package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_event_bill_event_summary")
public class EventBillEventSummary extends BaseEntity implements Serializable {
    /**
     * 活动费用账单Id
     */
    @ApiModelProperty(value = "活动费用账单Id")
    @Column(name = "fk_event_bill_id")
    private Long fkEventBillId;

    /**
     * 活动汇总费用摘要Id
     */
    @ApiModelProperty(value = "活动汇总费用摘要Id")
    @Column(name = "fk_event_summary_id")
    private Long fkEventSummaryId;

    private static final long serialVersionUID = 1L;
}