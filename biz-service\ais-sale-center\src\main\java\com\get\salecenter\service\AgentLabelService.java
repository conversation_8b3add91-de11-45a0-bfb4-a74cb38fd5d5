package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.salecenter.dto.AgentLabelDto;
import com.get.salecenter.vo.AgentLabelVo;

import java.util.List;
import java.util.Set;

public interface AgentLabelService {
    /**
     * 新增代理及代理联系人邮箱 标记
     *
     * @param agentLabelDto
     * @return
     */
    List<Long> addAgentLabel(AgentLabelDto agentLabelDto);

    /**
     * 删除代理及代理联系人邮箱 标记
     * @param id
     * @return
     */
    int deleteAgentLabel(Long id);

    /**
     * 获取代理及代理联系人邮箱 标记
     * @param agentLabelVo
     * @param page
     * @return
     */
    List<AgentLabelVo> dataList(AgentLabelDto agentLabelVo, Page page);

    List<AgentLabelVo> getAgentLabelListByAgentDto(Set<Long> agentIds, Set<String> labelEmails);

   ResponseBo getLabels(SearchBean<AgentLabelDto> page);

//    List<AgentLabelVo> getAgentLabelListByAgentDto(AgentLabelDto agentLabelDto);
}
