package com.get.permissioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.permissioncenter.entity.StaffContract;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface StaffContractMapper extends BaseMapper<StaffContract> {

    int insertSelective(StaffContract record);

    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [staffId]
     * <AUTHOR>
     */
    Bo<PERSON>an isExistByStaffId(Long staffId);

    /**
     * 根据员工ID获取最新并激活的劳动合同
     *
     * @param staffId
     * @return
     */
    StaffContract getStaffContractByStaffId(@Param("staffId") Long staffId);

}