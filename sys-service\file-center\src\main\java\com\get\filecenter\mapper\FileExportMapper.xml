<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.get.filecenter.mapper.FileExportMapper">

    <select id="selectByGuid" resultType="com.get.filecenter.entity.FileExport" parameterType="String">
      SELECT
        file_guid,
        file_type_orc,
        file_name_orc,
        file_name,
        file_path,
        file_key
      FROM
        m_file_export
      WHERE
        file_guid = #{guid}
    </select>
</mapper>
