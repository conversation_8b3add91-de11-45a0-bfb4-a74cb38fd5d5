<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.partnercenter.mapper.MAppStudentCheckMapper">

    <resultMap id="BaseResultMap" type="com.get.partnercenter.entity.MAppStudentCheckEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkAppStudentId" column="fk_app_student_id" jdbcType="BIGINT"/>
            <result property="checkComment" column="check_comment" jdbcType="VARCHAR"/>
            <result property="isPassed" column="is_passed" jdbcType="BIT"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="searchPage" resultType="com.get.partnercenter.vo.MAppStudentCheckSearchVo">

        select
        mAgent.name AS agentName,
        mAgent.personal_name AS agentPersonalName,
        mAppStudent.id AS appStudentId,
        mAppStudent.name AS studentName,
        mAppStudent.last_name AS lastName,
        mAppStudent.first_name AS firstName,
        mAppStudent.fk_institution_name_education,
        mAppStudent.fk_institution_name_education2,
        mAppStudent.birthday,
        mAppStudent.status,
        mAppStudent.gmt_create_user AS  gmtCreateUser,
        mPartnerUser.name AS  gmtCreateUserhis,
        mAppStudent.gmt_create,
        mAppStudent.gmt_modified,
        mAppStudent.gmt_modified_user,
        (
        select count(*)  from   ais_sale_center.m_app_student_offer_item mAppStudentOfferItem
                    WHERE mAppStudentOfferItem.fk_app_student_id=mAppStudent.id AND mAppStudentOfferItem.is_additional=1
        AND mAppStudentOfferItem.status_additional=1   LIMIT 1) AS checkItemNUm,
        (
        select count(*)  from   ais_sale_center.m_app_student_offer_item mAppStudentOfferItem
        WHERE mAppStudentOfferItem.fk_app_student_id=mAppStudent.id AND mAppStudentOfferItem.is_additional=1   LIMIT 1) AS jiashenCheckItemNUm


        from m_app_student mAppStudent
        INNER JOIN  m_agent mAgent ON mAppStudent.fk_agent_id=mAgent.id

        <if test="query.staffFollowerIds!=null and query.staffFollowerIds.size()>0 ">
            INNER JOIN r_agent_staff rAgentStaff on rAgentStaff.fk_agent_id = mAgent.id and rAgentStaff.is_active =1
        </if>
        LEFT JOIN  app_partner_center.m_partner_user mPartnerUser ON mPartnerUser.id=mAppStudent.gmt_create_user
        WHERE   mAppStudent.fk_platform_id=2

        <if test="query.staffFollowerIds!=null and query.staffFollowerIds.size()>0">
            AND rAgentStaff.fk_staff_id  in
            <foreach collection="query.staffFollowerIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>

        </if>

        <if test="query.status!=null">
            AND mAppStudent.status=#{query.status}
        </if>
        <if test="query.status==null">
            AND mAppStudent.status in(-1,1,2)
        </if>


        <if test="query.studentName!=null and query.studentName !=''">
            AND
            (
            mAppStudent.name   like concat('%',#{query.studentName},'%')
            OR REPLACE(CONCAT(IFNULL(mAppStudent.last_name,''),IFNULL(mAppStudent.first_name,'')),' ','') like concat('%',#{query.studentName},'%')
            OR REPLACE(CONCAT(IFNULL(mAppStudent.first_name,''),IFNULL(mAppStudent.last_name,'')),' ','') like concat('%',#{query.studentName},'%')
            )
        </if>
        <if test="query.agentName!=null and query.agentName !=''">
            AND mAgent.name like concat('%',#{query.agentName},'%')
        </if>
        ORDER BY mAppStudent.gmt_create DESC
    </select>

    <select id="searchDetail" resultType="com.get.partnercenter.vo.MAppStudentVo">

        select
            mAgent.name AS agentName,
            mAgent.personal_name AS agentPersonalName,
            mAppStudent.*

        from m_app_student mAppStudent
                 INNER JOIN  m_agent mAgent ON mAppStudent.fk_agent_id=mAgent.id


    </select>
</mapper>
