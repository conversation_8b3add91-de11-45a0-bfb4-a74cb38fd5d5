package com.get.partnercenter.rocketmq.msg;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/3/20
 * @Version 1.0
 * @apiNote:用户下线消息体
 */
@Data
public class UserOfflineDto {
    @ApiModelProperty(value = "用户登录账号")
    private List<String> loginIds;

    @ApiModelProperty(value = "平台ID")
    private Long platformId;

    public UserOfflineDto(List<String> userIds, Long platformId) {
        this.loginIds = userIds;
        this.platformId = platformId;
    }
}
