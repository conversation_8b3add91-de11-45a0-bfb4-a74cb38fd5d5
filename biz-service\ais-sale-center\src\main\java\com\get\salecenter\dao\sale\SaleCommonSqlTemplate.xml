<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.SaleCommonSqlTemplate">
    <!-- 如果你要改这个OS的规则，那下面的repeatOs你也要改哦 -->
    <sql id="os">
                SELECT
                msoi2.id, MIN( msoi2.fk_student_id) AS fk_student_id, MIN(rsois.gmt_create) AS minGmtCreate, msoi2.fk_area_country_id
                FROM
                ais_sale_center.m_student_offer_item AS msoi2
                INNER JOIN ais_sale_center.r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = msoi2.id
                INNER JOIN ais_sale_center.m_student AS ms ON ms.id = msoi2.fk_student_id
                INNER JOIN (
                    SELECT a.fk_student_id, a.fk_area_country_id, c.minGmtCreate, MIN(a.id) minItemId
                    FROM ais_sale_center.m_student_offer_item a
                    INNER JOIN ais_sale_center.r_student_offer_item_step b ON a.id=b.fk_student_offer_item_id
                    INNER JOIN ais_sale_center.u_student_offer_item_step AS usois ON usois.id = b.fk_student_offer_item_step_id
                    INNER JOIN (
                        SELECT
                        ms3.id,
                        msoi3.fk_area_country_id,
                        MIN( rsois.gmt_create ) AS minGmtCreate
                        FROM
                        ais_sale_center.m_student AS ms3
                        INNER JOIN ais_sale_center.m_student_offer_item AS msoi3 ON msoi3.fk_student_id = ms3.id
                        INNER JOIN ais_sale_center.r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = msoi3.id
                        INNER JOIN ais_sale_center.u_student_offer_item_step AS usois ON usois.id = rsois.fk_student_offer_item_step_id
                        INNER JOIN ais_sale_center.u_student_offer_item_step AS usoisFailure ON usoisFailure.id = msoi3.fk_student_offer_item_step_id
                        WHERE
                        ms3.fk_company_id IN
                         <foreach collection="bdStudentStatisticalComparisonDto.fkCompanyIds" item="fkCompanyId" open="(" separator="," close=")">
                            #{fkCompanyId}
                        </foreach>
                        AND
                            msoi3.status = 1
                        AND msoi3.is_follow = 0
                        AND usois.step_key IN ( "STEP_OFFER_SELECTION", "STEP_VISA_SUBMITTED", "STEP_ENROLLED", "STEP_VISA_GRANTED" )
                        <choose>
                            <when test="bdStudentStatisticalComparisonDto.statisticalType == 8 or bdStudentStatisticalComparisonDto.statisticalType == 6">
                                AND ( usoisFailure.step_key != 'STEP_FAILURE' OR EXISTS (SELECT 1 FROM r_student_offer_item_step AS rsois4 WHERE rsois4.fk_student_offer_item_id = msoi3.id AND rsois4.fk_student_offer_item_step_id = 10 ) )
                            </when>
                            <otherwise>
                                AND usoisFailure.step_key != 'STEP_FAILURE'
                            </otherwise>
                        </choose>
                        GROUP BY
                        ms3.id, msoi3.fk_area_country_id
                        ) c ON a.fk_student_id=c.id AND b.gmt_create=c.minGmtCreate AND a.fk_area_country_id = c.fk_area_country_id
                    WHERE usois.step_key IN ( "STEP_OFFER_SELECTION", "STEP_VISA_SUBMITTED", "STEP_ENROLLED", "STEP_VISA_GRANTED" )
                    GROUP BY a.fk_student_id, a.fk_area_country_id, c.minGmtCreate
                ) a ON a.minItemId = msoi2.id AND rsois.gmt_create=a.minGmtCreate AND a.fk_area_country_id = msoi2.fk_area_country_id
                WHERE
                DATE_FORMAT( rsois.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{beginTime}, '%Y-%m-%d' )
                AND DATE_FORMAT( rsois.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{endTime}, '%Y-%m-%d' )
                GROUP BY
                msoi2.id, msoi2.fk_area_country_id
    </sql>

    <!-- 不同国家线重复OS -->
    <sql id="repeatOs">
    SELECT ms4.id AS repeatOsStudentId, COUNT(DISTINCT repeatOs2.fk_area_country_id) AS repeatOsCount
    FROM m_student AS ms4

    INNER JOIN (

        SELECT
            msoi2.fk_student_id, msoi2.fk_area_country_id
        FROM
            ais_sale_center.m_student_offer_item AS msoi2
                INNER JOIN ais_sale_center.r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = msoi2.id
                INNER JOIN ais_sale_center.m_student AS ms ON ms.id = msoi2.fk_student_id
                INNER JOIN (
                SELECT a.fk_student_id, a.fk_area_country_id, c.minGmtCreate, MIN(a.id) minItemId
                FROM ais_sale_center.m_student_offer_item a
                         INNER JOIN ais_sale_center.r_student_offer_item_step b ON a.id=b.fk_student_offer_item_id
                         INNER JOIN ais_sale_center.u_student_offer_item_step AS usois ON usois.id = b.fk_student_offer_item_step_id
                         INNER JOIN (
                    SELECT
                        ms3.id,
                        msoi3.fk_area_country_id,
                        MIN( rsois.gmt_create ) AS minGmtCreate
                    FROM
                        ais_sale_center.m_student AS ms3
                            INNER JOIN ais_sale_center.m_student_offer_item AS msoi3 ON msoi3.fk_student_id = ms3.id
                            INNER JOIN ais_sale_center.r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = msoi3.id
                            INNER JOIN ais_sale_center.u_student_offer_item_step AS usois ON usois.id = rsois.fk_student_offer_item_step_id
                            INNER JOIN ais_sale_center.u_student_offer_item_step AS usoisFailure ON usoisFailure.id = msoi3.fk_student_offer_item_step_id
                    WHERE
                        ms3.fk_company_id IN
                         <foreach collection="bdStudentStatisticalComparisonDto.fkCompanyIds" item="fkCompanyId" open="(" separator="," close=")">
                            #{fkCompanyId}
                        </foreach>
                      AND
                        msoi3.status = 1
                      AND msoi3.is_follow = 0
                      AND usois.step_key IN ( "STEP_OFFER_SELECTION", "STEP_VISA_SUBMITTED", "STEP_ENROLLED", "STEP_VISA_GRANTED" )
                    <choose>
                        <when test="bdStudentStatisticalComparisonDto.statisticalType == 8">
                            AND ( usoisFailure.step_key != 'STEP_FAILURE' OR EXISTS (SELECT 1 FROM r_student_offer_item_step AS rsois4 WHERE rsois4.fk_student_offer_item_id = msoi3.id AND rsois4.fk_student_offer_item_step_id = 10 ) )
                        </when>
                        <otherwise>
                            AND usoisFailure.step_key != 'STEP_FAILURE'
                        </otherwise>
                    </choose>
                    GROUP BY
                        ms3.id, msoi3.fk_area_country_id
                ) c ON a.fk_student_id=c.id AND b.gmt_create=c.minGmtCreate AND a.fk_area_country_id = c.fk_area_country_id
                WHERE usois.step_key IN ( "STEP_OFFER_SELECTION", "STEP_VISA_SUBMITTED", "STEP_ENROLLED", "STEP_VISA_GRANTED" )
                GROUP BY a.fk_student_id, a.fk_area_country_id, c.minGmtCreate
            ) a ON a.minItemId = msoi2.id AND rsois.gmt_create=a.minGmtCreate AND a.fk_area_country_id = msoi2.fk_area_country_id
        WHERE
                DATE_FORMAT( rsois.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{beginTime}, '%Y-%m-%d' )
          AND DATE_FORMAT( rsois.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{endTime}, '%Y-%m-%d' )
        GROUP BY
            msoi2.fk_student_id, msoi2.fk_area_country_id

    )repeatOs2 ON repeatOs2.fk_student_id = ms4.id
    GROUP BY ms4.id
    HAVING repeatOsCount > 1
    </sql>

    <sql id="firstStudentNum">
            SELECT
            msoi2.id, MIN( msoi2.fk_student_id) AS fk_student_id
            FROM
            ais_sale_center.m_student_offer_item AS msoi2
            INNER JOIN ais_sale_center.m_student AS ms2 ON ms2.id = msoi2.fk_student_id
            INNER JOIN ais_sale_center.r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = msoi2.id
            INNER JOIN (
                SELECT a. fk_student_id, c.minGmtCreate, MIN(a.id) minItemId
                FROM ais_sale_center.m_student_offer_item a
                INNER JOIN ais_sale_center.r_student_offer_item_step b ON a.id=b.fk_student_offer_item_id
                INNER JOIN (
                    SELECT
                    ms3.id,
                    MIN( rsois.gmt_create ) AS minGmtCreate
                    FROM
                    ais_sale_center.m_student AS ms3
                    INNER JOIN ais_sale_center.m_student_offer_item AS msoi3 ON msoi3.fk_student_id = ms3.id
                    INNER JOIN ais_sale_center.r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = msoi3.id
                    INNER JOIN ais_sale_center.u_student_offer_item_step AS usois ON usois.id = rsois.fk_student_offer_item_step_id
                    WHERE
                    ms3.fk_company_id IN
                     <foreach collection="bdStudentStatisticalComparisonDto.fkCompanyIds" item="fkCompanyId" open="(" separator="," close=")">
                        #{fkCompanyId}
                    </foreach>
                    AND msoi3.status = 1
                    AND IFNULL( msoi3.fk_parent_student_offer_item_id, 0 ) = 0
                    GROUP BY
                    ms3.id
                    ) c ON a.fk_student_id=c.id AND b.gmt_create=c.minGmtCreate
                GROUP BY a.fk_student_id, c.minGmtCreate
            ) a ON a.minItemId = msoi2.id AND rsois.gmt_create=a.minGmtCreate
            WHERE
                DATE_FORMAT( msoi2.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{beginTime}, '%Y-%m-%d' )
            AND DATE_FORMAT( msoi2.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{endTime}, '%Y-%m-%d' )
            GROUP BY
            msoi2.id
    </sql>

    <sql id="appOptNum">
        SELECT
            msoi2.id, MIN( msoi2.fk_student_id) AS fk_student_id
        FROM
            ais_sale_center.m_student_offer_item AS msoi2
                INNER JOIN ais_sale_center.m_student AS ms2 ON ms2.id = msoi2.fk_student_id
        WHERE
            msoi2.new_app_status = 0 AND msoi2.status = 1 AND msoi2.is_follow = 0 AND msoi2.fk_student_offer_item_step_id = 1
          AND DATE_FORMAT( msoi2.new_app_opt_time, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{beginTime}, '%Y-%m-%d' )
          AND DATE_FORMAT( msoi2.new_app_opt_time, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{endTime}, '%Y-%m-%d' )
        GROUP BY
            msoi2.id
    </sql>

    <sql id="oldFirstStudentNum">
            SELECT
            msoi2.id, MIN( msoi2.fk_student_id) AS fk_student_id
            FROM
            ais_sale_center.m_student_offer_item AS msoi2
            INNER JOIN ais_sale_center.m_student AS ms2 ON ms2.id = msoi2.fk_student_id
            INNER JOIN ais_sale_center.r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = msoi2.id
            INNER JOIN (
                SELECT a. fk_student_id, c.minGmtCreate, MIN(a.id) minItemId
                FROM ais_sale_center.m_student_offer_item a
                INNER JOIN ais_sale_center.r_student_offer_item_step b ON a.id=b.fk_student_offer_item_id
                INNER JOIN (
                    SELECT
                    ms3.id,
                    MIN( rsois.gmt_create ) AS minGmtCreate
                    FROM
                    ais_sale_center.m_student AS ms3
                    INNER JOIN ais_sale_center.m_student_offer_item AS msoi3 ON msoi3.fk_student_id = ms3.id
                    INNER JOIN ais_sale_center.r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = msoi3.id
                    INNER JOIN ais_sale_center.u_student_offer_item_step AS usois ON usois.id = rsois.fk_student_offer_item_step_id
                    WHERE
                    ms3.fk_company_id IN
                     <foreach collection="bdStudentStatisticalComparisonDto.fkCompanyIds" item="fkCompanyId" open="(" separator="," close=")">
                        #{fkCompanyId}
                    </foreach>
                    AND msoi3.status = 1
                    AND IFNULL( msoi3.fk_parent_student_offer_item_id, 0 ) = 0
                    GROUP BY
                    ms3.id
                    ) c ON a.fk_student_id=c.id AND b.gmt_create=c.minGmtCreate
                GROUP BY a.fk_student_id, c.minGmtCreate
            ) a ON a.minItemId = msoi2.id AND rsois.gmt_create=a.minGmtCreate
            WHERE
                DATE_FORMAT( msoi2.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{beginTime}, '%Y-%m-%d' )
            AND DATE_FORMAT( msoi2.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{endTime}, '%Y-%m-%d' )
            AND DATE_FORMAT( ms2.gmt_create, '%Y-%m-%d' ) <![CDATA[< ]]> DATE_FORMAT(#{htiStartTime}, '%Y-%m-%d' )
            GROUP BY
            msoi2.id
    </sql>

    <sql id="newFirstStudentNum">
            SELECT
            msoi2.id, MIN( msoi2.fk_student_id) AS fk_student_id
            FROM
            ais_sale_center.m_student_offer_item AS msoi2
            INNER JOIN ais_sale_center.m_student AS ms2 ON ms2.id = msoi2.fk_student_id
            INNER JOIN ais_sale_center.r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = msoi2.id
            INNER JOIN (
                SELECT a. fk_student_id, c.minGmtCreate, MIN(a.id) minItemId
                FROM ais_sale_center.m_student_offer_item a
                INNER JOIN ais_sale_center.r_student_offer_item_step b ON a.id=b.fk_student_offer_item_id
                INNER JOIN (
                    SELECT
                    ms3.id,
                    MIN( rsois.gmt_create ) AS minGmtCreate
                    FROM
                    ais_sale_center.m_student AS ms3
                    INNER JOIN ais_sale_center.m_student_offer_item AS msoi3 ON msoi3.fk_student_id = ms3.id
                    INNER JOIN ais_sale_center.r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = msoi3.id
                    INNER JOIN ais_sale_center.u_student_offer_item_step AS usois ON usois.id = rsois.fk_student_offer_item_step_id
                    WHERE
                    ms3.fk_company_id IN
                     <foreach collection="bdStudentStatisticalComparisonDto.fkCompanyIds" item="fkCompanyId" open="(" separator="," close=")">
                        #{fkCompanyId}
                    </foreach>
                    AND msoi3.status = 1
                    AND IFNULL( msoi3.fk_parent_student_offer_item_id, 0 ) = 0
                    GROUP BY
                    ms3.id
                    ) c ON a.fk_student_id=c.id AND b.gmt_create=c.minGmtCreate
                GROUP BY a.fk_student_id, c.minGmtCreate
            ) a ON a.minItemId = msoi2.id AND rsois.gmt_create=a.minGmtCreate
            WHERE
                DATE_FORMAT( msoi2.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{beginTime}, '%Y-%m-%d' )
            AND DATE_FORMAT( msoi2.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{endTime}, '%Y-%m-%d' )
            AND DATE_FORMAT( ms2.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{htiStartTime}, '%Y-%m-%d' )
            GROUP BY
            msoi2.id
    </sql>

    <!-- 签证数 -->
    <sql id="visa">
                SELECT
                msoi2.id, MIN( msoi2.fk_student_id) AS fk_student_id, MIN( msoi2.fk_student_id) AS minStudentId, MIN(rsois.gmt_create) AS minGmtCreate, msoi2.fk_area_country_id
                FROM
                ais_sale_center.m_student AS ms2
                INNER JOIN ais_sale_center.m_student_offer_item AS msoi2 ON msoi2.fk_student_id = ms2.id
                INNER JOIN ais_sale_center.r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = msoi2.id
                INNER JOIN ais_sale_center.m_student AS ms ON ms.id = msoi2.fk_student_id
                INNER JOIN (
                    SELECT a.fk_student_id, a.fk_area_country_id, c.minGmtCreate, MIN(a.id) minItemId
                    FROM ais_sale_center.m_student_offer_item a
                    INNER JOIN ais_sale_center.r_student_offer_item_step b ON a.id=b.fk_student_offer_item_id
                    INNER JOIN (
                        SELECT
                        ms3.id,
                        msoi3.fk_area_country_id,
                        MIN( rsois.gmt_create ) AS minGmtCreate
                        FROM
                        ais_sale_center.m_student AS ms3
                        INNER JOIN ais_sale_center.m_student_offer_item AS msoi3 ON msoi3.fk_student_id = ms3.id
                        INNER JOIN ais_sale_center.r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = msoi3.id
                        INNER JOIN ais_sale_center.u_student_offer_item_step AS usois ON usois.id = rsois.fk_student_offer_item_step_id
                        INNER JOIN ais_sale_center.u_student_offer_item_step AS usoisFailure ON usoisFailure.id = msoi3.fk_student_offer_item_step_id
                        WHERE
                        ms3.fk_company_id IN
                         <foreach collection="bdStudentStatisticalComparisonDto.fkCompanyIds" item="fkCompanyId" open="(" separator="," close=")">
                            #{fkCompanyId}
                        </foreach>
                        AND msoi3.status = 1
                        AND msoi3.is_follow = 0
                        AND usois.step_key = "STEP_VISA_GRANTED"
                        GROUP BY
                        ms3.id, msoi3.fk_area_country_id
                        ) c ON a.fk_student_id=c.id AND b.gmt_create=c.minGmtCreate AND a.fk_area_country_id = c.fk_area_country_id
                    GROUP BY a.fk_student_id, a.fk_area_country_id, c.minGmtCreate
                ) a ON a.minItemId = msoi2.id AND rsois.gmt_create=a.minGmtCreate AND a.fk_area_country_id = msoi2.fk_area_country_id
                WHERE  DATE_FORMAT( rsois.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{beginTime}, '%Y-%m-%d' )
                AND DATE_FORMAT( rsois.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{endTime}, '%Y-%m-%d' )
                GROUP BY
                msoi2.id, msoi2.fk_area_country_id
                HAVING minStudentId NOT IN (
                    SELECT msca.fk_student_id FROM
                        m_staff_commission_action AS msca
                    WHERE
                      msca.status = 2
                      AND msca.fk_staff_commission_step_key = 'STEP_VISA_GRANTED_BACK'
                      AND msca.fk_student_project_role_key = #{bdStudentStatisticalComparisonDto.projectRoleKey}
                )

    </sql>

    <!-- 入学数 -->
    <sql id="enrolled">
        SELECT
                msoi2.id, MIN( msoi2.fk_student_id) AS fk_student_id, MIN(rsois.gmt_create) AS minGmtCreate, msoi2.fk_area_country_id
                FROM
                ais_sale_center.m_student_offer_item AS msoi2
                INNER JOIN ais_sale_center.r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = msoi2.id
                INNER JOIN ais_sale_center.m_student AS ms ON ms.id = msoi2.fk_student_id
                INNER JOIN (
                    SELECT a.fk_student_id, a.fk_area_country_id, c.minGmtCreate, MIN(a.id) minItemId
                    FROM ais_sale_center.m_student_offer_item a
                    INNER JOIN ais_sale_center.r_student_offer_item_step b ON a.id=b.fk_student_offer_item_id
                    INNER JOIN (
                        SELECT
                        ms3.id,
                        msoi3.fk_area_country_id,
                        MIN( rsois.gmt_create ) AS minGmtCreate
                        FROM
                        ais_sale_center.m_student AS ms3
                        INNER JOIN ais_sale_center.m_student_offer_item AS msoi3 ON msoi3.fk_student_id = ms3.id
                        INNER JOIN ais_sale_center.r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = msoi3.id
                        INNER JOIN ais_sale_center.u_student_offer_item_step AS usois ON usois.id = rsois.fk_student_offer_item_step_id
                        INNER JOIN ais_sale_center.u_student_offer_item_step AS usoisFailure ON usoisFailure.id = msoi3.fk_student_offer_item_step_id
                        WHERE
                        ms3.fk_company_id IN
                         <foreach collection="bdStudentStatisticalComparisonDto.fkCompanyIds" item="fkCompanyId" open="(" separator="," close=")">
                            #{fkCompanyId}
                        </foreach>
                        AND msoi3.status = 1
                        AND msoi3.is_follow = 0
                        AND usois.step_key = "STEP_ENROLLED"
                        AND usoisFailure.step_key != 'STEP_FAILURE'
                        GROUP BY
                        ms3.id, msoi3.fk_area_country_id
                        ) c ON a.fk_student_id=c.id AND b.gmt_create=c.minGmtCreate AND a.fk_area_country_id = c.fk_area_country_id
                    GROUP BY a.fk_student_id, a.fk_area_country_id, c.minGmtCreate
                ) a ON a.minItemId = msoi2.id AND rsois.gmt_create=a.minGmtCreate AND a.fk_area_country_id = msoi2.fk_area_country_id
                WHERE DATE_FORMAT( rsois.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{beginTime}, '%Y-%m-%d' )
                AND DATE_FORMAT( rsois.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{endTime}, '%Y-%m-%d' )
                GROUP BY
                msoi2.id, msoi2.fk_area_country_id
    </sql>
    <!-- 后补签证数 成功入学但是该学生没有签证业绩 -->
    <sql id="enrolledVisa">
        SELECT enrolled2.* FROM (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.enrolled" />
        )enrolled2
        WHERE NOT EXISTS(
            SELECT 1 FROM
            ais_sale_center.m_student AS ms3
            INNER JOIN ais_sale_center.m_student_offer_item AS msoi3 ON msoi3.fk_student_id = ms3.id
            INNER JOIN ais_sale_center.r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = msoi3.id
            INNER JOIN ais_sale_center.u_student_offer_item_step AS usois ON usois.id = rsois.fk_student_offer_item_step_id
            INNER JOIN ais_sale_center.u_student_offer_item_step AS usoisFailure ON usoisFailure.id =
            msoi3.fk_student_offer_item_step_id
            WHERE
            ms3.fk_company_id IN
             <foreach collection="bdStudentStatisticalComparisonDto.fkCompanyIds" item="fkCompanyId" open="(" separator="," close=")">
                #{fkCompanyId}
            </foreach>
            AND msoi3.status = 1
            AND msoi3.is_follow = 0
            AND usois.step_key = "STEP_VISA_GRANTED"
            AND usoisFailure.step_key != 'STEP_FAILURE'
            AND ms3.id = enrolled2.fk_student_id
        )
    </sql>

    <!-- 申请反馈数 -->
    <sql id="admitted">

        <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.admittedMain" />

        AND ( itemCreate <![CDATA[< ]]> #{delayConfig.admittedStartTime}
        OR ( itemCreate  <![CDATA[> ]]> #{delayConfig.admittedStartTime} AND dayDiff  <![CDATA[<= ]]> #{delayConfig.admittedDayInterval} )
        OR (institutionId IN
        <foreach collection="delayConfig.admittedExemptInstitutionIds" item="admittedExemptInstitutionId" open="(" separator="," close=")">
            #{admittedExemptInstitutionId}
        </foreach>
            )
        OR NOT EXISTS (SELECT 1 FROM r_student_offer_item_step AS rsois1 WHERE rsois1.fk_student_offer_item_id = msoi2.id AND rsois1.fk_student_offer_item_step_id = 2)
        )
    </sql>

    <!-- 延迟申请反馈数 -->
    <sql id="admittedDelay">

        <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.admittedMain" />

        AND ( ( itemCreate  <![CDATA[> ]]> #{delayConfig.admittedStartTime}
        AND dayDiff <![CDATA[ > ]]> #{delayConfig.admittedDayInterval} )
        AND (institutionId NOT IN
        <foreach collection="delayConfig.admittedExemptInstitutionIds" item="admittedExemptInstitutionId" open="(" separator="," close=")">
            #{admittedExemptInstitutionId}
        </foreach>
            )
        AND EXISTS (SELECT 1 FROM r_student_offer_item_step AS rsois1 WHERE rsois1.fk_student_offer_item_id = msoi2.id AND rsois1.fk_student_offer_item_step_id = 2)
        )
    </sql>

    <!-- 申请反馈数主sql -->
    <sql id="admittedMain">
        SELECT
            msoi2.id,
            MIN( msoi2.fk_student_id) AS fk_student_id,
            MIN(msoi2.gmt_create) AS itemCreate,
            LEAST(IFNULL(MIN( rsois2.gmt_create ), '9999-01-01 00:00:00'),
            IFNULL(MIN( rsois3.gmt_create), '9999-01-01 00:00:00'),
            IFNULL(MIN(rsoisFailure2.gmt_create), '9999-01-01 00:00:00'),
            IFNULL(MIN(rsoisFailure3.gmt_create), '9999-01-01 00:00:00')) AS minGmtCreate,
            LEAST(
                DATEDIFF(
                    IFNULL(
                        CAST(MIN(IFNULL(rsoisAdmitted2.reassign_time, rsoisAdmitted2.gmt_create)) AS DATETIME),
                        CAST('9999-01-01 00:00:00' AS DATETIME)
                    ),
                    IFNULL(
                        CAST(MIN(IFNULL(rsoisSubmitted2.reassign_time, rsoisSubmitted2.gmt_create)) AS DATETIME),
                        CAST('1970-01-01 00:00:00' AS DATETIME)
                    )),
                DATEDIFF(
                    IFNULL(
                        CAST(MIN(IFNULL(rsoisFailure2.reassign_time, rsoisFailure2.gmt_create)) AS DATETIME),
                        CAST('9999-01-01 00:00:00' AS DATETIME)
                    ),
                    IFNULL(
                        CAST(MIN(IFNULL(rsoisSubmitted2.reassign_time, rsoisSubmitted2.gmt_create)) AS DATETIME),
                        CAST('1970-01-01 00:00:00' AS DATETIME)
                    )),
                        DATEDIFF(
                    IFNULL(
                        CAST(MIN(IFNULL(rsoisAdmittedFailure3.reassign_time, rsoisAdmittedFailure3.gmt_create)) AS DATETIME),
                        CAST('9999-01-01 00:00:00' AS DATETIME)
                    ),
                    IFNULL(
                        CAST(MIN(IFNULL(rsoisSubmittedFailure3.reassign_time, rsoisSubmittedFailure3.gmt_create)) AS DATETIME),
                        CAST('1970-01-01 00:00:00' AS DATETIME)
                    )),
                DATEDIFF(
                    IFNULL(
                        CAST(MIN(IFNULL(rsoisFailure3.reassign_time, rsoisFailure3.gmt_create)) AS DATETIME),
                        CAST('9999-01-01 00:00:00' AS DATETIME)
                    ),
                    IFNULL(
                        CAST(MIN(IFNULL(rsoisSubmittedFailure3.reassign_time, rsoisSubmittedFailure3.gmt_create)) AS DATETIME),
                        CAST('1970-01-01 00:00:00' AS DATETIME)
                    ))
                ) AS dayDiff,
                MIN(msoi2.fk_institution_id) AS institutionId

            FROM
            ais_sale_center.m_student_offer_item AS msoi2
            LEFT JOIN (
                SELECT rsois2.* FROM ais_sale_center.r_student_offer_item_step AS rsois2
                INNER JOIN ais_sale_center.u_student_offer_item_step AS usois2 ON usois2.id = rsois2.fk_student_offer_item_step_id AND  usois2.step_key = 'STEP_ADMITTED'
            )rsois2 ON rsois2.fk_student_offer_item_id = msoi2.id
            LEFT JOIN ais_sale_center.u_student_offer_item_step AS usois2 ON usois2.id = rsois2.fk_student_offer_item_step_id
            LEFT JOIN ais_sale_center.u_student_offer_item_step AS usois2Now ON usois2Now.id = msoi2.fk_student_offer_item_step_id
            LEFT JOIN ais_sale_center.m_student AS ms2 ON ms2.id = msoi2.fk_student_id
            LEFT JOIN ais_sale_center.s_media_and_attached AS smaa2 ON smaa2.fk_table_id = msoi2.id
            AND smaa2.fk_table_name = 'm_student_offer_item'
            AND smaa2.type_key = 'entrance_fail_media'
            LEFT JOIN (
                SELECT rsoisFailure2.* FROM ais_sale_center.r_student_offer_item_step AS rsoisFailure2
                INNER JOIN ais_sale_center.u_student_offer_item_step AS usois2 ON usois2.id = rsoisFailure2.fk_student_offer_item_step_id AND usois2.step_key = 'STEP_FAILURE'
            ) AS rsoisFailure2 ON rsoisFailure2.fk_student_offer_item_id = msoi2.id AND IFNULL( smaa2.id, '' ) != ''

            LEFT JOIN ais_sale_center.r_student_offer_item_step AS rsoisSubmitted2 ON rsoisSubmitted2.fk_student_offer_item_id = msoi2.id AND rsoisSubmitted2.fk_student_offer_item_step_id = 2
            LEFT JOIN ais_sale_center.r_student_offer_item_step AS rsoisAdmitted2 ON rsoisAdmitted2.fk_student_offer_item_id = msoi2.id AND rsoisAdmitted2.fk_student_offer_item_step_id = 4

            <!-- 子计划 -->
            LEFT JOIN ais_sale_center.m_student_offer_item AS msoi3 ON msoi3.fk_parent_student_offer_item_id = msoi2.id
            AND msoi3.is_follow = 0
            AND msoi3.STATUS = 1
            LEFT JOIN (
                SELECT rsois3.* FROM ais_sale_center.r_student_offer_item_step AS rsois3
                INNER JOIN ais_sale_center.u_student_offer_item_step AS usois3 ON usois3.id = rsois3.fk_student_offer_item_step_id AND  usois3.step_key = 'STEP_ADMITTED'
            ) AS rsois3 ON rsois3.fk_student_offer_item_id = msoi2.id
            LEFT JOIN ais_sale_center.u_student_offer_item_step AS usois3 ON usois3.id = msoi3.fk_student_offer_item_step_id
            LEFT JOIN ais_sale_center.u_student_offer_item_step AS usois3Now ON usois3Now.id = msoi3.fk_student_offer_item_step_id
            LEFT JOIN ais_sale_center.s_media_and_attached AS smaa3 ON smaa3.fk_table_id = msoi2.id
            AND smaa3.fk_table_name = 'm_student_offer_item'
            AND smaa3.type_key = 'entrance_fail_media'
            LEFT JOIN (
                SELECT rsoisFailure3.* FROM ais_sale_center.r_student_offer_item_step AS rsoisFailure3
                INNER JOIN ais_sale_center.u_student_offer_item_step AS usois3 ON usois3.id = rsoisFailure3.fk_student_offer_item_step_id AND usois3.step_key = 'STEP_FAILURE'
            ) AS rsoisFailure3 ON rsoisFailure3.fk_student_offer_item_id = msoi3.id AND IFNULL( smaa3.id, '' ) != ''

            LEFT JOIN ais_sale_center.r_student_offer_item_step AS rsoisSubmittedFailure3 ON rsoisSubmittedFailure3.fk_student_offer_item_id = msoi2.id AND rsoisSubmittedFailure3.fk_student_offer_item_step_id = 2
            LEFT JOIN ais_sale_center.r_student_offer_item_step AS rsoisAdmittedFailure3 ON rsoisAdmittedFailure3.fk_student_offer_item_id = msoi2.id AND rsoisAdmittedFailure3.fk_student_offer_item_step_id = 4

            WHERE
            IFNULL( msoi2.fk_parent_student_offer_item_id, 0 ) = 0
            AND ms2.fk_company_id IN
             <foreach collection="bdStudentStatisticalComparisonDto.fkCompanyIds" item="fkCompanyId" open="(" separator="," close=")">
                #{fkCompanyId}
            </foreach>
            AND msoi2.STATUS = 1
            AND (
                ( IFNULL( usois2.step_key, '' ) != '' AND usois2Now.step_order >= usois2.step_order )
                OR ( IFNULL( usois3.step_key, '' ) != '' AND usois3Now.step_order >= usois3.step_order )
                OR ( IFNULL( smaa2.id, '' ) != '' AND IFNULL( rsoisFailure2.id, '' ) != '' )
                OR ( IFNULL( smaa3.id, '' ) != '' AND IFNULL( rsoisFailure3.id, '' ) != '' )
            )
            GROUP BY
            msoi2.id

            HAVING
            DATE_FORMAT( minGmtCreate, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{beginTime}, '%Y-%m-%d' )
            AND DATE_FORMAT( minGmtCreate, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{endTime}, '%Y-%m-%d' )

    </sql>

    <!-- 提交数 -->
    <sql id="mainItem">

        <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.submit" />

        AND ( minGmtCreate <![CDATA[< ]]> #{delayConfig.submittedStartTime}
        OR ( minGmtCreate  <![CDATA[> ]]> #{delayConfig.submittedStartTime} AND dayDiff  <![CDATA[<= ]]> #{delayConfig.submittedDayInterval} )
        OR EXISTS (SELECT 1 FROM m_student_offer_item AS msoi1 WHERE msoi1.id = msoi2.id AND msoi1.new_app_status IS NOT NULL)
        )
    </sql>

    <!-- 延迟提交数 -->
    <sql id="mainItemDelay">

        <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.submit" />

        AND ( minGmtCreate  <![CDATA[> ]]> #{delayConfig.submittedStartTime} AND dayDiff  <![CDATA[ > ]]> #{delayConfig.submittedDayInterval} )
        AND NOT EXISTS (SELECT 1 FROM m_student_offer_item AS msoi1 WHERE msoi1.id = msoi2.id AND msoi1.new_app_status IS NOT NULL)
    </sql>

    <!-- 提交数主sql -->
    <sql id="submit">
        SELECT
            msoi2.id, MIN( msoi2.fk_student_id) AS fk_student_id, MIN(rsois.gmt_create) AS minGmtCreate,
            IFNULL( DATEDIFF( MIN( IFNULL(rsois4.reassign_time, rsois4.gmt_create)), MIN( IFNULL(rsois3.reassign_time, rsois3.gmt_create ))), 0 ) AS dayDiff
        FROM
            ais_sale_center.m_student_offer_item AS msoi2
                INNER JOIN ais_sale_center.r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = msoi2.id
                INNER JOIN ais_sale_center.u_student_offer_item_step AS usois ON usois.id = rsois.fk_student_offer_item_step_id
                INNER JOIN ais_sale_center.m_student AS ms2 ON ms2.id = msoi2.fk_student_id

                LEFT JOIN ais_sale_center.r_student_offer_item_step AS rsois3 ON rsois3.fk_student_offer_item_id = msoi2.id AND rsois3.fk_student_offer_item_step_id = 1
                LEFT JOIN ais_sale_center.r_student_offer_item_step AS rsois4 ON rsois4.fk_student_offer_item_id = msoi2.id AND rsois4.fk_student_offer_item_step_id = 2
        WHERE usois.step_key IN ('STEP_SUBMITTED','STEP_NOTIFIED','STEP_APP_RECEIVED')
          AND msoi2.status = 1
          AND ms2.fk_company_id IN
         <foreach collection="bdStudentStatisticalComparisonDto.fkCompanyIds" item="fkCompanyId" open="(" separator="," close=")">
            #{fkCompanyId}
        </foreach>
          AND IFNULL( msoi2.fk_parent_student_offer_item_id, 0 ) = 0
        GROUP BY
            msoi2.id
        HAVING
            DATE_FORMAT( minGmtCreate, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{beginTime}, '%Y-%m-%d' )
           AND DATE_FORMAT( minGmtCreate, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{endTime}, '%Y-%m-%d' )
    </sql>

    <!-- 结算完成 -->
    <sql id="settlementCompleted">
        SELECT
            a.fk_student_offer_item_id AS id,
            MAX(IF
                ( a.fk_staff_commission_step_key = 'STEP_NEW_APP', a.fk_student_offer_item_id, NULL )) AS commissionActionItemId,
            MAX(IF
                ( a.fk_staff_commission_step_key = 'STEP_ADMITTED', a.fk_student_offer_item_id, NULL )) AS commissionAdmittedItemId,
            MAX(IF
                ( a.fk_staff_commission_step_key = 'STEP_OFFER_SELECTION', a.fk_student_id, NULL )) AS commissionActionOsStudentId,
            MAX(IF
                ( a.fk_staff_commission_step_key = 'STEP_OFFER_SELECTION', a.fk_student_offer_item_id, NULL )) AS commissionActionOsItemId,
            MAX(IF
                ( a.fk_staff_commission_step_key = 'STEP_VISA_GRANTED', a.fk_student_offer_item_id, NULL )) AS commissionActionVisaItemId,
            MAX(IF
                ( a.fk_staff_commission_step_key = 'STEP_VISA_GRANTED', a.fk_student_id, NULL )) AS commissionActionVisaStudentId,
            MAX(IF
                ( a.fk_staff_commission_step_key = 'STEP_ENROLLED', a.fk_student_id, NULL )) AS commissionActionEnrolledStudentId,
            MAX(IF
                ( a.fk_staff_commission_step_key = 'STEP_ENROLLED', a.fk_student_offer_item_id, NULL )) AS commissionActionEnrolledItemId,
            MAX(IF
                ( a.fk_staff_commission_step_key = 'STEP_VISA_GRANTED_BACK', a.fk_student_id, NULL )) AS commissionActionEnrolledVisaStudentId,
            MAX(IF
                ( a.fk_staff_commission_step_key = 'STEP_VISA_GRANTED_BACK', a.fk_student_offer_item_id, NULL )) AS commissionActionEnrolledVisaItemId,
            MAX(IF
                ( a.fk_staff_commission_step_key = 'STEP_SUBMITTED_DELAY', a.fk_student_offer_item_id, NULL )) AS commissionActionDelayItemId,
            MAX(IF
                ( a.fk_staff_commission_step_key = 'STEP_ADMITTED_DELAY', a.fk_student_offer_item_id, NULL )) AS commissionAdmittedDelayItemId
        FROM
            ais_sale_center.m_staff_commission_action a
        WHERE a.fk_company_id IN
         <foreach collection="bdStudentStatisticalComparisonDto.fkCompanyIds" item="fkCompanyId" open="(" separator="," close=")">
            #{fkCompanyId}
        </foreach>
          AND status = 2
          AND a.fk_student_project_role_key = #{bdStudentStatisticalComparisonDto.projectRoleKey}
          AND DATE_FORMAT( a.performance_time, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{beginTime}, '%Y-%m-%d' )
          AND DATE_FORMAT( a.performance_time, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{endTime}, '%Y-%m-%d' )
        GROUP BY
            a.fk_student_offer_item_id
    </sql>
    <!-- 旧学生数 -->
    <sql id="oldTotalStudentNum">
    <!--    SELECT MIN(msoi3.id) AS id, MIN( msoi3.fk_student_id) AS fk_student_id
        <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
            , msoi3.fk_area_country_id
        </if>
        FROM ais_sale_center.m_student_offer_item AS msoi3
        INNER JOIN ais_sale_center.r_student_offer_item_step AS rsois3 ON rsois3.fk_student_offer_item_id = msoi3.id
        INNER JOIN ais_sale_center.u_student_offer_item_step AS usois3 ON usois3.id = rsois3.fk_student_offer_item_step_id
        INNER JOIN ais_sale_center.m_student AS ms3 ON ms3.id = msoi3.fk_student_id
        INNER JOIN (
            SELECT
                ms2.id AS fk_student_id, MIN( msoi2.gmt_create) AS minGmtCreate
                <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
                    , msoi2.fk_area_country_id
                </if>
                FROM
                ais_sale_center.m_student_offer_item AS msoi2
                INNER JOIN ais_sale_center.r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = msoi2.id
                INNER JOIN ais_sale_center.u_student_offer_item_step AS usois ON usois.id = rsois.fk_student_offer_item_step_id
                INNER JOIN ais_sale_center.m_student AS ms2 ON ms2.id = msoi2.fk_student_id
                WHERE usois.step_key = 'STEP_NEW_APP'
                AND msoi2.status = 1
                AND ms2.fk_company_id IN
         <foreach collection="bdStudentStatisticalComparisonDto.fkCompanyIds" item="fkCompanyId" open="(" separator="," close=")">
            #{fkCompanyId}
        </foreach>
                AND IFNULL( msoi2.fk_parent_student_offer_item_id, 0 ) = 0
                GROUP BY
                ms2.id
                <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
                    ,msoi2.fk_area_country_id
                </if>
            )msoi4 ON msoi4.fk_student_id = ms3.id AND msoi4.minGmtCreate = msoi3.gmt_create
            <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
                AND msoi4.fk_area_country_id = msoi3.fk_area_country_id
            </if>
            WHERE usois3.step_key = 'STEP_NEW_APP'
            AND DATE_FORMAT( rsois3.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{beginTime}, '%Y-%m-%d' )
            AND DATE_FORMAT( rsois3.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{endTime}, '%Y-%m-%d' )
            AND DATE_FORMAT( ms3.gmt_create, '%Y-%m-%d' ) <![CDATA[< ]]> DATE_FORMAT(#{htiStartTime}, '%Y-%m-%d' )
        GROUP BY
            msoi3.fk_student_id
            <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
                ,msoi3.fk_area_country_id
            </if>

        -->

        SELECT
            msoi2.id, MIN( msoi2.fk_student_id) AS fk_student_id
            <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
                , msoi2.fk_area_country_id
            </if>
            FROM
            ais_sale_center.m_student_offer_item AS msoi2
            INNER JOIN ais_sale_center.m_student AS ms2 ON ms2.id = msoi2.fk_student_id
            INNER JOIN ais_sale_center.r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = msoi2.id
            INNER JOIN (
                SELECT a. fk_student_id, c.minGmtCreate, MIN(a.id) minItemId
                <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
                    , a.fk_area_country_id
                </if>
                FROM ais_sale_center.m_student_offer_item a
                INNER JOIN ais_sale_center.r_student_offer_item_step b ON a.id=b.fk_student_offer_item_id
                INNER JOIN (
                    SELECT
                    ms3.id,
                    MIN( rsois.gmt_create ) AS minGmtCreate
                    <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
                        , msoi3.fk_area_country_id
                    </if>
                    FROM
                    ais_sale_center.m_student AS ms3
                    INNER JOIN ais_sale_center.m_student_offer_item AS msoi3 ON msoi3.fk_student_id = ms3.id
                    INNER JOIN ais_sale_center.r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = msoi3.id
                    INNER JOIN ais_sale_center.u_student_offer_item_step AS usois ON usois.id = rsois.fk_student_offer_item_step_id
                    WHERE
                    ms3.fk_company_id IN
                     <foreach collection="bdStudentStatisticalComparisonDto.fkCompanyIds" item="fkCompanyId" open="(" separator="," close=")">
                        #{fkCompanyId}
                    </foreach>
                    AND msoi3.status = 1
                    AND IFNULL( msoi3.fk_parent_student_offer_item_id, 0 ) = 0
                    GROUP BY
                    ms3.id
                    <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
                        ,msoi3.fk_area_country_id
                    </if>
                    ) c ON a.fk_student_id=c.id AND b.gmt_create=c.minGmtCreate
                <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
                    AND c.fk_area_country_id = a.fk_area_country_id
                </if>
                GROUP BY a.fk_student_id, c.minGmtCreate
                <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
                    ,a.fk_area_country_id
                </if>
            ) a ON a.minItemId = msoi2.id AND rsois.gmt_create=a.minGmtCreate
            <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
                AND a.fk_area_country_id = msoi2.fk_area_country_id
            </if>
            WHERE
                DATE_FORMAT( msoi2.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{beginTime}, '%Y-%m-%d' )
            AND DATE_FORMAT( msoi2.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{endTime}, '%Y-%m-%d' )
            AND DATE_FORMAT( ms2.gmt_create, '%Y-%m-%d' ) <![CDATA[< ]]> DATE_FORMAT(#{htiStartTime}, '%Y-%m-%d' )
            GROUP BY
            msoi2.id
            <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
                ,msoi2.fk_area_country_id
            </if>
    </sql>


    <!-- 合同未返回数 -->
    <sql id="unsignedNum">
        FROM
        ais_permission_center.m_staff AS ms
        INNER JOIN ais_sale_center.r_staff_bd_code AS rsbc2 ON rsbc2.fk_staff_id = ms.id
        INNER JOIN ais_sale_center.r_agent_staff AS ras ON ras.fk_staff_id = ms.id AND ras.is_active = 1
        INNER JOIN ais_sale_center.m_agent AS ma ON ma.id = ras.fk_agent_id
        LEFT JOIN (
        SELECT ma2.id FROM m_agent AS ma2
        INNER JOIN ais_sale_center.m_agent_contract AS mac2 ON mac2.fk_agent_id = ma2.id
        INNER JOIN ais_sale_center.s_media_and_attached AS smaa2 ON smaa2.fk_table_id = mac2.id
        AND smaa2.fk_table_name = 'm_agent_contract' AND smaa2.type_key = 'sale_contract_file'
        GROUP BY ma2.id
        )AS contractFlag ON contractFlag.id = ma.id
        WHERE rsbc2.bd_code NOT LIKE 'T%'
        AND ms.fk_company_id = #{agentAnnualSummaryDto.fkCompanyId}
        <!-- 业务下属 -->
        AND ms.id IN
        <foreach collection="staffFollowerIds" item="staffId" index="index" open="(" separator="," close=")">
            #{staffId}
        </foreach>
        <!-- bd -->
        <if test ="agentAnnualSummaryDto.fkBdIds!=null and agentAnnualSummaryDto.fkBdIds.size()>0" >
            AND ms.id IN
            <foreach collection="agentAnnualSummaryDto.fkBdIds" item="fkBdId" open="(" separator="," close=")">
                #{fkBdId}
            </foreach>
        </if>
        <!-- 代理所在区域 -->
        <if test="agentAnnualSummaryDto.fkAreaCountryId != null">
            AND ma.fk_area_country_id = #{agentAnnualSummaryDto.fkAreaCountryId}
        </if>
        <if test="agentAnnualSummaryDto.fkAreaStateIdList != null and agentAnnualSummaryDto.fkAreaStateIdList.size()>0">
            AND ma.fk_area_state_id IN
            <foreach collection="agentAnnualSummaryDto.fkAreaStateIdList" item="fkAreaStateId" open="(" separator="," close=")">
                #{fkAreaStateId}
            </foreach>
        </if>
        <if test="agentAnnualSummaryDto.fkAreaCityIdList != null and agentAnnualSummaryDto.fkAreaCityIdList.size()>0">
            AND ma.fk_area_city_id IN
            <foreach collection="agentAnnualSummaryDto.fkAreaCityIdList" item="fkAreaCityId" open="(" separator="," close=")">
                #{fkAreaCityId}
            </foreach>
        </if>
        <!-- 大区 -->
        <if test="agentAnnualSummaryDto.fkAreaRegionIdLIst != null and agentAnnualSummaryDto.fkAreaRegionIdLIst.size()>0">
            AND rsbc2.fk_area_region_id IN
            <foreach collection="agentAnnualSummaryDto.fkAreaRegionIdLIst" item="fkAreaRegionId" open="(" separator="," close=")">
                #{fkAreaRegionId}
            </foreach>
        </if>
    </sql>

    <!-- 新学生数 -->
    <sql id="newTotalStudentNum">
     <!--    SELECT MIN(msoi3.id) AS id, MIN( msoi3.fk_student_id) AS fk_student_id
        <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
            , msoi3.fk_area_country_id
        </if>
        FROM ais_sale_center.m_student_offer_item AS msoi3
        INNER JOIN ais_sale_center.r_student_offer_item_step AS rsois3 ON rsois3.fk_student_offer_item_id = msoi3.id
        INNER JOIN ais_sale_center.u_student_offer_item_step AS usois3 ON usois3.id = rsois3.fk_student_offer_item_step_id
        INNER JOIN ais_sale_center.m_student AS ms3 ON ms3.id = msoi3.fk_student_id
        INNER JOIN (
            SELECT
                ms2.id AS fk_student_id, MIN( msoi2.gmt_create) AS minGmtCreate
                <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
                    , msoi2.fk_area_country_id
                </if>
                FROM
                ais_sale_center.m_student_offer_item AS msoi2
                INNER JOIN ais_sale_center.r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = msoi2.id
                INNER JOIN ais_sale_center.u_student_offer_item_step AS usois ON usois.id = rsois.fk_student_offer_item_step_id
                INNER JOIN ais_sale_center.m_student AS ms2 ON ms2.id = msoi2.fk_student_id
                WHERE usois.step_key = 'STEP_NEW_APP'
                AND msoi2.status = 1
                AND ms2.fk_company_id IN
         <foreach collection="bdStudentStatisticalComparisonDto.fkCompanyIds" item="fkCompanyId" open="(" separator="," close=")">
            #{fkCompanyId}
        </foreach>
                AND IFNULL( msoi2.fk_parent_student_offer_item_id, 0 ) = 0
                GROUP BY
                ms2.id
                <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
                    ,msoi2.fk_area_country_id
                </if>
            )msoi4 ON msoi4.fk_student_id = ms3.id AND msoi4.minGmtCreate = msoi3.gmt_create
            <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
                AND msoi4.fk_area_country_id = msoi3.fk_area_country_id
            </if>
            WHERE usois3.step_key = 'STEP_NEW_APP'
            AND DATE_FORMAT( rsois3.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{beginTime}, '%Y-%m-%d' )
            AND DATE_FORMAT( rsois3.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{endTime}, '%Y-%m-%d' )
            AND DATE_FORMAT( ms3.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{htiStartTime}, '%Y-%m-%d' )
            GROUP BY
            msoi3.fk_student_id
            <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
                ,msoi3.fk_area_country_id
            </if>
            -->


            SELECT
            msoi2.id, MIN( msoi2.fk_student_id) AS fk_student_id
            <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
                , msoi2.fk_area_country_id
            </if>
            FROM
            ais_sale_center.m_student_offer_item AS msoi2
            INNER JOIN ais_sale_center.m_student AS ms2 ON ms2.id = msoi2.fk_student_id
            INNER JOIN ais_sale_center.r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = msoi2.id
            INNER JOIN (
                SELECT a. fk_student_id, c.minGmtCreate, MIN(a.id) minItemId
                <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
                    , a.fk_area_country_id
                </if>
                FROM ais_sale_center.m_student_offer_item a
                INNER JOIN ais_sale_center.r_student_offer_item_step b ON a.id=b.fk_student_offer_item_id
                INNER JOIN (
                    SELECT
                    ms3.id,
                    MIN( rsois.gmt_create ) AS minGmtCreate
                    <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
                        , msoi3.fk_area_country_id
                    </if>
                    FROM
                    ais_sale_center.m_student AS ms3
                    INNER JOIN ais_sale_center.m_student_offer_item AS msoi3 ON msoi3.fk_student_id = ms3.id
                    INNER JOIN ais_sale_center.r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = msoi3.id
                    INNER JOIN ais_sale_center.u_student_offer_item_step AS usois ON usois.id = rsois.fk_student_offer_item_step_id
                    WHERE
                    ms3.fk_company_id IN
         <foreach collection="bdStudentStatisticalComparisonDto.fkCompanyIds" item="fkCompanyId" open="(" separator="," close=")">
            #{fkCompanyId}
        </foreach>
                    AND msoi3.status = 1
                    AND IFNULL( msoi3.fk_parent_student_offer_item_id, 0 ) = 0
                    GROUP BY
                    ms3.id
                    <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
                        ,msoi3.fk_area_country_id
                    </if>
                    ) c ON a.fk_student_id=c.id AND b.gmt_create=c.minGmtCreate
                <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
                    AND c.fk_area_country_id = a.fk_area_country_id
                </if>
                GROUP BY a.fk_student_id, c.minGmtCreate
                <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
                    ,a.fk_area_country_id
                </if>
            ) a ON a.minItemId = msoi2.id AND rsois.gmt_create=a.minGmtCreate
            <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
                AND a.fk_area_country_id = msoi2.fk_area_country_id
            </if>
            WHERE
                DATE_FORMAT( msoi2.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{beginTime}, '%Y-%m-%d' )
            AND DATE_FORMAT( msoi2.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{endTime}, '%Y-%m-%d' )
            AND DATE_FORMAT( ms2.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{htiStartTime}, '%Y-%m-%d' )
            GROUP BY
            msoi2.id
            <if test="bdStudentStatisticalComparisonDto.isDistinguishCountryFlag">
                ,msoi2.fk_area_country_id
            </if>
    </sql>

    <!-- 代理统计报表sql -->
    <sql id="agentStatistical">
        FROM
        ais_permission_center.m_staff AS ms
        LEFT JOIN ais_sale_center.r_staff_bd_code AS rsbc2 ON rsbc2.fk_staff_id = ms.id
        LEFT JOIN ais_sale_center.r_agent_staff AS ras ON ras.fk_staff_id = ms.id AND ras.is_active = 1
        <if test="agentAnnualSummaryDto.statisticalType != 2">
            INNER JOIN ais_sale_center.m_agent AS ma ON ma.id = ras.fk_agent_id
        </if>
        <if test="agentAnnualSummaryDto.statisticalType == 2">
            INNER JOIN (
                SELECT msoi1.fk_agent_id AS agentId, c.minCreate, MIN(msoi1.fk_staff_id) AS fk_staff_id
                FROM ais_sale_center.m_student_offer_item AS msoi1
                    INNER JOIN (
                    SELECT ma2.id, MIN(msoi2.gmt_create) AS minCreate FROM ais_sale_center.m_agent AS ma2
                    INNER JOIN ais_sale_center.m_student_offer_item AS msoi2 ON msoi2.fk_agent_id = ma2.id
                    WHERE
                    <!-- 业务国家 -->
                    msoi2.fk_area_country_id IN
                    <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">
                        #{countryId}
                    </foreach>
                    AND msoi2.status = 1
                    AND msoi2.is_follow = 0
                    GROUP BY ma2.id
                    )c ON c.id = msoi1.fk_agent_id AND c.minCreate = msoi1.gmt_create
                GROUP BY msoi1.fk_agent_id, c.minCreate
            ) AS firstItem ON firstItem.fk_staff_id = ms.id
            INNER JOIN ais_sale_center.m_agent AS ma ON ma.id = firstItem.agentId
        </if>

        WHERE rsbc2.bd_code NOT LIKE 'T%'
        AND ms.fk_company_id = #{agentAnnualSummaryDto.fkCompanyId}
        <!-- 业务下属 -->
        AND ms.id IN
        <foreach collection="staffFollowerIds" item="staffId" index="index" open="(" separator="," close=")">
            #{staffId}
        </foreach>

        <choose>
            <when test="agentAnnualSummaryDto.statisticalType == 1">
                AND (
                DATE_FORMAT( ma.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentAnnualSummaryDto.jumpBeginTime}, '%Y-%m-%d' )
                AND DATE_FORMAT( ma.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentAnnualSummaryDto.jumpEndTime}, '%Y-%m-%d' )
                )
            </when>
            <when test="agentAnnualSummaryDto.statisticalType == 2">
                AND (DATE_FORMAT( firstItem.minCreate, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentAnnualSummaryDto.jumpBeginTime}, '%Y-%m-%d' )
                AND DATE_FORMAT( firstItem.minCreate, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentAnnualSummaryDto.jumpEndTime}, '%Y-%m-%d' )
                )
            </when>
        </choose>
        <!-- bd -->
        <if test ="agentAnnualSummaryDto.fkBdIds!=null and agentAnnualSummaryDto.fkBdIds.size()>0" >
            AND ms.id IN
            <foreach collection="agentAnnualSummaryDto.fkBdIds" item="fkBdId" open="(" separator="," close=")">
                #{fkBdId}
            </foreach>
        </if>
        <!-- 代理所在区域 -->
        <if test="agentAnnualSummaryDto.fkAreaCountryId != null">
            AND ma.fk_area_country_id = #{agentAnnualSummaryDto.fkAreaCountryId}
        </if>
        <if test="agentAnnualSummaryDto.fkAreaStateIdList != null and agentAnnualSummaryDto.fkAreaStateIdList.size()>0">
            AND ma.fk_area_state_id IN
            <foreach collection="agentAnnualSummaryDto.fkAreaStateIdList" item="fkAreaStateId" open="(" separator="," close=")">
                #{fkAreaStateId}
            </foreach>
        </if>
        <if test="agentAnnualSummaryDto.fkAreaCityIdList != null and agentAnnualSummaryDto.fkAreaCityIdList.size()>0">
            AND ma.fk_area_city_id IN
            <foreach collection="agentAnnualSummaryDto.fkAreaCityIdList" item="fkAreaCityId" open="(" separator="," close=")">
                #{fkAreaCityId}
            </foreach>
        </if>
        <!-- 大区 -->
        <if test="agentAnnualSummaryDto.fkAreaRegionIdLIst != null and agentAnnualSummaryDto.fkAreaRegionIdLIst.size()>0">
            AND rsbc2.fk_area_region_id IN
            <foreach collection="agentAnnualSummaryDto.fkAreaRegionIdLIst" item="fkAreaRegionId" open="(" separator="," close=")">
                #{fkAreaRegionId}
            </foreach>
        </if>
    </sql>

    <!-- 代理统计报表sql （按代理创建时间统计）-->
    <sql id="agentStatisticalByCreateTime">
        FROM
        ais_permission_center.m_staff AS ms
        INNER JOIN ais_sale_center.r_staff_bd_code AS rsbc2 ON rsbc2.fk_staff_id = ms.id
        INNER JOIN ais_sale_center.m_app_agent AS maa ON maa.fk_staff_id = ms.id

        WHERE rsbc2.bd_code NOT LIKE 'T%'
        AND ms.fk_company_id = #{agentAnnualSummaryDto.fkCompanyId}
        <!-- 业务下属 -->
        AND ms.id IN
        <foreach collection="staffFollowerIds" item="staffId" index="index" open="(" separator="," close=")">
            #{staffId}
        </foreach>

        AND (
        DATE_FORMAT( maa.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentAnnualSummaryDto.jumpBeginTime}, '%Y-%m-%d' )
        AND DATE_FORMAT( maa.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentAnnualSummaryDto.jumpEndTime}, '%Y-%m-%d' )
        )

        AND maa.app_status != 2

        <!-- bd -->
        <if test ="agentAnnualSummaryDto.fkBdIds!=null and agentAnnualSummaryDto.fkBdIds.size()>0" >
            AND ms.id IN
            <foreach collection="agentAnnualSummaryDto.fkBdIds" item="fkBdId" open="(" separator="," close=")">
                #{fkBdId}
            </foreach>
        </if>
        <!-- 代理所在区域 -->
        <if test="agentAnnualSummaryDto.fkAreaCountryId != null">
            AND maa.fk_area_country_id = #{agentAnnualSummaryDto.fkAreaCountryId}
        </if>
        <if test="agentAnnualSummaryDto.fkAreaStateIdList != null and agentAnnualSummaryDto.fkAreaStateIdList.size()>0">
            AND maa.fk_area_state_id IN
            <foreach collection="agentAnnualSummaryDto.fkAreaStateIdList" item="fkAreaStateId" open="(" separator="," close=")">
                #{fkAreaStateId}
            </foreach>
        </if>
        <if test="agentAnnualSummaryDto.fkAreaCityIdList != null and agentAnnualSummaryDto.fkAreaCityIdList.size()>0">
            AND maa.fk_area_city_id IN
            <foreach collection="agentAnnualSummaryDto.fkAreaCityIdList" item="fkAreaCityId" open="(" separator="," close=")">
                #{fkAreaCityId}
            </foreach>
        </if>
        <!-- 大区 -->
        <if test="agentAnnualSummaryDto.fkAreaRegionIdLIst != null and agentAnnualSummaryDto.fkAreaRegionIdLIst.size()>0">
            AND rsbc2.fk_area_region_id IN
            <foreach collection="agentAnnualSummaryDto.fkAreaRegionIdLIst" item="fkAreaRegionId" open="(" separator="," close=")">
                #{fkAreaRegionId}
            </foreach>
        </if>

    </sql>

    <!-- bd统计跳转共用sql -->
    <sql id="bdStatisticalJumpSql">
        SELECT
            <choose>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 1">
                    oldFirstStudentNum.fk_student_id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 2">
                    newFirstStudentNum.fk_student_id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 3">
                    studentNum.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 4">
                    os.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 5">
                    visa.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 6">
                    enrolled.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 7">
                    oldTotalStudentNum.fk_student_id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 8">
                    newTotalStudentNum.fk_student_id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 9">
                    admitted.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 10">
                    firstStudentNum.fk_student_id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 11">
                    unsettledCommissionActionOsNum.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 12">
                    unsettledCommissionActionItemNum.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 13">
                    unsettledCommissionActionVisaNum.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 14">
                    unsettledCommissionActionEnrolledNum.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 15">
                    unsettledCommissionActionAdmittedNum.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 16">
                    commissionActionNum.commissionActionOsItemId AS id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 17">
                    commissionActionNum.commissionActionItemId AS id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 18">
                    commissionActionNum.commissionActionVisaItemId AS id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 19">
                    commissionActionNum.commissionActionEnrolledItemId AS id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 20">
                    commissionActionNum.commissionAdmittedItemId AS id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 21">
                    unsettledCommissionActionEnrolledVisaNum.fk_student_id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 22">
                    commissionActionNum.commissionActionEnrolledVisaStudentId AS fk_student_id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 23">
                    enrolledVisa.fk_student_id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 24">
                    repeatOs.repeatOsStudentId AS fk_student_id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 25">
                    studentNumDelay.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 26">
                    admittedDelay.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 27">
                    unsettledCommissionActionDelayItemNum.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 28">
                    commissionActionDelayItemId.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 29">
                    unsettledCommissionActionAdmittedDelayNum.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 30">
                    commissionAdmittedDelayItemId.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 31">
                    appOptNum.fk_student_id
                </when>
            </choose>
        FROM
            ais_sale_center.m_student_offer_item AS msoi
            INNER JOIN ais_sale_center.m_agent AS ma ON ma.id = msoi.fk_agent_id
            INNER JOIN ais_sale_center.r_staff_bd_code AS rsbc ON rsbc.fk_staff_id = msoi.fk_staff_id
            LEFT JOIN ais_institution_center.u_area_region AS uar ON uar.id = rsbc.fk_area_region_id
            INNER JOIN ais_sale_center.m_student AS ms ON ms.id = msoi.fk_student_id
            <if test="bdStudentStatisticalComparisonDto.jumpType == 3">
            INNER JOIN (
                <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.mainItem" />
            ) AS studentNum ON studentNum.id = msoi.id
            </if>
       <if test="bdStudentStatisticalComparisonDto.statisticalType == 1 or bdStudentStatisticalComparisonDto.statisticalType == 2 or bdStudentStatisticalComparisonDto.statisticalType == 3 or bdStudentStatisticalComparisonDto.statisticalType == 4 or bdStudentStatisticalComparisonDto.statisticalType == 6 or bdStudentStatisticalComparisonDto.statisticalType == 7 or bdStudentStatisticalComparisonDto.statisticalType == 8">
           <if test="bdStudentStatisticalComparisonDto.jumpType == 7">
           INNER JOIN (
               <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.oldTotalStudentNum" />
            ) AS oldTotalStudentNum ON oldTotalStudentNum.id = msoi.id
           </if>

           <if test="bdStudentStatisticalComparisonDto.jumpType == 8">
           INNER JOIN (
               <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.newTotalStudentNum" />
            ) AS newTotalStudentNum ON newTotalStudentNum.id = msoi.id
            </if>
       </if>

        <if test="bdStudentStatisticalComparisonDto.jumpType == 10">
        INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.firstStudentNum" />
        ) AS firstStudentNum ON firstStudentNum.id = msoi.id
        </if>

        <if test="bdStudentStatisticalComparisonDto.jumpType == 1">
        INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.oldFirstStudentNum" />
        ) AS oldFirstStudentNum ON oldFirstStudentNum.id = msoi.id
        </if>

        <if test="bdStudentStatisticalComparisonDto.jumpType == 2">
        INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.newFirstStudentNum" />
        ) AS newFirstStudentNum ON newFirstStudentNum.id = msoi.id
        </if>

        <if test="bdStudentStatisticalComparisonDto.jumpType == 4">
        INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.os" />
        ) AS os ON os.id = msoi.id
        </if>

        <if test="bdStudentStatisticalComparisonDto.jumpType == 24">
        INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.repeatOs" />
        ) AS repeatOs ON repeatOs.repeatOsStudentId = ms.id
        </if>

        <if test="bdStudentStatisticalComparisonDto.statisticalType == 8">

        <if test="bdStudentStatisticalComparisonDto.jumpType == 11">
        <!-- 未结算OS -->
        INNER JOIN (
            SELECT * FROM (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.os" />
            )AS unsettledCommissionActionOsNum2
            WHERE NOT EXISTS (
                SELECT 1 FROM
                m_staff_commission_action AS msca
                INNER JOIN ais_sale_center.m_student_offer_item AS msoi2 ON msoi2.id = msca.fk_student_offer_item_id
                INNER JOIN ais_sale_center.u_student_offer_item_step AS usois2 ON usois2.id = msoi2.fk_student_offer_item_step_id
                WHERE
                msca.fk_student_offer_item_id = unsettledCommissionActionOsNum2.id
                AND msoi2.fk_area_country_id = unsettledCommissionActionOsNum2.fk_area_country_id
                AND msca.status = 2
                AND msca.fk_staff_commission_step_key = 'STEP_OFFER_SELECTION'
                AND msca.fk_student_project_role_key = #{bdStudentStatisticalComparisonDto.projectRoleKey}
                AND (
                usois2.step_key != 'STEP_FAILURE'
                OR (
                usois2.step_key = 'STEP_FAILURE'
                AND EXISTS ( SELECT 1 FROM r_student_offer_item_step AS rsois4 WHERE rsois4.fk_student_offer_item_id = msoi2.id AND rsois4.fk_student_offer_item_step_id = 10 )
                        )
                    )
                )
        ) AS unsettledCommissionActionOsNum ON unsettledCommissionActionOsNum.id = msoi.id
        </if>

        <if test="bdStudentStatisticalComparisonDto.jumpType == 12">
        <!-- 未提交数 -->
        INNER JOIN (
            SELECT * FROM (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.mainItem" />
            )AS unsettledCommissionActionItemNum2
            WHERE NOT EXISTS (
                SELECT 1 FROM
                m_staff_commission_action AS msca
                WHERE
                msca.fk_student_offer_item_id = unsettledCommissionActionItemNum2.id
                AND msca.status = 2
                AND msca.fk_staff_commission_step_key = 'STEP_NEW_APP'
                AND msca.fk_student_project_role_key = #{bdStudentStatisticalComparisonDto.projectRoleKey}
            )
        ) AS unsettledCommissionActionItemNum ON unsettledCommissionActionItemNum.id = msoi.id
        </if>

        <if test="bdStudentStatisticalComparisonDto.jumpType == 13">
        <!-- 未结算签证 -->
        INNER JOIN (
            SELECT * FROM (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.visa" />
            )AS unsettledCommissionActionVisaNum2
            WHERE NOT EXISTS (
                SELECT 1 FROM
                m_staff_commission_action AS msca
                INNER JOIN ais_sale_center.m_student_offer_item AS msoi2 ON msoi2.id = msca.fk_student_offer_item_id
                INNER JOIN ais_sale_center.u_student_offer_item_step AS usois2 ON usois2.id = msoi2.fk_student_offer_item_step_id
                WHERE
                msca.fk_student_offer_item_id = unsettledCommissionActionVisaNum2.id
                AND msoi2.fk_area_country_id = unsettledCommissionActionVisaNum2.fk_area_country_id
                AND msca.status = 2
                AND msca.fk_staff_commission_step_key = 'STEP_VISA_GRANTED'
                AND msca.fk_student_project_role_key = #{bdStudentStatisticalComparisonDto.projectRoleKey}
            )
        ) AS unsettledCommissionActionVisaNum ON unsettledCommissionActionVisaNum.id = msoi.id
        </if>

        <if test="bdStudentStatisticalComparisonDto.jumpType == 14">
        <!-- 未结算成功 -->
        INNER JOIN (
            SELECT * FROM (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.enrolled" />
            )AS unsettledCommissionActionEnrolledNum2
            WHERE NOT EXISTS (
                SELECT 1 FROM
                m_staff_commission_action AS msca
                INNER JOIN ais_sale_center.m_student_offer_item AS msoi2 ON msoi2.id = msca.fk_student_offer_item_id
                INNER JOIN ais_sale_center.u_student_offer_item_step AS usois2 ON usois2.id = msoi2.fk_student_offer_item_step_id
                AND usois2.step_key != 'STEP_FAILURE'
                WHERE
                msca.fk_student_offer_item_id = unsettledCommissionActionEnrolledNum2.id
                AND msoi2.fk_area_country_id = unsettledCommissionActionEnrolledNum2.fk_area_country_id
                AND msca.status = 2
                AND msca.fk_staff_commission_step_key = 'STEP_ENROLLED'
                AND msca.fk_student_project_role_key = #{bdStudentStatisticalComparisonDto.projectRoleKey}
            )
        ) AS unsettledCommissionActionEnrolledNum ON unsettledCommissionActionEnrolledNum.id = msoi.id
        </if>

        <if test="bdStudentStatisticalComparisonDto.jumpType == 15">
        <!-- 未结算申请反馈数 -->
            INNER JOIN (
            SELECT * FROM (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.admitted" />
            )AS unsettledCommissionActionAdmittedNum2
            WHERE NOT EXISTS (
                SELECT 1 FROM
                m_staff_commission_action AS msca
                WHERE
                msca.fk_student_offer_item_id = unsettledCommissionActionAdmittedNum2.id
                AND msca.status = 2
                AND msca.fk_staff_commission_step_key = 'STEP_ADMITTED'
                AND msca.fk_student_project_role_key = #{bdStudentStatisticalComparisonDto.projectRoleKey}
            )
        ) AS unsettledCommissionActionAdmittedNum ON unsettledCommissionActionAdmittedNum.id = msoi.id
        </if>

        <if test="bdStudentStatisticalComparisonDto.jumpType == 16 or bdStudentStatisticalComparisonDto.jumpType == 17 or bdStudentStatisticalComparisonDto.jumpType == 18 or bdStudentStatisticalComparisonDto.jumpType == 19 or bdStudentStatisticalComparisonDto.jumpType == 20 or bdStudentStatisticalComparisonDto.jumpType == 22 or bdStudentStatisticalComparisonDto.jumpType == 28 or bdStudentStatisticalComparisonDto.jumpType == 30">
            INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.settlementCompleted"/>
            ) AS commissionActionNum ON commissionActionNum.id = msoi.id
        </if>

        </if>

        <if test="bdStudentStatisticalComparisonDto.statisticalType == 6 or bdStudentStatisticalComparisonDto.statisticalType == 7 or bdStudentStatisticalComparisonDto.statisticalType == 8">
            INNER JOIN ais_sale_center.m_student_offer AS mso ON mso.id = msoi.fk_student_offer_id
            LEFT JOIN ais_sale_center.s_student_project_role_staff AS ssprs ON ssprs.fk_table_id = mso.id
            AND ssprs.fk_table_name = 'm_student_offer' AND ssprs.is_active = 1
            LEFT JOIN ais_sale_center.u_student_project_role AS uspr ON uspr.id = ssprs.fk_student_project_role_id
            LEFT JOIN ais_permission_center.m_staff AS rms ON rms.id = ssprs.fk_staff_id
            INNER JOIN ais_permission_center.m_staff AS bdms ON bdms.id = mso.fk_staff_id

            <if test="bdStudentStatisticalComparisonDto.jumpType == 5">
            INNER JOIN (
                <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.visa" />
            ) AS visa ON visa.id = msoi.id
            </if>

            <if test="bdStudentStatisticalComparisonDto.jumpType == 6">
            INNER JOIN (
                <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.enrolled" />
            ) AS enrolled ON enrolled.id = msoi.id
            </if>
            <if test="bdStudentStatisticalComparisonDto.jumpType == 9">
        INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.admitted" />
        ) AS admitted ON admitted.id = msoi.id
        </if>
        </if>

        <if test="bdStudentStatisticalComparisonDto.jumpType == 21">
        <!-- 未结算后补签证数 -->
        INNER JOIN (
            SELECT * FROM (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.enrolledVisa" />
            )AS unsettledCommissionActionEnrolledVisaNum2
            WHERE NOT EXISTS (
            SELECT 1 FROM
            m_staff_commission_action AS msca
            WHERE
            msca.fk_student_offer_item_id = unsettledCommissionActionEnrolledVisaNum2.id
            AND msca.status = 2
            AND msca.fk_staff_commission_step_key = 'STEP_VISA_GRANTED_BACK'
            AND msca.fk_student_project_role_key = #{bdStudentStatisticalComparisonDto.projectRoleKey}
            )
        ) AS unsettledCommissionActionEnrolledVisaNum ON unsettledCommissionActionEnrolledVisaNum.id = msoi.id
        </if>

        <if test="bdStudentStatisticalComparisonDto.jumpType == 23">
            <!-- 后补签证数 -->
            INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.enrolledVisa" />
            ) AS enrolledVisa ON enrolledVisa.id = msoi.id
        </if>

        <if test="bdStudentStatisticalComparisonDto.jumpType == 25">
            <!-- 延迟提交数 -->
            INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.mainItemDelay" />
            ) AS studentNumDelay ON studentNumDelay.id = msoi.id
        </if>
        <if test="bdStudentStatisticalComparisonDto.jumpType == 26">
            <!-- 延迟申请反馈数 -->
            INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.admittedDelay" />
            ) AS admittedDelay ON admittedDelay.id = msoi.id
        </if>
        <if test="bdStudentStatisticalComparisonDto.jumpType == 27">
        <!-- 未结算延迟提交数 -->
        INNER JOIN (
            SELECT * FROM (
                <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.mainItemDelay" />
            )AS unsettledCommissionActionItemNum2
            WHERE NOT EXISTS (
                SELECT 1 FROM
                m_staff_commission_action AS msca
                WHERE
                msca.fk_student_offer_item_id = unsettledCommissionActionItemNum2.id
                AND msca.status = 2
                AND msca.fk_staff_commission_step_key = 'STEP_SUBMITTED_DELAY'
                AND msca.fk_student_project_role_key = #{bdStudentStatisticalComparisonDto.projectRoleKey}
            )
        ) AS unsettledCommissionActionDelayItemNum ON unsettledCommissionActionDelayItemNum.id = msoi.id
        </if>
        <if test="bdStudentStatisticalComparisonDto.jumpType == 29">
        <!-- 未结算延迟申请反馈数 -->
        INNER JOIN (
            SELECT * FROM (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.admittedDelay" />
            )AS unsettledCommissionActionAdmittedNum2
            WHERE NOT EXISTS (
            SELECT 1 FROM
            m_staff_commission_action AS msca
            WHERE
            msca.fk_student_offer_item_id = unsettledCommissionActionAdmittedNum2.id
            AND msca.status = 2
            AND msca.fk_staff_commission_step_key = 'STEP_ADMITTED_DELAY'
            AND msca.fk_student_project_role_key = #{bdStudentStatisticalComparisonDto.projectRoleKey}
            )
            ) AS unsettledCommissionActionAdmittedDelayNum ON unsettledCommissionActionAdmittedDelayNum.id = msoi.id
        </if>
        <if test="bdStudentStatisticalComparisonDto.jumpType == 31">
            INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.appOptNum" />
            ) AS appOptNum ON appOptNum.id = msoi.id
        </if>
        <!-- 在这个时间段内有过步骤变更的 都算有数据 -->
        INNER JOIN (
        <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.updateStepSql" />
        AND (
            ( DATE_FORMAT( rsois.gmt_create, '%Y-%m-%d' ) <![CDATA[>=]]> DATE_FORMAT(#{bdStudentStatisticalComparisonDto.studentBeginTime}, '%Y-%m-%d' )
            AND DATE_FORMAT( rsois.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{bdStudentStatisticalComparisonDto.studentEndTime}, '%Y-%m-%d' ) )
            OR
            ( DATE_FORMAT( msoi2.new_app_opt_time, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{bdStudentStatisticalComparisonDto.studentBeginTime}, '%Y-%m-%d' )
            AND DATE_FORMAT( msoi2.new_app_opt_time, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{bdStudentStatisticalComparisonDto.studentEndTime}, '%Y-%m-%d' ) )
        )
            GROUP BY
            msoi2.id
        ) AS a ON a.id = msoi.id

        <if test="bdStudentStatisticalComparisonDto.statisticalType == 7">
            INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.itemPermissionSql"/>
            ) z ON z.id = msoi.id
        </if>
            WHERE
            ms.fk_company_id IN
         <foreach collection="bdStudentStatisticalComparisonDto.fkCompanyIds" item="fkCompanyId" open="(" separator="," close=")">
            #{fkCompanyId}
        </foreach>
            AND msoi.status = 1
            AND msoi.is_follow = 0
            AND rsbc.bd_code NOT LIKE 'T%'
            <if test="bdStudentStatisticalComparisonDto.statisticalType != 6 and bdStudentStatisticalComparisonDto.statisticalType != 7  and bdStudentStatisticalComparisonDto.statisticalType != 8">
                <if test="bdStudentStatisticalComparisonDto.fkStaffIds != null and bdStudentStatisticalComparisonDto.fkStaffIds.size()>0">
                    AND msoi.fk_staff_id IN
                    <foreach collection="bdStudentStatisticalComparisonDto.fkStaffIds" item="fkStaffId" open="(" separator="," close=")">
                        #{fkStaffId}
                    </foreach>
                </if>
            </if>
            <if test="bdStudentStatisticalComparisonDto.statisticalType == 6 or bdStudentStatisticalComparisonDto.statisticalType == 8">
                AND uspr.role_key LIKE #{bdStudentStatisticalComparisonDto.projectRoleKey}
                <if test="bdStudentStatisticalComparisonDto.fkStaffIds != null and bdStudentStatisticalComparisonDto.fkStaffIds.size()>0">
                    AND rms.id IN
                    <foreach collection="bdStudentStatisticalComparisonDto.fkStaffIds" item="fkStaffId" open="(" separator="," close=")">
                        #{fkStaffId}
                    </foreach>
                </if>
            </if>

            <!-- 业务国家 -->
            <if test="bdStudentStatisticalComparisonDto.areaCountryIds != null and bdStudentStatisticalComparisonDto.areaCountryIds.size()>0">
                AND msoi.fk_area_country_id IN
                <foreach collection="bdStudentStatisticalComparisonDto.areaCountryIds" item="areaCountryId" open="(" separator="," close=")">
                    #{areaCountryId}
                </foreach>
            </if>
            <!-- 业务学校 -->
<!--            <if test="institutionIds !=null and institutionIds.size() > 0">-->
<!--                AND msoi.fk_institution_id IN-->
<!--                <foreach collection="institutionIds" item="institutionId" open="(" separator="," close=")">-->
<!--                    #{institutionId}-->
<!--                </foreach>-->
<!--            </if>-->

            <!-- 代理所在区域 -->
            <if test="bdStudentStatisticalComparisonDto.fkAreaCountryId != null">
                AND ma.fk_area_country_id = #{bdStudentStatisticalComparisonDto.fkAreaCountryId}
            </if>
            <if test="bdStudentStatisticalComparisonDto.fkAreaStateIdList != null and bdStudentStatisticalComparisonDto.fkAreaStateIdList.size()>0">
                AND ma.fk_area_state_id IN
                <foreach collection="bdStudentStatisticalComparisonDto.fkAreaStateIdList" item="fkAreaStateId" open="(" separator="," close=")">
                    #{fkAreaStateId}
                </foreach>
            </if>
            <if test="bdStudentStatisticalComparisonDto.fkAreaCityIdList != null and bdStudentStatisticalComparisonDto.fkAreaCityIdList.size()>0">
                AND ma.fk_area_city_id IN
                <foreach collection="bdStudentStatisticalComparisonDto.fkAreaCityIdList" item="fkAreaCityId" open="(" separator="," close=")">
                    #{fkAreaCityId}
                </foreach>
            </if>
            <if test="bdStudentStatisticalComparisonDto.agentNumStr != null and bdStudentStatisticalComparisonDto.agentNumStr != '' ">
                AND FIND_IN_SET (ma.num, #{bdStudentStatisticalComparisonDto.agentNumStr}) > 0
            </if>
            <!-- 大区 -->
            <if test="bdStudentStatisticalComparisonDto.fkAreaRegionIdLIst != null and bdStudentStatisticalComparisonDto.fkAreaRegionIdLIst.size()>0">
                AND rsbc.fk_area_region_id IN
                <foreach collection="bdStudentStatisticalComparisonDto.fkAreaRegionIdLIst" item="fkAreaRegionId" open="(" separator="," close=")">
                    #{fkAreaRegionId}
                </foreach>
            </if>
            <!--过滤：BD中英名字及编号-->
            <if test="bdStudentStatisticalComparisonDto.bdNameOrCode != null and bdStudentStatisticalComparisonDto.bdNameOrCode != ''">
                AND (
                <if test ="bdStudentStatisticalComparisonDto.fkBdIds!=null and bdStudentStatisticalComparisonDto.fkBdIds.size()>0" >
                    bdms.id IN
                    <foreach collection="bdStudentStatisticalComparisonDto.fkBdIds" item="fkBdId" open="(" separator="," close=")">
                        #{fkBdId}
                    </foreach>
                    OR
                </if>
                LOWER(bdms.name) LIKE CONCAT('%',LOWER(#{bdStudentStatisticalComparisonDto.bdNameOrCode}),'%')
                OR LOWER(bdms.name_en) LIKE CONCAT('%',LOWER(#{bdStudentStatisticalComparisonDto.bdNameOrCode}),'%')
                OR LOWER(rsbc.bd_code) LIKE CONCAT('%',LOWER(#{bdStudentStatisticalComparisonDto.bdNameOrCode}),'%')
                )
            </if>
            <!--过滤：角色成员-->
            <if test="bdStudentStatisticalComparisonDto.projectStaffNameOrEnName != null and bdStudentStatisticalComparisonDto.projectStaffNameOrEnName != ''">
                AND (
                <if test="bdStudentStatisticalComparisonDto.projectStaffIds!=null and bdStudentStatisticalComparisonDto.projectStaffIds.size()>0">
                    rms.id IN
                    <foreach collection="bdStudentStatisticalComparisonDto.projectStaffIds" item="projectStaffId"
                             open="(" separator="," close=")">
                        #{projectStaffId}
                    </foreach>
                    OR
                </if>
                LOWER(rms.name) LIKE CONCAT('%',LOWER(#{bdStudentStatisticalComparisonDto.projectStaffNameOrEnName}),'%')
                OR LOWER(rms.name_en) LIKE CONCAT('%',LOWER(#{bdStudentStatisticalComparisonDto.projectStaffNameOrEnName}))
                )
            </if>
            <!-- bd跳转 -->
            <if test="bdStudentStatisticalComparisonDto.jumpBdId != null and bdStudentStatisticalComparisonDto.jumpBdId != ''">
                AND msoi.fk_staff_id = #{bdStudentStatisticalComparisonDto.jumpBdId}
            </if>
            <!-- 区域跳转 -->
            <if test="bdStudentStatisticalComparisonDto.jumpCityId != null and bdStudentStatisticalComparisonDto.jumpCityId != ''">
                AND ma.fk_area_city_id = #{bdStudentStatisticalComparisonDto.jumpCityId}
            </if>
            <!-- 大区跳转 -->
            <if test="bdStudentStatisticalComparisonDto.jumpAreaRegionId != null and bdStudentStatisticalComparisonDto.jumpAreaRegionId != ''">
                AND rsbc.fk_area_region_id = #{bdStudentStatisticalComparisonDto.jumpAreaRegionId}
            </if>
            <!-- 代理跳转 -->
            <if test="bdStudentStatisticalComparisonDto.jumpAgentId != null and bdStudentStatisticalComparisonDto.jumpAgentId != ''">
                AND ma.id = #{bdStudentStatisticalComparisonDto.jumpAgentId}
            </if>
            <!-- 项目成员跳转 -->
            <if test="bdStudentStatisticalComparisonDto.projectRoleKey != null and bdStudentStatisticalComparisonDto.projectRoleKey != ''">
                AND ssprs.fk_student_project_role_id IN (
                    SELECT id FROM ais_sale_center.u_student_project_role WHERE role_key LIKE #{bdStudentStatisticalComparisonDto.projectRoleKey}
                )
            </if>
            <if test="bdStudentStatisticalComparisonDto.jumpRoleStaffId != null and bdStudentStatisticalComparisonDto.jumpRoleStaffId != ''">
                AND ssprs.fk_staff_id = #{bdStudentStatisticalComparisonDto.jumpRoleStaffId}
            </if>
            <!-- 跳转国家 -->
            <if test="bdStudentStatisticalComparisonDto.jumpCountryId != null and bdStudentStatisticalComparisonDto.jumpCountryId != ''">
                AND msoi.fk_area_country_id = #{bdStudentStatisticalComparisonDto.jumpCountryId}
            </if>

             GROUP BY
            <choose>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 1">
                    oldFirstStudentNum.fk_student_id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 2">
                    newFirstStudentNum.fk_student_id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 3">
                    studentNum.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 4">
                    os.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 5">
                    visa.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 6">
                    enrolled.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 7">
                    oldTotalStudentNum.fk_student_id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 8">
                    newTotalStudentNum.fk_student_id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 9">
                    admitted.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 10">
                    firstStudentNum.fk_student_id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 11">
                    unsettledCommissionActionOsNum.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 12">
                    unsettledCommissionActionItemNum.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 13">
                    unsettledCommissionActionVisaNum.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 14">
                    unsettledCommissionActionEnrolledNum.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 15">
                    unsettledCommissionActionAdmittedNum.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 16">
                    commissionActionNum.commissionActionOsItemId
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 17">
                    commissionActionNum.commissionActionItemId
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 18">
                    commissionActionNum.commissionActionVisaItemId
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 19">
                    commissionActionNum.commissionActionEnrolledItemId
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 20">
                    commissionActionNum.commissionAdmittedItemId
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 21">
                    unsettledCommissionActionEnrolledVisaNum.fk_student_id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 22">
                    commissionActionNum.commissionActionEnrolledVisaStudentId
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 23">
                    enrolledVisa.fk_student_id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 24">
                    repeatOs.repeatOsStudentId
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 25">
                    studentNumDelay.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 26">
                    admittedDelay.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 27">
                    unsettledCommissionActionDelayItemNum.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 28">
                    commissionActionDelayItemId.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 29">
                    unsettledCommissionActionAdmittedDelayNum.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 30">
                    commissionAdmittedDelayItemId.id
                </when>
                <when test="bdStudentStatisticalComparisonDto.jumpType == 31">
                    appOptNum.fk_student_id
                </when>
            </choose>
    </sql>

    <!-- 代理权限 -->
    <sql id="agentPermissionSql">
        SELECT DISTINCT a.id FROM (
        <!-- 父子代理的BD权限，儿子下面的学生，父亲的BD，第一层的权限 -->
        SELECT c.id FROM ais_sale_center.m_agent c
        INNER JOIN ais_sale_center.r_agent_staff d ON c.fk_parent_agent_id=d.fk_agent_id AND d.is_active=1 AND d.fk_staff_id IN
        <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
            #{staffFollowerId}
        </foreach>
        UNION ALL
        <!-- 父子代理的BD权限，儿子下面的学生，父亲的BD，第二层的权限 -->
        SELECT c.id FROM ais_sale_center.m_agent c
        INNER JOIN ais_sale_center.m_agent d ON c.fk_parent_agent_id=d.id AND d.fk_parent_agent_id IS NOT NULL
        INNER JOIN ais_sale_center.r_agent_staff e ON d.fk_parent_agent_id=e.fk_agent_id AND e.is_active=1 AND e.fk_staff_id IN
        <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
            #{staffFollowerId}
        </foreach>
        UNION ALL
        <!-- BD的权限 -->
        SELECT c.fk_agent_id FROM ais_sale_center.r_agent_staff c WHERE c.is_active = 1 AND c.fk_staff_id IN
        <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
            #{staffFollowerId}
        </foreach>
        UNION ALL
        <!-- #创建人的权限 -->
        SELECT c.id FROM ais_sale_center.m_agent c
        INNER JOIN ais_permission_center.m_staff b ON c.gmt_create_user=b.login_id AND b.id IN
        <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
            #{staffFollowerId}
        </foreach>
        ) a
    </sql>

    <!-- 学习计划权限 -->
    <sql id="itemPermissionSql">
        SELECT a.id FROM (
        <choose>
            <when test="isStudentAdmin">
                SELECT a.id FROM ais_sale_center.m_student_offer_item a
            </when>
            <when test="!isStudentAdmin and permissionGroupInstitutionIds != null and permissionGroupInstitutionIds.size()>0">
                <!-- 学校权限组 -->
                SELECT a.id FROM ais_sale_center.m_student_offer_item a
                <where>
                    <if test="permissionGroupInstitutionIds != null and permissionGroupInstitutionIds.size()>0">
                        AND a.fk_institution_id IN
                        <foreach collection="permissionGroupInstitutionIds" item="institutionIds" index="index" open="(" separator="," close=")">
                            #{institutionIds}
                        </foreach>
                    </if>
                    <if test="staffBoundBdIds != null and staffBoundBdIds.size()>0">
                        AND a.fk_staff_id IN
                        <foreach collection="staffBoundBdIds" item="staffId" index="index" open="(" separator="," close=")">
                            #{staffId}
                        </foreach>
                    </if>
                </where>
                GROUP BY a.id
            </when>
            <otherwise>
                <!--项目成员的权限-->
                SELECT a.id FROM ais_sale_center.m_student_offer_item a
                INNER JOIN ais_sale_center.s_student_project_role_staff b ON a.fk_student_offer_id=b.fk_table_id AND b.fk_table_name='m_student_offer'
                AND b.is_active=1
                AND b.fk_staff_id IN
                <foreach collection="staffFollowerIds" item="staffId" index="index" open="(" separator="," close=")">
                    #{staffId}
                </foreach>
                <where>
                    <if test="staffBoundBdIds != null and staffBoundBdIds.size()>0">
                        AND a.fk_staff_id IN
                        <foreach collection="staffBoundBdIds" item="staffId" index="index" open="(" separator="," close=")">
                            #{staffId}
                        </foreach>
                    </if>
                </where>
                GROUP BY a.id

                UNION ALL
                <!--申请计划对应的申请方案创建人的权限-->
                SELECT a.id FROM ais_sale_center.m_student_offer_item a
                INNER JOIN ais_sale_center.m_student_offer b ON a.fk_student_offer_id=b.id
                INNER JOIN ais_permission_center.m_staff c ON b.gmt_create_user=c.login_id AND c.id IN
                <foreach collection="staffFollowerIds" item="staffId" index="index" open="(" separator="," close=")">
                    #{staffId}
                </foreach>
                GROUP BY a.id

                UNION ALL
                <!--申请计划对应的BD的权限-->
                SELECT a.id FROM ais_sale_center.m_student_offer_item a WHERE a.fk_staff_id IN
                <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
                    #{staffFollowerId}
                </foreach>
                <if test="staffBoundBdIds != null and staffBoundBdIds.size()>0">
                    AND a.fk_staff_id IN
                    <foreach collection="staffBoundBdIds" item="staffId" index="index" open="(" separator="," close=")">
                        #{staffId}
                    </foreach>
                </if>
                </otherwise>
            </choose>
            ) a
            <if test="!isStudentOfferItemFinancialHiding">
                INNER JOIN ais_sale_center.m_student_offer_item b ON a.id=b.id
                WHERE IFNULL(b.is_follow_hidden, 0) != 1
            </if>
            GROUP BY a.id
        </sql>

        <!-- 学生权限 -->
    <sql id="studentAuthority">
        SELECT a.id FROM (
        <!-- BD的权限 -->
        SELECT a.id FROM ais_sale_center.m_student a
        INNER JOIN ais_sale_center.r_student_agent b ON a.id=b.fk_student_id AND b.is_active = 1
        INNER JOIN ais_sale_center.r_agent_staff c ON b.fk_agent_id=c.fk_agent_id AND c.is_active = 1
        <where>
            c.fk_staff_id IN
            <foreach collection="staffFollowerIds" item="staffId" index="index" open="(" separator="," close=")">
                #{staffId}
            </foreach>
            <if test="staffBoundBdIds != null and staffBoundBdIds.size()>0">
                AND c.fk_staff_id IN
                <foreach collection="staffBoundBdIds" item="staffId" index="index" open="(" separator="," close=")">
                    #{staffId}
                </foreach>
            </if>
        </where>
        GROUP BY a.id

        <if test="permissionGroupInstitutionIds == null or permissionGroupInstitutionIds.isEmpty()">
            UNION ALL
            <!-- 项目成员的权限(留学申请) -->
            SELECT a.id FROM ais_sale_center.m_student a
            INNER JOIN ais_sale_center.m_student_offer b ON a.id=b.fk_student_id
            INNER JOIN ais_sale_center.s_student_project_role_staff c ON b.id=c.fk_table_id AND c.fk_table_name='m_student_offer' AND c.is_active=1
            <where>
                c.fk_staff_id IN
                <foreach collection="staffFollowerIds" item="staffId" index="index" open="(" separator="," close=")">
                    #{staffId}
                </foreach>
                <if test="staffBoundBdIds != null and staffBoundBdIds.size()>0">
                    AND b.fk_staff_id IN
                    <foreach collection="staffBoundBdIds" item="staffId" index="index" open="(" separator="," close=")">
                        #{staffId}
                    </foreach>
                </if>
            </where>
            GROUP BY a.id
        </if>

        <if test="permissionGroupInstitutionIds != null and permissionGroupInstitutionIds.size()>0">
            UNION ALL
            <!-- 学校权限组 -->
            SELECT a.id FROM ais_sale_center.m_student a
            INNER JOIN ais_sale_center.m_student_offer b ON a.id=b.fk_student_id
            INNER JOIN ais_sale_center.m_student_offer_item c ON b.id=c.fk_student_offer_id
            <where>
                <if test="permissionGroupInstitutionIds != null and permissionGroupInstitutionIds.size()>0">
                    AND c.fk_institution_id IN
                    <foreach collection="permissionGroupInstitutionIds" item="institutionIds" index="index" open="(" separator="," close=")">
                        #{institutionIds}
                    </foreach>
                </if>
                <if test="staffBoundBdIds != null and staffBoundBdIds.size()>0">
                    AND b.fk_staff_id IN
                    <foreach collection="staffBoundBdIds" item="staffId" index="index" open="(" separator="," close=")">
                        #{staffId}
                    </foreach>
                </if>
            </where>
            GROUP BY a.id
        </if>

        UNION ALL
        <!-- 项目成员的权限(留学保险) -->
        SELECT a.id FROM ais_sale_center.m_student a
        INNER JOIN ais_sale_center.m_student_insurance b ON a.id=b.fk_student_id
        INNER JOIN ais_sale_center.s_student_project_role_staff c ON b.id=c.fk_table_id AND c.fk_table_name='m_student_insurance' AND c.is_active=1
        <where>
            c.fk_staff_id IN
            <foreach collection="staffFollowerIds" item="staffId" index="index" open="(" separator="," close=")">
                #{staffId}
            </foreach>
            <if test="staffBoundBdIds != null and staffBoundBdIds.size()>0">
                AND b.fk_staff_id IN
                <foreach collection="staffBoundBdIds" item="staffId" index="index" open="(" separator="," close=")">
                    #{staffId}
                </foreach>
            </if>
        </where>

        GROUP BY a.id

        UNION ALL
        <!-- 项目成员的权限(留学住宿) -->
        SELECT a.id FROM ais_sale_center.m_student a
        INNER JOIN ais_sale_center.m_student_accommodation b ON a.id=b.fk_student_id
        INNER JOIN ais_sale_center.s_student_project_role_staff c ON b.id=c.fk_table_id AND c.fk_table_name='m_student_accommodation' AND c.is_active=1
        <where>
            c.fk_staff_id IN
            <foreach collection="staffFollowerIds" item="staffId" index="index" open="(" separator="," close=")">
                #{staffId}
            </foreach>
            <if test="staffBoundBdIds != null and staffBoundBdIds.size()>0">
                AND b.fk_staff_id IN
                <foreach collection="staffBoundBdIds" item="staffId" index="index" open="(" separator="," close=")">
                    #{staffId}
                </foreach>
            </if>
        </where>
        GROUP BY a.id

        UNION ALL
        <!-- 项目成员的权限(留学服务费) -->
        SELECT a.id FROM ais_sale_center.m_student a
        INNER JOIN ais_sale_center.m_student_service_fee b ON a.id=b.fk_student_id
        INNER JOIN ais_sale_center.s_student_project_role_staff c ON b.id=c.fk_table_id AND c.fk_table_name='m_student_service_fee' AND c.is_active=1
         <where>
             c.fk_staff_id IN
             <foreach collection="staffFollowerIds" item="staffId" index="index" open="(" separator="," close=")">
                 #{staffId}
             </foreach>
             <if test="staffBoundBdIds != null and staffBoundBdIds.size()>0">
                 AND b.fk_staff_id IN
                 <foreach collection="staffBoundBdIds" item="staffId" index="index" open="(" separator="," close=")">
                     #{staffId}
                 </foreach>
             </if>
         </where>
        GROUP BY a.id

        UNION ALL
        <!-- 学生创建人的权限 -->
        SELECT a.id FROM ais_sale_center.m_student a
        INNER JOIN ais_permission_center.m_staff b ON a.gmt_create_user=b.login_id
        AND b.id IN
            <foreach collection="staffFollowerIds" item="staffId" index="index" open="(" separator="," close=")">
                #{staffId}
            </foreach>
        GROUP BY a.id

        UNION ALL
        <!-- 留学方案创建人的权限 -->
        SELECT a.id FROM ais_sale_center.m_student a
        INNER JOIN ais_sale_center.m_student_offer b ON a.id=b.fk_student_id
        INNER JOIN ais_permission_center.m_staff c ON b.gmt_create_user=c.login_id
        AND c.id IN
            <foreach collection="staffFollowerIds" item="staffId" index="index" open="(" separator="," close=")">
                #{staffId}
            </foreach>
        GROUP BY a.id

        UNION ALL
        <!-- 留学保险创建人的权限 -->
        SELECT a.id FROM ais_sale_center.m_student a
        INNER JOIN ais_sale_center.m_student_insurance b ON a.id=b.fk_student_id
        INNER JOIN ais_permission_center.m_staff c ON b.gmt_create_user=c.login_id
        AND c.id IN
            <foreach collection="staffFollowerIds" item="staffId" index="index" open="(" separator="," close=")">
                #{staffId}
            </foreach>
        GROUP BY a.id

        UNION ALL
        <!-- 留学小屋创建人的权限 -->
        SELECT a.id FROM ais_sale_center.m_student a
        INNER JOIN ais_sale_center.m_student_accommodation b ON a.id=b.fk_student_id
        INNER JOIN ais_permission_center.m_staff c ON b.gmt_create_user=c.login_id
        AND c.id IN
            <foreach collection="staffFollowerIds" item="staffId" index="index" open="(" separator="," close=")">
                #{staffId}
            </foreach>
        GROUP BY a.id
        UNION ALL
        <!-- 服务费创建人的权限 -->
        SELECT a.id FROM ais_sale_center.m_student a
        INNER JOIN ais_sale_center.m_student_service_fee b ON a.id=b.fk_student_id
        INNER JOIN ais_permission_center.m_staff c ON b.gmt_create_user=c.login_id
        AND c.id IN
            <foreach collection="staffFollowerIds" item="staffId" index="index" open="(" separator="," close=")">
                #{staffId}
            </foreach>
        GROUP BY a.id
        UNION ALL
        SELECT a.fk_student_id FROM ais_sale_center.m_student_offer_item a WHERE a.fk_staff_id IN
        <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
            #{staffFollowerId}
        </foreach>
        ) a
        GROUP BY a.id
    </sql>

    <!-- 申请方案计划权限 -->
    <sql id="offerPermissionSql">
        SELECT DISTINCT a.id FROM (
        <choose>
            <when test="isStudentAdmin">
                SELECT a.id FROM ais_sale_center.m_student_offer a
            </when>
            <when test="!isStudentAdmin and permissionGroupInstitutionIds != null and permissionGroupInstitutionIds.size()>0">
                <!-- 学校权限组 -->
                SELECT a.id FROM ais_sale_center.m_student_offer a
                INNER JOIN ais_sale_center.m_student_offer_item b on b.fk_student_offer_id = a.id
                <where>
                    <if test="permissionGroupInstitutionIds != null and permissionGroupInstitutionIds.size()>0">
                        AND b.fk_institution_id IN
                        <foreach collection="permissionGroupInstitutionIds" item="institutionIds" index="index" open="(" separator="," close=")">
                            #{institutionIds}
                        </foreach>
                    </if>
                    <if test="staffBoundBdIds != null and staffBoundBdIds.size()>0">
                        AND a.fk_staff_id IN
                        <foreach collection="staffBoundBdIds" item="staffId" index="index" open="(" separator="," close=")">
                            #{staffId}
                        </foreach>
                    </if>
                    UNION ALL
                    <!-- 申请方案创建人的权限 -->
                    SELECT a.id FROM ais_sale_center.m_student_offer a
                    INNER JOIN ais_permission_center.m_staff b ON a.gmt_create_user=b.login_id AND b.id IN
                    <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
                        #{staffFollowerId}
                    </foreach>
                </where>
                GROUP BY a.id
            </when>
            <otherwise>
                <!-- 项目成员的权限 -->
                SELECT a.id FROM ais_sale_center.m_student_offer a
                INNER JOIN ais_sale_center.s_student_project_role_staff b ON a.id=b.fk_table_id AND b.fk_table_name='m_student_offer' AND b.is_active=1 AND b.fk_staff_id IN
                <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
                    #{staffFollowerId}
                </foreach>
                <where>
                    <if test="staffBoundBdIds != null and staffBoundBdIds.size()>0">
                        AND a.fk_staff_id IN
                        <foreach collection="staffBoundBdIds" item="staffId" index="index" open="(" separator="," close=")">
                            #{staffId}
                        </foreach>
                    </if>
                </where>
                UNION ALL
                <!-- 申请方案创建人的权限 -->
                SELECT a.id FROM ais_sale_center.m_student_offer a
                INNER JOIN ais_permission_center.m_staff b ON a.gmt_create_user=b.login_id AND b.id IN
                <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
                    #{staffFollowerId}
                </foreach>
                UNION ALL
                <!-- 申请方案BD权限 -->
                SELECT a.id FROM ais_sale_center.m_student_offer a
                INNER JOIN ais_permission_center.m_staff b ON a.fk_staff_id=b.id AND b.id IN
                <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
                    #{staffFollowerId}
                </foreach>
                <if test="staffBoundBdIds != null and staffBoundBdIds.size()>0">
                    AND a.fk_staff_id IN
                    <foreach collection="staffBoundBdIds" item="staffId" index="index" open="(" separator="," close=")">
                        #{staffId}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        ) a
    </sql>

    <!-- 合同联系人权限 -->
    <sql id="contactPersonPermissionSql">
        SELECT DISTINCT a.id FROM (
        -- #创建人的权限
        SELECT a.id FROM ais_sale_center.s_contact_person a
        INNER JOIN ais_permission_center.m_staff b ON a.gmt_create_user=b.login_id AND b.id IN
        <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
            #{staffFollowerId}
        </foreach>
        -- #BD的权限
        UNION ALL
        SELECT a.id FROM ais_sale_center.s_contact_person a
        INNER JOIN ais_sale_center.m_agent b ON a.fk_table_id=b.id AND a.fk_table_name='m_agent'
        INNER JOIN ais_sale_center.r_agent_staff c ON b.id=c.fk_agent_id AND c.is_active = 1 AND c.fk_staff_id IN
        <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
            #{staffFollowerId}
        </foreach>
        UNION ALL
        SELECT a.id FROM ais_sale_center.s_contact_person a
        INNER JOIN ais_sale_center.m_agent_contract b ON a.fk_table_id=b.id AND a.fk_table_name='m_agent'
        INNER JOIN ais_sale_center.m_agent c on c.id = b.fk_agent_id
        INNER JOIN ais_sale_center.r_agent_staff d ON c.id=d.fk_agent_id AND d.is_active = 1 AND d.fk_staff_id IN
        <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
            #{staffFollowerId}
        </foreach>
        ) a
    </sql>

    <!-- 更新步骤sql -->
    <sql id="updateStepSql">
        <!-- 在这个时间段内有过步骤变更的 都算有数据 -->
            SELECT
            msoi2.id
            FROM
            ais_sale_center.m_student_offer_item AS msoi2
            INNER JOIN ais_sale_center.r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = msoi2.id
            INNER JOIN ais_sale_center.u_student_offer_item_step AS usois ON usois.id = rsois.fk_student_offer_item_step_id
            INNER JOIN ais_sale_center.m_student AS ms2 ON ms2.id = msoi2.fk_student_id
            WHERE
            ms2.fk_company_id IN
         <foreach collection="bdStudentStatisticalComparisonDto.fkCompanyIds" item="fkCompanyId" open="(" separator="," close=")">
            #{fkCompanyId}
        </foreach>
            AND msoi2.status = 1
            AND (
                usois.step_key IN ("STEP_NEW_APP", "STEP_OFFER_SELECTION", "STEP_VISA_SUBMITTED", "STEP_VISA_GRANTED",
                "STEP_ENROLLED", "STEP_SUBMITTED","STEP_NOTIFIED","STEP_APP_RECEIVED", "STEP_FAILURE",
                "STEP_ADMITTED")
            OR (
                msoi2.new_app_status = 0
                )
            )
    </sql>
</mapper>