package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.salecenter.vo.AgentContractAccountVo;
import com.get.salecenter.entity.AgentContractAgentAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AgentContractAgentAccountMapper extends BaseMapper<AgentContractAgentAccount> {
    int insert(AgentContractAgentAccount record);

    int insertSelective(AgentContractAgentAccount record);

    int updateById(AgentContractAgentAccount record);

    int updateByPrimaryKey(AgentContractAgentAccount record);

    /**
     * 合同账户列表数据
     *
     * @Date 16:15 2021/6/28
     * <AUTHOR>
     */
    List<AgentContractAccountVo> selectAgentContractAgentAccount(IPage<AgentContractAccountVo> iPage, @Param("fkAgentContractId") Long fkAgentContractId);
}