package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * author:Neil
 * Time: 11:40
 * Date: 2022/8/19
 * Description:
 */
@Data
@TableName("m_client_contact_person")
public class ClientContactPerson extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 客户Id
     */
    @ApiModelProperty(value = "客户Id")
    @Column(name = "fk_client_id")
    private Long fkClientId;
    /**
     * 与本人关系：父母/兄弟/亲戚/朋友
     */
    @ApiModelProperty(value = "与本人关系：父母/兄弟/亲戚/朋友")
    @Column(name = "relationship")
    private String relationship;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Column(name = "name")
    private String name;
    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "gender")
    private String gender;
    /**
     * 职业
     */
    @ApiModelProperty(value = "职业")
    @Column(name = "job")
    private String job;
    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    @Column(name = "mobile")
    private String mobile;
    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    @Column(name = "tel")
    private String tel;
    /**
     * Email
     */
    @ApiModelProperty(value = "Email")
    @Column(name = "email")
    private String email;
    /**
     * qq
     */
    @ApiModelProperty(value = "qq")
    @Column(name = "qq")
    private String qq;
    /**
     * 微信号
     */
    @ApiModelProperty(value = "微信号")
    @Column(name = "wechat")
    private String wechat;
    /**
     * whatsapp
     */
    @ApiModelProperty(value = "whatsapp")
    @Column(name = "whatsapp")
    private String whatsapp;
    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    @Column(name = "contact_address")
    private String contactAddress;
}
