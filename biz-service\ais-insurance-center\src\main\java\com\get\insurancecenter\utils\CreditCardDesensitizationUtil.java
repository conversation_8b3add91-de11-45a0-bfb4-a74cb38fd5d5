package com.get.insurancecenter.utils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 批量脱敏信用卡号，中间6位或中间部分替换为*
 */
public class CreditCardDesensitizationUtil {

    /**
     * 脱敏中间6位信用卡号集合，返回Map：原始卡号 -> 脱敏卡号
     *
     * @param cardNumbers 原始卡号集合
     * @return Map<String, String>
     */
    public static Map<String, String> maskCardNumbers(List<String> cardNumbers) {
        Map<String, String> result = new HashMap<>();
        if (cardNumbers == null || cardNumbers.isEmpty()) {
            return result;
        }

        for (String cardNumber : cardNumbers) {
            result.put(cardNumber, maskCardNumber(cardNumber));
        }

        return result;
    }

    /**
     * 脱敏单个信用卡号，中间6位脱敏；不足6位：只保留首尾
     *
     * @param cardNumber 原始卡号
     * @return 脱敏后的卡号
     */
    public static String maskCardNumber(String cardNumber) {
        if (cardNumber == null || cardNumber.length() <= 2) {
            return cardNumber; // 长度小于等于2，不处理
        }

        int length = cardNumber.length();

        if (length <= 6) {
            // 保留首位和末位，其余全部 *
            StringBuilder sb = new StringBuilder();
            sb.append(cardNumber.charAt(0));
            for (int i = 1; i < length - 1; i++) {
                sb.append("*");
            }
            sb.append(cardNumber.charAt(length - 1));
            return sb.toString();
        } else {
            // 中间6位脱敏
            int start = (length - 6) / 2;
            int end = start + 6;

            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < length; i++) {
                if (i >= start && i < end) {
                    sb.append("*");
                } else {
                    sb.append(cardNumber.charAt(i));
                }
            }
            return sb.toString();
        }
    }

    /**
     * 脱敏：小于等于5位的字符串，全部用 * 脱敏（支持列表）
     * 返回 Map：原始值 -> 脱敏后值
     */
    public static Map<String, String> desensitizeShortStrings(List<String> inputList) {
        Map<String, String> result = new HashMap<>();
        if (inputList == null || inputList.isEmpty()) {
            return result;
        }

        for (String input : inputList) {
            result.put(input, desensitizeShortString(input));
        }

        return result;
    }

    /**
     * 脱敏：小于等于5位的字符串，全部用 * 脱敏
     */
    public static String desensitizeShortString(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        int length = input.length();
        if (length > 5) {
            return input; // 超过5位不处理
        }
        return generateStars(length);
    }

    /**
     * 生成指定数量的 *
     */
    private static String generateStars(int length) {
        char[] chars = new char[length];
        Arrays.fill(chars, '*');
        return new String(chars);
    }

}
