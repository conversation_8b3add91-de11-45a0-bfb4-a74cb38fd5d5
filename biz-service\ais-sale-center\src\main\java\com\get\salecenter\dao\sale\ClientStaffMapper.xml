<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.get.salecenter.dao.sale.ClientStaffMapper">

    <insert id="insert" parameterType="com.get.salecenter.entity.ClientStaff">
        insert into r_client_staff
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fkClientId != null">fk_client_id,</if>
            <if test="fkStaffId != null">fk_staff_id,</if>
            <if test="isActive != null">is_active,</if>
            <if test="activeDate != null">active_date,</if>
            <if test="unactiveDate != null">unactive_date,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtCreateUser != null">gmt_create_user,</if>
            <if test="gmtModified != null">gmt_modified,</if>
            <if test="gmtModifiedUser != null">gmt_modified_user,</if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fkClientId != null">#{fkClientId},</if>
            <if test="fkStaffId != null">#{fkStaffId},</if>
            <if test="isActive != null">#{isActive},</if>
            <if test="activeDate != null">#{activeDate},</if>
            <if test="unactiveDate != null">#{unactiveDate},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtCreateUser != null">#{gmtCreateUser},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
            <if test="gmtModifiedUser != null">#{gmtModifiedUser},</if>
        </trim>
    </insert>
    <update id="updateById">
        UPDATE r_client_staff
        <set>
            <if test="clientStaff.fkClientId != null">
                fk_client_id = #{clientStaff.fkClientId},
            </if>
            <if test="clientStaff.fkStaffId != null">
                fk_staff_id = #{clientStaff.fkStaffId},
            </if>
            <if test="clientStaff.isActive != null">
                is_active = #{clientStaff.isActive},
            </if>
            <if test="clientStaff.activeDate != null">
                active_date = #{clientStaff.activeDate},
            </if>
            <if test="clientStaff.unactiveDate != null">
                unactive_date = #{clientStaff.unactiveDate},
            </if>
            <if test="clientStaff.gmtModified != null">
                gmt_modified = #{clientStaff.gmtModified},
            </if>
            <if test="clientStaff.gmtModifiedUser != null">
                gmt_modified_user = #{clientStaff.gmtModifiedUser},
            </if>
        </set>
        WHERE id = #{clientStaff.id}
    </update>

    <select id="findAllByClientStaff" resultType="com.get.salecenter.vo.ClientStaffVo">
        select
            id,
            fk_client_id,
            fk_staff_id,
            is_active,
            active_date,
            unactive_date,
            gmt_create,
            gmt_create_user,
            gmt_modified,
            gmt_modified_user
        from r_client_staff
        where fk_client_id = #{clientId}
    </select>
    <select id="selectByStaffId" resultType="com.get.salecenter.vo.ClientStaffVo">
        select
            id,
            fk_client_id,
            fk_staff_id,
            is_active,
            active_date,
            unactive_date,
            gmt_create,
            gmt_create_user,
            gmt_modified,
            gmt_modified_user
        from r_client_staff
        where fk_client_id = #{fkClientId} and fk_staff_id = #{fkStaffId} and is_active = 1
    </select>



</mapper>
