package com.get.salecenter.config;

import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 微信公众号配置
 */
@Configuration
public class WxMpOfficialConfiguration {

    @Value("${wx.pay.official.appid}")
    private String appId;

    @Value("${wx.pay.official.secret}")
    private String secret;

    /**
     * 创建WxMpService实例并注入到Spring容器
     */
    @Bean(name = "wxMpOfficialService")
    public WxMpService wxMpService() {
        WxMpService wxMpService = new WxMpServiceImpl();
        // 配置公众号参数
        WxMpDefaultConfigImpl config = new WxMpDefaultConfigImpl();
        config.setAppId(appId);      // 设置公众号的AppID
        config.setSecret(secret);    // 设置公众号的AppSecret
        wxMpService.setWxMpConfigStorage(config);
        return wxMpService;
    }
}
