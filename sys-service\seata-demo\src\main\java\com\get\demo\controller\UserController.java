package com.get.demo.controller;

import com.get.core.cache.utils.CacheUtil;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.redis.lock.RedisLock;
import com.get.core.secure.UserInfo;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.tool.api.Result;
import com.get.demo.entity.User;
import com.get.demo.mapper.UserMapper;
import com.get.demo.service.ICommonService;
import com.get.demo.service.IUserService;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static com.get.common.cache.CacheNames.BIZ_CACHE;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-07
 */
@RestController
@RequestMapping("/user")
public class UserController {
    @Resource
    private IUserService userService;
    @Resource
    private UserMapper userMapper;
    @Resource
    private ICommonService commonService;

    /**
     * 查询单条
     */
    @GetMapping("/detail")
    public Result<User> detail(User user) {
        return Result.data(userService.getOne(GetCondition.getQueryWrapper(user)));
    }

    /**
     * 演示跨库更新测试多数据源分布式事务
     * 适用跨2个数据源同时操作的方法，否则直接使用本地事务
     */
    @GetMapping("/updateDemo")
    public Result<Boolean> updateDemo() {
        return Result.data(commonService.updateDemo());
    }

    /**
     * 演示seata分布式事务
     * 适用范围：
     * 1、A调用B，比如本方法只调用一次B微服务，则不适用，直接使用本地事务+B返回判断结果抛出异常回滚即可
     * 2、A调B，A调C及以上，适用，注意需要手工判断调用返回的结果是成功还是失败，如失败抛出异常回滚即可
     */
    @GetMapping("/updateFeignDemo")
    public Result<Boolean> updateFeignDemo() {
        return Result.data(commonService.updateFeignDemo());
    }

    /*
     * 演示分布式锁RedisLock
     * 适用范围：重复提交表单及业务逻辑锁（比如库存更新等场景）
     */
    @GetMapping("/testRedisLock")
//    @RedisLock(value = "user:testRedisLock",param = "#user.id",waitTime = 15L)//只使用id
//    @RedisLock(value = "user:testRedisLock",param = "#user.id+#user.name",waitTime = 15L)//使用id+name组合
    @RedisLock(value = "user:testRedisLock", param = "#user.id+#user.name+#user.orderNum", waitTime = 15L)
    public Result<Boolean> testRedisLock(User user) {
        return Result.data(commonService.testRedisLock(user));
    }

    /*
     * 演示分布式锁RedisLock
     * 适用范围：重复提交表单及业务逻辑锁（比如库存更新等场景）
     */
    @GetMapping("/testRedisLockList")
    @VerifyLogin(IsVerify = false)
//    @RedisLock(value = "user:testRedisLock",param = "#user.id",waitTime = 15L)//只使用id
//    @RedisLock(value = "user:testRedisLock",param = "#user.id+#user.name",waitTime = 15L)//使用id+name组合
    @RedisLock(value = "user:testRedisLockList", param = "#users[0].name+#users[0].orderNum", waitTime = 0)
    public Result<Boolean> testRedisLockList(@RequestBody List<User> users) {
        User user = users.get(0);
        System.out.println("获取到的用户信息："+user.toString());
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return Result.data(true);
    }
    /*
     * 演示分布式锁RedisLock
     * 适用范围：重复提交表单及业务逻辑锁（比如库存更新等场景）
     */
    @GetMapping("/testRedisLockList2")
    @RedisLock(value = "user:testRedisLockList", param = "#user.staffId+#user.loginId", waitTime = 0)
    public Result<Boolean> testRedisLockList2(@RequestBody List<User> users, UserInfo user) {
        User user_ = users.get(0);
        System.out.println("获取到当前登录用户信息："+user.toString());
        System.out.println("获取提交的用户信息："+user_.toString());
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return Result.data(true);
    }

    /*
     * 演示缓存分页（spring内置的缓存管理工具，本地接入的是redis缓存）@Cacheable   如：@Cacheable(cacheNames = MENU_CACHE, key = "'auth:routes:' + #user.roleId")
     * 适用范围：针对通用的列表进行缓存,在更新时，清空缓存：CacheUtil.clear(BIZ_CACHE);
     */
    @VerifyLogin(IsVerify = false)
    @GetMapping("/testCacheable")
    @Cacheable(cacheNames = BIZ_CACHE, key = "'biz:page:' + #user.id")
    public Result<List<User>> testCacheable(User user) {
        List<User> users = userService.list();
        return Result.data(users);
    }

    /*
     * 测试增加redis缓存有效期30天
     *  缓存30天：30d
     *  缓存5分钟：5m
     *
     *
     *
     */
    @VerifyLogin(IsVerify = false)
    @GetMapping("/testCacheableTime")
    @Cacheable(cacheNames = BIZ_CACHE + "#5m", key = "'biz:test:page:' + #user.id")
    public Result<List<User>> testCacheableTime(User user) {
        List<User> users = userService.list();
        return Result.data(users);
    }

    /*
     * 测试清理缓存
     */
    @VerifyLogin(IsVerify = false)
    @GetMapping("/clearCacheable")
    public Result<List<User>> clearCacheable(User user) {
        //清理指定key范围缓存
        CacheUtil.clear(BIZ_CACHE + "::biz:test:page");
        //清理以get:biz开头的所有缓存
//        CacheUtil.clear(BIZ_CACHE);
        return Result.data(null);
    }

    @VerifyLogin(IsVerify = false)
    @GetMapping("/testUpdate1")
    public Result<Object> testUpdate1(User user) {
        this.userMapper.updateById(user);
        return Result.data(true);
    }


    @VerifyLogin(IsVerify = false)
    @GetMapping("/testUpdate2")
    public Result<Object> testUpdate2(User user) {
        User user_ = new User();
        user_.setId(1L);
        user_.setName("test1");
        user_.setOrderNum(100);
//        this.userMapper.updateById(user_);
        this.userMapper.updateByIdWithNull(user_);
        return Result.data(true);
    }


    //示例：写入缓存
//    String tokenResult = CacheUtil.get(
//            "hx:baidu",
//            "baidu:appkey:",
//            appKey,
//            () -> HttpUtil.proxyGet(String.format(BaiduConstant.TOKEN_URL, appKey, secret))
//    );

    //示例：清理缓存
//    CacheUtil.clear("hx:baidu");


    //示例：获取数据并写入缓存
//    /**
//     * 下拉数据 系统标签管理
//     */
//    @GetMapping("/select")
//    @ApiOperationSupport(order = 2)
//    @ApiOperation(value = "下拉数据", notes = "传入tagType 0-订单标签 1-商品标签")
//    public R<List<TagsSysSelectVO>> select(Integer tagType) {
//        List<TagsSys> tagsSysList = CacheUtil.get(
//                "hx:tags",
//                "sys:",
//                "tags"+tagType,
//                ()->tagsSysService.list(new LambdaQueryWrapper<TagsSys>().eq(TagsSys::getTagType, tagType))
////			tagsSysService::list
//        );
//        return R.data(TagsSysWrapper.build().listVO(tagsSysList));
//    }

}

