package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.AppAgentApproveComment;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

/**
 * @author: Hardy
 * @create: 2023/3/31 12:35
 * @verison: 1.0
 * @description:
 */
@Data
public class AppAgentApproveCommentVo extends BaseEntity implements Serializable {

    @ApiModelProperty
    private Boolean sendFlag=false;

    //==============实体类AppAgentApproveComment=====================
    /**
     * 学生代理申请Id
     */
    @ApiModelProperty(value = "学生代理申请Id")
    @Column(name = "fk_app_agent_id")
    private Long fkAppAgentId;

    /**
     * 审批意见
     */
    @ApiModelProperty(value = "审批意见")
    @Column(name = "approve_comment")
    private String approveComment;

    /**
     * 邮件通知时间，有就表示已发邮件
     */
    @ApiModelProperty(value = "邮件通知时间，有就表示已发邮件")
    @Column(name = "email_time")
    private Date emailTime;

    private static final long serialVersionUID = 1L;
}
