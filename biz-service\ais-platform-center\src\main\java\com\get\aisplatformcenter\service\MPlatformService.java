package com.get.aisplatformcenter.service;

import com.get.aisplatformcenterap.dto.MPlatformParamsDto;
import com.get.aisplatformcenterap.entity.MPlatformEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_platform】的数据库操作Service
* @createDate 2024-12-19 11:15:30
*/
public interface MPlatformService extends IService<MPlatformEntity> {
    /**
     * 平台 列表
     * @param dto
     * @return
     */
    List<MPlatformEntity> searchList(MPlatformParamsDto dto);

    Long delete(Long id);
}
