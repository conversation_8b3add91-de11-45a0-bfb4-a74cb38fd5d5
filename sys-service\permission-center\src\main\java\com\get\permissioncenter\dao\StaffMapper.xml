<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.permissioncenter.dao.StaffMapper">

    <select id="findStaffIdByLoginId" resultType="java.lang.Long">
    SELECT
    ms.id
    FROM
    m_staff ms
    where id=#{loginId}
  </select>

    <select id="getStaffNameById" parameterType="java.lang.Long" resultType="string">
        select
            CONCAT(if(s.is_on_duty=1,'','【离职】'),s.name,IF(s.name_en is null or s.name_en = '','',CONCAT("（",s.name_en,"）"))) as name
        from
            m_staff s
        where
     s.id = #{id}
     ORDER BY is_on_duty desc
  </select>

    <select id="getStaffLoginIdByIds" resultType="string">
        select
            *
        from
            m_staff s
        where
            s.id = #{id}
    </select>


    <select id="getStaffList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        select
        s.id,
        concat(if(s.is_on_duty=1,'','【离职】'),s.name,IFNULL(concat('(',s.name_en,')'),''),concat('(',d.name,')')) name,
        s.name nameChn,
        concat(if(s.is_on_duty=1,'','【离职】'),s.name,IFNULL(concat('(',s.name_en,')'),'')) AS fullName
        from
        m_staff s
        left join m_department d
        on s.fk_department_id =d.id
        <where>
            <if test="fkCompanyIds!=null">
                s.fk_company_id in
                <foreach collection="fkCompanyIds" item="fkCompanyId" index="index" open="(" separator="," close=")">
                    #{fkCompanyId,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="departNums!=null">
                and d.num in
                <foreach collection="departNums" item="departNum" index="index" open="(" separator="," close=")">
                    #{departNum,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
        ORDER BY is_on_duty desc,id asc
    </select>

    <select id="getStaffByDepartmentIds" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        select
        s.id,
        concat(if(s.is_on_duty=1,'','【离职】'),s.name,IFNULL(concat('(',s.name_en,')'),''),concat('(',d.name,')')) name,
        s.name nameChn,
        concat(if(s.is_on_duty=1,'','【离职】'),s.name,IFNULL(concat('(',s.name_en,')'),'')) AS fullName
        from
        m_staff s
        inner join m_department d
        on s.fk_department_id =d.id
        <where>
            <if test="departmentIds!=null">
                and d.id in
                <foreach collection="departmentIds" item="departmentId" index="index" open="(" separator="," close=")">
                    #{departmentId,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
        ORDER BY is_on_duty desc,id asc
    </select>

    <select id="getStaffByStaffIds" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        select
        s.id,
        concat(if(s.is_on_duty=1,'','【离职】'),s.name,IFNULL(concat('(',s.name_en,')'),''),concat('(',d.name,')')) name,
        s.name nameChn,
        concat(if(s.is_on_duty=1,'','【离职】'),s.name,IFNULL(concat('(',s.name_en,')'),'')) AS fullName,
        s.is_on_duty as status
        from
        m_staff s
        inner join m_department d
        on s.fk_department_id =d.id
        <where>
            <if test="staffIds!=null">
                and s.id in
                <foreach collection="staffIds" item="staffId" index="index" open="(" separator="," close=")">
                    #{staffId,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
        ORDER BY s.is_on_duty desc,s.id asc
    </select>

    <select id="getStaffIdsByAttendanceNum" resultType="com.get.permissioncenter.entity.Staff">
        SELECT id,attendance_num FROM m_staff
        <where>
            <if test="attendanceNums!=null and attendanceNums.size()>0">
                AND attendance_num IN
                <foreach collection="attendanceNums" item="attendanceNum" open="(" separator="," close=")">
                    #{attendanceNum}
                </foreach>
            </if>
            <if test="fkCompanyId != null and fkCompanyId != ''">
               AND fk_company_id = #{fkCompanyId}
            </if>
        </where>
    </select>

    <select id="getStaffIdByNameAndEnName" resultType="java.lang.Long">
        SELECT id FROM m_staff
        <where>
           AND `name` = #{nameChn}
           AND name_en LIKE CONCAT('%',#{name},'%')
           AND fk_company_id = 2
        </where>
    </select>

    <select id="getStaffIdsByCompanyId" resultType="java.lang.Long">
        select id from m_staff
        <where>
            <if test="fkCompanyIds != null and fkCompanyIds.size()>0">
                AND fk_company_id IN
                <foreach collection="fkCompanyIds" item="fkCompanyId" index="index" open="(" separator="," close=")">
                    #{fkCompanyId,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
    </select>

    <select id="isExistByOfficeId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id FROM m_staff where fk_office_id =#{officeId}
    </select>

    <select id="isExistByPositionId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id FROM m_staff where fk_position_id =#{positionId}
    </select>

    <select id="isExistByDepartmentId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id FROM m_staff where fk_department_id =#{departmentId}
    </select>

    <select id="isExistByCompanyId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id FROM m_staff where fk_company_id =#{companyId}
    </select>

    <select id="getCompanyNameByStaffId" resultType="java.lang.String">
    select
     b.short_name
    from
     m_staff a
    left join
     m_company b
    on
     a.fk_company_id = b.id
    where
     a.id = #{staffId}

  </select>

    <select id="getCompanyNameByStaffIds" resultType="com.get.permissioncenter.vo.StaffVo">
        select
        a.id,b.short_name AS companyName,
        concat(if(a.is_on_duty=1,'','【离职】'),a.name,IFNULL(concat('(',a.name_en,')'),'')) AS fullName
        from
        m_staff a
        left join
        m_company b
        on
        a.fk_company_id = b.id
        where
        a.id in
        <foreach collection="staffIds" item="staffId" index="index" open="(" separator="," close=")">
            #{staffId}
        </foreach>
        ORDER BY a.is_on_duty desc
    </select>

    <select id="getStaffIdsByDepartmentIds" resultType="java.lang.Long">
      select id from m_staff where fk_department_id = #{departmentId} AND fk_company_id = #{companyId}
  </select>
    <select id="getTopPositionStaffIds" resultType="java.lang.Long">
      SELECT
	   a.id
      FROM
	   m_staff a
	  RIGHT JOIN
	  (select mp1.id as id from m_position mp1 join (SELECT max(pos_level) as pos_level,fk_department_id,fk_company_id
        FROM m_position WHERE fk_department_id = #{departmentId} AND fk_company_id = #{companyId}) mp2 on mp1.pos_level = mp2.pos_level
        and mp1.fk_department_id = mp2.fk_department_id and mp1.fk_company_id = mp2.fk_company_id) p
	  ON a.fk_position_id = p.id
  </select>

    <select id="getAllPositionStaffIds" resultType="java.lang.Long">
        select DISTINCT(id) from m_staff where fk_company_id = #{companyId}
        AND
        fk_position_id
        IN
        <foreach collection="positionIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>

    <select id="getPositionByNum" resultType="java.lang.Long">
        SELECT ms.id FROM `m_staff` ms left join m_position mp on ms.fk_position_id = mp.id where mp.num in
        <foreach collection="num" item="num" index="index" open="(" separator="," close=")">
            #{num}
        </foreach>
        and ms.is_on_duty = 1
        and ms.is_active = 1
    </select>

    <select id="getCompanyIdByCreateUser" resultType="com.get.permissioncenter.vo.StaffVo">
     select *,concat(if(is_on_duty=1,'','【离职】'),name,IFNULL(concat('(',name_en,')'),'')) AS fullName from m_staff where login_id=#{createUser} ORDER BY is_on_duty desc
    </select>

    <select id="getCompanyIdByCreateUsers" resultType="com.get.permissioncenter.vo.StaffVo">
        select *,concat(if(is_on_duty=1,'','【离职】'),name,IFNULL(concat('(',name_en,')'),'')) AS fullName from m_staff where login_id in
        <foreach collection="createUsers" item="createUser" index="index" open="(" separator="," close=")">
            #{createUser}
        </foreach>
    </select>
    <select id="selectStaffByIdPs" resultType="com.get.permissioncenter.entity.Staff">
        select id id,fk_company_id fkCompanyId,fk_resume_guid fkResumeGuid,login_id loginId,login_ps loginPs,fk_staff_id_supervisor fkStaffIdSupervisor,
            is_modified_ps isModifiedPs,is_modified_resume isModifiedResume,is_admin isAdmin,is_active isActive,
               session_id sessionId,gmt_create gmtCreate,gmt_modified gmtModified,gmt_create_user gmtCreateUser,
               gmt_modified_user gmtModifiedUser,name name,name_en nameEn,fk_department_id fkDepartmentId,fk_position_id fkPositionId from m_staff where login_id = #{loginId} and login_ps = #{loginPs}
               and is_active = 1
    </select>

    <select id="getAllSubordinateIds" resultType="java.lang.Long">
        SELECT
            id
        FROM
            (
                SELECT
                    @ids AS _ids,
		( SELECT @ids := GROUP_CONCAT( id ) FROM m_staff WHERE FIND_IN_SET( fk_staff_id_supervisor, @ids ) ) AS cids,
		@l := @l + 1 AS LEVEL
                FROM
                    m_staff,
                    ( SELECT @ids := #{staffId}, @l := 0 ) b
                WHERE @ids IS NOT NULL ) id,
            m_staff DATA
        WHERE
            FIND_IN_SET( DATA.fk_staff_id_supervisor, id._ids )
        GROUP BY
            id,
            fk_staff_id_supervisor
        ORDER BY
            id
    </select>

    <select id="getDayOfStaffBirthday" resultType="com.get.permissioncenter.entity.Staff">
        select id from m_staff where fk_company_id!=3 AND DATE_FORMAT(birthday,'%m-%d') = DATE_FORMAT(#{nowDate},'%m-%d')
        and is_active = 1
    </select>
    <select id="getAdminDepartmentStaff" resultType="java.lang.Long">
        select ms.id AS id from m_department md join m_staff ms on ms.fk_department_id = md.id
        where md.num = 'GD0014' and ms.is_active = 1 and ms.is_on_duty = 1
    </select>
    <select id="getStaffIdsByPositionNums" resultType="java.lang.Long">
        SELECT
        ms.id
        FROM
        m_staff AS ms
        LEFT JOIN
        m_position AS mp
        ON
        ms.fk_position_id = mp.id
        WHERE
        1 = 1
        <if test="nums!=null and nums.size>0">
            AND mp.num IN
            <foreach collection="nums" open="(" separator="," close=")" item="num">
                #{num}
            </foreach>
        </if>
    </select>

    <update id="updateSalaryEffectiveDate">
    UPDATE m_staff SET salary_effective_date = #{salaryEffectiveDate} WHERE id=#{fkStaffId}
  </update>

    <select id="getStaffIdsBykeyWord" resultType="java.lang.Long">
    SELECT id  FROM m_staff WHERE `name` LIKE CONCAT('%',#{keyWord},'%') OR name_en LIKE CONCAT('%',#{keyWord},'%')
  </select>

    <select id="getStaffByCompanyId" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    SELECT id,name_en AS name,`name` AS nameChn FROM m_staff WHERE fk_company_id=#{fkCompanyId} AND is_on_duty=1
  </select>

    <select id="getStaffs" resultType="com.get.permissioncenter.entity.Staff">
    SELECT * FROM m_staff
    <where>
        <if test="fkCompanyId != null and fkCompanyId!=''">
            AND fk_company_id=#{fkCompanyId}
        </if>
        AND is_on_duty=1
    </where>
</select>

    <select id="getStaffDtoByFkCompanyId" resultType="com.get.permissioncenter.vo.StaffVo">
        SELECT ms.* ,md.name AS departmentName,mo.name AS officeName FROM m_staff AS ms
        LEFT JOIN m_department AS md ON ms.fk_department_id = md.id
        LEFT JOIN m_office AS mo ON ms.fk_office_id = mo.id
        <where>
            <if test="fkCompanyId != null and fkCompanyId!=''">
                AND ms.fk_company_id=#{fkCompanyId}
            </if>
            AND ms.is_on_duty=1
        </where>
    </select>

    <select id="getStaffDtos" resultType="com.get.permissioncenter.vo.StaffVo">
        SELECT
        ms.id,
        ms.fk_company_id,
        ms.fk_department_id,
        concat(ms.name,IFNULL(concat('(',ms.name_en,')'),'')) name,
        ms.entry_date,
        ms.attendance_num,
        mc.short_name AS companyName,
        md.name AS departmentName
        FROM m_staff AS ms
        LEFT JOIN m_company AS mc
        ON ms.fk_company_id = mc.id
        LEFT JOIN m_department AS md
        ON ms.fk_department_id = md.id
        <where>
            <if test="staffNameKeyOrEnNameKey!=null and staffNameKeyOrEnNameKey!=''">
               AND (ms.name LIKE CONCAT('%', #{staffNameKeyOrEnNameKey}, '%') OR ms.name_en LIKE CONCAT('%', #{staffNameKeyOrEnNameKey}, '%'))
            </if>
            <if test="fkCompanyId != null and fkCompanyId!=''">
                AND ms.fk_company_id=#{fkCompanyId}
            </if>
            <if test="fkDepartmentId != null and fkDepartmentId!=''">
                AND ms.fk_department_id = #{fkDepartmentId}
            </if>
            AND is_on_duty=1
        </where>
    </select>

    <select id="getStaffByIds" resultType="com.get.permissioncenter.vo.StaffVo">
        select id,fk_company_id,name,name_en,fk_staff_id_supervisor,email,concat(if(is_on_duty=1,'','【离职】'),name,IFNULL(concat('(',name_en,')'),'')) AS fullName,
        email_password
        from m_staff where id in
        <foreach collection="staffIds" index="index" item="data" open="(" separator="," close=")">
            #{data}
        </foreach>
        ORDER BY is_on_duty DESC
    </select>

    <select id="getStaffListByStaffName" resultType="java.lang.Long">
        select ms.id from m_staff ms
        where name LIKE CONCAT('%', #{staffName}, '%') OR name_en LIKE CONCAT('%', #{staffName}, '%')
    </select>

    <select id="isExistByStaffIdSupervisor" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id from m_staff where fk_staff_id_supervisor=#{staffId}
    </select>
    <select id="getDepartmentAndStaffDtoByStaffIds"
            resultType="com.get.permissioncenter.vo.DepartmentAndStaffVo">
        SELECT
            a.id as fkStaffId,
            b.id as fkDepartmentId,
            CONCAT(a.`name`,IFNULL(CONCAT("（",a.name_en,"）"),"")) as staffName,
            b.`name` as departmentName,
            c.id as fkCompanyId,
            c.short_name as fkCompanyName
        FROM
            m_staff a
                LEFT JOIN m_department b ON a.fk_department_id = b.id
                LEFT JOIN m_company c ON a.fk_company_id = c.id
        where 1=1
          and a.id in
        <foreach collection="staffIds" item="staffId" index="index" open="(" separator="," close=")">
            #{staffId}
        </foreach>

    </select>

    <select id="getStaffDtoByIds" resultType="com.get.permissioncenter.vo.StaffVo">
        SELECT ms.* ,
        md.name AS departmentName,
        mo.name AS officeName,concat(if(ms.is_on_duty=1,'','【离职】'),ms.name,IFNULL(concat('(',ms.name_en,')'),'')) AS fullName
        FROM m_staff AS ms
        LEFT JOIN m_department AS md
        ON ms.fk_department_id = md.id
        LEFT JOIN m_office AS mo
        ON ms.fk_office_id = mo.id
        <where>
            <if test="staffIds!=null and staffIds.size()>0">
                ms.id IN
                <foreach collection="staffIds" item="staffId" open="(" separator="," close=")">
                    #{staffId}
                </foreach>
            </if>
            AND ms.is_on_duty=1
        </where>
    </select>

    <select id="getStaffListByDepartmentId" resultType="com.get.permissioncenter.vo.StaffListByDepartmentVo">
        select a.id,a.`name` as nameChn,a.name_en as name ,concat(a.`name`,IFNULL(concat("（",a.name_en,"）"),"")) as fullName,
        b.short_name as fkCompanyName,
        b.id as fkCompanyId
        from m_staff a
        left join m_company b ON a.fk_company_id = b.id
        where a.fk_company_id = #{companyId} and a.fk_department_id = #{departmentId}
    </select>

    <select id="getStaffByStaffName" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
        a.id,
        CONCAT(a.`name`,IFNULL(CONCAT("（",a.name_en,"）"),"")) as name
        FROM
        m_staff a
        where 1=1
        <if test="companyIds !=null and companyIds.size()>0 ">
            and a.fk_company_id in
            <foreach collection="companyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
                #{fkCompanyId}
            </foreach
            >
        </if>
        <if test="staffName != '' and staffName != null ">
            and (a.name LIKE CONCAT('%', #{staffName}, '%') OR a.name_en LIKE CONCAT('%', #{staffName}, '%'))
        </if>
        limit 20
    </select>
    <select id="getExportInfo" resultType="com.get.permissioncenter.vo.StaffExportVo">
        SELECT
            s.id,
            s.fk_company_id as companyId,
            s.`name`,
            s.name_en as nameEn,
            s.gender,
            s.num,
            c.short_name as companyName,
            d.`name` as departmentName,
            p.`name` positionName,
            CONCAT(c2.num,t2.`name`,'（',t2.name_en,'）') as supervisorName,
            s.identity_num,
            s.home_tel,
            s.mobile,
            s.email,
            s.zip_code,
            s.address,
            s.emergency_contact,
            s.emergency_relationship,
            s.emergency_tel,
            s.birthday,
            s.hukou,
            GROUP_CONCAT(DISTINCT t.`name`) as superiorNames,
            GROUP_CONCAT(DISTINCT CONCAT(g.group_name,' ',l.grade_name)) as groupName,
            s.entry_date,
            s.pass_probation_date,
            s.work_tel,
            GROUP_CONCAT(DISTINCT o.`name`) as officeNames,
            GROUP_CONCAT(DISTINCT ac.name_chn) as areaCountryVos
        FROM
            m_staff s
        LEFT JOIN r_permission_group_grade_staff rs ON rs.fk_staff_id = s.id
        LEFT JOIN m_permission_group g ON rs.fk_permission_group_id = g.id
        LEFT JOIN m_permission_grade l ON rs.fk_permission_grade_id = l.id
        LEFT JOIN m_company c ON c.id = s.fk_company_id
        LEFT JOIN m_department d ON d.id = s.fk_department_id
        LEFT JOIN m_position p ON p.id = s.fk_position_id
        LEFT JOIN r_staff_superior su ON su.fk_staff_id = s.id
        LEFT JOIN m_staff t ON su.fk_staff_superior_id = t.id
        LEFT JOIN m_staff t2 ON t2.id = s.fk_staff_id_supervisor
        LEFT JOIN m_company c2 ON c2.id = t2.fk_company_id
        LEFT JOIN r_staff_area_country sc ON sc.fk_staff_id = s.id
        LEFT JOIN ais_institution_center.u_area_country ac ON sc.fk_area_country_key = ac.num
        LEFT JOIN r_staff_office ro ON ro.fk_staff_id = s.id
        LEFT JOIN m_office o ON o.id = ro.fk_office_id AND s.fk_company_id = o.fk_company_id
        WHERE
            1=1
            <if test="staffDto.fkCompanyId!=null and staffDto.fkCompanyId!=''">
                AND s.fk_company_id = #{staffDto.fkCompanyId}
            </if>
            <if test="staffDto.fkDepartmentId!=null and staffDto.fkDepartmentId!=''">
                AND s.fk_department_id = #{staffDto.fkDepartmentId}
            </if>
            <if test="staffDto.fkPositionId!=null and staffDto.fkPositionId!=''">
                AND s.fk_position_id = #{staffDto.fkPositionId}
            </if>
            <if test="staffDto.isOnDuty!=null and staffDto.isOnDuty!=''">
                AND s.is_on_duty = #{staffDto.isOnDuty}
            </if>
            <if test="staffDto.isActive!=null and staffDto.isActive!=''">
                AND s.is_active = #{staffDto.isActive}
            </if>
            <if test="staffDto.keyWord!=null and staffDto.keyWord!=''">
                AND (s.name like concat('%',#{staffDto.keyWord},'%') OR s.num like concat('%',#{staffDto.keyWord},'%') OR s.name_en like concat('%',#{staffDto.keyWord},'%')
                OR s.login_id like concat('%',#{staffDto.keyWord},'%')
                )
            </if>
        GROUP BY
            s.id
        ORDER BY
	    s.is_on_duty DESC
    </select>
    <select id="selectStaffInfoById" resultType="com.get.permissioncenter.vo.StaffVo">
        SELECT
            s.*, d.`name` AS departmentName,
            d.num AS departmentNum,
            concat(
                s. NAME,
                IFNULL(
                    concat('(', s.name_en, ')'),
                    ''
                )
            ) AS fullName,
            mp.num AS positionNum
        FROM
            m_staff s
        LEFT JOIN m_department d ON s.fk_department_id = d.id
        LEFT JOIN m_position AS mp ON mp.id = s.fk_position_id
        WHERE
            s.id = #{staffId}
    </select>
    <select id="getStaffDtosByDepartmentNums" resultType="com.get.permissioncenter.vo.StaffVo">
        SELECT
        DISTINCT a.*
        FROM
        m_staff a
        LEFT JOIN m_department b ON a.fk_department_id = b.id
        LEFT JOIN r_staff_area_country c ON a.id = c.fk_staff_id
        LEFT JOIN ais_institution_center.u_area_country d on c.fk_area_country_key = d.num
        where 1=1
        <if test="nums !=null and nums.size() > 0">
            and b.num in
            <foreach collection="nums" item="num" index="index" open="(" separator="," close=")">
                #{num}
            </foreach>
        </if>
        <if test="countryId !=null">
            and d.id = #{countryId}
        </if>
    </select>
    <select id="getStaffByLoginId" resultType="com.get.permissioncenter.vo.StaffVo">
       SELECT id,fk_company_id,name,name_en,fk_staff_id_supervisor,email,concat(name,IFNULL(concat('(',name_en,')'),'')) AS fullName
       FROM m_staff WHERE login_id = #{loginId}
    </select>
    <select id="getStaffByDepartmentIdsAndCountryNum" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        select
        s.id,
        concat(if(s.is_on_duty=1,'','【离职】'),s.name,IFNULL(concat('(',s.name_en,')'),''),concat('(',d.name,')')) name,
        s.name nameChn,
        concat(if(s.is_on_duty=1,'','【离职】'),s.name,IFNULL(concat('(',s.name_en,')'),'')) AS fullName
        from
        m_staff s
        inner join m_department d on s.fk_department_id =d.id
        <if test="fkCountryNum!=null and fkCountryNum!=''">
            inner join r_staff_area_country r on s.id = fk_staff_id and r.fk_area_country_key = #{fkCountryNum}
        </if>
        where 1=1
        <if test="departmentIds!=null">
            and d.id in
            <foreach collection="departmentIds" item="departmentId" index="index" open="(" separator="," close=")">
                #{departmentId,jdbcType=BIGINT}
            </foreach>
        </if>
        ORDER BY is_on_duty desc,id asc
    </select>
    <select id="getOnDutyStaffList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        select
        s.id,
        concat(s.name,IFNULL(concat('(',s.name_en,')'),''),concat('(',d.name,')')) name,
        s.name nameChn,
        concat(s.name,IFNULL(concat('(',s.name_en,')'),'')) AS fullName
        from
        m_staff s
        left join m_department d
        on s.fk_department_id =d.id
        <where>
            <if test="fkCompanyIds!=null">
                s.fk_company_id in
                <foreach collection="fkCompanyIds" item="fkCompanyId" index="index" open="(" separator="," close=")">
                    #{fkCompanyId,jdbcType=BIGINT}
                </foreach>
            </if>
            and is_on_duty = 1
        </where>
        ORDER BY id asc
    </select>
    <select id="getStaffIdsByResourceKey" resultType="java.lang.Long">
        SELECT
            a.id
        FROM
            m_staff a
                LEFT JOIN r_staff_resource b ON a.id = b.fk_staff_id
        WHERE
            b.fk_resource_key = #{resourceKey}
           <if test="isContainAdmin !=null and isContainAdmin">
           OR a.is_admin = 1
           </if>
        GROUP BY a.id
    </select>

    <select id="getPushDepartmentStaffEmail" resultType="java.lang.String">
        SELECT ms.email FROM ais_permission_center.m_staff AS ms
        INNER JOIN ais_permission_center.m_department AS md ON md.id = ms.fk_department_id
        INNER JOIN ais_permission_center.r_staff_area_country as rsac ON rsac.fk_staff_id = ms.id
        INNER JOIN ais_institution_center.u_area_country AS uac ON uac.num = rsac.fk_area_country_key
        <if test="fkInstitutionId != null">
            INNER JOIN ais_institution_center.m_institution AS mi ON mi.fk_area_country_id = uac.id AND mi.id = #{fkInstitutionId}
        </if>
        WHERE ms.is_active = 1 and ms.is_on_duty = 1 and ms.email is not null AND ms.email != ''
        <if test="fkCountryId != null">
            AND uac.id = #{fkCountryId}
        </if>
        <if test="departmentList != null and departmentList.size()>0 ">
            AND md.num IN
            <foreach collection="departmentList" item="department" index="index" open="(" separator="," close=")">
                #{department}
            </foreach>
        </if>
        GROUP BY ms.email
    </select>

    <select id="getAuthorizedStaffIdsByResourceKey" resultType="java.lang.Long">
        SELECT
            a.fk_staff_id
        FROM r_permission_group_grade_staff a
        INNER JOIN r_permission_group_grade_resource b ON a.fk_permission_group_id = b.fk_permission_group_id AND a.fk_permission_grade_id = b.fk_permission_grade_id
        WHERE b.fk_resource_key = #{resourceKey}
        GROUP BY a.fk_staff_id
        UNION
        SELECT
            fk_staff_id
        FROM r_staff_resource
        WHERE fk_resource_key = #{resourceKey}
    </select>

    <select id="getResetPasswordTemplate" resultType="com.get.permissioncenter.vo.ResetPasswordTemplateVo">
        select title, email_template as template
        from ais_reminder_center.u_remind_template
        where fk_remind_event_type_key = 'RESET_PASSWORD' limit 1
    </select>

    <select id="selectStaffPageWithGroupBind" resultType="com.get.permissioncenter.vo.StaffVo">
        SELECT ms.*
        <if test="staffVo.fkInstitutionPermissionGroupId !=null and staffVo.fkInstitutionPermissionGroupId !=''">
            ,ripgs.id AS fkInstitutionPermissionGroupStaffId
            ,COALESCE(GROUP_CONCAT(rsb.fk_staff_id_bd), '') AS staffIdBdIds
<!--           ,(SELECT-->
<!--            GROUP_CONCAT(sub_mc.id ORDER BY sub_mc.is_on_duty DESC SEPARATOR ',')-->
<!--            FROM-->
<!--            ais_permission_center.m_staff AS sub_mc-->
<!--            WHERE-->
<!--            FIND_IN_SET(sub_mc.id,COALESCE(GROUP_CONCAT(rsbc.fk_staff_id), '')) > 0 )AS staffBdStaffIds-->
        </if>
        FROM m_staff ms
        <if test="staffVo.fkInstitutionPermissionGroupId != null and staffVo.fkInstitutionPermissionGroupId != ''">
            LEFT JOIN r_institution_permission_group_staff ripgs
            ON ripgs.fk_staff_id = ms.id
            AND ripgs.fk_institution_permission_group_id = #{staffVo.fkInstitutionPermissionGroupId}
            LEFT JOIN r_staff_bd rsb ON rsb.fk_staff_id = ms.id
            LEFT JOIN ais_sale_center.r_staff_bd_code rsbc ON rsbc.fk_staff_id = rsb.fk_staff_id_bd
        </if>
        <where>
            <if test="staffVo.fkInstitutionPermissionGroupId != null and staffVo.fkInstitutionPermissionGroupId != ''">
<!--                <if test="isBind == 1">-->
<!--                    AND EXISTS (-->
<!--                    SELECT 1 FROM r_institution_permission_group_staff ripgs-->
<!--                    WHERE ripgs.fk_staff_id = ms.id-->
<!--                    AND ripgs.fk_institution_permission_group_id = #{fkInstitutionPermissionGroupId}-->
<!--                    )-->
<!--                </if>-->
<!--                <if test="isBind == 0">-->
<!--                    AND NOT EXISTS (-->
<!--                    SELECT 1 FROM r_institution_permission_group_staff ripgs-->
<!--                    WHERE ripgs.fk_staff_id = ms.id-->
<!--                    AND ripgs.fk_institution_permission_group_id = #{fkInstitutionPermissionGroupId} and ms.is_active = 1-->
<!--                    )-->
<!--                </if>-->
                <choose>
                    <when test="staffVo.isBind == 0">
                        AND ripgs.id IS NULL  <!-- 未绑定 -->
                    </when>
                    <when test="staffVo.isBind == 1">
                        AND ripgs.id IS NOT NULL  <!-- 已绑定 -->
                    </when>
                </choose>

            </if>

            <choose>
                <when test="staffVo.fkCompanyId != null and staffVo.fkCompanyId != ''">
                    AND ms.fk_company_id = #{staffVo.fkCompanyId}
                </when>

                <otherwise>
                    AND ms.fk_company_id IN
                    <foreach collection="companyIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </otherwise>
            </choose>

            <if test="staffVo.fkDepartmentId != null and staffVo.fkDepartmentId != ''">
                AND ms.fk_department_id = #{staffVo.fkDepartmentId}
            </if>

            <if test="staffVo.fkDepartmentId != null and staffVo.fkDepartmentId != ''">
                AND ms.fk_department_id = #{staffVo.fkDepartmentId}
            </if>
            <if test="staffVo.fkPositionId != null and staffVo.fkPositionId != ''">
                AND ms.fk_position_id = #{staffVo.fkPositionId}
            </if>
            <if test="staffVo.isActive != null and staffVo.isActive != ''">
                AND ms.is_active = #{staffVo.isActive}
            </if>
            <if test="staffVo.isOnDuty != null and staffVo.isOnDuty != ''">
                AND ms.is_on_duty = #{staffVo.isOnDuty}
            </if>
            <if test="staffVo.keyWord != null and staffVo.keyWord != ''">
                AND (
                ms.name LIKE CONCAT('%', #{staffVo.keyWord}, '%')
                OR ms.name_en LIKE CONCAT('%', #{staffVo.keyWord}, '%')
                OR ms.num LIKE CONCAT('%', #{staffVo.keyWord}, '%')
                OR ms.login_id LIKE CONCAT('%', #{staffVo.keyWord}, '%')
                )
            </if>
            <if test="staffVo.staffKeyWord != null and staffVo.staffKeyWord != ''">
                AND (
                ms.name LIKE CONCAT('%', #{staffVo.staffKeyWord}, '%')
                OR ms.name_en LIKE CONCAT('%', #{staffVo.staffKeyWord}, '%')
                OR ms.num LIKE CONCAT('%', #{staffVo.staffKeyWord}, '%')
                OR ms.login_id LIKE CONCAT('%', #{staffVo.staffKeyWord}, '%')
                )
            </if>
<!--            <if test="ew != null and ew.sqlSegment != null">-->
<!--                AND ${ew.sqlSegment}-->
<!--            </if>-->
        </where>
        GROUP BY ms.id
        ORDER BY ms.is_on_duty DESC, ms.is_active DESC
    </select>

    <select id="selectStaffByIdsAndCompanyIds" resultType="com.get.permissioncenter.entity.Staff">
        SELECT * FROM m_staff ms
        WHERE
            ms.is_active = 1
        <if test="staffIds != null and staffIds.size() > 0">
            AND ms.id IN
            <foreach item="item" index="index" collection="staffIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size() > 0">
            AND ms.fk_company_id IN
            <foreach item="item" index="index" collection="companyIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getObtainDirectSubordinatesIds" resultType="java.lang.Long">
        SELECT
            ms.id
        FROM
            m_staff ms
        WHERE
            ms.fk_staff_id_supervisor = #{staffId}
        ORDER BY ms.is_on_duty DESC, ms.is_active DESC
    </select>

    <select id="getStaffDatas" resultType="com.get.permissioncenter.vo.StaffVo">
        SELECT ms.id,
        ms.fk_company_id,
        ms.fk_department_id,
        ms.fk_position_id,
        ms.fk_office_id,
        ms.num,
        ms.name,
        ms.name_en,
        ms.gender,
        ms.is_active
        FROM m_staff ms
        <where>
            <if test="ew != null and ew.sqlSegment != null">
                AND ${ew.sqlSegment}
            </if>
        </where>
        ORDER BY ms.is_on_duty DESC, ms.is_active DESC
    </select>
</mapper>