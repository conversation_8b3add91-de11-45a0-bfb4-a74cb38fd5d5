package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 申请计划费用代付参数
 */
@Data
public class OfferItemPaymentFeeDto {

    @ApiModelProperty(value = "申请计划id")
    @NotNull(message = "申请计划id不能为空")
    private Long fkStudentOfferItemId;

    @ApiModelProperty(value = "代付类型：1.申请费 2.全额学费 3.学费首付 Deposit 4.学费尾款 Remaining")
    @NotNull(message = "代付类型不能为空")
    private Integer paymentType;

    @ApiModelProperty(value = "代付币种")
    @NotBlank(message = "代付币种不能为空")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "代付金额")
    @NotNull(message = "代付金额不能为空")
    private BigDecimal paidAmount;

    @ApiModelProperty(value = "收件人邮箱")
    @NotBlank(message = "收件人邮箱不能为空")
    private String email;

}
