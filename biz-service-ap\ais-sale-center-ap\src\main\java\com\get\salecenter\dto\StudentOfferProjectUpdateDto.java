package com.get.salecenter.dto;

import com.get.salecenter.dto.query.StudentOfferListQueryDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class StudentOfferProjectUpdateDto {


    @ApiModelProperty(value = "搜索条件")
    private StudentOfferListQueryDto studentOfferListVo;

    @ApiModelProperty(value = "旧项目成员id")
    @NotNull(message = "旧项目成员不能为空")
    private Long fkStaffIdOld;

    @ApiModelProperty(value = "旧角色id")
    @NotNull(message = "旧角色不能为空")
    private Long fkStudentProjectRoleIdOld;

    @ApiModelProperty(value = "新项目成员id")
    @NotNull(message = "新项目成员不能为空")
    private Long fkStaffIdOldNew;

    @ApiModelProperty(value = "新角色id")
    @NotNull(message = "新角色不能为空")
    private Long fkStudentProjectRoleIdNew;
}
