package com.get.permissioncenter.dao;

import com.get.core.mybatis.mapper.GetMapper;
import com.get.permissioncenter.entity.StaffAreaCountry;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface StaffAreaCountryMapper extends GetMapper<StaffAreaCountry> {

    List<StaffAreaCountry> getStaffAreaCountrysByfkStaffId(Long fkStaffId);

    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [staffId]
     * <AUTHOR>
     */
    Boolean isExistByStaffId(@Param("staffId") Long staffId);


    List<String> getStaffAreaCountryKeysByfkStaffId(Long staffId);

    Set<Long> getStaffAreaCountryIdByfkStaffId(Long staffId);

    /**
     * 根据业务国家keys和员工ids获取对应员工
     *
     * @param staffIds
     * @param areaCountryKeys
     * @return
     */
    List<Long> getStaffByAreaCountryKeys(List<Long> staffIds, List<String> areaCountryKeys);

}