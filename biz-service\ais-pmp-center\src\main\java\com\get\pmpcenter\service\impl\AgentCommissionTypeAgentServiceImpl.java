package com.get.pmpcenter.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.pmpcenter.dto.agent.AgentCommissionTypeAgentDto;
import com.get.pmpcenter.entity.AgentCommissionTypeAgent;
import com.get.pmpcenter.mapper.AgentCommissionTypeAgentMapper;
import com.get.pmpcenter.service.AgentCommissionTypeAgentService;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.salecenter.vo.AgenCommissionAndAgentSearchVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:26
 * @Version 1.0
 */
@Service
@Slf4j
public class AgentCommissionTypeAgentServiceImpl extends ServiceImpl<AgentCommissionTypeAgentMapper, AgentCommissionTypeAgent> implements AgentCommissionTypeAgentService {
    @Resource
    private AgentCommissionTypeAgentMapper agentCommissionTypeAgentMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ISaleCenterClient saleCenterClient;


    @Override
    public void add(AgentCommissionTypeAgentDto agentCommissionTypeAgentDto) {
        if (GeneralTool.isNotEmpty(agentCommissionTypeAgentDto.getFkAgentIds()) && GeneralTool.isNotEmpty(agentCommissionTypeAgentDto.getFkAgentCommissionTypeId())) {
            AgentCommissionTypeAgent agentCommissionTypeAgent = BeanCopyUtils.objClone(agentCommissionTypeAgentDto, AgentCommissionTypeAgent::new);
            utilService.setCreateInfo(agentCommissionTypeAgent);
            for (Long agentId : agentCommissionTypeAgentDto.getFkAgentIds()) {
                agentCommissionTypeAgent.setFkAgentId(agentId);
                agentCommissionTypeAgentMapper.insert(agentCommissionTypeAgent);
            }
            return;
        }
        throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
    }

    @Override
    public int delete(Long id) {
        return agentCommissionTypeAgentMapper.deleteById(id);

    }

    @Override
    public ResponseBo dataList(SearchBean<AgentCommissionTypeAgentDto> page) {

        AgentCommissionTypeAgentDto agentCommissionTypeAgentDto = page.getData();
        if (GeneralTool.isEmpty(agentCommissionTypeAgentDto.getIsBind())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("is_bind_null"));
        }
        if (GeneralTool.isEmpty(agentCommissionTypeAgentDto.getFkAgentCommissionTypeId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_commission_type_id_is_null"));
        }
        //查询所有代理 业务下属
        //远程调用时做权限校验，员工id + 业务下属员工ids
        Result<AgenCommissionAndAgentSearchVo> agentsAll = saleCenterClient.getAgentCommissionTypeAndAgentIsBind(page);
        if (! (agentsAll.isSuccess()) && GeneralTool.isEmpty(agentsAll.getData())){
            return new ResponseBo<>(null);
        }

        AgenCommissionAndAgentSearchVo data = agentsAll.getData();


        return new ResponseBo<>(data.getAgentCommissionAndAgentList(),data.getPage());

    }

}
