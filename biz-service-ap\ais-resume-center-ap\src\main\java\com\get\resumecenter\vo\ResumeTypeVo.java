package com.get.resumecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.resumecenter.entity.ResumeType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @DATE: 2020/7/30
 * @TIME: 12:18
 * @Description:
 **/
@Data
public class ResumeTypeVo extends BaseEntity {
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    @Column(name = "type_name")
    private String typeName;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    @Column(name = "view_order")
    private Integer viewOrder;

}
