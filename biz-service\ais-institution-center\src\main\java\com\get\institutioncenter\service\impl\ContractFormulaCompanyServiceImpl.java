package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.ContractFormulaCompanyMapper;
import com.get.institutioncenter.dao.ContractFormulaMajorLevelMapper;
import com.get.institutioncenter.vo.CompanyTreeVo;
import com.get.institutioncenter.entity.ContractFormulaCompany;
import com.get.institutioncenter.entity.ContractFormulaMajorLevel;
import com.get.institutioncenter.service.IContractFormulaCompanyService;
import com.get.institutioncenter.dto.ContractFormulaCompanyDto;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2021/1/8 10:27
 * @verison: 1.0
 * @description:
 */
@Service
public class ContractFormulaCompanyServiceImpl extends BaseServiceImpl<ContractFormulaCompanyMapper, ContractFormulaCompany> implements IContractFormulaCompanyService {
    @Resource
    private ContractFormulaCompanyMapper contractFormulaCompanyMapper;
    @Resource
    private ContractFormulaMajorLevelMapper contractFormulaMajorLevelMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editContractFormulaCompany(List<ContractFormulaCompanyDto> contractFormulaCompanyDtos) {
        if (GeneralTool.isEmpty(contractFormulaCompanyDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //先删后增
        List<Long> companyIds = SecureUtil.getCompanyIds();
        LambdaQueryWrapper<ContractFormulaCompany> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContractFormulaCompany::getFkContractFormulaId, contractFormulaCompanyDtos.get(0).getFkContractFormulaId()).in(ContractFormulaCompany::getFkCompanyId,companyIds);
        contractFormulaCompanyMapper.delete(wrapper);

        List<ContractFormulaCompany> collect = contractFormulaCompanyDtos.stream().map(contractFormulaCompanyVo -> BeanCopyUtils.objClone(contractFormulaCompanyVo, ContractFormulaCompany::new)).collect(Collectors.toList());
        for (ContractFormulaCompany contractFormulaCompany : collect) {
            utilService.updateUserInfoToEntity(contractFormulaCompany);
            contractFormulaCompanyMapper.insertSelective(contractFormulaCompany);
        }
    }

    @Override
    public List<Long> getCompanyIdListByFkid(Long contractFormulaId) {
        return contractFormulaCompanyMapper.getCompanyIdListByFkid(contractFormulaId);
    }

    @Override
    public Map<Long, String> getCompanyNameMapByFkids(List<Long> contractFormulaIds) {
        //关系map
        Map<Long, List<Long>> idMap = new HashMap<>();
        Map<Long, String> nameMap = new HashMap<>();
        //全部companyId集合
        Set<Long> companyIdSet = new HashSet<>();
        for (Long contractFormulaId : contractFormulaIds) {
            //通过contractFormulaId获取对应所有公司id
            List<Long> companyIds = getCompanyIdListByFkid(contractFormulaId);
            companyIdSet.addAll(companyIds);
            //contractFormulaId和companyIds一一对应关系map
            idMap.put(contractFormulaId, companyIds);
        }
        //查出companyId和companyName对应关系map
        //111111
        Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(companyIdSet);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }

        //map由contractFormulaId 对应 companyIds 转成 contractFormulaId 对应 companyNames
        for (Map.Entry<Long, List<Long>> contractFormula : idMap.entrySet()) {
            List<String> companyNames = new ArrayList<>();
            List<Long> companyIds = contractFormula.getValue();
            companyIds.retainAll(SecureUtil.getCompanyIds());
            for (Long companyId : companyIds) {
                companyNames.add(result.getData().get(companyId));
            }
            nameMap.put(contractFormula.getKey(), StringUtils.join(companyNames, "，"));
        }
        return nameMap;
    }

    @Override
    public List<CompanyTreeVo> getContractFormulaCompany(Long contractFormulaId) {
        if (GeneralTool.isEmpty(contractFormulaId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //获取公司
        List<CompanyTreeVo> companyTreeVo = getCompanyTreeDto();
        if (GeneralTool.isEmpty(companyTreeVo)) {
            return null;
        }
        List<ContractFormulaCompany> relation = getRelationByContractFormulaId(contractFormulaId);
        setContactFlag(companyTreeVo, relation);
        return getTreeList(companyTreeVo);
    }

    @Override
    public void deleteByFkid(Long contractFormulaId) {
        LambdaQueryWrapper<ContractFormulaMajorLevel> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContractFormulaMajorLevel::getFkContractFormulaId, contractFormulaId);
        contractFormulaMajorLevelMapper.delete(wrapper);
    }

    private List<CompanyTreeVo> getCompanyTreeDto() {
        Result<List<com.get.permissioncenter.vo.tree.CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        List<CompanyTreeVo> companyTreeVos = BeanCopyUtils.copyListProperties(result.getData(), CompanyTreeVo::new);
        return companyTreeVos;
    }

    private List<ContractFormulaCompany> getRelationByContractFormulaId(Long contractFormulaId) {
        LambdaQueryWrapper<ContractFormulaCompany> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContractFormulaCompany::getFkContractFormulaId, contractFormulaId);
        List<ContractFormulaCompany> contractFormulaCommissions = contractFormulaCompanyMapper.selectList(wrapper);
        return contractFormulaCommissions;
    }


    private void setContactFlag(List<CompanyTreeVo> companyTreeVo, List<ContractFormulaCompany> relation) {
        for (CompanyTreeVo treeDto : companyTreeVo) {
            for (ContractFormulaCompany contractFormulaCompany : relation) {
                if (GeneralTool.isNotEmpty(treeDto.getId()) && GeneralTool.isNotEmpty(contractFormulaCompany.getFkCompanyId()) && treeDto.getId().equals(String.valueOf(contractFormulaCompany.getFkCompanyId()))) {
                    treeDto.setFlag(true);
                }
            }
        }
    }

    private List<CompanyTreeVo> getTreeList(List<CompanyTreeVo> entityList) {
        List<CompanyTreeVo> resultList = new ArrayList<>();
        // 获取顶层元素集合
        String parentId;
        for (CompanyTreeVo entity : entityList) {
            parentId = String.valueOf(entity.getFkParentCompanyId());
            if (parentId == null || "0".equals(parentId)) {
                //获取父节点的部门信息
                resultList.add(entity);
            }
        }
        //假如没有父节点
        if (GeneralTool.isEmpty(resultList)) {
            //获取最小节点
            CompanyTreeVo minTreeNode = entityList.stream().min(Comparator.comparing(CompanyTreeVo::getFkParentCompanyId)).get();
            resultList.add(minTreeNode);
            if (GeneralTool.isNotEmpty(minTreeNode)) {
                //获取相同的最小节点
                List<CompanyTreeVo> minTreeNodes = entityList.stream().filter(treeDto ->
                        treeDto.getFkParentCompanyId().equals(minTreeNode.getFkParentCompanyId()) &&
                                !treeDto.getId().equals(minTreeNode.getId())).distinct().collect(Collectors.toList());
                resultList.addAll(minTreeNodes);
            }
        }
        // 获取每个顶层元素的子数据集合
        for (CompanyTreeVo entity : resultList) {
            entity.setChildList(getSubList(entity.getId(), entityList));
        }
        return resultList;
    }

    private List<CompanyTreeVo> getSubList(Long id, List<CompanyTreeVo> entityList) {
        List<CompanyTreeVo> childList = new ArrayList<>();
        Long parentId;
        // 子集的直接子对象
        for (CompanyTreeVo entity : entityList) {
            if (GeneralTool.isNotEmpty(entity.getFkParentCompanyId()) && GeneralTool.isNotEmpty(id)) {
                parentId = entity.getFkParentCompanyId();
                if (id.equals(parentId)) {
                    //获取子节点的部门信息
                    childList.add(entity);
                }
            }
        }
        // 子集的间接子对象
        for (CompanyTreeVo entity : childList) {
            //递归调用
            entity.setChildList(getSubList(entity.getId(), entityList));
        }
        // 递归退出条件
        if (childList.size() == 0) {
            return null;
        }
        return childList;
    }
}
