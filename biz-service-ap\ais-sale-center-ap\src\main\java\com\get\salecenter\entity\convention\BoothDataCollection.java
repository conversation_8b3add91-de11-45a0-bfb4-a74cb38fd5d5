package com.get.salecenter.entity.convention;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_booth_data_collection")
public class BoothDataCollection extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "峰会Id")
    @Column(name = "fk_convention_id")
    private Long fkConventionId;

    @ApiModelProperty(value = "峰会报名Id")
    @Column(name = "fk_convention_registration_id")
    private Long fkConventionRegistrationId;

    @ApiModelProperty(value = "简介")
    @Column(name = "profile")
    private String profile;

}
