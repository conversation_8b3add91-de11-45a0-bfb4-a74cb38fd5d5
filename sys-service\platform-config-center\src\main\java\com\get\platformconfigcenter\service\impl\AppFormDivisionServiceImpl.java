package com.get.platformconfigcenter.service.impl;

import com.get.core.mybatis.base.UtilService;
import com.get.platformconfigcenter.dao.appissue.AppFormDivisionMapper;
import com.get.platformconfigcenter.dao.appissue.IssueTranslationMapper;
import com.get.platformconfigcenter.service.AppFormDivisionService;
import com.get.platformconfigcenter.service.DeleteService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 申请表单板块业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2021/5/20 15:33
 */
@Service
public class AppFormDivisionServiceImpl implements AppFormDivisionService {
//    @Resource
//    private AppFormDivisionMapper appFormDivisionMapper;
    @Resource
    private AppFormDivisionMapper appFormDivisionMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private DeleteService deleteService;
    @Resource
    private IssueTranslationMapper issueTranslationMapper;


    /**
     * 申请表单板块列表数据
     *
     * @return
     * @Date 15:36 2021/5/20
     * <AUTHOR>
     */
//    @Override
//    public List<AppFormDivisionVo> getAppFormDivisionList(AppFormDivisionDto appFormDivisionVo, Page page) {
////        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
////        Example example = new Example(AppFormDivision.class);
////        example.createCriteria().andLike("name", "%" + appFormDivisionVo.getName() + "%");
////        example.orderBy("viewOrder").desc();
////        List<AppFormDivision> appFormDivisions = appFormDivisionMapper.selectByExample(example);
////        page.restPage(appFormDivisions);
//
//        LambdaQueryWrapper<AppFormDivision> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.like(AppFormDivision::getName, appFormDivisionVo.getName());
//        lambdaQueryWrapper.orderByDesc(AppFormDivision::getViewOrder);
//        IPage<AppFormDivision> pages = appFormDivisionMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
//        List<AppFormDivision> appFormDivisions = pages.getRecords();
//        page.setAll((int) pages.getTotal());
//        List<AppFormDivisionVo> appFormDivisionDtos = new ArrayList<>();
//        for (AppFormDivision appFormDivision : appFormDivisions) {
//            AppFormDivisionVo appFormDivisionDto = BeanCopyUtils.objClone(appFormDivision, AppFormDivisionVo::new);
//            appFormDivisionDto.setFkTableName(TableEnum.U_APP_FORM_DIVISION.key);
//            appFormDivisionDtos.add(appFormDivisionDto);
//        }
//        return appFormDivisionDtos;
//    }

    /**
     * 新增申请表单板块
     *
     * @Date 16:13 2021/5/20
     * <AUTHOR>
     */
//    @Override
//    public Long addAppFormDivision(AppFormDivisionDto appFormDivisionVo) {
//        if (GeneralTool.isEmpty(appFormDivisionVo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
//        }
////        Example example = new Example(AppFormDivision.class);
////        example.createCriteria().andEqualTo("name", appFormDivisionVo.getName());
////        Example.Criteria criteria = example.createCriteria().andEqualTo("divisionKey", appFormDivisionVo.getDivisionKey());
////        example.or(criteria);
////        List<AppFormDivision> appFormDivisions = appFormDivisionMapper.selectByExample(example);
//        LambdaQueryWrapper<AppFormDivision> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(AppFormDivision::getName, appFormDivisionVo.getName());
//        lambdaQueryWrapper.or();
//        lambdaQueryWrapper.eq(AppFormDivision::getDivisionKey, appFormDivisionVo.getDivisionKey());
//        List<AppFormDivision> appFormDivisions = appFormDivisionMapper.selectList(lambdaQueryWrapper);
//        if (GeneralTool.isNotEmpty(appFormDivisions)) {
//            throw new GetServiceException(ResultCode.INVALID_PARAM, LocaleMessageUtils.getMessage("type_name_exists"));
//        }
//        AppFormDivision appFormDivision = BeanCopyUtils.objClone(appFormDivisionVo, AppFormDivision::new);
//        appFormDivision.setViewOrder(appFormDivisionMapper.getMaxViewOrder());
//        utilService.updateUserInfoToEntity(appFormDivision);
//        appFormDivisionMapper.insert(appFormDivision);
//        return appFormDivision.getId();
//    }

    /**
     * 修改申请表单板块
     *
     * @Date 16:29 2021/5/20
     * <AUTHOR>
     */
//    @Override
//    public AppFormDivisionVo updateAppFormDivision(AppFormDivisionDto appFormDivisionVo) {
//        if (GeneralTool.isEmpty(appFormDivisionVo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
//        }
////        Example example = new Example(AppFormDivision.class);
////        example.createCriteria().andEqualTo("name", appFormDivisionVo.getName()).andNotEqualTo("id", appFormDivisionVo.getId());
////        Example.Criteria criteria = example.createCriteria().andEqualTo("divisionKey", appFormDivisionVo.getDivisionKey()).andNotEqualTo("id", appFormDivisionVo.getId());
////        example.or(criteria);
////        List<AppFormDivision> appFormDivisions = appFormDivisionMapper.selectByExample(example);
//
//        LambdaQueryWrapper<AppFormDivision> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.ne(AppFormDivision::getId, appFormDivisionVo.getId());
//        lambdaQueryWrapper.and(wrapper -> wrapper.eq(AppFormDivision::getName, appFormDivisionVo.getName()).or().eq(AppFormDivision::getDivisionKey, appFormDivisionVo.getDivisionKey()));
//        List<AppFormDivision> appFormDivisions = appFormDivisionMapper.selectList(lambdaQueryWrapper);
//
//        if (GeneralTool.isNotEmpty(appFormDivisions)) {
//            throw new GetServiceException(ResultCode.INVALID_PARAM, LocaleMessageUtils.getMessage("type_name_exists"));
//        }
//        AppFormDivision appFormDivision = appFormDivisionMapper.selectById(appFormDivisionVo.getId());
//        if (GeneralTool.isEmpty(appFormDivision)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        }
//        appFormDivision = BeanCopyUtils.objClone(appFormDivisionVo, AppFormDivision::new);
//        utilService.updateUserInfoToEntity(appFormDivision);
//        appFormDivisionMapper.updateByPrimaryKeySelective(appFormDivision);
//        return findAppFormDivisionById(appFormDivision.getId());
//    }

    /**
     * 申请表单板块详情
     *
     * @Date 16:34 2021/5/20
     * <AUTHOR>
     */
//    @Override
//    public AppFormDivisionVo findAppFormDivisionById(Long id) {
//        if (GeneralTool.isEmpty(id)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
//        AppFormDivision appFormDivision = appFormDivisionMapper.selectById(id);
//        if (GeneralTool.isEmpty(appFormDivision)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        }
//        return BeanCopyUtils.objClone(appFormDivision, AppFormDivisionVo::new);
//    }

    /**
     * 删除申请表单板块
     *
     * @Date 16:38 2021/5/20
     * <AUTHOR>
     */
//    @Override
//    public void delete(Long id) {
//        if (GeneralTool.isEmpty(id)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
//        deleteService.deleteValidateAppFormDivision(id);
//        appFormDivisionMapper.deleteById(id);
//
//        //删除翻译内容
//        issueTranslationMapper.deleteTranslations(TableEnum.U_APP_FORM_DIVISION.key, id);
//    }

    /**
     * 上移下移
     *
     * @Date 17:04 2021/5/20
     * <AUTHOR>
     */
//    @Override
//    public void movingOrder(List<AppFormDivisionDto> appFormDivisionVos) {
//        if (GeneralTool.isEmpty(appFormDivisionVos)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
//        }
//        AppFormDivision ro = BeanCopyUtils.objClone(appFormDivisionVos.get(0), AppFormDivision::new);
//        Integer oneorder = ro.getViewOrder();
//        AppFormDivision rt = BeanCopyUtils.objClone(appFormDivisionVos.get(1), AppFormDivision::new);
//        Integer twoorder = rt.getViewOrder();
//        ro.setViewOrder(twoorder);
//        utilService.updateUserInfoToEntity(ro);
//        rt.setViewOrder(oneorder);
//        utilService.updateUserInfoToEntity(rt);
//        appFormDivisionMapper.updateByPrimaryKeySelective(ro);
//        appFormDivisionMapper.updateByPrimaryKeySelective(rt);
//    }

    /**
     * @Date 15:11 2021/5/26
     * <AUTHOR>
     */
//    @Override
//    public List<AppFormDivisionVo> getAppFormDivisionSelect() {
////        Example example = new Example(AppFormDivision.class);
////        example.orderBy("viewOrder").desc();
////        List<AppFormDivision> appFormDivisions = appFormDivisionMapper.selectByExample(example);
//        List<AppFormDivision> appFormDivisions = appFormDivisionMapper.selectList(Wrappers.<AppFormDivision>query().lambda().orderByDesc(AppFormDivision::getViewOrder));
//        return appFormDivisions.stream().map(appFormDivision -> BeanCopyUtils.objClone(appFormDivision, AppFormDivisionVo::new)).collect(Collectors.toList());
//    }


}
