package com.get.permissioncenter.controller;


import com.baomidou.mybatisplus.extension.api.ApiController;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.permissioncenter.dto.MInstitutionPermissionGroupDto;
import com.get.permissioncenter.dto.MInstitutionPermissionGroupSearchDto;
import com.get.permissioncenter.service.MInstitutionPermissionGroupService;
import com.get.permissioncenter.vo.MInstitutionPermissionGroupVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 学校权限组别管理
 */
@RestController
@RequestMapping("/permission/institutionPermissionGroup")
public class MInstitutionPermissionGroupController extends ApiController {

    @Resource
    private MInstitutionPermissionGroupService mInstitutionPermissionGroupService;

    /**
     * 分页查询学校权限组别管理数据
     */
    @ApiOperation(value = "分页查询接口", notes = "")
    @PostMapping("datas")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/学校权限管理/查询学校权限组别")
    public  ResponseBo<MInstitutionPermissionGroupVo> selectAll(@RequestBody SearchBean<MInstitutionPermissionGroupSearchDto> page) {
        List<MInstitutionPermissionGroupVo> datas = mInstitutionPermissionGroupService.getInstitutionPermissionGroups(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 新增数据
     *
     * @param mInstitutionPermissionGroupDto 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/学校权限管理/新增学校权限组别")
    @PostMapping("add")
    public ResponseBo insert(@RequestBody MInstitutionPermissionGroupDto mInstitutionPermissionGroupDto) {
        return SaveResponseBo.ok(mInstitutionPermissionGroupService.addMInstitutionPermissionGroup(mInstitutionPermissionGroupDto));
    }

    /**
     * 修改数据
     * @param mInstitutionPermissionGroupDto 实体对象
     * @return 修改结果
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/学校权限管理/更新学校权限组别")
    @PostMapping("update")
    public ResponseBo<MInstitutionPermissionGroupVo> update(@RequestBody MInstitutionPermissionGroupDto mInstitutionPermissionGroupDto) {
        return UpdateResponseBo.ok(mInstitutionPermissionGroupService.updateById(mInstitutionPermissionGroupDto));
    }

    /**
     * 删除数据
     *
     * @param id 主键
     * @return 删除结果
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DELETE, description = "权限中心/学校权限管理/删除学校权限组别")
    @GetMapping("delete")
    public ResponseBo delete(@RequestParam("id") Long id) {
        mInstitutionPermissionGroupService.removeById(id);
        return ResponseBo.ok();
    }

    /**
     * 排序
     * @param mInstitutionPermissionGroupDtos
     * @return
     */
    @ApiOperation(value = "排序接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/学校权限管理/排序")
    @PostMapping("sortInstitutionPermissionGroup")
    public ResponseBo sortInstitutionPermissionGroup(@RequestBody List<MInstitutionPermissionGroupDto> mInstitutionPermissionGroupDtos) {
        mInstitutionPermissionGroupService.sortInstitutionPermissionGroup(mInstitutionPermissionGroupDtos);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "拖拽", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/学校权限管理/拖拽")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestParam("start")Integer start, @RequestParam("end")Integer end) {
        mInstitutionPermissionGroupService.movingOrder(start,end);
        return ResponseBo.ok();
    }
}

