package com.get.permissioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.dao.PermissionGradeMapper;
import com.get.permissioncenter.dao.PermissionGroupGradeResourceMapper;
import com.get.permissioncenter.dao.PermissionGroupGradeStaffMapper;
import com.get.permissioncenter.dto.PermissionGradeDto;
import com.get.permissioncenter.vo.PermissionGradeVo;
import com.get.permissioncenter.entity.PermissionGrade;
import com.get.permissioncenter.entity.PermissionGroupGradeResource;
import com.get.permissioncenter.entity.PermissionGroupGradeStaff;
import com.get.permissioncenter.service.ICompanyService;
import com.get.permissioncenter.service.IPermissionGradeService;
import com.get.permissioncenter.utils.MyStringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: jack
 * @create: 2020/7/2
 * @verison: 1.0
 * @description: 权限等级业务实现类
 */
@Service
public class PermissionGradeServiceImpl extends BaseServiceImpl<PermissionGradeMapper, PermissionGrade> implements IPermissionGradeService {
    @Resource
    PermissionGradeMapper permissionGradeMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private ICompanyService companyService;
    @Resource
    private PermissionGroupGradeResourceMapper permissionGroupGradeResourceMapper;
    @Resource
    private PermissionGroupGradeStaffMapper permissionGroupGradeStaffMapper;

    @Override
    public PermissionGradeVo findPermissionGradeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
//        PermissionGrade permissionGrade = permissionGradeMapper.selectByPrimaryKey(id);
        PermissionGrade permissionGrade = permissionGradeMapper.selectById(id);
        if (GeneralTool.isEmpty(permissionGrade)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
//        PermissionGradeVo permissionGradeVo = Tools.objClone(permissionGrade, PermissionGradeVo.class);
        PermissionGradeVo permissionGradeVo = BeanCopyUtils.objClone(permissionGrade, PermissionGradeVo::new);
        permissionGradeVo.setCompanyName(companyService.getCompanyNameById(permissionGradeVo.getFkCompanyId()));
        return permissionGradeVo;
    }

    @Override
    public List<PermissionGradeVo> getPermissionGrades(PermissionGradeDto permissionGradeDto, Page page) {
//        Example example = new Example(PermissionGrade.class);
//        if (GeneralTool.isEmpty(permissionGradeDto)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
//        }
//        if (GeneralTool.isEmpty(permissionGradeDto.getFkCompanyId())) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("company_id_null"));
//        }
//        Long companyId = permissionGradeDto.getFkCompanyId();
//        Example.Criteria criteria = example.createCriteria();
//        Example.Criteria criteria1 = example.createCriteria();
//        if (GeneralTool.isNotEmpty(permissionGradeDto)) {
//            if (GeneralTool.isNotEmpty(permissionGradeDto.getKeyword())) {
//                criteria.andLike("gradeNum", "%" + permissionGradeDto.getKeyword() + "%");
//                criteria1.andLike("gradeName", "%" + permissionGradeDto.getKeyword() + "%");
//            }
//            if (GeneralTool.isNotEmpty(permissionGradeDto.getFkCompanyId())) {
//                criteria.andEqualTo("fkCompanyId", companyId);
//                criteria1.andEqualTo("fkCompanyId", companyId);
//            }
//            example.or(criteria1);
//        }
//        example.orderBy("viewOrder").desc();
//        //获取分页数据
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<PermissionGrade> permissionGrades = permissionGradeMapper.selectByExample(example);
//        PageInfo<PermissionGrade> pageInfo = new PageInfo<PermissionGrade>(permissionGrades);
//        page.setTotalResult(new Long(pageInfo.getTotal()).intValue());

        LambdaQueryWrapper<PermissionGrade> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isEmpty(permissionGradeDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(permissionGradeDto.getFkCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_id_null"));
        }
        Long companyId = permissionGradeDto.getFkCompanyId();
        if (GeneralTool.isNotEmpty(permissionGradeDto)) {
            if (GeneralTool.isNotEmpty(permissionGradeDto.getKeyword())) {
                wrapper.and(wrapper_ ->
                        wrapper_.like(PermissionGrade::getGradeNum, permissionGradeDto.getKeyword()).or()
                                .like(PermissionGrade::getGradeName, permissionGradeDto.getKeyword()));
            }
            if (GeneralTool.isNotEmpty(permissionGradeDto.getFkCompanyId())) {
                wrapper.eq(PermissionGrade::getFkCompanyId, companyId);
            }
        }
        wrapper.orderByDesc(PermissionGrade::getViewOrder);

        IPage<PermissionGrade> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<PermissionGrade> permissionGrades = pages.getRecords();
        page.setAll((int) pages.getTotal());

        List<PermissionGradeVo> convertDatas = new ArrayList<>();
        for (PermissionGrade permissionGrade : permissionGrades) {
            PermissionGradeVo permissionGradeVo = BeanCopyUtils.objClone(permissionGrade, PermissionGradeVo::new);
            permissionGradeVo.setCompanyName(companyService.getCompanyNameById(permissionGradeVo.getFkCompanyId()));
            convertDatas.add(permissionGradeVo);
        }
        return convertDatas;
    }

    @Override
    public PermissionGradeVo updatePermissionGrade(PermissionGradeDto permissionGradeDto) {
        if (permissionGradeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
//        PermissionGrade pg = selectByKey(permissionGradeDto.getId());
        PermissionGrade pg = this.getById(permissionGradeDto.getId());
        if (pg == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
//        PermissionGrade permissionGrade = Tools.objClone(permissionGradeDto, PermissionGrade.class);
        PermissionGrade permissionGrade = BeanCopyUtils.objClone(permissionGradeDto, PermissionGrade::new);
        if (validateUpdate(permissionGradeDto)) {
//            utilService.setUpdateInfo(permissionGrade);
//            permissionGradeMapper.updateByPrimaryKeySelective(permissionGrade);
            utilService.updateUserInfoToEntity(permissionGrade);
            permissionGradeMapper.updateById(permissionGrade);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        return findPermissionGradeById(permissionGrade.getId());
    }

    @Override
    public Long addPermissionGrade(PermissionGradeDto permissionGradeDto) {
        PermissionGrade permissionGrade = BeanCopyUtils.objClone(permissionGradeDto, PermissionGrade::new);
        if (validateAdd(permissionGradeDto)) {
            permissionGrade.setViewOrder(permissionGradeMapper.getMaxViewOrder());
//            utilService.setCreateInfo(permissionGrade);
            utilService.updateUserInfoToEntity(permissionGrade);
            permissionGradeMapper.insert(permissionGrade);
            permissionGrade.setGradeNum(MyStringUtils.getGradeNum(permissionGrade.getId()));
//            permissionGradeMapper.updateByPrimaryKeySelective(permissionGrade);
            permissionGradeMapper.updateById(permissionGrade);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        return permissionGrade.getId();
    }

    @Override
    public void delete(Long id) {
        //TODO 改过
        //PermissionGrade permissionGrade = findPermissionGradeById(id);
        PermissionGradeVo permissionGrade = findPermissionGradeById(id);
        if (permissionGrade == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
//        Example example = new Example(PermissionGroupGradeResource.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkPermissionGradeId", id);
//        List<PermissionGroupGradeResource> list = this.permissionGroupGradeResourceMapper.selectByExample(example);
//        if (GeneralTool.isNotEmpty(list)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("grade_resource_data_association"));
//        }
//        Example example1 = new Example(PermissionGroupGradeStaff.class);
//        Example.Criteria criteria1 = example1.createCriteria();
//        criteria1.andEqualTo("fkPermissionGradeId", id);
//        List<PermissionGroupGradeStaff> list1 = this.permissionGroupGradeStaffMapper.selectByExample(example1);

        List<PermissionGroupGradeResource> list = this.permissionGroupGradeResourceMapper.selectList(Wrappers.<PermissionGroupGradeResource>query().lambda().eq(PermissionGroupGradeResource::getFkPermissionGradeId, id));
        if (GeneralTool.isNotEmpty(list)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("grade_resource_data_association"));
        }
        List<PermissionGroupGradeStaff> list1 = this.permissionGroupGradeStaffMapper.selectList(Wrappers.<PermissionGroupGradeStaff>query().lambda().eq(PermissionGroupGradeStaff::getFkPermissionGradeId, id));
        if (GeneralTool.isNotEmpty(list1)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("grade_staff_data_association"));
        }
        permissionGradeMapper.deleteById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<PermissionGradeDto> permissionGradeDtos) {
        for (PermissionGradeDto permissionGradeDto : permissionGradeDtos) {
            if (GeneralTool.isEmpty(permissionGradeDto.getId())) {
                if (validateAdd(permissionGradeDto)) {
//                    PermissionGrade permissionGrade = Tools.objClone(permissionGradeDto, PermissionGrade.class);
                    PermissionGrade permissionGrade = BeanCopyUtils.objClone(permissionGradeDto, PermissionGrade::new);
                    permissionGrade.setViewOrder(permissionGradeMapper.getMaxViewOrder());
//                    utilService.setCreateInfo(permissionGrade);
                    utilService.updateUserInfoToEntity(permissionGrade);
                    permissionGradeMapper.insert(permissionGrade);
                    permissionGrade.setGradeNum(MyStringUtils.getGradeNum(permissionGrade.getId()));
//                    permissionGradeMapper.updateByPrimaryKeySelective(permissionGrade);
                    permissionGradeMapper.updateById(permissionGrade);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
                }
            } else {
//                PermissionGrade permissionGrade = Tools.objClone(permissionGradeDto, PermissionGrade.class);
                PermissionGrade permissionGrade = BeanCopyUtils.objClone(permissionGradeDto, PermissionGrade::new);
                if (validateUpdate(permissionGradeDto)) {
//                    utilService.setUpdateInfo(permissionGrade);
//                    permissionGradeMapper.updateByPrimaryKeySelective(permissionGrade);
                    utilService.updateUserInfoToEntity(permissionGrade);
                    permissionGradeMapper.updateById(permissionGrade);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
                }
            }

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<PermissionGradeDto> permissionGradeDtos) {
        if (GeneralTool.isEmpty(permissionGradeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
//        PermissionGrade ro = Tools.objClone(permissionGradeDtos.get(0), PermissionGrade.class);
//        Integer oneorder = ro.getViewOrder();
//        PermissionGrade rt = Tools.objClone(permissionGradeDtos.get(1), PermissionGrade.class);
//        Integer twoorder = rt.getViewOrder();
//        ro.setViewOrder(twoorder);
//        utilService.setUpdateInfo(ro);
//        rt.setViewOrder(oneorder);
//        utilService.setUpdateInfo(rt);
//        permissionGradeMapper.updateByPrimaryKeySelective(ro);
//        permissionGradeMapper.updateByPrimaryKeySelective(rt);
        PermissionGrade ro = BeanCopyUtils.objClone(permissionGradeDtos.get(0), PermissionGrade::new);
        Integer oneorder = ro.getViewOrder();
        PermissionGrade rt = BeanCopyUtils.objClone(permissionGradeDtos.get(1), PermissionGrade::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        permissionGradeMapper.updateById(ro);
        permissionGradeMapper.updateById(rt);
    }

    private boolean validateUpdate(PermissionGradeDto permissionGradeDto) {
//        Example example = new Example(PermissionGrade.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkCompanyId", permissionGradeDto.getFkCompanyId());
//        criteria.andEqualTo("gradeLevel", permissionGradeDto.getGradeLevel());
//        List<PermissionGrade> list = this.permissionGradeMapper.selectByExample(example);
        List<PermissionGrade> list = this.permissionGradeMapper.selectList(Wrappers.<PermissionGrade>query().lambda()
                .eq(PermissionGrade::getFkCompanyId, permissionGradeDto.getFkCompanyId())
                .eq(PermissionGrade::getGradeLevel, permissionGradeDto.getGradeLevel()));
        return list.size() <= 0 || list.get(0).getId().equals(permissionGradeDto.getId());
    }

    private boolean validateAdd(PermissionGradeDto permissionGradeDto) {
//        Example example = new Example(PermissionGrade.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkCompanyId", permissionGradeDto.getFkCompanyId());
//        criteria.andEqualTo("gradeLevel", permissionGradeDto.getGradeLevel());
//        List<PermissionGrade> list = this.permissionGradeMapper.selectByExample(example);
        List<PermissionGrade> list = this.permissionGradeMapper.selectList(Wrappers.<PermissionGrade>query().lambda()
                .eq(PermissionGrade::getFkCompanyId, permissionGradeDto.getFkCompanyId())
                .eq(PermissionGrade::getGradeLevel, permissionGradeDto.getGradeLevel()));
        return GeneralTool.isEmpty(list);
    }

    private boolean validateAdd(List<PermissionGradeDto> permissionGradeDtos) {
        boolean success = true;
        for (PermissionGradeDto permissionGradeDto : permissionGradeDtos) {
//            Example example = new Example(PermissionGrade.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andEqualTo("fkCompanyId", permissionGradeDto.getFkCompanyId());
//            criteria.andEqualTo("gradeLevel", permissionGradeDto.getGradeLevel());
//            List<PermissionGrade> list = this.permissionGradeMapper.selectByExample(example);

            List<PermissionGrade> list = this.permissionGradeMapper.selectList(Wrappers.<PermissionGrade>query().lambda()
                    .eq(PermissionGrade::getFkCompanyId, permissionGradeDto.getFkCompanyId())
                    .eq(PermissionGrade::getGradeLevel, permissionGradeDto.getGradeLevel()));

            if (!GeneralTool.isEmpty(list)) {
                success = false;
            }
        }
        return success;
    }

}
