package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2024/4/18
 * @TIME: 17:23
 * @Description:
 **/
@Data
public class KpiPlanStatisticsVo {

    @ApiModelProperty(value = "考核人员列表")
    private List<KpiPlanStaffVo> kpiPlanStaffDtoList;

    @ApiModelProperty(value = "考核方案组别或子项列表")
    private KpiPlanGroupOrItemStatisticsResultVo kpiPlanGroupOrItemStatisticsResultDto;

    @ApiModelProperty(value = "KPI方案总计")
    private KpiPlanAllStatisticsVo kpiPlanAllStatisticsDto;

    @ApiModelProperty(value = "组别总计")
    private List<KpiPlanGroupAllStatisticsVo> kpiPlanGroupAllStatisticsDtos;

    @ApiModelProperty(value = "代理排名")
    private List<KpiAgentRankVo> kpiAgentRankDtos;

    @ApiModelProperty(value = "是否能够进入KPI方案统计")
    private Boolean isKpiPlanStatistics = false;

    @ApiModelProperty(value = "目标设置人Id")
    private Long fkStaffIdAdd;

    @ApiModelProperty(value = "KPI方案数据时间戳之和")
    private String sumTimeKpiData;

    @ApiModelProperty(value = "最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date taskEndTime;

}
