package com.get.aisplatformcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.aisplatformcenterap.entity.ULabelEntity;
import com.get.aisplatformcenterap.vo.LabelAboutAgentVo;
import com.get.salecenter.dto.AgentLabelDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ULabelMapper extends BaseMapper<ULabelEntity> {

    Integer getMaxOrder();

    Integer getAgentLabel(@Param("labelId") Long labelId);

    List<LabelAboutAgentVo> getLabelByLabelTypeIdAndKeyWord(IPage<LabelAboutAgentVo> iPage, @Param("agentLabelVo")AgentLabelDto agentLabelVo);
}
