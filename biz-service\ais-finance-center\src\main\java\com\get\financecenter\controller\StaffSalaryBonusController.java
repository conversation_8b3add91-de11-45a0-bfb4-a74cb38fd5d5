package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.financecenter.dto.ReceiptFormItemDto;
import com.get.financecenter.dto.query.StaffSalaryBonusQueryDto;
import com.get.financecenter.entity.StaffSalaryBonus;
import com.get.financecenter.service.StaffSalaryBonusService;
import com.get.financecenter.vo.BankAccountVo;
import com.get.financecenter.vo.ImportSalaryBonusVo;
import com.get.financecenter.vo.ReceiptFormItemVo;
import com.get.financecenter.vo.StaffSalaryBonusVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Api(tags = "工资奖金管理")
@RestController
@RequestMapping("finance/salaryBonus")
public class StaffSalaryBonusController {

    @Resource
    private StaffSalaryBonusService staffSalaryBonusService;


    @ApiOperation(value = "工资/奖金文件下载")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/工资奖金管理/工资/奖金文件下载")
    @PostMapping("downloadTemplateFile")
    public void downloadTemplateFile(HttpServletResponse response,@RequestParam("type") String type) {
        staffSalaryBonusService.downloadTemplateFile(response, type);
    }


    @ApiOperation(value = "工资/奖金数据导入", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/工资奖金管理/工资/奖金数据导入")
    @PostMapping("importSalaryBonus")
    public ResponseBo<ImportSalaryBonusVo> importSalaryBonus(@RequestParam("file") MultipartFile file, @RequestParam("type") String type){
        ImportSalaryBonusVo importSalaryBonusVo = staffSalaryBonusService.importSalaryBonus(file, type);
        return new ResponseBo<>(importSalaryBonusVo);
    }

    @ApiOperation(value = "Excel模板解析", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/工资奖金管理/Excel模板解析")
    @PostMapping("parseExcelTemplate")
    public ResponseBo<StaffSalaryBonusVo> parseExcelTemplate(@RequestParam("file") MultipartFile file, @RequestParam("type") String type){
        List<StaffSalaryBonusVo> datas = staffSalaryBonusService.parseExcelTemplate(file,type);
        return new ListResponseBo<>(datas);
    }


    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/工资奖金管理/工资奖金")
    @PostMapping("datas")
    public ResponseBo<StaffSalaryBonusVo> datas(@RequestBody SearchBean<StaffSalaryBonusQueryDto> page) {
        List<StaffSalaryBonusVo> datas = staffSalaryBonusService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/工资奖金管理/配置详情")
    @GetMapping("/{id}")
    public ResponseBo<StaffSalaryBonusVo> detail(@PathVariable("id") Long id) {
        StaffSalaryBonusVo data = staffSalaryBonusService.findStaffSalaryBonusById(id);
        return new ResponseBo<>(data);
    }






}
