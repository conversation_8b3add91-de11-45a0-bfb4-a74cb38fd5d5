package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.redis.lock.RedisLock;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.financecenter.dto.VouchApplicationFormPaymentAddDto;
import com.get.financecenter.dto.VouchApplicationFormPaymentQueryDto;
import com.get.financecenter.service.VouchApplicationFormPaymentService;
import com.get.financecenter.vo.PaymentMethodTypeSelectEntity;
import com.get.financecenter.vo.VouchApplicationFormPaymentVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "付款列表")
@RestController
@RequestMapping("finance/vouchApplicationFormPayment")
public class VouchApplicationFormPaymentController {

    @Resource
    private VouchApplicationFormPaymentService vouchApplicationFormPaymentService;

    @ApiOperation("付款列表")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/申请表单管理/付款列表")
    @PostMapping("/vouchApplicationFormPaymentDatas")
    public ResponseBo<VouchApplicationFormPaymentVo> vouchApplicationFormPaymentDatas(@RequestBody @Validated SearchBean<VouchApplicationFormPaymentQueryDto> page) {
        List<VouchApplicationFormPaymentVo> vouchApplicationFormPaymentVoList = vouchApplicationFormPaymentService.vouchApplicationFormPaymentDatas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(vouchApplicationFormPaymentVoList, p);
    }

    @ApiOperation("创建付款记录")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/申请表单管理/创建付款记录")
    @PostMapping("/addVouchApplicationFormPayment")
    @RedisLock(value = "fzh:createVouch", waitTime = 10L)
    public ResponseBo addVouchApplicationFormPayment(@RequestBody @Validated VouchApplicationFormPaymentAddDto vouchApplicationFormPaymentAddDto) {
        vouchApplicationFormPaymentService.addVouchApplicationFormPayment(vouchApplicationFormPaymentAddDto);
        return ResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "付款方式下拉框", notes = "")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/申请表单管理/付款方式下拉框")
    @GetMapping("/paymentMethodTypeSelect")
    public ResponseBo<PaymentMethodTypeSelectEntity> paymentMethodTypeSelect() {
        return new ListResponseBo<>(vouchApplicationFormPaymentService.paymentMethodTypeSelect());
    }

    @ApiOperation(value = "作废付款记录", notes = "")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/申请表单管理/作废付款记录")
    @PostMapping("/cancelVouchApplicationFormPayment/{id}")
    public ResponseBo<PaymentMethodTypeSelectEntity> cancelVouchApplicationFormPayment(@PathVariable("id") Long id) {
        vouchApplicationFormPaymentService.cancelVouchApplicationFormPayment(id);
        return ResponseBo.ok();
    }


}
