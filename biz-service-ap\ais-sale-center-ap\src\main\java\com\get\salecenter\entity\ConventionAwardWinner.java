package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_convention_award_winner")
public class ConventionAwardWinner extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    @Column(name = "fk_convention_id")
    private Long fkConventionId;
    /**
     * 峰会奖品Id
     */
    @ApiModelProperty(value = "峰会奖品Id")
    @Column(name = "fk_convention_award_id")
    private Long fkConventionAwardId;
    /**
     * 峰会参展人员Id（中奖人）
     */
    @ApiModelProperty(value = "峰会参展人员Id（中奖人）")
    @Column(name = "fk_convention_person_id")
    private Long fkConventionPersonId;
    /**
     * 峰会抽奖号码Id
     */
    @ApiModelProperty(value = "峰会抽奖号码Id")
    @Column(name = "fk_convention_award_code_id")
    private Long fkConventionAwardCodeId;
}