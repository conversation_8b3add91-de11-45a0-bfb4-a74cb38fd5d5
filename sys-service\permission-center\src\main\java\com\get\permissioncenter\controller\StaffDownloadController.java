package com.get.permissioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.permissioncenter.vo.StaffDownloadVo;
import com.get.permissioncenter.service.StaffDownloadService;
import com.get.permissioncenter.dto.StaffDownloadDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Api(tags = "文件下载")
@RequestMapping("permission/staffDownload")
@RestController
public class StaffDownloadController<T> {

    @Resource
    private StaffDownloadService staffDownloadService;

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取下载列表")
    @OperationLogger(module = LoggerModulesConsts.FILECENTER, type = LoggerOptTypeConst.LIST, description = "文件中心/获取下载列表")
    @PostMapping("/getDownloadList")
    public ResponseBo<StaffDownloadVo> getDownloadList(@RequestBody SearchBean<StaffDownloadDto> page) {
        return new ListResponseBo<>(staffDownloadService.getDownloadList(page), BeanCopyUtils.objClone(page, Page::new));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取下载列表操作方式")
    @OperationLogger(module = LoggerModulesConsts.FILECENTER, type = LoggerOptTypeConst.LIST, description = "文件中心/获取下载列表操作方式")
    @GetMapping("/getOperation")
    public ResponseBo<Map<String, Object>> getOperation() {
        return new ListResponseBo<>(staffDownloadService.doGetSelection(FileTypeEnum.OPERATION));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取下载列表状态")
    @OperationLogger(module = LoggerModulesConsts.FILECENTER, type = LoggerOptTypeConst.LIST, description = "文件中心/获取下载列表状态")
    @GetMapping("/getStatusSelection")
    public ResponseBo<Map<String, Object>> getStatusSelection() {
        return new ListResponseBo<>(staffDownloadService.doGetSelection(FileTypeEnum.STATUS));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "删除下载记录")
    @OperationLogger(module = LoggerModulesConsts.FILECENTER, type = LoggerOptTypeConst.DELETE, description = "文件中心/删除下载记录")
    @GetMapping("/deleteRecord/{id}")
    public ResponseBo deleteRecord(@PathVariable Long id) {
        return staffDownloadService.deleteRecord(id);
    }


    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "删除所有下载记录")
    @OperationLogger(module = LoggerModulesConsts.FILECENTER, type = LoggerOptTypeConst.DELETE, description = "文件中心/删除所有下载记录")
    @GetMapping("/deleteAll")
    public ResponseBo deleteRecord() {
        return staffDownloadService.deleteAll();
    }

}
