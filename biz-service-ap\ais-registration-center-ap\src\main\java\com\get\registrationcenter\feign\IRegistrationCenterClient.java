package com.get.registrationcenter.feign;

import com.get.common.constant.AppCenterConstant;
import com.get.core.tool.api.Result;
import com.get.registrationcenter.entity.User;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.dto.ContactPersonDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * Feign接口类
 */
@FeignClient(
        value = AppCenterConstant.APPLICATION_REGISTRATION_CENTER
)
public interface IRegistrationCenterClient {

    String API_PREFIX = "/feign";

    String GET_USERS_BY_IDS = API_PREFIX + "/get-users-by-ids";

    String UPDATE_ISSUE_CONTACT_PERSON = API_PREFIX + "/update-issue-contact-person";

    String INSERT_AGENT_USER = API_PREFIX + "/insert-agent-user";

    String GET_USER_BY_AGENT_ID = API_PREFIX + "/get-user-by-agent-id";

    /**
     *
     * @param userIds
     * @return
     */
    @PostMapping(GET_USERS_BY_IDS)
    Result<List<User>> getUsersByIds(@RequestBody List<Long> userIds);

    @PostMapping(UPDATE_ISSUE_CONTACT_PERSON)
    Result<Boolean> updateIssueContactPerson(@RequestBody ContactPersonDto contactPersonVo);

    @PostMapping(INSERT_AGENT_USER)
    Result<Long> insertAgentUser(@RequestBody Agent agent);

    @PostMapping(GET_USER_BY_AGENT_ID)
    Result<User> getUserByAgentId(@RequestParam("agentId") Long agentId);
}
