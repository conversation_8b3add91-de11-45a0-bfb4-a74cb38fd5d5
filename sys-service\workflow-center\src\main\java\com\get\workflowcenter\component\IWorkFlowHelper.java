package com.get.workflowcenter.component;

import com.get.officecenter.vo.LeaveLogVo;
import com.get.officecenter.vo.LeaveStockVo;
import com.get.officecenter.dto.LeaveLogDto;
import com.get.officecenter.dto.LeaveStockDto;
import com.get.permissioncenter.vo.StaffVo;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.task.Task;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/11/18 15:41
 * @verison: 1.0
 * @description:
 */
public interface IWorkFlowHelper {

    /**
     * 发送提醒信息
     *
     * @param staffVo
     * @param staffIdList
     * @param processDefinition
     * @param status
     */
    void sendMessage(StaffVo staffVo, List<String> staffIdList, ProcessDefinition processDefinition, String status, DelegateTask task, String dbName, String remindKey);

    /**
     * 发送提醒信息
     *
     * @param staffVo
     * @param staffIdList
     * @param processDefinition
     * @param status
     */
    void sendMessage(StaffVo staffVo, List<String> staffIdList, ProcessDefinition processDefinition, String status, Task task, String dbName);

    /**
     * 获取开始员工信息
     *
     * @param task
     * @return
     */
    StaffVo getStaffDto(DelegateTask task);

    /**
     * 获取开始员工信息
     *
     * @param execution
     * @return
     */
    StaffVo getExecutionStaffDto(DelegateExecution execution);

    /**
     * 添加库存日志
     *
     * @param leaveLogDto
     * @return
     */
    void addSystemLeaveLog(LeaveLogDto leaveLogDto);

    /**
     * 获取库存
     *
     * @param leaveStockDto
     * @return
     */
    List<LeaveStockVo> getLeaveStockDtos(LeaveStockDto leaveStockDto);

    /**
     * 获取库存（在有效期内可等于0）
     *
     * @param leaveStockDto
     * @return
     */
    List<LeaveStockVo> getEfficientLeaveStockDtos(LeaveStockDto leaveStockDto);

    /**
     * 新增库存
     *
     * @param leaveStockDto
     * @
     */
    void addSystem(LeaveStockDto leaveStockDto);

    /**
     * 调整时长(工作流feign调用)
     *
     * @param leaveStockDto
     * @return
     * @
     */
    void updateSystemLeavetock(LeaveStockDto leaveStockDto);

    /**
     * 获取工休日志
     *
     * @param leaveLogDto
     * @return
     * @
     */
    List<LeaveLogVo> getSystemLeaveLog(@RequestBody LeaveLogDto leaveLogDto);
}
