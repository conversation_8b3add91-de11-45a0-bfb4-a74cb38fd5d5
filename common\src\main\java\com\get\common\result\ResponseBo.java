package com.get.common.result;

import com.get.common.eunms.ErrorCodeEnum;

import java.io.Serializable;


/**
 * 响应基类
 *
 * @author: jack
 * @create: 2020-4-15
 * @verison: 1.0
 * @description: 响应基类
 */

public class ResponseBo<T> implements Serializable {
    private static final long serialVersionUID = -8713837118340960775L;

    private static final boolean SUCCESS = true;
    private static final boolean FAIL = false;

    private static final String MESSAGE_VALUE = "message";  //string
    private static final String DATA_VALUE = "data";      //object
    private static final String CODE_VALUE = "code";         //int
    private static final String SUCCESS_CODE = ErrorCodeEnum.REQUEST_OK.getCode();  //boolean
    private static final String SUCCESS_VALUE = "success";  //boolean


    private String code;
    private String message;
    private Boolean success;
    private T data;
    private Page<T> page;

    //分页统计时间
    private long cTime;

    public ResponseBo() {
        this.code = SUCCESS_CODE;
        this.success = SUCCESS;
        this.message = "Successfully";
    }

    public ResponseBo(T data) {
        this.code = SUCCESS_CODE;
        this.success = SUCCESS;
        this.data = data;
    }
    public ResponseBo(T data,Page<T> page) {
        this.code = SUCCESS_CODE;
        this.success = SUCCESS;
        this.data = data;
        this.page = page;
    }

    public ResponseBo(T data, long time) {
        this.code = SUCCESS_CODE;
        this.success = SUCCESS;
        this.data = data;
        this.cTime = time;
    }

    public ResponseBo(T data,Page<T> page, long time) {
        this.code = SUCCESS_CODE;
        this.success = SUCCESS;
        this.data = data;
        this.page = page;
        this.cTime = time;
    }

    public Page<T> getPage() {
        return page;
    }

    public void setPage(Page<T> page) {
        this.page = page;
    }

    public ResponseBo(boolean success) {
        this.success = success;
    }

    public ResponseBo(String code, Boolean success, String message, T data) {
        this.code = code;
        this.success = success;
        this.message = message;
        this.data = data;
    }

    public static ResponseBo ok() {
        return new ResponseBo();
    }

    public static ResponseBo ok(ErrorCodeEnum e) {
        ResponseBo responseBo = new ResponseBo(SUCCESS);
        responseBo.setCode(e.getCode());
        responseBo.setMessage(e.getMessage());
        return responseBo;
    }

    public static ResponseBo ok(String code, String message) {
        ResponseBo responseBo = new ResponseBo(SUCCESS);
        responseBo.setCode(code);
        responseBo.setMessage(message);
        return responseBo;
    }

    public static ResponseBo error() {
        return new ResponseBo(FAIL);
    }

    public static ResponseBo error(String code, String message) {
        ResponseBo responseBo = new ResponseBo(FAIL);
        responseBo.setCode(code);
        responseBo.setMessage(message);
        return responseBo;
    }

    public static ResponseBo error(ErrorCodeEnum e) {
        ResponseBo responseBo = new ResponseBo(FAIL);
        responseBo.setCode(e.getCode());
        responseBo.setMessage(e.getMessage());
        return responseBo;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public void setcTime(long cTime) {
        this.cTime = cTime;
    }

    public long getcTime() {
        return cTime;
    }

    public ResponseBo put(String value, T data) {
        this.code = SUCCESS_CODE;
        this.success = SUCCESS;
        this.data = data;
        return this;
    }


}
