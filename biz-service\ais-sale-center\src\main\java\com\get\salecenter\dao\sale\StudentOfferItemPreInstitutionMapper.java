package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.StudentOfferItemPreInstitution;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface StudentOfferItemPreInstitutionMapper extends BaseMapper<StudentOfferItemPreInstitution> {
    int insert(StudentOfferItemPreInstitution record);

    int insertSelective(StudentOfferItemPreInstitution record);

    int updateById(StudentOfferItemPreInstitution record);

    int updateByPrimaryKey(StudentOfferItemPreInstitution record);
}