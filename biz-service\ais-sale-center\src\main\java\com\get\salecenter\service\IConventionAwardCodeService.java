package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.salecenter.vo.ConventionAwardCodeVo;
import com.get.salecenter.entity.ConventionAwardCode;
import com.get.salecenter.dto.ConventionAwardCodeDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2021/9/13
 * @TIME: 15:47
 * @Description:
 **/
public interface IConventionAwardCodeService {

    /**
     * @return void
     * @Description :批量新增
     * @Param [conventionAwardVos]
     * <AUTHOR>
     */
    void batchAdd(ConventionAwardCodeDto conventionAwardCodeDto);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return java.util.List<com.get.salecenter.vo.ConventionAwardCodeDto>
     * @Description :列表
     * @Param [conventionAwardCodeDto, page]
     * <AUTHOR>
     */
    List<ConventionAwardCodeVo> getConventionAwardCodes(ConventionAwardCodeDto conventionAwardCodeDto, Page page);


    /**
     * @Description: 根据名称模糊搜索抽奖号码ids
     * @Author: Jerry
     * @Date:16:17 2021/9/15
     */
    Set<Long> getConventionAwardCodesIdsByConventionAwardCodesName(String conventionAwardCodesName);

    /**
     * @Description: 根据抽奖号码ids获取对象
     * @Author: Jerry
     * @Date:16:59 2021/9/15
     */
    Map<Long, ConventionAwardCode> getConventionAwardCodesByConventionAwardCodesIds(Set<Long> conventionAwardCodesIds);

    /**
     * @Description: 角色和奖品id获取奖券
     * @Author: jack
     * @Date:16:51 2021/9/15
     */
    List<ConventionAwardCodeVo> getTicketsByGroleAndAwardId(String agrole, int award_id, String status);

    /**
     * @Description: 查询还没有用的奖券
     * @Author: jack
     * @Date:16:51 2021/9/23
     */
    List<ConventionAwardCode> getListTicketsNoUsedByAwardId(Long awardId, Long fkConventionId);


}
