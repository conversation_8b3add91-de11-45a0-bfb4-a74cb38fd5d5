package com.get.salecenter.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.get.core.mybatis.base.BaseEntity;
import lombok.Data;

/**
 * log_student_offer_item_offer_file_identify
 * <AUTHOR>
@Data
public class LogStudentOfferItemOfferFileIdentify extends BaseEntity implements Serializable {
    /**
     * 申请计划Offer附件识别日志Id
     */
    private Long id;

    /**
     * 公司Id
     */
    private Long fkCompanyId;

    /**
     * 学生Id
     */
    private Long fkStudentId;

    /**
     * 学生申请方案项目Id
     */
    private Long fkStudentOfferItemId;

    /**
     * 文件guid(文档中心)
     */
    private String fkFileGuid;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 开学时间
     */
    private Date openingTime;

    /**
     * 识别分数
     */
    private BigDecimal score;

    /**
     * 币种编号
     */
    private String fkCurrencyTypeNum;

    /**
     * 学费金额
     */
    private BigDecimal tuitionAmount;

    /**
     * 识别结果json
     */
    private String json;

    /**
     * 状态：0失败/1成功
     */
    private Integer status;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 创建用户(登录账号)
     */
    private String gmtCreateUser;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 修改用户(登录账号)
     */
    private String gmtModifiedUser;

    private static final long serialVersionUID = 1L;
}