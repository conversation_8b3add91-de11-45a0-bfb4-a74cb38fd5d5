package com.get.platformconfigcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.platformconfigcenter.vo.StudentInfoVo;
import com.get.platformconfigcenter.service.StudentInfoService;
import com.get.platformconfigcenter.dto.StudentInfoDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * Created by Jerry.
 * Time: 17:15
 * Date: 2021/7/9
 * Description:学生信息管理
 */
@Api(tags = "学生信息管理")
@RestController
@RequestMapping("/platform/studentinfo")
public class StudentInfoController {
    @Resource
    private StudentInfoService studentInfoService;

    /**
     * 学生信息管理列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "学生信息管理列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.LIST, description = "业务平台配置中心/学生信息管理/查询")
    @PostMapping("datas")
    public ResponseBo<StudentInfoVo> datas(@RequestBody SearchBean<StudentInfoDto> page) {
        List<StudentInfoVo> userInfoList = studentInfoService.getStudentInfoList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(userInfoList, p);
    }


    /**
     * 更新学生信息状态
     *
     * @param studentInfoDto
     * @return
     * @
     */
    @ApiOperation(value = "更新学生信息状态", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.EDIT, description = "业务平台配置中心/学生信息管理/更新学生信息状态")
    @PostMapping("update")
    public ResponseBo<StudentInfoVo> update(@RequestBody StudentInfoDto studentInfoDto) {
        studentInfoService.updateStudentInfoStatus(studentInfoDto);
        return UpdateResponseBo.ok();
    }


    /**
     * 学生信息管理详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "学生信息管理详情", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DETAIL, description = "业务平台配置中心/学生信息管理/学生信息管理详情")
    @GetMapping("/{id}")
    public ResponseBo<StudentInfoVo> detail(@PathVariable("id") Long id) {
        return new ResponseBo<>(studentInfoService.findStudentInfoById(id));
    }

    /**
     * 代理下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "代理下拉框数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.LIST, description = "业务平台配置中心/学生信息管理/代理下拉框数据")
    @PostMapping("agentSelectDatas")
    public ResponseBo<Map<String, Object>> agentSelectDatas() {
        List<Map<String, Object>> agentNamesAndIds = studentInfoService.agentSelectDatas();
        return new ListResponseBo<>(agentNamesAndIds);
    }
}
