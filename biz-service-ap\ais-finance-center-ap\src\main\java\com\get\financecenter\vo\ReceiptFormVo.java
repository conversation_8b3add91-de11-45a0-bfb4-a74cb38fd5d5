package com.get.financecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2020/12/22
 * @TIME: 16:14
 * @Description:
 **/
@Data
public class ReceiptFormVo extends BaseEntity {
    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;
    /**
     * 目标类型名称
     */
    @ApiModelProperty(value = "目标类型名称")
    private String fkTypeKeyName;
    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String fkCurrencyTypeName;
    /**
     * 付款银行
     */
    @ApiModelProperty(value = "收款银行")
    private String fkBankAccountName;
    /**
     * 目标名称
     */
    @ApiModelProperty(value = "目标名称")
    private String targetName;
    /**
     * 发票名称
     */
    @ApiModelProperty(value = "发票名称")
    private String invoiceName;

    /**
     * 收款金额（拆分/总）
     */
    @ApiModelProperty(value = "实收金额（拆分/总）")
    private BigDecimal amountReceipt;

    @ApiModelProperty(value = "实收手续费（拆分/总）")
    private BigDecimal itemServiceFee;

    @ApiModelProperty(value = "总绑定金额")
    private BigDecimal totalBindingAmount;

    /**
     * 实收手续费
     */
    @ApiModelProperty(value = "实收手续费")
    private BigDecimal receiptFee=BigDecimal.ZERO;

    /**
     * 汇率（折合应收币种汇率）
     */
    @ApiModelProperty(value = "汇率（折合应收币种汇率）")
    private BigDecimal exchangeRateReceivable;

    /**
     * 收款金额（折合应收币种金额）
     */
    @ApiModelProperty(value = "收款金额（折合应收币种金额）")
    private BigDecimal amountReceivable;

    /**
     * 汇率调整（可正可负，为平衡计算应收金额）
     */
    @ApiModelProperty(value = "汇率调整（可正可负，为平衡计算应收金额）")
    private BigDecimal amountExchangeRate;


    /**
     * 差额
     */
    @ApiModelProperty(value = "差额")
    private BigDecimal diffAmount;

    /**
     * 收款单余额
     */
    @ApiModelProperty(value = "收款单余额")
    private BigDecimal receiptFormBalance;

    /**
     * 下拉框名称（金额币种加摘要）
     */
    @ApiModelProperty(value = "下拉框名称（金额币种加摘要）")
    private String selectName;

    /**
     * 收款费用类型名称
     */
    @ApiModelProperty(value = "收款费用类型名称")
    private String fkReceiptFeeTypeName;

    /**
     * 收款费用类型key
     */
    @ApiModelProperty(value = "收款费用类型key")
    private String fkReceiptFeeTypeKey;

    @ApiModelProperty("计划id")
    private Long fkReceivablePlanId;

    @ApiModelProperty("计划对象id")
    private Long fkReceivablePlanTargetId;

    /**
     * 发票列表
     */
    @ApiModelProperty("发票列表")
    private List<Map<String,Object>> invoiceList;

    /**
     * 关联发票编号
     */
    @ApiModelProperty("关联发票编号")
    private String fkInvoiceNums;

    //===================实体类ReceiptForm=====================
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
    /**
     * 目标类型关键字，枚举：m_institution_provider学校供应商/m_student学生
     */
    @ApiModelProperty(value = "目标类型关键字，枚举：m_institution_provider学校供应商/m_student学生")
    private String fkTypeKey;
    /**
     * 对应记录Id
     */
    @ApiModelProperty(value = "对应记录Id")
    private Long fkTypeTargetId;
    /**
     * 银行帐号Id（公司）
     */
    @ApiModelProperty(value = "银行帐号Id（公司）")
    private Long fkBankAccountId;

    /**
     * 发票编号
     */
    @ApiModelProperty(value = "发票编号")
    private String fkInvoiceNum;
    /**
     * 收款单编号（系统生成）
     */
    @ApiModelProperty(value = "收款单编号（系统生成）")
    private String numSystem;
    /**
     * 收款单编号（凭证号）
     */
    @ApiModelProperty(value = "收款单编号（凭证号）")
    private String numBank;
    /**
     * 收款日期
     */
    @ApiModelProperty(value = "收款日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date receiptDate;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "银行实际到账金额")
    private BigDecimal amountBank;

    @ApiModelProperty(value = "银行实际手续费")
    private BigDecimal serviceFeeBank;
    /**
     * 收款总金额（到账金额）
     */
    @ApiModelProperty(value = "收款总金额（到账金额）")
    private BigDecimal amount;
    /**
     * 汇率（港币）
     */
    @ApiModelProperty(value = "汇率（港币）")
    private BigDecimal exchangeRateHkd;
    /**
     * 收款总金额（到账金额，港币）
     */
    @ApiModelProperty(value = "收款总金额（到账金额，港币）")
    private BigDecimal amountHkd;
    /**
     * 汇率（人民币）
     */
    @ApiModelProperty(value = "汇率（人民币）")
    private BigDecimal exchangeRateRmb;
    /**
     * 收款总金额（到账金额，人民币）
     */
    @ApiModelProperty(value = "收款总金额（到账金额，人民币）")
    private BigDecimal amountRmb;
    /**
     * 手续费
     */
    @ApiModelProperty(value = "手续费")
    private BigDecimal serviceFee;
    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;
    /**
     * 代理佣金结算状态：0待结算/1可结算，默认为0
     */
    @ApiModelProperty(value = "代理佣金结算状态：0待结算/1可结算，默认为0")
    private Integer settlementStatus;
    /**
     * 状态：0作废/1打开
     */
    @ApiModelProperty(value = "状态：0作废/1打开")
    private Integer status;
    /**
     * 旧数据财务id(gea)
     */
    @ApiModelProperty(value = "旧数据财务id(gea)")
    private String idGeaFinance;

    @ApiModelProperty(value = "实收汇率")
    private BigDecimal exchangeRate;

}
