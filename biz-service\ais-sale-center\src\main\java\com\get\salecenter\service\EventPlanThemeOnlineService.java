package com.get.salecenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.core.mybatis.utils.ValidList;
import com.get.salecenter.vo.EventPlanThemeOnlineVo;
import com.get.salecenter.entity.EventPlanThemeOnline;
import com.get.salecenter.dto.EventPlanThemeOnlineDto;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
public interface EventPlanThemeOnlineService extends BaseService<EventPlanThemeOnline> {

    /**
     * 列表数据
     * <AUTHOR>
     * @DateTime 2023/12/14 10:43
     */
    List<EventPlanThemeOnlineVo> getEventPlanThemeOnlines(Long fkEventPlanId);

    /**
     * 根据主题获取线下活动列表数据
     * <AUTHOR>
     * @DateTime 2023/12/14 17:56
     */
    List<EventPlanThemeOnlineVo> getOnlinesByThemeId(Long fkEventPlanThemeId);

    /**
     * 激活
     * <AUTHOR>
     * @DateTime 2023/12/25 15:56
     */
    void activate(EventPlanThemeOnlineDto onlineVo);

    /**
     * 批量新增
     * <AUTHOR>
     * @DateTime 2023/12/14 16:33
     */
    void batchAdd(ValidList<EventPlanThemeOnlineDto> onlineVos);

    /**
     * 删除
     * <AUTHOR>
     * @DateTime 2023/12/14 10:58
     */
    void delete(Long id);


    /**
     * 移动
     * <AUTHOR>
     * @DateTime 2023/12/14 11:02
     */
    void movingOrder(Long fkEventPlanThemeId,Integer start,Integer end);

}
