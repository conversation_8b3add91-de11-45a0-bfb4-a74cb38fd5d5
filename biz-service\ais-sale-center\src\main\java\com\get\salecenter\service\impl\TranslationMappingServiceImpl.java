package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.ConventionProcedureMapper;
import com.get.salecenter.dao.sale.TranslationMapper;
import com.get.salecenter.dao.sale.TranslationMappingMapper;
import com.get.salecenter.vo.TranslationMappingVo;
import com.get.salecenter.entity.ConventionProcedure;
import com.get.salecenter.entity.Translation;
import com.get.salecenter.entity.TranslationMapping;
import com.get.salecenter.service.TranslationMappingService;
import com.get.salecenter.strategy.TranslationBeanStrategy;
import com.get.salecenter.dto.TranslationDto;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-04
 */
@Service
public class TranslationMappingServiceImpl extends BaseServiceImpl<TranslationMappingMapper, TranslationMapping> implements TranslationMappingService {

    @Resource
    private TranslationMappingMapper translationMappingMapper;

    @Resource
    private TranslationMapper translationMapper;

    @Resource
    private ConventionProcedureMapper conventionProcedureMapper;

    @Resource
    private UtilService utilService;

    @Resource(name = "translationBeanStrategiesMap")
    public Map<String, TranslationBeanStrategy> translationBeanStrategiesMap;

    @Override
    public List<TranslationMappingVo> getTranslationMappingDtos(TranslationDto translationDto) {
        if (GeneralTool.isEmpty(translationDto.getType()) || GeneralTool.isEmpty(translationDto.getLanguageCode())
                || GeneralTool.isEmpty(translationDto.getFkTableName())|| GeneralTool.isEmpty(translationDto.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        LambdaQueryWrapper<TranslationMapping> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(translationDto)) {
            if (GeneralTool.isNotEmpty(translationDto.getFkTableName())) {
                wrapper.eq(TranslationMapping::getFkTableName, translationDto.getFkTableName());
            }
        }
        wrapper.orderByDesc(TranslationMapping::getViewOrder);
        Map<String, Object> map = Maps.newHashMap();
        //根据策略获取javaBean
        TranslationBeanStrategy strategy = translationBeanStrategiesMap.get(translationDto.getFkTableName());
        if (GeneralTool.isNotEmpty(strategy)) {
            map = strategy.fromJavaBean(translationDto.getFkTableId(), this);
        }
//        Map<String, Object> map = fromJavaBean(translationDto.getFkTableName(), translationDto.getFkTableId());
        List<TranslationMapping> translationMappings = translationMappingMapper.selectList(wrapper);
        List<TranslationMappingVo> translationMappingVos = translationMappings.stream().map(TranslationMapping -> BeanCopyUtils.objClone(TranslationMapping, TranslationMappingVo::new)).collect(Collectors.toList());
        if (translationDto.getType() == 1 && translationDto.getLanguageCode().equals(ProjectKeyEnum.ZH_CN.key)) {
            for (TranslationMappingVo translationMappingVo : translationMappingVos) {
                for (String key : map.keySet()) {
                    if (key.equals(translationMappingVo.getFkColumnName())) {
                        /*translationMappingVo.setStandardContent(map.get(key).toString());
                        translationDto.setFkTranslationMappingId(translationMappingVo.getId());
                        translationMappingVo.setTranslationContent(translationMapper.getTranslation(translationDto));*/
                        translationDto.setFkTranslationMappingId(translationMappingVo.getId());
                        if (GeneralTool.isNotEmpty(map.get(key))) {
                            translationMappingVo.setTranslationContent(map.get(key).toString());
                        }
                    }
                }
            }
        } else {
            for (TranslationMappingVo translationMappingVo : translationMappingVos) {
                for (String key : map.keySet()) {
                    if (key.equals(translationMappingVo.getFkColumnName())) {
                        translationDto.setFkTranslationMappingId(translationMappingVo.getId());
                        translationMappingVo.setTranslationContent(translationMapper.getTranslation(translationDto));
                    }
                }
            }
        }
        return translationMappingVos;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateTranslations(List<TranslationDto> translationDtos) {
        if (GeneralTool.isEmpty(translationDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (translationDtos.get(0).getLanguageCode().equals(ProjectKeyEnum.ZH_CN.key)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("Simplified_Chinese_not_translation"));
        }
        for (TranslationDto translationDto : translationDtos) {
            LambdaQueryWrapper<Translation> wrapper = new LambdaQueryWrapper();
            if (GeneralTool.isNotEmpty(translationDto)) {
                if (GeneralTool.isNotEmpty(translationDto.getFkTableName())) {
                    wrapper.eq(Translation::getFkTableName, translationDto.getFkTableName());
                }
                if (GeneralTool.isNotEmpty(translationDto.getFkTableId())) {
                    wrapper.eq(Translation::getFkTableId, translationDto.getFkTableId());
                }
                if (GeneralTool.isNotEmpty(translationDto.getLanguageCode())) {
                    wrapper.eq(Translation::getLanguageCode, translationDto.getLanguageCode());
                }
                if (GeneralTool.isNotEmpty(translationDto.getFkTranslationMappingId())) {
                    wrapper.eq(Translation::getFkTranslationMappingId, translationDto.getFkTranslationMappingId());
                }
            }
            int i = translationMapper.delete(wrapper);
            if (i < 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
            }
            Translation translation = BeanCopyUtils.objClone(translationDto, Translation::new);
            utilService.updateUserInfoToEntity(translation);
            translationMapper.insert(translation);
        }
    }

    /**
     * 删除翻译内容
     *
     * @Date 12:11 2021/8/17
     * <AUTHOR>
     */
    @Override
    public void deleteTranslations(String fkTableName, Long fkTableId) {
        LambdaQueryWrapper<Translation> wrapper = new LambdaQueryWrapper();
        wrapper.eq(Translation::getFkTableName, fkTableName).eq(Translation::getFkTableId, fkTableId);
        translationMapper.delete(wrapper);
    }

    /**
     * 获取流程翻译内容
     * @param fkTableId
     * @return
     */
    @Override
    public Map<String, Object> getConventionProcedureTranslation(Long fkTableId) {
        if (GeneralTool.isEmpty(fkTableId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Map<String, Object> map = null;
        ConventionProcedure conventionProcedure = conventionProcedureMapper.selectById(fkTableId);
        if (GeneralTool.isEmpty(conventionProcedure)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        map = BeanCopyUtils.setConditionMap(conventionProcedure);
        return map;
    }
}
