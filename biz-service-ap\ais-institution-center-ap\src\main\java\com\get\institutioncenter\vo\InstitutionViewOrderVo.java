package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/6/8 18:13
 */
@Data
public class InstitutionViewOrderVo extends BaseEntity {

    @ApiModelProperty
    private String institutionName;

    @ApiModelProperty
    private String typeName;

    //=============实体类InstitutionViewOrder================
    /**
     * 排序方式枚举：学校资讯小程序=0
     */
    @ApiModelProperty(value = "排序方式枚举：学校资讯小程序=0")
    @Column(name = "type")
    private Integer type;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;

    private static final long serialVersionUID = 1L;

}

