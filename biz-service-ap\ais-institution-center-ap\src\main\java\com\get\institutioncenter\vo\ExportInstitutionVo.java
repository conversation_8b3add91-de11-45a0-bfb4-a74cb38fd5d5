package com.get.institutioncenter.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@ApiModel("学校导出类")
@Data
public class ExportInstitutionVo {

    @ApiModelProperty(value = "所属公司")
    private String providerInstitutionCompanyName;

    @ApiModelProperty(value = "编号")
    private String num;
    /**
     * 国家Name
     */
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;

    @ApiModelProperty(value = "名称")
    private String fullName;

    @ApiModelProperty(value = "学校类型名称")
    private String fkInstitutionTypeName;

    @ApiModelProperty(value = "课程数")
    private Integer courseCount;

    @ApiModelProperty(value = "合作关系")
    private String isBindingActive;


}
