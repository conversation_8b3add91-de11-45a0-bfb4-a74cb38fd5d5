package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.InstitutionProviderCompany;
import com.get.permissioncenter.vo.CompanyVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface InstitutionProviderCompanyMapper extends BaseMapper<InstitutionProviderCompany> {
    @Override
    int insert(InstitutionProviderCompany record);

    int insertSelective(InstitutionProviderCompany record);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据合同id查询所属提供商对应的公司ids
     * @Param [contractId]
     * <AUTHOR>
     */
    List<Long> getCompanyIdByContractId(Long contractId);

    List<CompanyVo> getCompanyNameById(@Param("id") Long id, @Param("companyIds") List<Long> companyIds);

    /**
     * 删除数据
     *
     * @return
     */
    void deleteByProviderId(@Param("id") Long id);

    List<InstitutionProviderCompany> getRelationsByProviderIds(@Param("providerIds") Set<Long> providerIds);
}