package com.get.votingcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description:媒体附件VO
 * @Param
 * @Date 10:40 2021/5/12
 * <AUTHOR>
 */
@Data
public class MediaAndAttachedDto extends BaseVoEntity implements Serializable {
    @NotBlank(message = "文件路径不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "文件路径", required = true)
    private String filePath;
    /**
     * 文件外部存储Key（如：腾讯云COS）
     */
    @ApiModelProperty(value = "文件外部存储Key（如：腾讯云COS）")
    private String fileKey;
    /**
     * 源文件名
     */
    @NotBlank(message = "文件名不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "源文件名", required = true)
    private String fileNameOrc;


    /**
     * 文件guid(文档中心)
     */
    @NotBlank(message = "文件guid不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "文件guid(文档中心)", required = true)
    private String fkFileGuid;

    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;

    /**
     * 表Id
     */
    @NotNull(message = "表Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "表Id", required = true)
    private Long fkTableId;

    /**
     * 类型关键字，如：voting_item_option_pic
     */
    @NotBlank(message = "类型关键字不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "类型关键字，如：voting_item_option_pic", required = true)
    private String typeKey;

    /**
     * 索引值(默认从0开始，同一类型下值唯一)
     */
    @ApiModelProperty(value = "索引值(默认从0开始，同一类型下值唯一)")
    private Integer indexKey;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 链接
     */
    @ApiModelProperty(value = "链接")
    private String link;
}
