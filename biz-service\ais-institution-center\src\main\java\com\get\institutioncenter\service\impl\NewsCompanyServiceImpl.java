package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.NewsCompanyMapper;
import com.get.institutioncenter.entity.NewsCompany;
import com.get.institutioncenter.service.INewsCompanyService;
import com.get.institutioncenter.dto.NewsCompanyDto;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/11/19
 * @TIME: 10:39
 * @Description: 新闻公司关联
 **/
@Service
public class NewsCompanyServiceImpl extends BaseServiceImpl<NewsCompanyMapper, NewsCompany> implements INewsCompanyService {
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private NewsCompanyMapper newsCompanyMapper;

    @Override
    public List<CompanyTreeVo> getNewCompanyRelation(Long newId) {
        if (GeneralTool.isEmpty(newId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //获取公司
        List<CompanyTreeVo> companyTreeVo = getCompanyTreeDto();
        if (GeneralTool.isEmpty(companyTreeVo)) {
            return null;
        }
        List<NewsCompany> relation = getRelationByNewId(newId);
        setContactFlag(companyTreeVo, relation);
        return getTreeList(companyTreeVo);
    }

    @Override
    public void editNewsCompany(List<NewsCompanyDto> newsCompanyDtos) {
        if (GeneralTool.isEmpty(newsCompanyDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //删除之前的记录
        List<Long> companyIds = SecureUtil.getCompanyIds();
        LambdaQueryWrapper<NewsCompany> wrapper = new LambdaQueryWrapper();
        wrapper.eq(NewsCompany::getFkNewsId, newsCompanyDtos.get(0).getFkNewsId()).in(NewsCompany::getFkCompanyId,companyIds);
        newsCompanyMapper.delete(wrapper);

        List<NewsCompany> collect = newsCompanyDtos.stream().map(newsCompanyVo ->
                BeanCopyUtils.objClone(newsCompanyVo, NewsCompany::new)).collect(Collectors.toList());
        collect.forEach(newsCompany -> newsCompanyMapper.insertSelective(newsCompany));
    }

    @Override
    public Map<Long, Set<Long>> getCompanyIdsByNewIds(Set<Long> ids) {
        Map<Long, Set<Long>> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        LambdaQueryWrapper<NewsCompany> wrapper = new LambdaQueryWrapper();
        wrapper.in(NewsCompany::getFkNewsId, ids);
        List<NewsCompany> newsCompanies = newsCompanyMapper.selectList(wrapper);
        if (GeneralTool.isEmpty(newsCompanies)) {
            return map;
        }
        for (NewsCompany newsCompany : newsCompanies) {
            //如果集合包含这个新闻id,则往原来的数据添加公司id
            if (map.containsKey(newsCompany.getFkNewsId())) {
                Set<Long> beforeCompanyIds = map.get(newsCompany.getFkNewsId());
                beforeCompanyIds.add(newsCompany.getFkCompanyId());
                map.put(newsCompany.getFkNewsId(), beforeCompanyIds);
                continue;
            }
            //如果没有包含,则往里面添加新数据
            Set<Long> companyIdsSet = new HashSet<>();
            companyIdsSet.add(newsCompany.getFkCompanyId());
            map.put(newsCompany.getFkNewsId(), companyIdsSet);
        }
        return map;
    }

    private List<CompanyTreeVo> getCompanyTreeDto() {
        Result<List<CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        return result.getData();
    }

    private List<NewsCompany> getRelationByNewId(Long newId) {
        LambdaQueryWrapper<NewsCompany> wrapper = new LambdaQueryWrapper();
        wrapper.eq(NewsCompany::getFkNewsId, newId);
        return newsCompanyMapper.selectList(wrapper);
    }

    private void setContactFlag(List<CompanyTreeVo> companyTreeVo, List<NewsCompany> relation) {
        for (CompanyTreeVo treeDto : companyTreeVo) {
            for (NewsCompany newsCompany : relation) {
                if (treeDto.getId().longValue() == (newsCompany.getFkCompanyId().longValue())) {
                    treeDto.setFlag(true);
                }
            }
        }
    }

    private List<CompanyTreeVo> getTreeList(List<CompanyTreeVo> entityList) {
        List<CompanyTreeVo> resultList = new ArrayList<>();
        // 获取顶层元素集合
        String parentId;
        for (CompanyTreeVo entity : entityList) {
            parentId = String.valueOf(entity.getFkParentCompanyId());
            if (parentId == null || "0".equals(parentId)) {
                //获取父节点的部门信息
                resultList.add(entity);
            }
        }
        //假如没有父节点
        if (GeneralTool.isEmpty(resultList)) {
            //获取最小节点
            CompanyTreeVo minTreeNode = entityList.stream().min(Comparator.comparing(CompanyTreeVo::getFkParentCompanyId)).get();
            resultList.add(minTreeNode);
            if (GeneralTool.isNotEmpty(minTreeNode)) {
                //获取相同的最小节点
                List<CompanyTreeVo> minTreeNodes = entityList.stream().filter(treeDto ->
                        treeDto.getFkParentCompanyId().equals(minTreeNode.getFkParentCompanyId()) &&
                                treeDto.getId().longValue() != minTreeNode.getId().longValue()).distinct().collect(Collectors.toList());
                resultList.addAll(minTreeNodes);
            }
        }
        // 获取每个顶层元素的子数据集合
        for (CompanyTreeVo entity : resultList) {
            entity.setChildList(getSubList(entity.getId(), entityList));
        }
        return resultList;
    }

    private List<CompanyTreeVo> getSubList(Long id, List<CompanyTreeVo> entityList) {
        List<CompanyTreeVo> childList = new ArrayList<>();
        // 子集的直接子对象
        for (CompanyTreeVo entity : entityList) {
            if (id.longValue() == entity.getFkParentCompanyId().longValue()) {
                //获取子节点的部门信息
                childList.add(entity);
            }
        }
        // 子集的间接子对象
        for (CompanyTreeVo entity : childList) {
            //递归调用
            entity.setChildList(getSubList(entity.getId(), entityList));
        }
        // 递归退出条件
        if (childList.size() == 0) {
            return null;
        }
        return childList;
    }


}
