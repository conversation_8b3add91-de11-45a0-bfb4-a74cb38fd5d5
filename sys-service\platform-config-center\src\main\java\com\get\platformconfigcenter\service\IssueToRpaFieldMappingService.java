package com.get.platformconfigcenter.service;

import com.get.common.result.Page;
import com.get.platformconfigcenter.vo.IssueToRpaFieldMappingVo;
import com.get.platformconfigcenter.dto.IssueToRpaFieldMappingDto;

import java.util.List;

/**
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　　　◆　　　　　　　　　　　　◆　　　　　　　　　　　　　　◆　　　　　　　　　　　　　◆◆　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　◆◆◆　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆
 * 　　　◆◆　　　　　◆◆　　　　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆◆◆◆
 * 　　　◆◆◆　　　◆◆◆　　　　　　　◆◆　　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆◆
 * 　　　　◆◆◆　◆◆◆　　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆
 * 　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　　◆◆
 * 　　　　　　◆◆◆　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆
 * 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆◆
 * <p>
 * Time: 11:01
 * Date: 2021/11/8
 * Description:数据字段映射管理业务类
 */
public interface IssueToRpaFieldMappingService {

    //TODO 注释ISSUE相关功能 lucky  2024/12/23
//    /**
//     * @Description: 数据字段映射列表数据
//     * @Author: Jerry
//     * @Date:11:04 2021/11/8
//     */
//    List<IssueToRpaFieldMappingVo> datas(IssueToRpaFieldMappingDto issueToRpaFieldMappingDto, Page page);
//
//
//    /**
//     * @Description: 新增数据字段映射
//     * @Author: Jerry
//     * @Date:11:05 2021/11/8
//     */
//    void add(IssueToRpaFieldMappingDto issueToRpaFieldMappingDto);
//
//    /**
//     * @Description: 更新数据字段映射
//     * @Author: Jerry
//     * @Date:11:05 2021/11/8
//     */
//    void update(IssueToRpaFieldMappingDto issueToRpaFieldMappingDto);
//
//    /**
//     * @Description: 数据字段映射详情
//     * @Author: Jerry
//     * @Date:11:05 2021/11/8
//     */
//    IssueToRpaFieldMappingVo detail(Long id);
//
//    /**
//     * @Description: 删除数据字段映射
//     * @Author: Jerry
//     * @Date:11:06 2021/11/8
//     */
//    void delete(Long id);
}
