package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class ConventionExamPrizesDto  extends BaseVoEntity {

    @ApiModelProperty("峰会Id")
    private Long fkConventionId;

    @ApiModelProperty("考试场次")
    private Long fkExaminationId;

    @ApiModelProperty("奖品类型")
    private Integer prizeType;

    @ApiModelProperty("是否已经兑换：0否/1是")
    private Boolean isExchange;

    @ApiModelProperty("获奖人员名称")
    private String fkConventionPersonName;

    @ApiModelProperty("bd名称")
    private String bdName;
   
}
