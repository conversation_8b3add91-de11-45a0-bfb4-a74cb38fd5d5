<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.LogStudentOfferItemOfferFileIdentifyMapper">
  <resultMap id="BaseResultMap" type="com.get.salecenter.entity.LogStudentOfferItemOfferFileIdentify">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_company_id" jdbcType="BIGINT" property="fkCompanyId" />
    <result column="fk_student_id" jdbcType="BIGINT" property="fkStudentId" />
    <result column="fk_student_offer_item_id" jdbcType="BIGINT" property="fkStudentOfferItemId" />
    <result column="fk_file_guid" jdbcType="VARCHAR" property="fkFileGuid" />
    <result column="course_name" jdbcType="VARCHAR" property="courseName" />
    <result column="opening_time" jdbcType="VARCHAR" property="openingTime" />
    <result column="fk_currency_type_num" jdbcType="VARCHAR" property="fkCurrencyTypeNum" />
    <result column="tuition_amount" jdbcType="DECIMAL" property="tuitionAmount" />
    <result column="json" jdbcType="VARCHAR" property="json" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>


</mapper>