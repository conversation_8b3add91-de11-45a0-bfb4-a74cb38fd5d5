package com.get.partnercenter.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName; 
import java.time.LocalDateTime;


/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-02-20 09:46:16
 */
@Data
@TableName("m_student_offer_item_agent_confirm")
public class MStudentOfferItemAgentConfirmEntity extends Model<MStudentOfferItemAgentConfirmEntity>{

  @ApiModelProperty("申请计划名单确认Id")
  private Long id;
 

  @ApiModelProperty("租户Id")
  private Long fkTenantId;
 

  @ApiModelProperty("学生代理Id")
  private Long fkAgentId;
 

  @ApiModelProperty("学生申请计划Id")
  private Long fkStudentOfferItemId;
 

  @ApiModelProperty("伙伴用户Id（确认人Id）")
  private Long fkPartnerUserId;
 

  @ApiModelProperty("是否系统自动确认：0否/1是")
  private Boolean isSystemConfirmed;
 

  @ApiModelProperty("创建时间")
  private LocalDateTime gmtCreate;
 

  @ApiModelProperty("创建用户(登录账号)")
  private String gmtCreateUser;
 

  @ApiModelProperty("修改时间")
  private LocalDateTime gmtModified;
 

  @ApiModelProperty("修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
