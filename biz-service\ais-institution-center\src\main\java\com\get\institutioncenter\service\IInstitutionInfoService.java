package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.dto.InstitutionInfoDto;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.vo.InstitutionInfoVo;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.entity.InstitutionInfo;
import com.get.institutioncenter.dto.query.InstitutionInfoQueryDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/8/11
 * @TIME: 10:22
 * @Description:
 **/
public interface IInstitutionInfoService extends BaseService<InstitutionInfo> {
    /**
     * 列表数据
     *
     * @param institutionInfoVo
     * @param page
     * @return
     */
    List<InstitutionInfoVo> datas(InstitutionInfoQueryDto institutionInfoVo, Page page);

    /**
     * 保存
     *
     * @param institutionInfoDto
     * @return
     */
    Long addInstitutionInfo(InstitutionInfoDto institutionInfoDto);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    InstitutionInfoVo findInstitutionInfoById(Long id);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);


    /**
     * 修改
     *
     * @param institutionInfoDto
     * @return
     */
    InstitutionInfoVo updateInstitutionInfo(InstitutionInfoDto institutionInfoDto);

    /**
     * 保存附件
     *
     * @param mediaAttachedVo
     * @return
     */
    List<MediaAndAttachedVo> addInstitutionInfoMedia(List<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * 获取附件类型
     *
     * @return
     */
    List<Map<String, Object>> findMediaAndAttachedType();

    /**
     * 获取公开对象下拉框
     *
     * @return
     */
    List<Map<String, Object>> getPublicObjectsSelect();

    /**
     * 查询附件
     *
     * @param data
     * @param page
     * @return
     * @
     */
    List<MediaAndAttachedVo> getInstitutionInfoMedia(MediaAndAttachedDto data, Page page);
}
