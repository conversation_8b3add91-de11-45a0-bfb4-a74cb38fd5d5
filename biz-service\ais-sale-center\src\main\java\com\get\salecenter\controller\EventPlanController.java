package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.vo.InstitutionProviderVo;
import com.get.salecenter.vo.EventPlanVo;
import com.get.salecenter.vo.EventPlanFormVo;
import com.get.salecenter.service.EventPlanService;
import com.get.salecenter.dto.EventPlanSearchDto;
import com.get.salecenter.dto.EventPlanDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  前端接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */

@Api(tags = "活动年度计划管理")
@RestController
@RequestMapping("sale/eventPlan")
@VerifyPermission(IsVerify = false)
public class EventPlanController {
    @Resource
    private EventPlanService eventPlanService;

    @ApiOperation(value = "活动年度计划表单")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/活动年度计划/活动年度计划表单")
    @PostMapping("/form")
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    public ResponseBo<EventPlanFormVo> getForm(@RequestParam("fkEventPlanId") Long fkEventPlanId,
                                               @RequestParam(value = "fkEventPlanRegistrationId",required = false) Long fkEventPlanRegistrationId) {
        return new ResponseBo(eventPlanService.getForm(fkEventPlanId,fkEventPlanRegistrationId));
    }


    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动年度计划/列表数据")
    @PostMapping("datas")
    public ResponseBo<EventPlanVo> datas(@RequestBody SearchBean<EventPlanSearchDto> page) {
        List<EventPlanVo> datas = eventPlanService.getEventPlans(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page,Page::new);
        return new ListResponseBo<>(datas,p);
    }

    @ApiOperation(value = "详情", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/活动年度计划/详情")
    @GetMapping("/{id}")
    public ResponseBo<EventPlanVo> detail(@PathVariable("id") Long id) {
        EventPlanVo data = eventPlanService.findEventPlanById(id);
        return new ResponseBo<>(data);
    }

    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/活动年度计划/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(EventPlanDto.Add.class)  EventPlanDto vo) {
        return SaveResponseBo.ok(eventPlanService.addEventPlan(vo));
    }

    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/活动年度计划/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        eventPlanService.delete(id);
        return DeleteResponseBo.ok();
    }

    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动年度计划/更新")
    @PostMapping("update")
    public ResponseBo<EventPlanVo> update(@RequestBody  @Validated(EventPlanDto.Update.class)  EventPlanDto eventPlanDto) {
        return UpdateResponseBo.ok(eventPlanService.updateEventPlan(eventPlanDto));
    }

    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "所属公司下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动年度计划/所属公司下拉")
    @PostMapping("getCompanyList")
    public ResponseBo getCompanyList(){
        return new ResponseBo(eventPlanService.getCompanyList());
    }


    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "年度下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动年度计划/年度下拉")
    @PostMapping("getYearList")
    public ResponseBo getYearList(){
        return new ResponseBo(eventPlanService.getYearList());
    }



    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "根据关键字模糊查询学校提供商列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动年度计划/根据关键字模糊查询学校提供商列表")
    @PostMapping("getInstitutionProvidersByName")
    public ResponseBo<InstitutionProviderVo> getInstitutionProvidersByName(@RequestParam("keyword") String keyword){
        return new ResponseBo(eventPlanService.getInstitutionProvidersByName(keyword));
    }
}
