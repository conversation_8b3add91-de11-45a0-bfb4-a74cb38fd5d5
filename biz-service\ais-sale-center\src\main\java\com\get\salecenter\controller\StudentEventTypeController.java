package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.permissioncenter.vo.ResourceVo;
import com.get.salecenter.vo.StudentEventTypeVo;
import com.get.salecenter.service.IStudentEventTypeService;
import com.get.salecenter.dto.StudentEventTypeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/11/3
 * @TIME: 9:55
 * @Description:
 **/
@Api(tags = "学生事件类型管理")
@RestController
@RequestMapping("sale/studentEventType")
public class StudentEventTypeController {
    @Resource
    private IStudentEventTypeService studentEventTypeService;

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生事件类型管理/查询")
    @PostMapping("datas")
    public ResponseBo<StudentEventTypeVo> datas(@RequestBody SearchBean<StudentEventTypeDto> page) {
        List<StudentEventTypeVo> datas = studentEventTypeService.getStudentEventTypes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生事件类型管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<StudentEventTypeVo> detail(@PathVariable("id") Long id) {
        //TODO 改过
        //StudentEventType data = studentEventTypeService.findStudentEventTypeById(id);
        StudentEventTypeVo data = studentEventTypeService.findStudentEventTypeById(id);
        StudentEventTypeVo studentEventTypeVo = BeanCopyUtils.objClone(data, StudentEventTypeVo::new);
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", studentEventTypeVo);
        return responseBo;
    }


    /**
     * 新增信息
     *
     * @param studentEventTypeDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生事件类型管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(StudentEventTypeDto.Add.class) StudentEventTypeDto studentEventTypeDto) {
        return SaveResponseBo.ok(this.studentEventTypeService.addStudentEventType(studentEventTypeDto));
    }

    /**
     * 删除
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生事件类型管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        this.studentEventTypeService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param studentEventTypeDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生事件类型管理/更新")
    @PostMapping("update")
    public ResponseBo<ResourceVo> update(@RequestBody @Validated(StudentEventTypeDto.Update.class) StudentEventTypeDto studentEventTypeDto) {
        return UpdateResponseBo.ok(studentEventTypeService.updateStudentEventType(studentEventTypeDto));
    }


    /**
     * 批量新增信息
     *
     * @param studentEventTypeDto
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生事件类型管理/批量保存")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody  @Validated(StudentEventTypeDto.Add.class) ValidList<StudentEventTypeDto> studentEventTypeDto) {
        studentEventTypeService.batchAdd(studentEventTypeDto);
        return ResponseBo.ok();
    }

    /**
     * 上移下移
     *
     * @param studentEventTypeDto
     * @return
     * @
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生事件类型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<StudentEventTypeDto> studentEventTypeDto) {
        studentEventTypeService.movingOrder(studentEventTypeDto);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 学生事件类型下拉
     * @Param [providerId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学生事件类型下拉", notes = "")
    @GetMapping("getStudentEventTypeSelect")
    public ResponseBo<BaseSelectEntity> getStudentEventTypeSelect() {
        return new ListResponseBo<>(studentEventTypeService.getStudentEventTypeSelect());
    }

}
