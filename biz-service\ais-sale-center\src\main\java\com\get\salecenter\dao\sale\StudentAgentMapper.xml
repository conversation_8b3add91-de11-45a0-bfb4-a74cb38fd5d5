<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.StudentAgentMapper">
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.StudentAgent">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_student_id" jdbcType="BIGINT" property="fkStudentId"/>
        <result column="fk_agent_id" jdbcType="BIGINT" property="fkAgentId"/>
        <result column="is_active" jdbcType="BIT" property="isActive"/>
        <result column="active_date" jdbcType="TIMESTAMP" property="activeDate"/>
        <result column="unactive_date" jdbcType="TIMESTAMP" property="unactiveDate"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <resultMap id="getAgentListByStudentIds"   type="java.util.HashMap">
        <result property="studentId" column="fk_student_id" jdbcType="BIGINT" />
        <result property="agentId" column="GROUP_CONCAT(fk_agent_id)" jdbcType="BIGINT"/>
    </resultMap>

    <insert id="insertSelective" parameterType="com.get.salecenter.entity.StudentAgent" keyProperty="id"
            useGeneratedKeys="true">
        insert into r_student_agent
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkStudentId != null">
                fk_student_id,
            </if>
            <if test="fkAgentId != null">
                fk_agent_id,
            </if>
            <if test="isActive != null">
                is_active,
            </if>
            <if test="activeDate != null">
                active_date,
            </if>
            <if test="unactiveDate != null">
                unactive_date,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkStudentId != null">
                #{fkStudentId,jdbcType=BIGINT},
            </if>
            <if test="fkAgentId != null">
                #{fkAgentId,jdbcType=BIGINT},
            </if>
            <if test="isActive != null">
                #{isActive,jdbcType=BIT},
            </if>
            <if test="activeDate != null">
                #{activeDate,jdbcType=TIMESTAMP},
            </if>
            <if test="unactiveDate != null">
                #{unactiveDate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="getAgentNameByStudentId" resultType="java.lang.String">
        SELECT a.name
        FROM  r_student_agent s
        left join m_agent a on a.id=s.fk_agent_id
        where s.fk_student_id= #{studentId,jdbcType=BIGINT} and s.is_active=1
    </select>

    <select id="getAgentNameByStudentIds" resultType="com.get.salecenter.vo.StudentAgentVo">
        SELECT s.fk_student_id,group_concat(distinct CONCAT(a.`name`,IF(a.name_note is null or a.name_note = "","",CONCAT("（",a.name_note,"）")))) AS agentName
        FROM  r_student_agent s
        left join m_agent a on a.id=s.fk_agent_id
        where s.is_active=1 and s.fk_student_id in
        <foreach collection="studentIds" item="studentId" index="index" open="(" separator="," close=")">
            #{studentId}
        </foreach>
        group by s.fk_student_id
    </select>

    <select id="isExistByAgentId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id from r_student_agent where fk_agent_id=#{agentId}
    </select>

    <select id="datas" resultType="com.get.salecenter.vo.StudentAgentVo">
        SELECT
               CONCAT(a.`name`,IF(a.name_note is null or a.name_note = "","",CONCAT("（",a.name_note,"）"))) as agentName,a.num as agentNum,
                s.* FROM  m_agent a
        left join r_student_agent s on s.fk_agent_id=a.id
        where fk_student_id=#{studentAgentDto.fkStudentId}
        ORDER BY is_active desc
    </select>

    <select id="validateAdd" resultType="java.lang.Integer">
        SELECT count(*) record  from r_student_agent r
        where r.fk_agent_id=#{agentId}
        and r.fk_student_id=#{studentId}
        and is_active=1 LIMIT 1
    </select>

    <select id="validateUpdate" resultType="java.lang.Integer">
        SELECT count(*) record  from r_student_agent r
        where r.fk_student_id=#{studentId}
        and is_active=1 LIMIT 1
    </select>

    <select id="getBdCodeByStudentId" resultType="java.lang.String">
        SELECT d.bd_code from r_staff_bd_code d
        where d.fk_staff_id in
        (SELECT a.fk_staff_id
        FROM  r_student_agent s
        left join r_agent_staff a on s.fk_agent_id=a.fk_agent_id
        where s.fk_student_id= #{studentId} and s.is_active=1 and a.is_active=1
        )
    </select>


    <select id="getBdCodeByStudentIds" resultType="com.get.salecenter.vo.StudentAgentVo">
        SELECT rsa.fk_student_id,group_concat(distinct d.bd_code) AS bdCode from r_staff_bd_code d
        left join r_agent_staff ras on ras.fk_staff_id = d.fk_staff_id
        left join r_student_agent rsa on ras.fk_agent_id=rsa.fk_agent_id
        where rsa.is_active=1 and ras.is_active=1 and rsa.fk_student_id in
        <foreach collection="studentIds" item="studentId" index="index" open="(" separator="," close=")">
            #{studentId}
        </foreach>
        group by rsa.fk_student_id
    </select>

    <select id="getAgentListByStudentId" resultType="com.get.salecenter.vo.AgentAndAgentLabelVo">
        SELECT
            a.id,
            concat(
                    IF
                        (a.is_active = 0, '【无效】', ''),
                    CONCAT(a.`name`,IF(a.name_note is null or a.name_note = "","",CONCAT("（",a.name_note,"）")))
                ) fullName,a.is_active status
        FROM
            r_student_agent AS sa
                LEFT JOIN m_agent AS a ON sa.fk_agent_id = a.id
        WHERE
            sa.is_active = 1 and sa.fk_student_id = #{studentId}
    </select>
    <select id="getAgentListByStudentIds" resultMap="getAgentListByStudentIds">
        SELECT
        fk_student_id,GROUP_CONCAT(fk_agent_id)
        FROM
        r_student_agent
        WHERE
        is_active = 1 and fk_student_id in
        <foreach collection="studentIds" item="studentId" index="index" open="(" separator="," close=")">
            #{studentId}
        </foreach>
        group by fk_student_id
    </select>
    <select id="getRelationByAgentName" resultType="java.lang.Long">
        select rsa.fk_student_id from
            r_student_agent rsa join m_agent ma on rsa.fk_agent_id = ma.id
        where ma.name like concat('%',#{agentName},'%') and rsa.is_active = 1
    </select>
    <select id="getAgentStaffNameByStudentId" resultType="com.get.salecenter.vo.StudentAgentVo">
        SELECT
        sa.*,
        CONCAT(a.`name`,IF(a.name_note is null or a.name_note = "","",CONCAT("（",a.name_note,"）")),'（',a.num,'）') as agentName,
        s.fk_staff_id AS staffId
        FROM
        r_student_agent AS sa
        INNER JOIN m_agent AS a ON a.id = sa.fk_agent_id
        LEFT JOIN r_agent_staff AS s ON s.fk_agent_id = a.id AND s.is_active = 1
        where sa.is_active = 1
        <if test="id != null">
            AND  sa.fk_student_id = #{id}
        </if>
    </select>

    <select id="getAgentStaffNameByStudentIds" resultType="com.get.salecenter.vo.StudentAgentVo">
        SELECT
        sa.*,
        CONCAT(a.`name`,IF(a.name_note is null or a.name_note = "","",CONCAT("（",a.name_note,"）"))) as agentName,
        s.fk_staff_id AS staffId
        FROM
        r_student_agent AS sa
        INNER JOIN m_agent AS a ON a.id = sa.fk_agent_id
        LEFT JOIN r_agent_staff AS s ON s.fk_agent_id = a.id AND s.is_active = 1
        where sa.is_active = 1
        and sa.fk_student_id in
        <foreach collection="studentIds" item="studentId" index="index" open="(" separator="," close=")">
            #{studentId}
        </foreach>
    </select>
    <select id="getAgentStudentNum" resultType="com.get.salecenter.entity.StudentAgent">
        select r.*
        from r_student_agent r
        inner join m_student s on s.id = r.fk_student_id
        where r.is_active = 1 and r.fk_agent_id in
        <foreach collection="fkAgentIds" item="fkAgentId" index="index" open="(" separator="," close=")">
            #{fkAgentId}
        </foreach>
    </select>
</mapper>