<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.RNewsTypeMapper">
    <insert id="insert" parameterType="com.get.institutioncenter.entity.RNewsType">
    insert into r_news_type (id, fk_news_id, fk_table_name, 
      fk_table_id, fk_news_type_id, gmt_create, 
      gmt_create_user, gmt_modified, gmt_modified_user
      )
    values (#{id,jdbcType=BIGINT}, #{fkNewsId,jdbcType=BIGINT}, #{fkTableName,jdbcType=VARCHAR}, 
      #{fkTableId,jdbcType=BIGINT}, #{fkNewsTypeId,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR}
      )
  </insert>
    <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.RNewsType">
        insert into r_news_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkNewsId != null">
                fk_news_id,
            </if>
            <if test="fkTableName != null">
                fk_table_name,
            </if>
            <if test="fkTableId != null">
                fk_table_id,
            </if>
            <if test="fkNewsTypeId != null">
                fk_news_type_id,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkNewsId != null">
                #{fkNewsId,jdbcType=BIGINT},
            </if>
            <if test="fkTableName != null">
                #{fkTableName,jdbcType=VARCHAR},
            </if>
            <if test="fkTableId != null">
                #{fkTableId,jdbcType=BIGINT},
            </if>
            <if test="fkNewsTypeId != null">
                #{fkNewsTypeId,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="getRNewsTypeByNewIds" resultType="com.get.institutioncenter.vo.RNewsTypeVo">
        SELECT * FROM r_news_type WHERE 1=1
        <if test="fkNewsId != null">
            and fk_news_id =#{fkNewsId,jdbcType=BIGINT}
        </if>
    </select>
    <select id="getRNewsByNewIds" resultType="com.get.institutioncenter.vo.RNewsTypeVo">
        SELECT rnt.*,unt.type_name as newsTypeName FROM r_news_type rnt
        left join u_news_type unt on unt.id = rnt.fk_news_type_id
        <if test="fkTableName != null and  fkTableName !=''">
            and fk_table_name = #{fkTableName}
        </if>
        <if test="fkNewsTypeId != null and  fkNewsTypeId !=''">
            and fk_news_type_id = #{fkNewsTypeId}
        </if>
        <if test="fkTableId != null and  fkTableId !=''">
            and fk_table_id = #{fkTableId}
        </if>
        <if test="fkNewsIds !=null and fkNewsIds.size() > 0">
            and fk_news_id in
            <foreach collection="fkNewsIds" item="data" index="index" open="(" close=")" separator=",">
                #{data}
            </foreach>
        </if>
    </select>
    <select id="getRNewName" resultType="com.get.institutioncenter.vo.RNewsTypeVo">
 select CONCAT(name,IFNULL(CONCAT('（',name_chn,'）'),''))as targetName from  ${fkTableName} where id =#{tableId}
    </select>
</mapper>