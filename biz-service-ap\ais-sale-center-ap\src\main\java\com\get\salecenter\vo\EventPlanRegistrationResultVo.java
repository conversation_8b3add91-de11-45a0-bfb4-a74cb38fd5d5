package com.get.salecenter.vo;

import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collection;

/**
 * <AUTHOR>
 * @DATE: 2024/1/16
 * @TIME: 12:30
 * @Description:
 **/
@Data
public class EventPlanRegistrationResultVo<T> extends ListResponseBo<T> {
    @ApiModelProperty(value = "报名名册活动项目数")
    private Integer registrationEventCount;

    public EventPlanRegistrationResultVo(Collection<T> list, Page page , Integer registrationEventCount) {
        super(list,page);
        this.registrationEventCount = registrationEventCount;
    }
}
