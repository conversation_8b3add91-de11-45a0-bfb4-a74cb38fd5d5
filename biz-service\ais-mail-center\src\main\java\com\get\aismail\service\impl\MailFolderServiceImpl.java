package com.get.aismail.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.aismail.dao.MMailAccountMapper;
import com.get.aismail.dao.MMailFolderMapper;
import com.get.aismail.dao.MMailSyncQueueDorisMapper;
import com.get.aismail.dao.MMailSyncQueueMapper;
import com.get.aismail.dto.AddNewFolder;
import com.get.aismail.entity.MMailAccount;
import com.get.aismail.entity.MMailFolder;
import com.get.aismail.entity.MMailSyncQueue;
import com.get.aismail.service.IMailFolderService;
import com.get.aismail.vo.DeleteFolder;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.StaffInfo;
import com.get.core.secure.utils.SecureUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

import static com.get.core.tool.api.ResultCode.BAD_REQUEST;

@Slf4j
@Service
public class MailFolderServiceImpl implements IMailFolderService {
    @Resource
    private MMailAccountMapper accountMapper;

    @Resource
    private MMailFolderMapper mailFolderMapper;

    @Resource
    private MMailSyncQueueMapper mailSyncQueueMapper;

    @Resource
    private MMailSyncQueueDorisMapper mailSyncQueueDorisMapper;

    @Override
    public void addNewFolder(AddNewFolder addNewFolder) {
        log.info("开始新增文件夹");
        String emailAccount = addNewFolder.getEmailAccount();
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        Long userId = staffInfo.getStaffId();
        LambdaQueryWrapper<MMailAccount> accountQueryWrapper = new LambdaQueryWrapper<>();
        accountQueryWrapper.eq(MMailAccount::getFkPlatformUserId, userId).eq(MMailAccount::getEmailAccount, emailAccount);
        List<MMailAccount> accounts = accountMapper.selectList(accountQueryWrapper);
        if (accounts.isEmpty()) {
            throw new GetServiceException(BAD_REQUEST, "mail account is not exist");
        }
        // 判断文件夹名称是否已经存在
        LambdaQueryWrapper<MMailFolder> checkFolderQueryWrapper = new LambdaQueryWrapper<>();
        checkFolderQueryWrapper.eq(MMailFolder::getFkMailAccountId, addNewFolder.getEmailAccount());
        checkFolderQueryWrapper.eq(MMailFolder::getFolderName, addNewFolder.getFolderName());
        List<MMailFolder> folders = mailFolderMapper.selectList(checkFolderQueryWrapper);
        if (!folders.isEmpty()) {
            throw new GetServiceException(BAD_REQUEST, "已经存在相同的文件夹");
        }
        Long mailAccountId = accounts.get(0).getId();
        // 判断是否已经有文件夹，如果有的话viewOrder顺次递增，否则为最新的。
        LambdaQueryWrapper<MMailFolder> folderQueryWrapper = new LambdaQueryWrapper<>();
        folderQueryWrapper.eq(MMailFolder::getFkMailAccountId, mailAccountId);
        folderQueryWrapper.orderByAsc(MMailFolder::getViewOrder);
        List<MMailFolder> mailFolders = mailFolderMapper.selectList(folderQueryWrapper);
        MMailFolder mailFolder = new MMailFolder();
        if (mailFolders.isEmpty()) {
            mailFolder.setViewOrder(0);
        } else {
            int bigMaxOrder = mailFolders.get(mailFolders.size() - 1).getViewOrder();
            mailFolder.setViewOrder(bigMaxOrder + 1);
        }
        mailFolder.setFkMailAccountId(mailAccountId);
        mailFolder.setFolderName(addNewFolder.getFolderName());
        mailFolder.setGmtCreate(LocalDateTime.now());
        mailFolder.setGmtCreateUser(staffInfo.getName());
        mailFolderMapper.insert(mailFolder);
        log.info("新增文件夹结束");
    }

    @Override
    public List<MMailFolder> selectAllFolder(String emailAccount) {
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        Long userId = staffInfo.getStaffId();
        LambdaQueryWrapper<MMailAccount> accountQueryWrapper = new LambdaQueryWrapper<>();
        accountQueryWrapper.eq(MMailAccount::getFkPlatformUserId, userId).eq(MMailAccount::getEmailAccount, emailAccount);
        List<MMailAccount> accounts = accountMapper.selectList(accountQueryWrapper);
        if (accounts.isEmpty()) {
            throw new GetServiceException(BAD_REQUEST, "mail account is not exist");
        }
        Long mailAccountId = accounts.get(0).getId();
        LambdaQueryWrapper<MMailFolder> folderQueryWrapper = new LambdaQueryWrapper<>();
        folderQueryWrapper.eq(MMailFolder::getFkMailAccountId, mailAccountId);
        folderQueryWrapper.orderByAsc(MMailFolder::getViewOrder);
        return mailFolderMapper.selectList(folderQueryWrapper);
    }

    @Override
    public void deleteFolder(DeleteFolder deleteFolder) {
        LambdaQueryWrapper<MMailSyncQueue> mailSyncQueueQueryWrapper = new LambdaQueryWrapper<>();
        mailSyncQueueQueryWrapper.eq(MMailSyncQueue::getFkMailAccountId, deleteFolder.getEmailAccount());
        mailSyncQueueQueryWrapper.eq(MMailSyncQueue::getOperationValue, String.valueOf(deleteFolder.getMailFolder().getId()));
        List<MMailSyncQueue> mailSyncQueues = mailSyncQueueMapper.selectList(mailSyncQueueQueryWrapper);
        for (MMailSyncQueue mailSyncQueue : mailSyncQueues) {
            LambdaQueryWrapper<MMailSyncQueue> deleteQueueWrapper = new LambdaQueryWrapper<>();
            deleteQueueWrapper.eq(MMailSyncQueue::getFkMailAccountId, deleteFolder.getEmailAccount());
            deleteQueueWrapper.eq(MMailSyncQueue::getFkMailId, mailSyncQueue.getFkMailId());
            deleteQueueWrapper.eq(MMailSyncQueue::getOperationType, 3);
            List<MMailSyncQueue> deleteQueues = mailSyncQueueMapper.selectList(deleteQueueWrapper);
            for (MMailSyncQueue deleteQueue : deleteQueues) {
                mailSyncQueueMapper.deleteById(deleteQueue.getId());
                // doris同步删除
                mailSyncQueueDorisMapper.deleteById(deleteQueue.getId());
            }
        }
        // 如果是删除文件夹则才删除
        if (deleteFolder.isDeleted()) {
            mailFolderMapper.deleteById(deleteFolder.getMailFolder().getId());
        }
    }

    @Override
    public void updateFolder(List<MMailFolder> mailFolders) {
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        for (int i = 0; i < mailFolders.size(); i++) {
            MMailFolder mailFolder = mailFolders.get(i);
            mailFolder.setViewOrder(i);
            mailFolder.setGmtModified(LocalDateTime.now());
            mailFolder.setGmtModifiedUser(staffInfo.getName());
            mailFolderMapper.updateById(mailFolder);
        }
    }
}
