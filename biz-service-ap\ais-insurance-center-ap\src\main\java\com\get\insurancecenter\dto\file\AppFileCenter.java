package com.get.insurancecenter.dto.file;

import com.get.insurancecenter.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:Oliver
 * @Date: 2025/6/19
 * @Version 1.0
 * @apiNote:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppFileCenter extends BaseEntity {

    @ApiModelProperty(value = "文件guid")
    private String fileGuid;

    @ApiModelProperty(value = "源文件类型")
    private String fileTypeOrc;

    @ApiModelProperty(value = "源文件名")
    private String fileNameOrc;

    @ApiModelProperty(value = "目标文件名")
    private String fileName;

    @ApiModelProperty(value = "目标文件路径")
    private String filePath;

    @ApiModelProperty(value = "文件外部存储Key（如：腾讯云COS）")
    private String fileKey;
}
