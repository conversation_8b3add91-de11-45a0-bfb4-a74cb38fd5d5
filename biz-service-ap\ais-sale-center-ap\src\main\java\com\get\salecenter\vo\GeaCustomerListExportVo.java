package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class GeaCustomerListExportVo {

    @ApiModelProperty(value = "申请计划编号")
    private String studentOfferItemNum;

    @ApiModelProperty(value = "业务国家")
    private String fkAreaCountryName;

    @ApiModelProperty(value = "BD+代理编号")
    private String bdAgentNum;

    @ApiModelProperty(value = "代理")
    private String agentName;

    @ApiModelProperty(value = "BD名称")
    private String bdName;

    /**
     * 学生姓名
     */
    @ApiModelProperty(value = "学生姓名")
    private String studentName;

    @ApiModelProperty(value = "佣金结算标记")
    private String commissionMark;

    /**
     * 学生名pinyin
     */
    @ApiModelProperty(value = "学生拼音")
    private String studentPinYin;


    /**
     * 学生id
     */
    @ApiModelProperty(value = "student ID")
    private String idNumer;

    /**
     * 学生生日
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "出生日期")
    private String birthdayStr;

    @ApiModelProperty("佣金合同方")
    private String fkInstitutionChannelName;

    @ApiModelProperty(value = "学校提供商")
    private String providerName;

//    /**
//     * 学校提供商名称
//     */
//    @ApiModelProperty(value = "学校提供商（渠道/集团/学校）")
//    private String fkInstitutionProviderName;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校")
    private String institutionFullName;

    /**
     * 申请备注（网申信息）
     */
    @ApiModelProperty(value = "收款计划摘要")
    private String appRemark;

    @ApiModelProperty(value = "课程名称")
    private String courseFullName;

    /**
     * 课程长度
     */
    @ApiModelProperty(value = "课程长度")
    private String durationInfo;

    /**
     * 开学时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开学日期")
    private String deferOpeningTimeStr;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;

    @ApiModelProperty(value = "项目成员（外联）")
    private String coordinatorRoleStaffName;

    @ApiModelProperty(value = "代理标签")
    private String agentLabelNames;
}
