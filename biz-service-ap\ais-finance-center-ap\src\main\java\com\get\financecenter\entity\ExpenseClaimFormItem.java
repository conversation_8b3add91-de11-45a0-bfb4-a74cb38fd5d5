package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("m_expense_claim_form_item")
public class ExpenseClaimFormItem extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "费用报销申请单Id")
    private Long fkExpenseClaimFormId;

    @ApiModelProperty(value = "费用报销费用类型Id")
    private Long fkExpenseClaimFeeTypeId;

    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "报销金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "发票金额")
    private BigDecimal invoiceAmount;

    @ApiModelProperty(value = "摘要")
    private String summary;

    @ApiModelProperty(value = "关联类型Key（目标类型表名）")
    private String relationTargetKey;

    @ApiModelProperty(value = "关联类型Id（目标类型表对应记录项Id）")
    private Long relationTargetId;

    @ApiModelProperty(value = "活动类型表名（目标类型表名）")
    private String fkEventTableName;

    @ApiModelProperty(value = "活动Id（目标类型表对应记录项Id）")
    private Long fkEventTableId;

    @ApiModelProperty(value = "学生代理Id")
    private Long fkAgentId;

    @ApiModelProperty(value = "费用报销关联代理内容Id")
    private Long fkExpenseClaimAgentContentId;

    @ApiModelProperty(value = "人数")
    private Integer peopleCount;

}