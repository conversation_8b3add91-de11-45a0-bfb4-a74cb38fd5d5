package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.ErrorCodeEnum;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.salecenter.dao.sale.StudentOfferItemFailureMapper;
import com.get.salecenter.dao.sale.StudentOfferItemMapper;
import com.get.salecenter.vo.StudentOfferItemFailureVo;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.entity.StudentOfferItemFailure;
import com.get.salecenter.service.IReceivablePlanService;
import com.get.salecenter.service.StudentOfferItemFailureService;
import com.get.salecenter.dto.StudentOfferItemFailureDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 学习计划生成应收、应付计划失败逻辑类
 *
 * <AUTHOR>
 * @date 2021/7/8 16:55
 */
@Service
public class StudentOfferItemFailureServiceImpl implements StudentOfferItemFailureService {
    @Resource
    private StudentOfferItemFailureMapper studentOfferItemFailureMapper;
    @Resource
    private StudentOfferItemMapper studentOfferItemMapper;
    @Resource
    private IReceivablePlanService receivablePlanService;
    @Resource
    private UtilService utilService;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    /**
     * 学习计划生成失败列表
     *
     * @Date 17:55 2021/7/8
     * <AUTHOR>
     */
    @Override
    public List<StudentOfferItemFailureVo> getStudentOfferItemFailureList(StudentOfferItemFailureDto studentOfferItemFailureDto, Page page) {
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        IPage<StudentOfferItemFailureVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<StudentOfferItemFailureVo> studentOfferItemFailureVos = studentOfferItemFailureMapper.getStudentOfferItemFailureList(pages, studentOfferItemFailureDto);
        page.setAll((int) pages.getTotal());
        if (GeneralTool.isNotEmpty(studentOfferItemFailureVos)) {
            Map<Long, String> countryNamesMap = new HashMap<>();
            Result<Map<Long, String>> result_1 = institutionCenterClient.getCountryNamesByIds(studentOfferItemFailureVos.stream().map(StudentOfferItemFailureVo::getAreaCountryId).collect(Collectors.toSet()));
            if (result_1.isSuccess() && GeneralTool.isNotEmpty(result_1.getData())) {
                countryNamesMap = result_1.getData();
            }
            Map<Long, String> institutionProviderNameMap = new HashMap<>();
            Result<Map<Long, String>> institutionProviderNameResult = institutionCenterClient.getInstitutionProviderNamesByIds(studentOfferItemFailureVos.stream().map(StudentOfferItemFailureVo::getInstitutionProviderId).collect(Collectors.toSet()));
            if (institutionProviderNameResult.isSuccess() && GeneralTool.isNotEmpty(institutionProviderNameResult.getData())) {
                institutionProviderNameMap = institutionProviderNameResult.getData();
            }
            Map<Long, String> institutionNameMap = new HashMap<>();
            Result<Map<Long, String>> result = institutionCenterClient.getInstitutionNamesByIds(studentOfferItemFailureVos.stream().map(StudentOfferItemFailureVo::getInstitutionId).collect(Collectors.toSet()));
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                institutionNameMap = result.getData();
            }
            Map<Long, String> institutionCourseNameMap = new HashMap<>();
            Result<Map<Long, String>> result_2 = institutionCenterClient.getInstitutionCourseNamesByIds(studentOfferItemFailureVos.stream().map(StudentOfferItemFailureVo::getInstitutionCourseId).collect(Collectors.toSet()));
            if (result_2.isSuccess() && GeneralTool.isNotEmpty(result_2.getData())) {
                institutionCourseNameMap = result_2.getData();
            }
            for (StudentOfferItemFailureVo studentOfferItemFailureVo : studentOfferItemFailureVos) {
                studentOfferItemFailureVo.setAreaCountryName(countryNamesMap.get(studentOfferItemFailureVo.getAreaCountryId()));
                studentOfferItemFailureVo.setInstitutionProviderName(institutionProviderNameMap.get(studentOfferItemFailureVo.getInstitutionProviderId()));
                studentOfferItemFailureVo.setInstitutionName(institutionNameMap.get(studentOfferItemFailureVo.getInstitutionId()));
                studentOfferItemFailureVo.setInstitutionCourseName(institutionCourseNameMap.get(studentOfferItemFailureVo.getInstitutionCourseId()));
            }
        }
//        page.restPage(studentOfferItemFailureVos);
        return studentOfferItemFailureVos;
    }

    /**
     * 公司代理下拉框
     *
     * @Date 11:01 2021/7/14
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> agentSelect(Long companyId) {
        return studentOfferItemFailureMapper.agentSelect(companyId);
    }

    /**
     * 重新生成 （废弃）
     *
     * @Date 11:02 2021/7/9
     * <AUTHOR>
     */
    @Override
    public ResponseBo regenerate(Long id) {
        StudentOfferItemFailure studentOfferItemFailure = studentOfferItemFailureMapper.selectById(id);
        if (GeneralTool.isEmpty(studentOfferItemFailure)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (studentOfferItemFailure.getStatus() == 1) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("student_offer_item_failure_abnormal_state"));
        }
        StudentOfferItem studentOfferItem = studentOfferItemMapper.selectById(studentOfferItemFailure.getFkStudentOfferItemId());
        String mark = receivablePlanService.generateReceivablePlan(studentOfferItem, false);
        if (GeneralTool.isEmpty(mark)) {
            studentOfferItemFailure.setStatus(1);
            utilService.updateUserInfoToEntity(studentOfferItemFailure);
            studentOfferItemFailureMapper.updateByPrimaryKey(studentOfferItemFailure);
            return ResponseBo.ok();
        } else {
            studentOfferItemFailure.setRemark(mark);
            utilService.updateUserInfoToEntity(studentOfferItemFailure);
            studentOfferItemFailureMapper.updateByPrimaryKey(studentOfferItemFailure);
            return ResponseBo.error(ErrorCodeEnum.PARAM_REQUIRED.getCode(), "没有匹配到相关合同公式");
        }
    }


}
