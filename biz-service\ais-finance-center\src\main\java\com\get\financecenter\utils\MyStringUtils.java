package com.get.financecenter.utils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Random;

/**
 * <AUTHOR>
 * @DATE: 2020/8/14
 * @TIME: 11:28
 * @Description: 财务中心编码规则工具类
 **/
public class MyStringUtils {


    /**
     * 供应商编号
     *
     * @param num
     * @return
     */
    public static String getProviderNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 7) {
            code = String.format("%06d", num);
        }
        return "PVD" + code;
    }

    /**
     * @return java.lang.String
     * @Description :费用报销单 借款申请单 差旅报销单 支付申请单编号
     * @Param []
     * <AUTHOR>
     */
    public static String getFormNum(String prefix,Long id) {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        String dateStr = formatter.format(calendar.getTime());
        // 格式化ID为8位，不足前面补0
        String idStr = String.format("%08d", id);
        return prefix + dateStr + idStr;
    }

    /**
     * 财务结算批次号
     *
     * @param
     * @return
     */
    public static String geFinancialSettlementNum() {
        Random random = new Random();
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        return formatter.format(calendar.getTime()) + "_" + (random.nextInt(900) + 100);
    }


//    public static void main(String[] args) {
//        List<String> testList= new ArrayList<>();
//        testList.add("123");
//        System.out.println(GeneralTool.isEmpty(testList));
//        System.out.println(CollectionUtil.isEmpty(testList));
//        System.out.println("-----------------------以下为空------------------>");
//        List<String> testList1= new ArrayList<>();
//        System.out.println(GeneralTool.isEmpty(testList1));
//        System.out.println(CollectionUtil.isEmpty(testList1));
//    }
}
