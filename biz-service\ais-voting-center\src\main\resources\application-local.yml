#服务器端口
server:
  port: 8100

##多数据源配置
#spring:
#  datasource:
#    url: ****************************************************************************************************************************
#    username: root
#    password: GETDEV_ROOT@HA8QXAW8

spring:
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    dynamic:
      #设置默认的数据源master
      primary: voting
      datasource:
        voting:
          url: ${get.datasource.test.voting.url}
          username: ${get.datasource.test.voting.username}
          password: ${get.datasource.test.voting.password}
        appvoting:
          url: ${get.datasource.test.appvoting.url}
          username: ${get.datasource.test.appvoting.username}
          password: ${get.datasource.test.appvoting.password}