package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: Hardy
 * @create: 2021/5/27 18:32
 * @verison: 1.0
 * @description:
 */
@Data
public class EventDataVo {
    //活动数据总览

    /**
     * 活动举办国家Name
     */
    @ApiModelProperty(value = "活动举办国家Name")
    private String eventTargetCountryName;

    /**
     * 活动举办州省Name
     */
    @ApiModelProperty(value = "活动举办州省Name")
    private String areaStateNameHold;

    /**
     * 活动举办城市Name
     */
    @ApiModelProperty(value = "活动举办城市Name")
    private String areaCityNameHold;

    /**
     * 活动举办省市Name
     */
    @ApiModelProperty(value = "活动举办省市Name")
    private String areaStateCityNameHold;

    /**
     * 负责人名字
     */
    @ApiModelProperty(value = "负责人名字")
    private String staffNameLeader1;

    /**
     * 负责人名字
     */
    @ApiModelProperty(value = "负责人名字")
    private String staffNameLeader2;


    /**
     * 活动类型名称
     */
    @ApiModelProperty(value = "活动类型名称")
    private String eventTypeName;


    /**
     * 活动总次数
     */
    @ApiModelProperty(value = "活动总次数")
    private Integer eventCount;


    /**
     * 总参与人数
     */
    @ApiModelProperty(value = "总参与人数")
    private Integer sumAttendedCount;

    /**
     * 平均参与人数
     */
    @ApiModelProperty(value = "平均参与人数")
    private Double averageAttendedCount;

    /**
     * 总预算费用
     */
    @ApiModelProperty(value = "总预算费用")
    private BigDecimal sumBudgetAmount;

    /**
     * 平均预算费用
     */
    @ApiModelProperty(value = "平均预算费用")
    private BigDecimal averageBudgetAmount;

    /**
     * 总实际费用
     */
    @ApiModelProperty(value = "总实际费用")
    private BigDecimal sumActualAmount;

    /**
     * 平均实际费用
     */
    @ApiModelProperty(value = "平均实际费用")
    private BigDecimal averageActualAmount;

    /**
     * 活动总览排名
     */
    @ApiModelProperty(value = "活动总览排名")
    private Integer rank;

    /**
     * 查询结果名称
     */
    @ApiModelProperty(value = "查询结果名称")
    private String searchResultName;

    /**
     * 搜索维度对应条件id
     */
    @ApiModelProperty(value = "搜索维度对应条件id")
    private Long id;
}
