package get.middlecenter.service;

import com.get.aisplatformcenterap.dto.GetPermissionMenuDto;
import com.get.aisplatformcenterap.dto.ReleaseInfoAndItemDto;
import com.get.aisplatformcenterap.dto.ReleaseInfoSearchDto;
import com.get.aisplatformcenterap.dto.UserScopedDataDto;
import com.get.aisplatformcenterap.vo.MenuTreeVo;
import com.get.aisplatformcenterap.vo.ReleaseInfoAndItemVo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.permissioncenter.vo.ResourceVo;
import java.util.List;


/**
 * 发版信息
 */
public interface ReleaseInfoAndItemService {


    /**
     * 分页查询所有数据
     * @param page
     * @return
     */
    ResponseBo getReleaseInfoAndItem(SearchBean<ReleaseInfoSearchDto> page);

    /**
     * 新增数据
     * @param releaseInfoAndItemDto
     */
    void addReleaseInfoAndItem(ReleaseInfoAndItemDto releaseInfoAndItemDto);

    /**
     * 删除数据
     * @param id
     */
    void deleteReleaseInfoAndItem(Long id);

    /**
     * 根据id获取详细信息
     * @param id
     * @return
     */
    ReleaseInfoAndItemVo getDetailedInformationById(Long id);

    /**
     * 编辑
     * @param releaseInfoAndItemDto
     */
    void editReleaseInfoAndItem(ReleaseInfoAndItemDto releaseInfoAndItemDto);

    /**
     * 获取AIS菜单
     * @return
     */
    List<ResourceVo> getAisPermissionMenu();

    /**
     * 获取华通伙伴菜单
     * @param getPermissionMenuDto
     * @return
     */
    List<MenuTreeVo> getPartnerPermissionMenu(GetPermissionMenuDto getPermissionMenuDto);

    /**
     * 更改发版信息的状态
     * @param releaseInfoAndItemDto
     */
    void updateReleaseInfoStatus(ReleaseInfoAndItemDto releaseInfoAndItemDto);

    /**
     * 根据用户权限获取发布信息和发布信息详情数据
     * @param searchBean
     * @return
     */
    ResponseBo getUserListByResourceKeys(SearchBean<UserScopedDataDto> searchBean);

    /**
     * 根据用户权限获取最新创建的一条数据
     * @param userScopedDataDto
     * @return
     */
    ReleaseInfoAndItemVo getUserOneByResourceKeys(UserScopedDataDto userScopedDataDto);
}