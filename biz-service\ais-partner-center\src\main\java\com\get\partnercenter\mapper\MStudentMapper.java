package com.get.partnercenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.Student;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【m_student】的数据库操作Mapper
* @createDate 2025-03-31 17:00:25
* @Entity com.get.partnercenter.entity.MStudent
*/
@Mapper
@DS("saledb")
public interface MStudentMapper extends BaseMapper<Student> {

}




