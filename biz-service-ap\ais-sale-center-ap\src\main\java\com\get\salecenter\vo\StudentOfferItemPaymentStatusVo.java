package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;

/**
 * 学习计划生成失败返回类
 *
 * <AUTHOR>
 * @date 2021/7/8 17:37
 */
@Data
public class StudentOfferItemPaymentStatusVo extends BaseEntity {

    @ApiModelProperty(value = "支付类型名称")
    private String paymentTypeName;

    @ApiModelProperty(value = "支付状态名称")
    private String paymentStatusName;

    @ApiModelProperty(value = "支付费用币种名称")
    private String fkCurrencyTypeNumName;

    //==============实体类StudentOfferItemPaymentStatus===============
    private static final long serialVersionUID = 1L;

    /**
     * 申请计划id
     */
    @ApiModelProperty(value = "申请计划id")
    @Column(name = "fk_student_offer_item_id")
    private Long fkStudentOfferItemId;
    /**
     * 收信人Email
     */
    @ApiModelProperty(value = "收信人Email")
    @Column(name = "email")
    private String email;
    /**
     * 支付币种
     */
    @ApiModelProperty(value = "支付币种")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;
    /**
     * 支付金额
     */
    @ApiModelProperty(value = "支付金额")
    @Column(name = "paid_amount")
    private BigDecimal paidAmount;
    /**
     * 支付类型：1.申请费 2.全额学费 3.学费首付 Deposit 4.学费尾款 Remaining
     * ProjectExtraEnum.PAYMENT_TYPE
     */
    @ApiModelProperty(value = "支付类型：1.申请费 2.全额学费 3.学费首付 Deposit 4.学费尾款 Remaining")
    @Column(name = "payment_type")
    private Integer paymentType;
    /**
     * 支付状态：0.付费失败 1.已发送代付邮件 2.已付费(支付渠道) 3.已付费(学校)
     * ProjectExtraEnum.PAYMENT_STATUS
     */
    @ApiModelProperty(value = "支付状态：0.付费失败 1.已发送代付邮件 2.已付费(支付渠道) 3.已付费(学校)")
    @Column(name = "payment_status")
    private Integer paymentStatus;
    /**
     * H5的url/小程序地址支付二维码的url/小程序的scheme码
     */
    @ApiModelProperty(value = "H5的url/小程序地址支付二维码的url/小程序的scheme码")
    @Column(name = "pay_url")
    private String payUrl;
    /**
     * 易思汇支付日志id，如果是手动新增状态，则为空
     */
    @ApiModelProperty(value = "易思汇支付日志id，如果是手动新增状态，则为空")
    @Column(name = "fk_easytransfer_payment_log_id")
    private Long fkEasytransferPaymentLogId;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

}
