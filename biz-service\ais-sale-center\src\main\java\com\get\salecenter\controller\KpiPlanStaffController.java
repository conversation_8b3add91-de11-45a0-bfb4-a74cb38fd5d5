package com.get.salecenter.controller;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.permissioncenter.vo.StaffVo;
import com.get.salecenter.vo.KpiPlanStaffTreeVo;
import com.get.salecenter.vo.KpiPlanStaffVo;
import com.get.salecenter.service.KpiPlanStaffService;
import com.get.salecenter.dto.KpiPlanStaffDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "KPI方案考核人员管理")
@RestController
@RequestMapping("sale/KpiPlanStaff")
public class KpiPlanStaffController {
    @Resource
    private KpiPlanStaffService kpiPlanStaffService;
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/KPI方案考核人员管理/列表数据")
    @PostMapping("datas")
    public ResponseBo<KpiPlanStaffVo> datas(@RequestBody KpiPlanStaffDto kpiPlanStaffVo) {
        List<KpiPlanStaffVo> datas = kpiPlanStaffService.datas(kpiPlanStaffVo);
        return new ListResponseBo<>(datas);
    }

    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/KPI方案考核人员管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(KpiPlanStaffDto.Add.class)  KpiPlanStaffDto vo) {
        return SaveResponseBo.ok(kpiPlanStaffService.addKpiPlanStaff(vo));
    }

    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/KPI方案考核人员管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        kpiPlanStaffService.delete(id);
        return SaveResponseBo.ok();
    }


    @ApiOperation(value = "拖拽", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/KPI方案考核人员管理/拖拽")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestParam("fkKpiPlanId") Long fkKpiPlanId,
                                  @RequestParam("start")Integer start,
                                  @RequestParam("end")Integer end) {
        kpiPlanStaffService.movingOrder(fkKpiPlanId,start,end);
        return ResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "考核人员下拉", notes = "")
    @PostMapping("getKpiStaffSelect")
    public ResponseBo<StaffVo> getKpiStaffSelect(@RequestBody KpiPlanStaffDto kpiPlanStaffVo) {
        return new ListResponseBo<>(kpiPlanStaffService.getKpiStaffSelect(kpiPlanStaffVo));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "考核人员树", notes = "登录人员自己及业务下属设置的整个考核人员树")
    @PostMapping("getKpiPlanStaffTree")
    public ResponseBo<KpiPlanStaffTreeVo> getKpiPlanStaffTree(@RequestParam("fkKpiPlanId")Long fkKpiPlanId) {
        return new ListResponseBo<>(kpiPlanStaffService.getKpiPlanStaffTree(fkKpiPlanId));
    }

}
