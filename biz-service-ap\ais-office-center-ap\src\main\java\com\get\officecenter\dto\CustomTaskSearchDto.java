package com.get.officecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author:cream
 * @Date: 2023/5/30  10:22
 */
@Data
public class CustomTaskSearchDto {

    @ApiModelProperty("今天")
    private Boolean isToday;

    @ApiModelProperty("一周内")
    private Boolean isWithinAWeek;

    @ApiModelProperty("一月内")
    private Boolean isWithinOneMonth;

    @ApiModelProperty("三月内")
    private Boolean isWithinThreeMonths;

    @ApiModelProperty("我的委派")
    private Boolean isMyDelegation;

    @ApiModelProperty("我的任务")
    private Boolean isMyTask;

    @ApiModelProperty("下属任务")
    private Boolean isSubordinateTask;

    @ApiModelProperty("任务状态")
    private Integer taskStatus;

    @ApiModelProperty("委派人")
    private Long delegate;

    @ApiModelProperty("接收人")
    private Long recipient;

    @ApiModelProperty("任务描述")
    private String taskDescription;

    @ApiModelProperty("是否是多人任务 true:是 false:否")
    private Boolean isMultiTask;

    @ApiModelProperty("委派人部门id")
    private List<Long> delegateDeptId;

    @ApiModelProperty("接收人部门id")
    private List<Long> recipientDeptId;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("创建时间（开始范围）")
    private Date createTimeStart;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("创建时间（结束范围）")
    private Date createTimeEnd;

    @ApiModelProperty("是否超时")
    private  Boolean isTimeout;

}
