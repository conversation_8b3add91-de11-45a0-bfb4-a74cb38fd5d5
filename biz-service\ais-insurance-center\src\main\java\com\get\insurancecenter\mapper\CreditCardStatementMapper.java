package com.get.insurancecenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.insurancecenter.dto.card.CreditCardPageDto;
import com.get.insurancecenter.dto.card.TradeRecordDto;
import com.get.insurancecenter.entity.CreditCardStatement;
import com.get.insurancecenter.vo.card.CreateCardPageVo;
import com.get.insurancecenter.vo.card.TradeRecordVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CreditCardStatementMapper extends BaseMapper<CreditCardStatement> {

    /**
     * 查询交易记录分页列表
     *
     * @param page
     * @param param
     * @return
     */
    List<TradeRecordVo> selectTradeRecordPage(IPage<TradeRecordVo> page, @Param("param") TradeRecordDto param);
}
