package com.get.salecenter.dao.sale;

import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.AppAgentContractAccountVo;
import com.get.salecenter.entity.AppAgentContractAccount;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface AppAgentContractAccountMapper extends GetMapper<AppAgentContractAccount> {

    /**
     * 详情获取
     *
     * @param id
     * @return
     */
    AppAgentContractAccountVo findAppAccountInfoById(Long id);
}