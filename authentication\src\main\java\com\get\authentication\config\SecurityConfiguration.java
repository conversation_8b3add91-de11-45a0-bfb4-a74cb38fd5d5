package com.get.authentication.config;

import com.get.authentication.support.GetPasswordEncoderFactories;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * Security配置
 */
@Configuration
@AllArgsConstructor
public class SecurityConfiguration extends WebSecurityConfigurerAdapter {

//    /**
//     * 自定义Provider
//     */
//    @Autowired
//    private WxPcAuthenticationProvider wxPcAuthenticationProvider;


    private final UserDetailsService userDetailsService;

    private final IPermissionCenterClient permissionCenterClient;


    @Bean
    @Override
    @SneakyThrows
    public AuthenticationManager authenticationManagerBean() {
        return super.authenticationManagerBean();
    }


//    @Override
//    public void configure(AuthenticationManagerBuilder auth) {
//        auth.authenticationProvider(wechatAuthenticationProvider()).
//                authenticationProvider(daoAuthenticationProvider());
//    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return GetPasswordEncoderFactories.createDelegatingPasswordEncoder();
    }

    @Override
    @SneakyThrows
    protected void configure(HttpSecurity http) {
        http.httpBasic().and().csrf().disable().authorizeRequests().anyRequest().fullyAuthenticated();
    }


//    @Override
//    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
//        // TODO Auto-generated method stub
//        auth.authenticationProvider(wxPcAuthenticationProvider);
//    }


//    /**
//     * 微信认证授权提供者
//     *
//     * @return
//     */
//    @Bean
//    public WxPcAuthenticationProvider wechatAuthenticationProvider() {
//        WxPcAuthenticationProvider provider = new WxPcAuthenticationProvider();
//        provider.setUserDetailsService(userDetailsService);
//        provider.setPermissionCenterClient(permissionCenterClient);
//        return provider;
//    }


    /**
     * 用户名密码认证授权提供者
     *
     * @return
     */
    @Bean
    public DaoAuthenticationProvider daoAuthenticationProvider() {
        DaoAuthenticationProvider provider = new DaoAuthenticationProvider();
        provider.setUserDetailsService(userDetailsService);
        provider.setPasswordEncoder(passwordEncoder());
        provider.setHideUserNotFoundExceptions(false); // 是否隐藏用户不存在异常，默认:true-隐藏；false-抛出异常；
        return provider;
    }
}
