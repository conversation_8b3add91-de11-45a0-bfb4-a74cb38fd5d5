package com.get.pmpcenter.dto.institution;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/3/24
 * @Version 1.0
 * @apiNote:提交审核请求参数
 */
@Data
public class SubmitApprovalDto {

    @ApiModelProperty(value = "审核人ID")
    @NotNull(message = "审核人ID不能为空")
    private Long staffId;

    @ApiModelProperty(value = "审核人邮箱")
    @NotBlank(message = "审核人邮箱不能为空")
    private String email;

    @ApiModelProperty(value = "合同ID")
    @NotNull(message = "合同ID不能为空")
    private Long contractId;
}
