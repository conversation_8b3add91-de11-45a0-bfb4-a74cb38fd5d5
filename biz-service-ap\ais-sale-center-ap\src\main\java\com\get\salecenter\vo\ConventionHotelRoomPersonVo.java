package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.ConventionHotelRoomPerson;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/24 10:44
 * @verison: 1.0
 * @description:
 */
@Data
@ApiModel(value = "酒店房间床位配置返回类")
public class ConventionHotelRoomPersonVo extends BaseEntity {

    /**
     * 住房人员信息
     */
    @ApiModelProperty(value = "住房人员信息")
    private ConventionPersonVo conventionPerson;

    /**
     * 床位数
     */
    @ApiModelProperty(value = "床位数")
    private Integer bedCount;

    /**
     * 其他房客
     */
    @ApiModelProperty(value = "其他房客")
    private List<ConventionPersonVo> otherConventionPersons;
//
//    /**
//     * 其他房客和房间信息
//     */
//    @ApiModelProperty(value = "其他房客和房间信息")
//    private List<ConventionHotelRoomPersonVo> otherPersons;


    /**
     * 酒店名称
     */
    @ApiModelProperty(value = "酒店名称")
    private String hotel;

    /**
     * 房间型号
     */
    @ApiModelProperty(value = "房间型号")
    private String roomType;

    /**
     * 酒店房号
     */
    @ApiModelProperty(value = "酒店房号")
    private String roomNum;

    /**
     * 房型id
     */
    @ApiModelProperty(value = "房型id")
    private Long conventionHotelId;


    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty
    private Date endTime;

    //连表定义

    /**
     * 房型id
     */
    @ApiModelProperty(value = "房型id")
    private Long fkConventionHotelId;

    /**
     * 系统房间编号
     */
    @ApiModelProperty(value = "系统房间编号")
    private String systemRoomNum;

    /**
     * 酒店房号
     */
    @ApiModelProperty(value = "酒店房号")
    private String hotelRoomNum;

    /**
     * 住店时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "住店时间")
    private Date stayDate;

    //==========实体类ConventionHotelRoomPerson===============
    private static final long serialVersionUID = 1L;
    /**
     * 酒店房间Id
     */
    @ApiModelProperty(value = "酒店房间Id")
    @Column(name = "fk_convention_hotel_room_id")
    private Long fkConventionHotelRoomId;
    /**
     * 峰会参展人员Id
     */
    @ApiModelProperty(value = "峰会参展人员Id")
    @Column(name = "fk_convention_person_id")
    private Long fkConventionPersonId;

}
