package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.CancelOfferReason;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Hardy
 * @create: 2023/1/11 11:38
 * @verison: 1.0
 * @description:
 */
@Data
public class CancelOfferReasonVo extends BaseEntity {

    //========实体类CancelOfferReason========
    /**
     * 原因名称
     */
    @ApiModelProperty(value = "原因名称")
    private String reasonName;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    private static final long serialVersionUID = 1L;
}
