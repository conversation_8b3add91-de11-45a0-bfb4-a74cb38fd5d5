package com.get.permissioncenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.permissioncenter.vo.PermissionGroupGradeStaffVo;
import com.get.permissioncenter.entity.PermissionGroupGradeStaff;
import com.get.permissioncenter.dto.PermissionGroupGradeForMovingAndCopyingDto;
import com.get.permissioncenter.dto.PermissionGroupGradeStaffDto;

import java.util.List;

/**
 * @author: jack
 * @create: 2020/7/14
 * @verison: 1.0
 * @description: 权限人员配置业务接口
 */
public interface IGroupGradeStaffService extends BaseService<PermissionGroupGradeStaff> {
    /**
     * 权限人员列表数据
     *
     * @return
     */
    PermissionGroupGradeStaffVo getGroupGradeStaff(Long companyId);

    /**
     * 组别等级人员集合
     *
     * @param permissionGroupGradeStaffDto
     * @return
     */
    List<Long> getGroupGradeStaffs(PermissionGroupGradeStaffDto permissionGroupGradeStaffDto);

    /**
     * 更新权限人员
     *
     * @param
     * @return
     */
    void updateGroupGradeStaffs(PermissionGroupGradeStaffDto permissionGroupGradeStaffDto);

    /**
     * 获取员工权限组别与等级
     *
     * @param
     * @return
     */
    List<PermissionGroupGradeStaffVo> getPermissionGroupGradeStaffDtosByStaffId(Long id);

    /**
     * 移动网点人员
     *
     * @param permissionGroupGradeForMovingAndCopyingDto
     */
    void movePermissionGroupGradeStaff(PermissionGroupGradeForMovingAndCopyingDto permissionGroupGradeForMovingAndCopyingDto);

    /**
     * 复制网点资源人员
     *
     * @param permissionGroupGradeForMovingAndCopyingDto
     */
    void copyPermissionGroupGradeStaff(PermissionGroupGradeForMovingAndCopyingDto permissionGroupGradeForMovingAndCopyingDto);
}
