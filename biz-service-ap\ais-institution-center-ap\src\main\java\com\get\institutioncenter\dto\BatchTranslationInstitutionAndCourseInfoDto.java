package com.get.institutioncenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2024/4/17 10:05
 * @verison: 1.0
 * @description:
 */
@Data
public class BatchTranslationInstitutionAndCourseInfoDto {

    @ApiModelProperty(value = "学校id")
    private List<Long> institutionIds;

    @ApiModelProperty(value = "appId")
    private String appId;

    @ApiModelProperty(value = "key")
    private String key;

}
