package com.get.financecenter.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.get.core.mybatis.base.BaseSelectEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AccountingItemDropdownMenuVo extends BaseSelectEntity {
    @ApiModelProperty(value = "科目等级")
    private Integer grade;

    @ApiModelProperty(value = "子科目")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<AccountingItemDropdownMenuVo> childrenAccountingItem;

}
