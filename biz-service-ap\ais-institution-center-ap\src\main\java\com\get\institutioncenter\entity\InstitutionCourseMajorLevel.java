package com.get.institutioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_institution_course_major_level")
public class InstitutionCourseMajorLevel extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 课程Id
     */
    @ApiModelProperty(value = "课程Id")
    @Column(name = "fk_institution_course_id")
    private Long fkInstitutionCourseId;
    /**
     * 专业等级Id
     */
    @ApiModelProperty(value = "专业等级Id")
    @Column(name = "fk_major_level_id")
    private Long fkMajorLevelId;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fkInstitutionCourseId=").append(fkInstitutionCourseId);
        sb.append(", fkMajorLevelId=").append(fkMajorLevelId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}