package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * author:Neil
 * Time: 12:38
 * Date: 2022/8/17
 * Description:
 */
@Data
@TableName("m_client_offer")
public class ClientOffer extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "客户Id")
    @Column(name = "fk_client_id")
    private Long fkClientId;

    @UpdateWithNull
    @ApiModelProperty(value = "代理Id（业绩绑定）")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;

//    @ApiModelProperty(value = "代理联系人Id")
//    @Column(name = "fk_contact_person_id")
//    private Long fkContactPersonId;

    @ApiModelProperty(value = "员工Id（业绩绑定，BD）")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;

    @UpdateWithNull
    @ApiModelProperty(value = "代理名称")
    @Column(name = "agent_name")
    private String agentName;

    @ApiModelProperty(value = "代理联系电话区号")
    @Column(name = "agent_contact_tel_area_code")
    private String agentContactTelAreaCode;

    @ApiModelProperty(value = "代理联系电话，多个用【;+空格】隔开")
    @Column(name = "agent_contact_tel")
    private String agentContactTel;

    @ApiModelProperty(value = "代理联系电邮，多个用【;+空格】隔开")
    @Column(name = "agent_contact_email")
    private String agentContactEmail;

    @ApiModelProperty(value = "国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "咨询方案编号")
    @Column(name = "num")
    private String num;

    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    @ApiModelProperty(value = "学生资源申请方案状态步骤Id")
    @Column(name = "fk_client_offer_step_id")
    private Long fkClientOfferStepId;

    @ApiModelProperty(value = "状态：0作废/1有效")
    @Column(name = "status")
    private Integer status;
}
