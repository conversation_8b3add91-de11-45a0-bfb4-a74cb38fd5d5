package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.redis.lock.RedisLock;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.vo.*;
import com.get.institutioncenter.service.IInstitutionCourseService;
import com.get.institutioncenter.dto.*;
import com.get.institutioncenter.dto.query.InstitutionCourseQueryDto;
import com.get.institutioncenter.dto.query.NewsQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/8/10
 * @TIME: 11:16
 * @Description:
 **/
@Api(tags = "课程管理")
@RestController
@RequestMapping("/institution/institutionCourse")
public class InstitutionCourseController {
    @Resource
    private IInstitutionCourseService institutionCourseService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/学校课程管理/学校课程详情")
    @GetMapping("/{id}")
    public ResponseBo<InstitutionCourseVo> detail(@PathVariable("id") Long id) {
        InstitutionCourseVo data = institutionCourseService.findInstitutionCourseById(id);
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", data);
        return responseBo;
    }

    /**
     * 新增信息
     *
     * @param institutionCourseDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/学校课程管理/新增学校课程")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(InstitutionCourseDto.Add.class) InstitutionCourseDto institutionCourseDto) {
        return SaveResponseBo.ok(institutionCourseService.addInstitutionCourse(institutionCourseDto));
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/学校课程管理/删除学校课程")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        institutionCourseService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param institutionCourseDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/学校课程管理/更新学校课程")
    @PostMapping("update")
    @RedisLock(value="fzh:courseUpdateLock",param = "#institutionCourseDto.id",waitTime = 10L)
    public ResponseBo<InstitutionCourseVo> update(@RequestBody @Validated(InstitutionCourseDto.Update.class) InstitutionCourseDto institutionCourseDto) {
        return UpdateResponseBo.ok(institutionCourseService.updateInstitutionCourse(institutionCourseDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校课程管理/查询学校课程")
    @PostMapping("datas")
    public ResponseBo<InstitutionCourseVo> datas(@RequestBody SearchBean<InstitutionCourseQueryDto> page) {
        List<InstitutionCourseVo> datas = institutionCourseService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * @param mediaAttachedVo
     * @return
     * @
     */
    @ApiOperation(value = "保存文件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/学校课程管理/保存附件资料")
    @PostMapping("addMediaAndAttached")
    public ResponseBo<MediaAndAttachedVo> addMediaAndAttached(@RequestBody @Validated(MediaAndAttachedDto.Add.class) List<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(institutionCourseService.addInstitutionCourseMedia(mediaAttachedVo));
    }

    /**
     * 附件类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "附件类型下拉框数据", notes = "")
    @PostMapping("findMediaAndAttachedType")
    public ResponseBo findMediaAndAttachedType() {
        List<Map<String, Object>> datas = institutionCourseService.findMediaAndAttachedType();
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 课程下拉框数据
     * @Param [institutionId]
     * <AUTHOR>
     **/
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "课程下拉框数据", notes = "")
    @PostMapping("getInstitutionCourseByInstitution")
    public ResponseBo<BaseSelectEntity> getInstitutionCourseByInstitution(@RequestParam(value = "institutionId") Long institutionId) {
        List<BaseSelectEntity> institutionCourseDtos = institutionCourseService.getInstitutionCourseByInstitution(institutionId);
        institutionCourseDtos.removeIf(baseSelectEntity -> baseSelectEntity.getStatus() == 0);
        return new ListResponseBo<>(institutionCourseDtos);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description :多个学校所有的课程下拉框数据
     * @Param [institutionIdList]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "多个学校所有的课程下拉框数据", notes = "")
    @PostMapping("getInstitutionCourseByInstitutionIdList")
    public ResponseBo<BaseSelectEntity> getInstitutionCourseByInstitutionIdList(@RequestBody List<Long> institutionIdList) {
        List<BaseSelectEntity> institutionCourseDtos = institutionCourseService.getInstitutionCourseByInstitutionIdList(institutionIdList);
        return new ListResponseBo<>(institutionCourseDtos);
    }

    /**
     * feign调用 通过课程id 查找对应的课程名称
     *
     * @param name
     * @return
     */
/*    @ApiIgnore
    @GetMapping(value = "getCourseIds")
    public List<Long> getCourseIds(@RequestParam(value = "name") String name) {
        return institutionCourseService.getCourseIds(name);
    }*/

    /**
     * feign调用 通过课程id 查找对应的课程名称
     *
     * @param id
     * @return
     */
/*    @ApiIgnore
    @GetMapping(value = "getCourseNameById")
    public String getCourseNameById(@RequestParam(value = "id") Long id) {
        return institutionCourseService.getCourseNameById(id);
    }*/


    /**
     * feign调用 通过课程ids 查找对应的课程名称
     *
     * @param ids
     * @return
     */
 /*   @ApiIgnore
    @GetMapping(value = "getCourseNameByIds")
    public Map<Long, String> getCourseNameByIds(@RequestParam(value = "ids") Set<Long> ids) {
        return institutionCourseService.getCourseNameByIds(ids);
    }*/

    /**
     * feign调用 通过自定义课程ids 查找对应的自定义课程名称
     *
     * @param ids
     * @return
     */
/*    @ApiIgnore
    @GetMapping(value = "getCourseNameByCustomIds")
    public Map<Long, String> getCourseNameByCustomIds(@RequestParam(value = "ids") Set<Long> ids)  {
        return institutionCourseService.getCourseNameByCustomIds(ids);
    }*/

    /**
     * feign调用 通过课程id 查找对应的课程名称
     *
     * @param id
     * @return
     */
/*    @ApiIgnore
    @GetMapping(value = "getCourseNameChnById")
    public String getCourseNameChnById(@RequestParam(value = "id") Long id) {
        return institutionCourseService.getCourseNameChnById(id);
    }*/

    /**
     * feign调用 通过课程ids 查找对应的课程名称
     *
     * @param ids
     * @return
     */
  /*  @ApiIgnore
    @GetMapping(value = "getCourseNameChnByIds")
    public Map<Long, String> getCourseNameChnByIds(@RequestParam(value = "ids") Set<Long> ids) {
        return institutionCourseService.getCourseNameChnByIds(ids);
    }*/

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.NewsVo>
     * @Description: 课程新闻列表数据
     * @Param [page]
     * <AUTHOR>
     **/
    @ApiOperation(value = "课程新闻列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/课程管理/查询新闻")
    @PostMapping("getNewsDatas")
    public ResponseBo<NewsVo> getNewsDatas(@RequestBody SearchBean<NewsQueryDto> page) {
        List<NewsVo> datas = institutionCourseService.getNewsData(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 查询课程附件
     *
     * @param voSearchBean
     * @return
     * @
     */
    @ApiOperation(value = "查询课程附件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/课程管理/查询课程附件")
    @PostMapping("getCourseMedia")
    public ResponseBo<MediaAndAttachedVo> getCourseMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> institutionMedia = institutionCourseService.getCourseMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(institutionMedia, page);
    }

    /**
     * 英语成绩列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "英语成绩列表数据", notes = "")
    @PostMapping("getEngScoreDatas")
    public ResponseBo<InstitutionCourseEngScoreVo> getEngScoreDatas(@RequestBody SearchBean<InstitutionCourseEngScoreDto> page) {
        List<InstitutionCourseEngScoreVo> datas = institutionCourseService.getEngScoreDatas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 学术成绩列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "学术成绩列表数据", notes = "")
    @PostMapping("getAcademicScoreDatas")
    public ResponseBo<InstitutionCourseAcademicScoreVo> getAcademicScoreDatas(@RequestBody SearchBean<InstitutionCourseAcademicScoreDto> page) {
        List<InstitutionCourseAcademicScoreVo> datas = institutionCourseService.getAcademicScoreDatas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 申请资料列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "申请资料列表数据", notes = "")
    @PostMapping("getAppInfoDatas")
    public ResponseBo<AppInfoVo> getAppInfoDatas(@RequestBody SearchBean<AppInfoDto> page) {
        List<AppInfoVo> datas = institutionCourseService.getAppInfoDatas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 目标类型下拉
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "目标类型下拉", notes = "")
    @GetMapping("findTargetType")
    public ResponseBo findTargetType() {
        List<Map<String, Object>> datas = institutionCourseService.findTargetType();
        return new ListResponseBo<>(datas);
    }

    /**
     * 目标类型获取目标
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "目标类型获取目标", notes = "")
    @PostMapping("findTarget")
    public ResponseBo<BaseSelectEntity> findTarget(@RequestParam String tableName) {
        List<BaseSelectEntity> datas = institutionCourseService.findTarget(tableName);
        return new ListResponseBo<>(datas);
    }

    /**
     * 新增信息
     *
     * @param resources
     * @return
     * @
     */
    @ApiOperation(value = "新增申请资料接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/申请资料管理/新增保存")
    @PostMapping("addAppInfos")
    public ResponseBo addAppInfos(@RequestBody @Validated(AppInfoDto.Add.class) ValidList<AppInfoDto> resources) {
        institutionCourseService.batchAdd(resources);
        return ResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 桥梁课程下拉框数据
     * @Param [institutionId]
     * <AUTHOR>
     **/
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "桥梁课程下拉框数据", notes = "")
    @PostMapping("getInstitutionCoursePathwayByInstitution")
    public ResponseBo<BaseSelectEntity> getInstitutionCoursePathwayByInstitution(@RequestBody InstitutionCourseDto institutionCourseDto) {
        List<BaseSelectEntity> institutionCourseDtos = institutionCourseService.getInstitutionCoursePathwayByInstitution(institutionCourseDto);
        return new ListResponseBo<>(institutionCourseDtos);
    }

    /**
     * 非桥梁课程下拉框数据
     *
     * @Date 17:36 2021/7/22
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "非桥梁课程下拉框数据", notes = "")
    @PostMapping("getNonBridgeInstitutionCoursePathwayByInstitution")
    public ResponseBo<BaseSelectEntity> getNonBridgeInstitutionCoursePathwayByInstitution(@RequestBody InstitutionCourseDto institutionCourseDto) {
        List<BaseSelectEntity> institutionCourseDtos = institutionCourseService.getNonBridgeInstitutionCoursePathwayByInstitution(institutionCourseDto);
        return new ListResponseBo<>(institutionCourseDtos);
    }

    /**
     * 新增信息
     *
     * @param resources
     * @return
     * @
     */
    @ApiOperation(value = "新增排名接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/课程管理/新增保存排名")
    @PostMapping("addInstitutionCourseRankings")
    public ResponseBo addInstitutionCourseRankings(@RequestBody @Validated(InstitutionCourseRankingDto.Add.class) ValidList<InstitutionCourseRankingDto> resources) {
        institutionCourseService.batchAddInstitutionCourseRanking(resources);
        return ResponseBo.ok();
    }

    /**
     * 排名列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "排名列表数据", notes = "")
    @PostMapping("getInstitutionCourseRankingDatas")
    public ResponseBo<InstitutionCourseRankingVo> getInstitutionCourseRankingDatas(@RequestBody SearchBean<InstitutionCourseRankingDto> page) {
        List<InstitutionCourseRankingVo> datas = institutionCourseService.getInstitutionCourseRankingDatas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 排名列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "特性列表数据", notes = "")
    @PostMapping("getCharacterDatas")
    public ResponseBo<CharacterVo> getCharacterDatas(@RequestBody SearchBean<CharacterDto> page) {
        List<CharacterVo> datas = institutionCourseService.getCharacterDatas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 批量新增信息
     *
     * @param resources
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/课程管理/批量保存")
    @PostMapping("batchAddCharacter")
    public ResponseBo batchAddCharacter(@RequestBody @Validated(CharacterDto.Add.class) ValidList<CharacterDto> resources) {
        institutionCourseService.batchAddCharacter(resources);
        return ResponseBo.ok();
    }

    /**
     * feign调用 通过id 获取学费
     *
     * @param id
     * @return
     */
/*    @ApiIgnore
    @GetMapping(value = "getFeeById")
    public BigDecimal getFeeById(@RequestParam(value = "id") Long id)  {
        return institutionCourseService.getFeeById(id);
    }*/

    /**
     * feign调用 通过ids 获取学费总和
     *
     * @param ids
     * @return
     */
  /*  @ApiIgnore
    @PostMapping(value = "getSumFeeByIds")
    public BigDecimal getSumFeeByIds(@RequestBody List<Long> ids)  {
        return institutionCourseService.getSumFeeByIds(ids);
    }*/

    /**
     * @Description :feign调用 根据课程ids查找对应课程名称map
     * @Param [institutionCourseIdSet]
     * <AUTHOR>
     */
/*    @ApiIgnore
    @PostMapping(value = "getInstitutionCourseNamesByIds")
    @VerifyLogin(IsVerify = false)
    public Map<Long, String> getInstitutionCourseNamesByIds(@RequestBody Set<Long> institutionCourseIdSet)  {
        return institutionCourseService.getInstitutionCourseNamesByIds(institutionCourseIdSet);
    }*/


    /**
     * feign调用 根据课程ids查找对应课程名称
     *
     * @Date 2:20 2021/6/19
     * <AUTHOR>
     */
  /*  @ApiIgnore
    @GetMapping(value = "getInstitutionCourseNameById")
    public String getInstitutionCourseNameById(@RequestParam(value = "id") Long id)  {
        return institutionCourseService.getInstitutionCourseNameById(id);
    }*/
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "检查获取课程链接", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/课程管理/检查获取课程链接")
    @GetMapping("/checkCourseWebsite")
    public ResponseBo<Long> checkCourseWebsite(@RequestParam("fkInstitutionId") Long fkInstitutionId, @RequestParam("courseWebsite") String courseWebsite) {
        Long courseId = institutionCourseService.checkCourseWebsite(fkInstitutionId, courseWebsite);
        return new ResponseBo<>(courseId);
    }

    /**
     * 根据输入关键字模糊查询课程列表
     *
     * @Date 2:20 2022/03/11
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据输入关键字模糊查询课程列表", notes = "")
    @PostMapping("getInstitutionCourseByCourseName")
    public ResponseBo InstitutionCourseByName(@RequestParam(value = "courseName") String courseName,@RequestParam(value="institutionId",required = false)Long institutionId) {
        List<Long> listInstitutionCourse = institutionCourseService.getInstitutionCourseByName(courseName,institutionId);
        return new ListResponseBo<>(listInstitutionCourse);
    }

    /**
     * 根据输入关键字模糊查询课程列表
     *
     * @Date 2:20 2022/03/11
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据输入关键字模糊查询课程列表", notes = "")
    @PostMapping("getInstitutionCourseListByName")
    public ResponseBo InstitutionCourseListByName(@RequestBody InstitutionCourseNameDto institutionCourseVo) {
        List<BaseSelectEntity> listInstitutionCourse = institutionCourseService.InstitutionCourseListByName(institutionCourseVo.getCourseName(),institutionCourseVo.getInstitutionIds(),institutionCourseVo.getCourseIds());
        return new ListResponseBo<>(listInstitutionCourse);
    }

    /**
     * 根据输入课程名模糊匹配学校课程
     *
     * @Date 2:20 2022/03/11
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "根据输入课程名模糊匹配学校课程", notes = "")
    @PostMapping("getInstitutionCourseByNameMatch")
    public ResponseBo<InstitutionCourseMatchVo> getInstitutionCourseByNameMatch(@RequestParam(value = "courseName") String courseName,
                                                                                @RequestParam(value="institutionId")Long institutionId) {

        List<InstitutionCourseMatchVo> listInstitutionCourse = institutionCourseService.getInstitutionCourseByNameMatch(courseName,institutionId);
        return new ListResponseBo<>(listInstitutionCourse);
    }


    /**
     * 根据级别名称模糊查询课程ids
     *
     * @param
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据级别和名称模糊查询课程ids", notes = "")
    @GetMapping("getInstitutionCourseIdsByLevelAndName")
    public List<Long> getInstitutionCourseIdsByLevelAndName(@RequestParam(value = "name") String name, @RequestParam(value = "levelId") Long levelId) {
        List<Long> datas = institutionCourseService.getInstitutionCourseIdsByLevelAndName(name, levelId);
        return datas;
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "课程下拉框数据（根据课程名字查询）", notes = "")
    @PostMapping("getInstitutionCourseListByCourseName")
    public ResponseBo<BaseSelectEntity> getInstitutionCourseList(@RequestBody InstitutionCourseNameSearchDto institutionCourseNameSearchDto) {
        List<BaseSelectEntity> datas = institutionCourseService.getInstitutionCourseList(institutionCourseNameSearchDto);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.NewsVo>
     * @Description: 课程新闻列表数据
     * @Param [page]
     * <AUTHOR>
     **/
    @ApiOperation(value = "课程科目列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/课程管理/课程科目列表数据")
    @PostMapping("getInstitutionCourseSubjectDatas")
    public ResponseBo<InstitutionCourseSubjectVo> getInstitutionCourseSubjectDatas(@RequestBody SearchBean<InstitutionCourseSubjectDto> page) {
        List<InstitutionCourseSubjectVo> datas = institutionCourseService.getInstitutionCourseSubjectDatas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

//    @VerifyPermission(IsVerify = false)
//    @ApiOperation(value = "课程下拉框数据（根据课程Ids查询）", notes = "")
//    @PostMapping("getInstitutionCourseListByIds")
//    public ResponseBo<BaseSelectEntity> getInstitutionCourseListByIds(@RequestBody InstitutionCourseNameSearchDto institutionCourseNameSearchVo) {
//        List<BaseSelectEntity> datas = institutionCourseService.getInstitutionCourseListByIds(institutionCourseNameSearchVo);
//        return new ListResponseBo<>(datas);
//    }
}
