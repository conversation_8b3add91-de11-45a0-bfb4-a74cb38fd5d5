package com.get.institutioncenter.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Data
public class InstitutionApplicationStaticsQueryBo {
    @ApiModelProperty(value = "排序方式：1-申请数；2-主课数；3-学生数；其他-成功入学（学生数）")
    private Integer sortedType;

    @ApiModelProperty(value = "当前登录人对应公司")
    private Long fkCompanyId;
    /**
     * 查询公司ID列表
     */
    @NotEmpty(message = "公司ID不能为空")
    @ApiModelProperty(value = "查询公司ID列表")
    private List<Long> fkCompanyIdList;
    /**
     * 国家ID
     */
    @ApiModelProperty(value = "国家ID")
    private Long fkAreaCountryId;
    /**
     * 学校模糊查询名称
     */
    @ApiModelProperty(value = "学校模糊查询名称")
    private String fkInstitutionName;

    /**
     * 学校提供商模糊查询名称
     */
    @ApiModelProperty(value = "学校提供商模糊查询名称")
    private String fkInstitutionProviderName;


    /**
     * 所属集团Id
     */
    @ApiModelProperty(value = "所属集团Id")
    private List<Long> fkInstitutionGroupIds;

    /**
     * 集团名称
     */
    @ApiModelProperty(value = "集团名称")
    private String groupName;

    /**
     * 当前申请步骤状态
     */
    @ApiModelProperty(value = "当前申请步骤状态")
    private List<Long> currentState;

    /**
     * 开学日期开始时间
     */
    @ApiModelProperty(value = "开学日期开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginOpeningTime;

    /**
     * 开学日期结束时间
     */
    @ApiModelProperty(value = "开学日期结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endOpeningTime;


    /**
     * 学校ID列表
     */
    @ApiModelProperty(value = "学校ID列表")
    private Set<Long> fkInstitutionIds;

    /**
     * 员工以及旗下员工所创建的代理ids
     */
    @ApiModelProperty(value = "员工以及旗下员工所创建的代理ids")
    private List<Long> staffFollowerIds;

    /**
     * 国家ID列表,员工（登录人）业务国家
     */
    @ApiModelProperty(value = "国家ID列表,员工（登录人）业务国家")
    private List<Long> fkAreaCountryIdList;

    /**
     * 申请创建开始时间
     */
    @ApiModelProperty(value = "申请创建开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    /**
     * 申请创建结束时间
     */
    @ApiModelProperty(value = "申请创建结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    /**
     * 首次成功入学开始时间
     */
    @ApiModelProperty(value = "首次成功入学开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date succTimeStart;

    /**
     * 首次成功入学结束时间
     */
    @ApiModelProperty(value = "首次成功入学结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date succTimeEnd;

    @ApiModelProperty(value = "统计方式:1-申请创建时间统计；2-首次成功入学时间统计")
    private Integer type;

    /**
     * 统计GEA成功量相关步骤列表枚举
     */
    @ApiModelProperty(value = "统计GEA成功量相关步骤列表枚举")
    private List<String> geaSuccessStatisticsStepList;

    /**
     * 业绩统计IAE成功量相关步骤列表枚举
     */
    @ApiModelProperty(value = "统计IAE成功量相关步骤列表枚举")
    private List<String> iaeSuccessStatisticsStepList;
}
