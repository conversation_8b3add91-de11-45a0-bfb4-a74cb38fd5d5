package com.get.salecenter.service;


import com.get.salecenter.dto.EventTargetAreaCountryDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/12/7 17:31
 * @verison: 1.0
 * @description:
 */
public interface IEventTargetAreaCountryService {

    /**
     * @return java.lang.Long
     * @Description :新增
     * @Param [eventTargetAreaCountryDto]
     * <AUTHOR>
     */
    Long addEventTargetAreaCountry(EventTargetAreaCountryDto eventTargetAreaCountryDto);

    /**
     * @return void
     * @Description :根据事件id删除
     * @Param [eventId]
     * <AUTHOR>
     */
    void deleteByEventId(Long eventId);

    /**
     * @return void
     * @Description :通过活动对象国家id 查找对应活动id
     * @Param [valueOf]
     * <AUTHOR>
     */
    List<Long> getEventIdsByCountryId(Long fkAreaCountryId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :根据全部活动id 查找对应所有的活动对象国家id
     * @Param [eventIds]
     * <AUTHOR>
     */
    List<Long> getCountryIdsByEventIds(List<Long> eventIds);

    /**
     *
     * @param eventTargetCountryList
     * @return
     */
    List<Long> getEventIdsByCountryIds(List<Long> eventTargetCountryList);
}
