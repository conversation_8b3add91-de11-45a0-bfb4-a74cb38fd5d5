package com.get.financecenter.vo;

import com.get.financecenter.entity.ExpenseClaimForm;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @author: Sea
 * @create: 2021/4/7 10:49
 * @verison: 1.0
 * @description:
 */
@Data
@Accessors(chain = true)
public class ExpenseClaimFormVo extends ExpenseClaimForm {
    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    /**
     * 报销人名称
     */
    @ApiModelProperty(value = "报销人名称")
    private String staffName;

    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String currencyTypeName;

    /**
     * 表单明细对象集合
     */
    @ApiModelProperty(value = "表单明细对象集合")
    private List<ExpenseClaimFormItemVo> expenseClaimFormItemDtoList;

    /**
     * 报销单状态 0待签1代办2无
     */
    @ApiModelProperty(value = "报销单状态 0待签1代办2无")
    private Integer expenseClaimFormStatus;

    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    private Long taskId;
    /**
     * 实例id
     */
    @ApiModelProperty(value = "实例id")
    private Long procInstId;

    /**
     * 任务版本号
     */
    @ApiModelProperty(value = "任务版本号")
    private Integer taskVersion;

    /**
     * 报销金额总和
     */
    @ApiModelProperty(value = "报销金额总和")
    private BigDecimal amountSum;

    /**
     * 同意按钮状态
     */
    @ApiModelProperty(value = "同意按钮状态")
    private Boolean agreeButtonType;

    /**
     * 拒绝按钮状态
     */
    @ApiModelProperty(value = "拒绝按钮状态")
    private Boolean refuseButtonType;

    @ApiModelProperty(value = "创建凭证人名字")
    private String fkStaffIdVouchCreatedName;


}
