<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionInfoMapper">
  <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.InstitutionInfo">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_info_type_id" jdbcType="BIGINT" property="fkInfoTypeId" />
    <result column="fk_institution_id" jdbcType="BIGINT" property="fkInstitutionId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="view_order" jdbcType="INTEGER" property="viewOrder" />
    <result column="public_level" jdbcType="VARCHAR" property="publicLevel" />
    <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime" />
    <result column="profile" jdbcType="VARCHAR" property="profile" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
    <result column="web_title" jdbcType="VARCHAR" property="webTitle" />
    <result column="web_meta_description" jdbcType="VARCHAR" property="webMetaDescription" />
    <result column="web_meta_keywords" jdbcType="VARCHAR" property="webMetaKeywords" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.get.institutioncenter.entity.InstitutionInfo">
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
  </resultMap>
  <sql id="Blob_Column_List">
    description
  </sql>
  <insert id="insert" parameterType="com.get.institutioncenter.entity.InstitutionInfo" keyProperty="id" useGeneratedKeys="true">
    insert into m_institution_info (id, fk_info_type_id, fk_institution_id, 
      title, view_order, public_level, profile,
      gmt_create, gmt_create_user, gmt_modified, publish_time,
      gmt_modified_user, description, web_title, web_meta_description, web_meta_keywords)
    values (#{id,jdbcType=BIGINT}, #{fkInfoTypeId,jdbcType=BIGINT}, #{fkInstitutionId,jdbcType=BIGINT}, 
      #{title,jdbcType=VARCHAR}, #{viewOrder,jdbcType=INTEGER}, #{publicLevel,jdbcType=VARCHAR}, #{profile,jdbcType=VARCHAR},
            #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP},#{publishTime,jdbcType=TIMESTAMP},
      #{gmtModifiedUser,jdbcType=VARCHAR}, #{description,jdbcType=LONGVARCHAR}, #{webTitle,jdbcType=VARCHAR},
    #{webMetaDescription,jdbcType=VARCHAR},#{webMetaKeywords,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionInfo" keyProperty="id" useGeneratedKeys="true">
    insert into m_institution_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkInfoTypeId != null">
        fk_info_type_id,
      </if>
      <if test="fkInstitutionId != null">
        fk_institution_id,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="publicLevel != null">
        public_level,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="publishTime != null">
        publish_time,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="webTitle != null">
        web_title,
      </if>
      <if test="webMetaDescription != null">
        web_meta_description,
      </if>
      <if test="webMetaKeywords != null">
        web_meta_keywords,
      </if>
      <if test="profile != null">
        profile,
    </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkInfoTypeId != null">
        #{fkInfoTypeId,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionId != null">
        #{fkInstitutionId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="publicLevel != null">
        #{publicLevel,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="publishTime != null">
        #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="webTitle != null">
        #{webTitle,jdbcType=VARCHAR},
      </if>
      <if test="webMetaDescription != null">
        #{webMetaDescription,jdbcType=VARCHAR},
      </if>
      <if test="webMetaKeywords != null">
        #{webMetaKeywords,jdbcType=VARCHAR},
      </if>
      <if test="profile != null">
        #{profile,jdbcType=LONGVARCHAR},
      </if>

    </trim>
  </insert>
  <select id="datas" resultMap="BaseResultMap">
    select * from m_institution_info
    <where>
      <if test="institutionInfoDto.fkInstitutionId!=null and institutionInfoDto.fkInstitutionId !=''">
        and fk_institution_id = #{institutionInfoDto.fkInstitutionId}
      </if>
      <if test="institutionInfoDto.fkInfoTypeId!=null and institutionInfoDto.fkInfoTypeId !=''">
        and fk_info_type_id = #{institutionInfoDto.fkInfoTypeId}
      </if>
      <if test="institutionInfoDto.publicLevel!=null">
        and  position(#{institutionInfoDto.publicLevel} in public_level)
      </if>
      <if test="institutionInfoDto.keyWord!=null and institutionInfoDto.keyWord !=''">
        and (position(#{institutionInfoDto.keyWord} in description) or  position(#{institutionInfoDto.keyWord} in title))
      </if>
    </where>
    order by view_order desc,fk_info_type_id,gmt_create desc
  </select>
    <select id="getInstitutionInfoIds" resultType="java.lang.Long">
      select id from m_institution_info where ${columnName} is not NULL and ${columnName} != ""
    </select>
  <select id="getInstitutionInfoById" resultType="com.get.institutioncenter.entity.InstitutionInfo">
    select * from m_institution_info where id = #{id}
  </select>
</mapper>