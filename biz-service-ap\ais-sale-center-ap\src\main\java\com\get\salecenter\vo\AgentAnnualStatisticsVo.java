package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 代理年度统计
 *
 * <AUTHOR>
 * @date 2023/3/17 11:21
 */
@Data
public class AgentAnnualStatisticsVo {

    @ApiModelProperty(value = "bdId")
    private Long bdId;

    @ApiModelProperty(value = "月份")
    private int month;

    @ApiModelProperty(value = "年份")
    private int year;

    @ApiModelProperty(value = "代理数量/通过审核数")
    private Long agentNum;

    @ApiModelProperty(value = "申请总数（通过审核 + 审核中 + 拒绝）")
    private Long applicationsNum;

    @ApiModelProperty(value = "审核中 数(新申请+审核中)")
    private Long inReviewNum;

    @ApiModelProperty(value = "拒绝 数")
    private Long refuseNum;

    @ApiModelProperty(value = "合同未返回数")
    private Long unsignedNum;

}
