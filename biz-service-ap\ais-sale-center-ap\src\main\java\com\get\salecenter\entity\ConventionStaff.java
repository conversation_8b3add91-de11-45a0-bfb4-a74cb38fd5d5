package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_convention_staff")
public class ConventionStaff extends BaseEntity implements Serializable {
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    @Column(name = "fk_convention_id")
    private Long fkConventionId;

    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;

    /**
     * 权限模式：0禁止进入/1允许进入
     */
    @ApiModelProperty(value = "权限模式：0禁止进入/1允许进入")
    @Column(name = "mode")
    private Integer mode;

    private static final long serialVersionUID = 1L;
}