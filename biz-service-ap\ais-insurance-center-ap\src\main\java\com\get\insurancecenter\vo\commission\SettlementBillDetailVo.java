package com.get.insurancecenter.vo.commission;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/6/13
 * @Version 1.0
 * @apiNote:对账单详细
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementBillDetailVo {

    @ApiModelProperty(value = "对账单Id")
    private Long settlementBillId;

    @ApiModelProperty(value = "结算币种")
    private String settlementCurrencyTypeNum;

    @ApiModelProperty(value = "总计结算金额-结算币种")
    private BigDecimal totalSettlementAmount;

    @ApiModelProperty(value = "订单币种")
    private String orderCurrencyTypeNum;

    @ApiModelProperty(value = "总计结算金额-订单币种")
    private BigDecimal originTotalSettlementAmount;

    @ApiModelProperty(value = "汇率")
    private BigDecimal rate;

    @ApiModelProperty(value = "签名")
    private String signature;

    @ApiModelProperty(value = "订单明细")
    private List<SettlementBillItemVo> billItems;

    @ApiModelProperty(value = "代理账户")
    private AgentAccountVo agentAccount;
}
