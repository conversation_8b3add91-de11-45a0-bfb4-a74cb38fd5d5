package com.get.filecenter.service;

import com.get.filecenter.dto.SaleFileDto;
import com.get.filecenter.vo.FileVo;
import com.qcloud.cos.model.Bucket;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface ITencentCloudService {
    /**
     * @describe 上传文件的方法
     * @methods uploadObject 方法名
     * @parameter fileUrl 上传文件地址
     * @parameter fileKey 文件对象名称
     * @parameter @return 对象列表
     */
    Boolean uploadObject(boolean isPub,String bucketName, MultipartFile file, String fileKey);

    /**
     * 改存储桶已经在前端界面进行创建
     */
    void createCosBucket(String bucketName);

    /**
     * 查询存储桶列表
     *
     * @return
     */
    List<Bucket> queryBucketList(String bucketName);

    /**
     * 下载对象,isPub:是否从公开桶下载（下载不同的桶区分）
     */
    void downLoadObject(FileVo fileVo, HttpServletResponse response,Boolean isPub,Boolean isShare,Boolean isHtiPub);

    /**
     * 删除对象
     */
    void deleteObjectRequest(String key);

    /**
     * 同步服务器文件到腾讯云
     */
    void synchronizeFiles() throws IOException;

    /**
     * 下载文件
     *
     * @param fileVo
     * @return
     */
    SaleFileDto getDownloadFile(FileVo fileVo);
}
