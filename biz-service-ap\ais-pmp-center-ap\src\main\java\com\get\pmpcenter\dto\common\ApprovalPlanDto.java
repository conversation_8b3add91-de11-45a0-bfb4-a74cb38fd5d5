package com.get.pmpcenter.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/3/24
 * @Version 1.0
 * @apiNote:审核合同DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ApprovalPlanDto {

    @ApiModelProperty(value = "审批状态：2通过/3拒绝/4-驳回合同方案审批(仅代理端审核才有此选项)")
    @NotNull(message = "审批状态不能为空")
    private Integer approvalStatus;

    @ApiModelProperty(value = "方案ID")
    @NotNull(message = "方案ID不能为空")
    private Long planId;

    @ApiModelProperty(value = "审批意见")
    private String approvalComment;

    @ApiModelProperty(value = "审批附件文件集合")
    private List<MediaDto> mediaList;
}
