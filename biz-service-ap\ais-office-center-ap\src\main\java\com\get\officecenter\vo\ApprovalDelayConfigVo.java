package com.get.officecenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Hardy
 * @create: 2023/6/8 12:19
 * @verison: 1.0
 * @description:
 */
@Data
public class ApprovalDelayConfigVo extends BaseVoEntity {

    @ApiModelProperty("是否已经上传附件true已上传/false未上传")
    private Boolean hasUpdateAttachment;

    @ApiModelProperty("允许延迟的时间（小时）")
    private Integer delayHours;
}
