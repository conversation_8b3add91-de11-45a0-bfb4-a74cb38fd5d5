package com.get.mpscenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@TableName("r_institution_provider_mps_commission_area_country")
@Data
public class InstitutionProviderMpsCommissionAreaCountry extends BaseEntity implements Serializable {

    @ApiModelProperty("学校提供商佣金Id")
    private Long fkInstitutionProviderMpsCommissionId;

    @ApiModelProperty("业务国家Id")
    private Long fkAreaCountryId;

    @ApiModelProperty("是否包括：1包括/-1除外")
    private Boolean isInclude;

}
