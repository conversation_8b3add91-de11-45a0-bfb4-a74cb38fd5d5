package com.get.pmpcenter.dto.agent;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/3/24
 * @Version 1.0
 * @apiNote:审核代理方案DTO
 */
@Data
public class ApprovalAgentCompanyDto {

    @ApiModelProperty(value = "审批状态：2通过/3拒绝")
    @NotNull(message = "审批状态不能为空")
    private Integer approvalStatus;

    @ApiModelProperty(value = "提供商ID")
    @NotNull(message = "提供商ID不能为空")
    private Long institutionProviderId;

    @ApiModelProperty(value = "分公司ID")
    @NotNull(message = "分公司ID不能为空")
    private Long companyId;

    @ApiModelProperty(value = "审批意见")
    private String approvalComment;
}
