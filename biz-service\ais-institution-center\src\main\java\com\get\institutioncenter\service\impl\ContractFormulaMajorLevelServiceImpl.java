package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.ContractFormulaMajorLevelMapper;
import com.get.institutioncenter.entity.ContractFormulaMajorLevel;
import com.get.institutioncenter.service.IContractFormulaMajorLevelService;
import com.get.institutioncenter.service.IMajorLevelService;
import com.get.institutioncenter.dto.ContractFormulaMajorLevelDto;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/1/8 10:28
 * @verison: 1.0
 * @description:
 */
@Service
public class ContractFormulaMajorLevelServiceImpl extends BaseServiceImpl<ContractFormulaMajorLevelMapper, ContractFormulaMajorLevel> implements IContractFormulaMajorLevelService {
    @Resource
    private ContractFormulaMajorLevelMapper contractFormulaMajorLevelMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IMajorLevelService majorLevelService;

    @Override
    public Long addContractFormulaMajorLevel(ContractFormulaMajorLevelDto contractFormulaMajorLevelDto) {
        if (GeneralTool.isEmpty(contractFormulaMajorLevelDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ContractFormulaMajorLevel contractFormulaMajorLevel = BeanCopyUtils.objClone(contractFormulaMajorLevelDto, ContractFormulaMajorLevel::new);
        utilService.updateUserInfoToEntity(contractFormulaMajorLevel);
        contractFormulaMajorLevelMapper.insertSelective(contractFormulaMajorLevel);
        return contractFormulaMajorLevel.getId();
    }

    @Override
    public void deleteByFkid(Long contractFormulaId) {
        LambdaQueryWrapper<ContractFormulaMajorLevel> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContractFormulaMajorLevel::getFkContractFormulaId, contractFormulaId);
        contractFormulaMajorLevelMapper.delete(wrapper);
    }

    @Override
    public List<Long> getMajorLevelIdListByFkid(Long contractFormulaId) {
        return contractFormulaMajorLevelMapper.getMajorLevelIdListByFkid(contractFormulaId);
    }

    @Override
    public String getMajorLevelNameByFkid(Long contractFormulaId) {
        List<String> countryNameList = contractFormulaMajorLevelMapper.getMajorLevelNameByFkid(contractFormulaId);
        String result = "";
        if (GeneralTool.isNotEmpty(countryNameList)) {
            result = StringUtils.join(countryNameList, ",");
        }
        return result;
    }
}
