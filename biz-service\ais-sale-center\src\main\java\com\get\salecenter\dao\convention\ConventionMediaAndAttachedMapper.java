package com.get.salecenter.dao.convention;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.entity.SaleMediaAndAttached;
import com.get.salecenter.entity.convention.ConventionMediaAndAttached;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author:cream
 * @Date: 2023/5/12  11:26
 */
@Mapper
@DS("conventiondb")
public interface ConventionMediaAndAttachedMapper extends GetMapper<ConventionMediaAndAttached> {

    Integer getNextIndexKey(@Param("tableId") Long tableId, @Param("tableName") String tableName);
}
