package com.get.officecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: Sea
 * @create: 2020/8/14 10:09
 * @verison: 1.0
 * @description:
 */
@Data
@ApiModel(value = "媒体附件VO")
public class MediaAndAttachedDto extends BaseVoEntity {
    @NotBlank(message = "文件路径不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "文件路径", required = true)
    private String filePath;

    /**
     * 源文件名
     */
    @NotBlank(message = "文件名不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "源文件名", required = true)
    private String fileNameOrc;

    /**
     * 文件外部存储Key（如：腾讯云COS）
     */
    @ApiModelProperty(value = "文件外部存储Key（如：腾讯云COS）")
    private String fileKey;
    /**
     * 文件guid(文档中心)
     */
    @NotBlank(message = "文件guid不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "文件guid(文档中心)", required = true)
    private String fkFileGuid;

    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;

    /**
     * 表Id
     */
    @NotNull(message = "表Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "表Id", required = true)
    private Long fkTableId;

    /**
     * 类型关键字，如：institution_mov/institution_pic/alumnus_head_icon
     */
    @NotBlank(message = "类型关键字不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "类型关键字，如：institution_mov/institution_pic/alumnus_head_icon", required = true)
    private String typeKey;

    /**
     * 索引值(默认从0开始，同一类型下值唯一)
     */
    @ApiModelProperty(value = "索引值(默认从0开始，同一类型下值唯一)")
    private Integer indexKey;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 链接
     */
    @ApiModelProperty(value = "链接")
    private String link;
}
