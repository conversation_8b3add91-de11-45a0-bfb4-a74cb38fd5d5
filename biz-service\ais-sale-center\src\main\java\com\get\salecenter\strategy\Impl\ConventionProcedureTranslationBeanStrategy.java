package com.get.salecenter.strategy.Impl;

import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.salecenter.entity.ConventionProcedure;
import com.get.salecenter.service.TranslationMappingService;
import com.get.salecenter.strategy.TranslationBeanStrategy;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @author: Hardy
 * @create: 2024/2/4 10:53
 * @verison: 1.0
 * @description:
 */
@Component
public class ConventionProcedureTranslationBeanStrategy implements TranslationBeanStrategy {

    private static final String TYPE = TableEnum.SALE_CONVENTION_PROCEDURE.key;

    @Override
    public String getType() {
        return TYPE;
    }

    @Override
    public Map<String, Object> fromJavaBean(Long fkTableId, TranslationMappingService translationMappingService) {
        return translationMappingService.getConventionProcedureTranslation(fkTableId);
    }
}
