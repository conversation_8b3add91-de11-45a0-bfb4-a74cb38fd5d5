package com.get.permissioncenter.service;


import com.get.core.mybatis.base.BaseService;
import com.get.permissioncenter.dto.BatchModifyConfigDto;
import com.get.permissioncenter.vo.BatchModifyConfigVo;
import com.get.permissioncenter.entity.BatchModifyConfig;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/2/22 17:12
 * @verison: 1.0
 * @description:
 */
public interface IBatchModifyConfigService extends BaseService<BatchModifyConfig> {

    /**
     * @return java.util.List<com.get.systemcenter.vo.BatchModifyConfigVo>
     * @Description :加载批量修改项接口
     * @Param [batchModifyConfigDto]
     * <AUTHOR>
     */
    List<BatchModifyConfigVo> getBatchUpdateItems(BatchModifyConfigDto batchModifyConfigDto);

    /**
     * @return com.get.systemcenter.vo.BatchModifyConfigVo
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    BatchModifyConfigVo findbatchModifyConfigById(Long id);
}
