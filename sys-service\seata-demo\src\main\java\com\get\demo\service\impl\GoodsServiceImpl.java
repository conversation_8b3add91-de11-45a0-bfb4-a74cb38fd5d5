package com.get.demo.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.demo.entity.Goods;
import com.get.demo.mapper.GoodsMapper;
import com.get.demo.service.IGoodsService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-07
 */
@Service
public class GoodsServiceImpl extends ServiceImpl<GoodsMapper, Goods> implements IGoodsService {

    @Override
    public Boolean updateByDemo() {
        Goods goods = this.getById(1);
        this.update(Wrappers.<Goods>lambdaUpdate().set(Goods::getStock, goods.getStock() + 1).eq(Goods::getId, 1));
        return true;
    }
}
