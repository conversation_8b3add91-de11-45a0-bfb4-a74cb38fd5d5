<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hti-java-ais</artifactId>
        <groupId>com.get</groupId>
        <version>1.0.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>common</artifactId>
    <name>${project.artifactId}</name>
    <version>${get.project.version}</version>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-mybatis</artifactId>
            <version>${get.project.version}</version>
        </dependency>
        <!--httpClient -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.9</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.9</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-start</artifactId>
            <version>${get.project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-ribbon</artifactId>
            <version>${get.project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-auto</artifactId>
            <version>${get.project.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-core</artifactId>
            <version>1.3.2</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>net.sf.json-lib</groupId>
            <artifactId>json-lib</artifactId>
            <version>2.4</version>
            <classifier>jdk15</classifier>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.5.20</version>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-secure</artifactId>
            <version>${get.project.version}</version>
            <scope>compile</scope>
        </dependency>
        <!--        <dependency>-->
<!--            <groupId>com.get</groupId>-->
<!--            <artifactId>core-log</artifactId>-->
<!--        </dependency>-->
        <!--        <dependency>-->
<!--            <groupId>jakarta.validation</groupId>-->
<!--            <artifactId>jakarta.validation-api</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.get</groupId>-->
<!--            <artifactId>core-tool</artifactId>-->
<!--        </dependency>-->
        <!--        <dependency>-->
<!--            <groupId>com.get</groupId>-->
<!--            <artifactId>core-log</artifactId>-->
<!--        </dependency>-->
    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                    <finalName>${project.name}</finalName>
                </configuration>
            </plugin>

            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>1.2.2</version>
                <configuration>
                    <skipDockerBuild>true</skipDockerBuild>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>