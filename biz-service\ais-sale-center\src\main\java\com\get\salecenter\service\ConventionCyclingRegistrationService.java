package com.get.salecenter.service;


import com.get.common.result.DeleteResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.salecenter.vo.CyclingRegistrationVo;
import com.get.salecenter.dto.CyclingRegistrationDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/14 11:14
 */
public interface ConventionCyclingRegistrationService {

    /**
     * 公益骑行列表
     *
     * @Date 11:31 2021/10/14
     * <AUTHOR>
     */
    List<CyclingRegistrationVo> getCyclingRegistrationList(CyclingRegistrationDto cyclingRegistrationDto);

    /**
     * 导出公益骑行列表
     * <AUTHOR>
     * @DateTime 2023/4/13 15:08
     */
    void exportCyclingRegistrationExcel(CyclingRegistrationDto cyclingRegistrationDto, HttpServletResponse response);


    /**
     * 增加编辑备注
     * @param id
     * @param remark
     * @return
     */
    SaveResponseBo editRemark(Long id,String remark);

    /**
     * 删除备注
     * @param id
     * @return
     */
    DeleteResponseBo delete(Long id);

    /**
     * 更新支付状态
     * @param id
     * @param status
     * @return
     */
    SaveResponseBo updatePayStatus(Long id,Integer status);

}
