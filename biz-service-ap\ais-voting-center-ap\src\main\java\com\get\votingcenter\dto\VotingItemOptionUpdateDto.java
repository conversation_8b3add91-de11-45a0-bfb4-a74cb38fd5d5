package com.get.votingcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/9/26 16:43
 * @verison: 1.0
 * @description:
 */
@Data
public class VotingItemOptionUpdateDto extends BaseVoEntity implements Serializable {
    /**
     * 投票项Id
     */
    @ApiModelProperty(value = "投票项Id")
    @NotNull(message = "投票项Id不能为空", groups = {Add.class})
    private Long fkVotingItemId;

    /**
     * 选项名称
     */
    @ApiModelProperty(value = "选项名称")
    private String name;

    /**
     * 选项副名称
     */
    @ApiModelProperty(value = "选项副名称")
    private String nameSub;

    /**
     * 投票选项图片
     */
    @ApiModelProperty(value = "投票选项图片", required = true)
    private List<MediaAndAttachedDto> mediaAttachedVos;
}
