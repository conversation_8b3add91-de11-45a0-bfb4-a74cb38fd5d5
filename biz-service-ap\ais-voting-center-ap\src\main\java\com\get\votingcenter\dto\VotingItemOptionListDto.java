package com.get.votingcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: Hardy
 * @create: 2021/9/26 16:43
 * @verison: 1.0
 * @description:
 */
@Data
public class VotingItemOptionListDto extends BaseVoEntity implements Serializable {

    /**
     * 投票项id
     */
    @ApiModelProperty(value = "投票项id")
    private Long fkVotingItemId;

    /**
     * 选项名称
     */
    @ApiModelProperty(value = "选项名称")
    private String name;

    /**
     * 选项副名称
     */
    @ApiModelProperty(value = "选项副名称")
    private String nameSub;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer viewOrder;

}
