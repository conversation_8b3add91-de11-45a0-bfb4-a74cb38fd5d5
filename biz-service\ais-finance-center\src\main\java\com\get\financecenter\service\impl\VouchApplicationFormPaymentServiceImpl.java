package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.BeanUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.AccountingItemMapper;
import com.get.financecenter.dao.ExpenseClaimFormMapper;
import com.get.financecenter.dao.InvoiceMapper;
import com.get.financecenter.dao.PaymentApplicationFormItemMapper;
import com.get.financecenter.dao.PaymentApplicationFormMapper;
import com.get.financecenter.dao.PaymentFeeTypeMapper;
import com.get.financecenter.dao.PaymentMethodTypeMapper;
import com.get.financecenter.dao.PrepayApplicationFormMapper;
import com.get.financecenter.dao.TravelClaimFormMapper;
import com.get.financecenter.dao.VouchApplicationFormPaymentMapper;
import com.get.financecenter.dao.VouchItemMapper;
import com.get.financecenter.dao.VouchMapper;
import com.get.financecenter.dao.VouchOperationConfigMapper;
import com.get.financecenter.dto.VouchApplicationFormPaymentAddDto;
import com.get.financecenter.dto.VouchApplicationFormPaymentQueryDto;
import com.get.financecenter.entity.AccountingItem;
import com.get.financecenter.entity.ExpenseClaimForm;
import com.get.financecenter.entity.PaymentApplicationForm;
import com.get.financecenter.entity.PaymentApplicationFormItem;
import com.get.financecenter.entity.PaymentFeeType;
import com.get.financecenter.entity.PaymentMethodType;
import com.get.financecenter.entity.PrepayApplicationForm;
import com.get.financecenter.entity.TravelClaimForm;
import com.get.financecenter.entity.Vouch;
import com.get.financecenter.entity.VouchApplicationFormPayment;
import com.get.financecenter.entity.VouchItem;
import com.get.financecenter.entity.VouchOperationConfig;
import com.get.financecenter.service.VouchApplicationFormPaymentService;
import com.get.financecenter.vo.PaymentMethodTypeSelectEntity;
import com.get.financecenter.vo.VouchApplicationFormPaymentVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 付款
 */
@Service
public class VouchApplicationFormPaymentServiceImpl implements VouchApplicationFormPaymentService {

    @Resource
    private VouchApplicationFormPaymentMapper vouchApplicationFormPaymentMapper;
    @Resource
    private VouchOperationConfigMapper vouchOperationConfigMapper;
    @Resource
    private VouchItemMapper vouchItemMapper;
    @Resource
    private VouchMapper vouchMapper;
    @Resource
    private InvoiceMapper invoiceMapper;
    @Resource
    private AccountingItemMapper accountingItemMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private PrepayApplicationFormMapper prepayApplicationFormMapper;
    @Resource
    private PaymentMethodTypeMapper paymentMethodTypeMapper;
    @Resource
    private ExpenseClaimFormMapper expenseClaimFormMapper;
    @Resource
    private TravelClaimFormMapper travelClaimFormMapper;
    @Resource
    private PaymentApplicationFormMapper paymentApplicationFormMapper;
    @Resource
    private PaymentApplicationFormItemMapper paymentApplicationFormItemMapper;
    @Resource
    private PaymentFeeTypeMapper paymentFeeTypeMapper;

    /**
     * 付款列表
     *
     * @param vouchApplicationFormPaymentQueryDto
     * @param page
     * @return
     */
    @Override
    public List<VouchApplicationFormPaymentVo> vouchApplicationFormPaymentDatas(VouchApplicationFormPaymentQueryDto vouchApplicationFormPaymentQueryDto, Page page) {
        IPage<VouchApplicationFormPaymentVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<VouchApplicationFormPaymentVo> vouchApplicationFormPaymentVoList = vouchApplicationFormPaymentMapper.getVouchApplicationFormPaymentDatas(iPage, vouchApplicationFormPaymentQueryDto);
        page.setAll((int) iPage.getTotal());
        return vouchApplicationFormPaymentVoList;
    }

    /**
     * 创建付款记录
     *
     * @param vouchApplicationFormPaymentAddDto
     */
    @Override
    @Transactional
    public void addVouchApplicationFormPayment(VouchApplicationFormPaymentAddDto vouchApplicationFormPaymentAddDto) {
        //表单号
        String num = "";
        //申请人
        Long staffId = null;
        if (vouchApplicationFormPaymentAddDto.getFkTableName().equals(TableEnum.FINANCE_PREPAY_APPLICATION_FORM.key)) {
            PrepayApplicationForm prepayApplicationForm = prepayApplicationFormMapper.selectById(vouchApplicationFormPaymentAddDto.getFkTableId());
            if (GeneralTool.isEmpty(prepayApplicationForm)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("id_is_null"));
            }
            num = prepayApplicationForm.getNum();
            staffId = prepayApplicationForm.getFkStaffId();
        } else if (vouchApplicationFormPaymentAddDto.getFkTableName().equals(TableEnum.FINANCE_EXPENSE_CLAIM_FORM.key)) {
            ExpenseClaimForm expenseClaimForm = expenseClaimFormMapper.selectById(vouchApplicationFormPaymentAddDto.getFkTableId());
            if (GeneralTool.isEmpty(expenseClaimForm)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("id_is_null"));
            }
            num = expenseClaimForm.getNum();
            staffId = expenseClaimForm.getFkStaffId();
        } else if (vouchApplicationFormPaymentAddDto.getFkTableName().equals(TableEnum.FINANCE_TRAVEL_CLAIM_FORM.key)) {
            TravelClaimForm travelClaimForm = travelClaimFormMapper.selectById(vouchApplicationFormPaymentAddDto.getFkTableId());
            if (GeneralTool.isEmpty(travelClaimForm)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("id_is_null"));
            }
            num = travelClaimForm.getNum();
            staffId = travelClaimForm.getFkStaffId();
        } else if (vouchApplicationFormPaymentAddDto.getFkTableName().equals(TableEnum.FINANCE_PAYMENT_APPLICATION_FORM.key)) {
            PaymentApplicationForm paymentApplicationForm = paymentApplicationFormMapper.selectById(vouchApplicationFormPaymentAddDto.getFkTableId());
            if (GeneralTool.isEmpty(paymentApplicationForm)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("id_is_null"));
            }
            num = paymentApplicationForm.getNum();
            staffId = paymentApplicationForm.getFkStaffId();
        }

        //创建凭证
        Vouch vouch = new Vouch();
        //凭证号：银付-202504-000279
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
        //凭证月份
        String vouchMonth = now.format(formatter);
        int maxVouchQty = invoiceMapper.getMaxVouchQty(vouchMonth);
        //凭证当月数量累计（6位补0）
        String vouchQty = String.format("%06d", maxVouchQty);
        String vouchNum = vouchApplicationFormPaymentAddDto.getPaymentKey() + "-" + vouchMonth + "-" + vouchQty;
        vouch.setVouchNum(vouchNum);
        vouch.setVouchType(vouchApplicationFormPaymentAddDto.getPaymentKey());
        vouch.setVouchMonth(vouchMonth);
        vouch.setVouchQty(maxVouchQty);
        vouch.setBusinessDate(vouchApplicationFormPaymentAddDto.getPaymentDate());
        StringBuilder remark = new StringBuilder();
        if (vouchApplicationFormPaymentAddDto.getFkTableName().equals(TableEnum.FINANCE_PREPAY_APPLICATION_FORM.key)) {
            remark.append("创建借款申请单付款凭证，表单号[");
        } else if (vouchApplicationFormPaymentAddDto.getFkTableName().equals(TableEnum.FINANCE_EXPENSE_CLAIM_FORM.key)) {
            remark.append("创建费用报销单付款凭证，表单号[");
        } else if (vouchApplicationFormPaymentAddDto.getFkTableName().equals(TableEnum.FINANCE_TRAVEL_CLAIM_FORM.key)) {
            remark.append("创建差旅报销单付款凭证，表单号[");
        } else if (vouchApplicationFormPaymentAddDto.getFkTableName().equals(TableEnum.FINANCE_PAYMENT_APPLICATION_FORM.key)) {
            remark.append("创建支付申请单付款凭证，表单号[");
        }

        remark.append(num).append("]，申请人[").append(SecureUtil.getStaffName()).append("]，").append(vouchApplicationFormPaymentAddDto.getRemark());
        vouch.setRemark(remark.toString());
        vouch.setFkCompanyId(SecureUtil.getFkCompanyId());
        vouch.setCreateType("system");
        vouch.setIsActive(true);
        utilService.setCreateInfo(vouch);
        vouchMapper.insert(vouch);

        //科目id
        Long fkAccountingItemId;
        String relationTargetKey = null;
        //凭证明细 借
        if (vouchApplicationFormPaymentAddDto.getFkTableName().equals(TableEnum.FINANCE_PAYMENT_APPLICATION_FORM.key)) {
            PaymentApplicationFormItem paymentApplicationFormItem = paymentApplicationFormItemMapper.selectById(vouchApplicationFormPaymentAddDto.getFkTableItemId());
            PaymentFeeType paymentFeeType = paymentFeeTypeMapper.selectById(paymentApplicationFormItem.getFkPaymentFeeTypeId());
            fkAccountingItemId = paymentFeeType.getFkAccountingItemId();
        } else {
            String operationKey = null;
            if (vouchApplicationFormPaymentAddDto.getFkTableName().equals(TableEnum.FINANCE_PREPAY_APPLICATION_FORM.key)) {
                operationKey = "PAFPaymentVouchDrAccounting";
            } else if (vouchApplicationFormPaymentAddDto.getFkTableName().equals(TableEnum.FINANCE_EXPENSE_CLAIM_FORM.key)) {
                operationKey = "ECFPaymentVouchDrAccounting";
            } else if (vouchApplicationFormPaymentAddDto.getFkTableName().equals(TableEnum.FINANCE_TRAVEL_CLAIM_FORM.key)) {
                operationKey = "TCFPaymentVouchDrAccounting";
            }
            VouchOperationConfig vouchOperationConfig = vouchOperationConfigMapper.selectOne(Wrappers.<VouchOperationConfig>lambdaQuery().eq(VouchOperationConfig::getOperationKey, operationKey));
            if (GeneralTool.isEmpty(vouchOperationConfig)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("NO_VOUCH_OPERATION_CONFIG"));
            }
            fkAccountingItemId = vouchOperationConfig.getFkAccountingItemId();
            relationTargetKey = vouchOperationConfig.getRelationTargetKey();
        }
        //科目
        AccountingItem accountingItem = accountingItemMapper.selectById(fkAccountingItemId);
        //子凭证
        VouchItem vouchItem = new VouchItem();
        vouchItem.setFkVouchId(vouch.getId());
        vouchItem.setSummary(accountingItem.getCodeName());
        vouchItem.setFkAccountingItemId(fkAccountingItemId);
        vouchItem.setFkCurrencyTypeNum(vouchApplicationFormPaymentAddDto.getFkCurrencyTypeNum());
        vouchItem.setAmountDr(vouchApplicationFormPaymentAddDto.getAmount());
        if (vouchApplicationFormPaymentAddDto.getFkTableName().equals(TableEnum.FINANCE_PREPAY_APPLICATION_FORM.key) ||
                vouchApplicationFormPaymentAddDto.getFkTableName().equals(TableEnum.FINANCE_EXPENSE_CLAIM_FORM.key) ||
                vouchApplicationFormPaymentAddDto.getFkTableName().equals(TableEnum.FINANCE_TRAVEL_CLAIM_FORM.key)) {
            if (GeneralTool.isNotEmpty(relationTargetKey) && TableEnum.PERMISSION_STAFF.key.equals(relationTargetKey)) {
                vouchItem.setRelationTargetKey(TableEnum.PERMISSION_STAFF.key);
                vouchItem.setRelationTargetId(staffId);
            }
        } else {
            PaymentApplicationFormItem paymentApplicationFormItem = paymentApplicationFormItemMapper.selectById(vouchApplicationFormPaymentAddDto.getFkTableItemId());
            vouchItem.setRelationTargetKey(paymentApplicationFormItem.getRelationTargetKey());
            vouchItem.setRelationTargetId(paymentApplicationFormItem.getRelationTargetId());
        }
        utilService.setCreateInfo(vouchItem);
        vouchItemMapper.insert(vouchItem);

        //凭证明细 贷
        fkAccountingItemId = vouchApplicationFormPaymentAddDto.getFkAccountingItemId();
        accountingItem = accountingItemMapper.selectById(fkAccountingItemId);
        vouchItem = new VouchItem();
        vouchItem.setFkVouchId(vouch.getId());
        vouchItem.setSummary(accountingItem.getCodeName());
        vouchItem.setFkAccountingItemId(fkAccountingItemId);
        vouchItem.setFkCurrencyTypeNum(vouchApplicationFormPaymentAddDto.getFkCurrencyTypeNum());
        vouchItem.setAmountCr(vouchApplicationFormPaymentAddDto.getAmount());
        utilService.setCreateInfo(vouchItem);
        vouchItemMapper.insert(vouchItem);

        //凭证和财务单据的支付记录
        VouchApplicationFormPayment vouchApplicationFormPayment = BeanUtil.copy(vouchApplicationFormPaymentAddDto, VouchApplicationFormPayment.class);
        vouchApplicationFormPayment.setFkVouchId(vouch.getId());
        vouchApplicationFormPayment.setStatus(1);
        utilService.setCreateInfo(vouchApplicationFormPayment);
        vouchApplicationFormPaymentMapper.insert(vouchApplicationFormPayment);
    }

    /**
     * 付款方式下拉框
     *
     * @return
     */
    @Override
    public List<PaymentMethodTypeSelectEntity> paymentMethodTypeSelect() {
        List<PaymentMethodTypeSelectEntity> baseSelectEntities = new ArrayList<>();
        LambdaQueryWrapper<PaymentMethodType> lambdaQueryWrapper = new LambdaQueryWrapper<>();

        lambdaQueryWrapper.orderByDesc(PaymentMethodType::getViewOrder);
        List<PaymentMethodType> paymentMethodTypeList = paymentMethodTypeMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isEmpty(paymentMethodTypeList)) {
            return baseSelectEntities;
        }
        for (PaymentMethodType paymentMethod : paymentMethodTypeList) {
            PaymentMethodTypeSelectEntity baseSelectEntity = BeanCopyUtils.objClone(paymentMethod, PaymentMethodTypeSelectEntity::new);
            baseSelectEntity.setName(paymentMethod.getTypeName());
            baseSelectEntities.add(baseSelectEntity);
        }
        return baseSelectEntities;
    }

    /**
     * 作废付款记录
     */
    @Override
    public void cancelVouchApplicationFormPayment(Long id) {
        VouchApplicationFormPayment vouchApplicationFormPayment = vouchApplicationFormPaymentMapper.selectById(id);
        if (vouchApplicationFormPayment == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        vouchApplicationFormPayment.setStatus(0);
        utilService.updateUserInfoToEntity(vouchApplicationFormPayment);
        vouchApplicationFormPaymentMapper.updateById(vouchApplicationFormPayment);

        Vouch vouch = vouchMapper.selectById(vouchApplicationFormPayment.getFkVouchId());
        vouch.setIsActive(false);
        utilService.updateUserInfoToEntity(vouch);
        vouchMapper.updateById(vouch);
    }


}
