package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Set;

/**
 * BD学生统计对比表 条件回显 DTO
 *
 * <AUTHOR>
 * @date 2023/1/6 15:21
 */
@Data
public class BdStudentStatisticalConditionalEchoVo {

    @ApiModelProperty(value = "所属分公司")
    private String companyNameStr;

    @ApiModelProperty(value = "申请计划创建时间")
    private String creamTimeStr;

    @ApiModelProperty(value = "同期比对年数")
    private String comparisonYearsStr;

    @ApiModelProperty(value = "业务国家")
    private String countryStr;

    @ApiModelProperty(value = "代理所在区域")
    private String agentAreaStr;

    @ApiModelProperty(value = "BD大区")
    private String areaRegionStr;

    @ApiModelProperty(value = "BD编号")
    private String bdCodeStr;

    @ApiModelProperty(value = "代理编号文本 逗号分割")
    private String agentNumStr;

    @ApiModelProperty(value = "角色名称")
    private String projectRoleName;

    @ApiModelProperty(value = "员工名称")
    private Set<String> staffName;

}
