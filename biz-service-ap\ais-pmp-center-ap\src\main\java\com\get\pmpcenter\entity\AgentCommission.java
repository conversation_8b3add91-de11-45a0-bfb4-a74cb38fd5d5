package com.get.pmpcenter.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@TableName("m_agent_commission")
public class AgentCommission extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 学校提供商Id
     **/
    @ApiModelProperty(value = "学校提供商Id")
    private Long fkInstitutionProviderId;

    /**
     * 代理佣金方案Id
     **/
    @ApiModelProperty(value = "代理佣金方案Id")
    private Long fkAgentCommissionPlanId;

    /**
     * 佣金类型
     **/
    @ApiModelProperty(value = "佣金类型：1课程/2阶梯/3组合")
    private Integer commissionType;

    /**
     * 标题
     **/
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 学校提供商Id
     **/
    @ApiModelProperty(value = "标题（本地语言）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String titleNative;
    /**
     * 阶梯起始学生数
     */
    @ApiModelProperty(value = "阶梯起始学生数")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer studentCountMin;
    /**
     * 阶梯结束学生数
     */
    @ApiModelProperty(value = "阶梯结束学生数")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer studentCountMax;
    /**
     * 阶梯统计类型：0不追加/1从第1个学生开始计算
     */
    @ApiModelProperty(value = "阶梯统计类型：0不追加/1从第1个学生开始计算")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer studentCountType;
    /**
     * 佣金
     */
    @ApiModelProperty(value = "佣金")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal commission;
    /**
     * 佣金单位：%/CNY/等货币编号
     */
    @ApiModelProperty(value = "佣金单位：%/CNY/等货币编号")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String commissionUnit;

    @ApiModelProperty(value = "后续学校提供商Id（不一致才需要选择）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long fkInstitutionProviderIdFollow;
    /**
     * 后续佣金
     */
    @ApiModelProperty(value = "后续佣金")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal followCommission;
    /**
     * 后续佣金单位：%/CNY/等货币编号
     */
    @ApiModelProperty(value = "后续佣金单位：%/CNY/等货币编号")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String followCommissionUnit;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remarkNote;
    /**
     * 备注（本地语言）
     */
    @ApiModelProperty(value = "备注（本地语言）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remarkNoteNative;
    /**
     * 组合名称（同组相同）
     */
    @ApiModelProperty(value = "组合名称（同组相同）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String packageName;
    /**
     * 组合名称（本地语言）（同组相同）
     */
    @ApiModelProperty(value = "组合名称（本地语言）（同组相同）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String packageNameNative;
    /**
     * 组合key（同组相同，且唯一）
     */
    @ApiModelProperty(value = "组合key（同组相同，且唯一）")
    private String packageKey;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Integer isActive;

    @ApiModelProperty(value = "课程")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String course;

    @ApiModelProperty(value = "后续备注")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String followRemarkNote;

    @ApiModelProperty(value = "后续备注（中文）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String followRemarkNoteNative;

    @ApiModelProperty(value = "学校提供商佣金Id（继承，自己添加没有，用处：佣金提醒，学校提供商佣金明细删除时会统一删除继承）")
    private Long fkInstitutionProviderCommissionId;

}
