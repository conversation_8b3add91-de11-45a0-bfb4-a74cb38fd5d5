package com.get.insurancecenter.vo.commission;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author:<PERSON>
 * @Date: 2025/6/18
 * @Version 1.0
 * @apiNote:汇率兑换结果
 */
@Data
public class RateResult {

    @ApiModelProperty(value = "结果-不等于0都是失败")
    private Integer status;

    @ApiModelProperty(value = "请求结果描述")
    private String msg;

    @ApiModelProperty(value = "请求结果")
    private RateDetail result;

}
