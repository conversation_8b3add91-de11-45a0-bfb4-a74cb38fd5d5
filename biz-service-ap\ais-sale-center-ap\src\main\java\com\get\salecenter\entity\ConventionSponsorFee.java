package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("m_convention_sponsor_fee")
public class ConventionSponsorFee extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    @Column(name = "fk_convention_id")
    private Long fkConventionId;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    @Column(name = "type_name")
    private String typeName;
    /**
     * 费用名称
     */
    @ApiModelProperty(value = "费用名称")
    @Column(name = "title")
    private String title;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;
    /**
     * 费用金额
     */
    @ApiModelProperty(value = "费用金额")
    @Column(name = "fee")
    private BigDecimal fee;
    /**
     * 日期描述
     */
    @ApiModelProperty(value = "日期描述")
    @Column(name = "date_note")
    private String dateNote;
    /**
     * 时间描述
     */
    @ApiModelProperty(value = "时间描述")
    @Column(name = "time_note")
    private String timeNote;
    /**
     * 图片路径
     */
    @ApiModelProperty(value = "图片路径")
    @Column(name = "img_url")
    private String imgUrl;
    /**
     * 费用摘要
     */
    @ApiModelProperty(value = "费用摘要")
    @Column(name = "summary")
    private String summary;
    /**
     * 数量限制，不填或<=0为不限制
     */
    @ApiModelProperty(value = "数量限制，不填或<=0为不限制")
    @Column(name = "count_limit")
    private Integer countLimit;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
    /**
     * 费用金额（折合人民币）
     */
    @ApiModelProperty(value = "费用金额（折合人民币）")
    @Column(name = "fee_cny")
    private BigDecimal feeCny;
}