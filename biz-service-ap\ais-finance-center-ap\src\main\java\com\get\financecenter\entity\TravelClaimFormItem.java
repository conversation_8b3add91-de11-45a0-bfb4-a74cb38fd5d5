package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 差旅报销申请单子项
 */
@Data
@TableName("m_travel_claim_form_item")
public class TravelClaimFormItem extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "差旅报销申请单Id")
    private Long fkTravelClaimFormId;

    @ApiModelProperty(value = "差旅报销费用类型Id")
    private Long fkTravelClaimFeeTypeId;

    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "报销金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "出发地")
    private String fromPlace;

    @ApiModelProperty(value = "目的地")
    private String toPlace;

    @ApiModelProperty(value = "开始时间")
    private Date fromDate;

    @ApiModelProperty(value = "结束时间")
    private Date toDate;

    @ApiModelProperty(value = "出差天数")
    private BigDecimal tripDays;

    @ApiModelProperty(value = "摘要")
    private String summary;

    @ApiModelProperty(value = "活动类型表名（目标类型表名）")
    private String fkEventTableName;

    @ApiModelProperty(value = "活动Id（目标类型表对应记录项Id）")
    private Long fkEventTableId;


}