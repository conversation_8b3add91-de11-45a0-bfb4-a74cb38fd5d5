package com.get.remindercenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.remindercenter.entity.UnsubscribeEmail;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface UnsubscribeEmailMapper extends BaseMapper<UnsubscribeEmail> {

    /**
     * 获取退订邮件
     * @param type
     * @return
     */
    List<String> getUnsubscribeEmail(Integer type);

}
