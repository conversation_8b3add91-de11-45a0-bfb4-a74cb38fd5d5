package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class StudentServiceFeeTypeDto extends BaseVoEntity {

    @ApiModelProperty("类型名称")
    @NotBlank(message = "类型名称不能为空",groups = {Add.class,Update.class})
    private String typeName;

    @ApiModelProperty("类型key")
    private String typeKey;

    @ApiModelProperty("排序")
    private Integer viewOrder;

    @ApiModelProperty("公司id")
    @NotNull(message = "公司id不能为null",groups = {Add.class})
    private Long fkCompanyId;

    @ApiModelProperty("公司id集合")
    private List<Long> fkCompanyIds;

  
}
