package com.get.common.query.annotation;

import com.get.common.query.enums.LogicType;
import com.get.common.query.enums.QueryType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 查询字段注解
 * 用于标记DTO字段的查询方式，支持动态查询条件构建
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface QueryField {
    
    /**
     * 查询类型
     */
    QueryType type() default QueryType.EQ;
    
    /**
     * 数据库字段名（默认使用字段名转下划线）
     * 如果指定了column，则使用指定的字段名
     */
    String column() default "";
    
    /**
     * 是否忽略空值
     * true: 当字段值为null、空字符串、空集合时忽略该条件
     * false: 不忽略空值
     */
    boolean ignoreEmpty() default true;
    
    /**
     * 条件组合方式
     */
    LogicType logic() default LogicType.AND;
    
    /**
     * 排序字段（当type为ORDER_BY时使用）
     * true: 升序，false: 降序
     */
    boolean asc() default true;
    
    /**
     * 查询条件的优先级，数字越小优先级越高
     * 用于控制查询条件的执行顺序
     */
    int priority() default 0;
    
    /**
     * 查询条件分组，用于实现复杂的条件组合
     * 相同分组内的条件使用AND连接，不同分组间使用OR连接
     * 默认为"default"，表示不分组，所有条件按logic()参数执行
     * 示例：
     * - group="group1"的条件会用AND连接成一组
     * - group="group2"的条件会用AND连接成另一组
     * - 然后两组之间用OR连接：(group1_conditions) OR (group2_conditions)
     */
    String group() default "default";
}