package com.get.aismail.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.time.LocalDateTime;

@Data
@TableName("M_MAIL_FOLDER")
public class MMailFolder {
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "邮件文件夹Id")
    @Column(name = "id")
    private Long id;

    @ApiModelProperty(value = "用户邮箱账号Id")
    @Column(name = "fk_mail_account_id")
    private Long fkMailAccountId;

    @ApiModelProperty(value = "文件夹名称")
    @Column(name = "folder_name")
    private String folderName;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;

    @ApiModelProperty(value = "创建时间")
    @Column(name = "gmt_create")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "创建用户(登录账号)")
    @Column(name = "gmt_create_user")
    private String gmtCreateUser;

    @ApiModelProperty(value = "修改时间")
    @Column(name = "gmt_modified")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "修改用户(登录账号)")
    @Column(name = "gmt_modified_user")
    private String gmtModifiedUser;
}
