<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.aisplatformcenter.mapper.MFeedbackOrderReplyMapper">

    <resultMap id="BaseResultMap" type="com.get.aisplatformcenterap.entity.MFeedbackOrderReplyEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkFeedbackOrderId" column="fk_feedback_order_id" jdbcType="BIGINT"/>
            <result property="fkStaffId" column="fk_staff_id" jdbcType="BIGINT"/>
            <result property="reply" column="reply" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

</mapper>
