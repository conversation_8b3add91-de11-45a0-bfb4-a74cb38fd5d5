package com.get.pmpcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@TableName("r_agent_commission_plan_institution")
public class AgentCommissionPlanInstitution extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "代理佣金方案Id")
    private Long fkAgentCommissionPlanId;

    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;
}
