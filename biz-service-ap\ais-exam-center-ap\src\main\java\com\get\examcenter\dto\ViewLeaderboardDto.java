package com.get.examcenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by <PERSON>.
 * Time: 10:21
 * Date: 2021/8/30
 * Description:排行榜查询Vo
 */
@Data
public class ViewLeaderboardDto {
    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户姓名")
    private String userName;

    /**
     * 所在城市
     */
    @ApiModelProperty(value = "所在城市")
    private Long fkAreaCityId;

    /**
     * 大区
     */
    @ApiModelProperty(value = "大区")
    private Long fkAreaRegionId;
}
