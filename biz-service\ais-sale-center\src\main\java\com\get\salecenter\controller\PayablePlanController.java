package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ErrorCodeEnum;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dto.OneClickSettlementDto;
import com.get.salecenter.vo.*;
import com.get.salecenter.vo.PayablePlanVo;
import com.get.salecenter.service.IPayablePlanService;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.query.PayablePlanNewQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/11/23
 * @TIME: 14:27
 * @Description:
 **/

@Api(tags = "应付计划管理")
@RestController
@RequestMapping("sale/payablePlan")
public class PayablePlanController {

    @Resource
    @Lazy
    private IPayablePlanService payablePlanService;


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ReceivablePlanVo>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/应付计划管理/列表数据")
    @PostMapping("datas")
    public ListResponseBo<PayablePlanVo> datas(@RequestBody SearchBean<PayablePlanDto> page) {
        String[] times = {"0", "0"};//[0]-o-主SQL执行时间,[1]-f-远程调用时间
        List<PayablePlanVo> datas = payablePlanService.datas(page.getData(), page, times);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p, times[0], times[1]);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ReceivablePlanVo>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "应付计划管理列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/应付计划管理/新框架列表数据")
    @PostMapping("payablePlanDatas")
    public ListResponseBo<PayablePlanNewVo> payablePlanDatas(@RequestBody SearchBean<PayablePlanNewQueryDto> page) {
        String[] times = {"0", "0"};//[0]-o-主SQL执行时间,[1]-f-远程调用时间
        List<PayablePlanNewVo> datas = payablePlanService.payablePlanDatas(page.getData(), page, times);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p, times[0], times[1]);
    }

    @ApiOperation(value = "获取应付计划列表")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/应付计划管理/获取应付计划列表")
    @PostMapping("getPayableList")
    public ResponseBo<PayablePlanVo> getPayableList(@RequestBody SearchBean<PayablePlanDto> searchBean){
        return payablePlanService.getPayableList(searchBean.getData(),searchBean);
    }


    /**
     * @Description: 代理应付汇总统计明细
     * @Author: Jerry
     * @Date:11:55 2021/11/22
     */
    @ApiOperation(value = "代理应付汇总统计明细")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/应付计划管理/代理应付汇总统计明细")
    @PostMapping("agentPaySumDetail")
    public ResponseBo<PayablePlanVo> agentPaySumDetail(@RequestBody SearchBean<AgentPaySumDetailDto> page) {
        List<PayablePlanVo> datas = payablePlanService.agentPaySumDetail(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @Description: 学生应付汇总统计明细
     * @Author: Jerry
     * @Date:11:55 2021/11/22
     */
    @ApiOperation(value = "学生应付汇总统计明细")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/应付计划管理/学生应付汇总统计明细")
    @PostMapping("studentPaySumDetail")
    public ResponseBo<PayablePlanVo> studentPaySumDetail(@RequestBody SearchBean<StudentPaySumDetailDto> page) {
        List<PayablePlanVo> datas = payablePlanService.studentPaySumDetail(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.PayablePlanVo>
     * @Description: 配置详情接口
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/应付计划管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<PayablePlanVo> detail(@PathVariable("id") Long id) {
        PayablePlanVo data = payablePlanService.findPayablePlanById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增接口
     * @Param [payablePlanDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/应付计划管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(PayablePlanDto.Add.class) PayablePlanDto payablePlanDto) {
        return SaveResponseBo.ok(payablePlanService.addPayablePlan(payablePlanDto));
    }

    @ApiOperation(value = "校验新增应付计划", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LOGIN, description = "销售中心/应付计划管理/校验新增应付计划")
    @PostMapping("checkoutPayablePlan")
    public ResponseBo<PayablePlanCheckOutVo> checkoutPayablePlan(@Validated @RequestBody PayablePlanCheckOutDto payablePlanCheckOutDto) {
        PayablePlanCheckOutVo payablePlanCheckOutVo = payablePlanService.checkoutPayablePlan(payablePlanCheckOutDto);
        return new ResponseBo<>(payablePlanCheckOutVo);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.PayablePlanVo>
     * @Description: 修改信息
     * @Param [payablePlanDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/应付计划管理/更新")
    @PostMapping("update")
    public ResponseBo<PayablePlanVo> update(@RequestBody @Validated(PayablePlanDto.Update.class)  PayablePlanDto payablePlanDto) {
        return UpdateResponseBo.ok(payablePlanService.updatePayablePlan(payablePlanDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/应付计划管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        payablePlanService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 查询应付计划附件
     * @Param [voSearchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "查询应收计划附件", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/应付计划管理/查询代理附件")
    @PostMapping("getPayableMedia")
    public ResponseBo<MediaAndAttachedVo> getPayableMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> staffMedia = payablePlanService.getMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 保存应付计划附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "保存应付计划附件")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/应付计划管理/代理附件保存接口")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> addMedia(@RequestBody @Validated(MediaAndAttachedDto.Add.class)  ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(payablePlanService.addMedia(mediaAttachedVo));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 关闭应付计划
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "关闭应付计划", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/应付计划管理/关闭")
    @PostMapping("unablePayable")
    public ResponseBo unableOffer(@RequestParam("id") Long id, @RequestParam("status") Long status) {
        payablePlanService.unablePayable(id, status);
        return UpdateResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.PayablePlanVo>
     * @Description: feign调用
     * @Param [id]
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getPayablePlan")
    public ResponseBo<PayablePlanVo> getPayablePlan(@RequestParam("typeKey") String typeKey,
                                                    @RequestParam("targetId") Long targetId) {
        List<PayablePlanVo> data = payablePlanService.getPayablePlan(typeKey, targetId);
        return new ListResponseBo<>(data);
    }

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: feign
     * @Param [typeKey, targetId]
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getPayablePlanIds")
    public List<Long> getPayablePlanId(@RequestParam(value = "typeKey", required = false) String typeKey,
                                       @RequestParam(value = "targetId", required = false) Long targetId) {
        return payablePlanService.getPayablePlanId(typeKey, targetId);
    }

    /**
     * @return
     * @Description：feign 根据应付计划id查询应付计划应付金额
     * @Param
     * @Date 17:00 2021/4/23
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getPayablePlanAmountById")
    public BigDecimal getPayablePlanAmountById(@RequestParam(value = "id") Long id) {
        return payablePlanService.getPayablePlanAmountById(id);
    }

    /**
     * feign 根据ids获取应付计划列表
     *
     * @param ids
     * @return
     */
    @ApiIgnore
    @PostMapping("getPayablePlanByIds")
    public List<PayablePlanVo> getPayablePlanByIds(@RequestBody Set<Long> ids) {
        return payablePlanService.getPayablePlanByIds(ids);
    }


    @ApiOperation(value = "获取应付计划列表", notes = "id为此条数据的外键id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/申请结算汇总/获取应付计划列表")
    @GetMapping("getPayablePlanByOfferItemId/{offerItemId}")
    public Result<List<SettlementPayablePlanVo>> getPayablePlanByOfferItemId(@PathVariable("offerItemId") Long offerItemId) {
        return payablePlanService.getPayablePlanByOfferItemId(offerItemId);
    }


    /**
     * 获取iFile Excel信息
     *
     * @Date 14:14 2022/3/8
     * <AUTHOR>
     */
    @ApiIgnore
    @GetMapping("iFileGroupByAgidAndCurrencyInfo")
    public List<IFileInfoVo> iFileGroupByAgidAndCurrencyInfo(@RequestParam("numSettlementBatch") String numSettlementBatch) {
        return payablePlanService.iFileGroupByAgidAndCurrencyInfo(numSettlementBatch);
    }

    /**
     * @return void
     * @Description :导出应付计划Excel
     * @Param [response, conventionSponsorVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "导出应付计划Excel")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/应付计划管理/导出应付计划Excel")
    @PostMapping("/exportPayablePlanExcel")
    @ResponseBody
    public void exportPayablePlanExcel(HttpServletResponse response, @RequestBody PayablePlanNewQueryDto payablePlanNewVo) {
        payablePlanService.exportPayablePlanExcel(response, payablePlanNewVo);
    }

//    @ApiOperation(value = "应付计划预付按钮", notes = "id为此条应付计划id")
//    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/应付计划管理/应付计划预付按钮")
//    @PostMapping("prepaymentButton")
//    public ResponseBo prepaymentButton(@RequestBody PrepaymentButtonDto prepaymentButtonDto) {
//        payablePlanService.prepaymentButton(prepaymentButtonDto);
//        return ResponseBo.ok();
//    }

//    @ApiOperation(value = "应付计划取消预付按钮")
//    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/应付计划管理/应付计划取消预付按钮")
//    @PostMapping("cancelPrepaymentButton")
//    public ResponseBo cancelPrepaymentButton(@RequestBody PrepaymentButtonCancelDto prepaymentButtonCancelDto) {
//        payablePlanService.cancelPrepaymentButton(prepaymentButtonCancelDto);
//        return ResponseBo.ok();
//    }

    @ApiOperation(value = "应付计划一键结算按钮")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/应付计划管理/应付计划一键结算按钮")
    @PostMapping("oneClickSettlement")
    public ResponseBo oneClickSettlement(@RequestBody OneClickSettlementDto oneClickSettlementDto) {
        payablePlanService.oneClickSettlement(oneClickSettlementDto);
        return ResponseBo.ok();
    }


    @ApiOperation(value = "获取最新的三条学费", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/应付计划管理/获取最新的三条学费")
    @GetMapping("getPayPlanTheLatestThreeTuitionFees")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<String> getPayPlanTheLatestThreeTuitionFees(@RequestParam("fkCompanyId") Long fkCompanyId) {
        return new ListResponseBo<>(payablePlanService.getPayPlanTheLatestThreeTuitionFees(fkCompanyId));
    }

    @PostMapping("batchUpdatePayablePlanInfo")
    @ApiOperation(value = "批量更新应付信息")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER,type = LoggerOptTypeConst.EDIT,description = "销售中心/应付计划管理/批量修改信息")
    public ResponseBo batchUpdatePayablePlanInfo(@RequestBody @Validated BatchUpdatePayablePlanDto batchUpdatePayablePlanDto) {
        payablePlanService.batchUpdatePayablePlanInfo(batchUpdatePayablePlanDto);
        return UpdateResponseBo.ok();
    }



}
