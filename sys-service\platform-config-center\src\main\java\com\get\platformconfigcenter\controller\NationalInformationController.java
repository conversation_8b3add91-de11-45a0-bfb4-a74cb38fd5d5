package com.get.platformconfigcenter.controller;

import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.platformconfigcenter.service.NationalInformationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 国家资讯配置模块控制类
 *
 * <AUTHOR>
 * @date 2021/5/11 14:51
 */
@Api(tags = "国家资讯配置")
@RestController
@RequestMapping("/platform/mso/nationalInformation")
public class NationalInformationController {
    @Resource
    private NationalInformationService nationalInformationService;

    /**
     * @Description:国家资讯配置列表数据
     * @Param
     * @Date 17:50 2021/5/11
     * <AUTHOR>
     */
//    @ApiOperation(value = "国家资讯列表数据", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.LIST, description = "业务平台配置中心/MSO/国家资讯配置/查询")
//    @PostMapping("datas")
//    public ResponseBo<AgentModuleVo> datas(@RequestBody SearchBean<AgentModuleDto> page) {
//        List<AgentModuleVo> agentModuleList = nationalInformationService.getAgentModuleList(page.getData(), page);
//        Page p = BeanCopyUtils.objClone(page, Page::new);
//        return new ListResponseBo<>(agentModuleList, p);
//    }

    /**
     * 新增国家资讯
     *
     * @param agentModuleVo
     * @return
     * @
     */
//    @ApiOperation(value = "新增国家资讯接口", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.ADD, description = "业务平台配置中心/MSO/国家资讯配置/新增国家资讯")
//    @PostMapping("add")
//    public ResponseBo add(@RequestBody @Validated(AgentModuleDto.Add.class) AgentModuleDto agentModuleVo) {
//        return SaveResponseBo.ok(nationalInformationService.addAgentModule(agentModuleVo));
//    }

    /**
     * 更新国家资讯
     *
     * @param agentModuleVo
     * @return
     * @
     */
//    @ApiOperation(value = "修改接口", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.EDIT, description = "业务平台配置中心/MSO/国家资讯配置/更新国家资讯")
//    @PostMapping("update")
//    public ResponseBo<AgentModuleVo> update(@RequestBody @Validated(AgentModuleDto.Update.class) AgentModuleDto agentModuleVo) {
//        return UpdateResponseBo.ok(nationalInformationService.updateAgentModule(agentModuleVo));
//    }

    /**
     * @Description:国家资讯配置详情
     * @Param
     * @Date 11:31 2021/5/12
     * <AUTHOR>
     */
//    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DETAIL, description = "业务平台配置中心/MSO/国家资讯配置/详情")
//    @GetMapping("/{id}")
//    public ResponseBo<AgentModuleVo> detail(@PathVariable("id") Long id) {
//        AgentModuleVo data = nationalInformationService.findAgentModuleById(id);
//        return new ResponseBo<>(data);
//    }

    /**
     * @Description:删除国家资讯
     * @Param
     * @Date 12:38 2021/5/12
     * <AUTHOR>
     */
//    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DELETE, description = "业务平台配置中心/MSO/国家资讯配置/删除国家资讯")
//    @PostMapping("delete/{id}")
//    public ResponseBo delete(@PathVariable("id") Long id) {
//        nationalInformationService.delete(id);
//        return DeleteResponseBo.ok();
//    }

    /**
     * @Description:上移下移
     * @Param
     * @Date 12:48 2021/5/12
     * <AUTHOR>
     */
//    @ApiOperation(value = "上移下移", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "业务平台配置中心/MSO/国家资讯配置/移动顺序")
//    @PostMapping("movingOrder")
//    public ResponseBo movingOrder(@RequestBody List<AgentModuleDto> agentModuleVos) {
//        nationalInformationService.movingOrder(agentModuleVos);
//        return ResponseBo.ok();
//    }

    /**
     * 国家资讯模块类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "国家资讯模块类型下拉框", notes = "")
    @GetMapping("getModuleKey")
    public ResponseBo getModuleKey() {
        List<Map<String, Object>> datas = nationalInformationService.getModuleKey();
        return new ListResponseBo<>(datas);
    }

}
