package com.get.institutioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("s_character")
public class Character extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    @Column(name = "fk_table_name")
    private String fkTableName;
    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    @Column(name = "fk_table_id")
    private Long fkTableId;
    /**
     * 特性类型，枚举：世界排名/国家排名/CUG排名/REF排名/师生比例/男女比例/国际学生百分比/中国学生百分比
     */
    @ApiModelProperty(value = "特性类型，枚举：世界排名/国家排名/CUG排名/REF排名/师生比例/男女比例/国际学生百分比/中国学生百分比")
    @Column(name = "character_type")
    private Integer characterType;
    /**
     * 特性值
     */
    @ApiModelProperty(value = "特性值")
    @Column(name = "value")
    private String value;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fkTableName=").append(fkTableName);
        sb.append(", fkTableId=").append(fkTableId);
        sb.append(", characterType=").append(characterType);
        sb.append(", value=").append(value);
        sb.append(", viewOrder=").append(viewOrder);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}