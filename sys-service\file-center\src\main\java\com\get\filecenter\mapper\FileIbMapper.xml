<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.filecenter.mapper.FileIbMapper">
    <resultMap id="BaseResultMap" type="com.get.filecenter.entity.FileIb">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="file_guid" jdbcType="VARCHAR" property="fileGuid"/>
        <result column="file_type_orc" jdbcType="VARCHAR" property="fileTypeOrc"/>
        <result column="file_name_orc" jdbcType="VARCHAR" property="fileNameOrc"/>
        <result column="file_name" jdbcType="VARCHAR" property="fileName"/>
        <result column="file_path" jdbcType="VARCHAR" property="filePath"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, file_guid, file_type_orc, file_name_orc, file_name, file_path, gmt_create, gmt_create_user, 
    gmt_modified, gmt_modified_user
  </sql>
 
</mapper>