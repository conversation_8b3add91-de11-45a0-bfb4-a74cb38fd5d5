package com.get.partnercenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.get.partnercenter.vo.AgentAccountVo;
import com.get.partnercenter.vo.ReginVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * ais_institution_center
 */
@Mapper
@DS("institutiondb")
public interface InstitutionCenterMapper {

    List<ReginVo> getRegionList();

    String getCountryNameByIds(List<Long> ids);

    AgentAccountVo getRegionNames(@Param("areaRegionId") String areaRegionId);

}
