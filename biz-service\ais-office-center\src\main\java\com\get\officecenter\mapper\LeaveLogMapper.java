package com.get.officecenter.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.officecenter.vo.LeaveLogVo;
import com.get.officecenter.vo.StaffOfLeaveLogVo;
import com.get.officecenter.entity.LeaveLog;
import com.get.officecenter.dto.StaffOfLeaveLogDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2022/1/14
 * @TIME: 11:26
 * @Description:
 **/
@Mapper
public interface LeaveLogMapper extends GetMapper<LeaveLog> {
    /**
     * 获取公休时长
     * @param fkStaffIds
     * @param fkCompanyId
     * @param startDate
     * @param endDate
     * @return
     */
    List<LeaveLogVo> getLeaveLog(@Param("fkStaffIds") Set<Long> fkStaffIds,
                                 @Param("fkCompanyId") Long fkCompanyId,
                                 @Param("startDate") Date startDate,
                                 @Param("endDate") Date endDate,
                                 @Param("status") Integer status );

    /**
     * 获取指定时间之后所有影响补休时长的日志记录合计时长
     *
     * @param fkStaffIds
     * @param endTime
     * @return
     */
    List<LeaveLogVo> getLeaveLogsAboutTakeDeferredHoliday(@Param("fkStaffIds") Set<Long> fkStaffIds,
                                                          @Param("fkCompanyId") Long fkCompanyId,
                                                          @Param("endTime") Date endTime,
                                                          @Param("status") Integer status);

    /**
     * 获取指定时间之后所有影响上一年年假时长的日志记录合计时长
     *
     * @param fkStaffIds
     * @param date
     * @return
     */
    List<LeaveLogVo> getLeaveLogsAboutAnnualVacationLastYear(@Param("fkStaffIds") Set<Long> fkStaffIds,
                                                             @Param("fkCompanyId") Long fkCompanyId,
                                                             @Param("endTime") Date endTime,
                                                             @Param("date") Date date,
                                                             @Param("status") Integer status);

    /**
     * 获取考勤范围时间之后所有变更今年年假时长的日志记录合计时长
     *
     * @param fkStaffIds
     * @param date
     * @return
     */
    List<LeaveLogVo> getLeaveLogsAboutAnnualVacationThisYear(@Param("fkStaffIds") Set<Long> fkStaffIds,
                                                             @Param("fkCompanyId") Long fkCompanyId,
                                                             @Param("endTime") Date endTime,
                                                             @Param("date") Date date,
                                                             @Param("status") Integer status);

    /**
     * 获取指定时间之后所有影响病假时长的日志记录合计时长
     * @param fkStaffIds
     * @param fkLeaveStockIds
     * @Param fkCompanyId
     * @param endTime
     * @param status
     * @return
     */
    List<LeaveLogVo> getLeaveLogsAboutDiseaseVacationQuarter(@Param("fkStaffIds") Set<Long> fkStaffIds,
                                                             @Param("fkLeaveStockIds") Set<Long> fkLeaveStockIds,
                                                             @Param("fkCompanyId") Long fkCompanyId,
                                                             @Param("endTime") Date endTime,
                                                             @Param("status") Integer status);


    /**
     * 获取考勤时间范围已休上一年年假记录
     *
     * @param fkStaffIds
     * @return
     */
    List<LeaveLogVo> getAnnualVacationLastYearLog(@Param("fkStaffIds") Set<Long> fkStaffIds,
                                                  @Param("fkCompanyId") Long fkCompanyId,
                                                  @Param("startDate") Date startDate,
                                                  @Param("endDate") Date endDate,
                                                  @Param("date") Date date,
                                                  @Param("leaveType") String leaveType,
                                                  @Param("optTypeKey") String optTypeKey,
                                                  @Param("status") Integer status);

    /**
     * 获取考勤时间范围已休今年年假记录
     *
     * @param fkStaffIds
     * @return
     */
    List<LeaveLogVo> getAnnualVacationThisYearLog(@Param("fkStaffIds") Set<Long> fkStaffIds,
                                                  @Param("fkCompanyId") Long fkCompanyId,
                                                  @Param("startDate") Date startDate,
                                                  @Param("endDate") Date endDate,
                                                  @Param("date") Date date,
                                                  @Param("leaveType") String leaveType,
                                                  @Param("optTypeKey") String optTypeKey,
                                                  @Param("status") Integer status);

    /**
     * 移动端休假记录员工列表
     *
     * @param iPage
     * @param staffOfLeaveLogDto
     * @return
     */
    List<StaffOfLeaveLogVo> getStaffOfLeaveLogList(IPage<StaffOfLeaveLogVo> iPage, @Param("staffOfLeaveLogDto") StaffOfLeaveLogDto staffOfLeaveLogDto);
}
