package com.get.pmpcenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 有附件的表明枚举
 */

@Getter
@AllArgsConstructor
public enum MediaTableEnum {

    PROVIDER_CONTRACT("m_institution_provider_contract", "学校提供商合同"),
    PROVIDER_COMMISSION_PLAN("m_institution_provider_commission_plan", "学校提供商佣金方案"),
    PROVIDER_COMMISSION_PLAN_APPROVAL("m_institution_provider_commission_plan_approval", "学校提供商佣金方案审核记录"),
    AGENT_COMMISSION_PLAN_APPROVAL("m_agent_commission_plan_approval", "代理佣金方案审核记录"),
    ;

    private String code;

    private String msg;

    public static MediaTableEnum getEnumByCode(String code) {
        for (MediaTableEnum value : MediaTableEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
