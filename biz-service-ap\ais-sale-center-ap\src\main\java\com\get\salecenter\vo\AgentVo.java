package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import com.get.platformconfigcenter.vo.UserAgentVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/7 11:51
 * @verison: 1.0
 * @description:
 */
@Data
@ApiModel(value = "代理返回类")
//@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AgentVo extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "联系人")
    List<ContactPersonVo> contactPersonDtos;

    /**
     * 返佣比例备注，换行符拼接
     */
    @ApiModelProperty(value = "返佣比例备注")
    private String returnCommissionRateNote;


    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String countryName;

    /**
     * 州省名称
     */
    @ApiModelProperty(value = "州省名称")
    private String stateName;

    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称")
    private String cityName;

    /**
     * 学生代理父编号
     */
    @ApiModelProperty(value = "学生代理父编号")
    private String parentAgentNum;

    /**
     * 学生代理父名称
     */
    @ApiModelProperty(value = "学生代理父名称")
    private String parentAgentName;

    /**
     * BD编号
     */
    @ApiModelProperty(value = "BD编号")
    private String bdCode;

    /**
     * 名字
     */
    @ApiModelProperty(value = "BD名称")
    private String bdName;

    /**
     * 名字
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "公司id串(逗号分隔)")
    private String companyIdStr;

    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "公司ids")
    private List<Long> companyIds;

    @ApiModelProperty(value = "父代理名字")
    private String parentName;

    @ApiModelProperty(value = "父代理编号")
    private String parentNum;

    /**
     * 角色-员工 字符串(逗号分隔)
     */
    private String roleStaffStr;

    @ApiModelProperty(value = "项目角色配置")
    private List<AgentRoleStaffVo> agentRoleStaffDtoList;

    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

    /**
     * 方案id
     */
    @ApiModelProperty(value = "方案id")
    private Long offerId;

    /**
     * BD绑定的大区
     */
    @ApiModelProperty("BD绑定的大区")
    private List<AreaRegionVo> areaRegionDtos;

    /**
     * typeKeyStr
     */
    @ApiModelProperty("typeKeyStr")
    private String typeKeyStr;

    /**
     * 是否可以一键进入ISSUE
     */
    @ApiModelProperty(value = "一键进入ISSUE")
    private Boolean isJumpIssue;

    @ApiModelProperty(value = "用户信息")
    List<UserAgentVo> agentInfoDto;
    /**
     * 拼接字符：名字备注/公司名称
     */
    @ApiModelProperty(value = "拼接字符：名字备注/公司名称")
    private String nameNoteAndCompanyName;

    @ApiModelProperty(value = "代理id")
    private Long fkAgentId;

    @ApiModelProperty(value = "学生id")
    private Long fkStudentId;

    @ApiModelProperty("规定时间前，该代理是否有营业执照")
    private Boolean isABusinessLicense = true;

    @ApiModelProperty("代理项目成员")
    private List<AgentRoleStaffVo> agentRoleStaffDtos;

    @ApiModelProperty("代理学生数")
    private Integer studentNum;

    @ApiModelProperty("是否无合同附件代理:false否/true是")
    private Boolean isHasContractAttachment;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("首次合同创建时间")
    private Date firstContractTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("首次创建申请时间")
    private Date firstOfferItemTime;

    @ApiModelProperty("代理名称+编号")
    private String fkAgentNameNumStr;

    //===============实体类Agent=====================
    private static final long serialVersionUID = 1L;
    /**
     * 学生代理父Id
     */
    @ApiModelProperty(value = "学生代理父Id")
    @Column(name = "fk_parent_agent_id")
    @UpdateWithNull
    private Long fkParentAgentId;
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 州省Id
     */
    @ApiModelProperty(value = "州省Id")
    @Column(name = "fk_area_state_id")
    private Long fkAreaStateId;
    /**
     * 城市Id
     */
    @ApiModelProperty(value = "城市Id")
    @Column(name = "fk_area_city_id")
    private Long fkAreaCityId;
    /**
     * 代理编号
     */
    @ApiModelProperty(value = "代理编号")
    @Column(name = "num")
    private String num;
    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    @Column(name = "name")
    private String name;
    /**
     * 名称备注
     */
    @ApiModelProperty(name = "名称备注")
    @Column(name = "name_note")
    private String nameNote;
    /**
     * 代理昵称
     */
    @ApiModelProperty(value = "代理昵称")
    @Column(name = "nick_name")
    private String nickName;
    /**
     * 性质：公司/个人/工作室/国际学校/其他
     */
    @ApiModelProperty(value = "性质：公司/个人/工作室/国际学校/其他")
    @Column(name = "nature")
    private String nature;
    /**
     * 性质备注
     */
    @ApiModelProperty(value = "性质备注")
    @Column(name = "nature_note")
    private String natureNote;
    /**
     * 法定代表人
     */
    @ApiModelProperty(value = "法定代表人")
    @Column(name = "legal_person")
    private String legalPerson;
    /**
     * 税号/统一社会信用代码（公司）
     */
    @ApiModelProperty(value = "税号/统一社会信用代码（公司）")
    @Column(name = "tax_code")
    private String taxCode;
    /**
     * 身份证号（个人）
     */
    @ApiModelProperty(value = "身份证号（个人）")
    @Column(name = "id_card_num")
    private String idCardNum;
    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    @Column(name = "address")
    private String address;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 邀请码，8位数字或字母随机数
     */
    @ApiModelProperty(value = "邀请码，8位数字或字母随机数")
    @Column(name = "invitation_code")
    private String invitationCode;
    /**
     * 是否结算口，0否/1是
     */
    @ApiModelProperty(value = "是否结算口，0否/1是")
    @Column(name = "is_settlement_port")
    private Boolean isSettlementPort;
    /**
     * 是否关键代理：0否/1是
     */
    @ApiModelProperty(value = "是否关键代理：0否/1是")
    @Column(name = "is_key_agent")
    private Boolean isKeyAgent;
    /**
     * 关键代理失效时间
     */
    @ApiModelProperty(value = "关键代理失效时间")
    @Column(name = "key_agent_failure_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date keyAgentFailureTime;

    @ApiModelProperty(value = "是否渠道代理：0否/1是")
    @Column(name = "is_customer_channel")
    private Boolean isCustomerChannel;

    @ApiModelProperty(value = "是否拒收系统邮件：0否/1是")
    @Column(name = "is_reject_email")
    private Boolean isRejectEmail;


    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    @Column(name = "id_gea")
    private String idGea;
    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    @Column(name = "id_iae")
    private String idIae;


    @ApiModelProperty("个人姓名")
    @Column(name = "personal_name")
    private String personalName;

    @ApiModelProperty("代理标签")
    private List<AgentLabelVo> agentLabelVos;

    /**
     * 合同到期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("合同到期时间")
    private Date contractEndTime;

    /**
     * 合同状态
     */
    @ApiModelProperty("合同状态：0无合同/1有合同/2未签署/3待审核/4审核通过/-4审核驳回/5续约中/6生效中/7已过期")
    private Integer contractApprovalStatus;

}
