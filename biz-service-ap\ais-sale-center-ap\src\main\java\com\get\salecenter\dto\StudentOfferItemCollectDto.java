package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import java.util.Set;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2022/4/2
 * @TIME: 16:08
 * @Description:
 **/
@Data
public class StudentOfferItemCollectDto extends BaseVoEntity {

    /**
     * offer student id
     */
    @ApiModelProperty(value = "offer student id")
    private String studentId;


    @ApiModelProperty(value = "高级搜索条件")
    private List<StudentOfferItemAdvancedSearchDto> studentOfferItemAdvancedSearchDtoList;

    @ApiModelProperty(value = "申请计划id列表（KPI统计跳转）")
    private Set<Long> studentOfferItemIds;

    @NotNull(message = "页码不能为空")
    @ApiModelProperty(value = "页码")
    private Integer pageNumber;
    @ApiModelProperty("agentIdGea")
    private String agentIdGea;
    @ApiModelProperty(value = "偏移量")
    private Integer offset;

    @NotNull(message = "显示条数不能为空")
    @ApiModelProperty(value = "页面大小")
    private Integer pageSize=20;

    @ApiModelProperty(value = "是否延迟入学 true:是  false:否")
    private Boolean deferFlag;
    /**
     * 代理国家
     */
    @ApiModelProperty(value = "代理国家")
    private Long fkAreaCountryIdAgent;
    /**
     * 代理省份ID列表
     */
    @ApiModelProperty(value = "代理省份ID列表")
    private List<Long> fkAreaStateIdAgentList;
    /**
     * 步骤状态变更时间（开始范围）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "步骤状态变更时间（开始范围）")
    private Date offerStepStartTime;

    /**
     * 步骤状态变更时间（结束范围）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "步骤状态变更时间（结束范围）")
    private Date offerStepEndTime;

    @ApiModelProperty("变更的步骤id")
    private List<Long> changeStepId;

    /**
     * 集团名称
     */
    @ApiModelProperty(value = "集团名称")
    private String institutionGroupName;

    /**
     * 集团Ids
     */
    @ApiModelProperty(value = "集团ids(多选)")
    private List<Long> fkInstitutionGroupIds;
    /**
     * 项目成员id列表
     */
    @ApiModelProperty(value = "项目成员id列表")
    private List<Long> fkProjectRoleIds;

    /**
     * 状态：0关闭/1打开
     */
    @ApiModelProperty(value = "状态：0关闭/1打开")
    private Integer status;

    /**
     * 多选步骤
     */
    @ApiModelProperty(value = "多选步骤")
    private List<Integer> stepOrderList;
    /**
     * 一键申请状态
     */
    @ApiModelProperty(value = "一键申请状态")
    private String fkIssueRpaOrderId;
    /**
     * 学生来源
     */
    @ApiModelProperty(value = "学生来源")
    private String studentSource;

    /**
     * 开学日期开始时间
     */
    @ApiModelProperty(value = "开学日期开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginOpeningTime;
    /**
     * 开学日期结束时间
     */
    @ApiModelProperty(value = "开学日期结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endOpeningTime;
    /**
     * 申请国家
     */
    @ApiModelProperty(value = "申请国家")
    private Long fkAreaCountryId;

    /**
     * 申请国家列表
     */
    @ApiModelProperty(value = "申请国家列表")
    private List<Long> fkAreaCountryIdList;

    /**
     * 申请创建时间（开始范围）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "申请创建时间（开始范围）")
    private Date createStartTime;

    /**
     * 申请创建时间（结束范围）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "申请创建时间（结束范围）")
    private Date createEndTime;

    @ApiModelProperty(value = "跳转类型：" +
            "8-代理详情国家分布图"+
            "1-代理详情定校量（按学生）；" +
            "2-代理详情成功入学（按学生）" +
            "3-业绩统计定校量（按学校）" +
            "4-业绩统计成功入学（按学校）" +
            "5-业绩统计处理申请（含加申）" +
            "6-业绩统计定校量（按学生）" +
            "7-业绩统计成功入学（按学生）")
    private Integer jumpState;

    /**
     * 申请开始时间【代理详情定校量（按学生）、成功入学（按学生）跳转】
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "申请开始时间")
    private Date itemBeginTime;

    /**
     * 申请结束时间【代理详情定校量（按学生）、成功入学（按学生）跳转】
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "申请结束时间")
    private Date itemEndTime;

    /**
     * 业绩统计GEA定校量相关步骤列表枚举
     */
    @ApiModelProperty(value = "业绩统计GEA定校量相关步骤列表枚举")
    private List<String> geaConfirmationStatisticsStepList;

    /**
     * 业绩统计IAE定校量相关步骤列表枚举
     */
    @ApiModelProperty(value = "业绩统计IAE定校量相关步骤列表枚举")
    private List<String> iaeConfirmationStatisticsStepList;

    /**
     * 业绩统计GEA成功量相关步骤列表枚举
     */
    @ApiModelProperty(value = "业绩统计GEA成功量相关步骤列表枚举")
    private List<String> geaSuccessStatisticsStepList;

    /**
     * 业绩统计IAE成功量相关步骤列表枚举
     */
    @ApiModelProperty(value = "业绩统计IAE成功量相关步骤列表枚举")
    private List<String> iaeSuccessStatisticsStepList;



    /**
     * 入学失败原因Id
     */
    @ApiModelProperty(value = "入学失败原因Id")
    private Long fkEnrolFailureReasonId;

    /**
     * 入学失败原因Id
     */
    @ApiModelProperty(value = "入学失败原因Ids")
    private List<Long> fkEnrolFailureReasonIds;
    /**
     * 学校名
     */
    @ApiModelProperty(value = "学校名")
    private String schoolName;

    /**
     * 学校IDS集合
     */
    @ApiModelProperty(value = "学校IDS集合")
    private List<Long> institutionIds;
    /**
     * 课程名称搜索的ids集合
     */
    @ApiModelProperty(value = "课程名称搜索的ids集合")
    private List<Long> courseIds;
    /**
     * 申请课程名
     */
    @ApiModelProperty(value = "申请课程名")
    private String courseName;

    @ApiModelProperty(value = "是否全局匹配课程名称 枚举：0百度式搜索/1全局匹配")
    private Boolean isCourseGlobalMatching = false;

    @ApiModelProperty(value = "课程组别ids")
    private List<Long> courseTypeGroupIds;

    @ApiModelProperty("旧课程类型组别名称（多选）")
    private List<String> oldCourseTypeGroupNames;

    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    @ApiModelProperty(value = "渠道名称（多选）")
    private List<String> channelNames;
    /**
     * 代理名字
     */
    @ApiModelProperty(value = "代理名字")
    private String agentName;

    /**
     * 代理ids
     */
    @ApiModelProperty(value = "代理ids")
    private List<Long> fkAgentIds;
    /**
     * bd名
     */
    @ApiModelProperty(value = "bd名")
    private String bdName;

    @ApiModelProperty(value = "BD的ids")
    private List<Long> fkBdIds;

    @ApiModelProperty(value = "大区")
    private Long fkAreaRegionId;
    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;
    /**
     * 公司IDs
     */
    @ApiModelProperty(value = "公司IDs")
    private List<Long> fkCompanyIds;
    /**
     * 学生名称（中英文名）
     */
    @ApiModelProperty(value = "学生名称")
    private String studentName;
    /**
     * 学生编号
     */
    @ApiModelProperty(value = "学生编号")
    private String studentNum;
    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;
    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String tel;

    /**
     * 学生国籍所在国家Id
     */
    @ApiModelProperty(value = "学生国籍所在国家Id")
    private Long fkAreaCountryIdNationality;


//    /**
//     * 课程等级名称
//     */
//    @ApiModelProperty(value = "课程等级名称")
//    private String courseTypeName;

    @ApiModelProperty(value = "课程等级Id")
    private Long majorLevelId;

    @ApiModelProperty(value = "课程等级ids（多选）")
    private List<Long> majorLevelIds;


    @ApiModelProperty(value = "旧课程等级名称")
    private String oldCourseMajorLevelName;

    @ApiModelProperty(value = "旧课程等级名称（多选）")
    private List<String> oldCourseMajorLevelNames;

    /**
     * 毕业院校名称
     */
    @ApiModelProperty(value = "毕业院校名称")
    private String fkInstitutionNameEducation;

    /**
     * 毕业院校名称(国内中文)
     */
    @ApiModelProperty(value = "毕业院校名称(国内中文)")
    private String fkInstitutionNameCnEducation;

    /**
     * 毕业院校Ids
     */
    @ApiModelProperty(value = "毕业院校Ids")
    private List<Long> fkInstitutionIdsEducation;

    /**
     * 毕业院校名称（国际）
     */
    @ApiModelProperty(value = "毕业院校名称（国际）")
    private String fkInstitutionNameEducation2;

    /**
     * 毕业院校Ids（国际）
     */
    @ApiModelProperty(value = "毕业院校Ids（国际）")
    private List<Long> fkInstitutionIdsEducation2;

    @ApiModelProperty(value = "新申请状态：枚举：0缺资料/1未开放")
    private Integer newAppStatus;

    @ApiModelProperty(value = "是否数据处理(fk_institution_course_id或者fk_institution_channel_id或者fk_institution_provider_id为-1)")
    private Boolean isProcessing;

    /**
     * 忽略代理id（学校统计申请量跳转/业绩统计跳转）
     */
    @ApiModelProperty(value = "忽略代理id")
    private List<Long> noAgentIds;

    /**
     * 学习申请方案状态列表（学校统计申请量跳转/业绩统计跳转）
     */
    @ApiModelProperty(value = "学习申请方案状态列表")
    private List<Integer> studentOfferStatusList;

    /**
     * 学校统计模块-提交数跳转
     */
    @ApiModelProperty(value = "学校统计模块-提交数跳转")
    private Boolean parentItemIsNull;

    @ApiModelProperty("学生创建时间(开始)")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;
    @ApiModelProperty("学生创建时间(结束)")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty("是否筛选主课，枚举：0不进行筛选/1筛选")
    private Boolean isMainCourse;

    /**
     * 高中成绩类型，枚举Key
     */
    @ApiModelProperty(value = "高中成绩类型，枚举Key")
    private String highSchoolTestType;
    /**
     * 高中成绩
     */
    @ApiModelProperty(value = "高中成绩")
    private String highSchoolTestScore;
    /**
     * 标准测试类型
     */
    @ApiModelProperty(value = "本科成绩类型，枚举Key")
    private String standardTestType;
    /**
     * 标准测试成绩
     */
    @ApiModelProperty(value = "本科成绩")
    private String standardTestScore;

    @ApiModelProperty(value ="当前步骤")
    private Long currentStep;

    @ApiModelProperty(value = "步骤超过的天数")
    private Integer stepDays;

    @ApiModelProperty(value = "bd统计跳转查询条件")
    private BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonVo;

    @ApiModelProperty("附件凭证")
    private List<String> attachmentVouchers;


    @ApiModelProperty(value = "学校提供商")
    private Long fkInstitutionProviderId;
    /**
     * 备注
     */
    @ApiModelProperty(value = "学生备注")
    private String remarkComment;

    /**
     * 备注
     */
    @ApiModelProperty(value = "m_student_offer_item.app_remark、m_student_offer_item.remark、s_comment备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否曾经计入业绩,0不曾经计入业绩/1曾经计入业绩")
    private Boolean isStaffCommissionAction;

    @ApiModelProperty(value = "业绩统计数类型")
    private List<String> commissionActionStep;

    @ApiModelProperty(value = "业绩统计数类型")
    private List<String> commissionActionStepLimitStudent;

    @ApiModelProperty(value = "导出ids")
    private List<Long> exportIds;

    @ApiModelProperty(value = "1创建时间倒序/2创建时间正序序/3开学时间倒序/4开学时间正序")
    private Integer sortType;

    @ApiModelProperty(value = "是否需要后续显示")
    private Boolean isFollowItem = false;

    @ApiModelProperty(value = "最早开学时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDeferOpeningTime;

    @ApiModelProperty(value = "最早开学时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDeferOpeningTime;

    @ApiModelProperty("支付押金时间(开始)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDepositTime;

    @ApiModelProperty("支付押金时间(结束)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDepositTime;

    @ApiModelProperty(value = "支付学费时间(结束)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTuitionTime;

    @ApiModelProperty(value = "支付学费时间(开始)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTuitionTime;

    /**
     * 学生国籍所在国家Id（多选）
     */
    @ApiModelProperty(value = "学生国籍所在国家Id")
    private Set<Long> fkAreaCountryIdNationalitys;

    @ApiModelProperty(value = "是否有代付费用日志")
    private Boolean isPayLog;

    @ApiModelProperty(value = "是否后续")
    private Boolean isFollow;

}