package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.CommonUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.financecenter.vo.OccVo;
import com.get.salecenter.dao.occ.OccMapper;
import com.get.salecenter.dao.occ.SaleMapper;
import com.get.salecenter.dao.sale.AgentContractAccountMapper;
import com.get.salecenter.dao.sale.PayablePlanMapper;
import com.get.salecenter.dao.sale.ReceivablePlanMapper;
import com.get.salecenter.dao.sale.StudentOfferItemMapper;
import com.get.salecenter.dao.sale.StudentOfferMapper;
import com.get.salecenter.dao.sale.StudentProjectRoleStaffMapper;
import com.get.salecenter.entity.AgentContractAccount;
import com.get.salecenter.entity.PayablePlan;
import com.get.salecenter.entity.ReceivablePlan;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.entity.StudentProjectRoleStaff;
import com.get.salecenter.service.DataImportService;
import com.get.salecenter.service.IPayablePlanService;
import com.get.salecenter.service.IStudentProjectRoleStaffService;
import com.get.salecenter.vo.COfferVo;
import com.get.salecenter.vo.StudentOfferItemVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DataImportServiceImpl implements DataImportService {

    @Resource
    private OccMapper occMapper;


    @Resource
    private SaleMapper saleMapper;

    @Resource
    private ReceivablePlanMapper receivablePlanMapper;


    @Resource
    private PayablePlanMapper payablePlanMapper;

    @Resource
    private IPayablePlanService payablePlanService;

    @Resource
    private IFinanceCenterClient iFinanceCenterClient;

    @Resource
    private AgentContractAccountMapper accountMapper;

    @Resource
    private StudentOfferMapper studentOfferMapper;

    @Resource
    private StudentOfferItemMapper studentOfferItemMapper;

    @Resource
    private StudentProjectRoleStaffMapper studentProjectRoleStaffMapper;

    @Resource
    private IStudentProjectRoleStaffService projectRoleStaffService;

    private final static String RECEIVABLE = "应收";

    private final static String RECEIVED = "实收";

    private final static String PAYABLE = "应付";

    private final static String PAID = "实付";

    private final static String CREATE_USER = "admin-tx";

    private final static Long COMPANY_ID = 2L;

    private final static String TYPE_KEY = "m_student_offer_item";


    public final ConcurrentHashMap<String, Exception> map = new ConcurrentHashMap<>(128);
    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(8, 16, 60, TimeUnit.SECONDS, new LinkedBlockingQueue<>(5));

    @Override
    public void dataImport() {
//        log.info("开始查询财务数据");
//        importNewData();
        List<Long> l1 = new ArrayList<Long>(){{
            add(3L);
            add(7L);
        }};
        List<Long> l2 = new ArrayList<Long>(){{
            add(6L);
            add(8L);
            add(9L);
        }};
        List<Long> l3 = new ArrayList<Long>(){{
            add(14L);
            add(15L);
            add(16L);
        }};
        List<Long> l4 = new ArrayList<Long>(){{
            add(4L);
            add(5L);
            add(23L);
            add(20L);
            add(17L);
            add(21L);
            add(18L);
        }};
        List<Long> studentIds= new ArrayList<>();
        List<List<Long>> all = new ArrayList<>();
        all.add(l1);
        all.add(l2);
        all.add(l3);
        all.add(l4);
        for (List<Long> longs : all) {
            updateOffer(longs);
            try {
                Thread.sleep(10000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

    }

    @Override
    public void updateOffer(List<Long> ids) {
        List<COfferVo> test = studentOfferMapper.test(ids);
        if (test.isEmpty()) {
            return;
        }
        Map<Long, List<COfferVo>> collect = test.stream().collect(Collectors.groupingBy(COfferVo::getStudentId));
        Set<Map.Entry<Long, List<COfferVo>>> entries = collect.entrySet();
        Map<Long,Map<Long,String>> offMap = new HashMap<>();
        Map<Long,Map<String,Integer>> result = new HashMap<>();
        for (Map.Entry<Long, List<COfferVo>> entry : entries) {
            List<COfferVo> value = entry.getValue();
            Map<Long,String> temp = new HashMap<>();
            Map<String,Integer> map = new HashMap<>();
            for (COfferVo cOfferVo : value) {
                if (cOfferVo.getK()==null) {
                    continue;
                }
                String[] split = cOfferVo.getK().split(",");
                List<String> list = Arrays.asList(split);
                list.sort(Comparator.comparing(String::hashCode));
                String k = String.valueOf(Math.abs(list.toString().hashCode()));
                if (map.containsKey(k)) {
                    map.put(k,map.get(k) + 1);
                }else {
                    map.put(k, 1);
                }
                temp.put(cOfferVo.getOfferId(),k);
            }
            offMap.put(entry.getKey(),temp);
            result.put(entry.getKey(),map);
        }
        cases(result,collect,offMap,ids);
    }

    private void cases(Map<Long,Map<String,Integer>> result, Map<Long, List<COfferVo>> collect, Map<Long,Map<Long,String>> offMap, List<Long> ids){
        Map<Long,List<Long>> rsMap = new HashMap<>();
        List<Long> repList = new ArrayList<>();
        for (Map.Entry<Long, Map<String, Integer>> entry : result.entrySet()) {
            Map<String, Integer> entryValue = entry.getValue();
            List<COfferVo> cOfferVos = collect.get(entry.getKey());
            if (cOfferVos !=null) {
                double s2 = cOfferVos.size();
                double max = 0.00;
                String code = null;
                Collection<Integer> values = entryValue.values();
                List<Integer> list = new ArrayList<>(values);
                if (values.size()==1) {
                    continue;
                }
                list.sort((a,b)->b-a);
                if (list.size()>=3) {
                    if (list.get(0).equals(list.get(1)) && list.get(0) > list.get(2)) {
                        repList.add(entry.getKey());
                        continue;
                    }
                }
                for (Map.Entry<String, Integer> integerEntry : entryValue.entrySet()) {
                    double value = integerEntry.getValue();
                    double temp = Math.max(max,value / s2);
                    if (temp>max) {
                        max = temp;
                        code = integerEntry.getKey();
                    }
                }
                Map<Long, String> offer = offMap.get(entry.getKey());
                Long keyId = getKey(code,offer);
                List<Long> longs = new ArrayList<>();
                for (Map.Entry<Long, String> e2 : offer.entrySet()) {
                    if (e2.getValue().equals(code)) {
                        continue;
                    }
                    longs.add(e2.getKey());
                }
                if ((longs.size()/s2) != max) {
                    rsMap.put(keyId,longs);
                }
            }
        }
        Map<Long, List<Long>> listMap = rsMap.entrySet().stream().filter(f -> GeneralTool.isNotEmpty(f.getValue())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
//        for (Map.Entry<Long, List<Long>> entry : listMap.entrySet()) {
//            System.out.println(entry.getKey() + "\t" + entry.getValue());
//        }
        Set<Long> keys = listMap.keySet();
        if (keys.isEmpty()) {
            return;
        }
        List<StudentProjectRoleStaff> projectRoleStaffs = studentProjectRoleStaffMapper.selectList(Wrappers.<StudentProjectRoleStaff>lambdaQuery()
                .eq(StudentProjectRoleStaff::getFkTableName, TableEnum.SALE_STUDENT_OFFER.key).in(StudentProjectRoleStaff::getFkTableId,keys)
                .eq(StudentProjectRoleStaff::getIsActive, true));
        Map<Long, List<StudentProjectRoleStaff>> map = projectRoleStaffs.stream().collect(Collectors.groupingBy(StudentProjectRoleStaff::getFkTableId));
        List<StudentProjectRoleStaff> addList = new ArrayList<>();
        List<Long> delList = new ArrayList<>();
        for (Map.Entry<Long, List<StudentProjectRoleStaff>> entry : map.entrySet()) {
            Long key = entry.getKey();
            List<StudentProjectRoleStaff> value = entry.getValue();
            List<Long> offerIds = listMap.get(key);
            if (GeneralTool.isNotEmpty(offerIds)) {
                delList.addAll(offerIds);
                for (Long id : offerIds) {
                    try {
                        List<StudentProjectRoleStaff> roleStaffs = CommonUtil.deepCopy(value);
                        roleStaffs.forEach(v->{
                            v.setFkTableId(id);
                            v.setGmtCreateUser("admin-tg");
                        });
                        addList.addAll(roleStaffs);
                    } catch (IOException | ClassNotFoundException e) {
                        e.printStackTrace();
                    }

                }
            }
        }
//        if (GeneralTool.isNotEmpty(delList)) {
//            studentProjectRoleStaffMapper.delete(Wrappers.<StudentProjectRoleStaff>lambdaQuery().eq(StudentProjectRoleStaff::getFkTableName, TableEnum.SALE_STUDENT_OFFER.key)
//                    .in(StudentProjectRoleStaff::getFkTableId, delList));
//        }
//        try {
//            Thread.sleep(5000);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//        List<List<StudentProjectRoleStaff>> lists = ListUtils.partition(addList, 1000);
//        for (List<StudentProjectRoleStaff> list : lists) {
//            projectRoleStaffService.batchAdd(list);
//        }

        System.out.println("////////////////一比一学生列表//////////////////");
        for (Long aLong : repList) {
            System.out.println("学生：\t"+aLong);
        }
    }

    private Long getKey(String code,Map<Long, String> offer){
        for (Map.Entry<Long, String> entry : offer.entrySet()) {
            if (entry.getValue().equals(code)) {
                return entry.getKey();
            }
        }
        return null;
    }

    @Override
    public void import2(List<String> names) {
        if (names.isEmpty()) {
            return;
        }
        dt(names);
    }


    private void dt(List<String> names) {
        //        List<Long> error=new ArrayList<>();
//        List<Long> success=new ArrayList<>();
//        List<Long> xb=new ArrayList<>();
//        List<Long> eb=new ArrayList<>();
//        List<OccDto> data = occMapper.getData();
//        for (OccDto vo : data) {
//            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
//            PayablePlan payablePlan = payablePlanMapper.getPayablePlanByCourseId(vo.getCourseid(), format.format(vo.getGmtpayablecreatetime()));
//            if (GeneralTool.isNotEmpty(payablePlan)) {
//                payablePlan.setCommissionRate(vo.getPayablecommissionrate());
//                BigDecimal arrangement = vo.getArrangement();
//                BigDecimal fixedAmount = vo.getPayablefixedamount();
//                if (fixedAmount!=null) {
//                    if (GeneralTool.isEmpty(arrangement) || arrangement.compareTo(BigDecimal.valueOf(100))>0) {
//                        arrangement=BigDecimal.valueOf(100);
//                    }
//                    payablePlan.setFixedAmount(fixedAmount);
//                    payablePlan.setSplitRate(arrangement);
//                    payablePlan.setCommissionAmount(fixedAmount.multiply(arrangement.divide(BigDecimal.valueOf(100),4, RoundingMode.HALF_UP)));
//                    payablePlan.setGmtCreateUser("admin-2");
//                    payablePlanMapper.updateById(payablePlan);
//                    success.add(payablePlan.getId());
//                    try {
//                        Thread.sleep(10);
//                    } catch (InterruptedException e) {
//                        e.printStackTrace();
//                    }
//                }
//            }else {
//                error.add(vo.getId());
//            }
//        }
//        try {
//            Thread.sleep(2000);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//        log.info("开始同步第二份数据");
//        List<SaleDataTempVo> otherPlan = payablePlanMapper.getOtherPlan();
//        for (SaleDataTempVo plan : otherPlan) {
//            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
//            OccDto info = occMapper.getDataByInfo(plan.getCourseId(), format.format(plan.getCreateTime()));
//            if (GeneralTool.isNotEmpty(info)) {
//                PayablePlan fInfo = payablePlanMapper.selectById(plan.getId());
//                BigDecimal arrangement = info.getArrangement();
//                BigDecimal fixedAmount = info.getPayablefixedamount();
//                BigDecimal tuitionamount = info.getTuitionamount();
//                BigDecimal payableamount = info.getPayableamount();
//                if (GeneralTool.isEmpty(arrangement) || arrangement.compareTo(BigDecimal.valueOf(100))>0) {
//                    arrangement=BigDecimal.valueOf(100);
//                }
//                if (fixedAmount!=null) {
////                    fInfo.setCommissionRate(BigDecimal.ZERO);
////                    fInfo.setFixedAmount(fixedAmount);
////                    fInfo.setSplitRate(arrangement);
////                    fInfo.setCommissionAmount(fixedAmount.multiply(arrangement.divide(BigDecimal.valueOf(100),4, RoundingMode.HALF_UP)));
//                } else if (GeneralTool.isNotEmpty(tuitionamount)
//                        && tuitionamount.compareTo(BigDecimal.ZERO)>0
//                        && GeneralTool.isNotEmpty(payableamount)
//                ){
//                    BigDecimal payablecommissionrate = info.getPayablecommissionrate();
//                    if (GeneralTool.isEmpty(payablecommissionrate)) {
//                        payablecommissionrate = payableamount.divide(tuitionamount);
//                    }
//                    fInfo.setFixedAmount(BigDecimal.ZERO);
//                    fInfo.setSplitRate(arrangement);
//                    fInfo.setCommissionAmount(tuitionamount.multiply(payablecommissionrate).multiply(arrangement.divide(BigDecimal.valueOf(100))));
//                    fInfo.setCommissionRate(payablecommissionrate.multiply(BigDecimal.valueOf(100)));
//                }
//                fInfo.setGmtCreateUser("admin-x");
//                xb.add(info.getId());
//                payablePlanMapper.updateById(fInfo);
//                try {
//                    Thread.sleep(10);
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
//            }else {
//                eb.add(plan.getId());
//            }
//        }
//        System.out.println(error);
//        System.out.println(success.size());
//        System.out.println(eb);
//        System.out.println(xb.size());
//        List<String> courseList = occMapper.getAllCourseId();
//        List<List<String>> partition = ListUtils.partition(courseList, 3000);
//        for (List<String> listList : partition) {
//            executor.execute(() -> over(listList));
//        }
        names = names.stream().distinct().collect(Collectors.toList());
        Set<String> byNames = studentOfferItemMapper.getCourseIdByNames(names);
        List<OccVo> data = occMapper.getDataByIds(byNames);
        Set<String> ids = data.stream().map(OccVo::getCourseid).collect(Collectors.toSet());
//        System.out.println("xx");
        over(new ArrayList<>(ids));
    }

    private void importNewData() {
        Map<String, String> pid = new HashMap<>();
        List<OccVo> dtoList = occMapper.getAllNewReceivedPay();
        List<String> courseIds = dtoList.stream().map(OccVo::getCourseid).collect(Collectors.toList());
        log.info("课程id个数:{}", courseIds.size());
        Long bankId;
        int flag = 0;
        for (OccVo occVo : dtoList) {
            bankId = 0L;
            PayablePlan payablePlan = payablePlanMapper.queryPayablePlan(occVo.getCourseid());
            if (payablePlan == null) {
                pid.put(occVo.getCourseid(), "1");
                payablePlan = payablePlanMapper.queryTsPayablePlan(occVo.getCourseid());
            }
            StudentOfferItem offerItem = studentOfferItemMapper.selectById(payablePlan.getFkTypeTargetId());
            Long agentId = offerItem.getFkAgentId();
            List<AgentContractAccount> account = accountMapper.selectList(Wrappers.<AgentContractAccount>lambdaQuery()
                    .eq(AgentContractAccount::getFkAgentId, agentId));
            if (!account.isEmpty()) {
                bankId = account.get(0).getId();
            }
            iFinanceCenterClient.importPaymentNewRecord(occVo, payablePlan.getId(), payablePlan.getFkCurrencyTypeNum()
                    , agentId, payablePlan.getPayableAmount().doubleValue(), bankId);
            flag += 1;
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
//        List<PayablePlan> payablePlans = payablePlanMapper.queryPayablePlan(courseIds);
//        List<Long> collect = payablePlans.stream().map(PayablePlan::getId).collect(Collectors.toList());
        log.info("成功插入次数:{}", flag);
        log.info("bms导出课程:{}", pid.size());
    }

    private void over(List<String> courseList) {
        try {
            BigDecimal fee = BigDecimal.ZERO;
            StudentOfferItemVo studentOfferItemVo;
            List<OccVo> hiPaidInfoList;
            OccVo payableInfo;
            List<OccVo> receivedOcList;
            ReceivablePlan receivablePlan;
            OccVo occVo;
            boolean reFlag = true;
            boolean finaFlag;
            OccVo dto;
            for (String s : courseList) {
                dto = new OccVo();
                dto.setAmountreceivable(BigDecimal.ZERO);
                occVo = occMapper.getUptoRecord(s, RECEIVABLE);
                studentOfferItemVo = saleMapper.getOfferByCourseId(s);
                if (GeneralTool.isEmpty(studentOfferItemVo)) {
                    continue;
                }
                receivedOcList = occMapper.getOcByCourseId(s, RECEIVED);
                receivablePlan = new ReceivablePlan();
                //获取应付计划对应记录
                payableInfo = occMapper.getUptoRecord(s, PAYABLE);
                hiPaidInfoList = occMapper.getOcByCourseId(s, PAID);
                finaFlag = GeneralTool.isNotEmpty(payableInfo) || GeneralTool.isNotEmpty(hiPaidInfoList);
                if (GeneralTool.isEmpty(occVo)) {
                    if (GeneralTool.isNotEmpty(receivedOcList)) {
                        dto.setTuitionamount(receivedOcList.get(0).getTuitionamount());
                        fee = createReceivable(dto, receivedOcList, receivablePlan, studentOfferItemVo.getId(), true, finaFlag);
                        if (null != fee) {
                            reFlag = false;
                        } else {
                            fee = BigDecimal.ZERO;
                        }
                    }
                } else {
                    if (GeneralTool.isEmpty(receivedOcList)) {
                        fee = createReceivable(occVo, receivedOcList, receivablePlan, studentOfferItemVo.getId(), false, finaFlag);
                    } else {
                        fee = createReceivable(occVo, receivedOcList, receivablePlan, studentOfferItemVo.getId(), true, finaFlag);
                    }
                    if (null != fee) {
                        reFlag = false;
                    } else {
                        fee = BigDecimal.ZERO;
                    }
                }
                try {
                    Thread.sleep(10);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                // 没有应付有实付
                if (GeneralTool.isEmpty(payableInfo)) {
                    if (GeneralTool.isNotEmpty(hiPaidInfoList)) {
                        payableInfo = dto;
                        payableInfo.setPayableamount(BigDecimal.ZERO);
                        payableInfo.setTuitionamount(hiPaidInfoList.get(0).getTuitionamount());
                        if (compareAmount(payableInfo, hiPaidInfoList)) {
                            if (reFlag) {
                                dto.setTuitionamount(hiPaidInfoList.get(0).getTuitionamount());
                                fee = createReceivable(dto, receivedOcList, receivablePlan, studentOfferItemVo.getId(), false, finaFlag);
                            }
                            //创建应付
                            createPayable(payableInfo, studentOfferItemVo.getId(), receivablePlan.getId(), true, studentOfferItemVo.getFkAgentId(), hiPaidInfoList, fee);
                        }
                    }
                } else {
                    if (compareAmount(payableInfo, hiPaidInfoList)) {
                        if (reFlag) {
                            dto.setTuitionamount(payableInfo.getTuitionamount());
                            fee = createReceivable(dto, receivedOcList, receivablePlan, studentOfferItemVo.getId(), false, finaFlag);
                        }
                        createPayable(payableInfo, studentOfferItemVo.getId(), receivablePlan.getId(), !GeneralTool.isEmpty(hiPaidInfoList), studentOfferItemVo.getFkAgentId(), hiPaidInfoList, fee);
                    }
                }
                reFlag = true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private boolean compareAmount(OccVo payableInfo, List<OccVo> hiPaidInfoList) {
        BigDecimal payableAmount = payableInfo.getPayableamount();
        if (GeneralTool.isEmpty(payableAmount) || payableAmount.compareTo(BigDecimal.ZERO) == 0) {
            if (GeneralTool.isEmpty(hiPaidInfoList)) {
                return false;
            }
            hiPaidInfoList = hiPaidInfoList.stream().filter(r -> r.getPaidamount() != null && r.getPaidamount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
            return hiPaidInfoList.size() != 0;
        }
        return true;
    }

    private BigDecimal createReceivable(OccVo occVo, List<OccVo> receivedOcList, ReceivablePlan receivablePlan, Long targetId, boolean redFlag, boolean hasPay) {
        BigDecimal fee = BigDecimal.ZERO;
        BigDecimal zero = BigDecimal.ZERO;
        BigDecimal amountreceivable = occVo.getAmountreceivable();
        if ((GeneralTool.isEmpty(amountreceivable) || amountreceivable.compareTo(zero) == 0)) {
            if (!redFlag && !hasPay) {
                return null;
            }
            receivedOcList = receivedOcList.stream().filter(r -> r.getReceivedamount() != null && r.getReceivedamount().compareTo(zero) != 0).collect(Collectors.toList());
            if (receivedOcList.size() == 0 && !hasPay) {
                return null;
            }
        }
        receivablePlan.setFkCompanyId(COMPANY_ID);
        receivablePlan.setFkTypeKey(TYPE_KEY);
        receivablePlan.setFkTypeTargetId(targetId);
        receivablePlan.setFkCurrencyTypeNum(occVo.getCurrency());
        BigDecimal tuitionAmount = occVo.getTuitionamount();
        receivablePlan.setTuitionAmount(tuitionAmount);
        BigDecimal reAmount = occVo.getAmountreceivable();
        if (GeneralTool.isNotEmpty(occVo.getReceivablefixedamount()) && occVo.getReceivablefixedamount().compareTo(BigDecimal.ZERO) > 0) {
            if (GeneralTool.isEmpty(reAmount) || reAmount.compareTo(BigDecimal.ZERO) == 0) {
                reAmount = occVo.getReceivablefixedamount();
            }
            receivablePlan.setFixedAmount(occVo.getReceivablefixedamount());
        } else if (GeneralTool.isNotEmpty(tuitionAmount)) {
            if (GeneralTool.isNotEmpty(occVo.getArrangement())) {
                receivablePlan.setNetRate(occVo.getArrangement());
            } else {
                receivablePlan.setNetRate(BigDecimal.valueOf(100));
            }
            BigDecimal commissionRate = occVo.getReceivablecommissionrate();
            if (GeneralTool.isEmpty(commissionRate)) {
                commissionRate = BigDecimal.valueOf(0);
            }
            receivablePlan.setCommissionRate(commissionRate);
            receivablePlan.setCommissionAmount(tuitionAmount
                    .multiply(commissionRate.divide(BigDecimal.valueOf(100), 8, RoundingMode.HALF_UP))
                    .multiply(receivablePlan.getNetRate().divide(BigDecimal.valueOf(100), 8, RoundingMode.HALF_UP)));
            if (receivablePlan.getCommissionAmount().compareTo(BigDecimal.ZERO) > 0 && (GeneralTool.isEmpty(reAmount) || reAmount.compareTo(BigDecimal.ZERO) == 0)) {
                reAmount = receivablePlan.getCommissionAmount();
            }
        }
        receivablePlan.setReceivableAmount(reAmount);
        receivablePlan.setGmtCreate(occVo.getCreatetime());
        receivablePlan.setStatus(1);
        receivablePlan.setGmtCreateUser(CREATE_USER);
        if (receivablePlanMapper.insert(receivablePlan) > 0 && redFlag) {
            Result<BigDecimal> result = iFinanceCenterClient.importReGea(receivedOcList, receivablePlan.getId(), receivablePlan.getFkTypeTargetId(), receivablePlan.getFkCurrencyTypeNum());
            if (result.isSuccess() && result.getData() != null) {
                fee = result.getData();
            }
        }
        return fee;
    }

    private void createPayable(OccVo hiPayableInfo, Long targetId, Long receivablePlanId, boolean payFlag, Long agentId, List<OccVo> hiPaidInfoList, BigDecimal fee) {
        BigDecimal zero = BigDecimal.valueOf(0);

                    /*
                    创建应付计划
                       */
        PayablePlan payablePlan = new PayablePlan();
        payablePlan.setFkCompanyId(COMPANY_ID);
        payablePlan.setFkTypeKey(TYPE_KEY);
        payablePlan.setFkTypeTargetId(targetId);
        payablePlan.setFkReceivablePlanId(receivablePlanId);
        //枚举未给
        payablePlan.setIsPayInAdvance(false);
        payablePlan.setStatusSettlement(0);
        payablePlan.setFkCurrencyTypeNum(hiPayableInfo.getCurrency());
        BigDecimal tuitionamount = hiPayableInfo.getTuitionamount();
        BigDecimal payableamount = hiPayableInfo.getPayableamount();
        BigDecimal payablecommissionrate = hiPayableInfo.getPayablecommissionrate();
        payablePlan.setTuitionAmount(tuitionamount);
        if (GeneralTool.isNotEmpty(hiPayableInfo.getPayablefixedamount())) {
            payablePlan.setFixedAmount(hiPayableInfo.getPayablefixedamount());
        } else if (GeneralTool.isNotEmpty(tuitionamount) && tuitionamount.compareTo(zero) > 0 && GeneralTool.isNotEmpty(payableamount)) {
            if (GeneralTool.isEmpty(payablecommissionrate)) {
                payablecommissionrate = payableamount.divide(tuitionamount, 8, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100));
            }
            if (payablecommissionrate.compareTo(zero) > 0) {
//                payablePlan.setCommissionAmount(tuitionamount
//                        .multiply(payablecommissionrate.divide(BigDecimal.valueOf(100), 8, RoundingMode.HALF_UP))
//                        .multiply(payablePlan.getSplitRate().divide(BigDecimal.valueOf(100), 8, RoundingMode.HALF_UP)));
                payablePlan.setCommissionAmount(payableamount);
                payablePlan.setCommissionRate(payablecommissionrate);
            }
        }
        if (GeneralTool.isNotEmpty(hiPayableInfo.getArrangement())) {
            payablePlan.setSplitRate(hiPayableInfo.getArrangement());
        } else {
            payablePlan.setSplitRate(BigDecimal.valueOf(100));
        }
        payablePlan.setPayableAmount(payableamount);
        payablePlan.setStatus(1);
        payablePlan.setGmtCreate(hiPayableInfo.getCreatetime());
        payablePlan.setGmtCreateUser(CREATE_USER);
        payablePlan.setIsPayInAdvance(false);
        if (payablePlanMapper.insert(payablePlan) > 0 && payFlag) {
            //获取实付对应记录
            Long payablePlanId = payablePlan.getId();
            iFinanceCenterClient.importPayGea(hiPaidInfoList, payablePlanId, payablePlan.getFkCurrencyTypeNum(), agentId
                    , payableamount == null ? 0.00 : payableamount.doubleValue(), fee.doubleValue());
        }

    }
}
