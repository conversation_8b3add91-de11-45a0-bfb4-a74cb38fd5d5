package com.get.helpcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.helpcenter.vo.HelpVo;
import com.get.helpcenter.vo.HelpInfoVo;
import com.get.helpcenter.entity.Help;
import com.get.helpcenter.dto.ExchangeHelpDto;
import com.get.helpcenter.dto.HelpDto;
import com.get.helpcenter.dto.query.HelpQueryDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Hardy
 * @create: 2021/5/11 14:59
 * @verison: 1.0
 * @description:
 */
public interface IHelpService extends IService<Help> {
    /**
     * 树形数据
     *
     * @param helpVo
     * @return
     * @
     */
    List<HelpVo> getHelpTree(HelpQueryDto helpVo);

    /**
     * 交换帮助前置问题
     *
     * @param exchangeHelpDto
     */
    void exchangeHelp(ExchangeHelpDto exchangeHelpDto);


    /**
     * @return java.lang.Long
     * @Description :新增
     * @Param [helpDto]
     * <AUTHOR>
     */
    Long addHelp(HelpDto helpDto);

    /**
     * @return com.get.helpcenter.vo.HelpVo
     * @Description :修改
     * @Param [helpDto]
     * <AUTHOR>
     */
    HelpVo updateHelpVo(HelpDto helpDto);

    /**
     * @return void
     * @Description : 排序
     * @Param [helpDtoList]
     * <AUTHOR>
     */
    void movingOrder(List<HelpDto> helpDtoList);

    /**
     * @return com.get.helpcenter.vo.HelpVo
     * @Description :根据id查找
     * @Param [id]
     * <AUTHOR>
     */
    HelpVo findHelpById(Long id);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void deleteHelp(Long id);

    /**
     * @return java.util.List<com.get.helpcenter.vo.HelpVo>
     * @Description :帮助信息下拉框
     * @Param [id]
     * <AUTHOR>
     */
    List<HelpVo> getHelpSelect();

    /**
     * @return java.util.List<java.lang.Object>
     * @Description :显示类型下拉框
     * @Param
     * <AUTHOR>
     */
    List<Map<String, Object>> getShowTypeObjectsSelect();

    /**
     * @return com.get.helpcenter.vo.HelpVo
     * @Description :根据keyCode查询
     * @Param [keyCode]
     * <AUTHOR>
     */
    HelpVo getHelpByKeyCode(String keyCode);

    /**
     * feign调用 根据帮助id获取帮助详细
     *
     * @Date 10:46 2021/9/8
     * <AUTHOR>
     */
    List<HelpVo> getHelpDtoByHelpId(Set<Long> helpIds);

    @VerifyLogin(IsVerify = false)
    List<HelpInfoVo> getHelpInfo();
}
