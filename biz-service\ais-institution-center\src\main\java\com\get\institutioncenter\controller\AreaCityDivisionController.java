package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.dto.AreaCityDivisionDto;
import com.get.institutioncenter.vo.AreaCityDivisionVo;
import com.get.institutioncenter.service.IAreaCityDivisionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/3/3 14:46
 * @verison: 1.0
 * @description:
 */
@Api(tags = "城市区域管理")
@RestController
@RequestMapping("institution/areaCityDivision")
public class AreaCityDivisionController {
    @Resource
    private IAreaCityDivisionService areaCityDivisionService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AreaCityDivisionVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/城市区域管理/城市区域详情")
    @GetMapping("/{id}")
    public ResponseBo<AreaCityDivisionVo> detail(@PathVariable("id") Long id) {
        AreaCityDivisionVo data = areaCityDivisionService.findAreaCityDivisionById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增
     * @Param [areaCityDivisionDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/城市区域管理/新增城市区域")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(AreaCityDivisionDto.Add.class) AreaCityDivisionDto areaCityDivisionDto) {
        return SaveResponseBo.ok(areaCityDivisionService.add(areaCityDivisionDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/城市区域管理/删除城市区域")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        areaCityDivisionService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AreaCityDivisionVo>
     * @Description :修改信息
     * @Param [areaCityDivisionDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/城市区域管理/更新城市区域")
    @PostMapping("update")
    public ResponseBo<AreaCityDivisionVo> update(@RequestBody @Validated(AreaCityDivisionDto.Update.class) AreaCityDivisionDto areaCityDivisionDto) {
        return UpdateResponseBo.ok(areaCityDivisionService.updateAreaCityDivision(areaCityDivisionDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AreaCityDivisionVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/城市区域管理/查询城市区域")
    @PostMapping("datas")
    public ResponseBo<AreaCityDivisionVo> datas(@RequestBody SearchBean<AreaCityDivisionDto> page) {
        List<AreaCityDivisionVo> datas = areaCityDivisionService.getAreaCityDivisions(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :上移下移
     * @Param [areaCityInfoTypeVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/城市资讯类型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<AreaCityDivisionDto> areaCityDivisionDtos) {
        areaCityDivisionService.movingOrder(areaCityDivisionDtos);
        return ResponseBo.ok();
    }

    /**
     * feign调用 通过城市区域ids 查找对应的城市区域名称map
     * @param ids
     * @return
     */
/*    @ApiIgnore
    @PostMapping(value = "getCityDivisionFullNamesByIds")
    public Map<Long, String> getCityDivisionFullNamesByIds(@RequestBody Set<Long> ids) {
        return areaCityDivisionService.getCityDivisionFullNamesByIds(ids);
    }*/

//    /**
//     * @Description: 查询城市下面的区域
//     * @Author: Jerry
//     * @Date:12:37 2021/9/10
//     */
//    @VerifyPermission(IsVerify = false)
//    @ApiOperation(value = "查询城市下面的区域", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/城市区域管理/查询城市下区域")
//    @GetMapping("getByFkAreaCityId")
//    public ResponseBo getByFkAreaCityId(@RequestParam(value = "fkAreaCityId", required = false) Long fkAreaCityId)  {
//        List<AreaCityDivisionVo> datas = areaCityDivisionService.getByFkAreaCityId(fkAreaCityId);
//        return new ListResponseBo<>(datas);
//    }

    /**
     * 查询城市下面区域
     *
     * @param id
     * @return
     */
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "查询州省下面城市", notes = "")
    @GetMapping("getByFkAreaCityId/{id}")
    public ResponseBo getByFkAreaCityId(@PathVariable("id") Long id) {
        List<AreaCityDivisionVo> datas = areaCityDivisionService.getByFkAreaCityId(id);
        return new ListResponseBo<>(datas);
    }


}
