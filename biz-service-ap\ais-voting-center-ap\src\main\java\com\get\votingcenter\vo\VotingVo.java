package com.get.votingcenter.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseEntity;
import com.get.votingcenter.entity.Voting;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.util.Date;

/**
 * @author: Hardy
 * @create: 2021/9/23 10:58
 * @verison: 1.0
 * @description:
 */
@Data
public class VotingVo  extends BaseEntity {

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;
    /**
     * 主题名称
     */
    @ApiModelProperty(value = "主题名称")
    @Column(name = "theme_name")
    private String themeName;
    /**
     * 主题Key（系统唯一Key，外部功能模块接入时使用）
     */
    @ApiModelProperty(value = "主题Key（系统唯一Key，外部功能模块接入时使用）")
    @Column(name = "theme_key")
    private String themeKey;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Column(name = "description")
    private String description;



}
