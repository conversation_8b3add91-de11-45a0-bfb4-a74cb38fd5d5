package com.get.examcenter.utils;

/**
 * Created by <PERSON>.
 * Time: 10:53
 * Date: 2021/8/23
 * Description:考试中心编码规则工具类
 */
public class MyStringUtils {

    /**
     * @Description: 考试编号
     * @Author: <PERSON>
     * @Date:10:53 2021/8/23
     */
    public static String getExaminationNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 6) {
            code = String.format("%05d", num);
        }
        return "E" + code;
    }

    /**
     * @Description: 考场编号
     * @Author: <PERSON>
     * @Date:11:37 2021/8/23
     */
    public static String getExaminationPaperNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 6) {
            code = String.format("%05d", num);
        }
        return "P" + code;
    }

    /**
     * @Description: 考题编号
     * @Author: Jerry
     * @Date:9:53 2021/8/27
     */
    public static String getQuestionNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 9) {
            code = String.format("%08d", num);
        }
        return "Q" + code;
    }
}
