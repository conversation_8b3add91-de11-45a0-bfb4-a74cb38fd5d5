package com.get.salecenter.service.impl;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.vo.InstitutionProviderVo;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.EventPlanMapper;
import com.get.salecenter.vo.EventPlanVo;
import com.get.salecenter.vo.EventPlanFormVo;
import com.get.salecenter.vo.EventPlanFormResoutVo;
import com.get.salecenter.vo.EventPlanThemeFormVo;
import com.get.salecenter.vo.EventPlanThemeFormHeaderVo;
import com.get.salecenter.entity.EventPlan;
import com.get.salecenter.entity.EventPlanRegistration;
import com.get.salecenter.entity.EventPlanRegistrationContactPerson;
import com.get.salecenter.entity.EventPlanRegistrationEvent;
import com.get.salecenter.entity.EventPlanTheme;
import com.get.salecenter.entity.EventPlanThemeOffline;
import com.get.salecenter.entity.EventPlanThemeOfflineItem;
import com.get.salecenter.entity.EventPlanThemeOnline;
import com.get.salecenter.entity.EventPlanThemeWorkshop;
import com.get.salecenter.service.EventPlanRegistrationContactPersonService;
import com.get.salecenter.service.EventPlanRegistrationEventService;
import com.get.salecenter.service.EventPlanRegistrationService;
import com.get.salecenter.service.EventPlanService;
import com.get.salecenter.service.EventPlanThemeOfflineItemService;
import com.get.salecenter.service.EventPlanThemeOfflineService;
import com.get.salecenter.service.EventPlanThemeOnlineService;
import com.get.salecenter.service.EventPlanThemeService;
import com.get.salecenter.service.EventPlanThemeWorkshopService;
import com.get.salecenter.dto.EventPlanRegistrationContactPersonFormDto;
import com.get.salecenter.dto.EventPlanSearchDto;
import com.get.salecenter.dto.EventPlanDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
@Service
public class EventPlanServiceImpl extends ServiceImpl<EventPlanMapper, EventPlan> implements EventPlanService {

    @Resource
    private EventPlanMapper eventPlanMapper;

    @Resource
    private EventPlanThemeService eventPlanThemeService;

    @Resource
    private EventPlanThemeOnlineService eventPlanThemeOnlineService;

    @Resource
    private EventPlanThemeOfflineService eventPlanThemeOfflineService;

    @Resource
    private EventPlanThemeWorkshopService eventPlanThemeWorkshopService;

    @Resource
    private EventPlanThemeOfflineItemService eventPlanThemeOfflineItemService;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private UtilService utilService;

    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Resource
    private EventPlanRegistrationEventService registrationEventService;

    @Resource
    private EventPlanRegistrationContactPersonService personService;

    @Resource
    private EventPlanRegistrationService registrationService;

    @Override
    public EventPlanFormVo getForm(Long id, Long fkEventPlanRegistrationId) {
        EventPlan eventPlan = eventPlanMapper.selectById(id);
        if(GeneralTool.isEmpty(eventPlan)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        EventPlanFormVo dto = BeanCopyUtils.objClone(eventPlan, EventPlanFormVo::new);
        //公司名称
        String fkCompanyName = permissionCenterClient.getCompanyNameById(dto.getFkCompanyId()).getData();
        dto.setFkCompanyName(fkCompanyName);
        //活动计划主题
        List<EventPlanTheme> eventPlanThemes = eventPlanThemeService.list(Wrappers.<EventPlanTheme>lambdaQuery()
                .eq(EventPlanTheme::getFkEventPlanId, id)
                .orderByDesc(EventPlanTheme::getViewOrder));


        if(GeneralTool.isNotEmpty(eventPlanThemes)){
            List<Long> themesIds = eventPlanThemes.stream().map(EventPlanTheme::getId).collect(Collectors.toList());
            //线上活动类型项目
            List<EventPlanThemeOnline> onlinelist = eventPlanThemeOnlineService.list(Wrappers.<EventPlanThemeOnline>lambdaQuery()
                    .in(EventPlanThemeOnline::getFkEventPlanThemeId, themesIds).orderByDesc(EventPlanThemeOnline::getViewOrder));

            //线下活动类型项目
            List<EventPlanThemeOffline> offlinelist = eventPlanThemeOfflineService.list(Wrappers.<EventPlanThemeOffline>lambdaQuery()
                    .in(EventPlanThemeOffline::getFkEventPlanThemeId,themesIds).orderByDesc(EventPlanThemeOffline::getViewOrder));

            //线下专访类型项目
            List<EventPlanThemeWorkshop> workshoplist = eventPlanThemeWorkshopService.list(Wrappers.<EventPlanThemeWorkshop>lambdaQuery()
                    .in(EventPlanThemeWorkshop::getFkEventPlanThemeId,themesIds).orderByDesc(EventPlanThemeWorkshop::getViewOrder));

            //已存在报名名册
            List<EventPlanRegistrationEvent> registrationEventList = new ArrayList<>();
            if(GeneralTool.isNotEmpty(fkEventPlanRegistrationId)){
                //查询已经报名的项目
                registrationEventList = registrationEventService.list(Wrappers.<EventPlanRegistrationEvent>lambdaQuery()
                        .select(EventPlanRegistrationEvent::getFkTableName,EventPlanRegistrationEvent::getFkTableId)
                        .eq(EventPlanRegistrationEvent::getFkEventPlanRegistrationId, fkEventPlanRegistrationId)
                        .and(wrapper->{
                            wrapper.eq(EventPlanRegistrationEvent::getIsCancel,false)
                                    .or()
                                    .isNull(EventPlanRegistrationEvent::getIsCancel);
                        })
                        .groupBy(EventPlanRegistrationEvent::getFkTableName,EventPlanRegistrationEvent::getFkTableId)
                );
            }


            //属性设置
            List<EventPlanFormResoutVo> resoutDtos = new ArrayList<>();
            for(EventPlanTheme theme:eventPlanThemes){
                EventPlanFormResoutVo resoutDto = new EventPlanFormResoutVo();
                List<EventPlanThemeFormVo> eventPlanThemeFormVoList = new ArrayList<>();
                //线上
                if(ProjectExtraEnum.EVENT_PLAN_THEME_ONLINE.key.equals(theme.getDisplayType())){
                    if(GeneralTool.isNotEmpty(onlinelist)){
                        List<EventPlanThemeOnline> onlines = onlinelist.stream()
                                .filter(o -> theme.getId().equals(o.getFkEventPlanThemeId()))
                                .sorted(Comparator.comparing(EventPlanThemeOnline::getViewOrder).reversed())
                                .collect(Collectors.toList());
                        //线上已报名的项目
                        Map<Long, EventPlanRegistrationEvent> registrationMap = new HashMap<>();
                        if(GeneralTool.isNotEmpty(registrationEventList)){
                            List<EventPlanRegistrationEvent> onlineRegistrationList = registrationEventList.stream()
                                    .filter(r -> "m_event_plan_theme_online".equals(r.getFkTableName())).collect(Collectors.toList());
                           if(GeneralTool.isNotEmpty(onlineRegistrationList)){
                               registrationMap = onlineRegistrationList.stream()
                                       .collect(Collectors.toMap(EventPlanRegistrationEvent::getFkTableId, Function.identity() ));
                           }

                        }
                        for(EventPlanThemeOnline online:onlines){
                            EventPlanThemeFormVo themeFormDto = BeanCopyUtils.objClone(theme, EventPlanThemeFormVo::new);
                            themeFormDto.setFkEventPlanThemeOnlineId(online.getId());
                            themeFormDto.setIsActive(online.getIsActive());
                            //主题为不激活状态，所有项目都为不激活状态
                            if(!theme.getIsActive()){
                                themeFormDto.setIsActive(theme.getIsActive());
                            }
                            themeFormDto.setIsSelected(GeneralTool.isEmpty(registrationMap.get(online.getId())) ? false : true);
                            themeFormDto.setName(online.getName());
                            StringBuffer sb = new StringBuffer();
                            sb.append(online.getFkCurrencyTypeNum())
                                    .append(online.getAmount().stripTrailingZeros().toPlainString())
                                    .append("/")
                                    .append(online.getUnit());
                            themeFormDto.setFee(sb.toString());
                            themeFormDto.setDisplayTypeName(ProjectExtraEnum.EVENT_PLAN_THEME_ONLINE.value);
                            eventPlanThemeFormVoList.add(themeFormDto);
                        }
                        //表单头设置
                        List<String> headerList = new ArrayList<>(Arrays.asList("Activity","Charge of fee","Please fill\n" + "(number/tick)"));
                        List<String> fieldList = new ArrayList<>(Arrays.asList("name","fee","fill"));
                        List<EventPlanThemeFormHeaderVo> headerDtos = new ArrayList<>();
                        int i = 0;
                        for (String s : headerList) {
                            EventPlanThemeFormHeaderVo headerDto = new EventPlanThemeFormHeaderVo();
                            headerDto.setProp(fieldList.get(i));
                            headerDto.setLabel(s);
                            headerDtos.add(headerDto);
                            i++;
                        }
                        resoutDto.setFormHeaders(headerDtos);
                    }
                }


                //线下
                if(ProjectExtraEnum.EVENT_PLAN_THEME_OFFLINE.key.equals(theme.getDisplayType())){
                    if(GeneralTool.isNotEmpty(offlinelist)){
                        List<EventPlanThemeOffline> offlines = offlinelist.stream()
                                .filter(o -> theme.getId().equals(o.getFkEventPlanThemeId()))
                                .sorted(Comparator.comparing(EventPlanThemeOffline::getViewOrder).reversed())
                                .collect(Collectors.toList());
                        //线下子项已报名项目
                        Map<Long, EventPlanRegistrationEvent> registrationMap = new HashMap<>();
                        if(GeneralTool.isNotEmpty(registrationEventList)){
                            List<EventPlanRegistrationEvent> offlineItemRegistrationList = registrationEventList.stream()
                                    .filter(r -> "m_event_plan_theme_offline_item".equals(r.getFkTableName())).collect(Collectors.toList());
                            if(GeneralTool.isNotEmpty(offlineItemRegistrationList)){
                                registrationMap = offlineItemRegistrationList.stream()
                                        .collect(Collectors.toMap(EventPlanRegistrationEvent::getFkTableId, Function.identity()));
                            }

                        }

                        List<EventPlanThemeOfflineItem> OfflineItemList = new ArrayList<>();
                        if(GeneralTool.isNotEmpty(offlines)){
                            List<Long>  offlineIds = offlines.stream().map(EventPlanThemeOffline::getId).collect(Collectors.toList());
                            //线下活动类型项目子项列表
                            OfflineItemList = eventPlanThemeOfflineItemService.list(Wrappers.<EventPlanThemeOfflineItem>lambdaQuery()
                                    .in(EventPlanThemeOfflineItem::getFkEventPlanThemeOfflineId, offlineIds)
                                    .orderByDesc(EventPlanThemeOfflineItem::getViewOrder));
                        }

                        for(EventPlanThemeOffline offline:offlinelist){
                            if(GeneralTool.isNotEmpty(OfflineItemList)){
                                List<EventPlanThemeOfflineItem> itemList = OfflineItemList.stream()
                                        .filter(i -> offline.getId().equals(i.getFkEventPlanThemeOfflineId()))
                                        .sorted(Comparator.comparing(EventPlanThemeOfflineItem::getViewOrder).reversed())
                                        .collect(Collectors.toList());
                                for(EventPlanThemeOfflineItem item:itemList){
                                    EventPlanThemeFormVo themeFormDto = BeanCopyUtils.objClone(theme, EventPlanThemeFormVo::new);
                                    themeFormDto.setFkEventPlanThemeOfflineId(offline.getId());
                                    themeFormDto.setFkEventPlanThemeOfflineItemId(item.getId());
                                    themeFormDto.setIsActive(item.getIsActive());
                                    //线下活动为不激活，子项都为不激活
                                    if(!offline.getIsActive()){
                                        themeFormDto.setIsActive(offline.getIsActive());
                                    }
                                    //主题为不激活状态，所有项目都为不激活状态
                                    if(!theme.getIsActive()){
                                        themeFormDto.setIsActive(theme.getIsActive());
                                    }
                                    themeFormDto.setIsSelected(GeneralTool.isEmpty(registrationMap.get(item.getId())) ? false : true);
                                    themeFormDto.setAreaCountryName(offline.getAreaCountryName());
                                    themeFormDto.setDescription(offline.getDescription());
                                    themeFormDto.setLocation(item.getLocation());
                                    themeFormDto.setDate(item.getDate());
                                    StringBuffer sb = new StringBuffer();
                                    sb.append(item.getFkCurrencyTypeNum())
                                            .append(item.getAmount().stripTrailingZeros().toPlainString())
                                            .append("/")
                                            .append(item.getUnit());
                                    themeFormDto.setFee(sb.toString());
                                    themeFormDto.setDisplayTypeName(ProjectExtraEnum.EVENT_PLAN_THEME_OFFLINE.value);
                                    eventPlanThemeFormVoList.add(themeFormDto);
                                }

                                //表单头设置
                                List<String> headerList = new ArrayList<>(Arrays.asList("Activity","Nation","Location","Date\n" + "(MM-YY)","Charge of fee","Please tick","Note"));
                                List<String> fieldList = new ArrayList<>(Arrays.asList("subTitle","areaCountryName","location","date","fee","fill","description"));
                                List<EventPlanThemeFormHeaderVo> headerDtos = new ArrayList<>();
                                int i = 0;
                                for (String s : headerList) {
                                    EventPlanThemeFormHeaderVo headerDto = new EventPlanThemeFormHeaderVo();
                                    headerDto.setProp(fieldList.get(i));
                                    headerDto.setLabel(s);
                                    headerDtos.add(headerDto);
                                    i++;
                                }
                                resoutDto.setFormHeaders(headerDtos);
                            }
                        }


                    }
                }


                //线下专访
                if(ProjectExtraEnum.EVENT_PLAN_THEME_WORKSHOP.key.equals(theme.getDisplayType())){
                    if(GeneralTool.isNotEmpty(workshoplist)){
                        List<EventPlanThemeWorkshop> workshops = workshoplist.stream()
                                .filter(o -> theme.getId().equals(o.getFkEventPlanThemeId()))
                                .sorted(Comparator.comparing(EventPlanThemeWorkshop::getViewOrder).reversed())
                                .collect(Collectors.toList());
                        //线下专访已报名项目
                        Map<Long, EventPlanRegistrationEvent> registrationMap = new HashMap<>();
                        if(GeneralTool.isNotEmpty(registrationEventList)){
                            List<EventPlanRegistrationEvent> workshopRegistrationList = registrationEventList.stream()
                                    .filter(r -> "m_event_plan_theme_workshop".equals(r.getFkTableName())).collect(Collectors.toList());
                            if(GeneralTool.isNotEmpty(workshopRegistrationList)){
                                registrationMap = workshopRegistrationList.stream()
                                        .collect(Collectors.toMap(EventPlanRegistrationEvent::getFkTableId, Function.identity()));
                            }

                        }
                        for(EventPlanThemeWorkshop workshop:workshops){
                            EventPlanThemeFormVo themeFormDto = BeanCopyUtils.objClone(theme, EventPlanThemeFormVo::new);
                            themeFormDto.setFkEventPlanThemeWorkShopId(workshop.getId());
                            themeFormDto.setIsActive(workshop.getIsActive());
                            //主题为不激活状态，所有项目都为不激活状态
                            if(!theme.getIsActive()){
                                themeFormDto.setIsActive(theme.getIsActive());
                            }
                            themeFormDto.setIsSelected(GeneralTool.isEmpty(registrationMap.get(workshop.getId())) ? false : true);
                            themeFormDto.setLocation(workshop.getLocation());
                            themeFormDto.setDate(workshop.getDate());
                            themeFormDto.setDuration(workshop.getDuration());
                            themeFormDto.setScale(workshop.getScale());
                            StringBuffer sb = new StringBuffer();
                            sb.append(workshop.getFkCurrencyTypeNum())
                                    .append(workshop.getAmount().stripTrailingZeros().toPlainString())
                                    .append("/")
                                    .append(workshop.getUnit());
                            themeFormDto.setFee(sb.toString());
                            themeFormDto.setNote(workshop.getNote());
                            themeFormDto.setDisplayTypeName(ProjectExtraEnum.EVENT_PLAN_THEME_WORKSHOP.value);
                            eventPlanThemeFormVoList.add(themeFormDto);
                        }

                        //表单头设置
                        List<String> headerList = new ArrayList<>(Arrays.asList("Activity","Location","Date","Duration","Scale","Charge of fee","Please tick","Note"));
                        List<String> fieldList = new ArrayList<>(Arrays.asList("subTitle","location","date","duration","scale","fee","fill","note"));
                        List<EventPlanThemeFormHeaderVo> headerDtos = new ArrayList<>();
                        int i = 0;
                        for (String s : headerList) {
                            EventPlanThemeFormHeaderVo headerDto = new EventPlanThemeFormHeaderVo();
                            headerDto.setProp(fieldList.get(i));
                            headerDto.setLabel(s);
                            headerDtos.add(headerDto);
                            i++;
                        }
                        resoutDto.setFormHeaders(headerDtos);
                    }
                }

                resoutDto.setFormName(theme.getMainTitle());
                resoutDto.setFormData(eventPlanThemeFormVoList);
                resoutDtos.add(resoutDto);
            }


            dto.setResoutDtos(resoutDtos);
        }

        if(GeneralTool.isNotEmpty(fkEventPlanRegistrationId)){
            //查询报名名册信息
            EventPlanRegistration eventPlanRegistration = registrationService.getById(fkEventPlanRegistrationId);
            //查询联系人信息
            List<EventPlanRegistrationContactPerson> personList = personService.list(Wrappers.<EventPlanRegistrationContactPerson>lambdaQuery()
                    .eq(EventPlanRegistrationContactPerson::getFkEventPlanRegistrationId, fkEventPlanRegistrationId));
            if(GeneralTool.isNotEmpty(eventPlanRegistration)){
                dto.setInstitutionProviderId(eventPlanRegistration.getFkInstitutionProviderId());
                dto.setCurrencyTypeNumInvoice(eventPlanRegistration.getFkCurrencyTypeNumInvoice());
            }
            if(GeneralTool.isNotEmpty(personList)){
                List<EventPlanRegistrationContactPersonFormDto> eventPlanRegistrationContactPersonFormDtos = BeanCopyUtils.copyListProperties(personList, EventPlanRegistrationContactPersonFormDto::new);
                dto.setPersonList(eventPlanRegistrationContactPersonFormDtos);

            }
        }

        return dto;
    }


    @Override
    public List<EventPlanVo> getEventPlans(EventPlanSearchDto vo, Page page){

        IPage<EventPlanVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));

        List<EventPlanVo> eventPlanVos = eventPlanMapper.getEventPlans(iPage, vo);
        //设置公司名
        Set<Long> fkCompanyIds = eventPlanVos.stream().map(EventPlanVo::getFkCompanyId).collect(Collectors.toSet());
        Map<Long, String> fkCompanyNameMap = permissionCenterClient.getCompanyNamesByIds(fkCompanyIds).getData();
        eventPlanVos.stream().forEach(e->e.setFkCompanyName(fkCompanyNameMap.get(e.getFkCompanyId())));
        page.setAll((int) iPage.getTotal());
        return eventPlanVos;
    }

    @Override
    public EventPlanVo findEventPlanById(Long id){
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        EventPlan eventPlan = eventPlanMapper.selectById(id);
        EventPlanVo eventPlanVo = BeanCopyUtils.objClone(eventPlan, EventPlanVo::new);
        //公司名称
        String fkCompanyName = permissionCenterClient.getCompanyNameById(eventPlanVo.getFkCompanyId()).getData();
        eventPlanVo.setFkCompanyName(fkCompanyName);
        return eventPlanVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addEventPlan(EventPlanDto eventPlanDto) {
        if (GeneralTool.isEmpty(eventPlanDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        EventPlan eventPlan = BeanCopyUtils.objClone(eventPlanDto, EventPlan::new);
        utilService.setCreateInfo(eventPlan);
        eventPlanMapper.insert(eventPlan);
        return eventPlan.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        EventPlan eventPlan = eventPlanMapper.selectById(id);
        if (GeneralTool.isEmpty(eventPlan)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //判断能否删除
        List<EventPlanTheme> themeList = eventPlanThemeService.list(Wrappers.<EventPlanTheme>lambdaQuery()
                .eq(EventPlanTheme::getFkEventPlanId, eventPlan.getId()));
        if(GeneralTool.isNotEmpty(themeList)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("has_associated"));
        }
        int delete = eventPlanMapper.deleteById(id);
        if (delete <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public EventPlanVo updateEventPlan(EventPlanDto vo) {
        if (GeneralTool.isEmpty(vo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        EventPlan eventPlan = eventPlanMapper.selectById(vo.getId());
        if (GeneralTool.isEmpty(eventPlan)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        EventPlan eventPlan1 = BeanCopyUtils.objClone(vo, EventPlan::new);
        utilService.setUpdateInfo(eventPlan1);
        int update = eventPlanMapper.updateById(eventPlan1);

        if (update <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }

        return findEventPlanById(eventPlan1.getId());
    }

    @Override
    public List<Map<Long, String>> getCompanyList() {
        List<Long> companyIds = eventPlanMapper.getCompanyList();
        if(GeneralTool.isEmpty(companyIds)){
            return Collections.emptyList();
        }
        Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(new HashSet<>(companyIds));
        List<Map<Long, String>> list = new ArrayList<>();
        if(result.isSuccess()){
            Map<Long, String> data = result.getData();
            for (Map.Entry<Long, String> da : data.entrySet()) {
                Map companyMap = new HashMap();
                companyMap.put("id",da.getKey());
                companyMap.put("name",da.getValue());
                list.add(companyMap);
            }
        }
        return list;
    }

    @Override
    public List<Integer> getYearList() {
        return eventPlanMapper.getYearList();
    }

    @Override
    public List<InstitutionProviderVo> getInstitutionProvidersByName(String keyword){
        List<InstitutionProviderVo> institutionProviders = institutionCenterClient.getInstitutionProvidersByName(keyword);
        if(GeneralTool.isEmpty(institutionProviders)){
            return Collections.emptyList();
        }
        return institutionProviders;
    }
}
