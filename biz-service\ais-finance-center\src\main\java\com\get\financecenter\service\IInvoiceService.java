package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.core.mybatis.utils.ValidList;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import com.get.financecenter.vo.InvoiceSelectVo;
import com.get.financecenter.vo.InvoiceVo;
import com.get.financecenter.entity.Invoice;
import com.get.financecenter.dto.*;
import com.get.financecenter.dto.InvoiceDto;
import com.get.financecenter.dto.query.InvoiceQueryDto;
import com.get.salecenter.dto.PrepaymentButtonHtiDto;
import com.get.salecenter.vo.ReceivablePlanVo;
import com.get.salecenter.dto.InvoiceReceiptFormDto;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/12/16 17:05
 * @verison: 1.0
 * @description:
 */
public interface IInvoiceService extends BaseService<Invoice> {
    /**
     * @return com.get.salecenter.vo.InvoiceDto
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    InvoiceVo findInvoiceById(Long id);


    /**
     * 编辑佣金通知
     * @param invoiceId
     * @param receivablePlanId
     * @param remark
     * @return
     */
    SaveResponseBo editCommissionNotice(Long invoiceId,Long receivablePlanId,String remark);

    /**
     * 批量修改绑定金额
     * @return
     * @param batchUpdateBindingAmountDto
     */
    SaveResponseBo batchUpdateBindingAmount(BatchUpdateBindingAmountDto batchUpdateBindingAmountDto);

    /**
     * @return void
     * @Description :新增
     * @Param [invoiceVo]
     * <AUTHOR>
     */
    Long addInvoice(InvoiceDto invoiceDto);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.salecenter.vo.InvoiceDto
     * @Description :修改
     * @Param [invoiceVo]
     * <AUTHOR>
     */
    InvoiceVo updateInvoice(InvoiceDto invoiceDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.InvoiceDto>
     * @Description :列表
     * @Param [invoiceVo, page]
     * <AUTHOR>
     */
    List<InvoiceVo> getInvoices(InvoiceQueryDto invoiceVo, Page page);

    /**
     * @return java.util.List<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 附件列表
     * @Param [data, page]
     * <AUTHOR>
     */
    List<FMediaAndAttachedVo> getItemMedia(MediaAndAttachedDto data, Page page);

    /**
     * @return java.lang.Long
     * @Description: 新增附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    List<FMediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * @return void
     * @Description :是否作废
     * @Param [invoiceId]
     * <AUTHOR>
     */
    void isCancel(Long invoiceId,Integer status);

    /**
     * 发票下拉框数据
     *
     * @return
     */
    List<BaseSelectEntity> getInvoiceSelect();


    /**
     * 获取所有的发票编号map
     *
     * @return
     */
    Map<Long, String> getAllNums();

    /**
     * 根据发票ID获取应收计划列表
     *
     * @param page
     * @return
     */
    List<ReceivablePlanVo> getReceivablePlans(InvoiceReceivablePlanDto data, Page page);

    /**
     *  根据发票ID获取应收计划ID集合
     */
    List<Long> doGetReceivablePlanIds(Long invoiceId);

    /**
     * 根据计划Ids获取发票编码
     *
     * @param palnIds
     * @return
     */
    Map<Long, String> getFkInvoiceNum(Set<Long> palnIds);

    /**
     * 根据提供商id获取对应发票下拉接口
     *
     * @param fkTypeTargetId
     * @param fkTypeKey
     * @return
     */
    List<InvoiceSelectVo> getInvoiceSelectByTargetId(Long fkTypeTargetId, String fkTypeKey, Long fkReceiptFormId);

    /**
     * 获取收款单发票下拉列表
     * @return
     */
    List<InvoiceSelectVo> doGetInvoiceSelectList(InvoiceReceiptFormDto invoiceReceiptFormDto);


    /**
     * 获取所有发票
     * @param receivableInvoiceQueryDto
     * @return
     */
    List<BaseSelectEntity> doGetAllInvoice(ReceivableInvoiceQueryDto receivableInvoiceQueryDto);


    List<Invoice> getInvoiceByIds(List<Long> ids);
    /**
     * @return void
     * @Description :解绑应收计划
     * @Param [invoiceVo]
     * <AUTHOR>
     */
    void unBoundPlans(List<Long> ids);

    /**
     * 获取绑定的发票编号
     *
     * @param invoiceBindDto
     * @return
     */
    String getBindInvoiceNums(InvoiceBindDto invoiceBindDto);


    /**
     * 批量建立发票和应收计划映射
     * @param invoiceReceivablePlanBatchDto
     * @return
     */
    ResponseBo batchMappingInvoiceAndReceivablePlan(InvoiceReceivablePlanBatchDto invoiceReceivablePlanBatchDto);

    /**
     * 根据传入的发票id 获取发票金额
     * @param invoiceIds
     * @return
     */
    ResponseBo<BigDecimal> doGetInvoiceTotalAmountByIds(Set<Long> invoiceIds);

    /**
     * 生成发票  返回发票id
     *
     * @param invoiceDto
     * @return
     */
    Long generateInvoice(InvoiceDto invoiceDto);

    /**
     * 绑定发票和应收计划
     *
     * @param invoiceReceivablePlanDto
     */
    void generateInvoiceReceivablePlan(InvoiceReceivablePlanDto invoiceReceivablePlanDto);

    /**
     * 根据编号获取发票
     *
     * @param invoiceNum
     * @return
     */
    List<Invoice> getInvoiceByNum(String invoiceNum);


    /**
     * 获取应收排序后的发票编号
     * @param ids
     * @return
     */
    Map<Long,String> getInvoiceNumByReceivableId(Set<Long> ids);
    /**
     * 创建发票和目标对象映射
     * @param typeKey
     * @param invoiceId
     * @param targetId
     */
    void createInvoiceAndTargetMapping(String typeKey,Long invoiceId,Long targetId);

    /**
     * 更新发票和目标对象映射
     * @param typeKey
     * @param invoiceId
     * @param targetId
     */
    void updateInvoiceAndTargetMapping(String typeKey,Long invoiceId,Long targetId);

    /**
     * 发票编号获取发票对象
     *
     * @param numSet
     * @return
     */
    List<InvoiceVo> getInvoiceByNumList(Set<String> numSet);

    /**
     * 获取指定类型发票最大序号
     *
     * @param num
     * @return
     */
    Integer getMaxPoNum(String num);

    /**
     * 获取重复的发票
     *
     * @param poNum
     * @param invoiceNum
     * @return
     */
    Invoice isExistNum(Integer poNum, String invoiceNum);

    /**
     * 解除与发票的绑定
     * 如果该发票只有这条绑定记录，同时作废发票
     * @param receivablePlanIdset
     * @return
     */
    Boolean unBindInvoiceByReceivablePlanIds(List<Long> receivablePlanIdset);

    /**
     * 同步实收收款日期到发票
     * @param fkInvoiceIdList
     * @param receiptDate
     */
    void syncToInvoiceReceiptDate(List<Long> fkInvoiceIdList, Date receiptDate);

    /**
     * 导出发票列表Excel
     * @param response
     * @param invoiceVo
     */
    void exportInvoicesExcel(HttpServletResponse response, InvoiceQueryDto invoiceVo);

    List<Long> getInvoiceIdsByReceivablePlanId(Long receivablePlanId);

    Boolean isExistInvoiceNum(String invoiceNum);

    /**
     * HTI预付按钮
     *
     * @return
     * @Date 17:52 2024/6/13
     * <AUTHOR>
     */
    String prepaymentButtonHti(List<PrepaymentButtonHtiDto> prepaymentButtonHtiDtoList);

    /**
     * HTI取消预付按钮
     * @param prepaymentButtonHtiDtoList
     * @return
     */
    String cancelPrepaymentButtonHti(List<PrepaymentButtonHtiDto> prepaymentButtonHtiDtoList);

}
