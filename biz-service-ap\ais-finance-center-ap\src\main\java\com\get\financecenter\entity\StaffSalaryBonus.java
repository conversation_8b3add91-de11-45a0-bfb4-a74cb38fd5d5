package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "m_staff_salary_bonus")
@Data
public class StaffSalaryBonus extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("类型Key：salary / bonus")
    private String typeKey;

    @ApiModelProperty("员工ID")
    private Long fkStaffId;

    @ApiModelProperty("公司id")
    private Long fkCompanyId;

    @ApiModelProperty("年月，如：20250")
    @TableField("`year_month`")
    private String yearMonth;

    @ApiModelProperty("币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty("金额，工资：发卡工资 / 奖金：合计奖金")
    private BigDecimal amount;

    @ApiModelProperty("明细Json")
    private String jsonDetail;

    @ApiModelProperty("备注")
    private String remark;


    @ApiModelProperty("导入文件")
    private String importFileName;




}
