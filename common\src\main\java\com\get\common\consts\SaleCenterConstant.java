package com.get.common.consts;

/**
 * @author: <PERSON>
 * @create: 2021/11/26 16:29
 * @verison: 1.0
 * @description:
 */
public class SaleCenterConstant {

    public static final String OFFER_REMINDER = "<div class=\"desc\">\n" +
            "    <div>代理名称：${agentName}</div>\n" +
            "    <div>学生姓名：${studentName}</div>\n" +
            "    <div>学生生日：${birthday}</div>\n" +
            "    <div>申请国家：${fkAreaCountryName}</div>\n" +
            "    <div>申请学校：${fkInstitutionName}</div>\n" +
            "    <div>申请课程：${fkCourseName}</div>\n" +
            "    <div>申请方式：${appMethodName}</div>\n" +
            "    <div>课程长度：${duration}${durationTypeName}</div>\n" +
            "    <div>开学时间：${openingTime}</div>\n" +
            "</div>";

    public static final String PAYDEPOSIT_REMINDER = "<div class=\"desc\">\n" +
            "    <div>代理名称：${agentName}</div>\n" +
            "    <div>学生姓名：${studentName}</div>\n" +
            "    <div>学生生日：${birthday}</div>\n" +
            "    <div>申请国家：${fkAreaCountryName}</div>\n" +
            "    <div>申请学校：${fkInstitutionName}</div>\n" +
            "    <div>申请课程：${fkCourseName}</div>\n" +
            "    <div>申请方式：${appMethodName}</div>\n" +
            "    <div>课程长度：${duration}${durationTypeName}</div>\n" +
            "    <div>开学时间：${openingTime}</div>\n" +
            "</div>";

    public static final String SEA_OFFER_REMINDER = "<div class=\"desc\">\n" +
            "    <div>Agent Name：${agentName}</div>\n" +
            "    <div>Student Name：${studentName}</div>\n" +
            "    <div>Date of Birth：${birthday}</div>\n" +
            "    <div>Country Applies for：${fkAreaCountryName}</div>\n" +
            "    <div>School Applies for：${fkInstitutionName}</div>\n" +
            "    <div>Course Applies for：${fkCourseName}</div>\n" +
            "    <div>Application Method：${appMethodName}</div>\n" +
            "    <div>Program Duration：${duration}${durationTypeName}</div>\n" +
            "    <div>Program Starts Date：${openingTime}</div>\n" +
            "</div>";

    public static final String SEA_PAYDEPOSIT_REMINDER = "<div class=\"desc\">\n" +
            "    <div>Agent Name：${agentName}</div>\n" +
            "    <div>Student Name：${studentName}</div>\n" +
            "    <div>Date of Birth：${birthday}</div>\n" +
            "    <div>Country Applies for：${fkAreaCountryName}</div>\n" +
            "    <div>School Applies for：${fkInstitutionName}</div>\n" +
            "    <div>Course Applies for：${fkCourseName}</div>\n" +
            "    <div>Application Method：${appMethodName}</div>\n" +
            "    <div>Program Duration：${duration}${durationTypeName}</div>\n" +
            "    <div>Program Starts Date：${openingTime}</div>\n" +
            "</div>";

    public static final String COURSE_OPEN_TIME = "<div class=\"desc\">\n" +
            "    <div>学生姓名：${studentName}</div>\n" +
            "    <div>学生生日：${birthday}</div>\n" +
            "    <div>申请国家：${fkAreaCountryName}</div>\n" +
            "    <div>申请学校：${fkInstitutionName}</div>\n" +
            "    <div>申请课程：${fkCourseName}</div>\n" +
            "    <div>开学时间：${openingTime}</div>\n" +
            "</div>";

    public static final String STUDENT_OFFER_ITEM_DISCLAIMER = "<div class=\"desc\" style = \"color:#666666\">\n" +"<div>  此邮件为无偿、额外、友情、善意的提醒服务，其唯一目的就是通过邮件方式再次提醒您/贵司重视OFFER项下记载的录取信息和押金支付期限，邮件发送者对如下可能发生的情形不承担任何法律责任，该等情形包括但不限于：</div>\n" +
            "<div>1.因任何人为或系统原因导致邮件未能推送成功的；</div>\n" +
            "<div>2.因任何人为或系统原因导致邮件记载的提醒信息与OFFER记载内容不一致的；</div>\n" +
            "<div>3.录取院校临时更新录取政策导致OFFER记载内容和效力发生变化的。</div>"+
            "</div>";

    //延期入学提醒模板
    public static final String DEFERENTRANCE_REMINDER = "<div class=\"desc\">\n" +
            "    <div>学生：${studentName}</div>\n" +
            "    <div>代理：${agentName}</div>\n" +
            "    <div>学校：${institutionName}</div>\n" +
            "    <div>课程：${courseName}</div>\n" +
            "    <div>开学时间：${diffDateTime}</div>\n" +
            "</div>";

    //延期入学提醒模板
    public static final String APP_AGENT_REMINDER = "<div class=\"desc\">\n" +
            "    <div>${staffName}您好：</div>\n" +
            "    <div>代理：${agentName}${other}</div>\n" +
            "</div>";

    //延期入学提醒英文模板
    public static final String APP_AGENT_REMINDER_ENGLISH = "<div class=\"desc\">\n" +
            "    <div>${staffName}Hello：</div>\n" +
            "    <div>Agent Name：${agentName}${other}</div>\n" +
            "</div>";


    //审批意见内容
    public static final String APP_AGENT_APPROVE_COMMENT_REMINDER = "<div class=\"desc\">\n" +
            "    <div>${staffName}，您好：</div>\n" +
            "    <div>申请代理：${agentName}</div>\n" +
            "    <div>审批意见如下：${approveComment}</div>\n" +
            "    <div>在线表单修改：<a style='display:inline-block;margin-top:0' href=\"${taskLink}\">${taskLink}</a></div>\n" +
            "</div>";

    //审批意见内容
    public static final String APP_AGENT_APPROVE_COMMENT_REMINDER_ENGLISH = "<div class=\"desc\">\n" +
            "    <div>${staffName}，Hello：</div>\n" +
            "    <div>Application Agent：${agentName}</div>\n" +
            "    <div>The approval opinions are as follows：${approveComment}</div>\n" +
            "    <div>Online form modification：<a style='display:inline-block;margin-top:0' href=\"${taskLink}\">${taskLink}</a></div>\n" +
            "</div>";


    //校验申请计划学校、课程模板
    public static final String CREATE_OFFER_ITEM_INSTITION_COURSE_REMINDER = "<div class=\"desc\">\n" +
            "    <div>学生：${studentName}</div>\n" +
            "    <div>申请国家：${fkAreaCountryName}</div>\n" +
            "    <div>申请学校：${fkInstitutionName}</div>\n" +
            "    <div>申请课程：${fkCourseName}</div>\n" +
            "</div>";
    // 申请计划付费状态发生改变，邮件通知文案（ARC）内容
    public static final String STUDENT_OFFER_ITEM_CHANGE_PAYMENT_STATUS_ARC_REMINDER = "<div class=\"desc\">\n" +
            "    <div>学生：${studentName}</div>\n" +
            "    <div>代理：${agentName}</div>\n" +
            "    <div>申请国家：${fkAreaCountryName}</div>\n" +
            "    <div>申请学校：${fkInstitutionName}</div>\n" +
            "    <div>申请课程：${fkCourseName}</div>\n" +
            "    <div>支付类型：${paymentTypeName}</div>\n" +
            "    <div>支付币种：${fkCurrencyTypeNumName}</div>\n" +
            "    <div>支付金额：${paidAmount}</div>\n" +
            "    <div>支付状态：${paymentStatusName}</div>\n" +
            "    <div>备注：${remark}</div>\n" +
            "    <div><a style='display:inline-block;margin-top:0' href=\"${studentOfferItemDetail}\">【邮件可点击查看申请计划详情】</a></div>\n" +
            "</div>";

    // 学生资源过期提醒邮件内容
    public static final String STUDENT_RESOURCE_EXPIRE_REMINDER =
            "    <div>学生资源名称：${name}</div>\n" +
                    "    <div>签证到期日：${visaExpirationDateStr}</div>\n" +
                    "    <div>学生资源创建人：${gmtCreateUser}</div>\n" +
                    "    <div>创建时间：${gmtCreate}</div>\n" ;

    public static final String STUDENT_RESOURCE_CREATED_NOTICE = "<div class=\"desc\">\n" +
            "    <div>学生：${studentName}</div>\n" +
            "    <div>推荐类型：${recommendType}</div>\n" +
            "    <div>${sourceLabel}：${source}</div>\n" +
            "    <div>代理：${agentName}</div>\n" +
            "    <div>BD名称：${bdName}</div>\n" +
            "    <div>创建人：${creator}</div>\n" +
            "    <div>创建时间：${createTime}</div>\n" +
            "</div>";



    // 当活动时间或者活动状态发生变化时，邮件提醒的内容
    public static final String EVENT_DATE_OR_STATUS_CHANGE_REMINDER = "<div class=\"desc\">\n" +
            "    <div>活动时间：${eventTimeName}</div>\n" +
            "    <div>活动状态：${eventStatusName}</div>\n" +
            "    <div>编辑人：${gmtModifiedUser}</div>\n" +
            "    <div>编辑时间：${gmtModified}</div>\n" +
            "</div>";
//    public static final String EVENT_BILL_REMINDER_CONTENT ="${startDivHead} id=\"div1\">" +
//            "学校提供商(收款方)：${fkInstitutionProviderName}${br}" +
//            "活动费用币种：${fkCurrencyTypeNumEventName}${br}" +
//            "发起Invoice币种：${fkCurrencyTypeNumInvoiceName}${br}" +
//            "invoice名目:${invoiceSummary}${br}" +
//            "invoice收件人：${invoiceContactPerson}${br}" +
//            "活动年份：${eventYear}${br}" +
//            "活动摘要：${fkEventSummaryName}${br}" +
//            "备注:${remark}" +
//            "${endDiv}" +
//            "${startDivHead} id=\"div2\">  " +
//            "业务国家：${fkAreaCountryName}${br}" +
//            "活动费用金额：${eventAmount}${br}" +
//            "发起Invoice金额：${invoiceAmount}${br}${br}${br}" +
//            "invoiceEmail：${invoiceContactEmail}${br}" +
//            "活动摘要：${fkEventSummaryName}" +
//            "${endDiv}";
    //特殊复姓
    public static final String COMPOUND_SURNAME = "百里,北堂,北野,北宫,辟闾,淳于,成公,陈生,褚师,端木,东方,东郭,东野,东门,第五,大狐,段干,段阳,带曰,第二,东宫,公孙,公冶,公羊,公良,公西,公孟,高堂,高阳,公析,公肩,公坚,郭公,谷梁,毌将,公乘,毌丘,公户,公广,公仪,公祖,皇甫,黄龙,胡母,何阳,夹谷,九方,即墨,梁丘,闾丘,洛阳,陵尹,冷富,龙丘,令狐,林彭,南宫,南郭,女娲,南伯,南容,南门,南野,欧阳,欧侯,濮阳,青阳,漆雕,亓官,渠丘,壤驷,上官,少室,少叔,司徒,司马,司空,司寇,士孙,申屠,申徒,申鲜,申叔,夙沙,叔先,叔仲,侍其,叔孙,澹台,太史,太叔,太公,屠岸,唐古,闻人,巫马,微生,王孙,无庸,夏侯,西门,信平,鲜于,轩辕,相里,新垣,徐离姓,羊舌,羊角,延陵,於陵,伊祁,吾丘,乐正,诸葛,颛孙,仲孙,仲长,钟离,宗政,主父,中叔,左人,左丘,宰父,长儿,仉督,单于,叱干,叱利,车非,独孤,大野,独吉,达奚,哥舒,赫连,呼延,贺兰,黑齿,斛律,斛粟,贺若,夹谷,吉胡,可频,慕容,万俟,抹捻,纳兰,普周,仆固,仆散,蒲察,屈突,屈卢,钳耳,是云,索卢,厍狄,拓跋,同蹄,秃发,完颜,宇文,尉迟,耶律,长孙";

}
