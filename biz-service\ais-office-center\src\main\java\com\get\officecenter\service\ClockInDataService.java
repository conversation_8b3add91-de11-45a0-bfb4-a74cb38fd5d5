package com.get.officecenter.service;

import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.officecenter.vo.ClockInDataVo;
import com.get.officecenter.entity.ClockInData;
import com.get.officecenter.dto.ClockInDataListDto;
import com.get.officecenter.dto.ClockInDataDto;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/5/10
 * @TIME: 10:40
 * @Description:
 **/
public interface ClockInDataService {
    /**
     * 打卡记录列表
     * @param clockInDataVo
     * @return
     */
    List<ClockInDataVo> getAllClockInData(ClockInDataListDto clockInDataVo, Page page);

    /**
     * 导出打卡记录列表Excel
     * @param response
     * @param clockInDataVo
     * @throws Exception
     */
    void exportClockInDataExcel(HttpServletResponse response, ClockInDataListDto clockInDataVo) throws Exception;

    /**
     * 补卡
     * @param clockInDataDto
     */
    void addClockInData(ClockInDataDto clockInDataDto);

    /**
     * 是否有效设置
     * @param id
     * @param isActive
     */
    void updateActive(Long id,Boolean isActive);

    /**
     * 考勤机数据录入
     * @param file
     */
    ResponseBo importAttendanceMachineData(Long fkCompanyId , MultipartFile file);

    /**
     * 钉钉考勤数据录入
     * @param file
     */
    ResponseBo importDingTalkAttendanceData(Long fkCompanyId ,MultipartFile file);

    /**
     * 获取打卡数据
     * @param startTime
     * @param endTime
     * @return
     */
    List<ClockInData> getClockInData(Date startTime, Date endTime , Long fkCompanyId);
}
