package com.get.helpcenter.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR> by Ron
 * @date 2020/5/18 5:47
 */
@Component
public class SpringUtil implements ApplicationContextAware {

    /**
     * 当前IOC
     */
    private static ApplicationContext applicationContext;

    /**
     * 从当前IOC获取bean
     */
    public static <T> T getObject(Class<T> clazz) {
        return applicationContext.getBean(clazz);
    }

    public static void showClass() {
        String[] beanDefinitionNames = applicationContext.getBeanDefinitionNames();
        for (String beanDefinitionName : beanDefinitionNames) {
            System.out.println(beanDefinitionName);
        }
    }

    /**
     * @return java.lang.String
     * @Description :计算耗时
     * @Param [startTime, endTime, format]
     * <AUTHOR>
     */
    public static String dateDiff(String startTime, String endTime,
                                  String format) {
        SimpleDateFormat sd = new SimpleDateFormat(format);
        long nd = 1000 * 60 * 60 * 24;// 一天的毫秒数
        long nh = 1000 * 60 * 60;// 一小时的毫秒数
        long nm = 1000 * 60;// 一分钟的毫秒数
        long ns = 1000;// 一秒钟的毫秒数
        long diff;
        long day = 0;
        long hour = 0;
        long min = 0;
        long sec = 0;
        try {
            diff = sd.parse(endTime).getTime() - sd.parse(startTime).getTime();
            day = diff / nd;// 计算差多少天
            hour = diff % nd / nh + day * 24;// 计算差多少小时
            min = diff % nd % nh / nm + day * 24 * 60;// 计算差多少分钟
            sec = diff % nd % nh % nm / ns;// 计算差多少秒
            return "时间相差：" + day + "天" + (hour - day * 24) + "小时"
                    + (min - day * 24 * 60) + "分钟" + sec + "秒。";
        } catch (ParseException e) {

            e.printStackTrace();
        }
        return null;
    }

    /**
     * @Author: LEO
     * @Date: ${DATE} ${TIME}
     * @version 1.0
     */
    public static String timeCycle(Date date) {

        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);


    }

    /**
     * 设置applicationContext
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringUtil.applicationContext = applicationContext;
    }

}
