package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

@Data
public class StaffCommissionBatchRefundDto {

    @ApiModelProperty("ids")
    private Set<Long> ids;

    @ApiModelProperty("退款审核内容")
    private String refundReviewContent;

    @ApiModelProperty("退款审核状态：0未审核/1无需退款/2确认退款")
    private Integer refundReviewStatus;

    @ApiModelProperty(value = "结算退款原因Id")
    private Long fkSettlementRefundReasonId;
}
