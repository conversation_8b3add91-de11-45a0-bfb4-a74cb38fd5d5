package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.salecenter.dto.KpiPlanSearchDto;
import com.get.salecenter.entity.KpiPlan;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Mapper
public interface KpiPlanMapper extends BaseMapper<KpiPlan> {

    /**
     * 获取KPI方案数据时间戳之和（所有涉及到KPI统计所需要的数据表）
     *
     * @param fkKpiPlanId kpi方案id
     * @return
     */
    String getSumTimeKpiData(@Param("fkKpiPlanId") Long fkKpiPlanId);

    List<KpiPlan> selectKpiPlanList( IPage<KpiPlan> pages,
                                     @Param("kpiPlanSearchDto") KpiPlanSearchDto kpiPlanSearchDto,
                                     @Param("staffFollowerIds")Set<Long> staffFollowerIds,
                                     @Param("companyIds") Set<String> companyIds,
                                     @Param("currentUser") String currentUser);
}
