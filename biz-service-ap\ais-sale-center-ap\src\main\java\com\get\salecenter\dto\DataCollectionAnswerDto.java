package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 学校机构资料收集表单 答案Dto
 */
@Data
public class DataCollectionAnswerDto {

    @ApiModelProperty(value = "答案内容")
    @NotBlank(message = "答案内容不能为空")
    private String answer;

    /**
     * 是否正确答案：0否/1是
     */
    @ApiModelProperty(value = "是否正确答案：0否/1是")
    @NotNull(message = "是否正确答案不能为空")
    private Boolean isRightAnswer;

}
