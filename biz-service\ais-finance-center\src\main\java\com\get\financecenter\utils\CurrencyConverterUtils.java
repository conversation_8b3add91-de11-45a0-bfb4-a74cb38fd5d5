package com.get.financecenter.utils;

import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.service.IExchangeRateService;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class CurrencyConverterUtils {
    @Resource
    private IExchangeRateService exchangeRateService;

    /**
     * 根据币种动态计算金额
     *
     * @param currencyTypeNumFrom
     * @param currencyTypeNumTo
     * @param amount
     * @return
     */
    private final Map<String, BigDecimal> exchangeRateCache = new HashMap<>();

    //当前币种  转换成目标币种 转换金额
    public BigDecimal convertAmount(String currencyTypeNum, String currencyTypeNumTo, BigDecimal amount) {
        // 尝试从缓存中获取汇率
        Optional<BigDecimal> optionalExchangeRate = Optional.ofNullable(exchangeRateCache.get(currencyTypeNum + "_" + currencyTypeNumTo));
        BigDecimal money = BigDecimal.ZERO;
        String finalCurrencyTypeNumTo = currencyTypeNumTo;
        BigDecimal exchangeRate = optionalExchangeRate.orElseGet(() -> {
            // 如果缓存中没有找到汇率，则通过API获取并缓存
            BigDecimal rate = exchangeRateService.getLastExchangeRate(false, currencyTypeNum, finalCurrencyTypeNumTo).getExchangeRate();
            if (GeneralTool.isEmpty(rate)) {
                rate = BigDecimal.ZERO.setScale(2);
            }
            exchangeRateCache.put(currencyTypeNum + "_" + finalCurrencyTypeNumTo, rate); // 缓存汇率
            return rate;
        });
        if (GeneralTool.isEmpty(exchangeRate)) {
            money = amount;
        } else {
            money = amount.multiply(exchangeRate).setScale(2, BigDecimal.ROUND_HALF_UP);
        }

        //获取汇率后进行转换计算
        return money;

    }
}
