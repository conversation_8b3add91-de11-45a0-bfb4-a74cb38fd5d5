package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.CourseTypeGroupCourseTypeMapper;
import com.get.institutioncenter.dao.CourseTypeMapper;
import com.get.institutioncenter.dao.InstitutionCourseMapper;
import com.get.institutioncenter.dao.InstitutionCourseTypeMapper;
import com.get.institutioncenter.vo.CourseTypeVo;
import com.get.institutioncenter.entity.CourseType;
import com.get.institutioncenter.entity.CourseTypeGroupCourseType;
import com.get.institutioncenter.entity.InstitutionCourseType;
import com.get.institutioncenter.service.ICourseTypeGroupService;
import com.get.institutioncenter.service.ICourseTypeService;
import com.get.institutioncenter.service.ITranslationMappingService;
import com.get.institutioncenter.dto.CourseTypeDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/12/7
 * @TIME: 11:31
 * @Description:
 **/
@Service
public class CourseTypeServiceImpl extends BaseServiceImpl<CourseTypeMapper, CourseType> implements ICourseTypeService {
    @Resource
    private CourseTypeMapper courseTypeMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private ICourseTypeGroupService courseTypeGroupService;
    @Resource
    private InstitutionCourseMapper institutionCourseMapper;
    @Resource
    private CourseTypeGroupCourseTypeMapper courseTypeGroupCourseTypeMapper;
    @Resource
    private InstitutionCourseTypeMapper institutionCourseTypeMapper;
    @Resource
    private ITranslationMappingService translationMappingService;

    @Override
    public CourseTypeVo findCourseTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        CourseType courseType = courseTypeMapper.selectById(id);
        if (GeneralTool.isEmpty(courseType)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        CourseTypeVo courseTypeVo = BeanCopyUtils.objClone(courseType, CourseTypeVo::new);
        courseTypeVo.setFkTableName(TableEnum.INSTITUTION_COURSE_TYPE.key);
        courseTypeVo.setCourseTypeGroupIds(courseTypeGroupService.getCourseTypeIdByCourseTypeId(id));
        courseTypeVo.setFkCourseTypeGroupName(courseTypeGroupService.getCourseTypeGroupNameByCourseTypeId(id));
        if(GeneralTool.isNotEmpty(courseType.getPublicLevel())){
            StringJoiner str = new StringJoiner(",");
            String[] publivLevels = courseType.getPublicLevel().split(",");
            for(String publicLevel: publivLevels){
                str.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(publicLevel),ProjectExtraEnum.PUBLIC_OBJECTS));
            }
            courseTypeVo.setPublicLevelName(str.toString());
        }


        return courseTypeVo;
    }

    @Override
    public List<CourseTypeVo> getCourseTypes(CourseTypeDto courseTypeDto, Page page) {

        IPage<CourseType> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<String> strPublic= new ArrayList<>();
        if(GeneralTool.isNotEmpty(courseTypeDto.getPublicLevel())){
          strPublic = Arrays.asList(courseTypeDto.getPublicLevel().split(","));
        }
        //要按照最新记录排序
        List<CourseType> courseTypes = courseTypeMapper.selectCourseTypeList(pages, courseTypeDto.getKeyWord(), courseTypeDto.getCourseTypeGroupIds(),strPublic);
        page.setAll((int) pages.getTotal());
        List<CourseTypeVo> convertDatas = new ArrayList<>();
        for (CourseType courseType : courseTypes) {
            CourseTypeVo courseTypeVo = BeanCopyUtils.objClone(courseType, CourseTypeVo::new);
            courseTypeVo.setFkTableName(TableEnum.INSTITUTION_COURSE_TYPE.key);
            String courseTypeName = courseTypeGroupCourseTypeMapper.getNamesByCourseTypeId(courseType.getId());
            courseTypeVo.setFkCourseTypeGroupName(courseTypeName);
            courseTypeVo.setCourseTypeGroupIds(courseTypeGroupService.getCourseTypeIdByCourseTypeId(courseType.getId()));
            if (GeneralTool.isNotEmpty(courseTypeVo.getPublicLevel())) {
                StringJoiner stringJoiner = new StringJoiner(" ");
                String[] result = courseTypeVo.getPublicLevel().split(",");
                for (String name : result) {
                    stringJoiner.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(name), ProjectExtraEnum.PUBLIC_OBJECTS));
                }
                courseTypeVo.setPublicLevelName(stringJoiner.toString());
            }
            convertDatas.add(courseTypeVo);
        }
        return convertDatas;
    }

    @Override
    public CourseTypeVo updateCourseType(CourseTypeDto courseTypeDto) {
        if (courseTypeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(courseTypeDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        CourseType ct = courseTypeMapper.selectById(courseTypeDto.getId());
        if (ct == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        CourseType courseType = BeanCopyUtils.objClone(courseTypeDto, CourseType::new);
        if (validateUpdate(courseTypeDto)) {
            utilService.updateUserInfoToEntity(courseType);
            courseTypeMapper.updateById(courseType);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        //更新课程类型-组别绑定
        updateCourseTypeGroupInfo(courseTypeDto.getId(), courseTypeDto.getCourseTypeGroupIds());
        return findCourseTypeById(courseType.getId());
    }

    /**
     * @Description：更新课程类型-组别绑定
     * @Param
     * @Date 19:25 2021/4/27
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    private void updateCourseTypeGroupInfo(Long courseTypeId, List<Long> courseTypeGroupIds) {
        LambdaQueryWrapper<CourseTypeGroupCourseType> wrapper = new LambdaQueryWrapper();
        wrapper.eq(CourseTypeGroupCourseType::getFkCourseTypeId, courseTypeId);
        courseTypeGroupCourseTypeMapper.delete(wrapper);
        for (Long courseTypeGroupId : courseTypeGroupIds) {
            CourseTypeGroupCourseType courseTypeGroupCourseType = new CourseTypeGroupCourseType();
            courseTypeGroupCourseType.setFkCourseTypeId(courseTypeId);
            courseTypeGroupCourseType.setFkCourseTypeGroupId(courseTypeGroupId);
            utilService.updateUserInfoToEntity(courseTypeGroupCourseType);
            courseTypeGroupCourseTypeMapper.insert(courseTypeGroupCourseType);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        //TODO 改过
        //CourseType courseType = findCourseTypeById(id);
        CourseTypeVo courseType = findCourseTypeById(id);
        if (courseType == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        boolean success = institutionCourseTypeMapper.isExistByCourseType(courseType.getId());
        boolean existSuccess = courseTypeGroupCourseTypeMapper.isExistByCourseTypeId(courseType.getId());
        if (existSuccess) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("courseType_courseTypeGroup_data_association"));
        }
        if (!success) {
            int i = courseTypeMapper.deleteById(courseType);
            if (i <= 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
            }
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("courseType_course_data_association"));
        }
        //删除翻译内容
        translationMappingService.deleteTranslations(TableEnum.INSTITUTION_COURSE_TYPE.key, id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<CourseTypeDto> courseTypeDtos) {
        for (CourseTypeDto courseTypeDto : courseTypeDtos) {
            Long courseTypeId = courseTypeDto.getId();
            if (GeneralTool.isEmpty(courseTypeDto.getId())) {
                if (validateAdd(courseTypeDto)) {
                    CourseType courseType = BeanCopyUtils.objClone(courseTypeDto, CourseType::new);
                    courseType.setViewOrder(courseTypeMapper.getMaxViewOrder());
                    utilService.updateUserInfoToEntity(courseType);
                    courseTypeMapper.insert(courseType);
                    courseTypeId = courseType.getId();
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("courseType_course_data_association"));
                }
            } else {
                if (validateUpdate(courseTypeDto)) {
                    CourseType courseType = BeanCopyUtils.objClone(courseTypeDto, CourseType::new);
                    utilService.updateUserInfoToEntity(courseType);
                    courseTypeMapper.updateById(courseType);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
                }
            }
            //更新课程类型-组别绑定
            updateCourseTypeGroupInfo(courseTypeId, courseTypeDto.getCourseTypeGroupIds());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<CourseTypeDto> courseTypeDtos) {
        if (GeneralTool.isEmpty(courseTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("institution_character_data_association"));
        }
        CourseType ro = BeanCopyUtils.objClone(courseTypeDtos.get(0), CourseType::new);
        Integer oneorder = ro.getViewOrder();
        CourseType rt = BeanCopyUtils.objClone(courseTypeDtos.get(1), CourseType::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        courseTypeMapper.updateById(ro);
        courseTypeMapper.updateById(rt);
    }

    @Override
    public List<BaseSelectEntity> getCourseTypeList() {
        return courseTypeMapper.getCourseTypeList();
    }

    @Override
    public List<BaseSelectEntity> getCourseTypeList(String keyword) {
        return courseTypeMapper.getCourseTypeListByKeyword(keyword);
    }

    @Override
    public List<BaseSelectEntity> getCourseTypeListByMode() {
        return courseTypeMapper.getCourseTypeListByMode();
    }

    @Override
    public Map<Long, String> getCourseTypeNamesByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        LambdaQueryWrapper<CourseType> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CourseType::getId, ids);
        List<CourseType> courseTypes = courseTypeMapper.selectList(wrapper);
        if (GeneralTool.isEmpty(courseTypes)) {
            return map;
        }
        for (CourseType courseType : courseTypes) {
            map.put(courseType.getId(), courseType.getTypeName());
        }
        return map;
    }

    @Override
    public String getCourseTypeNameById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return courseTypeMapper.getNameById(id);
    }


    @Override
    public Map<Long, String> getCourseTypeNamesByCourseIds(Set<Long> courseIds) {
        LambdaQueryWrapper<InstitutionCourseType> wrapper = new LambdaQueryWrapper();
        wrapper.in(InstitutionCourseType::getFkInstitutionCourseId, courseIds);
        List<InstitutionCourseType> institutionCourseTypes = institutionCourseTypeMapper.selectList(wrapper);
        Map<Long, List<Long>> map = new HashMap<>();
        for (InstitutionCourseType institutionCourseType : institutionCourseTypes) {
            map.put(institutionCourseType.getFkInstitutionCourseId(), new ArrayList<Long>());
        }
        for (InstitutionCourseType institutionCourseType : institutionCourseTypes) {
            map.get(institutionCourseType.getFkInstitutionCourseId()).add(institutionCourseType.getFkCourseTypeId());
        }

        Set<Long> typeIds = institutionCourseTypes.stream().map(InstitutionCourseType::getFkCourseTypeId).collect(Collectors.toSet());
        typeIds.removeIf(Objects::isNull);
        Map<Long, String> courseTypeNamesByIds = getCourseTypeNamesByIds(typeIds);

        Map<Long, String> resultMap = new HashMap<>();
        for (InstitutionCourseType institutionCourseType : institutionCourseTypes) {
            List<Long> types = map.get(institutionCourseType.getFkInstitutionCourseId());
            StringJoiner stringJoiner = new StringJoiner(",");
            if (GeneralTool.isNotEmpty(types)) {
                for (Long type : types) {
                    stringJoiner.add(courseTypeNamesByIds.get(type));
                }
                resultMap.put(institutionCourseType.getFkInstitutionCourseId(), stringJoiner.toString());
            } else {
                resultMap.put(institutionCourseType.getFkInstitutionCourseId(), "");
            }
        }
        return resultMap;
    }

    private boolean validateAdd(CourseTypeDto courseTypeDto) {
        LambdaQueryWrapper<CourseType> wrapper = new LambdaQueryWrapper();
        wrapper.in(CourseType::getTypeName, courseTypeDto.getTypeName());
        List<CourseType> list = this.courseTypeMapper.selectList(wrapper);
        return GeneralTool.isEmpty(list);
    }

    private boolean validateUpdate(CourseTypeDto courseTypeDto) {
        LambdaQueryWrapper<CourseType> wrapper = new LambdaQueryWrapper();
        wrapper.in(CourseType::getTypeName, courseTypeDto.getTypeName());
        List<CourseType> list = this.courseTypeMapper.selectList(wrapper);
        return list.size() <= 0 || list.get(0).getId().equals(courseTypeDto.getId());
    }
}
