package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.Convention;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/1 11:27
 * @verison: 1.0
 * @description:
 */
@Data
@ApiModel(value = "峰会返回类")
public class ConventionVo extends BaseEntity {
    /**
     * 流程数
     */
    @ApiModelProperty(value = "流程数")
    private Long procedureCount;
    /**
     * 报名数
     */
    @ApiModelProperty(value = "报名数")
    private Long registrationCount;
    /**
     * 参展人数
     */
    @ApiModelProperty(value = "参展人数")
    private Long personCount;

    /**
     * 媒体附件
     */
    @ApiModelProperty(value = "媒体附件")
    private List<MediaAndAttachedVo> mediaAndAttachedDtos;

    /**
     * 所属公司名称
     */
    @ApiModelProperty(value = "所属公司名称")
    private String companyName;

    /**
     * 活动主题名称
     */
    @ApiModelProperty(value = "活动主题名称")
    private String eventThemeName;

    @ApiModelProperty(value = "true/false:有操作权限/无操作权限")
    private Boolean hasOperatePermission;

    //============实体类Convention==================
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    /**
     * 活动Id
     */
    @UpdateWithNull
    @ApiModelProperty(value = "活动Id")
    @Column(name = "fk_event_id")
    private Long fkEventId;

    /**
     * 主题名称
     */
    @ApiModelProperty(value = "主题名称")
    @Column(name = "theme_name")
    private String themeName;
    /**
     * 峰会开始时间
     */
    @ApiModelProperty(value = "峰会开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "start_time")
    private Date startTime;
    /**
     * 峰会结束时间
     */
    @ApiModelProperty(value = "峰会结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "end_time")
    private Date endTime;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Column(name = "description")
    private String description;

    @ApiModelProperty(value = "特殊参数配置")
    @Column(name = "json")
    private String json;

    /**
     * 活动编号
     */
    @ApiModelProperty(value = "活动编号")
    @Column(name = "num")
    private String num;


    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fkCompanyId=").append(fkCompanyId);
        sb.append(", themeName=").append(themeName);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", description=").append(description);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }

}
