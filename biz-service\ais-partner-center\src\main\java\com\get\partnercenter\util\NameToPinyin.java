package com.get.partnercenter.util;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class NameToPinyin {


    // 中国常见复姓集合（约100个）
    public static final Set<String> COMPOUND_SURNAMES = new HashSet<>(Arrays.asList(
            "欧阳", "太史", "端木", "上官", "司马", "东方", "独孤", "南宫", "万俟", "闻人",
            "夏侯", "诸葛", "尉迟", "公羊", "赫连", "澹台", "皇甫", "宗政", "濮阳", "公冶",
            "太叔", "申屠", "公孙", "慕容", "仲孙", "钟离", "长孙", "宇文", "司徒", "鲜于",
            "司空", "闾丘", "子车", "亓官", "司寇", "巫马", "公西", "颛孙", "壤驷", "公良",
            "漆雕", "乐正", "宰父", "谷梁", "拓跋", "夹谷", "轩辕", "令狐", "段干", "百里",
            "呼延", "东郭", "南门", "羊舌", "微生", "公户", "公玉", "公仪", "梁丘", "公仲",
            "公上", "公门", "公山", "公坚", "左丘", "公伯", "西门", "公祖", "第五", "公乘",
            "贯丘", "公皙", "南荣", "东里", "东宫", "仲长", "子书", "子桑", "即墨", "达奚",
            "褚师", "吴铭", "纳兰", "归海", "东乡", "相里", "胡母", "司城", "张简", "丘穆",
            "叔孙", "屈突", "尔朱", "东丹", "叱利", "叱卢", "俟伏", "纥干", "可频", "阿单",
            "阿史", "沙陀", "谷浑", "素和", "斛斯", "斛律", "吐万", "綦连", "兀官", "沮渠"
    ));

    public static String[] convertChineseNameToPinyin(String chineseName) throws BadHanyuPinyinOutputFormatCombination {
        if (chineseName == null || chineseName.isEmpty()) {
            return new String[]{"", ""};
        }

        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setCaseType(HanyuPinyinCaseType.UPPERCASE);
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);

        // 检查复姓
        String surname = "";
        String givenName = "";

        if (chineseName.length() >= 2) {
            String firstTwoChars = chineseName.substring(0, 2);
            if (COMPOUND_SURNAMES.contains(firstTwoChars)) {
                surname = firstTwoChars;
                givenName = chineseName.substring(2);
            }
        }

        // 单姓处理
        if (surname.isEmpty()) {
            surname = chineseName.substring(0, 1);
            givenName = chineseName.length() > 1 ? chineseName.substring(1) : surname;
        }

        return new String[]{
                convertToPinyin(surname, format),
                convertToPinyin(givenName, format)
        };
    }

    private static String convertToPinyin(String chinese, HanyuPinyinOutputFormat format)
            throws BadHanyuPinyinOutputFormatCombination {
        StringBuilder pinyin = new StringBuilder();
        for (int i = 0; i < chinese.length(); i++) {
            char c = chinese.charAt(i);
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, format);
            if (pinyinArray != null && pinyinArray.length > 0) {
                pinyin.append(pinyinArray[0]);
            } else {
                pinyin.append(c);
            }
        }
        return pinyin.toString();
    }

    
}
