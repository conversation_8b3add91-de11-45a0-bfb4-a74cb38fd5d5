package com.get.institutioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_institution_provider_area_country")
public class InstitutionProviderAreaCountry extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 学校代理Id
     */
    @ApiModelProperty(value = "学校代理Id")
    @Column(name = "fk_institution_provider_id")
    private Long fkInstitutionProviderId;
    /**
     * 业务国家Id
     */
    @ApiModelProperty(value = "业务国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
}