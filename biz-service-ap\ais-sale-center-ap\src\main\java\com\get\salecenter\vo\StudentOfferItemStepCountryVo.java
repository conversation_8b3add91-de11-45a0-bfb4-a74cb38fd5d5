package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.StudentOfferItemStepCountry;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * @author: Hardy
 * @create: 2024/2/23 11:15
 * @verison: 1.0
 * @description:
 */
@Data
public class StudentOfferItemStepCountryVo extends BaseEntity {

    @ApiModelProperty(value = "前置条件名称")
    private String fkStudentOfferItemStepNamesPrecondition;

    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryNames;

    //=============实体类StudentOfferItemStepCountry=============
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "学生申请方案项目状态步骤Id")
    private Long fkStudentOfferItemStepId;

    @ApiModelProperty(value = "前置条件id，需要完成步骤条件")
    private String fkStudentOfferItemStepIdPrecondition;

    @ApiModelProperty(value = "适用国家Ids（多选）")
    private String fkAreaCountryIds;
}
