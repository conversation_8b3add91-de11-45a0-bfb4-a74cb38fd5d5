package com.get.salecenter.service;


import com.get.common.result.SearchBean;
import com.get.salecenter.vo.AnnualConferenceRegistrationVo;
import com.get.salecenter.vo.AnnualRegistrationVo;
import com.get.salecenter.vo.ConventionSponsorFeeVo;
import com.get.salecenter.vo.ConventionTableVo;
import com.get.salecenter.vo.EarlyBirdConfigVo;
import com.get.salecenter.dto.AgentConventionPersonSaveDto;
import com.get.salecenter.dto.AnnualConferenceRegistrationDto;
import com.get.salecenter.dto.AnnualRegistrationDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @author: Sea
 * @create: 2021/4/29 10:48
 * @verison: 1.0
 * @description:
 */
public interface IAnnualConferenceRegistrationService {

    /**
     * @return java.lang.Long
     * @Description :新增
     * @Param [annualConferenceRegistrationDto]
     * <AUTHOR>
     */
    void addAnnualConferenceRegistration(AnnualConferenceRegistrationDto annualConferenceRegistrationDto);

    /**
     * @return void
     * @Description :修改
     * @Param [annualConferenceRegistrationDto]
     * <AUTHOR>
     */
    void updateAnnualConferenceRegistration(AnnualConferenceRegistrationDto annualConferenceRegistrationDto);

    /**
     * @return com.get.salecenter.vo.AnnualConferenceRegistrationVo
     * @Description :根据回执码查找对应信息
     * @Param [conventionId, receiptCode]
     * <AUTHOR>
     */
    AnnualConferenceRegistrationVo getAnnualConferenceRegistrationDto(Long conventionId, String receiptCode);

    /**
     * @return java.util.List<com.get.salecenter.vo.SponsorshipConfigVo>
     * @Description :获取赞助列表信息以及每个赞助是否售空
     * @Param []
     * <AUTHOR>
     */
    List<Map<String, List<ConventionSponsorFeeVo>>> getSponsorshipConfig(Long conventionId);

    /**
     * @return java.lang.Boolean
     * @Description :单个赞助对象验证是否售空
     * @Param [sponsorshipConfigId, initNum]
     * <AUTHOR>
     */
    Boolean soldOut(Long sponsorshipConfigId, Integer initNum);

    /**
     * @return java.util.List<java.lang.String>
     * @Description :获取已选了的展位号
     * @Param [conventionId]
     * <AUTHOR>
     */
    List<String> getBoothIndex(Long conventionId);

    /**
     * @return java.lang.Boolean
     * @Description :该展位是否已坐
     * @Param [conventionId, boothNum]
     * <AUTHOR>
     */
    Boolean haveSit(Long conventionId, String boothNum);

    /**
     * @return java.lang.Boolean
     * @Description :
     * @Param [conventionId, providerName]
     * <AUTHOR>
     */
    Boolean providerNameVerify(Long conventionId, String providerName);

    /**
     * 表单新增报名名册
     *
     * @param annualRegistrationDto
     */
    void addRegistration(AnnualRegistrationDto annualRegistrationDto) throws Exception;

    /**
     * 表单编辑报名名册
     *
     * @param annualRegistrationDto
     */
    AnnualRegistrationVo updateRegistration(AnnualRegistrationDto annualRegistrationDto);

    /**
     * 删除参会人接口
     *
     * @param id
     */
    void deleteConventionPerson(Long id);

    /**
     * 报名名册列表
     *
     * @param annualRegistrationDto
     * @param page
     * @return
     */
    List<AnnualRegistrationVo> getAnnualRegistrationDtos(AnnualRegistrationDto annualRegistrationDto, SearchBean<AnnualRegistrationDto> page);

    /**
     * 导出报名名册Excel
     *
     * @param response
     * @param annualRegistrationDto
     */
    void exportRegistrationExcel(HttpServletResponse response, AnnualRegistrationDto annualRegistrationDto);

    /**
     * 新增参会人员
     *
     * @param agentConventionPersonSaveDto
     */
    void addAgentConventionPerson(AgentConventionPersonSaveDto agentConventionPersonSaveDto);

    /**
     * 2022GEA峰会 代理参会人员报名培训桌下拉框
     *
     * @Date 17:57 2022/11/2
     * <AUTHOR>
     */
    List<ConventionTableVo> agentConventionTableList();

    /**
     * 获取早鸟价配置
     *
     * @Date 10:00 2023/10/25
     * <AUTHOR>
     */
    EarlyBirdConfigVo getEarlyBirdConfig();

}
