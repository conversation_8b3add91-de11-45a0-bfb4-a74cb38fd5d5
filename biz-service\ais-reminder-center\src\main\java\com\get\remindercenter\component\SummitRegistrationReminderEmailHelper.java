package com.get.remindercenter.component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.get.common.consts.AESConstant;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.AESUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.remindercenter.dao.EmailSenderQueueMapper;
import com.get.remindercenter.dao.EmailTemplateMapper;
import com.get.remindercenter.dto.SummitRegistrationReminderDto;
import com.get.remindercenter.dto.WorkLeaveReminderDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.EmailTemplate;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.remindercenter.service.RemindTaskQueueService;
import com.get.remindercenter.utils.ReminderTemplateUtils;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import com.get.rocketmqcenter.feign.IRocKetMqCenterClient;
import com.get.salecenter.entity.Convention;
import com.get.salecenter.entity.ConventionRegistration;
import com.get.salecenter.entity.Student;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.salecenter.vo.ConventionRegistrationVo;
import com.get.salecenter.vo.ConventionVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component("summitRegistrationReminderEmailHelper")
public class SummitRegistrationReminderEmailHelper extends EmailAbstractHelper{

    @Resource
    private EmailTemplateMapper emailTemplateMapper;

    @Resource
    private EmailSenderQueueMapper emailSenderQueueMapper;



    @Resource
    private ISaleCenterClient saleCenterClient;

    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private RemindTaskQueueService remindTaskQueueService;

    @Override
    public void sendMail(EmailSenderQueue emailSenderQueue) {
        StringJoiner failedEmails = new StringJoiner("; "); // 新增：记录失败邮箱
        try {
            SummitRegistrationReminderDto reminderDto = assembleEmailData(emailSenderQueue);
            //设置邮件模板
            String template = setEmailTemplate(reminderDto);
            List<String> staffEmailSet = new ArrayList<>();
            StringJoiner emailsCombined = new StringJoiner(", ");
            staffEmailSet.add(reminderDto.getEmail());
            staffEmailSet.add("<EMAIL>");
            staffEmailSet.add("<EMAIL>");
            for(String email:staffEmailSet){
             try {
                 EmailSystemMQMessageDto emailSystemMQMessageDto = new EmailSystemMQMessageDto();
                 emailSystemMQMessageDto.setEmailSenderQueueId(reminderDto.getId());
                 emailSystemMQMessageDto.setTitle(reminderDto.getEmailTitle());
                 emailSystemMQMessageDto.setContent(template);
                 emailSystemMQMessageDto.setToEmail(email);
                 //emailSystemMQMessageDto.setToEmail("<EMAIL>");
                 remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
                 emailsCombined.add(email);
             }catch (Exception e){
                 // 记录发送失败的邮箱
                 String failedEmail = email;
                 failedEmails.add(failedEmail + "(" + (e.getMessage() != null ? e.getMessage() : "发送失败") + ")");
                 log.error("邮件发送失败 -  email: {}, 原因: {}",  failedEmail, e.getMessage());
             }
            }
            emailSenderQueue.setEmailTo(emailsCombined.toString());
            LambdaUpdateWrapper<EmailSenderQueue> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(EmailSenderQueue::getId, emailSenderQueue.getId())  // 更新条件（按ID匹配）
                    .set(EmailSenderQueue::getEmailTo, emailsCombined.toString());  // 只更新 emailTo 字段
            // 如果 failedEmails 不为空，则额外更新 errorMessage
            if (failedEmails.length() > 0) {
                updateWrapper.set(EmailSenderQueue::getErrorMessage, "失败邮箱: " + failedEmails.toString());
            }
            emailSenderQueueMapper.update(null, updateWrapper);  // 传入 null，由 updateWrapper 控制更新
        }catch (Exception e){
        log.error("summitRegistrationReminderEmailHelper error:{}", e);
            failedEmails.add("邮件发送异常"+e.getMessage());
            emailSenderQueue.setOperationCount(emailSenderQueue.getOperationCount() + 1);
            emailSenderQueue.setErrorMessage(failedEmails.toString());
        emailSenderQueue.setOperationStatus(-1);
        emailSenderQueueMapper.updateById(emailSenderQueue);
    }
    }

    @Override
    public SummitRegistrationReminderDto assembleEmailData(EmailSenderQueue emailSenderQueue) {
        SummitRegistrationReminderDto reminderDto = new SummitRegistrationReminderDto();
        BeanUtils.copyProperties(emailSenderQueue, reminderDto);
        List<ConventionRegistrationVo> conventionRegistrations = new ArrayList<>();
        ConventionRegistration conventionRegistration = null;
        Convention convention = null;
        Map<String, String> parsedMap = null;
        ObjectMapper mapper = new ObjectMapper();
        try {
            parsedMap = mapper.readValue(emailSenderQueue.getEmailParameter(), Map.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        //获取学生信息
        //Student student = saleCenterClient.getStudentById().getData();
        String  receiptCodeList=parsedMap.get("receiptCodeList");
        if (GeneralTool.isNotEmpty(receiptCodeList)) {
            List<String> list = Arrays.asList(receiptCodeList.split("\\s*,\\s*"));
            conventionRegistrations = saleCenterClient.getConventionRegistrationByReceiptCode(list).getData();
        }
//        Set<Long> fkConventionIds = conventionRegistrations.stream()
//                .map(ConventionRegistration::getFkConventionId)
//                .filter(Objects::nonNull) // 可选：过滤掉 null 值
//                .collect(Collectors.toSet());
        Long fkConventionId = conventionRegistrations.get(0).getFkConventionId();
        convention = saleCenterClient.getConventionRegistrationById(fkConventionId).getData();
        String versionValue2 = null;
        versionValue2= parsedMap.get("language");
        if(GeneralTool.isEmpty(versionValue2)){
            versionValue2 = "zh";
        }
        String title1 = "";
        if(!versionValue2.equals("en")){
           // title1 = "【报名成功通知】："+convention.getThemeName();
            title1 = "【报名成功通知】：2025（第11届）Hti国际教育交流峰会";
        }else {
//            title1  = "【Notice of Successful Registration】："+convention.getThemeName();
            title1  = "【Notice of Successful Registration】The 11th HTl International Education Summit";
        }
        reminderDto.setEmailTitle(title1);
        reminderDto.setLanguageCode(versionValue2);
        Map<String, String> map = new HashMap<>();
        //map.put("themeName",convention.getThemeName());
        if(!versionValue2.equals("en")){
            map.put("themeName","2025（第11届）Hti国际教育交流峰会");
        }else {
            map.put("themeName","11th HTl International Education Summit");
        }
        String link = getAppAgentFormDetailLink(receiptCodeList);
        //String link = "https://summit.ht-international.online/sale-center/sale/registrationView/confirm?code="+receiptCodeList;
        //String link = "http://************:9023/sale-center/sale/registrationView/confirm?code="+receiptCodeList;
        map.put("taskLink",link);
//       for (ConventionRegistrationVo conventionRegistrationVo:conventionRegistrations){
//           map.put("boothName",conventionRegistrationVo.getBoothName());
//           map.put("receiptCode",conventionRegistrationVo.getReceiptCode());
//           String countryStr = conventionRegistrationVo.getCountry().stream()
//                   .filter(s -> s != null && !s.trim().isEmpty())
//                   .collect(Collectors.joining(","));
//           map.put("country", countryStr);
//           reminderDto.setEmail(conventionRegistrationVo.getContactEmail());
//       }
        List<String> liList = new ArrayList<>();

        for (ConventionRegistrationVo conventionRegistrationVo : conventionRegistrations) {
            String boothName = conventionRegistrationVo.getBoothName();
            String receiptCode = conventionRegistrationVo.getReceiptCode();
            reminderDto.setEmail(conventionRegistrationVo.getContactEmail());
            if (GeneralTool.isNotEmpty(conventionRegistrationVo.getInstitutionProviderName())) {
                map.put("institutionProviderName", conventionRegistrationVo.getInstitutionProviderName());
            }else {
                map.put("institutionProviderName", conventionRegistrationVo.getProviderName());
            }

            //feign调用 根据国家key查找国家名称
            List<String> countrys = new ArrayList<>();
            if(versionValue2.equals("en")){
                if (GeneralTool.isNotEmpty(conventionRegistrationVo.getCNums())) {
                    for (String countryKey : conventionRegistrationVo.getCNums()) {
                        Result<String> result = institutionCenterClient.getCountryNameEn(countryKey);
                        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                            countrys.add(result.getData());
                        }
                    }
                }
            }else {
                if (GeneralTool.isNotEmpty(conventionRegistrationVo.getCNums())) {
                    for (String countryKey : conventionRegistrationVo.getCNums()) {
                        Result<String> result = institutionCenterClient.getCountryName(countryKey);
                        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                            countrys.add(result.getData());
                        }

                    }
                }
            }

            conventionRegistrationVo.setCountry(countrys);
            // 处理国家列表，过滤空值后拼接
            String countryStr = conventionRegistrationVo.getCountry().stream()
                    .filter(s -> s != null && !s.trim().isEmpty())
                    .collect(Collectors.joining("，"));

            // 构建单个 li 字符串
            String liItem = null;
            if(versionValue2.equals("en")){
                liItem = String.format(
                        "<li> Booth Name：%s，Countries / Regions：%s，Receipt code：%s</li>",
                        boothName, countryStr, receiptCode
                );
            }else {
                 liItem = String.format(
                        "<li>展桌名称：%s，适用国家地区：%s，参展回执码：%s</li>",
                        boothName, countryStr, receiptCode
                );
            }


            liList.add(liItem);

        }

// 拼接完整的 ul 字符串
        String ulContent = "<ul style=\"text-indent: 0;\">\n" + String.join("\n", liList) + "\n</ul>";

// 存入 map 中供后续使用（比如替换模板）
        map.put("ulContent", ulContent);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
       Date date = new Date();
       map.put("currentDate",sdf.format(date));
       reminderDto.setMap(map);
        //插入标题
        if (GeneralTool.isNotEmpty(reminderDto.getEmailTitle())) {
            emailSenderQueue.setEmailTitle(reminderDto.getEmailTitle());
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }

        return reminderDto;
    }


    private String setEmailTemplate(SummitRegistrationReminderDto reminderDto) {
        List<EmailTemplate> remindTemplates = new ArrayList<>();
        if (reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.SUMMIT_REGISTRATION_REMINDER.getEmailTemplateKey())) {
            remindTemplates = emailTemplateMapper.selectList(Wrappers.<EmailTemplate>lambdaQuery().eq(EmailTemplate::getEmailTypeKey, EmailTemplateEnum.SUMMIT_REGISTRATION_REMINDER.getEmailTemplateKey()));
        }

        if (GeneralTool.isEmpty(remindTemplates)) {
            log.error("邮箱模板不存在，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        String emailTemplate =null;
        if (!reminderDto.getLanguageCode().equals("en")) {
            emailTemplate = remindTemplates.get(0).getEmailTemplate();
        }else {
            emailTemplate = remindTemplates.get(0).getEmailTemplateEn();
        }
        emailTemplate  = ReminderTemplateUtils.getReminderTemplate(reminderDto.getMap(), emailTemplate);
        if (GeneralTool.isEmpty(emailTemplate)) {
            log.error("邮箱模板内容为空，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        emailTemplate = emailTemplate.replace("#{taskTitle}", reminderDto.getEmailTitle());
        //把emailTemplate插入到父模板里
        String parentEmailTemplate = null;
        if(GeneralTool.isNotEmpty(remindTemplates.get(0).getFkParentEmailTemplateId())&&remindTemplates.get(0).getFkParentEmailTemplateId()!=0){
            EmailTemplate parentTemplate = emailTemplateMapper.selectById(remindTemplates.get(0).getFkParentEmailTemplateId());
            Map map = new HashMap();
            map.put("subtemplate",emailTemplate);
            if (reminderDto.getLanguageCode().equals("en")) {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplateEn());
            }else {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplate());
            }

        }
        return parentEmailTemplate;
    }



    public String getAppAgentFormDetailLink(String receiptCode){
        String encrypt = null;

        String link = "";
        //根据公司获取域名
        ConfigVo configVo = permissionCenterClient.getConfigByKey(ProjectKeyEnum.SUMMIT_REGISTRATION_FORM.key).getData();
        String configValue1 = configVo.getValue1();
        try {
            encrypt =  AESUtils.EncryptURL(receiptCode, AESConstant.AESKEY);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        String taskRemark = null;
        link = configValue1+"/sale-center/sale/registrationView/confirm?code=" + encrypt;
        System.out.println("============="+link);
        return link;
    }

}
