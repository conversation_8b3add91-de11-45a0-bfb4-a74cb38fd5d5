<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.helpcenter.dao.HelpTypeMapper">

  <insert id="insertSelective" parameterType="com.get.helpcenter.entity.HelpType" keyProperty="id" useGeneratedKeys="true">
    insert into u_help_type
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkParentHelpTypeId != null">
        fk_parent_help_type_id,
      </if>
      <if test="typeName != null">
        type_name,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkParentHelpTypeId != null">
        #{fkParentHelpTypeId,jdbcType=BIGINT},
      </if>
      <if test="typeName != null">
        #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select ifnull(max(view_order)+1,1) from u_help_type
  </select>

  <select id="getAllChildHelpTypeId" resultType="java.lang.Long">
    select id from u_help_type where 1=1
    <if test="ids != null and ids.size()>0">
      AND fk_parent_help_type_id IN
      <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
        #{id,jdbcType=BIGINT}
      </foreach>
    </if>
  </select>

  <select id="getHelpTypeSelect" resultType="com.get.helpcenter.vo.HelpTypeVo">
    select * from u_help_type
  </select>
    <select id="getHelpTypeTitleById" resultType="com.get.helpcenter.entity.HelpType">
      select * from u_help_type where id = #{id}
    </select>
</mapper>