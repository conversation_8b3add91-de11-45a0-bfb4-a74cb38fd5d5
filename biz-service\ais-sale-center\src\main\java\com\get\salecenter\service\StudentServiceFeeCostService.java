package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.core.mybatis.utils.ValidList;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.vo.StudentServiceFeeCostVo;
import com.get.salecenter.vo.StudentServiceFeeCostListVo;
import com.get.salecenter.entity.StudentServiceFeeCost;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.StudentServiceFeeCostListDto;
import com.get.salecenter.dto.StudentServiceFeeCostDto;

import java.util.List;
import java.util.Optional;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-05
 */
public interface StudentServiceFeeCostService extends BaseService<StudentServiceFeeCost> {

    Long add(StudentServiceFeeCostDto studentServiceFeeCostDto);

    Optional<StudentServiceFeeCostVo> findStudentServiceFeeCostById(Long id);

    StudentServiceFeeCostVo updateStudentServiceFeeCost(StudentServiceFeeCostDto studentServiceFeeCostDto);

    void updateActive(Long id, Integer status);

    List<StudentServiceFeeCostListVo> getStudentServiceFeeCostListDtos(StudentServiceFeeCostListDto studentServiceFeeCostListDto, Page page);

    /**
     * 上传附件
     *
     * @param mediaAttachedVo 附件信息
     * @return
     */
    List<MediaAndAttachedVo> addAttachedFile(ValidList<MediaAndAttachedDto> mediaAttachedVo);
}
