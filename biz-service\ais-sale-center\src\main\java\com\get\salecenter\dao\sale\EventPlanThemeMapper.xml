<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.EventPlanThemeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.EventPlanTheme">
        <id column="id" property="id" />
        <result column="fk_event_plan_id" property="fkEventPlanId" />
        <result column="display_type" property="displayType" />
        <result column="main_title" property="mainTitle" />
        <result column="sub_title" property="subTitle" />
        <result column="description" property="description" />
        <result column="view_order" property="viewOrder" />
        <result column="is_active" property="isActive" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_create_user" property="gmtCreateUser" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="gmt_modified_user" property="gmtModifiedUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, fk_event_plan_id, display_type, main_title, sub_title, description, view_order, is_active, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
    </sql>

    <select id="getEventPlanThemes" resultType="com.get.salecenter.vo.EventPlanThemeVo">
        SELECT t.*, IFNULL(t1.registrationCount,0) + IFNULL(t2.registrationCount,0) + IFNULL(t3.registrationCount,0) AS registrationCount
        FROM m_event_plan_theme AS t
        LEFT JOIN (
             SELECT a.id,COUNT(a.registrationEventId) AS registrationCount FROM(
                    SELECT mept.id,repre.id AS registrationEventId
                    FROM m_event_plan_theme AS mept
                    INNER JOIN m_event_plan_theme_online AS mepto ON mept.id = mepto.fk_event_plan_theme_id
                    INNER JOIN r_event_plan_registration_event AS repre ON mepto.id = repre.fk_table_id
                    WHERE mept.fk_event_plan_id = #{fkEventPlanId}
                    AND repre.fk_table_name = 'm_event_plan_theme_online'
                    AND mept.is_active = 1
                    AND mepto.is_active = 1
                    GROUP BY mept.id,repre.fk_event_plan_registration_id
                ) AS a GROUP BY a.id
        ) AS t1 ON t.id = t1.id

        LEFT JOIN (
             SELECT a.id,count(a.registrationEventId) AS registrationCount FROM(
                SELECT mept.id,repre.id AS registrationEventId
                FROM m_event_plan_theme AS mept
                INNER JOIN m_event_plan_theme_workshop AS meptw ON mept.id = meptw.fk_event_plan_theme_id
                INNER JOIN r_event_plan_registration_event AS repre ON meptw.id = repre.fk_table_id
                WHERE mept.fk_event_plan_id = #{fkEventPlanId}
                AND mept.is_active = 1
                AND meptw.is_active = 1
                AND repre.fk_table_name = 'm_event_plan_theme_workshop'
                GROUP BY mept.id,repre.fk_event_plan_registration_id
             )AS a GROUP BY a.id

        )AS t2 ON t.id = t2.id

        LEFT JOIN (

             SELECT a.id,count(a.registrationEventId) AS registrationCount FROM(
                    SELECT mept.id,repre.id AS registrationEventId
                    FROM m_event_plan_theme AS mept
                    INNER JOIN m_event_plan_theme_offline AS mepto ON mept.id = mepto.fk_event_plan_theme_id
                    INNER JOIN m_event_plan_theme_offline_item AS meptoi ON mepto.id = meptoi.fk_event_plan_theme_offline_id
                    INNER JOIN r_event_plan_registration_event AS repre ON meptoi.id = repre.fk_table_id
                    WHERE mept.fk_event_plan_id = #{fkEventPlanId}
                    AND mept.is_active = 1
                    AND mepto.is_active = 1
                    AND meptoi.is_active = 1
                    AND repre.fk_table_name = 'm_event_plan_theme_offline_item'
                    GROUP BY mept.id,repre.fk_event_plan_registration_id
				  )AS a GROUP BY a.id

        )AS t3 ON t.id = t3.id
        WHERE t.fk_event_plan_id = #{fkEventPlanId}
        ORDER BY t.view_order DESC
    </select>


    <select id="getMaxViewOrder" resultType="java.lang.Integer">
      SELECT
        max(view_order)+1 view_order
      FROM
      m_event_plan_theme
      <where>
         AND fk_event_plan_id = #{fkEventPlanId}
      </where>
    </select>
</mapper>
