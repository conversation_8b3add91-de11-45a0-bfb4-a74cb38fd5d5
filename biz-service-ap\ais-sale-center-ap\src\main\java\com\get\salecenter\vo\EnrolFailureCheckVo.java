package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/25 16:42
 * @desciption:
 */
@Data
public class EnrolFailureCheckVo extends BaseEntity {

    @ApiModelProperty(value = "是否已付押金、已付学费")
    private Boolean isDepositPaidTuition = false;

    @ApiModelProperty(value = "已付押金信息")
    private List<EnrolFailureCheckItemVo> depositPaidList;

    @ApiModelProperty(value = "已付学费信息")
    private List<EnrolFailureCheckItemVo> depositTuitionList;
}
