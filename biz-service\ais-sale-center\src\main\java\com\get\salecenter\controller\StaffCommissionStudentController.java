package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.salecenter.service.IStaffCommissionStudentService;
import com.get.salecenter.dto.StaffCommissionStudentDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: Hardy
 * @create: 2023/2/7 15:47
 * @verison: 1.0
 * @description:
 */
@Api(tags = "员工提成学生关系管理")
@RestController
@RequestMapping("sale/staffCommissionStudent")
public class StaffCommissionStudentController {

    @Resource
    private IStaffCommissionStudentService staffCommissionStudentService;

    @ApiOperation(value = "学生设置激活提成", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/员工提成学生关系管理/设置学生激活提成")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated({BaseVoEntity.Add.class}) StaffCommissionStudentDto staffCommissionStudentDto) {
        return SaveResponseBo.ok(staffCommissionStudentService.addStaffCommissionStudent(staffCommissionStudentDto));
    }

    @ApiOperation(value = "批量设置学生激活提成", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/员工提成学生关系管理/批量设置学生激活提成")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated({BaseVoEntity.Add.class}) ValidList<StaffCommissionStudentDto> staffCommissionStudentDtos) {
        staffCommissionStudentService.batchAdd(staffCommissionStudentDtos);
        return SaveResponseBo.ok();
    }

    @ApiOperation(value = "删除", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/员工提成学生关系管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        staffCommissionStudentService.delete(id);
        return DeleteResponseBo.ok();
    }
}
