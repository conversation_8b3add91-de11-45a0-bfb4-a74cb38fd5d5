package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/2/17 10:42
 * @verison: 1.0
 * @description:
 */
@Data
public class CommissionStaffDatasItemVo {

    @ApiModelProperty("公司id")
    private Long fkCompanyId;

    @ApiModelProperty("公司id")
    private String fkCompanyName;

    @ApiModelProperty("员工id")
    private Long fkStaffId;

    @ApiModelProperty("员工名称")
    private String fkStaffName;

    @ApiModelProperty("学生id")
    private Long fkStudentId;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("姓")
    private String lastName;

    @ApiModelProperty("名")
    private String firstName;

    @ApiModelProperty("结案状态0申请中/1结案")
    private Integer commissionStatus;

    @ApiModelProperty("结案状态0申请中/1结案")
    private String commissionStatusName;

    @ApiModelProperty("结算状态0未结算/1已结算")
    private Integer settlementStatus;

    @ApiModelProperty("结算状态0未结算/1已结算")
    private String settlementStatusName;

    @ApiModelProperty("角色")
    private String fkStudentProjectRoleIds;

    @ApiModelProperty("角色名称")
    private String fkStudentProjectRoleName;

    @ApiModelProperty("提成步骤")
    private String fkStaffCommissionStepKey;

    @ApiModelProperty("金额")
    private BigDecimal commissionAmount;

    @ApiModelProperty("动态数据")
    private List<StaffCommissionDatasVo<CommissionStaffDetailVo>> staffCommissionDatasDtos;

}
