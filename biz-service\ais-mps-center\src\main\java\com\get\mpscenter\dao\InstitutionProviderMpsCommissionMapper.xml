<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.mpscenter.dao.InstitutionProviderMpsCommissionMapper">

    <select id="getCommissionList" resultType="com.get.mpscenter.vo.InstitutionProviderMpsCommissionVo">
        SELECT mps.*,IFNULL(mps.fk_institution_provider_mps_commission_id_from,mps.id) as fkInstitutionProviderMpsCommissionIdFromId
        FROM m_institution_provider_mps_commission mps
        LEFT JOIN r_institution_provider_mps_commission_institution i on i.fk_institution_provider_mps_commission_id = mps.id
        INNER JOIN r_institution_provider_mps_commission_company c on c.fk_institution_provider_mps_commission_id = mps.id
        where 1=1
        <if test="institutionProviderMpsCommissionDto.year != null">
            and mps.year = #{institutionProviderMpsCommissionDto.year}
        </if>
        <if test="institutionProviderMpsCommissionVo.fkInstitutionId != null">
            and (i.fk_institution_id = #{institutionProviderMpsCommissionVo.fkInstitutionId}
<!--            <if test="institutionProviderMpsCommissionVo.suitInstitutionProviderIds != null  and institutionProviderMpsCommissionVo.suitInstitutionProviderIds.size>0">-->
<!--                or (i.fk_institution_id is null and mps.fk_institution_provider_id in-->
<!--            <foreach collection="institutionProviderMpsCommissionVo.suitInstitutionProviderIds" item="sid" open="(" close=")" separator=",">-->
<!--                #{sid}-->
<!--            </foreach>)-->
<!--            </if>-->
                )
        </if>
        <if test="companyIds != null and companyIds.size>0">
            and c.fk_company_id in
            <foreach collection="companyIds" item="compnyId" open="(" close=")" separator=",">
                #{compnyId}
            </foreach>
        </if>
--         佣金列表使用
        <if test="institutionProviderMpsCommissionDto.fkInstitutionCommissionId != null">
            and i.fk_institution_id = #{institutionProviderMpsCommissionDto.fkInstitutionCommissionId}
        </if>
        <if test="institutionProviderMpsCommissionDto.fkInstitutionProviderId != null">
            and mps.fk_institution_provider_id = #{institutionProviderMpsCommissionDto.fkInstitutionProviderId}
        </if>
--         关键字搜索 m_institution_provider_mps_commission表的commission, commission_note,
        -- agent_commission, agent_commission_note, follow_commission, follow_commission_note, agent_follow_commission, agent_follow_commission_note, remark.
        <if test="institutionProviderMpsCommissionVo.keyWord != null">
            and (
                mps.commission like concat("%",#{institutionProviderMpsCommissionVo.keyWord},"%")
                or mps.commission_note like concat("%",#{institutionProviderMpsCommissionVo.keyWord},"%")
                or mps.agent_commission like concat("%",#{institutionProviderMpsCommissionVo.keyWord},"%")
                or mps.agent_commission_note like concat("%",#{institutionProviderMpsCommissionVo.keyWord},"%")
                or mps.follow_commission like concat("%",#{institutionProviderMpsCommissionVo.keyWord},"%")
                or mps.follow_commission_note like concat("%",#{institutionProviderMpsCommissionVo.keyWord},"%")
                or mps.agent_follow_commission like concat("%",#{institutionProviderMpsCommissionVo.keyWord},"%")
                or mps.agent_follow_commission_note like concat("%",#{institutionProviderMpsCommissionVo.keyWord},"%")
                or mps.remark like concat("%",#{institutionProviderMpsCommissionVo.keyWord},"%")
                or mps.title like concat("%",#{institutionProviderMpsCommissionVo.keyWord},"%")
            )
        </if>
        group by mps.id
        order by mps.is_active desc,mps.view_order,mps.title,fkInstitutionProviderMpsCommissionIdFromId,mps.id
    </select>
    <select id="getProviderInstitutionList" resultType="com.get.mpscenter.vo.InstitutionProviderCommissionVo">
        SELECT mps.*
        FROM m_institution_provider_mps_commission mps
        INNER JOIN r_institution_provider_mps_commission_institution i on i.fk_institution_provider_mps_commission_id = mps.id
        where 1=1 and mps.fk_institution_provider_id != #{fkInstitutionProviderId} and i.fkInstitutionId = #{fkInstitutionId}
        order by mps.is_active desc,mps.view_order,mps.title
    </select>
    <select id="getYearList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT DISTINCT year as mode FROM m_institution_provider_mps_commission order by year desc
    </select>
    <select id="getInstitutionProviderList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT ip.id,CONCAT(ip.`name`,IF(ip.name_chn is null or ip.name_chn = '','',CONCAT("（",ip.name_chn,"）"))) AS fullName
        FROM
        m_institution_provider_mps_commission mps
        inner join r_institution_provider_mps_commission_institution rim on rim.fk_institution_provider_mps_commission_id = mps.id
        inner join ais_institution_center.m_institution_provider ip on ip.id = mps.fk_institution_provider_id
        <where>
            <if test="fkInstitutionId != null">
                rim.fk_institution_id = #{fkInstitutionId}
            </if>
        </where>
        group by ip.id order by mps.is_active desc,mps.view_order,mps.title
    </select>
    <select id="getInstitutionList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT i.id,CONCAT(i.`name`,IF(i.name_chn is null or i.name_chn = '','',CONCAT("（",i.name_chn,"）"))) AS fullName
        FROM
        m_institution_provider_mps_commission mps
        inner join r_institution_provider_mps_commission_institution rim on rim.fk_institution_provider_mps_commission_id = mps.id
        inner join ais_institution_center.m_institution i on i.id = rim.fk_institution_id
        <where>
            <if test="fkInstitutionProviderId != null">
                mps.fk_institution_provider_id = #{fkInstitutionProviderId}
            </if>
        </where>
        group by i.id order by mps.is_active desc,mps.view_order,mps.title
    </select>


    <select id="unActiveCommissionList" resultType="com.get.mpscenter.vo.InstitutionProviderMpsCommissionVo">
        select mps.* ,GROUP_CONCAT(fk_company_id)
            from m_institution_provider_mps_commission mps
            left join r_institution_provider_mps_commission_company rc on mps.id = rc.fk_institution_provider_mps_commission_id
        WHERE
        <if test="mpsCommissionVo.fkCompanyIds != null and mpsCommissionVo.fkCompanyIds.size>0">
            rc.fk_company_id IN
                <foreach collection="mpsCommissionVo.fkCompanyIds" item="compnyId" open="(" close=")" separator=",">
                        #{compnyId}
               </foreach>
        </if>
        -- 默认显示 现在没有配置先不写
        AND mps.is_active=0
        GROUP BY mps.id
        order by mps.gmt_create desc
    </select>
</mapper>