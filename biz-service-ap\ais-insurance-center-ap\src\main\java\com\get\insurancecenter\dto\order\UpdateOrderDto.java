package com.get.insurancecenter.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/6/24
 * @Version 1.0
 * @apiNote:修改订单DTO
 */
@Data
public class UpdateOrderDto {

    @ApiModelProperty(value = "订单id")
    private Long id;

    @ApiModelProperty(value = "保险公司Id")
    private Long fkInsuranceCompanyId;

    @ApiModelProperty(value = "保险产品类型Id")
    private Long fkProductTypeId;

    @ApiModelProperty(value = "保险单号")
    private String insuranceNum;

    @ApiModelProperty(value = "保险单类型：Single/Couple/Family")
    private String insuranceType;

    @ApiModelProperty(value = "受保人姓名")
    private String insurantName;

    @ApiModelProperty(value = "受保人姓（英/拼音）")
    private String insurantLastName;

    @ApiModelProperty(value = "受保人名（英/拼音）")
    private String insurantFirstName;

    @ApiModelProperty(value = "受保人性别")
    private String insurantGender;

    @ApiModelProperty(value = "受保人国籍")
    private String insurantNationality;

    @ApiModelProperty(value = "保单开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-d", timezone = "GMT+8")
    private Date insuranceStartTime;

    @ApiModelProperty(value = "保单结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceEndTime;

    @ApiModelProperty(value = "入学时间/课程开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date enrollmentTime;

    @ApiModelProperty(value = "毕业时间/课程结算时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date graduationTime;

    @ApiModelProperty(value = "受保人生日")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insurantBirthday;

    @ApiModelProperty(value = "受保人护照号")
    private String insurantPassportNum;

    @ApiModelProperty(value = "受保人Email")
    private String insurantEmail;

    @ApiModelProperty(value = "受保人移动电话")
    private String insurantMobile;

    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "保单金额")
    private BigDecimal insuranceAmount;

}
