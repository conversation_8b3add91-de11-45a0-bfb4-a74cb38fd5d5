<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.AreaCityMapper">
  <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.AreaCity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_area_state_id" jdbcType="BIGINT" property="fkAreaStateId" />
    <result column="num" jdbcType="VARCHAR" property="num" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="name_chn" jdbcType="VARCHAR" property="nameChn" />
    <result column="population" jdbcType="VARCHAR" property="population" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="public_level" jdbcType="VARCHAR" property="publicLevel" />
    <result column="view_order" jdbcType="INTEGER" property="viewOrder" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <sql id="Base_Column_List">
    id, fk_area_state_id, num, name, name_chn, population, area, remark, public_level,
    view_order, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
  </sql>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.AreaCity" keyProperty="id" useGeneratedKeys="true">
    insert into u_area_city
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkAreaStateId != null">
        fk_area_state_id,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="nameChn != null">
        name_chn,
      </if>
      <if test="population != null">
        population,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="publicLevel != null">
        public_level,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkAreaStateId != null">
        #{fkAreaStateId,jdbcType=BIGINT},
      </if>
      <if test="num != null">
        #{num,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="nameChn != null">
        #{nameChn,jdbcType=VARCHAR},
      </if>
      <if test="population != null">
        #{population,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="publicLevel != null">
        #{publicLevel,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getCityNameById" parameterType="java.lang.Long" resultType="java.lang.String">
    select
      name
    from
      u_area_city
    where
      id = #{id}
  </select>
  <select id="getCityNameChnById" parameterType="java.lang.Long" resultType="java.lang.String">
    select
      name_chn
    from
      u_area_city
    where
      id = #{id}
  </select>
  <select id="getCityFullNameById" parameterType="java.lang.Long" resultType="java.lang.String">
    select
      CASE WHEN IFNULL(name_chn,'')='' THEN `name` ELSE CONCAT(`name`,'（',name_chn,'）') END fullName
    from
      u_area_city
    where
      id = #{id}
  </select>

  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select
     IFNULL(max(view_order)+1,0) view_order
    from
     u_area_city

  </select>
  <select id="getAreaCitys" parameterType="com.get.institutioncenter.dto.AreaCityDto" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
        from u_area_city
    <where>

      <if test="areaCityDto.fkAreaStateId!=null and areaCityDto.fkAreaStateId !=''">
        and fk_area_state_id = #{areaCityDto.fkAreaStateId}
      </if>
      <if test="areaCityDto.keyWord!=null and areaCityDto.keyWord !=''">
        and (num like concat("%",#{areaCityDto.keyWord},"%") or name like concat("%",#{areaCityDto.keyWord},"%") or name_chn like
        concat("%",#{areaCityDto.keyWord},"%"))
      </if>
    </where>
      order by fk_area_state_id, IFNULL(view_order,0) DESC, CONVERT(name USING gbk)
  </select>
  <select id="selectAreaCityByVo" resultType="com.get.institutioncenter.entity.AreaCity">
    SELECT
      id,
      fk_area_state_id,
      num,
      NAME,
      name_chn,
      remark,
      view_order,
      gmt_create,
      gmt_create_user,
      gmt_modified,
      gmt_modified_user
    FROM
      u_area_city
    ORDER BY
      fk_area_state_id,
      IFNULL( view_order, 0 ) DESC,
      CONVERT (
              NAME USING gbk)

  </select>
    <select id="getCityChnNameById" resultType="java.lang.String">
      select c.name_chn
      from
        u_area_city c
      where
        c.id = #{id}
    </select>
    <select id="getExistsAgentOfferItemAreaStateCityList"
            resultType="com.get.core.mybatis.base.BaseSelectEntity">
      SELECT
        uac.id,
        uac.num,
        uac.NAME,
        uac.name_chn,
        CASE

          WHEN IFNULL( uac.name_chn, '' )= '' THEN
            uac.NAME ELSE CONCAT( uac.NAME, '（', uac.name_chn, '）' )
          END fullName
      FROM
        u_area_city AS uac
          INNER JOIN (
          SELECT
            a.fk_area_city_id
          FROM
            ais_sale_center.m_student_offer_item AS msoi
              INNER JOIN ais_sale_center.m_student AS ms ON ms.id = msoi.fk_student_id
              INNER JOIN ais_sale_center.m_agent AS a ON  a.id = msoi.fk_agent_id
              LEFT JOIN ais_sale_center.r_staff_bd_code AS rsbc ON rsbc.fk_staff_id = msoi.fk_staff_id
          WHERE
            ms.fk_company_id = #{companyId} AND msoi.STATUS = 1 AND rsbc.bd_code NOT LIKE 'T%'
          AND msoi.fk_area_country_id IN
          <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">
            #{countryId}
          </foreach>
          GROUP BY
            a.fk_area_city_id ) a ON a.fk_area_city_id = uac.id
					where uac.fk_area_state_id IN (${stateIds})
      ORDER BY
        view_order DESC
    </select>
    <select id="getExistsAgentAreaStateCityList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    SELECT
        uac.id,
        uac.num,
        uac.NAME,
        uac.name_chn,
        CASE

          WHEN IFNULL( uac.name_chn, '' )= '' THEN
            uac.NAME ELSE CONCAT( uac.NAME, '（', uac.name_chn, '）' )
          END fullName
      FROM
        u_area_city AS uac
          INNER JOIN ais_sale_center.m_agent AS ma ON ma.fk_area_city_id = uac.id
					where uac.fk_area_state_id IN (${stateIds})
	GROUP BY uac.id
      ORDER BY
        view_order DESC
    </select>
</mapper>