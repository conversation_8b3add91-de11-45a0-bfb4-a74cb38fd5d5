package com.get.officecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.officecenter.vo.CustomTaskVo;
import com.get.officecenter.entity.Comment;
import com.get.officecenter.service.CustomTaskService;
import com.get.officecenter.dto.CustomTaskSearchDto;
import com.get.officecenter.dto.CustomTaskDto;
import com.get.permissioncenter.vo.DepartmentAndStaffVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Author:cream
 * @Date: 2023/5/30  14:22
 */
@Api(tags = "系统任务管理")
@RestController
@RequestMapping("office/customTask")
public class CustomTaskController {

    @Resource
    private CustomTaskService customTaskService;

    @ApiOperation(value = "新增任务")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.ADD, description = "办公中心/系统任务/新增任务")
    @PostMapping("/addTask")
    public SaveResponseBo addTask(@Validated({CustomTaskDto.Add.class}) @RequestBody CustomTaskDto customTaskDto){
        return customTaskService.addTask(customTaskDto);
    }

    @ApiOperation(value = "任务详情")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DETAIL, description = "办公中心/系统任务/任务详情")
    @GetMapping("/getTaskInfoById")
    public ResponseBo<CustomTaskVo> getTaskInfoById(@RequestParam("taskId") Long taskId){
        return customTaskService.getTaskInfoById(taskId);
    }

    @ApiOperation(value = "任务列表")
//    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/系统任务/任务列表")
    @PostMapping("/taskList")
    public ListResponseBo<CustomTaskVo> taskList(@RequestBody SearchBean<CustomTaskSearchDto> searchBean){
        return customTaskService.taskList(searchBean.getData(),searchBean);
    }



    @ApiOperation(value = "任务列表导出")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/系统任务/任务列表导出")
    @PostMapping("/exportTaskList")
    public void exportTaskList(HttpServletResponse response, @RequestBody SearchBean<CustomTaskSearchDto> searchBean){
         customTaskService.exportTaskList(response,searchBean.getData());
    }





    /**
     * 获取部门下拉
     * @param type
     * @return
     */
    @ApiOperation(value = "任务部门下拉  0 :获取接收人部门  1：委派人部门")
    @GetMapping("/getTaskDepartment")
    public ListResponseBo<DepartmentAndStaffVo> getTaskDepartment(@RequestParam("type") Integer type){
        return customTaskService.getTaskDepartment(type);
    }




    @ApiOperation(value = "更新任务内容")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.EDIT, description = "办公中心/系统任务/更新任务内容")
    @PostMapping("/updateTask")
    public SaveResponseBo updateTask(@Validated({BaseVoEntity.Update.class}) @RequestBody CustomTaskDto customTaskDto){
        return customTaskService.updateTask(customTaskDto);
    }

    @ApiOperation(value = "新增任务的评论")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.ADD, description = "办公中心/系统任务/新增任务的评论")
    @PostMapping("/addTaskComment")
    public SaveResponseBo addTaskComment(@RequestParam("taskId") Long taskId,@RequestParam("comment") String comment){
        return customTaskService.addTaskComment(taskId,comment);
    }

    @ApiOperation(value = "获取评论列表")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.ADD, description = "办公中心/系统任务/获取评论列表")
    @PostMapping("/getTaskCommentList")
    public ListResponseBo<Comment> getTaskCommentList(@RequestBody SearchBean<CustomTaskDto> searchBean){
        return customTaskService.getTaskCommentList(searchBean.getData().getTaskId(),searchBean);
    }

    @ApiOperation(value = "删除任务评论")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.ADD, description = "办公中心/系统任务/删除任务评论")
    @PostMapping("/removeTaskComment")
    public DeleteResponseBo removeTaskComment(Long commentId) {
        return customTaskService.removeTaskComment(commentId);
    }

    @ApiOperation(value = "更新任务的评论")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.EDIT, description = "办公中心/系统任务/更新任务的评论")
    @PostMapping("/updateTaskComment")
    public SaveResponseBo updateTaskComment(@RequestParam("taskId")Long taskId,@RequestParam("commentId")Long commentId,@RequestParam("comment")String comment){
        return customTaskService.updateTaskComment(taskId,commentId,comment);
    }

    @ApiOperation(value = "任务委派")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.EDIT, description = "办公中心/系统任务/任务委派")
    @PostMapping("/taskDelegation")
    public SaveResponseBo taskDelegation(@Validated({BaseVoEntity.Update.class}) @RequestBody CustomTaskDto customTaskDto){
        return customTaskService.taskDelegation(customTaskDto);
    }

    @ApiOperation(value = "任务设置完成")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.EDIT, description = "办公中心/系统任务/任务设置完成")
    @PostMapping("/taskCompletion")
    public SaveResponseBo taskCompletion(@RequestParam("taskId") Long taskId){
        return customTaskService.taskCompletion(taskId);
    }

    @ApiOperation(value = "删除任务")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DELETE, description = "办公中心/系统任务/删除任务")
    @PostMapping("/taskDelete")
    public DeleteResponseBo taskDelete(@RequestParam("taskId") Long taskId){
        return customTaskService.taskDelete(taskId);
    }
}
