package com.get.financecenter.vo;

import com.get.financecenter.entity.PaymentApplicationForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/3/30 15:44
 */
@Data
public class PaymentApplicationFormVo extends PaymentApplicationForm {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "事物或理由，金额等")
    private List<PaymentApplicationFormItemVo> paymentApplicationFormItemDtos = null;
    @ApiModelProperty(value = "所属部门")
    private String DepartmentName;
    @ApiModelProperty("公司简称")
    private String shortName;
    /**
     * 报销人名称
     */
    @ApiModelProperty(value = "报销人名称")
    private String staffName;

    @ApiModelProperty("部署id")
    private String deployId;
    @ApiModelProperty("任务版本号")
    private Integer taskVersion;
    @ApiModelProperty("任务id")
    private String taskId;
    @ApiModelProperty("待签或代表")
    private int signOrGetStatus;
    @ApiModelProperty("流程实例id")
    private String procInstId;
    @ApiModelProperty("币种中英")
    private String fkCurrencyTypeNumName;
    @ApiModelProperty("同表父id")
    private Long fkTableParentId;
    /**
     * 同意按钮状态
     */
    @ApiModelProperty(value = "同意按钮状态")
    private Boolean agreeButtonType;

    /**
     * 拒绝按钮状态
     */
    @ApiModelProperty(value = "拒绝按钮状态")
    private Boolean refuseButtonType;

    @ApiModelProperty(value = "金额总和")
    private BigDecimal amountSum;

    @ApiModelProperty(value = "创建凭证人名字")
    private String fkStaffIdVouchCreatedName;

    @ApiModelProperty(value = "凭证Id")
    private Long fkVouchId;

}
