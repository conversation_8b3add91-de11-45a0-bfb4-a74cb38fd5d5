package com.get.examcenter.dto.query;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class ExaminationQuestionQueryDto extends BaseVoEntity {

    /**
     * 考卷Id
     */
    @ApiModelProperty(value = "考卷Id")
    private Long fkExaminationPaperId;

    /**
     * 题目类型Id
     */
    @ApiModelProperty(value = "题目类型Id")
//@NotNull(message = "题目类型Id不能为空", groups = {Add.class, Update.class})
    private Long fkQuestionTypeId;

    /**
     * 问题内容
     */
    @ApiModelProperty(value = "问题内容")
    @NotBlank(message = "问题内容不能为空")
    private String question;

    /**
     * 是否允许重考：0否/1是
     */
    @ApiModelProperty(value = "是否允许重考：0否/1是")
    @NotNull(message = "是否允许重考不能为空")
    private Boolean isRetest;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

}
