package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@TableName("r_user_contact_person")
public class UserContactPerson extends BaseEntity implements Serializable  {
    /**
     * 注册中心的user id
     */
    @ApiModelProperty(value = "注册中心的user id")
    @Column(name = "fk_user_id")
    private Long fkUserId;

    /**
     * 联系人Id（BMS）
     */
    @ApiModelProperty(value = "联系人Id（BMS）")
    @Column(name = "fk_contact_person_id")
    private Long fkContactPersonId;

    private static final long serialVersionUID = 1L;
}