package com.get.permissioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.permissioncenter.dto.CommentDto;
import com.get.permissioncenter.dto.MediaAndAttachedDto;
import com.get.permissioncenter.vo.CommentVo;
import com.get.permissioncenter.vo.MediaAndAttachedVo;
import com.get.permissioncenter.vo.StaffContractVo;
import com.get.permissioncenter.service.IStaffContractService;
import com.get.permissioncenter.dto.StaffContractDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/7
 * @TIME: 16:26
 * @Description: 合同管理
 **/
@Api(tags = "合同管理")
@RestController
@RequestMapping("permission/contract")
public class StaffContractController {
    @Resource
    private IStaffContractService contractService;

    /**
     * 合同列表
     *
     * @param voSearchBean
     * @return
     */
    @ApiOperation(value = "员工合同列表")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "权限中心/合同管理/合同列表")
    @PostMapping("datas")
    public ResponseBo<StaffContractVo> datas(@RequestBody SearchBean<StaffContractDto> voSearchBean) {
        List<StaffContractVo> staffContractVos = contractService.getStaffContract(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffContractVos, page);
    }


    /**
     * 合同详情
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "合同详情", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "权限中心/合同管理/合同详情")
    @GetMapping("/{id}")
    public ResponseBo<StaffContractVo> detail(@PathVariable("id") Long id) {
        StaffContractVo staffContractVo = contractService.getStaffContractById(id);
        return new ResponseBo<>(staffContractVo);
    }


    /**
     * 新增信息
     *
     * @param staffContractDto
     * @return
     */
    @ApiOperation(value = "新增接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/合同管理/新增参数")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(StaffContractDto.Add.class) StaffContractDto staffContractDto) {
        return SaveResponseBo.ok(contractService.addStaffContract(staffContractDto));
    }

    /**
     * 修改信息
     *
     * @param staffContractDto
     * @return
     */
    @ApiOperation(value = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/合同管理/更新参数")
    @PostMapping("update")
    public ResponseBo<StaffContractVo> update(@RequestBody @Validated(StaffContractDto.Update.class) StaffContractDto staffContractDto) {
        return UpdateResponseBo.ok(contractService.updateStaffContractVo(staffContractDto));
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.DELETE, description = "权限中心/合同管理/删除资源")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        contractService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.permissioncenter.vo.MediaAndAttachedDto>
     * @Description: 保存合同附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "保存合同附件")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/合同管理/保存合同")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> upload(@RequestBody @Validated(MediaAndAttachedDto.Add.class) ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(contractService.addContractMedia(mediaAttachedVo));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.permissioncenter.vo.MediaAndAttachedDto>
     * @Description: 查询合同附件
     * @Param [voSearchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "查询合同附件", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/员工管理/查询员工附件")
    @PostMapping("getContractMedia")
    public ResponseBo<MediaAndAttachedVo> getContractMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> staffMedia = contractService.getContractMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 编辑评论
     * @Param [commentDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑评论")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/合同管理/编辑评论")
    @PostMapping("editComment")
    public ResponseBo editComment(@RequestBody @Validated(CommentDto.Add.class) CommentDto commentDto) {
        return SaveResponseBo.ok(contractService.editComment(commentDto));
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.permissioncenter.vo.CommentVo>
     * @Description: 评论列表数据
     * @Param [searchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "评论列表数据")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/合同管理/评论查询")
    @PostMapping("getComments")
    public ResponseBo<CommentVo> getComment(@RequestBody SearchBean<CommentDto> searchBean) {
        List<CommentVo> datas = contractService.getComments(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(datas, p);
    }

}
