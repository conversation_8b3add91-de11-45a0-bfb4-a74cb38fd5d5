package com.get.institutioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_contract_formula_pre_major_level")
public class ContractFormulaPreMajorLevel extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 合同公式Id
     */
    @ApiModelProperty(value = "合同公式Id")
    @Column(name = "fk_contract_formula_id")
    private Long fkContractFormulaId;
    /**
     * 专业等级Id(前置等级)
     */
    @ApiModelProperty(value = "专业等级Id(前置等级)")
    @Column(name = "fk_major_level_id")
    private Long fkMajorLevelId;
}