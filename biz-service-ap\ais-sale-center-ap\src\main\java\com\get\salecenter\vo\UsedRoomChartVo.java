package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: <PERSON>
 * @create: 2021/5/20 10:14
 * @verison: 1.0
 * @description: 被使用的房间统计图
 */
@Data
public class UsedRoomChartVo {
    /**
     * 每一条柱状图的名字
     */
    @ApiModelProperty(value = "每一条柱状图的名字")
    private String name;

    /**
     * 统计集合
     */
    @ApiModelProperty(value = "统计集合")
    private List<Integer> countList;
}
