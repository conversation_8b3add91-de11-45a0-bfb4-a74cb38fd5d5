package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2021/10/11
 * @TIME: 22:47
 * @Description:
 **/
@Data
public class TicketsNoUsedVo implements Serializable {
    /**
     * 抽奖号码（预生成，可自定义前序字符）
     */
    @ApiModelProperty(value = "抽奖号码（预生成，可自定义前序字符）")
    private String awardCode;
    @ApiModelProperty("id，主键")
    private Long id;
}
