package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *  报名名册联系人
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
@Data
@TableName("r_event_plan_registration_contact_person")
@ApiModel(value="EventPlanRegistrationContactPerson对象", description="")
public class EventPlanRegistrationContactPerson extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动年度计划报名名册Id")
    private Long fkEventPlanRegistrationId;

    @ApiModelProperty(value = "联系人名称")
    private String name;

    @ApiModelProperty(value = "联系人邮箱")
    private String email;


}
