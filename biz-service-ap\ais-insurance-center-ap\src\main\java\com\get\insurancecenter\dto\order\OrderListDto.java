package com.get.insurancecenter.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author:Oliver
 * @Date: 2025/5/23
 * @Version 1.0
 * @apiNote:
 */
@Data
public class OrderListDto {

    @ApiModelProperty(value = "保险公司Id")
    private Long insuranceCompanyId;

    @ApiModelProperty(value = "保险单类型")
    private String insuranceType;

    @ApiModelProperty(value = "订单状态:2-已完成;1-待下单;-2-下单失败")
    private Integer orderStatus;

    @ApiModelProperty(value = "保险单号")
    private String insuranceNum;

    @ApiModelProperty(value = "系统订单号")
    private String orderNum;

    @ApiModelProperty(value = "姓名")
    private String insurantName;

}
