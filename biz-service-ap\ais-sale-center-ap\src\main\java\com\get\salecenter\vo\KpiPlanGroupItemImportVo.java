package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * KPI数据导入实体类
 */
@Data
public class KpiPlanGroupItemImportVo implements Serializable {

    @ApiModelProperty(value = "Group")
    private String fkKpiPlanGroupName;

    @ApiModelProperty(value = "Institution")
    private String institution;

    @ApiModelProperty(value = "Description")
    private String description;

    @ApiModelProperty(value = "Country")
    private String country;

    @ApiModelProperty(value = "Target")
    private Integer targetEnrolled;

}
