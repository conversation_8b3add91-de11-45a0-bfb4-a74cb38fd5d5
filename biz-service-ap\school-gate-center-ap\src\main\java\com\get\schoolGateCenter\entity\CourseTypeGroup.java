package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.type.Alias;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("u_course_type_group")
public class CourseTypeGroup extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 类型组别名称
     */
    @ApiModelProperty(value = "类型组别名称")
    @Column(name = "type_group_name")
    private String typeGroupName;
    /**
     * 类型组别名称
     */
    @ApiModelProperty(value = "类型组别名称中文")
    @Column(name = "type_group_name_chn")
    private String typeGroupNameChn;
    /**
     * 分类模式
     */
    @ApiModelProperty(value = "分类模式")
    @Column(name = "mode")
    private Integer mode;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;

    @ApiModelProperty(value = "公开对象")
    @Column(name = "public_level")
    private String publicLevel;

}