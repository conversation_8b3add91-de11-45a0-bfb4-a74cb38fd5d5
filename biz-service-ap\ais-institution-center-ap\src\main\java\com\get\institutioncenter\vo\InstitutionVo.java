package com.get.institutioncenter.vo;

import com.get.common.annotion.TableDto;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2020/8/4
 * @TIME: 18:20
 * @Description:学校dto
 **/
@ApiModel("学校返回类")
@Data
public class InstitutionVo extends BaseEntity {
//学校权限组 使用字段
    @ApiModelProperty(value = "学校权限组Id")
    private Long fkInstitutionPermissionGroupInstitutionId;
//学校权限组 使用字段-end
    /**
     * 国家Name
     */
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;
    /**
     * 州省Name
     */
    @ApiModelProperty(value = "州省名称")
    private String fkAreaStateName;

    /**
     * 城市Name
     */
    @ApiModelProperty(value = "城市名称")
    private String fkAreaCityName;
    /**
     * 类型Name
     */
    @ApiModelProperty(value = "学校类型名称")
    @TableDto(tableName = "u_institution_type", columnDto = "type_name", entityColumnDto = "fkInstitutionTypeName", columnDtoMainId = "fk_institution_type_id")
    private String fkInstitutionTypeName;
    /**
     * 类型Key
     */
    @ApiModelProperty(value = "学校类型key")
    @TableDto(tableName = "u_institution_type", columnDto = "type_key", entityColumnDto = "fkInstitutionTypeKey", columnDtoMainId = "fk_institution_type_id")
    private String fkInstitutionTypeKey;
    /**
     * 课程数
     */
    @ApiModelProperty(value = "课程数")
    private Integer courseCount;

    /**
     * 学校图片
     */
    @ApiModelProperty(value = "学校图片")
    private MediaAndAttachedVo mediaAndAttachedDto;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;
    /**
     * 全称
     */
    @ApiModelProperty(value = "全称")
    private String fullName;
    /**
     * 货币名称
     */
    @ApiModelProperty(value = "货币名称")
    private String currencyName;
    /**
     * 公开对象名称
     */
    @ApiModelProperty(value = "公开对象名称")
    private String publicLevelName;

    /**
     * 数据等级名称
     */
    @ApiModelProperty(value = "数据等级名称")
    private String dataLevelName;
    /**
     * 学校提供商和学校中间表id
     */
    @ApiModelProperty(value = "学校提供商和学校中间表id")
    private String fkId;


    /**
     * 重点推荐的学校等级：1小推荐/2中推荐/3大推荐
     */
    @ApiModelProperty(value = "重点推荐的学校等级：1小推荐/2中推荐/3大推荐")
    private String kpiLevelName;
    /**
     * 校区统计
     */
    @ApiModelProperty(value = "校区统计")
    private Integer zoneNum;
    /**
     * 院校统计
     */
    @ApiModelProperty(value = "院校统计")
    private Integer facultyNum;
    /**
     * 课程统计
     */
    @ApiModelProperty(value = "课程统计")
    private Integer courseNum;
    /**
     * 常见问题统计
     */
    @ApiModelProperty(value = "常见问题统计")
    private Integer faqNum;
    /**
     * 知名校友统计
     */
    @ApiModelProperty(value = "知名校友统计")
    private Integer alumnusNum;
    /**
     * 资讯统计
     */
    @ApiModelProperty(value = "资讯统计")
    private Integer infoNum;

    /**
     * 奖学金统计
     */
    @ApiModelProperty(value = "奖学金统计")
    private Integer scholarshipNum;
    /**
     * 申请费用统计
     */
    @ApiModelProperty(value = "申请费用统计")
    private Integer appFeeNum;
    /**
     * 申请截止统计
     */
    @ApiModelProperty(value = "申请截止统计")
    private Integer deadlineInfoNum;
    /**
     * 是否合作
     */
    @ApiModelProperty(value = "合作状态：0无/1有")
    private Boolean isBindingActive;

    /**
     * 是否激活提成
     */
    @ApiModelProperty(value = "是否激活提成：false否/true是")
    private Boolean isActiveCommission = false;

    @ApiModelProperty(value = "关系所属公司Id，多选，英文逗号分隔")
    private String fkCompanyIds;

    @ApiModelProperty(value = "学校提供商和学校中间表公司名")
    private String providerInstitutionCompanyName;

    //===============实体类Institution======================
    private static final long serialVersionUID = 1L;
    /**
     * 学校类型Id
     */
    @ApiModelProperty(value = "学校类型Id")
    @Column(name = "fk_institution_type_id")
    private Long fkInstitutionTypeId;
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 显示名称
     */
    @ApiModelProperty(value = "显示名称")
    @Column(name = "name_display")
    private String nameDisplay;
    /**
     * 州省Id
     */
    @UpdateWithNull
    @ApiModelProperty(value = "州省Id")
    @Column(name = "fk_area_state_id")
    private Long fkAreaStateId;
    /**
     * 城市Id
     */
    @UpdateWithNull
    @ApiModelProperty(value = "城市Id")
    @Column(name = "fk_area_city_id")
    private Long fkAreaCityId;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;
    /**
     * 学校编号
     */
    @ApiModelProperty(value = "学校编号")
    @Column(name = "num")
    private String num;
    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    @Column(name = "name")
    private String name;
    /**
     * 学校中文名称
     */
    @ApiModelProperty(value = "学校中文名称")
    @Column(name = "name_chn")
    private String nameChn;
    /**
     * 学校简称
     */
    @ApiModelProperty(value = "学校简称")
    @Column(name = "short_name")
    private String shortName;
    /**
     * 学校中文简称
     */
    @ApiModelProperty(value = "学校中文简称")
    @Column(name = "short_name_chn")
    private String shortNameChn;
    /**
     * 学校性质：公立/私立
     */
    @ApiModelProperty(value = "学校性质：公立/私立")
    @Column(name = "nature")
    private String nature;
    /**
     * 成立时间
     */
    @ApiModelProperty(value = "成立时间")
    @Column(name = "established_date")
    private String establishedDate;
    /**
     * 入学申请时间
     */
    @ApiModelProperty(value = "入学申请时间")
    @Column(name = "apply_date")
    private String applyDate;
    /**
     * 学费下限
     */
    @ApiModelProperty(value = "学费下限")
    @Column(name = "apply_fee_min")
    private BigDecimal applyFeeMin;
    /**
     * 学费上限
     */
    @ApiModelProperty(value = "学费上限")
    @Column(name = "apply_fee_max")
    private BigDecimal applyFeeMax;
    /**
     * 学费参考（显示）
     */
    @ApiModelProperty(value = "学费参考（显示）")
    @Column(name = "apply_fee_ref")
    private BigDecimal applyFeeRef;
    /**
     * 学费（统一为人民币，主要是学费筛选使用）
     */
    @ApiModelProperty(value = "学费（统一为人民币，主要是学费筛选使用）")
    @Column(name = "apply_fee_cny")
    private BigDecimal applyFeeCny;
    /**
     * 是否存在官网：0否/1是
     */
    @ApiModelProperty(value = "是否存在官网：0否/1是")
    @Column(name = "is_exist_website")
    private Boolean isExistWebsite;
    /**
     * 官网
     */
    @ApiModelProperty(value = "官网")
    @Column(name = "website")
    private String website;
    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    @Column(name = "zip_code")
    private String zipCode;
    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    @Column(name = "address")
    private String address;
    /**
     * 重点推荐的学校：0否/1是
     */
    @ApiModelProperty(value = "重点推荐的学校：0否/1是")
    @Column(name = "is_kpi")
    private Boolean isKpi;
    /**
     * 重点推荐的学校等级：1小推荐/2中推荐/3大推荐
     */
    @ApiModelProperty(value = "重点推荐的学校等级：1小推荐/2中推荐/3大推荐")
    @Column(name = "kpi_level")
    private Integer kpiLevel;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @Column(name = "public_level")
    private String publicLevel;
    /**
     * 数据等级：0旧数据导入/1官方数据导入/2需要补充/3采集完整
     */
    @ApiModelProperty(value = "数据等级：0旧数据导入/1官方数据导入/2需要补充/3采集完整")
    @Column(name = "data_level")
    private Integer dataLevel;
    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    @Column(name = "id_gea")
    private String idGea;
    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    @Column(name = "id_iae")
    private String idIae;
    /**
     * 详细描述
     */
    @ApiModelProperty(value = "详细描述")
    @Column(name = "detail")
    private String detail;
    /**
     * 地图坐标（google）
     */
    @ApiModelProperty(value = "地图坐标（google）")
    @Column(name = "map_xy_gg")
    private String mapXyGg;
    /**
     * 地图坐标（baidu）
     */
    @ApiModelProperty(value = "地图坐标（baidu）")
    @Column(name = "map_xy_bd")
    private String mapXyBd;

    @ApiModelProperty(value = "学校类型")
    @Column(name = "ranking_type")
    private String rankingType;

    @ApiModelProperty(value = "k12类型枚举，多选逗号分隔：0=幼儿园/1=小学/2=初中/3=高中")
    private String k12Type;

    @ApiModelProperty(value = "是否教会学校：0否/1是")
    private int isChurch;

    @ApiModelProperty(value = "提供给学生住宿方式枚举，多选逗号分隔：1=走读/2=寄宿/3=混合（即偶尔住校）")
    private String accommodationType;

    @ApiModelProperty(value = "k12类型枚举，多选逗号分隔：0=幼儿园/1=小学/2=初中/3=高中")
    private String k12TypeName;

    @ApiModelProperty(value = "提供给学生住宿方式枚举，多选逗号分隔：1=走读/2=寄宿/3=混合（即偶尔住校）")
    private String accommodationTypeName;
}
