package com.get.common.utils;

import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * HTI鉴权工具类
 *
 * <AUTHOR>
 * @date 2023/12/14 11:33
 */
public class HtiAuthenticationUtils {

    /**
     * HTI验证
     *
     * @Date 11:37 2023/12/14
     * <AUTHOR>
     */
    public static boolean validateHti(long currentTimeMillis, String secretKeyHti) {
        long timeDifference = Math.abs(System.currentTimeMillis() - currentTimeMillis);
        // 检查时间差是否小于等于10分钟（以毫秒为单位）
        boolean isWithin10Minutes = timeDifference <= 10 * 60 * 1000;
        if (!isWithin10Minutes) {
            return false;
        }
        //String password = fkPlatformType + currentTimeMillis+appId+appSecret;
        String password = "HTI" + currentTimeMillis + "HTI_2023#@!" + "41889f86-66d2-429e-b665-5bdd6c7dc68b";
        String secretKey = DigestUtils.md5DigestAsHex(password.getBytes());
        if (!secretKey.equals(secretKeyHti)) {
            return false;
        } else {
            return true;
        }
    }

    public static String encryptSHA256(String input) {
        try {
            // 创建 MessageDigest 对象，并指定使用 SHA-256 算法
            MessageDigest digest = MessageDigest.getInstance("SHA-256");

            // 将输入字符串转换为字节数组
            byte[] encodedHash = digest.digest(input.getBytes(StandardCharsets.UTF_8));

            // 将字节数组转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : encodedHash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            // 处理算法不存在异常
            e.printStackTrace();
        }

        return null;
    }


}
