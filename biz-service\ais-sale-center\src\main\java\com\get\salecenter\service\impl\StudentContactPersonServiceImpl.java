package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.StudentContactPersonMapper;
import com.get.salecenter.vo.StudentContactPersonVo;
import com.get.salecenter.entity.StudentContactPerson;
import com.get.salecenter.service.IStudentContactPersonService;
import com.get.salecenter.utils.VerifyDataPermissionsUtils;
import com.get.salecenter.dto.StudentContactPersonDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/11/11
 * @TIME: 12:41
 * @Description:
 **/
@Service
public class StudentContactPersonServiceImpl implements IStudentContactPersonService {
    @Resource
    private UtilService utilService;
    @Resource
    private StudentContactPersonMapper studentContactPersonMapper;

    @Resource
    private VerifyDataPermissionsUtils verifyDataPermissionsUtils;

    @Override
    public StudentContactPersonVo findContactPersonById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StudentContactPerson studentContactPerson = studentContactPersonMapper.selectById(id);
        return BeanCopyUtils.objClone(studentContactPerson, StudentContactPersonVo::new);
    }

    @Override
    public List<StudentContactPersonVo> getContactPersons(StudentContactPersonDto contactPersonVo, Page page) {
        if (GeneralTool.isEmpty(contactPersonVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(contactPersonVo.getFkStudentId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("student_id_null"));
        }
        verifyDataPermissionsUtils.verifyByBusinessId(contactPersonVo.getFkStudentId(),VerifyDataPermissionsUtils.STUDENT_O);

//        Example example = new Example(StudentContactPerson.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<StudentContactPerson> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StudentContactPerson::getFkStudentId, contactPersonVo.getFkStudentId());
//        criteria.andEqualTo("fkStudentId", contactPersonVo.getFkStudentId());
        if (GeneralTool.isNotEmpty(contactPersonVo.getName())) {
//            criteria.andLike("name", "%" + contactPersonVo.getName() + "%");
            lambdaQueryWrapper.like(StudentContactPerson::getName, contactPersonVo.getName());
        }
        if (GeneralTool.isNotEmpty(contactPersonVo.getRelationship())) {
//            criteria.andEqualTo("relationship", contactPersonVo.getRelationship());
            lambdaQueryWrapper.eq(StudentContactPerson::getRelationship, contactPersonVo.getRelationship());
        }
        lambdaQueryWrapper.orderByDesc(StudentContactPerson::getGmtCreate);
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        List<StudentContactPerson> studentContactPersonList = studentContactPersonMapper.selectList(lambdaQueryWrapper);
//        page.restPage(studentContactPersonList);

        return studentContactPersonList.stream().map(studentContactPerson ->
                BeanCopyUtils.objClone(studentContactPerson, StudentContactPersonVo::new)).collect(Collectors.toList());
    }

    @Override
    public StudentContactPersonVo updateContactPerson(StudentContactPersonDto contactPersonVo) {
        if (GeneralTool.isEmpty(contactPersonVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        StudentContactPerson contactPerson = BeanCopyUtils.objClone(contactPersonVo, StudentContactPerson::new);
        utilService.updateUserInfoToEntity(contactPerson);
        studentContactPersonMapper.updateById(contactPerson);
        return findContactPersonById(contactPerson.getId());
    }

    @Override
    public Long addContactPerson(StudentContactPersonDto contactPersonVo) {
        if (GeneralTool.isEmpty(contactPersonVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        StudentContactPerson contactPerson = BeanCopyUtils.objClone(contactPersonVo, StudentContactPerson::new);
        utilService.updateUserInfoToEntity(contactPerson);
        studentContactPersonMapper.insertSelective(contactPerson);
        return contactPerson.getId();
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        studentContactPersonMapper.deleteById(id);
    }

    @Override
    public List<StudentContactPersonVo> getStudentContactPersonByStudentId(Long studentId) {
        if (GeneralTool.isEmpty(studentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("student_null"));
        }
        return studentContactPersonMapper.getStudentContactPersonByStudentId(studentId);
    }

    /**
     * Author Cream
     * Description : //删除被合并学生联系人信息
     * Date 2023/5/11 12:25
     * Params:
     * Return
     */
    @Override
    public void deleteByStudentId(Long mergedStudentId) {
        if (GeneralTool.isNotEmpty(mergedStudentId)) {
            if (studentContactPersonMapper.selectCount(Wrappers.<StudentContactPerson>lambdaQuery().eq(StudentContactPerson::getFkStudentId,mergedStudentId))>0) {
                studentContactPersonMapper.delete(Wrappers.<StudentContactPerson>lambdaQuery().eq(StudentContactPerson::getFkStudentId,mergedStudentId));
            }
        }
    }
}
