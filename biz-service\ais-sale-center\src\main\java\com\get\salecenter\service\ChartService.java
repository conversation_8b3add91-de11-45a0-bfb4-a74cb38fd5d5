package com.get.salecenter.service;


import com.get.salecenter.vo.ChartVo;
import com.get.salecenter.vo.DateRoomChartVo;
import com.get.salecenter.vo.UsedRoomChartVo;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/10/27 15:04
 * @verison: 1.0
 * @description:
 */
public interface ChartService {

    /**
     * @return java.util.List<java.util.Map < java.lang.String, java.util.List < java.lang.Integer>>>
     * @Description :获取房间汇总
     * @Param [isArrange, conventionId]
     * <AUTHOR>
     */
    List<DateRoomChartVo> getRoomChartList(boolean isArrange, Long conventionId, List<Integer> types);


    /**
     * @return java.util.List<java.util.Map < java.lang.String, java.util.List < java.lang.Integer>>>
     * @Description :获取桌台座位汇总
     * @Param [conventionId]
     * <AUTHOR>
     */
    ChartVo getTableChartList(Long conventionId, String tableType);

    /**
     * @return java.util.List<java.lang.String>
     * @Description :获取日期集合
     * @Param [conventionId]
     * <AUTHOR>
     */
    List<String> getDates(Long conventionId);

    /**
     * @return java.util.List<java.lang.String>
     * @Description :获取桌台编号集合
     * @Param [conventionId]
     * <AUTHOR>
     */
    List<String> getTableNums(Long conventionId, String tableType);

    /**
     * @return java.util.List<com.get.salecenter.vo.UsedRoomChartVo>
     * @Description :获取房间被使用汇总
     * @Param [conventionId]
     * <AUTHOR>
     */
    List<UsedRoomChartVo> getUsedRoomChartList(boolean isUsed, Long conventionId);

    /**
     * @return java.util.List<java.lang.String>
     * @Description :获取房间类型名列表
     * @Param [conventionId]
     * <AUTHOR>
     */
    List<String> getRoomTypeNames(Long conventionId);
}
