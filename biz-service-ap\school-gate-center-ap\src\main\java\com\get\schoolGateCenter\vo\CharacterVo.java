package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 
 * <AUTHOR>
 * @Description:
 * 
 * @param null 
 * @Return 
 * @date 2023/4/12 15:33
 */
@Data
public class CharacterVo extends BaseVoEntity {
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    @NotBlank(message = "表名不能为空", groups = {Add.class, Update.class})
    private String fkTableName;

    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    @NotNull(message = "表Id不能为空", groups = {Add.class, Update.class})
    private Long fkTableId;

    /**
     * 特性类型，枚举：世界排名/国家排名/CUG排名/REF排名/师生比例/男女比例/国际学生百分比/中国学生百分比
     */
    @ApiModelProperty(value = "特性类型，枚举：世界排名/国家排名/CUG排名/REF排名/师生比例/男女比例/国际学生百分比/中国学生百分比")
    @NotNull(message = "特性类型不能为空", groups = {Add.class, Update.class})
    private Integer characterType;

    /**
     * 特性值
     */
    @ApiModelProperty(value = "特性值")
    @NotBlank(message = "特性值不能为空", groups = {Add.class, Update.class})
    private String value;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

}
