<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.CourseTypeMapper">
  <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.CourseType">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="type_name" jdbcType="VARCHAR" property="typeName" />
    <result column="type_name_chn" jdbcType="VARCHAR" property="typeNameChn" />
    <result column="view_order" jdbcType="INTEGER" property="viewOrder" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.CourseType" useGeneratedKeys="true" keyProperty="id">
    insert into u_course_type
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="typeName != null">
        type_name,
      </if>
      <if test="typeNameChn != null">
        type_name_chn,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="typeName != null">
        #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="typeNameChn != null">
        #{typeNameChn,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select ifnull(max(view_order)+1,1) from u_course_type
  </select>
  <select id="getCourseTypeList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    select id,CASE WHEN IFNULL(type_name_chn, '') = '' THEN `type_name` ELSE CONCAT(`type_name`, '（', type_name_chn, '）') END  name from u_course_type order by type_name
  </select>

  <select id="getCourseTypeListByKeyword" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    SELECT
        id,
        type_name AS NAME,
        type_name_chn AS name_chn,
        CASE
    WHEN IFNULL(type_name_chn, '') = '' THEN
        `type_name`
    ELSE
        CONCAT(
            `type_name`,
            '（',
            type_name_chn,
            '）'
        )
    END fullName
    FROM
        u_course_type
    <if test="keyword!=null and keyword!=''">
      WHERE
      (`type_name` LIKE concat('%',#{keyword},'%') OR type_name_chn LIKE concat('%',#{keyword},'%'))
    </if>
    ORDER BY
        type_name
    LIMIT 50
  </select>

  <select id="getCourseTypeListByMode" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    select uct.id,CASE WHEN IFNULL(uct.type_name_chn, '') = '' THEN uct.type_name ELSE CONCAT(uct.type_name, '（', uct.type_name_chn, '）') END  name from u_course_type uct
    left join r_course_type_group_course_type rctgct on rctgct.fk_course_type_id = uct.id
    left join u_course_type_group uctg on uctg.id = rctgct.fk_course_type_group_id
    where 1=1

       and uctg.mode = 3

    order by uct.type_name
  </select>
  <select id="getNameById" parameterType="java.lang.Long" resultType="string">
    select
      CASE
    WHEN IFNULL(type_name_chn, '') = '' THEN
        `type_name`
    ELSE
        CONCAT(
            `type_name`,
            '（',
            type_name_chn,
            '）'
        )
    END fullName
    from
      u_course_type i
    where
      i.id = #{id}
  </select>

  <select id="selectCourseTypeList" resultType="com.get.institutioncenter.entity.CourseType">
    SELECT
      ct.*
    FROM
      `u_course_type` AS ct
        LEFT JOIN r_course_type_group_course_type AS ctg ON ctg.fk_course_type_id = ct.id
        LEFT JOIN u_course_type_group AS tg ON tg.id = ctg.fk_course_type_group_id
      <where>
        <if test="keyWord != null and  keyWord != ''">
          and ct.type_name like concat("%",#{keyWord},"%") or ct.type_name_chn like concat("%",#{keyWord},"%")
        </if>
        <if test="courseTypeGroupIds != null and courseTypeGroupIds.size() > 0">
        AND ctg.fk_course_type_group_id in <foreach collection="courseTypeGroupIds" open="(" separator="," close=")" item="item">
        #{item}
        </foreach>
        </if>

        <if test="strPublic != null and strPublic.size > 0">
          and
          <foreach collection="strPublic" item="item" separator="or" open="(" index=")" close=")">
            FIND_IN_SET(#{item},ct.public_level)
          </foreach>
        </if>

      </where>
    GROUP BY ct.id
    ORDER BY ct.view_order desc
  </select>

</mapper>