package com.get.officecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.officecenter.vo.OfficeMediaAndAttachedVo;
import com.get.officecenter.entity.OfficeMediaAndAttached;
import com.get.officecenter.mapper.MediaAndAttachedMapper;
import com.get.officecenter.service.IMediaAndAttachedService;
import com.get.officecenter.dto.MediaAndAttachedDto;
import com.google.common.collect.Maps;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2020/8/14 9:58
 * @verison: 1.0
 * @description:
 */
@Service
public class MediaAndAttachedServiceImpl extends ServiceImpl<MediaAndAttachedMapper,OfficeMediaAndAttached> implements IMediaAndAttachedService {
    @Resource
    private MediaAndAttachedMapper attachedMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IFileCenterClient fileCenterClient;

    @Override
    public List<FileDto> upload(MultipartFile[] multipartFiles) {
        if (GeneralTool.isEmpty(multipartFiles)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }
        List<FileDto> fileDtos = null;
        Result<List<FileDto>> result = fileCenterClient.upload(multipartFiles, LoggerModulesConsts.FINANCECENTER);
        if (!result.isSuccess()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_failure"));
        }
        JSONArray jsonArray = JSONArray.fromObject(result.getData());
        fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
        return fileDtos;
    }

    @Override
    public List<FileDto> uploadAppendix(MultipartFile[] multipartFiles) {
        if (GeneralTool.isEmpty(multipartFiles)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }
        List<FileDto> fileDtos = null;
        Result<List<FileDto>> result = fileCenterClient.uploadAppendix(multipartFiles, LoggerModulesConsts.OFFICECENTER);
        if (!result.isSuccess()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_failure"));
        }
        JSONArray jsonArray = JSONArray.fromObject(result.getData());
        fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
        return fileDtos;
    }

    @Override
    public void deleteMediaAttached(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        OfficeMediaAndAttached mediaAndAttached = attachedMapper.selectById(id);
        if (GeneralTool.isEmpty(mediaAndAttached)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        Result<Boolean> result = fileCenterClient.delete(mediaAndAttached.getFkFileGuid(), LoggerModulesConsts.OFFICECENTER);
        if (result.isSuccess()) {
            int i = attachedMapper.deleteById(id);
            if (i <= 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
            }
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Override
    public OfficeMediaAndAttachedVo addMediaAndAttached(MediaAndAttachedDto mediaAttachedVo) {
        if (GeneralTool.isEmpty(mediaAttachedVo.getFkTableName())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("tableName_null"));
        }
        OfficeMediaAndAttached andAttached = BeanCopyUtils.objClone(mediaAttachedVo, OfficeMediaAndAttached::new);
        Integer nextIndexKey = attachedMapper.getNextIndexKey(andAttached.getFkTableId(), mediaAttachedVo.getFkTableName());
        nextIndexKey = GeneralTool.isNotEmpty(nextIndexKey) ? nextIndexKey : 0;
        //实例化对象
        andAttached.setIndexKey(nextIndexKey);
        utilService.updateUserInfoToEntity(andAttached);
        attachedMapper.insertSelective(andAttached);
        OfficeMediaAndAttachedVo mediaAndAttachedDto = BeanCopyUtils.objClone(andAttached, OfficeMediaAndAttachedVo::new);
        mediaAndAttachedDto.setFilePath(mediaAttachedVo.getFilePath());
        mediaAndAttachedDto.setFileNameOrc(mediaAttachedVo.getFileNameOrc());
        mediaAndAttachedDto.setTypeValue(FileTypeEnum.getValue(mediaAttachedVo.getTypeKey()));
        mediaAndAttachedDto.setId(andAttached.getId());
        mediaAndAttachedDto.setFileKey(mediaAttachedVo.getFileKey());
        return mediaAndAttachedDto;
    }

    @Override
    public List<OfficeMediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo) {
        LambdaQueryWrapper<OfficeMediaAndAttached> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(attachedVo.getTypeKey())) {
            wrapper.eq(OfficeMediaAndAttached::getTypeKey, attachedVo.getTypeKey());
        }
        wrapper.eq(OfficeMediaAndAttached::getFkTableName, attachedVo.getFkTableName());
        wrapper.eq(OfficeMediaAndAttached::getFkTableId, attachedVo.getFkTableId());
        wrapper.orderByDesc(OfficeMediaAndAttached::getIndexKey);
        List<OfficeMediaAndAttached> mediaAndAttacheds = this.attachedMapper.selectList(wrapper);
        return getFileMedia(mediaAndAttacheds);
    }

    @Override
    public List<OfficeMediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo, Page page) {
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<MediaAndAttached> mediaAndAttacheds = getMediaAndAttacheds(attachedVo);
//        page.restPage(mediaAndAttacheds);
        if (GeneralTool.isEmpty(attachedVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }

//        Example example = new Example(MediaAndAttached.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(attachedVo.getTypeKey())) {
//            criteria.andEqualTo("typeKey", attachedVo.getTypeKey());
//        }
//        criteria.andEqualTo("fkTableName", attachedVo.getFkTableName());
//        criteria.andEqualTo("fkTableId", attachedVo.getFkTableId());
//        example.orderBy("indexKey").desc();
//        return attachedMapper.selectByExample(example);

        LambdaQueryWrapper<OfficeMediaAndAttached> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(attachedVo.getTypeKey())) {
            wrapper.eq(OfficeMediaAndAttached::getTypeKey, attachedVo.getTypeKey());
        }
        wrapper.eq(OfficeMediaAndAttached::getFkTableName, attachedVo.getFkTableName());
        wrapper.eq(OfficeMediaAndAttached::getFkTableId, attachedVo.getFkTableId());
        wrapper.orderByDesc(OfficeMediaAndAttached::getIndexKey);
        IPage<OfficeMediaAndAttached> pages = this.attachedMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<OfficeMediaAndAttached> mediaAndAttacheds = pages.getRecords();
        return getFileMedia(mediaAndAttacheds);
    }

    @Override
    public void updateTableId(Long id, Long tableId) {
        OfficeMediaAndAttached mediaAndAttached = new OfficeMediaAndAttached();
        mediaAndAttached.setFkTableId(tableId);
        mediaAndAttached.setId(id);
        attachedMapper.updateById(mediaAndAttached);
    }

    @Override
    public void deleteMediaAndAttachedByTableId(Long tableId, String tableName) {
//        Example example = new Example(MediaAndAttached.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkTableId", tableId);
//        criteria.andEqualTo("fkTableName", tableName);
//        attachedMapper.deleteByExample(example);
        LambdaQueryWrapper<OfficeMediaAndAttached> wrapper = new LambdaQueryWrapper();
        wrapper.eq(OfficeMediaAndAttached::getFkTableName, tableName);
        wrapper.eq(OfficeMediaAndAttached::getFkTableId, tableId);
        attachedMapper.delete(wrapper);
    }


    @Override
    public List<Map<String, Object>> findAgentMediaType() {
        return FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.SALEAGENT);
    }

    @Override
    public List<Map<String, Object>> findContractMediaType() {
        return FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.SALECONTRACT);
    }

    @Override
    public void movingOrder(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        OfficeMediaAndAttached ro = BeanCopyUtils.objClone(mediaAttachedVos.get(0), OfficeMediaAndAttached::new);
        Integer oneorder = ro.getIndexKey();
        OfficeMediaAndAttached rt = BeanCopyUtils.objClone(mediaAttachedVos.get(1), OfficeMediaAndAttached::new);
        Integer twoorder = rt.getIndexKey();
        ro.setIndexKey(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setIndexKey(oneorder);
        utilService.updateUserInfoToEntity(rt);
        attachedMapper.updateById(ro);
        attachedMapper.updateById(rt);
    }

    /**
     * 批量保存附件
     * @param mediaAndAttachedDtos
     * @return
     */
    @Override
    public Boolean saveBatchMediaAndAttached(List<MediaAndAttachedDto> mediaAndAttachedDtos) {
        if (GeneralTool.isEmpty(mediaAndAttachedDtos)){
            return false;
        }
        if (GeneralTool.isEmpty(mediaAndAttachedDtos.get(0).getFkTableName())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("tableName_null"));
        }
//        List<OfficeMediaAndAttached> officeMediaAndAttacheds = Lists.newArrayList();
        Map<Long,Integer> indexKeyMap = Maps.newHashMap();
        Set<Long> ids = mediaAndAttachedDtos.stream().map(MediaAndAttachedDto::getFkTableId).collect(Collectors.toSet());
        for (Long id : ids) {
            Integer nextIndexKey = attachedMapper.getNextIndexKey(id, mediaAndAttachedDtos.get(0).getFkTableName());
            indexKeyMap.put(id,nextIndexKey);
        }
        for (MediaAndAttachedDto mediaAttachedVo : mediaAndAttachedDtos) {
            if (GeneralTool.isEmpty(mediaAttachedVo.getFkTableName())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("tableName_null"));
            }
            OfficeMediaAndAttached andAttached = BeanCopyUtils.objClone(mediaAttachedVo, OfficeMediaAndAttached::new);
            Integer nextIndexKey = GeneralTool.isNotEmpty(indexKeyMap.get(mediaAttachedVo.getFkTableId()))?indexKeyMap.get(mediaAttachedVo.getFkTableId()):0;
            //实例化对象
            assert andAttached != null;
            andAttached.setIndexKey(nextIndexKey);
            utilService.setCreateInfo(andAttached);
//            officeMediaAndAttacheds.add(andAttached);
            indexKeyMap.put(mediaAttachedVo.getFkTableId(),nextIndexKey+1);
            int i = attachedMapper.insert(andAttached);
            if (i<=0){
                return false;
            }
        }
        return true;
    }


//    /**
//     * 获取媒体附件
//     *
//     * @param attachedVo
//     * @return
//     * @throws GetServiceException
//     */
//    private List<MediaAndAttached> getMediaAndAttacheds(MediaAndAttachedVo attachedVo) throws GetServiceException {
//        if (GeneralTool.isEmpty(attachedVo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
//        }
//        Example example = new Example(MediaAndAttached.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(attachedVo.getTypeKey())) {
//            criteria.andEqualTo("typeKey", attachedVo.getTypeKey());
//        }
//        criteria.andEqualTo("fkTableName", attachedVo.getFkTableName());
//        criteria.andEqualTo("fkTableId", attachedVo.getFkTableId());
//        example.orderBy("indexKey").desc();
//        return attachedMapper.selectByExample(example);
//    }

    private List<OfficeMediaAndAttachedVo> getFileMedia(List<OfficeMediaAndAttached> mediaAndAttachedList) {
        if (GeneralTool.isEmpty(mediaAndAttachedList)) {
            return null;
        }
        //获取guid集合
        List<String> guidList = mediaAndAttachedList.stream().map(OfficeMediaAndAttached::getFkFileGuid).collect(Collectors.toList());
        //转换成DTO
        List<OfficeMediaAndAttachedVo> mediaAndAttachedDtos = mediaAndAttachedList.stream()
                .map(mediaAndAttached -> BeanCopyUtils.objClone(mediaAndAttached, OfficeMediaAndAttachedVo::new)).collect(Collectors.toList());
        //根据GUID服务调用查询
        Map<String, List<String>> guidListWithTypeMap = new HashMap<>();
        guidListWithTypeMap.put(LoggerModulesConsts.OFFICECENTER, guidList);
        Result<List<FileDto>> result = fileCenterClient.findFileByGuid(guidListWithTypeMap);
        //返回结果不为空时 需要测试已经返回的数据但是可能没有显示。
        List<OfficeMediaAndAttachedVo> collect = null;
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            JSONArray jsonArray = JSONArray.fromObject(result.getData());
            List<FileDto> fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
            if (GeneralTool.isNotEmpty(fileDtos)) {
                //遍历查询GUID是否一致
                collect = mediaAndAttachedDtos.stream().map(mediaAndAttachedDto -> fileDtos
                        .stream()
                        .filter(fileDto -> fileDto.getFileGuid().equals(mediaAndAttachedDto.getFkFileGuid()))

                        .findFirst()
                        .map(fileDto -> {
                            mediaAndAttachedDto.setFilePath(fileDto.getFilePath());
                            mediaAndAttachedDto.setFileNameOrc(fileDto.getFileNameOrc());
                            mediaAndAttachedDto.setTypeValue(FileTypeEnum.getValue(mediaAndAttachedDto.getTypeKey()));
                            mediaAndAttachedDto.setFileKey(fileDto.getFileKey());
                            /*mediaAndAttachedDto.setFkTableName(null);*/
                            return mediaAndAttachedDto;
                        }).orElse(null)
                ).collect(Collectors.toList());
            }
        }
        return collect;
    }
}
