package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: Sea
 * @create: 2020/7/28 11:00
 * @verison: 1.0
 * @description:
 */
@Data
public class AreaCityVo extends BaseVoEntity {
    /**
     * 州省Id
     */
    @NotNull(message = "州省Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "州省Id")
    private Long fkAreaStateId;

    /**
     * 城市编号
     */
    @ApiModelProperty(value = "城市编号")
    private String num;

    /**
     * 城市名称
     */
    @NotBlank(message = "城市名称不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "城市名称")
    private String name;

    /**
     * 城市中文名称
     */
    @NotBlank(message = "城市中文名称不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "城市中文名称")
    private String nameChn;

    /**
     * 人口
     */
    @ApiModelProperty(value = "人口")
    private String population;

    /**
     * 面积
     */
    @ApiModelProperty(value = "面积")
    private String area;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    private String publicLevel;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    //自定义内容
    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyWord;
}
