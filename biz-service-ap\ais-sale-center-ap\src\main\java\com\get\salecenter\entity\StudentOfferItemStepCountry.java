package com.get.salecenter.entity;

import com.get.core.mybatis.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-22
 */
@Data
@TableName("r_student_offer_item_step_country")
@ApiModel(value="StudentOfferItemStepCountry对象", description="")
public class StudentOfferItemStepCountry extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "学生申请方案项目状态步骤Id")
    private Long fkStudentOfferItemStepId;

    @ApiModelProperty(value = "前置条件id，需要完成步骤条件")
    private String fkStudentOfferItemStepIdPrecondition;

    @ApiModelProperty(value = "适用国家Ids（多选）")
    private String fkAreaCountryIds;

}
