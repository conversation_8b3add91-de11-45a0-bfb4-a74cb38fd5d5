package com.get.pmpcenter.dto.institution;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/3/18
 * @Version 1.0
 * @apiNote:
 */
@Data
public class LockContractDto {

    @ApiModelProperty(value = "是否锁定：0否/1是")
    @NotNull(message = "是否锁定状态不能为空")
    private Integer isLocked;

    @ApiModelProperty(value = "合同ID")
    @NotNull(message = "合同ID不能为空")
    private Long contractId;
}
