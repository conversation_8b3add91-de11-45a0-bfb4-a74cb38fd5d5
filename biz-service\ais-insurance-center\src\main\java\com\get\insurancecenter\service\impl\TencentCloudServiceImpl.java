package com.get.insurancecenter.service.impl;

import com.get.insurancecenter.config.ConnectTencentCloud;
import com.get.insurancecenter.service.TencentCloudService;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.model.PutObjectRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Objects;

/**
 * @Author:Oliver
 * @Date: 2025/6/19
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class TencentCloudServiceImpl implements TencentCloudService {
    @Override
    public Boolean uploadObject(String bucketName, MultipartFile file, String fileKey) {
        try {
            // 指定要上传的文件
            File localFile = null;
            try {
                localFile = multipartFileToFile(file);
            } catch (Exception e) {
                log.error("上传文件失败:{}", e.getMessage());
                e.printStackTrace();
            }
            if (Objects.nonNull(localFile)) {
                if (localFile.exists()) {
                    try {
                        Thread.sleep(15);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    // fileKey 指定要上传到 COS 上对象键
                    PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, fileKey, localFile);
                    //公开桶上传
                    //私有桶区分GET和IAE两种类型，这里通过bucketname是否包含“iae”来区分，包含则取默认的私有key
                    log.info("上传文件到IAE私密桶：{}", bucketName);
                    ConnectTencentCloud.getPrivateCosClient().putObject(putObjectRequest);
                    log.info("上传文件成功");
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }

        } catch (CosServiceException serverException) {
            log.error("上传文件平台Server异常:{}", serverException.getErrorMessage());
            throw new RuntimeException("上传文件平台Server异常" + serverException.getErrorMessage());
        } catch (CosClientException clientException) {
            log.error("上传文件平台Client异常:{}", clientException.getMessage());
            throw new RuntimeException("上传文件平台Client异常" + clientException.getMessage());
        }
    }

    @Override
    public void deleteObjectRequest(String key, String bucketName) {
        try {
            // 指定对象在 COS 上的对象键
            ConnectTencentCloud.getPrivateCosClient().deleteObject(bucketName, key);

        } catch (Exception e) {
            log.error("删除文件失败:{}", e.getMessage());
        }
    }

    /**
     * MultipartFile 转 File
     *
     * @param file
     * @throws Exception
     */
    public static File multipartFileToFile(MultipartFile file) throws Exception {

        File toFile = null;
        if (file.equals("") || file.getSize() <= 0) {
            file = null;
        } else {
            InputStream ins = null;
            ins = file.getInputStream();
            toFile = new File(file.getOriginalFilename());
            inputStreamToFile(ins, toFile);
            ins.close();
        }
        return toFile;
    }

    /**
     * 获取流文件
     *
     * @param ins  传入文件字节流
     * @param file 返回的文件对象
     */
    private static void inputStreamToFile(InputStream ins, File file) {
        try {
            OutputStream os = new FileOutputStream(file);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            ins.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
