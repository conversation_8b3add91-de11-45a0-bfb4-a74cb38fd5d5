package com.get.remindercenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("m_batch_sending_email")
public class BatchSendingEmail extends BaseEntity {


    @ApiModelProperty(value = "目标表名，如：s_news")
    private String fkTableName;

    @ApiModelProperty(value = "目标Id")
    private Long fkTableId;

    @ApiModelProperty(value = "已发邮箱地址")
    private String email;

    @ApiModelProperty(value = "备注")
    private String remark;

}
