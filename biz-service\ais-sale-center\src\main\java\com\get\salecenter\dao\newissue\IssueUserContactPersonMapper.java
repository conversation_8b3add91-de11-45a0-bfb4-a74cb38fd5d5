package com.get.salecenter.dao.newissue;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.UserContactPerson;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("newissuedb")
public interface IssueUserContactPersonMapper extends BaseMapper<UserContactPerson> {

//    int insertSelective(UserContactPerson record);


}