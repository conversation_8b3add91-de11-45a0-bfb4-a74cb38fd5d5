package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/11/22 10:33
 * @verison: 1.0
 * @description:
 */
@Data
public class StudentReceivableAndPaySumVo extends BaseEntity {

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyShortName;

    @ApiModelProperty(value = "课程id")
    private Long fkInstitutionCourseId;

    /**
     * 学生名称
     */
    @ApiModelProperty(value = "学生名称")
    private String studentName;

    /**
     * 国家
     */
    @ApiModelProperty(value = "国家")
    private String countryName;

    /**
     * 国家中文名
     */
    @ApiModelProperty(value = "国家中文名")
    private String countryNameChn;

    /**
     * 学校名
     */
    @ApiModelProperty(value = "学校名")
    private String institutionName;

    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    private String fkAgentName;

    public String getFkAgentName() {
        if (StringUtils.isNotBlank(nameNote)) {
            fkAgentName += "(" + nameNote +")";
        }
        return fkAgentName;
    }

    @ApiModelProperty("代理名称备注")
    private String nameNote;
    /**
     * 学校中文名
     */
    @ApiModelProperty(value = "学校中文名")
    private String institutionNameChn;

    /**
     * 课程名
     */
    @ApiModelProperty(value = "课程名")
    private String institutionCourseName;

    /**
     * 课程中文名
     */
    @ApiModelProperty(value = "课程中文名")
    private String institutionCourseNameChn;

    /**
     * 课程长度类型(0周、1月、2年、3学期)
     */
    @ApiModelProperty(value = "课程长度类型(0周、1月、2年、3学期)")
    private Integer durationType;

    /**
     * 课程长度
     */
    @ApiModelProperty(value = "课程长度")
    private BigDecimal duration;

    /**
     * 应收金额
     */
    @ApiModelProperty(value = "应收金额")
    private String receivablePlanAmountInfo;

    /**
     * 实收金额
     */
    @ApiModelProperty(value = "实收金额")
    private String receivableActualAmountInfo;

    /**
     * 应收未收
     */
    @ApiModelProperty(value = "应收未收")
    private String receivableDiffAmountInfo;
    /**
     * 应付金额
     */
    @ApiModelProperty(value = "应付金额")
    private String payablePlanAmountInfo;
    /**
     * 实付金额
     */
    @ApiModelProperty(value = "实付金额")
    private String payableActualAmountInfo;
    /**
     * 应付未付
     */
    @ApiModelProperty(value = "应付未付")
    private String payableDiffAmountInfo;

    /**
     * 学校id
     */
    @ApiModelProperty(value = "学校id")
    private String fkInstitutionId;

    /**
     * 应付差额(前端判断用)
     */
    @ApiModelProperty(value = "应付差额(前端判断用)")
    private String[] payableDiffAmountList;

    /**
     * 应付差额
     */
    @ApiModelProperty(value = "应付差额")
    private String payableDiffAmount;

    /**
     * 应付差额币种
     */
    @ApiModelProperty(value = "应付差额币种")
    private String payableDiffAmountCurrencyType;

    /**
     * 应付金额
     */
    @ApiModelProperty(value = "应付金额")
    private String payablePlanAmount;

    /**
     * 应付金额币种
     */
    @ApiModelProperty(value = "应付金额币种")  //实付金额币种
    private String payablePlanAmountCurrencyType;

    /**
     * 实付金额
     */
    @ApiModelProperty(value = "实付金额")
    private String payableActualAmount;

    /**
     * 实付金额币种
     */
    @ApiModelProperty(value = "实付金额币种")
    private String payableActualAmountCurrencyType;

    /**
     * 应收差额(前端判断用)
     */
    @ApiModelProperty(value = "应收差额(前端判断用)")
    private String[] receivableDiffAmountList;

    /**
     * 应收差额
     */
    @ApiModelProperty(value = "应收差额")
    private String receivableDiffAmount;

    /**
     * 应收差额币种
     */
    @ApiModelProperty(value = "应收差额币种")
    private String receivableDiffAmountCurrencyType;


    /**
     * 应收金额
     */
    @ApiModelProperty("应收金额")
    private String receivablePlanAmount;


    @ApiModelProperty("发票绑定金额")
    private BigDecimal invoiceBindAmount;

    /**
     * 应收金额币种
     */
    @ApiModelProperty("应收金额币种")
    private String receivablePlanAmountCurrencyType;

    /**
     * 实收金额
     */
    @ApiModelProperty("实收金额")
    private String receivableActualAmount;

    @ApiModelProperty("发票绑定应付金额")
    private BigDecimal invoicePayableActualAmount;

    /**
     * 实收金额币种
     */
    @ApiModelProperty("实收金额币种")
    private String receivableActualAmountCurrencyType;

    /**
     * 应收目标类型key
     */
    @ApiModelProperty("应收目标类型key")
    private String receivableTypeKey;

    /**
     * 应收类型名称
     */
    @ApiModelProperty("应收类型名称")
    private String receivableTypeName;

    /**
     * 应付类型key
     */
    @ApiModelProperty("应付类型key")
    private String payableTypeKey;

    /**
     * 应付类型名称
     */
    @ApiModelProperty("应付类型名称")
    private String payableTypeName;


    /**
     * 旧系统学校名称
     */
    @ApiModelProperty(value = "旧系统学校名称")
    private String oldInstitutionName;

    /**
     * 旧系统学校全称
     */
    @ApiModelProperty(value = "旧系统学校全称")
    private String oldInstitutionFullName;

    /**
     * 旧系统课程名称
     */
    @ApiModelProperty(value = "旧系统课程名称")
    private String oldCourseCustomName;

    @ApiModelProperty(value = "bd名称")
    private String bdName;

    @ApiModelProperty(value = "代理id")
    private Long agentId;

    @ApiModelProperty(value = "代理编号")
    private String agentNum;

    @ApiModelProperty(value = "BD+代理编号")
    private String fkBdAgentNum;

    public String getFkBdAgentNum() {
        if (StringUtils.isNotBlank(bdName)) {
            fkBdAgentNum = bdName + " " + agentNum;
        }else {
            fkBdAgentNum= agentNum;
        }
        return fkBdAgentNum;
    }



    @ApiModelProperty(value = "渠道名")
    private String channelName;

    @ApiModelProperty(value = "实收金额hkd")
    private String receivableActualAmountInfoHkd;

    @ApiModelProperty(value = "应收金额hkd")
    private String receivablePlanAmountInfoHkd;

    @ApiModelProperty(value = "应收未收hkd")
    private String receivableDiffAmountInfoHkd;




    @ApiModelProperty(value = "应付金额hkd")
    private String payablePlanAmountInfoHkd;

    @ApiModelProperty(value = "实付金额Hkd")
    private String payableActualAmountInfoHkd;

    @ApiModelProperty(value = "应付未付Hkd")
    private String payableDiffAmountInfoHkd;


    @ApiModelProperty("应收金额币种")
    private String receivableCurrencyTypeNum;


    /**
     * 应收金额币种
     */
    @ApiModelProperty("应付金额币种")
    private String payCurrencyTypeNum;

    @ApiModelProperty("应收未收(前端判断用)")
    private List<StudentReceivableHkdVo> studentReceivableDiffHkdList;

    @ApiModelProperty("应收金额(前端判断用)")
    private List<StudentReceivableHkdVo> studentReceivableAmountHkdList;

    @ApiModelProperty("实收金额(前端判断用)")
    private List<StudentReceivableHkdVo> studentReceivableActualAmountHkdList;



    @ApiModelProperty("应付未付(前端判断用)")
    private List<StudentPayHkdVo> studentPayDiffHkdList;

    @ApiModelProperty("应付金额(前端判断用)")
    private List<StudentPayHkdVo> studentPayAmountHkdList;

    @ApiModelProperty("实付金额(前端判断用)")
    private List<StudentPayHkdVo> studentPayActualAmountHkdList;

    @ApiModelProperty("开学时间（申请计划延时入学时间）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deferOpeningTime;

    @ApiModelProperty(value = "佣金费率")
    private String commissionRate;

    @ApiModelProperty(value = "学费金额")
    private BigDecimal tuitionAmount;

    @ApiModelProperty(value = "代理佣金费率")
    private String agentCommissionRate;

    @ApiModelProperty("应收计划数量")
    private Integer receivablePlanNum;

    @ApiModelProperty("申请创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date itemGmtCreate;
}
