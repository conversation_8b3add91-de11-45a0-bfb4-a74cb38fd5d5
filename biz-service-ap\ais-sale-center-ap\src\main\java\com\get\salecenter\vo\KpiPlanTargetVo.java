package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2024/4/16
 * @TIME: 18:30
 * @Description:
 **/
@Data
public class KpiPlanTargetVo extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "KPI方案组别子项Id")
    private Long fkKpiPlanGroupItemId;

    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;

    @ApiModelProperty(value = "员工姓名")
    private String staffName;

    @ApiModelProperty(value = "目标设置（成功入学）")
    private Integer targetEnrolled;

    @ApiModelProperty(value = "KPI方案考核人员Id")
    //@Column(name = "fk_kpi_plan_staff_id")
    private Long fkKpiPlanStaffId;

    @ApiModelProperty(value = "统计方式，枚举：个人=1/团队(含业务下属)=2")
    private Integer countMode;
}
