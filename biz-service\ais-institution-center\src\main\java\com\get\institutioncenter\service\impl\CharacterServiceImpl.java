package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.CharacterMapper;
import com.get.institutioncenter.vo.CharacterVo;
import com.get.institutioncenter.entity.Character;
import com.get.institutioncenter.service.ICharacterService;
import com.get.institutioncenter.dto.CharacterDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/12/30
 * @TIME: 15:42
 * @Description:
 **/
@Service
public class CharacterServiceImpl extends BaseServiceImpl<CharacterMapper, Character> implements ICharacterService {
    @Resource
    private CharacterMapper characterMapper;
    @Resource
    private UtilService utilService;

    @Override
    public CharacterVo findCharacterById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Character character = characterMapper.selectById(id);
        if (GeneralTool.isEmpty(character)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        CharacterVo characterVo = BeanCopyUtils.objClone(character, CharacterVo::new);
        if (GeneralTool.isNotEmpty(characterVo.getCharacterType())) {
            characterVo.setTypeName(ProjectExtraEnum.getValue(characterVo.getCharacterType()));
        }
        return characterVo;
    }

    @Override
    public List<CharacterVo> getCharacters(CharacterDto characterDto, Page page) {
        LambdaQueryWrapper<Character> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(characterDto)) {
            if (GeneralTool.isNotEmpty(characterDto.getFkTableId())) {
                wrapper.eq(Character::getFkTableId, characterDto.getFkTableId());
            }
            if (GeneralTool.isNotEmpty(characterDto.getFkTableName())) {
                wrapper.eq(Character::getFkTableName, characterDto.getFkTableName());
            }
        }
        wrapper.orderByDesc(Character::getViewOrder);
        //获取分页数据
        IPage<Character> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<Character> ct = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<CharacterVo> convertDatas = new ArrayList<>();
        for (Character c : ct) {
            CharacterVo dto = BeanCopyUtils.objClone(c, CharacterVo::new);
            if (GeneralTool.isNotEmpty(dto.getCharacterType())) {
                dto.setTypeName(ProjectExtraEnum.getValueByKey(dto.getCharacterType(), ProjectExtraEnum.FEATURE_TYPE));
            }
            convertDatas.add(dto);
        }
        return convertDatas;
    }

    @Override
    public CharacterVo updateCharacter(CharacterDto characterDto) {
        if (characterDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(characterDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Character ct = characterMapper.selectById(characterDto.getId());
        if (ct == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        Character character = BeanCopyUtils.objClone(characterDto, Character::new);
        utilService.updateUserInfoToEntity(character);
        characterMapper.updateById(character);
        return findCharacterById(character.getId());
    }

    @Override
    public void delete(Long id) {
        //TODO 改过
        //Character character = findCharacterById(id);
        CharacterVo character = findCharacterById(id);
        if (character == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        int i = characterMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAdd(List<CharacterDto> characterDtos) {
        for (CharacterDto characterDto : characterDtos) {
            Character Character = BeanCopyUtils.objClone(characterDto, com.get.institutioncenter.entity.Character::new);
            utilService.updateUserInfoToEntity(Character);
            characterMapper.insert(Character);
        }
    }

    @Override
    public List<Map<String, Object>> findType() {
        return ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.FEATURE_TYPE);
    }
}
