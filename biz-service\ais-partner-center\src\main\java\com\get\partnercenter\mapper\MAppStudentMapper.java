package com.get.partnercenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.get.partnercenter.entity.MAppStudentEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.partnercenter.vo.MAppStudentVo;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【m_app_student】的数据库操作Mapper
* @createDate 2025-03-31 14:55:51
* @Entity com.get.partnercenter.entity.MAppStudent
*/
@Mapper
@DS("saledb")
public interface MAppStudentMapper extends BaseMapper<MAppStudentEntity> {
    MAppStudentVo searchDetail(Long id);
}




