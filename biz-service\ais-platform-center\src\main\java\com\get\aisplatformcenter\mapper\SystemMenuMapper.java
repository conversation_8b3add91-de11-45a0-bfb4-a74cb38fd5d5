package com.get.aisplatformcenter.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.aisplatformcenterap.dto.GetPermissionMenuDto;
import com.get.aisplatformcenterap.entity.SystemMenu;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * Partner (SystemMenu)表数据库访问层
 */
@Mapper
public interface SystemMenuMapper extends BaseMapper<SystemMenu> {

    List<SystemMenu> selectPartnerMenuList(@Param("getPermissionMenuDto") GetPermissionMenuDto getPermissionMenuDto);
}

