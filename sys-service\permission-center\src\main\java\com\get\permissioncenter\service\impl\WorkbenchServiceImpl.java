package com.get.permissioncenter.service.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.permissioncenter.dto.workbench.WorkbenchApprovalDto;
import com.get.permissioncenter.enums.WorkbenchApprovalTypeEnum;
import com.get.permissioncenter.service.WorkbenchService;
import com.get.permissioncenter.vo.workbench.WorkbenchApprovalVo;
import com.get.pmpcenter.feign.IPmpCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/4/14
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class WorkbenchServiceImpl implements WorkbenchService {

    @Autowired
    private IPmpCenterClient pmpCenterClient;


    @Override
    public List<WorkbenchApprovalVo> getWorkbenchApprovalList(WorkbenchApprovalDto params, Page page) {
        //如果审核类型为空,调用全部关联的服务获取全部数据整合到一个list里面,如果有指定类型,调用指定类型的服务获取数据，减少请求次数
        List<WorkbenchApprovalVo> result = new ArrayList<>();
        //获取PMP审核列表
        if (StringUtils.isNotBlank(params.getApprovalType()) && WorkbenchApprovalTypeEnum.isPmpType(params.getApprovalType())) {
            result = getPmpApprovalList(params);
        }
        //工休单审核列表
        if (StringUtils.isNotBlank(params.getApprovalType()) && WorkbenchApprovalTypeEnum.WORK_LEAVE_FORM.getCode().equals(params.getApprovalType())) {
            result = getWorkLeaveFormApprovalList(params);
        }

        if (StringUtils.isBlank(params.getApprovalType())) {
            result.addAll(getPmpApprovalList(params));
            result.addAll(getWorkLeaveFormApprovalList(params));
        }
        //排序
        result = result.stream()
                .sorted(Comparator.comparing(WorkbenchApprovalVo::getGmtCreate).reversed())
                .collect(Collectors.toList());
        // 分页处理
        int currentPage = page.getCurrentPage();
        int showCount = page.getShowCount();
        int totalCount = result.size();
        int fromIndex = (currentPage - 1) * showCount;
        int toIndex = Math.min(fromIndex + showCount, totalCount);

        List<WorkbenchApprovalVo> pagedResult = fromIndex >= totalCount ? Collections.emptyList() : result.subList(fromIndex, toIndex);
        // 设置分页信息
        page.setTotalResult(totalCount);
        page.setTotalPage(Objects.nonNull(showCount) && showCount > 0 ? (totalCount + showCount - 1) / showCount : 0);
        return pagedResult;
    }


    private List<WorkbenchApprovalVo> getPmpApprovalList(WorkbenchApprovalDto params) {
        //获取PMP审核列表
        log.info("获取PMP审核列表, approvalType:{}, staffId:{}", params.getApprovalType(), SecureUtil.getStaffId());
        Result<List<WorkbenchApprovalVo>> workbenchApprovalList = pmpCenterClient.getWorkbenchApprovalList(SecureUtil.getStaffId(),
                params.getApprovalType(),
                Objects.nonNull(params.getApprovalStatus()) ? params.getApprovalStatus() : -1,
                SecureUtil.getLoginId());
        if (!workbenchApprovalList.isSuccess()) {
            log.error("获取PMP审核列表失败, approvalType:{}, staffId:{}, msg:{}", params.getApprovalType(), SecureUtil.getStaffId());
            throw new GetServiceException(LocaleMessageUtils.getMessage("FAILED_TO_GET_PMP_REVIEW_LIST"));
        }
        return workbenchApprovalList.getData();
    }

    private List<WorkbenchApprovalVo> getWorkLeaveFormApprovalList(WorkbenchApprovalDto params) {
        //todo 获取工休单审核列表
        return new ArrayList<>();
    }
}
