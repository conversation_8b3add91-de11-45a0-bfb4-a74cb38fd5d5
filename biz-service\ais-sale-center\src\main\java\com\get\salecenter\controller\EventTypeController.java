package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.EventTypeVo;
import com.get.salecenter.service.IEventTypeService;
import com.get.salecenter.dto.EventTypeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/12/7 11:26
 * @verison: 1.0
 * @description:
 */
@Api(tags = "活动类型管理")
@RestController
@RequestMapping("sale/eventType")
public class EventTypeController {
    @Resource
    private IEventTypeService eventTypeService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventTypeVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/活动类型管理/活动类型详情")
    @GetMapping("/{id}")
    public ResponseBo<EventTypeVo> detail(@PathVariable("id") Long id) {
        EventTypeVo data = eventTypeService.findEventTypeById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :批量新增信息
     * @Param [eventTypeDtos]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/活动类型管理/新增活动类型")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(EventTypeDto.Add.class)  ValidList<EventTypeDto> eventTypeDtos) {
        eventTypeService.batchAdd(eventTypeDtos);
        return SaveResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/活动类型管理/删除活动类型")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        eventTypeService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventTypeVo>
     * @Description :修改信息
     * @Param [eventTypeDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动类型管理/更新活动类型")
    @PostMapping("update")
    public ResponseBo<EventTypeVo> update(@RequestBody @Validated(EventTypeDto.Update.class)  EventTypeDto eventTypeDto) {
        return UpdateResponseBo.ok(eventTypeService.updateEventType(eventTypeDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventTypeVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为搜索关键字(类型名称)")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/活动类型管理/查询活动类型")
    @PostMapping("datas")
    public ResponseBo<EventTypeVo> datas(@RequestBody SearchBean<EventTypeDto> page) {
        List<EventTypeVo> datas = eventTypeService.getEventTypes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :上移下移
     * @Param [eventTypeDtos]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动类型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<EventTypeDto> eventTypeDtos) {
        eventTypeService.movingOrder(eventTypeDtos);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :活动类型下拉框数据
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "活动类型下拉框数据", notes = "")
    @GetMapping("getEventTypeList")
    public ResponseBo<EventTypeVo> getEventTypeList() {
        List<EventTypeVo> datas = eventTypeService.getEventTypeList();
        return new ListResponseBo<>(datas);
    }
}
