package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 提交代理确认结算Vo
 *
 * <AUTHOR>
 * @date 2021/12/29 12:10
 */
@Data
public class SubmitSettlementDto {

    @NotNull(message = "代理id不能为空")
    @ApiModelProperty(value = "代理id")
    private Long agentId;

    @NotNull(message = "应付计划id不能为空")
    @ApiModelProperty(value = "应付计划ids")
    private List<Long> fkPayablePlanIdList;


}
