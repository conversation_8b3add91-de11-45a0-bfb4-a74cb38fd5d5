package com.get.salecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.AESConstant;
import com.get.common.consts.CacheKeyConstants;
import com.get.common.consts.SaleCenterConstant;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.AESUtils;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.redis.cache.GetRedis;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.Exceptions;
import com.get.core.tool.utils.GeneralTool;
import cn.hutool.core.util.URLUtil;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.dto.RemindTaskDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.salecenter.dao.sale.AppAgentApproveCommentMapper;
import com.get.salecenter.dto.AppAgentApproveCommentDto;
import com.get.salecenter.dto.EmailSendContext;
import com.get.salecenter.entity.AppAgent;
import com.get.salecenter.entity.AppAgentApproveComment;
import com.get.salecenter.enums.AgentAppFromEnum;
import com.get.salecenter.enums.MiniProgramPageEnum;
import com.get.salecenter.service.IAppAgentApproveCommentService;
import com.get.salecenter.service.IAppAgentService;
import com.get.salecenter.utils.EmailSenderUtils;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.vo.AppAgentApproveCommentVo;
import com.google.common.collect.Lists;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @author: Hardy
 * @create: 2023/3/31 10:43
 * @verison: 1.0
 * @description:
 */
@Slf4j
@Service
public class AppAgentApproveCommentServiceImpl extends GetServiceImpl<AppAgentApproveCommentMapper, AppAgentApproveComment> implements IAppAgentApproveCommentService {

    @Resource
    private UtilService utilService;
    @Resource
    private IReminderCenterClient reminderCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Lazy
    @Resource
    private IAppAgentService appAgentService;
    @Resource
    private GetRedis getRedis;
    @Resource
    private EmailSenderUtils emailSenderUtils;

    /**
     * 保存代理申请审批意见
     * @param appAgentApproveCommentDto
     * @return
     */
    @Override
    public Long addAppAgentApproveCommentVo(AppAgentApproveCommentDto appAgentApproveCommentDto) {
        AppAgentApproveComment appAgentApproveComment = BeanCopyUtils.objClone(appAgentApproveCommentDto, AppAgentApproveComment::new);
        utilService.setCreateInfo(appAgentApproveComment);
        boolean saveFlag = save(appAgentApproveComment);
        if (!saveFlag){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return appAgentApproveComment.getId();
    }


    /**
     * 发送邮件
     * @param id
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public void sendEmail(Long id) {
        AppAgentApproveComment appAgentApproveComment = getById(id);
        if (GeneralTool.isEmpty(appAgentApproveComment)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (GeneralTool.isNotEmpty(appAgentApproveComment.getEmailTime())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("email_send_success"));
        }
        Long fkAppAgentId = appAgentApproveComment.getFkAppAgentId();
        AppAgent appAgent = appAgentService.getById(fkAppAgentId);
        if (GeneralTool.isEmpty(appAgent)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (GeneralTool.isEmpty(appAgent.getFkStaffId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("staff_id_null"));
        }

        //获取中英文配置
        Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
        String versionValue2 = versionConfigMap.get(appAgent.getFkCompanyId());
        //发送邮件
        StringBuilder taskTitle = null;
        String staffName = null;
        String taskRemark = null;
        Map<String, String> map = new HashMap<>();
        if(versionValue2.equals("en")){
            taskTitle = new StringBuilder("【Agent Application Approval Decision Notification】");
            Set<Long> staffIds = new HashSet<>();
            staffIds.add(appAgent.getFkStaffId());
            Map<Long, String> map1  =permissionCenterClient.getStaffEnNameByIds(staffIds);
            staffName =map1.get(appAgent.getFkStaffId());
            //加提醒任务
            map.put("staffName",staffName);
            map.put("agentName",appAgent.getName());
            map.put("approveComment",appAgentApproveComment.getApproveComment());
            // 申请表单链接
            String link =getAppAgentFormDetailLink(fkAppAgentId,appAgent.getFkCompanyId());
            map.put("taskLink", link);
            taskRemark = MyStringUtils.getReminderTemplate(map, SaleCenterConstant.APP_AGENT_APPROVE_COMMENT_REMINDER_ENGLISH);
        }else {
            taskTitle = new StringBuilder("代理申请审批意见通知");
            staffName = permissionCenterClient.getStaffName(appAgent.getFkStaffId()).getData();
            //加提醒任务
            map.put("staffName",staffName);
            map.put("agentName",appAgent.getName());
            map.put("approveComment",appAgentApproveComment.getApproveComment());
            // 申请表单链接
            String link =getAppAgentFormDetailLink(fkAppAgentId,appAgent.getFkCompanyId());
            map.put("taskLink", link);
            taskRemark = MyStringUtils.getReminderTemplate(map, SaleCenterConstant.APP_AGENT_APPROVE_COMMENT_REMINDER);
        }
        map.put("startTime", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now()));
        map.put("versionValue",versionValue2);
        taskTitle.append("（").append(appAgent.getName()).append("）");

        //String link = ApplyAgentOnlineFormUtils.getAppAgentFormDetailLink(fkAppAgentId, appAgent.getFkCompanyId());

        RemindTaskDto remindTaskDto = new RemindTaskDto();
        remindTaskDto.setTaskTitle(taskTitle.toString());
        remindTaskDto.setTaskRemark(taskRemark);
        //邮件方式发送
        remindTaskDto.setRemindMethod("1");
        //默认设置执行中
        remindTaskDto.setStatus(1);
        //默认背景颜色
        remindTaskDto.setTaskBgColor("#3788d8");
        remindTaskDto.setFkStaffId(appAgent.getFkStaffId());
        remindTaskDto.setStartTime(new Date());
        remindTaskDto.setFkTableName(TableEnum.APP_AGENT_APPROVE_COMMENT.key);
        remindTaskDto.setFkTableId(id);
        remindTaskDto.setFkRemindEventTypeKey(ProjectKeyEnum.APP_AGENT_ADD_NOTICE.key);
        if(versionValue2.equals("en")){
            remindTaskDto.setLanguageCode("en");
        }
        appAgentApproveComment.setEmailTime(new Date());
        utilService.setUpdateInfo(appAgentApproveComment);
        boolean updateFlag = updateById(appAgentApproveComment);
        if (!updateFlag){
            throw new GetServiceException(LocaleMessageUtils.getMessage("news_email_send_fail"));
        }

        //Result<Boolean> result = reminderCenterClient.batchAdd(Lists.newArrayList(remindTaskDto));
        Result<Boolean> result = reminderCenterClient.batchAddTask(Lists.newArrayList(remindTaskDto));
        if (!result.isSuccess()||GeneralTool.isEmpty(result.getData())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("news_email_send_fail"));
        }
        List<Long> staffIds = new ArrayList<>();
        staffIds.add(appAgent.getFkStaffId());
        List<EmailSenderQueue> emailSenderQueues = new ArrayList<>();
        EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
        emailSenderQueue.setFkDbName(ProjectKeyEnum.SALE_CENTER.key);
        emailSenderQueue.setFkTableId(id);
        emailSenderQueue.setFkTableName(TableEnum.APP_AGENT_APPROVE_COMMENT.key);
        emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.APP_AGENT_ADD_NOTICE.getEmailTemplateKey());
        emailSenderQueue.setOperationTime(new Date());
        emailSenderQueue.setEmailParameter(JSON.toJSONString(map));
        emailSenderQueue.setEmailTo(staffIds.toString());
        emailSenderQueue.setEmailTitle(taskTitle.toString());
        emailSenderQueues.add(emailSenderQueue);

        Result<Boolean> result1 = reminderCenterClient.batchAddEmailQueue(emailSenderQueues);
        if (!result1.isSuccess()||GeneralTool.isEmpty(result1.getData())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("news_email_send_fail"));
        }

    }

    public String getAppAgentFormDetailLink(Long fkAppAgentId, Long fkCompanyId){
        String encrypt = null;
        try {
            encrypt = AESUtils.Encrypt(String.valueOf(fkAppAgentId), AESConstant.AESKEY);
        } catch (Exception e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("sign_encrypt_fail"));
        }
        String link = "";
        //根据公司获取域名
        ConfigVo configVo = permissionCenterClient.getConfigByKey(ProjectKeyEnum.FILE_SRC_PREFIX.key).getData();
        String configValue3 = configVo.getValue3();

        String taskRemark = null;
        link = configValue3+"/apply-agent-online-form/?sign=" + encrypt;
        System.out.println("============="+link);
        return link;
    }





    /**
     * 发送邮件
     * @param id
     */
    @Override
    public void sendEmailByLock(Long id) {
        boolean tryLock = false;
        int spinCount = 0;
        //获取锁 获取不到则进入自旋
        while(!tryLock&&spinCount<3){
            tryLock = getRedis.setNx(CacheKeyConstants.APP_AGENT_APPROVE_COMMENT_SEND_EMAIL_LOCK_KEY + id, 1, 10L);
            spinCount++;

        }
        if (!tryLock){
            throw new GetServiceException(LocaleMessageUtils.getMessage("SYSTEM_BUSY"));
        }
        try {
            //加了分布式事务
            this.sendEmail(id);
        }catch (Exception e){
            //处理抛出自定义异常
            if (e instanceof GetServiceException){
                throw new GetServiceException(e.getMessage());
            }else{
                throw Exceptions.unchecked(e);
            }
        }finally {
            //释放锁
            getRedis.del(CacheKeyConstants.APP_AGENT_APPROVE_COMMENT_SEND_EMAIL_LOCK_KEY + id);
        }
    }

    /**
     * 保存并发送邮件
     * @param appAgentApproveCommentDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long saveAndSendEmail(AppAgentApproveCommentDto appAgentApproveCommentDto) {
        //新增
        Long appAgentApproveCommentId = addAppAgentApproveCommentVo(appAgentApproveCommentDto);

        // 调用统一的邮件发送方法
        sendEmailUnified(appAgentApproveCommentId);
        return appAgentApproveCommentId;
    }

    /**
     * 保存并发送邮件
     * @param appAgentApproveCommentDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long saveCommentAndSendRejectEmail(AppAgentApproveCommentDto appAgentApproveCommentDto) {
        Long appAgentId = appAgentApproveCommentDto.getFkAppAgentId();
        if (ObjectUtils.isNull(appAgentId)) {
            log.error("代理申请ID为空");
            throw new GetServiceException(LocaleMessageUtils.getMessage("app_agent_id_null"));
        }

        // 更新申请状态为拒绝（会触发邮件发送逻辑）
        appAgentService.updateAppStatus(appAgentId, ProjectExtraEnum.APP_STATUS_REJECT.key);
        
        // 新增审批意见
        Long appAgentApproveCommentId = addAppAgentApproveCommentVo(appAgentApproveCommentDto);

        return appAgentApproveCommentId;
    }


    /**
     * 删除
     * @param id
     */
    @Override
    public void delete(Long id) {
        boolean removeFlag = removeById(id);
        if (!removeFlag){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    /**
     * 列表
     * @param appAgentApproveCommentDto
     * @param page
     * @return
     */
    @Override
    public List<AppAgentApproveCommentVo> getAppAgentApproveComments(AppAgentApproveCommentDto appAgentApproveCommentDto, Page page) {
        if (GeneralTool.isEmpty(appAgentApproveCommentDto.getFkAppAgentId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        LambdaQueryWrapper<AppAgentApproveComment> wrapper = Wrappers.lambdaQuery(AppAgentApproveComment.class);
        wrapper.eq(AppAgentApproveComment::getFkAppAgentId, appAgentApproveCommentDto.getFkAppAgentId());
        wrapper.orderByDesc(AppAgentApproveComment::getGmtCreate);
        IPage<AppAgentApproveComment> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<AppAgentApproveComment> appAgentApproveComments = pages.getRecords();
        page.setAll((int) pages.getTotal());

        List<AppAgentApproveCommentVo> appAgentApproveCommentVos = BeanCopyUtils.copyListProperties(appAgentApproveComments, AppAgentApproveCommentVo::new);
        appAgentApproveCommentVos.forEach(appAgentApproveCommentVo -> {
            if (GeneralTool.isEmpty(appAgentApproveCommentVo.getEmailTime())){
                appAgentApproveCommentVo.setSendFlag(true);
            }
        });
        return appAgentApproveCommentVos;
    }

    /**
     * 发送代理申请审批意见通知邮件（新版）
     *
     * @param appAgentId               代理申请ID
     * @param approveComment           审批意见
     * @param appAgentApproveCommentId
     */
    private void sendApprovalNoticeEmail(Long appAgentId, String approveComment, Long appAgentApproveCommentId) {
        try {
            log.info("开始发送代理申请审批意见通知邮件，代理申请ID: {}", appAgentId);
            
            // 获取AppAgent信息
            AppAgent appAgent = appAgentService.getById(appAgentId);
            if (GeneralTool.isEmpty(appAgent)) {
                log.error("未找到代理申请信息，ID: {}", appAgentId);
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            
            if (GeneralTool.isEmpty(appAgent.getFkStaffId())) {
                log.error("代理申请关联的员工ID为空，代理申请ID: {}", appAgentId);
                throw new GetServiceException(LocaleMessageUtils.getMessage("staff_id_null"));
            }
            
            // 获取BD员工信息
            StaffVo bdStaff = appAgentService.getBdStaffInfoHasEmail(appAgent.getFkStaffId());
            if (bdStaff == null) {
                log.error("获取BD员工信息失败，员工ID: {}", appAgent.getFkStaffId());
                throw new GetServiceException(LocaleMessageUtils.getMessage("bd_staff_info_get_failed"));
            }
            
            // 对AppAgent ID进行AES加密
            String encryptedAppAgentId;
            try {
                encryptedAppAgentId = AESUtils.Encrypt(String.valueOf(appAgentId), AESConstant.AESKEY);
            } catch (Exception e) {
                log.error("代理申请ID加密失败，代理申请ID: {}", appAgentId, e);
                throw new GetServiceException(LocaleMessageUtils.getMessage("sign_encrypt_fail"));
            }
            
            // 构建邮件参数Map
            Map<String, String> emailParams = new HashMap<>();
            emailParams.put("sign", URLUtil.encodeAll(encryptedAppAgentId));

            // 构建小程序二维码路径
            String qrcodePath = MiniProgramPageEnum.NEW_APPAGENT_ADD.buildFullPath(URLUtil.encodeAll(encryptedAppAgentId));
            emailParams.put("qrcode", qrcodePath);
            
            // 根据appAgentApproveCommentId查询审批意见获取创建时间
            AppAgentApproveComment appAgentApproveComment = getById(appAgentApproveCommentId);
            String currentTime = "";
            if (appAgentApproveComment != null && appAgentApproveComment.getGmtCreate() != null) {
                // 将Date转换为LocalDateTime再格式化
                LocalDateTime createDateTime = LocalDateTime.ofInstant(
                    appAgentApproveComment.getGmtCreate().toInstant(), 
                    java.time.ZoneId.systemDefault()
                );
                currentTime = createDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
            
            // 添加其他邮件参数
            emailParams.put("approveComment", approveComment != null ? approveComment : "");
            emailParams.put("staffName", bdStaff.getName() != null ? bdStaff.getName() : "");
            emailParams.put("personalName", appAgent.getName() != null ? appAgent.getName() : "");
            emailParams.put("currentTime", currentTime);
            
            // 构建邮件发送上下文
            EmailSendContext emailContext = EmailSendContext.builder()
                    .projectKey(ProjectKeyEnum.SALE_CENTER)
                    .tableName(TableEnum.APP_AGENT_APPROVE_COMMENT)
                    .tableId(appAgentApproveCommentId)
                    .recipient(bdStaff.getEmail())
                    .emailTemplate(EmailTemplateEnum.AGENT_APPLICATION_APPROVE_NOTICE)
                    .parameters(emailParams)
                    .staffId(appAgent.getFkStaffId())
                    .build();
            
            // 发送邮件
            List<EmailSendContext> emailContexts = new ArrayList<>();
            emailContexts.add(emailContext);
            
            emailSenderUtils.sendBatchEmails(emailContexts, appAgentId);

            log.info("代理申请审批意见通知邮件发送成功，代理申请ID: {}, 收件人: {}", appAgentId, bdStaff.getEmail());

            // 更新审批意见的邮件发送时间和人
            updateEmailTime(appAgentApproveCommentId);

        } catch (Exception e) {
            log.error("发送代理申请审批意见通知邮件异常，代理申请ID: {}", appAgentId, e);
            throw new GetServiceException(LocaleMessageUtils.getMessage("news_email_send_fail"));
        }
    }

    /**
     * 统一的邮件发送方法（根据代理申请类型选择新版或旧版邮件）
     *
     * @param appAgentApproveCommentId 审批意见ID
     */
    @Override
    public void sendEmailUnified(Long appAgentApproveCommentId) {
        try {
            AppAgentApproveComment appAgentApproveComment = this.getById(appAgentApproveCommentId);
            if (appAgentApproveComment == null) {
                return;
            }
            Long appAgentId = appAgentApproveComment.getFkAppAgentId();
            // 获取AppAgent信息判断类型
            AppAgent appAgent = appAgentService.getById(appAgentId);
            if (GeneralTool.isEmpty(appAgent)) {
                log.error("未找到代理申请信息，ID: {}", appAgentId);
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            
            Integer appFrom = appAgent.getAppFrom();
            if (ObjectUtils.isNotNull(appFrom) && AgentAppFromEnum.isNewType(appFrom)) {
                // 发送新版代理申请审批意见通知邮件
                sendApprovalNoticeEmail(appAgentId, appAgentApproveComment.getApproveComment(), appAgentApproveCommentId);
            } else {
                // 发送旧版邮件
                sendEmailByLock(appAgentApproveCommentId);
            }
            
        } catch (Exception e) {
            log.error("统一邮件发送异常，审批意见ID: {}", appAgentApproveCommentId, e);
            throw e;
        }
    }

    /**
     * 更新审批意见的邮件发送时间和人
     *
     * @param appAgentApproveCommentId 审批意见ID
     */
    @Override
    public void updateEmailTime(Long appAgentApproveCommentId) {
        try {
            AppAgentApproveComment appAgentApproveComment = getById(appAgentApproveCommentId);
            if (appAgentApproveComment == null) {
                log.error("审批意见不存在，ID: {}", appAgentApproveCommentId);
                return;
            }

            // 更新邮件发送时间和人
            appAgentApproveComment.setEmailTime(new Date());
            utilService.setUpdateInfo(appAgentApproveComment);
            boolean updateFlag = updateById(appAgentApproveComment);

            if (updateFlag) {
                log.info("成功更新审批意见邮件发送时间，ID: {}", appAgentApproveCommentId);
            } else {
                log.error("更新审批意见邮件发送时间失败，ID: {}", appAgentApproveCommentId);
            }

        } catch (Exception e) {
            log.error("更新审批意见邮件发送时间异常，ID: {}, 错误: {}", appAgentApproveCommentId, e.getMessage());
        }
    }

}
