package com.get.aisplatformcenterap.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("m_user")
public class UserInfoEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 来自平台类型：get_mso
     */
    @ApiModelProperty(value = "来自平台类型：get_mso")
    @Column(name = "fk_platform_type")
    private String fkPlatformType;
    /**
     * 登陆用户Id
     */
    @ApiModelProperty(value = "登陆用户Id")
    @Column(name = "login_id")
    private String loginId;
    /**
     * 登陆用户密码
     */
    @ApiModelProperty(value = "登陆用户密码")
    @Column(name = "login_ps")
    private String loginPs;
    /**
     * 会员编号
     */
    @ApiModelProperty(value = "会员编号")
    @Column(name = "num")
    private String num;
    /**
     * 姓名（中文）
     */
    @ApiModelProperty(value = "姓名（中文）")
    @Column(name = "name")
    private String name;
    /**
     * 姓名（英文）
     */
    @ApiModelProperty(value = "姓名（英文）")
    @Column(name = "name_en")
    private String nameEn;
    /**
     * 姓（拼音）
     */
    @ApiModelProperty(value = "姓（拼音）")
    @Column(name = "family_name_py")
    private String familyNamePy;
    /**
     * 名（拼音）
     */
    @ApiModelProperty(value = "名（拼音）")
    @Column(name = "first_name_py")
    private String firstNamePy;
    /**
     * 昵称
     */
    @ApiModelProperty(value = "昵称")
    @Column(name = "nickname")
    private String nickname;
    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男")
    @Column(name = "gender")
    private String gender;
    /**
     * 生日
     */
    @ApiModelProperty(value = "生日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "birthday")
    private Date birthday;
    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    @Column(name = "identity_card")
    private String identityCard;
    /**
     * 手机区号
     */
    @ApiModelProperty(value = "手机区号")
    @Column(name = "mobile_area_code")
    private String mobileAreaCode;
    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    @Column(name = "mobile")
    private String mobile;
    /**
     * Email
     */
    @ApiModelProperty(value = "Email")
    @Column(name = "email")
    private String email;
    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    @Column(name = "company")
    private String company;
    /**
     * 职位
     */
    @ApiModelProperty(value = "职位")
    @Column(name = "position")
    private String position;
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 州省Id
     */
    @ApiModelProperty(value = "州省Id")
    @Column(name = "fk_area_state_id")
    private Long fkAreaStateId;
    /**
     * 城市Id
     */
    @ApiModelProperty(value = "城市Id")
    @Column(name = "fk_area_city_id")
    private Long fkAreaCityId;
    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    @Column(name = "zip_code")
    private String zipCode;
    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    @Column(name = "address")
    private String address;
    /**
     * QQ号
     */
    @ApiModelProperty(value = "QQ号")
    @Column(name = "qq")
    private String qq;
    /**
     * 微信号
     */
    @ApiModelProperty(value = "微信号")
    @Column(name = "wechat")
    private String wechat;
    /**
     * whatsapp号
     */
    @ApiModelProperty(value = "whatsapp号")
    @Column(name = "whatsapp")
    private String whatsapp;
    /**
     * 微信昵称
     */
    @ApiModelProperty(value = "微信昵称")
    @Column(name = "wechat_nickname")
    private String wechatNickname;
    /**
     * 微信头像URL
     */
    @ApiModelProperty(value = "微信头像URL")
    @Column(name = "wechat_icon_url")
    private String wechatIconUrl;
    /**
     * 微信openid
     */
    @ApiModelProperty(value = "微信openid")
    @Column(name = "wechat_openid")
    private String wechatOpenid;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
}