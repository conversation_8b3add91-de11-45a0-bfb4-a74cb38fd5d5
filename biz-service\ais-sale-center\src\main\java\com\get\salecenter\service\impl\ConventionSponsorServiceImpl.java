package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.salecenter.dao.sale.ConventionSponsorMapper;
import com.get.salecenter.dao.sale.EventCostMapper;
import com.get.salecenter.vo.ConventionSponsorFeeVo;
import com.get.salecenter.vo.ConventionSponsorVo;
import com.get.salecenter.vo.SponsorExportVo;
import com.get.salecenter.entity.ConventionRegistration;
import com.get.salecenter.entity.ConventionSponsor;
import com.get.salecenter.entity.ConventionSponsorFee;
import com.get.salecenter.entity.ConventionSponsorSponsorFee;
import com.get.salecenter.entity.EventCost;
import com.get.salecenter.service.IConventionRegistrationService;
import com.get.salecenter.service.IConventionSponsorFeeService;
import com.get.salecenter.service.IConventionSponsorService;
import com.get.salecenter.service.IConventionSponsorSponsorFeeService;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.dto.ConventionRegistrationDto;
import com.get.salecenter.dto.ConventionSponsorDto;
import com.get.salecenter.dto.ConventionSponsorSponsorFeeDto;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2021/5/8 11:23
 * @verison: 1.0
 * @description:
 */
@Service
public class ConventionSponsorServiceImpl extends BaseServiceImpl<ConventionSponsorMapper, ConventionSponsor> implements IConventionSponsorService {
    @Resource
    private ConventionSponsorMapper conventionSponsorMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IConventionSponsorSponsorFeeService conventionSponsorSponsorFeeService;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private IConventionRegistrationService conventionRegistrationService;
    @Resource
    private IConventionSponsorFeeService conventionSponsorFeeService;
    @Resource
    private EventCostMapper eventCostMapper;

    @Override
    public ConventionSponsorVo findConventionSponsorById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ConventionSponsor conventionSponsor = conventionSponsorMapper.selectById(id);
        if (GeneralTool.isEmpty(conventionSponsor)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ConventionSponsorVo conventionSponsorVo = BeanCopyUtils.objClone(conventionSponsor, ConventionSponsorVo::new);
        conventionSponsorVo.setConventionSponsorFeeDtoList(conventionSponsorSponsorFeeService.getSponsorFeeDtoList(id));
        return conventionSponsorVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addConventionSponsor(ConventionSponsorDto conventionSponsorDto) {
        if (GeneralTool.isEmpty(conventionSponsorDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ConventionSponsor conventionSponsor = BeanCopyUtils.objClone(conventionSponsorDto, ConventionSponsor::new);
        //初始状态
        if (GeneralTool.isEmpty(conventionSponsorDto.getReceiptCode())) {
            //没有回执码得时候，自动获取，重复则继续获取，直到不重复
            boolean result = true;
            while (result) {
                String randomCode = MyStringUtils.getRandomCode();
                if (!validateReceiptCode(randomCode)) {
                    result = false;
                    conventionSponsor.setReceiptCode(randomCode);
                }
            }
        }


        validatedCurrencyNum(conventionSponsorDto);

        conventionSponsor.setStatus(0);
        utilService.updateUserInfoToEntity(conventionSponsor);
        conventionSponsorMapper.insertSelective(conventionSponsor);
        if (GeneralTool.isNotEmpty(conventionSponsorDto.getConventionSponsorFeeVoList())){
            //插入中间表数据
            List<ConventionSponsorSponsorFeeDto> newConventionSponsorFeeVoList = getNewSponsorFeeIds(conventionSponsorDto);
            insertTable(conventionSponsorDto, conventionSponsor.getId(), newConventionSponsorFeeVoList);
        }

        return conventionSponsor.getId();
    }

    private void validatedCurrencyNum(ConventionSponsorDto conventionSponsorDto) {
        if (GeneralTool.isNotEmpty(conventionSponsorDto.getFkCurrencyTypeNum())){
            if (GeneralTool.isNotEmpty(conventionSponsorDto.getConventionSponsorFeeVoList())){
                List<Long> fkConventionSponsorFeeIdList = conventionSponsorDto.getConventionSponsorFeeVoList().stream().map(ConventionSponsorSponsorFeeDto::getFkConventionSponsorFeeId).collect(Collectors.toList());
                List<ConventionSponsorFee> conventionSponsorFees = conventionSponsorFeeService.list(Wrappers.<ConventionSponsorFee>lambdaQuery()
                        .in(ConventionSponsorFee::getId, fkConventionSponsorFeeIdList));

                Set<String> numSet = conventionSponsorFees.stream().map(ConventionSponsorFee::getFkCurrencyTypeNum).filter(Objects::nonNull).collect(Collectors.toSet());
                if (numSet.size()!=1||!numSet.contains(conventionSponsorDto.getFkCurrencyTypeNum())){
                    //说明多个币种
                    throw new GetServiceException(LocaleMessageUtils.getMessage("currency_not_same"));
                }
            }
        }
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ConventionSponsor conventionSponsor = conventionSponsorMapper.selectById(id);
        if (conventionSponsor == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        conventionSponsorMapper.deleteById(id);
        //删除中间表
        //conventionRegistrationService.deleteByReceiptCode(conventionSponsor.getFkConventionId(), conventionSponsor.getReceiptCode());
        deleteTable(conventionSponsor);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ConventionSponsorVo updateConventionSponsor(ConventionSponsorDto conventionSponsorDto) {
        if (conventionSponsorDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ConventionSponsor result = conventionSponsorMapper.selectById(conventionSponsorDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        validatedCurrencyNum(conventionSponsorDto);
        ConventionSponsor conventionSponsor = BeanCopyUtils.objClone(conventionSponsorDto, ConventionSponsor::new);
        utilService.updateUserInfoToEntity(conventionSponsor);
        conventionSponsorMapper.updateById(conventionSponsor);
        if (GeneralTool.isNotEmpty(conventionSponsorDto.getConventionSponsorFeeVoList())){
            List<ConventionSponsorSponsorFeeDto> newConventionSponsorFeeVoList = getNewSponsorFeeIds(conventionSponsorDto);
            insertTable(conventionSponsorDto, conventionSponsor.getId(), newConventionSponsorFeeVoList);

        }else {
            //取消的话如果有数量 加回去  没有的话就不用管
            LambdaQueryWrapper<ConventionSponsorSponsorFee> lambdaQueryWrapper = Wrappers.<ConventionSponsorSponsorFee>lambdaQuery();
            lambdaQueryWrapper.eq(ConventionSponsorSponsorFee::getFkConventionSponsorId, conventionSponsorDto.getId());
//            List<ConventionSponsorSponsorFee> conventionSponsorSponsorFees = conventionSponsorSponsorFeeService.list(lambdaQueryWrapper);
//            if (GeneralTool.isNotEmpty(conventionSponsorSponsorFees)){
//                List<Long> feeIds = conventionSponsorSponsorFees.stream().map(ConventionSponsorSponsorFee::getFkConventionSponsorFeeId).collect(Collectors.toList());
//                //查看有数量的fee并+1更新
//                List<ConventionSponsorFee> conventionSponsorFeeList = conventionSponsorFeeService.list(Wrappers.<ConventionSponsorFee>lambdaQuery()
//                        .in(ConventionSponsorFee::getId, feeIds)
//                        .isNotNull(ConventionSponsorFee::getCountLimit));
//
//                if (GeneralTool.isNotEmpty(conventionSponsorFeeList)){
//                    for (ConventionSponsorFee conventionSponsorFee : conventionSponsorFeeList) {
//                        conventionSponsorFee.setCountLimit(conventionSponsorFee.getCountLimit()+1);
//                        utilService.setUpdateInfo(conventionSponsorFee);
//                    }
//                    conventionSponsorFeeService.updateBatchById(conventionSponsorFeeList);
//                }
//            }
            conventionSponsorSponsorFeeService.remove(lambdaQueryWrapper);
        }
        return findConventionSponsorById(conventionSponsor.getId());
    }

    @Override
    public void updateConventionSponsor2(ConventionSponsorDto conventionSponsorDto) {
        if (conventionSponsorDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        List<ConventionSponsor> conventionSponsors = getConventionSponsors(conventionSponsorDto.getFkConventionId(), conventionSponsorDto.getReceiptCode());
        if (conventionSponsors == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //遍历查看这些对象中是否有新建状态得对象，新建状态就表示要进行修改操作
        for (ConventionSponsor conventionSponsor : conventionSponsors) {
            if (0 == conventionSponsor.getStatus()) {
                //通过赞助商id查找赞助类型
                List<ConventionSponsorFeeVo> sponsorFeeDtoList = conventionSponsorSponsorFeeService.getSponsorFeeDtoList(conventionSponsor.getId());
                //获取已经选过得id集合
//                List<Long> sponsorFeeDtoIdList = sponsorFeeDtoList.stream().map(ConventionSponsorFeeVo::getId).collect(Collectors.toList());
                //获取要新增得赞助费
                List<ConventionSponsorSponsorFeeDto> newConventionSponsorFeeVoList = getNewSponsorFeeIds(conventionSponsorDto);
                //获取要新增得赞助费
                //结合在一起保存到中间表中
//                sponsorFeeDtoIdList.addAll(newSponsorFeeIds);
                conventionSponsorDto.setConventionSponsorFeeVoList(newConventionSponsorFeeVoList);
                utilService.updateUserInfoToEntity(conventionSponsor);
                conventionSponsorMapper.updateById(conventionSponsor);
                insertTable(conventionSponsorDto, conventionSponsor.getId(), newConventionSponsorFeeVoList);
                return;
            }
        }
        //如果遍历完上面还没有操作过，说明这里面没有新建状态得对象，要新增一条数据，内容、回执码和原来得一致，所选赞助类型不同而已（表示这些赞助是后面新增上去的）
        List<ConventionSponsorSponsorFeeDto> newConventionSponsorFeeVoList = getNewSponsorFeeIds(conventionSponsorDto);
        //如果没有新增得赞助 就不操作
        if (GeneralTool.isNotEmpty(newConventionSponsorFeeVoList)) {
            conventionSponsorDto.setConventionSponsorFeeVoList(newConventionSponsorFeeVoList);
            conventionSponsorDto.setId(null);
            addConventionSponsor(conventionSponsorDto);
        }
    }

    @Override
    public List<ConventionSponsorVo> getConventionSponsors(ConventionSponsorDto conventionSponsorDto, Page page) {
        LambdaQueryWrapper<ConventionSponsor> wrapper = getWrapper(conventionSponsorDto);
        IPage<ConventionSponsor> pages = conventionSponsorMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<ConventionSponsor> conventionSponsors = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<ConventionSponsorVo> convertDatas = getConvertDatas(conventionSponsors, conventionSponsorDto);

        Map<Long, EventCost> eventCostMap = Maps.newHashMap();
        List<Long> eventCostIds = convertDatas.stream().map(ConventionSponsorVo::getFkEventCostId).filter(Objects::nonNull).collect(Collectors.toList());
        Set<String> currencyTypeNums = Sets.newHashSet();
        if (GeneralTool.isNotEmpty(eventCostIds)){
            List<EventCost> eventCosts = eventCostMapper.selectBatchIds(eventCostIds);
            if (GeneralTool.isNotEmpty(eventCosts)) {
                eventCostMap = eventCosts.stream().collect(Collectors.toMap(EventCost::getId, Function.identity()));
                currencyTypeNums = eventCosts.stream().map(EventCost::getFkCurrencyTypeNum).filter(Objects::nonNull).collect(Collectors.toSet());
            }
        }
        Map<String, String> numNameMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(currencyTypeNums)){
            numNameMap = financeCenterClient.getCurrencyNamesByNums(currencyTypeNums).getData();
        }

        for (ConventionSponsorVo convertData : convertDatas) {
            EventCost eventCost = eventCostMap.get(convertData.getFkEventCostId());
            if (GeneralTool.isNotEmpty(eventCost)){
                convertData.setEventCostAmount(eventCost.getAmount());
                convertData.setEventCostAmountCurrencyNum(eventCost.getFkCurrencyTypeNum());
                convertData.setEventCostAmountCurrencyNumName(numNameMap.get(eventCost.getFkCurrencyTypeNum()));
            }
        }
        return convertDatas;
    }

    public List<ConventionSponsorVo> getConvertDatas(List<ConventionSponsor> conventionSponsors, ConventionSponsorDto conventionSponsorDto){
        Set<String> receiptCodes = conventionSponsors.stream().filter(c -> GeneralTool.isNotEmpty(c.getReceiptCode())).map(ConventionSponsor::getReceiptCode).collect(Collectors.toSet());
        if (GeneralTool.isEmpty(receiptCodes)){
            receiptCodes.add(" ");
        }

        ConventionRegistrationDto conventionRegistrationDto = new ConventionRegistrationDto();
        conventionRegistrationDto.setFkConventionId(conventionSponsorDto.getFkConventionId());
        conventionRegistrationDto.setReceiptCodes(receiptCodes);
        List<ConventionRegistration> conventionRegistrations = conventionRegistrationService.getConventionRegistrationsByVo(conventionRegistrationDto);
        Map<String, String> providerNameMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(conventionRegistrations)){
            providerNameMap = conventionRegistrations.stream().collect(HashMap::new, (m, v) -> m.put(v.getReceiptCode(), v.getProviderName()), HashMap::putAll);
        }

        List<ConventionSponsorVo> convertDatas = new ArrayList<>();
        //学校提供商id集合
        Set<Long> institutionProviderIds = new HashSet<>();
        //币种编号
        Set<String> currencyTypeNums = new HashSet<>();
        //获取各自的值
        Set<String> numSet = conventionSponsors.stream().map(ConventionSponsor::getFkCurrencyTypeNum).filter(Objects::nonNull).collect(Collectors.toSet());
        currencyTypeNums.addAll(numSet);
        for (ConventionSponsor conventionSponsor : conventionSponsors) {
            institutionProviderIds.add(conventionSponsor.getFkInstitutionProviderId());
            List<ConventionSponsorFeeVo> sponsorFeeDtoList = conventionSponsorSponsorFeeService.getSponsorFeeDtoList(conventionSponsor.getId());
            sponsorFeeDtoList.removeIf(Objects::isNull);
            //TODO 改过
            //currencyTypeNums.addAll(sponsorFeeDtoList.stream().map(ConventionSponsorFee::getFkCurrencyTypeNum).collect(Collectors.toSet()));
            currencyTypeNums.addAll(sponsorFeeDtoList.stream().map(ConventionSponsorFeeVo::getFkCurrencyTypeNum).collect(Collectors.toSet()));
        }
        //学校提供商map
        institutionProviderIds.removeIf(Objects::isNull);
        Map<Long, String> institutionProviderNameMap = new HashMap<>();
        Result<Map<Long, String>> institutionProviderNameResult = institutionCenterClient.getInstitutionProviderNamesByIds(institutionProviderIds);
        if (institutionProviderNameResult.isSuccess() && GeneralTool.isNotEmpty(institutionProviderNameResult.getData())) {
            institutionProviderNameMap = institutionProviderNameResult.getData();
        }
        //币种编号map
        currencyTypeNums.removeIf(Objects::isNull);
        Map<String, String> currencyNameMap = new HashMap<>();
        Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNums);
        if (result.isSuccess() && result.getData() != null) {
            currencyNameMap = result.getData();
        }

        //赞助类型总数
        Integer total = conventionSponsorFeeService.getTotal(conventionSponsorDto.getFkConventionId());
        for (ConventionSponsor conventionSponsor : conventionSponsors) {
            ConventionSponsorVo conventionSponsorVo = BeanCopyUtils.objClone(conventionSponsor, ConventionSponsorVo::new);
            List<ConventionSponsorFeeVo> sponsorFeeDtoList = conventionSponsorSponsorFeeService.getSponsorFeeDtoList(conventionSponsor.getId());
            BigDecimal sumFeeCny = new BigDecimal(0);
            sponsorFeeDtoList.removeIf(Objects::isNull);

            String typeName = "";
            for (ConventionSponsorFeeVo conventionSponsorFeeVo : sponsorFeeDtoList) {
                conventionSponsorFeeVo.setCurrencyName(currencyNameMap.get(conventionSponsorFeeVo.getFkCurrencyTypeNum()));
//                if (GeneralTool.isNotEmpty(conventionSponsorFeeVo.getFeeCny())) {
//                    sumFeeCny = sumFeeCny.add(conventionSponsorFeeVo.getFeeCny());
//                }
                //用于导出，赞助费用类型
                if (typeName.equals("")){
                    typeName = conventionSponsorFeeVo.getTypeName()+":"+ conventionSponsorFeeVo.getFee().toString() + currencyNameMap.get(conventionSponsorFeeVo.getFkCurrencyTypeNum());
                }else{
                    typeName =
                            typeName+"\n" + conventionSponsorFeeVo.getTypeName()+":"+ conventionSponsorFeeVo.getFee().toString() + currencyNameMap.get(conventionSponsorFeeVo.getFkCurrencyTypeNum());
                }

            }

            //赞助类型
            conventionSponsorVo.setConventionSponsorFeeDtoList(sponsorFeeDtoList);
            //总费用金额
            BigDecimal sumFee = BigDecimal.ZERO;
            sumFee = sponsorFeeDtoList.stream().map(ConventionSponsorFeeVo::getFee).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (GeneralTool.isNotEmpty(conventionSponsor.getFeeOther())){
                sumFee = sumFee.add(conventionSponsor.getFeeOther());
            }
            if (sumFee.compareTo(BigDecimal.ZERO)!=0){
                conventionSponsorVo.setSumFee(sumFee.setScale(2, RoundingMode.HALF_UP));
            }
            conventionSponsorVo.setTypeName(typeName);
            //总费用币种
            conventionSponsorVo.setFkCurrencyTypeNumFee(currencyNameMap.get(conventionSponsorVo.getFkCurrencyTypeNum()));
            if (GeneralTool.isNotEmpty(sponsorFeeDtoList)&&GeneralTool.isEmpty(conventionSponsorVo.getFkCurrencyTypeNumFee())){
                conventionSponsorVo.setFkCurrencyTypeNumFee(sponsorFeeDtoList.get(0).getCurrencyName());
            }
            //赞助合计折合人民币,拿其他赞助金额币种或者赞助费用币种
            BigDecimal exchangeRateCNY = BigDecimal.ZERO;
            if (GeneralTool.isNotEmpty(conventionSponsor.getFkCurrencyTypeNum())){
                Result<BigDecimal> exchangeRate = financeCenterClient.getLastExchangeRate(true,conventionSponsor.getFkCurrencyTypeNum(), "CNY");
                exchangeRateCNY = exchangeRate.getData();
            }
            if (GeneralTool.isNotEmpty(sponsorFeeDtoList) && GeneralTool.isNotEmpty(sponsorFeeDtoList.get(0).getFkCurrencyTypeNum())){
                Result<BigDecimal> exchangeRate = financeCenterClient.getLastExchangeRate(true,sponsorFeeDtoList.get(0).getFkCurrencyTypeNum(), "CNY");
                exchangeRateCNY  = exchangeRate.getData();
            }
            if (exchangeRateCNY.compareTo(BigDecimal.ZERO)!= 0){
                conventionSponsorVo.setSumFeeCny(sumFee.multiply(exchangeRateCNY).setScale(2, RoundingMode.HALF_UP));
            }
            if (GeneralTool.isNotEmpty(sponsorFeeDtoList)) {
                //已选赞助类型数
                conventionSponsorVo.setSelectNum(sponsorFeeDtoList.size());
            }
            //提供商名称
            if (GeneralTool.isNotEmpty(institutionProviderNameMap)) {
                conventionSponsorVo.setInstitutionProviderName(institutionProviderNameMap.get(conventionSponsorVo.getFkInstitutionProviderId()));
            }
            //赞助类型总数
            conventionSponsorVo.setTotal(total);

            if (GeneralTool.isNotEmpty(providerNameMap)&&GeneralTool.isNotEmpty(providerNameMap.get(conventionSponsorVo.getReceiptCode()))){
                conventionSponsorVo.setProviderName(providerNameMap.get(conventionSponsorVo.getReceiptCode()));
            }

            if (GeneralTool.isNotEmpty(conventionSponsorVo.getFkCurrencyTypeNum())){
                if (GeneralTool.isNotEmpty(currencyNameMap)&&GeneralTool.isNotEmpty(currencyNameMap.get(conventionSponsorVo.getFkCurrencyTypeNum()))){
                    conventionSponsorVo.setCurrencyTypeName(currencyNameMap.get(conventionSponsorVo.getFkCurrencyTypeNum()));
                    if (GeneralTool.isNotEmpty(conventionSponsorVo.getFeeOther())){
                        conventionSponsorVo.setFeeOtherCurrency(conventionSponsorVo.getFeeOther()+ conventionSponsorVo.getCurrencyTypeName());
                    }
                }
            }
            if (GeneralTool.isNotEmpty(conventionSponsorDto.getStatus())) {
                String statusName = ProjectExtraEnum.getInitialValueByKey(conventionSponsorDto.getStatus(), ProjectExtraEnum.CONVENTION_STATUS);
                conventionSponsorDto.setStatusName(statusName);
            }

            convertDatas.add(conventionSponsorVo);


        }
        return convertDatas;
    }

    @Override
    public List<ConventionSponsorVo> getConventionSponsorFeeDtos(Long conventionId, String receiptCode) {
        List<ConventionSponsorVo> list = new ArrayList<>();
        List<ConventionSponsor> conventionSponsors = getConventionSponsors(conventionId, receiptCode);
        for (ConventionSponsor conventionSponsor : conventionSponsors) {
            ConventionSponsorVo conventionSponsorVo = BeanCopyUtils.objClone(conventionSponsor, ConventionSponsorVo::new);
            List<ConventionSponsorFeeVo> sponsorFeeDtoList = conventionSponsorSponsorFeeService.getSponsorFeeDtoList(conventionSponsor.getId());
            conventionSponsorVo.setConventionSponsorFeeDtoList(sponsorFeeDtoList);
            list.add(conventionSponsorVo);
        }
        return list;
    }

    @Override
    public List<Map<String, List<ConventionSponsorFeeVo>>> getSponsorshipConfig(Long conventionId) {
        return conventionSponsorFeeService.getSponsorshipFee(conventionId);
    }

    @Override
    public Boolean soldOut(Long sponsorFeeId, Integer initNum) {
        return conventionSponsorSponsorFeeService.soldOut(sponsorFeeId, initNum, null);
    }

    @Override
    public void updateStatus(Long id, Integer status) {
        ConventionSponsor conventionSponsor = conventionSponsorMapper.selectById(id);
        if (GeneralTool.isNotEmpty(conventionSponsor)) {
            conventionSponsor.setStatus(status);
            utilService.updateUserInfoToEntity(conventionSponsor);
            conventionSponsorMapper.updateById(conventionSponsor);
        }
    }

    @Override
    public Boolean validateReceiptCode(String receiptCode) {
//        Example example = new Example(ConventionSponsor.class);
//        example.createCriteria().andEqualTo("receiptCode", receiptCode);
//        List<ConventionSponsor> conventionRegistrations = conventionSponsorMapper.selectByExample(example);
        List<ConventionSponsor> conventionRegistrations = conventionSponsorMapper.selectList(Wrappers.<ConventionSponsor>lambdaQuery().eq(ConventionSponsor::getReceiptCode, receiptCode));
        //能查到说明数据库已有这个回执码
        if (GeneralTool.isEmpty(conventionRegistrations)) {
            return false;
        }
        return true;
    }

    @Override
    public void exportSponsorExcel(HttpServletResponse response, ConventionSponsorDto conventionSponsorDto) {
        LambdaQueryWrapper<ConventionSponsor> lambdaQueryWrapper = getWrapper(conventionSponsorDto);
        List<ConventionSponsor> conventionSponsors = conventionSponsorMapper.selectList(lambdaQueryWrapper);
        List<ConventionSponsorVo> convertDatas = getConvertDatas(conventionSponsors, conventionSponsorDto);
        List<SponsorExportVo> sponsorExportVos = BeanCopyUtils.copyListProperties(convertDatas, SponsorExportVo::new);
        FileUtils.exportExcelNotWrapText(response, sponsorExportVos, "Sponsor", SponsorExportVo.class);

    }

    @Override
    public ConventionSponsor getConventionSponsorById(Long conventionRegistrationId) {
        return conventionSponsorMapper.selectById(conventionRegistrationId);
    }

    @Override
    public void updateConventionSponsorById(ConventionSponsor conventionSponsor) {
        boolean b = updateById(conventionSponsor);
        if (!b){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
    }

    @Override
    public List<ConventionSponsor> getConventionSponsorsByVo(ConventionSponsorDto conventionSponsorDto) {
        LambdaQueryWrapper<ConventionSponsor> lambdaQueryWrapper = Wrappers.lambdaQuery();

        if (GeneralTool.isNotEmpty(conventionSponsorDto.getFkEventCostId())){
            lambdaQueryWrapper.eq(ConventionSponsor::getFkEventCostId, conventionSponsorDto.getFkEventCostId());
        }
        List<ConventionSponsor> conventionSponsors = conventionSponsorMapper.selectList(lambdaQueryWrapper);
        return conventionSponsors;
    }

    @Override
    public void updateWithNullConventionSponsorById(ConventionSponsor conventionSponsor) {
        conventionSponsorMapper.updateByIdWithNull(conventionSponsor);
    }

    /**
     * @return void
     * @Description :插入中间表数据
     * @Param [conventionSponsorDto]
     * <AUTHOR>
     */
    private void insertTable(ConventionSponsorDto conventionSponsorDto, Long id, List<ConventionSponsorSponsorFeeDto> conventionSponsorFeeVoList) {
        //要新增到中间表得数据集合
        List<ConventionSponsorSponsorFeeDto> conventionSponsorSponsorFeeDtos = conventionSponsorFeeVoList;
//        for (Long sponsorFeeId : conventionSponsorDto.getConventionSponsorFeeIdList()) {
//            ConventionSponsorSponsorFeeDto conventionSponsorSponsorFeeVo = new ConventionSponsorSponsorFeeDto();
//            conventionSponsorSponsorFeeVo.setFkConventionSponsorId(id);
//            conventionSponsorSponsorFeeVo.setFkConventionSponsorFeeId(sponsorFeeId);
//            conventionSponsorSponsorFeeDtos.add(conventionSponsorSponsorFeeVo);
//        }
        for (ConventionSponsorSponsorFeeDto conventionSponsorSponsorFeeDto : conventionSponsorSponsorFeeDtos) {
            conventionSponsorSponsorFeeDto.setFkConventionSponsorId(id);
        }
        //验证新增得是否售完
        conventionSponsorSponsorFeeService.validateAdd(conventionSponsorFeeVoList.stream().map(ConventionSponsorSponsorFeeDto::getFkConventionSponsorFeeId).collect(Collectors.toList()), id);
        //没问题就新增
        if (GeneralTool.isNotEmpty(conventionSponsorSponsorFeeDtos)) {
            conventionSponsorSponsorFeeService.batchAdd(conventionSponsorSponsorFeeDtos);
        }
    }

    private List<ConventionSponsorSponsorFeeDto> getNewSponsorFeeIds(ConventionSponsorDto conventionSponsorDto) {
        //通过赞助商id查找对应赞助类型对象集合
        List<ConventionSponsorVo> conventionSponsorVos = getConventionSponsorFeeDtos(conventionSponsorDto.getFkConventionId(), conventionSponsorDto.getReceiptCode());
        List<ConventionSponsorFeeVo> sponsorFeeDtos = new ArrayList<>();
        for (ConventionSponsorVo conventionSponsorVo : conventionSponsorVos) {
            sponsorFeeDtos.addAll(conventionSponsorVo.getConventionSponsorFeeDtoList());
        }
        //获取赞助类型id，这些是之前选过的
        List<Long> oldSponsorFeeIds = sponsorFeeDtos.stream().map(ConventionSponsorFeeVo::getId).collect(Collectors.toList());
        List<Long> oldTmp = new ArrayList<>(oldSponsorFeeIds);
        //和现在要新增得比对，看看哪些是要最新过来得
        List<Long> newSponsorFeeIds = conventionSponsorDto.getConventionSponsorFeeVoList().stream().map(ConventionSponsorSponsorFeeDto::getFkConventionSponsorFeeId).collect(Collectors.toList());
        //传来的去除旧的  就是要新增得内容
        newSponsorFeeIds.removeAll(oldSponsorFeeIds);
        List<ConventionSponsorSponsorFeeDto> conventionSponsorFeeVoList = new ArrayList<>();
        for (ConventionSponsorSponsorFeeDto conventionSponsorSponsorFeeDto : conventionSponsorDto.getConventionSponsorFeeVoList()) {
            for (Long newSponsorFeeId : newSponsorFeeIds) {
                if (newSponsorFeeId.equals(conventionSponsorSponsorFeeDto.getFkConventionSponsorFeeId())) {
                    conventionSponsorFeeVoList.add(conventionSponsorSponsorFeeDto);
                }
            }
        }
        return conventionSponsorFeeVoList;
    }

    /**
     * @return void
     * @Description :删除中间表
     * @Param [conventionSponsor]
     * <AUTHOR>
     */
    private void deleteTable(ConventionSponsor conventionSponsor) {
        conventionSponsorSponsorFeeService.deleteByFkid(conventionSponsor.getId());
    }

    /**
     * @return java.util.List<com.get.salecenter.entity.ConventionSponsor>
     * @Description :通过峰会id和回执码查找赞助商对象集合
     * @Param [conventionId, receiptCode]
     * <AUTHOR>
     */
    private List<ConventionSponsor> getConventionSponsors(Long conventionId, String receiptCode) {
//        Example example = new Example(ConventionSponsor.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionId", conventionId);
//        criteria.andEqualTo("receiptCode", receiptCode);
//        return conventionSponsorMapper.selectByExample(example);
        return conventionSponsorMapper.selectList(Wrappers.<ConventionSponsor>lambdaQuery().eq(ConventionSponsor::getFkConventionId, conventionId).eq(ConventionSponsor::getReceiptCode, receiptCode));
    }

    /**
     * @return tk.mybatis.mapper.entity.Example
     * @Description :设置好example查询条件
     * @Param [conventionSponsorDto]
     * <AUTHOR>
     */
    private LambdaQueryWrapper<ConventionSponsor> getWrapper(ConventionSponsorDto conventionSponsorDto) {
        LambdaQueryWrapper<ConventionSponsor> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ConventionSponsor::getFkConventionId, conventionSponsorDto.getFkConventionId());
        if (GeneralTool.isNotEmpty(conventionSponsorDto)) {
            //查询条件-回执码
            if (GeneralTool.isNotEmpty(conventionSponsorDto.getReceiptCode())) {
                wrapper.like(ConventionSponsor::getReceiptCode, conventionSponsorDto.getReceiptCode());
            }
            //查询条件-赞助商名称
            if (GeneralTool.isNotEmpty(conventionSponsorDto.getSponsorName())) {
                wrapper.like(ConventionSponsor::getSponsorName, conventionSponsorDto.getSponsorName());

            }
            //查询条件-学校提供商名称
            if (GeneralTool.isNotEmpty(conventionSponsorDto.getInstitutionProviderName())) {
                //通过学校提供商名称模糊查询符合的学校提供商ids
//                List<Long> institutionProviderIds = institutionCenterClient.getInstitutionProviderIdsByName(conventionSponsorDto.getInstitutionProviderName());
                Result<List<Long>> institutionProviderResult = institutionCenterClient.getInstitutionProviderIdsByName(conventionSponsorDto.getInstitutionProviderName());
                if (institutionProviderResult.isSuccess() && GeneralTool.isNotEmpty(institutionProviderResult.getData())) {
                    wrapper.in(ConventionSponsor::getFkInstitutionProviderId, institutionProviderResult.getData());
                }
            }
            //查询条件-状态
            if (GeneralTool.isNotEmpty(conventionSponsorDto.getStatus())) {
                wrapper.eq(ConventionSponsor::getStatus, conventionSponsorDto.getStatus());
            }
        }
        wrapper.orderByDesc(ConventionSponsor::getGmtCreate);
        return wrapper;
    }
}
