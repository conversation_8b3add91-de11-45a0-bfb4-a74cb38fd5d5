package com.get.salecenter.dao.sale;

import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.SelItem;
import com.get.salecenter.vo.StudentOfferItemDeferEntranceTimeVo;
import com.get.salecenter.entity.StudentOfferItemDeferEntranceTime;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2021/6/16
 * @TIME: 12:34
 * @Description:
 **/
@Mapper
public interface StudentOfferItemDeferEntranceTimeMapper extends GetMapper<StudentOfferItemDeferEntranceTime> {
    int insert(StudentOfferItemDeferEntranceTime studentOfferItemDeferEntranceTime);

    /**
     * 延迟入学状态统计
     *
     * @Date 11:11 2021/7/22
     * <AUTHOR>
     */
    StudentOfferItemDeferEntranceTimeVo getStudentsFailureState(@Param("companyId") Long companyId,
                                                                @Param("studentIds") List<Long> studentIdList,
                                                                @Param("userNames") List<String> userNames,
                                                                @Param("beginTime") Date beginTime,
                                                                @Param("endTime") Date endTime,
                                                                @Param("staffId") Long staffId,
                                                                @Param("areaCountryIds") List<Long> areaCountryIds,
                                                                @Param("countryIds") List<Long> countryIds);


    /**
     * 根据计划ID获取
     *
     * @param fkStudentOfferItemId
     * @return
     */
    List<Date> getDeferEntranceByItemId(@Param("fkStudentOfferItemId") Long fkStudentOfferItemId);


    /**
     * 根据计划ID获取
     *
     * @param fkStudentOfferItemId
     * @return
     */
    Date getLastDeferEntranceByItemId(@Param("fkStudentOfferItemId") Long fkStudentOfferItemId);

    List<StudentOfferItemDeferEntranceTime> getAllDeferEntranceByItemIds(@Param("fkStudentOfferItemIds") Set<Long> fkStudentOfferItemIds);

    List<SelItem> getLastDeferEntranceByItemIds(@Param("fkStudentOfferItemIds") Set<Long> fkStudentOfferItemIds);
}
