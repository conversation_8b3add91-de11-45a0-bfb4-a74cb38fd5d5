package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/5/9 18:15
 * @verison: 1.0
 * @description:
 */
@Data
public class EventBillListDto {

    //---------列表查询条件----------

    @ApiModelProperty("公司id")
    private Long fkCompanyId;
    @ApiModelProperty("公司ids")
    private List<Long> fkCompanyIdList;

    @ApiModelProperty("提供商名称")
    private String institutionProviderName;

    @ApiModelProperty("业务国家(多选）")
    private List<Long> fkAreaCountryIdList;

//    @ApiModelProperty("摘要、备注")
//    private String summaryKeyWord;

    @ApiModelProperty("发票号")
    private String fkInvoiceNum;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty(value = "活动年份")
    private Integer eventYear;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("活动摘要(多选）")
    private List<Long> fkEventSummaryIdList;

    @ApiModelProperty("分配余额状态：未分配/已经分配/完成分配：0/1/2")
    private Integer allocationStatus;

    @ApiModelProperty("创建人")
    private String gmtCreateUser;

    @ApiModelProperty("是否创建了发票,0/否，1/是")
    private Boolean isCreateInvoice;
}
