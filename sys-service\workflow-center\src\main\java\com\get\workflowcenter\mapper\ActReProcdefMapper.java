package com.get.workflowcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.log.model.LogLogin;
import com.get.workflowcenter.vo.ActReProcdefVo;
import com.get.workflowcenter.entity.ActReProcdef;
import com.get.workflowcenter.dto.ActReProcdefDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ActReProcdefMapper extends BaseMapper<ActReProcdef> {

    int insertSelective(ActReProcdef record);

    List<ActReProcdefVo> getProcefList(IPage<LogLogin> page, @Param("procdefVo") ActReProcdefDto procdefVo);
}