package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.InstitutionProviderInstitutionChannel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstitutionProviderInstitutionChannelMapper extends BaseMapper<InstitutionProviderInstitutionChannel> {
    int insert(InstitutionProviderInstitutionChannel record);

    int insertSelective(InstitutionProviderInstitutionChannel record);

    int updateByPrimaryKeySelective(InstitutionProviderInstitutionChannel record);

    int updateByPrimaryKey(InstitutionProviderInstitutionChannel record);

    /**
     * 获取渠道名称
     *
     * @param id
     * @return
     */
    String getInstitutionChannelNamesById(@Param("id") Long id,
                                          @Param("companyIds") List<Long> companyIds);
}