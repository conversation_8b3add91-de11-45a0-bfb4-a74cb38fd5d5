package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: Sea
 * @create: 2021/3/29 11:34
 * @verison: 1.0
 * @description:
 */
@Data
public class ContractFormulaAreaCountryStudentDto extends BaseVoEntity {
    /**
     * 合同公式Id
     */
    @ApiModelProperty(value = "合同公式Id")
    private Long fkContractFormulaId;

    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;
}
