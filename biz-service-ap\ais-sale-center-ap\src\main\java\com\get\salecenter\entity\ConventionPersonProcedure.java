package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_convention_person_procedure")
public class ConventionPersonProcedure extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 峰会参展人员Id
     */
    @ApiModelProperty(value = "峰会参展人员Id")
    @Column(name = "fk_convention_person_id")
    private Long fkConventionPersonId;
    /**
     * 峰会流程Id
     */
    @ApiModelProperty(value = "峰会流程Id")
    @Column(name = "fk_convention_procedure_id")
    private Long fkConventionProcedureId;
}