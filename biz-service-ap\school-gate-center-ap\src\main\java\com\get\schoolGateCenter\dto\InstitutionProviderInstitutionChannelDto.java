package com.get.schoolGateCenter.dto;
import com.get.schoolGateCenter.entity.InstitutionProviderInstitutionChannel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Hardy
 * @create: 2021/9/22 11:23
 * @verison: 1.0
 * @description:
 */
@Data
public class InstitutionProviderInstitutionChannelDto extends InstitutionProviderInstitutionChannel {

    /**
     * 学校提供商名称
     */
    @ApiModelProperty(value = "学校提供商名称")
    private String providerName;

    @ApiModelProperty(value = "提供商名称")
    private String name;

    /**
     * 渠道名称
     */
    @ApiModelProperty("渠道名称")
    private String channelName;

    @ApiModelProperty(value = "学校提供商中文名称")
    private String providerNameChn;

    @ApiModelProperty(value = "渠道中文名称")
    private String channelNameChn;

    /**
     * 渠道和学校提供商名称
     */
    @ApiModelProperty(value = "渠道和学校提供商名称")
    private String channelProviderName;
}
