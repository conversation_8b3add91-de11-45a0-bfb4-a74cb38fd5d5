package com.get.pmpcenter.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/4/27
 * @Version 1.0
 * @apiNote:日志记录d
 */
@Data
public class LogRecordDto {

    @ApiModelProperty(value = "合同佣金方案id")
    @NotNull(message = "合同佣金方案id不能为空")
    private Long providerCommissionPlanId;

    @ApiModelProperty(value = "代理佣金方案id")
    private Long agentCommissionPlanId;

    @ApiModelProperty(value = "类型:1-合同佣金方案,2-代理佣金方案")
    private Integer type;
}
