package com.get.salecenter.vo;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @author: Hardy
 * @create: 2023/3/8 10:19
 * @verison: 1.0
 * @description:
 */
@Data
public class StaffCommissionStatisticsDateAndAmountVo {

    /**
     * 结算日期 可多个
     *
     * eg：
     * [
     * {
     *     "value":"202301",
     *     "type":0  //0未结算1已结算
     *  },
     * {
     *     "value":"202301",
     *     "type":1  //0未结算1已结算
     *  },
     * ]
     *
     */
    private List<Map<String,Object>> settlementDates;

    /**
     * 结算金额 可多个
     *
     * eg：
     * [
     * {
     *     "value":500.00,
     *     "type":0  //0未结算1已结算
     *  },
     * {
     *     "value":200.00,
     *     "type":1  //0未结算1已结算
     *  },
     * ]
     *
     */
    private List<Map<String,Object>> settlementAmounts;

}
