package com.get.schoolGateCenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.schoolGateCenter.entity.InstitutionDeadlineInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/6 15:35
 */
@Data
public class InstitutionDeadlineInfoDto extends InstitutionDeadlineInfo {

    @ApiModelProperty("学校名")
    private String fkInstitutionName;

    @ApiModelProperty("公开对象名称")
    private String publicLevelName;

    @ApiModelProperty("学校封面url")
    private String coversUrl;

    @ApiModelProperty("学校中文名")
    private String fkInstitutionNameZh;

    @ApiModelProperty("学校英文名")
    private String fkInstitutionNameEn;

    @ApiModelProperty("最新创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date newGmtCreate;
}
