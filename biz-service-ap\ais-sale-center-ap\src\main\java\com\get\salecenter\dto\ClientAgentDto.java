package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class ClientAgentDto {

    @ApiModelProperty(value = "代理id")
    @NotNull(message = "fkAgentId is not null")
    private Long fkAgentId;

    @ApiModelProperty(value = "客户id")
    @NotNull(message = "fkClientId is not null")
    private Long fkClientId;

    @ApiModelProperty(value = "是否激活：0否/1是")
    @NotNull(message = "isActive is not null")
    private Boolean isActive;
}
