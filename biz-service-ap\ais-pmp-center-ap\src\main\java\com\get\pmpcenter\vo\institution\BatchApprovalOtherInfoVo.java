package com.get.pmpcenter.vo.institution;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.pmpcenter.dto.institution.PlanTerritoryDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/5/30
 * @Version 1.0
 * @apiNote:批量提交除去批量审核之外的方案信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchApprovalOtherInfoVo {

    @ApiModelProperty(value = "佣金方案下的适用国家/区域规则说明-前端不用传/回显也不用管这个字段")
    private List<PlanTerritoryDto> planTerritoryList;

    @ApiModelProperty(value = "方案有效时间（开始）")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "方案有效时间（结束）")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "有效时间是否无时间限制：0否/1是")
    private Integer isTimeless;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "Territory特殊说明-英文")
    private String territory;

    @ApiModelProperty(value = "方案摘要（用户内部查看）-英文")
    private String summary;
}
