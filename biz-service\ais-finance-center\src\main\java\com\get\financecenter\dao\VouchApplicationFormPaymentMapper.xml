<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.VouchApplicationFormPaymentMapper">

    <select id="getVouchApplicationFormPaymentDatas"
            resultType="com.get.financecenter.vo.VouchApplicationFormPaymentVo">
        SELECT mvafp.*,
               CASE
                   WHEN IFNULL(uct.type_name, '') = '' THEN
                       uct.num ELSE CONCAT(uct.num, '（', uct.type_name, '）')
                   END AS currencyName
        FROM m_vouch_application_form_payment AS mvafp
        LEFT JOIN u_currency_type AS uct ON uct.num = mvafp.fk_currency_type_num
        <where>
            mvafp.fk_table_name = #{vouchApplicationFormPaymentQueryDto.fkTableName}
            AND mvafp.fk_table_id = #{vouchApplicationFormPaymentQueryDto.fkTableId}
        </where>
        order by mvafp.status desc, mvafp.gmt_create desc
    </select>

</mapper>