<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.workflowcenter.mapper.ActHiTaskInstMapper">
    <resultMap id="BaseResultMap" type="com.get.workflowcenter.entity.ActHiTaskInst">
        <id column="ID_" jdbcType="VARCHAR" property="id"/>
        <result column="PROC_DEF_ID_" jdbcType="VARCHAR" property="procDefId"/>
        <result column="TASK_DEF_KEY_" jdbcType="VARCHAR" property="taskDefKey"/>
        <result column="PROC_INST_ID_" jdbcType="VARCHAR" property="procInstId"/>
        <result column="EXECUTION_ID_" jdbcType="VARCHAR" property="executionId"/>
        <result column="NAME_" jdbcType="VARCHAR" property="name"/>
        <result column="PARENT_TASK_ID_" jdbcType="VARCHAR" property="parentTaskId"/>
        <result column="DESCRIPTION_" jdbcType="VARCHAR" property="description"/>
        <result column="OWNER_" jdbcType="VARCHAR" property="owner"/>
        <result column="ASSIGNEE_" jdbcType="VARCHAR" property="assignee"/>
        <result column="START_TIME_" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="CLAIM_TIME_" jdbcType="TIMESTAMP" property="claimTime"/>
        <result column="END_TIME_" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="DURATION_" jdbcType="BIGINT" property="duration"/>
        <result column="DELETE_REASON_" jdbcType="VARCHAR" property="deleteReason"/>
        <result column="PRIORITY_" jdbcType="INTEGER" property="priority"/>
        <result column="DUE_DATE_" jdbcType="TIMESTAMP" property="dueDate"/>
        <result column="FORM_KEY_" jdbcType="VARCHAR" property="formKey"/>
        <result column="CATEGORY_" jdbcType="VARCHAR" property="category"/>
        <result column="TENANT_ID_" jdbcType="VARCHAR" property="tenantId"/>
    </resultMap>
    <sql id="Base_Column_List">
    ID_, PROC_DEF_ID_, TASK_DEF_KEY_, PROC_INST_ID_, EXECUTION_ID_, NAME_, PARENT_TASK_ID_, 
    DESCRIPTION_, OWNER_, ASSIGNEE_, START_TIME_, CLAIM_TIME_, END_TIME_, DURATION_, 
    DELETE_REASON_, PRIORITY_, DUE_DATE_, FORM_KEY_, CATEGORY_, TENANT_ID_
  </sql>

    <insert id="insertSelective" parameterType="com.get.workflowcenter.entity.ActHiTaskInst" useGeneratedKeys="true"
            keyProperty="id">
        insert into act_hi_taskinst
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID_,
            </if>
            <if test="procDefId != null">
                PROC_DEF_ID_,
            </if>
            <if test="taskDefKey != null">
                TASK_DEF_KEY_,
            </if>
            <if test="procInstId != null">
                PROC_INST_ID_,
            </if>
            <if test="executionId != null">
                EXECUTION_ID_,
            </if>
            <if test="name != null">
                NAME_,
            </if>
            <if test="parentTaskId != null">
                PARENT_TASK_ID_,
            </if>
            <if test="description != null">
                DESCRIPTION_,
            </if>
            <if test="owner != null">
                OWNER_,
            </if>
            <if test="assignee != null">
                ASSIGNEE_,
            </if>
            <if test="startTime != null">
                START_TIME_,
            </if>
            <if test="claimTime != null">
                CLAIM_TIME_,
            </if>
            <if test="endTime != null">
                END_TIME_,
            </if>
            <if test="duration != null">
                DURATION_,
            </if>
            <if test="deleteReason != null">
                DELETE_REASON_,
            </if>
            <if test="priority != null">
                PRIORITY_,
            </if>
            <if test="dueDate != null">
                DUE_DATE_,
            </if>
            <if test="formKey != null">
                FORM_KEY_,
            </if>
            <if test="category != null">
                CATEGORY_,
            </if>
            <if test="tenantId != null">
                TENANT_ID_,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="procDefId != null">
                #{procDefId,jdbcType=VARCHAR},
            </if>
            <if test="taskDefKey != null">
                #{taskDefKey,jdbcType=VARCHAR},
            </if>
            <if test="procInstId != null">
                #{procInstId,jdbcType=VARCHAR},
            </if>
            <if test="executionId != null">
                #{executionId,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="parentTaskId != null">
                #{parentTaskId,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="owner != null">
                #{owner,jdbcType=VARCHAR},
            </if>
            <if test="assignee != null">
                #{assignee,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="claimTime != null">
                #{claimTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="duration != null">
                #{duration,jdbcType=BIGINT},
            </if>
            <if test="deleteReason != null">
                #{deleteReason,jdbcType=VARCHAR},
            </if>
            <if test="priority != null">
                #{priority,jdbcType=INTEGER},
            </if>
            <if test="dueDate != null">
                #{dueDate,jdbcType=TIMESTAMP},
            </if>
            <if test="formKey != null">
                #{formKey,jdbcType=VARCHAR},
            </if>
            <if test="category != null">
                #{category,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
</mapper>