package com.get.financecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/4/6 14:48
 */
@Data
public class PaymentFeeTypeVo extends BaseEntity {

    @ApiModelProperty(value = "关联项关联类型名")
    private String relationTargetKeyName;

    //==============实体类PaymentFeeType====================
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    private String typeName;

    @ApiModelProperty(value = "科目Id")
    private Long fkAccountingItemId;

    @ApiModelProperty(value = "科目名称")
    private String accountingItemName;

    @ApiModelProperty(value = "关联类型Key（目标类型表名）")
    private String relationTargetKey;

    /**
     * 对象类型：枚举：供应商provider
     */
    @ApiModelProperty(value = "对象类型：枚举：供应商provider")
    private String targetType;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

}
