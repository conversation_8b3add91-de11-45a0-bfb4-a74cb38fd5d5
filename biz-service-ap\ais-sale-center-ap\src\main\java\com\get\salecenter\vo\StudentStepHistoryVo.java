package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class StudentStepHistoryVo {

    @ApiModelProperty("步骤ID")
    private Long stepId;

    @ApiModelProperty("步骤名称")
    private String stepName;

    @ApiModelProperty("步骤最早出现时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date earliestTime;

    @ApiModelProperty("步骤出现次数")
    private Integer stepCount;

}
