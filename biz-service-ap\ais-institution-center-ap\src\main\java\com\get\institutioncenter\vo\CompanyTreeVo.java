package com.get.institutioncenter.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/23
 * @TIME: 16:11
 * @Description:
 **/
@Data
public class CompanyTreeVo {
    @ApiModelProperty(value = "公司ID")
    private Long id;
    @ApiModelProperty(value = "公司名称")
    private String name;
    @ApiModelProperty(value = "公司中文名称")
    private String shortName;
    @ApiModelProperty(value = "父级公司ID")
    private Long fkParentCompanyId;
    @ApiModelProperty(value = "公司编号")
    private String num;
    @ApiModelProperty(value = "子公司")
    private List<CompanyTreeVo> childList;
    @ApiModelProperty(value = "是否选中")
    private Boolean flag;

}
