package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.AgentStaff;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

/**
 * @author: Sea
 * @create: 2020/10/20 14:57
 * @verison: 1.0
 * @description:
 */
@Data
public class AgentStaffVo extends BaseEntity implements Serializable {

    /**
     * BD名字
     */
    @ApiModelProperty(value = "BD名字")
    private String bdName;

    /**
     * BD名字
     */
    @ApiModelProperty(value = "BD名字")
    private String bdCode;

    /**
     * 公司名字
     */
    @ApiModelProperty(value = "公司名字")
    private String companyName;

    //==============实体类AgentStaff====================
    private static final long serialVersionUID = 1L;
    /**
     * 代理Id
     */
    @ApiModelProperty(value = "代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
    /**
     * 绑定时间
     */
    @ApiModelProperty(value = "绑定时间")
    @Column(name = "active_date")
    private Date activeDate;
    /**
     * 取消绑定时间（下次绑定时，需要重新建立记录）
     */
    @ApiModelProperty(value = "取消绑定时间（下次绑定时，需要重新建立记录）")
    @Column(name = "unactive_date")
    private Date unactiveDate;
}
