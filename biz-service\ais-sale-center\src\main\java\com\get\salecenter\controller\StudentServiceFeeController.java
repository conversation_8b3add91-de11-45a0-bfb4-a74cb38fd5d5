package com.get.salecenter.controller;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ErrorCodeEnum;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dto.ServiceFeePaymentFormDto;
import com.get.financecenter.dto.ServiceFeeReceiptFormDto;
import com.get.salecenter.dto.CommentDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.ServiceFeeAPDto;
import com.get.salecenter.dto.ServiceFeeARAPDto;
import com.get.salecenter.dto.StudentFeeCommissionStatementExportDto;
import com.get.salecenter.dto.StudentServiceFeeDto;
import com.get.salecenter.dto.StudentServiceFeeSummaryDto;
import com.get.salecenter.service.StudentServiceFeeService;
import com.get.salecenter.vo.CommentVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.vo.StudentServiceFeeSummaryVo;
import com.get.salecenter.vo.StudentServiceFeeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 */
@Api(tags = "服务费管理")
@RestController
@RequestMapping("sale/serviceFee")
public class StudentServiceFeeController {

    @Resource
    private StudentServiceFeeService studentServiceFeeService;


    @ApiOperation(value = "新增学生留学服务费数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生服务费管理/新增学生留学服务费数据")
    @PostMapping("add")
    public SaveResponseBo add(@Validated({BaseVoEntity.Add.class}) @RequestBody StudentServiceFeeDto studentServiceFeeDto) {
        return studentServiceFeeService.save(studentServiceFeeDto);
    }

    @ApiOperation(value = "学生留学服务费数据详情")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生服务费管理/学生留学服务费数据详情")
    @GetMapping("{id}")
    public ResponseBo<StudentServiceFeeVo> findServiceFeeById(@PathVariable("id")Long id) {
        return new ResponseBo<>(studentServiceFeeService.findServiceFeeById(id));
    }

    @ApiOperation(value = "编辑学生留学服务费数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生服务费管理/编辑学生留学服务费数据")
    @PostMapping("update")
    public ResponseBo<StudentServiceFeeVo> update(@Validated({BaseVoEntity.Update.class}) @RequestBody StudentServiceFeeDto studentServiceFeeDto){
        return studentServiceFeeService.update(studentServiceFeeDto);
    }

    @ApiOperation(value = "一键作废")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生服务费管理/一键作废")
    @GetMapping("cancel")
    public SaveResponseBo cancel(@RequestParam("id") Long id,@RequestParam("status") Integer status) {
        return studentServiceFeeService.cancel(id,status);
    }

    @ApiOperation(value = "作废应收应付")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生服务费管理/留学服务费详情/作废应收应付")
    @PostMapping("cancelReceivablePayable")
    public UpdateResponseBo cancelReceivablePayable(@RequestParam("serviceFeeId") Long serviceFeeId) {
        return studentServiceFeeService.cancelReceivablePayable(serviceFeeId);
    }

    @ApiOperation(value = "学生服务费汇总")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生服务费管理/学生服务费汇总")
    @PostMapping("serviceFeeSummary")
    public ResponseBo<StudentServiceFeeSummaryVo> serviceFeeSummary(@RequestBody SearchBean<StudentServiceFeeSummaryDto> summaryVoSearchBean){
        return studentServiceFeeService.serviceFeeSummary(summaryVoSearchBean.getData(),summaryVoSearchBean);
    }

    /**
     * 导出Excel
     *
     * @param response
     * @param studentServiceFeeSummaryDto
     */
    @ApiOperation(value = "学生服务费汇总导出Excel", notes = "")
    @PostMapping("exportStudentServiceFeeSummary")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生服务费管理/学生服务费汇总导出Excel")
    @ResponseBody
    public void exportStudentServiceFeeSummary(HttpServletResponse response, @RequestBody StudentServiceFeeSummaryDto studentServiceFeeSummaryDto) {
        studentServiceFeeService.exportStudentServiceFeeSummary(response, studentServiceFeeSummaryDto);
    }

    @ApiOperation(value = "学生服务费汇总导出佣金提成结算表", notes = "")
    @PostMapping("exportFeeCommissionStatement")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生服务费管理/学生服务费汇总导出佣金提成结算表")
    @ResponseBody
    public void exportFeeCommissionStatement(HttpServletResponse response, @RequestBody @Validated StudentFeeCommissionStatementExportDto studentFeeCommissionStatementExportVo) {
        studentServiceFeeService.exportFeeCommissionStatement(response, studentFeeCommissionStatementExportVo);
    }

    @ApiOperation(value = "批量创建应收应付")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生服务费管理/批量创建应收应付")
    @PostMapping("createARAP")
    public SaveResponseBo createARAP(@Validated @RequestBody ServiceFeeARAPDto serviceFeeARAPDto) {
        return studentServiceFeeService.createARAP(serviceFeeARAPDto);
    }

    @ApiOperation(value = "批量创建应付")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生服务费管理/批量创建应付")
    @PostMapping("createAP")
    public SaveResponseBo createAP(@Validated @RequestBody ServiceFeeAPDto serviceFeeAPDto) {
        return studentServiceFeeService.createAP(serviceFeeAPDto);
    }

    @ApiOperation(value = "获取附件")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生服务费管理/获取附件")
    @PostMapping("getAttacheFileMedia")
    public ResponseBo<MediaAndAttachedVo> getAttacheFileMedia(@RequestBody SearchBean<MediaAndAttachedDto> searchBean){
        return studentServiceFeeService.getAttacheFileMedia(searchBean.getData(),searchBean);
    }

    @ApiOperation(value = "上传附件")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生服务费管理/上传附件")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> addAttachedFile(@RequestBody  @Validated(MediaAndAttachedDto.Add.class) ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(studentServiceFeeService.addItemMedia(mediaAttachedVo));
    }

    @ApiOperation(value = "编辑备注")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生服务费管理/编辑备注")
    @PostMapping("editComment")
    public SaveResponseBo editComment(@RequestBody CommentDto commentDto) {
        return studentServiceFeeService.editComment(commentDto);
    }

    @ApiOperation(value = "获取备注")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生服务费管理/获取备注")
    @PostMapping("getComments")
    public ResponseBo<CommentVo> getComments(@RequestBody SearchBean<CommentDto> searchBean){
        return studentServiceFeeService.getComments(searchBean.getData(),searchBean);
    }

    @ApiOperation(value = "批量创建实收", notes = "未创建应收计划时，会同时创建应收计划及收款单；已创建应收计划时，创建收款单")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生服务费管理/批量创建实收")
    @PostMapping("batchCreateReceiptForm")
    public ResponseBo batchCreateReceiptForm(@Validated @RequestBody ServiceFeeReceiptFormDto serviceFeeReceiptFormDto) {
        return studentServiceFeeService.batchCreateReceiptForm(serviceFeeReceiptFormDto);
    }

    @ApiOperation(value = "批量创建实付", notes = "未创建应付计划时，会同时创建应付计划及付款单；已创建应付计划时，创建付款单")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生服务费管理/批量创建实付")
    @PostMapping("batchCreatePaymentForm")
    public ResponseBo batchCreatePaymentForm(@Validated @RequestBody ServiceFeePaymentFormDto serviceFeePaymentFormDto) {
        String remindMsg = studentServiceFeeService.batchCreatePaymentForm(serviceFeePaymentFormDto);
        if (GeneralTool.isNotEmpty(remindMsg)) {
            return ResponseBo.ok(ErrorCodeEnum.REQUEST_OK_MESSAGE.getCode(), remindMsg);
        }
        return ResponseBo.ok();
    }

    @ApiOperation(value = "取消已完成的业务状态", notes = "若已经创建应收计划财务数据，就不能取消【已完成】的业务状态")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生服务费管理/取消已完成的业务状态")
    @PostMapping("cancelCompletedBusinessStatus")
    public SaveResponseBo cancelCompletedBusinessStatus(@RequestParam("serviceFeeId") Long serviceFeeId) {
        return studentServiceFeeService.cancelCompletedBusinessStatus(serviceFeeId);
    }

    @ApiOperation(value = "批量标记已提交财务")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生服务费管理/批量标记已提交财务")
    @PostMapping("batchCompleteSettlementStatus")
    public ResponseBo batchCompleteSettlementStatus(@RequestBody Set<Long> serviceFeeIds) {
        return studentServiceFeeService.batchCompleteSettlementStatus(serviceFeeIds);
    }

    @ApiOperation(value = "批量完成业务状态")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生服务费管理/批量完成业务状态")
    @PostMapping("batchCompleteBusinessStatus")
    public ResponseBo batchCompleteBusinessStatus(@RequestBody Set<Long> serviceFeeIds) {
        return studentServiceFeeService.batchCompleteBusinessStatus(serviceFeeIds);
    }


    @ApiOperation(value = "取消已完成的结算审批", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生服务费管理/取消已完成的结算审批")
    @PostMapping("cancelCompletedSettlementApproval")
    public SaveResponseBo cancelCompletedSettlementApproval(@RequestParam("serviceFeeId") Long serviceFeeId) {
        return studentServiceFeeService.cancelCompletedSettlementApproval(serviceFeeId);
    }

    @ApiOperation(value = "批量完成结算审批")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生服务费管理/批量完成结算审批")
    @PostMapping("batchCompleteSettlementApproval")
    public ResponseBo batchCompleteSettlementApproval(@RequestBody Set<Long> serviceFeeIds) {
        return studentServiceFeeService.batchCompleteSettlementApproval(serviceFeeIds);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "部门下拉框数据", notes = "id公司id")
    @PostMapping("getDepartmentSelect")
    public ResponseBo<BaseSelectEntity> getDepartmentList(@RequestParam("id") Long companyId) {
        List<BaseSelectEntity> datas = studentServiceFeeService.getDepartmentSelect(companyId);
        return new ListResponseBo<>(datas);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "收款方类型下拉框", notes = "")
    @PostMapping("getTypeKeyReceivableSelect")
    public ResponseBo getTypeKeyReceivableSelect() {
        return new ListResponseBo<>(TableEnum.enumsTranslation2Arrays(TableEnum.TYPE_KEY_RECEIVABLE));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "附件类型下拉", notes = "")
    @PostMapping("getMediaType")
    public ResponseBo getMediaType() {
        return new ListResponseBo<>(FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.M_STUDENT_SERVICE_FEE_MEDIA_TYPE));
    }

//    留学申请服务费生成发票
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "留学申请服务费生成发票", notes = "")
    @PostMapping("createPdf")
    public void createServiceFeePdf(@RequestBody Map<String, Long> request, HttpServletResponse response) {
        Long id = request.get("id");
       studentServiceFeeService.createServiceFeePdf(id,response);
    }

    @ApiIgnore
    @GetMapping("getStudentServiceFeeSummary")
    public ResponseBo getServiceFeeNum(@RequestParam(required = false) Long id) {
        return new ResponseBo<>(studentServiceFeeService.getServiceFeeInfoById(id));
    }


}
