<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.AgentContractFormulaInstitutionMapper">

  <insert id="insertSelective" parameterType="com.get.salecenter.entity.AgentContractFormulaInstitution" keyProperty="id" useGeneratedKeys="true">
    insert into r_agent_contract_formula_institution
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkAgentContractFormulaId != null">
        fk_agent_contract_formula_id,
      </if>
      <if test="fkInstitutionId != null">
        fk_institution_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkAgentContractFormulaId != null">
        #{fkAgentContractFormulaId,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionId != null">
        #{fkInstitutionId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getInstitutionIdsByFkid" resultType="java.lang.Long">
    select
     fk_institution_id
    from
     r_agent_contract_formula_institution
    where
     fk_agent_contract_formula_id = #{agentContractFormulaId}
  </select>
</mapper>