<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.ContactPersonMapper">

  <select id="datas" resultType="com.get.institutioncenter.vo.ContactPersonVo">
    select cp.* from  s_contact_person cp right join r_contact_person_company cpc
    <if test="fkTableName!=null and fkTableName !=''" >
      left join ${fkTableName} ip on cp.fk_table_id = ip.id
    </if>
    <where>
      <if test="fkTableName!=null and fkTableName !=''" >
        and  cp.fk_table_name = #{fkTableName}
      </if>
      <if test="targetName != null and  targetName !=''">
        and position(#{targetName,jdbcType=VARCHAR} in ip.name)
      </if>
      <if test="fkContactPersonTypeKey!=null and fkContactPersonTypeKey !=''" >
        and  cp.fk_contact_person_type_key like concat("%",#{fkContactPersonTypeKey},"%")
      </if>
      <if test="keyWord != null and  keyWord !=''">
        and position(#{keyWord,jdbcType=VARCHAR} in cp.name)
      </if>
      <if test="companyIds != null and companyIds.size()>0">
        AND cpc.fk_company_id IN
        <foreach collection="companyIds" item="companyId" index="index" open="(" separator="," close=")">
          #{companyId}
        </foreach>
      </if>
    </where>
  </select>
  <select id="getCountByInstitutionId" parameterType="java.lang.Long" resultType="java.lang.Integer">
    select count(*) from s_contact_person  where fk_table_name = 'm_institution'  and
      fk_table_id = #{id}
  </select>

  <select id="checkProviderInfoIsEmptyByProviderId"  resultType="java.lang.Boolean">
    select IFNULL(max(id),0) id from s_contact_person where fk_table_name = #{tableName} and fk_table_id= #{providerId} LIMIT 1
  </select>
</mapper>