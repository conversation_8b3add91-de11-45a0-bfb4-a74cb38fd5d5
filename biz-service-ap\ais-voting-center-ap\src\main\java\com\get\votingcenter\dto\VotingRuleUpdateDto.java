package com.get.votingcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @author: Hardy
 * @create: 2021/9/23 14:46
 * @verison: 1.0
 * @description:
 */
@Data
public class VotingRuleUpdateDto extends BaseVoEntity implements Serializable {

    /** 投票主题Id
     */
    @ApiModelProperty(value = "投票主题Id")
    @NotNull(message = "投票主题Id不能为空", groups = {Add.class, Update.class})
    private Long fkVotingId;

    /**
     * 每次投票票数，默认为1，必填
     */
    @ApiModelProperty(value = "每次投票票数，默认为1，必填")
    @NotNull(message = "投票票数不能为空", groups = {Add.class, Update.class})
    private Integer voteCount;

    /**
     * 投票次数限制，无限制为-1，默认为1，必填
     */
    @ApiModelProperty(value = "投票次数限制，无限制为-1，默认为1，必填")
    @NotNull(message = "投票次数限制不能为空", groups = {Add.class, Update.class})
    private Integer voteLimit;

    /**
     * 是否能重复投票，0否/1是
     */
    @ApiModelProperty(value = "是否能重复投票，0否/1是")
//    @NotNull(message = "是否能重复投票不能为空", groups = {Add.class, Update.class})
    private Boolean isRepeatVoting;

    /**
     * 投票规则适用对象：填手机号，多个手机号逗号分隔。若为空为默认所有用户规则
     */
    @ApiModelProperty(value = "投票规则适用对象：填手机号，多个手机号逗号分隔。若为空为默认所有用户规则")
    private String voteRuleAuth;

}
