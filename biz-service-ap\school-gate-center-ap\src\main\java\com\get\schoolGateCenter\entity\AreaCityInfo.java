package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("u_area_city_info")
public class AreaCityInfo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 城市资讯类型Id
     */
    @ApiModelProperty(value = "城市资讯类型Id")
    @Column(name = "fk_area_city_info_type_id")
    private Long fkAreaCityInfoTypeId;
    /**
     * 城市Id
     */
    @ApiModelProperty(value = "城市Id")
    @Column(name = "fk_area_city_id")
    private Long fkAreaCityId;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @Column(name = "title")
    private String title;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @Column(name = "public_level")
    private String publicLevel;
    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    @Column(name = "publish_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date publishTime;
    /**
     * 内容描述
     */
    @ApiModelProperty(value = "内容描述")
    @Column(name = "description")
    private String description;
    /**
     * 网页标题
     */
    @ApiModelProperty(value = "网页标题")
    @Column(name = "web_title")
    private String webTitle;
    /**
     * 网页Meta描述
     */
    @ApiModelProperty(value = "网页Meta描述")
    @Column(name = "web_meta_description")
    private String webMetaDescription;
    /**
     * 网页Meta关键字
     */
    @ApiModelProperty(value = "网页Meta关键字")
    @Column(name = "web_meta_keywords")
    private String webMetaKeywords;
}