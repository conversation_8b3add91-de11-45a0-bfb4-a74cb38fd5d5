package com.get.examcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("m_examination_answer")
public class ExaminationAnswer extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 问题Id
     */
    @ApiModelProperty(value = "问题Id")
    private Long fkExaminationQuestionId;
    /**
     * 答案内容
     */
    @ApiModelProperty(value = "答案内容")
    private String answer;
    /**
     * 是否正确答案：0否/1是
     */
    @ApiModelProperty(value = "是否正确答案：0否/1是")
    private Boolean isRightAnswer;

    /**
     * 答案得分
     */
    @ApiModelProperty(value = "得分")
    private Integer score;

    /**
     * 默认为0，为0时随机排序，排序为从大到小顺序排列
     */
    @ApiModelProperty(value = "默认为0，为0时随机排序，排序为从大到小顺序排列")
    private Integer viewOrder;
}