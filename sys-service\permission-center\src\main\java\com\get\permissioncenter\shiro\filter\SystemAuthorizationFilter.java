//package com.get.permissioncenter.shiro.filter;
//
//import com.get.common.eunms.ErrorCodeEnum;
//import com.get.common.result.ResponseBo;
//import com.get.common.utils.WebUtilsPro;
//import org.apache.shiro.subject.Subject;
//import org.apache.shiro.web.filter.authz.AuthorizationFilter;
//
//import javax.servlet.ServletRequest;
//import javax.servlet.ServletResponse;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.io.IOException;
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;
//
///**
// * shiro无权限过滤器
// *
// * @package: com.yinghu.platform.shiro.filter
// * @author: jack
// * @create: 2020-05-20
// * @verison: 1.0
// * @description: shiro无权限过滤器
// */
//public class SystemAuthorizationFilter extends AuthorizationFilter {
//    @Override
//    protected boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object o) throws Exception {
//        HttpServletRequest httpRequest = (HttpServletRequest) request;
//        HttpServletResponse httpResponse = (HttpServletResponse) response;
//        String url = httpRequest.getRequestURI();
//        String reqUrl = httpRequest.getRequestURL().toString();
//        System.out.println("请求地址："+reqUrl);
//        System.out.println("参数地址："+url);
//
//        String re = "\\/(.*?)\\/";
//        Pattern p = Pattern.compile(re);
//        Matcher m = p.matcher(url);
//        String server="";
//        int n=0;
//        while(m.find()){
//            server=m.group(1);
//            if(n==1){
//                break;
//            }
//            n++;
//        }
//
//        Subject subject = getSubject(request, response);
//        System.out.println("访问服务名："+server+"；用户角色是否存在："+subject.hasRole(server));
//        if(subject.hasRole(server)){
//            //进一步看是否是访问分类
//            String rsearch_cid=request.getParameter("search_cid");
//            if(rsearch_cid!=null){
//                try{
//                    subject.checkPermission(server+"&"+rsearch_cid);
//                }catch(Exception e){
//                    //抛出异常表示无权限
//                    return false;
//                }
//            }
//            return true;
//        }
//
//        return false;
//    }
//    @Override
//    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws IOException {
//       WebUtilsPro.out((HttpServletResponse) response, ResponseBo.error(ErrorCodeEnum.VERIFY_FAILED));
//        return Boolean.FALSE;
//    }
//}
