package com.get.financecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 公司利润表项目
 */
@Data
public class CompanyProfitAndLossItemVo extends BaseEntity {

    @ApiModelProperty("公司id")
    private Long fkCompanyId;

    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("项目名称")
    private String title;

    @ApiModelProperty("显示模式：Code/Expand/Sum")
    private String showMode;

    @ApiModelProperty("显示模式名称")
    private String showModeName;

    @ApiModelProperty("科目Id")
    private Long fkAccountingItemId;
    @ApiModelProperty("科目名称")
    private String accountingItemName;

    @ApiModelProperty("加减方向：1/-1")
    private Integer directionValue;
    @ApiModelProperty("加减方向名称")
    private String directionValueName;

    @ApiModelProperty("排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty("颜色代码RGB")
    private String colorCode;


}

