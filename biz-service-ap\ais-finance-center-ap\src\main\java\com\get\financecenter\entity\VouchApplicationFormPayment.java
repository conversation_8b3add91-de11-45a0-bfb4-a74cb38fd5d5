package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 凭证和财务单据的支付记录
 */
@Data
@TableName("m_vouch_application_form_payment")
public class VouchApplicationFormPayment extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "凭证Id")
    private Long fkVouchId;

    @ApiModelProperty(value = "财务单据表名")
    private String fkTableName;

    @ApiModelProperty(value = "财务单据表记录Id")
    private Long fkTableId;

    @ApiModelProperty(value = "财务单据表明细记录Id")
    private Long fkTableItemId;

    @ApiModelProperty(value = "付款类型Key：银付/现付/转/核销借款")
    private String paymentKey;

    @ApiModelProperty(value = "付款时间（业务时间）")
    private Date paymentDate;

    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "状态：0作废/1生效")
    private Integer status;

}