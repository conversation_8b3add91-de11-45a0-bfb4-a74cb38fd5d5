<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.platformconfigcenter.dao.appmso.TranslationMapper">
    <resultMap id="BaseResultMap" type="com.get.platformconfigcenter.entity.Translation">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_table_name" jdbcType="VARCHAR" property="fkTableName"/>
        <result column="fk_table_id" jdbcType="BIGINT" property="fkTableId"/>
        <result column="fk_translation_mapping_id" jdbcType="BIGINT" property="fkTranslationMappingId"/>
        <result column="language_code" jdbcType="VARCHAR" property="languageCode"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.get.platformconfigcenter.entity.Translation">
        <result column="translation" jdbcType="LONGVARCHAR" property="translation"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , fk_table_name, fk_table_id, fk_translation_mapping_id, language_code, gmt_create,
    gmt_create_user, gmt_modified, gmt_modified_user
    </sql>
    <sql id="Blob_Column_List">
        translation
    </sql>
   <!-- <insert id="insert" parameterType="com.get.platformconfigcenter.entity.Translation">
        insert into s_translation (id, fk_table_name, fk_table_id,
                                   fk_translation_mapping_id, language_code, gmt_create,
                                   gmt_create_user, gmt_modified, gmt_modified_user,
                                   translation)
        values (#{id,jdbcType=BIGINT}, #{fkTableName,jdbcType=VARCHAR}, #{fkTableId,jdbcType=BIGINT},
                #{fkTranslationMappingId,jdbcType=BIGINT}, #{languageCode,jdbcType=VARCHAR},
                #{gmtCreate,jdbcType=TIMESTAMP},
                #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP},
                #{gmtModifiedUser,jdbcType=VARCHAR},
                #{translation,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.get.platformconfigcenter.entity.Translation">
        insert into s_translation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkTableName != null">
                fk_table_name,
            </if>
            <if test="fkTableId != null">
                fk_table_id,
            </if>
            <if test="fkTranslationMappingId != null">
                fk_translation_mapping_id,
            </if>
            <if test="languageCode != null">
                language_code,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
            <if test="translation != null">
                translation,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkTableName != null">
                #{fkTableName,jdbcType=VARCHAR},
            </if>
            <if test="fkTableId != null">
                #{fkTableId,jdbcType=BIGINT},
            </if>
            <if test="fkTranslationMappingId != null">
                #{fkTranslationMappingId,jdbcType=BIGINT},
            </if>
            <if test="languageCode != null">
                #{languageCode,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
            <if test="translation != null">
                #{translation,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.get.platformconfigcenter.entity.Translation">
        update s_translation
        <set>
            <if test="fkTableName != null">
                fk_table_name = #{fkTableName,jdbcType=VARCHAR},
            </if>
            <if test="fkTableId != null">
                fk_table_id = #{fkTableId,jdbcType=BIGINT},
            </if>
            <if test="fkTranslationMappingId != null">
                fk_translation_mapping_id = #{fkTranslationMappingId,jdbcType=BIGINT},
            </if>
            <if test="languageCode != null">
                language_code = #{languageCode,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
            <if test="translation != null">
                translation = #{translation,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.get.platformconfigcenter.entity.Translation">
        update s_translation
        set fk_table_name             = #{fkTableName,jdbcType=VARCHAR},
            fk_table_id               = #{fkTableId,jdbcType=BIGINT},
            fk_translation_mapping_id = #{fkTranslationMappingId,jdbcType=BIGINT},
            language_code             = #{languageCode,jdbcType=VARCHAR},
            gmt_create                = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_create_user           = #{gmtCreateUser,jdbcType=VARCHAR},
            gmt_modified              = #{gmtModified,jdbcType=TIMESTAMP},
            gmt_modified_user         = #{gmtModifiedUser,jdbcType=VARCHAR},
            translation               = #{translation,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.get.platformconfigcenter.entity.Translation">
        update s_translation
        set fk_table_name             = #{fkTableName,jdbcType=VARCHAR},
            fk_table_id               = #{fkTableId,jdbcType=BIGINT},
            fk_translation_mapping_id = #{fkTranslationMappingId,jdbcType=BIGINT},
            language_code             = #{languageCode,jdbcType=VARCHAR},
            gmt_create                = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_create_user           = #{gmtCreateUser,jdbcType=VARCHAR},
            gmt_modified              = #{gmtModified,jdbcType=TIMESTAMP},
            gmt_modified_user         = #{gmtModifiedUser,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <delete id="deleteTranslations">
        DELETE FROM s_translation WHERE fk_table_name = #{tableName} and fk_table_id = #{id}
    </delete>
    <select id="getTranslation" resultType="java.lang.String">
        select translation from s_translation
        <where>
            <if test="fkTableName != null and  fkTableName !=''">
                and fk_table_name =#{fkTableName}
            </if>
            <if test="fkTableId != null">
                and fk_table_id = #{fkTableId}
            </if>
            <if test="fkTranslationMappingId != null">
                and fk_translation_mapping_id = #{fkTranslationMappingId}
            </if>
            <if test="languageCode != null and  languageCode !=''">
                and language_code =#{languageCode}
            </if>
        </where>
    </select>-->
</mapper>