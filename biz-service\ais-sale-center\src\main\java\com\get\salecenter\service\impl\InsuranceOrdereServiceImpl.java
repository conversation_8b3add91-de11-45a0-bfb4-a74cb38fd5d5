package com.get.salecenter.service.impl;

import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.dao.insurance.InsuranceOrderMapper;
import com.get.salecenter.service.InsuranceOrdereService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 澳小保业务处理类
 */
@Service
public class InsuranceOrdereServiceImpl implements InsuranceOrdereService {
    @Resource
    private InsuranceOrderMapper insuranceOrderMapper;

    /**
     * 澳小保下拉框
     * @param companyId
     * @return
     */
    @Override
    public List<BaseSelectEntity> getInsuranceOrderSelect(Long companyId) {
        List<BaseSelectEntity> baseSelectEntitys = insuranceOrderMapper.getInsuranceOrder(Collections.singletonList(companyId), null);
        return baseSelectEntitys;
    }
}
