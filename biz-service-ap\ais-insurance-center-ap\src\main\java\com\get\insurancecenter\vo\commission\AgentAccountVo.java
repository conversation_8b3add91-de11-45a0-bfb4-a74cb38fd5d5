package com.get.insurancecenter.vo.commission;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.get.insurancecenter.config.BigDecimalTwoScaleSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author:Oliver
 * @Date: 2025/6/20
 * @Version 1.0
 * @apiNote:代理账户信息
 */
@Data
public class AgentAccountVo {

    @ApiModelProperty(value = "账户ID")
    private Long accountId;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "代理ID")
    private Long agentId;

    @ApiModelProperty(value = "币种编号")
    private String currencyTypeNum;

    @ApiModelProperty(value = "账户卡类型，枚举：借记卡1,存折2,信用卡3,准贷记卡4,预付卡费5,境外卡6")
    private Integer accountCardType;

    @ApiModelProperty(value = "银行账户名称")
    private String bankAccount;

    @ApiModelProperty(value = "银行账号")
    private String bankAccountNum;

    @ApiModelProperty(value = "银行名称")
    private String bankName;

    @ApiModelProperty(value = "银行支行名称")
    private String bankBranchName;

    @ApiModelProperty(value = "银行地址国家Id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "银行地址州省Id")
    private Long fkAreaStateId;

    @ApiModelProperty(value = "银行地址城市Id")
    private Long fkAreaCityId;

    @ApiModelProperty(value = "银行地址城市区域Id")
    private Long fkAreaCityDivisionId;

    @ApiModelProperty(value = "银行地址")
    private String bankAddress;

    @ApiModelProperty(value = "银行编号类型：SwiftCode/BSB")
    private String bankCodeType;

    @ApiModelProperty(value = "银行编号")
    private String bankCode;

    @ApiModelProperty(value = "国家编码")
    private String areaCountryCode;

    @ApiModelProperty(value = "是否默认首选：0否/1是")
    private Integer isDefault;

    @ApiModelProperty(value = "是否激活：0否/1是")
    private Integer isActive;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "支付金额")
    @JsonSerialize(using = BigDecimalTwoScaleSerializer.class)
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "支付金额币种")
    private String orderCurrencyTypeNum;

    @ApiModelProperty(value = "兑换币种/结算币种")
    private String settlementCurrencyTypeNum;

    @ApiModelProperty(value = "汇率")
    private BigDecimal exchangeRate;

    @ApiModelProperty(value = "折合金额/结算金额")
    @JsonSerialize(using = BigDecimalTwoScaleSerializer.class)
    private BigDecimal settlementAmount;

    @ApiModelProperty(value = "关联的对账单ID")
    private Long settlementBillId;
}
