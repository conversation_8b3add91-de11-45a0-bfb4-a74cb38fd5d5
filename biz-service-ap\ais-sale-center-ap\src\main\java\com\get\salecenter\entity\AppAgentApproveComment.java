package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("m_app_agent_approve_comment")
public class AppAgentApproveComment extends BaseEntity implements Serializable {
    /**
     * 学生代理申请Id
     */
    @ApiModelProperty(value = "学生代理申请Id")
    @Column(name = "fk_app_agent_id")
    private Long fkAppAgentId;

    /**
     * 审批意见
     */
    @ApiModelProperty(value = "审批意见")
    @Column(name = "approve_comment")
    private String approveComment;

    /**
     * 邮件通知时间，有就表示已发邮件
     */
    @ApiModelProperty(value = "邮件通知时间，有就表示已发邮件")
    @Column(name = "email_time")
    private Date emailTime;

    private static final long serialVersionUID = 1L;
}