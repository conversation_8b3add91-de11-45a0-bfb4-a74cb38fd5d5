//package com.get.permissioncenter.shiro;
//
//import com.get.core.tool.utils.GeneralTool;
//import org.apache.shiro.web.servlet.ShiroHttpServletRequest;
//import org.apache.shiro.web.session.mgt.DefaultWebSessionManager;
//import org.apache.shiro.web.util.WebUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import javax.servlet.ServletRequest;
//import javax.servlet.ServletResponse;
//import java.io.Serializable;
//
//public class StatelessSessionManager extends DefaultWebSessionManager {
//
//    /**
//     * 这个是服务端要返回给客户端
//     */
//    public static final String TOKEN_NAME = "token";
//    /**
//     * 这个是客户端请求服务端带的header
//     */
//    public static final String HEADER_TOKEN_NAME = "token";
//
//    public final static Logger LOG = LoggerFactory.getLogger(StatelessSessionManager.class);
//    private static final String REFERENCED_SESSION_ID_SOURCE = "Stateless request";
//
//    @Override
//    protected Serializable getSessionId(ServletRequest request, ServletResponse response) {
//        String id = WebUtils.toHttp(request).getHeader(HEADER_TOKEN_NAME);
//        if(GeneralTool.isEmpty(id)){
//            return super.getSessionId(request, response);
//        }else{
//            request.setAttribute(ShiroHttpServletRequest.REFERENCED_SESSION_ID_SOURCE,REFERENCED_SESSION_ID_SOURCE);
//            request.setAttribute(ShiroHttpServletRequest.REFERENCED_SESSION_ID,id);
//            request.setAttribute(ShiroHttpServletRequest.REFERENCED_SESSION_ID_IS_VALID,Boolean.TRUE);
//            return id;
//        }
//    }
//}
