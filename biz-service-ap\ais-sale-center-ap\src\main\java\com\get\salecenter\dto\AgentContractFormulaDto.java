package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/1/6 12:37
 * @verison: 1.0
 * @description:
 */
@Data
public class AgentContractFormulaDto extends BaseVoEntity {
    /**
     * 学生代理Id
     */
    @NotNull(message = "学生代理不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "学生代理Id")
    private Long fkAgentId;

    /*    *//**
     * 学校提供商Id
     *//*
    @NotNull(message = "学校提供商Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "学校提供商Id")
    private Long fkInstitutionProviderId;*/

    /*
     */
/**
 * 合同公式Id
 *//*

    @NotNull(message = "合同公式不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "合同公式Id")
    private Long fkContractFormulaId;
*/

    /**
     * 生效开始时间
     */
    @ApiModelProperty(value = "生效开始时间")
    private Date startTime;

    /**
     * 生效结束时间
     */
    @ApiModelProperty(value = "生效结束时间")
    private Date endTime;

    /*
     */
/**
 * 币种编号
 *//*

    @NotBlank(message = "币种编号不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;
*/

    /**
     * 代理佣金上限(总)
     */
    @ApiModelProperty(value = "代理佣金上限(总)")
    @Column(name = "limit_amount_ag")
    private BigDecimal limitAmountAg;

    /**
     * 公式备注
     */
    @ApiModelProperty(value = "公式备注")
    private String remark;

    /**
     * 排序（倒序），数字由大到小排列
     */
    @ApiModelProperty(value = "排序（倒序），数字由大到小排列")
    private Integer viewOrder;

    /**
     * 是否激活：0否/1是
     */
    @NotNull(message = "是否生效不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

    //自定义内容
    /**
     * 国家区域(多选)
     */
    @ApiModelProperty(value = "国家区域(多选)")
    private List<Long> countryIdList;

    /**
     * 课程类型(多选)
     */
    @ApiModelProperty(value = "课程类型(多选)")
    private List<Long> courseTypeIdList;

    /**
     * 课程等级(多选)
     */
    @ApiModelProperty(value = "课程等级(多选)")
    private List<Long> majorLevelIdList;

    /**
     * 学校(多选)
     */
    @ApiModelProperty(value = "学校(多选)")
    private List<Long> institutionIdList;

    /**
     * 课程(多选)
     */
    @ApiModelProperty(value = "课程(多选)")
    private List<Long> courseIdList;

    /**
     * 学生代理合同公式佣金配置
     */
    @ApiModelProperty(value = "学生代理合同公式佣金配置")
    private List<AgentContractFormulaCommissionDto> agentContractFormulaCommissionVos;

}
