package com.get.salecenter.service.impl;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.consts.SaleCenterConstant;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.FocExportVo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.CommonUtil;
import com.get.common.utils.DataConverter;
import com.get.common.utils.GetStringUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestContextUtil;
import com.get.file.utils.FileUtils;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.entity.StaffConfig;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.salecenter.dao.sale.*;
import com.get.salecenter.dto.AiStudentDto;
import com.get.salecenter.dto.ClientStudentDto;
import com.get.salecenter.dto.CommentDto;
import com.get.salecenter.dto.CurrentStudentApplicationStatusListDto;
import com.get.salecenter.dto.CurrentStudentApplicationStatusStatisticsDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.ReceiveApplyDataTimeDto;
import com.get.salecenter.dto.StudentAccommodationDto;
import com.get.salecenter.dto.StudentAgentBindingDto;
import com.get.salecenter.dto.StudentClientSourceDto;
import com.get.salecenter.dto.StudentCourseStatisticsListDto;
import com.get.salecenter.dto.StudentCourseStatisticsSearchDto;
import com.get.salecenter.dto.StudentDto;
import com.get.salecenter.dto.StudentInfoDto;
import com.get.salecenter.dto.StudentInsuranceDto;
import com.get.salecenter.dto.StudentOfferDto;
import com.get.salecenter.dto.StudentOfferItemStatisticalStatusDto;
import com.get.salecenter.dto.StudentProjectRoleStaffDto;
import com.get.salecenter.dto.query.StudentListQueryDto;
import com.get.salecenter.entity.*;
import com.get.salecenter.service.*;
import com.get.salecenter.utils.VerifyDataPermissionsUtils;
import com.get.salecenter.utils.sale.GetAgentLabelDataUtils;
import com.get.salecenter.utils.sale.VerifyStudentOfferItemUtils;
import com.get.salecenter.vo.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.lang.management.ManagementFactory;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @DATE: 2020/9/29
 * @TIME: 14:28
 * @Description:
 **/
@Service
@Slf4j
public class StudentServiceImpl extends ServiceImpl<StudentMapper, Student> implements IStudentService {
    @Resource
    private StudentMapper studentMapper;
    @Resource
    private VerifyDataPermissionsUtils verifyDataPermissionsUtils;
    @Resource
    @Lazy
    private AsyncExportService asyncExportService;
    @Resource
    private StudentProjectRoleStaffMapper studentProjectRoleStaffMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IFileCenterClient fileCenterClient;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    @Lazy
    private IStudentAgentService studentAgentService;
    @Resource
    private StudentEducationLevelTypeMapper studentEducationLevelTypeMapper;
    @Resource
    private IStudentProjectRoleStaffService projectRoleStaffService;
    @Resource
    @Lazy
    private ICommentService commentService;
    @Resource
    @Lazy
    private IStudentOfferService studentOfferService;
    @Resource
    @Lazy
    private IDeleteService deleteService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private IStudentOfferItemStepService studentOfferItemStepService;

    @Resource
    private AgentMapper agentMapper;
    @Resource
    private StudentOfferItemMapper studentOfferItemMapper;
    @Resource
    private StudentOfferItemStepMapper studentOfferItemStepMapper;
    @Resource
    private StaffCommissionActionMapper staffCommissionActionMapper;
    @Resource
    private EnrolFailureReasonMapper enrolFailureReasonMapper;
    @Resource
    private StudentOfferItemDeferEntranceTimeMapper studentOfferItemDeferEntranceTimeMapper;
    @Resource
    @Lazy
    private IAgentService agentService;
    @Resource
    private StudentAgentMapper studentAgentMapper;
    @Resource
    private StudentOfferMapper studentOfferMapper;
    @Resource
    private IContactPersonService contactPersonService;
    @Resource
    private IAgentStaffService agentStaffService;
    @Resource
    private IStaffBdCodeService staffBdCodeService;
    @Resource
    @Lazy
    private IStudentAccommodationService studentAccommodationService;
    @Resource
    private IStudentInsuranceService studentInsuranceService;
    @Resource
    private RStudentOfferItemStepMapper rStudentOfferItemStepMapper;
    @Resource
    private StudentInsuranceMapper studentInsuranceMapper;
    @Resource
    private StudentAccommodationMapper studentAccommodationMapper;
    @Resource
    private StudentServiceFeeService studentServiceFeeService;
    @Resource
    @Lazy
    private IStudentOfferItemService offerItemService;
    @Resource
    private RStudentIssueStudentMapper rStudentIssueStudentMapper;
    @Resource
    private StaffBdCodeMapper staffBdCodeMapper;
    @Resource
    private StudentProjectRoleMapper studentProjectRoleMapper;
    @Resource
    @Lazy
    private IStaffCommissionStudentService staffCommissionStudentService;

    @Resource
    private IStudentContactPersonService studentContactPersonService;

    @Resource
    private IStudentSubjectScoreService studentSubjectScoreService;

    @Resource
    private IStudentEventService studentEventService;

    @Resource
    private RStudentIssueStudentService studentIssueStudentService;

    @Resource
    private IStudentOfferItemIssueInstitutionCourseService studentOfferItemIssueInstitutionCourseService;


    @Resource
    private StaffCommissionPolicyMapper staffCommissionPolicyMapper;
    @Lazy
    @Resource
    private IStaffCommissionActionService staffCommissionActionService;
    @Lazy
    @Resource
    private IStaffCommissionStepService staffCommissionStepService;
    @Resource
    private VerifyStudentOfferItemUtils verifyStudentOfferItemUtils;

    @Resource
    private IReminderCenterClient reminderCenterClient;

    @Resource
    private KpiInstitutionProviderMapper kpiInstitutionProviderMapper;
    @Resource
    private IStudentOfferService offerService;
    @Resource
    private StudentServiceFeeMapper studentServiceFeeMapper;

    @Resource
    private ClientMapper clientMapper;

    @Resource
    private ClientSourceMapper clientSourceMapper;

    @Resource
    private BusinessProviderMapper businessProviderMapper;
    @Resource
    private IFinanceCenterClient financeCenterClient;

    @Resource
    private RStudentUuidMapper rStudentUuidMapper;

    @Resource
    private GetAgentLabelDataUtils getAgentLabelDataUtils;

    @Resource
    private RStudentIdentifyMapper rStudentIdentifyMapper;

    /**
     * ISSUE请求地址
     */
    @Value("${issue.url}")
    private String url;

    @Value("${emailMate-url}")
    private String emailMateUrl;

    private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


    private static Integer getAge(Date birthDay) {
        Calendar cal = Calendar.getInstance();
        if (cal.before(birthDay)) {
            //出生日期晚于当前时间，无法计算
            return null;
        }
        int yearNow = cal.get(Calendar.YEAR);
        int monthNow = cal.get(Calendar.MONTH);
        int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH);
        cal.setTime(birthDay);
        int yearBirth = cal.get(Calendar.YEAR);
        int monthBirth = cal.get(Calendar.MONTH);
        int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);
        int age = yearNow - yearBirth;
        if (monthNow <= monthBirth) {
            if (monthNow == monthBirth) {
                if (dayOfMonthNow < dayOfMonthBirth) {
                    age--;//当前日期在生日之前，年龄减一
                }
            } else {
                age--;//当前月份在生日之前，年龄减一
            }
        }
        return age;
    }

    /**
     * 新增学生
     *
     * @param studentDto
     * @return
     */
    @Override
    public Long addStudent(StudentDto studentDto) {
        if (GeneralTool.isEmpty(studentDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (!SecureUtil.validateCompany(studentDto.getFkCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        Long fkDepartmentId = SecureUtil.getFkDepartmentId();
        Long fkCompanyId = SecureUtil.getFkCompanyId();

        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.STUDENT_SHARED_PATH_REQUIRED.key, 1).getData();
        String configValue1 = companyConfigMap.get(fkCompanyId);
        List<Long> fkDepartmentIds = new ArrayList<>(JSON.parseArray(configValue1, Long.class));
        if (GeneralTool.isNotEmpty(fkDepartmentIds) && fkDepartmentIds.contains(fkDepartmentId) && GeneralTool.isEmpty(studentDto.getSharedPath())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }

//        ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.STUDENT_SHARED_PATH_REQUIRED.key).getData();
//        if (GeneralTool.isNotEmpty(configDto) && GeneralTool.isNotEmpty(configDto.getValue1())){
//            JSONObject jsonObject = JSONObject.parseObject(configDto.getValue1());
//            List<Long> fkDepartmentIds = Lists.newArrayList();
//            com.alibaba.fastjson.JSONArray jsonArray = null;
//            if (fkCompanyId.equals(2L)){
//                jsonArray = jsonObject.getJSONArray("GEA");
//            }else if (fkCompanyId.equals(3L)){
//                jsonArray = jsonObject.getJSONArray("IAE");
//            }else {
//                jsonArray = jsonObject.getJSONArray("OTHER");
//            }
//            if (GeneralTool.isNotEmpty(jsonArray)){
//                fkDepartmentIds = jsonArray.toJavaList(Long.class);
//            }
//            if (GeneralTool.isNotEmpty(fkDepartmentIds)&&fkDepartmentIds.contains(fkDepartmentId)&&GeneralTool.isEmpty(studentDto.getSharedPath())){
//                throw  new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
//            }
//        }


        //为转代理学生时，最高学历不必填。
        if (GeneralTool.isEmpty(studentDto.getFkInstitutionIdEducation()) && GeneralTool.isEmpty(studentDto.getEducationLevelType()) &&
                GeneralTool.isEmpty(studentDto.getFkInstitutionIdEducation2()) && GeneralTool.isEmpty(studentDto.getEducationLevelType2())
                && !(GeneralTool.isNotEmpty(studentDto.getConditionType()) && studentDto.getConditionType().contains("0"))) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("FILL_IN_STUDENT_EDUCATION_BACKGROUND"));
        }
        Student student = BeanCopyUtils.objClone(studentDto, Student::new);
        //学生名称去掉空格
        if (StringUtils.isNotBlank(student.getName())) {
            student.setName(student.getName().trim());
        }
        if (StringUtils.isNotBlank(student.getFirstName())) {
            student.setFirstName(student.getFirstName().replace(" ", "").trim());
        }
        if (StringUtils.isNotBlank(student.getLastName())) {
            student.setLastName(student.getLastName().replace(" ", "").trim());
        }
        utilService.updateUserInfoToEntity(student);
        studentMapper.insert(student);
        student.setNum(GetStringUtils.getStudentNum(student.getId()));
        studentMapper.updateById(student);

        if (GeneralTool.isNotEmpty(studentDto.getIdentifyFrom())){
            RStudentIdentify studentIdentify = new RStudentIdentify();
            studentIdentify.setFkStudentId(student.getId());
            studentIdentify.setIdentifyFrom(studentDto.getIdentifyFrom());
            utilService.setCreateInfo(studentIdentify);
            rStudentIdentifyMapper.insert(studentIdentify);
        }

        UserInfo user = GetAuthInfo.getUser();
        RStudentUuid studentUuidEntity = new RStudentUuid();
        studentUuidEntity.setFkStudentId(student.getId());
        studentUuidEntity.setFkStudentUuid(UUID.randomUUID().toString());
        studentUuidEntity.setGmtCreateUser(user.getLoginId());
        studentUuidEntity.setGmtCreate(new Date());
        rStudentUuidMapper.insert(studentUuidEntity);


        return student.getId();
    }

    @Override
    public ResponseBo getStudentPaginationInfo(StudentListQueryDto studentListQueryDto, Page page) {
        //设置当前登录人对应公司，统计跳转使用登录人对应公司
        studentListQueryDto.setFkCompanyId(SecureUtil.getFkCompanyId());
        if (GeneralTool.isEmpty(studentListQueryDto.getFkCompanyIds())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_id_null"));
        }
        Long staffId = SecureUtil.getStaffId();

        List<Long> staffFollowerIds = new ArrayList<>();
        //员工id + 业务下属员工ids
        List<Long> staffFollowerIdsResult = permissionCenterClient.getStaffFollowerIds(staffId).getData();
        if (GeneralTool.isNotEmpty(staffFollowerIdsResult)) {
            staffFollowerIds = staffFollowerIdsResult.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
        staffFollowerIds.add(staffId);
        //员工 + 业务下属员工 loginId
        Set<Long> ids = new HashSet<>(staffFollowerIds);
        Map<Long, String> longIdMap = permissionCenterClient.getStaffLoginIdByIds(ids).getData();
        List<String> userNames = new ArrayList<>(longIdMap.values());
        //业务国家
//        List<String> fkAreaCountryKeyList = permissionCenterClient.getStaffAreaCountryKeysByfkStaffId(staffId).getData();
//        List<Long> fkAreaCountryIds = institutionCenterClient.getCountryIdByKey(fkAreaCountryKeyList).getData();
        List<Long> fkAreaCountryIds = SecureUtil.getCountryIds();

        long var1 = System.currentTimeMillis();
        if (StringUtils.isNotBlank(studentListQueryDto.getName())) {
            studentListQueryDto.setName(studentListQueryDto.getName().replace(" ", "").trim());
        }

        //针对like的字段转为小写
        if (GeneralTool.isNotEmpty(studentListQueryDto.getName())) {
            studentListQueryDto.setName(studentListQueryDto.getName().toLowerCase());
        }
        if (GeneralTool.isNotEmpty(studentListQueryDto.getAgentName())) {
            studentListQueryDto.setAgentName(studentListQueryDto.getAgentName().toLowerCase());
        }
        if (GeneralTool.isNotEmpty(studentListQueryDto.getBdNameOrCode())) {
            studentListQueryDto.setBdNameOrCode(studentListQueryDto.getBdNameOrCode().toLowerCase());
        }
        //groupName
        if (GeneralTool.isNotEmpty(studentListQueryDto.getGroupName())) {
            studentListQueryDto.setGroupName(studentListQueryDto.getGroupName().toLowerCase());
        }
        //channelName
        if (GeneralTool.isNotEmpty(studentListQueryDto.getChannelName())) {
            studentListQueryDto.setChannelName(studentListQueryDto.getChannelName().toLowerCase());
        }

        //channelNames
        if (GeneralTool.isNotEmpty(studentListQueryDto.getChannelNames())) {
            studentListQueryDto.setChannelNames(studentListQueryDto.getChannelNames().stream().map(name -> name.toLowerCase()).collect(Collectors.toList()));
        }

        //email
        if (GeneralTool.isNotEmpty(studentListQueryDto.getEmail())) {
            studentListQueryDto.setEmail(studentListQueryDto.getEmail().toLowerCase());
        }
        //fkInstitutionNameCnEducation
        if (GeneralTool.isNotEmpty(studentListQueryDto.getFkInstitutionNameCnEducation())) {
            studentListQueryDto.setFkInstitutionNameCnEducation(studentListQueryDto.getFkInstitutionNameCnEducation().toLowerCase());
        }
        //fkInstitutionNameEducation
        if (GeneralTool.isNotEmpty(studentListQueryDto.getFkInstitutionNameEducation())) {
            studentListQueryDto.setFkInstitutionNameEducation(studentListQueryDto.getFkInstitutionNameEducation().toLowerCase());
        }
        //institutionName
        if (GeneralTool.isNotEmpty(studentListQueryDto.getInstitutionName())) {
            studentListQueryDto.setInstitutionName(studentListQueryDto.getInstitutionName().toLowerCase());
        }
        //courseName
        if (GeneralTool.isNotEmpty(studentListQueryDto.getCourseName())) {
            studentListQueryDto.setCourseName(studentListQueryDto.getCourseName().toLowerCase());
        }
        //oldCourseMajorLevelName
        if (GeneralTool.isNotEmpty(studentListQueryDto.getOldCourseMajorLevelName())) {
            studentListQueryDto.setOldCourseMajorLevelName(studentListQueryDto.getOldCourseMajorLevelName().toLowerCase());
        }
        //oldCourseMajorLevelNames
        if (GeneralTool.isNotEmpty(studentListQueryDto.getOldCourseMajorLevelNames())) {
            studentListQueryDto.setOldCourseMajorLevelNames(studentListQueryDto.getOldCourseMajorLevelNames().stream().map(name -> name.toLowerCase()).collect(Collectors.toList()));
        }
        //oldCourseTypeGroupNames
        if (GeneralTool.isNotEmpty(studentListQueryDto.getOldCourseTypeGroupNames())) {
            studentListQueryDto.setOldCourseTypeGroupNames(studentListQueryDto.getOldCourseTypeGroupNames().stream().map(name -> name.toLowerCase()).collect(Collectors.toList()));
        }
        //unionApplyCountryCount
        if (GeneralTool.isNotEmpty(studentListQueryDto.getUnionApplyCountryIds())) {
            studentListQueryDto.setUnionApplyCountryCount(studentListQueryDto.getUnionApplyCountryIds().size());
        }
        studentListQueryDto.setGeaConfirmationStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.GEA_CONFIRMATION_STATISTICS_STEP));
        studentListQueryDto.setGeaSuccessStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.GEA_SUCCESS_STATISTICS_STEP));
        studentListQueryDto.setIaeConfirmationStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.IAE_CONFIRMATION_STATISTICS_STEP));
        studentListQueryDto.setIaeSuccessStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.IAE_SUCCESS_STATISTICS_STEP));
        ConfigVo configVo = permissionCenterClient.getConfigByKey("HTI_BMS_START_TIME").getData();
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        Date htiStartTime;
        try {
            htiStartTime = sf.parse(configVo.getValue1());
        } catch (ParseException e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("DATE_FORMAT_CONVERSION_ERROR"));
        }
        //bd统计跳转专用时间
        Date jumpBeginTime = null;
        Date jumpEndTime = null;
        if (GeneralTool.isNotEmpty(studentListQueryDto.getBdStudentStatisticalComparisonVo())) {
            jumpBeginTime = studentListQueryDto.getBdStudentStatisticalComparisonVo().getStudentBeginTime();
        }
        if (GeneralTool.isNotEmpty(studentListQueryDto.getBdStudentStatisticalComparisonVo())) {
            jumpEndTime = studentListQueryDto.getBdStudentStatisticalComparisonVo().getStudentEndTime();
        }
        configVo = permissionCenterClient.getConfigByKey(ProjectKeyEnum.FINANCE_OPENING_TIME_COUNT.key).getData();
        Date beginOpenTime;
        Date endOpenTime;
        try {
            beginOpenTime = sf.parse(configVo.getValue1());
            endOpenTime = sf.parse(configVo.getValue2());
        } catch (ParseException e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("DATE_FORMAT_CONVERSION_ERROR"));
        }

        //查找失败步骤信息
        Boolean isBd = studentOfferService.getIsBd(SecureUtil.getStaffId());
        List<StudentVo> studentVos = studentMapper.getStudents(studentListQueryDto, staffFollowerIds, userNames, fkAreaCountryIds, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds(), ProjectKeyEnum.STEP_FAILURE.key, SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(), SecureUtil.getStaffInfo().getIsStudentAdmin(),
                studentListQueryDto.getCurrentStudentApplicationStatusStatisticsVo(), studentListQueryDto.getBdStudentStatisticalComparisonVo(), htiStartTime, jumpBeginTime, jumpEndTime, staffId, beginOpenTime, endOpenTime,new ArrayList<>(),SecureUtil.getInstitutionIds(),isBd, false);
        Integer totalCount = studentVos.get(0).getTotalCount();
        Integer showCount = page.getShowCount();
        page.setTotalPage(page.getShowCount() != null && showCount > 0 ? totalCount % showCount == 0 ? totalCount / showCount : totalCount / showCount + 1 : 0);
        page.setTotalResult(totalCount);
        long var2 = System.currentTimeMillis();
        Page<StudentVo> ipage = BeanCopyUtils.objClone(page, Page::new);
//        return new ResponseBo<>(BeanCopyUtils.objClone(page, Page::new), var2 - var1);

        CountVo countVo = new CountVo();
        Integer totalStudentOfferCount = studentMapper.getStudentOfferItemNumCount(studentListQueryDto, staffFollowerIds, userNames, fkAreaCountryIds, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds(), ProjectKeyEnum.STEP_FAILURE.key, SecureUtil.getStaffInfoByStaffId(staffId).getIsStudentOfferItemFinancialHiding(),
                SecureUtil.getStaffInfoByStaffId(staffId).getIsStudentAdmin(), studentListQueryDto.getCurrentStudentApplicationStatusStatisticsVo(), studentListQueryDto.getBdStudentStatisticalComparisonVo(), htiStartTime,
                jumpBeginTime, jumpEndTime, staffId, beginOpenTime, endOpenTime, new ArrayList<>(), SecureUtil.getInstitutionIds(), isBd, studentListQueryDto.getIsExport());

        countVo.setTotalStudentOfferItemCount(totalStudentOfferCount);

        return new ResponseBo(countVo,ipage,var2 - var1);
    }

    /**
     * 学生列表
     *
     * @param studentListQueryDto
     * @param times
     * @param staffId
     * @param local
     * @param fkAreaCountryIds
     * @return
     */
    @Override
    public List<StudentVo> getStudents(StudentListQueryDto studentListQueryDto, String[] times, Long staffId, String local, List<Long> fkAreaCountryIds) {
        //设置当前登录人对应公司，统计跳转使用登录人对应公司
        studentListQueryDto.setFkCompanyId(SecureUtil.getFkCompanyId());
        if (GeneralTool.isEmpty(studentListQueryDto.getFkCompanyIds())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_id_null"));
        }
        long startTime = System.currentTimeMillis();

        List<Long> staffFollowerIds = new ArrayList<>();
        //员工id + 业务下属员工ids
        List<Long> staffFollowerIdsResult = permissionCenterClient.getStaffFollowerIds(staffId).getData();
        if (GeneralTool.isNotEmpty(staffFollowerIdsResult)) {
            staffFollowerIds = staffFollowerIdsResult.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
        staffFollowerIds.add(staffId);

        long sqlBefore1 = System.currentTimeMillis();
        System.out.println("====>sqlBefore1:" + String.valueOf(sqlBefore1 - startTime));

        //员工 + 业务下属员工 loginId
        Set<Long> ids = new HashSet<>(staffFollowerIds);
        Map<Long, String> longIdMap = permissionCenterClient.getStaffLoginIdByIds(ids).getData();
        List<String> userNames = new ArrayList<>(longIdMap.values());
        //统计执行时间的时间戳
        long fStartTime = System.currentTimeMillis();
        //分页参数 起始点
        if (studentListQueryDto.getPageNumber() != null && studentListQueryDto.getPageNumber() > 0) {
            studentListQueryDto.setOffset((studentListQueryDto.getPageNumber() - 1) * studentListQueryDto.getPageSize());
        }


        if (StringUtils.isNotBlank(studentListQueryDto.getName())) {
            studentListQueryDto.setName(studentListQueryDto.getName().replace(" ", "").trim());
        }
        //针对like的字段转为小写
        if (GeneralTool.isNotEmpty(studentListQueryDto.getName())) {
            studentListQueryDto.setName(studentListQueryDto.getName().toLowerCase());
        }
        if (GeneralTool.isNotEmpty(studentListQueryDto.getAgentName())) {
            studentListQueryDto.setAgentName(studentListQueryDto.getAgentName().toLowerCase());
        }
        if (GeneralTool.isNotEmpty(studentListQueryDto.getBdNameOrCode())) {
            studentListQueryDto.setBdNameOrCode(studentListQueryDto.getBdNameOrCode().toLowerCase());
        }
        //groupName
        if (GeneralTool.isNotEmpty(studentListQueryDto.getGroupName())) {
            studentListQueryDto.setGroupName(studentListQueryDto.getGroupName().toLowerCase());
        }
        //channelName
        if (GeneralTool.isNotEmpty(studentListQueryDto.getChannelName())) {
            studentListQueryDto.setChannelName(studentListQueryDto.getChannelName().toLowerCase());
        }
        //channelNames
        if (GeneralTool.isNotEmpty(studentListQueryDto.getChannelNames())) {
            List<String> channelNames = studentListQueryDto.getChannelNames().stream().map(String::toLowerCase).collect(Collectors.toList());
            studentListQueryDto.setChannelNames(channelNames);
        }
        //email
        if (GeneralTool.isNotEmpty(studentListQueryDto.getEmail())) {
            studentListQueryDto.setEmail(studentListQueryDto.getEmail().toLowerCase());
        }
        //fkInstitutionNameCnEducation
        if (GeneralTool.isNotEmpty(studentListQueryDto.getFkInstitutionNameCnEducation())) {
            studentListQueryDto.setFkInstitutionNameCnEducation(studentListQueryDto.getFkInstitutionNameCnEducation().toLowerCase());
        }
        //fkInstitutionNameEducation
        if (GeneralTool.isNotEmpty(studentListQueryDto.getFkInstitutionNameEducation())) {
            studentListQueryDto.setFkInstitutionNameEducation(studentListQueryDto.getFkInstitutionNameEducation().toLowerCase());
        }
        //institutionName
        if (GeneralTool.isNotEmpty(studentListQueryDto.getInstitutionName())) {
            studentListQueryDto.setInstitutionName(studentListQueryDto.getInstitutionName().toLowerCase());
        }
        //courseName
        if (GeneralTool.isNotEmpty(studentListQueryDto.getCourseName())) {
            studentListQueryDto.setCourseName(studentListQueryDto.getCourseName().toLowerCase());
        }
        //oldCourseMajorLevelName
        if (GeneralTool.isNotEmpty(studentListQueryDto.getOldCourseMajorLevelName())) {
            studentListQueryDto.setOldCourseMajorLevelName(studentListQueryDto.getOldCourseMajorLevelName().toLowerCase());
        }
        //oldCourseMajorLevelNames
        if (GeneralTool.isNotEmpty(studentListQueryDto.getOldCourseMajorLevelNames())) {
            studentListQueryDto.setOldCourseMajorLevelNames(studentListQueryDto.getOldCourseMajorLevelNames().stream().map(String::toLowerCase).collect(Collectors.toList()));
        }
        //oldCourseTypeGroupNames
        if (GeneralTool.isNotEmpty(studentListQueryDto.getOldCourseTypeGroupNames())) {
            studentListQueryDto.setOldCourseTypeGroupNames(studentListQueryDto.getOldCourseTypeGroupNames().stream().map(String::toLowerCase).collect(Collectors.toList()));
        }
        //remarkComment
        if (GeneralTool.isNotEmpty(studentListQueryDto.getRemarkComment())) {
            studentListQueryDto.setRemarkComment(studentListQueryDto.getRemarkComment().toLowerCase());
        }
        //unionApplyCountryCount
        if (GeneralTool.isNotEmpty(studentListQueryDto.getUnionApplyCountryIds())) {
            studentListQueryDto.setUnionApplyCountryCount(studentListQueryDto.getUnionApplyCountryIds().size());
        }

        if (GeneralTool.isNotEmpty(studentListQueryDto.getInstitutionIsKpi())) {
            //kpi排除学校列表
            StringBuilder fkInstitutionIdsExcluding = new StringBuilder();
            List<KpiInstitutionProvider> kpiInstitutionProviders = kpiInstitutionProviderMapper.selectList(new LambdaQueryWrapper<KpiInstitutionProvider>().isNotNull(KpiInstitutionProvider::getFkInstitutionIdsExcluding));
            for (KpiInstitutionProvider kpiInstitutionProvider : kpiInstitutionProviders) {
                if (GeneralTool.isBlank(fkInstitutionIdsExcluding)) {
                    fkInstitutionIdsExcluding.append(kpiInstitutionProvider.getFkInstitutionIdsExcluding());
                } else {
                    fkInstitutionIdsExcluding.append(",").append(kpiInstitutionProvider.getFkInstitutionIdsExcluding());
                }
            }
            if (GeneralTool.isNotBlank(fkInstitutionIdsExcluding)) {
                studentListQueryDto.setFkInstitutionIdsExcluding(fkInstitutionIdsExcluding.toString());
            }
        }


        //业绩跳转
        studentListQueryDto.setGeaConfirmationStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.GEA_CONFIRMATION_STATISTICS_STEP));
        studentListQueryDto.setGeaSuccessStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.GEA_SUCCESS_STATISTICS_STEP));
        studentListQueryDto.setIaeConfirmationStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.IAE_CONFIRMATION_STATISTICS_STEP));
        studentListQueryDto.setIaeSuccessStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.IAE_SUCCESS_STATISTICS_STEP));

        //Bd统计表跳转配置参数
        ConfigVo configVo = permissionCenterClient.getConfigByKey("HTI_BMS_START_TIME").getData();
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        Date htiStartTime;
        try {
            htiStartTime = sf.parse(configVo.getValue1());
        } catch (ParseException e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("DATE_FORMAT_CONVERSION_ERROR"));
        }

        //bd统计跳转专用时间
        Date jumpBeginTime = null;
        Date jumpEndTime = null;
        if (GeneralTool.isNotEmpty(studentListQueryDto.getBdStudentStatisticalComparisonVo())) {
            jumpBeginTime = studentListQueryDto.getBdStudentStatisticalComparisonVo().getStudentBeginTime();
        }
        if (GeneralTool.isNotEmpty(studentListQueryDto.getBdStudentStatisticalComparisonVo())) {
            jumpEndTime = studentListQueryDto.getBdStudentStatisticalComparisonVo().getStudentEndTime();
        }

        configVo = permissionCenterClient.getConfigByKey(ProjectKeyEnum.FINANCE_OPENING_TIME_COUNT.key).getData();
        Date beginOpenTime;
        Date endOpenTime;
        try {
            beginOpenTime = sf.parse(configVo.getValue1());
            endOpenTime = sf.parse(configVo.getValue2());
        } catch (ParseException e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("DATE_FORMAT_CONVERSION_ERROR"));
        }

        boolean staffInfoFlag = GeneralTool.isNotEmpty(SecureUtil.getStaffInfoByStaffId(staffId));

        List<StudentVo> collect = Lists.newArrayList();
        Boolean isBd = studentOfferService.getIsBd(SecureUtil.getStaffId());
        long datasStartTime = System.currentTimeMillis();
        if (staffInfoFlag) {
            if (studentListQueryDto.getIsExport()) {
                collect = studentMapper.getExportStudents(studentListQueryDto, staffFollowerIds, userNames, fkAreaCountryIds, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds(), ProjectKeyEnum.STEP_FAILURE.key, SecureUtil.getStaffInfoByStaffId(staffId).getIsStudentOfferItemFinancialHiding(),
                        SecureUtil.getStaffInfoByStaffId(staffId).getIsStudentAdmin(), studentListQueryDto.getCurrentStudentApplicationStatusStatisticsVo(), studentListQueryDto.getBdStudentStatisticalComparisonVo(), htiStartTime,
                        jumpBeginTime, jumpEndTime, staffId, beginOpenTime, endOpenTime, new ArrayList<>(), SecureUtil.getInstitutionIds(), isBd, studentListQueryDto.getIsExport());
            } else {
                collect = studentMapper.getStudents(studentListQueryDto, staffFollowerIds, userNames, fkAreaCountryIds, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds(), ProjectKeyEnum.STEP_FAILURE.key, SecureUtil.getStaffInfoByStaffId(staffId).getIsStudentOfferItemFinancialHiding(),
                        SecureUtil.getStaffInfoByStaffId(staffId).getIsStudentAdmin(), studentListQueryDto.getCurrentStudentApplicationStatusStatisticsVo(), studentListQueryDto.getBdStudentStatisticalComparisonVo(), htiStartTime,
                        jumpBeginTime, jumpEndTime, staffId, beginOpenTime, endOpenTime, new ArrayList<>(), SecureUtil.getInstitutionIds(), isBd, studentListQueryDto.getIsExport());

            }
        }
        long datasEndTime = System.currentTimeMillis();
        System.out.println("主sql花费的秒：" + (datasEndTime - datasStartTime) / 1000);

        long fEndTime = System.currentTimeMillis();

        if (GeneralTool.isNotEmpty(collect)) {
            Map<String, String> companyMap = getCompanyMap();
            List<Long> studentIds = collect.stream().map(StudentVo::getId).collect(Collectors.toList());
            Map<Long, StudentItemStatusVo> studentItemStatusMap = new HashMap<>();
            if (staffInfoFlag && !studentListQueryDto.getIsExport()) {
                //根据学生ids查询学生的最高最低状态
                List<StudentItemStatusVo> studentItemStatus = studentMapper.getStudentItemStatus(studentIds, studentListQueryDto,
                        staffFollowerIds, fkAreaCountryIds, ProjectKeyEnum.STEP_FAILURE.key,
                        SecureUtil.getStaffInfoByStaffId(staffId).getIsStudentOfferItemFinancialHiding(),
                        SecureUtil.getStaffInfoByStaffId(staffId).getIsStudentAdmin(),
                        staffId, beginOpenTime, endOpenTime, isBd,
                        SecureUtil.getPermissionGroupInstitutionIds(),
                        SecureUtil.getStaffBoundBdIds());
                studentItemStatusMap.putAll(studentItemStatus.stream().collect(Collectors.toMap(StudentItemStatusVo::getId, studentItemStatusVo -> studentItemStatusVo)));
            }
            long endTime2 = System.currentTimeMillis();
            //System.out.println("====StudentVo执行时间==="+(endTime2-startTime2));

            //根据学生ids查询代理和BD
            long one = System.currentTimeMillis();
            List<Map<String, Object>> studentAgentBdList = studentOfferItemMapper.getAgentBdByStudentId(studentIds);
            long tow = System.currentTimeMillis();
            System.out.println("----------------根据学生ids查询代理和BD花费的秒：" + (tow - one) / 1000);
            studentAgentBdList.removeIf(Objects::isNull);
            //获取学习计划状态变更最新时间
            long one2 = System.currentTimeMillis();
            List<Map<String, Object>> statusChangeLastTimeList = studentOfferItemMapper.getStatusChangeLastTime(studentIds);
            long tow2 = System.currentTimeMillis();
            System.out.println("----------------获取学习计划状态变更最新时间花费的秒：" + (tow2 - one2) / 1000);
            statusChangeLastTimeList.removeIf(Objects::isNull);
            Set<Long> fkAgentIds = new HashSet<>();

            long sqlAfter1 = System.currentTimeMillis();
            System.out.println("====>sqlAfter1:" + String.valueOf(sqlAfter1 - fEndTime));

            //获取所有代理ids、员工ids
            for (Map<String, Object> studentAgentBd : studentAgentBdList) {
                if (GeneralTool.isNotEmpty(studentAgentBd.get("fkAgentId"))) {
                    fkAgentIds.add(Long.valueOf(studentAgentBd.get("fkAgentId").toString()));
                }
            }
            //根据代理ids获取名称
            Map<Long, Agent> agentNamesByIds = new HashMap<>();
            Map<Long, List<AgentLabelVo>> agentLabelMap = getAgentLabelDataUtils.getAgentLabelMap(fkAgentIds).getAgentLabelMap();
            if (GeneralTool.isNotEmpty(fkAgentIds)) {
                agentNamesByIds.putAll(agentService.getAgentsByIds(fkAgentIds));
            }
            //根据员工ids获取姓名
            Map<Long, String> staffNamesByIds = new HashMap<>();
            Set<Long> allStaffIds = permissionCenterClient.getAllStaffIds();
            staffNamesByIds.putAll(permissionCenterClient.getStaffNamesByIds(allStaffIds));

            //获取步骤id和步骤名
            List<StudentOfferItemStep> studentOfferItemSteps = studentOfferItemStepMapper.selectList(Wrappers.<StudentOfferItemStep>lambdaQuery());
            Map<Integer, String> studentOfferItemStepMap = studentOfferItemSteps.stream().collect(Collectors.toMap(StudentOfferItemStep::getStepOrder, StudentOfferItemStep::getStepName));
            //推荐来源Map
            Set<Long> clientIds = collect.stream().map(StudentVo::getFkClientId).collect(Collectors.toSet());
            Map<Long, StudentClientSourceDto> studentClientSourceDtoMap = getClientSourceMap(clientIds);

            //获取学生集合的所有毕业院校ids
            Set<Long> institutionIds = collect.stream().map(StudentVo::getFkInstitutionIdEducation).collect(Collectors.toSet());
            institutionIds.addAll(collect.stream().map(StudentVo::getFkInstitutionIdEducation2).collect(Collectors.toSet()));
            //学生id ： 学习计划List
            Map<Long, List<StudentOfferItemVo>> studentOfferItemMap = new HashMap<>();
            //获取学生申请的学校
            Set<Long> studentIdSet = collect.stream().map(StudentVo::getId).collect(Collectors.toSet());
            //提成激活状态
            Map<Long, Boolean> commissionActiveStatusMap = getCommissionActiveStatusByStudentIds(studentIdSet);
            if (GeneralTool.isNotEmpty(studentIdSet) && !studentListQueryDto.getIsExport()) {
                List<StudentOfferItemVo> studentOfferItemVos = Lists.newArrayList();
                if (staffInfoFlag) {
                    studentOfferItemVos = studentOfferItemMapper.selectOfferItemByStudentIds(studentIdSet, SecureUtil.getStaffInfoByStaffId(staffId).getIsStudentOfferItemFinancialHiding());
                }
                for (StudentOfferItemVo studentOfferItemVo : studentOfferItemVos) {
                    List<StudentOfferItemVo> studentOfferItemVoList = studentOfferItemMap.get(studentOfferItemVo.getFkStudentId());
                    if (GeneralTool.isEmpty(studentOfferItemVoList)) {
                        studentOfferItemVoList = new ArrayList<>();
                    }
                    studentOfferItemVoList.add(studentOfferItemVo);
                    if (GeneralTool.isNotEmpty(studentOfferItemVo.getFkInstitutionId())) {
                        institutionIds.add(studentOfferItemVo.getFkInstitutionId());
                    }
                    studentOfferItemMap.put(studentOfferItemVo.getFkStudentId(), studentOfferItemVoList);
                }
            }
            long sqlAfter2 = System.currentTimeMillis();
            System.out.println("====>sqlAfter2:" + String.valueOf(sqlAfter2 - sqlAfter1));

            //根据州省ids获取州省名称
            Map<Long, String> stateNamesByIds = institutionCenterClient.getStateFullNames().getData();
            //根据城市ids获取城市名称
            Map<Long, String> cityNamesByIds = institutionCenterClient.getCityFullNames().getData();
            //根据学校ids获取学校名称
            Map<Long, String> institutionNamesByIds = new HashMap<>();
            if (GeneralTool.isNotEmpty(institutionIds)) {
                institutionNamesByIds.putAll(institutionCenterClient.getInstitutionNamesByIds(institutionIds).getData());
            }
            //获取所有学生的角色员工集合
            Map<Long, String> projectRoleStaffMap = new HashMap<>();
            List<Map<Long, String>> projectRoleStaffList = projectRoleStaffService.getProjectRoleStaffNoPage(TableEnum.SALE_STUDENT_OFFER.key, studentIds);
            for (Map<Long, String> map : projectRoleStaffList) {
                List agentStaff = new ArrayList(map.values());
                if (agentStaff.size() < 2) {
                    continue;
                }
                String value = (String) agentStaff.get(0);
                Long key = (Long) agentStaff.get(1);

                projectRoleStaffMap.put(key, value);
            }

            long sqlAfter3 = System.currentTimeMillis();
            System.out.println("====>sqlAfter3:" + String.valueOf(sqlAfter3 - sqlAfter2));

            List<StudentOffer> studentOffers = new ArrayList<>();
            //方案代理
            Map<Long, Agent> offerAgentNamesByIds = new HashMap<>();
            //根据国家ids获取国家名称
            Map<Long, String> countryNamesByIds = institutionCenterClient.getCountryNameMap().getData();
            if (GeneralTool.isNotEmpty(studentIds)) {
                studentOffers = studentOfferService.getStudentOffersByStudentIds(studentIds);
                Set<Long> agentIds = studentOffers.stream().map(StudentOffer::getFkAgentId).collect(Collectors.toSet());
                offerAgentNamesByIds.putAll(agentService.getAgentsByIds(agentIds));
            }
            // 在循环外预先处理studentOffers
            Map<Long, List<StudentOffer>> studentOffersMap = studentOffers.stream()
                    .collect(Collectors.groupingBy(StudentOffer::getFkStudentId));

            long one3 = System.currentTimeMillis();
            // 创建虚拟线程池（根据实际情况调整核心线程数）
            ThreadPoolExecutor executorService = (ThreadPoolExecutor) Executors.newFixedThreadPool(10);
            // 根据系统负载动态调整线程池大小
            double systemLoad = ManagementFactory.getOperatingSystemMXBean().getSystemLoadAverage();
            if (systemLoad > 0.7) {
                executorService.setCorePoolSize((int) (executorService.getCorePoolSize() * 1.5));
            } else if (systemLoad < 0.3) {
                executorService.setCorePoolSize(Math.max(executorService.getCorePoolSize() / 2, 1));
            }
            List<CompletableFuture<Void>> futures = collect.stream()
                    .map(studentVo -> CompletableFuture.runAsync(() -> {
                        studentVo.setIsComplexEducation(GeneralTool.isNotEmpty(studentVo.getIsComplexEducation()) && studentVo.getIsComplexEducation());
                        if (GeneralTool.isNotEmpty(studentItemStatusMap)) {
                            StudentItemStatusVo studentItemStatusVo = studentItemStatusMap.get(studentVo.getId());
                            if (GeneralTool.isNotEmpty(studentItemStatusVo)) {
                                studentVo.setMaxStepOrder(studentItemStatusVo.getMaxStepOrder());
                                studentVo.setMinStepOrder(studentItemStatusVo.getMinStepOrder());
                            }
                        }

                        List<StudentOffer> studentOffersFor = studentOffersMap.getOrDefault(studentVo.getId(), Collections.emptyList());
                        setNameList(
                                companyMap, studentVo, studentOffersFor, countryNamesByIds, stateNamesByIds,
                                cityNamesByIds, institutionNamesByIds, studentAgentBdList, agentNamesByIds, staffNamesByIds,
                                statusChangeLastTimeList, studentOfferItemMap, projectRoleStaffMap, local,
                                studentOfferItemStepMap, offerAgentNamesByIds,
                                studentClientSourceDtoMap, agentLabelMap
                        );
                        studentVo.setIsActiveCommission(commissionActiveStatusMap.get(studentVo.getId()));
                    }, executorService))
                    .collect(Collectors.toList());
            // 等待所有异步任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            // 关闭线程池（注意：通常在应用关闭时才需要执行）
            executorService.shutdown();
            long tow3 = System.currentTimeMillis();
            System.out.println("----------------for循环花费的秒：" + (tow3 - one3) / 1000);


            long sqlAfter4 = System.currentTimeMillis();
            System.out.println("====>sqlAfter4:" + String.valueOf(sqlAfter4 - sqlAfter3));
        }
        long endTime = System.currentTimeMillis();
        if (GeneralTool.isNotEmpty(times)) {
            times[0] = String.valueOf((fEndTime - fStartTime));
            times[1] = String.valueOf((endTime - startTime) - (fEndTime - fStartTime));
        }



        return collect;
    }

    /**
     * 获取推荐来源
     * @param clientIds
     * @return
     */
    private Map<Long, StudentClientSourceDto> getClientSourceMap(Set<Long> clientIds) {

        Map<Long, StudentClientSourceDto> map = new HashMap<>();
        Map<Long, List<ClientSource>> clientSourceMap = new HashMap<>();
        Map<Long, String> agentNamesByIds = new HashMap<>();
        Map<Long, String> staffNamesByIds = new HashMap<>();
        Map<Long, String> businessProviderNameMap = Maps.newHashMap();
        if (GeneralTool.isEmpty(clientIds)) {
            return map;
        }

        List<Client> clients = clientMapper.selectBatchIds(clientIds);

        List<ClientSource> clientSources = clientSourceMapper.selectList(Wrappers.<ClientSource>lambdaQuery().in(ClientSource::getFkClientId, clientIds));
        if (GeneralTool.isNotEmpty(clientSources)) {
            Set<Long> agentIds = clientSources.stream().map(ClientSource::getFkAgentId).collect(Collectors.toSet());
            Set<Long> staffIds = clientSources.stream().map(ClientSource::getFkStaffId).collect(Collectors.toSet());
            agentNamesByIds = agentService.getAgentNamesByIds(agentIds);
            staffNamesByIds = permissionCenterClient.getStaffNamesByIds(staffIds);
            clientSourceMap = clientSources.stream().collect(Collectors.groupingBy(ClientSource::getFkClientId));
        }

        Set<Long> businessProviderIds = clientSources.stream().filter(
                        dto -> ProjectKeyEnum.CLIENT_SOURCE_TYPE_BUSINESS_PROVIDER.key.equals(dto.getFkTableName()))
                .map(ClientSource::getFkTableId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(businessProviderIds)) {
            List<BusinessProvider> businessProviders = businessProviderMapper.selectBatchIds(businessProviderIds);
            if (GeneralTool.isNotEmpty(businessProviders)) {
                businessProviderNameMap = businessProviders.stream().collect(Collectors.toMap(BusinessProvider::getId, businessProvider -> {
                    if (GeneralTool.isNotEmpty(businessProvider.getName())) {
                        if (GeneralTool.isNotEmpty(businessProvider.getNameChn())) {
                            return businessProvider.getName() + "(" + businessProvider.getNameChn() + ")";
                        }
                        return businessProvider.getName();
                    } else if (GeneralTool.isNotEmpty(businessProvider.getNameChn())) {
                        return businessProvider.getNameChn();
                    }
                    return "";
                }));
            }
        }
        for (Client client : clients) {
            StudentClientSourceDto studentClientSourceDto = new StudentClientSourceDto();
            if (GeneralTool.isNotEmpty(clientSourceMap) && clientSourceMap.containsKey(client.getId())) {
                studentClientSourceDto.setStudentSource(clientSourceMap.get(client.getId()).get(0).getFkTableValue());
                studentClientSourceDto.setClientSourceType(clientSourceMap.get(client.getId()).get(0).getFkTableName());
                // 翻译推荐来源类型名称
                if (GeneralTool.isNotEmpty(studentClientSourceDto.getClientSourceType())) {
                    studentClientSourceDto.setFkTableNameValue(ProjectKeyEnum.getInitialValue(studentClientSourceDto.getClientSourceType()));
                }
                if (ProjectKeyEnum.CLIENT_SOURCE_TYPE_BUSINESS_PROVIDER.key.equals(clientSourceMap.get(client.getId()).get(0).getFkTableName())) {
                    studentClientSourceDto.setSourceBusinessProviderName(businessProviderNameMap.get(clientSourceMap.get(client.getId()).get(0).getFkTableId()));
                }

                if (GeneralTool.isNotEmpty(agentNamesByIds) && clientSourceMap.get(client.getId()).get(0).getFkAgentId() != null) {
                    studentClientSourceDto.setSourceAgentName(agentNamesByIds.get(clientSourceMap.get(client.getId()).get(0).getFkAgentId()));
                }
                if (GeneralTool.isNotEmpty(staffNamesByIds) && clientSourceMap.get(client.getId()).get(0).getFkStaffId() != null) {
                    studentClientSourceDto.setSourceBdName(staffNamesByIds.get(clientSourceMap.get(client.getId()).get(0).getFkStaffId()));
                }
            }
            map.put(client.getId(), studentClientSourceDto);
        }
        return map;
    }

    /**
     * 获取对应申请步骤的ids
     *
     * @param states
     * @return
     */
    private Set<Long> getStateList(Set<Long> states) {
        states.removeIf(Objects::isNull);
        if (GeneralTool.isNotEmpty(states)) {
//            Example example = new Example(StudentOfferItemStep.class);
//            example.createCriteria().andIn("stepOrder", states);
//            return studentOfferItemStepMapper.selectByExample(example).stream().map(StudentOfferItemStep::getId).collect(Collectors.toSet());
            return studentOfferItemStepMapper.selectList(Wrappers.<StudentOfferItemStep>lambdaQuery().in(StudentOfferItemStep::getStepOrder, states)).stream().map(StudentOfferItemStep::getId).collect(Collectors.toSet());
        }
        return null;
    }

    @Override
    public List<StudentVo> getStudentsByIds(StudentDto studentDto, List<Long> studentIdList) {
        if (GeneralTool.isEmpty(studentDto)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<Student> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        List<Long> studentIdList = getStudentIdList(null);
//        List<Long> ids = studentIdList.stream().distinct().collect(Collectors.toList());
//        if (GeneralTool.isEmpty(ids)) {
//            ids.add(0L);
//        }
//        lambdaQueryWrapper.in(Student::getId,ids);
//        criteria.andCondition("id in " + MyStringUtils.getSqlString(ids));

        //条件查询
        if (GeneralTool.isNotEmpty(studentDto.getFkCompanyId())) {
            if (!SecureUtil.validateCompany(studentDto.getFkCompanyId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
            lambdaQueryWrapper.eq(Student::getFkCompanyId, studentDto.getFkCompanyId());
        }
        if (GeneralTool.isNotEmpty(studentDto.getFkAreaCountryId())) {
            lambdaQueryWrapper.eq(Student::getFkAreaCountryId, studentDto.getFkAreaCountryId());
        }
        if (GeneralTool.isNotEmpty(studentDto.getFkAreaStateId())) {
            lambdaQueryWrapper.eq(Student::getFkAreaStateId, studentDto.getFkAreaStateId());
        }
        if (GeneralTool.isNotEmpty(studentDto.getFkAreaCityId())) {
            lambdaQueryWrapper.eq(Student::getFkAreaCityId, studentDto.getFkAreaCityId());
        }
        if (GeneralTool.isNotEmpty(studentDto.getBeginTime())) {
            lambdaQueryWrapper.ge(Student::getGmtCreate, studentDto.getBeginTime());
        }
        if (GeneralTool.isNotEmpty(studentDto.getEndTime())) {
            lambdaQueryWrapper.le(Student::getGmtCreate, studentDto.getBeginTime());
        }
        if (GeneralTool.isNotEmpty(studentDto.getNum())) {
            lambdaQueryWrapper.like(Student::getNum, studentDto.getNum());
        }
        if (GeneralTool.isNotEmpty(studentDto.getName())) {
            lambdaQueryWrapper.and(wrapper_ ->
                    wrapper_.like(Student::getName, studentDto.getName())
                            .or().like(Student::getLastName, studentDto.getName())
                            .or().like(Student::getFirstName, studentDto.getName()));
        }
        if (GeneralTool.isNotEmpty(studentDto.getTel())) {
            lambdaQueryWrapper.like(Student::getTel, studentDto.getTel());
        }
        if (GeneralTool.isNotEmpty(studentDto.getEmail())) {
            lambdaQueryWrapper.like(Student::getEmail, studentDto.getEmail());
        }
        if (GeneralTool.isNotEmpty(studentDto.getAgentName())) {
            //根据代理名称模糊查询学生
            List<Long> studentIds = studentAgentService.getRelationByAgentName(studentDto.getAgentName());
            if (GeneralTool.isEmpty(studentIds)) {
                studentIds = new ArrayList<>();
                studentIds.add(0L);
            }
//            lambdaQueryWrapper.andCondition("id in " + MyStringUtils.getSqlString(studentIds));
            lambdaQueryWrapper.in(Student::getId, studentIds);
//            criteria.andIn("id", studentIds);
        }
        if (GeneralTool.isNotEmpty(studentDto.getFkAgentId())) {
            //根据代理id模糊查询学生
            List<Long> studentIds = studentAgentService.getRelationByAgentId(studentDto.getFkAgentId());
            if (GeneralTool.isEmpty(studentIds)) {
                studentIds = new ArrayList<>();
                studentIds.add(0L);
            }
//            criteria.andCondition("id in " + MyStringUtils.getSqlString(studentIds));
            lambdaQueryWrapper.in(Student::getId, studentIds);
//            criteria.andIn("id", studentIds);
        }
        if (GeneralTool.isNotEmpty(studentDto.getBdNameOrCode())) {
            //根据BD名称或者编号模糊查询学生id
            List<Long> studentIds = studentOfferService.getStudentIdByStaffNameOrCode(studentDto.getBdNameOrCode());
            if (GeneralTool.isEmpty(studentIds)) {
                studentIds = new ArrayList<>();
                studentIds.add(0L);
            }
//            criteria.andCondition("id in " + MyStringUtils.getSqlString(studentIds));
            lambdaQueryWrapper.in(Student::getId, studentIds);
//            criteria.andIn("id", studentIds);
        }
        //项目角色不为空或者角色名称不为空，继续添加学生id
        lambdaQueryWrapper.orderByDesc(Student::getGmtCreate);

        List<StudentVo> collect = new ArrayList<>();
        //如果下面的条件有一条不为空，才进行第二次筛选
        if (GeneralTool.isNotEmpty(studentDto.getTargetCountryId())
                || GeneralTool.isNotEmpty(studentDto.getFailureReasonId())
                || GeneralTool.isNotEmpty(studentDto.getIsDeferEntrance())
                || GeneralTool.isNotEmpty(studentDto.getStatusBeginTime())
                || GeneralTool.isNotEmpty(studentDto.getStatusEndTime())
                || GeneralTool.isNotEmpty(studentDto.getState())
                || GeneralTool.isNotEmpty(studentDto.getBeginOpeningTime())
                || GeneralTool.isNotEmpty(studentDto.getEndOpeningTime())
        ) {
            //需要不分页查询一次学生，然后再进行第二次筛选
            List<Student> students = studentMapper.selectList(lambdaQueryWrapper);
            //如果为空，直接结束程序
            if (GeneralTool.isEmpty(students)) {
                return collect;
            }
            collect = students.stream().map(student -> BeanCopyUtils.objClone(student, StudentVo::new)).collect(Collectors.toList());
            //获取其他条件筛选后的学生ID，然后再进行目标国家的筛选
            Set<Long> studentIds = collect.stream().map(StudentVo::getId).collect(Collectors.toSet());
            //前面筛选的条件查询不出学生，就不进行第二次筛选
            if (GeneralTool.isNotEmpty(studentIds)) {
                Set<Long> stateList = null;
                Set<Long> states;
                if (GeneralTool.isNotEmpty(studentDto.getState())) {
//                    String[] split = studentDto.getState().split(",");
                    states = studentDto.getState().stream().collect(Collectors.toSet());
                    stateList = getStateList(states);
                }
                collect = studentMapper.getStudentsByTargetCountryAndStudentIds(null,
                        studentIds,
                        GeneralTool.isEmpty(studentDto.getTargetCountryId()) ? null : studentDto.getTargetCountryId(),
                        GeneralTool.isEmpty(studentDto.getFailureReasonId()) ? null : studentDto.getFailureReasonId(),
                        GeneralTool.isEmpty(studentDto.getIsDeferEntrance()) ? null : studentDto.getIsDeferEntrance(),
                        GeneralTool.isEmpty(studentDto.getStatusBeginTime()) ? null : studentDto.getStatusBeginTime(),
                        GeneralTool.isEmpty(studentDto.getStatusEndTime()) ? null : studentDto.getStatusEndTime(),
                        GeneralTool.isEmpty(studentDto.getBeginOpeningTime()) ? null : studentDto.getBeginOpeningTime(),
                        GeneralTool.isEmpty(studentDto.getEndOpeningTime()) ? null : studentDto.getEndOpeningTime(),
                        GeneralTool.isEmpty(studentDto.getTargetCountryIdList()) ? null : studentDto.getTargetCountryIdList(),
                        stateList);
            }
        } else {
            List<Student> students = studentMapper.selectList(lambdaQueryWrapper);
            collect = students.stream().map(student -> BeanCopyUtils.objClone(student, StudentVo::new)).collect(Collectors.toList());
        }
        return collect;
    }

    /**
     * 更新学生申请状态
     *
     * @Date 12:00 2021/7/8
     * <AUTHOR>
     */
    private void setStepNameAndReasonName(List<Map<String, Object>> studentMinStepList, List<Map<String, Object>> itemStepList, StudentVo studentVo) {
        Long studentId = studentVo.getId();

        for (Map<String, Object> studentMinStepMap : studentMinStepList) {
            if (studentMinStepMap.get("studentId").equals(studentId) && GeneralTool.isNotEmpty(studentMinStepMap.get("stepOrder"))) {
                for (Map<String, Object> itemStepMap : itemStepList) {
                    Long steOrder = Long.valueOf(itemStepMap.get("steOrder").toString());
                    if (steOrder.equals(Long.valueOf(studentMinStepMap.get("stepOrder").toString()))) {
                        studentVo.setStepName(itemStepMap.get("stepName").toString());
                    }
                }
            }
        }

        //降序
//        HashSet<Long> set = new HashSet<>();
//        for (Map<String, Object> studentMinStepMap : studentMinStepList) {
//            if (studentMinStepMap.get("studentId").equals(studentId) && GeneralTool.isNotEmpty(studentMinStepMap.get("stepOrder"))) {
//                set.add(Long.valueOf(studentMinStepMap.get("stepOrder").toString()));
//            }
//        }
//        StringJoiner stepName = new StringJoiner(",");
        StringJoiner reasonName = new StringJoiner(",");
        Set<String> reasonNameSet = new HashSet<>();
//        if (GeneralTool.isNotEmpty(set)) {
//            //排序，从最小开始
//            TreeSet<Long> stepOrder = new TreeSet(set);
//            for (Long aLong : stepOrder) {
//                for (Map<String, Object> itemStepMap : itemStepList) {
//                    Long steOrder = Long.valueOf(itemStepMap.get("steOrder").toString());
//                    if (steOrder.equals(aLong)) {
//                        stepName.add(itemStepMap.get("stepName").toString());
//                        break;
//                    }
//                }
//            }
//            //学生申请状态
//            studentVo.setStepName(stepName.toString());
//        }

        //失败原因
        for (Map<String, Object> studentMinStepMap : studentMinStepList) {
            if (studentMinStepMap.get("studentId").equals(studentId) && GeneralTool.isNotEmpty(studentMinStepMap.get("reasonName"))) {
                reasonNameSet.add(studentMinStepMap.get("reasonName").toString());
            }
        }
        for (String s : reasonNameSet) {
            reasonName.add(s);
        }
        studentVo.setReasonName(reasonName.toString());
    }

    @Override
    public StudentVo findStudentById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        verifyDataPermissionsUtils.verifyByBusinessId(id, VerifyDataPermissionsUtils.STUDENT_O);
        Student student = studentMapper.selectById(id);
        if (GeneralTool.isNotEmpty(student)) {
            if (!SecureUtil.validateCompany(student.getFkCompanyId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        } else {
            return null;//找不到就返回
        }
        StudentVo studentVo = BeanCopyUtils.objClone(student, StudentVo::new);
        Map<String, String> companyMap = getCompanyMap();

        Set<Long> countryIds = new HashSet<>();
        countryIds.add(studentVo.getFkAreaCountryId());
        countryIds.add(studentVo.getFkAreaCountryIdNationality());
        countryIds.add(studentVo.getFkAreaCountryIdGreenCard());
        countryIds.add(studentVo.getFkAreaCountryIdEducation());
        countryIds.add(studentVo.getFkAreaCountryIdEducation2());
        countryIds.add(studentVo.getFkAreaCountryIdBirth());
        countryIds.add(studentVo.getFkAreaCountryIdPassport());
        countryIds = countryIds.stream().filter(Objects::nonNull).collect(Collectors.toSet());

        Set<Long> stateIds = new HashSet<>();
        stateIds.add(studentVo.getFkAreaStateId());
        stateIds.add(studentVo.getFkAreaStateIdEducation());
        stateIds.add(studentVo.getFkAreaStateIdEducation2());
        stateIds.add(studentVo.getFkAreaStateIdBirth());

        Set<Long> cityIds = new HashSet<>();
        cityIds.add(studentVo.getFkAreaCityId());
        cityIds.add(studentVo.getFkAreaCityIdEducation());
        cityIds.add(studentVo.getFkAreaCityIdEducation2());
        cityIds.add(studentVo.getFkAreaCityIdBirth());

        //根据国家ids获取国家名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIds)) {
//            countryNamesByIds = institutionCenterClient.getCountryNamesByIds(countryIds);
            Result<Map<Long, String>> result = institutionCenterClient.getCountryNamesByIds(countryIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                countryNamesByIds = result.getData();
            }
        }
        //根据州省ids获取州省名称
        Map<Long, String> stateNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(stateIds)) {
//            stateNamesByIds = institutionCenterClient.getStateNamesByIds(stateIds);
            Result<Map<Long, String>> stateNamesByIdsResult = institutionCenterClient.getStateNamesByIds(stateIds);
            if (stateNamesByIdsResult.isSuccess() && GeneralTool.isNotEmpty(stateNamesByIdsResult.getData())) {
                stateNamesByIds = stateNamesByIdsResult.getData();
            }
        }
        //根据城市ids获取城市名称
        Map<Long, String> cityNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(cityIds)) {
//            cityNamesByIds = institutionCenterClient.getCityNamesByIds(cityIds);
            Result<Map<Long, String>> result = institutionCenterClient.getCityNamesByIds(cityIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                cityNamesByIds = result.getData();
            }
        }
        //获取绑定的项目成员
        List<StudentProjectRoleStaffVo> studentProjectRoleStaffVos = studentProjectRoleStaffMapper.selectProjectStaffByStudentId(id);
//        List<BaseSelectEntity> studentOfferSelect = studentOfferMapper.getStudentOfferSelect(id);
//        List<StudentProjectRoleStaffVo> studentProjectRoleStaffVos = new ArrayList<>();
//        if (GeneralTool.isNotEmpty(studentOfferSelect)) {
//            List<Long> offerIds = studentOfferSelect.stream().map(BaseSelectEntity::getId).collect(Collectors.toList());
//            studentProjectRoleStaffVos.addAll(studentProjectRoleStaffMapper.selectProjectStaff(TableEnum.SALE_STUDENT_OFFER.key, offerIds));
//        }
//        Example example1 = new Example(StudentInsurance.class);
//        example1.createCriteria().andEqualTo("fkStudentId", id).andEqualTo("status", 1);
//        List<StudentInsurance> studentInsurances = studentInsuranceMapper.selectByExample(example1);
//        if (GeneralTool.isNotEmpty(studentInsurances)) {
//            List<Long> insuranceIds = studentInsurances.stream().map(StudentInsurance::getId).collect(Collectors.toList());
//            List<StudentProjectRoleStaffVo> studentProjectRoleStaffDtoList = studentProjectRoleStaffMapper.selectProjectStaff(TableEnum.SALE_STUDENT_INSURANCE.key, insuranceIds);
//            studentProjectRoleStaffVos.addAll(studentProjectRoleStaffDtoList);
//        }
//        example1 = new Example(StudentAccommodation.class);
//        example1.createCriteria().andEqualTo("fkStudentId", id).andEqualTo("status", 1);
//        List<StudentAccommodation> studentAccommodations = studentAccommodationMapper.selectByExample(example1);
//
//        if (GeneralTool.isNotEmpty(studentAccommodations)) {
//            List<Long> accommodationsIds = studentAccommodations.stream().map(StudentAccommodation::getId).collect(Collectors.toList());
//            List<StudentProjectRoleStaffVo> studentProjectRoleStaffDtoList = studentProjectRoleStaffMapper.selectProjectStaff(TableEnum.SALE_STUDENT_ACCOMMODATION.key, accommodationsIds);
//            studentProjectRoleStaffVos.addAll(studentProjectRoleStaffDtoList);
//        }
        Set<Long> staffIds = studentProjectRoleStaffVos.stream().map(StudentProjectRoleStaffVo::getFkStaffId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> staffNamesByIds = permissionCenterClient.getStaffNamesByIds(staffIds);
        for (StudentProjectRoleStaffVo studentProjectRoleStaffVo : studentProjectRoleStaffVos) {
//            String[] staffIdStr = studentProjectRoleStaffVo.getStaffIdStr().split(",");
//            Set<Long> staffIds = Arrays.stream(staffIdStr).map(Long::valueOf).collect(Collectors.toSet());
//            Map<Long, String> staffNameMap = feignPermissionService.getStaffNamesByIds(staffIds);
//            studentProjectRoleStaffVo.setStaffName(StringUtils.join(staffNameMap.values(), ","));
            studentProjectRoleStaffVo.setStaffName(staffNamesByIds.get(studentProjectRoleStaffVo.getFkStaffId()));
        }
        studentVo.setStudentProjectRoleStaffDtos(studentProjectRoleStaffVos);
        //获取是否有申请方案
//        Example example = new Example(StudentOffer.class);
//        example.createCriteria().andEqualTo("fkStudentId", id).andEqualTo("status", 1);
        List<StudentOffer> studentOffers = studentOfferMapper.selectList(Wrappers.<StudentOffer>lambdaQuery().eq(StudentOffer::getFkStudentId, id).eq(StudentOffer::getStatus, 1));
        if (GeneralTool.isNotEmpty(studentOffers)) {
            studentVo.setStudentOfferFlag(true);
        } else {
            studentVo.setStudentOfferFlag(false);
        }

        //获取是否有留学住宿（状态类型：0：作废 1：有效 2：成功 3：延期 4：失败）
        List<StudentAccommodation> studentAccommodations = studentAccommodationMapper.selectList(Wrappers.<StudentAccommodation>lambdaQuery()
                .eq(StudentAccommodation::getFkStudentId, id)
                .ne(StudentAccommodation::getStatus, 0).isNotNull(StudentAccommodation::getStatus));
        if (GeneralTool.isNotEmpty(studentAccommodations)) {
            studentVo.setStudentAccommodationFlag(true);
        } else {
            studentVo.setStudentAccommodationFlag(false);
        }
        //获取是否有留学保险
        List<StudentInsurance> studentInsurances = studentInsuranceMapper.selectList(Wrappers.<StudentInsurance>lambdaQuery()
                .eq(StudentInsurance::getFkStudentId, id).eq(StudentInsurance::getStatus, 1));
        if (GeneralTool.isNotEmpty(studentInsurances)) {
            studentVo.setStudentInsurancesFlag(true);
        } else {
            studentVo.setStudentInsurancesFlag(false);
        }
        // 获取是否有留学服务费
        List<StudentServiceFee> studentServiceFees = studentServiceFeeMapper.selectList(Wrappers.<StudentServiceFee>lambdaQuery()
                .eq(StudentServiceFee::getFkStudentId, id).eq(StudentServiceFee::getStatus, 1));
        if (GeneralTool.isNotEmpty(studentServiceFees)) {
            studentVo.setStudentServiceFeeFlag(true);
        } else {
            studentVo.setStudentServiceFeeFlag(false);
        }

//        Map<Long, String> emailData = null;
        Map<Long, String> countryData = null;
        if (studentVo.isStudentOfferFlag()) {
            //获取所有申请方案绑定代理联系人邮箱
//            Set<Long> personIds = studentOffers.stream()
//                    .filter(studentOffer -> studentOffer.getFkContactPersonId() != null)
//                    .map(StudentOffer::getFkContactPersonId)
//                    .collect(Collectors.toSet());
//            emailData = contactPersonService.getContactPersonEmail(personIds);
            //获取所有申请方案的国家名称
            Set<Long> cIds = studentOffers.stream()
                    .filter(studentOffer -> studentOffer.getFkAreaCountryId() != null)
                    .map(StudentOffer::getFkAreaCountryId)
                    .collect(Collectors.toSet());
            Result<Map<Long, String>> countryNameResult = institutionCenterClient.getCountryNamesByIds(cIds);
            if (countryNameResult.isSuccess() && GeneralTool.isNotEmpty(countryNameResult.getData())) {
                countryData = countryNameResult.getData();
            }
        }


        //获取绑定的代理
        List<StudentAgentVo> studentAgentVoList = studentAgentMapper.getAgentStaffNameByStudentId(id);
        Set<Long> collect = studentAgentVoList.stream().map(StudentAgentVo::getStaffId).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(collect)) {
            Map<Long, String> staffNameMap = new HashMap<>();
            Result<Map<Long, String>> result = permissionCenterClient.getStaffNameMap(collect);
            if (result.isSuccess() && result.getData() != null) {
                staffNameMap = result.getData();
            }
            for (StudentAgentVo studentAgentVo : studentAgentVoList) {
                //邮箱
//                Set<String> emails = new HashSet<>();
//                if (GeneralTool.isNotEmpty(emailData)) {
//                    for (StudentOffer studentOffer : studentOffers) {
//                        //已绑定申请方案代理
//                        if (studentAgentVo.getFkAgentId().equals(studentOffer.getFkAgentId())) {
//                            if (GeneralTool.isNotEmpty(studentOffer.getFkContactPersonId())) {
//                                if (GeneralTool.isNotEmpty(emailData.get(studentOffer.getFkContactPersonId()))) {
//
//                                    StringBuffer stringBuffer = new StringBuffer();
//                                    //申请方案数大于1条数据，需拼接国家返回
//                                    if (studentOffers.size() > 1 && GeneralTool.isNotEmpty(studentOffer.getFkAreaCountryId())) {
//                                        stringBuffer.append("（").append(countryData.get(studentOffer.getFkAreaCountryId())).append("）");
//                                    }
//                                    stringBuffer.append(emailData.get(studentOffer.getFkContactPersonId()));
//                                    emails.add(stringBuffer.toString());
//                                }
//                            }
//                        }
//                    }
//                }
                //邮箱
                Set<String> emails = new HashSet<>();
                Set<Long> agentIds = new HashSet<>();
                for (StudentOffer studentOffer : studentOffers) {
                    if (studentAgentVo.getFkAgentId().equals(studentOffer.getFkAgentId())) {
                        StringBuffer stringBuffer = new StringBuffer();
                        //申请方案数大于1条数据，需拼接国家返回
                        if (studentOffers.size() > 1 && GeneralTool.isNotEmpty(studentOffer.getFkAreaCountryId())) {
                            stringBuffer.append("（").append(countryData.get(studentOffer.getFkAreaCountryId())).append("）");
                        }
                        stringBuffer.append(studentOffer.getAgentContactEmail());
                        emails.add(stringBuffer.toString());
                    }
                    agentIds.add(studentOffer.getFkAgentId());

                }
                studentAgentVo.setEmails(emails);
                studentAgentVo.setBdName(staffNameMap.get(studentAgentVo.getStaffId()));
                if (GeneralTool.isNotEmpty(studentAgentVo.getFkAgentId())) {
                    getAgentLabelDataUtils.setAgentLabelVos(studentAgentVo, studentAgentVo.getFkAgentId(), StudentAgentVo::setAgentLabelVos);
                }
                if (GeneralTool.isNotEmpty(studentAgentVo.getEmails())) {
                    getAgentLabelDataUtils.setAgentEmailLabelVos(studentAgentVo, studentAgentVo.getEmails().toString(), StudentAgentVo::setAgentEmailLabelVos);
                }

            }


        }
        if (GeneralTool.isNotEmpty(studentVo.getEducationProject())) {
            //项目说明名称
            switch (studentVo.getEducationProject()) {
                case 0:
                    studentVo.setEducationProjectName(ProjectExtraEnum.THREE_ONE.value);
                    break;
                case 1:
                    studentVo.setEducationProjectName(ProjectExtraEnum.TWO_TWO.value);
                    break;
                case 2:
                    studentVo.setEducationProjectName(ProjectExtraEnum.FOUR_ZERO.value);
                    break;
                case 3:
                    studentVo.setEducationProjectName(ProjectExtraEnum.EXCHANGE_STUDENTS.value);
                    break;
                default:
                    break;
            }
        }
        if (GeneralTool.isNotEmpty(studentVo.getEducationDegree())) {
            //学位情况名称
            switch (studentVo.getEducationDegree()) {
                case 0:
                    studentVo.setEducationDegreeName(ProjectExtraEnum.DOUBLE_DEGREE.value);
                    break;
                case 1:
                    studentVo.setEducationDegreeName(ProjectExtraEnum.INTERNATIONAL_DEGREE.value);
                    break;
                case 2:
                    studentVo.setEducationDegreeName(ProjectExtraEnum.DOMESTIC_DEGREE.value);
                    break;
                default:
                    break;
            }
        }

        studentVo.setStudentAgentDtoList(studentAgentVoList);
        List<StudentEducationLevelType> studentEducationLevelTypes = studentEducationLevelTypeMapper.selectList(Wrappers.lambdaQuery());
        Map<Long, String> educationMap = studentEducationLevelTypes.stream().collect(Collectors.toMap(StudentEducationLevelType::getId, StudentEducationLevelType::getTypeNameChn));
        String educationLevelType = studentVo.getEducationLevelType();
        String educationLevelType2 = studentVo.getEducationLevelType2();
        if (StringUtils.isNotBlank(educationLevelType) && StringUtils.isNumeric(educationLevelType)) {
            studentVo.setDomesticEducationName(educationMap.get(Long.valueOf(educationLevelType)));
        }
        if (StringUtils.isNotBlank(educationLevelType2) && StringUtils.isNumeric(educationLevelType2)) {
            studentVo.setInternationalEducationName(educationMap.get(Long.valueOf(educationLevelType2)));
        }

        Set<Long> studentIds = new HashSet<>();
        studentIds.add(studentVo.getId());


        Map<Long, Boolean> commissionActiveStatusMap = getCommissionActiveStatusByStudentIds(studentIds);
        //根据学生ids查询代理名称
        Map<Long, String> agentNameByStudentIds = new HashMap<>();
        //根据学生ids获取bd
        Map<Long, String> bdCodeByStudentIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(studentIds)) {
            agentNameByStudentIds = studentAgentService.getAgentNameByStudentIds(studentIds);
            bdCodeByStudentIds = studentAgentService.getBdCodeByStudentIds(studentIds);
        }
        Result<String> institutionNameResult = institutionCenterClient.getInstitutionName(student.getFkInstitutionIdEducation());
        if (institutionNameResult.isSuccess() && GeneralTool.isNotEmpty(institutionNameResult.getData())) {
            studentVo.setEducationInstitutionName(institutionNameResult.getData());
        }
        //国际
        String educationInstitutionName2 = "";
        Result<String> institutionNamesByIdsResult = institutionCenterClient.getInstitutionName(student.getFkInstitutionIdEducation2());
        if (institutionNamesByIdsResult.isSuccess() && GeneralTool.isNotEmpty(institutionNamesByIdsResult.getData())) {
            educationInstitutionName2 = institutionNamesByIdsResult.getData();
        }
        studentVo.setEducationInstitutionName2(educationInstitutionName2);

//        StudentReceivableAndPaySumDto studentReceivableAndPaySumVo = new StudentReceivableAndPaySumDto();
//        studentReceivableAndPaySumVo.setFkStudentId(id);

//        //针对like的字段转为小写
//        if(GeneralTool.isNotEmpty(studentReceivableAndPaySumVo.getStudentName()))
//        {
//            studentReceivableAndPaySumVo.setStudentName(studentReceivableAndPaySumVo.getStudentName().toLowerCase());
//        }
//        if(GeneralTool.isNotEmpty(studentReceivableAndPaySumVo.getInstitutionName()))
//        {
//            studentReceivableAndPaySumVo.setInstitutionName(studentReceivableAndPaySumVo.getInstitutionName().toLowerCase());
//        }
//        if(GeneralTool.isNotEmpty(studentReceivableAndPaySumVo.getInstitutionCourseName()))
//        {
//            studentReceivableAndPaySumVo.setInstitutionCourseName(studentReceivableAndPaySumVo.getInstitutionCourseName().toLowerCase());
//        }

//        //应收应付信息
//        List<StudentReceivableAndPaySumVo> studentReceivableAndPaySumDatas = studentOfferItemMapper.getStudentReceivableAndPaySumDatas(null, studentReceivableAndPaySumVo);
//        //应收应付计划类型
//        for (StudentReceivableAndPaySumVo vo : studentReceivableAndPaySumDatas) {
//            if (GeneralTool.isNotEmpty(vo.getReceivableTypeKey())) {
//                vo.setReceivableTypeName(TableEnum.getValueByKey(vo.getReceivableTypeKey(), TableEnum.SALE_TARGET_TYPE));
//            }
//            if (GeneralTool.isNotEmpty(vo.getPayableTypeKey())) {
//                vo.setPayableTypeName(TableEnum.getValueByKey(vo.getPayableTypeKey(), TableEnum.SALE_TARGET_TYPE));
//            }
//        }
//
//        studentVo.setStudentReceivableAndPayInfo(studentReceivableAndPaySumDatas);
        setName(companyMap, studentVo, countryNamesByIds, stateNamesByIds, cityNamesByIds, agentNameByStudentIds, bdCodeByStudentIds);
        //学生Id（ISSUEv2版申请）
        RStudentIssueStudent rStudentIssueStudent = rStudentIssueStudentMapper.getRStudentIssueStudentByStudentId(studentVo.getId());
        if (GeneralTool.isNotEmpty(rStudentIssueStudent)) {
            studentVo.setFkStudentIdIssue2(rStudentIssueStudent.getFkStudentIdIssue2());
        }
        studentVo.setIsActiveCommission(commissionActiveStatusMap.get(id));

        List<Map<String, Object>> settlementStatusList = Lists.newArrayList();

        List<StaffCommissionStep> staffCommissionSteps = staffCommissionStepService.list(Wrappers.lambdaQuery(StaffCommissionStep.class).eq(StaffCommissionStep::getFkCompanyId, student.getFkCompanyId()).orderByDesc(StaffCommissionStep::getStepOrder));
        List<StaffCommissionAction> staffCommissionActions = staffCommissionActionService.list(Wrappers.lambdaQuery(StaffCommissionAction.class).eq(StaffCommissionAction::getFkStudentId, id));
        if (GeneralTool.isNotEmpty(staffCommissionSteps)) {
            String notSettlementStep = "";
            String settlementStep = "";
            List<String> settlementStepList = Lists.newArrayList();
            if (GeneralTool.isNotEmpty(staffCommissionActions)) {
                //与已经结算步骤的取交集
                settlementStepList = staffCommissionActions.stream().filter(s -> s.getStatus() != 0).map(StaffCommissionAction::getFkStaffCommissionStepKey).distinct().collect(Collectors.toList());
            }

            List<String> finalSettlementStepList = settlementStepList;
            notSettlementStep = staffCommissionSteps.stream().filter(e -> !finalSettlementStepList.contains(e.getStepKey())).map(StaffCommissionStep::getStepName).distinct().collect(Collectors.joining("/"));
            settlementStep = staffCommissionSteps.stream().filter(e -> finalSettlementStepList.contains(e.getStepKey())).map(StaffCommissionStep::getStepName).distinct().collect(Collectors.joining("/"));

            if (GeneralTool.isNotEmpty(notSettlementStep)) {
                Map<String, Object> notSettlementStepMap = Maps.newHashMap();
                notSettlementStepMap.put("status", 0);
                notSettlementStepMap.put("statusName", "未结算");
                notSettlementStepMap.put("stepNames", notSettlementStep);
                settlementStatusList.add(notSettlementStepMap);
            }

            if (GeneralTool.isNotEmpty(settlementStep)) {
                Map<String, Object> settlementStepMap = Maps.newHashMap();
                settlementStepMap.put("status", 1);
                settlementStepMap.put("statusName", "已结算");
                settlementStepMap.put("stepNames", settlementStep);
                settlementStatusList.add(settlementStepMap);
            }

            if (GeneralTool.isNotEmpty(settlementStatusList)) {
                studentVo.setSettlementStatusList(settlementStatusList);
            }
        }
        studentVo.setIsComplexEducation(GeneralTool.isNotEmpty(studentVo.getIsComplexEducation()) && studentVo.getIsComplexEducation());


        return studentVo;
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        deleteService.deleteValidateStudent(id);
        List<RStudentIdentify> rStudentIdentifies = rStudentIdentifyMapper.selectList(new LambdaQueryWrapper<RStudentIdentify>().eq(RStudentIdentify::getFkStudentId, id));
        if (GeneralTool.isNotEmpty(rStudentIdentifies)){
            Set<Long> identifyIds = rStudentIdentifies.stream().map(RStudentIdentify::getId).collect(Collectors.toSet());
            rStudentIdentifyMapper.deleteBatchIds(identifyIds);
        }
        int delete = studentMapper.deleteById(id);
        if (delete <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Override
    public StudentVo updateStudent(StudentDto studentDto) {
        if (GeneralTool.isEmpty(studentDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        Student student = studentMapper.selectById(studentDto.getId());
        if (GeneralTool.isEmpty(student)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        BeanCopyUtils.copyProperties(studentDto, student);
        //学生名称去掉空格
        if (StringUtils.isNotBlank(student.getName())) {
            student.setName(student.getName().trim());
        }
        utilService.updateUserInfoToEntity(student);
        studentMapper.updateByIdWithNull(student);
        return findStudentById(student.getId());
    }

    @Override
    public Long addProjectRoleStaff(StudentProjectRoleStaffDto projectRoleStaffVo) {
//        projectRoleStaffVo.setFkTableName(TableEnum.SALE_STUDENT_OFFER.key);
        projectRoleStaffVo.setFkTableName(projectRoleStaffVo.getFkTableName());
        projectRoleStaffVo.setActiveDate(new Date());
        projectRoleStaffVo.setIsActive(true);
//        AgentRoleStaffDto agentRoleStaffVo = new AgentRoleStaffDto();
//        agentRoleStaffVo.setFkTypeKey(projectRoleStaffVo.getFkTableName());
//        agentRoleStaffVo.setFkStaffId(projectRoleStaffVo.getFkStaffId());
//        agentRoleStaffVo.setFkStudentProjectRoleId(projectRoleStaffVo.getFkStudentProjectRoleId());
//        agentRoleStaffVo.setIsActive(projectRoleStaffVo.getIsActive());
//        agentRoleStaffVo.setFkCompanyId(SecureUtil.getFkCompanyId());
//        agentRoleStaffService.addCommonAgentRoleStaff(agentRoleStaffVo);
        return projectRoleStaffService.addProjectRoleStaff(projectRoleStaffVo);
    }

    @Override
    public List<StudentProjectRoleStaffVo> getProjectRoleStaff(StudentProjectRoleStaffDto projectRoleStaffVo, Page page) {
        if (GeneralTool.isEmpty(projectRoleStaffVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(projectRoleStaffVo.getFkStudentId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("student_id_null"));
        }
//        projectRoleStaffVo.setFkTableName(TableEnum.SALE_STUDENT_OFFER.key);key
        return projectRoleStaffService.getProjectRoleStaff(projectRoleStaffVo, page);
    }

    @Override
    public Long editComment(CommentDto commentDto) {
        SaleComment comment = BeanCopyUtils.objClone(commentDto, SaleComment::new);
        if (GeneralTool.isNotEmpty(commentDto)) {
            if (GeneralTool.isNotEmpty(commentDto.getId())) {
                comment.setFkTableName(TableEnum.SALE_STUDENT.key);
                commentService.updateComment(comment);
            } else {
                comment.setFkTableName(TableEnum.SALE_STUDENT.key);
                commentService.addComment(comment);
            }
        }
        return comment.getId();
    }

    @Override
    public List<CommentVo> getComments(CommentDto commentDto, Page page) {
        commentDto.setFkTableName(TableEnum.SALE_STUDENT.key);
        return commentService.datas(commentDto, page);
    }

    @Override
    public List<MediaAndAttachedVo> addMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.SALE_STUDENT.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }

    @Override
    public List<MediaAndAttachedVo> getMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        verifyDataPermissionsUtils.verifyByBusinessId(attachedVo.getFkTableId(), VerifyDataPermissionsUtils.STUDENT_O);
        attachedVo.setFkTableName(TableEnum.SALE_STUDENT.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);

    }

    @Override
    public List<Long> getStudentIds(String name) {
        return studentMapper.getStudentIds(name);
    }

    @Override
    public String getStudentNameById(Long id) {
        return studentMapper.getStudentNameById(id);
    }

    @Override
    public Map<Long, String> getStudentNameByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
//        Example example = new Example(Student.class);
//        example.createCriteria().andIn("id", ids);
        List<Student> students = studentMapper.selectBatchIds(ids);
        if (GeneralTool.isEmpty(students)) {
            return map;
        }
        for (Student student : students) {
            StringBuffer stringBuffer = new StringBuffer();
            if (GeneralTool.isNotEmpty(student.getLastName()) && GeneralTool.isNotEmpty(student.getFirstName())) {
                stringBuffer.append(student.getName()).append('（').append(student.getLastName()).append(" ").append(student.getFirstName()).append('）');
            } else if (GeneralTool.isEmpty(student.getLastName()) && GeneralTool.isNotEmpty(student.getFirstName())) {
                stringBuffer.append(student.getName()).append('（').append(student.getFirstName()).append('）');
            } else {
                stringBuffer.append(student.getName());
            }

            map.put(student.getId(), stringBuffer.toString());
        }
        return map;
    }

    @Override
    public List<BaseSelectEntity> getStudentList(Long companyId) {
//        Example example = new Example(Student.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkCompanyId", companyId);
        List<Student> agents = studentMapper.selectList(Wrappers.<Student>lambdaQuery().eq(Student::getFkCompanyId, companyId));
        return agents.stream().map(agent -> BeanCopyUtils.objClone(agent, BaseSelectEntity::new)).collect(Collectors.toList());
    }


    /**
     * 导出学生信息Excel
     *
     * @param studentListQueryDto
     * @param focExportVos
     */
    @Override
    public void exportStudentInfoExcel(StudentListQueryDto studentListQueryDto, List<FocExportVo> focExportVos) {
        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
        com.get.core.secure.UserInfo user = GetAuthInfo.getUser();
        String locale = SecureUtil.getLocale();
        asyncExportService.exportStudentList(studentListQueryDto, focExportVos, headerMap, user, locale, SecureUtil.getCountryIds());
    }

    /**
     * 导出学生信息Excel表头选项
     *
     * @return
     */
    @Override
    public List<FocExportVo> getStudentOptions() {
        CommonUtil<StudentExportVo> commonUtil = new CommonUtil<>();
        StaffConfig config = permissionCenterClient.getStaffConfigByType(TableEnum.SALE_STUDENT.key);
        List<FocExportVo> options = new ArrayList<>();
        if (config == null || StringUtils.isBlank(config.getConfigValue())) {
            options = commonUtil.getOptions(StudentExportVo.class);
        } else {
            options = commonUtil.getOptions(StudentExportVo.class, config.getConfigValue());
        }
        // 学生申请资料收取时间   ADD_RECEIVED_APPLICATION_DATA_DATE
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.ADD_RECEIVED_APPLICATION_DATA_DATE.key, 1).getData();
        if (GeneralTool.isNotEmpty(companyConfigMap)) {
            String configValue = companyConfigMap.get(SecureUtil.getFkCompanyId());
            if (GeneralTool.isNotEmpty(configValue) && configValue.equals("0")) {
                options = options.stream().filter(i -> !"receivedApplicationDataDate".equals(i.getFiledName())).collect(Collectors.toList());
            }
        }
        return options;
    }

    /**
     * 学生步骤状态数据列表
     *
     * @Date 10:07 2021/7/21
     * <AUTHOR>
     */
    @Override
    public List<StudentOfferItemStatisticalStatusVo> getStudentsStepState(StudentOfferItemStatisticalStatusDto studentOfferItemStatisticalStatusDto) {
        List<StudentOfferItemStatisticalStatusVo> studentOfferItemStatisticalStatusVoList = new ArrayList<>();
        if (GeneralTool.isEmpty(SecureUtil.getCountryIds())) {
            return studentOfferItemStatisticalStatusVoList;
        }
        List<Long> studentIdList;
        List<String> userNames = new ArrayList<>();
        if (studentOfferItemStatisticalStatusDto.getIsAdmin()) {
            studentIdList = new ArrayList<>();
        } else {
            studentIdList = getStudentIdList(studentOfferItemStatisticalStatusDto.getStaffIds());
            if (GeneralTool.isEmpty(studentOfferItemStatisticalStatusDto.getStaffIds())) {
                Long staffId = SecureUtil.getStaffId();
                List<Long> staffFollowerIds = new ArrayList<>();
                Result<List<Long>> result = permissionCenterClient.getStaffFollowerIds(staffId);
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    staffFollowerIds.addAll(result.getData());
                }
                staffFollowerIds.add(staffId);
                Set<Long> ids = new HashSet<>(staffFollowerIds);
                if (GeneralTool.isNotEmpty(ids)) {
                    Map<Long, String> longIdMap = new HashMap<>();
                    Result<Map<Long, String>> staffLoginIdResult = permissionCenterClient.getStaffLoginIdByIds(ids);
                    if (staffLoginIdResult.isSuccess() && GeneralTool.isNotEmpty(staffLoginIdResult.getData())) {
                        longIdMap = staffLoginIdResult.getData();
                    }
                    userNames = new ArrayList<>(longIdMap.values());
                }
            }
        }
        if (GeneralTool.isEmpty(studentIdList)) {
            studentIdList.add(0L);
        }
        //步骤状态
//        Example example = new Example(StudentOfferItemStep.class);
//        example.createCriteria().andNotEqualTo("stepKey", "STEP_FAILURE");

//        List<StudentOfferItemVo> studentOfferItemVos = studentOfferItemMapper.getStudentsStepState(studentOfferItemStatisticalStatusDto.getCompanyId(),
//                studentIdList, studentOfferItemStatisticalStatusDto.getBeginTime(), studentOfferItemStatisticalStatusDto.getEndTime());

        //除了入学失败之外所有步骤
        List<StudentOfferItemStep> studentOfferItemSteps = studentOfferItemStepMapper.selectList(Wrappers.<StudentOfferItemStep>lambdaQuery().ne(StudentOfferItemStep::getStepKey, "STEP_FAILURE"));
        //根据BD编号获取员工ID
        Long staffId = null;
        if (GeneralTool.isNotEmpty(studentOfferItemStatisticalStatusDto.getBdCode())) {
            staffId = staffBdCodeService.getStaffIdByBdCode(studentOfferItemStatisticalStatusDto.getBdCode());
        }
        //获取每个步骤的学习计划数
        List<StudentOfferItemVo> studentOfferItemVos = studentOfferItemMapper.getStudentsStepState(
                studentOfferItemStatisticalStatusDto.getCompanyId(),
                studentIdList,
                userNames,
                studentOfferItemStatisticalStatusDto.getBeginTime(),
                studentOfferItemStatisticalStatusDto.getEndTime(),
                staffId,
                studentOfferItemStatisticalStatusDto.getAreaCountryIds(),
                SecureUtil.getCountryIds());
        for (StudentOfferItemStep studentOfferItemStep : studentOfferItemSteps) {
            StudentOfferItemStepVo studentOfferItemStepVo = BeanCopyUtils.objClone(studentOfferItemStep, StudentOfferItemStepVo::new);
            for (StudentOfferItemVo studentOfferItemVo : studentOfferItemVos) {
                if (studentOfferItemVo.getStepOrder().equals(studentOfferItemStep.getStepOrder())) {
                    studentOfferItemStepVo.setItemNum(studentOfferItemVo.getItemNum());
                    studentOfferItemStepVo.setStepOrder(studentOfferItemStepVo.getStepOrder());
                }
            }
            if (GeneralTool.isEmpty(studentOfferItemStepVo.getItemNum())) {
                studentOfferItemStepVo.setItemNum(0L);
            }

            StudentOfferItemStatisticalStatusVo studentOfferItemStatisticalStatusVo = new StudentOfferItemStatisticalStatusVo();
            studentOfferItemStatisticalStatusVo.setItemNum(studentOfferItemStepVo.getItemNum());
            studentOfferItemStatisticalStatusVo.setName(studentOfferItemStepVo.getStepName());
            studentOfferItemStatisticalStatusVo.setStepOrder(studentOfferItemStepVo.getStepOrder());
            studentOfferItemStatisticalStatusVo.setBeginTime(studentOfferItemStatisticalStatusDto.getBeginTime());
            studentOfferItemStatisticalStatusVo.setEndTime(studentOfferItemStatisticalStatusDto.getEndTime());
//            List<StudentVo> studentVoList = studentMapper.getStudentNumByOfferItemStepOrder(studentOfferItemStatisticalStatusDto.getCompanyId(), studentIdList,
//                    studentOfferItemStatisticalStatusDto.getBeginTime(), studentOfferItemStatisticalStatusDto.getEndTime(),
//                    studentOfferItemStepVo.getStepOrder());

            //根据学习计划状态各步骤获取学生数
            List<StudentVo> studentVoList = studentMapper.getStudentNumByOfferItemStepOrder(
                    studentOfferItemStatisticalStatusDto.getCompanyId(),
                    studentIdList,
                    userNames,
                    studentOfferItemStatisticalStatusDto.getBeginTime(),
                    studentOfferItemStatisticalStatusDto.getEndTime(),
                    studentOfferItemStepVo.getStepOrder(),
                    staffId,
                    studentOfferItemStatisticalStatusDto.getAreaCountryIds(),
                    SecureUtil.getCountryIds());

            if (GeneralTool.isNotEmpty(studentVoList)) {
                studentOfferItemStatisticalStatusVo.setStudentNum(studentVoList.size());
            } else {
                studentOfferItemStatisticalStatusVo.setStudentNum(0);
            }
            studentOfferItemStatisticalStatusVoList.add(studentOfferItemStatisticalStatusVo);
        }


        //失败步骤
        //失败步骤
        List<EnrolFailureReasonVo> enrolFailureReasonVoList = enrolFailureReasonMapper.getStudentsFailureState(
                studentOfferItemStatisticalStatusDto.getCompanyId(),
                studentIdList,
                userNames,
                studentOfferItemStatisticalStatusDto.getBeginTime(),
                studentOfferItemStatisticalStatusDto.getEndTime(),
                staffId,
                studentOfferItemStatisticalStatusDto.getAreaCountryIds(),
                SecureUtil.getCountryIds());
//        List<EnrolFailureReason> enrolFailureReasons = enrolFailureReasonMapper.selectAll();
        List<EnrolFailureReason> enrolFailureReasons = enrolFailureReasonMapper.selectList(Wrappers.<EnrolFailureReason>lambdaQuery());
        //有些步骤数量为0导致SQL加时间查询查不出这个步骤
        if (enrolFailureReasons.size() != enrolFailureReasonVoList.size()) {
            for (EnrolFailureReason enrolFailureReason : enrolFailureReasons) {
                boolean flag = false;
                for (int i = 0; i < enrolFailureReasonVoList.size(); i++) {
                    if (flag) {
                        break;
                    }
                    EnrolFailureReasonVo enrolFailureReasonVo = enrolFailureReasonVoList.get(i);
                    if (enrolFailureReason.getId().equals(enrolFailureReasonVo.getId())) {
                        enrolFailureReasonVo.setReasonName("入学失败-" + enrolFailureReasonVo.getReasonName() + "（" + enrolFailureReasonVo.getReasonKey() + "）");
                        flag = true;
                    }
                }
                if (!flag) {
                    EnrolFailureReasonVo failureReasonDto = BeanCopyUtils.objClone(enrolFailureReason, EnrolFailureReasonVo::new);
                    failureReasonDto.setReasonName("入学失败-" + failureReasonDto.getReasonName() + "（" + failureReasonDto.getReasonKey() + "）");
                    failureReasonDto.setItemNum(0L);
                    enrolFailureReasonVoList.add(failureReasonDto);
                }
            }
        }

        for (EnrolFailureReasonVo enrolFailureReasonVo : enrolFailureReasonVoList) {
            StudentOfferItemStatisticalStatusVo studentOfferItemStatisticalStatusVo = new StudentOfferItemStatisticalStatusVo();
            studentOfferItemStatisticalStatusVo.setItemNum(enrolFailureReasonVo.getItemNum());
            studentOfferItemStatisticalStatusVo.setName(enrolFailureReasonVo.getReasonName());
            studentOfferItemStatisticalStatusVo.setFailureReasonId(enrolFailureReasonVo.getId());
            studentOfferItemStatisticalStatusVo.setBeginTime(studentOfferItemStatisticalStatusDto.getBeginTime());
            studentOfferItemStatisticalStatusVo.setEndTime(studentOfferItemStatisticalStatusDto.getEndTime());
            if (enrolFailureReasonVo.getItemNum() != 0) {
                List<StudentVo> studentNumByFailureReasonKey = studentMapper.getStudentNumByFailureReasonKey(
                        studentOfferItemStatisticalStatusDto.getCompanyId(),
                        studentIdList,
                        userNames,
                        studentOfferItemStatisticalStatusDto.getBeginTime(),
                        studentOfferItemStatisticalStatusDto.getEndTime(),
                        enrolFailureReasonVo.getId(),
                        staffId,
                        studentOfferItemStatisticalStatusDto.getAreaCountryIds(),
                        SecureUtil.getCountryIds());
                if (GeneralTool.isNotEmpty(studentNumByFailureReasonKey)) {
                    studentOfferItemStatisticalStatusVo.setStudentNum(studentNumByFailureReasonKey.size());
                } else {
                    studentOfferItemStatisticalStatusVo.setStudentNum(0);
                }
            }
            studentOfferItemStatisticalStatusVoList.add(studentOfferItemStatisticalStatusVo);
        }

        //延迟入学
        StudentOfferItemDeferEntranceTimeVo studentOfferItemDeferEntranceTimeVo = studentOfferItemDeferEntranceTimeMapper.getStudentsFailureState(
                studentOfferItemStatisticalStatusDto.getCompanyId(),
                studentIdList,
                userNames,
                studentOfferItemStatisticalStatusDto.getBeginTime(),
                studentOfferItemStatisticalStatusDto.getEndTime(),
                staffId,
                studentOfferItemStatisticalStatusDto.getAreaCountryIds(),
                SecureUtil.getCountryIds());
        StudentOfferItemStatisticalStatusVo studentOfferItemStatisticalStatusVo = new StudentOfferItemStatisticalStatusVo();
        studentOfferItemStatisticalStatusVo.setName("延迟入学（Delayed Enrollment）");
        studentOfferItemStatisticalStatusVo.setItemNum(studentOfferItemDeferEntranceTimeVo.getItemNum());
        if (GeneralTool.isNotEmpty(studentOfferItemStatisticalStatusVo.getItemNum())) {
            List<StudentVo> studentVoList = studentMapper.getStudentNumByDeferEntranceTime(
                    studentOfferItemStatisticalStatusDto.getCompanyId(),
                    studentIdList,
                    userNames,
                    studentOfferItemStatisticalStatusDto.getBeginTime(),
                    studentOfferItemStatisticalStatusDto.getEndTime(),
                    staffId,
                    studentOfferItemStatisticalStatusDto.getAreaCountryIds());
            if (GeneralTool.isNotEmpty(studentVoList)) {
                studentOfferItemStatisticalStatusVo.setStudentNum(studentVoList.size());
            } else {
                studentOfferItemStatisticalStatusVo.setStudentNum(0);
            }
        }
        studentOfferItemStatisticalStatusVo.setIsDeferEntrance(true);
        studentOfferItemStatisticalStatusVo.setBeginTime(studentOfferItemStatisticalStatusDto.getBeginTime());
        studentOfferItemStatisticalStatusVo.setEndTime(studentOfferItemStatisticalStatusDto.getEndTime());
        studentOfferItemStatisticalStatusVoList.add(studentOfferItemStatisticalStatusVo);
        return studentOfferItemStatisticalStatusVoList;
    }

    /**
     * 学生步骤状态数据列表
     *
     * @Date 10:07 2021/7/21
     * <AUTHOR>
     */
    @Override
    public List<StudentOfferItemStatisticalStatusVo> getStudentsStepStateNew(StudentOfferItemStatisticalStatusDto studentOfferItemStatisticalStatusDto) {
        List<StudentOfferItemStatisticalStatusVo> studentOfferItemStatisticalStatusVoList = new ArrayList<>();
        if (GeneralTool.isEmpty(SecureUtil.getCountryIds())) {
            return studentOfferItemStatisticalStatusVoList;
        }


        //业务下属
        Long staffId = SecureUtil.getStaffId();
        List<Long> staffFollowerIds = new ArrayList<>();
        if (GeneralTool.isNotEmpty(studentOfferItemStatisticalStatusDto.getStaffIds())) {
            staffFollowerIds = studentOfferItemStatisticalStatusDto.getStaffIds();
        } else {
            //员工id + 业务下属员工ids
            Result<List<Long>> followerIds = permissionCenterClient.getStaffFollowerIds(staffId);
            if (followerIds.getData() != null) {
                staffFollowerIds = followerIds.getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
            }
//            staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
            staffFollowerIds.add(staffId);
        }
        //业务国家
        List<Long> fkAreaCountryIds = new ArrayList<>();
//        if (GeneralTool.isNotEmpty(studentOfferItemStatisticalStatusDto.getAreaCountryIds())) {
//            fkAreaCountryIds = studentOfferItemStatisticalStatusDto.getAreaCountryIds();
//        } else {
//
//        }
        fkAreaCountryIds = SecureUtil.getCountryIds();
        //根据BD编号获取员工ID
        Long fkStaffId = null;
        if (GeneralTool.isNotEmpty(studentOfferItemStatisticalStatusDto.getBdCode())) {
            fkStaffId = staffBdCodeService.getStaffIdByBdCode(studentOfferItemStatisticalStatusDto.getBdCode());
        }
//        //根据项目成员名称获取员工ID
//        List<Long> fkProjectMemberIds = new ArrayList<>();
//        if (GeneralTool.isNotEmpty(studentOfferItemStatisticalStatusDto.getFkProjectMemberName())) {
//            fkProjectMemberIds = permissionCenterClient.getStaffListByStaffName(studentOfferItemStatisticalStatusDto.getFkProjectMemberName());
//        }
        List<StudentOfferItemStatisticalStatusVo> studentOfferItemStatisticalStatusVos = new ArrayList<>();

//        以下字段Like改为小写
        if (GeneralTool.isNotEmpty(studentOfferItemStatisticalStatusDto.getBdCode())) {
            studentOfferItemStatisticalStatusDto.setBdCode(studentOfferItemStatisticalStatusDto.getBdCode().toLowerCase());
        }
        if (GeneralTool.isNotEmpty(studentOfferItemStatisticalStatusDto.getFkProjectMemberName())) {
            studentOfferItemStatisticalStatusDto.setFkProjectMemberName(studentOfferItemStatisticalStatusDto.getFkProjectMemberName().toLowerCase());
        }


        //按步骤状态变更时间统计
        if (studentOfferItemStatisticalStatusDto.getSelectType().equals(1)) {
            studentOfferItemStatisticalStatusVos = studentOfferItemMapper.getStudentsRStepStateStu(
                    studentOfferItemStatisticalStatusDto.getCompanyId(),
                    staffFollowerIds,
                    studentOfferItemStatisticalStatusDto.getSelectType(),
                    studentOfferItemStatisticalStatusDto.getBeginTime(),
                    studentOfferItemStatisticalStatusDto.getEndTime(),
                    fkStaffId,
                    fkAreaCountryIds,
                    studentOfferItemStatisticalStatusDto.getBdCode(),
                    studentOfferItemStatisticalStatusDto.getFkProjectMemberName(),
                    studentOfferItemStatisticalStatusDto.getAreaCountryIds(),
                    SecureUtil.getStaffInfo().getIsStudentAdmin(),
                    SecureUtil.getPermissionGroupInstitutionIds(),
                    SecureUtil.getStaffBoundBdIds());
        } else {
            studentOfferItemStatisticalStatusVos = studentOfferItemMapper.getStudentsStepStateStu(
                    studentOfferItemStatisticalStatusDto.getCompanyId(),
                    staffFollowerIds,
                    studentOfferItemStatisticalStatusDto.getSelectType(),
                    studentOfferItemStatisticalStatusDto.getBeginTime(),
                    studentOfferItemStatisticalStatusDto.getEndTime(),
                    fkStaffId,
                    fkAreaCountryIds,
                    studentOfferItemStatisticalStatusDto.getBdCode(),
                    studentOfferItemStatisticalStatusDto.getFkProjectMemberName(),
                    studentOfferItemStatisticalStatusDto.getAreaCountryIds(),
                    SecureUtil.getStaffInfo().getIsStudentAdmin(),
                    SecureUtil.getPermissionGroupInstitutionIds(),
                    SecureUtil.getStaffBoundBdIds()
            );
        }

//        //除了入学失败之外所有步骤
//        List<StudentOfferItemStep> studentOfferItemSteps = studentOfferItemStepMapper.selectList(Wrappers.<StudentOfferItemStep>lambdaQuery().orderByAsc(StudentOfferItemStep::getStepOrder));
//        if(GeneralTool.isEmpty(studentOfferItemStatisticalStatusVos))
//        {
//            studentOfferItemStatisticalStatusVos = new LinkedList<>();
//            if(!GeneralTool.isEmpty(studentOfferItemSteps))
//            {
//                for (StudentOfferItemStep studentOfferItemStep : studentOfferItemSteps) {
//                    StudentOfferItemStatisticalStatusVo studentOfferItemStatisticalStatusDto = new StudentOfferItemStatisticalStatusVo();
//                    studentOfferItemStatisticalStatusDto.setFkStudentOfferItemStepId(studentOfferItemStep.getId());
//                    studentOfferItemStatisticalStatusDto.setStepOrder(studentOfferItemStep.getStepOrder());
//                    studentOfferItemStatisticalStatusDto.setName(studentOfferItemStep.getStepName());
//                    studentOfferItemStatisticalStatusDto.setStepKey(studentOfferItemStep.getStepKey());
//                    studentOfferItemStatisticalStatusDto.setItemNum(0L);
//                    studentOfferItemStatisticalStatusDto.setStudentNum(0);
//                    studentOfferItemStatisticalStatusVos.add(studentOfferItemStatisticalStatusDto);
//                }
//            }
//        }else
//        {
//            if(!GeneralTool.isEmpty(studentOfferItemSteps))
//            {
//                List<Integer> stepOrders = studentOfferItemStatisticalStatusVos.stream().map(StudentOfferItemStatisticalStatusVo::getStepOrder).collect(Collectors.toList());
//                for (StudentOfferItemStep studentOfferItemStep : studentOfferItemSteps) {
//                    //如不包含则写入0
//                    if(!stepOrders.contains(studentOfferItemStep.getStepOrder()))
//                    {
//                        StudentOfferItemStatisticalStatusVo studentOfferItemStatisticalStatusDto = new StudentOfferItemStatisticalStatusVo();
//                        studentOfferItemStatisticalStatusDto.setFkStudentOfferItemStepId(studentOfferItemStep.getId());
//                        studentOfferItemStatisticalStatusDto.setStepOrder(studentOfferItemStep.getStepOrder());
//                        studentOfferItemStatisticalStatusDto.setName(studentOfferItemStep.getStepName());
//                        studentOfferItemStatisticalStatusDto.setStepKey(studentOfferItemStep.getStepKey());
//                        studentOfferItemStatisticalStatusDto.setItemNum(0L);
//                        studentOfferItemStatisticalStatusDto.setStudentNum(0);
//                        studentOfferItemStatisticalStatusVos.add(studentOfferItemStatisticalStatusDto);
//                    }
//                }
//            }
//        }
//        //去除集合中步骤id为null的数据
        List<StudentOfferItemStatisticalStatusVo> studentOfferItemStatisticalStatusDtos_ = studentOfferItemStatisticalStatusVos.stream().filter(item -> GeneralTool.isNotEmpty(item.getFkStudentOfferItemStepId())).collect(Collectors.toList());
        return studentOfferItemStatisticalStatusDtos_;
    }

    /**
     * 验证学生唯一性
     *
     * @param studentDto
     * @return
     */
    @Override
    public Long validateStudent(StudentDto studentDto) {
        if (GeneralTool.isEmpty(studentDto.getName()) && GeneralTool.isEmpty(studentDto.getBirthday())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
//        List<Student> students = studentMapper.getStudentByNameAndBirthday(studentDto);
        LambdaQueryWrapper<Student> saleStudentLambdaQueryWrapper = new LambdaQueryWrapper<>();
        saleStudentLambdaQueryWrapper.eq(Student::getName, studentDto.getName())
                .eq(Student::getBirthday, studentDto.getBirthday());
        if (GeneralTool.isNotEmpty(studentDto.getId())) {
            saleStudentLambdaQueryWrapper.ne(Student::getId, studentDto.getId());
        }
        List<Student> students = studentMapper.selectList(saleStudentLambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(students)) {
            return students.get(0).getId();
        }
        return null;
    }

    @Override
    public Boolean validatePhone(Long studentId, String phone) {
//        Example example = new Example(Student.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<Student> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Student::getMobile, phone);
        if (GeneralTool.isNotEmpty(studentId)) {
            //修改情况时
            lambdaQueryWrapper.ne(Student::getId, studentId);
        }
        List<Student> students = studentMapper.selectList(lambdaQueryWrapper);
        return GeneralTool.isEmpty(students);
    }

//    /**
//     * @Date 11:32 2021/6/24
//     * <AUTHOR>
//     */
//    private List<Long> getStudentIdList() {
//        //获取代理以及旗下代理所能看到的学生ids
//        List<Long> studentIdList = studentAgentService.getStudentIds();
//        if (GeneralTool.isEmpty(studentIdList)) {
//            studentIdList = new ArrayList<>();
//            studentIdList.add(0L);
//        }
//        //获取员工以及旗下员工所创建的学生ids
//        Long staffId = GetAuthInfo.getStaffId();
//        List<Long> staffFollowerIds = new ArrayList<>();
//        Result<List<Long>> result = permissionCenterClient.getStaffFollowerIds(staffId);
//        if(result.isSuccess() && GeneralTool.isNotEmpty(result.getData()))
//        {
//            staffFollowerIds.addAll(result.getData());
//        }
//        staffFollowerIds.add(staffId);
//        Set<Long> ids = new HashSet<>(staffFollowerIds);
//        Map<Long, String> longIdMap = new HashMap<>();
//        Result<Map<Long, String>> staffLoginIdResult = permissionCenterClient.getStaffLoginIdByIds(ids);
//        if(staffLoginIdResult.isSuccess() && GeneralTool.isNotEmpty(staffLoginIdResult.getData()))
//        {
//            longIdMap = staffLoginIdResult.getData();
//            List<String> userNames = new ArrayList<>(longIdMap.values());
//            List<SaleStudent> studentList = studentMapper.selectList(Wrappers.<SaleStudent>lambdaQuery().in(SaleStudent::getGmtCreateUser,userNames));
//            studentIdList.addAll(studentList.stream().map(SaleStudent::getId).collect(Collectors.toList()));
//        }

    /// /        Example example = new Example(Student.class);
    /// /        Example.Criteria criteria = example.createCriteria();
    /// /        criteria.andIn("gmtCreateUser", userNames);
    /// /        List<Student> studentList = studentMapper.selectByExample(example);
//        //获取当前登录人以及旗下员工所在项目组的学生
//        studentIdList.addAll(studentProjectRoleStaffMapper.getStudetnIdsByStaffId(staffFollowerIds));
//        return studentIdList;
//    }
    @Override
    public Boolean validateEmail(Long studentId, String email) {
//        Example example = new Example(Student.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("email", email);
//        if (GeneralTool.isNotEmpty(studentId)) {
//            //修改情况时
//            criteria.andNotEqualTo("id", studentId);
//        }
//        List<Student> students = studentMapper.selectByExample(example);

        LambdaQueryWrapper<Student> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Student::getEmail, email);
        if (GeneralTool.isNotEmpty(studentId)) {
            //修改情况时
            lambdaQueryWrapper.ne(Student::getId, studentId);
        }
        List<Student> students = studentMapper.selectList(lambdaQueryWrapper);
        return GeneralTool.isEmpty(students);
    }

    /**
     * 获取代理以及旗下代理所能看到的学生ids + 当前登录人以及旗下员工所在项目组的学生 （不包含当前登录人以及旗下员工所创建的学生ids）
     *
     * @Date 11:32 2021/6/24
     * <AUTHOR>
     */
    private List<Long> getStudentIdList(List<Long> staffIds) {
        List<Long> studentIdList = new ArrayList<>();
        //获取代理以及旗下代理所能看到的学生ids
        if (GeneralTool.isNotEmpty(staffIds)) {
            //获取当前筛选用户对应代理ID
            List<Long> agentIds = agentStaffService.getAgentIdByStaffIdsIsActive(staffIds);
            if (GeneralTool.isNotEmpty(agentIds)) {
                List<StudentAgent> studentAgents = studentAgentMapper.selectList(Wrappers.<StudentAgent>lambdaQuery().in(StudentAgent::getFkAgentId, agentIds).eq(StudentAgent::getIsActive, true));
                List<Long> collect = studentAgents.stream().map(StudentAgent::getFkStudentId).distinct().collect(Collectors.toList());
                studentIdList = collect;
            }

        } else {
            studentIdList = studentAgentService.getStudentIds();
        }

        //获取员工以及旗下员工所创建的学生ids
        Long staffId = SecureUtil.getStaffId();

        //不存在员工下属筛选条件
        if (GeneralTool.isEmpty(staffIds)) {
            //获取当前登录员工下属
            List<Long> staffFollowerIds = new ArrayList<>();
            Result<List<Long>> result = permissionCenterClient.getStaffFollowerIds(staffId);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                staffFollowerIds.addAll(result.getData());
            }
            staffFollowerIds.add(staffId);
            if (GeneralTool.isEmpty(studentIdList)) {
                studentIdList = new ArrayList<>();
            }
            //获取当前登录人以及旗下员工所在项目组的学生
            if (GeneralTool.isNotEmpty(staffFollowerIds)) {
                studentIdList.addAll(studentProjectRoleStaffMapper.getStudetnIdsByStaffId(staffFollowerIds));
            }
        }
        return studentIdList;
    }

    private void setNameList(Map<String, String> companyMap, StudentVo studentVo, List<StudentOffer> studentOffers, Map<Long, String> countryNamesByIds,
                             Map<Long, String> stateNamesByIds, Map<Long, String> cityNamesByIds,
                             Map<Long, String> institutionNamesByIds,
                             List<Map<String, Object>> studentAgentBdList,
                             Map<Long, Agent> agentNamesByIds, Map<Long, String> staffNamesByIds,
                             List<Map<String, Object>> statusChangeLastTimeList,
                             Map<Long, List<StudentOfferItemVo>> studentOfferItemMap, Map<Long, String> projectRoleStaffMap, String local,
                             Map<Integer, String> studentOfferItemStepMap,
                             Map<Long, Agent> offerAgentNamesByIds, Map<Long, StudentClientSourceDto> studentClientSourceDtoMap, Map<Long, List<AgentLabelVo>> agentLabelMap) {

        //设置公司名
        studentVo.setFkCompanyName(companyMap.get(String.valueOf(studentVo.getFkCompanyId())));
        //居住位置
        String cityName = cityNamesByIds.get(studentVo.getFkAreaCityId());
        String countryName = countryNamesByIds.get(studentVo.getFkAreaCountryId());
        String stateName = stateNamesByIds.get(studentVo.getFkAreaStateId());
        //出生位置
        String birthCityName = cityNamesByIds.get(studentVo.getFkAreaCityIdBirth());
        String birthCountryName = cityNamesByIds.get(studentVo.getFkAreaCountryIdBirth());
        String birthStateName = cityNamesByIds.get(studentVo.getFkAreaStateIdBirth());
        //毕业大学位置
        String educationCityName = cityNamesByIds.get(studentVo.getFkAreaCityIdEducation());
        String educationCountryName = countryNamesByIds.get(studentVo.getFkAreaCountryIdEducation());
        String educationStateName = stateNamesByIds.get(studentVo.getFkAreaStateIdEducation());
        //毕业大学位置（国际）
        String educationCityName2 = cityNamesByIds.get(studentVo.getFkAreaCityIdEducation2());
        String educationCountryName2 = null;
        if (GeneralTool.isNotEmpty(studentVo.getFkAreaCountryIdEducation2()) && GeneralTool.isNotEmpty(countryNamesByIds)) {
            educationCountryName2 = countryNamesByIds.get(studentVo.getFkAreaCountryIdEducation2());
        }
        String educationStateName2 = stateNamesByIds.get(studentVo.getFkAreaStateIdEducation2());
        //毕业院校名
        String educationInstitutionName = institutionNamesByIds.get(studentVo.getFkInstitutionIdEducation());
        String educationInstitutionName2 = institutionNamesByIds.get(studentVo.getFkInstitutionIdEducation2());

        studentVo.setCityName(cityName);
        studentVo.setCountryName(countryName);
        studentVo.setStateName(stateName);

        studentVo.setCityNameBirth(birthCityName);
        studentVo.setCountryNameBirth(birthCountryName);
        studentVo.setStateNameBirth(birthStateName);

        studentVo.setCityNameEducation(educationCityName);
        studentVo.setCountryNameEducation(educationCountryName);
        studentVo.setStateNameEducation(educationStateName);

        studentVo.setCityNameEducation2(educationCityName2);
        studentVo.setCountryNameEducation2(educationCountryName2);
        studentVo.setStateNameEducation2(educationStateName2);

        studentVo.setEducationInstitutionName(educationInstitutionName);
        studentVo.setEducationInstitutionName2(educationInstitutionName2);

        studentVo.setMaxStepOrderName(studentOfferItemStepMap.get(studentVo.getMaxStepOrder()));
        studentVo.setMinStepOrderName(studentOfferItemStepMap.get(studentVo.getMinStepOrder()));
        //设置资源来源
        if (GeneralTool.isNotEmpty(studentVo.getFkClientId())) {
            studentVo.setStudentClientSourceDto(studentClientSourceDtoMap.get(studentVo.getFkClientId()));
        }

        //set 学习计划学校名
        List<StudentOfferItemVo> studentOfferItemVoList = studentOfferItemMap.get(studentVo.getId());
        if (GeneralTool.isNotEmpty(studentOfferItemVoList)) {
            for (StudentOfferItemVo studentOfferItemVo : studentOfferItemVoList) {
                if (GeneralTool.isNotEmpty(studentOfferItemVo.getFkInstitutionId())) {
                    studentOfferItemVo.setFkInstitutionName(institutionNamesByIds.get(studentOfferItemVo.getFkInstitutionId()));
                }
            }
            studentVo.setStudentOfferItemDtoList(studentOfferItemVoList);
        }

        Long studentId = studentVo.getId();

        //当前绑定代理 / BD
        Set<String> currentBdNameAndStaffNameList = new HashSet<>();
        //当前绑定代理
        Set<String> currentStaffNameList = new LinkedHashSet();
        Set<AgentAndAgentLabelVo> currentStaffNameAndAgentLabelList = new LinkedHashSet();
        //当前绑定代理编号
        Set<String> currentAgentNumList = new LinkedHashSet<>();
        //当前绑定BD
        Set<String> currentBdNameList = new LinkedHashSet();
        //学生申请方案绑定代理/BD
        Set<String> studentOfferBdNameAndStaffNameList = new HashSet<>();
        //学生申请方案绑定代理
        Set<String> studentOfferStaffNameList = new LinkedHashSet<>();
        Set<AgentAndAgentLabelVo> studentOfferStaffNameAndAgentLabelList = new LinkedHashSet();
        //学生申请方案绑定代理编号
        Set<String> studentOfferAgentNumList = new LinkedHashSet<>();
        //学生申请方案绑定BD
        Set<String> studentOfferBdNameList = new LinkedHashSet<>();

        Set<Long> offerCountryIds = new HashSet<>();
        offerCountryIds.addAll(studentOffers.stream().map(StudentOffer::getFkAreaCountryId).collect(Collectors.toSet()));
//        //根据国家ids获取国家名称
        Map<Long, String> offerCountryNamesByIds = new HashMap<>();
        countryNamesByIds.forEach((key, value) -> {
            if (offerCountryIds.contains(key)) {
                offerCountryNamesByIds.put(key, value);
            }
        });
        if (GeneralTool.isNotEmpty(studentOffers)) {
            for (StudentOffer studentOffer : studentOffers) {
                StringBuilder sb = new StringBuilder();
                //获取国家名称
                String agentName = "";
                String nameNote;
                String agentNum = null;
                Agent agent = offerAgentNamesByIds.get(studentOffer.getFkAgentId());
                if (GeneralTool.isNotEmpty(agent)) {
                    agentName = agent.getName();
                    nameNote = agent.getNameNote();
                    agentNum = agent.getNum();
                    if (StringUtils.isNotBlank(nameNote)) {
                        agentName += "（" + nameNote + "）";
                    }
                }

                //获取代理名称
                //获取员工姓名
                String staffName = staffNamesByIds.get(studentOffer.getFkStaffId());
                if (GeneralTool.isNotEmpty(staffName)) {
                    sb.append(agentName).append("（").append(staffName).append("）");
                    //currentBdNameList.add(staffName);
                    studentOfferBdNameList.add(staffName);
                }
                studentOfferBdNameAndStaffNameList.add(sb.toString());
                //申请方案绑定代理
                studentOfferStaffNameList.add(agentName);
                studentOfferStaffNameAndAgentLabelList.add(new AgentAndAgentLabelVo(studentOffer.getFkAgentId(), agentName, agentLabelMap.get(studentOffer.getFkAgentId())));
                if (GeneralTool.isNotEmpty(agentNum)) {
                    studentOfferAgentNumList.add(agentNum);
                }


            }


            //项目角色 / 员工
            Set<String> currentProjectRoleStaffList = new HashSet<>();
            for (StudentOffer studentOffer : studentOffers) {
                //项目角色 / 员工
                if (GeneralTool.isNotEmpty(projectRoleStaffMap.get(studentOffer.getId()))) {
                    StringBuilder sb = new StringBuilder();
                    String name = projectRoleStaffMap.get(studentOffer.getId());
                    sb.append(name).append(",");
                    currentProjectRoleStaffList.add(sb.toString());
                }
            }
            studentVo.setProjectRoleStaffList(currentProjectRoleStaffList);
        }

        for (Map<String, Object> studentAgentBd : studentAgentBdList) {
            //获取当前学生不为空的代理和不为空的员工
            if (studentAgentBd.get("studentId").equals(studentId)
                    && GeneralTool.isNotEmpty(studentAgentBd.get("fkAgentId"))
                    && GeneralTool.isNotEmpty(studentAgentBd.get("fkStaffId"))) {
                StringBuilder sb = new StringBuilder();
                //获取代理名称
                String agentName = "";
                String nameNote = "";
                String agentNum = null;
                Long fkAgentId = Long.valueOf(studentAgentBd.get("fkAgentId").toString());
                if (agentNamesByIds.containsKey(fkAgentId)) {
                    Agent agent = agentNamesByIds.get(fkAgentId);
                    agentName = agent.getName();
                    nameNote = agent.getNameNote();
                    agentNum = agent.getNum();
                    if (StringUtils.isNotBlank(nameNote)) {
                        agentName += "（" + nameNote + "）";
                    }
                }

                //获取员工姓名
                String staffName = staffNamesByIds.get(Long.valueOf(studentAgentBd.get("fkStaffId").toString()));
                if (GeneralTool.isNotEmpty(agentName) && GeneralTool.isNotEmpty(staffName)) {
                    //格式：代理名称A（员工BD名称A）
                    sb.append(agentName).append("（").append(staffName).append("）");
                    if (StringUtils.isNotBlank(nameNote)) {
                        sb.append("（").append(nameNote).append("）");
                    }
                    //当前绑定代理
                    currentStaffNameList.add(agentName);
                    currentStaffNameAndAgentLabelList.add(new AgentAndAgentLabelVo(fkAgentId, agentName, agentLabelMap.get(fkAgentId)));
                    currentBdNameList.add(staffName);
                    currentBdNameAndStaffNameList.add(sb.toString());
                }
                //代理编号
                if (GeneralTool.isNotEmpty(agentNum)) {
                    currentAgentNumList.add(agentNum);
                }

            }
        }

        studentVo.setCurrentBdNameAndStaffNameList(currentBdNameAndStaffNameList);
        studentVo.setCurrentStaffNameList(currentStaffNameList);
        studentVo.setCurrentAgentNumList(currentAgentNumList);
        studentVo.setCurrentBdNameList(currentBdNameList);
        studentVo.setStudentOfferBdNameAndStaffNameList(studentOfferBdNameAndStaffNameList);
        studentVo.setStudentOfferStaffNameList(studentOfferStaffNameList);
        studentVo.setStudentOfferAgentNumList(studentOfferAgentNumList);
        studentVo.setStudentOfferBdNamList(studentOfferBdNameList);
        studentVo.setCurrentStaffNameAndAgentLabelList(new ArrayList<>(currentStaffNameAndAgentLabelList));
        studentVo.setStudentOfferStaffNameAndAgentLabelList(new ArrayList<>(studentOfferStaffNameAndAgentLabelList));

        if (GeneralTool.isNotEmpty(offerCountryNamesByIds)) {
            studentVo.setAreaCountryNames(offerCountryNamesByIds.values().stream().collect(Collectors.joining(",")));
        }
        //获取该学生状态变更最新时间
        Date statusChangeLastTime = null;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (Map<String, Object> statusChangeLastTimeMap : statusChangeLastTimeList) {
            //获取当前学生不为空的时间
            if (statusChangeLastTimeMap.get("studentId").equals(studentId) && GeneralTool.isNotEmpty(statusChangeLastTimeMap.get("statusChangeLastTime"))) {
                try {
                    statusChangeLastTime = sdf.parse(statusChangeLastTimeMap.get("statusChangeLastTime").toString());
                } catch (ParseException e) {
                    statusChangeLastTime = null;
                }
                //符合条件退出循环
                break;
            }
        }
        //状态变更最新时间
        studentVo.setStatusChangeLastTime(statusChangeLastTime);
        //高中成绩类型名称本科成绩类型名称
        if (GeneralTool.isNotEmpty(studentVo.getHighSchoolTestType())) {
            studentVo.setHighSchoolTestTypeName(ProjectExtraEnum.getValueByKey(Integer.valueOf(studentVo.getHighSchoolTestType()), ProjectExtraEnum.HIGH_SCHOOL_GRADES, local));
        }
        if (GeneralTool.isNotEmpty(studentVo.getStandardTestType())) {
            studentVo.setStandardTestTypeName(ProjectExtraEnum.getValueByKey(Integer.valueOf(studentVo.getStandardTestType()), ProjectExtraEnum.UNDERGRADUATE_ACHIEVEMENT, local));
        }
        if (GeneralTool.isNotEmpty(studentVo.getEnglishTestType())) {
            studentVo.setEnglishTestTypeName(ProjectExtraEnum.getValueByKey(Integer.valueOf(studentVo.getEnglishTestType()), ProjectExtraEnum.ENGLISH_TEST_TYPE, local));
        }

        if (GeneralTool.isNotEmpty(studentVo.getBirthday())) {
            studentVo.setAge(getAge(studentVo.getBirthday()));
        }
        if (GeneralTool.isNotEmpty(studentVo.getConditionType())) {
            String[] split = studentVo.getConditionType().split(",");
            StringJoiner stringJoiner = new StringJoiner(" ");
            for (String type : split) {
                String valueByKey = ProjectExtraEnum.getValueByKey(Integer.valueOf(type), ProjectExtraEnum.STUDENT_BUSINESS_STATUS, local);
                stringJoiner.add(valueByKey);
            }
            studentVo.setConditionTypeName(stringJoiner.toString());
        }
    }

    private void setName(Map<String, String> companyMap, StudentVo studentVo, Map<Long, String> countryNamesByIds,
                         Map<Long, String> stateNamesByIds, Map<Long, String> cityNamesByIds, Map<Long, String> agentNameByStudentIds,
                         Map<Long, String> bdCodeByStudentIds) {

        //设置公司名
        studentVo.setFkCompanyName(companyMap.get(String.valueOf(studentVo.getFkCompanyId())));

        String cityName = cityNamesByIds.get(studentVo.getFkAreaCityId());
        String countryName = countryNamesByIds.get(studentVo.getFkAreaCountryId());
        String stateName = stateNamesByIds.get(studentVo.getFkAreaStateId());

        String educationCityName = cityNamesByIds.get(studentVo.getFkAreaCityIdEducation());
        String educationCountryName = countryNamesByIds.get(studentVo.getFkAreaCountryIdEducation());
        String educationStateName = stateNamesByIds.get(studentVo.getFkAreaStateIdEducation());

        String educationCityName2 = cityNamesByIds.get(studentVo.getFkAreaCityIdEducation2());
        String educationCountryName2 = countryNamesByIds.get(studentVo.getFkAreaCountryIdEducation2());
        String educationStateName2 = stateNamesByIds.get(studentVo.getFkAreaStateIdEducation2());

        studentVo.setCityName(cityName);
        studentVo.setCountryName(countryName);
        studentVo.setStateName(stateName);

        studentVo.setCityNameEducation(educationCityName);
        studentVo.setCountryNameEducation(educationCountryName);
        studentVo.setStateNameEducation(educationStateName);


        studentVo.setCityNameEducation2(educationCityName2);
        studentVo.setCountryNameEducation2(educationCountryName2);
        studentVo.setStateNameEducation2(educationStateName2);

        studentVo.setFkAreaCityBirthName(cityNamesByIds.get(studentVo.getFkAreaCityIdBirth()));
        studentVo.setFkAreaCountryBirthName(countryNamesByIds.get(studentVo.getFkAreaCountryIdBirth()));
        studentVo.setFkAreaStateBirthName(stateNamesByIds.get(studentVo.getFkAreaStateIdBirth()));

        //国籍
        studentVo.setCountryNameNationality(countryNamesByIds.get(studentVo.getFkAreaCountryIdNationality()));
        //绿卡
        studentVo.setCountryNameGreenCard(countryNamesByIds.get(studentVo.getFkAreaCountryIdGreenCard()));

        if (GeneralTool.isNotEmpty(studentVo.getFkAreaCountryIdPassport())) {
            studentVo.setFkAreaCountryNamePassport(countryNamesByIds.get(studentVo.getFkAreaCountryIdPassport()));
        }

        //获取代理名称
        String agentName = agentNameByStudentIds.get(studentVo.getId());
        List<String> agentNameList = GeneralTool.isEmpty(agentName) ? new ArrayList<>() : Arrays.asList(agentName);
        studentVo.setFkAgentName(agentNameList);

        //获取bd编号
        String bdCode = bdCodeByStudentIds.get(studentVo.getId());
        List<String> bdCodeList = GeneralTool.isEmpty(bdCode) ? new ArrayList<>() : Arrays.asList(bdCode);
        studentVo.setFkStaffName(bdCodeList);

        if (GeneralTool.isNotEmpty(studentVo.getBirthday())) {
            studentVo.setAge(getAge(studentVo.getBirthday()));
        }
        if (GeneralTool.isNotEmpty(studentVo.getConditionType())) {
            String[] split = studentVo.getConditionType().split(",");
            StringJoiner stringJoiner = new StringJoiner(" ");
            for (String type : split) {
                String valueByKey = ProjectExtraEnum.getValueByKey(Integer.valueOf(type), ProjectExtraEnum.STUDENT_BUSINESS_STATUS);
                stringJoiner.add(valueByKey);
            }
            studentVo.setConditionTypeName(stringJoiner.toString());
        }
        //高中成绩类型名称本科成绩类型名称
        if (GeneralTool.isNotEmpty(studentVo.getHighSchoolTestType())) {
            studentVo.setHighSchoolTestTypeName(ProjectExtraEnum.getValueByKey(Integer.valueOf(studentVo.getHighSchoolTestType()), ProjectExtraEnum.HIGH_SCHOOL_GRADES));
        }
        if (GeneralTool.isNotEmpty(studentVo.getStandardTestType())) {
            studentVo.setStandardTestTypeName(ProjectExtraEnum.getValueByKey(Integer.valueOf(studentVo.getStandardTestType()), ProjectExtraEnum.UNDERGRADUATE_ACHIEVEMENT));
        }
        if (GeneralTool.isNotEmpty(studentVo.getEnglishTestType())) {
            studentVo.setEnglishTestTypeName(ProjectExtraEnum.getValueByKey(Integer.valueOf(studentVo.getEnglishTestType()), ProjectExtraEnum.ENGLISH_TEST_TYPE));
        }
        if (GeneralTool.isNotEmpty(studentVo.getMasterTestType())) {
            studentVo.setMasterTestTypeName(ProjectExtraEnum.getValueByKey(Integer.valueOf(studentVo.getMasterTestType()),
                    ProjectExtraEnum.UNDERGRADUATE_ACHIEVEMENT));
        }

    }

    private Map<String, String> getCompanyMap() {
        //初始为5的map
        Map<String, String> companyMap = new HashMap<>(5);
        Result<List<com.get.permissioncenter.vo.tree.CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
            JsonConfig config = new JsonConfig();
            config.setExcludes(new String[]{"departmentTree", "totalNum"});
            JSONArray jsonArray = JSONArray.fromObject(result.getData(), config);
            List<CompanyTreeVo> companyTreeVos = JSONArray.toList(jsonArray, new CompanyTreeVo(), new JsonConfig());
            if (GeneralTool.isNotEmpty(companyTreeVos)) {
                companyMap = companyTreeVos.stream().collect(Collectors.toMap(CompanyTreeVo::getId, CompanyTreeVo::getShortName));
            }
        }
        return companyMap;
    }

    private Map<Long, String> getSuccessfulApplicationCountry(List<Long> ids) {
        if (GeneralTool.isEmpty(ids)) {
            return null;
        }
        //初始为5的map
        Map<Long, String> successfulCountryMap = new HashMap<>(5);
        List<BaseSelectEntity> successfulCountryList = studentMapper.getSuccessfulApplicationCountry(ids);
        if (GeneralTool.isNotEmpty(successfulCountryList)) {
            successfulCountryMap = successfulCountryList.stream().collect(Collectors.toMap(BaseSelectEntity::getId, BaseSelectEntity::getName));
        }
        return successfulCountryMap;
    }

    /**
     * 项目角色不为空或者角色名称不为空，继续添加学生id
     *
     * @param studentDto
     */
    private void addStudentIds(StudentDto studentDto, LambdaQueryWrapper<Student> lambdaQueryWrapper) {
        //两个查询条件都为空，则不往下执行
        if (GeneralTool.isEmpty(studentDto.getProjectStaffNameOrEnName()) && GeneralTool.isEmpty(studentDto.getProjectRoleId())) {
            return;
        }

        Set<Long> studentIds = new HashSet<>();
        Set<Long> staffIds = null;
        //根据角色名称或英文名模糊查询人员id，筛选掉不符合条件的学生
        if (GeneralTool.isNotEmpty(studentDto.getProjectStaffNameOrEnName())) {
            staffIds = studentOfferService.getStaffIdByStaffNameOrEnName(studentDto.getProjectStaffNameOrEnName());
            //没有查询出符合条件的数据
            if (GeneralTool.isEmpty(staffIds)) {
                studentIds.add(0L);
                lambdaQueryWrapper.in(Student::getId, studentIds);
                return;
            }
        }

        //根据项目角色id或者角色名称筛选后的人员获取学生
        List<Map<String, Object>> studentAndStaffByProjectRoleId = studentProjectRoleStaffMapper.getStudentAndStaffByProjectRoleId(
                GeneralTool.isEmpty(studentDto.getProjectRoleId()) ? null : studentDto.getProjectRoleId(), staffIds);
        //没有查询出符合条件的数据
        if (GeneralTool.isEmpty(studentAndStaffByProjectRoleId)) {
            studentIds.add(0L);
            lambdaQueryWrapper.in(Student::getId, studentIds);
            return;
        }

        for (Map<String, Object> stringObjectMap : studentAndStaffByProjectRoleId) {
            if (GeneralTool.isNotEmpty(stringObjectMap)) {
                if (GeneralTool.isNotEmpty(stringObjectMap.get("fkStudentId"))) {
                    studentIds.add(Long.valueOf(stringObjectMap.get("fkStudentId").toString()));
                }
            }
        }

        if (GeneralTool.isEmpty(studentIds)) {
            studentIds.add(0L);
        }
        lambdaQueryWrapper.in(Student::getId, studentIds);
    }

    @Override
    public List<StudentAccommodationVo> getStudentAccommodationList(StudentAccommodationDto studentAccommodationDto, Page page) {
        if (GeneralTool.isEmpty(studentAccommodationDto.getFkStudentId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        verifyDataPermissionsUtils.verifyByBusinessId(studentAccommodationDto.getFkStudentId(), VerifyDataPermissionsUtils.STUDENT_O);
        return studentAccommodationService.getStudentAccommodationList(studentAccommodationDto, page);
    }

    @Override
    public List<StudentInsuranceVo> getStudentInsuranceList(StudentInsuranceDto studentInsuranceDto, Page page) {
        if (GeneralTool.isEmpty(studentInsuranceDto.getFkStudentId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        verifyDataPermissionsUtils.verifyByBusinessId(studentInsuranceDto.getFkStudentId(), VerifyDataPermissionsUtils.STUDENT_O);
        studentInsuranceDto.setIsHidden(SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding());
        return studentInsuranceService.getStudentInsuranceList(studentInsuranceDto, page);
    }

    @Override
    public ExistStudentVo getIsExistStudent(String studentName, String email, String mobile, String passpost,
                                            String birthday, Long id, String fkcompanyIds) {
        List<Long> companyIds = GeneralTool.isEmpty(fkcompanyIds) ? Collections.singletonList(SecureUtil.getFkCompanyId()) : Arrays.stream(fkcompanyIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
        List<Map<String, String>> listMap = new ArrayList<>();
        ExistStudentVo existStudentVo = new ExistStudentVo();
        Map<String, String> map = new HashMap<>();
        String type = "";
        List<Student> student = null;
        if (GeneralTool.isNotBlank(birthday)) {
            LambdaQueryWrapper<Student> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.in(GeneralTool.isNotEmpty(companyIds), Student::getFkCompanyId, companyIds).eq(Student::getBirthday, birthday).ne(GeneralTool.isNotEmpty(id), Student::getId, id).and(wrapper_ -> wrapper_.eq(Student::getName, studentName).or().eq(Student::getName, studentName.replace(" ", "")));
            List<Student> isExitStudentBirthday = studentMapper.selectList(lambdaQueryWrapper);
            if (GeneralTool.isNotEmpty(isExitStudentBirthday)) {
                student = isExitStudentBirthday;
                type = "birthday";
            }
        }
        if (GeneralTool.isEmpty(student) && GeneralTool.isNotBlank(passpost)) {
            LambdaQueryWrapper<Student> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(Student::getPassportNum, passpost).in(Student::getFkCompanyId, companyIds).ne(GeneralTool.isNotEmpty(id), Student::getId, id);
            List<Student> isExitStudentPassport = studentMapper.selectList(lambdaQueryWrapper);
            if (GeneralTool.isNotEmpty(isExitStudentPassport)) {
                student = isExitStudentPassport;
                type = "passpost";
            }
        }

        //学生重名标记
        LambdaQueryWrapper<Student> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Student::getBirthday, birthday).in(Student::getFkCompanyId, companyIds).ne(GeneralTool.isNotEmpty(id), Student::getId, id).and(wrapper_ -> wrapper_.eq(Student::getName, studentName).or().eq(Student::getName, studentName.replace(" ", "")));
        List<Student> isExitStudentName = studentMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(isExitStudentName)) {
            existStudentVo.setExistsStudentName(true);
        } else {
            existStudentVo.setExistsStudentName(false);
        }

        StringBuilder sb = new StringBuilder();
        int temp = 0;
        if (GeneralTool.isNotEmpty(student)) {
            List<Long> ids = student.stream().map(Student::getId).collect(Collectors.toList());
            List<AgentVo> studentAgents = studentMapper.getStudentAgents(ids);

            for (Student studentData : student) {
                StudentOfferDto studentOfferDto = new StudentOfferDto();
                studentOfferDto.setFkStudentId(studentData.getId());
                studentOfferDto.setIsClientQuery(true);//过滤权限
                List<StudentOfferVo> studentOfferNew = offerService.getStudentOfferNew(studentOfferDto, new Page());
                //TODO 改过
                //List<StudentItemAndStepVo> dtos = new ArrayList<>();
                List<StudentItemInfoVo> dtos = new ArrayList<>();
                //TODO 改过
                //StudentItemAndStepVo dto = new StudentItemAndStepVo();
                StudentItemInfoVo dto = new StudentItemInfoVo();
                if (GeneralTool.isNotEmpty(studentOfferNew)) {
                    List<StudentOfferVo> collect = studentOfferNew.stream().filter(item -> item.getStatus() == 1).collect(Collectors.toList());
                    collect.forEach(item -> {
                        if (GeneralTool.isNotEmpty(item.getStudentItemAndStepDtos())) {
                            //TODO 改过
//                            StudentItemAndStepVo studentItemAndStepVo = item.getStudentItemAndStepDtos().stream() .filter(d -> d.getDeferOpeningTime() != null) // 如果需要排除null值
//                                    .min(Comparator.comparing(StudentItemAndStepVo::getDeferOpeningTime).reversed()).get();
                            StudentItemInfoVo studentItemAndStepVo = item.getStudentItemAndStepDtos().stream().filter(d -> d.getDeferOpeningTime() != null) // 如果需要排除null值
                                    .min(Comparator.comparing(StudentItemInfoVo::getDeferOpeningTime).reversed()).get();
                            studentItemAndStepVo.setFkAreaCountryName(item.getFkAreaCountryName());
                            dtos.add(studentItemAndStepVo);
                        }
                    });
                }
                if (GeneralTool.isNotEmpty(dtos)) {
                    //TODO 改过
                    // dto = dtos.stream().filter(t->t.getDeferOpeningTime()!=null).min(Comparator.comparing(StudentItemAndStepVo::getDeferOpeningTime).reversed()).get();
                    dto = dtos.stream().filter(t -> t.getDeferOpeningTime() != null).min(Comparator.comparing(StudentItemInfoVo::getDeferOpeningTime).reversed()).get();

                }
                if (!studentData.getId().equals(id)) {
                    temp++;
                    sb.append("【" + temp + "】").append(LocaleMessageUtils.getMessage("m_student")).append("：").append(studentData.getName()).append("，");
                    switch (type) {
                        case "birthday":
                            Optional.ofNullable(studentData.getBirthday()).ifPresent(d -> sb.append(LocaleMessageUtils.getMessage("BIRTHDAY")).append("：").append(new SimpleDateFormat("yyyy-MM-dd").format(studentData.getBirthday())).append("，").append(LocaleMessageUtils.getMessage("NUMBERING")).append("：").append(studentData.getNum()).append("，").append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！").append("<br/>"));
                            break;
                        case "passpost":
                            Optional.ofNullable(studentData.getPassportNum()).ifPresent(d -> sb.append(LocaleMessageUtils.getMessage("PASSPOST")).append("：").append(studentData.getPassportNum()).append("，").append(LocaleMessageUtils.getMessage("NUMBERING")).append("：").append(studentData.getNum()).append("，").append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！").append("<br/>"));
                    }
                    if (GeneralTool.isNotEmpty(studentAgents)) {
                        sb.append(LocaleMessageUtils.getMessage("STUDENT_AGENT_IS")).append("：<br/>");
                        Set<Long> staffIds = studentAgents.stream().map(AgentVo::getFkStaffId).collect(Collectors.toSet());
                        List<StaffVo> staffByIds = permissionCenterClient.getStaffByIds(staffIds);
                        if (GeneralTool.isNotEmpty(staffByIds)) {
                            studentAgents.stream().forEach(data -> {
                                for (StaffVo staffById : staffByIds) {
                                    if (data.getFkStudentId().equals(studentData.getId())) {
                                        if (data.getFkStaffId().equals(staffById.getId())) {
                                            sb.append(data.getName()).append("，").append("BD：").append(staffById.getName()).append("；<br/>");
                                        }
                                    }
                                }
                            });
                        }
                    }
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    if (GeneralTool.isNotEmpty(dto.getDeferOpeningTime())) {
                        String format = simpleDateFormat.format(dto.getDeferOpeningTime());
                        sb.append(LocaleMessageUtils.getMessage("STUDENT_APPLICATION_STATUS_TOP")).append(":<br/>").append(dto.getFkAreaCountryName()).append("-").append(dto.getStepName()).append("-").append(format).append("<br/>");
                    }
                    map.put(String.valueOf(studentData.getId()), sb.toString());
                    sb.delete(0, sb.length());
                }
            }
            if (map.size() > 0) {
                listMap.add(map);
                existStudentVo.setType(type);
                existStudentVo.setMaps(listMap);
                return existStudentVo;
            }
        }

        List<StudentVo> hint = null;
        String msg = "";
        if (GeneralTool.isNotBlank(mobile)) {
            hint = BeanCopyUtils.copyListProperties(studentMapper.selectList(Wrappers.<Student>lambdaQuery().eq(Student::getMobile, mobile).in(Student::getFkCompanyId, companyIds).ne(GeneralTool.isNotEmpty(id), Student::getId, id)), StudentVo::new);
            msg = "mobile";
        }
        if (GeneralTool.isEmpty(hint) && GeneralTool.isNotBlank(email)) {
            hint = BeanCopyUtils.copyListProperties(studentMapper.selectList(Wrappers.<Student>lambdaQuery().eq(Student::getEmail, email).in(Student::getFkCompanyId, companyIds).ne(GeneralTool.isNotEmpty(id), Student::getId, id)), StudentVo::new);
            msg = "email";
        }

        switch (msg) {
            case "email":
                if (GeneralTool.isNotEmpty(email) && GeneralTool.isNotEmpty(hint)) {
                    for (StudentVo dto : hint) {
                        if (!dto.getId().equals(id)) {
                            temp++;
                            sb.append("【" + temp + "】").append(LocaleMessageUtils.getMessage("m_student")).append("：").append(dto.getName()).append("，")
                                    .append(LocaleMessageUtils.getMessage("NUMBERING")).append("：").append(dto.getNum()).append("；<br/>");
                            sb.append(LocaleMessageUtils.getMessage("EMAIL")).append("：").append(email).append("，").append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！").append("<br/>");
                            map.put(String.valueOf(dto.getId()), sb.toString());
                            sb.delete(0, sb.length());
                        }
                    }


                }
                break;
            case "mobile":
                if (GeneralTool.isNotEmpty(mobile) && GeneralTool.isNotEmpty(hint)) {
                    for (StudentVo dto : hint) {
                        if (!dto.getId().equals(id)) {
                            temp++;
                            sb.append("【" + temp + "】").append(LocaleMessageUtils.getMessage("m_student")).append("：").append(dto.getName()).append("，")
                                    .append(LocaleMessageUtils.getMessage("NUMBERING")).append("：").append(dto.getNum()).append("；<br/>");
                        }
                        sb.append(LocaleMessageUtils.getMessage("MOBILE")).append("：").append(mobile).append("，").append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！").append("<br/>");
                        map.put(String.valueOf(dto.getId()), sb.toString());
                        sb.delete(0, sb.length());

                    }


                }
        }
        if (map.size() > 0) {
            listMap.add(map);
            existStudentVo.setType(msg);
            existStudentVo.setMaps(listMap);
        }
        return existStudentVo;
    }

    /**
     * 用于学生资源详情中，绑定业务学生
     * 根据学生资源所属分公司 + 学生名称 + 学生生日。查找对应业务学生数据，若无，显示：暂无可以绑定学生数据。
     *
     * @param companyId   学生资源所属分公司id
     * @param studentName 学生名称
     * @param birthday    学生生日
     * @return
     */
    @Override
    public List<ExistStudentByBoundVo> getStudentByBoundBusinessStudent(Long companyId, String studentName, String birthday) {
        List<ExistStudentByBoundVo> res = Lists.newArrayList();

        List<Student> students = studentMapper.selectList(Wrappers.<Student>lambdaQuery()
                .eq(Student::getFkCompanyId, companyId)
                .and(wrapper_ -> wrapper_.eq(Student::getName, studentName)
                        .or()
                        .eq(Student::getName, studentName.replace(" ", ""))
                )
                .eq(Student::getBirthday, birthday)
        );

        if (GeneralTool.isNotEmpty(students)) {
            List<Long> ids = students.stream().map(Student::getId).collect(Collectors.toList());
            // 获取学生代理信息
            List<AgentVo> studentAgents = studentMapper.getStudentAgents(ids);
            // 代理
            Map<Long, List<AgentVo>> agentDtoMap = Maps.newHashMap();
            // BD名称
            Map<Long, String> bdNameMap = Maps.newHashMap();
            if (GeneralTool.isNotEmpty(studentAgents)) {
                agentDtoMap = studentAgents.stream().collect(Collectors.groupingBy(AgentVo::getFkStudentId));
                Set<Long> staffIds = studentAgents.stream().map(AgentVo::getFkStaffId).collect(Collectors.toSet());
                List<StaffVo> staffByIds = permissionCenterClient.getStaffByIds(staffIds);
                if (GeneralTool.isNotEmpty(staffByIds)) {
                    bdNameMap = staffByIds.stream().collect(Collectors.groupingBy(
                            StaffVo::getId,
                            Collectors.mapping(StaffVo::getName, Collectors.joining("、"))
                    ));
                }
            }

            for (int i = 0; i < students.size(); i++) {
                Student student = students.get(i);
                ExistStudentByBoundVo existStudentByBoundVo = new ExistStudentByBoundVo();
                existStudentByBoundVo.setFkStudentId(student.getId());
                existStudentByBoundVo.setFkClientId(student.getFkClientId());
                StringBuilder studentInfo = new StringBuilder();
                if (GeneralTool.isNotEmpty(student.getFkClientId())) {
                    existStudentByBoundVo.setIsExist(true);
                } else {
                    existStudentByBoundVo.setIsExist(false);
                }
                studentInfo.append("【" + (i + 1) + "】");
                if (StringUtils.isNotBlank(student.getName())) {
                    // 学生姓名
                    studentInfo.append(LocaleMessageUtils.getMessage("m_student")).append("：").append(student.getName()).append("，");
                }
                if (GeneralTool.isNotEmpty(student.getBirthday())) {
                    // 学生生日
                    studentInfo.append(LocaleMessageUtils.getMessage("BIRTHDAY")).append("：").append(new SimpleDateFormat("yyyy-MM-dd").format(student.getBirthday())).append("，");
                }
                if (StringUtils.isNotBlank(student.getNum())) {
                    // 学生编号
                    studentInfo.append(LocaleMessageUtils.getMessage("NUMBERING")).append("：").append(student.getNum()).append("，");
                }
                // 已经存在
                studentInfo.append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！");
                if (agentDtoMap.containsKey(student.getId())) {
                    List<AgentVo> agentDtos = agentDtoMap.get(student.getId());
                    if (GeneralTool.isNotEmpty(agentDtos)) {
                        String agentName = agentDtos.stream().map(AgentVo::getName).collect(Collectors.joining("、"));
                        // 绑定代理
                        studentInfo.append(LocaleMessageUtils.getMessage("STUDENT_AGENT_IS")).append(agentName).append("，");
                        // BD
                        Set<Long> fkStaffIds = agentDtos.stream().map(AgentVo::getFkStaffId).collect(Collectors.toSet());
                        if (GeneralTool.isNotEmpty(fkStaffIds)) {
                            String bdName = fkStaffIds.stream().map(bdNameMap::get).filter(StringUtils::isNotBlank).collect(Collectors.joining("、"));
                            studentInfo.append("BD：").append(bdName).append("；");
                        }
                    }
                }
                existStudentByBoundVo.setStudentInfo(studentInfo.toString());
                res.add(existStudentByBoundVo);
            }
        }
        return res;
    }


    /**
     * 获取中文名称的拼音
     *
     * @param studentName
     * @return
     */
    @Override
    public PinyinNameVo getNameToPinyin(String studentName) {
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        format.setVCharType(HanyuPinyinVCharType.WITH_V);

        PinyinNameVo result = new PinyinNameVo();

        StringBuilder sb = new StringBuilder();
        char[] chars = studentName.toCharArray();
        for (char c : chars) {
            String s = String.valueOf(c);
            if (GetStringUtils.isChinese(s)) {
                //是中文
                sb.append(s);
            }
        }
        String chName = sb.toString();

        if (GeneralTool.isEmpty(chName)) {
            return result;
        }

        List<PinYinVo> lastNameList = new ArrayList<>();
        List<PinYinVo> firstNameList = new ArrayList<>();

        //复姓
        if (chName.length() > 2 && SaleCenterConstant.COMPOUND_SURNAME.contains(chName.substring(0, 2))) {
            String chLastName = chName.substring(0, 2);
            String lastNamePinYin;
            try {
                lastNamePinYin = PinyinHelper.toHanYuPinyinString(chLastName, format, "", false);
            } catch (BadHanyuPinyinOutputFormatCombination e) {
                throw new GetServiceException(e.getMessage());
            }
            lastNamePinYin = lastNamePinYin.substring(0, 1).toUpperCase() + lastNamePinYin.substring(1);
            PinYinVo lastNamePinYinVo = new PinYinVo();
            lastNamePinYinVo.setName(chLastName);
            lastNamePinYinVo.setEn(new String[]{lastNamePinYin});
            lastNameList.add(lastNamePinYinVo);
            result.setLastNameList(lastNameList);

            for (int i = 2; i < chName.length(); i++) {
                String firstName;
                if (chName.length() == i + 1) {
                    firstName = chName.substring(i);
                } else {
                    firstName = chName.substring(i, i + 1);
                }
                String[] firstNamePinYinArray = new String[0];
                try {
                    firstNamePinYinArray = PinyinHelper.toHanyuPinyinStringArray(firstName.charAt(0), format);
                } catch (BadHanyuPinyinOutputFormatCombination e) {
                    throw new GetServiceException(e.getMessage());
                }

                if (i == 2) {
                    for (int j = 0; j < firstNamePinYinArray.length; j++) {
                        String firstNamePinYin = firstNamePinYinArray[j];
                        firstNamePinYin = firstNamePinYin.substring(0, 1).toUpperCase() + firstNamePinYin.substring(1);
                        firstNamePinYinArray[j] = firstNamePinYin;

                    }
                }
                PinYinVo firstNamePinYinVo = new PinYinVo();
                firstNamePinYinVo.setName(firstName);
                firstNamePinYinVo.setEn(firstNamePinYinArray);
                firstNameList.add(firstNamePinYinVo);
                result.setFirstNameList(firstNameList);
            }
        } else {
            //不是复姓
            String lastName = chName.substring(0, 1);
            String[] lastNamePinYinArray = new String[0];
            try {
                lastNamePinYinArray = PinyinHelper.toHanyuPinyinStringArray(lastName.charAt(0), format);
            } catch (BadHanyuPinyinOutputFormatCombination e) {
                throw new GetServiceException(e.getMessage());
            }
            for (int i = 0; i < lastNamePinYinArray.length; i++) {
                String lastNamePinYin = lastNamePinYinArray[i];
                lastNamePinYin = lastNamePinYin.substring(0, 1).toUpperCase() + lastNamePinYin.substring(1);
                lastNamePinYinArray[i] = lastNamePinYin;
            }
            PinYinVo lastNamePinYinVo = new PinYinVo();
            lastNamePinYinVo.setName(lastName);
            lastNamePinYinVo.setEn(lastNamePinYinArray);
            lastNameList.add(lastNamePinYinVo);

            if (chName.length() > 1) {
                String firstName = chName.substring(1, 2);
                String[] firstNamePinYinArray;
                try {
                    firstNamePinYinArray = PinyinHelper.toHanyuPinyinStringArray(chName.substring(1, 2).charAt(0), format);
                } catch (BadHanyuPinyinOutputFormatCombination e) {
                    throw new GetServiceException(e.getMessage());
                }
                for (int i = 0; i < firstNamePinYinArray.length; i++) {
                    String firstNamePinYin = firstNamePinYinArray[i];
                    firstNamePinYin = firstNamePinYin.substring(0, 1).toUpperCase() + firstNamePinYin.substring(1);
                    firstNamePinYinArray[i] = firstNamePinYin;
                }
                PinYinVo firstNamePinYinVo = new PinYinVo();
                firstNamePinYinVo.setName(firstName);
                firstNamePinYinVo.setEn(firstNamePinYinArray);
                firstNameList.add(firstNamePinYinVo);

                if (chName.length() > 2) {
                    for (int i = 2; i < chName.length(); i++) {
                        if (chName.length() == i + 1) {
                            firstName = chName.substring(i);
                        } else {
                            firstName = chName.substring(i, i + 1);
                        }
                        try {
                            firstNamePinYinArray = PinyinHelper.toHanyuPinyinStringArray(firstName.charAt(0), format);
                        } catch (BadHanyuPinyinOutputFormatCombination e) {
                            throw new GetServiceException(e.getMessage());
                        }
                        firstNamePinYinVo = new PinYinVo();
                        firstNamePinYinVo.setName(firstName);
                        firstNamePinYinVo.setEn(firstNamePinYinArray);
                        firstNameList.add(firstNamePinYinVo);
                    }
                }
            }
            result.setLastNameList(lastNameList);
            result.setFirstNameList(firstNameList);

        }
        return result;
    }


    /**
     * 设置一键入学失败
     *
     * @param studentId
     * @param reasonId
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchEnrolFailure(Long studentId, Long reasonId, String reason, Long studentOfferId) {
        Student student = studentMapper.selectById(studentId);
        if (Objects.isNull(student)) {
            return;
        }
        StudentOfferDto studentOfferDto = new StudentOfferDto();
        studentOfferDto.setFkStudentId(studentId);
        //获取业务下属
        List<Long> staffFollowerIds = new ArrayList<>();
        Result<List<Long>> longResult = permissionCenterClient.getStaffFollowerIds(SecureUtil.getStaffId());
        if (longResult.isSuccess() && GeneralTool.isNotEmpty(longResult.getData())) {
            staffFollowerIds.addAll(longResult.getData());
        }
        if (GeneralTool.isEmpty(staffFollowerIds)) {
            staffFollowerIds = new ArrayList<>();
        }
        staffFollowerIds.add(SecureUtil.getStaffId());
        //需要加权限
        //检查是否是BD
        Boolean isBd = studentOfferService.getIsBd(SecureUtil.getStaffId());

        List<StudentOffer> studentOffers = studentOfferMapper.getStudentOfferNew(null, studentOfferDto, staffFollowerIds, SecureUtil.getCountryIds(), SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(), SecureUtil.getStaffInfo().getIsStudentAdmin(), SecureUtil.getStaffId(), SecureUtil.getInstitutionIds(), isBd, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());

        if (GeneralTool.isEmpty(studentOffers)) {
            return;
        }
        //让用户可以单独操作某个国家线的申请
        if (GeneralTool.isNotEmpty(studentOfferId)) {
            studentOffers = studentOffers.stream().filter(studentOffer -> studentOffer.getId().equals(studentOfferId)).collect(Collectors.toList());
            if (GeneralTool.isEmpty(studentOffers)) {
                return;
            }
        }
        Long fkCompanyId = student.getFkCompanyId();

        Result<Map<Long, String>> configResult = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.ONE_CLICK_FAILURE_STATUS.key, 1);
        List<String> list = new ArrayList<>();
        if (configResult.isSuccess() && configResult.getData() != null) {
            String configValue1 = configResult.getData().get(fkCompanyId);
            //            String value1 = configByKey.getData().getValue1();
            if (StringUtils.isNotBlank(configValue1)) {
                com.alibaba.fastjson.JSONArray jsonArray = JSON.parseArray(configValue1);
                list = jsonArray.toJavaList(String.class);

//                JSONObject jsonObject = JSONObject.parseObject(value1);
//                if (fkCompanyId == 3) {
//                    list = JSONObject.parseObject(jsonObject.getString("IAE"), new TypeReference<List<String>>() {
//                    });
//                } else {
//                    list = JSONObject.parseObject(jsonObject.getString("OTHER"), new TypeReference<List<String>>() {
//                    });
//                }
            }
        } else {
            throw new GetServiceException(LocaleMessageUtils.getFormatMessage(LocaleMessageUtils.getMessage("lack_of_business_configuration", "ONE_CLICK_FAILURE_STATUS")));
        }
        Set<Long> offerIds = studentOffers.stream().map(StudentOffer::getId).collect(Collectors.toSet());
        LambdaQueryWrapper<StudentOfferItem> lambdaQueryWrapper = Wrappers.<StudentOfferItem>lambdaQuery().in(StudentOfferItem::getFkStudentOfferId, offerIds)
                .eq(StudentOfferItem::getStatus, 1);
        if (!SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding()) {
            lambdaQueryWrapper.ne(StudentOfferItem::getIsFollowHidden, 1);
        }
        List<StudentOfferItem> studentOfferItems = studentOfferItemMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isEmpty(studentOfferItems)) {
            return;
        }
        StudentOfferItemStep studentOfferItemStep = studentOfferItemStepMapper.selectOne(Wrappers.<StudentOfferItemStep>lambdaQuery()
                .eq(StudentOfferItemStep::getStepKey, ProjectKeyEnum.STEP_FAILURE.key));
        Long stepId = studentOfferItemStep.getId();
        list.add(ProjectKeyEnum.STEP_FAILURE.key);
        List<StudentOfferItemStep> steps = studentOfferItemStepMapper.selectList(Wrappers.<StudentOfferItemStep>lambdaQuery()
                .in(StudentOfferItemStep::getStepKey, list));
        List<Long> ignore = steps.stream().map(StudentOfferItemStep::getId).collect(Collectors.toList());
        //入学失败更新负数应收计划ids
        for (StudentOfferItem studentOfferItem : studentOfferItems) {
            if (ignore.contains(studentOfferItem.getFkStudentOfferItemStepId())) {
                continue;
            }
            offerItemService.validateInvalid(studentOfferItem.getId(), ProjectKeyEnum.BATCH_ENROLL_FAILURE.key, false);
            //需要加日志表
            studentOfferItem.setStudentOfferItemStepTime(new Date());
            studentOfferItem.setFkStudentOfferItemStepId(stepId);
            studentOfferItem.setFkEnrolFailureReasonId(reasonId);
            if (GeneralTool.isNotEmpty(reason)) {
                studentOfferItem.setOtherFailureReason(reason);
            }
            utilService.updateUserInfoToEntity(studentOfferItem);
            verifyStudentOfferItemUtils.verifyOfferItemUpdate(studentOfferItem);
            studentOfferItemMapper.updateById(studentOfferItem);
            RStudentOfferItemStep rStudentOfferItemStep = new RStudentOfferItemStep();
            rStudentOfferItemStep.setFkStudentOfferItemId(studentOfferItem.getId());
            rStudentOfferItemStep.setFkStudentOfferItemStepId(stepId);
            utilService.updateUserInfoToEntity(rStudentOfferItemStep);
            rStudentOfferItemStepMapper.insertSelective(rStudentOfferItemStep);

            //删除未执行的提醒任务
            List<String> fkRemindEventTypeKeys = new ArrayList<>();
            fkRemindEventTypeKeys.add("STUDENT_ACCEP_OFFER");
            fkRemindEventTypeKeys.add("STUDENT_PAY_DEPOSIT");
            fkRemindEventTypeKeys.add("AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE");
            fkRemindEventTypeKeys.add("AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE");
            fkRemindEventTypeKeys.add("STUDENT_ACCEP_OFFER_ENG");
            fkRemindEventTypeKeys.add("STUDENT_PAY_DEPOSIT_ENG");
            fkRemindEventTypeKeys.add("AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE_ENG");
            fkRemindEventTypeKeys.add("AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE_ENG");
            reminderCenterClient.batchDeleteByTableId(ProjectKeyEnum.SALE_STUDENT_OFFER_ITEM.key, studentOfferItem.getId(), fkRemindEventTypeKeys);
        }
    }

    @Override
    public Boolean hasPassportNum(Long fkStudentId) {
        if (GeneralTool.isEmpty(fkStudentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return studentMapper.hasPassportNum(fkStudentId);
    }

    @Override
    public List<Map<String, Object>> getTestTypeSelect() {
        return ProjectExtraEnum.enums2Arrays(ProjectExtraEnum.TEST_TYPE);
    }

    @Override
    public StudentAgentBindingVo getStudentAgentBindingByStudent(String fkStudentNum) {
        LambdaQueryWrapper<Student> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Student::getNum, fkStudentNum);
        Student student = studentMapper.selectOne(lambdaQueryWrapper);
        if (GeneralTool.isEmpty(student)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("student_info_null"));
        }


        StudentAgentBindingVo studentAgentBindingVo = new StudentAgentBindingVo();
        Long studentId = student.getId();
        studentAgentBindingVo.setStudentId(studentId);
        studentAgentBindingVo.setFkCompanyId(student.getFkCompanyId());
        //获取当前学生所绑定代理
        List<AgentsBindingVo> agentBinding = studentMapper.getStudentAgentBinding(fkStudentNum);
        if (GeneralTool.isNotEmpty(agentBinding)) {
            studentAgentBindingVo.setStudentAgentsBinding(agentBinding);
        }

        //获取该学生下的申请方案绑定信息
        List<AgentsBindingVo> agentByStudentIdTypeKey = studentOfferService.getAgentByStudentIdTypeKey(fkStudentNum, TableEnum.SALE_STUDENT_OFFER.key);
        if (GeneralTool.isNotEmpty(agentBinding)) {
            studentAgentBindingVo.setStudentOfferBinding(agentByStudentIdTypeKey);
        }
        //获取该学生下保险绑定信息
        List<AgentsBindingVo> agentByStudentIdTypeKey1 = studentOfferService.getAgentByStudentIdTypeKey(fkStudentNum, TableEnum.SALE_STUDENT_INSURANCE.key);
        if (GeneralTool.isNotEmpty(agentBinding)) {
            studentAgentBindingVo.setStudentInsuranceBinding(agentByStudentIdTypeKey1);
        }
        //获取该学生下住宿绑定信息
        List<AgentsBindingVo> agentByStudentIdTypeKey2 = studentOfferService.getAgentByStudentIdTypeKey(fkStudentNum, TableEnum.SALE_STUDENT_ACCOMMODATION.key);
        if (GeneralTool.isNotEmpty(agentBinding)) {
            studentAgentBindingVo.setStudentAccommodationBinding(agentByStudentIdTypeKey2);
        }
        return studentAgentBindingVo;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStudentAgentBinding(StudentAgentBindingDto studentAgentBindingDto) {

        if (GeneralTool.isEmpty(studentAgentBindingDto.getModifiedMode())) {
            studentAgentBindingDto.setModifiedMode(0);
        }
        switch (studentAgentBindingDto.getModifiedMode()) {
            case 0:
                //单独设置申请方案
                setStudentAgent(studentAgentBindingDto);
                break;
            case 1:
                //单独设置申请方案
                setOfferAgent(studentAgentBindingDto);
                break;
            default:
                break;
        }
    }

    private void setOfferAgent(StudentAgentBindingDto studentAgentBindingDto) {
        if (GeneralTool.isEmpty(studentAgentBindingDto.getOfferIds())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("申请方案id不能为空"));
        }
        if (GeneralTool.isNotEmpty(studentAgentBindingDto.getFkStudentIds())) {
            LambdaQueryWrapper<Student> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.in(Student::getId, studentAgentBindingDto.getFkStudentIds());
            List<Student> students = studentMapper.selectList(lambdaQueryWrapper);
            if (GeneralTool.isEmpty(students)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("student_info_null"));
            }
            Set<Long> studentIds = students.stream().map(Student::getId).collect(Collectors.toSet());

            //取消学生绑定代理
            if (studentAgentBindingDto.getIsCancelOriginalBinding()) {
                LambdaQueryWrapper<StudentAgent> studentAgentWrapper = new LambdaQueryWrapper<>();
                studentAgentWrapper.in(StudentAgent::getFkStudentId, studentIds);
                StudentAgent studentAgent = new StudentAgent();
                studentAgent.setIsActive(false);
                studentAgent.setUnactiveDate(new Date());
                studentAgentMapper.update(studentAgent, studentAgentWrapper);
            }
            //新增学生绑定的代理信息
            if (GeneralTool.isNotEmpty(studentAgentBindingDto.getFkAgentId())) {
                LambdaQueryWrapper<StudentAgent> wrapper = new LambdaQueryWrapper<>();
                wrapper.in(StudentAgent::getFkStudentId, studentIds);
                wrapper.eq(StudentAgent::getFkAgentId, studentAgentBindingDto.getFkAgentId());
                List<StudentAgent> studentAgents = studentAgentMapper.selectList(wrapper);
                //已存在则不用添加
                if (GeneralTool.isEmpty(studentAgents)) {
                    Set<Long> studentAgentIds = studentAgents.stream().map(StudentAgent::getId).collect(Collectors.toSet());
                    studentIds.removeAll(studentAgentIds);
                }
                if (GeneralTool.isNotEmpty(studentIds)) {
                    List<StudentAgent> agentStudentList = new ArrayList<>();
                    for (Long id : studentIds) {
                        StudentAgent studentAgent = new StudentAgent();
                        studentAgent.setFkAgentId(studentAgentBindingDto.getFkAgentId());
                        studentAgent.setFkStudentId(id);
                        studentAgent.setIsActive(true);
                        studentAgent.setActiveDate(new Date());
                        agentStudentList.add(studentAgent);
                    }
                    studentAgentService.saveBatch(agentStudentList);
                }
            }

        }

        //修改学生绑定的申请方案信息
        if (GeneralTool.isNotEmpty(studentAgentBindingDto.getStudentOfferAgentId()) || GeneralTool.isNotEmpty(studentAgentBindingDto.getStudentOfferStaffId()) || GeneralTool.isNotEmpty(studentAgentBindingDto.getStudentOfferEmail())) {
            LambdaQueryWrapper<StudentOffer> studentOfferWrapper = new LambdaQueryWrapper<>();
            studentOfferWrapper.in(StudentOffer::getId, studentAgentBindingDto.getOfferIds());
            StudentOffer studentOffer = new StudentOffer();
            if (GeneralTool.isNotEmpty(studentAgentBindingDto.getStudentOfferAgentId())) {
                studentOffer.setFkAgentId(studentAgentBindingDto.getStudentOfferAgentId());
            }
            if (GeneralTool.isNotEmpty(studentAgentBindingDto.getStudentOfferStaffId())) {
                studentOffer.setFkStaffId(studentAgentBindingDto.getStudentOfferStaffId());
            }
            if (GeneralTool.isNotEmpty(studentAgentBindingDto.getStudentOfferEmail())) {
                studentOffer.setAgentContactEmail(studentAgentBindingDto.getStudentOfferEmail());
            }
            studentOfferMapper.update(studentOffer, studentOfferWrapper);

            List<StudentOffer> studentOffers = studentOfferMapper.selectList(studentOfferWrapper);

            //修改学习计划绑定信息
            Set<Long> studentOfferIds = studentOffers.stream().map(StudentOffer::getId).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(studentOfferIds)) {
                LambdaQueryWrapper<StudentOfferItem> studentOfferItemWrapper = new LambdaQueryWrapper<>();
                studentOfferItemWrapper.in(StudentOfferItem::getFkStudentOfferId, studentOfferIds);
                StudentOfferItem studentOfferItem = new StudentOfferItem();
                if (GeneralTool.isNotEmpty(studentAgentBindingDto.getStudentOfferAgentId())) {
                    studentOfferItem.setFkAgentId(studentAgentBindingDto.getStudentOfferAgentId());
                }
                if (GeneralTool.isNotEmpty(studentAgentBindingDto.getStudentOfferStaffId())) {
                    studentOfferItem.setFkStaffId(studentAgentBindingDto.getStudentOfferStaffId());
                }
                if (GeneralTool.isNotEmpty(studentAgentBindingDto.getStudentOfferAgentId()) || GeneralTool.isNotEmpty(studentAgentBindingDto.getStudentOfferStaffId())) {
                    studentOfferItemMapper.update(studentOfferItem, studentOfferItemWrapper);
                }
            }

        }
    }

    private void setStudentAgent(StudentAgentBindingDto studentAgentBindingDto) {
        if (GeneralTool.isEmpty(studentAgentBindingDto.getFkStudentIds())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("学生id不能为空"));
        }
        LambdaQueryWrapper<Student> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(Student::getId, studentAgentBindingDto.getFkStudentIds());
        List<Student> students = studentMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isEmpty(students)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("student_info_null"));
        }
        Set<Long> studentIds = students.stream().map(Student::getId).collect(Collectors.toSet());
        //同步issue代理
        Map<Long, String> idIssueInfoMap = students.stream().collect(HashMap::new, (map, student) -> map.put(student.getId(), student.getIdIssueInfo()), HashMap::putAll);
        //取消学生绑定代理
        if (studentAgentBindingDto.getIsCancelOriginalBinding()) {
            LambdaQueryWrapper<StudentAgent> studentAgentWrapper = new LambdaQueryWrapper<>();
            studentAgentWrapper.in(StudentAgent::getFkStudentId, studentIds);
            StudentAgent studentAgent = new StudentAgent();
            studentAgent.setIsActive(false);
            studentAgent.setUnactiveDate(new Date());
            studentAgentMapper.update(studentAgent, studentAgentWrapper);
        }
        //新增学生绑定的代理信息
        if (GeneralTool.isNotEmpty(studentAgentBindingDto.getFkAgentId())) {
            LambdaQueryWrapper<StudentAgent> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(StudentAgent::getFkStudentId, studentIds);
            wrapper.eq(StudentAgent::getFkAgentId, studentAgentBindingDto.getFkAgentId());
            List<StudentAgent> studentAgents = studentAgentMapper.selectList(wrapper);
            //已存在则不用添加
            if (GeneralTool.isEmpty(studentAgents)) {
                Set<Long> studentAgentIds = studentAgents.stream().map(StudentAgent::getId).collect(Collectors.toSet());
                studentIds.removeAll(studentAgentIds);
            }
            if (GeneralTool.isNotEmpty(studentIds)) {
                List<StudentAgent> agentStudentList = new ArrayList<>();
                for (Long id : studentIds) {
                    StudentAgent studentAgent = new StudentAgent();
                    studentAgent.setFkAgentId(studentAgentBindingDto.getFkAgentId());
                    studentAgent.setFkStudentId(id);
                    studentAgent.setIsActive(true);
                    studentAgent.setActiveDate(new Date());
                    agentStudentList.add(studentAgent);
                    //issue同步代理信息
                    if (GeneralTool.isNotEmpty(idIssueInfoMap) && GeneralTool.isNotEmpty(idIssueInfoMap.get(id))) {
                        Map<String, Object> queries = new HashMap<>();
                        queries.put("fkAgentId", studentAgentBindingDto.getFkAgentId().intValue());
                        queries.put("fkSutdentId", Integer.parseInt(idIssueInfoMap.get(id)));
                        HttpUtil.get(url + "/stu_appl/updateStudentAgent", queries);
                        log.info("issue同步代理信息中......" + "url:" + url + ",fkAgentId:" + studentAgentBindingDto.getFkAgentId() + ",fkSutdentId:" + idIssueInfoMap.get(id));
                    }
                }
                studentAgentService.saveBatch(agentStudentList);
            }
        }
        //修改学生绑定的申请方案信息
        if (GeneralTool.isNotEmpty(studentAgentBindingDto.getStudentOfferAgentId()) || GeneralTool.isNotEmpty(studentAgentBindingDto.getStudentOfferStaffId()) || GeneralTool.isNotEmpty(studentAgentBindingDto.getStudentOfferEmail())) {
            LambdaQueryWrapper<StudentOffer> studentOfferWrapper = new LambdaQueryWrapper<>();
            studentOfferWrapper.in(StudentOffer::getFkStudentId, studentIds);
            StudentOffer studentOffer = new StudentOffer();
            if (GeneralTool.isNotEmpty(studentAgentBindingDto.getStudentOfferAgentId())) {
                studentOffer.setFkAgentId(studentAgentBindingDto.getStudentOfferAgentId());
            }
            if (GeneralTool.isNotEmpty(studentAgentBindingDto.getStudentOfferStaffId())) {
                studentOffer.setFkStaffId(studentAgentBindingDto.getStudentOfferStaffId());
            }
            if (GeneralTool.isNotEmpty(studentAgentBindingDto.getStudentOfferEmail())) {
                studentOffer.setAgentContactEmail(studentAgentBindingDto.getStudentOfferEmail());
            }
            studentOfferMapper.update(studentOffer, studentOfferWrapper);

            List<StudentOffer> studentOffers = studentOfferMapper.selectList(studentOfferWrapper);
//            //修改代理联系人邮箱
//            if (GeneralTool.isNotEmpty(studentAgentBindingDto.getStudentOfferEmail()) && GeneralTool.isNotEmpty(studentOffers)){
//                Set<Long> contactPersonIds = studentOffers.stream().map(StudentOffer::getFkContactPersonId).collect(Collectors.toSet());
//                if (GeneralTool.isNotEmpty(contactPersonIds)){
//                    LambdaQueryWrapper<SaleContactPerson> contactPersonWrapper = new LambdaQueryWrapper<>();
//                    contactPersonWrapper.in(SaleContactPerson::getId,contactPersonIds);
//                    SaleContactPerson contactPerson = new SaleContactPerson();
//                    contactPerson.setEmail(studentAgentBindingDto.getStudentOfferEmail());
//                    contactPersonMapper.update(contactPerson,contactPersonWrapper);
//                }
//            }

            //修改学习计划绑定信息
            Set<Long> studentOfferIds = studentOffers.stream().map(StudentOffer::getId).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(studentOfferIds)) {
                LambdaQueryWrapper<StudentOfferItem> studentOfferItemWrapper = new LambdaQueryWrapper<>();
                studentOfferItemWrapper.in(StudentOfferItem::getFkStudentOfferId, studentOfferIds);
                StudentOfferItem studentOfferItem = new StudentOfferItem();
                if (GeneralTool.isNotEmpty(studentAgentBindingDto.getStudentOfferAgentId())) {
                    studentOfferItem.setFkAgentId(studentAgentBindingDto.getStudentOfferAgentId());
                }
                if (GeneralTool.isNotEmpty(studentAgentBindingDto.getStudentOfferStaffId())) {
                    studentOfferItem.setFkStaffId(studentAgentBindingDto.getStudentOfferStaffId());
                }
                if (GeneralTool.isNotEmpty(studentAgentBindingDto.getStudentOfferAgentId()) || GeneralTool.isNotEmpty(studentAgentBindingDto.getStudentOfferStaffId())) {
                    studentOfferItemMapper.update(studentOfferItem, studentOfferItemWrapper);
                }
            }

        }
        //修改学生绑定的住宿申请信息
        if (GeneralTool.isNotEmpty(studentAgentBindingDto.getAccommodationAgentId()) || GeneralTool.isNotEmpty(studentAgentBindingDto.getAccommodationStaffId())) {
            LambdaQueryWrapper<StudentAccommodation> accommodationWrapper = new LambdaQueryWrapper<>();
            accommodationWrapper.in(StudentAccommodation::getFkStudentId, studentIds);
            StudentAccommodation studentAccommodation = new StudentAccommodation();
            if (GeneralTool.isNotEmpty(studentAgentBindingDto.getAccommodationAgentId())) {
                studentAccommodation.setFkAgentId(studentAgentBindingDto.getAccommodationAgentId());
            }
            if (GeneralTool.isNotEmpty(studentAgentBindingDto.getAccommodationStaffId())) {
                studentAccommodation.setFkStaffId(studentAgentBindingDto.getAccommodationStaffId());
            }
            studentAccommodationMapper.update(studentAccommodation, accommodationWrapper);
        }
        //修改学生绑定的保险申请信息
        if (GeneralTool.isNotEmpty(studentAgentBindingDto.getInsuranceAgentId()) || GeneralTool.isNotEmpty(studentAgentBindingDto.getInsuranceStaffId())) {
            LambdaQueryWrapper<StudentInsurance> insuranceWrapper = new LambdaQueryWrapper<>();
            insuranceWrapper.in(StudentInsurance::getFkStudentId, studentIds);
            StudentInsurance studentInsurance = new StudentInsurance();
            if (GeneralTool.isNotEmpty(studentAgentBindingDto.getInsuranceAgentId())) {
                studentInsurance.setFkAgentId(studentAgentBindingDto.getInsuranceAgentId());
            }
            if (GeneralTool.isNotEmpty(studentAgentBindingDto.getInsuranceStaffId())) {
                studentInsurance.setFkStaffId(studentAgentBindingDto.getInsuranceStaffId());
            }
            studentInsuranceMapper.update(studentInsurance, insuranceWrapper);
        }

        if (GeneralTool.isNotEmpty(studentAgentBindingDto.getFeeAgentId()) || GeneralTool.isNotEmpty(studentAgentBindingDto.getFeeStaffId())) {
            LambdaQueryWrapper<StudentServiceFee> feeWrapper = new LambdaQueryWrapper<>();
            feeWrapper.in(StudentServiceFee::getFkStudentId, studentIds);
            StudentServiceFee studentServiceFee = new StudentServiceFee();
            if (GeneralTool.isNotEmpty(studentAgentBindingDto.getFeeAgentId())) {
                studentServiceFee.setFkAgentId(studentAgentBindingDto.getFeeAgentId());
            }
            if (GeneralTool.isNotEmpty(studentAgentBindingDto.getFeeStaffId())) {
                studentServiceFee.setFkStaffId(studentAgentBindingDto.getFeeStaffId());
            }
            studentServiceFeeMapper.update(studentServiceFee, feeWrapper);
        }
    }

    /**
     * 学生最终申请状态统计
     *
     * @Date 12:00 2022/6/30
     * <AUTHOR>
     */
    @Override
    public List<CurrentStudentApplicationStatusVo> getCurrentStudentApplicationStatus(CurrentStudentApplicationStatusListDto currentStudentApplicationStatusListDto) {
        List<CurrentStudentApplicationStatusVo> currentStudentApplicationStatusVoList = new ArrayList<>();
        if (GeneralTool.isEmpty(SecureUtil.getCountryIds())) {
            return currentStudentApplicationStatusVoList;
        }
        //业务下属
        Long staffId = SecureUtil.getStaffId();
        List<Long> staffFollowerIds = new ArrayList<>();

        //员工id + 业务下属员工ids
        Result<List<Long>> followerIds = permissionCenterClient.getStaffFollowerIds(staffId);
        if (followerIds.getData() != null) {
            staffFollowerIds = followerIds.getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
        staffFollowerIds.add(staffId);

        //业务国家
        List<Long> fkAreaCountryIds = SecureUtil.getCountryIds();
        //查找失败步骤信息
        StudentOfferItemStep studentOfferItemStep = studentOfferItemStepMapper.selectOne(Wrappers.<StudentOfferItemStep>lambdaQuery().eq(StudentOfferItemStep::getStepKey, ProjectKeyEnum.STEP_FAILURE.key));
        Boolean isBd = studentOfferService.getIsBd(staffId);
        currentStudentApplicationStatusVoList = studentOfferItemMapper.getCurrentStudentApplicationStatus(currentStudentApplicationStatusListDto,
                fkAreaCountryIds, staffFollowerIds, studentOfferItemStep.getStepOrder(), SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(),
                SecureUtil.getStaffInfo().getIsStudentAdmin(), isBd, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());
        return currentStudentApplicationStatusVoList;
    }

    /**
     * 我的学生申请状态统计
     *
     * @Date 11:47 2022/7/14
     * <AUTHOR>
     */
    @Override
    public List<CurrentStudentApplicationStatusStatisticsVo> getCurrentStudentApplicationStatusStatistics(CurrentStudentApplicationStatusStatisticsDto currentStudentApplicationStatusStatisticsDto) {
        if (GeneralTool.isEmpty(SecureUtil.getCountryIds())) {
            return Collections.emptyList();
        }
        //业务下属
        Long staffId = SecureUtil.getStaffId();
        List<Long> staffFollowerIds = new ArrayList<>();

        //员工id + 业务下属员工ids
        Result<List<Long>> followerIds = permissionCenterClient.getStaffFollowerIds(staffId);
        if (followerIds.getData() != null) {
            staffFollowerIds = followerIds.getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
        staffFollowerIds.add(staffId);
        //业务国家
        List<Long> fkAreaCountryIds = SecureUtil.getCountryIds();
        if (GeneralTool.isNotEmpty(currentStudentApplicationStatusStatisticsDto.getSelectType()) &&
                currentStudentApplicationStatusStatisticsDto.getSelectType() == 4) {
            return studentOfferItemMapper.getPerformanceStatusStatistics(currentStudentApplicationStatusStatisticsDto, staffFollowerIds, fkAreaCountryIds, SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(), SecureUtil.getStaffInfo().getIsStudentAdmin());
        } else {
            return studentOfferItemMapper.getCurrentStudentApplicationStatusStatistics(currentStudentApplicationStatusStatisticsDto, staffFollowerIds,
                    fkAreaCountryIds, SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(), SecureUtil.getStaffInfo().getIsStudentAdmin(),
                    SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());
        }
    }

    /**
     * Author Cream
     * Description : // 获取学生毕业国家下拉
     * Date 2022/8/26 10:49
     * Params:
     * Return
     */
    @Override
    public List<BaseSelectEntity> getStudentAllGraduatedCountrySelect(Long companyId) {
        if (GeneralTool.isNull(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return studentMapper.getStudentAllGraduatedCountrySelect(companyId);
    }

    /**
     * Author Cream
     * Description : // 获取学生毕业国家下的周省下拉
     * Date 2022/8/26 10:49
     * Params:
     * Return
     */
    @Override
    public List<BaseSelectEntity> getStudentGraduateCountryMpStateSelect(Long id, Long companyId) {
        if (GeneralTool.isNull(id) || GeneralTool.isNull(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return studentMapper.getStudentGraduateCountryMpStateSelect(id, companyId);
    }

    /**
     * Author Cream
     * Description : // 获取学生毕业学校下拉
     * Date 2022/8/26 10:49
     * Params:
     * Return
     */
    @Override
    public List<BaseSelectEntity> getStudentGraduateSchool(Long companyId, Long countryId, Long stateId) {
        if (GeneralTool.isNull(countryId) || GeneralTool.isNull(stateId) || GeneralTool.isNull(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return studentMapper.getStudentGraduateSchool(companyId, countryId, stateId);
    }

    /**
     * Author Cream
     * Description : //获取申请计划国家下拉
     * Date 2022/8/26 10:50
     * Params:
     * Return
     */
    @Override
    public List<BaseSelectEntity> getStudentStudyPlanCountrySelect(Long companyId) {
        if (GeneralTool.isNull(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return studentOfferItemMapper.getStudentStudyPlanCountrySelect(companyId);
    }

    /**
     * Author Cream
     * Description : // 获取申请计划院校下拉
     * Date 2022/8/26 10:53
     * Params:
     * Return
     */
    @Override
    public List<BaseSelectEntity> getStudentStudyPlanSchoolSelect(Long companyId, Long id) {
        if (GeneralTool.isNull(id) || GeneralTool.isNull(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return studentOfferItemMapper.getStudentStudyPlanSchoolSelect(companyId, id);
    }

    /**
     * Author Cream
     * Description : // 获取学生课程统计信息
     * Date 2022/8/26 16:52
     * Params:
     * Return
     *
     * @return
     */
    @Override
    public StudentCourseStatisticsListDto getStudentCourseStatisticsInfoList(StudentCourseStatisticsSearchDto statisticsSearchVo, Page page) {
        if (GeneralTool.isEmpty(statisticsSearchVo) || GeneralTool.isEmpty(statisticsSearchVo.getCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        IPage<StudentCourseStatisticsVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        StudentCourseStatisticsListDto listVo = new StudentCourseStatisticsListDto();
        //获取当前登录人
        Long staffId = SecureUtil.getStaffId();
        String locale = SecureUtil.getLocale();
        //设置课程名称
        statisticsSearchVo.setCourseName(DataConverter.stringManipulation(statisticsSearchVo.getCourseName()));
        //查询所有学历
        List<StudentEducationLevelType> studentEducationLevelTypes = studentEducationLevelTypeMapper.selectList(Wrappers.lambdaQuery());
        Map<Long, String> educationMap = studentEducationLevelTypes.stream().collect(Collectors.toMap(StudentEducationLevelType::getId, StudentEducationLevelType::getTypeNameChn));
        //查询申请计划案例列表, union all 主要是要查询学生国外学历的一些成绩和其他信息
        CompletableFuture<Void> t1 = CompletableFuture.runAsync(() -> {
            List<StudentCourseStatisticsVo> courseStatistics = studentOfferItemMapper.getStudentCourseStatistics(iPage, statisticsSearchVo, SecureUtil.getStaffInfoByStaffId(staffId).getIsStudentOfferItemFinancialHiding(), SecureUtil.getInstitutionIds());
            page.setAll((int) iPage.getTotal());
            //学历，成绩类型参数封装
            courseStatistics.forEach(f -> {
                Integer professionalScoreType = f.getProfessionalScoreType();
                if (GeneralTool.isNotEmpty(professionalScoreType) && professionalScoreType > 0) {
                    String value = ProjectExtraEnum.getValueByKey(professionalScoreType, ProjectExtraEnum.HIGH_SCHOOL_GRADES, locale);
                    if (StringUtils.isBlank(value)) {
                        value = ProjectExtraEnum.getValueByKey(professionalScoreType, ProjectExtraEnum.UNDERGRADUATE_ACHIEVEMENT, locale);
                    }
                    f.setProfessionalScoreTypeName(value);
                }
                if (GeneralTool.isNotEmpty(f.getEnglishTestType())) {
                    f.setEnglishTestTypeName(ProjectExtraEnum.getValueByKey(f.getEnglishTestType(), ProjectExtraEnum.ENGLISH_TEST_TYPE, locale));
                }
                String education = f.getEducationType();
                if (StringUtils.isNotBlank(education) && StringUtils.isNumeric(education)) {
                    f.setEducationName(educationMap.get(Long.valueOf(education)));
                }
            });
            listVo.setStatisticsInfoList(courseStatistics);
        });
        //统计获得offer的案例，和没获得offer的案例
        CompletableFuture<Void> t2 = CompletableFuture.runAsync(() -> {
            List<Integer> c2 = studentOfferItemMapper.getStudentOfferStepStatus(null, statisticsSearchVo,
                    SecureUtil.getStaffInfoByStaffId(staffId).getIsStudentOfferItemFinancialHiding(), SecureUtil.getInstitutionIds(),
                    SecureUtil.getPermissionGroupInstitutionIds(),
                    SecureUtil.getStaffBoundBdIds());
            long count = c2.stream().filter(c -> c == 1).count();
            double cSize = c2.size();
            BigDecimal b1 = new BigDecimal(cSize);
            BigDecimal bigDecimal = new BigDecimal(100);
            if (count > 0) {
                BigDecimal b2 = BigDecimal.valueOf((double) count);
                listVo.setYOfferRatio(b2.divide(b1, 2, RoundingMode.HALF_UP).multiply(bigDecimal).doubleValue());
            }
            listVo.setNOfferRatio(100.00 - listVo.getYOfferRatio());
        });
        //执行任务
        CompletableFuture.allOf(t1, t2).join();
        return listVo;
    }

    @Override
    public StudentGraduationBackgroundVo getStudentGraduationBackgroundInfo(StudentCourseStatisticsSearchDto statisticsSearchVo) {
        StudentGraduationBackgroundVo graduationBackgroundDto = studentMapper.getStudentGraduationBackgroundInfo(statisticsSearchVo, SecureUtil.getIsStudentOfferItemFinancialHiding());
        Integer studentCount = studentMapper.selectCount(Wrappers.lambdaQuery());
        graduationBackgroundDto.setTotalStudentNum(studentCount);
        Integer numberOfDomestic = studentMapper.selectCount(Wrappers.<Student>lambdaQuery().isNotNull(Student::getFkInstitutionIdEducation));
        Integer internationalStudentCount = studentMapper.selectCount(Wrappers.<Student>lambdaQuery().isNotNull(Student::getFkInstitutionIdEducation2));
        graduationBackgroundDto.setNumberInternational(internationalStudentCount);
        graduationBackgroundDto.setNumberOfDomestic(numberOfDomestic);
        Integer graduationStudentCount = studentMapper.selectCount(Wrappers.<Student>lambdaQuery().isNotNull(Student::getFkInstitutionIdEducation).or().isNotNull(Student::getFkInstitutionIdEducation2));
        graduationBackgroundDto.setGraduationTotalStudentCount(graduationStudentCount);
        return graduationBackgroundDto;
    }

    /**
     * Author Cream
     * Description : // 学生课程信息导出
     * Date 2022/8/29 9:45
     * Params: statisticsSearchVo
     * Return
     */
    @Override
    public void exportStudentCourseStatisticsInfo(StudentCourseStatisticsSearchDto statisticsSearchVo, HttpServletResponse response) {
        if (GeneralTool.isEmpty(statisticsSearchVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        List<StudentCourseStatisticsVo> infoList = studentOfferItemMapper.getStudentCourseStatisticsExportInfo(statisticsSearchVo, SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding());
        if (infoList.isEmpty()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("export_empty"));
        }
        List<StudentCourseStatisticsExportVo> statisticsExportDtoList = new ArrayList<>();
        List<StudentEducationLevelType> studentEducationLevelTypes = studentEducationLevelTypeMapper.selectList(Wrappers.lambdaQuery());
        Map<Long, String> educationMap = studentEducationLevelTypes.stream().collect(Collectors.toMap(StudentEducationLevelType::getId, StudentEducationLevelType::getTypeNameChn));
        for (StudentCourseStatisticsVo statisticsDto : infoList) {
            StudentCourseStatisticsExportVo exportDto = new StudentCourseStatisticsExportVo();
            String education = statisticsDto.getEducationType();
            if (StringUtils.isNotBlank(education) && StringUtils.isNumeric(education)) {
                statisticsDto.setEducationName(educationMap.get(Long.valueOf(education)));
            }
            BeanUtils.copyProperties(statisticsDto, exportDto);
            Integer professionalScoreType = statisticsDto.getProfessionalScoreType();
            if (GeneralTool.isNotEmpty(professionalScoreType) && professionalScoreType > 0) {
                String value = ProjectExtraEnum.getValueByKey(professionalScoreType, ProjectExtraEnum.HIGH_SCHOOL_GRADES);
                if (StringUtils.isBlank(value)) {
                    value = ProjectExtraEnum.getValueByKey(professionalScoreType, ProjectExtraEnum.UNDERGRADUATE_ACHIEVEMENT);
                }
                exportDto.setProfessionalScoreTypeName(value);
            }
            if (GeneralTool.isNotEmpty(statisticsDto.getEnglishTestType())) {
                exportDto.setEnglishTestTypeName(ProjectExtraEnum.getValueByKey(statisticsDto.getEnglishTestType(), ProjectExtraEnum.ENGLISH_TEST_TYPE));
            }
            statisticsExportDtoList.add(exportDto);
        }
        FileUtils.exportExcel(response, statisticsExportDtoList, "studentCourse", StudentCourseStatisticsExportVo.class);
    }

    /**
     * Author Cream
     * Description : //获取学历下拉
     * Date 2023/3/30 10:04
     * Params:
     * Return
     */
    @Override
    public List<BaseSelectEntity> getEducationDropDown() {
        List<BaseSelectEntity> selectEntities = studentEducationLevelTypeMapper.getEducationDropDown();
        return selectEntities;
    }

    /**
     * @param studentInfoDto
     * @return StudentAgentBindingNewVo
     * @author: Neil
     * @description: 根据学生名称获取学生绑定信息
     * @date: 2022/12/5 12:35
     */
    @Override
    public List<StudentAgentBindingNewVo> getStudentAgentBindingByStudentName(StudentInfoDto studentInfoDto, Page page) {

        if (GeneralTool.isEmpty(studentInfoDto.getStudentName())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }

        IPage<StudentAgentBindingNewVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));

        if (StringUtils.isNotBlank(studentInfoDto.getStudentName())) {
            studentInfoDto.setStudentName(studentInfoDto.getStudentName().replace(" ", "").trim());
        }

        List<StudentAgentBindingNewVo> studentAgentBindingNewList = studentMapper.getStudentAgentBindingByStudentName(iPage, studentInfoDto.getStudentName(),
                studentInfoDto.getFkCompanyId());

        if (GeneralTool.isEmpty(studentAgentBindingNewList)) {
            return Collections.emptyList();
        }
        page.setAll((int) iPage.getTotal());

        List<Long> studentIds_ = studentAgentBindingNewList.stream().map(StudentAgentBindingNewVo::getStudentId).collect(Collectors.toList());
        List<StudentOffer> studentOffers = studentOfferService.getStudentOffersByStudentIds(studentIds_);
        Set<Long> fkAgentIds = new HashSet<>();
        Set<Long> fkStaffIds = new HashSet<>();
        if (GeneralTool.isNotEmpty(studentOffers)) {
            fkAgentIds = studentOffers.stream().map(StudentOffer::getFkAgentId).collect(Collectors.toSet());
            fkStaffIds = studentOffers.stream().map(StudentOffer::getFkStaffId).collect(Collectors.toSet());
        }

        //根据学生ids查询代理和BD
        List<Map<String, Object>> studentAgentBdList = studentOfferItemMapper.getAgentBdByStudentId(studentIds_);

        //根据学生ids查询学生绑定代理
        Map<Long, List<StudentAgentVo>> agentStaffNameMap = new HashMap<>();
        Set<Long> studentIds = studentAgentBindingNewList.stream().map(StudentAgentBindingNewVo::getStudentId).collect(Collectors.toSet());
        List<StudentAgentVo> agentStaffNameList = studentAgentMapper.getAgentStaffNameByStudentIds(studentIds);
        if (GeneralTool.isNotEmpty(agentStaffNameList)) {
            agentStaffNameMap = agentStaffNameList.stream().collect(Collectors.groupingBy(StudentAgentVo::getFkStudentId));
        }

        /*
        学生是否存在实付佣金绑定数据，需要考虑
         m_student_offer_item（留学申请计划）
         m_student_insurance（留学保险）
         m_student_accommodation（留学住宿）
         m_student_service_fee（留学服务费）
         */
        List<String> keys = Arrays.asList(TableEnum.SALE_STUDENT_OFFER_ITEM.key, TableEnum.SALE_STUDENT_INSURANCE.key,
                TableEnum.SALE_STUDENT_ACCOMMODATION.key, TableEnum.SALE_STUDENT_SERVICE_FEE.key);
        Result<Map<Long, Integer>> result = financeCenterClient.getPaymentFormItemList(keys, studentIds);
        // key:学生id value:存在的实付佣金绑定数据条数
        Map<Long, Integer> countMap = new HashMap<>();
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            countMap = result.getData();
        }

        //获取所有代理ids、员工ids
//        for (Map<String, Object> studentAgentBd : studentAgentBdList) {
//            if (GeneralTool.isNotEmpty(studentAgentBd.get("fkAgentId"))) {
//                fkAgentIds.add(Long.valueOf(studentAgentBd.get("fkAgentId").toString()));
//            }
//            if (GeneralTool.isNotEmpty(studentAgentBd.get("fkStaffId"))) {
//                fkStaffIds.add(Long.valueOf(studentAgentBd.get("fkStaffId").toString()));
//            }
//        }

        //根据代理ids获取名称
        Map<Long, Agent> agentNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkAgentIds)) {
            agentNamesByIds = agentService.getAgentsByIds(fkAgentIds);
        }
        //根据员工ids获取姓名
        Map<Long, String> staffNamesByIds = new HashMap<>();
        Set<Long> allStaffIds = permissionCenterClient.getAllStaffIds();
        staffNamesByIds = permissionCenterClient.getStaffNamesByIds(allStaffIds);

        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(studentOffers)) {
            Set<Long> countryIds = studentOffers.stream().map(StudentOffer::getFkAreaCountryId).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(countryIds)) {
                if (GeneralTool.isNotEmpty(countryIds)) {
                    countryNamesByIds = institutionCenterClient.getCountryNamesByIds(countryIds).getData();
                }
            }
        }

        for (StudentAgentBindingNewVo studentAgentBindingNewVo : studentAgentBindingNewList) {
            if (GeneralTool.isNotEmpty(agentStaffNameMap)) {
                List<StudentAgentVo> studentAgentVos = agentStaffNameMap.get(studentAgentBindingNewVo.getStudentId());
                if (GeneralTool.isNotEmpty(studentAgentVos)) {
                    Set<String> currentStaffNameList = new HashSet<>();
                    for (StudentAgentVo studentAgentVo : studentAgentVos) {
                        currentStaffNameList.add(studentAgentVo.getAgentName());
                    }
                    studentAgentBindingNewVo.setCurrentStaffNameList(currentStaffNameList);
                }
            }

            List<StudentOffer> offerList =
                    studentOffers.stream().filter(studentOffer -> studentOffer.getFkStudentId().equals(studentAgentBindingNewVo.getStudentId())).collect(Collectors.toList());
            //当前绑定代理 / BD
            Set<String> currentBdNameAndStaffNameList = new HashSet<>();

            for (StudentOffer studentOffer : offerList) {
                StringBuilder sb = new StringBuilder();
                //获取国家名称
                String agentName = "";
                String nameNote = "";
                if (agentNamesByIds.containsKey(studentOffer.getFkAgentId())) {
                    Agent agent = agentNamesByIds.get(studentOffer.getFkAgentId());
                    agentName = agent.getName();
                    nameNote = agent.getNameNote();
                    if (StringUtils.isNotBlank(nameNote)) {
                        agentName += "（" + nameNote + "）";
                    }
                }
                //获取代理名称
                //获取员工姓名
                String staffName = staffNamesByIds.get(studentOffer.getFkStaffId());
                if (GeneralTool.isNotEmpty(staffName)) {
                    sb.append(agentName).append("（").append(staffName).append("）");
                }
                currentBdNameAndStaffNameList.add(sb.toString());
            }

            Set<Long> offerCountryIds = new HashSet<>();
            offerCountryIds.addAll(offerList.stream().map(StudentOffer::getFkAreaCountryId).collect(Collectors.toSet()));
            //根据国家ids获取国家名称
            Map<Long, String> offerCountryNamesByIds = new HashMap<>();

            countryNamesByIds.forEach((key, value) -> {
                if (offerCountryIds.contains(key)) {
                    offerCountryNamesByIds.put(key, value);
                }
            });

            if (GeneralTool.isNotEmpty(offerCountryNamesByIds)) {
                studentAgentBindingNewVo.setAreaCountryNames(offerCountryNamesByIds.values().stream().collect(Collectors.joining(",")));
            }
            studentAgentBindingNewVo.setCurrentBdNameAndStaffNameList(currentBdNameAndStaffNameList);
            if (GeneralTool.isNotEmpty(currentBdNameAndStaffNameList) && currentBdNameAndStaffNameList.size() > 1) {
                studentAgentBindingNewVo.setIsMultipleOffers(true);
            }
            // 是否存在实付佣金绑定数据
            Integer count = countMap.get(studentAgentBindingNewVo.getStudentId());
            studentAgentBindingNewVo.setExistPaymentForm(count != null && count > 0);
        }
        return studentAgentBindingNewList;
    }

    @Override
    public List<StudentAgentEmailVo> getStudentInfoByEmail(String email, Long companyId) {

        Map map = new HashMap(16);
        map.put("email", email);
        map.put("companyId", companyId);
        String body = HttpUtil.createGet(emailMateUrl + "?" + HttpUtil.toParams(map)).execute().body();
        EmailMatchScoreVo emailMatchScoreVo = JSONObject.parseObject(body, EmailMatchScoreVo.class);
        List<EmailMatchScoreVo.EmailMatchDto> data = emailMatchScoreVo.getData();
        log.info("相似邮箱匹配结果： Params:{},data:{}", map, data);
        if (GeneralTool.isNotEmpty(data)) {
            Set<Long> ids = data.stream().map(EmailMatchScoreVo.EmailMatchDto::getMStudentId).collect(Collectors.toSet());
            List<StudentAgentEmailVo> studentAgentEmailVoList = studentMapper.getStudentInfoByEmail(ids);
            return studentAgentEmailVoList;
        }
        return Collections.emptyList();
    }

    @Override
    public void updatePassportNum(Long id, String passportNum) {
        Student student = studentMapper.selectById(id);
        if (GeneralTool.isEmpty(student)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        student.setPassportNum(passportNum);
        studentMapper.updateById(student);
    }

    /**
     * Author Cream
     * Description : //学生信息合并
     * Date 2023/5/11 14:28
     * Params:
     * Return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SaveResponseBo mergeStudentInformation(Long mergedStudentId, Long targetStudentId, String stuSource) {
        if (Objects.isNull(mergedStudentId) || Objects.isNull(targetStudentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        int count = studentMapper.selectCount(Wrappers.<Student>lambdaQuery().in(Student::getId, Arrays.asList(mergedStudentId, targetStudentId)));
        if (count != 2) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("illegal_student_data"));
        }
        //检查学生是否存在不同代理
        studentAgentService.checkDifferentAgent(mergedStudentId, targetStudentId);
        //处理学生基本信息
        mergeData(mergedStudentId, targetStudentId);
        //处理合并issue学生
        studentIssueStudentService.processIssueStudent(mergedStudentId, targetStudentId, stuSource);
        //合并学习方案数据
        studentOfferService.mergeData(mergedStudentId, targetStudentId);
        //合并留学保险数据
        studentInsuranceService.mergeData(mergedStudentId, targetStudentId);
        //合并留学住宿数据
        studentAccommodationService.mergeData(mergedStudentId, targetStudentId);
        //合并申请计划数据
        offerItemService.mergeData(mergedStudentId, targetStudentId);
        //合并服务费数据
        studentServiceFeeService.mergeData(mergedStudentId, targetStudentId);
        //合并学生业绩结算数据
        staffCommissionActionService.mergeData(mergedStudentId, targetStudentId);
        //合并学生代理数据
        studentAgentService.mergeData(mergedStudentId, targetStudentId);
        //合并是否计算业绩关系数据
        staffCommissionStudentService.mergeData(mergedStudentId, targetStudentId);
        //合并学生事件
        studentEventService.mergeData(mergedStudentId, targetStudentId);
        //合并学生备注
        commentService.mergeData(mergedStudentId, targetStudentId);
        //删除被合并学生联系人信息
        studentContactPersonService.deleteByStudentId(mergedStudentId);
        //删除被合并学生事件
        //studentEventService.deleteByStudentId(mergedStudentId);
        //删除被合并学生成绩数据
        studentSubjectScoreService.deleteByStudentId(mergedStudentId);
        //删除被合并学生issue关联表数据
//        studentIssueStudentService.deleteByStudentId(mergedStudentId);
        //删除被合并学生ISSUE课程关联数据
//        studentOfferItemIssueInstitutionCourseService.deleteByStudentId(mergedStudentId);
        //删除被合并学生备注
        //commentService.deleteByStudentId(mergedStudentId);
        //删除学生附件
        attachedService.deleteByStudentId(mergedStudentId);
        //删除被合并学生
        studentMapper.deleteById(mergedStudentId);
        return SaveResponseBo.ok(targetStudentId);
    }

    //处理学生基本信息
    private void mergeData(Long mergedStudentId, Long targetStudentId) {
        Student merged = studentMapper.selectById(mergedStudentId);
        Student target = studentMapper.selectById(targetStudentId);

        //备注信息合并
        StringBuilder remark = new StringBuilder();
        if (GeneralTool.isNotEmpty(merged.getRemark())) {
            if (GeneralTool.isNotEmpty(target.getRemark())) {
                remark.append(target.getRemark()).append("\n").append(merged.getRemark());
            } else {
                remark.append(merged.getRemark());
            }
            target.setRemark(remark.toString());
        }

        //业务状态信息合并
        if (GeneralTool.isNotEmpty(merged.getConditionType()) && merged.getConditionType().contains("0")) {
            if (GeneralTool.isNotEmpty(target.getConditionType()) && !target.getConditionType().contains("0")) {
                target.setConditionType(target.getConditionType() + ",0");
            } else {
                target.setConditionType("0");
            }
        }
        studentMapper.updateById(target);
    }

    /**
     * Author Cream
     * Description : //通过学生编号获取学生
     * Date 2023/5/11 14:36
     * Params:
     * Return
     */
    @Override
    public StudentVo getStudentByNum(String num) {
        if (StringUtils.isBlank(num)) {
            return null;
        }
        return studentMapper.getStudentByNum(num);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void mergeStudentIssueId(Long mergedStudentId, Long targetStudentId) {
        RStudentIssueStudent mergedStudent = rStudentIssueStudentMapper.selectOne(new LambdaQueryWrapper<RStudentIssueStudent>().eq(RStudentIssueStudent::getFkStudentId, mergedStudentId));


        RStudentIssueStudent targetIssueStudent = rStudentIssueStudentMapper.selectOne(new LambdaQueryWrapper<RStudentIssueStudent>().eq(RStudentIssueStudent::getFkStudentId, targetStudentId));

        if (GeneralTool.isEmpty(mergedStudent) || GeneralTool.isEmpty(mergedStudent)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }

        mergedStudent.setFkStudentIdIssue(targetIssueStudent.getFkStudentIdIssue2());
        targetIssueStudent.setFkStudentIdIssue2(mergedStudent.getFkStudentIdIssue());

        mergedStudent.setFkStudentIdIssue2(null);
        targetIssueStudent.setFkStudentIdIssue(null);

        rStudentIssueStudentMapper.updateByIdWithNull(mergedStudent);

        rStudentIssueStudentMapper.updateByIdWithNull(targetIssueStudent);
    }

    @Override
    public List<StudentVo> getClientStudent(ClientStudentDto clientStudentDto, Page page) {
        IPage<StudentVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        clientStudentDto.setFkBusinessCountryIds(SecureUtil.getCountryIds());
        List<StudentVo> clientApprovalDtos = studentMapper.getClientStudent(iPage, clientStudentDto);
        page.setAll((int) iPage.getTotal());

        if (GeneralTool.isNotEmpty(clientApprovalDtos)) {
            Set<Long> ids = clientApprovalDtos.stream().map(StudentVo::getId).collect(Collectors.toSet());
            Map<Long, Set<String>> agentNameMap = studentAgentService.getAgentNameListByStudentIds(ids);

            Set<Long> companyIds = clientApprovalDtos.stream().map(StudentVo::getFkCompanyId).collect(Collectors.toSet());
            Map<Long, String> companyMap = permissionCenterClient.getCompanyNamesByIds(companyIds).getData();

            for (StudentVo clientApprovalDto : clientApprovalDtos) {
                if (GeneralTool.isNotEmpty(agentNameMap)) {
                    clientApprovalDto.setCurrentStaffNameList(agentNameMap.get(clientApprovalDto.getId()));
                }
                if (GeneralTool.isNotEmpty(companyMap)) {
                    clientApprovalDto.setFkCompanyName(companyMap.get(clientApprovalDto.getFkCompanyId()));
                }
            }
        }

        return clientApprovalDtos;
    }

    /**
     * @param studentId
     * @param studentOfferId
     */
    @Override
    public void batchActivateEnrolFailure(Long studentId, Long studentOfferId) {
        Student student = studentMapper.selectById(studentId);
        if (Objects.isNull(student)) {
            return;
        }
        StudentOfferDto studentOfferDto = new StudentOfferDto();
        studentOfferDto.setFkStudentId(studentId);
        //获取业务下属
        List<Long> staffFollowerIds = new ArrayList<>();
        Result<List<Long>> longResult = permissionCenterClient.getStaffFollowerIds(SecureUtil.getStaffId());
        if (longResult.isSuccess() && GeneralTool.isNotEmpty(longResult.getData())) {
            staffFollowerIds.addAll(longResult.getData());
        }
        if (GeneralTool.isEmpty(staffFollowerIds)) {
            staffFollowerIds = new ArrayList<>();
        }
        staffFollowerIds.add(SecureUtil.getStaffId());
        //需要加权限
        Boolean isBd = studentOfferService.getIsBd(SecureUtil.getStaffId());
        List<StudentOffer> studentOffers = studentOfferMapper.getStudentOfferNew(null, studentOfferDto, staffFollowerIds, SecureUtil.getCountryIds(), SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(), SecureUtil.getStaffInfo().getIsStudentAdmin(), SecureUtil.getStaffId(), SecureUtil.getInstitutionIds(), isBd, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());
        if (GeneralTool.isEmpty(studentOffers)) {
            return;
        }
        //让用户可以单独操作某个国家线的申请
        if (GeneralTool.isNotEmpty(studentOfferId)) {
            studentOffers = studentOffers.stream().filter(studentOffer -> studentOffer.getId().equals(studentOfferId)).collect(Collectors.toList());
            if (GeneralTool.isEmpty(studentOffers)) {
                return;
            }
        }

        Set<Long> offerIds = studentOffers.stream().map(StudentOffer::getId).collect(Collectors.toSet());
        LambdaQueryWrapper<StudentOfferItem> lambdaQueryWrapper = Wrappers.<StudentOfferItem>lambdaQuery().in(StudentOfferItem::getFkStudentOfferId, offerIds)
                .eq(StudentOfferItem::getStatus, 1);

        if (!SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding()) {
            lambdaQueryWrapper.ne(StudentOfferItem::getIsFollowHidden, 1);
        }
        List<StudentOfferItem> studentOfferItems = studentOfferItemMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isEmpty(studentOfferItems)) {
            return;
        }


        List<Long> studentOfferItemIds = studentOfferItems.stream().map(StudentOfferItem::getId).collect(Collectors.toList());

        //入学失败的步骤id
        StudentOfferItemStep studentOfferItemStep = studentOfferItemStepMapper.selectOne(Wrappers.<StudentOfferItemStep>lambdaQuery()
                .eq(StudentOfferItemStep::getStepKey, ProjectKeyEnum.STEP_FAILURE.key));
        Long stepId = studentOfferItemStep.getId();

        //学习计划的步骤关系表
        List<RStudentOfferItemStep> rStudentOfferItemSteps = rStudentOfferItemStepMapper.selectList(Wrappers.lambdaQuery(RStudentOfferItemStep.class)
                .in(RStudentOfferItemStep::getFkStudentOfferItemId, studentOfferItemIds)
                .orderByDesc(RStudentOfferItemStep::getId));

        Map<Long, List<RStudentOfferItemStep>> stepsMap = rStudentOfferItemSteps.stream().collect(Collectors.groupingBy(RStudentOfferItemStep::getFkStudentOfferItemId));
        for (StudentOfferItem studentOfferItem : studentOfferItems) {
            //如果不是入学失败
            if (!studentOfferItem.getFkStudentOfferItemStepId().equals(stepId)) {
                continue;
            }
            List<RStudentOfferItemStep> studentOfferItemSteps = stepsMap.get(studentOfferItem.getId());
            if (GeneralTool.isEmpty(studentOfferItemSteps) || studentOfferItemSteps.size() < 2) {
                continue;
            }

            //前一次记录id
            Long updateStepId = studentOfferItemSteps.get(1).getFkStudentOfferItemStepId();
            //第一条记录为最新日志 这里为入学失败
            Long id = studentOfferItemSteps.get(0).getId();
            rStudentOfferItemStepMapper.deleteById(id);

            //更新
            studentOfferItem.setStudentOfferItemStepTime(new Date());
            studentOfferItem.setFkStudentOfferItemStepId(updateStepId);
            studentOfferItem.setFkEnrolFailureReasonId(null);
            studentOfferItem.setOtherFailureReason(null);
            utilService.updateUserInfoToEntity(studentOfferItem);
            studentOfferItemMapper.updateByIdWithNull(studentOfferItem);
        }
    }


    @Override
    public Boolean validateShareParthRequired() {
        Long fkDepartmentId = SecureUtil.getFkDepartmentId();
        Long fkCompanyId = SecureUtil.getFkCompanyId();
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.STUDENT_SHARED_PATH_REQUIRED.key, 1).getData();
        String configValue1 = companyConfigMap.get(fkCompanyId);
        List<Long> fkDepartmentIds = new ArrayList<>(JSON.parseArray(configValue1, Long.class));
        return GeneralTool.isNotEmpty(fkDepartmentIds) && fkDepartmentIds.contains(fkDepartmentId);

//        ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.STUDENT_SHARED_PATH_REQUIRED.key).getData();
//        if (GeneralTool.isNotEmpty(configDto) && GeneralTool.isNotEmpty(configDto.getValue1())){
//            JSONObject jsonObject = JSONObject.parseObject(configDto.getValue1());
//            List<Long> fkDepartmentIds = Lists.newArrayList();
//            com.alibaba.fastjson.JSONArray jsonArray = null;
//            if (fkCompanyId.equals(2L)){
//                jsonArray = jsonObject.getJSONArray("GEA");
//            }else if (fkCompanyId.equals(3L)){
//                jsonArray = jsonObject.getJSONArray("IAE");
//            }else {
//                jsonArray = jsonObject.getJSONArray("OTHER");
//            }
//            if (GeneralTool.isNotEmpty(jsonArray)){
//                fkDepartmentIds = jsonArray.toJavaList(Long.class);
//            }
//            return GeneralTool.isNotEmpty(fkDepartmentIds)&&fkDepartmentIds.contains(fkDepartmentId);
//        }
//        return false;
    }

    @Override
    public ReceiveApplyDataTimeConfigVo getReceiveApplyDataTimeConfig(Long fkStudentId) {

        if (GeneralTool.isEmpty(fkStudentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ReceiveApplyDataTimeConfigVo receiveApplyDataTimeConfigDto = new ReceiveApplyDataTimeConfigVo();
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.ADD_RECEIVED_APPLICATION_DATA_DATE.key, 1).getData();
        if (GeneralTool.isNotEmpty(companyConfigMap)) {
            String configValue = companyConfigMap.get(SecureUtil.getFkCompanyId());
            if (GeneralTool.isNotEmpty(configValue) && configValue.equals("1")) {
                receiveApplyDataTimeConfigDto.setIsSwitch(true);
            }
        }
        Student student = studentMapper.selectById(fkStudentId);
        if (GeneralTool.isNotEmpty(student)) {
            receiveApplyDataTimeConfigDto.setReceivedApplicationDataDate(student.getReceivedApplicationDataDate());
        }
        return receiveApplyDataTimeConfigDto;
    }

    @Override
    public void editReceiveApplyDataTimeConfig(ReceiveApplyDataTimeDto vo) {
        if (GeneralTool.isEmpty(vo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Student student = studentMapper.selectById(vo.getId());
        student.setReceivedApplicationDataDate(vo.getReceivedApplicationDataDate());
        studentMapper.updateByIdWithNull(student);
    }

    @Override
    public List<BaseSelectEntity> getStudentSelect(String name, List<Long> companyIds, Long studentId) {
        if (!SecureUtil.validateCompanys(companyIds)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        List<Student> students = studentMapper.getStudentSelect(name, companyIds, studentId);
        return Optional.ofNullable(students.stream().map(e -> {
            BaseSelectEntity baseSelect = new BaseSelectEntity();
            baseSelect.setId(e.getId());
            StringBuilder sb = new StringBuilder();
            sb.append(e.getName()).append("（").append(e.getLastName()).append(" ").append(e.getFirstName()).append("）");
            if (GeneralTool.isNotEmpty(e.getBirthday())) {
                sb.append("/").append(new SimpleDateFormat("yyyy-MM-dd").format(e.getBirthday()));
            }
            baseSelect.setFullName(sb.toString());
            return baseSelect;
        }).collect(Collectors.toList())).orElse(null);
    }

    @Override
    public Student selectById(Long studentId) {
        return studentMapper.selectById(studentId);
    }

    /**
     * AI获取学生信息
     *
     * @param studentName
     * @return
     */
    @Override
    public List<AiStudentDto> getAiStudentInfo(String studentName) {
        Long staffId = SecureUtil.getStaffId();
        List<Long> staffFollowerIds = new ArrayList<>();
        //员工id + 业务下属员工ids
        List<Long> staffFollowerIdsResult = permissionCenterClient.getStaffFollowerIds(staffId).getData();
        if (GeneralTool.isNotEmpty(staffFollowerIdsResult)) {
            staffFollowerIds = staffFollowerIdsResult.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
        staffFollowerIds.add(staffId);
        //员工 + 业务下属员工 loginId 去重
        staffFollowerIds = staffFollowerIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());

        List<AiStudentInfoVo> aiStudentInfoVoList = studentMapper.getAiStudentInfo(studentName, SecureUtil.getCompanyIds(),
                SecureUtil.getCountryIds(), staffFollowerIds, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds(),
                SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(),SecureUtil.getStaffInfo().getIsStudentAdmin(),
                SecureUtil.getInstitutionIds());
        if (GeneralTool.isEmpty(aiStudentInfoVoList)) {
            throw new GetServiceException("找不到该名字的学生");
        }
        List<AiStudentDto> aiStudentDtoList = new ArrayList<>();
        List<Long> aiStudentIds = aiStudentInfoVoList.stream().map(AiStudentInfoVo::getStudentId).collect(Collectors.toList());
        List<AiStudentOfferItemInfoVo> aiStudentOfferItemInfoVoList = studentOfferItemMapper.getAiStudentOfferItem(aiStudentIds, SecureUtil.getCompanyIds(),
                SecureUtil.getCountryIds(), staffFollowerIds, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds(),
                SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(),SecureUtil.getStaffInfo().getIsStudentAdmin(),
                SecureUtil.getInstitutionIds());
        Map<Long, List<AiStudentOfferItemInfoVo>> map = aiStudentOfferItemInfoVoList.stream().collect(Collectors.groupingBy(AiStudentOfferItemInfoVo::getFkStudentId));
        aiStudentInfoVoList.forEach(aiStudentInfoVo -> {
            AiStudentDto aiStudentDto = new AiStudentDto();
            aiStudentDto.setAiStudentInfoVo(aiStudentInfoVo);
            List<AiStudentOfferItemInfoVo> aiStudentOfferItemInfoVos = map.get(aiStudentInfoVo.getStudentId());
            aiStudentDto.setAiStudentOfferItemInfoVos(aiStudentOfferItemInfoVos);
            aiStudentDtoList.add(aiStudentDto);
        });
        return aiStudentDtoList;
    }

    /**
     * 获取激活状态
     *
     * @param studentIds
     * @return
     */
    private Map<Long, Boolean> getCommissionActiveStatusByStudentIds(Set<Long> studentIds) {
        Map<Long, Boolean> map = Maps.newHashMap();
        for (Long studentId : studentIds) {
            map.put(studentId, false);
        }
        List<StaffCommissionStudent> staffCommissionStudents = staffCommissionStudentService.list(Wrappers.lambdaQuery(StaffCommissionStudent.class)
                .in(StaffCommissionStudent::getFkStudentId, studentIds));
        if (GeneralTool.isEmpty(staffCommissionStudents)) {
            return map;
        }
        for (StaffCommissionStudent staffCommissionStudent : staffCommissionStudents) {
            map.put(staffCommissionStudent.getFkStudentId(), true);
        }
        return map;
    }
}
