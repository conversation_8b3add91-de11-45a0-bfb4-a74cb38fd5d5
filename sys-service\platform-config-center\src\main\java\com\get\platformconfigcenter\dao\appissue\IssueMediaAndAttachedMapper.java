package com.get.platformconfigcenter.dao.appissue;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.IssueMediaAndAttached;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
@DS("issuedb")
public interface IssueMediaAndAttachedMapper extends BaseMapper<IssueMediaAndAttached> {
//    int insert(IssueMediaAndAttached record);
//
//    int insertSelective(IssueMediaAndAttached record);
//
//    int updateByPrimaryKeySelective(IssueMediaAndAttached record);
//
//    int updateByPrimaryKey(IssueMediaAndAttached record);
//
//    Integer getNextIndexKey(@Param("tableId") Long tableId, @Param("tableName") String tableName);

}