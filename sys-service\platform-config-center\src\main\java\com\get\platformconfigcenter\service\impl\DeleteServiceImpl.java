package com.get.platformconfigcenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.utils.GeneralTool;
import com.get.platformconfigcenter.dao.appissue.AppFormConfigAttachmentMapper;
import com.get.platformconfigcenter.dao.appissue.AppFormConfigDivisionMapper;
import com.get.platformconfigcenter.dao.appissue.AppInstitutionCharacterItemMapper;
import com.get.platformconfigcenter.dao.appissue.AppInstitutionCharacterMapper;
//import com.get.platformconfigcenter.dao.appissue.StudentInstitutionCourseMapper;
import com.get.platformconfigcenter.dao.appmso.AgentRecommendMapper;
import com.get.platformconfigcenter.dao.appmso.UserInstitutionCourseCollectionMapper;
import com.get.platformconfigcenter.entity.AgentRecommend;
import com.get.platformconfigcenter.entity.AppFormConfigAttachment;
import com.get.platformconfigcenter.entity.AppFormConfigDivision;
import com.get.platformconfigcenter.entity.AppInstitutionCharacterItem;
import com.get.platformconfigcenter.service.DeleteService;
import com.get.platformconfigcenter.service.MediaAndAttachedMsoService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 删除业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2021/5/12 12:40
 */
@Service
public class DeleteServiceImpl implements DeleteService {
//    @Resource
//    private MediaAndAttachedMsoService mediaAndAttachedMsoService;
//    @Resource
//    private AgentRecommendMapper agentRecommendMapper;
//    @Resource
//    private AppInstitutionCharacterItemMapper appInstitutionCharacterItemMapper;
    @Resource
    private AppFormConfigAttachmentMapper appFormConfigAttachmentMapper;
//    @Resource
//    private AppFormConfigDivisionMapper appFormConfigDivisionMapper;
    @Resource
    private AppInstitutionCharacterMapper appInstitutionCharacterMapper;
//    @Resource
//    private StudentInstitutionCourseMapper studentInstitutionCourseMapper;
//    @Resource
//    private UserInstitutionCourseCollectionMapper userInstitutionCourseCollectionMapper;

    /**
     * @Description:删除国家资讯配置逻辑验证
     * @Param
     * @Date 12:42 2021/5/12
     * <AUTHOR>
     */
//    @Override
//    public boolean deleteValidateAgentModuleInfo(Long agentModuleId) {
//        //同时删除该表id下的所有媒体附件
//        mediaAndAttachedMsoService.deleteMediaAndAttached(TableEnum.AGENT_MODULE_INFO.key, agentModuleId);
//        return true;
//    }

//    @Override
//    public boolean deleteValidateSiteMap(Long siteMapId) {
//        //同时删除该表id下的所有媒体附件
//        mediaAndAttachedMsoService.deleteMediaAndAttached(TableEnum.M_SITEMAP.key, siteMapId);
//        return true;
//    }

//    @Override
//    public boolean deleteValidateAgentInfo(Long agentInfoId) {
//        //同时删除该表id下的所有媒体附件
//        mediaAndAttachedMsoService.deleteMediaAndAttached(TableEnum.AGENT_INFO.key, agentInfoId);
//        //删除代理推荐学校
////        Example example = new Example(AgentRecommend.class);
////        example.createCriteria().andEqualTo("fkAgentId", agentInfoId);
////        agentRecommendMapper.deleteByExample(example);
//
//        agentRecommendMapper.delete(Wrappers.<AgentRecommend>lambdaQuery().eq(AgentRecommend::getFkAgentId, agentInfoId));
//        return true;
//    }

    /**
     * 删除学生附件类型逻辑验证
     *
     * @Date 16:42 2021/5/20
     * <AUTHOR>
     */
    @Override
    public boolean deleteValidateStudentAttachment(Long studentAttachmentId) {
//        Example example = new Example(AppFormConfigAttachment.class);
//        example.createCriteria().andEqualTo("fkStudentAttachmentId", studentAttachmentId);
//        List<AppFormConfigAttachment> appFormConfigAttachments = appFormConfigAttachmentMapper.selectByExample(example);
        List<AppFormConfigAttachment> appFormConfigAttachments = appFormConfigAttachmentMapper.selectList(Wrappers.<AppFormConfigAttachment>lambdaQuery().eq(AppFormConfigAttachment::getFkStudentAttachmentId, studentAttachmentId));
        if (GeneralTool.isNotEmpty(appFormConfigAttachments)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("information_collection_configuration_attachment_data_association"));
        }
        return true;
    }

    /**
     * 删除申请表单板块逻辑验证
     *
     * @Date 10:51 2021/5/21
     * <AUTHOR>
     */
//    @Override
//    public boolean deleteValidateAppFormDivision(Long appFormDivisionId) {
////        Example example = new Example(AppFormConfigDivision.class);
////        example.createCriteria().andEqualTo("fkAppFormDivisionId", appFormDivisionId);
////        List<AppFormConfigDivision> appFormConfigDivisions = appFormConfigDivisionMapper.selectByExample(example);
//        List<AppFormConfigDivision> appFormConfigDivisions = appFormConfigDivisionMapper.selectList(Wrappers.<AppFormConfigDivision>lambdaQuery().eq(AppFormConfigDivision::getFkAppFormDivisionId, appFormDivisionId));
//        if (GeneralTool.isNotEmpty(appFormConfigDivisions)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("app_form_division_configure_data_association"));
//        }
//        return true;
//    }

    /**
     * 删除课程动态表单配置逻辑验证
     *
     * @Date 16:19 2021/5/21
     * <AUTHOR>
     */
//    @Override
//    public boolean deleteValidateAppInstitutionCharacter(Long appInstitutionCharacterId) {
////        Example example = new Example(AppInstitutionCharacterItem.class);
////        example.createCriteria().andEqualTo("fkAppInstitutionCharacterId", appInstitutionCharacterId);
////        List<AppInstitutionCharacterItem> appInstitutionCharacterItems = appInstitutionCharacterItemMapper.selectByExample(example);
//        List<AppInstitutionCharacterItem> appInstitutionCharacterItems = appInstitutionCharacterItemMapper.selectList(Wrappers.<AppInstitutionCharacterItem>lambdaQuery().eq(AppInstitutionCharacterItem::getFkAppInstitutionCharacterId, appInstitutionCharacterId));
//
//        if (GeneralTool.isNotEmpty(appInstitutionCharacterItems)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("institution_character_item_data_association"));
//        }
//        return true;
//    }

    /**
     * 删除信息收集内容配置逻辑验证
     *
     * @Date 18:04 2021/5/21
     * <AUTHOR>
     */
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public boolean deleteValidateAppFormConfig(Long appFormConfigId) {
//        //直接删关系表
////        Example example = new Example(AppFormConfigDivision.class);
////        example.createCriteria().andEqualTo("fkAppFormConfigId", appFormConfigId);
////        appFormConfigDivisionMapper.deleteByExample(example);
////        example = new Example(AppFormConfigAttachment.class);
////        example.createCriteria().andEqualTo("fkAppFormConfigId", appFormConfigId);
////        appFormConfigAttachmentMapper.deleteByExample(example);
//        appFormConfigDivisionMapper.delete(Wrappers.<AppFormConfigDivision>lambdaQuery().eq(AppFormConfigDivision::getFkAppFormConfigId, appFormConfigId));
//        appFormConfigAttachmentMapper.delete(Wrappers.<AppFormConfigAttachment>lambdaQuery().eq(AppFormConfigAttachment::getFkAppFormConfigId, appFormConfigId));
//        return true;
//    }


//    @Override
//    public boolean deleteValidateCourse(Long courseId) {
//        if (appInstitutionCharacterMapper.isExistByCourseId(courseId)) {
//            return true;
//        }
//
//        if (studentInstitutionCourseMapper.isExistByCourseId(courseId)) {
//            return true;
//        }
//
////        if (userInstitutionCourseCollectionMapper.isExistByCourseId(courseId)) {
////            return true;
////        }
//        return false;
//    }
}
