package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @DATE: 2020/12/18
 * @TIME: 17:52
 * @Description:
 **/
@Data
public class InstitutionCourseRankingVo extends BaseEntity {
    private String rankingName;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;

    //==============实体类InstitutionCourseRanking===================
    private static final long serialVersionUID = 1L;
    /**
     * 课程Id
     */
    @ApiModelProperty(value = "课程Id")
    @Column(name = "fk_institution_course_id")
    private Long fkInstitutionCourseId;
    /**
     * 排名类型：国家排名0/世界排名1
     */
    @ApiModelProperty(value = "排名类型：国家排名0/世界排名1")
    @Column(name = "ranking_type")
    private Integer rankingType;
    /**
     * 排名（最前）
     */
    @ApiModelProperty(value = "排名（最前）")
    @Column(name = "ranking_first")
    private Integer rankingFirst;
    /**
     * 排名（最后）
     */
    @ApiModelProperty(value = "排名（最后）")
    @Column(name = "ranking_last")
    private Integer rankingLast;
    /**
     * 排名描述
     */
    @ApiModelProperty(value = "排名描述")
    @Column(name = "ranking_note")
    private String rankingNote;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fkInstitutionCourseId=").append(fkInstitutionCourseId);
        sb.append(", rankingType=").append(rankingType);
        sb.append(", rankingFirst=").append(rankingFirst);
        sb.append(", rankingLast=").append(rankingLast);
        sb.append(", rankingNote=").append(rankingNote);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
