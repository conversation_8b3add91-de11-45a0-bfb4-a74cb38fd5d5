package com.get.permissioncenter.service.impl;
import cn.wanghaomiao.xpath.model.JXDocument;
import com.alibaba.fastjson.JSONObject;
import com.gargoylesoftware.htmlunit.WebClient;
import com.gargoylesoftware.htmlunit.html.HtmlForm;
import com.gargoylesoftware.htmlunit.html.HtmlImageInput;
import com.gargoylesoftware.htmlunit.html.HtmlPage;
import com.gargoylesoftware.htmlunit.html.HtmlPasswordInput;
import com.gargoylesoftware.htmlunit.html.HtmlTextInput;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.service.IConfigService;
import com.get.permissioncenter.service.IToolCaseService;
import com.get.permissioncenter.utils.AESUtils;
import com.get.permissioncenter.utils.HttpClientUtils;
import com.get.permissioncenter.utils.ToolCaseConstant;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by Jerry.
 * User: 14:57
 * Date: 2021/6/11
 * Description:工具箱业务实现类
 */
@Service
public class ToolCaseServiceImpl implements IToolCaseService {

    @Value("${faceMatch.url}")
    private String faceMatchUrl;

    @Resource
    private IConfigService configService;

    @Override
    public String loginAndGetPersonHtml(String code, HttpServletRequest request, HttpServletResponse response) {
        String codeUrl = ToolCaseConstant.CODEURL + code;
        WebClient webClient = new WebClient();
        HtmlPage loginPage;
        //用户名
        String userName = configService.getConfigValueByConfigKey(ToolCaseConstant.USERNAMEKEY);
        if (StringUtils.isEmpty(userName)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("not_configured_yet_userName_key_or_value"));
        }
        //密码
        String passWord = configService.getConfigValueByConfigKey(ToolCaseConstant.PASSWORDKEY);
        if (StringUtils.isEmpty(passWord)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("not_configured_yet_password_key_or_value"));
        }
        try {
            userName = AESUtils.Decrypt(userName, ToolCaseConstant.AESKEY);
            passWord = AESUtils.Decrypt(passWord, ToolCaseConstant.AESKEY);
        } catch (Exception e) {
            e.printStackTrace();
            throw new GetServiceException(LocaleMessageUtils.getMessage("exception_message") + e.getMessage());
        }

        String jSessionId = null;
        Cookie[] cookiesList = request.getCookies();
        if (null != cookiesList) {
            for (Cookie cookie : cookiesList) {
                if (ToolCaseConstant.COOKIENAME.equals(cookie.getName())) {
                    jSessionId = cookie.getValue();
                    break;
                }
            }
        }

        if (null == jSessionId) {
            try {
                loginPage = webClient.getPage(ToolCaseConstant.PAGEURL);
            } catch (IOException e) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("get_page_exception"));
            }

            //获取form表单
            HtmlForm form = loginPage.getHtmlElementById("loginForm");

            //提交按钮
            final HtmlImageInput button = form.getInputByName("Submit");
            //用户名输入框
            final HtmlTextInput j_username = form.getInputByName("j_username");
            //密码输入框
            final HtmlPasswordInput j_password = form.getInputByName("j_password");

            //设置用户名
            j_username.setValueAttribute(userName);
            //设置密码
            j_password.setValueAttribute(passWord);

            try {
                button.click();
            } catch (IOException e) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("simulated_login_exception"));
            }
            Set<com.gargoylesoftware.htmlunit.util.Cookie> cookies = webClient.getCookieManager().getCookies();
            jSessionId = cookies.iterator().next().toString();
            //获取;之前的JSESSIONID
            jSessionId = jSessionId.substring(0, jSessionId.indexOf(";"));
            Cookie cookie = new Cookie(ToolCaseConstant.COOKIENAME, jSessionId);
            cookie.setMaxAge(60 * 5);   //存活期为5分钟
            response.addCookie(cookie);
        }

        Map headerParams = new HashMap();
        headerParams.put("Cookie", jSessionId);
        //获取人员的所有信息
        String html = HttpClientUtils.sendPostRequest(codeUrl, headerParams, new HashMap<>());
        JXDocument jxDocument = new JXDocument(html);
        String srcPath = "//*[@id='photo']/table/tbody/tr[1]/td/img/@src";
        List<Object> srcSel = new ArrayList<>();
        try {
            srcSel = jxDocument.sel(srcPath);
        } catch (Exception e) {
            e.printStackTrace();
            throw new GetServiceException(LocaleMessageUtils.getMessage("exception_message") + e.getMessage());
        }
        String oldSrc = srcSel.get(0).toString();
        //获取图片地址里面的随机码,为了后面的替换字符以及对随机码的特殊字符进行编码
        oldSrc = oldSrc.substring(oldSrc.lastIndexOf("=") + 1);
        String base64 = "";
        try {
            base64 = HttpClientUtils.downloadImagesToBase64(ToolCaseConstant.IMAGEURL + URLEncoder.encode(oldSrc, "UTF-8"), jSessionId);
        } catch (Exception e) {
            e.printStackTrace();
            throw new GetServiceException(LocaleMessageUtils.getMessage("exception_message") + e.getMessage());
        }
        //图片base64路径
        base64 = "data:image/png;base64," + base64;

        //需要的div标签内容
        String divPath = "//body/div[position()>4]";
        List<Object> divSel = new ArrayList<>();
        try {
            divSel = jxDocument.sel(divPath);
        } catch (Exception e) {
            e.printStackTrace();
            throw new GetServiceException(LocaleMessageUtils.getMessage("exception_message") + e.getMessage());
        }
        StringBuilder sb = new StringBuilder();
        for (Object o : divSel) {
            if (null != o) {
                sb.append(o.toString());
            }
        }
        html = sb.toString();
        if (oldSrc.contains(">")) {
            oldSrc = oldSrc.replace(">", "&gt;");
        } else if (oldSrc.contains("<")) {
            oldSrc = oldSrc.replace("<", "&lt;");
        }
        html = html.replace("/ielts-trf/photo.svt?random=" + oldSrc, base64);
        html = html.replace("<img src=\"/ielts-trf/images/common/blank.gif\"", "<img class=\"hidden\" src=\"/ielts-trf/images/common/blank.gif\"");
        //拼接需要的td标签内容
        /*for(int i = 4; i <= 12; i++){
            String xpath="//*[@id='resultstoptable']/table/tbody/tr[2]/td/table/tbody/tr["+i+"]/td[2]//text()";
            String xpath1="//*[@id='resultstoptable']/table/tbody/tr[2]/td/table/tbody/tr["+i+"]/td[3]//text()";
            JXDocument jxDocument = new JXDocument(html);
            List<Object> rs = null;
            List<Object> rs1 = null;
            try{
                //rs解析为key
                rs = jxDocument.sel(xpath);
                //rs1解析为key对应的value
                rs1 = jxDocument.sel(xpath1);
            }catch(Exception e){
                System.out.println("html解析异常");
            }
            if(null != rs && null != rs1 && !StringUtils.isEmpty(rs.get(0).toString())){
                resultMap.put(rs.get(0).toString(),rs1.get(0).toString().replace("Band Scores Explained",""));
            }
        }*/
        return html;
    }

    @Override
    public JSONObject faceMatch(String imagePath1, String imagePath2) throws Exception {

        if(GeneralTool.isEmpty(imagePath1) && GeneralTool.isEmpty(imagePath2)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("PLEASE_UPLOAD_CORRESPONDING_NUMBER_OF_IMAGES_FOR_COMPARISON"));
        }
        if(GeneralTool.isEmpty(faceMatchUrl))
        {
            throw new GetServiceException(LocaleMessageUtils.getMessage("FACE_MATCH_URL_NOT_CONFIGURED"));
        }
        File file1 = urlToFile(imagePath1);
        File file2 = urlToFile(imagePath2);
        String url = faceMatchUrl;
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        headers.setContentType(MediaType.parseMediaType("multipart/form-data;charset=UTF-8"));
        MultiValueMap<String, Object> form = new LinkedMultiValueMap<>();
        form.add("image_file1", new FileSystemResource(file1));
        form.add("image_file2",new FileSystemResource(file2));
        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(form, headers);
        JSONObject s = restTemplate.postForObject(url, httpEntity, JSONObject.class);
        return s;
    }

    public static File urlToFile(String url) throws IOException{

        // 创建一个URL对象
        URL imageUrl = new URL(url);

        // 创建一个临时文件对象
        File tempFile = File.createTempFile("img", ".jpg");

        // 使用Java的URLConnection对象获取图片输入流
        InputStream is = imageUrl.openStream();

        // 使用Java的FileOutputStream对象将图片输入流写入临时文件
        FileOutputStream fos = new FileOutputStream(tempFile);
        byte[] buffer = new byte[1024];
        int bytesRead;
        while ((bytesRead = is.read(buffer)) != -1) {
            fos.write(buffer, 0, bytesRead);
        }
        fos.close();
        is.close();
        return tempFile;
    }
}
