package com.get.salecenter.service.impl;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.KpiPlanGroupItemMapper;
import com.get.salecenter.vo.KpiPlanGroupItemVo;
import com.get.salecenter.entity.KpiPlan;
import com.get.salecenter.entity.KpiPlanGroup;
import com.get.salecenter.entity.KpiPlanGroupItem;
import com.get.salecenter.entity.KpiPlanTarget;
import com.get.salecenter.service.KpiPlanGroupItemService;
import com.get.salecenter.service.KpiPlanGroupService;
import com.get.salecenter.service.KpiPlanService;
import com.get.salecenter.service.KpiPlanTargetService;
import com.get.salecenter.dto.KpiPlanGroupItemDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Service
public class KpiPlanGroupItemServiceImpl extends ServiceImpl<KpiPlanGroupItemMapper, KpiPlanGroupItem> implements KpiPlanGroupItemService {

    @Resource
    private KpiPlanGroupItemMapper kpiPlanGroupItemMapper;

    @Resource
    private KpiPlanTargetService kpiPlanTargetService;

    @Resource
    private UtilService utilService;

    @Resource
    private KpiPlanService kpiPlanService;

    @Resource
    private KpiPlanGroupService kpiPlanGroupService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addKpiPlanGroupItem(KpiPlanGroupItemDto kpiPlanGroupItemDto) {
        if (GeneralTool.isEmpty(kpiPlanGroupItemDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        KpiPlanGroupItem kpiPlanGroupItem = BeanCopyUtils.objClone(kpiPlanGroupItemDto, KpiPlanGroupItem::new);
        //获取最大排序
        KpiPlanGroupItem maxView = kpiPlanGroupItemMapper.selectOne(Wrappers.<KpiPlanGroupItem>lambdaQuery()
                .select(KpiPlanGroupItem::getViewOrder)
                .eq(KpiPlanGroupItem::getFkKpiPlanId, kpiPlanGroupItemDto.getFkKpiPlanId())
                .eq(KpiPlanGroupItem::getFkKpiPlanGroupId, kpiPlanGroupItemDto.getFkKpiPlanGroupId())
                .orderByDesc(KpiPlanGroupItem::getViewOrder)
                .last("limit 1"));
        Integer viewOrder = GeneralTool.isEmpty(maxView) ? 0 : maxView.getViewOrder() + 1;
        if(GeneralTool.isNotEmpty(kpiPlanGroupItem.getFkAreaCountryIds()) && GeneralTool.isEmpty(kpiPlanGroupItem.getCountryIncludeType())){
            kpiPlanGroupItem.setCountryIncludeType(1);
        }
        kpiPlanGroupItem.setViewOrder(viewOrder);
        utilService.setCreateInfo(kpiPlanGroupItem);
        kpiPlanGroupItemMapper.insert(kpiPlanGroupItem);
        return kpiPlanGroupItem.getId();
    }

    @Override
    public KpiPlanGroupItemVo findKpiPlanGroupItemById(Long id){
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        KpiPlanGroupItem kpiPlanGroupItem = kpiPlanGroupItemMapper.selectById(id);
        if(GeneralTool.isEmpty(kpiPlanGroupItem)){
            return null;
        }
        KpiPlanGroupItemVo itemDto = BeanCopyUtils.objClone(kpiPlanGroupItem, KpiPlanGroupItemVo::new);
        KpiPlan kpiPlan = kpiPlanService.getById(kpiPlanGroupItem.getFkKpiPlanId());
        if(GeneralTool.isNotEmpty(kpiPlan)){
            itemDto.setFkCompanyIds(kpiPlan.getFkCompanyIds());
        }
        KpiPlanGroup kpiPlanGroup = kpiPlanGroupService.getById(kpiPlanGroupItem.getFkKpiPlanGroupId());
        if(GeneralTool.isNotEmpty(kpiPlanGroup)){
            itemDto.setFkKpiPlanGroupName(kpiPlanGroup.getGroupName());
        }
        return itemDto;

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        KpiPlanGroupItem kpiPlanGroupItem = kpiPlanGroupItemMapper.selectById(id);
        if (GeneralTool.isEmpty(kpiPlanGroupItem)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        int delete = kpiPlanGroupItemMapper.deleteById(id);
        if(delete < 0){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        //删除KPI目标设置
         kpiPlanTargetService.remove(Wrappers.<KpiPlanTarget>lambdaQuery().eq(KpiPlanTarget::getFkKpiPlanGroupItemId, id));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateKpiPlanGroupItem(KpiPlanGroupItemDto kpiPlanGroupItemDto) {
        if (GeneralTool.isEmpty(kpiPlanGroupItemDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        KpiPlanGroupItem kpiPlanGroupItem = kpiPlanGroupItemMapper.selectById(kpiPlanGroupItemDto.getId());
        if (GeneralTool.isEmpty(kpiPlanGroupItem)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_obj_null"));
        }
        KpiPlanGroupItem updateKpiPlanGroupItem = BeanCopyUtils.objClone(kpiPlanGroupItemDto, KpiPlanGroupItem::new);
        // 组别发生改变才修改viewOrder
        if (!kpiPlanGroupItem.getFkKpiPlanGroupId().equals(kpiPlanGroupItemDto.getFkKpiPlanGroupId())) {
            KpiPlanGroup targetGroup = kpiPlanGroupService.getOne(Wrappers.<KpiPlanGroup>lambdaQuery()
                    .eq(KpiPlanGroup::getFkKpiPlanId, kpiPlanGroupItemDto.getFkKpiPlanId())
                    .eq(KpiPlanGroup::getId, kpiPlanGroupItemDto.getFkKpiPlanGroupId())
                    .last("limit 1"));
            if (GeneralTool.isEmpty(targetGroup)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail_target_group_not_exist"));
            }
            // 获取最大排序
            KpiPlanGroupItem maxView = kpiPlanGroupItemMapper.selectOne(Wrappers.<KpiPlanGroupItem>lambdaQuery()
                    .select(KpiPlanGroupItem::getViewOrder)
                    .eq(KpiPlanGroupItem::getFkKpiPlanId, kpiPlanGroupItemDto.getFkKpiPlanId())
                    .eq(KpiPlanGroupItem::getFkKpiPlanGroupId, kpiPlanGroupItemDto.getFkKpiPlanGroupId())
                    .orderByDesc(KpiPlanGroupItem::getViewOrder)
                    .last("limit 1"));
            Integer viewOrder = GeneralTool.isEmpty(maxView) ? 0 : maxView.getViewOrder() + 1;
            updateKpiPlanGroupItem.setViewOrder(viewOrder);
        }
        utilService.setUpdateInfo(updateKpiPlanGroupItem);
        int i = kpiPlanGroupItemMapper.updateByIdWithNull(updateKpiPlanGroupItem);
        if(i < 0){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void movingOrder(Long fkKpiPlanGroupId, Integer start, Integer end) {
        if (GeneralTool.isEmpty(fkKpiPlanGroupId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        LambdaQueryWrapper<KpiPlanGroupItem> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(KpiPlanGroupItem::getFkKpiPlanGroupId,fkKpiPlanGroupId);
        if (end > start){
            lambdaQueryWrapper.between(KpiPlanGroupItem::getViewOrder,start,end).orderByDesc(KpiPlanGroupItem::getViewOrder);
        }else {
            lambdaQueryWrapper.between(KpiPlanGroupItem::getViewOrder,end,start).orderByDesc(KpiPlanGroupItem::getViewOrder);

        }
        List<KpiPlanGroupItem> kpiPlanGroupItems = list(lambdaQueryWrapper);

        //从下上移 列表倒序排序
        List<KpiPlanGroupItem> updateList = Lists.newArrayList();
        if (end > start){
            int finalEnd = end;
            List<KpiPlanGroupItem> sortedList = Lists.newArrayList();
            KpiPlanGroupItem policy = kpiPlanGroupItems.get(kpiPlanGroupItems.size() - 1);
            sortedList.add(policy);
            kpiPlanGroupItems.remove(kpiPlanGroupItems.size() - 1);
            sortedList.addAll(kpiPlanGroupItems);
            for (KpiPlanGroupItem item : sortedList) {
                item.setViewOrder(finalEnd);
                finalEnd--;
            }
            updateList.addAll(sortedList);
        }else {
            int finalStart = start;
            List<KpiPlanGroupItem> sortedList = Lists.newArrayList();
            KpiPlanGroupItem policy = kpiPlanGroupItems.get(0);
            kpiPlanGroupItems.remove(0);
            sortedList.addAll(kpiPlanGroupItems);
            sortedList.add(policy);
            for (KpiPlanGroupItem item : sortedList) {
                item.setViewOrder(finalStart);
                finalStart--;
            }
            updateList.addAll(sortedList);
        }

        if (GeneralTool.isNotEmpty(updateList)){
            updateList.forEach(item -> utilService.setUpdateInfo(item));
            boolean batch = updateBatchById(updateList);
            if (!batch){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
    }
}
