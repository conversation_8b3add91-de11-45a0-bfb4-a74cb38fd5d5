package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/11/17 16:22
 * @verison: 1.0
 * @description:
 */
@Data
public class AppAgentRenewalUpdateDto extends BaseVoEntity {


    /**
     * 合同开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "合同开始时间")
    @NotNull(message = "合作伙伴昵称", groups = {Update.class})
    private Date contractStartTime;

    /**
     * 合同结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "合同结束时间")
    @NotNull(message = "合作伙伴昵称", groups = {Update.class})
    private Date contractEndTime;


    @ApiModelProperty("代理联系人")
    @NotEmpty(message = "代理联系人",groups = {Update.class})
    private List<AppAgentContactPersonAddDto> appAgentContactPersonAddVos;

    @ApiModelProperty(value = "合作伙伴昵称")
    @JsonProperty("natureNote")
    @NotNull(message = "合作伙伴昵称", groups = {Update.class})
    private String natureNote;

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:text &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:变更声明内容 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty(value = "变更声明内容")
    @JsonProperty("changeStatement")
    private String changeStatement;

}
