package com.get.aisplatformcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.aisplatformcenterap.dto.MPlatformParamsDto;
import com.get.aisplatformcenterap.entity.MPlatformEntity;
import com.get.aisplatformcenter.service.MPlatformService;
import com.get.aisplatformcenter.mapper.MPlatformMapper;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.ResultCode;
import com.get.core.tool.utils.GeneralTool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_platform】的数据库操作Service实现
* @createDate 2024-12-19 11:15:30
*/
@Service
public class MPlatformServiceImpl extends ServiceImpl<MPlatformMapper, MPlatformEntity>
    implements MPlatformService {
    @Autowired
    private MPlatformMapper mplatformMapper;

    @Override
    public List<MPlatformEntity> searchList(MPlatformParamsDto dto) {

        List<MPlatformEntity>  result=mplatformMapper.selectList(new LambdaQueryWrapper<MPlatformEntity>().eq(MPlatformEntity::getStatus,1).orderBy(true,false,MPlatformEntity::getGmtCreate));
        return result;
    }

    @Override
    public Long delete(Long id) {
        MPlatformEntity dbnum=mplatformMapper.selectById(id);
        if(GeneralTool.isNotEmpty(dbnum) && dbnum.getStatus()==1){
            throw new GetServiceException(ResultCode.BUSINESS_EXCEPTION,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","启用不可以删除!"));
        }
        mplatformMapper.deleteById(id);
        return id;
    }


}




