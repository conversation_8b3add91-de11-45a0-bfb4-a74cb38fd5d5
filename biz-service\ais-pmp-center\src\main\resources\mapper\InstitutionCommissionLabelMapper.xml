<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.pmpcenter.mapper.InstitutionCommissionLabelMapper">

    <select id="selectBusinessLabel" resultType="com.get.pmpcenter.vo.institution.NameLabel">
        select *
        from ais_sale_center.s_name_label
        where fk_table_name = 'm_institution'
          and fk_table_id = #{institutionId}
        order by view_order desc
    </select>


    <select id="selectCustomizeLabel"
            resultType="com.get.pmpcenter.vo.institution.CustomizeLabelVo">
        select l.id as labelId,
        l.label_name as labelName,
        l.fk_label_type_id as labelTypeId,
        l.label_key as labelKey,
        l.color as color,
        l.remark as remark,
        l.icon_name as iconName,
        l.view_order as viewOrder,
        t.type_name as labelTypeName,
        t.type_key as labelTypeKey
        from ais_platform_center.u_label l
        left join ais_platform_center.u_label_type t on l.fk_label_type_id = t.id
        where l.id in
        <foreach collection="labelIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        order by l.view_order desc
    </select>

    <select id="selectCustomizeLabelType" resultType="com.get.pmpcenter.vo.institution.CustomizeLabelVo">
        select id        as labelTypeId,
               type_name as labelTypeName,
               type_key  as labelTypeKey
        from ais_platform_center.u_label_type
        order by view_order desc
    </select>

    <select id="selectCustomizeLabelByTypeAndKeyword"
            resultType="com.get.pmpcenter.vo.institution.CustomizeLabelVo">
        select l.id as labelId,
        l.label_name as labelName,
        l.fk_label_type_id as labelTypeId,
        l.label_key as labelKey,
        l.color as color,
        l.remark as remark,
        l.icon_name as iconName,
        l.view_order as viewOrder,
        t.type_name as labelTypeName,
        t.type_key as labelTypeKey
        from ais_platform_center.u_label l
        left join ais_platform_center.u_label_type t on l.fk_label_type_id = t.id
        <where>
            l.is_active = 1
            <if test="labelTypeId != null and labelTypeId > 0">
                and l.fk_label_type_id =
                #{labelTypeId}
            </if>
            <if test="keyword != null and keyword != ''">
                and
                (l.label_name like concat('%',#{keyword},'%')
                    or l.label_key like concat('%',#{keyword},'%')
                    or l.remark like concat('%',#{keyword},'%'))
            </if>
        </where>
        order by l.view_order desc
    </select>
</mapper>