package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.AnnualConferenceRegistrationBooth;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface AnnualConferenceRegistrationBoothMapper extends BaseMapper<AnnualConferenceRegistrationBooth> {

    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(AnnualConferenceRegistrationBooth record);

}