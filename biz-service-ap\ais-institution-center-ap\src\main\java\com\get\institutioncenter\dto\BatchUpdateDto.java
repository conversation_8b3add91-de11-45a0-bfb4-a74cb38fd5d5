package com.get.institutioncenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/2/24 17:00
 * @verison: 1.0
 * @description:进行批量修改的vo
 */
@Data
public class BatchUpdateDto {
    /**
     * 要修改对象的id
     */
    @ApiModelProperty(value = "要修改对象的id", required = true)
    private Long id;

    /**
     * 批量修改配置的id
     */
    @ApiModelProperty(value = "批量修改配置的id", required = true)
    private Long batchModifyConfigId;

    /**
     * 修改后的值
     */
    @ApiModelProperty(value = "修改后的值(数组格式保存)", required = true)
    private List<String> resultList;

    /**
     * 修改后的值
     */
    @ApiModelProperty(value = "修改后的值(string格式保存)", required = true)
    private String result;
}
