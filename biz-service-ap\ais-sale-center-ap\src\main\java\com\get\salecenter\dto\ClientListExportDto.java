package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ClientListExportDto {

    @ApiModelProperty(value = "学生资源编号")
    private String num;

    @ApiModelProperty(value = "所属公司")
    private String fkCompanyName;

    @ApiModelProperty(value = "部门")
    private String departmentName;

    @ApiModelProperty(value = "推荐来源类型")
    private String fkTableName;

    @ApiModelProperty(value = "推荐来源编号")
    private String studentSource;

    @ApiModelProperty(value = "推荐来源代理")
    private String sourceAgentName;

    @ApiModelProperty(value = "推荐来源BD")
    private String sourceBdName;

    @ApiModelProperty(value = "学生姓名")
    private String name;

    @ApiModelProperty(value = "出生日期")
    private String birthday;

    @ApiModelProperty(value = "资源星级")
    private Integer starLevel;

    @ApiModelProperty(value = "预计签约时间")
    private String expectSigningTime;

    @ApiModelProperty(value = "方案步骤状态")
    private String fkClientOfferStepName;

    @ApiModelProperty(value = "在读/毕业学校")
    private String institutionName;

    @ApiModelProperty(value = "在读/毕业专业")
    private String majorName;

    @ApiModelProperty(value = "入读时间")
    private String startTimeEducation;

    @ApiModelProperty(value = "毕业时间")
    private String endTimeEducation;

    @ApiModelProperty(value = "是否入境")
    private String isEnterCountryName;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "微信")
    private String wechat;

    @ApiModelProperty(value = "电子邮箱")
    private String email;

    @ApiModelProperty(value = "来源代理")
    private String fkAgentName;

    @ApiModelProperty(value = "方案跟进BD")
    private String bdName;

    @ApiModelProperty(value = "方案项目成员")
    private String projectRoleName;

    @ApiModelProperty(value = "方案申请国家")
    private String offerCountryName;

    //    @ApiModelProperty(value = "咨询情况（申请国家-升学方案）")
    @ApiModelProperty(value = "方案内容")
    private String remark;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "最近回访时间")
    private Date lastVisitTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "预约回访时间")
    private Date followUpTime;

    @ApiModelProperty(value = "学生负责人")
    private String fkStudentStaffNames;

    @ApiModelProperty(value = "创建人")
    private String gmtCreateUser;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;


}
