package com.get.salecenter.dao.sale;

import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.entity.CancelOfferReason;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface CancelOfferReasonMapper extends GetMapper<CancelOfferReason> {

    /**
     * @return java.lang.Integer
     * @Description:查找最大排序序号
     * @Param []
     * <AUTHOR>
     */
    Integer getMaxViewOrder();

}