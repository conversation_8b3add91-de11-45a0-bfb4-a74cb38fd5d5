package com.get.helpcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.ChineseAndEnglishEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.helpcenter.dao.HelpMapper;
import com.get.helpcenter.dao.HelpTypeMapper;
import com.get.helpcenter.dao.TranslationMapper;
import com.get.helpcenter.vo.HelpVo;
import com.get.helpcenter.vo.HelpInfoVo;
import com.get.helpcenter.vo.TranslationVo;
import com.get.helpcenter.entity.Help;
import com.get.helpcenter.entity.HelpType;
import com.get.helpcenter.entity.ParentHelp;
import com.get.helpcenter.service.IHelpService;
import com.get.helpcenter.service.ITranslationMappingService;
import com.get.helpcenter.dto.ExchangeHelpDto;
import com.get.helpcenter.dto.HelpDto;
import com.get.helpcenter.dto.TranslationDto;
import com.get.helpcenter.dto.query.HelpQueryDto;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.StringJoiner;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2021/5/11 15:00
 * @verison: 1.0
 * @description:
 */
@Service
public class HelpServiceImpl  extends ServiceImpl<HelpMapper,Help> implements IHelpService {
    @Resource
    private HelpMapper helpMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private HelpTypeMapper helpTypeMapper;
    @Resource
    private TranslationMapper translationMapper;
    @Resource
    private ITranslationMappingService translationMappingService;

    /**
     * 树形结构数据
     *
     * @param helpVo
     * @return
     */
    @Override
    public List<HelpVo> getHelpTree(HelpQueryDto helpVo) {
//        Example example = new Example(Help.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(helpVo)) {
//            //查询条件
//            if (GeneralTool.isNotEmpty(helpVo.getTitle())) {
//                criteria.andLike("title", "%" + helpVo.getTitle() + "%");
//            }
//            //帮助类型
//            if (GeneralTool.isNotEmpty(helpVo.getFkHelpTypeId())) {
//                criteria.andEqualTo("fkHelpTypeId", helpVo.getFkHelpTypeId());
//            }
//        }
//        example.orderBy("viewOrder").desc();
//        List<Help> helps = helpMapper.selectByExample(example);

        LambdaQueryWrapper<Help> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(helpVo)) {
            //查询条件
            if (GeneralTool.isNotEmpty(helpVo.getTitle())) {
                lambdaQueryWrapper.like(Help::getTitle, helpVo.getTitle());
            }
            //帮助类型
            if (GeneralTool.isNotEmpty(helpVo.getFkHelpTypeId())) {
                lambdaQueryWrapper.eq(Help::getFkHelpTypeId, helpVo.getFkHelpTypeId());
            }
        }
        lambdaQueryWrapper.orderByDesc(Help::getViewOrder);
        List<Help> helps = helpMapper.selectList(lambdaQueryWrapper);

        List<HelpVo> collect = helps.stream().map(help -> BeanCopyUtils.objClone(help, HelpVo::new)).collect(Collectors.toList());
        List<HelpVo> resultList = new ArrayList<>();

        Long parentId = null;
        for (HelpVo helpDto1 : collect) {
            //设置公开对象名称
            setPublicLevelName(helpDto1);
            //父级Id
            parentId = helpDto1.getFkParentHelpId();
            //设置前置问题title
            if (GeneralTool.isNotEmpty(parentId)) {
                Help parentHelp = helpMapper.getHelpById(parentId);
                if (GeneralTool.isNotEmpty(parentHelp)) {
                    if (GeneralTool.isNotEmpty(parentHelp.getTitle())) {
                        helpDto1.setFkParentHelpTitle(parentHelp.getTitle());
                    }
                }
            }
            //设置关联问题
            List<Long> relationIds = helpMapper.getRelationHelpIds(helpDto1.getId());
            //关联问题ID
            helpDto1.setRelationHelpIds(relationIds);
            //关联问题title
            List<String> relationHelpTitleList = new ArrayList<>();
            if (GeneralTool.isNotEmpty(relationIds)) {
                for (Long relationId : relationIds) {
                    Help relationHelp = helpMapper.getHelpById(relationId);
                    if (GeneralTool.isNotEmpty(relationHelp)) {
                        if (GeneralTool.isNotEmpty(relationHelp.getTitle())) {
                            relationHelpTitleList.add(relationHelp.getTitle());
                        } else {
                            relationHelpTitleList.add("");
                        }
                    }
                }
                helpDto1.setRelationHelpTitleList(relationHelpTitleList);
            }
            //获取根节点:搜索指定title问题 || 父级ID为NULL || 父级ID为0
            if (GeneralTool.isNotEmpty(helpVo.getTitle()) || parentId == null || parentId.equals(0L)) {
                resultList.add(helpDto1);
            }

        }
        if (GeneralTool.isNotEmpty(helpVo.getTitle())) {
            return resultList;
        }
        // 获取每个顶层元素的子数据集合  resultList 就是当前的最高级别，通过递归获取所有所有方法
        for (HelpVo helpDto1 : resultList) {
            int Node = 1;
            helpDto1.setChild(getChild(helpDto1.getId(), collect, Node));
            helpDto1.setNode(Node);
        }
        return resultList;
    }

    /**
     * 获取子级
     *
     * @param id
     * @param helpVoList
     * @return
     */
    private List<HelpVo> getChild(Long id, List<HelpVo> helpVoList, int Node) {
        //每次递归，节点自增
        Node++;
        //helpVoList 最顶层帮助类型list
        List<HelpVo> childList = new ArrayList<>();
        //子级的直接子对象
        for (HelpVo helpVo : helpVoList) {
            Long parentId = helpVo.getFkParentHelpId();
            if (GeneralTool.isNotEmpty(parentId)) {
                if (parentId.equals(id)) {
                    helpVo.setNode(Node);
                    childList.add(helpVo);
                }
            }
        }

        //如果没有子级了，退出递归
        if (childList.size() == 0) {
            return null;
        }

        // 子集的间接子对象(递归 2级找第三级以下开始递归)
        for (HelpVo helpVo : childList) {
            //递归调用
            helpVo.setChild(getChild(helpVo.getId(), helpVoList, Node));
        }

        return childList;
    }

    /**
     * 交换帮助前置问题
     *
     * @param exchangeHelpDto
     */
    @Override
    public void exchangeHelp(ExchangeHelpDto exchangeHelpDto) {
        Long fkParentHelpId = exchangeHelpDto.getFkParentHelpId();
        Help help = helpMapper.selectById(fkParentHelpId);
        if (help!=null || fkParentHelpId == 0) {
            List<Long> helpIds = exchangeHelpDto.getHelpIds();
            List<Help> helps = helpMapper.selectBatchIds(helpIds);
            Map<Long, Help> helpMap = helps.stream().collect(Collectors.toMap(Help::getId, Function.identity()));
            int cur = helpMap.size();
            List<Help> upList = new ArrayList<>(helpMap.size());
            for (Long id : helpIds) {
                Help h = helpMap.get(id);
                if (h!=null) {
                    h.setFkParentHelpId(fkParentHelpId);
                    h.setViewOrder(cur);
                    upList.add(h);
                    cur--;
                }
            }
            if (!upList.isEmpty()) {
                updateBatchById(upList);
            }
        }
//            Help help2 = helpMapper.selectById(fkHelpId);
//            Integer order = help.getViewOrder();
//            help2.setFkParentHelpId(fkParentHelpId);
//            help2.setViewOrder(order);
//            helpMapper.updateById(help2);
    }

    /**
     * 新增帮助信息
     *
     * @param helpDto
     * @return
     * @
     */
    @Override
    public Long addHelp(HelpDto helpDto) {
        if (GeneralTool.isEmpty(helpDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //校验keycode
        if (GeneralTool.isNotEmpty(helpDto.getKeyCode())) {
            Long aLong = validateUpdate(helpDto);
            if (GeneralTool.isNotEmpty(aLong)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("keyCode_exists"));
            }
        }
        Help help = BeanCopyUtils.objClone(helpDto, Help::new);
        //获取最大vieworder
        Integer maxViewOrder = helpMapper.getMaxViewOrder();
        maxViewOrder = GeneralTool.isEmpty(maxViewOrder) ? 0 : maxViewOrder;
        help.setViewOrder(maxViewOrder);
        utilService.updateUserInfoToEntity(help);
        int i = helpMapper.insertSelective(help);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        //插入关联问题
        if (GeneralTool.isNotEmpty(helpDto.getRelationHelpIds())) {
            for (Long relationHelpId : helpDto.getRelationHelpIds()) {
                //判断关联记录是否已存在
                Long id = helpMapper.getRelationHelp(relationHelpId, help.getId());
                //存在记录直接跳过
                if (GeneralTool.isNotEmpty(id)) {
                    continue;
                }
                ParentHelp parentHelp = new ParentHelp();
                parentHelp.setFkHelpId(relationHelpId);
                parentHelp.setParentId(help.getId());
                utilService.updateUserInfoToEntity(parentHelp);
                helpMapper.insertParents(parentHelp);
            }

        }
        return help.getId();
    }

    /**
     * 更新帮助信息
     *
     * @param helpDto
     * @return
     * @
     */
    @Override
    public HelpVo updateHelpVo(HelpDto helpDto) {
        if (GeneralTool.isEmpty(helpDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(helpDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (GeneralTool.isNotEmpty(helpDto.getKeyCode())) {
            Long aLong = validateUpdate(helpDto);
            if (!helpDto.getId().equals(aLong)) {
                //keyCode存在
                throw new GetServiceException(LocaleMessageUtils.getMessage("help_fail"));
            }
        }
        //查询判断修改的问题是否为根节点，如果为根节点，不能设置前置问题
        HelpVo helpVo = findHelpById(helpDto.getId());
        if (GeneralTool.isEmpty(helpVo.getFkParentHelpId()) || helpVo.getFkParentHelpId().equals(0L)) {
            if (GeneralTool.isNotEmpty(helpDto.getFkParentHelpId()) && !helpDto.getFkParentHelpId().equals(0L)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
        //更新
        Help help = BeanCopyUtils.objClone(helpDto, Help::new);
        utilService.updateUserInfoToEntity(help);
        helpMapper.updateById(help);
        //删除关联问题
        helpMapper.deleteParentIds(helpDto.getId());
        //插入关联问题
        if (GeneralTool.isNotEmpty(helpDto.getRelationHelpIds())) {
            for (Long relationHelpId : helpDto.getRelationHelpIds()) {
                ParentHelp parentHelp = new ParentHelp();
                parentHelp.setFkHelpId(relationHelpId);
                parentHelp.setParentId(helpDto.getId());
                utilService.updateUserInfoToEntity(parentHelp);
                helpMapper.insertParents(parentHelp);
            }
        }

        return findHelpById(helpDto.getId());
    }

    /**
     * 编辑校验是否存在
     *
     * @param helpDto
     * @return
     */
    private Long validateUpdate(HelpDto helpDto) {
//        Example example = new Example(Help.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("keyCode", helpDto.getKeyCode());
//        List<Help> helps = helpMapper.selectByExample(example);
        List<Help> helps = helpMapper.selectList(Wrappers.<Help>lambdaQuery().eq(Help::getKeyCode, helpDto.getKeyCode()));
        return GeneralTool.isNotEmpty(helps) ? helps.get(0).getId() : helpDto.getId();
    }

    /**
     * 上移下移
     *
     * @param helpDtoList
     * @
     */
    @Override
    public void movingOrder(List<HelpDto> helpDtoList) {
        if (GeneralTool.isEmpty(helpDtoList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Help ro = BeanCopyUtils.objClone(helpDtoList.get(0), Help::new);
        Integer oneorder = ro.getViewOrder();
        Help rt = BeanCopyUtils.objClone(helpDtoList.get(1), Help::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        helpMapper.updateById(ro);
        helpMapper.updateById(rt);
    }

    /**
     * 根据id获取帮助信息
     *
     * @param id
     * @return
     * @
     */
    @Override
    public HelpVo findHelpById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Help help = helpMapper.selectById(id);
        if (GeneralTool.isEmpty(help)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        HelpVo helpVo = BeanCopyUtils.objClone(help, HelpVo::new);
        Long pId = helpVo.getFkParentHelpId();
        //前置问题不为空,设置前置问题title
        if (GeneralTool.isNotEmpty(helpVo.getFkParentHelpId())) {
            Help parentHelp = helpMapper.getHelpById(helpVo.getFkParentHelpId());
            if (GeneralTool.isNotEmpty(parentHelp)) {
                if (GeneralTool.isNotEmpty(parentHelp.getTitle())) {
                    helpVo.setFkParentHelpTitle(parentHelp.getTitle());
                }
            }

        }
        //关联问题
        List<Long> relationHelpIds = helpMapper.getRelationHelpIds(helpVo.getId());
        List<String> relationHelpTitleList = new ArrayList<>();
        helpVo.setRelationHelpIds(relationHelpIds);
        if (GeneralTool.isNotEmpty(relationHelpIds)) {
            for (Long relationHelpId : relationHelpIds) {
                Help fkParentHelp = helpMapper.selectById(relationHelpId);
                if (GeneralTool.isNotEmpty(fkParentHelp)) {
                    //title不为空
                    if (GeneralTool.isNotEmpty(fkParentHelp.getTitle())) {
                        relationHelpTitleList.add(fkParentHelp.getTitle());
                    }
                }

            }
            helpVo.setRelationHelpTitleList(relationHelpTitleList);
        }
        //设置公开对象名称
        setPublicLevelName(helpVo);
        //设置显示类型名称
        helpVo.setShowTypeName(ProjectExtraEnum.getValueByKey(helpVo.getShowType(), ProjectExtraEnum.SHOW_TYPES));
        //设置帮助类型名称
        if (GeneralTool.isNotEmpty(helpVo.getFkHelpTypeId()) && helpVo.getFkHelpTypeId() != 0L) {
            HelpType helpType = helpTypeMapper.getHelpTypeTitleById(helpVo.getFkHelpTypeId());
            helpVo.setHelpTypeName(helpType.getTypeName());
        }
        //设置表名
        helpVo.setFkTableName(TableEnum.HELP.key);
        String languageCode = SecureUtil.getLocale();
        TranslationDto translationDto = new TranslationDto();
        if (ChineseAndEnglishEnum.ZH_CN.key.equals(languageCode)) {
            languageCode = ChineseAndEnglishEnum.getValue(ChineseAndEnglishEnum.ZH_CN.key);
        }else if (ChineseAndEnglishEnum.ZH_EN.key.equals(languageCode)){
            languageCode = ChineseAndEnglishEnum.getValue(ChineseAndEnglishEnum.ZH_EN.key);
            translationDto.setFkTableId(id);
            translationDto.setLanguageCode(languageCode);
            List<TranslationVo> translationInfo = translationMapper.getTranslationInfo(translationDto);
            translationInfo.forEach(f->{
                if (f.getFkTranslationMappingId() == 1) {
                    helpVo.setTitle(f.getTranslation());
                }else if (f.getFkTranslationMappingId() == 2){
                    helpVo.setDescription(f.getTranslation());
                }
            });
        }
        return helpVo;
    }

    /**
     * 设置公开对象名称
     *
     * @param helpVo
     */
    private void setPublicLevelName(HelpVo helpVo) {
        StringJoiner stringJoiner = new StringJoiner(",");
        if (GeneralTool.isNotEmpty(helpVo.getPublicLevel())) {
            //把公开对象的序号用逗号割开
            List<String> result = Arrays.asList(helpVo.getPublicLevel().split(","));
            for (String name : result) {
                stringJoiner.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(name), ProjectExtraEnum.PUBLIC_OBJECTS));
            }
            helpVo.setPublicLevelName(stringJoiner.toString());
        }
    }

    /**
     * 删除帮助信息
     *
     * @param id
     * @
     */
    @Override
    public void deleteHelp(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (helpMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }

        //判断有关联子帮助问题
        Long chilHelpIds = helpMapper.getAllChilHelpId(id);
        if (GeneralTool.isNotEmpty(chilHelpIds)) {
            //有关联子帮助问题，不能删除
            throw new GetServiceException(LocaleMessageUtils.getMessage("help_was_used_to_be_parent_help"));
        }
        //判断是否为其他问题的前置问题
        List<Long> helpIds = helpMapper.getHelpByParentId(id);
        if (helpIds.size() > 0) {
            //是其他问题的前置问题，不能删除
            throw new GetServiceException(LocaleMessageUtils.getMessage("help_was_used_to_be_parent_help"));
        }
        //无子帮助问题
        helpMapper.deleteById(id);

        //删除翻译内容
        translationMappingService.deleteTranslations(TableEnum.HELP.key, id);
    }

    /**
     * 帮助信息下拉框
     *
     * @return
     * @
     */
    @Override
    public List<HelpVo> getHelpSelect() {
        return helpMapper.getHelpSelect();
    }


    /**
     * 新增校验是否存在，keyCode唯一
     *
     * @param keyCode
     * @return boolean
     */
    private boolean validateKeyCode(String keyCode) {
//        Example example = new Example(Help.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("keyCode", keyCode);
//        List<Help> helps = helpMapper.selectByExample(example);

        List<Help> helps = helpMapper.selectList(Wrappers.<Help>lambdaQuery().eq(Help::getKeyCode, keyCode));
        return GeneralTool.isEmpty(helps);
    }

    /**
     * 显示类型下拉框
     *
     * @return
     * @
     */
    @Override
    public List<Map<String, Object>> getShowTypeObjectsSelect() {
        List<Map<String, Object>> maps = ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.SHOW_TYPES);
        for (Map<String, Object> map : maps) {
            map.get("value");
        }
        return ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.SHOW_TYPES);
    }

    /**
     * 根据keyCode获取帮助信息
     *
     * @param keyCode
     * @return
     * @
     */
    @Override
    public HelpVo getHelpByKeyCode(String keyCode) {
//        Example example = new Example(Help.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("keyCode", keyCode);
//        List<Help> helps = helpMapper.selectByExample(example);

        List<Help> helps = helpMapper.selectList(Wrappers.<Help>lambdaQuery().eq(Help::getKeyCode, keyCode));

        if (GeneralTool.isEmpty(helps)) {
            return null;
        }
        Help help = helps.get(0);
        HelpVo helpVo = BeanCopyUtils.objClone(help, HelpVo::new);
        //前置问题不为空,设置前置问题title
        if (GeneralTool.isNotEmpty(helpVo.getFkParentHelpId())) {
            Help parentHelp = helpMapper.getHelpById(helpVo.getFkParentHelpId());
            if (GeneralTool.isNotEmpty(parentHelp)) {
                if (GeneralTool.isNotEmpty(parentHelp.getTitle())) {
                    helpVo.setFkParentHelpTitle(parentHelp.getTitle());
                }
            }

        }
        //关联问题
        List<Long> relationHelpIds = helpMapper.getRelationHelpIds(helpVo.getId());
        List<String> relationHelpTitleList = new ArrayList<>();
        helpVo.setRelationHelpIds(relationHelpIds);
        if (GeneralTool.isNotEmpty(relationHelpIds)) {
            for (Long relationHelpId : relationHelpIds) {
                Help fkParentHelp = helpMapper.selectById(relationHelpId);
                if (GeneralTool.isNotEmpty(fkParentHelp)) {
                    //title不为空
                    if (GeneralTool.isNotEmpty(fkParentHelp.getTitle())) {
                        relationHelpTitleList.add(fkParentHelp.getTitle());
                    }
                }

            }
            helpVo.setRelationHelpTitleList(relationHelpTitleList);
        }
        List<Help> childHelps = helpMapper.selectList(Wrappers.<Help>query().lambda().eq(Help::getFkParentHelpId, helpVo.getId()));
        helpVo.setChild(getChildHelpList(BeanCopyUtils.copyListProperties(childHelps, HelpVo::new)));

        return helpVo;
    }

    /**
     * feign调用 根据帮助id获取帮助详细
     *
     * @Date 10:46 2021/9/8
     * <AUTHOR>
     */
    @Override
    public List<HelpVo> getHelpDtoByHelpId(@RequestBody Set<Long> helpIds) {
//        Example example = new Example(Help.class);
//        example.createCriteria().andIn("id", helpIds).andIsNotNull("keyInfoName");
//        List<Help> helps = helpMapper.selectByExample(example);
        List<Help> helps = helpMapper.selectList(Wrappers.<Help>lambdaQuery().in(Help::getId, helpIds).ne(Help::getKeyInfoName, null));
        return helps.stream().map(help -> BeanCopyUtils.objClone(help, HelpVo::new)).collect(Collectors.toList());
    }

    @Override
    public List<HelpInfoVo> getHelpInfo() {
        return helpMapper.getHelpInfo();
    }

    private List<HelpVo> getChildHelpList(List<HelpVo> child) {
        if (GeneralTool.isEmpty(child)) {
            return null;
        }
        for (HelpVo helpVo : child) {
            List<Help> childHelps = helpMapper.selectList(Wrappers.<Help>query().lambda().eq(Help::getFkParentHelpId, helpVo.getId()));
            helpVo.setChild(getChildHelpList(BeanCopyUtils.copyListProperties(childHelps, HelpVo::new)));
        }
        return child;
    }

}
