package com.get.salecenter.service;

import com.get.common.result.SearchBean;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.salecenter.vo.BusinessProviderTypeVo;
import com.get.salecenter.dto.BusinessProviderTypeListDto;
import com.get.salecenter.dto.BusinessProviderTypeDto;

import java.util.List;

public interface BusinessProviderTypeService {
    List<BusinessProviderTypeVo> getBusinessProviderTypeDtos(BusinessProviderTypeListDto data, SearchBean<BusinessProviderTypeListDto> page);

    void addBusinessProviderType(ValidList<BusinessProviderTypeDto> businessProviderTypeDto);

    BusinessProviderTypeVo updateBusinessProviderType(BusinessProviderTypeDto businessProviderTypeDto);

    void deleteBusinessProviderType(Long id);

    void movingOrder(List<BusinessProviderTypeDto> businessProviderTypeDto);

    List<BaseSelectEntity> selectBusinessProviderType();
}
