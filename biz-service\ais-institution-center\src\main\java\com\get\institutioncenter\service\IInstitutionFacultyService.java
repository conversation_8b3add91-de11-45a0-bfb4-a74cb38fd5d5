package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.filecenter.dto.FileDto;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.vo.InstitutionFacultyVo;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.entity.InstitutionFaculty;
import com.get.institutioncenter.dto.InstitutionFacultyDto;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/8/3
 * @TIME: 14:40
 * @Description:学院业务类
 **/
public interface IInstitutionFacultyService extends BaseService<InstitutionFaculty> {
    /**
     * 列表数据
     *
     * @param institutionFacultyDto
     * @param page
     * @return
     */
    List<InstitutionFacultyVo> datas(InstitutionFacultyDto institutionFacultyDto, Page page);

    /**
     * 保存
     *
     * @param institutionFacultyDto
     * @return
     */
    Long addInstitutionFaculty(InstitutionFacultyDto institutionFacultyDto);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    InstitutionFacultyVo findInstitutionFacultyById(Long id);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 文件上传
     *
     * @param multipartFiles
     */
    FileDto upload(MultipartFile[] multipartFiles);


    /**
     * 修改
     *
     * @param institutionFacultyDto
     * @return
     */
    InstitutionFacultyVo updateInstitutionFaculty(InstitutionFacultyDto institutionFacultyDto);


    /**
     * String tableName = "m_institution_faculty";
     */

    List<MediaAndAttachedVo> addInstitutionFacultyMedia(List<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * 获取学校下面的学院下拉框
     *
     * @param id
     * @return
     */
    List<InstitutionFacultyVo> getByfkInstitutionId(Long id);

    /**
     * 获取学院附件类型
     *
     * @return
     */
    List<Map<String, Object>> findMediaAndAttachedType();

    /**
     * 查询学院附件
     *
     * @param data
     * @param page
     * @return
     * @
     */
    List<MediaAndAttachedVo> getInstitutionFacultyMedia(MediaAndAttachedDto data, Page page);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.InstitutionFacultyVo>
     * @Description :多个学校所有的学院下拉框数据
     * @Param [institutionIdList]
     * <AUTHOR>
     */
    List<InstitutionFacultyVo> getInstitutionFacultySelectByInstitutionIdList(List<Long> institutionIdList);


    /**
     * 根据课程ids获取学院名称
     *
     * @param courseIds
     * @return
     */
    Map<Long, String> getInstitutionFacultyNameByCourseIds(Set<Long> courseIds);

    /**
     * 根据课程id获取学院名称
     *
     * @param id
     * @return
     */
    String getInstitutionFacultyNameByid(Long id);

    /**
     * 根据课程ids获取学院名称
     *
     * @param ids
     * @return
     */
    Map<Long,String>getInstitutionFacultyNameByIds(Set<Long> ids);

    /**
     * 获取学院下拉
     * @param keyword
     * @param institutionId
     * @return
     */
    List<BaseSelectEntity> getInstitutionFacultyList(String keyword,Long institutionId);

}
