package com.get.partnercenter.dto.wechat;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * @Author:Oliver
 * @Date: 2025/7/21
 * @Version 1.0
 * @apiNote:微信小程序二维码请求参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MinProgramQrCodeDto {

    @ApiModelProperty("扫码进入的小程序页面路径，最大长度 128 个字符，不能为空")
    @NotBlank(message = "路径不能为空")
    private String path;

    @ApiModelProperty("二维码的宽度，单位 px。最小 280px，最大 1280px;默认是430")
    private Integer width = 430;
}
