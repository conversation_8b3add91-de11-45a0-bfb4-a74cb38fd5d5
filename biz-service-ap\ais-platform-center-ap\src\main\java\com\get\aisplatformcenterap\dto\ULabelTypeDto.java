package com.get.aisplatformcenterap.dto;

import com.get.core.mybatis.base.BaseEntity;
import com.get.core.mybatis.base.BaseVoEntity;
import lombok.Data;
import lombok.NonNull;

import javax.validation.constraints.NotNull;

@Data
public class ULabelTypeDto extends BaseVoEntity {

    /**
     * 类型名称
     */
    @NotNull(message = "类型名称不能为空",groups = {BaseVoEntity.Add.class})
    private  String typeName;

    /**
     * 类型Key
     */
    @NotNull(message = "类型key不能为空",groups = {BaseVoEntity.Add.class})
    private String typeKey;

    /**
     * 类型描述
     */
    private String remark;

    /**
     * 排序
     */
    private Integer viewOrder;

}
