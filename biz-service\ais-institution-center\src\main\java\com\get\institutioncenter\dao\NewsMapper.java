package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.institutioncenter.vo.NewsVo;
import com.get.institutioncenter.entity.News;
import com.get.institutioncenter.dto.query.NewsQueryDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface NewsMapper extends BaseMapper<News> {
    @Override
    int insert(News record);

    int insertSelective(News record);

    List<NewsVo> datas(IPage<News> pages,@Param("newsDto") NewsQueryDto newsVo);

    /**
     * 目标类型获取目标
     *
     * @return
     */
    List<BaseSelectEntity> getNewsTarget(@Param("tableName") String tableName,@Param("companyIds") List<Long> companyIds);
    /**
     * 获取目标名称
     *
     * @return
     */
    String getTargetName(@Param("tableName") String tableName, @Param("id") Long id);

    /**
     * 根据学校查找相关数据
     *
     * @param id
     * @return
     */
    Integer getCountByInstitutionId(Long id);

    /**
     * 根据课程查找相关数据
     *
     * @param id
     * @return
     */
    Integer getCountByCourseId(Long id);

    /**
     * @return boolean
     * @Description :校验国家能否删除
     * @Param [areaCountryId, tableName]
     * <AUTHOR>
     */
    boolean newsIsEmpty(@Param("areaCountryId") Long areaCountryId, @Param("tableName") String tableName);

    List<Long> getNewsIds(@Param("columnName") String columnName);

    /**
     * @return
     * @Description :获取news
     * @Param
     * <AUTHOR>
     */
    News getNewsById(@Param("id") Long newsId);

    /**
     * 所有新闻下拉框
     *
     * @Date 11:55 2021/7/28
     * <AUTHOR>
     */
    List<BaseSelectEntity> getAllNewsSelect();

    /**
     * 根据课程id验证是否存在
     *
     * @return
     */
    boolean isExistByCourseId(@Param("courseId") Long courseId);
}