<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.ReceiptFeeTypeMapper">
    <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select
     IFNULL(max(view_order)+1,0) view_order
    from
     u_receipt_fee_type

  </select>

  <select id="getNameById" parameterType="java.lang.Long" resultType="java.lang.String">
    select
      i.type_name
    from
      u_receipt_fee_type i
    where
      i.id = #{id}
  </select>

   <select id="selectByFkAccountingItemId" resultType="com.get.financecenter.entity.ReceiptFeeType">
        select * from u_receipt_fee_type where fk_accounting_item_id = #{fkAccountingItemId}
   </select>

    <select id="getReceiptFeeTypeSelect" resultType="com.get.financecenter.vo.BaseSelectVo">
        select id , type_name as name,fk_accounting_item_id as fkAccountingItemId, relation_target_key as relationTargetKey from u_receipt_fee_type order by view_order desc
    </select>

    <select id="checkName" resultType="int">
        select count(*) from u_receipt_fee_type urft where urft.type_name = #{typeName}
    </select>
</mapper>