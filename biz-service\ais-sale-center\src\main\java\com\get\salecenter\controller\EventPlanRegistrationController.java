package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.EventPlanRegistrationVo;
import com.get.salecenter.vo.EventPlanRegistrationResultVo;
import com.get.salecenter.vo.EventPlanRegistrationUpdateVo;
import com.get.salecenter.vo.EventPlanSelectVo;
import com.get.salecenter.vo.EventPlanThemeOnlineSelectVo;
import com.get.salecenter.vo.EventPlanThemeSelectVo;
import com.get.salecenter.service.EventPlanRegistrationService;
import com.get.salecenter.dto.EventPlanRegistrationFormDto;
import com.get.salecenter.dto.EventPlanRegistrationSearchDto;
import com.get.salecenter.dto.EventPlanRegistrationDto;
import com.get.salecenter.dto.OfflineLocationSelectDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 *  前端接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */

@Api(tags = "活动年度计划报名名册")
@RestController
@RequestMapping("sale/eventPlanRegistration")
public class EventPlanRegistrationController {

    @Resource
    private EventPlanRegistrationService eventPlanRegistrationService;

    @ApiOperation(value = "活动年度计划表单注册")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/活动年度计划报名名册/活动年度计划表单注册")
    @PostMapping("/register")
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    public ResponseBo eventPlanRegistration(@RequestBody @Validated EventPlanRegistrationFormDto registrationFormVo){
        eventPlanRegistrationService.eventPlanRegistration(registrationFormVo);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动年度计划报名名册/列表数据")
    @PostMapping("datas")
    public EventPlanRegistrationResultVo<EventPlanRegistrationVo> datas(@RequestBody SearchBean<EventPlanRegistrationSearchDto> page) {
        List<EventPlanRegistrationVo> datas = eventPlanRegistrationService.getEventPlanRegistrations(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page,Page::new);
        return new EventPlanRegistrationResultVo<>(datas,p,datas.size());
    }


    @ApiOperation(value = "列表收费总计", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动年度计划报名名册/列表收费总计")
    @PostMapping("getTotalFee")
    public ResponseBo getEventPlanRegistrationTotalFee(@RequestBody EventPlanRegistrationSearchDto searchVo){
        return new ResponseBo(eventPlanRegistrationService.getEventPlanRegistrationTotalFee(searchVo));
    }

    @ApiOperation(value = "导出列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/活动年度计划报名名册/导出列表数据")
    @PostMapping("/exportEventPlanRegistrationsExcel")
    @ResponseBody
    public void exportEventPlanRegistrationsExcel(@RequestBody EventPlanRegistrationSearchDto searchVo, HttpServletResponse response) {
        eventPlanRegistrationService.exportEventPlanRegistrationsExcel(searchVo,response);
    }

    @ApiOperation(value = "详情", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/活动年度计划报名名册/详情")
    @GetMapping("/{id}")
    public ResponseBo<EventPlanRegistrationUpdateVo> detail(@PathVariable("id") Long id) {
        EventPlanRegistrationUpdateVo data = eventPlanRegistrationService.findEventPlanRegistrationById(id);
        return new ResponseBo<>(data);
    }

    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动年度计划报名名册/更新")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated(EventPlanRegistrationDto.Update.class) EventPlanRegistrationDto eventPlanRegistrationDto) {
        eventPlanRegistrationService.updateEventPlanRegistration(eventPlanRegistrationDto);
        return UpdateResponseBo.ok();
    }

    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/活动年度计划报名名册/删除报名名册项目")
    @PostMapping("delete/{fkEventPlanRegistrationEventId}")
    public ResponseBo delete(@PathVariable("fkEventPlanRegistrationEventId") Long fkEventPlanRegistrationEventId) {
        eventPlanRegistrationService.delete(fkEventPlanRegistrationEventId);
        return DeleteResponseBo.ok();
    }

    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "活动年度计划下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动年度计划报名名册/活动年度计划下拉")
    @PostMapping("getEventPlanSelect")
    public ResponseBo<EventPlanSelectVo> getEventPlanSelect(@RequestParam("year") Integer year){
        return new ResponseBo(eventPlanRegistrationService.getEventPlanListByYear(year));
    }

    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "活动主题下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动年度计划报名名册/活动主题下拉")
    @PostMapping("getEventPlanThemeSelect")
    public ResponseBo<EventPlanThemeSelectVo> getEventPlanThemeSelect(@RequestParam("fkEventPlanId") Long fkEventPlanId, @RequestParam(required = false,value = "displayType") Integer displayType){
        return new ResponseBo(eventPlanRegistrationService.getThemeListByPlanId(fkEventPlanId,displayType));
    }

    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "线上活动项目下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动年度计划报名名册/线上活动项目下拉")
    @PostMapping("getOnlineSelect")
    public ResponseBo<EventPlanThemeOnlineSelectVo> getOnlineSelect(@RequestBody List<Long> fkEventPlanThemeIds){
        return new ResponseBo(eventPlanRegistrationService.getOnlineSelect(fkEventPlanThemeIds));
    }


    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "线下活动项目业务国家下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动年度计划报名名册/线下活动项目业务国家下拉")
    @PostMapping("getOfflineAreaCountryNameSelect")
    public ResponseBo getOfflineAreaCountryNameSelect(@RequestBody List<Long> fkEventPlanThemeIds){
        return new ResponseBo(eventPlanRegistrationService.getOfflineAreaCountryNameList(fkEventPlanThemeIds));
    }

    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "线下活动项目地区下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动年度计划报名名册/线下活动项目地区下拉")
    @PostMapping("getOfflineLocationSelect")
    public ResponseBo getOfflineLocationSelect(@RequestBody OfflineLocationSelectDto offlineLocationSelectDto){
        return new ResponseBo(eventPlanRegistrationService.getOfflineLocationList(offlineLocationSelectDto));
    }

    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "线下专访项目地区下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动年度计划报名名册/线下专访项目地区下拉")
    @PostMapping("getWorkshopLocationSelect")
    public ResponseBo getWorkshopLocationSelect(@RequestBody List<Long> fkEventPlanThemeIds){
        return new ResponseBo(eventPlanRegistrationService.getWorkshopLocationSelect(fkEventPlanThemeIds));
    }


    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学校提供商下拉(名册列表过滤使用，只包含列表存在的提供商)", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动年度计划/根据关键字模糊查询学校提供商列表")
    @PostMapping("getInstitutionProviderSelect")
    public ResponseBo<BaseSelectEntity> getInstitutionProviderSelect(){
        return new ResponseBo(eventPlanRegistrationService.getInstitutionProviderSelect());
    }

}
