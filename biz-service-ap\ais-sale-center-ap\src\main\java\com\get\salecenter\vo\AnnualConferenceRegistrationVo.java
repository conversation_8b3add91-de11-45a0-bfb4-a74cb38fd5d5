package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/29 12:08
 * @verison: 1.0
 * @description:
 */
@Data
public class AnnualConferenceRegistrationVo {
    /**
     * 赞助商名称
     */
    @ApiModelProperty(value = "赞助商名称")
    String sponsorName;

    /**
     * 联系人名称
     */
    @ApiModelProperty(value = "联系人名称")
    String name;

    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话")
    String tel;

    /**
     * 联系人邮箱
     */
    @ApiModelProperty(value = "联系人邮箱")
    String email;

    /**
     * 回执码
     */
    @ApiModelProperty(value = "回执码")
    String receiptCode;

    /**
     * 展位对象集合
     */
    @ApiModelProperty(value = "展位对象集合")
    List<ConventionRegistrationVo> registrationDtos;

    /**
     * 赞助对象集合
     */
    @ApiModelProperty(value = "赞助对象集合")
    List<ConventionSponsorFeeVo> sponsorFeeDtos;
}
