package com.get.insurancecenter.vo.card;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author:<PERSON>
 * @Date: 2025/7/25
 * @Version 1.0
 * @apiNote:银行列表
 */
@Data
public class BankVo {

    @ApiModelProperty(value = "银行Id")
    private Long bankId;

    @ApiModelProperty(value = "银行名称")
    private String bankName;

    @ApiModelProperty(value = "币种编号")
    private String currencyTypeNum;
}
