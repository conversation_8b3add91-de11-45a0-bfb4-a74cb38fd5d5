<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.ContractFormulaCourseTypeMapper">
  <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.ContractFormulaCourseType">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_contract_formula_id" jdbcType="BIGINT" property="fkContractFormulaId" />
    <result column="fk_course_type_id" jdbcType="BIGINT" property="fkCourseTypeId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.ContractFormulaCourseType" keyProperty="id" useGeneratedKeys="true">
    insert into r_contract_formula_course_type
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkContractFormulaId != null">
        fk_contract_formula_id,
      </if>
      <if test="fkCourseTypeId != null">
        fk_course_type_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkContractFormulaId != null">
        #{fkContractFormulaId,jdbcType=BIGINT},
      </if>
      <if test="fkCourseTypeId != null">
        #{fkCourseTypeId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getCourseTypeIdListByFkid" resultType="java.lang.Long">
    select
     fk_course_type_id
    from
     r_contract_formula_course_type
    where
     fk_contract_formula_id = #{contractFormulaId}
  </select>

  <select id="getCourseTypeNameByFkid" resultType="java.lang.String">
    SELECT
	    b.type_name
    FROM
        r_contract_formula_course_type a
        LEFT JOIN u_course_type b ON a.fk_course_type_id = b.id
    WHERE
        fk_contract_formula_id = #{contractFormulaId}
  </select>
</mapper>