package com.get.salecenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.DataConverter;
import com.get.common.utils.GetStringUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.DateUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.financecenter.dto.SettlementInstallmentUpdateDto;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.financecenter.vo.ReceiptFormItemVo;
import com.get.financecenter.vo.ReceiptFormVo;
import com.get.institutioncenter.dto.InstitutionStudentOfferItemDto;
import com.get.institutioncenter.entity.ContractFormula;
import com.get.institutioncenter.entity.ContractFormulaCommission;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.institutioncenter.vo.ContractFormulaCommissionVo;
import com.get.institutioncenter.vo.InstitutionProviderVo;
import com.get.permissioncenter.dto.ConfigRemindDto;
import com.get.permissioncenter.entity.Company;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.CompanyConfigAnalysisVo;
import com.get.permissioncenter.vo.CompanyVo;
import com.get.salecenter.dao.insurance.InsuranceOrderMapper;
import com.get.salecenter.dao.insurance.ProductTypeMapper;
import com.get.salecenter.dao.sale.AgentMapper;
import com.get.salecenter.dao.sale.BusinessChannelMapper;
import com.get.salecenter.dao.sale.EventBillMapper;
import com.get.salecenter.dao.sale.PayablePlanMapper;
import com.get.salecenter.dao.sale.RStudentOfferItemStepMapper;
import com.get.salecenter.dao.sale.ReceivablePlanBonusSettingMapper;
import com.get.salecenter.dao.sale.ReceivablePlanContractFormulaMapper;
import com.get.salecenter.dao.sale.ReceivablePlanDateMapper;
import com.get.salecenter.dao.sale.ReceivablePlanMapper;
import com.get.salecenter.dao.sale.StaffBdCodeMapper;
import com.get.salecenter.dao.sale.StudentAccommodationMapper;
import com.get.salecenter.dao.sale.StudentInsuranceMapper;
import com.get.salecenter.dao.sale.StudentMapper;
import com.get.salecenter.dao.sale.StudentOfferItemDeferEntranceTimeMapper;
import com.get.salecenter.dao.sale.StudentOfferItemFailureMapper;
import com.get.salecenter.dao.sale.StudentOfferItemMapper;
import com.get.salecenter.dao.sale.StudentOfferItemStepMapper;
import com.get.salecenter.dao.sale.StudentOfferMapper;
import com.get.salecenter.dao.sale.StudentServiceFeeMapper;
import com.get.salecenter.dto.ARAPDto;
import com.get.salecenter.dto.ArApHedgingDto;
import com.get.salecenter.dto.InstitutionProviderReceivableSumDetailDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.ReceivablePlanBatchDto;
import com.get.salecenter.dto.ReceivablePlanDto;
import com.get.salecenter.dto.ReceivablePlanNewDto;
import com.get.salecenter.dto.StudentReceivableSumDetailDto;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.entity.InsuranceOrder;
import com.get.salecenter.entity.PayablePlan;
import com.get.salecenter.entity.ProductType;
import com.get.salecenter.entity.RStudentOfferItemStep;
import com.get.salecenter.entity.ReceivablePlan;
import com.get.salecenter.entity.ReceivablePlanBonusSetting;
import com.get.salecenter.entity.ReceivablePlanContractFormula;
import com.get.salecenter.entity.ReceivablePlanDate;
import com.get.salecenter.entity.SaleComment;
import com.get.salecenter.entity.StaffBdCode;
import com.get.salecenter.entity.Student;
import com.get.salecenter.entity.StudentAccommodation;
import com.get.salecenter.entity.StudentInsurance;
import com.get.salecenter.entity.StudentOffer;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.entity.StudentOfferItemFailure;
import com.get.salecenter.entity.StudentOfferItemStep;
import com.get.salecenter.service.DorisService;
import com.get.salecenter.service.IAgentService;
import com.get.salecenter.service.ICommentService;
import com.get.salecenter.service.IMediaAndAttachedService;
import com.get.salecenter.service.IPayablePlanService;
import com.get.salecenter.service.IReceivablePlanService;
import com.get.salecenter.service.IReceivableReasonService;
import com.get.salecenter.service.IStudentAccommodationService;
import com.get.salecenter.service.IStudentInsuranceService;
import com.get.salecenter.service.IStudentOfferItemService;
import com.get.salecenter.service.StudentOfferItemDeferEntranceService;
import com.get.salecenter.service.StudentServiceFeeService;
import com.get.salecenter.utils.ConvertUtils;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.vo.BusinessChannelVo;
import com.get.salecenter.vo.CompanyTreeVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.vo.PayablePlanVo;
import com.get.salecenter.vo.PlanRemarkDetailResultVo;
import com.get.salecenter.vo.PlanRemarkDetailVo;
import com.get.salecenter.vo.PublicPayFormDetailVo;
import com.get.salecenter.vo.PublicReceiptFormDetailVo;
import com.get.salecenter.vo.ReceivablePlanDateVo;
import com.get.salecenter.vo.ReceivablePlanExportVo;
import com.get.salecenter.vo.ReceivablePlanNewVo;
import com.get.salecenter.vo.ReceivablePlanVo;
import com.get.salecenter.vo.SelItem;
import com.get.salecenter.vo.StudentAccommodationVo;
import com.get.salecenter.vo.StudentInsuranceVo;
import com.get.salecenter.vo.StudentOfferItemVo;
import com.get.salecenter.vo.StudentServiceFeeVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @DATE: 2020/11/19
 * @TIME: 16:13
 * @Description:
 **/
@Service
@Slf4j
public class ReceivablePlanServiceImpl extends ServiceImpl<ReceivablePlanMapper, ReceivablePlan> implements IReceivablePlanService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    private ReceivablePlanMapper receivablePlanMapper;
    @Resource
    private ReceivablePlanDateMapper receivablePlanDateMapper;
    @Resource
    private DorisService dorisService;
    @Resource
    @Lazy
    private IStudentOfferItemService offerItemService;
    @Resource
    private UtilService utilService;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private StaffBdCodeMapper staffBdCodeMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private PayablePlanMapper payablePlanMapper;
    @Lazy
    @Resource
    private IStudentInsuranceService studentInsuranceService;
    @Lazy
    @Resource
    private IStudentAccommodationService studentAccommodationService;
    @Resource
    private IReceivableReasonService reasonService;
    @Resource
    private StudentOfferMapper studentOfferMapper;
    @Resource
    private StudentMapper studentMapper;
    @Resource
    private StudentServiceFeeService studentServiceFeeService;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private ReceivablePlanContractFormulaMapper receivablePlanContractFormulaMapper;
    @Resource
    private StudentOfferItemDeferEntranceTimeMapper studentOfferItemDeferEntranceTimeMapper;
    @Resource
    private StudentOfferItemMapper studentOfferItemMapper;
    @Resource
    @Lazy
    private IPayablePlanService payablePlanService;
    @Resource
    private StudentOfferItemFailureMapper studentOfferItemFailureMapper;
    @Resource
    private StudentInsuranceMapper studentInsuranceMapper;
    @Resource
    private StudentAccommodationMapper studentAccommodationMapper;
    @Resource
    private BusinessChannelMapper businessChannelMapper;
    @Resource
    private EventBillMapper eventBillMapper;
    @Resource
    @Lazy
    private IAgentService agentService;
    @Lazy
    @Resource
    private ICommentService commentService;
    @Resource
    private RStudentOfferItemStepMapper rStudentOfferItemStepMapper;
    @Resource
    private StudentOfferItemStepMapper studentOfferItemStepMapper;
    @Resource
    private StudentOfferItemDeferEntranceService studentOfferItemDeferEntranceService;
    @Resource
    private StudentServiceFeeMapper studentServiceFeeMapper;
    @Resource
    private ReceivablePlanBonusSettingMapper receivablePlanBonusSettingMapper;
    @Resource
    private InsuranceOrderMapper insuranceOrderMapper;
    @Resource
    private ProductTypeMapper productTypeMapper;
    @Resource
    private AgentMapper agentMapper;


    /**
     * Author Cream
     * Description : // 封装应收计划详细信息
     * Date 2023/9/8 14:38
     * Params:
     * Return
     */
    @Override
    public List<ReceivablePlanVo> packResult(List<ReceivablePlanVo> receivablePlanVos) {
//        List<ReceivablePlanVo> receivablePlanDtoList = new ArrayList<>();
//        if (GeneralTool.isNotEmpty(receivablePlans)) {
//            receivablePlanDtoList = receivablePlans.stream().map(receivablePlan -> BeanCopyUtils.objClone(receivablePlan, ReceivablePlanVo::new)).collect(Collectors.toList());
//        }
        //筛选出来之后
        Map<Long, BigDecimal> actualReceivableAmountMap = receivablePlanVos.stream().filter(r -> GeneralTool.isNotEmpty(r.getActualReceivableAmount())).collect(Collectors.toMap(ReceivablePlanVo::getId, ReceivablePlanVo::getActualReceivableAmount
                , (a1, a2) -> a1.compareTo(a2) == 0 ? a1 : a2));
        Map<Long, BigDecimal> diffReceivableAmountMap = receivablePlanVos.stream().filter(r -> GeneralTool.isNotEmpty(r.getDiffReceivableAmount())).collect(Collectors.toMap(ReceivablePlanVo::getId, ReceivablePlanVo::getDiffReceivableAmount, (a1, a2) -> a1.compareTo(a2) == 0 ? a1 : a2));
        Map<Long, Integer> receiveStatusMap = receivablePlanVos.stream().filter(r -> GeneralTool.isNotEmpty(r.getReceiveStatus())).collect(Collectors.toMap(ReceivablePlanVo::getId, ReceivablePlanVo::getReceiveStatus, (a1, a2) -> a1.equals(a2) ? a1 : a2));
        List<Long> itemIds = receivablePlanVos.stream().filter(receivablePlan -> TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(receivablePlan.getFkTypeKey()))
                //TODO 改过
                //.map(ReceivablePlan::getFkTypeTargetId).collect(Collectors.toList());
         .map(ReceivablePlanVo::getFkTypeTargetId).collect(Collectors.toList());
        if (GeneralTool.isEmpty(itemIds)) {
            itemIds = new ArrayList<>();
            itemIds.add(0L);
        }
        Map<Long, Long> providerIdMap = offerItemService.getProviderIdsByItemIds(itemIds);
        Map<Long, Long> companyIdMap = offerItemService.getCompanyIdsByItemIds(itemIds);

        for (ReceivablePlanVo receivablePlanVo : receivablePlanVos) {
            receivablePlanVo.setActualReceivableAmount(actualReceivableAmountMap.get(receivablePlanVo.getId()));
            receivablePlanVo.setDiffReceivableAmount(diffReceivableAmountMap.get(receivablePlanVo.getId()));
            receivablePlanVo.setReceiveStatus(receiveStatusMap.get(receivablePlanVo.getId()));
            if (GeneralTool.isNotEmpty(providerIdMap)) {
                receivablePlanVo.setFkInstitutionProviderId(providerIdMap.get(receivablePlanVo.getFkTypeTargetId()));
            }
            if (GeneralTool.isNotEmpty(companyIdMap)
                    && !TableEnum.INSTITUTION_PROVIDER.key.equals(receivablePlanVo.getFkTypeKey())
                    && !TableEnum.SALE_OTHER_INCOME.key.equals(receivablePlanVo.getFkTypeKey())
                    && !TableEnum.INSTITUTION_PROVIDER_CHANNEL.key.equals(receivablePlanVo.getFkTypeKey())) {
                receivablePlanVo.setFkCompanyId(companyIdMap.get(receivablePlanVo.getFkTypeTargetId()));
            }
        }
        List<ReceivablePlanVo> collect = receivablePlanVos.stream().filter(receivablePlanDto -> Objects.nonNull(receivablePlanDto.getReceiveStatus())).collect(Collectors.toList());

        Set<Long> providerIds = receivablePlanVos.stream().filter(receivablePlan -> {
            if (GeneralTool.isNotEmpty(receivablePlan.getFkTypeKey())) {
                return TableEnum.INSTITUTION_PROVIDER.key.equals(receivablePlan.getFkTypeKey()) || TableEnum.SALE_OTHER_INCOME.key.equals(receivablePlan.getFkTypeKey());
            }
            return false;
            //TODO 改过ReceivablePlan
        }).map(ReceivablePlanVo::getFkTypeTargetId).collect(Collectors.toSet());
        if (GeneralTool.isEmpty(providerIds)) {
            providerIds.add(0L);
        }

        setListName(collect);

        //活动费用的应收计划
        Set<Long> fkPlanIdSet = collect.stream().filter(r -> ProjectExtraEnum.EVENT_COST.key.equals(r.getBonusType())).map(ReceivablePlanVo::getId).collect(Collectors.toSet());
        if (GeneralTool.isEmpty(fkPlanIdSet)) {
            fkPlanIdSet.add(0L);
        }
        List<ReceivablePlanVo> datas = eventBillMapper.getCountryNamesByPlanIds(fkPlanIdSet);
        Map<Long, String> countryNames = null;
        if (GeneralTool.isNotEmpty(datas)) {
            countryNames = datas.stream().collect(HashMap::new, (m, v) -> m.put(v.getId(), v.getFkAreaCountryName()), HashMap::putAll);
        }

        Map<Long, String> finalCountryNames = countryNames;
        collect = collect.stream().map(r -> {
            if (fkPlanIdSet.contains(r.getId())) {
                if (GeneralTool.isNotEmpty(finalCountryNames) && GeneralTool.isNotEmpty(finalCountryNames.get(r.getId()))) {
                    r.setFkAreaCountryName(finalCountryNames.get(r.getId()));
                }
            }
            return r;
        }).collect(Collectors.toList());

        List<ReceivablePlanVo> data = collect.stream().map(receivablePlanDto -> BeanCopyUtils.objClone(receivablePlanDto, ReceivablePlanVo::new)).collect(Collectors.toList());
        return data;
    }

    /**
     * Author Cream
     * Description : 获取应收计划id 和 公司名称
     * Date 2022/5/9 10:20
     * Params:
     * Return
     */
    @Override
    public List<BaseSelectEntity> getPlanIdsAndCompanyName(String typeKey, Long targetId, Long receiptFormId) {
        return receivablePlanMapper.getReIdsAndCompanyName(typeKey, targetId, receiptFormId);
    }

    @Override
    public Page getReceiptFormReceivablePlanPaginationInfo(String tableName, Long targetId, Long receiptFormId, String fkTypeKey, Page page) {
        Integer count = null;
        if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(tableName)) {
            //提供商
            if (TableEnum.INSTITUTION_PROVIDER.key.equals(fkTypeKey)) {
                count = receivablePlanMapper.getReceiptFormReCount(fkTypeKey, targetId, receiptFormId);
            } else if (ProjectKeyEnum.INSTITUTION_PROVIDER_CHANNEL.key.equals(fkTypeKey)) {
                //学校渠道
                count = receivablePlanMapper.getReceiptFormReCountByChannel(fkTypeKey, targetId, receiptFormId);
            }
        } else if (TableEnum.SALE_STUDENT_INSURANCE.key.equals(tableName)) {
            //保险
            if (ProjectKeyEnum.BUSINESS_CHANNEL_INS.key.equals(fkTypeKey)) {
                count = receivablePlanMapper.getReceiptFormReCount(fkTypeKey, targetId, receiptFormId);
            }
        } else if (TableEnum.SALE_STUDENT_ACCOMMODATION.key.equals(tableName)) {
            //住宿
            if (ProjectKeyEnum.BUSINESS_CHANNEL_ACC.key.equals(fkTypeKey)) {
                count = receivablePlanMapper.getReceiptFormReCount(fkTypeKey, targetId, receiptFormId);
            }
        }
        page.setAll(count);
        return page;
    }

    /**
     * Author Cream
     * Description : //获取最新的三条学费
     * Date 2022/12/21 13:39
     * Params:
     * Return
     * @param fkCompanyId
     */
    @Override
    public List<String> getRePlanTheLatestThreeTuitionFees(Long fkCompanyId) {
        return receivablePlanMapper.getRePlanTheLatestThreeTuitionFees(fkCompanyId);
    }

    /**
     * 留学申请业务，学费佣金到账后自动关绑入学Enrolled步骤
     * @param receivablePlanIds
     */
    @Override
    public Boolean autoRelationReceipten(Set<Long> receivablePlanIds) {
        List<ReceivablePlan> receivablePlans = receivablePlanMapper.selectList(Wrappers.<ReceivablePlan>lambdaQuery().in(ReceivablePlan::getId,
                receivablePlanIds).eq(ReceivablePlan::getFkTypeKey,TableEnum.SALE_STUDENT_OFFER_ITEM.key));
        if (GeneralTool.isNotEmpty(receivablePlans)){
            Set<Long> offerItemIds = receivablePlans.stream().map(ReceivablePlan::getFkTypeTargetId).collect(Collectors.toSet());
            Set<Long> enrolledStepOfferIds = studentOfferItemMapper.isEenrolledStep(offerItemIds);

            for (Long offeItemrId : offerItemIds) {
                if (GeneralTool.isNotEmpty(enrolledStepOfferIds)&&enrolledStepOfferIds.contains(offeItemrId)){
                    continue;
                }
                StudentOfferItem studentOfferItem = studentOfferItemMapper.selectById(offeItemrId);
                studentOfferItem.setStudentOfferItemStepTime(new Date());
                studentOfferItem.setFkStudentOfferItemStepId(8L);
                if (GeneralTool.isNotEmpty(studentOfferItem.getFkParentStudentOfferItemId()) && studentOfferItem.getIsStepFollow()){
                    studentOfferItem.setIsStepFollow(false);
                }
                studentOfferItemMapper.updateById(studentOfferItem);
                RStudentOfferItemStep rStudentOfferItemStep = new RStudentOfferItemStep();
                utilService.setCreateInfo(rStudentOfferItemStep);
                rStudentOfferItemStep.setFkStudentOfferItemId(offeItemrId);
                rStudentOfferItemStep.setFkStudentOfferItemStepId(8L);
                rStudentOfferItemStepMapper.insert(rStudentOfferItemStep);
            }
            //跟随的子申请计划，也设置相同的步骤日志及当前步骤
            List<StudentOfferItem> studentOfferItemSons =
                    studentOfferItemMapper.selectList(Wrappers.<StudentOfferItem>lambdaQuery().in(StudentOfferItem::getFkParentStudentOfferItemId, offerItemIds).eq(StudentOfferItem::getIsStepFollow,true));
            if (GeneralTool.isNotEmpty(studentOfferItemSons)){
                Set<Long> sunOfferIds = studentOfferItemSons.stream().map(StudentOfferItem::getId).collect(Collectors.toSet());
                Set<Long> enrolledStepOfferSonIds = studentOfferItemMapper.isEenrolledStep(sunOfferIds);
                for (Long offerId : sunOfferIds) {
                    if (GeneralTool.isNotEmpty(enrolledStepOfferSonIds) && enrolledStepOfferSonIds.contains(offerId)){
                        break;
                    }
                    StudentOfferItem studentOfferItem = new StudentOfferItem();
                    studentOfferItem.setStudentOfferItemStepTime(new Date());
                    studentOfferItem.setFkStudentOfferItemStepId(8L);
                    studentOfferItemMapper.update(studentOfferItem,Wrappers.<StudentOfferItem>lambdaQuery().eq(StudentOfferItem::getId,offerId));
                    RStudentOfferItemStep rStudentOfferItemStep = new RStudentOfferItemStep();
                    utilService.setCreateInfo(rStudentOfferItemStep);
                    rStudentOfferItemStep.setFkStudentOfferItemId(offerId);
                    rStudentOfferItemStep.setFkStudentOfferItemStepId(8L);
                    rStudentOfferItemStepMapper.insert(rStudentOfferItemStep);
                }
            }
        }
        return true;
    }

    /**
     * Author Cream
     * Description : // 应收应付对冲
     * Date 2023/2/20 10:27
     * Params:
     * Return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SaveResponseBo arApHedging(ArApHedgingDto arApHedgingDto) {
        if (BigDecimal.ZERO.compareTo(DataConverter.bigDecimalNullConvert(arApHedgingDto.getReceivableAmount()))<0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("the_receivable_amount_should_be_negative"));
        }
        /*
         具体需求详细过长，详见禅道：
         https://zentao.geteducation.net/zentao/bug-view-2673.html
         */
        Long receivablePlanId = arApHedgingDto.getReceivablePlanId();
        boolean flag = GeneralTool.isNotEmpty(arApHedgingDto.getPayableFlag()) && arApHedgingDto.getPayableFlag();
        ReceivablePlan receivablePlan = receivablePlanMapper.selectById(receivablePlanId);
        if (receivablePlan != null) {
            //处理应收
            ReceivablePlan newPlan = new ReceivablePlan();
            BeanUtils.copyProperties(receivablePlan, newPlan);
            newPlan.setReceivableAmount(arApHedgingDto.getReceivableAmount());
            BigDecimal tuitionAmount = DataConverter.bigDecimalNullConvert(receivablePlan.getTuitionAmount());
            if (BigDecimal.ZERO.compareTo(tuitionAmount) != 0) {
                BigDecimal bigDecimal = BigDecimal.valueOf(100);
                BigDecimal multiply = arApHedgingDto.getReceivableAmount().divide(tuitionAmount, 2, RoundingMode.UP);
                newPlan.setCommissionRate(multiply.multiply(bigDecimal));
                BigDecimal cAmount = tuitionAmount.multiply(multiply).multiply(DataConverter.bigDecimalNullConvert(newPlan.getNetRate()).divide(bigDecimal, 2, RoundingMode.UP));
                newPlan.setCommissionAmount(cAmount);
            }
            newPlan.setId(null);
            newPlan.setGmtCreate(null);
            newPlan.setGmtCreateUser(null);
            newPlan.setGmtModified(null);
            newPlan.setGmtModifiedUser(null);
            newPlan.setFkCompanyId(arApHedgingDto.getFkCompanyId());
            newPlan.setFkReceivableReasonId(arApHedgingDto.getFkReceivableResonId());
            newPlan.setFkCurrencyTypeNum(arApHedgingDto.getFkReceivableCurrencyNum());
            newPlan.setSummary(arApHedgingDto.getReceivableRemark());
            utilService.setCreateInfo(newPlan);
            //新增一个新的应收
            receivablePlanMapper.insert(newPlan);
            //应收绑定发票
            financeCenterClient.receivableBindingInvoice(newPlan.getId(), arApHedgingDto.getReceivableAmount(), arApHedgingDto.getInvoiceId());
            //处理应付
            if (flag) {
                BigDecimal payableAmount = DataConverter.bigDecimalNullConvert(arApHedgingDto.getPayableAmount());
                if (BigDecimal.ZERO.compareTo(payableAmount)<0) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("the_payable_amount_should_be_negative"));
                }
                PayablePlan payablePlan = payablePlanService.getPayablePlanByReceivablePlanId(receivablePlan.getId());
                if (GeneralTool.isNotEmpty(payablePlan)) {
                    PayablePlan newPay = new PayablePlan();
                    BeanUtils.copyProperties(payablePlan,newPay);
                    newPay.setPayableAmount(payableAmount);
                    newPay.setFkReceivablePlanId(newPlan.getId());
                    BigDecimal planTuitionAmount = DataConverter.bigDecimalNullConvert(payablePlan.getTuitionAmount());
                    if (BigDecimal.ZERO.compareTo(planTuitionAmount) != 0) {
                        BigDecimal bigDecimal = BigDecimal.valueOf(100);
                        BigDecimal multiply = payableAmount.divide(planTuitionAmount, 2, RoundingMode.UP);
                        newPay.setCommissionRate(multiply.multiply(bigDecimal));
                        BigDecimal cAmount = planTuitionAmount.multiply(multiply).multiply(DataConverter.bigDecimalNullConvert(newPay.getSplitRate()).divide(bigDecimal, 2, RoundingMode.UP));
                        newPay.setCommissionAmount(cAmount);
                    }
                    newPay.setFkCurrencyTypeNum(arApHedgingDto.getFkPayableCurrencyNum());
                    newPay.setId(null);
                    newPay.setIsPayInAdvance(null);
                    newPay.setPayInAdvancePercent(null);
                    newPay.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
                    newPay.setGmtCreate(null);
                    newPay.setGmtCreateUser(null);
                    newPay.setGmtModified(null);
                    newPay.setGmtModifiedUser(null);
                    newPay.setSummary(arApHedgingDto.getPayableRemark());
                    utilService.setCreateInfo(newPay);
                    //新增一个新的应付
                    payablePlanMapper.insert(newPay);
                }
            }
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("accounts_receivable_do_not_exist"));
        }
        return SaveResponseBo.ok(receivablePlanId);
    }

    /**
     * 获取应收金额信息
     * @param receivablePlanIds
     * @return
     */
    @Override
    public List<ReceivablePlanVo> getReceivableAmountInfo(Set<Long> receivablePlanIds) {
        if (receivablePlanIds.isEmpty()) {
            return Collections.emptyList();
        }
        return receivablePlanMapper.getReceivableAmountInfo(receivablePlanIds);
    }

    /**
     * 获取传入固定排序的应收计划
     * @param planIds
     * @param invoiceAmountSort
     * @param receiptAmountSort
     * @param studentName
     * @param receiptFormId
     * @return
     */
    @Override
    public List<ReceivablePlanVo> getReceivablePlansBySort(Set<Long> planIds, Boolean invoiceAmountSort, Boolean receiptAmountSort, String studentName, Long receiptFormId) {
        if (planIds.isEmpty()) {
            return Collections.emptyList();
        }
        studentName = DataConverter.stringManipulation(studentName);
        return payablePlanMapper.getReceivablePlansBySort(planIds,invoiceAmountSort,receiptAmountSort,studentName,receiptFormId);
    }

    /**
     * 提供商收款单获取应收计划下拉列表
     *
     * @param providerId
     * @param receiptFormId
     * @return
     */
    @Override
    public List<BaseSelectEntity> getReceivablePlanSelectByProvider(Long providerId, Long receiptFormId) {
        return receivablePlanMapper.getReceivablePlanSelectByProvider(providerId, receiptFormId);
    }


    /**
     * 根据ids获取应收计划列表
     *
     * @param fkReceivablePlanIds
     * @return
     * @
     */
    @Override
    public List<ReceivablePlanVo> getReceivablePlanDtosByIds(List<Long> fkReceivablePlanIds) {
        if (GeneralTool.isEmpty(fkReceivablePlanIds)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ReceivablePlanDto receivablePlanDto = new ReceivablePlanDto();
        receivablePlanDto.setIds(fkReceivablePlanIds);
        List<ReceivablePlanVo> receivablePlanVos = receivablePlanMapper.getReceivablePlansInfo(receivablePlanDto);
        return packResult(receivablePlanVos);
//        long getReceivablePlansInfoTime = System.currentTimeMillis();
//
//        List<ReceivablePlan> receivablePlans = receivablePlanMapper.selectBatchIds(fkReceivablePlanIds);
//        List<ReceivablePlanVo> receivablePlanDtoList = new ArrayList<>();
//        if (GeneralTool.isNotEmpty(receivablePlans)) {
//            receivablePlanDtoList = receivablePlans.stream().map(receivablePlan -> BeanCopyUtils.objClone(receivablePlan, ReceivablePlanVo::new)).collect(Collectors.toList());
//        }
//        //筛选出来之后
//        Map<Long, BigDecimal> actualReceivableAmountMap = receivablePlanVos.stream().collect(Collectors.toMap(ReceivablePlanVo::getId, ReceivablePlanVo::getActualReceivableAmount));
//        Map<Long, BigDecimal> diffReceivableAmountMap = receivablePlanVos.stream().collect(Collectors.toMap(ReceivablePlanVo::getId, ReceivablePlanVo::getDiffReceivableAmount));
//        Map<Long, Integer> receiveStatusMap = receivablePlanVos.stream().collect(Collectors.toMap(ReceivablePlanVo::getId, ReceivablePlanVo::getReceiveStatus));
//        List<Long> itemIds = receivablePlans.stream().filter(receivablePlan -> TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(receivablePlan.getFkTypeKey()))
//                .map(ReceivablePlan::getFkTypeTargetId).collect(Collectors.toList());
//        if (GeneralTool.isEmpty(itemIds)) {
//            itemIds = new ArrayList<>();
//            itemIds.add(0L);
//        }
//        Map<Long, Long> providerIdMap = offerItemService.getProviderIdsByItemIds(itemIds);
//        Map<Long, Long> companyIdMap = offerItemService.getCompanyIdsByItemIds(itemIds);
//        Map<String, String> companyMap = getCompanyMap();
//
//        for (ReceivablePlanVo receivablePlanDto : receivablePlanDtoList) {
//            receivablePlanDto.setActualReceivableAmount(actualReceivableAmountMap.get(receivablePlanDto.getId()));
//            receivablePlanDto.setDiffReceivableAmount(diffReceivableAmountMap.get(receivablePlanDto.getId()));
//            receivablePlanDto.setReceiveStatus(receiveStatusMap.get(receivablePlanDto.getId()));
//            if (GeneralTool.isNotEmpty(providerIdMap)) {
//                receivablePlanDto.setFkInstitutionProviderId(providerIdMap.get(receivablePlanDto.getFkTypeTargetId()));
//            }
//            if (GeneralTool.isNotEmpty(companyIdMap)) {
//                receivablePlanDto.setFkCompanyId(companyIdMap.get(receivablePlanDto.getFkTypeTargetId()));
//            }
//        }
//        List<ReceivablePlanVo> collect = receivablePlanDtoList.stream().filter(receivablePlanDto -> Objects.nonNull(receivablePlanDto.getReceiveStatus())).collect(Collectors.toList());
//
//        //应收类型对应记录ids
//        Set<Long> fkTypeTargetIds = collect.stream().map(ReceivablePlanVo::getFkTypeTargetId).collect(Collectors.toSet());
//        //根据应收类型对应记录ids获取对象map
//        Map<Long, StudentOfferItemVo> offerItemByIds = new HashMap<>();
//        if (GeneralTool.isNotEmpty(fkTypeTargetIds)) {
//            offerItemByIds = offerItemService.findOfferItemByIds(fkTypeTargetIds);
//        }
//
//        long findOfferItemByIdsTime = System.currentTimeMillis();
//
//        //币种编号nums
//        Set<String> fkCurrencyTypeNums = collect.stream().map(ReceivablePlanVo::getFkCurrencyTypeNum).collect(Collectors.toSet());
//        //根据币种编号nums获取名称
//        Map<String, String> currencyNamesByNums = new HashMap<>();
//        if (GeneralTool.isNotEmpty(fkCurrencyTypeNums)) {
//            Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(fkCurrencyTypeNums);
//            if (result.isSuccess() && result.getData() != null) {
//                currencyNamesByNums = result.getData();
//            }
//        }
//
//        long getCurrencyNamesByNumsTime = System.currentTimeMillis();
//
//        //应收计划额外原因ids
//        Set<Integer> fkReceivableReasonIds = collect.stream().map(ReceivablePlanVo::getFkReceivableReasonId).collect(Collectors.toSet());
//        //根据应收计划额外原因ids获取名称
//        Map<Long, String> reasonNameByIds = new HashMap<>();
//        if (GeneralTool.isNotEmpty(fkReceivableReasonIds)) {
//            reasonNameByIds = reasonService.getReasonNameByIds(fkReceivableReasonIds);
//        }
//
//
//        //提供商公司id map
//        Map<Long, Set<Long>> providerCompanyIdMap = new HashMap<>();
//        Set<Long> providerIds = receivablePlans.stream().filter(receivablePlan -> {
//            if (GeneralTool.isNotEmpty(receivablePlan.getFkTypeKey())) {
//                if (TableEnum.INSTITUTION_PROVIDER.key.equals(receivablePlan.getFkTypeKey()) || TableEnum.SALE_OTHER_INCOME.key.equals(receivablePlan.getFkTypeKey())) {
//                    return true;
//                } else {
//                    return false;
//                }
//            }
//            return false;
//        }).map(ReceivablePlan::getFkTypeTargetId).collect(Collectors.toSet());
//        if (GeneralTool.isEmpty(providerIds)) {
//            providerIds.add(0L);
//        }
//        providerCompanyIdMap = institutionCenterClient.getCompanyIdsByProviderIds(providerIds).getData();
//
//        Map<Long, String> institutionProviderSelectNamesByIds = institutionCenterClient.getInstitutionProviderSelectNamesByIds(providerIds);
//        long getInstitutionProviderSelectNamesByIdsTime = System.currentTimeMillis();
//
//        for (ReceivablePlanVo receivablePlanDto : collect) {
//            if (GeneralTool.isNotEmpty(receivablePlanDto)) {
//                setListName(receivablePlanDto, companyMap, offerItemByIds, currencyNamesByNums, reasonNameByIds, institutionProviderSelectNamesByIds);
//            }
//        }
//        List<ReceivablePlanVo> data = collect.stream().map(receivablePlanDto -> BeanCopyUtils.objClone(receivablePlanDto, ReceivablePlanVo::new)).collect(Collectors.toList());
//
//        long endTime = System.currentTimeMillis();
//        log.info("====>发票应收计划列表：getReceivablePlansInfo用时{}",String.valueOf((getReceivablePlansInfoTime - startTime)));
//        log.info("====>发票应收计划列表：findOfferItemByIds用时{}",String.valueOf((findOfferItemByIdsTime - getReceivablePlansInfoTime)));
//        log.info("====>发票应收计划列表：getCurrencyNamesByNums用时{}",String.valueOf((getCurrencyNamesByNumsTime - findOfferItemByIdsTime)));
//        log.info("====>发票应收计划列表：getInstitutionProviderSelectNamesByIds用时{}",String.valueOf((getInstitutionProviderSelectNamesByIdsTime - getCurrencyNamesByNumsTime)));
//        log.info("====>发票应收计划列表：setListName用时{}",String.valueOf((endTime - getInstitutionProviderSelectNamesByIdsTime)));
//        return data;
    }

    @Override
    public List<ReceivablePlanVo> getReceivablePlan(ReceivablePlanDto receivablePlanDto, Page page, String[] times) {
        long startTime = System.currentTimeMillis();

        if (GeneralTool.isEmpty(receivablePlanDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }

        //查询条件-公司ids
        if (GeneralTool.isNotEmpty(receivablePlanDto.getFkCompanyIds())) {
            if (!SecureUtil.validateCompanys(receivablePlanDto.getFkCompanyIds())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }
        Set<Long> ids = new HashSet<>();
        List<ReceivablePlanVo> receivablePlanVos = receivablePlanMapper.getReceivablePlansInfo(receivablePlanDto);
        ids = receivablePlanVos.stream().map(ReceivablePlanVo::getId).collect(Collectors.toSet());
        ids.removeIf(Objects::isNull);
        if (GeneralTool.isEmpty(ids)) {
            ids.add(0L);
        }
        LambdaQueryWrapper<ReceivablePlan> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(ids) && GeneralTool.isEmpty(receivablePlanDto.getFkStudentId())) {
            lambdaQueryWrapper.in(ReceivablePlan::getId, ids);
        }
        if (GeneralTool.isNotEmpty(receivablePlanDto.getFkStudentId()) && GeneralTool.isNotEmpty(receivablePlanDto.getFkTypeKey())) {
            //查询学习计划的应收计划
            lambdaQueryWrapper.eq(ReceivablePlan::getFkTypeKey, receivablePlanDto.getFkTypeKey());
//            List<Long> itemIds = getTypeTargetIds(receivablePlanDto);
//            criteria.andIn("fkTypeTargetId", itemIds);
        }

        if (GeneralTool.isNotEmpty(receivablePlanDto.getFkCompanyId())) {
            List<Long> planIds = new ArrayList<>();
            if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(receivablePlanDto.getFkTypeKey())) {
                planIds = receivablePlanMapper.getPlanIdByCompanyId(receivablePlanDto.getFkCompanyId(), receivablePlanDto.getFkStudentId());
            } else if (TableEnum.SALE_STUDENT_ACCOMMODATION.key.equals(receivablePlanDto.getFkTypeKey())) {
                planIds = receivablePlanMapper.getPlanIdByCompanyIdAndAccommodation(receivablePlanDto.getFkCompanyId(), receivablePlanDto.getFkStudentId());
            } else if (TableEnum.SALE_STUDENT_INSURANCE.key.equals(receivablePlanDto.getFkTypeKey())) {
                planIds = receivablePlanMapper.getPlanIdByCompanyIdAndInsurance(receivablePlanDto.getFkCompanyId(), receivablePlanDto.getFkStudentId());
            } else if (TableEnum.INSTITUTION_PROVIDER.key.equals(receivablePlanDto.getFkTypeKey()) || TableEnum.SALE_OTHER_INCOME.key.equals(receivablePlanDto.getFkTypeKey())) {
                List<Long> institutionProviderIds = institutionCenterClient.getInstitutionProviderIdsByCompanyId(receivablePlanDto.getFkCompanyId());
                planIds = receivablePlanMapper.getPlanIdByInstitutionProviderIds(receivablePlanDto.getFkTypeKey(), institutionProviderIds);
            } else {
                //默认学习计划
                List<Long> institutionProviderIds = institutionCenterClient.getInstitutionProviderIdsByCompanyId(receivablePlanDto.getFkCompanyId());
                planIds = receivablePlanMapper.getAllPlanIdByCompanyId(receivablePlanDto.getFkCompanyId(), receivablePlanDto.getFkStudentId(), institutionProviderIds);
            }
            if (GeneralTool.isEmpty(planIds)) {
                planIds = new ArrayList<>();
                planIds.add(0L);
            }
            lambdaQueryWrapper.in(ReceivablePlan::getId, planIds);
        }
        //公司多选情况
        if (GeneralTool.isNotEmpty(receivablePlanDto.getFkCompanyIds())) {
            List<Long> planIds = new ArrayList<>();
            if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(receivablePlanDto.getFkTypeKey())) {
                planIds = receivablePlanMapper.getPlanIdByCompanyIds(receivablePlanDto.getFkCompanyIds(), receivablePlanDto.getFkStudentId());
            } else if (TableEnum.SALE_STUDENT_ACCOMMODATION.key.equals(receivablePlanDto.getFkTypeKey())) {
                planIds = receivablePlanMapper.getPlanIdByCompanyIdsAndAccommodation(receivablePlanDto.getFkCompanyIds(), receivablePlanDto.getFkStudentId());
            } else if (TableEnum.SALE_STUDENT_INSURANCE.key.equals(receivablePlanDto.getFkTypeKey())) {
                planIds = receivablePlanMapper.getPlanIdByCompanyIdsAndInsurance(receivablePlanDto.getFkCompanyIds(), receivablePlanDto.getFkStudentId());
            } else if (TableEnum.INSTITUTION_PROVIDER.key.equals(receivablePlanDto.getFkTypeKey()) || TableEnum.SALE_OTHER_INCOME.key.equals(receivablePlanDto.getFkTypeKey())) {
                List<Long> institutionProviderIds = institutionCenterClient.getInstitutionProviderIdsByCompanyIds(receivablePlanDto.getFkCompanyIds());
                planIds = receivablePlanMapper.getPlanIdByInstitutionProviderIds(receivablePlanDto.getFkTypeKey(), institutionProviderIds);
            } else {
                //默认学习计划
                List<Long> institutionProviderIds = institutionCenterClient.getInstitutionProviderIdsByCompanyIds(receivablePlanDto.getFkCompanyIds());
                planIds = receivablePlanMapper.getAllPlanIdByCompanyIds(receivablePlanDto.getFkCompanyIds(), receivablePlanDto.getFkStudentId(), institutionProviderIds);
            }
            if (GeneralTool.isEmpty(planIds)) {
                planIds = new ArrayList<>();
                planIds.add(0L);
            }
            lambdaQueryWrapper.in(ReceivablePlan::getId, planIds);
        }
        if (GeneralTool.isNotEmpty(receivablePlanDto.getFkTypeKey())) {
            lambdaQueryWrapper.eq(ReceivablePlan::getFkTypeKey, receivablePlanDto.getFkTypeKey());
        }
        if (GeneralTool.isNotEmpty(receivablePlanDto.getOfferItemNum())) {
            List<Long> itemIds = offerItemService.getItemIdByNum(receivablePlanDto.getOfferItemNum());
            if (GeneralTool.isNotEmpty(itemIds)) {
                itemIds = new ArrayList<>();
                itemIds.add(0L);
            }
            lambdaQueryWrapper.eq(ReceivablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_OFFER_ITEM.key);
            lambdaQueryWrapper.in(ReceivablePlan::getFkTypeTargetId, itemIds);
        }
        if (GeneralTool.isNotEmpty(receivablePlanDto.getStudentName())) {
            List<Long> planIds = receivablePlanMapper.getPlanIdByStudentName(receivablePlanDto.getStudentName());
            if (GeneralTool.isEmpty(planIds)) {
                planIds = new ArrayList<>();
                planIds.add(0L);
            }
            lambdaQueryWrapper.in(ReceivablePlan::getId, planIds);
        }
        if (GeneralTool.isNotEmpty(receivablePlanDto.getInstitutionProviderName())) {
            List<Long> providerIds = institutionCenterClient.getInstitutionProviderIds(receivablePlanDto.getInstitutionProviderName());
            List<Long> planIds = receivablePlanMapper.getPlanIdByProviderId(providerIds);
            if (GeneralTool.isEmpty(planIds)) {
                planIds = new ArrayList<>();
            }
            lambdaQueryWrapper.in(ReceivablePlan::getId, planIds);
        }
        if (GeneralTool.isNotEmpty(receivablePlanDto.getInstitutionName())) {
            List<Long> institutionIds = institutionCenterClient.getInstitutionIds(receivablePlanDto.getInstitutionName()).getData();
            List<Long> planIds = receivablePlanMapper.getPlanIdByInstitutionId(institutionIds);
            if (GeneralTool.isEmpty(planIds)) {
                planIds = new ArrayList<>();
                planIds.add(0L);
            }
            lambdaQueryWrapper.in(ReceivablePlan::getId, planIds);
        }
        if (GeneralTool.isNotEmpty(receivablePlanDto.getCourseName())) {
            List<Long> courseIds = institutionCenterClient.getCourseIds(receivablePlanDto.getCourseName()).getData();
            List<Long> planIds = receivablePlanMapper.getPlanIdByCourseId(courseIds);
            if (GeneralTool.isEmpty(planIds)) {
                planIds = new ArrayList<>();
                planIds.add(0L);
            }
            lambdaQueryWrapper.in(ReceivablePlan::getId, planIds);
        }

        if (GeneralTool.isNotEmpty(receivablePlanDto.getFkinstitutionId())
                || GeneralTool.isNotEmpty(receivablePlanDto.getEnrollmentDateStart())
                || GeneralTool.isNotEmpty(receivablePlanDto.getEnrollmentDateEnd())) {
            List<Long> studentOfferItemIds = studentOfferItemMapper.getStudentOfferItemIdsByVo(receivablePlanDto);
            if (GeneralTool.isEmpty(studentOfferItemIds)) {
                studentOfferItemIds.add(0L);
            }
            List<Long> planIds = receivablePlanMapper.getPlanIdsByStudentOfferItemIds(GetStringUtils.getSqlString(studentOfferItemIds));
            if (GeneralTool.isEmpty(planIds)) {
                planIds = new ArrayList<>();
                planIds.add(0L);
            }
            lambdaQueryWrapper.in(ReceivablePlan::getId, planIds);
        }
        if (GeneralTool.isNotEmpty(receivablePlanDto.getBeginTime())) {
            lambdaQueryWrapper.ge(ReceivablePlan::getGmtCreate, receivablePlanDto.getBeginTime());
        }
        if (GeneralTool.isNotEmpty(receivablePlanDto.getEndTime())) {
            lambdaQueryWrapper.le(ReceivablePlan::getGmtCreate, receivablePlanDto.getEndTime());
        }
        if (GeneralTool.isNotEmpty(receivablePlanDto.getSummary())) {
            lambdaQueryWrapper.like(ReceivablePlan::getSummary, receivablePlanDto.getSummary());
        }
        if (GeneralTool.isNotEmpty(receivablePlanDto.getFkTypeTargetId())) {
            lambdaQueryWrapper.eq(ReceivablePlan::getFkTypeKey, receivablePlanDto.getFkTypeKey());
            lambdaQueryWrapper.eq(ReceivablePlan::getFkTypeTargetId, receivablePlanDto.getFkTypeTargetId());
        }
        lambdaQueryWrapper.isNotNull(ReceivablePlan::getFkTypeKey);
        lambdaQueryWrapper.orderByDesc(ReceivablePlan::getId);
        long fStartTime = System.currentTimeMillis();

        IPage<ReceivablePlan> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        IPage<ReceivablePlan> pageList = receivablePlanMapper.selectPage(iPage, lambdaQueryWrapper);
        List<ReceivablePlan> receivablePlans = pageList.getRecords();
        page.setAll((int) pageList.getTotal());
        if (GeneralTool.isEmpty(receivablePlans)) {
            return Lists.newArrayList();
        }
        long fEndTime = System.currentTimeMillis();

        Map<String, String> companyMap = getCompanyMap();
        //筛选出来之后
        Map<Long, BigDecimal> actualReceivableAmountMap = receivablePlanVos.stream().collect(Collectors.toMap(ReceivablePlanVo::getId, ReceivablePlanVo::getActualReceivableAmount));
        Map<Long, BigDecimal> diffReceivableAmountMap = receivablePlanVos.stream().collect(Collectors.toMap(ReceivablePlanVo::getId, ReceivablePlanVo::getDiffReceivableAmount));
        Map<Long, Integer> receiveStatusMap = receivablePlanVos.stream().collect(Collectors.toMap(ReceivablePlanVo::getId, ReceivablePlanVo::getReceiveStatus));


        List<ReceivablePlanVo> receivablePlanVoList =
                receivablePlans.stream().map(receivablePlan -> BeanCopyUtils.objClone(receivablePlan, ReceivablePlanVo::new)).collect(Collectors.toList());

        List<Long> itemIds = receivablePlans.stream().filter(receivablePlan -> TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(receivablePlan.getFkTypeKey()))
                .map(ReceivablePlan::getFkTypeTargetId).collect(Collectors.toList());
        if (GeneralTool.isEmpty(itemIds)) {
            itemIds = new ArrayList<>();
            itemIds.add(0L);
        }
        //提供商Map
        Map<Long, Long> providerIdMap = offerItemService.getProviderIdsByItemIds(itemIds);

        //学习计划
        List<StudentOfferItemVo> studentOfferItemVos = studentOfferItemMapper.getStudentOfferItemDtoListByIds(itemIds);
        Map<Long, Long> offerIdMap = new HashMap<>();
        Map<Long, Long> offerStudentIdMap = new HashMap<>();
        Map<Long, Boolean> isFollowMap = new HashMap<>();
        Map<Long, Long> parentItemIdMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(studentOfferItemVos)) {
            //学习方案id map
            offerIdMap = studentOfferItemVos.stream().collect(Collectors.toMap(StudentOfferItemVo::getId, StudentOfferItemVo::getFkStudentOfferId));
            //学生ID Map
            offerStudentIdMap = studentOfferItemVos.stream().filter(studentOfferItemDto -> GeneralTool.isNotEmpty(studentOfferItemDto.getFkStudentId()))
                    .collect(Collectors.toMap(StudentOfferItemVo::getId, StudentOfferItemVo::getFkStudentId));
            //isFollow状态值
            isFollowMap = studentOfferItemVos.stream().filter(studentOfferItemDto -> GeneralTool.isNotEmpty(studentOfferItemDto.getIsFollow()))
                    .collect(Collectors.toMap(StudentOfferItemVo::getId, StudentOfferItemVo::getIsFollow));
            //父计划id
            parentItemIdMap = studentOfferItemVos.stream().filter(studentOfferItemDto -> GeneralTool.isNotEmpty(studentOfferItemDto.getFkParentStudentOfferItemId()))
                    .collect(Collectors.toMap(StudentOfferItemVo::getId, StudentOfferItemVo::getFkParentStudentOfferItemId));

        }
        //学习计划公司ID Map
        Map<Long, Long> offerItemCompanyIdMap = new HashMap<>();
        offerItemCompanyIdMap = offerItemService.getCompanyIdsByItemIds(itemIds);


        //留学住宿
        List<Long> accommodationIds = receivablePlans.stream()
                .filter(receivablePlan -> TableEnum.SALE_STUDENT_ACCOMMODATION.key.equals(receivablePlan.getFkTypeKey()))
                .map(ReceivablePlan::getFkTypeTargetId).collect(Collectors.toList());
        if (GeneralTool.isEmpty(accommodationIds)) {
            accommodationIds = new ArrayList<>();
            accommodationIds.add(0L);
        }
        List<StudentAccommodationVo> studentAccommodationVos = studentAccommodationMapper.getCompanyIdsByAccommodationIds(accommodationIds);
        Map<Long, Long> accommodationCompanyIdMap = new HashMap<>();
        Map<Long, Long> accommodationStudentIdMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(studentAccommodationVos)) {
            //留学住宿公司Id
            accommodationCompanyIdMap = studentAccommodationVos.stream().filter(s -> s.getFkCompanyId() != null)
                    .collect(Collectors.toMap(StudentAccommodationVo::getId, StudentAccommodationVo::getFkCompanyId));
            //留学住宿学生Id
            accommodationStudentIdMap = studentAccommodationVos.stream().filter(s -> s.getFkStudentId() != null)
                    .collect(Collectors.toMap(StudentAccommodationVo::getId, StudentAccommodationVo::getFkStudentId));
        }


        //留学保险
        List<Long> insuranceIds = receivablePlans.stream()
                .filter(receivablePlan -> TableEnum.SALE_STUDENT_INSURANCE.key.equals(receivablePlan.getFkTypeKey()))
                .map(ReceivablePlan::getFkTypeTargetId).collect(Collectors.toList());
        if (GeneralTool.isEmpty(insuranceIds)) {
            insuranceIds = new ArrayList<>();
            insuranceIds.add(0L);
        }
        List<StudentInsuranceVo> studentInsuranceVos = studentInsuranceMapper.getCompanyIdsByInsuranceIds(insuranceIds);
        Map<Long, Long> insuranceCompanyIdMap = new HashMap<>();
        Map<Long, Long> insuranceStudentIdMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(studentInsuranceVos)) {
            //留学保险公司Id
            insuranceCompanyIdMap = studentInsuranceVos.stream().filter(s -> s.getFkCompanyId() != null)
                    .collect(Collectors.toMap(StudentInsuranceVo::getId, StudentInsuranceVo::getFkCompanyId));
            //留学住宿学生Id
            insuranceStudentIdMap = studentInsuranceVos.stream().filter(s -> s.getFkStudentId() != null)
                    .collect(Collectors.toMap(StudentInsuranceVo::getId, StudentInsuranceVo::getFkStudentId));
        }

        //提供商公司id map
        Map<Long, LinkedList<Long>> providerCompanyIdMap = new HashMap<>();
        Set<Long> providerIds = receivablePlans.stream().filter(receivablePlan -> {
            if (GeneralTool.isNotEmpty(receivablePlan.getFkTypeKey())) {
                if (TableEnum.INSTITUTION_PROVIDER.key.equals(receivablePlan.getFkTypeKey()) || TableEnum.SALE_OTHER_INCOME.key.equals(receivablePlan.getFkTypeKey())) {
                    return true;
                } else {
                    return false;
                }
            }
            return false;
        }).map(ReceivablePlan::getFkTypeTargetId).collect(Collectors.toSet());
        if (GeneralTool.isEmpty(providerIds)) {
            providerIds.add(0L);
        }
        providerCompanyIdMap = institutionCenterClient.getCompanyIdsByProviderIds(providerIds).getData();

        Map<Long, String> institutionProviderSelectNamesByIds = institutionCenterClient.getInstitutionProviderSelectNamesByIds(providerIds);

        for (ReceivablePlanVo receivablePlanVo : receivablePlanVoList) {
            receivablePlanVo.setActualReceivableAmount(actualReceivableAmountMap.get(receivablePlanVo.getId()));
            receivablePlanVo.setDiffReceivableAmount(diffReceivableAmountMap.get(receivablePlanVo.getId()));
            receivablePlanVo.setReceiveStatus(receiveStatusMap.get(receivablePlanVo.getId()));
            //申请计划提供商
            if (GeneralTool.isNotEmpty(providerIdMap) && receivablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_OFFER_ITEM.key)) {
                receivablePlanVo.setFkInstitutionProviderId(providerIdMap.get(receivablePlanVo.getFkTypeTargetId()));
            }
            //公司ID
            if (GeneralTool.isNotEmpty(offerItemCompanyIdMap) && receivablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_OFFER_ITEM.key)) {
                receivablePlanVo.setFkCompanyId(offerItemCompanyIdMap.get(receivablePlanVo.getFkTypeTargetId()));
            }
            if (GeneralTool.isNotEmpty(accommodationCompanyIdMap) && receivablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_ACCOMMODATION.key)) {
                receivablePlanVo.setFkCompanyId(accommodationCompanyIdMap.get(receivablePlanVo.getFkTypeTargetId()));
            }
            if (GeneralTool.isNotEmpty(insuranceCompanyIdMap) && receivablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_INSURANCE.key) && GeneralTool.isEmpty(receivablePlanVo.getFkCompanyId())) {
                receivablePlanVo.setFkCompanyId(insuranceCompanyIdMap.get(receivablePlanVo.getFkTypeTargetId()));
            }
            if (GeneralTool.isNotEmpty(providerCompanyIdMap) && (TableEnum.INSTITUTION_PROVIDER.key.equals(receivablePlanVo.getFkTypeKey()) || TableEnum.SALE_OTHER_INCOME.key.equals(receivablePlanVo.getFkTypeKey()))) {
                LinkedList<Long> companyIds = providerCompanyIdMap.get(receivablePlanVo.getFkTypeTargetId());
                if (GeneralTool.isNotEmpty(companyIds)) {
                    StringJoiner sj = new StringJoiner(",");
                    for (Long companyId : companyIds) {
                        sj.add(companyMap.get(companyId.toString()));
                    }
                    receivablePlanVo.setCompanyName(sj.toString());
                }
//                receivablePlanVo.setFkCompanyId(insuranceCompanyIdMap.get(receivablePlanVo.getFkTypeTargetId()));
            }

            //申请方案Id
            if (GeneralTool.isNotEmpty(offerIdMap) && receivablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_OFFER_ITEM.key)) {
                receivablePlanVo.setFkStudentOfferId(offerIdMap.get(receivablePlanVo.getFkTypeTargetId()));
            }
            //学生ID
            if (GeneralTool.isNotEmpty(offerStudentIdMap) && receivablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_OFFER_ITEM.key)) {
                receivablePlanVo.setFkStudentId(offerStudentIdMap.get(receivablePlanVo.getFkTypeTargetId()));
            }
            //isFollow是否后续课程状态值
            if (GeneralTool.isNotEmpty(isFollowMap) && receivablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_OFFER_ITEM.key)) {
                receivablePlanVo.setIsFollow(isFollowMap.get(receivablePlanVo.getFkTypeTargetId()));
            }
            //父学习计划id
            if (GeneralTool.isNotEmpty(parentItemIdMap) && receivablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_OFFER_ITEM.key)) {
                receivablePlanVo.setFkParentStudentOfferItemId(parentItemIdMap.get(receivablePlanVo.getFkTypeTargetId()));
            }
            if (GeneralTool.isNotEmpty(accommodationStudentIdMap) && receivablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_ACCOMMODATION.key)) {
                receivablePlanVo.setFkStudentId(accommodationStudentIdMap.get(receivablePlanVo.getFkTypeTargetId()));
            }
            if (GeneralTool.isNotEmpty(insuranceStudentIdMap) && receivablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_INSURANCE.key)) {
                receivablePlanVo.setFkStudentId(insuranceStudentIdMap.get(receivablePlanVo.getFkTypeTargetId()));
            }
        }
        //TODO 改过
        //List<ReceivablePlanVo> collect = receivablePlanVoList.stream().filter(receivablePlanDto -> Objects.nonNull(receivablePlanDto.getReceiveStatus())).collect(Collectors.toList());
        List<ReceivablePlanVo> collect = receivablePlanVoList.stream().filter(receivablePlanVo -> Objects.nonNull(receivablePlanDto.getReceiveStatus())).collect(Collectors.toList());


        //发票编号
        Set<Long> planIds = collect.stream().map(ReceivablePlanVo::getId).collect(Collectors.toSet());
        Map<Long, String> invoiceNumMap = financeCenterClient.getFkInvoiceNum(planIds);
        setListName(receivablePlanVos);
        for (ReceivablePlanVo planDto : receivablePlanVos) {
            planDto.setFkInvoiceNum(invoiceNumMap.get(planDto.getId()));
        }
        long endTime = System.currentTimeMillis();

        if (GeneralTool.isNotEmpty(times)) {
            times[0] = String.valueOf((fEndTime - fStartTime));
            times[1] = String.valueOf((endTime - startTime) - (fEndTime - fStartTime));
        }
        return collect;
    }

    /**
     * Author Cream
     * Description : //批量保存应收计划
     * Date 2023/9/8 14:41
     * Params:
     * Return
     */
    @Override
    public void saveBatch(List<ReceivablePlan> receivablePlanList) {
        if (GeneralTool.isNotEmpty(receivablePlanList)) {
            saveBatch(receivablePlanList,receivablePlanList.size());
        }
    }

    private Map<Long, Long> getCompanyIdsByAccommodationIds(List<Long> accommodationIds) {

        return null;
    }


    /**
     * @Description: 提供商应收汇总统计明细
     * @Author: Jerry
     * @Date:11:39 2021/11/22
     */
    @Override
    public List<ReceivablePlanVo> institutionProviderReceivableSumDetail(InstitutionProviderReceivableSumDetailDto institutionProviderReceivableSumDetailDto,
                                                                         Page page) {
        IPage<ReceivablePlanVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<ReceivablePlanVo> collect = receivablePlanMapper.institutionProviderReceivableSumDetail(iPage, institutionProviderReceivableSumDetailDto);
        page.setAll((int) iPage.getTotal());

        if (GeneralTool.isEmpty(collect)) {
            return Collections.emptyList();
        }
        //应收类型对应记录ids
        Set<Long> fkTypeTargetIds = collect.stream().map(ReceivablePlanVo::getFkTypeTargetId).collect(Collectors.toSet());
        Map<Long, Object> convert = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkTypeTargetIds)) {
            List<SelItem> stepByIds = studentOfferItemMapper.getStepByIds(fkTypeTargetIds);
            convert = ConvertUtils.convert(stepByIds);
        }
        List<Long> ids = collect.stream().map(ReceivablePlanVo::getId).collect(Collectors.toList());
        ReceivablePlanDto receivablePlanDto = new ReceivablePlanDto();
        receivablePlanDto.setIds(ids);
        List<ReceivablePlanVo> receivablePlansInfo = receivablePlanMapper.getReceivablePlansInfo(receivablePlanDto);

        Map<Long, BigDecimal> actualReceivableAmountMap = receivablePlansInfo.stream().collect(Collectors.toMap(ReceivablePlanVo::getId, ReceivablePlanVo::getActualReceivableAmount));
        Map<Long, BigDecimal> diffReceivableAmountMap = receivablePlansInfo.stream().collect(Collectors.toMap(ReceivablePlanVo::getId, ReceivablePlanVo::getDiffReceivableAmount));
        //发票编码
        Set<Long> planIds = collect.stream().map(ReceivablePlanVo::getId).collect(Collectors.toSet());
        Map<Long, String> invoiceNumMap = financeCenterClient.getFkInvoiceNum(planIds);

        setListName(collect);
        for (ReceivablePlanVo receivablePlanVo : collect) {
            if (GeneralTool.isNotEmpty(receivablePlanVo)) {

                receivablePlanVo.setActualReceivableAmount(actualReceivableAmountMap.get(receivablePlanVo.getId()));
                receivablePlanVo.setDiffReceivableAmount(diffReceivableAmountMap.get(receivablePlanVo.getId()));
            }
            receivablePlanVo.setFkInvoiceNum(invoiceNumMap.get(receivablePlanVo.getId()));

            if (GeneralTool.isNotEmpty(convert) && convert.containsKey(receivablePlanVo.getFkTypeTargetId())) {
                receivablePlanVo.setStepName((String) convert.get(receivablePlanVo.getFkTypeTargetId()));
            }
        }
        return collect;
    }

    @Override
    public List<ReceivablePlanVo> studentReceivableSumDetail(StudentReceivableSumDetailDto studentReceivableSumDetailDto, Page page) {
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<ReceivablePlanVo> collect = receivablePlanMapper.studentReceivableSumDetail(studentReceivableSumDetailDto);
//        page.restPage(collect);

        IPage<ReceivablePlanVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<ReceivablePlanVo> collect = receivablePlanMapper.studentReceivableSumDetail(iPage, studentReceivableSumDetailDto);
        page.setAll((int) iPage.getTotal());

        if (GeneralTool.isEmpty(collect)) {
            return Collections.emptyList();
        }
        List<Long> ids = collect.stream().map(ReceivablePlanVo::getId).collect(Collectors.toList());
        ReceivablePlanDto receivablePlanDto = new ReceivablePlanDto();
        receivablePlanDto.setIds(ids);
        List<ReceivablePlanVo> receivablePlansInfo = receivablePlanMapper.getReceivablePlansInfo(receivablePlanDto);

        Map<Long, BigDecimal> actualReceivableAmountMap = receivablePlansInfo.stream().collect(Collectors.toMap(ReceivablePlanVo::getId, ReceivablePlanVo::getActualReceivableAmount));
        Map<Long, BigDecimal> diffReceivableAmountMap = receivablePlansInfo.stream().collect(Collectors.toMap(ReceivablePlanVo::getId, ReceivablePlanVo::getDiffReceivableAmount));

        //发票编号
        Set<Long> planIds = collect.stream().map(ReceivablePlanVo::getId).collect(Collectors.toSet());
        Map<Long, String> fkInvoiceNumMap = financeCenterClient.getFkInvoiceNum(planIds);
        Set<Long> itemIds = collect.stream().filter(f -> TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(f.getFkTypeKey())).map(ReceivablePlanVo::getFkTypeTargetId).collect(Collectors.toSet());
        if (GeneralTool.isEmpty(ids)){
            ids.add(0L);
        }
        List<SelItem> stepByIds = studentOfferItemMapper.getStepByIds(itemIds);
        Map<Long, Object> convert = ConvertUtils.convert(stepByIds);
        //获取备注 copy应收计划列表的
        List<PlanRemarkDetailResultVo> planRemarkDetailResultVos = receivablePlanMapper.getReceivablePlanRemarkDetails(planIds);
        Map<Long, List<SaleComment>> studentCommentMap = Maps.newHashMap();
        Map<Long, List<SaleComment>> studentOfferItemCommentMap = Maps.newHashMap();
        Map<Long, PlanRemarkDetailResultVo> planRemarkDetailResultDtoMap = Maps.newHashMap();
        if(GeneralTool.isNotEmpty(planRemarkDetailResultVos)){
            planRemarkDetailResultDtoMap = planRemarkDetailResultVos.stream().collect(HashMap::new, (m, v) -> m.put(v.getId(), v), HashMap::putAll);
            Set<Long> studentIds = planRemarkDetailResultVos.stream().map(PlanRemarkDetailResultVo::getFkStudentId).filter(Objects::nonNull).collect(Collectors.toSet());
            if (GeneralTool.isEmpty(studentIds)){
                studentIds.add(0L);
            }
            Set<Long> studentOfferItemIds = planRemarkDetailResultVos.stream().map(PlanRemarkDetailResultVo::getFkStudentOfferItemId).filter(Objects::nonNull).collect(Collectors.toSet());
            if (GeneralTool.isEmpty(studentOfferItemIds)){
                studentOfferItemIds.add(0L);
            }
            List<SaleComment> studentComments = commentService.list(Wrappers.<SaleComment>lambdaQuery()
                    .in(SaleComment::getFkTableId, studentIds)
                    .eq(SaleComment::getFkTableName, TableEnum.SALE_STUDENT.key));
            List<SaleComment> studentOfferItemComments = commentService.list(Wrappers.<SaleComment>lambdaQuery()
                    .in(SaleComment::getFkTableId, studentOfferItemIds)
                    .eq(SaleComment::getFkTableName, TableEnum.SALE_STUDENT_OFFER_ITEM.key));

            if (GeneralTool.isNotEmpty(studentComments)){
                studentCommentMap = studentComments.stream().collect(Collectors.groupingBy(SaleComment::getFkTableId));
            }
            if (GeneralTool.isNotEmpty(studentOfferItemComments)){
                studentOfferItemCommentMap = studentOfferItemComments.stream().collect(Collectors.groupingBy(SaleComment::getFkTableId));
            }
        }
        setListName(collect);
        for (ReceivablePlanVo receivablePlanVo : collect) {
            if (GeneralTool.isNotEmpty(receivablePlanVo)) {

                BigDecimal actualReceivableAmout = actualReceivableAmountMap.get(receivablePlanVo.getId());
                if (actualReceivableAmout != null) {
                    receivablePlanVo.setActualReceivableAmount(actualReceivableAmout.abs());
                }
                BigDecimal diffReceivableAmount = diffReceivableAmountMap.get(receivablePlanVo.getId());
                if (diffReceivableAmount != null) {
                    receivablePlanVo.setDiffReceivableAmount(diffReceivableAmount.abs());
                }
                if (convert.containsKey(receivablePlanVo.getFkTypeTargetId())) {
                    receivablePlanVo.setStepName((String) convert.get(receivablePlanVo.getFkTypeTargetId()));
                }

                receivablePlanVo.setFkInvoiceNum(fkInvoiceNumMap.get(receivablePlanVo.getId()));
                if (GeneralTool.isNotEmpty(planRemarkDetailResultDtoMap)) {
                    PlanRemarkDetailResultVo planRemarkDetailResultVo = planRemarkDetailResultDtoMap.get(receivablePlanVo.getId());
                    PlanRemarkDetailVo planRemarkDetailVo = BeanCopyUtils.objClone(planRemarkDetailResultVo, PlanRemarkDetailVo::new);
                    if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(receivablePlanVo.getFkTypeKey())) {
                        assert planRemarkDetailVo != null;
                        if (GeneralTool.isNotEmpty(studentCommentMap)
                                && GeneralTool.isNotEmpty(studentCommentMap.get(planRemarkDetailResultVo.getFkStudentId()))) {
                            List<SaleComment> saleComments = studentCommentMap.get(planRemarkDetailResultVo.getFkStudentId());
                            List<String> studentCommentList = saleComments.stream().map(SaleComment::getComment).collect(Collectors.toList());
                            planRemarkDetailVo.setStudentComments(studentCommentList);
                        }
                        if (GeneralTool.isNotEmpty(studentOfferItemCommentMap)
                                && GeneralTool.isNotEmpty(studentOfferItemCommentMap.get(planRemarkDetailResultVo.getFkStudentOfferItemId()))) {
                            List<SaleComment> saleComments = studentOfferItemCommentMap.get(planRemarkDetailResultVo.getFkStudentOfferItemId());
                            List<String> studentOfferItemCommentList = saleComments.stream().map(SaleComment::getComment).collect(Collectors.toList());
                            planRemarkDetailVo.setStudentOfferItemComments(studentOfferItemCommentList);
                        }
                        if (GeneralTool.isNotEmpty(planRemarkDetailResultVo.getEducationProject())) {
                            planRemarkDetailVo.setEducationProjectName(ProjectExtraEnum.getValueByKey(planRemarkDetailResultVo.getEducationProject(), ProjectExtraEnum.EDUCATION_PROJECT));
                        }
                        if (GeneralTool.isNotEmpty(planRemarkDetailResultVo.getEducationDegree())) {
                            planRemarkDetailVo.setEducationDegreeName(ProjectExtraEnum.getValueByKey(planRemarkDetailResultVo.getEducationDegree(), ProjectExtraEnum.EDUCATION_DEGREE));
                        }
                    }

                    receivablePlanVo.setPlanRemarkDetailDto(planRemarkDetailVo);
                    if (GeneralTool.isEmpty(planRemarkDetailVo)
                            || (GeneralTool.isNotEmpty(planRemarkDetailVo) && MyStringUtils.isAllFieldNull(planRemarkDetailVo))) {
                        receivablePlanVo.setPlanRemarkDetailDto(null);
                    }
                }
            }
        }
        return collect;
    }

    //BD奖金=1
    private static final int BD_BONUS = 1;

    @Override
    public Long addReceivablePlan(ReceivablePlanDto receivablePlanDto) {
        if (GeneralTool.isEmpty(receivablePlanDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
//        if (receivablePlanDto.getReceivableAmount().compareTo(new BigDecimal(0)) == 0) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("RECEIVABLE_AMOUNT_NOT_ZERO"));
//        }
        ReceivablePlan receivablePlan = BeanCopyUtils.objClone(receivablePlanDto, ReceivablePlan::new);
        receivablePlan.setStatus(1);
        utilService.setCreateInfo(receivablePlan);
        receivablePlanMapper.insert(receivablePlan);
        Date receivablePlanDate = receivablePlan.getReceivablePlanDate();
        if (GeneralTool.isNotEmpty(receivablePlanDate)) {
            ReceivablePlanDate planDate = new ReceivablePlanDate();
            planDate.setFkReceivablePlanId(receivablePlan.getId());
            planDate.setReceivablePlanDate(receivablePlanDate);
            utilService.setCreateInfo(planDate);
            receivablePlanDateMapper.insert(planDate);
        }
        if (receivablePlanDto.getFlag()){
            StudentOfferItem studentOfferItem = studentOfferItemMapper.selectById(receivablePlan.getFkTypeTargetId());
            studentOfferItem.setTuitionAmount(receivablePlan.getTuitionAmount());
            utilService.updateUserInfoToEntity(studentOfferItem);
            studentOfferItemMapper.updateById(studentOfferItem);
        }
        if(!("").equals(receivablePlanDto.getBonusCommissionRate()) || receivablePlanDto.getBonusFixedAmount().compareTo(BigDecimal.ZERO) !=0){
            ReceivablePlanBonusSetting receivablePlanBonus = new ReceivablePlanBonusSetting();
            receivablePlanBonus.setFkReceivablePlanId(receivablePlan.getId());
            receivablePlanBonus.setType(BD_BONUS);
            receivablePlanBonus.setBonusCommissionRate(receivablePlanDto.getBonusCommissionRate());
            receivablePlanBonus.setBonusFixedAmount(receivablePlanDto.getBonusFixedAmount());
            utilService.setCreateInfo(receivablePlanBonus);
            receivablePlanBonusSettingMapper.insert(receivablePlanBonus);
        }
        return receivablePlan.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReceivablePlanVo updateReceivablePlan(ReceivablePlanDto receivablePlanDto) {
        if (GeneralTool.isEmpty(receivablePlanDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }


        UpdateWrapper<ReceivablePlan> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(ReceivablePlan::getId, receivablePlanDto.getId())
                .set(GeneralTool.isEmpty(receivablePlanDto.getTuitionAmount()), ReceivablePlan::getTuitionAmount, null)
                .set(GeneralTool.isEmpty(receivablePlanDto.getCommissionRate()), ReceivablePlan::getCommissionRate, null)
                .set(GeneralTool.isEmpty(receivablePlanDto.getNetRate()), ReceivablePlan::getNetRate, null)
                .set(GeneralTool.isEmpty(receivablePlanDto.getCommissionAmount()), ReceivablePlan::getCommissionAmount, null)
                .set(GeneralTool.isEmpty(receivablePlanDto.getFixedAmount()), ReceivablePlan::getFixedAmount, null)
                .set(GeneralTool.isEmpty(receivablePlanDto.getBonusType()), ReceivablePlan::getBonusType, null)
                .set(GeneralTool.isEmpty(receivablePlanDto.getBonusAmount()), ReceivablePlan::getBonusAmount, null)
                .set(GeneralTool.isEmpty(receivablePlanDto.getReceivablePlanDate()), ReceivablePlan::getReceivablePlanDate, null)
                .set(GeneralTool.isEmpty(receivablePlanDto.getFkReceivableReasonId()), ReceivablePlan::getFkReceivableReasonId, null);

        ReceivablePlan receivablePlan = receivablePlanMapper.selectById(receivablePlanDto.getId());
        BeanCopyUtils.copyProperties(receivablePlanDto, receivablePlan);
        if((!("").equals(receivablePlanDto.getBonusCommissionRate()) && receivablePlanDto.getBonusCommissionRate()!=null) || GeneralTool.isNotEmpty(receivablePlanDto.getBonusFixedAmount()) ){
            ReceivablePlanBonusSetting receivablePlanBonusSetting = receivablePlanBonusSettingMapper.selectOne(
                    new QueryWrapper<ReceivablePlanBonusSetting>().lambda()
                            .eq(ReceivablePlanBonusSetting::getFkReceivablePlanId, receivablePlan.getId())
            );
            if (GeneralTool.isEmpty(receivablePlanBonusSetting)){
                receivablePlanBonusSetting = new ReceivablePlanBonusSetting();
                receivablePlanBonusSetting.setFkReceivablePlanId(receivablePlan.getId());
                if (GeneralTool.isNotEmpty(receivablePlanDto.getBonusFixedAmount()) && receivablePlanDto.getBonusFixedAmount().compareTo(BigDecimal.ZERO) > 0){
                    receivablePlanBonusSetting.setBonusFixedAmount(receivablePlanDto.getBonusFixedAmount());
                }

                if (GeneralTool.isNotEmpty(receivablePlanDto.getBonusCommissionRate()) && receivablePlanDto.getBonusCommissionRate().compareTo(BigDecimal.ZERO) > 0){
                    receivablePlanBonusSetting.setBonusCommissionRate(receivablePlanDto.getBonusCommissionRate());
                }
                receivablePlanBonusSetting.setType(BD_BONUS);
                utilService.updateUserInfoToEntity(receivablePlanBonusSetting);
                receivablePlanBonusSettingMapper.insert(receivablePlanBonusSetting);

            }else {
                ReceivablePlanBonusSetting receivablePlanBonus = new ReceivablePlanBonusSetting();
                receivablePlanBonus.setFkReceivablePlanId(receivablePlan.getId());
                if(GeneralTool.isNotEmpty(receivablePlanBonusSetting)){
                    receivablePlanBonus.setId(receivablePlanBonusSetting.getId());
                }

                if (GeneralTool.isEmpty(receivablePlanBonusSetting.getBonusCommissionRate())){
                    if (GeneralTool.isEmpty(receivablePlanDto.getBonusCommissionRate())){
                        receivablePlanBonus.setBonusCommissionRate(null);
                        receivablePlanBonus.setBonusFixedAmount(receivablePlanDto.getBonusFixedAmount());
                    }

                }
                if (GeneralTool.isNotEmpty(receivablePlanBonusSetting.getBonusFixedAmount())){
                    if (GeneralTool.isEmpty(receivablePlanDto.getBonusFixedAmount())){
                        receivablePlanBonus.setBonusCommissionRate(receivablePlanDto.getBonusCommissionRate());
                        receivablePlanBonus.setBonusFixedAmount(null);
                    }
                }
                utilService.setUpdateInfo(receivablePlanBonus);

                UpdateWrapper<ReceivablePlanBonusSetting> updateBounsWrapper = new UpdateWrapper<ReceivablePlanBonusSetting>();
                updateBounsWrapper.lambda()
                        .eq(ReceivablePlanBonusSetting::getFkReceivablePlanId, receivablePlanDto.getId())
                        .set(ReceivablePlanBonusSetting::getBonusCommissionRate, receivablePlanBonus.getBonusCommissionRate())
                        .set(ReceivablePlanBonusSetting::getBonusFixedAmount, receivablePlanBonus.getBonusFixedAmount())
                        .set(ReceivablePlanBonusSetting::getGmtModified, receivablePlanBonus.getGmtModified())
                        .set(ReceivablePlanBonusSetting::getGmtModifiedUser, receivablePlanBonus.getGmtModifiedUser());

                receivablePlanBonusSettingMapper.update(receivablePlanBonus, updateBounsWrapper);

//                receivablePlanBonusSettingMapper.updateById(receivablePlanBonus);

            }

        } else if (GeneralTool.isEmpty(receivablePlanDto.getBonusCommissionRate()) && GeneralTool.isEmpty(receivablePlanDto.getBonusFixedAmount())){
            receivablePlanBonusSettingMapper.delete(new LambdaQueryWrapper<ReceivablePlanBonusSetting>().eq(ReceivablePlanBonusSetting::getFkReceivablePlanId, receivablePlanDto.getId()));
        }
        if (receivablePlanDto.getSyn() != null && receivablePlanDto.getSyn()) {
            BigDecimal newT = receivablePlanDto.getTuitionAmount();
            receivablePlan.setTuitionAmount(newT);
            PayablePlan plan = payablePlanMapper.getPayablePlanByReId(receivablePlan.getId());
            if (plan == null) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_payable_plan_found"));
            }
            plan.setTuitionAmount(newT);
            if (plan.getFixedAmount() == null || plan.getFixedAmount().compareTo(BigDecimal.ZERO) == 0) {
                BigDecimal commissionRate = plan.getCommissionRate() == null ? BigDecimal.ZERO : plan.getCommissionRate();
                BigDecimal splitRate = plan.getSplitRate() == null ? BigDecimal.ZERO : plan.getSplitRate();
                BigDecimal finalAmount = newT.multiply(commissionRate.divide(BigDecimal.valueOf(100))).multiply(splitRate.divide(BigDecimal.valueOf(100)));
                plan.setCommissionAmount(finalAmount);
                plan.setPayableAmount(finalAmount);
            }
            utilService.updateUserInfoToEntity(plan);
            payablePlanMapper.updateById(plan);
        }
        utilService.updateUserInfoToEntity(receivablePlan);
        receivablePlanMapper.update(receivablePlan, updateWrapper);

        PayablePlan payablePlan = payablePlanMapper.selectOne(Wrappers.<PayablePlan>lambdaQuery().eq(PayablePlan::getFkReceivablePlanId, receivablePlanDto.getId()));

        //应付计划为学校提供商类型或者业务提供商类型,不用生成给代理的佣金
        if (GeneralTool.isNotEmpty(payablePlan) && !payablePlan.getFkTypeKey().equals(TableEnum.INSTITUTION_PROVIDER.key) && !payablePlan.getFkTypeKey().equals(TableEnum.SALE_BUSINESS_PROVIDER.key)) {
            //佣金结算更新
            SettlementInstallmentUpdateDto settlementInstallmentUpdateDto = new SettlementInstallmentUpdateDto();
            settlementInstallmentUpdateDto.setFkCompanyId(payablePlan.getFkCompanyId());
            settlementInstallmentUpdateDto.setFkPayablePlanId(payablePlan.getId());
            settlementInstallmentUpdateDto.setFkReceivablePlanId(receivablePlan.getId());
            settlementInstallmentUpdateDto.setReceivableAmount(receivablePlan.getReceivableAmount());
            settlementInstallmentUpdateDto.setPayableAmount(payablePlan.getPayableAmount());
            settlementInstallmentUpdateDto.setReceivableCurrencyTypeNum(receivablePlan.getFkCurrencyTypeNum());
            settlementInstallmentUpdateDto.setPayableCurrencyTypeNum(payablePlan.getFkCurrencyTypeNum());
            Result result = financeCenterClient.settlementInstallmentUpdate(settlementInstallmentUpdateDto);
            if (!result.isSuccess()) {
                throw new GetServiceException(result.getMessage());
            }
        }

//        PayablePlan payablePlan = payablePlanMapper.selectOne(Wrappers.<PayablePlan>lambdaQuery().eq(PayablePlan::getFkReceivablePlanId, receivablePlanDto.getId()));
//        if (GeneralTool.isNotEmpty(payablePlan)) {
//            //查看是否有未结算的分期数据，重新生成  预付的不管了 自己取消
//            List<PayablePlanSettlementInstallment> payablePlanSettlementInstallments = payablePlanSettlementInstallmentMapper.selectList(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
//                    .eq(PayablePlanSettlementInstallment::getFkPayablePlanId, payablePlan.getId())
//                    .eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_UNTREATED.key)
//                    .isNotNull(PayablePlanSettlementInstallment::getFkReceiptFormItemId));
//            if (GeneralTool.isNotEmpty(payablePlanSettlementInstallments)) {
//                if (payablePlan.getIsPayInAdvance()) {
//                    //预付的重新call一下判断，绑定的收款单是否已经大于修改后的应收金额  收齐了的话就生成分期数据
//                    //有预付的情况：根据应收计划id判断收款状态 生成分期数据
//                    payablePlanSettlementInstallmentMapper.delete(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery().eq(PayablePlanSettlementInstallment::getFkPayablePlanId, payablePlan.getId())
//                            .eq(PayablePlanSettlementInstallment::getStatus, 0).isNotNull(PayablePlanSettlementInstallment::getFkReceiptFormItemId));
//                    Result result = financeCenterClient.isPayInAdvanceInsertSettlementInstallment(payablePlan.getFkReceivablePlanId());
//                    if (!result.isSuccess()) {
//                        throw new GetServiceException(result.getMessage());
//                    }
//                } else {
//                    Set<Long> receiptFormItemIds = payablePlanSettlementInstallments.stream().map(PayablePlanSettlementInstallment::getFkReceiptFormItemId).collect(Collectors.toSet());
//                    List<ReceiptFormItemDto> receiptFormItemList = financeCenterClient.getReceiptFormItemListFeignByFormItemIds(receiptFormItemIds);
//                    for (ReceiptFormItemDto receiptFormItemDto : receiptFormItemList) {
//                        //旧数据不进入结算
//                        if ("admin-1".equals(receiptFormItemDto.getGmtCreateUser())) {
//                            continue;
//                        }
//                        SaleReceiptFormItemVo saleReceiptFormItemDto = new SaleReceiptFormItemVo();
//                        BeanCopyUtils.copyProperties(receiptFormItemDto, saleReceiptFormItemDto);
//                        payablePlanService.insertSettlementInstallmentByReceiptFormItem(saleReceiptFormItemDto);
//                    }
//                }
//            }
//        }
        return findReceivablePlan(receivablePlan.getId());
    }

    /**
     * Author Cream
     * Description : //获取应收计划收款时间列表
     * Date 2023/7/26 15:49
     * Params:
     * Return
     */
    @Override
    public List<ReceivablePlanDateVo> getReceivablePlanDateList(Long receivablePlanId) {
        List<ReceivablePlanDate> receivablePlanDates = receivablePlanDateMapper.selectList(Wrappers.<ReceivablePlanDate>lambdaQuery().eq(ReceivablePlanDate::getFkReceivablePlanId, receivablePlanId)
                .orderByAsc(ReceivablePlanDate::getReceivablePlanDate));
        return BeanCopyUtils.copyListProperties(receivablePlanDates, ReceivablePlanDateVo::new);
    }

    /**
     * Author Cream
     * Description : //添加应收计划收款时间
     * Date 2023/7/26 15:40
     * Params:
     * Return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SaveResponseBo addReceivablePlanDate(Long receivablePlanId, String receivablePlanDate) {
        ReceivablePlanDate planDate = new ReceivablePlanDate();
        planDate.setFkReceivablePlanId(receivablePlanId);
        Date date = DateUtil.parse(receivablePlanDate, "yyyy-MM-dd");
        planDate.setReceivablePlanDate(date);
        utilService.setCreateInfo(planDate);
        receivablePlanDateMapper.insert(planDate);
        ReceivablePlan receivablePlan = receivablePlanMapper.selectById(receivablePlanId);
        if (GeneralTool.isNotEmpty(receivablePlan)) {
            Date planReceivablePlanDate = receivablePlan.getReceivablePlanDate();
            if (GeneralTool.isNotEmpty(planReceivablePlanDate)) {
                if (date.compareTo(planReceivablePlanDate)<0) {
                    receivablePlan.setReceivablePlanDate(date);
                }
            }else {
                receivablePlan.setReceivablePlanDate(date);
            }
            utilService.setUpdateInfo(receivablePlan);
            receivablePlanMapper.updateById(receivablePlan);
        }
        return SaveResponseBo.ok(planDate.getId());
    }


    /**
     * 批量添加应收计划收款时间
     *
     * @Date 16:54 2023/11/13
     * <AUTHOR>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAddReceivablePlanDate(List<Long> receivablePlanIdList, String receivablePlanDate) {
        for (Long receivablePlanId : receivablePlanIdList) {
            addReceivablePlanDate(receivablePlanId, receivablePlanDate);
        }
    }

    /**
     * Author Cream
     * Description : //更新应收计划收款时间
     * Date 2023/7/26 15:49
     * Params:
     * Return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SaveResponseBo updateReceivablePlanDate(Long fkReceivablePlanDateId, String receivablePlanDate) {
        ReceivablePlanDate planDate = receivablePlanDateMapper.selectById(fkReceivablePlanDateId);
        if (planDate!=null) {
            Long receivablePlanId = planDate.getFkReceivablePlanId();
            Date old = planDate.getReceivablePlanDate();
            Date date = DateUtil.parse(receivablePlanDate, "yyyy-MM-dd");
            if (old.compareTo(date)!=0) {
                planDate.setReceivablePlanDate(date);
                receivablePlanDateMapper.updateById(planDate);
                ReceivablePlan receivablePlan = receivablePlanMapper.selectById(receivablePlanId);
                ReceivablePlanDate minDate = receivablePlanDateMapper.selectOne(Wrappers.<ReceivablePlanDate>lambdaQuery().eq(ReceivablePlanDate::getFkReceivablePlanId, planDate.getFkReceivablePlanId())
                        .ne(ReceivablePlanDate::getId,fkReceivablePlanDateId).orderByAsc(ReceivablePlanDate::getReceivablePlanDate).last("limit 1"));
                if (GeneralTool.isNotEmpty(receivablePlan)) {
                    if (Objects.isNull(minDate)) {
                        receivablePlan.setReceivablePlanDate(date);
                    }else {
                        Date planReceivablePlanDate = minDate.getReceivablePlanDate();
                        if (date.compareTo(planReceivablePlanDate)<0) {
                            receivablePlan.setReceivablePlanDate(date);
                        }
                    }
                    utilService.setUpdateInfo(receivablePlan);
                    receivablePlanMapper.updateById(receivablePlan);
                }
            }
        }
        return SaveResponseBo.ok(fkReceivablePlanDateId);
    }

    /**
     * Author Cream
     * Description : //删除应收计划收款时间
     * Date 2023/7/26 15:41
     * Params:
     * Return
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBo deleteReceivablePlanDate(Long fkReceivablePlanDateId) {
        ReceivablePlanDate planDate = receivablePlanDateMapper.selectById(fkReceivablePlanDateId);
        if (planDate!=null) {
            receivablePlanDateMapper.deleteById(fkReceivablePlanDateId);
            ReceivablePlanDate minDate = receivablePlanDateMapper.selectOne(Wrappers.<ReceivablePlanDate>lambdaQuery().eq(ReceivablePlanDate::getFkReceivablePlanId, planDate.getFkReceivablePlanId())
                    .ne(ReceivablePlanDate::getId,fkReceivablePlanDateId).orderByAsc(ReceivablePlanDate::getReceivablePlanDate).last("limit 1"));
            ReceivablePlan receivablePlan = receivablePlanMapper.selectById(planDate.getFkReceivablePlanId());
            if (GeneralTool.isNotEmpty(receivablePlan)) {
                if (Objects.isNull(minDate)) {
                    receivablePlan.setReceivablePlanDate(null);
                }else {
                    receivablePlan.setReceivablePlanDate(minDate.getReceivablePlanDate());
                }
                utilService.setUpdateInfo(receivablePlan);
                receivablePlanMapper.updateByIdWithNull(receivablePlan);
            }
        }
        return DeleteResponseBo.ok();
    }

    /**
     * 根据应收计划id 检查是否有佣金分期数据，有则不可编辑
     *
     * @Date 16:25 2022/4/22
     * <AUTHOR>
     */
    private boolean checkStatusSettlementByReceivablePlan(Long receivablePlanId) {
        PayablePlan payablePlan = payablePlanMapper.selectOne(Wrappers.<PayablePlan>lambdaQuery().eq(PayablePlan::getFkReceivablePlanId, receivablePlanId).ne(PayablePlan::getStatus,0));
        return true;
    }

    @Override
    public String getCurrencyNum(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ReceivablePlan receivablePlan = receivablePlanMapper.selectById(id);
        return receivablePlan.getFkCurrencyTypeNum();
    }

    @Override
    public ReceivablePlanVo findReceivablePlan(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ReceivablePlan receivablePlan = receivablePlanMapper.selectById(id);
        if (GeneralTool.isEmpty(receivablePlan)) {
            return null;
        }
        ReceivablePlanBonusSetting receivablePlanBonusSetting = receivablePlanBonusSettingMapper.selectOne(
                new QueryWrapper<ReceivablePlanBonusSetting>().lambda()
                        .eq(ReceivablePlanBonusSetting::getFkReceivablePlanId, receivablePlan.getId())
        );

        ReceivablePlanVo receivablePlanVo = BeanCopyUtils.objClone(receivablePlan, ReceivablePlanVo::new);
        if(GeneralTool.isNotEmpty(receivablePlanBonusSetting)){
            receivablePlanVo.setBonusCommissionRate(receivablePlanBonusSetting.getBonusCommissionRate());
            receivablePlanVo.setBonusFixedAmount(receivablePlanBonusSetting.getBonusFixedAmount());
        }


        List<ReceiptFormVo> receiptFormDtoList = new ArrayList<>();
        Result<List<ReceiptFormVo>> result = financeCenterClient.getReceiptFormList(id);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            receiptFormDtoList.addAll(result.getData());
        }
        //过滤作废的子单
        receiptFormDtoList = receiptFormDtoList.stream().filter(receiptFormDto -> receiptFormDto.getStatus() == 1).collect(Collectors.toList());

        BigDecimal amountReceivable = new BigDecimal(0);
        BigDecimal actualReceivableAmount =BigDecimal.ZERO;
        for (ReceiptFormVo receiptFormVo : receiptFormDtoList) {
            amountReceivable = amountReceivable.add(receiptFormVo.getAmountReceivable()).add(receiptFormVo.getAmountExchangeRate());
            actualReceivableAmount = actualReceivableAmount.add(DataConverter.bigDecimalNullConvert(receiptFormVo.getAmountReceipt()));
        }
        receivablePlanVo.setAmountReceivable(amountReceivable);
        receivablePlanVo.setActualReceivableAmount(actualReceivableAmount);
        if (GeneralTool.isNotEmpty(receivablePlanVo.getReceivableAmount())) {
            receivablePlanVo.setDifference(amountReceivable.subtract(receivablePlanVo.getReceivableAmount()));
        } else {
            receivablePlanVo.setDifference(amountReceivable);
        }
        Result<String> currencyNameResult = financeCenterClient.getCurrencyNameByNum(receivablePlan.getFkCurrencyTypeNum());
        if (result.isSuccess() && GeneralTool.isNotEmpty(currencyNameResult.getData())) {
            receivablePlanVo.setEquivalentCurrencyTypeName(currencyNameResult.getData());
            receivablePlanVo.setFkCurrencyTypeName(currencyNameResult.getData());
        }
        Map<String, String> companyMap = getCompanyMap();

        //绑定应付计划Id
        LambdaQueryWrapper<PayablePlan> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PayablePlan::getFkReceivablePlanId,id).ne(PayablePlan::getStatus,0);
        List<PayablePlan> payablePlans = payablePlanMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(payablePlans)){
            receivablePlanVo.setFkPayPlanId(payablePlans.get(0).getId());
        }

//        if (TableEnum.INSTITUTION_PROVIDER.key.equals(receivablePlan.getFkTypeKey()) || TableEnum.SALE_OTHER_INCOME.key.equals(receivablePlan.getFkTypeKey())) {
//            Set<Long> idSet = new HashSet<>(1);
//            idSet.add(receivablePlan.getFkTypeTargetId());
//            Map<Long, Set<Long>> providerCompanyIdMap = new HashMap<>();
//            providerCompanyIdMap = institutionCenterClient.getCompanyIdsByProviderIds(idSet).getData();
//            Set<Long> companyIds = providerCompanyIdMap.get(receivablePlan.getFkTypeTargetId());
//            if (GeneralTool.isNotEmpty(companyIds)) {
//                StringJoiner sj = new StringJoiner(",");
//                for (Long companyId : companyIds) {
//                    sj.add(companyMap.get(companyId.toString()));
//                }
//                receivablePlanVo.setCompanyName(sj.toString());
//            }
//        }
        setName(receivablePlanVo, companyMap);
        return receivablePlanVo;
    }

    @Override
    public ARAPDto getARAPInformation(Long itemId, String typeKey) {
        if (Objects.isNull(itemId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<ReceivablePlan> planDtos = receivablePlanMapper.selectList(Wrappers.<ReceivablePlan>lambdaQuery().eq(ReceivablePlan::getFkTypeTargetId,itemId)
                        .eq(ReceivablePlan::getFkTypeKey,typeKey).eq(ReceivablePlan::getStatus,1));
        ARAPDto arapDto = new ARAPDto();
        if (GeneralTool.isNotEmpty(planDtos)) {
            List<Long> ids = planDtos.stream().map(ReceivablePlan::getId).collect(Collectors.toList());
            List<ReceivablePlanVo> receivablePlansDetail = getReceivablePlansDetail(new HashSet<>(ids));
            List<PublicReceiptFormDetailVo> receiptFormList = receivablePlanMapper.getReceiptAmountByIds(ids);
            Map<Long, List<PublicReceiptFormDetailVo>> listMap = receiptFormList.stream().collect(Collectors.groupingBy(PublicReceiptFormDetailVo::getId));
            for (ReceivablePlanVo planDto : receivablePlansDetail) {
                planDto.setReceiptFormDetailDtos(listMap.get(planDto.getId()));
            }
            arapDto.setReceivablePlanDtos(receivablePlansDetail);
        }
        List<PayablePlanVo> payablePlanList = payablePlanService.getPayablePlan(typeKey, itemId);
        if (GeneralTool.isNotEmpty(payablePlanList)) {
            List<Long> payIds = payablePlanList.stream().map(PayablePlanVo::getId).collect(Collectors.toList());
            List<PublicPayFormDetailVo> paidAmountInfo = payablePlanService.getPaidAmountInfo(payIds);
            Map<Long, List<PublicPayFormDetailVo>> listMap = paidAmountInfo.stream().collect(Collectors.groupingBy(PublicPayFormDetailVo::getId));
            for (PayablePlanVo payablePlanVo : payablePlanList) {
                payablePlanVo.setPayFormDetailDtos(listMap.get(payablePlanVo.getId()));
            }
            arapDto.setPayablePlanDtos(payablePlanList);
        }
        return arapDto;
    }

    @Override
    public List<ReceivablePlanVo> getReceivablePlansDetail(Set<Long> ids) {
        if (GeneralTool.isEmpty(ids)) {
            ids.add(0L);
        }
        List<ReceivablePlan> receivablePlans = receivablePlanMapper.selectBatchIds(ids);
        if (GeneralTool.isEmpty(receivablePlans)) {
            return null;
        }
        List<ReceivablePlanVo> receivablePlanVos = BeanCopyUtils.copyListProperties(receivablePlans, ReceivablePlanVo::new);
        //获取收款单
        List<ReceiptFormVo> receiptFormListFeignByPlanIds = financeCenterClient.getReceiptFormListFeignByPlanIds(ids);
        Map<Long, List<ReceiptFormVo>> listMap = receiptFormListFeignByPlanIds.stream().collect(Collectors.groupingBy(ReceiptFormVo::getFkReceivablePlanId));
        Set<String> nums = receivablePlanVos.stream().map(ReceivablePlanVo::getFkCurrencyTypeNum).collect(Collectors.toSet());
        Map<String, String> currencyNamesByNums = new HashMap<>();
        Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(nums);
        if (result.isSuccess() && result.getData() != null) {
            currencyNamesByNums = result.getData();
        }
        Map<String, String> companyMap = getCompanyMap();

        for (ReceivablePlanVo receivablePlanVo : receivablePlanVos) {
            List<ReceiptFormVo> receiptFormDtoList = listMap.get(receivablePlanVo.getId());
            BigDecimal amountReceivable = new BigDecimal(0);
            if (GeneralTool.isNotEmpty(receiptFormDtoList)) {
                for (ReceiptFormVo receiptFormVo : receiptFormDtoList) {
                    amountReceivable = amountReceivable.add(receiptFormVo.getAmountReceivable()).add(receiptFormVo.getAmountExchangeRate());
                }
            }
            receivablePlanVo.setAmountReceivable(amountReceivable);
            if (GeneralTool.isNotEmpty(receivablePlanVo.getReceivableAmount())) {
                receivablePlanVo.setDifference(amountReceivable.subtract(receivablePlanVo.getReceivableAmount()));
            } else {
                receivablePlanVo.setDifference(amountReceivable);
            }
            Result<String> currencyNameResult = financeCenterClient.getCurrencyNameByNum(receivablePlanVo.getFkCurrencyTypeNum());
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                receivablePlanVo.setEquivalentCurrencyTypeName(currencyNameResult.getData());
                receivablePlanVo.setFkCurrencyTypeName(currencyNameResult.getData());
            }
            setName(receivablePlanVo, companyMap);
        }
        return receivablePlanVos;
    }


    @Override
    public List<ReceivablePlanVo> getReceivablePlanByIds(Set<Long> ids) {
        if (GeneralTool.isEmpty(ids)) {
            ids.add(0L);
        }
        List<ReceivablePlan> receivablePlans = receivablePlanMapper.selectBatchIds(ids);
        if (GeneralTool.isEmpty(receivablePlans)) {
            return null;
        }
        List<ReceivablePlanVo> receivablePlanVos = receivablePlans.stream().map(r -> BeanCopyUtils.objClone(r, ReceivablePlanVo::new)).collect(Collectors.toList());
        Set<Long> tIds = receivablePlanVos.stream().map(ReceivablePlanVo::getFkTypeTargetId).collect(Collectors.toSet());
        Map<Long, StudentOfferItemVo> offerItemByIds = offerItemService.findOfferItemByIds(tIds);
        Map<Long, String> nameByItemIds = offerItemService.getStudentNameByItemIds(tIds);
        for (ReceivablePlanVo receivablePlanVo : receivablePlanVos) {
            receivablePlanVo.setFkTypeName(TableEnum.getValue(receivablePlanVo.getFkTypeKey()));
            StudentOfferItemVo offerItem =offerItemByIds.get(receivablePlanVo.getFkTypeTargetId());
            if (GeneralTool.isEmpty(offerItem)) {
                continue;
            }
            String targetName =nameByItemIds.get(offerItem.getId());
            receivablePlanVo.setFkTypeTargetName(targetName);
            String targetNames = getTargetNames(offerItem, targetName);
            receivablePlanVo.setTargetNames(targetNames);
        }
        return receivablePlanVos;

    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        receivablePlanMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<MediaAndAttachedVo> addMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.SALE_RECEIVABLE.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;

    }

    @Override
    public List<MediaAndAttachedVo> getMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.SALE_RECEIVABLE.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void unableReceivable(Long id, Long status,Boolean syncPay) {
        ReceivablePlan receivablePlan = receivablePlanMapper.selectById(id);
        if (receivablePlan == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_false"));
        }
        //判断是否已绑定收款单
        List<ReceiptFormVo> receiptFormDtoList = new ArrayList<>();
        Result<List<ReceiptFormVo>> result = financeCenterClient.getReceiptFormListFeign(id);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            receiptFormDtoList = result.getData();
        }
        if (receiptFormDtoList.size() > 0 && status.equals(0L)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("receivable_plan_is_not_close"));
        }
        if (syncPay) {
            //应付计划是否不存在，或已付款或进入结算。
            if (payablePlanService.checkPayableInfo(id)) {
                payablePlanService.cancelPayPlan(id);
            } else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("receivable_plan_not_close_pay_plan"));
            }
        }
        receivablePlan.setId(id);
        receivablePlan.setStatus(Math.toIntExact(status));
        utilService.setUpdateInfo(receivablePlan);
        receivablePlanMapper.updateById(receivablePlan);
    }

    /**
     * Author Cream
     * Description : // 批量作废应收计划
     * Date 2023/5/4 9:47
     * Params:
     * Return
     * @param receivableIds
     */
    @Override
    public ResponseBo<StudentOfferItemVo> batchUnableReceivablePlan(Set<Long> receivableIds) {
        if (GeneralTool.isNotEmpty(receivableIds)) {
            List<ReceivablePlan> receivablePlans = receivablePlanMapper.selectBatchIds(receivableIds);
            if (GeneralTool.isNotEmpty(receivablePlans)) {
                long fCount = receivablePlans.stream().filter(f -> !TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(f.getFkTypeKey())).count();
                if (fCount>0) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("cancel_fixed_type_accounts_receivable_plans"));
                }
                List<ReceiptFormItemVo> data = financeCenterClient.getReceiptFormItemListFeignByPlanIds(receivableIds);
                Set<Long> reIds = data.stream().map(ReceiptFormItemVo::getFkReceivablePlanId).collect(Collectors.toSet());
                List<ReceivablePlan> finalUpdateReceivablePlans = receivablePlans.stream().filter(f -> !reIds.contains(f.getId())).collect(Collectors.toList());
                List<ReceivablePlan> tipReceivablePlans = receivablePlans.stream().filter(f -> reIds.contains(f.getId())).collect(Collectors.toList());
                for (ReceivablePlan receivablePlan : finalUpdateReceivablePlans) {
                    receivablePlan.setStatus(0);
                    utilService.setUpdateInfo(receivablePlan);
                }
                if (GeneralTool.isNotEmpty(finalUpdateReceivablePlans)) {
                    updateBatchById(finalUpdateReceivablePlans);
                }
                if (GeneralTool.isNotEmpty(tipReceivablePlans)) {
                    Set<Long> offerItemIds = tipReceivablePlans.stream().map(ReceivablePlan::getFkTypeTargetId).collect(Collectors.toSet());
                    List<StudentOfferItemVo> offerItemByIds = offerItemService.getOfferItemByIds(offerItemIds);
                    return new ListResponseBo<>(offerItemByIds);
                }
                return new ResponseBo<>();
            }
        }
        throw new GetServiceException(LocaleMessageUtils.getMessage("update_false"));
    }

    @Override
    public List<Long> getReceivablePlanId(String typeKey, Long targetId) {
        LambdaQueryWrapper<ReceivablePlan> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(typeKey)) {
            lambdaQueryWrapper.eq(ReceivablePlan::getFkTypeKey, typeKey);
        }
        if (GeneralTool.isNotEmpty(targetId)) {
            lambdaQueryWrapper.eq(ReceivablePlan::getFkTypeTargetId, targetId);
        }
        List<ReceivablePlan> receivablePlans = receivablePlanMapper.selectList(lambdaQueryWrapper);
        return receivablePlans.stream().map(ReceivablePlan::getId).collect(Collectors.toList());
    }

    @Override
    public Map<Long, List<Long>> getReceivablePlanIds(String typeKey, Set<Long> targetIds) {
        LambdaQueryWrapper<ReceivablePlan> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(typeKey)) {
            lambdaQueryWrapper.eq(ReceivablePlan::getFkTypeKey, typeKey);
        }
        if (GeneralTool.isNotEmpty(targetIds)) {
            lambdaQueryWrapper.in(ReceivablePlan::getFkTypeTargetId, targetIds);
        }
        // 去除作废的应收计划
        lambdaQueryWrapper.ne(ReceivablePlan::getStatus, 0);
        List<ReceivablePlan> receivablePlans = receivablePlanMapper.selectList(lambdaQueryWrapper);
        Map<Long, List<ReceivablePlan>> collect = receivablePlans.stream().collect(Collectors.groupingBy(ReceivablePlan::getFkTypeTargetId));
        Map<Long, List<Long>> map = new HashMap<>();
        if (GeneralTool.isNotEmpty(collect)) {
            collect.forEach((k, v) -> map.put(k, v.stream().distinct().map(ReceivablePlan::getId).collect(Collectors.toList())));
        }
        return map;
    }

    /**
     * 生成应收应付 （废弃）
     *
     * @param failureFlag true:静默返回 false:
     * @Date 11:24 2021/7/9
     * <AUTHOR>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String generateReceivablePlan(StudentOfferItem studentOfferItem, boolean failureFlag) {
        logger.info("generateReceivablePlan<|>生成应收应付开始<|>num:{}<|>studentOfferItem:{}", studentOfferItem.getNum(), JSONObject.toJSONString(studentOfferItem));
        Student student = getStudent(studentOfferItem);
        if (GeneralTool.isEmpty(student)) {
            logger.error("generateReceivablePlan<|>学习计划获取学生信息异常<|> studentOfferItem:{}", JSONObject.toJSONString(studentOfferItem));
            throw new GetServiceException(LocaleMessageUtils.getMessage("student_info_null"));
        }
        //根据学习计划获取匹配合同公式
        InstitutionStudentOfferItemDto institutionStudentOfferItemDto = BeanCopyUtils.objClone(studentOfferItem, InstitutionStudentOfferItemDto::new);
        Result<List<ContractFormula>> contractFormulaResult = institutionCenterClient.getContractFormulasByOfferItem(student.getFkCompanyId(), student.getFkAreaCountryId(), institutionStudentOfferItemDto);
        //获取课程学费
        if (contractFormulaResult.isSuccess() && GeneralTool.isNotEmpty(contractFormulaResult.getData())) {
            JSONArray jsonArray = JSONArray.fromObject(contractFormulaResult.getData());
            //获取符合学习计划的合同公式
            List<ContractFormula> contractFormulas = JSONArray.toList(jsonArray, new ContractFormula(), new JsonConfig());
            if (GeneralTool.isEmpty(contractFormulas)) {
                logger.info("generateReceivablePlan<|>获取符合学习计划的合同公式sql初步匹配不上<|>num:{}<|>failureFlag:{}<|>studentOfferItem:{}", studentOfferItem.getNum(), failureFlag, JSONObject.toJSONString(studentOfferItem));
                String mark = "获取符合学习计划的合同公式sql初步匹配不上";
                if (failureFlag) {
                    insertFailureData(student, studentOfferItem, mark);
                }
                return mark;
            }
//            //特殊合同公式
//            List<ContractFormula> specialContractFormula = contractFormulas.stream().filter(contractFormula -> contractFormula.getFormulaType().equals(ProjectExtraEnum.SPECIAL.key)).collect(Collectors.toList());
            //普通合同公式
            List<ContractFormula> commonContractFormula = contractFormulas.stream().filter(contractFormula -> !contractFormula.getFormulaType().equals(ProjectExtraEnum.SPECIAL.key)).collect(Collectors.toList());
            boolean flagOver = false;
//            //特殊合同公式
//            for (ContractFormula contractFormula : specialContractFormula) {
//                //初步条件筛选
//                boolean flagStart = conditionScreening(studentOfferItem, contractFormula, student);
//                if (flagStart) {
//                    logger.info("generateReceivablePlan<|>初步命中特殊合同公式，开始生成应收应付计划<|>num:{}<|>studentOfferItem:{}", studentOfferItem.getNum(), JSONObject.toJSONString(studentOfferItem));
//                    flagOver = true;
//                    //生成应收应付计划
//                    insertPlan(studentOfferItem, contractFormula);
//                }
//            }
//            //命中过特殊合同公式，普通公式不生效
//            if (flagOver) {
//                logger.info("generateReceivablePlan<|>命中过特殊合同公式，普通公式不生效<|>num:{}<|>studentOfferItem:{}", studentOfferItem.getNum(), JSONObject.toJSONString(studentOfferItem));
//                return null;
//            }

            //普通合同公式
            if (GeneralTool.isNotEmpty(commonContractFormula)) {
                //如果命中过合同公式并生成应收应付计划 为 true
                boolean hitBoolean = false;
                for (ContractFormula contractFormula : commonContractFormula) {
                    //获取符合合同公式的课程
                    boolean flag = false;
                    //公式类型不为空
                    if (GeneralTool.isNotEmpty(contractFormula.getFormulaType())) {
                        //不是一次性奖励
                        if (contractFormula.getFormulaType().equals(ProjectExtraEnum.ROUTINE.key)
//                                || contractFormula.getFormulaType().equals(ProjectExtraEnum.AWARD.key)
                        ) {
                            flag = conditionScreening(studentOfferItem, contractFormula, student);
                            logger.info("generateReceivablePlan<|>进入普通公式不是一次性奖励判断<|>num:{}<|>flag:{}<|>studentOfferItem:{}", studentOfferItem.getNum(), flag, JSONObject.toJSONString(studentOfferItem));
                            //一次性奖励
                        }
//                        else if (contractFormula.getFormulaType().equals(ProjectExtraEnum.ONE_AWARD.key)) {
//                            if (!receivablePlanContractFormulaMapper.isExistByFormulaId(contractFormula.getId())) {
//                                flag = conditionScreening(studentOfferItem, contractFormula, student);
//                            }
//                            logger.info("generateReceivablePlan<|>进入普通公式一次性奖励判断<|>num:{}<|>flag:{}<|>studentOfferItem:{}", studentOfferItem.getNum(), flag, JSONObject.toJSONString(studentOfferItem));
//                        }
                    } else {
                        flag = conditionScreening(studentOfferItem, contractFormula, student);
                    }
                    if (flag) {
                        logger.info("generateReceivablePlan<|>初步命中普通合同公式，开始生成应收应付计划<|>num:{}<|>studentOfferItem:{}", studentOfferItem.getNum(), JSONObject.toJSONString(studentOfferItem));
                        //生成应收应付计划
                        insertPlan(studentOfferItem, contractFormula);
                        hitBoolean = true;
                    } else {
                        logger.info("generateReceivablePlan<|>普通公式条件筛选不通过<|>num:{}<|>studentOfferItem:{}", studentOfferItem.getNum(), JSONObject.toJSONString(studentOfferItem));
                        String mark = "普通公式条件筛选不通过";
                        if (failureFlag) {
                            insertFailureData(student, studentOfferItem, mark);
                        }
//                        return mark;
                    }
                }
                //一条都没中
                if (!hitBoolean) {
                    return "普通公式条件筛选不通过";
                }
            } else {
                logger.info("generateReceivablePlan<|>没有命中特殊公式，也没有查到普通公式<|>num:{}<|>studentOfferItem:{}", studentOfferItem.getNum(), JSONObject.toJSONString(studentOfferItem));
                String mark = "没有命中特殊公式，也没有查到普通公式";
                if (failureFlag) {
                    insertFailureData(student, studentOfferItem, mark);
                }
                return mark;
            }
        } else {
            logger.error("generateReceivablePlan<|>根据学习计划获取匹配合同公式异常<|>fkCompanyId:{}<|>fkAreaCountryId:{}<|>studentOfferItem:{}<|>responseBo:{}",
                    student.getFkCompanyId(), student.getFkAreaCountryId(), studentOfferItem, JSONObject.toJSONString(contractFormulaResult));
            throw new GetServiceException(contractFormulaResult.getMessage());
        }
        logger.info("generateReceivablePlan<|>生成应收应付结束<|>num:{}<|>studentOfferItem:{}", studentOfferItem.getNum(), JSONObject.toJSONString(studentOfferItem));
        return null;
    }

    /**
     * 合同公式生成失败，插入失败表
     *
     * @Date 15:42 2021/7/8
     * <AUTHOR>
     */
    private void insertFailureData(Student student, StudentOfferItem studentOfferItem, String remark) {
//        Example example = new Example(StudentOfferItemFailure.class);
//        example.createCriteria().andEqualTo("fkCompanyId", student.getFkCompanyId()).andEqualTo("fkStudentId", student.getId())
//                .andEqualTo("fkStudentOfferItemId", studentOfferItem.getId()).andEqualTo("remark", remark).andEqualTo("status", 0);

        LambdaQueryWrapper<StudentOfferItemFailure> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StudentOfferItemFailure::getFkCompanyId, student.getFkCompanyId());
        lambdaQueryWrapper.eq(StudentOfferItemFailure::getFkStudentId, student.getId());
        lambdaQueryWrapper.eq(StudentOfferItemFailure::getFkStudentOfferItemId, studentOfferItem.getId());
        lambdaQueryWrapper.eq(StudentOfferItemFailure::getRemark, remark);
        lambdaQueryWrapper.eq(StudentOfferItemFailure::getStatus, 0);
        List<StudentOfferItemFailure> studentOfferItemFailures = studentOfferItemFailureMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isEmpty(studentOfferItemFailures)) {
            StudentOfferItemFailure studentOfferItemFailure = new StudentOfferItemFailure();
            studentOfferItemFailure.setFkCompanyId(student.getFkCompanyId());
            studentOfferItemFailure.setFkStudentId(student.getId());
            studentOfferItemFailure.setFkStudentOfferItemId(studentOfferItem.getId());
            studentOfferItemFailure.setRemark(remark);
            studentOfferItemFailure.setStatus(0);
            utilService.updateUserInfoToEntity(studentOfferItemFailure);
            studentOfferItemFailureMapper.insert(studentOfferItemFailure);
        }
    }

    /**
     * 条件筛选
     *
     * @Date 16:06 2021/6/8
     * <AUTHOR>
     */
    private boolean conditionScreening(StudentOfferItem studentOfferItem, ContractFormula contractFormula, Student student) {
        //业务场景筛选
        String studentConditionType = student.getConditionType();
        String studentOfferItemConditionType = studentOfferItem.getConditionType();
        if (GeneralTool.isNotEmpty(contractFormula.getConditionType())) {
            switch (Objects.requireNonNull(ProjectExtraEnum.getEnum(Integer.parseInt(contractFormula.getConditionType()), ProjectExtraEnum.CONDITION_TYPE))) {
                case TRANSFER_AGENT_STUDENT:
//                case PERCENTAGE_OF_SCHOLARSHIP_FEES:
                    if (!contractFormula.getConditionType().equals(studentConditionType)) {
                        return false;
                    }
                    break;
//                case READ_ONLY_PHASE_ONE:
//                case READ_ONLY_PHASE_TWO:
//                case READ_ONLY_PHASE_THREE:
//                case READ_ONLY_PHASE_FOUR:
//                    if (!contractFormula.getConditionType().equals(studentOfferItemConditionType)) {
//                        return false;
//                    }
//                    break;
                default:
                    logger.error("conditionScreening<|>条件筛选异常，没有匹配的条件枚举<|> ConditionType:{}", contractFormula.getConditionType());
                    throw new GetServiceException(LocaleMessageUtils.getMessage("student_condition_type_null"));
            }
        }

        //币种校验 学习计划币种必须和合同公式币种一致
        if (!contractFormula.getFkCurrencyTypeNum().equals(studentOfferItem.getFkCurrencyTypeNum())) {
            return false;
        }

        //前置条件筛选
        //前置父学习计划
        StudentOfferItem parentStudentOfferItem = studentOfferItemMapper.selectById(studentOfferItem.getFkParentStudentOfferItemId());

        //合同公式前置学校
        List<Long> contractFormulaPreInstitutionIds = new ArrayList<>();
        Result<List<Long>> contractFormulaPreInstitutionResult = institutionCenterClient.getContractFormulaPreInstitutionByContractFormulaId(contractFormula.getId());
        if (contractFormulaPreInstitutionResult.isSuccess() && GeneralTool.isNotEmpty(contractFormulaPreInstitutionResult.getData())) {
            contractFormulaPreInstitutionIds = contractFormulaPreInstitutionResult.getData();
        }
        if (GeneralTool.isNotEmpty(contractFormulaPreInstitutionIds)) {
            if (!studentOfferItem.getIsFollow() || GeneralTool.isEmpty(parentStudentOfferItem)) {
                return false;
            }
            boolean contractFormulaPreInstitutionFlag = false;
            for (Long contractFormulaPreInstitutionId : contractFormulaPreInstitutionIds) {
                if (parentStudentOfferItem.getFkInstitutionId().equals(contractFormulaPreInstitutionId)) {
                    contractFormulaPreInstitutionFlag = true;
                    break;
                }
            }
            if (!contractFormulaPreInstitutionFlag) {
                return false;
            }
        }


        //合同公式前置集团
        List<Long> contractFormulaPreInstitutionGroupIds = new ArrayList<>();
        Result<List<Long>> contractFormulaPreInstitutionGroupIdsResult = institutionCenterClient.getPreInstitutionGroupByContractFormulaId(contractFormula.getId());
        if (contractFormulaPreInstitutionGroupIdsResult.isSuccess() && GeneralTool.isNotEmpty(contractFormulaPreInstitutionGroupIdsResult.getData())) {
            contractFormulaPreInstitutionIds.addAll(contractFormulaPreInstitutionGroupIdsResult.getData());
        }
        InstitutionProviderVo institutionProviderVo = institutionCenterClient.getInstitutionProviderById(parentStudentOfferItem.getFkInstitutionProviderId());
        //学习计划前置集团条件
//        List<StudentOfferItemPreInstitutionGroup> studentOfferItemPreInstitutionGroups = studentOfferItemPreInstitutionGroupMapper.selectList(Wrappers.<StudentOfferItemPreInstitutionGroup>lambdaQuery().eq(StudentOfferItemPreInstitutionGroup::getFkStudentOfferItemId, studentOfferItem.getId()));
        if (GeneralTool.isNotEmpty(contractFormulaPreInstitutionGroupIds)) {
            if (!studentOfferItem.getIsFollow() || GeneralTool.isEmpty(parentStudentOfferItem)) {
                return false;
            }

            boolean contractFormulaPreInstitutionGroupFlag = false;
            for (Long contractFormulaPreInstitutionGroupId : contractFormulaPreInstitutionGroupIds) {
                if (institutionProviderVo.getFkInstitutionGroupId().equals(contractFormulaPreInstitutionGroupId)) {
                    contractFormulaPreInstitutionGroupFlag = true;
                    break;
                }
            }
            if (!contractFormulaPreInstitutionGroupFlag) {
                return false;
            }
        }

        //合同公式前置课程等级
        List<Long> preMajorLevelIds = Lists.newArrayList();
        Result<List<Long>> preMajorLevelIdsResult = institutionCenterClient.getPreMajorLevelByContractFormulaId(contractFormula.getId());
        if (preMajorLevelIdsResult.isSuccess() && GeneralTool.isNotEmpty(preMajorLevelIdsResult.getData())) {
            preMajorLevelIds.addAll(preMajorLevelIdsResult.getData());
        }
        if (GeneralTool.isNotEmpty(preMajorLevelIds)) {
            if (!studentOfferItem.getIsFollow() || GeneralTool.isEmpty(parentStudentOfferItem)) {
                return false;
            }
            boolean preMajorLevelFlag = false;
            for (Long preMajorLevelId : preMajorLevelIds) {
                if (GeneralTool.isNotEmpty(studentOfferItem.getFkInstitutionCourseMajorLevelIds())){
                    for (String courseMajorLevelId : studentOfferItem.getFkInstitutionCourseMajorLevelIds().split(",")) {
                        if (Long.valueOf(courseMajorLevelId).equals(preMajorLevelId)) {
                            preMajorLevelFlag = true;
                        }
                    }
                }
            }
            if (!preMajorLevelFlag) {
                return false;
            }
        }

        //        boolean flag = false;
//        //无统计类型
//        if (GeneralTool.isEmpty(contractFormula.getCountType())) {
//            flag = true;
//            //按累计学生人数统计
//        } else if (contractFormula.getCountType().equals(ProjectExtraEnum.NUMBER_OF_STUDENTS.key)) {
//            ContractFormulaFeignVo contractFormulaConfig = feignInstitutionService.getContractFormulaConfigByContractFormulaId(contractFormula.getId());
//            //符合合公式条件的学习计划
//            List<Long> studentOfferIdList = studentOfferItemMapper.getOferItemByContractFormula(contractFormulaConfig).stream().map(StudentOfferItem::getFkStudentOfferId).collect(Collectors.toList());
//            BigDecimal studentCount;
//            if (GeneralTool.isNotEmpty(studentOfferIdList)) {
//                studentCount = new BigDecimal(studentOfferMapper.getStudentCountByOfferIds(studentOfferIdList));
//            } else {
//                studentCount = new BigDecimal(0);
//            }
//            if (studentCount.compareTo(contractFormula.getCountValeMin()) > -1 && studentCount.compareTo(contractFormula.getCountValeMax()) < 1) {
//                flag = true;
//            }
//            //按学费统计
//        } else if (contractFormula.getCountType().equals(ProjectExtraEnum.FEE_OF_STUDENTS.key)) {
//            if (contractFormula.getCountValeMin().compareTo(studentOfferItem.getTuitionAmount()) < 1 && contractFormula.getCountValeMax().compareTo(studentOfferItem.getTuitionAmount()) > -1) {
//                flag = true;
//            }
//
//            //课程长度（周）
//        } else if (contractFormula.getCountType().equals(ProjectExtraEnum.COURSE_DURATION_WEEK.key) && (GeneralTool.isNotEmpty(studentOfferItem.getDurationType()) && 0 == studentOfferItem.getDurationType())) {
//            if (contractFormula.getCountValeMin().compareTo(studentOfferItem.getDuration()) < 1 && contractFormula.getCountValeMax().compareTo(studentOfferItem.getDuration()) > -1) {
//                flag = true;
//            }
//            //课程长度（月）
//        } else if (contractFormula.getCountType().equals(ProjectExtraEnum.COURSE_DURATION_MONTH.key) && (GeneralTool.isNotEmpty(studentOfferItem.getDurationType()) && 1 == studentOfferItem.getDurationType())) {
//            if (contractFormula.getCountValeMin().compareTo(studentOfferItem.getDuration()) < 1 && contractFormula.getCountValeMax().compareTo(studentOfferItem.getDuration()) > -1) {
//                flag = true;
//            }
//            //课程长度（年）
//        } else if (contractFormula.getCountType().equals(ProjectExtraEnum.COURSE_DURATION_YEAR.key) && (GeneralTool.isNotEmpty(studentOfferItem.getDurationType()) && 2 == studentOfferItem.getDurationType())) {
//            if (contractFormula.getCountValeMin().compareTo(studentOfferItem.getDuration()) < 1 && contractFormula.getCountValeMax().compareTo(studentOfferItem.getDuration()) > -1) {
//                flag = true;
//            }
//            //课程长度(学期)
//        } else if (contractFormula.getCountType().equals(ProjectExtraEnum.COURSE_DURATION_SEMESTER.key) && (GeneralTool.isNotEmpty(studentOfferItem.getDurationType()) && 3 == studentOfferItem.getDurationType())) {
//            if (contractFormula.getCountValeMin().compareTo(studentOfferItem.getDuration()) < 1 && contractFormula.getCountValeMax().compareTo(studentOfferItem.getDuration()) > -1) {
//                flag = true;
//            }
//            //累计学费
//        } else if (contractFormula.getCountType().equals(ProjectExtraEnum.ACCUMULATED_TUITION.key)) {
//            ContractFormulaFeignVo contractFormulaConfig = feignInstitutionService.getContractFormulaConfigByContractFormulaId(contractFormula.getId());
//            List<StudentOfferItem> studentOfferItems = studentOfferItemMapper.getOferItemByContractFormula(contractFormulaConfig);
//            //累计学费
//            BigDecimal totalfee = new BigDecimal(0);
//            for (StudentOfferItem offerItem : studentOfferItems) {
//                totalfee = totalfee.add(offerItem.getTuitionAmount());
//            }
//            if (totalfee.compareTo(contractFormula.getCountValeMin()) > -1 && totalfee.compareTo(contractFormula.getCountValeMax()) < 1) {
//                flag = true;
//            }
//        }
//        return flag;
        return true;
    }

    /**
     * 生成应收应付计划
     *
     * @Date 14:12 2021/6/8
     * <AUTHOR>
     */
    private void insertPlan(StudentOfferItem studentOfferItem, ContractFormula contractFormula) {
        logger.info("insertPlan<|>生成应收计划开始<|>num:{}<|>studentOfferItem:{}", studentOfferItem.getNum(), JSONObject.toJSONString(studentOfferItem));
        //feign调用 通过合同公式查询合同公式佣金配置
//        ListResponseBo responseBo = institutionCenterClient.getContractFormulaCommissionByContractFormulaId(contractFormula.getId());
        List<ContractFormulaCommission> contractFormulaCommissions = Lists.newArrayList();
        Result<List<ContractFormulaCommissionVo>> contractFormulaCommissionResult = institutionCenterClient.getContractFormulaCommissionByContractFormulaId(contractFormula.getId());
        if (contractFormulaCommissionResult.isSuccess() && GeneralTool.isNotEmpty(contractFormulaCommissionResult.getData())) {
            JSONArray jsonArray = JSONArray.fromObject(contractFormulaCommissionResult.getData());
            //合同公式佣金配置
            contractFormulaCommissions.addAll(JSONArray.toList(jsonArray, new ContractFormulaCommission(), new JsonConfig()));
        } else {
            logger.error("insertPlan<|>feign调用 通过合同公式查询合同公式佣金配置异常<|>num:{}<|>studentOfferItem:{}<|>result:{}",
                    studentOfferItem.getNum(), JSONObject.toJSONString(studentOfferItem), JSONObject.toJSONString(contractFormulaCommissionResult));
            throw new GetServiceException(contractFormulaCommissionResult.getMessage());
        }

        //佣金期数
//        Map<Integer, String> steps = new HashMap<>();
        //判断是否有获取N阶段佣金业务场景
//        boolean flag = false;
//        if (GeneralTool.isNotEmpty(studentOfferItem.getConditionType())) {
//            String conditionType = studentOfferItem.getConditionType();
//            if (GeneralTool.isNotEmpty(ConditionTypeAndStepsEnum.getValue(conditionType))) {
//                flag = true;
//                steps.put(ConditionTypeAndStepsEnum.getStep(conditionType), ConditionTypeAndStepsEnum.getValue(conditionType));
//            }
//        }
        BigDecimal receivableAmountNum = new BigDecimal(0);
        for (int i = 0; i < contractFormulaCommissions.size(); i++) {
            ContractFormulaCommission contractFormulaCommission = contractFormulaCommissions.get(i);
//            //当前期数
//            Integer step;
//            //获取N阶段佣金业务场景
//            if (flag) {
//                //当前期数
//                step = contractFormulaCommission.getStep();
//                //当前期数和学习计划获取第N阶段佣金业务场景不匹配，读取下一阶段
//                if (GeneralTool.isEmpty(steps.get(step))) {
//                    continue;
//                }
//            }

            //佣金为0，不生成公式
//            boolean commissionRateFlag = GeneralTool.isNotEmpty(contractFormulaCommission.getCommissionRate()) && contractFormulaCommission.getCommissionRate().compareTo(BigDecimal.ZERO) > 0;
//            boolean fixedAmountFlag = GeneralTool.isNotEmpty(contractFormulaCommission.getFixedAmount()) && contractFormulaCommission.getFixedAmount().compareTo(BigDecimal.ZERO) > 0;
//            if (commissionRateFlag || fixedAmountFlag) {
            ReceivablePlan receivablePlan = new ReceivablePlan();
            receivablePlan.setStatus(1);
            receivablePlan.setFkTypeKey(TableEnum.SALE_STUDENT_OFFER_ITEM.key);
            receivablePlan.setFkTypeTargetId(studentOfferItem.getId());
            receivablePlan.setFkCurrencyTypeNum(contractFormula.getFkCurrencyTypeNum());
            receivablePlan.setTuitionAmount(studentOfferItem.getTuitionAmount());
            receivablePlan.setCommissionRate(contractFormulaCommission.getCommissionRate());
            if (GeneralTool.isNotEmpty(contractFormulaCommission.getCommissionRate())) {
                //佣金
                BigDecimal receivableAmount = BigDecimal.valueOf(studentOfferItem.getTuitionAmount().doubleValue() * contractFormulaCommission.getCommissionRate().doubleValue() / 100);
                //存在佣金上限
                if (GeneralTool.isNotEmpty(contractFormulaCommission.getLimitAmount())) {
                    //佣金大于佣金上限
                    if (contractFormulaCommission.getLimitAmount().compareTo(receivableAmount) < 0) {
                        receivableAmount = contractFormulaCommission.getLimitAmount();
                    }
                }
                //总佣金上限
                if (GeneralTool.isNotEmpty(contractFormula.getLimitAmount())) {
                    //累计总金额已经 >= 总佣金上限 后续阶段不生成佣金
                    if (receivableAmountNum.compareTo(contractFormula.getLimitAmount()) >= 0) {
                        continue;
                    } else {
                        receivableAmountNum = receivableAmountNum.add(receivableAmount);
                        //累计总金额+现阶段佣金金额 > 总佣金上限
                        if (receivableAmountNum.compareTo(contractFormula.getLimitAmount()) > 0) {
//                                //计算出的佣金 = 原佣金 - 超过的佣金      累计金额 = 佣金上限
//                                BigDecimal receivableAmount1 = receivableAmount.subtract(receivableAmountNum.subtract(contractFormula.getLimitAmount()));
//
//                                //结果计算出的佣金居然大于佣金上限，那就 恢复累计金额 然后 佣金=佣金上限
//                                if (contractFormulaCommission.getLimitAmount().compareTo(receivableAmount1) < 0) {
//                                    receivableAmountNum.subtract(receivableAmount);
//                                    receivableAmount1 = contractFormulaCommission.getLimitAmount();
//                                    //再假如累计总金额+计算出的佣金金额还是 > 总佣金上限
//                                    receivableAmountNum = receivableAmountNum.add(receivableAmount1);
//                                    if (receivableAmountNum.compareTo(contractFormula.getLimitAmount()) > 0) {
//                                        //最终的佣金 = 计算出的佣金 - 超过的佣金
//                                        receivableAmount = receivableAmount1.subtract(receivableAmountNum.subtract(contractFormula.getLimitAmount()));
//                                    } else {
//                                        receivableAmount = receivableAmount1;
//                                    }
//                                }
                            //佣金 = 佣金上限 - 原有佣金 = 剩余佣金份额      累计金额 = 佣金上限
                            receivableAmount = contractFormula.getLimitAmount().subtract(receivableAmountNum.subtract(receivableAmount));
//                                        receivableAmount = receivableAmount.subtract(receivableAmountNum.subtract(contractFormula.getLimitAmount()));
                            receivableAmountNum = contractFormula.getLimitAmount();
                        }
                    }
                }
                receivablePlan.setReceivableAmount(receivableAmount);
                if (contractFormula.getFormulaType().equals(ProjectExtraEnum.AWARD.key) || contractFormula.getFormulaType().equals(ProjectExtraEnum.ONE_AWARD.key)) {
                    receivablePlan.setBonusAmount(receivableAmount);
                }

            } else if (GeneralTool.isNotEmpty(contractFormulaCommission.getFixedAmount())) {
                //佣金
                BigDecimal receivableAmount = contractFormulaCommission.getFixedAmount();
                //存在总佣金上限 并且
                if (GeneralTool.isNotEmpty(contractFormula.getLimitAmount())) {
                    //累计总金额已经 >= 总佣金上限 后续阶段不生成佣金
                    if (receivableAmountNum.compareTo(contractFormula.getLimitAmount()) >= 0) {
                        continue;
                    } else {
                        receivableAmountNum = receivableAmountNum.add(receivableAmount);
                        //累计总金额+现阶段佣金金额 > 总佣金上限
                        if (receivableAmountNum.compareTo(contractFormula.getLimitAmount()) > 0) {
                            //佣金 = 佣金上限 - 原有佣金 = 剩余佣金份额      累计金额 = 佣金上限
                            receivableAmount = contractFormula.getLimitAmount().subtract(receivableAmountNum.subtract(receivableAmount));
//                                receivableAmount = receivableAmount.subtract(receivableAmountNum.subtract(contractFormula.getLimitAmount()));
                            receivableAmountNum = contractFormula.getLimitAmount();
                        }
                    }
                }
                receivablePlan.setReceivableAmount(receivableAmount);
                receivablePlan.setFixedAmount(receivableAmount);
                if (contractFormula.getFormulaType().equals(ProjectExtraEnum.AWARD.key) || contractFormula.getFormulaType().equals(ProjectExtraEnum.ONE_AWARD.key)) {
                    receivablePlan.setBonusAmount(receivableAmount);
                }
            }
//                if (receivablePlan.getReceivableAmount() == null || receivablePlan.getReceivableAmount().compareTo(BigDecimal.ZERO) < 1) {
//                    logger.error("insertPlan<|>金额异常:计算得出应收金额为0<|> receivableAmount:{}<|>studentOfferItem:{} <|>ContractFormula:{}",
//                            receivablePlan.getReceivableAmount(), JSONObject.toJSONString(studentOfferItem), JSONObject.toJSONString(contractFormula));
//                    throw new GetServiceException(LocaleMessageUtils.getMessage("receivable_amount_error"));
//                }
            utilService.updateUserInfoToEntity(receivablePlan);
            receivablePlanMapper.insertSelective(receivablePlan);
            ReceivablePlanContractFormula rpcf = new ReceivablePlanContractFormula();
            rpcf.setFkContractFormulaId(contractFormula.getId());
            rpcf.setFkReceivablePlanId(receivablePlan.getId());
            utilService.updateUserInfoToEntity(rpcf);
            receivablePlanContractFormulaMapper.insert(rpcf);
//            }
        }
        logger.info("insertPlan<|>生成应收计划结束<|>num:{}<|>studentOfferItem:{}", studentOfferItem.getNum(), JSONObject.toJSONString(studentOfferItem));

        //生成应付计划
        payablePlanService.generatePayablePlan(contractFormula, studentOfferItem, studentOfferItem.getTuitionAmount(), contractFormulaCommissions);
    }

    @Override
    public Student getStudent(StudentOfferItem studentOfferItem) {
        if (GeneralTool.isNotEmpty(studentOfferItem)) {
            StudentOffer studentOffer = studentOfferMapper.selectById(studentOfferItem.getFkStudentOfferId());
            if (GeneralTool.isNotEmpty(studentOffer)) {
                Student student = studentMapper.selectById(studentOffer.getFkStudentId());
                if (GeneralTool.isNotEmpty(student)) {
                    return student;
                }
            }
        }
        return null;
    }

    private void setName(ReceivablePlanVo receivablePlanVo, Map<String, String> companyMap) {
        receivablePlanVo.setFkTypeName(TableEnum.getValue(receivablePlanVo.getFkTypeKey()));
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        List<ReceivablePlanDate> planDates = receivablePlanDateMapper.selectList(Wrappers.<ReceivablePlanDate>lambdaQuery().eq(ReceivablePlanDate::getFkReceivablePlanId, receivablePlanVo.getId()));
        if (GeneralTool.isNotEmpty(planDates)) {
            receivablePlanVo.setReceivablePlanDateStr(planDates.stream().sorted(Comparator.comparing(ReceivablePlanDate::getReceivablePlanDate)).map(p->formatter.format(p.getReceivablePlanDate())).collect(Collectors.joining("，")));
        }
        receivablePlanVo.setCompanyName(companyMap.get(receivablePlanVo.getFkCompanyId().toString()));
        Long studentId = null;
        if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(receivablePlanVo.getFkTypeKey())) {
            StudentOfferItemVo offerItem = offerItemService.findOfferItemById(receivablePlanVo.getFkTypeTargetId());
            if (GeneralTool.isNotEmpty(offerItem)) {
                studentId = studentOfferMapper.getStudentIdById(offerItem.getFkStudentOfferId());
            }

            if (GeneralTool.isNotEmpty(receivablePlanVo.getFkInstitutionChannelId())) {
                Result<String> stringResult = institutionCenterClient.getChannelName(receivablePlanVo.getFkInstitutionChannelId());
                if (stringResult.isSuccess() && GeneralTool.isNotEmpty(stringResult.getData())) {
                    receivablePlanVo.setFkInstitutionChannelName(stringResult.getData());
                }

            }
            receivablePlanVo.setOpeningTime(offerItem.getDeferOpeningTime());
            receivablePlanVo.setDeferOpeningTime(offerItem.getDeferOpeningTime());//无需处理
            receivablePlanVo.setDuration(offerItem.getDuration());
            receivablePlanVo.setDurationType(offerItem.getDurationType());
            receivablePlanVo.setAppRemark(offerItem.getAppRemark());
            receivablePlanVo.setFkAreaCountryName(offerItem.getFkAreaCountryName());
            receivablePlanVo.setFkCourseName(offerItem.getFkCourseName());
            receivablePlanVo.setFkInstitutionName(offerItem.getFkInstitutionName());
            receivablePlanVo.setOldInstitutionName(offerItem.getOldInstitutionName());
            receivablePlanVo.setOldInstitutionFullName(offerItem.getOldInstitutionFullName());
            receivablePlanVo.setOldCourseCustomName(offerItem.getOldCourseCustomName());
            receivablePlanVo.setFkInstitutionProviderName(offerItem.getFkInstitutionProviderName());
            receivablePlanVo.setFkTypeTargetName(offerItem.getNum());
            receivablePlanVo.setFkInstitutionProviderId(offerItem.getFkInstitutionProviderId());
            receivablePlanVo.setFkInstitutionId(offerItem.getFkInstitutionId());
            receivablePlanVo.setFkInstitutionCourseId(offerItem.getFkInstitutionCourseId());
            receivablePlanVo.setFkInstitutionChannelId(offerItem.getFkInstitutionChannelId());
            //原jack添加
            Date studentBirthDay = studentMapper.getStudentBirthDay(studentId);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            if (GeneralTool.isEmpty(studentBirthDay)) {
                receivablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId));
            } else {
                String format = simpleDateFormat.format(studentBirthDay);
                receivablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId) + "/" + format);
            }
            String targetName = offerItemService.getStudentNameByItemId(offerItem.getId());
            receivablePlanVo.setFkTypeTargetName(targetName);

            String targetNames = getTargetNames(offerItem, targetName);
            receivablePlanVo.setTargetNames(targetNames);

            if (GeneralTool.isNotEmpty(receivablePlanVo.getFkCurrencyTypeNum())) {
                Result<String> result = financeCenterClient.getCurrencyNameByNum(receivablePlanVo.getFkCurrencyTypeNum());
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    String currencyName = result.getData();
                    receivablePlanVo.setFkCurrencyTypeName(currencyName);
                }
            }
            if (GeneralTool.isNotEmpty(receivablePlanVo.getFkReceivableReasonId())) {
                String reasonName = reasonService.getReasonNameById(receivablePlanVo.getFkReceivableReasonId());
                receivablePlanVo.setFkReceivableReasonName(reasonName);
            }
        } else if (TableEnum.SALE_STUDENT_INSURANCE.key.equals(receivablePlanVo.getFkTypeKey())) {
            StudentInsurance studentInsurance = studentInsuranceMapper.selectById(receivablePlanVo.getFkTypeTargetId());
            if (GeneralTool.isNotEmpty(studentInsurance)) {
                //学生信息
                studentId = studentInsurance.getFkStudentId();
                Student student = null;
                //0学生保险/1陪读人保险
                if (GeneralTool.isNotEmpty(studentInsurance.getType()) && studentInsurance.getType()==1){
                    StringBuilder studentInfo = new StringBuilder();
                    if (GeneralTool.isNotEmpty(studentInsurance.getInsurantName())){
                        studentInfo.append(studentInsurance.getInsurantName());
                    }
                    if (GeneralTool.isNotEmpty(studentInsurance.getInsurantFirstName()) && GeneralTool.isNotEmpty(studentInsurance.getInsurantLastName())){
                        studentInfo.append("("+ studentInsurance.getInsurantLastName()+" "+studentInsurance.getInsurantFirstName()+")");
                    }
                    if (GeneralTool.isNotEmpty(studentInsurance.getInsurantPassportNum())){
                        studentInfo.append(studentInsurance.getInsurantPassportNum());
                    }
                    receivablePlanVo.setStudentInformation(studentInfo.toString());
                }else {
                    if (GeneralTool.isNotEmpty(studentId)) {
                        student = studentMapper.getStudentById(studentId);
                        if (GeneralTool.isNotEmpty(student) && GeneralTool.isNotEmpty(student.getPassportNum())) {
                            receivablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId) + "/" + student.getPassportNum());
                        } else {
                            receivablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId));
                        }
                    }
                }
//                receivablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId) + "/" + studentMapper.getStudentPassportMum(studentId));
                //国家名称
                String fkAreaCountryName = null;
                if (GeneralTool.isNotEmpty(studentInsurance.getFkAreaCountryId())) {
                    fkAreaCountryName = institutionCenterClient.getCountryNameById(studentInsurance.getFkAreaCountryId()).getData();
                    receivablePlanVo.setFkAreaCountryName(fkAreaCountryName);
                }
                //业务信息
                receivablePlanVo.setBusinessInformation(businessChannelMapper.getFullNameById(studentInsurance.getFkBusinessChannelId()) + "/" + formatter.format(studentInsurance.getInsuranceStartTime())
                        + "/" + formatter.format(studentInsurance.getInsuranceEndTime()));

                //目标对象
                StringJoiner stringJoiner = new StringJoiner("/");
                stringJoiner.add(studentMapper.getStudentZhEnName(studentId));
                Set<Long> agentIds = new HashSet<Long>();
                //根据代理ids获取名称
                Map<Long, String> agentNamesByIds = new HashMap<>();
                if (GeneralTool.isNotEmpty(studentInsurance.getFkAgentId())) {
                    agentIds.add(studentInsurance.getFkAgentId());
                    agentNamesByIds = agentService.getAgentNamesByIds(agentIds);
                    stringJoiner.add(agentNamesByIds.get(studentInsurance.getFkAgentId()));
                }

                //国家
                if (GeneralTool.isNotEmpty(fkAreaCountryName)) {
                    stringJoiner.add(fkAreaCountryName);
                }
                if (GeneralTool.isNotEmpty(studentInsurance.getInsuranceStartTime()) && GeneralTool.isNotEmpty(studentInsurance.getInsuranceEndTime())) {
                    stringJoiner.add(formatter.format(studentInsurance.getInsuranceStartTime()));
                    stringJoiner.add(formatter.format(studentInsurance.getInsuranceEndTime()));
                }
                //保险渠道
                String businessChannelName = null;
                if (GeneralTool.isNotEmpty(studentInsurance.getFkBusinessChannelId())) {
                    businessChannelName = businessChannelMapper.getFullNameById(receivablePlanVo.getFkTypeTargetId());
                }
                if (GeneralTool.isNotEmpty(businessChannelName)) {
                    stringJoiner.add(businessChannelName);
                }
                receivablePlanVo.setTargetNames(stringJoiner.toString());

            }
        } else if(TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(receivablePlanVo.getFkTypeKey())){
            StudentServiceFeeVo serviceFeeDto = studentServiceFeeService.findServiceFeeById(receivablePlanVo.getFkTypeTargetId());
            if (GeneralTool.isNotEmpty(serviceFeeDto)) {
                studentId = serviceFeeDto.getFkStudentId();
//                receivablePlanVo.setStudentInformation(serviceFeeDto.getFkStudentName());
                receivablePlanVo.setFkAreaCountryName(serviceFeeDto.getFkAreaCountryName());
                receivablePlanVo.setBusinessInformation(serviceFeeDto.getServiceTypeName());
                receivablePlanVo.setTargetNames("【"+serviceFeeDto.getNum()+"】"
                        + serviceFeeDto.getFkStudentName()+"/"+serviceFeeDto.getFkAreaCountryName()+"/"+serviceFeeDto.getAgentName()+"/"+serviceFeeDto.getServiceTypeName());
            }
            receivablePlanVo.setFkTypeKeyReceivableName(TableEnum.getValueByKey(serviceFeeDto.getFkTypeKeyReceivable(), TableEnum.TYPE_KEY_RECEIVABLE));
            List<StudentServiceFeeVo> studentServiceFeeVos = studentServiceFeeMapper.getFeeTargetName(Collections.singletonList(receivablePlanVo.getFkTypeTargetId()));
            if (GeneralTool.isNotEmpty(studentServiceFeeVos)) {
                receivablePlanVo.setReceivableName(studentServiceFeeVos.get(0).getReceivableName());
                receivablePlanVo.setStudentInformation(studentServiceFeeVos.get(0).getReceivableName());
            }

        }
        else if (TableEnum.INSTITUTION_PROVIDER_CHANNEL.key.equals(receivablePlanVo.getFkTypeKey())) {
            Result<String> result = institutionCenterClient.getChannelName(receivablePlanVo.getFkTypeTargetId());
            if (result.isSuccess()) {
                String name = result.getData();
                receivablePlanVo.setChannelInformation(name);
                receivablePlanVo.setFkTypeTargetName(name);
            }
        } else if (TableEnum.SALE_STUDENT_ACCOMMODATION.key.equals(receivablePlanVo.getFkTypeKey())) {
            StudentAccommodation studentAccommodation = studentAccommodationMapper.selectById(receivablePlanVo.getFkTypeTargetId());
            if (GeneralTool.isNotEmpty(studentAccommodation)) {
                //学生信息
                studentId = studentAccommodation.getFkStudentId();
                Student student;

                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                if (GeneralTool.isNotEmpty(studentId)) {
                    student = studentMapper.getStudentById(studentId);
                    if (GeneralTool.isNotEmpty(student) && GeneralTool.isNotEmpty(student.getBirthday())) {
                        String format = simpleDateFormat.format(student.getBirthday());
                        receivablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId) + "/" + format);
                    } else {
                        receivablePlanVo.setStudentInformation(studentMapper.getStudentZhEnName(studentId));
                    }
                }

                //国家名称
                String fkAreaCountryName = null;
                if (GeneralTool.isNotEmpty(studentAccommodation.getFkAreaCountryId())) {
                    fkAreaCountryName = institutionCenterClient.getCountryNameById(studentAccommodation.getFkAreaCountryId()).getData();
                    receivablePlanVo.setFkAreaCountryName(fkAreaCountryName);
                }
                //业务信息
                receivablePlanVo.setBusinessInformation(businessChannelMapper.getFullNameById(studentAccommodation.getFkBusinessChannelId()) + "/" + studentAccommodation.getApartmentName()
                        + "/" + formatter.format(studentAccommodation.getCheckInDate()) + "/" + formatter.format(studentAccommodation.getCheckOutDate())
                        + "(" + studentAccommodation.getDuration() + ")");
                //渠道信息
                String channelInformation = businessChannelMapper.getFullNameById(receivablePlanVo.getFkTypeTargetId());
                receivablePlanVo.setChannelInformation(channelInformation);

                //目标对象
                StringJoiner stringJoiner = new StringJoiner("/");
                stringJoiner.add(studentMapper.getStudentZhEnName(studentId));
                Set<Long> agentIds = new HashSet<Long>();
                //根据代理ids获取名称
                Map<Long, String> agentNamesByIds = new HashMap<>();
                if (GeneralTool.isNotEmpty(studentAccommodation.getFkAgentId())) {
                    agentIds.add(studentAccommodation.getFkAgentId());
                    agentNamesByIds = agentService.getAgentNamesByIds(agentIds);
                    stringJoiner.add(agentNamesByIds.get(studentAccommodation.getFkAgentId()));
                }

                //国家
                if (GeneralTool.isNotEmpty(fkAreaCountryName)) {
                    stringJoiner.add(fkAreaCountryName);
                }
                if (GeneralTool.isNotEmpty(studentAccommodation.getApartmentName())) {
                    stringJoiner.add(studentAccommodation.getApartmentName());
                }
                if (GeneralTool.isNotEmpty(studentAccommodation.getCheckInDate()) && GeneralTool.isNotEmpty(studentAccommodation.getCheckOutDate())) {
                    stringJoiner.add(formatter.format(studentAccommodation.getCheckInDate()));
                    stringJoiner.add(formatter.format(studentAccommodation.getCheckOutDate()));
                    stringJoiner.add("(" + studentAccommodation.getDuration() + ")");
                }
                //保险渠道
                String businessChannelName = null;
                if (GeneralTool.isNotEmpty(studentAccommodation.getFkBusinessChannelId())) {
                    businessChannelName = businessChannelMapper.getFullNameById(receivablePlanVo.getFkTypeTargetId());
                }
                if (GeneralTool.isNotEmpty(businessChannelName)) {
                    stringJoiner.add(businessChannelName);
                }
                receivablePlanVo.setTargetNames(stringJoiner.toString());
            }
        }else if (TableEnum.BUSINESS_CHANNEL_INS.key.equals(receivablePlanVo.getFkTypeKey())
                || TableEnum.BUSINESS_CHANNEL_ACC.key.equals(receivablePlanVo.getFkTypeKey())){
            String name = businessChannelMapper.getNameById(receivablePlanVo.getFkTypeTargetId());
            receivablePlanVo.setChannelInformation(name);
            receivablePlanVo.setFkTypeTargetName(name);
        } else if (TableEnum.INSURANCE_ORDER.key.equals(receivablePlanVo.getFkTypeKey())) {
            InsuranceOrder insuranceOrder = insuranceOrderMapper.selectById(receivablePlanVo.getFkTypeTargetId());
            StringBuilder studentName = new StringBuilder();
            studentName.append(insuranceOrder.getInsurantName()).append("（").append(insuranceOrder.getInsurantFirstName()).append(" ").append(insuranceOrder.getInsurantLastName()).append("）");
            receivablePlanVo.setStudentName(studentName.toString());
            receivablePlanVo.setStudentInformation(studentName.toString());
            ProductType productType = productTypeMapper.selectById(insuranceOrder.getFkProductTypeId());
            Agent agent = agentMapper.selectById(insuranceOrder.getFkAgentId());
            //业务信息：产品名称：保单号，保单类型，时间范围
            //业务信息：安联：XXXXXXXXXXXX，Single，2025-05-30至2026-05-30
            StringBuilder studentInformation = new StringBuilder();
            studentInformation.append(agent.getName()).append(" / ").append(productType.getTypeName()).append("（").append(productType.getTypeKey()).append("）")
                    .append("：").append(insuranceOrder.getInsuranceNum()).append("，").append(insuranceOrder.getInsuranceType()).append("，");
            studentInformation.append(formatter.format(insuranceOrder.getInsuranceStartTime())).append("至").append(formatter.format(insuranceOrder.getInsuranceEndTime()));
            receivablePlanVo.setBusinessInformation(studentInformation.toString());
        } else {
            //提供商和其他收入
            Set<Long> idSet = new HashSet<>(1);
            idSet.add(receivablePlanVo.getFkTypeTargetId());
            Map<Long, String> institutionProviderSelectNamesByIds = institutionCenterClient.getInstitutionProviderSelectNamesByIds(idSet);
            Result<List<CompanyVo>> providerCompanyName = institutionCenterClient.getProviderCompanyName(receivablePlanVo.getFkTypeTargetId());
            if (providerCompanyName.isSuccess() && providerCompanyName.getData()!=null) {
                String companyName = providerCompanyName.getData().stream().map(CompanyVo::getShortName).collect(Collectors.joining(","));
                receivablePlanVo.setTargetCompanyName(companyName);
                receivablePlanVo.setTargetCompanyNameId(providerCompanyName.getData().stream().map(CompanyVo::getId).collect(Collectors.toList()));
            }
            if (GeneralTool.isNotEmpty(institutionProviderSelectNamesByIds)&&GeneralTool.isNotEmpty(institutionProviderSelectNamesByIds.get(receivablePlanVo.getFkTypeTargetId()))){
                receivablePlanVo.setTargetNames(institutionProviderSelectNamesByIds.get(receivablePlanVo.getFkTypeTargetId()));
                receivablePlanVo.setBusinessInformation(institutionProviderSelectNamesByIds.get(receivablePlanVo.getFkTypeTargetId()));
            }
        }
        if (studentId!=null) {
            Company company = studentMapper.getCompanyNameByStudentId(studentId);
            receivablePlanVo.setTargetCompanyName(company.getShortName());
            List<Long> targetCompanyNameId = new ArrayList<>(1);
            targetCompanyNameId.add(company.getId());
            receivablePlanVo.setTargetCompanyNameId(targetCompanyNameId);
        }
        if (GeneralTool.isNotEmpty(receivablePlanVo.getBonusType())) {
            receivablePlanVo.setBonusTypeName(ProjectExtraEnum.getValueByKey(receivablePlanVo.getBonusType(), ProjectExtraEnum.BONUS_TYPE));
        }
        //发票ids
        List<Long> invoiceIds = financeCenterClient.getInvoiceIdsByReceivablePlanId(receivablePlanVo.getId()).getData();
        receivablePlanVo.setFkInvoiceIds(invoiceIds);

    }

    /**
     * 列表信息封装
     * @param receivablePlanVos
     */
    private void setListName(List<ReceivablePlanVo> receivablePlanVos) {
        if (GeneralTool.isEmpty(receivablePlanVos)){
            return;
        }
        //获取应收详细详细
        Set<Long> planIds = receivablePlanVos.stream().map(ReceivablePlanVo::getId).collect(Collectors.toSet());
        List<ReceivablePlanDate> receivablePlanDates = receivablePlanDateMapper.selectList(Wrappers.<ReceivablePlanDate>lambdaQuery().in(ReceivablePlanDate::getFkReceivablePlanId, planIds));
        Map<Long, List<ReceivablePlanDate>> planDateMap = receivablePlanDates.stream().collect(Collectors.groupingBy(ReceivablePlanDate::getFkReceivablePlanId));
        List<PlanRemarkDetailResultVo> planRemarkDetailResultVos = receivablePlanMapper.getReceivablePlanRemarkDetails(planIds);
        Map<Long, List<SaleComment>> studentCommentMap = Maps.newHashMap();
        Map<Long, List<SaleComment>> studentOfferItemCommentMap = Maps.newHashMap();
        Map<Long, PlanRemarkDetailResultVo> planRemarkDetailResultDtoMap = Maps.newHashMap();
        if(GeneralTool.isNotEmpty(planRemarkDetailResultVos)){
            planRemarkDetailResultDtoMap = planRemarkDetailResultVos.stream().collect(HashMap::new, (m, v) -> m.put(v.getId(), v), HashMap::putAll);
            Set<Long> studentIds = planRemarkDetailResultVos.stream().map(PlanRemarkDetailResultVo::getFkStudentId).filter(Objects::nonNull).collect(Collectors.toSet());
            if (GeneralTool.isEmpty(studentIds)){
                studentIds.add(0L);
            }
            Set<Long> studentOfferItemIds = planRemarkDetailResultVos.stream().map(PlanRemarkDetailResultVo::getFkStudentOfferItemId).filter(Objects::nonNull).collect(Collectors.toSet());
            if (GeneralTool.isEmpty(studentOfferItemIds)){
                studentOfferItemIds.add(0L);
            }
            List<SaleComment> studentComments = commentService.list(Wrappers.<SaleComment>lambdaQuery()
                    .in(SaleComment::getFkTableId, studentIds)
                    .eq(SaleComment::getFkTableName, TableEnum.SALE_STUDENT.key));
            List<SaleComment> studentOfferItemComments = commentService.list(Wrappers.<SaleComment>lambdaQuery()
                    .in(SaleComment::getFkTableId, studentOfferItemIds)
                    .eq(SaleComment::getFkTableName, TableEnum.SALE_STUDENT_OFFER_ITEM.key));

            if (GeneralTool.isNotEmpty(studentComments)){
                studentCommentMap = studentComments.stream().collect(Collectors.groupingBy(SaleComment::getFkTableId));
            }
            if (GeneralTool.isNotEmpty(studentOfferItemComments)){
                studentOfferItemCommentMap = studentOfferItemComments.stream().collect(Collectors.groupingBy(SaleComment::getFkTableId));
            }
        }
        //币种编号nums
        Set<String> fkCurrencyTypeNums = receivablePlanVos.stream().map(ReceivablePlanVo::getFkCurrencyTypeNum).collect(Collectors.toSet());
        //根据币种编号nums获取名称map
        Map<String, String> currencyNamesByNums = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkCurrencyTypeNums)) {
            Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(fkCurrencyTypeNums);
            if (result.isSuccess() && result.getData() != null) {
                currencyNamesByNums = result.getData();
            }
        }
        //应收计划额外原因ids
        Set<Integer> fkReceivableReasonIds = receivablePlanVos.stream().map(ReceivablePlanVo::getFkReceivableReasonId).collect(Collectors.toSet());
        //根据应收计划额外原因ids获取名称
        Map<Long, String> reasonNameByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkReceivableReasonIds)) {
            reasonNameByIds = reasonService.getReasonNameByIds(fkReceivableReasonIds);
        }
        Map<String, String> companyMap = getCompanyMap();
        //应付类型对应记录ids
        Set<Long> fkTypeTargetIds = receivablePlanVos.stream().filter(f->TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(f.getFkTypeKey()))
                .map(ReceivablePlanVo::getFkTypeTargetId).collect(Collectors.toSet());
        //根据应付类型对应记录ids获取对象map
        Map<Long, StudentOfferItemVo> offerItemByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkTypeTargetIds)) {
            offerItemByIds = offerItemService.findOfferItemByIdsNew(fkTypeTargetIds);
        }
        //获取学生id
        Set<Long> studentIds = Sets.newHashSet();
        Set<Long> businessChannelIds = Sets.newHashSet();
        Map<Long, String> courseNameMap = new HashMap<>();
        Map<Long, String> institutionNameMap = new HashMap<>();
        Map<Long, String> stepNameMap = new HashMap<>();
//        Set<Long> agentIdSet;
//        Map<Long, String> agentNameMap = Maps.newHashMap();
//        Map<Long, String> institutionMap = Maps.newHashMap();
//        Map<Long, String> courseMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(offerItemByIds)){
            Set<Long> studentIdSet = offerItemByIds.values().stream().map(StudentOfferItemVo::getFkStudentId).filter(Objects::nonNull).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(studentIdSet)){
                studentIds.addAll(studentIdSet);
            }
            //获取课程
            Set<Long> courseIds = offerItemByIds.values().stream().map(StudentOfferItemVo::getFkInstitutionCourseId).collect(Collectors.toSet());
            courseNameMap = institutionCenterClient.getCourseNameByIds(courseIds).getData();

            //获取学校
            Set<Long> institutionIds = offerItemByIds.values().stream().map(StudentOfferItemVo::getFkInstitutionId).collect(Collectors.toSet());
            institutionNameMap = institutionCenterClient.getInstitutionNamesByIds(institutionIds).getData();

            //获取步骤
            Set<Long> stepIds = offerItemByIds.values().stream().map(StudentOfferItemVo::getFkStudentOfferItemStepId).collect(Collectors.toSet());
            List<StudentOfferItemStep> studentOfferItemSteps = studentOfferItemStepMapper.selectBatchIds(stepIds);
            stepNameMap = studentOfferItemSteps.stream().collect(Collectors.toMap(StudentOfferItemStep::getId,StudentOfferItemStep::getStepName));
//            Set<Long> institutionIds = offerItemByIds.values().stream().map(StudentOfferItemVo::getFkInstitutionId).filter(Objects::nonNull).collect(Collectors.toSet());
//            if (GeneralTool.isNotEmpty(institutionIds)) {
//                institutionMap = institutionCenterClient.getInstitutionNamesByIds(institutionIds).getData();
//            }
//            Set<Long> courseIds = offerItemByIds.values().stream().map(StudentOfferItemVo::getFkInstitutionCourseId).filter(Objects::nonNull).collect(Collectors.toSet());
//            if (GeneralTool.isNotEmpty(institutionIds)) {
//                courseMap = institutionCenterClient.getInstitutionCourseNamesByIds(courseIds).getData();
//            }
//            agentIdSet = offerItemByIds.values().stream().map(StudentOfferItemVo::getFkAgentId).filter(Objects::nonNull).collect(Collectors.toSet());
//            if (GeneralTool.isNotEmpty(agentIdSet)) {
//                agentNameMap = agentService.getAgentNamesByIds(agentIdSet);
//            }
        }
        //获取学校提供商
        Map<Long, String> institutionProviderSelectNamesByIds = new HashMap<>();
        Set<Long> providerIds = receivablePlanVos.stream().filter(f -> TableEnum.INSTITUTION_PROVIDER.key.equals(f.getFkTypeKey())).map(ReceivablePlanVo::getFkTypeTargetId).collect(Collectors.toSet());
        providerIds.addAll(offerItemByIds.values().stream().map(StudentOfferItemVo::getFkInstitutionProviderId).collect(Collectors.toSet()));

        //获取保险 和保险的学生
        Map<Long,StudentInsurance> studentInsuranceMap = Maps.newHashMap();
        Set<Long> insuranceIds = receivablePlanVos.stream().filter(p -> TableEnum.SALE_STUDENT_INSURANCE.key.equals(p.getFkTypeKey())).map(ReceivablePlanVo::getFkTypeTargetId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(insuranceIds)){
            List<StudentInsurance> studentInsurances = studentInsuranceService.listByIds(insuranceIds);
            if (GeneralTool.isNotEmpty(studentInsurances)){
                studentInsuranceMap = studentInsurances.stream().collect(HashMap::new, (m, v) -> m.put(v.getId(), v), HashMap::putAll);

                Set<Long> studentIdSet = studentInsurances.stream().map(StudentInsurance::getFkStudentId).filter(Objects::nonNull).collect(Collectors.toSet());
                if (GeneralTool.isNotEmpty(studentIdSet)){
                    studentIds.addAll(studentIdSet);
                }

                Set<Long> businessChannelIdSet = studentInsurances.stream().map(StudentInsurance::getFkBusinessChannelId).filter(Objects::nonNull).collect(Collectors.toSet());
                if (GeneralTool.isNotEmpty(businessChannelIdSet)){
                    businessChannelIds.addAll(businessChannelIdSet);
                }
            }

        }
        //获取澳小保保险学生
        Map<Long,InsuranceOrder> insuranceOrderMap = Maps.newHashMap();
        Map<Long ,ProductType> productTypeMap = Maps.newHashMap();
        Set<Long> insuranceOrderIds = receivablePlanVos.stream().filter(p -> TableEnum.INSURANCE_ORDER.key.equals(p.getFkTypeKey())).map(ReceivablePlanVo::getFkTypeTargetId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(insuranceOrderIds)){
            List<InsuranceOrder> insuranceOrders = insuranceOrderMapper.selectBatchIds(insuranceOrderIds);
            if (GeneralTool.isNotEmpty(insuranceOrders)){
                insuranceOrderMap = insuranceOrders.stream().collect(HashMap::new, (m, v) -> m.put(v.getId(), v), HashMap::putAll);
            }
            Set<Long> productTypeIdIdSet = insuranceOrders.stream().map(InsuranceOrder::getFkProductTypeId).filter(Objects::nonNull).collect(Collectors.toSet());
            productTypeMapper.selectBatchIds(productTypeIdIdSet).forEach(productType -> {
                productTypeMap.put(productType.getId(), productType);
            });
        }

        //获取住宿 和住宿的学生
        Map<Long,StudentAccommodation> studentAccommodationMap = Maps.newHashMap();
        Set<Long> accommodationIds = receivablePlanVos.stream().filter(p -> TableEnum.SALE_STUDENT_ACCOMMODATION.key.equals(p.getFkTypeKey())).map(ReceivablePlanVo::getFkTypeTargetId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(accommodationIds)){
            List<StudentAccommodation> studentAccommodations = studentAccommodationService.listByIds(accommodationIds);
            if (GeneralTool.isNotEmpty(studentAccommodations)){
                studentAccommodationMap = studentAccommodations.stream().collect(HashMap::new, (m, v) -> m.put(v.getId(), v), HashMap::putAll);

                Set<Long> studentIdSet = studentAccommodations.stream().map(StudentAccommodation::getFkStudentId).filter(Objects::nonNull).collect(Collectors.toSet());
                if (GeneralTool.isNotEmpty(studentIdSet)){
                    studentIds.addAll(studentIdSet);
                }

                Set<Long> businessChannelIdSet = studentAccommodations.stream().map(StudentAccommodation::getFkBusinessChannelId).filter(Objects::nonNull).collect(Collectors.toSet());
                if (GeneralTool.isNotEmpty(businessChannelIdSet)){
                    businessChannelIds.addAll(businessChannelIdSet);
                }
            }
        }
        //获取留学服务费
        List<Long> serviceIds = receivablePlanVos.stream().filter(f -> TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(f.getFkTypeKey())).map(ReceivablePlanVo::getFkTypeTargetId).collect(Collectors.toList());
        Map<Long, StudentServiceFeeVo> serviceFeeDtoMap = new HashMap<>();
        Map<Long, String> feeTargetMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(serviceIds)) {
            List<StudentServiceFeeVo> fee = studentServiceFeeService.getServiceFeeByIds(serviceIds);
            studentIds.addAll(fee.stream().map(StudentServiceFeeVo::getFkStudentId).collect(Collectors.toSet()));
            Set<Long> studentIdSet = fee.stream().filter(studentServiceFeeDto -> TableEnum.SALE_STUDENT.key.equals(studentServiceFeeDto.getFkTypeKeyReceivable())).map(StudentServiceFeeVo::getFkTypeTargetIdReceivable).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(studentIdSet)) {
                studentIds.addAll(studentIdSet);
            }
            Set<Long> providerIdSet = fee.stream().filter(studentServiceFeeDto -> TableEnum.INSTITUTION_PROVIDER.key.equals(studentServiceFeeDto.getFkTypeKeyReceivable())).map(StudentServiceFeeVo::getFkTypeTargetIdReceivable).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(providerIdSet)) {
                providerIds.addAll(providerIdSet);
            }
            serviceFeeDtoMap = fee.stream().collect(Collectors.toMap(StudentServiceFeeVo::getId, Function.identity()));

            List<StudentServiceFeeVo> studentServiceFeeVos = studentServiceFeeMapper.getFeeTargetName(serviceIds);
            studentServiceFeeVos.forEach(studentServiceFeeDto -> {
                feeTargetMap.put(studentServiceFeeDto.getId(),  studentServiceFeeDto.getReceivableName());
            });
        }

        if (GeneralTool.isNotEmpty(providerIds)) {
            institutionProviderSelectNamesByIds = institutionCenterClient.getInstitutionProviderNamesByIds(providerIds).getData();
        }
//        Map<Long, Date> birthdayMap = Maps.newHashMap();
//        Map<Long, String> fullNameMap = Maps.newHashMap();
//        Map<Long, String> passportNumMap = Maps.newHashMap();
        Map<Long, Student> studentMap = Maps.newHashMap();
        Map<Long, String> businessChannelNameMap = Maps.newHashMap();
        Set<Long> countryIds = institutionCenterClient.getAllCountryId().getData();
        Map<Long, String> countryNameMap = institutionCenterClient.getCountryNamesByIds(countryIds).getData();
        if (GeneralTool.isNotEmpty(studentIds)){
            List<Student> students = studentMapper.selectBatchIds(studentIds);
            studentMap = students.stream().collect(Collectors.toMap(Student::getId,Function.identity()));
//            //获取学生生日
//            List<Student> studentBirthDayByIds = studentMapper.getStudentBirthDayByIds(studentIds);
//            if (GeneralTool.isNotEmpty(studentBirthDayByIds)){
//                birthdayMap = studentBirthDayByIds.stream().filter(s->GeneralTool.isNotEmpty(s.getBirthday())).collect(HashMap::new, (m, v) -> m.put(v.getId(), v.getBirthday()), HashMap::putAll);
//            }
//            //获取学生中英文名
//            List<StudentVo> studentZhEnNameByStudentIds = studentMapper.getStudentZhEnNameByIds(studentIds);
//            if (GeneralTool.isNotEmpty(studentZhEnNameByStudentIds)){
//                fullNameMap = studentZhEnNameByStudentIds.stream().filter(s->GeneralTool.isNotEmpty(s.getFullName())).collect(HashMap::new, (m, v) -> m.put(v.getId(), v.getFullName()), HashMap::putAll);
//            }
//
//            List<StudentVo> studentPassportMumByIds = studentMapper.getStudentPassportMumByIds(studentIds);
//            if (GeneralTool.isNotEmpty(studentPassportMumByIds)){
//                passportNumMap = studentPassportMumByIds.stream().filter(s->GeneralTool.isNotEmpty(s.getPassportNum())).collect(HashMap::new, (m, v) -> m.put(v.getId(), v.getPassportNum()), HashMap::putAll);
//            }

        }
        //获取留学住宿和保险渠道
        Set<Long> bisInsIds = receivablePlanVos.stream().filter(f -> TableEnum.BUSINESS_CHANNEL_INS.key.equals(f.getFkTypeKey())).map(ReceivablePlanVo::getFkTypeTargetId).collect(Collectors.toSet());
        businessChannelIds.addAll(bisInsIds);
        Set<Long> bisAccIds = receivablePlanVos.stream().filter(f -> TableEnum.BUSINESS_CHANNEL_ACC.key.equals(f.getFkTypeKey())).map(ReceivablePlanVo::getFkTypeTargetId).collect(Collectors.toSet());
        businessChannelIds.addAll(bisAccIds);
        if (GeneralTool.isNotEmpty(businessChannelIds)){
            List<BusinessChannelVo> businessChannelNameByIds = businessChannelMapper.getFullNameByIds(businessChannelIds);
            if (GeneralTool.isNotEmpty(businessChannelNameByIds)){
                businessChannelNameMap = businessChannelNameByIds.stream().filter(s->GeneralTool.isNotEmpty(s.getFullName())).collect(HashMap::new, (m, v) -> m.put(v.getId(), v.getFullName()), HashMap::putAll);
            }
        }

        //获取学校渠道
        Set<Long> institutionChannelIds = receivablePlanVos.stream().filter(f -> TableEnum.INSTITUTION_PROVIDER_CHANNEL.key.equals(f.getFkTypeKey())).map(ReceivablePlanVo::getFkTypeTargetId).collect(Collectors.toSet());
        Map<Long, String> institutionChannelMap = new HashMap<>();
        institutionChannelIds.addAll(offerItemByIds.values().stream().map(StudentOfferItemVo::getFkInstitutionChannelId).collect(Collectors.toSet()));
        if (GeneralTool.isNotEmpty(institutionChannelIds)) {
            institutionChannelMap = institutionCenterClient.getInstitutionProviderChannelByIds(institutionChannelIds).getData();
        }

        //应收列表根据不同类型的应收封装不同的信息，主要是优化代码。
        for (ReceivablePlanVo receivablePlanVo : receivablePlanVos) {
            receivablePlanVo.setFkTypeName(TableEnum.getValue(receivablePlanVo.getFkTypeKey()));
            Long studentId;
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            List<ReceivablePlanDate> planDates = planDateMap.get(receivablePlanVo.getId());
            if (GeneralTool.isNotEmpty(planDates)) {
                String planDateStr = planDates.stream().sorted(Comparator.comparing(ReceivablePlanDate::getReceivablePlanDate)).map(p -> formatter.format(p.getReceivablePlanDate())).collect(Collectors.joining("，"));
                receivablePlanVo.setReceivablePlanDateStr(planDateStr);
            }
            if(GeneralTool.isNotEmpty(planRemarkDetailResultDtoMap)){
                PlanRemarkDetailResultVo planRemarkDetailResultVo = planRemarkDetailResultDtoMap.get(receivablePlanVo.getId());
                if (GeneralTool.isNotEmpty(planRemarkDetailResultVo)) {
                    PlanRemarkDetailVo planRemarkDetailVo = BeanCopyUtils.objClone(planRemarkDetailResultVo, PlanRemarkDetailVo::new);
                    if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(receivablePlanVo.getFkTypeKey())){
                        assert planRemarkDetailVo != null;
                        if (GeneralTool.isNotEmpty(studentCommentMap)
                                &&GeneralTool.isNotEmpty(studentCommentMap.get(planRemarkDetailResultVo.getFkStudentId()))){
                            List<SaleComment> saleComments = studentCommentMap.get(planRemarkDetailResultVo.getFkStudentId());
                            List<String> studentCommentList = saleComments.stream().map(SaleComment::getComment).collect(Collectors.toList());
                            planRemarkDetailVo.setStudentComments(studentCommentList);
                        }
                        if (GeneralTool.isNotEmpty(studentOfferItemCommentMap)
                                &&GeneralTool.isNotEmpty(studentOfferItemCommentMap.get(planRemarkDetailResultVo.getFkStudentOfferItemId()))){
                            List<SaleComment> saleComments = studentOfferItemCommentMap.get(planRemarkDetailResultVo.getFkStudentOfferItemId());
                            List<String> studentOfferItemCommentList = saleComments.stream().map(SaleComment::getComment).collect(Collectors.toList());
                            planRemarkDetailVo.setStudentOfferItemComments(studentOfferItemCommentList);
                        }
                        if (GeneralTool.isNotEmpty(planRemarkDetailResultVo.getEducationProject())){
                            planRemarkDetailVo.setEducationProjectName(ProjectExtraEnum.getValueByKey(planRemarkDetailResultVo.getEducationProject(),ProjectExtraEnum.EDUCATION_PROJECT));
                        }
                        if (GeneralTool.isNotEmpty(planRemarkDetailResultVo.getEducationDegree())){
                            planRemarkDetailVo.setEducationDegreeName(ProjectExtraEnum.getValueByKey(planRemarkDetailResultVo.getEducationDegree(),ProjectExtraEnum.EDUCATION_DEGREE));
                        }
                    }

                    receivablePlanVo.setPlanRemarkDetailDto(planRemarkDetailVo);
                    if (GeneralTool.isEmpty(planRemarkDetailVo)
                            ||(GeneralTool.isNotEmpty(planRemarkDetailVo)&&MyStringUtils.isAllFieldNull(planRemarkDetailVo))){
                        receivablePlanVo.setPlanRemarkDetailDto(null);
                    }
                }
            }
            if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(receivablePlanVo.getFkTypeKey())) {
                StudentOfferItemVo offerItem = offerItemByIds.get(receivablePlanVo.getFkTypeTargetId());
                studentId = offerItem.getFkStudentId();
                Student student = studentMap.get(studentId);
                String fullName = null;
                receivablePlanVo.setStudentId(offerItem.getStudentId());
                if (GeneralTool.isNotEmpty(student)) {
                    receivablePlanVo.setStudentName(student.getName());
                    receivablePlanVo.setStudentFirstName(student.getFirstName());
                    receivablePlanVo.setStudentLastName(student.getLastName());
                    if (GeneralTool.isNotEmpty(student.getBirthday())) {
                        receivablePlanVo.setStudentBirthday(formatter.format(student.getBirthday()));
                    }
                    receivablePlanVo.setStudentNum(student.getNum());
                    fullName = student.getName() + "（" + student.getLastName() + " " + student.getFirstName() + "）";
                }
                String institutionName = institutionNameMap.get(offerItem.getFkInstitutionId());
                String providerName = institutionProviderSelectNamesByIds.get(offerItem.getFkInstitutionProviderId());
                String courseName = -1 == offerItem.getFkInstitutionCourseId() ? offerItem.getOldCourseCustomName() : courseNameMap.get(offerItem.getFkInstitutionCourseId());
                String countryName = countryNameMap.get(offerItem.getFkAreaCountryId());
                receivablePlanVo.setStepName(stepNameMap.get(offerItem.getFkStudentOfferItemStepId()));
                receivablePlanVo.setFkInstitutionProviderId(offerItem.getFkInstitutionProviderId());
                receivablePlanVo.setFkAreaCountryName(countryName);
                receivablePlanVo.setFkCourseName(courseName);
                receivablePlanVo.setOldCourseCustomName(offerItem.getOldCourseCustomName());
                receivablePlanVo.setFkInstitutionName(institutionName);
                receivablePlanVo.setFkInstitutionProviderName(providerName);
                receivablePlanVo.setFkTypeTargetName(offerItem.getNum());
                receivablePlanVo.setDuration(offerItem.getDuration());
                receivablePlanVo.setFkInstitutionCourseId(offerItem.getFkInstitutionCourseId());
                receivablePlanVo.setFkInstitutionId(offerItem.getFkInstitutionId());
                receivablePlanVo.setDurationType(offerItem.getDurationType());
                receivablePlanVo.setAppRemark(offerItem.getAppRemark());
                receivablePlanVo.setFkTypeTargetName(fullName);
                receivablePlanVo.setFkAreaCountryName(countryNameMap.get(offerItem.getFkAreaCountryId()));
                if (GeneralTool.isNotEmpty(receivablePlanVo.getFkReceivableReasonId())) {
                    String reasonName = reasonNameByIds.get(receivablePlanVo.getFkReceivableReasonId().longValue());
                    receivablePlanVo.setFkReceivableReasonName(reasonName);
                }
                if (receivablePlanVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_OFFER_ITEM.key)) {
                    //原jack添加
                    if (GeneralTool.isNotEmpty(studentId)) {
                        Date studentBirthDay = student.getBirthday();
                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                        if (GeneralTool.isEmpty(studentBirthDay)) {
                            receivablePlanVo.setStudentInformation(fullName);
                        } else {
                            String dateString = simpleDateFormat.format(studentBirthDay);
                            receivablePlanVo.setStudentInformation(fullName + "/" + dateString);
                        }
                        if (GeneralTool.isNotEmpty(receivablePlanVo.getStudentId())){
                            receivablePlanVo.setStudentInformation(receivablePlanVo.getStudentInformation()+"/"+ receivablePlanVo.getStudentId());
                        }
                    }
                    //业务信息
                    StringBuilder businessInformation = new StringBuilder();
                    if (GeneralTool.isNotEmpty(institutionName)) {
                        businessInformation.append(institutionName);
                    }
                    if (GeneralTool.isNotEmpty(courseName)) {
                        businessInformation.append("/");
                        businessInformation.append(courseName);
                        if (GeneralTool.isNotEmpty(offerItem.getDurationType()) && GeneralTool.isNotEmpty(offerItem.getDuration())) {
                            businessInformation.append(" (").append(offerItem.getDuration()).append(ProjectExtraEnum.getValueByKey(offerItem.getDurationType(), ProjectExtraEnum.DURATION_TYPE)).append(")");
                        }
                    }
                    if (GeneralTool.isNotEmpty(offerItem.getDeferOpeningTime())) {
                        businessInformation.append("/");
                        businessInformation.append(formatter.format(offerItem.getDeferOpeningTime()));
                        receivablePlanVo.setDeferOpeningTime(offerItem.getDeferOpeningTime());
                    }
                    receivablePlanVo.setIsDeferEntrance(offerItem.getIsDeferEntrance());
                    receivablePlanVo.setBusinessInformation(businessInformation.toString());
                    receivablePlanVo.setIstOrApmName(institutionName);
                    receivablePlanVo.setShortNameOrApaStartTime(institutionName);
                    //渠道信息
                    receivablePlanVo.setChannelInformation(institutionChannelMap.get(offerItem.getFkInstitutionChannelId()));
                }
            } else if (TableEnum.SALE_STUDENT_INSURANCE.key.equals(receivablePlanVo.getFkTypeKey())) {
                StudentInsurance studentInsurance = studentInsuranceMap.get(receivablePlanVo.getFkTypeTargetId());
                receivablePlanVo.setFkBusinessChannelId(studentInsurance.getFkBusinessChannelId());
                if (GeneralTool.isNotEmpty(studentInsurance)) {
                    if(GeneralTool.isNotEmpty(studentInsurance.getType()) && studentInsurance.getType() == 1){
                        StringBuilder studentInfo = new StringBuilder();
                        if (GeneralTool.isNotEmpty(studentInsurance.getInsurantName())){
                            studentInfo.append(studentInsurance.getInsurantName());
                        }
                        if (GeneralTool.isNotEmpty(studentInsurance.getInsurantFirstName()) && GeneralTool.isNotEmpty(studentInsurance.getInsurantLastName())){
                            studentInfo.append("("+ studentInsurance.getInsurantLastName()+" "+studentInsurance.getInsurantFirstName()+")");
                        }
                        if (GeneralTool.isNotEmpty(studentInsurance.getInsurantPassportNum())){
                            studentInfo.append(studentInsurance.getInsurantPassportNum());
                        }
                        receivablePlanVo.setStudentInformation(studentInfo.toString());
                        receivablePlanVo.setStudentName(studentInsurance.getInsurantName());
                        receivablePlanVo.setStudentFirstName(studentInsurance.getInsurantFirstName());
                        receivablePlanVo.setStudentLastName(studentInsurance.getInsurantLastName());
                    }else {
                        studentId = studentInsurance.getFkStudentId();
                        //学生信息
                        Student student = studentMap.get(studentId);
                        if (GeneralTool.isNotEmpty(student)) {
                            receivablePlanVo.setStudentName(student.getName());
                            receivablePlanVo.setStudentFirstName(student.getFirstName());
                            receivablePlanVo.setStudentLastName(student.getLastName());
                            if (GeneralTool.isNotEmpty(student.getBirthday())) {
                                receivablePlanVo.setStudentBirthday(formatter.format(student.getBirthday()));
                            }
                            receivablePlanVo.setStudentNum(student.getNum());
                            String fullName = student.getName() + "（" + student.getLastName() + " " + student.getFirstName() + "）";
                            if (GeneralTool.isNotEmpty(studentId)) {
                                String passportNum = student.getPassportNum();
                                if (GeneralTool.isEmpty(passportNum)) {
                                    receivablePlanVo.setStudentInformation(fullName);
                                } else {
                                    receivablePlanVo.setStudentInformation(fullName + "/" + passportNum);
                                }
                            }
                        }
                    }

                    //国家名称
                    if (GeneralTool.isNotEmpty(studentInsurance.getFkAreaCountryId())) {
                        receivablePlanVo.setFkAreaCountryName(countryNameMap.get(studentInsurance.getFkAreaCountryId()));
                    }
                    String s = businessChannelNameMap.get(studentInsurance.getFkBusinessChannelId());
                    //渠道信息
                    receivablePlanVo.setChannelInformation(s);
                    //业务信息
                    receivablePlanVo.setBusinessInformation(s + "/" + formatter.format(studentInsurance.getInsuranceStartTime())
                            + "/" + formatter.format(studentInsurance.getInsuranceEndTime()));
                    receivablePlanVo.setIstOrApmName(formatter.format(studentInsurance.getInsuranceStartTime()));
                    receivablePlanVo.setShortNameOrApaStartTime(formatter.format(studentInsurance.getInsuranceEndTime()));
                }
            } else if (TableEnum.SALE_STUDENT_ACCOMMODATION.key.equals(receivablePlanVo.getFkTypeKey())) {
                StudentAccommodation studentAccommodation = studentAccommodationMap.get(receivablePlanVo.getFkTypeTargetId());
                receivablePlanVo.setFkBusinessChannelId(studentAccommodation.getFkBusinessChannelId());
                if (GeneralTool.isNotEmpty(studentAccommodation)) {
                    //学生信息
                    studentId = studentAccommodation.getFkStudentId();
                    Student student = studentMap.get(studentId);
                    if (GeneralTool.isNotEmpty(student)) {
                        receivablePlanVo.setStudentName(student.getName());
                        receivablePlanVo.setStudentFirstName(student.getFirstName());
                        receivablePlanVo.setStudentLastName(student.getLastName());
                        if (GeneralTool.isNotEmpty(student.getBirthday())) {
                            receivablePlanVo.setStudentBirthday(formatter.format(student.getBirthday()));
                        }
                        receivablePlanVo.setStudentNum(student.getNum());
                        String fullName = student.getName() + "（" + student.getLastName() + " " + student.getFirstName() + "）";
                        //原jack添加
                        if (GeneralTool.isNotEmpty(studentId)) {
                            Date studentBirthDay = student.getBirthday();
                            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                            if (GeneralTool.isEmpty(studentBirthDay)) {
                                receivablePlanVo.setStudentInformation(fullName);
                            } else {
                                String format = simpleDateFormat.format(studentBirthDay);
                                receivablePlanVo.setStudentInformation(fullName + "/" + format);
                            }
                        }
                    }
                    //国家名称
                    if (GeneralTool.isNotEmpty(studentAccommodation.getFkAreaCountryId())) {
                        receivablePlanVo.setFkAreaCountryName(countryNameMap.get(studentAccommodation.getFkAreaCountryId()));
                    }
                    //业务信息
                    receivablePlanVo.setBusinessInformation(businessChannelNameMap.get(studentAccommodation.getFkBusinessChannelId()) + "/" + studentAccommodation.getApartmentName()
                            + "/" + formatter.format(studentAccommodation.getCheckInDate()) + "/" + formatter.format(studentAccommodation.getCheckOutDate())
                            + "(" + studentAccommodation.getDuration() + ")");
                    //渠道信息
                    receivablePlanVo.setIstOrApmName(studentAccommodation.getApartmentName());
                    receivablePlanVo.setShortNameOrApaStartTime(formatter.format(studentAccommodation.getCheckInDate()));
                    receivablePlanVo.setChannelInformation(businessChannelNameMap.get(studentAccommodation.getFkBusinessChannelId()));
                }
            } else if(TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(receivablePlanVo.getFkTypeKey())){
                StudentServiceFeeVo serviceFeeDto = serviceFeeDtoMap.get(receivablePlanVo.getFkTypeTargetId());
                if (GeneralTool.isNotEmpty(serviceFeeDto)) {
                    receivablePlanVo.setReceivableName(feeTargetMap.get(receivablePlanVo.getFkTypeTargetId()));
                    studentId = serviceFeeDto.getFkStudentId();
                    //学生信息
                    Student student = studentMap.get(studentId);
                    if (GeneralTool.isNotEmpty(student)) {
                        receivablePlanVo.setStudentName(student.getName());
                        receivablePlanVo.setStudentFirstName(student.getFirstName());
                        receivablePlanVo.setStudentLastName(student.getLastName());
                        if (GeneralTool.isNotEmpty(student.getBirthday())) {
                            receivablePlanVo.setStudentBirthday(formatter.format(student.getBirthday()));
                        }
                        receivablePlanVo.setStudentNum(student.getNum());
                    }
                    receivablePlanVo.setFkInstitutionChannelName(serviceFeeDto.getServiceTypeName());
//                    receivablePlanVo.setSummary(serviceFeeDto.getRemark());
                    receivablePlanVo.setStudentInformation(serviceFeeDto.getFkStudentName());
                    receivablePlanVo.setFkAreaCountryName(serviceFeeDto.getFkAreaCountryName());
                    receivablePlanVo.setBusinessInformation(serviceFeeDto.getServiceTypeName());
                }
            }else if (TableEnum.INSTITUTION_PROVIDER_CHANNEL.key.equals(receivablePlanVo.getFkTypeKey())) {
                String channelName = institutionChannelMap.get(receivablePlanVo.getFkTypeTargetId());
                receivablePlanVo.setCompanyName(companyMap.get(receivablePlanVo.getFkCompanyId().toString()));
                receivablePlanVo.setChannelInformation("【" + channelName + "】");
                receivablePlanVo.setFkTypeTargetName(channelName);
            }else if (TableEnum.BUSINESS_CHANNEL_INS.key.equals(receivablePlanVo.getFkTypeKey())
                    || TableEnum.BUSINESS_CHANNEL_ACC.key.equals(receivablePlanVo.getFkTypeKey())) {
                String name = businessChannelNameMap.get(receivablePlanVo.getFkTypeTargetId());
                receivablePlanVo.setCompanyName(companyMap.get(receivablePlanVo.getFkCompanyId().toString()));
                receivablePlanVo.setChannelInformation("【" + name + "】");
                receivablePlanVo.setFkTypeTargetName(name);
            } else if (TableEnum.INSURANCE_ORDER.key.equals(receivablePlanVo.getFkTypeKey())) {
                InsuranceOrder insuranceOrder = insuranceOrderMap.get(receivablePlanVo.getFkTypeTargetId());
                StringBuilder studentName = new StringBuilder();
                studentName.append(insuranceOrder.getInsurantName()).append("（").append(insuranceOrder.getInsurantFirstName()).append(" ").append(insuranceOrder.getInsurantLastName()).append("）");
                receivablePlanVo.setStudentName(studentName.toString());
                ProductType productType = productTypeMap.get(insuranceOrder.getFkProductTypeId());

                //业务信息：产品名称：保单号，保单类型，时间范围
                //业务信息：安联：XXXXXXXXXXXX，Single，2025-05-30至2026-05-30
                StringBuilder studentInformation = new StringBuilder();
                studentInformation.append(productType.getTypeName()).append("（").append(productType.getTypeKey()).append("）")
                        .append("：").append(insuranceOrder.getInsuranceNum()).append("，").append(insuranceOrder.getInsuranceType()).append("，");
                studentInformation.append(formatter.format(insuranceOrder.getInsuranceStartTime())).append("至").append(formatter.format(insuranceOrder.getInsuranceEndTime()));
                receivablePlanVo.setInsuranceInfo(studentInformation.toString());
                if (GeneralTool.isNotEmpty(insuranceOrder.getFkAreaCountryIdTo())) {
                    receivablePlanVo.setFkAreaCountryName(countryNameMap.get(insuranceOrder.getFkAreaCountryIdTo()));
                }
            }else {
                //提供商和其他收入
                if (GeneralTool.isNotEmpty(institutionProviderSelectNamesByIds) && GeneralTool.isNotEmpty(institutionProviderSelectNamesByIds.get(receivablePlanVo.getFkTypeTargetId()))) {
                    String info = institutionProviderSelectNamesByIds.get(receivablePlanVo.getFkTypeTargetId());
                    receivablePlanVo.setChannelInformation(info.substring(info.lastIndexOf("】") + 1, info.indexOf("（")));
                    receivablePlanVo.setBusinessInformation(info);
                    //公司
                    receivablePlanVo.setCompanyName(companyMap.get(receivablePlanVo.getFkCompanyId().toString()));
                }

            }
            if (GeneralTool.isNotEmpty(receivablePlanVo.getFkCurrencyTypeNum())) {
                String currencyName = currencyNamesByNums.get(receivablePlanVo.getFkCurrencyTypeNum());
                receivablePlanVo.setFkCurrencyTypeName(currencyName);
            }
//            if (!TableEnum.INSTITUTION_PROVIDER.key.equals(receivablePlanVo.getFkTypeKey()) || !TableEnum.SALE_OTHER_INCOME.key.equals(receivablePlanVo.getFkTypeKey())) {
//                Long companyId = receivablePlanMapper.getCompanyIdByPlanId(receivablePlanVo.getId());
//                if (GeneralTool.isNotEmpty(companyId)) {
//                    receivablePlanVo.setCompanyName(companyMap.get(companyId.toString()));
//                    receivablePlanVo.setFkCompanyName(companyMap.get(companyId.toString()));
//                }
//            }
        }

    }

    private String getTargetNames(StudentOfferItemVo offerItem, String targetName) {
        StringJoiner stringJoiner = new StringJoiner("/");
        if (GeneralTool.isNotEmpty(offerItem.getNum())) {
            stringJoiner.add("【" + offerItem.getNum() + "】");
        }
        if (GeneralTool.isNotEmpty(targetName)) {
            stringJoiner.add(targetName);
        }
        if (GeneralTool.isNotEmpty(offerItem.getFkAreaCountryName())) {
            stringJoiner.add(offerItem.getFkAreaCountryName());
        }
        if (GeneralTool.isNotEmpty(offerItem.getFkInstitutionProviderName())) {
            String institutionProviderChannelName = null;
            if (GeneralTool.isNotEmpty(offerItem.getFkInstitutionChannelId())) {
                if (GeneralTool.isNotEmpty(offerItem.getFkInstitutionChannelId())) {
//                    institutionProviderChannelName = institutionCenterClient.getInstitutionProviderChannelById(offerItem.getFkInstitutionChannelId());
                    Result<String> result = institutionCenterClient.getInstitutionProviderChannelById(offerItem.getFkInstitutionChannelId());
                    if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                        institutionProviderChannelName = result.getData();
                    }
                }
            }
            stringJoiner.add(GeneralTool.isNotEmpty(institutionProviderChannelName) ? "【" + institutionProviderChannelName + "】" : "" + offerItem.getFkInstitutionProviderName());
        }
        if (GeneralTool.isNotEmpty(offerItem.getFkInstitutionName())) {
            stringJoiner.add(offerItem.getFkInstitutionName());
        }
        if (GeneralTool.isNotEmpty(offerItem.getFkCourseName())) {
            stringJoiner.add(offerItem.getFkCourseName());
        }
        return stringJoiner.toString();
    }

    private List<Long> getTypeTargetIds(ReceivablePlanDto receivablePlanDto) {
        List<Long> itemids = new ArrayList<>();
        List<Long> offerItems = offerItemService.getItemIdByStudentId(receivablePlanDto.getFkStudentId());
        List<Long> insurances = studentInsuranceMapper.getInsuranceIdsByStudentId(receivablePlanDto.getFkStudentId());
        List<Long> accommodations = studentAccommodationMapper.getAccommodationIdsByStudentId(receivablePlanDto.getFkStudentId());
        itemids.addAll(offerItems);
        itemids.addAll(insurances);
        itemids.addAll(accommodations);
        if (GeneralTool.isEmpty(itemids)) {
            itemids = new ArrayList<>();
            itemids.add(0L);
        }
        return itemids;
    }

    private Map<String, String> getCompanyMap() {
        Map<String, String> companyMap = new HashMap<>(5);
        Result<List<com.get.permissioncenter.vo.tree.CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
            JsonConfig config = new JsonConfig();
            config.setExcludes(new String[]{"departmentTree", "totalNum"});
            JSONArray jsonArray = JSONArray.fromObject(result.getData(), config);
            List<CompanyTreeVo> companyTreeVos = JSONArray.toList(jsonArray, new CompanyTreeVo(), new JsonConfig());
            if (GeneralTool.isNotEmpty(companyTreeVos)) {
                companyMap = companyTreeVos.stream().collect(Collectors.toMap(CompanyTreeVo::getId, CompanyTreeVo::getShortName));
            }
        }
        return companyMap;
    }

    /**
     * @return
     * @Description：feign 根据应收计划id查询应收计划应收金额
     * @Param
     * @Date 14:10 2021/4/23
     * <AUTHOR>
     */
    @Override
    public BigDecimal getReceivablePlanAmountById(Long id) {
        ReceivablePlan receivablePlan = receivablePlanMapper.selectById(id);
        if (receivablePlan == null) {
            return BigDecimal.ZERO;
        }
        return receivablePlan.getReceivableAmount();
    }

    /**
     * 根据fkProviderId获取应收计划列表
     *
     * @param fkProviderId
     * @return
     */
    @Override
    public List<ReceivablePlanVo> getReceivablePlanDtosByProviderId(Long fkProviderId) {
        return receivablePlanMapper.getReceivablePlanDtosByProviderId(fkProviderId);
    }

    @Override
    public void deleteReceivablePlanByItemId(Long id) {
//        Example example = new Example(ReceivablePlan.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkTypeTargetId", id);
//        criteria.andEqualTo("fkTypeKey", TableEnum.SALE_STUDENT_OFFER_ITEM.key);
//        criteria.andEqualTo("status", 0);
        int j = receivablePlanMapper.delete(Wrappers.<ReceivablePlan>lambdaQuery()
                .eq(ReceivablePlan::getFkTypeTargetId, id)
                .eq(ReceivablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_OFFER_ITEM.key)
                .eq(ReceivablePlan::getStatus, 0));
        if (j < 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

//    @Override
//    public void exportReceivablePlanExcel(HttpServletResponse response, ReceivablePlanDto receivablePlanVo) {
//        List<ReceivablePlanVo> receivablePlans = getReceivablePlan(receivablePlanVo, null, null);
//        List<ReceivablePlanExportVo> receivablePlanExportDtos = receivablePlans.stream().map(receivablePlanDto -> {
//            ReceivablePlanExportVo receivablePlanExportDto = new ReceivablePlanExportVo();
//            BeanCopyUtils.copyProperties(receivablePlanDto, receivablePlanExportDto);
//            if (receivablePlanDto.getStatus() == 0) {
//                receivablePlanExportDto.setStatusName("作废");
//            } else if (receivablePlanDto.getStatus() == 1) {
//                receivablePlanExportDto.setStatusName("有效");
//            } else {
//                receivablePlanExportDto.setStatusName("完成");
//            }
//            return receivablePlanExportDto;
//        }).collect(Collectors.toList());
//
//        FileUtils.exportExcelNotWrapText(response, receivablePlanExportDtos, "ReceivablePlan", ReceivablePlanExportVo.class);
//    }

    @Override
    public void exportReceivablePlanExcel(HttpServletResponse response, ReceivablePlanNewDto receivablePlanNewDto) {
        List<ReceivablePlanNewVo> receivablePlanNewVos = getReceivablePlanNew(receivablePlanNewDto, null, null);

        List<ReceivablePlanExportVo> receivablePlanExportVos = receivablePlanNewVos.stream().map(receivablePlanDto -> {
            ReceivablePlanExportVo receivablePlanExportVo = new ReceivablePlanExportVo();
            BeanCopyUtils.copyProperties(receivablePlanDto, receivablePlanExportVo);
            if (GeneralTool.isNotEmpty(receivablePlanDto.getCommissionMark())) {
                receivablePlanExportVo.setCommissionMark("【" + receivablePlanDto.getCommissionMark() + "】");
            }
            if (GeneralTool.isNotEmpty(receivablePlanDto.getBonusType())) {
                receivablePlanExportVo.setBonusTypeName(ProjectExtraEnum.getValueByKey(receivablePlanDto.getBonusType(), ProjectExtraEnum.BONUS_TYPE));
            }
            if (receivablePlanDto.getStatus() == 0) {
                receivablePlanExportVo.setStatusName("作废");
            } else if (receivablePlanDto.getStatus() == 1) {
                receivablePlanExportVo.setStatusName("有效");
            } else {
                receivablePlanExportVo.setStatusName("完成");
            }
            receivablePlanExportVo.setBdAgentNum(receivablePlanDto.getAgentNum());
            receivablePlanExportVo.setProviderName(receivablePlanDto.getFkInstitutionProviderName());
            receivablePlanExportVo.setStudentNameEn(receivablePlanExportVo.getStudentName());
            if (GeneralTool.isNotEmpty(receivablePlanExportVo.getStudentName()) && GeneralTool.isNotEmpty(receivablePlanDto.getStudentLastName())) {
                receivablePlanExportVo.setStudentNameEng( receivablePlanDto.getStudentLastName() + " " + receivablePlanDto.getStudentFirstName());
            }
            if (GeneralTool.isNotEmpty(receivablePlanDto.getFkInstitutionChannelName()) && GeneralTool.isNotEmpty(receivablePlanDto.getFkInstitutionProviderName())) {
                receivablePlanExportVo.setFkInstitutionProviderName(receivablePlanDto.getFkInstitutionChannelName() + "/" + receivablePlanDto.getFkInstitutionProviderName());
            }
            if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(receivablePlanDto.getFkTypeKey())) {
                if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(receivablePlanNewDto.getFkTypeKey())){
                    receivablePlanExportVo.setBusinessInformation(receivablePlanDto.getIstOrApmName());
                    if (GeneralTool.isNotEmpty(receivablePlanDto.getShortNameOrApaStartTime())) {
                        receivablePlanExportVo.setBusinessInformation(receivablePlanExportVo.getBusinessInformation() + "(" + receivablePlanDto.getShortNameOrApaStartTime() + ")");
                    }
                    receivablePlanExportVo.setFkInstitutionProviderName(receivablePlanDto.getFkInstitutionChannelName());
                    receivablePlanExportVo.setFkInstitutionName(receivablePlanDto.getFkInstitutionProviderName());
                    if (GeneralTool.isNotEmpty(receivablePlanDto.getStudentLastName())){
                        receivablePlanExportVo.setStudentFirstName(receivablePlanDto.getStudentLastName() + " " + receivablePlanDto.getStudentFirstName());
                    }
                    if (GeneralTool.isNotEmpty(receivablePlanDto.getDeferOpeningTime())){
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        receivablePlanExportVo.setDeferOpeningTime(sdf.format(receivablePlanDto.getDeferOpeningTime()));
                    }
                }else {
                    receivablePlanExportVo.setBusinessInformation(receivablePlanDto.getIstOrApmName());
                    if(GeneralTool.isNotEmpty(receivablePlanDto.getShortNameOrApaStartTime())){
                        receivablePlanExportVo.setBusinessInformation(receivablePlanExportVo.getBusinessInformation()+"("+receivablePlanDto.getShortNameOrApaStartTime()+")");
                    }
                    if (GeneralTool.isNotEmpty(receivablePlanDto.getFkInstitutionCourseId())) {
                        if (-1L == receivablePlanDto.getFkInstitutionCourseId()) {
                            receivablePlanExportVo.setBusinessInformation(receivablePlanExportVo.getBusinessInformation() + "/" + receivablePlanDto.getOldCourseCustomName());
                        } else {
                            receivablePlanExportVo.setBusinessInformation(receivablePlanExportVo.getBusinessInformation() + "/" + receivablePlanDto.getFkCourseName());
                        }
                    }
                    if (GeneralTool.isNotEmpty(receivablePlanDto.getDuration()) && GeneralTool.isNotEmpty(receivablePlanDto.getDurationType())) {
                        receivablePlanExportVo.setBusinessInformation(receivablePlanExportVo.getBusinessInformation() + "（" + receivablePlanDto.getDuration() + ProjectExtraEnum.getValueByKey(receivablePlanDto.getDurationType(), ProjectExtraEnum.DURATION_TYPE) + "）");
                    }
                    if (GeneralTool.isNotEmpty(receivablePlanDto.getDeferOpeningTime())){
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        receivablePlanExportVo.setBusinessInformation(receivablePlanExportVo.getBusinessInformation()+"/"+sdf.format(receivablePlanDto.getDeferOpeningTime()));
                        receivablePlanExportVo.setDeferOpeningTime(sdf.format(receivablePlanDto.getDeferOpeningTime()));
                    }
                }
//                if (GeneralTool.isNotEmpty(receivablePlanDto.getStudentBirthday())) {
//                    receivablePlanExportVo.setStudentName(receivablePlanExportVo.getStudentName() + "/" +receivablePlanDto.getGenderName() +"/"
//                            + receivablePlanDto.getStudentBirthday()+"/"+receivablePlanDto.getStudentNum());
//                }
//                if (GeneralTool.isNotEmpty(receivablePlanDto.getFkCourseName())) {
//                    receivablePlanExportVo.setBusinessInformation(receivablePlanExportVo.getBusinessInformation() + "/" + receivablePlanDto.getFkCourseName());
//                }
//                if (GeneralTool.isNotEmpty(receivablePlanDto.getOpeningTime())) {
//                    receivablePlanExportVo.setBusinessInformation(receivablePlanExportVo.getBusinessInformation() + "/" + receivablePlanDto.getOpeningTime());
//                }
            } else if (TableEnum.SALE_STUDENT_INSURANCE.key.equals(receivablePlanDto.getFkTypeKey())) {
                receivablePlanExportVo.setBusinessInformation(receivablePlanDto.getIstOrApmName());
                if (GeneralTool.isNotEmpty(receivablePlanDto.getShortNameOrApaStartTime())) {
                    receivablePlanExportVo.setBusinessInformation(receivablePlanExportVo.getBusinessInformation() + "-" + receivablePlanDto.getShortNameOrApaStartTime());
                }
            } else if (TableEnum.SALE_STUDENT_ACCOMMODATION.key.equals(receivablePlanDto.getFkTypeKey())) {
                receivablePlanExportVo.setBusinessInformation(receivablePlanDto.getIstOrApmName());
                if (GeneralTool.isNotEmpty(receivablePlanDto.getShortNameOrApaStartTime())) {
                    receivablePlanExportVo.setBusinessInformation(receivablePlanExportVo.getBusinessInformation() + "(" + receivablePlanDto.getShortNameOrApaStartTime() + ")");
                }
                if (GeneralTool.isNotEmpty(receivablePlanDto.getFkCourseName())) {
                    receivablePlanExportVo.setBusinessInformation(receivablePlanExportVo.getBusinessInformation() + "/" + receivablePlanDto.getFkCourseName());
                }
                if (GeneralTool.isNotEmpty(receivablePlanDto.getStuCnApaDay())) {
                    receivablePlanExportVo.setBusinessInformation(receivablePlanExportVo.getBusinessInformation() + "/" + receivablePlanDto.getStuCnApaDay());
                }
            }else if (TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(receivablePlanDto.getFkTypeKey())) {
                receivablePlanExportVo.setBusinessInformation(receivablePlanDto.getFkInstitutionChannelName());
            }else if (TableEnum.BUSINESS_CHANNEL_INS.key.equals(receivablePlanDto.getFkTypeKey())) {
                receivablePlanExportVo.setFkInstitutionProviderName(receivablePlanDto.getFkInstitutionChannelName());
                receivablePlanExportVo.setBusinessInformation(receivablePlanDto.getFkInstitutionChannelName());
            }else if (TableEnum.BUSINESS_CHANNEL_ACC.key.equals(receivablePlanDto.getFkTypeKey())) {
                receivablePlanExportVo.setFkInstitutionProviderName(receivablePlanDto.getFkInstitutionChannelName());
                receivablePlanExportVo.setBusinessInformation(receivablePlanDto.getIstOrApmName());
            }else if(TableEnum.INSTITUTION_PROVIDER_CHANNEL.key.equals(receivablePlanDto.getFkTypeKey())){
                receivablePlanExportVo.setFkInstitutionProviderName(receivablePlanDto.getFkInstitutionChannelName());
            }
            return receivablePlanExportVo;
        }).collect(Collectors.toList());

        // STUDENT_TO_CLIENT_APPROVER_DEFAULT
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.STUDENT_TO_CLIENT_APPROVER_DEFAULT.key, 1).getData();
        // 是否显示佣金结算标记
        boolean commissionMarkFlag = false;
        if (GeneralTool.isNotEmpty(companyConfigMap)) {
            String configValue = companyConfigMap.get(SecureUtil.getFkCompanyId());
            if (GeneralTool.isNotEmpty(configValue)){
                if (Long.parseLong(configValue) > 0) {
                    commissionMarkFlag = true;
                }
            }
        }

        Set<String> ignoreFields = new HashSet<>();
        if (!commissionMarkFlag){
            ignoreFields.add("commissionMark");
        }
        if (SecureUtil.getFkCompanyId().equals(3L)){
            //hit导出忽略字段
            ignoreFields.add("studentNameEn");
            ignoreFields.add("studentFirstName");
            ignoreFields.add("agentNum");
        }else {
            //gea忽略导出字段
            ignoreFields.add("offerItemNum");
            ignoreFields.add("bdAgentNum");
            ignoreFields.add("fkInstitutionChannelName");
            ignoreFields.add("providerName");

            if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(receivablePlanNewDto.getFkTypeKey())){
//            ignoreFields.add("studentName");
                ignoreFields.add("studentFirstName");
                ignoreFields.add("studentNameEn");
//            Map<String, String> fileMap = FileUtils.getFileMapIgnoreSomeField(ReceivablePlanExportVo.class, ignoreFields);
//            FileUtils.exportExcel(response, receivablePlanExportVos, "ReceivablePlan", fileMap);
//            FileUtils.exportExcelNotWrapText(response, receivablePlanExportVos, "ReceivablePlan", ReceivablePlanExportVo.class);
            }else {
                ignoreFields.add("studentFirstName");
//            ignoreFields.add("genderName");
//            ignoreFields.add("studentBirthday");
                ignoreFields.add("studentNum");
                ignoreFields.add("fkInstitutionName");
                ignoreFields.add("fkCourseName");
                ignoreFields.add("openingTime");
                ignoreFields.add("studentNameEn");
//            Map<String, String> fileMap = FileUtils.getFileMapIgnoreSomeField(ReceivablePlanExportVo.class, ignoreFields);
//            FileUtils.exportExcel(response, receivablePlanExportVos, "ReceivablePlan", fileMap);
//            FileUtils.exportExcelNotWrapText(response, receivablePlanExportVos, "ReceivablePlan", ReceivablePlanExportVo.class);
            }

        }
        Map<String, String> fileMap = FileUtils.getFileMapIgnoreSomeField(ReceivablePlanExportVo.class, ignoreFields);
        FileUtils.exportExcel(response, receivablePlanExportVos, "ReceivablePlan", fileMap);

    }

    /**
     * 根据应付计划信息获取对应应收计划下拉框数据
     *
     * @param fkTypeKey
     * @param fkTypeTargetId
     * @Date 11:03 2022/4/6
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> getReceivablePlanByPayablePlanInfo(String fkTypeKey, Long fkTypeTargetId) {
        List<BaseSelectEntity> baseSelectEntities = new ArrayList<>();
        List<ReceivablePlan> receivablePlans = receivablePlanMapper.getReceivablePlanByPayablePlanInfo(fkTypeKey, fkTypeTargetId);
        if (GeneralTool.isNotEmpty(receivablePlans)) {
            for (ReceivablePlan receivablePlan : receivablePlans) {
                BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
                baseSelectEntity.setId(receivablePlan.getId());
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append("应收：").append(receivablePlan.getReceivableAmount()).append("/");
                if (GeneralTool.isNotEmpty(receivablePlan.getBonusType())) {
                    stringBuilder.append(ProjectExtraEnum.getValueByKey(receivablePlan.getBonusType(), ProjectExtraEnum.BONUS_TYPE)).append("/");
                }
                stringBuilder.append(receivablePlan.getFkCurrencyTypeNum()).append("/").append("摘要:").append(GeneralTool.isEmpty(receivablePlan.getSummary()) ? "无" : receivablePlan.getSummary());
                baseSelectEntity.setFullName(stringBuilder.toString());
                baseSelectEntities.add(baseSelectEntity);
            }
        }
        return baseSelectEntities;
    }

    /**
     * 合同公式获取N阶段佣金业务场景 - 佣金期数
     *
     * @Date 12:54 2021/6/9
     * <AUTHOR>
     */
    enum ConditionTypeAndStepsEnum {
        /**
         * 获得第一阶段佣金
         */
        GET_THE_FIRST_STAGE_COMMISSION(ProjectExtraEnum.GET_THE_FIRST_STAGE_COMMISSION.key, 1, "获得第一阶段佣金"),
        /**
         * 获得第二阶段佣金
         */
        GET_THE_SECOND_STAGE_COMMISSION(ProjectExtraEnum.GET_THE_SECOND_STAGE_COMMISSION.key, 2, "获得第二阶段佣金"),
        /**
         * 获得第三阶段佣金
         */
        GET_THE_THIRD_STAGE_COMMISSION(ProjectExtraEnum.GET_THE_THIRD_STAGE_COMMISSION.key, 3, "获得第三阶段佣金"),
        /**
         * 获得第四阶段佣金
         */
        GET_THE_FOURTH_STAGE_COMMISSION(ProjectExtraEnum.GET_THE_FOURTH_STAGE_COMMISSION.key, 4, "获得第四阶段佣金");
        /**
         * 业务场景
         */
        public Integer conditionType;
        /**
         * 期数
         */
        public Integer step;
        /**
         * 描述
         */
        public String value;


        ConditionTypeAndStepsEnum(Integer conditionType, Integer step, String value) {
            this.conditionType = conditionType;
            this.step = step;
            this.value = value;
        }

        public static String getValue(String conditionType) {
            ConditionTypeAndStepsEnum[] conditionTypeAndStepsEnums = values();
            for (ConditionTypeAndStepsEnum conditionTypeAndStepsEnum : conditionTypeAndStepsEnums) {
                if (conditionTypeAndStepsEnum.conditionType().equals(Integer.valueOf(conditionType))) {
                    return conditionTypeAndStepsEnum.value();
                }
            }
            return null;
        }

        public static Integer getStep(String conditionType) {
            ConditionTypeAndStepsEnum[] conditionTypeAndStepsEnums = values();
            for (ConditionTypeAndStepsEnum conditionTypeAndStepsEnum : conditionTypeAndStepsEnums) {
                if (conditionTypeAndStepsEnum.conditionType().equals(Integer.valueOf(conditionType))) {
                    return conditionTypeAndStepsEnum.step();
                }
            }
            return null;
        }

        private Integer conditionType() {
            return this.conditionType;
        }

        private String value() {
            return this.value;
        }

        private Integer step() {
            return this.step;
        }
    }

    @Override
    public List<ReceivablePlanNewVo> getReceivablePlanNew(ReceivablePlanNewDto receivablePlanNewDto, SearchBean<ReceivablePlanNewDto> page, String[] times) {
        List<ReceivablePlanNewVo> collect;
        long fStartTime = System.currentTimeMillis();
        String studentName = receivablePlanNewDto.getStudentName();
        if (StringUtils.isNotBlank(studentName)) {
            receivablePlanNewDto.setStudentName(studentName.replace(" ", "").trim());
        }

        //针对like的字段转为小写
        if(GeneralTool.isNotEmpty(receivablePlanNewDto.getStudentName()))
        {
            receivablePlanNewDto.setStudentName(receivablePlanNewDto.getStudentName().toLowerCase());
        }
        if(GeneralTool.isNotEmpty(receivablePlanNewDto.getBzoName()))
        {
            receivablePlanNewDto.setBzoName(receivablePlanNewDto.getBzoName().toLowerCase());
        }
        if(GeneralTool.isNotEmpty(receivablePlanNewDto.getBziName()))
        {
            receivablePlanNewDto.setBziName(receivablePlanNewDto.getBziName().toLowerCase());
        }
        if(GeneralTool.isNotEmpty(receivablePlanNewDto.getBziNameSupplement()))
        {
            receivablePlanNewDto.setBziNameSupplement(receivablePlanNewDto.getBziNameSupplement().toLowerCase());
        }
        if(GeneralTool.isNotEmpty(receivablePlanNewDto.getFkInvoiceNums()))
        {
            receivablePlanNewDto.setFkInvoiceNums(receivablePlanNewDto.getFkInvoiceNums().toLowerCase().replace("'","_"));
        }
        if(GeneralTool.isNotEmpty(receivablePlanNewDto.getSummary()))
        {
            receivablePlanNewDto.setSummary(receivablePlanNewDto.getSummary().replace(" ", "").trim());
        }
        if(GeneralTool.isNotEmpty(receivablePlanNewDto.getSummary()))
        {
            receivablePlanNewDto.setSummary(receivablePlanNewDto.getSummary().toLowerCase());
        }
        if (GeneralTool.isNotEmpty(receivablePlanNewDto.getCommissionMark())) {
            receivablePlanNewDto.setCommissionMark(receivablePlanNewDto.getCommissionMark().toLowerCase());
        }
        /**
         * OS晚于开学时间，设置2个时间段的提醒，【一般】已有，增加多一个【严重】高亮提示
         * 成功客户列表--分两个颜色（3个月/6个月）--并且需要置顶（6个月优先于3个月）
         */

        Map<Long, CompanyConfigAnalysisVo> configMap = permissionCenterClient.getCompanyConfigAnalysis(ProjectKeyEnum.AR_LIST_LATE_OS_REMIND.key).getData();
        if (GeneralTool.isNotEmpty(configMap) && configMap.containsKey(SecureUtil.getFkCompanyId())){
            ConfigRemindDto configRemindDto = new ConfigRemindDto();
            CompanyConfigAnalysisVo companyConfigAnalysisVo = configMap.get(SecureUtil.getFkCompanyId());
            configRemindDto.setReminderTime(DateUtil.parse(companyConfigAnalysisVo.getValue1(),"yyyy-MM-dd HH:mm:ss"));
            configRemindDto.setReminderDay2(Long.parseLong(companyConfigAnalysisVo.getValue2()));
            configRemindDto.setReminderDay3(Long.parseLong(companyConfigAnalysisVo.getValue3()));
            receivablePlanNewDto.setConfigRemindVo(configRemindDto);
        }
//        ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.AR_LIST_LATE_OS_REMIND.key).getData();
//        if (GeneralTool.isNotEmpty(configDto)){
//            ConfigRemindDto configRemindVo = new ConfigRemindDto();
//            String value1 = configDto.getValue1();
//            configRemindVo.setReminderTime(DateUtil.parse(value1,"yyyy-MM-dd HH:mm:ss"));
//
//            JSONObject jsonObject2 = JSONObject.parseObject(configDto.getValue2());
//            String geaDayString = jsonObject2.getString("OTHER");
//            String iaeDayString = jsonObject2.getString("IAE");
//            JSONObject jsonObject3 = JSONObject.parseObject(configDto.getValue3());
//            String geaDayString3 = jsonObject3.getString("OTHER");
//            String iaeDayString3 = jsonObject3.getString("IAE");
//
//            configRemindVo.setGeaDay2(Long.parseLong(geaDayString));
//            configRemindVo.setIaeDay2(Long.parseLong(iaeDayString));
//            configRemindVo.setGeaDay3(Long.parseLong(geaDayString3));
//            configRemindVo.setIaeDay3(Long.parseLong(iaeDayString3));
//            receivablePlanNewDto.setConfigRemindVo(configRemindVo);
//        }

        if (GeneralTool.isNotEmpty(receivablePlanNewDto.getMajorLevelId())) {
            receivablePlanNewDto.setFkTypeKey(TableEnum.SALE_STUDENT_OFFER_ITEM.key);
        }
        if (receivablePlanNewDto.getIsInvoiceFlag() && GeneralTool.isEmpty(receivablePlanNewDto.getFkInvoiceId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (page != null) {
            IPage<ReceivablePlanNewVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            collect = dorisService.getDorisReceivablePlanNewDtos(receivablePlanNewDto, iPage);
            page.setAll((int) iPage.getTotal());
        } else {
            collect = dorisService.getDorisReceivablePlanNewDtos(receivablePlanNewDto, null);
        }
        if (collect.isEmpty()) {
            return Collections.emptyList();
        }
        Set<Long> ids = collect.stream().filter(f -> TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(f.getFkTypeKey())).map(ReceivablePlanNewVo::getFkTypeTargetId).collect(Collectors.toSet());
        if (GeneralTool.isEmpty(ids)){
            ids.add(0L);
        }

        //延迟入学时间
        Map<Long, Date> maxDeferEntranceTimesMap = new HashMap<>();
        maxDeferEntranceTimesMap = studentOfferItemDeferEntranceService.getMaxDeferEntranceTimesMap(ids);

        List<SelItem> stepByIds = studentOfferItemMapper.getStepByIds(ids);
        Map<Long, Object> convert = ConvertUtils.convert(stepByIds);
        long fEndTime = System.currentTimeMillis();
//        Map<Long, String> fkInvoiceNum = new HashMap<>();
        Set<Long> planIds = collect.stream().map(ReceivablePlanNewVo::getId).collect(Collectors.toSet());
        List<ReceivablePlanDate> receivablePlanDates = receivablePlanDateMapper.selectList(Wrappers.<ReceivablePlanDate>lambdaQuery().in(ReceivablePlanDate::getFkReceivablePlanId, planIds));
        Map<Long, List<ReceivablePlanDate>> planDateMap = receivablePlanDates.stream().collect(Collectors.groupingBy(ReceivablePlanDate::getFkReceivablePlanId));

        Map<Long, String> invoiceNumByReceivableId = financeCenterClient.getInvoiceNumByReceivableId(planIds);
        if (GeneralTool.isEmpty(planIds)) {
            planIds.add(0L);
        }

        List<PlanRemarkDetailResultVo> planRemarkDetailResultVos = receivablePlanMapper.getReceivablePlanRemarkDetails(planIds);
        Map<Long, List<SaleComment>> studentCommentMap = Maps.newHashMap();
        Map<Long, List<SaleComment>> studentOfferItemCommentMap = Maps.newHashMap();
        Map<Long, PlanRemarkDetailResultVo> planRemarkDetailResultDtoMap = Maps.newHashMap();
        if(GeneralTool.isNotEmpty(planRemarkDetailResultVos)){
            planRemarkDetailResultDtoMap = planRemarkDetailResultVos.stream().collect(HashMap::new, (m, v) -> m.put(v.getId(), v), HashMap::putAll);
            Set<Long> studentIds = planRemarkDetailResultVos.stream().map(PlanRemarkDetailResultVo::getFkStudentId).filter(Objects::nonNull).collect(Collectors.toSet());
            if (GeneralTool.isEmpty(studentIds)){
                studentIds.add(0L);
            }
            Set<Long> studentOfferItemIds = planRemarkDetailResultVos.stream().map(PlanRemarkDetailResultVo::getFkStudentOfferItemId).filter(Objects::nonNull).collect(Collectors.toSet());
            if (GeneralTool.isEmpty(studentOfferItemIds)){
                studentOfferItemIds.add(0L);
            }
            List<SaleComment> studentComments = commentService.list(Wrappers.<SaleComment>lambdaQuery()
                    .in(SaleComment::getFkTableId, studentIds)
                    .eq(SaleComment::getFkTableName, TableEnum.SALE_STUDENT.key));
            List<SaleComment> studentOfferItemComments = commentService.list(Wrappers.<SaleComment>lambdaQuery()
                    .in(SaleComment::getFkTableId, studentOfferItemIds)
                    .eq(SaleComment::getFkTableName, TableEnum.SALE_STUDENT_OFFER_ITEM.key));

            if (GeneralTool.isNotEmpty(studentComments)){
                studentCommentMap = studentComments.stream().collect(Collectors.groupingBy(SaleComment::getFkTableId));
            }
            if (GeneralTool.isNotEmpty(studentOfferItemComments)){
                studentOfferItemCommentMap = studentOfferItemComments.stream().collect(Collectors.groupingBy(SaleComment::getFkTableId));
            }
        }

        //fkInvoiceNum = financeCenterClient.getFkInvoiceNum(planIds);
        Set<Long> countryIds = Arrays.stream(collect.stream()
                .filter(f -> TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(f.getFkTypeKey()))
                .map(ReceivablePlanNewVo::getFkAreaCountryIds)
                .collect(Collectors.joining(",")).split(",")).filter(StringUtils::isNotBlank).map(Long::valueOf).collect(Collectors.toSet());
        Map<Long, String> country = institutionCenterClient.getCountryFullNamesByIds(countryIds).getData();
        Map<Long,String> map = Collections.emptyMap();
        Map<Long, PayablePlan> payMap = Collections.emptyMap();
        Map<String, String> currencyNumsMap = Collections.emptyMap();
        List<ReceiptFormVo> receiptFormVos = financeCenterClient.getReceiptFormListFeignByPlanIds(planIds);
        Set<String> receiptCurrencyNums = receiptFormVos.stream().map(ReceiptFormVo::getFkCurrencyTypeNum).collect(Collectors.toSet());
//        HashMap<Long, Date> paymentDate = new HashMap<>();
        Map<Long, String> bdMap = new HashMap<>();
        Set<Long> bdIds = collect.stream().map(ReceivablePlanNewVo::getFkStaffId).collect(Collectors.toSet());
        List<StaffBdCode> staffBdCodes = staffBdCodeMapper.selectList(Wrappers.<StaffBdCode>lambdaQuery().in(StaffBdCode::getFkStaffId,bdIds));
        bdMap = staffBdCodes.stream().collect(Collectors.toMap(StaffBdCode::getFkStaffId, StaffBdCode::getBdCode));

        if (receivablePlanNewDto.getIsInvoiceFlag()) {
            map = financeCenterClient.getInvoiceCommissionNotice(receivablePlanNewDto.getFkInvoiceId());
            if (GeneralTool.isNotEmpty(planIds)) {
                List<PayablePlan> planList = payablePlanMapper.selectList(Wrappers.<PayablePlan>lambdaQuery().in(PayablePlan::getFkReceivablePlanId, planIds));
                receiptCurrencyNums.addAll(planList.stream().map(PayablePlan::getFkCurrencyTypeNum).collect(Collectors.toSet()));
                currencyNumsMap = financeCenterClient.getCurrencyNamesByNums(receiptCurrencyNums).getData();
                payMap = planList.stream().collect(HashMap<Long, PayablePlan>::new, (m, v) -> m.put(v.getFkReceivablePlanId(), v), HashMap::putAll);
//                List<ReceiptFormItemDto> receiptFormItems = financeCenterClient.getReceiptFormByInvoiceId(receivablePlanNewDto.getFkInvoiceId());
//                paymentDate = receiptFormItems.stream().collect(HashMap<Long, Date>::new, (m, v) -> m.put(v.getFkReceivablePlanId(), v.getReceiptDate()), HashMap::putAll);
            }
        } else {
            currencyNumsMap = financeCenterClient.getCurrencyNamesByNums(receiptCurrencyNums).getData();
        }
        Set<Long> agentIds = collect.stream().filter(Objects::nonNull).map(ReceivablePlanNewVo::getFkAgentId).collect(Collectors.toSet());
        Map<Long, Agent> agents = agentService.getAgentsByIds(agentIds);

        List<Long> feeIds = collect.stream().filter(f -> TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(f.getFkTypeKey())).map(ReceivablePlanNewVo::getFkTypeTargetId).collect(Collectors.toList());
        Map<Long, String> feeTargetMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(feeIds)) {
            List<StudentServiceFeeVo> studentServiceFeeVos = studentServiceFeeMapper.getFeeTargetName(feeIds);
            studentServiceFeeVos.forEach(studentServiceFeeDto -> {
                feeTargetMap.put(studentServiceFeeDto.getId(),  studentServiceFeeDto.getReceivableName());
            });
        }

        for (ReceivablePlanNewVo receivablePlanNewVo : collect) {
//            if (GeneralTool.isNotEmpty(fkInvoiceNum)&&GeneralTool.isNotEmpty(fkInvoiceNum.get(receivablePlanNewVo.getId()))){
//                receivablePlanNewVo.setFkInvoiceNums(fkInvoiceNum.get(receivablePlanNewVo.getId()));
//            }
            if (GeneralTool.isNotEmpty(receivablePlanNewVo.getIstOrApmName()) && receivablePlanNewVo.getFkTypeKey().equals(TableEnum.SALE_STUDENT_OFFER_ITEM.key)){
                receivablePlanNewVo.setFkInstitutionName(receivablePlanNewVo.getIstOrApmName());
            }

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            List<ReceivablePlanDate> planDates = planDateMap.get(receivablePlanNewVo.getId());
            if (GeneralTool.isNotEmpty(planDates)) {
                String planDateStr = planDates.stream().sorted(Comparator.comparing(ReceivablePlanDate::getReceivablePlanDate)).map(p -> sdf.format(p.getReceivablePlanDate())).collect(Collectors.joining("，"));
                receivablePlanNewVo.setReceivablePlanDateStr(planDateStr);
            }
            String fkTypeKey = receivablePlanNewVo.getFkTypeKey();
            if (TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(fkTypeKey)
                    ||TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(fkTypeKey)
                    ||TableEnum.SALE_STUDENT_INSURANCE.key.equals(fkTypeKey)
                    ||TableEnum.SALE_STUDENT_ACCOMMODATION.key.equals(fkTypeKey)) {
                Agent agent = agents.get(receivablePlanNewVo.getFkAgentId());
                if (GeneralTool.isNotEmpty(agent)) {
                    receivablePlanNewVo.setAgentName(agent.getName());
                    receivablePlanNewVo.setAgentNum(agent.getNum());
                    receivablePlanNewVo.setAgentNameNote(agent.getNameNote());
                }
            }
            if (TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(fkTypeKey)) {
                StringBuilder countryName = new StringBuilder();
                String fkAreaCountryIds = receivablePlanNewVo.getFkAreaCountryIds();
                if (StringUtils.isNotBlank(fkAreaCountryIds)) {
                    String[] split = fkAreaCountryIds.split(",");
                    for (String s : split) {
                        countryName.append(country.get(Long.valueOf(s))).append("，");
                    }
                    countryName.delete(countryName.length()-1,countryName.length());
                    receivablePlanNewVo.setFkAreaCountryName(countryName.toString());
                }
                receivablePlanNewVo.setReceivableName(feeTargetMap.get(receivablePlanNewVo.getFkTypeTargetId()));
            }
            String bdCode = bdMap.get(receivablePlanNewVo.getFkStaffId());
            if (StringUtils.isNotBlank(bdCode) && StringUtils.isNotBlank(receivablePlanNewVo.getAgentNum())) {
                receivablePlanNewVo.setAgentNum(bdCode + receivablePlanNewVo.getAgentNum());
            }
            if (receivablePlanNewDto.getIsInvoiceFlag()) {
                receivablePlanNewVo.setCommissionNotice(map.get(receivablePlanNewVo.getId()));
//                Date date = paymentDate.get(receivablePlanNewVo.getId());
//                if (GeneralTool.isNotEmpty(date)) {
//                    receivablePlanNewVo.setPaymentDate(sdf.format(date));
//                }
//                String bdCode = bdMap.get(receivablePlanNewVo.getFkStaffId());
//                if (StringUtils.isNotBlank(bdCode) && StringUtils.isNotBlank(receivablePlanNewVo.getAgentNum())) {
//                    receivablePlanNewVo.setAgentNum(bdCode + receivablePlanNewVo.getAgentNum());
//                }
                PayablePlan payablePlan = payMap.get(receivablePlanNewVo.getId());
                if (GeneralTool.isNotEmpty(payablePlan)) {
                    receivablePlanNewVo.setExistPayable(true);
                    receivablePlanNewVo.setPayablePlanId(payablePlan.getId());
                    receivablePlanNewVo.setPayableAmount(payablePlan.getPayableAmount());
                    receivablePlanNewVo.setPayableCurrencyNumName(currencyNumsMap.get(payablePlan.getFkCurrencyTypeNum()));
                    receivablePlanNewVo.setPayableCurrencyNum(payablePlan.getFkCurrencyTypeNum());
                    receivablePlanNewVo.setPayableAmountInfo(payablePlan.getPayableAmount() + "（" + receivablePlanNewVo.getPayableCurrencyNumName() + "）");
                }else {
                    receivablePlanNewVo.setExistPayable(false);
                }
            }
            if (StringUtils.isNotBlank(receivablePlanNewVo.getReceiptCurrencyTypeNum())) {
                //实收币种
                String curNum = currencyNumsMap.get(receivablePlanNewVo.getReceiptCurrencyTypeNum().split(",")[0]);
                if (GeneralTool.isNotEmpty(curNum)) {
                    receivablePlanNewVo.setActualReceivableAmountInfo(receivablePlanNewVo.getActualReceivableAmount() + curNum);
                }else {
                    receivablePlanNewVo.setActualReceivableAmountInfo(receivablePlanNewVo.getActualReceivableAmount()+"");
                }
            }
            receivablePlanNewVo.setFkInvoiceNum(invoiceNumByReceivableId.get(receivablePlanNewVo.getId()));
            if (convert.containsKey(receivablePlanNewVo.getFkTypeTargetId())) {
                receivablePlanNewVo.setStepName((String) convert.get(receivablePlanNewVo.getFkTypeTargetId()));
            }
            if (receivablePlanNewVo.getIsDeferEntrance() && TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(receivablePlanNewVo.getFkTypeKey())
              && GeneralTool.isNotEmpty(maxDeferEntranceTimesMap)) {
//                receivablePlanNewVo.setMaxDeferEntranceTimes(studentOfferItemDeferEntranceTimeMapper.getLastDeferEntranceByItemId(receivablePlanNewVo.getFkTypeTargetId()));
                receivablePlanNewVo.setMaxDeferEntranceTimes(maxDeferEntranceTimesMap.get(receivablePlanNewVo.getFkTypeTargetId()));
            }


            receivablePlanNewVo.setFkTypeName(TableEnum.getValue(receivablePlanNewVo.getFkTypeKey()));
            if (GeneralTool.isNotEmpty(receivablePlanNewVo.getBonusType())) {
                receivablePlanNewVo.setBonusTypeName(ProjectExtraEnum.getValueByKey(receivablePlanNewVo.getBonusType(), ProjectExtraEnum.BONUS_TYPE));
            }

            if(GeneralTool.isNotEmpty(planRemarkDetailResultDtoMap)){
                PlanRemarkDetailResultVo planRemarkDetailResultVo = planRemarkDetailResultDtoMap.get(receivablePlanNewVo.getId());
                if (GeneralTool.isNotEmpty(planRemarkDetailResultVo)) {
                    PlanRemarkDetailVo planRemarkDetailVo = BeanCopyUtils.objClone(planRemarkDetailResultVo, PlanRemarkDetailVo::new);
                    if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(receivablePlanNewVo.getFkTypeKey())){
                        assert planRemarkDetailVo != null;
                        if (GeneralTool.isNotEmpty(studentCommentMap)
                                &&GeneralTool.isNotEmpty(studentCommentMap.get(planRemarkDetailResultVo.getFkStudentId()))){
                            List<SaleComment> saleComments = studentCommentMap.get(planRemarkDetailResultVo.getFkStudentId());
                            List<String> studentCommentList = saleComments.stream().map(SaleComment::getComment).collect(Collectors.toList());
                            planRemarkDetailVo.setStudentComments(studentCommentList);
                        }
                        if (GeneralTool.isNotEmpty(studentOfferItemCommentMap)
                                &&GeneralTool.isNotEmpty(studentOfferItemCommentMap.get(planRemarkDetailResultVo.getFkStudentOfferItemId()))){
                            List<SaleComment> saleComments = studentOfferItemCommentMap.get(planRemarkDetailResultVo.getFkStudentOfferItemId());
                            List<String> studentOfferItemCommentList = saleComments.stream().map(SaleComment::getComment).collect(Collectors.toList());
                            planRemarkDetailVo.setStudentOfferItemComments(studentOfferItemCommentList);
                        }
                        if (GeneralTool.isNotEmpty(planRemarkDetailResultVo.getEducationProject())){
                            planRemarkDetailVo.setEducationProjectName(ProjectExtraEnum.getValueByKey(planRemarkDetailResultVo.getEducationProject(),ProjectExtraEnum.EDUCATION_PROJECT));
                        }
                        if (GeneralTool.isNotEmpty(planRemarkDetailResultVo.getEducationDegree())){
                            planRemarkDetailVo.setEducationDegreeName(ProjectExtraEnum.getValueByKey(planRemarkDetailResultVo.getEducationDegree(),ProjectExtraEnum.EDUCATION_DEGREE));
                        }
                    }

                    receivablePlanNewVo.setPlanRemarkDetailDto(planRemarkDetailVo);
                    if (GeneralTool.isEmpty(planRemarkDetailVo)
                            ||(GeneralTool.isNotEmpty(planRemarkDetailVo)&&MyStringUtils.isAllFieldNull(planRemarkDetailVo))){
                        receivablePlanNewVo.setPlanRemarkDetailDto(null);
                    }
                }
            }
            receivablePlanNewVo.setFkCurrencyTypeName(receivablePlanNewVo.getFkCurrencyTypeName() + "（"  + receivablePlanNewVo.getFkCurrencyTypeNum()+ "）");
            if (GeneralTool.isNotEmpty(receivablePlanNewVo.getAgentNameNote())){
                receivablePlanNewVo.setAgentName(receivablePlanNewVo.getAgentName()+"("+ receivablePlanNewVo.getAgentNameNote()+")");
            }
        }
        if (GeneralTool.isNotEmpty(times)) {
            times[0] = String.valueOf((fEndTime - fStartTime));
        }
        return collect;
    }

    /**
     * 查询数据库仓库 应收计划列表
     *
     * @Date 16:15 2022/6/27
     * <AUTHOR>
     */
    @DS("saledb-doris")
    public List<ReceivablePlanNewVo> getDorisReceivablePlanNewDtos(ReceivablePlanNewDto receivablePlanNewDto, IPage<ReceivablePlanNewVo> iPage) {
        return receivablePlanMapper.getReceivablePlanNew(iPage, receivablePlanNewDto);
    }



    /**
     * Author Cream
     * Description : 根据提供商 && 发展业务对象查询应收计划下拉数据
     * Date 2022/4/16 11:44
     * Params:
     * Return
     */
    @Override
    public List<ReceivablePlanVo> doGetInvoiceNewReceivablePlan(ReceivablePlanBatchDto receivablePlanBatchDto, String[] times) {
        long var1 = System.currentTimeMillis();
        if (GeneralTool.isNotEmpty(receivablePlanBatchDto.getPageNumber())) {
            receivablePlanBatchDto.setOffset((receivablePlanBatchDto.getPageNumber() - 1) * receivablePlanBatchDto.getPageSize());
        }
        String studentName = receivablePlanBatchDto.getStudentName();
        if (StringUtils.isNotBlank(studentName)) {
            receivablePlanBatchDto.setStudentName(studentName.replace(" ", "").trim());
        }
        List<ReceivablePlanNewVo> info = receivablePlanMapper.getReceivablePlanInfoByKeyATargetId(receivablePlanBatchDto);
        times[0] = String.valueOf(System.currentTimeMillis() - var1);
        List<ReceivablePlanVo> data = new ArrayList<>();
        if (GeneralTool.isNotEmpty(info)) {
            List<Long> ids = info.stream().map(ReceivablePlanNewVo::getId).collect(Collectors.toList());
            if (GeneralTool.isNotEmpty(ids)) {
                data = getReceivablePlanDtosByIds(ids);
            }
        }

        return data;
    }

    @Override
    public ResponseBo doGetInvoiceRPPaginationInfo(ReceivablePlanBatchDto receivablePlanBatchDto, Page page) {
        long var1 = System.currentTimeMillis();
        String studentName = receivablePlanBatchDto.getStudentName();
        if (StringUtils.isNotBlank(studentName)) {
            receivablePlanBatchDto.setStudentName(studentName.replace(" ", "").trim());
        }
        List<ReceivablePlanNewVo> info = receivablePlanMapper.getReceivablePlanInfoByKeyATargetId(receivablePlanBatchDto);
        if (GeneralTool.isNotEmpty(info)) {
            page.setAll(info.get(0).getTotalCount());
        }
        long var2 = System.currentTimeMillis();
        return new ResponseBo<>(BeanCopyUtils.objClone(page, Page::new), var2 - var1);
    }

    @Override
    public List<ReceivablePlanVo> getReceivablePlansDetailNew(Set<Long> ids) {
        if (GeneralTool.isEmpty(ids)) {
            ids.add(0L);
        }
        List<ReceivablePlan> receivablePlans = receivablePlanMapper.selectBatchIds(ids);
        if (GeneralTool.isEmpty(receivablePlans)) {
            return null;
        }

        return BeanCopyUtils.copyListProperties(receivablePlans, ReceivablePlanVo::new);
    }

    @Override
    public Boolean batchUpdateByIds(List<ReceivablePlan> receivablePlans) {
        if (receivablePlans.isEmpty()) {
            return false;
        }
        return updateBatchById(receivablePlans);
    }
}
