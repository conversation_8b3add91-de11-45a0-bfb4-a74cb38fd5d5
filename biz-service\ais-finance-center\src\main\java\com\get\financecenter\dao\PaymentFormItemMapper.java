package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.dto.AgentSettlementBatchExportDto;
import com.get.financecenter.dto.AgentSettlementDto;
import com.get.financecenter.vo.*;
import com.get.financecenter.vo.AgentSettlementAccommodationVo;
import com.get.financecenter.entity.PaymentFormItem;
import com.get.financecenter.excelmodel.AgentSettlementAccommodationModel;
import com.get.financecenter.excelmodel.AgentSettlementInsuranceModel;
import com.get.financecenter.excelmodel.AgentSettlementIntermediaryModel;
import com.get.financecenter.excelmodel.AgentSettlementServiceFeeModel;
import com.get.financecenter.excelmodel.AgentSettlementTransferModel;
import com.get.financecenter.dto.PayablePlanTypeKeyDto;
import com.get.salecenter.vo.SelItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Mapper
public interface PaymentFormItemMapper extends BaseMapper<PaymentFormItem> {


    int insertSelective(PaymentFormItem record);

    int updateFee(@Param("fee")BigDecimal fee,@Param("id") Long id,@Param("payFormId")Long var2);


    /**
     * @return java.lang.String
     * @Description: 获取上次付款单的支付币种
     * @Param [planId]
     * <AUTHOR>
     */
    List<AlreadyPayVo> getAlreadyPayByPlanId(Long planId);


    /**
     * @return java.lang.Long
     * @Description: 查询公司id
     * @Param [itemId]
     * <AUTHOR>
     */
    Long getCompanyIdByItemId(Long itemId);


    /**
     * @return java.lang.Long
     * @Description: 查询公司id
     * @Param [itemIds]
     * <AUTHOR>
     */
    List<SelItem> getCompanyIdByItemIds(@Param("itemIds") Set<Long> itemIds);

    /**
     * @return java.math.BigDecimal
     * @Description:
     * @Param [formId]
     * <AUTHOR>
     */
    BigDecimal getAmountByFormId(@Param("formId") Long formId, @Param("formItemId") Long formItemId);


//    /**
//     * 佣金结算代理列表
//     *
//     * @Date 11:46 2021/12/21
//     * <AUTHOR>
//     */
//    List<AgentSettlementVo> agentSettlementList(@Param("agentSettlementDto")AgentSettlementDto agentSettlementVo, @Param("agentIdList")List<Long> agentIdList);

    /**
     * 佣金结算留学申请子项列表 未结算步骤
     *
     * @Date 16:06 2021/12/21
     * <AUTHOR>
     */
    List<AgentSettlementOfferItemVo> agentSettlementOfferItemList(@Param("agentSettlementDto") AgentSettlementDto agentSettlementDto,
                                                                  @Param("agentIds") List<Long> agentIds,
                                                                  @Param("enrolledKey") String enrolledKey,
                                                                  @Param("stepEnrolledTbc") String stepEnrolledTbc,
                                                                  @Param("exportFlag") boolean exportFlag,
                                                                  @Param("payInAdvanceFlag") boolean payInAdvanceFlag);


    /**
     * 无应付或应付为0的数据
     *
     * @Date 16:29 2023/12/20
     * <AUTHOR>
     */
    List<AgentSettlementOfferItemVo> getHtiNoCommission(@Param("agentSettlementDto") AgentSettlementDto agentSettlementDto);


    /**
     * 佣金结算留学申请子项列表  结算中 OR 代理确认步骤
     *
     * @Date 16:06 2021/12/21
     * <AUTHOR>
     */
    List<AgentSettlementOfferItemVo> agentSettlementOfferConfirmationItemList(@Param("agentSettlementDto") AgentSettlementDto agentSettlementDto,
                                                                              @Param("agentIdList") List<Long> agentIdList,
                                                                              @Param("statusSettlementList") List<Integer> statusSettlementList);


    /**
     * 佣金结算留学保险子项列表 未结算步骤
     *
     * @Date 16:06 2021/12/21
     * <AUTHOR>
     */
    List<AgentSettlementInsuranceVo> agentSettlementInsuranceList(@Param("agentSettlementDto") AgentSettlementDto agentSettlementDto,
                                                                  @Param("agentIdList") List<Long> agentIdList);

    /**
     * 佣金结算留学保险子项列表 结算中 OR 代理确认步骤
     *
     * @Date 16:06 2021/12/21
     * <AUTHOR>
     */
    List<AgentSettlementInsuranceVo> agentSettlementConfirmationInsuranceList(@Param("agentSettlementDto") AgentSettlementDto agentSettlementDto,
                                                                              @Param("agentIdList") List<Long> agentIdList,
                                                                              @Param("statusSettlementList") List<Integer> statusSettlementList);

    /**
     * 佣金结算留学住宿子项列表
     *
     * @Date 16:06 2021/12/21
     * <AUTHOR>
     */
    List<AgentSettlementAccommodationVo> agentSettlementAccommodationList(@Param("agentSettlementDto") AgentSettlementDto agentSettlementDto,
                                                                          @Param("agentIdList") List<Long> agentIdList);

    /**
     * 佣金结算留学服务费子项列表
     *
     * @Date 17:07 2023/2/9
     * <AUTHOR>
     */
    List<AgentSettlementServiceFeeVo> agentSettlementServiceFeeList(@Param("agentSettlementDto") AgentSettlementDto agentSettlementDto,
                                                                    @Param("agentIdList") List<Long> agentIdList);


    /**
     * 佣金结算留学住宿子项列表  结算中 OR 代理确认步骤
     *
     * @Date 16:06 2021/12/21
     * <AUTHOR>
     */
    List<AgentSettlementAccommodationVo> agentSettlementConfirmationAccommodationList(@Param("agentSettlementDto") AgentSettlementDto agentSettlementDto,
                                                                                      @Param("agentIdList") List<Long> agentIdList,
                                                                                      @Param("statusSettlementList") List<Integer> statusSettlementList);

    /**
     * 佣金结算留学住宿子项列表 结算中 OR 代理确认步骤
     *
     * @Date 16:06 2021/12/21
     * <AUTHOR>
     */
    List<AgentSettlementAccommodationVo> agentSettlementAccommodationConfirmationList(@Param("agentSettlementDto") AgentSettlementDto agentSettlementDto, @Param("agentIdList") List<Long> agentIdList);

    /**
     * 佣金结算子项列表 人民币 中介结算汇出表数据模型信息
     *
     * @Date 16:06 2021/12/21
     * <AUTHOR>
     */
    List<AgentSettlementIntermediaryModel> agentSettlementItemIntermediaryExport(@Param("agentSettlementBatchExportVoList") List<AgentSettlementBatchExportDto> agentSettlementBatchExportVoList, @Param("flag") boolean flag, @Param("currency") String currency);

    /**
     * 佣金结算子项列表 人民币 易思汇表数据模型信息
     *
     * @Date 12:26 2022/1/15
     * <AUTHOR>
     */
    List<AgentSettlementTransferModel> agentSettlementItemTransferExport(@Param("payablePlanIdList") List<Long> payablePlanIdList, @Param("flag") boolean flag, @Param("currency") String currency);

    /**
     * 佣金结算子项列表 保险结算表数据模型信息
     *
     * @Date 12:26 2022/1/15
     * <AUTHOR>
     */
    List<AgentSettlementInsuranceModel> agentSettlementItemInsuranceExport(@Param("agentSettlementBatchExportVoList") List<AgentSettlementBatchExportDto> agentSettlementBatchExportVoList, @Param("flag") boolean flag, @Param("currency") String currency);

    /**
     * 佣金结算子项列表 住宿结算表数据模型信息
     *
     * @Date 12:26 2022/1/15
     * <AUTHOR>
     */
    List<AgentSettlementAccommodationModel> agentSettlementAccommodationExport(@Param("agentSettlementBatchExportVoList") List<AgentSettlementBatchExportDto> agentSettlementBatchExportVoList, @Param("flag") boolean flag, @Param("currency") String currency);

    /**
     * 佣金结算子项列表 服务费结算表数据模型信息
     *
     * @Date 9:46 2023/2/11
     * <AUTHOR>
     */
    List<AgentSettlementServiceFeeModel> agentSettlementServiceFeeExport(@Param("agentSettlementBatchExportVoList") List<AgentSettlementBatchExportDto> agentSettlementBatchExportVoList,
                                                                         @Param("flag") Boolean flag, @Param("currency") String currency);

    /**
     * 根据应付计划id获取已付折合金额
     *
     * @Date 17:22 2022/7/12
     * <AUTHOR>
     */
    BigDecimal getAmountPaidByPayablePlanId(Long payablePlanId);

    /**
     * 获取付款单子单是否存在
     * @param planIds
     * @return
     */
    Integer getPayFormItemCount(@Param("planIds") Set<Long> planIds);

    /**
     * 批量查询
     *
     * @param planIds
     * @return
     */
    List<AlreadyPayVo> getAlreadyPayByPlanIds(@Param("planIds")Set<Long> planIds);

    /**
     * 根据学生ids获取付款单子单
     *
     * @param payablePlanTypeKeyDto 应付类型关键字，一定需要存在一个为true
     * @param studentIds           学生id集合
     * @return
     */
    List<PaymentFormItemCountVo> getPaymentFormItemList(@Param("payablePlanTypeKeyDto") PayablePlanTypeKeyDto payablePlanTypeKeyDto,
                                                        @Param("studentIds") Set<Long> studentIds);

    List<MSettlementBillItemVo> getAllSettlementBillItem(@Param("fkPayablePlanIds") List<Long> fkPayablePlanIds);
    int selectstatusSettlement(@Param("agentId") Long agentId);
}