package com.get.permissioncenter.dao;

import com.get.core.mybatis.mapper.GetMapper;
import com.get.permissioncenter.entity.StaffHrEvent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface StaffHrEventMapper extends GetMapper<StaffHrEvent> {

    int insertSelective(StaffHrEvent record);

    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [staffId]
     * <AUTHOR>
     */
    Boolean isExistByStaffId(@Param("staffId") Long staffId);

}