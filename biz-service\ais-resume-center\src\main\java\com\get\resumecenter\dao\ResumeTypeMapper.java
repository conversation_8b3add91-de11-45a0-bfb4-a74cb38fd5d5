package com.get.resumecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.resumecenter.entity.ResumeType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

;

@Mapper
public interface ResumeTypeMapper extends BaseMapper<ResumeType> {
    int insert(ResumeType record);

    int insertSelective(ResumeType record);


    List<BaseSelectEntity> getResumeTypeSelect();


    Integer getMaxViewOrder();
}