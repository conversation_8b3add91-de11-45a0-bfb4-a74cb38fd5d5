package com.get.institutioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("s_translation_mapping")
public class InstitutionTranslationMapping extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    @Column(name = "fk_table_name")
    private String fkTableName;
    /**
     * 字段名
     */
    @ApiModelProperty(value = "字段名")
    @Column(name = "fk_column_name")
    private String fkColumnName;
    /**
     * 字段标题名
     */
    @ApiModelProperty(value = "字段标题名")
    @Column(name = "input_title")
    private String inputTitle;
    /**
     * 输入类型：0单行/1多行/2富文本
     */
    @ApiModelProperty(value = "输入类型：0单行/1多行/2富文本")
    @Column(name = "input_type")
    private Integer inputType;
    /**
     * 最大字符限制数
     */
    @ApiModelProperty(value = "最大字符限制数")
    @Column(name = "max_length")
    private Integer maxLength;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    @Column(name = "view_order")
    private Integer viewOrder;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;

}