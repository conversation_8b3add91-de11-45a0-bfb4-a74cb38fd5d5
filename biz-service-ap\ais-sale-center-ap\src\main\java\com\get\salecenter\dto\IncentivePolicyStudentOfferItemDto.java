package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-03-13
 */
@Data
public class IncentivePolicyStudentOfferItemDto  extends BaseVoEntity  {

//    @NotNull(message = "公司Id", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "公司Id", required = true)
    private List<Long> fkCompanyIds;

    @ApiModelProperty(value = "Incentive奖励政策Id")
    private Long fkIncentivePolicyId;

//    @ApiModelProperty(value = "学生申请方案项目Id")
//    private Long fkStudentOfferItemId;

    @ApiModelProperty(value = "统计方式：0人工剔除/1人工计入/2系统计入")
    private Integer countType;

    @ApiModelProperty(value = "财务状态：0未结算/1已结算")
    private Integer financeStatus;

    @ApiModelProperty(value = "代理名称或编号")
    private String agentNameOrNum;

    @ApiModelProperty(value = "学生姓名（中/英）")
    private String studentName;

    @ApiModelProperty(value = "学校名字")
    private String institutionName;

    @ApiModelProperty(value = "是否重新统计符合策略的学生：0 否/1 是")
    private Integer isCountStudent;

 

//    @ApiModelProperty(value = "结算时间")
//    private LocalDateTime settlementTime;
}
