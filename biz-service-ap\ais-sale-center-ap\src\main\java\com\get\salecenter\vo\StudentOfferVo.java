package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/11/13
 * @TIME: 15:44
 * @Description:
 **/
@Data
public class StudentOfferVo extends BaseEntity {

    /**
     * 绑定的项目成员
     */
    @ApiModelProperty(value = "绑定的项目成员")
    List<StudentProjectRoleStaffVo> studentProjectRoleStaffDtos;
    @ApiModelProperty(value = "代理名称")
    private String fkAgentName;
    @ApiModelProperty(value = "代理id")
    private Long fkAgentId;
    @ApiModelProperty(value = "BD名称")
    private String bdName;
    @ApiModelProperty(value = "国家名字")
    private String fkAreaCountryName;
    @ApiModelProperty(value = "申请方案项目")
    //TODO 改过
//    private List<StudentItemAndStepVo> studentItemAndStepDtos;
    private List<StudentItemInfoVo> studentItemAndStepDtos;

    @ApiModelProperty(value = "角色员工")
    private List<StudentRoleAndStaffVo> studentRoleAndStaffList;
    /**
     * @Description: 学习计划学校
     * @Author: Jerry
     * @Date:12:59 2021/8/17
     */
    @ApiModelProperty(value = "学习计划学校")
    private List<InstitutionTabVo> institutionTabs;
    @ApiModelProperty(value = "入学登记完成flag")
    private boolean enrolledFlag;
    @ApiModelProperty(value = "公司名称")
    private String companyName;
    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    private Long taskId;
    /**
     * 实例id
     */
    @ApiModelProperty(value = "实例id")
    private Long procInstId;
    /**
     * 任务版本号
     */
    @ApiModelProperty(value = "任务版本号")
    private Integer taskVersion;
    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;
    /**
     * 学生名称
     */
    @ApiModelProperty(value = "学生名称")
    private String studentName;
    /**
     * 流程名称
     */
    @ApiModelProperty(value = "流程名称")
    private String workflowName;
    /**
     * 流程名称类型
     */
    @ApiModelProperty(value = "流程名称类型")
    private Integer buttonType;
    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称")
    private String courseFullName;
    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String institutionFullName;
    /**
     * 学习计划idStr
     */
    @ApiModelProperty(value = "学习计划idStr")
    private String studentOfferItemIdStr;
    /**
     * 学校idStr
     */
    @ApiModelProperty(value = "学习计划idStr")
    private String institutionIdStr;
    /**
     * 课程idStr
     */
    @ApiModelProperty(value = "学习计划idStr")
    private String institutionCourseIdStr;
    /**
     * 学校和课程idStr
     */
    @ApiModelProperty(value = "学校和课程idStr")
    private String institutionAndCourseIdStr;
    /**
     * 学校和课程idStr
     */
    @ApiModelProperty(value = "学校和课程idStr")
    private String institutionAndCustomCourseIdStr;
    /**
     *
     */
    private List<InstitutionAndItemVo> institutionAndItemDtos;
    /**
     *
     */
    private Boolean studentOfferFlag;
    /**
     * 联系人邮箱
     */
    @ApiModelProperty(value = "联系人邮箱")
    private String email;

    /**
     * 同意按钮状态
     */
    @ApiModelProperty(value = "同意按钮状态")
    private Boolean agreeButtonType;

    /**
     * 拒绝按钮状态
     */
    @ApiModelProperty(value = "拒绝按钮状态")
    private Boolean refuseButtonType;

    /**
     * 总记录数
     */
    @ApiModelProperty(value = "总记录数")
    private Integer totalCount;

    @ApiModelProperty(value = "是否有学习计划")
    private Boolean studyPlanFlag;

    @ApiModelProperty(value = "项目成员是否限制编辑")
    private Boolean isProjectMembersLimit = false;

    @ApiModelProperty(value = "申请方案终止作废原因")
    private String fkCancelOfferReasonName;

    @ApiModelProperty(value = "作废学习计划")
    private Boolean invalidateOfferItem;

    @ApiModelProperty(value = "申请方案终止作废flag")
    private Boolean cancelOfferFlag;

    /**
     * 状态 0待签1代办2无
     */
    @ApiModelProperty(value = "报销单状态 0待签1代办2无")
    private Integer expenseClaimFormStatus;

    //==============实体类StudentOffer==================
    private static final long serialVersionUID = 1L;
    /**
     * 学生Id
     */
    @ApiModelProperty(value = "学生Id")
    @Column(name = "fk_student_id")
    private Long fkStudentId;

    /**
     * 代理联系人Id
     */
//    @ApiModelProperty(value = "代理联系人Id")
//    @Column(name = "fk_contact_person_id")
//    private Long fkContactPersonId;
    /**
     * 员工Id（业绩绑定，BD）
     */
    @ApiModelProperty(value = "员工Id（业绩绑定，BD）")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "申请方案终止作废原因Id")
    @Column(name = "fk_cancel_offer_reason_id")
    private Long fkCancelOfferReasonId;
    /**
     * 申请人员工Id
     */
    @ApiModelProperty(value = "申请人员工Id")
    @Column(name = "fk_staff_id_workflow")
    private Long fkStaffIdWorkflow;
    /**
     * 申请方案编号
     */
    @ApiModelProperty(value = "申请方案编号")
    @Column(name = "num")
    private String num;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 状态：0关闭/1打开
     */
    @ApiModelProperty(value = "状态：0关闭/1打开")
    @Column(name = "status")
    private Integer status;
    /**
     * 状态：0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销
     */
    @ApiModelProperty(value = "状态：0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销")
    @Column(name = "status_workflow")
    private Integer statusWorkflow;
    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    @Column(name = "id_gea")
    private String idGea;
    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    @Column(name = "id_iae")
    private String idIae;

    @ApiModelProperty(value = "联系人邮箱")
    @Column(name = "agent_contact_email")
    private String agentContactEmail;

    @ApiModelProperty("代理标签")
    private List<AgentLabelVo> agentLabelVos;

    @ApiModelProperty("代理邮电标签")
    private List<AgentLabelVo> agentEmailLabelVos;
}
