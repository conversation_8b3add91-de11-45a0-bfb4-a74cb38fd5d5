package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 业务区域VO
 *
 * <AUTHOR>
 * @date 2021/7/28 14:58
 */
@Data
public class AreaRegionDto extends BaseVoEntity {
    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;


    /**
     * 国家Id
     */
    @NotNull(message = "国家Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;

    /**
     * 大区编号
     */
    @ApiModelProperty(value = "大区编号")
    private String num;

    /**
     * 大区名称
     */
    @NotBlank(message = "大区名称不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "大区名称")
    private String name;

    /**
     * 大区中文名称
     */
    @NotBlank(message = "大区中文名称不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "大区中文名称")
    private String nameChn;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    /**
     * 搜索关键词
     */
    @ApiModelProperty(value = "搜索关键词")
    private String keyWord;
}
