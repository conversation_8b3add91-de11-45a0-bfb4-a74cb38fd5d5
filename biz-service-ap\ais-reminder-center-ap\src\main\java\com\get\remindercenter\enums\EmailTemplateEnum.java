package com.get.remindercenter.enums;

import lombok.Getter;

/**
 * 邮件模板枚举
 */
@Getter
public enum EmailTemplateEnum {

    STUDENT_OFFER_ITEM_COMMISSION_NOTICE("STUDENT_OFFER_ITEM_COMMISSION_NOTICE", "学习计划佣金结算通知"),

    OFFER_ACCEPT_DUE_REMIND("OFFER_ACCEPT_DUE_REMIND", "接受Offer截止提醒"),

    PAY_DEPOSIT_DUE_REMIND("PAY_DEPOSIT_DUE_REMIND", "支付押金截止提醒"),

    AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE ("AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE", "代理学生接受Offer截止提醒"),

    AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE ("AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE", "代理学生支付押金截止提醒"),

    COURSE_OPENING_REMINDER ("COURSE_OPENING_REMINDER", "课程预计开课提醒"),

    STUDY_PLAN_SAME_SCHOOL_COURSE_REMINDER ("STUDY_PLAN_SAME_SCHOOL_COURSE_REMINDER", "学习计划相同学校课程提醒"),
    WORK_LEAVE_WORKFLOW_REMINDER ("WORK_LEAVE_WORKFLOW_REMINDER", "工休单工作流提醒"),

    NON_WORK_LEAVE_WORKFLOW_REMINDER ("NON_WORK_LEAVE_WORKFLOW_REMINDER", "非工休单工作流提醒"),

    APPLN_TERM_INVAL_WORKFLOW_REMINDER ("APPLN_TERM_INVAL_WORKFLOW_REMINDER", "申请方案终止作废工作流提醒"),

    EVENT_FEE_PLAN_CHANGE_REMINDER ("EVENT_FEE_PLAN_CHANGE_REMINDER", "活动费用汇总收款计划变更提醒"),

    REWARD_PROMOTION_ACTIVITY_REMINDER ("REWARD_PROMOTION_ACTIVITY_REMINDER", "奖励活动推广活动提醒"),

    MULTI_USER_TASK_REMINDER ("MULTI_USER_TASK_REMINDER", "多人任务提醒"),

    NEW_MULTI_USER_TASK_REMINDER ("NEW_MULTI_USER_TASK_REMINDER", "新多人任务提醒"),

    SUMMIT_REGISTRATION_REMINDER ("SUMMIT_REGISTRATION_REMINDER", "峰会提交报名册提醒"),

    PROVIDER_CONTRACT_EXPIRE ("PROVIDER_CONTRACT_EXPIRE", "供应商合同到期提醒"),

    REMINDER_EMAIL_CREATE_STUDENT_OFFER("REMINDER_EMAIL_CREATE_STUDENT_OFFER", "申请方案创建邮件"),

    REMINDER_EMAIL_STEP_ADMITTED("REMINDER_EMAIL_STEP_ADMITTED", "申请步骤变更邮件"),

    REMINDER_COMMISSION_NOTICE("REMINDER_COMMISSION_NOTICE", "申请计划佣金通知"),

    INSURANCE_COMMISSION_NOTICE("INSURANCE_COMMISSION_NOTICE","留学保险佣金结算通知"),

    ACCOMMODATION_COMMISSION_NOTICE("ACCOMMODATION_COMMISSION_NOTICE","留学住宿佣金结算通知"),

    SERVICE_FEE_COMMISSION_NOTICE("SERVICE_FEE_COMMISSION_NOTICE","服务费佣金结算通知"),

    DEFER_ENTRANCE_REMIND("DEFER_ENTRANCE_REMIND","延迟入学通知"),

    OFFER_ITEM_COMMISSION_NOTICE("OFFER_ITEM_COMMISSION_NOTICE","申请计划更新通知佣金部"),

    AGENT_APPLICATION_APPROVED_HAS_ACCOUNT("AGENT_APPLICATION_APPROVED_HAS_ACCOUNT","代理申请通过有账号"),

    AGENT_APPLICATION_APPROVED_NO_ACCOUNT("AGENT_APPLICATION_APPROVED_NO_ACCOUNT","代理申请通过无账号"),

    AGENT_APPLICATION_REJECTED("AGENT_APPLICATION_REJECTED","代理申请不通过"),

    AGENT_RENEWAL_APPROVED_HAS_ACCOUNT("AGENT_RENEWAL_APPROVED_HAS_ACCOUNT","代理续签申请通过有账号"),

    AGENT_RENEWAL_APPROVED_NO_ACCOUNT("AGENT_RENEWAL_APPROVED_NO_ACCOUNT","代理续签申请通过无账号"),

    AGENT_RENEWAL_REJECTED("AGENT_RENEWAL_REJECTED","代理续签申请不通过"),

    CONTRACT_APPROVAL_PASSED_HAS_ACCOUNT("CONTRACT_APPROVAL_PASSED_HAS_ACCOUNT","合同审批通过有账号"),

    CONTRACT_APPROVAL_PASSED_NO_ACCOUNT("CONTRACT_APPROVAL_PASSED_NO_ACCOUNT","合同审批通过无账号"),

    AGENT_CONTRACT_APPROVAL_PASSED("AGENT_CONTRACT_APPROVAL_PASSED","代理合同审批通过"),

    CONTRACT_APPROVAL_REJECTED("CONTRACT_APPROVAL_REJECTED","合同审批驳回"),

    AGENT_APPLICATION_SUBMITTED("AGENT_APPLICATION_SUBMITTED","代理申请提交确认"),

    AGENT_RENEWAL_SUBMITTED("AGENT_RENEWAL_SUBMITTED","代理续约提交确认"),

    AGENT_CONTRACT_RENEWAL("AGENT_CONTRACT_RENEWAL","代理合同续签"),

    AGENT_RENEWAL_REJECTED_WITH_MSG("AGENT_RENEWAL_REJECTED_WITH_MSG","代理续签申请不通过带审批意见"),

    AGENT_APPLICATION_APPROVE_NOTICE("AGENT_APPLICATION_APPROVE_NOTICE","代理申请审批意见通知"),

    CLIENT_STUDENT_INVALID("CLIENT_STUDENT_INVALID","客户学生作废通知"),

    CLIENT_APPROVAL_NOTICE( "CLIENT_APPROVAL_NOTICE", "客户资源学生审批邮件提醒"),

    STUDENT_RESOURCE_EXPIRED_NOTICE("STUDENT_RESOURCE_EXPIRED_NOTICE", "学生资源签证到期模版"),

    HTI_EVENT_CHANGE_NOTICE("HTI_EVENT_CHANGE_NOTICE", "HTI活动变更通知"),

    FOLLOW_UP_APPOINTMENT("FOLLOW_UP_APPOINTMENT", "预约回访提醒"),

    EMPLOYEE_BIRTHDAY ("EMPLOYEE_BIRTHDAY", "员工生日邮件"),

    APPLICATION_FORM_APPROVAL_NOTICE("APPLICATION_FORM_APPROVAL_NOTICE","申请表审批通知"),
    APPLICATION_FORM_APPROVAL_APPROVED("APPLICATION_FORM_APPROVAL_APPROVED","申请表审批通过通知"),
    APPLICATION_FORM_APPROVAL_REJECTED("APPLICATION_FORM_APPROVAL_REJECTED","申请表审批驳回通知"),

    STAFF_CONTRACT_EXPIRE("STAFF_CONTRACT_EXPIRE","员工劳动合同到期提醒"),


    ENROL_FAILURE_NOTICE("ENROL_FAILURE_NOTICE","成功客户列表入学失败邮件提醒"),

    NEWS_EMAIL_NOTICE("NEWS_EMAIL_NOTICE","新增新闻信息邮件"),

    APP_AGENT_ADD_NOTICE("APP_AGENT_ADD_NOTICE","代理申请表单邮件提醒（BD版）"),

    APP_AGENT_CONTACT_PERSON_NOTICE ("APP_AGENT_CONTACT_PERSON_NOTICE","代理申请表单邮件提醒（代理联系人）"),

    APP_AGENT_CONTACT_NOTICE("APP_AGENT_CONTACT_NOTICE","代理申请表单邮件提醒（代理版）")






    ;

    /**
     * 发送邮件模板key
     */
    public static final EmailTemplateEnum[] REMINDER_TEMPLATE_KEY = new EmailTemplateEnum[]{STUDENT_OFFER_ITEM_COMMISSION_NOTICE,OFFER_ACCEPT_DUE_REMIND};



    private String emailTemplateKey;

    private String remark;

    EmailTemplateEnum(String emailTemplateKey, String remark) {
        this.emailTemplateKey = emailTemplateKey;
        this.remark = remark;
    }

}
