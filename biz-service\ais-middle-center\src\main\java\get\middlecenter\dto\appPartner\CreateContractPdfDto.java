package get.middlecenter.dto.appPartner;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author:<PERSON>
 * @Date: 2025/7/23
 * @Version 1.0
 * @apiNote:生成pdf请求类
 */
@Data
public class CreateContractPdfDto {

    @ApiModelProperty(value = "合同id")
    private Long contractId;

    @ApiModelProperty(value = "代理商id")
    private Long agentId;

    @ApiModelProperty(value = "合同版本")
    private Integer contractVsion;

    @ApiModelProperty(value = "合同模板模式")
    private Integer contractTemplateMode;
}
