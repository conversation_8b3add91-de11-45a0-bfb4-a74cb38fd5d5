package com.get.aisplatformcenterap.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class UserScopedDataDto {
    @ApiModelProperty(value = "平台应用Id 1 AIS 2 华通伙伴")
    private Long fkPlatformId;

    //平台应用CODE
    @ApiModelProperty(value = "平台应用CODE")
    private String fkPlatformCode;

    @ApiModelProperty(value = "系统资源Keys，逗号分隔")
    private List<String> fkResourceKeys;

    //id
    @ApiModelProperty(value = "发版信息父项id")
    private Long releaseInfoId;

    //用户ID
    @ApiModelProperty(value = "用户ID")
    private Long fkStaffId;

    //标题
    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "发布时间开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date releaseTimeStart;
    @ApiModelProperty(value = "发布时间结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date releaseTimeEnd;
}
