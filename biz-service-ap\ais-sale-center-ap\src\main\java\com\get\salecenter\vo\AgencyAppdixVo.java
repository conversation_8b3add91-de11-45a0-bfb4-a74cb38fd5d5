package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.AgentContract;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 同步cpp代理合同DTO
 **/
@Data
public class AgencyAppdixVo extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "agencyid")
    private String agencyId;

    @ApiModelProperty(value = "附件名")
    private String agreementName;

    @ApiModelProperty(value = "上传时间")
    private Date uploadTm;

    @ApiModelProperty(value = "终止时间")
    private Date endTm;

    //==============实体类AgentContract==================
    private static final long serialVersionUID = 1L;
    /**
     * 学生代理Id
     */
    @ApiModelProperty(value = "学生代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 学生代理合同类型Id
     */
    @ApiModelProperty(value = "学生代理合同类型Id")
    @Column(name = "fk_agent_contract_type_id")
    private Long fkAgentContractTypeId;
    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @Column(name = "contract_num")
    private String contractNum;
    /**
     * 合同开始时间
     */
    @ApiModelProperty(value = "合同开始时间")
    @Column(name = "start_time")
    private Date startTime;
    /**
     * 合同结束时间
     */
    @ApiModelProperty(value = "合同结束时间")
    @Column(name = "end_time")
    private Date endTime;
    /**
     * 返佣比例备注
     */
    @ApiModelProperty(value = "返佣比例备注")
    @Column(name = "return_commission_rate_note")
    private String returnCommissionRateNote;
    /**
     * 合同备注
     */
    @ApiModelProperty(value = "合同备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
    @ApiModelProperty(value = "状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废")
    @Column(name = "status")
    private Integer status;
    @ApiModelProperty(value = "合同审批模式：0普通/1特殊")
    @Column(name = "contract_approval_mode")
    private Integer contractApprovalMode;
    @ApiModelProperty(value = "关联撤销合同Id")
    @Column(name = "fk_agent_contract_id_revoke")
    private Long fkAgentContractIdRevoke;

}
