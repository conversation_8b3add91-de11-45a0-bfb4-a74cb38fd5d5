package com.get.insurancecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.insurancecenter.dto.file.AppFileCenter;
import com.get.insurancecenter.entity.InsuranceMediaAndAttached;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * @Author:Oliver
 * @Date: 2025/6/19
 * @Version 1.0
 * @apiNote:
 */
public interface InsuranceMediaAndAttachedService extends IService<InsuranceMediaAndAttached> {

    /**
     * 上传文件
     *
     * @param file
     * @return
     */
    InsuranceMediaAndAttached uploadAttached(MultipartFile file,Long id);


    /**
     * 下载文件
     *
     * @param appFileCenter
     * @param response
     */
    public void downloadFile(AppFileCenter appFileCenter, HttpServletResponse response);
}
