package com.get.financecenter.service.impl;

import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.GetDateUtil;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.secure.StaffInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.BeanUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.AccountingItemMapper;
import com.get.financecenter.dao.CompanyProfitAndLossItemMapper;
import com.get.financecenter.dao.StageProfitAndLossValueMapper;
import com.get.financecenter.dao.StageValueTaskQueueMapper;
import com.get.financecenter.dao.VouchMapper;
import com.get.financecenter.dto.ProfitAndLossStatementDto;
import com.get.financecenter.dto.RecomputeProfitLossStatementDto;
import com.get.financecenter.entity.AccountingItem;
import com.get.financecenter.entity.CompanyProfitAndLossItem;
import com.get.financecenter.entity.StageProfitAndLossValue;
import com.get.financecenter.entity.StageValueTaskQueue;
import com.get.financecenter.service.AccountingItemService;
import com.get.financecenter.service.ProfitAndLossService;
import com.get.financecenter.vo.FinancialStatsAsyncVo;
import com.get.financecenter.vo.ProfitAndLossStatementItemVo;
import com.get.financecenter.vo.ProfitAndLossStatementValueVo;
import com.get.financecenter.vo.ProfitAndLossStatementVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class ProfitAndLossServiceImpl implements ProfitAndLossService {

    @Resource
    private CompanyProfitAndLossItemMapper companyProfitAndLossItemMapper;
    @Resource
    private AccountingItemMapper accountingItemMapper;
    @Resource
    private StageProfitAndLossValueMapper stageProfitAndLossValueMapper;
    @Resource
    private StageValueTaskQueueMapper stageValueTaskQueueMapper;
    @Resource
    private VouchMapper vouchMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private AccountingItemService accountingItemService;
    @Lazy
    @Resource
    private ProfitAndLossService profitAndLossService;


    /**
     * 创建损益表
     *
     * @param probAndLossStatementDto
     * @return
     */
    @Override
    @Transactional
    public ProfitAndLossStatementVo createProfitAndLossStatement(ProfitAndLossStatementDto probAndLossStatementDto) {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 获取当前年份
        int year = currentDate.getYear();
        // 获取当前月份（1-12）
        int month = currentDate.getMonthValue();
        Long companyId = probAndLossStatementDto.getCompanyId();

        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        //创建损益表数据
        createStageProfitAndLossValue(companyId, year, month, staffInfo);

        //构建返回当年的损益表数据
        ProfitAndLossStatementVo profitAndLossStatementVo = new ProfitAndLossStatementVo();
        //获取损益表项排序后的标题List
        List<ProfitAndLossStatementItemVo> profitAndLossStatementVoList = stageProfitAndLossValueMapper.getProfitAndLossStatementTitle(probAndLossStatementDto);
        if (GeneralTool.isEmpty(profitAndLossStatementVoList)) {
            return null;
        }
        //获取损益表基础数据
        List<StageProfitAndLossValue> stageProfitAndLossValues = stageProfitAndLossValueMapper.selectList(Wrappers.<StageProfitAndLossValue>lambdaQuery()
                .eq(StageProfitAndLossValue::getFkCompanyId, companyId)
                .eq(StageProfitAndLossValue::getYear, probAndLossStatementDto.getYear())
                .orderByAsc(StageProfitAndLossValue::getMonth)
                .orderByAsc(StageProfitAndLossValue::getItemIndex));
        //按月份分类,并且从小到大排序
        List<Integer> months = stageProfitAndLossValues.stream().map(StageProfitAndLossValue::getMonth).distinct().sorted().collect(Collectors.toList());
        profitAndLossStatementVo.setMonths(months);

        for (ProfitAndLossStatementItemVo profitAndLossStatementItemVo : profitAndLossStatementVoList) {
            //标题匹配 -> 获得该标题每个月的数据
            List<StageProfitAndLossValue> stageProfitAndLossValueList = stageProfitAndLossValues.stream().filter(stageProfitAndLossValue -> stageProfitAndLossValue.getTitle().equals(profitAndLossStatementItemVo.getTitle())).collect(Collectors.toList());
            Map<Integer, ProfitAndLossStatementValueVo> monthsAmountMap = new HashMap<>();
            for (StageProfitAndLossValue stageProfitAndLossValue : stageProfitAndLossValueList) {
                ProfitAndLossStatementValueVo profitAndLossStatementValueVo = BeanUtil.copy(stageProfitAndLossValue, ProfitAndLossStatementValueVo.class);
                monthsAmountMap.put(stageProfitAndLossValue.getMonth(), profitAndLossStatementValueVo);
            }
            profitAndLossStatementItemVo.setMonthsAmountMap(monthsAmountMap);
            profitAndLossStatementItemVo.setTotalBalance(monthsAmountMap.values().stream().map(ProfitAndLossStatementValueVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        profitAndLossStatementVo.setItems(profitAndLossStatementVoList);
        return profitAndLossStatementVo;
    }

    /**
     * 创建损益表数据
     *
     * @param companyId
     * @param year
     * @param month
     * @param staffInfo
     */
    @Transactional
    public void createStageProfitAndLossValue(Long companyId, int year, int month, StaffInfo staffInfo) {
        //1.删除当月损益表
        stageProfitAndLossValueMapper.delete(Wrappers.<StageProfitAndLossValue>lambdaQuery().eq(StageProfitAndLossValue::getFkCompanyId, companyId).eq(StageProfitAndLossValue::getYear, year).eq(StageProfitAndLossValue::getMonth, month));
        //2.损益表配置
        List<CompanyProfitAndLossItem> companyProfitAndLossItems = companyProfitAndLossItemMapper.selectList(Wrappers.<CompanyProfitAndLossItem>lambdaQuery().eq(CompanyProfitAndLossItem::getFkCompanyId, companyId).orderByDesc(CompanyProfitAndLossItem::getViewOrder));
        if (GeneralTool.isNotEmpty(companyProfitAndLossItems)) {
            //生成当月的一套损益表
            List<StageProfitAndLossValue> stageProfitAndLossValueList = new ArrayList<>();
            int itemIndex = 0;
            for (CompanyProfitAndLossItem companyProfitAndLossItem : companyProfitAndLossItems) {
                Long fkAccountingItemId = companyProfitAndLossItem.getFkAccountingItemId();
                AccountingItem accountingItem = accountingItemMapper.selectById(fkAccountingItemId);
                if ("Expand".equals(companyProfitAndLossItem.getShowMode())) {
                    //创建损益表统计数值
                    List<StageProfitAndLossValue> stageProfitAndLossValues = new ArrayList<>();
                    stageProfitAndLossValues.add(createStageProfitAndLossValue(companyId, companyProfitAndLossItem, accountingItem, year, month, true, ++itemIndex, staffInfo));
                    //二级科目
                    List<AccountingItem> accountingItems = accountingItemMapper.selectList(Wrappers.<AccountingItem>lambdaQuery().eq(AccountingItem::getFkParentAccountingItemId, fkAccountingItemId));
                    if (GeneralTool.isNotEmpty(accountingItems)) {
                        for (AccountingItem secondAccountingItem : accountingItems) {
                            //创建损益表统计数值
                            stageProfitAndLossValues.add(createStageProfitAndLossValue(companyId, companyProfitAndLossItem, secondAccountingItem, year, month, false, ++itemIndex, staffInfo));
                        }
                    }
                    stageProfitAndLossValueList.addAll(stageProfitAndLossValues);
                    //合计
                    StageProfitAndLossValue stageProfitAndLossValue = new StageProfitAndLossValue();
                    stageProfitAndLossValue.setFkCompanyId(companyId);
                    stageProfitAndLossValue.setYear(year);
                    stageProfitAndLossValue.setMonth(month);
                    stageProfitAndLossValue.setItemIndex(++itemIndex);
                    stageProfitAndLossValue.setFkCompanyProfitAndLossItemId(companyProfitAndLossItem.getId());
                    stageProfitAndLossValue.setTitle(accountingItem.getCodeName() + "[合计]");
                    stageProfitAndLossValue.setDirectionValue(companyProfitAndLossItem.getDirectionValue());
                    //期初数
                    stageProfitAndLossValue.setAmountOpeningBalance(stageProfitAndLossValues.stream().filter(StageProfitAndLossValue::getIsSum).map(StageProfitAndLossValue::getAmountOpeningBalance).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                    //期末数
                    stageProfitAndLossValue.setAmountClosingBalance(stageProfitAndLossValues.stream().filter(StageProfitAndLossValue::getIsSum).map(StageProfitAndLossValue::getAmountClosingBalance).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                    //借方发生额
                    stageProfitAndLossValue.setAmountDr(stageProfitAndLossValues.stream().filter(StageProfitAndLossValue::getIsSum).map(StageProfitAndLossValue::getAmountDr).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                    //借方发生额
                    stageProfitAndLossValue.setAmountCr(stageProfitAndLossValues.stream().filter(StageProfitAndLossValue::getIsSum).map(StageProfitAndLossValue::getAmountCr).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                    //发生额
                    stageProfitAndLossValue.setAmount(stageProfitAndLossValues.stream().filter(StageProfitAndLossValue::getIsSum).map(StageProfitAndLossValue::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                    stageProfitAndLossValue.setIsSum(false);
                    stageProfitAndLossValue.setGmtCreateUser(staffInfo.getLoginId());
                    stageProfitAndLossValue.setGmtCreate(new Date());
                    stageProfitAndLossValueList.add(stageProfitAndLossValue);
                } else if ("Code".equals(companyProfitAndLossItem.getShowMode())) {
                    stageProfitAndLossValueList.add(createStageProfitAndLossValue(companyId, companyProfitAndLossItem, accountingItem, year, month, true, ++itemIndex, staffInfo));
                } else if ("Sum".equals(companyProfitAndLossItem.getShowMode())) {
                    StageProfitAndLossValue stageProfitAndLossValue = new StageProfitAndLossValue();
                    stageProfitAndLossValue.setFkCompanyId(companyId);
                    stageProfitAndLossValue.setYear(year);
                    stageProfitAndLossValue.setMonth(month);
                    stageProfitAndLossValue.setItemIndex(++itemIndex);
                    stageProfitAndLossValue.setFkCompanyProfitAndLossItemId(companyProfitAndLossItem.getId());
                    stageProfitAndLossValue.setTitle(companyProfitAndLossItem.getTitle());
                    stageProfitAndLossValue.setDirectionValue(companyProfitAndLossItem.getDirectionValue());
                    //期初数
                    BigDecimal amountOpeningBalance = BigDecimal.ZERO;
                    //借方发生额
                    BigDecimal amountDr = BigDecimal.ZERO;
                    //贷方发生额
                    BigDecimal amountCr = BigDecimal.ZERO;
                    //期末数
                    BigDecimal amountClosingBalance = BigDecimal.ZERO;
                    //发生额
                    BigDecimal amount = BigDecimal.ZERO;
                    for (StageProfitAndLossValue profitAndLossValue : stageProfitAndLossValueList) {
                        if (profitAndLossValue.getIsSum()) {
                            amountOpeningBalance = amountOpeningBalance.add(profitAndLossValue.getAmountOpeningBalance().multiply(new BigDecimal(profitAndLossValue.getDirectionValue())));
                            amountDr = amountDr.add(profitAndLossValue.getAmountDr());
                            amountCr = amountCr.add(profitAndLossValue.getAmountCr());
                            amountClosingBalance = amountClosingBalance.add(profitAndLossValue.getAmountClosingBalance().multiply(new BigDecimal(profitAndLossValue.getDirectionValue())));
                            amount = amount.add(profitAndLossValue.getAmount().multiply(new BigDecimal(profitAndLossValue.getDirectionValue())));
                        }
                    }
                    //期初数
                    stageProfitAndLossValue.setAmountOpeningBalance(stageProfitAndLossValueList.stream().filter(StageProfitAndLossValue::getIsSum).map(StageProfitAndLossValue::getAmountOpeningBalance).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                    //期末数
                    stageProfitAndLossValue.setAmountClosingBalance(stageProfitAndLossValueList.stream().filter(StageProfitAndLossValue::getIsSum).map(StageProfitAndLossValue::getAmountClosingBalance).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                    //借方发生额
                    stageProfitAndLossValue.setAmountDr(stageProfitAndLossValueList.stream().filter(StageProfitAndLossValue::getIsSum).map(StageProfitAndLossValue::getAmountDr).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                    //借方发生额
                    stageProfitAndLossValue.setAmountCr(stageProfitAndLossValueList.stream().filter(StageProfitAndLossValue::getIsSum).map(StageProfitAndLossValue::getAmountCr).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                    //发生额
                    stageProfitAndLossValue.setAmount(stageProfitAndLossValueList.stream().filter(StageProfitAndLossValue::getIsSum).map(StageProfitAndLossValue::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                    stageProfitAndLossValue.setIsSum(false);
                    stageProfitAndLossValue.setGmtCreateUser(staffInfo.getLoginId());
                    stageProfitAndLossValue.setGmtCreate(new Date());
                    stageProfitAndLossValueList.add(stageProfitAndLossValue);
                }
            }
            stageProfitAndLossValueMapper.insertBatch(stageProfitAndLossValueList);
        }
    }

    /**
     * 重新统计损益表
     *
     * @param recomputeProfitLossStatement
     * @return
     */
    @Override
    public void recomputeProfitLossStatement(RecomputeProfitLossStatementDto recomputeProfitLossStatement) {
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        //插入异步任务信息
        StageValueTaskQueue stageValueTaskQueue = new StageValueTaskQueue();
        stageValueTaskQueue.setFkTableName(TableEnum.FINANCE_STAGE_PROFIT_AND_LOSS_VALUE.key);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        stageValueTaskQueue.setStartYearMonth(sdf.format(recomputeProfitLossStatement.getStartTime()));
        stageValueTaskQueue.setFkCompanyIds(StringUtils.join(recomputeProfitLossStatement.getCompanyIds(), ","));
        stageValueTaskQueue.setStartTime(new Date());
        stageValueTaskQueue.setStatus(1);
        stageValueTaskQueue.setGmtCreateUser(staffInfo.getLoginId());
        stageValueTaskQueue.setGmtCreate(new Date());
        stageValueTaskQueueMapper.insert(stageValueTaskQueue);
        profitAndLossService.recomputeProfitLossStatementAsync(recomputeProfitLossStatement, staffInfo, stageValueTaskQueue);
    }

    /**
     * 异步信息查询
     * @param fkTableName
     * @return
     */
    @Override
    public FinancialStatsAsyncVo financialStatsAsyncQuery(String fkTableName) {
        FinancialStatsAsyncVo financialStatsAsyncVo = new FinancialStatsAsyncVo();
        if (TableEnum.FINANCE_STAGE_PROFIT_AND_LOSS_VALUE.key.equals(fkTableName)) {
            financialStatsAsyncVo.setTypeName(LocaleMessageUtils.getMessage("RECREATE_INCOME_STATEMENT_STATS_DATA"));
        } else if (TableEnum.FINANCE_STAGE_BALANCE_SHEET_VALUE.key.equals(fkTableName)) {
            financialStatsAsyncVo.setTypeName(LocaleMessageUtils.getMessage("RECREATE_BALANCE_SHEET_STATS_DATA"));
        } else if (TableEnum.FINANCE_STAGE_ACCOUNTING_ITEM_VALUE.key.equals(fkTableName)) {
            financialStatsAsyncVo.setTypeName(LocaleMessageUtils.getMessage("RECREATE_TRIAL_BALANCE_DATA"));
        }
        StageValueTaskQueue stageValueTaskQueue = stageValueTaskQueueMapper.selectOne(Wrappers.lambdaQuery(StageValueTaskQueue.class)
                .eq(StageValueTaskQueue::getFkTableName, fkTableName)
                .orderByDesc(StageValueTaskQueue::getGmtCreate)
                .last("limit 1"));
        if (GeneralTool.isEmpty(stageValueTaskQueue)) {
            return financialStatsAsyncVo;
        }
        financialStatsAsyncVo.setTime(stageValueTaskQueue.getStartYearMonth());
        String[] split = stageValueTaskQueue.getFkCompanyIds().split(",");
        Set<Long> companyIds = Arrays.stream(split).map(Long::parseLong).collect(Collectors.toSet());
        Map<Long, String> companyMap = permissionCenterClient.getCompanyNamesByIds(companyIds).getData();
        StringBuilder companyStringBuilder = new StringBuilder();
        for (String companyId : split) {
            companyStringBuilder.append(companyMap.get(Long.parseLong(companyId))).append(",");
        }
        //去掉最后一个字符
        companyStringBuilder.deleteCharAt(companyStringBuilder.length() - 1);
        financialStatsAsyncVo.setStatus(stageValueTaskQueue.getStatus());
        financialStatsAsyncVo.setCompanyName(companyStringBuilder.toString());
        financialStatsAsyncVo.setStaffName(permissionCenterClient.getStaffByCreateUser(stageValueTaskQueue.getGmtCreateUser()).getData().getName());
        financialStatsAsyncVo.setStartTime(stageValueTaskQueue.getStartTime());
        financialStatsAsyncVo.setEndTime(stageValueTaskQueue.getEndTime());
        return financialStatsAsyncVo;
    }

    /**
     * 异步创建
     *
     * @param recomputeProfitLossStatement
     * @param staffInfo
     * @param stageValueTaskQueue
     */
    @Async
    @Transactional
    public void recomputeProfitLossStatementAsync(RecomputeProfitLossStatementDto recomputeProfitLossStatement, StaffInfo staffInfo, StageValueTaskQueue stageValueTaskQueue) {
        try {
            List<Long> companyIds = recomputeProfitLossStatement.getCompanyIds();
            Date startTime = recomputeProfitLossStatement.getStartTime();
            // 获取系统当前时间
            Date currentDate = new Date();
            // 获取从传入时间到当前时间的所有YearMonth集合
            List<YearMonth> yearMonthList = GetDateUtil.getYearMonthRange(startTime, currentDate);
            for (Long companyId : companyIds) {
                for (YearMonth yearMonth : yearMonthList) {
                    createStageProfitAndLossValue(companyId, yearMonth.getYear(), yearMonth.getMonthValue(), staffInfo);
                }
            }
            stageValueTaskQueue.setEndTime(new Date());
            stageValueTaskQueue.setStatus(2);
            stageValueTaskQueue.setGmtCreateUser(staffInfo.getLoginId());
            stageValueTaskQueue.setGmtCreate(new Date());
            stageValueTaskQueueMapper.updateById(stageValueTaskQueue);
        } catch (Exception e) {
            e.printStackTrace();
            updateTaskStatus(stageValueTaskQueue, e.getMessage(), staffInfo);
            throw new RuntimeException(e);
        }
    }

    /**
     * 使用REQUIRES_NEW确保创建新事务
     * @param queue
     * @param message
     * @param staff
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateTaskStatus(StageValueTaskQueue queue, String message, StaffInfo staff) {
        queue.setEndTime(new Date());
        queue.setStatus(3);
        queue.setMessage(message);
        queue.setGmtCreateUser(staff.getLoginId());
        queue.setGmtCreate(new Date());
        stageValueTaskQueueMapper.updateById(queue);
    }






    /**
     * 创建损益表统计数值
     *
     * @param companyId
     * @param companyProfitAndLossItem
     * @param accountingItem
     * @param year
     * @param month
     * @param itemIndex
     * @return
     */
    private StageProfitAndLossValue createStageProfitAndLossValue(Long companyId, CompanyProfitAndLossItem companyProfitAndLossItem,
                                                                  AccountingItem accountingItem, int year, int month, Boolean isSum, int itemIndex, StaffInfo staffInfo) {
        //所有科目id(包含下级所有科目)
        List<Long> accountingItemList = accountingItemService.getChildrenAccountingItems(Collections.singletonList(accountingItem.getId()));
        accountingItemList.add(accountingItem.getId());
        //一级科目
        StageProfitAndLossValue stageProfitAndLossValue = new StageProfitAndLossValue();
        stageProfitAndLossValue.setFkCompanyId(companyId);
        stageProfitAndLossValue.setYear(year);
        stageProfitAndLossValue.setMonth(month);
        stageProfitAndLossValue.setItemIndex(itemIndex);
        stageProfitAndLossValue.setFkCompanyProfitAndLossItemId(companyProfitAndLossItem.getId());
        if (isSum) {
            stageProfitAndLossValue.setTitle(companyProfitAndLossItem.getTitle());
        } else {
            stageProfitAndLossValue.setTitle("-" + accountingItem.getCodeName());
        }
        stageProfitAndLossValue.setDirectionValue(companyProfitAndLossItem.getDirectionValue());

        //当月第一天
        Date firstDayOfMonth = GetDateUtil.getBeginTime(year, month);
        //获取当月的前一天时间
        Date lastDayOfPrevMonth = GetDateUtil.getYesterdayDate(firstDayOfMonth);
        //当月最后一天
        Date lastDayOfMonth = GetDateUtil.getLastDayOfMonth(firstDayOfMonth);

        //期初数计算当月第一天的前一天
        stageProfitAndLossValue.setAmountOpeningBalance(vouchMapper.getAccountingItemBalance(companyId, accountingItemList, null, lastDayOfPrevMonth, null, null, accountingItem.getDirection()));
        //期末数计算当月最后一天
        stageProfitAndLossValue.setAmountClosingBalance(vouchMapper.getAccountingItemBalance(companyId, accountingItemList, null, lastDayOfMonth, null, null, accountingItem.getDirection()));
        //借方发生额：当月第一条和最后一天 dr累加
        stageProfitAndLossValue.setAmountDr(vouchMapper.getAccountingItemBalance(companyId, accountingItemList, firstDayOfMonth, lastDayOfMonth, null, null, 2));
        //贷方发生额：当月第一条和最后一天 cr累加
        stageProfitAndLossValue.setAmountCr(vouchMapper.getAccountingItemBalance(companyId, accountingItemList, firstDayOfMonth, lastDayOfMonth, null, null, 3));
        //发生额
        stageProfitAndLossValue.setAmount(vouchMapper.getAccountingItemBalance(companyId, accountingItemList, firstDayOfMonth, lastDayOfMonth, null, null, accountingItem.getDirection()));
        stageProfitAndLossValue.setIsSum(isSum);
        stageProfitAndLossValue.setGmtCreateUser(staffInfo.getLoginId());
        stageProfitAndLossValue.setGmtCreate(new Date());
        return stageProfitAndLossValue;
    }




}
