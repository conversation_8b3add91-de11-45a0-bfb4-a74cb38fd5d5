package com.get.schoolGateCenter.vo;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/18
 * @TIME: 17:44
 * @Description:
 **/
@Data
public class NewsVo extends BaseVoEntity {
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名", required = true)
    private String fkTableName;

    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id", required = true)
    private Long fkTableId;

    /**
     * 新闻类型Id
     */
    @ApiModelProperty(value = "新闻类型Id", required = true)
    private Long fkNewsTypeId;
    /**
     * 目标类型目标新闻类型Id集合
     */
    @ApiModelProperty(value = "目标类型目标新闻类型Id集合", required = true)
    @NotNull(message = "目标类型目标新闻类型Id集合不能为空", groups = {Add.class, Update.class})
    private List<RNewsTypeVo> rNewsTypeVo;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题", required = true)
    @NotBlank(message = "标题不能为空", groups = {Add.class, Update.class})
    private String title;

    /**
     * 描述内容
     */
    @ApiModelProperty(value = "描述内容", required = true)
    @NotBlank(message = "描述内容不能为空", groups = {Add.class, Update.class})
    private String description;
    @ApiModelProperty(value = "关键词")
    private String keyWord;
    @ApiModelProperty(value = "目标名称")
    private String targetName;
    @ApiModelProperty(value = "附件")
    private MediaAndAttachedVo mediaAttachedVo;
    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @NotBlank(message = "公开对象", groups = {Add.class, Update.class})
    private String publicLevel;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    @NotNull(message = "发布时间不能为空", groups = {Add.class, Update.class})
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "publish_time")
    private Date publishTime;

    private String gotoUrl;

    @ApiModelProperty(value = "所属公司id")
    private Long companyId;

    /**
     * 目标类型对应的表名
     */
    @ApiModelProperty(value = "目标类型对应的表名")
    private String tableName;
    /**
     * 简介
     */
    @ApiModelProperty(value = "简介")
    private String profile;

    /**
     * 网页标题
     */
    @ApiModelProperty(value = "网页标题seo标题")
    private String webTitle;

    /**
     * 网页Meta描述
     */
    @ApiModelProperty(value = "网页Meta描述SEO描述")
    private String webMetaDescription;

    /**
     * 网页Meta关键字
     */
    @ApiModelProperty(value = "网页Meta关键字SEO关键词")
    private String webMetaKeywords;

    @ApiModelProperty(value = "生效时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date effectiveStartTime;

    @ApiModelProperty(value = "失效时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date effectiveEndTime;

    @ApiModelProperty(value = "新闻推荐咨询，多个用分号隔开")
    private String fkNewsTypeIdRecommend;

    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date activityStartTime;

    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date activityEndTime;

}
