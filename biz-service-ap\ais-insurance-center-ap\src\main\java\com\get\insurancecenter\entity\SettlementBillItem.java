package com.get.insurancecenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 结算账单明细
 */
@Data
@TableName("m_settlement_bill_item")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SettlementBillItem extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "结算账单Id")
    private Long fkSettlementBillId;

    @ApiModelProperty(value = "订单应付计划结算表Id")
    private Long fkInsuranceOrderSettlementId;

    @ApiModelProperty(value = "实际支付币种")
    private String fkCurrencyTypeNumActual;

    @ApiModelProperty(value = "实际支付金额")
    private BigDecimal amountActual;

    @ApiModelProperty(value = "实际手续费金额")
    private BigDecimal serviceFeeActual;

    @ApiModelProperty(value = "兑换汇率")
    private BigDecimal exchangeRate;

    @ApiModelProperty(value = "兑换支付币种")
    private String fkCurrencyTypeNumExchange;

    @ApiModelProperty(value = "兑换支付金额")
    private BigDecimal amountExchange;

    @ApiModelProperty(value = "兑换手续费金额")
    private BigDecimal serviceFeeExchange;

    @ApiModelProperty(value = "结算订单结算状态：0待确认/1已确认/2代理确认/3财务确认/4结算完成")
    @TableField(exist = false)
    private Integer approvalStatus;
}
