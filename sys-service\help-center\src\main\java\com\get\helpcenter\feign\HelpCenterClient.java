package com.get.helpcenter.feign;

import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.helpcenter.service.IHelpService;
import com.get.helpcenter.vo.HelpInfoVo;
import com.get.helpcenter.vo.HelpVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

/**
 * Feign实现
 */
@Slf4j
@RestController
@AllArgsConstructor
@VerifyPermission(IsVerify = false)
public class HelpCenterClient implements IHelpCenterClient {
    private final IHelpService helpService;

    @Override
    public Result<List<HelpVo>> getHelpDtoByHelpId(Set<Long> helpIds) {
        return Result.data(helpService.getHelpDtoByHelpId(helpIds));
    }

    @Override
    public Result<Boolean> updateByDemo() {
        int a = 10;
        if (a != 11) {
            log.info("数据不对，要回滚");
            throw new GetServiceException(LocaleMessageUtils.getMessage("inaccurate_data"));
        }
        return Result.data(true);
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<List<HelpInfoVo>> getHelpInfo() {
        return Result.data(helpService.getHelpInfo());
    }
}
