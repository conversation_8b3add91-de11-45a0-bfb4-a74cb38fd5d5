#服务器端口
server:
  port: 9112

spring:
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    dynamic:
      primary: institution
      datasource:
        institution:
          username: root
          password: fzhmysql
          url: *********************************************************************************************************************************************************************************************************************************************************************************************************
        partner:
          username: root
          password: fzhmysql
          url: *****************************************************************************************************************************************************************************************************************************************************************************************************
        appsystem:
          username: root
          password: fzhmysql
          url: ****************************************************************************************************************************************************************************************************************************************************************************************************
        platform:
          username: root
          password: fzhmysql
          url: ******************************************************************************************************************************************************************************************************************************************************************************************************

mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

logging:
  level:
    com.baomidou: DEBUG
