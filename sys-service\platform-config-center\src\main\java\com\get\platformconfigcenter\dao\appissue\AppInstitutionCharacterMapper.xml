<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.platformconfigcenter.dao.appissue.AppInstitutionCharacterMapper">
  <resultMap id="BaseResultMap" type="com.get.platformconfigcenter.entity.AppInstitutionCharacter">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_area_country_id" jdbcType="BIGINT" property="fkAreaCountryId" />
    <result column="fk_institution_id" jdbcType="BIGINT" property="fkInstitutionId" />
    <result column="fk_institution_course_id" jdbcType="BIGINT" property="fkInstitutionCourseId" />
    <result column="is_active" jdbcType="BIT" property="isActive" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <sql id="Base_Column_List">
    id, fk_area_country_id, fk_institution_id, is_active, gmt_create, gmt_create_user, 
    gmt_modified, gmt_modified_user, fk_institution_course_id
  </sql>
  <!--TODO 注释sql-->
<!--  <insert id="insert" parameterType="com.get.platformconfigcenter.entity.AppInstitutionCharacter" keyProperty="id" useGeneratedKeys="true">-->
<!--    insert into u_app_institution_character (id, fk_area_country_id, fk_institution_id, -->
<!--      is_active, gmt_create, gmt_create_user, fk_institution_course_id,-->
<!--      gmt_modified, gmt_modified_user)-->
<!--    values (#{id,jdbcType=BIGINT}, #{fkAreaCountryId,jdbcType=BIGINT}, #{fkInstitutionId,jdbcType=BIGINT}, -->
<!--      #{isActive,jdbcType=BIT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{fkInstitutionCourseId,jdbcType=BIGINT},-->
<!--      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})-->
<!--  </insert>-->
<!--  <insert id="insertSelective" parameterType="com.get.platformconfigcenter.entity.AppInstitutionCharacter">-->
<!--    insert into u_app_institution_character-->
<!--    <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        id,-->
<!--      </if>-->
<!--      <if test="fkAreaCountryId != null">-->
<!--        fk_area_country_id,-->
<!--      </if>-->
<!--      <if test="fkInstitutionId != null">-->
<!--        fk_institution_id,-->
<!--      </if>-->
<!--      <if test="isActive != null">-->
<!--        is_active,-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        gmt_create,-->
<!--      </if>-->
<!--      <if test="gmtCreateUser != null">-->
<!--        gmt_create_user,-->
<!--      </if>-->
<!--      <if test="gmtModified != null">-->
<!--        gmt_modified,-->
<!--      </if>-->
<!--      <if test="gmtModifiedUser != null">-->
<!--        gmt_modified_user,-->
<!--      </if>-->
<!--      <if test="fkInstitutionCourseId != null">-->
<!--        fk_institution_course_id,-->
<!--      </if>-->
<!--    </trim>-->
<!--    <trim prefix="values (" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        #{id,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="fkAreaCountryId != null">-->
<!--        #{fkAreaCountryId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="fkInstitutionId != null">-->
<!--        #{fkInstitutionId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="isActive != null">-->
<!--        #{isActive,jdbcType=BIT},-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        #{gmtCreate,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtCreateUser != null">-->
<!--        #{gmtCreateUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="gmtModified != null">-->
<!--        #{gmtModified,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtModifiedUser != null">-->
<!--        #{gmtModifiedUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="fkInstitutionCourseId != null">-->
<!--        #{fkInstitutionCourseId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--    </trim>-->
<!--  </insert>-->
<!--  <update id="updateByPrimaryKeySelective" parameterType="com.get.platformconfigcenter.entity.AppInstitutionCharacter">-->
<!--    update u_app_institution_character-->
<!--    <set>-->
<!--      <if test="fkAreaCountryId != null">-->
<!--        fk_area_country_id = #{fkAreaCountryId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="fkInstitutionId != null">-->
<!--        fk_institution_id = #{fkInstitutionId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="isActive != null">-->
<!--        is_active = #{isActive,jdbcType=BIT},-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtCreateUser != null">-->
<!--        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="gmtModified != null">-->
<!--        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtModifiedUser != null">-->
<!--        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="fkInstitutionCourseId != null">-->
<!--        fk_institution_course_id = #{fkInstitutionCourseId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--    </set>-->
<!--    where id = #{id,jdbcType=BIGINT}-->
<!--  </update>-->
<!--  <update id="updateByPrimaryKey" parameterType="com.get.platformconfigcenter.entity.AppInstitutionCharacter">-->
<!--    update u_app_institution_character-->
<!--    set fk_area_country_id = #{fkAreaCountryId,jdbcType=BIGINT},-->
<!--      fk_institution_id = #{fkInstitutionId,jdbcType=BIGINT},-->
<!--      fk_institution_course_id = #{fkInstitutionCourseId,jdbcType=BIGINT},-->
<!--      is_active = #{isActive,jdbcType=BIT},-->
<!--      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},-->
<!--      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},-->
<!--      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},-->
<!--      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}-->
<!--    where id = #{id,jdbcType=BIGINT}-->
<!--  </update>-->
<!--  <select id="isExistByCourseId" resultType="java.lang.Boolean">-->
<!--    SELECT IFNULL(max(id),0) id from u_app_institution_character where fk_institution_course_id=#{courseId}-->
<!--  </select>-->
</mapper>