package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2020/7/22
 * @TIME: 10:49
 * @Description: 员工业务国家DTO
 **/
@Data
public class StaffAreaCountryVo extends BaseVoEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 国家编号
     */
    @NotBlank(message = "国家编号不能为空", groups = {Update.class})
    @ApiModelProperty(value = "国家编号", required = true)
    private String num;

    /**
     * 国家名称
     */
    @NotBlank(message = "国家编号不能为空", groups = {Update.class})
    @ApiModelProperty(value = "国家名称", required = true)
    private String name;

    /**
     * 国家中文名称
     */
    @ApiModelProperty(value = "国家中文名称")
    private String nameChn;

    /**
     * 是否选择国家
     */
    @ApiModelProperty(value = "是否选择国家", required = true)
    @NotNull(message = "选中状态不能为空", groups = {Update.class})
    private Boolean isSelect;
}
