package com.get.resumecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2020/7/30
 * @TIME: 11:42
 * @Description:
 **/
@Data
public class IndustryTypeDto  extends BaseVoEntity implements Serializable {
        private static final long serialVersionUID = 1L;

    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    @NotBlank(message = "类型名称不能为空", groups = {Add.class, Update.class})
    private String typeName;
    /**
     * 子类型名称
     */
    @ApiModelProperty(value = "子类型名称")
    private String subTypeName;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序")
    private Integer viewOrder;
}
