package com.get.remindercenter.component;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.remindercenter.dao.EmailSenderQueueMapper;
import com.get.remindercenter.dao.EmailTemplateMapper;
import com.get.remindercenter.dao.RemindTaskMapper;
import com.get.remindercenter.dao.RemindTaskQueueMapper;
import com.get.remindercenter.dto.InsuranceCommissionNoticeEmailDto;
import com.get.remindercenter.dto.LaborContractExpiryDto;
import com.get.remindercenter.dto.SummitRegistrationReminderDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.EmailTemplate;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.service.RemindTaskQueueService;
import com.get.remindercenter.utils.ReminderTemplateUtils;
import com.get.remindercenter.vo.StaffContractRemindVo;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.StringJoiner;

@Component("laborContractExpiryEmailHelper")
@Slf4j
public class LaborContractExpiryEmailHelper extends EmailAbstractHelper{

    @Resource
    private EmailTemplateMapper emailTemplateMapper;

    @Resource
    private EmailSenderQueueMapper emailSenderQueueMapper;


    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private RemindTaskQueueService remindTaskQueueService;

    @Resource
    private RemindTaskQueueMapper remindTaskQueueMapper;

    @Resource
    private RemindTaskMapper remindTaskMapper;


    @Override
    public void sendMail(EmailSenderQueue emailSenderQueue) {
        StringJoiner failedEmails = new StringJoiner("; "); // 新增：记录失败邮箱
        try {
            LaborContractExpiryDto laborContractExpiryDto = assembleEmailData(emailSenderQueue);
            StringJoiner emailsCombined = new StringJoiner(", ");


            String template = setEmailTemplate(laborContractExpiryDto);
            for (String email : laborContractExpiryDto.getStaffEmailSet()) {
                try {
                    EmailSystemMQMessageDto emailSystemMQMessageDto = new EmailSystemMQMessageDto();
                    emailSystemMQMessageDto.setEmailSenderQueueId(emailSenderQueue.getId());
                    emailSystemMQMessageDto.setTitle(laborContractExpiryDto.getEmailTitle());
                    emailSystemMQMessageDto.setContent(template);
                    emailSystemMQMessageDto.setToEmail(email);
                    //emailSystemMQMessageDto.setToEmail("<EMAIL>");
                    remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
                    if (GeneralTool.isNotEmpty(email)) {
                        emailsCombined.add(email);
                    }
                }catch (Exception e){
                    // 记录发送失败的邮箱
                    String failedEmail = email;
                    failedEmails.add(failedEmail + "(" + (e.getMessage() != null ? e.getMessage() : "发送失败") + ")");
                    log.error("邮件发送失败 -  email: {}, 原因: {}", failedEmail, e.getMessage());
                }
            }
            LambdaUpdateWrapper<EmailSenderQueue> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(EmailSenderQueue::getId, emailSenderQueue.getId())  // 更新条件（按ID匹配）
                    .set(EmailSenderQueue::getEmailTo, emailsCombined.toString());  // 只更新 emailTo 字段
            // 如果 failedEmails 不为空，则额外更新 errorMessage
            if (failedEmails.length() > 0) {
                updateWrapper.set(EmailSenderQueue::getErrorMessage, "失败邮箱: " + failedEmails.toString());
            }
            emailSenderQueueMapper.update(null, updateWrapper);  // 传入 null，由 updateWrapper 控制更新
        } catch (Exception e) {
            log.error("laborContractExpiryEmailHelper error:{}", e);
            emailSenderQueue.setErrorMessage(e.getMessage());
            emailSenderQueue.setOperationCount(emailSenderQueue.getOperationCount() + 1);
            emailSenderQueue.setOperationStatus(-1);
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }
    }

    @Override
    public LaborContractExpiryDto assembleEmailData(EmailSenderQueue emailSenderQueue) {
        LaborContractExpiryDto reminderDto = new LaborContractExpiryDto();
        BeanUtils.copyProperties(emailSenderQueue, reminderDto);
        Boolean isOnDuty = remindTaskQueueMapper.getStaffContractIsOnDuty(emailSenderQueue.getFkTableId());
        List<String> emails = Lists.newArrayList();
        Long companyId = remindTaskQueueMapper.getStaffContractCompanyId(emailSenderQueue.getFkTableId());
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_CONTRACT_EXPIRATION.key, 2).getData();
        String configValue2 = companyConfigMap.get(companyId);
        emails = new ArrayList<>(JSON.parseArray(configValue2, String.class));
        Set<String> staffEmailSet = new HashSet<>();
        if (isOnDuty) {
            staffEmailSet.addAll(emails);
        }
        //获取中英文配置
        Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
        String versionValue2 = versionConfigMap.get(companyId);
        if(GeneralTool.isEmpty(versionValue2)){
                versionValue2 = "zh";
        }
        StaffContractRemindVo staffContractRemindDto = remindTaskMapper.getStaffContractRemindDto(emailSenderQueue.getFkTableId());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String title = null;
        if (!versionValue2.equals("en")) {
           title = "【" + staffContractRemindDto.getStaffFullName() + "】员工到期合同提醒，合同到期日：" + simpleDateFormat.format(staffContractRemindDto.getContractEndDate());
        }else {
            title = "[" + staffContractRemindDto.getStaffFullName() + "] Employee contract expiration reminder, contract expiration date：" + simpleDateFormat.format(staffContractRemindDto.getContractEndDate());
        }
        Map<String, String> map = new HashMap<>();
        map.put("companyName", staffContractRemindDto.getCompanyName());
        map.put("staffFullName", staffContractRemindDto.getStaffFullName());
        map.put("departmentName", staffContractRemindDto.getDepartmentName());
        map.put("positionName", staffContractRemindDto.getPositionName());
        map.put("contractEndDate", simpleDateFormat.format(staffContractRemindDto.getContractEndDate()));
        reminderDto.setMap(map);
        reminderDto.setStaffEmailSet(staffEmailSet);
        reminderDto.setLanguageCode(versionValue2);
        return reminderDto;
    }

    private String setEmailTemplate(LaborContractExpiryDto reminderDto) {
        List<EmailTemplate> remindTemplates = new ArrayList<>();
        if (reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.STAFF_CONTRACT_EXPIRE.getEmailTemplateKey())) {
            remindTemplates = emailTemplateMapper.selectList(Wrappers.<EmailTemplate>lambdaQuery().eq(EmailTemplate::getEmailTypeKey, EmailTemplateEnum.STAFF_CONTRACT_EXPIRE.getEmailTemplateKey()));
        }

        if (GeneralTool.isEmpty(remindTemplates)) {
            log.error("邮箱模板不存在，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        String emailTemplate =null;
        if (!reminderDto.getLanguageCode().equals("en")) {
            emailTemplate = remindTemplates.get(0).getEmailTemplate();
        }else {
            emailTemplate = remindTemplates.get(0).getEmailTemplateEn();
        }
        emailTemplate  = ReminderTemplateUtils.getReminderTemplate(reminderDto.getMap(), emailTemplate);
        if (GeneralTool.isEmpty(emailTemplate)) {
            log.error("邮箱模板内容为空，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        emailTemplate = emailTemplate.replace("#{taskTitle}", reminderDto.getEmailTitle());
        //把emailTemplate插入到父模板里
        String parentEmailTemplate = null;
        if(GeneralTool.isNotEmpty(remindTemplates.get(0).getFkParentEmailTemplateId())&&remindTemplates.get(0).getFkParentEmailTemplateId()!=0){
            EmailTemplate parentTemplate = emailTemplateMapper.selectById(remindTemplates.get(0).getFkParentEmailTemplateId());
            Map map = new HashMap();
            map.put("subtemplate",emailTemplate);
            if (reminderDto.getLanguageCode().equals("en")) {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplateEn());
            }else {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplate());
            }

        }
        return parentEmailTemplate;
    }


}
