package com.get.officecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.officecenter.vo.WorkScheduleTimeConfigVo;
import com.get.officecenter.service.WorkScheduleTimeConfigService;
import com.get.officecenter.dto.WorkScheduleTimeConfigAddDto;
import com.get.officecenter.dto.WorkScheduleTimeConfigListDto;
import com.get.officecenter.dto.WorkScheduleTimeConfigUpdateDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/7 12:42
 */
@Api(tags = "工作时间设置管理")
@RestController
@RequestMapping("office/workScheduleTimeConfig")
public class WorkScheduleTimeConfigController {

    @Resource
    private WorkScheduleTimeConfigService workScheduleTimeConfigService;

    /**
     * 查询工作时间设置
     *
     * @Date 12:47 2022/11/7
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/工作时间设置管理/查询工作时间设置")
    @PostMapping("datas")
    public ResponseBo<WorkScheduleTimeConfigVo> datas(@RequestBody SearchBean<WorkScheduleTimeConfigListDto> page) {
        List<WorkScheduleTimeConfigVo> datas = workScheduleTimeConfigService.getWorkScheduleTimeConfigs(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 工作时间设置详情
     *
     * @Date 12:54 2022/11/7
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DETAIL, description = "办公中心/工作时间设置管理/工作时间设置详情")
    @GetMapping("/{id}")
    public ResponseBo<WorkScheduleTimeConfigVo> detail(@PathVariable("id") Long id) {
        WorkScheduleTimeConfigVo data = workScheduleTimeConfigService.findWorkScheduleTimeConfigById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 新增工作时间设置
     *
     * @Date 12:50 2022/11/7
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "办公中心/工作时间设置管理/新增工作时间设置")
    @PostMapping("add")
    public ResponseBo add(@RequestBody WorkScheduleTimeConfigAddDto workScheduleTimeConfigAddDto) {
        workScheduleTimeConfigService.add(workScheduleTimeConfigAddDto);
        return SaveResponseBo.ok();
    }

    /**
     * 更新工作时间设置
     *
     * @Date 12:50 2022/11/7
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.EDIT, description = "办公中心/工作时间设置管理/更新工作时间设置")
    @PostMapping("update")
    public ResponseBo<WorkScheduleTimeConfigVo> update(@RequestBody @Validated(WorkScheduleTimeConfigUpdateDto.Update.class) WorkScheduleTimeConfigUpdateDto workScheduleTimeConfigUpdateDto) {
        workScheduleTimeConfigService.updateWorkScheduleTimeConfig(workScheduleTimeConfigUpdateDto);
        return UpdateResponseBo.ok();
    }

    /**
     * 获取当前登录人部门对应的工作时间配置
     * @return
     */
    @ApiOperation(value = "获取当前登录人部门对应的工作时间配置", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DETAIL, description = "办公中心/工作时间设置管理/获取当前登录人部门对应的工作时间配置")
    @PostMapping("getWorkScheduleTimeConfigDto")
    public ResponseBo<WorkScheduleTimeConfigVo> getWorkScheduleTimeConfigDto(){
       return new ResponseBo(workScheduleTimeConfigService.getWorkScheduleTimeConfigDto());
    }

    /**
     * 删除工作时间设定
     *
     * @Date 12:03 2022/11/21
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DELETE, description = "办公中心/工作时间设置管理/删除工作时间设定")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        workScheduleTimeConfigService.delete(id);
        return DeleteResponseBo.ok();
    }

}
