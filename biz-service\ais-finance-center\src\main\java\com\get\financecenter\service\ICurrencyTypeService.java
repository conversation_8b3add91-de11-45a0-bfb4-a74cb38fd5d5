package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.financecenter.vo.CurrencyTypeVo;
import com.get.financecenter.entity.CurrencyType;
import com.get.financecenter.dto.CurrencyTypeDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/9/17 11:46
 * @verison: 1.0
 * @description:
 */
public interface ICurrencyTypeService extends BaseService<CurrencyType> {

    /**
     * @return com.get.financecenter.vo.CurrencyTypeDto
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    CurrencyTypeVo findCurrencyTypeById(Long id);

    /**
     * @return void
     * @Description :批量新增
     * @Param [currencyTypeVos]
     * <AUTHOR>
     */
    void batchAdd(List<CurrencyTypeDto> currencyTypeDtos);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.financecenter.vo.CurrencyTypeDto
     * @Description :修改
     * @Param [currencyTypeVo]
     * <AUTHOR>
     */
    CurrencyTypeVo updateCurrencyType(CurrencyTypeDto currencyTypeDto);

    /**
     * @return java.util.List<com.get.financecenter.vo.CurrencyTypeDto>
     * @Description :列表
     * @Param [currencyTypeVo, page]
     * <AUTHOR>
     */
    List<CurrencyTypeVo> getCurrencyTypes(CurrencyTypeDto currencyTypeDto, Page page);

    /**
     * @return void
     * @Description :上移下移
     * @Param [currencyTypeVos]
     * <AUTHOR>
     */
    void movingOrder(List<CurrencyTypeDto> currencyTypeDtos);

    /**
     * 货币类型下拉框数据
     *
     * @return
     */
    List<CurrencyTypeVo> getCurrencyTypeList();


    /**
     * 货币类型下拉框数据不包含外币
     *
     * @return
     */
    List<CurrencyTypeVo> getCurrencyTypeSelect();

    /**
     * feign调用 根据币种编号查找币种名称
     *
     * @param num
     * @return
     */
    String getCurrencyNameByNum(String num);

    /**
     * @Description :feign调用 根据币种编号nums查找币种名称map
     * @Param [nums]
     * <AUTHOR>
     */
    Map<String, String> getCurrencyNamesByNums(Set<String> nums);


    /**
     * @Description: feign调用 根据币种编号nums查找币种名称map（只返回名称）
     * @Author: Jerry
     * @Date:11:49 2021/11/20
     */
    Map<String, String> getNewCurrencyNamesByNums(Set<String> nums);

    /**
     * 获取所有币种
     *
     * @return
     */
    Map<String, String> getAllCurrencyTypeNames();

    /**
     * 根据PublicLevel获取币种
     * @param key
     * @return
     */
    List<CurrencyTypeVo> getCurrencyByPublicLevel(Integer key);

    Map<String, CurrencyTypeVo> getCurrencyTypeDtoByNums(Set<String> currencyNums);
}
