#参考网站：https://www.bbsmax.com/A/kmzLrw9K5G/
# 邮件服务器的SMTP地址，可选，默认为smtp.<发件人邮箱后缀>
# 阿里企业邮箱 smtp.mxhichina.com 或者自己的 smtp.testmymail.com
#host = *************
# 邮件服务器的SMTP端口，可选，默认25 ，ssl安全的用465
#port = 465
# 发件人（必须正确，否则发送失败）
#from = <EMAIL>
#from = <EMAIL>
# 用户名，默认为发件人邮箱前缀
#user = zhangsan #这样配置就会报错 526  Authentication failure 。 如果不配置这个user，hutool默认取from前缀，还是会报错。
#阿里企业邮箱，这个user需要配置和from一样。
#user = <EMAIL>
#user = <EMAIL>
# 密码（注意，某些邮箱需要为SMTP服务单独设置授权码，详情查看相关帮助）
#阿里企业邮箱，这个密码就是自己的邮箱密码
#pass = Yxznalina86@
#pass = Gea321321
# 使用SSL安全连接 ; 在使用QQ或Gmail邮箱时，需要强制开启SSL支持. 阿里企业邮箱 port=465时需要sslEnable=true
#sslEnable = true
host =smtp.exmail.qq.com
port = 465
from=<EMAIL>
user=<EMAIL>
pass=GZEtLhkf6WzpcPd9
sslEnable = true
# 指定实现javax.net.SocketFactory接口的类的名称,这个类将被用于创建SMTP的套接字
socketFactoryClass = javax.net.ssl.SSLSocketFactory
# 如果设置为true,未能创建一个套接字使用指定的套接字工厂类将导致使用java.net.Socket创建的套接字类, 默认值为true
socketFactoryFallback = true