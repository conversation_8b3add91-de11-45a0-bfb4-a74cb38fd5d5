package com.get.aisplatformcenterap.vo.work;

import com.get.aisplatformcenterap.entity.MFeedbackOrderEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class MFeedbackOrderVo extends MFeedbackOrderEntity {


    @ApiModelProperty("公司名字")
    private String companyName;

    @ApiModelProperty("类型名称")
    private String typeName;

    @ApiModelProperty("所属平台名称")
    private String platformName;

    @ApiModelProperty("处理人")
    private String staffName;

    @ApiModelProperty("处理时间")
    private Date gmtReplyTime;





}
