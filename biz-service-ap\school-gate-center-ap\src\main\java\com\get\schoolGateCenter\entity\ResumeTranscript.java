package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-24
 */
@Data
@TableName("m_resume_transcript")
@ApiModel(value="MResumeTranscript对象", description="")
public class ResumeTranscript extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "简历关联id")
    private Long fkResumeId;

    @ApiModelProperty(value = "结束年度")
    private Integer toYear;

    @ApiModelProperty(value = "开始年度")
    private Integer byYear;

    @ApiModelProperty(value = "学期")
    private String semester;

    @ApiModelProperty(value = "学校名字")
    private String institutionName;

    @ApiModelProperty(value = "年级名次")
    private Integer gradeRank;

    @ApiModelProperty(value = "年级学生总数")
    private Integer numberOfGradeStudent;

    @ApiModelProperty(value = "班级名次")
    private String classRanking;

    @ApiModelProperty(value = "班级学生总数")
    private Integer numberOfClassStudent;

    @ApiModelProperty(value = "行为")
    private String behavior;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "标记")
    private String mark;

}
