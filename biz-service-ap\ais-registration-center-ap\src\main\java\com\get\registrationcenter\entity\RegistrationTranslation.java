package com.get.registrationcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("s_translation")
public class RegistrationTranslation extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;
    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    private Long fkTableId;
    /**
     * 翻译配置Id
     */
    @ApiModelProperty(value = "翻译配置Id")
    private Long fkTranslationMappingId;
    /**
     * 语言枚举code：zh-hk/en-us
     */
    @ApiModelProperty(value = "语言枚举code：zh-hk/en-us")
    private String languageCode;
    /**
     * 翻译内容
     */
    @ApiModelProperty(value = "翻译内容")
    private String translation;
}