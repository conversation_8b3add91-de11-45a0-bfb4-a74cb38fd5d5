package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.dto.ContactPersonDto;
import com.get.institutioncenter.vo.ContactPersonVo;
import com.get.institutioncenter.entity.ContactPerson;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ContactPersonMapper extends BaseMapper<ContactPerson> {


    /**
     * 根据合同联系人列表
     *
     * @param contactPersonDto
     * @return
     */
    List<ContactPersonVo> datas(ContactPersonDto contactPersonDto);

    /**
     * 根据学校查找是否联系人
     *
     * @param id
     * @return
     */
    Integer getCountByInstitutionId(Long id);

    /**
     * 校验 联系人中是否有对应的学校提供商绑定
     *
     * @Date 10:48 2021/4/16
     * <AUTHOR>
     */
    Boolean checkProviderInfoIsEmptyByProviderId(@Param("providerId") Long providerId, @Param("tableName") String tableName);
}