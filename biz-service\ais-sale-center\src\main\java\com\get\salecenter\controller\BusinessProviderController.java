package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.salecenter.dto.BusinessProviderDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.query.BusinessProviderQueryDto;
import com.get.salecenter.service.IBusinessProviderService;
import com.get.salecenter.vo.BusinessChannelVo;
import com.get.salecenter.vo.BusinessProviderSelectVo;
import com.get.salecenter.vo.BusinessProviderVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * author:Neil
 * Time: 16:55
 * Date: 2022/6/20
 * Description:
 */
@Api(tags = "业务提供商管理")
@RestController
@RequestMapping("sale/businessProvider")
public class BusinessProviderController {
    @Resource
    private IBusinessProviderService businessProviderService;

    @ApiOperation(value = "保存提供商")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER)
    @PostMapping("editProviderCompanyRelation")
    public ResponseBo editProviderCompanyRelation(@RequestBody  @Validated(BusinessProviderDto.Add.class)
                                                  ValidList<BusinessProviderDto> validList) {
        businessProviderService.editProviderCompanyRelation(validList);
        return UpdateResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "显示选择的公司", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER)
    @PostMapping("getProviderCompanyRelation/{providerId}")
    public ResponseBo<CompanyTreeVo> getProviderCompanyRelation(@PathVariable("providerId")Long providerId) {
        return new ListResponseBo<>(businessProviderService.getProviderCompanyRelation(providerId));
    }


    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "所有公司", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER)
    @PostMapping("allCompany")
    public ResponseBo<List<CompanyTreeVo>> allCompany(@RequestParam(name = "companyId", required = false) Long companyId) {
        return new ResponseBo<>(businessProviderService.allCompany(companyId));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "业务类型下拉框", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/业务提供商管理/业务类型下拉框")
    @PostMapping("getBusinessTypeSelect")
    public ResponseBo getBusinessTypeSelect() {
        return new ListResponseBo<>(ProjectKeyEnum.enumsTranslation2Arrays(ProjectKeyEnum.BUSINESS_TYPE));
    }

    /**
     * @return com.get.common.result.ListResponseBo<com.get.salecenter.vo.AgentEventTypeVo>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/业务提供商管理/查询列表")
    @PostMapping("datas")
    public ListResponseBo<BusinessProviderVo> datas(@RequestBody SearchBean<BusinessProviderQueryDto> page) {
        List<BusinessProviderVo> datas = businessProviderService.getBusinessProviderDtos(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [agentEventVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/业务提供商管理/新增业务提供商")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(BusinessProviderDto.Add.class) ValidList<BusinessProviderDto> businessProviderDto) {
        businessProviderService.addBusinessProvider(businessProviderDto);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "详情", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/业务提供商管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<BusinessChannelVo> detail(@PathVariable("id") Long id) {
        return new ResponseBo(businessProviderService.findBusinessProviderById(id));
    }

    @ApiOperation(value = "代理附件保存接口")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理管理/代理附件保存接口")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> addBusinessProviderMedia(@RequestBody @Validated(MediaAndAttachedDto.Add.class) ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(businessProviderService.addBusinessProviderMedia(mediaAttachedVo));
    }

    @ApiOperation(value = "查询附件", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理管理/查询附件")
    @PostMapping("getBusinessProviderMedia")
    public ResponseBo<MediaAndAttachedVo> getBusinessProviderMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> staffMedia = businessProviderService.getBusinessProviderMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AgentEventTypeVo>
     * @Description: 修改信息
     * @Param [agentEventVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/业务提供商管理/更新")
    @PostMapping("update")
    public ResponseBo<BusinessChannelVo> update(@RequestBody @Validated(BusinessProviderDto.Update.class) BusinessProviderDto businessProviderDto) {
        return UpdateResponseBo.ok(businessProviderService.updateBusinessProvider(businessProviderDto));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/业务提供商管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        businessProviderService.deleteBusinessProvider(id);
        return DeleteResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "渠道下拉框数据", notes = "")
    @GetMapping("providerSelect")
    public ResponseBo<BaseSelectEntity> providerSelect(@RequestParam(value = "tableName",required = false) String tableName,@RequestParam(value = "oldId",required = false) Long oldId,@RequestParam(value = "companyId") Long companyId) {
        List<BaseSelectEntity> list = businessProviderService.providerSelect(tableName,oldId,companyId);
        return new ListResponseBo<>(list);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.officecenter.vo.LeaveApplicationFormTypeVo>
     * @Description :渠道下拉框数据
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "推荐来源渠道(本地市场来源)下拉框数据", notes = "")
    @GetMapping("getClientSourceProviderSelect")
    public ResponseBo<BaseSelectEntity> getClientSourceProviderSelect(@RequestParam(value = "tableName",required = false) String tableName,@RequestParam(value = "fkCompanyId",required = false) Long fkCompanyId) {
        List<BaseSelectEntity> list = businessProviderService.getClientSourceProviderSelect(tableName,fkCompanyId);
        return new ListResponseBo<>(list);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取服务提供商下拉框数据", notes = "学生留学保险服务")
    @PostMapping("getBusinessProviderSelect")
    public ResponseBo<BusinessProviderSelectVo> getBusinessProviderSelect(@RequestParam(value = "fkTypeKey") String fkTypeKey, @RequestParam(value = "fkCompanyId") Long fkCompanyId) {
        List<BusinessProviderSelectVo> list = businessProviderService.getBusinessProviderSelect(fkTypeKey, fkCompanyId);
        return new ListResponseBo<>(list);
    }

}
