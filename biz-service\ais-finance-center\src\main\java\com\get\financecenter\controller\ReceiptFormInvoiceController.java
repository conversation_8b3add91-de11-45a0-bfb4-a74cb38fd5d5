package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.financecenter.service.IReceiptFormInvoiceService;
import com.get.financecenter.dto.BalancingReceiptFormDto;
import com.get.financecenter.dto.QuickReceiptFormDto;
import com.get.financecenter.dto.ReceiptFormInvoiceDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @DATE: 2021/1/6
 * @TIME: 18:44
 * @Description:
 **/
@Api(tags = "绑定发票管理")
@RestController
@RequestMapping("finance/receiptFormInvoice")
public class ReceiptFormInvoiceController {
    @Resource
    private IReceiptFormInvoiceService receiptFormInvoiceService;

    /**
     * 修改信息
     *
     * @param receiptFormInvoiceDto
     * @return
     */
    @ApiOperation(value = "绑定", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/付款管理/绑定")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated(ReceiptFormInvoiceDto.Add.class)  ReceiptFormInvoiceDto receiptFormInvoiceDto) {
        receiptFormInvoiceService.update(receiptFormInvoiceDto);
        return ResponseBo.ok();
    }


    /**
     * @param id
     * @return
     */

    @ApiOperation(value = "删除")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/付款管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        receiptFormInvoiceService.delete(id);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "快速补单")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/收款管理/绑定")
    @PostMapping("quickCreateReceiptForm")
    public ResponseBo quickCreateReceiptForm(@RequestBody @Validated QuickReceiptFormDto quickReceiptFormDto){
        return receiptFormInvoiceService.doQuickCreateReceiptForm(quickReceiptFormDto);
    }

    @ApiOperation(value = "批量找平")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/收款管理/批量找平")
    @PostMapping("batchBalancingReceiptForm")
    public ResponseBo batchBalancingReceiptForm(@RequestBody @Validated ValidList<BalancingReceiptFormDto> balancingReceiptFormDtos){
        return receiptFormInvoiceService.batchBalancingReceiptForm(balancingReceiptFormDtos);
    }

}
