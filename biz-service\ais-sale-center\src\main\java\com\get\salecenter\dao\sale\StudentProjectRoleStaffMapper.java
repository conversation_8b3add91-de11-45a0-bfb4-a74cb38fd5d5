package com.get.salecenter.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.salecenter.vo.ClientProjectRoleStaffVo;
import com.get.salecenter.vo.SelItem;
import com.get.salecenter.vo.StudentOfferRoleAndStaffVo;
import com.get.salecenter.vo.StudentProjectRoleStaffVo;
import com.get.salecenter.entity.StudentProjectRole;
import com.get.salecenter.entity.StudentProjectRoleStaff;
import com.get.salecenter.dto.StudentProjectRoleStaffDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Mapper
public interface StudentProjectRoleStaffMapper extends BaseMapper<StudentProjectRoleStaff> {

    int insertSelective(StudentProjectRoleStaff record);

    int batchInsertSelective(@Param("list")List<StudentProjectRoleStaff> list);

    /**
     * @return java.lang.Integer
     * @Description:
     * @Param [projectRoleStaff]
     * <AUTHOR>
     */
    Integer validateEdit(StudentProjectRoleStaff projectRoleStaff);

    /**
     * 根据员工ids查询绑定项目对应的学生ids
     *
     * @Date 17:26 2021/6/24
     * <AUTHOR>
     */
    List<Long> getStudetnIdsByStaffId(@Param("staffIds") List<Long> staffIds);

    /**
     * 获取绑定的项目成员
     *
     * @Date 11:17 2021/7/30
     * <AUTHOR>
     */
    List<StudentProjectRoleStaffVo> selectProjectStaff(@Param("key")String key, @Param("itemIdList") List<Long> itemIdList);


    List<StudentProjectRoleStaffVo> selectProjectStaffById(@Param("key") String key, @Param("fkTableId") Long fkTableId);

    List<StudentProjectRoleStaffVo> selectProjectStaffByIds(@Param("key")String key, @Param("itemIdList") List<Long> itemIdList);
    /**
     * 根据项目角色id和人员集合获取学生
     *
     * @param projectRoleId
     * @param fkStaffIds
     * @return
     */
    List<Map<String, Object>> getStudentAndStaffByProjectRoleId(@Param("projectRoleId") Long projectRoleId,
                                                                @Param("fkStaffIds") Set<Long> fkStaffIds);

    /**
     * 根据id set获取StudentProjectRole
     *
     * @param projectRoleIdSet
     * @return
     */
    List<StudentProjectRole> getStudentProjectRoleByIdSet(@Param("projectRoleIdSet") Set<Long> projectRoleIdSet);

    /**
     * 根据id set获取StudentProjectRole（顾问、跟进顾问）
     *
     * @param projectRoleIdSet
     * @return
     */
    List<StudentProjectRole> getStudentProjectConsultantRoleByIdSet(@Param("projectRoleIdSet") Set<Long> projectRoleIdSet);

    /**
     * 根据学生申请方案Id和用户ID获取对应角色列表
     *
     * @param fkStudentOfferId
     * @param staffId
     * @return
     */
    List<StudentProjectRoleStaffVo> getProjectRole(@Param("fkStudentOfferId") Long fkStudentOfferId, @Param("staffIds") List<Long> staffId);


    @DS("saledb-doris")
    List<StudentProjectRoleStaff> getProjectRoleStaff(IPage<StudentProjectRoleStaff> pages, @Param("projectRoleStaffDto") StudentProjectRoleStaffDto projectRoleStaffDto,
                                                      @Param("staffFollowerIds")List<Long> staffFollowerIds, @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                      @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                      @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    List<StudentProjectRoleStaff> getClientProjectRoleStaff(IPage<StudentProjectRoleStaff> pages, @Param("projectRoleStaffDto") StudentProjectRoleStaffDto projectRoleStaffVo,
                                                      @Param("staffFollowerIds")List<Long> staffFollowerIds);


    List<Map<Long, String>> getProjectRoleStaffNoPage(@Param("fkTableName") String fkTableName, @Param("studentIds") List<Long> studentIds);

    List<SelItem> getAFollowUpConsultant(@Param("fkTableName") String fkTableName, @Param("clientIds") Set<Long> clientIds,@Param("key")String key);

    /**
     * 根据申请方案Id获取所有项目成员
     *
     * @return
     */
    List<StudentProjectRoleStaffVo> getAllProjectRoleStaff(@Param("fkStudentOfferId") Long fkStudentOfferId);

    /**
     * 根据学生id获取所有项目成员
     *
     * @return
     */
    List<StudentProjectRoleStaffVo> selectProjectStaffByStudentId(@Param("studentId") Long studentId);

    /**
     * @param roleKey
     * @return
     */
    List<StudentProjectRoleStaffVo> getItemIdByRoleKey(@Param("roleKey") String roleKey, @Param("tableIds")Set<Long> tableIds);

    /**
     * 获取staffid
     *
     * @param roleKey
     * @param tableIds
     * @return
     */
    List<StudentProjectRoleStaffVo> getStaffIdByRoleKey(@Param("roleKey") String roleKey, @Param("tableIds")Set<Long> tableIds);

    List<StudentOfferRoleAndStaffVo> selectListRoleByIds(@Param("offerIds") Set<Long> offerIds);

    List<StudentOfferRoleAndStaffVo> selectListRoleByClientIds(@Param("offerIds") Set<Long> offerIds);

    List<ClientProjectRoleStaffVo> selectProjectStaffByClientId(@Param("clientId") Long clientId);

    List<ClientProjectRoleStaffVo> selectProjectStaffByClientIds(@Param("clientIds") List<Long> clientIds);
}