package com.get.permissioncenter.dao;

import   com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.permissioncenter.vo.ResourceVo;
import com.get.permissioncenter.entity.Resource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ResourceMapper extends BaseMapper<Resource> {

    int insert(Resource record);

    int insertSelective(Resource record);

    List<Resource> getResources(@Param("keyWord") String keyWord);

    List<String> getApiKeysByResourceKeys(@Param("resourcekeys") List<String> resourcekeys);

    List<ResourceVo> getChildResources(@Param("id") Long id, @Param("fkCompanyId") Long fkCompanyId);

    List<ResourceVo> getTopLevelResources(@Param("fkCompanyId") Long fkCompanyId);

    Integer getMaxViewOrder();
}