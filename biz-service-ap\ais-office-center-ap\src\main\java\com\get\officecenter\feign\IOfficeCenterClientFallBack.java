package com.get.officecenter.feign;

import com.get.common.result.ResponseBo;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.officecenter.entity.Task;
import com.get.officecenter.entity.TaskItem;
import com.get.officecenter.vo.*;
import com.get.officecenter.entity.LeaveApplicationForm;
import com.get.officecenter.entity.OfficeMediaAndAttached;
import com.get.officecenter.dto.CreateTaskAndTaskItemDto;
import com.get.officecenter.dto.LeaveLogDto;
import com.get.officecenter.dto.LeaveStockDto;
import com.get.officecenter.dto.query.LeaveApplicationFormQueryDto;
import com.get.permissioncenter.vo.WxCpUserVo;
import com.get.remindercenter.vo.ReminderTaskCountVo;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Feign失败配置
 */
@Component
@VerifyPermission(IsVerify = false)
public class IOfficeCenterClientFallBack implements IOfficeCenterClient {


    @Override
    public Result<Boolean> changeStatus(Integer status, String tableName, Long businessKey) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<String> getLeaveTypeNameById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<LeaveApplicationForm> getLeaveApplicationForm(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Boolean> addSystemLeaveLog(LeaveLogDto leaveLogDto) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Long> addSystem(LeaveStockDto leaveStockDto) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> updateSystemLeavetock(LeaveStockDto leaveStockDto) {
        return Result.fail("操作失败");
    }

    @Override
    public List<LeaveStockVo> getLeaveStockDtos(LeaveStockDto leaveStockDto) {
        return null;
    }

    @Override
    public String getLeaveTypeKeyById(Long id) {
        return "";
    }

    @Override
    public List<LeaveLogVo> getSystemLeaveLog(LeaveLogDto leaveLogDto) {
        return null;
    }

    @Override
    public Result<List<ReminderTaskCountVo>> getApplicationAndApprovalCount(Long staffId) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> hasMediaAndAttach(String key, Long id) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<WxCpUserVo> getWxCpUserIdByCode(String code) {
        return Result.fail("操作失败");
    }

    @Override
    public List<LeaveStockVo> getEfficientLeaveStockDtos(LeaveStockDto leaveStockDto) {
        return null;
    }

    @Override
    public Result<List<OfficeMediaAndAttached>> getOfficeMediaAndAttacheds(Long id,String loginId) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<List<LeaveApplicationFormExportVo>> getLeaveApplicationFormExportDtos(LeaveApplicationFormQueryDto leaveApplicationFormVo) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> addTaskAndTaskItem(CreateTaskAndTaskItemDto createTaskAndTaskItemDto) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<CustomTaskVo> getTask(Long taskId) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<List<TaskItem>> getTaskList(Long taskId) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<List<Long>> getTaskItemIds(Long taskId) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<List<Long>> getUnfinishedTaskItemReceiver(Long taskId) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<TaskItem> getTaskItem(Long taskItemId) {
        return null;
    }

    @Override
    public Result<List<Task>> getTaskByEndTime() {
        return null;
    }
}
