package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("r_student_offer_item_settlement_batch_exchange")
public class StudentOfferItemSettlementBatchExchange extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 财务结算汇总批次号
     */
    @ApiModelProperty(value = "财务结算汇总批次号")
    @Column(name = "num_settlement_batch")
    private String numSettlementBatch;
    /**
     * 币种编号（原币种）
     */
    @ApiModelProperty(value = "币种编号（原币种）")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;
    /**
     * 币种编号（兑换币种）
     */
    @ApiModelProperty(value = "币种编号（兑换币种）")
    @Column(name = "fk_currency_type_num_exchange")
    private String fkCurrencyTypeNumExchange;
    /**
     * 兑换汇率
     */
    @ApiModelProperty(value = "兑换汇率")
    @Column(name = "exchange_rate")
    private BigDecimal exchangeRate;
    /**
     * 手续费
     */
    @ApiModelProperty(value = "手续费")
    @Column(name = "service_fee")
    private BigDecimal serviceFee;
    /**
     * 学生代理Id
     */
    @ApiModelProperty(value = "学生代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
}