package com.get.remindercenter.service;

import com.get.remindercenter.dto.AliyunSendMailDto;
import com.get.remindercenter.dto.BatchEmailPromotionQueueDto;
import com.get.remindercenter.vo.AliyunSendMailVo;
import com.get.remindercenter.vo.BatchEmailPromotionQueueVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/20 12:58
 */
public interface IBatchEmailPromotionQueueService {

    List<BatchEmailPromotionQueueVo> getSendNewsEamilQueues();

    Boolean addTask2NewsEmailQueen(AliyunSendMailDto aliyunSendMailDto);

    Boolean deleteMsgFromQueue(Long id);
}
