package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.ClientApprovalVo;
import com.get.salecenter.entity.RStudentToClientApproval;
import com.get.salecenter.dto.ClientApprovalDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface RStudentToClientApprovalMapper extends GetMapper<RStudentToClientApproval>,BaseMapper<RStudentToClientApproval> {

    List<ClientApprovalVo> getClientApprovalList(IPage<ClientApprovalVo> iPage, @Param("clientApprovalDto") ClientApprovalDto clientApprovalDto,
                                                 @Param("loginIds")  List<String> loginIds,
                                                 @Param("isStaffIdApproval") Boolean isStaffIdApproval,
                                                 @Param("staffFollowerIds") List<Long> staffFollowerIds);
}