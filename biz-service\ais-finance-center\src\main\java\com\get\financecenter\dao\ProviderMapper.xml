<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.ProviderMapper">

  <select id="getProviderSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    select id , concat(if(is_active=0,'【无效】',''),name) name ,is_active status from m_provider

  </select>
</mapper>