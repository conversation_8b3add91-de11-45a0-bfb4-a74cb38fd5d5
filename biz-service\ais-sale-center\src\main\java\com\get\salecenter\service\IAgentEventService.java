package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.salecenter.vo.AgentEventVo;
import com.get.salecenter.vo.AgentEventTypeVo;
import com.get.salecenter.dto.AgentEventDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/9/11
 * @TIME: 17:11
 * @Description:
 **/
public interface IAgentEventService {

    /**
     * 新增事件
     *
     * @param agentEventDto
     * @return
     * @
     */
    Long addAgentEvent(AgentEventDto agentEventDto);

    /**
     * 查询所有事件
     *
     * @param agentEventDto
     * @param page
     * @return
     * @
     */
    List<AgentEventVo> getAgentEventDtos(AgentEventDto agentEventDto, Page page);

    /**
     * 查询单个事件
     *
     * @param id
     * @return
     * @
     */
    AgentEventVo findAgentEventById(Long id);

    /**
     * 修改
     *
     * @param agentEventDto
     * @return
     * @
     */
    AgentEventVo updateAgentEvent(AgentEventDto agentEventDto);

    /**
     * @return void
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    void deleteAgentEvent(Long id);

    /**
     * @return java.util.List<com.get.salecenter.vo.AgentEventTypeVo>
     * @Description: 下拉框
     * @Param []
     * <AUTHOR>
     */
    List<AgentEventTypeVo> getAllEventType();
}
