package com.get.insurancecenter.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.insurancecenter.dto.order.CreditCardOrderListDto;
import com.get.insurancecenter.dto.order.OrderListDto;
import com.get.insurancecenter.dto.order.UpdateOrderDto;
import com.get.insurancecenter.entity.InsuranceCompany;
import com.get.insurancecenter.entity.InsuranceOrder;
import com.get.insurancecenter.entity.ProductType;
import com.get.insurancecenter.enums.OrderStatusEnum;
import com.get.insurancecenter.mapper.InsuranceCompanyMapper;
import com.get.insurancecenter.mapper.InsuranceOrderMapper;
import com.get.insurancecenter.mapper.PartnerCenterMapper;
import com.get.insurancecenter.mapper.ProductTypeMapper;
import com.get.insurancecenter.service.InsuranceOrderService;
import com.get.insurancecenter.vo.agent.AgentEntity;
import com.get.insurancecenter.vo.order.CreditCardOrderVo;
import com.get.insurancecenter.vo.order.OrderDetailVo;
import com.get.insurancecenter.vo.order.OrderInfoMap;
import com.get.permissioncenter.vo.CompanyVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/5/23
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class InsuranceOrderServiceImpl extends ServiceImpl<InsuranceOrderMapper, InsuranceOrder> implements InsuranceOrderService {

    @Autowired
    private InsuranceOrderMapper insuranceOrderMapper;
    @Autowired
    private InsuranceCompanyMapper insuranceCompanyMapper;
    @Autowired
    private ProductTypeMapper productTypeMapper;
    @Autowired
    private PartnerCenterMapper partnerCenterMapper;

    @Override
    public List<OrderDetailVo> orderList(OrderListDto params, Page page) {
        IPage<OrderDetailVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<OrderDetailVo> orderList = insuranceOrderMapper.selectOrderPage(pages, params);
        OrderInfoMap orderInfoMap = initOrderInfoMap(orderList);
        orderList.stream().forEach(order -> {
            //保险公司
            order.setInsuranceCompanyName(orderInfoMap.getInsuranceCompanyMap().getOrDefault(order.getFkInsuranceCompanyId(), new InsuranceCompany()).getName());
            //保险产品类型
            order.setProductTypeName(orderInfoMap.getProductTypeMap().getOrDefault(order.getFkProductTypeId(), new ProductType()).getTypeName());
            order.setProductTypeKey(orderInfoMap.getProductTypeMap().getOrDefault(order.getFkProductTypeId(), new ProductType()).getTypeKey());
            //代理信息
            order.setAgentName(orderInfoMap.getAgentMap().getOrDefault(order.getFkAgentId(), new AgentEntity()).getName());
            order.setAgentNum(orderInfoMap.getAgentMap().getOrDefault(order.getFkAgentId(), new AgentEntity()).getNum());
            //分公司信息
            order.setCompanyName(orderInfoMap.getCompanyMap().getOrDefault(order.getFkCompanyId(), new CompanyVo()).getName());
            order.setCompanyNum(orderInfoMap.getCompanyMap().getOrDefault(order.getFkCompanyId(), new CompanyVo()).getNum());
            //创建人名称
            order.setGmtCreateName(orderInfoMap.getCreatorMap().getOrDefault(order.getGmtCreateUser(), ""));
            order.setShowStatus(getOrderShowStatus(order));
        });
        int totalCount = (int) pages.getTotal();
        Integer showCount = page.getShowCount();
        page.setTotalPage(page.getShowCount() != null && showCount > 0 ? totalCount % showCount == 0 ? totalCount / showCount : totalCount / showCount + 1 : 0);
        page.setTotalResult(totalCount);
        return orderList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrder(UpdateOrderDto orderDto) {
        InsuranceOrder order = insuranceOrderMapper.selectById(orderDto.getId());
        if (Objects.isNull(order)) {
            log.error("订单不存在");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "search_result_null", "没有找到结果"));
        }
        BeanCopyUtils.copyProperties(orderDto, order);
        order.setGmtModified(new Date());
        order.setGmtModifiedUser(SecureUtil.getLoginId());
        insuranceOrderMapper.updateById(order);
        //todo 变更订单后的其他逻辑
    }

    @Override
    public OrderDetailVo getOrderInfo(Long orderId) {
        return insuranceOrderMapper.selectOrderDetailById(orderId);
    }

    @Override
    public List<CreditCardOrderVo> creditCardOrderList(CreditCardOrderListDto params, Page page) {
        IPage<CreditCardOrderVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<CreditCardOrderVo> orderList = insuranceOrderMapper.selectCreditCardOrderPage(pages, params);
        //填充订单状态展示
        orderList.stream().forEach(order -> {
            OrderDetailVo vo = new OrderDetailVo();
            BeanCopyUtils.copyProperties(order, vo);
            order.setShowStatus(getOrderShowStatus(vo));
        });
        int totalCount = (int) pages.getTotal();
        Integer showCount = page.getShowCount();
        page.setTotalPage(page.getShowCount() != null && showCount > 0 ? totalCount % showCount == 0 ? totalCount / showCount : totalCount / showCount + 1 : 0);
        page.setTotalResult(totalCount);
        return orderList;
    }

    private OrderInfoMap initOrderInfoMap(List<OrderDetailVo> orderList) {
        OrderInfoMap orderInfoMap = new OrderInfoMap();
        List<Long> insuranceCompanyIds = orderList.stream().map(OrderDetailVo::getFkInsuranceCompanyId).distinct().collect(Collectors.toList());
        List<Long> productTypeIds = orderList.stream().map(OrderDetailVo::getFkProductTypeId).distinct().collect(Collectors.toList());
        List<Long> agentIds = orderList.stream().map(OrderDetailVo::getFkAgentId).distinct().collect(Collectors.toList());
        List<Long> companyIds = orderList.stream().map(OrderDetailVo::getFkCompanyId).distinct().collect(Collectors.toList());
        List<String> creatorIds = orderList.stream().map(OrderDetailVo::getGmtCreateUser).distinct().collect(Collectors.toList());
        //保险公司
        if (CollectionUtils.isNotEmpty(insuranceCompanyIds)) {
            Map<Long, InsuranceCompany> map = insuranceCompanyMapper.selectList(new LambdaQueryWrapper<InsuranceCompany>().in(InsuranceCompany::getId, insuranceCompanyIds))
                    .stream()
                    .filter(Objects::nonNull)
                    .filter(c -> c.getId() != null)
                    .collect(Collectors.toMap(
                            InsuranceCompany::getId,
                            Function.identity(),
                            (existing, replacement) -> existing));
            orderInfoMap.setInsuranceCompanyMap(map);
        }
        //保险产品类型
        if (CollectionUtils.isNotEmpty(productTypeIds)) {
            Map<Long, ProductType> map = productTypeMapper.selectList(new LambdaQueryWrapper<ProductType>().in(ProductType::getId, productTypeIds))
                    .stream()
                    .filter(Objects::nonNull)
                    .filter(c -> c.getId() != null)
                    .collect(Collectors.toMap(
                            ProductType::getId,
                            Function.identity(),
                            (existing, replacement) -> existing));
            orderInfoMap.setProductTypeMap(map);
        }
        //代理信息
        if (CollectionUtils.isNotEmpty(agentIds)) {
            Map<Long, AgentEntity> map = partnerCenterMapper.selectAgentByIds(agentIds)
                    .stream()
                    .filter(Objects::nonNull)
                    .filter(c -> c.getId() != null)
                    .collect(Collectors.toMap(
                            AgentEntity::getId,
                            Function.identity(),
                            (existing, replacement) -> existing));
            orderInfoMap.setAgentMap(map);
        }
        //分公司信息
        if (CollectionUtils.isNotEmpty(companyIds)) {
            Map<Long, CompanyVo> map = partnerCenterMapper.selectCompanyByIds(companyIds)
                    .stream()
                    .filter(Objects::nonNull)
                    .filter(c -> c.getId() != null)
                    .collect(Collectors.toMap(
                            CompanyVo::getId,
                            Function.identity(),
                            (existing, replacement) -> existing));
            orderInfoMap.setCompanyMap(map);
        }
        //创建人名称
        if (CollectionUtils.isNotEmpty(creatorIds)) {
            Map<Long, String> map = partnerCenterMapper.selectPartnerUserNameByIds(agentIds);
            orderInfoMap.setCreatorMap(map);
        }
        return orderInfoMap;
    }

    private Integer getOrderShowStatus(OrderDetailVo order) {
        //订单状态(展示以此字段为准):-2:下单失败;1:下单中;2:已完成待生效;3:生效中;4:已失效;5:已退款(退保);6:已取消;
        if (Objects.isNull(order) || Objects.isNull(order.getOrderStatus())) {
            return null;
        }
        if (Arrays.asList(OrderStatusEnum.PENDING.getCode(), OrderStatusEnum.PROGRESSING.getCode()).contains(order.getOrderStatus())) {
            return OrderStatusEnum.PROGRESSING.getCode();
        }
        if (order.getOrderStatus().equals(OrderStatusEnum.SUCCESS.getCode())) {
            LocalDate today = LocalDate.now();
            LocalDate start = toLocalDate(order.getInsuranceStartTime());
            LocalDate end = toLocalDate(order.getInsuranceEndTime());
            return today.isBefore(start) ? 2 :
                    today.isAfter(end) ? 4 : 3;
        }
        return order.getOrderStatus();
    }

    public static LocalDate toLocalDate(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

}
