package com.get.salecenter.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


@Data
public class AppUserDto extends BaseEntity {
    /**
     * 来自平台类型：get_mso
     */
    @ApiModelProperty(value = "来自平台类型：get_mso")
    private String fkPlatformType;

    /**
     * 登陆用户Id
     */

    @ApiModelProperty(value = "登录用户id")
    private String loginId;

    /**
     * 登陆用户密码
     */

    @ApiModelProperty(value = "登陆用户密码", required = true)
    private String loginPs;

    /**
     * 会员编号
     */
    @ApiModelProperty(value = "会员编号")
    private String num;

    /**
     * 姓名（中文）
     */
    @ApiModelProperty(value = "真实姓名（中文）")
    private String name;

    /**
     * 姓名（英文）
     */
    @ApiModelProperty(value = "姓名（英文）")
    private String nameEn;
    /**
     * 微信昵称
     */
    @ApiModelProperty(value = "微信昵称")
    private String wechatNickname;
    /**
     * 微信头像URL
     */
    @ApiModelProperty(value = "微信头像URL")
    private String wechatIconUrl;
    /**
     * 微信openid
     */
    @ApiModelProperty(value = "微信openid")
    private String wechatOpenid;
    /**
     * 姓（拼音）
     */
    @ApiModelProperty(value = "姓（拼音）")
    private String familyNamePy;

    /**
     * 名（拼音）
     */
    @ApiModelProperty(value = "名（拼音）")
    private String firstNamePy;

    /**
     * 昵称
     */
    @ApiModelProperty(value = "昵称")
    private String nickname;

    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男")
    private Integer gender;

    /**
     * 生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "生日,格式：yyyy-MM-dd")
    private Date birthday;

    /**
     * 公司名
     */
    @ApiModelProperty(value = "公司名")
    private String company;
    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    private String department;
    /**
     * 职位
     */
    @ApiModelProperty(value = "职位")
    private String title;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    private String identityCard;

    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话", required = true)
    private String mobile;

    /**
     * 移动电话
     */
    @ApiModelProperty(value = "手机区号")
    private String mobileAreaCode;

    /**
     * 电话区号
     */
    @ApiModelProperty(value = "电话区号")
    private String telAreaCode;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String tel;

    /**
     * Email
     */
    @ApiModelProperty(value = "Email")
    private String email;

    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    private String contactAddress;

    /**
     * qq
     */
    @ApiModelProperty(value = "qq")
    private String qq;
    /**
     * 微信号
     */
    @ApiModelProperty(value = "微信号")
    private String wechat;
    /**
     * whatsapp
     */
    @ApiModelProperty(value = "whatsapp")
    private String whatsapp;

    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;

    /**
     * 州省Id
     */
    @ApiModelProperty(value = "州省Id")
    private Long fkAreaStateId;

    /**
     * 城市Id
     */
    @ApiModelProperty(value = "城市Id")
    private Long fkAreaCityId;

    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    private String zipCode;


    @ApiModelProperty(value = "供应商ID")
    private Long agentId;

    /**
     * 邀请码
     */
    @ApiModelProperty(value = "邀请码")
    private String invitationCode;

    @ApiModelProperty(value = "聯係人id")
    private Long contactPersonId;

    /**
     * 语言枚举code：zh-hk/en-us
     */
    @ApiModelProperty(value = "语言枚举code：zh-hk/en-us")
    private String bmsLanguageCode;

    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

    @ApiModelProperty("学生来源")
    private String stuSource;

    @ApiModelProperty(value = "是否强制修改密码，false否/true是")
    private Boolean isModifiedPs;

    /**
     * 联系人类型Key，多值逗号隔开
     */
    @ApiModelProperty(value = "联系人类型Key，多值逗号隔开")
    private String fkContactPersonTypeKey;
}
