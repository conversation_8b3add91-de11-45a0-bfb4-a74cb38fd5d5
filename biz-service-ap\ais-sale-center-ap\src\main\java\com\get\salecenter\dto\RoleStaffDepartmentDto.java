package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class RoleStaffDepartmentDto {

    @ApiModelProperty("角色key")
    @NotEmpty(message = "角色key不能为空")
    private String projectRoleKey;

    @ApiModelProperty("公司ids")
    @NotEmpty(message = "公司ids不能为空")
    private List<Long> companyIds;

}
