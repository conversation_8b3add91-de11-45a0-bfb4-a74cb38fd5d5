<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.partnercenter.mapper.MStudentOfferItemMapper">

    <resultMap id="BaseResultMap" type="com.get.partnercenter.entity.MStudentOfferItemEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkParentStudentOfferItemId" column="fk_parent_student_offer_item_id" jdbcType="BIGINT"/>
            <result property="fkStudentId" column="fk_student_id" jdbcType="BIGINT"/>
            <result property="fkAgentId" column="fk_agent_id" jdbcType="BIGINT"/>
            <result property="fkStaffId" column="fk_staff_id" jdbcType="BIGINT"/>
            <result property="fkStudentOfferId" column="fk_student_offer_id" jdbcType="BIGINT"/>
            <result property="fkAreaCountryId" column="fk_area_country_id" jdbcType="BIGINT"/>
            <result property="fkInstitutionId" column="fk_institution_id" jdbcType="BIGINT"/>
            <result property="fkInstitutionFacultyId" column="fk_institution_faculty_id" jdbcType="BIGINT"/>
            <result property="fkInstitutionZoneId" column="fk_institution_zone_id" jdbcType="BIGINT"/>
            <result property="fkInstitutionCourseId" column="fk_institution_course_id" jdbcType="BIGINT"/>
            <result property="fkInstitutionCourseCustomId" column="fk_institution_course_custom_id" jdbcType="BIGINT"/>
            <result property="fkInstitutionProviderId" column="fk_institution_provider_id" jdbcType="BIGINT"/>
            <result property="fkInstitutionChannelId" column="fk_institution_channel_id" jdbcType="BIGINT"/>
            <result property="fkStudentOfferItemStepId" column="fk_student_offer_item_step_id" jdbcType="BIGINT"/>
            <result property="studentOfferItemStepTime" column="student_offer_item_step_time" jdbcType="TIMESTAMP"/>
            <result property="fkIssueRpaOrderId" column="fk_issue_rpa_order_id" jdbcType="BIGINT"/>
            <result property="fkInstitutionCourseMajorLevelIds" column="fk_institution_course_major_level_ids" jdbcType="VARCHAR"/>
            <result property="fkInstitutionCourseTypeIds" column="fk_institution_course_type_ids" jdbcType="VARCHAR"/>
            <result property="fkInstitutionCourseTypeGroupIds" column="fk_institution_course_type_group_ids" jdbcType="VARCHAR"/>
            <result property="num" column="num" jdbcType="VARCHAR"/>
            <result property="studentId" column="student_id" jdbcType="VARCHAR"/>
            <result property="durationType" column="duration_type" jdbcType="INTEGER"/>
            <result property="duration" column="duration" jdbcType="DECIMAL"/>
            <result property="openingTime" column="opening_time" jdbcType="DATE"/>
            <result property="deferOpeningTime" column="defer_opening_time" jdbcType="DATE"/>
            <result property="closingTime" column="closing_time" jdbcType="DATE"/>
            <result property="fkCurrencyTypeNum" column="fk_currency_type_num" jdbcType="VARCHAR"/>
            <result property="tuitionAmount" column="tuition_amount" jdbcType="DECIMAL"/>
            <result property="tuitionStatus" column="tuition_status" jdbcType="INTEGER"/>
            <result property="fkAppFeeCurrencyTypeNum" column="fk_app_fee_currency_type_num" jdbcType="VARCHAR"/>
            <result property="appFeeAmount" column="app_fee_amount" jdbcType="DECIMAL"/>
            <result property="appFeeStatus" column="app_fee_status" jdbcType="INTEGER"/>
            <result property="courseWebsite" column="course_website" jdbcType="VARCHAR"/>
            <result property="courseOpenTime" column="course_open_time" jdbcType="DATE"/>
            <result property="isMain" column="is_main" jdbcType="BIT"/>
            <result property="isFollow" column="is_follow" jdbcType="BIT"/>
            <result property="isFollowHidden" column="is_follow_hidden" jdbcType="BIT"/>
            <result property="isCreditExemption" column="is_credit_exemption" jdbcType="BIT"/>
            <result property="isAddApp" column="is_add_app" jdbcType="BIT"/>
            <result property="isStepFollow" column="is_step_follow" jdbcType="BIT"/>
            <result property="isNoCommission" column="is_no_commission" jdbcType="BIT"/>
            <result property="newAppStatus" column="new_app_status" jdbcType="INTEGER"/>
            <result property="newAppOptTime" column="new_app_opt_time" jdbcType="TIMESTAMP"/>
            <result property="appMethod" column="app_method" jdbcType="INTEGER"/>
            <result property="appRemark" column="app_remark" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="conditionType" column="condition_type" jdbcType="VARCHAR"/>
            <result property="fkEnrolFailureReasonId" column="fk_enrol_failure_reason_id" jdbcType="BIGINT"/>
            <result property="otherFailureReason" column="other_failure_reason" jdbcType="VARCHAR"/>
            <result property="isDeferEntrance" column="is_defer_entrance" jdbcType="BIT"/>
            <result property="learningMode" column="learning_mode" jdbcType="INTEGER"/>
            <result property="submitAppTime" column="submit_app_time" jdbcType="DATE"/>
            <result property="depositTime" column="deposit_time" jdbcType="DATE"/>
            <result property="depositDeadline" column="deposit_deadline" jdbcType="DATE"/>
            <result property="acceptOfferDeadline" column="accept_offer_deadline" jdbcType="DATE"/>
            <result property="tuitionTime" column="tuition_time" jdbcType="DATE"/>
            <result property="insuranceBuyMethod" column="insurance_buy_method" jdbcType="INTEGER"/>
            <result property="depositPaymentMethod" column="deposit_payment_method" jdbcType="INTEGER"/>
            <result property="tuitionPaymentMethod" column="tuition_payment_method" jdbcType="INTEGER"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="rpaOptTime" column="rpa_opt_time" jdbcType="TIMESTAMP"/>
            <result property="rpaFinishTime" column="rpa_finish_time" jdbcType="TIMESTAMP"/>
            <result property="rpaRemark" column="rpa_remark" jdbcType="VARCHAR"/>
            <result property="statusRpa" column="status_rpa" jdbcType="VARCHAR"/>
            <result property="fkPlatformType" column="fk_platform_type" jdbcType="VARCHAR"/>
            <result property="oldInstitutionName" column="old_institution_name" jdbcType="VARCHAR"/>
            <result property="oldInstitutionFullName" column="old_institution_full_name" jdbcType="VARCHAR"/>
            <result property="oldCourseCustomName" column="old_course_custom_name" jdbcType="VARCHAR"/>
            <result property="oldCourseMajorLevelName" column="old_course_major_level_name" jdbcType="VARCHAR"/>
            <result property="oldCourseTypeName" column="old_course_type_name" jdbcType="VARCHAR"/>
            <result property="issueCourseInputFlag" column="issue_course_input_flag" jdbcType="VARCHAR"/>
            <result property="idGeaFinance" column="id_gea_finance" jdbcType="VARCHAR"/>
            <result property="idIssue" column="id_issue" jdbcType="VARCHAR"/>
            <result property="idGea" column="id_gea" jdbcType="VARCHAR"/>
            <result property="idIae" column="id_iae" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>


        <select id="selectByOfferArr" resultType="com.get.partnercenter.entity.MStudentOfferEntity">
                SELECT mStudentOffer.* FROM ais_sale_center.m_student_offer mStudentOffer
                LEFT JOIN  ais_sale_center.m_student_offer_item mStudentOfferItem ON mStudentOffer.id=mStudentOfferItem.fk_student_offer_id
                WHERE mStudentOffer.fk_student_id=#{fkStudentId} AND mStudentOfferItem.fk_student_offer_id IS NULL


        </select>


</mapper>
