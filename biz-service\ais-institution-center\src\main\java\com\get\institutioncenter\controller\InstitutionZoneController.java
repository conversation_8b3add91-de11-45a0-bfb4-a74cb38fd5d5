package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.vo.InstitutionZoneVo;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.service.IInstitutionZoneService;
import com.get.institutioncenter.service.IMediaAndAttachedService;
import com.get.institutioncenter.dto.InstitutionZoneDto;
import com.get.institutioncenter.dto.query.InstitutionZoneQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/8/4
 * @TIME: 16:37
 * @Description:
 **/
@Api(tags = "校区管理")
@RestController
@RequestMapping("/institution/institutionZone")
public class InstitutionZoneController {
    @Resource
    private IInstitutionZoneService institutionZoneService;
    @Resource
    private IMediaAndAttachedService mediaAndAttachedService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/校区管理/校区详情")
    @GetMapping("/{id}")
    public ResponseBo<InstitutionZoneVo> detail(@PathVariable("id") Long id) {
        InstitutionZoneVo data = institutionZoneService.findInstitutionZoneById(id);
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", data);
        return responseBo;
    }

    /**
     * 新增信息
     *
     * @param institutionZoneDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/校区管理/新增校区")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(InstitutionZoneDto.Add.class) InstitutionZoneDto institutionZoneDto) {
        return SaveResponseBo.ok(institutionZoneService.addInstitutionZone(institutionZoneDto));
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/校区管理/删除校区")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        institutionZoneService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param institutionZoneDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/校区管理/更新校区")
    @PostMapping("update")
    public ResponseBo<InstitutionZoneVo> update(@RequestBody @Validated(InstitutionZoneDto.Update.class) InstitutionZoneDto institutionZoneDto) {
        return UpdateResponseBo.ok(institutionZoneService.updateInstitutionZone(institutionZoneDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校校区管理/查询学校校区")
    @PostMapping("datas")
    public ResponseBo<InstitutionZoneVo> datas(@RequestBody SearchBean<InstitutionZoneQueryDto> page) {
        List<InstitutionZoneVo> datas = institutionZoneService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 上移下移
     *
     * @param mediaAttachedVos
     * @return
     * @
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/学校校区管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<MediaAndAttachedDto> mediaAttachedVos) {
        mediaAndAttachedService.movingOrder(mediaAttachedVos);
        return ResponseBo.ok();
    }

    /**
     * @param mediaAttachedVos
     * @return
     * @
     */

    @ApiOperation(value = "保存文件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/学校校区管理/保存附件资料")
    @PostMapping("addMediaAndAttached")
    public ResponseBo addMediaAndAttached(@RequestBody @Validated(MediaAndAttachedDto.Add.class) List<MediaAndAttachedDto> mediaAttachedVos) {
        return new ListResponseBo<>(institutionZoneService.addInstitutionZoneMedia(mediaAttachedVos));
    }

    /**
     * 学院下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "校区下拉框数据", notes = "")
    @GetMapping("getInstitutionZoneList/{id}")
    public ResponseBo<InstitutionZoneVo> getInstitutionZoneList(@PathVariable("id") Long id) {
        List<InstitutionZoneVo> datas = institutionZoneService.getByfkInstitutionId(id);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.InstitutionZoneVo>
     * @Description :多个学校所有的校区下拉框数据
     * @Param [institutionIdList]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "多个学校所有的校区下拉框数据", notes = "")
    @PostMapping("getInstitutionZoneSelectByInstitutionIdList")
    public ResponseBo<InstitutionZoneVo> getInstitutionZoneSelectByInstitutionIdList(@RequestBody List<Long> institutionIdList) {
        List<InstitutionZoneVo> datas = institutionZoneService.getInstitutionZoneSelectByInstitutionIdList(institutionIdList);
        return new ListResponseBo<>(datas);
    }

    /**
     * 学院附件类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "校区附件类型下拉框数据", notes = "")
    @PostMapping("findMediaAndAttachedType")
    public ResponseBo findMediaAndAttachedType() {
        List<Map<String, Object>> datas = institutionZoneService.findMediaAndAttachedType();
        return new ListResponseBo<>(datas);
    }

    /**
     * 查询学院附件
     *
     * @param voSearchBean
     * @return
     * @
     */
    @ApiOperation(value = "查询校区附件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/校区管理/查询校区附件")
    @PostMapping("getInstitutionZoneMedia")
    public ResponseBo<MediaAndAttachedVo> getInstitutionZoneMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> institutionMedia = institutionZoneService.getInstitutionZoneMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(institutionMedia, page);
    }
}
