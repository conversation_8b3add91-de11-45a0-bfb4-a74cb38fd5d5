package com.get.institutioncenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.vo.ContractFormulaCommissionVo;
import com.get.institutioncenter.entity.ContractFormulaCommission;
import com.get.institutioncenter.dto.ContractFormulaCommissionDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/23 17:12
 * @verison: 1.0
 * @description:
 */
public interface IContractFormulaCommissionService extends BaseService<ContractFormulaCommission> {
    /**
     * @return java.lang.Long
     * @Description :新增
     * @Param [contractFormulaCommissionDto]
     * <AUTHOR>
     */
    Long addContractFormulaCommission(ContractFormulaCommissionDto contractFormulaCommissionDto);

    /**
     * @return void
     * @Description :根据contractFormulaId删除
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    void deleteByFkid(Long contractFormulaId);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.ContractFormulaCommissionVo>
     * @Description :获取佣金配置对象dtos
     * @Param [id]
     * <AUTHOR>
     */
    List<ContractFormulaCommissionVo> getContractFormulaCommissionDtoByFkid(Long id);
}
