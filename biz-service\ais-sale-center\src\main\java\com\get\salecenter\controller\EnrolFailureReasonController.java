package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.salecenter.vo.EnrolFailureReasonVo;
import com.get.salecenter.service.EnrolFailureReasonService;
import com.get.salecenter.dto.EnrolFailureReasonDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2021/6/16
 * @TIME: 14:49
 * @Description:入学失败原因管理
 **/
@Api(tags = "入学失败原因管理")
@RestController
@RequestMapping("sale/enrolFailureReason")
public class EnrolFailureReasonController {
    @Resource
    private EnrolFailureReasonService enrolFailureReasonService;

    /**
     * @return com.get.common.result.ListResponseBo<com.get.salecenter.vo.EnrolFailureReasonVo>
     * @Description:列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/入学失败原因管理/查询")
    @PostMapping("datas")
    public ListResponseBo<EnrolFailureReasonVo> datas(@RequestBody SearchBean<EnrolFailureReasonDto> page) {
        List<EnrolFailureReasonVo> datas = enrolFailureReasonService.getEnrolFailureReasonDtos(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description:新增信息
     * @Param [enrolFailureReasonDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/入学失败原因管理/新增原因")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(EnrolFailureReasonDto.Add.class) ValidList<EnrolFailureReasonDto> enrolFailureReasonDto) {
        enrolFailureReasonService.addEnrolFailureReason(enrolFailureReasonDto);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EnrolFailureReasonVo>
     * @Description:修改信息
     * @Param [enrolFailureReasonDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/入学失败原因管理/更新原因")
    @PostMapping("update")
    public ResponseBo<EnrolFailureReasonVo> update(@RequestBody  @Validated(EnrolFailureReasonDto.Update.class) EnrolFailureReasonDto enrolFailureReasonDto) {
        return UpdateResponseBo.ok(enrolFailureReasonService.updateEnrolFailureReason(enrolFailureReasonDto));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description:删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/入学失败原因管理/删除原因")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        enrolFailureReasonService.deleteEnrolFailureReason(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description:上移下移
     * @Param [enrolFailureReasonDtoList]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/入学失败原因管理/排序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<EnrolFailureReasonDto> enrolFailureReasonDtoList) {
        enrolFailureReasonService.movingOrder(enrolFailureReasonDtoList);
        return ResponseBo.ok();
    }

}
