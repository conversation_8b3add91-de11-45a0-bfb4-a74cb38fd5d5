package com.get.aismail.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("u_area_city")
public class AreaCity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 州省Id
     */
    @ApiModelProperty(value = "州省Id")
    @Column(name = "fk_area_state_id")
    private Long fkAreaStateId;
    /**
     * 城市编号
     */
    @ApiModelProperty(value = "城市编号")
    @Column(name = "num")
    private String num;
    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称")
    @Column(name = "name")
    private String name;
    /**
     * 城市中文名称
     */
    @ApiModelProperty(value = "城市中文名称")
    @Column(name = "name_chn")
    private String nameChn;
    /**
     * 人口
     */
    @ApiModelProperty(value = "人口")
    @Column(name = "population")
    private String population;
    /**
     * 面积
     */
    @ApiModelProperty(value = "面积")
    @Column(name = "area")
    private String area;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @Column(name = "public_level")
    private String publicLevel;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}