package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2024/1/17
 * @TIME: 9:55
 * @Description:
 **/
@Data
public class InstitutionEnrolledConversionRateDto {
    @ApiModelProperty(value = "统计方式:1-课程等级，2-课程类型")
    @NotNull(message = "统计方式不能为空" )
    private Integer queryType;

    @ApiModelProperty(value = "公司Ids")
    private List<Long> fkCompanyIds;

    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "州省Id")
    private List<Long> fkAreaStateIds;

    @ApiModelProperty(value = "入学年份")
    private List<Integer> years;

    @ApiModelProperty(value = "学校Ids")
    private List<Long> fkInstitutionIds;

    /**
     * 员工以及旗下员工所创建的代理ids
     */
    @ApiModelProperty(value = "员工以及旗下员工所创建的代理ids")
    private List<Long> staffFollowerIds;

    /**
     * 国家ID列表,员工（登录人）业务国家
     */
    @ApiModelProperty(value = "国家ID列表,员工（登录人）业务国家")
    private List<Long> fkAreaCountryIdList;

}
