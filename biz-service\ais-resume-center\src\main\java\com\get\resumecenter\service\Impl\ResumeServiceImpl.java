package com.get.resumecenter.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.resumecenter.dao.ResumeMapper;
import com.get.resumecenter.dto.MediaAndAttachedDto;
import com.get.resumecenter.vo.*;
import com.get.resumecenter.vo.MediaAndAttachedVo;
import com.get.resumecenter.entity.Resume;
import com.get.resumecenter.service.IDeleteService;
import com.get.resumecenter.service.IMediaAndAttachedService;
import com.get.resumecenter.service.IResumeAttachmentService;
import com.get.resumecenter.service.IResumeCertificateService;
import com.get.resumecenter.service.IResumeEducationService;
import com.get.resumecenter.service.IResumeIntentionService;
import com.get.resumecenter.service.IResumeOtherService;
import com.get.resumecenter.service.IResumeService;
import com.get.resumecenter.service.IResumeSkillService;
import com.get.resumecenter.service.IResumeTrainingService;
import com.get.resumecenter.service.IResumeTypeService;
import com.get.resumecenter.service.IResumeWorkService;
import com.get.resumecenter.dto.ResumeDto;
import com.get.resumecenter.dto.query.ResumeQueryDto;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/7/31
 * @TIME: 16:06
 * @Description:
 **/
@Service
public class ResumeServiceImpl implements IResumeService {
    @Resource
    private ResumeMapper resumeMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private IResumeIntentionService intentionService;
    @Resource
    private IResumeWorkService workService;
    @Resource
    private IResumeEducationService educationService;
    @Resource
    private IResumeSkillService skillService;
    @Resource
    private IResumeCertificateService certificateService;
    @Resource
    private IResumeTrainingService trainingService;
    @Resource
    private IResumeOtherService otherService;
    @Resource
    private IResumeTypeService resumeTypeService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private IResumeAttachmentService resumeAttachmentService;

    @Override
    public List<ResumeVo> getResumeListDtos(ResumeQueryDto resumeVo, Page page) {
//        Example example = new Example(Resume.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<Resume> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isEmpty(resumeVo.getFkCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_id_null"));
        } else {
            if (!SecureUtil.validateCompany(resumeVo.getFkCompanyId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
            lambdaQueryWrapper.eq(Resume::getFkCompanyId, resumeVo.getFkCompanyId());
        }
        if (GeneralTool.isNotEmpty(resumeVo.getFkResumeTypeId())) {
            lambdaQueryWrapper.eq(Resume::getFkResumeTypeId, resumeVo.getFkResumeTypeId());
        }
        if (GeneralTool.isNotEmpty(resumeVo.getName())) {
            lambdaQueryWrapper.eq(Resume::getName, resumeVo.getName());
        }
        if (GeneralTool.isNotEmpty(resumeVo.getMobile())) {
            lambdaQueryWrapper.like(Resume::getMobile, resumeVo.getMobile());
        }
        if (GeneralTool.isNotEmpty(resumeVo.getResumeGuid())) {
            lambdaQueryWrapper.like(Resume::getResumeGuid, resumeVo.getResumeGuid());
        }
        if (GeneralTool.isNotEmpty(resumeVo.getInstitution())) {
            //毕业的学校
            List<Long> resumeIds = educationService.getResumeIdByInstitution(resumeVo.getInstitution());
            if (GeneralTool.isEmpty(resumeIds)) {
                resumeIds = new ArrayList<>();
                resumeIds.add(0L);
            }
            lambdaQueryWrapper.in(Resume::getId, resumeIds);
        }
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        IPage<Resume> iPage = resumeMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<Resume> resumes = iPage.getRecords();
        page.setAll((int) iPage.getTotal());
        List<ResumeVo> collect = resumes.stream().map(resume -> BeanCopyUtils.objClone(resume, ResumeVo::new)).collect(Collectors.toList());
        Map<String, String> companyMap = getCompanyMap();
        for (ResumeVo resumeDto1 : collect) {
            setName(resumeDto1, companyMap);
        }

        return collect;
    }

    @Override
    public ResumeVo getResumeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("resume_id_null"));
        }
        Resume resume = resumeMapper.selectById(id);
        if (GeneralTool.isEmpty(resume)) {
            return null;
        }
        ResumeVo resumeVo = BeanCopyUtils.objClone(resume, ResumeVo::new);
        //设置头像属性链接
        setResumeLink(resumeVo);

        //查询职业意向
        ResumeIntentionVo resumeIntentionVo = intentionService.getResumeIntentionDto(id);
        resumeVo.setResumeIntentionDto(resumeIntentionVo);
        //查询工作经验
        List<ResumeWorkVo> resumeWorkListDto = workService.getResumeWorkListDto(id);
        resumeVo.setResumeWorkDtos(resumeWorkListDto);
        //查询教育经历
        List<ResumeEducationVo> resumeEducationListDto = educationService.getResumeEducationListDto(id);
        resumeVo.setResumeEducationDtos(resumeEducationListDto);
        //查询简历技能
        List<ResumeSkillVo> resumeSkillListDto = skillService.getResumeSkillListDto(id);
        resumeVo.setResumeSkillDtos(resumeSkillListDto);
        //查询简历证书
        List<ResumeCertificateVo> resumeCertificateListDto = certificateService.getResumeCertificateListDto(id);
        resumeVo.setResumeCertificateDtos(resumeCertificateListDto);
        //查询简历培训经历
        List<ResumeTrainingVo> resumeTrainingListDto = trainingService.getResumeTrainingListDto(id);
        resumeVo.setResumeTrainingDtos(resumeTrainingListDto);
        //查询简历附加信息
        List<ResumeOtherVo> resumeOtherListDto = otherService.getResumeOtherListDto(id);
        resumeVo.setResumeOtherDtos(resumeOtherListDto);
        //查询简历附件
        List<ResumeAttachmentVo> resumeAttachment = resumeAttachmentService.getResumeAttachment(id);
        resumeVo.setResumeAttachmentDtos(resumeAttachment);

        if (GeneralTool.isNotEmpty(resumeVo.getFkResumeTypeId())) {
            ResumeTypeVo resumeType = resumeTypeService.findResumeTypeById(resumeVo.getFkResumeTypeId());
            if (GeneralTool.isNotEmpty(resumeType)) {
                resumeVo.setFkResumeTypeName(resumeType.getTypeName());
            }
        }
        return resumeVo;
    }

    @Override
    public List<MediaAndAttachedVo> saveResumeMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
//        if (!StaffContext.getStaff().getIsAdmin()) {
//            if (!StaffContext.getStaff().getIsModifiedResume()) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
//            }
//        }
        if (!GetAuthInfo.isAdmin()) {
            if (!SecureUtil.getIsModifiedResume()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
            }
        }
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();

        if (mediaAttachedVos.get(0).getTypeKey().equals(FileTypeEnum.RESUME_ICON.key)) {
            if (GeneralTool.isNotEmpty(mediaAttachedVos.get(0).getFkTableId())) {
                MediaAndAttachedDto mediaAndAttached = new MediaAndAttachedDto();
                mediaAndAttached.setFkTableName(mediaAttachedVos.get(0).getFkTableName());
                mediaAndAttached.setFkTableId(mediaAttachedVos.get(0).getFkTableId());
                mediaAndAttached.setTypeKey(mediaAttachedVos.get(0).getTypeKey());
                attachedService.delete(mediaAndAttached);
            }
        }
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.RESUME_RESUME.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;

    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteResume(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Resume resume = resumeMapper.selectById(id);
        if (GeneralTool.isEmpty(resume)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        String guid = resume.getResumeGuid();
        int delete = resumeMapper.deleteById(id);
        if (delete > 0) {
            deleteService.deleteValidateResume(id);
            Result<Boolean> result = permissionCenterClient.deleteGuid(guid);//这里注意跨微服务的事务处理
            if (!result.isSuccess()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
            }

        }
    }

    @Override
    public ResumeVo getResumeByGuid(String guid) {
        if (GeneralTool.isEmpty(guid)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("guid_null"));
        }
        Resume resume = resumeMapper.getByGuid(guid);
        Long id = resume.getId();
        ResumeVo resumeVo = BeanCopyUtils.objClone(resume, ResumeVo::new);
        //设置头像属性链接
        setResumeLink(resumeVo);

        //查询职业意向
        ResumeIntentionVo resumeIntentionVo = intentionService.getResumeIntentionDto(id);
        resumeVo.setResumeIntentionDto(resumeIntentionVo);
        //查询工作经验
        List<ResumeWorkVo> resumeWorkListDto = workService.getResumeWorkListDto(id);
        resumeVo.setResumeWorkDtos(resumeWorkListDto);
        //查询教育经历
        List<ResumeEducationVo> resumeEducationListDto = educationService.getResumeEducationListDto(id);
        resumeVo.setResumeEducationDtos(resumeEducationListDto);
        //查询简历技能
        List<ResumeSkillVo> resumeSkillListDto = skillService.getResumeSkillListDto(id);
        resumeVo.setResumeSkillDtos(resumeSkillListDto);
        //查询简历证书
        List<ResumeCertificateVo> resumeCertificateListDto = certificateService.getResumeCertificateListDto(id);
        resumeVo.setResumeCertificateDtos(resumeCertificateListDto);
        //查询简历培训经历
        List<ResumeTrainingVo> resumeTrainingListDto = trainingService.getResumeTrainingListDto(id);
        resumeVo.setResumeTrainingDtos(resumeTrainingListDto);
        //查询简历附加信息
        List<ResumeOtherVo> resumeOtherListDto = otherService.getResumeOtherListDto(id);
        resumeVo.setResumeOtherDtos(resumeOtherListDto);
        //查询简历附件
        List<ResumeAttachmentVo> resumeAttachment = resumeAttachmentService.getResumeAttachment(id);
        resumeVo.setResumeAttachmentDtos(resumeAttachment);

        if (GeneralTool.isNotEmpty(resumeVo.getFkResumeTypeId())) {
            ResumeTypeVo resumeType = resumeTypeService.findResumeTypeById(resumeVo.getFkResumeTypeId());
            if (GeneralTool.isNotEmpty(resumeType)) {
                resumeVo.setFkResumeTypeName(resumeType.getTypeName());
            }
        }
        return resumeVo;
    }

    @Override
    public Long addResume(ResumeDto resumeDto) {
//        if (!StaffContext.getStaff().getIsAdmin()) {
//            if (!StaffContext.getStaff().getIsModifiedResume()) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
//            }
//        }
        if (!GetAuthInfo.isAdmin()) {
            if (!SecureUtil.getIsModifiedResume()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
            }
        }
        if (GeneralTool.isEmpty(resumeDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        Resume resume = BeanCopyUtils.objClone(resumeDto, Resume::new);
        resume.setResumeGuid(UUID.randomUUID().toString().replaceAll("-", ""));
        utilService.updateUserInfoToEntity(resume);
        int returnId = resumeMapper.insertSelective(resume);
        if (returnId <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        Long fkMediaId = resumeDto.getFkMediaId();
        if (GeneralTool.isNotEmpty(fkMediaId)) {
            attachedService.updateTableId(fkMediaId, resume.getId());
        }
        permissionCenterClient.saveResumeGuid(resume.getResumeGuid());
        return resume.getId();
    }

    @Override
    public ResumeVo updateResume(ResumeDto resumeDto) {
//        if (!StaffContext.getStaff().getIsAdmin()) {
//            if (!StaffContext.getStaff().getIsModifiedResume()) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
//            }
//        }
        if (!GetAuthInfo.isAdmin()) {
            if (!SecureUtil.getIsModifiedResume()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
            }
        }
        if (GeneralTool.isEmpty(resumeDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Resume resume = BeanCopyUtils.objClone(resumeDto, Resume::new);
        utilService.updateUserInfoToEntity(resume);
        resumeMapper.updateById(resume);
        return getResumeById(resume.getId());
    }

    /**
     * @return void
     * @Description: 设置简历的附件属性
     * @Param [resumeVo]
     * <AUTHOR>
     */
    private void setResumeLink(ResumeVo resumeVo) {
        MediaAndAttachedDto attachedVo = new MediaAndAttachedDto();
        //设置头像链接
        attachedVo.setTypeKey(FileTypeEnum.RESUME_ICON.key);
        attachedVo.setFkTableName(TableEnum.RESUME_RESUME.key);
        attachedVo.setFkTableId(resumeVo.getId());

        List<MediaAndAttachedVo> headIcon = attachedService.getMediaAndAttachedDto(attachedVo);
        if (GeneralTool.isNotEmpty(headIcon)) {
            resumeVo.setHeadImageDto(headIcon.get(headIcon.size()-1));
        }
        //设置公司logo
        if (GeneralTool.isNotEmpty(resumeVo.getFkCompanyId())) {
//            ResponseBo<MediaAndAttachedDto> responseBo = permissionCenterClient.getCompanyIcon(resumeVo.getFkCompanyId());
            Result<com.get.permissioncenter.vo.MediaAndAttachedVo> result = permissionCenterClient.getCompanyIcon(resumeVo.getFkCompanyId());
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                com.get.permissioncenter.vo.MediaAndAttachedVo companyIcon = result.getData();
                MediaAndAttachedVo mediaAndAttachedVo = BeanCopyUtils.objClone(companyIcon, MediaAndAttachedVo::new);
                resumeVo.setCompanyIconDto(mediaAndAttachedVo);
            }
        }
    }

    private void setName(ResumeVo resumeVo, Map<String, String> companyMap) {
        //所属公司
        if (GeneralTool.isNotEmpty(resumeVo.getFkCompanyId())) {
            String companyName = companyMap.get(String.valueOf(resumeVo.getFkCompanyId()));
            resumeVo.setFkCompanyName(companyName);
        }

        //简历类型
        if (GeneralTool.isNotEmpty(resumeVo.getFkResumeTypeId())) {
            ResumeTypeVo resumeType = resumeTypeService.findResumeTypeById(resumeVo.getFkResumeTypeId());
            if (GeneralTool.isNotEmpty(resumeType)) {
                resumeVo.setFkResumeTypeName(resumeType.getTypeName());
            }
        }
        //教育经历
        List<ResumeEducationVo> resumeEducationListDto = educationService.getResumeEducationListDto(resumeVo.getId());
        if (GeneralTool.isNotEmpty(resumeEducationListDto)) {
            List<String> graduationLevel = resumeEducationListDto.stream().map(ResumeEducationVo::getGraduationLevel).collect(Collectors.toList());
            if (GeneralTool.isNotEmpty(graduationLevel)) {
                String institutionNames = StringUtils.join(graduationLevel.toArray(), "/");
                resumeVo.setResumeEducations(institutionNames);
            }
        }
        //工作经历
        List<ResumeWorkVo> resumeWorkListDto = workService.getResumeWorkListDto(resumeVo.getId());
        if (GeneralTool.isNotEmpty(resumeWorkListDto)) {
            List<String> companyName = resumeWorkListDto.stream().map(ResumeWorkVo::getCompany).collect(Collectors.toList());
            if (GeneralTool.isNotEmpty(companyName)) {
                String companyNames = StringUtils.join(companyName.toArray(), "/");
                resumeVo.setResumeWorks(companyNames);
            }
        }
    }


    private Map<String, String> getCompanyMap() {
        //初始为5的map
        Map<String, String> companyMap = new HashMap<>(5);
        Result<List<com.get.permissioncenter.vo.tree.CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
            JsonConfig config = new JsonConfig();
            config.setExcludes(new String[]{"departmentTree", "totalNum"});
            JSONArray jsonArray = JSONArray.fromObject(result.getData(), config);
            List<CompanyTreeVo> companyTreeVos = JSONArray.toList(jsonArray, new CompanyTreeVo(), new JsonConfig());

            if (GeneralTool.isNotEmpty(companyTreeVos)) {
                companyMap = companyTreeVos.stream().collect(Collectors.toMap(CompanyTreeVo::getId, CompanyTreeVo::getShortName));
            }
        }
        return companyMap;
    }
}