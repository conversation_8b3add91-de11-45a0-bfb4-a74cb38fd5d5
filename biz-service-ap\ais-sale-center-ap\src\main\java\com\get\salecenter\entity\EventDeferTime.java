package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("m_event_defer_time")
public class EventDeferTime extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 活动Id
     */
    @ApiModelProperty(value = "活动Id")
    @Column(name = "fk_event_id")
    private Long fkEventId;
    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    @Column(name = "event_time")
    private Date eventTime;
    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    @Column(name = "event_time_end")
    private Date eventTimeEnd;

}