package com.get.financecenter.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ExpenseClaimAgentContentVo extends BaseEntity {

    @ApiModelProperty(value = "内容名称")
    private String name;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

}