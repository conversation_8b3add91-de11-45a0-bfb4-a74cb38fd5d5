package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Byron
 * @create: 2020/7/22 16:01
 * @verison: 1.0
 * @description:
 */
@Data
public class ContactPersonTypeVo extends BaseEntity {

    //============实体类InisContactPersonType================
    private static final long serialVersionUID = 1L;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    @Column(name = "type_name")
    private String typeName;
    /**
     * 类型Key，枚举类型Key
     */
    @ApiModelProperty(value = "类型Key，枚举类型Key")
    @Column(name = "type_key")
    private String typeKey;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}
