package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2020/8/21
 * @TIME: 16:13
 * @Description: 学校提供商管理-公司绑定配置VO
 **/
@Data
public class InstitutionProviderCompanyDto extends BaseVoEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 学校提供商Id
     */
    @ApiModelProperty(value = "学校提供商Id", required = true)
    @NotNull(message = "学校提供商Id不能为空", groups = {Add.class, Update.class})
    private Long fkInstitutionProviderId;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
}
