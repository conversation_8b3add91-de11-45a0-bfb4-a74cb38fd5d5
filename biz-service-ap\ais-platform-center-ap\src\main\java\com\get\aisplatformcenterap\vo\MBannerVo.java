package com.get.aisplatformcenterap.vo;

import com.get.aisplatformcenterap.entity.MBannerEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MBannerVo extends MBannerEntity {

    @ApiModelProperty("平台名称")
    private String platFormName;

    @ApiModelProperty("公司名字")
    private String companyName;
    @ApiModelProperty("类型名称")
    private String typeName;

    @ApiModelProperty("宽")
    private int width;
    @ApiModelProperty("高")
    private int height;

    @ApiModelProperty("面图")
    private String fileKey;

    @ApiModelProperty("文件guid(文档中心)")
    private String fileGuid;



}
