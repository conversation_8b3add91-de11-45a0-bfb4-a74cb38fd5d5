package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.institutioncenter.vo.InstitutionProviderVo;
import com.get.salecenter.vo.EventPlanVo;
import com.get.salecenter.vo.EventPlanFormVo;
import com.get.salecenter.entity.EventPlan;
import com.get.salecenter.dto.EventPlanSearchDto;
import com.get.salecenter.dto.EventPlanDto;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
public interface EventPlanService extends IService<EventPlan> {

    /**
     * 获取活动年度计划表单
     * <AUTHOR>
     * @DateTime 2023/12/12 17:34
     */
    EventPlanFormVo getForm(Long id, Long fkEventPlanRegistrationId);

    /**
     * 列表数据
     * <AUTHOR>
     * @DateTime 2023/12/13 15:55
     */
    List<EventPlanVo> getEventPlans(EventPlanSearchDto vo, Page page);

    /**
     * 详情
     * <AUTHOR>
     * @DateTime 2023/12/13 17:55
     */
    EventPlanVo findEventPlanById(Long id);

    /**
     * 新增
     * <AUTHOR>
     * @DateTime 2023/12/13 17:28
     */
    Long addEventPlan(EventPlanDto eventPlanDto);

    /**
     * 删除
     * <AUTHOR>
     * @DateTime 2023/12/13 17:29
     */
    void delete(Long id);

    /**
     * 修改
     * <AUTHOR>
     * @DateTime 2023/12/13 17:29
     */
    EventPlanVo updateEventPlan(EventPlanDto vo);

    /**
     * 所属公司下拉
     * <AUTHOR>
     * @DateTime 2023/12/13 17:31
     */
    List<Map<Long,String>> getCompanyList();

    /**
     * 年度下拉
     * <AUTHOR>
     * @DateTime 2023/12/13 17:32
     */
    List<Integer> getYearList();

    /**
     * 根据关键字模糊查询学校提供商列表
     * <AUTHOR>
     * @DateTime 2023/12/19 17:22
     */
    List<InstitutionProviderVo> getInstitutionProvidersByName(String keyword);
}
