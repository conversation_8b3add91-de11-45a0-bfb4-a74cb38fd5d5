//package com.get.platformconfigcenter.controller;
//
//import com.get.platformconfigcenter.props.GetProperties;
//import io.swagger.annotations.Api;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.cloud.context.config.annotation.RefreshScope;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
///**
// * Demo控制器 测试nacos修改后，会及时刷新值
// *
// */
//@RefreshScope
//@RestController
//@RequestMapping("demo")
//@Api(value = "nacos配置接口", tags = "即时刷新nacos配置")
//public class DemoController {
//
//	@Value("${demo.name}")
//	private String name;
//
//	private final GetProperties properties;
//
//	public DemoController(GetProperties properties) {
//		this.properties = properties;
//	}
//
//
//	@GetMapping("name")
//	public String getName() {
//		return name;
//	}
//
//	@GetMapping("name-by-props")
//	public String getNameByProps() {
//		return properties.getName();
//	}
//
//}
