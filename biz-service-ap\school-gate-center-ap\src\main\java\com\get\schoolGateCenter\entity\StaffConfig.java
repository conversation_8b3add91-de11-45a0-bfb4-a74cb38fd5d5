package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.type.Alias;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_staff_config")
@Alias("SchoolGateStaffConfig")
public class StaffConfig extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;
    /**
     * 设定类型key
     */
    @ApiModelProperty(value = "设定类型key")
    @Column(name = "config_type_key")
    private String configTypeKey;
    /**
     * 设定数值
     */
    @ApiModelProperty(value = "设定数值")
    @Column(name = "config_value")
    private String configValue;
}