package com.get.examcenter.mapper.appexam;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.examcenter.vo.UserPaperScoreVo;
import com.get.examcenter.vo.UserexaminationPaperScoreVo;
import com.get.examcenter.entity.UserexaminationPaperScore;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@DS("appexamdb")
public interface UserexaminationPaperScoreMapper extends BaseMapper<UserexaminationPaperScore> {
//    int insert(UserexaminationPaperScore record);

    int insertSelective(UserexaminationPaperScore record);

    int updateByPrimaryKeySelective(UserexaminationPaperScore record);

    int updateByPrimaryKey(UserexaminationPaperScore record);

    /**
     * @Description: 考卷查看排行榜
     * @Author: Jerry
     * @Date:15:52 2021/8/23
     */
    List<UserexaminationPaperScoreVo> viewExaminationPaperLeaderboard(@Param("fkExaminationPaperId") Long fkExaminationPaperId);

    /**
     * @Description: 考试查看排行榜
     * @Author: Jerry
     * @Date:15:52 2021/8/23
     */
    List<UserexaminationPaperScoreVo> viewExaminationLeaderboard(@Param("fkExaminationId") Long fkExaminationId);

    /**
     * @Description: 考卷根据姓名查看排行榜
     * @Author: Jerry
     * @Date:15:53 2021/8/23
     */
    List<UserexaminationPaperScoreVo> viewExaminationPaperLeaderboardByUserIds(
            IPage<UserexaminationPaperScoreVo> iPage,
            @Param("fkExaminationPaperId") Long fkExaminationPaperId,
            @Param("userStaffBdIds") Set<Long> userStaffBdIds,
            @Param("userIds") Set<Long> userIds);

    /**
     * @Description: 考试根据姓名查看排行榜
     * @Author: Jerry
     * @Date:15:53 2021/8/23
     */
    List<UserexaminationPaperScoreVo> viewExaminationLeaderboardByUserIds(
            IPage<UserexaminationPaperScoreVo> iPage,
            @Param("fkExaminationId") Long fkExaminationId,
            @Param("userStaffBdIds") Set<Long> userStaffBdIds,
            @Param("userIds") Set<Long> userIds);

    /**
     * @ Description :列表
     * @ Param [data]
     * @ return java.util.List<com.get.examcenter.vo.UserexaminationPaperScoreDto>
     * @ author LEO
     */
    List<UserexaminationPaperScoreVo> getUserExaminationPaperScore(IPage<UserexaminationPaperScoreVo> iPage, @Param("userIds") Set<Long> userIds);

    List<UserPaperScoreVo>selectUserPaperScourceList(@Param("fkExaminationPaperId") Long fkExaminationPaperId);
}