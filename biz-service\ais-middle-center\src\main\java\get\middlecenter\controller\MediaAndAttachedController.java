package get.middlecenter.controller;

import com.get.aismiddle.dto.UploadMediaAndAttachedRequestDto;
import com.get.aismiddle.dto.UploadRequestDto;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import get.middlecenter.service.IMediaAndAttachedService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "公用附件接口")
@RestController
@RequestMapping("middle/media")
public class MediaAndAttachedController {

    @Resource
    private IMediaAndAttachedService mediaAndAttachedService;

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "上传文件")
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.ADD, description = "中台/上传文件")
    @PostMapping("upload")
    public ResponseBo upload(@ModelAttribute @Validated UploadRequestDto uploadRequestDto) {
        return new ResponseBo(mediaAndAttachedService.upload(uploadRequestDto));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "保存文件")
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.ADD, description = "中台/保存文件")
    @PostMapping("uploadMediaAndAttached")
    public ResponseBo upload(@RequestBody @Validated List<UploadMediaAndAttachedRequestDto> uploadMediaAndAttachedRequestList) {
        mediaAndAttachedService.uploadMediaAndAttached(uploadMediaAndAttachedRequestList);
        return ResponseBo.ok();
    }

}
