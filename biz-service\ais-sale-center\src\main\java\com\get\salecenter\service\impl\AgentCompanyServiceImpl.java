package com.get.salecenter.service.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.AgentCompanyMapper;
import com.get.salecenter.dao.sale.AgentStaffMapper;
import com.get.salecenter.entity.AgentCompany;
import com.get.salecenter.entity.AgentStaff;
import com.get.salecenter.service.IAgentCompanyService;
import com.get.salecenter.dto.AgentCompanyDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/9/15
 * @TIME: 14:39
 * @Description:
 **/
@Service
public class AgentCompanyServiceImpl extends GetServiceImpl<AgentCompanyMapper,AgentCompany> implements IAgentCompanyService {
    @Resource
    private AgentCompanyMapper agentCompanyMapper;
    @Autowired
    private UtilService utilService;
    @Resource
    private AgentStaffMapper agentStaffMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editAgentCompany(List<AgentCompanyDto> agentCompanyDtos) {
        if (GeneralTool.isEmpty(agentCompanyDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        Long fkCompanyId = agentCompanyDtos.get(0).getFkCompanyId();
        Long fkAgentId = agentCompanyDtos.get(0).getFkAgentId();

        if (GeneralTool.isEmpty(fkCompanyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("edit_companyId"));
        }
        //若在安全里设置取消代理所属公司时，需要验证，所属公司必须包含当前所绑定BD的公司，是才能提交，否提交失败，
        //
        //并提示：现时代理所属公司，包含了绑定的BD代理所在的公司，修改代理所属公司失败。若要操作，请先解除BD绑定，再进行代理所属公司调整。
//        Example example = new Example(AgentCompany.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkAgentId", agentCompanyDtos.get(0).getFkAgentId());
//        //旧绑定公司数据
//        List<AgentCompany> agentCompanies = agentCompanyMapper.selectByExample(example);

        List<AgentCompany> agentCompanies = agentCompanyMapper.selectList(Wrappers.<AgentCompany>lambdaQuery().eq(AgentCompany::getFkAgentId, agentCompanyDtos.get(0).getFkAgentId()));
        //取绑公司ids
        List<Long> deleteCompanyIds = new ArrayList<>();
        for (AgentCompany agentCompany : agentCompanies) {
            boolean companyFlag = true;
            for (AgentCompanyDto agentCompanyDto : agentCompanyDtos) {
                //没取绑
                if (agentCompany.getFkCompanyId().equals(agentCompanyDto.getFkCompanyId())) {
                    companyFlag = false;
                }
            }
            //没匹配上，取绑了这个公司
            if (companyFlag) {
                deleteCompanyIds.add(agentCompany.getFkCompanyId());
            }
        }
        //校验已绑定的bd所属的公司是否被取消绑定了
        if (GeneralTool.isNotEmpty(deleteCompanyIds)) {
//            Example example1 = new Example(AgentStaff.class);
//            example1.createCriteria().andEqualTo("fkAgentId", fkAgentId).andEqualTo("isActive", 1);
//            List<AgentStaff> agentStaffs = agentStaffMapper.selectByExample(example1);

            List<AgentStaff> agentStaffs = agentStaffMapper.selectList(Wrappers.<AgentStaff>lambdaQuery()
                    .eq(AgentStaff::getFkAgentId, fkAgentId)
                    .eq(AgentStaff::getIsActive, 1));
            Set<Long> staffIds = agentStaffs.stream().map(AgentStaff::getFkStaffId).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(staffIds)) {
//                Map<Long, Long> staffCompanyIdMap = permissionCenterClient.getCompanyIdByStaffIds(staffIds);
                Result<Map<Long, Long>> result = permissionCenterClient.getCompanyIdByStaffIds(staffIds);
                Map<Long, Long> staffCompanyIdMap = new HashMap<>();
                if (result.isSuccess() && result.getData() != null) {
                    staffCompanyIdMap = result.getData();
                }
                Collection<Long> values = staffCompanyIdMap.values();
                for (Long deleteCompanyId : deleteCompanyIds) {
                    for (Long value : values) {
                        if (value.equals(deleteCompanyId)) {
                            throw new GetServiceException(LocaleMessageUtils.getMessage("bd_agent_company_data_association"));
                        }
                    }
                }
            }
        }

        //删除之前的记录
        List<Long> companyIds = SecureUtil.getCompanyIds();
        agentCompanyMapper.delete(Wrappers.<AgentCompany>lambdaQuery().eq(AgentCompany::getFkAgentId, agentCompanyDtos.get(0).getFkAgentId()).in(AgentCompany::getFkCompanyId,companyIds));
        //插入记录
        List<AgentCompany> agentCompanyList = agentCompanyDtos.stream().map(agentCompanyDto ->
                BeanCopyUtils.objClone(agentCompanyDto, AgentCompany::new)).collect(Collectors.toList());
        for (AgentCompany agentCompany : agentCompanyList) {
            utilService.setCreateInfo(agentCompany);
        }
        agentCompanyList.forEach(agentCompany -> agentCompanyMapper.insertSelective(agentCompany));

    }

    @Override
    public Long addRelation(AgentCompanyDto agentCompanyDto) {
        AgentCompany agentCompany = BeanCopyUtils.objClone(agentCompanyDto, AgentCompany::new);
        utilService.updateUserInfoToEntity(agentCompany);
        agentCompanyMapper.insertSelective(agentCompany);
        return agentCompany.getId();
    }

    @Override
    public List<Long> getRelationByCompanyId(List<Long> companyId) {
        if (GeneralTool.isEmpty(companyId)) {
            return null;
        }
//        Example example = new Example(AgentCompany.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andIn("fkCompanyId", companyId);
//        List<AgentCompany> agentCompanies = agentCompanyMapper.selectByExample(example);
        List<AgentCompany> agentCompanies = agentCompanyMapper.selectList(Wrappers.<AgentCompany>lambdaQuery().in(AgentCompany::getFkCompanyId, companyId));
        if (GeneralTool.isEmpty(agentCompanies)) {
            return null;
        }
        return agentCompanies.stream().map(AgentCompany::getFkAgentId).collect(Collectors.toList());
    }

    @Override
    public List<Long> getRelationByAgentId(Long agentId) {
        if (GeneralTool.isEmpty(agentId)) {
            return null;
        }
//        Example example = new Example(AgentCompany.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkAgentId", agentId);
//        List<AgentCompany> agentCompanies = agentCompanyMapper.selectByExample(example);

        List<AgentCompany> agentCompanies = agentCompanyMapper.selectList(Wrappers.<AgentCompany>lambdaQuery().eq(AgentCompany::getFkAgentId, agentId));

        if (GeneralTool.isEmpty(agentCompanies)) {
            return null;
        }
        return agentCompanies.stream().map(AgentCompany::getFkCompanyId).collect(Collectors.toList());
    }


    @Override
    public Map<Long, Set<Long>> getRelationByAgentIds(Set<Long> ids) {
        Map<Long, Set<Long>> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
//        Example example = new Example(AgentCompany.class);
//        example.createCriteria().andIn("fkAgentId", ids);
//        List<AgentCompany> agentCompanies = agentCompanyMapper.selectByExample(example);
        List<AgentCompany> agentCompanies = agentCompanyMapper.selectList(Wrappers.<AgentCompany>lambdaQuery().in(AgentCompany::getFkAgentId, ids));
        if (GeneralTool.isEmpty(agentCompanies)) {
            return map;
        }
        for (AgentCompany agentCompany : agentCompanies) {
            //如果集合包含这个代理id,则往原来的数据添加公司id
            if (map.containsKey(agentCompany.getFkAgentId())) {
                Set<Long> beforeRelationIds = map.get(agentCompany.getFkAgentId());
                beforeRelationIds.add(agentCompany.getFkCompanyId());
                map.put(agentCompany.getFkAgentId(), beforeRelationIds);
                continue;
            }
            //如果没有包含,则往里面添加新数据
            Set<Long> relationSet = new HashSet<>();
            relationSet.add(agentCompany.getFkCompanyId());
            map.put(agentCompany.getFkAgentId(), relationSet);
        }
        List<Long> companyIds = SecureUtil.getCompanyIds();
        map.values().stream().forEach(set -> set.removeIf(id -> !companyIds.contains(id)));
        return map;
    }

    @Override
    public List<Long> getAgentCompanyIdByContractId(Long contractId) {
        if (GeneralTool.isEmpty(contractId)) {
            return null;
        }
        return agentCompanyMapper.getAgentCompanyIdByContractId(contractId);
    }
}
