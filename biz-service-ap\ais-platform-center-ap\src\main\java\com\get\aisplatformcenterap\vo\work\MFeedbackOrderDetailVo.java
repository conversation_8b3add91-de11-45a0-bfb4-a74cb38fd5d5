package com.get.aisplatformcenterap.vo.work;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.get.aisplatformcenterap.entity.MFeedbackOrderEntity;
import com.get.aisplatformcenterap.entity.MFeedbackOrderReplyEntity;
import com.get.aisplatformcenterap.vo.FileArray;
import com.get.aisplatformcenterap.vo.FilePubArray;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class MFeedbackOrderDetailVo extends MFeedbackOrderEntity {
    @ApiModelProperty("公司名字")
    private String companyName;
    @ApiModelProperty("所属平台名称")
    private String platformName;

    @ApiModelProperty("类型名称")
    private String typeName;

    @ApiModelProperty("代理ID")
    private Long fkAgentId;

    @ApiModelProperty("代理类型")
    private String nature;

    @ApiModelProperty("代理类型名称")
    private String natureName;

    @ApiModelProperty("华通反馈人邮箱")
    private String email;


    @ApiModelProperty("反馈附件")
    List<FilePubArray> fileArray;

    List<MFeedbackOrderReplyVo> replyVoList;

    public String getNatureName() {
        if(StringUtils.isNotBlank(nature)){
            if("1".equals(nature)){
                natureName="公司";
            }else if("2".equals(nature)){
                natureName="个人";
            }else if("3".equals(nature)){
                natureName="工作室";
            }else if("4".equals(nature)){
                natureName="国际学校";
            }else if("5".equals(nature)){
                natureName="其他";
            }else if("6".equals(nature)){
                natureName="个人账户公司";
            }
        }
        return natureName;
    }
}
