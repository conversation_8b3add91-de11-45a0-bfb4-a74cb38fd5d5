package com.get.schoolGateCenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class StaffVo extends BaseVoEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id", required = true)
    @NotNull(message = "公司Id不能为空", groups = {Add.class, Update.class})
    @Min(value = 1, message = "缺少公司id参数", groups = {Add.class, Update.class})
    private Long fkCompanyId;
    /**
     * 部门Id
     */
    @ApiModelProperty(value = "部门Id", required = true)
    @NotNull(message = "部门Id不能为空", groups = {Add.class, Update.class})
    private Long fkDepartmentId;
    /**
     * 职位Id
     */
    @ApiModelProperty(value = "职位Id", required = true)
    @NotNull(message = "职位Id不能为空", groups = {Add.class, Update.class})
    private Long fkPositionId;
    /**
     * 办公室Id
     */
    @ApiModelProperty("办公室Id")
    private Long fkOfficeId;
    /**
     * 直属上司Id（公司架构）
     */
    @ApiModelProperty("直属上司Id（公司架构）")
    private Long fkStaffIdSupervisor;
    /**
     * 简历guid(人才中心)
     */
    @ApiModelProperty("简历guid(人才中心)")
    private String fkResumeGuid;
    /**
     * 登陆用户Id
     */
    @ApiModelProperty("登陆用户Id")
    private String loginId;
    /**
     * 目标登陆用户Id
     */
    @ApiModelProperty("目标登陆用户Id")
    private String targetLoginId;
    /**
     * 登陆用户密码
     */
    @ApiModelProperty("登陆用户密码")
    private String loginPs;
    /**
     * 员工编号
     */
    @ApiModelProperty(value = "员工编号", required = true)
    private String num;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名", required = true)
    @NotBlank(message = "员工姓名不能为空", groups = {Add.class, Update.class})
    private String name;
    /**
     * 英文名
     */
    @ApiModelProperty("英文名")
    private String nameEn;
    /**
     * 性别：0女/1男
     */
    @ApiModelProperty("性别：0女/1男")
    private Integer gender;
    /**
     * 生日
     */
    @ApiModelProperty("生日")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;
    /**
     * 户口：农业户口/城镇户口
     */
    @ApiModelProperty("户口：农业户口/城镇户口")
    private Integer hukou;

    /**
     * 证件类型枚举：0身份证/1护照/2通行证/3回乡证
     */
    @ApiModelProperty(value = "证件类型枚举：0身份证/1护照/2通行证/3回乡证")
    @NotNull(message = "证件类型不能为空", groups = {Add.class, Update.class})
    private Integer identityType;
    /**
     * 身份证号
     */
    @ApiModelProperty("身份证号")
    private String identityNum;
    /**
     * 身份证地址
     */
    @ApiModelProperty("身份证地址")
    private String identityCardAddress;
    /**
     * 住址电话区号
     */
    @ApiModelProperty("住址电话区号")
    private String homeTelAreaCode;
    /**
     * 住址电话
     */
    @ApiModelProperty("住址电话")
    private String homeTel;
    /**
     * 工作电话区号
     */
    @ApiModelProperty("工作电话区号")
    private String workTelAreaCode;
    /**
     * 工作电话
     */
    @ApiModelProperty("工作电话")
    private String workTel;
    /**
     * 手机区号
     */
    @ApiModelProperty("手机区号")
    private String mobileAreaCode;
    /**
     * 移动电话
     */
    @ApiModelProperty("移动电话")
    private String mobile;
    /**
     * Email
     */
    @ApiModelProperty("Email")
    private String email;
    /**
     * 邮编
     */
    @ApiModelProperty("邮编")
    private String zipCode;
    /**
     * 联系地址国家Id
     */
    @ApiModelProperty("联系地址国家Id")
    private Long fkAreaCountryId;
    /**
     * 联系地址州省Id
     */
    @ApiModelProperty("联系地址州省Id")
    private Long fkAreaStateId;
    /**
     * 联系地址城市Id
     */
    @ApiModelProperty("联系地址城市Id")
    private Long fkAreaCityId;
    /**
     * 联系地址城市区域Id
     */
    @ApiModelProperty("联系地址城市区域Id")
    private Long fkAreaCityDivisionId;

    /**
     * 地址
     */
    @ApiModelProperty("地址")
    private String address;
    /**
     * QQ号
     */
    @ApiModelProperty("QQ号")
    private String qq;
    /**
     * 微信号
     */
    @ApiModelProperty("微信号")
    private String wechat;
    /**
     * whatsapp号
     */
    @ApiModelProperty("whatsapp号")
    private String whatsapp;
    /**
     * 紧急联系人
     */
    @ApiModelProperty(value = "紧急联系人")
    @NotBlank(message = "紧急联系人不能为空", groups = {Add.class, Update.class})
    private String emergencyContact;

    /**
     * 紧急联系人关系：父母/妻子/儿女/亲戚/朋友
     */
    @ApiModelProperty(value = "紧急联系人关系：父母/妻子/儿女/亲戚/朋友")
    @NotBlank(message = "紧急联系人关系不能为空", groups = {Add.class, Update.class})
    private String emergencyRelationship;

    /**
     * 紧急联系人电话
     */
    @ApiModelProperty(value = "紧急联系人电话")
    @NotBlank(message = "紧急联系人电话不能为空", groups = {Add.class, Update.class})
    private String emergencyTel;
    /**
     * 职责
     */
    @ApiModelProperty("职责")
    private String jobDescription;
    /**
     * 工资生效日期
     */
    @ApiModelProperty("工资生效日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date salaryEffectiveDate;
    /**
     * 基本工资
     */
    @ApiModelProperty("基本工资")
    private BigDecimal salaryBase;
    /**
     * 绩效工资
     */
    @ApiModelProperty("绩效工资")
    private BigDecimal salaryPerformance;
    /**
     * 岗位津贴
     */
    @ApiModelProperty("岗位津贴")
    private BigDecimal allowancePosition;
    /**
     * 餐饮津贴
     */
    @ApiModelProperty("餐饮津贴")
    private BigDecimal allowanceCatering;
    /**
     * 交通津贴
     */
    @ApiModelProperty("交通津贴")
    private BigDecimal allowanceTransportation;
    /**
     * 通讯津贴
     */
    @ApiModelProperty("通讯津贴")
    private BigDecimal allowanceTelecom;
    /**
     * 其他津贴
     */
    @ApiModelProperty("其他津贴")
    private BigDecimal allowanceOther;
    /**
     * 入职时间
     */
    @ApiModelProperty("入职时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date entryDate;
    /**
     * 转正时间
     */
    @ApiModelProperty("转正时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date passProbationDate;
    /**
     * 离职时间
     */
    @ApiModelProperty("离职时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date leaveDate;
    /**
     * 是否在职，0否/1是
     */
    @ApiModelProperty("是否在职，0否/1是")
    private Boolean isOnDuty;
    /**
     * 是否领取离职证明，0否/1是
     */
    @ApiModelProperty("是否领取离职证明，0否/1是")
    private Boolean isGetLeavingCertificate;
    /**
     * 是否已经停保，0否/1是
     */
    @ApiModelProperty("是否已经停保，0否/1是")
    private Boolean isStopSocialInsurance;
    /**
     * 停保年月
     */
    @ApiModelProperty("停保年月")
    private String stopSocialInsuranceMonth;
    /**
     * 考勤编号
     */
    @ApiModelProperty("考勤编号")
    private String attendanceNum;
    /**
     * 年假基数
     */
    @ApiModelProperty("年假基数")
    private BigDecimal annualLeaveBase;

    /**
     * 补休基数
     */
    @ApiModelProperty("补休基数")
    private BigDecimal compensatoryLeaveBase;
    /**
     * 是否强制修改密码，0否/1是
     */
    @ApiModelProperty("是否强制修改密码，0否/1是")
    private Boolean isModifiedPs;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty("是否激活：0否/1是")
    private Boolean isActive;
    /**
     * 是否记住登录状态
     */
    private boolean rememberMe;

    /**
     * 头像媒体附件id
     */
    @ApiModelProperty(value = "头像媒体附件id")
    private Long fkMediaId;

    /**
     * 是否超级管理员：0否/1是
     */
    @ApiModelProperty("是否超级管理员：0否/1是")
    private Boolean isAdmin;
    private String keyWord;

    /**
     * 新密码
     */
    @ApiModelProperty("新密码")
    private String newLoginPs;

    /**
     * 是否可编辑简历，0否/1是
     */
    @ApiModelProperty("是否可编辑简历，0否/1是")
    private Boolean isModifiedResume=true;

    @ApiModelProperty(value = "ip白名单")
    private String ipWhiteList;

    @ApiModelProperty(value = "是否需要验证码登陆")
    private Boolean isVcodeRequired = false;
}
