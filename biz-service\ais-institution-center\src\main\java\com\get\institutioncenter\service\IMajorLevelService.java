package com.get.institutioncenter.service;


import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.vo.MajorLevelVo;
import com.get.institutioncenter.vo.MajorLevelSelectVo;
import com.get.institutioncenter.entity.MajorLevel;
import com.get.institutioncenter.dto.MajorLevelDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/7/31 12:12
 * @verison: 1.0
 * @description: 专业等级管理接口
 */
public interface IMajorLevelService extends BaseService<MajorLevel> {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    MajorLevelVo findMajorLevelById(Long id);

    /**
     * 批量新增
     *
     * @param majorLevelDtos
     * @return
     */
    void batchAdd(List<MajorLevelDto> majorLevelDtos);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    void delete(Long id);

    /**
     * 修改
     *
     * @param majorLevelDto
     * @return
     */
    MajorLevelVo updateMajorLevel(MajorLevelDto majorLevelDto);


    /**
     * 列表
     *
     * @param majorLevelDto
     * @param page
     * @return
     */
    List<MajorLevelVo> getMajorLevels(MajorLevelDto majorLevelDto, Page page);

    /**
     * 上移下移
     *
     * @param majorLevelDtos
     * @return
     */
    void movingOrder(List<MajorLevelDto> majorLevelDtos);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description :专业等级下拉框
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getMajorLevelSelect();

    /**
     * @Description :
     * @Param [ids]
     * <AUTHOR>
     */
    Map<Long, String> getMajorLevelNamesByIds(Set<Long> ids);

    Map<Long, MajorLevel> getMajorLevelByIds(Set<Long> ids);

    /**
     * feign调用 通过课程等级id 查找对应的课程等级名称
     *
     * @Date 16:24 2021/5/24
     * <AUTHOR>
     */
    String getMajorLevelNamesById(Long id);

    /**
     * 课程对应的课程等级
     *
     * @param ids
     * @return
     */
    Map<Long, String> getMajorLevelNamesByCourIds(Set<Long> ids);

    String getMajorLevelIdStringByCourseId(Long id);

    /**
     * @Description :
     * @Param [ids]
     * <AUTHOR>
     */
    Map<Long, String> getMajorLevelNameByIds(Set<Long> ids);

    /**
     * 课程等级下拉菜单
     * @return
     */
    List<MajorLevelSelectVo> getMajorLevelAndGroupSelect();

    /**
     * feign调用 通过课程等级id 查找对应的课程等级中文名称
     * @param ids
     * @return
     */
    Map<Long, String> getMajorLevelNameChnsByIds(Set<Long> ids);

    Map<Long,Set<Long>> getMajorLevelIdsByIds(Set<Long> ids);
}
