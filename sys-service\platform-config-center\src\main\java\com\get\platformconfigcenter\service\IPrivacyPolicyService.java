package com.get.platformconfigcenter.service;

import com.get.common.result.SearchBean;
import com.get.platformconfigcenter.vo.PrivacyPolicyVo;
import com.get.platformconfigcenter.dto.PrivacyPolicyDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Hardy
 * @create: 2021/5/13 15:53
 * @verison: 1.0
 * @description:
 */
public interface IPrivacyPolicyService {
    /**
     * @return java.util.List<com.get.registrationcenter.vo.PrivacyPolicyVo>
     * @Description :列表
     * @Param [privacyPolicyDto, page]
     * <AUTHOR>
     */
    List<PrivacyPolicyVo> getPrivacyPolicys(PrivacyPolicyDto privacyPolicyDto, SearchBean<PrivacyPolicyDto> page);

    /**
     * @return java.lang.Long
     * @Description :
     * @Param privacyPolicyDto
     * <AUTHOR>
     */
    Long addPrivacyPolicy(PrivacyPolicyDto privacyPolicyDto);

    /**
     * @return com.get.registrationcenter.vo.PrivacyPolicyVo
     * @Description :
     * @Param privacyPolicyDto
     * <AUTHOR>
     */
    PrivacyPolicyVo updatePrivacyPolicyVo(PrivacyPolicyDto privacyPolicyDto);

    /**
     * @return com.get.registrationcenter.vo.PrivacyPolicyVo
     * @Description :
     * @Param [id]
     * <AUTHOR>
     */
    PrivacyPolicyVo findPrivacyPolicyById(Long id);

    /**
     * @return void
     * @Description :
     * @Param [id]
     * <AUTHOR>
     */
    void deletePrivacyPolicy(Long id);

    /**
     * @return java.util.List<com.get.registrationcenter.vo.PrivacyPolicyVo>
     * @Description :城市资讯类型下拉框数据
     * @Param []
     * <AUTHOR>
     */
    List<PrivacyPolicyVo> getPrivacyPolicyList();

    /**
     * @return java.util.Map
     * @Description :
     * @Param [ids]
     * <AUTHOR>
     */
    Map<Long, String> getPrivacyPolicyTitlesByIds(Set<Long> ids);
}
