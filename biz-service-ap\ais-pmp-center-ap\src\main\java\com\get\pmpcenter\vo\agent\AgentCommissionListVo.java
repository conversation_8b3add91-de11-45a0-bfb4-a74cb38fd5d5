package com.get.pmpcenter.vo.agent;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/12  16:47
 * @Version 1.0
 * @Description:代理佣金方案明细列表
 */
@Data
public class AgentCommissionListVo {

    @ApiModelProperty(value = "代理单项佣金明细列表")
    private List<AgentCommissionInfo> agentCommissionInfoList;

    @ApiModelProperty(value = "组合课程列表")
    private List<AgentCombinationInfo> agentCombinationList;

    @ApiModelProperty(value = "整体佣金Bonus条件列表")
    private List<AgentBonusInfo> agentBonusList;

    @ApiModelProperty(value = "关联学校ID集合")
    private List<Long> institutionIds;


    public AgentCommissionListVo() {
        this.agentCommissionInfoList = new ArrayList<>();
        this.agentCombinationList = new ArrayList<>();
        this.agentBonusList = new ArrayList<>();
        this.institutionIds = new ArrayList<>();
    }

    @Data
    @ApiModel(value = "代理佣金明细基础信息")
    public static class AgentCommissionBaseInfo {

        @ApiModelProperty(value = "佣金明细ID-编辑传")
        private Long id;

        @ApiModelProperty(value = "学校提供商Id-必传")
        private Long fkInstitutionProviderId;

        @ApiModelProperty(value = "代理佣金方案Id-必传")
        private Long fkAgentCommissionPlanId;

        @ApiModelProperty(value = "佣金类型：1课程/2阶梯/3组合")
        private Integer commissionType;

        @ApiModelProperty(value = "佣金")
        private BigDecimal commission;

        @ApiModelProperty(value = "原始佣金-新增编辑不用传")
        private BigDecimal originalCommission;

        @ApiModelProperty(value = "原始佣金单位-新增编辑不用传")
        private String originalCommissionUnit;

        @ApiModelProperty(value = "佣金单位：%/CNY/等货币编号")
        private String commissionUnit;

        @ApiModelProperty(value = "后续佣金")
        private BigDecimal followCommission;

        @ApiModelProperty(value = "原始后续佣金-新增编辑不用传")
        private BigDecimal originalFollowCommission;

        @ApiModelProperty(value = "原始后续佣金单位-新增编辑不用传")
        private String originalFollowCommissionUnit;

        @ApiModelProperty(value = "后续佣金单位：%/CNY/等货币编号")
        private String followCommissionUnit;

        @ApiModelProperty(value = "备注")
        private String remarkNote;

        @ApiModelProperty(value = "备注（本地语言）")
        private String remarkNoteNative;

        @ApiModelProperty(value = "是否激活：0否/1是")
        private Integer isActive;

        @ApiModelProperty(value = "标题")
        private String title;

        @ApiModelProperty(value = "标题（本地语言）")
        private String titleNative;

        @ApiModelProperty(value = "课程")
        private String course;

        @ApiModelProperty(value = "后续备注")
        private String followRemarkNote;

        @ApiModelProperty(value = "后续备注（中文）")
        private String followRemarkNoteNative;

        @ApiModelProperty(value = "学校提供商佣金Id（继承，自己添加没有，用处：佣金提醒，学校提供商佣金明细删除时会统一删除继承）")
        private Long fkInstitutionProviderCommissionId;

        @ApiModelProperty(value = "佣金是否超过限制")
        private Boolean commissionOverLimit = Boolean.FALSE;

        @ApiModelProperty(value = "后续佣金是否超过限制")
        private Boolean followCommissionOverLimit = Boolean.FALSE;

        @ApiModelProperty(value = "保存限制的最大比例")
        private String saveRate;
    }

    @Data
    @ApiModel(value = "代理单项佣金明细信息")
    public static class AgentCommissionInfo extends AgentCommissionBaseInfo {

        @ApiModelProperty(value = "课程等级Id")
        private Long levelId;

        @ApiModelProperty(value = "等级名称")
        private String customName;

        @ApiModelProperty(value = "等级名称-中文")
        private String customNameChn;

        @ApiModelProperty(value = "后续学校提供商Id（不一致才需要选择）")
        private Long fkInstitutionProviderIdFollow;

        @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
        private Integer viewOrder;

        @ApiModelProperty(value = "父级课程等级Id")
        private Long parentLevelId;

        @ApiModelProperty(value = "父级等级名称")
        private String parentLevelName;

        @ApiModelProperty(value = "父级等级名称-中文")
        private String parentLevelNameChn;
    }

    //一个组合里面包含多个课程等级明细（commissionId）,通过packageKey区分组
    @Data
    @ApiModel(value = "代理组合课程列表信息")
    public static class AgentCombinationInfo {

        @ApiModelProperty(value = "组合名称（同组相同）")
        private String packageName;

        @ApiModelProperty(value = "组合名称（本地语言）（同组相同）")
        private String packageNameNative;

        @ApiModelProperty(value = "组合key-保存过才会有,新增时没有,系统自动生成")
        private String packageKey;

        @ApiModelProperty(value = "整个组合包含的所有课程等级列表-新增编辑不用传")
        private List<Long> levelIds;

        @ApiModelProperty(value = "整个组合包含的所有课程等级名称列表-新增编辑不用传")
        private List<String> levelNames;

        @ApiModelProperty(value = "包含的佣金明细列表")
        private List<AgentCommissionInfo> agentCommissionInfos;

    }

    @Data
    @ApiModel(value = "代理整体佣金Bonus列表信息")
    public static class AgentBonusInfo extends AgentCommissionBaseInfo {

        @ApiModelProperty(value = "阶梯起始学生数")
        private Integer studentCountMin;

        @ApiModelProperty(value = "阶梯结束学生数")
        private Integer studentCountMax;

        @ApiModelProperty(value = "阶梯统计类型：0不追加/1从第1个学生开始计算")
        private Integer studentCountType;

        @ApiModelProperty(value = "包含的课程等级列表")
        private List<Long> levelIds;

        @ApiModelProperty(value = "整个组合包含的所有课程等级名称列表-新增编辑不用传")
        private List<String> levelNames;

    }

}
