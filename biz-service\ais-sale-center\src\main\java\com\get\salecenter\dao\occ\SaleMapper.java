package com.get.salecenter.dao.occ;


import com.get.salecenter.vo.StudentOfferItemVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
@Mapper
public interface SaleMapper {


    StudentOfferItemVo getOfferByCourseId(String courseId);

    List<StudentOfferItemVo> getOfferByErrorState(@Param("list")List<String> list);
}
