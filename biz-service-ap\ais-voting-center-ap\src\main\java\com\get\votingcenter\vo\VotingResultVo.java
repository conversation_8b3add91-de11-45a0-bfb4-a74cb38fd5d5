package com.get.votingcenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/9/29 12:52
 * @verison: 1.0
 * @description:
 */
@Data
public class VotingResultVo {

    /**
     * 投票选项list
     */
    @ApiModelProperty(value = "投票选项list")
    private List<VotingItemOptionVo> votingItemOptionDtos;

    /**
     * 总投票数
     */
    @ApiModelProperty(value = "总投票数")
    private Long sumVotingCount;
}
