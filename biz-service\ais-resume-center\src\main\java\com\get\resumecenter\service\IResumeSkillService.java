package com.get.resumecenter.service;


import com.get.resumecenter.vo.ResumeSkillVo;
import com.get.resumecenter.dto.ResumeSkillDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/10
 * @TIME: 15:00
 * @Description: 员工技能
 **/
public interface IResumeSkillService {

    /**
     * 根据简历id 查询所有的技能
     *
     * @param resumeId
     * @return
     * @
     */
    List<ResumeSkillVo> getResumeSkillListDto(Long resumeId);

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    ResumeSkillVo getResumeSkillById(Long id);


    /**
     * 添加
     *
     * @param skillVo
     * @return
     */
    Long addResumeSkill(ResumeSkillDto skillVo);

    /**
     * 修改
     *
     * @param skillVo
     * @return
     */
    ResumeSkillVo updateResumeSkill(ResumeSkillDto skillVo);

    /**
     * 删除
     *
     * @param id
     */
    void deleteResumeSkill(Long id);


}
