package com.get.insurancecenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 交易流水业务类型枚举类
 */

@Getter
@AllArgsConstructor
public enum RelationTargetEnum {

    INSURANCE_ORDER("m_insurance_order", "保险订单"),
    ;

    private String code;

    private String msg;


    public static RelationTargetEnum getEnumByCode(String code) {
        for (RelationTargetEnum value : RelationTargetEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
