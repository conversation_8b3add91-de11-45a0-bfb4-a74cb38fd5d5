package com.get.financecenter.excelmodel;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 留学住宿模板
 *
 * <AUTHOR>
 * @date 2022/1/15 17:32
 */
@Data
public class AgentSettlementAccommodationModel {

    @ApiModelProperty(value = "应付计划id")
    private Long id;

    @ApiModelProperty(value = "学生名字")
    private String studentName;

    @ApiModelProperty(value = "国家名称")
    private String countryName;

    @ApiModelProperty(value = "国家编号")
    private String countryNum;

    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "国家名")
    private Long fkAreaCountryName;

    @ApiModelProperty(value = "公寓名称")
    private String apartmentName;

    @ApiModelProperty(value = "入住时间")
    private String checkInDate;

    @ApiModelProperty(value = "住宿天数")
    private String duration;

    @ApiModelProperty(value = "应付币种")
    private String payablePlanCurrency;

    @ApiModelProperty(value = "应付金额")
    private BigDecimal payableAmount;

    @ApiModelProperty(value = "折合已收金额")
    private BigDecimal paid;

    @ApiModelProperty(value = "本次结算金额")
    private BigDecimal outstanding;

    @ApiModelProperty(value = "实际手续费金额")
    private BigDecimal serviceFeeActual;

}
