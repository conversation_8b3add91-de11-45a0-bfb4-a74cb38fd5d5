<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.AppAgentMapper">

    <select id="getAppAgents" resultType="com.get.salecenter.vo.AppAgentListVo">
        SELECT
           a.*
        FROM
            m_app_agent a
        INNER JOIN (
        SELECT DISTINCT b.id
        FROM (

        SELECT b.id
        -- #第一层####################
        FROM ais_sale_center.m_app_agent b

        <!-- 代理统计报表跳转专用sql -->
        <if test="agentAnnualSummaryDto != null">
            INNER JOIN (
            SELECT
            maa.id
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.agentStatisticalByCreateTime"/>
            <if test="agentAnnualSummaryDto.jumpType == 2">
                AND maa.app_status IN (0,1)
            </if>
            <if test="agentAnnualSummaryDto.jumpType == 3">
                AND maa.app_status = 3
            </if>
            GROUP BY maa.id
            )jumpAgent ON jumpAgent.id = b.id
        </if>


        INNER JOIN (
        SELECT DISTINCT a.id FROM (
        -- #BD的权限
        SELECT a.id FROM ais_sale_center.m_app_agent a
        WHERE a.fk_staff_id IN
        <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
            #{staffFollowerId}
        </foreach>
        UNION ALL
        -- #创建人的权限
        SELECT a.id FROM ais_sale_center.m_app_agent a
        INNER JOIN ais_permission_center.m_staff b ON a.gmt_create_user=b.login_id AND b.id IN
        <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
            #{staffFollowerId}
        </foreach>
        ) a
        ) z ON b.id=z.id
        ) b
        ) c on a.id = c.id
        where 1=1
        <if test="appAgentListDto.fkCompanyId != null">

            AND a.fk_company_id
            IN
            <foreach item="item" index="index" collection="appAgentListDto.fkCompanyId" open="(" separator="," close=")">
                #{item}
            </foreach>

        </if>
        <if test="appAgentListDto.appStatus != null">
            AND a.app_status = #{appAgentListDto.appStatus}
        </if>
        <if test="appAgentListDto.appFrom != null">
            AND a.app_from = #{appAgentListDto.appFrom}
        </if>
        <if test="appAgentListDto.appType != null">
            AND a.app_type = #{appAgentListDto.appType}
        </if>
        <if test="appAgentListDto.name != null and appAgentListDto.name != ''">
            AND a.`name` LIKE CONCAT("%",#{appAgentListDto.name},"%")
        </if>
        <if test="appAgentListDto.createBeginTime != null">
            AND DATE_FORMAT(a.gmt_create,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{appAgentListDto.createBeginTime},'%Y-%m-%d')
        </if>
        <if test="appAgentListDto.createEndTime != null">
            AND DATE_FORMAT(a.gmt_create,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{appAgentListDto.createEndTime},'%Y-%m-%d')
        </if>
        <if test="appAgentListDto.fkStaffId != null">
            AND a.fk_staff_id = #{appAgentListDto.fkStaffId}
        </if>
        ORDER BY a.gmt_create desc
    </select>
    <select id="getBdStaffSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            c.id,
            concat_ws(
                    ',',
                    CONCAT(
                            c.NAME,
                            IF
                                (
                                        c.name_en IS NULL
                                        OR c.name_en = '',
                                        '',
                                        CONCAT( "（", c.name_en, "）" )))) AS fullName,
            c.NAME AS NAME
        FROM
            m_app_agent AS ma
                LEFT JOIN ais_permission_center.m_staff c  ON ma.fk_staff_id = c.id
        where c.is_on_duty = 1 AND c.is_active = 1
        and ma.fk_company_id = #{companyId}
        GROUP BY c.id
        ORDER BY c.gmt_create desc
    </select>
</mapper>