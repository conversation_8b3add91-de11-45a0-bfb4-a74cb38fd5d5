package com.get.platformconfigcenter.dao.registration;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.UserInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS("registrationdb")
public interface UserInfoMapper extends BaseMapper<UserInfo> {
    int insert(UserInfo record);

    int insertSelective(UserInfo record);

    int updateByPrimaryKeySelective(UserInfo record);

    int updateByPrimaryKey(UserInfo record);

    /**
     * 根据用户Ids获取用户信息
     *
     * @param fkUserIds
     * @return
     */
    List<UserInfo> getUserInfoByIds(@Param("fkUserIds") List<Long> fkUserIds);
}