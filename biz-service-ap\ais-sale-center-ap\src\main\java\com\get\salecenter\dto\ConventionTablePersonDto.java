package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/28 16:21
 * @verison: 1.0
 * @description:
 */
@Data
public class ConventionTablePersonDto extends BaseVoEntity {

    /**
     * 峰会桌子Id
     */
    @NotNull(message = "峰会桌子Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "峰会桌子Id", required = true)
    private Long fkConventionTableId;

    /**
     * 峰会参展人员Id
     */
    @NotNull(message = "峰会参展人员Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "峰会参展人员Id", required = true)
    private Long fkConventionPersonId;

    //自定义
    /**
     * 峰会桌台Ids
     */
    @ApiModelProperty(value = "峰会桌台Ids")
    private List<Long> tableIds;

  
}
