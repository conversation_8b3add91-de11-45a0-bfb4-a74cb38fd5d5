package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.core.mybatis.utils.ValidList;
import com.get.financecenter.dto.MediaAndAttachedDto;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import com.get.financecenter.vo.ProviderVo;
import com.get.financecenter.entity.Provider;
import com.get.financecenter.dto.ProviderDto;
import com.get.financecenter.dto.query.ProviderQueryDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/12/18 10:11
 * @verison: 1.0
 * @description:
 */
public interface IProviderService extends BaseService<Provider> {
    /**
     * @return com.get.salecenter.vo.ProviderDto
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    ProviderVo findProviderById(Long id);

    /**
     * @return void
     * @Description :新增
     * @Param [providerVo]
     * <AUTHOR>
     */
    Long addProvider(ProviderDto providerDto);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.salecenter.vo.ProviderDto
     * @Description :修改
     * @Param [providerVo]
     * <AUTHOR>
     */
    ProviderVo updateProvider(ProviderDto providerDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.ProviderDto>
     * @Description :列表
     * @Param [providerVo, page]
     * <AUTHOR>
     */
    List<ProviderVo> getProviders(ProviderQueryDto providerQueryDto, Page page);

    /**
     * @return java.util.List<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 附件列表
     * @Param [data, page]
     * <AUTHOR>
     */
    List<FMediaAndAttachedVo> getItemMedia(MediaAndAttachedDto data, Page page);

    /**
     * @return java.lang.Long
     * @Description: 新增附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    List<FMediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVo);

    List<ProviderVo> getProviderList();

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description :供应商下拉
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getProviderSelect();
}
