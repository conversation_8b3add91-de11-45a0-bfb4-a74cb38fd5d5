package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.salecenter.entity.Student;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 财务 编辑应付计划回显对象
 *
 * <AUTHOR>
 * @date 2021/12/23 15:39
 */
@Data
public class StudentPlanVo extends Student {

    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    @ApiModelProperty(value = "国家名称）")
    private String fkAreaCountryName;

    @ApiModelProperty(value = "学校提供商名称")
    private String institutionProviderName;

    @ApiModelProperty(value = "学校名称")
    private String fkInstitutionName;

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "学生代理名称")
    private String agentName;

    @ApiModelProperty(value = "目标对象")
    private String targetName;

    @ApiModelProperty(value = "计划类型")
    private String fkTypeKey;

    @ApiModelProperty(value = "计划类型名")
    private String fkTypeKeyName;

    @ApiModelProperty(value = "应付计划详情")
    private PayablePlanVo payablePlanDto;

    //===============实体类==================
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;
    /**
     * 学生编号
     */
    @ApiModelProperty(value = "学生编号")
    @Column(name = "num")
    private String num;
    /**
     * 学生姓名（中）
     */
    @ApiModelProperty(value = "学生姓名（中）")
    @Column(name = "name")
    private String name;
    /**
     * 姓（英/拼音）
     */
    @ApiModelProperty(value = "姓（英/拼音）")
    @Column(name = "last_name")
    private String lastName;
    /**
     * 名（英/拼音）
     */
    @ApiModelProperty(value = "名（英/拼音）")
    @Column(name = "first_name")
    private String firstName;
    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    @Column(name = "gender")
    private String gender;
    /**
     * 生日
     */
    @ApiModelProperty(value = "生日")
    @Column(name = "birthday")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;
    /**
     * 学生出生所在国家Id
     */
    @ApiModelProperty(value = "学生出生所在国家Id")
    @Column(name = "fk_area_country_id_birth")
    @UpdateWithNull
    private Long fkAreaCountryIdBirth;
    /**
     * 学生出生所在州省Id
     */
    @ApiModelProperty(value = "学生出生所在州省Id")
    @Column(name = "fk_area_state_id_birth")
    @UpdateWithNull
    private Long fkAreaStateIdBirth;
    /**
     * 学生出生所在城市Id
     */
    @ApiModelProperty(value = "学生出生所在城市Id")
    @Column(name = "fk_area_city_id_birth")
    @UpdateWithNull
    private Long fkAreaCityIdBirth;
    /**
     * 学生出生所在国家名称
     */
    @ApiModelProperty(value = "学生出生所在国家名称")
    @Column(name = "fk_area_country_name_birth")
    private String fkAreaCountryNameBirth;
    /**
     * 学生出生所在州省名称
     */
    @ApiModelProperty(value = "学生出生所在州省名称")
    @Column(name = "fk_area_state_name_birth")
    private String fkAreaStateNameBirth;
    /**
     * 学生出生所在城市名称
     */
    @ApiModelProperty(value = "学生出生所在城市名称")
    @Column(name = "fk_area_city_name_birth")
    private String fkAreaCityNameBirth;
    /**
     * 手机区号
     */
    @ApiModelProperty(value = "手机区号")
    @Column(name = "mobile_area_code")
    private String mobileAreaCode;
    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    @Column(name = "mobile")
    private String mobile;
    /**
     * 电话区号
     */
    @ApiModelProperty(value = "电话区号")
    @Column(name = "tel_area_code")
    private String telAreaCode;
    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    @Column(name = "tel")
    private String tel;
    /**
     * Email
     */
    @ApiModelProperty(value = "Email")
    @Column(name = "email")
    private String email;
    /**
     * 学生现居所在国家Id
     */
    @ApiModelProperty(value = "学生现居所在国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 学生现居所在州省Id
     */
    @ApiModelProperty(value = "学生现居所在州省Id")
    @Column(name = "fk_area_state_id")
    private Long fkAreaStateId;
    /**
     * 学生现居所在城市Id
     */
    @ApiModelProperty(value = "学生现居所在城市Id")
    @Column(name = "fk_area_city_id")
    private Long fkAreaCityId;

    /**
     * 学生现居所在州省名称
     */
    @ApiModelProperty(value = "学生现居所在州省名称")
    @Column(name = "fk_area_state_name")
    private String fkAreaStateName;
    /**
     * 学生现居所在城市名称
     */
    @ApiModelProperty(value = "学生现居所在城市名称")
    @Column(name = "fk_area_city_name")
    private String fkAreaCityName;
    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    @Column(name = "zipcode")
    private String zipcode;

//    /**
//     * 学历区域类型：国内/国际
//     */
//    @ApiModelProperty(value = "学历区域类型：国内/国际")
//    @Column(name = "education_area_type")
//    private String educationAreaType;
    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    @Column(name = "contact_address")
    private String contactAddress;
    /**
     * 学历等级类型：中专/高中/大学/大专/专升本/本科/研究生/硕士/博士在读/博士/博士后/HND
     */
    @ApiModelProperty(value = "学历等级类型：中专/高中/大学/大专/专升本/本科/研究生/硕士/博士在读/博士/博士后/HND")
    @Column(name = "education_level_type")
    private String educationLevelType;
    /**
     * 毕业专业
     */
    @ApiModelProperty(value = "毕业专业")
    @Column(name = "education_major")
    private String educationMajor;
    /**
     * 毕业大学国家Id
     */
    @ApiModelProperty(value = "毕业大学国家Id")
    @Column(name = "fk_area_country_id_education")
    private Long fkAreaCountryIdEducation;
    /**
     * 毕业大学州省Id
     */
    @ApiModelProperty(value = "毕业大学州省Id")
    @Column(name = "fk_area_state_id_education")
    private Long fkAreaStateIdEducation;
    /**
     * 毕业大学城市Id
     */
    @ApiModelProperty(value = "毕业大学城市Id")
    @Column(name = "fk_area_city_id_education")
    private Long fkAreaCityIdEducation;
    /**
     * 毕业大学国家名称
     */
    @ApiModelProperty(value = "毕业大学国家名称")
    @Column(name = "fk_area_country_name_education")
    private String fkAreaCountryNameEducation;
    /**
     * 毕业大学州省名称
     */
    @ApiModelProperty(value = "毕业大学州省名称")
    @Column(name = "fk_area_state_name_education")
    private String fkAreaStateNameEducation;
    /**
     * 毕业大学城市名称
     */
    @ApiModelProperty(value = "毕业大学城市名称")
    @Column(name = "fk_area_city_name_education")
    private String fkAreaCityNameEducation;
    /**
     * 毕业院校Id
     */
    @ApiModelProperty(value = "毕业院校Id")
    @Column(name = "fk_institution_id_education")
    @UpdateWithNull
    private Long fkInstitutionIdEducation;
    /**
     * 毕业院校名称
     */
    @ApiModelProperty(value = "毕业院校名称")
    @Column(name = "fk_institution_name_education")
    @UpdateWithNull
    private String fkInstitutionNameEducation;
    /**
     * 毕业学校类型：985/211/其他，默认选项：其他
     */
    @ApiModelProperty(value = "毕业学校类型：985/211/其他，默认选项：其他")
    @Column(name = "institution_type_education")
    private String institutionTypeEducation;
    /**
     * 学历等级类型（国际）：中专/高中/大学/大专/专升本/本科/研究生/硕士/博士在读/博士/博士后/HND
     */
    @ApiModelProperty(value = "学历等级类型（国际）：中专/高中/大学/大专/专升本/本科/研究生/硕士/博士在读/博士/博士后/HND")
    @Column(name = "education_level_type2")
    private String educationLevelType2;
    /**
     * 毕业专业（国际）
     */
    @ApiModelProperty(value = "毕业专业（国际）")
    @Column(name = "education_major2")
    private String educationMajor2;
    /**
     * 毕业大学国家Id（国际）
     */
    @ApiModelProperty(value = "毕业大学国家Id（国际）")
    @Column(name = "fk_area_country_id_education2")
    private Long fkAreaCountryIdEducation2;
    /**
     * 毕业大学州省Id（国际）
     */
    @ApiModelProperty(value = "毕业大学州省Id（国际）")
    @Column(name = "fk_area_state_id_education2")
    private Long fkAreaStateIdEducation2;
    /**
     * 毕业大学城市Id（国际）
     */
    @ApiModelProperty(value = "毕业大学城市Id（国际）")
    @Column(name = "fk_area_city_id_education2")
    private Long fkAreaCityIdEducation2;
    /**
     * 毕业大学国家名称（国际）
     */
    @ApiModelProperty(value = "毕业大学国家名称（国际）")
    @Column(name = "fk_area_country_name_education2")
    private String fkAreaCountryNameEducation2;
    /**
     * 毕业大学州省名称（国际）
     */
    @ApiModelProperty(value = "毕业大学州省名称（国际）")
    @Column(name = "fk_area_state_name_education2")
    private String fkAreaStateNameEducation2;
    /**
     * 毕业大学城市名称（国际）
     */
    @ApiModelProperty(value = "毕业大学城市名称（国际）")
    @Column(name = "fk_area_city_name_education2")
    private String fkAreaCityNameEducation2;
    /**
     * 毕业院校Id（国际）
     */
    @ApiModelProperty(value = "毕业院校Id（国际）")
    @Column(name = "fk_institution_id_education2")
    @UpdateWithNull
    private Long fkInstitutionIdEducation2;
    /**
     * 毕业院校名称（国际）
     */
    @ApiModelProperty(value = "毕业院校名称（国际）")
    @Column(name = "fk_institution_name_education2")
    @UpdateWithNull
    private String fkInstitutionNameEducation2;
    /**
     * 项目说明，枚举：0=3+1/1=2+2/2=4+0/3=交换生
     */
    @ApiModelProperty(value = "项目说明，枚举：0=3+1/1=2+2/2=4+0/3=交换生")
    @Column(name = "education_project")
    private Integer educationProject;
    /**
     * 学位情况，枚举：获得双学位/获得国际学位/获得国内学位
     */
    @ApiModelProperty(value = "学位情况，枚举：获得双学位/获得国际学位/获得国内学位")
    @Column(name = "education_degree")
    private Integer educationDegree;

    /**
     * 是否复杂教育背景：0否/1是
     */
    @ApiModelProperty(value = "是否复杂教育背景：0否/1是")
    @Column(name = "is_complex_education")
    private Boolean isComplexEducation;

    /**
     * '复杂教育背景备注'
     */
    @ApiModelProperty(value = "'复杂教育背景备注'")
    @Column(name = "complex_education_remark")
    private String complexEducationRemark;

    /**
     * 高中成绩类型，枚举Key
     */
    @ApiModelProperty(value = "高中成绩类型，枚举Key")
    @Column(name = "high_school_test_type")
    private String highSchoolTestType;
    /**
     * 高中成绩
     */
    @ApiModelProperty(value = "高中成绩")
    @Column(name = "high_school_test_score")
    private String highSchoolTestScore;
    /**
     * 本科成绩类型，枚举Key
     */
    @ApiModelProperty(value = "本科成绩类型，枚举Key")
    @Column(name = "standard_test_type")
    private String standardTestType;
    /**
     * 本科成绩
     */
    @ApiModelProperty(value = "本科成绩")
    @Column(name = "standard_test_score")
    private String standardTestScore;
    /**
     * 硕士成绩类型，枚举Key
     */
    @ApiModelProperty(value = "硕士成绩类型，枚举Key")
    @Column(name = "master_test_type")
    private String masterTestType;
    /**
     * 硕士成绩
     */
    @ApiModelProperty(value = "硕士成绩")
    @Column(name = "master_test_score")
    private String masterTestScore;
    /**
     * 英语测试类型
     */
    @ApiModelProperty(value = "英语测试类型")
    @Column(name = "english_test_type")
    private String englishTestType;
    /**
     * 英语测试成绩
     */
    @ApiModelProperty(value = "英语测试成绩")
    @Column(name = "english_test_score")
    private BigDecimal englishTestScore;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 共享路径
     */
    @ApiModelProperty(value = "共享路径")
    @Column(name = "shared_path")
    private String sharedPath;
    /**
     * 学生业务状态(多选)：0转代理学生/1奖学金占学费的60%以上
     */
    @ApiModelProperty(value = "学生业务状态(多选)：0转代理学生/1奖学金占学费的60%以上")
    @Column(name = "condition_type")
    private String conditionType;
    /**
     * 旧数据num(gea)
     */
    @ApiModelProperty(value = "旧数据num(gea)")
    @Column(name = "num_gea")
    private String numGea;
    /**
     * 旧数据num(iae)
     */
    @ApiModelProperty(value = "旧数据num(iae)")
    @Column(name = "num_iae")
    private String numIae;
    /**
     * 旧数据id(issue学生Id)
     */
    @ApiModelProperty(value = "旧数据id(issue学生Id)")
    @Column(name = "id_issue")
    private String idIssue;
    /**
     * 旧数据id(issue学生信息Id)
     */
    @ApiModelProperty(value = "旧数据id(issue学生信息Id)")
    @Column(name = "id_issue_info")
    private String idIssueInfo;
    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    @Column(name = "id_gea")
    private String idGea;
    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    @Column(name = "id_iae")
    private String idIae;
    /**
     * 学生国籍所在国家Id
     */
    @ApiModelProperty(value = "学生国籍所在国家Id")
    @Column(name = "fk_area_country_id_nationality")
    private Long fkAreaCountryIdNationality;
    /**
     * 学生国籍所在国家名称
     */
    @ApiModelProperty(value = "学生国籍所在国家名称")
    @Column(name = "fk_area_country_name_nationality")
    private String fkAreaCountryNameNationality;
    /**
     * 绿卡国家Id
     */
    @ApiModelProperty(value = "绿卡国家Id")
    @Column(name = "fk_area_country_id_green_card")
    private Long fkAreaCountryIdGreenCard;
    /**
     * 护照编号（保险业务必填）
     */
    @ApiModelProperty(value = "护照编号（保险业务必填）")
    @Column(name = "passport_num")
    private String passportNum;

    @ApiModelProperty(value = "护照签发地Id")
    @Column(name = "fk_area_country_id_passport")
    private Long fkAreaCountryIdPassport;

    @ApiModelProperty(value = "收到申请资料时间")
    @Column(name = "received_application_data_date")
    @UpdateWithNull
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date receivedApplicationDataDate;


    @ApiModelProperty(value = "学生资源Id")
    @Column(name = "fk_client_id")
    private Long fkClientId;
}
