<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>biz-service-ap</artifactId>
        <groupId>com.get</groupId>
        <version>1.0.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ais-resume-center-ap</artifactId>
    <name>${project.artifactId}</name>
    <version>${get.project.version}</version>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-cache</artifactId>
            <version>${get.project.version}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/javax.persistence/javax.persistence-api -->
        <dependency>
            <groupId>javax.persistence</groupId>
            <artifactId>javax.persistence-api</artifactId>
            <version>2.2</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-secure</artifactId>
        </dependency>
    </dependencies>

</project>