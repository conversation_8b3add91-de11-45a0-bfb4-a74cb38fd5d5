package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:Oliver
 * @Date: 2025/7/15
 * @Version 1.0
 * @apiNote:微信订单参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WxOrderRequestDto {

    @ApiModelProperty("商户订单号")
    private String outTradeNo;

    @ApiModelProperty("金额-分")
    private Integer amount;

    @ApiModelProperty("用户openid")
    private String openid;

    @ApiModelProperty("订单描述")
    private String description;

}
