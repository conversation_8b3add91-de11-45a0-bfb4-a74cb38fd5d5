package com.get.registrationcenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.registrationcenter.entity.RegistrationTranslation;
import com.get.registrationcenter.dto.TranslationDto;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface TranslationMapper extends BaseMapper<RegistrationTranslation> {
    int insert(RegistrationTranslation record);

    int insertSelective(RegistrationTranslation record);

    String getTranslation(TranslationDto translationDto);
}