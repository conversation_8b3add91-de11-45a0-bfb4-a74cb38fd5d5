package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.financecenter.dto.PaymentMethodTypeDto;
import com.get.financecenter.entity.PaymentMethodType;
import com.get.financecenter.vo.PaymentMethodTypeVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper
public interface PaymentMethodTypeMapper extends GetMapper<PaymentMethodType> {

    List<PaymentMethodTypeVo> getPaymentMethodTypes(IPage<PaymentMethodType> pages,@Param("paymentMethodTypeDto") PaymentMethodTypeDto paymentMethodTypeDto);

    Integer checkName(@Param("typeName") String typeName);

    Integer getMaxViewOrder();

    void updateBatchById(@Param("updateList") List<PaymentMethodType> updateList);

    PaymentMethodType selectByFkAccountingItemId(@Param("fkAccountingItemId") Long id);
}
