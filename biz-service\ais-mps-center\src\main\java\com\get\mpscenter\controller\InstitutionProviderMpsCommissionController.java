package com.get.mpscenter.controller;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.mpscenter.dto.InstitutionProviderMpsCommissionDto;
import com.get.mpscenter.dto.KeyWordMajorLevelDto;
import com.get.mpscenter.dto.MpsCommissionDto;
import com.get.mpscenter.service.InstitutionProviderMpsCommissionService;
import com.get.mpscenter.vo.InstitutionProviderBingVo;
import com.get.mpscenter.vo.InstitutionProviderMpsCommissionVo;
import com.get.mpscenter.vo.KeyWordVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.ibatis.annotations.Param;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "mps佣金管理")
@RestController
@RequestMapping("mps/institutionProviderMpsCommission")
public class InstitutionProviderMpsCommissionController {

    @Resource
    private InstitutionProviderMpsCommissionService institutionProviderMpsCommissionService;


    @ApiOperation(value = "佣金列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.MPSCENTER, type = LoggerOptTypeConst.LIST, description = "佣金中心/mps佣金管理/佣金列表")
    @PostMapping("datas")
    public ResponseBo<InstitutionProviderMpsCommissionVo> datas(@RequestBody SearchBean<InstitutionProviderMpsCommissionDto> page) {
        List<InstitutionProviderMpsCommissionVo> datas = institutionProviderMpsCommissionService.getCommissionList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "新增佣金", notes = "")
    @OperationLogger(module = LoggerModulesConsts.MPSCENTER, type = LoggerOptTypeConst.ADD, description = "佣金中心/mps佣金管理/新增佣金")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(MpsCommissionDto.Add.class) MpsCommissionDto mpsCommissionDto) {
        return SaveResponseBo.ok(institutionProviderMpsCommissionService.add(mpsCommissionDto));
    }

    @ApiOperation(value = "删除佣金", notes = "")
    @OperationLogger(module = LoggerModulesConsts.MPSCENTER, type = LoggerOptTypeConst.DELETE, description = "佣金中心/mps佣金管理/删除佣金")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        institutionProviderMpsCommissionService.delete(id);
        return SaveResponseBo.ok();
    }

    @ApiOperation(value = "新增关键字", notes = "")
    @OperationLogger(module = LoggerModulesConsts.MPSCENTER, type = LoggerOptTypeConst.ADD, description = "佣金中心/mps佣金管理/新增关键字")
    @PostMapping("addKeyWord")
    public ResponseBo addKeyWord(@RequestBody @Validated(KeyWordMajorLevelDto.Add.class) KeyWordMajorLevelDto keyWordMajorLevelDto) {
        institutionProviderMpsCommissionService.addKeyWord(keyWordMajorLevelDto);
        return SaveResponseBo.ok();
    }

    @ApiOperation(value = "提供商学校列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.MPSCENTER, type = LoggerOptTypeConst.LIST, description = "佣金中心/mps佣金管理/提供商学校列表")
    @PostMapping("getProviderInstitutionList")
    public ResponseBo<InstitutionProviderBingVo> getProviderInstitutionList(@Param("fkInstitutionId") Long fkInstitutionId, @Param("fkInstitutionProviderId") Long fkInstitutionProviderId) {
        InstitutionProviderBingVo institutionProviderBingVo = institutionProviderMpsCommissionService.getProviderInstitutionList(fkInstitutionId,fkInstitutionProviderId);
        return new ResponseBo(institutionProviderBingVo);
    }


    @ApiOperation(value = "佣金详情", notes = "详情")
    @OperationLogger(module = LoggerModulesConsts.MPSCENTER, type = LoggerOptTypeConst.DETAIL, description = "佣金中心/mps佣金管理/佣金详情")
    @GetMapping("/{id}")
    public ResponseBo<InstitutionProviderMpsCommissionVo> detail(@PathVariable("id") Long id) {
        InstitutionProviderMpsCommissionVo data = institutionProviderMpsCommissionService.findCommissionById(id);
        return new ResponseBo<>(data);
    }

    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.MPSCENTER, type = LoggerOptTypeConst.EDIT, description = "佣金中心/mps佣金管理/佣金修改")
    @PostMapping("update")
    public ResponseBo<InstitutionProviderMpsCommissionVo> update(@RequestBody @Validated(MpsCommissionDto.Add.class) MpsCommissionDto mpsCommissionDto) {
        return UpdateResponseBo.ok(institutionProviderMpsCommissionService.update(mpsCommissionDto));
    }


    @ApiOperation(value = "关键字列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.MPSCENTER, type = LoggerOptTypeConst.LIST, description = "佣金中心/mps佣金管理/关键字列表数据")
    @PostMapping("getKeyWordList")
    public ResponseBo<KeyWordVo> getKeyWordList() {
        List<KeyWordVo> datas = institutionProviderMpsCommissionService.getKeyWordList();
        return new ListResponseBo<>(datas);
    }

    //年份下拉
    @ApiOperation(value = "年份下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.MPSCENTER, type = LoggerOptTypeConst.LIST, description = "佣金中心/mps佣金管理/年份下拉")
    @VerifyPermission(IsVerify = false)
    @PostMapping("getYearList")
    public ResponseBo<BaseSelectEntity> getYearList() {
        return new ResponseBo(institutionProviderMpsCommissionService.getYearList());
    }

    @ApiOperation(value = "学校提供商下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.MPSCENTER, type = LoggerOptTypeConst.LIST, description = "佣金中心/mps佣金管理/学校提供商下拉")
    @PostMapping("getInstitutionProviderList")
    public ResponseBo<BaseSelectEntity> getInstitutionProviderList(@RequestParam(required = false,value = "fkInstitutionId") Long fkInstitutionId) {
        return new ResponseBo(institutionProviderMpsCommissionService.getInstitutionProviderList(fkInstitutionId));
    }

    @ApiOperation(value = "学校下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.MPSCENTER, type = LoggerOptTypeConst.LIST, description = "佣金中心/mps佣金管理/学校下拉")
    @VerifyPermission(IsVerify = false)
    @PostMapping("getInstitutionList")
    public ResponseBo<BaseSelectEntity> getInstitutionList(@RequestParam(required = false,value = "fkInstitutionProviderId") Long fkInstitutionProviderId) {
        return new ResponseBo(institutionProviderMpsCommissionService.getInstitutionList(fkInstitutionProviderId));
    }


    @ApiOperation(value = "拖拽", notes = "")
    @OperationLogger(module = LoggerModulesConsts.MPSCENTER, type = LoggerOptTypeConst.EDIT, description = "佣金中心/mps佣金管理/排序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestParam("fkProviderMpsCommissionId")Long fkProviderMpsCommissionId,@RequestParam("start")Integer start,@RequestParam("end")Integer end) {
        institutionProviderMpsCommissionService.movingOrder(fkProviderMpsCommissionId,start,end);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "复制佣金", notes = "")
    @OperationLogger(module = LoggerModulesConsts.MPSCENTER, type = LoggerOptTypeConst.ADD, description = "佣金中心/mps佣金管理/复制佣金")
    @PostMapping("copyMpsCommission")
    public ResponseBo<InstitutionProviderMpsCommissionVo> copyMpsCommission(@RequestParam("id") Long id) {
        return new ResponseBo<>(institutionProviderMpsCommissionService.copyMpsCommission(id));
    }

    @ApiOperation(value = "修改激活状态接口", notes = "0停用/1激活")
    @OperationLogger(module = LoggerModulesConsts.MPSCENTER, type = LoggerOptTypeConst.EDIT, description = "佣金中心/mps佣金管理/修改激活状态接口")
    @PostMapping("updateActive")
    public ResponseBo updateActive(@RequestParam("id")Long id, @RequestParam("status")Boolean status) {
        institutionProviderMpsCommissionService.updateActive(id,status);
        return UpdateResponseBo.ok();
    }

    //课程类型下拉
    @ApiOperation(value = "课程模式下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.MPSCENTER, type = LoggerOptTypeConst.LIST, description = "佣金中心/mps佣金管理/课程模式下拉")
    @PostMapping("getProgrammesModeSelect")
    public ResponseBo getProgrammesModeSelect() {
        return new ListResponseBo<>(ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.PROGRAMES_MODE));
    }

    // 沒有激活的佣金数据
    @ApiOperation(value = "未激活佣金列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.MPSCENTER, type = LoggerOptTypeConst.LIST, description = "佣金中心/mps佣金管理/未激活佣金列表")
    @PostMapping("unActiveCommissionList")
    public ResponseBo unActiveCommissionList(@RequestBody SearchBean<MpsCommissionDto> page) {
        List<InstitutionProviderMpsCommissionVo> unActiveCommissionList = institutionProviderMpsCommissionService.getUnActiveCommissionList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo(unActiveCommissionList,p);
    }

}
