package com.get.pmpcenter.service.impl;

import com.get.pmpcenter.entity.InstitutionProviderContract;
import com.get.pmpcenter.mapper.InstitutionProviderContractMapper;
import com.get.pmpcenter.service.MailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.mail.internet.MimeMessage;

/**
 * @Author:Oliver
 * @Date: 2025/3/24
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class MailServiceImpl implements MailService {

    @Autowired
    private InstitutionProviderContractMapper contractMapper;
    @Autowired
    private JavaMailSender javaMailSender;

    @Value("${spring.mail.username:<EMAIL>}")
    private String from;

    @Override
    public void sendContractApprovalMail(Long contractId, String toEmail) {
        InstitutionProviderContract contract = contractMapper.selectById(contractId);
        String emailContent = "您有一条新的合同待审批，请登录系统进行审批。合同标题:" + contract.getContractTitle();
        //todo 模板替换
        try {
            MimeMessage mail = javaMailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mail);

            helper.setTo(toEmail);
            helper.setSubject("合同审批");
            helper.setFrom(from);
            helper.setText(emailContent, true);
            javaMailSender.send(mail);
            log.info("邮件发送成功===》邮件：{}", toEmail);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("邮件发送出现异常：{}", e.getMessage());
        }
    }

    private String getContractApprovalTemplate() {
        return StringUtils.EMPTY;
    }
}
