package com.get.schoolGateCenter.dto;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2020/7/24
 * @TIME: 14:11
 * @Description: 员工业务办公室DTO
 **/
@Data
public class StaffOfficeDto extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
    /**
     * 办公室编号
     */
    @ApiModelProperty(value = "办公室编号")
    private String num;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 是否选中
     */
    @ApiModelProperty(value = "是否选择办公室")
    private Boolean isSelect;

}
