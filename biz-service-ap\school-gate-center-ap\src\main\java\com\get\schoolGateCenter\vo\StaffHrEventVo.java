package com.get.schoolGateCenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2020/7/13
 * @TIME: 10:37
 * @Description:
 **/
@Data
public class StaffHrEventVo extends BaseVoEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id", required = true)
    @NotNull(message = "员工id不能为空", groups = {Add.class, Update.class})
    private Long fkStaffId;

    /**
     * 事件类型：升职/降职/岗位调动/外调/薪资上调/薪资下调/停薪留职/入职/离职/试用期转正/试用期延期/合同变更
     */
    @ApiModelProperty(value = "事件类型：升职/降职/岗位调动/外调/薪资上调/薪资下调/停薪留职/入职/离职/试用期转正/试用期延期/合同变更", required = true)
    @NotBlank(message = "人事事件不能为空", groups = {Add.class, Update.class})
    private String eventType;

    /**
     * 原办公室Id
     */
    @ApiModelProperty(value = "原办公室Id")
    private Long fkOfficeIdFrom;

    /**
     * 原部门Id
     */
    @ApiModelProperty(value = "原部门Id")
    private Long fkDepartmentIdFrom;

    /**
     * 原职位Id
     */
    @ApiModelProperty(value = "原职位Id")
    private Long fkPositionIdFrom;

    /**
     * 目标办公室Id
     */
    @ApiModelProperty(value = "目标办公室Id")
    private Long fkOfficeIdTo;

    /**
     * 目标部门Id
     */
    @ApiModelProperty(value = "目标部门Id")
    private Long fkDepartmentIdTo;

    /**
     * 目标职位Id
     */
    @ApiModelProperty(value = "目标职位Id")
    private Long fkPositionIdTo;

    /**
     * 原基本工资
     */
    @ApiModelProperty(value = "原基本工资")
    private BigDecimal fromSalaryBase;

    /**
     * 原绩效工资
     */
    @ApiModelProperty(value = "原绩效工资")
    private BigDecimal fromSalaryPerformance;

    /**
     * 原岗位津贴
     */
    @ApiModelProperty(value = "原岗位津贴")
    private BigDecimal fromAllowancePosition;

    /**
     * 原餐饮津贴
     */
    @ApiModelProperty(value = "原餐饮津贴")
    private BigDecimal fromAllowanceCatering;

    /**
     * 原交通津贴
     */
    @ApiModelProperty(value = "原交通津贴")
    private BigDecimal fromAllowanceTransportation;

    /**
     * 原通讯津贴
     */
    @ApiModelProperty(value = "原通讯津贴")
    private BigDecimal fromAllowanceTelecom;

    /**
     * 原其他津贴
     */
    @ApiModelProperty(value = "原其他津贴")
    private BigDecimal fromAllowanceOther;

    /**
     * 目标基本工资
     */
    @ApiModelProperty(value = "目标基本工资")
    private BigDecimal toSalaryBase;

    /**
     * 目标绩效工资
     */
    @ApiModelProperty(value = "目标绩效工资")
    private BigDecimal toSalaryPerformance;

    /**
     * 目标岗位津贴
     */
    @ApiModelProperty(value = "目标岗位津贴")
    private BigDecimal toAllowancePosition;

    /**
     * 目标餐饮津贴
     */
    @ApiModelProperty(value = "目标餐饮津贴")
    private BigDecimal toAllowanceCatering;

    /**
     * 目标交通津贴
     */
    @ApiModelProperty(value = "目标交通津贴")
    private BigDecimal toAllowanceTransportation;

    /**
     * 目标通讯津贴
     */
    @ApiModelProperty(value = "目标通讯津贴")
    private BigDecimal toAllowanceTelecom;

    /**
     * 目标其他津贴
     */
    @ApiModelProperty(value = "目标其他津贴")
    private BigDecimal toAllowanceOther;

    /**
     * 生效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "生效日期")
    private Date effectiveDate;

    /**
     * 调整原因
     */
    @ApiModelProperty(value = "调整原因")
    private String reason;

    /**
     * 关键字
     */
    @ApiModelProperty(value = "查询关键字")
    private String keyWord;

    /**
     * 转正时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("转正时间")
    private Date passProbationDate;

}
