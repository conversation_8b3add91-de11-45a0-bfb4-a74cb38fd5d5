package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_agent_event")
public class AgentEvent extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 学生代理Id
     */
    @ApiModelProperty(value = "学生代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 学生代理事件类型Id
     */
    @ApiModelProperty(value = "学生代理事件类型Id")
    @Column(name = "fk_agent_event_type_id")
    private Long fkAgentEventTypeId;
    /**
     * 事件内容
     */
    @ApiModelProperty(value = "事件内容")
    @Column(name = "description")
    private String description;
}