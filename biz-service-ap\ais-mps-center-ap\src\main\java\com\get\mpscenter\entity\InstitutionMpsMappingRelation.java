package com.get.mpscenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import java.io.Serializable;

@TableName("m_institution_mps_mapping_relation")
@Data
public class InstitutionMpsMappingRelation extends BaseEntity implements Serializable {

    @ApiModelProperty("BMS学校提供商Id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty("BMS学校Id")
    private Long fkInstitutionId;

    @ApiModelProperty("对应MPS学校名称")
    private String institutionNameMps;

    @ApiModelProperty("对应MPS学校 Id")
    private Long idGea;

    @ApiModelProperty("同步类型")
    private Integer type;

}
