package com.get.salecenter.dto;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2024/1/16
 * @TIME: 14:15
 * @Description:
 **/
@Data
public class EventPlanRegistrationDto implements Serializable {

    @JsonSerialize(
            using = ToStringSerializer.class
    )
    @ApiModelProperty("主键id")
    @NotNull(
            message = "主键不能为空！",
            groups = {BaseVoEntity.Update.class}
    )
    private Long id;

    @ApiModelProperty(value = "发票建议币种")
    private String fkCurrencyTypeNumInvoice;

    @ApiModelProperty(value = "联系人信息")
    @NotEmpty(message = "联系人信息不能为空")
    private List<EventPlanRegistrationContactPersonFormDto> EventPlanRegistrationContactPersonList;

    public interface Update {
    }

    public interface Add {
    }
}
