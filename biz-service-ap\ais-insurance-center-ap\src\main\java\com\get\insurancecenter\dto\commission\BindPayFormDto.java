package com.get.insurancecenter.dto.commission;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.helpcenter.dto.MediaAndAttachedDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/7/16
 * @Version 1.0
 * @apiNote:绑定付款单Dto
 */
@Data
public class BindPayFormDto {

    @ApiModelProperty("代理id")
    @NotNull(message = "代理id不能为空")
    private Long agentId;

    @ApiModelProperty(value = "代理提交结算批次编号-列表获取")
    @NotBlank(message = "代理提交结算批次编号不能为空")
    private String fkNumOptBatch;

    @ApiModelProperty(value = "代理银行账号ID-列表获取,对应列表的agentContractAccountId")
    @NotNull(message = "代理银行账号ID不能为空")
    private Long fkBankAccountId;

    @ApiModelProperty(value = "银行帐号Id(公司)-付款银行账户ID")
    @NotNull(message = "付款银行账户ID不能为空")
    private Long fkBankAccountIdCompany;

    @ApiModelProperty(value = "付款时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message = "付款时间不能为空")
    private Date paymentDate;

    @ApiModelProperty(value = "付款单编号(凭证号)/绑定支付流水号")
    @NotBlank(message = "绑定支付流水号不能为空")
    private String numBank;

    @ApiModelProperty(value = "摘要")
    private String summary;

    @ApiModelProperty(value = "附件信息")
    private List<MediaAndAttachedDto> mediaAndAttachedDtoList;
}
