package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/3/16 9:51
 */
@Data
public class AplOldIssueOrderDto extends BaseVoEntity  {

    @ApiModelProperty(value = "表id")
    private Integer OrderId;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "项目成员名称")
    private String projectStaffName;

    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

    @ApiModelProperty("代理联系人邮箱")
    private String fkContactPersonEmail;

    @ApiModelProperty("学生邮箱")
    private String fkStudentEmail;

    /**
     * 多选公司Id
     */
    @ApiModelProperty(value = "多选公司Id", required = true)
    private List<Long> fkCompanyIds;


}
