package com.get.rocketmq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.get.core.tool.api.Result;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 系统邮件队列监听
 */
@Component
@Slf4j
//@RocketMQMessageListener(
//        consumeThreadMax = 10,
//        topic = "mail_system_queue_topic", // topic
//        consumerGroup = "mail_system_queue_topic_consumer_group", // 消费组
//        maxReconsumeTimes = 3, //最大重试次数
//        consumeMode = ConsumeMode.ORDERLY
//)
public class SystemMailQueueListener implements RocketMQListener<EmailSystemMQMessageDto> {

    @Resource
    private IReminderCenterClient reminderCenterClient;


    @Override
    public void onMessage(EmailSystemMQMessageDto emailSystemMQMessageDto) {
        try {
            Result result = reminderCenterClient.sendSystemMail(emailSystemMQMessageDto);
            if (!result.isSuccess()) {
                throw new RuntimeException(result.getMessage());
            }
        } catch (Exception e) {
            emailSystemMQMessageDto.setContent(null);
            log.error("邮件发送失败，emailMQMessage={}", JSONObject.toJSONString(emailSystemMQMessageDto));
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

}
