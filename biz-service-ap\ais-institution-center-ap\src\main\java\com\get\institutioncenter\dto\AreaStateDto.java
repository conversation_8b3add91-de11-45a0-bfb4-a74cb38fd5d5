package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/27 12:26
 * @verison: 1.0
 * @description:
 */
@Data
public class AreaStateDto extends BaseVoEntity {
    /**
     * 国家Id
     */
    @NotNull(message = "国家Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "国家Id", required = true)
    private Long fkAreaCountryId;

    /**
     * 州省编号
     */
    @ApiModelProperty(value = "州省编号")
    private String num;

    /**
     * 州省名称
     */
    @NotBlank(message = "州省名称不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "州省名称", required = true)
    private String name;

    /**
     * 州省中文名称
     */
    @NotBlank(message = "州省中文名称不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "州省中文名称")
    private String nameChn;

    /**
     * 备注
     */
    //@NotBlank(message = "备注不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "备注")
    private String remark;

    //自定义内容
    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyWord;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;


    /**
     * 大区Id
     */
    @ApiModelProperty(value = "大区Id")
    private List<Long> fkAreaRegionId;
}
