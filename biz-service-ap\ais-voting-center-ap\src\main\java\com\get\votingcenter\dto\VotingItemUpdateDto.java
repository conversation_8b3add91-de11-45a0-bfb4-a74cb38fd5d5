package com.get.votingcenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/9/24 11:13
 * @verison: 1.0
 * @description:
 */
@Data
public class VotingItemUpdateDto extends BaseVoEntity implements Serializable {
    /**
     * 投票主题Id
     */
    @ApiModelProperty(value = "投票主题Id")
    @NotNull(message = "投票主题Id不能为空", groups = {Add.class, Update.class})
    private Long fkVotingId;

    /**
     * 投票项标题
     */
    @ApiModelProperty(value = "投票项标题")
    @NotBlank(message = "投票项标题不能为空", groups = {Add.class, Update.class})
    private String title;

    /**
     * 投票项描述
     */
    @ApiModelProperty(value = "投票项描述")
    private String description;

    /**
     * 开放时间
     */
    @ApiModelProperty(value = "开放时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 状态（优于时间控制）：0没开始/1进行中/2已结束，只有设置为1=进行中，才按时间控制
     */
    @ApiModelProperty(value = "状态（优于时间控制）：0没开始/1进行中/2已结束，只有设置为1=进行中，才按时间控制")
    @NotNull(message = "投票项状态不能为空", groups = {Add.class, Update.class})
    private Integer status;

    /**
     * 排名称号图片
     */
    @ApiModelProperty(value = "投票项管理图片")
    private List<MediaAndAttachedDto> mediaAttachedVos;
}
