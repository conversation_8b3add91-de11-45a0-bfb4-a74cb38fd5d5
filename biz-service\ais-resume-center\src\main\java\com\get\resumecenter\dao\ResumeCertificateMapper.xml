<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.resumecenter.dao.ResumeCertificateMapper">
  <resultMap id="BaseResultMap" type="com.get.resumecenter.entity.ResumeCertificate">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_resume_id" jdbcType="BIGINT" property="fkResumeId" />
    <result column="get_date" jdbcType="DATE" property="getDate" />
    <result column="certificate" jdbcType="VARCHAR" property="certificate" />
    <result column="score" jdbcType="VARCHAR" property="score" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <sql id="Base_Column_List">
    id, fk_resume_id, get_date, certificate, score, gmt_create, gmt_create_user, gmt_modified, 
    gmt_modified_user
  </sql>
  <insert id="insertSelective" parameterType="com.get.resumecenter.entity.ResumeCertificate" keyProperty="id" useGeneratedKeys="true">
    insert into m_resume_certificate
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkResumeId != null">
        fk_resume_id,
      </if>
      <if test="getDate != null">
        get_date,
      </if>
      <if test="certificate != null">
        certificate,
      </if>
      <if test="score != null">
        score,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkResumeId != null">
        #{fkResumeId,jdbcType=BIGINT},
      </if>
      <if test="getDate != null">
        #{getDate,jdbcType=DATE},
      </if>
      <if test="certificate != null">
        #{certificate,jdbcType=VARCHAR},
      </if>
      <if test="score != null">
        #{score,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>