package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.DataConverter;
import com.get.common.utils.GetStringUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestContextUtil;
import com.get.financecenter.dao.BankAccountMapper;
import com.get.financecenter.dao.InvoiceMapper;
import com.get.financecenter.dao.InvoiceReceivablePlanMapper;
import com.get.financecenter.dao.PayablePlanSettlementInstallmentMapper;
import com.get.financecenter.dao.ReceiptFeeTypeMapper;
import com.get.financecenter.dao.ReceiptFormInvoiceMapper;
import com.get.financecenter.dao.ReceiptFormItemMapper;
import com.get.financecenter.dao.ReceiptFormMapper;
import com.get.financecenter.dto.CommentDto;
import com.get.financecenter.dto.MediaAndAttachedDto;
import com.get.financecenter.dto.ReceiptFormDto;
import com.get.financecenter.dto.ReceiptFormExchangeRateDto;
import com.get.financecenter.dto.ReceiptFormParamDto;
import com.get.financecenter.dto.ReceiptFormPortionDto;
import com.get.financecenter.dto.ServiceFeeReceiptFormDto;
import com.get.financecenter.dto.SettlementInstallmentUpdateDto;
import com.get.financecenter.dto.query.ReceiptFormQueryDto;
import com.get.financecenter.entity.FComment;
import com.get.financecenter.entity.Invoice;
import com.get.financecenter.entity.PayablePlanSettlementInstallment;
import com.get.financecenter.entity.ReceiptForm;
import com.get.financecenter.entity.ReceiptFormInvoice;
import com.get.financecenter.entity.ReceiptFormItem;
import com.get.financecenter.service.AsyncExportService;
import com.get.financecenter.service.HtiAgencyCommissionSettlementService;
import com.get.financecenter.service.IBankAccountService;
import com.get.financecenter.service.ICommentService;
import com.get.financecenter.service.ICurrencyTypeService;
import com.get.financecenter.service.IExchangeRateService;
import com.get.financecenter.service.IInvoiceService;
import com.get.financecenter.service.IMediaAndAttachedService;
import com.get.financecenter.service.IReceiptFormInvoiceService;
import com.get.financecenter.service.IReceiptFormItemService;
import com.get.financecenter.service.IReceiptFormService;
import com.get.financecenter.vo.FCommentVo;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import com.get.financecenter.vo.InvoiceVo;
import com.get.financecenter.vo.ReceiptFormItemVo;
import com.get.financecenter.vo.ReceiptFormVo;
import com.get.institutioncenter.entity.Contract;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.entity.PayablePlan;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.salecenter.vo.ReceivablePlanNewVo;
import com.get.salecenter.vo.ReceivablePlanVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @DATE: 2020/12/22
 * @TIME: 17:04
 * @Description:
 **/
@Service
public class ReceiptFormServiceImpl extends BaseServiceImpl<ReceiptFormMapper, ReceiptForm> implements IReceiptFormService {
    @Resource
    private IInstitutionCenterClient institutionService;
    @Resource
    private ISaleCenterClient feignSaleService;
    @Resource
    private UtilService utilService;
    @Resource
    private ReceiptFormMapper receiptFormMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ICurrencyTypeService currencyTypeService;
    @Resource
    private BankAccountMapper bankAccountMapper;
    @Resource
    private InvoiceReceivablePlanMapper invoiceReceivablePlanMapper;
    @Resource
    private ReceiptFormInvoiceMapper receiptFormInvoiceMapper;
    @Resource
    private InvoiceMapper invoiceMapper;
    @Resource
    private ICommentService commentService;
    @Resource
    private IMediaAndAttachedService attachedService;
    //    @Resource
//    private FeignInstitutionService feignInstitutionService;
    @Resource
    private IPermissionCenterClient feignPermissionService;
    @Resource
    private IBankAccountService bankAccountService;
    @Resource
    private IReceiptFormInvoiceService receiptFormInvoiceService;
    @Resource
    private IInvoiceService invoiceService;
    @Resource
    private ReceiptFormItemMapper receiptFormItemMapper;
    @Resource
    private ReceiptFeeTypeMapper receiptFeeTypeMapper;
    @Resource
    private ISaleCenterClient saleCenterClient;
    @Resource
    private HtiAgencyCommissionSettlementService htiAgencyCommissionSettlementService;
    @Resource
    private IReceiptFormItemService receiptFormItemService;
    @Resource
    private AsyncExportService asyncExportService;
    @Resource
    private IExchangeRateService exchangeRateService;
    @Resource
    private PayablePlanSettlementInstallmentMapper payablePlanSettlementInstallmentMapper;

    @Override
    public List<ReceiptFormVo> getReceiptForms(ReceiptFormQueryDto receiptFormVo, Page page) {
//        LambdaQueryWrapper<ReceiptForm> wrapper = new LambdaQueryWrapper<>();
//        List<Long> targetId = new ArrayList<>();
//        if (GeneralTool.isNotEmpty(receiptFormVo)) {
//            if (GeneralTool.isNotEmpty(receiptFormVo.getFkCompanyId())) {
//                if (!SecureUtil.validateCompany(receiptFormVo.getFkCompanyId())) {
//                    throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
//                }
//                wrapper.eq(ReceiptForm::getFkCompanyId, receiptFormVo.getFkCompanyId());
//            }
//            if (GeneralTool.isNotEmpty(receiptFormVo.getSettlementStatus())) {
//                wrapper.eq(ReceiptForm::getSettlementStatus, receiptFormVo.getSettlementStatus());
//            }
//            //查询条件-公司ids
//            if (GeneralTool.isNotEmpty(receiptFormVo.getFkCompanyIds())) {
//                if (GeneralTool.isNotEmpty(page)&&!SecureUtil.validateCompanys(receiptFormVo.getFkCompanyIds())) {
//                    throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
//                }
//                wrapper.in(ReceiptForm::getFkCompanyId, receiptFormVo.getFkCompanyIds());
//            }
//            String typeKey = receiptFormVo.getFkTypeKey();
//            if (GeneralTool.isNotEmpty(typeKey)) {
//                wrapper.eq(ReceiptForm::getFkTypeKey, typeKey);
//            }
//            if (GeneralTool.isNotEmpty(receiptFormVo.getFkTypeTargetId())) {
//                wrapper.eq(ReceiptForm::getFkTypeTargetId, receiptFormVo.getFkTypeTargetId());
//            }
//            if (GeneralTool.isNotEmpty(receiptFormVo.getStartTime())) {
//                wrapper.ge(ReceiptForm::getGmtCreate, receiptFormVo.getStartTime());
//            }
//            if (GeneralTool.isNotEmpty(receiptFormVo.getEndTime())) {
//                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//                try {
//                    Date gmtTime = sdf.parse(dateFormat(receiptFormVo.getEndTime()));
//                    wrapper.le(ReceiptForm::getGmtCreate, gmtTime);
//                } catch (Exception e) {
//                    throw new GetServiceException("日期格式转换错误");
//                }
//            }
//            if (GeneralTool.isNotEmpty(receiptFormVo.getKeyWord())) {
//                wrapper.and(wrapper_ ->
//                        wrapper_.like(ReceiptForm::getNumSystem, receiptFormVo.getKeyWord()).or()
//                                .like(ReceiptForm::getNumBank, receiptFormVo.getKeyWord()));
//            }
//            if (GeneralTool.isNotEmpty(receiptFormVo.getAmountStart())) {
//                wrapper.apply("amount + service_fee >= {0}", receiptFormVo.getAmountStart());
//            }
//            if (GeneralTool.isNotEmpty(receiptFormVo.getAmountEnd())) {
//                wrapper.apply("amount + service_fee <= {0}", receiptFormVo.getAmountEnd());
//            }
//        }
//        wrapper.orderByDesc(ReceiptForm::getStatus).last(",CASE WHEN num_bank = 'HKR20220425' THEN 0 ELSE 1 END DESC,gmt_create DESC");
//        //获取分页数据
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        List<ReceiptForm> ct = Lists.newArrayList();
        if (GeneralTool.isNotEmpty(page)){
            IPage<ReceivablePlanNewVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            ct = receiptFormMapper.getReceiptForms(receiptFormVo,iPage);
            page.setAll((int) iPage.getTotal());
//            IPage<ReceiptForm> pages = receiptFormMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
//            page.setAll((int) pages.getTotal());
//            ct = pages.getRecords();
        }else {
            ct = receiptFormMapper.getReceiptForms(receiptFormVo,null);
        }
        if (GeneralTool.isEmpty(ct)) {
            return new ArrayList<>();
        }
//        PageInfo<ReceiptForm> pageInfo = new PageInfo<ReceiptForm>(ct);
//        page.setTotalResult(new Long(pageInfo.getTotal()).intValue());
        //List<ReceiptFormVo> convertDatas = new ArrayList<>();

        //收款单ids
        Set<Long> fkReceiptFormIds = ct.stream().map(ReceiptForm::getId).collect(Collectors.toSet());
        List<ReceiptFormVo> convertDatas = receiptFormMapper.getReceiptFormListByBindingStatus(receiptFormVo.getBindingStatus(), fkReceiptFormIds);

        //公司ids
        Set<Long> companyIds = ct.stream().map(ReceiptForm::getFkCompanyId).collect(Collectors.toSet());
        //根据公司ids获取名称map
        Map<Long, String> companyNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(companyIds)) {
            Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(companyIds);
            if (result.isSuccess() && result.getData() != null) {
                companyNamesByIds = result.getData();
            }
        }

        //对应记录ids
        Set<Long> fkTypeTargetIds = ct.stream().map(ReceiptForm::getFkTypeTargetId).collect(Collectors.toSet());
        fkTypeTargetIds.removeIf(Objects::isNull);
        if (GeneralTool.isEmpty(fkTypeTargetIds)) {
            fkTypeTargetIds.add(0L);
        }
        //根据提供商ids获取名称map
        Map<Long, String> institutionProviderNamesByIds = new HashMap<>();
        //根据渠道ids获取名称map
        Map<Long, String> channelNamesByIds = new HashMap<>();
        //根据学生ids获取名称map
        Map<Long, String> studentNameByIds = new HashMap<>();
        //学校渠道名称ids
        Map<Long, String> institutionChannelNamesByIds = new HashMap<>();

        Map<Long, String> businessProviderByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkTypeTargetIds)) {
            //111111
            Result<Map<Long, String>> resultProviderNamesByIds = institutionService.getInstitutionProviderNamesByIds(fkTypeTargetIds);
            if (resultProviderNamesByIds.isSuccess()) {
                institutionProviderNamesByIds = resultProviderNamesByIds.getData();
            }
            //获取留学住宿提供商名称集合
            Result<Map<Long, String>> accommodationProvider = feignSaleService.getAStudyAccommodationProvider(fkTypeTargetIds);
            if (accommodationProvider.isSuccess() && accommodationProvider.getData() != null) {
                businessProviderByIds = accommodationProvider.getData();
            }

            if (GeneralTool.isNotEmpty(fkTypeTargetIds)) {
                channelNamesByIds = feignSaleService.getChannelNamesByIds(fkTypeTargetIds).getData();
                studentNameByIds = saleCenterClient.getStudentNameByIds(fkTypeTargetIds).getData();
            }
            if (GeneralTool.isNotEmpty(fkTypeTargetIds)) {
                institutionChannelNamesByIds = institutionService.getChannelByIds(fkTypeTargetIds).getData();
            }
            //这里待确定
//            Result<Map<Long, String>> resultNameByIds = feignSaleService.getStudentNameByIds(fkTypeTargetIds);
//            if (resultNameByIds.isSuccess()) {
//                studentNameByIds = resultNameByIds.getData();
//            }
        }

        //币种编号nums
        Set<String> fkCurrencyTypeNums = ct.stream().map(ReceiptForm::getFkCurrencyTypeNum).collect(Collectors.toSet());
        //根据币种编号nums查找币种名称map
        Map<String, String> currencyNamesByNums = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkCurrencyTypeNums)) {
            currencyNamesByNums = currencyTypeService.getCurrencyNamesByNums(fkCurrencyTypeNums);
        }

        //银行账户ids
        Set<Long> fkBankAccountIds = ct.stream().map(ReceiptForm::getFkBankAccountId).collect(Collectors.toSet());
        //根据银行账户ids获取名称map
        Map<Long, String> bankAccountNameByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkBankAccountIds)) {
            bankAccountNameByIds = bankAccountService.getBankAccountNameByIds(fkBankAccountIds);
        }

        //收款单ids
        //Set<Long> fkReceiptFormIds = ct.stream().map(ReceiptForm::getId).collect(Collectors.toSet());
        //根据收款单ids获取发票map
        Map<Long, Set<Long>> invoiceByFkReceiptFormIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkReceiptFormIds)) {
            invoiceByFkReceiptFormIds = receiptFormInvoiceService.getInvoiceByFkReceiptFormIds(fkReceiptFormIds);
        }

        //获取所有的发票编号map
        Map<Long, String> allNums = invoiceService.getAllNums();

        for (ReceiptFormVo dto : convertDatas) {
            if (GeneralTool.isNotEmpty(dto.getFkCompanyId())) {
                String name = companyNamesByIds.get(dto.getFkCompanyId());
                dto.setFkCompanyName(name);
            }
            if (GeneralTool.isNotEmpty(dto.getFkTypeKey())) {
                if (GeneralTool.isEmpty(page)){
                    dto.setFkTypeKeyName(ProjectKeyEnum.getInitialValue(dto.getFkTypeKey()));
                }else {
                    dto.setFkTypeKeyName(ProjectKeyEnum.getValue(dto.getFkTypeKey()));
                }
            }
            Long tId = dto.getFkTypeTargetId();
            String key = dto.getFkTypeKey();
            //设置收款单目标对象名称
            if (GeneralTool.isNotEmpty(tId)) {
                String targetName = null;
                if (TableEnum.INSTITUTION_PROVIDER.key.equals(key)) {
                    targetName = institutionProviderNamesByIds.get(tId);
                }else if (TableEnum.BUSINESS_CHANNEL_INS.key.equals(key)
                        || TableEnum.BUSINESS_CHANNEL_ACC.key.equals(key)) {
                    targetName = channelNamesByIds.get(tId);
                }else if (ProjectKeyEnum.INSTITUTION_PROVIDER_CHANNEL.key.equals(key)) {
                    targetName = institutionChannelNamesByIds.get(tId);
                }else if (ProjectKeyEnum.BUSINESS_PROVIDER.key.equals(key)|| ProjectKeyEnum.BUSINESS_PROVIDER_INS.key.equals(key)){
                    targetName = businessProviderByIds.get(tId);
                }else if(ProjectKeyEnum.M_STUDENT.key.equals(key)){
                    targetName = studentNameByIds.get(tId);
                }
                dto.setTargetName(targetName);
            }
            if (GeneralTool.isNotEmpty(dto.getFkCurrencyTypeNum())) {
                dto.setFkCurrencyTypeName(currencyNamesByNums.get(dto.getFkCurrencyTypeNum()));
            }
            if (GeneralTool.isNotEmpty(dto.getFkBankAccountId())) {
                dto.setFkBankAccountName(bankAccountNameByIds.get(dto.getFkBankAccountId()));
            }
            Set<Long> ids = invoiceByFkReceiptFormIds.get(dto.getId());
            List<Map<String,Object>> invoiceList = new ArrayList<>();
            StringBuilder builder = new StringBuilder();
            if (GeneralTool.isNotEmpty(ids)) {
                for (Long id : ids) {
                    if (GeneralTool.isNotEmpty(allNums.get(id))) {
                        builder.append(allNums.get(id)).append("，");
                        Map<String,Object> map = new HashMap<>();
                        map.put("id", id);
                        map.put("num", allNums.get(id));
                        invoiceList.add(map);
                    }
                }
            }
            dto.setInvoiceName(sub(builder));
            dto.setInvoiceList(invoiceList);
        }
        return convertDatas;
    }

    /**
     * @Description: 导出收款大单列表
     * @Author: Jerry
     * @Date:16:14 2021/12/20
     */
    @Override
    public void exportReceiptFormExcel(HttpServletResponse response, ReceiptFormQueryDto receiptFormVo) {
        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
        com.get.core.secure.UserInfo user = GetAuthInfo.getUser();
        String locale = SecureUtil.getLocale();
        asyncExportService.exportReceiptFormExcel(receiptFormVo,headerMap,user,locale);
    }


    /**
     * Author Cream
     * Description : //修改收款单结算状态
     * Date 2023/4/27 11:15
     * Params:
     * Return
     */
    @Override
    public SaveResponseBo updateSettlementStatus(Long receiptFormId) {
        if (Objects.isNull(receiptFormId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("SELECT_RECEIPT_FORM_TO_MODIFY"));
        }

        ReceiptForm receiptForm = receiptFormMapper.selectById(receiptFormId);
        if (Objects.isNull(receiptForm)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_false"));
        }
        receiptForm.setSettlementStatus(1);
        receiptFormMapper.updateById(receiptForm);
        //激活佣金
        List<ReceiptFormItem> receiptFormItems = receiptFormItemMapper.selectList(Wrappers.<ReceiptFormItem>lambdaQuery().eq(ReceiptFormItem::getFkReceiptFormId, receiptFormId));
        Set<Long> receiptFormItemIds = receiptFormItems.stream().map(ReceiptFormItem::getId).collect(Collectors.toSet());
        receiptFormItemService.activateCommissionSettlement(receiptFormItemIds);
        return SaveResponseBo.ok(receiptFormId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Long addReceiptForm(ReceiptFormDto receiptFormDto) {
        ReceiptForm receiptForm = BeanCopyUtils.objClone(receiptFormDto, ReceiptForm::new);
        utilService.updateUserInfoToEntity(receiptForm);
        //设置汇率调整金额
        if (GeneralTool.isEmpty(receiptFormDto.getAmountExchangeRate())) {
            receiptForm.setAmountExchangeRate(BigDecimal.ZERO.setScale(2,RoundingMode.UP));
        }
        receiptFormMapper.insert(receiptForm);
        ReceiptForm rf = receiptFormMapper.selectById(receiptForm.getId());
        if (GeneralTool.isNotEmpty(rf)) {
            receiptForm.setNumSystem(GetStringUtils.getReceiptFormNum(rf.getId()));
        }
        receiptForm.setStatus(1);
        receiptFormMapper.updateById(receiptForm);
        //如果应收计划id不为空，则 将新增的收款单绑定这个应收计划
        if (GeneralTool.isNotEmpty(receiptFormDto.getReceivablePlanId())) {
            ReceiptFormItem receiptFormItem = new ReceiptFormItem();
            receiptFormItem.setFkReceiptFormId(receiptForm.getId());
            receiptFormItem.setFkReceivablePlanId(receiptFormDto.getReceivablePlanId());
            receiptFormItem.setAmountReceipt(receiptForm.getAmount());
            receiptFormItem.setExchangeRateReceivable(new BigDecimal(1));
            receiptFormItem.setAmountReceivable(receiptForm.getAmount());
            receiptFormItem.setAmountExchangeRate(new BigDecimal(0));
            receiptFormItem.setExchangeRateHkd(receiptForm.getExchangeRateHkd());
            receiptFormItem.setAmountHkd(receiptForm.getAmountHkd());
            receiptFormItem.setExchangeRateRmb(receiptForm.getExchangeRateRmb());
            receiptFormItem.setAmountRmb(receiptForm.getAmountRmb());
            receiptFormItem.setServiceFee(receiptFormDto.getServiceFee());
            utilService.updateUserInfoToEntity(receiptFormItem);
            receiptFormItemMapper.insert(receiptFormItem);
            //根据应收计划id获取对应的应付计划信息
            Result<PayablePlan> payablePlanResult = saleCenterClient.getPayablePlanByReceivablePlanId(receiptFormItem.getFkReceivablePlanId());
            if (!payablePlanResult.isSuccess()) {
                throw new GetServiceException(payablePlanResult.getMessage());
            }
            PayablePlan payablePlan = payablePlanResult.getData();
            SettlementInstallmentUpdateDto settlementInstallmentUpdateDto = new SettlementInstallmentUpdateDto();
            settlementInstallmentUpdateDto.setFkCompanyId(payablePlan.getFkCompanyId());
            //todo 要对冲吗？
//            settlementInstallmentUpdateDto.setHedgeAmount(hedgeAmount);
            settlementInstallmentUpdateDto.setFkPayablePlanId(payablePlan.getId());
            ReceivablePlanVo receivablePlanVo = saleCenterClient.getReceivablePlanById(payablePlan.getFkReceivablePlanId()).getData();
            settlementInstallmentUpdateDto.setFkReceivablePlanId(receivablePlanVo.getId());
            settlementInstallmentUpdateDto.setReceivableAmount(receivablePlanVo.getReceivableAmount());
            settlementInstallmentUpdateDto.setPayableAmount(payablePlan.getPayableAmount());
            settlementInstallmentUpdateDto.setReceivableCurrencyTypeNum(receivablePlanVo.getFkCurrencyTypeNum());
            settlementInstallmentUpdateDto.setPayableCurrencyTypeNum(payablePlan.getFkCurrencyTypeNum());
            ReceiptFormItemVo receiptFormItemVo = BeanCopyUtils.objClone(receiptFormItem, ReceiptFormItemVo::new);
            receiptFormItemVo.setFkCurrencyTypeNum(receiptForm.getFkCurrencyTypeNum());
            htiAgencyCommissionSettlementService.insertSettlementInstallment(settlementInstallmentUpdateDto, receiptFormItemVo);
        }

        //批量绑定发票
        if (GeneralTool.isNotEmpty(receiptFormDto.getFkInvoiceIdList())) {
            for (Long id : receiptFormDto.getFkInvoiceIdList()) {
                ReceiptFormInvoice receiptFormInvoice = new ReceiptFormInvoice();
                receiptFormInvoice.setFkInvoiceId(id);
                receiptFormInvoice.setFkReceiptFormId(receiptForm.getId());
                utilService.updateUserInfoToEntity(receiptFormInvoice);
                receiptFormInvoiceMapper.insertSelective(receiptFormInvoice);
            }
            //同步发票实收时间
            updateReceiptDate(receiptForm.getReceiptDate(),receiptForm.getFkCompanyId(), receiptFormDto.getFkInvoiceIdList());
        }
        return receiptForm.getId();
    }

    /**
     * Author Cream
     * Description : ////同步发票实收时间
     * Date 2023/8/4 11:51
     * Params:
     * Return
     */
    private void updateReceiptDate(Date receiptDate,Long fkCompanyId,List<Long> fkInvoiceIdList){
        if (GeneralTool.isNotEmpty(receiptDate)) {
            Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.RECEIPT_FORM_RECEIPT_DATE_TO_INVOICE.key, 1).getData();
            String configValue1 = companyConfigMap.get(fkCompanyId);
            if (configValue1.equals("1")) {
                //更新发票实收时间
                invoiceService.syncToInvoiceReceiptDate(fkInvoiceIdList,receiptDate);
            }
//            ConfigVo c2 = permissionCenterClient.getConfigByKey(ProjectKeyEnum.RECEIPT_FORM_RECEIPT_DATE_TO_INVOICE.key).getData();
//            String value = c2.getValue1();
//            if (StringUtils.isNotBlank(value)) {
//                SwitchConfigEntity switchConfigEntity = JSONObject.parseObject(JSON.toJSONString(JSON.parse(value)), SwitchConfigEntity.class);
//                if (fkCompanyId == 3) {
//                    if (switchConfigEntity.isIae()) {
//                        //更新发票实收时间
//                        invoiceService.syncToInvoiceReceiptDate(fkInvoiceIdList,receiptDate);
//                    }
//                } else {
//                    if (switchConfigEntity.isOther()) {
//                        //更新发票实收时间
//                        invoiceService.syncToInvoiceReceiptDate(fkInvoiceIdList,receiptDate);
//                    }
//                }
//            }
        }
    }

    /**
     * Author Cream
     * Description : //获取收款单实收汇率
     * Date 2022/11/7 12:41
     * Params:
     * Return
     */
    @Override
    public ResponseBo<BigDecimal> obtainThePaidInExchangeRate(ReceiptFormExchangeRateDto receiptFormExchangeRateDto) {
        if (GeneralTool.isNotEmpty(receiptFormExchangeRateDto.getFkInvoiceIdList())) {
            List<Invoice> invoices = invoiceMapper.selectList(Wrappers.<Invoice>lambdaQuery().in(Invoice::getId, receiptFormExchangeRateDto.getFkInvoiceIdList()));
            Map<String, List<Invoice>> collect = invoices.stream().filter(f->f.getFkCurrencyTypeNum()!=null).collect(Collectors.groupingBy(Invoice::getFkCurrencyTypeNum));
            if (collect.size() == 1) {
                if (receiptFormExchangeRateDto.getFkCurrencyTypeNum().equals(invoices.get(0).getFkCurrencyTypeNum())) {
                    return new ResponseBo<>(BigDecimal.valueOf(1.000000).setScale(6, RoundingMode.UP));
                }
            }
        }
        return new ResponseBo<>(null);
    }

    @Override
    public ReceiptFormVo findReceiptFormById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ReceiptForm receiptForm = receiptFormMapper.selectById(id);
        if (GeneralTool.isEmpty(receiptForm)) {
            return null;
        }
        ReceiptFormVo dto = BeanCopyUtils.objClone(receiptForm,ReceiptFormVo::new);
        if (GeneralTool.isNotEmpty(dto.getFkCompanyId())) {
            Result<String> result = permissionCenterClient.getCompanyNameById(dto.getFkCompanyId());
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                dto.setFkCompanyName(result.getData());
            }
        }
        if (GeneralTool.isNotEmpty(dto.getFkTypeKey())) {
            dto.setFkTypeKeyName(ProjectKeyEnum.getValue(dto.getFkTypeKey()));
        }
        Set<Long> fkTypeTargetIds = new HashSet<Long>();
        fkTypeTargetIds.add(dto.getFkTypeTargetId());
        //根据渠道ids获取名称map
        Map<Long, String> channelNamesByIds = new HashMap<>();
        Map<Long, String> institutionProviderNamesByIds = new HashMap<>();
        Map<Long, String> institutionChannelNamesByIds = new HashMap<>();
        Map<Long, String> businessProviderByIds = new HashMap<>();
        //根据学生ids获取名称map
        Map<Long, String> studentNameByIds = new HashMap<>();
        // 留学保险提供商
        Map<Long, String> insuranceProviderNameMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkTypeTargetIds)) {
            //获取渠道名称
            Result<Map<Long, String>> namesByIds = feignSaleService.getChannelNamesByIds(fkTypeTargetIds);
            if (namesByIds.isSuccess()) {
                channelNamesByIds = namesByIds.getData();
            }
            //111111
            Result<Map<Long, String>> resultProviderNamesByIds = institutionService.getInstitutionProviderNamesByIds(fkTypeTargetIds);
            if (resultProviderNamesByIds.isSuccess()) {
                institutionProviderNamesByIds = resultProviderNamesByIds.getData();
            }
            Result<Map<Long, String>> channelByIds = institutionService.getChannelByIds(fkTypeTargetIds);
            if (channelByIds.isSuccess()) {
                institutionChannelNamesByIds = channelByIds.getData();
            }
            //获取留学住宿提供商名称集合
            Result<Map<Long, String>> accommodationProvider = feignSaleService.getAStudyAccommodationProvider(fkTypeTargetIds);
            if (accommodationProvider.isSuccess() && accommodationProvider.getData() != null) {
                businessProviderByIds = accommodationProvider.getData();
            }
            //获取学生名称
            studentNameByIds = saleCenterClient.getStudentNameByIds(fkTypeTargetIds).getData();
            Result<Map<Long, String>> result = saleCenterClient.getInsuranceProviderNameByIds(fkTypeTargetIds);
            if (result.isSuccess() && result.getData() != null) {
                insuranceProviderNameMap = result.getData();
            }
        }
        //设置目标对象名称
        if (GeneralTool.isNotEmpty(fkTypeTargetIds)) {
            String targetName = null;
            if (TableEnum.INSTITUTION_PROVIDER.key.equals(receiptForm.getFkTypeKey())) {
                targetName = institutionProviderNamesByIds.get(dto.getFkTypeTargetId());
            } else if (TableEnum.BUSINESS_CHANNEL_INS.key.equals(dto.getFkTypeKey())
                    || TableEnum.BUSINESS_CHANNEL_ACC.key.equals(dto.getFkTypeKey())) {
                targetName = channelNamesByIds.get(dto.getFkTypeTargetId());
            } else if (ProjectKeyEnum.INSTITUTION_PROVIDER_CHANNEL.key.equals(dto.getFkTypeKey())) {
                targetName = institutionChannelNamesByIds.get(dto.getFkTypeTargetId());
            }else if (ProjectKeyEnum.BUSINESS_PROVIDER.key.equals(receiptForm.getFkTypeKey())){
                targetName = businessProviderByIds.get(dto.getFkTypeTargetId());
            }else if(ProjectKeyEnum.M_STUDENT.key.equals(receiptForm.getFkTypeKey())){
                targetName = studentNameByIds.get(dto.getFkTypeTargetId());
            }else if(ProjectKeyEnum.BUSINESS_PROVIDER_INS.key.equals(receiptForm.getFkTypeKey())){
                targetName = insuranceProviderNameMap.get(dto.getFkTypeTargetId());
            }
            dto.setTargetName(targetName);
        }
        if (GeneralTool.isNotEmpty(dto.getFkCurrencyTypeNum())) {
            dto.setFkCurrencyTypeName(currencyTypeService.getCurrencyNameByNum(dto.getFkCurrencyTypeNum()));
        }
        if (GeneralTool.isNotEmpty(dto.getFkBankAccountId())) {
            dto.setFkBankAccountName(bankAccountMapper.getBankAccountNameById(dto.getFkBankAccountId()));
        }


        //发票列表
        List<Long> fkInvoiceIds = receiptFormInvoiceService.getInvoiceByFormId(receiptForm.getId());
        //获取所有的发票编号map
        List<Invoice> invoiceByIds = invoiceService.getInvoiceByIds(fkInvoiceIds);
        if (GeneralTool.isNotEmpty(invoiceByIds)) {
            Map<Long, List<Invoice>> collect = invoiceByIds.stream().collect(Collectors.groupingBy(Invoice::getId));
            List<Map<String,Object>> invoiceList = new ArrayList<>();
            for (Long fkInvoiceId : fkInvoiceIds) {
                if (GeneralTool.isNotEmpty(collect.get(fkInvoiceId))) {
                    Map<String,Object> map = new HashMap<>();
                    map.put("id", fkInvoiceId);
                    map.put("num", collect.containsKey(fkInvoiceId)?collect.get(fkInvoiceId).get(0).getNum():null);
                    map.put("currencyNum",collect.containsKey(fkInvoiceId)?collect.get(fkInvoiceId).get(0).getFkCurrencyTypeNum():null);
                    invoiceList.add(map);
                }
            }
            dto.setInvoiceList(invoiceList);
        }
        List<ReceiptFormItem> receiptFormItems = receiptFormItemMapper.selectList(Wrappers.<ReceiptFormItem>query().lambda().eq(ReceiptFormItem::getFkReceiptFormId, id));
        //收款金额（拆分/总）
        BigDecimal amountReceivable = BigDecimal.ZERO;
        BigDecimal itemServiceFee = BigDecimal.ZERO;
        for (ReceiptFormItem receiptFormItem : receiptFormItems) {
            amountReceivable = amountReceivable.add(receiptFormItem.getAmountReceipt()).add(receiptFormItem.getAmountExchangeRate());
            itemServiceFee = itemServiceFee.add(receiptFormItem.getServiceFee());
        }

        dto.setAmountReceipt(amountReceivable);
        dto.setItemServiceFee(itemServiceFee);
        BigDecimal totalBindingAmount = amountReceivable.add(itemServiceFee).setScale(2, RoundingMode.UP);
        dto.setTotalBindingAmount(totalBindingAmount);
        dto.setDiffAmount(dto.getAmount().add(dto.getServiceFee().add(DataConverter.bigDecimalNullConvert(dto.getAmountExchangeRate()))).subtract(totalBindingAmount).setScale(2, RoundingMode.UP));

        return dto;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateStatus(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ReceiptForm receiptForm = receiptFormMapper.selectById(id);
        if (GeneralTool.isEmpty(receiptForm)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }

        //删除收款单和发票关系表
        receiptFormInvoiceMapper.delete(Wrappers.lambdaQuery(ReceiptFormInvoice.class).eq(ReceiptFormInvoice::getFkReceiptFormId, id));

        List<ReceiptFormItem> receiptFormItems = receiptFormItemMapper.selectList(Wrappers.<ReceiptFormItem>lambdaQuery().eq(ReceiptFormItem::getFkReceiptFormId, id));

        //删除和FormItem的关系表
        receiptFormItemMapper.delete(Wrappers.lambdaQuery(ReceiptFormItem.class).eq(ReceiptFormItem::getFkReceiptFormId, id));

        receiptForm.setStatus(0);
        receiptFormMapper.updateById(receiptForm);

        if (GeneralTool.isNotEmpty(receiptFormItems)) {
            Set<Long> receiptFormItemSet = receiptFormItems.stream().map(ReceiptFormItem::getId).collect(Collectors.toSet());
            //检查收款单子项是否处于结算中或结算完成 true:没有处于结算中的，可以编辑 并删除批次表数据
            LambdaQueryWrapper<PayablePlanSettlementInstallment> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            List<Integer> status = new ArrayList<>();
            status.add(ProjectExtraEnum.INSTALLMENT_PROCESSING.key);
            status.add(ProjectExtraEnum.INSTALLMENT_COMPLETE.key);
            lambdaQueryWrapper.in(PayablePlanSettlementInstallment::getFkReceiptFormItemId, receiptFormItemSet)
                    .in(PayablePlanSettlementInstallment::getStatus, status);
            List<PayablePlanSettlementInstallment> payablePlanSettlementInstallments = payablePlanSettlementInstallmentMapper.selectList(lambdaQueryWrapper);
            boolean flag = GeneralTool.isEmpty(payablePlanSettlementInstallments);
            if (flag) {
                payablePlanSettlementInstallmentMapper.delete(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery().in(PayablePlanSettlementInstallment::getFkReceiptFormItemId, receiptFormItemSet)
                        .eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_UNTREATED.key));
            } else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("sale_settlement_in_progress"));
            }

            //删除分期数据
            payablePlanSettlementInstallmentMapper.delete(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                    .in(PayablePlanSettlementInstallment::getFkReceiptFormItemId, receiptFormItemSet)
                    .eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_UNTREATED.key));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReceiptFormVo updateReceiptForm(ReceiptFormDto receiptFormDto) {
        if (GeneralTool.isEmpty(receiptFormDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ReceiptForm receiptForm = receiptFormMapper.selectById(receiptFormDto.getId());
        if (GeneralTool.isEmpty(receiptForm)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //是否激活佣金结算 true:激活佣金
        boolean commissionActivationFlag = receiptForm.getSettlementStatus() == 0 && receiptFormDto.getSettlementStatus() == 1;
        //校验收款单金额是否小于所绑定的子单


        //绑定收款单
        List<ReceiptFormItem> receiptFormItems = receiptFormItemMapper.selectList(Wrappers.<ReceiptFormItem>query().lambda()
                .eq(ReceiptFormItem::getFkReceiptFormId, receiptFormDto.getId()));

//            List<ReceiptFormItem> receiptFormItems = receiptFormItemMapper.selectList(Wrappers.<ReceiptFormItem>query().lambda()
//                    .eq(ReceiptFormItem::getFkReceiptFormId, receiptFormVo.getId()));
            BigDecimal totalAmount = BigDecimal.ZERO;
            if (GeneralTool.isNotEmpty(receiptFormItems)) {
                for (ReceiptFormItem receiptFormItem : receiptFormItems) {
                    totalAmount = totalAmount.add(receiptFormItem.getAmountReceipt());
                }
                // 收款单，提交时会和绑定实收总金额做比较，汇率有差额，取消比较限制
//                if (receiptFormVo.getAmount().compareTo(totalAmount) < 0) {
//                    throw new GetServiceException(LocaleMessageUtils.getMessage("receipt_form_amount_less_binding_amount"));
//                }
            }

        BeanCopyUtils.copyProperties(receiptFormDto, receiptForm);
        utilService.updateUserInfoToEntity(receiptForm);
        receiptFormMapper.updateByIdWithNull(receiptForm);

        //绑定多张发票
        if (GeneralTool.isNotEmpty(receiptFormDto.getFkInvoiceIdList())) {
            if (receiptFormDto.getExchangeRate().compareTo(BigDecimal.valueOf(1))==0){
                List<Invoice> invoices = invoiceMapper.selectList(Wrappers.<Invoice>lambdaQuery().in(Invoice::getId, receiptFormDto.getFkInvoiceIdList()));
                if (invoices.stream().anyMatch(i->!i.getFkCurrencyTypeNum().equals(receiptFormDto.getFkCurrencyTypeNum()))) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("CURRENCY_MISMATCH_REAL_EXCHANGE_RATE_NOT_ONE"));
                }
            }
            //删除原有绑定
            receiptFormInvoiceMapper.deleteByReceiptFormId(receiptForm.getId());
            //新增绑定
            for (Long fkInvoiceId : receiptFormDto.getFkInvoiceIdList()) {
                ReceiptFormInvoice receiptFormInvoice = new ReceiptFormInvoice();
                receiptFormInvoice.setFkInvoiceId(fkInvoiceId);
                receiptFormInvoice.setFkReceiptFormId(receiptForm.getId());
                utilService.updateUserInfoToEntity(receiptFormInvoice);
                receiptFormInvoiceMapper.insertSelective(receiptFormInvoice);
            }
            //同步发票实收时间
            updateReceiptDate(receiptForm.getReceiptDate(),receiptForm.getFkCompanyId(), receiptFormDto.getFkInvoiceIdList());
        }

        //激活佣金结算
        if (commissionActivationFlag) {
            //激活佣金
            Set<Long> receiptFormItemIds = receiptFormItems.stream().map(ReceiptFormItem::getId).collect(Collectors.toSet());
            receiptFormItemService.activateCommissionSettlement(receiptFormItemIds);
        }

        return findReceiptFormById(receiptFormDto.getId());
    }

    /**
     * 修改付款单中的部分信息
     *
     * @param receiptFormPortionVo 修改参数
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long updatePortionReceiptForm(ReceiptFormPortionDto receiptFormPortionVo) {
        if (GeneralTool.isEmpty(receiptFormPortionVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ReceiptForm receiptForm = receiptFormMapper.selectById(receiptFormPortionVo.getReceiptFormId());
        if (GeneralTool.isEmpty(receiptForm)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }

        // 修改的数据重新赋值
        receiptForm.setFkBankAccountId(receiptFormPortionVo.getFkBankAccountId());
        receiptForm.setReceiptDate(receiptFormPortionVo.getReceiptDate());

        receiptFormMapper.updateById(receiptForm);
        return receiptForm.getId();
    }

    /**
     * 根据目标类型关键字和目标id集合获取收款单
     *
     * @param fkTypeKey       目标类型关键字，枚举：m_institution_provider学校供应商/m_student学生
     * @param fkTypeTargetIds 目标id集合
     * @return key：目标id，value：收款单集合
     */
    @Override
    public Map<Long, List<ReceiptFormVo>> getReceiptFormsByTargetIds(String fkTypeKey, Set<Long> fkTypeTargetIds) {
        List<ReceiptForm> receiptForms = receiptFormMapper.selectList(Wrappers.<ReceiptForm>lambdaQuery()
                .eq(ReceiptForm::getFkTypeKey, fkTypeKey)
                .in(ReceiptForm::getFkTypeTargetId, fkTypeTargetIds)
                .eq(ReceiptForm::getStatus, 1));
        Map<Long, List<ReceiptFormVo>> receiptFormMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(receiptForms)) {
            List<ReceiptFormVo> receiptFormDtoList = BeanCopyUtils.copyListProperties(receiptForms, ReceiptFormVo::new);
            receiptFormMap = receiptFormDtoList.stream().collect(Collectors.groupingBy(ReceiptFormVo::getFkTypeTargetId));
        }
        return receiptFormMap;
    }

    /**
     * 根据服务费id获取对应的收款单信息
     * @param feeIds
     * @return
     */
    @Override
    public Map<Long, List<ReceiptFormVo>> getReceiptFormsByTargetIds(List<Long> feeIds) {
        Map<Long, List<ReceiptFormVo>> receiptFormMap = Maps.newHashMap();
        if (GeneralTool.isEmpty(feeIds)) {
            return receiptFormMap;
        }
        List<ReceiptFormVo> receiptFormVoList = receiptFormMapper.getReceiptFormsByTargetIds(feeIds);
        if (GeneralTool.isEmpty(receiptFormVoList)) {
            return receiptFormMap;
        }
        receiptFormMap = receiptFormVoList.stream().collect(Collectors.groupingBy(ReceiptFormVo::getFkReceivablePlanTargetId));
        return receiptFormMap;
    }

    @Override
    public List<ReceiptFormVo> findReceiptFormAll() {
        List<ReceiptForm> receiptFormVos = receiptFormMapper.selectList(new LambdaQueryWrapper<ReceiptForm>());
        List<ReceiptFormVo> receiptFormVoDatas = BeanCopyUtils.copyListProperties(receiptFormVos, ReceiptFormVo::new);
        if (GeneralTool.isNotEmpty(receiptFormVoDatas)){
            return receiptFormVoDatas;
        }
        return Collections.emptyList();
    }

    @Override
    public List<InvoiceVo> getInvoices(ReceiptFormDto receiptFormDto, Page page) {
        if (GeneralTool.isEmpty(receiptFormDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<InvoiceVo> invoiceVos = new ArrayList<>();
        //获取分页数据
        IPage<Contract> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<ReceiptFormInvoice> receiptFormInvoices = receiptFormInvoiceMapper.getReceiptFormInvoiceByfkReceiptFormId(receiptFormDto.getId());
        page.setAll((int) pages.getTotal());
        //发票ids
        Set<Long> fkInvoiceIds = receiptFormInvoices.stream().map(ReceiptFormInvoice::getFkInvoiceId).collect(Collectors.toSet());
        //根据发票ids获取Invoice对象map
        Map<Long, Invoice> invoiceByIds = getInvoiceByIds(fkInvoiceIds);

        //提供商ids
        Set<Long> fkInstitutionProviderIds = new HashSet<>();
        Map<Long, String> institutionProviderNamesByIds = new HashMap<>();
        //公司ids
        Set<Long> fkCompanyIds = new HashSet<>();
        Map<Long, String> companyNamesByIds = new HashMap<>();
        //币种编号nums
        Set<String> fkCurrencyTypeNums = new HashSet<>();
        Map<String, String> currencyNamesByNums = new HashMap<>();
        //遍历集合中的对象获取所有的提供商ids，公司ids，币种编号nums
        for (Map.Entry<Long, Invoice> longInvoiceEntry : invoiceByIds.entrySet()) {
            Invoice invoice = longInvoiceEntry.getValue();
//            fkInstitutionProviderIds.add(invoice.getFkInstitutionProviderId());
            fkCompanyIds.add(invoice.getFkCompanyId());
            fkCurrencyTypeNums.add(invoice.getFkCurrencyTypeNum());
        }

        //111111
        if (GeneralTool.isNotEmpty(fkInstitutionProviderIds)) {
            Result<Map<Long, String>> resultinstitutionProviderNamesByIds = institutionService.getInstitutionProviderNamesByIds(fkInstitutionProviderIds);
            if (resultinstitutionProviderNamesByIds.isSuccess()) {
                institutionProviderNamesByIds = resultinstitutionProviderNamesByIds.getData();
            }
        }

        if (GeneralTool.isNotEmpty(fkCompanyIds)) {
            Result<Map<Long, String>> resultcompanyNamesByIds = feignPermissionService.getCompanyNamesByIds(fkCompanyIds);
            if (resultcompanyNamesByIds.isSuccess()) {
                companyNamesByIds = resultcompanyNamesByIds.getData();
            }
        }

        if (GeneralTool.isNotEmpty(fkCurrencyTypeNums)) {
            currencyNamesByNums = currencyTypeService.getCurrencyNamesByNums(fkCurrencyTypeNums);
        }

        for (ReceiptFormInvoice receiptFormInvoice : receiptFormInvoices) {
            Invoice invoice = invoiceByIds.get(receiptFormInvoice.getFkInvoiceId());
            InvoiceVo invoiceVo = BeanCopyUtils.objClone(invoice, InvoiceVo::new);
            invoiceVo.setPid(receiptFormInvoice.getId());
//            invoiceDto.setInstitutionProviderName(institutionProviderNamesByIds.get(invoiceDto.getFkInstitutionProviderId()));
            invoiceVo.setCompanyName(companyNamesByIds.get(invoiceVo.getFkCompanyId()));
            invoiceVo.setCurrencyTypeName(currencyNamesByNums.get(invoiceVo.getFkCurrencyTypeNum()));
            invoiceVos.add(invoiceVo);
        }
        return invoiceVos;
    }

    @Override
    public String getCurrencyByFormId(Long formId) {
        if (GeneralTool.isEmpty(formId)) {
            return null;
        }
        ReceiptForm receiptForm = receiptFormMapper.selectById(formId);
        if (GeneralTool.isEmpty(receiptForm)) {
            return null;
        }
        return receiptForm.getFkCurrencyTypeNum();
    }

    @Override
    public BigDecimal getAmountByFormId(Long formId) {
        if (GeneralTool.isEmpty(formId)) {
            return null;
        }
        ReceiptForm receiptForm = receiptFormMapper.selectById(formId);
        if (GeneralTool.isEmpty(receiptForm)) {
            return null;
        }
        return receiptForm.getAmount();
    }

    @Override
    public List<FMediaAndAttachedVo> addMedia(List<MediaAndAttachedDto> mediaAndAttachedDtoList) {
        if (GeneralTool.isEmpty(mediaAndAttachedDtoList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<FMediaAndAttachedVo> mediaAndAttachedDtos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAndAttachedDtoList) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.FINANCE_RECEIPT_FORM.key);
            mediaAndAttachedDtos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedDtos;
    }

    @Override
    public List<FMediaAndAttachedVo> getMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.FINANCE_RECEIPT_FORM.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);
    }

    @Override
    public List<Map<String, Object>> findTypeKeySelect() {
//        return TableEnum.enumsTranslation2Arrays(TableEnum.RECEIPT_TARGET_TYPE);
        return ProjectKeyEnum.enums2Arrays(ProjectKeyEnum.RECEIPT_TARGET_TYPE);
    }

    @Override
    public List<BaseSelectEntity> findTypeTargetSelect(String tableName, Long companyId) {
        List<BaseSelectEntity> baseSelectEntities = new ArrayList<>();
        if (!SecureUtil.validateCompany(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        /*
            根据类型和公司获取队友的目标对象下拉
         */
        if (TableEnum.INSTITUTION_PROVIDER.key.equals(tableName)) {
            Result<List<BaseSelectEntity>> result = institutionService.getInstitutionProviderSelect(companyId);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                baseSelectEntities.addAll(result.getData());
                baseSelectEntities.removeIf(Objects::isNull);
            }
        }  else if (ProjectKeyEnum.BUSINESS_CHANNEL_ACC.key.equals(tableName)) {
            Result<List<BaseSelectEntity>> result = saleCenterClient.getTargetName(companyId, TableEnum.SALE_STUDENT_ACCOMMODATION.key);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                baseSelectEntities.addAll(result.getData());
                baseSelectEntities.removeIf(Objects::isNull);
            }
        } else if (ProjectKeyEnum.BUSINESS_CHANNEL_INS.key.equals(tableName)) {
            Result<List<BaseSelectEntity>> result = saleCenterClient.getTargetName(companyId, TableEnum.SALE_STUDENT_INSURANCE.key);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                baseSelectEntities.addAll(result.getData());
                baseSelectEntities.removeIf(Objects::isNull);
            }
        } else if (ProjectKeyEnum.INSTITUTION_PROVIDER_CHANNEL.key.equals(tableName)) {
            Result<List<BaseSelectEntity>> result = institutionService.getInstitutionProviderChannelSelect();
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                baseSelectEntities.addAll(result.getData());
                baseSelectEntities.removeIf(Objects::isNull);
            }
        }else if (ProjectKeyEnum.BUSINESS_PROVIDER.key.equals(tableName)){
//            Result<List<BaseSelectEntity>> businessObjectSelection = saleCenterClient.getBusinessObjectSelection(companyId);
//            if (businessObjectSelection.isSuccess() && businessObjectSelection.getData()!=null) {
//                baseSelectEntities.addAll(businessObjectSelection.getData());
//                baseSelectEntities.removeIf(Objects::isNull);
//            }
            Result<List<BaseSelectEntity>> businessProviderAccSelect = saleCenterClient.getBusinessProviderSelectByTypeKey(companyId, TableEnum.SALE_STUDENT_ACCOMMODATION.key);
            if (businessProviderAccSelect.isSuccess() && GeneralTool.isNotEmpty(businessProviderAccSelect.getData())) {
                baseSelectEntities.addAll(businessProviderAccSelect.getData());
                baseSelectEntities.removeIf(Objects::isNull);
            }
        }else if (ProjectKeyEnum.M_STUDENT.key.equals(tableName)){
            List<BaseSelectEntity> selection = saleCenterClient.getInvoiceStudentSelection(companyId);
            baseSelectEntities.addAll(selection);
            baseSelectEntities.removeIf(Objects::isNull);
        } else if (ProjectKeyEnum.BUSINESS_PROVIDER_INS.key.equals(tableName)) {
            Result<List<BaseSelectEntity>> businessProviderInsSelect = saleCenterClient.getBusinessProviderSelectByTypeKey(companyId, TableEnum.SALE_STUDENT_INSURANCE.key);
            if (businessProviderInsSelect.isSuccess() && GeneralTool.isNotEmpty(businessProviderInsSelect.getData())) {
                baseSelectEntities.addAll(businessProviderInsSelect.getData());
                baseSelectEntities.removeIf(Objects::isNull);
            }
        } else if (ProjectKeyEnum.INSURANCE_ORDER.key.equals(tableName)) {
            Result<List<BaseSelectEntity>> insuranceOrderSelect = saleCenterClient.getInsuranceOrderSelect(companyId);
            if (insuranceOrderSelect.isSuccess() && GeneralTool.isNotEmpty(insuranceOrderSelect.getData())) {
                baseSelectEntities.addAll(insuranceOrderSelect.getData());
                baseSelectEntities.removeIf(Objects::isNull);
            }
        }
        return baseSelectEntities;
    }

    @Override
    public List<Long> getFormByCompanyId(Long fkCompanyId) {
        if (GeneralTool.isEmpty(fkCompanyId)) {
            return null;
        }
//        Example example = new Example(ReceiptForm::new);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(fkCompanyId)) {
//            criteria.andEqualTo("fkCompanyId", fkCompanyId);
//        }
//        List<ReceiptForm> receiptForms = receiptFormMapper.selectByExample(example);

        LambdaQueryWrapper<ReceiptForm> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(fkCompanyId)) {
            lambdaQueryWrapper.eq(ReceiptForm::getFkCompanyId, fkCompanyId);
        }
        List<ReceiptForm> receiptForms = receiptFormMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isEmpty(receiptForms)) {
            return null;
        }
        return receiptForms.stream().map(ReceiptForm::getId).collect(Collectors.toList());
    }

    @Override
    public Long editComment(CommentDto commentDto) {
        FComment comment = BeanCopyUtils.objClone(commentDto, FComment::new);
        if (GeneralTool.isNotEmpty(commentDto)) {
            if (GeneralTool.isNotEmpty(commentDto.getId())) {
                comment.setFkTableName(TableEnum.FINANCE_RECEIPT_FORM.key);
                commentService.updateComment(comment);
            } else {
                comment.setFkTableName(TableEnum.FINANCE_RECEIPT_FORM.key);
                commentService.addComment(comment);
            }
        }
        return comment.getId();
    }

    @Override
    public List<FCommentVo> getComments(CommentDto commentDto, Page page) {
        commentDto.setFkTableName(TableEnum.FINANCE_RECEIPT_FORM.key);
        return commentService.datas(commentDto, page);
    }

    @Override
    public List<ReceiptFormVo> getReceiptFormList(Long planId) {
        if (GeneralTool.isEmpty(planId)) {
            return null;
        }
        return receiptFormMapper.getReceiptFormList(planId);
    }

    @Override
    public List<ReceiptFormVo> getReceiptFormListFeignByPlanIds(Set<Long> planIds) {
        return receiptFormMapper.getReceiptFormListFeignByPlanIds(planIds);
    }

    /**
     * 获取收款单
     *
     * @param id
     * @return
     */
    @Override
    public ReceiptFormVo getReceiptFormByFormId(Long id) {
        if (GeneralTool.isEmpty(id)) {
            return null;
        }
        ReceiptForm receiptForm = receiptFormMapper.selectById(id);
        return BeanCopyUtils.objClone(receiptForm, ReceiptFormVo::new);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveBatchReceiptForms(ServiceFeeReceiptFormDto serviceFeeReceiptFormDto) {
        List<ReceiptFormParamDto> receiptFormParamVoList = serviceFeeReceiptFormDto.getReceiptFormParamVoList();
        if (GeneralTool.isEmpty(receiptFormParamVoList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        // 所选服务费记录的币种
        Set<String> currencyTypeNums = receiptFormParamVoList.stream().map(ReceiptFormParamDto::getFkReceivableCurrencyNum)
                .filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());

        Map<String, BigDecimal> hkdRate = Maps.newHashMap();
        Map<String, BigDecimal> cnyRate = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(currencyTypeNums)) {
            hkdRate = exchangeRateService.getLastExchangeRate(currencyTypeNums, "HKD");
            cnyRate = exchangeRateService.getLastExchangeRate(currencyTypeNums, "CNY");
        }
        // 发票id目前先写死-30，后续再改动
        Long fkInvoiceId = -30L;
        Invoice invoice = invoiceMapper.selectById(fkInvoiceId);

        for (ReceiptFormParamDto param : receiptFormParamVoList) {
            // 币种
            String currencyNum = param.getFkReceivableCurrencyNum();
            // 金额
            BigDecimal receivableAmount = param.getReceivableAmount();

            // 创建收款单
            ReceiptForm receiptForm = new ReceiptForm();
            receiptForm.setFkCompanyId(serviceFeeReceiptFormDto.getFkCompanyId());
            receiptForm.setFkTypeKey(ProjectKeyEnum.M_STUDENT.key);
            receiptForm.setFkTypeTargetId(param.getFkStudentId());
            receiptForm.setFkBankAccountId(serviceFeeReceiptFormDto.getFkBankAccountId());
            receiptForm.setFkInvoiceNum(invoice.getNum());
            receiptForm.setReceiptDate(serviceFeeReceiptFormDto.getRecipientDate());
            receiptForm.setFkCurrencyTypeNum(currencyNum);
            receiptForm.setExchangeRate(new BigDecimal("1.000000"));
            receiptForm.setAmount(receivableAmount);
            receiptForm.setExchangeRateHkd(GeneralTool.isNotEmpty(hkdRate.get(currencyNum)) ? hkdRate.get(currencyNum) : new BigDecimal("1.0000"));
            receiptForm.setAmountHkd(receivableAmount.multiply(receiptForm.getExchangeRateHkd()));
            receiptForm.setExchangeRateRmb(GeneralTool.isNotEmpty(cnyRate.get(currencyNum)) ? cnyRate.get(currencyNum) : new BigDecimal("1.0000"));
            receiptForm.setAmountRmb(receivableAmount.multiply(receiptForm.getExchangeRateRmb()));
            receiptForm.setServiceFee(BigDecimal.ZERO);
            receiptForm.setSettlementStatus(1);
            receiptForm.setStatus(1);
            utilService.setCreateInfo(receiptForm);
            receiptFormMapper.insert(receiptForm);
            if (GeneralTool.isNotEmpty(receiptForm)) {
                receiptForm.setNumSystem(GetStringUtils.getReceiptFormNum(receiptForm.getId()));
                receiptFormMapper.updateById(receiptForm);
            }
            // 创建收款单和发票关联记录
            ReceiptFormInvoice rfi = new ReceiptFormInvoice();
            rfi.setFkReceiptFormId(receiptForm.getId());
            rfi.setFkInvoiceId(fkInvoiceId);
            utilService.setCreateInfo(rfi);
            receiptFormInvoiceMapper.insert(rfi);
            // 创建收款单明细
            ReceiptFormItem receiptFormItem = new ReceiptFormItem();
            receiptFormItem.setFkReceiptFormId(receiptForm.getId());
            receiptFormItem.setFkReceivablePlanId(param.getFkReceivablePlanId());
            receiptFormItem.setAmountReceipt(receivableAmount);
            receiptFormItem.setServiceFee(BigDecimal.ZERO);
            BigDecimal finalExchangeRate = receiptForm.getExchangeRate();
            receiptFormItem.setExchangeRateReceivable(finalExchangeRate);
            receiptFormItem.setAmountReceivable(receivableAmount.add(receiptFormItem.getServiceFee()).multiply(finalExchangeRate));
            receiptFormItem.setAmountExchangeRate(BigDecimal.ZERO);
            receiptFormItem.setExchangeRateHkd(GeneralTool.isNotEmpty(hkdRate.get(currencyNum)) ? hkdRate.get(currencyNum) : new BigDecimal("1.0000"));
            receiptFormItem.setAmountHkd(receivableAmount.add(receiptFormItem.getServiceFee()).multiply(receiptFormItem.getExchangeRateHkd()));
            receiptFormItem.setExchangeRateRmb(GeneralTool.isNotEmpty(cnyRate.get(currencyNum)) ? cnyRate.get(currencyNum) : new BigDecimal("1.0000"));
            receiptFormItem.setAmountRmb(receivableAmount.add(receiptFormItem.getServiceFee()).multiply(receiptFormItem.getExchangeRateRmb()));
            utilService.setCreateInfo(receiptFormItem);
            receiptFormItemMapper.insert(receiptFormItem);
        }
        return true;
    }

    private String sub(StringBuilder sb) {
        if (GeneralTool.isEmpty(sb)) {
            return null;
        }
        String substring = null;
        int i = sb.lastIndexOf("，");
        if (i != -1) {
            substring = sb.substring(0, i);
        }
        return substring;
    }

    private String dateFormat(String date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cd = Calendar.getInstance();
        try {
            cd.setTime(sdf.parse(date));
        } catch (ParseException e) {
            e.printStackTrace();
            throw new GetServiceException(LocaleMessageUtils.getMessage("date_format_conversion_exception"));
        }
        //增加一天
        cd.add(Calendar.DATE, 1);
        return sdf.format(cd.getTime());
    }


    /**
     * 根据发票ids获取Invoice对象map
     *
     * @param ids
     * @return
     */
    private Map<Long, Invoice> getInvoiceByIds(Set<Long> ids) {
        Map<Long, Invoice> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
//        Example example = new Example(Invoice::new);
//        example.createCriteria().andIn("id", ids);
//        List<Invoice> invoices = invoiceMapper.selectByExample(example);
        List<Invoice> invoices = invoiceMapper.selectBatchIds(ids);
        if (GeneralTool.isEmpty(invoices)) {
            return map;
        }
        for (Invoice invoice : invoices) {
            map.put(invoice.getId(), invoice);
        }
        return map;
    }
}
