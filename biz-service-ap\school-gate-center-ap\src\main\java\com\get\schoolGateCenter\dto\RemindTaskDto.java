package com.get.schoolGateCenter.dto;
import com.get.schoolGateCenter.entity.RemindTask;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Date: 2021/11/15
 * Description:提醒任务返回类
 */
@Data
public class RemindTaskDto extends RemindTask {
    /**
     * 循环周日期提醒（周日-周六）：多选：0,1,2,3,4,5,6
     */
    @ApiModelProperty(value = "循环周日期提醒（周日-周六）：多选：0,1,2,3,4,5,6")
    private List<String> loopWeekDaysList;

    /**
     * 循环月日期提醒：多选：1,2...31
     */
    @ApiModelProperty(value = "循环月日期提醒：多选：1,2...31")
    private List<String> loopMonthDaysList;

    /**
     * 是否是周期(true：是)
     */
    @ApiModelProperty(value = "是否是周期(true：是)")
    private boolean isCycle;


    /**
     * 是否需要提醒(true：是)
     */
    @ApiModelProperty(value = "是否需要提醒(true：是)")
    private boolean isRemind;

    /**
     * 提醒方法集合
     */
    @ApiModelProperty(value = "提醒方法集合")
    private List<String> remindMethodList;
}
