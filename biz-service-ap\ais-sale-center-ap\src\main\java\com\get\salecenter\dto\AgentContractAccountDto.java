package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/9/15
 * @TIME: 9:49
 * @Description: 合同账号
 **/
@Data
public class AgentContractAccountDto extends BaseVoEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    /**
     * 学生代理Id
     */
    @NotNull(message = "学生代理Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "学生代理Id", required = true)
    private Long fkAgentId;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @NotNull(message = "是否激活不能为空", groups = {Add.class, Update.class})
    private Boolean isActive;
    /**
     * 币种编号（用于编辑）
     */
    @ApiModelProperty(value = "币种编号（用于编辑）")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;

    /**
     * 账户卡类型
     */
    @ApiModelProperty(value = "账户卡类型，枚举：借记卡1,存折2,信用卡3,准贷记卡4,预付卡费5,境外卡6")
    @Column(name = "account_card_type")
    private Integer accountCardType;
    /**
     * 银行账户名称
     */
    @ApiModelProperty(value = "银行账户名称")
    @Column(name = "bank_account")
    private String bankAccount;
    /**
     * 银行账号
     */
    @ApiModelProperty(value = "银行账号")
    @Column(name = "bank_account_num")
    private String bankAccountNum;
    /**
     * 银行名称
     */
    @ApiModelProperty(value = "银行名称")
    @Column(name = "bank_name")
    private String bankName;
    /**
     * 银行支行名称
     */
    @ApiModelProperty(value = "银行支行名称")
    @Column(name = "bank_branch_name")
    private String bankBranchName;
    /**
     * 银行地址国家Id
     */
    @ApiModelProperty(value = "银行地址国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 银行地址州省Id
     */
    @ApiModelProperty(value = "银行地址州省Id")
    @Column(name = "fk_area_state_id")
    private Long fkAreaStateId;
    /**
     * 银行地址城市Id
     */
    @ApiModelProperty(value = "银行地址城市Id")
    @Column(name = "fk_area_city_id")
    private Long fkAreaCityId;
    /**
     * 银行地址城市区域Id
     */
    @ApiModelProperty(value = "银行地址城市区域Id")
    @Column(name = "fk_area_city_division_id")
    private Long fkAreaCityDivisionId;
    /**
     * 银行地址
     */
    @ApiModelProperty(value = "银行地址")
    @Column(name = "bank_address")
    private String bankAddress;
    /**
     * 银行编号类型：SwiftCode/BSB
     */
    @ApiModelProperty(value = "银行编号类型：SwiftCode/BSB")
    @Column(name = "bank_code_type")
    private String bankCodeType;
    /**
     * 银行编号
     */
    @ApiModelProperty(value = "银行编号")
    @Column(name = "bank_code")
    private String bankCode;
    /**
     * 国家编码
     */
    @ApiModelProperty(value = "国家编码")
    @Column(name = "area_country_code")
    private String areaCountryCode;
    /**
     * 是否默认首选：0否/1是
     */
    @ApiModelProperty(value = "是否默认首选：0否/1是")
    @Column(name = "is_default")
    private Boolean isDefault;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


    /**
     * 币种编号列表（用于新增）
     */
    @ApiModelProperty(value = "币种编号列表（用于新增）")
    private List<String> fkCurrencyTypeNumList;

   


}
