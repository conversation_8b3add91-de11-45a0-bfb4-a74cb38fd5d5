<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.get.platformconfigcenter.dao.hubs.SaleOrderMapper">

    <select id="getAppHubsOrders" resultType="com.get.platformconfigcenter.vo.SubmitOrderVo">
        SELECT
            so.id,
            so.gmt_create as createTime,
            so.fk_country_butler_id,
            so.fk_currency_type_num,
            so.total_amount,
            so.payment_status,
            u.student_name,
            u.student_school
        FROM
            m_sale_order so
                LEFT JOIN m_sale_order_user_info u ON so.id = u.fk_sale_order_id
        <where>
            <if test="data.fkCountryButlerId != null">
                and so.fk_country_butler_id = #{data.fkCountryButlerId}
            </if>
            <if test="data.createTime != null and data.createTime !='' ">
                and DATE(so.gmt_create) = #{data.createTime}
            </if>
            <if test="data.stuName != null and data.stuName !='' ">
                and student_name LIKE CONCAT("%",#{data.stuName},"%")
            </if>
        </where>
        ORDER BY so.gmt_create Desc
    </select>
    <select id="getSaleOrderUserInfo" resultType="com.get.platformconfigcenter.vo.SaleOrderUserInfoVo">
        SELECT
            a.*,
            b.payment_status,
            b.total_amount,
            b.fk_currency_type_num
        FROM
            m_sale_order_user_info a
                LEFT JOIN m_sale_order b on a.fk_sale_order_id = b.id
        WHERE
            fk_sale_order_id = #{fkOrderId} limit 1
    </select>
    <select id="getAppHubsOrdersItemDetailByOrderId"
            resultType="com.get.platformconfigcenter.vo.SaleOrderItemVo">
        SELECT
            a.id,
            a.quantity,
            a.original_price,
            a.discount_price,
            a.fk_currency_type_num,
            a.product_attributes_json,
            b.`name` as productName,
            c.name_chn as serviceNameChn
        FROM
            m_sale_order_item a
                LEFT JOIN m_product b on a.fk_product_id = b.id
                LEFT JOIN m_service c on b.fk_service_id = c.id
        WHERE a.fk_sale_order_id = #{fkOrderId}
    </select>
    <select id="getAppHubsCountry" resultType="com.get.platformconfigcenter.entity.CountryButler">
        SELECT * from m_country_butler ORDER BY view_order DESC
    </select>
    <select id="getRegisteredUser" resultType="com.get.platformconfigcenter.vo.HubsUserVo">
    select mu.id,
           mu.name,
           mu.mobile,
           mu.gender,
           rupt.gmt_create
       from ais_app_registration_center.r_user_platform_type rupt
            left join ais_app_registration_center.m_user mu on rupt.fk_user_id = mu.id
       where 1=1
        <if test="data.mobile != null and data.mobile != ''">
            and mu.mobile LIKE concat("%",#{data.mobile},"%")
        </if>
        <if test="(data.startTime != null) and (data.startTime != '') and (data.endTime != null) and (data.endTime != '')">
            and DATE(rupt.gmt_create) BETWEEN DATE(#{data.startTime}) AND DATE(#{data.endTime})
        </if>
    and rupt.fk_platform_type = 'get_hubs'
    </select>
</mapper>
