<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.VouchMapper">

    <select id="getVouchs" resultType="com.get.financecenter.vo.VouchVo">
        SELECT
        mv.id,
        mv.vouch_num AS vouchNum,
        mv.vouch_type AS VouchType,
        mv.business_date AS businessDate,
        mv.remark,
        mv.fk_company_id AS fkCompanyId,
        mv.create_type AS createType,
        mv.is_active AS isActive,
        mv.gmt_create AS gmtCreate,
        mv.gmt_create_user AS gmtCreateUser,
        mv.gmt_modified AS gmtModified,
        mv.gmt_modified_user AS gmtModifiedUser
        FROM m_vouch mv
        WHERE 1=1
        <if test="vouchDto.vouchNum != null and vouchDto.vouchNum != ''">
            AND mv.vouch_num LIKE CONCAT('%',#{vouchDto.vouchNum},'%')
        </if>
        <if test="vouchDto.businessDate !=null">
            AND mv.business_date = #{vouchDto.businessDate}
        </if>
        <if test="vouchDto.fkCompanyId !=null and vouchDto.fkCompanyId !=''">
            AND mv.fk_company_id = #{vouchDto.fkCompanyId}
        </if>

        <if test="vouchDto.remark !=null and vouchDto.remark !=''">
            AND mv.remark LIKE CONCAT('%',#{vouchDto.remark},'%')
        </if>

        ORDER BY mv.gmt_create DESC
    </select>

    <select id="getAccountingItemBalance" resultType="java.math.BigDecimal">
        SELECT
        <if test="direction == 0">
            COALESCE(SUM(mvi.amount_dr), 0) - COALESCE(SUM(mvi.amount_cr), 0)
        </if>
        <if test="direction == 1">
            COALESCE(SUM(mvi.amount_cr), 0)  - COALESCE(SUM(mvi.amount_dr), 0)
        </if>
        <if test="direction == 2">
            COALESCE(SUM(mvi.amount_dr), 0)
        </if>
        <if test="direction == 3">
            COALESCE(SUM(mvi.amount_cr), 0)
        </if>
        FROM
        m_vouch AS mv
        INNER JOIN m_vouch_item AS mvi ON mvi.fk_vouch_id = mv.id
        WHERE
        mv.is_active = 1
        AND mv.fk_company_id = #{companyId}
        AND mvi.fk_accounting_item_id IN
        <foreach item="item" collection="accountingItemIds" separator="," open="(" close=")" index="index">
            #{item}
        </foreach>
        <if test="startDate != null">
            AND DATE_FORMAT( mv.business_date, '%Y-%m-%d' )  <![CDATA[>= ]]>  DATE_FORMAT( #{startDate}, '%Y-%m-%d' )
        </if>
        <if test="endDate != null">
            AND DATE_FORMAT( mv.business_date, '%Y-%m-%d' )  <![CDATA[<= ]]>  DATE_FORMAT( #{endDate}, '%Y-%m-%d' )
        </if>
        <if test="relationTargetKey !=null">
            AND mvi.relation_target_key = #{relationTargetKey}
        </if>
        <if test="relationTargetId !=null">
            AND mvi.relation_target_id = #{relationTargetId}
        </if>
    </select>


</mapper>