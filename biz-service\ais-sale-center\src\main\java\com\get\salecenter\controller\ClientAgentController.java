package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.vo.ClientAgentVo;
import com.get.salecenter.entity.ClientAgent;
import com.get.salecenter.service.IClientAgentService;
import com.get.salecenter.dto.ClientAgentDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * author:Neil
 * Time: 14:02
 * Date: 2022/8/17
 * Description:客户代理管理
 */
@Api(tags = "客户代理管理")
@RestController
@RequestMapping("sale/clientAgent")
public class ClientAgentController {
    @Resource
    private IClientAgentService clientAgentService;


    @ApiOperation(value = "绑定代理", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/客户管理/绑定代理")
    @PostMapping("addClientAgent")
    public ResponseBo<Long> addClientAgent(@RequestBody ClientAgentDto clientAgentDto){
        return clientAgentService.addClientAgent(clientAgentDto);
    }

    @ApiOperation(value = "更新代理绑定", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/客户管理/更新代理绑定")
    @PostMapping("updateClientAgent")
    public ResponseBo<ClientAgent> updateClientAgent(@RequestBody ClientAgentDto clientAgentDto){
        return clientAgentService.updateClientAgent(clientAgentDto);
    }

    @ApiOperation(value = "客户代理列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/客户管理/客户代理列表")
    @PostMapping("datas")
    public ResponseBo<ClientAgentVo> datas(@RequestBody SearchBean<ClientAgentDto> searchBean){
        return clientAgentService.getClientAgentList(searchBean.getData(),searchBean);
    }

}
