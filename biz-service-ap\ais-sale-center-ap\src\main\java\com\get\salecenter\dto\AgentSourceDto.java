package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Param:
 * @return:
 * @Author: Walker
 * @Date: 2022/3/16
 */
@Data
public class AgentSourceDto extends BaseVoEntity{
    /**
     * 代理名
     */
    @ApiModelProperty(value = "代理名")
    private String agentName;


}
