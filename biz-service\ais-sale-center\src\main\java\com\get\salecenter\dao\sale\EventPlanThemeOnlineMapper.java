package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.vo.EventPlanThemeOnlineVo;
import com.get.salecenter.entity.EventPlanThemeOnline;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
@Mapper
public interface EventPlanThemeOnlineMapper extends BaseMapper<EventPlanThemeOnline> {

    List<EventPlanThemeOnlineVo> getEventPlanThemeOnlines(@Param("fkEventPlanId") Long fkEventPlanId, @Param("displayType") Integer displayType);

    /**
     * 获取最大排序
     * <AUTHOR>
     * @DateTime 2023/12/15 16:29
     */
    Integer getMaxViewOrder(Long fkEventPlanThemeId);

}
