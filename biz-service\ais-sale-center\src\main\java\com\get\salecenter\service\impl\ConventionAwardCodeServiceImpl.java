package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.ConventionAwardCodeMapper;
import com.get.salecenter.dao.sale.ConventionAwardMapper;
import com.get.salecenter.vo.ConventionAwardCodeVo;
import com.get.salecenter.entity.ConventionAward;
import com.get.salecenter.entity.ConventionAwardCode;
import com.get.salecenter.service.IConventionAwardCodeService;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.dto.ConventionAwardCodeDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2021/9/13
 * @TIME: 16:06
 * @Description:
 **/
@Service
public class ConventionAwardCodeServiceImpl implements IConventionAwardCodeService {
    @Resource
    private UtilService utilService;
    @Resource
    private ConventionAwardCodeMapper conventionAwardCodeMapper;
    @Resource
    private ConventionAwardMapper conventionAwardMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(ConventionAwardCodeDto conventionAwardCodeDto) {
        for (int i = conventionAwardCodeDto.getStartNum(); i < conventionAwardCodeDto.getEndNum() + 1; i++) {
            ConventionAwardCode conventionAwardCode = BeanCopyUtils.objClone(conventionAwardCodeDto, ConventionAwardCode::new);
            // 表示4位长度， 如 0001
            conventionAwardCode.setAwardCode(MyStringUtils.getAwardCodeNum(i, conventionAwardCodeDto.getDigit(), conventionAwardCodeDto.getPreamble()));
            utilService.updateUserInfoToEntity(conventionAwardCode);
            conventionAwardCodeMapper.insert(conventionAwardCode);
        }
    }

    @Override
    public void delete(Long id) {
        conventionAwardCodeMapper.deleteByStatus(id);
    }

    @Override
    public List<ConventionAwardCodeVo> getConventionAwardCodes(ConventionAwardCodeDto conventionAwardCodeDto, Page page) {
        //获取分页数据
        IPage<ConventionAwardCodeVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<ConventionAwardCodeVo> conventionAwardCodeVos = conventionAwardCodeMapper.getConventionAwardCodes(iPage, conventionAwardCodeDto);
        page.setAll((int) iPage.getTotal());

        for (ConventionAwardCodeVo conventionAwardCodeVo : conventionAwardCodeVos) {
            if (GeneralTool.isNotEmpty(conventionAwardCodeVo.getUseType())) {
                conventionAwardCodeVo.setUseTypeName(ProjectExtraEnum.getValueByKey(Integer.valueOf(conventionAwardCodeVo.getUseType()), ProjectExtraEnum.LOTTERY_TICKET_PURCHASE_STATUS));
            }
            String role = null;
            if (GeneralTool.isNotEmpty(conventionAwardCodeVo.getType())) {
                role = ProjectExtraEnum.getValueByKey(Integer.valueOf(conventionAwardCodeVo.getType()), ProjectExtraEnum.CONVENTION_PERSON_TYPE);
            }
            if (GeneralTool.isNotEmpty(conventionAwardCodeVo.getName())) {
                conventionAwardCodeVo.setPurchaser(conventionAwardCodeVo.getName() + "(" + role + ")");
            }
        }
        return conventionAwardCodeVos;
    }


    /**
     * @Description: 根据名称模糊搜索抽奖号码ids
     * @Author: Jerry
     * @Date:16:17 2021/9/15
     */
    @Override
    public Set<Long> getConventionAwardCodesIdsByConventionAwardCodesName(String conventionAwardCodesName) {
//        Example example = new Example(ConventionAwardCode.class);
//        example.createCriteria().andLike("awardCode","%"+conventionAwardCodesName+"%");
//        List<ConventionAwardCode> conventionAwardCodes = conventionAwardCodeMapper.selectByExample(example);

        List<ConventionAwardCode> conventionAwardCodes = conventionAwardCodeMapper.selectList(Wrappers.<ConventionAwardCode>lambdaQuery().like(ConventionAwardCode::getAwardCode, conventionAwardCodesName));
        if (GeneralTool.isEmpty(conventionAwardCodes)) {
            return null;
        }
        return conventionAwardCodes.stream().map(ConventionAwardCode::getId).collect(Collectors.toSet());
    }


    /**
     * @Description: 根据抽奖号码ids获取对象
     * @Author: Jerry
     * @Date:17:00 2021/9/15
     */
    @Override
    public Map<Long, ConventionAwardCode> getConventionAwardCodesByConventionAwardCodesIds(Set<Long> conventionAwardCodesIds) {
        Map<Long, ConventionAwardCode> map = new HashMap<>();
        if (GeneralTool.isEmpty(conventionAwardCodesIds)) {
            return map;
        }
//        Example example = new Example(ConventionAwardCode.class);
//        example.createCriteria().andIn("id",conventionAwardCodesIds);
//        List<ConventionAwardCode> conventionAwardCodes = conventionAwardCodeMapper.selectByExample(example);
        List<ConventionAwardCode> conventionAwardCodes = conventionAwardCodeMapper.selectList(Wrappers.<ConventionAwardCode>lambdaQuery().in(ConventionAwardCode::getId, conventionAwardCodesIds));
        if (GeneralTool.isEmpty(conventionAwardCodes)) {
            return map;
        }
        for (ConventionAwardCode conventionAwardCode : conventionAwardCodes) {
            map.put(conventionAwardCode.getId(), conventionAwardCode);
        }
        return map;
    }

    @Override
    public List<ConventionAwardCodeVo> getTicketsByGroleAndAwardId(String agrole, int award_id, String status) {
        return null;
    }

    @Override
    public List<ConventionAwardCode> getListTicketsNoUsedByAwardId(Long awardId, Long fkConventionId) {
        if (GeneralTool.isEmpty(awardId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //奖品信息
        ConventionAward conventionAward = conventionAwardMapper.selectOne(Wrappers.<ConventionAward>lambdaQuery()
                .eq(ConventionAward::getFkConventionId, fkConventionId)
                .eq(ConventionAward::getId, awardId));
        return conventionAwardCodeMapper.getLuckyNumber(fkConventionId, null, conventionAward.getGetRole());

//        String roles = conventionAwardMapper.getRole(awardId);
//        String personId = conventionAwardMapper.getPersonId(awardId);
//        List<TicketsNoUsedVo> conventionAwardCodeDtos = conventionAwardCodeMapper.getListTicketsNoUsedByAwardId(awardId, roles, personId);
//        List<TicketsNoUsedVo> codes = new ArrayList<>();
//        if (GeneralTool.isNotEmpty(personId)) {
//            String[] split = personId.split(",");
//            for (String s : split) {
//                if (GeneralTool.isNotEmpty(s)) {
//                    codes.addAll(conventionAwardCodeMapper.getCodeByAwardAndType(Long.valueOf(s)));
//                }
//            }
//        }
//        RealTicketsVo realTicketsDto = new RealTicketsVo();
//        realTicketsDto.setCodes(codes);
//        realTicketsDto.setNormals(conventionAwardCodeDtos);
//        return realTicketsDto;
    }
}
