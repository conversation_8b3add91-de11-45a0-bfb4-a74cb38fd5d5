package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.StudentOfferItemSettlementAgentAccount;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper
public interface StudentOfferItemSettlementAgentAccountMapper extends BaseMapper<StudentOfferItemSettlementAgentAccount> {
    int insert(StudentOfferItemSettlementAgentAccount record);

    int insertSelective(StudentOfferItemSettlementAgentAccount record);


    /**
     * 根据学习计划ids 获取结算标记
     *
     * @return
     * @Date 16:55 2021/12/22
     * <AUTHOR>
     */
    List<StudentOfferItemSettlementAgentAccount> getSettlementMarkByItemIds(List<Long> itemIds);
}