package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class ServiceFeeARAPDto {


    @ApiModelProperty("服务费ids")
    @NotEmpty(message = "请选择要创建应收应付的对象")
    private Set<Long> serviceFeeIds;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @NotNull(message = "公司id不能为空")
    private Long fkCompanyId;


    @ApiModelProperty("应收币种")
    @NotBlank(message = "请选择应收币种")
    private String fkReceivableCurrencyNum;

    @ApiModelProperty("应收金额")
    @NotNull(message = "请填写应收金额")
    private BigDecimal receivableAmount;

    @ApiModelProperty("是否创建应付")
    @NotNull(message = "是否创建应付标志不能为空")
    private Boolean isCreatePayable;

    @ApiModelProperty("应付比率")
    private BigDecimal payableRatio;

    @ApiModelProperty("应付币种")
    private String fkPayableCurrencyNum;

    @ApiModelProperty("应付金额")
    private BigDecimal payableAmount;
}
