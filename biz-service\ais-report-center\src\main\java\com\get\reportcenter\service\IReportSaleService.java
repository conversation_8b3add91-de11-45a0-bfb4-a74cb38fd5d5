package com.get.reportcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.reportcenter.dto.ReportSaleDto;
import com.get.reportcenter.entity.ReportSale;
//import com.get.reportcenter.vo.ReportSaleVo;
import com.get.salecenter.vo.PeriodicStatisticsVo;
import com.get.salecenter.dto.StudentApplicationStatisticsDto;
import com.get.salecenter.vo.StudentApplicationStatisticsVo;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * <AUTHOR>
 * @DATE: 2021/3/4
 * @TIME: 18:19
 * @Description:
 **/
public interface IReportSaleService extends IService<ReportSale> {
    /**
     * 查询最近一次周报统计报表记录
     * <AUTHOR>
     * @DateTime 2022/12/28 16:38
     */
    ReportSale getLastReportSale(Long fkStaffId);

    /**
     * 获取报表
     * <AUTHOR>
     * @DateTime 2023/1/3 16:20
     */
    ReportSale getReportSale(StudentApplicationStatisticsDto studentApplicationStatisticsDto);

    /**
     * 更新报表状态
     * <AUTHOR>
     * @DateTime 2023/1/3 16:20
     */
    void updateReportSaleStatus(Long fkReportSaleId,Integer reportStatus);

    /**
     * 新增报表
     * <AUTHOR>
     * @DateTime 2023/1/3 16:20
     */
     Long addReportSale(@RequestBody StudentApplicationStatisticsDto studentApplicationStatisticsDto);

    /**
     * 跟新报表结果
     * <AUTHOR>
     * @DateTime 2023/1/3 16:19
     */
     void updateReportSale(PeriodicStatisticsVo periodicStatisticsVo, Long fkReportSaleId);

    /**
     * 新增报表记录
     * @param reportSaleVo
     * @return
     */
    Long addReportSaleCommon(ReportSaleDto reportSaleVo);

    Integer getReportSaleStatusById(Long fkReportSaleId);

    /**
     *
     * @param fkReportSaleId
     * @return
     */
    ReportSaleDto getReportSaleById(Long fkReportSaleId);

    ReportSaleDto getLastReportSaleVoByReportNameAndUserId(String key, Long staffId);
}
