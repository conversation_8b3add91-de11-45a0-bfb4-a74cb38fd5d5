package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.vo.CourseTypeVo;
import com.get.institutioncenter.entity.CourseType;
import com.get.institutioncenter.service.ICourseTypeService;
import com.get.institutioncenter.dto.CourseTypeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/12/7
 * @TIME: 14:55
 * @Description:
 **/
@Api(tags = "课程类型管理")
@RestController
@RequestMapping("/institution/courseType")
public class CourseTypeController {
    @Resource
    private ICourseTypeService courseTypeService;

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/课程类型管理/查询课程类型")
    @PostMapping("datas")
    public ResponseBo<CourseTypeVo> datas(@RequestBody SearchBean<CourseTypeDto> page) {
        List<CourseTypeVo> datas = courseTypeService.getCourseTypes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/课程类型管理/类型详情")
    @GetMapping("/{id}")
    public ResponseBo<CourseTypeVo> detail(@PathVariable("id") Long id) {
        //TODO 改过
        //CourseType data = courseTypeService.findCourseTypeById(id);
        CourseTypeVo data = courseTypeService.findCourseTypeById(id);
        CourseTypeVo courseTypeVo = BeanCopyUtils.objClone(data, CourseTypeVo::new);
        return new ResponseBo<>(courseTypeVo);
    }


    /**
     * 删除
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/课程类型管理/删除类型")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        this.courseTypeService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param courseTypeDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/课程类型管理/更新类型")
    @PostMapping("update")
    public ResponseBo<CourseTypeVo> update(@RequestBody @Validated(CourseTypeDto.Update.class) CourseTypeDto courseTypeDto) {
        return UpdateResponseBo.ok(courseTypeService.updateCourseType(courseTypeDto));
    }


    /**
     * 批量新增信息
     *
     * @param resources
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/课程类型管理/批量保存")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(CourseTypeDto.Add.class) ValidList<CourseTypeDto> resources) {
        courseTypeService.batchAdd(resources);
        return ResponseBo.ok();
    }

    /**
     * 上移下移
     *
     * @param resources
     * @return
     * @
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/课程类型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<CourseTypeDto> resources) {
        courseTypeService.movingOrder(resources);
        return ResponseBo.ok();
    }

    /**
     * 课程类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "课程类型下拉框数据", notes = "")
    @GetMapping("getCourseTypeList")
    public ResponseBo<BaseSelectEntity> getCourseTypeList() {
        List<BaseSelectEntity> datas = courseTypeService.getCourseTypeList();
        return new ListResponseBo<>(datas);
    }
    /**
     * 课程类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "通过mode课程类型下拉框数据", notes = "")
    @GetMapping("getCourseTypeListByMode")
    public ResponseBo<BaseSelectEntity> getCourseTypeListByMode() {
        List<BaseSelectEntity> datas = courseTypeService.getCourseTypeListByMode();
        return new ListResponseBo<>(datas);
    }
    /**
     * @Description :feign调用 通过课程类型ids 查找对应的课程类型名称map
     * @Param [ids]
     * <AUTHOR>
     */
//    @ApiIgnore
//    @PostMapping(value = "getCourseTypeNamesByIds")
//    public Map<Long, String> getCourseTypeNamesByIds(@RequestBody Set<Long> ids) {
//        return courseTypeService.getCourseTypeNamesByIds(ids);
//    }

    /**
     * @Description :feign调用 通过课程类型ids 查找对应的课程类型名称map
     * @Param [ids]
     * <AUTHOR>
     */
    /*@ApiIgnore
    @PostMapping(value = "getCourseTypeNamesByCourseIds")
    public Map<Long, String> getCourseTypeNamesByCourseIds(@RequestBody Set<Long> ids) {
        return courseTypeService.getCourseTypeNamesByCourseIds(ids);
    }*/

}
