package com.get.partnercenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/2/12  10:46
 * @Version 1.0
 * 邮件记录
 */
@Data
public class MailLogVo {

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("操作类型：1新建/2重置密码/3学生确认提醒")
    private Integer optType;

    @ApiModelProperty("发件人登录账号")
    private String fromUser;

    @ApiModelProperty("发件人名称")
    private String fromUserName;

    @ApiModelProperty("发件人电邮地址")
    private String fromEmail;

    @ApiModelProperty("收件人名称（代理联系人名称/伙伴账号名称）")
    private String toUser;

    @ApiModelProperty("收件人电邮地址")
    private String toEmail;

    @ApiModelProperty("邮件标题")
    private String subject;

    @ApiModelProperty("邮件内容")
    private String body;

    @ApiModelProperty("邮件发送时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendDate;

    @ApiModelProperty("发送状态：0失败/1成功'")
    private Integer sendStatus;

    @ApiModelProperty("发送信息，可以记录失败原因")
    private String sendMessage;

    @ApiModelProperty("创建人")
    private String gmtCreateUser;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    @ApiModelProperty("更新人")
    private String gmtModifiedUser;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("更新时间")
    private Date gmtModified;

    @ApiModelProperty("代理名称")
    private String agentName;

}
