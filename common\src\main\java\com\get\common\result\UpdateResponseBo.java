package com.get.common.result;

/**
 * 更新数据-响应类
 *
 * @author: jack
 * @create: 2020-4-15
 * @verison: 1.0
 * @description: 更新数据-响应类
 */
public class UpdateResponseBo<T> extends ResponseBo {
    private static final String DATA = "data";

    public UpdateResponseBo(T t) {
        super();
        this.put(DATA, t);
    }

    /**
     * 修改成功，返回修改后的对象
     *
     * @param t
     * @param <T>
     * @return
     */
    public static <T> UpdateResponseBo ok(T t) {
        return new UpdateResponseBo<>(t);
    }

}
