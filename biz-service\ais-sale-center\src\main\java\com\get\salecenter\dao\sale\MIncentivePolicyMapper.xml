<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.get.salecenter.dao.sale.MIncentivePolicyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.IncentivePolicy">
        <id column="id" property="id" />
        <result column="fk_company_id" property="fkCompanyId" />
        <result column="fk_area_country_id" property="fkAreaCountryId" />
        <result column="fk_institution_provider_id" property="fkInstitutionProviderId" />
        <result column="fk_institution_ids" property="fkInstitutionIds" />
        <result column="num" property="num" />
        <result column="year" property="year" />
        <result column="activity_time_note" property="activityTimeNote" />
        <result column="submitted_time_note" property="submittedTimeNote" />
        <result column="deposit_paid_time_note" property="depositPaidTimeNote" />
        <result column="enrolled_time_note" property="enrolledTimeNote" />
        <result column="condition_note" property="conditionNote" />
        <result column="reward_note" property="rewardNote" />
        <result column="condition_json" jdbcType="VARCHAR" property="conditionJson" javaType="com.get.salecenter.vo.IncentivePolicyConditionVo" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
        <result column="reward_json" jdbcType="VARCHAR" property="rewardJson" javaType="com.get.salecenter.vo.IncentivePolicyRewardVo" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
        <result column="remark" property="remark" />
        <result column="view_order" property="viewOrder" />
        <result column="public_level" property="publicLevel" />
        <result column="status" property="status" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_create_user" property="gmtCreateUser" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="gmt_modified_user" property="gmtModifiedUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, fk_company_id, fk_area_country_id, fk_institution_provider_id, fk_institution_ids, num, year, activity_time_note, submitted_time_note, deposit_paid_time_note, enrolled_time_note, condition_note, reward_note, condition_json, reward_json, remark, view_order, public_level, status, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
    </sql>

    <select id="getInstitutionProviderSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
        provider.id,
        concat(
        IF
            ( provider.is_active = 0, '【无效】', '' ),
        CASE
                WHEN IFNULL( provider.name_chn, '' ) = '' THEN
                provider.NAME ELSE CONCAT( provider.NAME, '（', provider.name_chn, '）' )
            END
            ) NAME,
            provider.name_chn,
            provider.is_active STATUS
        FROM
            ais_institution_center.m_institution_provider provider
            INNER JOIN ais_sale_center.m_incentive_policy policy ON provider.id = policy.fk_institution_provider_id
        WHERE
            policy.fk_company_id = #{companyId}
        GROUP BY provider.id
        ORDER BY
		provider.is_active DESC,
		provider.id ASC
    </select>

    <select id="getInstitutionListByProviderId" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        select i.id,
               i.name,
               i.name_chn,
               concat(
                       IF
                           (i.is_active = 0, '【无效】', ''),
                       CASE
                           WHEN IFNULL(i.name_chn, '') = '' THEN
                               i.NAME
                           ELSE CONCAT(i.NAME, '（', i.name_chn, '）')
                           END
                   ) fullName,i.is_active status
        from ais_institution_center.m_institution i
                 inner join ais_sale_center.m_incentive_policy p on FIND_IN_SET(i.id,p.fk_institution_ids)
        where p.fk_institution_provider_id = #{providerId}
        GROUP BY i.id
    </select>

</mapper>
