package com.get.resumecenter.service.Impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.resumecenter.dao.ResumeWorkMapper;
import com.get.resumecenter.vo.ResumeWorkVo;
import com.get.resumecenter.entity.ResumeWork;
import com.get.resumecenter.service.IIndustryTypeService;
import com.get.resumecenter.service.IResumeWorkService;
import com.get.resumecenter.dto.ResumeWorkDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/8/5
 * @TIME: 11:43
 * @Description: 工作经验实现类
 **/
@Service
public class ResumeWorkImpl implements IResumeWorkService {
    @Resource
    private ResumeWorkMapper workMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IIndustryTypeService industryTypeService;


    @Override
    public List<ResumeWorkVo> getResumeWorkListDto(Long resumeId) {
        if (GeneralTool.isEmpty(resumeId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("resume_id_null"));
        }
//        Example example = new Example(ResumeWork.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkResumeId", resumeId);
//        example.orderBy("startDate").desc();
//        List<ResumeWork> resumeWorks = workMapper.selectByExample(example);
        List<ResumeWork> resumeWorks = workMapper.selectList(Wrappers.<ResumeWork>lambdaQuery().eq(ResumeWork::getFkResumeId, resumeId).orderByDesc(ResumeWork::getStartDate));
        List<ResumeWorkVo> collect = resumeWorks.stream().map(resumeWork -> BeanCopyUtils.objClone(resumeWork, ResumeWorkVo::new)).collect(Collectors.toList());
        for (ResumeWorkVo resumeWorkVo : collect) {
            resumeWorkVo.setFkIndustryTypeName(industryTypeService.getNameByTypeId(resumeWorkVo.getFkIndustryTypeId()));
        }
        return collect;
    }

    @Override
    public ResumeWorkVo getResumeWorkById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ResumeWork resumeWork = workMapper.selectById(id);
        return BeanCopyUtils.objClone(resumeWork, ResumeWorkVo::new);
    }

    @Override
    public Long addResumeWork(ResumeWorkDto resumeWorkDto) {
//        if (!StaffContext.getStaff().getIsAdmin()) {
//            if (!StaffContext.getStaff().getIsModifiedResume()) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
//            }
//        }
        if (!GetAuthInfo.isAdmin()) {
            if (!SecureUtil.getIsModifiedResume()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
            }
        }
        if (GeneralTool.isEmpty(resumeWorkDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ResumeWork resumeWork = BeanCopyUtils.objClone(resumeWorkDto, ResumeWork::new);
        utilService.updateUserInfoToEntity(resumeWork);
        int i = workMapper.insertSelective(resumeWork);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return resumeWork.getId();
    }

    @Override
    public ResumeWorkVo updateResumeWork(ResumeWorkDto resumeWorkDto) {
//        if (!StaffContext.getStaff().getIsAdmin()) {
//            if (!StaffContext.getStaff().getIsModifiedResume()) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
//            }
//        }
        if (!GetAuthInfo.isAdmin()) {
            if (!SecureUtil.getIsModifiedResume()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
            }
        }
        if (GeneralTool.isEmpty(resumeWorkDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ResumeWork resumeWork = BeanCopyUtils.objClone(resumeWorkDto, ResumeWork::new);
        utilService.updateUserInfoToEntity(resumeWork);
        workMapper.updateById(resumeWork);
        return getResumeWorkById(resumeWork.getId());
    }

    @Override
    public void deleteResumeWork(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        workMapper.deleteById(id);
    }
}
