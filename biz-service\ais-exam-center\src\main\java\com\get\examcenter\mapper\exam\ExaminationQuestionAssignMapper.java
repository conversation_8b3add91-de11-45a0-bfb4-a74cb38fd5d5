package com.get.examcenter.mapper.exam;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.examcenter.entity.ExaminationQuestionAssign;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DS("examdb")
public interface ExaminationQuestionAssignMapper extends BaseMapper<ExaminationQuestionAssign> {
    int insert(ExaminationQuestionAssign record);

    int insertSelective(ExaminationQuestionAssign record);

    int updateByPrimaryKeySelective(ExaminationQuestionAssign record);

    int updateByPrimaryKey(ExaminationQuestionAssign record);

    List<ExaminationQuestionAssign> getListByPaperIdAndExist(Long fkExaminationPaperId);

    int updateActiveById(Long id);

    /**
     * 校代考卷关系 全删全增
     * @param tel
     * @param fkExaminationId
     */
    void deleteByTel(@Param("tel") String tel, @Param("fkExaminationId") Long fkExaminationId);
}