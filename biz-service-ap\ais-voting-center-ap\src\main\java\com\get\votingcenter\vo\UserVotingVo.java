package com.get.votingcenter.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseEntity;
import com.get.votingcenter.entity.UserVoting;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.util.Date;

/**
 * @author: Hardy
 * @create: 2021/9/27 16:34
 * @verison: 1.0
 * @description:
 */
@Data
public class UserVotingVo  extends BaseEntity {
    /**
     * 排名
     */
    @ApiModelProperty(value = "排名")
    private Integer rank;

    /**
     * 投票总数
     */
    @ApiModelProperty(value = "投票总数")
    private Integer totalVoteCount;

    /**
     * 注册中心的user id
     */
    @ApiModelProperty(value = "注册中心的user id")
    @Column(name = "fk_user_id")
    private Long fkUserId;
    /**
     * 投票选项Id
     */
    @ApiModelProperty(value = "投票选项Id")
    @Column(name = "fk_voting_item_option_id")
    private Long fkVotingItemOptionId;
    /**
     * 投票票数
     */
    @ApiModelProperty(value = "投票票数")
    @Column(name = "vote_count")
    private Integer voteCount;

}
