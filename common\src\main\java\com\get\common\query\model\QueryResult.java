package com.get.common.query.model;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.get.common.result.Page;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 查询结果封装类
 * 包含QueryWrapper和分页信息
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */
@Data
@AllArgsConstructor
public class QueryResult<T> {
    
    /**
     * 查询条件包装器
     */
    private QueryWrapper<T> wrapper;
    
    /**
     * 分页信息
     */
    private Page page;
    
    /**
     * 获取分页的当前页码
     */
    public Integer getCurrentPage() {
        return page != null ? page.getCurrentPage() : null;
    }
    
    /**
     * 获取分页的页大小
     */
    public Integer getShowCount() {
        return page != null ? page.getShowCount() : null;
    }
    
    /**
     * 是否需要分页
     */
    public boolean needPaging() {
        return page != null && page.getShowCount() != null && page.getShowCount() > 0;
    }
}