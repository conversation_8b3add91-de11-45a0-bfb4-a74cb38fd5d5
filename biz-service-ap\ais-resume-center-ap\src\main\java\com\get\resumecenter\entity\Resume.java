package com.get.resumecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("m_resume")
public class Resume extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;
    /**
     * 简历类型Id
     */
    @ApiModelProperty(value = "简历类型Id")
    @Column(name = "fk_resume_type_id")
    private Long fkResumeTypeId;
    /**
     * 简历guid(人才中心)
     */
    @ApiModelProperty(value = "简历guid(人才中心)")
    @Column(name = "resume_guid")
    private String resumeGuid;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @Column(name = "name")
    private String name;
    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男")
    @Column(name = "gender")
    private Integer gender;
    /**
     * 生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "生日")
    @Column(name = "birthday")
    private Date birthday;
    /**
     * 身高（cm）
     */
    @ApiModelProperty(value = "身高（cm）")
    @Column(name = "height")
    private Integer height;
    /**
     * 开始工作年份
     */
    @ApiModelProperty(value = "开始工作年份")
    @Column(name = "start_working_year")
    private Integer startWorkingYear;
    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    @Column(name = "identity_card")
    private String identityCard;
    /**
     * 国籍
     */
    @ApiModelProperty(value = "国籍")
    @Column(name = "nationality")
    private String nationality;
    /**
     * 户口所在地
     */
    @ApiModelProperty(value = "户口所在地")
    @Column(name = "residence")
    private String residence;
    /**
     * 婚姻状态：未婚/已婚/保密
     */
    @ApiModelProperty(value = "婚姻状态：未婚/已婚/保密")
    @Column(name = "marriage")
    private String marriage;
    /**
     * 政治面貌：党员/预备党员/团员/民主党派人士/无党派民主人士/普通公民
     */
    @ApiModelProperty(value = "政治面貌：党员/预备党员/团员/民主党派人士/无党派民主人士/普通公民")
    @Column(name = "political")
    private String political;
    /**
     * 住址电话
     */
    @ApiModelProperty(value = "住址电话")
    @Column(name = "home_tel")
    private String homeTel;
    /**
     * 手机区号
     */
    @ApiModelProperty(value = "手机区号")
    @Column(name = "mobile_area_code")
    private String mobileAreaCode;
    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    @Column(name = "mobile")
    private String mobile;
    /**
     * Email
     */
    @ApiModelProperty(value = "Email")
    @Column(name = "email")
    private String email;
    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    @Column(name = "zip_code")
    private String zipCode;
    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    @Column(name = "address")
    private String address;
    /**
     * QQ号
     */
    @ApiModelProperty(value = "QQ号")
    @Column(name = "qq")
    private String qq;
    /**
     * 微信号
     */
    @ApiModelProperty(value = "微信号")
    @Column(name = "wechat")
    private String wechat;
    /**
     * whatsapp号
     */
    @ApiModelProperty(value = "whatsapp号")
    @Column(name = "whatsapp")
    private String whatsapp;
}