package com.get.aismail.vo;

import com.get.aismail.dto.AttachedDelete;
import com.get.aismail.entity.MFileMail;
import lombok.Data;

import java.util.List;

@Data
public class SendMailVo {
    // 邮箱用户名
    private String emailAccount;
    // 收件人
    private String recipient;
    // 抄送人
    private String ccPeople;
    // 密送人
    private String bccPeople;
    // 主题
    private String subject;
    // 内容
    private String content;
    // 如果是草稿箱会有需要删除的附件
    private List<AttachedDelete> attachedDeletes;
    // 邮件id
    private String mailId;
    // id
    private Long id;
    // 附件内容
    private List<MFileMail> attachments;
}
