package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/12/19
 * @TIME: 16:30
 * @Description:
 **/
@Data
public class PeriodicStatisticsVo {
    /**
     * 周统计
     */
    @ApiModelProperty(value = "周统计")
    private List<StatisticsVo> weekStatistics;

    /**
     * 周报变化时间点
     */
    @ApiModelProperty("周报变化时间点")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date weekStatisticsChangeDate;

    /**
     * 月统计
     */
    @ApiModelProperty(value = "月统计")
    private List<StatisticsVo> monthStatistics;

    /**
     * 月报变化时间点
     */
    @ApiModelProperty("月报变化时间点")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date monthStatisticsChangeDate;

    /**
     * 测试时间
     */
    @ApiModelProperty("测试时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date testDate;

    /**
     * 季统计
     */
    @ApiModelProperty(value = "季度统计")
    private List<StatisticsVo> quarterStatistics;
    /**
     * 年统计
     */
    @ApiModelProperty(value = "年统计")
    private List<StatisticsVo> yearStatistics;
}
