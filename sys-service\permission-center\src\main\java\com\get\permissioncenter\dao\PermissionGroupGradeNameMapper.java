package com.get.permissioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.permissioncenter.entity.PermissionGroupGradeName;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface PermissionGroupGradeNameMapper extends BaseMapper<PermissionGroupGradeName> {
//    int insert(PermissionGroupGradeName record);

    int insertSelective(PermissionGroupGradeName record);

    /**
     * @return java.lang.String
     * @Description: 获取等级组别名称
     * @Param groupId, gradeId
     * <AUTHOR>
     */
    String selectNameByGroupAndGrade(@Param("groupId") Long groupId, @Param("gradeId") Long gradeId);

    /**
     * @return PermissionGroupGradeName
     * @Description: 获取等级组别名称
     * @Param groupId, gradeId
     * <AUTHOR>
     */
    PermissionGroupGradeName selectByGroupAndGrade(@Param("groupId") Long groupId, @Param("gradeId") Long gradeId);
}