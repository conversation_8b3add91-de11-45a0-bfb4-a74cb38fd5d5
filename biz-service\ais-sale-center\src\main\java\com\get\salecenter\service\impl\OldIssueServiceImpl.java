package com.get.salecenter.service.impl;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.StyleSet;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.common.result.SearchBean;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.vo.AplOrderVo;
import com.get.salecenter.dao.newissue.AplOldIssueOrderMapper;
import com.get.salecenter.dao.sale.StudentOfferItemMapper;
import com.get.salecenter.vo.StudentProjectRoleStaffVo;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.entity.AplOldIssueOrder;
import com.get.salecenter.service.IAgentService;
import com.get.salecenter.service.IOldIssueService;
import com.get.salecenter.dto.AplOldIssueOrderDto;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/3/15 20:19
 */
@Service
public class OldIssueServiceImpl implements IOldIssueService {

    @Resource
    private AplOldIssueOrderMapper aplOldOrderMapper;
    @Resource
    private StudentOfferItemMapper studentOfferItemMapper;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IAgentService agentService;

    @Override
    public List<AplOrderVo> getRpaTableList(AplOldIssueOrderDto data, Page page) {
        List<AplOrderVo> rpaTableList = null;
        if (GeneralTool.isEmpty(data.getFkCompanyIds())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_id_null"));
        }
        String name = data.getName();
        if (StringUtils.isNotBlank(name)) {
            name = name.replace(" ", "").trim().toLowerCase();
        }
        List<Long> staffIdsByNameKey = new ArrayList<>();
        if (GeneralTool.isNotEmpty(data.getProjectStaffName())) {
            staffIdsByNameKey = permissionCenterClient.getStaffIdsByNameKey(data.getProjectStaffName());
        }
        //找当前登录人下属包括自己
        List<Long> staffList = new ArrayList<>();
        Long fkStaffId = SecureUtil.getStaffId();
        Result<List<Long>> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(fkStaffId);
        if (GeneralTool.isNotEmpty(staffFollowerIds.getData())) {
            staffList.addAll(staffFollowerIds.getData());
            staffList.add(fkStaffId);
        } else {
            //添加自己
            staffList.add(fkStaffId);
        }
        //登录员工业务国家
        List<Long> countryIds1 = SecureUtil.getCountryIds();
        if (GeneralTool.isEmpty(countryIds1)) {
            return rpaTableList;
        }
        //Long companyId = SecureUtil.validateCompany(GeneralTool.isNotEmpty(data.getFkCompanyId()) ? data.getFkCompanyId() : null) ? data.getFkCompanyId() : null;

        IPage<AplOrderVo> ipage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        rpaTableList = studentOfferItemMapper.getRpaTableListCondition(ipage, data.getOrderId(), name, staffIdsByNameKey,
                staffList,data.getFkCompanyIds(), countryIds1, SecureUtil.getStaffInfo().getIsStudentAdmin(),
                data.getFkContactPersonEmail(),SecureUtil.getInstitutionIds(), SecureUtil.getIsStudentOfferItemFinancialHiding(),
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds());


        page.setAll((int) ipage.getTotal());
        page.setCurrentResult((int) ipage.getSize());

        if (GeneralTool.isNotEmpty(rpaTableList)) {

            List<Integer> orderIds = rpaTableList.stream().map(AplOrderVo::getOrderId).collect(Collectors.toList());
            Set<Long> countryIds = rpaTableList.stream().map(d -> Long.valueOf(d.getCountryName())).collect(Collectors.toSet());
            Set<Long> schoolId = rpaTableList.stream().filter(d -> NumberUtils.isNumber(d.getSchoolName())).map(d -> Long.valueOf(d.getSchoolName())).collect(Collectors.toSet());
            Set<Long> courseIds = rpaTableList.stream().filter(d -> NumberUtils.isNumber(d.getCourseName())).map(d -> Long.valueOf(d.getCourseName())).collect(Collectors.toSet());
            List<Long> itemId = rpaTableList.stream().map(AplOrderVo::getId).collect(Collectors.toList());
            Set<Long> staffIds = rpaTableList.stream().filter(d -> GeneralTool.isNotEmpty(d.getBdName())).map(d -> Long.valueOf(d.getBdName())).collect(Collectors.toSet());
            Set<Long> companyIds = rpaTableList.stream().map(AplOrderVo::getFkCompanyId).collect(Collectors.toSet());
            List<StudentProjectRoleStaffVo> projectName = studentOfferItemMapper.getProjectName(itemId);
            Set<Long> agentIds = rpaTableList.stream().map(AplOrderVo::getFkAgentId).collect(Collectors.toSet());
            Map<Long, Agent> agentsByIds = agentService.getAgentsByIds(agentIds);
            Set<Long> staffids = projectName.stream().filter(Objects::nonNull).filter(d -> GeneralTool.isNotEmpty(d.getFkStaffId())).map(StudentProjectRoleStaffVo::getFkStaffId).collect(Collectors.toSet());
            staffIds.addAll(staffids);
            Map<Long, String> staffNameMap = permissionCenterClient.getStaffEnNameByIds(staffIds);

            Result<Map<Long, String>> companyNamesByIds = permissionCenterClient.getCompanyNamesByIds(companyIds);
            Map<Long, String> companyMap = new HashMap<>();
            if (GeneralTool.isNotEmpty(companyNamesByIds)) {
                companyMap = companyNamesByIds.getData();
            }
            Map<Long, String> companyMapFinal = companyMap;


            Iterator<StudentProjectRoleStaffVo> iterator = projectName.iterator();

            while (iterator.hasNext()) {
                StudentProjectRoleStaffVo next = iterator.next();
                for (AplOrderVo aplOrderVo : rpaTableList) {
                    if (next.getId().equals(aplOrderVo.getId())) {
                        if (GeneralTool.isEmpty(aplOrderVo.getProjeckName())) {
                            aplOrderVo.setProjeckName(staffNameMap.get(next.getFkStaffId()));
                        } else {
                            String projeckName = aplOrderVo.getProjeckName();
                            aplOrderVo.setProjeckName(projeckName + ", " + staffNameMap.get(next.getFkStaffId()));
                        }
                    }
                }
                iterator.remove();
            }

            Map<Long, String> institutionNamesByIds = institutionCenterClient.getInstitutionNamesByIds(schoolId).getData();
            Map<Long, String> countryNamesByIds = institutionCenterClient.getCountryNamesByIds(countryIds).getData();
            Map<Integer, AplOldIssueOrder> map = aplOldOrderMapper.getListByOrderIds(orderIds).stream().collect(Collectors.toMap(AplOldIssueOrder::getOrderId, d -> d));
            Map<Long, String> courseNameByIds = institutionCenterClient.getInstitutionCourseNamesByIds(courseIds).getData();
            rpaTableList.forEach(d -> {
                Long agentId = d.getFkAgentId();
                if (agentsByIds.containsKey(agentId)) {
                    String nameNote = agentsByIds.get(agentId).getNameNote();
                    if (StringUtils.isNotBlank(nameNote)) {
                        d.setAgentName(d.getAgentName() + "(" + nameNote + ")");
                    }
                }
                AplOldIssueOrder aplOldIssueOrder = map.get(d.getOrderId());
                if (aplOldIssueOrder != null) {
                    d.setCountryName(countryNamesByIds.get(Long.valueOf(d.getCountryName())));
                    if (GeneralTool.isNotEmpty(institutionNamesByIds) && NumberUtils.isNumber(d.getSchoolName())) {
                        d.setSchoolName(institutionNamesByIds.get(Long.valueOf(d.getSchoolName())));
                    }
                    if (GeneralTool.isNotEmpty(courseNameByIds) && NumberUtils.isNumber(d.getCourseName())) {
                        d.setCourseName(courseNameByIds.get(Long.valueOf(d.getCourseName())));
                    }
                    Optional.ofNullable(d.getBdName()).ifPresent(dd -> {
                        d.setBdName(staffNameMap.get(Long.valueOf(dd)));
                    });
                    d.setCompanyName(companyMapFinal.get(d.getFkCompanyId()));
                    d.setRpaState(aplOldIssueOrder.getRobotStatus());
                    d.setRpaAccount(aplOldIssueOrder.getCdetSchLoginName());
                    d.setRpaPassword(aplOldIssueOrder.getCdetSchLoginPw());
                    d.setEmail(aplOldIssueOrder.getEmail());
                    d.setRpaRobotReturnMsg(aplOldIssueOrder.getRobotReturnMsg());
                    d.setPeopleMsg(aplOldIssueOrder.getPeopleMsg());
                }
            });
            if (GeneralTool.isNotBlank(data.getFkStudentEmail())) {
                rpaTableList = rpaTableList.stream().filter(d -> d.getEmail().equals(data.getFkStudentEmail())).collect(Collectors.toList());
            }
        }
        return rpaTableList;
    }

    @Override
    public void exportRobotTable(HttpServletResponse response, SearchBean<AplOldIssueOrderDto> page) {
        List<AplOrderVo> rpaTableList = this.getRpaTableList(page.getData(), page);
        ServletOutputStream out = null;
        ExcelWriter writer = ExcelUtil.getWriter(true);

        try {
            Map<String, String> fileMap = getFileMap(AplOrderVo.class);
            writer.renameSheet("RobotTable");
            Sheet sheet = writer.getSheet();
//            setSizeColumn(sheet, fileMap.size());
            FileUtils.setSizeColumn(sheet, fileMap.size());
            //样式
            StyleSet styleSet = writer.getStyleSet();
            CellStyle headCellStyle = styleSet.getHeadCellStyle();
            //字体样式
            Font font = writer.createFont();
            font.setBold(true);
            font.setFontName("微软雅黑");
            headCellStyle.setFont(font);
            headCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            CellStyle cellStyle = styleSet.getCellStyle();
            cellStyle.setWrapText(true);
            //设置字段,表头
            for (Map.Entry<String, String> field : fileMap.entrySet()) {
                writer.addHeaderAlias(field.getValue(), field.getKey());
            }
            writer.write(rpaTableList, true);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");

            //文件名          文件的后缀
            response.setHeader("Content-Disposition", "attachment;filename=" + "RobotTable" + ".xlsx");

            out = response.getOutputStream();
            writer.flush(out, true);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            IoUtil.close(out);
            writer.close();
        }


    }

    private Map<String, String> getFileMap(Class beanType) {
        Map<String, String> fileMap = new LinkedHashMap<>(7);
        Field[] fields = ReflectUtil.getFields(beanType);
        for (Field field : fields) {
            ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
            if (annotation != null) {
                fileMap.put(annotation.value(), field.getName());
            }
        }
        return fileMap;
    }


//    private void setSizeColumn(Sheet sheet, int size) {
//        for (int columnNum = 0; columnNum < size; columnNum++) {
//            int columnWidth = sheet.getColumnWidth(columnNum) / 256;
//            for (int rowNum = 0; rowNum < sheet.getLastRowNum(); rowNum++) {
//                Row currentRow;
//                //当前行未被使用过
//                if (sheet.getRow(rowNum) == null) {
//                    currentRow = sheet.createRow(rowNum);
//                } else {
//                    currentRow = sheet.getRow(rowNum);
//                }
//                if (currentRow.getCell(columnNum) != null) {
//                    Cell currentCell = currentRow.getCell(columnNum);
//                    if (currentCell.getCellType() == CellType.STRING) {
//                        int length = currentCell.getStringCellValue().getBytes().length;
//                        if (columnWidth < length) {
//                            columnWidth = length;
//                        }
//                    }
//                }
//            }
//            sheet.setColumnWidth(columnNum, columnWidth * 512);
//        }
//    }
}
