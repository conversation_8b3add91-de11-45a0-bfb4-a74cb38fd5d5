package com.get.salecenter.service;

import com.get.salecenter.dto.NewIssueUserSuperiorDto;

import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/10/25 14:28
 */
public interface IIssueService {
//TODO 注释ISSUE相关功能 lucky  2024/12/23
    //void getSetIssueUserSuperior(Long fkUserId, List<NewIssueUserSuperiorDto> userSuperiorVos, Long fkCompanyId);

   // NewIssueUserSuperiorVo getIssueUserSuperior(Long userId, Long fkAgentId, Long fkCompanyId);

    //void getRemoveIssueUserSuperior(Long fkUserId, List<NewIssueUserSuperiorDto> userSuperiorVos, Long fkCompanyId);

  //  List<UserInfoDto> getIssueUserSubordinate(long fkUserId, long fkAgentId, Long fkCompanyId);

   // void updateIssueStudentInstitutionCourse(EventOfferPlanDto offerItemVo, StudentOfferItem offerItem,Long issueCourseId,Boolean isUpdateCourse);

    //void updateIssueStudentInstitutionCourseStatus(Long issueCourseId,Long status);
}
