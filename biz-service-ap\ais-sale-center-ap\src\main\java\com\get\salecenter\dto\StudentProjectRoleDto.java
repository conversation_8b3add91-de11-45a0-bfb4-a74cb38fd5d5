package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: Sea
 * @create: 2020/11/2 16:24
 * @verison: 1.0
 * @description:
 */
@Data
public class StudentProjectRoleDto extends BaseVoEntity {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 角色名称
     */
    @ApiModelProperty(value = "角色名称")
    private String roleName;

    /**
     * 角色Key(系统使用)
     */
    @ApiModelProperty(value = "角色Key(系统使用)")
    private String roleKey;

    /**
     * 部门编号引用，可以多选，用逗号隔开，如：D001,D002
     */
    @ApiModelProperty(value = "部门编号引用，可以多选，用逗号隔开，如：D001,D002")
    private String departmentNum;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
   
}
