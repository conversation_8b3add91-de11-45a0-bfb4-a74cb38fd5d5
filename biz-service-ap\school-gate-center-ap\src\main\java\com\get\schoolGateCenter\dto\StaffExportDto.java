package com.get.schoolGateCenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class StaffExportDto {

    private Long id;

    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 性别：
     */
    @ApiModelProperty(value = "性别")
    private String genderName;

    private Integer gender;

    @ApiModelProperty(value = "员工编号")
    private String num;

    private Long companyId;
    @ApiModelProperty(value = "所属分公司")
    private String companyName;

    @ApiModelProperty(value = "部门")
    private String departmentName;

    @ApiModelProperty(value = "岗位")
    private String positionName;

    @ApiModelProperty(value = "直属上司")
    private String supervisorName;


    @ApiModelProperty(value = "身份证")
    private String identityNum;

    @ApiModelProperty(value = "家用电话")
    private String homeTel;

    @ApiModelProperty(value = "手机")
    private String mobile;

    @ApiModelProperty(value = "Email")
    private String email;

    @ApiModelProperty(value = "邮编")
    private String zipCode;

    @ApiModelProperty(value = "联系地址")
    private String address;

    @ApiModelProperty(value = "紧急联系人")
    private String emergencyContact;

    @ApiModelProperty(value = "紧急联系人关系")
    private String emergencyRelationship;

    @ApiModelProperty(value = "紧急联系人电话")
    private String emergencyTel;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "生日")
    private Date birthday;

    private Integer hukou;

    @ApiModelProperty("户口")
    private String hukouName;

    @ApiModelProperty(value = "业务上司")
    private String superiorNames;

    @ApiModelProperty(value = "负责业务国家")
    private String areaCountryDtos;

    @ApiModelProperty(value = "参与职务分公司")
    private String officeNames;


    @ApiModelProperty(value = "所属权限组别及等级")
    private String groupName;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "入职时间")
    private Date entryDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "转正时间")
    private Date passProbationDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "最新合同开始时间")
    private Date startTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "最新合同结束时间")
    private Date endTime;

    @ApiModelProperty(value = "工作电话")
    private String workTel;

}
