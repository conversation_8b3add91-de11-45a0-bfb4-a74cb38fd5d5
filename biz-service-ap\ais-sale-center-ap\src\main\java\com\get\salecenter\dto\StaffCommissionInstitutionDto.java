package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: Hardy
 * @create: 2023/2/7 16:44
 * @verison: 1.0
 * @description:
 */
@Data
public class StaffCommissionInstitutionDto extends BaseVoEntity {

    @ApiModelProperty(value = "关键词")
    private String keyWord;
    /**
     * 学校Id
     */
    @NotNull(message = "学校Id", groups = {Add.class,Update.class})
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

    /**
     * 状态，枚举：1激活提成
     */
    @ApiModelProperty(value = "状态，枚举：1激活提成")
    private Integer status = 1;

    private static final long serialVersionUID = 1L;

  
}
