package com.get.partnercenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.partnercenter.dto.HighCommissionPutAwayParamsDto;
import com.get.partnercenter.vo.HighCommissionComboxVo;
import com.get.partnercenter.vo.HighCommissionVo;
import com.get.partnercenter.entity.MInstitutionHighCommissionEntity;
import com.get.partnercenter.dto.HighCommissionDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_institution_high_commission(学校高佣表)】的数据库操作Mapper
* @createDate 2024-12-05 11:23:44
* @Entity com.get.partnercenter.entity.MInstitutionHighCommission
*/
@Mapper
public interface MInstitutionHighCommissionMapper extends BaseMapper<MInstitutionHighCommissionEntity> {

    List<HighCommissionVo> searchPage(IPage<HighCommissionVo> page, @Param("query") HighCommissionDto params);


    int updatePutAway(HighCommissionPutAwayParamsDto dto);


    HighCommissionVo getDetail(HighCommissionDto params);

    List<HighCommissionComboxVo> resultList(HighCommissionDto commissionDto);

    List<HighCommissionComboxVo> resulOldPMPtList(HighCommissionDto commissionDto);


}




