package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 代理项目成员Vo类
 *
 * <AUTHOR>
 * @date 2021/8/2 11:36
 */
@Data
public class AgentRoleStaffDto extends BaseVoEntity {

    /**
     * 代理Id
     */
    @ApiModelProperty(value = "代理Id")
//    @NotNull(message = "代理Id不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private Long fkAgentId;

    /**
     * 学生项目角色Id
     */
    @ApiModelProperty(value = "学生项目角色Id")
    @NotNull(message = "学生项目角色Id", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private Long fkStudentProjectRoleId;

    @ApiModelProperty(value = "学生项目角色Id")
    private Long studentProjectRoleId;

    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    @NotNull(message = "员工Id", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private Long fkStaffId;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @NotNull(message = "公司Id", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private Long fkCompanyId;

    /**
     * 国家Id，可不选，不选为适合所有国家线
     */
    @ApiModelProperty(value = "国家Id，可不选，不选为适合所有国家线")
    private Long fkAreaCountryId;

    /**
     * 国家Id，可不选，不选为适合所有国家线(多选)
     */
    @ApiModelProperty(value = "国家Id，可不选，不选为适合所有国家线(多选)")
    private List<Long> fkAreaCountryIds;

    /**
     * 州省Id
     */
    @ApiModelProperty(value = "州省Id")
    private Long fkAreaStateId;

    /**
     * 城市Id
     */
    @ApiModelProperty(value = "城市Id")
    private Long fkAreaCityId;

    /**
     * BD编号（4位）
     */
    @ApiModelProperty(value = "BD编号（4位）")
    private String bdCode;

    @ApiModelProperty(value = "关键词")
    private String keyWord;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

    /**
     * 目标类型关键字，枚举：m_student_offer留学申请/m_student_insurance留学保险/m_student_accommodation留学住宿
     */
    @ApiModelProperty(value = "目标类型关键字，枚举：m_student_offer留学申请/m_student_insurance留学保险/m_student_accommodation留学住宿")
//    @NotBlank(message = "业务类型", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private String fkTypeKey;

    /**
     * 目标类型关键字，枚举：m_student_offer留学申请/m_student_insurance留学保险/m_student_accommodation留学住宿
     */
    @ApiModelProperty(value = "目标类型关键字，枚举：m_student_offer留学申请/m_student_insurance留学保险/m_student_accommodation留学住宿")
    private String batchUpdateTypeKey;

    @ApiModelProperty(value = "分配项目成员")
    private List<AgentRoleStaffDto> agentRoleStaffVoList;

    @ApiModelProperty(value = "业务国家线")
    private Long agentCountryId;

    /**
     * 代理所在国家Id
     */
    @ApiModelProperty(value = "代理所在国家Id")
    private Long fkAreaCountryIdAgent;

    /**
     * 代理所在州省Id
     */
    @ApiModelProperty(value = "代理所在州省Id")
    private Long fkAreaStateIdAgent;

    /**
     * 代理所在州省Ids(多选)
     */
    @ApiModelProperty(value = "代理所在州省Ids(多选)")
    private List<Long> fkAreaStateIdAgentList;
}
