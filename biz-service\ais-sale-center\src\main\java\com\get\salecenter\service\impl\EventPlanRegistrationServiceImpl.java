package com.get.salecenter.service.impl;
import cn.hutool.poi.excel.BigExcelWriter;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.DateUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.institutioncenter.vo.InstitutionProviderVo;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.remindercenter.entity.RemindTemplate;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.remindercenter.dto.RemindTaskDto;
import com.get.salecenter.dao.sale.EventPlanRegistrationMapper;
import com.get.salecenter.vo.*;
import com.get.salecenter.vo.EventPlanRegistrationVo;
import com.get.salecenter.entity.EventPlan;
import com.get.salecenter.entity.EventPlanRegistration;
import com.get.salecenter.entity.EventPlanRegistrationContactPerson;
import com.get.salecenter.entity.EventPlanRegistrationEvent;
import com.get.salecenter.entity.EventPlanTheme;
import com.get.salecenter.entity.EventPlanThemeOffline;
import com.get.salecenter.entity.EventPlanThemeOfflineItem;
import com.get.salecenter.entity.EventPlanThemeOnline;
import com.get.salecenter.entity.EventPlanThemeWorkshop;
import com.get.salecenter.service.EventPlanRegistrationContactPersonService;
import com.get.salecenter.service.EventPlanRegistrationEventService;
import com.get.salecenter.service.EventPlanRegistrationService;
import com.get.salecenter.service.EventPlanService;
import com.get.salecenter.service.EventPlanThemeOfflineItemService;
import com.get.salecenter.service.EventPlanThemeOfflineService;
import com.get.salecenter.service.EventPlanThemeOnlineService;
import com.get.salecenter.service.EventPlanThemeService;
import com.get.salecenter.service.EventPlanThemeWorkshopService;
import com.get.salecenter.dto.EventPlanRegistrationFormDto;
import com.get.salecenter.dto.EventPlanRegistrationSearchDto;
import com.get.salecenter.dto.EventPlanRegistrationDto;
import com.get.salecenter.dto.OfflineLocationSelectDto;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
@Service
public class EventPlanRegistrationServiceImpl extends BaseServiceImpl<EventPlanRegistrationMapper, EventPlanRegistration> implements EventPlanRegistrationService {

    @Resource
    private UtilService utilService;

    @Resource
    private EventPlanRegistrationMapper eventPlanRegistrationMapper;

    @Resource
    private EventPlanRegistrationContactPersonService personService;

    @Resource
    private EventPlanRegistrationEventService registrationEventService;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Resource
    @Lazy
    private EventPlanService eventPlanService;

    @Resource
    @Lazy
    private EventPlanThemeService eventPlanThemeService;

    @Resource
    @Lazy
    private EventPlanThemeOnlineService onlineService;

    @Resource
    @Lazy
    private EventPlanThemeOfflineService offlineService;

    @Resource
    @Lazy
    private EventPlanThemeOfflineItemService offlineItemService;

    @Resource
    @Lazy
    private EventPlanThemeWorkshopService workshopService;

    @Resource
    private IReminderCenterClient reminderCenterClient;

    @Resource(name = "saleTaskExecutor")
    private ThreadPoolTaskExecutor saleTaskExecutor;



    @Transactional(rollbackFor = Exception.class)
    @Override
    public void eventPlanRegistration(EventPlanRegistrationFormDto registrationFormVo) {
        if (GeneralTool.isEmpty(registrationFormVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }

        if(GeneralTool.isNotEmpty(registrationFormVo.getId())){
            EventPlanRegistration registration = BeanCopyUtils.objClone(registrationFormVo, EventPlanRegistration::new);
            utilService.setUpdateInfo(registration);
            eventPlanRegistrationMapper.updateById(registration);
            //删除联系人
            personService.remove(Wrappers.<EventPlanRegistrationContactPerson>lambdaQuery().eq(EventPlanRegistrationContactPerson::getFkEventPlanRegistrationId,registrationFormVo.getId()));
            //删除报名名册所有报名项目（保留状态为取消的项目）
            registrationEventService.remove(Wrappers.<EventPlanRegistrationEvent>lambdaQuery()
                    .eq(EventPlanRegistrationEvent::getFkEventPlanRegistrationId,registrationFormVo.getId())
                    .and(wrapper->{
                        wrapper.eq(EventPlanRegistrationEvent::getIsCancel,false)
                                .or()
                                .isNull(EventPlanRegistrationEvent::getIsCancel);

                    })
            );
        }else{
            EventPlanRegistration registration = BeanCopyUtils.objClone(registrationFormVo, EventPlanRegistration::new);
            utilService.setCreateInfo(registration);
            int insert = eventPlanRegistrationMapper.insert(registration);
            if(insert <= 0){
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
            registrationFormVo.setId(registration.getId());
        }

        //新增联系人
        if(GeneralTool.isNotEmpty(registrationFormVo.getPersonFormVoList())){
            List<EventPlanRegistrationContactPerson> eventPlanRegistrationContactPeople = BeanCopyUtils.copyListProperties(registrationFormVo.getPersonFormVoList(), EventPlanRegistrationContactPerson::new);
            for(EventPlanRegistrationContactPerson person:eventPlanRegistrationContactPeople){
                person.setFkEventPlanRegistrationId(registrationFormVo.getId());
                utilService.setCreateInfo(person);
                personService.save(person);
            }
        }

        //参加线上活动
        if(GeneralTool.isNotEmpty(registrationFormVo.getFkEventPlanThemeOnlineIds())){
            insertRegistrationEvent(registrationFormVo.getId(),TableEnum.EVENT_PLAN_THEME_ONLINE.key,registrationFormVo.getFkEventPlanThemeOnlineIds());
        }
        //参加线下子项目活动
        if(GeneralTool.isNotEmpty(registrationFormVo.getFkEventPlanThemeOfflineItemIds())){
            insertRegistrationEvent(registrationFormVo.getId(),TableEnum.EVENT_PLAN_THEME_OFFLINE_ITEM.key,registrationFormVo.getFkEventPlanThemeOfflineItemIds());
        }
        //参加线下专访活动
        if(GeneralTool.isNotEmpty(registrationFormVo.getFkEventPlanThemeWorkShopIds())){
            insertRegistrationEvent(registrationFormVo.getId(),TableEnum.EVENT_PLAN_THEME_WORKSHOP.key,registrationFormVo.getFkEventPlanThemeWorkShopIds());
        }

//        Result<ConfigVo> result = permissionCenterClient.getConfigByKey(ProjectKeyEnum.REMINDER_EMAIL_EVENT_PLAN_REGISTRATION.key);
//        ConfigVo configDto;
//        if (result.isSuccess() && result.getData() != null) {
//            configDto = result.getData();
//        } else {
//            throw new GetServiceException("缺少活动计划报名邮件提醒配置");
//        }
        //邮件发送
        sendEmailProcessor(registrationFormVo);
    }


    public void sendEmailProcessor(EventPlanRegistrationFormDto registrationFormVo) {
        //邮件通知
        Boolean sendEmail = isSendEmail(registrationFormVo.getFkCompanyId());
        if(sendEmail){
            //查询线上项目对应主题
            Set<Long> fkEventPlanThemeIds = new HashSet<>();
            if(GeneralTool.isNotEmpty(registrationFormVo.getFkEventPlanThemeOnlineIds())){
                List<EventPlanThemeOnline> onlineList = onlineService.list(Wrappers.<EventPlanThemeOnline>lambdaQuery().in(EventPlanThemeOnline::getId, registrationFormVo.getFkEventPlanThemeOnlineIds()));
                if(GeneralTool.isNotEmpty(onlineList)){
                    Set<Long> onlineThemIds = onlineList.stream().map(EventPlanThemeOnline::getFkEventPlanThemeId).collect(Collectors.toSet());
                    fkEventPlanThemeIds.addAll(onlineThemIds);
                }
            }
            //查询线下子项目名称以及线下项目业务国家
            Set<Long> fkAraeCountryIds = new HashSet<>();
            if(GeneralTool.isNotEmpty(registrationFormVo.getFkEventPlanThemeOfflineItemIds())){
                //根据线下活动子项目查询线下活动
                List<EventPlanThemeOfflineItem> offlineItemList = offlineItemService.list(Wrappers.<EventPlanThemeOfflineItem>lambdaQuery()
                        .in(EventPlanThemeOfflineItem::getId, registrationFormVo.getFkEventPlanThemeOfflineItemIds()));
                if(GeneralTool.isNotEmpty(offlineItemList)){
                    List<Long> fkEventPlanThemeOfflineIds = offlineItemList.stream().map(EventPlanThemeOfflineItem::getFkEventPlanThemeOfflineId).collect(Collectors.toList());
                    List<EventPlanThemeOffline> offlineList = offlineService.list(Wrappers.<EventPlanThemeOffline>lambdaQuery().in(EventPlanThemeOffline::getId, fkEventPlanThemeOfflineIds));
                    //获取对应的业务国家，后续根据业务国家匹配配置获取相应需发送邮件的邮箱
                    if(GeneralTool.isNotEmpty(offlineList)){
                        offlineList.forEach(i->{
                            String[] split = i.getFkAreaCountryIds().split(",");
                            for (String s : split){
                                fkAraeCountryIds.add(Long.valueOf(s));
                            }
                            fkEventPlanThemeIds.add(i.getFkEventPlanThemeId());
                        });
                    }
                }
            }

            //参加线下专访活动
            if(GeneralTool.isNotEmpty(registrationFormVo.getFkEventPlanThemeWorkShopIds())){
                List<EventPlanThemeWorkshop> workshopList = workshopService.list(Wrappers.<EventPlanThemeWorkshop>lambdaQuery()
                        .in(EventPlanThemeWorkshop::getId, registrationFormVo.getFkEventPlanThemeWorkShopIds()));
                if(GeneralTool.isNotEmpty(workshopList)){
                    Set<Long> workshopThemIds = workshopList.stream().map(EventPlanThemeWorkshop::getFkEventPlanThemeId).collect(Collectors.toSet());
                    fkEventPlanThemeIds.addAll(workshopThemIds);
                }
            }
            //查询主题名称
            String themeName = "";
            if(GeneralTool.isNotEmpty(fkEventPlanThemeIds)){
                List<EventPlanTheme> themelist = eventPlanThemeService.list(Wrappers.<EventPlanTheme>lambdaQuery()
                        .in(EventPlanTheme::getId, fkEventPlanThemeIds)
                        .orderByDesc(EventPlanTheme::getViewOrder));
                if(GeneralTool.isNotEmpty(themelist)){
                    StringBuilder sb = new StringBuilder();
                    themelist.forEach(i->{
                        sb.append(i.getMainTitle()).append(",");
                    });
                     themeName = sb.toString();
                    if(themeName.length()>0){
                        themeName = themeName.substring(0,themeName.length()-1);
                    }
                }
            }

            //获取需发送的邮箱
            List<String> emailList = getEmailList(fkAraeCountryIds, registrationFormVo.getFkCompanyId());
            //异步发送邮件通知
            if(GeneralTool.isNotEmpty(emailList)){
                String finalThemeName = themeName;
                CompletableFuture.runAsync(()->{
                    sendEmail(registrationFormVo.getId(),registrationFormVo.getFkCompanyId(),registrationFormVo.getFkEventPlanId(),emailList,finalThemeName, registrationFormVo.getFkInstitutionProviderId());
                },saleTaskExecutor);
            }

        }
    }

    /**
     * 是否发送邮件
     * <AUTHOR>
     * @DateTime 2024/3/28 18:07
     */
    public Boolean isSendEmail(Long fkCompanyId){
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_EVENT_PLAN_REGISTRATION.key, 1).getData();
        String configValue1 = companyConfigMap.get(fkCompanyId);
        return "1".equals(configValue1);
//        String value1 = configDto.getValue1();
//        JSONObject jsonObject = JSON.parseObject(value1);
//        Boolean isSend = false;
//        if (fkCompanyId == 2L){
//            String gea = jsonObject.getString("GEA");
//            isSend = "1".equals(gea);
//        } else if (fkCompanyId == 3L){
//            String iae = jsonObject.getString("IAE");
//            isSend = "1".equals(iae);
//        } else {
//            String other = jsonObject.getString("OTHER");
//            isSend = "1".equals(other);
//        }
//        return isSend;
    }

    /**
     * 获取邮件内容链接域名地址
     * <AUTHOR>
     * @DateTime 2024/3/29 17:09
     */
    public String getDomain(Long fkCompanyId){
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.FILE_SRC_PREFIX.key, 2).getData();
        String configValue2 = companyConfigMap.get(fkCompanyId);
        String domainName = "";
        if (GeneralTool.isNotEmpty(configValue2)){
            domainName = configValue2;
        } else {
            Result<String> domainNameResult = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.FILE_SRC_PREFIX.key);
            if (domainNameResult.isSuccess() && GeneralTool.isNotEmpty(domainNameResult.getData())) {
                domainName = domainNameResult.getData();
            }
        }
        return domainName;

//        String domain = "";
//        Properties props = System.getProperties();
//        String profile = props.getProperty("spring.profiles.active");
//        if (GeneralTool.isNotEmpty(profile)) {
//            if (profile.equals(AppConstant.PROD_CODE) || profile.equals(AppConstant.IAE_CODE)|| profile.equals(AppConstant.TW_CODE)) {
//                ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.FILE_SRC_PREFIX.key).getData();
//                if (GeneralTool.isNotEmpty(configDto)){
//                    String configJson = configDto.getValue2();
//                    JSONObject configJsonObject = JSON.parseObject(configJson);
//                    if (fkCompanyId.equals(3L)){
//                        domain = configJsonObject.getString("IAE");
//                    }else {
//                        domain = configJsonObject.getString("OTHER");
//                    }
//                }
//            }else {
//                Result<String> domainNameResult = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.FILE_SRC_PREFIX.key);
//                if (domainNameResult.isSuccess() && GeneralTool.isNotEmpty(domainNameResult.getData())) {
//                    domain = domainNameResult.getData();
//                }
//            }
//        }
//        return domain;
    }

    /**
     * 发送邮件通知
     * <AUTHOR>
     * @DateTime 2024/3/29 17:11
     */
    public void sendEmail(Long fkRegistrationId,Long fkCompanyId,Long fkEventPlanId,List<String> emailList,String themeName,Long fkInstitutionProviderId){
        //域名获取
        String domain = getDomain(fkCompanyId);
        //查询年度计划对应年份
        Integer year = null;
        if(GeneralTool.isNotEmpty(fkEventPlanId)){
            EventPlan eventPlan = eventPlanService.getById(fkEventPlanId);
            if(GeneralTool.isNotEmpty(eventPlan)){
                year = eventPlan.getYear();
            }
        }

        //查询对应学校提供商名称
        InstitutionProviderVo institutionProviderVo = institutionCenterClient.getInstitutionProviderById(fkInstitutionProviderId);
        if(GeneralTool.isEmpty(institutionProviderVo)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("feign_execution_failed"));
        }
        //TODO EVENT_BILL_NOTICE
        String providerName = institutionProviderVo.getName()+"（"+ institutionProviderVo.getNameChn()+"）";
        String taskTitle = providerName +"学校已提交活动报名表单，请尽快确认报名项目和出具Invoice";
        //邮件内容链接
        String content = providerName +"学校报名了"+ themeName +"活动，请尽快确认报名项目以及出具Invoice";
        String br = "<br>";
        String link = domain+"/sales-center_event_activity-annual-plan_registration-list?companyid="+fkCompanyId+"&planid="+fkEventPlanId+"&year="+year+"&institutionProviderId="+fkInstitutionProviderId;
        String linkContent = "<a href='"+link+"'>"+link+"</a>";
        String subContent = "<div>" + content + br + linkContent + "</div>";
        //获取邮件模板
        Result<RemindTemplate> tempResult = reminderCenterClient.getRemindTemplateByTypeKey(ProjectKeyEnum.ADD_EVENT_PLAN_REGISTRATION_EMAIL.key);
        if(!tempResult.isSuccess() || GeneralTool.isEmpty(tempResult.getData())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("feign_execution_failed"));
        }
        RemindTemplate remindTemplate  = tempResult.getData();
        String emailTemplate = remindTemplate.getEmailTemplate();
        emailTemplate = emailTemplate.replace("#{taskTitle}",taskTitle);
        emailTemplate = emailTemplate.replace("#{taskRemark}",subContent);
        String emailTemplateTest = Base64.getEncoder().encodeToString((emailTemplate).getBytes(StandardCharsets.UTF_8));
        for(String email : emailList){
            Result<Boolean> result = reminderCenterClient.sendMail(taskTitle, emailTemplateTest,email,null);
            if (!result.isSuccess() || (GeneralTool.isNotEmpty(result.getData()) && !result.getData()) || GeneralTool.isEmpty(result.getData())){
                throw new GetServiceException(LocaleMessageUtils.getMessage("news_emil_send_fail"));
            }
        }


        //保存发送提醒通知数据记录（日志）
        List<RemindTaskDto> remindTaskDtos = new ArrayList<>();
        RemindTaskDto remindTaskDto = new RemindTaskDto();
        remindTaskDto.setTaskTitle(taskTitle);
        remindTaskDto.setTaskRemark("");
        //邮件方式发送
        remindTaskDto.setRemindMethod("1");
        //发送结束
        remindTaskDto.setStatus(3);
        //默认背景颜色
        remindTaskDto.setFkRemindEventTypeKey("EVENT_BILL_NOTICE");
        remindTaskDto.setTaskBgColor("#3788d8");
        remindTaskDto.setFkStaffId(null);
        remindTaskDto.setStartTime(new Date());
        remindTaskDto.setAdvanceDays("0");
        remindTaskDto.setFkTableName(TableEnum.EVENT_PLAN_REGISTRATION.key);
        remindTaskDto.setFkTableId(fkRegistrationId);
        remindTaskDto.setFkDbName(ProjectKeyEnum.REMINDER_CENTER.key);
        remindTaskDto.setTaskLink(link);
        remindTaskDtos.add(remindTaskDto);

        try {
            reminderCenterClient.batchAdd(remindTaskDtos);
        }catch (Exception e){
            log.error("添加发起通知记录失败：",e);
        }
    }

    /**
     * 根据业务国家发送给对应的PM邮箱
     * <AUTHOR>
     * @DateTime 2024/3/29 10:00
     */
    public List<String> getEmailList(Set<Long> fkAreaCountryIds,Long fkCompanyId){
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_EVENT_PLAN_REGISTRATION.key, 2).getData();
        String configValue2 = companyConfigMap.get(fkCompanyId);
        JSONArray emailJson = JSONArray.parseArray(configValue2);

        List<String> emailList = new ArrayList<>();
//        String value2 = configDto.getValue2();
//        JSONObject jsonObject = JSON.parseObject(value2);
//        JSONArray emailJson;
//        if (fkCompanyId == 2L){
//            emailJson = jsonObject.getJSONArray("GEA");
//        } else if (fkCompanyId == 3L){
//            emailJson = jsonObject.getJSONArray("IAE");
//        } else {
//            emailJson = jsonObject.getJSONArray("OTHER");
//        }
        Map<Long,String> emailMap = new HashMap<>();
        emailJson.stream().forEach(item->{
            String str = item.toString();
            String[] split = str.split("=");
            emailMap.put(Long.valueOf(split[0]),split[1]);
        });
        //存在默认邮箱，无需校验对应业务国家。发送邮箱必须发送默认邮箱
        if(GeneralTool.isNotEmpty(emailMap.get(0L))){
            emailList.add(emailMap.get(0L));
        }
        fkAreaCountryIds.stream().forEach(item->{
            if(GeneralTool.isNotEmpty(emailMap.get(item))){
                emailList.add(emailMap.get(item));
            }
        });
        return emailList;
    }



    public void insertRegistrationEvent(Long fkRegistrationId,String fkTableName,List<Long> fkTableIds){
        for(Long fktableId : fkTableIds){
            EventPlanRegistrationEvent event = new EventPlanRegistrationEvent();
            event.setFkTableName(fkTableName);
            event.setFkTableId(fktableId);
            event.setFkEventPlanRegistrationId(fkRegistrationId);
            utilService.setCreateInfo(event);
            registrationEventService.save(event);
        }
    }



    @Override
    public List<EventPlanRegistrationVo> getEventPlanRegistrations(EventPlanRegistrationSearchDto vo, Page page) {
        if(GeneralTool.isEmpty(vo)){
           throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        vo = doSetEventPlanRegistrationSearchVo(vo);
        IPage<EventPlanRegistrationVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<EventPlanRegistrationVo> eventPlanRegistrationVos = eventPlanRegistrationMapper.getEventPlanRegistrations(iPage, vo, true);
        if(GeneralTool.isEmpty(eventPlanRegistrationVos)){
            return Collections.emptyList();
        }
        page.setAll((int) iPage.getTotal());
        List<Long> registrationIds = eventPlanRegistrationVos.stream().map(EventPlanRegistrationVo::getId).collect(Collectors.toList());
        vo.setFkRegistrationIds(registrationIds);
        List<EventPlanRegistrationVo> registrationDtos = eventPlanRegistrationMapper.getEventPlanRegistrations(null, vo ,false);
        //设置属性
        List<EventPlanRegistrationVo> registrationDtoList = doSetEventPlanRegistrationDto(registrationDtos,true);
        registrationDtoList = registrationDtoList.stream().sorted(
                Comparator.comparing(EventPlanRegistrationVo::getId).reversed().thenComparing(EventPlanRegistrationVo::getPersonName))
                .collect(Collectors.toList());
        return registrationDtoList;
    }


    //处理字符
    public EventPlanRegistrationSearchDto doSetEventPlanRegistrationSearchVo(EventPlanRegistrationSearchDto vo){
        if(GeneralTool.isNotEmpty(vo.getAreaCountryNames())){
            List<String> areaCountryNames = new ArrayList<>();
            vo.getAreaCountryNames().forEach(a -> areaCountryNames.add(StringEscapeUtils.unescapeHtml(a)));
            vo.setAreaCountryNames(areaCountryNames);
        }
        if(GeneralTool.isNotEmpty(vo.getOfflineLocations())){
            List<String> offlineLocations = new ArrayList<>();
            vo.getOfflineLocations().forEach(o -> offlineLocations.add(StringEscapeUtils.unescapeHtml(o)));
            vo.setOfflineLocations(offlineLocations);
        }
        if(GeneralTool.isNotEmpty(vo.getWorkShopLocations())){
            List<String> workShopLocations = new ArrayList<>();
            vo.getWorkShopLocations().forEach(w-> workShopLocations.add(StringEscapeUtils.unescapeHtml(w)));
            vo.setWorkShopLocations(workShopLocations);
        }
        return vo;
    }

    public List<EventPlanRegistrationVo> doSetEventPlanRegistrationDto(List<EventPlanRegistrationVo> registrationDtos, Boolean isFormat){
        if(GeneralTool.isEmpty(registrationDtos)){
            return Collections.emptyList();
        }

        //查询数据时，会多查一条未包含任何活动项目数据。若同一个名册存在包含活动项目的，需将该条未包含活动任务的数据清除
        Map<Long, List<EventPlanRegistrationVo>> rgMap = registrationDtos.stream().collect(Collectors.groupingBy(EventPlanRegistrationVo::getId));
        List<EventPlanRegistrationVo> rgList = new ArrayList<>();
        for(Map.Entry<Long,List<EventPlanRegistrationVo>> entry : rgMap.entrySet()){
            List<EventPlanRegistrationVo> list = entry.getValue();
            if(list.size() > 1){
                list = list.stream().filter(l->GeneralTool.isNotEmpty(l.getActivityName())).collect(Collectors.toList());
            }
            rgList.addAll(list);
        }
        //公司
        Set<Long> fkCompanyIds = rgList.stream().map(EventPlanRegistrationVo::getFkCompanyId).collect(Collectors.toSet());
        Map<Long, String> companyNamesByIds = new HashMap<>();
        Result<Map<Long, String>> companyNamesByIdsResult = permissionCenterClient.getCompanyNamesByIds(fkCompanyIds);
        if (companyNamesByIdsResult.isSuccess() && companyNamesByIdsResult.getData() != null) {
            companyNamesByIds = companyNamesByIdsResult.getData();
        }

        //学校提供商
        Set<Long> fkInstitutionProviderIds = rgList.stream().map(EventPlanRegistrationVo::getFkInstitutionProviderId).collect(Collectors.toSet());
        Map<Long, String> institutionProviderNamesByIds = new HashMap<>();
        Result<Map<Long, String>> institutionProviderNamesByIdsResult = institutionCenterClient.getInstitutionProviderNamesByIds(fkInstitutionProviderIds);
        if (companyNamesByIdsResult.isSuccess() && companyNamesByIdsResult.getData() != null) {
            institutionProviderNamesByIds = institutionProviderNamesByIdsResult.getData();
        }

         //小计
        Map<Long, Map<String, List<EventPlanRegistrationVo>>> registrationMap = rgList.stream()
                .filter(r-> GeneralTool.isNotEmpty(r.getFkCurrencyTypeNum()) && !r.getIsCancel())
                .collect(Collectors.groupingBy(EventPlanRegistrationVo::getId, Collectors.groupingBy(EventPlanRegistrationVo::getFkCurrencyTypeNum)));
        HashMap<Long, String> registrationSumAmoutMap = new HashMap<>();
        for (Map.Entry<Long,Map<String, List<EventPlanRegistrationVo>>> entry: registrationMap.entrySet()){
            StringBuffer sb = new StringBuffer();
            for(Map.Entry<String,List<EventPlanRegistrationVo>> listEntry: entry.getValue().entrySet()){
                BigDecimal sumAmout = BigDecimal.ZERO;
                for(EventPlanRegistrationVo registrationDto : listEntry.getValue()){
                    sumAmout = sumAmout.add(registrationDto.getAmount());
                }
                sb.append(sumAmout).append(listEntry.getKey()).append("\n");
            }
            String fee = sb.toString();
            int lastIndexof = fee.lastIndexOf("\n");
            if(lastIndexof != -1){
                fee = fee.substring(0,lastIndexof);
            }

            registrationSumAmoutMap.put(entry.getKey(),isFormat ? fee.replace("\n","<br>") : fee);

        }

        for(EventPlanRegistrationVo dto:rgList){
            //设置公司名称
            if(GeneralTool.isNotEmpty(companyNamesByIds)){
                dto.setFkCompanyName(companyNamesByIds.get(dto.getFkCompanyId()));
            }
            //设置提供商名称
            if(GeneralTool.isNotEmpty(institutionProviderNamesByIds)){
                dto.setFkInstitutionProviderName(institutionProviderNamesByIds.get(dto.getFkInstitutionProviderId()));
            }

            //联系人信息
            if(GeneralTool.isNotEmpty(dto.getPersonInformation())){
                //添加币种
                StringBuffer sb = new StringBuffer();
                sb.append(dto.getPersonInformation()).append("\nInvoice币种：").append(dto.getFkCurrencyTypeNumInvoice());
                dto.setPersonInformation(isFormat ? sb.toString().replace("\n","<br>") : sb.toString());
            }
            //小计费用
            dto.setSubtotalFee(registrationSumAmoutMap.get(dto.getId()));
        }

        return rgList;
    }


    @Override
    public String getEventPlanRegistrationTotalFee(EventPlanRegistrationSearchDto vo){
        if(GeneralTool.isEmpty(vo)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        vo = doSetEventPlanRegistrationSearchVo(vo);
        List<EventPlanRegistrationVo> registrationDtos = eventPlanRegistrationMapper.getEventPlanRegistrations(null,vo,false);
        if(GeneralTool.isEmpty(registrationDtos)){
            return null;
        }

        return totalFee(registrationDtos).replace("\n","<br>");
    }

    public String totalFee(List<EventPlanRegistrationVo> registrationDtos){
        Map<String, List<EventPlanRegistrationVo>> totalMap = registrationDtos.stream().filter(r->GeneralTool.isNotEmpty(r.getFkCurrencyTypeNum()))
                .collect(Collectors.groupingBy(EventPlanRegistrationVo::getFkCurrencyTypeNum));
        StringBuffer sb = new StringBuffer();
        for(Map.Entry<String,List<EventPlanRegistrationVo>> entry : totalMap.entrySet()){
            BigDecimal totalAmount = BigDecimal.ZERO;
            for(EventPlanRegistrationVo dto : entry.getValue()){
                totalAmount = totalAmount.add(dto.getAmount());
            }
            sb.append(totalAmount.toPlainString()).append(entry.getKey()).append("\n");

        }
        String fee = sb.toString();
        int lastIndexof = fee.lastIndexOf("\n");
        if(lastIndexof != -1){
            fee = fee.substring(0,lastIndexof);
        }
        return fee;
    }


    @Override
    public void exportEventPlanRegistrationsExcel(EventPlanRegistrationSearchDto vo, HttpServletResponse response){
        vo = doSetEventPlanRegistrationSearchVo(vo);
        //设置列标题
        List<String> titleList = new ArrayList<>(Arrays.asList("所属公司","报名学校提供商","联系人资料","活动主题","活动项目","报名时间","报名费用","小计"));
        List<EventPlanRegistrationVo> registrationDtos = eventPlanRegistrationMapper.getEventPlanRegistrations(null,vo,false);
        //设置属性
        List<EventPlanRegistrationVo> registrationDtoList = doSetEventPlanRegistrationDto(registrationDtos,false);
        registrationDtoList = registrationDtoList.stream().sorted(
                Comparator.comparing(EventPlanRegistrationVo::getId).thenComparing(EventPlanRegistrationVo::getPersonName))
                .collect(Collectors.toList());

        BigExcelWriter writer = FileUtils.setExcelStyleWrapText("EventPlanRegistrationsExcel", titleList.size());
        List<List<String>> rowList = new ArrayList<>();
        rowList.add(titleList);
        for(EventPlanRegistrationVo dto:registrationDtoList){
            List<String> contentList = new ArrayList<>();
            contentList.add(GeneralTool.isNotEmpty(dto.getFkCompanyName()) ? dto.getFkCompanyName() : null);
            contentList.add(GeneralTool.isNotEmpty(dto.getFkInstitutionProviderName()) ? dto.getFkInstitutionProviderName() : null);
            contentList.add(GeneralTool.isNotEmpty(dto.getPersonInformation()) ? dto.getPersonInformation() : null);
            contentList.add(GeneralTool.isNotEmpty(dto.getMainTitle()) ? dto.getMainTitle() : null);
            contentList.add(GeneralTool.isNotEmpty(dto.getActivityName()) ? dto.getActivityName() : null);
            contentList.add(GeneralTool.isNotEmpty(dto.getGmtCreate()) ? DateUtil.formatDateTime(dto.getGmtCreate()) : null);
            contentList.add(GeneralTool.isNotEmpty(dto.getFee()) ? dto.getFee() : null);
            contentList.add(GeneralTool.isNotEmpty(dto.getSubtotalFee()) ? dto.getSubtotalFee() : null);
            rowList.add(contentList);
        }
        //总计
        String totalFee = totalFee(registrationDtos);
        String[] split = totalFee.split("\n");
        List<String> lastList = new ArrayList<>();
        lastList.add("总计："+ totalFee);
        rowList.add(lastList);

        writer.write(rowList,true);
        //设置默认行高
        writer.setDefaultRowHeight(30);
        //内容列宽
        for(int i = 1 ;i < titleList.size();i++){
            writer.setColumnWidth(i,35);
        }
        //表格设置合并
        Map<Long, List<EventPlanRegistrationVo>> registrationMap = registrationDtoList.stream().collect(Collectors.groupingBy(EventPlanRegistrationVo::getId, TreeMap::new,Collectors.toList()));
        //内容起始行
        int firstRow = 1;
        //内容结束行
        int lastRow = 0;
        for(Map.Entry<Long,List<EventPlanRegistrationVo>> entry:registrationMap.entrySet()){
            lastRow = lastRow + entry.getValue().size();
            //合并数必须包含2个或更多单元格
            if(entry.getValue().size() >= 2){
                //合并公司
                writer.merge(firstRow,lastRow,0,0,null,true);
                //合并提供商
                writer.merge(firstRow,lastRow,1,1,null,true);
                //合并联系人信息
                writer.merge(firstRow,lastRow,2,2,null,true);
                //合并小计
                writer.merge(firstRow,lastRow,7,7,null,true);
            }
            firstRow = lastRow + 1;
        }
        writer.merge(rowList.size()-1,rowList.size()+split.length-1,0,titleList.size()-1,null,true);
        //设置标题样式
        for (int i = 0; i < titleList.size(); i++) {
            setCellStyle(writer,i,0);
        }
        //设置总计样式
        setCellStyle(writer,0,rowList.size()-1);
        FileUtils.doExportExcel(response, writer, "RegistrationsExcel");
    }

    /**
     * 单元格背景色
     * @param writer
     * @param x
     * @param y
     */
    public void setCellStyle(BigExcelWriter writer, int x, int y) {
        CellStyle cellStyle = writer.createCellStyle(x, y);
        cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());

        setBorder(cellStyle);
        // 字体垂直居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //字体水平居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        //自动换行
        cellStyle.setWrapText(true);
    }
    /**
     * 设置单元格背景色，需重设边框，否则出现边框丢失情况
     * @param cellStyle
     */
    public void setBorder(CellStyle cellStyle){
        //边框设置
        // 顶边栏
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        // 右边栏
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
        // 底边栏
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        // 左边栏
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        //设置纹理，设置背景色必须设置
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
    }

    @Override
    public EventPlanRegistrationUpdateVo findEventPlanRegistrationById(Long id){
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        EventPlanRegistration registration = eventPlanRegistrationMapper.selectById(id);
        if (GeneralTool.isEmpty(registration)) {
            return null;
        }
        List<EventPlanRegistrationContactPerson> list = personService.list(Wrappers.<EventPlanRegistrationContactPerson>lambdaQuery()
                .eq(EventPlanRegistrationContactPerson::getFkEventPlanRegistrationId, id));
        EventPlanRegistrationUpdateVo dto = BeanCopyUtils.objClone(registration, EventPlanRegistrationUpdateVo::new);
        dto.setEventPlanRegistrationContactPersonList(list);
        return dto;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateEventPlanRegistration(EventPlanRegistrationDto eventPlanRegistrationDto){
        EventPlanRegistration registration = eventPlanRegistrationMapper.selectById(eventPlanRegistrationDto.getId());
        if (GeneralTool.isEmpty(registration)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        registration.setFkCurrencyTypeNumInvoice(eventPlanRegistrationDto.getFkCurrencyTypeNumInvoice());
        utilService.setUpdateInfo(registration);
        eventPlanRegistrationMapper.updateById(registration);


        if(GeneralTool.isNotEmpty(eventPlanRegistrationDto.getEventPlanRegistrationContactPersonList())){
            //删除原有联系人
            personService.remove(Wrappers.<EventPlanRegistrationContactPerson>lambdaQuery()
                    .eq(EventPlanRegistrationContactPerson::getFkEventPlanRegistrationId, eventPlanRegistrationDto.getId()));
            //新增联系人
            List<EventPlanRegistrationContactPerson> personList = BeanCopyUtils.copyListProperties(eventPlanRegistrationDto.getEventPlanRegistrationContactPersonList(), EventPlanRegistrationContactPerson::new);
            for(EventPlanRegistrationContactPerson person:personList){
                person.setFkEventPlanRegistrationId(eventPlanRegistrationDto.getId());
                utilService.setCreateInfo(person);
                personService.save(person);
            }
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id){
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        EventPlanRegistrationEvent registrationEvent = registrationEventService.getById(id);
        if (GeneralTool.isEmpty(registrationEvent)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        boolean b = registrationEventService.removeById(id);
        if (!b) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Override
    public List<EventPlanSelectVo> getEventPlanListByYear(Integer year){
        if(GeneralTool.isEmpty(year)){
            return Collections.emptyList();
        }
        List<EventPlan> list = eventPlanService.list(Wrappers.<EventPlan>lambdaQuery().eq(EventPlan::getYear, year));
        if(GeneralTool.isEmpty(list)){
            return Collections.emptyList();
        }
        return BeanCopyUtils.copyListProperties(list, EventPlanSelectVo::new);
    }

    @Override
    public List<EventPlanThemeSelectVo> getThemeListByPlanId(Long fkEventPlanId, Integer displayType){
        if(GeneralTool.isEmpty(fkEventPlanId)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<EventPlanTheme> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EventPlanTheme::getFkEventPlanId, fkEventPlanId);
        wrapper.eq(EventPlanTheme::getIsActive, true);
        if(GeneralTool.isNotEmpty(displayType)){
            wrapper.eq(EventPlanTheme::getDisplayType,displayType);
        }
        wrapper.orderByDesc(EventPlanTheme::getViewOrder);

        List<EventPlanTheme> list = eventPlanThemeService.list(wrapper);
        if(GeneralTool.isEmpty(list)){
            return Collections.emptyList();
        }

        return BeanCopyUtils.copyListProperties(list, EventPlanThemeSelectVo::new);
    }

    @Override
    public List<EventPlanThemeOnlineSelectVo> getOnlineSelect(List<Long> fkEventPlanThemeIds){
        if(GeneralTool.isEmpty(fkEventPlanThemeIds)){
            return Collections.emptyList();
        }
        List<EventPlanThemeOnline> list = onlineService.list(Wrappers.<EventPlanThemeOnline>lambdaQuery()
                .in(EventPlanThemeOnline::getFkEventPlanThemeId, fkEventPlanThemeIds)
                .eq(EventPlanThemeOnline::getIsActive, true)
                .orderByDesc(EventPlanThemeOnline::getViewOrder));
        if(GeneralTool.isEmpty(list)){
            return Collections.emptyList();
        }
       return BeanCopyUtils.copyListProperties(list, EventPlanThemeOnlineSelectVo::new);
    }

    @Override
    public List<String> getOfflineAreaCountryNameList(List<Long> fkEventPlanThemeIds){
        if(GeneralTool.isEmpty(fkEventPlanThemeIds)){
            return Collections.emptyList();
        }
        List<EventPlanThemeOffline> offlineList = offlineService.list(Wrappers.<EventPlanThemeOffline>lambdaQuery()
                .in(EventPlanThemeOffline::getFkEventPlanThemeId, fkEventPlanThemeIds)
                .groupBy(EventPlanThemeOffline::getAreaCountryName)
                .eq(EventPlanThemeOffline::getIsActive, true).orderByDesc(EventPlanThemeOffline::getViewOrder));
        if(GeneralTool.isEmpty(offlineList)){
            return Collections.emptyList();
        }
        List<String> countryNameList = offlineList.stream().map(EventPlanThemeOffline::getAreaCountryName).collect(Collectors.toList());
        return countryNameList;

    }

    @Override
    public List<String> getOfflineLocationList(OfflineLocationSelectDto vo){
        if(GeneralTool.isEmpty(vo)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<EventPlanThemeOffline> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(GeneralTool.isNotEmpty(vo.getAreaCountryNames())){
            lambdaQueryWrapper.in(EventPlanThemeOffline::getAreaCountryName, vo.getAreaCountryNames());
        }
        if(GeneralTool.isNotEmpty(vo.getFkEventPlanThemeIds())){
            lambdaQueryWrapper.in(EventPlanThemeOffline::getFkEventPlanThemeId, vo.getFkEventPlanThemeIds());
        }

        List<EventPlanThemeOffline> offlineList = offlineService.list(lambdaQueryWrapper);
        if(GeneralTool.isEmpty(offlineList)){
            return Collections.emptyList();
        }
        Set<Long> offlineIds = offlineList.stream().map(EventPlanThemeOffline::getId).collect(Collectors.toSet());
        List<EventPlanThemeOfflineItem> list = offlineItemService.list(Wrappers.<EventPlanThemeOfflineItem>lambdaQuery()
                .in(EventPlanThemeOfflineItem::getFkEventPlanThemeOfflineId, offlineIds)
                .orderByDesc(EventPlanThemeOfflineItem::getViewOrder));
        if(GeneralTool.isEmpty(list)){
            return Collections.emptyList();
        }
        Set<String> offlineLocations = list.stream().map(EventPlanThemeOfflineItem::getLocation).collect(Collectors.toSet());
        return new ArrayList<>(offlineLocations);
    }

    @Override
    public List<String> getWorkshopLocationSelect(List<Long> fkEventThemeIds){
        if(GeneralTool.isEmpty(fkEventThemeIds)){
            return Collections.emptyList();
        }
        List<EventPlanThemeWorkshop> list = workshopService.list(Wrappers.<EventPlanThemeWorkshop>lambdaQuery()
                .in(EventPlanThemeWorkshop::getFkEventPlanThemeId, fkEventThemeIds)
                .eq(EventPlanThemeWorkshop::getIsActive, true)
                .orderByDesc(EventPlanThemeWorkshop::getViewOrder));
        if(GeneralTool.isEmpty(list)){
            return Collections.emptyList();
        }
        Set<String> collect = list.stream().map(EventPlanThemeWorkshop::getLocation).collect(Collectors.toSet());
        return new ArrayList<>(collect);
    }

    @Override
    public List<BaseSelectEntity> getInstitutionProviderSelect(){
        //登录用户所拥有权限id
        List<Long> ids = SecureUtil.getCompanyIds();
        List<EventPlanRegistration> eventPlanRegistrations = eventPlanRegistrationMapper.selectList(Wrappers.<EventPlanRegistration>lambdaQuery()
                .in(EventPlanRegistration::getFkCompanyId, ids).groupBy(EventPlanRegistration::getFkInstitutionProviderId));
        if(GeneralTool.isEmpty(eventPlanRegistrations)){
            return Collections.emptyList();
        }
        Set<Long> fkProviderIdList = eventPlanRegistrations.stream().map(EventPlanRegistration::getFkInstitutionProviderId).collect(Collectors.toSet());
        Result<Map<Long, String>> result = institutionCenterClient.getInstitutionProviderNamesByIds(fkProviderIdList);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        Map<Long, String> dataMap = result.getData();
        List<BaseSelectEntity> list = new ArrayList<>();
        for(Map.Entry<Long,String> entry:dataMap.entrySet()){
            BaseSelectEntity baseEntity = new BaseSelectEntity();
            baseEntity.setId(entry.getKey());
            baseEntity.setName(entry.getValue());
            list.add(baseEntity);
        }
        return list;
    }
}
