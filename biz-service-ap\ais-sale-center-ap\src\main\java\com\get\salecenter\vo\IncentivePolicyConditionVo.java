package com.get.salecenter.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 奖励策略内的课程条件json明细
 * <AUTHOR>
 * @since 2023-03-13
 */
@Data
public class IncentivePolicyConditionVo implements Serializable{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "提交申请时间集合")
    private List<DateTimeInfo> submitAppTimes;

    @ApiModelProperty(value = "支付押金时间集合")
    private List<DateTimeInfo> depositTimes;

    @ApiModelProperty(value = "支付学费时间集合")
    private List<DateTimeInfo> tuitionTimes;

    @ApiModelProperty(value = "入学时间集合")
    private List<DateTimeInfo> deferOpeningTimes;


    @ApiModelProperty(value = "符合奖励状态集合")
    private List<Long> stepOrderIds;

    @ApiModelProperty(value = "符合奖励状态名称集合(返回)")
    @TableField(exist = false)
    @UpdateWithNull
    private List<String> stepOrderNames;

//    /**
//     * 学生申请方案项目状态步骤Ids
//     */
//    @ApiModelProperty(value = "符合奖励状态步骤集合")
//    private List<Long> studentOfferItemStepIds;

    /**
     * 主课条件标记（如有任何主课条件则标记为true,否则为false）
     */
    @ApiModelProperty(value = "主课条件标记（如有任何主课条件则标记为true,否则为false）")
    private Boolean mainCourseMark;

    /**
     * 主课程等级(多选)
     */
    @ApiModelProperty(value = "主课程等级(多选)")
    private List<Long> mainMajorLevelIds;

    @ApiModelProperty(value = "主课程等级名称集合(返回)")
    @TableField(exist = false)
    @UpdateWithNull
    private List<String> mainMajorLevelNames;

    /**
     * 主课程类型(多选)
     */
    @ApiModelProperty(value = "主课程类型(多选)")
    private List<Long> mainCourseTypeIds;

    @ApiModelProperty(value = "主课程类型名称集合(返回)")
    @TableField(exist = false)
    @UpdateWithNull
    private List<String> mainCourseTypeNames;

    /**
     * 主课程(多选)
     */
    @ApiModelProperty(value = "主课程(多选)")
    private List<Long> mainCourseIds;

    @ApiModelProperty(value = "主课程名称集合(返回)")
    @TableField(exist = false)
    @UpdateWithNull
    private List<String> mainCourseNames;

    /**
     * 主课和子课之间的条件标记，0:"AND",1:"OR"
     */
    @ApiModelProperty(value = "主课和子课之间的条件标记[0-AND,1-OR]")
    private Integer mainAndSubCourseFlag;

    /**
     * 子课条件标记（如有任何子课条件则标记为true,否则为false）
     */
    @ApiModelProperty(value = "子课条件标记（如有任何子课条件则标记为true,否则为false）")
    private Boolean subCourseMark;

    /**
     * 子课程等级(多选)
     */
    @ApiModelProperty(value = "子课程等级(多选)")
    private List<Long> subMajorLevelIds;

    @ApiModelProperty(value = "子课程等级名称集合(返回)")
    @TableField(exist = false)
    @UpdateWithNull
    private List<String> subMajorLevelNames;


    /**
     * 子课程类型(多选)
     */
    @ApiModelProperty(value = "子课程类型(多选)")
    private List<Long> subCourseTypeIds;

    @ApiModelProperty(value = "子课程类型名称集合(返回)")
    @TableField(exist = false)
    @UpdateWithNull
    private List<String> subCourseTypeNames;

    /**
     * 子课程(多选)
     */
    @ApiModelProperty(value = "子课程(多选)")
    private List<Long> subCourseIds;

    @ApiModelProperty(value = "子课程名称集合(返回)")
    @TableField(exist = false)
    @UpdateWithNull
    private List<String> subCourseNames;

    /**
     * 子课和后续课程之间的条件标记，0:"AND",1:"OR"
     */
    @ApiModelProperty(value = "子课和后续课程之间的条件标记[0-AND,1-OR]")
    private Integer subAndLaterCourseFlag;

    /**
     * 后续课条件标记（如有任何后续课条件则标记为true,否则为false）
     */
    @ApiModelProperty(value = "后续课条件标记（如有任何后续课条件则标记为true,否则为false）")
    private Boolean laterCourseMark;

    /**
     * 后续课程学校提供商(单选)
     */
    @ApiModelProperty(value = "后续课程学校提供商Id")
    @UpdateWithNull
    private Long laterInstitutionProviderId;

    @ApiModelProperty(value = "后续课程学校提供商名称(返回)")
    @TableField(exist = false)
    @UpdateWithNull
    private String laterInstitutionProviderName;

    /**
     * 后续课程学校(多选)
     */
    @ApiModelProperty(value = "后续课程学校Ids")
    private List<Long> laterInstitutionIds;

    @ApiModelProperty(value = "后续课程学校名称集合(返回)")
    @TableField(exist = false)
    @UpdateWithNull
    private List<String> laterInstitutionNames;

    /**
     * 后续课程等级(多选)
     */
    @ApiModelProperty(value = "后续课程等级(多选)")
    private List<Long> laterUpMajorLevelIds;


    @ApiModelProperty(value = "后续课程等级名称集合(返回)")
    @TableField(exist = false)
    @UpdateWithNull
    private List<String> laterUpMajorLevelNames;

    /**
     * 后续课程类型(多选)
     */
    @ApiModelProperty(value = "后续课程类型(多选)")
    private List<Long> laterUpCourseTypeIds;

    @ApiModelProperty(value = "后续课程类型名称集合(返回)")
    @TableField(exist = false)
    @UpdateWithNull
    private List<String> laterUpCourseTypeNames;

    /**
     * 后续课程(多选)
     */
    @ApiModelProperty(value = "后续课程(多选)")
    private List<Long> laterUpCourseIds;

    @ApiModelProperty(value = "后续课程名称集合(返回)")
    @TableField(exist = false)
    @UpdateWithNull
    private List<String> laterUpCourseNames;

    @Data
    public static class DateTimeInfo {
        @ApiModelProperty(value = "开始时间")
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
        @UpdateWithNull
        private Date startTime;
        @ApiModelProperty(value = "结束时间")
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
        @UpdateWithNull
        private Date endTime;
    }

}
