package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.StudentSubjectScoreMapper;
import com.get.salecenter.entity.StudentSubjectScore;
import com.get.salecenter.service.IStudentSubjectScoreService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Author:cream
 * @Date: 2023/5/11  12:39
 */
@Service
public class IStudentSubjectScoreServiceImpl extends BaseServiceImpl<StudentSubjectScoreMapper, StudentSubjectScore> implements IStudentSubjectScoreService {


    @Resource
    private StudentSubjectScoreMapper studentSubjectScoreMapper;

    /**
     * Author Cream
     * Description : //删除被合并学生成绩数据
     * Date 2023/5/11 12:41
     * Params:
     * Return
     */
    @Override
    public void deleteByStudentId(Long mergedStudentId) {
        if (GeneralTool.isNotEmpty(mergedStudentId)) {
            if (studentSubjectScoreMapper.selectCount(Wrappers.<StudentSubjectScore>lambdaQuery().eq(StudentSubjectScore::getFkStudentId, mergedStudentId)) > 0) {
                studentSubjectScoreMapper.delete(Wrappers.<StudentSubjectScore>lambdaQuery().eq(StudentSubjectScore::getFkStudentId, mergedStudentId));
            }
        }
    }
}
