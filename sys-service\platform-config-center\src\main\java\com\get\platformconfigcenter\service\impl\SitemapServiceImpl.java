package com.get.platformconfigcenter.service.impl;

import com.get.core.mybatis.base.UtilService;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.platformconfigcenter.dao.appmso.MediaAndAttachedMapper;
import com.get.platformconfigcenter.service.DeleteService;
import com.get.platformconfigcenter.service.MediaAndAttachedMsoService;
import com.get.platformconfigcenter.service.SitemapService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * MSO菜单管理逻辑处理类
 *
 * <AUTHOR>
 * @date 2021/7/27 14:18
 */
@Service
public class SitemapServiceImpl implements SitemapService {

//    @Resource
//    private SitemapMapper sitemapMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private MediaAndAttachedMsoService mediaAndAttachedMsoService;
    @Resource
    private MediaAndAttachedMapper mediaAndAttachedMapper;
    @Resource
    private DeleteService deleteService;
    @Resource
    private IInstitutionCenterClient feignInstitutionService;
//    @Resource
//    private TranslationMapper translationMapper;
//    @Resource
//    private SitemapPageTemplateMapper sitemapPageTemplateMapper;

    /**
     * 菜单树
     *
     * @Date 14:29 2021/7/27
     * <AUTHOR>
     */
//    @Override
//    public List<SitemapVo> getSitemapTree() {
////        Example example = new Example(Sitemap.class);
////        example.createCriteria().andEqualTo("fkParentSitemapId", 0);
////        example.orderBy("viewOrder").desc();
////        List<Sitemap> sitemaps = sitemapMapper.selectByExample(example);
//        List<Sitemap> sitemaps = sitemapMapper.selectList(Wrappers.<Sitemap>lambdaQuery().eq(Sitemap::getFkParentSitemapId, 0).orderByDesc(Sitemap::getViewOrder));
//        List<SitemapVo> sitemapDtoList = sitemaps.stream().map(sitemap -> BeanCopyUtils.objClone(sitemap, SitemapVo::new)).collect(Collectors.toList());
//        if (GeneralTool.isEmpty(sitemapDtoList)) {
//            return new ArrayList<>();
//        }
//        getChildList(sitemapDtoList);
//        return sitemapDtoList;
//    }

//    @Override
//    public Long addSitemap(SitemapDto sitemapVo) {
//        if (GeneralTool.isEmpty(sitemapVo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
//        }
//        Sitemap sitemap = BeanCopyUtils.objClone(sitemapVo, Sitemap::new);
//        Integer maxViewOrder = sitemapMapper.getMaxViewOrder();
//        sitemap.setViewOrder(maxViewOrder);
//        utilService.updateUserInfoToEntity(sitemap);
//        sitemapMapper.insertSelective(sitemap);
//        //保存图片
//        List<MediaAndAttachedVo> mediaAndAttachedVoList = sitemapVo.getMediaAttachedVos();
//        if (GeneralTool.isNotEmpty(mediaAndAttachedVoList)) {
//            for (MediaAndAttachedVo mediaAndAttachedVo : mediaAndAttachedVoList) {
//                mediaAndAttachedVo.setTypeKey(FileTypeEnum.PLATFORM_MSO_AGENT_SITEMAP_COLUMN_PIC.key);
//                mediaAndAttachedVo.setFkTableId(sitemap.getId());
//                mediaAndAttachedVo.setFkTableName(TableEnum.M_SITEMAP.key);
//                mediaAndAttachedMsoService.addMediaAndAttached(mediaAndAttachedVo);
//            }
//        }
//        return sitemap.getId();
//    }

//    @Override
//    @DSTransactional
//    public void updateSitemap(SitemapDto sitemapVo) {
//        if (GeneralTool.isEmpty(sitemapVo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
//        }
//        Sitemap beforeSitemap = sitemapMapper.selectById(sitemapVo.getId());
//        if (GeneralTool.isEmpty(beforeSitemap)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        }
//        Sitemap sitemap = BeanCopyUtils.objClone(sitemapVo, Sitemap::new);
//        utilService.updateUserInfoToEntity(sitemap);
//        sitemapMapper.updateByPrimaryKey(sitemap);
//        //删除图片
////        Example example = new Example(MediaAndAttached.class);
////        example.createCriteria().andEqualTo("fkTableName", TableEnum.M_SITEMAP.key)
////                .andEqualTo("fkTableId", sitemap.getId()).andEqualTo("typeKey", FileTypeEnum.PLATFORM_MSO_AGENT_SITEMAP_COLUMN_PIC.key);
////        mediaAndAttachedMapper.deleteByExample(example);
//
//        mediaAndAttachedMapper.delete(Wrappers.<ConfigMediaAndAttached>lambdaQuery()
//                .eq(ConfigMediaAndAttached::getFkTableName, TableEnum.M_SITEMAP.key)
//                .eq(ConfigMediaAndAttached::getFkTableId, sitemap.getId())
//                .eq(ConfigMediaAndAttached::getTypeKey, FileTypeEnum.PLATFORM_MSO_AGENT_SITEMAP_COLUMN_PIC.key));
//        //保存图片
//        List<MediaAndAttachedVo> mediaAndAttachedVoList = sitemapVo.getMediaAttachedVos();
//        if (GeneralTool.isNotEmpty(mediaAndAttachedVoList)) {
//            for (MediaAndAttachedVo mediaAndAttachedVo : mediaAndAttachedVoList) {
//                mediaAndAttachedVo.setTypeKey(FileTypeEnum.PLATFORM_MSO_AGENT_SITEMAP_COLUMN_PIC.key);
//                mediaAndAttachedVo.setFkTableId(sitemap.getId());
//                mediaAndAttachedVo.setFkTableName(TableEnum.M_SITEMAP.key);
//                mediaAndAttachedMsoService.addMediaAndAttached(mediaAndAttachedVo);
//            }
//        }
//    }

//    @Override
//    public SitemapVo findSitemapById(Long id) {
//        if (GeneralTool.isEmpty(id)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
//        Sitemap sitemap = sitemapMapper.selectById(id);
//        if (GeneralTool.isEmpty(sitemap)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        }
//        SitemapVo sitemapDto = BeanCopyUtils.objClone(sitemap, SitemapVo::new);
//        sitemapDto.setFkTableName(TableEnum.M_SITEMAP.key);
//        sitemapDto.setMenuLinkTypeName(ProjectKeyEnum.getValue(sitemapDto.getMenuLinkType()));
//
//        String fkAreaCountryName = null;
//        if (GeneralTool.isNotEmpty(sitemapDto.getFkAreaCountryId())) {
//            //根据国家id获取名称111111
//            Result<String> resultfkAreaCountryName = feignInstitutionService.getCountryNameById(sitemapDto.getFkAreaCountryId());
//            if (resultfkAreaCountryName.isSuccess()) {
//                fkAreaCountryName = resultfkAreaCountryName.getData();
//            }
//        }
//        sitemapDto.setFkAreaCountryName(fkAreaCountryName);
//
//        /*List<SitemapVo> sitemapDtoList = new ArrayList<>();
//        sitemapDtoList.add(sitemapDto);
//        getChildList(sitemapDtoList);*/
//        //查询图片
//        MediaAndAttachedVo attachedVo = new MediaAndAttachedVo();
//        attachedVo.setFkTableName(TableEnum.M_SITEMAP.key);
//        attachedVo.setFkTableId(id);
//        attachedVo.setTypeKey(FileTypeEnum.PLATFORM_MSO_AGENT_SITEMAP_COLUMN_PIC.key);
//        List<MediaAndAttachedDto> mediaAndAttachedDtos = mediaAndAttachedMsoService.getMediaAndAttachedVo(attachedVo);
//        sitemapDto.setMediaAndAttachedDtoList(mediaAndAttachedDtos);
//        return sitemapDto;
//    }

//    @Override
//    public void delete(Long id) {
//        if (GeneralTool.isEmpty(id)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
//        //删除校验，如果有子菜单的话不可以删除
//        validateDelete(id);
//        boolean succuee = deleteService.deleteValidateSiteMap(id);
//        if (succuee) {
//            sitemapMapper.deleteById(id);
//        }
//
//        //删除翻译内容
//        translationMapper.deleteTranslations(TableEnum.M_SITEMAP.key, id);
//    }

//    @Override
//    public List<SitemapVo> getSiteMapByParentId(Long id, String keyWord) {
//        if (GeneralTool.isEmpty(id)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
////        Example example = new Example(Sitemap.class);
////        example.createCriteria().andEqualTo("fkParentSitemapId", id);
////        if(GeneralTool.isNotEmpty(keyWord)){
////            Example.Criteria criteria = example.createCriteria();
////            criteria.orLike("menuName","%"+keyWord+"%");
////            criteria.orLike("menuKey","%"+keyWord+"%");
////            criteria.orLike("webTitle","%"+keyWord+"%");
////            criteria.orLike("webMetaDescription","%"+keyWord+"%");
////            criteria.orLike("webMetaKeywords","%"+keyWord+"%");
////            example.and(criteria);
////        }
////        example.orderBy("viewOrder").desc();
//
//        LambdaQueryWrapper<Sitemap> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(Sitemap::getFkParentSitemapId, id);
//        if (GeneralTool.isNotEmpty(keyWord)) {
//            lambdaQueryWrapper.and(wrapper -> wrapper.like(Sitemap::getMenuName, keyWord)
//                    .or().like(Sitemap::getMenuKey, keyWord)
//                    .or().like(Sitemap::getWebTitle, keyWord)
//                    .or().like(Sitemap::getWebMetaDescription, keyWord)
//                    .or().like(Sitemap::getWebMetaKeywords, keyWord));
//        }
//        lambdaQueryWrapper.orderByDesc(Sitemap::getViewOrder);
//        List<Sitemap> sitemaps = sitemapMapper.selectList(lambdaQueryWrapper);
//        List<SitemapVo> sitemapDtoList = sitemaps.stream().map(sitemap -> BeanCopyUtils.objClone(sitemap, SitemapVo::new)).collect(Collectors.toList());
//        if (GeneralTool.isEmpty(sitemapDtoList)) {
//            return new ArrayList<>();
//        }
//        sitemapDtoList.stream().forEach(sitemap -> sitemap.setMenuLinkTypeName(ProjectKeyEnum.getValue(sitemap.getMenuLinkType())));
//        sitemapDtoList.stream().forEach(sitemap -> sitemap.setFkTableName(TableEnum.M_SITEMAP.key));
//        return sitemapDtoList;
//    }

//    @Override
//    public void movingOrder(List<SitemapDto> sitemapVo) {
//        if (GeneralTool.isEmpty(sitemapVo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
//        }
//        Sitemap ro = BeanCopyUtils.objClone(sitemapVo.get(0), Sitemap::new);
//        Integer oneorder = ro.getViewOrder();
//        Sitemap rt = BeanCopyUtils.objClone(sitemapVo.get(1), Sitemap::new);
//        Integer twoorder = rt.getViewOrder();
//        ro.setViewOrder(twoorder);
//        utilService.updateUserInfoToEntity(ro);
//        rt.setViewOrder(oneorder);
//        utilService.updateUserInfoToEntity(rt);
//        sitemapMapper.updateByPrimaryKeySelective(ro);
//        sitemapMapper.updateByPrimaryKeySelective(rt);
//    }


    /**
     * 获取子菜单
     *
     * @Date 14:43 2021/7/27
     * <AUTHOR>
     */
//    private void getChildList(List<SitemapVo> entityList) {
//        entityList.stream().forEach(sitemap -> sitemap.setMenuLinkTypeName(ProjectKeyEnum.getValue(sitemap.getMenuLinkType())));
//        for (SitemapVo sitemapDto : entityList) {
//            List<SitemapVo> childSitemap = sitemapMapper.getChildSitemap(sitemapDto.getId());
//            sitemapDto.setChildSitemapDtoList(childSitemap);
//            if (GeneralTool.isNotEmpty(childSitemap)) {
//                getChildList(childSitemap);
//            }
//        }
//    }

    /**
     * 校验当前菜单下面是否有子菜单，有的话不可以删除
     *
     * @param
     */
//    private void validateDelete(Long id) throws GetServiceException {
////        Example example = new Example(Sitemap.class);
////        example.createCriteria().andEqualTo("fkParentSitemapId", id);
////        example.orderBy("viewOrder").desc();
////        List<Sitemap> sitemaps = sitemapMapper.selectByExample(example);
//        List<Sitemap> sitemaps = sitemapMapper.selectList(Wrappers.<Sitemap>lambdaQuery().eq(Sitemap::getFkParentSitemapId, id).orderByDesc(Sitemap::getViewOrder));
//        if (GeneralTool.isNotEmpty(sitemaps)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("menus_contain_subMenus"));
//        }
//    }

//    @Override
//    public List<SitemapPageTemplate> sitemapPageTemplate() {
//        return sitemapPageTemplateMapper.sitemapPageTemplate();
//    }


}