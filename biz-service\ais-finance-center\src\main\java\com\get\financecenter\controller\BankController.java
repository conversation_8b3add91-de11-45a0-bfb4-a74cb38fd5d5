package com.get.financecenter.controller;

import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.financecenter.service.IBankService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/2/14
 * @TIME: 14:55
 * @Description:
 **/
@Api(tags = "银行管理")
@RestController
@RequestMapping("finance/bank")
public class BankController {
    @Resource
    private IBankService bankService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.officecenter.vo.LeaveApplicationFormTypeVo>
     * @Description :下拉框数据
     * @Param []
     * <AUTHOR>
     */
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "下拉框数据", notes = "")
    @GetMapping("bankSelect")
    public ResponseBo<BaseSelectEntity> bankSelect(@RequestParam String num) {
        List<BaseSelectEntity> list = bankService.bankSelect(num);
        return new ListResponseBo<>(list);
    }
}
