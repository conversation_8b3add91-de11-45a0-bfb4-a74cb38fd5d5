package com.get.officecenter.utils;

import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.GetDateUtil;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.utils.GeneralTool;
import com.get.officecenter.vo.LeaveApplicationFormVo;
import com.get.officecenter.entity.WorkScheduleDateConfig;
import com.get.officecenter.entity.WorkScheduleStaffConfig;
import com.get.officecenter.entity.WorkScheduleTimeConfig;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2022/5/11
 * @TIME: 17:14
 * @Description:考勤通用工具
 **/
public class AttendanceUtils {
    /**
     * 打卡生效开始时间
     * @param year
     * @param month
     * @param day
     * @return
     */
    public static Date getOpenClockIn(int year ,int month ,int day){
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.setTime(GetDateUtil.getDate(month < 1 ? year - 1:year,month < 1 ? 12:month ,day));
        calendar.set(Calendar.HOUR_OF_DAY, 7);
        calendar.set(Calendar.MINUTE, 30);
        calendar.set(Calendar.SECOND, 0);
        Date start = calendar.getTime();
        return start;
    }
    /**
     * 打卡生效结束时间
     * @param year 年
     * @param month 月
     * @param day 日
     * @param actualMaximum 月份的最大一天
     * @return
     */
    public static Date getCloseClockIn(int year ,int month ,int day,int actualMaximum){
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        int newDay = (day+1) > actualMaximum ? 1 : (day+1);
        int newMonth = (day+1) > actualMaximum ? (month+1) : month;
        int newYear = month > 12 ? year + 1 : year;
        newMonth = newMonth > 12 ? 1:newMonth;
        calendar.setTime(GetDateUtil.getDate(newYear , newMonth , newDay));
        calendar.set(Calendar.HOUR_OF_DAY, 4);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date end = calendar.getTime();
        return end;
    }

    /**
     * 获取指定时间的特殊人员时间配置
     * <AUTHOR>
     * @DateTime 2023/3/8 12:14
     */
    public static WorkScheduleStaffConfig getWorkScheduleStaffConfig(Date date,String formTypeKey,List<WorkScheduleStaffConfig> workScheduleStaffConfigList){
        if(GeneralTool.isEmpty(workScheduleStaffConfigList)){
            return null;
        }
        Integer week = GetDateUtil.getWeek(date) - 1 > 0 ? GetDateUtil.getWeek(date) - 1 : 7;
        WorkScheduleStaffConfig staffConfig = null;
        for(WorkScheduleStaffConfig cf : workScheduleStaffConfigList){
            if(cf.getEffectiveStartTime().compareTo(date) <= 0 && cf.getEffectiveEndTime().compareTo(date) >= 0){
                //类型为加班，忽略循环周字段
                if(ProjectKeyEnum.OVERTIEM.key.equals(formTypeKey)){
                    staffConfig = cf;
                }else{
                    List<String> result = Arrays.asList(cf.getWorkingWeekCycle().split(","));
                    if (result.contains(String.valueOf(week))) {
                        staffConfig = cf;
                    }
                }
            }
        }
        return staffConfig;
    }


    /**
     * 获取上班时间/下班时间/中午休息开始时间/中午休息结束时间
     * @param type  类型：1-获取上班时间，2-获取下班时间，3-获取中午休息开始时间，4-获取中午休息结束时间
     * @return 返回时分秒组成的list 例如:八点三十分[8,30,0]
     */
    public static List<Integer> getWorkScheduleTimeConfig(WorkScheduleTimeConfig workScheduleTimeConfig,
                                                          WorkScheduleStaffConfig staffConfig,
                                                          Integer type){

        //存在特殊人员时间设定,使用该时间设定
        if(GeneralTool.isNotEmpty(staffConfig)){
            switch (type) {
                case 1:
                    //上班时间
                    return formatWorkTime(staffConfig.getWorkingStartTime());
                case 2:
                    //下班时间
                    return formatWorkTime(staffConfig.getWorkingEndTime());
                case 3:
                    //午休开始时间
                    if(GeneralTool.isNotEmpty(staffConfig.getNoonBreakStartTime())){
                        return formatWorkTime(staffConfig.getNoonBreakStartTime());
                    }else {
                        return null;
                    }

                case 4:
                    //午休结束时间
                    if(GeneralTool.isNotEmpty(staffConfig.getNoonBreakEndTime())){
                        return formatWorkTime(staffConfig.getNoonBreakEndTime());
                    }else{
                        return null;
                    }

            }
        }else{
            switch (type) {
                case 1:
                    //上班时间
                    return formatWorkTime(workScheduleTimeConfig.getWorkingStartTime());
                case 2:
                    //下班时间
                    return formatWorkTime(workScheduleTimeConfig.getWorkingEndTime());
                case 3:
                    //午休开始时间
                    if(GeneralTool.isNotEmpty(workScheduleTimeConfig.getNoonBreakStartTime())){
                        return formatWorkTime(workScheduleTimeConfig.getNoonBreakStartTime());
                    }else{
                        return null;
                    }

                case 4:
                    //午休结束时间
                    if(GeneralTool.isNotEmpty(workScheduleTimeConfig.getNoonBreakEndTime())){
                        return formatWorkTime(workScheduleTimeConfig.getNoonBreakEndTime());
                    }else {
                        return null;
                    }

            }
        }

        return null;
    }

    /**
     * 判断是否为休假
     * <AUTHOR>
     * @DateTime 2023/2/28 16:00
     */
    public static Boolean dayOff(Date date,List<WorkScheduleDateConfig> workScheduleDateConfigList,WorkScheduleStaffConfig staffTimeConfig){
        //为节假日的排班
        List<WorkScheduleDateConfig> holiday = workScheduleDateConfigList.stream().filter(w -> w.getScheduleType().equals("0")).collect(Collectors.toList());
        //为工作日的排班
        List<WorkScheduleDateConfig> working = workScheduleDateConfigList.stream().filter(w -> w.getScheduleType().equals("1")).collect(Collectors.toList());

        //若当前日期为节假日排班，返回true
        for (WorkScheduleDateConfig workScheduleDateConfig : holiday) {
            if (GetDateUtil.isSameDay(workScheduleDateConfig.getScheduleDate(), date)) {
                return true;
            }
        }
        //周末
        int week = GetDateUtil.getWeek(date);
        //特殊人员判断
        if (GeneralTool.isNotEmpty(staffTimeConfig)){
            if (week==Calendar.SUNDAY){
                week=7;
            }else {
                week= week-1;
            }

            if (staffTimeConfig.getWorkingWeekCycle().contains(String.valueOf(week))) {
                return false;
            } else {
                return true;
            }


        }
        if (week == Calendar.SATURDAY || week == Calendar.SUNDAY) {
            if(GeneralTool.isEmpty(working)){
                return true;
            }
            //不为工作日排班
            for (WorkScheduleDateConfig workScheduleDateConfig : working) {
                if (!GetDateUtil.isSameDay(workScheduleDateConfig.getScheduleDate(), date)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 获取对应排班时间设定
     * <AUTHOR>
     * @DateTime 2023/1/12 18:19
     */
    public static WorkScheduleTimeConfig getTimeConfig(List<WorkScheduleTimeConfig> timeConfigList,Long fkCompanyId,Long departmentId){
        if(GeneralTool.isEmpty(timeConfigList)){
            return null;
        }
        List<WorkScheduleTimeConfig> list = timeConfigList.stream()
                .filter(t->GeneralTool.isNotEmpty(t.getFkDepartmentId())).collect(Collectors.toList());
        Map<Long, WorkScheduleTimeConfig> timeConfigMap = list.stream()
                .collect(Collectors.toMap(WorkScheduleTimeConfig::getFkDepartmentId, Function.identity()));
        //通过部门获取对应时间设定
        WorkScheduleTimeConfig workScheduleTimeConfig = timeConfigMap.get(departmentId);
        //部门未设置时间设定使用默认
        if(GeneralTool.isEmpty(workScheduleTimeConfig)){
            List<WorkScheduleTimeConfig> configs =  timeConfigList.stream().filter(c->GeneralTool.isEmpty(c.getFkDepartmentId()) && c.getFkCompanyId().equals(fkCompanyId)).collect(Collectors.toList());
            if(GeneralTool.isNotEmpty(configs)){
                workScheduleTimeConfig = configs.get(0);
            }
        }
        if(GeneralTool.isEmpty(workScheduleTimeConfig)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("workScheduleTimeConfig_not_set"));
        }
        return workScheduleTimeConfig;
    }

    public static List<Integer> formatWorkTime(String workingStartTime){
        String[] workingStart = workingStartTime.split(":");
        ArrayList<String> stringList = new ArrayList<>(workingStart.length);
        Collections.addAll(stringList,workingStart);
        return stringList.stream().map(Integer::valueOf).collect(Collectors.toList());
    }

    /**
     * 判断时间范围是否请假
     * @param leaveApplicationFormVos 工休单请假列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    public static Boolean isLeave(List<LeaveApplicationFormVo> leaveApplicationFormVos, Date startTime, Date endTime){
        List<LeaveApplicationFormVo> form = new ArrayList();
        if(GeneralTool.isNotEmpty(leaveApplicationFormVos)){
            for(LeaveApplicationFormVo formDto : leaveApplicationFormVos){
                if(formDto.getStartTime().compareTo(startTime) <= 0 && formDto.getEndTime().compareTo(endTime) >=0){
                    form.add(formDto);
                }
            }
        }
        if(GeneralTool.isNotEmpty(form)){
            return true;
        }
        return false;
    }


    /**
     * 计算两个时间段的工休申请时长
     * <AUTHOR>
     * @DateTime 2023/5/24 16:42
     */
    public static BigDecimal getDuration(List<WorkScheduleTimeConfig> workScheduleTimeConfigList,
                                         List<WorkScheduleStaffConfig> workScheduleStaffConfigList,
                                         List<WorkScheduleDateConfig> workScheduleDateConfigList,
                                         Long fkCompanyId,
                                         Long fkDepartmentId,
                                         String formTypeKey,
                                         Date startTime,
                                         Date endTime,
                                         Integer type){
        Date pointInTime = GetDateUtil.setTime(startTime,0,0,0) ;
        BigDecimal durationSum = BigDecimal.ZERO;
        BigDecimal days = BigDecimal.ZERO;
        //排班时间设定
        WorkScheduleTimeConfig timeConfig = getTimeConfig(workScheduleTimeConfigList, fkCompanyId, fkDepartmentId);
        //计算每天时长
        while(pointInTime.compareTo(endTime) <= 0){
            //特殊人员排班设定
            WorkScheduleStaffConfig workScheduleStaffConfig = getWorkScheduleStaffConfig(pointInTime,formTypeKey,workScheduleStaffConfigList);

            //校验是否为非工作日
            workScheduleDateConfigList = workScheduleDateConfigList.stream().filter(c->GeneralTool.isEmpty(c.getFkDepartmentId()) || c.getFkDepartmentId().equals(fkDepartmentId)).collect(Collectors.toList());
            Boolean ignoreDate= dayOff(pointInTime,workScheduleDateConfigList,workScheduleStaffConfig);
            //非加班类型，都忽略非工作日申请时长
            if(ignoreDate && !ProjectKeyEnum.OVERTIEM.key.equals(formTypeKey)){
                //第二天
                pointInTime = GetDateUtil.getTomorrowDate(pointInTime);
                continue;
            }

            //工作开始时间
            List<Integer> workingStart = getWorkScheduleTimeConfig(timeConfig, workScheduleStaffConfig,1);
            BigDecimal workingStartTime = new BigDecimal(workingStart.get(0) * 60 + workingStart.get(1));

            //工作结束时间
            List<Integer> workingEnd = getWorkScheduleTimeConfig(timeConfig, workScheduleStaffConfig,2);
            BigDecimal workingEndTime = new BigDecimal(workingEnd.get(0) * 60 + workingEnd.get(1));

            //工休申请开始时间
            BigDecimal applicationStartTime;
            if(GetDateUtil.isSameDay(pointInTime,startTime)){
                int applicationStartHour = GetDateUtil.getHour(startTime);
                int applicationStartMinute = GetDateUtil.getMinutes(startTime);
                applicationStartTime = new BigDecimal(applicationStartHour * 60 + applicationStartMinute);
            }else{
                applicationStartTime = workingStartTime;
            }

            //工休申请结束时间
            BigDecimal applicationEndTime;
            if(GetDateUtil.isSameDay(pointInTime,endTime)){
                int applicationEndHour = GetDateUtil.getHour(endTime);
                int applicationEndMinute = GetDateUtil.getMinutes(endTime);
                applicationEndTime = new BigDecimal(applicationEndHour * 60 + applicationEndMinute);
            }else{
                applicationEndTime = workingEndTime;
            }


            //获取当天需要工作的时长
            BigDecimal workingDuration = GeneralTool.isNotEmpty(workScheduleStaffConfig) ? workScheduleStaffConfig.getWorkingDuration() : timeConfig.getWorkingDuration();


            //午休占用时长
            BigDecimal noonBreakTime = new BigDecimal(0);
            List<Integer> noonBreakStartTimeConfig = getWorkScheduleTimeConfig(timeConfig, workScheduleStaffConfig, 3);
            List<Integer> noonBreakEndTimeConfig = getWorkScheduleTimeConfig(timeConfig, workScheduleStaffConfig, 4);
            BigDecimal noonBreakStartTime = new BigDecimal(0);
            BigDecimal noonBreakEndTime = new BigDecimal(0);
            if(GeneralTool.isNotEmpty(noonBreakStartTimeConfig) && GeneralTool.isNotEmpty(noonBreakEndTimeConfig)){
                noonBreakStartTime = new BigDecimal(noonBreakStartTimeConfig.get(0) * 60 + noonBreakStartTimeConfig.get(1));
                noonBreakEndTime = new BigDecimal(noonBreakEndTimeConfig.get(0) * 60 + noonBreakEndTimeConfig.get(1));
                BigDecimal noonHeadTime = noonBreakStartTime;
                BigDecimal noonTailTime = noonBreakEndTime;
                if(applicationStartTime.compareTo(noonBreakStartTime) > 0 && applicationStartTime.compareTo(noonBreakEndTime) < 0 ){
                    noonHeadTime = applicationStartTime;
                }
                if(GetDateUtil.getTomorrowDate(pointInTime).compareTo(endTime) > 0){
                    if(applicationEndTime.compareTo(noonBreakStartTime) > 0 && applicationEndTime.compareTo(noonBreakEndTime) < 0){
                        noonTailTime = applicationEndTime;
                    }
                }
                noonBreakTime = noonTailTime.subtract(noonHeadTime);

            }

            //HTI加班类型时长计算
            if(ProjectKeyEnum.OVERTIEM.key.equals(formTypeKey)){
                BigDecimal betweenTime = new BigDecimal(0);
                //每天开始和结尾时间(单位分钟)
                BigDecimal headTime = GetDateUtil.isSameDay(pointInTime,startTime) ? applicationStartTime : new BigDecimal(0);
                BigDecimal tailTime = GetDateUtil.isSameDay(pointInTime,endTime) ? applicationEndTime : new BigDecimal(1440);

                //工作日
                if(!ignoreDate){
                    if((headTime.compareTo(workingStartTime) < 0 && tailTime.compareTo(workingStartTime) < 0 )||
                       (headTime.compareTo(workingEndTime) > 0 && tailTime.compareTo(workingEndTime) > 0)
                    ){
                        betweenTime = tailTime.subtract(headTime);
                    }else{

                        BigDecimal betweenStartTime = new BigDecimal(0);
                        if(headTime.compareTo(workingStartTime) < 0 && tailTime.compareTo(workingStartTime) >= 0){
                            betweenStartTime = workingStartTime.subtract(headTime);
                        }

                        BigDecimal betweenEndTime = new BigDecimal(0);
                        if(headTime.compareTo(workingEndTime) <= 0 && tailTime.compareTo(workingEndTime) > 0){
                            betweenEndTime = tailTime.subtract(workingEndTime);
                        }

                        betweenTime = betweenStartTime.add(betweenEndTime);
                    }

                }
                //非工作日
                if(ignoreDate){
                    betweenTime = tailTime.subtract(headTime);
                    //跨度午休，扣减午休占用时长
                    if (headTime.compareTo(noonBreakEndTime) < 0 && tailTime.compareTo(noonBreakStartTime)>0) {
                        betweenTime = betweenTime.subtract(noonBreakTime);
                    }
                }
                betweenTime = betweenTime.divide(new BigDecimal("60"), 2, BigDecimal.ROUND_HALF_UP);
                durationSum = durationSum.add(betweenTime);
                days = days.add(betweenTime.divide(workingDuration,2, BigDecimal.ROUND_HALF_UP));
                //第二天
                pointInTime = GetDateUtil.getTomorrowDate(pointInTime);
                continue;
            }

            if(GetDateUtil.isSameDay(pointInTime,startTime) || GetDateUtil.isSameDay(pointInTime,endTime)){
                //时间间隔
                BigDecimal headTime = applicationStartTime.compareTo(workingStartTime) < 0 ? workingStartTime : applicationStartTime;
                BigDecimal tailTime = applicationEndTime.compareTo(workingEndTime) > 0 ? workingEndTime :  applicationEndTime;

                BigDecimal betweenTime = tailTime.subtract(headTime);
                //工休跨度午休，扣减午休占用时长
                if (applicationStartTime.compareTo(noonBreakEndTime) < 0 && applicationEndTime.compareTo(noonBreakStartTime)>0) {
                    betweenTime = betweenTime.subtract(noonBreakTime);
                }
                //分钟转小时
                betweenTime = betweenTime.divide(new BigDecimal("60"), 2, BigDecimal.ROUND_HALF_UP);
                durationSum = durationSum.add(betweenTime);
                days = days.add(betweenTime.divide(workingDuration,2, BigDecimal.ROUND_HALF_UP));
                //第二天
                pointInTime = GetDateUtil.getTomorrowDate(pointInTime);
                continue;
            }

            //直接增加当天工作时长
            durationSum = durationSum.add(workingDuration);
            //天数加1
            days = days.add(new BigDecimal(1));
            pointInTime = GetDateUtil.getTomorrowDate(pointInTime);
        }

        //HTI相关申请时长取整
        BigDecimal bg = durationSum;
        BigDecimal returnDays = days;
            //1、HTI【无薪事假】【年假】【病假】【补休】限制申请时长为：2，4，8小时，不够向上取整
            if(ProjectKeyEnum.LEAVE_VACATION.key.equals(formTypeKey) || ProjectKeyEnum.ANNUAL_VACATION.key.equals(formTypeKey) ||
               ProjectKeyEnum.DISEASE_VACATION.key.equals(formTypeKey) ||  ProjectKeyEnum.TAKE_DEFERRED_HOLIDAYS.key.equals(formTypeKey)){
                //申请时长取余8小时
                BigDecimal[] bigDecimals = bg.divideAndRemainder(new BigDecimal(8));
                BigDecimal remain = bigDecimals[1];
                //将余数取余2小时
                BigDecimal[] bigDecimals1 = remain.divideAndRemainder(new BigDecimal(2));
                //原时长+向上取整时长
                if(bigDecimals1[1].compareTo(BigDecimal.ZERO) > 0){
                    bg = durationSum.add(new BigDecimal(2).subtract(bigDecimals1[1]));
                }

                //计算天数
                returnDays = bg.divide(new BigDecimal(8),2, BigDecimal.ROUND_HALF_UP);
            }

            //2、HTI【加班】为向下取整，如：0.5取0，若小于2小时的加班，提示不能提交。
            if(ProjectKeyEnum.OVERTIEM.key.equals(formTypeKey)){
                if(bg.compareTo(new BigDecimal(2)) < 0){
                    throw new GetServiceException(LocaleMessageUtils.getMessage("apply_for_overtime_fail"));
                }
                bg = durationSum.setScale(0,BigDecimal.ROUND_DOWN);

                //计算天数
                returnDays = bg.divide(new BigDecimal(8),2, BigDecimal.ROUND_HALF_UP);
            }

        return type.equals(1) ? bg : returnDays;
    }

}
