package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.salecenter.entity.StudentServiceFeeCost;
import com.get.salecenter.vo.ServiceFeePayFormDetailVo;
import com.get.salecenter.vo.StudentProjectRoleStaffVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 导出佣金提成结算表VO
 */
@Data
public class StudentFeeCommissionStatementExportDto {

    @ApiModelProperty("结算币种")
    @NotBlank(message = "结算币种不能为空")
    private String settlementCurrencyTypeNum;

    @ApiModelProperty("提成比率")
    @NotNull(message = "提成比率不能为空")
    private BigDecimal royaltyRate;

    @ApiModelProperty("下属提成比率")
    @NotNull(message = "下属提成比率不能为空")
    private BigDecimal subordinateRoyaltyRate;

    //=================继承StudentServiceFeeSummaryVo==============================
    @ApiModelProperty("服务费id")
    private Long serviceFeeId;

    @ApiModelProperty("公司id")
    private Long targetCompanyNameId;

    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("服务费状态")
    private String serviceFeeStatus;

    /**
     * ProjectExtraEnum.SETTLEMENT_TYPE
     */
    //@ApiModelProperty("结算状态：1处理中/2已完成")
    @ApiModelProperty("结算状态：0未开始/1已提交财务/2已完成")
    private Integer settlementStatus;

    @ApiModelProperty("结算状态名称")
    private String settlementStatusName;

    @ApiModelProperty("应收币种")
    private String fkReceivableCurrencyNum;

    @ApiModelProperty("应收金额")
    private String receivableAmount;

    @ApiModelProperty("true已有应收")
    private Boolean receivableFlag = false;

    @ApiModelProperty("true已有应付")
    private Boolean payableFlag = false;

    @ApiModelProperty("应付币种")
    private String fkPayableCurrencyNum;

    @ApiModelProperty("服务费状态id")
    private Integer status;

    @ApiModelProperty("应付金额")
    private String payableAmount;

    @ApiModelProperty("实付金额")
    private String paidAmount;

    @ApiModelProperty("实收金额")
    private String ReceiptAmount;

    @ApiModelProperty("应付未付")
    private String payableDiffAmount;

    /**
     * ProjectExtraEnum.AR_STATUS
     */
    @ApiModelProperty("收款状态：0未收/1部分已收/2已收齐")
    private Integer receiveStatus;

    /**
     * ProjectExtraEnum.AP_STATUS
     */
    @ApiModelProperty("付款状态：0未付/1已付部分/2已付齐")
    private Integer payableStatus;

    @ApiModelProperty("收款状态名称")
    private String collectionStatus;

    @ApiModelProperty("付款状态名称")
    private String paymentStatus;

    @ApiModelProperty("服务费编号")
    private String serviceFeeNum;

    @ApiModelProperty("代理Id")
    private Long fkAgentId;

    @ApiModelProperty("代理名称")
    private String fkAgentName;

    @ApiModelProperty("BD名称")
    private String bdName;

    @ApiModelProperty("业务国家IDS")
    private String countryIds;

    @ApiModelProperty("业务国家名称")
    private String countryNames;

    @ApiModelProperty("服务费类型名称")
    private String serviceFeeTypeName;

    @ApiModelProperty("服务费币种")
    private String serviceFeeCurrencyNum;

    @ApiModelProperty("服务费币种名称")
    private String serviceFeeCurrencyName;

    /**
     * 付款情况信息显示
     */
    @ApiModelProperty(value = "付款情况信息显示（币种=payableCurrencyTypeName，实付金额=actualPayableAmount，付款时间=gmtCreate）")
    private List<ServiceFeePayFormDetailVo> payDetailList;

    @ApiModelProperty("学生id")
    private Long fkStudentId;

    @ApiModelProperty("学生名称")
    private String studentName;

    /**
     * amount+IFNULL(taxes,0)
     */
    @ApiModelProperty("服务费金额")
    private BigDecimal serviceFeeAmount;

    /**
     * amount字段
     */
    @ApiModelProperty("税前金额")
    private BigDecimal pretaxAmount;

    /**
     * taxes字段
     */
    @ApiModelProperty("服务费税金")
    private BigDecimal serviceFeeTaxes;

    @ApiModelProperty("业务状态：0未完成/1已完成")
    private Integer businessStatus;

    @ApiModelProperty("业务状态名称")
    private String businessStatusName;

    @ApiModelProperty("结算状态审批：0未审批/1已审批")
    private Integer approveStatus;

    @ApiModelProperty("结算状态审批名称")
    private String approveStatusName;

    @ApiModelProperty("业务发生开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date businessStartTime;

    @ApiModelProperty("业务发生结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date businessEndTime;

    @ApiModelProperty("业务发生时间")
    private String businessTimeStr;

    @ApiModelProperty("服务费备注")
    private String remark;

    @ApiModelProperty("项目成员")
    private List<StudentProjectRoleStaffVo> projectRoleStaffDtos;

    @ApiModelProperty("创建人")
    private String gmtCreateUser;

    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    /**
     * 应收应付IDS
     */
    @ApiModelProperty(value = "应收应付IDS")
    private List<Long> aRAPIds;

    /**
     * 成本支出实体集合
     */
    @ApiModelProperty(value = "成本支出实体集合")
    List<StudentServiceFeeCost> serviceFeeCosts;

    @ApiModelProperty(value = "成本支出")
    private String studentServiceFeeCosts;

    @ApiModelProperty(value = "是否有服务费在途结算")
    private Boolean settlementFlag = false;

    @ApiModelProperty("收款方类型，枚举，如：m_student/m_institution_provider")
    private String fkTypeKeyReceivable;

    @ApiModelProperty("收款方类型Id，如：m_student.id")
    private Long fkTypeTargetIdReceivable;

    @ApiModelProperty("收款方类型名")
    private String fkTypeKeyReceivableName;

    @ApiModelProperty("收款方名")
    private String receivableName;

    @ApiModelProperty("服务类型id")
    @Column(name = "fk_student_service_fee_type_id")
    private Long fkStudentServiceFeeTypeId;

    @ApiModelProperty("服务类型key")
    private String serviceFeeTypeKey;



    @ApiModelProperty("公司id")
    private Long fkCompanyId;

    @ApiModelProperty("公司id")
    private List<Long> fkCompanyIds;


    @ApiModelProperty("代理名称")
    private String agentName;



    @ApiModelProperty("项目成员名称")
    private String projectRoleName;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    @ApiModelProperty("部门id")
    private Long fkDepartmentId;



    @ApiModelProperty("学生服务费类型Id")
    private List<Long> fkStudentServiceFeeTypeIds;

    /**
     * 应收状态
     */
    @ApiModelProperty(value = "应收状态：0/未收 1/部分已收 2/收款完成")
    private Integer arStatus;

    /**
     * 应付状态
     */
    @ApiModelProperty(value = "应付状态：0/未付 1/部分已付 2/已付清 3/未付【已创建应付计划】")
    private Integer apStatus;



    @ApiModelProperty("审批开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date approveStartTime;

    @ApiModelProperty("审批结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date approveEndTime;


    @ApiModelProperty("销售开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date salesStartTime;

    @ApiModelProperty("销售结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date salesEndTime;
}
