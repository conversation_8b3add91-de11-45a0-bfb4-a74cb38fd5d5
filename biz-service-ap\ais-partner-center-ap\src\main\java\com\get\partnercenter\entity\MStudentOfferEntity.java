package com.get.partnercenter.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-05-19 14:34:43
 */

@Data
@TableName("m_student_offer") 
public class MStudentOfferEntity extends Model<MStudentOfferEntity>{

  @ApiModelProperty(  "学生申请方案Id")
  private Long id;
 

  @ApiModelProperty(  "学生Id")
  private Long fkStudentId;
 

  @ApiModelProperty(  "代理Id（业绩绑定）")
  private Long fkAgentId;
 

  @ApiModelProperty(  "员工Id（业绩绑定，BD）")
  private Long fkStaffId;
 

  @ApiModelProperty(  "国家Id")
  private Long fkAreaCountryId;
 

  @ApiModelProperty(  "申请方案终止作废原因Id")
  private Long fkCancelOfferReasonId;
 

  @ApiModelProperty(  "申请方案编号")
  private String num;
 

  @ApiModelProperty(  "代理联系人邮箱地址")
  private String agentContactEmail;
 

  @ApiModelProperty(  "备注")
  private String remark;
 

  @ApiModelProperty(  "状态：0关闭/1打开/2终止/3成功结案")
  private Integer status;
 

  @ApiModelProperty(  "申请人员工Id")
  private Long fkStaffIdWorkflow;
 

  @ApiModelProperty(  "状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销")
  private Integer statusWorkflow;
 

  @ApiModelProperty(  "旧数据id(gea)")
  private String idGea;
 

  @ApiModelProperty(  "旧数据id(iae)")
  private String idIae;
 

  @ApiModelProperty(  "创建时间")
  private LocalDateTime gmtCreate;
 

  @ApiModelProperty(  "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @ApiModelProperty(  "修改时间")
  private LocalDateTime gmtModified;
 

  @ApiModelProperty(  "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
