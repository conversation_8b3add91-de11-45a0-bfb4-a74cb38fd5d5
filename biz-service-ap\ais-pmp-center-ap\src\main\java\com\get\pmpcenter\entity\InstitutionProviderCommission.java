package com.get.pmpcenter.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  11:32
 * @Version 1.0
 */
@Data
@TableName("m_institution_provider_commission")
public class InstitutionProviderCommission extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "学校提供商Id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "学校提供商佣金方案Id")
    private Long fkInstitutionProviderCommissionPlanId;

    @ApiModelProperty(value = "佣金类型：1课程/2阶梯/3组合")
    private Integer commissionType;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "标题（中文）")
    private String titleChn;

    @ApiModelProperty(value = "阶梯起始学生数")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer studentCountMin;

    @ApiModelProperty(value = "阶梯结束学生数")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer studentCountMax;

    @ApiModelProperty(value = "阶梯统计类型：0不追加/1从第1个学生开始计算")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer studentCountType;

    @ApiModelProperty(value = "佣金")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal commission;

    @ApiModelProperty(value = "佣金单位：%/CNY/等货币编号")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String commissionUnit;

    @ApiModelProperty(value = "后续学校提供商Id（不一致才需要选择）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long fkInstitutionProviderIdFollow;

    @ApiModelProperty(value = "后续佣金")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal followCommission;

    @ApiModelProperty(value = "后续佣金单位：%/CNY/等货币编号")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String followCommissionUnit;

    @ApiModelProperty(value = "备注")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remarkNote;

    @ApiModelProperty(value = "备注（中文）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remarkNoteChn;

    @ApiModelProperty(value = "组合名称（同组相同）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String packageName;

    @ApiModelProperty(value = "组合名称（中文）（同组相同）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String packageNameChn;

    @ApiModelProperty(value = "组合key（同组相同，且唯一）")
    private String packageKey;

    @ApiModelProperty(value = "对应旧系统佣金id，文本记录")
    private String oldCommissionId;

    @ApiModelProperty(value = "对应旧系统学校id，文本记录")
    private String oldCollegeId;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "是否激活：0否/1是")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer isActive;

    @ApiModelProperty(value = "课程")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String course;

    @ApiModelProperty(value = "后续备注")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String followRemarkNote;

    @ApiModelProperty(value = "后续备注（中文）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String followRemarkNoteChn;
}