package com.get.resumecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.resumecenter.vo.MediaAndAttachedVo;
import com.get.resumecenter.vo.ResumeVo;
import com.get.resumecenter.service.IResumeService;
import com.get.resumecenter.dto.MediaAndAttachedDto;
import com.get.resumecenter.dto.ResumeDto;
import com.get.resumecenter.dto.query.ResumeQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/3
 * @TIME: 12:33
 * @Description: 简历管理
 **/

@Api(tags = "简历管理")
@RestController
@RequestMapping("resume/resume")
public class ResumeController {
    @Autowired
    private IResumeService resumeService;


    @ApiOperation(value = "列表数据", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.LIST, description = "人才中心/简历管理/查询简历")
    @PostMapping("datas")
    public ResponseBo<ResumeVo> datas(@RequestBody SearchBean<ResumeQueryDto> voSearchBean) {
        List<ResumeVo> resumeListDtos = resumeService.getResumeListDtos(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(resumeListDtos, page);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.resumecenter.vo.ResumeVo>
     * @Description: 详情
     * @Param [resumeId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.DETAIL, description = "人才中心/简历管理/参数详情")
    @GetMapping("/{resumeId}")
    public ResponseBo<ResumeVo> detail(@PathVariable("resumeId") Long resumeId) {
        ResumeVo resumeVo = resumeService.getResumeById(resumeId);
        return new ResponseBo<>(resumeVo);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.resumecenter.vo.ResumeVo>
     * @Description: 详情
     * @Param [resumeId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "Guid查找详情接口", notes = "Guid查找详情接口")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.DETAIL, description = "人才中心/简历管理/参数详情")
    @GetMapping("/getResumeByGuid/{guid}")
    public ResponseBo<ResumeVo> getResumeByGuid(@PathVariable("guid") String guid) {
        ResumeVo resumeVo = resumeService.getResumeByGuid(guid);
        return new ResponseBo<>(resumeVo);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [resumeDto]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "新增接口")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.ADD, description = "人才中心/简历管理/新增简历")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(ResumeDto.Add.class)  ResumeDto resumeDto) {
        return SaveResponseBo.ok(resumeService.addResume(resumeDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.resumecenter.vo.ResumeVo>
     * @Description: 修改信息
     * @Param [resumeDto]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.EDIT, description = "人才中心/简历管理/修改简历")
    @PostMapping("update")
    public ResponseBo<ResumeVo> update(@RequestBody @Validated(ResumeDto.Update.class)  ResumeDto resumeDto) {
        return UpdateResponseBo.ok(resumeService.updateResume(resumeDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.DELETE, description = "人才中心/简历管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        resumeService.deleteResume(id);
        return ResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 保存文件接口
     * @Param [mediaAttachedVos]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "保存文件接口")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.ADD, description = "人才中心/简历管理/保存")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> upload(@RequestBody @Validated(MediaAndAttachedDto.Add.class)  ValidList<MediaAndAttachedDto> mediaAttachedVos) {
        return UpdateResponseBo.ok(resumeService.saveResumeMedia(mediaAttachedVos));
    }

}
