package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.ConventionTableTypeVo;
import com.get.salecenter.service.IConventionTableTypeService;
import com.get.salecenter.dto.ConventionTableTypeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/25 15:00
 * @verison: 1.0
 * @description: 桌台类型管理控制器
 */
@Api(tags = "桌台类型管理")
@RestController
@RequestMapping("sale/tableType")
public class ConventionTableTypeController {

    @Resource
    private IConventionTableTypeService tableTypeService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/桌台类型管理/桌台类型详情")
    @GetMapping("/{id}")
    public ResponseBo<ConventionTableTypeVo> detail(@PathVariable("id") Long id) {
        ConventionTableTypeVo data = tableTypeService.findTableTypeById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 批量新增信息
     *
     * @param conventionTableTypeDtos
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/桌台类型管理/新增桌台类型")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(ConventionTableTypeDto.Add.class) ValidList<ConventionTableTypeDto> conventionTableTypeDtos) {
        tableTypeService.batchAdd(conventionTableTypeDtos);
        return SaveResponseBo.ok();
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/桌台类型管理/删除桌台类型")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        tableTypeService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param tableTypeVo
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/桌台类型管理/更新桌台类型")
    @PostMapping("update")
    public ResponseBo<ConventionTableTypeVo> update(@RequestBody @Validated(ConventionTableTypeDto.Update.class) ConventionTableTypeDto tableTypeVo) {
        return UpdateResponseBo.ok(tableTypeService.updateTableType(tableTypeVo));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为搜索关键字(类型名称，类型key)")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/桌台类型管理/查询桌台类型")
    @PostMapping("datas")
    public ResponseBo<ConventionTableTypeVo> datas(@RequestBody SearchBean<ConventionTableTypeDto> page) {
        List<ConventionTableTypeVo> datas = tableTypeService.getTableTypes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 上移下移
     *
     * @param tableTypeVos
     * @return
     * @
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/酒店房型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<ConventionTableTypeDto> tableTypeVos) {
        tableTypeService.movingOrder(tableTypeVos);
        return ResponseBo.ok();
    }

    /**
     * 桌台类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "桌台类型下拉框数据", notes = "")
    @GetMapping("getTableTypeList")
    public ResponseBo getTableTypeList() {
        List<ConventionTableTypeVo> datas = tableTypeService.getTableTypeList();
        return new ListResponseBo<>(datas);
    }
}
