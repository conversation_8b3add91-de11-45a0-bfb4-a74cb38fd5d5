package com.get.examcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.examcenter.vo.QuestionTypeVo;
import com.get.examcenter.service.IQuestionTypeService;
import com.get.examcenter.dto.QuestionTypeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/8/24 17:03
 */
@Api(tags = "考题类型管理")
@RestController
@RequestMapping("exam/QuestionType")
public class QuestionTypeController {

    @Autowired
    private IQuestionTypeService questionTypeService;

    /**
     * @ Description :保存与更新考题类型
     * @ Param [questionTypeVo]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation("保存与更新考题类型")
    @PostMapping("saveOrUpdataQuestionType")
    public ResponseBo saveOrUpdataQuestionType(@RequestBody QuestionTypeDto questionTypeDto) {
        Long aLong = questionTypeService.saveOrUpdataQuestionType(questionTypeDto);
        return new ResponseBo(aLong);
    }

    /**
     * @ Description :列表
     * @ Param [page]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation("列表")
    @PostMapping("getQuestionTypeList")
    public ResponseBo getQuestionTypeList(@RequestBody SearchBean<QuestionTypeDto> page) {
        List<QuestionTypeVo> questionTypeList = questionTypeService.getQuestionTypeList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(questionTypeList, p);
    }

    /**
     * @ Description :删除
     * @ Param [id]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation("删除")
    @PostMapping("deleteQuestionType")
    public ResponseBo deleteQuestionType(@RequestParam("id") Long id) {
        questionTypeService.deleteQuestionType(id);
        return ResponseBo.ok();
    }

    /**
     * @Description: 详情
     * @Author: Jerry
     * @Date:10:27 2021/8/27
     */
    @ApiOperation(value = "详情", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.DETAIL, description = "考试中心/考题类型管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<QuestionTypeVo> detail(@PathVariable("id") Long id) {
        return new ResponseBo(questionTypeService.detail(id));
    }

    /**
     * @Description: feign调用 根据考题类型ids获取名称
     * @Author: Jerry
     * @Date:10:17 2021/8/27
     */
    @ApiIgnore
    @PostMapping("getNamesByQuestionTypeIds")
    public Map<Long, String> getNamesByQuestionTypeIds(@RequestBody Set<Long> questionTypeIds) {
        return questionTypeService.getNamesByQuestionTypeIds(questionTypeIds);
    }


    /**
     * @Description: 考题类型下拉框
     * @Author: Jerry
     * @Date:9:47 2021/9/3
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "考题类型下拉框", notes = "")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.LIST, description = "考试中心/考题类型管理/考题类型下拉框")
    @PostMapping("/questionTypeSelect")
    public ResponseBo<BaseSelectEntity> questionTypeSelect(@RequestParam("fkCompanyId") String fkCompanyId) {
        return new ListResponseBo<>(questionTypeService.questionTypeSelect(fkCompanyId));
    }


    /**
     * @Description: 上移下移
     * @Author: Jerry
     * @Date:9:52 2021/9/13
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.EDIT, description = "考试中心/考题类型管理/上移下移")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<QuestionTypeDto> questionTypeDtos) {
        questionTypeService.movingOrder(questionTypeDtos);
        return ResponseBo.ok();
    }
}
