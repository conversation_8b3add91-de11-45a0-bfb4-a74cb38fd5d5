package com.get.salecenter.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.get.common.consts.BaseConstant;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.SystemPageVo;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.institutioncenter.vo.AreaCityVo;
import com.get.institutioncenter.vo.AreaStateVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.config.NettyConfig;
import com.get.salecenter.config.NettyPushConfig;
import com.get.salecenter.dao.sale.StudentOfferItemMapper;
import com.get.salecenter.dao.sale.StudentOfferItemStepMapper;
import com.get.salecenter.netty.protocol.Command;
import com.get.salecenter.service.IStudentCountService;
import com.get.salecenter.service.ISystemPageService;
import com.get.salecenter.vo.*;
import io.netty.channel.Channel;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2021/2/2
 * @TIME: 16:43
 * @Description:
 **/
@Slf4j
@Service
public class SystemPageServiceImpl implements ISystemPageService {
    @Resource
    private IStudentCountService studentCountService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Resource
    private IFinanceCenterClient financeCenterClient;

    @Resource
    private StudentOfferItemMapper studentOfferItemMapper;
    @Resource
    private StudentOfferItemStepMapper studentOfferItemStepMapper;

    private final Map<String, BigDecimal> exchangeRateCache = new HashMap<>();

    /**
     * 首页柱状图初始化
     *
     * @param companyIds
     * @param num
     * @param year
     * @param statisticsFlag true:统计申请计划数  false:统计学生数
     * @return
     */
    @Override
    public SystemPageVo1 getInitialDatas(List<Long> companyIds, String num, String year, Boolean statisticsFlag) {
        Long staffId = SecureUtil.getStaffId();

        List<Long> staffFollowerIds = new ArrayList<>();
        //员工id + 业务下属员工ids
        if (GeneralTool.isNotEmpty(permissionCenterClient.getStaffFollowerIds(staffId).getData())) {
            staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
        staffFollowerIds.add(staffId);

        List<Long> countryIds = SecureUtil.getCountryIds();
        SystemPageVo1 systemPageVo1 = new SystemPageVo1();
        systemPageVo1.setStudentTotalSum(studentCountService.getStudentTotalSum(companyIds, countryIds, year, SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(), staffFollowerIds, SecureUtil.getStaffInfo().getIsStudentAdmin()));
        systemPageVo1.setStudentCountRecords(studentCountService.getStudentCountRecord(companyIds, countryIds, year));
        if (GeneralTool.isEmpty(GetAuthInfo.getUser())) {
            systemPageVo1.setCountryStudentCount(studentCountService.getAllWorldHistogram(companyIds, year));
        } else {
            systemPageVo1.setCountryStudentCount(studentCountService.getWorldHistogram(companyIds, SecureUtil.getCountryIdsByStaffId(GetAuthInfo.getStaffId()), year, statisticsFlag));
        }
        List<WorldHistogramVo> list;
        if ("TW".equals(num)) {
            List<Long> stateIds = getStateIds(num);
            Long stateId = stateIds.get(0);
            List<Long> cityIds = getCityIds(stateId);
            list = studentCountService.getCityStudentNum(companyIds, countryIds, cityIds, year, statisticsFlag, SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(), staffFollowerIds);
//            int size;
//            if (list.size() >= 10) {
//                size = 10;
//                list = list.subList(0, size);
//            }
        } else {
            List<Long> stateIds = getStateIds(num);
            list = studentCountService.getStateStudentNum(companyIds, countryIds, stateIds, year, statisticsFlag, SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(), staffFollowerIds);
//            int size;
//            if (list.size() >= 10) {
//                size = 10;
//                list = list.subList(0, size);
//            }
        }

        systemPageVo1.setStateStudentCount(list);
        systemPageVo1.setWorldMapDtos(studentCountService.getWorldMapDtos(companyIds, countryIds, year, false));
        systemPageVo1.setNationallonlat(JSONArray.fromObject(BaseConstant.NATIONAL_LONGITUDE_LATITUDE));
//        Set<String> publicCountryHomeNums = feignInstitutionService.selectPublicCountryHomeNums();
        Map<String, JSONArray> map = new HashMap<>();
//        for (String countryNum : publicCountryHomeNums) {
//            if ("CHN".equals(countryNum)) {
        map.put("中国", JSONArray.fromObject(BaseConstant.PROVINCES_LONGITUDE_LATITUDE));
//            } else if ("VNM".equals(countryNum)) {
        map.put("越南", JSONArray.fromObject(BaseConstant.PROVINCES_LONGITUDE_LATITUDE_VN));
//            } else if ("MYS".equals(countryNum)) {
        map.put("马来西亚", JSONArray.fromObject(BaseConstant.PROVINCES_LONGITUDE_LATITUDE_MY));
//            } else if ("IDN".equals(countryNum)) {
        map.put("印尼", JSONArray.fromObject(BaseConstant.PROVINCES_LONGITUDE_LATITUDE_INA));
//            } else if ("TW".equals(countryNum)) {
        map.put("台湾（中国）", JSONArray.fromObject(BaseConstant.PROVINCES_LONGITUDE_LATITUDE_TW));
//            }
//        }

        systemPageVo1.setProvinceslonlat(JSONObject.fromObject(map));
        return systemPageVo1;
    }

    /**
     * 首页学生/申请计划统计数
     * <p>
     * statisticsFlag true:统计申请计划数  false:统计学生数
     *
     * @Date 18:00 2022/8/2
     * <AUTHOR>
     */
    @Override
    public Long getStudentTotalSum(List<Long> companyIds, List<Long> areaCountryIds, String num, String year, Boolean statisticsFlag) {

        Long staffId = SecureUtil.getStaffId();

        List<Long> staffFollowerIds = new ArrayList<>();
        //员工id + 业务下属员工ids
        if (GeneralTool.isNotEmpty(permissionCenterClient.getStaffFollowerIds(staffId).getData())) {
            staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
        staffFollowerIds.add(staffId);

        areaCountryIds = GeneralTool.isEmpty(SecureUtil.getCountryIds()) ? areaCountryIds : SecureUtil.getCountryIds();
        if (statisticsFlag) {
            return studentCountService.getStudentItemTotalSum(companyIds, areaCountryIds, year, SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(), staffFollowerIds, SecureUtil.getStaffInfo().getIsStudentAdmin());
        } else {
            return studentCountService.getStudentTotalSum(companyIds, areaCountryIds, year, SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(), staffFollowerIds, SecureUtil.getStaffInfo().getIsStudentAdmin());
        }
    }


    /**
     * 系统首页数据-获取总学生申请数
     *
     * @Date 11:32 2022/8/2
     * <AUTHOR>
     */
//    @Override
//    public Long getStudentItemTotalSum(List<Long> companyIds, List<Long> countryIds, String year) {
//        return studentCountService.getStudentItemTotalSum(companyIds, countryIds, year);
//    }
    @Override
    public List<StudentCountVo> getStudentCountRecords(List<Long> companyIds, String num, String year) {
        return studentCountService.getStudentCountRecord(companyIds, SecureUtil.getCountryIds(), year);
    }

    /**
     * @param companyIds
     * @param areaCountryIds
     * @param num1
     * @param year
     * @param statisticsFlag true:统计申请计划数  false:统计学生数
     * @return
     */
    @Override
    public List<WorldHistogramVo> getCountryStudentCount(List<Long> companyIds, List<Long> areaCountryIds, String num1, String year, Boolean statisticsFlag) {
        List<WorldHistogramVo> worldHistogramVos = new ArrayList<>();
        if (GeneralTool.isEmpty(SecureUtil.getStaffInfo())) {
//            worldHistogramVos.addAll(studentCountService.getAllWorldHistogram(companyIds, year));
            worldHistogramVos.addAll(studentCountService.getWorldHistogram(companyIds, areaCountryIds, year, statisticsFlag));
        } else {
            worldHistogramVos.addAll(studentCountService.getWorldHistogram(companyIds, SecureUtil.getCountryIds(), year, statisticsFlag));
        }
        return worldHistogramVos;
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentVo>
     * @Description: 获取国家学生数
     * @Param [statisticsFlag true:统计申请计划数  false:统计学生数 ]
     * <AUTHOR>
     */
    @Override
    public List<WorldHistogramVo> getStateStudentCount(List<Long> companyIds, List<Long> areaCountryIds, String num, String year, Boolean statisticsFlag) {
        Long staffId = SecureUtil.getStaffId();

        List<Long> staffFollowerIds = new ArrayList<>();
        //员工id + 业务下属员工ids
        if (GeneralTool.isNotEmpty(permissionCenterClient.getStaffFollowerIds(staffId).getData())) {
            staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
        staffFollowerIds.add(staffId);

        List<WorldHistogramVo> list = new ArrayList<>();
        List<Long> areaCountryIds_ = GeneralTool.isEmpty(SecureUtil.getCountryIds()) ? areaCountryIds : SecureUtil.getCountryIds();
        if ("TW".equals(num)) {
            List<Long> stateIds = getStateIds(num);
            Long stateId = stateIds.get(0);
            List<Long> cityIds = getCityIds(stateId);
            list.addAll(studentCountService.getCityStudentNum(companyIds, areaCountryIds_, cityIds, year, statisticsFlag, SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(), staffFollowerIds));
        } else {
            List<Long> stateIds = getStateIds(num);
            list.addAll(studentCountService.getStateStudentNum(companyIds, areaCountryIds_, stateIds, year, statisticsFlag, SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(), staffFollowerIds));
        }
        return list;
    }

    /**
     * @param companyIds
     * @param areaCountryIds
     * @param num
     * @param year
     * @param statisticsFlag true:统计申请计划数  false:统计学生数
     * @return
     */
    @Override
    public List<WorldMapVo> getWorldMap(List<Long> companyIds, List<Long> areaCountryIds, String num, String year, Boolean statisticsFlag) {
        List<Long> areaCountryIds_ = GeneralTool.isEmpty(SecureUtil.getCountryIds()) ? areaCountryIds : SecureUtil.getCountryIds();
        return studentCountService.getWorldMapDtos(companyIds, areaCountryIds_, year, statisticsFlag);
    }

    @Override
    public SystemPageLonlatVo getLonlat(List<Long> companyIds, String num, String year) {
        SystemPageLonlatVo systemPageLonlatVo = new SystemPageLonlatVo();
        systemPageLonlatVo.setNationallonlat(JSONArray.fromObject(BaseConstant.NATIONAL_LONGITUDE_LATITUDE));
        Map<String, JSONArray> map = new HashMap<>();
        map.put("中国", JSONArray.fromObject(BaseConstant.PROVINCES_LONGITUDE_LATITUDE));
        map.put("越南", JSONArray.fromObject(BaseConstant.PROVINCES_LONGITUDE_LATITUDE_VN));
        map.put("马来西亚", JSONArray.fromObject(BaseConstant.PROVINCES_LONGITUDE_LATITUDE_MY));
        map.put("印尼", JSONArray.fromObject(BaseConstant.PROVINCES_LONGITUDE_LATITUDE_INA));
        map.put("台湾（中国）", JSONArray.fromObject(BaseConstant.PROVINCES_LONGITUDE_LATITUDE_TW));
        systemPageLonlatVo.setProvinceslonlat(JSONObject.fromObject(map));
        return systemPageLonlatVo;
    }

    /**
     * 学生申请状态统计
     *
     * @param companyIds
     * @param num
     * @param year
     * @return
     */
    @Override
    public List<StudentApplicationStatusCountVo> getStudentApplicationStatusCount(List<Long> companyIds, String num, String year) {
        Long staffId = SecureUtil.getStaffId();
        List<Long> staffFollowerIds = new ArrayList<>();
        //员工id + 业务下属员工ids
        if (GeneralTool.isNotEmpty(permissionCenterClient.getStaffFollowerIds(staffId).getData())) {
            staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
        staffFollowerIds.add(staffId);

        List<StudentApplicationStatusCountVo> resultList = new ArrayList<>();
        //检查用户权限
        Map<String, Object> statusCount = studentOfferItemMapper.getStudentApplicationStatusCount(companyIds, year, staffFollowerIds,
                SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(), SecureUtil.getStaffInfoByStaffId(staffId).getIsStudentAdmin(),
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds());

        // 2. 定义步骤ID与统计字段名的映射关系
        Map<Long, String> stepIdToFieldMap = new HashMap<>();
        stepIdToFieldMap.put(1L, "sumNew");
        stepIdToFieldMap.put(2L, "sumSubmitted");
        stepIdToFieldMap.put(4L, "sumAdmitted");
        stepIdToFieldMap.put(5L, "sumDeposited");
        stepIdToFieldMap.put(6L, "sumCas");
        stepIdToFieldMap.put(8L, "sumEnrolled");

        if (GeneralTool.isNotEmpty(statusCount)){
            studentOfferItemStepMapper.getItemStepMapAndId().forEach(itemStepMap -> {
                Long stepId = (Long) itemStepMap.get("id");
                String fullStepName = (String) itemStepMap.get("stepName");

                // 4. 提取中英文名称（示例："入学登记完成（Enrolled）"）
                String[] zhEnParts = fullStepName.split("\\（|\\）");
                String stepNameZh = zhEnParts[0];
                String stepName = zhEnParts.length > 1 ? zhEnParts[1] : "Undefined";

                // 5. 根据步骤ID获取对应的统计值
                String countField = stepIdToFieldMap.get(stepId);
                if (countField != null) {
                    Integer count = ((Number) statusCount.getOrDefault(countField, 0)).intValue();

                    // 6. 构建VO对象
                    StudentApplicationStatusCountVo vo = new StudentApplicationStatusCountVo();
                    vo.setStepName(stepName);
                    vo.setStepNameZh(stepNameZh);
                    vo.setNumberOfStudentApplicationStatuses(count);

                    resultList.add(vo);
                }

            });
        }


        return resultList;

    }

    @Override
    public List<AssistantVo> getStudentStatisticsCount(List<Long> companyIds, String num, String year) {
        Long staffId = SecureUtil.getStaffId();
        List<Long> staffFollowerIds = new ArrayList<>();
        if (GeneralTool.isNotEmpty(permissionCenterClient.getStaffFollowerIds(staffId).getData())) {
            staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData()
                    .stream()
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
        }
        staffFollowerIds.add(staffId);

        boolean isFinancialHiding = SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding();
        boolean isStudentAdmin = SecureUtil.getStaffInfo().getIsStudentAdmin();

        List<AssistantVo> resultList = new ArrayList<>();

        // 1. 处理业务国家线层级
        List<StudentStatisticsVo> businessCountryGroups = studentOfferItemMapper.getBusinessCountryGroupStudentCount(
                companyIds, year, staffFollowerIds, isFinancialHiding, isStudentAdmin,
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds()
        );

        List<Long> businessCountryGroupIds = businessCountryGroups.stream().map(StudentStatisticsVo::getBusinessCountryGroupId).collect(Collectors.toList());
        Integer totalBusinessStudents = businessCountryGroups.stream()
                .mapToInt(StudentStatisticsVo::getStudentCount)
                .sum();
        //1. 拼接大区学生数据
        Map<Long, AssistantVo> businessCountryMap = new HashMap<>();
        businessCountryGroups.forEach(region -> {
            AssistantVo vo = new AssistantVo();
            vo.setName(region.getGroupName());
            vo.setNameZh(region.getGroupNameChn());
            vo.setStudentCount(region.getStudentCount());
            //增加比率
            if (totalBusinessStudents > 0) {
                BigDecimal ratio = BigDecimal.valueOf((region.getStudentCount() * 100.0) / totalBusinessStudents);
                vo.setRatio(ratio.setScale(2, RoundingMode.HALF_UP));
            } else {
                vo.setRatio(BigDecimal.valueOf(0));
            }
            businessCountryMap.put(region.getBusinessCountryGroupId(), vo);
            resultList.add(vo);
        });
        List<CountryStatisticsVo> countries = studentOfferItemMapper.getCountryStudentCount(
                companyIds, businessCountryGroupIds, year, staffFollowerIds,
                isFinancialHiding, isStudentAdmin,
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds()
        );

        List<Long> countryIds = countries.stream().map(CountryStatisticsVo::getAreaCountryId).collect(Collectors.toList());
        Integer totalContryStudents = countries.stream()
                .mapToInt(CountryStatisticsVo::getStudentCount)
                .sum();
        Map<String, AssistantVo> countryMap = new HashMap<>();
        countries.forEach(country -> {
            Long businessCountryGroupId = country.getBusinessCountryGroupId();
            Long areaCountryId = country.getAreaCountryId();
            String key = businessCountryGroupId + "_" + areaCountryId;

            // 创建国家节点
            AssistantVo countryVo = new AssistantVo();
            countryVo.setName(country.getAreaCountryName());
            countryVo.setNameZh(country.getAreaCountryNameChn());
            //比率
            if (totalBusinessStudents > 0) {
                BigDecimal ratio = BigDecimal.valueOf((country.getStudentCount() * 100.0) / totalContryStudents);
                countryVo.setRatio(ratio.setScale(2, RoundingMode.HALF_UP));
            } else {
                countryVo.setRatio(BigDecimal.valueOf(0));
            }
            countryVo.setStudentCount(country.getStudentCount());

            // 挂载到大区节点
            AssistantVo businessCountryGroupVo = businessCountryMap.get(businessCountryGroupId);
            if (businessCountryGroupVo != null) {
                businessCountryGroupVo.getChildren().add(countryVo);
                countryMap.put(key, countryVo);
            }
        });
        List<SchoolStudentCountVo> schools = studentOfferItemMapper.getSchoolStudentCount(
                companyIds,businessCountryGroupIds, countryIds,
                year, staffFollowerIds, isFinancialHiding, isStudentAdmin,
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds()
        );
//        Integer totalSchoolStudents = schools.stream()
//                .mapToInt(SchoolStudentCountVo::getStudentCount)
//                .sum();
        // 按国家分组学校，并处理每个国家的学校
        schools.stream()
                .collect(Collectors.groupingBy(
                        school -> school.getBusinessCountryGroupId() + "_" + school.getAreaCountryId()
                ))
                .forEach((countryKey, schoolList) -> {
                    // 获取对应的国家节点
                    AssistantVo countryVo = countryMap.get(countryKey);
                    if (countryVo != null) {
                        // 计算当前国家的总学生数（用于比率计算）
                        int totalStudentsInCountry = schoolList.stream()
                                .mapToInt(SchoolStudentCountVo::getStudentCount)
                                .sum();

                        // 按学生数降序排序，取前100名
                        List<SchoolStudentCountVo> top100Schools = schoolList.stream()
                                .sorted((s1, s2) -> Integer.compare(s2.getStudentCount(), s1.getStudentCount()))
                                .limit(100)
                                .collect(Collectors.toList());

                        // 添加前100学校到国家节点
                        top100Schools.forEach(school -> {
                            AssistantVo schoolVo = new AssistantVo();
                            schoolVo.setName(school.getInstitutionName());
                            schoolVo.setNameZh(school.getInstitutionNameChn());
                            schoolVo.setStudentCount(school.getStudentCount());

                            // 计算比率：当前学校学生数 / 所属国家总学生数
                            if (totalStudentsInCountry > 0) {
                                BigDecimal ratio = BigDecimal.valueOf(
                                        (school.getStudentCount() * 100.0) / totalStudentsInCountry
                                ).setScale(2, RoundingMode.HALF_UP);
                                schoolVo.setRatio(ratio);
                            } else {
                                schoolVo.setRatio(BigDecimal.ZERO);
                            }

                            countryVo.getChildren().add(schoolVo);
                        });
                    }
                });
        return resultList;
//        for (StudentStatisticsVo groupVo : businessCountryGroups) {
//            // 2. 转换业务国家线对象
//            AssistantVo groupAssistantVo = new AssistantVo();
//            groupAssistantVo.setName(groupVo.getGroupName());
//            groupAssistantVo.setNameZh(groupVo.getGroupNameChn());
//            groupAssistantVo.setStudentCount(groupVo.getStudentCount());
//
//            // 3. 处理国家层级
//            List<CountryStatisticsVo> countries = studentOfferItemMapper.getCountryStudentCount(
//                    companyIds, groupVo.getBusinessCountryGroupId(), year, staffFollowerIds,
//                    isFinancialHiding, isStudentAdmin
//            );
//
//            List<AssistantVo> countryChildren = new ArrayList<>();
//            for (CountryStatisticsVo countryVo : countries) {
//                // 4. 转换国家对象
//                AssistantVo countryAssistantVo = new AssistantVo();
//                countryAssistantVo.setName(countryVo.getAreaCountryName());
//                countryAssistantVo.setNameZh(countryVo.getAreaCountryNameChn());
//                countryAssistantVo.setStudentCount(countryVo.getStudentCount());
//
//                // 5. 处理学校层级
//                List<SchoolStudentCountVo> schools = studentOfferItemMapper.getSchoolStudentCount(
//                        companyIds, groupVo.getBusinessCountryGroupId(), countryVo.getAreaCountryId(),
//                        year, staffFollowerIds, isFinancialHiding, isStudentAdmin
//                );
//
//                List<AssistantVo> schoolChildren = schools.stream()
//                        .map(school -> {
//                            // 6. 转换学校对象
//                            AssistantVo schoolAssistantVo = new AssistantVo();
//                            schoolAssistantVo.setName(school.getInstitutionName());
//                            schoolAssistantVo.setNameZh(school.getInstitutionNameChn());
//                            schoolAssistantVo.setStudentCount(school.getStudentCount());
//                            return schoolAssistantVo;
//                        })
//                        .collect(Collectors.toList());
//
//                // 7. 返回学校到国家
//                countryAssistantVo.getChildren().addAll(schoolChildren);
//                countryChildren.add(countryAssistantVo);
//            }
//
//            // 8. 返回国家到业务国家线
//            groupAssistantVo.getChildren().addAll(countryChildren);
//            resultList.add(groupAssistantVo);
//        }


    }


    @Override
    public List<AssistantVo> getStudentStatistics(List<Long> companyIds, String num, String year) {
        Long staffId = SecureUtil.getStaffId();
        List<Long> staffFollowerIds = new ArrayList<>();
        if (GeneralTool.isNotEmpty(permissionCenterClient.getStaffFollowerIds(staffId).getData())) {
            staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData()
                    .stream()
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
        }
        staffFollowerIds.add(staffId);

        boolean isStudentOfferItemFinancialHiding = SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding();
        boolean isStudentAdmin = SecureUtil.getStaffInfo().getIsStudentAdmin();

        List<AssistantVo> resultList = new ArrayList<>();
        //大区学生数据
        List<AreaRegionStatisticsVo> areaRegionStatisticsVos = studentOfferItemMapper.getAreaRegionStudentCount(
                companyIds, year, staffFollowerIds, isStudentOfferItemFinancialHiding, isStudentAdmin,
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds()
        );
        //        1. 拼接大区学生数据
        Map<Long, AssistantVo> areaRegionMap = new HashMap<>();
        areaRegionStatisticsVos.forEach(region -> {
            AssistantVo vo = new AssistantVo();
            vo.setName(region.getAreaRegionName());
            vo.setNameZh(region.getAreaRegionNameChn());
            vo.setStudentCount(region.getStudentCount());
            areaRegionMap.put(region.getAreaRegionId(), vo);
            resultList.add(vo);
        });

        List<Long> areaRegionIds = areaRegionStatisticsVos.stream().map(AreaRegionStatisticsVo::getAreaRegionId).collect(Collectors.toList());
        //bd学生数据
        List<BdStaffStatisticsVo> bdStaffStatisticsItems = studentOfferItemMapper.getBdStaffStudentCount(
                companyIds, areaRegionIds, year, staffFollowerIds, isStudentOfferItemFinancialHiding, isStudentAdmin,
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds()
        );
//        2. 拼接BD学生数据
//       将 bdStaffStatisticsItems.getAgentId()放入相应的大区中areaRegionStatisticsVos的children中
        Map<String, AssistantVo> bdStaffMap = new HashMap<>();
        bdStaffStatisticsItems.forEach(bd -> {
            Long regionId = bd.getAreaRegionId();
            Long bdStaffId = bd.getBdStaffId();
            String key = regionId + "_" + bdStaffId;

            // 创建BD节点
            AssistantVo countryVo = new AssistantVo();
            countryVo.setName(bd.getBdStaffNameEn());
            countryVo.setNameZh(bd.getBdStaffName());
            countryVo.setStudentCount(bd.getStudentCount());

            // 挂载到大区节点
            AssistantVo regionVo = areaRegionMap.get(regionId);
            if (regionVo != null && !bdStaffMap.containsKey(key)) {
                regionVo.getChildren().add(countryVo);
                bdStaffMap.put(key, countryVo);
            }
        });


        List<Long> bdStaffIds =bdStaffStatisticsItems.stream().map(BdStaffStatisticsVo::getBdStaffId).collect(Collectors.toList());
        //代理学生数据
        List<AgentStaristisVo> agentStaristisVos = studentOfferItemMapper.getAgentStudentCount(
                companyIds, areaRegionIds, bdStaffIds, year, staffFollowerIds, isStudentOfferItemFinancialHiding, isStudentAdmin,
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds()
        );

        agentStaristisVos.forEach(agent -> {
            Long regionId = agent.getAreaRegionId();
            Long bdStaffId = agent.getBdStaffId();
            String key = regionId + "_" + bdStaffId;

            // 找到对应的BD员工节点
            AssistantVo bdVo = bdStaffMap.get(key);
            if (bdVo != null) {
                // 3. 拼接代理学生数据
                // 将agentStaristisVos的数据根据大区id和staffID放入对应的位置
                // 当代理学生数小于5个，将该条信息放入other中,其他数据正常放入相应的children中
                AssistantVo agentVo = new AssistantVo();
                agentVo.setName(agent.getAgentNameNote());
                agentVo.setNameZh(agent.getAgentName());
                agentVo.setStudentCount(agent.getStudentCount());

                // 判断是否放入Other节点
                if (agent.getStudentCount() < 5) {
                    // 查找或创建 "Other" 节点
                    Optional<AssistantVo> otherOpt = bdVo.getOthers().stream()
                            .filter(v -> "Other".equals(v.getName()))
                            .findFirst();
                    AssistantVo otherVo = otherOpt.orElseGet(() -> {
                        AssistantVo vo = new AssistantVo();
                        vo.setName("Other");
                        vo.setNameZh("其他");
                        vo.setStudentCount(0);
                        bdVo.getOthers().add(vo);  // 添加到 others 列表
                        return vo;
                    });
                    // 累加小代理的学生数到 "Other" 节点
                    otherVo.setStudentCount(otherVo.getStudentCount() + agent.getStudentCount());
                } else {
                    // 直接添加到BD节点的children
                    bdVo.getChildren().add(agentVo);
                }

                // 更新BD节点的总学生数
                bdVo.setStudentCount(bdVo.getStudentCount() + agent.getStudentCount());
            }
        });

        // 4. 更新大区节点的总学生数（确保包含所有子节点）
        areaRegionMap.values().forEach(region -> {
            int total = region.getChildren().stream()
                    .mapToInt(AssistantVo::getStudentCount)
                    .sum();
            region.setStudentCount(total);
        });

        return resultList;

    }

    @Override
    public List<AreaRegionStatisticsVo> getRegionStudentCount(List<Long> companyIds, String num, String year) {
        Long staffId = SecureUtil.getStaffId();
        List<Long> staffFollowerIds = new ArrayList<>();
        if (GeneralTool.isNotEmpty(permissionCenterClient.getStaffFollowerIds(staffId).getData())) {
            staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData()
                    .stream()
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
        }
        staffFollowerIds.add(staffId);

        boolean isStudentOfferItemFinancialHiding = SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding();
        boolean isStudentAdmin = SecureUtil.getStaffInfo().getIsStudentAdmin();
        //大区学生数据
        List<AreaRegionStatisticsVo> areaRegionStatisticsVos = studentOfferItemMapper.getAreaRegionStudentCount(
                companyIds, year, staffFollowerIds, isStudentOfferItemFinancialHiding, isStudentAdmin,
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds()
        );
        return areaRegionStatisticsVos;
    }


    /**
     * 获取COE月度统计数据
     *
     * @param companyIds
     * @param num
     * @param year
     * @return
     */
    @Override
    public COEMonthlyStatisticsVo getCOEMonthlyStatistics(List<Long> companyIds, String num, String year) {
        Long staffId = SecureUtil.getStaffId();
        List<Long> staffFollowerIds = new ArrayList<>();
        //员工id + 业务下属员工ids
        if (GeneralTool.isNotEmpty(permissionCenterClient.getStaffFollowerIds(staffId).getData())) {
            staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
        staffFollowerIds.add(staffId);
        List<COEParameterVo> coeMonthlyStatistics = studentOfferItemMapper.getCOEMonthlyStatistics(companyIds, year, staffFollowerIds,
                SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(), SecureUtil.getStaffInfoByStaffId(staffId).getIsStudentAdmin(),
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds());

        COEMonthlyStatisticsVo coeMonthlyStatisticsVo = new COEMonthlyStatisticsVo();
        //获取年份月列表 （去掉相同年月数据）
        List<String> yearMonths = coeMonthlyStatistics.stream()
                .map(COEParameterVo::getYearMonth)
                .distinct()
                .sorted()
                .collect(Collectors.toList());

        coeMonthlyStatisticsVo.setYearMonth(yearMonths);
        //获取国家组名称
        Map<String, List<COEParameterVo>> groupMap = coeMonthlyStatistics.stream()
                .collect(Collectors.groupingBy(COEParameterVo::getCountryGroupName));


        List<COEMonthlyStatisticsVo.SeriesItem> seriesItems = new ArrayList<>();
        //根据年份月列表和国家组别名称，将学生列表按顺序赋值到data中

        groupMap.forEach((groupName, vos) -> {
            COEMonthlyStatisticsVo.SeriesItem item = new COEMonthlyStatisticsVo.SeriesItem();
            item.setCountryGroupName(groupName);
            item.setCountryGroupNameZh(vos.get(0).getCountryGroupNameChn());

            List<Integer> data = new ArrayList<>(Collections.nCopies(yearMonths.size(), 0));
            for (COEParameterVo vo : vos) {
                String yearMonthStr = vo.getYearMonth().toString();
                int index = yearMonths.indexOf(yearMonthStr);
                if (index != -1) {
                    data.set(index, vo.getStudentCount());
                }
            }

            item.setData(data);
            seriesItems.add(item);
            coeMonthlyStatisticsVo.setSeries(seriesItems);
        });
        return coeMonthlyStatisticsVo;
    }

    @Override
    public Map<String, List<FinanceStatisticVO>> getFinanceStatistic(List<Long> companyIds, String num, String year, String currencyTypeNumTo) {
        Long staffId = SecureUtil.getStaffId();
        List<Long> staffFollowerIds = new ArrayList<>();
        //员工id + 业务下属员工ids
//        if (GeneralTool.isNotEmpty(permissionCenterClient.getStaffFollowerIds(staffId).getData())) {
//            staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
//        }
        List<Long> getPermssionData = permissionCenterClient.getStaffFollowerIds(staffId).getData();
        if ( getPermssionData!= null && !getPermssionData.isEmpty()) {
            staffFollowerIds = getPermssionData.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
        staffFollowerIds.add(staffId);
        //获取所有统计数据
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        List<FinanceStatisticVO>  FinanceStatisticVOs= studentOfferItemMapper.getFinanceStatistic(companyIds, year,
                staffFollowerIds,SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(),SecureUtil.getStaffInfoByStaffId(staffId).getIsStudentAdmin(),
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds());
        // 记录结束时间
        long endTime = System.currentTimeMillis();
        // 计算并打印执行时间（以毫秒为单位）
        long duration = endTime - startTime;
        System.out.println("Execution time: " + duration + " ms");

        //根据季度和类型分组
        Map<String, Map<String, List<FinanceStatisticVO>>> collect = FinanceStatisticVOs.stream()
                .collect(Collectors.groupingBy(
                        FinanceStatisticVO::getYearQuarter,
                        Collectors.groupingBy(
                                FinanceStatisticVO::getType
                        )));



        Map<String, List<FinanceStatisticVO>> result = new TreeMap<>();
        //日期
        for (Map.Entry<String, Map<String, List<FinanceStatisticVO>>> longMapEntry : collect.entrySet()) {

                    String yearMonth = longMapEntry.getKey();
                    List<FinanceStatisticVO> list = new ArrayList<>();
                    result.put(yearMonth,list);
                    Map<String, List<FinanceStatisticVO>> value = longMapEntry.getValue();
                    //类型
                    for (Map.Entry<String, List<FinanceStatisticVO>> stringListEntry : value.entrySet()) {
                        String name = stringListEntry.getKey();
                        BigDecimal sumAmount = BigDecimal.ZERO;
                        List<FinanceStatisticVO> value1 = stringListEntry.getValue();
                        //根据币种计算金额
                        for (FinanceStatisticVO financeStatisticVO : value1) {
                            String currencyTypeNum1 = financeStatisticVO.getCurrencyTypeNum();
                            BigDecimal amount = financeStatisticVO.getAmount();
                            BigDecimal aDouble = this.ConvertAmount(currencyTypeNum1,currencyTypeNumTo ,amount);
                            sumAmount=sumAmount.add(aDouble);
                        }
                        FinanceStatisticVO vo = new FinanceStatisticVO();
                        // 将sumAmount除以10000，并设置结果保留两位小数，采用HALF_UP舍入模式
                        sumAmount= sumAmount.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP);
                        vo.setType(name);
                        vo.setAmount(sumAmount);
                        list.add(vo);
                }


        }


        return result;


    }


    // 计算季度的方法保持不变
    private String calculateYearQuarter(String yearMonth) {
        int month = Integer.parseInt(yearMonth.substring(4));
        int quarter = (month - 1) / 3 + 1; // 计算季度
        return yearMonth.substring(0, 4) + "Q" + quarter; // 返回格式如"2024Q1"
    }


    /**
     * 根据币种动态计算金额
     * @param currencyTypeNumFrom
     * @param currencyTypeNumTo
     * @param amount
     * @return
     */
    private BigDecimal ConvertAmount(String currencyTypeNumFrom,String currencyTypeNumTo,BigDecimal amount){
        //判断是否有指定转换币种，没有默认指定港币
        if(GeneralTool.isEmpty(currencyTypeNumTo)){
            currencyTypeNumTo = "HKD";
        }

        // 尝试从缓存中获取汇率
        Optional<BigDecimal> optionalExchangeRate = Optional.ofNullable(exchangeRateCache.get(currencyTypeNumFrom + "_" + currencyTypeNumTo));
        BigDecimal money = BigDecimal.ZERO;
        String finalCurrencyTypeNumTo = currencyTypeNumTo;
        BigDecimal exchangeRate = optionalExchangeRate.orElseGet(() -> {
            // 如果缓存中没有找到汇率，则通过API获取并缓存
            Result<BigDecimal> result = financeCenterClient.getLastExchangeRate(false, currencyTypeNumFrom, finalCurrencyTypeNumTo);
            BigDecimal rate = result.getData();
//            if (rate == null) {
//                // 使用字符串拼接输出错误信息
//                System.out.println("无法从API获取汇率: from " + currencyTypeNumFrom + " to " + finalCurrencyTypeNumTo);
//                throw new RuntimeException("获取汇率失败");
//            }
            exchangeRateCache.put(currencyTypeNumFrom + "_" + finalCurrencyTypeNumTo, rate); // 缓存汇率
            return rate;
        });
            if(GeneralTool.isEmpty(exchangeRate)){
                money = amount;
            }else {
                money = amount.multiply(exchangeRate);
            }

        //获取汇率后进行转换计算
        return money;

    }


    /**
     * 获取cityIds
     *
     * @param stateId
     * @return
     */
    private List<Long> getCityIds(Long stateId) {
//        ListResponseBo listResponseBo = institutionCenterClient.getByFkAreaStateId(stateId);
        Result<List<AreaCityVo>> result = institutionCenterClient.getByFkAreaStateId(stateId);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            Object data = result.getData();
            cn.hutool.json.JSONArray objects = JSONUtil.parseArray(data);
            List<AreaCityVo> areaCityVos = JSONUtil.toList(objects, AreaCityVo.class);
            return areaCityVos.stream().map(AreaCityVo::getId).collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 获取stateIds
     *
     * @return
     */
    private List<Long> getStateIds(String num) {
        List<String> numList = new ArrayList<>();
        numList.add(num);
        Result<List<Long>> result = institutionCenterClient.getCountryIdByKey(numList);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            Result<List<AreaStateVo>> result_ = institutionCenterClient.getByFkAreaCountryId(result.getData().get(0));
            if (result_.isSuccess() && GeneralTool.isNotEmpty(result_.getData())) {
                cn.hutool.json.JSONArray objects = JSONUtil.parseArray(result_.getData());
                List<AreaStateVo> datas = JSONUtil.toList(objects, AreaStateVo.class);
                return datas.stream().map(AreaStateVo::getId).collect(Collectors.toList());
//                return result_.getData().stream().map(AreaCityVo::getFkAreaStateId).collect(Collectors.toList());
            }
        }
        return null;
    }


    @Override
    public void pushDatas() {
        //所有用户建立的连接
        ConcurrentHashMap<Long, Channel> userChannelMap = NettyConfig.getUserChannelMap();
        //搜索条件
        ConcurrentHashMap<Long, SystemPageVo> userSearchMap = NettyConfig.getUserSearchMap();
        if (GeneralTool.isNotEmpty(userChannelMap)) {
            log.info("userChannelMap参数--------------------" + com.alibaba.fastjson.JSONObject.toJSONString(userChannelMap));
        }
        if (GeneralTool.isNotEmpty(userSearchMap)) {
            log.info("userChannelMap参数--------------------" + com.alibaba.fastjson.JSONObject.toJSONString(userSearchMap));
        }

//        ConcurrentHashMap<Long, SystemPageVo> userSearchMap = new ConcurrentHashMap<>();
//        SystemPageVo systemPageVo = new SystemPageVo();
//        systemPageVo.setAction(2);
//        systemPageVo.setId(1L);
//        List<Long> objects = new ArrayList<>();objects.add(1L);
//        systemPageVo.setFkCompanyIds(objects);
//        List<Long> list = new ArrayList<>();list.add(3L);list.add(4L);
//        systemPageVo.setAreaCountryIds(list);
//        userSearchMap.put(1L,systemPageVo);

        Map areaCountryMap = getSameMap(userSearchMap, "areaCountryIds");
        Map numMap = getSameMap(userSearchMap, "num");
        ConcurrentHashMap<String, List<StudentCountVo>> pushFlagMap = NettyPushConfig.getPushFlagMap();
        List<StudentCountVo> studentCountVos = pushFlagMap.get(ProjectKeyEnum.PUSH_FLAG.key);
        pushFlagMap.put(ProjectKeyEnum.PUSH_FLAG.key, new ArrayList<StudentCountVo>());
        String num = "CHN";
        if (numMap.keySet().toArray().length > 0) {
            Object[] str = numMap.keySet().toArray();
            String countryNum = str[0].toString();
            String[] split = countryNum.split(":");
            String s = split[1];
            if (GeneralTool.isNotEmpty(countryNum) && !"null".equals(s)) {
                num = s;
            }
        }
        if (GeneralTool.isNotEmpty(studentCountVos)) {
            String finalNum = num;
            areaCountryMap.forEach((key, value) ->
            {

                String[] split = key.toString().split(":");
                //公司id
                List<Long> companyIds = getSelectLong(split[0]);
                //获取国家选项
                List<Long> areaCountryIds = getSelectLong(split[1]);

                List<Long> userIds = getSelectLong(value.toString());
                List<StudentCountVo> dtos = new ArrayList<>();
                for (StudentCountVo dto : studentCountVos) {
                    if (companyIds.contains(dto.getCompanyId())) {
                        dtos.add(dto);
                    }
                }

                Calendar cal = Calendar.getInstance();
                String year = String.valueOf(cal.get(Calendar.YEAR)); //默认为当前日期所在的年份

                //构造推送的对象 学生数
                SystemPageVo1 systemPageVo1 = new SystemPageVo1();
                //构造推送的对象 学习计划数
                SystemPageVo1 systemPageItemDto = new SystemPageVo1();

                //返回系统首页数据-国家学生数
                List<WorldHistogramVo> worldHistogramDtos_1 = this.getCountryStudentCount(companyIds, areaCountryIds, finalNum, year, false);
                if (GeneralTool.isNotEmpty(worldHistogramDtos_1)) {
                    systemPageVo1.setCountryStudentCount(worldHistogramDtos_1);
                }
                //返回系统首页数据-国家学生申请数
                worldHistogramDtos_1 = this.getCountryStudentCount(companyIds, areaCountryIds, finalNum, year, true);
                if (GeneralTool.isNotEmpty(worldHistogramDtos_1)) {
                    systemPageItemDto.setCountryStudentCount(worldHistogramDtos_1);
                }
                //返回系统首页数据-州省学生数
                List<WorldHistogramVo> worldHistogramDtos_2 = this.getStateStudentCount(companyIds, areaCountryIds, finalNum, year, false);
                if (GeneralTool.isNotEmpty(worldHistogramDtos_2)) {
                    systemPageVo1.setStateStudentCount(worldHistogramDtos_2);
                }
                //返回系统首页数据-州省学生申请数
                worldHistogramDtos_2 = this.getStateStudentCount(companyIds, areaCountryIds, finalNum, year, true);
                if (GeneralTool.isNotEmpty(worldHistogramDtos_2)) {
                    systemPageItemDto.setStateStudentCount(worldHistogramDtos_2);
                }

                //返回系统首页初始数据-学生申请记录
                systemPageVo1.setStudentCountRecords(dtos);//直接返回当前学生申请记录（前端会在列表开头累加）
                systemPageItemDto.setStudentCountRecords(dtos);//直接返回当前学生申请记录（前端会在列表开头累加）
//                List<StudentCountVo> studentCountRecords = this.getStudentCountRecords(companyIds, finalNum, year);//学生列表
//                if(GeneralTool.isNotEmpty(studentCountRecords))
//                {
//                    systemPageVo1.setStudentCountRecords(studentCountRecords);
//                }
                //返回系统首页数据-世界地图 学生数
                List<WorldMapVo> worldMapVos = this.getWorldMap(companyIds, areaCountryIds, finalNum, year, false);//世界地图
                if (GeneralTool.isNotEmpty(worldMapVos)) {
                    systemPageVo1.setWorldMapDtos(worldMapVos);
                }
                //返回系统首页数据-世界地图 申请数数
                worldMapVos = this.getWorldMap(companyIds, areaCountryIds, finalNum, year, true);//世界地图
                if (GeneralTool.isNotEmpty(worldMapVos)) {
                    systemPageItemDto.setWorldMapDtos(worldMapVos);
                }
                //返回系统首页数据-获取总学生数
                Long studentTotalSum = this.getStudentTotalSum(companyIds, areaCountryIds, finalNum, year, false);
                systemPageVo1.setStudentTotalSum(studentTotalSum);
                //返回系统首页数据-获取总学生申请数
                studentTotalSum = this.getStudentTotalSum(companyIds, areaCountryIds, finalNum, year, true);
                systemPageItemDto.setStudentTotalSum(studentTotalSum);

                SystemPageLonlatVo systemPageLonlatVo = this.getLonlat(companyIds, finalNum, year);
                if (GeneralTool.isNotEmpty(systemPageLonlatVo)) {
                    systemPageVo1.setNationallonlat(systemPageLonlatVo.getNationallonlat());
                    systemPageVo1.setProvinceslonlat(systemPageLonlatVo.getProvinceslonlat());
                    systemPageItemDto.setNationallonlat(systemPageLonlatVo.getNationallonlat());
                    systemPageItemDto.setProvinceslonlat(systemPageLonlatVo.getProvinceslonlat());
                }

//                //todo 先写死，后面改造
//                systemPageVo1.setStudentTotalSum(studentCountService.getStudentTotalSum(companyIds, countryIds, year));
//
//                systemPageVo1.setStudentCountRecords(dtos);

//                List<WorldHistogramVo> stateStudentNums = null;
//                try {
//                    List<Long> stateIds = getStateIds(finalNum);
//                    stateStudentNums = studentCountService.getStateStudentNum(companyIds, SecureUtil.getCountryIds(), stateIds, year);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//                List<WorldMapVo> worldMapVos = studentCountService.getWorldMapDtos(companyIds, SecureUtil.getCountryIds(), "2022");
//                for (WorldMapVo worldMapDto : worldMapVos) {
//                    worldMapDto.setShow(false);
//                    for (StudentCountVo scd : dtos) {
//                        if (worldMapDto.getStartCountry().equals(scd.getStudentCountryId()) && worldMapDto.getEndCountry().equals(scd.getFkAreaCountryId())) {
//                            worldMapDto.setShow(true);
//                        }
//                    }
//                }
//                systemPageVo1.setStateStudentCount(stateStudentNums);
//                systemPageVo1.setWorldMapDtos(worldMapVos);
//                List<WorldHistogramVo> worldHistograms = studentCountService.getWorldHistogram(companyIds, areaCountryIds, "2022");
//                systemPageVo1.setCountryStudentCount(worldHistograms);
                systemPageVo1.setCommand(Command.MESSAGE_RES);
                systemPageItemDto.setCommand(Command.MESSAGE_RES);
                String resultWorlds = JSON.toJSONStringWithDateFormat(systemPageVo1, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);
                String resultItemWorlds = JSON.toJSONStringWithDateFormat(systemPageItemDto, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);
                for (Long staffId : userIds) {
                    Channel channel = userChannelMap.get(staffId);
                    if (!Objects.isNull(channel)) {
                        if (userSearchMap.get(staffId).getIsStudentNumFlag()) {
                            log.info("websocket推送成功：channel编号{}，内容{}", channel.id(), resultWorlds);
                            // 如果该用户的客户端是与本服务器建立的channel,直接推送消息
                            channel.writeAndFlush(new TextWebSocketFrame(resultWorlds));
                        } else {
                            log.info("websocket推送成功：channel编号{}，内容{}", channel.id(), resultItemWorlds);
                            // 如果该用户的客户端是与本服务器建立的channel,直接推送消息
                            channel.writeAndFlush(new TextWebSocketFrame(resultItemWorlds));
                        }
                    }
                }

            });
        }
    }


    /**
     * 首页年份下拉框
     *
     * @Date 17:00 2022/1/4
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> yearSelect(List<Long> companyId) {
        return studentOfferItemMapper.selectYear(companyId);
    }

/*

    private void pushDatas(ConcurrentHashMap<Long, Channel> userChannelMap, ConcurrentHashMap<Long, SystemPageVo> userSearchMap) {
        Map areaCountryMap = getSameMap(userSearchMap, "fkCompanyIds");
        areaCountryMap.forEach((key, value) ->
        {
            List<Long> companyIds = getSelectLong(key.toString());
            //获取推送的用户ids
            List<Long> userIds = getSelectLong(value.toString());
            //查询数据 ->遍历userIds ->推送消息
            List<WorldHistogramVo> worldHistograms = studentCountService.getWorldHistogram(companyIds, null);
            List<WorldHistogramVo> stateStudentNums = studentCountService.getStateStudentNum(companyIds, null);
            List<WorldMapVo> worldMapDtos = studentCountService.getWorldMapDtos(companyIds);
            SystemPageVo1 systemPageDto = new SystemPageVo1();
            systemPageDto.setCountryStudentCount(worldHistograms);
            systemPageDto.setStateStudentCount(stateStudentNums);
            systemPageDto.setWorldMapDtos(worldMapDtos);
            String resultWorlds = JSONUtil.toJsonStr(systemPageDto);
            for (Long userId : userIds) {
                Channel channel = userChannelMap.get(userId);
                if (!Objects.isNull(channel)) {
                    // 如果该用户的客户端是与本服务器建立的channel,直接推送消息
                    channel.writeAndFlush(new TextWebSocketFrame(resultWorlds));
                }
            }
        });

    }
*/


    /*private void pushWorldHistogram(ConcurrentHashMap<Long, Channel> userChannelMap, ConcurrentHashMap<Long, SystemPageVo> userSearchMap) {
        Map areaCountryMap = getSameMap(userSearchMap, "fkCompanyIds");
        areaCountryMap.forEach((key, value) ->
        {
            List<Long> companyIds = new ArrayList<>();
            String[] split = key.toString().split(":");
            //公司id
            String[] cids = split[0].split(",");
            for (String id : cids) {
                companyIds.add(Long.valueOf(id));
            }
            //获取推送的用户ids
            List<Long> userIds = getSelectLong(value.toString());
            //查询数据 ->遍历userIds ->推送消息
            List<WorldHistogramVo> worldHistogramDtos = studentCountService.getWorldHistogram(companyIds, null);
            String resultWorlds = JSONUtil.toJsonStr(worldHistogramDtos);
            for (Long userId : userIds) {
                Channel channel = userChannelMap.get(userId);
                if (!Objects.isNull(channel)) {
                    // 如果该用户的客户端是与本服务器建立的channel,直接推送消息
                    channel.writeAndFlush(new TextWebSocketFrame("countryStudentCount" + ":" + resultWorlds));
                }
            }
        });

    }*/


    private void pushStateStudentNum
            (ConcurrentHashMap<Long, Channel> userChannelMap, ConcurrentHashMap<Long, SystemPageVo> userSearchMap) {
        Map areaStateMap = getSameMap(userSearchMap, "areaStateIds");
        areaStateMap.forEach((key, value) ->
        {
            List<Long> companyIds = new ArrayList<>();
            String[] split = key.toString().split(":");
            //公司id
            String[] cids = split[0].split(",");
            for (String id : cids) {
                companyIds.add(Long.valueOf(id));
            }
            //获取国家选项
            List<Long> areaStateIds = new ArrayList<>();
            /*List<Long> areaStateIds = getSelectLong(split[1]);*/
            //获取推送的用户ids
            List<Long> userIds = getSelectLong(value.toString());
            //查询数据 ->遍历userIds ->推送消息
            List<WorldHistogramVo> worldHistogramVos = null;
            try {
                worldHistogramVos = studentCountService.getStateStudentNum(companyIds, SecureUtil.getCountryIds(), areaStateIds, "2022", true, SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(), null);
            } catch (Exception e) {
                e.printStackTrace();
            }
            String resultWorlds = JSONUtil.toJsonStr(worldHistogramVos);
            for (Long userId : userIds) {
                Channel channel = userChannelMap.get(userId);
                if (!Objects.isNull(channel)) {
                    // 如果该用户的客户端是与本服务器建立的channel,直接推送消息
                    channel.writeAndFlush(new TextWebSocketFrame("stateStudentCount" + ":" + resultWorlds));
                }
            }
        });

    }


    /**
     * @return java.util.Map
     * @Description: 将搜索条件一样的value 进行转换
     * @Param [map]
     * <AUTHOR>
     */
    private Map getSameMap(Map<Long, SystemPageVo> map, String type) {
        Map values = new HashMap(8);
        List list;
        for (Long key : map.keySet()) {
            Object value = null;
            if ("fkCompanyIds".equals(type)) {
                value = map.get(key).getFkCompanyIds();
            }
            if ("areaCountryIds".equals(type)) {
                List<Long> companyIds = map.get(key).getFkCompanyIds();
                value = companyIds + ":" + map.get(key).getAreaCountryIds();
            }
            if ("areaStateIds".equals(type)) {
                List<Long> companyIds = map.get(key).getFkCompanyIds();
                value = companyIds + ":" + map.get(key).getAreaStateIds();
            }
            if ("num".equals(type)) {
                List<Long> companyIds = map.get(key).getFkCompanyIds();
                value = companyIds + ":" + map.get(key).getNum();
            }
            //查看已有条件是否包含当前条件
            if (values.containsKey(value)) {
                list = (List) values.get(value);
            } else {
                list = new ArrayList();
            }
            //有则加入已有用户数组，没有则新增数组
            list.add(key);
            values.put(value, list);
        }
        return values;
    }

    private List<Long> getSelectLong(String s) {
        String trim = s.substring(1, s.length() - 1).trim();
        if (GeneralTool.isEmpty(trim)) {
            return null;
        }
        String[] split1 = trim.split(",");
        return Arrays.stream(split1).map(s1 -> Long.parseLong(s1.trim())).collect(Collectors.toList());
    }

}
