package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.vo.InstitutionProviderVo;
import com.get.institutioncenter.entity.InstitutionProviderInstitution;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface InstitutionProviderInstitutionMapper extends BaseMapper<InstitutionProviderInstitution> {

    /**
     * 根据学校查找是否有关联
     *
     * @param id
     * @return
     */
    Integer getCountByInstitutionId(Long id);

    /**
     * 校验 学校提供商是否能删除
     *
     * @Date 10:33 2021/4/16
     * <AUTHOR>
     */
    boolean providerInfoIsEmpty(Long providerId);

    /**
     * 获取对应学校提供商ids对应关联的学校数量
     *
     * @param institutionProviderIdSet
     * @param companyIds
     * @return
     */
    List<InstitutionProviderVo> getInstitutionCountByInstitutionProviderIds(@Param("institutionProviderIdSet") Set<Long> institutionProviderIdSet,
                                                                            @Param("companyIds") List<Long> companyIds);
}