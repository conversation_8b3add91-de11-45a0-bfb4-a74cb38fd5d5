package com.get.insurancecenter.vo.order;

import com.get.insurancecenter.entity.InsuranceOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author:Oliver
 * @Date: 2025/8/5
 * @Version 1.0
 * @apiNote:信用卡订单信息
 */
@Data
public class CreditCardOrderVo extends InsuranceOrder {

    @ApiModelProperty(value = "保险公司名称")
    private String insuranceCompanyName;

    @ApiModelProperty(value = "保险产品类型名称")
    private String productTypeName;

    @ApiModelProperty(value = "代理编号")
    private String agentNum;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "订单状态(展示以此字段为准):-2:下单失败;1:下单中;2:已完成待生效;3:生效中;4:已失效;5:已退款(退保);6:已取消;")
    private Integer showStatus;

}
