package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 系统业务操作对应凭证创建配置
 */
@Data
@TableName("u_vouch_operation_config")
public class VouchOperationConfig extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "系统业务操作Key")
    private String operationKey;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "科目Id")
    private Long fkAccountingItemId;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "关联类型Key（目标类型表名）")
    private String relationTargetKey;

}