<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.mpscenter.dao.MpsKeyWordMapper">

    <select id="getMaxOrder" resultType="java.lang.Integer">
        select
            IFNULL(max(view_order)+1,0) view_order
        from
            u_mps_key_word
    </select>
    <select id="getKeyWords" resultType="com.get.mpscenter.vo.KeyWordVo">
        select
        umkw.*
        from
        u_mps_key_word umkw
        left join r_mps_key_word_major_level rimcm on rimcm.fk_mps_key_word_id = umkw.id
        where 1=1
        <if test="keyWordMajorLevelSelectDto.keyWord != null and keyWordMajorLevelSelectDto.keyWord != ''">
            and umkw.key_word like concat('%',#{keyWordMajorLevelSelectDto.keyWord},'%')
        </if>
        <if test="keyWordMajorLevelSelectDto.fkMajorLevelIds != null and keyWordMajorLevelSelectDto.fkMajorLevelIds.size()>0 ">
            and rimcm.fk_major_level_id in
            <foreach collection="keyWordMajorLevelSelectDto.fkMajorLevelIds" item="item" separator="," open="("
                     close=")">
                #{item}
            </foreach>
        </if>
        group by umkw.id
        order by umkw.view_order desc,umkw.key_word
    </select>

</mapper>