package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.vo.StudentServiceFeeCostListVo;
import com.get.salecenter.vo.StudentServiceFeeCostVo;
import com.get.salecenter.service.StudentServiceFeeCostService;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.StudentServiceFeeCostListDto;
import com.get.salecenter.dto.StudentServiceFeeCostDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  前端接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-05
 */
@Api(tags = "留学服务费其他支出费用管理")
@RestController
@RequestMapping("sale/studentServiceFeeCost")
public class StudentServiceFeeCostController {

    @Resource
    private StudentServiceFeeCostService studentServiceFeeCostService;

    @ApiOperation(value = "新增")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/留学服务费其他支出费用管理/新增")
    @PostMapping("add")
    public ResponseBo add(@Validated({BaseVoEntity.Add.class}) @RequestBody StudentServiceFeeCostDto studentServiceFeeCostDto) {
        return SaveResponseBo.ok(studentServiceFeeCostService.add(studentServiceFeeCostDto));
    }


    @ApiOperation(value = "详情", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/留学服务费其他支出费用管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<StudentServiceFeeCostVo> detail(@PathVariable("id")Long id) {
        return new ResponseBo<>(studentServiceFeeCostService.findStudentServiceFeeCostById(id).orElseThrow(()->new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"))));
    }

    @ApiOperation(value = "编辑")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/留学服务费其他支出费用管理/编辑")
    @PostMapping("update")
    public ResponseBo<StudentServiceFeeCostVo> update(@Validated({BaseVoEntity.Update.class}) @RequestBody StudentServiceFeeCostDto studentServiceFeeCostDto) {
        return UpdateResponseBo.ok(studentServiceFeeCostService.updateStudentServiceFeeCost(studentServiceFeeCostDto));
    }

    @ApiOperation(value = "修改激活状态接口", notes = "0停用/1激活")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/留学服务费其他支出费用管理/修改激活状态接口")
    @PostMapping("updateActive")
    public ResponseBo updateActive(@RequestParam("id")Long id, @RequestParam("status")Integer status) {
        studentServiceFeeCostService.updateActive(id,status);
        return UpdateResponseBo.ok();
    }

    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/留学服务费其他支出费用管理/列表数据")
    @PostMapping("datas")
    public ListResponseBo<StudentServiceFeeCostListVo> datas(@RequestBody @Validated SearchBean<StudentServiceFeeCostListDto> page) {
        List<StudentServiceFeeCostListVo> datas = studentServiceFeeCostService.getStudentServiceFeeCostListDtos(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "上传附件")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/留学服务费其他支出费用管理/上传附件")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> addAttachedFile(@RequestBody  @Validated(MediaAndAttachedDto.Add.class) ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(studentServiceFeeCostService.addAttachedFile(mediaAttachedVo));
    }

}
