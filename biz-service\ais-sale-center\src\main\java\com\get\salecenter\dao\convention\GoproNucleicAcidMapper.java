package com.get.salecenter.dao.convention;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.entity.GoproNucleicAcid;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
@DS("conventiondb")
public interface GoproNucleicAcidMapper extends BaseMapper<GoproNucleicAcid>, GetMapper<GoproNucleicAcid> {

    @Override
    int insert(GoproNucleicAcid record);

    int insertSelective(GoproNucleicAcid record);

    Integer getMaxOrderId(@Param("type")String type);
}