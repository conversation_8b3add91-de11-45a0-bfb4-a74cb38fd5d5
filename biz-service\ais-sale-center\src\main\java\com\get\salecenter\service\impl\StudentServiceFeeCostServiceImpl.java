package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.salecenter.dao.sale.StudentServiceFeeCostMapper;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.vo.StudentServiceFeeCostListVo;
import com.get.salecenter.vo.StudentServiceFeeCostVo;
import com.get.salecenter.entity.StudentServiceFeeCost;
import com.get.salecenter.service.IMediaAndAttachedService;
import com.get.salecenter.service.StudentServiceFeeCostService;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.StudentServiceFeeCostListDto;
import com.get.salecenter.dto.StudentServiceFeeCostDto;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-05
 */
@Service
public class StudentServiceFeeCostServiceImpl extends BaseServiceImpl<StudentServiceFeeCostMapper, StudentServiceFeeCost> implements StudentServiceFeeCostService {

    @Resource
    private UtilService utilService;

    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private IMediaAndAttachedService attachedService;

    /**
     * 添加学生服务费用信息
     * @param studentServiceFeeCostDto 学生服务费用信息对象
     * @return 添加成功的学生服务费用ID
     * @throws GetServiceException 添加失败时抛出异常
     */
    @Override
    public Long add(StudentServiceFeeCostDto studentServiceFeeCostDto) {
        // 克隆学生服务费用信息对象
        StudentServiceFeeCost studentServiceFeeCost = BeanCopyUtils.objClone(studentServiceFeeCostDto, StudentServiceFeeCost::new);
        // 设置学生服务费用状态为生效
        studentServiceFeeCost.setStatus(1);
        // 设置创建信息
        utilService.setCreateInfo(studentServiceFeeCost);

        // 插入学生服务费用信息
        int i = baseMapper.insert(studentServiceFeeCost);
        // 如果插入失败，则抛出异常
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        // 上传附件
        if (GeneralTool.isNotEmpty(studentServiceFeeCostDto.getMediaAttachedVo())) {
            List<MediaAndAttachedDto> mediaAndAttachedDtoList = studentServiceFeeCostDto.getMediaAttachedVo();
            for (MediaAndAttachedDto mediaAndAttachedDto : mediaAndAttachedDtoList) {
                mediaAndAttachedDto.setFkTableId(studentServiceFeeCost.getId());
                mediaAndAttachedDto.setFkTableName(ProjectKeyEnum.M_STUDENT_SERVICE_FEE_COST.key);
                attachedService.addMediaAndAttached(mediaAndAttachedDto);
            }
        }
        // 返回学生服务费用ID
        return studentServiceFeeCost.getId();
    }

    /**
     * 根据学生服务费用ID查询学生服务费用信息
     * @param id 学生服务费用ID
     * @return 可选的学生服务费用信息
     */
    @Override
    public Optional<StudentServiceFeeCostVo> findStudentServiceFeeCostById(Long id) {
        // 根据ID查询学生服务费用信息
        StudentServiceFeeCost studentServiceFeeCost = baseMapper.selectById(id);
        // 如果查询结果为空，则返回空
        if (GeneralTool.isEmpty(studentServiceFeeCost)) {
            return Optional.empty();
        }
        // 克隆学生服务费用信息到学生服务费用信息DTO对象
        StudentServiceFeeCostVo studentServiceFeeCostVo = BeanCopyUtils.objClone(studentServiceFeeCost, StudentServiceFeeCostVo::new);
        // 确保学生服务费用信息DTO对象不为空
        assert studentServiceFeeCostVo != null;
        if (GeneralTool.isNotEmpty(studentServiceFeeCostVo.getFkCurrencyTypeNum())){
            String fkCurrencyTypeNumName = financeCenterClient.getCurrencyNameByNum(studentServiceFeeCostVo.getFkCurrencyTypeNum()).getData();
            studentServiceFeeCostVo.setFkCurrencyNumName(fkCurrencyTypeNumName);
        }

        // 获取付款银行名称
        if (GeneralTool.isNotEmpty(studentServiceFeeCostVo.getFkBankAccountIdCompany())) {
            Result<Map<Long, String>> result = financeCenterClient.getBankNameByIds(Collections.singleton(studentServiceFeeCostVo.getFkBankAccountIdCompany()));
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                Map<Long, String> bankNameMap = result.getData();
                studentServiceFeeCostVo.setBankName(bankNameMap.get(studentServiceFeeCostVo.getFkBankAccountIdCompany()));
            }
        }
        // 返回学生服务费用信息DTO对象
        return Optional.of(studentServiceFeeCostVo);
    }

    /**
     * 更新学生服务费成本
     * @param studentServiceFeeCostDto 学生服务费成本VO对象
     * @return 更新后的学生服务费成本DTO对象
     */
    @Override
    public StudentServiceFeeCostVo updateStudentServiceFeeCost(StudentServiceFeeCostDto studentServiceFeeCostDto) {
        // 克隆学生服务费成本VO对象
        StudentServiceFeeCost studentServiceFeeCost = BeanCopyUtils.objClone(studentServiceFeeCostDto, StudentServiceFeeCost::new);
        // 设置更新信息
        utilService.setUpdateInfo(studentServiceFeeCost);
        // 通过Mapper更新学生服务费成本
        this.baseMapper.updateById(studentServiceFeeCost);
        // 根据ID查询学生服务费成本，若为空则抛出异常
        return findStudentServiceFeeCostById(studentServiceFeeCost.getId()).orElseThrow(()->new GetServiceException(LocaleMessageUtils.getMessage("search_result_null")));
    }

    /**
     * 更新学生服务费成本的激活状态
     *
     * @param id 学生服务费成本ID
     * @param status 激活状态（1表示激活，0表示不激活）
     */
    @Override
    public void updateActive(Long id, Integer status) {
        // 根据ID查询学生服务费成本
        StudentServiceFeeCost studentServiceFeeCost = baseMapper.selectById(id);
        if (studentServiceFeeCost != null) {
            // 更新激活状态
            studentServiceFeeCost.setStatus(status == 1 ? 1 : 0);
            try {
                // 记录更新信息
                utilService.setUpdateInfo(studentServiceFeeCost);
                // 更新学生服务费成本
                int updatedRows = baseMapper.updateById(studentServiceFeeCost);
                if (updatedRows <= 0) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
                }
            } catch (Exception e) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
    }

    /**
     * 获取学生服务费成本列表的DTOs
     *
     * @param studentServiceFeeCostListDto 学生服务费成本列表的VO
     * @param page                       分页对象
     * @return 学生服务费成本列表的DTOs
     */
    @Override
    public List<StudentServiceFeeCostListVo> getStudentServiceFeeCostListDtos(StudentServiceFeeCostListDto studentServiceFeeCostListDto, Page page) {
        // 检查学生服务费成本列表VO是否为空
        if (GeneralTool.isEmpty(studentServiceFeeCostListDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }

        // 获取分页对象
        IPage<StudentServiceFeeCostListVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));

        // 调用mapper方法获取学生服务费成本列表的DTOs
        List<StudentServiceFeeCostListVo> studentServiceFeeCostListVos = baseMapper.getStudentServiceFeeCostListDtos(iPage, studentServiceFeeCostListDto);

        // 设置总记录数
        page.setAll((int) iPage.getTotal());

        // 如果学生服务费成本列表的DTOs为空，则返回空列表
        if (GeneralTool.isEmpty(studentServiceFeeCostListVos)){
            return Collections.emptyList();
        }

        // 创建货币类型编号名称映射的Map
        Map<String, String> currencyTypeNumNameMap = Maps.newHashMap();

        // 获取学生服务费成本列表的DTOs中的货币类型编号集合
        Set<String> currencyTypeNums = studentServiceFeeCostListVos.stream().map(StudentServiceFeeCostListVo::getFkCurrencyTypeNum).collect(Collectors.toSet());

        // 如果货币类型编号集合不为空，则调用financeCenterClient获取货币类型名称映射的Map
        if (GeneralTool.isNotEmpty(currencyTypeNums)){
            Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNums);
            if (result.isSuccess()){
                currencyTypeNumNameMap = result.getData();
            }
        }

        // 付款银行Ids
        Set<Long> bankAccountIds = studentServiceFeeCostListVos.stream()
                .map(StudentServiceFeeCostListVo::getFkBankAccountIdCompany)
                .filter(GeneralTool::isNotEmpty)
                .collect(Collectors.toSet());
        // 获取付款银行名称
        Map<Long, String> bankNameMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(bankAccountIds)) {
            Result<Map<Long, String>> result = financeCenterClient.getBankNameByIds(bankAccountIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                bankNameMap = result.getData();
            }
        }

        // 获取附件列表
        List<Long> studentServiceFeeCostIds = studentServiceFeeCostListVos.stream().map(StudentServiceFeeCostListVo::getId).collect(Collectors.toList());
        List<MediaAndAttachedVo> attachedDtos = attachedService.getMediaAndAttachedDtos(studentServiceFeeCostIds,
                ProjectKeyEnum.M_STUDENT_SERVICE_FEE_COST.key,
                FileTypeEnum.M_STUDENT_SERVICE_FEE_COST_INVOICE.key);
        Map<Long, List<MediaAndAttachedVo>> mediaMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(attachedDtos)) {
            mediaMap = attachedDtos.stream().collect(Collectors.groupingBy(MediaAndAttachedVo::getFkTableId));
        }

        for (StudentServiceFeeCostListVo studentServiceFeeCostListVo : studentServiceFeeCostListVos) {
            studentServiceFeeCostListVo.setFkCurrencyTypeNumName(currencyTypeNumNameMap.get(studentServiceFeeCostListVo.getFkCurrencyTypeNum()));
            studentServiceFeeCostListVo.setBankName(bankNameMap.get(studentServiceFeeCostListVo.getFkBankAccountIdCompany()));
            studentServiceFeeCostListVo.setMediaAndAttachedDtos(mediaMap.get(studentServiceFeeCostListVo.getId()));
        }

        // 返回学生服务费成本列表的DTOs
        return studentServiceFeeCostListVos;
    }

    @Override
    public List<MediaAndAttachedVo> addAttachedFile(ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        if (GeneralTool.isEmpty(mediaAttachedVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVo) {
            // 设置插入的表
            mediaAndAttachedDto.setFkTableName(ProjectKeyEnum.M_STUDENT_SERVICE_FEE_COST.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }

}
