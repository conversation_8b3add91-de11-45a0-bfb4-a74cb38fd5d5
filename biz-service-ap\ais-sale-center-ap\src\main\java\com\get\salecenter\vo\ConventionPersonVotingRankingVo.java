package com.get.salecenter.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "峰会参会人员助力排行返回类")
public class ConventionPersonVotingRankingVo {

    @ApiModelProperty(value = "排名")
    private Integer ranking;

    @ApiModelProperty(value = "助力值")
    private Integer voteCount;

    @ApiModelProperty(value = "bdName")
    private String bdName;

    @ApiModelProperty(value = "bdCode")
    private String bdCode;

    @ApiModelProperty(value = "峰会人员名")
    private String fkConventionPersonName;

    @ApiModelProperty(value = "峰会人员手机号")
    private String fkConventionPersonPhone;

    @ApiModelProperty(value = "峰会人员Id")
    private Long fkConventionPersonId;
}

