package com.get.reportcenter.service.impl;

import com.get.common.utils.LocaleMessageUtils;
import com.get.competitioncenter.feign.ICompetitionCenterClient;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.officecenter.dto.query.LeaveApplicationFormQueryDto;
import com.get.officecenter.feign.IOfficeCenterClient;
import com.get.officecenter.vo.LeaveApplicationFormExportVo;
import com.get.reportcenter.service.IExportService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/2 17:55
 */
@Service
public class ExportServiceImpl implements IExportService {
//    @Resource
//    private StudentOfferItemMapper studentOfferItemMapper;

    @Resource
    private ICompetitionCenterClient competitionCenterClient;

    @Resource
    private IOfficeCenterClient officeCenterClient;
//
//    /**
//     * 成功客户列表导出
//     *
//     * @return
//     * @Date 15:22 2022/3/2
//     * <AUTHOR>
//     */
//    @Override
//    public List<StudentOfferItemReportModel> successfullyExportedCustomerList(ReportStudentOfferItemDto studentOfferItemVo) {
////        List<StudentOfferItemReportModel> studentOfferItemList = studentOfferItemMapper.getStudentOfferItemDtoList(studentOfferItemVo);
////        return studentOfferItemList;
//        return null;
//    }
//
//
//    /**
//     * 导出竞赛报名名册
//     * @param response
//     * @param competitionRegistrationListVo
//     */
//    @Override
//    public void exportCompetitionRegistrationExcel(HttpServletResponse response, CompetitionRegistrationListDto competitionRegistrationListVo) {
//
//        List<CompetitionRegistrationExportDto> competitionRegistrationExportDtos = competitionCenterClient.getCompetitionRegistrationExportDtos(competitionRegistrationListVo).getData();
//
//        if (GeneralTool.isEmpty(competitionRegistrationExportDtos)){
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
//        }
//
//        FileUtils.exportExcelNotWrapText(response, competitionRegistrationExportDtos, "CompetitionRegistration", CompetitionRegistrationExportDto.class);
//
//    }
//
    @Override
    public void exportLeaveApplicationFormExcel(HttpServletResponse response, LeaveApplicationFormQueryDto leaveApplicationFormVo) {
        List<LeaveApplicationFormExportVo> leaveApplicationFormExportVos = officeCenterClient.getLeaveApplicationFormExportDtos(leaveApplicationFormVo).getData();
        if (GeneralTool.isEmpty(leaveApplicationFormExportVos)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        FileUtils.exportExcelNotWrapText(response, leaveApplicationFormExportVos, "LeaveApplicationForm", LeaveApplicationFormExportVo.class);
    }
//
//
}
