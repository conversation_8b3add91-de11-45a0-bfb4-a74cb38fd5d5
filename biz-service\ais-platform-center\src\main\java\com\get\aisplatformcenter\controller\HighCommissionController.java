package com.get.aisplatformcenter.controller;


import com.get.aisplatformcenter.service.MInstitutionHighCommissionService;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.partnercenter.dto.DeletePatchParamsDto;
import com.get.partnercenter.dto.HighCommissionDto;
import com.get.partnercenter.dto.HighCommissionPutAwayParamsDto;
import com.get.partnercenter.vo.HighCommissionComboxVo;
import com.get.partnercenter.vo.HighCommissionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Api(tags = "后台管理-高佣管理")
@RestController
@RequestMapping("platform/highCommission")
public class HighCommissionController {
    @Autowired
    private MInstitutionHighCommissionService mInstitutionService;

    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.LIST, description = "华通伙伴/高佣维护/列表查询")
    @PostMapping("searchPage")
    public ResponseBo<HighCommissionVo> searchPage(@RequestBody SearchBean<HighCommissionDto> page) {
        List<HighCommissionVo> datas = mInstitutionService.searchPage(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/高佣维护/新增")
    @PostMapping("save")
    public ResponseBo save(@RequestBody @Validated(BaseVoEntity.Add.class)  HighCommissionDto dto){
        return SaveResponseBo.ok(mInstitutionService.saveOrUpdateHightCommission(dto));
    }



    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/高佣维护/修改")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated(BaseVoEntity.Update.class)  HighCommissionDto dto){
        return SaveResponseBo.ok(mInstitutionService.saveOrUpdateHightCommission(dto));
    }

    @ApiOperation(value = "一键(上架)下架-接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/高佣维护/一键(上架)下架-接口")
    @PostMapping("putAway")
    public ResponseBo putAway(@RequestBody  @Valid HighCommissionPutAwayParamsDto dto){

        return SaveResponseBo.ok(mInstitutionService.putAway(dto));
    }

    @ApiOperation(value = "一键删除-接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/高佣维护/一键删除")
    @PostMapping("deleteBatch")
    public ResponseBo deleteBatch(@RequestBody  @Valid DeletePatchParamsDto entity){
        mInstitutionService.deleteBatch(entity);
        return SaveResponseBo.ok();
    }


    @ApiOperation(value = "详情-接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/消息管理/详情")
    @GetMapping("getDetail/{id}")
    public ResponseBo<HighCommissionVo> getDetail(@PathVariable("id") Long id){
        HighCommissionVo bannerDetail=mInstitutionService.getDetail(id);
        return new ResponseBo<>(bannerDetail);
    }


    @ApiOperation(value = "高佣学校数据")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.LIST, description = "华通伙伴/直播类型管理/列表查询")
    @PostMapping("searchList")
    public ResponseBo<HighCommissionComboxVo> searchList(){

        List<HighCommissionComboxVo> datas=mInstitutionService.searchList();
        return new ResponseBo(datas);
    }



}
