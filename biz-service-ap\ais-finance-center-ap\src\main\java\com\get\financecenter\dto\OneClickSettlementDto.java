package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 应付计划一键结算按钮按钮
 *
 * <AUTHOR>
 * @date 2022/3/22 11:06
 */
@Data
public class OneClickSettlementDto {

    @ApiModelProperty(value = "应付计划ids")
    @NotNull(message = "应付计划ids不能为空")
    private List<Long> payablePlanIds;

    @ApiModelProperty(value = "结算金额不能为空")
    @NotNull(message = "结算金额不能为空")
    private BigDecimal settlementAmount;



}
