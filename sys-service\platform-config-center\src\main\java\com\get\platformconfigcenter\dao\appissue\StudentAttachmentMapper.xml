<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.platformconfigcenter.dao.appissue.StudentAttachmentMapper">
  <resultMap id="BaseResultMap" type="com.get.platformconfigcenter.entity.StudentAttachment">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type_key" jdbcType="VARCHAR" property="typeKey" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="file_extension" jdbcType="VARCHAR" property="fileExtension" />
    <result column="size_limit" jdbcType="INTEGER" property="sizeLimit" />
    <result column="is_required" jdbcType="BIT" property="isRequired" />
    <result column="is_active" jdbcType="BIT" property="isActive" />
    <result column="is_multiple_required" jdbcType="BIT" property="isMultipleRequired" />
    <result column="view_order" jdbcType="INTEGER" property="viewOrder" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <sql id="Base_Column_List">
    id, type_key, name, remark, file_extension, size_limit, is_active, view_order, gmt_create, 
    gmt_create_user, gmt_modified, gmt_modified_user, is_required, is_multiple_required
  </sql>
  <!--TODO 注释sql-->
<!--  <insert id="insert" parameterType="com.get.platformconfigcenter.entity.StudentAttachment" keyProperty="id" useGeneratedKeys="true">-->
<!--    insert into u_student_attachment (id, type_key, name, -->
<!--      remark, file_extension, size_limit, is_required,-->
<!--      is_active, view_order, gmt_create, is_multiple_required,-->
<!--      gmt_create_user, gmt_modified, gmt_modified_user-->
<!--      )-->
<!--    values (#{id,jdbcType=BIGINT}, #{typeKey,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, -->
<!--      #{remark,jdbcType=VARCHAR}, #{fileExtension,jdbcType=VARCHAR}, #{sizeLimit,jdbcType=INTEGER}, #{isRequired,jdbcType=BIT},-->
<!--      #{isActive,jdbcType=BIT}, #{viewOrder,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP},  #{isMultipleRequired,jdbcType=BIT},-->
<!--      #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR}-->
<!--      )-->
<!--  </insert>-->
<!--  <insert id="insertSelective" parameterType="com.get.platformconfigcenter.entity.StudentAttachment">-->
<!--    insert into u_student_attachment-->
<!--    <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        id,-->
<!--      </if>-->
<!--      <if test="typeKey != null">-->
<!--        type_key,-->
<!--      </if>-->
<!--      <if test="name != null">-->
<!--        name,-->
<!--      </if>-->
<!--      <if test="remark != null">-->
<!--        remark,-->
<!--      </if>-->
<!--      <if test="fileExtension != null">-->
<!--        file_extension,-->
<!--      </if>-->
<!--      <if test="sizeLimit != null">-->
<!--        size_limit,-->
<!--      </if>-->
<!--      <if test="isActive != null">-->
<!--        is_active,-->
<!--      </if>-->
<!--      <if test="viewOrder != null">-->
<!--        view_order,-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        gmt_create,-->
<!--      </if>-->
<!--      <if test="gmtCreateUser != null">-->
<!--        gmt_create_user,-->
<!--      </if>-->
<!--      <if test="gmtModified != null">-->
<!--        gmt_modified,-->
<!--      </if>-->
<!--      <if test="gmtModifiedUser != null">-->
<!--        gmt_modified_user,-->
<!--      </if>-->
<!--      <if test="isRequired != null">-->
<!--        is_required,-->
<!--      </if>-->
<!--      <if test="isMultipleRequired != null">-->
<!--        is_multiple_required,-->
<!--      </if>-->
<!--    </trim>-->
<!--    <trim prefix="values (" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        #{id,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="typeKey != null">-->
<!--        #{typeKey,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="name != null">-->
<!--        #{name,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="remark != null">-->
<!--        #{remark,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="fileExtension != null">-->
<!--        #{fileExtension,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="sizeLimit != null">-->
<!--        #{sizeLimit,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="isActive != null">-->
<!--        #{isActive,jdbcType=BIT},-->
<!--      </if>-->
<!--      <if test="viewOrder != null">-->
<!--        #{viewOrder,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        #{gmtCreate,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtCreateUser != null">-->
<!--        #{gmtCreateUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="gmtModified != null">-->
<!--        #{gmtModified,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtModifiedUser != null">-->
<!--        #{gmtModifiedUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="isRequired != null">-->
<!--        #{isRequired,jdbcType=BIT},-->
<!--      </if>-->
<!--      <if test="isMultipleRequired != null">-->
<!--        #{isMultipleRequired,jdbcType=BIT},-->
<!--      </if>-->
<!--    </trim>-->
<!--  </insert>-->
<!--  <update id="updateByPrimaryKeySelective" parameterType="com.get.platformconfigcenter.entity.StudentAttachment">-->
<!--    update u_student_attachment-->
<!--    <set>-->
<!--      <if test="typeKey != null">-->
<!--        type_key = #{typeKey,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="name != null">-->
<!--        name = #{name,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="remark != null">-->
<!--        remark = #{remark,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="fileExtension != null">-->
<!--        file_extension = #{fileExtension,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="sizeLimit != null">-->
<!--        size_limit = #{sizeLimit,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="isActive != null">-->
<!--        is_active = #{isActive,jdbcType=BIT},-->
<!--      </if>-->
<!--      <if test="viewOrder != null">-->
<!--        view_order = #{viewOrder,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtCreateUser != null">-->
<!--        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="gmtModified != null">-->
<!--        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtModifiedUser != null">-->
<!--        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="isRequired != null">-->
<!--        is_required = #{isRequired,jdbcType=BIT},-->
<!--      </if>-->
<!--      <if test="isMultipleRequired != null">-->
<!--        is_multiple_required = #{isMultipleRequired,jdbcType=BIT},-->
<!--      </if>-->
<!--    </set>-->
<!--    where id = #{id,jdbcType=BIGINT}-->
<!--  </update>-->
<!--  <update id="updateByPrimaryKey" parameterType="com.get.platformconfigcenter.entity.StudentAttachment">-->
<!--    update u_student_attachment-->
<!--    set type_key = #{typeKey,jdbcType=VARCHAR},-->
<!--      name = #{name,jdbcType=VARCHAR},-->
<!--      remark = #{remark,jdbcType=VARCHAR},-->
<!--      file_extension = #{fileExtension,jdbcType=VARCHAR},-->
<!--      size_limit = #{sizeLimit,jdbcType=INTEGER},-->
<!--      is_active = #{isActive,jdbcType=BIT},-->
<!--      view_order = #{viewOrder,jdbcType=INTEGER},-->
<!--      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},-->
<!--      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},-->
<!--      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},-->
<!--      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},-->
<!--      is_multiple_required = #{isMultipleRequired,jdbcType=BIT},-->
<!--      is_required = #{isRequired,jdbcType=BIT}-->
<!--    where id = #{id,jdbcType=BIGINT}-->
<!--  </update>-->
<!--  <select id="getMaxViewOrder" resultType="java.lang.Integer">-->
<!--    select-->
<!--      IFNULL(max(view_order)+1,0) view_order-->
<!--    from-->
<!--      u_student_attachment-->
<!--  </select>-->
</mapper>