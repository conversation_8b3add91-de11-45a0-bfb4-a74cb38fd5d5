package com.get.platformconfigcenter.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.platformconfigcenter.entity.Notice;
import com.get.platformconfigcenter.mapper.NoticeMapper;
import com.get.platformconfigcenter.service.IDynamicService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 动态数据源测试专用
 */
@Service
public class DynamicServiceImpl extends BaseServiceImpl<NoticeMapper, Notice> implements IDynamicService {

    //如果没有标记DS,则默认使用master数据源，如果其他数据源配置错误导致连接不上，则默认使用master
    @Override
    public List<Notice> masterList() {
        return this.list();
    }

    @Override
    @DS("issuedb")
    public List<Notice> slaveList() {
        return this.list();
    }
}
