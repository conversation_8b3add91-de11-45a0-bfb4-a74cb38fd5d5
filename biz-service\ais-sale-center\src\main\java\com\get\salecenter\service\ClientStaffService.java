package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.dto.ClientStaffDto;
import com.get.salecenter.vo.ClientStaffVo;

import javax.validation.Valid;
import java.util.List;


public interface ClientStaffService {

    List<BaseSelectEntity> getObtainSubordinateEmployeesSelect();

    void updateCLientStaff(ClientStaffDto clientStaffDto);

    void addClientStaff(ClientStaffDto clientStaffDto);

    List<ClientStaffVo> findAllByClientStaff(@Valid ClientStaffDto data, Page page);
}
