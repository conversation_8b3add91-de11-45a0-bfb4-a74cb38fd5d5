package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.institutioncenter.entity.Information;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @ClassName: Information
 * @Author: Eric
 * @Date: 2023/4/26 10:30
 * @Version: 1.0
 */
@Mapper
public interface InformationMapper extends BaseMapper<Information> {
    List<BaseSelectEntity> getAllInformationSelect();
}
