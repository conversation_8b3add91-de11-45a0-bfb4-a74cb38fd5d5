<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ConventionPersonMapper">
  <resultMap id="BaseResultMap" type="com.get.salecenter.entity.ConventionPerson">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_convention_id" jdbcType="BIGINT" property="fkConventionId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="num" jdbcType="VARCHAR" property="num" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="name_chn" jdbcType="VARCHAR" property="nameChn" />
    <result column="gender" jdbcType="INTEGER" property="gender" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="company" jdbcType="VARCHAR" property="company" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="tel" jdbcType="VARCHAR" property="tel" />
    <result column="passport_num" jdbcType="VARCHAR" property="passportNum" />
    <result column="id_card_num" jdbcType="VARCHAR" property="idCardNum" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />

    <result column="checkInTime" jdbcType="TIMESTAMP" property="checkInTime" />
    <result column="checkOutTime" jdbcType="TIMESTAMP" property="checkOutTime" />
    <result column="arrivalTime" jdbcType="TIMESTAMP" property="arrivalTime" />
    <result column="arrivalTransportation" jdbcType="VARCHAR" property="arrivalTransportation" />
    <result column="arrivalTransportationCode" jdbcType="VARCHAR" property="arrivalTransportationCode" />
    <result column="leaveTime" jdbcType="TIMESTAMP" property="leaveTime" />
    <result column="leaveTransportation" jdbcType="VARCHAR" property="leaveTransportation" />
    <result column="leaveTransportationCode" jdbcType="VARCHAR" property="leaveTransportationCode" />

    <result column="hotel_fee_type" jdbcType="INTEGER" property="hotelFeeType" />
    <result column="fk_currency_type_num_hotel_expense" jdbcType="VARCHAR" property="fkCurrencyTypeNumHotelExpense" />
    <result column="hotel_expense" jdbcType="DECIMAL" property="hotelExpense" />

    <result column="fk_convention_hotel_id" jdbcType="BIGINT" property="fkConventionHotelId" />
    <result column="bd_code" jdbcType="VARCHAR" property="bdCode" />

    <result column="is_vip" jdbcType="BIT" property="isVip" />

    <result column="is_attend" jdbcType="BIT" property="isAttend" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>


  <insert id="insertSelective" parameterType="com.get.salecenter.entity.ConventionPerson" keyProperty="id" useGeneratedKeys="true">
    insert into m_convention_person
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkConventionId != null">
        fk_convention_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="nameChn != null">
        name_chn,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="company != null">
        company,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="tel != null">
        tel,
      </if>
      <if test="passportNum != null">
        passport_num,
      </if>
      <if test="idCardNum != null">
        id_card_num,
      </if>
      <if test="remark != null">
        remark,
      </if>

      <if test="checkInTime != null">
        check_in_time,
      </if>
      <if test="checkOutTime != null">
        check_out_time,
      </if>
      <if test="arrivalTime != null">
        arrival_time,
      </if>
      <if test="arrivalTransportation != null">
        arrival_transportation,
      </if>
      <if test="arrivalTransportationCode != null">
        arrival_transportation_code,
      </if>
      <if test="leaveTime != null">
        leave_time,
      </if>
      <if test="leaveTransportation != null">
        leave_transportation,
      </if>
      <if test="leaveTransportationCode != null">
        leave_transportation_code,
      </if>

      <if test="hotelFeeType != null">
        hotel_fee_type,
      </if>
      <if test="fkCurrencyTypeNumHotelExpense != null">
        fk_currency_type_num_hotel_expense,
      </if>
      <if test="hotelExpense != null">
        hotel_expense,
      </if>
      <if test="fkConventionHotelId != null">
        fk_convention_hotel_id,
      </if>
      <if test="bdCode != null">
        bd_code,
      </if>
      <if test="isAttend != null">
        is_attend,
      </if>
      <if test="isVip != null">
        is_vip,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkConventionId != null">
        #{fkConventionId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        #{num,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="nameChn != null">
        #{nameChn,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=INTEGER},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="company != null">
        #{company,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="tel != null">
        #{tel,jdbcType=VARCHAR},
      </if>
      <if test="passportNum != null">
        #{passportNum,jdbcType=VARCHAR},
      </if>
      <if test="idCardNum != null">
        #{idCardNum,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>

      <if test="checkInTime != null">
        #{checkInTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checkOutTime != null">
        #{checkOutTime,jdbcType=TIMESTAMP},
      </if>
      <if test="arrivalTime != null">
        #{arrivalTime,jdbcType=TIMESTAMP},
      </if>
      <if test="arrivalTransportation != null">
        #{arrivalTransportation,jdbcType=VARCHAR},
      </if>
      <if test="arrivalTransportationCode != null">
        #{arrivalTransportationCode,jdbcType=VARCHAR},
      </if>
      <if test="leaveTime != null">
        #{leaveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="leaveTransportation != null">
        #{leaveTransportation,jdbcType=VARCHAR},
      </if>
      <if test="leaveTransportationCode != null">
        #{leaveTransportationCode,jdbcType=VARCHAR},
      </if>

      <if test="hotelFeeType != null">
        #{hotelFeeType,jdbcType=INTEGER},
      </if>
      <if test="fkCurrencyTypeNumHotelExpense != null">
        #{fkCurrencyTypeNumHotelExpense,jdbcType=VARCHAR},
      </if>
      <if test="hotelExpense != null">
        #{hotelExpense,jdbcType=DECIMAL},
      </if>

      <if test="fkConventionHotelId != null">
        #{fkConventionHotelId,jdbcType=BIGINT},
      </if>
      <if test="bdCode != null">
        #{bdCode,jdbcType=VARCHAR},
      </if>
      <if test="isAttend != null">
        #{isAttend,jdbcType=BIT},
      </if>
      <if test="isVip != null">
        #{isVip,jdbcType=BIT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="findConventionPersonNameAndSex" parameterType="java.lang.Long" resultType="com.get.salecenter.vo.ConventionPersonVo">
    select
     id,name,name_chn,gender,is_attend,type
    from
     m_convention_person
    where
     id = #{id}

  </select>

  <select id="getBdList" resultType="com.get.salecenter.vo.StaffBdCodeVo">
    select
    b.*,
    CASE WHEN IFNULL(ms.name_en,'')='' THEN ms.`name` ELSE CONCAT(ms.`name`,'（',ms.name_en,'）') END bdName
    from
    r_staff_bd_code b
    INNER JOIN ais_permission_center.m_staff ms on ms.id = b.fk_staff_id
    where
    b.fk_company_id = #{companyId}
    <if test="fkAreaRegionId != null">
      and b.fk_area_region_id = #{fkAreaRegionId}
    </if>
    order by bd_code
  </select>

  <select id="getAllBdList" resultType="com.get.salecenter.vo.StaffBdCodeVo">
    select
    b.*,
    concat(
            CASE WHEN IFNULL(ms.name_en, '') = '' THEN ms.`name` ELSE CONCAT(ms.`name`, '（', ms.name_en, '）') END
    )bdName
    from
    r_staff_bd_code b
    INNER JOIN ais_permission_center.m_staff ms on ms.id = b.fk_staff_id
    where
    b.fk_company_id = #{companyId} and
    b.bd_code not like "T%"
    order by  b.fk_area_region_id,bd_code
  </select>


  <select id="getPersonIdsByName" resultType="java.lang.Long">
    select
     id
    from
     m_convention_person
    where
     REPLACE(name," ","") like #{personName}
    or
     name like #{personName}
    or
     name_chn like #{personName}
    or
     REPLACE(name_chn," ","") like #{personName}
<!--      or
     to_pinyin(REPLACE ( name_chn, " ", "" )) like #{personName} -->

  </select>

  <select id="findPersonById" resultType="com.get.salecenter.vo.ConventionPersonVo">
    select
     id , name ,name_chn ,is_attend,type
    from
     m_convention_person
    where
     id = #{id}

  </select>

  <select id="getPersonForTableList" resultType="com.get.salecenter.entity.ConventionPerson">
    select
     id,type,name,name_chn ,company from m_convention_person
    where
     id
    in
     (select
       DISTINCT(a.fk_convention_person_id)
      from
       r_convention_person_procedure a
      where fk_convention_procedure_id
      in
       (select id from m_convention_procedure where fk_table_type_key = #{type} and fk_convention_id = #{id}))

  </select>

  <!--拷贝上面的-->
  <select id="getPersonForTable" resultType="com.get.salecenter.entity.ConventionPerson">
    select
      id,type,name,name_chn ,company from m_convention_person
    where
        id
        in
        (select
           DISTINCT(a.fk_convention_person_id)
         from
           r_convention_table_person a
         where fk_convention_table_id
                 in
               (select id from m_convention_table where fk_table_type_key = #{type} and fk_convention_id = #{id}))
  </select>

  <select id="getBdCode" resultType="java.lang.String">
    select
     bd_code
    from
      r_staff_bd_code
    <where>
      <if test="staffIds != null and staffIds.size()>0">
      and fk_staff_id in
      <foreach collection="staffIds" item="staffId" index="index" open="(" separator="," close=")">
        #{staffId}
      </foreach>
      </if>
    </where>
  </select>
  <select id="getBdName" resultType="java.lang.Long">
    select
      fk_staff_id
    from
      r_staff_bd_code
    where
     bd_code = #{bdCode}
  </select>

  <select id="getNotArrangedPersonList" resultType="com.get.salecenter.vo.ConventionPersonVo">
   select a.id,a.type,a.name,a.name_chn,a.company,a.gender,a.num
   from m_convention_person a
   where fk_convention_id = #{conventionDto.fkConventionId} and id
    in
     (
    select fk_convention_person_id from r_convention_person_procedure where 1=1
    <if test="personIds!=null and personIds.size>0">
      AND fk_convention_person_id NOT in
      <foreach item="item" index="index" collection="personIds" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
     <if test="conventionDto.fkConventionProcedureIds!=null and conventionDto.fkConventionProcedureIds.size>0">
       AND fk_convention_procedure_id IN
       <foreach item="item" index="index" collection="conventionDto.fkConventionProcedureIds" open="(" separator="," close=")">
         #{item}
       </foreach>
     </if>
    GROUP BY fk_convention_person_id
      )
    <if test="conventionDto.type != null and conventionDto.type != ''">
      and type = #{conventionDto.type}
    </if>
    <if test="conventionDto.type == 0">
      and type = #{conventionDto.type}
    </if>
    <if test="conventionDto.nameKey != null and conventionDto.nameKey != ''">
      and (position( #{conventionDto.nameKey} in name) or position(#{conventionDto.nameKey} in name_chn))
    </if>
    order by
     num
  </select>

  <select id="getRoomNotArrangedPersonList" resultType="com.get.salecenter.vo.ConventionPersonVo">
    SELECT
	 a.id,a.type,a.name,a.name_chn,a.company,a.gender,a.num
    FROM
	 m_convention_person a
    WHERE
     a.fk_convention_id = #{conventionDto.fkConventionId}
	AND
	 a.id
	NOT IN
	    (
	    SELECT IFNULL(fk_convention_person_id,0) FROM r_convention_hotel_room_person WHERE fk_convention_hotel_room_id
	    IN
       <foreach item="item" index="index" collection="roomIds" open="(" separator="," close=")">
        #{item}
       </foreach>
	    )
    <if test="conventionDto.type != null and conventionDto.type != ''">
      and type = #{conventionDto.type}
    </if>
    <if test="conventionDto.type == 0">
      and type = #{conventionDto.type}
    </if>
<!--    <if test="conventionDto.bdCode != null and conventionDto.bdCode != ''">-->
<!--      and bd_code = #{conventionDto.bdCode}-->
<!--    </if>    -->
    <if test="conventionDto.bdCodes != null and conventionDto.bdCodes.size()>0">
      and bd_code in
      <foreach collection="conventionDto.bdCodes" item="bdCode" index="index" open="(" separator="," close=")">
        #{bdCode}
      </foreach>
    </if>
    <if test="conventionDto.nameKey != null and conventionDto.nameKey != ''">
      and
      (REPLACE(name," ","") like #{conventionDto.nameKey}
      or
      name like #{conventionDto.nameKey}
      or
      name_chn like #{conventionDto.nameKey}
      or
      REPLACE(name_chn," ","") like #{conventionDto.nameKey}
<!--      <or-->
<!--      to_pinyin(REPLACE ( name_chn, " ", "" )) like #{conventionDto.nameKey})>-->
    </if>
    order by
    num
  </select>

  <select id="conventionPersonIsEmpty" parameterType="java.lang.Long" resultType="java.lang.Boolean">
    select IFNULL(max(id),0)id from m_convention_person where fk_convention_id = #{id} LIMIT 1

  </select>

  <select id="getPersonIdsByType" resultType="java.lang.Long">
    select
      id
    from
      m_convention_person
    where
      type = #{personType}
  </select>
    <select id="getInstitutionName" resultType="com.get.salecenter.entity.ConventionRegistration">
      SELECT
        cr.*
      FROM
        m_convention_registration AS cr
          INNER JOIN
        r_convention_person_registration AS cpr
        ON
          cr.id = cpr.fk_convention_registration_id
          INNER JOIN
        m_convention_person AS cp
        ON
          cp.id = cpr.fk_convention_person_id
      WHERE
        cp.id = #{id}
    </select>
    <select id="getPersonCount" resultType="java.lang.Integer">
      SELECT
        count(*)
      FROM
        m_convention_registration AS cr
          INNER JOIN r_convention_person_registration AS cpr ON cr.id = cpr.fk_convention_registration_id
          LEFT JOIN m_convention_person AS cp ON cp.id = cpr.fk_convention_person_id
      WHERE cr.receipt_code = #{receiptCode} and (cp.fk_convention_hotel_id is not null or cp.fk_convention_hotel_id !="")
    </select>

    <select id="getConventionPersonBoothMap" resultType="com.get.salecenter.vo.ConventionPersonVo">
      SELECT a.fk_convention_person_id as id, GROUP_CONCAT(b.booth_name) as excelBoothName
      FROM r_convention_person_registration a
             LEFT JOIN m_convention_registration b ON a.fk_convention_registration_id=b.id
      WHERE a.fk_convention_person_id in
      <foreach item="item" index="index" collection="personIds" open="(" separator="," close=")">
        #{item}
      </foreach>
      GROUP BY a.fk_convention_person_id
    </select>

    <select id="getPersonIdsByBdCode" resultType="java.lang.Long">
      select
        id
      from
        m_convention_person
      where
        bd_code = #{bdCode}
    </select>
    <select id="findConventionPersonNameAndSexByIds" resultType="com.get.salecenter.vo.ConventionPersonVo">
      select
        id , name ,name_chn, gender ,is_attend,type
      from
        m_convention_person
      <where>
        <if test="personIds != null and personIds.size()>0">
          AND id IN
          <foreach collection="personIds" item="id" index="index" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
          </foreach>
        </if>
      </where>
    </select>
  <select id="findConventionPersonNameAndSexByConventionId"
          resultType="com.get.salecenter.vo.ConventionPersonVo">
    select
    id , name ,name_chn, gender ,is_attend,type,company
    from
    m_convention_person
    where
    fk_convention_id = #{conventionId}
    <if test="bdCodes != null and bdCodes.size()>0">
      and bd_code in
      <foreach collection="bdCodes" item="bdCode" index="index" open="(" separator="," close=")">
        #{bdCode}
      </foreach>
    </if>
  </select>
    <select id="getPersonIdsByDinnerProcedure" resultType="java.lang.Long">
      SELECT DISTINCT
      cppd.fk_convention_person_id
      FROM
      m_convention_person AS cp
      INNER JOIN
      r_convention_person_procedure AS cppd
      ON
      cp.id = cppd.fk_convention_person_id
      INNER JOIN
      m_convention_procedure AS cpd
      ON
      cppd.fk_convention_procedure_id = cpd.id
      WHERE
      cp.fk_convention_id = #{conventionPersonDto.fkConventionId}
      <if test="conventionPersonDto.isAttendDinnerProcedure != null">
        and (cpd.fk_table_type_key = "CONVENTION_DINNER_TABLE")

      </if>
    </select>
  <select id="getPersonIdsByTrainingProcedure" resultType="java.lang.Long">
    SELECT DISTINCT
    cppd.fk_convention_person_id
    FROM
    m_convention_person AS cp
    INNER JOIN
    r_convention_person_procedure AS cppd
    ON
    cp.id = cppd.fk_convention_person_id
    INNER JOIN
    m_convention_procedure AS cpd
    ON
    cppd.fk_convention_procedure_id = cpd.id
    WHERE
    cp.fk_convention_id = #{conventionPersonDto.fkConventionId}
    <if test="conventionPersonDto.isAttendTrainingProcedure != null">

        and (cpd.fk_table_type_key = "CONVENTION_TRAINING_TABLE")

    </if>
  </select>
  <select id="getPersonIdsByNotTrainingProcedure" resultType="java.lang.Long">
    SELECT
      id
    FROM
      m_convention_person
    WHERE
      fk_convention_id = #{conventionPersonDto.fkConventionId}
      AND id NOT IN (
      SELECT DISTINCT
        cppd.fk_convention_person_id
      FROM
        m_convention_person AS cp
          INNER JOIN r_convention_person_procedure AS cppd ON cp.id = cppd.fk_convention_person_id
          INNER JOIN m_convention_procedure AS cpd ON cppd.fk_convention_procedure_id = cpd.id
      WHERE
        cp.fk_convention_id = #{conventionPersonDto.fkConventionId}
        AND cpd.fk_table_type_key = "CONVENTION_TRAINING_TABLE")
  </select>
  <select id="getPersonIdsByNotDinnerProcedure" resultType="java.lang.Long">
    SELECT
      id
    FROM
      m_convention_person
    WHERE
      fk_convention_id = #{conventionPersonDto.fkConventionId}
      AND id NOT IN (
      SELECT DISTINCT
        cppd.fk_convention_person_id
      FROM
        m_convention_person AS cp
          INNER JOIN r_convention_person_procedure AS cppd ON cp.id = cppd.fk_convention_person_id
          INNER JOIN m_convention_procedure AS cpd ON cppd.fk_convention_procedure_id = cpd.id
      WHERE
        cp.fk_convention_id = #{conventionPersonDto.fkConventionId}
        AND cpd.fk_table_type_key = "CONVENTION_DINNER_TABLE")
  </select>
    <select id="getRepeatPerson" resultType="java.lang.Long">
      SELECT
        a.fk_convention_person_id
      FROM
        r_convention_table_person a
          left JOIN m_convention_table b on a.fk_convention_table_id = b.id and b.fk_convention_id = #{conventionId} and b.fk_table_type_key = #{tableTypeKey}
      where b.id is not null
      GROUP BY a.fk_convention_person_id
<!--      <if test="searchRepeatPerson != null and searchRepeatPerson == 0">-->
<!--      HAVING count(*) <![CDATA[<= ]]> 1-->
<!--      </if>-->
      <if test="searchRepeatPerson != null and searchRepeatPerson == 1">
      HAVING count(*) > 1
      </if>
    </select>
    <select id="getRanking" resultType="com.get.salecenter.vo.ConventionPersonVotingRankingVo">

      SELECT
      CONCAT(e.`name`, "(", e.name_en, ")") AS bdName,
      d.bd_code,
      CONCAT(a.`name`, "(", a.name_chn, ")") AS fkConventionPersonName,
      a.tel fkConventionPersonPhone,
      a.id fkConventionPersonId
      FROM
      m_convention_person a
      LEFT JOIN `r_convention_person_agent` b ON a.id = b.fk_convention_person_id
      LEFT JOIN r_agent_staff c ON c.fk_agent_id = b.fk_agent_id
      LEFT JOIN r_staff_bd_code d ON d.fk_staff_id = c.fk_staff_id
      LEFT JOIN ais_permission_center.m_staff e ON e.id = c.fk_staff_id
      WHERE 1=1
      and a.fk_convention_id = 20
      <if test="conventionPersonVotingRankingDto.phone != null">
      and a.tel like concat('%',#{conventionPersonVotingRankingDto.phone},'%')
      </if>
      <if test="conventionPersonVotingRankingDto.name != null">
      and REPLACE(CONCAT(LOWER(a.`name`),LOWER(a.name_chn)),' ','') like concat('%',#{conventionPersonVotingRankingDto.name},'%')
      </if>
      and REPLACE(CONCAT(LOWER(e.`name`),LOWER(e.name_en)),' ','') like concat('%',#{conventionPersonVotingRankingDto.bdName},'%')
      <if test="conventionPersonVotingRankingDto.bdName != null">
        and CONCAT(LOWER(a.`name`), "(", a.name_chn, ")") like'%Hana%'
      </if>
    </select>
  <select id="getRankingList" resultType="com.get.salecenter.vo.ConventionPersonVotingRankingVo">
    select * from (
      select mcp.id fkConventionPersonId,count(mhv.id)voteCount from m_convention_person mcp
      left join ais_app_convention_center.m_help_voting mhv on mcp.id=mhv.fk_convention_person_id
      where 1=1 and mcp.fk_convention_id=20
      group by mcp.id
    )aa order by voteCount desc,fkConventionPersonId asc
  </select>
  <select id="getLikeCollectionActivityList" resultType="com.get.salecenter.vo.LikeCollectionActivityVo">
    select
    mlc.id,
    mlc.fk_convention_id,
    mlc.type,
    mlc.like_count,
    mlc.status,
    mlc.gmt_create,
    mlc.platform_account,
    mlc.nickname,
    cp.name,
    cp.tel,
    cp.name_chn,
    mlc.fk_convention_person_id
    from ais_app_convention_center.m_like_count as mlc
    left join  ais_sale_center.m_convention_person cp on mlc.fk_convention_person_id = cp.id
    where 1=1
    <if test="data.type !=null">
      and mlc.type = #{data.type}
    </if>
    <if test="data.fkConventionId !=null">
      and mlc.fk_convention_id = #{data.fkConventionId}
    </if>
    <if test="data.status !=null">
      and mlc.status = #{data.status}
    </if>
    <if test="data.nameOrTel !=null and data.nameOrTel !=''">
       and (cp.name like concat("%",#{data.nameOrTel},"%")
       OR cp.name_chn like concat("%",#{data.nameOrTel},"%")
      OR cp.tel like concat("%",#{data.nameOrTel},"%"))
    </if>
    <if test="data.nickname !=null and data.nickname !=''">
    and mlc.nickname like concat("%",#{data.nickname},"%")
    </if>
    <if test="data.platformAccount !=null and data.platformAccount !=''">
      and mlc.platform_account like concat("%",#{data.platformAccount},"%")
    </if>
    order by mlc.gmt_create desc
  </select>
  <select  id="getMediaAndAttached" resultType="com.get.salecenter.vo.MediaAndAttachedVo">
  select * from
    ais_voting_center.s_media_and_attached as smaa
  where smaa.fk_table_name = #{fkTableName}
  and smaa.type_key = #{typeKey}
  and smaa.fk_table_id = #{fkTableId}
</select>
  <select id="getConventionSelect" resultType="com.get.salecenter.vo.ConventionSelectVo">
    select
    mlc.fk_convention_id as id,
    mc.theme_name
    from ais_app_convention_center.m_like_count as mlc
    left join m_convention as mc on mlc.fk_convention_id = mc.id
  </select>

  <update id="likeConventionApproval">
    UPDATE ais_app_convention_center.m_like_count
    SET status = #{status}
    WHERE id = #{id}
  </update>

  <update id="likeConventionEdit">
    UPDATE ais_app_convention_center.m_like_count
    SET
      nickname = CASE
                   WHEN #{data.nickname} IS NOT NULL THEN #{data.nickname}
                   ELSE nickname
        END,
      platform_account = CASE
                           WHEN #{data.platformAccount} IS NOT NULL THEN #{data.platformAccount}
                           ELSE platform_account
        END,
      like_count = CASE
                     WHEN #{data.likeCount} IS NOT NULL THEN #{data.likeCount}
                     ELSE like_count
        END
     where id = #{data.id};
  </update>

  <select id="getBdNameList" resultType="com.get.salecenter.entity.StaffBdCode">
    select
    fk_staff_id,bd_code
    from
    r_staff_bd_code
    where
    bd_code in
    <foreach collection="bdCodeList" item="bdcode" index="index" open="(" separator="," close=")">
      #{bdcode}
    </foreach>
  </select>
  <select id="getHelpRankingList" resultType="com.get.salecenter.vo.ConventionPersonVotingRankingVo">
    select * from (
      select mcp.id fkConventionPersonId,count(mhv.id)voteCount,max(mcp.`name`)fkConventionPersonName,
	  max(tel)fkConventionPersonPhone,max(bd_code)bdCode
      from m_convention_person mcp
      left join ais_app_convention_center.m_help_voting mhv on mcp.id=mhv.fk_convention_person_id
      where 1=1 and mcp.fk_convention_id=20
      <if test="bdCodeList != null">
      and bd_code in
      <foreach collection="bdCodeList" item="bdCodeList" index="index" open="(" separator="," close=")">
        #{bdCodeList}
      </foreach>
      </if>
    <if test="name != null">
      and REPLACE(CONCAT(LOWER(`name`),LOWER(name_chn)),' ','') like REPLACE(LOWER(concat('%',#{name},'%')),' ','')
    </if>
    <if test="tel != null">
      and REPLACE(tel,' ','') like concat('%',#{tel},'%')
    </if>
      group by mcp.id
    )aa order by voteCount desc,fkConventionPersonId asc
  </select>
    <select id="getAgentAttendanceStatistics" resultType="com.get.salecenter.vo.AgentAttendanceStatisticsVo">
    select a.* from
    (
      SELECT
        c.`name` bdNameKey,
        COUNT(*) totalPersonCount,
        COUNT(CASE WHEN a.is_attend = 1 THEN 1 END) attendCount,
        COUNT(CASE WHEN a.is_attend = !1 or a.is_attend is null THEN 1 END) unAttendCount
      FROM
        m_convention_person a
          INNER JOIN r_staff_bd_code b on a.bd_code = b.bd_code
          LEFT JOIN ais_permission_center.m_staff c on b.fk_staff_id = c.id
      where a.type = 1 AND a.bd_code is not null
      <if test="agentAttendanceStatisticsDto.fkConventionId !=null">
        and a.fk_convention_id = #{agentAttendanceStatisticsDto.fkConventionId}
      </if>
      <if test="agentAttendanceStatisticsDto.bdNameKey !=null and agentAttendanceStatisticsDto.bdNameKey !=''">
        and (
            c.`name` LIKE CONCAT("%",#{agentAttendanceStatisticsDto.bdNameKey},"%") or c.name_en LIKE CONCAT("%",#{agentAttendanceStatisticsDto.bdNameKey},"%")
        )
      </if>
      GROUP BY a.bd_code
      ORDER BY  totalPersonCount desc
      ) a
    union all
    (
    SELECT
      null as bdNameKey,
      COUNT(*) totalPersonCount,
      COUNT(CASE WHEN a.is_attend = 1 THEN 1 END) attendCount,
      COUNT(CASE WHEN a.is_attend = !1 or a.is_attend is null THEN 1 END) unAttendCount
      FROM
      m_convention_person a
      INNER JOIN r_staff_bd_code b on a.bd_code = b.bd_code
      LEFT JOIN ais_permission_center.m_staff c on b.fk_staff_id = c.id
      where a.type = 1 AND a.bd_code is not null
      <if test="agentAttendanceStatisticsDto.fkConventionId !=null">
        and a.fk_convention_id = #{agentAttendanceStatisticsDto.fkConventionId}
      </if>
      <if test="agentAttendanceStatisticsDto.bdNameKey !=null and agentAttendanceStatisticsDto.bdNameKey !=''">
        and (
        c.`name` LIKE CONCAT("%",#{agentAttendanceStatisticsDto.bdNameKey},"%") or c.name_en LIKE CONCAT("%",#{agentAttendanceStatisticsDto.bdNameKey},"%")
        )
      </if>
      )
    </select>
  <select id="getAgentAttendanceBindingInfo" resultType="com.get.salecenter.vo.AgentAttendanceBindingInfoVo">
    SELECT
      COUNT(*) totalCount,
      COUNT(CASE WHEN a.bd_code IS NOT NULL and a.bd_code != "" THEN 1 ELSE NULL END) bindingCount
    FROM
      m_convention_person a
        LEFT JOIN r_staff_bd_code b on a.bd_code = b.bd_code
        LEFT JOIN ais_permission_center.m_staff c on b.fk_staff_id = c.id
    where a.type = 1
    <if test="agentAttendanceStatisticsDto.fkConventionId !=null">
      and a.fk_convention_id = #{agentAttendanceStatisticsDto.fkConventionId}
    </if>
    <if test="agentAttendanceStatisticsDto.bdNameKey !=null and agentAttendanceStatisticsDto.bdNameKey !=''">
      and (
      c.`name` LIKE CONCAT("%",#{agentAttendanceStatisticsDto.bdNameKey},"%") or c.name_en LIKE CONCAT("%",#{agentAttendanceStatisticsDto.bdNameKey},"%")
      )
    </if>
  </select>
</mapper>