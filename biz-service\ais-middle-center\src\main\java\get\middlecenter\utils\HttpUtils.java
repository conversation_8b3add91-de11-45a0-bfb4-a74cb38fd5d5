package get.middlecenter.utils; 

import cn.hutool.json.JSONObject; 
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope; 
import org.springframework.http.*; 
import org.springframework.stereotype.Component; 
import org.springframework.util.LinkedMultiValueMap; 
import org.springframework.util.MultiValueMap; 
import org.springframework.web.client.RestTemplate; 
import org.springframework.web.multipart.MultipartFile; 
import java.net.URI;

/**
 * Created by <PERSON>.xie.
 * Time: 9:39
 * Date: 2025/7/2
 */
@Component 
@RefreshScope
@Slf4j
public class HttpUtils { 

    @Value("${defautauth.baseUrl}") 
    private String baseUrl; 

    public String sendPostRequest(Object requestBody, String path, String nonce) { 
        return sendPostRequest(requestBody, path, nonce, null); 
    } 

    public String sendPostRequest(Object requestBody, String path, String nonce, MultipartFile file) { 
        RestTemplate restTemplate = new RestTemplate(); 
        try { 
            if (file != null) { 
                return handleFileUpload(restTemplate, requestBody, path, nonce, file); 
            } else { 
                return handleNormalRequest(restTemplate, requestBody, path, nonce); 
            } 
        } catch (Exception e) {
            log.error("sign-API请求发送失败: " + e.getMessage());
            return ""; 
        } 
    } 

    private String handleNormalRequest(RestTemplate restTemplate, Object requestBody, String path, String nonce) { 
        String body = JSONUtil.toJsonStr(requestBody); 
        RequestEntity<String> request = RequestEntity 
                .post(URI.create(baseUrl + path)) 
                .contentType(MediaType.APPLICATION_JSON) 
                .header("X-Nonce", nonce) 
                .body(body); 
        ResponseEntity<String> responseEntity = restTemplate.exchange(request, String.class); 
        return processResponse(responseEntity); 
    } 

    private String handleFileUpload(RestTemplate restTemplate, Object requestBody, String path, String nonce, MultipartFile file) throws Exception { 
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>(); 
        body.add("file", file.getResource()); 
        if (requestBody != null) { 
            body.add("requestBody", JSONUtil.toJsonStr(requestBody));
        } 

        HttpHeaders headers = new HttpHeaders(); 
        headers.setContentType(MediaType.MULTIPART_FORM_DATA); 
        headers.set("X-Nonce", nonce);

        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers); 
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(baseUrl + path, requestEntity, String.class); 
        return processResponse(responseEntity); 
    } 

    private String processResponse(ResponseEntity<String> responseEntity) { 
        if (responseEntity.getStatusCode() == HttpStatus.OK) { 
            JSONObject parseObj = JSONUtil.parseObj(responseEntity.getBody()); 
            Object getData = parseObj.get("data"); 
            return getData != null ? JSONUtil.toJsonStr(getData) : ""; 
        } 
        return ""; 
    } 
}