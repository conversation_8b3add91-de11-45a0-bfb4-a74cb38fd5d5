package com.get.resumecenter.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.resumecenter.entity.IndustryType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface IndustryTypeMapper extends BaseMapper<IndustryType> {
    int insert(IndustryType record);

    int insertSelective(IndustryType record);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 下拉
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getIndustryTypeSelect();

    /**
     * @return java.lang.String
     * @Description: 根据类型查询类型名称
     * @Param [typeId]
     * <AUTHOR>
     */
    String getTypeNameByTypeId(Long typeId);

    Integer getMaxViewOrder();

}