package com.get.officecenter.controller;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONObject;
import com.get.officecenter.config.WxCpTpConfiguration;
import com.get.officecenter.constant.InfoTypeEnum;
import com.get.officecenter.utils.JsonUtils;
import com.get.common.result.ResponseBo;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.utils.GeneralTool;
import com.google.common.collect.Maps;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.util.RandomUtils;
import me.chanjar.weixin.common.util.crypto.SHA1;
import me.chanjar.weixin.cp.bean.WxCpTpCorp;
import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;
import me.chanjar.weixin.cp.bean.message.WxCpXmlOutMessage;
import me.chanjar.weixin.cp.config.WxCpTpConfigStorage;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import me.chanjar.weixin.cp.util.crypto.WxCpTpCryptUtil;
import me.chanjar.weixin.cp.util.xml.XStreamTransformer;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <NAME_EMAIL> on 2020/2/14
 * 企业微信第三方应用开发
 */
@RestController
@RequestMapping("/wx/wxCpTp")
public class WxCpTpController extends BaseController{
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Value("${wx.cptp.appConfigs[0].suiteId}")
    private String SUIT_ID;

    @Value("${wx.cptp.appConfigs[0].secret}")
    private String SUIT_SECRT;

    private WxCpTpService tpService;

    @Resource
    private WxCpTpConfiguration configuration;

    /**
     * 数据回调url，应用中发送消息接收
     * @param signature
     * @param timestamp
     * @param nonce
     * @param echostr
     * @return
     */
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @RequestMapping("/callback/data")
    public String data(@RequestParam(name = "msg_signature", required = false) String signature,
                       @RequestParam(name = "timestamp", required = false) String timestamp,
                       @RequestParam(name = "nonce", required = false) String nonce,
                       @RequestParam(name = "echostr", required = false) String echostr,
                       HttpServletRequest request) {
        logger.info("数据回调");
        logger.info("signature = [{}], timestamp = [{}], nonce = [{}], echostr = [{}]", signature, timestamp, nonce, echostr);
        tpService = WxCpTpConfiguration.getCpTpService(SUIT_ID);
        if (StringUtils.isEmpty(echostr)){
            try {
                BufferedReader reader = request.getReader();
                StringBuffer buffer = new StringBuffer();
                String line = " ";
                while (null != (line = reader.readLine())) {
                    buffer.append(line);
                }
                String encryptedXml = buffer.toString();
                WxCpTpCryptUtil cryptUtil = new WxCpTpCryptUtil(tpService.getWxCpTpConfigStorage());
                String plainText = cryptUtil.decrypt(signature, timestamp, nonce, encryptedXml);
                System.out.println("数据回调-解密后的xml数据:" + plainText);
                logger.debug("解密后的原始xml消息内容：{}", plainText);
                WxCpTpXmlMessage inMessage = WxCpTpXmlMessage.fromXml(plainText);
                logger.debug("\n消息解密后内容为：\n{} ", JsonUtils.toJson(inMessage));
                WxCpXmlOutMessage outMessage = this.route(SUIT_ID, inMessage);
                if (outMessage == null) {
                    return "";
                }
                String plainXml = XStreamTransformer.toXml((Class) outMessage.getClass(), outMessage);
                String out = cryptUtil.encrypt(plainXml);
                return out;
            }catch (Exception e){
                logger.error("校验失败：" + e.getMessage());
                return "success";
            }
        }
        try {
            if (tpService.checkSignature(signature, timestamp, nonce, echostr)) {
                return new WxCpTpCryptUtil(tpService.getWxCpTpConfigStorage()).decrypt(echostr);
            }
        } catch (Exception e) {
            logger.error("校验签名失败：" + e.getMessage());
        }
        return "success";
    }



    /**
     * 指令回调url  通讯录，部门变更，  授权变更，  ticket数据
     * @param signature
     * @param timestamp
     * @param nonce
     * @param echostr
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @RequestMapping(value = "/suite/receive")
    public String suite(
                        @RequestParam("msg_signature") String signature,
                        @RequestParam("timestamp") String timestamp,
                        @RequestParam("nonce") String nonce,
                        @RequestParam(value = "echostr", required = false) String echostr,
                        HttpServletRequest request,
                        HttpServletResponse response) throws Exception {
        logger.info("指令回调URL-调用我了");
        logger.info("signature = [{}], timestamp = [{}], nonce = [{}], echostr = [{}]", signature, timestamp, nonce, echostr);
        logger.info("\n接收微信请求：[signature=[{}], timestamp=[{}], nonce=[{}], echostr = [{}]",
                signature, timestamp, nonce,echostr);

        final WxCpTpService wxCpTpService = WxCpTpConfiguration.getCpTpService(SUIT_ID);
        // 不为空为回调配置请求
        if (null != echostr&&!"".equals(echostr)) {
            try {
                if (wxCpTpService.checkSignature(signature, timestamp, nonce, echostr)) {
                    String decrypt = new WxCpTpCryptUtil(wxCpTpService.getWxCpTpConfigStorage()).decrypt(echostr);
                    logger.info("指令回调URL结果：" + decrypt);
                    return decrypt;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            return null;
        }

        BufferedReader reader = request.getReader();
        StringBuffer buffer = new StringBuffer();
        String line = " ";
        while (null != (line = reader.readLine())) {
            buffer.append(line);
        }
        String encryptedXml = buffer.toString();
        String decryptMsgs = null;
        try {
            decryptMsgs = new WxCpTpCryptUtil(wxCpTpService.getWxCpTpConfigStorage()).decrypt(signature, timestamp, nonce, encryptedXml);
            System.out.println("解密后的xml数据:" + decryptMsgs);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("校验失败：" + e.getMessage());
            return "success";
        }
        WxCpTpXmlMessage wxCpTpXmlMessage = WxCpTpXmlMessage.fromXml(decryptMsgs);
        InfoTypeEnum infoTypeEnum = InfoTypeEnum.getByInstance(wxCpTpXmlMessage.getInfoType());
        if (infoTypeEnum == null){
            throw new Exception("不支持该类型操作");
        }
        Map<String, Object> jsonObject = wxCpTpXmlMessage.getAllFieldsMap();
        switch (infoTypeEnum) {
            //（每十分钟）推送ticket。ticket会实时变更，并用于后续接口的调用。
            case SUITE_TICKET: {
                String suitId = wxCpTpXmlMessage.getSuiteId();
                String suiteTicket = wxCpTpXmlMessage.getSuiteTicket();
                Integer timeStamp = Convert.toInt(wxCpTpXmlMessage.getTimeStamp());
                logger.info("推送ticket成功:" + jsonObject.toString());
                System.out.println("jsonObject.toString() = " + jsonObject.toString());
                WxCpTpConfigStorage tpConfig = wxCpTpService.getWxCpTpConfigStorage();
                tpConfig.updateSuiteTicket(suiteTicket, 20*60);
                logger.info("suit ticket缓存更新成功");
                System.out.println("suiteTicket" + suiteTicket);
                String suiteAccessTokenOld = tpConfig.getSuiteAccessToken();
                System.out.println("suiteAccessTokenOld:" + suiteAccessTokenOld);
                if (GeneralTool.isEmpty(suiteAccessTokenOld)||"null".equals(suiteAccessTokenOld)){
                    String suiteAccessToken = getSuiteAccessToken(suiteTicket,SUIT_ID,SUIT_SECRT);
                    if (!"null".equals(suiteAccessToken)){
                        //有效期两个小时
                        tpConfig.updateSuiteAccessToken(suiteAccessToken, 60*60*2);
                        logger.info("suitAccessToken:" + suiteAccessToken);
                        System.out.println("suitAccessToken:" + suiteAccessToken);
                    }
                }

                break;
            }
            // 企业微信应用市场发起授权时，企业微信后台会推送授权成功通知
            case CREATE_AUTH:{
                logger.info("创建授权：" + jsonObject.toString());
                String suiteId = wxCpTpXmlMessage.getSuiteId();
                String authCode = wxCpTpXmlMessage.getAuthCode();
                String timeStamp = wxCpTpXmlMessage.getTimeStamp();
                WxCpTpConfigStorage tpConfig = wxCpTpService.getWxCpTpConfigStorage();
                String suiteTicket = tpConfig.getSuiteTicket();
                String suiteAccessTokenOld = tpConfig.getSuiteAccessToken();
                System.out.println("suiteAccessTokenOld:" + suiteAccessTokenOld);
                if (GeneralTool.isEmpty(suiteAccessTokenOld)||"null".equals(suiteAccessTokenOld)){
                    String suiteAccessToken = getSuiteAccessToken(suiteTicket,SUIT_ID,SUIT_SECRT);
                    suiteAccessTokenOld = suiteAccessToken;
                    if (!"null".equals(suiteAccessToken)){
                        //有效期两个小时
                        tpConfig.updateSuiteAccessToken(suiteAccessToken, 60*60*2);
                        logger.info("suitAccessToken:" + suiteAccessToken);
                    }
                }
                WxCpTpCorp permanentCode = getPermanentCode(suiteAccessTokenOld, authCode);
                logger.info("永久授权码：" + permanentCode);
                break;
            }
            case CHANGE_AUTH:{
                logger.info("变更授权");
                // TODO: 2020/2/14
                break;
            }
            case CANCEL_AUTH:{
                logger.info("取消授权通知");
                // TODO: 2020/2/14
                break;
            }
            case CHANGE_CONTACT:{
                logger.info("通讯录事件");
                // TODO: 2020/2/14
                break;
            }
            case CHANGE_EXTERNAL_CONTACT:{
                logger.info("外部联系人事件");
                // TODO: 2020/2/14
                break;
            }
            default: {
                break;
            }
        }
        return "success";
    }

    private WxCpXmlOutMessage route(String suitId, WxCpTpXmlMessage message) {
        try {
            return WxCpTpConfiguration.getRouters().get(suitId).route(message);
        } catch (Exception e) {
            this.logger.error(e.getMessage(), e);
        }
        return null;
    }


    /**
     * 获取预授权码
     * @return
     */
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @GetMapping(value = "/doGetPreAuthCode")
    public ResponseBo<Map> doGetPreAuthCode(){
        try {
            final WxCpTpService wxCpTpService = WxCpTpConfiguration.getCpTpService(SUIT_ID);
            WxCpTpConfigStorage tpConfig = wxCpTpService.getWxCpTpConfigStorage();

            String suiteAccessTokenOld = tpConfig.getSuiteAccessToken();
            System.out.println("suiteAccessTokenOld:" + suiteAccessTokenOld);
            if (GeneralTool.isEmpty(suiteAccessTokenOld)||"null".equals(suiteAccessTokenOld)){
                String suiteTicket = tpConfig.getSuiteTicket();
                String suiteAccessToken = getSuiteAccessToken(suiteTicket,SUIT_ID,SUIT_SECRT);
                if (!"null".equals(suiteAccessToken)){
                    //有效期两个小时
                    tpConfig.updateSuiteAccessToken(suiteAccessToken, 60*60*2);
                    logger.info("suitAccessToken:" + suiteAccessToken);
                    System.out.println("suitAccessToken:" + suiteAccessToken);
                }
            }
            String preAuthCode = getPreAuthCode(suiteAccessTokenOld);
            String authType = doSetAuthType(suiteAccessTokenOld, preAuthCode);

            HashMap<String, String> map = Maps.newHashMap();
            map.put("pre_auth_code",preAuthCode);
            map.put("auth_type",authType);

            return new ResponseBo<>(map);
        }catch (Exception e) {
            this.logger.error(e.getMessage(), e);
            return ResponseBo.error();
        }
    }


    /**
     * 获取预授权码
     * @return
     */
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @GetMapping(value = "/doGetSuiteAccessToken")
    public ResponseBo<String> doGetSuiteAccessToken(){
        try {
            final WxCpTpService wxCpTpService = WxCpTpConfiguration.getCpTpService(SUIT_ID);
            WxCpTpConfigStorage tpConfig = wxCpTpService.getWxCpTpConfigStorage();
            String suiteTicket = tpConfig.getSuiteTicket();
            String suiteAccessTokenOld = tpConfig.getSuiteAccessToken();
            if (GeneralTool.isNotEmpty(suiteAccessTokenOld)&&!"null".equals(suiteAccessTokenOld)){
                return new ResponseBo<>(suiteAccessTokenOld);
            }
            String suiteAccessToken = getSuiteAccessToken(suiteTicket,SUIT_ID,SUIT_SECRT);
            tpConfig.updateSuiteAccessToken(suiteAccessToken, 20*60);
            System.out.println("suiteAccessToken" + suiteAccessToken);
            return new ResponseBo<>(suiteAccessToken);
        }catch (Exception e) {
            this.logger.error(e.getMessage(), e);
            return ResponseBo.error();
        }
    }


    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @RequestMapping("/create/jsapiSign")
    public ResponseBo<WxJsapiSignature> jssdk(@RequestParam("url") String url) throws Exception{
        final WxCpTpService tpService = WxCpTpConfiguration.getCpTpService(SUIT_ID);
        WxCpTpConfigStorage tpConfig = tpService.getWxCpTpConfigStorage();
        String authCropId = BaseController.authCropIdMap.get(SUIT_ID);
        logger.info("authCorpId:{}  url:{}", authCropId, url);

        long timestamp = System.currentTimeMillis() / 1000;
        String nonceStr = RandomUtils.getRandomStr();

        String suiteAccessTokenOld = tpConfig.getSuiteAccessToken();
        System.out.println("suiteAccessTokenOld:" + suiteAccessTokenOld);
        if (GeneralTool.isEmpty(suiteAccessTokenOld)||"null".equals(suiteAccessTokenOld)){
            String suiteTicket = tpConfig.getSuiteTicket();
            String suiteAccessToken = getSuiteAccessToken(suiteTicket,SUIT_ID,SUIT_SECRT);
            if (!"null".equals(suiteAccessToken)){
                //有效期两个小时
                tpConfig.updateSuiteAccessToken(suiteAccessToken, 60*60*2);
                logger.info("suitAccessToken:" + suiteAccessToken);
                System.out.println("suitAccessToken:" + suiteAccessToken);
            }
        }

        JSONObject object = getJsApiTicket(suiteAccessTokenOld, tpService,SUIT_ID);
        String jsApiTicket = object.getString("ticket");
        String signature = SHA1.genWithAmple(
                "jsapi_ticket=" + jsApiTicket,
                "noncestr=" + nonceStr,
                "timestamp=" + timestamp,
                "url=" + url
        );
        WxJsapiSignature jsapiSignature = new WxJsapiSignature();
        jsapiSignature.setTimestamp(timestamp);
        jsapiSignature.setNonceStr(nonceStr);
        jsapiSignature.setUrl(url);
        jsapiSignature.setSignature(signature);
        jsapiSignature.setAppId(authCropId);
        logger.info("data：" + JsonUtils.toJson(jsapiSignature));
        return new ResponseBo<>(jsapiSignature);
    }


}
