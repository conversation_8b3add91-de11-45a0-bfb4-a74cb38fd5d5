package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.institutioncenter.vo.InstitutionRankingVo;
import com.get.institutioncenter.vo.InstitutionScholarshipVo;
import com.get.institutioncenter.entity.InstitutionRanking;
import com.get.institutioncenter.dto.InstitutionRankingDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstitutionRankingMapper extends BaseMapper<InstitutionRanking> {

    int insertSelective(InstitutionRanking record);


    List<InstitutionRankingVo> getWcComprehensiveRanking(IPage<InstitutionScholarshipVo> ipage,
                                                         @Param("data") InstitutionRankingDto data);

    List<InstitutionRankingVo> getWcComprehensiveRankingHome();

    List<InstitutionRankingVo> getWcCountryByKey(@Param("typeKey")Integer typeKey);

    InstitutionRankingVo getWcCourseTypeKey(@Param("typeKey") String typeKey);

    List<InstitutionRankingVo> getWcMajorRanking(@Param("typeKey") String typeKey);

    Integer getMaxYear();

}